import { Component, Inject, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { ExpenseData, QuoteOption, MilestoneCreationData } from '../../pages/pm-expense-landing-page/pm-expense-landing-page.component';
import { PmExpenseServiceService } from '../../services/pm-expense-service.service';

export interface MilestoneDialogData {
  selectedExpenses: ExpenseData[];
  totalAmount: number;
  projectId: number;
  itemId: number;
}

@Component({
  selector: 'app-milestone-creation-dialog',
  templateUrl: './milestone-creation-dialog.component.html',
  styleUrls: ['./milestone-creation-dialog.component.scss']
})
export class MilestoneCreationDialogComponent implements OnInit {
  milestoneForm: FormGroup;
  availableQuotes: QuoteOption[] = [];
  isLoading = false;
  isSubmitting = false;

  constructor(
    private fb: FormBuilder,
    private dialogRef: MatDialogRef<MilestoneCreationDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: MilestoneDialogData,
    private expenseService: PmExpenseServiceService,
    private snackBar: MatSnackBar
  ) {
    this.milestoneForm = this.fb.group({
      milestone_name: ['', [Validators.required, Validators.minLength(3)]],
      quote_id: ['', Validators.required]
    });
  }

  ngOnInit(): void {
    this.loadAvailableQuotes();
  }

  loadAvailableQuotes(): void {
    this.isLoading = true;
    this.expenseService.getAvailableQuotes(this.data.projectId, this.data.itemId)
      .subscribe({
        next: (quotes) => {
          this.availableQuotes = quotes;
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error loading quotes:', error);
          this.snackBar.open('Error loading quotes', 'Close', { duration: 3000 });
          this.isLoading = false;
        }
      });
  }

  onSubmit(): void {
    if (this.milestoneForm.valid && !this.isSubmitting) {
      this.isSubmitting = true;
      
      const milestoneData: MilestoneCreationData = {
        milestone_name: this.milestoneForm.value.milestone_name,
        quote_id: this.milestoneForm.value.quote_id,
        selected_expenses: this.data.selectedExpenses,
        total_amount: this.data.totalAmount
      };

      this.expenseService.createMilestone(milestoneData, this.data.projectId, this.data.itemId)
        .subscribe({
          next: (response) => {
            this.snackBar.open('Milestone created successfully', 'Close', { duration: 3000 });
            this.dialogRef.close({ success: true, data: response });
          },
          error: (error) => {
            console.error('Error creating milestone:', error);
            this.snackBar.open('Error creating milestone', 'Close', { duration: 3000 });
            this.isSubmitting = false;
          }
        });
    }
  }

  onCancel(): void {
    this.dialogRef.close({ success: false });
  }

  get selectedExpensesCount(): number {
    return this.data.selectedExpenses.length;
  }

  get formattedTotalAmount(): string {
    return `USD ${this.data.totalAmount.toFixed(2)}`;
  }
}
