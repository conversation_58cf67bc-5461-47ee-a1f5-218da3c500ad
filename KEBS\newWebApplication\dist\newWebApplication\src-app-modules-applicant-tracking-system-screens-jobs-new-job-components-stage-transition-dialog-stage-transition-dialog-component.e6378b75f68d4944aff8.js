(window.webpackJsonp=window.webpackJsonp||[]).push([[920],{yprC:function(e,t,n){"use strict";n.r(t),n.d(t,"StageTransitionDialogComponent",(function(){return qe}));var i=n("mrSG"),a=n("3Pt+"),o=n("0IaG"),r=n("XNiG"),s=n("1G5W"),l=n("+rOU"),c=n("B0y8"),m=n("fXoL"),p=n("YVm3"),d=n("rQiX"),u=n("XNFG"),g=n("rDax"),h=n("JLuW"),f=n("URR/"),C=n("NFeN"),v=n("ofXK"),x=n("Xa2L"),F=n("kmnG"),y=n("qFsG"),M=n("L9uM"),b=n("bSwM"),w=n("UVjm"),O=n("su5B"),_=n("4/q7"),T=n("6t9p"),P=n("1jcm"),S=n("y5kQ"),E=n("pPzn");const D=["triggerPlaceholderChange"],I=["bodyEditor"];function L(e,t){1&e&&(m["\u0275\u0275elementStart"](0,"div",28),m["\u0275\u0275element"](1,"mat-spinner",29),m["\u0275\u0275elementEnd"]())}function k(e,t){}function V(e,t){if(1&e&&m["\u0275\u0275template"](0,k,0,0,"ng-template",38),2&e){m["\u0275\u0275nextContext"](3);const e=m["\u0275\u0275reference"](29);m["\u0275\u0275property"]("ngTemplateOutlet",e)}}function z(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275pipe"](1,"columnCustomization"),m["\u0275\u0275elementStart"](2,"span",34),m["\u0275\u0275text"](3),m["\u0275\u0275pipe"](4,"columnCustomization"),m["\u0275\u0275template"](5,V,1,1,void 0,35),m["\u0275\u0275pipe"](6,"columnCustomization"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](7,"mat-form-field",36),m["\u0275\u0275element"](8,"input",37),m["\u0275\u0275pipe"](9,"columnCustomization"),m["\u0275\u0275pipe"](10,"columnCustomization"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"](2);m["\u0275\u0275classMapInterpolate1"]("d-flex flex-column col-",m["\u0275\u0275pipeBind3"](1,7,e.stageFormFields,"stageName","col")," p-0"),m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate1"]("",m["\u0275\u0275pipeBind3"](4,11,e.stageFormFields,"stageName","label")," "),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",m["\u0275\u0275pipeBind3"](6,15,e.stageFormFields,"stageName","isMandatory")),m["\u0275\u0275advance"](3),m["\u0275\u0275property"]("placeholder",m["\u0275\u0275pipeBind3"](9,19,e.stageFormFields,"stageName","placeholder"))("disabled",m["\u0275\u0275pipeBind3"](10,23,e.stageFormFields,"stageName","isDisabled"))}}function j(e,t){}function B(e,t){if(1&e&&m["\u0275\u0275template"](0,j,0,0,"ng-template",38),2&e){m["\u0275\u0275nextContext"](3);const e=m["\u0275\u0275reference"](29);m["\u0275\u0275property"]("ngTemplateOutlet",e)}}const A=function(e){return{"app-input-search-field-highlight":e}};function N(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275pipe"](1,"columnCustomization"),m["\u0275\u0275elementStart"](2,"span",34),m["\u0275\u0275text"](3),m["\u0275\u0275pipe"](4,"columnCustomization"),m["\u0275\u0275template"](5,B,1,1,void 0,35),m["\u0275\u0275pipe"](6,"columnCustomization"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275element"](7,"app-input-search-single-select",39),m["\u0275\u0275pipe"](8,"columnCustomization"),m["\u0275\u0275pipe"](9,"columnCustomization"),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"](2);m["\u0275\u0275classMapInterpolate1"]("d-flex flex-column col-",m["\u0275\u0275pipeBind3"](1,10,e.stageFormFields,"action","col")," p-0"),m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate1"]("",m["\u0275\u0275pipeBind3"](4,14,e.stageFormFields,"action","label")," "),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",m["\u0275\u0275pipeBind3"](6,18,e.stageFormFields,"action","isMandatory")),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngClass",m["\u0275\u0275pureFunction1"](30,A,e.stageTransitionForms.get("action").hasError("required")&&e.stageTransitionForms.get("action").touched))("hasNoneOption",!m["\u0275\u0275pipeBind3"](8,22,e.stageFormFields,"action","isMandatory"))("list",e.actionMasterData)("hideMatLabel",!0)("placeholder",m["\u0275\u0275pipeBind3"](9,26,e.stageFormFields,"action","placeholder"))}}function q(e,t){}function R(e,t){if(1&e&&m["\u0275\u0275template"](0,q,0,0,"ng-template",38),2&e){m["\u0275\u0275nextContext"](3);const e=m["\u0275\u0275reference"](29);m["\u0275\u0275property"]("ngTemplateOutlet",e)}}function H(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275pipe"](1,"columnCustomization"),m["\u0275\u0275elementStart"](2,"span",34),m["\u0275\u0275text"](3),m["\u0275\u0275pipe"](4,"columnCustomization"),m["\u0275\u0275template"](5,R,1,1,void 0,35),m["\u0275\u0275pipe"](6,"columnCustomization"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275element"](7,"app-input-search-single-select",40),m["\u0275\u0275pipe"](8,"columnCustomization"),m["\u0275\u0275pipe"](9,"columnCustomization"),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"](2);m["\u0275\u0275classMapInterpolate1"]("d-flex flex-column col-",m["\u0275\u0275pipeBind3"](1,10,e.stageFormFields,"frequency","col")," p-0"),m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate1"]("",m["\u0275\u0275pipeBind3"](4,14,e.stageFormFields,"frequency","label")," "),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",m["\u0275\u0275pipeBind3"](6,18,e.stageFormFields,"frequency","isMandatory")),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngClass",m["\u0275\u0275pureFunction1"](30,A,e.stageTransitionForms.get("frequency").hasError("required")&&e.stageTransitionForms.get("frequency").touched))("hasNoneOption",!m["\u0275\u0275pipeBind3"](8,22,e.stageFormFields,"frequency","isMandatory"))("list",e.frequencyMasterData)("hideMatLabel",!0)("placeholder",m["\u0275\u0275pipeBind3"](9,26,e.stageFormFields,"frequency","placeholder"))}}function G(e,t){}function Z(e,t){if(1&e&&m["\u0275\u0275template"](0,G,0,0,"ng-template",38),2&e){m["\u0275\u0275nextContext"](3);const e=m["\u0275\u0275reference"](29);m["\u0275\u0275property"]("ngTemplateOutlet",e)}}function U(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275pipe"](1,"columnCustomization"),m["\u0275\u0275elementStart"](2,"span",34),m["\u0275\u0275text"](3),m["\u0275\u0275pipe"](4,"columnCustomization"),m["\u0275\u0275template"](5,Z,1,1,void 0,35),m["\u0275\u0275pipe"](6,"columnCustomization"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275element"](7,"app-input-search-single-select",41),m["\u0275\u0275pipe"](8,"columnCustomization"),m["\u0275\u0275pipe"](9,"columnCustomization"),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"](2);m["\u0275\u0275classMapInterpolate1"]("d-flex flex-column col-",m["\u0275\u0275pipeBind3"](1,10,e.stageFormFields,"whenToSend","col")," p-0"),m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate1"]("",m["\u0275\u0275pipeBind3"](4,14,e.stageFormFields,"whenToSend","label")," "),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",m["\u0275\u0275pipeBind3"](6,18,e.stageFormFields,"whenToSend","isMandatory")||2==e.stageTransitionForms.get("frequency").value),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngClass",m["\u0275\u0275pureFunction1"](30,A,e.stageTransitionForms.get("whenToSend").hasError("required")&&e.stageTransitionForms.get("whenToSend").touched))("hasNoneOption",!m["\u0275\u0275pipeBind3"](8,22,e.stageFormFields,"whenToSend","isMandatory")||2==e.stageTransitionForms.get("frequency").value)("list",e.whenToSendMasterData)("hideMatLabel",!0)("placeholder",m["\u0275\u0275pipeBind3"](9,26,e.stageFormFields,"whenToSend","placeholder"))}}function W(e,t){}function K(e,t){if(1&e&&m["\u0275\u0275template"](0,W,0,0,"ng-template",38),2&e){m["\u0275\u0275nextContext"](3);const e=m["\u0275\u0275reference"](29);m["\u0275\u0275property"]("ngTemplateOutlet",e)}}function J(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275pipe"](1,"columnCustomization"),m["\u0275\u0275elementStart"](2,"span",34),m["\u0275\u0275text"](3),m["\u0275\u0275pipe"](4,"columnCustomization"),m["\u0275\u0275template"](5,K,1,1,void 0,35),m["\u0275\u0275pipe"](6,"columnCustomization"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](7,"mat-form-field",36),m["\u0275\u0275element"](8,"input",42),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"](2);m["\u0275\u0275classMapInterpolate1"]("d-flex flex-column col-",m["\u0275\u0275pipeBind3"](1,5,e.stageFormFields,"whenToSendTime","col")," p-0"),m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate1"]("",m["\u0275\u0275pipeBind3"](4,9,e.stageFormFields,"whenToSendTime","label")," "),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",m["\u0275\u0275pipeBind3"](6,13,e.stageFormFields,"whenToSendTime","isMandatory")||2==e.stageTransitionForms.get("frequency").value)}}function X(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275pipe"](1,"columnCustomization"),m["\u0275\u0275elementStart"](2,"mat-checkbox",43),m["\u0275\u0275elementStart"](3,"span",44),m["\u0275\u0275text"](4),m["\u0275\u0275pipe"](5,"columnCustomization"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"](2);m["\u0275\u0275classMapInterpolate1"]("d-flex flex-column col-",m["\u0275\u0275pipeBind3"](1,4,e.stageFormFields,"excludeWeekOff","col")," p-0 checkbox"),m["\u0275\u0275advance"](4),m["\u0275\u0275textInterpolate"](m["\u0275\u0275pipeBind3"](5,8,e.stageFormFields,"excludeWeekOff","label"))}}function Q(e,t){}function Y(e,t){if(1&e&&m["\u0275\u0275template"](0,Q,0,0,"ng-template",38),2&e){m["\u0275\u0275nextContext"](3);const e=m["\u0275\u0275reference"](29);m["\u0275\u0275property"]("ngTemplateOutlet",e)}}function $(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275pipe"](1,"columnCustomization"),m["\u0275\u0275elementStart"](2,"span",34),m["\u0275\u0275text"](3),m["\u0275\u0275pipe"](4,"columnCustomization"),m["\u0275\u0275template"](5,Y,1,1,void 0,35),m["\u0275\u0275pipe"](6,"columnCustomization"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275element"](7,"app-input-search-single-select",45),m["\u0275\u0275pipe"](8,"columnCustomization"),m["\u0275\u0275pipe"](9,"columnCustomization"),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"](2);m["\u0275\u0275classMapInterpolate1"]("d-flex flex-column col-",m["\u0275\u0275pipeBind3"](1,10,e.stageFormFields,"status","col")," p-0"),m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate1"]("",m["\u0275\u0275pipeBind3"](4,14,e.stageFormFields,"status","label")," "),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",m["\u0275\u0275pipeBind3"](6,18,e.stageFormFields,"status","isMandatory")),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngClass",m["\u0275\u0275pureFunction1"](30,A,e.stageTransitionForms.get("status").hasError("required")&&e.stageTransitionForms.get("status").touched))("hasNoneOption",!m["\u0275\u0275pipeBind3"](8,22,e.stageFormFields,"status","isMandatory"))("list",e.statusMasterData)("hideMatLabel",!0)("placeholder",m["\u0275\u0275pipeBind3"](9,26,e.stageFormFields,"status","placeholder"))}}function ee(e,t){}function te(e,t){if(1&e&&m["\u0275\u0275template"](0,ee,0,0,"ng-template",38),2&e){m["\u0275\u0275nextContext"](3);const e=m["\u0275\u0275reference"](29);m["\u0275\u0275property"]("ngTemplateOutlet",e)}}function ne(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275pipe"](1,"columnCustomization"),m["\u0275\u0275elementStart"](2,"span",34),m["\u0275\u0275text"](3),m["\u0275\u0275pipe"](4,"columnCustomization"),m["\u0275\u0275template"](5,te,1,1,void 0,35),m["\u0275\u0275pipe"](6,"columnCustomization"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275element"](7,"app-input-search-single-select",46),m["\u0275\u0275pipe"](8,"columnCustomization"),m["\u0275\u0275pipe"](9,"columnCustomization"),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"](2);m["\u0275\u0275classMapInterpolate1"]("d-flex flex-column col-",m["\u0275\u0275pipeBind3"](1,10,e.stageFormFields,"template","col")," p-0"),m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate1"]("",m["\u0275\u0275pipeBind3"](4,14,e.stageFormFields,"template","label")," "),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",m["\u0275\u0275pipeBind3"](6,18,e.stageFormFields,"template","isMandatory")),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngClass",m["\u0275\u0275pureFunction1"](30,A,e.stageTransitionForms.get("template").hasError("required")&&e.stageTransitionForms.get("template").touched))("hasNoneOption",!m["\u0275\u0275pipeBind3"](8,22,e.stageFormFields,"template","isMandatory"))("list",e.templateMasterData)("hideMatLabel",!0)("placeholder",m["\u0275\u0275pipeBind3"](9,26,e.stageFormFields,"template","placeholder"))}}function ie(e,t){}function ae(e,t){if(1&e&&m["\u0275\u0275template"](0,ie,0,0,"ng-template",38),2&e){m["\u0275\u0275nextContext"](3);const e=m["\u0275\u0275reference"](29);m["\u0275\u0275property"]("ngTemplateOutlet",e)}}const oe=function(){return{fieldKey:"from"}};function re(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275pipe"](1,"columnCustomization"),m["\u0275\u0275elementStart"](2,"span",34),m["\u0275\u0275text"](3),m["\u0275\u0275pipe"](4,"columnCustomization"),m["\u0275\u0275template"](5,ae,1,1,void 0,35),m["\u0275\u0275pipe"](6,"columnCustomization"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](7,"app-single-select-chip",47),m["\u0275\u0275listener"]("onValueChange",(function(t){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"](2).onSingleSelectChipChanges(t)})),m["\u0275\u0275pipe"](8,"columnCustomization"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}if(2&e){const e=m["\u0275\u0275nextContext"](2);m["\u0275\u0275classMapInterpolate1"]("d-flex flex-column col-",m["\u0275\u0275pipeBind3"](1,11,e.stageFormFields,"from","col")," p-0"),m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate1"]("",m["\u0275\u0275pipeBind3"](4,15,e.stageFormFields,"from","label")," "),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",m["\u0275\u0275pipeBind3"](6,19,e.stageFormFields,"from","isMandatory")),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("isChip",!0)("placeholder",m["\u0275\u0275pipeBind3"](8,23,e.stageFormFields,"from","placeholder"))("masterData",e.fromMailPlaceHolderMasterData)("selectedValue",e.stageTransitionForms.get("from").value)("data",m["\u0275\u0275pureFunction0"](27,oe))("displayClose",!1)}}function se(e,t){}function le(e,t){if(1&e&&m["\u0275\u0275template"](0,se,0,0,"ng-template",38),2&e){m["\u0275\u0275nextContext"](3);const e=m["\u0275\u0275reference"](29);m["\u0275\u0275property"]("ngTemplateOutlet",e)}}const ce=function(){return{fieldKey:"to"}};function me(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275pipe"](1,"columnCustomization"),m["\u0275\u0275elementStart"](2,"span",34),m["\u0275\u0275text"](3),m["\u0275\u0275pipe"](4,"columnCustomization"),m["\u0275\u0275template"](5,le,1,1,void 0,35),m["\u0275\u0275pipe"](6,"columnCustomization"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](7,"app-multi-select-chip",48),m["\u0275\u0275listener"]("onValueChange",(function(t){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"](2).onMultiSelectChipChanges(t)})),m["\u0275\u0275pipe"](8,"columnCustomization"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}if(2&e){const e=m["\u0275\u0275nextContext"](2);m["\u0275\u0275classMapInterpolate1"]("d-flex flex-column col-",m["\u0275\u0275pipeBind3"](1,10,e.stageFormFields,"to","col")," p-0"),m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate1"]("",m["\u0275\u0275pipeBind3"](4,14,e.stageFormFields,"to","label")," "),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",m["\u0275\u0275pipeBind3"](6,18,e.stageFormFields,"to","isMandatory")),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("type",2)("placeholder",m["\u0275\u0275pipeBind3"](8,22,e.stageFormFields,"to","placeholder"))("masterData",e.mailPlaceHolderMasterData)("selectedValues",e.stageTransitionForms.get("to").value)("data",m["\u0275\u0275pureFunction0"](26,ce))}}function pe(e,t){}function de(e,t){if(1&e&&m["\u0275\u0275template"](0,pe,0,0,"ng-template",38),2&e){m["\u0275\u0275nextContext"](3);const e=m["\u0275\u0275reference"](29);m["\u0275\u0275property"]("ngTemplateOutlet",e)}}const ue=function(){return{fieldKey:"cc"}};function ge(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275pipe"](1,"columnCustomization"),m["\u0275\u0275elementStart"](2,"span",34),m["\u0275\u0275text"](3),m["\u0275\u0275pipe"](4,"columnCustomization"),m["\u0275\u0275template"](5,de,1,1,void 0,35),m["\u0275\u0275pipe"](6,"columnCustomization"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](7,"app-multi-select-chip",48),m["\u0275\u0275listener"]("onValueChange",(function(t){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"](2).onMultiSelectChipChanges(t)})),m["\u0275\u0275pipe"](8,"columnCustomization"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}if(2&e){const e=m["\u0275\u0275nextContext"](2);m["\u0275\u0275classMapInterpolate1"]("d-flex flex-column col-",m["\u0275\u0275pipeBind3"](1,10,e.stageFormFields,"cc","col")," p-0"),m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate1"]("",m["\u0275\u0275pipeBind3"](4,14,e.stageFormFields,"cc","label")," "),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",m["\u0275\u0275pipeBind3"](6,18,e.stageFormFields,"cc","isMandatory")),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("type",2)("placeholder",m["\u0275\u0275pipeBind3"](8,22,e.stageFormFields,"cc","placeholder"))("masterData",e.mailPlaceHolderMasterData)("selectedValues",e.stageTransitionForms.get("cc").value)("data",m["\u0275\u0275pureFunction0"](26,ue))}}function he(e,t){}function fe(e,t){if(1&e&&m["\u0275\u0275template"](0,he,0,0,"ng-template",38),2&e){m["\u0275\u0275nextContext"](3);const e=m["\u0275\u0275reference"](29);m["\u0275\u0275property"]("ngTemplateOutlet",e)}}const Ce=function(){return{fieldKey:"replyTo"}};function ve(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275pipe"](1,"columnCustomization"),m["\u0275\u0275elementStart"](2,"span",34),m["\u0275\u0275text"](3),m["\u0275\u0275pipe"](4,"columnCustomization"),m["\u0275\u0275template"](5,fe,1,1,void 0,35),m["\u0275\u0275pipe"](6,"columnCustomization"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](7,"app-multi-select-chip",48),m["\u0275\u0275listener"]("onValueChange",(function(t){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"](2).onMultiSelectChipChanges(t)})),m["\u0275\u0275pipe"](8,"columnCustomization"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}if(2&e){const e=m["\u0275\u0275nextContext"](2);m["\u0275\u0275classMapInterpolate1"]("d-flex flex-column col-",m["\u0275\u0275pipeBind3"](1,10,e.stageFormFields,"replyTo","col")," p-0"),m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate1"]("",m["\u0275\u0275pipeBind3"](4,14,e.stageFormFields,"replyTo","label")," "),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",m["\u0275\u0275pipeBind3"](6,18,e.stageFormFields,"replyTo","isMandatory")),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("type",2)("placeholder",m["\u0275\u0275pipeBind3"](8,22,e.stageFormFields,"replyTo","placeholder"))("masterData",e.mailPlaceHolderMasterData)("selectedValues",e.stageTransitionForms.get("replyTo").value)("data",m["\u0275\u0275pureFunction0"](26,Ce))}}function xe(e,t){}function Fe(e,t){if(1&e&&m["\u0275\u0275template"](0,xe,0,0,"ng-template",38),2&e){m["\u0275\u0275nextContext"](3);const e=m["\u0275\u0275reference"](29);m["\u0275\u0275property"]("ngTemplateOutlet",e)}}function ye(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275pipe"](1,"columnCustomization"),m["\u0275\u0275elementStart"](2,"span",34),m["\u0275\u0275text"](3),m["\u0275\u0275pipe"](4,"columnCustomization"),m["\u0275\u0275template"](5,Fe,1,1,void 0,35),m["\u0275\u0275pipe"](6,"columnCustomization"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](7,"mat-form-field",49),m["\u0275\u0275element"](8,"input",50),m["\u0275\u0275pipe"](9,"columnCustomization"),m["\u0275\u0275pipe"](10,"columnCustomization"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"](2);m["\u0275\u0275classMapInterpolate1"]("d-flex flex-column col-",m["\u0275\u0275pipeBind3"](1,7,e.stageFormFields,"subject","col")," p-0"),m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate1"]("",m["\u0275\u0275pipeBind3"](4,11,e.stageFormFields,"subject","label")," "),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",m["\u0275\u0275pipeBind3"](6,15,e.stageFormFields,"subject","isMandatory")),m["\u0275\u0275advance"](3),m["\u0275\u0275property"]("placeholder",m["\u0275\u0275pipeBind3"](9,19,e.stageFormFields,"subject","placeholder"))("disabled",m["\u0275\u0275pipeBind3"](10,23,e.stageFormFields,"subject","isDisabled"))}}function Me(e,t){}function be(e,t){if(1&e&&m["\u0275\u0275template"](0,Me,0,0,"ng-template",38),2&e){m["\u0275\u0275nextContext"](3);const e=m["\u0275\u0275reference"](29);m["\u0275\u0275property"]("ngTemplateOutlet",e)}}const we=function(){return["8pt","10pt","12pt","14pt","18pt","24pt","36pt"]},Oe=function(){return{"aria-label":"Font size"}},_e=function(e){return{inputAttr:e}},Te=function(){return["Arial","Courier New","Georgia","Impact","Lucida Console","Tahoma","Times New Roman","Verdana"]},Pe=function(){return{"aria-label":"Font family"}};function Se(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275pipe"](1,"columnCustomization"),m["\u0275\u0275elementStart"](2,"span",34),m["\u0275\u0275text"](3),m["\u0275\u0275pipe"](4,"columnCustomization"),m["\u0275\u0275template"](5,be,1,1,void 0,35),m["\u0275\u0275pipe"](6,"columnCustomization"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](7,"dx-html-editor",51,52),m["\u0275\u0275elementStart"](9,"dxo-toolbar",53),m["\u0275\u0275element"](10,"dxi-item",54),m["\u0275\u0275element"](11,"dxi-item",55),m["\u0275\u0275element"](12,"dxi-item",56),m["\u0275\u0275element"](13,"dxi-item",57),m["\u0275\u0275element"](14,"dxi-item",58),m["\u0275\u0275element"](15,"dxi-item",59),m["\u0275\u0275element"](16,"dxi-item",60),m["\u0275\u0275element"](17,"dxi-item",61),m["\u0275\u0275element"](18,"dxi-item",62),m["\u0275\u0275element"](19,"dxi-item",63),m["\u0275\u0275element"](20,"dxi-item",64),m["\u0275\u0275element"](21,"dxi-item",65),m["\u0275\u0275element"](22,"dxi-item",66),m["\u0275\u0275element"](23,"dxi-item",67),m["\u0275\u0275element"](24,"dxi-item",68),m["\u0275\u0275element"](25,"dxi-item",69),m["\u0275\u0275element"](26,"dxi-item",70),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](27,"div",71),m["\u0275\u0275elementStart"](28,"span",72),m["\u0275\u0275text"](29),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](30,"span",73,74),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275reference"](32),n=m["\u0275\u0275nextContext"](2),i=m["\u0275\u0275reference"](31);return n.openOverlay(t,i)}))("mouseenter",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275reference"](8);return m["\u0275\u0275nextContext"](2).saveCursorPosition(t)})),m["\u0275\u0275text"](33,"Choose Placeholder"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}if(2&e){const e=m["\u0275\u0275nextContext"](2);m["\u0275\u0275classMapInterpolate1"]("d-flex flex-column col-",m["\u0275\u0275pipeBind3"](1,11,e.stageFormFields,"body","col")," p-0"),m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate1"]("",m["\u0275\u0275pipeBind3"](4,15,e.stageFormFields,"body","label")," "),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",m["\u0275\u0275pipeBind3"](6,19,e.stageFormFields,"body","isMandatory")),m["\u0275\u0275advance"](4),m["\u0275\u0275property"]("multiline",!1),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("acceptedValues",m["\u0275\u0275pureFunction0"](23,we))("options",m["\u0275\u0275pureFunction1"](25,_e,m["\u0275\u0275pureFunction0"](24,Oe))),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("acceptedValues",m["\u0275\u0275pureFunction0"](27,Te))("options",m["\u0275\u0275pureFunction1"](29,_e,m["\u0275\u0275pureFunction0"](28,Pe))),m["\u0275\u0275advance"](18),m["\u0275\u0275textInterpolate"]("@ Placeholders")}}function Ee(e,t){}function De(e,t){if(1&e&&m["\u0275\u0275template"](0,Ee,0,0,"ng-template",38),2&e){m["\u0275\u0275nextContext"](3);const e=m["\u0275\u0275reference"](29);m["\u0275\u0275property"]("ngTemplateOutlet",e)}}function Ie(e,t){1&e&&m["\u0275\u0275element"](0,"dx-html-editor",79)}function Le(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275pipe"](1,"columnCustomization"),m["\u0275\u0275elementStart"](2,"div",75),m["\u0275\u0275elementStart"](3,"div"),m["\u0275\u0275elementStart"](4,"span",34),m["\u0275\u0275text"](5),m["\u0275\u0275pipe"](6,"columnCustomization"),m["\u0275\u0275template"](7,De,1,1,void 0,35),m["\u0275\u0275pipe"](8,"columnCustomization"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](9,"div"),m["\u0275\u0275elementStart"](10,"div",76),m["\u0275\u0275element"](11,"mat-slide-toggle",77),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](12,Ie,1,0,"dx-html-editor",78),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"](2);m["\u0275\u0275classMapInterpolate1"]("d-flex flex-column col-",m["\u0275\u0275pipeBind3"](1,6,e.stageFormFields,"signature","col")," p-0"),m["\u0275\u0275advance"](5),m["\u0275\u0275textInterpolate1"]("",m["\u0275\u0275pipeBind3"](6,10,e.stageFormFields,"signature","label")," "),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",m["\u0275\u0275pipeBind3"](8,14,e.stageFormFields,"signature","isMandatory")||e.stageTransitionForms.get("isSignatureOn").value),m["\u0275\u0275advance"](5),m["\u0275\u0275property"]("ngIf",e.stageTransitionForms.get("isSignatureOn").value)}}function ke(e,t){}function Ve(e,t){if(1&e&&m["\u0275\u0275template"](0,ke,0,0,"ng-template",38),2&e){m["\u0275\u0275nextContext"](3);const e=m["\u0275\u0275reference"](29);m["\u0275\u0275property"]("ngTemplateOutlet",e)}}function ze(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div"),m["\u0275\u0275pipe"](1,"columnCustomization"),m["\u0275\u0275elementStart"](2,"span",34),m["\u0275\u0275text"](3),m["\u0275\u0275pipe"](4,"columnCustomization"),m["\u0275\u0275template"](5,Ve,1,1,void 0,35),m["\u0275\u0275pipe"](6,"columnCustomization"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](7,"div",80),m["\u0275\u0275listener"]("click",(function(){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"](2).openAttachmentPlugin()})),m["\u0275\u0275text"](8," Click to Add or View Attachment(s) "),m["\u0275\u0275element"](9,"br"),m["\u0275\u0275elementStart"](10,"span",81),m["\u0275\u0275text"](11),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}if(2&e){const e=m["\u0275\u0275nextContext"](2);m["\u0275\u0275classMapInterpolate1"]("d-flex flex-column col-",m["\u0275\u0275pipeBind3"](1,6,e.stageFormFields,"attachment","col")," p-0"),m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate1"]("",m["\u0275\u0275pipeBind3"](4,10,e.stageFormFields,"attachment","label")," "),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",m["\u0275\u0275pipeBind3"](6,14,e.stageFormFields,"attachment","isMandatory")),m["\u0275\u0275advance"](6),m["\u0275\u0275textInterpolate1"]("",e.attachmentCount," attachment(s) added!")}}const je=function(e){return{"pointer-events":e}};function Be(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div",30),m["\u0275\u0275elementStart"](1,"form",31),m["\u0275\u0275template"](2,z,11,27,"div",32),m["\u0275\u0275pipe"](3,"columnCustomization"),m["\u0275\u0275template"](4,N,10,32,"div",32),m["\u0275\u0275pipe"](5,"columnCustomization"),m["\u0275\u0275template"](6,H,10,32,"div",32),m["\u0275\u0275pipe"](7,"columnCustomization"),m["\u0275\u0275template"](8,U,10,32,"div",32),m["\u0275\u0275pipe"](9,"columnCustomization"),m["\u0275\u0275template"](10,J,9,17,"div",32),m["\u0275\u0275pipe"](11,"columnCustomization"),m["\u0275\u0275template"](12,X,6,12,"div",32),m["\u0275\u0275pipe"](13,"columnCustomization"),m["\u0275\u0275template"](14,$,10,32,"div",32),m["\u0275\u0275pipe"](15,"columnCustomization"),m["\u0275\u0275template"](16,ne,10,32,"div",32),m["\u0275\u0275pipe"](17,"columnCustomization"),m["\u0275\u0275template"](18,re,9,28,"div",32),m["\u0275\u0275pipe"](19,"columnCustomization"),m["\u0275\u0275template"](20,me,9,27,"div",32),m["\u0275\u0275pipe"](21,"columnCustomization"),m["\u0275\u0275template"](22,ge,9,27,"div",32),m["\u0275\u0275pipe"](23,"columnCustomization"),m["\u0275\u0275template"](24,ve,9,27,"div",32),m["\u0275\u0275pipe"](25,"columnCustomization"),m["\u0275\u0275template"](26,ye,11,27,"div",32),m["\u0275\u0275pipe"](27,"columnCustomization"),m["\u0275\u0275template"](28,Se,34,31,"div",32),m["\u0275\u0275pipe"](29,"columnCustomization"),m["\u0275\u0275template"](30,Le,13,18,"div",32),m["\u0275\u0275pipe"](31,"columnCustomization"),m["\u0275\u0275template"](32,ze,12,18,"div",32),m["\u0275\u0275pipe"](33,"columnCustomization"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](34,"div",33),m["\u0275\u0275listener"]("click",(function(){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"]().onClickSave()})),m["\u0275\u0275text"](35," Save "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}if(2&e){const e=m["\u0275\u0275nextContext"]();m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("formGroup",e.stageTransitionForms),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",m["\u0275\u0275pipeBind3"](3,18,e.stageFormFields,"stageName","isActive")),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",m["\u0275\u0275pipeBind3"](5,22,e.stageFormFields,"action","isActive")),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",m["\u0275\u0275pipeBind3"](7,26,e.stageFormFields,"frequency","isActive")),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",m["\u0275\u0275pipeBind3"](9,30,e.stageFormFields,"whenToSend","isActive")&&2==e.stageTransitionForms.get("frequency").value),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",m["\u0275\u0275pipeBind3"](11,34,e.stageFormFields,"whenToSendTime","isActive")&&2==e.stageTransitionForms.get("frequency").value),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",m["\u0275\u0275pipeBind3"](13,38,e.stageFormFields,"excludeWeekOff","isActive")&&2==e.stageTransitionForms.get("frequency").value),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",m["\u0275\u0275pipeBind3"](15,42,e.stageFormFields,"status","isActive")),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",m["\u0275\u0275pipeBind3"](17,46,e.stageFormFields,"template","isActive")),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",m["\u0275\u0275pipeBind3"](19,50,e.stageFormFields,"from","isActive")),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",m["\u0275\u0275pipeBind3"](21,54,e.stageFormFields,"to","isActive")),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",m["\u0275\u0275pipeBind3"](23,58,e.stageFormFields,"cc","isActive")),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",m["\u0275\u0275pipeBind3"](25,62,e.stageFormFields,"replyTo","isActive")),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",m["\u0275\u0275pipeBind3"](27,66,e.stageFormFields,"subject","isActive")),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",m["\u0275\u0275pipeBind3"](29,70,e.stageFormFields,"body","isActive")),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",m["\u0275\u0275pipeBind3"](31,74,e.stageFormFields,"signature","isActive")),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",m["\u0275\u0275pipeBind3"](33,78,e.stageFormFields,"attachment","isActive")),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngStyle",m["\u0275\u0275pureFunction1"](82,je,e.isApiInProgress?"none":""))}}function Ae(e,t){1&e&&(m["\u0275\u0275elementStart"](0,"span",82),m["\u0275\u0275text"](1,"*"),m["\u0275\u0275elementEnd"]())}function Ne(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"app-placeholder-group-menu-overlay",83),m["\u0275\u0275listener"]("onConfirmation",(function(t){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"]().onSelectPlaceholder(t)})),m["\u0275\u0275elementEnd"]()}if(2&e){const e=m["\u0275\u0275nextContext"]();m["\u0275\u0275property"]("list",e.placeholdersMasterData)}}let qe=(()=>{class e{constructor(e,t,n,i,o,s,l,c,m,p,d){this.data=e,this._dialogRef=t,this._fb=n,this._atsJobService=i,this._atsMasterService=o,this._toaster=s,this._viewContainerRef=l,this._overlay=c,this._dialog=m,this._sharedService=p,this._atsTemplateSettingsService=d,this._onDestroy=new r.b,this.savedCursorPosition=0,this.attachmentCount=0,this.attachmentConfig={},this.filesUploaded=[],this.currentData={},this.isLoading=!0,this.isApiInProgress=!1,this.stageFormFields=[],this.actionMasterData=[],this.frequencyMasterData=[],this.whenToSendMasterData=[],this.statusMasterData=[],this.templateMasterData=[],this.mailPlaceHolderMasterData=[],this.fromMailPlaceHolderMasterData=[],this.placeholdersMasterData=[],this.stageTransitionForms=this._fb.group({jobId:new a.j(null),stageId:new a.j(null),stageName:new a.j(null),action:new a.j(null),frequency:new a.j(null),whenToSend:new a.j(null),whenToSendTime:new a.j(null),excludeWeekOff:new a.j(!1),status:new a.j(null),template:new a.j(null),from:new a.j(null),to:new a.j(null),cc:new a.j(null),replyTo:new a.j(null),subject:new a.j(null),body:new a.j(null),signature:new a.j(null),isSignatureOn:new a.j(!1)})}ngOnInit(){return Object(i.c)(this,void 0,void 0,(function*(){yield this.getAtsFormsConfig("hiringStageTransition"),this.addDynamicValidators(),yield this.getAttachmentConfig(),this.data.isExistingData&&(this.retrieveUploadedObjects(),this.getStageTransistionById(this.data.jobId,this.data.stageId)),yield Promise.all([this.getStageActionType(),this.getStageTransitionType(),this.getHiringFlowStatusByStageId(),this.getStageTransitionFrequency(),this.getSenderEmail(),this.getReceiverEmail(),this.getPlaceHolders(),this.fetchAllEmailTemplates()]).then(e=>{this.stageTransitionForms.get("stageId").setValue(this.data.stageId),this.stageTransitionForms.get("stageName").setValue(this.data.stageName),this.stageTransitionForms.get("jobId").setValue(this.data.jobId),this.stageTransitionForms.get("stageName").disable(),this.isLoading=!1}),this.stageTransitionForms.get("template").valueChanges.subscribe(e=>{if(null!=e){let t=this.templateMasterData.find(t=>t.id==e);t&&this.stageTransitionForms.patchValue({subject:(null==t?void 0:t.subject)?t.subject:null,body:(null==t?void 0:t.body)?t.body:null,signature:(null==t?void 0:t.signature)?t.signature:null,isSignatureOn:!!(null==t?void 0:t.signature)})}}),this.stageTransitionForms.get("frequency").valueChanges.subscribe(e=>{2==e?(this.stageTransitionForms.get("whenToSend").setValidators(a.H.required),this.stageTransitionForms.get("whenToSendTime").setValidators(a.H.required)):(this.stageTransitionForms.get("excludeWeekOff").setValue(!1),this.stageTransitionForms.get("whenToSend").clearValidators(),this.stageTransitionForms.get("whenToSend").setErrors(null),this.stageTransitionForms.get("whenToSend").setValue(null),this.stageTransitionForms.get("whenToSend").updateValueAndValidity(),this.stageTransitionForms.get("whenToSendTime").clearValidators(),this.stageTransitionForms.get("whenToSendTime").setErrors(null),this.stageTransitionForms.get("whenToSendTime").setValue(null),this.stageTransitionForms.get("whenToSendTime").updateValueAndValidity())}),this.stageTransitionForms.get("isSignatureOn").valueChanges.subscribe(e=>{e?this.stageTransitionForms.get("signature").setValidators(a.H.required):(this.stageTransitionForms.get("signature").clearValidators(),this.stageTransitionForms.get("signature").setErrors(null),this.stageTransitionForms.get("signature").setValue(null),this.stageTransitionForms.get("signature").updateValueAndValidity())}),this.stageTransitionForms.get("from").valueChanges.subscribe(e=>{var t;if(e){let n=null===(t=this.fromMailPlaceHolderMasterData.find(t=>t.id==e))||void 0===t?void 0:t.signature;n&&(this.stageTransitionForms.get("signature").setValue(n),this.stageTransitionForms.get("isSignatureOn").setValue(!0))}else this.stageTransitionForms.get("signature").setValue(null),this.stageTransitionForms.get("isSignatureOn").setValue(!1)})}))}addDynamicValidators(){this.stageFormFields.forEach(e=>{const t=this.stageTransitionForms.get(e.key);t&&t.setValidators([e.isMandatory?a.H.required:null].filter(e=>null!==e))})}markFormGroupTouched(e){return Object(i.c)(this,void 0,void 0,(function*(){Object.values(e.controls).forEach(e=>{e instanceof a.m?this.markFormGroupTouched(e):(e.markAsTouched(),e.markAllAsTouched(),e.markAsDirty())})}))}onSingleSelectChipChanges(e){this.stageTransitionForms.get(e.data.fieldKey).setValue(e.val)}onMultiSelectChipChanges(e){this.stageTransitionForms.get(e.data.fieldKey).setValue(e.val)}openAttachmentPlugin(){this._dialog.open(c.a,{width:"100%",height:"90%",data:{data:{destinationBucket:this.attachmentConfig.bucket_name,routingKey:this.attachmentConfig.routing_key,contextId:"job-"+this.data.jobId+"-"+this.data.stageId,allowEdit:!0}},disableClose:!0}).afterClosed().subscribe(e=>{this.attachmentCount=e.fileCount,this.filesUploaded.splice(0),this.retrieveUploadedObjects()})}openOverlay(e,t){var n;if(!(null===(n=this.overlayRef)||void 0===n?void 0:n.hasAttached())){const n=this._overlay.position().flexibleConnectedTo(e).withFlexibleDimensions(!0).withPush(!0).withViewportMargin(30).withGrowAfterOpen(!0).withPositions([{originX:"center",originY:"top",overlayX:"center",overlayY:"bottom"}]),i=this._overlay.scrollStrategies.close();this.overlayRef=this._overlay.create({positionStrategy:n,scrollStrategy:i,hasBackdrop:!0,backdropClass:"",panelClass:["pop-up"]});const a=new l.h(t,this._viewContainerRef);this.overlayRef.attach(a),this.overlayRef.backdropClick().subscribe(()=>{this.closeOverlay()})}}closeOverlay(){var e;null===(e=this.overlayRef)||void 0===e||e.dispose()}saveCursorPosition(e){const t=e.instance.getSelection();this.savedCursorPosition=t?t.index:0}onSelectPlaceholder(e){this.closeOverlay(),this.insertPlaceholder(this.bodyEditor,e.placeholder,{color:"#1890FF"})}insertPlaceholder(e,t,n){e.instance.insertText(this.savedCursorPosition,t,n)}patchValuesToForm(){this.stageTransitionForms.patchValue({action:this.currentData.action_type||null,frequency:this.currentData.transition_type||null,whenToSend:this.currentData.no_of_days||null,whenToSendTime:this.currentData.time||null,excludeWeekOff:this.currentData.exclude_weekoff||!1,status:this.currentData.status||null,template:this.currentData.template||null,from:this.currentData.fromEmail||null,to:this.currentData.toEmail||null,cc:this.currentData.cc||null,replyTo:this.currentData.reply_to||null,subject:this.currentData.subject||null,body:this.currentData.body||null,signature:this.currentData.signature||null,isSignatureOn:!!this.currentData.signature}),2==this.currentData.transition_type&&(this.stageTransitionForms.get("whenToSend").setValidators(a.H.required),this.stageTransitionForms.get("whenToSendTime").setValidators(a.H.required)),this.currentData.signature&&this.stageTransitionForms.get("signature").setValidators(a.H.required)}onClose(e){this._dialogRef.close({id:this.data.stageId,flag:e})}onClickSave(){return Object(i.c)(this,void 0,void 0,(function*(){if(yield this.markFormGroupTouched(this.stageTransitionForms),this.stageTransitionForms.invalid)return void this._toaster.showWarning("Warning \u26a0\ufe0f","Kindly fill all Mandatory Fields!",7e3);this.isApiInProgress=!0;let e=[],t=this.data.isExistingData?this.currentData.rule_id:null;for(let a=0;a<this.filesUploaded.length;a++)e.push({bucketName:this.filesUploaded[a].bucket,key:this.filesUploaded[a].key});let n=[],i={ruleId:t,stageId:this.data.stageId,jobId:this.data.jobId,fromEmail:this.stageTransitionForms.get("from").value,toEmail:this.stageTransitionForms.get("to").value,cc:this.stageTransitionForms.get("cc").value,subject:this.stageTransitionForms.get("subject").value,body:this.stageTransitionForms.get("body").value,signature:this.stageTransitionForms.get("signature").value,transitionType:this.stageTransitionForms.get("frequency").value,actionType:this.stageTransitionForms.get("action").value,template:this.stageTransitionForms.get("template").value,no_of_days:this.stageTransitionForms.get("whenToSend").value,time:this.stageTransitionForms.get("whenToSendTime").value,status:this.stageTransitionForms.get("status").value,excludeWeekOff:this.stageTransitionForms.get("excludeWeekOff").value,attachment:e};n.push(i),yield this.updateStageTransisitonRule(n)}))}getAtsFormsConfig(e){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._atsMasterService.getAtsFormsConfig(e).pipe(Object(s.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?this.stageFormFields=e.data.form[0].formFields:this._toaster.showError("Error",e.msg,7e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Forms Configuration Retrieval Failed!",7e3),n()}}))}))}getStageActionType(){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getStageActionType().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.actionMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Master Data Retrieval Failed!",7e3),t()}}))}))}getStageTransitionType(){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getStageTransitionType().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.frequencyMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Master Data Retrieval Failed!",7e3),t()}}))}))}getSenderEmail(){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getSenderEmail().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.fromMailPlaceHolderMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Master Data Retrieval Failed!",7e3),t()}}))}))}getReceiverEmail(){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getReceiverEmail().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.mailPlaceHolderMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Master Data Retrieval Failed!",7e3),t()}}))}))}getPlaceHolders(){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getPlaceHolders().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.placeholdersMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Master Data Retrieval Failed!",7e3),t()}}))}))}getHiringFlowStatusByStageId(){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getHiringFlowStatusByStageId(this.data.stageId).pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.statusMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Master Data Retrieval Failed!",7e3),t()}}))}))}fetchAllEmailTemplates(){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsTemplateSettingsService.fetchAllEmailTemplates(null,null,null).pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.templateMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Master Data Retrieval Failed!",7e3),t()}}))}))}getAttachmentConfig(){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getAttachmentConfig(1).pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?t.data&&t.data.length>0&&(this.attachmentConfig=t.data[0]):this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Master Data Retrieval Failed!",7e3),t()}}))}))}retrieveUploadedObjects(){return Object(i.c)(this,void 0,void 0,(function*(){this._sharedService.retrieveUploadedObjects(this.attachmentConfig.bucket_name,"job-"+this.data.jobId+"-"+this.data.stageId).subscribe(e=>{this.attachmentCount=e.data.length,this.filesUploaded=e.data},e=>{console.error(e)})}))}updateStageTransisitonRule(e){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._atsJobService.updateStageTransisitonRule(e).pipe(Object(s.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?(this._toaster.showSuccess("Success","Stage Transition Rule Details Saved Successfully!",7e3),this.onClose(!0)):this._toaster.showError("Error",e.msg,7e3),this.isApiInProgress=!1,t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Master Data Retrieval Failed!",7e3),this.isApiInProgress=!1,n()}}))}))}getStageTransistionById(e,t){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((n,i)=>this._atsJobService.getStageTransistionById(e,t).pipe(Object(s.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?(this.currentData=e.data.length>0?e.data[e.data.length-1]:{},this.patchValuesToForm()):this._toaster.showError("Error",e.msg,7e3),n(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Data Retrieval Failed!",7e3),i()}}))}))}getStageTransitionFrequency(){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getStageTransitionFrequency().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.whenToSendMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Master Data Retrieval Failed!",7e3),t()}}))}))}}return e.\u0275fac=function(t){return new(t||e)(m["\u0275\u0275directiveInject"](o.a),m["\u0275\u0275directiveInject"](o.h),m["\u0275\u0275directiveInject"](a.i),m["\u0275\u0275directiveInject"](p.a),m["\u0275\u0275directiveInject"](d.a),m["\u0275\u0275directiveInject"](u.a),m["\u0275\u0275directiveInject"](m.ViewContainerRef),m["\u0275\u0275directiveInject"](g.e),m["\u0275\u0275directiveInject"](o.b),m["\u0275\u0275directiveInject"](h.a),m["\u0275\u0275directiveInject"](f.a))},e.\u0275cmp=m["\u0275\u0275defineComponent"]({type:e,selectors:[["app-stage-transition-dialog"]],viewQuery:function(e,t){if(1&e&&(m["\u0275\u0275viewQuery"](D,!0),m["\u0275\u0275viewQuery"](I,!0)),2&e){let e;m["\u0275\u0275queryRefresh"](e=m["\u0275\u0275loadQuery"]())&&(t.triggerPlaceholderChange=e.first),m["\u0275\u0275queryRefresh"](e=m["\u0275\u0275loadQuery"]())&&(t.bodyEditor=e.first)}},decls:32,vars:3,consts:[[1,"main-container"],[1,"d-flex","align-items-center","justify-content-between","header"],[1,"title","p-0"],[1,"d-flex","align-items-center"],["width","273","height","72","viewBox","0 0 273 72","fill","none"],["d","M133.603 -22.5168V4.76469C133.603 8.35397 132.087 11.811 129.39 14.4067C125.91 17.7302 120.857 19.1903 115.947 18.2782L115.946 18.2781L93.9778 14.2877L103.74 5.17581L116.468 7.51274L116.468 7.51278C119.364 8.04124 122.088 5.97651 122.088 3.19398V-11.8208L133.603 -22.5168Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M179.021 14.2879L157.081 18.2781L157.08 18.2782C152.176 19.1903 147.129 17.73 143.653 14.4063L143.651 14.4043C140.911 11.856 139.396 8.40031 139.396 4.81089V-22.5164L150.897 -11.8208V2.96299C150.897 5.88334 153.766 8.04193 156.76 7.46638C156.76 7.46632 156.76 7.46626 156.761 7.46621L169.223 5.17578L179.021 14.2879Z","fill","#EAEDF3","stroke","#DADCE2","stroke-width","0.421296"],["d","M133.603 37.6434V64.9408L122.088 54.2565V38.8894C122.088 36.2934 119.563 34.3712 116.817 34.8517L116.816 34.852L103.74 37.2326L93.9781 28.1311L115.946 24.1452L115.947 24.145C120.857 23.234 125.91 24.6926 129.391 28.0125C132.087 30.6053 133.603 34.0584 133.603 37.6434Z","fill","#EAEDF3","stroke","#DADCE2","stroke-width","0.421296"],["d","M139.396 64.9404V37.6589C139.396 34.0697 140.912 30.6127 143.609 28.0171C147.089 24.6935 152.143 23.2333 157.052 24.1453L157.053 24.1455L179.02 28.1357L169.26 37.2017L155.935 34.7723C155.935 34.7723 155.935 34.7723 155.935 34.7723C153.337 34.2898 150.912 36.1239 150.912 38.629V54.2443L139.396 64.9404Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M40.0758 14.7878V42.0694C40.0758 45.6587 38.5596 49.1157 35.8631 51.7114C32.3828 55.0348 27.3294 56.495 22.4198 55.5829L22.419 55.5828L0.45043 51.5924L10.2125 42.4805L22.9402 44.8174L22.9404 44.8175C25.8368 45.3459 28.5604 43.2812 28.5604 40.4987V25.4839L40.0758 14.7878Z","fill","#F3F5FA","stroke","#DADCE2","stroke-width","0.421296"],["d","M85.4934 51.5926L63.5532 55.5828L63.5524 55.5829C58.6488 56.495 53.6015 55.0347 50.1255 51.711L50.1234 51.709C47.3832 49.1606 45.8689 45.705 45.8689 42.1156V14.7883L57.3696 25.4838V40.2677C57.3696 43.188 60.2384 45.3466 63.2324 44.7711C63.2327 44.771 63.233 44.771 63.2333 44.7709L75.6953 42.4805L85.4934 51.5926Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M40.0758 74.9481V102.245L28.5604 91.5612V76.1941C28.5604 73.5981 26.0352 71.6758 23.29 72.1564L23.2886 72.1567L10.2125 74.5373L0.450787 65.4358L22.419 61.4499L22.4198 61.4497C27.3296 60.5386 32.3831 61.9973 35.8634 65.3172C38.5597 67.91 40.0758 71.3631 40.0758 74.9481Z","fill","#F7F8FA","stroke","#DADCE2","stroke-width","0.421296"],["d","M45.8689 102.245V74.9636C45.8689 71.3744 47.385 67.9174 50.0813 65.3218C53.5617 61.9982 58.6152 60.5379 63.5248 61.45L63.5256 61.4502L85.493 65.4403L75.7324 74.5064L62.4081 72.077C62.408 72.077 62.4078 72.077 62.4077 72.0769C59.8092 71.5944 57.3843 73.4286 57.3843 75.9337V91.549L45.8689 102.245Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M40.0758 14.4832V41.7647C40.0758 45.354 38.5596 48.811 35.8631 51.4067C32.3828 54.7302 27.3294 56.1903 22.4198 55.2782L22.419 55.2781L0.45043 51.2877L10.2125 42.1758L22.9402 44.5127L22.9404 44.5128C25.8368 45.0412 28.5604 42.9765 28.5604 40.194V25.1792L40.0758 14.4832Z","fill","#EAEDF3","fill-opacity","0.5","stroke","#DADCE2","stroke-width","0.421296"],["d","M85.4934 51.2879L63.5532 55.2781L63.5524 55.2782C58.6488 56.1903 53.6015 54.73 50.1255 51.4063L50.1234 51.4043C47.3832 48.856 45.8689 45.4003 45.8689 41.8109V14.4836L57.3696 25.1792V39.963C57.3696 42.8833 60.2384 45.0419 63.2324 44.4664C63.2327 44.4663 63.233 44.4663 63.2333 44.4662L75.6953 42.1758L85.4934 51.2879Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M40.0758 74.6434V101.941L28.5604 91.2565V75.8894C28.5604 73.2934 26.0352 71.3712 23.29 71.8517L23.2886 71.852L10.2125 74.2326L0.450787 65.1311L22.419 61.1452L22.4198 61.145C27.3296 60.234 32.3831 61.6926 35.8634 65.0125C38.5597 67.6053 40.0758 71.0584 40.0758 74.6434Z","fill","#EAEDF3","stroke","#DADCE2","stroke-width","0.421296"],["d","M45.8689 101.94V74.6589C45.8689 71.0697 47.385 67.6127 50.0813 65.0171C53.5617 61.6935 58.6152 60.2333 63.5248 61.1453L63.5256 61.1455L85.493 65.1357L75.7324 74.2017L62.4081 71.7723C62.408 71.7723 62.4078 71.7723 62.4077 71.7723C59.8092 71.2898 57.3843 73.1239 57.3843 75.629V91.2443L45.8689 101.94Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M227.13 14.7878V42.0694C227.13 45.6587 225.614 49.1157 222.918 51.7114C219.437 55.0348 214.384 56.495 209.475 55.5829L209.474 55.5828L187.505 51.5924L197.267 42.4805L209.995 44.8174L209.995 44.8175C212.892 45.3459 215.615 43.2812 215.615 40.4987V25.4839L227.13 14.7878Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M272.548 51.5926L250.608 55.5828L250.607 55.5829C245.703 56.495 240.656 55.0347 237.18 51.711L237.178 51.709C234.438 49.1606 232.924 45.705 232.924 42.1156V14.7883L244.424 25.4838V40.2677C244.424 43.188 247.293 45.3466 250.287 44.7711C250.287 44.771 250.288 44.771 250.288 44.7709L262.75 42.4805L272.548 51.5926Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M227.13 74.9481V102.245L215.615 91.5612V76.1941C215.615 73.5981 213.09 71.6758 210.345 72.1564L210.343 72.1567L197.267 74.5373L187.505 65.4358L209.474 61.4499L209.474 61.4497C214.384 60.5386 219.438 61.9973 222.918 65.3172C225.614 67.91 227.13 71.3631 227.13 74.9481Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M232.924 102.245V74.9636C232.924 71.3744 234.44 67.9174 237.136 65.3218C240.616 61.9982 245.67 60.5379 250.58 61.45L250.58 61.4502L272.548 65.4403L262.787 74.5064L249.463 72.077C249.463 72.077 249.463 72.077 249.462 72.0769C246.864 71.5944 244.439 73.4286 244.439 75.9337V91.549L232.924 102.245Z","fill","#EAEDF3","fill-opacity","0.5","stroke","#DADCE2","stroke-width","0.421296"],[1,"icon",3,"click"],[1,"icon"],["class","d-flex align-items-center justify-content-center loading-screen",4,"ngIf"],["class","main-screen",4,"ngIf"],["mandatoryTemplate",""],["cdkConnectedOverlay","",3,"cdkConnectedOverlayOrigin"],["triggerPlaceholderChange",""],[1,"d-flex","align-items-center","justify-content-center","loading-screen"],["diameter","40",1,"green-spinner"],[1,"main-screen"],[1,"d-flex","flex-wrap",2,"row-gap","16px",3,"formGroup"],[3,"class",4,"ngIf"],[1,"save-btn",3,"ngStyle","click"],[1,"form-label"],[4,"ngIf"],["appearance","outline",1,"form-field-class"],["type","text","matInput","","formControlName","stageName","readonly","",3,"placeholder","disabled"],[3,"ngTemplateOutlet"],["formControlName","action",1,"form-field-class-app-input-wo-width",3,"ngClass","hasNoneOption","list","hideMatLabel","placeholder"],["formControlName","frequency",1,"form-field-class-app-input-wo-width",3,"ngClass","hasNoneOption","list","hideMatLabel","placeholder"],["formControlName","whenToSend",1,"form-field-class-app-input-wo-width",3,"ngClass","hasNoneOption","list","hideMatLabel","placeholder"],["type","time","matInput","","formControlName","whenToSendTime"],["formControlName","excludeWeekOff"],[1,"checkbox-text"],["formControlName","status",1,"form-field-class-app-input-wo-width",3,"ngClass","hasNoneOption","list","hideMatLabel","placeholder"],["formControlName","template",1,"form-field-class-app-input-wo-width",3,"ngClass","hasNoneOption","list","hideMatLabel","placeholder"],[3,"isChip","placeholder","masterData","selectedValue","data","displayClose","onValueChange"],[3,"type","placeholder","masterData","selectedValues","data","onValueChange"],["appearance","outline",1,"form-field-class-wo-width"],["maxlength","255","type","text","matInput","","formControlName","subject",3,"placeholder","disabled"],["height","300px","formControlName","body",1,"dev-extreme-body-styles"],["bodyEditor",""],[3,"multiline"],["name","size",3,"acceptedValues","options"],["name","font",3,"acceptedValues","options"],["name","bold"],["name","italic"],["name","strike"],["name","underline"],["name","alignLeft"],["name","alignCenter"],["name","alignRight"],["name","alignJustify"],["name","orderedList"],["name","bulletList"],["name","color"],["name","background"],["name","separator"],["name","link"],["name","image"],[1,"d-flex","align-items-center","placeholder-container"],[1,"text"],["cdkOverlayOrigin","",1,"click-to-open",3,"click","mouseenter"],["triggerPlaceholder","cdkOverlayOrigin","triggerPlaceholderField",""],[1,"d-flex","align-items-center","justify-content-between"],[1,"slide-toggle"],["formControlName","isSignatureOn"],["class","dev-extreme-sign-styles","height","150px","formControlName","signature",4,"ngIf"],["height","150px","formControlName","signature",1,"dev-extreme-sign-styles"],[1,"d-flex","flex-column","align-items-center","justify-content-center","attachment-plugin",3,"click"],[1,"attachment-text"],[2,"color","#cf0001"],[3,"list","onConfirmation"]],template:function(e,t){1&e&&(m["\u0275\u0275elementStart"](0,"div",0),m["\u0275\u0275elementStart"](1,"div",1),m["\u0275\u0275elementStart"](2,"div",2),m["\u0275\u0275text"](3,"Stage Transition Rules"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](4,"div",3),m["\u0275\u0275elementStart"](5,"div"),m["\u0275\u0275namespaceSVG"](),m["\u0275\u0275elementStart"](6,"svg",4),m["\u0275\u0275element"](7,"path",5),m["\u0275\u0275element"](8,"path",6),m["\u0275\u0275element"](9,"path",7),m["\u0275\u0275element"](10,"path",8),m["\u0275\u0275element"](11,"path",9),m["\u0275\u0275element"](12,"path",10),m["\u0275\u0275element"](13,"path",11),m["\u0275\u0275element"](14,"path",12),m["\u0275\u0275element"](15,"path",13),m["\u0275\u0275element"](16,"path",14),m["\u0275\u0275element"](17,"path",15),m["\u0275\u0275element"](18,"path",16),m["\u0275\u0275element"](19,"path",17),m["\u0275\u0275element"](20,"path",18),m["\u0275\u0275element"](21,"path",19),m["\u0275\u0275element"](22,"path",20),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275namespaceHTML"](),m["\u0275\u0275elementStart"](23,"div",21),m["\u0275\u0275listener"]("click",(function(){return t.onClose(!1)})),m["\u0275\u0275elementStart"](24,"mat-icon",22),m["\u0275\u0275text"](25,"close"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](26,L,2,0,"div",23),m["\u0275\u0275template"](27,Be,36,84,"div",24),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](28,Ae,2,0,"ng-template",null,25,m["\u0275\u0275templateRefExtractor"]),m["\u0275\u0275template"](30,Ne,1,1,"ng-template",26,27,m["\u0275\u0275templateRefExtractor"])),2&e&&(m["\u0275\u0275advance"](26),m["\u0275\u0275property"]("ngIf",t.isLoading),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",!t.isLoading),m["\u0275\u0275advance"](3),m["\u0275\u0275property"]("cdkConnectedOverlayOrigin",t.triggerPlaceholder))},directives:[C.a,v.NgIf,g.a,x.c,a.J,a.w,a.n,v.NgStyle,F.c,y.b,a.e,a.v,a.l,v.NgTemplateOutlet,M.a,v.NgClass,b.a,w.a,O.a,a.q,_.a,T.Ge,T.o,g.b,P.a,S.a],pipes:[E.a],styles:[".main-container[_ngcontent-%COMP%]{overflow:hidden}.main-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{width:-webkit-fill-available;height:72px;padding:24px;background-color:#f4f4f6;position:absolute;z-index:1;top:0}.main-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:20px;font-weight:700;color:#111434}.main-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{font-size:18px;width:18px;height:18px;color:#1c1b1f;cursor:pointer}.main-container[_ngcontent-%COMP%]   .loading-screen[_ngcontent-%COMP%]{margin-top:20%}.main-container[_ngcontent-%COMP%]   .loading-screen[_ngcontent-%COMP%]     .green-spinner circle{stroke:var(--atsprimaryColor)!important}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]{padding:40px 24px;margin-top:72px}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:11px;font-weight:400;color:#5f6c81}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]{width:90%;font-size:12px;font-family:var(--atsfontFamily)}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-input-element{height:17px!important}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-form-field{width:90%!important;font-size:12px!important;font-family:var(--atsfontFamily)!important}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-form-field-wrapper, .main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-wrapper{padding-bottom:0!important}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]     .mat-slide-toggle-thumb{width:16px!important;height:16px!important;transform:translate(50%,50%)}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]     .mat-slide-toggle-bar{background-color:#dadce2;border-radius:15px!important;height:24px!important;width:40px!important}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]     .mat-slide-toggle-thumb-container{top:-4px!important;left:-5px!important}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]     .mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-bar{background-color:var(--atssecondaryColor)}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]     .mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-thumb{background-color:#fff}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]     .mat-input-element[type=time]{padding-top:8px;height:16px}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .dev-extreme-body-styles[_ngcontent-%COMP%]     .dx-htmleditor-toolbar-wrapper{background-color:#e8e9ee!important;border-radius:4px!important;border:1px solid #d2d2d2!important}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .dev-extreme-body-styles[_ngcontent-%COMP%]     .dx-htmleditor-toolbar-wrapper .dx-toolbar-items-container{height:48px!important}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .dev-extreme-body-styles[_ngcontent-%COMP%]     .dx-htmleditor-toolbar-wrapper .dx-button-content .dx-icon{font-size:16px!important;color:#515965!important}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .dev-extreme-body-styles[_ngcontent-%COMP%]     .dx-htmleditor-toolbar-wrapper .dx-button-content .dx-button-text{color:#515965!important}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .dev-extreme-body-styles[_ngcontent-%COMP%]     .dx-quill-container, .main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .dev-extreme-sign-styles[_ngcontent-%COMP%]     .dx-quill-container{border:1px solid #d2d2d2!important;border-radius:4px!important}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .form-field-class-wo-width[_ngcontent-%COMP%]{font-size:12px;font-family:var(--atsfontFamily)}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .form-field-class-wo-width[_ngcontent-%COMP%]     .mat-input-element{height:17px!important}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .form-field-class-wo-width[_ngcontent-%COMP%]     .mat-form-field{font-size:12px!important;font-family:var(--atsfontFamily)!important}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .form-field-class-wo-width[_ngcontent-%COMP%]     .mat-form-field-wrapper, .main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .form-field-class-wo-width[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-wrapper{padding-bottom:0!important}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .form-field-class-app-input-wo-width[_ngcontent-%COMP%]{font-size:12px;font-family:var(--atsfontFamily)}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .form-field-class-app-input-wo-width[_ngcontent-%COMP%]     .mat-input-element{height:17px!important}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .form-field-class-app-input-wo-width[_ngcontent-%COMP%]     .mat-form-field{width:90%;font-size:12px!important;font-family:var(--atsfontFamily)!important}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .form-field-class-app-input-wo-width[_ngcontent-%COMP%]     .mat-form-field-wrapper, .main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .form-field-class-app-input-wo-width[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-wrapper{padding-bottom:0!important}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]     .mat-checkbox-checked.mat-accent .mat-checkbox-background{background-color:var(--atssecondaryColor)!important}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]     .mat-checkbox-frame{border-color:#b9c0ca!important}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]     .mat-checkbox-inner-container{width:14px!important;height:14px!important}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .app-input-search-field-highlight[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-outline, .main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .app-input-search-field-highlight[_ngcontent-%COMP%]     .mat-form-field-appearance-outline:hover .mat-form-field-outline{color:#d63031!important;border:1px solid #d63031!important;border-radius:5px!important}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .checkbox[_ngcontent-%COMP%]{height:20px;margin-right:8px}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .checkbox[_ngcontent-%COMP%]   .checkbox-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:400;color:#6e7b8f}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .attachment-plugin[_ngcontent-%COMP%]{height:60px;width:-webkit-fill-available;border:2px dashed #b9c0ca;border-radius:6px;font-family:var(--atsfontFamily);font-size:12px;font-weight:400;color:#b9c0ca;cursor:pointer;text-align:center}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .attachment-plugin[_ngcontent-%COMP%]   .attachment-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:13px;font-weight:700;color:#111434}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .placeholder-container[_ngcontent-%COMP%]{width:-webkit-fill-available;background-color:#e8e9ee;border-radius:4px;border:1px solid #d2d2d2;gap:16px;padding:12px 16px}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .placeholder-container[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:400;color:#515965}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .placeholder-container[_ngcontent-%COMP%]   .click-to-open[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:12px;font-weight:500;color:#1b2140;cursor:pointer;border:1px solid #111434;border-radius:4px;padding:4px}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:700;color:#fff;padding:8px 12px;border-radius:8px;background:linear-gradient(270deg,var(--atsprimaryColor),var(--atssecondaryColor) 105.29%);cursor:pointer;width:-moz-fit-content;width:fit-content;float:right}"]}),e})()}}]);