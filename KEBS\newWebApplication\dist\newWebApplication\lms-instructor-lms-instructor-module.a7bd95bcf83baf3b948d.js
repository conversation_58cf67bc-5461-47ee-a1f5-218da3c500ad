(window.webpackJsonp=window.webpackJsonp||[]).push([[791],{jzNV:function(e,t,n){"use strict";n.r(t),n.d(t,"LMSInstructorModule",(function(){return g}));var o=n("ofXK"),r=n("tyNb"),a=n("fXoL"),l=n("jaxi");function u(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"mat-button-toggle",5),a["\u0275\u0275text"](1),a["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;a["\u0275\u0275propertyInterpolate"]("value",e.path),a["\u0275\u0275property"]("routerLink",e.path),a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate1"](" ",e.label," ")}}const i=[{path:"",redirectTo:"myCourses",pathMatch:"full"},{path:"",component:(()=>{class e{constructor(e,t){this.router=e,this._route=t,this.routeData=[{path:"myCourses",label:"My courses"},{path:"myEvaluations",label:"My evaluations"}]}ngOnInit(){}routePages(e){this.router.navigate([e],{relativeTo:this._route})}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275directiveInject"](r.g),a["\u0275\u0275directiveInject"](r.a))},e.\u0275cmp=a["\u0275\u0275defineComponent"]({type:e,selectors:[["lms-instructor-home"]],decls:6,vars:1,consts:[[1,"p-3"],[1,"row","btn-toggle-grp"],[3,"change"],["headerBtnToggleGroup","matButtonToggleGroup"],["class","toggle-btn","routerLinkActive","cur-active-btn",3,"routerLink","value",4,"ngFor","ngForOf"],["routerLinkActive","cur-active-btn",1,"toggle-btn",3,"routerLink","value"]],template:function(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"div",0),a["\u0275\u0275elementStart"](1,"div",1),a["\u0275\u0275elementStart"](2,"mat-button-toggle-group",2,3),a["\u0275\u0275listener"]("change",(function(){a["\u0275\u0275restoreView"](e);const n=a["\u0275\u0275reference"](3);return t.routePages(n.value)})),a["\u0275\u0275template"](4,u,2,3,"mat-button-toggle",4),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275element"](5,"router-outlet"),a["\u0275\u0275elementEnd"]()}2&e&&(a["\u0275\u0275advance"](4),a["\u0275\u0275property"]("ngForOf",t.routeData))},directives:[l.b,o.NgForOf,r.l,l.a,r.i,r.h],styles:[".btn-toggle-grp[_ngcontent-%COMP%]   .toggle-btn[_ngcontent-%COMP%]{width:130px;padding:1px 0}.btn-toggle-grp[_ngcontent-%COMP%]     .mat-button-toggle-appearance-standard .mat-button-toggle-label-content{font-size:12px!important}.cur-active-btn[_ngcontent-%COMP%]{background:#cf0000!important;color:#f5f5f5!important;border:1px solid #cf0000}"]}),e})(),children:[{path:"myCourses",loadChildren:()=>Promise.all([n.e(0),n.e(526)]).then(n.bind(null,"2McJ")).then(e=>e.MyCoursesModule),data:{breadcrumb:"My Courses"}},{path:"myEvaluations",loadChildren:()=>Promise.all([n.e(31),n.e(78),n.e(0),n.e(527)]).then(n.bind(null,"9fym")).then(e=>e.MyEvaluationsModule),data:{breadcrumb:"My Evaluations"}}]},{path:"course/:courseId/:courseName",loadChildren:()=>Promise.all([n.e(19),n.e(31),n.e(78),n.e(147),n.e(0),n.e(412)]).then(n.bind(null,"cO3B")).then(e=>e.CourseContentModule)}];let c=(()=>{class e{}return e.\u0275mod=a["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=a["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[r.k.forChild(i)],r.k]}),e})();var s=n("hQ4u"),d=n("dNgK");let g=(()=>{class e{}return e.\u0275mod=a["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=a["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[o.CommonModule,l.c,c,s.b,d.b]]}),e})()}}]);