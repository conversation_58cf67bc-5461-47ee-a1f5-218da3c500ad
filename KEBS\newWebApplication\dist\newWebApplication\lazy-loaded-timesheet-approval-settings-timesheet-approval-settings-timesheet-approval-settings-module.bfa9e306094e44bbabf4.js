(window.webpackJsonp=window.webpackJsonp||[]).push([[777],{IxOS:function(e,t,n){"use strict";n.r(t),n.d(t,"TimesheetApprovalSettingsModule",(function(){return k}));var o=n("ofXK"),i=n("tyNb"),r=n("mrSG"),a=n("XNiG"),s=n("1G5W"),l=n("fXoL"),m=n("1A3m"),p=n("AK7O"),d=n("/zTS"),c=n("f0Cb"),h=n("kmnG"),g=n("d3UM"),v=n("3Pt+"),u=n("FKr1");function f(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"mat-option",20),l["\u0275\u0275text"](1),l["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;l["\u0275\u0275property"]("value",e.value),l["\u0275\u0275advance"](1),l["\u0275\u0275textInterpolate1"](" ",e.display," ")}}function _(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"mat-option",20),l["\u0275\u0275text"](1),l["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;l["\u0275\u0275property"]("value",e.value),l["\u0275\u0275advance"](1),l["\u0275\u0275textInterpolate1"](" ",e.display," ")}}function y(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"mat-option",20),l["\u0275\u0275text"](1),l["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;l["\u0275\u0275property"]("value",e.value),l["\u0275\u0275advance"](1),l["\u0275\u0275textInterpolate1"](" ",e.display," ")}}function S(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"mat-option",20),l["\u0275\u0275text"](1),l["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;l["\u0275\u0275property"]("value",e.value),l["\u0275\u0275advance"](1),l["\u0275\u0275textInterpolate1"](" ",e.display," ")}}function M(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"mat-option",20),l["\u0275\u0275text"](1),l["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;l["\u0275\u0275property"]("value",e.value),l["\u0275\u0275advance"](1),l["\u0275\u0275textInterpolate1"](" ",e.display," ")}}function x(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"mat-option",20),l["\u0275\u0275text"](1),l["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;l["\u0275\u0275property"]("value",e.value),l["\u0275\u0275advance"](1),l["\u0275\u0275textInterpolate1"](" ",e.display," ")}}const O=[{path:"",component:(()=>{class e{constructor(e,t,n,o){this.router=e,this.toasterService=t,this.tsSettingService=n,this.tsService=o,this.endDateArray=[],this.monthPeriod=[{id:1,value:0,display:"Current Month"},{id:2,value:1,display:"Next Month"}],this.hours=[],this.minutes=[],this._onDestroy=new a.b}ngOnInit(){return Object(r.c)(this,void 0,void 0,(function*(){yield this.checkSettingsAccess();for(let e=1;e<=31;e++)this.endDateArray.push({id:e,value:e,display:e});this.endDateArray.push({id:32,value:"END",display:"Monthly Calendar End Day"});for(let e=0;e<=23;e++)this.hours.push({id:e,value:e,display:e});for(let e=0;e<=59;e++)this.minutes.push({id:e,value:e,display:e});this.getTimesheetSetting()}))}onResize(){this.calculateDynamicContentHeight()}calculateDynamicContentHeight(){this.dynamicHeight=window.innerHeight-112+"px",document.documentElement.style.setProperty("--dynamicHeight",this.dynamicHeight),this.dynamicSubHeight=window.innerHeight-274+"px",document.documentElement.style.setProperty("--dynamicSubHeight",this.dynamicSubHeight)}saveTimesheetSettings(){if(this.monthApprovalFromOf==this.monthApprovalToOf&&this.monthApproavlTo<this.monthApprovalFrom)return this.toasterService.showError("Timesheet Setting Message","Monthly Approval End Date cannot be less than Monthly Approval Start Date",7e3);1==this.monthApprovalFromOf&&this.toasterService.showError("Timesheet Setting Message","Current Month Timesheet Approval Cannot Start From Next Month",7e3);let e={month_timesheet_approval_from:this.monthApprovalFrom,month_timesheet_approval_from_of:this.monthApprovalFromOf,monthly_timesheet_approval_end_date:this.monthApproavlTo,monthly_timesheet_approval_falls_on:this.monthApprovalToOf,monthly_timesheet_approval_end_hours:this.monthApprovalEndHour,monthly_timesheet_approval_end_minutes:this.monthApprovalEndMin};return new Promise((t,n)=>this.tsSettingService.saveTimesheetSettings(e,{data:"No"},2).pipe(Object(s.a)(this._onDestroy)).subscribe({next:e=>{this.toasterService.showInfo("Timesheet App Message",e.messText,7e3),t(!0)},error:e=>{this.toasterService.showError("Timesheet Setting Message","Error: Timesheet Settings Save Failed, Kindly try after some time",7e3),n()}}))}getTimesheetSetting(){return new Promise((e,t)=>this.tsSettingService.getTimesheetSettings(2).pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{var n,o,i,r,a,s,l,m,p,d;"S"==t.messType?(this.monthApprovalFromOf=parseInt(null===(n=t.generalSettings)||void 0===n?void 0:n.month_timesheet_approval_from_of),this.monthApprovalFrom="END"!=(null===(o=t.generalSettings)||void 0===o?void 0:o.month_timesheet_approval_from)?parseInt(null===(i=t.generalSettings)||void 0===i?void 0:i.month_timesheet_approval_from):null===(r=t.generalSettings)||void 0===r?void 0:r.month_timesheet_approval_from,this.monthApproavlTo="END"!=(null===(a=t.generalSettings)||void 0===a?void 0:a.monthly_timesheet_approval_end_date)?parseInt(null===(s=t.generalSettings)||void 0===s?void 0:s.monthly_timesheet_approval_end_date):null===(l=t.generalSettings)||void 0===l?void 0:l.monthly_timesheet_approval_end_date,this.monthApprovalToOf=parseInt(null===(m=t.generalSettings)||void 0===m?void 0:m.month_timesheet_approval_to_of),this.monthApprovalEndHour=parseInt(null===(p=t.generalSettings)||void 0===p?void 0:p.monthly_timesheet_approval_end_hours),this.monthApprovalEndMin=parseInt(null===(d=t.generalSettings)||void 0===d?void 0:d.monthly_timesheet_approval_end_minutes)):this.toasterService.showInfo("Timesheet App Message",t.messText,7e3),e(!0)},error:e=>{this.toasterService.showError("Timesheet Setting Message","Error: Timesheet Settings Save Failed, Kindly try after some time",7e3),t()}}))}naviagteToTimesheetSettings(){this.router.navigateByUrl("/main/timesheetv2/settings")}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}checkSettingsAccess(){return Object(r.c)(this,void 0,void 0,(function*(){this.tsService.checkTimesheetAccess().subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){"S"==e.messType&&(e.settingAccess||this.router.navigateByUrl("/main/timesheetv2/submission"))})))}))}}return e.\u0275fac=function(t){return new(t||e)(l["\u0275\u0275directiveInject"](i.g),l["\u0275\u0275directiveInject"](m.a),l["\u0275\u0275directiveInject"](p.a),l["\u0275\u0275directiveInject"](d.a))},e.\u0275cmp=l["\u0275\u0275defineComponent"]({type:e,selectors:[["app-timesheet-approval-settings"]],hostBindings:function(e,t){1&e&&l["\u0275\u0275listener"]("resize",(function(){return t.onResize()}),!1,l["\u0275\u0275resolveWindow"])},decls:48,vars:12,consts:[[1,"settings-container"],[1,"settings"],[1,"settings-header"],[1,"title","d-flex"],[1,"d-flex","back-button",3,"click"],["_ngcontent-gbh-c448","","width","16","height","16","viewBox","0 0 16 16","fill","none",1,"arrow-svg"],["_ngcontent-gbh-c448","","d","M6.00065 14.6668H10.0007C13.334 14.6668 14.6673 13.3335 14.6673 10.0002V6.00016C14.6673 2.66683 13.334 1.3335 10.0007 1.3335H6.00065C2.66732 1.3335 1.33398 2.66683 1.33398 6.00016V10.0002C1.33398 13.3335 2.66732 14.6668 6.00065 14.6668Z","stroke","#526179","stroke-width","0.8","stroke-linecap","round","stroke-linejoin","round"],["_ngcontent-gbh-c448","","d","M8.84081 10.3532L6.49414 7.99982L8.84081 5.64648","stroke","#526179","stroke-width","0.8","stroke-linecap","round","stroke-linejoin","round"],[1,"buttons"],[1,"save-button",3,"click"],[2,"padding-top","5px"],[1,"mat-start-divider"],[1,"settings-content"],[1,"col-12","pt-2","pb-2","pl-0","pr-0","row"],[1,"col-3","header-text","align-items-center"],[1,"col-6"],["appearance","outline"],[3,"ngModel","ngModelChange"],[3,"value",4,"ngFor","ngForOf"],["appearance","outline",2,"padding-left","15px"],[3,"value"]],template:function(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"div",0),l["\u0275\u0275elementStart"](1,"div",1),l["\u0275\u0275elementStart"](2,"div",2),l["\u0275\u0275elementStart"](3,"div",3),l["\u0275\u0275elementStart"](4,"div",4),l["\u0275\u0275listener"]("click",(function(){return t.naviagteToTimesheetSettings()})),l["\u0275\u0275namespaceSVG"](),l["\u0275\u0275elementStart"](5,"svg",5),l["\u0275\u0275element"](6,"path",6),l["\u0275\u0275element"](7,"path",7),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275text"](8," Timesheet Approval Settings "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275namespaceHTML"](),l["\u0275\u0275elementStart"](9,"div",8),l["\u0275\u0275elementStart"](10,"div",9),l["\u0275\u0275listener"]("click",(function(){return t.saveTimesheetSettings()})),l["\u0275\u0275text"](11,"Save"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](12,"div",10),l["\u0275\u0275element"](13,"mat-divider",11),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](14,"div",12),l["\u0275\u0275elementStart"](15,"div",13),l["\u0275\u0275elementStart"](16,"div",14),l["\u0275\u0275text"](17," Monthly Timesheet Approval Period "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](18,"div",15),l["\u0275\u0275elementStart"](19,"div"),l["\u0275\u0275text"](20," From "),l["\u0275\u0275elementStart"](21,"mat-form-field",16),l["\u0275\u0275elementStart"](22,"mat-select",17),l["\u0275\u0275listener"]("ngModelChange",(function(e){return t.monthApprovalFrom=e})),l["\u0275\u0275template"](23,f,2,2,"mat-option",18),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275text"](24," of "),l["\u0275\u0275elementStart"](25,"mat-form-field",16),l["\u0275\u0275elementStart"](26,"mat-select",17),l["\u0275\u0275listener"]("ngModelChange",(function(e){return t.monthApprovalFromOf=e})),l["\u0275\u0275template"](27,_,2,2,"mat-option",18),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](28,"div"),l["\u0275\u0275text"](29," To \xa0\xa0\xa0 "),l["\u0275\u0275elementStart"](30,"mat-form-field",16),l["\u0275\u0275elementStart"](31,"mat-select",17),l["\u0275\u0275listener"]("ngModelChange",(function(e){return t.monthApproavlTo=e})),l["\u0275\u0275template"](32,y,2,2,"mat-option",18),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275text"](33," of "),l["\u0275\u0275elementStart"](34,"mat-form-field",16),l["\u0275\u0275elementStart"](35,"mat-select",17),l["\u0275\u0275listener"]("ngModelChange",(function(e){return t.monthApprovalToOf=e})),l["\u0275\u0275template"](36,S,2,2,"mat-option",18),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](37,"div",13),l["\u0275\u0275elementStart"](38,"div",14),l["\u0275\u0275text"](39," Monthly Timesheet Approval Cutoff Time "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](40,"div",15),l["\u0275\u0275elementStart"](41,"div"),l["\u0275\u0275elementStart"](42,"mat-form-field",16),l["\u0275\u0275elementStart"](43,"mat-select",17),l["\u0275\u0275listener"]("ngModelChange",(function(e){return t.monthApprovalEndHour=e})),l["\u0275\u0275template"](44,M,2,2,"mat-option",18),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](45,"mat-form-field",19),l["\u0275\u0275elementStart"](46,"mat-select",17),l["\u0275\u0275listener"]("ngModelChange",(function(e){return t.monthApprovalEndMin=e})),l["\u0275\u0275template"](47,x,2,2,"mat-option",18),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&e&&(l["\u0275\u0275advance"](22),l["\u0275\u0275property"]("ngModel",t.monthApprovalFrom),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngForOf",t.endDateArray),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("ngModel",t.monthApprovalFromOf),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngForOf",t.monthPeriod),l["\u0275\u0275advance"](4),l["\u0275\u0275property"]("ngModel",t.monthApproavlTo),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngForOf",t.endDateArray),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("ngModel",t.monthApprovalToOf),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngForOf",t.monthPeriod),l["\u0275\u0275advance"](7),l["\u0275\u0275property"]("ngModel",t.monthApprovalEndHour),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngForOf",t.hours),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngModel",t.monthApprovalEndMin),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngForOf",t.minutes))},directives:[c.a,h.c,g.c,v.v,v.y,o.NgForOf,u.p],styles:[".settings-container[_ngcontent-%COMP%]{background-color:#f1f3f8;overflow:hidden}.settings-container[_ngcontent-%COMP%]   .settings[_ngcontent-%COMP%]{margin-left:16px;margin-right:16px;margin-bottom:16px;padding:4px 16px 14px;height:var(--dynamicHeight);border-radius:4px;background-color:#fff;border:1px solid #e8e9ee;position:relative;gap:5px;display:flex;flex-direction:column}.settings-container[_ngcontent-%COMP%]   .settings[_ngcontent-%COMP%]   .settings-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;padding:10px 10px 10px 0}.settings-container[_ngcontent-%COMP%]   .settings[_ngcontent-%COMP%]   .settings-header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{color:#45546e;font-family:var(--fontFamily);font-size:14px;font-weight:900;line-height:16px;letter-spacing:.02em}.settings-container[_ngcontent-%COMP%]   .settings[_ngcontent-%COMP%]   .settings-header[_ngcontent-%COMP%]   .buttons[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%]{padding:8px 12px;background-color:#79ba44;color:#fff;font-family:var(--fontFamily);font-size:14px;font-weight:600;line-height:16px;border-radius:5px;cursor:pointer}.settings-container[_ngcontent-%COMP%]   .settings[_ngcontent-%COMP%]   .settings-content[_ngcontent-%COMP%]{overflow:auto;height:100%}.settings-container[_ngcontent-%COMP%]   .settings[_ngcontent-%COMP%]   .settings-content[_ngcontent-%COMP%]::-webkit-scrollbar{width:7px!important}.settings-container[_ngcontent-%COMP%]   .settings[_ngcontent-%COMP%]   .settings-content[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:transparent!important}.settings-container[_ngcontent-%COMP%]   .settings[_ngcontent-%COMP%]   .mat-start-divider[_ngcontent-%COMP%]{position:absolute;width:98%;background:#45546e}.settings-container[_ngcontent-%COMP%]   .settings[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]{font-family:var(--fontFamily);font-weight:500;font-size:14px;color:#45546e;padding:15px}.settings-container[_ngcontent-%COMP%]   .settings[_ngcontent-%COMP%]   .header-detail[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-weight:400;font-size:12px;color:#b9c0ca}.settings-container[_ngcontent-%COMP%]   .settings[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]{cursor:pointer;margin-right:15px}"]}),e})()}];let E=(()=>{class e{}return e.\u0275mod=l["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=l["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.k.forChild(O)],i.k]}),e})();var C=n("qFsG"),b=n("bSwM"),A=n("1jcm"),P=n("QibW"),T=n("NFeN"),w=n("Qu3c"),F=n("Xi0T");let k=(()=>{class e{}return e.\u0275mod=l["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=l["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[v.p,o.CommonModule,E,v.E,h.e,C.c,b.b,A.b,P.c,g.d,T.b,c.b,w.b,F.a]]}),e})()}}]);