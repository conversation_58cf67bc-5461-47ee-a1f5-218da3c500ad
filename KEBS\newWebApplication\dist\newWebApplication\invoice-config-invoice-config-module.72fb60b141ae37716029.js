(window.webpackJsonp=window.webpackJsonp||[]).push([[731],{LtrH:function(e,t,n){"use strict";n.r(t),n.d(t,"InvoiceConfigModule",(function(){return oe}));var i=n("ofXK"),o=n("tyNb"),a=n("fXoL"),l=n("Wp6s"),r=n("NFeN");function s(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"div",5),a["\u0275\u0275listener"]("click",(function(){a["\u0275\u0275restoreView"](e);const n=t.$implicit;return a["\u0275\u0275nextContext"]().selectedSettings(n.id)})),a["\u0275\u0275elementStart"](1,"div",6),a["\u0275\u0275elementStart"](2,"mat-icon",7),a["\u0275\u0275text"](3),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](4,"div",8),a["\u0275\u0275text"](5),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=a["\u0275\u0275nextContext"]();a["\u0275\u0275property"]("ngClass",e.id==n.selectedItemId?"sr-nav-item":"r-nav-item slide-from-down")("routerLink",e.link),a["\u0275\u0275advance"](3),a["\u0275\u0275textInterpolate1"](" ",e.icon," "),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate1"](" ",e.label," ")}}let d=(()=>{class e{constructor(){this.authorizedNavList=[{id:1,application_id:10,label:"Legal Entity Field",icon:"description",link:"form-config"},{id:2,application_id:10,label:"Invoice Template",icon:"description",link:"template-config"},{id:3,application_id:10,label:"FTE Details",icon:"description",link:"fte-config"}],this.selectedItemId=1}ngOnInit(){}selectedSettings(e){this.selectedItemId=e}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=a["\u0275\u0275defineComponent"]({type:e,selectors:[["app-landing-page"]],decls:6,vars:1,consts:[[1,"container-fluid","admin-settings-style"],[1,"pt-4","layout-wrapper"],[1,"edit-main"],[1,"edit-nav"],["class","py-3 px-3 slide-from-down row",3,"ngClass","routerLink","click",4,"ngFor","ngForOf"],[1,"py-3","px-3","slide-from-down","row",3,"ngClass","routerLink","click"],[1,"mt-1","pl-0","col-2","icon"],[2,"font-size","25px !important","color","#000000ad !important"],[1,"col-8","px-2","my-auto"]],template:function(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"div",0),a["\u0275\u0275elementStart"](1,"div",1),a["\u0275\u0275elementStart"](2,"div",2),a["\u0275\u0275element"](3,"router-outlet"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](4,"mat-card",3),a["\u0275\u0275template"](5,s,6,4,"div",4),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e&&(a["\u0275\u0275advance"](5),a["\u0275\u0275property"]("ngForOf",t.authorizedNavList))},directives:[o.l,l.a,i.NgForOf,i.NgClass,o.h,r.a],styles:[".admin-settings-style[_ngcontent-%COMP%]{overflow-y:hidden!important}.admin-settings-style[_ngcontent-%COMP%]   .layout-wrapper[_ngcontent-%COMP%]{display:flex;height:90vh}.admin-settings-style[_ngcontent-%COMP%]   .edit-nav[_ngcontent-%COMP%]{width:20vw;overflow-y:scroll;background-color:#fff;color:#000}.admin-settings-style[_ngcontent-%COMP%]   .edit-main[_ngcontent-%COMP%]{width:75vw;padding:5px;overflow-y:scroll!important}.admin-settings-style[_ngcontent-%COMP%]   .sr-nav-item[_ngcontent-%COMP%]{background-color:#f5f5f5!important;font-weight:600;border-left:solid #545352;border-radius:2px;box-shadow:0 3px 1px -2px rgba(0,0,0,.2),0 2px 2px 0 rgba(0,0,0,.14),0 1px 5px 0 rgba(0,0,0,.12)}.admin-settings-style[_ngcontent-%COMP%]   .r-nav-item[_ngcontent-%COMP%]{font-weight:400;cursor:pointer}.admin-settings-style[_ngcontent-%COMP%]   .r-nav-item[_ngcontent-%COMP%]:hover{background-color:#f5f5f5!important;font-weight:500;border-left:solid #545352;border-radius:2px;transition:box-shadow .3s;box-shadow:0 4px 8px 0 rgba(0,0,0,.2),0 6px 20px 0 rgba(0,0,0,.19)}.admin-settings-style[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}.admin-settings-style[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}"]}),e})();var c=n("mrSG"),m=n("3Pt+"),p=n("0IaG"),g=n("kmnG"),f=n("qFsG"),u=n("ihCf"),h=n("bTqV");let v=(()=>{class e{constructor(e,t){this.dialogRef=e,this.data=t,this.valueEmitter=new a.EventEmitter,this.text=this.data.text}closeDialog(){this.dialogRef.close(this.text)}closeDialogWithValue(){console.log("text dialog"),console.log(this.text),this.dialogRef.close(this.text),this.valueEmitter.emit(this.text)}ngOnInit(){}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275directiveInject"](p.h),a["\u0275\u0275directiveInject"](p.a))},e.\u0275cmp=a["\u0275\u0275defineComponent"]({type:e,selectors:[["app-terms-and-conditions"]],outputs:{valueEmitter:"valueEmitter"},decls:11,vars:1,consts:[[1,"row","mt-2","justify-content-end"],[2,"color","#cf0001","font-size","18px","float","right","cursor","pointer",3,"click"],[1,"row","container"],["appearance","outline",1,"textArea"],["matInput","","cdkTextareaAutosize","","cdkAutosizeMinRows","10","cdkAutosizeMaxRows","10",3,"ngModel","ngModelChange"],["autosize","cdkTextareaAutosize"],[1,"justify-content-end","mr-2",2,"display","flex"],["mat-mini-fab","","aria-label","Example icon-button with a heart icon",1,"ml-auto","mr-1","mini-tick",2,"background-color","#cf0001"],[2,"color","white",3,"click"]],template:function(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"div",0),a["\u0275\u0275elementStart"](1,"mat-icon",1),a["\u0275\u0275listener"]("click",(function(){return t.closeDialog()})),a["\u0275\u0275text"](2,"close"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](3,"div",2),a["\u0275\u0275elementStart"](4,"mat-form-field",3),a["\u0275\u0275elementStart"](5,"textarea",4,5),a["\u0275\u0275listener"]("ngModelChange",(function(e){return t.text=e})),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](7,"div",6),a["\u0275\u0275elementStart"](8,"button",7),a["\u0275\u0275elementStart"](9,"mat-icon",8),a["\u0275\u0275listener"]("click",(function(){return t.closeDialogWithValue()})),a["\u0275\u0275text"](10,"done_all"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e&&(a["\u0275\u0275advance"](5),a["\u0275\u0275property"]("ngModel",t.text))},directives:[r.a,g.c,f.b,u.b,m.e,m.v,m.y,h.a],styles:[".textArea[_ngcontent-%COMP%]{width:500px}.container[_ngcontent-%COMP%]{align-items:center;justify-content:center;overflow:hidden}.mini-tick[_ngcontent-%COMP%]{height:35px!important;width:34px!important;background-color:#cf0001!important;color:#fff!important}"]}),e})();var C=n("lA+9"),x=n("1A3m"),y=n("dNgK"),S=n("JqCM"),_=n("Xa2L"),b=n("Qu3c"),w=n("d3UM"),E=n("FKr1"),P=n("1jcm");function O(e,t){1&e&&(a["\u0275\u0275elementContainerStart"](0),a["\u0275\u0275elementStart"](1,"div",1),a["\u0275\u0275element"](2,"mat-spinner",2),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementContainerEnd"]())}function M(e,t){1&e&&(a["\u0275\u0275elementContainerStart"](0),a["\u0275\u0275elementStart"](1,"ngx-spinner",3),a["\u0275\u0275elementStart"](2,"p",4),a["\u0275\u0275text"](3,"Importing Master Configuration..."),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementContainerEnd"]()),2&e&&(a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("fullScreen",!0))}function F(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"mat-option",13),a["\u0275\u0275elementStart"](1,"span",14),a["\u0275\u0275text"](2),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;a["\u0275\u0275property"]("value",e.entity_id),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("matTooltip",e.entity_name),a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate"](e.entity_name)}}function I(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"mat-option",41),a["\u0275\u0275text"](1),a["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=a["\u0275\u0275nextContext"](2).$implicit,i=a["\u0275\u0275nextContext"](4);let o=null,l=null;a["\u0275\u0275property"]("value",e.pdf_section)("selected",e.pdf_section===(null==(o=i.adminfieldform.get(n.key_name))||null==(l=o.get("pdf_display_section"))?null:l.value)),a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate1"](" ",e.pdf_section," ")}}function k(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"div",38),a["\u0275\u0275elementStart"](1,"mat-form-field",28),a["\u0275\u0275elementStart"](2,"mat-select",39),a["\u0275\u0275listener"]("selectionChange",(function(t){a["\u0275\u0275restoreView"](e);const n=a["\u0275\u0275nextContext"](),i=n.$implicit,o=n.index;return a["\u0275\u0275nextContext"](4).onchangedropdownvalue(t,i.key_name,o)})),a["\u0275\u0275template"](3,I,2,3,"mat-option",40),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()}if(2&e){const e=a["\u0275\u0275nextContext"]().$implicit,t=a["\u0275\u0275nextContext"](4);a["\u0275\u0275propertyInterpolate"]("formGroupName",e.key_name),a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("ngForOf",t.pdfDisplaySectionOptions)}}function L(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"mat-option",13),a["\u0275\u0275text"](1),a["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;a["\u0275\u0275property"]("value",e.pdf_section),a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate1"](" ",e.pdf_section," ")}}function j(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"div"),a["\u0275\u0275elementStart"](1,"mat-form-field",28),a["\u0275\u0275elementStart"](2,"mat-label"),a["\u0275\u0275text"](3,"PDF Display Section"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](4,"mat-select",42),a["\u0275\u0275listener"]("selectionChange",(function(t){a["\u0275\u0275restoreView"](e);const n=a["\u0275\u0275nextContext"]().index;return a["\u0275\u0275nextContext"](4).onchangedropdownvalue(t,"",n)})),a["\u0275\u0275template"](5,L,2,2,"mat-option",9),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()}if(2&e){const e=a["\u0275\u0275nextContext"](5);a["\u0275\u0275advance"](5),a["\u0275\u0275property"]("ngForOf",e.pdfDisplaySectionOptions)}}function D(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"div"),a["\u0275\u0275elementStart"](1,"div",25),a["\u0275\u0275elementStart"](2,"div",26),a["\u0275\u0275text"](3),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](4,"div",27),a["\u0275\u0275elementStart"](5,"mat-form-field",28),a["\u0275\u0275elementStart"](6,"input",29),a["\u0275\u0275listener"]("change",(function(n){a["\u0275\u0275restoreView"](e);const i=t.$implicit,o=t.index;return a["\u0275\u0275nextContext"](4).onchangelabel(n,i,o,i.key_name)})),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](7,"div",30),a["\u0275\u0275elementStart"](8,"mat-slide-toggle",31),a["\u0275\u0275listener"]("change",(function(n){a["\u0275\u0275restoreView"](e);const i=t.$implicit,o=t.index;return a["\u0275\u0275nextContext"](4).toggleisactiveChangesarr(n,i.active_status,o)})),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](9,"div",32),a["\u0275\u0275elementStart"](10,"mat-slide-toggle",31),a["\u0275\u0275listener"]("change",(function(n){a["\u0275\u0275restoreView"](e);const i=t.$implicit,o=t.index;return a["\u0275\u0275nextContext"](4).toggleismandatoryChangesarr(n,i.is_mandatory,o)})),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](11,"div",33),a["\u0275\u0275elementStart"](12,"mat-slide-toggle",31),a["\u0275\u0275listener"]("change",(function(n){a["\u0275\u0275restoreView"](e);const i=t.$implicit,o=t.index;return a["\u0275\u0275nextContext"](4).toggleisShowPDF(n,i.is_to_show_in_pdf,o)})),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](13,"div",20),a["\u0275\u0275template"](14,k,4,2,"div",34),a["\u0275\u0275template"](15,j,6,1,"div",0),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](16,"div",35),a["\u0275\u0275elementStart"](17,"button",36),a["\u0275\u0275listener"]("click",(function(){a["\u0275\u0275restoreView"](e);const n=t.index;return a["\u0275\u0275nextContext"](4).openDialog(n)})),a["\u0275\u0275elementStart"](18,"mat-icon"),a["\u0275\u0275text"](19,"edit_note"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](20,"div",37),a["\u0275\u0275text"](21),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=t.index;a["\u0275\u0275advance"](3),a["\u0275\u0275textInterpolate1"](" ",n+1," "),a["\u0275\u0275advance"](3),a["\u0275\u0275propertyInterpolate"]("value",e.label_name),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("checked",1==e.active_status),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("checked",1==e.is_mandatory),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("checked",1==e.is_to_show_in_pdf),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("ngIf",0==e.is_new_field),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",1==e.is_new_field),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("disabled","termsAndConditions"!=e.key_name),a["\u0275\u0275advance"](4),a["\u0275\u0275textInterpolate1"](" ",e.key_name," ")}}function T(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",15),a["\u0275\u0275elementStart"](1,"mat-card",16),a["\u0275\u0275elementStart"](2,"div"),a["\u0275\u0275elementStart"](3,"div",17),a["\u0275\u0275elementStart"](4,"div",18),a["\u0275\u0275elementStart"](5,"div",19),a["\u0275\u0275text"](6,"SL No"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](7,"div",20),a["\u0275\u0275text"](8,"Field Label"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](9,"div",19),a["\u0275\u0275text"](10,"Active Status"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](11,"div",21),a["\u0275\u0275text"](12,"Mandatory"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](13,"div",22),a["\u0275\u0275text"](14,"Show In All PDFS"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](15,"div",20),a["\u0275\u0275text"](16,"PDF Section"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](17,"div",22),a["\u0275\u0275text"](18,"Default value"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](19,"div",20),a["\u0275\u0275text"](20,"External Reference key"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](21,"div"),a["\u0275\u0275elementStart"](22,"form",23),a["\u0275\u0275template"](23,D,22,9,"div",24),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"](3);a["\u0275\u0275advance"](22),a["\u0275\u0275property"]("formGroup",e.adminfieldform),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngForOf",e.formFieldList)}}function V(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"div"),a["\u0275\u0275elementStart"](1,"div"),a["\u0275\u0275elementStart"](2,"h1",43),a["\u0275\u0275text"](3," No Configuration found ! "),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](4,"div",44),a["\u0275\u0275element"](5,"img",45),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](6,"div",46),a["\u0275\u0275elementStart"](7,"button",47),a["\u0275\u0275listener"]("click",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"](3).importMasterConfiguration()})),a["\u0275\u0275elementStart"](8,"mat-icon"),a["\u0275\u0275text"](9,"system_update_alt"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275text"](10," Import Master Configuration "),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()}}const N=function(e,t){return{buttonOff:e,buttonOn:t}};function $(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementContainerStart"](0),a["\u0275\u0275elementStart"](1,"div",5),a["\u0275\u0275elementStart"](2,"div",6),a["\u0275\u0275elementStart"](3,"mat-form-field",7),a["\u0275\u0275elementStart"](4,"mat-label"),a["\u0275\u0275text"](5,"Legal Entity"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](6,"mat-select",8),a["\u0275\u0275listener"]("ngModelChange",(function(t){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"](2).selectedEntityId=t}))("selectionChange",(function(t){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"](2).onLechangedropdownvalue(t)})),a["\u0275\u0275template"](7,F,3,3,"mat-option",9),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](8,"button",10),a["\u0275\u0275listener"]("click",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"](2).onSubmit()})),a["\u0275\u0275text"](9," Save \xa0 "),a["\u0275\u0275elementStart"](10,"mat-icon",11),a["\u0275\u0275text"](11,"keyboard_arrow_right"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](12,T,24,2,"div",12),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](13,V,11,0,"div",0),a["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=a["\u0275\u0275nextContext"](2);a["\u0275\u0275advance"](6),a["\u0275\u0275property"]("ngModel",e.selectedEntityId),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngForOf",e.entityDetails),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("disabled",0==e.apiParams.length)("ngClass",a["\u0275\u0275pureFunction2"](6,N,0==e.apiParams.length,0!=e.apiParams.length)),a["\u0275\u0275advance"](4),a["\u0275\u0275property"]("ngIf",0!=e.formFieldList.length),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",0==e.formFieldList.length)}}function z(e,t){if(1&e&&(a["\u0275\u0275elementContainerStart"](0),a["\u0275\u0275template"](1,M,4,1,"ng-container",0),a["\u0275\u0275template"](2,$,14,9,"ng-container",0),a["\u0275\u0275elementContainerEnd"]()),2&e){const e=a["\u0275\u0275nextContext"]();a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",e.showSpinner),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",!e.showSpinner)}}function B(e,t){1&e&&(a["\u0275\u0275elementContainerStart"](0),a["\u0275\u0275elementStart"](1,"div",1),a["\u0275\u0275element"](2,"mat-spinner",2),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementContainerEnd"]())}function A(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"mat-option",11),a["\u0275\u0275elementStart"](1,"span",12),a["\u0275\u0275text"](2),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;a["\u0275\u0275property"]("value",e.service_type_id),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("matTooltip",e.service_type_name),a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate"](e.service_type_name)}}function G(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"mat-icon",31),a["\u0275\u0275text"](1,"lock"),a["\u0275\u0275elementEnd"]())}const R=function(e){return{"bright-disabled":e}};function q(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"div"),a["\u0275\u0275elementStart"](1,"div",22),a["\u0275\u0275elementStart"](2,"div",23),a["\u0275\u0275template"](3,G,2,0,"mat-icon",24),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](4,"div",23),a["\u0275\u0275text"](5),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](6,"div",25),a["\u0275\u0275elementStart"](7,"mat-form-field",26),a["\u0275\u0275elementStart"](8,"input",27),a["\u0275\u0275listener"]("change",(function(n){a["\u0275\u0275restoreView"](e);const i=t.$implicit,o=t.index;return a["\u0275\u0275nextContext"](3).onchangelabel(n,i,o,i.key_name)})),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](9,"div",28),a["\u0275\u0275elementStart"](10,"mat-slide-toggle",29),a["\u0275\u0275listener"]("change",(function(n){a["\u0275\u0275restoreView"](e);const i=t.$implicit,o=t.index;return a["\u0275\u0275nextContext"](3).toggleisactiveChanges(n,i.active_status,o)})),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](11,"div",30),a["\u0275\u0275text"](12),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=t.index;a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("ngIf",0===e.is_editable),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate1"](" ",n+1," "),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("ngClass",a["\u0275\u0275pureFunction1"](10,R,0===e.is_editable)),a["\u0275\u0275advance"](1),a["\u0275\u0275propertyInterpolate"]("value",e.label_name),a["\u0275\u0275property"]("disabled",1!=e.is_editable)("ngClass",a["\u0275\u0275pureFunction1"](12,R,0===e.is_editable)),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("checked",1==e.active_status)("disabled",1!=e.is_editable)("ngClass",a["\u0275\u0275pureFunction1"](14,R,0===e.is_editable)),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate1"](" ",e.key_name," ")}}function J(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",13),a["\u0275\u0275elementStart"](1,"mat-card",14),a["\u0275\u0275elementStart"](2,"div"),a["\u0275\u0275elementStart"](3,"div",15),a["\u0275\u0275elementStart"](4,"div",16),a["\u0275\u0275element"](5,"div",17),a["\u0275\u0275elementStart"](6,"div",17),a["\u0275\u0275text"](7,"SL No"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](8,"div",18),a["\u0275\u0275text"](9,"Field Label"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](10,"div",17),a["\u0275\u0275text"](11,"Active Status"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](12,"div",19),a["\u0275\u0275text"](13,"External Reference key"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](14,"div"),a["\u0275\u0275elementStart"](15,"form",20),a["\u0275\u0275template"](16,q,13,16,"div",21),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"](2);a["\u0275\u0275advance"](15),a["\u0275\u0275property"]("formGroup",e.admintemplateform),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngForOf",e.templateConfig)}}function U(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"div"),a["\u0275\u0275elementStart"](1,"div"),a["\u0275\u0275elementStart"](2,"h1",32),a["\u0275\u0275text"](3," No Details found ! "),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](4,"div",33),a["\u0275\u0275element"](5,"img",34),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]())}const K=function(e,t){return{buttonOff:e,buttonOn:t}};function X(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementContainerStart"](0),a["\u0275\u0275elementStart"](1,"div",3),a["\u0275\u0275elementStart"](2,"div",4),a["\u0275\u0275elementStart"](3,"mat-form-field",5),a["\u0275\u0275elementStart"](4,"mat-label"),a["\u0275\u0275text"](5,"Service type"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](6,"mat-select",6),a["\u0275\u0275listener"]("ngModelChange",(function(t){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"]().selectedServiceTypeId=t}))("selectionChange",(function(t){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"]().onStchangedropdownvalue(t)})),a["\u0275\u0275template"](7,A,3,3,"mat-option",7),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](8,"button",8),a["\u0275\u0275listener"]("click",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"]().onSubmit()})),a["\u0275\u0275text"](9," Save \xa0 "),a["\u0275\u0275elementStart"](10,"mat-icon",9),a["\u0275\u0275text"](11,"keyboard_arrow_right"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](12,J,17,2,"div",10),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](13,U,6,0,"div",0),a["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=a["\u0275\u0275nextContext"]();a["\u0275\u0275advance"](6),a["\u0275\u0275property"]("ngModel",e.selectedServiceTypeId),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngForOf",e.serviceDetails),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("disabled",0==e.apiParams.length)("ngClass",a["\u0275\u0275pureFunction2"](6,K,0==e.apiParams.length,0!=e.apiParams.length)),a["\u0275\u0275advance"](4),a["\u0275\u0275property"]("ngIf",0!=e.templateConfig.length),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",0==e.templateConfig.length)}}function Y(e,t){1&e&&(a["\u0275\u0275elementContainerStart"](0),a["\u0275\u0275elementStart"](1,"div",1),a["\u0275\u0275element"](2,"mat-spinner",2),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementContainerEnd"]())}function W(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"div"),a["\u0275\u0275elementStart"](1,"div",18),a["\u0275\u0275elementStart"](2,"div",19),a["\u0275\u0275text"](3),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](4,"div",20),a["\u0275\u0275elementStart"](5,"mat-form-field",21),a["\u0275\u0275elementStart"](6,"input",22),a["\u0275\u0275listener"]("change",(function(n){a["\u0275\u0275restoreView"](e);const i=t.$implicit,o=t.index;return a["\u0275\u0275nextContext"](3).onchangelabel(n,i,o,i.key_name)})),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](7,"div",23),a["\u0275\u0275elementStart"](8,"mat-slide-toggle",24),a["\u0275\u0275listener"]("change",(function(n){a["\u0275\u0275restoreView"](e);const i=t.$implicit,o=t.index;return a["\u0275\u0275nextContext"](3).toggleisactiveChanges(n,i.active_status,o)})),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](9,"div",25),a["\u0275\u0275elementStart"](10,"mat-slide-toggle",24),a["\u0275\u0275listener"]("change",(function(n){a["\u0275\u0275restoreView"](e);const i=t.$implicit,o=t.index;return a["\u0275\u0275nextContext"](3).toggleismandatoryChanges(n,i.is_mandatory,o)})),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](11,"div",26),a["\u0275\u0275text"](12),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=t.index;a["\u0275\u0275advance"](3),a["\u0275\u0275textInterpolate1"](" ",n+1," "),a["\u0275\u0275advance"](3),a["\u0275\u0275propertyInterpolate"]("value",e.label_name),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("checked",1==e.active_status),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("checked",1==e.is_mandatory),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate1"](" ",e.key_name," ")}}function H(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",8),a["\u0275\u0275elementStart"](1,"mat-card",9),a["\u0275\u0275elementStart"](2,"div"),a["\u0275\u0275elementStart"](3,"div",10),a["\u0275\u0275elementStart"](4,"div",11),a["\u0275\u0275element"](5,"div",12),a["\u0275\u0275elementStart"](6,"div",12),a["\u0275\u0275text"](7,"SL No"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](8,"div",13),a["\u0275\u0275text"](9,"Field Label"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](10,"div",12),a["\u0275\u0275text"](11,"Active Status"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](12,"div",14),a["\u0275\u0275text"](13,"Mandatory"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](14,"div",15),a["\u0275\u0275text"](15,"External Reference key"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](16,"div"),a["\u0275\u0275elementStart"](17,"form",16),a["\u0275\u0275template"](18,W,13,5,"div",17),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"](2);a["\u0275\u0275advance"](17),a["\u0275\u0275property"]("formGroup",e.adminFteform),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngForOf",e.fteConfig)}}function Q(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"div"),a["\u0275\u0275elementStart"](1,"div"),a["\u0275\u0275elementStart"](2,"h1",27),a["\u0275\u0275text"](3," No Details found ! "),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](4,"div",28),a["\u0275\u0275element"](5,"img",29),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]())}const Z=function(e,t){return{buttonOff:e,buttonOn:t}};function ee(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementContainerStart"](0),a["\u0275\u0275elementStart"](1,"div",3),a["\u0275\u0275elementStart"](2,"div",4),a["\u0275\u0275elementStart"](3,"button",5),a["\u0275\u0275listener"]("click",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"]().onSubmit()})),a["\u0275\u0275text"](4," Save \xa0 "),a["\u0275\u0275elementStart"](5,"mat-icon",6),a["\u0275\u0275text"](6,"keyboard_arrow_right"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](7,H,19,2,"div",7),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](8,Q,6,0,"div",0),a["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=a["\u0275\u0275nextContext"]();a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("disabled",0==e.apiParams.length)("ngClass",a["\u0275\u0275pureFunction2"](4,Z,0==e.apiParams.length,0!=e.apiParams.length)),a["\u0275\u0275advance"](4),a["\u0275\u0275property"]("ngIf",0!=e.fteConfig.length),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",0==e.fteConfig.length)}}const te=[{path:"",component:d,children:[{path:"",redirectTo:"form-config",pathMatch:"full"},{path:"form-config",component:(()=>{class e{constructor(e,t,n,i,o,a){this.invConfigService=e,this.formBuilder=t,this._toaster=n,this._snackBar=i,this.spinnerService=o,this.dialog=a,this.data=["header,item"],this.fieldList={},this.apiParams=[],this.showTable=!1,this.isComponentLoading=!1,this.showSpinner=!1,this.adminfieldform=this.formBuilder.group({}),this.isDialogOpen=!1}ngOnInit(){return Object(c.c)(this,void 0,void 0,(function*(){this.entityDetails=yield this.getLegalEntity(),this.pdfDisplaySectionOptions=yield this.getPDFSections(),this.formFieldList=yield this.FormFieldConfig(null),this.getUniquePdfDisplaySections(),this.isComponentLoading=!0,console.log(this.pdfDisplaySectionOptions),console.log("form initial"),console.log(this.formFieldList)}))}getUniquePdfDisplaySections(){for(let e of this.formFieldList){let t=e.key_name,n=new m.m({pdf_display_section:new m.j(e.pdf_display_section),default_value:new m.j("")});this.adminfieldform.addControl(t,n)}console.log("formg"),console.log(this.adminfieldform)}FormFieldConfig(e){return new Promise((t,n)=>{this.invConfigService.FormFieldConfig(e).subscribe(e=>{this.selectedEntityId=e.entity_id,console.log("----------------------------------"),console.log(e),console.log(this.selectedEntityId),console.log(e.data),t(e.data)},e=>{console.log(e),n(e)})})}getPDFSections(){return new Promise((e,t)=>{this.invConfigService.getPDFSections().subscribe(t=>{e(t.data)},e=>{console.log(e),t(e)})})}getLegalEntity(){return new Promise((e,t)=>{this.invConfigService.getLegalEntity().subscribe(t=>{e(t.data)},e=>{console.log(e),t(e)})})}toggleismandatoryChangesarr(e,t,n){this.formFieldList[n].is_mandatory=!t,this.removeDocument(this.formFieldList[n]),this.apiParams.push(this.formFieldList[n])}toggleisactiveChangesarr(e,t,n){this.formFieldList[n].active_status=!t,this.removeDocument(this.formFieldList[n]),this.apiParams.push(this.formFieldList[n])}toggleisShowPDF(e,t,n){this.formFieldList[n].is_to_show_in_pdf=!t,this.removeDocument(this.formFieldList[n]),this.apiParams.push(this.formFieldList[n])}onchangedropdownvalue(e,t,n){var i,o;this.formFieldList[n].pdf_display_section=t&&""!=t?null===(o=null===(i=this.adminfieldform.get(t))||void 0===i?void 0:i.get("pdf_display_section"))||void 0===o?void 0:o.value:e.value,this.removeDocument(this.formFieldList[n]),this.apiParams.push(this.formFieldList[n])}onLechangedropdownvalue(e){return Object(c.c)(this,void 0,void 0,(function*(){console.log("Entered into function"),this.legalEntityId=e.value,this.formFieldList=yield this.FormFieldConfig(e.value),console.log("lensp"),console.log(this.formFieldList),console.log(this.formFieldList.length),this.getUniquePdfDisplaySections()}))}onchangelabel(e,t,n,i){this.formFieldList[n].label_name=e.target.value,this.removeDocument(this.formFieldList[n]),this.apiParams.push(this.formFieldList[n]),""==i&&this.generateKey(e.target.value,n)}removeDocument(e){this.apiParams.forEach((t,n)=>{t===e&&this.apiParams.splice(n,1)})}onSubmit(){return console.log("34dar"),console.log(this.apiParams),new Promise((e,t)=>{this.invConfigService.updateadminFieldConfig(this.apiParams).subscribe(t=>{t.err||(this._toaster.showSuccess("Success","Form configuration updated Successfully !",2e3),this.apiParams=[],e(t))},e=>{console.log("error",e),t(e)})})}addRow(){this.formFieldList.push({key_name:"",label_name:"",is_mandatory:!0,active_status:!0,is_to_show_in_pdf:!0,pdf_display_section:"",legal_entity_id:this.legalEntityId,is_new_field:1})}generateKey(e,t){let n="cf_"+e.trim().split(/\s+/).map(e=>e.toLowerCase()).join("_");this.formFieldList[t].key_name=n;let i=new m.m({pdf_display_section:new m.j});this.adminfieldform.addControl(n,i)}importMasterConfiguration(){return Object(c.c)(this,void 0,void 0,(function*(){this.showSpinner=!0,this.spinnerService.show();let e=yield this.importFieldConfig();return"S"==e.messType?(this._snackBar.open(e.messText,"dismiss",{duration:2e3}),this.formFieldList=yield this.FormFieldConfig(this.legalEntityId),this.getUniquePdfDisplaySections(),this.spinnerService.hide(),void(this.showSpinner=!1)):(this._snackBar.open(e.messText,"dismiss",{duration:2e3}),this.formFieldList=yield this.FormFieldConfig(null),this.getUniquePdfDisplaySections(),this.spinnerService.hide(),void(this.showSpinner=!1))}))}importFieldConfig(){return new Promise((e,t)=>{this.invConfigService.importFieldConfig(this.legalEntityId).subscribe(t=>{e(t)},e=>{console.log(e),t(e),this.spinnerService.hide(),this.showSpinner=!1})})}openDialog(e){if(this.isDialogOpen)return;console.log(this.formFieldList),console.log(this.formFieldList[e].default_value),this.isDialogOpen=!0;const t=this.dialog.open(v,{width:"40%",height:"37%",data:{text:this.formFieldList[e].default_value},disableClose:!0});t.componentInstance.valueEmitter.subscribe(t=>{var n;console.log("Received value from dialog:",t),t&&(null===(n=this.formFieldList[e])||void 0===n?void 0:n.default_value)!==t&&(console.log("inside if"),this.formFieldList[e].default_value=t,this.apiParams.push(this.formFieldList[e]))}),t.afterClosed().subscribe(e=>{this.isDialogOpen=!1})}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275directiveInject"](C.a),a["\u0275\u0275directiveInject"](m.i),a["\u0275\u0275directiveInject"](x.a),a["\u0275\u0275directiveInject"](y.a),a["\u0275\u0275directiveInject"](S.c),a["\u0275\u0275directiveInject"](p.b))},e.\u0275cmp=a["\u0275\u0275defineComponent"]({type:e,selectors:[["app-form-field-config"]],decls:2,vars:2,consts:[[4,"ngIf"],[1,"d-flex","justify-content-center","mt-3"],["matTooltip","Please wait...","diameter","30"],["bdColor","rgb(245,245,245,0.6)","size","medium","color","#cf0001","type","ball-clip-rotate",3,"fullScreen"],[2,"color","#cf0001"],[1,"invConfig","scroll-area"],[1,"btns"],["appearance","outline",2,"width","30%","margin-left","30px","margin-top","20px"],["matNativeControl","",3,"ngModel","ngModelChange","selectionChange"],[3,"value",4,"ngFor","ngForOf"],["type","submit",1,"submitButton",3,"disabled","ngClass","click"],[1,"arrow_icon"],["style","display: flex;justify-content: center;",4,"ngIf"],[3,"value"],[3,"matTooltip"],[2,"display","flex","justify-content","center"],[2,"width","fit-content","justify-content","center"],[1,"header","pl-3"],[2,"display","inline-flex"],[2,"width","70px"],[2,"width","150px"],[2,"width","90px"],[2,"width","100px"],[3,"formGroup"],[4,"ngFor","ngForOf"],[1,"rowstyle"],[1,"fieldwidth","keyval",2,"width","70px"],[1,"fieldstyle",2,"width","150px"],["appearance","outline",2,"width","80%"],["matInput","","placeholder","Enter Field label",3,"value","change"],[1,"fieldstyle",2,"width","70px"],[3,"checked","change"],[1,"fieldstyle",2,"width","90px"],[1,"fieldstyle",2,"width","100px"],[3,"formGroupName",4,"ngIf"],[1,"keyval",2,"width","100px"],["mat-icon-button","",3,"disabled","click"],[1,"keyval",2,"width","150px"],[3,"formGroupName"],["matNativeControl","","formControlName","pdf_display_section",3,"selectionChange"],[3,"value","selected",4,"ngFor","ngForOf"],[3,"value","selected"],["matNativeControl","",3,"selectionChange"],[1,"d-flex","justify-content-center","align-items-center","mt-5","slide-in-top"],[1,"d-flex","justify-content-center","align-items-center","slide-from-down"],["src","assets/images/nomilestone.png","height","300","width","350",1,"mt-4"],[1,"d-flex","justify-content-center","align-items-center"],["mat-raised-button","","color","primary",3,"click"]],template:function(e,t){1&e&&(a["\u0275\u0275template"](0,O,3,0,"ng-container",0),a["\u0275\u0275template"](1,z,3,2,"ng-container",0)),2&e&&(a["\u0275\u0275property"]("ngIf",!t.isComponentLoading),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",t.isComponentLoading))},directives:[i.NgIf,_.c,b.a,S.a,g.c,g.g,w.c,m.v,m.y,i.NgForOf,i.NgClass,r.a,E.p,l.a,m.J,m.w,m.n,f.b,P.a,h.a,m.o,m.l],styles:[".invConfig[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{font-size:15px;width:-moz-fit-content;width:fit-content;padding-top:20px;margin-left:30px}.invConfig[_ngcontent-%COMP%]   .rowstyle[_ngcontent-%COMP%]{display:inline-flex;border-color:#c6c0c0;padding-top:5px;padding-left:15px;margin-left:30px}.invConfig[_ngcontent-%COMP%]   .fieldstyle[_ngcontent-%COMP%], .invConfig[_ngcontent-%COMP%]   .keyval[_ngcontent-%COMP%]{display:flex;align-items:center}.invConfig[_ngcontent-%COMP%]   .submitButton[_ngcontent-%COMP%]{margin-bottom:10px;border:none;margin-right:10px;border-radius:4px;padding:10px;color:#fff;line-height:16px}.invConfig[_ngcontent-%COMP%]   .buttonOn[_ngcontent-%COMP%]{background-color:#ee4961}.invConfig[_ngcontent-%COMP%]   .buttonOff[_ngcontent-%COMP%], .invConfig[_ngcontent-%COMP%]   .buttonOn[_ngcontent-%COMP%]{display:inline-flex;justify-content:center;align-items:center;height:40px;cursor:pointer;margin-left:63px;margin-top:16px}.invConfig[_ngcontent-%COMP%]   .buttonOff[_ngcontent-%COMP%]{background-color:#d18c96}.invConfig[_ngcontent-%COMP%]   .btns[_ngcontent-%COMP%]{position:absolute;display:contents;right:22px}.invConfig[_ngcontent-%COMP%]   .scroll-area[_ngcontent-%COMP%]{height:60vh;overflow-y:scroll}.invConfig[_ngcontent-%COMP%]   .addButton[_ngcontent-%COMP%]{margin-bottom:10px;border:none;margin-right:20px;border-radius:4px;padding:10px;color:#fff;line-height:16px;background-color:#6ae146;display:inline-flex;align-items:center;justify-content:center;height:40px}.invConfig[_ngcontent-%COMP%]   .bright-disabled[_ngcontent-%COMP%]{opacity:none;pointer-events:none}  .mat-menu-panel.menu-class{min-width:300px!important;max-width:-moz-fit-content!important;max-width:fit-content!important;max-height:calc(100vh - 387px)!important}"]}),e})()},{path:"template-config",component:(()=>{class e{constructor(e,t,n){this.invConfigService=e,this.formBuilder=t,this._toaster=n,this.isComponentLoading=!1,this.admintemplateform=this.formBuilder.group({}),this.apiParams=[]}ngOnInit(){return Object(c.c)(this,void 0,void 0,(function*(){this.serviceDetails=yield this.getServiceType(),this.templateConfig=yield this.getTemplateConfig(null),this.isComponentLoading=!0,console.log("template config"),console.log(this.templateConfig),console.log(this.serviceDetails)}))}getServiceType(){return new Promise((e,t)=>{this.invConfigService.getServiceType().subscribe(t=>{e(t.data)},e=>{console.log(e),t(e)})})}getTemplateConfig(e){return new Promise((t,n)=>{this.invConfigService.getTemplateConfig(e).subscribe(e=>{this.selectedServiceTypeId=e.service_type_id,console.log("----------------------------------"),console.log(e),console.log(this.selectedServiceTypeId),console.log(e.data),t(e.data)},e=>{console.log(e),n(e)})})}onStchangedropdownvalue(e){return Object(c.c)(this,void 0,void 0,(function*(){console.log("Entered into function"),this.templateConfig=yield this.getTemplateConfig(e.value),console.log("lensp"),console.log(this.templateConfig),console.log(this.templateConfig.length)}))}onchangelabel(e,t,n,i){this.templateConfig[n].label_name=e.target.value,this.removeDocument(this.templateConfig[n]),this.apiParams.push(this.templateConfig[n])}removeDocument(e){this.apiParams.forEach((t,n)=>{t===e&&this.apiParams.splice(n,1)})}toggleismandatoryChanges(e,t,n){this.templateConfig[n].is_mandatory=!t,this.removeDocument(this.templateConfig[n]),this.apiParams.push(this.templateConfig[n])}toggleisactiveChanges(e,t,n){this.templateConfig[n].active_status=!t,this.removeDocument(this.templateConfig[n]),this.apiParams.push(this.templateConfig[n])}onSubmit(){return console.log("34dar"),console.log(this.apiParams),new Promise((e,t)=>{this.invConfigService.updateTemplateConfig(this.apiParams).subscribe(t=>{t.err||(this._toaster.showSuccess("Success","Template configuration updated Successfully !",2e3),this.apiParams=[],e(t))},e=>{console.log("error",e),t(e)})})}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275directiveInject"](C.a),a["\u0275\u0275directiveInject"](m.i),a["\u0275\u0275directiveInject"](x.a))},e.\u0275cmp=a["\u0275\u0275defineComponent"]({type:e,selectors:[["app-invoice-template-config"]],decls:2,vars:2,consts:[[4,"ngIf"],[1,"d-flex","justify-content-center","mt-3"],["matTooltip","Please wait...","diameter","30"],[1,"invConfig","scroll-area"],[1,"btns"],["appearance","outline",2,"width","30%","margin-left","30px","margin-top","20px"],["matNativeControl","",3,"ngModel","ngModelChange","selectionChange"],[3,"value",4,"ngFor","ngForOf"],["type","submit",1,"submitButton",3,"disabled","ngClass","click"],[1,"arrow_icon"],["style","display: flex;justify-content: center;",4,"ngIf"],[3,"value"],[3,"matTooltip"],[2,"display","flex","justify-content","center"],[2,"width","fit-content","justify-content","center"],[1,"header","pl-3"],[2,"display","inline-flex"],[2,"width","70px"],[2,"width","150px"],[1,"fieldwidth"],[3,"formGroup"],[4,"ngFor","ngForOf"],[1,"rowstyle"],[1,"fieldstyle","keyval",2,"width","70px"],["fontSet","material-icons-outlined","matSuffix","","matTooltip","Field is locked",4,"ngIf"],[1,"fieldstyle",2,"width","150px"],["appearance","outline",2,"width","80%",3,"ngClass"],["matInput","","placeholder","Enter Field label",3,"value","disabled","ngClass","change"],[1,"fieldstyle",2,"width","70px"],[3,"checked","disabled","ngClass","change"],[1,"fieldwidth","keyval"],["fontSet","material-icons-outlined","matSuffix","","matTooltip","Field is locked"],[1,"d-flex","justify-content-center","align-items-center","mt-5","slide-in-top"],[1,"d-flex","justify-content-center","align-items-center","slide-from-down"],["src","assets/images/nomilestone.png","height","300","width","350",1,"mt-4"]],template:function(e,t){1&e&&(a["\u0275\u0275template"](0,B,3,0,"ng-container",0),a["\u0275\u0275template"](1,X,14,9,"ng-container",0)),2&e&&(a["\u0275\u0275property"]("ngIf",!t.isComponentLoading),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",t.isComponentLoading))},directives:[i.NgIf,_.c,b.a,g.c,g.g,w.c,m.v,m.y,i.NgForOf,i.NgClass,r.a,E.p,l.a,m.J,m.w,m.n,f.b,P.a,g.i],styles:['@import url("https://fonts.googleapis.com/icon?family=Material+Icons|Material+Icons+Outlined");.invConfig[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{font-size:15px;width:-moz-fit-content;width:fit-content;padding-top:20px;margin-left:30px}.invConfig[_ngcontent-%COMP%]   .rowstyle[_ngcontent-%COMP%]{display:inline-flex;border-color:#c6c0c0;padding-top:5px;padding-left:15px;margin-left:30px}.invConfig[_ngcontent-%COMP%]   .fieldstyle[_ngcontent-%COMP%], .invConfig[_ngcontent-%COMP%]   .keyval[_ngcontent-%COMP%]{display:flex;align-items:center}.invConfig[_ngcontent-%COMP%]   .submitButton[_ngcontent-%COMP%]{margin-bottom:10px;border:none;margin-right:10px;border-radius:4px;padding:10px;color:#fff;line-height:16px}.invConfig[_ngcontent-%COMP%]   .buttonOn[_ngcontent-%COMP%]{background-color:#ee4961}.invConfig[_ngcontent-%COMP%]   .buttonOff[_ngcontent-%COMP%], .invConfig[_ngcontent-%COMP%]   .buttonOn[_ngcontent-%COMP%]{display:inline-flex;justify-content:center;align-items:center;height:40px;cursor:pointer;margin-left:63px;margin-top:16px}.invConfig[_ngcontent-%COMP%]   .buttonOff[_ngcontent-%COMP%]{background-color:#d18c96}.invConfig[_ngcontent-%COMP%]   .btns[_ngcontent-%COMP%]{position:absolute;display:contents;right:22px}.invConfig[_ngcontent-%COMP%]   .scroll-area[_ngcontent-%COMP%]{height:60vh;overflow-y:scroll}.invConfig[_ngcontent-%COMP%]   .addButton[_ngcontent-%COMP%]{margin-bottom:10px;border:none;margin-right:20px;border-radius:4px;padding:10px;color:#fff;line-height:16px;background-color:#6ae146;display:inline-flex;align-items:center;justify-content:center;height:40px}.invConfig[_ngcontent-%COMP%]   .bright-disabled[_ngcontent-%COMP%]{opacity:1;pointer-events:none}']}),e})()},{path:"fte-config",component:(()=>{class e{constructor(e,t,n){this.invConfigService=e,this.formBuilder=t,this._toaster=n,this.isComponentLoading=!1,this.adminFteform=this.formBuilder.group({}),this.apiParams=[]}ngOnInit(){return Object(c.c)(this,void 0,void 0,(function*(){this.fteConfig=yield this.getfteConfig(),this.isComponentLoading=!0,console.log("template config"),console.log(this.fteConfig)}))}getServiceType(){return new Promise((e,t)=>{this.invConfigService.getServiceType().subscribe(t=>{e(t.data)},e=>{console.log(e),t(e)})})}getfteConfig(){return new Promise((e,t)=>{this.invConfigService.getfteConfig().subscribe(t=>{console.log("----------------------------------"),console.log(t),console.log(t.data),e(t.data)},e=>{console.log(e),t(e)})})}onchangelabel(e,t,n,i){this.fteConfig[n].label_name=e.target.value,this.removeDocument(this.fteConfig[n]),this.apiParams.push(this.fteConfig[n])}removeDocument(e){this.apiParams.forEach((t,n)=>{t===e&&this.apiParams.splice(n,1)})}toggleismandatoryChanges(e,t,n){this.fteConfig[n].is_mandatory=!t,this.removeDocument(this.fteConfig[n]),this.apiParams.push(this.fteConfig[n])}toggleisactiveChanges(e,t,n){this.fteConfig[n].active_status=!t,this.removeDocument(this.fteConfig[n]),this.apiParams.push(this.fteConfig[n])}onSubmit(){return console.log("34dar"),console.log(this.apiParams),new Promise((e,t)=>{this.invConfigService.updatefteConfig(this.apiParams).subscribe(t=>{t.err||(this._toaster.showSuccess("Success","FTE configuration updated Successfully !",2e3),this.apiParams=[],e(t))},e=>{console.log("error",e),t(e)})})}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275directiveInject"](C.a),a["\u0275\u0275directiveInject"](m.i),a["\u0275\u0275directiveInject"](x.a))},e.\u0275cmp=a["\u0275\u0275defineComponent"]({type:e,selectors:[["app-fte-details-config"]],decls:2,vars:2,consts:[[4,"ngIf"],[1,"d-flex","justify-content-center","mt-3"],["matTooltip","Please wait...","diameter","30"],[1,"invConfig","scroll-area"],[1,"btns"],["type","submit",1,"submitButton",3,"disabled","ngClass","click"],[1,"arrow_icon"],["style","display: flex;justify-content: center;",4,"ngIf"],[2,"display","flex","justify-content","center"],[2,"width","fit-content","justify-content","center"],[1,"header","pl-3"],[2,"display","inline-flex"],[2,"width","70px"],[2,"width","150px"],[2,"width","90px"],[1,"fieldwidth"],[3,"formGroup"],[4,"ngFor","ngForOf"],[1,"rowstyle"],[1,"fieldstyle","keyval",2,"width","70px"],[1,"fieldstyle",2,"width","150px"],["appearance","outline",2,"width","80%"],["matInput","","placeholder","Enter Field label",3,"value","change"],[1,"fieldstyle",2,"width","70px"],[3,"checked","change"],[1,"fieldstyle",2,"width","90px"],[1,"fieldwidth","keyval"],[1,"d-flex","justify-content-center","align-items-center","mt-5","slide-in-top"],[1,"d-flex","justify-content-center","align-items-center","slide-from-down"],["src","assets/images/nomilestone.png","height","300","width","350",1,"mt-4"]],template:function(e,t){1&e&&(a["\u0275\u0275template"](0,Y,3,0,"ng-container",0),a["\u0275\u0275template"](1,ee,9,7,"ng-container",0)),2&e&&(a["\u0275\u0275property"]("ngIf",!t.isComponentLoading),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",t.isComponentLoading))},directives:[i.NgIf,_.c,b.a,i.NgClass,r.a,l.a,m.J,m.w,m.n,i.NgForOf,g.c,f.b,P.a],styles:['@import url("https://fonts.googleapis.com/icon?family=Material+Icons|Material+Icons+Outlined");.invConfig[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{font-size:15px;width:-moz-fit-content;width:fit-content;padding-top:20px;margin-left:30px}.invConfig[_ngcontent-%COMP%]   .rowstyle[_ngcontent-%COMP%]{display:inline-flex;border-color:#c6c0c0;padding-top:5px;padding-left:15px;margin-left:30px}.invConfig[_ngcontent-%COMP%]   .fieldstyle[_ngcontent-%COMP%], .invConfig[_ngcontent-%COMP%]   .keyval[_ngcontent-%COMP%]{display:flex;align-items:center}.invConfig[_ngcontent-%COMP%]   .submitButton[_ngcontent-%COMP%]{margin-bottom:10px;border:none;margin-right:10px;border-radius:4px;padding:10px;color:#fff;line-height:16px}.invConfig[_ngcontent-%COMP%]   .buttonOn[_ngcontent-%COMP%]{background-color:#ee4961}.invConfig[_ngcontent-%COMP%]   .buttonOff[_ngcontent-%COMP%], .invConfig[_ngcontent-%COMP%]   .buttonOn[_ngcontent-%COMP%]{display:inline-flex;justify-content:center;align-items:center;height:40px;cursor:pointer;margin-left:63px;margin-top:16px}.invConfig[_ngcontent-%COMP%]   .buttonOff[_ngcontent-%COMP%]{background-color:#d18c96}.invConfig[_ngcontent-%COMP%]   .btns[_ngcontent-%COMP%]{position:absolute;display:contents;right:22px}.invConfig[_ngcontent-%COMP%]   .scroll-area[_ngcontent-%COMP%]{height:60vh;overflow-y:scroll}.invConfig[_ngcontent-%COMP%]   .addButton[_ngcontent-%COMP%]{margin-bottom:10px;border:none;margin-right:20px;border-radius:4px;padding:10px;color:#fff;line-height:16px;background-color:#6ae146;display:inline-flex;align-items:center;justify-content:center;height:40px}.invConfig[_ngcontent-%COMP%]   .bright-disabled[_ngcontent-%COMP%]{opacity:1;pointer-events:none}']}),e})()}]}];let ne=(()=>{class e{}return e.\u0275mod=a["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=a["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[o.k.forChild(te)],o.k]}),e})();var ie=n("Xi0T");let oe=(()=>{class e{}return e.\u0275mod=a["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=a["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,ne,l.d,m.p,m.E,g.e,f.c,P.b,ie.a,w.d,r.b,b.b,_.b,h.b,S.b]]}),e})()}}]);