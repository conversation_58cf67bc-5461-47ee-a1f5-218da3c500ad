(window.webpackJsonp=window.webpackJsonp||[]).push([[685,634,858],{NJ67:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));class i{constructor(){this.disabled=!1}onChange(e){}onTouched(e){}writeValue(e){this.value=e}registerOnChange(e){this.onChange=e}registerOnTouched(e){this.onTouched=e}setDisabledState(e){this.disabled=e}}},ttKn:function(e,t,n){"use strict";n.r(t),n.d(t,"TimesheetUrsReportModule",(function(){return U}));var i=n("ofXK"),r=n("tyNb"),o=n("mrSG"),a=n("3Pt+"),s=n("fXoL"),l=n("jtHE"),c=n("XNiG"),d=n("NJ67"),p=n("1G5W"),h=n("xG9w"),m=n("kmnG"),u=n("d3UM"),g=n("FKr1"),f=n("WJ5W"),y=n("bSwM");function v(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"mat-label"),s["\u0275\u0275text"](1),s["\u0275\u0275elementEnd"]()),2&e){const e=s["\u0275\u0275nextContext"]();s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate"](e.placeholder)}}function b(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"mat-option",7),s["\u0275\u0275text"](1),s["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=s["\u0275\u0275nextContext"]();s["\u0275\u0275property"]("value",e[n.idKey]),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate1"](" ",e[n.valueKey]," ")}}const D=function(){return{standalone:!0}};let S=(()=>{class e extends d.a{constructor(){super(),this.fieldCtrl=new a.j,this.fieldFilterCtrl=new a.j,this.filteredList=new l.a,this.showLabel=!1,this.list=[],this.required=!1,this.idKey="id",this.valueKey="name",this.disabled=!1,this.valueChange=new s.EventEmitter,this._onDestroy=new c.b,this.allSelected=!1}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(p.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(p.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){null!=this.list&&this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>{var n,i;return(null===(i=null===(n=t[this.valueKey])||void 0===n?void 0:n.toLowerCase())||void 0===i?void 0:i.indexOf(e))>-1}))):this.filteredList.next(this.list.slice())}emptyAllSelection(){this.fieldCtrl.patchValue([]),this.allSelected=!1}toggleAllSelection(){this.fieldCtrl.patchValue(this.allSelected?h.pluck(this.list,"id"):[])}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-multi-select-search"]],inputs:{showLabel:"showLabel",list:"list",placeholder:"placeholder",required:"required",idKey:"idKey",valueKey:"valueKey",disabled:"disabled"},outputs:{valueChange:"valueChange"},features:[s["\u0275\u0275ProvidersFeature"]([{provide:a.t,useExisting:Object(s.forwardRef)(()=>e),multi:!0}]),s["\u0275\u0275InheritDefinitionFeature"],s["\u0275\u0275NgOnChangesFeature"]],decls:10,vars:14,consts:[["appearance","outline",2,"width","100%"],[4,"ngIf"],[3,"formControl","placeholder","required","disabled","multiple"],["noEntriesFoundLabel","No Results Found",3,"formControl","placeholderLabel"],[1,"select-all"],[3,"ngModel","ngModelOptions","ngModelChange","change"],[3,"value",4,"ngFor","ngForOf"],[3,"value"]],template:function(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"mat-form-field",0),s["\u0275\u0275template"](1,v,2,1,"mat-label",1),s["\u0275\u0275elementStart"](2,"mat-select",2),s["\u0275\u0275elementStart"](3,"mat-option"),s["\u0275\u0275element"](4,"ngx-mat-select-search",3),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](5,"div",4),s["\u0275\u0275elementStart"](6,"mat-checkbox",5),s["\u0275\u0275listener"]("ngModelChange",(function(e){return t.allSelected=e}))("change",(function(){return t.toggleAllSelection()})),s["\u0275\u0275text"](7,"Select All"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](8,b,2,2,"mat-option",6),s["\u0275\u0275pipe"](9,"async"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e&&(s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",t.showLabel),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled)("multiple",!0),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("ngModel",t.allSelected)("ngModelOptions",s["\u0275\u0275pureFunction0"](13,D)),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("ngForOf",s["\u0275\u0275pipeBind1"](9,11,t.filteredList)))},directives:[m.c,i.NgIf,u.c,a.v,a.k,a.F,g.p,f.a,y.a,a.y,i.NgForOf,m.g],pipes:[i.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}.select-all[_ngcontent-%COMP%]{margin:5px 17px}"]}),e})();var w=n("Kj3r"),x=n("wd/R"),C=n("1yaQ"),M=n("1A3m"),F=n("XXEo"),P=n("tk/3");let _=(()=>{class e{constructor(e){this._http=e}getErrorDetails(){return this._http.post("/api/timesheetv2/Master/getErrorDetails",{})}getEmployeeDetailsForDailyLogReport(e,t,n,i,r,o){return this._http.post("/api/timesheetv2/Master/getEmployeeDetailsForDailyLogReport",{startDate:e,endDate:t,aid:n,oid:i,projectIdForFilter:r,sowIdForFilter:o})}getProjectDetailsForDailyLogReport(e,t,n,i){return this._http.post("/api/timesheetv2/Master/getProjectDetailsForDailyLogReport",{startDate:e,endDate:t,aid:n,oid:i})}getItemDetailsForDailyLogReport(e,t,n,i,r){return this._http.post("/api/timesheetv2/Master/getItemDetailsForDailyLogReport",{startDate:e,endDate:t,aid:n,oid:i,projectIdForFilter:r,retrieveTm:1})}getUrsTimesheetReport(e,t,n,i,r,o,a){return this._http.post("/api/timesheetv2/Reports/getUrsTimesheetReport",{startDate:e,endDate:t,aid:n,oid:i,projectIdForFilter:r,sowIdForFilter:o,aidForFilter:a})}getTimesheetReportAccess(e){return this._http.post("/api/timesheetv2/Reports/getTimesheetReportAccess",{applicationId:e})}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275inject"](P.c))},e.\u0275prov=s["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var E=n("NFeN"),O=n("xHqg"),R=n("iadO"),I=n("ZzPI"),j=n("6t9p");const L=["dataGrid"],Y=["stepper"],k=function(e){return{"pointer-events":e}},T={parse:{dateInput:"DD-MMM-YYYY"},display:{dateInput:"DD-MMM-YYYY",monthYearLabel:"MMM YYYY",dateA11yLabel:"LL",monthYearA11yLabel:"MMMM YYYY"}},A=[{path:"",component:(()=>{class e{constructor(e,t,n,i){this.router=e,this.toastService=t,this.authService=n,this.reportService=i,this.errorData=[],this._onDestroy=new c.b,this.statusList=[],this.employeeList=[],this.portfolioList=[],this.sowList=[],this.logData=[],this.apiInProgress=!1,this.portfolioFilterApiInProgress=!1,this.sowFilterApiInProgress=!1,this.employeeFilterApiInProgress=!1,this.startDate=new a.j(null),this.endDate=new a.j(null),this.portfolio=new a.j([]),this.sow=new a.j([]),this.employee=new a.j([]),this.status=new a.j([])}ngOnInit(){return Object(o.c)(this,void 0,void 0,(function*(){this.getTimesheetReportAccess(),this.currentMonthStartDate=x().startOf("month"),this.currentMonthEndDate=x().endOf("month"),this.startDate.setValue(this.currentMonthStartDate),this.endDate.setValue(this.currentMonthEndDate),this.getProjectDetailsForDailyLogReport(),this.getItemDetailsForDailyLogReport(),this.getEmployeeDetailsForDailyLogReport(),this.calculateDynamicContentHeight(),this.currentUser=this.authService.getProfile().profile,this.oid=this.currentUser.oid,this.aid=this.currentUser.aid,this.token=this.authService.getJwtToken(),yield this.getErrorDetails(),this.portfolio.valueChanges.pipe(Object(w.a)(1e3)).subscribe(()=>Object(o.c)(this,void 0,void 0,(function*(){null!=this.startDate.value&&null!=this.endDate.value&&this.getItemDetailsForDailyLogReport()}))),this.sow.valueChanges.pipe(Object(w.a)(1e3)).subscribe(()=>Object(o.c)(this,void 0,void 0,(function*(){null!=this.startDate.value&&null!=this.endDate.value&&this.getEmployeeDetailsForDailyLogReport()})))}))}onResize(){this.calculateDynamicContentHeight()}calculateDynamicContentHeight(){this.dynamicHeight=window.innerHeight-104+"px",document.documentElement.style.setProperty("--dynamicHeight",this.dynamicHeight),this.dynamicGridReportHeight=window.innerHeight-240+"px",document.documentElement.style.setProperty("--dynamicGridReportHeight",this.dynamicGridReportHeight),this.dynamicReportHeight=window.innerHeight-223+"px",document.documentElement.style.setProperty("--dynamicReportHeight",this.dynamicReportHeight)}goToReportsMainScreen(){this.router.navigate(["/main/reports"])}goToPreviousScreen(){this.onDeleteFilters(),this.clearSortAndFilters(),this.stepper.reset()}onKeyDownDateSearch(e){e.preventDefault()}onChangeInDate(e){null!=this.startDate.value&&null!=this.endDate.value&&this.getProjectDetailsForDailyLogReport()}onDeleteFilters(){this.startDate.setValue(null),this.endDate.setValue(null),this.resetSpecificSearchComponent(0),this.resetSpecificSearchComponent(1),this.resetSpecificSearchComponent(2),this.resetSpecificSearchComponent(3),this.portfolio.setValue([]),this.sow.setValue([]),this.employee.setValue([]),this.status.setValue([]),this.portfolioList=[],this.sowList=[],this.employeeList=[],this.logData=[]}clearSortAndFilters(){this.dataGrid.instance.clearSorting(),this.dataGrid.instance.clearFilter()}resetSpecificSearchComponent(e){if(this.multiSelectSearch){let t=this.multiSelectSearch.toArray()[e];t&&t.emptyAllSelection()}}onGenerateReport(){return Object(o.c)(this,void 0,void 0,(function*(){null!=this.startDate.value&&null!=this.endDate.value?this.portfolioFilterApiInProgress||this.sowFilterApiInProgress||this.employeeFilterApiInProgress?this.toastService.showInfo("Refreshing report with your input. Please wait a moment before clicking Generate Report again!","",3e3):(yield this.getUrsTimesheetReport(),this.stepper.next()):this.toastService.showWarning("Date Range is Mandatory!","")}))}onExporting(e){e.fileName="Timesheet URS Report"}getErrorDetails(){return Object(o.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this.reportService.getErrorDetails().pipe(Object(p.a)(this._onDestroy)).subscribe({next:t=>{"S"==t.messType&&t.data.length>0&&(this.errorData=t.data),e(!0)},error:e=>{console.log(e),t()}}))}))}getEmployeeDetailsForDailyLogReport(){return Object(o.c)(this,void 0,void 0,(function*(){return this.employeeFilterApiInProgress=!0,this.employeeList=[],this.resetSpecificSearchComponent(2),new Promise((e,t)=>this.reportService.getEmployeeDetailsForDailyLogReport(x(this.startDate.value).format("YYYY-MM-DD"),x(this.endDate.value).format("YYYY-MM-DD"),this.aid,this.oid,this.portfolio.value,this.sow.value).pipe(Object(p.a)(this._onDestroy)).subscribe({next:t=>{"S"==t.messType?this.employeeList=t.data&&t.data.length>0?t.data:[]:this.toastService.showInfo("Timesheet App Message",t.messText,3e3),this.employeeFilterApiInProgress=!1,e(!0)},error:e=>{var n;this.toastService.showError("Timesheet Error "+e.error.code,null===(n=h.filter(this.errorData,{error_code:e.error.code})[0])||void 0===n?void 0:n.user_message,6e4),this.employeeFilterApiInProgress=!1,t()}}))}))}getProjectDetailsForDailyLogReport(){return Object(o.c)(this,void 0,void 0,(function*(){return this.portfolioFilterApiInProgress=!0,this.portfolioList=[],this.resetSpecificSearchComponent(0),new Promise((e,t)=>this.reportService.getProjectDetailsForDailyLogReport(x(this.startDate.value).format("YYYY-MM-DD"),x(this.endDate.value).format("YYYY-MM-DD"),this.aid,this.oid).pipe(Object(p.a)(this._onDestroy)).subscribe({next:t=>{"S"==t.messType?this.portfolioList=t.data&&t.data.length>0?t.data:[]:this.toastService.showInfo("Timesheet App Message",t.messText,3e3),this.portfolioFilterApiInProgress=!1,e(!0)},error:e=>{var n;this.toastService.showError("Timesheet Error "+e.error.code,null===(n=h.filter(this.errorData,{error_code:e.error.code})[0])||void 0===n?void 0:n.user_message,6e4),this.portfolioFilterApiInProgress=!1,t()}}))}))}getItemDetailsForDailyLogReport(){return Object(o.c)(this,void 0,void 0,(function*(){return this.sowFilterApiInProgress=!0,this.sowList=[],this.resetSpecificSearchComponent(1),new Promise((e,t)=>this.reportService.getItemDetailsForDailyLogReport(x(this.startDate.value).format("YYYY-MM-DD"),x(this.endDate.value).format("YYYY-MM-DD"),this.aid,this.oid,this.portfolio.value).pipe(Object(p.a)(this._onDestroy)).subscribe({next:t=>{"S"==t.messType?this.sowList=t.data&&t.data.length>0?t.data:[]:this.toastService.showInfo("Timesheet App Message",t.messText,3e3),this.sowFilterApiInProgress=!1,e(!0)},error:e=>{var n;this.toastService.showError("Timesheet Error "+e.error.code,null===(n=h.filter(this.errorData,{error_code:e.error.code})[0])||void 0===n?void 0:n.user_message,6e4),this.sowFilterApiInProgress=!1,t()}}))}))}getUrsTimesheetReport(){return Object(o.c)(this,void 0,void 0,(function*(){return this.apiInProgress=!0,new Promise((e,t)=>this.reportService.getUrsTimesheetReport(x(this.startDate.value).format("YYYY-MM-DD"),x(this.endDate.value).format("YYYY-MM-DD"),this.aid,this.oid,this.portfolio.value&&this.portfolio.value.length>0?this.portfolio.value:[],this.sow.value&&this.sow.value.length>0?this.sow.value:[],this.employee.value&&this.employee.value.length>0?this.employee.value:[]).pipe(Object(p.a)(this._onDestroy)).subscribe({next:t=>{"S"==t.messType?this.logData=t.data:this.toastService.showInfo("Timesheet App Message",t.messText,3e3),this.apiInProgress=!1,e(!0)},error:e=>{var n;this.toastService.showError("Timesheet Error "+e.error.code,null===(n=h.filter(this.errorData,{error_code:e.error.code})[0])||void 0===n?void 0:n.user_message,6e4),this.apiInProgress=!1,t()}}))}))}getTimesheetReportAccess(){this.reportService.getTimesheetReportAccess(985).pipe(Object(p.a)(this._onDestroy)).subscribe({next:e=>{e.reportAccess||this.router.navigateByUrl("/main/reports")},error:e=>{this.toastService.showError("Timesheet App Message",e.messText,3e3)}})}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275directiveInject"](r.g),s["\u0275\u0275directiveInject"](M.a),s["\u0275\u0275directiveInject"](F.a),s["\u0275\u0275directiveInject"](_))},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-timesheet-urs-report"]],viewQuery:function(e,t){if(1&e&&(s["\u0275\u0275viewQuery"](L,!0),s["\u0275\u0275viewQuery"](Y,!0),s["\u0275\u0275viewQuery"](S,!0)),2&e){let e;s["\u0275\u0275queryRefresh"](e=s["\u0275\u0275loadQuery"]())&&(t.dataGrid=e.first),s["\u0275\u0275queryRefresh"](e=s["\u0275\u0275loadQuery"]())&&(t.stepper=e.first),s["\u0275\u0275queryRefresh"](e=s["\u0275\u0275loadQuery"]())&&(t.multiSelectSearch=e)}},hostBindings:function(e,t){1&e&&s["\u0275\u0275listener"]("resize",(function(){return t.onResize()}),!1,s["\u0275\u0275resolveWindow"])},features:[s["\u0275\u0275ProvidersFeature"]([{provide:g.c,useClass:C.c,deps:[g.f,C.a]},{provide:g.e,useValue:T}])],decls:77,vars:28,consts:[[1,"bg-container"],[1,"report-screen"],[1,"align-items-center",2,"margin-bottom","16px"],[1,"align-items-center"],[1,"back-btn",3,"click"],[1,"back-btn-icon"],[1,"header-title"],["stepper",""],["label","Report Input","editable","false"],[1,"align-items-center-justify"],[1,"col-8","p-0","report-height","flex-direction-column"],[1,"align-items-center",2,"margin-bottom","16px","gap","10px","height","60px"],[1,"col-6","p-0"],["appearance","outline",1,"form-field"],[3,"rangePicker"],["matStartDate","","placeholder","Start Date",3,"formControl","keydown","dateChange"],["matEndDate","","placeholder","End Date",3,"formControl","keydown","dateChange"],["matSuffix","",3,"for"],["picker",""],["placeholder","Portfolio",3,"formControl","list"],[1,"align-items-center",2,"margin-bottom","16px","gap","10px"],["placeholder","Project Code",3,"formControl","list"],["placeholder","Employee Name",3,"formControl","list"],[1,"btn-line-style"],[1,"btn-style",3,"ngStyle","click"],["label","Report Result","editable","false"],[1,"align-items-space-between"],[1,"btn-style",3,"click"],[1,"report-height"],[1,"data-grid",3,"height","dataSource","showBorders","columnAutoWidth","showColumnLines","showRowLines","onExporting"],["dataGrid",""],[3,"visible"],["mode","select",3,"allowSearch","enabled"],[3,"enabled"],["mode","infinite"],["caption","Employee Id","dataField","employee_id"],["caption","Employee Name","dataField","employee_name"],["caption","Employment Type","dataField","employment_type"],["caption","Employment Status","dataField","employment_status"],["caption","Customer","dataField","customer"],["caption","Portfolio","dataField","portfolio"],["caption","Project Code","dataField","project_code"],["caption","Project Type","dataField","project_type"],["caption","Zifo Legal Entity","dataField","legal_entity"],["caption","Region","dataField","region"],["caption","Business Unit","dataField","business_unit"],["caption","Allocation Start Date","dataField","allocation_start_date"],["caption","Allocation End Date","dataField","allocation_end_date"],["caption","Billing Type","dataField","billing_type"],["caption","Location","dataField","location"],["caption","Date","dataField","date","dataType","date","format","dd-MMM-yyyy"],["caption","Logged Hours","dataField","logged_hours"],["caption","Rule Type","dataField","rule_type"],["caption","Billable Hours","dataField","billable_hours"],["caption","NonBillable Hours","dataField","nonbillable_hours"],["caption","Billing Rule Hours","dataField","billing_rule_hours","dataType","number"],[3,"groupInterval"],["caption","Project Daily Working Hours","dataField","daily_working_hours"]],template:function(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"div",0),s["\u0275\u0275elementStart"](1,"div",1),s["\u0275\u0275elementStart"](2,"div",2),s["\u0275\u0275elementStart"](3,"div",3),s["\u0275\u0275elementStart"](4,"div",4),s["\u0275\u0275listener"]("click",(function(){return t.goToReportsMainScreen()})),s["\u0275\u0275elementStart"](5,"mat-icon",5),s["\u0275\u0275text"](6,"chevron_left"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275text"](7," Back "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](8,"div",6),s["\u0275\u0275text"](9,"Timesheet URS Report"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](10,"mat-horizontal-stepper",null,7),s["\u0275\u0275elementStart"](12,"mat-step",8),s["\u0275\u0275elementStart"](13,"div",9),s["\u0275\u0275elementStart"](14,"div",10),s["\u0275\u0275elementStart"](15,"div",11),s["\u0275\u0275elementStart"](16,"div",12),s["\u0275\u0275elementStart"](17,"mat-form-field",13),s["\u0275\u0275elementStart"](18,"mat-label"),s["\u0275\u0275text"](19,"Duration *"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](20,"mat-date-range-input",14),s["\u0275\u0275elementStart"](21,"input",15),s["\u0275\u0275listener"]("keydown",(function(e){return t.onKeyDownDateSearch(e)}))("dateChange",(function(e){return t.onChangeInDate(e)})),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](22,"input",16),s["\u0275\u0275listener"]("keydown",(function(e){return t.onKeyDownDateSearch(e)}))("dateChange",(function(e){return t.onChangeInDate(e)})),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275element"](23,"mat-datepicker-toggle",17),s["\u0275\u0275element"](24,"mat-date-range-picker",null,18),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](26,"div",12),s["\u0275\u0275element"](27,"app-multi-select-search",19),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](28,"div",20),s["\u0275\u0275elementStart"](29,"div",12),s["\u0275\u0275element"](30,"app-multi-select-search",21),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](31,"div",12),s["\u0275\u0275element"](32,"app-multi-select-search",22),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](33,"div",23),s["\u0275\u0275elementStart"](34,"div",24),s["\u0275\u0275listener"]("click",(function(){return t.onDeleteFilters()})),s["\u0275\u0275text"](35," Clear Inputs "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](36,"div",24),s["\u0275\u0275listener"]("click",(function(){return t.onGenerateReport()})),s["\u0275\u0275text"](37," Generate Report "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](38,"mat-step",25),s["\u0275\u0275elementStart"](39,"div",26),s["\u0275\u0275elementStart"](40,"div",4),s["\u0275\u0275listener"]("click",(function(){return t.goToPreviousScreen()})),s["\u0275\u0275elementStart"](41,"mat-icon",5),s["\u0275\u0275text"](42,"chevron_left"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275text"](43," Back to Previous Screen "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](44,"div",27),s["\u0275\u0275listener"]("click",(function(){return t.clearSortAndFilters()})),s["\u0275\u0275text"](45," Clear Sort & Filters "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](46,"div",28),s["\u0275\u0275elementStart"](47,"dx-data-grid",29,30),s["\u0275\u0275listener"]("onExporting",(function(e){return t.onExporting(e)})),s["\u0275\u0275element"](49,"dxo-filter-row",31),s["\u0275\u0275element"](50,"dxo-header-filter",31),s["\u0275\u0275element"](51,"dxo-column-chooser",32),s["\u0275\u0275element"](52,"dxo-export",33),s["\u0275\u0275element"](53,"dxo-scrolling",34),s["\u0275\u0275element"](54,"dxi-column",35),s["\u0275\u0275element"](55,"dxi-column",36),s["\u0275\u0275element"](56,"dxi-column",37),s["\u0275\u0275element"](57,"dxi-column",38),s["\u0275\u0275element"](58,"dxi-column",39),s["\u0275\u0275element"](59,"dxi-column",40),s["\u0275\u0275element"](60,"dxi-column",41),s["\u0275\u0275element"](61,"dxi-column",42),s["\u0275\u0275element"](62,"dxi-column",43),s["\u0275\u0275element"](63,"dxi-column",44),s["\u0275\u0275element"](64,"dxi-column",45),s["\u0275\u0275element"](65,"dxi-column",46),s["\u0275\u0275element"](66,"dxi-column",47),s["\u0275\u0275element"](67,"dxi-column",48),s["\u0275\u0275element"](68,"dxi-column",49),s["\u0275\u0275element"](69,"dxi-column",50),s["\u0275\u0275element"](70,"dxi-column",51),s["\u0275\u0275element"](71,"dxi-column",52),s["\u0275\u0275element"](72,"dxi-column",53),s["\u0275\u0275element"](73,"dxi-column",54),s["\u0275\u0275elementStart"](74,"dxi-column",55),s["\u0275\u0275element"](75,"dxo-header-filter",56),s["\u0275\u0275elementEnd"](),s["\u0275\u0275element"](76,"dxi-column",57),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e){const e=s["\u0275\u0275reference"](25);s["\u0275\u0275advance"](20),s["\u0275\u0275property"]("rangePicker",e),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("formControl",t.startDate),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("formControl",t.endDate),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("for",e),s["\u0275\u0275advance"](4),s["\u0275\u0275property"]("formControl",t.portfolio)("list",t.portfolioList),s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("formControl",t.sow)("list",t.sowList),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("formControl",t.employee)("list",t.employeeList),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("ngStyle",s["\u0275\u0275pureFunction1"](24,k,t.apiInProgress?"none":"")),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("ngStyle",s["\u0275\u0275pureFunction1"](26,k,t.apiInProgress?"none":"")),s["\u0275\u0275advance"](11),s["\u0275\u0275property"]("height",t.dynamicGridReportHeight)("dataSource",t.logData)("showBorders",!0)("columnAutoWidth",!0)("showColumnLines",!0)("showRowLines",!0),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("visible",!0),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("visible",!0),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("allowSearch",!0)("enabled",!0),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("enabled",!0),s["\u0275\u0275advance"](23),s["\u0275\u0275property"]("groupInterval",1)}},directives:[E.a,O.a,O.b,m.c,m.g,R.d,R.l,a.e,a.v,a.k,R.k,R.i,m.i,R.e,S,i.NgStyle,I.a,j.dc,j.Cc,j.tb,j.Sb,j.Jd,j.g],styles:[".bg-container[_ngcontent-%COMP%]{background-color:#f1f3f8;overflow:hidden}.bg-container[_ngcontent-%COMP%]   .report-screen[_ngcontent-%COMP%]{margin:24px;height:var(--dynamicHeight)}.bg-container[_ngcontent-%COMP%]   .report-height[_ngcontent-%COMP%]{height:var(--dynamicReportHeight)}.bg-container[_ngcontent-%COMP%]   .align-items-center[_ngcontent-%COMP%]{display:flex;align-items:center}.bg-container[_ngcontent-%COMP%]   .align-items-center-justify[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center}.bg-container[_ngcontent-%COMP%]   .align-items-space-between[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center}.bg-container[_ngcontent-%COMP%]   .flex-direction-column[_ngcontent-%COMP%]{display:flex;flex-direction:column}.bg-container[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;color:#111434;font-size:11px;font-weight:400;border:1px solid #8b95a5;border-radius:4px;padding:4px;cursor:pointer;width:-moz-fit-content;width:fit-content}.bg-container[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{width:0!important;height:15px!important}.bg-container[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]   .back-btn-icon[_ngcontent-%COMP%]{font-size:18px;color:#111434;margin-right:20px;margin-bottom:2px}.bg-container[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]{font-size:16px;font-weight:600;color:#111434;margin-left:20px}.bg-container[_ngcontent-%COMP%]   .form-field[_ngcontent-%COMP%]{width:100%}.bg-container[_ngcontent-%COMP%]   .btn-line-style[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;gap:20px}.bg-container[_ngcontent-%COMP%]   .btn-style[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;background-color:#79ba44;border-radius:4px;padding:8px;cursor:pointer;color:#fff}.bg-container[_ngcontent-%COMP%]   .data-grid[_ngcontent-%COMP%]     .dx-toolbar .dx-toolbar-items-container{height:50px!important}.bg-container[_ngcontent-%COMP%]     .mat-step-header .mat-step-icon-selected, .bg-container[_ngcontent-%COMP%]     .mat-step-header .mat-step-icon-state-done, .bg-container[_ngcontent-%COMP%]     .mat-step-header .mat-step-icon-state-edit, .bg-container[_ngcontent-%COMP%]     .mat-step-header .mat-step-icon-state-number{background-color:#79ba44;color:#fff}.bg-container[_ngcontent-%COMP%]     .mat-step-header{pointer-events:none!important}"]}),e})()}];let H=(()=>{class e{}return e.\u0275mod=s["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=s["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[r.k.forChild(A)],r.k]}),e})();var G=n("PVOt"),N=n("Xa2L"),K=n("qFsG"),B=n("Qu3c");let U=(()=>{class e{}return e.\u0275mod=s["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=s["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,H,I.b,G.f,N.b,m.e,K.c,R.h,g.n,a.p,a.E,E.b,B.b,u.d,f.b,O.f,y.b]]}),e})()}}]);