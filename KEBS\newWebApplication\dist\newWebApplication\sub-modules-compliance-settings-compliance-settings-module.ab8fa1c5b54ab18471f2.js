(window.webpackJsonp=window.webpackJsonp||[]).push([[1008,267,634,853,858,861,981],{"8SgF":function(e,t,i){"use strict";i.d(t,"a",(function(){return S}));var n=i("mrSG"),r=i("fXoL"),s=i("XNiG"),o=i("Kj3r"),a=i("1G5W"),l=i("3Pt+"),d=i("NJ67"),h=i("F97M"),c=i("XVR1"),g=i("kmnG"),m=i("ofXK"),p=i("qFsG"),u=i("/1cH"),b=i("NFeN"),f=i("FKr1");function C(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"mat-label"),r["\u0275\u0275text"](1),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate"](e.label)}}function O(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"mat-option",7),r["\u0275\u0275elementStart"](1,"small"),r["\u0275\u0275text"](2),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,i=r["\u0275\u0275nextContext"]();r["\u0275\u0275property"]("hidden",!i.isAutocomplete)("value",e),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate2"]("",e.displayName," | ",e.mail,"")}}let S=(()=>{class e extends d.a{constructor(e,t){super(),this.graphApi=e,this._AppShareService=t,this.userSearchSubject=new s.b,this.isAutocomplete=!1,this.Formwidth="",this.showUserList=new r.EventEmitter,this.selectedUser=new r.EventEmitter,this.label="",this.blur=new r.EventEmitter,this.required=!1,this.fieldCtrl=new l.j,this.disabled=!1,this.readonly=!1,this.isGraphApi=0,this.optClicked=!1,this._onDestroy=new s.b}ngOnInit(){this.userSearchSubject.pipe(Object(o.a)(600)).subscribe(e=>Object(n.c)(this,void 0,void 0,(function*(){if(console.log(e),this.isGraphApi)this.graphApi.getUserSuggestions(e).then(t=>Object(n.c)(this,void 0,void 0,(function*(){if(t&&t.length>0)this.showUserList.emit(t);else{let t=yield this.getUserSuggestionsFromDB(e);this.showUserList.emit(t)}})),t=>Object(n.c)(this,void 0,void 0,(function*(){console.log(t);let i=yield this.getUserSuggestionsFromDB(e);this.showUserList.emit(i)})));else{let t=yield this.getUserSuggestionsFromDB(e);this.showUserList.emit(t)}}))),this.fieldCtrl.valueChanges.pipe(Object(a.a)(this._onDestroy)).subscribe(e=>{console.log(e),e?(this.value=e.id,this.onChange(e.id)):(this.value="",this.onChange(""))})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}ngAfterViewInit(){}searchUser(e){if(this.text=e.target.value,this.optClicked="Enter"==e.key,!this.text)return this.graphApi.userSuggestions=[];this.userSearchSubject.next(this.text)}resetSuggestion(){this.graphApi.userSuggestions=[]}checkAndClearInput(){this.optClicked||0!=this.readonly||this.fieldCtrl.setValue("")}selectedOption(e){this.optClicked=!0,this.selectedUser.emit(e.option.value),this.value=e.option.value,this.blur.emit()}displayUserName(e){return e?e.displayName:""}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){return Object(n.c)(this,void 0,void 0,(function*(){if(console.log("inisde writevalue"),console.log(e),e)if(this.isGraphApi)this.graphApi.getUserProfile(e).then(t=>Object(n.c)(this,void 0,void 0,(function*(){if(t)this.fieldCtrl.setValue(t);else{let t=yield this.getUserProfileFromDB(e);this.fieldCtrl.setValue(t)}})),t=>Object(n.c)(this,void 0,void 0,(function*(){let t=yield this.getUserProfileFromDB(e);this.fieldCtrl.setValue(t)})));else{let t=yield this.getUserProfileFromDB(e);console.log(t),this.fieldCtrl.setValue(t)}else this.fieldCtrl.setValue("")}))}getUserSuggestionsFromDB(e){return Object(n.c)(this,void 0,void 0,(function*(){let t=yield this._AppShareService.getUserSuggestionsFromDB(e);return this.graphApi.userSuggestions=t.value,t.value}))}getUserProfileFromDB(e){return Object(n.c)(this,void 0,void 0,(function*(){let t=yield this._AppShareService.getUserProfileFromDB(e);return console.log(t),t&&t.length>0?t[0]:""}))}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](h.a),r["\u0275\u0275directiveInject"](c.a))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["app-search-user"]],inputs:{isAutocomplete:"isAutocomplete",Formwidth:"Formwidth",label:"label",required:"required",disabled:"disabled",readonly:"readonly",isGraphApi:"isGraphApi"},outputs:{showUserList:"showUserList",selectedUser:"selectedUser",blur:"blur"},features:[r["\u0275\u0275ProvidersFeature"]([{provide:l.t,useExisting:Object(r.forwardRef)(()=>e),multi:!0}]),r["\u0275\u0275InheritDefinitionFeature"]],decls:9,vars:10,consts:[["appearance","outline","width","Formwidth"],[4,"ngIf"],["matInput","",3,"matAutocomplete","placeholder","required","formControl","disabled","readonly","keyup","focus","focusout"],["matSuffix","",2,"color","#66615B !important","font-size","21px !important"],[3,"hidden","displayWith","optionSelected"],["auto","matAutocomplete"],[3,"hidden","value",4,"ngFor","ngForOf"],[3,"hidden","value"]],template:function(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div"),r["\u0275\u0275elementStart"](1,"mat-form-field",0),r["\u0275\u0275template"](2,C,2,1,"mat-label",1),r["\u0275\u0275elementStart"](3,"input",2),r["\u0275\u0275listener"]("keyup",(function(e){return t.searchUser(e)}))("focus",(function(){return t.resetSuggestion()}))("focusout",(function(){return t.checkAndClearInput()})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](4,"mat-icon",3),r["\u0275\u0275text"](5,"person_pin"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](6,"mat-autocomplete",4,5),r["\u0275\u0275listener"]("optionSelected",(function(e){return t.selectedOption(e)})),r["\u0275\u0275template"](8,O,3,4,"mat-option",6),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275reference"](7);r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngIf",t.label),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("matAutocomplete",e)("placeholder",t.label)("required",t.required)("formControl",t.fieldCtrl)("disabled",t.disabled)("readonly",t.readonly),r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("hidden",!t.isAutocomplete)("displayWith",t.displayUserName),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngForOf",t.graphApi.userSuggestions)}},directives:[g.c,m.NgIf,p.b,u.d,l.e,l.F,l.v,l.k,b.a,g.i,u.b,m.NgForOf,g.g,f.p],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding-bottom:8px!important}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-underline{bottom:0}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-infix{padding:.65em 0!important;border-top:.54375em solid transparent!important;font-size:13px}.mat-form-field[_ngcontent-%COMP%]     .mat-select-arrow{margin:0 4px!important}"]}),e})()},NJ67:function(e,t,i){"use strict";i.d(t,"a",(function(){return n}));class n{constructor(){this.disabled=!1}onChange(e){}onTouched(e){}writeValue(e){this.value=e}registerOnChange(e){this.onChange=e}registerOnTouched(e){this.onTouched=e}setDisabledState(e){this.disabled=e}}},jylm:function(e,t,i){"use strict";i.r(t),i.d(t,"ComplianceSettingsModule",(function(){return x}));var n=i("ofXK"),r=i("tyNb"),s=i("1G5W"),o=i("XNiG"),a=i("3Pt+"),l=i("fXoL"),d=i("LcQX"),h=i("BVzC"),c=i("WGBV"),g=i("GnQ3"),m=i("XXEo"),p=i("R/Xf"),u=i("8SgF"),b=i("bTqV"),f=i("Qu3c"),C=i("NFeN"),O=i("kmnG"),S=i("d3UM"),_=i("FKr1");function v(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"mat-option",15),l["\u0275\u0275text"](1),l["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;l["\u0275\u0275property"]("value",e),l["\u0275\u0275advance"](1),l["\u0275\u0275textInterpolate1"](" ",e.category_name," ")}}function w(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"div",16),l["\u0275\u0275elementStart"](1,"div",17),l["\u0275\u0275elementStart"](2,"span",18),l["\u0275\u0275text"](3,"No Settings found ! "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](4,"div",19),l["\u0275\u0275element"](5,"img",20),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]())}const E=[{path:"",component:(()=>{class e{constructor(e,t,i,n,r,s,a){this._util=e,this._ErrorService=t,this.accountService=i,this._udrfService=n,this._loginService=r,this._cmsService=s,this.fb=a,this.isLoading=!0,this.isSettings=!0,this._onDestroy=new o.b,this.category=[]}ngOnInit(){this.getTypeMasterData("Category"),this.taskOwnerBulkEdit=this.fb.group({taskCurrentOwner:[null,a.H.required],taskUpdOwner:[null,a.H.required]}),this.complainceOwnerEdit=this.fb.group({complianceCurrentOwner:[null,a.H.required],complianceUpdOwner:[null,a.H.required],complianceCategory:[null,a.H.required]})}groupAlert(){this._cmsService.sendGroupAlert().pipe(Object(s.a)(this._onDestroy)).subscribe(e=>{this._util.showMessage("Notification Sent Successfully","Dismiss")},e=>{this._util.showMessage("Error","Dismiss"),this._ErrorService.userErrorAlert(e.error.errorCode,"Error deleting attachment",e.error.errMessage)})}alert(){this._cmsService.sendIndividualAlert().pipe(Object(s.a)(this._onDestroy)).subscribe(e=>{this._util.showMessage("Notification Sent Successfully","Dismiss")},e=>{this._util.showMessage("Error","Dismiss"),this._ErrorService.userErrorAlert(e.error.errorCode,"Error deleting attachment",e.error.errMessage)})}getComplianceInternalDeadlineToSendAlertNotification(){this._cmsService.getComplianceInternalDeadlineToSendAlertNotification().pipe(Object(s.a)(this._onDestroy)).subscribe(e=>{this._util.showMessage("Notification Sent Successfully","Dismiss")},e=>{this._util.showMessage("Error","Dismiss"),this._ErrorService.userErrorAlert(e.error.errorCode,"Error deleting attachment",e.error.errMessage)})}getComplianceDeadlineToSendAlertNotification(){this._cmsService.getComplianceDeadlineToSendAlertNotification().pipe(Object(s.a)(this._onDestroy)).subscribe(e=>{this._util.showMessage("Notification Sent Successfully","Dismiss")},e=>{this._util.showMessage("Error","Dismiss"),this._ErrorService.userErrorAlert(e.error.errorCode,"Error deleting attachment",e.error.errMessage)})}getComplianceDeadlineIn3DaysToSendGroupAlertNotification(){this._cmsService.getComplianceDeadlineIn3DaysToSendGroupAlertNotification().pipe(Object(s.a)(this._onDestroy)).subscribe(e=>{this._util.showMessage("Notification Sent Successfully","Dismiss")},e=>{this._util.showMessage("Error","Dismiss"),this._ErrorService.userErrorAlert(e.error.errorCode,"Error deleting attachment",e.error.errMessage)})}getComplianceDeadlineIn2DaysToSendGroupAlertNotification(){this._cmsService.getComplianceDeadlineIn2DaysToSendGroupAlertNotification().pipe(Object(s.a)(this._onDestroy)).subscribe(e=>{this._util.showMessage("Notification Sent Successfully","Dismiss")},e=>{this._util.showMessage("Error","Dismiss"),this._ErrorService.userErrorAlert(e.error.errorCode,"Error deleting attachment",e.error.errMessage)})}getComplianceDeadlineIn1DaysToSendGroupAlertNotification(){this._cmsService.getComplianceDeadlineIn1DaysToSendGroupAlertNotification().pipe(Object(s.a)(this._onDestroy)).subscribe(e=>{this._util.showMessage("Notification Sent Successfully","Dismiss")},e=>{this._util.showMessage("Error","Dismiss"),this._ErrorService.userErrorAlert(e.error.errorCode,"Error deleting attachment",e.error.errMessage)})}getComplianceDeadlineToSendGroupAlertNotification(){this._cmsService.getComplianceDeadlineToSendGroupAlertNotification().pipe(Object(s.a)(this._onDestroy)).subscribe(e=>{this._util.showMessage("Notification Sent Successfully","Dismiss")},e=>{this._util.showMessage("Error","Dismiss"),this._ErrorService.userErrorAlert(e.error.errorCode,"Error deleting attachment",e.error.errMessage)})}getComplianceInternalDeadlineToSendGroupAlertNotification(){this._cmsService.getComplianceInternalDeadlineToSendGroupAlertNotification().pipe(Object(s.a)(this._onDestroy)).subscribe(e=>{this._util.showMessage("Notification Sent Successfully","Dismiss")},e=>{this._util.showMessage("Error","Dismiss"),this._ErrorService.userErrorAlert(e.error.errorCode,"Error deleting attachment",e.error.errMessage)})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}bulkUpdTaskOwner(){if(this.taskOwnerBulkEdit.valid){let e={taskCurrentOwner:this.taskOwnerBulkEdit.get("taskCurrentOwner").value,taskUpdOwner:this.taskOwnerBulkEdit.get("taskUpdOwner").value};this._cmsService.bulkUpdTaskOwner(e).pipe(Object(s.a)(this._onDestroy)).subscribe(e=>{this._util.showMessage("N"==e.err?"Updated Successfully":"Couldn't update","Dismiss")},e=>{this._util.showMessage("Error","Dismiss"),this._ErrorService.userErrorAlert(e.error.errorCode,"Error updating owner",e.error.errMessage)})}else this._util.showMessage("Kindly Fill All Details","Dismiss");console.log(this.taskOwnerBulkEdit.value)}updComplianceOwner(){if(this.complainceOwnerEdit.valid){let e={oldOwner_id:this.complainceOwnerEdit.get("complianceCurrentOwner").value,newOwner_id:this.complainceOwnerEdit.get("complianceUpdOwner").value,category_name:this.complainceOwnerEdit.get("complianceCategory").value.category_name};this._cmsService.updComplianceOwner(e).pipe(Object(s.a)(this._onDestroy)).subscribe(e=>{this._util.showMessage("N"==e.err?"Updated Successfully":"Couldn't update","Dismiss")},e=>{this._util.showMessage("Error","Dismiss"),this._ErrorService.userErrorAlert(e.error.errorCode,"Error updating owner",e.error.errMessage)})}else this._util.showMessage("Kindly Fill All Details","Dismiss")}getTypeMasterData(e){this._cmsService.getTypeMasterData({type:e}).pipe(Object(s.a)(this._onDestroy)).subscribe(t=>{"Category"==e&&(this.category=t.data)},e=>{console.error(e),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}}return e.\u0275fac=function(t){return new(t||e)(l["\u0275\u0275directiveInject"](d.a),l["\u0275\u0275directiveInject"](h.a),l["\u0275\u0275directiveInject"](c.a),l["\u0275\u0275directiveInject"](g.a),l["\u0275\u0275directiveInject"](m.a),l["\u0275\u0275directiveInject"](p.a),l["\u0275\u0275directiveInject"](a.i))},e.\u0275cmp=l["\u0275\u0275defineComponent"]({type:e,selectors:[["settings-landing-page"]],decls:33,vars:10,consts:[[1,"container-fluid"],[1,"row","pt-5"],[1,"col-12"],[1,"pt-0",2,"font-weight","500","color","#cf0001"],[3,"formGroup","ngSubmit"],[1,"d-flex",2,"gap","5%"],["label","Current Task Owner","required","true","formControlName","taskCurrentOwner",3,"isAutocomplete"],["label","Updated Task Owner","required","true","formControlName","taskUpdOwner",3,"isAutocomplete"],["mat-icon-button","","type","submit",1,"iconbtn",3,"matTooltip"],["label","Current Task Owner","required","true","formControlName","complianceCurrentOwner",3,"isAutocomplete"],["appearance","outline"],["formControlName","complianceCategory"],[3,"value",4,"ngFor","ngForOf"],["label","Updated Task Owner","required","true","formControlName","complianceUpdOwner",3,"isAutocomplete"],["class","my-3","style","text-align: center; padding-top: 3rem",4,"ngIf"],[3,"value"],[1,"my-3",2,"text-align","center","padding-top","3rem"],[1,"d-flex","pb-2","justify-content-center","align-items-center","slide-in-top"],[2,"font-size","20px","font-weight","500"],[1,"d-flex","justify-content-center","align-items-center","slide-from-down","pt-2","pb-2"],["src","https://assets.kebs.app/images/no_compliance.png","height","300","width","300",1,"mt-2","mb-2"]],template:function(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"div",0),l["\u0275\u0275elementStart"](1,"div",1),l["\u0275\u0275elementStart"](2,"div",2),l["\u0275\u0275elementStart"](3,"h3",3),l["\u0275\u0275text"](4,"Task Owner Bulk Update"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](5,"div",2),l["\u0275\u0275elementStart"](6,"form",4),l["\u0275\u0275listener"]("ngSubmit",(function(){return t.bulkUpdTaskOwner()})),l["\u0275\u0275elementStart"](7,"div",5),l["\u0275\u0275element"](8,"app-search-user",6),l["\u0275\u0275element"](9,"app-search-user",7),l["\u0275\u0275elementStart"](10,"button",8),l["\u0275\u0275elementStart"](11,"mat-icon"),l["\u0275\u0275text"](12,"done_all"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](13,"div",1),l["\u0275\u0275elementStart"](14,"div",2),l["\u0275\u0275elementStart"](15,"h3",3),l["\u0275\u0275text"](16,"Edit Compliance Owner"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](17,"div",2),l["\u0275\u0275elementStart"](18,"form",4),l["\u0275\u0275listener"]("ngSubmit",(function(){return t.updComplianceOwner()})),l["\u0275\u0275elementStart"](19,"div",5),l["\u0275\u0275element"](20,"app-search-user",9),l["\u0275\u0275elementStart"](21,"mat-form-field",10),l["\u0275\u0275elementStart"](22,"mat-label"),l["\u0275\u0275text"](23,"Category"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](24,"mat-select",11),l["\u0275\u0275elementStart"](25,"mat-option"),l["\u0275\u0275text"](26,"None"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](27,v,2,2,"mat-option",12),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275element"](28,"app-search-user",13),l["\u0275\u0275elementStart"](29,"button",8),l["\u0275\u0275elementStart"](30,"mat-icon"),l["\u0275\u0275text"](31,"done_all"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](32,w,6,0,"div",14),l["\u0275\u0275elementEnd"]()),2&e&&(l["\u0275\u0275advance"](6),l["\u0275\u0275property"]("formGroup",t.taskOwnerBulkEdit),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("isAutocomplete",!0),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("isAutocomplete",!0),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("matTooltip","Update Task Owner"),l["\u0275\u0275advance"](8),l["\u0275\u0275property"]("formGroup",t.complainceOwnerEdit),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("isAutocomplete",!0),l["\u0275\u0275advance"](7),l["\u0275\u0275property"]("ngForOf",t.category),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("isAutocomplete",!0),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("matTooltip","Update Task Owner"),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("ngIf",!t.isLoading&&!t.isSettings))},directives:[a.J,a.w,a.n,u.a,a.F,a.v,a.l,b.a,f.a,C.a,O.c,O.g,S.c,_.p,n.NgForOf,n.NgIf],styles:[".reset-btn[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff;padding:1px}.slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}.slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}.iconbtn[_ngcontent-%COMP%]{background-color:#c92020;color:#fff;padding:0;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}"]}),e})()}];let y=(()=>{class e{}return e.\u0275mod=l["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=l["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[r.k.forChild(E)],r.k]}),e})();var D=i("Xi0T");let x=(()=>{class e{}return e.\u0275mod=l["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=l["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[n.CommonModule,y,C.b,b.b,f.b,D.a,a.p,a.E,O.e,S.d]]}),e})()},w76M:function(e,t,i){"use strict";i.d(t,"a",(function(){return d})),i.d(t,"b",(function(){return h}));var n=i("jhN1"),r=i("fXoL"),s=i("oHs6"),o=i("PVOt"),a=i("6t9p");const l=["*"];let d=(()=>{let e=class extends o.b{constructor(e,t,i,n,r,s,o,a){super(e,t,i,n,o,a),this._watcherHelper=n,this._idh=r,this._createEventEmitters([{subscribe:"contentReady",emit:"onContentReady"},{subscribe:"disposing",emit:"onDisposing"},{subscribe:"hidden",emit:"onHidden"},{subscribe:"hiding",emit:"onHiding"},{subscribe:"initialized",emit:"onInitialized"},{subscribe:"optionChanged",emit:"onOptionChanged"},{subscribe:"resize",emit:"onResize"},{subscribe:"resizeEnd",emit:"onResizeEnd"},{subscribe:"resizeStart",emit:"onResizeStart"},{subscribe:"showing",emit:"onShowing"},{subscribe:"shown",emit:"onShown"},{subscribe:"titleRendered",emit:"onTitleRendered"},{emit:"accessKeyChange"},{emit:"animationChange"},{emit:"closeOnOutsideClickChange"},{emit:"containerChange"},{emit:"contentTemplateChange"},{emit:"deferRenderingChange"},{emit:"disabledChange"},{emit:"dragEnabledChange"},{emit:"elementAttrChange"},{emit:"focusStateEnabledChange"},{emit:"fullScreenChange"},{emit:"heightChange"},{emit:"hintChange"},{emit:"hoverStateEnabledChange"},{emit:"maxHeightChange"},{emit:"maxWidthChange"},{emit:"minHeightChange"},{emit:"minWidthChange"},{emit:"positionChange"},{emit:"resizeEnabledChange"},{emit:"rtlEnabledChange"},{emit:"shadingChange"},{emit:"shadingColorChange"},{emit:"showCloseButtonChange"},{emit:"showTitleChange"},{emit:"tabIndexChange"},{emit:"titleChange"},{emit:"titleTemplateChange"},{emit:"toolbarItemsChange"},{emit:"visibleChange"},{emit:"widthChange"}]),this._idh.setHost(this),s.setHost(this)}get accessKey(){return this._getOption("accessKey")}set accessKey(e){this._setOption("accessKey",e)}get animation(){return this._getOption("animation")}set animation(e){this._setOption("animation",e)}get closeOnOutsideClick(){return this._getOption("closeOnOutsideClick")}set closeOnOutsideClick(e){this._setOption("closeOnOutsideClick",e)}get container(){return this._getOption("container")}set container(e){this._setOption("container",e)}get contentTemplate(){return this._getOption("contentTemplate")}set contentTemplate(e){this._setOption("contentTemplate",e)}get deferRendering(){return this._getOption("deferRendering")}set deferRendering(e){this._setOption("deferRendering",e)}get disabled(){return this._getOption("disabled")}set disabled(e){this._setOption("disabled",e)}get dragEnabled(){return this._getOption("dragEnabled")}set dragEnabled(e){this._setOption("dragEnabled",e)}get elementAttr(){return this._getOption("elementAttr")}set elementAttr(e){this._setOption("elementAttr",e)}get focusStateEnabled(){return this._getOption("focusStateEnabled")}set focusStateEnabled(e){this._setOption("focusStateEnabled",e)}get fullScreen(){return this._getOption("fullScreen")}set fullScreen(e){this._setOption("fullScreen",e)}get height(){return this._getOption("height")}set height(e){this._setOption("height",e)}get hint(){return this._getOption("hint")}set hint(e){this._setOption("hint",e)}get hoverStateEnabled(){return this._getOption("hoverStateEnabled")}set hoverStateEnabled(e){this._setOption("hoverStateEnabled",e)}get maxHeight(){return this._getOption("maxHeight")}set maxHeight(e){this._setOption("maxHeight",e)}get maxWidth(){return this._getOption("maxWidth")}set maxWidth(e){this._setOption("maxWidth",e)}get minHeight(){return this._getOption("minHeight")}set minHeight(e){this._setOption("minHeight",e)}get minWidth(){return this._getOption("minWidth")}set minWidth(e){this._setOption("minWidth",e)}get position(){return this._getOption("position")}set position(e){this._setOption("position",e)}get resizeEnabled(){return this._getOption("resizeEnabled")}set resizeEnabled(e){this._setOption("resizeEnabled",e)}get rtlEnabled(){return this._getOption("rtlEnabled")}set rtlEnabled(e){this._setOption("rtlEnabled",e)}get shading(){return this._getOption("shading")}set shading(e){this._setOption("shading",e)}get shadingColor(){return this._getOption("shadingColor")}set shadingColor(e){this._setOption("shadingColor",e)}get showCloseButton(){return this._getOption("showCloseButton")}set showCloseButton(e){this._setOption("showCloseButton",e)}get showTitle(){return this._getOption("showTitle")}set showTitle(e){this._setOption("showTitle",e)}get tabIndex(){return this._getOption("tabIndex")}set tabIndex(e){this._setOption("tabIndex",e)}get title(){return this._getOption("title")}set title(e){this._setOption("title",e)}get titleTemplate(){return this._getOption("titleTemplate")}set titleTemplate(e){this._setOption("titleTemplate",e)}get toolbarItems(){return this._getOption("toolbarItems")}set toolbarItems(e){this._setOption("toolbarItems",e)}get visible(){return this._getOption("visible")}set visible(e){this._setOption("visible",e)}get width(){return this._getOption("width")}set width(e){this._setOption("width",e)}get toolbarItemsChildren(){return this._getOption("toolbarItems")}set toolbarItemsChildren(e){this.setChildren("toolbarItems",e)}_createInstance(e,t){return new s.a(e,t)}ngOnDestroy(){this._destroyWidget()}ngOnChanges(e){super.ngOnChanges(e),this.setupChanges("toolbarItems",e)}setupChanges(e,t){e in this._optionsToUpdate||this._idh.setup(e,t)}ngDoCheck(){this._idh.doCheck("toolbarItems"),this._watcherHelper.checkWatchers(),super.ngDoCheck(),super.clearChangedOptions()}_setOption(e,t){let i=this._idh.setupSingle(e,t),n=null!==this._idh.getChanges(e,t);(i||n)&&super._setOption(e,t)}};return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](r.ElementRef),r["\u0275\u0275directiveInject"](r.NgZone),r["\u0275\u0275directiveInject"](o.e),r["\u0275\u0275directiveInject"](o.j),r["\u0275\u0275directiveInject"](o.g),r["\u0275\u0275directiveInject"](o.i),r["\u0275\u0275directiveInject"](n.h),r["\u0275\u0275directiveInject"](r.PLATFORM_ID))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["dx-popup"]],contentQueries:function(e,t,i){if(1&e&&r["\u0275\u0275contentQuery"](i,a.L,!1),2&e){let e;r["\u0275\u0275queryRefresh"](e=r["\u0275\u0275loadQuery"]())&&(t.toolbarItemsChildren=e)}},inputs:{accessKey:"accessKey",animation:"animation",closeOnOutsideClick:"closeOnOutsideClick",container:"container",contentTemplate:"contentTemplate",deferRendering:"deferRendering",disabled:"disabled",dragEnabled:"dragEnabled",elementAttr:"elementAttr",focusStateEnabled:"focusStateEnabled",fullScreen:"fullScreen",height:"height",hint:"hint",hoverStateEnabled:"hoverStateEnabled",maxHeight:"maxHeight",maxWidth:"maxWidth",minHeight:"minHeight",minWidth:"minWidth",position:"position",resizeEnabled:"resizeEnabled",rtlEnabled:"rtlEnabled",shading:"shading",shadingColor:"shadingColor",showCloseButton:"showCloseButton",showTitle:"showTitle",tabIndex:"tabIndex",title:"title",titleTemplate:"titleTemplate",toolbarItems:"toolbarItems",visible:"visible",width:"width"},outputs:{onContentReady:"onContentReady",onDisposing:"onDisposing",onHidden:"onHidden",onHiding:"onHiding",onInitialized:"onInitialized",onOptionChanged:"onOptionChanged",onResize:"onResize",onResizeEnd:"onResizeEnd",onResizeStart:"onResizeStart",onShowing:"onShowing",onShown:"onShown",onTitleRendered:"onTitleRendered",accessKeyChange:"accessKeyChange",animationChange:"animationChange",closeOnOutsideClickChange:"closeOnOutsideClickChange",containerChange:"containerChange",contentTemplateChange:"contentTemplateChange",deferRenderingChange:"deferRenderingChange",disabledChange:"disabledChange",dragEnabledChange:"dragEnabledChange",elementAttrChange:"elementAttrChange",focusStateEnabledChange:"focusStateEnabledChange",fullScreenChange:"fullScreenChange",heightChange:"heightChange",hintChange:"hintChange",hoverStateEnabledChange:"hoverStateEnabledChange",maxHeightChange:"maxHeightChange",maxWidthChange:"maxWidthChange",minHeightChange:"minHeightChange",minWidthChange:"minWidthChange",positionChange:"positionChange",resizeEnabledChange:"resizeEnabledChange",rtlEnabledChange:"rtlEnabledChange",shadingChange:"shadingChange",shadingColorChange:"shadingColorChange",showCloseButtonChange:"showCloseButtonChange",showTitleChange:"showTitleChange",tabIndexChange:"tabIndexChange",titleChange:"titleChange",titleTemplateChange:"titleTemplateChange",toolbarItemsChange:"toolbarItemsChange",visibleChange:"visibleChange",widthChange:"widthChange"},features:[r["\u0275\u0275ProvidersFeature"]([o.e,o.j,o.i,o.g]),r["\u0275\u0275InheritDefinitionFeature"],r["\u0275\u0275NgOnChangesFeature"]],ngContentSelectors:l,decls:1,vars:0,template:function(e,t){1&e&&(r["\u0275\u0275projectionDef"](),r["\u0275\u0275projection"](0))},encapsulation:2}),e})(),h=(()=>{let e=class{};return e.\u0275mod=r["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=r["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[a.bb,a.Gc,a.Vd,a.vd,a.hb,a.lb,a.sb,a.id,a.jd,a.M,o.c,o.f,n.b],a.bb,a.Gc,a.Vd,a.vd,a.hb,a.lb,a.sb,a.id,a.jd,a.M,o.f]}),e})()}}]);