(window.webpackJsonp=window.webpackJsonp||[]).push([[642],{Ejrh:function(e,t,n){"use strict";n.r(t),n.d(t,"RrProjectionsReportModule",(function(){return re}));var i=n("ofXK"),o=n("tyNb"),r=n("mrSG"),s=n("ZzPI"),a=n("3Pt+"),c=n("0IaG"),l=n("fXoL"),d=n("dNgK"),m=n("JqCM"),p=n("Hzim"),u=n("kmnG"),h=n("qFsG");function v(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"mat-hint",16),l["\u0275\u0275text"](1," Version name cannot exceed 25 characters. "),l["\u0275\u0275elementEnd"]())}let g=(()=>{class e{constructor(e,t,n,i,o){this.dialogRef=e,this.data=t,this.snackBar=n,this._spinnerService=i,this._rrProjectionsService=o,this.versionName=new a.j("",[a.H.maxLength(25)])}ngOnInit(){}saveLiveVersion(){return Object(r.c)(this,void 0,void 0,(function*(){if(!this.versionName.valid)return this.snackBar.open("Please enter version name !","Dismiss",{duration:2e3});console.log("saveLiveVersion"),console.log(this.versionName.value);let e=yield this._rrProjectionsService.saveLiveVersion(this.versionName.value);this.snackBar.open(e.messText,"Dismiss",{duration:2e3}),this.dialogRef.close("saved")}))}cancel(){this.dialogRef.close()}}return e.\u0275fac=function(t){return new(t||e)(l["\u0275\u0275directiveInject"](c.h),l["\u0275\u0275directiveInject"](c.a),l["\u0275\u0275directiveInject"](d.a),l["\u0275\u0275directiveInject"](m.c),l["\u0275\u0275directiveInject"](p.a))},e.\u0275cmp=l["\u0275\u0275defineComponent"]({type:e,selectors:[["app-save-live-version"]],decls:24,vars:4,consts:[[1,"container-fluid","temporary-version-dialog"],[1,"row","mt-4"],[1,"col","header"],[1,"row","mt-3",2,"font-size","16px","color","#526179"],[1,"col"],[1,"row","mt-1"],[1,"col-12"],["appearance","outline",2,"width","100%"],["matInput","","placeholder","Type here",3,"formControl"],["class","mt-1 mat-hint",4,"ngIf"],["align","end",1,"mt-2"],[1,"row","mt-2"],[1,"col","mt-3"],[1,"cancel-btn",3,"click"],[1,"ml-2","save-btn",3,"disabled","click"],["size","medium","type","ball-clip-rotate","bdColor","rgba(236, 233, 233, 0.8)","color","#FF0000"],[1,"mt-1","mat-hint"]],template:function(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"div",0),l["\u0275\u0275elementStart"](1,"div",1),l["\u0275\u0275elementStart"](2,"div",2),l["\u0275\u0275text"](3," Save Live Version "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](4,"div",3),l["\u0275\u0275elementStart"](5,"div",4),l["\u0275\u0275elementStart"](6,"span"),l["\u0275\u0275text"](7,"Title"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](8,"div",5),l["\u0275\u0275elementStart"](9,"div",6),l["\u0275\u0275elementStart"](10,"mat-form-field",7),l["\u0275\u0275element"](11,"input",8),l["\u0275\u0275template"](12,v,2,0,"mat-hint",9),l["\u0275\u0275elementStart"](13,"mat-hint",10),l["\u0275\u0275text"](14),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](15,"div",11),l["\u0275\u0275elementStart"](16,"div",12),l["\u0275\u0275elementStart"](17,"button",13),l["\u0275\u0275listener"]("click",(function(){return t.cancel()})),l["\u0275\u0275elementStart"](18,"span"),l["\u0275\u0275text"](19,"Cancel"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](20,"button",14),l["\u0275\u0275listener"]("click",(function(){return t.saveLiveVersion()})),l["\u0275\u0275elementStart"](21,"span"),l["\u0275\u0275text"](22,"Save"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275element"](23,"ngx-spinner",15)),2&e&&(l["\u0275\u0275advance"](11),l["\u0275\u0275property"]("formControl",t.versionName),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",t.versionName.invalid&&(t.versionName.touched||t.versionName.dirty)),l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate1"]("",t.versionName.value.length,"/25"),l["\u0275\u0275advance"](6),l["\u0275\u0275property"]("disabled",!t.versionName.valid))},directives:[u.c,h.b,a.e,a.v,a.k,i.NgIf,u.f,m.a],styles:[".temporary-version-dialog[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{font-size:16px;color:#526179;font-weight:600}.temporary-version-dialog[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%]{font-size:14px}.temporary-version-dialog[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{border:2px solid #526179;border-radius:4px;color:#526179;background:#fff;width:60px;height:35px;cursor:pointer}.temporary-version-dialog[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%]{border:2px solid red;border-radius:4px;color:#fff;background:#ee4961;width:60px;height:35px;cursor:pointer}.temporary-version-dialog[_ngcontent-%COMP%]   .mat-hint[_ngcontent-%COMP%]{color:red;padding-top:6px;font-size:10px}"]}),e})();var f=n("PSD3"),x=n.n(f),S=n("xG9w"),y=n("1G5W"),b=n("Kj3r"),P=n("XNiG"),j=n("NFeN"),C=n("Qu3c"),I=n("8hBH"),E=n("STbY"),_=n("bTqV"),w=n("6t9p"),R=n("d3UM"),k=n("FKr1"),V=n("dlKe");const M=["MenuTrigger"],F=["menu"],O=["openMenu"],L=["onScroll"];function B(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"mat-option",64),l["\u0275\u0275elementStart"](1,"span"),l["\u0275\u0275text"](2),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](3,"span"),l["\u0275\u0275text"](4),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;l["\u0275\u0275property"]("value",e.id),l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate"](e.id),l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate1"]("- ",e.name,"")}}function A(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"mat-error",65),l["\u0275\u0275text"](1," Project is required "),l["\u0275\u0275elementEnd"]())}function T(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",52),l["\u0275\u0275elementStart"](1,"mat-form-field",53),l["\u0275\u0275elementStart"](2,"mat-label"),l["\u0275\u0275text"](3,"Project"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](4,"mat-select",54,55),l["\u0275\u0275listener"]("openedChange",(function(t){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"]().onOpenedChange(t,"onScrollProjectList")}))("selectionChange",(function(t){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"]().onDataChange(t)})),l["\u0275\u0275elementStart"](6,"div",56),l["\u0275\u0275elementStart"](7,"span",57),l["\u0275\u0275elementStart"](8,"mat-icon",58),l["\u0275\u0275text"](9,"search"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](10,"input",59),l["\u0275\u0275listener"]("keydown",(function(e){return e.stopPropagation()})),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](11,"span",60),l["\u0275\u0275listener"]("click",(function(){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"]().cancelSearchValue()})),l["\u0275\u0275elementStart"](12,"mat-icon",61),l["\u0275\u0275text"](13,"highlight_off"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](14,B,5,3,"mat-option",62),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](15,A,2,0,"mat-error",63),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&e){const e=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](4),l["\u0275\u0275property"]("formControl",e.selectedValCtrl),l["\u0275\u0275advance"](6),l["\u0275\u0275property"]("formControl",e.searchCtrl),l["\u0275\u0275advance"](4),l["\u0275\u0275property"]("ngForOf",e.list),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",e.selectedValCtrl.invalid)}}function D(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"mat-icon",68),l["\u0275\u0275text"](1,"flag"),l["\u0275\u0275elementEnd"]())}function z(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",69),l["\u0275\u0275elementStart"](1,"div",70),l["\u0275\u0275listener"]("click",(function(){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"](2).changeToCurrentVersion()})),l["\u0275\u0275text"](2,"SET"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}}function N(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"span"),l["\u0275\u0275template"](1,D,2,0,"mat-icon",66),l["\u0275\u0275template"](2,z,3,0,"div",67),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",1==e.isCurrentVersion),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",0==e.isCurrentVersion)}}function $(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",71),l["\u0275\u0275listener"]("click",(function(){l["\u0275\u0275restoreView"](e);const t=l["\u0275\u0275nextContext"]();return t.fetchReportData(4,t.activeVersion[0].version_id,"active"),t.setTitle(t.activeVersion[0])})),l["\u0275\u0275elementStart"](1,"div",72),l["\u0275\u0275text"](2),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](3,"div",73),l["\u0275\u0275elementStart"](4,"div",74),l["\u0275\u0275text"](5),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&e){const e=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("matTooltip",e.activeVersion[0].title),l["\u0275\u0275advance"](1),l["\u0275\u0275textInterpolate"](e.activeVersion[0].title),l["\u0275\u0275advance"](3),l["\u0275\u0275textInterpolate1"](" ",e.activeVersion[0].changed_on," ")}}function H(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"div",75),l["\u0275\u0275text"](1," No Current Version Found "),l["\u0275\u0275elementEnd"]())}function Q(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",78),l["\u0275\u0275listener"]("click",(function(){l["\u0275\u0275restoreView"](e);const n=t.$implicit,i=l["\u0275\u0275nextContext"](2),o=l["\u0275\u0275reference"](34);return i.fetchReport(4,n.version_id,"active",o),i.setTitle(n)})),l["\u0275\u0275elementStart"](1,"div",72),l["\u0275\u0275text"](2),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](3,"div",73),l["\u0275\u0275elementStart"](4,"div",74),l["\u0275\u0275text"](5),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("matTooltip",e.title),l["\u0275\u0275advance"](1),l["\u0275\u0275textInterpolate"](e.title),l["\u0275\u0275advance"](3),l["\u0275\u0275textInterpolate1"](" ",e.changed_on," ")}}function W(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div"),l["\u0275\u0275elementStart"](1,"div",76),l["\u0275\u0275listener"]("scrolled",(function(){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"]().onScroll()})),l["\u0275\u0275template"](2,Q,6,3,"div",77),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&e){const e=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("infiniteScrollDistance",2)("scrollWindow",!1),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngForOf",e.menuItems)}}function q(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"div",75),l["\u0275\u0275text"](1," No Active Version Found "),l["\u0275\u0275elementEnd"]())}function G(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"button",30),l["\u0275\u0275listener"]("click",(function(){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"]().runOptionClicked("Isa")})),l["\u0275\u0275text"](1,"Internal Stakeholder "),l["\u0275\u0275elementStart"](2,"button",31),l["\u0275\u0275elementStart"](3,"mat-icon",79),l["\u0275\u0275text"](4,"info"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}}function K(e,t){if(1&e&&l["\u0275\u0275element"](0,"dxi-column",80),2&e){const e=t.$implicit;l["\u0275\u0275property"]("dataField",e.report_column)("allowReordering",!0)("caption",e.display_name)("visible",e.is_visible)("allowResizing",!1)}}function J(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"dxo-summary"),l["\u0275\u0275element"](1,"dxi-total-item",81),l["\u0275\u0275element"](2,"dxi-total-item",82),l["\u0275\u0275element"](3,"dxi-total-item",83),l["\u0275\u0275element"](4,"dxi-total-item",84),l["\u0275\u0275elementEnd"]())}function U(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"dxo-summary"),l["\u0275\u0275element"](1,"dxi-total-item",81),l["\u0275\u0275element"](2,"dxi-total-item",82),l["\u0275\u0275element"](3,"dxi-total-item",83),l["\u0275\u0275element"](4,"dxi-total-item",84),l["\u0275\u0275elementEnd"]())}function X(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"dxo-summary"),l["\u0275\u0275element"](1,"dxi-total-item",85),l["\u0275\u0275elementEnd"]())}function Y(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"dxo-summary"),l["\u0275\u0275element"](1,"dxi-total-item",81),l["\u0275\u0275elementEnd"]())}function Z(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"dxo-summary"),l["\u0275\u0275element"](1,"dxi-total-item",81),l["\u0275\u0275elementEnd"]())}const ee=function(e){return{"red-icon":e}},te=[{path:"",component:(()=>{class e{constructor(e,t,n,i,o){this._rrProjectionsService=e,this._spinnerService=t,this._snackBar=n,this.dialog=i,this.$route=o,this.startIndex=0,this.offSet=10,this.menuItems=[],this.progressPercentage=0,this.isCurrentVersion=!1,this.activeVersion=[],this.IsRRIsaBasedManualRunCheckAccess=!1,this.startIndexForProjectList=0,this.offSetForProjectList=50,this.list=[],this.searchCtrl=new a.j,this.selectedValCtrl=new a.j(null,[a.H.required]),this._onDestroy=new P.b}ngOnInit(){return Object(r.c)(this,void 0,void 0,(function*(){console.log("aaaaaaaaaaaaaaaaaaaaaaaa"),yield this.fetchData(),this.list.length>0&&this.selectedValCtrl.patchValue(this.list[0].id),yield this.fetchReportData(1,0,"live"),yield this.getActiveVersion(),this.menuItems=yield this.fetchVersionMaster(this.startIndex,this.offSet),console.log(this.menuItems),this.rrIsaBasedManualRunCheckAccess(),this.searchCtrl.valueChanges.pipe(Object(y.a)(this._onDestroy),Object(b.a)(700)).subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){null!=e&&""!=e&&(this.list=[],this.startIndexForProjectList=0,this.offSetForProjectList=50,yield this.fetchData())})))}))}getActiveVersion(){return new Promise((e,t)=>{this._rrProjectionsService.getActiveVersion().subscribe(t=>{this.activeVersion=t.data,console.log(this.activeVersion),e(t.data)},e=>{t(e)})})}getColumnConfigForRRReport(e){return new Promise((t,n)=>{this._rrProjectionsService.getColumnConfigForRRReport(e).subscribe(e=>{t(e.data)},e=>{n(e)})})}fetchReportData(e,t,n){var i;return Object(r.c)(this,void 0,void 0,(function*(){this.reportId=e,this.versionId=t,this.type=n,this.isCurrentVersion=!1,this.rrProjectionsReportColConfig=yield this.getColumnConfigForRRReport(e),this.reportColumnsDefinitions=[],console.log(this.reportColumnsDefinitions);let o=null===(i=this.rrProjectionsReportColConfig[0])||void 0===i?void 0:i.column_config;console.log(o);for(let e of o)this.reportColumnsDefinitions.push({report_column:e.valueExpr,display_name:e.column,is_visible:e.isVisible});this.loadData(e,t,n)}))}getEmpRevenueProjectionLive(e){return new Promise((t,n)=>{this._rrProjectionsService.getEmpRevenueProjectionLive(e).subscribe(e=>{t(e.data),this._spinnerService.hide()},e=>{this._spinnerService.hide(),this._snackBar.open("Failed to retrieve employee revenue projection live records","Dismiss",{duration:2e3}),n(e)})})}getEmpRevenueProjectionInactive(){return new Promise((e,t)=>{this._rrProjectionsService.getEmpRevenueProjectionInactive().subscribe(t=>{e(t.data),this._spinnerService.hide()},e=>{this._spinnerService.hide(),this._snackBar.open("Failed to retrieve employee revenue projection inactive records","Dismiss",{duration:2e3}),t(e)})})}getEmpRevenueProjectionVersionHistory(){return new Promise((e,t)=>{this._rrProjectionsService.getEmpRevenueProjectionVersionHistory().subscribe(t=>{e(t.data),this._spinnerService.hide()},e=>{this._spinnerService.hide(),this._snackBar.open("Failed to retrieve employee revenue projection version history","Dismiss",{duration:2e3}),t(e)})})}getPositionBasedProjection(){return new Promise((e,t)=>{this._rrProjectionsService.getPositionBasedProjection(this.selectedValCtrl.value).subscribe(t=>{e(t.data),this._spinnerService.hide()},e=>{this._spinnerService.hide(),this._snackBar.open("Failed to retrieve position based revenue projection records","Dismiss",{duration:2e3}),t(e)})})}getEmpRevenueProjectionActiveVersion(e){return new Promise((t,n)=>{this._rrProjectionsService.getEmpRevenueProjectionActiveVersion(e).subscribe(e=>{t(e.data),this._spinnerService.hide()},e=>{this._spinnerService.hide(),this._snackBar.open("Failed to retrieve employee revenue projection active version","Dismiss",{duration:2e3}),n(e)})})}getEmpProjectionErrorLogsHistory(){return new Promise((e,t)=>{this._rrProjectionsService.getEmpProjectionErrorLogsHistory().subscribe(t=>{e(t.data),this._spinnerService.hide()},e=>{this._spinnerService.hide(),this._snackBar.open("Failed to retrieve employee projection error logs history","Dismiss",{duration:2e3}),t(e)})})}getProjectionRunLogs(){return new Promise((e,t)=>{this._rrProjectionsService.getProjectionRunLogs(this.selectedValCtrl.value).subscribe(t=>{e(t.data),this._spinnerService.hide()},e=>{this._spinnerService.hide(),this._snackBar.open("Failed to retrieve projection logs history","Dismiss",{duration:2e3}),t(e)})})}getEmpProjectionManualRunHistory(){return new Promise((e,t)=>{this._rrProjectionsService.getEmpProjectionManualRunHistory().subscribe(t=>{e(t.data),this._spinnerService.hide()},e=>{this._spinnerService.hide(),this._snackBar.open("Failed to retrieve employee projection manual log log history","Dismiss",{duration:2e3}),t(e)})})}fetchVersionMaster(e,t){return new Promise((n,i)=>{this._rrProjectionsService.getMasterVersions(e,t).subscribe(e=>{n(e.data)},e=>{i(e)})})}fetchVersionMasterOnSave(){return Object(r.c)(this,void 0,void 0,(function*(){this.menuItems=[],yield this.getActiveVersion();let e=yield this.fetchVersionMaster(this.startIndex,this.offSet);this.menuItems=this.menuItems.concat(e);let t=S.filter(this.activeVersion,{version_id:this.versionId});console.log("activeVersion"),console.log(this.activeVersion),console.log(this.versionId),console.log("element"),console.log(t),t.length>0&&this.setTitle(t[0])}))}onScroll(){return Object(r.c)(this,void 0,void 0,(function*(){this.startIndex=this.startIndex+this.offSet;let e=yield this.fetchVersionMaster(this.startIndex,this.offSet);this.menuItems=this.menuItems.concat(e)}))}menuItemsData(){0==this.menuItems.length&&this._snackBar.open("No Versions Found","Dismiss",{duration:2e3})}loadData(e,t,n){return Object(r.c)(this,void 0,void 0,(function*(){1==e&&(this.isActiveIcon=1,this.title="Employee Revenue Projection Live",this.dataSource=yield this.getEmpRevenueProjectionLive(this.selectedValCtrl.value)),2==e&&(this.isActiveIcon=2,this.title="Employee Revenue Projection Inactive",this.dataSource=yield this.getEmpRevenueProjectionInactive()),3==e&&(this.isActiveIcon=3,this.title="Employee Revenue Projection Version History",this.dataSource=yield this.getEmpRevenueProjectionVersionHistory()),7==e&&(this.isActiveIcon=7,this.title="Quote Position Based Revenue Projection",this.dataSource=yield this.getPositionBasedProjection()),"active"==n&&(this.isActiveIcon=4,this.dataSource=yield this.getEmpRevenueProjectionActiveVersion(t)),6==e&&(this.isActiveIcon=6,this.title="Internal Stakeholders Projection Update Report",this.dataSource=yield this.getEmpProjectionErrorLogsHistory()),5==e&&(this.isActiveIcon=5,this.title="Employee Projection Manual Run Logs",this.dataSource=yield this.getEmpProjectionManualRunHistory()),8==e&&(this.isActiveIcon=8,this.title="RR Projection Logs",this.dataSource=yield this.getProjectionRunLogs())}))}setTitle(e){this.title=e.title,this.isCurrentVersion=1==e.is_current_version}getTotalRecords(e){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>{this._rrProjectionsService.getTotalRecords(e).subscribe(e=>{t(e.data),this._spinnerService.hide()},e=>{this._spinnerService.hide(),this._snackBar.open("Failed to retrieve employee revenue projection inactive records","Dismiss",{duration:2e3}),n(e)})})}))}openLiveVersionSaveDialog(){return Object(r.c)(this,void 0,void 0,(function*(){(yield this.dialog.open(g,{height:"32%",width:"30%",data:{}})).afterClosed().subscribe(e=>{"saved"===e&&(this.startIndex=0,this.offSet=10,this.fetchVersionMasterOnSave())})}))}changeToCurrentVersion(){return Object(r.c)(this,void 0,void 0,(function*(){console.log("changeToCurrentVersion");let e=yield this._rrProjectionsService.changeSavedVersionToCurrentVersion(this.versionId);this._snackBar.open(e.messText,"Dismiss",{duration:2e3}),this.startIndex=0,this.offSet=10,this.fetchVersionMasterOnSave()}))}runOptionClicked(e){return Object(r.c)(this,void 0,void 0,(function*(){if("All"==e)x.a.fire({title:"Do you want to run projections for all projects?",icon:"info",showCancelButton:!0,cancelButtonText:"No",confirmButtonText:"Yes",cancelButtonColor:"#aca8a8",confirmButtonColor:"#EE4961",allowOutsideClick:!1,allowEscapeKey:!1}).then(e=>{e.isConfirmed&&this.runAllClicked()});else{const{RunInternalStakeholdersComponent:t}=yield n.e(315).then(n.bind(null,"Wwka"));this.dialog.open(t,{width:"30%",height:"30%",autoFocus:!1,maxWidth:"90vw",data:{type:e}}).afterClosed().subscribe(t=>{console.log("Dialog result:",t),t&&null!=t&&this.insertProjetionLiveManualRun(e,t)})}}))}insertProjetionLiveManualRun(e,t){return Object(r.c)(this,void 0,void 0,(function*(){if(t){if("Projects"==e){let e=yield this._rrProjectionsService.insertRRProjectionsLive("Portfolio",t);this._snackBar.open(e.messText,"Dismiss",{duration:2e3}),this.fetchReportData(5,0,"manual_run")}if("Items"==e){let e=yield this._rrProjectionsService.insertRRProjectionsLive("Project",t);this._snackBar.open(e.messText,"Dismiss",{duration:2e3}),this.fetchReportData(5,0,"manual_run")}if("Isa"==e){let e=yield this._rrProjectionsService.insertRRProjectionsLive("Internal Stakeholder",t);this._snackBar.open(e.messText,"Dismiss",{duration:2e3}),this.fetchReportData(5,0,"manual_run")}if("Billing Advice"==e){let e=yield this._rrProjectionsService.insertRRProjectionsLive("Billing Advice",t);this._snackBar.open(e.messText,"Dismiss",{duration:2e3}),this.fetchReportData(5,0,"manual_run")}if("Billed Invoices"==e){let e=yield this._rrProjectionsService.insertRRProjectionsLive("Billed Invoices",t);this._snackBar.open(e.messText,"Dismiss",{duration:2e3}),this.fetchReportData(5,0,"manual_run")}}}))}runAllClicked(){return Object(r.c)(this,void 0,void 0,(function*(){let e=yield this._rrProjectionsService.insertRRProjectionsLive("All",0);this._snackBar.open(e.messText,"Dismiss",{duration:2e3}),this.fetchReportData(5,0,"manual_run"),console.log("run all clicked")}))}fetchReport(e,t,n,i){i.close(),this.fetchReportData(e,t,n)}onCloseReport(){this.$route.navigateByUrl("/main/reports")}rrIsaBasedManualRunCheckAccess(){this.IsRRIsaBasedManualRunCheckAccess=this._rrProjectionsService.rrIsaBasedManualRunCheckAccess()}onOpenedChange(e,t){e&&this[t].panel.nativeElement.addEventListener("scroll",e=>{let n=Math.ceil(this[t].panel.nativeElement.scrollTop),i=this[t].panel.nativeElement.scrollHeight-this[t].panel.nativeElement.offsetHeight;n-1!=i&&n!=i&&n+1!=i||(this.startIndexForProjectList=this.startIndexForProjectList+this.offSetForProjectList,this.fetchData())})}fetchData(){return Object(r.c)(this,void 0,void 0,(function*(){let e=yield this._rrProjectionsService.getItemList(this.searchCtrl.value,this.startIndexForProjectList,this.offSetForProjectList);this.list=this.list.concat(e.data),console.log("list proj"),console.log(this.list)}))}cancelSearchValue(){this.searchCtrl.patchValue(""),this.startIndexForProjectList=0,this.offSetForProjectList=50,this.fetchData()}onDataChange(e){return Object(r.c)(this,void 0,void 0,(function*(){this.selectedValCtrl.valid&&(1==this.reportId&&(this.isActiveIcon=1,this.title="Employee Revenue Projection Live",this.dataSource=yield this.getEmpRevenueProjectionLive(this.selectedValCtrl.value)),7==this.reportId&&(this.isActiveIcon=7,this.title="Quote Position Based Revenue Projection",this.dataSource=yield this.getPositionBasedProjection()),8==this.reportId&&(this.isActiveIcon=8,this.title="RR Projection Logs",this.dataSource=yield this.getProjectionRunLogs()))}))}}return e.\u0275fac=function(t){return new(t||e)(l["\u0275\u0275directiveInject"](p.a),l["\u0275\u0275directiveInject"](m.c),l["\u0275\u0275directiveInject"](d.a),l["\u0275\u0275directiveInject"](c.b),l["\u0275\u0275directiveInject"](o.g))},e.\u0275cmp=l["\u0275\u0275defineComponent"]({type:e,selectors:[["app-landing-page"]],viewQuery:function(e,t){if(1&e&&(l["\u0275\u0275viewQuery"](M,!0),l["\u0275\u0275viewQuery"](F,!0),l["\u0275\u0275viewQuery"](O,!0),l["\u0275\u0275viewQuery"](s.a,!0),l["\u0275\u0275viewQuery"](L,!0)),2&e){let e;l["\u0275\u0275queryRefresh"](e=l["\u0275\u0275loadQuery"]())&&(t.MenuTrigger=e.first),l["\u0275\u0275queryRefresh"](e=l["\u0275\u0275loadQuery"]())&&(t.menuTrigger=e.first),l["\u0275\u0275queryRefresh"](e=l["\u0275\u0275loadQuery"]())&&(t.openMenu=e.first),l["\u0275\u0275queryRefresh"](e=l["\u0275\u0275loadQuery"]())&&(t.dataGrid=e.first),l["\u0275\u0275queryRefresh"](e=l["\u0275\u0275loadQuery"]())&&(t.onScrollProjectList=e.first)}},decls:93,vars:56,consts:[[1,"container-fluid"],[1,"row"],[1,"col-7","d-flex","justify-content"],[1,"mt-3","backButton",3,"click"],[1,"arrow_icon_back"],[1,"pr-2"],["class","d-flex ml-3 mt-2 mr-2",4,"ngIf"],[1,"mt-3","ml-3",2,"color","#1b2140","font-size","17px","font-weight","500","letter-spacing",".32px"],[4,"ngIf"],[1,"col-5"],[1,"tool-bar",2,"width","100%"],["matTooltip","Live Version",3,"click"],[1,"mb-1","ml-1","mr-1",2,"font-size","20px",3,"ngClass"],["matTooltip","Quote Position Based Projection",3,"click"],["matTooltip","Save",3,"click"],[1,"mb-1","ml-1","mr-1",2,"font-size","20px"],["matTooltip","Inactive History",3,"click"],["matTooltip","Version History",3,"click"],["matTooltip","Saved Version",3,"click"],[1,"mb-1","ml-1","mr-1",2,"font-size","20px",3,"satPopoverAnchor","ngClass"],["horizontalAlign","center","verticalAlign","below","hasBackdrop",""],["insertBelow",""],[1,"card"],[1,"row","version-header"],["class","row active-version","style","padding-left: 12px; cursor: pointer;",3,"click",4,"ngIf"],["class","row active-version","style","padding-left: 11px; color: grey;font-size: 13px;",4,"ngIf"],[1,"row","showHistory"],[1,"col-12","p-0","pl-3"],["matTooltip","Run Projections",3,"matMenuTriggerFor"],["runMenu","matMenu"],["mat-menu-item","",2,"color","#6E7B8F",3,"click"],["mat-icon-button","",1,"trend-button-inactive","my-auto"],["matTooltip","Run Projections for Internal Stakeholders of a Specific Project",2,"color","grey","font-size","15px"],["mat-menu-item","","style","color: #6E7B8F;",3,"click",4,"ngIf"],["matTooltip","Run Projections for a specific milestone",2,"color","grey","font-size","15px"],["matTooltip","Manual Run Logs",3,"click"],["matTooltip","Internal Stakeholders Projection Update Report",3,"click"],["matTooltip","RR Projection Logs",3,"click"],[1,"row",2,"background","white"],[1,"col-12"],["id","gridContainer"],["id","gridContainer",1,"dev-style",3,"allowColumnResizing","dataSource","showBorders","hoverStateEnabled","columnAutoWidth","allowColumnReordering"],["placeholder","Search...",3,"visible"],["mode","select",3,"enabled"],[3,"enabled"],["mode","single"],[3,"visible"],[3,"pageSize","pageIndex"],[3,"enabled","fileName"],[3,"dataField","allowReordering","caption","visible","allowResizing",4,"ngFor","ngForOf"],["bdColor","rgba(0, 0, 0, 0)","size","medium","color","#FF0000","type","ball-clip-rotate",3,"fullScreen"],[2,"color","#FF0000","margin-top","10vh !important","font-weight","400"],[1,"d-flex","ml-3","mt-2","mr-2"],["appearance","outline",1,"full-width","mr-3"],["placeholder","Search",3,"formControl","openedChange","selectionChange"],["onScrollProjectList",""],[2,"width","100%"],[1,"pl-1","icon-search"],["matPrefix",""],["placeholder","Search","noEntriesFoundLabel","No results found",1,"p-3","pl-0",2,"width","75%",3,"formControl","keydown"],[1,"icon-search-cancel",3,"click"],["matSuffix","",1,"mr-1"],[3,"value",4,"ngFor","ngForOf"],["class","mt-2",4,"ngIf"],[3,"value"],[1,"mt-2"],["class","ml-2 pt-1 mt-2 current-flag-icon","matTooltip","Current Version","style","vertical-align: bottom;",4,"ngIf"],["class","ml-2 mt-3 active-button","style","cursor: pointer;","matTooltip","Set as Current Version",4,"ngIf"],["matTooltip","Current Version",1,"ml-2","pt-1","mt-2","current-flag-icon",2,"vertical-align","bottom"],["matTooltip","Set as Current Version",1,"ml-2","mt-3","active-button",2,"cursor","pointer"],[1,"active-button-text",2,"font-size","11px",3,"click"],[1,"row","active-version",2,"padding-left","12px","cursor","pointer",3,"click"],[1,"version-history-text",3,"matTooltip"],[1,"date-space",2,"display","flex","align-items","center","justify-content","center","margin-left","10px"],[2,"background","whitesmoke","color","grey","font-weight","500","font-size","11px","border-radius","10px","padding-left","5px","padding-right","5px"],[1,"row","active-version",2,"padding-left","11px","color","grey","font-size","13px"],["infinite-scroll","",2,"max-height","300px","overflow-y","auto","overflow-x","hidden",3,"infiniteScrollDistance","scrollWindow","scrolled"],["class","row border-bottom export-class pt-1 pb-1",3,"click",4,"ngFor","ngForOf"],[1,"row","border-bottom","export-class","pt-1","pb-1",3,"click"],["matTooltip","Run Projections for a Specific Internal Stakeholder ID",2,"color","grey","font-size","15px"],[3,"dataField","allowReordering","caption","visible","allowResizing"],["column","id","summaryType","count"],["column","total_revenue","summaryType","sum"],["column","total_cost","summaryType","sum"],["column","working_hours","summaryType","sum"],["column","version_id","summaryType","count"]],template:function(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",0),l["\u0275\u0275elementStart"](1,"div",1),l["\u0275\u0275elementStart"](2,"div",2),l["\u0275\u0275elementStart"](3,"button",3),l["\u0275\u0275listener"]("click",(function(){return t.onCloseReport()})),l["\u0275\u0275elementStart"](4,"mat-icon",4),l["\u0275\u0275text"](5,"keyboard_arrow_left"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](6,"span",5),l["\u0275\u0275text"](7,"Back"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](8,T,16,4,"div",6),l["\u0275\u0275elementStart"](9,"span",7),l["\u0275\u0275text"](10),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](11,N,3,2,"span",8),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](12,"div",9),l["\u0275\u0275elementStart"](13,"table",10),l["\u0275\u0275elementStart"](14,"tr"),l["\u0275\u0275elementStart"](15,"td",11),l["\u0275\u0275listener"]("click",(function(){return t.fetchReportData(1,0,"live")})),l["\u0275\u0275elementStart"](16,"mat-icon",12),l["\u0275\u0275text"](17,"stacked_line_chart"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](18,"td",13),l["\u0275\u0275listener"]("click",(function(){return t.fetchReportData(7,0,"positionBased")})),l["\u0275\u0275elementStart"](19,"mat-icon",12),l["\u0275\u0275text"](20,"bar_chart"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](21,"td",14),l["\u0275\u0275listener"]("click",(function(){return t.openLiveVersionSaveDialog()})),l["\u0275\u0275elementStart"](22,"mat-icon",15),l["\u0275\u0275text"](23,"save"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](24,"td",16),l["\u0275\u0275listener"]("click",(function(){return t.fetchReportData(2,0,"inactive")})),l["\u0275\u0275elementStart"](25,"mat-icon",12),l["\u0275\u0275text"](26,"remove_circle"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](27,"td",17),l["\u0275\u0275listener"]("click",(function(){return t.fetchReportData(3,0,"history")})),l["\u0275\u0275elementStart"](28,"mat-icon",12),l["\u0275\u0275text"](29,"history"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](30,"td",18),l["\u0275\u0275listener"]("click",(function(){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275reference"](34).toggle()})),l["\u0275\u0275elementStart"](31,"mat-icon",19),l["\u0275\u0275text"](32,"menu_open"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](33,"sat-popover",20,21),l["\u0275\u0275elementStart"](35,"div",22),l["\u0275\u0275elementStart"](36,"div",23),l["\u0275\u0275text"](37," Current Version "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](38,$,6,3,"div",24),l["\u0275\u0275template"](39,H,2,0,"div",25),l["\u0275\u0275elementStart"](40,"div",23),l["\u0275\u0275text"](41," Active Version "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](42,"div",26),l["\u0275\u0275elementStart"](43,"div",27),l["\u0275\u0275elementStart"](44,"div"),l["\u0275\u0275template"](45,W,3,3,"div",8),l["\u0275\u0275template"](46,q,2,0,"div",25),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](47,"td",28),l["\u0275\u0275elementStart"](48,"mat-icon",15),l["\u0275\u0275text"](49,"play_arrow"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](50,"mat-menu",null,29),l["\u0275\u0275elementStart"](52,"button",30),l["\u0275\u0275listener"]("click",(function(){return t.runOptionClicked("Items")})),l["\u0275\u0275text"](53,"Project "),l["\u0275\u0275elementStart"](54,"button",31),l["\u0275\u0275elementStart"](55,"mat-icon",32),l["\u0275\u0275text"](56,"info"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](57,G,5,0,"button",33),l["\u0275\u0275elementStart"](58,"button",30),l["\u0275\u0275listener"]("click",(function(){return t.runOptionClicked("Billing Advice")})),l["\u0275\u0275text"](59,"Billing Advice Projections "),l["\u0275\u0275elementStart"](60,"button",31),l["\u0275\u0275elementStart"](61,"mat-icon",34),l["\u0275\u0275text"](62,"info"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](63,"td",35),l["\u0275\u0275listener"]("click",(function(){return t.fetchReportData(5,0,"manual_run")})),l["\u0275\u0275elementStart"](64,"mat-icon",12),l["\u0275\u0275text"](65,"manage_history"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](66,"td",36),l["\u0275\u0275listener"]("click",(function(){return t.fetchReportData(6,0,"err_logs")})),l["\u0275\u0275elementStart"](67,"mat-icon",12),l["\u0275\u0275text"](68,"running_with_errors"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](69,"td",37),l["\u0275\u0275listener"]("click",(function(){return t.fetchReportData(8,0,"logs")})),l["\u0275\u0275elementStart"](70,"mat-icon",12),l["\u0275\u0275text"](71,"description"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](72,"div",38),l["\u0275\u0275elementStart"](73,"div",39),l["\u0275\u0275elementStart"](74,"div",40),l["\u0275\u0275elementStart"](75,"dx-data-grid",41),l["\u0275\u0275element"](76,"dxo-search-panel",42),l["\u0275\u0275element"](77,"dxo-column-chooser",43),l["\u0275\u0275element"](78,"dxo-column-fixing",44),l["\u0275\u0275element"](79,"dxo-selection",45),l["\u0275\u0275element"](80,"dxo-header-filter",46),l["\u0275\u0275element"](81,"dxo-filter-row",46),l["\u0275\u0275element"](82,"dxo-paging",47),l["\u0275\u0275element"](83,"dxo-export",48),l["\u0275\u0275template"](84,K,1,5,"dxi-column",49),l["\u0275\u0275template"](85,J,5,0,"dxo-summary",8),l["\u0275\u0275template"](86,U,5,0,"dxo-summary",8),l["\u0275\u0275template"](87,X,2,0,"dxo-summary",8),l["\u0275\u0275template"](88,Y,2,0,"dxo-summary",8),l["\u0275\u0275template"](89,Z,2,0,"dxo-summary",8),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](90,"ngx-spinner",50),l["\u0275\u0275elementStart"](91,"p",51),l["\u0275\u0275text"](92," Loading Data... "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&e){const e=l["\u0275\u0275reference"](34),n=l["\u0275\u0275reference"](51);l["\u0275\u0275advance"](8),l["\u0275\u0275property"]("ngIf",1==t.reportId||7==t.reportId||8==t.reportId),l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate"](t.title),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",4==t.isActiveIcon),l["\u0275\u0275advance"](5),l["\u0275\u0275property"]("ngClass",l["\u0275\u0275pureFunction1"](40,ee,1==t.isActiveIcon)),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("ngClass",l["\u0275\u0275pureFunction1"](42,ee,7==t.isActiveIcon)),l["\u0275\u0275advance"](6),l["\u0275\u0275property"]("ngClass",l["\u0275\u0275pureFunction1"](44,ee,2==t.isActiveIcon)),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("ngClass",l["\u0275\u0275pureFunction1"](46,ee,3==t.isActiveIcon)),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("satPopoverAnchor",e)("ngClass",l["\u0275\u0275pureFunction1"](48,ee,4==t.isActiveIcon)),l["\u0275\u0275advance"](7),l["\u0275\u0275property"]("ngIf",t.activeVersion.length>0),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",0==(null==t.activeVersion?null:t.activeVersion.length)),l["\u0275\u0275advance"](6),l["\u0275\u0275property"]("ngIf",t.menuItems.length>0),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",0==t.menuItems.length),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("matMenuTriggerFor",n),l["\u0275\u0275advance"](10),l["\u0275\u0275property"]("ngIf",1==t.IsRRIsaBasedManualRunCheckAccess),l["\u0275\u0275advance"](7),l["\u0275\u0275property"]("ngClass",l["\u0275\u0275pureFunction1"](50,ee,5==t.isActiveIcon)),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("ngClass",l["\u0275\u0275pureFunction1"](52,ee,6==t.isActiveIcon)),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("ngClass",l["\u0275\u0275pureFunction1"](54,ee,8==t.isActiveIcon)),l["\u0275\u0275advance"](5),l["\u0275\u0275property"]("allowColumnResizing",!0)("dataSource",t.dataSource)("showBorders",!0)("hoverStateEnabled",!0)("columnAutoWidth",!0)("allowColumnReordering",!0),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("visible",!0),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("enabled",!0),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("enabled",!0),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("visible",!0),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("visible",!0),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("pageSize",10)("pageIndex",0),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("enabled",!0)("fileName",t.title),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngForOf",t.reportColumnsDefinitions),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",1==t.isActiveIcon),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",2==t.isActiveIcon),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",3==t.isActiveIcon),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",5==t.isActiveIcon),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",6==t.isActiveIcon),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("fullScreen",!1)}},directives:[j.a,i.NgIf,C.a,i.NgClass,I.b,I.a,E.f,E.g,E.d,_.a,s.a,w.Md,w.tb,w.vb,w.Od,w.Cc,w.dc,w.od,w.Sb,i.NgForOf,m.a,u.c,u.g,R.c,a.v,a.k,u.h,a.e,u.i,k.p,u.b,V.a,w.g,w.ve,w.N],styles:['.icon-container[_ngcontent-%COMP%]{display:flex;align-items:center}.icon-container[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-right:12%}.clicked-icon[_ngcontent-%COMP%]:after{content:"!";position:absolute;top:0;right:-7px;color:red;width:33px;height:15px;display:flex;justify-content:center;align-items:center;font-size:15px;font-weight:700}mat-progress-bar[_ngcontent-%COMP%]{height:2em}.progress-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;position:fixed;top:50%;left:50%;transform:translate(-50%,-50%);width:30%;z-index:1001}.progress-overlay[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100%;height:100%;background-color:hsla(0,0%,100%,0);z-index:1000}.current-flag-icon[_ngcontent-%COMP%]{color:#ee4961;font-size:26px}.red-icon[_ngcontent-%COMP%]{color:#ee4961}.icon-btn[_ngcontent-%COMP%]{background:#eae8e8;border-radius:4px;cursor:pointer}.view-button-inactive[_ngcontent-%COMP%]{margin-top:5px!important;line-height:8px;width:28px;height:28px;margin-right:10px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12);animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}.view-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.view-button-inactive[_ngcontent-%COMP%]   .iconButtonWarn[_ngcontent-%COMP%]{color:#cf0001;font-size:18px}.view-button-inactive[_ngcontent-%COMP%]   .iconButtonWarnAnim[_ngcontent-%COMP%]{color:#cf0001;font-size:18px;animation:blink 1.1s cubic-bezier(.5,0,1,1) infinite alternate}.tool-bar[_ngcontent-%COMP%]{border-collapse:collapse;width:auto;margin:12px auto 20px 15px}.tool-bar[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{border:1px solid hsla(0,3%,80.2%,.7215686274509804);text-align:center;cursor:pointer;color:hsla(0,0%,50.2%,.7215686274509804);font-size:12px;display:inline-flex;padding:6px 6px 0}.tool-bar[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:hover{background-color:#f5f5f5}.export-class[_ngcontent-%COMP%]{color:#dadce2;cursor:pointer;height:36px}.export-class[_ngcontent-%COMP%]:hover{color:#c5c6c9;cursor:pointer;height:36px}.version-history-text[_ngcontent-%COMP%]{font-size:13px!important;padding-top:6px;color:#6e7b8f;font-size:12px;font-weight:500;line-height:16px;letter-spacing:.02em;text-align:left;width:150px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.active-button[_ngcontent-%COMP%]{width:30px;height:20px;background:#ee4961!important;display:flex;align-items:center;justify-content:center;border-radius:5px;color:#fff;margin-top:4px;margin-left:6px}.custom-mat-menu[_ngcontent-%COMP%]{width:400px}.version-header[_ngcontent-%COMP%]{padding-left:12px;margin-top:10px;margin-bottom:5px;font-size:14px;font-weight:700}.active-version-set[_ngcontent-%COMP%]{height:250px!important;width:290px!important;overflow-y:auto}.backButton[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;padding:0;color:#8b95a5;border-radius:4px;background-color:#fff;font-weight:400;font-size:11px;line-height:16px;border:thin solid #8b95a5;cursor:pointer;height:22px}.arrow_icon_back[_ngcontent-%COMP%]{font-size:18px;align-items:center;display:inline-flex}.iconbtn[_ngcontent-%COMP%]{background-color:#c92020;color:#fff;padding:0;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}input[_ngcontent-%COMP%], input[_ngcontent-%COMP%]:focus{border:none;outline:none;caret-color:#cf0001}.icon-search[_ngcontent-%COMP%], .icon-search-cancel[_ngcontent-%COMP%]{color:#a1a1a2;font-size:20px;vertical-align:middle}.icon-search-cancel[_ngcontent-%COMP%]{cursor:pointer}']}),e})()}];let ne=(()=>{class e{}return e.\u0275mod=l["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=l["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[o.k.forChild(te)],o.k]}),e})();var ie=n("bv9b"),oe=n("WJ5W");let re=(()=>{class e{}return e.\u0275mod=l["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=l["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,ne,s.b,j.b,m.b,d.b,E.e,V.b,ie.b,_.b,u.e,a.p,h.c,C.b,a.E,R.d,oe.b,I.c]]}),e})()},Hzim:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var i=n("xG9w"),o=n("fXoL"),r=n("tk/3"),s=n("flaP");let a=(()=>{class e{constructor(e,t){this.$http=e,this.rolesService=t,this.getColumnConfigForRRReport=e=>this.$http.post("/api/misFunctions/getColumnConfigForRRReport",{reportId:e}),this.getEmpRevenueProjectionLive=e=>this.$http.post("/api/misFunctions/getEmpRevenueProjectionLive",{projectId:e}),this.getEmpRevenueProjectionInactive=()=>this.$http.post("/api/misFunctions/getEmpRevenueProjectionInactive",{}),this.getEmpRevenueProjectionActiveVersion=e=>this.$http.post("/api/misFunctions/getEmpRevenueProjectionActiveVersion",{versionId:e}),this.getEmpRevenueProjectionVersionHistory=()=>this.$http.post("/api/misFunctions/getEmpRevenueProjectionVersionHistory",{}),this.getMasterVersions=(e,t)=>this.$http.post("/api/misFunctions/getMasterVersions",{startIndex:e,offSet:t}),this.getActiveVersion=()=>this.$http.post("/api/misFunctions/getActiveVersion",{}),this.getTotalRecords=e=>this.$http.post("/api/misFunctions/getTotalRecords",{reportId:e}),this.getEmpProjectionErrorLogsHistory=()=>this.$http.post("/api/misFunctions/getEmpProjectionErrorLogsHistory",{}),this.getProjectionRunLogs=e=>this.$http.post("/api/misFunctions/getRRActionLogs",{project_id:e}),this.getEmpProjectionManualRunHistory=()=>this.$http.post("/api/misFunctions/getEmpProjectionManualRunHistory",{}),this.getPositionBasedProjection=e=>this.$http.post("/api/misFunctions/getPositionBasedProjection",{projectId:e})}saveLiveVersion(e){return console.log("save version in service"),console.log(e),new Promise((t,n)=>{this.$http.post("/api/misFunctions/saveLiveProjection",{title:e}).subscribe(e=>t(e),e=>n(e))})}changeSavedVersionToCurrentVersion(e){return console.log("change in service"),console.log(e),new Promise((t,n)=>{this.$http.post("/api/misFunctions/markAsCurrentProjection",{savedVersionId:e}).subscribe(e=>t(e),e=>n(e))})}getProjectList(e,t,n){return new Promise((i,o)=>{this.$http.post("/api/misFunctions/getProjectMaster",{searchText:e,startIndex:t,offSet:n}).subscribe(e=>i(e),e=>o(e))})}getItemList(e,t,n){return new Promise((i,o)=>{this.$http.post("/api/misFunctions/getItemMaster",{searchText:e,startIndex:t,offSet:n}).subscribe(e=>i(e),e=>o(e))})}getIsaList(e,t,n){return new Promise((i,o)=>{this.$http.post("/api/misFunctions/getIsaMasterData",{searchText:e,startIndex:t,offSet:n}).subscribe(e=>i(e),e=>o(e))})}insertRRProjectionsLive(e,t){return new Promise((n,i)=>{this.$http.post("/api/misFunctions/insertRRProjectionsLive",{type:e,id:t}).subscribe(e=>n(e),e=>i(e))})}getBillingAdviceMilestoneList(e,t,n){return new Promise((i,o)=>{this.$http.post("/api/misFunctions/getBillingAdviceMilestoneMaster",{searchText:e,startIndex:t,offSet:n}).subscribe(e=>i(e),e=>o(e))})}getBilledInvoiceMilestoneList(e,t,n){return new Promise((i,o)=>{this.$http.post("/api/misFunctions/getBilledInvoiceMilestoneList",{searchText:e,startIndex:t,offSet:n}).subscribe(e=>i(e),e=>o(e))})}rrIsaBasedManualRunCheckAccess(){return i.where(this.rolesService.roles,{application_id:1001,object_id:1}).length>0}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275inject"](r.c),o["\u0275\u0275inject"](s.a))},e.\u0275prov=o["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()}}]);