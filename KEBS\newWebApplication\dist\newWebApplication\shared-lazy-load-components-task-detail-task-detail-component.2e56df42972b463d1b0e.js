(window.webpackJsonp=window.webpackJsonp||[]).push([[898],{"Eh+D":function(t,e,i){"use strict";i.r(e),i.d(e,"TaskDetailComponent",(function(){return I}));var n=i("fXoL"),o=i("0IaG"),a=i("1G5W"),r=i("XNiG"),s=i("ofXK"),l=(i("kmnG"),i("qFsG"),i("bTqV")),c=i("NFeN"),d=i("Qu3c"),m=(i("Xa2L"),i("Xi0T"),i("7pIB"),i("XXEo")),p=i("BVzC"),h=i("0qc8");const f=["projectDetailItemContainer"];let I=(()=>{class t{constructor(t,e,i,n,o,a){this._auth=t,this.dialogRef=e,this.inData=i,this._ErrorService=n,this.compiler=o,this._detailTaskService=a,this._onDestroy=new r.b,this.isDetailLoading=!1,this.closeTaskDetailModal=()=>{this.dialogRef.close({event:"Close"})}}ngOnInit(){this.inData&&this.inData.dataItems&&(this.dataItem=this.inData.dataItems),this.getRequestDetails()}getRequestDetails(){if(this.isDetailLoading=!0,this.dataItem.project_id){let t={projectId:this.dataItem.project_id,id:this.dataItem.id.toString(),version:this.dataItem.version};this._detailTaskService.getProjectItem(t).pipe(Object(a.a)(this._onDestroy)).subscribe(t=>{0==t.err&&t.data?(this.projectItem=t.data,console.log("Project Item Details",this.projectItem),this.projectDetailItemContainerRef&&this.projectDetailItemContainerRef.clear(),this.loadProjectDetailItem(),this.isDetailLoading=!1):this.isDetailLoading=!1},t=>{this.isDetailLoading=!1,this._ErrorService.userErrorAlert(t.error.errorCode,"Error getting Project details",t.error.errMessage)})}}loadProjectDetailItem(){this.projectDetailItemContainerRef&&this.projectDetailItemContainerRef.clear(),i.e(1014).then(i.bind(null,"30bQ")).then(t=>{const e=this.compiler.compileModuleSync(t.DetailItemsModule).create(this.projectDetailItemContainerRef.injector).componentFactoryResolver.resolveComponentFactory(t.DetailItemsComponent);this.projectDetailItemContainerRef.createComponent(e).instance.requestItem=this.projectItem})}ngOnDestroy(){this.projectDetailItemContainerRef&&this.projectDetailItemContainerRef.clear(),this._onDestroy.next(),this._onDestroy.complete()}}return t.\u0275fac=function(e){return new(e||t)(n["\u0275\u0275directiveInject"](m.a),n["\u0275\u0275directiveInject"](o.h),n["\u0275\u0275directiveInject"](o.a),n["\u0275\u0275directiveInject"](p.a),n["\u0275\u0275directiveInject"](n.Compiler),n["\u0275\u0275directiveInject"](h.a))},t.\u0275cmp=n["\u0275\u0275defineComponent"]({type:t,selectors:[["app-task-detail"]],viewQuery:function(t,e){if(1&t&&n["\u0275\u0275viewQuery"](f,!0,n.ViewContainerRef),2&t){let t;n["\u0275\u0275queryRefresh"](t=n["\u0275\u0275loadQuery"]())&&(e.projectDetailItemContainerRef=t.first)}},decls:12,vars:3,consts:[[1,"container-fluid","pt-2","pb-2","task-detail-styles"],[2,"background-color","#f9f9f9"],[1,"row"],[1,"col-11","pt-2","headingBold"],[1,"col-1","mt-0","d-flex"],["mat-icon-button","",1,"ml-auto","close-button","mt-1",3,"click"],["matTooltip","Close",1,"close-Icon"],["projectDetailItemContainer",""]],template:function(t,e){1&t&&(n["\u0275\u0275elementStart"](0,"div",0),n["\u0275\u0275elementStart"](1,"div",1),n["\u0275\u0275elementStart"](2,"div",2),n["\u0275\u0275elementStart"](3,"div",3),n["\u0275\u0275text"](4),n["\u0275\u0275pipe"](5,"titlecase"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](6,"div",4),n["\u0275\u0275elementStart"](7,"button",5),n["\u0275\u0275listener"]("click",(function(){return e.closeTaskDetailModal()})),n["\u0275\u0275elementStart"](8,"mat-icon",6),n["\u0275\u0275text"](9,"close"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementContainer"](10,null,7),n["\u0275\u0275elementEnd"]()),2&t&&(n["\u0275\u0275advance"](4),n["\u0275\u0275textInterpolate1"]("",n["\u0275\u0275pipeBind1"](5,1,e.dataItem.text)," Detailed View"))},directives:[l.a,c.a,d.a],pipes:[s.TitleCasePipe],styles:[".task-detail-styles[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.task-detail-styles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}.task-detail-styles[_ngcontent-%COMP%]   .headingBold[_ngcontent-%COMP%]{color:#cf0001;font-weight:500;font-size:14px}.task-detail-styles[_ngcontent-%COMP%]   .headerFont[_ngcontent-%COMP%]{color:#868383;font-size:14px!important;font-weight:400;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}.task-detail-styles[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}"]}),t})()}}]);