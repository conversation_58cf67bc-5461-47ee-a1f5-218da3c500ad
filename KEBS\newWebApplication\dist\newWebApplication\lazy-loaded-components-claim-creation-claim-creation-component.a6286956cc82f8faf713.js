(window.webpackJsonp=window.webpackJsonp||[]).push([[747,765,821,822,983,987,990,991],{"2cJt":function(e,t,n){"use strict";n.r(t),n.d(t,"ClaimCreationComponent",(function(){return ql}));var l=n("mrSG"),i=n("fXoL"),o=n("xG9w"),a=n("3Pt+"),r=n("R0Ic"),s=n("7pIB"),c=n("wd/R"),d=n("ofXK"),m=n("AytR"),p=n("0IaG"),u=n("CiYR");let f=(()=>{class e{transform(e){if(!e)return"";let t=e.toString().replace(/[^0-9]/g,"");if(t.length>4&&(t=t.slice(0,4)),1===t.length&&("3"!==t&&"4"!==t&&"5"!==t&&"6"!==t&&"7"!==t&&"8"!==t&&"9"!==t||(t="0"+t)),t.length>=2&&t.length<4)t=t.slice(0,2)+":"+t.slice(2,4);else if(4===t.length){const e=parseInt(t.slice(0,2),10),n=parseInt(t.slice(2,4),10);t=e>23?"23:59":n>59?t.slice(0,2)+":59":t.slice(0,2)+":"+t.slice(2,4)}return 3===t.length&&(t=t.slice(0,2)),t}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=i["\u0275\u0275definePipe"]({name:"timeFormat",type:e,pure:!0}),e})();var g=n("kmnG"),h=n("qFsG"),v=n("NFeN"),C=n("bTqV"),x=n("Qu3c"),y=n("iadO"),b=(n("Xi0T"),n("lVl8")),_=n("Xa2L"),E=n("1yaQ"),F=n("FKr1"),I=(n("gvOY"),n("d3UM")),w=(n("9mMp"),n("pA3K"),n("dNgK")),S=n("JqCM"),D=(n("8hBH"),n("5+WD"),n("VJID")),O=n("XXEo"),A=n("ucYs"),M=n("LcQX"),T=n("1d+P"),P=n("78ai");let k=(()=>{class e{constructor(e,t){this.data=e,this._dialogRef=t}ngOnInit(){}closeMessage(){this._dialogRef.close()}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](p.a),i["\u0275\u0275directiveInject"](p.h))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-expense-custom-toast"]],decls:5,vars:3,consts:[[1,"dialog",3,"ngClass","click"],[1,"title-text"],[1,"msg-text"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275listener"]("click",(function(){return t.closeMessage()})),i["\u0275\u0275elementStart"](1,"p",1),i["\u0275\u0275text"](2),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](3,"p",2),i["\u0275\u0275text"](4),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275property"]("ngClass","dialog-"+t.data.icon),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate"](t.data.title),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate"](t.data.msg))},directives:[d.NgClass],styles:[".dialog[_ngcontent-%COMP%]{min-width:320px;min-height:64px;border-width:2px;border-style:solid;border-radius:4px;padding:8px 16px;cursor:pointer}.dialog[_ngcontent-%COMP%]   .title-text[_ngcontent-%COMP%]{font-weight:600;color:#111434;line-height:24px}.dialog[_ngcontent-%COMP%]   .msg-text[_ngcontent-%COMP%], .dialog[_ngcontent-%COMP%]   .title-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;margin:0}.dialog[_ngcontent-%COMP%]   .msg-text[_ngcontent-%COMP%]{font-weight:400;color:#515965;line-height:20px}.dialog-info[_ngcontent-%COMP%]{background:#e8f4ff;border-color:#1890ff}.dialog-check_circle[_ngcontent-%COMP%]{background:#eef9e8;border-color:#52c41a}.dialog-close[_ngcontent-%COMP%]{background:#ffebec;border-color:#ff3a46}.dialog-warning[_ngcontent-%COMP%]{background:#fff3e8;border-color:#fa8c16}"]}),e})();var V=n("tyNb"),L=n("XNiG"),B=n("pLZG"),N=n("1G5W");let R=(()=>{class e{constructor(e,t,n){this._dialog=e,this.rendererFactory=t,this.router=n,this.currentDialogRef=null,this.destroy$=new L.b,this.router.events.pipe(Object(B.a)(e=>e instanceof V.e),Object(N.a)(this.destroy$)).subscribe(e=>{this.closeCurrentDialog()}),this.renderer=this.rendererFactory.createRenderer(null,null),this.applyStyles()}ngOnInit(){}applyStyles(){const e=this.renderer.createElement("style");e.type="text/css",e.appendChild(this.renderer.createText(".mat-dialog-container { box-shadow: none !important; }")),this.renderer.appendChild(document.head,e)}closeCurrentDialog(){this.currentDialogRef&&(this.currentDialogRef.close(),this.currentDialogRef=null)}showInfo(e,t,n){this.closeCurrentDialog(),this.currentDialogRef=this._dialog.open(k,{data:{title:e,msg:t,icon:"info"},disableClose:!0,position:{bottom:"16px",right:"16px"},backdropClass:"none",hasBackdrop:!1}),setTimeout(()=>{this.currentDialogRef.close()},n)}showSuccess(e,t,n){this.closeCurrentDialog(),this.currentDialogRef=this._dialog.open(k,{data:{title:e,msg:t,icon:"check_circle"},disableClose:!0,position:{bottom:"16px",right:"16px"},backdropClass:"none",hasBackdrop:!1}),setTimeout(()=>{this.currentDialogRef.close()},n)}showError(e,t,n){this.closeCurrentDialog(),this.currentDialogRef=this._dialog.open(k,{data:{title:e,msg:t,icon:"close"},disableClose:!0,position:{bottom:"16px",right:"16px"},backdropClass:"none",hasBackdrop:!1}),setTimeout(()=>{this.currentDialogRef.close()},n)}showWarning(e,t,n){this.closeCurrentDialog(),this.currentDialogRef=this._dialog.open(k,{data:{title:e,msg:t,icon:"warning"},disableClose:!0,position:{bottom:"16px",right:"16px"},backdropClass:"none",hasBackdrop:!1}),setTimeout(()=>{this.currentDialogRef.close()},n)}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275inject"](p.b),i["\u0275\u0275inject"](i.RendererFactory2),i["\u0275\u0275inject"](V.g))},e.\u0275prov=i["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var q=n("3kOA"),H=n("K8NC"),Y=n("me71"),j=n("Kj3r"),W=n("tk/3");const $=["onScroll"];function U(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"span"),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=i["\u0275\u0275nextContext"]().$implicit;i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"]("",n[e],"\xa0")}}function z(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-option",12),i["\u0275\u0275template"](1,U,2,1,"span",13),i["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=i["\u0275\u0275nextContext"]();i["\u0275\u0275property"]("value",e),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngForOf",n.optionLabel)}}const G=function(e){return{"disabled-field":e}};let J=(()=>{class e{constructor(e,t){this._http=e,this._mulL=t,this.selectedValues=new i.EventEmitter,this.disabled=!1,this.bgColorOnDisable=!1,this.list=[],this.searchCtrl=new a.j,this.selectedValCtrl=new a.j,this._onDestroy=new L.b,this.isLoading=!1,this.startIndex=0,this.endIndex=15,this.noToRetrieve=[]}ngOnInit(){return Object(l.c)(this,void 0,void 0,(function*(){this.fieldValue&&(this.list=this.list.concat(this.fieldValue),this.selectedValCtrl.patchValue(this.fieldValue)),yield this.fetchData(),this.searchCtrl.valueChanges.pipe(Object(N.a)(this._onDestroy),Object(j.a)(700)).subscribe(e=>Object(l.c)(this,void 0,void 0,(function*(){null!=e&&""!=e&&(this.list=[],this.startIndex=0,this.endIndex=15,yield this.fetchData())}))),this.selectedValCtrl.valueChanges.subscribe(e=>{this.selectedValues.emit(e)}),this._mulL.getRemoveIndex().subscribe(e=>{this.removeSelectedOption(e)})}))}setEmployeeList(e){if(this.selectedValCtrl.value){let t=e.filter(({associate_id:e,display_name:t})=>!this.selectedValCtrl.value.some(({associate_id:n,display_name:l})=>e===n&&t===l)),n=this.list.filter(({associate_id:e,display_name:t})=>!this.selectedValCtrl.value.some(({associate_id:n,display_name:l})=>e===n&&t===l));this.list=this.selectedValCtrl.value,this.list=this.list.concat(n),this.list=this.list.concat(t)}else this.list=this.list.concat(e)}removeSelectedOption(e){let t=[];t=this.selectedValCtrl.value,t.splice(e,1),this.selectedValCtrl.patchValue([...t]),this.selectedValues.emit(this.selectedValCtrl.value)}fetchData(){return Object(l.c)(this,void 0,void 0,(function*(){this.isLoading=!0;let e=yield this._mulL.getEmployeeList(this.searchCtrl.value,this.startIndex,this.endIndex,o.pluck(this.selectedValCtrl.value,"associate_id"));this.isLoading=!1,this.setEmployeeList(e.data)}))}onOpenedChange(e,t){e&&this[t].panel.nativeElement.addEventListener("scroll",e=>{let n=Math.ceil(this[t].panel.nativeElement.scrollTop),l=this[t].panel.nativeElement.scrollHeight-this[t].panel.nativeElement.offsetHeight;n-1!=l&&n!=l&&n+1!=l||(this.startIndex+=15,this.endIndex+=15,this.fetchData())})}cancelSearchValue(){this.searchCtrl.patchValue(""),this.fetchData()}ngOnChanges(){this.selectedValCtrl.patchValue(this.fieldValue)}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](W.c),i["\u0275\u0275directiveInject"](P.a))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search-people-involved-dropdown"]],viewQuery:function(e,t){if(1&e&&i["\u0275\u0275viewQuery"]($,!0),2&e){let e;i["\u0275\u0275queryRefresh"](e=i["\u0275\u0275loadQuery"]())&&(t.onScroll=e.first)}},inputs:{label:"label",placeholder:"placeholder",appearance:"appearance",optionLabel:"optionLabel",API_URL:"API_URL",bodyParams:"bodyParams",token:"token",fieldValue:"fieldValue",disabled:"disabled",bgColorOnDisable:"bgColorOnDisable"},outputs:{selectedValues:"selectedValues"},features:[i["\u0275\u0275NgOnChangesFeature"]],decls:15,vars:9,consts:[["appearance","outline",1,"full-width",3,"ngClass"],["multiple","",3,"placeholder","formControl","disabled","openedChange"],["onScroll",""],[2,"width","100%"],[1,"pl-1","icon-search"],["matPrefix",""],["noEntriesFoundLabel","No results found",1,"p-3","pl-0",2,"width","80%",3,"formControl","placeholder","keydown"],[1,"icon-search-cancel",3,"click"],["matSuffix","",1,"mr-1"],[1,"no-selection-box",3,"click"],[1,"ml-4"],[3,"value",4,"ngFor","ngForOf"],[3,"value"],[4,"ngFor","ngForOf"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"mat-form-field",0),i["\u0275\u0275elementStart"](1,"mat-select",1,2),i["\u0275\u0275listener"]("openedChange",(function(e){return t.onOpenedChange(e,"onScroll")})),i["\u0275\u0275elementStart"](3,"div",3),i["\u0275\u0275elementStart"](4,"span",4),i["\u0275\u0275elementStart"](5,"mat-icon",5),i["\u0275\u0275text"](6,"search"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](7,"input",6),i["\u0275\u0275listener"]("keydown",(function(e){return e.stopPropagation()})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](8,"span",7),i["\u0275\u0275listener"]("click",(function(){return t.cancelSearchValue()})),i["\u0275\u0275elementStart"](9,"mat-icon",8),i["\u0275\u0275text"](10,"highlight_off"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](11,"mat-option",9),i["\u0275\u0275listener"]("click",(function(){return t.selectedValCtrl.reset()})),i["\u0275\u0275elementStart"](12,"span",10),i["\u0275\u0275text"](13,"Clear Entries"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](14,z,2,2,"mat-option",11),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction1"](7,G,1==t.bgColorOnDisable)),i["\u0275\u0275advance"](1),i["\u0275\u0275propertyInterpolate"]("placeholder",t.placeholder),i["\u0275\u0275property"]("formControl",t.selectedValCtrl)("disabled",!!t.disabled),i["\u0275\u0275advance"](6),i["\u0275\u0275property"]("formControl",t.searchCtrl)("placeholder",t.placeholder?t.placeholder:""),i["\u0275\u0275advance"](7),i["\u0275\u0275property"]("ngForOf",t.list))},directives:[g.c,d.NgClass,I.c,a.v,a.k,v.a,g.h,a.e,g.i,F.p,d.NgForOf],styles:[".full-width[_ngcontent-%COMP%]{width:100%;width:100%!important}input[_ngcontent-%COMP%], input[_ngcontent-%COMP%]:focus{border:none;outline:none;caret-color:#cf0001}.icon-search[_ngcontent-%COMP%], .icon-search-cancel[_ngcontent-%COMP%]{color:#a1a1a2;font-size:20px;vertical-align:middle}.icon-search-cancel[_ngcontent-%COMP%]{cursor:pointer}.disabled-field[_ngcontent-%COMP%]   .mat-form-field-outline-end[_ngcontent-%COMP%],   .disabled-field .mat-form-field-appearance-outline .mat-form-field-outline-thick,   .disabled-field .mat-form-field-outline-end,   .disabled-field .mat-form-field-outline-gap,   .disabled-field .mat-form-field-outline-start{background-color:#e8e9ee!important}  .no-selection-box .mat-option-ripple,   .no-selection-box .mat-pseudo-checkbox{display:none}"]}),e})();var K=n("wFJK");function X(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"span",14),i["\u0275\u0275elementStart"](1,"mat-icon",15),i["\u0275\u0275text"](2,"info"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](3,"span",16),i["\u0275\u0275text"](4),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](4),i["\u0275\u0275textInterpolate1"](" ",e.expense_creation_restriction_message," ")}}function Q(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",23),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const t=i["\u0275\u0275nextContext"]().index;return i["\u0275\u0275nextContext"](2).selectClaimType(t)})),i["\u0275\u0275elementStart"](1,"div",24),i["\u0275\u0275elementStart"](2,"mat-icon",25),i["\u0275\u0275text"](3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"div",26),i["\u0275\u0275text"](5),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"]().$implicit;i["\u0275\u0275propertyInterpolate"]("matTooltip",e.description),i["\u0275\u0275property"]("ngClass",e.active?"btn-active":"btn-not-active"),i["\u0275\u0275advance"](3),i["\u0275\u0275textInterpolate1"](" ",e.icon," "),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"](" ",e.name," ")}}function Z(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"span",27),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]().$implicit,t=i["\u0275\u0275nextContext"](2);i["\u0275\u0275styleProp"]("border","1px solid "+t.theme_color1),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](null==e?null:e.count)}}function ee(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",20),i["\u0275\u0275template"](1,Q,6,4,"button",21),i["\u0275\u0275template"](2,Z,2,3,"span",22),i["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=t.index,l=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",n<l.showIndex),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null==e?null:e.count)}}function te(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",17),i["\u0275\u0275elementStart"](1,"div",18),i["\u0275\u0275template"](2,ee,3,2,"div",19),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngForOf",e.claimType)}}const ne=function(){return{color:"#79BA44","background-color":"#79BA44"}};function le(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",28),i["\u0275\u0275elementStart"](1,"span",29),i["\u0275\u0275text"](2,"Loading..."),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngStyle",i["\u0275\u0275pureFunction0"](1,ne)))}function ie(e,t){}function oe(e,t){if(1&e&&i["\u0275\u0275template"](0,ie,0,0,"ng-template",51),2&e){i["\u0275\u0275nextContext"](2);const e=i["\u0275\u0275reference"](20);i["\u0275\u0275property"]("ngTemplateOutlet",e)}}const ae=function(e){return{"app-input-search-field-highlight":e}};function re(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",32),i["\u0275\u0275elementStart"](1,"div",33),i["\u0275\u0275text"](2),i["\u0275\u0275template"](3,oe,1,1,void 0,13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"app-expense-search-user",50),i["\u0275\u0275listener"]("change",(function(t){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"](2).onClaimedByChange(t)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"](2);let t=null,n=null;i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"]("",null!=e.fieldConfig&&null!=e.fieldConfig.claimedBy&&e.fieldConfig.claimedBy.field_label?e.fieldConfig.claimedBy.field_label:"Claimed By *"," "),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",(null==(t=e.claimForm.get("claimedBy"))?null:t.hasError("required"))&&(null==(t=e.claimForm.get("claimedBy"))?null:t.touched)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction1"](7,ae,(null==(n=e.claimForm.get("claimedBy"))?null:n.hasError("required"))&&(null==(n=e.claimForm.get("claimedBy"))?null:n.touched)))("isAutocomplete",!0)("readonly",e.isDisableClaimedBy)("disabled",!(null==e.fieldConfig||null==e.fieldConfig.claimedBy||!e.fieldConfig.claimedBy.is_disabled))("bgColorOnDisable",!(null==e.fieldConfig||null==e.fieldConfig.claimedBy||!e.fieldConfig.claimedBy.is_disabled))}}const se=function(e){return{"disabled-field":e}};function ce(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",32),i["\u0275\u0275elementStart"](1,"div",33),i["\u0275\u0275text"](2),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](3,"mat-form-field",52,53),i["\u0275\u0275element"](5,"input",54),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate"](null!=e.fieldConfig&&null!=e.fieldConfig.claimDate&&e.fieldConfig.claimDate.field_label?e.fieldConfig.claimDate.field_label:"Date of submission"),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction1"](4,se,1==(null==e.fieldConfig||null==e.fieldConfig.claimDate?null:e.fieldConfig.claimDate.is_disabled))),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("readonly",!(null==e.fieldConfig||null==e.fieldConfig.claimDate||!e.fieldConfig.claimDate.is_disabled))("required",!(null==e.fieldConfig||null==e.fieldConfig.claimDate||!e.fieldConfig.claimDate.is_mandatory))}}function de(e,t){}function me(e,t){if(1&e&&i["\u0275\u0275template"](0,de,0,0,"ng-template",51),2&e){i["\u0275\u0275nextContext"]();const e=i["\u0275\u0275reference"](20);i["\u0275\u0275property"]("ngTemplateOutlet",e)}}function pe(e,t){}function ue(e,t){if(1&e&&i["\u0275\u0275template"](0,pe,0,0,"ng-template",51),2&e){i["\u0275\u0275nextContext"](2);const e=i["\u0275\u0275reference"](20);i["\u0275\u0275property"]("ngTemplateOutlet",e)}}function fe(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",32),i["\u0275\u0275elementStart"](1,"div",33),i["\u0275\u0275text"](2),i["\u0275\u0275template"](3,ue,1,1,void 0,13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"app-exp-input-search",55),i["\u0275\u0275listener"]("change",(function(t){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"](2).changeCostCenter(t)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"](2);let t=null,n=null;i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"]("",null!=e.fieldConfig&&null!=e.fieldConfig.costCenterCode&&e.fieldConfig.costCenterCode.field_label?e.fieldConfig.costCenterCode.field_label:"Cost Center"," * "),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",(null==(t=e.claimForm.get("costCenter"))?null:t.hasError("required"))&&(null==(t=e.claimForm.get("costCenter"))?null:t.touched)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction1"](8,ae,(null==(n=e.claimForm.get("costCenter"))?null:n.hasError("required"))&&(null==(n=e.claimForm.get("costCenter"))?null:n.touched)))("placeholder",null!=e.fieldConfig&&null!=e.fieldConfig.costCenterCode&&e.fieldConfig.costCenterCode.field_label?e.fieldConfig.costCenterCode.field_label:"Cost Center")("list",e.costCenterList)("disabled",!(null==e.fieldConfig||null==e.fieldConfig.costCenterCode||!e.fieldConfig.costCenterCode.is_disabled))("bgColorOnDisable",!(null==e.fieldConfig||null==e.fieldConfig.costCenterCode||!e.fieldConfig.costCenterCode.is_disabled))("hideMatLabel",!0)}}function ge(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",32),i["\u0275\u0275elementStart"](1,"div",33),i["\u0275\u0275text"](2),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](3,"mat-form-field",52,53),i["\u0275\u0275element"](5,"input",56),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate"](null!=e.fieldConfig&&null!=e.fieldConfig.department&&e.fieldConfig.department.field_label?e.fieldConfig.department.field_label:"Department *"),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction1"](5,se,1==(null==e.fieldConfig||null==e.fieldConfig.department?null:e.fieldConfig.department.is_disabled))),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("placeholder",null!=e.fieldConfig&&null!=e.fieldConfig.department&&e.fieldConfig.department.field_label?e.fieldConfig.department.field_label:"Department")("readonly",!(null==e.fieldConfig||null==e.fieldConfig.department||!e.fieldConfig.department.is_disabled))("required",!(null==e.fieldConfig||null==e.fieldConfig.department||!e.fieldConfig.department.is_mandatory))}}function he(e,t){}function ve(e,t){if(1&e&&i["\u0275\u0275template"](0,he,0,0,"ng-template",51),2&e){i["\u0275\u0275nextContext"](2);const e=i["\u0275\u0275reference"](20);i["\u0275\u0275property"]("ngTemplateOutlet",e)}}function Ce(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",32),i["\u0275\u0275elementStart"](1,"div",33),i["\u0275\u0275text"](2),i["\u0275\u0275template"](3,ve,1,1,void 0,13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"app-exp-input-search",57),i["\u0275\u0275listener"]("input",(function(t){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"](2).onExpenseTagChange(t)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"](2);let t=null,n=null;i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"](" ",null!=e.fieldConfig&&null!=e.fieldConfig.expenseTag&&e.fieldConfig.expenseTag.field_label?e.fieldConfig.expenseTag.field_label:"Expense Tag"," * "),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",(null==(t=e.claimForm.get("expenseTag"))?null:t.hasError("required"))&&(null==(t=e.claimForm.get("expenseTag"))?null:t.touched)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction1"](8,ae,(null==(n=e.claimForm.get("expenseTag"))?null:n.hasError("required"))&&(null==(n=e.claimForm.get("expenseTag"))?null:n.touched)))("placeholder",null!=e.fieldConfig&&null!=e.fieldConfig.expenseTag&&e.fieldConfig.expenseTag.field_label?e.fieldConfig.expenseTag.field_label:"Expense Tag")("list",e.expenseTagList)("disabled",!(null==e.fieldConfig||null==e.fieldConfig.expenseTag||!e.fieldConfig.expenseTag.is_disabled))("bgColorOnDisable",!(null==e.fieldConfig||null==e.fieldConfig.expenseTag||!e.fieldConfig.expenseTag.is_disabled))("hideMatLabel",!0)}}function xe(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span"),i["\u0275\u0275text"](1,"*"),i["\u0275\u0275elementEnd"]())}function ye(e,t){}function be(e,t){if(1&e&&i["\u0275\u0275template"](0,ye,0,0,"ng-template",51),2&e){i["\u0275\u0275nextContext"](2);const e=i["\u0275\u0275reference"](20);i["\u0275\u0275property"]("ngTemplateOutlet",e)}}function _e(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-option",64),i["\u0275\u0275text"](1),i["\u0275\u0275element"](2,"span"),i["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;i["\u0275\u0275property"]("value",null==e?null:e.oid)("matTooltip",(null==e?null:e.name)+" - "+(null==e?null:e.role)),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate2"](" ",null==e?null:e.name," - ",null==e?null:e.role,"")}}const Ee=function(e,t){return{"disabled-field":e,"app-input-search-field-highlight":t}},Fe=function(){return{"max-height":"200px","overflow-y":"auto"}};function Ie(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",58),i["\u0275\u0275elementStart"](1,"div",33),i["\u0275\u0275text"](2),i["\u0275\u0275template"](3,xe,2,0,"span",13),i["\u0275\u0275elementStart"](4,"mat-icon",59),i["\u0275\u0275text"](5," info_outline "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](6,be,1,1,void 0,13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](7,"div",60),i["\u0275\u0275elementStart"](8,"mat-form-field",61,53),i["\u0275\u0275elementStart"](10,"mat-select",62),i["\u0275\u0275listener"]("selectionChange",(function(t){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"](2).onManualHeaderApproverSelection(t)})),i["\u0275\u0275template"](11,_e,3,4,"mat-option",63),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"](2);let t=null,n=null;i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"]("",null!=e.fieldConfig&&null!=e.fieldConfig.manualApprover&&e.fieldConfig.manualApprover.field_label?e.fieldConfig.manualApprover.field_label:"Select Approver *"," "),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null==e.fieldConfig||null==e.fieldConfig.manualApprover?null:e.fieldConfig.manualApprover.is_mandatory),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngIf",(null==(t=e.claimForm.get("headerLevelApprover"))?null:t.hasError("required"))&&(null==(t=e.claimForm.get("headerLevelApprover"))?null:t.touched)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction2"](8,Ee,!(null!=e.approversList&&e.approversList.length),(null==(n=e.claimForm.get("headerLevelApprover"))?null:n.hasError("required"))&&(null==(n=e.claimForm.get("headerLevelApprover"))?null:n.touched))),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("disabled",!(null==e.fieldConfig||null==e.fieldConfig.manualApprover||!e.fieldConfig.manualApprover.is_disabled))("placeholder",null==e.fieldConfig||null==e.fieldConfig.manualApprover?null:e.fieldConfig.manualApprover.field_label)("panelStyle",e.headerApproverSelect?i["\u0275\u0275pureFunction0"](11,Fe):null),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngForOf",e.approversList)}}function we(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",69),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](2,"div",69),i["\u0275\u0275text"](3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"div",69),i["\u0275\u0275text"](5),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]().$implicit;i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" ",e.name," "),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"](" ",e.designation," "),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"](" Level ",e.level," ")}}function Se(e,t){if(1&e&&(i["\u0275\u0275elementContainerStart"](0),i["\u0275\u0275element"](1,"app-user-image",67),i["\u0275\u0275template"](2,we,6,3,"ng-template",null,68,i["\u0275\u0275templateRefExtractor"]),i["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit,n=i["\u0275\u0275reference"](3);i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("tooltip",n)("id",e.oid)}}function De(e,t){if(1&e&&(i["\u0275\u0275elementContainerStart"](0),i["\u0275\u0275template"](1,Se,4,2,"ng-container",66),i["\u0275\u0275elementContainerEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](3);i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngForOf",e.claimForm.value.headerLevelApprover)}}function Oe(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",65),i["\u0275\u0275template"](1,De,2,1,"ng-container",13),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",""!=e.claimForm.value.headerLevelApprover)}}function Ae(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span",72),i["\u0275\u0275text"](1,"*"),i["\u0275\u0275elementEnd"]())}function Me(e,t){}function Te(e,t){if(1&e&&i["\u0275\u0275template"](0,Me,0,0,"ng-template",51),2&e){i["\u0275\u0275nextContext"](2);const e=i["\u0275\u0275reference"](20);i["\u0275\u0275property"]("ngTemplateOutlet",e)}}function Pe(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"mat-hint"),i["\u0275\u0275text"](1," Maximum length is 255 characters "),i["\u0275\u0275elementEnd"]())}function ke(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",32),i["\u0275\u0275elementStart"](1,"div",33),i["\u0275\u0275text"](2),i["\u0275\u0275template"](3,Ae,2,0,"span",70),i["\u0275\u0275template"](4,Te,1,1,void 0,13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"mat-form-field",52,53),i["\u0275\u0275element"](7,"input",71),i["\u0275\u0275template"](8,Pe,2,0,"mat-hint",13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](2);let t=null,n=null,l=null;i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"]("",null!=e.fieldConfig&&null!=e.fieldConfig.headerDescription&&e.fieldConfig.headerDescription.field_label?e.fieldConfig.headerDescription.field_label:"Description"," "),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null==e.fieldConfig||null==e.fieldConfig.headerDescription?null:e.fieldConfig.headerDescription.is_mandatory),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",(null==(t=e.claimForm.get("headerDescription"))?null:t.hasError("required"))&&(null==(t=e.claimForm.get("headerDescription"))?null:t.touched)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction2"](8,Ee,1==(null==e.fieldConfig||null==e.fieldConfig.headerDescription?null:e.fieldConfig.headerDescription.is_disabled),(null==(n=e.claimForm.get("headerDescription"))?null:n.hasError("required"))&&(null==(n=e.claimForm.get("headerDescription"))?null:n.touched))),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("placeholder",null!=e.fieldConfig&&null!=e.fieldConfig.headerDescription&&e.fieldConfig.headerDescription.field_label?e.fieldConfig.headerDescription.field_label:"headerDescription")("required",!(null==e.fieldConfig||null==e.fieldConfig.headerDescription||!e.fieldConfig.headerDescription.is_mandatory))("readonly",!(null==e.fieldConfig||null==e.fieldConfig.headerDescription||!e.fieldConfig.headerDescription.is_disabled)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",(null==(l=e.claimForm.get("headerDescription"))||null==l.value?null:l.value.length)>255)}}function Ve(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span",72),i["\u0275\u0275text"](1,"*"),i["\u0275\u0275elementEnd"]())}function Le(e,t){}function Be(e,t){if(1&e&&i["\u0275\u0275template"](0,Le,0,0,"ng-template",51),2&e){i["\u0275\u0275nextContext"](2);const e=i["\u0275\u0275reference"](20);i["\u0275\u0275property"]("ngTemplateOutlet",e)}}function Ne(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-option",77),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;i["\u0275\u0275property"]("value",e.id),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](e.billing_type)}}function Re(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-form-field",74,53),i["\u0275\u0275elementStart"](2,"mat-select",75),i["\u0275\u0275template"](3,Ne,2,2,"mat-option",76),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](3);i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction1"](5,se,1==(null==e.fieldConfig||null==e.fieldConfig.customerBilling?null:e.fieldConfig.customerBilling.is_disabled))),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("disabled",!(null==e.fieldConfig||null==e.fieldConfig.customerBilling||!e.fieldConfig.customerBilling.is_disabled))("required",!(null==e.fieldConfig||null==e.fieldConfig.customerBilling||!e.fieldConfig.customerBilling.is_mandatory))("placeholder",null==e.fieldConfig||null==e.fieldConfig.customerBilling?null:e.fieldConfig.customerBilling.field_label),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngForOf",e.billingConfig)}}function qe(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",32),i["\u0275\u0275elementStart"](1,"div",33),i["\u0275\u0275text"](2),i["\u0275\u0275template"](3,Ve,2,0,"span",70),i["\u0275\u0275template"](4,Be,1,1,void 0,13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"div",60),i["\u0275\u0275template"](6,Re,4,7,"mat-form-field",73),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](2);let t=null,n=null;i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"](" ",null!=e.fieldConfig&&null!=e.fieldConfig.customerBilling&&e.fieldConfig.customerBilling.field_label?e.fieldConfig.customerBilling.field_label:"Customer Billing *"," "),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null==e.fieldConfig||null==e.fieldConfig.customerBilling?null:e.fieldConfig.customerBilling.is_mandatory),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",(null==(t=e.claimForm.get("customerBilling"))?null:t.hasError("required"))&&(null==(t=e.claimForm.get("customerBilling"))?null:t.touched)||"I"==e.costCenterType&&3==e.claimForm.get("customerBilling").value),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction1"](5,ae,(null==(n=e.claimForm.get("customerBilling"))?null:n.hasError("required"))&&(null==(n=e.claimForm.get("customerBilling"))?null:n.touched)||"I"==e.costCenterType&&3==e.claimForm.get("customerBilling").value)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",e.billingConfig)}}function He(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span"),i["\u0275\u0275text"](1,"*"),i["\u0275\u0275elementEnd"]())}function Ye(e,t){}function je(e,t){if(1&e&&i["\u0275\u0275template"](0,Ye,0,0,"ng-template",51),2&e){i["\u0275\u0275nextContext"](2);const e=i["\u0275\u0275reference"](20);i["\u0275\u0275property"]("ngTemplateOutlet",e)}}function We(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",32),i["\u0275\u0275elementStart"](1,"div",33),i["\u0275\u0275text"](2),i["\u0275\u0275template"](3,He,2,0,"span",13),i["\u0275\u0275template"](4,je,1,1,void 0,13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"app-exp-input-search",78),i["\u0275\u0275listener"]("change",(function(t){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"](2).changeSpentFrom(t)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"](2);let t=null,n=null;i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"](" ",null!=e.fieldConfig&&null!=e.fieldConfig.spentFrom&&e.fieldConfig.spentFrom.field_label?e.fieldConfig.spentFrom.field_label:"Spent From"," "),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null==e.fieldConfig||null==e.fieldConfig.spentFrom?null:e.fieldConfig.spentFrom.is_mandatory),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",(null==(t=e.claimForm.get("spentFrom"))?null:t.hasError("required"))&&(null==(t=e.claimForm.get("spentFrom"))?null:t.touched)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction1"](9,ae,(null==(n=e.claimForm.get("spentFrom"))?null:n.hasError("required"))&&(null==(n=e.claimForm.get("spentFrom"))?null:n.touched)))("placeholder","Spent From")("list",e.SpentFromList)("disabled",!(null==e.fieldConfig||null==e.fieldConfig.spentFrom||!e.fieldConfig.spentFrom.is_disabled))("bgColorOnDisable",!(null==e.fieldConfig||null==e.fieldConfig.spentFrom||!e.fieldConfig.spentFrom.is_disabled))("hideMatLabel",!0)}}function $e(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span"),i["\u0275\u0275text"](1,"*"),i["\u0275\u0275elementEnd"]())}function Ue(e,t){}function ze(e,t){if(1&e&&i["\u0275\u0275template"](0,Ue,0,0,"ng-template",51),2&e){i["\u0275\u0275nextContext"](2);const e=i["\u0275\u0275reference"](20);i["\u0275\u0275property"]("ngTemplateOutlet",e)}}function Ge(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",32),i["\u0275\u0275elementStart"](1,"div",33),i["\u0275\u0275text"](2),i["\u0275\u0275template"](3,$e,2,0,"span",13),i["\u0275\u0275elementStart"](4,"mat-icon",79),i["\u0275\u0275text"](5," info_outline "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](6,ze,1,1,void 0,13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](7,"app-exp-input-search",80),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](2);let t=null,n=null;i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"]("",null!=e.fieldConfig&&null!=e.fieldConfig.corporateCard&&e.fieldConfig.corporateCard.field_label?e.fieldConfig.corporateCard.field_label:"Corporate Card"," "),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",(null==e.fieldConfig||null==e.fieldConfig.corporateCard?null:e.fieldConfig.corporateCard.is_mandatory)&&(null==e.corporateCardList?null:e.corporateCardList.length)),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngIf",(null==(t=e.claimForm.get("corporateCard"))?null:t.hasError("required"))&&(null==(t=e.claimForm.get("corporateCard"))?null:t.touched)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction1"](9,ae,(null==(n=e.claimForm.get("corporateCard"))?null:n.hasError("required"))&&(null==(n=e.claimForm.get("corporateCard"))?null:n.touched)))("placeholder","Select Corporate Card ")("list",e.corporateCardList)("disabled",(null==e.fieldConfig||null==e.fieldConfig.corporateCard?null:e.fieldConfig.corporateCard.is_disabled)||!(null!=e.corporateCardList&&e.corporateCardList.length))("bgColorOnDisable",(null==e.fieldConfig||null==e.fieldConfig.corporateCard?null:e.fieldConfig.corporateCard.is_disabled)||!(null!=e.corporateCardList&&e.corporateCardList.length))("hideMatLabel",!0)}}function Je(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span",81),i["\u0275\u0275text"](1,"This field is required"),i["\u0275\u0275elementEnd"]())}function Ke(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span",82),i["\u0275\u0275text"](1," Highlites"),i["\u0275\u0275elementEnd"]())}function Xe(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"span",83),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" Last ",e.restrictionBilledDate," days, no future dates allowed ")}}function Qe(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span",84),i["\u0275\u0275text"](1," Future dates are not allowed "),i["\u0275\u0275elementEnd"]())}function Ze(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span",85),i["\u0275\u0275text"](1," End date and time cannot be earlier than the Start date and time. "),i["\u0275\u0275elementEnd"]())}function et(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span"),i["\u0275\u0275text"](1,"*"),i["\u0275\u0275elementEnd"]())}function tt(e,t){}function nt(e,t){if(1&e&&i["\u0275\u0275template"](0,tt,0,0,"ng-template",51),2&e){i["\u0275\u0275nextContext"](3);const e=i["\u0275\u0275reference"](20);i["\u0275\u0275property"]("ngTemplateOutlet",e)}}function lt(e,t){}function it(e,t){if(1&e&&i["\u0275\u0275template"](0,lt,0,0,"ng-template",51),2&e){i["\u0275\u0275nextContext"](3);const e=i["\u0275\u0275reference"](24);i["\u0275\u0275property"]("ngTemplateOutlet",e)}}function ot(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",32),i["\u0275\u0275elementStart"](1,"div",33),i["\u0275\u0275text"](2),i["\u0275\u0275template"](3,et,2,0,"span",13),i["\u0275\u0275template"](4,nt,1,1,void 0,13),i["\u0275\u0275template"](5,it,1,1,void 0,13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](6,"mat-form-field",52,53),i["\u0275\u0275elementStart"](8,"input",107),i["\u0275\u0275listener"]("dateChange",(function(t){i["\u0275\u0275restoreView"](e);const n=i["\u0275\u0275nextContext"]().index;return i["\u0275\u0275nextContext"](2).onInvoiceDateChange(t,n)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](9,"mat-datepicker-toggle",108),i["\u0275\u0275element"](10,"mat-datepicker",null,109),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275reference"](11),t=i["\u0275\u0275nextContext"]().index,n=i["\u0275\u0275nextContext"](2);let l=null,o=null,a=null,r=null;i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"]("",null!=n.fieldConfig&&null!=n.fieldConfig.invoiceDate&&n.fieldConfig.invoiceDate.field_label?n.fieldConfig.invoiceDate.field_label:"Billed Date"," "),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null==n.fieldConfig||null==n.fieldConfig.invoiceDate?null:n.fieldConfig.invoiceDate.is_mandatory),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",(null==(l=n.claimForm.get("claims").at(t))?null:l.get("invoiceDate").hasError("required"))&&(null==(l=n.claimForm.get("claims").at(t))?null:l.get("invoiceDate").touched)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",(null==(o=n.claimForm.get("claims").at(t))?null:o.get("invoiceDate").hasError("invalidInvoiceDate"))&&(null==(o=n.claimForm.get("claims").at(t))?null:o.get("invoiceDate").touched)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction2"](11,Ee,1==(null==n.fieldConfig||null==n.fieldConfig.invoiceDate?null:n.fieldConfig.invoiceDate.is_disabled),(null==(a=n.claimForm.get("claims").at(t))||null==(r=a.get("invoiceDate"))?null:r.hasError("required"))&&(null==(a=n.claimForm.get("claims").at(t))||null==(r=a.get("invoiceDate"))?null:r.touched))),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("matDatepicker",e)("min",n.billingStartDate)("max",n.tomorrow)("required",!(null==n.fieldConfig||null==n.fieldConfig.invoiceDate||!n.fieldConfig.invoiceDate.is_mandatory))("disabled",!(null==n.fieldConfig||null==n.fieldConfig.invoiceDate||!n.fieldConfig.invoiceDate.is_disabled)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("for",e)}}function at(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span"),i["\u0275\u0275text"](1,"*"),i["\u0275\u0275elementEnd"]())}function rt(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",58),i["\u0275\u0275elementStart"](1,"div",33),i["\u0275\u0275text"](2),i["\u0275\u0275template"](3,at,2,0,"span",13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"mat-form-field",74,53),i["\u0275\u0275element"](6,"input",110),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](3);i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"]("",null!=e.fieldConfig&&null!=e.fieldConfig.invoiceNo&&e.fieldConfig.invoiceNo.field_label?e.fieldConfig.invoiceNo.field_label:"Invoice No"," "),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null==e.fieldConfig||null==e.fieldConfig.invoiceNo?null:e.fieldConfig.invoiceNo.is_mandatory),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction1"](6,se,1==(null==e.fieldConfig||null==e.fieldConfig.invoiceNo?null:e.fieldConfig.invoiceNo.is_disabled))),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("placeholder",null!=e.fieldConfig&&null!=e.fieldConfig.invoiceNo&&e.fieldConfig.invoiceNo.field_label?e.fieldConfig.invoiceNo.field_label:"invoiceNo *")("readonly",!(null==e.fieldConfig||null==e.fieldConfig.invoiceNo||!e.fieldConfig.invoiceNo.is_disabled))("required",!(null==e.fieldConfig||null==e.fieldConfig.invoiceNo||!e.fieldConfig.invoiceNo.is_mandatory))}}function st(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span"),i["\u0275\u0275text"](1,"*"),i["\u0275\u0275elementEnd"]())}function ct(e,t){}function dt(e,t){if(1&e&&i["\u0275\u0275template"](0,ct,0,0,"ng-template",51),2&e){i["\u0275\u0275nextContext"](3);const e=i["\u0275\u0275reference"](20);i["\u0275\u0275property"]("ngTemplateOutlet",e)}}function mt(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",111),i["\u0275\u0275elementStart"](1,"div",33),i["\u0275\u0275text"](2),i["\u0275\u0275template"](3,st,2,0,"span",13),i["\u0275\u0275template"](4,dt,1,1,void 0,13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"mat-form-field",52,53),i["\u0275\u0275element"](7,"input",112),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]().index,t=i["\u0275\u0275nextContext"](2);let n=null,l=null,o=null;i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"]("",null!=t.fieldConfig&&null!=t.fieldConfig.description&&t.fieldConfig.description.field_label?t.fieldConfig.description.field_label:"Description"," "),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null==t.fieldConfig||null==t.fieldConfig.description?null:t.fieldConfig.description.is_mandatory),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",(null==(n=t.claimForm.get("claims").at(e))?null:n.get("description").hasError("required"))&&(null==(n=t.claimForm.get("claims").at(e))?null:n.get("description").touched)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction2"](7,Ee,1==(null==t.fieldConfig||null==t.fieldConfig.description?null:t.fieldConfig.description.is_disabled),(null==(l=t.claimForm.get("claims").at(e))||null==(o=l.get("description"))?null:o.hasError("required"))&&(null==(l=t.claimForm.get("claims").at(e))||null==(o=l.get("description"))?null:o.touched))),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("placeholder",null!=t.fieldConfig&&null!=t.fieldConfig.description&&t.fieldConfig.description.field_label?t.fieldConfig.description.field_label:"Description")("required",!(null==t.fieldConfig||null==t.fieldConfig.description||!t.fieldConfig.description.is_mandatory))("readonly",!(null==t.fieldConfig||null==t.fieldConfig.description||!t.fieldConfig.description.is_disabled))}}function pt(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span"),i["\u0275\u0275text"](1,"*"),i["\u0275\u0275elementEnd"]())}function ut(e,t){}function ft(e,t){if(1&e&&i["\u0275\u0275template"](0,ut,0,0,"ng-template",51),2&e){i["\u0275\u0275nextContext"](3);const e=i["\u0275\u0275reference"](20);i["\u0275\u0275property"]("ngTemplateOutlet",e)}}function gt(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",32),i["\u0275\u0275elementStart"](1,"div",33),i["\u0275\u0275text"](2,"Claim Type "),i["\u0275\u0275template"](3,pt,2,0,"span",13),i["\u0275\u0275template"](4,ft,1,1,void 0,13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"app-exp-input-search",113),i["\u0275\u0275listener"]("change",(function(t){i["\u0275\u0275restoreView"](e);const n=i["\u0275\u0275nextContext"]().index;return i["\u0275\u0275nextContext"](2).changeClaimType(t,n)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"]().index,t=i["\u0275\u0275nextContext"](2);let n=null,l=null,o=null;i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngIf",null==t.fieldConfig||null==t.fieldConfig.category?null:t.fieldConfig.category.is_mandatory),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",(null==(n=t.claimForm.get("claims").at(e))?null:n.get("category").hasError("required"))&&(null==(n=t.claimForm.get("claims").at(e))?null:n.get("category").touched)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction1"](7,ae,(null==(l=t.claimForm.get("claims").at(e))||null==(o=l.get("category"))?null:o.hasError("required"))&&(null==(l=t.claimForm.get("claims").at(e))||null==(o=l.get("category"))?null:o.touched)))("list",t.claimType)("disabled",!(null==t.fieldConfig||null==t.fieldConfig.category||!t.fieldConfig.category.is_disabled))("bgColorOnDisable",!(null==t.fieldConfig||null==t.fieldConfig.category||!t.fieldConfig.category.is_disabled))("hideMatLabel",!0)}}function ht(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"mat-option",117),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const n=t.$implicit,l=i["\u0275\u0275nextContext"](2).index;return i["\u0275\u0275nextContext"](2).onManualApproverSelection(n,l)})),i["\u0275\u0275text"](1),i["\u0275\u0275element"](2,"span"),i["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;i["\u0275\u0275property"]("value",e.oid),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate2"](" ",e.manager_type," - ",e.name," - ")}}function vt(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",58),i["\u0275\u0275elementStart"](1,"div",33),i["\u0275\u0275text"](2),i["\u0275\u0275elementStart"](3,"mat-icon",114),i["\u0275\u0275text"](4," info_outline "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"mat-form-field",74,53),i["\u0275\u0275elementStart"](7,"mat-select",115),i["\u0275\u0275template"](8,ht,3,3,"mat-option",116),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](3);i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"]("",null!=e.fieldConfig&&null!=e.fieldConfig.manualApprover&&e.fieldConfig.manualApprover.field_label?e.fieldConfig.manualApprover.field_label:"Select Approver *"," "),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction1"](6,se,!(null!=e.approversList&&e.approversList.length))),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("disabled",!(null==e.fieldConfig||null==e.fieldConfig.manualApprover||!e.fieldConfig.manualApprover.is_disabled))("required",!(null==e.fieldConfig||null==e.fieldConfig.manualApprover||!e.fieldConfig.manualApprover.is_mandatory))("placeholder",null==e.fieldConfig||null==e.fieldConfig.manualApprover?null:e.fieldConfig.manualApprover.field_label),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngForOf",e.approversList)}}function Ct(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",69),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](2,"div",69),i["\u0275\u0275text"](3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"div",69),i["\u0275\u0275text"](5),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]().$implicit;i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" ",e.name," "),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"](" ",e.designation," "),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"](" Level ",e.level," ")}}function xt(e,t){if(1&e&&(i["\u0275\u0275elementContainerStart"](0),i["\u0275\u0275element"](1,"app-user-image",67),i["\u0275\u0275template"](2,Ct,6,3,"ng-template",null,68,i["\u0275\u0275templateRefExtractor"]),i["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit,n=i["\u0275\u0275reference"](3);i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("tooltip",n)("id",e.oid)}}function yt(e,t){if(1&e&&(i["\u0275\u0275elementContainerStart"](0),i["\u0275\u0275template"](1,xt,4,2,"ng-container",66),i["\u0275\u0275elementContainerEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](2).index,t=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngForOf",t.claimForm.value.claims[e].approvers)}}function bt(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",65),i["\u0275\u0275template"](1,yt,2,1,"ng-container",13),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]().$implicit;i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",""!=e.approvers)}}function _t(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",69),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](2,"div",69),i["\u0275\u0275text"](3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"div",69),i["\u0275\u0275text"](5),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]().$implicit;i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" ",e.name," "),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"](" ",e.designation," "),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"](" Level ",e.level," ")}}function Et(e,t){if(1&e&&(i["\u0275\u0275elementContainerStart"](0),i["\u0275\u0275element"](1,"app-user-image",67),i["\u0275\u0275template"](2,_t,6,3,"ng-template",null,68,i["\u0275\u0275templateRefExtractor"]),i["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit,n=i["\u0275\u0275reference"](3);i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("tooltip",n)("id",e.oid)}}function Ft(e,t){if(1&e&&(i["\u0275\u0275elementContainerStart"](0),i["\u0275\u0275elementStart"](1,"p",119),i["\u0275\u0275text"](2,"Approvers"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](3,Et,4,2,"ng-container",66),i["\u0275\u0275elementContainerEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](2).index,t=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngForOf",t.claimForm.value.claims[e].approvers)}}function It(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",118),i["\u0275\u0275template"](1,Ft,4,1,"ng-container",13),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]().$implicit;i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",""!=e.approvers)}}function wt(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span"),i["\u0275\u0275text"](1,"*"),i["\u0275\u0275elementEnd"]())}function St(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",32),i["\u0275\u0275elementStart"](1,"div",33),i["\u0275\u0275text"](2),i["\u0275\u0275template"](3,wt,2,0,"span",13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"app-exp-input-search",120),i["\u0275\u0275listener"]("change",(function(){i["\u0275\u0275restoreView"](e);const t=i["\u0275\u0275nextContext"]().index;return i["\u0275\u0275nextContext"](2).ontaxRatetypeChange(t)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"](3);i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"]("",null!=e.fieldConfig&&null!=e.fieldConfig.taxRatetype&&e.fieldConfig.taxRatetype.field_label?e.fieldConfig.taxRatetype.field_label:"taxRatetype"," "),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null==e.fieldConfig||null==e.fieldConfig.taxRatetype?null:e.fieldConfig.taxRatetype.is_mandatory),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("placeholder",null!=e.fieldConfig&&null!=e.fieldConfig.taxRatetype&&e.fieldConfig.taxRatetype.field_label?e.fieldConfig.taxRatetype.field_label:"taxRatetype")("list",e.getTaxRateType)("disabled",!(null==e.fieldConfig||null==e.fieldConfig.taxRatetype||!e.fieldConfig.taxRatetype.is_disabled))("bgColorOnDisable",!(null==e.fieldConfig||null==e.fieldConfig.taxRatetype||!e.fieldConfig.taxRatetype.is_disabled))("hideMatLabel",!0)}}function Dt(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span"),i["\u0275\u0275text"](1,"*"),i["\u0275\u0275elementEnd"]())}function Ot(e,t){}function At(e,t){if(1&e&&i["\u0275\u0275template"](0,Ot,0,0,"ng-template",51),2&e){i["\u0275\u0275nextContext"](3);const e=i["\u0275\u0275reference"](20);i["\u0275\u0275property"]("ngTemplateOutlet",e)}}function Mt(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",32),i["\u0275\u0275elementStart"](1,"div",33),i["\u0275\u0275text"](2),i["\u0275\u0275template"](3,Dt,2,0,"span",13),i["\u0275\u0275template"](4,At,1,1,void 0,13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"app-exp-input-search",121),i["\u0275\u0275listener"]("change",(function(){i["\u0275\u0275restoreView"](e);const t=i["\u0275\u0275nextContext"]().index;return i["\u0275\u0275nextContext"](2).itemCurrencyChange(t)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"]().index,t=i["\u0275\u0275nextContext"](2);let n=null,l=null,o=null;i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"]("",null!=t.fieldConfig&&null!=t.fieldConfig.currency&&t.fieldConfig.currency.field_label?t.fieldConfig.currency.field_label:"currency"," "),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null==t.fieldConfig||null==t.fieldConfig.currency?null:t.fieldConfig.currency.is_mandatory),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",(null==(n=t.claimForm.get("claims").at(e))?null:n.get("currencyCode").hasError("required"))&&(null==(n=t.claimForm.get("claims").at(e))?null:n.get("currencyCode").touched)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction1"](9,ae,(null==(l=t.claimForm.get("claims").at(e))||null==(o=l.get("currencyCode"))?null:o.hasError("required"))&&(null==(l=t.claimForm.get("claims").at(e))||null==(o=l.get("currencyCode"))?null:o.touched)))("placeholder",null!=t.fieldConfig&&null!=t.fieldConfig.currency&&t.fieldConfig.currency.field_label?t.fieldConfig.currency.field_label:"currency")("list",t.currencyList)("disabled",!(null==t.fieldConfig||null==t.fieldConfig.currency||!t.fieldConfig.currency.is_disabled))("bgColorOnDisable",!(null==t.fieldConfig||null==t.fieldConfig.currency||!t.fieldConfig.currency.is_disabled))("hideMatLabel",!0)}}function Tt(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span"),i["\u0275\u0275text"](1,"*"),i["\u0275\u0275elementEnd"]())}function Pt(e,t){}function kt(e,t){if(1&e&&i["\u0275\u0275template"](0,Pt,0,0,"ng-template",51),2&e){i["\u0275\u0275nextContext"](3);const e=i["\u0275\u0275reference"](20);i["\u0275\u0275property"]("ngTemplateOutlet",e)}}function Vt(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"span",127),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](2).index,t=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" ",t.claimForm.value.claims[e].currenyCodeValue," ")}}function Lt(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span"),i["\u0275\u0275text"](1," Amount can't be 0, -ve "),i["\u0275\u0275elementEnd"]())}function Bt(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span"),i["\u0275\u0275text"](1," ,empty "),i["\u0275\u0275elementEnd"]())}function Nt(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span"),i["\u0275\u0275text"](1," , cannot exceed 15 digits before the decimal. "),i["\u0275\u0275elementEnd"]())}function Rt(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-hint",128),i["\u0275\u0275template"](1,Lt,2,0,"span",13),i["\u0275\u0275template"](2,Bt,2,0,"span",13),i["\u0275\u0275template"](3,Nt,2,0,"span",13),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](2).index,t=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",!t.claimForm.controls.claims.controls[e].controls.amount.errors.validAmount),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.claimForm.controls.claims.controls[e].controls.amount.errors.required),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null==t.claimForm.controls.claims.controls[e].controls.amount.errors?null:t.claimForm.controls.claims.controls[e].controls.amount.errors.maxDigitsExceeded)}}function qt(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",32),i["\u0275\u0275elementStart"](1,"div",122),i["\u0275\u0275text"](2),i["\u0275\u0275template"](3,Tt,2,0,"span",13),i["\u0275\u0275template"](4,kt,1,1,void 0,13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"mat-form-field",52),i["\u0275\u0275elementStart"](6,"div",123),i["\u0275\u0275elementStart"](7,"input",124),i["\u0275\u0275listener"]("input",(function(t){i["\u0275\u0275restoreView"](e);const n=i["\u0275\u0275nextContext"]().index;return i["\u0275\u0275nextContext"](2).itemAmountChange(t,n)}))("focusout",(function(){i["\u0275\u0275restoreView"](e);const t=i["\u0275\u0275nextContext"]().index;return i["\u0275\u0275nextContext"](2).onPaymentMadeChange(t)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](8,Vt,2,1,"span",125),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](9,Rt,4,3,"mat-hint",126),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"]().index,t=i["\u0275\u0275nextContext"](2);let n=null,l=null,o=null;i["\u0275\u0275advance"](1),i["\u0275\u0275propertyInterpolate"]("matTooltip",null!=t.fieldConfig&&null!=t.fieldConfig.amount&&t.fieldConfig.amount.field_label?t.fieldConfig.amount.field_label:"Amount *"),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" ",null!=t.fieldConfig&&null!=t.fieldConfig.amount&&t.fieldConfig.amount.field_label?t.fieldConfig.amount.field_label:"Amount *"," "),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null==t.fieldConfig||null==t.fieldConfig.amount?null:t.fieldConfig.amount.is_mandatory),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",(null==(n=t.claimForm.get("claims").at(e))?null:n.get("amount").hasError("required"))&&(null==(n=t.claimForm.get("claims").at(e))?null:n.get("amount").touched)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction2"](10,Ee,1==(null==t.fieldConfig||null==t.fieldConfig.amount?null:t.fieldConfig.amount.is_disabled),(null==(l=t.claimForm.get("claims").at(e))||null==(o=l.get("amount"))?null:o.hasError("required"))&&(null==(l=t.claimForm.get("claims").at(e))||null==(o=l.get("amount"))?null:o.touched))),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("placeholder",null!=t.fieldConfig&&null!=t.fieldConfig.amount&&t.fieldConfig.amount.field_label?t.fieldConfig.amount.field_label:"Amount *")("maxlength",15)("readonly",!(null==t.fieldConfig||null==t.fieldConfig.amount||!t.fieldConfig.amount.is_disabled)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.claimForm.value.claims[e].currenyCodeValue),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.claimForm.controls.claims.controls[e].controls.amount.invalid&&(t.claimForm.controls.claims.controls[e].controls.amount.touched||t.claimForm.controls.claims.controls[e].controls.amount.dirty))}}function Ht(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span"),i["\u0275\u0275text"](1,"*"),i["\u0275\u0275elementEnd"]())}function Yt(e,t){}function jt(e,t){if(1&e&&i["\u0275\u0275template"](0,Yt,0,0,"ng-template",51),2&e){i["\u0275\u0275nextContext"](3);const e=i["\u0275\u0275reference"](20);i["\u0275\u0275property"]("ngTemplateOutlet",e)}}function Wt(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",32),i["\u0275\u0275elementStart"](1,"div",33),i["\u0275\u0275text"](2),i["\u0275\u0275template"](3,Ht,2,0,"span",13),i["\u0275\u0275template"](4,jt,1,1,void 0,13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"app-exp-input-search",129),i["\u0275\u0275listener"]("change",(function(){i["\u0275\u0275restoreView"](e);const t=i["\u0275\u0275nextContext"]().index,n=i["\u0275\u0275nextContext"](2);return n.ontaxPercentageChange(t),n.updateTaxAmountValidator(t)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"]().index,t=i["\u0275\u0275nextContext"](2);let n=null,l=null,o=null;i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"]("",null!=t.fieldConfig&&null!=t.fieldConfig.taxPercentage&&t.fieldConfig.taxPercentage.field_label?t.fieldConfig.taxPercentage.field_label:"Tax Percentage"," "),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null==t.fieldConfig||null==t.fieldConfig.taxPercentage?null:t.fieldConfig.taxPercentage.is_mandatory),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",(null==(n=t.claimForm.get("claims").at(e))?null:n.get("taxPercentage").hasError("required"))&&(null==(n=t.claimForm.get("claims").at(e))?null:n.get("taxPercentage").touched)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction1"](9,ae,(null==(l=t.claimForm.get("claims").at(e))||null==(o=l.get("taxPercentage"))?null:o.hasError("required"))&&(null==(l=t.claimForm.get("claims").at(e))||null==(o=l.get("taxPercentage"))?null:o.touched)))("placeholder",null!=t.fieldConfig&&null!=t.fieldConfig.taxPercentage&&t.fieldConfig.taxPercentage.field_label?t.fieldConfig.taxPercentage.field_label:"Tax Percentage")("list",t.defaulttaxPercentage)("disabled",!(null==t.fieldConfig||null==t.fieldConfig.taxPercentage||!t.fieldConfig.taxPercentage.is_disabled))("bgColorOnDisable",!(null==t.fieldConfig||null==t.fieldConfig.taxPercentage||!t.fieldConfig.taxPercentage.is_disabled))("hideMatLabel",!0)}}function $t(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span"),i["\u0275\u0275text"](1,"*"),i["\u0275\u0275elementEnd"]())}function Ut(e,t){}function zt(e,t){if(1&e&&i["\u0275\u0275template"](0,Ut,0,0,"ng-template",51),2&e){i["\u0275\u0275nextContext"](3);const e=i["\u0275\u0275reference"](20);i["\u0275\u0275property"]("ngTemplateOutlet",e)}}function Gt(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"span",127),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](2).index,t=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](t.claimForm.value.claims[e].currenyCodeValue)}}function Jt(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span"),i["\u0275\u0275text"](1," Amount can't be 0, -ve "),i["\u0275\u0275elementEnd"]())}function Kt(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span"),i["\u0275\u0275text"](1," ,empty "),i["\u0275\u0275elementEnd"]())}function Xt(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-hint",128),i["\u0275\u0275template"](1,Jt,2,0,"span",13),i["\u0275\u0275template"](2,Kt,2,0,"span",13),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](2).index,t=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",!t.claimForm.controls.claims.controls[e].controls.taxAmount.errors.validAmount),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",(null==t.claimForm.controls.claims.controls[e].controls.taxAmount.errors?null:t.claimForm.controls.claims.controls[e].controls.taxAmount.errors.required)&&1e3==t.claimForm.value.claims[e].taxPercentage)}}function Qt(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",32),i["\u0275\u0275elementStart"](1,"div",33),i["\u0275\u0275text"](2),i["\u0275\u0275template"](3,$t,2,0,"span",13),i["\u0275\u0275template"](4,zt,1,1,void 0,13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"mat-form-field",52,53),i["\u0275\u0275elementStart"](7,"div",123),i["\u0275\u0275elementStart"](8,"input",130),i["\u0275\u0275listener"]("focusout",(function(){i["\u0275\u0275restoreView"](e);const t=i["\u0275\u0275nextContext"]().index;return i["\u0275\u0275nextContext"](2).onTaxAmountFieldValueChange(t)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](9,Gt,2,1,"span",125),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](10,Xt,3,2,"mat-hint",126),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"]().index,t=i["\u0275\u0275nextContext"](2);let n=null,l=null,o=null;i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"]("",null!=t.fieldConfig&&null!=t.fieldConfig.taxAmount&&t.fieldConfig.taxAmount.field_label?t.fieldConfig.taxAmount.field_label:"taxAmount"," "),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null==t.fieldConfig||null==t.fieldConfig.taxAmount?null:t.fieldConfig.taxAmount.is_mandatory),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",(null==(n=t.claimForm.get("claims").at(e))?null:n.get("taxAmount").hasError("required"))&&(null==(n=t.claimForm.get("claims").at(e))?null:n.get("taxAmount").touched)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction2"](8,Ee,1e3!=t.claimForm.value.claims[e].taxPercentage,(null==(l=t.claimForm.get("claims").at(e))||null==(o=l.get("taxAmount"))?null:o.hasError("required"))&&(null==(l=t.claimForm.get("claims").at(e))||null==(o=l.get("taxAmount"))?null:o.touched))),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("placeholder",null!=t.fieldConfig&&null!=t.fieldConfig.taxAmount&&t.fieldConfig.taxAmount.field_label?t.fieldConfig.taxAmount.field_label:"taxAmount")("readonly",1e3!=t.claimForm.value.claims[e].taxPercentage),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.claimForm.value.claims[e].currenyCodeValue),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.claimForm.controls.claims.controls[e].controls.taxAmount.invalid&&(t.claimForm.controls.claims.controls[e].controls.taxAmount.touched||t.claimForm.controls.claims.controls[e].controls.taxAmount.dirty))}}function Zt(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span"),i["\u0275\u0275text"](1,"*"),i["\u0275\u0275elementEnd"]())}const en=function(){return["displayName","associate_id"]};function tn(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div"),i["\u0275\u0275elementStart"](1,"div",33),i["\u0275\u0275text"](2),i["\u0275\u0275template"](3,Zt,2,0,"span",13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"app-input-search-people-involved-dropdown",131),i["\u0275\u0275listener"]("selectedValues",(function(t){i["\u0275\u0275restoreView"](e);const n=i["\u0275\u0275nextContext"]().index;return i["\u0275\u0275nextContext"](2).getPeopleInvolved(t,n)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"]().index,t=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"]("",null!=t.fieldConfig&&null!=t.fieldConfig.peopleInvolved&&t.fieldConfig.peopleInvolved.field_label?t.fieldConfig.peopleInvolved.field_label:"People Involved"," "),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null==t.fieldConfig||null==t.fieldConfig.peopleInvolved?null:t.fieldConfig.peopleInvolved.is_mandatory),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("token",t.token)("optionLabel",i["\u0275\u0275pureFunction0"](9,en))("API_URL",t.employeeSearchUrl)("fieldValue",t.claimForm.controls.claims.controls[e].controls.peopleInvolved.value)("disabled",!(null==t.fieldConfig||null==t.fieldConfig.peopleInvolved||!t.fieldConfig.peopleInvolved.is_disabled))("bgColorOnDisable",!(null==t.fieldConfig||null==t.fieldConfig.peopleInvolved||!t.fieldConfig.peopleInvolved.is_disabled))("placeholder",null!=t.fieldConfig&&null!=t.fieldConfig.peopleInvolved&&t.fieldConfig.peopleInvolved.field_label?t.fieldConfig.peopleInvolved.field_label:"People Involved")}}function nn(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",136),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const t=i["\u0275\u0275nextContext"](2).index,n=i["\u0275\u0275nextContext"](2);return n.openExpensePeopleInvolved(n.claimForm.controls.claims.controls[t].controls.peopleInvolved.value)})),i["\u0275\u0275elementStart"](1,"div",137),i["\u0275\u0275elementStart"](2,"div"),i["\u0275\u0275element"](3,"img",138),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=i["\u0275\u0275nextContext"](4);i["\u0275\u0275advance"](3),i["\u0275\u0275styleProp"]("width",n.width,"px")("height",n.height,"px"),i["\u0275\u0275propertyInterpolate"]("matTooltip",e.displayName)}}function ln(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",132),i["\u0275\u0275elementStart"](1,"div",133),i["\u0275\u0275template"](2,nn,4,5,"div",134),i["\u0275\u0275elementStart"](3,"div",135),i["\u0275\u0275text"](4),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]().index,t=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngForOf",t.claimForm.controls.claims.controls[e].controls.peopleInvolved.value.slice(0,2)),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"](" +",t.claimForm.controls.claims.controls[e].controls.peopleInvolved.value.length-2," ")}}function on(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",141),i["\u0275\u0275elementStart"](1,"div",137),i["\u0275\u0275elementStart"](2,"div"),i["\u0275\u0275element"](3,"img",138),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=i["\u0275\u0275nextContext"](4);i["\u0275\u0275advance"](3),i["\u0275\u0275styleProp"]("width",n.width,"px")("height",n.height,"px"),i["\u0275\u0275propertyInterpolate"]("matTooltip",e.displayName)}}function an(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div"),i["\u0275\u0275elementStart"](1,"div",139),i["\u0275\u0275template"](2,on,4,5,"div",140),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]().index,t=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngForOf",null==t.claimForm.controls.claims.controls[e].controls.peopleInvolved.value?null:t.claimForm.controls.claims.controls[e].controls.peopleInvolved.value.slice(0,2))}}function rn(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span"),i["\u0275\u0275text"](1,"*"),i["\u0275\u0275elementEnd"]())}function sn(e,t){}function cn(e,t){if(1&e&&i["\u0275\u0275template"](0,sn,0,0,"ng-template",51),2&e){i["\u0275\u0275nextContext"](3);const e=i["\u0275\u0275reference"](20);i["\u0275\u0275property"]("ngTemplateOutlet",e)}}function dn(e,t){}function mn(e,t){if(1&e&&i["\u0275\u0275template"](0,dn,0,0,"ng-template",51),2&e){i["\u0275\u0275nextContext"](3);const e=i["\u0275\u0275reference"](26);i["\u0275\u0275property"]("ngTemplateOutlet",e)}}function pn(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",142),i["\u0275\u0275elementStart"](1,"div",33),i["\u0275\u0275text"](2),i["\u0275\u0275template"](3,rn,2,0,"span",13),i["\u0275\u0275template"](4,cn,1,1,void 0,13),i["\u0275\u0275template"](5,mn,1,1,void 0,13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](6,"mat-form-field",52,53),i["\u0275\u0275elementStart"](8,"input",143),i["\u0275\u0275listener"]("dateChange",(function(){i["\u0275\u0275restoreView"](e);const t=i["\u0275\u0275nextContext"]().index,n=i["\u0275\u0275nextContext"](2);return n.calculateNoOfDays(t),n.validateEndDateTime(t)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](9,"mat-datepicker-toggle",144),i["\u0275\u0275element"](10,"mat-datepicker",null,145),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275reference"](11),t=i["\u0275\u0275nextContext"]().index,n=i["\u0275\u0275nextContext"](2);let l=null,o=null,a=null,r=null,s=null,c=null;i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"](" ",null!=n.fieldConfig&&null!=n.fieldConfig.perDiemstartDate&&n.fieldConfig.perDiemstartDate.field_label?n.fieldConfig.perDiemstartDate.field_label:"Start Date"," "),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null==n.fieldConfig||null==n.fieldConfig.perDiemstartDate?null:n.fieldConfig.perDiemstartDate.is_mandatory),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",(null==(l=n.claimForm.get("claims").at(t))||null==(o=l.get("perDiemstartDate"))?null:o.hasError("required"))&&(null==(l=n.claimForm.get("claims").at(t))||null==(o=l.get("perDiemstartDate"))?null:o.touched)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",(null==(a=n.claimForm.get("claims").at(t))||null==(r=a.get("perDiemstartDate"))?null:r.hasError("invalidInvoiceDate"))&&(null==(a=n.claimForm.get("claims").at(t))||null==(r=a.get("perDiemstartDate"))?null:r.touched)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction2"](12,Ee,1==(null==n.fieldConfig||null==n.fieldConfig.perDiemstartDate?null:n.fieldConfig.perDiemstartDate.is_disabled),(null==(s=n.claimForm.get("claims").at(t))||null==(c=s.get("perDiemstartDate"))?null:c.hasError("required"))&&(null==(s=n.claimForm.get("claims").at(t))||null==(c=s.get("perDiemstartDate"))?null:c.touched))),i["\u0275\u0275advance"](2),i["\u0275\u0275propertyInterpolate"]("placeholder",null!=n.fieldConfig&&null!=n.fieldConfig.perDiemstartDate&&n.fieldConfig.perDiemstartDate.field_label?n.fieldConfig.perDiemstartDate.field_label:"Start Date"),i["\u0275\u0275property"]("matDatepicker",e)("max",n.tomorrow)("required",!(null==n.fieldConfig||null==n.fieldConfig.perDiemstartDate||!n.fieldConfig.perDiemstartDate.is_mandatory))("readonly",!(null==n.fieldConfig||null==n.fieldConfig.perDiemstartDate||!n.fieldConfig.perDiemstartDate.is_disabled))("disabled",!(null==n.fieldConfig||null==n.fieldConfig.perDiemstartDate||!n.fieldConfig.perDiemstartDate.is_disabled)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("for",e)}}function un(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span"),i["\u0275\u0275text"](1,"*"),i["\u0275\u0275elementEnd"]())}function fn(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",142),i["\u0275\u0275elementStart"](1,"div",33),i["\u0275\u0275text"](2),i["\u0275\u0275template"](3,un,2,0,"span",13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"mat-form-field",52,53),i["\u0275\u0275elementStart"](6,"input",146,147),i["\u0275\u0275listener"]("keydown",(function(t){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"](3).onKeyDownPreventInvalidChars(t)}))("paste",(function(t){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"](3).preventPaste(t)}))("keyup",(function(t){i["\u0275\u0275restoreView"](e);const n=i["\u0275\u0275nextContext"]().index;return i["\u0275\u0275nextContext"](2).onInputChangeFormatTime(t,n)}))("change",(function(){i["\u0275\u0275restoreView"](e);const t=i["\u0275\u0275nextContext"]().index,n=i["\u0275\u0275nextContext"](2);return n.calculateNoOfDays(t),n.validateEndDateTime(t)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](8,"mat-icon",148),i["\u0275\u0275text"](9,"alarm"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"]().index,t=i["\u0275\u0275nextContext"](2);let n=null,l=null;i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"](" ",null!=t.fieldConfig&&null!=t.fieldConfig.perDiemStartTime&&t.fieldConfig.perDiemStartTime.field_label?t.fieldConfig.perDiemStartTime.field_label:"Start Time"," "),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null==t.fieldConfig||null==t.fieldConfig.perDiemStartTime?null:t.fieldConfig.perDiemStartTime.is_mandatory),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction2"](5,Ee,1==(null==t.fieldConfig||null==t.fieldConfig.perDiemStartTime?null:t.fieldConfig.perDiemStartTime.is_disabled),(null==(n=t.claimForm.get("claims").at(e))||null==(l=n.get("perDiemStartTime"))?null:l.hasError("required"))&&(null==(n=t.claimForm.get("claims").at(e))||null==(l=n.get("perDiemStartTime"))?null:l.touched))),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("required",!0)("readonly",!1)}}function gn(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span"),i["\u0275\u0275text"](1,"*"),i["\u0275\u0275elementEnd"]())}function hn(e,t){}function vn(e,t){if(1&e&&i["\u0275\u0275template"](0,hn,0,0,"ng-template",51),2&e){i["\u0275\u0275nextContext"](3);const e=i["\u0275\u0275reference"](20);i["\u0275\u0275property"]("ngTemplateOutlet",e)}}function Cn(e,t){}function xn(e,t){if(1&e&&i["\u0275\u0275template"](0,Cn,0,0,"ng-template",51),2&e){i["\u0275\u0275nextContext"](3);const e=i["\u0275\u0275reference"](26);i["\u0275\u0275property"]("ngTemplateOutlet",e)}}function yn(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",142),i["\u0275\u0275elementStart"](1,"div",33),i["\u0275\u0275text"](2),i["\u0275\u0275template"](3,gn,2,0,"span",13),i["\u0275\u0275template"](4,vn,1,1,void 0,13),i["\u0275\u0275template"](5,xn,1,1,void 0,13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](6,"mat-form-field",52,53),i["\u0275\u0275elementStart"](8,"input",149),i["\u0275\u0275listener"]("dateChange",(function(){i["\u0275\u0275restoreView"](e);const t=i["\u0275\u0275nextContext"]().index,n=i["\u0275\u0275nextContext"](2);return n.calculateNoOfDays(t),n.validateEndDateTime(t)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](9,"mat-datepicker-toggle",144),i["\u0275\u0275element"](10,"mat-datepicker",null,150),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275reference"](11),t=i["\u0275\u0275nextContext"]().index,n=i["\u0275\u0275nextContext"](2);let l=null,o=null,a=null,r=null,s=null,c=null;i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"](" ",null!=n.fieldConfig&&null!=n.fieldConfig.perDiemEndDate&&n.fieldConfig.perDiemEndDate.field_label?n.fieldConfig.perDiemEndDate.field_label:"Start Date"," "),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null==n.fieldConfig||null==n.fieldConfig.perDiemEndDate?null:n.fieldConfig.perDiemEndDate.is_mandatory),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",(null==(l=n.claimForm.get("claims").at(t))||null==(o=l.get("perDiemEndDate"))?null:o.hasError("required"))&&(null==(l=n.claimForm.get("claims").at(t))||null==(o=l.get("perDiemEndDate"))?null:o.touched)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",(null==(a=n.claimForm.get("claims").at(t))||null==(r=a.get("perDiemEndDate"))?null:r.hasError("invalidInvoiceDate"))&&(null==(a=n.claimForm.get("claims").at(t))||null==(r=a.get("perDiemEndDate"))?null:r.touched)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction2"](12,Ee,1==(null==n.fieldConfig||null==n.fieldConfig.perDiemEndDate?null:n.fieldConfig.perDiemEndDate.is_disabled),(null==(s=n.claimForm.get("claims").at(t))||null==(c=s.get("perDiemEndDate"))?null:c.hasError("required"))&&(null==(s=n.claimForm.get("claims").at(t))||null==(c=s.get("perDiemEndDate"))?null:c.touched))),i["\u0275\u0275advance"](2),i["\u0275\u0275propertyInterpolate"]("placeholder",null!=n.fieldConfig&&null!=n.fieldConfig.perDiemEndDate&&n.fieldConfig.perDiemEndDate.field_label?n.fieldConfig.perDiemEndDate.field_label:"End Date"),i["\u0275\u0275property"]("matDatepicker",e)("max",n.tomorrow)("required",!(null==n.fieldConfig||null==n.fieldConfig.perDiemEndDate||!n.fieldConfig.perDiemEndDate.is_mandatory))("readonly",!(null==n.fieldConfig||null==n.fieldConfig.perDiemEndDate||!n.fieldConfig.perDiemEndDate.is_disabled))("disabled",!(null==n.fieldConfig||null==n.fieldConfig.perDiemEndDate||!n.fieldConfig.perDiemEndDate.is_disabled)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("for",e)}}function bn(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span"),i["\u0275\u0275text"](1,"*"),i["\u0275\u0275elementEnd"]())}function _n(e,t){}function En(e,t){if(1&e&&i["\u0275\u0275template"](0,_n,0,0,"ng-template",51),2&e){i["\u0275\u0275nextContext"](3);const e=i["\u0275\u0275reference"](28);i["\u0275\u0275property"]("ngTemplateOutlet",e)}}function Fn(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",142),i["\u0275\u0275elementStart"](1,"div",33),i["\u0275\u0275text"](2),i["\u0275\u0275template"](3,bn,2,0,"span",13),i["\u0275\u0275template"](4,En,1,1,void 0,13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"mat-form-field",52,53),i["\u0275\u0275elementStart"](7,"input",151,152),i["\u0275\u0275listener"]("keydown",(function(t){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"](3).onKeyDownPreventInvalidChars(t)}))("paste",(function(t){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"](3).preventPaste(t)}))("keyup",(function(t){i["\u0275\u0275restoreView"](e);const n=i["\u0275\u0275nextContext"]().index;return i["\u0275\u0275nextContext"](2).onInputChangeFormatEndTime(t,n)}))("change",(function(){i["\u0275\u0275restoreView"](e);const t=i["\u0275\u0275nextContext"]().index,n=i["\u0275\u0275nextContext"](2);return n.calculateNoOfDays(t),n.validateEndDateTime(t)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](9,"mat-icon",148),i["\u0275\u0275text"](10,"alarm"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"]().index,t=i["\u0275\u0275nextContext"](2);let n=null,l=null,o=null,a=null;i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"](" ",null!=t.fieldConfig&&null!=t.fieldConfig.perDiemEndTime&&t.fieldConfig.perDiemEndTime.field_label?t.fieldConfig.perDiemEndTime.field_label:"End Time"," "),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null==t.fieldConfig||null==t.fieldConfig.perDiemEndTime?null:t.fieldConfig.perDiemEndTime.is_mandatory),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",(null==(n=t.claimForm.get("claims").at(e))||null==(l=n.get("perDiemEndDate"))?null:l.hasError("invalidEndDateTime"))&&(null==(n=t.claimForm.get("claims").at(e))||null==(l=n.get("perDiemEndDate"))?null:l.touched)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction2"](6,Ee,1==(null==t.fieldConfig||null==t.fieldConfig.perDiemEndTime?null:t.fieldConfig.perDiemEndTime.is_disabled),(null==(o=t.claimForm.get("claims").at(e))||null==(a=o.get("perDiemEndTime"))?null:a.hasError("required"))&&(null==(o=t.claimForm.get("claims").at(e))||null==(a=o.get("perDiemEndTime"))?null:a.touched))),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("required",!0)("readonly",!1)}}function In(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span"),i["\u0275\u0275text"](1,"*"),i["\u0275\u0275elementEnd"]())}function wn(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",32),i["\u0275\u0275elementStart"](1,"div",33),i["\u0275\u0275text"](2),i["\u0275\u0275template"](3,In,2,0,"span",13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"span",153),i["\u0275\u0275text"](5),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]().index,t=i["\u0275\u0275nextContext"](2);let n=null,l=null;i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"](" ",null!=t.fieldConfig&&null!=t.fieldConfig.days&&t.fieldConfig.days.field_label?t.fieldConfig.days.field_label:"Total Days"," "),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null==t.fieldConfig||null==t.fieldConfig.days?null:t.fieldConfig.days.is_mandatory),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"]("",null==(n=t.claimForm.get("claims").at(e))||null==(l=n.get("noOfDays"))?null:l.value," DAY(S)")}}function Sn(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span"),i["\u0275\u0275text"](1,"*"),i["\u0275\u0275elementEnd"]())}function Dn(e,t){}function On(e,t){if(1&e&&i["\u0275\u0275template"](0,Dn,0,0,"ng-template",51),2&e){i["\u0275\u0275nextContext"](3);const e=i["\u0275\u0275reference"](20);i["\u0275\u0275property"]("ngTemplateOutlet",e)}}function An(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"mat-hint"),i["\u0275\u0275text"](1," Maximum length is 100 characters "),i["\u0275\u0275elementEnd"]())}function Mn(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",32),i["\u0275\u0275elementStart"](1,"div",33),i["\u0275\u0275text"](2),i["\u0275\u0275template"](3,Sn,2,0,"span",13),i["\u0275\u0275template"](4,On,1,1,void 0,13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"mat-form-field",52,53),i["\u0275\u0275element"](7,"input",154),i["\u0275\u0275elementStart"](8,"mat-icon",148),i["\u0275\u0275text"](9,"location_on"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](10,An,2,0,"mat-hint",13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]().index,t=i["\u0275\u0275nextContext"](2);let n=null,l=null,o=null,a=null,r=null,s=null;i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"](" ",null!=t.fieldConfig&&null!=t.fieldConfig.location&&t.fieldConfig.location.field_label?t.fieldConfig.location.field_label:"Location"," "),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null==t.fieldConfig||null==t.fieldConfig.location?null:t.fieldConfig.location.is_mandatory),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",(null==(n=t.claimForm.get("claims").at(e))||null==(l=n.get("location"))?null:l.hasError("required"))&&(null==(n=t.claimForm.get("claims").at(e))||null==(l=n.get("location"))?null:l.touched)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction2"](7,Ee,1==(null==t.fieldConfig||null==t.fieldConfig.location?null:t.fieldConfig.location.is_disabled),(null==(o=t.claimForm.get("claims").at(e))||null==(a=o.get("location"))?null:a.hasError("required"))&&(null==(o=t.claimForm.get("claims").at(e))||null==(a=o.get("location"))?null:a.touched))),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("required",!(null==t.fieldConfig||!t.fieldConfig.location.is_mandatory))("readonly",!(null==t.fieldConfig||null==t.fieldConfig.description||!t.fieldConfig.description.is_disabled)),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngIf",(null==(r=t.claimForm.get("claims").at(e))||null==(s=r.get("location"))||null==s.value?null:s.value.length)>100)}}function Tn(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",155),i["\u0275\u0275elementStart"](1,"div",33),i["\u0275\u0275text"](2),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](3,"mat-form-field",74,53),i["\u0275\u0275elementStart"](5,"mat-date-range-input",156),i["\u0275\u0275element"](6,"input",157),i["\u0275\u0275element"](7,"input",158),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](8,"mat-datepicker-toggle",159),i["\u0275\u0275element"](9,"mat-date-range-picker",null,160),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275reference"](10),t=i["\u0275\u0275nextContext"](3);i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate"](null!=t.fieldConfig&&null!=t.fieldConfig.startDate&&t.fieldConfig.startDate.field_label?t.fieldConfig.startDate.field_label:"Expense Duration *"),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction1"](9,se,1===(null==t.fieldConfig||null==t.fieldConfig.startDate?null:t.fieldConfig.startDate.is_disabled))),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("rangePicker",e)("min",t.billingStartDate)("disabled",!(null==t.fieldConfig||null==t.fieldConfig.startDate||!t.fieldConfig.startDate.is_disabled)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("readonly",!(null==t.fieldConfig||null==t.fieldConfig.startDate||!t.fieldConfig.startDate.is_disabled)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("readonly",!(null==t.fieldConfig||null==t.fieldConfig.endDate||!t.fieldConfig.endDate.is_disabled)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("for",e)("disabled",!(null==t.fieldConfig||null==t.fieldConfig.startDate||!t.fieldConfig.startDate.is_disabled))}}function Pn(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span"),i["\u0275\u0275text"](1,"*"),i["\u0275\u0275elementEnd"]())}function kn(e,t){}function Vn(e,t){if(1&e&&i["\u0275\u0275template"](0,kn,0,0,"ng-template",51),2&e){i["\u0275\u0275nextContext"](3);const e=i["\u0275\u0275reference"](20);i["\u0275\u0275property"]("ngTemplateOutlet",e)}}function Ln(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"mat-hint"),i["\u0275\u0275text"](1," Maximum length is 100 characters "),i["\u0275\u0275elementEnd"]())}function Bn(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",161),i["\u0275\u0275elementStart"](1,"div",33),i["\u0275\u0275text"](2),i["\u0275\u0275template"](3,Pn,2,0,"span",13),i["\u0275\u0275template"](4,Vn,1,1,void 0,13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"mat-form-field",52,53),i["\u0275\u0275element"](7,"input",162),i["\u0275\u0275template"](8,Ln,2,0,"mat-hint",13),i["\u0275\u0275elementStart"](9,"mat-icon",148),i["\u0275\u0275text"](10,"location_on"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]().index,t=i["\u0275\u0275nextContext"](2);let n=null,l=null,o=null,a=null,r=null,s=null;i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"](" ",null!=t.fieldConfig&&null!=t.fieldConfig.fromLocation&&t.fieldConfig.fromLocation.field_label?t.fieldConfig.fromLocation.field_label:"From Location"," "),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null==t.fieldConfig||null==t.fieldConfig.fromLocation?null:t.fieldConfig.fromLocation.is_mandatory),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",(null==(n=t.claimForm.get("claims").at(e))||null==(l=n.get("fromLocation"))?null:l.hasError("required"))&&(null==(n=t.claimForm.get("claims").at(e))||null==(l=n.get("fromLocation"))?null:l.touched)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction2"](7,Ee,1==(null==t.fieldConfig||null==t.fieldConfig.fromLocation?null:t.fieldConfig.fromLocation.is_disabled),(null==(o=t.claimForm.get("claims").at(e))||null==(a=o.get("fromLocation"))?null:a.hasError("required"))&&(null==(o=t.claimForm.get("claims").at(e))||null==(a=o.get("fromLocation"))?null:a.touched))),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("required",!(null==t.fieldConfig||!t.fieldConfig.fromLocation.is_mandatory))("readonly",!(null==t.fieldConfig||null==t.fieldConfig.fromLocation||!t.fieldConfig.fromLocation.is_disabled)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",(null==(r=t.claimForm.get("claims").at(e))||null==(s=r.get("fromLocation"))||null==s.value?null:s.value.length)>100)}}function Nn(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span"),i["\u0275\u0275text"](1,"*"),i["\u0275\u0275elementEnd"]())}function Rn(e,t){}function qn(e,t){if(1&e&&i["\u0275\u0275template"](0,Rn,0,0,"ng-template",51),2&e){i["\u0275\u0275nextContext"](3);const e=i["\u0275\u0275reference"](20);i["\u0275\u0275property"]("ngTemplateOutlet",e)}}function Hn(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"mat-hint"),i["\u0275\u0275text"](1," Maximum length is 100 characters "),i["\u0275\u0275elementEnd"]())}function Yn(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",161),i["\u0275\u0275elementStart"](1,"div",33),i["\u0275\u0275text"](2),i["\u0275\u0275template"](3,Nn,2,0,"span",13),i["\u0275\u0275template"](4,qn,1,1,void 0,13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"mat-form-field",52,53),i["\u0275\u0275element"](7,"input",163),i["\u0275\u0275template"](8,Hn,2,0,"mat-hint",13),i["\u0275\u0275elementStart"](9,"mat-icon",148),i["\u0275\u0275text"](10,"location_on"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]().index,t=i["\u0275\u0275nextContext"](2);let n=null,l=null,o=null,a=null,r=null,s=null;i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"](" ",null!=t.fieldConfig&&null!=t.fieldConfig.toLocation&&t.fieldConfig.toLocation.field_label?t.fieldConfig.toLocation.field_label:"To Location"," "),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null==t.fieldConfig||null==t.fieldConfig.toLocation?null:t.fieldConfig.toLocation.is_mandatory),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",(null==(n=t.claimForm.get("claims").at(e))||null==(l=n.get("toLocation"))?null:l.hasError("required"))&&(null==(n=t.claimForm.get("claims").at(e))||null==(l=n.get("toLocation"))?null:l.touched)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction2"](7,Ee,1==(null==t.fieldConfig||null==t.fieldConfig.toLocation?null:t.fieldConfig.toLocation.is_disabled),(null==(o=t.claimForm.get("claims").at(e))||null==(a=o.get("toLocation"))?null:a.hasError("required"))&&(null==(o=t.claimForm.get("claims").at(e))||null==(a=o.get("toLocation"))?null:a.touched))),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("required",!(null==t.fieldConfig||!t.fieldConfig.toLocation.is_mandatory))("readonly",!(null==t.fieldConfig||null==t.fieldConfig.toLocation||!t.fieldConfig.toLocation.is_disabled)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",(null==(r=t.claimForm.get("claims").at(e))||null==(s=r.get("toLocation"))||null==s.value?null:s.value.length)>100)}}function jn(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span"),i["\u0275\u0275text"](1,"*"),i["\u0275\u0275elementEnd"]())}function Wn(e,t){}function $n(e,t){if(1&e&&i["\u0275\u0275template"](0,Wn,0,0,"ng-template",51),2&e){i["\u0275\u0275nextContext"](3);const e=i["\u0275\u0275reference"](20);i["\u0275\u0275property"]("ngTemplateOutlet",e)}}function Un(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",32),i["\u0275\u0275elementStart"](1,"div",33),i["\u0275\u0275text"](2),i["\u0275\u0275template"](3,jn,2,0,"span",13),i["\u0275\u0275template"](4,$n,1,1,void 0,13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"app-exp-input-search",164),i["\u0275\u0275listener"]("change",(function(t){i["\u0275\u0275restoreView"](e);const n=i["\u0275\u0275nextContext"]().index;return i["\u0275\u0275nextContext"](2).changeVehicleType(t,n)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"]().index,t=i["\u0275\u0275nextContext"](2);let n=null,l=null,o=null,a=null;i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"](" ",null!=t.fieldConfig&&null!=t.fieldConfig.vehicleType&&t.fieldConfig.vehicleType.field_label?t.fieldConfig.vehicleType.field_label:"Vehicle Type"," "),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null==t.fieldConfig||null==t.fieldConfig.vehicleType?null:t.fieldConfig.vehicleType.is_mandatory),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",(null==(n=t.claimForm.get("claims").at(e))||null==(l=n.get("vehicleType"))?null:l.hasError("required"))&&(null==(n=t.claimForm.get("claims").at(e))||null==(l=n.get("vehicleType"))?null:l.touched)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction1"](9,ae,(null==(o=t.claimForm.get("claims").at(e))||null==(a=o.get("vehicleType"))?null:a.hasError("required"))&&(null==(o=t.claimForm.get("claims").at(e))||null==(a=o.get("vehicleType"))?null:a.touched)))("placeholder","Select Vehicle Type")("list",t.vehicleTypeList)("disabled",!(null==t.fieldConfig||null==t.fieldConfig.vehicleType||!t.fieldConfig.vehicleType.is_disabled))("bgColorOnDisable",!(null==t.fieldConfig||null==t.fieldConfig.vehicleType||!t.fieldConfig.vehicleType.is_disabled))("hideMatLabel",!0)}}function zn(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span"),i["\u0275\u0275text"](1,"*"),i["\u0275\u0275elementEnd"]())}function Gn(e,t){}function Jn(e,t){if(1&e&&i["\u0275\u0275template"](0,Gn,0,0,"ng-template",51),2&e){i["\u0275\u0275nextContext"](3);const e=i["\u0275\u0275reference"](20);i["\u0275\u0275property"]("ngTemplateOutlet",e)}}function Kn(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",32),i["\u0275\u0275elementStart"](1,"div",33),i["\u0275\u0275text"](2),i["\u0275\u0275template"](3,zn,2,0,"span",13),i["\u0275\u0275elementStart"](4,"mat-icon",165),i["\u0275\u0275text"](5," info_outline "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](6,Jn,1,1,void 0,13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](7,"app-exp-input-search",166),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]().index,t=i["\u0275\u0275nextContext"](2);let n=null,l=null,o=null,a=null;i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"](" ",null!=t.fieldConfig&&null!=t.fieldConfig.vehicleEngineType&&t.fieldConfig.vehicleEngineType.field_label?t.fieldConfig.vehicleEngineType.field_label:"Vehicle Engine Type"," "),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",(null==t.fieldConfig||null==t.fieldConfig.vehicleEngineType?null:t.fieldConfig.vehicleEngineType.is_mandatory)&&(null==t.vehicleEngineTypeList?null:t.vehicleEngineTypeList.length)),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngIf",(null==(n=t.claimForm.get("claims").at(e))||null==(l=n.get("vehicleEngineType"))?null:l.hasError("required"))&&(null==(n=t.claimForm.get("claims").at(e))||null==(l=n.get("vehicleEngineType"))?null:l.touched)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction1"](9,ae,(null==(o=t.claimForm.get("claims").at(e))||null==(a=o.get("vehicleEngineType"))?null:a.hasError("required"))&&(null==(o=t.claimForm.get("claims").at(e))||null==(a=o.get("vehicleEngineType"))?null:a.touched)))("placeholder","Select Vehicle engine Type & Size")("list",t.vehicleEngineTypeList)("disabled",(null==t.fieldConfig||null==t.fieldConfig.vehicleEngineType?null:t.fieldConfig.vehicleEngineType.is_disabled)||!(null!=t.vehicleEngineTypeList&&t.vehicleEngineTypeList.length))("bgColorOnDisable",(null==t.fieldConfig||null==t.fieldConfig.vehicleEngineTypeList?null:t.fieldConfig.vehicleEngineTypeList.is_disabled)||!(null!=t.vehicleEngineTypeList&&t.vehicleEngineTypeList.length))("hideMatLabel",!0)}}function Xn(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span"),i["\u0275\u0275text"](1,"*"),i["\u0275\u0275elementEnd"]())}function Qn(e,t){}function Zn(e,t){if(1&e&&i["\u0275\u0275template"](0,Qn,0,0,"ng-template",51),2&e){i["\u0275\u0275nextContext"](3);const e=i["\u0275\u0275reference"](20);i["\u0275\u0275property"]("ngTemplateOutlet",e)}}function el(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-option",64),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;i["\u0275\u0275property"]("value",null==e?null:e.id)("matTooltip",null==e?null:e.name),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" ",null==e?null:e.name," ")}}function tl(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span"),i["\u0275\u0275text"](1," Distance must be greater than 0. "),i["\u0275\u0275elementEnd"]())}function nl(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span"),i["\u0275\u0275text"](1," Distance cannot exceed 15 digits before the decimal. "),i["\u0275\u0275elementEnd"]())}function ll(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span"),i["\u0275\u0275text"](1," Unit is required. "),i["\u0275\u0275elementEnd"]())}function il(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",32),i["\u0275\u0275elementStart"](1,"div",33),i["\u0275\u0275text"](2),i["\u0275\u0275template"](3,Xn,2,0,"span",13),i["\u0275\u0275template"](4,Zn,1,1,void 0,13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"mat-form-field",52,53),i["\u0275\u0275elementStart"](7,"div",167),i["\u0275\u0275elementStart"](8,"input",168),i["\u0275\u0275listener"]("input",(function(){i["\u0275\u0275restoreView"](e);const t=i["\u0275\u0275nextContext"]().index;return i["\u0275\u0275nextContext"](2).onMilesInput(t)}))("blur",(function(){i["\u0275\u0275restoreView"](e);const t=i["\u0275\u0275nextContext"]().index;return i["\u0275\u0275nextContext"](2).roundMiles(t)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](9,"mat-divider",169),i["\u0275\u0275elementStart"](10,"mat-select",170),i["\u0275\u0275template"](11,el,2,3,"mat-option",63),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](12,"mat-hint"),i["\u0275\u0275template"](13,tl,2,0,"span",13),i["\u0275\u0275template"](14,nl,2,0,"span",13),i["\u0275\u0275template"](15,ll,2,0,"span",13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"]().index,t=i["\u0275\u0275nextContext"](2);let n=null,l=null,o=null,a=null,r=null,s=null,c=null,d=null,m=null,p=null;i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"](" ",null!=t.fieldConfig&&null!=t.fieldConfig.miles&&t.fieldConfig.miles.field_label?t.fieldConfig.miles.field_label:"No of Miles/Kms"," "),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null==t.fieldConfig||null==t.fieldConfig.miles?null:t.fieldConfig.miles.is_mandatory),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",(null==(n=t.claimForm.get("claims").at(e))||null==(l=n.get("miles"))?null:l.hasError("required"))&&(null==(n=t.claimForm.get("claims").at(e))||null==(l=n.get("miles"))?null:l.touched)||(null==(n=t.claimForm.get("claims").at(e))||null==(l=n.get("unit"))?null:l.hasError("required"))&&(null==(n=t.claimForm.get("claims").at(e))||null==(l=n.get("unit"))?null:l.touched)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction2"](9,Ee,1==(null==t.fieldConfig||null==t.fieldConfig.miles?null:t.fieldConfig.miles.is_disabled),(null==(o=t.claimForm.get("claims").at(e))||null==(a=o.get("miles"))?null:a.hasError("required"))&&(null==(o=t.claimForm.get("claims").at(e))||null==(a=o.get("miles"))?null:a.touched)||(null==(o=t.claimForm.get("claims").at(e))||null==(a=o.get("unit"))?null:a.hasError("required"))&&(null==(o=t.claimForm.get("claims").at(e))||null==(a=o.get("unit"))?null:a.touched))),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("disabled",null==t.fieldConfig||null==t.fieldConfig.miles?null:t.fieldConfig.miles.is_disabled),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngForOf",t.unitList),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf",null==(r=t.claimForm.get("claims").at(e))||null==(s=r.get("miles"))?null:s.hasError("min")),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",(null==(c=t.claimForm.get("claims").at(e))||null==(d=c.get("miles"))?null:d.hasError("maxDigitsExceeded"))||(null==(c=t.claimForm.get("claims").at(e))||null==(d=c.get("amount"))?null:d.hasError("validAmount"))),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",(null==(m=t.claimForm.get("claims").at(e))||null==(p=m.get("unit"))?null:p.hasError("required"))&&(null==(m=t.claimForm.get("claims").at(e))||null==(p=m.get("unit"))?null:p.touched))}}function ol(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"span",127),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](5);i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"]("",e.claimForm.value.legalEntityCurrency," ")}}function al(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"span",127),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](3).index,t=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](t.claimForm.value.claims[e].currenyCodeValue)}}function rl(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",174),i["\u0275\u0275elementStart"](1,"mat-form-field",175),i["\u0275\u0275elementStart"](2,"span"),i["\u0275\u0275text"](3,"1"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](4,ol,2,1,"span",125),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"mat-icon",176),i["\u0275\u0275text"](6,"arrow_forward"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](7,"mat-form-field",175),i["\u0275\u0275elementStart"](8,"span"),i["\u0275\u0275text"](9),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](10,al,2,1,"span",125),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](2).index,t=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](4),i["\u0275\u0275property"]("ngIf",t.claimForm.value.legalEntityCurrency),i["\u0275\u0275advance"](5),i["\u0275\u0275textInterpolate"](t.claimForm.value.claims[e].conversionRate),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.claimForm.value.claims[e].currenyCodeValue)}}function sl(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span"),i["\u0275\u0275text"](1,"*"),i["\u0275\u0275elementEnd"]())}function cl(e,t){}function dl(e,t){if(1&e&&i["\u0275\u0275template"](0,cl,0,0,"ng-template",51),2&e){i["\u0275\u0275nextContext"](4);const e=i["\u0275\u0275reference"](20);i["\u0275\u0275property"]("ngTemplateOutlet",e)}}function ml(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span"),i["\u0275\u0275text"](1," Amount can't be 0, -ve "),i["\u0275\u0275elementEnd"]())}function pl(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span"),i["\u0275\u0275text"](1," ,empty "),i["\u0275\u0275elementEnd"]())}function ul(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-hint",128),i["\u0275\u0275template"](1,ml,2,0,"span",13),i["\u0275\u0275template"](2,pl,2,0,"span",13),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](3).index,t=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",!t.claimForm.controls.claims.controls[e].controls.conversionRate.errors.validAmount),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.claimForm.controls.claims.controls[e].controls.conversionRate.errors.required)}}function fl(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",177),i["\u0275\u0275elementStart"](1,"div",33),i["\u0275\u0275text"](2),i["\u0275\u0275template"](3,sl,2,0,"span",13),i["\u0275\u0275template"](4,dl,1,1,void 0,13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"mat-form-field",52,53),i["\u0275\u0275elementStart"](7,"input",178),i["\u0275\u0275listener"]("input",(function(t){i["\u0275\u0275restoreView"](e);const n=i["\u0275\u0275nextContext"](2).index;return i["\u0275\u0275nextContext"](2).onConversionRateInput(t,n)}))("focusout",(function(){i["\u0275\u0275restoreView"](e);const t=i["\u0275\u0275nextContext"](2).index;return i["\u0275\u0275nextContext"](2).onConversionRateChange(t)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](8,ul,3,2,"mat-hint",126),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"](2).index,t=i["\u0275\u0275nextContext"](2);let n=null,l=null,o=null,a=null;i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"]("",null!=t.fieldConfig&&null!=t.fieldConfig.conversionRate&&t.fieldConfig.conversionRate.field_label?t.fieldConfig.conversionRate.field_label:"conversionRate"," "),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null==t.fieldConfig||null==t.fieldConfig.conversionRate?null:t.fieldConfig.conversionRate.is_mandatory),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",(null==(n=t.claimForm.get("claims").at(e))||null==(l=n.get("conversionRate"))?null:l.hasError("required"))&&(null==(n=t.claimForm.get("claims").at(e))||null==(l=n.get("conversionRate"))?null:l.touched)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction2"](7,Ee,t.claimForm.value.claims[e].currenyCodeValue==t.claimForm.value.legalEntityCurrency,(null==(o=t.claimForm.get("claims").at(e))||null==(a=o.get("conversionRate"))?null:a.hasError("required"))&&(null==(o=t.claimForm.get("claims").at(e))||null==(a=o.get("conversionRate"))?null:a.touched))),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("placeholder",null!=t.fieldConfig&&null!=t.fieldConfig.conversionRate&&t.fieldConfig.conversionRate.field_label?t.fieldConfig.conversionRate.field_label:"conversionRate")("readonly",t.claimForm.value.claims[e].currenyCodeValue==t.claimForm.value.legalEntityCurrency),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.claimForm.controls.claims.controls[e].controls.conversionRate.invalid&&(t.claimForm.controls.claims.controls[e].controls.conversionRate.touched||t.claimForm.controls.claims.controls[e].controls.conversionRate.dirty))}}function gl(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span"),i["\u0275\u0275text"](1,"*"),i["\u0275\u0275elementEnd"]())}function hl(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"span",127),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](3).index,t=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](t.claimForm.value.claims[e].currenyCodeValue)}}function vl(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",177),i["\u0275\u0275elementStart"](1,"div",33),i["\u0275\u0275text"](2),i["\u0275\u0275template"](3,gl,2,0,"span",13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"mat-form-field",52,53),i["\u0275\u0275elementStart"](6,"div",123),i["\u0275\u0275element"](7,"input",179),i["\u0275\u0275template"](8,hl,2,1,"span",125),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](2).index,t=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"]("",null!=t.fieldConfig&&null!=t.fieldConfig.finalamount&&t.fieldConfig.finalamount.field_label?null==t.fieldConfig||null==t.fieldConfig.finalamount?null:t.fieldConfig.finalamount.field_label:"finalamount"," "),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null==t.fieldConfig||null==t.fieldConfig.finalamount?null:t.fieldConfig.finalamount.is_mandatory),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction1"](6,se,1===(null==t.fieldConfig||null==t.fieldConfig.finalamount?null:t.fieldConfig.finalamount.is_disabled))),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("placeholder",null!=t.fieldConfig&&null!=t.fieldConfig.finalamount&&t.fieldConfig.finalamount.field_label?null==t.fieldConfig||null==t.fieldConfig.finalamount?null:t.fieldConfig.finalamount.field_label:"finalamount")("readonly",!(null==t.fieldConfig||null==t.fieldConfig.finalamount||!t.fieldConfig.finalamount.is_disabled)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.claimForm.value.claims[e].currenyCodeValue)}}function Cl(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span"),i["\u0275\u0275text"](1,"*"),i["\u0275\u0275elementEnd"]())}function xl(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"span",127),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](5);i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](e.claimForm.value.legalEntityCurrency)}}function yl(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",177),i["\u0275\u0275elementStart"](1,"div",33),i["\u0275\u0275text"](2),i["\u0275\u0275template"](3,Cl,2,0,"span",13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"mat-form-field",52,53),i["\u0275\u0275elementStart"](6,"div",123),i["\u0275\u0275element"](7,"input",180),i["\u0275\u0275template"](8,xl,2,1,"span",125),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](4);i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"]("",null!=e.fieldConfig&&null!=e.fieldConfig.settlement&&e.fieldConfig.settlement.field_label?null==e.fieldConfig||null==e.fieldConfig.settlement?null:e.fieldConfig.settlement.field_label:"Settlement Amount"," "),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null==e.fieldConfig||null==e.fieldConfig.settlement?null:e.fieldConfig.settlement.is_mandatory),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction1"](6,se,1===(null==e.fieldConfig||null==e.fieldConfig.settlement?null:e.fieldConfig.settlement.is_disabled))),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("placeholder",null!=e.fieldConfig&&null!=e.fieldConfig.settlement&&e.fieldConfig.settlement.field_label?null==e.fieldConfig||null==e.fieldConfig.settlement?null:e.fieldConfig.settlement.field_label:"settlement")("readonly",!(null==e.fieldConfig||null==e.fieldConfig.settlement||!e.fieldConfig.settlement.is_disabled)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null==e.claimForm||null==e.claimForm.value?null:e.claimForm.value.legalEntityCurrency)}}function bl(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",43),i["\u0275\u0275elementStart"](1,"div",171),i["\u0275\u0275text"](2," Exchange Rate Details "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](3,rl,11,3,"div",172),i["\u0275\u0275template"](4,fl,9,10,"div",173),i["\u0275\u0275template"](5,vl,9,8,"div",173),i["\u0275\u0275template"](6,yl,9,8,"div",173),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](3);i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngIf",null!=e.fieldConfig&&null!=e.fieldConfig.fromToConverstionRate&&e.fieldConfig.fromToConverstionRate.is_visible?null==e.fieldConfig||null==e.fieldConfig.fromToConverstionRate?null:e.fieldConfig.fromToConverstionRate.is_visible:0),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null!=e.fieldConfig&&null!=e.fieldConfig.conversionRate&&e.fieldConfig.conversionRate.is_visible?null==e.fieldConfig||null==e.fieldConfig.conversionRate?null:e.fieldConfig.conversionRate.is_visible:0),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null!=e.fieldConfig&&null!=e.fieldConfig.finalamount&&e.fieldConfig.finalamount.is_visible?null==e.fieldConfig||null==e.fieldConfig.finalamount?null:e.fieldConfig.finalamount.is_visible:0),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null!=e.fieldConfig&&null!=e.fieldConfig.settlement&&e.fieldConfig.settlement.is_visible?null==e.fieldConfig||null==e.fieldConfig.settlement?null:e.fieldConfig.settlement.is_visible:0)}}function _l(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"span",181),i["\u0275\u0275elementStart"](1,"mat-icon",182),i["\u0275\u0275text"](2," error_outline "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](3,"span",183),i["\u0275\u0275text"](4),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]().$implicit;i["\u0275\u0275advance"](4),i["\u0275\u0275textInterpolate"](e.controls.duplicationError.value)}}function El(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",190),i["\u0275\u0275elementStart"](1,"app-expense-upload-btn",191),i["\u0275\u0275listener"]("change",(function(t){i["\u0275\u0275restoreView"](e);const n=i["\u0275\u0275nextContext"](2).index;return i["\u0275\u0275nextContext"](2).changeInFiles(t,n)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"](2).$implicit;i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("destinationBucket","kebs-expenses")("routingKey","expenses")("contextId",e.controls.contextId.value)("allowEdit",!0)}}function Fl(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"mat-icon",196),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275reference"](3).click()})),i["\u0275\u0275text"](1,"attachment "),i["\u0275\u0275elementStart"](2,"input",197,198),i["\u0275\u0275listener"]("change",(function(){i["\u0275\u0275restoreView"](e);const t=i["\u0275\u0275nextContext"](3).index;return i["\u0275\u0275nextContext"](2).onFileAdd(t)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"](5);i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("uploader",e.uploader)("accept",e.allowedMimeType.toString())}}function Il(e,t){1&e&&i["\u0275\u0275element"](0,"mat-spinner",199)}function wl(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"mat-icon",200),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275reference"](3).click()})),i["\u0275\u0275text"](1,"edit "),i["\u0275\u0275elementStart"](2,"input",197,198),i["\u0275\u0275listener"]("change",(function(){i["\u0275\u0275restoreView"](e);const t=i["\u0275\u0275nextContext"](3).index;return i["\u0275\u0275nextContext"](2).onFileAdd(t)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"](5);i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("uploader",e.uploader)("accept",e.allowedMimeType.toString())}}function Sl(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",190),i["\u0275\u0275template"](1,Fl,4,2,"mat-icon",192),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](2,Il,1,0,"mat-spinner",193),i["\u0275\u0275elementStart"](3,"span",194),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const t=i["\u0275\u0275nextContext"](2).index;return i["\u0275\u0275nextContext"](2).openFile(t)})),i["\u0275\u0275text"](4),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](5,wl,4,2,"mat-icon",195)}if(2&e){const e=i["\u0275\u0275nextContext"](2).$implicit;i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",!e.controls.uploadInProgress.value&&!e.controls.attachment.value[0]),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",e.controls.uploadInProgress.value),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("matTooltip",null==e.controls.attachment.value[0]?null:e.controls.attachment.value[0].fileName),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" ",null==e.controls.attachment.value[0]?null:e.controls.attachment.value[0].fileName,""),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",""!=(null==e.controls.attachment.value[0]?null:e.controls.attachment.value[0].fileName)&&null!=(null==e.controls.attachment.value[0]?null:e.controls.attachment.value[0].fileName))}}const Dl=function(e,t){return{"invalid-attachment":e,"attachment-btn":t}};function Ol(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",184),i["\u0275\u0275elementStart"](1,"div",185),i["\u0275\u0275elementStart"](2,"div",186),i["\u0275\u0275elementStart"](3,"div",187),i["\u0275\u0275text"](4),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](5,El,2,4,"div",188),i["\u0275\u0275template"](6,Sl,6,5,"ng-template",null,189,i["\u0275\u0275templateRefExtractor"]),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275reference"](7),t=i["\u0275\u0275nextContext"](),n=t.index,l=t.$implicit,o=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction2"](5,Dl,null==o.claimForm.controls.claims.controls[n].controls.contextId.errors?null:o.claimForm.controls.claims.controls[n].controls.contextId.errors.required,!(null!=o.claimForm.controls.claims.controls[n].controls.contextId.errors&&o.claimForm.controls.claims.controls[n].controls.contextId.errors.required))),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("matTooltip",o.getAttahmentAlertMessage(l.controls.category.value)),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" ",o.getAttahmentAlertMessage(l.controls.category.value),""),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",(null==l||null==l.controls||null==l.controls.contextId?null:l.controls.contextId.value)||null==o.draftInfo||!o.draftInfo.claims[n]||!(null!=l&&null!=l.controls&&null!=l.controls.contextId&&l.controls.contextId.value)&&!(null!=l&&null!=l.controls&&null!=l.controls.attachment&&l.controls.attachment.value))("ngIfElse",e)}}const Al=function(e){return{"duplicate-error":e}};function Ml(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",86),i["\u0275\u0275elementStart"](1,"div",87),i["\u0275\u0275elementStart"](2,"div",88),i["\u0275\u0275elementStart"](3,"div",43),i["\u0275\u0275elementStart"](4,"div",89),i["\u0275\u0275elementStart"](5,"div",43),i["\u0275\u0275elementStart"](6,"div",90),i["\u0275\u0275text"](7),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](8,"div",91),i["\u0275\u0275elementStart"](9,"div"),i["\u0275\u0275elementStart"](10,"button",92),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"](2).addClaim()})),i["\u0275\u0275elementStart"](11,"mat-icon",93),i["\u0275\u0275text"](12,"add_circle"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](13,"button",92),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const n=t.index;return i["\u0275\u0275nextContext"](2).deleteClaim(n)})),i["\u0275\u0275elementStart"](14,"mat-icon",93),i["\u0275\u0275text"](15,"remove_circle"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](16,"div",43),i["\u0275\u0275template"](17,ot,12,14,"div",31),i["\u0275\u0275template"](18,rt,7,8,"div",35),i["\u0275\u0275template"](19,mt,8,10,"div",94),i["\u0275\u0275template"](20,gt,6,9,"div",31),i["\u0275\u0275template"](21,vt,9,8,"div",35),i["\u0275\u0275template"](22,bt,2,1,"div",36),i["\u0275\u0275template"](23,It,2,1,"div",95),i["\u0275\u0275template"](24,St,5,7,"div",31),i["\u0275\u0275template"](25,Mt,6,11,"div",31),i["\u0275\u0275template"](26,qt,10,13,"div",31),i["\u0275\u0275template"](27,Wt,6,11,"div",31),i["\u0275\u0275template"](28,Qt,11,11,"div",31),i["\u0275\u0275elementStart"](29,"div",96),i["\u0275\u0275template"](30,tn,5,10,"div",13),i["\u0275\u0275elementStart"](31,"div",97),i["\u0275\u0275template"](32,ln,5,2,"div",98),i["\u0275\u0275template"](33,an,3,1,"div",13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](34,pn,12,15,"div",99),i["\u0275\u0275template"](35,fn,10,8,"div",99),i["\u0275\u0275template"](36,yn,12,15,"div",99),i["\u0275\u0275template"](37,Fn,11,9,"div",99),i["\u0275\u0275template"](38,wn,6,3,"div",31),i["\u0275\u0275template"](39,Mn,11,10,"div",31),i["\u0275\u0275template"](40,Tn,11,11,"div",100),i["\u0275\u0275template"](41,Bn,11,10,"div",101),i["\u0275\u0275template"](42,Yn,11,10,"div",101),i["\u0275\u0275template"](43,Un,6,11,"div",31),i["\u0275\u0275template"](44,Kn,8,11,"div",31),i["\u0275\u0275template"](45,il,16,12,"div",31),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](46,bl,7,4,"div",102),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](47,"div",103),i["\u0275\u0275elementStart"](48,"div",43),i["\u0275\u0275elementStart"](49,"div",104),i["\u0275\u0275template"](50,_l,5,1,"span",105),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](51,Ol,8,8,"div",106),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=t.index,l=i["\u0275\u0275nextContext"](2);let o=null,a=null,r=null,s=null,c=null,d=null,m=null,p=null,u=null,f=null,g=null,h=null,v=null,C=null,x=null,y=null,b=null,_=null,E=null,F=null,I=null,w=null,S=null,D=null,O=null;i["\u0275\u0275property"]("formGroupName",n),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction1"](33,Al,1==(null==(o=e.get("hasDuplicateError"))?null:o.value))),i["\u0275\u0275advance"](6),i["\u0275\u0275textInterpolate1"](" ",null==(a=l.claimForm.get("claims").at(n))||null==(r=a.get("categoryName"))?null:r.value," Receipt Details "),i["\u0275\u0275advance"](10),i["\u0275\u0275property"]("ngIf",null!=l.fieldConfig&&null!=l.fieldConfig.invoiceDate&&l.fieldConfig.invoiceDate.is_visible?null==l.fieldConfig||null==l.fieldConfig.invoiceDate?null:l.fieldConfig.invoiceDate.is_visible:0),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null!=l.fieldConfig&&null!=l.fieldConfig.invoiceNo&&l.fieldConfig.invoiceNo.is_visible?null==l.fieldConfig||null==l.fieldConfig.invoiceNo?null:l.fieldConfig.invoiceNo.is_visible:0),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null!=l.fieldConfig&&null!=l.fieldConfig.description&&l.fieldConfig.description.is_visible?null==l.fieldConfig||null==l.fieldConfig.description?null:l.fieldConfig.description.is_visible:0),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null!=l.fieldConfig&&null!=l.fieldConfig.category&&l.fieldConfig.category.is_visible?null==l.fieldConfig||null==l.fieldConfig.category?null:l.fieldConfig.category.is_visible:0),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",1==l.isManualApproverSelectionApplicable&&0==l.isHeaderLevelApprover),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",1==l.isManualApproverSelectionApplicable&&0==l.isHeaderLevelApprover),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",0==l.isManualApproverSelectionApplicable),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null!=l.fieldConfig&&null!=l.fieldConfig.taxRatetype&&l.fieldConfig.taxRatetype.is_visible?null==l.fieldConfig||null==l.fieldConfig.taxRatetype?null:l.fieldConfig.taxRatetype.is_visible:0),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null!=l.fieldConfig&&null!=l.fieldConfig.currency&&l.fieldConfig.currency.is_visible?null==l.fieldConfig||null==l.fieldConfig.currency?null:l.fieldConfig.currency.is_visible:0),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null!=l.fieldConfig&&null!=l.fieldConfig.amount&&l.fieldConfig.amount.is_visible?null==l.fieldConfig||null==l.fieldConfig.amount?null:l.fieldConfig.amount.is_visible:0),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null!=l.fieldConfig&&null!=l.fieldConfig.taxPercentage&&l.fieldConfig.taxPercentage.is_visible?null==l.fieldConfig||null==l.fieldConfig.taxPercentage?null:l.fieldConfig.taxPercentage.is_visible:0),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null!=l.fieldConfig&&null!=l.fieldConfig.taxAmount&&l.fieldConfig.taxAmount.is_visible?null==l.fieldConfig||null==l.fieldConfig.taxAmount?null:l.fieldConfig.taxAmount.is_visible:0),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf",null!=l.fieldConfig&&null!=l.fieldConfig.peopleInvolved&&l.fieldConfig.peopleInvolved.is_visible?null==l.fieldConfig||null==l.fieldConfig.peopleInvolved?null:l.fieldConfig.peopleInvolved.is_visible:0),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf",l.claimForm.controls.claims.controls[n].controls.peopleInvolved.value&&(null==l.claimForm.controls.claims.controls[n].controls.peopleInvolved.value?null:l.claimForm.controls.claims.controls[n].controls.peopleInvolved.value.length)>=3),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",(null==l.claimForm.controls.claims.controls[n].controls.peopleInvolved.value?null:l.claimForm.controls.claims.controls[n].controls.peopleInvolved.value.length)<3),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",8==(null==(s=l.claimForm.get("claims").at(n))||null==(c=s.get("claimTypeId"))?null:c.value)&&null!=l.fieldConfig&&null!=l.fieldConfig.perDiemstartDate&&l.fieldConfig.perDiemstartDate.is_visible?null==l.fieldConfig||null==l.fieldConfig.perDiemstartDate?null:l.fieldConfig.perDiemstartDate.is_visible:0),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",8==(null==(d=l.claimForm.get("claims").at(n))||null==(m=d.get("claimTypeId"))?null:m.value)&&null!=l.fieldConfig&&null!=l.fieldConfig.perDiemStartTime&&l.fieldConfig.perDiemStartTime.is_visible?null==l.fieldConfig||null==l.fieldConfig.perDiemStartTime?null:l.fieldConfig.perDiemStartTime.is_visible:0),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",8==(null==(p=l.claimForm.get("claims").at(n))||null==(u=p.get("claimTypeId"))?null:u.value)&&null!=l.fieldConfig&&null!=l.fieldConfig.perDiemEndDate&&l.fieldConfig.perDiemEndDate.is_visible?null==l.fieldConfig||null==l.fieldConfig.perDiemEndDate?null:l.fieldConfig.perDiemEndDate.is_visible:0),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",8==(null==(f=l.claimForm.get("claims").at(n))||null==(g=f.get("claimTypeId"))?null:g.value)&&null!=l.fieldConfig&&null!=l.fieldConfig.perDiemEndTime&&l.fieldConfig.perDiemEndTime.is_visible?null==l.fieldConfig||null==l.fieldConfig.perDiemEndTime?null:l.fieldConfig.perDiemEndTime.is_visible:0),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",8==(null==(h=l.claimForm.get("claims").at(n))||null==(v=h.get("claimTypeId"))?null:v.value)&&null!=l.fieldConfig&&null!=l.fieldConfig.days&&l.fieldConfig.days.is_visible?null==l.fieldConfig||null==l.fieldConfig.days?null:l.fieldConfig.days.is_visible:0),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",8==(null==(C=l.claimForm.get("claims").at(n))||null==(x=C.get("claimTypeId"))?null:x.value)&&null!=l.fieldConfig&&null!=l.fieldConfig.location&&l.fieldConfig.location.is_visible?null==l.fieldConfig||null==l.fieldConfig.location?null:l.fieldConfig.location.is_visible:0),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null!=l.fieldConfig&&null!=l.fieldConfig.startDate&&l.fieldConfig.startDate.is_visible?null==l.fieldConfig||null==l.fieldConfig.startDate?null:l.fieldConfig.startDate.is_visible:0),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",18==(null==(y=l.claimForm.get("claims").at(n))||null==(b=y.get("claimTypeId"))?null:b.value)&&null!=l.fieldConfig&&null!=l.fieldConfig.fromLocation&&l.fieldConfig.fromLocation.is_visible?null==l.fieldConfig||null==l.fieldConfig.fromLocation?null:l.fieldConfig.fromLocation.is_visible:0),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",18==(null==(_=l.claimForm.get("claims").at(n))||null==(E=_.get("claimTypeId"))?null:E.value)&&null!=l.fieldConfig&&null!=l.fieldConfig.toLocation&&l.fieldConfig.toLocation.is_visible?null==l.fieldConfig||null==l.fieldConfig.toLocation?null:l.fieldConfig.toLocation.is_visible:0),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",18==(null==(F=l.claimForm.get("claims").at(n))||null==(I=F.get("claimTypeId"))?null:I.value)&&null!=l.fieldConfig&&null!=l.fieldConfig.vehicleType&&l.fieldConfig.vehicleType.is_visible?null==l.fieldConfig||null==l.fieldConfig.vehicleType?null:l.fieldConfig.vehicleType.is_visible:0),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",18==(null==(w=l.claimForm.get("claims").at(n))||null==(S=w.get("claimTypeId"))?null:S.value)&&null!=l.fieldConfig&&null!=l.fieldConfig.vehicleEngineType&&l.fieldConfig.vehicleEngineType.is_visible?null==l.fieldConfig||null==l.fieldConfig.vehicleEngineType?null:l.fieldConfig.vehicleEngineType.is_visible:0),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",18==(null==(D=l.claimForm.get("claims").at(n))||null==(O=D.get("claimTypeId"))?null:O.value)&&(null==l.fieldConfig||null==l.fieldConfig.miles?null:l.fieldConfig.miles.is_visible)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",1==l.isToShowConversionDetails),i["\u0275\u0275advance"](4),i["\u0275\u0275property"]("ngIf",1==e.controls.hasDuplicateError.value),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",l.isExpenseCreationAllowed)}}function Tl(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",201),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"](2).closeMessage()})),i["\u0275\u0275elementStart"](1,"span",202),i["\u0275\u0275elementStart"](2,"span",203),i["\u0275\u0275namespaceSVG"](),i["\u0275\u0275elementStart"](3,"svg",204),i["\u0275\u0275element"](4,"path",205),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275text"](5),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](5),i["\u0275\u0275textInterpolate1"](" ",e.payment_terms_message," ")}}function Pl(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"mat-icon",211),i["\u0275\u0275text"](1," save"),i["\u0275\u0275elementEnd"]())}function kl(e,t){}function Vl(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"mat-icon",211),i["\u0275\u0275text"](1," done_all"),i["\u0275\u0275elementEnd"]())}function Ll(e,t){}const Bl=function(e){return{"restict-cursor":e}};function Nl(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",206),i["\u0275\u0275elementStart"](1,"button",207),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"](2).saveAsDraft()})),i["\u0275\u0275template"](2,Pl,2,0,"mat-icon",208),i["\u0275\u0275template"](3,kl,0,0,"ng-template",null,209,i["\u0275\u0275templateRefExtractor"]),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"button",210),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"](2).saveExpense()})),i["\u0275\u0275template"](6,Vl,2,0,"mat-icon",208),i["\u0275\u0275template"](7,Ll,0,0,"ng-template",null,209,i["\u0275\u0275templateRefExtractor"]),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275reference"](4),t=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](1),i["\u0275\u0275styleProp"]("background-color",t.isBeingDrafted||t.isBeingClaimed||t.isBeingWorkflowDetermined||t.isBeingCheckedForDuplicate||t.restrictClaimCreation?"#d3d3d3":"#1F2347"),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction1"](11,Bl,t.isBeingDrafted||t.isBeingClaimed||t.isBeingWorkflowDetermined||t.restrictClaimCreation))("disabled",t.isBeingDrafted||t.isBeingClaimed||t.isBeingWorkflowDetermined||t.isBeingCheckedForDuplicate),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",0==t.isBeingDrafted)("ngIfElse",e),i["\u0275\u0275advance"](3),i["\u0275\u0275styleProp"]("background-color",t.isBeingDrafted||t.isBeingClaimed||t.isBeingWorkflowDetermined||t.isBeingCheckedForDuplicate||t.restrictClaimCreation?"#d3d3d3":t.theme_color1),i["\u0275\u0275property"]("disabled",t.isBeingDrafted||t.isBeingClaimed||t.isBeingWorkflowDetermined||t.isBeingCheckedForDuplicate||t.restrictClaimCreation),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",0==t.isBeingDrafted)("ngIfElse",e)}}function Rl(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div"),i["\u0275\u0275elementStart"](1,"div",30),i["\u0275\u0275template"](2,re,5,9,"div",31),i["\u0275\u0275template"](3,ce,6,6,"div",31),i["\u0275\u0275elementStart"](4,"div",32),i["\u0275\u0275elementStart"](5,"div",33),i["\u0275\u0275text"](6),i["\u0275\u0275template"](7,me,1,1,void 0,13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](8,"app-exp-input-search",34),i["\u0275\u0275listener"]("change",(function(t){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"]().changeLegalEntity(t)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](9,fe,5,10,"div",31),i["\u0275\u0275template"](10,ge,6,7,"div",31),i["\u0275\u0275template"](11,Ce,5,10,"div",31),i["\u0275\u0275template"](12,Ie,12,12,"div",35),i["\u0275\u0275template"](13,Oe,2,1,"div",36),i["\u0275\u0275template"](14,ke,9,11,"div",31),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](15,"div",37),i["\u0275\u0275template"](16,qe,7,7,"div",31),i["\u0275\u0275template"](17,We,6,11,"div",31),i["\u0275\u0275template"](18,Ge,8,11,"div",31),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](19,Je,2,0,"ng-template",null,38,i["\u0275\u0275templateRefExtractor"]),i["\u0275\u0275template"](21,Ke,2,0,"ng-template",null,39,i["\u0275\u0275templateRefExtractor"]),i["\u0275\u0275template"](23,Xe,2,1,"ng-template",null,40,i["\u0275\u0275templateRefExtractor"]),i["\u0275\u0275template"](25,Qe,2,0,"ng-template",null,41,i["\u0275\u0275templateRefExtractor"]),i["\u0275\u0275template"](27,Ze,2,0,"ng-template",null,42,i["\u0275\u0275templateRefExtractor"]),i["\u0275\u0275elementStart"](29,"div",43),i["\u0275\u0275elementContainerStart"](30,44),i["\u0275\u0275elementStart"](31,"div",45),i["\u0275\u0275template"](32,Ml,52,35,"div",46),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementContainerEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](33,"div",43),i["\u0275\u0275elementStart"](34,"div",47),i["\u0275\u0275template"](35,Tl,6,1,"div",48),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](36,Nl,9,13,"div",49),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"]();let t=null,n=null;i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf",null!=e.fieldConfig&&null!=e.fieldConfig.claimedBy&&e.fieldConfig.claimedBy.is_visible?null==e.fieldConfig||null==e.fieldConfig.claimedBy?null:e.fieldConfig.claimedBy.is_visible:0),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null!=e.fieldConfig&&null!=e.fieldConfig.claimDate&&e.fieldConfig.claimDate.is_visible?null==e.fieldConfig||null==e.fieldConfig.claimDate?null:e.fieldConfig.claimDate.is_visible:0),i["\u0275\u0275advance"](3),i["\u0275\u0275textInterpolate1"]("",null!=e.fieldConfig&&null!=e.fieldConfig.legalEntityCode&&e.fieldConfig.legalEntityCode.field_label?e.fieldConfig.legalEntityCode.field_label:"Legal Entity"," "),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",(null==(t=e.claimForm.get("legalEntity"))?null:t.hasError("required"))&&(null==(t=e.claimForm.get("legalEntity"))?null:t.touched)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction1"](22,ae,(null==(n=e.claimForm.get("legalEntity"))?null:n.hasError("required"))&&(null==(n=e.claimForm.get("legalEntity"))?null:n.touched)))("placeholder",null!=e.fieldConfig&&null!=e.fieldConfig.legalEntityCode&&e.fieldConfig.legalEntityCode.field_label?e.fieldConfig.legalEntityCode.field_label:"Legal Entity")("list",e.legalEntityList)("disabled",!(null==e.fieldConfig||null==e.fieldConfig.legalEntityCode||!e.fieldConfig.legalEntityCode.is_disabled))("bgColorOnDisable",!(null==e.fieldConfig||null==e.fieldConfig.legalEntityCode||!e.fieldConfig.legalEntityCode.is_disabled))("hideMatLabel",!0),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null!=e.fieldConfig&&null!=e.fieldConfig.costCenterCode&&e.fieldConfig.costCenterCode.is_visible?null==e.fieldConfig||null==e.fieldConfig.costCenterCode?null:e.fieldConfig.costCenterCode.is_visible:0),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null!=e.fieldConfig&&null!=e.fieldConfig.department&&e.fieldConfig.department.is_visible?null==e.fieldConfig||null==e.fieldConfig.department?null:e.fieldConfig.department.is_visible:0),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null!=e.fieldConfig&&null!=e.fieldConfig.expenseTag&&e.fieldConfig.expenseTag.is_visible?null==e.fieldConfig||null==e.fieldConfig.expenseTag?null:e.fieldConfig.expenseTag.is_visible:0),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",1==e.isManualApproverSelectionApplicable&&1==e.isHeaderLevelApprover),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",1==e.isManualApproverSelectionApplicable&&1==e.isHeaderLevelApprover),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null!=e.fieldConfig&&null!=e.fieldConfig.headerDescription&&e.fieldConfig.headerDescription.is_visible?null==e.fieldConfig||null==e.fieldConfig.headerDescription?null:e.fieldConfig.headerDescription.is_visible:0),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf","I"==e.costCenterType&&(null!=e.fieldConfig&&null!=e.fieldConfig.customerBilling&&e.fieldConfig.customerBilling.is_visible?null==e.fieldConfig||null==e.fieldConfig.customerBilling?null:e.fieldConfig.customerBilling.is_visible:0)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",(null!=e.fieldConfig&&null!=e.fieldConfig.spentFrom&&e.fieldConfig.spentFrom.is_visible?null==e.fieldConfig||null==e.fieldConfig.spentFrom?null:e.fieldConfig.spentFrom.is_visible:0)&&e.isCorporateCardApplicable),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",(null!=e.fieldConfig&&null!=e.fieldConfig.corporateCard&&e.fieldConfig.corporateCard.is_visible?null==e.fieldConfig||null==e.fieldConfig.corporateCard?null:e.fieldConfig.corporateCard.is_visible:0)&&e.isCorporateCardApplicable),i["\u0275\u0275advance"](14),i["\u0275\u0275property"]("ngForOf",e.claimFormData.claims.controls),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngIf",e.isDialogOpen),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",e.isExpenseCreationAllowed)}}let ql=(()=>{class e{constructor(e,t,r,d,p,u,f,g,h,v,C,x,y,b){this.fb=e,this._expHomeService=t,this._auth=r,this._wfService=d,this._util=p,this._fileSaver=u,this.dialog=f,this.dialogRef=g,this.draftInfo=h,this._expenseSharedService=v,this._snackBar=C,this.spinnerService=x,this._toaster=y,this.timeFormat=b,this.claimType=[],this.warningMessages=[],this.currentlyActiveFormType="singleExpenseCreation",this.defaulttaxPercentage=[{id:1e3,config_code:1,tax_description:"Other",tax_percentage:null,name:"Other"}],this.restrictClaimCreation=!1,this.isCorporateCardApplicable=!1,this.enableWarning=!1,this.isBeingDrafted=!1,this.isBeingCheckedForDuplicate=!1,this.isBeingClaimed=!1,this.isBeingWorkflowDetermined=!1,this.costCenter=null,this.showIndex=20,this.fieldConfig={},this.defaultLegalEntityBasedOnClaimer=!1,this.attachment_needed_or_not=""==this._expHomeService.getLabelForExpense(65,9)?"Attachments not Mandatory":this._expHomeService.getLabelForExpense(65,9),this.checkForDropDown=""==this._expHomeService.checkIfWeNeedDropdown(65,10)?"Use search in Expense":this._expHomeService.checkIfWeNeedDropdown(65,10),this.isDialogOpen=!0,this.imgArr=[],this.costCenterList=[],this.contextId=[],this.deletedContextId=[],this.close=new i.EventEmitter,this.maxFileSize=10485760,this.fileType=m.a.fileuploadedSupportedFileTypes,this.currentFileIndex=0,this.tomorrow=c().format("YYYY-MM-DD"),this.employeeSearchUrl=window.location.origin+"/api/okr/objective/searchEmployee",this.token=this._auth.getToken(),this.selectedPeopleInvolvedList=[],this.selectedPeopleInvolvedId=[],this.legalEntityList=[],this.currencyList=[],this.expenseTagList=[],this.getTaxRateType=[],this.isDefaultCostCenter=!1,this.isDisableClaimedBy=!1,this.Validators=a.H,this.isHeaderLevelApprover=!1,this.getManualApproverExpenseConfig=()=>new Promise((e,t)=>{this._expHomeService.getExpenseConfig().subscribe(t=>{if(t.data){if(null!=t.data){let e=JSON.parse(t.data[0].expense_application_config);null!=e&&(this.isManualApproverSelectionApplicable=!!(null==e?void 0:e.is_manual_approver_selection_applicable)&&(null==e?void 0:e.is_manual_approver_selection_applicable),e.allowed_no_of_billed_days&&0!=e.allowed_no_of_billed_days&&(this.restrictionBilledDate=e.allowed_no_of_billed_days,this.billingStartDate=c().subtract(e.allowed_no_of_billed_days,"days").format("YYYY-MM-DD")),1==e.is_disable_claimedby&&(this.isDisableClaimedBy=!0),1==e.is_header_level_approver&&(this.isHeaderLevelApprover=!0))}e(t.data)}},e=>{console.log(e),t(e)})}),this.showClaimCreation=!1,this.theme_color1="#52C41A",this.theme_color2="white",this.formBorderColor="#cf0001",this.onDraftOpenLegalEntityData=!0,this.isToShowConversionDetails=!1,this.uploadFileAttachment=()=>{this.allowedMimeType=["*/*"],this.uploader.onProgressItem=e=>{this.claimForm.controls.claims.controls["singleExpenseCreation"==this.currentlyActiveFormType?0:this.currentFileIndex].controls.uploadInProgress.patchValue(!0)},this.uploader.onCompleteItem=(e,t,n,l)=>{if(this.claimForm.controls.claims.controls["singleExpenseCreation"==this.currentlyActiveFormType?0:this.currentFileIndex].controls.uploadInProgress.patchValue(!1),200==n&&t.length>0&&null!=t){let e=JSON.parse(t),n=[];n=n.concat(e.files_json),this.claimForm.controls.claims.controls["singleExpenseCreation"==this.currentlyActiveFormType?0:this.currentFileIndex].controls.attachment.patchValue(n),this.uploader.clearQueue()}else this._util.showMessage("Unable to upload","Dismiss",3e3)}},this.getDepartmentMaster=e=>{this._expHomeService.getDepartmentMaster(e).subscribe(e=>Object(l.c)(this,void 0,void 0,(function*(){e&&1==e.length&&this.claimForm.get("department").patchValue(e[0].department_name)})),e=>{console.error(e)})},this.getWorkFlowProperty=()=>new Promise((e,t)=>{this._expHomeService.getExpenseWorkFlowProperties().subscribe(t=>{e(t)},e=>{console.error(e),t(e)})}),this.getWorkFlowPropertyForDelivery=(e,t)=>new Promise((n,l)=>{this._expHomeService.getExpenseWorkFlowPropertiesOtherTypes(e,t).subscribe(e=>{n(e)},e=>{console.error(e),l(e)})}),this.getExpenseClaimWorkflows=()=>new Promise((e,t)=>{this._expHomeService.getExpenseClaimWorkFlowProperties().subscribe(t=>{"S"==t.messType&&e(t.data)},e=>{console.log(e),t(e)})}),this.isManualApproverSelectionApplicable=!1,this.getExpenseConfig=()=>new Promise((e,t)=>{this._expHomeService.getExpenseConfig().subscribe(t=>{if(t.data){if(null!=t.data){let e=JSON.parse(t.data[0].expense_application_config);null!=e&&(this.isManualApproverSelectionApplicable=!!(null==e?void 0:e.is_manual_approver_selection_applicable)&&(null==e?void 0:e.is_manual_approver_selection_applicable),e.allowed_no_of_billed_days&&0!=e.allowed_no_of_billed_days&&(this.billingStartDate=c().subtract(e.allowed_no_of_billed_days,"days").format("YYYY-MM-DD")),1==e.is_disable_claimedby&&(this.isDisableClaimedBy=!0),e.is_to_show_payment_terms_message_to_claimer&&1==e.is_to_show_payment_terms_message_to_claimer&&(this.is_to_show_payment_terms_message_to_claimer=!0,this.payment_terms_message=e.payment_terms_message,this.expense_creation_restriction_message=e.expense_creation_restriction_message))}e(t.data)}},e=>{console.log(e),t(e)})}),this.claimedByForDraft=!0,this.onDraftOpen=!0,this.onDraftOpenLegalEntity=!0,this.detectValueChanges=()=>{this.claimForm.get("claimedBy").valueChanges.subscribe(e=>{this.isDefaultCostCenter?this.draftInfo||this.getCostCenterOfClaimer():(this.claimForm.controls.costCenter.patchValue(null),this.claimForm.controls.costCenterCode.patchValue(null),this.changeInClaimedBy()),this.costCenter&&(1==this.isManualApproverSelectionApplicable?this.getExpenseApproverBasedOnRoleFunc():this.determineWorkflow())}),this.claimForm.get("costCenter").valueChanges.subscribe(e=>Object(l.c)(this,void 0,void 0,(function*(){this.costCenter=""!=e?e:null}))),this.claimForm.get("costCenterCode").valueChanges.subscribe(e=>Object(l.c)(this,void 0,void 0,(function*(){var t,n;if(this.costCenter=""!=e?e:null,this.costCenter){let e=this.costCenter?"string"==typeof this.costCenter?(null===(t=this.parseJSON(this.costCenter))||void 0===t?void 0:t.cost_centre)?null===(n=this.parseJSON(this.costCenter))||void 0===n?void 0:n.cost_centre:this.parseJSON(this.costCenter):this.costCenter.cost_centre:"";yield this.getDepartmentMaster(e),this.defaultCustomerBillingConfig(),0==this.isManualApproverSelectionApplicable&&this.determineWorkflow()}}))),this.claimForm.get("legalEntity").valueChanges.subscribe(e=>Object(l.c)(this,void 0,void 0,(function*(){null!=e&&""!=e&&0==this.isManualApproverSelectionApplicable&&(this.getEntityCurrency(),this.determineWorkflow())}))),this.claimForm.get("legalEntityCode").valueChanges.subscribe(e=>Object(l.c)(this,void 0,void 0,(function*(){null!=e&&""!=e&&(0==this.isManualApproverSelectionApplicable?(this.getEntityCurrency(),this.determineWorkflow()):this.getFormFieldConfig())})))},this.itemAmountChange=(e,t)=>{this.claimForm.get(`claims.${t}.taxPercentage`),this.amountcalculation(),0==this.isManualApproverSelectionApplicable&&this.determineWorkflow()},this.approversList=[],this.getExpenseApproverBasedOnRole=()=>Object(l.c)(this,void 0,void 0,(function*(){let e=this.claimForm.value.costCenter,t="string"==typeof e?this.parseJSON(e):e;return this.getDepartmentMaster(null==t?void 0:t.cost_centre),new Promise((e,n)=>{this._expHomeService.getExpenseApproverBasedOnRole(this.claimForm.value.claimDate,this.claimForm.value.claimedBy,null==t?void 0:t.cost_centre,null==t?void 0:t.cost_centre_description).subscribe(e=>(this.approversList=e.data?e.data:[],this.claimForm.value.headerLevelApprover&&this.claimForm.value.headerLevelApprover.length>0&&this.approversList&&0==o.filter(this.approversList,{oid:this.claimForm.value.headerLevelApprover[0].oid}).length&&(this.approversList=this.approversList.concat(this.claimForm.value.headerLevelApprover)),this._util.showMessage(e.messText,"Dismiss",3e3),e),e=>{n(e),console.error(e)})})})),this.itemCurrencyChange=e=>{let t=this.claimForm.get(`claims.${e}.currencyCode`).value,n=this.currencyList.find(e=>e.currency_id==t);n&&this.claimForm.get("claims").at(e).patchValue({currency:JSON.stringify(n),currenyCodeValue:n.currency_code,conversionRate:1}),0==this.isManualApproverSelectionApplicable&&this.determineWorkflow(),this.currencyconversionrate();let l=this.fixNumberByDecimal(this.claimForm.value.claims[e].amount,this.claimForm.value.claims[e].currenyCodeValue);this.claimForm.controls.claims.controls[e].controls.amount.patchValue(0==parseFloat(l)?null:l);let i=this.fixNumberByDecimal(this.claimForm.value.claims[e].conversionRate,this.claimForm.value.claims[e].currenyCodeValue);this.claimForm.controls.claims.controls[e].controls.conversionRate.patchValue(i);let o=this.fixNumberByDecimal(this.claimForm.value.claims[e].taxAmount,this.claimForm.value.claims[e].currenyCodeValue);o="0.00"==o?null:o,this.claimForm.controls.claims.controls[e].controls.taxAmount.patchValue(o);let a=this.fixNumberByDecimal(this.claimForm.value.claims[e].finalamount,this.claimForm.value.claims[e].currenyCodeValue);this.claimForm.controls.claims.controls[e].controls.finalamount.patchValue(a);let r=this.claimForm.value.claims[e].conversionRate;r=r?"string"==typeof r?parseFloat(r.replace(/,/g,"")):parseFloat(r):0;let s=this.claimForm.value.claims[e].amount;s=s?"string"==typeof s?parseFloat(s.replace(/,/g,"")):parseFloat(s):0;let c=this.fixNumberByDecimal(s*r,this.claimForm.value.claims[e].currenyCodeValue);this.claimForm.controls.claims.controls[e].controls.settlement.patchValue(c)},this.isTaxAmountEditable=!1,this.ontaxPercentageChange=e=>{let t=this.claimForm.get(`claims.${e}.taxPercentage`).value;if(1e3==t||null==t){if(this.isTaxAmountEditable=!0,this.claimForm.controls.claims.controls[e].controls.taxAmount.patchValue(null),this.claimForm.get("claims").at(e).patchValue({taxType:null,taxRate:null}),1e3==t){let t=this.claimForm.get(`claims.${e}.amount`).value,n=t?parseFloat(String(t).replace(/,/g,"")):null,l=this.claimForm.get(`claims.${e}.taxAmount`).value;if(n&&l){let t=null!=n?"string"==typeof n?parseFloat(n.replace(/,/g,"")):"number"==typeof n?n:null:null,i=l?"string"==typeof l?parseFloat(l.replace(/,/g,"")):parseFloat(l):null,o=this.fixNumberOnUI(0!=n?100*i/(t-i):0,this.claimForm.value.claims[e].currenyCodeValue);this.claimForm.controls.claims.controls[e].controls.taxRate.patchValue(o),this.claimForm.controls.claims.controls[e].controls.taxType.patchValue(`Others - ${o} %`)}}}else{this.claimForm.controls.claims.controls[e].controls.taxAmount.patchValue("0.00");let n=this.defaulttaxPercentage.find(e=>e.id==t);n&&this.claimForm.get("claims").at(e).patchValue({taxType:n.name,taxRate:n.tax_percentage}),this.amountcalculation()}},this.getAdminApproversHierarchy=(e,t,n)=>Object(l.c)(this,void 0,void 0,(function*(){let t=this.claimForm.value.costCenter,l="string"==typeof t?JSON.parse(t):t;this.getDepartmentMaster(l.cost_centre);let i={workflowId:e.id,sortByDesignation:e.sort_by_desgn,initiatorOId:this.claimedBy,approvalType:"P",approvalParams:n,costCentresAndTypes:[{costCentre:l.cost_centre,costCentreType:l.cost_centre_type}]};return new Promise((e,t)=>{this._expHomeService.getExpenseApproversHierarchy(i).subscribe(n=>{this._wfService.formatApproversHierarchy([l],n.data).then(t=>{e(t)},e=>{t(e),console.error(e)})},e=>{t(e),console.error(e)})})})),this.getExpenseApproversHierarchy=e=>Object(l.c)(this,void 0,void 0,(function*(){let t=this.claimForm.value.costCenter,n="string"==typeof t?JSON.parse(t):t;this.getDepartmentMaster(n.cost_centre);let l={workflowId:e.id,sortByDesignation:e.sort_by_desgn,initiatorOId:this.claimedBy,approvalType:e.approval_type,approvalParams:e.approval_params,costCentresAndTypes:[{costCentre:n.cost_centre,costCentreType:n.cost_centre_type}]};return new Promise((e,t)=>{this._expHomeService.getExpenseApproversHierarchy(l).subscribe(l=>{this._wfService.formatApproversHierarchy([n],l.data).then(t=>{e(t)},e=>{t(e),console.error(e)})},e=>{t(e),console.error(e)})})})),this.getClaimCategoryMasterByLegalEntity=()=>{this._expHomeService.getClaimCategoryByLE(this.claimForm.value.legalEntityCode).subscribe(e=>{this.claimType=e.data.map(e=>({active:!1,id:e.id,claim_type_id:e.claim_type_id,code:e.code,icon:e.mat_icon?e.mat_icon.toLowerCase():null,name:e.name,description:e.description,warningMsg:e.warning_msg,alertMsg:e.alert_msg,billable_attachment_warning_msg:e.billable_attachment_warning_msg,non_billable_attachment_warning_msg:e.non_billable_attachment_warning_msg})),this.refreshButtons()},e=>{console.log(e)})},this.getClaimCategoryMaster=()=>{this._expHomeService.getClaimCategory().subscribe(e=>{this.claimType=e.data.map(e=>({active:!1,id:e.id,code:e.code,icon:e.mat_icon.toLowerCase(),name:e.name,description:e.description,warningMsg:e.warning_msg,alertMsg:e.alert_msg,billable_attachment_warning_msg:e.billable_attachment_warning_msg,non_billable_attachment_warning_msg:e.non_billable_attachment_warning_msg})),this.refreshButtons(),this.draftInfo&&this.ngOnChanges()},e=>{console.log(e)})},this.getAdminApprovers=(e,t)=>new Promise((n,l)=>{this._expHomeService.getAdminApproversAID(e,t).subscribe(e=>{"S"==e.messType&&n(e.data)},e=>{console.log(e),l(e)})}),this.createForm=()=>Object(l.c)(this,void 0,void 0,(function*(){this.claimForm=this.fb.group({expenseType:["C"],claimedBy:[this.draftInfo?null:this._auth.getProfile().profile.oid,a.H.required],originalInitiatorOId:[this._auth.getProfile().profile.oid,a.H.required],legalEntity:["",a.H.required],expenseTag:["",a.H.required],department:[""],costCenter:["",a.H.required],claimDate:[c().format("DD-MMM-YYYY"),a.H.required],claims:this.fb.array([this.setClaimArray()]),costCenterCode:["",a.H.required],legalEntityCode:["",a.H.required],customerBilling:[3,a.H.required],headerLevelApprover:[null,a.H.required],headerLevelApproverList:[null],legalEntityCurrency:[null],headerDescription:[null,[a.H.required,a.H.maxLength(255)]],spentFrom:[""],corporateCard:[""]}),this.detectClaimFormChange(),console.log(this.claimForm)})),this.setClaimArray=()=>this.fb.group({categoryCode:["",a.H.required],category:["",a.H.required],categoryName:["",a.H.required],claimTypeId:["",a.H.required],amount:["",[a.H.required,Hl]],currency:["",a.H.required],currencyCode:["",a.H.required],currenyCodeValue:[""],taxPercentage:[""],taxRate:[""],taxType:[""],taxAmount:["",[Yl]],taxRatetype:[1],finalamount:[""],conversionRate:[1,[a.H.required,Hl]],currencyFrom:[""],currencyTo:[""],invoiceDate:[c().format("YYYY-MM-DD"),a.H.compose([a.H.required,this.invoiceDateValidator()])],description:["",a.H.required],approvers:["",a.H.required],workflowId:["",a.H.required],isAggregationAllowed:[""],attachment:[""],uploadInProgress:[!1],startDate:[c().format("YYYY-MM-DD")],endDate:[c().format("YYYY-MM-DD")],peopleInvolved:[""],contextId:[null],invoiceNo:[""],fromLocation:[""],toLocation:[""],vehicleType:[""],vehicleEngineType:[""],miles:[""],unit:[""],location:[""],perDiemstartDate:[""],perDiemEndDate:[""],perDiemStartTime:[""],perDiemEndTime:[""],noOfDays:["0.00"],approverMenu:[""],settlement:[""],legalEntityCurrency:[""],hasDuplicateError:[!1],duplicationError:[""]}),this.detectClaimFormChange=()=>{this.claimFormData.claims.valueChanges.subscribe(e=>{})},this.determineWorkflow=()=>{let e=this.claimForm.get("claims").value,t=this.claimForm.get("legalEntity").value;this.determineWorkflowSubscription&&this.determineWorkflowSubscription.unsubscribe();let n=o.filter(e,e=>{if(e.amount&&""!=e.amount&&e.currency&&""!=e.currency&&""!=e.categoryCode)return!0});if(e.length==n.length&&this.costCenter&&""!=t&&null!=t){for(let[t,n]of e.entries())this.claimForm.controls.claims.controls[t].controls.workflowId.reset(),this.claimForm.controls.claims.controls[t].controls.approvers.reset();this.expWorkflowProperties&&(this.isBeingWorkflowDetermined=!0,this.determineWorkflowSubscription=this._expHomeService.determineWfPropertyForEachItemClaim(e,t,this.expWorkflowProperties,this.costCenter.cost_centre).subscribe(e=>{this.isBeingWorkflowDetermined=!1,"S"==e.messType?this.getApproversAndFormat(e):"E"==e.messType&&(this.isBeingWorkflowDetermined=!1,this._util.showMessage(e.messText,"close"))},e=>{this.isBeingWorkflowDetermined=!1,console.error(e)}))}},this.getApproversAndFormat=e=>Object(l.c)(this,void 0,void 0,(function*(){let t=e.data;1==e.workflowNotFound&&this._util.showMessage(e.messText,"Dismiss",3e3);for(let[e,n]of t.entries())if(n.wfProperty){let t=this.claimForm.value.costCenter,l=("string"==typeof t&&JSON.parse(t),yield this.getExpenseApproversHierarchy(n.wfProperty));l&&l.length>0&&(this.claimForm.controls.claims.controls[e].controls.workflowId.patchValue(n.workflowId),this.claimForm.controls.claims.controls[e].controls.approvers.patchValue(l),this.claimForm.controls.claims.controls[e].controls.isAggregationAllowed.patchValue(n.wfProperty.aggregation_allowed))}})),this.detectAmountChangesInClaimArray=()=>{this.claimFormData.claims.valueChanges.subscribe(e=>{setTimeout(()=>{let e=this.claimForm.value.claims.map(e=>e.amount),t=0;e.forEach(e=>{""!=e&&(t+=e?"string"==typeof e?parseFloat(e.replace(/,/g,"")):parseFloat(e):0)}),this.claimForm.get("totalAmount").patchValue(t)},500)})},this.addClaim=(e,t,n,l)=>{this.currentlyActiveFormType="multipleExpenseCreation";const i=this.claimFormData.claims;i.push(this.fb.group({categoryCode:[t,a.H.required],category:[e,a.H.required],categoryName:[n,a.H.required],claimTypeId:[l,a.H.required],amount:["",[a.H.required,Hl]],currency:[this.defaultCurrency,a.H.required],currencyCode:[this.legalEntityCurrenyId,a.H.required],currenyCodeValue:[this.legalEntityCurrency],taxPercentage:[""],taxRate:[""],taxType:[""],taxAmount:[""],taxRatetype:[!1],finalamount:[""],conversionRate:[1,[a.H.required,Hl]],currencyFrom:[""],currencyTo:[""],invoiceDate:[c().format("YYYY-MM-DD"),a.H.compose([a.H.required,this.invoiceDateValidator()])],description:["",a.H.required],attachment:[""],approvers:[this.claimForm.value.headerLevelApprover?this.claimForm.value.headerLevelApprover:"",a.H.required],workflowId:[""],isAggregationAllowed:[""],uploadInProgress:[!1],startDate:[c().format("YYYY-MM-DD")],endDate:[c().format("YYYY-MM-DD")],peopleInvolved:[""],contextId:[null],invoiceNo:[""],approverMenu:[""],settlement:[""],fromLocation:[""],toLocation:[""],vehicleType:[""],vehicleEngineType:[""],miles:[""],unit:[""],location:[""],perDiemstartDate:[""],perDiemEndDate:[""],perDiemStartTime:[""],perDiemEndTime:[""],noOfDays:["0.00"],legalEntityCurrency:[""],hasDuplicateError:[!1],duplicationError:[""]})),this.updateCategoryValidators(i.length-1)},this.deleteClaim=e=>{var t,n;const l=this.claimFormData.claims;if(l.length>1){null!=this.claimFormData.claims.value[e].contextId&&this.deletedContextId.push(null===(n=null===(t=this.claimFormData)||void 0===t?void 0:t.claims)||void 0===n?void 0:n.value[e].contextId);let i=l.value[e].category;o.each(this.claimType,e=>{e.id==i&&(e.count=e.count?e.count-1:0)}),l.removeAt(e),0==this.isManualApproverSelectionApplicable&&this.determineWorkflow(),1==l.length&&(this.currentlyActiveFormType="singleExpenseCreation")}else this._util.showMessage("You should have alteast one expense item!","dismiss")},this.deleteQuickClaim=e=>{const t=this.claimFormData.claims;let n=this.claimForm.value.claims.map(e=>e.category).indexOf(e);t.removeAt(n)},this.selectClaimType=e=>{this.claimType[e].count=this.claimType[e].count?this.claimType[e].count+1:1;let t=this.claimForm.value.claims[0].category;""==t||null==t?(this.claimForm.controls.claims.controls[0].controls.category.patchValue(this.claimType[e].id),this.claimForm.controls.claims.controls[0].controls.categoryCode.patchValue(this.claimType[e].code),this.claimForm.controls.claims.controls[0].controls.categoryName.patchValue(this.claimType[e].name),this.claimForm.controls.claims.controls[0].controls.claimTypeId.patchValue(this.claimType[e].claim_type_id),this.updateCategoryValidators(0)):this.addClaim(this.claimType[e].id,this.claimType[e].code,this.claimType[e].name,this.claimType[e].claim_type_id),this.currencyconversionrate(),0==this.isManualApproverSelectionApplicable&&this.determineWorkflow()},this.updateCategoryValidators=e=>{var t;console.log("Updating validators for index:",e);let n=this.claimForm.get("claims").at(e),l=null===(t=n.get("claimTypeId"))||void 0===t?void 0:t.value;if(!this.fieldConfig)return;const i=["fromLocation","toLocation","vehicleType","miles","unit"],o=["location","perDiemstartDate","perDiemEndDate","perDiemStartTime","perDiemEndTime","noOfDays"];[...i,...o].forEach(e=>{var t,l;this.fieldConfig[e]&&(null===(t=n.get(e))||void 0===t||t.clearValidators(),null===(l=n.get(e))||void 0===l||l.updateValueAndValidity())}),18===l?i.forEach(e=>{var t,l,i,o;let r=n.get(e);if(r){let n;1==(null===(t=this.fieldConfig[e])||void 0===t?void 0:t.is_mandatory)&&(n=[a.H.required]),"fromLocation"===e&&1==(null===(l=this.fieldConfig.fromLocation)||void 0===l?void 0:l.is_mandatory)&&n.push(a.H.maxLength(100)),"toLocation"===e&&1==(null===(i=this.fieldConfig.toLocation)||void 0===i?void 0:i.is_mandatory)&&n.push(a.H.maxLength(100)),"miles"===e&&1==(null===(o=this.fieldConfig.miles)||void 0===o?void 0:o.is_mandatory)&&n.push(Hl),r.setValidators(n),r.updateValueAndValidity()}}):8===l&&o.forEach(e=>{var t,l,i,o,r,s;let c=n.get(e);if(c){let n;1==(null===(t=this.fieldConfig[e])||void 0===t?void 0:t.is_mandatory)&&(n=[a.H.required]),"location"===e&&1==(null===(l=this.fieldConfig.location)||void 0===l?void 0:l.is_mandatory)&&n.push(a.H.maxLength(100)),"perDiemStartTime"===e&&1==(null===(i=this.fieldConfig.perDiemStartTime)||void 0===i?void 0:i.is_mandatory)&&n.push(this.validateTimeFormat),"perDiemEndTime"===e&&1==(null===(o=this.fieldConfig.perDiemEndTime)||void 0===o?void 0:o.is_mandatory)&&n.push(this.validateTimeFormat),"perDiemstartDate"===e&&1==(null===(r=this.fieldConfig.perDiemstartDate)||void 0===r?void 0:r.is_mandatory)&&n.push(this.futureDateValidator()),"perDiemEndDate"===e&&1==(null===(s=this.fieldConfig.perDiemEndDate)||void 0===s?void 0:s.is_mandatory)&&n.push(this.futureDateValidator()),c.setValidators(n),c.updateValueAndValidity()}})},this.generateWarning=()=>{this.enableWarning=!1,this.claimForm.value.claims.forEach((e,t)=>{"Certifications"==e.categoryName&&(this.enableWarning=!0,this.claimType.forEach(e=>{"Certifications"==e.name&&(this.warningMessages.length>0&&this.warningMessages.pop(),this.warningMessages.push(e.warningMsg))}))})},this.closeForm=()=>{if(null!=this.draftInfo&&this.draftInfo.claims){let e=o.pluck(this.draftInfo.claims,"contextId"),t=o.difference(this.contextId,e);t&&t.length>0&&this._expHomeService.deleteContextIds(t)}else this.contextId&&this.contextId.length>0&&this._expHomeService.deleteContextIds(this.contextId);this.dialogRef.close(),this.resetForm(),this.close.emit("close")},this.saveExpense=()=>Object(l.c)(this,void 0,void 0,(function*(){var e,t,n,i;this.isBeingCheckedForDuplicate=!0;let a=yield this._expHomeService.getExpenseDuplicateValidation(this.claimForm.value,null===(e=this.draftInfo)||void 0===e?void 0:e.expHeaderId);if(a&&"E"==a.messType)return this._util.showMessage(a.data.toString(","),"close");let r=!1;for(let l=0;l<this.claimForm.value.claims.length;l++){let e=o.filter(a.data,{index:l});if((null==e?void 0:e.length)>0){r=!0;let t=o.pluck(e,"expense_id");this.claimForm.controls.claims.controls[l].controls.hasDuplicateError.patchValue(!0),this.claimForm.controls.claims.controls[l].controls.duplicationError.patchValue("Receipt with entered details already exist on "+t.toString(","))}else this.claimForm.controls.claims.controls[l].controls.hasDuplicateError.patchValue(null),this.claimForm.controls.claims.controls[l].controls.duplicationError.patchValue(null)}if(1==r)return this.isBeingCheckedForDuplicate=!1,void this._toaster.showWarning("Warning \u26a0\ufe0f",`Duplicate Receipt exist. Please create claim with unique\n         ${null===(t=this.fieldConfig.invoiceDate)||void 0===t?void 0:t.field_label.replace(/\*$/,"").trim()}, \n         ${null===(n=this.fieldConfig.amount)||void 0===n?void 0:n.field_label.replace(/\*$/,"").trim()} !`,7e3);if(1==this.isManualApproverSelectionApplicable&&1==this.isHeaderLevelApprover&&this.claimForm.get("claims").controls.forEach(e=>{e.patchValue({workflowId:this.workflowId,approvers:this.claimForm.value.headerLevelApprover,isAggregationAllowed:this.aggregationAllowed,approverMenu:this.claimForm.value.headerLevelApproverList,amount:e.controls.amount.value?"string"==typeof e.controls.amount.value?parseFloat(e.controls.amount.value.replace(/,/g,"")):parseFloat(e.controls.amount.value):null,finalamount:e.controls.finalamount.value?"string"==typeof e.controls.finalamount.value?parseFloat(e.controls.finalamount.value.replace(/,/g,"")):parseFloat(e.controls.finalamount.value):0,conversionRate:e.controls.conversionRate.value?"string"==typeof e.controls.conversionRate.value?parseFloat(e.controls.conversionRate.value.replace(/,/g,"")):parseFloat(e.controls.conversionRate.value):0,taxAmount:e.controls.taxAmount.value?"string"==typeof e.controls.taxAmount.value?parseFloat(e.controls.taxAmount.value.replace(/,/g,"")):parseFloat(e.controls.taxAmount.value):0,settlement:e.controls.settlement.value?"string"==typeof e.controls.settlement.value?parseFloat(e.controls.settlement.value.replace(/,/g,"")):parseFloat(e.controls.settlement.value):0})}),"Attachments Mandatory"==this.attachment_needed_or_not){if(!this.claimForm.valid)return this.isBeingCheckedForDuplicate=!1,this.markFormGroupTouched(this.claimForm),this.logInvalidFields(this.claimForm),void this._toaster.showWarning("Warning \u26a0\ufe0f","Kindly enter all mandatory fields!",7e3);if("singleExpenseCreation"==this.currentlyActiveFormType){if(this.isBeingCheckedForDuplicate=!1,1==(""==this.claimForm.value.claims[0].attachment||null==this.claimForm.value.claims[0].attachment)&&1==(""==this.claimForm.value.claims[0].contextId||null==this.claimForm.value.claims[0].contextId)){let e=null;if(1==this.claimForm.value.customerBilling){let t=o.pluck(this.claimForm.value.claims,"categoryCode"),n=this.claimType.filter(e=>t.includes(e.code)),l=o.pluck(n,"billable_attachment_warning_msg"),i=o.compact(o.uniq(l));i.length>0?(e=i.toString(),e=e.replace(",","/")):e="Receipts"}else{let t=o.pluck(this.claimForm.value.claims,"categoryCode"),n=this.claimType.filter(e=>t.includes(e.code)),l=o.pluck(n,"non_billable_attachment_warning_msg"),i=o.compact(o.uniq(l));i.length>0?(e=i.toString(),e=e.replace(",","/")):e="Receipts"}return void this._toaster.showWarning("Warning \u26a0\ufe0f",`Kindly attach ${e}!`,7e3)}}else{this.isBeingCheckedForDuplicate=!1;let e=null;if(1==this.claimForm.value.customerBilling){let t=o.pluck(this.claimForm.value.claims,"categoryCode"),n=this.claimType.filter(e=>t.includes(e.code)),l=o.pluck(n,"billable_attachment_warning_msg"),i=o.compact(o.uniq(l));i.length>0?(e=i.toString(),e=e.replace(",","/")):e="Receipts"}else{let t=o.pluck(this.claimForm.value.claims,"categoryCode"),n=this.claimType.filter(e=>t.includes(e.code)),l=o.pluck(n,"non_billable_attachment_warning_msg"),i=o.compact(o.uniq(l));i.length>0?(e=i.toString(),e=e.replace(",","/")):e="Receipts"}let t=0;for(let n of this.claimFormData.claims.controls)((null===(i=n.controls.attachment.value[0])||void 0===i?void 0:i.fileName)||n.controls.contextId.value)&&(t+=1);if(this.claimFormData.claims.controls.length!=t)return void this._toaster.showWarning("Warning \u26a0\ufe0f",`Kindly attach ${e} for all claims!`,7e3)}if(this.claimForm.valid){this.isBeingCheckedForDuplicate=!1;let e=this.claimForm.value;e.claimDate=this.claimForm.value.claimDate,e.isExpenseCreatedFromWeb=1,e.claims.forEach((e,t)=>{e.systemConversionRate=this.conversionRateData&&this.conversionRateData[t]?this.conversionRateData[t].conversionRate:1}),this.deletedContextId.length>0&&this._expHomeService.deleteContextIds(this.deletedContextId),this.draftInfo?this.convertTheDraftIntoExpense(e):this.createAsNewExpense(e)}else this.isBeingCheckedForDuplicate=!1,this.checkFormValidators()}else{this.isBeingCheckedForDuplicate=!1;let e=!1;if(o.each(this.claimForm.value.claims,t=>{t.contextId&&""!=t.contextId||(e=!0)}),e){let e="Are you sure you want to proceed without attaching the expense receipts?",t=yield this._util.openConfirmationSweetAlertWithCustom(e,"");if(!t||0==t)return;if(this.claimForm.valid){this.claimForm.get("claims").controls.forEach(e=>{e.patchValue({workflowId:this.workflowId,approvers:this.claimForm.value.headerLevelApprover,isAggregationAllowed:this.aggregationAllowed,approverMenu:this.claimForm.value.headerLevelApproverList,amount:e.controls.amount.value?"string"==typeof e.controls.amount.value?parseFloat(e.controls.amount.value.replace(/,/g,"")):parseFloat(e.controls.amount.value):0,finalamount:e.controls.finalamount.value?"string"==typeof e.controls.finalamount.value?parseFloat(e.controls.finalamount.value.replace(/,/g,"")):parseFloat(e.controls.finalamount.value):0,conversionRate:e.controls.conversionRate.value?"string"==typeof e.controls.conversionRate.value?parseFloat(e.controls.conversionRate.value.replace(/,/g,"")):parseFloat(e.controls.conversionRate.value):0,taxAmount:e.controls.taxAmount.value?"string"==typeof e.controls.taxAmount.value?parseFloat(e.controls.taxAmount.value.replace(/,/g,"")):parseFloat(e.controls.taxAmount.value):0,settlement:e.controls.settlement.value?"string"==typeof e.controls.settlement.value?parseFloat(e.controls.settlement.value.replace(/,/g,"")):parseFloat(e.controls.settlement.value):0})});let e=this.claimForm.value;e.claimDate=this.claimForm.value.claimDate,e.isExpenseCreatedFromWeb=1,e.claims.forEach((e,t)=>{e.systemConversionRate=this.conversionRateData&&this.conversionRateData[t]?this.conversionRateData[t].conversionRate:1}),this.deletedContextId.length>0&&this._expHomeService.deleteContextIds(this.deletedContextId),this.draftInfo?this.convertTheDraftIntoExpense(e):(this.isBeingClaimed=!0,this.createNewClaimSubscription&&this.createNewClaimSubscription.unsubscribe(),this.createNewClaimSubscription=this._expHomeService.createOneOrMoreExpenses(e,this.approversList).subscribe(e=>Object(l.c)(this,void 0,void 0,(function*(){"S"==e.messType?(this._toaster.showSuccess("Success","Claim request created successfully",7e3),this.isBeingClaimed=!1,this.dialogRef.close("refresh")):"E"==e.messType&&(this._toaster.showError("Error",e.messText,7e3),this.isBeingClaimed=!1)})),e=>{console.error(e),this._toaster.showError("Error","Oops! something went wrong.",7e3),this.isBeingClaimed=!1}))}else this.checkFormValidators()}else if(this.claimForm.valid){this.claimForm.get("claims").controls.forEach(e=>{e.patchValue({workflowId:this.workflowId,approvers:this.claimForm.value.headerLevelApprover,isAggregationAllowed:this.aggregationAllowed,approverMenu:this.claimForm.value.headerLevelApproverList,amount:e.controls.amount.value?"string"==typeof e.controls.amount.value?parseFloat(e.controls.amount.value.replace(/,/g,"")):parseFloat(e.controls.amount.value):0,finalamount:e.controls.finalamount.value?"string"==typeof e.controls.finalamount.value?parseFloat(e.controls.finalamount.value.replace(/,/g,"")):parseFloat(e.controls.finalamount.value):0,conversionRate:e.controls.conversionRate.value?"string"==typeof e.controls.conversionRate.value?parseFloat(e.controls.conversionRate.value.replace(/,/g,"")):parseFloat(e.controls.conversionRate.value):0,taxAmount:e.controls.taxAmount.value?"string"==typeof e.controls.taxAmount.value?parseFloat(e.controls.taxAmount.value.replace(/,/g,"")):parseFloat(e.controls.taxAmount.value):0,settlement:e.controls.settlement.value?"string"==typeof e.controls.settlement.value?parseFloat(e.controls.settlement.value.replace(/,/g,"")):parseFloat(e.controls.settlement.value):0})});let e=this.claimForm.value;e.claimDate=this.claimForm.value.claimDate,e.isExpenseCreatedFromWeb=1,e.claims.forEach((e,t)=>{e.systemConversionRate=this.conversionRateData&&this.conversionRateData[t]?this.conversionRateData[t].conversionRate:1}),this.deletedContextId.length>0&&this._expHomeService.deleteContextIds(this.deletedContextId),this.draftInfo?this.convertTheDraftIntoExpense(e):(this.isBeingClaimed=!0,this.createNewClaimSubscription&&this.createNewClaimSubscription.unsubscribe(),this.createNewClaimSubscription=this._expHomeService.createOneOrMoreExpenses(e,this.approversList).subscribe(e=>Object(l.c)(this,void 0,void 0,(function*(){"S"==e.messType?(this._toaster.showSuccess("Success","Claim request created successfully",7e3),this.isBeingClaimed=!1,this.dialogRef.close("refresh")):"E"==e.messType&&(this._toaster.showError("Error",e.messText,7e3),this.isBeingClaimed=!1)})),e=>{console.error(e),this._toaster.showError("Error","Oops! something went wrong.",7e3),this.isBeingClaimed=!1}))}else this.checkFormValidators()}})),this.checkFormValidators=()=>{var e,t,n,l,i,a,r,s,c,d,m,p,u,f,g,h,v,C,x,y,b,_,E,F,I,w,S,D,O,A,M,T,P,k,V,L,B,N,R,q,H,Y,j,W,$,U,z,G,J,K,X,Q,Z,ee,te,ne,le,ie,oe,ae,re,se,ce;let de=this.claimForm.get("claims").value;if(this.claimForm.get("claimedBy").invalid)return this._util.showMessage(`Please fill ${(null===(t=null===(e=this.fieldConfig)||void 0===e?void 0:e.claimedBy)||void 0===t?void 0:t.field_label)?null===(l=null===(n=this.fieldConfig)||void 0===n?void 0:n.claimedBy)||void 0===l?void 0:l.field_label.replace(/\*$/,"").trim():"Claimed By"} !`,"close");if(this.claimForm.get("originalInitiatorOId").invalid)return this._util.showMessage("Initiator Id has not been assigned !","close");if(this.claimForm.get("legalEntity").invalid)return this._util.showMessage(`Please fill ${(null===(a=null===(i=this.fieldConfig)||void 0===i?void 0:i.legalEntityCode)||void 0===a?void 0:a.field_label)?null===(s=null===(r=this.fieldConfig)||void 0===r?void 0:r.legalEntityCode)||void 0===s?void 0:s.field_label.replace(/\*$/,"").trim():"Legal Entity Id"}  !`,"close");if(this.claimForm.get("costCenter").invalid)return this._util.showMessage(`Please fill the ${(null===(d=null===(c=this.fieldConfig)||void 0===c?void 0:c.costCenterCode)||void 0===d?void 0:d.field_label)?null===(p=null===(m=this.fieldConfig)||void 0===m?void 0:m.costCenterCode)||void 0===p?void 0:p.field_label.replace(/\*$/,"").trim():"Cost Center"} !`,"close");if(this.claimForm.get("expenseTag").invalid)return this._util.showMessage(`Please choose the ${(null===(f=null===(u=this.fieldConfig)||void 0===u?void 0:u.expenseTag)||void 0===f?void 0:f.field_label)?null===(h=null===(g=this.fieldConfig)||void 0===g?void 0:g.expenseTag)||void 0===h?void 0:h.field_label.replace(/\*$/,"").trim():"Expense Tag"} !`,"close");if(this.claimForm.get("headerLevelApprover").invalid)return this._util.showMessage("Approver is not selected. Choose the Approver to proceed","close");if(1==(null===(C=null===(v=this.fieldConfig)||void 0===v?void 0:v.department)||void 0===C?void 0:C.is_visible)&&!this.claimForm.value.department)return this._util.showMessage("Department has not been determined , Please change the Cost Center !","close");if(this.claimForm.get("headerDescription").invalid||""==(null===(x=this.claimForm.get("headerDescription").value)||void 0===x?void 0:x.trim()))return this._util.showMessage("Please fill "+((null===(b=null===(y=this.fieldConfig)||void 0===y?void 0:y.headerDescription)||void 0===b?void 0:b.field_label)?null===(E=null===(_=this.fieldConfig)||void 0===_?void 0:_.headerDescription)||void 0===E?void 0:E.field_label.replace(/\*$/,"").trim():"Description"),"close");if(this.claimForm.get("customerBilling").invalid)return this._util.showMessage(`Please choose the ${(null===(I=null===(F=this.fieldConfig)||void 0===F?void 0:F.customerBilling)||void 0===I?void 0:I.field_label)?null===(S=null===(w=this.fieldConfig)||void 0===w?void 0:w.customerBilling)||void 0===S?void 0:S.field_label.replace(/\*$/,"").trim():"Customer Billing"} !`,"close");if(null!=o.findWhere(de,{description:""})||null!=o.findWhere(de,{description:null})||o.find(de,e=>{var t;return""===(null===(t=e.description)||void 0===t?void 0:t.trim())}))return this._util.showMessage(`Please fill the ${(null===(O=null===(D=this.fieldConfig)||void 0===D?void 0:D.description)||void 0===O?void 0:O.field_label)?null===(M=null===(A=this.fieldConfig)||void 0===A?void 0:A.description)||void 0===M?void 0:M.field_label.replace(/\*$/,"").trim():"Description"} ! `,"close");if(null!=o.findWhere(de,{categoryCode:""})||null!=o.findWhere(de,{categoryCode:null}))return this._util.showMessage(`Please choose the ${(null===(P=null===(T=this.fieldConfig)||void 0===T?void 0:T.claimType)||void 0===P?void 0:P.field_label)?null===(V=null===(k=this.fieldConfig)||void 0===k?void 0:k.claimType)||void 0===V?void 0:V.field_label.replace(/\*$/,"").trim():"Claim Type"} ! `,"close");if(null!=o.findWhere(de,{currency:""})||null!=o.findWhere(de,{currency:null}))return this._util.showMessage("Please choose the currency !","close");if(null!=o.findWhere(de,{amount:""})||null!=o.findWhere(de,{amount:null})||null!=o.findWhere(de,{amount:"0"})||null!=o.findWhere(de,{amount:0}))return this._util.showMessage(`Please fill the ${(null===(B=null===(L=this.fieldConfig)||void 0===L?void 0:L.amount)||void 0===B?void 0:B.field_label)?null===(R=null===(N=this.fieldConfig)||void 0===N?void 0:N.amount)||void 0===R?void 0:R.field_label.replace(/\*$/,"").trim():"amount"} !`,"close");if(null!=o.findWhere(de,{invoiceDate:""})||null!=o.findWhere(de,{invoiceDate:null}))return this._util.showMessage(`Please fill the ${(null===(H=null===(q=this.fieldConfig)||void 0===q?void 0:q.invoiceDate)||void 0===H?void 0:H.field_label)?null===(j=null===(Y=this.fieldConfig)||void 0===Y?void 0:Y.invoiceDate)||void 0===j?void 0:j.field_label.replace(/\*$/,"").trim():"Receipt Date"} !`,"close");if((null===($=null===(W=this.fieldConfig)||void 0===W?void 0:W.taxPercentage)||void 0===$?void 0:$.is_visible)&&null!=o.findWhere(de,{taxPercentage:""})||null!=o.findWhere(de,{taxPercentage:null}))return this._util.showMessage(`Please choose the ${(null===(z=null===(U=this.fieldConfig)||void 0===U?void 0:U.taxPercentage)||void 0===z?void 0:z.field_label)?null===(J=null===(G=this.fieldConfig)||void 0===G?void 0:G.taxPercentage)||void 0===J?void 0:J.field_label.replace(/\*$/,"").trim():"Tax Percentage"} !`,"close");if(null!=o.findWhere(de,{invoiceNo:""})||null!=o.findWhere(de,{invoiceNo:null})){if(1==(null===(X=null===(K=this.fieldConfig)||void 0===K?void 0:K.invoiceNo)||void 0===X?void 0:X.is_mandatory))return this._util.showMessage(`Please fill the ${(null===(Z=null===(Q=this.fieldConfig)||void 0===Q?void 0:Q.invoiceNo)||void 0===Z?void 0:Z.field_label)?null===(te=null===(ee=this.fieldConfig)||void 0===ee?void 0:ee.invoiceNo)||void 0===te?void 0:te.field_label.replace(/\*$/,"").trim():"Invoice No"} ! `,"close")}else{if(null!=o.findWhere(de,{description:""})||null!=o.findWhere(de,{description:null}))return this._util.showMessage(`Please fill the ${(null===(le=null===(ne=this.fieldConfig)||void 0===ne?void 0:ne.description)||void 0===le?void 0:le.field_label)?null===(oe=null===(ie=this.fieldConfig)||void 0===ie?void 0:ie.description)||void 0===oe?void 0:oe.field_label.replace(/\*$/,"").trim():"description"} ! `,"close");if(null!=o.findWhere(de,{approvers:""})||null!=o.findWhere(de,{approvers:null}))return this._util.showMessage("Approvers not determined for one or more expense item ! ","close");if(0==this.isManualApproverSelectionApplicable&&(null!=o.findWhere(de,{workflowId:""})||null!=o.findWhere(de,{workflowId:null})))return this._util.showMessage("Workflow not found for one or more expense item ! ","close");if(null!=o.findWhere(de,{currency:""})||null!=o.findWhere(de,{currency:null}))return this._util.showMessage(`Please choose the ${(null===(re=null===(ae=this.fieldConfig)||void 0===ae?void 0:ae.currency)||void 0===re?void 0:re.field_label)?null===(ce=null===(se=this.fieldConfig)||void 0===se?void 0:se.currency)||void 0===ce?void 0:ce.field_label.replace(/\*$/,"").trim():"currency"} !`,"close");if(null==o.findWhere(de,{contextId:""})&&null==o.findWhere(de,{contextId:null}))return this._util.showMessage("Please fill all mandatory fields","close");if("Attachments Mandatory"==this.attachment_needed_or_not)return this._util.showMessage("Please attach Bills !","close")}},this.convertTheDraftIntoExpense=e=>{this.isBeingClaimed=!0,this.draftToClaimSubscription=this._expHomeService.newdraftToSubmission(e,this.draftInfo.expHeaderId,this.approversList).subscribe(e=>Object(l.c)(this,void 0,void 0,(function*(){"S"==e.messType?(this._util.showMessage("Claim Moved From Draft To Submission","close"),this.dialogRef.close("refresh")):"E"==e.messType&&(this._snackBar.open(e.err?e.err.messText:e.messText,"close"),this.isBeingClaimed=!1)})),e=>{console.error(e),this._util.showMessage("Oops! something went wrong.","close"),this.isBeingClaimed=!1})},this.createAsNewExpense=e=>{this._util.openConfirmationSweetAlertWithCustom("Create New Claim ?","").then(t=>{1==t&&(this.isBeingClaimed=!0,this.createNewClaimSubscription&&this.createNewClaimSubscription.unsubscribe(),this.createNewClaimSubscription=this._expHomeService.createOneOrMoreExpenses(e,this.approversList).subscribe(e=>Object(l.c)(this,void 0,void 0,(function*(){"S"==e.messType?(this._toaster.showSuccess("Success","Claim request created successfully",7e3),this.isBeingClaimed=!1,this.dialogRef.close("refresh")):"E"==e.messType&&(e.err?(this._snackBar.open(e.err.messText,"close"),this.isBeingClaimed=!1):(this._snackBar.open(e.messText,"close"),this.isBeingClaimed=!1))})),e=>{console.error(e),this._util.showMessage("Oops! something went wrong.","close"),this.isBeingClaimed=!1}))})},this.saveAsDraft=()=>Object(l.c)(this,void 0,void 0,(function*(){var e,t,n;this.isBeingCheckedForDuplicate=!0;let i=yield this._expHomeService.getExpenseDuplicateValidation(this.claimForm.value,null===(e=this.draftInfo)||void 0===e?void 0:e.expHeaderId);if(i&&"E"==i.messType)return this._util.showMessage(i.data.toString(","),"close");let a=!1;for(let l=0;l<this.claimForm.value.claims.length;l++){let e=o.filter(i.data,{index:l});if((null==e?void 0:e.length)>0){a=!0;let t=o.pluck(e,"expense_id");this.claimForm.controls.claims.controls[l].controls.hasDuplicateError.patchValue(!0),this.claimForm.controls.claims.controls[l].controls.duplicationError.patchValue("Bill with entered details already exist on "+t.toString(","))}else this.claimForm.controls.claims.controls[l].controls.hasDuplicateError.patchValue(null),this.claimForm.controls.claims.controls[l].controls.duplicationError.patchValue(null)}if(1==a)return this.isBeingCheckedForDuplicate=!1,void this._toaster.showWarning("Warning \u26a0\ufe0f",`Duplicate Receipt exist. Please create claim with unique\n         ${null===(t=this.fieldConfig.invoiceDate)||void 0===t?void 0:t.field_label.replace(/\*$/,"").trim()}, \n         ${null===(n=this.fieldConfig.amount)||void 0===n?void 0:n.field_label.replace(/\*$/,"").trim()} !`,7e3);if(1==this.isManualApproverSelectionApplicable&&1==this.isHeaderLevelApprover&&this.claimForm.get("claims").controls.forEach(e=>{e.patchValue({workflowId:this.workflowId,approvers:this.claimForm.value.headerLevelApprover,isAggregationAllowed:this.aggregationAllowed,approverMenu:this.claimForm.value.headerLevelApproverList,amount:e.controls.amount.value?"string"==typeof e.controls.amount.value?parseFloat(e.controls.amount.value.replace(/,/g,"")):parseFloat(e.controls.amount.value):null,finalamount:e.controls.finalamount.value?"string"==typeof e.controls.finalamount.value?parseFloat(e.controls.finalamount.value.replace(/,/g,"")):parseFloat(e.controls.finalamount.value):0,conversionRate:e.controls.conversionRate.value?"string"==typeof e.controls.conversionRate.value?parseFloat(e.controls.conversionRate.value.replace(/,/g,"")):parseFloat(e.controls.conversionRate.value):0,taxAmount:e.controls.taxAmount.value?"string"==typeof e.controls.taxAmount.value?parseFloat(e.controls.taxAmount.value.replace(/,/g,"")):parseFloat(e.controls.taxAmount.value):0,settlement:e.controls.settlement.value?"string"==typeof e.controls.settlement.value?parseFloat(e.controls.settlement.value.replace(/,/g,"")):parseFloat(e.controls.settlement.value):0})}),console.log("this.claimForm.valid"),console.log(this.claimForm.valid),console.log(this.claimForm),this.claimForm.valid){this.isBeingCheckedForDuplicate=!1;let e=this.claimForm.value;e.claimDate=this.claimForm.value.claimDate,e.isExpenseCreatedFromWeb=1;let t=this.draftInfo?this.draftInfo.expHeaderId:0;this.isBeingDrafted=!0,this.saveDraftSubscription&&this.saveDraftSubscription.unsubscribe(),this.deletedContextId.length>0&&this._expHomeService.deleteContextIds(this.deletedContextId),this.saveDraftSubscription=this._expHomeService.saveClaimAsDraft(e,t,this.approversList).subscribe(e=>Object(l.c)(this,void 0,void 0,(function*(){"S"==e.messType?(this.isBeingDrafted=!1,this._toaster.showSuccess("Success","Claim saved as Draft",7e3),this.dialogRef.close("refresh")):"E"==e.messType&&(e.err?(this._snackBar.open(e.err.messText,"close"),this.isBeingClaimed=!1):(this._snackBar.open(e.messText,"close"),this.isBeingClaimed=!1))})))}else{if(!this.claimForm.valid)return this.isBeingCheckedForDuplicate=!1,this.markFormGroupTouched(this.claimForm),void this._toaster.showWarning("Warning \u26a0\ufe0f","Kindly enter all mandatory fields!",1e4);this.checkFormValidators()}})),this.resetForm=()=>{this.onDraftOpenLegalEntity=!1,this.onDraftOpen=!1;let e=this._auth.getProfile().profile;this.approvers=null,this.claimType.forEach(e=>{e.active=!1}),this.removeClaimsExceptFirst(),this.claimForm.reset(),this.claimForm.controls.claims.controls[0].controls.attachment.patchValue(""),this.claimForm.controls.claims.controls[0].controls.uploadInProgress.patchValue(!1),this.claimForm.get("claimedBy").patchValue(e.oid),this.claimForm.get("originalInitiatorOId").patchValue(e.oid),this.claimForm.get("claimDate").patchValue(c().format("DD-MMM-YYYY")),this.claimForm.get("legalEntity").patchValue(this.defaultLE),this.claimForm.get("expenseType").patchValue("C"),this.claimForm.get("expenseTag").patchValue(""),this.currentFileIndex=0,this.currentlyActiveFormType="singleExpenseCreation",this.onDraftOpenLegalEntity=!1,this.onDraftOpen=!1},this.removeClaimsExceptFirst=()=>{const e=this.claimFormData.claims;let t=e.length-1;for(;t>0;)e.removeAt(t),t--},this.onFileAdd=e=>{e&&(this.currentFileIndex=e),this.uploader.uploadAll()},this.openFile=e=>{let t=this.claimForm.controls.claims.controls[e].controls.attachment.value[0].key,i=this.claimForm.controls.claims.controls[e].controls.attachment.value[0].fileName,o=this.claimForm.controls.claims.controls[e].controls.attachment.value[0].type;this.claimForm.controls.claims.controls[e].controls.uploadInProgress.patchValue(!0),this._expHomeService.getFileDataFromS3(t).subscribe(t=>Object(l.c)(this,void 0,void 0,(function*(){if(this.claimForm.controls.claims.controls[e].controls.uploadInProgress.patchValue(!1),"S"==t.messType)if("image/png"==o||"image/jpg"==o||"image/jpeg"==o){let e="application/pdf"==o?"90vh":"auto",l="application/pdf"==o?"90vw":"auto",a="application/pdf"==o;const{AttachmentViewerComponent:r}=yield n.e(742).then(n.bind(null,"0LyG"));this.dialog.open(r,{width:l,height:e,maxWidth:"100vh",maxHeight:"100vh",disableClose:!1,data:{fileData:"data:"+o+";base64,"+t.data.fileData,fileName:i,isFilePDF:a}})}else"application/pdf"==o?window.open("").document.write("<iframe width='100%' height='100%' src='data:application/pdf;base64, "+t.data.fileData+"'></iframe>"):this._fileSaver.saveAsFile(t.data.fileData,i,o);else this._util.showMessage(t.messText,"Dismiss",3e3)})),t=>{this.claimForm.controls.claims.controls[e].controls.uploadInProgress.patchValue(!1),this._util.showMessage("Error in Downloading !","Dismiss",3e3)})},this.changeClaimType=(e,t)=>{this.patchNewlySelectedClaimType(e,t),0==this.isManualApproverSelectionApplicable&&this.determineWorkflow();let n=this.claimForm.value.claims[t].category;o.each(this.claimType,e=>{e.id==n&&(e.count=e.count?e.count+1:1)}),this.updateCategoryValidators(t)},this.refreshButtons=()=>{this.claimForm.controls.claims.valueChanges.subscribe(e=>{setTimeout(()=>{let e=this.claimForm.value.claims.map(e=>e.category),t=o.countBy(e);this.claimType.forEach(n=>{n.active=e.includes(n.id),n.count=t[n.id]||0})},600)})},this.patchNewlySelectedClaimType=(e,t)=>{let n=e;this.claimForm.controls.claims.controls[t].controls.categoryCode.patchValue(n.code),this.claimForm.controls.claims.controls[t].controls.categoryName.patchValue(n.name),this.claimForm.controls.claims.controls[t].controls.claimTypeId.patchValue(n.claim_type_id)},this.patchDraftValue=e=>{console.log(e),this.onDraftOpenLegalEntity=!1,this.onDraftOpen=!1;let t=this.getExpenseApproverBasedOnRoleFunc();this.claimForm.patchValue({expenseType:e.expenseType,isAggregationAllowed:e.isAggregationAllowed,workFlowId:e.workFlowId,claimedBy:e.claimedBy,originalInitiatorOId:e.originalInitiatorOId,legalEntity:JSON.stringify(e.legalEntity),expenseTag:[e.expenseTag,a.H.required],department:e.department,costCenter:JSON.stringify(e.costCenter),claimDate:c().format("DD-MMM-YYYY"),costCenterCode:e.costCenter.id,legalEntityCode:e.legalEntity.entity_id,customerBilling:e.customerBilling?e.customerBilling:3,headerLevelApproverList:t,headerLevelApprover:this.approversList,legalEntityCurrency:e.legalEntityCurrency?e.legalEntityCurrency:null,headerDescription:e.headerDescription,spentFrom:e.spentFrom,corporateCard:e.corporateCard}),this.onDraftOpenLegalEntity=!1,this.onDraftOpen=!1;const n=this.claimFormData.claims;let l=n.length-1;for(;0==l;)n.removeAt(l),l--;this.patchClaimDraftArray(e,n),this.defaultCustomerBillingConfig()},this.onDraftOpenCostCenter=!0,this.spinnerService.show(),this.draftInfo=h,this.uploader=new s.d({url:"/api/exPrimary2/uploadExpenseAttachment",authToken:"Bearer "+this._auth.getToken(),disableMultipart:!1,allowedMimeType:this.allowedMimeType,maxFileSize:this.maxFileSize}),this.getManualApproversConfigFunc(),this.createForm(),h||this.detectValueChanges(),this.uploadFileAttachment(),this.getExpenseUiMasterConfig(),this.draftInfo&&(this.patchDraftValue(this.draftInfo),this.getExpenseApproverBasedOnRoleFunc(),this.claimForm.controls.expenseTag.patchValue(this.draftInfo.expenseTag),this.getFormFieldConfig(),this.getClaimCategoryMasterByLegalEntity(),this.setDefaultLE(this.draftInfo.claimedBy))}getExpenseTag(){return Object(l.c)(this,void 0,void 0,(function*(){this.draftInfo?yield this._expHomeService.getExpenseTag(this.draftInfo.legalEntityCode).then(e=>{e&&e.data.length>0&&(this.expenseTagList=e.data)}):yield this._expHomeService.getExpenseTag(this.claimForm.value.legalEntityCode).then(e=>{e&&e.data.length>0&&(this.expenseTagList=e.data)})}))}getManualApproversConfigFunc(){return Object(l.c)(this,void 0,void 0,(function*(){yield this.getManualApproverExpenseConfig()}))}ngOnInit(){return Object(l.c)(this,void 0,void 0,(function*(){if(this.getExpenseGeneralConfig(),this.expWorkflowProperties=yield this.getExpenseClaimWorkflows(),this.getBillingConfig(),this.getCorporateCardApplicable(),this.getSpentFromList(),this.getvehicleTypeList(),this.getUnitList(),this.getFormFieldConfig(),this.getSpentFromList(),this.currencyconversionrate(),this.gettaxvalue(),yield this._expHomeService.getAllCostCenter(this.claimForm.value.claimedBy).then(e=>{this.costCenterList=e}),yield this._expHomeService.getAllLegalEntity().then(e=>{e.data.length>0&&(this.legalEntityList=e.data)}),yield this._expHomeService.getTaxRateType().then(e=>{e&&e.data.length>0&&(this.getTaxRateType=e.data)}),this.draftInfo||(yield this.getEntityCurrency()),this.draftInfo&&(this.claimForm.value.legalEntityCode&&this._expHomeService.gettaxvalue(this.claimForm.value.legalEntityCode).subscribe(e=>{"S"==e.messType&&(this.defaulttaxPercentage=[this.defaulttaxPercentage[0],...e.data])}),this.patchDraftValues(),this.claimForm.value.spentFrom)&&(this.changeSpentFrom(this.claimForm.value.spentFrom),this.claimForm.get("claims").controls.forEach((e,t)=>{this.updateCategoryValidators(t)})),this.spinnerService.hide(),this.getCurrencyMasterData(),this.draftInfo){let e=this.claimForm.value.claims.map(e=>e.category),t=o.countBy(e);this.claimType.forEach(n=>{n.active=e.includes(n.id),n.count=t[n.id]||0})}}))}getExpenseGeneralConfig(){return Object(l.c)(this,void 0,void 0,(function*(){let e=yield this._expHomeService.getExpenseGeneralConfig();e&&"S"==e.messType&&(this.theme_color1=e.data.theme_color_1&&e.data.theme_color_1.config_value?e.data.theme_color_1.config_value:this.theme_color1,this.theme_color2=e.data.theme_color_2&&e.data.theme_color_2.config_value?e.data.theme_color_2.config_value:this.theme_color2,this.formBorderColor=this.theme_color1,document.documentElement.style.setProperty("--formBorderColor",this.formBorderColor))}))}gettaxvalue(){return Object(l.c)(this,void 0,void 0,(function*(){this.claimForm.value.legalEntityCode&&this._expHomeService.gettaxvalue(this.claimForm.value.legalEntityCode).subscribe(e=>{"S"==e.messType&&(this.defaulttaxPercentage=[this.defaulttaxPercentage[0],...e.data])},e=>{console.error(e)})}))}getCurrencyMasterData(){return Object(l.c)(this,void 0,void 0,(function*(){this._expHomeService.getCurrencyMasterData().then(e=>{e.data.length>0&&"S"==e.messType&&(this.currencyList=e.data)},e=>{console.error(e)})}))}onConversionRateInput(e,t){let n=this.claimForm.value.claims[t].conversionRate,l=this.claimForm.value.claims[t].finalamount;if(n&&l){let e=(n?"string"==typeof n?parseFloat(n.replace(/,/g,"")):parseFloat(n):0)*(l?"string"==typeof l?parseFloat(l.replace(/,/g,"")):parseFloat(l):0);e&&(e=parseFloat(e.toFixed(2))),this.onSettlementAmountChange(t,e)}}currencyconversionrate(){return Object(l.c)(this,void 0,void 0,(function*(){let e=this.claimForm.get("claims").value,t=this.claimForm.get("legalEntity").value;yield this._expHomeService.currencyconversionrate(e,t).subscribe(e=>{if("S"==e.messType){this.conversionRateData=e.data;for(let e=0;e<this.conversionRateData.length;e++){this.conversionRate=this.conversionRateData[e].conversionRate,this.currencyFrom=this.conversionRateData[e].currencyFrom,this.currencyTo=this.conversionRateData[e].currencyTo;let t=this.claimForm.value.claims[e].finalamount;t=t?"string"==typeof t?parseFloat(t.replace(/,/g,"")):parseFloat(t):0;let n=t&&this.conversionRate?t*this.conversionRate:0,l="0.00";n&&(n=parseFloat(n.toFixed(2)),l=this.fixNumberByDecimal(n,this.claimForm.value.claims[e].currenyCodeValue)),this.claimForm.controls.claims.controls[e].controls.settlement.patchValue(l),this.claimForm.controls.claims.controls[e].controls.conversionRate.patchValue(this.conversionRate),this.claimForm.controls.claims.controls[e].controls.currencyFrom.patchValue(this.currencyFrom),this.claimForm.controls.claims.controls[e].controls.currencyTo.patchValue(this.currencyTo)}}},e=>{console.error(e)})}))}getExpenseUiMasterConfig(){return Object(l.c)(this,void 0,void 0,(function*(){this._expHomeService.getExpenseUiMasterConfig(2).subscribe(e=>{"S"==e.messType&&(this.primaryColor=e.data[0].primary_colour,document.documentElement.style.setProperty("--expensefontFamily",e.data[0].font_family),document.documentElement.style.setProperty("--expenseprimaryColor",e.data[0].primary_colour),document.documentElement.style.setProperty("--expensesecondaryColor",e.data[0].secondary_colour),document.documentElement.style.setProperty("--expenseecondaryColor1",e.data[0].secondary_colour_1),document.documentElement.style.setProperty("--expensesecondaryColor2",e.data[0].secondary_colour_2),document.documentElement.style.setProperty("--expensesecondaryColor3",e.data[0].secondary_colour_3),document.documentElement.style.setProperty("--expensesecondaryColor4",e.data[0].secondary_colour_4))})}))}amountcalculation(){return Object(l.c)(this,void 0,void 0,(function*(){this.claimForm.get("legalEntity");let e,t,n=this.claimForm.value;for(let l=0;l<n.claims.length;l++){let i=n.claims[l],o=i.amount;const a=this.claimForm.get(`claims.${l}.taxPercentage`);let r=this.claimForm.get(`claims.${l}.taxRate`).value,s=a.value;if(isNaN(i.taxRate)||0===i.taxRate){i.taxAmount=0,i.totalAmount=o,o=o?"string"==typeof o?parseFloat(o.replace(/,/g,"")):parseFloat(o):null,this.onTaxAmountChange(l,0),this.onFinalAmountChange(l,o);let e=this.claimForm.value.claims[l].conversionRate;e=e?"string"==typeof e?parseFloat(e.replace(/,/g,"")):parseFloat(e):0;let t=o&&e?o*e:0;t&&(t=parseFloat(t.toFixed(2))),this.onSettlementAmountChange(l,t)}else if(1e3!=s){let n=i.taxRatetype;o=o?"string"==typeof o?parseFloat(o.replace(/,/g,"")):parseFloat(o):null,r=r?"string"==typeof r?parseFloat(r.replace(/,/g,"")):parseFloat(r):0,2==n?(e=o*(r/100),t=o+e):(e=o*(r/(100+r)),t=o),i.taxAmount=e,i.totalAmount=t;let a=this.claimForm.value.claims[l].conversionRate;a=a?"string"==typeof a?parseFloat(a.replace(/,/g,"")):parseFloat(a):0,this.onTaxAmountChange(l,e),this.onFinalAmountChange(l,t);let s=t&&a?t*a:0;s&&(s=parseFloat(s.toFixed(2))),this.onSettlementAmountChange(l,s)}else if(1e3==s){this.onTaxAmountFieldValueChange(l),i.totalAmount=o,o=o?"string"==typeof o?parseFloat(o.replace(/,/g,"")):parseFloat(o):null,this.onFinalAmountChange(l,o);let e=this.claimForm.value.claims[l].conversionRate;e=e?"string"==typeof e?parseFloat(e.replace(/,/g,"")):parseFloat(e):0;let t=o&&e?o*e:0;t&&(t=parseFloat(t.toFixed(2))),this.onSettlementAmountChange(l,t)}}}))}changeLegalEntity(e){this.draftInfo&&1==this.onDraftOpenLegalEntityData?this.onDraftOpenLegalEntityData=!1:(this.claimForm.controls.legalEntity.patchValue(e),this.getExpenseTag(),this.gettaxvalue(),this.amountcalculation())}onExpenseTagChange(e){this.claimForm.controls.expenseTag.patchValue(e)}getBillingConfig(){return Object(l.c)(this,void 0,void 0,(function*(){yield this.getCustomerBillingConfig();let e=yield this.getExpenseConfig();e&&(this.isDefaultCostCenter=JSON.parse(e[0].expense_application_config).is_default_cost_center,this.isToShowConversionDetails=JSON.parse(e[0].expense_application_config).is_to_show_conversion_details,this.isToShowConversionDetails=!!this.isToShowConversionDetails&&this.isToShowConversionDetails,this.workflowId=this.parseJSON(e[0].expense_application_config).workflow_id?this.parseJSON(e[0].expense_application_config).workflow_id:null,this.aggregationAllowed=this.parseJSON(e[0].expense_application_config).aggregation_allowed||0==this.parseJSON(e[0].expense_application_config).aggregation_allowed?this.parseJSON(e[0].expense_application_config).aggregation_allowed:null,this.isDefaultCostCenter&&this.claimForm.value.claimedBy)&&(yield this.getDepartmentCostCenter(this.claimForm.value.claimedBy))}))}getDepartmentCostCenter(e){return Object(l.c)(this,void 0,void 0,(function*(){this.draftInfo||this._expHomeService.getDepartmentCostCenter(e).subscribe(e=>{if("S"==e.messType){let t=e.data[0].cost_center;this.claimForm.controls.costCenterCode.patchValue(t);let n=o.filter(this.costCenterList,e=>{if(e.id==t)return e});n.length>0&&this.claimForm.controls.costCenter.patchValue(n[0])}},e=>{console.error(e)})}))}getCustomerBillingConfig(){return Object(l.c)(this,void 0,void 0,(function*(){this._expHomeService.getExpenseBillingConfig().subscribe(e=>{this.billingConfig=e.data},e=>{console.error(e)})}))}defaultCustomerBillingConfig(){var e;return Object(l.c)(this,void 0,void 0,(function*(){this.costCenterType=null===(e=this.parseJSON(this.claimForm.value.costCenter))||void 0===e?void 0:e.cost_centre_type}))}getFormFieldConfig(){return Object(l.c)(this,void 0,void 0,(function*(){yield this._expHomeService.getFormFieldConfig(this.claimForm.value.legalEntityCode).subscribe(e=>{this.formFieldConfig=e.data,this.spinnerService.hide(),this.formFieldConfig&&(this.fieldConfig=null,this.fieldConfig={},this.formFieldConfig.forEach(e=>{this.fieldConfig[e.field_key]=e}),this.claimForm.get("claims")),this.showClaimCreation=!0},e=>{this.showClaimCreation=!0,this.spinnerService.hide(),console.error(e)})}))}getCorporateCardApplicable(){return Object(l.c)(this,void 0,void 0,(function*(){yield this._expHomeService.getCorporateCardApplicable(this.claimForm.value.claimedBy).then(e=>{var t;this.isCorporateCardApplicable=!!(e&&(null===(t=e.data)||void 0===t?void 0:t.length)>0),this.updateSpentFromValidators()})}))}updateSpentFromValidators(){const e=this.claimForm.get("spentFrom");this.isCorporateCardApplicable?null==e||e.setValidators([a.H.required]):null==e||e.clearValidators(),null==e||e.updateValueAndValidity()}closeMessage(){this.isDialogOpen=!1}getCostCenterOfClaimer(){return Object(l.c)(this,void 0,void 0,(function*(){yield this.getDepartmentCostCenter(this.claimedBy),yield this.setDefaultLE(this.claimedBy),1==this.isManualApproverSelectionApplicable?this.getExpenseApproverBasedOnRoleFunc():this.determineWorkflow()}))}getAllCostCenter(){return Object(l.c)(this,void 0,void 0,(function*(){this.draftInfo||this.claimForm.controls.costCenter.patchValue(null),yield this._expHomeService.getAllCostCenter(this.claimForm.value.claimedBy).then(e=>{this.costCenterList=e})}))}parseJSON(e){try{return JSON.parse(e)}catch(t){return e}}changeInClaimedBy(){var e,t;return Object(l.c)(this,void 0,void 0,(function*(){this.claimForm.controls.costCenterCode.patchValue(null),yield this.setDefaultLE(this.claimForm.value.claimedBy),this.getFormFieldConfig(),this.getSpentFromList(),this.getCorporateCardApplicable(),this.claimForm.controls.costCenter.patchValue(null),this.claimForm.controls.customerBilling.patchValue(3),this.claimForm.controls.headerLevelApproverList.patchValue(null),this.claimForm.controls.headerLevelApprover.patchValue(null),this.defaultCustomerBillingConfig(),this.getAllCostCenter();let n=this.costCenter?"string"==typeof this.costCenter?(null===(e=this.parseJSON(this.costCenter))||void 0===e?void 0:e.cost_centre)?null===(t=this.parseJSON(this.costCenter))||void 0===t?void 0:t.cost_centre:this.parseJSON(this.costCenter):this.costCenter.cost_centre:"";return yield this.getDepartmentMaster(n),1==this.isManualApproverSelectionApplicable?this.getExpenseApproverBasedOnRoleFunc():this.determineWorkflow(),Promise.resolve(!0)}))}onClaimedByChange(e){return Object(l.c)(this,void 0,void 0,(function*(){this.approversList=[],this.claimedBy=this.claimForm.value.claimedBy,this.isDefaultCostCenter?this.draftInfo||this.getCostCenterOfClaimer():(this.claimForm.controls.costCenter.patchValue(null),this.claimForm.controls.costCenterCode.patchValue(null),this.claimForm.controls.expenseTag.patchValue(null),setTimeout(()=>{this.changeInClaimedBy()},1e3))}))}getExpenseApproverBasedOnRoleFunc(){return Object(l.c)(this,void 0,void 0,(function*(){yield this.getExpenseApproverBasedOnRole()}))}onManualApproverSelection(e,t){let n=[];n.push(e),this.claimForm.controls.claims.controls[t].controls.workflowId.patchValue(this.workflowId),this.claimForm.controls.claims.controls[t].controls.approvers.patchValue(n),this.claimForm.controls.claims.controls[t].controls.approverMenu.patchValue(e.oid),this.claimForm.controls.claims.controls[t].controls.isAggregationAllowed.patchValue(this.aggregationAllowed)}onManualHeaderApproverSelection(e){return Object(l.c)(this,void 0,void 0,(function*(){this.restrictClaimCreation=!1;let t=null==e?void 0:e.value,n=[];if(e.source.value){let e=o.filter(this.approversList,{oid:t});n.push(e[0])}this._expHomeService.checkForDelegatedApprovers(this.claimForm.value.claimDate,n,this.claimForm.value.claimedBy).subscribe(e=>{let l=e.data,i=!!e.isDelegationPresent&&e.isDelegationPresent,o=!!e.isDelegateEmployeeRetired&&e.isDelegateEmployeeRetired;return e.IsClaimerSameAsApprover&&e.IsClaimerSameAsApprover?(this.restrictClaimCreation=!0,void this._toaster.showWarning("Warning \u26a0\ufe0f","Claimer same as Approver!",7e3)):o?(this.restrictClaimCreation=!0,void this._toaster.showWarning("Warning \u26a0\ufe0f","The delegated employee is no longer active. Claim creation is restricted.",7e3)):void(i?(this.claimForm.controls.headerLevelApproverList.patchValue(t),this.claimForm.controls.headerLevelApprover.patchValue(l),this.claimForm.get("claims").controls.forEach(e=>{e.patchValue({workflowId:this.workflowId,approvers:l.data,isAggregationAllowed:this.aggregationAllowed,approverMenu:l.data})})):(this.claimForm.controls.headerLevelApproverList.patchValue(t),this.claimForm.controls.headerLevelApprover.patchValue(n),this.claimForm.get("claims").controls.forEach(e=>{e.patchValue({workflowId:this.workflowId,approvers:n,isAggregationAllowed:this.aggregationAllowed,approverMenu:n})})))},e=>{console.log(e)})}))}ontaxRatetypeChange(e){this.ontaxPercentageChange(e),this.amountcalculation()}onInvoiceDateChange(e,t){this.claimForm.controls.claims.controls[t].controls.conversionRate.patchValue(1),this.amountcalculation(),this.currencyconversionrate()}hasValidator(e,t){if(!e||!e.validator)return!1;const n=e.validator({});return n&&n.hasOwnProperty("required")}invoiceDateValidator(){return e=>{const t=c(e.value,"YYYY-MM-DD").format("YYYY-MM-DD"),n=c().startOf("day").format("YYYY-MM-DD");return c(t).isBefore(this.billingStartDate)||c(t).isAfter(n)?{invalidInvoiceDate:!0}:null}}futureDateValidator(){return e=>{const t=c(e.value,"YYYY-MM-DD").format("YYYY-MM-DD"),n=c().startOf("day").format("YYYY-MM-DD");return c(t).isAfter(n)?{invalidInvoiceDate:!0}:null}}setDefaultLE(e){return Object(l.c)(this,void 0,void 0,(function*(){return this.defaultLE=yield this._expHomeService.getDefaultLE(e),this.defaultLE&&(this.claimForm.patchValue({legalEntity:this.defaultLE,legalEntityCode:JSON.parse(this.defaultLE).entity_id}),this.isExpenseCreationAllowed=JSON.parse(this.defaultLE).is_expense_creation_allowed,this.getClaimCategoryMasterByLegalEntity(),this.draftInfo||this.getEntityCurrency(),this.gettaxvalue(),this.getExpenseTag(),this.defaultLegalEntityBasedOnClaimer=!0),this.defaultLE}))}getEntityCurrency(){return Object(l.c)(this,void 0,void 0,(function*(){this.claimForm.value.legalEntityCode&&this._expHomeService.getEntityCurrency(this.claimForm.value.legalEntityCode).subscribe(e=>{var t;"S"==e.messType&&(this.defaultCurrency=e.data,this.defaultCurrency&&(this.legalEntityCurrency=this.parseJSON(this.defaultCurrency).currency_code,this.claimForm.controls.legalEntityCurrency.patchValue(this.legalEntityCurrency),this.legalEntityCurrenyId=null===(t=this.parseJSON(this.defaultCurrency))||void 0===t?void 0:t.id),this.claimForm.get("claims").controls.forEach(e=>{e.patchValue({currency:this.defaultCurrency,currencyCode:this.parseJSON(this.defaultCurrency).id,currenyCodeValue:this.parseJSON(this.defaultCurrency).currency_code})}),this.currencyconversionrate())},e=>{console.error(e)})}))}validateTimeFormat(e){return!e.value||/^([01]\d|2[0-3]):([0-5]\d)$/.test(e.value)?null:{invalidTimeFormat:!0}}onKeyDownPreventInvalidChars(e){["0","1","2","3","4","5","6","7","8","9","Backspace","Delete","ArrowLeft","ArrowRight","ArrowUp","ArrowDown"].includes(e.key)||e.preventDefault()}preventPaste(e){e.preventDefault()}onInputChangeFormatTime(e,t){let{key:n}=e;if(["Backspace","Delete","ArrowRight","ArrowLeft"].includes(n))return;let l=this.claimForm.get("claims");if(!l||!l.controls[t])return;let i=l.at(t).get("perDiemStartTime");if(!i)return;let o=i.value||"";if(o.includes(":")&&5!==o.length)return;2!==o.length||o.includes(":")||(o+=":",i.setValue(o));let a=this.timeFormat.transform(o);i.setValue(a)}onInputChangeFormatEndTime(e,t){let{key:n}=e;if(["Backspace","Delete","ArrowRight","ArrowLeft"].includes(n))return;let l=this.claimForm.get("claims");if(!l||!l.controls[t])return;let i=l.at(t).get("perDiemEndTime");if(!i)return;let o=i.value||"";if(o.includes(":")&&5!==o.length)return;2!==o.length||o.includes(":")||(o+=":",i.setValue(o));let a=this.timeFormat.transform(o);i.setValue(a)}get claimFormData(){return this.claimForm.controls}getAttahmentAlertMessage(e){let t=o.filter(this.claimType,{id:e});return t.length>0?1==this.claimForm.value.customerBilling||2==this.claimForm.value.customerBilling?t[0].billable_attachment_warning_msg?"Attach "+t[0].billable_attachment_warning_msg:"Attach Receipts":t[0].non_billable_attachment_warning_msg?"Attach "+t[0].non_billable_attachment_warning_msg:"Attach Receipts":"Attach Receipts"}markFormGroupTouched(e){Object.values(e.controls).forEach(e=>{e instanceof a.m?this.markFormGroupTouched(e):(e.markAsTouched(),e.markAllAsTouched(),e.markAsDirty())})}logInvalidFields(e){Object.keys(e.controls).forEach(t=>{const n=e.get(t);n instanceof a.m?this.logInvalidFields(n):n instanceof a.g?n.controls.forEach((e,n)=>{console.log(`FormArray '${t}' - Index ${n}`),this.logInvalidFields(e)}):(null==n?void 0:n.invalid)&&console.log(`Field '${t}' is invalid. Errors:`,n.errors)})}ngOnChanges(){this.draftInfo&&(this.resetForm(),this.patchDraftValue(this.draftInfo))}changeCostCenter(e){var t,n;return Object(l.c)(this,void 0,void 0,(function*(){this.claimForm.controls.costCenter.patchValue(e),this.claimForm.controls.customerBilling.patchValue(3),this.claimForm.controls.headerLevelApproverList.patchValue(null),this.claimForm.controls.headerLevelApprover.patchValue(null),this.defaultCustomerBillingConfig();let l=this.costCenter?"string"==typeof this.costCenter?(null===(t=this.parseJSON(this.costCenter))||void 0===t?void 0:t.cost_centre)?null===(n=this.parseJSON(this.costCenter))||void 0===n?void 0:n.cost_centre:this.parseJSON(this.costCenter):this.costCenter.cost_centre:"";yield this.getDepartmentMaster(l),1==this.isManualApproverSelectionApplicable?this.getExpenseApproverBasedOnRoleFunc():this.determineWorkflow()}))}fetchAndPatchVehicleEngineType(e,t){var n;return Object(l.c)(this,void 0,void 0,(function*(){yield this._expHomeService.getVehicleEngineType(e).then(e=>{var t;this.vehicleEngineTypeList=e&&(null===(t=e.data)||void 0===t?void 0:t.length)>0?e.data:[]});let l=this.claimForm.get("claims").at(t).get("vehicleEngineType");(null===(n=this.vehicleEngineTypeList)||void 0===n?void 0:n.length)>0?null==l||l.setValidators([a.H.required]):null==l||l.clearValidators(),null==l||l.updateValueAndValidity()}))}changeVehicleType(e,t){return Object(l.c)(this,void 0,void 0,(function*(){yield this.fetchAndPatchVehicleEngineType(e.id,t)}))}patchDraftValues(){return Object(l.c)(this,void 0,void 0,(function*(){this.claimForm.get("claims").controls.forEach((e,t)=>Object(l.c)(this,void 0,void 0,(function*(){var n;let l=null===(n=e.get("vehicleType"))||void 0===n?void 0:n.value;l&&(yield this.fetchAndPatchVehicleEngineType(l,t))})))}))}changeSpentFrom(e){var t,n,i,o;return Object(l.c)(this,void 0,void 0,(function*(){1==this.claimForm.value.spentFrom&&this.claimForm.patchValue({corporateCard:""}),yield this._expHomeService.getCorporateCards(this.claimForm.value.spentFrom,this.claimForm.value.legalEntityCode).then(e=>{var t;this.corporateCardList=e&&(null===(t=e.data)||void 0===t?void 0:t.length)>0?e.data:[]}),(null===(t=this.corporateCardList)||void 0===t?void 0:t.length)>0?null===(n=this.claimForm.get("corporateCard"))||void 0===n||n.setValidators([a.H.required]):null===(i=this.claimForm.get("corporateCard"))||void 0===i||i.clearValidators(),null===(o=this.claimForm.get("corporateCard"))||void 0===o||o.updateValueAndValidity()}))}getSpentFromList(){return Object(l.c)(this,void 0,void 0,(function*(){yield this._expHomeService.getSpentFromList(this.claimForm.value.legalEntityCode).then(e=>{var t;this.SpentFromList=e&&(null===(t=e.data)||void 0===t?void 0:t.length)>0?e.data:[]})}))}getvehicleTypeList(){return Object(l.c)(this,void 0,void 0,(function*(){yield this._expHomeService.getvehicleTypeList().then(e=>{var t;this.vehicleTypeList=e&&(null===(t=e.data)||void 0===t?void 0:t.length)>0?e.data:[]})}))}getUnitList(){return Object(l.c)(this,void 0,void 0,(function*(){yield this._expHomeService.getUnitList().then(e=>{var t;this.unitList=e&&(null===(t=e.data)||void 0===t?void 0:t.length)>0?e.data:[]})}))}patchClaimDraftArray(e,t){e.claims.forEach((e,n)=>{t.push(this.fb.group({categoryCode:[e.categoryCode,a.H.required],category:[e.category,a.H.required],categoryName:[e.categoryName,a.H.required],claimTypeId:[e.claimTypeId,a.H.required],amount:[this.fixNumberByDecimal(e.amount,e.currenyCodeValue),[a.H.required,Hl]],currency:[JSON.stringify(e.currency),a.H.required],currencyCode:[e.currencyCode,a.H.required],currenyCodeValue:[e.currenyCodeValue?e.currenyCodeValue:""],invoiceDate:[c.utc(e.invoiceDate).format("YYYY-MM-DD"),a.H.required],description:[e.description,a.H.required],attachment:[e.attachment],taxPercentage:[e.taxPercentage],taxType:[e.taxType],taxRate:[e.taxRate],taxAmount:[this.fixNumberByDecimal(e.taxAmount,e.currenyCodeValue),[Yl]],taxRatetype:[e.taxRatetype],finalamount:[this.fixNumberByDecimal(e.finalamount,e.currenyCodeValue)],conversionRate:[e.conversionRate,[a.H.required,Hl]],currencyFrom:[e.currencyFrom],currencyTo:[e.currencyTo],fromLocation:[e.fromLocation],toLocation:[e.toLocation],vehicleType:[e.vehicleType],vehicleEngineType:[e.vehicleEngineType],miles:[e.miles],unit:[e.unit],location:[e.location],perDiemstartDate:[e.perDiemstartDate],perDiemEndDate:[e.perDiemEndDate],perDiemStartTime:[e.perDiemStartTime],perDiemEndTime:[e.perDiemEndTime],noOfDays:[e.noOfDays],approvers:[e.approvers?e.approvers:"",a.H.required],workflowId:[e.workflowId?e.workflowId:""],isAggregationAllowed:[e.isAggregationAllowed?e.isAggregationAllowed:""],uploadInProgress:[!1],startDate:[e.startDate?c.utc(e.startDate).format("YYYY-MM-DD"):c().format("YYYY-MM-DD")],endDate:[e.endDate?c.utc(e.endDate).format("YYYY-MM-DD"):c().format("YYYY-MM-DD")],peopleInvolved:[e.peopleInvolved?e.peopleInvolved:""],contextId:[e.contextId],invoiceNo:[e.invoiceNo?e.invoiceNo:""],approverMenu:[e.approverMenu?e.approverMenu:null],settlement:[e.settlement?this.fixNumberByDecimal(e.settlement,e.currenyCodeValue):null],legalEntityCurrency:[e.legalEntityCurrency?e.legalEntityCurrency:null],hasDuplicateError:[!1],duplicationError:[""]})),n>0&&(this.currentlyActiveFormType="multipleExpenseCreation")})}ngOnDestroy(){this.saveDraftSubscription&&this.saveDraftSubscription.unsubscribe(),this.draftToClaimSubscription&&this.draftToClaimSubscription.unsubscribe(),this.createNewClaimSubscription&&this.createNewClaimSubscription.unsubscribe(),this.determineWorkflowSubscription&&this.determineWorkflowSubscription.unsubscribe()}getPeopleInvolved(e,t){this.selectedPeopleInvolvedList=e,this.selectedPeopleInvolvedId=[],this.claimForm.controls.claims.controls[t].controls.peopleInvolved.patchValue(this.selectedPeopleInvolvedList)}removeEmp(e,t,n){let l=[],i=this.claimForm.controls.claims.controls["singleExpenseCreation"==this.currentlyActiveFormType?0:n].controls.peopleInvolved.value;for(let o=0;o<i.length;o++)e.id!=i[o].id&&l.push(i[o]);this.claimForm.controls.claims.controls["singleExpenseCreation"==this.currentlyActiveFormType?0:n].controls.peopleInvolved.patchValue(l)}openExpensePeopleInvolved(e){return Object(l.c)(this,void 0,void 0,(function*(){this.dialog.open(u.a,{data:{peopleInvolvedData:e,primaryColor:this.primaryColor},height:"35%",width:"20%"})}))}changeInAttachment(e){return Object(l.c)(this,void 0,void 0,(function*(){o.contains(this.contextId,e)||this.contextId.push(e),this.claimForm.controls.claims.controls[0].controls.contextId.patchValue(e),null==e&&null!=this.draftInfo&&(yield this._expHomeService.updateExpenseAttachment(this.draftInfo.expHeaderId,0))}))}changeInFiles(e,t){return Object(l.c)(this,void 0,void 0,(function*(){o.contains(this.contextId,e)||this.contextId.push(e),this.claimForm.controls.claims.controls[t].controls.contextId.patchValue(e),null==e&&null!=this.draftInfo&&(yield this._expHomeService.updateExpenseAttachment(this.draftInfo.expHeaderId,t))}))}parseValue(e){if("string"==typeof e){let t=e.trim();return""===t?0:parseFloat(t.replace(/,/g,""))}return"number"==typeof e?e:0}fixNumberOnUI(e,t){if(0==e||"0"==e||""==e)return"0.00";let n=e;return"string"==typeof e&&(n=e?"string"==typeof e?parseFloat(e.replace(/,/g,"")):parseFloat(e):0),"INR"==t?new Intl.NumberFormat("en-IN",{minimumFractionDigits:2,maximumFractionDigits:2}).format(Number(n)):new Intl.NumberFormat("en-US",{minimumFractionDigits:2,maximumFractionDigits:2}).format(Number(n))}onPaymentMadeChange(e){if(1e3==this.claimForm.get(`claims.${e}.taxPercentage`).value){let t=this.claimForm.get(`claims.${e}.amount`).value,n=this.claimForm.get(`claims.${e}.taxAmount`).value;t=t?"string"==typeof t?parseFloat(t.replace(/,/g,"")):parseFloat(t):null,n=n?"string"==typeof n?parseFloat(n.replace(/,/g,"")):parseFloat(n):0,n>t&&this.claimForm.controls.claims.controls[e].controls.taxAmount.patchValue(null)}let t=this.claimForm.value.claims[e].amount;if(t=t&&"string"==typeof t?parseFloat(t.replace(/,/g,"")):t,!t||null==t||t<=0)this.claimForm.controls.claims.controls[e].controls.amount.patchValue(null);else{if(!/^\d*\.?\d*$/.test(t))return t="",void this.claimForm.controls.claims.controls[e].controls.amount.patchValue(null);let n=this.claimForm.value.claims[e].currenyCodeValue,l=this.fixNumberOnUI(this.parseValue(t),n);this.claimForm.controls.claims.controls[e].controls.amount.patchValue(l&&"0.00"==l?null:l)}}onConversionRateChange(e){let t=this.claimForm.value.claims[e].conversionRate;if(!t||null==t||t<=0)this.claimForm.controls.claims.controls[e].controls.conversionRate.patchValue(0);else{if(!/^\d*\.?\d*$/.test(t))return t="",void this.claimForm.controls.claims.controls[e].controls.conversionRate.patchValue(0);let n=this.claimForm.value.claims[e].currenyCodeValue,l=this.fixNumberOnUIWithDecimals(this.parseValue(t),n);this.claimForm.controls.claims.controls[e].controls.conversionRate.patchValue(l)}}fixNumberOnUIWithDecimals(e,t){if("string"==typeof e)return e;{if(0==e||"0"==e||""==e)return"0.00";let n=e?"string"==typeof e?parseFloat(e.replace(/,/g,"")):parseFloat(e):0,[l,i]=n.toString().split(".");return"INR"==t?new Intl.NumberFormat("en-IN").format(Number(l))+(i?"."+i:""):new Intl.NumberFormat("en-US").format(Number(l))+(i?"."+i:"")}}onTaxAmountFieldValueChange(e){return Object(l.c)(this,void 0,void 0,(function*(){if(this.draftInfo){yield this.updateTaxAmountValidator(e);let t=this.claimForm.value.claims[e].taxAmount?parseFloat(this.claimForm.value.claims[e].taxAmount.replace(/,/g,"")):0,n=this.claimForm.value.claims[e].taxAmount,l=this.claimForm.value.claims[e].amount?parseFloat(String(this.claimForm.value.claims[e].amount).replace(/,/g,"")):null;if(!t||null==t||t<=0||t>l)this.claimForm.controls.claims.controls[e].controls.taxAmount.patchValue(null);else{if(!/^\d*\.?\d*$/.test(n))return n="",void this.claimForm.controls.claims.controls[e].controls.taxAmount.patchValue(null);let t=this.claimForm.value.claims[e].currenyCodeValue,l=this.fixNumberOnUI(this.parseValue(n),t);if(this.claimForm.controls.claims.controls[e].controls.taxAmount.patchValue(l),1e3==this.claimForm.get(`claims.${e}.taxPercentage`).value){let t=this.claimForm.get(`claims.${e}.amount`).value,n=t?parseFloat(String(t).replace(/,/g,"")):null,l=this.claimForm.get(`claims.${e}.taxAmount`).value;if(n&&l){let t=null!=n?"string"==typeof n?parseFloat(n.replace(/,/g,"")):"number"==typeof n?n:null:null,i=l?"string"==typeof l?parseFloat(l.replace(/,/g,"")):parseFloat(l):null,o=this.fixNumberOnUI(0!==n?100*i/(t-i):0,this.claimForm.value.claims[e].currenyCodeValue);this.claimForm.controls.claims.controls[e].controls.taxRate.patchValue(o),this.claimForm.controls.claims.controls[e].controls.taxType.patchValue(`Others - ${o} %`)}}}}let t=this.claimForm.value.claims[e].taxAmount?parseFloat(this.claimForm.value.claims[e].taxAmount.replace(/,/g,"")):0,n=this.claimForm.value.claims[e].taxAmount,l=this.claimForm.value.claims[e].amount?parseFloat(String(this.claimForm.value.claims[e].amount).replace(/,/g,"")):null;if(!t||null==t||t<=0||t>l)this.claimForm.controls.claims.controls[e].controls.taxAmount.patchValue(null);else{if(!/^\d*\.?\d*$/.test(n))return n="",void this.claimForm.controls.claims.controls[e].controls.taxAmount.patchValue(null);let t=this.claimForm.value.claims[e].currenyCodeValue,l=this.fixNumberOnUI(this.parseValue(n),t);if(this.claimForm.controls.claims.controls[e].controls.taxAmount.patchValue(l),1e3==this.claimForm.get(`claims.${e}.taxPercentage`).value){let t=this.claimForm.get(`claims.${e}.amount`).value,n=t?parseFloat(String(t).replace(/,/g,"")):null,l=this.claimForm.get(`claims.${e}.taxAmount`).value;if(n&&l){let t=null!=n?"string"==typeof n?parseFloat(n.replace(/,/g,"")):"number"==typeof n?n:null:null,i=l?"string"==typeof l?parseFloat(l.replace(/,/g,"")):parseFloat(l):null,o=this.fixNumberOnUI(0!==n?100*i/(t-i):0,this.claimForm.value.claims[e].currenyCodeValue);this.claimForm.controls.claims.controls[e].controls.taxRate.patchValue(o),this.claimForm.controls.claims.controls[e].controls.taxType.patchValue(`Others - ${o} %`)}}}}))}calculateNoOfDays(e){var t,n,l,i,o,a;let r=this.claimForm.get("claims").at(e),s=(null===(t=r.get("perDiemstartDate"))||void 0===t?void 0:t.value)?c(null===(n=r.get("perDiemstartDate"))||void 0===n?void 0:n.value).format("YYYY-MM-DD"):null,d=null===(l=r.get("perDiemStartTime"))||void 0===l?void 0:l.value,m=(null===(i=r.get("perDiemEndDate"))||void 0===i?void 0:i.value)?c(null===(o=r.get("perDiemEndDate"))||void 0===o?void 0:o.value).format("YYYY-MM-DD"):null,p=null===(a=r.get("perDiemEndTime"))||void 0===a?void 0:a.value;if(!(s&&d&&m&&p))return void console.warn("Missing date or time values.");let u=c(`${s} ${d}`,"YYYY-MM-DD HH:mm"),f=c(`${m} ${p}`,"YYYY-MM-DD HH:mm");if(!u.isValid()||!f.isValid())return void console.error("Invalid start or end datetime format.");let g=f.diff(u,"hours",!0);this.noOfDays=(g/24).toFixed(2),r.patchValue({noOfDays:this.noOfDays})}validateEndDateTime(e){var t,n,l,i,o,a,r,s,d,m;let p=this.claimForm.get("claims").at(e),u=(null===(t=p.get("perDiemstartDate"))||void 0===t?void 0:t.value)?c(null===(n=p.get("perDiemstartDate"))||void 0===n?void 0:n.value).format("YYYY-MM-DD"):null,f=null===(l=p.get("perDiemStartTime"))||void 0===l?void 0:l.value,g=(null===(i=p.get("perDiemEndDate"))||void 0===i?void 0:i.value)?c(null===(o=p.get("perDiemEndDate"))||void 0===o?void 0:o.value).format("YYYY-MM-DD"):null,h=null===(a=p.get("perDiemEndTime"))||void 0===a?void 0:a.value;if(u&&f&&g&&h){const e=new Date(`${u}T${f}`),t=new Date(`${g}T${h}`);t<e&&(null===(r=p.get("perDiemEndDate"))||void 0===r||r.setErrors({invalidEndDateTime:!0}),null===(s=p.get("perDiemEndTime"))||void 0===s||s.setErrors({invalidEndDateTime:!0})),t>=e&&(null===(d=p.get("perDiemEndDate"))||void 0===d||d.setErrors(null),null===(m=p.get("perDiemEndTime"))||void 0===m||m.setErrors(null))}}onMilesInput(e){var t,n,l;let i=this.claimForm.get("claims").at(e),o=null===(t=i.get("miles"))||void 0===t?void 0:t.value;null!=o&&(o<=0?null===(n=i.get("miles"))||void 0===n||n.setErrors({min:!0}):null===(l=i.get("miles"))||void 0===l||l.setErrors(null))}roundMiles(e){var t,n;let l=this.claimForm.get("claims").at(e),i=null===(t=l.get("miles"))||void 0===t?void 0:t.value;null!=i&&i>0&&(null===(n=l.get("miles"))||void 0===n||n.setValue(parseFloat(i).toFixed(2)))}updateTaxAmountValidator(e){var t;return Object(l.c)(this,void 0,void 0,(function*(){const n=this.claimForm.get("claims").at(e),l=null===(t=n.get("taxPercentage"))||void 0===t?void 0:t.value,i=n.get("taxAmount");1e3==l?null==i||i.setValidators([a.H.required,Yl(l)]):null==i||i.setValidators([Yl(l)]),null==i||i.updateValueAndValidity()}))}onTaxAmountChange(e,t){let n=t;if(!n||null==n||n<=0)this.claimForm.controls.claims.controls[e].controls.taxAmount.patchValue(null);else{if(!/^\d*\.?\d*$/.test(n))return n="",void this.claimForm.controls.claims.controls[e].controls.taxAmount.patchValue(null);let t=this.claimForm.value.claims[e].currenyCodeValue,l=this.fixNumberOnUI(this.parseValue(n),t);this.claimForm.controls.claims.controls[e].controls.taxAmount.patchValue(l)}}onFinalAmountChange(e,t){let n=t;if(!n||null==n||n<=0)this.claimForm.controls.claims.controls[e].controls.finalamount.patchValue(0);else{if(!/^\d*\.?\d*$/.test(n))return n="",void this.claimForm.controls.claims.controls[e].controls.finalamount.patchValue(0);let t=this.claimForm.value.claims[e].currenyCodeValue,l=this.fixNumberOnUI(this.parseValue(n),t);this.claimForm.controls.claims.controls[e].controls.finalamount.patchValue(l)}}onSettlementAmountChange(e,t){let n=t;if(!n||null==n||n<=0)this.claimForm.controls.claims.controls[e].controls.settlement.patchValue(0);else{if(!/^\d*\.?\d*$/.test(n))return n="",void this.claimForm.controls.claims.controls[e].controls.settlement.patchValue(0);let t=this.claimForm.value.claims[e].currenyCodeValue,l=this.fixNumberOnUI(this.parseValue(n),t);this.claimForm.controls.claims.controls[e].controls.settlement.patchValue(l)}}fixNumberByDecimal(e,t){return"string"==typeof e&&(e=parseFloat(e.replace(/,/g,""))),!e||null==e||e<=0?"0.00":/^\d*\.?\d*$/.test(e)?this.fixNumberOnUI(this.parseValue(e),t):(e="","0.00")}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](a.i),i["\u0275\u0275directiveInject"](D.a),i["\u0275\u0275directiveInject"](O.a),i["\u0275\u0275directiveInject"](A.a),i["\u0275\u0275directiveInject"](M.a),i["\u0275\u0275directiveInject"](T.a),i["\u0275\u0275directiveInject"](p.b),i["\u0275\u0275directiveInject"](p.h),i["\u0275\u0275directiveInject"](p.a),i["\u0275\u0275directiveInject"](P.a),i["\u0275\u0275directiveInject"](w.a),i["\u0275\u0275directiveInject"](S.c),i["\u0275\u0275directiveInject"](R),i["\u0275\u0275directiveInject"](f))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["ng-component"]],inputs:{max:"max"},outputs:{close:"close"},features:[i["\u0275\u0275ProvidersFeature"]([f,{provide:F.c,useClass:E.c,deps:[F.f,E.a]},{provide:F.e,useValue:{parse:{dateInput:["DD-MMM-YYYY","DD-MM-YYYY"]},display:{dateInput:"DD-MMM-YYYY",monthYearLabel:"MMM YYYY",dateA11yLabel:"DD-MMM-YYYY",monthYearA11yLabel:"MMMM YYYY"}}}]),i["\u0275\u0275NgOnChangesFeature"]],decls:18,vars:7,consts:[[1,"container-fluid","expense-claim-creation",3,"formGroup"],[1,"row","pt-2","pb-2","pl-2","align-items-center",2,"font-weight","800","font-size","16px"],[1,"col-6","d-flex","align-items-center"],["class","d-flex align-items-center ml-4",4,"ngIf"],[1,"col-6","d-flex","justify-content-end"],[1,"header-start",2,"cursor","pointer",3,"click"],[1,"header-text","ml-2"],[1,"row","border-top"],[1,"col-3","pl-3","pr-0","pt-3"],[1,"row","p-0","pl-2","pb-2",2,"font-size","15px","font-weight","500"],["class","row pt-2",4,"ngIf"],[1,"col","pl-0","pr-0","border-left"],["class","mt-3 spinner-border d-flex justify-content-center","role","status","style","margin-left: 350px; color: #79BA44;",4,"ngIf"],[4,"ngIf"],[1,"d-flex","align-items-center","ml-4"],[1,"blinking-icon","pt-1"],[2,"font-weight","500","font-size","14px","color","#007bff","margin-left","5px"],[1,"row","pt-2"],[1,"col-12","pl-0","pr-0","scrollable-content-side-nav"],["class","d-flex mb-1",4,"ngFor","ngForOf"],[1,"d-flex","mb-1"],["mat-raised-button","","class","mr-1",3,"ngClass","matTooltip","click",4,"ngIf"],["style","width: 30px;\n            height: 30px;\n            color: rgb(95, 108, 129);\n            border-radius: 100%;\n            font-size: 12px;\n            overflow: hidden;\n            text-overflow: ellipsis;\n            white-space: nowrap;\n            text-align: center; /* Center the text horizontally */\n            line-height: 30px;\n            margin-top: 2px;\n            margin-left: 9px;\n            ",3,"border",4,"ngIf"],["mat-raised-button","",1,"mr-1",3,"ngClass","matTooltip","click"],[1,"icon-circle"],[1,"claim-icons"],[1,"category"],[2,"width","30px","height","30px","color","rgb(95, 108, 129)","border-radius","100%","font-size","12px","overflow","hidden","text-overflow","ellipsis","white-space","nowrap","text-align","center","/* center the text horizontally */\n            line-height","30px","margin-top","2px","margin-left","9px"],["role","status",1,"mt-3","spinner-border","d-flex","justify-content-center",2,"margin-left","350px","color","#79BA44"],[1,"sr-only",3,"ngStyle"],[1,"row","pt-3"],["class","col-4",4,"ngIf"],[1,"col-4"],[1,"label","pl-2"],["required","true","formControlName","legalEntityCode",1,"create-claim-field-inputsearch","font-family-class",3,"ngClass","placeholder","list","disabled","bgColorOnDisable","hideMatLabel","change"],["class","col-3",4,"ngIf"],["class","col-1 pt-4 d-flex",4,"ngIf"],[1,"row","border-bottom","pb-2"],["requiredTemplate",""],["highlightTemplate",""],["billingDateErrorTemplate",""],["futureDateErrorTemplate",""],["invalidEndDateTemplate",""],[1,"row"],["formArrayName","claims"],[1,"scrollable-content"],["class","row","style","border-bottom: 1px solid #d3d3d3; background: #F7F9FB;",3,"formGroupName",4,"ngFor","ngForOf"],[1,"col"],["class","col-8 pt-3 dialog dialog-info",3,"click",4,"ngIf"],["class","col-2 p-0 pt-2 icon-postion",4,"ngIf"],["formControlName","claimedBy",1,"font-family-class",3,"ngClass","isAutocomplete","readonly","disabled","bgColorOnDisable","change"],[3,"ngTemplateOutlet"],["appearance","outline",1,"create-claim-field",2,"width","100%",3,"ngClass"],["myFormField",""],["matInput","","required","true","formControlName","claimDate",3,"readonly","required"],["formControlName","costCenterCode",1,"create-claim-field-inputsearch","font-family-class",3,"ngClass","placeholder","list","disabled","bgColorOnDisable","hideMatLabel","change"],["matInput","","formControlName","department",3,"placeholder","readonly","required"],["required","true","formControlName","expenseTag",1,"create-claim-field-inputsearch",3,"ngClass","placeholder","list","disabled","bgColorOnDisable","hideMatLabel","input"],[1,"col-3"],["tooltip","Select a cost center to enable approver selection","content-type","html",1,"info-icon"],[3,"ngClass"],["appearance","outline",2,"width","100%"],["formControlName","headerLevelApproverList",3,"disabled","placeholder","panelStyle","selectionChange"],[3,"value","matTooltip",4,"ngFor","ngForOf"],[3,"value","matTooltip"],[1,"col-1","pt-4","d-flex"],[4,"ngFor","ngForOf"],["content-type","template","placement","top","imgHeight","32px","imgWidth","32px","borderStyle","solid","borderWidth","2px",1,"font-family-class",2,"margin","2px",3,"tooltip","id"],["approverTooltip",""],[1,"row","tooltip-text"],["style","color: #cf0001;",4,"ngIf"],["matInput","","formControlName","headerDescription",3,"placeholder","required","readonly"],[2,"color","#cf0001"],["appearance","outline","style","width:100%",3,"ngClass",4,"ngIf"],["appearance","outline",2,"width","100%",3,"ngClass"],["formControlName","customerBilling",3,"disabled","required","placeholder"],[3,"value",4,"ngFor","ngForOf"],[3,"value"],["formControlName","spentFrom",1,"create-claim-field-inputsearch","font-family-class",3,"ngClass","placeholder","list","disabled","bgColorOnDisable","hideMatLabel","change"],["tooltip","This field will be enabled based on the 'Spent From' selection.","content-type","html",1,"info-icon"],["formControlName","corporateCard",1,"create-claim-field-inputsearch","font-family-class",3,"ngClass","placeholder","list","disabled","bgColorOnDisable","hideMatLabel"],["matTooltip","This field is required",2,"color","#cf0001","font-size","8px","padding-left","1%"],[1,"app-input-search-field-highlight-inside"],[2,"color","#cf0001","font-size","8px","padding-left","1%"],["matTooltip","Future dates are not allowed.",2,"color","#cf0001","font-size","8px","padding-left","1%"],["matTooltip","End date and time cannot be earlier than the Start date and time.",2,"color","#cf0001","font-size","8px","padding-left","1%"],[1,"row",2,"border-bottom","1px solid #d3d3d3","background","#F7F9FB",3,"formGroupName"],[1,"row","pt-2","pb-1",3,"ngClass"],[1,"col","p-0"],[1,"col-12","p-0"],[1,"col","mt-1",2,"color","#1F2347","font-weight","600"],[1,"col-3","d-flex","justify-content-end"],["color","primary","mat-icon-button","",1,"remove-icon",3,"click"],[2,"color","#1F2347 !important"],["class","col-8",4,"ngIf"],["class","col-3 d-flex",4,"ngIf"],[1,"col-4","d-flex",2,"width","100%"],["content-type","template","max-width","300","placement","top",1,"people-display"],["style","margin-top: 9px; margin-left: 10px;",4,"ngIf"],["class","col-2",4,"ngIf"],["class","col-4 d-flex",4,"ngIf"],["class","col-6",4,"ngIf"],["class","row",4,"ngIf"],[1,"col-12"],[1,"col","p-0","pt-2"],["class","pt-2",4,"ngIf"],["class","col-3 p-0 d-flex justify-content-end",4,"ngIf"],["matInput","","formControlName","invoiceDate",3,"matDatepicker","min","max","required","disabled","dateChange"],["matSuffix","",3,"for"],["invoiceDate",""],["matInput","","formControlName","invoiceNo",3,"placeholder","readonly","required"],[1,"col-8"],["matInput","","formControlName","description",3,"placeholder","required","readonly"],["required","true","placeholder","Claim Type","formControlName","category",1,"create-claim-field-inputsearch",3,"ngClass","list","disabled","bgColorOnDisable","hideMatLabel","change"],["tooltip","Select cost centre so that you can select approvers","content-type","html",1,"info-icon"],["formControlName","approverMenu",3,"disabled","required","placeholder"],[3,"value","click",4,"ngFor","ngForOf"],[3,"value","click"],[1,"col-3","d-flex"],[1,"mr-3","header","my-auto"],["required","false","formControlName","taxRatetype",1,"create-claim-field-inputsearch",3,"placeholder","list","disabled","bgColorOnDisable","hideMatLabel","change"],["required","true","formControlName","currencyCode",1,"create-claim-field-inputsearch",3,"ngClass","placeholder","list","disabled","bgColorOnDisable","hideMatLabel","change"],[1,"label","pl-2",3,"matTooltip"],[1,"d-flex"],["matInput","","formControlName","amount",1,"pr-3",3,"placeholder","maxlength","readonly","input","focusout"],["class","currency-indicator",4,"ngIf"],["class","mat-hint mt-1",4,"ngIf"],[1,"currency-indicator"],[1,"mat-hint","mt-1"],["formControlName","taxPercentage",1,"create-claim-field-inputsearch","font-family-class",3,"ngClass","placeholder","list","disabled","bgColorOnDisable","hideMatLabel","change"],["matInput","","formControlName","taxAmount",3,"placeholder","readonly","focusout"],[1,"font-family-class",2,"width","100%",3,"token","optionLabel","API_URL","fieldValue","disabled","bgColorOnDisable","placeholder","selectedValues"],[2,"margin-top","9px","margin-left","10px"],[1,"row","mt-4"],["style","cursor: pointer","class","pl-1",3,"click",4,"ngFor","ngForOf"],[1,"third-div","people-involved-counter"],[1,"pl-1",2,"cursor","pointer",3,"click"],[1,"row","image-circle"],["src","https://assets.kebs.app/images/User.png","alt","profile",1,"first-div",3,"matTooltip"],[1,"row","mt-4",2,"margin-top","2px"],["style","cursor: pointer","class","pl-1",4,"ngFor","ngForOf"],[1,"pl-1",2,"cursor","pointer"],[1,"col-2"],["matInput","","formControlName","perDiemstartDate",3,"matDatepicker","placeholder","max","required","readonly","disabled","dateChange"],["matSuffix","",2,"color","#B9C0CA","position","relative","top","4px",3,"for"],["perDiemstartDate",""],["matInput","","type","text","placeholder","HH:MM","maxlength","5","formControlName","perDiemStartTime",3,"required","readonly","keydown","paste","keyup","change"],["perDiemStartTimeInput",""],["matSuffix","",2,"color","#B9C0CA"],["matInput","","formControlName","perDiemEndDate",3,"matDatepicker","placeholder","max","required","readonly","disabled","dateChange"],["perDiemEndDate",""],["matInput","","type","text","placeholder","HH:MM","maxlength","5","formControlName","perDiemEndTime",3,"required","readonly","keydown","paste","keyup","change"],["perDiemEndTimeInput",""],[1,"perdiem-total-days"],["matInput","","placeholder","Enter Location","formControlName","location",3,"required","readonly"],[1,"col-4","d-flex"],[3,"rangePicker","min","disabled"],["matStartDate","","formControlName","startDate","placeholder","Start date",3,"readonly"],["matEndDate","","formControlName","endDate","placeholder","End date",3,"readonly"],["matSuffix","",3,"for","disabled"],["picker",""],[1,"col-6"],["matInput","","placeholder","Enter From Location","formControlName","fromLocation",3,"required","readonly"],["matInput","","placeholder","Enter To Location","formControlName","toLocation",3,"required","readonly"],["formControlName","vehicleType",1,"create-claim-field-inputsearch","font-family-class",3,"ngClass","placeholder","list","disabled","bgColorOnDisable","hideMatLabel","change"],["tooltip","This field will be enabled when a Vehicle Type is selected.","content-type","html",1,"info-icon"],["formControlName","vehicleEngineType",1,"create-claim-field-inputsearch","font-family-class",3,"ngClass","placeholder","list","disabled","bgColorOnDisable","hideMatLabel"],[1,"split-input"],["matInput","","type","number","placeholder","Enter Distance","formControlName","miles",3,"disabled","input","blur"],["vertical",""],["formControlName","unit","placeholder","Unit"],[1,"col-12","pt-2","pb-2",2,"color","#1F2347","font-weight","600"],["class","col-3 pt-2",4,"ngIf"],["class","col-4 pt-2",4,"ngIf"],[1,"col-3","pt-2"],["appearance","outline",2,"width","40%"],[2,"width","20%"],[1,"col-4","pt-2"],["matInput","","formControlName","conversionRate",3,"placeholder","readonly","input","focusout"],["matInput","","formControlName","finalamount",3,"placeholder","readonly"],["matInput","","formControlName","settlement",3,"placeholder","readonly"],[1,"pt-2"],[2,"color","#cf0001","font-size","20px","vertical-align","middle"],[1,"pl-1",2,"color","#CF0001","font-weight","700","font-size","12px"],[1,"col-3","p-0","d-flex","justify-content-end"],[2,"max-width","200px","height","80%"],[1,"d-flex",3,"ngClass"],[1,"pt-2","pl-2","content-muliple",3,"matTooltip"],["mat-icon-button","","class","close-button ml-auto",4,"ngIf","ngIfElse"],["attachmentOld",""],["mat-icon-button","",1,"close-button","ml-auto"],[2,"color","black",3,"destinationBucket","routingKey","contextId","allowEdit","change"],["style","font-size: 21px !important;","matTooltip","Attach File",3,"click",4,"ngIf"],["class","m-auto","diameter","20",4,"ngIf"],[1,"ml-2","my-auto","file-name",2,"cursor","pointer",3,"matTooltip","click"],["matTooltip","Attach File","class","m-auto edit-icon",3,"click",4,"ngIf"],["matTooltip","Attach File",2,"font-size","21px !important",3,"click"],["type","file","name","file","ng2FileSelect","",2,"display","none",3,"uploader","accept","change"],["multiFileInput",""],["diameter","20",1,"m-auto"],["matTooltip","Attach File",1,"m-auto","edit-icon",3,"click"],[1,"col-8","pt-3","dialog","dialog-info",3,"click"],[1,"msg-text"],[1,"icon"],["xmlns","http://www.w3.org/2000/svg","height","24px","viewBox","0 -960 960 960","width","24px","fill","#1890ff"],["d","M440-280h80v-240h-80v240Zm40-320q17 0 28.5-11.5T520-640q0-17-11.5-28.5T480-680q-17 0-28.5 11.5T440-640q0 17 11.5 28.5T480-600Zm0 520q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-80q134 0 227-93t93-227q0-134-93-227t-227-93q-134 0-227 93t-93 227q0 134 93 227t227 93Zm0-320Z"],[1,"col-2","p-0","pt-2","icon-postion"],["mat-icon-button","","matTooltip","Save as Draft","type","submit",1,"iconbtn","ml-3","mt-1","mb-1",3,"ngClass","disabled","click"],["style","color: white",4,"ngIf","ngIfElse"],["showDraftSpinner",""],["mat-icon-button","","matTooltip","Create Claim","type","submit",1,"iconbtn","ml-3","mt-1","mb-1",3,"disabled","click"],[2,"color","white"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275elementStart"](1,"div",1),i["\u0275\u0275elementStart"](2,"div",2),i["\u0275\u0275elementStart"](3,"span"),i["\u0275\u0275text"](4,"New Claim"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](5,X,5,1,"span",3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](6,"div",4),i["\u0275\u0275elementStart"](7,"div",5),i["\u0275\u0275listener"]("click",(function(){return t.closeForm()})),i["\u0275\u0275elementStart"](8,"span",6),i["\u0275\u0275text"](9,"Close"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](10,"div",7),i["\u0275\u0275elementStart"](11,"div",8),i["\u0275\u0275elementStart"](12,"div",9),i["\u0275\u0275text"](13," Claim Type "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](14,te,3,1,"div",10),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](15,"div",11),i["\u0275\u0275template"](16,le,3,2,"div",12),i["\u0275\u0275template"](17,Rl,37,24,"div",13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275property"]("formGroup",t.claimForm),i["\u0275\u0275advance"](5),i["\u0275\u0275property"]("ngIf",!t.isExpenseCreationAllowed&&t.defaultLegalEntityBasedOnClaimer),i["\u0275\u0275advance"](7),i["\u0275\u0275styleProp"]("color",t.theme_color1),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf",t.claimType.length>0),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf",0==t.showClaimCreation),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",1==t.showClaimCreation))},directives:[a.w,a.n,d.NgIf,v.a,d.NgForOf,C.a,d.NgClass,x.a,d.NgStyle,q.a,a.F,a.v,a.l,a.h,H.a,d.NgTemplateOutlet,g.c,h.b,a.e,b.a,I.c,F.p,Y.a,g.f,a.o,y.g,y.i,g.i,y.f,a.q,J,y.d,y.l,y.k,y.e,a.A,K.a,s.b,_.c],styles:[".mt-2-5[_ngcontent-%COMP%]{margin-top:13px}.people-display[_ngcontent-%COMP%]{display:inline-flex;cursor:pointer}.people-display[_ngcontent-%COMP%]   .first-div[_ngcontent-%COMP%]{width:24px;height:24px;border-radius:50%;margin-right:-10px;margin-top:0}.people-display[_ngcontent-%COMP%]   .second-div[_ngcontent-%COMP%], .people-display[_ngcontent-%COMP%]   .third-div[_ngcontent-%COMP%]{width:24px;height:24px;border-radius:50%;margin-left:3px}.people-display[_ngcontent-%COMP%]   .third-div[_ngcontent-%COMP%]{background-color:#fff;align-items:center;text-align:center;display:flex;justify-content:center;font-size:12px}.image-circle[_ngcontent-%COMP%]{display:flex}.create-claim-styles[_ngcontent-%COMP%]   .restict-cursor[_ngcontent-%COMP%]{cursor:not-allowed}.create-claim-styles[_ngcontent-%COMP%]   .btn-spinner[_ngcontent-%COMP%]{margin-left:4px}.create-claim-styles[_ngcontent-%COMP%]   .name[_ngcontent-%COMP%]{color:#cf0001;font-weight:500;font-size:14px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.create-claim-styles[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{color:#5f5f5f;font-size:12px!important}.create-claim-styles[_ngcontent-%COMP%]   .mat-hint[_ngcontent-%COMP%]{color:red;padding-top:6px;font-size:10px}.create-claim-styles[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]{display:flex;line-height:26px;border-radius:50%;cursor:context-menu!important;width:26px;height:26px;background-color:#fbfbfb;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.create-claim-styles[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#cf0001;font-size:18px;margin-top:3px!important;margin-left:4px!important}.create-claim-styles[_ngcontent-%COMP%]   .iconbtn[_ngcontent-%COMP%], .create-claim-styles[_ngcontent-%COMP%]   .iconbtn-submit[_ngcontent-%COMP%]{color:#fff;padding:0;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.create-claim-styles[_ngcontent-%COMP%]   .iconbtn-submit[_ngcontent-%COMP%]{background-color:#c92020}.create-claim-styles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}.create-claim-styles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%]{border-radius:64px!important;border:1px solid #52c41a;margin-bottom:6%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;font-weight:400;width:230px;height:36px;text-align:left;background:#eef9e8;padding:2px}.create-claim-styles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%]   .icon-circle[_ngcontent-%COMP%]{background:#1f2347;width:24px;height:24px;border-radius:50px;margin-top:3px;margin-left:2px}.create-claim-styles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%]   .claim-icons[_ngcontent-%COMP%]{font-size:15px;margin-right:6px;margin-left:4px;color:#fff;width:10px;height:13.33px;margin-top:5px!important;vertical-align:top}.create-claim-styles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%]   .category[_ngcontent-%COMP%]{color:#52c41a;font-weight:400;font-size:12px;vertical-align:middle;margin-top:-30px;margin-left:50px}.create-claim-styles[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]{border-radius:64px!important;border:1px solid #caccce;margin-bottom:4%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;font-weight:400;width:230px;height:36px;text-align:left;padding:2px}.create-claim-styles[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]   .icon-circle[_ngcontent-%COMP%]{background:#1f2347;width:24px;height:24px;border-radius:50px;margin-top:3px;margin-left:2px}.create-claim-styles[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]   .claim-icons[_ngcontent-%COMP%]{font-size:15px;margin-right:6px;margin-left:4px;color:#fff;width:10px;height:13.33px;margin-top:5px!important;vertical-align:top}.create-claim-styles[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]   .category[_ngcontent-%COMP%]{color:#5f6c81;font-weight:400;font-size:12px;vertical-align:middle;margin-top:-30px;margin-left:50px}.create-claim-styles[_ngcontent-%COMP%]   .title-table[_ngcontent-%COMP%]{font-size:14px;color:#1a1a1a}.create-claim-styles[_ngcontent-%COMP%]   .create-claim-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%!important}.create-claim-styles[_ngcontent-%COMP%]   .create-claim-field-inputsearch[_ngcontent-%COMP%]{font-size:13px!important}.create-claim-styles[_ngcontent-%COMP%]   .singleClaimExpenseHeader[_ngcontent-%COMP%]{position:relative;z-index:1}.create-claim-styles[_ngcontent-%COMP%]   .scrollable-content[_ngcontent-%COMP%]{height:450px;overflow-y:auto;overflow-x:hidden;padding-right:15px}.create-claim-styles[_ngcontent-%COMP%]   .scrollable-content-side-nav[_ngcontent-%COMP%]{height:600px;overflow-y:auto;overflow-x:hidden;padding-right:15px}.create-claim-styles[_ngcontent-%COMP%]   .singleClaimExpenseItem[_ngcontent-%COMP%]{height:100%;overflow-y:auto;overflow-x:hidden}.create-claim-styles[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:25px;cursor:pointer}.create-claim-styles[_ngcontent-%COMP%]   .edit-icon[_ngcontent-%COMP%]{color:#66615b;padding-top:7px;font-size:15px;cursor:pointer}.create-claim-styles[_ngcontent-%COMP%]   .file-name[_ngcontent-%COMP%]{font-size:14px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.create-claim-styles[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}.create-claim-styles[_ngcontent-%COMP%]   .invalid-attachment[_ngcontent-%COMP%]{color:#cf0001;border:2px solid #cf0001;height:100%;width:100%;border-radius:5px}.create-claim-styles[_ngcontent-%COMP%]   .form-check-input-multiple[_ngcontent-%COMP%]{position:absolute;margin-left:-7rem}.create-claim-styles[_ngcontent-%COMP%]   .form-check-input-single[_ngcontent-%COMP%]{position:absolute;margin-top:.2rem;margin-left:-7rem}.create-claim-styles[_ngcontent-%COMP%]   .icon-postion[_ngcontent-%COMP%]{margin-top:-5px}.create-claim-styles[_ngcontent-%COMP%]   .attachment-btn[_ngcontent-%COMP%]{width:100%;color:#5f5f5f;border:2px solid #d3d3d3;height:90%;border-radius:5px}.create-claim-styles[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;width:90%}.create-claim-styles[_ngcontent-%COMP%]   .content-muliple[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;width:100%}.expense-claim-creation[_ngcontent-%COMP%]   .blinking-icon[_ngcontent-%COMP%]{animation:blink 1s infinite alternate;color:#007bff;font-size:15px}@keyframes blink{0%{opacity:1}to{opacity:.3}}.expense-claim-creation[_ngcontent-%COMP%]   .header-start[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.33rem;width:55px;height:20px;border:1px solid #526179;border-radius:3px}.expense-claim-creation[_ngcontent-%COMP%]   .header-start[_ngcontent-%COMP%]   .back-icon[_ngcontent-%COMP%]{width:16px;height:25px;border-radius:4px;cursor:pointer;display:flex;align-items:baseline}.expense-claim-creation[_ngcontent-%COMP%]   .header-start[_ngcontent-%COMP%]   .icn-class[_ngcontent-%COMP%]{font-size:14px;color:#526179;margin-top:1px}.expense-claim-creation[_ngcontent-%COMP%]   .header-start[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]{font-size:14px;font-weight:400}.expense-claim-creation[_ngcontent-%COMP%]   .app-input-search-field-highlight[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-outline, .expense-claim-creation[_ngcontent-%COMP%]   .app-input-search-field-highlight[_ngcontent-%COMP%]     .mat-form-field-appearance-outline:hover .mat-form-field-outline{color:#d63031!important;border:1px solid #d63031!important;border-radius:5px!important}.expense-claim-creation[_ngcontent-%COMP%]   .app-input-search-field-highlight[_ngcontent-%COMP%]     .mat-select-value{color:#45546e}.expense-claim-creation[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-outline{background-color:#fff}.expense-claim-creation[_ngcontent-%COMP%]     .mat-focused .mat-form-field-placeholder, .expense-claim-creation[_ngcontent-%COMP%]     .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline, .expense-claim-creation[_ngcontent-%COMP%]     .mat-form-field-appearance-outline:hover .mat-form-field-outline, .expense-claim-creation[_ngcontent-%COMP%]     .mat-form-field-placeholder{color:var(--formBorderColor)!important}.expense-claim-creation[_ngcontent-%COMP%]   .theme-green[_ngcontent-%COMP%]     .mat-form-field-appearance-outline:hover .mat-form-field-outline{color:var(--formBorderColor)!important;color:var(--color)!important}.expense-claim-creation[_ngcontent-%COMP%]     .mat-pseudo-checkbox:after{position:inline!important}.expense-claim-creation[_ngcontent-%COMP%]     .mat-checkbox-checked.mat-accent .mat-checkbox-background, .expense-claim-creation[_ngcontent-%COMP%]     .mat-checkbox-indeterminate.mat-accent .mat-checkbox-background, .expense-claim-creation[_ngcontent-%COMP%]     .mat-checkbox-ripple .mat-ripple-element{background-color:var(--formBorderColor)!important}.expense-claim-creation[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]:focus, .expense-claim-creation[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]:hover{border:2px var(--formBorderColor)!important;outline:none}.expense-claim-creation[_ngcontent-%COMP%]   .scrollable-content[_ngcontent-%COMP%]{height:450px;overflow-y:auto;overflow-x:hidden}.expense-claim-creation[_ngcontent-%COMP%]   .scrollable-content-side-nav[_ngcontent-%COMP%]{height:600px;overflow-y:auto;overflow-x:hidden;padding-right:15px}.expense-claim-creation[_ngcontent-%COMP%]     .mat-badge-warn .mat-badge-content{color:#fff;background:var(--formBorderColor)!important}.expense-claim-creation[_ngcontent-%COMP%]     .mat-focused .mat-form-field-label, .expense-claim-creation[_ngcontent-%COMP%]     .mat-form-field.mat-focused .mat-form-field-label, .expense-claim-creation[_ngcontent-%COMP%]     .mat-form-field.mat-form-field-invalid .mat-form-field-label{color:var(--formBorderColor)}.expense-claim-creation[_ngcontent-%COMP%]     .mat-primary .mat-option.mat-selected:not(.mat-option-disabled){color:var(--formBorderColor)!important}.border-bottom[_ngcontent-%COMP%]{border-bottom:2px solid #dadce2}.border-left[_ngcontent-%COMP%]{border-left:2px solid #dadce2}.border-right[_ngcontent-%COMP%]{border-right:2px solid #dadce2}.border-top[_ngcontent-%COMP%]{border-top:2px solid #dadce2}.info-icon[_ngcontent-%COMP%]{font-size:13px;margin-left:5px;height:10px;cursor:pointer;margin-top:2.5px;color:grey}.custom-tooltip[_ngcontent-%COMP%]{background-color:#111434!important;color:#fff!important}.btn-active[_ngcontent-%COMP%]{border-radius:64px!important;border:1px var(--formBorderColor);margin-bottom:4%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;font-weight:400;width:230px;height:36px;text-align:left;background:#eef9e8;padding:2px}.btn-active[_ngcontent-%COMP%]   .icon-circle[_ngcontent-%COMP%]{background:#1f2347;width:24px;height:24px;border-radius:50px;margin-top:5px;margin-left:2px}.btn-active[_ngcontent-%COMP%]   .claim-icons[_ngcontent-%COMP%]{font-size:15px;margin-right:6px;margin-left:4px;color:#fff;width:10px;height:13.33px;margin-top:5px!important;vertical-align:top}.btn-active[_ngcontent-%COMP%]   .category[_ngcontent-%COMP%]{color:var(--formBorderColor);font-weight:500;font-size:12px;vertical-align:middle;margin-top:-30px;margin-left:40px}.btn-not-active[_ngcontent-%COMP%]{border-radius:64px!important;border:1px solid #caccce;margin-bottom:4%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;font-weight:400;width:230px;height:36px;text-align:left;padding:2px}.btn-not-active[_ngcontent-%COMP%]   .icon-circle[_ngcontent-%COMP%]{background:#1f2347;width:24px;height:24px;border-radius:50px;margin-top:3px;margin-left:2px}.btn-not-active[_ngcontent-%COMP%]   .claim-icons[_ngcontent-%COMP%]{font-size:15px;margin-right:6px;margin-left:4px;color:#fff;width:10px;height:13.33px;margin-top:5px!important;vertical-align:top}.btn-not-active[_ngcontent-%COMP%]   .category[_ngcontent-%COMP%]{color:#5f6c81;font-weight:500;font-size:12px;vertical-align:middle;margin-top:-30px;margin-left:40px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.currency-indicator[_ngcontent-%COMP%]{margin-left:5px;color:#999}.mat-form-field[_ngcontent-%COMP%]:not(.mat-form-field-appearance-legacy)   .mat-form-field-suffix[_ngcontent-%COMP%]   .mat-icon-button[_ngcontent-%COMP%],   .mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button,   .mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button{height:2em!important}  .disabled-field .mat-form-field-appearance-outline .mat-form-field-outline-thick{background-color:#e8e9ee!important}  .disabled-field .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label{color:rgba(0,0,0,.6)!important}.disabled-field[_ngcontent-%COMP%]   .mat-form-field-outline-end[_ngcontent-%COMP%],   .disabled-field .mat-form-field-outline-end,   .disabled-field .mat-form-field-outline-gap,   .disabled-field .mat-form-field-outline-start{background-color:#e8e9ee!important}.mat-hint[_ngcontent-%COMP%]{color:#cf0001;font-size:8px;padding-left:1%;margin-top:10px!important}.people-involved-counter[_ngcontent-%COMP%]{color:var(--formBorderColor)!important}.dialog-info[_ngcontent-%COMP%]{background:#e8f4ff;border-color:#1890ff}.dialog[_ngcontent-%COMP%]{min-width:320px;min-height:48px;border-width:2px;border-style:solid;border-radius:4px;padding:10px;cursor:pointer;margin-top:8px;bottom:7px}.dialog[_ngcontent-%COMP%]   .msg-text[_ngcontent-%COMP%]{font-family:Plus Jakarta Sans!important;font-size:14px;font-weight:400;color:#515965;line-height:20px;margin:0}.dialog[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{margin-right:8px}.duplicate-error[_ngcontent-%COMP%]{background:#f7f9fb}.label[_ngcontent-%COMP%]{color:#5f6c81;font-size:11px;font-weight:400;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;width:90%}.split-input[_ngcontent-%COMP%]{display:flex;align-items:center;width:95%}.split-input[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{flex:1;border-right:1px solid #ccc;padding-right:10px}mat-divider[_ngcontent-%COMP%]{height:50%;margin:0 8px}.split-input[_ngcontent-%COMP%]   mat-select[_ngcontent-%COMP%]{width:25%}.perdiem-total-days[_ngcontent-%COMP%]{color:#45546e;font-weight:700;letter-spacing:.02em;text-transform:capitalize;display:block;margin-top:12px;margin-left:8px}.custom-dropdown-panel[_ngcontent-%COMP%]{max-height:200px;overflow-y:auto}"],data:{animation:[Object(r.o)("fadeInOut",[Object(r.l)("void",Object(r.m)({opacity:0})),Object(r.n)("void <=> *",Object(r.e)(400))])]}}),e})();function Hl(e){let t="string"==typeof e.value?parseFloat(e.value.replace(/,/g,"")):e.value;return t<=0?{validAmount:!1}:/^[^~!@#$%\^&*()_+={}|[\]\\:';"<>?,/]+(\.[^~!@#$%\^&*()_+={}|[\]\\:';"<>?,/]+)?$/.test(t)?t.toString().split(".")[0].length>15?{maxDigitsExceeded:!0}:null:{validAmount:!1}}function Yl(e){return t=>{let n="string"==typeof t.value?parseFloat(t.value.replace(/,/g,"")):t.value;return 1e3===e&&n<=0?{validAmount:!1,message:"Amount must be greater than 0 when tax is Choosen as Others"}:1e3!==e&&n<0?{validAmount:!1,message:"Amount cannot be negative"}:/^[^~!@#$%\^&*()_+={}|[\]\\:';"<>?,/]+(\.[^~!@#$%\^&*()_+={}|[\]\\:';"<>?,/]+)?$/.test(n)?null:{validAmount:!1,message:"Invalid characters in amount"}}}},"3vwd":function(e,t,n){"use strict";n.d(t,"a",(function(){return E}));var l=n("fXoL"),i=n("wO+i"),o=n("m5YA"),a=n("B0y8"),r=n("0IaG"),s=n("JLuW"),c=n("ofXK"),d=n("bTqV"),m=n("TU8p"),p=n("NFeN"),u=n("Qu3c"),f=n("tvi8"),g=n("H/9Z");function h(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"button",2),l["\u0275\u0275listener"]("click",(function(){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"]().openPopUp()})),l["\u0275\u0275elementStart"](1,"mat-icon"),l["\u0275\u0275text"](2,"attach_file"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&e){const e=l["\u0275\u0275nextContext"]();l["\u0275\u0275propertyInterpolate"]("matBadge",e.fileCount),l["\u0275\u0275property"]("matBadgeHidden",0==e.fileCount)}}function v(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"button",4),l["\u0275\u0275listener"]("click",(function(){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"](2).openPopUp()})),l["\u0275\u0275elementStart"](1,"span",5),l["\u0275\u0275text"](2),l["\u0275\u0275elementEnd"](),l["\u0275\u0275text"](3," Attachments "),l["\u0275\u0275elementEnd"]()}if(2&e){const e=l["\u0275\u0275nextContext"](2);l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate"](e.fileCount)}}function C(e,t){if(1&e&&(l["\u0275\u0275element"](0,"img",10),l["\u0275\u0275pipe"](1,"fileIcon")),2&e){const e=l["\u0275\u0275nextContext"](3);l["\u0275\u0275property"]("src",l["\u0275\u0275pipeBind1"](1,1,e.singleFileDetail.file_format),l["\u0275\u0275sanitizeUrl"])}}function x(e,t){if(1&e&&(l["\u0275\u0275element"](0,"img",10),l["\u0275\u0275pipe"](1,"async"),l["\u0275\u0275pipe"](2,"getUrl")),2&e){const e=l["\u0275\u0275nextContext"](3);l["\u0275\u0275property"]("src",l["\u0275\u0275pipeBind1"](1,1,l["\u0275\u0275pipeBind1"](2,3,e.singleFileDetail.cdn_link)),l["\u0275\u0275sanitizeUrl"])}}function y(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"button",4),l["\u0275\u0275listener"]("click",(function(){l["\u0275\u0275restoreView"](e);const t=l["\u0275\u0275nextContext"](2);return t.viewFile(t.singleFileDetail)})),l["\u0275\u0275elementStart"](1,"span",6),l["\u0275\u0275template"](2,C,2,3,"img",7),l["\u0275\u0275template"](3,x,3,5,"ng-template",null,8,l["\u0275\u0275templateRefExtractor"]),l["\u0275\u0275elementEnd"](),l["\u0275\u0275text"](5,"\xa0 "),l["\u0275\u0275elementStart"](6,"div",9),l["\u0275\u0275text"](7),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&e){const e=l["\u0275\u0275reference"](4),t=l["\u0275\u0275nextContext"](2);l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf","png"!=t.singleFileDetail.file_format&&"jpg"!=t.singleFileDetail.file_format&&"jpeg"!=t.singleFileDetail.file_format)("ngIfElse",e),l["\u0275\u0275advance"](4),l["\u0275\u0275propertyInterpolate"]("matTooltip",t.singleFileDetail.file_name),l["\u0275\u0275advance"](1),l["\u0275\u0275textInterpolate1"](" ",t.singleFileDetail.file_name," ")}}function b(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"span"),l["\u0275\u0275text"](1,"-"),l["\u0275\u0275elementEnd"]())}function _(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div"),l["\u0275\u0275template"](1,v,4,1,"button",3),l["\u0275\u0275template"](2,y,8,4,"button",3),l["\u0275\u0275template"](3,b,2,0,"span",1),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",e.fileCount>1),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",1==e.fileCount&&e.singleFileDetail),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",0==e.fileCount)}}let E=(()=>{class e{constructor(e,t){this.dialog=e,this._shared=t,this.change=new l.EventEmitter,this.btnColor="#cf0001",this.isDialogOpened=!1,this.isViewFileOpened=!1,this.fileCount=0}ngOnInit(){document.documentElement.style.setProperty("--btnColor",this.btnColor),this.destinationBucket&&this.contextId&&this._shared.getObjectCount(this.destinationBucket,this.contextId).pipe(Object(i.a)("data")).subscribe(e=>{this.fileCount=e,1==this.fileCount&&this.getFileDetail()},e=>{console.error(e)})}getFileDetail(){this._shared.retrieveUploadedObjects(this.destinationBucket,this.contextId).pipe(Object(i.a)("data")).subscribe(e=>{let t=[];t.push(...e),t.length>0&&(this.singleFileDetail=t[0]),console.log(this.singleFileDetail)},e=>{console.error(e)})}openPopUp(){let e=null;0==this.isDialogOpened&&(e=this.dialog.open(a.a,{width:"100%",height:"90%",data:{data:{destinationBucket:this.destinationBucket,routingKey:this.routingKey,contextId:this.contextId,allowEdit:this.allowEdit,myFilesDefaultFolder:this.myFilesDefaultFolder},expHeaderId:this.expHeaderId},disableClose:!0}),this.isDialogOpened=!0),e.afterClosed().subscribe(e=>{this.isDialogOpened=!1;let t=e.contextId;this.fileCount=e.fileCount,this.change.emit(t)})}viewFile(e){console.log(this.isViewFileOpened);let t=e.cdn_link,n=null;0==this.isViewFileOpened&&(this.isViewFileOpened=!0,this._shared.getDownloadUrl(t).subscribe(t=>{n=this.dialog.open(o.a,{width:"100%",height:"100%",data:{selectedFileUrl:t.data,fileFormat:e.file_format,expHeaderId:this.expHeaderId}}),n.afterClosed().subscribe(e=>{this.isViewFileOpened=!1})}))}}return e.\u0275fac=function(t){return new(t||e)(l["\u0275\u0275directiveInject"](r.b),l["\u0275\u0275directiveInject"](s.a))},e.\u0275cmp=l["\u0275\u0275defineComponent"]({type:e,selectors:[["attachment-upload-btn"]],inputs:{destinationBucket:"destinationBucket",routingKey:"routingKey",contextId:"contextId",allowEdit:"allowEdit",myFilesDefaultFolder:"myFilesDefaultFolder",expHeaderId:"expHeaderId",btnColor:"btnColor"},outputs:{change:"change"},decls:2,vars:2,consts:[["mat-icon-button","","matBadgeColor","warn","class","upload-btn",3,"matBadge","matBadgeHidden","click",4,"ngIf"],[4,"ngIf"],["mat-icon-button","","matBadgeColor","warn",1,"upload-btn",3,"matBadge","matBadgeHidden","click"],["mat-button","",3,"click",4,"ngIf"],["mat-button","",3,"click"],[1,"attachment-count"],[1,"thumbnail"],["style","height: 26px; width: 26px; border-radius: 50%","alt","",3,"src",4,"ngIf","ngIfElse"],["showimg",""],[1,"file-name",3,"matTooltip"],["alt","",2,"height","26px","width","26px","border-radius","50%",3,"src"]],template:function(e,t){1&e&&(l["\u0275\u0275template"](0,h,3,2,"button",0),l["\u0275\u0275template"](1,_,4,3,"div",1)),2&e&&(l["\u0275\u0275property"]("ngIf",t.allowEdit),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",!t.allowEdit))},directives:[c.NgIf,d.a,m.a,p.a,u.a],pipes:[f.a,c.AsyncPipe,g.a],styles:[".file-name[_ngcontent-%COMP%]{white-space:nowrap;width:81px;overflow:hidden;text-overflow:ellipsis;display:inline-block}.attachment-count[_ngcontent-%COMP%]{color:var(--btnColor)!important}"]}),e})()},ES5A:function(e,t,n){"use strict";n.r(t),n.d(t,"ClaimCreationComponent",(function(){return Ft}));var l=n("mrSG"),i=n("fXoL"),o=n("xG9w"),a=n("3Pt+"),r=n("R0Ic"),s=n("7pIB"),c=n("wd/R"),d=n("ofXK"),m=n("AytR"),p=n("0IaG"),u=n("kmnG"),f=n("qFsG"),g=n("NFeN"),h=n("bTqV"),v=n("Qu3c"),C=n("iadO"),x=(n("Xi0T"),n("lVl8")),y=n("Xa2L"),b=n("1yaQ"),_=n("FKr1"),E=(n("gvOY"),n("d3UM")),F=(n("ltUE"),n("pA3K"),n("dNgK")),I=n("JqCM"),w=n("fkeC"),S=n("XXEo"),D=n("ucYs"),O=n("LcQX"),A=n("1d+P"),M=n("m3M1"),T=n("mGZT"),P=n("jtHE"),k=n("XNiG"),V=n("NJ67"),L=n("1G5W"),B=n("WJ5W");function N(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-label"),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](e.placeholder)}}function R(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"mat-option",7),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275nextContext"](),i["\u0275\u0275property"]("value",null),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"]("Clear Entries"))}function q(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-option",8),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;i["\u0275\u0275property"]("value",e.id||e._id)("matTooltip",e.name),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let H=(()=>{class e extends V.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new a.j,this.fieldFilterCtrl=new a.j,this.list=[],this.required=!1,this.disableNone=!1,this.valueChange=new i.EventEmitter,this.disabled=!1,this.hasNoneOption=!0,this.filteredList=new P.a,this.change=new i.EventEmitter,this.hideMatLabel=!1,this._onDestroy=new k.b,this.emitChanges=e=>{console.log("hhhhhhhhhhhhhhhhhh"),console.log(null==e?void 0:e.id),console.log(e),console.log(this.fieldCtrl.value);let t=o.filter(this.list,{id:this.fieldCtrl.value});console.log(this.list),console.log("datadata"),console.log(t),this.change.emit(t?t[0]:null)}}ngOnInit(){console.log("app-exp-input-search"),this.fieldFilterCtrl.valueChanges.pipe(Object(L.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(L.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](i.Renderer2))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-exp-input-search"]],inputs:{list:"list",placeholder:"placeholder",required:"required",disableNone:"disableNone",disabled:"disabled",hasNoneOption:"hasNoneOption",hideMatLabel:"hideMatLabel"},outputs:{valueChange:"valueChange",change:"change"},features:[i["\u0275\u0275ProvidersFeature"]([{provide:a.t,useExisting:Object(i.forwardRef)(()=>e),multi:!0}]),i["\u0275\u0275InheritDefinitionFeature"],i["\u0275\u0275NgOnChangesFeature"]],decls:9,vars:11,consts:[["appearance","outline"],[4,"ngIf"],[3,"formControl","placeholder","required","disabled","selectionChange"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value",4,"ngIf"],[3,"value","matTooltip",4,"ngFor","ngForOf"],[3,"value"],[3,"value","matTooltip"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"mat-form-field",0),i["\u0275\u0275template"](1,N,2,1,"mat-label",1),i["\u0275\u0275elementStart"](2,"mat-select",2,3),i["\u0275\u0275listener"]("selectionChange",(function(e){return t.emitChanges(e.value)})),i["\u0275\u0275elementStart"](4,"mat-option"),i["\u0275\u0275element"](5,"ngx-mat-select-search",4),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](6,R,2,2,"mat-option",5),i["\u0275\u0275template"](7,q,2,3,"mat-option",6),i["\u0275\u0275pipe"](8,"async"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",!t.hideMatLabel),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.hasNoneOption),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngForOf",i["\u0275\u0275pipeBind1"](8,9,t.filteredList)))},directives:[u.c,d.NgIf,E.c,a.v,a.k,a.F,_.p,B.a,d.NgForOf,u.g,v.a],pipes:[d.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%}"]}),e})();var Y=n("me71"),j=n("qFYv"),W=n("Kj3r"),$=n("tk/3");const U=["onScroll"];function z(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"span"),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=i["\u0275\u0275nextContext"]().$implicit;i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"]("",n[e],"\xa0")}}function G(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-option",11),i["\u0275\u0275template"](1,z,2,1,"span",12),i["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=i["\u0275\u0275nextContext"]();i["\u0275\u0275property"]("value",e),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngForOf",n.optionLabel)}}let J=(()=>{class e{constructor(e,t){this._http=e,this._mulL=t,this.selectedValues=new i.EventEmitter,this.list=[],this.searchCtrl=new a.j,this.selectedValCtrl=new a.j,this._onDestroy=new k.b,this.isLoading=!1,this.startIndex=0,this.endIndex=15,this.noToRetrieve=[]}ngOnInit(){return Object(l.c)(this,void 0,void 0,(function*(){this.fieldValue&&(this.list=this.list.concat(this.fieldValue),this.selectedValCtrl.patchValue(this.fieldValue)),yield this.fetchData(),this.searchCtrl.valueChanges.pipe(Object(L.a)(this._onDestroy),Object(W.a)(700)).subscribe(e=>Object(l.c)(this,void 0,void 0,(function*(){null!=e&&""!=e&&(this.list=[],this.startIndex=0,this.endIndex=15,yield this.fetchData())}))),this.selectedValCtrl.valueChanges.subscribe(e=>{this.selectedValues.emit(e)}),this._mulL.getRemoveIndex().subscribe(e=>{this.removeSelectedOption(e)})}))}setEmployeeList(e){if(this.selectedValCtrl.value){let t=e.filter(({associate_id:e,display_name:t})=>!this.selectedValCtrl.value.some(({associate_id:n,display_name:l})=>e===n&&t===l)),n=this.list.filter(({associate_id:e,display_name:t})=>!this.selectedValCtrl.value.some(({associate_id:n,display_name:l})=>e===n&&t===l));this.list=this.selectedValCtrl.value,this.list=this.list.concat(n),this.list=this.list.concat(t)}else this.list=this.list.concat(e)}removeSelectedOption(e){let t=[];t=this.selectedValCtrl.value,t.splice(e,1),this.selectedValCtrl.patchValue([...t]),this.selectedValues.emit(this.selectedValCtrl.value)}fetchData(){return Object(l.c)(this,void 0,void 0,(function*(){this.isLoading=!0;let e=yield this._mulL.getEmployeeList(this.searchCtrl.value,this.startIndex,this.endIndex,o.pluck(this.selectedValCtrl.value,"associate_id"));this.isLoading=!1,this.setEmployeeList(e.data)}))}onOpenedChange(e,t){e&&this[t].panel.nativeElement.addEventListener("scroll",e=>{let n=Math.ceil(this[t].panel.nativeElement.scrollTop),l=this[t].panel.nativeElement.scrollHeight-this[t].panel.nativeElement.offsetHeight;n-1!=l&&n!=l&&n+1!=l||(this.startIndex+=15,this.endIndex+=15,this.fetchData())})}cancelSearchValue(){this.searchCtrl.patchValue(""),this.fetchData()}ngOnChanges(){this.selectedValCtrl.patchValue(this.fieldValue)}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"]($.c),i["\u0275\u0275directiveInject"](M.a))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search-people-involved-dropdown"]],viewQuery:function(e,t){if(1&e&&i["\u0275\u0275viewQuery"](U,!0),2&e){let e;i["\u0275\u0275queryRefresh"](e=i["\u0275\u0275loadQuery"]())&&(t.onScroll=e.first)}},inputs:{label:"label",placeholder:"placeholder",appearance:"appearance",optionLabel:"optionLabel",API_URL:"API_URL",bodyParams:"bodyParams",token:"token",fieldValue:"fieldValue"},outputs:{selectedValues:"selectedValues"},features:[i["\u0275\u0275NgOnChangesFeature"]],decls:16,vars:6,consts:[["appearance","outline",1,"full-width"],["multiple","",3,"placeholder","formControl","openedChange"],["onScroll",""],[2,"width","100%"],[1,"pl-1","icon-search"],["matPrefix",""],["noEntriesFoundLabel","No results found",1,"p-3","pl-0",2,"width","80%",3,"formControl","placeholder","keydown"],[1,"icon-search-cancel",3,"click"],["matSuffix","",1,"mr-1"],[3,"click"],[3,"value",4,"ngFor","ngForOf"],[3,"value"],[4,"ngFor","ngForOf"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"mat-form-field",0),i["\u0275\u0275elementStart"](1,"mat-label"),i["\u0275\u0275text"](2),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](3,"mat-select",1,2),i["\u0275\u0275listener"]("openedChange",(function(e){return t.onOpenedChange(e,"onScroll")})),i["\u0275\u0275elementStart"](5,"div",3),i["\u0275\u0275elementStart"](6,"span",4),i["\u0275\u0275elementStart"](7,"mat-icon",5),i["\u0275\u0275text"](8,"search"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](9,"input",6),i["\u0275\u0275listener"]("keydown",(function(e){return e.stopPropagation()})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](10,"span",7),i["\u0275\u0275listener"]("click",(function(){return t.cancelSearchValue()})),i["\u0275\u0275elementStart"](11,"mat-icon",8),i["\u0275\u0275text"](12,"highlight_off"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](13,"mat-option",9),i["\u0275\u0275listener"]("click",(function(){return t.selectedValCtrl.reset()})),i["\u0275\u0275text"](14,"None"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](15,G,2,2,"mat-option",10),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate"](t.label),i["\u0275\u0275advance"](1),i["\u0275\u0275propertyInterpolate"]("placeholder",t.placeholder),i["\u0275\u0275property"]("formControl",t.selectedValCtrl),i["\u0275\u0275advance"](6),i["\u0275\u0275property"]("formControl",t.searchCtrl)("placeholder",t.placeholder?t.placeholder:""),i["\u0275\u0275advance"](6),i["\u0275\u0275property"]("ngForOf",t.list))},directives:[u.c,u.g,E.c,a.v,a.k,g.a,u.h,a.e,u.i,_.p,d.NgForOf],styles:[".full-width[_ngcontent-%COMP%]{width:100%;width:100%!important}input[_ngcontent-%COMP%], input[_ngcontent-%COMP%]:focus{border:none;outline:none;caret-color:#cf0001}.icon-search[_ngcontent-%COMP%], .icon-search-cancel[_ngcontent-%COMP%]{color:#a1a1a2;font-size:20px;vertical-align:middle}.icon-search-cancel[_ngcontent-%COMP%]{cursor:pointer}"]}),e})();var K=n("3vwd");function X(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",34),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const t=i["\u0275\u0275nextContext"]().index;return i["\u0275\u0275nextContext"](2).selectClaimType(t)})),i["\u0275\u0275elementStart"](1,"mat-icon",35),i["\u0275\u0275text"](2),i["\u0275\u0275elementEnd"](),i["\u0275\u0275text"](3),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"]().$implicit;i["\u0275\u0275propertyInterpolate"]("matTooltip",e.description),i["\u0275\u0275property"]("ngClass",e.active?"btn-active":"btn-not-active"),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"](" ",e.icon," "),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" ",e.name," ")}}function Q(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"span",32),i["\u0275\u0275template"](1,X,4,4,"button",33),i["\u0275\u0275elementEnd"]()),2&e){const e=t.index,n=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",e<n.showIndex)}}function Z(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div"),i["\u0275\u0275elementStart"](1,"button",36),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const t=i["\u0275\u0275nextContext"](2);return t.showIndex=16==t.showIndex?t.claimType.length:16})),i["\u0275\u0275text"](2),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate"](16==e.showIndex?"more...":"show less")}}function ee(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",11),i["\u0275\u0275elementStart"](1,"div",30),i["\u0275\u0275template"](2,Q,2,1,"span",31),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](3,Z,3,1,"div",15),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275property"]("@fadeInOut",void 0),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngForOf",e.claimType),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",e.claimType.length>16)}}function te(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",39),i["\u0275\u0275elementStart"](1,"div",47),i["\u0275\u0275element"](2,"app-expense-search-user",48),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("isAutocomplete",!0)("label",null!=e.fieldConfig&&null!=e.fieldConfig.claimedBy&&e.fieldConfig.claimedBy.field_label?e.fieldConfig.claimedBy.field_label:"Claimed By *")("readonly",e.isDisableClaimedBy)}}function ne(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",39),i["\u0275\u0275elementStart"](1,"div",47),i["\u0275\u0275elementStart"](2,"app-exp-input-search",49),i["\u0275\u0275listener"]("change",(function(t){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"](2).changeLegalEntity(t)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("placeholder",null!=e.fieldConfig&&null!=e.fieldConfig.legalEntityCode&&e.fieldConfig.legalEntityCode.field_label?e.fieldConfig.legalEntityCode.field_label:"Legal Entity")("list",e.legalEntityList)}}function le(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",50),i["\u0275\u0275elementStart"](1,"app-exp-input-search",51),i["\u0275\u0275listener"]("change",(function(t){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"](2).changeCostCenter(t)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("placeholder",null!=e.fieldConfig&&null!=e.fieldConfig.costCenterCode&&e.fieldConfig.costCenterCode.field_label?e.fieldConfig.costCenterCode.field_label:"Cost Center")("list",e.costCenterList)}}function ie(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",55),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](2,"div",55),i["\u0275\u0275text"](3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"div",55),i["\u0275\u0275text"](5),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]().$implicit;i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" ",e.name," "),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"](" ",e.role," "),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"](" Level ",e.level," ")}}function oe(e,t){if(1&e&&(i["\u0275\u0275elementContainerStart"](0),i["\u0275\u0275element"](1,"app-user-image",53),i["\u0275\u0275template"](2,ie,6,3,"ng-template",null,54,i["\u0275\u0275templateRefExtractor"]),i["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit,n=i["\u0275\u0275reference"](3);i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("tooltip",n)("id",e.oid)}}function ae(e,t){if(1&e&&(i["\u0275\u0275elementContainerStart"](0),i["\u0275\u0275template"](1,oe,4,2,"ng-container",52),i["\u0275\u0275elementContainerEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngForOf",e.claimForm.value.claims[0].approvers)}}function re(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",56),i["\u0275\u0275elementStart"](1,"mat-form-field",57),i["\u0275\u0275elementStart"](2,"mat-label"),i["\u0275\u0275text"](3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](4,"input",58),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](3),i["\u0275\u0275textInterpolate"](null!=e.fieldConfig&&null!=e.fieldConfig.department&&e.fieldConfig.department.field_label?e.fieldConfig.department.field_label:"Department *"),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("placeholder",null!=e.fieldConfig&&null!=e.fieldConfig.department&&e.fieldConfig.department.field_label?e.fieldConfig.department.field_label:"Department *")}}function se(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",56),i["\u0275\u0275elementStart"](1,"mat-form-field",57),i["\u0275\u0275elementStart"](2,"mat-label"),i["\u0275\u0275text"](3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](4,"input",59),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](3),i["\u0275\u0275textInterpolate"](null!=e.fieldConfig&&null!=e.fieldConfig.claimDate&&e.fieldConfig.claimDate.field_label?e.fieldConfig.claimDate.field_label:"Date of submission")}}const ce=function(){return["currency_code"]};function de(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",56),i["\u0275\u0275elementStart"](1,"app-input-search-huge-input",67),i["\u0275\u0275listener"]("ngModelChange",(function(){i["\u0275\u0275restoreView"](e);const t=i["\u0275\u0275nextContext"]().index;return i["\u0275\u0275nextContext"](2).itemCurrencyChange(t)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"](3);i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("label",null!=e.fieldConfig&&null!=e.fieldConfig.currency&&e.fieldConfig.currency.field_label?e.fieldConfig.currency.field_label:"Currency")("optionLabel",i["\u0275\u0275pureFunction0"](4,ce))("optionValue","currency_id")("apiUri","/api/exPrimary/currencyMasterData")}}function me(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",56),i["\u0275\u0275elementStart"](1,"mat-form-field",57),i["\u0275\u0275elementStart"](2,"mat-label"),i["\u0275\u0275text"](3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](4,"input",68),i["\u0275\u0275element"](5,"mat-datepicker-toggle",69),i["\u0275\u0275element"](6,"mat-datepicker",null,70),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275reference"](7),t=i["\u0275\u0275nextContext"](3);i["\u0275\u0275advance"](3),i["\u0275\u0275textInterpolate"](null!=t.fieldConfig&&null!=t.fieldConfig.invoiceDate&&t.fieldConfig.invoiceDate.field_label?t.fieldConfig.invoiceDate.field_label:"Billed Date"),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("matDatepicker",e)("min",t.billingStartDate)("max",t.tomorrow),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("for",e)}}function pe(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span"),i["\u0275\u0275text"](1," Amount can't be 0, -ve "),i["\u0275\u0275elementEnd"]())}function ue(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span"),i["\u0275\u0275text"](1," ,empty "),i["\u0275\u0275elementEnd"]())}function fe(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-hint",73),i["\u0275\u0275template"](1,pe,2,0,"span",15),i["\u0275\u0275template"](2,ue,2,0,"span",15),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](2).index,t=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",!t.claimForm.controls.claims.controls[e].controls.amount.errors.validAmount),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.claimForm.controls.claims.controls[e].controls.amount.errors.required)}}function ge(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",47),i["\u0275\u0275elementStart"](1,"mat-form-field",57),i["\u0275\u0275elementStart"](2,"mat-label"),i["\u0275\u0275text"](3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"input",71),i["\u0275\u0275listener"]("keyup",(function(){i["\u0275\u0275restoreView"](e);const t=i["\u0275\u0275nextContext"]().index;return i["\u0275\u0275nextContext"](2).itemAmountChange(t)}))("change",(function(){i["\u0275\u0275restoreView"](e);const t=i["\u0275\u0275nextContext"]().index;return i["\u0275\u0275nextContext"](2).itemAmountChange(t)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](5,fe,3,2,"mat-hint",72),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"]().index,t=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](3),i["\u0275\u0275textInterpolate"](null!=t.fieldConfig&&null!=t.fieldConfig.amount&&t.fieldConfig.amount.field_label?t.fieldConfig.amount.field_label:"Amount *"),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("placeholder",null!=t.fieldConfig&&null!=t.fieldConfig.amount&&t.fieldConfig.amount.field_label?t.fieldConfig.amount.field_label:"Amount"),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.claimForm.controls.claims.controls[e].controls.amount.invalid&&(t.claimForm.controls.claims.controls[e].controls.amount.touched||t.claimForm.controls.claims.controls[e].controls.amount.dirty))}}function he(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",47),i["\u0275\u0275elementStart"](1,"mat-form-field",57),i["\u0275\u0275elementStart"](2,"mat-label"),i["\u0275\u0275text"](3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](4,"input",74),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](3);i["\u0275\u0275advance"](3),i["\u0275\u0275textInterpolate"](null!=e.fieldConfig&&null!=e.fieldConfig.description&&e.fieldConfig.description.field_label?e.fieldConfig.description.field_label:"Description"),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("placeholder",null!=e.fieldConfig&&null!=e.fieldConfig.description&&e.fieldConfig.description.field_label?e.fieldConfig.description.field_label:"Description")}}function ve(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",75),i["\u0275\u0275elementStart"](1,"mat-form-field",76),i["\u0275\u0275elementStart"](2,"mat-label"),i["\u0275\u0275text"](3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"mat-date-range-input",77),i["\u0275\u0275element"](5,"input",78),i["\u0275\u0275element"](6,"input",79),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](7,"mat-datepicker-toggle",69),i["\u0275\u0275element"](8,"mat-date-range-picker",null,80),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275reference"](9),t=i["\u0275\u0275nextContext"](3);i["\u0275\u0275advance"](3),i["\u0275\u0275textInterpolate"](null!=t.fieldConfig&&null!=t.fieldConfig.startDate&&t.fieldConfig.startDate.field_label?t.fieldConfig.startDate.field_label:"Expense Duration *"),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("rangePicker",e)("min",t.billingStartDate),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("for",e)}}const Ce=function(){return["displayName","associate_id"]};function xe(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",81),i["\u0275\u0275elementStart"](1,"app-input-search-people-involved-dropdown",82),i["\u0275\u0275listener"]("selectedValues",(function(t){i["\u0275\u0275restoreView"](e);const n=i["\u0275\u0275nextContext"]().index;return i["\u0275\u0275nextContext"](2).getPeopleInvolved(t,n)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"]().index,t=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("label",null!=t.fieldConfig&&null!=t.fieldConfig.peopleInvolved&&t.fieldConfig.peopleInvolved.field_label?t.fieldConfig.peopleInvolved.field_label:"People Involved")("token",t.token)("optionLabel",i["\u0275\u0275pureFunction0"](6,Ce))("fieldValue",t.claimForm.controls.claims.controls[e].controls.peopleInvolved.value)("API_URL",t.employeeSearchUrl)("placeholder",null==t.fieldConfig||null==t.fieldConfig.peopleInvolved?null:t.fieldConfig.peopleInvolved.field_label)}}function ye(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",83),i["\u0275\u0275elementStart"](1,"div",84),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const n=t.$implicit,l=t.index,o=i["\u0275\u0275nextContext"]().index;return i["\u0275\u0275nextContext"](2).removeEmp(n,l,o)})),i["\u0275\u0275element"](2,"app-user-image",85),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;i["\u0275\u0275advance"](1),i["\u0275\u0275propertyInterpolate"]("matTooltip",e.displayName),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("id",e.id)}}function be(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",56),i["\u0275\u0275elementStart"](1,"mat-form-field",76),i["\u0275\u0275elementStart"](2,"mat-label"),i["\u0275\u0275text"](3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](4,"input",86),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](3);i["\u0275\u0275advance"](3),i["\u0275\u0275textInterpolate"](null!=e.fieldConfig&&null!=e.fieldConfig.invoiceNo&&e.fieldConfig.invoiceNo.field_label?e.fieldConfig.invoiceNo.field_label:"Invoice No. ")}}function _e(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-option",90),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;i["\u0275\u0275property"]("value",e.id),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](e.billing_type)}}function Ee(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-form-field",76),i["\u0275\u0275elementStart"](1,"mat-label"),i["\u0275\u0275text"](2),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](3,"mat-select",88),i["\u0275\u0275template"](4,_e,2,2,"mat-option",89),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](4);i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate"](null!=e.fieldConfig&&null!=e.fieldConfig.customerBilling&&e.fieldConfig.customerBilling.field_label?e.fieldConfig.customerBilling.field_label:"Customer Billing *"),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("formControl",e.claimForm.get("customerBilling")),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngForOf",e.billingConfig)}}function Fe(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",75),i["\u0275\u0275template"](1,Ee,5,3,"mat-form-field",87),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](3);i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",e.billingConfig)}}function Ie(e,t){if(1&e&&(i["\u0275\u0275elementContainerStart"](0,60),i["\u0275\u0275elementStart"](1,"div",39),i["\u0275\u0275template"](2,de,2,5,"div",44),i["\u0275\u0275template"](3,me,8,5,"div",44),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"div",39),i["\u0275\u0275template"](5,ge,6,3,"div",61),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](6,"div",11),i["\u0275\u0275template"](7,he,5,2,"div",61),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](8,"div",43),i["\u0275\u0275template"](9,ve,10,4,"div",62),i["\u0275\u0275elementStart"](10,"div",63),i["\u0275\u0275template"](11,xe,2,7,"div",64),i["\u0275\u0275elementStart"](12,"div",65),i["\u0275\u0275template"](13,ye,3,2,"div",66),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](14,"div",43),i["\u0275\u0275template"](15,be,5,1,"div",44),i["\u0275\u0275template"](16,Fe,2,1,"div",62),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.index,n=i["\u0275\u0275nextContext"](2);i["\u0275\u0275property"]("formGroupName",e),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf",null!=n.fieldConfig&&null!=n.fieldConfig.currency&&n.fieldConfig.currency.is_visible?null==n.fieldConfig||null==n.fieldConfig.currency?null:n.fieldConfig.currency.is_visible:0),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null!=n.fieldConfig&&null!=n.fieldConfig.invoiceDate&&n.fieldConfig.invoiceDate.is_visible?null==n.fieldConfig||null==n.fieldConfig.invoiceDate?null:n.fieldConfig.invoiceDate.is_visible:0),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf",null!=n.fieldConfig&&null!=n.fieldConfig.amount&&n.fieldConfig.amount.is_visible?null==n.fieldConfig||null==n.fieldConfig.amount?null:n.fieldConfig.amount.is_visible:0),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf",null!=n.fieldConfig&&null!=n.fieldConfig.description&&n.fieldConfig.description.is_visible?null==n.fieldConfig||null==n.fieldConfig.description?null:n.fieldConfig.description.is_visible:0),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf",null!=n.fieldConfig&&null!=n.fieldConfig.startDate&&n.fieldConfig.startDate.is_visible?null==n.fieldConfig||null==n.fieldConfig.startDate?null:n.fieldConfig.startDate.is_visible:0),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf",null!=n.fieldConfig&&null!=n.fieldConfig.peopleInvolved&&n.fieldConfig.peopleInvolved.is_visible?null==n.fieldConfig||null==n.fieldConfig.peopleInvolved?null:n.fieldConfig.peopleInvolved.is_visible:0),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngForOf",n.claimForm.controls.claims.controls[e].controls.peopleInvolved.value),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf",null!=n.fieldConfig&&null!=n.fieldConfig.invoiceNo&&n.fieldConfig.invoiceNo.is_visible?null==n.fieldConfig||null==n.fieldConfig.invoiceNo?null:n.fieldConfig.invoiceNo.is_visible:0),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null!=n.fieldConfig&&null!=n.fieldConfig.customerBilling&&n.fieldConfig.customerBilling.is_visible?null==n.fieldConfig||null==n.fieldConfig.customerBilling?null:n.fieldConfig.customerBilling.is_visible:0)}}function we(e,t){if(1&e&&(i["\u0275\u0275elementContainerStart"](0),i["\u0275\u0275elementStart"](1,"div",11),i["\u0275\u0275elementStart"](2,"div",37),i["\u0275\u0275template"](3,te,3,3,"div",38),i["\u0275\u0275template"](4,ne,3,2,"div",38),i["\u0275\u0275elementStart"](5,"div",39),i["\u0275\u0275template"](6,le,2,2,"div",40),i["\u0275\u0275elementStart"](7,"div",41),i["\u0275\u0275elementStart"](8,"p",42),i["\u0275\u0275text"](9,"Approvers"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](10,ae,2,1,"ng-container",15),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](11,"div",43),i["\u0275\u0275template"](12,re,5,2,"div",44),i["\u0275\u0275template"](13,se,5,1,"div",44),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementContainerStart"](14,45),i["\u0275\u0275template"](15,Ie,17,10,"ng-container",46),i["\u0275\u0275elementContainerEnd"](),i["\u0275\u0275element"](16,"div",39),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementContainerEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngIf",null!=e.fieldConfig&&null!=e.fieldConfig.claimedBy&&e.fieldConfig.claimedBy.is_visible?null==e.fieldConfig||null==e.fieldConfig.claimedBy?null:e.fieldConfig.claimedBy.is_visible:0),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null!=e.fieldConfig&&null!=e.fieldConfig.legalEntityCode&&e.fieldConfig.legalEntityCode.is_visible?null==e.fieldConfig||null==e.fieldConfig.legalEntityCode?null:e.fieldConfig.legalEntityCode.is_visible:0),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf",null!=e.fieldConfig&&null!=e.fieldConfig.costCenterCode&&e.fieldConfig.costCenterCode.is_visible?null==e.fieldConfig||null==e.fieldConfig.costCenterCode?null:e.fieldConfig.costCenterCode.is_visible:0),i["\u0275\u0275advance"](4),i["\u0275\u0275property"]("ngIf",e.claimForm.value.claims[0].approvers),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf",null!=e.fieldConfig&&null!=e.fieldConfig.department&&e.fieldConfig.department.is_visible?null==e.fieldConfig||null==e.fieldConfig.department?null:e.fieldConfig.department.is_visible:0),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null!=e.fieldConfig&&null!=e.fieldConfig.claimDate&&e.fieldConfig.claimDate.is_visible?null==e.fieldConfig||null==e.fieldConfig.claimDate?null:e.fieldConfig.claimDate.is_visible:0),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngForOf",e.claimFormData.claims.controls)}}function Se(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",39),i["\u0275\u0275elementStart"](1,"div",98),i["\u0275\u0275element"](2,"app-expense-search-user",48),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("isAutocomplete",!0)("label",null!=e.fieldConfig&&null!=e.fieldConfig.claimedBy&&e.fieldConfig.claimedBy.field_label?e.fieldConfig.claimedBy.field_label:"Claimed By *")("readonly",e.isDisableClaimedBy)}}function De(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",39),i["\u0275\u0275elementStart"](1,"div",99),i["\u0275\u0275elementStart"](2,"app-exp-input-search",51),i["\u0275\u0275listener"]("change",(function(t){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"](2).changeCostCenter(t)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("placeholder",null!=e.fieldConfig&&null!=e.fieldConfig.costCenterCode&&e.fieldConfig.costCenterCode.field_label?e.fieldConfig.costCenterCode.field_label:"Cost Center")("list",e.costCenterList)}}function Oe(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",39),i["\u0275\u0275elementStart"](1,"div",100),i["\u0275\u0275elementStart"](2,"mat-form-field",57),i["\u0275\u0275elementStart"](3,"mat-label"),i["\u0275\u0275text"](4),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](5,"input",59),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](4),i["\u0275\u0275textInterpolate"](null!=e.fieldConfig&&null!=e.fieldConfig.claimDate&&e.fieldConfig.claimDate.field_label?e.fieldConfig.claimDate.field_label:"Date of submission")}}function Ae(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",101),i["\u0275\u0275elementStart"](1,"div",102),i["\u0275\u0275elementStart"](2,"mat-form-field",57),i["\u0275\u0275elementStart"](3,"mat-label"),i["\u0275\u0275text"](4),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](5,"input",58),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](4),i["\u0275\u0275textInterpolate"](null!=e.fieldConfig&&null!=e.fieldConfig.department&&e.fieldConfig.department.field_label?e.fieldConfig.department.field_label:"Department *"),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("placeholder",null!=e.fieldConfig&&null!=e.fieldConfig.department&&e.fieldConfig.department.field_label?e.fieldConfig.department.field_label:"Department *")}}function Me(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",39),i["\u0275\u0275elementStart"](1,"div",47),i["\u0275\u0275elementStart"](2,"app-exp-input-search",49),i["\u0275\u0275listener"]("change",(function(t){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"](2).changeLegalEntity(t)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("placeholder",null!=e.fieldConfig&&null!=e.fieldConfig.legalEntityCode&&e.fieldConfig.legalEntityCode.field_label?e.fieldConfig.legalEntityCode.field_label:"Legal Entity")("list",e.legalEntityList)}}function Te(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-option",90),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;i["\u0275\u0275property"]("value",e.id),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](e.billing_type)}}function Pe(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-form-field",76),i["\u0275\u0275elementStart"](1,"mat-label"),i["\u0275\u0275text"](2),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](3,"mat-select",104),i["\u0275\u0275template"](4,Te,2,2,"mat-option",89),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](3);i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate"](null!=e.fieldConfig&&null!=e.fieldConfig.customerBilling&&e.fieldConfig.customerBilling.field_label?e.fieldConfig.customerBilling.field_label:"Customer Billing *"),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngForOf",e.billingConfig)}}function ke(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",39),i["\u0275\u0275elementStart"](1,"div",103),i["\u0275\u0275template"](2,Pe,5,2,"mat-form-field",87),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf",e.billingConfig)}}function Ve(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",119),i["\u0275\u0275elementStart"](1,"app-exp-input-search",120),i["\u0275\u0275listener"]("change",(function(t){i["\u0275\u0275restoreView"](e);const n=i["\u0275\u0275nextContext"]().index;return i["\u0275\u0275nextContext"](2).changeClaimType(t,n)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"](3);i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("list",e.claimType)}}function Le(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",121),i["\u0275\u0275elementStart"](1,"mat-form-field",57),i["\u0275\u0275elementStart"](2,"mat-label"),i["\u0275\u0275text"](3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](4,"input",74),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](3);i["\u0275\u0275advance"](3),i["\u0275\u0275textInterpolate"](null!=e.descriptionfieldConfig&&null!=e.descriptionfieldConfig.description&&e.descriptionfieldConfig.description.field_label?e.fieldConfig.description.field_label:"Description"),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("placeholder",null!=e.fieldConfig&&null!=e.fieldConfig.description&&e.fieldConfig.description.field_label?e.fieldConfig.description.field_label:"Description")}}function Be(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",121),i["\u0275\u0275elementStart"](1,"mat-form-field",57),i["\u0275\u0275elementStart"](2,"mat-label"),i["\u0275\u0275text"](3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](4,"input",122),i["\u0275\u0275element"](5,"mat-datepicker-toggle",69),i["\u0275\u0275element"](6,"mat-datepicker",null,70),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275reference"](7),t=i["\u0275\u0275nextContext"](3);i["\u0275\u0275advance"](3),i["\u0275\u0275textInterpolate"](null!=t.fieldConfig&&null!=t.fieldConfig.invoiceDate&&t.fieldConfig.invoiceDate.field_label?t.fieldConfig.invoiceDate.field_label:"Billed Date"),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("matDatepicker",e)("min",t.billingStartDate)("max",t.tomorrow),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("for",e)}}function Ne(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span"),i["\u0275\u0275text"](1," Amount can't be 0, -ve "),i["\u0275\u0275elementEnd"]())}function Re(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span"),i["\u0275\u0275text"](1," ,empty "),i["\u0275\u0275elementEnd"]())}function qe(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-hint",73),i["\u0275\u0275template"](1,Ne,2,0,"span",15),i["\u0275\u0275template"](2,Re,2,0,"span",15),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](2).index,t=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",!t.claimForm.controls.claims.controls[e].controls.amount.errors.validAmount),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.claimForm.controls.claims.controls[e].controls.amount.errors.required)}}function He(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",119),i["\u0275\u0275elementStart"](1,"mat-form-field",57),i["\u0275\u0275elementStart"](2,"mat-label"),i["\u0275\u0275text"](3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"input",123),i["\u0275\u0275listener"]("change",(function(){i["\u0275\u0275restoreView"](e);const t=i["\u0275\u0275nextContext"]().index;return i["\u0275\u0275nextContext"](2).itemAmountChange(t)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](5,qe,3,2,"mat-hint",72),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"]().index,t=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](3),i["\u0275\u0275textInterpolate"](null!=t.fieldConfig&&null!=t.fieldConfig.amount&&t.fieldConfig.amount.field_label?t.fieldConfig.amount.field_label:"Amount *"),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("placeholder",null!=t.fieldConfig&&null!=t.fieldConfig.amount&&t.fieldConfig.amount.field_label?t.fieldConfig.amount.field_label:"Amount *"),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.claimForm.controls.claims.controls[e].controls.amount.invalid&&(t.claimForm.controls.claims.controls[e].controls.amount.touched||t.claimForm.controls.claims.controls[e].controls.amount.dirty))}}function Ye(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",124),i["\u0275\u0275elementStart"](1,"app-input-search-huge-input",125),i["\u0275\u0275listener"]("change",(function(){i["\u0275\u0275restoreView"](e);const t=i["\u0275\u0275nextContext"]().index;return i["\u0275\u0275nextContext"](2).itemCurrencyChange(t)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"](3);i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("label",null!=e.fieldConfig&&null!=e.fieldConfig.currency&&e.fieldConfig.currency.field_label?e.fieldConfig.currency.field_label:"Currency")("optionLabel",i["\u0275\u0275pureFunction0"](4,ce))("optionValue","currency_id")("apiUri","/api/exPrimary/currencyMasterData")}}function je(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",55),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](2,"div",55),i["\u0275\u0275text"](3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"div",55),i["\u0275\u0275text"](5),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]().$implicit;i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" ",e.name," "),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"](" ",e.designation," "),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"](" Level ",e.level," ")}}function We(e,t){if(1&e&&(i["\u0275\u0275elementContainerStart"](0),i["\u0275\u0275element"](1,"app-user-image",53),i["\u0275\u0275template"](2,je,6,3,"ng-template",null,54,i["\u0275\u0275templateRefExtractor"]),i["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit,n=i["\u0275\u0275reference"](3);i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("tooltip",n)("id",e.oid)}}function $e(e,t){if(1&e&&(i["\u0275\u0275elementContainerStart"](0),i["\u0275\u0275elementStart"](1,"p",42),i["\u0275\u0275text"](2,"Approvers"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](3,We,4,2,"ng-container",52),i["\u0275\u0275elementContainerEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]().index,t=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngForOf",t.claimForm.value.claims[e].approvers)}}function Ue(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",126),i["\u0275\u0275elementStart"](1,"mat-form-field",76),i["\u0275\u0275elementStart"](2,"mat-label"),i["\u0275\u0275text"](3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"mat-date-range-input",77),i["\u0275\u0275element"](5,"input",78),i["\u0275\u0275element"](6,"input",79),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](7,"mat-datepicker-toggle",69),i["\u0275\u0275element"](8,"mat-date-range-picker",null,80),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275reference"](9),t=i["\u0275\u0275nextContext"](3);i["\u0275\u0275advance"](3),i["\u0275\u0275textInterpolate"](null!=t.fieldConfig&&null!=t.fieldConfig.startDate&&t.fieldConfig.startDate.field_label?t.fieldConfig.startDate.field_label:"Expense Duration *"),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("rangePicker",e)("min",t.billingStartDate),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("for",e)}}function ze(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",81),i["\u0275\u0275elementStart"](1,"app-input-search-people-involved-dropdown",127),i["\u0275\u0275listener"]("selectedValues",(function(t){i["\u0275\u0275restoreView"](e);const n=i["\u0275\u0275nextContext"]().index;return i["\u0275\u0275nextContext"](2).getPeopleInvolved(t,n)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"]().index,t=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("label",null!=t.fieldConfig&&null!=t.fieldConfig.peopleInvolved&&t.fieldConfig.peopleInvolved.field_label?t.fieldConfig.peopleInvolved.field_label:"People Involved")("token",t.token)("optionLabel",i["\u0275\u0275pureFunction0"](5,Ce))("API_URL",t.employeeSearchUrl)("fieldValue",t.claimForm.controls.claims.controls[e].controls.peopleInvolved.value)}}function Ge(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",83),i["\u0275\u0275elementStart"](1,"div",84),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const n=t.$implicit,l=t.index,o=i["\u0275\u0275nextContext"]().index;return i["\u0275\u0275nextContext"](2).removeEmp(n,l,o)})),i["\u0275\u0275element"](2,"app-user-image",85),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;i["\u0275\u0275advance"](1),i["\u0275\u0275propertyInterpolate"]("matTooltip",e.displayName),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("id",e.id)}}function Je(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",126),i["\u0275\u0275elementStart"](1,"mat-form-field",76),i["\u0275\u0275elementStart"](2,"mat-label"),i["\u0275\u0275text"](3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](4,"input",86),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](3);i["\u0275\u0275advance"](3),i["\u0275\u0275textInterpolate"](null!=e.fieldConfig&&null!=e.fieldConfig.invoiceNo&&e.fieldConfig.invoiceNo.field_label?e.fieldConfig.invoiceNo.field_label:"Invoice No")}}function Ke(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",128),i["\u0275\u0275elementStart"](1,"attachment-upload-btn",129),i["\u0275\u0275listener"]("change",(function(t){i["\u0275\u0275restoreView"](e);const n=i["\u0275\u0275nextContext"]().index;return i["\u0275\u0275nextContext"](2).changeInFiles(t,n)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"]().$implicit;i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("destinationBucket","kebs-expenses")("routingKey","expenses")("contextId",e.controls.contextId.value)("allowEdit",!0)}}function Xe(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"mat-icon",134),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275reference"](3).click()})),i["\u0275\u0275text"](1,"attachment "),i["\u0275\u0275elementStart"](2,"input",135,136),i["\u0275\u0275listener"]("change",(function(){i["\u0275\u0275restoreView"](e);const t=i["\u0275\u0275nextContext"](2).index;return i["\u0275\u0275nextContext"](2).onFileAdd(t)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"](4);i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("uploader",e.uploader)("accept",e.allowedMimeType.toString())}}function Qe(e,t){1&e&&i["\u0275\u0275element"](0,"mat-spinner",137)}function Ze(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"mat-icon",138),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275reference"](3).click()})),i["\u0275\u0275text"](1,"edit "),i["\u0275\u0275elementStart"](2,"input",135,136),i["\u0275\u0275listener"]("change",(function(){i["\u0275\u0275restoreView"](e);const t=i["\u0275\u0275nextContext"](2).index;return i["\u0275\u0275nextContext"](2).onFileAdd(t)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"](4);i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("uploader",e.uploader)("accept",e.allowedMimeType.toString())}}function et(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",128),i["\u0275\u0275template"](1,Xe,4,2,"mat-icon",130),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](2,Qe,1,0,"mat-spinner",131),i["\u0275\u0275elementStart"](3,"span",132),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const t=i["\u0275\u0275nextContext"]().index;return i["\u0275\u0275nextContext"](2).openFile(t)})),i["\u0275\u0275text"](4),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](5,Ze,4,2,"mat-icon",133)}if(2&e){const e=i["\u0275\u0275nextContext"]().$implicit;i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",!e.controls.uploadInProgress.value&&!e.controls.attachment.value[0]),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",e.controls.uploadInProgress.value),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("matTooltip",null==e.controls.attachment.value[0]?null:e.controls.attachment.value[0].fileName),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" ",null==e.controls.attachment.value[0]?null:e.controls.attachment.value[0].fileName,""),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",""!=(null==e.controls.attachment.value[0]?null:e.controls.attachment.value[0].fileName)&&null!=(null==e.controls.attachment.value[0]?null:e.controls.attachment.value[0].fileName))}}const tt=function(e,t){return{"invalid-attachment":e,"attachment-btn":t}};function nt(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",105),i["\u0275\u0275template"](1,Ve,2,1,"div",106),i["\u0275\u0275template"](2,Le,5,2,"div",107),i["\u0275\u0275template"](3,Be,8,5,"div",107),i["\u0275\u0275template"](4,He,6,3,"div",106),i["\u0275\u0275template"](5,Ye,2,5,"div",108),i["\u0275\u0275elementStart"](6,"div",109),i["\u0275\u0275template"](7,$e,4,1,"ng-container",15),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](8,Ue,10,4,"div",110),i["\u0275\u0275elementStart"](9,"div",111),i["\u0275\u0275template"](10,ze,2,6,"div",64),i["\u0275\u0275elementStart"](11,"div",65),i["\u0275\u0275template"](12,Ge,3,2,"div",66),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](13,Je,5,1,"div",110),i["\u0275\u0275element"](14,"div",19),i["\u0275\u0275elementStart"](15,"div",112),i["\u0275\u0275elementStart"](16,"div",113),i["\u0275\u0275elementStart"](17,"div",114),i["\u0275\u0275elementStart"](18,"div",115),i["\u0275\u0275text"](19),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](20,Ke,2,4,"div",116),i["\u0275\u0275template"](21,et,6,5,"ng-template",null,117,i["\u0275\u0275templateRefExtractor"]),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](23,"button",118),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"](2).addClaim()})),i["\u0275\u0275elementStart"](24,"mat-icon"),i["\u0275\u0275text"](25,"add_circle"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](26,"button",118),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const n=t.index;return i["\u0275\u0275nextContext"](2).deleteClaim(n)})),i["\u0275\u0275elementStart"](27,"mat-icon"),i["\u0275\u0275text"](28,"remove_circle"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=t.index,l=i["\u0275\u0275reference"](22),o=i["\u0275\u0275nextContext"](2);i["\u0275\u0275property"]("formGroupName",n),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null!=o.fieldConfig&&null!=o.fieldConfig.category&&o.fieldConfig.category.is_visible?null==o.fieldConfig||null==o.fieldConfig.category?null:o.fieldConfig.category.is_visible:0),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null!=o.fieldConfig&&null!=o.fieldConfig.description&&o.fieldConfig.description.is_visible?null==o.fieldConfig||null==o.fieldConfig.description?null:o.fieldConfig.description.is_visible:0),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null!=o.fieldConfig&&null!=o.fieldConfig.invoiceDate&&o.fieldConfig.invoiceDate.is_visible?null==o.fieldConfig||null==o.fieldConfig.invoiceDate?null:o.fieldConfig.invoiceDate.is_visible:0),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null!=o.fieldConfig&&null!=o.fieldConfig.amount&&o.fieldConfig.amount.is_visible?null==o.fieldConfig||null==o.fieldConfig.amount?null:o.fieldConfig.amount.is_visible:0),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null!=o.fieldConfig&&null!=o.fieldConfig.currency&&o.fieldConfig.currency.is_visible?null==o.fieldConfig||null==o.fieldConfig.currency?null:o.fieldConfig.currency.is_visible:0),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf",""!=e.approvers),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null!=o.fieldConfig&&null!=o.fieldConfig.startDate&&o.fieldConfig.startDate.is_visible?null==o.fieldConfig||null==o.fieldConfig.startDate?null:o.fieldConfig.startDate.is_visible:0),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf",null!=o.fieldConfig&&null!=o.fieldConfig.peopleInvolved&&o.fieldConfig.peopleInvolved.is_visible?null==o.fieldConfig||null==o.fieldConfig.peopleInvolved?null:o.fieldConfig.peopleInvolved.is_visible:0),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngForOf",o.claimForm.controls.claims.controls[n].controls.peopleInvolved.value),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null!=o.fieldConfig&&null!=o.fieldConfig.invoiceNo&&o.fieldConfig.invoiceNo.is_visible?null==o.fieldConfig||null==o.fieldConfig.invoiceNo?null:o.fieldConfig.invoiceNo.is_visible:0),i["\u0275\u0275advance"](4),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction2"](16,tt,null==o.claimForm.controls.claims.controls[n].controls.contextId.errors?null:o.claimForm.controls.claims.controls[n].controls.contextId.errors.required,!(null!=o.claimForm.controls.claims.controls[n].controls.contextId.errors&&o.claimForm.controls.claims.controls[n].controls.contextId.errors.required))),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("matTooltip",o.getAttahmentAlertMessage(e.controls.category.value)),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](o.getAttahmentAlertMessage(e.controls.category.value)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",(null==e||null==e.controls||null==e.controls.contextId?null:e.controls.contextId.value)||null==o.draftInfo||!o.draftInfo.claims[n]||!(null!=e&&null!=e.controls&&null!=e.controls.contextId&&e.controls.contextId.value)&&!(null!=e&&null!=e.controls&&null!=e.controls.attachment&&e.controls.attachment.value))("ngIfElse",l)}}function lt(e,t){if(1&e&&(i["\u0275\u0275elementContainerStart"](0),i["\u0275\u0275elementStart"](1,"div",91),i["\u0275\u0275elementStart"](2,"div",92),i["\u0275\u0275template"](3,Se,3,3,"div",38),i["\u0275\u0275template"](4,De,3,2,"div",38),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"div",93),i["\u0275\u0275template"](6,Oe,6,1,"div",38),i["\u0275\u0275template"](7,Ae,6,2,"div",94),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](8,"div",95),i["\u0275\u0275template"](9,Me,3,2,"div",38),i["\u0275\u0275template"](10,ke,3,1,"div",38),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](11,"div",11),i["\u0275\u0275elementStart"](12,"div",37),i["\u0275\u0275elementContainerStart"](13,45),i["\u0275\u0275elementStart"](14,"div",96),i["\u0275\u0275template"](15,nt,29,19,"div",97),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementContainerEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementContainerEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngIf",null!=e.fieldConfig&&null!=e.fieldConfig.claimedBy&&e.fieldConfig.claimedBy.is_visible?null==e.fieldConfig||null==e.fieldConfig.claimedBy?null:e.fieldConfig.claimedBy.is_visible:0),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null!=e.fieldConfig&&null!=e.fieldConfig.costCenterCode&&e.fieldConfig.costCenterCode.is_visible?null==e.fieldConfig||null==e.fieldConfig.costCenterCode?null:e.fieldConfig.costCenterCode.is_visible:0),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf",null!=e.fieldConfig&&null!=e.fieldConfig.claimDate&&e.fieldConfig.claimDate.is_visible?null==e.fieldConfig||null==e.fieldConfig.claimDate?null:e.fieldConfig.claimDate.is_visible:0),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null!=e.fieldConfig&&null!=e.fieldConfig.department&&e.fieldConfig.department.is_visible?null==e.fieldConfig||null==e.fieldConfig.department?null:e.fieldConfig.department.is_visible:0),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf",null!=e.fieldConfig&&null!=e.fieldConfig.legalEntityCode&&e.fieldConfig.legalEntityCode.is_visible?null==e.fieldConfig||null==e.fieldConfig.legalEntityCode?null:e.fieldConfig.legalEntityCode.is_visible:0),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null!=e.fieldConfig&&null!=e.fieldConfig.customerBilling&&e.fieldConfig.customerBilling.is_visible?null==e.fieldConfig||null==e.fieldConfig.customerBilling?null:e.fieldConfig.customerBilling.is_visible:0),i["\u0275\u0275advance"](5),i["\u0275\u0275property"]("ngForOf",e.claimFormData.claims.controls)}}function it(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",143),i["\u0275\u0275elementStart"](1,"mat-icon",144),i["\u0275\u0275text"](2,"error_outline"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](3,"span",145),i["\u0275\u0275text"](4),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;i["\u0275\u0275advance"](4),i["\u0275\u0275textInterpolate1"](" ",e," ")}}function ot(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",141),i["\u0275\u0275template"](1,it,5,1,"div",142),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngForOf",e.warningMessages)}}function at(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",139),i["\u0275\u0275template"](1,ot,2,1,"div",140),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",e.warningMessages)}}function rt(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",148),i["\u0275\u0275elementStart"](1,"button",149),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275reference"](5).click()})),i["\u0275\u0275elementStart"](2,"mat-icon"),i["\u0275\u0275text"](3,"attachment"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"input",135,150),i["\u0275\u0275listener"]("change",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"](2).onFileAdd(!1)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](4),i["\u0275\u0275property"]("uploader",e.uploader)("accept",e.allowedMimeType.toString())}}function st(e,t){1&e&&i["\u0275\u0275element"](0,"mat-spinner",152)}function ct(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div"),i["\u0275\u0275template"](1,st,1,0,"mat-spinner",151),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",e.claimFormData.claims.controls[0].controls.uploadInProgress.value)}}function dt(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"span",157),i["\u0275\u0275element"](1,"i",158),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](4);i["\u0275\u0275advance"](1),i["\u0275\u0275classMap"](e.fileType[null==e.claimFormData.claims.controls[0].controls.attachment.value[0]?null:e.claimFormData.claims.controls[0].controls.attachment.value[0].type]||"ms-Icon ms-Icon--FileTemplate")}}function mt(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementContainerStart"](0,154),i["\u0275\u0275template"](1,dt,2,2,"span",155),i["\u0275\u0275elementStart"](2,"span",156),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"](3).openFile(0)})),i["\u0275\u0275text"](3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"](3);i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",e.claimFormData.claims.controls[0].controls.attachment.value[0]),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"](" ",null==e.claimFormData.claims.controls[0].controls.attachment.value[0]?null:e.claimFormData.claims.controls[0].controls.attachment.value[0].fileName," ")}}function pt(e,t){if(1&e&&(i["\u0275\u0275elementContainerStart"](0),i["\u0275\u0275template"](1,mt,4,2,"ng-container",153),i["\u0275\u0275elementContainerEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",""!=e.claimForm.value.claims[0].attachment&&null!=e.claimForm.value.claims[0].attachment)}}function ut(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",146),i["\u0275\u0275template"](1,rt,6,2,"div",147),i["\u0275\u0275template"](2,ct,2,1,"div",15),i["\u0275\u0275template"](3,pt,2,1,"ng-container",15),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf","singleExpenseCreation"==e.currentlyActiveFormType),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf","singleExpenseCreation"==e.currentlyActiveFormType),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",e.claimForm.value.claims)}}function ft(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div"),i["\u0275\u0275elementStart"](1,"div",160),i["\u0275\u0275elementStart"](2,"div",161),i["\u0275\u0275text"](3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"div",162),i["\u0275\u0275elementStart"](5,"attachment-upload-btn",129),i["\u0275\u0275listener"]("change",(function(t){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"](2).changeInAttachment(t)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction2"](7,tt,null==e.claimForm.controls.claims.controls[0].controls.contextId.errors?null:e.claimForm.controls.claims.controls[0].controls.contextId.errors.required,!(null!=e.claimForm.controls.claims.controls[0].controls.contextId.errors&&e.claimForm.controls.claims.controls[0].controls.contextId.errors.required))),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("matTooltip",e.getAttahmentAlertMessage(e.claimForm.controls.claims.controls[0].controls.category.value)),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](e.getAttahmentAlertMessage(e.claimForm.controls.claims.controls[0].controls.category.value)),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("destinationBucket","kebs-expenses")("routingKey","expenses")("contextId",e.claimForm.controls.claims.controls[0].controls.contextId.value)("allowEdit",!0)}}function gt(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div"),i["\u0275\u0275elementStart"](1,"div",160),i["\u0275\u0275elementStart"](2,"div",161),i["\u0275\u0275text"](3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"div",162),i["\u0275\u0275elementStart"](5,"attachment-upload-btn",129),i["\u0275\u0275listener"]("change",(function(t){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"](3).changeInAttachment(t)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"](3);i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction2"](7,tt,null==e.claimForm.controls.claims.controls[0].controls.contextId.errors?null:e.claimForm.controls.claims.controls[0].controls.contextId.errors.required,!(null!=e.claimForm.controls.claims.controls[0].controls.contextId.errors&&e.claimForm.controls.claims.controls[0].controls.contextId.errors.required))),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("matTooltip",e.getAttahmentAlertMessage(e.claimForm.controls.claims.controls[0].controls.category.value)),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](e.getAttahmentAlertMessage(e.claimForm.controls.claims.controls[0].controls.category.value)),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("destinationBucket","kebs-expenses")("routingKey","expenses")("contextId",e.claimForm.controls.claims.controls[0].controls.contextId.value)("allowEdit",!0)}}function ht(e,t){if(1&e&&i["\u0275\u0275template"](0,gt,6,10,"div",15),2&e){const e=i["\u0275\u0275nextContext"](2);i["\u0275\u0275property"]("ngIf","singleExpenseCreation"==e.currentlyActiveFormType)}}function vt(e,t){if(1&e&&(i["\u0275\u0275template"](0,ft,6,10,"div",25),i["\u0275\u0275template"](1,ht,1,1,"ng-template",null,159,i["\u0275\u0275templateRefExtractor"])),2&e){const e=i["\u0275\u0275reference"](2),t=i["\u0275\u0275nextContext"]();i["\u0275\u0275property"]("ngIf","singleExpenseCreation"==t.currentlyActiveFormType&&t.claimForm.controls.claims.controls[0].controls.contextId.value)("ngIfElse",e)}}function Ct(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"mat-icon"),i["\u0275\u0275text"](1," save"),i["\u0275\u0275elementEnd"]())}function xt(e,t){1&e&&i["\u0275\u0275element"](0,"mat-spinner",163)}function yt(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"mat-icon"),i["\u0275\u0275text"](1," done_all"),i["\u0275\u0275elementEnd"]())}function bt(e,t){1&e&&i["\u0275\u0275element"](0,"mat-spinner",163)}const _t=function(e){return{"background-color":e}},Et=function(e){return{"restict-cursor":e}};let Ft=(()=>{class e{constructor(e,t,r,d,p,u,f,g,h,v,C,x){this.fb=e,this._expHomeService=t,this._auth=r,this._wfService=d,this._util=p,this._fileSaver=u,this.dialog=f,this.dialogRef=g,this.draftInfo=h,this._expenseSharedService=v,this._snackBar=C,this.spinnerService=x,this.claimType=[],this.warningMessages=[],this.currentlyActiveFormType="singleExpenseCreation",this.defaultCurrency='{"currency_id":1,"currency_code":"INR","currency_description":"Indian Rupee"}',this.enableWarning=!1,this.isBeingDrafted=!1,this.isBeingClaimed=!1,this.isBeingWorkflowDetermined=!1,this.costCenter=null,this.showIndex=16,this.fieldConfig={},this.attachment_needed_or_not=""==this._expHomeService.getLabelForExpense(65,9)?"Attachments not Mandatory":this._expHomeService.getLabelForExpense(65,9),this.checkForDropDown=""==this._expHomeService.checkIfWeNeedDropdown(65,10)?"Use search in Expense":this._expHomeService.checkIfWeNeedDropdown(65,10),this.imgArr=[],this.costCenterList=[],this.contextId=[],this.deletedContextId=[],this.close=new i.EventEmitter,this.maxFileSize=10485760,this.fileType=m.a.fileuploadedSupportedFileTypes,this.currentFileIndex=0,this.tomorrow=c().format("YYYY-MM-DD"),this.employeeSearchUrl=window.location.origin+"/api/okr/objective/searchEmployee",this.token=this._auth.getToken(),this.selectedPeopleInvolvedList=[],this.selectedPeopleInvolvedId=[],this.legalEntityList=[],this.isDefaultCostCenter=!1,this.isDisableClaimedBy=!1,this.uploadFileAttachment=()=>{this.allowedMimeType=["*/*"],this.uploader.onProgressItem=e=>{this.claimForm.controls.claims.controls["singleExpenseCreation"==this.currentlyActiveFormType?0:this.currentFileIndex].controls.uploadInProgress.patchValue(!0)},this.uploader.onCompleteItem=(e,t,n,l)=>{if(this.claimForm.controls.claims.controls["singleExpenseCreation"==this.currentlyActiveFormType?0:this.currentFileIndex].controls.uploadInProgress.patchValue(!1),200==n&&t.length>0&&null!=t){let e=JSON.parse(t),n=[];n=n.concat(e.files_json),this.claimForm.controls.claims.controls["singleExpenseCreation"==this.currentlyActiveFormType?0:this.currentFileIndex].controls.attachment.patchValue(n),this.uploader.clearQueue()}else this._util.showMessage("Unable to upload","Dismiss",3e3)}},this.getDepartmentMaster=e=>{this._expHomeService.getDepartmentMaster(e).subscribe(e=>Object(l.c)(this,void 0,void 0,(function*(){e&&1==e.length&&this.claimForm.get("department").patchValue(e[0].department_name)})),e=>{console.error(e)})},this.getWorkFlowProperty=()=>new Promise((e,t)=>{this._expHomeService.getExpenseWorkFlowProperties().subscribe(t=>{console.log("getWorkflowProperty response"),console.log(t),e(t)},e=>{console.error(e),t(e)})}),this.getWorkFlowPropertyForDelivery=(e,t)=>new Promise((n,l)=>{this._expHomeService.getExpenseWorkFlowPropertiesOtherTypes(e,t).subscribe(e=>{console.log("getWorkflowProperty response Deivery"),console.log(e),n(e)},e=>{console.error(e),l(e)})}),this.getExpenseClaimWorkflows=()=>new Promise((e,t)=>{this._expHomeService.getExpenseClaimWorkFlowProperties().subscribe(t=>{console.log("getExpenseClaimWorkflows"),console.log(t),"S"==t.messType&&e(t.data)},e=>{console.log(e),t(e)})}),this.getExpenseConfig=()=>new Promise((e,t)=>{this._expHomeService.getExpenseConfig().subscribe(t=>{if(t.data){if(null!=t.data){let e=JSON.parse(t.data[0].expense_application_config);null!=e&&(e.allowed_no_of_billed_days&&0!=e.allowed_no_of_billed_days&&(this.billingStartDate=c().subtract(e.allowed_no_of_billed_days,"days")),1==e.is_disable_claimedby&&(this.isDisableClaimedBy=!0))}e(t.data)}},e=>{console.log(e),t(e)})}),this.detectValueChanges=()=>{this.claimForm.get("claimedBy").valueChanges.subscribe(e=>{this.claimedBy=e,1==this.isDefaultCostCenter&&(this.draftInfo||this.getCostCenterOfClaimer()),this.costCenter&&this.determineWorkflow()}),this.claimForm.get("costCenter").valueChanges.subscribe(e=>Object(l.c)(this,void 0,void 0,(function*(){if(console.log("Approver workflow triggered"),this.costCenter=""!=e?e:null,this.costCenter){let e=this.costCenter?"string"==typeof this.costCenter?JSON.parse(this.costCenter).cost_centre:this.costCenter.cost_centre:"";yield this.getDepartmentMaster(e),this.determineWorkflow()}}))),this.claimForm.get("costCenterCode").valueChanges.subscribe(e=>Object(l.c)(this,void 0,void 0,(function*(){if(console.log(e),console.log("Approver workflow triggered"),this.costCenter=""!=e?e:null,this.costCenter){let e=this.costCenter?"string"==typeof this.costCenter?JSON.parse(this.costCenter).cost_centre:this.costCenter.cost_centre:"";yield this.getDepartmentMaster(e),this.determineWorkflow()}}))),this.claimForm.get("legalEntity").valueChanges.subscribe(e=>Object(l.c)(this,void 0,void 0,(function*(){console.log("legal ENtity"),console.log(e),null!=e&&""!=e&&this.determineWorkflow()}))),this.claimForm.get("legalEntityCode").valueChanges.subscribe(e=>Object(l.c)(this,void 0,void 0,(function*(){null!=e&&""!=e&&this.determineWorkflow()})))},this.itemAmountChange=e=>{console.log("itemAmountChange"),console.log(e),this.determineWorkflow()},this.itemCurrencyChange=e=>{console.log("itemCurrencyChange"),console.log(e),this.determineWorkflow()},this.getAdminApproversHierarchy=(e,t,n)=>Object(l.c)(this,void 0,void 0,(function*(){let l=this.claimForm.value.costCenter;console.log(t);let i="string"==typeof l?JSON.parse(l):l;this.getDepartmentMaster(i.cost_centre),console.log("assigned property"),console.log(e);let o={workflowId:e.id,sortByDesignation:e.sort_by_desgn,initiatorOId:this.claimedBy,approvalType:"P",approvalParams:n,costCentresAndTypes:[{costCentre:i.cost_centre,costCentreType:i.cost_centre_type}]};return console.log("Expense hierarchyParams"),console.log(o),new Promise((e,t)=>{this._expHomeService.getExpenseApproversHierarchy(o).subscribe(n=>{let l=n;console.log("Approvers Hierarchy"),console.log(l),this._wfService.formatApproversHierarchy([i],l.data).then(t=>{console.log("Claim approvers"),console.log(t),e(t)},e=>{t(e),console.error(e)})},e=>{t(e),console.error(e)})})})),this.getExpenseApproversHierarchy=e=>Object(l.c)(this,void 0,void 0,(function*(){let t=this.claimForm.value.costCenter,n="string"==typeof t?JSON.parse(t):t;this.getDepartmentMaster(n.cost_centre),console.log("assigned property"),console.log(e);let l={workflowId:e.id,sortByDesignation:e.sort_by_desgn,initiatorOId:this.claimedBy,approvalType:e.approval_type,approvalParams:e.approval_params,costCentresAndTypes:[{costCentre:n.cost_centre,costCentreType:n.cost_centre_type}]};return console.log("Expense hierarchyParams"),console.log(l),new Promise((e,t)=>{this._expHomeService.getExpenseApproversHierarchy(l).subscribe(l=>{let i=l;console.log("Approvers Hierarchy"),console.log(i),this._wfService.formatApproversHierarchy([n],i.data).then(t=>{console.log("Claim approvers"),console.log(t),e(t)},e=>{t(e),console.error(e)})},e=>{t(e),console.error(e)})})})),this.getClaimCategoryMaster=()=>{this._expHomeService.getClaimCategory().subscribe(e=>{this.claimType=e.data.map(e=>({active:!1,id:e.id,code:e.code,icon:e.mat_icon.toLowerCase(),name:e.name,description:e.description,warningMsg:e.warning_msg,alertMsg:e.alert_msg,billable_attachment_warning_msg:e.billable_attachment_warning_msg,non_billable_attachment_warning_msg:e.non_billable_attachment_warning_msg})),this.refreshButtons(),this.draftInfo&&this.ngOnChanges()},e=>{console.log(e)})},this.getAdminApprovers=(e,t)=>new Promise((n,l)=>{this._expHomeService.getAdminApproversAID(e,t).subscribe(e=>{"S"==e.messType&&n(e.data)},e=>{console.log(e),l(e)})}),this.createForm=()=>Object(l.c)(this,void 0,void 0,(function*(){this.claimForm=this.fb.group({expenseType:["C"],claimedBy:[this._auth.getProfile().profile.oid,a.H.required],originalInitiatorOId:[this._auth.getProfile().profile.oid,a.H.required],legalEntity:["",a.H.required],department:["",a.H.required],costCenter:["",a.H.required],claimDate:[c().format("DD-MM-YYYY"),a.H.required],claims:this.fb.array([this.setClaimArray()]),costCenterCode:["",a.H.required],legalEntityCode:["",a.H.required],customerBilling:[0,a.H.required]}),this.detectClaimFormChange()})),this.setClaimArray=()=>this.fb.group({categoryCode:["",a.H.required],category:["",a.H.required],categoryName:["",a.H.required],amount:["",[a.H.required,a.H.min(0),a.H.pattern("^[0-9]+(.[0-9]{0,9})?$"),It]],currency:[this.defaultCurrency,a.H.required],invoiceDate:[c().format("YYYY-MM-DD"),a.H.required],description:["",a.H.required],approvers:["",a.H.required],workflowId:["",a.H.required],isAggregationAllowed:[""],attachment:[""],uploadInProgress:[!1],startDate:[c().format("YYYY-MM-DD")],endDate:[c().format("YYYY-MM-DD")],peopleInvolved:[""],contextId:[null,a.H.required],invoiceNo:[""]}),this.detectClaimFormChange=()=>{this.claimFormData.claims.valueChanges.subscribe(e=>{setTimeout(()=>{},700)})},this.determineWorkflow=()=>{console.log("determineWorkfow");let e=this.claimForm.get("claims").value,t=this.claimForm.get("legalEntity").value;this.determineWorkflowSubscription&&this.determineWorkflowSubscription.unsubscribe();let n=o.filter(e,e=>{if(e.amount&&""!=e.amount&&e.currency&&""!=e.currency&&""!=e.categoryCode)return!0});if(console.log(t),e.length==n.length&&this.costCenter&&""!=t&&null!=t){console.log("Claims Value filled");for(let[t,n]of e.entries())this.claimForm.controls.claims.controls[t].controls.workflowId.reset(),this.claimForm.controls.claims.controls[t].controls.approvers.reset();this.isBeingWorkflowDetermined=!0,this.expWorkflowProperties&&(this.determineWorkflowSubscription=this._expHomeService.determineWfPropertyForEachItemClaim(e,t,this.expWorkflowProperties,this.costCenter.cost_centre).subscribe(e=>{console.log("Expense Claim Item Level Approvers !"),console.log(e),this.isBeingWorkflowDetermined=!1,"S"==e.messType?this.getApproversAndFormat(e):"E"==e.messType&&(console.log(e.messText),this.isBeingWorkflowDetermined=!1,this._util.showMessage(e.messText,"close"))},e=>{this.isBeingWorkflowDetermined=!1,console.error(e)}))}},this.getApproversAndFormat=e=>Object(l.c)(this,void 0,void 0,(function*(){let t=e.data;1==e.workflowNotFound&&this._util.showMessage(e.messText,"Dismiss",3e3);for(let[e,n]of t.entries())if(n.wfProperty){let t=this.claimForm.value.costCenter,l="string"==typeof t?JSON.parse(t):t,i=yield this.getAdminApprovers(n.category,l.cost_centre),o=[];null!=i&&(o=yield this.getAdminApproversHierarchy(n.wfProperty,n.category,i));let a=yield this.getExpenseApproversHierarchy(n.wfProperty);o&&o.length>0&&(a=o.concat(a)),a&&a.length>0&&(this.claimForm.controls.claims.controls[e].controls.workflowId.patchValue(n.workflowId),this.claimForm.controls.claims.controls[e].controls.approvers.patchValue(a),this.claimForm.controls.claims.controls[e].controls.isAggregationAllowed.patchValue(n.wfProperty.aggregation_allowed))}})),this.detectAmountChangesInClaimArray=()=>{this.claimFormData.claims.valueChanges.subscribe(e=>{setTimeout(()=>{let e=this.claimForm.value.claims.map(e=>e.amount),t=0;e.forEach(e=>{""!=e&&(t+=parseFloat(e))}),this.claimForm.get("totalAmount").patchValue(t)},500)})},this.addClaim=(e,t,n)=>{this.claimFormData.claims.push(this.fb.group({categoryCode:[t,a.H.required],category:[e,a.H.required],categoryName:[n,a.H.required],amount:["",[a.H.required,a.H.pattern("^[0-9]+(.[0-9]{0,9})?$"),It]],currency:[this.defaultCurrency,a.H.required],invoiceDate:[c().format("YYYY-MM-DD"),a.H.required],description:["",a.H.required],attachment:[""],approvers:["",a.H.required],workflowId:[""],isAggregationAllowed:["",a.H.required],uploadInProgress:[!1],startDate:[c().format("YYYY-MM-DD")],endDate:[c().format("YYYY-MM-DD")],peopleInvolved:[""],contextId:[null,a.H.required],invoiceNo:[""]}))},this.deleteClaim=e=>{var t,n;const l=this.claimFormData.claims;l.length>1?(null!=this.claimFormData.claims.value[e].contextId&&this.deletedContextId.push(null===(n=null===(t=this.claimFormData)||void 0===t?void 0:t.claims)||void 0===n?void 0:n.value[e].contextId),l.removeAt(e),this.determineWorkflow()):this._util.showMessage("You should have alteast one expense item!","dismiss")},this.deleteQuickClaim=e=>{console.log(e);const t=this.claimFormData.claims;let n=this.claimForm.value.claims.map(e=>e.category).indexOf(e);t.removeAt(n)},this.selectClaimType=e=>{let t=this.claimForm.value.claims[0].category;""==t||null==t?(this.claimForm.controls.claims.controls[0].controls.category.patchValue(this.claimType[e].id),this.claimForm.controls.claims.controls[0].controls.categoryCode.patchValue(this.claimType[e].code),this.claimForm.controls.claims.controls[0].controls.categoryName.patchValue(this.claimType[e].name)):(this.currentlyActiveFormType="multipleExpenseCreation",this.addClaim(this.claimType[e].id,this.claimType[e].code,this.claimType[e].name)),this.determineWorkflow()},this.generateWarning=()=>{this.enableWarning=!1,this.claimForm.value.claims.forEach((e,t)=>{"Certifications"==e.categoryName&&(this.enableWarning=!0,this.claimType.forEach(e=>{console.log(e),"Certifications"==e.name&&(console.log(e.warningMsg),this.warningMessages.length>0&&this.warningMessages.pop(),this.warningMessages.push(e.warningMsg))}))})},this.closeForm=()=>{if(null!=this.draftInfo&&this.draftInfo.claims){let e=o.pluck(this.draftInfo.claims,"contextId"),t=o.difference(this.contextId,e);t&&t.length>0&&this._expHomeService.deleteContextIds(t)}else this.contextId&&this.contextId.length>0&&this._expHomeService.deleteContextIds(this.contextId);this.dialogRef.close(),this.resetForm(),this.close.emit("close")},this.saveExpense=()=>{var e;if(console.log(this.attachment_needed_or_not),"Attachments Mandatory"==this.attachment_needed_or_not)if(console.log(this.currentlyActiveFormType),"singleExpenseCreation"==this.currentlyActiveFormType){if(1==(""==this.claimForm.value.claims[0].attachment||null==this.claimForm.value.claims[0].attachment)&&1==(""==this.claimForm.value.claims[0].contextId||null==this.claimForm.value.claims[0].contextId)){console.log(this.claimType);let e=null;if(1==this.claimForm.value.customerBilling){let t=o.pluck(this.claimForm.value.claims,"categoryCode");console.log(t);let n=this.claimType.filter(e=>t.includes(e.code));console.log(n);let l=o.pluck(n,"billable_attachment_warning_msg");console.log(l);let i=o.compact(o.uniq(l));console.log(i),i.length>0?(e=i.toString(),console.log(e),e=e.replace(",","/"),console.log(e)):e="Bills"}else{let t=o.pluck(this.claimForm.value.claims,"categoryCode");console.log(t);let n=this.claimType.filter(e=>t.includes(e.code));console.log(n);let l=o.pluck(n,"non_billable_attachment_warning_msg");console.log(l);let i=o.compact(o.uniq(l));console.log(i),i.length>0?(e=i.toString(),console.log(e),e=e.replace(",","/"),console.log(e)):e="Bills"}return void this._util.showMessage(`Kindly attach ${e} !`,"close")}}else{let t=null;if(1==this.claimForm.value.customerBilling){let e=o.pluck(this.claimForm.value.claims,"categoryCode");console.log(e);let n=this.claimType.filter(t=>e.includes(t.code));console.log(n);let l=o.pluck(n,"billable_attachment_warning_msg");console.log(l);let i=o.compact(o.uniq(l));console.log(i),i.length>0?(t=i.toString(),console.log(t),t=t.replace(",","/"),console.log(t)):t="Bills"}else{let e=o.pluck(this.claimForm.value.claims,"categoryCode");console.log(e);let n=this.claimType.filter(t=>e.includes(t.code));console.log(n);let l=o.pluck(n,"non_billable_attachment_warning_msg");console.log(l);let i=o.compact(o.uniq(l));console.log(i),i.length>0?(t=i.toString(),console.log(t),t=t.replace(",","/"),console.log(t)):t="Bills"}let n=0;for(let l of this.claimFormData.claims.controls)((null===(e=l.controls.attachment.value[0])||void 0===e?void 0:e.fileName)||l.controls.contextId.value)&&(n+=1);if(this.claimFormData.claims.controls.length!=n)return void this._util.showMessage(`Kindly attach ${t} for all claim!`,"close")}if(this.claimForm.valid){let e=this.claimForm.value;e.claimDate=this.claimForm.value.claimDate,e.isExpenseCreatedFromWeb=1,this.deletedContextId.length>0&&this._expHomeService.deleteContextIds(this.deletedContextId),this.draftInfo?this.convertTheDraftIntoExpense(e):this.createAsNewExpense(e)}else this.checkFormValidators()},this.checkFormValidators=()=>{console.log(this.claimType);let e=this.claimForm.get("claims").value;return this.claimForm.get("claimedBy").invalid?this._util.showMessage("Please fill Claimed By !","close"):this.claimForm.get("originalInitiatorOId").invalid?this._util.showMessage("Initiator Id has not been assigned !","close"):this.claimForm.get("legalEntity").invalid?this._util.showMessage("Please fill Legal Entity Id  !","close"):this.claimForm.get("costCenter").invalid?this._util.showMessage("Please fill the Cost Center !","close"):this.claimForm.get("department").invalid?this._util.showMessage("Department has not been determined , Please change the Cost Center !","close"):this.claimForm.get("customerBilling").invalid?this._util.showMessage("Please choose the Customer Billing !","close"):null!=o.findWhere(e,{categoryCode:""})||null!=o.findWhere(e,{categoryCode:null})?this._util.showMessage("Please choose the claim type ! ","close"):null!=o.findWhere(e,{amount:""})||null!=o.findWhere(e,{amount:null})?this._util.showMessage("Please fill the amount !","close"):null!=o.findWhere(e,{currency:""})||null!=o.findWhere(e,{currency:null})?this._util.showMessage("Please choose the currency !","close"):null!=o.findWhere(e,{invoiceDate:""})||null!=o.findWhere(e,{invoiceDate:null})?this._util.showMessage("Please choose the invoice date ! ","close"):null!=o.findWhere(e,{description:""})||null!=o.findWhere(e,{description:null})?this._util.showMessage("Please fill the description ! ","close"):null!=o.findWhere(e,{approvers:""})||null!=o.findWhere(e,{approvers:null})?this._util.showMessage("Approvers not determined for one or more expense item ! ","close"):null!=o.findWhere(e,{workflowId:""})||null!=o.findWhere(e,{workflowId:null})?this._util.showMessage("Workflow not found for one or more expense item ! ","close"):null!=o.findWhere(e,{currency:""})||null!=o.findWhere(e,{currency:null})?this._util.showMessage("Please choose the currency !","close"):void 0},this.convertTheDraftIntoExpense=e=>{this._util.openConfirmationSweetAlertWithCustom("Are you sure to Submit?","").then(t=>{1==t&&(this.isBeingClaimed=!0,this.draftToClaimSubscription&&this.draftToClaimSubscription.unsubscribe(),this.draftToClaimSubscription=this._expHomeService.newdraftToSubmission(e,this.draftInfo.expHeaderId).subscribe(e=>Object(l.c)(this,void 0,void 0,(function*(){"S"==e.messType?(this._util.showMessage("Claim Moved From Draft To Submission","close"),this.isBeingClaimed=!1,this.dialogRef.close("refresh")):"E"==e.messType&&(this._snackBar.open(e.err?e.err.messText:e.messText,"close"),this.isBeingClaimed=!1)})),e=>{console.error(e),this._util.showMessage("Oops! something went wrong.","close"),this.isBeingClaimed=!1}))})},this.createAsNewExpense=e=>{this._util.openConfirmationSweetAlertWithCustom("Create New Claim ?","").then(t=>{1==t&&(this.isBeingClaimed=!0,this.createNewClaimSubscription&&this.createNewClaimSubscription.unsubscribe(),this.createNewClaimSubscription=this._expHomeService.createOneOrMoreExpenses(e).subscribe(e=>Object(l.c)(this,void 0,void 0,(function*(){console.log(e),"S"==e.messType?(this._util.showMessage("Claim request created successfully","close"),this.isBeingClaimed=!1,this.dialogRef.close("refresh")):"E"==e.messType&&(e.err?(this._snackBar.open(e.err.messText,"close"),this.isBeingClaimed=!1):(this._snackBar.open(e.messText,"close"),this.isBeingClaimed=!1))})),e=>{console.error(e),this._util.showMessage("Oops! something went wrong.","close"),this.isBeingClaimed=!1}))})},this.saveAsDraft=()=>{if(this.claimForm.valid){let e=this.claimForm.value;e.claimDate=this.claimForm.value.claimDate,e.isExpenseCreatedFromWeb=1;let t=this.draftInfo?this.draftInfo.expHeaderId:0;this.isBeingDrafted=!0,this.saveDraftSubscription&&this.saveDraftSubscription.unsubscribe(),this.deletedContextId.length>0&&this._expHomeService.deleteContextIds(this.deletedContextId),this.saveDraftSubscription=this._expHomeService.saveClaimAsDraft(e,t).subscribe(e=>Object(l.c)(this,void 0,void 0,(function*(){console.log(e),"S"==e.messType?(this.isBeingDrafted=!1,this._util.showMessage("Claim saved as Draft !","close"),this.dialogRef.close("refresh")):"E"==e.messType&&(e.err?(this._snackBar.open(e.err.messText,"close"),this.isBeingClaimed=!1):(this._snackBar.open(e.messText,"close"),this.isBeingClaimed=!1))})))}else this.checkFormValidators()},this.resetForm=()=>{let e=this._auth.getProfile().profile;this.approvers=null,this.claimType.forEach(e=>{e.active=!1}),this.removeClaimsExceptFirst(),this.claimForm.reset(),this.claimForm.controls.claims.controls[0].controls.attachment.patchValue(""),this.claimForm.controls.claims.controls[0].controls.uploadInProgress.patchValue(!1),this.claimForm.get("claimedBy").patchValue(e.oid),this.claimForm.get("originalInitiatorOId").patchValue(e.oid),this.claimForm.get("claimDate").patchValue(c().format("DD-MM-YYYY")),this.claimForm.get("legalEntity").patchValue(this.defaultLE),this.claimForm.get("expenseType").patchValue("C"),this.currentFileIndex=0,this.currentlyActiveFormType="singleExpenseCreation"},this.removeClaimsExceptFirst=()=>{const e=this.claimFormData.claims;let t=e.length-1;for(;t>0;)e.removeAt(t),t--},this.onFileAdd=e=>{e&&(this.currentFileIndex=e),this.uploader.uploadAll()},this.openFile=e=>{let t=this.claimForm.controls.claims.controls[e].controls.attachment.value[0].key,i=this.claimForm.controls.claims.controls[e].controls.attachment.value[0].fileName,o=this.claimForm.controls.claims.controls[e].controls.attachment.value[0].type;this.claimForm.controls.claims.controls[e].controls.uploadInProgress.patchValue(!0),this._expHomeService.getFileDataFromS3(t).subscribe(t=>Object(l.c)(this,void 0,void 0,(function*(){if(this.claimForm.controls.claims.controls[e].controls.uploadInProgress.patchValue(!1),"S"==t.messType)if("image/png"==o||"image/jpg"==o||"image/jpeg"==o){let e="application/pdf"==o?"90vh":"auto",l="application/pdf"==o?"90vw":"auto",a="application/pdf"==o;const{AttachmentViewerComponent:r}=yield n.e(742).then(n.bind(null,"5zst"));this.dialog.open(r,{width:l,height:e,maxWidth:"100vh",maxHeight:"100vh",disableClose:!1,data:{fileData:"data:"+o+";base64,"+t.data.fileData,fileName:i,isFilePDF:a}})}else"application/pdf"==o?window.open("").document.write("<iframe width='100%' height='100%' src='data:application/pdf;base64, "+t.data.fileData+"'></iframe>"):this._fileSaver.saveAsFile(t.data.fileData,i,o);else this._util.showMessage(t.messText,"Dismiss",3e3)})),t=>{this.claimForm.controls.claims.controls[e].controls.uploadInProgress.patchValue(!1),this._util.showMessage("Error in Downloading !","Dismiss",3e3)})},this.changeClaimType=(e,t)=>{this.patchNewlySelectedClaimType(e,t),this.determineWorkflow()},this.refreshButtons=()=>{this.claimForm.controls.claims.valueChanges.subscribe(e=>{setTimeout(()=>{let e=new Set(o.pluck(this.claimForm.value.claims,"category"));this.claimType.forEach(t=>{t.active=!!e.has(t.id)})},600)})},this.patchNewlySelectedClaimType=(e,t)=>{let n=e;this.claimForm.controls.claims.controls[t].controls.categoryCode.patchValue(n.code),this.claimForm.controls.claims.controls[t].controls.categoryName.patchValue(n.name)},this.patchDraftValue=e=>{this.claimForm.patchValue({expenseType:e.expenseType,isAggregationAllowed:e.isAggregationAllowed,workFlowId:e.workFlowId,claimedBy:e.claimedBy,originalInitiatorOId:e.originalInitiatorOId,legalEntity:JSON.stringify(e.legalEntity),department:e.department,costCenter:JSON.stringify(e.costCenter),claimDate:c().format("DD-MM-YYYY"),costCenterCode:e.costCenter.id,legalEntityCode:e.legalEntity.entity_id,customerBilling:e.customerBilling});const t=this.claimFormData.claims;let n=t.length-1;for(;0==n;)t.removeAt(n),n--;this.patchClaimDraftArray(e,t)},console.log(h),this.uploader=new s.d({url:"/api/exPrimary/uploadExpenseAttachment",authToken:"Bearer "+this._auth.getToken(),disableMultipart:!1,allowedMimeType:this.allowedMimeType,maxFileSize:this.maxFileSize}),this.createForm(),this.detectValueChanges(),this.uploadFileAttachment(),this.setDefaultLE()}ngOnInit(){return Object(l.c)(this,void 0,void 0,(function*(){this.spinnerService.show(),this.getClaimCategoryMaster(),this.expWorkflowProperties=yield this.getExpenseClaimWorkflows(),this.getBillingConfig(),this.getFormFieldConfig(),yield this._expHomeService.getAllCostCenter().then(e=>{this.costCenterList=e}),yield this._expHomeService.getAllLegalEntity().then(e=>{e.data.length>0&&(this.legalEntityList=e.data)}),this.draftInfo||(yield this.getEntityCurrency()),this.spinnerService.hide()}))}getEntityCurrency(){return Object(l.c)(this,void 0,void 0,(function*(){this.claimForm.value.legalEntityCode&&this._expHomeService.getEntityCurrency(this.claimForm.value.legalEntityCode).subscribe(e=>{if("S"==e.messType){this.entityCurrency=e.data[0];for(let e of this.claimForm.get("claims").controls)e.controls.currency.patchValue(JSON.stringify(this.entityCurrency));this.defaultCurrency=JSON.stringify(this.entityCurrency)}},e=>{console.error(e)})}))}changeLegalEntity(e){this.claimForm.controls.legalEntity.patchValue(e),this.getEntityCurrency()}getBillingConfig(){return Object(l.c)(this,void 0,void 0,(function*(){yield this.getCustomerBillingConfig();let e=yield this.getExpenseConfig();e&&(this.isDefaultCostCenter=JSON.parse(e[0].expense_application_config).is_default_cost_center,1==this.isDefaultCostCenter&&this.claimForm.value.claimedBy)&&(yield this.getDepartmentCostCenter(this.claimForm.value.claimedBy))}))}getDepartmentCostCenter(e){return Object(l.c)(this,void 0,void 0,(function*(){this.draftInfo||this._expHomeService.getDepartmentCostCenter(e).subscribe(e=>{if("S"==e.messType){let t=e.data[0].cost_center;this.claimForm.controls.costCenterCode.patchValue(t);let n=o.filter(this.costCenterList,e=>{if(e.id==t)return e});n.length>0&&this.claimForm.controls.costCenter.patchValue(n[0])}},e=>{console.error(e)})}))}getCustomerBillingConfig(){return Object(l.c)(this,void 0,void 0,(function*(){this._expHomeService.getExpenseBillingConfig().subscribe(e=>{this.billingConfig=e.data},e=>{console.error(e)})}))}getFormFieldConfig(){return Object(l.c)(this,void 0,void 0,(function*(){yield this._expHomeService.getFormFieldConfig().subscribe(e=>{this.formFieldConfig=e.data,this.spinnerService.hide(),this.formFieldConfig&&this.formFieldConfig.forEach(e=>{this.fieldConfig[e.field_key]=e})},e=>{this.spinnerService.hide(),console.error(e)})}))}getCostCenterOfClaimer(){return Object(l.c)(this,void 0,void 0,(function*(){yield this.getDepartmentCostCenter(this.claimedBy),this.determineWorkflow()}))}setDefaultLE(){return Object(l.c)(this,void 0,void 0,(function*(){return this.defaultLE=yield this._expHomeService.getDefaultLE(),this.claimForm.patchValue({legalEntity:this.defaultLE,legalEntityCode:JSON.parse(this.defaultLE).entity_id}),this.defaultLE}))}get claimFormData(){return this.claimForm.controls}getAttahmentAlertMessage(e){let t=o.filter(this.claimType,{id:e});return t.length>0?1==this.claimForm.value.customerBilling?"Attach "+t[0].billable_attachment_warning_msg:"Attach "+t[0].non_billable_attachment_warning_msg:"Attach Bills"}ngOnChanges(){this.draftInfo&&(this.resetForm(),this.patchDraftValue(this.draftInfo),console.log(this.draftInfo))}changeCostCenter(e){this.claimForm.controls.costCenter.patchValue(e)}patchClaimDraftArray(e,t){console.log("reached here"),e.claims.forEach((e,n)=>{console.log(e),t.push(this.fb.group({categoryCode:[e.categoryCode,a.H.required],category:[e.category,a.H.required],categoryName:[e.categoryName,a.H.required],amount:[e.amount,[a.H.required,a.H.pattern("^[0-9]+(.[0-9]{0,9})?$"),It]],currency:[JSON.stringify(e.currency),a.H.required],invoiceDate:[c.utc(e.invoiceDate).format("YYYY-MM-DD"),a.H.required],description:[e.description,a.H.required],attachment:[e.attachment],approvers:[e.approvers?e.approvers:"",a.H.required],workflowId:[e.workflowId?e.workflowId:""],isAggregationAllowed:[e.isAggregationAllowed?e.isAggregationAllowed:""],uploadInProgress:[!1],startDate:[e.startDate?c.utc(e.startDate).format("YYYY-MM-DD"):c().format("YYYY-MM-DD")],endDate:[e.endDate?c.utc(e.endDate).format("YYYY-MM-DD"):c().format("YYYY-MM-DD")],peopleInvolved:[e.peopleInvolved?e.peopleInvolved:""],contextId:[e.contextId,a.H.required],invoiceNo:[e.invoiceNo?e.invoiceNo:""]})),n>0&&(this.currentlyActiveFormType="multipleExpenseCreation")}),console.log("need to refresh buttons")}ngOnDestroy(){this.saveDraftSubscription&&this.saveDraftSubscription.unsubscribe(),this.draftToClaimSubscription&&this.draftToClaimSubscription.unsubscribe(),this.createNewClaimSubscription&&this.createNewClaimSubscription.unsubscribe(),this.determineWorkflowSubscription&&this.determineWorkflowSubscription.unsubscribe()}getPeopleInvolved(e,t){this.selectedPeopleInvolvedList=e,this.selectedPeopleInvolvedId=[],this.claimForm.controls.claims.controls["singleExpenseCreation"==this.currentlyActiveFormType?0:t].controls.peopleInvolved.patchValue(this.selectedPeopleInvolvedList)}removeEmp(e,t,n){let l=[],i=this.claimForm.controls.claims.controls["singleExpenseCreation"==this.currentlyActiveFormType?0:n].controls.peopleInvolved.value;for(let o=0;o<i.length;o++)e.id!=i[o].id&&l.push(i[o]);this.claimForm.controls.claims.controls["singleExpenseCreation"==this.currentlyActiveFormType?0:n].controls.peopleInvolved.patchValue(l)}changeInAttachment(e){return Object(l.c)(this,void 0,void 0,(function*(){o.contains(this.contextId,e)||this.contextId.push(e),console.log(e),this.claimForm.controls.claims.controls[0].controls.contextId.patchValue(e),console.log(this.claimForm.controls.claims.controls[0].controls),console.log(this.claimForm.value),null==e&&null!=this.draftInfo&&(yield this._expHomeService.updateExpenseAttachment(this.draftInfo.expHeaderId,0))}))}changeInFiles(e,t){return Object(l.c)(this,void 0,void 0,(function*(){o.contains(this.contextId,e)||this.contextId.push(e),this.claimForm.controls.claims.controls[t].controls.contextId.patchValue(e),console.log(this.claimForm.controls.claims.controls[0].controls),console.log(this.claimForm.value),null==e&&null!=this.draftInfo&&(yield this._expHomeService.updateExpenseAttachment(this.draftInfo.expHeaderId,t))}))}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](a.i),i["\u0275\u0275directiveInject"](w.a),i["\u0275\u0275directiveInject"](S.a),i["\u0275\u0275directiveInject"](D.a),i["\u0275\u0275directiveInject"](O.a),i["\u0275\u0275directiveInject"](A.a),i["\u0275\u0275directiveInject"](p.b),i["\u0275\u0275directiveInject"](p.h),i["\u0275\u0275directiveInject"](p.a),i["\u0275\u0275directiveInject"](M.a),i["\u0275\u0275directiveInject"](F.a),i["\u0275\u0275directiveInject"](I.c))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["ng-component"]],inputs:{max:"max"},outputs:{close:"close"},features:[i["\u0275\u0275ProvidersFeature"]([{provide:_.c,useClass:b.c,deps:[_.f,b.a]},{provide:_.e,useValue:{parse:{dateInput:"DD-MM-YYYY"},display:{dateInput:"DD-MM-YYYY",monthYearLabel:"MMM YYYY"}}}]),i["\u0275\u0275NgOnChangesFeature"]],decls:38,vars:26,consts:[[1,"container-fluild","pl-2","pr-2","create-claim-styles"],[3,"formGroup","keydown.enter"],[1,"row","p-0"],[1,"col-12","p-0"],[1,"row","border-bottom","solid"],[1,"col-11","pt-2","pb-2","d-flex"],["mat-icon-button","",1,"bubble"],[1,"iconButton"],[1,"name","my-auto","ml-2"],[1,"col-1","d-flex","pt-2"],["matTooltip","Close",1,"close-Icon",2,"cursor","pointer","color","#4d4d4b",3,"click"],[1,"row","pt-2"],[1,"col-4","pl-0","pr-0",2,"border-right","1px solid #d3d3d3"],["class","row pt-2",4,"ngIf"],[1,"col-8"],[4,"ngIf"],[1,"row","mb-2"],[1,"col-5","d-flex"],["class","row pt-0",4,"ngIf"],[1,"col"],[1,"col-3","p-0","pt-3","d-flex",2,"justify-content","flex-end"],["style","width: 100%",4,"ngIf","ngIfElse"],["attachmentPlugin",""],[1,"col-5","p-0"],["mat-icon-button","","matTooltip","Save as Draft","type","submit",1,"iconbtn","ml-3","mt-1","mb-1",3,"ngStyle","ngClass","disabled","click"],[4,"ngIf","ngIfElse"],["showDraftSpinner",""],["mat-icon-button","","matTooltip","Create Claim","type","submit",1,"iconbtn","ml-3","mr-2","mt-1","mb-1",3,"ngStyle","ngClass","disabled","click"],["showSubmitSpinner",""],["size","medium","type","ball-clip-rotate","bdColor","rgba(236, 233, 233, 0.8)","color","#cf0001"],[1,"col-12","pl-1","pr-0"],["style","padding: 5px;",4,"ngFor","ngForOf"],[2,"padding","5px"],["mat-raised-button","","class","mr-1",3,"ngClass","matTooltip","click",4,"ngIf"],["mat-raised-button","",1,"mr-1",3,"ngClass","matTooltip","click"],[1,"claim-icons"],["mat-button","",3,"click"],[1,"col-12","pl-1","pr-1"],["class","row",4,"ngIf"],[1,"row"],["class","col-6 pt-2",4,"ngIf"],[1,"col-6","d-flex","align-items-center"],[1,"mr-3","header","my-auto"],[1,"row","pt-1"],["class","col-6",4,"ngIf"],["formArrayName","claims"],[3,"formGroupName",4,"ngFor","ngForOf"],[1,"col-12"],["formControlName","claimedBy",3,"isAutocomplete","label","readonly"],["required","true","formControlName","legalEntityCode",1,"create-claim-field-inputsearch",3,"placeholder","list","change"],[1,"col-6","pt-2"],["required","true","formControlName","costCenterCode",1,"create-claim-field-inputsearch",3,"placeholder","list","change"],[4,"ngFor","ngForOf"],["content-type","template","placement","top","imgHeight","32px","imgWidth","32px","borderStyle","solid","borderWidth","2px",2,"margin","2px",3,"tooltip","id"],["approverTooltip",""],[1,"row","tooltip-text"],[1,"col-6"],["appearance","outline",1,"create-claim-field"],["matInput","","formControlName","department","readonly","",3,"placeholder"],["matInput","","required","true","formControlName","claimDate","readonly",""],[3,"formGroupName"],["class","col-12",4,"ngIf"],["class","col-6 pl-3",4,"ngIf"],[1,"col-6","d-flex"],["class","col-8 p-0",4,"ngIf"],[1,"col-4","p-0","d-flex",2,"flex-wrap","wrap"],["class","row mt-2",4,"ngFor","ngForOf"],["formControlName","currency",3,"label","optionLabel","optionValue","apiUri","ngModelChange"],["matInput","","placeholder","DD-MM-YYYY","required","true","formControlName","invoiceDate",3,"matDatepicker","min","max"],["matSuffix","",3,"for"],["invoiceDate",""],["matInput","","type","number","min","1","formControlName","amount","onkeydown","return event.keyCode !== 69",3,"placeholder","keyup","change"],["class","mat-hint",4,"ngIf"],[1,"mat-hint"],["matInput","","formControlName","description","required","true",3,"placeholder"],[1,"col-6","pl-3"],["appearance","outline",2,"width","100%"],[3,"rangePicker","min"],["matStartDate","","formControlName","startDate","placeholder","Start date"],["matEndDate","","formControlName","endDate","placeholder","End date"],["picker",""],[1,"col-8","p-0"],[2,"width","100%",3,"label","token","optionLabel","fieldValue","API_URL","placeholder","selectedValues"],[1,"row","mt-2"],[1,"pl-1",2,"cursor","pointer",3,"matTooltip","click"],["imgWidth","20px","imgHeight","20px",3,"id"],["matInput","","formControlName","invoiceNo","placeholder","Invoice No."],["appearance","outline","style","width:100%",4,"ngIf"],[3,"formControl"],[3,"value",4,"ngFor","ngForOf"],[3,"value"],[1,"row","pt-2","mb-2",2,"border-bottom","1px solid #d3d3d3","background-color","#fbfbfb"],[1,"col-4","pl-0","pr-0"],[1,"col-4","pl-1","pr-0"],["class","row pt-2 pb-1",4,"ngIf"],[1,"col-4","pl-0","pr-1"],[2,"height","24rem","overflow","scroll"],["class","row pt-2 mb-2 pb-1","style","border-bottom: 1px solid #d3d3d3;",3,"formGroupName",4,"ngFor","ngForOf"],[1,"col-12","pl-0"],[1,"col-12","pt-2","pl-0"],[1,"col-12","pl-1"],[1,"row","pt-2","pb-1"],[1,"col-12","d-flex","align-items-center","pl-1"],[1,"col-12","pt-1"],["formControlName","customerBilling"],[1,"row","pt-2","mb-2","pb-1",2,"border-bottom","1px solid #d3d3d3",3,"formGroupName"],["class","col-4 pl-0",4,"ngIf"],["class","col-4",4,"ngIf"],["class","col-4 pl-3 pr-0",4,"ngIf"],[1,"col-4","d-flex"],["class","col-4 pt-2 pl-0 d-flex",4,"ngIf"],[1,"col-6","pt-2","d-flex"],[1,"col-4","p-0","d-flex","justify-content-end"],[2,"max-width","200px","height","70%"],[1,"d-flex",3,"ngClass"],[1,"pt-2","pl-2","content-muliple",3,"matTooltip"],["mat-icon-button","","class","close-button ml-auto",4,"ngIf","ngIfElse"],["attachmentOld",""],["color","primary","mat-icon-button","",1,"remove-icon",3,"click"],[1,"col-4","pl-0"],["required","true","placeholder","Claim Type","formControlName","category",1,"create-claim-field-inputsearch",3,"list","change"],[1,"col-4"],["matInput","","required","true","formControlName","invoiceDate",3,"matDatepicker","min","max"],["matInput","","type","number","formControlName","amount",3,"placeholder","change"],[1,"col-4","pl-3","pr-0"],["formControlName","currency",3,"label","optionLabel","optionValue","apiUri","change"],[1,"col-4","pt-2","pl-0","d-flex"],[2,"width","100%",3,"label","token","optionLabel","API_URL","fieldValue","selectedValues"],["mat-icon-button","",1,"close-button","ml-auto"],[2,"color","black",3,"destinationBucket","routingKey","contextId","allowEdit","change"],["style","font-size: 21px !important;","matTooltip","Attach File",3,"click",4,"ngIf"],["class","m-auto","diameter","20",4,"ngIf"],[1,"ml-2","my-auto","file-name",2,"cursor","pointer",3,"matTooltip","click"],["matTooltip","Attach File","class","m-auto edit-icon",3,"click",4,"ngIf"],["matTooltip","Attach File",2,"font-size","21px !important",3,"click"],["type","file","name","file","ng2FileSelect","",2,"display","none",3,"uploader","accept","change"],["multiFileInput",""],["diameter","20",1,"m-auto"],["matTooltip","Attach File",1,"m-auto","edit-icon",3,"click"],[1,"row","pt-0"],["class","col-12 d-flex",4,"ngIf"],[1,"col-12","d-flex"],["class","my-auto slide-from-down",4,"ngFor","ngForOf"],[1,"my-auto","slide-from-down"],[1,"align-middle","mr-2",2,"font-size","22px","color","#b94141"],[1,"pt-1",2,"color","#636060"],[2,"width","100%"],["class","ml-auto",4,"ngIf"],[1,"ml-auto"],["matTooltip","Attach a file","mat-mini-fab","",1,"iconbtn","mt-1","mb-1",3,"click"],["singleFileInput",""],["class","ml-2 my-auto","diameter","20",4,"ngIf"],["diameter","20",1,"ml-2","my-auto"],["class","d-flex",4,"ngIf"],[1,"d-flex"],["class","ml-4 my-auto",4,"ngIf"],[1,"ml-4","my-auto",2,"cursor","pointer",3,"click"],[1,"ml-4","my-auto"],["aria-hidden","true",2,"font-size","18px !important"],["draftAttachment",""],[1,"d-flex","mt-1",2,"height","85%",3,"ngClass"],[1,"pt-2","pl-2","content",2,"width","90%",3,"matTooltip"],[1,"d-flex","justify-content-end"],["diameter","30",1,"btn-spinner"]],template:function(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275elementStart"](1,"form",1),i["\u0275\u0275listener"]("keydown.enter",(function(e){return e.preventDefault()})),i["\u0275\u0275elementStart"](2,"div",2),i["\u0275\u0275elementStart"](3,"div",3),i["\u0275\u0275elementStart"](4,"div",4),i["\u0275\u0275elementStart"](5,"div",5),i["\u0275\u0275elementStart"](6,"div",6),i["\u0275\u0275elementStart"](7,"mat-icon",7),i["\u0275\u0275text"](8,"request_quote"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](9,"span",8),i["\u0275\u0275text"](10),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](11,"div",9),i["\u0275\u0275elementStart"](12,"mat-icon",10),i["\u0275\u0275listener"]("click",(function(){return t.closeForm()})),i["\u0275\u0275text"](13,"close"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](14,"div",11),i["\u0275\u0275elementStart"](15,"div",12),i["\u0275\u0275template"](16,ee,4,3,"div",13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](17,"div",14),i["\u0275\u0275template"](18,we,17,7,"ng-container",15),i["\u0275\u0275template"](19,lt,16,7,"ng-container",15),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](20,"div",16),i["\u0275\u0275elementStart"](21,"div",17),i["\u0275\u0275template"](22,at,2,1,"div",18),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](23,"div",19),i["\u0275\u0275elementStart"](24,"div",20),i["\u0275\u0275template"](25,ut,4,3,"div",21),i["\u0275\u0275template"](26,vt,3,2,"ng-template",null,22,i["\u0275\u0275templateRefExtractor"]),i["\u0275\u0275elementStart"](28,"div",23),i["\u0275\u0275elementStart"](29,"button",24),i["\u0275\u0275listener"]("click",(function(){return t.saveAsDraft()})),i["\u0275\u0275template"](30,Ct,2,0,"mat-icon",25),i["\u0275\u0275template"](31,xt,1,0,"ng-template",null,26,i["\u0275\u0275templateRefExtractor"]),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](33,"button",27),i["\u0275\u0275listener"]("click",(function(){return t.saveExpense()})),i["\u0275\u0275template"](34,yt,2,0,"mat-icon",25),i["\u0275\u0275template"](35,bt,1,0,"ng-template",null,28,i["\u0275\u0275templateRefExtractor"]),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](37,"ngx-spinner",29)),2&e){const e=i["\u0275\u0275reference"](27),n=i["\u0275\u0275reference"](32),l=i["\u0275\u0275reference"](36);i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("formGroup",t.claimForm),i["\u0275\u0275advance"](9),i["\u0275\u0275textInterpolate"](t.draftInfo?"Claim #"+t.draftInfo.expHeaderId:"New Claim"),i["\u0275\u0275advance"](6),i["\u0275\u0275property"]("ngIf",t.claimType.length>0),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf","singleExpenseCreation"==t.currentlyActiveFormType),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf","multipleExpenseCreation"==t.currentlyActiveFormType),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngIf",1==t.enableWarning),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngIf",null==t.claimForm.controls.claims.controls[0].controls.attachment?null:t.claimForm.controls.claims.controls[0].controls.attachment.value)("ngIfElse",e),i["\u0275\u0275advance"](4),i["\u0275\u0275property"]("ngStyle",i["\u0275\u0275pureFunction1"](18,_t,0==t.isBeingDrafted?"#cf0001":"#f3f3f3"))("ngClass",i["\u0275\u0275pureFunction1"](20,Et,t.isBeingDrafted||t.isBeingClaimed||t.isBeingWorkflowDetermined))("disabled",t.isBeingDrafted||t.isBeingClaimed||t.isBeingWorkflowDetermined),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",0==t.isBeingDrafted)("ngIfElse",n),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngStyle",i["\u0275\u0275pureFunction1"](22,_t,0==t.isBeingClaimed?"#cf0001":"#f3f3f3"))("ngClass",i["\u0275\u0275pureFunction1"](24,Et,t.isBeingDrafted||t.isBeingClaimed||t.isBeingWorkflowDetermined))("disabled",t.isBeingClaimed||t.isBeingDrafted||t.isBeingWorkflowDetermined),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",0==t.isBeingClaimed)("ngIfElse",l)}},directives:[a.J,a.w,a.n,g.a,v.a,d.NgIf,h.a,d.NgStyle,d.NgClass,I.a,d.NgForOf,a.h,T.a,a.v,a.l,H,a.F,Y.a,x.a,u.c,u.g,f.b,a.e,a.o,j.a,C.g,C.i,u.i,C.f,a.A,u.f,C.d,C.l,C.k,C.e,J,E.c,a.k,_.p,K.a,s.b,y.c],styles:[".create-claim-styles[_ngcontent-%COMP%]   .restict-cursor[_ngcontent-%COMP%]{cursor:not-allowed}.create-claim-styles[_ngcontent-%COMP%]   .btn-spinner[_ngcontent-%COMP%]{margin-left:4px}.create-claim-styles[_ngcontent-%COMP%]   .name[_ngcontent-%COMP%]{color:#cf0001;font-weight:500;font-size:14px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.create-claim-styles[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{color:#5f5f5f;font-size:12px!important}.create-claim-styles[_ngcontent-%COMP%]   .mat-hint[_ngcontent-%COMP%]{color:red;padding-top:6px;font-size:10px}.create-claim-styles[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]{display:flex;line-height:26px;border-radius:50%;cursor:context-menu!important;width:26px;height:26px;background-color:#fbfbfb;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.create-claim-styles[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#cf0001;font-size:18px;margin-top:3px!important;margin-left:4px!important}.create-claim-styles[_ngcontent-%COMP%]   .iconbtn[_ngcontent-%COMP%]{background-color:#c92020;color:#fff;padding:0;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.create-claim-styles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}.create-claim-styles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff;font-weight:400;margin-bottom:4%;width:11rem;text-align:left;padding:2px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.create-claim-styles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%]   .claim-icons[_ngcontent-%COMP%]{font-size:20px;margin-right:6px;margin-left:11px;color:#fff}.create-claim-styles[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]{background-color:#f3f3f3;color:#1a1a1a;margin-bottom:4%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;font-weight:400;text-align:left;width:11rem;padding:2px}.create-claim-styles[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]   .claim-icons[_ngcontent-%COMP%]{font-size:20px;margin-right:6px;margin-left:11px;color:#343434}.create-claim-styles[_ngcontent-%COMP%]   .title-table[_ngcontent-%COMP%]{font-size:14px;color:#1a1a1a}.create-claim-styles[_ngcontent-%COMP%]   .create-claim-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%!important}.create-claim-styles[_ngcontent-%COMP%]   .create-claim-field-inputsearch[_ngcontent-%COMP%]{font-size:13px!important}.create-claim-styles[_ngcontent-%COMP%]   .create-claim-field-inputsearch[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]{width:70%!important}.create-claim-styles[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:25px;cursor:pointer}.create-claim-styles[_ngcontent-%COMP%]   .edit-icon[_ngcontent-%COMP%]{color:#66615b;padding-top:7px;font-size:15px;cursor:pointer}.create-claim-styles[_ngcontent-%COMP%]   .file-name[_ngcontent-%COMP%]{font-size:14px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.create-claim-styles[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}.create-claim-styles[_ngcontent-%COMP%]   .invalid-attachment[_ngcontent-%COMP%]{color:#cf0001;border:2px solid #cf0001;height:90%;width:100%;border-radius:5px}.create-claim-styles[_ngcontent-%COMP%]   .attachment-btn[_ngcontent-%COMP%]{width:100%;color:#5f5f5f;border:2px solid #d3d3d3;height:90%;border-radius:5px}.create-claim-styles[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;width:90%}.create-claim-styles[_ngcontent-%COMP%]   .content-muliple[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;width:100%}"],data:{animation:[Object(r.o)("fadeInOut",[Object(r.l)("void",Object(r.m)({opacity:0})),Object(r.n)("void <=> *",Object(r.e)(400))])]}}),e})();function It(e){return e.value<=0?{validAmount:!1}:null}},gvOY:function(e,t,n){"use strict";n.d(t,"a",(function(){return E})),n.d(t,"b",(function(){return F})),n.d(t,"c",(function(){return _}));var l=n("fXoL"),i=n("XNiG"),o=n("mrSG"),a=n("tk/3"),r=n("3Pt+"),s=n("1G5W"),c=n("Kj3r"),d=n("ofXK"),m=n("bTqV"),p=n("NFeN"),u=n("kmnG"),f=n("qFsG"),g=n("d3UM"),h=n("/1cH"),v=n("WJ5W"),C=n("FKr1");function x(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"mat-option",6),l["\u0275\u0275elementStart"](1,"div",7),l["\u0275\u0275element"](2,"div"),l["\u0275\u0275element"](3,"div"),l["\u0275\u0275element"](4,"div"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]())}function y(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"span"),l["\u0275\u0275text"](1),l["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=l["\u0275\u0275nextContext"]().$implicit;l["\u0275\u0275advance"](1),l["\u0275\u0275textInterpolate1"]("",n[e],"\xa0")}}function b(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"mat-option",8),l["\u0275\u0275template"](1,y,2,1,"span",9),l["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=l["\u0275\u0275nextContext"]();l["\u0275\u0275property"]("value",e),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngForOf",n.optionLabel)}}let _=(()=>{class e{constructor(){this.msg=new i.b,this.removeOption=e=>{this.msg.next(e)},this.getRemoveIndex=()=>this.msg.asObservable()}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275prov=Object(l["\u0275\u0275defineInjectable"])({factory:function(){return new e},token:e,providedIn:"root"}),e})(),E=(()=>{class e{constructor(e,t){this._http=e,this._mulL=t,this.selectedValues=new l.EventEmitter,this.list=[],this.searchCtrl=new r.j,this.selectedValCtrl=new r.j,this._onDestroy=new i.b,this.isLoading=!1,this.fetchData=()=>{const e={headers:new a.f({"Content-Type":"application/json",Authorization:"Bearer "+this.token})};return this.isLoading=!0,new Promise((t,n)=>{this._http.post(this.API_URL?this.API_URL:"",{searchText:this.searchCtrl.value},e).subscribe(e=>{t(e),this.isLoading=!1},e=>{n(e),this.isLoading=!1,console.error(e)})})}}ngOnInit(){this.searchCtrl.valueChanges.pipe(Object(s.a)(this._onDestroy),Object(c.a)(700)).subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){null!=e&&""!=e&&(this.list=yield this.fetchData())}))),this.selectedValCtrl.valueChanges.subscribe(e=>{this.selectedValues.emit(e)}),this._mulL.getRemoveIndex().subscribe(e=>{this.removeSelectedOption(e)})}removeSelectedOption(e){let t=[];t=this.selectedValCtrl.value,t.splice(e,1),this.selectedValCtrl.patchValue([...t]),this.selectedValues.emit(this.selectedValCtrl.value)}}return e.\u0275fac=function(t){return new(t||e)(l["\u0275\u0275directiveInject"](a.c),l["\u0275\u0275directiveInject"](_))},e.\u0275cmp=l["\u0275\u0275defineComponent"]({type:e,selectors:[["kebs-mul-sel-search"]],inputs:{label:"label",placeholder:"placeholder",appearance:"appearance",optionLabel:"optionLabel",API_URL:"API_URL",bodyParams:"bodyParams",token:"token"},outputs:{selectedValues:"selectedValues"},decls:10,vars:8,consts:[["appearance","outline",1,"full-width"],["multiple","",3,"placeholder","formControl"],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel","hideClearSearchButton"],["class","is-loading",4,"ngIf"],[3,"click"],[3,"value",4,"ngFor","ngForOf"],[1,"is-loading"],[1,"lds-facebook"],[3,"value"],[4,"ngFor","ngForOf"]],template:function(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"mat-form-field",0),l["\u0275\u0275elementStart"](1,"mat-label"),l["\u0275\u0275text"](2),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](3,"mat-select",1),l["\u0275\u0275elementStart"](4,"mat-option"),l["\u0275\u0275element"](5,"ngx-mat-select-search",2),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](6,x,5,0,"mat-option",3),l["\u0275\u0275elementStart"](7,"mat-option",4),l["\u0275\u0275listener"]("click",(function(){return t.selectedValCtrl.reset()})),l["\u0275\u0275text"](8,"None"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](9,b,2,2,"mat-option",5),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&e&&(l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate"](t.label),l["\u0275\u0275advance"](1),l["\u0275\u0275propertyInterpolate"]("placeholder",t.placeholder),l["\u0275\u0275property"]("formControl",t.selectedValCtrl),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("formControl",t.searchCtrl)("placeholderLabel",t.placeholder?t.placeholder:"")("hideClearSearchButton",!1),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",t.isLoading),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("ngForOf",t.list))},directives:[u.c,u.g,g.c,r.v,r.k,C.p,v.a,d.NgIf,d.NgForOf],styles:[".full-width[_ngcontent-%COMP%]{width:100%}.lds-facebook[_ngcontent-%COMP%]{display:inline-block;position:sticky;width:80px;height:24px;margin-left:110px}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{display:inline-block;position:absolute;left:4px;width:5px;background:#cf0001;-webkit-animation:lds-facebook 1.2s cubic-bezier(0,.5,.5,1) infinite;animation:lds-facebook 1.2s cubic-bezier(0,.5,.5,1) infinite}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:first-child{left:8px;-webkit-animation-delay:-.24s;animation-delay:-.24s}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:nth-child(2){left:16px;-webkit-animation-delay:-.12s;animation-delay:-.12s}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:nth-child(3){left:24px;-webkit-animation-delay:0;animation-delay:0}@-webkit-keyframes lds-facebook{0%{top:8px;height:32px}50%,to{top:12px;height:16px}}@keyframes lds-facebook{0%{top:8px;height:32px}50%,to{top:12px;height:16px}}"]}),e})(),F=(()=>{class e{}return e.\u0275mod=l["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=l["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[d.CommonModule,m.b,p.b,u.e,f.c,g.d,h.c,a.d,r.E,v.b]]}),e})()},hJL4:function(e,t,n){"use strict";n.d(t,"a",(function(){return m}));var l=n("mrSG"),i=n("XNiG"),o=n("xG9w"),a=n("fXoL"),r=n("tk/3"),s=n("LcQX"),c=n("XXEo"),d=n("flaP");let m=(()=>{class e{constructor(e,t,n,l){this.http=e,this.UtilityService=t,this.loginService=n,this.roleService=l,this.msg=new i.b,this.taskStatusColor=[],this.approveRequest=(e,t)=>this.http.post("/api/isa/request/approveResourceRequest",{requestDetails:e,approverOid:t}),this.rejectRequest=(e,t)=>this.http.post("/api/isa/request/rejectResourceRequest",{requestDetails:e,approverOid:t}),this.getTaskStatusColor()}getAllRequestsOfUser(e,t,n,l,i,o,a){let r=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getAllRequestsOfUser",{oid:e,statusId:t,statusCode:n,objectIds:l,skip:i,limit:o,filterConfig:a,orgIds:r})}getAllRoleAccess(){return o.where(this.roleService.roles,{application_id:139})}getRequestsBasedOnStatus(e,t,n,l,i,o,a){let r=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getRequestsBasedOnStatus",{oid:e,statusId:t,statusCode:n,objectIds:l,skip:i,limit:o,filterConfig:a,orgIds:r})}getRequestsForAwaitingApproval(e,t,n,l){return this.http.post("/api/isa/request/getRequestsForAwaitingApproval",{oid:e,skip:t,limit:n,filterConfig:l})}getObjectIdsBasedOnOid(e){return this.http.post("/api/isa/request/getObjectIdsBasedOnOid",{oid:e})}getStatusCountBasedOnOid(e,t,n,l){let i=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getStatusCountBasedOnOid",{oid:e,objectIds:t,dataTypeArray:n,filterConfig:l,orgIds:i})}getHeaderCount(e){return this.http.post("/api/isa/request/getHeaderCount",{oid:e})}getRequestBasedOnStatus(e,t){return this.http.post("/api/isa/request/getRequestBasedOnStatus",{oid:e,status:t})}getDataTypeArray(){return this.http.post("/api/isa/request/getISAStatusMasterDataUDRF",{})}sendMsg(e){this.msg.next(e)}getMsg(){return this.msg.asObservable()}getTaskStatusColor(){return new Promise((e,t)=>{this.http.post("/api/isa/request/getTaskStatusNameColor",{}).subscribe(t=>(this.taskStatusColor=t,e(t)),e=>{})})}getAllRequestsForRmg(e){return this.http.post("/api/isa/request/getAllRequestsForRmg",{filterConfig:e})}getTotalForRmg(e){return new Promise((t,n)=>{this.http.post("/api/isa/request/getTotalForRmg",{filterConfig:e}).subscribe(e=>t(e),e=>{})})}getDefaultISAStatusToDisplay(){return this.http.post("/api/isa/request/getISADefaultStatusToDisplay",{})}getCTCbreakUpForSkillSet(e){return new Promise((t,n)=>{this.http.post("/api/isa/request/getCTCbreakUpForSkillSet",e).subscribe(e=>t(e.data),e=>{n(e)})})}getCurrentUserRole(){return this.roleService&&this.roleService.roles&&this.roleService.roles.length>0?this.roleService.roles[0].role_id:null}getTemplateForUser(e){return this.http.post("/api/isa/request/getVisibleFormFieldsForUser",e)}saveAndApproveRequest(e){return this.http.post("/api/isa/request/updBudgetInfoByRequestId",e)}getVisibilityMatrix(e){return this.http.post("/api/isa/request/getAuthorzedFieldsByRoleId",e)}getISAAttachmentFromS3(e){return this.http.post("/api/isa/attachment/getISAAttachmentFromS3",{key:e})}getSkillType(){return this.http.post("/api/isa/request/getSkillType",{})}getSkillLevel(){return this.http.post("/api/isa/request/getSkillLevel",{})}getSkillExperience(){return this.http.post("/api/isa/request/getSkillExperience",{})}getSkillName(e){return this.http.post("/api/isa/request/getSkillName",{skill_type_id:e})}getIsaActivityReportDownload(){return this.http.post("/api/isa/request/getIsaActivityReportDownload",{})}getISASLAreport(e){return this.http.post("/api/isa/request/getISASLAreport",{filterConfig:e})}getISACustomReport(e){return this.http.post("/api/isa/request/getISACustomReport",{filterConfig:e})}getPositionType(){return this.http.post("/api/employee360/masterData/getPositionType",{})}calculateNetCostBasedOnPosition(e,t,n,i,a,r,s){return Object(l.c)(this,void 0,void 0,(function*(){let l;l=r&&r.length>1&&(yield this.getManpowerCostByOId(r,n,a,2))||(yield this.getManpowerCostBasedOnPosition(e,t,n,a,s));let c=yield this.getNonManpowerCost(t,n,i,a,2),d=yield this.getAllocatedCost(),m=0;m=(l?l.cost:0)+c.length>0?o.reduce(o.pluck(c,"cost"),(e,t)=>e+t,0):0;let p=d.length>0?o.reduce(o.pluck(d,"percentage"),(e,t)=>e+t,0):0;return{cost:m,currency:l&&l.currency_code?l.currency_code:"",manpowerCost:l,nonManpowerCost:c,allocatedCost:d,allocatedCostValue:m*(p/100)}}))}getManpowerCostBasedOnPosition(e,t,n,l,i){return new Promise((o,a)=>{this.http.post("/api/isa/configuration/getManpowerCostBasedOnPosition",{skillRoleId:e,nationalityId:t,locationGroupId:n,unit:l,position:i}).subscribe(e=>o(e),e=>(console.log(e),a(e)))})}getNonManpowerCost(e,t,n,l,i){return new Promise((o,a)=>{this.http.post("/api/project/getNonManpowerCost",{nationalityId:e,locationGroupId:t,locationId:n,unit:l,currency_id:i}).subscribe(e=>o(e),e=>(console.log(e),a(e)))})}getAllocatedCost(){return new Promise((e,t)=>{this.http.post("/api/project/getAllocatedCost","").subscribe(t=>e(t),e=>(console.log(e),t(e)))})}getManpowerCostByOId(e,t,n,l){return new Promise((i,o)=>{this.http.post("/api/project/getEmployeeSalary",{oid:e,location_group_id:t,unit_id:n,currency_id:l}).subscribe(e=>i(e),e=>(console.log(e),o(e)))})}getSkillNameMaster(){return this.http.post("/api/isa/configuration/getSkillName",{})}getVendorOid(e,t){return this.http.post("/api/isa/request/getVendorOid",{vendorNames:e,docID:t})}getVendorList(e,t){return this.http.post("/api/isa/request/getVendorList",{documentID:e,limitval:t})}removeVendor(e,t){return this.http.post("/api/isa/request/removeVendors",{vendorOid:e,documentID:t})}checkVendorOrNot(){return this.http.post("/api/isa/request/checkVendorOrNot",{})}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275inject"](r.c),a["\u0275\u0275inject"](s.a),a["\u0275\u0275inject"](c.a),a["\u0275\u0275inject"](d.a))},e.\u0275prov=a["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},m5YA:function(e,t,n){"use strict";n.d(t,"a",(function(){return f}));var l=n("0IaG"),i=n("fXoL"),o=n("tk/3"),a=n("bTqV"),r=n("NFeN"),s=n("Qu3c"),c=n("ofXK"),d=n("w4ga");function m(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div"),i["\u0275\u0275elementStart"](1,"div",8),i["\u0275\u0275element"](2,"img",9),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](3,"div",8),i["\u0275\u0275text"](4," Please wait, we are opening the file! "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]())}function p(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"ngx-doc-viewer",10),i["\u0275\u0275listener"]("loaded",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"]().isLoaded=!0})),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275property"]("url",e.data.selectedFileUrl)}}function u(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",11),i["\u0275\u0275element"](1,"img",12),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("src",e.data.selectedFileUrl,i["\u0275\u0275sanitizeUrl"])}}let f=(()=>{class e{constructor(e,t,n){this.data=e,this.dialogRef=t,this.http=n,this.isLoaded=!1,this.expHeaderId=e.expHeaderId}ngOnInit(){}getBlobUrl(e){return this.http.get(e,{responseType:"blob"})}downloadFile(e){if(this.expHeaderId){let t=e.substring(e.lastIndexOf("/")+1,e.indexOf("?"));this.getBlobUrl(e).subscribe(e=>{let n=URL.createObjectURL(e),l=document.createElement("a");l.href=n;let i=decodeURIComponent(t.replace(/%20/g," "));l.download="EX"+this.expHeaderId+"-"+i,l.click()})}else window.open(e)}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](l.a),i["\u0275\u0275directiveInject"](l.h),i["\u0275\u0275directiveInject"](o.c))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["attachment-doc-viewer"]],decls:13,vars:3,consts:[[1,"row"],[1,"col-10"],[1,"col-2"],["mat-icon-button","",3,"click"],["mat-icon-button","","matTooltip","Close",3,"click"],[4,"ngIf"],["viewer","google","style","height: 100%",3,"url","loaded",4,"ngIf","ngIfElse"],["showimg",""],[1,"d-flex","align-items-center","justify-content-center"],["src","https://assets.kebs.app/images/spinner.svg","alt",""],["viewer","google",2,"height","100%",3,"url","loaded"],[1,"d-flex","align-items-center","justify-content-center","mt-5"],["alt","",2,"height","80%","width","80%",3,"src"]],template:function(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275element"](1,"div",1),i["\u0275\u0275elementStart"](2,"div",2),i["\u0275\u0275elementStart"](3,"button",3),i["\u0275\u0275listener"]("click",(function(){return t.downloadFile(t.data.selectedFileUrl)})),i["\u0275\u0275elementStart"](4,"mat-icon"),i["\u0275\u0275text"](5,"download"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](6,"button",4),i["\u0275\u0275listener"]("click",(function(){return t.dialogRef.close()})),i["\u0275\u0275elementStart"](7,"mat-icon"),i["\u0275\u0275text"](8,"close"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](9,m,5,0,"div",5),i["\u0275\u0275template"](10,p,1,1,"ngx-doc-viewer",6),i["\u0275\u0275template"](11,u,2,1,"ng-template",null,7,i["\u0275\u0275templateRefExtractor"])),2&e){const e=i["\u0275\u0275reference"](12);i["\u0275\u0275advance"](9),i["\u0275\u0275property"]("ngIf",!t.isLoaded&&"png"!=t.data.fileFormat&&"jpg"!=t.data.fileFormat&&"jpeg"!=t.data.fileFormat&&"JPEG"!=t.data.fileFormat&&"JPG"!=t.data.fileFormat),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf","png"!=t.data.fileFormat&&"jpg"!=t.data.fileFormat&&"JPEG"!=t.data.fileFormat&&"JPG"!=t.data.fileFormat&&"jpeg"!=t.data.fileFormat)("ngIfElse",e)}},directives:[a.a,r.a,s.a,c.NgIf,d.a],styles:[""]}),e})()},ucYs:function(e,t,n){"use strict";n.d(t,"a",(function(){return s}));var l=n("mrSG"),i=n("xG9w"),o=n("fXoL"),a=n("tk/3"),r=n("BVzC");let s=(()=>{class e{constructor(e,t){this.http=e,this.errorService=t,this.workflowStatusList=[],this.getWorkflowStatusList()}getWorkflowStatusList(){this.http.post("/api/wfPrimary/getWorkflowStatusList","").subscribe(e=>{this.workflowStatusList=e.data},e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server While Getting Workflow Status List",e&&e.params?e.params:e&&e.error?e.error.params:{})})}getWorkflowProperties(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/getWorkflowProperties",{applicationId:e}).subscribe(e=>t(e),e=>n(e))})}getWorkflowPropertiesByWorkflowId(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/getWorkflowPropertiesByWorkflowId",{workflowId:e}).subscribe(e=>t(e),e=>n(e))})}getApproversHierarchy(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/getApproversHierarchy",e).subscribe(e=>t(e),e=>n(e))})}createWorkflowItems(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/createWorkflowItems",e).subscribe(e=>t(e),e=>n(e))})}getWorkflowDetails(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/getWorkflowDetails",{workflowId:e}).subscribe(e=>t(e),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while getting workflow details",e&&e.params?e.params:e&&e.error?e.error.params:{})})})}formatApproversHierarchy(e,t){return Object(l.c)(this,void 0,void 0,(function*(){0==e.length&&t.cc0&&t.cc0.appr0&&t.cc0.appr0.length>0&&e.push([{approvalType:"I",purposeOfInsertingIntoArray:"Just to ensure that 'I' type data is used, if it exists"}]);for(let n=0;n<e.length;n++){let l=[],o=i.keys(t["cc"+n]);for(let i=0;i<o.length;i++)for(let a=0;a<t["cc"+n][o[i]].length;a++){let r={name:t["cc"+n][o[i]][a].DELEGATE_NAME,oid:t["cc"+n][o[i]][a].DELEGATE_OID,level:i+1,designation:t["cc"+n][o[i]][a].DELEGATE_DESIGNATION_NAME,is_delegated:t["cc"+n][o[i]][a].IS_DELEGATED,role:t["cc"+n][o[i]][a].DELEGATE_ROLE_NAME};if(1==t["cc"+n][o[i]][a].IS_DELEGATED&&(r.delegated_by={name:t["cc"+n][o[i]][a].APPROVER_NAME,oid:t["cc"+n][o[i]][a].APPROVER_OID,level:i+1,designation:t["cc"+n][o[i]][a].APPROVER_DESIGNATION_NAME}),l.push(r),n==e.length-1&&i==o.length-1&&a==t["cc"+n][o[i]].length-1)return l}}}))}storeComments(e,t,n){return new Promise((l,i)=>{this.http.post("/api/wfPrimary/updateComments",{workflowHeaderId:e,newComments:t,commentor:n}).subscribe(e=>l(e),e=>(this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server Error while Updating Workflow Comments",e&&e.params?e.params:e&&e.error?e.error.params:{}),i(e)))})}updateWorkflowItems(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/updateWorkflowItems",e).subscribe(e=>t(e),e=>(console.log(e),n(e)))})}formatApproversHierarchyForOpportunityApprovalActivity(e){return Object(l.c)(this,void 0,void 0,(function*(){for(let t=0;t<1;t++){let n=[],l=i.keys(e["cc"+t]);for(let i=0;i<l.length;i++)for(let o=0;o<e["cc"+t][l[i]].length;o++){let a={name:e["cc"+t][l[i]][o].DELEGATE_NAME,oid:e["cc"+t][l[i]][o].DELEGATE_OID,level:e["cc"+t][l[i]][o].APPROVAL_ORDER,designation:e["cc"+t][l[i]][o].DELEGATE_DESIGNATION_NAME,is_delegated:e["cc"+t][l[i]][o].IS_DELEGATED};if(1==e["cc"+t][l[i]][o].IS_DELEGATED&&(a.delegated_by={name:e["cc"+t][l[i]][o].APPROVER_NAME,oid:e["cc"+t][l[i]][o].APPROVER_OID,level:e["cc"+t][l[i]][o].APPROVAL_ORDER,designation:e["cc"+t][l[i]][o].APPROVER_DESIGNATION_NAME}),n.push(a),i==l.length-1&&o==e["cc"+t][l[i]].length-1)return n}}}))}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275inject"](a.c),o["\u0275\u0275inject"](r.a))},e.\u0275prov=o["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()}}]);