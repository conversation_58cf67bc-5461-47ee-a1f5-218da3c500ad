(window.webpackJsonp=window.webpackJsonp||[]).push([[988,634,858,987,990,991],{HmYF:function(e,t,o){"use strict";o.d(t,"a",(function(){return c}));var r=o("mrSG"),n=o("Iab2"),i=o("EUZL"),s=o("wd/R"),l=o("xG9w"),a=o("fXoL");let c=(()=>{class e{constructor(){this.formatColumn=(e,t,o)=>{const r=i.utils.decode_range(e["!ref"]);for(let n=r.s.r+1;n<=r.e.r;++n){const r=i.utils.encode_cell({r:n,c:t});e[r]&&e[r].v&&(e[r].t="d",e[r].z=o)}}}exportAsExcelFile(e,t,o,r,n){console.log("Excel to JSON Service",e);const s=i.utils.json_to_sheet(e);if(n&&n.length){const e=i.utils.sheet_to_json(s,{header:1}).shift();for(const t of n){const o=e.indexOf(t.fieldKey);this.formatColumn(s,o,t.fieldFormat)}}null==o&&(o=[]),null==r&&(r="DD-MM-YYYY"),this.formatExcelDateData(s,o,r);const l=i.write({Sheets:{data:s},SheetNames:["data"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(l,t)}formatExcelDateData(e,t,o){for(let i of Object.keys(e))if(null!=e[i]&&null!=e[i].t&&null!=e[i].v&&s(e[i].v,o,!0).isValid()){let r=i.replace(/[0-9]/g,"")+"1";0==l.where(t,{value:e[r].v}).length&&null!=e[r]&&null!=e[r].t&&t.push({value:e[r].v,format:o})}let r=[],n=1;for(let i of t)for(let t of Object.keys(e)){let o=parseInt(t.replace(/[^0-9]/g,""));o>n&&(n=o),null!=e[t]&&null!=e[t].v&&e[t].v==i.value&&r.push({value:t.replace(/[0-9]/g,""),format:i.format})}for(let i of r)for(let t=2;t<=n;t++)null!=e[i.value+""+t]&&null!=e[i.value+""+t].t&&(e[i.value+""+t].t="d",null!=e[i.value+""+t].v&&"Invalid date"!=e[i.value+""+t].v?e[i.value+""+t].v=s(e[i.value+""+t].v,i.format).format("YYYY/MM/DD"):(console.log(e[i.value+""+t].t),e[i.value+""+t].v="",e[i.value+""+t].t="s"))}saveAsExcelFile(e,t){const o=new Blob([e],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8"});n.saveAs(o,t.concat(".xlsx"))}exportAsExcelFileWFH(e,t,o){const r=i.utils.json_to_sheet(e),n=i.utils.json_to_sheet(t),s=i.write({Sheets:{All_Approvals:r,Pending_Approvals:n},SheetNames:["All_Approvals","Pending_Approvals"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(s,o)}exportAsExcelFileForPayroll(e,t,o,r,n,s){const l=i.utils.json_to_sheet(e),a=i.utils.json_to_sheet(t),c=i.utils.json_to_sheet(o),p=i.utils.json_to_sheet(r),d=i.utils.json_to_sheet(n),u=i.write({Sheets:{Regular_Report:l,Intern_Report:a,Contract_Report:c,Perdiem_Report:p,RP_Report:d},SheetNames:["Regular_Report","Intern_Report","Contract_Report","Perdiem_Report","RP_Report"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(u,s)}exportAsCsvFileWithSheetName(e,t){return Object(r.c)(this,void 0,void 0,(function*(){let o=i.utils.book_new();for(let t of e){let e=i.utils.json_to_sheet(t.data);i.utils.book_append_sheet(o,e,t.sheetName)}let r=i.write(o,{bookType:"xlsx",type:"binary"});yield this.saveAsCsvFile(r,t)}))}saveAsCsvFile(e,t){return Object(r.c)(this,void 0,void 0,(function*(){const o=new Blob([yield this.s2ab(e)],{type:"application/octet-stream"});n.saveAs(o,t.concat(".csv"))}))}s2ab(e){return Object(r.c)(this,void 0,void 0,(function*(){for(var t=new ArrayBuffer(e.length),o=new Uint8Array(t),r=0;r<e.length;r++)o[r]=255&e.charCodeAt(r);return t}))}exportAsExcelFileWithCellMerge(e,t,o){const r=i.utils.json_to_sheet(e);r["!merges"]=o;const n=i.write({Sheets:{data:r},SheetNames:["data"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(n,t)}exportJsonToExcelWithMultipleSheets(e,t){return Object(r.c)(this,void 0,void 0,(function*(){let o=i.utils.book_new();for(let t of e){let e=i.utils.json_to_sheet(t.data);i.utils.book_append_sheet(o,e,t.sheetName)}let r=i.write(o,{bookType:"xlsx",type:"array"});this.saveAsExcelFile(r,t)}))}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275prov=a["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},NJ67:function(e,t,o){"use strict";o.d(t,"a",(function(){return r}));class r{constructor(){this.disabled=!1}onChange(e){}onTouched(e){}writeValue(e){this.value=e}registerOnChange(e){this.onChange=e}registerOnTouched(e){this.onTouched=e}setDisabledState(e){this.disabled=e}}},"Xtw+":function(e,t,o){"use strict";o.r(t),o.d(t,"MigrationCockpitModalComponent",(function(){return c})),o("ofXK");var r=o("bTqV"),n=o("NFeN"),i=o("Qu3c"),s=(o("Xi0T"),o("fXoL")),l=o("0IaG"),a=o("mbKZ");let c=(()=>{class e{constructor(e){this.dialogRef=e}ngOnInit(){}closeModal(){this.dialogRef.close({event:"Close"})}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275directiveInject"](l.h))},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["migration-cockpit-modal"]],decls:13,vars:0,consts:[[1,"container-fluid","migration-cockpit-modal-styles"],[1,"row","pt-2","pb-2",2,"border-bottom","solid 1px #cacaca"],[1,"col-1","pl-0","pr-0"],["mat-icon-button","",1,"bubble","alignCenter"],[1,"iconButton"],[1,"col-10","pl-0","pr-0","name","alignVerticalCenter"],["mat-icon-button","","matTooltip","Close",1,"d-flex","ml-auto","mr-2","close-button",3,"click"],[1,"close-icon"]],template:function(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"div",0),s["\u0275\u0275elementStart"](1,"div",1),s["\u0275\u0275elementStart"](2,"div",2),s["\u0275\u0275elementStart"](3,"div",3),s["\u0275\u0275elementStart"](4,"mat-icon",4),s["\u0275\u0275text"](5,"cloud_upload"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](6,"div",5),s["\u0275\u0275text"](7,"Migration Cockpit"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](8,"div",2),s["\u0275\u0275elementStart"](9,"button",6),s["\u0275\u0275listener"]("click",(function(){return t.closeModal()})),s["\u0275\u0275elementStart"](10,"mat-icon",7),s["\u0275\u0275text"](11,"close"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275element"](12,"migration-cockpit"),s["\u0275\u0275elementEnd"]())},directives:[n.a,r.a,i.a,a.a],styles:[".migration-cockpit-modal-styles[_ngcontent-%COMP%]{background-size:200px 181px;background-repeat:no-repeat;overflow-y:hidden!important;min-height:95vh;background-position:99% 105%;padding-left:0!important;padding-right:0!important}.migration-cockpit-modal-styles[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]{display:flex;line-height:26px;border-radius:50%;cursor:context-menu!important;width:26px;height:26px;background-color:#fbfbfb;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.migration-cockpit-modal-styles[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#cf0001;font-size:18px;margin-top:4px!important;margin-left:3px!important}.migration-cockpit-modal-styles[_ngcontent-%COMP%]   .alignCenter[_ngcontent-%COMP%]{margin:0 auto;display:block}.migration-cockpit-modal-styles[_ngcontent-%COMP%]   .alignVerticalCenter[_ngcontent-%COMP%]{display:flex;align-items:center}.migration-cockpit-modal-styles[_ngcontent-%COMP%]   .name[_ngcontent-%COMP%]{font-size:14px;color:#cf0001;font-family:Roboto;font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.migration-cockpit-modal-styles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:30px!important;height:30px!important;line-height:30px!important}.migration-cockpit-modal-styles[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}"]}),e})()},ucYs:function(e,t,o){"use strict";o.d(t,"a",(function(){return a}));var r=o("mrSG"),n=o("xG9w"),i=o("fXoL"),s=o("tk/3"),l=o("BVzC");let a=(()=>{class e{constructor(e,t){this.http=e,this.errorService=t,this.workflowStatusList=[],this.getWorkflowStatusList()}getWorkflowStatusList(){this.http.post("/api/wfPrimary/getWorkflowStatusList","").subscribe(e=>{this.workflowStatusList=e.data},e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server While Getting Workflow Status List",e&&e.params?e.params:e&&e.error?e.error.params:{})})}getWorkflowProperties(e){return new Promise((t,o)=>{this.http.post("/api/wfPrimary/getWorkflowProperties",{applicationId:e}).subscribe(e=>t(e),e=>o(e))})}getWorkflowPropertiesByWorkflowId(e){return new Promise((t,o)=>{this.http.post("/api/wfPrimary/getWorkflowPropertiesByWorkflowId",{workflowId:e}).subscribe(e=>t(e),e=>o(e))})}getApproversHierarchy(e){return new Promise((t,o)=>{this.http.post("/api/wfPrimary/getApproversHierarchy",e).subscribe(e=>t(e),e=>o(e))})}createWorkflowItems(e){return new Promise((t,o)=>{this.http.post("/api/wfPrimary/createWorkflowItems",e).subscribe(e=>t(e),e=>o(e))})}getWorkflowDetails(e){return new Promise((t,o)=>{this.http.post("/api/wfPrimary/getWorkflowDetails",{workflowId:e}).subscribe(e=>t(e),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while getting workflow details",e&&e.params?e.params:e&&e.error?e.error.params:{})})})}formatApproversHierarchy(e,t){return Object(r.c)(this,void 0,void 0,(function*(){0==e.length&&t.cc0&&t.cc0.appr0&&t.cc0.appr0.length>0&&e.push([{approvalType:"I",purposeOfInsertingIntoArray:"Just to ensure that 'I' type data is used, if it exists"}]);for(let o=0;o<e.length;o++){let r=[],i=n.keys(t["cc"+o]);for(let n=0;n<i.length;n++)for(let s=0;s<t["cc"+o][i[n]].length;s++){let l={name:t["cc"+o][i[n]][s].DELEGATE_NAME,oid:t["cc"+o][i[n]][s].DELEGATE_OID,level:n+1,designation:t["cc"+o][i[n]][s].DELEGATE_DESIGNATION_NAME,is_delegated:t["cc"+o][i[n]][s].IS_DELEGATED,role:t["cc"+o][i[n]][s].DELEGATE_ROLE_NAME};if(1==t["cc"+o][i[n]][s].IS_DELEGATED&&(l.delegated_by={name:t["cc"+o][i[n]][s].APPROVER_NAME,oid:t["cc"+o][i[n]][s].APPROVER_OID,level:n+1,designation:t["cc"+o][i[n]][s].APPROVER_DESIGNATION_NAME}),r.push(l),o==e.length-1&&n==i.length-1&&s==t["cc"+o][i[n]].length-1)return r}}}))}storeComments(e,t,o){return new Promise((r,n)=>{this.http.post("/api/wfPrimary/updateComments",{workflowHeaderId:e,newComments:t,commentor:o}).subscribe(e=>r(e),e=>(this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server Error while Updating Workflow Comments",e&&e.params?e.params:e&&e.error?e.error.params:{}),n(e)))})}updateWorkflowItems(e){return new Promise((t,o)=>{this.http.post("/api/wfPrimary/updateWorkflowItems",e).subscribe(e=>t(e),e=>(console.log(e),o(e)))})}formatApproversHierarchyForOpportunityApprovalActivity(e){return Object(r.c)(this,void 0,void 0,(function*(){for(let t=0;t<1;t++){let o=[],r=n.keys(e["cc"+t]);for(let n=0;n<r.length;n++)for(let i=0;i<e["cc"+t][r[n]].length;i++){let s={name:e["cc"+t][r[n]][i].DELEGATE_NAME,oid:e["cc"+t][r[n]][i].DELEGATE_OID,level:e["cc"+t][r[n]][i].APPROVAL_ORDER,designation:e["cc"+t][r[n]][i].DELEGATE_DESIGNATION_NAME,is_delegated:e["cc"+t][r[n]][i].IS_DELEGATED};if(1==e["cc"+t][r[n]][i].IS_DELEGATED&&(s.delegated_by={name:e["cc"+t][r[n]][i].APPROVER_NAME,oid:e["cc"+t][r[n]][i].APPROVER_OID,level:e["cc"+t][r[n]][i].APPROVAL_ORDER,designation:e["cc"+t][r[n]][i].APPROVER_DESIGNATION_NAME}),o.push(s),n==r.length-1&&i==e["cc"+t][r[n]].length-1)return o}}}))}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275inject"](s.c),i["\u0275\u0275inject"](l.a))},e.\u0275prov=i["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()}}]);