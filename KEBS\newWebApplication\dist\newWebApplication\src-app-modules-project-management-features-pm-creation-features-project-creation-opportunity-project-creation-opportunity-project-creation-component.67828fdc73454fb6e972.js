(window.webpackJsonp=window.webpackJsonp||[]).push([[972],{"1RYl":function(e,t,i){"use strict";i.d(t,"a",(function(){return o}));var s=i("xG9w"),r=i("fXoL");let o=(()=>{class e{transform(e,t,i){let r=s.findWhere(t,{field_name:e,type:i});return!!r&&!!r.info_icon}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=r["\u0275\u0275definePipe"]({name:"checkInfoIcon",type:e,pure:!0}),e})()},"8EZa":function(e,t,i){"use strict";i.d(t,"a",(function(){return p}));var s=i("mrSG"),r=i("xG9w"),o=i("fXoL"),n=i("tk/3"),c=i("flaP"),l=i("1A3m"),a=i("XXEo");let p=(()=>{class e{constructor(e,t,i,s){this.http=e,this.rolesService=t,this.toaster=i,this.loginService=s,this.projectRoleAccessList=[],this.roleList=[],this.mainAccess=[],this.projectEmployeeRole=[],this.projectListAccess=[],this.status_list=[],this.checkProjectApplicationAccess(),this.getProjectOverallAccess(),this.getUserRoleAccessProjectList(),this.getStatusList()}getProjectRoleAccess(){return new Promise((e,t)=>{if(this.projectRoleAccessList&&this.projectRoleAccessList.length>0)return e(this.projectRoleAccessList);this.http.post("/api/pm/auth/getProjectRoleAccessList",{}).subscribe(t=>(this.projectRoleAccessList=t,e(t)),e=>t(e))})}checkProjectApplicationAccess(){return new Promise((e,t)=>Object(s.c)(this,void 0,void 0,(function*(){if(this.rolesService.roles&&this.rolesService.roles.length>0){let t=r.where(this.rolesService.roles,{application_id:915});this.projectEmployeeRole=t;let i=r.where(this.projectEmployeeRole,{object_id:6});return e(i.length>0)}{const t=this.loginService.getProfile().profile;yield this.getAccessList(t.oid,"project").subscribe(t=>{let i=r.where(t,{application_id:915});this.projectEmployeeRole=i;let s=r.where(this.projectEmployeeRole,{object_id:6});return e(s.length>0)})}})))}checkProjectRoleAccess(e){let t=r.where(this.projectEmployeeRole,{application_id:915,object_id:6});if(t.length>0){if("*"==t[0].object_value)return{messType:"S",message:"Admin access enabled!",data:this.getAccessTopPriority([1],e)};if("True"==t[0].object_value){let i="string"==typeof t[0].object_entries?JSON.parse(t[0].object_entries):t[0].object_entries;return"null"==i||"*"==i||null==i||"null"==i?{messType:"S",message:"Team Member Access enabled",data:this.getAccessTopPriority([9],e)}:{messType:"S",message:"Project Role Access enabled",data:this.getAccessTopPriority(i,e)}}return{messType:"E",message:"Not authorized!",data:this.getAccessTopPriority([],e)}}return{messType:"E",message:"Not authorized!",data:this.getAccessTopPriority([],e)}}getAccessTopPriority(e,t){return r.filter(t,t=>{for(let i of e)if(t.id==i)return t})}getProjectRoleObjectAccess(){return Object(s.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>Object(s.c)(this,void 0,void 0,(function*(){this.projectRoleAccess&&"S"==this.projectRoleAccess.messType?e(this.projectRoleAccess):yield this.getProjectRoleAccess().then(t=>{let i=this.checkProjectRoleAccess(this.projectRoleAccessList);if("S"==i.messType){let t=i.data;0==t.length?(this.projectRoleAccess={messType:"E",message:"No access to Projects",access:!1,data:[]},e(this.projectRoleAccess)):(this.projectRoleAccess={messType:"S",message:"Access to Project",access:!0,data:t},e(this.projectRoleAccess))}else this.projectRoleAccess={messType:"E",message:"No access to Projects",access:!1,data:[]},e(this.projectRoleAccess)},e=>{this.toaster.showError("This action is not allowed!","Dismiss",3e3),t(!1)})})))}))}getProjectOverallAccess(){return new Promise((e,t)=>Object(s.c)(this,void 0,void 0,(function*(){if(this.roleList&&this.roleList.length>0)return e(this.roleList);yield this.getProjectRoleObjectAccess().then(i=>{if("S"!=i.messType)return e([]);if(!(i.data.length>0))return e([]);{let s=r.pluck(i.data,"id");if(!(s.length>0))return e([]);this.http.post("/api/pm/auth/getProjectOverallAccess",{mainAccess:s}).subscribe(t=>(console.log(this.roleList),this.roleList=t,e(t)),e=>t(e))}})})))}getProjectObjectAccess(e){return r.where(this.roleList,{object_id:e}).length>0}getEmployeeRoleObjectAccess(e){return r.where(this.projectEmployeeRole,{object_id:e}).length>0}getUserRoleAccessProjectList(){return new Promise((e,t)=>Object(s.c)(this,void 0,void 0,(function*(){this.projectListAccess&&this.projectListAccess.length>0?e(this.projectListAccess):yield this.getProjectRoleObjectAccess().then(i=>"S"!=i.messType?e([]):i.data.length>0?void this.http.post("/api/pm/auth/getUserRoleAccessProjectList",{mainAccess:i.data}).subscribe(t=>(this.projectListAccess=t,e(t)),e=>t(e)):e([]))})))}getAccessList(e,t){return this.http.post("/api/pm/auth/getAccessFor",{oid:e,type:t})}getReadWriteAccess(e,t){return new Promise((i,o)=>Object(s.c)(this,void 0,void 0,(function*(){yield this.getUserRoleAccessProjectList().then(o=>Object(s.c)(this,void 0,void 0,(function*(){yield this.getStatusList().then(s=>{let n=r.where(o,{item_id:t,project_id:e,object_access:"Both"});if(n.length>0){let e=r.where(s,{id:n[0].item_status_id,object_access:"Both"});i(e.length>0)}else i(!1)})})),e=>{i(!0)})})))}getAdminAccess(){return new Promise((e,t)=>Object(s.c)(this,void 0,void 0,(function*(){this.http.post("/api/pm/auth/getAdminAccessList",{}).subscribe(t=>e(t),e=>t(e))})))}getStatusList(){return new Promise((e,t)=>{if(this.status_list&&this.status_list.length>0)return e(this.status_list);this.http.post("/api/pm/masterData/getStatusList",{}).subscribe(t=>(this.status_list=t,e(this.status_list)),e=>t(e))})}getProjectWiseObjectAccess(e,t,i){return Object(s.c)(this,void 0,void 0,(function*(){let s=r.pluck(r.where(this.projectListAccess,{project_id:e,item_id:t}),"role_access_id"),o=yield this.getSuperiorRole(s);return r.where(this.roleList,{role_id:o,object_id:i}).length>0}))}getSuperiorRole(e){let t=r.filter(this.projectRoleAccessList,t=>{if(r.contains(e,t.id))return t});return r.sortBy(t,"sequence_list")[0].id}updateProjectStatus(e,t,i){for(let s of this.projectListAccess)s.project_id==e&&s.item_id==t&&(s.item_status_id=i)}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275inject"](n.c),o["\u0275\u0275inject"](c.a),o["\u0275\u0275inject"](l.a),o["\u0275\u0275inject"](a.a))},e.\u0275prov=o["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},Bns8:function(e,t,i){"use strict";i.d(t,"a",(function(){return o}));var s=i("xG9w"),r=i("fXoL");let o=(()=>{class e{transform(e,t,i,r){let o=s.findWhere(t,{field_name:e,type:i});return o&&o.tooltip?o.tooltip:r}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=r["\u0275\u0275definePipe"]({name:"checkTooltip",type:e,pure:!0}),e})()},NOI7:function(e,t,i){"use strict";i.d(t,"a",(function(){return o}));var s=i("xG9w"),r=i("fXoL");let o=(()=>{class e{transform(e,t,i){let r=s.findWhere(t,{field_name:e,type:i});return!!r&&!!r.is_mandant}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=r["\u0275\u0275definePipe"]({name:"checkMandatedField",type:e,pure:!0}),e})()},R898:function(e,t,i){"use strict";i.d(t,"a",(function(){return o}));var s=i("xG9w"),r=i("fXoL");let o=(()=>{class e{transform(e,t,i,r){let o=s.findWhere(t,{field_name:e,type:i});return o&&o.label?o.label:r}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=r["\u0275\u0275definePipe"]({name:"checkLabel",type:e,pure:!0}),e})()},q0ZZ:function(e,t,i){"use strict";i.d(t,"a",(function(){return n}));var s=i("fXoL"),r=i("3Pt+"),o=i("1A3m");let n=(()=>{class e{constructor(e,t){this.ngControl=e,this.toastr=t,this.allowDecimal=!1,this.notAllowZero=!0,this.isPercentage=!1,this.isNegative=!1,this.digitsAllowed=5,this.decimalsAllowed=null,this.el=e}onInput(e){var t,i,s,r,o,n,c,l,a,p,u,h,d,g;if(this.isPercentage&&this.allowDecimal){if("-"==(e=this.isNegative?e.replace(/[^0-9.-]|(?!^)-/g,"").replace(/-00$/,"-0"):e.replace(/[^0-9.]/g,"")))return void(null===(t=this.el.control)||void 0===t||t.patchValue("-"));if(""!=e){let t=e.split(".");t.length>1?e=(""!=t[0]?parseInt(t[0])+"":"0")+"."+t.slice(1).join("").substring(0,this.digitsAllowed):1==t.length&&(e=""!=t[0]?"-"==t[0]||"-0"==t[0]?"-0":parseInt(t[0])+"":"0");try{parseFloat(e)>100?(null===(i=this.el.control)||void 0===i||i.patchValue(100),this.toastr.showWarning("Warning","Percentage Limit Reached")):null===(s=this.el.control)||void 0===s||s.patchValue(e)}catch(m){console.log(m)}}else null===(r=this.el.control)||void 0===r||r.patchValue("")}else if(this.isPercentage){if("-"==(e=this.isNegative?e.replace(/[^0-9.-]|(?!^)-/g,"").replace(/-00$/,"-0"):e.replace(/[^0-9.]/g,"")))return void(null===(o=this.el.control)||void 0===o||o.patchValue("-"));if(""!=e)try{parseInt(e)>100?null===(n=this.el.control)||void 0===n||n.patchValue(100):(e=parseInt(e)+"",null===(c=this.el.control)||void 0===c||c.patchValue(e))}catch(m){console.log(m)}else null===(l=this.el.control)||void 0===l||l.patchValue("")}else if(this.allowDecimal){if("-"==(e=this.isNegative?e.replace(/[^0-9.-]|(?!^)-/g,"").replace(/-00$/,"-0"):e.replace(/[^0-9.]/g,"")))return void(null===(a=this.el.control)||void 0===a||a.patchValue("-"));if(""!=e){let t=e.split(".");t.length>1?e=this.decimalsAllowed?(""!=t[0]?"-"==t[0]?"0":t[0]+"":"0")+"."+t.slice(1).join("").substring(0,this.decimalsAllowed):(""!=t[0]?"-"==t[0]?"0":t[0]+"":"0")+"."+t.slice(1).join("").substring(0,this.digitsAllowed):1==t.length&&(e=(""!=t[0]?"-"==t[0]||"-0"==t[0]?"-0":parseInt(t[0])+"":"0").substring(0,this.digitsAllowed)),this.maxValue&&e>this.maxValue&&(e=this.maxValue),null===(p=this.el.control)||void 0===p||p.patchValue(e)}else null===(u=this.el.control)||void 0===u||u.patchValue("")}else{if("-"==(e=this.isNegative?e.replace(/[^0-9.-]|(?!^)-/g,"").replace(/-00$/,"-0"):e.replace(/[^0-9.]/g,"")))return void(null===(h=this.el.control)||void 0===h||h.patchValue("-"));if(""!=e)try{e="-"==e||"-0"==e?"-0":(parseInt(e)+"").substring(0,this.digitsAllowed),this.maxValue&&e>this.maxValue&&(e=this.maxValue),null===(d=this.el.control)||void 0===d||d.patchValue(e)}catch(m){console.log(m)}else null===(g=this.el.control)||void 0===g||g.patchValue("")}}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275directiveInject"](r.u),s["\u0275\u0275directiveInject"](o.a))},e.\u0275dir=s["\u0275\u0275defineDirective"]({type:e,selectors:[["","digitOnly",""]],hostBindings:function(e,t){1&e&&s["\u0275\u0275listener"]("input",(function(e){return t.onInput(e.target.value)}))},inputs:{allowDecimal:"allowDecimal",notAllowZero:"notAllowZero",isPercentage:"isPercentage",isNegative:"isNegative",digitsAllowed:"digitsAllowed",decimalsAllowed:"decimalsAllowed",maxValue:"maxValue"}}),e})()},uCSo:function(e,t,i){"use strict";i.d(t,"a",(function(){return o}));var s=i("fXoL"),r=i("tk/3");let o=(()=>{class e{constructor(e){this.http=e}getProjectCode(){return new Promise((e,t)=>{this.http.post("/api/pm/planning/getProjectCode",{}).subscribe(t=>e(t),e=>t(e))})}checkProjectCodeDuplication(e){return new Promise((t,i)=>{this.http.post("/api/pm/planning/checkProjectCodeDuplication",{code:e}).subscribe(e=>t(e),e=>i(e))})}getPortfolioList(){return new Promise((e,t)=>{this.http.post("/api/pm/planning/getPortfolioList",{}).subscribe(t=>e(t),e=>t(e))})}getResourceFromAccounts(e){return new Promise((t,i)=>{this.http.post("/api/pm/integration/getResourceFromAccounts",{customer_id:e}).subscribe(e=>t(e),e=>i(e))})}getFinancialValues(e){return new Promise((t,i)=>{this.http.post("/api/pm/planning/getFinancialValues",{opportunity_id:e}).subscribe(e=>t(e),e=>i(e))})}saveProjectDetails(e,t,i,s,r,o,n,c,l){return new Promise((a,p)=>{this.http.post("/api/pm/planning/saveProjectDetails",{data:e,selectedOption:t,week_array:i,fields:s,stakeholders:r,tags:o,code:n,intergeration:c,external_stakeholders:l}).subscribe(e=>a(e),e=>p(e))})}getBillingAddress(e){return new Promise((t,i)=>{this.http.post("/api/pm/planning/getBillingAddress",{id:e}).subscribe(e=>t(e),e=>i(e))})}getOpportunity(e,t,i){return new Promise((s,r)=>{this.http.post("/api/pm/planning/getOpportunity",{customer_id:e,status_list:t,mode:i}).subscribe(e=>s(e),e=>r(e))})}getItemOpportunity(e,t){return new Promise((i,s)=>{this.http.post("/api/pm/planning/getItemOpportunity",{project_id:e,project_item_id:t}).subscribe(e=>i(e),e=>s(e))})}getDOJ(e){return new Promise((t,i)=>{this.http.post("/api/pm/planning/getDOJ",{aid:e}).subscribe(e=>t(e),e=>i(e))})}getLocations(e,t){return new Promise((i,s)=>{this.http.post("/api/pm/planning/getLocations",{project_id:e,item_id:t}).subscribe(e=>i(e),e=>s(e))})}getGeneralSettings(e,t){return new Promise((i,s)=>{this.http.post("/api/pm/planning/getGeneralSettings",{project_id:e,item_id:t}).subscribe(e=>i(e),e=>s(e))})}getTagsProject(e){return new Promise((t,i)=>{this.http.post("/api/pm/planning/getTagsProject",{item_id:e}).subscribe(e=>t(e),e=>i(e))})}getProjectQuote(e,t){return new Promise((i,s)=>{this.http.post("/api/pm/planning/getProjectQuote",{project_id:e,item_id:t}).subscribe(e=>i(e),e=>s(e))})}updateProjectDetails(e,t,i,s,r,o,n,c,l,a,p,u,h){return new Promise((d,g)=>{this.http.post("/api/pm/planning/updateProjectDetails",{oldData:e,newData:t,data:i,selectedOption:s,week_array:r,fields:o,tags:n,item_id:c,code:l,intergeration:a,portfolioChanged:p,oldFinancialData:u,newFinancialData:h}).subscribe(e=>d(e),e=>g(e))})}getProjectFinnacialData(e){return new Promise((t,i)=>{this.http.post("/api/pm/planning/getProjectFinnacialData",{item_id:e}).subscribe(e=>t(e),e=>i(e))})}getOrgMapping(){return new Promise((e,t)=>{this.http.post("/api/project/v2/getEmployeeDirectoryOrgMapping",{}).subscribe(t=>e(t),e=>t(e))})}getMaxMinProjectDates(e,t){return new Promise((i,s)=>{this.http.post("/api/pm/planning/getMaxMinProjectDates",{project_id:e,item_id:t}).subscribe(e=>i(e),e=>s(e))})}getEditedOpportunity(e,t,i,s){return new Promise((r,o)=>{this.http.post("/api/pm/planning/getEditedOpportunity",{project_id:e,item_id:t,customer_id:i,status_list:s}).subscribe(e=>r(e),e=>o(e))})}getCustomerProjectCode(e){return new Promise((t,i)=>{this.http.post("/api/pm/planning/getCustomerProjectCode",{customer_id:e}).subscribe(e=>t(e),e=>i(e))})}getProjectOpportunityDetails(e){return this.http.post("/api/pm/report/getOpportunityCustomerDetails",{opportunityId:e})}getCustomerPortfolioList(e){return this.http.post("/api/pm/planning/getCustomerPortfolioList",{customer_id:e})}saveProjectFromOpportunity(e){return this.http.post("/api/pm/planning/saveOpportunityProjectCreation",{opc_payload:e})}getProjectCodeForCustomer(e){return this.http.post("/api/pm/planning/getCustomerProjectCode",{customer_id:e})}getProjectChildCustomerList(e){return this.http.post("/api/pm/planning/getChildCustomer",{customer_id:e})}saveOpportunityProjectCreation(e,t,i){return this.http.post("/api/pm/planning/saveOpportunityProjectCreation",{data:e,stakeholders:t,external_stakeholders:i})}getSumOFMilestoneValue(e){return new Promise((t,i)=>{this.http.post("/api/pm/planning/getSumOFMilestoneValue",{id:e}).subscribe(e=>t(e),e=>i(e))})}retrieveMilestoneId(e){return new Promise((t,i)=>{this.http.post("/api/pm/planning/retrieveMilestoneId",{itemID:e}).subscribe(e=>t(e),e=>i(e))})}getAllParentOpportunity(e){return new Promise((t,i)=>{this.http.post("/api/salesMaster/getAllParentOpportunities",{customer_id:e}).subscribe(e=>t(e),e=>i(e))})}getAllChildOpportunity(e){return new Promise((t,i)=>{this.http.post("/api/salesMaster/getAllChildOpportunity",{parentOpportunityId:[e]}).subscribe(e=>t(e),e=>i(e))})}getParentEditedOpportunity(e,t,i,s){return new Promise((r,o)=>{this.http.post("/api/pm/planning/getParentEditedOpportunity",{project_id:e,item_id:t,customer_id:i,status_list:s}).subscribe(e=>r(e),e=>o(e))})}checkSowReferenceNumberDuplication(e,t,i){return new Promise((s,r)=>{this.http.post("/api/pm/planning/checkSowReferenceNumberDuplication",{sowReferenceNumber:e,oprFlag:t,itemId:i}).subscribe(e=>s(e),e=>r(e))})}getGroupedMilestone(e){return new Promise((t,i)=>{this.http.post("/api/pm/planning/getGroupedMilestone",{item_id:e}).subscribe(e=>t(e),e=>i(e))})}getResourceFromOpportunity(e){return new Promise((t,i)=>{this.http.post("/api/opportunity/getOpportunityStakeHolders",{opportunityId:[e]}).subscribe(e=>t(e),e=>i(e))})}getProjectFinancialwithoutopp(e,t){return new Promise((i,s)=>{this.http.post("/api/pm/planning/getProjectFinancialwithoutopp",{project_id:e,item_id:t}).subscribe(e=>i(e),e=>s(e))})}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275inject"](r.c))},e.\u0275prov=s["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()}}]);