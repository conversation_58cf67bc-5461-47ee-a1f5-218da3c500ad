(window.webpackJsonp=window.webpackJsonp||[]).push([[667],{J7hc:function(t,e,i){"use strict";i.r(e),i.d(e,"TicketTasksComponent",(function(){return ct})),i.d(e,"TicketTasksModule",(function(){return dt}));var s=i("mrSG"),a=i("fXoL"),n=i("1G5W"),r=i("XNiG"),o=i("xG9w"),l=i("wd/R"),c=i("ofXK"),d=i("bTqV"),p=i("NFeN"),u=i("Qu3c"),h=i("0IaG"),m=i("3Pt+"),k=i("/1cH"),g=i("d3UM"),_=i("iadO"),f=i("FKr1"),y=i("Xa2L"),v=i("4/q7"),b=i("1yaQ"),I=i("xm0x"),T=i("3beV"),E=i("xYXA"),S=i("Xi0T"),C=i("TC2u"),w=i("yu80"),x=i("nAV5"),A=i("JLuW"),O=i("BVzC"),M=i("zcNR"),D=i("yArD"),P=i("mS9j"),R=i("AtTz"),q=i("82JS"),$=i("EcHI");function j(t,e){1&t&&(a["\u0275\u0275elementStart"](0,"div"),a["\u0275\u0275element"](1,"div",3),a["\u0275\u0275elementStart"](2,"div",4),a["\u0275\u0275element"](3,"mat-spinner",5),a["\u0275\u0275elementEnd"](),a["\u0275\u0275element"](4,"div",3),a["\u0275\u0275elementEnd"]())}function F(t,e){1&t&&(a["\u0275\u0275elementStart"](0,"div",15),a["\u0275\u0275text"](1," Actual hours "),a["\u0275\u0275elementEnd"]())}function L(t,e){}function N(t,e){if(1&t){const t=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"div",33),a["\u0275\u0275listener"]("click",(function(){a["\u0275\u0275restoreView"](t);const e=a["\u0275\u0275nextContext"]().$implicit;return a["\u0275\u0275nextContext"](2).activateInlineEdit("Task name","simple-text",[],e)})),a["\u0275\u0275template"](1,L,0,0,"ng-template",34),a["\u0275\u0275elementEnd"]()}if(2&t){a["\u0275\u0275nextContext"]();const t=a["\u0275\u0275reference"](8);a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngTemplateOutlet",t)}}function B(t,e){if(1&t&&a["\u0275\u0275text"](0),2&t){const t=a["\u0275\u0275nextContext"]().$implicit;a["\u0275\u0275textInterpolate1"](" ",null==t?null:t.task_name," ")}}function Y(t,e){}function V(t,e){if(1&t){const t=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"div",33),a["\u0275\u0275listener"]("click",(function(){a["\u0275\u0275restoreView"](t);const e=a["\u0275\u0275nextContext"]().$implicit,i=a["\u0275\u0275nextContext"](2);return i.activateInlineEdit("Status","minimal-dropdown",i.statusTypes,e)})),a["\u0275\u0275template"](1,Y,0,0,"ng-template",34),a["\u0275\u0275elementEnd"]()}if(2&t){a["\u0275\u0275nextContext"]();const t=a["\u0275\u0275reference"](12);a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngTemplateOutlet",t)}}const H=function(t){return{background:t}};function U(t,e){if(1&t&&(a["\u0275\u0275element"](0,"span",35),a["\u0275\u0275pipe"](1,"getCtaStatusColor"),a["\u0275\u0275elementStart"](2,"span",36),a["\u0275\u0275text"](3),a["\u0275\u0275elementEnd"]()),2&t){const t=a["\u0275\u0275nextContext"]().$implicit;a["\u0275\u0275property"]("ngStyle",a["\u0275\u0275pureFunction1"](4,H,a["\u0275\u0275pipeBind1"](1,2,null==t?null:t.task_status))),a["\u0275\u0275advance"](3),a["\u0275\u0275textInterpolate"](null==t?null:t.task_status)}}function K(t,e){}function z(t,e){if(1&t){const t=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"div",33),a["\u0275\u0275listener"]("click",(function(){a["\u0275\u0275restoreView"](t);const e=a["\u0275\u0275nextContext"]().$implicit;return a["\u0275\u0275nextContext"](2).activateInlineEdit("Due on","date-picker",[],e)})),a["\u0275\u0275template"](1,K,0,0,"ng-template",34),a["\u0275\u0275elementEnd"]()}if(2&t){a["\u0275\u0275nextContext"]();const t=a["\u0275\u0275reference"](16);a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngTemplateOutlet",t)}}function W(t,e){if(1&t&&(a["\u0275\u0275text"](0),a["\u0275\u0275pipe"](1,"date")),2&t){const t=a["\u0275\u0275nextContext"]().$implicit;a["\u0275\u0275textInterpolate1"](" ",null!=t&&t.end_date?a["\u0275\u0275pipeBind2"](1,1,null==t?null:t.end_date,"dd-MMM-yy"):"-"," ")}}function X(t,e){}function G(t,e){if(1&t){const t=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"div",33),a["\u0275\u0275listener"]("click",(function(){a["\u0275\u0275restoreView"](t);const e=a["\u0275\u0275nextContext"]().$implicit;return a["\u0275\u0275nextContext"](2).activateInlineEdit("Assigned to","search-dropdown",[],e)})),a["\u0275\u0275template"](1,X,0,0,"ng-template",34),a["\u0275\u0275elementEnd"]()}if(2&t){a["\u0275\u0275nextContext"]();const t=a["\u0275\u0275reference"](20);a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngTemplateOutlet",t)}}function J(t,e){if(1&t&&(a["\u0275\u0275element"](0,"app-user-profile",38),a["\u0275\u0275pipe"](1,"activeObject")),2&t){const t=a["\u0275\u0275nextContext"](2).$implicit;a["\u0275\u0275property"]("type","name")("oid",a["\u0275\u0275pipeBind2"](1,2,null==t?null:t.assigned_to,"employee_oid"))}}function Q(t,e){1&t&&(a["\u0275\u0275elementStart"](0,"span"),a["\u0275\u0275text"](1,"Not Assigned"),a["\u0275\u0275elementEnd"]())}function Z(t,e){if(1&t&&(a["\u0275\u0275template"](0,J,2,5,"app-user-profile",37),a["\u0275\u0275template"](1,Q,2,0,"span",1)),2&t){const t=a["\u0275\u0275nextContext"]().$implicit;a["\u0275\u0275property"]("ngIf",(null==t||null==t.assigned_to?null:t.assigned_to.length)>0),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",0==(null==t||null==t.assigned_to?null:t.assigned_to.length))}}function tt(t,e){if(1&t){const t=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"div",39,40),a["\u0275\u0275listener"]("click",(function(){a["\u0275\u0275restoreView"](t);const e=a["\u0275\u0275reference"](1),i=a["\u0275\u0275nextContext"]().$implicit;return a["\u0275\u0275nextContext"](2).activateInlineEditTs(e,i)})),a["\u0275\u0275text"](2),a["\u0275\u0275pipe"](3,"totalActualHours"),a["\u0275\u0275elementEnd"]()}if(2&t){const t=a["\u0275\u0275nextContext"]().$implicit;a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate1"](" ",a["\u0275\u0275pipeBind1"](3,1,null==t?null:t.actual_hours)," Hrs ")}}function et(t,e){}function it(t,e){if(1&t){const t=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"div",33),a["\u0275\u0275listener"]("click",(function(){a["\u0275\u0275restoreView"](t);const e=a["\u0275\u0275nextContext"]().$implicit;return a["\u0275\u0275nextContext"](2).activateInlineEdit("Planned hours","simple-text",[],e)})),a["\u0275\u0275template"](1,et,0,0,"ng-template",34),a["\u0275\u0275elementEnd"]()}if(2&t){a["\u0275\u0275nextContext"]();const t=a["\u0275\u0275reference"](25);a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngTemplateOutlet",t)}}function st(t,e){if(1&t&&a["\u0275\u0275text"](0),2&t){const t=a["\u0275\u0275nextContext"]().$implicit;a["\u0275\u0275textInterpolate1"](" ",null!=t&&t.planned_hours?null==t?null:t.planned_hours:"0"," Hrs ")}}const at=function(t){return{color:t}};function nt(t,e){if(1&t){const t=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementContainerStart"](0),a["\u0275\u0275elementStart"](1,"div",16),a["\u0275\u0275pipe"](2,"getCtaStatusColor"),a["\u0275\u0275elementStart"](3,"div",17),a["\u0275\u0275elementStart"](4,"div",18),a["\u0275\u0275elementStart"](5,"div",19),a["\u0275\u0275template"](6,N,2,1,"div",20),a["\u0275\u0275template"](7,B,1,1,"ng-template",null,21,a["\u0275\u0275templateRefExtractor"]),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](9,"div",22),a["\u0275\u0275template"](10,V,2,1,"div",20),a["\u0275\u0275template"](11,U,4,6,"ng-template",null,23,a["\u0275\u0275templateRefExtractor"]),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](13,"div",24),a["\u0275\u0275template"](14,z,2,1,"div",20),a["\u0275\u0275template"](15,W,2,4,"ng-template",null,25,a["\u0275\u0275templateRefExtractor"]),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](17,"div",24),a["\u0275\u0275template"](18,G,2,1,"div",20),a["\u0275\u0275template"](19,Z,2,2,"ng-template",null,26,a["\u0275\u0275templateRefExtractor"]),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](21,tt,4,3,"div",27),a["\u0275\u0275elementStart"](22,"div",28),a["\u0275\u0275template"](23,it,2,1,"div",20),a["\u0275\u0275template"](24,st,1,1,"ng-template",null,29,a["\u0275\u0275templateRefExtractor"]),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](26,"div",30),a["\u0275\u0275elementStart"](27,"button",31),a["\u0275\u0275listener"]("click",(function(){a["\u0275\u0275restoreView"](t);const i=e.index;return a["\u0275\u0275nextContext"](2).openTaskDetail(i)})),a["\u0275\u0275elementStart"](28,"mat-icon",32),a["\u0275\u0275text"](29,"open_in_full"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementContainerEnd"]()}if(2&t){const t=e.$implicit,i=a["\u0275\u0275reference"](8),s=a["\u0275\u0275reference"](12),n=a["\u0275\u0275reference"](16),r=a["\u0275\u0275reference"](20),o=a["\u0275\u0275reference"](25),l=a["\u0275\u0275nextContext"](2);a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngStyle",a["\u0275\u0275pureFunction1"](15,at,a["\u0275\u0275pipeBind1"](2,13,null==t?null:t.status))),a["\u0275\u0275advance"](4),a["\u0275\u0275property"]("matTooltip",null==t?null:t.task_name),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",l.isTicketEditable)("ngIfElse",i),a["\u0275\u0275advance"](4),a["\u0275\u0275property"]("ngIf",l.isTicketEditable)("ngIfElse",s),a["\u0275\u0275advance"](4),a["\u0275\u0275property"]("ngIf",l.isTicketEditable)("ngIfElse",n),a["\u0275\u0275advance"](4),a["\u0275\u0275property"]("ngIf",l.isTicketEditable)("ngIfElse",r),a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("ngIf",!l.isCurrentUserCustomer),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("ngIf",l.isTicketEditable)("ngIfElse",o)}}function rt(t,e){if(1&t&&(a["\u0275\u0275elementStart"](0,"div"),a["\u0275\u0275elementStart"](1,"div",6),a["\u0275\u0275elementStart"](2,"div",7),a["\u0275\u0275text"](3," Total : "),a["\u0275\u0275elementStart"](4,"span",8),a["\u0275\u0275text"](5),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](6,"div",9),a["\u0275\u0275elementStart"](7,"div",10),a["\u0275\u0275text"](8," Task "),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](9,"div",11),a["\u0275\u0275text"](10," Status "),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](11,"div",11),a["\u0275\u0275text"](12," Due date "),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](13,"div",11),a["\u0275\u0275text"](14," Assigned to "),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](15,F,2,0,"div",12),a["\u0275\u0275elementStart"](16,"div",13),a["\u0275\u0275text"](17," Planned hours "),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](18,nt,30,17,"ng-container",14),a["\u0275\u0275elementEnd"]()),2&t){const t=a["\u0275\u0275nextContext"]();a["\u0275\u0275advance"](5),a["\u0275\u0275textInterpolate"](t.taskList.length),a["\u0275\u0275advance"](10),a["\u0275\u0275property"]("ngIf",!t.isCurrentUserCustomer),a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("ngForOf",t.taskList)}}function ot(t,e){if(1&t){const t=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"button",47),a["\u0275\u0275listener"]("click",(function(){return a["\u0275\u0275restoreView"](t),a["\u0275\u0275nextContext"](2).openCreateTasks()})),a["\u0275\u0275text"](1," Create Task "),a["\u0275\u0275elementEnd"]()}}function lt(t,e){if(1&t&&(a["\u0275\u0275elementStart"](0,"div",41),a["\u0275\u0275elementStart"](1,"div",42),a["\u0275\u0275elementStart"](2,"span",43),a["\u0275\u0275text"](3,"No Tasks found ! "),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](4,"div",44),a["\u0275\u0275element"](5,"img",45),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](6,ot,2,0,"button",46),a["\u0275\u0275elementEnd"]()),2&t){const t=a["\u0275\u0275nextContext"]();a["\u0275\u0275advance"](6),a["\u0275\u0275property"]("ngIf",t.isTicketEditable)}}let ct=(()=>{class t{constructor(t,e,n,o,l,c,d,p){this.viewContainerRef=t,this.tsInlineEntryPopupService=e,this.dialog=n,this._ticket=o,this.inlineEditPopupService=l,this.sharedLazyLoadedComponentsService=c,this._ErrorService=d,this.tsSubmissionPrimaryService=p,this.updateTaskId=new a.EventEmitter,this.sendTaskTemplate=new a.EventEmitter,this._onDestroy=new r.b,this.taskList=[],this.isTaskLoading=!0,this.statusTypes=[{status_name:"Open"},{status_name:"In Progress"},{status_name:"Completed"}],this.dataArray=[],this.inlineEditField="",this.editType="",this.isTicketEditable=!0,this.changeStatusToEstimation=(t,e)=>{if(t&&"Estimation"!=this.ticketItem.status[0].status_name){this.ticketItem.status_history=e;let t=this.ticketItem.status_history[this.ticketItem.status_history.length-1];this.ticketItem.status=[{_id:t.status_id,status_name:"Estimation",is_active:!0}],this.ticketItem.status_history_names.push({_id:t.status_id,status_name:"Estimation"})}},this.openTaskDetail=t=>Object(s.c)(this,void 0,void 0,(function*(){let e={ticketId:this.ticketId,taskId:this.taskId,taskItem:this.taskList[t]};const{TicketTaskDetailViewComponent:s}=yield i.e(130).then(i.bind(null,"CtN/"));this.dialog.open(s,{height:"99%",width:"86%",maxWidth:"86%",data:{modalParams:e}}).afterClosed().subscribe(t=>{},t=>{this._ErrorService.userErrorAlert(t.error.errorCode,"Some Error Happened in completing the Activity",t.error.errMessage)})}))}activateInlineEditTs(t,e){if(this.ticketItem.cost_centre){(!this.tsInlineEntryPopupService.inlineEditCallbackSubscription||this.tsInlineEntryPopupService.inlineEditCallbackSubscription&&this.tsInlineEntryPopupService.inlineEditCallbackSubscription.closed)&&(this.tsInlineEntryPopupService.inlineEditCallbackSubscription=this.tsInlineEntryPopupService.inlineEditCallback.subscribe(t=>{if(t&&(0!==Object.keys(t).length||t.constructor!==Object))if("S"==t.method)"W"==t.type?(e.timesheet_ids.push(t.data.submission_id),e.entries.push(t.entryItem)):e.actual_hours.push(t.data),"nonBillable"!=t.entryItem.billingType&&(e.actual_hours_total+=t.entryItem.hours);else{if("W"==t.type){let i=[];for(let s of e.timesheet_ids)s!=t.submissionId&&i.push(s);e.timesheet_ids=i,i=[];for(let s of e.entries)(s.submissionId&&s.submissionId!=t.submissionId||s.submission_id&&s.submission_id!=t.submissionId)&&i.push(s);e.entries=i}else{let i=[];for(let s of e.actual_hours)s._id!=t.mongoId&&i.push(s);e.actual_hours=i,i=[];for(let s of e.entries)s._id!=t.mongoId&&i.push(s);e.entries=i}for(let i of t.billingOnly){let t=o.where(e.entries,{submission_id:i.submissionId});0==o.where(e.entries,{submissionId:i.submissionId}).length&&0==t.length&&e.entries.push(i)}e.actual_hours_total=0;for(let t of e.entries)"nonBillable"!=t.billingType&&(e.actual_hours_total+=t.hours);for(let t of e.actual_hours)"billingOnly"==t.billing_type?e.actual_hours_total+=t.actual_hours?t.actual_hours:0:t.day_type||(e.actual_hours_total+=t.actual_hours?t.actual_hours:0)}}));let i=[],s=0;for(let t of e.actual_hours)"billingOnly"==t.billing_type?(i.push({_id:t._id,submissionId:0,date:t.actual_date,hours:t.actual_hours,location:t.location?t.location:"",dayType:t.day_type?t.day_type:"R",billingType:t.billing_type}),s+=t.actual_hours):t.day_type||(i.push({_id:t._id,submissionId:0,date:t.actual_date,hours:t.actual_hours,location:t.location?t.location:"",dayType:"R",billingType:"billable"}),s+=t.actual_hours);let a=e.entries,n=[];for(let t of a)n.push({_id:t._id,submissionId:t.submission_id?t.submission_id:t.submissionId,date:t.date,hours:t.hours,dayType:t.day_type?t.day_type:t.dayType,billingType:t.billing_type?t.billing_type:t.billingType}),s+=t.hours;i=i.concat(n);let r=!0;l().date()==this.tsInlineEntryPopupService.tsProperties.monthly_resubm_cut_off_date?r=!(l().hour()>this.tsInlineEntryPopupService.tsProperties.monthly_resubm_cut_off_hour||l().hour()==this.tsInlineEntryPopupService.tsProperties.monthly_resubm_cut_off_hour&&l().minute()>this.tsInlineEntryPopupService.tsProperties.monthly_resubm_cut_off_minute):l().date()>this.tsInlineEntryPopupService.tsProperties.monthly_resubm_cut_off_date&&!this.tsInlineEntryPopupService.tsProperties.is_payroll_frozen&&(r=!1);let c,d=o.where(this.tsInlineEntryPopupService.wfProperties,{sub_application_id:"R"})[0],p=o.where(e.assigned_to,{is_active:!0});p=p.length>0?p[0].employee_oid:"",c=l(e.start_date).format("DD-MMM-YY"),this.tsInlineEntryPopupService.setInlineEditActiveDataSubject({inlineEditTs:t,viewContainerRef:this.viewContainerRef,costCentreItem:{costCentre:this.ticketItem.cost_centre,costCentreDescription:this.ticketItem.project_item_name,costCentreType:"I",location:e.location?e.location:"",approvers:[]},currentUserOid:p,wfProperties:this.tsInlineEntryPopupService.wfProperties,activeWfProperties:d,week:Math.ceil((l(e.start_date).date()-1-l(e.start_date).day())/7)+1,inlineApplicationDetails:{applicationId:95,objectDetails:[{objectName:this.ticketItem.project_item_name},{objectName:e.task_name}],start_date:e.start_date,end_date:e.end_date,tsTaskId:e._id,taskItem:{_id:e._id,task_name:e.task_name,approved_planned_hours:e.approved_planned_hours},ticketId:this.ticketId,taskId:this.ticketItem.task_id},hasMultipleTasks:!1,totalHours:s,entries:i,objectDetailsVisible:!0,isFromTs:!1,isSubmissionDisabled:!1,canEnterTs:r,canOpenPopup:!0,canDateBeChanged:!0,formattedMonthYearDate:l(e.start_date).hour(15).format("YYYY-MM-DD"),defaultFormattedMonthYearDate:l(e.start_date).hour(15).format("YYYY-MM-DD"),monthYearDate:l(e.start_date),defaultMonthYearDate:l(e.start_date),defaultDate:c,defaultBackupDate:c,defaultHours:8,defaultBackupHours:8,defaultDayType:"R",defaultBackupDayType:"R",defaultBillingType:r?"billable":"billingOnly",defaultBackupBillingType:r?"billable":"billingOnly"})}}ngOnInit(){this.taskRefreshSubscription=this._ticket.getTaskRefreshObservable.pipe(Object(n.a)(this._onDestroy)).subscribe(t=>{console.log(t),t&&this.getTicketTasks()}),this.tsInlineEntryPopupService.getWorkflowProperties({applicationId:37}).then(t=>{this.tsInlineEntryPopupService.wfProperties=t,this.tsInlineEntryPopupService.getTimesheetProperties().then(t=>{this.tsInlineEntryPopupService.tsProperties=t,this.isTicketEditable=this._ticket.checkIfTicketIsEditable(this.ticketProperties,this.ticketItem),this.getTicketTasks(),this.getTaskTemplate(),this.resolveSubscriptions()})})}ngOnChanges(){this.isTicketEditable=this._ticket.checkIfTicketIsEditable(this.ticketProperties,this.ticketItem)}resolveSubscriptions(){this.createResponseSubscription||(this.createResponseSubscription=this._ticket.getActivityObservable.subscribe(t=>{t&&("Create Task"==t.type&&this.openCreateTasks(),"Create task from template"==t.type&&this.createTaskFromTemplate(t.data))},t=>{this._ErrorService.userErrorAlert(t.error.errorCode,"Some Error Happened in completing the Activity",t.error.errMessage)}))}getTicketTasks(){this.taskId?this._ticket.getTicketTasks(this.taskId).pipe(Object(n.a)(this._onDestroy)).subscribe(t=>Object(s.c)(this,void 0,void 0,(function*(){if("N"==t.error&&t.data)if(this.taskList=t.data.tasks,this.isCurrentUserCustomer)this.isTaskLoading=!1;else for(let t=0;t<this.taskList.length;t++){this.taskList[t].timesheet_ids=this.taskList[t].timesheet_ids?this.taskList[t].timesheet_ids:[];let e=o.where(this.taskList[t].assigned_to,{is_active:!0});e.length>0&&(e=e[0].employee_oid,yield this.tsSubmissionPrimaryService.getTicketSubmissionsOfMonth(e,this.taskList[t].timesheet_ids).pipe(Object(n.a)(this._onDestroy)).subscribe(e=>Object(s.c)(this,void 0,void 0,(function*(){this.taskList[t].entries="S"==e.messType?e.data:[],this.taskList[t].actual_hours_total=0;for(let e of this.taskList[t].entries)"nonBillable"!=e.billing_type&&(this.taskList[t].actual_hours_total+=e.hours?e.hours:0);for(let e of this.taskList[t].actual_hours)"billingOnly"==e.billing_type?this.taskList[t].actual_hours_total+=e.actual_hours?e.actual_hours:0:e.day_type||(this.taskList[t].actual_hours_total+=e.actual_hours?e.actual_hours:0);t==this.taskList.length-1&&(this.isTaskLoading=!1)}))))}0==this.taskList.length&&(this.isTaskLoading=!1)})),t=>{this.isTaskLoading=!1,this._ErrorService.userErrorAlert(t.error.errorCode,"Some Error Happened in completing the Activity",t.error.errMessage)}):this.isTaskLoading=!1}createTaskFromTemplate(t){let e=null;for(let i of this.ticketItem.consultants)for(let t of i.consultant_oids)t.is_active&&(e=t.oid);this._ticket.createTaskFromTemplate(t,this.ticketId,this.ticketItem.estimated_closure_date,l(),e).pipe(Object(n.a)(this._onDestroy)).subscribe(t=>{"N"==t.error&&(t.data&&!this.taskId&&(this.taskId=t.data,this.updateTaskId.emit(this.taskId)),this.getTicketTasks())},t=>{this._ErrorService.userErrorAlert(t.error.errorCode,"Some Error Happened in completing the Activity",t.error.errMessage)})}getTaskTemplate(){this.taskId||this._ticket.getTaskTemplate().pipe(Object(n.a)(this._onDestroy)).subscribe(t=>{"N"==t.error&&t.data&&this.sendTaskTemplate.emit(t.data)},t=>{this._ErrorService.userErrorAlert(t.error.errorCode,"Some Error Happened in completing the Activity",t.error.errMessage)})}openCreateTasks(){return Object(s.c)(this,void 0,void 0,(function*(){let t={applicationName:"Ticket",taskId:this.taskId,ticketId:this.ticketId};const{CreateTicketTaskComponent:e}=yield Promise.all([i.e(128),i.e(267)]).then(i.bind(null,"lNso"));this.dialog.open(e,{height:"100%",width:"65%",position:{right:"0px"},data:{modalParams:t}}).afterClosed().subscribe(t=>{t&&"Submit"==t.event&&(t.data&&(t.data.mainResp.task_id&&!this.taskId&&(this.taskId=t.data.mainResp.task_id,this.updateTaskId.emit(this.taskId)),0!=t.data.taskDetails.planned_hours&&"mainData"in t.data.mainResp.ticketData&&this.changeStatusToEstimation(t.data.taskDetails.planned_hours,t.data.mainResp.ticketData.mainData.status_history)),this.getTicketTasks())},t=>{this._ErrorService.userErrorAlert(t.error.errorCode,"Some Error Happened in completing the Activity",t.error.errMessage)})}))}activateInlineEdit(t,e,i,s){this.dataArray=i,this.inlineEditActiveRow=s,(!this.inlineEditPopupService.inlineEditCallbackSubscription||this.inlineEditPopupService.inlineEditCallbackSubscription&&this.inlineEditPopupService.inlineEditCallbackSubscription.closed)&&(this.inlineEditPopupService.inlineEditCallbackSubscription=this.inlineEditPopupService.inlineEditCallback.subscribe(t=>{!t||0===Object.keys(t).length&&t.constructor===Object||this.inlineEditResponseFunction(t)},t=>{this._ErrorService.userErrorAlert(t.error.errorCode,"Some Error Happened in completing the Activity",t.error.errMessage)})),this.inlineEditField=t,this.editType=e,"minimal-dropdown"==e?"Status"==this.inlineEditField&&(this.dropdownConfig={apiDataUpdateKeyName:"status_name",apiDataSelectedKeyName:"status_name",statusColorMapping:this._ticket.task_status_colors,maxWidth:"200px"}):"search-dropdown"==e?this.dropdownConfig={inlineEditField:t,dropdownSelectedValue:null,apiServiceVariable:this.sharedLazyLoadedComponentsService,apiFunctionName:"searchConsultants",apiDataUpdateKeyName:"oid",apiDataSelectedKeyName:"name",hasImageView:!0,apiDataImageKeyName:"oid",maxWidth:"250px"}:"date-picker"==this.editType?this.dropdownConfig={inlineEditField:this.inlineEditField,dropdownSelectedValue:this.inlineEditActiveRow.end_date,apiDataSelectedKeyName:this.inlineEditField,maxWidth:"250px"}:"simple-text"==this.editType&&(this.dropdownConfig={inlineEditField:this.inlineEditField,dropdownSelectedValue:"Task name"==this.inlineEditField?this.inlineEditActiveRow.task_name:this.inlineEditActiveRow.planned_hours,apiDataSelectedKeyName:this.inlineEditField,inputType:"Task name"==this.inlineEditField?"text":"number",minValue:0,maxWidth:"250px"}),this.dataArray=i,this.inlineEditPopupService.setInlineEditActiveDataSubject({editType:this.editType,dataArray:this.dataArray,dropdownConfig:this.dropdownConfig})}inlineEditResponseFunction(t){if("Status"==this.inlineEditField)this.inlineEditActiveRow.task_status!=t[this.dropdownConfig.apiDataUpdateKeyName]&&this._ticket.editTaskStatus(this.taskId,this.inlineEditActiveRow,t[this.dropdownConfig.apiDataUpdateKeyName],this.ticketId).pipe(Object(n.a)(this._onDestroy)).subscribe(e=>Object(s.c)(this,void 0,void 0,(function*(){"N"==e.error?(this.inlineEditActiveRow.task_status=t[this.dropdownConfig.apiDataUpdateKeyName],this._ticket.showMessage("Task Status updated Successfully !")):this._ticket.showMessage(e.msg)})),t=>{this._ErrorService.userErrorAlert(t.error.errorCode,"Some Error Happened in completing the Activity",t.error.errMessage)});else if("Assigned to"==this.inlineEditField)this.inlineEditActiveRow.assigned_to!=t[this.dropdownConfig.apiDataUpdateKeyName]&&(this.inlineEditActiveRow.actual_hours_total>0?this._ticket.showMessage("Kindly remove actual hours before changing assigned person"):this._ticket.editTaskAssigned(this.taskId,this.inlineEditActiveRow,{oid:t[this.dropdownConfig.apiDataUpdateKeyName],name:t[this.dropdownConfig.apiDataSelectedKeyName]},this.ticketId).pipe(Object(n.a)(this._onDestroy)).subscribe(t=>Object(s.c)(this,void 0,void 0,(function*(){if("N"==t.error&&t.data){let e=o.where(t.data.tasks,{_id:this.inlineEditActiveRow._id});e.length>0&&(this.inlineEditActiveRow.assigned_to=[],this.inlineEditActiveRow.assigned_to=e[0].assigned_to),this._ticket.showMessage("Task updated Successfully !")}else this._ticket.showMessage(t.msg)})),t=>{this._ErrorService.userErrorAlert(t.error.errorCode,"Some Error Happened in completing the Activity",t.error.errMessage)}));else if("Due on"==this.inlineEditField){if(l(this.inlineEditActiveRow.end_date).format("YYYY-MM-DD")!=l(new Date(t[this.inlineEditField])).format("YYYY-MM-DD")){let e=l(new Date(t[this.inlineEditField])).format("YYYY-MM-DD");this._ticket.editTaskDueDate(this.taskId,this.inlineEditActiveRow,e,this.ticketId).pipe(Object(n.a)(this._onDestroy)).subscribe(t=>Object(s.c)(this,void 0,void 0,(function*(){"N"==t.error?(this.inlineEditActiveRow.end_date=e,this._ticket.showMessage("Task End date updated Successfully !")):this._ticket.showMessage(t.msg)})),t=>{this._ErrorService.userErrorAlert(t.error.errorCode,"Some Error Happened in completing the Activity",t.error.errMessage)})}}else if("Planned hours"==this.inlineEditField){if(this.inlineEditActiveRow.planned_hours!=t[this.inlineEditField])if(this.ticketItem.is_workflow_complete){let e=0;for(let t of this.inlineEditActiveRow.actual_hours)t.is_billable&&(e+=t.actual_hours);t[this.inlineEditField]<e?this._ticket.showMessage("Planned hours cannot be less than Billable hours !"):this._ticket.editTaskPlannedHours(this.taskId,this.inlineEditActiveRow,t[this.inlineEditField],this.ticketId).pipe(Object(n.a)(this._onDestroy)).subscribe(e=>Object(s.c)(this,void 0,void 0,(function*(){"N"==e.error?(this.inlineEditActiveRow.planned_hours=Number(t[this.inlineEditField]),e.data&&e.data.mainData&&e.data.mainData.status_history&&this.changeStatusToEstimation(this.inlineEditActiveRow.planned_hours,e.data.mainData.status_history),this._ticket.showMessage("Task planned hours updated Successfully !")):this._ticket.showMessage(e.msg)})),t=>{this._ErrorService.userErrorAlert(t.error.errorCode,"Some Error Happened in completing the Activity",t.error.errMessage)})}else this._ticket.showMessage("Planned hours cannot be edited as workflow is in progress !")}else"Task name"==this.inlineEditField&&this.inlineEditActiveRow.task_name!=t[this.inlineEditField]&&(this.inlineEditActiveRow.approved_planned_hours>0?this._ticket.showMessage("Task name cannot be edited since hours are already Approved !"):this._ticket.editTaskName(this.taskId,this.inlineEditActiveRow,t[this.inlineEditField],this.ticketId).pipe(Object(n.a)(this._onDestroy)).subscribe(e=>Object(s.c)(this,void 0,void 0,(function*(){"N"==e.error?(this.inlineEditActiveRow.task_name=t[this.inlineEditField],this._ticket.showMessage("Task name updated Successfully !")):this._ticket.showMessage(e.msg)})),t=>{this._ErrorService.userErrorAlert(t.error.errorCode,"Some Error Happened in completing the Activity",t.error.errMessage)}))}activateActualHoursInlineEdit(t){this._ticket.setActualHoursObservable({taskItem:t,taskId:this.taskId,ticketId:this.ticketId,ticketItem:this.ticketItem,taskList:this.taskList})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete(),this.createResponseSubscription&&this.createResponseSubscription.unsubscribe(),this.taskRefreshSubscription&&this.taskRefreshSubscription.unsubscribe()}}return t.\u0275fac=function(e){return new(e||t)(a["\u0275\u0275directiveInject"](a.ViewContainerRef),a["\u0275\u0275directiveInject"](C.a),a["\u0275\u0275directiveInject"](h.b),a["\u0275\u0275directiveInject"](w.a),a["\u0275\u0275directiveInject"](x.a),a["\u0275\u0275directiveInject"](A.a),a["\u0275\u0275directiveInject"](O.a),a["\u0275\u0275directiveInject"](M.a))},t.\u0275cmp=a["\u0275\u0275defineComponent"]({type:t,selectors:[["app-ticket-tasks"]],inputs:{ticketId:"ticketId",taskId:"taskId",ticketItem:"ticketItem",ticketProperties:"ticketProperties",isCurrentUserCustomer:"isCurrentUserCustomer"},outputs:{updateTaskId:"updateTaskId",sendTaskTemplate:"sendTaskTemplate"},features:[a["\u0275\u0275NgOnChangesFeature"]],decls:4,vars:3,consts:[[1,"container-fluid","ticket-task-styles","pl-0"],[4,"ngIf"],["style","text-align: center;padding-top: 3rem;",4,"ngIf"],[1,"flex-grow-1"],[1,"row","pt-4","justify-content-center"],["diameter","30"],[1,"row","pb-0"],[1,"pt-2","col-11","title"],[1,"heading","pl-2"],[1,"row","pt-2","pb-1","header"],[1,"col-3","pl-4","d-flex"],[1,"col-2"],["class","col-1 pr-0",4,"ngIf"],[1,"col-1","pr-0","pl-1"],[4,"ngFor","ngForOf"],[1,"col-1","pr-0"],[1,"card","listcard",2,"border-left","3px solid #928F8D",3,"ngStyle"],[1,"card-body",2,"padding","2px !important","cursor","pointer"],[1,"row"],[1,"col-3","pr-0","pt-2","pl-3","normalFont",2,"font-weight","500","color","#cf0001",3,"matTooltip"],["inlineEdit","",3,"click",4,"ngIf","ngIfElse"],["taskNameContent",""],[1,"col-2","pt-2","pl-0","normalFont"],["taskStatusContent",""],[1,"col-2","pt-2","normalFont"],["taskDueContent",""],["taskAssignedContent",""],["class","col-1 pt-2 normalFont d-flex justify-content-center",3,"click",4,"ngIf"],[1,"col-1","pt-2","normalFont","d-flex","justify-content-center"],["taskPlHrContent",""],[1,"col-1","d-flex","justify-content-center"],["mat-icon-button","","matTooltip","Detail view",1,"icon-tray-button",3,"click"],[1,"smallCardIcon"],["inlineEdit","",3,"click"],[3,"ngTemplateOutlet"],[1,"status-dot","mb-1",3,"ngStyle"],[1,"pl-2"],[3,"type","oid",4,"ngIf"],[3,"type","oid"],[1,"col-1","pt-2","normalFont","d-flex","justify-content-center",3,"click"],["inlineEditTs",""],[2,"text-align","center","padding-top","3rem"],[1,"d-flex","pb-2","justify-content-center","align-items-center","slide-in-top"],[2,"font-size","16px","font-weight","normal"],[1,"d-flex","justify-content-center","align-items-center","slide-from-down","pt-2","pb-2"],["src","https://assets.kebs.app/images/nomilestone.png","height","170","width","200",1,"mt-2","mb-2"],["mat-raised-button","","class","mt-2 btn-active slide-from-down",3,"click",4,"ngIf"],["mat-raised-button","",1,"mt-2","btn-active","slide-from-down",3,"click"]],template:function(t,e){1&t&&(a["\u0275\u0275elementStart"](0,"div",0),a["\u0275\u0275template"](1,j,5,0,"div",1),a["\u0275\u0275template"](2,rt,19,3,"div",1),a["\u0275\u0275template"](3,lt,7,1,"div",2),a["\u0275\u0275elementEnd"]()),2&t&&(a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",e.isTaskLoading),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",e.taskList.length>0&&!e.isTaskLoading),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",0==e.taskList.length&&!e.isTaskLoading))},directives:[c.NgIf,y.c,c.NgForOf,c.NgStyle,u.a,d.a,p.a,D.a,c.NgTemplateOutlet,P.a],pipes:[R.a,c.DatePipe,q.a,$.a],styles:[".ticket-task-styles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%]{background-color:#cf0001;border-color:#cf0001;color:#fff;font-weight:400;font-size:12px!important;min-width:10rem;line-height:28px;border-radius:4px}.ticket-task-styles[_ngcontent-%COMP%]   .normalFont[_ngcontent-%COMP%]{color:#181818;font-size:14px}.ticket-task-styles[_ngcontent-%COMP%]   .normalFont[_ngcontent-%COMP%], .ticket-task-styles[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-weight:400;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ticket-task-styles[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{color:#868383;font-size:14px!important}.ticket-task-styles[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]{color:#1a1a1a;font-size:12px;font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ticket-task-styles[_ngcontent-%COMP%]   .ctaActionButton[_ngcontent-%COMP%]{line-height:0!important;width:auto!important;height:auto!important}.ticket-task-styles[_ngcontent-%COMP%]   .status-dot[_ngcontent-%COMP%]{height:11px;width:11px;border-radius:50%;display:inline-block;vertical-align:middle}.ticket-task-styles[_ngcontent-%COMP%]   .icon-button-inactive[_ngcontent-%COMP%]{background-color:#f1f1f1;line-height:8px;width:26px;height:26px;margin-right:15px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.ticket-task-styles[_ngcontent-%COMP%]   .icon-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.ticket-task-styles[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{color:#5f5f5f;font-size:10px!important}.ticket-task-styles[_ngcontent-%COMP%]   .listcard[_ngcontent-%COMP%]{transition:all .25s linear;height:35px}.ticket-task-styles[_ngcontent-%COMP%]   .listcard[_ngcontent-%COMP%]:hover   .icon-tray-button[_ngcontent-%COMP%]{visibility:visible!important;animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}.ticket-task-styles[_ngcontent-%COMP%]   .icon-tray-button[_ngcontent-%COMP%]{width:27px!important;height:27px!important;line-height:25px!important;visibility:hidden}.ticket-task-styles[_ngcontent-%COMP%]   .smallCardIcon[_ngcontent-%COMP%]{font-size:19px!important;color:#868683!important}.ticket-task-styles[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}.ticket-task-styles[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}"]}),t})(),dt=(()=>{class t{}return t.\u0275mod=a["\u0275\u0275defineNgModule"]({type:t}),t.\u0275inj=a["\u0275\u0275defineInjector"]({factory:function(e){return new(e||t)},providers:[{provide:f.c,useClass:b.c,deps:[f.f,b.a]},{provide:f.e,useValue:{parse:{dateInput:"DD-MM-YYYY"},display:{dateInput:"DD-MM-YY",monthYearLabel:"MMM YYYY"}}}],imports:[[c.CommonModule,p.b,d.b,u.b,h.g,m.p,m.E,k.c,g.d,f.n,_.h,I.a,y.b,v.b,S.a,T.a,E.a]]}),t})()},v2fc:function(t,e,i){"use strict";i.d(e,"a",(function(){return u}));var s=i("xG9w"),a=i("wd/R"),n=i("2Vo4"),r=i("XNiG"),o=i("fXoL"),l=i("tk/3"),c=i("LcQX"),d=i("flaP"),p=i("XXEo");let u=(()=>{class t{constructor(t,e,i,s){this.$http=t,this._util=e,this._roles=i,this._auth=s,this.currentUser=this._auth.getProfile().profile,this.token=this._auth.getJwtToken(),this.isaBudgettedAttachmentSubject=new n.a({}),this.getisaBudgettedAttachmentObservable=this.isaBudgettedAttachmentSubject.asObservable(),this.activitySubject=new r.b,this.getActivityObservable=this.activitySubject.asObservable(),this.priority_status_colors=[{statusName:"Low",statusColor:"#BADC58"},{statusName:"Medium",statusColor:"#91AECB"},{statusName:"High",statusColor:"#FFA502"},{statusName:"Very High",statusColor:"#cf0001"}],this.getVendorDetailsData=()=>this.$http.post("/api/isa/request/getVendorForList",{})}showMessage(t){this._util.showToastMessage(t)}showErrorMessage(t){this._util.showErrorMessage(t,"KEBS")}getToken(){return this._auth.getToken()}getCurrentUserRole(){return this._roles&&this._roles.roles&&this._roles.roles.length>0?this._roles.roles[0].role_id:null}setActivityObservable(t){this.activitySubject.next(t)}getStatusObjectEntries(){let t=s.where(this._roles.roles,{application_id:139,object_id:68});return t.length>0?JSON.parse(t[0].object_entries):null}resolveActivityTemplate(t,e,i,n,r,o){let l=s.where(t,{activity_name:("Status"==e?"status":"Priority"==e?"priority":"RMG Owner"==e?"rmgOwner":"TAG Owner"==e?"tagOwner":null)||e});return l.length>0?{activity_type_id:l[0]._id,activity_description:this.getActivityDescription(l[0],i,n,r,o),activity_by:this.currentUser.oid,activity_created_date:a()}:{}}getActivityDescription(t,e,i,s,a){let n="";return n=t.activity_template[e],i&&(n=n.replace("from_value",i)),s&&(n=n.replace("to_value",s)),a&&(n=n.replace("object_name",a)),n}getRequestById(t){return this.$http.post("/api/isa/request/getRequestById",{requestId:t})}getActivityTypes(){return this.$http.post("/api/isa/request/getActivityTypeMasterData",{})}getStatusMasterData(){return this.$http.post("/api/isa/request/getISAStatusMasterData",{statusValues:this.getStatusObjectEntries()})}updateRequestStatus(t,e,i,s){return this.$http.post("/api/isa/request/statusChange",{requestId:t,statusRefId:e,currentStatusId:i,activityTemplate:s})}updateKeyValueInResourceRequest(t,e,i){return this.$http.post("/api/isa/request/updateKeyValueInResourceRequest",{requestId:t,activityTemplate:e,modifyKeyValue:i})}changeTagOrRmgOwner(t,e,i,s){return this.$http.post("/api/isa/request/changeTagOrRmgOwner",{requestId:t,activityTemplate:e,type:i,changedOid:s})}getActivity(t){return this.$http.post("/api/isa/request/activityRetreivalBasedOnRequestIdOrActivityId",{activityId:t})}insertISAActivity(t,e){return this.$http.post("/api/isa/request/insertISAActivity",{requestId:t,activityTemplate:e})}editISAActivity(t,e){return this.$http.post("/api/isa/request/editISAActivity",{activity_id:t,activity_details:e})}getISAAttachmentById(t){return this.$http.post("/api/isa/attachment/getISAAttachmentById",{request_id:t})}getISAAttachmentFromS3(t){return this.$http.post("/api/isa/attachment/getISAAttachmentFromS3",{key:t})}deleteISAAttachment(t,e,i){return this.$http.post("/api/isa/attachment/deleteISAAttachment",{requestId:t,file:e,activityTemplate:i})}updateISAAttachment(t,e,i,s){return this.$http.post("/api/isa/attachment/updateISAAttachment",{requestId:t,file:i,attachmentId:e,activityTemplate:s})}getApproverStatuses(t,e){return this.$http.post("/api/isa/request/getApproverStatusForRequest",{workflowHeaderId:t,approvers:e})}createTask(t,e,i){return this.$http.post("/api/isa/tasks/createTasks",{request_id:t,tasks:e,activityTemplate:i})}getRequestTasks(t){return this.$http.post("/api/isa/tasks/getTaskById",{request_id:t})}addTask(t,e,i,s){return this.$http.post("/api/isa/tasks/addTasks",{task_id:t,request_id:e,tasks:i,activityTemplate:s})}updateTaskObject(t,e,i,s,a){return this.$http.post("/api/isa/tasks/updateTaskObject",{task_id:t,sub_task_id:e,key:i,object:s,activityTemplate:a})}getTaskTemplate(){return this.$http.post("/api/isa/tasks/getTaskTemplates",{})}createTaskFromTemplate(t,e,i,s,a,n,r,o){return this.$http.post("/api/isa/tasks/assignTaskFromTemplate",{task_template_id:t,request_id:e,task_id:i,rmg_owner:s,tag_owner:a,activityTemplate:n,taskTypesList:r,requestSkillId:o})}updateTaskDataFromAttachment(t){return this.$http.post("/api/isa/tasks/updateTaskNameAndOwner",t)}updateExtTaskExtAtchId(t){return this.$http.post("/api/isa/tasks/updateExtTaskExtAtchId",t)}getTaskStatusList(){return this.$http.post("/api/isa/tasks/getTaskStatus",{})}getTaskTypeList(){return this.$http.post("/api/isa/tasks/getTaskTypeMasterData",{})}updateTypeListInReq(t){return this.$http.post("/api/isa/request/insertTaskTypeOwnerDetail",t)}updateTypeAssigned(t){return this.$http.post("/api/isa/request/updTaskTypeOwnerDetail",t)}deleteTask(t,e,i,s,a){return this.$http.post("/api/isa/tasks/changeTaskFlag",{request_id:t,task_id:e,sub_task_id:i,is_active:s,activityTemplate:a})}updateTaskAssigned(t,e,i,s,a){return this.$http.post("/api/isa/tasks/updateAssignedToTask",{request_id:t,task_id:e,sub_task_id:i,assigned_to:s,activityTemplate:a})}updateTaskData(t,e,i,s,a,n){return this.$http.post("/api/isa/tasks/updateTaskData",{request_id:t,task_id:e,sub_task_id:i,key:s,value:a,activityTemplate:n})}getRequestCTAs(t){return this.$http.post("/api/isa/configuration/getRequestCTAs",{requestId:t})}getISAWfConfig(){return this.$http.post("/api/isa/configuration/getISAWfConfig",{})}getTodoDetails(t){return this.$http.post("/api/isa/todo/getTodo",{todoId:t})}createTodo(t,e,i){return this.$http.post("/api/isa/todo/createToDo",{request_id:t,to_do_list:e,activityTemplate:i})}insertTodo(t,e,i,s){return this.$http.post("/api/isa/todo/insertToDo",{request_id:t,to_do_id:e,to_do_list:i,activityTemplate:s})}editTodo(t,e,i,s){return this.$http.post("/api/isa/todo/editToDo",{request_id:t,to_do_id:e,to_do_details:i,activityTemplate:s})}updateResourceInRequest(t,e,i,s,a){return this.$http.post("/api/isa/request/updateResourceInRequest",{requestId:t,activityTemplate:e,wfActivityTemplate:i,statusRefId:s,resourceOid:a})}triggerWfOnSubmission(t,e,i,s,a,n,r,o){return this.$http.post("/api/isa/request/triggerWfOnSubmission",{requestId:t,activityTemplate:e,wfActivityTemplate:i,statusRefId:s,task_id:a,sub_task_id:n,task_status:r,wfConfig:o})}addIsaTaskActualHours(t,e,i,s,a){return this.$http.post("/api/isa/tasks/addIsaTaskActualHours",{task_id:t,task_item:e,actual_hours:i,timesheet_id:s,request_id:a})}deleteIsaTaskActualHours(t,e,i,s,a){return this.$http.post("/api/isa/tasks/deleteIsaTaskActualHours",{task_id:t,task_item:e,actual_hours:i,request_id:s,timesheet_id:a})}getSLAByRequestId(t){return this.$http.post("/api/isa/request/getSLAByRequestId",{requestId:t})}getRequestBasedOnSearch(t){return this.$http.post("/api/isa/request/getRequestBasedOnSearch",{searchParameter:t})}moveTaskToRequest(t,e,i,s,a,n){return this.$http.post("/api/isa/tasks/moveTaskToRequest",{fromRequestId:t,fromTaskId:e,mainTaskId:i,toRequestId:s,toTaskId:a,activityTemplate:n})}removeResourceFromRequest(t,e,i,s,a){return this.$http.post("/api/isa/request/removeResourceFromRequest",{requestId:t,statusId:e,statusRefId:i,resActivityTemplate:s,statusActivityTemplate:a})}checkIfUserHasAccessToRequest(t){return this.$http.post("/api/isa/request/checkIfUserHasAccessToRequest",{requestId:t,oid:this.currentUser.oid})}updateEmployeeStatus(t){return this.$http.post("/api/obPrimary/updateEmployeeStatus",t)}checkIfEmailHasRequestId(t){return this.$http.post("/api/obPrimary/checkIfEmailHasRequestId",{apiParams:{emailId:t}})}createISAAttachment(t){return this.$http.post("/api/isa/attachment/createIsaAttachment",t)}getSourceList(){return this.$http.post("/api/isa/attachment/getAllSource",{})}saveEmailtoTask(t){return this.$http.post("/api/isa/tasks/saveEmailtoTask",t)}setISABudgetedAttachmentActivityObservable(t){this.isaBudgettedAttachmentSubject.next(t)}getTemplateForUser(t){return this.$http.post("/api/isa/request/getVisibleFormFieldsForUser",t)}budgetApprovalCheck(t){return this.$http.post("/api/isa/request/ctcRangeCheck",t)}checkElligibleToSubmit(t){return new Promise((e,i)=>{this.$http.post("/api/isa/request/budgetApprovalCheck",t).subscribe(t=>e(t),t=>i(t))})}offerApprovalCheck(t){return this.$http.post("/api/isa/request/offerApprovalCheck",t)}triggerWfOnBudgetApproval(t,e,i,s,a){return this.$http.post("/api/isa/request/triggerWfOnBudgetApproval",{requestId:t,activityTemplate:e,wfActivityTemplate:i,statusRefId:s,wfConfig:a})}getMasterDataByMasterDataName(t){return this.$http.post("/api/isa/request/getMasterDataByMasterDataName",t)}getVisibilityMatrix(t){return this.$http.post("/api/isa/request/getAuthorzedFieldsByRoleId",t)}getAllRoleAccess(){return s.where(this._roles.roles,{application_id:139})}}return t.\u0275fac=function(e){return new(e||t)(o["\u0275\u0275inject"](l.c),o["\u0275\u0275inject"](c.a),o["\u0275\u0275inject"](d.a),o["\u0275\u0275inject"](p.a))},t.\u0275prov=o["\u0275\u0275defineInjectable"]({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()}}]);