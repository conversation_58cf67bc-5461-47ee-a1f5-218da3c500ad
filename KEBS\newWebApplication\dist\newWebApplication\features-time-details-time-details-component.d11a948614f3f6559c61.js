(window.webpackJsonp=window.webpackJsonp||[]).push([[670,535,631,634,858],{"+JMY":function(e,t,i){"use strict";i.r(t),i.d(t,"TimeDetailsComponent",(function(){return F})),i.d(t,"TimeDetailsModule",(function(){return P}));var n=i("mrSG"),l=i("fXoL"),a=i("xG9w"),o=i("wd/R"),s=i("0IaG"),r=i("ofXK"),m=i("Xi0T"),d=i("kmnG"),c=i("qFsG"),p=i("3Pt+"),u=i("iadO"),h=i("FKr1"),f=i("NFeN"),v=i("bTqV"),g=i("Xa2L"),y=i("Qu3c"),S=i("1jcm"),D=i("33Jv"),b=i("1yaQ"),E=i("bSwM"),T=i("QyBZ"),C=i("jAlA"),_=i("1A3m"),w=i("TmG/"),x=i("fbGU");function k(e,t){1&e&&(l["\u0275\u0275elementContainerStart"](0),l["\u0275\u0275elementStart"](1,"div",2),l["\u0275\u0275element"](2,"mat-spinner",3),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementContainerEnd"]())}function O(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"span",9),l["\u0275\u0275text"](1," \xa0*"),l["\u0275\u0275elementEnd"]())}function M(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div",7),l["\u0275\u0275elementStart"](1,"div",8),l["\u0275\u0275text"](2," Project Available hours "),l["\u0275\u0275template"](3,O,2,0,"span",31),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](4,"div",6),l["\u0275\u0275elementStart"](5,"mat-form-field",10),l["\u0275\u0275element"](6,"input",32),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"](2);l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("ngIf","1"==e.timeDetailsFormGroup.get("workclassification").value),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("allowDecimal",!0)("required","1"==e.timeDetailsFormGroup.get("workclassification").value)}}function I(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementContainerStart"](0),l["\u0275\u0275elementStart"](1,"div",4),l["\u0275\u0275text"](2,"Time Details"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](3,"form",5),l["\u0275\u0275listener"]("keydown.enter",(function(e){return e.preventDefault()})),l["\u0275\u0275elementStart"](4,"div",6),l["\u0275\u0275elementStart"](5,"div",7),l["\u0275\u0275elementStart"](6,"div",8),l["\u0275\u0275text"](7," Effective date "),l["\u0275\u0275elementStart"](8,"span",9),l["\u0275\u0275text"](9," \xa0*"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](10,"div",6),l["\u0275\u0275elementStart"](11,"mat-form-field",10),l["\u0275\u0275element"](12,"input",11),l["\u0275\u0275element"](13,"mat-datepicker-toggle",12),l["\u0275\u0275element"](14,"mat-datepicker",null,13),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](16,"form",5),l["\u0275\u0275listener"]("keydown.enter",(function(e){return e.preventDefault()})),l["\u0275\u0275elementStart"](17,"div",6),l["\u0275\u0275elementStart"](18,"div",7),l["\u0275\u0275elementStart"](19,"div",8),l["\u0275\u0275text"](20," In Time "),l["\u0275\u0275elementStart"](21,"span",9),l["\u0275\u0275text"](22," \xa0*"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](23,"div",6),l["\u0275\u0275elementStart"](24,"app-input-search",14),l["\u0275\u0275listener"]("change",(function(){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"]().handleTimeChange()})),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](25,"div",7),l["\u0275\u0275elementStart"](26,"div",8),l["\u0275\u0275text"](27," Out Time "),l["\u0275\u0275elementStart"](28,"span",9),l["\u0275\u0275text"](29," \xa0*"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](30,"div",6),l["\u0275\u0275elementStart"](31,"app-input-search",15),l["\u0275\u0275listener"]("change",(function(){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"]().handleTimeChange()})),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](32,"div",16),l["\u0275\u0275elementStart"](33,"mat-checkbox",17),l["\u0275\u0275text"](34,"Next Calendar Day"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](35,"div",6),l["\u0275\u0275elementStart"](36,"div",7),l["\u0275\u0275elementStart"](37,"div",8),l["\u0275\u0275text"](38," Daily working hours "),l["\u0275\u0275elementStart"](39,"span",9),l["\u0275\u0275text"](40," \xa0*"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](41,"div",6),l["\u0275\u0275elementStart"](42,"mat-form-field",10),l["\u0275\u0275element"](43,"input",18),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](44,"div",7),l["\u0275\u0275elementStart"](45,"div",8),l["\u0275\u0275text"](46," Monthly working hours "),l["\u0275\u0275elementStart"](47,"span",9),l["\u0275\u0275text"](48," \xa0*"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](49,"div",6),l["\u0275\u0275elementStart"](50,"mat-form-field",10),l["\u0275\u0275element"](51,"input",19),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](52,"div",7),l["\u0275\u0275elementStart"](53,"div",8),l["\u0275\u0275text"](54," Work Classification "),l["\u0275\u0275elementStart"](55,"span",9),l["\u0275\u0275text"](56," \xa0*"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](57,"div",6),l["\u0275\u0275element"](58,"app-input-search",20),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](59,"div",6),l["\u0275\u0275elementStart"](60,"div",7),l["\u0275\u0275elementStart"](61,"div",8),l["\u0275\u0275text"](62," Work schedule "),l["\u0275\u0275elementStart"](63,"span",9),l["\u0275\u0275text"](64," \xa0*"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](65,"div",6),l["\u0275\u0275element"](66,"app-input-search",21),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](67,"div",7),l["\u0275\u0275elementStart"](68,"div",8),l["\u0275\u0275text"](69," Holiday calendar "),l["\u0275\u0275elementStart"](70,"span",9),l["\u0275\u0275text"](71," \xa0*"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](72,"div",6),l["\u0275\u0275element"](73,"app-input-search",22),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](74,"div",7),l["\u0275\u0275elementStart"](75,"div",8),l["\u0275\u0275text"](76," Work Location "),l["\u0275\u0275elementStart"](77,"span",9),l["\u0275\u0275text"](78," \xa0*"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](79,"div",6),l["\u0275\u0275element"](80,"app-input-search",23),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](81,"div",6),l["\u0275\u0275template"](82,M,7,3,"div",24),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](83,"div",6),l["\u0275\u0275elementStart"](84,"div",7),l["\u0275\u0275elementStart"](85,"div",25),l["\u0275\u0275text"](86," Auto Timesheet Submission "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](87,"div"),l["\u0275\u0275elementStart"](88,"mat-slide-toggle",26),l["\u0275\u0275listener"]("change",(function(t){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"]().handleTimesheetToggle(t)})),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](89,"div",7),l["\u0275\u0275elementStart"](90,"div",25),l["\u0275\u0275text"](91," Timesheet Submission "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](92,"div"),l["\u0275\u0275elementStart"](93,"mat-slide-toggle",26),l["\u0275\u0275listener"]("change",(function(t){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"]().handleTimesheetsubmissionToggle(t)})),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](94,"div",7),l["\u0275\u0275elementStart"](95,"div",25),l["\u0275\u0275text"](96," Leave Auto Approval "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](97,"div"),l["\u0275\u0275elementStart"](98,"mat-slide-toggle",26),l["\u0275\u0275listener"]("change",(function(t){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"]().handleleaveAutoApprovalToggle(t)})),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](99,"div",7),l["\u0275\u0275elementStart"](100,"div",25),l["\u0275\u0275text"](101," Allow Editing Of Timesheet "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](102,"div"),l["\u0275\u0275elementStart"](103,"mat-slide-toggle",26),l["\u0275\u0275listener"]("change",(function(t){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"]().handleTimesheetEdit(t)})),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](104,"div",27),l["\u0275\u0275elementStart"](105,"div",28),l["\u0275\u0275elementStart"](106,"button",29),l["\u0275\u0275listener"]("click",(function(){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"]().skipSection()})),l["\u0275\u0275text"](107),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](108,"button",30),l["\u0275\u0275listener"]("click",(function(){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"]().saveDetails()})),l["\u0275\u0275text"](109),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=l["\u0275\u0275reference"](15),t=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("formGroup",t.effectiveDateFormGroup),l["\u0275\u0275advance"](9),l["\u0275\u0275property"]("matDatepicker",e)("min",t.employeeDoj),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("for",e),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("formGroup",t.timeDetailsFormGroup),l["\u0275\u0275advance"](8),l["\u0275\u0275property"]("list",t.inTimeMaster)("disableNone",!0),l["\u0275\u0275advance"](7),l["\u0275\u0275property"]("list",t.outTimeMaster)("disableNone",!0),l["\u0275\u0275advance"](12),l["\u0275\u0275property"]("allowDecimal",!0),l["\u0275\u0275advance"](8),l["\u0275\u0275property"]("allowDecimal",!0),l["\u0275\u0275advance"](7),l["\u0275\u0275property"]("list",t.workclassificationTypeMaster)("disableNone",!0),l["\u0275\u0275advance"](8),l["\u0275\u0275property"]("list",t.workScheduleTypeMaster)("disableNone",!0),l["\u0275\u0275advance"](7),l["\u0275\u0275property"]("list",t.holidayCalenderTypeMaster)("disableNone",!0),l["\u0275\u0275advance"](7),l["\u0275\u0275property"]("list",t.workLocationTypeMaster)("disableNone",!0),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf","1"==t.timeDetailsFormGroup.get("workclassification").value),l["\u0275\u0275advance"](6),l["\u0275\u0275property"]("checked",null==t.autoTimeSheetSubmissionControl?null:t.autoTimeSheetSubmissionControl.value),l["\u0275\u0275advance"](5),l["\u0275\u0275property"]("checked",null==t.TimeSheetSubmissionControl?null:t.TimeSheetSubmissionControl.value),l["\u0275\u0275advance"](5),l["\u0275\u0275property"]("checked",null==t.leaveAutoApprovalControl?null:t.leaveAutoApprovalControl.value),l["\u0275\u0275advance"](5),l["\u0275\u0275property"]("checked",null==t.timesheetEditControl?null:t.timesheetEditControl.value),l["\u0275\u0275advance"](4),l["\u0275\u0275textInterpolate"](t.isFromModal?"Cancel":"Skip"),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("disabled",t.loaderObject.isFormSubmitLoading),l["\u0275\u0275advance"](1),l["\u0275\u0275textInterpolate1"](" ",t.isFromModal?"Update":"Save & Next >"," ")}}let F=(()=>{class e{constructor(e,t,i,n){this._edService=e,this._toaster=t,this.fb=i,this.injector=n,this.dialogRef=null,this.isFromModal=!1,this.timeDetailsRes=new l.EventEmitter,this.effectiveDateFormGroup=this.fb.group({effectiveDate:["",p.H.required]}),this.effectiveDatePayload={},this.timeDetailsFormGroup=this.fb.group({inTime:["",p.H.required],outTime:["",p.H.required],isNextCalenderDay:["",p.H.required],monthlyWH:["",p.H.required],dailyWH:["",p.H.required],workSchedule:["",p.H.required],holidayCalender:["",p.H.required],workLocation:["",p.H.required],workclassification:["",p.H.required],autoTimeSheetSubmission:[!1],TimeSheetSubmission:[!0],leaveAutoApproval:[!1],projectHours:[""],timesheetSchedule:[""],timesheetNotification:[""],allowMappedCostCenter:[""],timesheetEdit:[!1]}),this.workScheduleTypeMaster=[],this.timesheetScheduleMaster=[],this.holidayCalenderTypeMaster=[],this.workLocationTypeMaster=[],this.workclassificationTypeMaster=[],this.inTimeMaster=[],this.outTimeMaster=[],this.timeDetailsPayload={},this.subs=new D.a,this.loaderObject={isComponentLoading:!1,isFormSubmitLoading:!1},this.dialogRef=this.injector.get(s.h,null),this.dialogData=this.injector.get(s.a,null)}getWorkSchedule(){return new Promise((e,t)=>{this.subs.sink=this._edService.getWorkSchedule().subscribe(t=>{e(t.data)},e=>{console.log(e),t(e)})})}getWorkLocation(){return new Promise((e,t)=>{this.subs.sink=this._edService.getWorkLocation().subscribe(t=>{e(t.data)},e=>{console.log(e),t(e)})})}getWorkClassification(){return new Promise((e,t)=>{this.subs.sink=this._edService.getWorkClassification().subscribe(t=>{e(t.data)},e=>{console.log(e),t(e)})})}getInTimeDetails(){return new Promise((e,t)=>{this.subs.sink=this._edService.fetchInTime().subscribe(t=>{e(t.data)},e=>{console.log(e),t(e)})})}get autoTimeSheetSubmissionControl(){return this.timeDetailsFormGroup.get("autoTimeSheetSubmission")}get TimeSheetSubmissionControl(){return this.timeDetailsFormGroup.get("TimeSheetSubmission")}get leaveAutoApprovalControl(){return this.timeDetailsFormGroup.get("leaveAutoApproval")}get timesheetEditControl(){return this.timeDetailsFormGroup.get("timesheetEdit")}getOutTimeDetails(){return new Promise((e,t)=>{this.subs.sink=this._edService.fetchOutTime().subscribe(t=>{e(t.data)},e=>{console.log(e),t(e)})})}handleleaveAutoApprovalToggle(e){this.leaveAutoApprovalControl.patchValue(e.checked),e.checked!=this.timeDetailsPayload.leaveAutoApproval.value&&(this.timeDetailsPayload.leaveAutoApproval.isChanged=!0)}handleTimesheetToggle(e){this.autoTimeSheetSubmissionControl.patchValue(e.checked),e.checked!=this.timeDetailsPayload.autoTimeSheetSubmission.value&&(this.timeDetailsPayload.autoTimeSheetSubmission.isChanged=!0)}handleTimesheetsubmissionToggle(e){this.TimeSheetSubmissionControl.patchValue(e.checked),e.checked!=this.timeDetailsPayload.TimeSheetSubmission.value&&(this.timeDetailsPayload.TimeSheetSubmission.isChanged=!0)}handleTimesheetEdit(e){this.timesheetEditControl.patchValue(e.checked),e.checked!=this.timeDetailsPayload.timesheetEdit.value&&(this.timeDetailsPayload.timesheetEdit.isChanged=!0)}getHolidayCalender(){return new Promise((e,t)=>{this.subs.sink=this._edService.getHolidayCalender().subscribe(t=>{e(t.data)},e=>{console.log(e),t(e)})})}getDateOfJoining(e){return new Promise((t,i)=>{this.subs.sink=this._edService.getDateOfJoining(e).subscribe(e=>{e.err?i(e):t(e.doj)},e=>{console.log(e),i(e)})})}ngOnInit(){var e,t,i,l,a,o,s,r;return Object(n.c)(this,void 0,void 0,(function*(){this.loaderObject.isComponentLoading=!0,this.associateId=(null===(t=null===(e=this.dialogData)||void 0===e?void 0:e.modalParams)||void 0===t?void 0:t.associateId)?null===(l=null===(i=this.dialogData)||void 0===i?void 0:i.modalParams)||void 0===l?void 0:l.associateId:this.associateId,this.isFromModal=!!(null===(o=null===(a=this.dialogData)||void 0===a?void 0:a.modalParams)||void 0===o?void 0:o.isFromModal)&&(null===(r=null===(s=this.dialogData)||void 0===s?void 0:s.modalParams)||void 0===r?void 0:r.isFromModal),yield this.bindSavedResponse(),this.employeeDoj=yield this.getDateOfJoining(this.associateId),this.setEffectiveDateValidation(),this.loaderObject.isComponentLoading=!1,this.workScheduleTypeMaster=yield this.getWorkSchedule(),this.timesheetScheduleMaster=yield this.getTimeSheetScheduleMaster(),this.workLocationTypeMaster=yield this.getWorkLocation(),this.workclassificationTypeMaster=yield this.getWorkClassification(),this.inTimeMaster=yield this.getInTimeDetails(),this.outTimeMaster=yield this.getOutTimeDetails(),this.holidayCalenderTypeMaster=yield this.getHolidayCalender(),yield this.handleTenantWiseFieldConfig(),this.createInitValue(),this.handleEffectiveDatePayloadObject(),this.handleTimeDetailsPayloadObject(),this.valueChangeListener()}))}createInitValue(){this.effectiveDateInitValue=JSON.parse(JSON.stringify(this.effectiveDateFormGroup.value)),this.timeDetailsInitValue=JSON.parse(JSON.stringify(this.timeDetailsFormGroup.value))}valueChangeListener(){this.effectiveDateFormGroup.valueChanges.subscribe(e=>{this.handleEffectiveDatePayloadObject()}),this.timeDetailsFormGroup.valueChanges.subscribe(e=>{this.handleTimeDetailsPayloadObject()})}getTimeSheetScheduleMaster(){return new Promise((e,t)=>{this.subs.sink=this._edService.getTimeSheetSchedule().subscribe(t=>{e(t.data)},e=>{console.log(e),t(e)})})}handleTimeDetailsPayloadObject(){let e=this.timeDetailsFormGroup.value,t=Object.keys(e);for(let i of t)this.timeDetailsPayload[i]={value:e[i],isChanged:this.checkIfChanged(e,this.timeDetailsInitValue,i)};console.log(this.timeDetailsPayload)}handleEffectiveDatePayloadObject(){let e=this.effectiveDateFormGroup.value,t=Object.keys(e);for(let i of t)this.effectiveDatePayload[i]={value:e[i],isChanged:this.checkIfChanged(e,this.effectiveDateInitValue,i)};console.log(this.effectiveDatePayload)}checkIfChanged(e,t,i){return"object"==typeof e[i]?!a.isEqual(e[i],t[i]):e[i]!==t[i]}bindSavedResponse(){return new Promise((e,t)=>{this.subs.sink=this._edService.getTimeDetailsCP(this.associateId).subscribe(t=>{if(!t.err){let e=t.data;this.patchValueInForm(this.timeDetailsFormGroup,"inTime",e,"in_time"),this.patchValueInForm(this.timeDetailsFormGroup,"outTime",e,"out_time"),this.timeDetailsFormGroup.get("isNextCalenderDay").patchValue(!!e.is_next_calender_day,{emitEvent:!1}),this.timeDetailsFormGroup.get("timesheetNotification").patchValue(!!e.ts_notif_flag,{emitEvent:!1}),this.timeDetailsFormGroup.get("allowMappedCostCenter").patchValue(!!e.restrict_mapped_cc_in_ts,{emitEvent:!1}),this.patchValueInForm(this.timeDetailsFormGroup,"dailyWH",e,"daily_working_hours"),this.patchValueInForm(this.timeDetailsFormGroup,"monthlyWH",e,"monthly_working_hours"),this.patchValueInForm(this.timeDetailsFormGroup,"workSchedule",e,"work_schedule"),this.patchValueInForm(this.timeDetailsFormGroup,"holidayCalender",e,"holiday_calender"),this.patchValueInForm(this.timeDetailsFormGroup,"workLocation",e,"work_location"),this.patchValueInForm(this.timeDetailsFormGroup,"workclassification",e,"work_classification"),this.patchValueInForm(this.timeDetailsFormGroup,"projectHours",e,"project_available_hours"),this.patchValueInForm(this.timeDetailsFormGroup,"timesheetSchedule",e,"timesheet_schedule"),this.timeDetailsFormGroup.get("autoTimeSheetSubmission").patchValue(!!e.auto_timesheet_submission),this.timeDetailsFormGroup.get("TimeSheetSubmission").patchValue(!!e.timesheet_submission),this.timeDetailsFormGroup.get("leaveAutoApproval").patchValue(!!e.leave_auto_approve),this.timeDetailsFormGroup.get("timesheetEdit").patchValue(!!e.is_timesheet_edit_allowed)}e(!0)},e=>{console.log(e),t(e)})})}patchValueInForm(e,t,i,n){let l=i[n]?i[n]:"";e.get(t).patchValue(l,{emitEvent:!1})}saveDetails(){if(this.effectiveDateFormGroup.valid&&this.timeDetailsFormGroup.valid)if(this.validateIfChanged()){this.loaderObject.isFormSubmitLoading=!0;let e=Object.assign(Object.assign({associate_id:this.associateId},this.timeDetailsPayload),this.effectiveDatePayload);this.handleDateFormatPayLoad(e),this.subs.sink=this._edService.saveTimeDetailsCP(e).subscribe(e=>{this.loaderObject.isFormSubmitLoading=!1,e.err?this._toaster.showError("Error","Failed to save. Kindly contact KEBS team to resolve",2e3):(this._toaster.showSuccess("Success","Time details updated successfully !",2e3),this.resetFormFields(),this.isFromModal?this.closeDialog("Updated"):this.timeDetailsRes.emit({isCompleted:!0}))},e=>{this.loaderObject.isFormSubmitLoading=!1,console.log(e),this._toaster.showError("Error","Failed to save. Kindly contact KEBS team to resolve",2e3)})}else this._toaster.showWarning("No changes","No new changes were made !");else this._toaster.showWarning("Invalid data","Kindly fill all mandatory fields to proceed !")}skipSection(){this.isFromModal?this.closeDialog("Close"):this.timeDetailsRes.emit({isCompleted:!1,isSkipped:!0})}validateIfChanged(){let e=Object.assign(Object.assign({},this.timeDetailsPayload),this.effectiveDatePayload);console.log(e);let t=Object.keys(e);for(let i of t)if(e[i].isChanged)return!0;return!1}resetFormFields(){this.effectiveDateFormGroup.reset(),this.timeDetailsFormGroup.reset()}closeDialog(e){this.dialogRef.close(e)}setEffectiveDateValidation(){let e=this._edService.getEffectiveDate(this.employeeDoj);this.effectiveDateFormGroup.get("effectiveDate").patchValue(e,{emitEvent:!1})}handleTimeChange(){if(this.timeDetailsFormGroup.get("inTime").valid&&this.timeDetailsFormGroup.get("outTime").valid){let e=this.timeDetailsFormGroup.get("inTime").value,t=this.timeDetailsFormGroup.get("outTime").value,i=a.findWhere(this.inTimeMaster,{id:e}),n=a.findWhere(this.outTimeMaster,{id:t});if(i&&n){i=o(i.in_time,"h:mm:ss"),n=o(n.out_time,"h:mm:ss");let e=this.calcualteTimeDiff(i,n);this.timeDetailsFormGroup.get("dailyWH").patchValue(e),this.timeDetailsFormGroup.get("monthlyWH").patchValue(22*e)}}else this.timeDetailsFormGroup.get("dailyWH").reset(),this.timeDetailsFormGroup.get("monthlyWH").reset()}calcualteTimeDiff(e,t){let i;if(e<t)i=o.duration(t.diff(e)).asHours();else{let n=o("24:00:00","h:mm:ss"),l=o("00:00:00","h:mm:ss");i=o.duration(n.diff(e)).asHours()+o.duration(t.diff(l)).asHours()}return i}handleDateFormatPayLoad(e){e.effectiveDate.value=e.effectiveDate.value?o(e.effectiveDate.value).format("YYYY-MM-DD"):""}handleTenantWiseFieldConfig(){return Object(n.c)(this,void 0,void 0,(function*(){let e=yield this.getFieldTenantConfig("time_details");e.length>0&&e.forEach((e,t)=>{this.tenantFields[e.field]={isMandatory:!!e.is_mandatory,isActiveField:!!e.is_active_field}})}))}getFieldTenantConfig(e){return new Promise((t,i)=>{this.subs.sink=this._edService.getFieldTenantConfig(e).subscribe(e=>{t(e.data)},e=>{console.log(e),i(e)})})}ngOnDestroy(){this.subs.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(l["\u0275\u0275directiveInject"](C.a),l["\u0275\u0275directiveInject"](_.a),l["\u0275\u0275directiveInject"](p.i),l["\u0275\u0275directiveInject"](l.Injector))},e.\u0275cmp=l["\u0275\u0275defineComponent"]({type:e,selectors:[["ed-time-details"]],inputs:{associateId:"associateId"},outputs:{timeDetailsRes:"timeDetailsRes"},decls:3,vars:2,consts:[[1,"container-fluid","time-details"],[4,"ngIf"],[1,"d-flex","justify-content-center","mt-3"],["matTooltip","Please wait...","diameter","30"],[1,"row","section-header","mb-2"],[1,"slide-from-down",3,"formGroup","keydown.enter"],[1,"row"],[1,"col-3","px-0","mr-4"],[1,"row","field-title"],[1,"required-star"],["appearance","outline",2,"width","100%"],["matInput","","required","","formControlName","effectiveDate","placeholder","DD MM YYYY","readonly","",3,"matDatepicker","min"],["matSuffix","",3,"for"],["picker1",""],["hideMatLabel","false","required","true","placeholder","Select One","hideMatLabel","false","formControlName","inTime",2,"width","100%",3,"list","disableNone","change"],["hideMatLabel","false","required","true","placeholder","Select One","formControlName","outTime",2,"width","100%",3,"list","disableNone","change"],[1,"col-3","px-0","mr-4","my-auto"],["formControlName","isNextCalenderDay",1,"mr-3","field-title"],["required","","matInput","","placeholder","Enter one","formControlName","dailyWH","maxlength","9","digitOnly","",3,"allowDecimal"],["required","","matInput","","placeholder","Enter here","formControlName","monthlyWH","digitOnly","","maxlength","9",3,"allowDecimal"],["hideMatLabel","false","required","true","placeholder","Select One","formControlName","workclassification",2,"width","100%",3,"list","disableNone"],["hideMatLabel","false","required","true","placeholder","Select One","formControlName","workSchedule",2,"width","100%",3,"list","disableNone"],["hideMatLabel","false","required","true","placeholder","Select One","formControlName","holidayCalender",2,"width","100%",3,"list","disableNone"],["hideMatLabel","false","required","true","placeholder","Select One","formControlName","workLocation",2,"width","100%",3,"list","disableNone"],["class","col-3 px-0 mr-4",4,"ngIf"],[1,"mr-3",2,"color","#45546e","font-size","16px"],[3,"checked","change"],[1,"row","mt-4"],[1,"d-flex","flex-row"],["type","button","mat-stroked-button","",1,"cancel-btn",3,"click"],["type","button","mat-raised-button","",1,"ml-3","create-employee-btn",3,"disabled","click"],["class","required-star",4,"ngIf"],["matInput","","placeholder","Enter here","formControlName","projectHours","digitOnly","","maxlength","9",3,"allowDecimal","required"]],template:function(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"div",0),l["\u0275\u0275template"](1,k,3,0,"ng-container",1),l["\u0275\u0275template"](2,I,110,27,"ng-container",1),l["\u0275\u0275elementEnd"]()),2&e&&(l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",t.loaderObject.isComponentLoading),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",!t.loaderObject.isComponentLoading))},directives:[r.NgIf,g.c,y.a,p.J,p.w,p.n,d.c,c.b,p.e,u.g,p.F,p.v,p.l,u.i,d.i,u.f,w.a,E.a,p.q,x.a,S.a,v.a],styles:[".time-details[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]{font-size:16px;font-weight:500;color:#45546e}.time-details[_ngcontent-%COMP%]   .field-title[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#6e7b8f}.time-details[_ngcontent-%COMP%]   .required-star[_ngcontent-%COMP%]{color:#cf0001}.time-details[_ngcontent-%COMP%]     .mat-form-field-outline, .time-details[_ngcontent-%COMP%]     mat-datepicker-toggle{color:#b9c0ca!important}.time-details[_ngcontent-%COMP%]   .add-link[_ngcontent-%COMP%]{text-decoration:underline;color:#f27a6c;font-size:14px;cursor:pointer;font-weight:500}.time-details[_ngcontent-%COMP%]   .create-employee-btn[_ngcontent-%COMP%]{background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%)!important;color:#fff;height:42px}.time-details[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{color:#45546e;height:42px}.time-details[_ngcontent-%COMP%]     .mat-form-field-disabled{opacity:.5}.time-details[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}"]}),e})(),P=(()=>{class e{}return e.\u0275mod=l["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=l["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},providers:[{provide:h.c,useClass:b.c,deps:[h.f,b.a]},{provide:h.e,useValue:{parse:{dateInput:"DD-MM-YYYY"},display:{dateInput:"DD-MM-YYYY",monthYearLabel:"MMM YYYY"}}}],imports:[[r.CommonModule,m.a,d.e,c.c,p.E,p.p,u.h,h.x,f.b,v.b,g.b,y.b,S.b,E.b,T.a]]}),e})()},NJ67:function(e,t,i){"use strict";i.d(t,"a",(function(){return n}));class n{constructor(){this.disabled=!1}onChange(e){}onTouched(e){}writeValue(e){this.value=e}registerOnChange(e){this.onChange=e}registerOnTouched(e){this.onTouched=e}setDisabledState(e){this.disabled=e}}},"TmG/":function(e,t,i){"use strict";i.d(t,"a",(function(){return y}));var n=i("fXoL"),l=i("3Pt+"),a=i("jtHE"),o=i("XNiG"),s=i("NJ67"),r=i("1G5W"),m=i("kmnG"),d=i("ofXK"),c=i("d3UM"),p=i("FKr1"),u=i("WJ5W"),h=i("Qu3c");function f(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"mat-label"),n["\u0275\u0275text"](1),n["\u0275\u0275elementEnd"]()),2&e){const e=n["\u0275\u0275nextContext"]();n["\u0275\u0275advance"](1),n["\u0275\u0275textInterpolate"](e.placeholder)}}function v(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"mat-option",7),n["\u0275\u0275text"](1),n["\u0275\u0275elementEnd"]()),2&e){const e=n["\u0275\u0275nextContext"]();n["\u0275\u0275property"]("value",null),n["\u0275\u0275advance"](1),n["\u0275\u0275textInterpolate"](e.disableNone?"Select One":"None")}}function g(e,t){if(1&e){const e=n["\u0275\u0275getCurrentView"]();n["\u0275\u0275elementStart"](0,"mat-option",8),n["\u0275\u0275listener"]("click",(function(){n["\u0275\u0275restoreView"](e);const i=t.$implicit;return n["\u0275\u0275nextContext"]().emitChanges(i)})),n["\u0275\u0275text"](1),n["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;n["\u0275\u0275property"]("value",e.id||e._id)("matTooltip",e.name),n["\u0275\u0275advance"](1),n["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let y=(()=>{class e extends s.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new l.j,this.fieldFilterCtrl=new l.j,this.list=[],this.required=!1,this.disableNone=!1,this.valueChange=new n.EventEmitter,this.disabled=!1,this.hasNoneOption=!0,this.filteredList=new a.a,this.change=new n.EventEmitter,this.hideMatLabel=!1,this._onDestroy=new o.b,this.emitChanges=e=>{this.change.emit(e)}}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(r.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(r.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}}return e.\u0275fac=function(t){return new(t||e)(n["\u0275\u0275directiveInject"](n.Renderer2))},e.\u0275cmp=n["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search"]],inputs:{list:"list",placeholder:"placeholder",required:"required",disableNone:"disableNone",disabled:"disabled",hasNoneOption:"hasNoneOption",hideMatLabel:"hideMatLabel"},outputs:{valueChange:"valueChange",change:"change"},features:[n["\u0275\u0275ProvidersFeature"]([{provide:l.t,useExisting:Object(n.forwardRef)(()=>e),multi:!0}]),n["\u0275\u0275InheritDefinitionFeature"],n["\u0275\u0275NgOnChangesFeature"]],decls:9,vars:11,consts:[["appearance","outline"],[4,"ngIf"],[3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value",4,"ngIf"],[3,"value","matTooltip","click",4,"ngFor","ngForOf"],[3,"value"],[3,"value","matTooltip","click"]],template:function(e,t){1&e&&(n["\u0275\u0275elementStart"](0,"mat-form-field",0),n["\u0275\u0275template"](1,f,2,1,"mat-label",1),n["\u0275\u0275elementStart"](2,"mat-select",2,3),n["\u0275\u0275elementStart"](4,"mat-option"),n["\u0275\u0275element"](5,"ngx-mat-select-search",4),n["\u0275\u0275elementEnd"](),n["\u0275\u0275template"](6,v,2,2,"mat-option",5),n["\u0275\u0275template"](7,g,2,3,"mat-option",6),n["\u0275\u0275pipe"](8,"async"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e&&(n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf",!t.hideMatLabel),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),n["\u0275\u0275advance"](3),n["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf",t.hasNoneOption),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngForOf",n["\u0275\u0275pipeBind1"](8,9,t.filteredList)))},directives:[m.c,d.NgIf,c.c,l.v,l.k,l.F,p.p,u.a,d.NgForOf,m.g,h.a],pipes:[d.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%}"]}),e})()},dmRi:function(e,t,i){"use strict";i.r(t),i.d(t,"TimeDetailsComponent",(function(){return _})),i.d(t,"TimeDetailsModule",(function(){return w}));var n=i("mrSG"),l=i("33Jv"),a=i("ofXK"),o=i("Xi0T"),s=i("NFeN"),r=i("bTqV"),m=i("Xa2L"),d=i("Qu3c"),c=i("STbY"),p=i("fXoL"),u=i("jAlA"),h=i("1A3m"),f=i("0IaG");const v=["menuTrigger"];function g(e,t){1&e&&(p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275elementStart"](1,"div",2),p["\u0275\u0275element"](2,"mat-spinner",3),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementContainerEnd"]())}function y(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"div",29),p["\u0275\u0275elementStart"](1,"span",30),p["\u0275\u0275text"](2,"Add Time Details By Clicking the Button Below"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]())}function S(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",25),p["\u0275\u0275elementStart"](1,"button",31),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](3).editTimeDetails()})),p["\u0275\u0275pipe"](2,"async"),p["\u0275\u0275pipe"](3,"async"),p["\u0275\u0275text"](4," Add Details > "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"](3);p["\u0275\u0275advance"](1),p["\u0275\u0275propertyInterpolate"]("disabled",p["\u0275\u0275pipeBind1"](2,2,e.$isEmpRetired)),p["\u0275\u0275property"]("ngClass",p["\u0275\u0275pipeBind1"](3,4,e.$isEmpRetired)?"disabled-add-button":"add-details-btn")}}function D(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",21),p["\u0275\u0275elementStart"](1,"div",22),p["\u0275\u0275elementStart"](2,"div",23),p["\u0275\u0275element"](3,"img",24),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](4,"div",25),p["\u0275\u0275elementStart"](5,"span",26),p["\u0275\u0275text"](6,"No Data Here"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](7,y,3,0,"div",27),p["\u0275\u0275template"](8,S,5,6,"div",28),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](2);p["\u0275\u0275advance"](7),p["\u0275\u0275property"]("ngIf",e.showAddButton),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",e.showAddButton)}}function b(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"span",39),p["\u0275\u0275elementStart"](1,"mat-icon",40),p["\u0275\u0275text"](2,"dark_mode"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]())}function E(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"span",39),p["\u0275\u0275elementStart"](1,"mat-icon",40),p["\u0275\u0275text"](2,"dark_mode"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]())}function T(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",32),p["\u0275\u0275elementStart"](1,"div",33),p["\u0275\u0275elementStart"](2,"div",22),p["\u0275\u0275elementStart"](3,"div",7),p["\u0275\u0275elementStart"](4,"span",34),p["\u0275\u0275text"](5,"In Time"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](6,"div",35),p["\u0275\u0275elementStart"](7,"span",36),p["\u0275\u0275text"](8),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](9,b,3,0,"span",37),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](10,"div",22),p["\u0275\u0275elementStart"](11,"div",7),p["\u0275\u0275elementStart"](12,"span",34),p["\u0275\u0275text"](13,"Out Time"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](14,"div",35),p["\u0275\u0275elementStart"](15,"span",36),p["\u0275\u0275text"](16),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](17,E,3,0,"span",37),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](18,"div",38),p["\u0275\u0275elementStart"](19,"div",22),p["\u0275\u0275elementStart"](20,"div",7),p["\u0275\u0275elementStart"](21,"span",34),p["\u0275\u0275text"](22,"Daily Hours"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](23,"div",7),p["\u0275\u0275elementStart"](24,"span",36),p["\u0275\u0275text"](25),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](26,"div",22),p["\u0275\u0275elementStart"](27,"div",7),p["\u0275\u0275elementStart"](28,"span",34),p["\u0275\u0275text"](29,"Monthly Hours"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](30,"div",7),p["\u0275\u0275elementStart"](31,"span",36),p["\u0275\u0275text"](32),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](33,"div",38),p["\u0275\u0275elementStart"](34,"div",22),p["\u0275\u0275elementStart"](35,"div",7),p["\u0275\u0275elementStart"](36,"span",34),p["\u0275\u0275text"](37,"Work Schedule"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](38,"div",7),p["\u0275\u0275elementStart"](39,"span",36),p["\u0275\u0275text"](40),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](41,"div",22),p["\u0275\u0275elementStart"](42,"div",7),p["\u0275\u0275elementStart"](43,"span",34),p["\u0275\u0275text"](44,"Holiday Calendar"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](45,"div",7),p["\u0275\u0275elementStart"](46,"span",36),p["\u0275\u0275text"](47),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](48,"div",22),p["\u0275\u0275elementStart"](49,"div",7),p["\u0275\u0275elementStart"](50,"span",34),p["\u0275\u0275text"](51,"Work Location"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](52,"div",7),p["\u0275\u0275elementStart"](53,"span",36),p["\u0275\u0275text"](54),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](55,"div",38),p["\u0275\u0275elementStart"](56,"div",22),p["\u0275\u0275elementStart"](57,"div",7),p["\u0275\u0275elementStart"](58,"span",34),p["\u0275\u0275text"](59,"Work Classification"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](60,"div",7),p["\u0275\u0275elementStart"](61,"span",36),p["\u0275\u0275text"](62),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](63,"div",38),p["\u0275\u0275elementStart"](64,"div",22),p["\u0275\u0275elementStart"](65,"div",7),p["\u0275\u0275elementStart"](66,"span",34),p["\u0275\u0275text"](67,"Auto Timesheet Submission"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](68,"div",7),p["\u0275\u0275elementStart"](69,"span",36),p["\u0275\u0275text"](70),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](71,"div",22),p["\u0275\u0275elementStart"](72,"div",7),p["\u0275\u0275elementStart"](73,"span",34),p["\u0275\u0275text"](74,"Timesheet Submission"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](75,"div",7),p["\u0275\u0275elementStart"](76,"span",36),p["\u0275\u0275text"](77),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](78,"div",22),p["\u0275\u0275elementStart"](79,"div",7),p["\u0275\u0275elementStart"](80,"span",34),p["\u0275\u0275text"](81,"Project Available Hours"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](82,"div",7),p["\u0275\u0275elementStart"](83,"span",36),p["\u0275\u0275text"](84),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](85,"div",38),p["\u0275\u0275elementStart"](86,"div",22),p["\u0275\u0275elementStart"](87,"div",7),p["\u0275\u0275elementStart"](88,"span",34),p["\u0275\u0275text"](89,"Leave Auto Approval"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](90,"div",7),p["\u0275\u0275elementStart"](91,"span",36),p["\u0275\u0275text"](92),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](93,"div",22),p["\u0275\u0275elementStart"](94,"div",7),p["\u0275\u0275elementStart"](95,"span",34),p["\u0275\u0275text"](96,"Allow Editing Of Timesheet"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](97,"div",7),p["\u0275\u0275elementStart"](98,"span",36),p["\u0275\u0275text"](99),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](2);p["\u0275\u0275advance"](7),p["\u0275\u0275propertyInterpolate"]("matTooltip",null!=e.employeeTimeDetails&&e.employeeTimeDetails.in_time?null==e.employeeTimeDetails?null:e.employeeTimeDetails.in_time:"-"),p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" ",null!=e.employeeTimeDetails&&e.employeeTimeDetails.in_time?null==e.employeeTimeDetails?null:e.employeeTimeDetails.in_time:"-"," "),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",null==e.employeeTimeDetails?null:e.employeeTimeDetails.is_next_calender_day),p["\u0275\u0275advance"](6),p["\u0275\u0275propertyInterpolate"]("matTooltip",null!=e.employeeTimeDetails&&e.employeeTimeDetails.out_time?null==e.employeeTimeDetails?null:e.employeeTimeDetails.out_time:"-"),p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" ",null!=e.employeeTimeDetails&&e.employeeTimeDetails.out_time?null==e.employeeTimeDetails?null:e.employeeTimeDetails.out_time:"-"," "),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",null==e.employeeTimeDetails?null:e.employeeTimeDetails.is_next_calender_day),p["\u0275\u0275advance"](7),p["\u0275\u0275propertyInterpolate"]("matTooltip",null!=e.employeeTimeDetails&&e.employeeTimeDetails.daily_working_hours?null==e.employeeTimeDetails?null:e.employeeTimeDetails.daily_working_hours:"-"),p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" ",null!=e.employeeTimeDetails&&e.employeeTimeDetails.daily_working_hours?null==e.employeeTimeDetails?null:e.employeeTimeDetails.daily_working_hours:"-"," "),p["\u0275\u0275advance"](6),p["\u0275\u0275propertyInterpolate"]("matTooltip",null!=e.employeeTimeDetails&&e.employeeTimeDetails.monthly_working_hours?null==e.employeeTimeDetails?null:e.employeeTimeDetails.monthly_working_hours:"-"),p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" ",null!=e.employeeTimeDetails&&e.employeeTimeDetails.monthly_working_hours?null==e.employeeTimeDetails?null:e.employeeTimeDetails.monthly_working_hours:"-"," "),p["\u0275\u0275advance"](7),p["\u0275\u0275propertyInterpolate"]("matTooltip",null!=e.employeeTimeDetails&&e.employeeTimeDetails.work_schedule?null==e.employeeTimeDetails?null:e.employeeTimeDetails.work_schedule:"-"),p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" ",null!=e.employeeTimeDetails&&e.employeeTimeDetails.work_schedule?null==e.employeeTimeDetails?null:e.employeeTimeDetails.work_schedule:"-"," "),p["\u0275\u0275advance"](6),p["\u0275\u0275propertyInterpolate"]("matTooltip",null!=e.employeeTimeDetails&&e.employeeTimeDetails.holiday_calendar?null==e.employeeTimeDetails?null:e.employeeTimeDetails.holiday_calendar:"-"),p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" ",null!=e.employeeTimeDetails&&e.employeeTimeDetails.holiday_calendar?null==e.employeeTimeDetails?null:e.employeeTimeDetails.holiday_calendar:"-"," "),p["\u0275\u0275advance"](6),p["\u0275\u0275propertyInterpolate"]("matTooltip",null!=e.employeeTimeDetails&&e.employeeTimeDetails.work_location?null==e.employeeTimeDetails?null:e.employeeTimeDetails.work_location:"-"),p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" ",null!=e.employeeTimeDetails&&e.employeeTimeDetails.work_location?null==e.employeeTimeDetails?null:e.employeeTimeDetails.work_location:"-"," "),p["\u0275\u0275advance"](7),p["\u0275\u0275propertyInterpolate"]("matTooltip",null!=e.employeeTimeDetails&&e.employeeTimeDetails.work_classification?null==e.employeeTimeDetails?null:e.employeeTimeDetails.work_classification:"-"),p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" ",null!=e.employeeTimeDetails&&e.employeeTimeDetails.work_classification?null==e.employeeTimeDetails?null:e.employeeTimeDetails.work_classification:"-"," "),p["\u0275\u0275advance"](7),p["\u0275\u0275propertyInterpolate"]("matTooltip",null!=e.employeeTimeDetails&&e.employeeTimeDetails.auto_timesheet_submission?null==e.employeeTimeDetails?null:e.employeeTimeDetails.auto_timesheet_submission:"-"),p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" ",null!=e.employeeTimeDetails&&e.employeeTimeDetails.auto_timesheet_submission?null==e.employeeTimeDetails?null:e.employeeTimeDetails.auto_timesheet_submission:"-"," "),p["\u0275\u0275advance"](6),p["\u0275\u0275propertyInterpolate"]("matTooltip",null!=e.employeeTimeDetails&&e.employeeTimeDetails.timesheet_submission?null==e.employeeTimeDetails?null:e.employeeTimeDetails.timesheet_submission:"-"),p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" ",null!=e.employeeTimeDetails&&e.employeeTimeDetails.timesheet_submission?null==e.employeeTimeDetails?null:e.employeeTimeDetails.timesheet_submission:"-"," "),p["\u0275\u0275advance"](6),p["\u0275\u0275propertyInterpolate"]("matTooltip",null!=e.employeeTimeDetails&&e.employeeTimeDetails.project_available_hours?null==e.employeeTimeDetails?null:e.employeeTimeDetails.project_available_hours:"-"),p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" ",null!=e.employeeTimeDetails&&e.employeeTimeDetails.project_available_hours?null==e.employeeTimeDetails?null:e.employeeTimeDetails.project_available_hours:"-"," "),p["\u0275\u0275advance"](7),p["\u0275\u0275propertyInterpolate"]("matTooltip",null!=e.employeeTimeDetails&&e.employeeTimeDetails.leave_auto_approve?null==e.employeeTimeDetails?null:e.employeeTimeDetails.leave_auto_approve:"-"),p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" ",null!=e.employeeTimeDetails&&e.employeeTimeDetails.leave_auto_approve?null==e.employeeTimeDetails?null:e.employeeTimeDetails.leave_auto_approve:"-"," "),p["\u0275\u0275advance"](6),p["\u0275\u0275propertyInterpolate"]("matTooltip",null!=e.employeeTimeDetails&&e.employeeTimeDetails.is_timesheet_edit_allowed?null==e.employeeTimeDetails?null:e.employeeTimeDetails.is_timesheet_edit_allowed:"-"),p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" ",null!=e.employeeTimeDetails&&e.employeeTimeDetails.is_timesheet_edit_allowed?null==e.employeeTimeDetails?null:e.employeeTimeDetails.is_timesheet_edit_allowed:"-"," ")}}function C(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275elementStart"](1,"div",4),p["\u0275\u0275elementStart"](2,"mat-card",5),p["\u0275\u0275elementStart"](3,"div",6),p["\u0275\u0275elementStart"](4,"div",7),p["\u0275\u0275elementStart"](5,"div",8),p["\u0275\u0275elementStart"](6,"span",9),p["\u0275\u0275text"](7,"Time Details"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](8,"div",10),p["\u0275\u0275elementStart"](9,"button",11,12),p["\u0275\u0275elementStart"](11,"mat-icon",13),p["\u0275\u0275text"](12," edit "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](13,"span",14),p["\u0275\u0275text"](14," Edit "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](15,"mat-icon",15),p["\u0275\u0275text"](16," keyboard_arrow_down "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](17,"mat-menu",null,16),p["\u0275\u0275elementStart"](19,"button",17),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"]().editTimeDetails()})),p["\u0275\u0275pipe"](20,"async"),p["\u0275\u0275text"](21," Edit "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](22,"button",18),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"]().openEditHistory()})),p["\u0275\u0275text"](23," Edit History "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](24,D,9,2,"div",19),p["\u0275\u0275template"](25,T,100,28,"div",20),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=p["\u0275\u0275reference"](18),t=p["\u0275\u0275nextContext"]();p["\u0275\u0275advance"](9),p["\u0275\u0275property"]("matMenuTriggerFor",e)("disabled",t.isNoDataFound),p["\u0275\u0275advance"](10),p["\u0275\u0275propertyInterpolate"]("disabled",!t.showEditButton||p["\u0275\u0275pipeBind1"](20,5,t.$isEmpRetired)),p["\u0275\u0275advance"](5),p["\u0275\u0275property"]("ngIf",t.isNoDataFound),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!t.isNoDataFound)}}let _=(()=>{class e{constructor(e,t,i){this._edService=e,this._toaster=t,this.dialog=i,this.subs=new l.a,this.isNoDataFound=!1,this.showAddButton=!1,this.showEditButton=!1,this.loaderObject={isComponentLoading:!1}}ngOnInit(){return Object(n.c)(this,void 0,void 0,(function*(){this.loaderObject.isComponentLoading=!0,this.employeeTimeDetails=yield this.getEmployeeTimeDetails(this.associateId);let e=this._edService.checkViewAndEditAccess(189);this.showAddButton=e,this.showEditButton=e,this.$isEmpRetired=this._edService.getEmployeeRetiredStatus(),this.isNoDataFound=!(this.employeeTimeDetails.length>0),this.employeeTimeDetails=this.employeeTimeDetails[0],this.loaderObject.isComponentLoading=!1}))}getEmployeeTimeDetails(e){if(e)return new Promise((t,i)=>{this.subs.sink=this._edService.getEmployeeTimeDetails(e).subscribe(e=>{t(e.data)},e=>{this._toaster.showError("Error","Failed to retrieve employee !",2e3),console.log(e),i(e)})})}editTimeDetails(){return Object(n.c)(this,void 0,void 0,(function*(){this.trigger.closeMenu();let e={associateId:parseInt(this.associateId),isFromModal:!0};const{TimeDetailsComponent:t}=yield Promise.resolve().then(i.bind(null,"+JMY"));this.dialog.open(t,{height:"75%",width:"80vw",panelClass:"e360-time-details-modalbox",data:{modalParams:e}}).afterClosed().subscribe(e=>{"Updated"==e&&this.ngOnInit()},e=>{this._toaster.showError("Error","Failed to retrieve employee !",2e3)})}))}openEditHistory(){return Object(n.c)(this,void 0,void 0,(function*(){this.trigger.closeMenu();let e={associateId:parseInt(this.associateId),tabKey:"time_details"};const{EditHistoryModalComponent:t}=yield Promise.all([i.e(0),i.e(955)]).then(i.bind(null,"Qcrq"));this.dialog.open(t,{height:"100%",width:"40%",position:{right:"0px"},data:{modalParams:e}})}))}handleTenantWiseFieldConfig(){return Object(n.c)(this,void 0,void 0,(function*(){let e=yield this.getFieldTenantConfig("time_details");e.length>0&&e.forEach((e,t)=>{this.tenantFields[e.field]={isMandatory:!!e.is_mandatory,isActiveField:!!e.is_active_field}})}))}getFieldTenantConfig(e){return new Promise((t,i)=>{this.subs.sink=this._edService.getFieldTenantConfig(e).subscribe(e=>{t(e.data)},e=>{console.log(e),i(e)})})}ngOnDestroy(){this.subs.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(p["\u0275\u0275directiveInject"](u.a),p["\u0275\u0275directiveInject"](h.a),p["\u0275\u0275directiveInject"](f.b))},e.\u0275cmp=p["\u0275\u0275defineComponent"]({type:e,selectors:[["app-time-details"]],viewQuery:function(e,t){if(1&e&&p["\u0275\u0275viewQuery"](v,!0),2&e){let e;p["\u0275\u0275queryRefresh"](e=p["\u0275\u0275loadQuery"]())&&(t.trigger=e.first)}},inputs:{associateId:"associateId"},decls:3,vars:2,consts:[[1,"container-fluid","p-0","pr-1","mt-2","time-details-styles"],[4,"ngIf"],[1,"d-flex","justify-content-center","mt-3"],["matTooltip","Please wait...","diameter","30"],[1,"row","pt-3","pb-2"],[1,"pl-0","mat-card","slide-from-down",2,"min-height","75vh"],[1,"col-12","px-2","py-2"],[1,"row"],[1,"col-10"],[1,"section-heading"],[1,"col-2"],["mat-button","",1,"edit-button",3,"matMenuTriggerFor","disabled"],["menuTrigger","matMenuTrigger"],[1,"edit-icon"],[1,"pl-0","edit-text"],[1,"pl-0","drop-down-icon"],["editMenu","matMenu"],["mat-menu-item","",3,"disabled","click"],["mat-menu-item","",3,"click"],["class","row pt-5 pb-2 justify-content-center",4,"ngIf"],["class","col-12 p-0",4,"ngIf"],[1,"row","pt-5","pb-2","justify-content-center"],[1,"col-4"],[1,"d-flex","justify-content-center","align-items-center","slide-from-down"],["src","https://assets.kebs.app/images/empty_re_opql.png","height","150","width","200",1,"mt-2"],[1,"pt-3","no-data-found-text"],[1,"item-value",2,"font-size","15px !important","font-weight","700 !important"],["class","pt-2 no-data-found-text",4,"ngIf"],["class","pt-3 no-data-found-text",4,"ngIf"],[1,"pt-2","no-data-found-text"],[1,"item-value",2,"white-space","normal !important","text-align","center","font-weight","400 !important"],["mat-raised-button","",1,"ml-3",3,"disabled","ngClass","click"],[1,"col-12","p-0"],[1,"row","pt-2"],[1,"title-heading"],[1,"row","d-flex"],[1,"item-value",3,"matTooltip"],["class","pl-2 creacent-icon",4,"ngIf"],[1,"row","pt-3"],[1,"pl-2","creacent-icon"],[2,"font-size","20px"]],template:function(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"div",0),p["\u0275\u0275template"](1,g,3,0,"ng-container",1),p["\u0275\u0275template"](2,C,26,7,"ng-container",1),p["\u0275\u0275elementEnd"]()),2&e&&(p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",t.loaderObject.isComponentLoading),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!t.loaderObject.isComponentLoading))},directives:[a.NgIf,m.c,d.a,r.a,c.f,s.a,c.g,c.d,a.NgClass],pipes:[a.AsyncPipe],styles:[".time-details-styles[_ngcontent-%COMP%]{overflow-x:auto;height:70vh;scrollbar-width:none}.time-details-styles[_ngcontent-%COMP%]::-webkit-scrollbar{display:none}.time-details-styles[_ngcontent-%COMP%]   .add-details-btn[_ngcontent-%COMP%]{background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%)!important;color:#fff;height:42px}.time-details-styles[_ngcontent-%COMP%]   .disabled-add-button[_ngcontent-%COMP%]{background:#d3d3d3;color:grey}.time-details-styles[_ngcontent-%COMP%]   .no-data-found-text[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}.time-details-styles[_ngcontent-%COMP%]   .creacent-icon[_ngcontent-%COMP%]{color:#45546e}.time-details-styles[_ngcontent-%COMP%]   .mat-card[_ngcontent-%COMP%]{width:100%;border:1px solid #fff;background-color:#fff;border-radius:5px}.time-details-styles[_ngcontent-%COMP%]   .title-heading[_ngcontent-%COMP%]{font-weight:400;color:#8b95a5}.time-details-styles[_ngcontent-%COMP%]   .item-value[_ngcontent-%COMP%], .time-details-styles[_ngcontent-%COMP%]   .title-heading[_ngcontent-%COMP%]{font-size:14px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;text-transform:capitalize;max-width:96%}.time-details-styles[_ngcontent-%COMP%]   .item-value[_ngcontent-%COMP%]{font-weight:500;color:#45546e}.time-details-styles[_ngcontent-%COMP%]   .section-heading[_ngcontent-%COMP%]{color:#ee4961;font-size:15px;font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;text-transform:capitalize;max-width:96%}.time-details-styles[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%]{border:1px solid #dadce2;border-radius:4px;line-height:24px;padding:2px;float:right;display:flex;flex-direction:row;align-items:center}.time-details-styles[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%]   .edit-icon[_ngcontent-%COMP%]{font-size:14px;line-height:22px}.time-details-styles[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%]   .edit-text[_ngcontent-%COMP%]{color:#45546e;font-size:14px;font-weight:400}.time-details-styles[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%]   .drop-down-icon[_ngcontent-%COMP%]{font-size:21px;line-height:24px}.time-details-styles[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}.time-details-styles[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}  .e360-time-details-modalbox mat-dialog-container{padding:24px!important;overflow:auto}"]}),e})(),w=(()=>{class e{}return e.\u0275mod=p["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=p["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[a.CommonModule,o.a,s.b,r.b,m.b,d.b,c.e]]}),e})()}}]);