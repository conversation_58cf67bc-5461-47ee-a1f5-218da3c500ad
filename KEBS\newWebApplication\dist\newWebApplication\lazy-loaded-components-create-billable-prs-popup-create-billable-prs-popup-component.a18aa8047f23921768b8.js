(window.webpackJsonp=window.webpackJsonp||[]).push([[749],{"u6//":function(e,t,n){"use strict";n.r(t),n.d(t,"CreateBillablePrsPopupComponent",(function(){return re}));var i=n("mrSG"),o=n("0IaG"),a=n("wd/R"),l=n("1G5W"),r=n("XNiG"),s=n("xG9w"),c=n("PSD3"),p=n.n(c),m=n("Kj3r"),d=n("/uUt"),h=n("33Jv"),u=n("ofXK"),g=n("bTqV"),f=n("NFeN"),C=n("Qu3c"),b=n("kmnG"),x=n("qFsG"),y=n("3Pt+"),P=(n("d3UM"),n("bSwM")),v=n("YhS8"),_=(n("Xi0T"),n("Xa2L")),S=n("dlKe"),M=n("KFJe"),I=n("JqCM"),w=n("fXoL"),O=n("LcQX"),L=n("g1SM"),E=n("XXEo"),A=n("a1r6"),j=n("dNgK"),k=n("vxfF"),R=n("me71");function T(e,t){if(1&e){const e=w["\u0275\u0275getCurrentView"]();w["\u0275\u0275elementStart"](0,"button",34),w["\u0275\u0275listener"]("click",(function(){return w["\u0275\u0275restoreView"](e),w["\u0275\u0275nextContext"]().clearMilestoneName()})),w["\u0275\u0275elementStart"](1,"mat-icon",35),w["\u0275\u0275text"](2," close "),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"]()}}function V(e,t){if(1&e){const e=w["\u0275\u0275getCurrentView"]();w["\u0275\u0275elementStart"](0,"button",34),w["\u0275\u0275listener"]("click",(function(){return w["\u0275\u0275restoreView"](e),w["\u0275\u0275nextContext"]().clearCostCenterSearch()})),w["\u0275\u0275elementStart"](1,"mat-icon",36),w["\u0275\u0275text"](2," close "),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"]()}}function D(e,t){if(1&e&&(w["\u0275\u0275elementStart"](0,"div"),w["\u0275\u0275element"](1,"mat-spinner",37),w["\u0275\u0275elementEnd"]()),2&e){const e=w["\u0275\u0275nextContext"]();w["\u0275\u0275advance"](1),w["\u0275\u0275propertyInterpolate1"]("matTooltip","Loading ",e.costCenterFieldLabel?e.costCenterFieldLabel:"Cost center","")}}function N(e,t){1&e&&w["\u0275\u0275element"](0,"hr",46)}function B(e,t){if(1&e){const e=w["\u0275\u0275getCurrentView"]();w["\u0275\u0275elementStart"](0,"div",40),w["\u0275\u0275elementStart"](1,"div",41),w["\u0275\u0275elementStart"](2,"div",42),w["\u0275\u0275listener"]("click",(function(){w["\u0275\u0275restoreView"](e);const n=t.$implicit;return w["\u0275\u0275nextContext"](2).showBillableClaims(n)})),w["\u0275\u0275elementStart"](3,"span"),w["\u0275\u0275text"](4),w["\u0275\u0275elementStart"](5,"span",43),w["\u0275\u0275text"](6),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementStart"](7,"span",44),w["\u0275\u0275text"](8),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"](),w["\u0275\u0275template"](9,N,1,0,"hr",45),w["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=w["\u0275\u0275nextContext"](2);w["\u0275\u0275property"]("ngClass",n.selectedCCItem==e.name?"pr-cc-is-active":""),w["\u0275\u0275advance"](2),w["\u0275\u0275propertyInterpolate3"]("matTooltip","",null!=e&&e.name&&null!=(null==e?null:e.name)?null==e?null:e.name:""," - ",null!=e&&e.description&&null!=(null==e?null:e.description)?null==e?null:e.description:""," (",null!=e&&e.claim_count&&null!=(null==e?null:e.claim_count)?null==e?null:e.claim_count:"",")"),w["\u0275\u0275advance"](2),w["\u0275\u0275textInterpolate1"]("",null==e?null:e.name," - "),w["\u0275\u0275advance"](2),w["\u0275\u0275textInterpolate"](null==e?null:e.description),w["\u0275\u0275advance"](2),w["\u0275\u0275textInterpolate1"]("(",null==e?null:e.claim_count,")"),w["\u0275\u0275advance"](1),w["\u0275\u0275property"]("ngIf",n.costCenterPRList.length>1)}}function F(e,t){if(1&e){const e=w["\u0275\u0275getCurrentView"]();w["\u0275\u0275elementStart"](0,"div",38),w["\u0275\u0275listener"]("scrolled",(function(){return w["\u0275\u0275restoreView"](e),w["\u0275\u0275nextContext"]().getCostCenterList()})),w["\u0275\u0275element"](1,"div"),w["\u0275\u0275elementStart"](2,"div"),w["\u0275\u0275template"](3,B,10,8,"div",39),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"]()}if(2&e){const e=w["\u0275\u0275nextContext"]();w["\u0275\u0275classMapInterpolate1"]("col-12 p-0 ",!e.isProjectAndItemApisLoading&&e.isProjectAndItemVisible?"claim-list-with-project":"claim-list",""),w["\u0275\u0275property"]("infiniteScrollDistance",2)("scrollWindow",!1)("stopPropagation",!0),w["\u0275\u0275advance"](3),w["\u0275\u0275property"]("ngForOf",e.costCenterPRList)}}function G(e,t){1&e&&w["\u0275\u0275element"](0,"mat-spinner",47)}function Y(e,t){1&e&&(w["\u0275\u0275elementStart"](0,"p",11),w["\u0275\u0275text"](1," Project Name"),w["\u0275\u0275elementEnd"]())}function z(e,t){if(1&e&&(w["\u0275\u0275elementStart"](0,"p",48),w["\u0275\u0275text"](1),w["\u0275\u0275elementEnd"]()),2&e){const e=w["\u0275\u0275nextContext"]();w["\u0275\u0275property"]("matTooltip",e.projectName),w["\u0275\u0275advance"](1),w["\u0275\u0275textInterpolate1"](" ",e.projectName,"")}}function q(e,t){1&e&&(w["\u0275\u0275elementStart"](0,"p",11),w["\u0275\u0275text"](1," Item Name"),w["\u0275\u0275elementEnd"]())}function U(e,t){if(1&e&&(w["\u0275\u0275elementStart"](0,"p",48),w["\u0275\u0275text"](1),w["\u0275\u0275elementEnd"]()),2&e){const e=w["\u0275\u0275nextContext"]();w["\u0275\u0275property"]("matTooltip",e.itemName),w["\u0275\u0275advance"](1),w["\u0275\u0275textInterpolate1"](" ",e.itemName,"")}}function W(e,t){if(1&e){const e=w["\u0275\u0275getCurrentView"]();w["\u0275\u0275elementStart"](0,"button",34),w["\u0275\u0275listener"]("click",(function(){return w["\u0275\u0275restoreView"](e),w["\u0275\u0275nextContext"](2).clearSearch()})),w["\u0275\u0275elementStart"](1,"mat-icon",36),w["\u0275\u0275text"](2," close "),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"]()}}function K(e,t){if(1&e){const e=w["\u0275\u0275getCurrentView"]();w["\u0275\u0275elementStart"](0,"mat-form-field",49),w["\u0275\u0275elementStart"](1,"span",13),w["\u0275\u0275elementStart"](2,"mat-icon",14),w["\u0275\u0275text"](3,"search"),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementStart"](4,"input",50),w["\u0275\u0275listener"]("ngModelChange",(function(t){return w["\u0275\u0275restoreView"](e),w["\u0275\u0275nextContext"]().searchParameter=t}))("keyup.enter",(function(){return w["\u0275\u0275restoreView"](e),w["\u0275\u0275nextContext"]().callSearchApi()}))("ngModelChange",(function(){return w["\u0275\u0275restoreView"](e),w["\u0275\u0275nextContext"]().onSearchParameterChange()})),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementStart"](5,"mat-icon",9),w["\u0275\u0275template"](6,W,3,0,"button",10),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"]()}if(2&e){const e=w["\u0275\u0275nextContext"]();w["\u0275\u0275advance"](4),w["\u0275\u0275property"]("ngModel",e.searchParameter),w["\u0275\u0275advance"](2),w["\u0275\u0275property"]("ngIf",e.searchParameter&&""!=e.searchParameter)}}const X=function(){return["name"]};function $(e,t){if(1&e){const e=w["\u0275\u0275getCurrentView"]();w["\u0275\u0275elementStart"](0,"kebs-mul-sel-inf-search",51),w["\u0275\u0275listener"]("selectedValues",(function(t){return w["\u0275\u0275restoreView"](e),w["\u0275\u0275nextContext"]().selectSubGroup(t)})),w["\u0275\u0275elementEnd"]()}if(2&e){const e=w["\u0275\u0275nextContext"]();w["\u0275\u0275property"]("token",e.token)("optionLabel",w["\u0275\u0275pureFunction0"](4,X))("API_URL",e.searchSubGroups)("lazyLoadingCount",10)}}const J=function(){return{applyLabel:"Apply",format:"DD MMM YYYY",customRangeLabel:"Custom Duration"}};function H(e,t){if(1&e){const e=w["\u0275\u0275getCurrentView"]();w["\u0275\u0275elementStart"](0,"input",52),w["\u0275\u0275listener"]("ngModelChange",(function(t){return w["\u0275\u0275restoreView"](e),w["\u0275\u0275nextContext"]().duration=t}))("change",(function(t){return w["\u0275\u0275restoreView"](e),w["\u0275\u0275nextContext"]().selectDuration(t)})),w["\u0275\u0275elementEnd"]()}if(2&e){const e=w["\u0275\u0275nextContext"]();w["\u0275\u0275property"]("showCustomRangeLabel",!0)("locale",w["\u0275\u0275pureFunction0"](7,J))("alwaysShowCalendars",!0)("showCancel",!0)("ranges",e.durationRanges)("linkedCalendars",!0)("ngModel",e.duration)}}function Q(e,t){1&e&&(w["\u0275\u0275elementStart"](0,"div",53),w["\u0275\u0275element"](1,"mat-spinner",54),w["\u0275\u0275elementEnd"]())}function Z(e,t){if(1&e){const e=w["\u0275\u0275getCurrentView"]();w["\u0275\u0275elementStart"](0,"div",57),w["\u0275\u0275listener"]("click",(function(){w["\u0275\u0275restoreView"](e);const n=t.$implicit;return w["\u0275\u0275nextContext"](2).selectExpenseListItem(n)})),w["\u0275\u0275elementStart"](1,"div",58),w["\u0275\u0275element"](2,"mat-checkbox",59),w["\u0275\u0275elementStart"](3,"p",60),w["\u0275\u0275text"](4),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementStart"](5,"p",61),w["\u0275\u0275text"](6," - "),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementStart"](7,"p",62),w["\u0275\u0275text"](8),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementStart"](9,"div",63),w["\u0275\u0275elementStart"](10,"div",64),w["\u0275\u0275elementStart"](11,"p",65),w["\u0275\u0275text"](12,"PR Code"),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementStart"](13,"p",66),w["\u0275\u0275text"](14),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementStart"](15,"div",67),w["\u0275\u0275elementStart"](16,"p",65),w["\u0275\u0275text"](17,"Amount Raised"),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementStart"](18,"p",66),w["\u0275\u0275text"](19),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementStart"](20,"div",67),w["\u0275\u0275elementStart"](21,"p",65),w["\u0275\u0275text"](22,"Vendor Type"),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementStart"](23,"p",66),w["\u0275\u0275text"](24),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementStart"](25,"div",67),w["\u0275\u0275elementStart"](26,"p",65),w["\u0275\u0275text"](27,"Created By"),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementStart"](28,"div",68),w["\u0275\u0275element"](29,"app-user-image",69),w["\u0275\u0275elementStart"](30,"p",70),w["\u0275\u0275text"](31),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementStart"](32,"div",71),w["\u0275\u0275elementStart"](33,"p",65),w["\u0275\u0275text"](34,"Created On"),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementStart"](35,"p",66),w["\u0275\u0275text"](36),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;w["\u0275\u0275advance"](2),w["\u0275\u0275property"]("checked",e.isChecked),w["\u0275\u0275advance"](1),w["\u0275\u0275property"]("matTooltip",e.vendor_name),w["\u0275\u0275advance"](1),w["\u0275\u0275textInterpolate1"](" ",e.vendor_name,""),w["\u0275\u0275advance"](3),w["\u0275\u0275property"]("matTooltip",e.sub_group_name),w["\u0275\u0275advance"](1),w["\u0275\u0275textInterpolate1"](" ",e.sub_group_name,""),w["\u0275\u0275advance"](5),w["\u0275\u0275property"]("matTooltip",e.pr_code),w["\u0275\u0275advance"](1),w["\u0275\u0275textInterpolate"](e.pr_code),w["\u0275\u0275advance"](4),w["\u0275\u0275property"]("matTooltip",e.expense_request_amount),w["\u0275\u0275advance"](1),w["\u0275\u0275textInterpolate1"]("",e.expense_request_amount," "),w["\u0275\u0275advance"](4),w["\u0275\u0275property"]("matTooltip",e.vendor_type),w["\u0275\u0275advance"](1),w["\u0275\u0275textInterpolate1"]("",e.vendor_type," "),w["\u0275\u0275advance"](4),w["\u0275\u0275property"]("matTooltip",e.requested_by),w["\u0275\u0275advance"](1),w["\u0275\u0275property"]("id",e?e.requested_by_oid:"")("imgWidth","25px")("imgHeight","25px"),w["\u0275\u0275advance"](2),w["\u0275\u0275textInterpolate"](e.requested_by),w["\u0275\u0275advance"](4),w["\u0275\u0275property"]("matTooltip",e.billed_on),w["\u0275\u0275advance"](1),w["\u0275\u0275textInterpolate1"](" ",e.billed_on,"")}}function ee(e,t){if(1&e){const e=w["\u0275\u0275getCurrentView"]();w["\u0275\u0275elementStart"](0,"div",55),w["\u0275\u0275listener"]("scrolled",(function(){return w["\u0275\u0275restoreView"](e),w["\u0275\u0275nextContext"]().getPRListBasedOnCostCenter(!1)})),w["\u0275\u0275template"](1,Z,37,18,"div",56),w["\u0275\u0275elementEnd"]()}if(2&e){const e=w["\u0275\u0275nextContext"]();w["\u0275\u0275classMapInterpolate1"]("col-12 p-0 pl-3 pr-3 ",e.areExpenseMilestonesLoading?"milestone-list-loading-more":"milestone-list",""),w["\u0275\u0275property"]("infiniteScrollDistance",4)("scrollWindow",!1),w["\u0275\u0275advance"](1),w["\u0275\u0275property"]("ngForOf",e.PRList)}}function te(e,t){1&e&&w["\u0275\u0275element"](0,"mat-spinner",72)}function ne(e,t){if(1&e&&(w["\u0275\u0275elementStart"](0,"div",77),w["\u0275\u0275elementStart"](1,"p",78),w["\u0275\u0275text"](2,"Total Claim Value"),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementStart"](3,"p",79),w["\u0275\u0275text"](4),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"]()),2&e){const e=w["\u0275\u0275nextContext"](2);w["\u0275\u0275advance"](4),w["\u0275\u0275textInterpolate1"]("",e.totalClaimValue," ")}}function ie(e,t){1&e&&w["\u0275\u0275element"](0,"mat-spinner",80)}function oe(e,t){if(1&e){const e=w["\u0275\u0275getCurrentView"]();w["\u0275\u0275elementStart"](0,"button",81),w["\u0275\u0275listener"]("click",(function(){return w["\u0275\u0275restoreView"](e),w["\u0275\u0275nextContext"](2).createBillablePR()})),w["\u0275\u0275text"](1,"Create"),w["\u0275\u0275elementStart"](2,"mat-icon",82),w["\u0275\u0275text"](3,"navigate_next "),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"]()}if(2&e){const e=w["\u0275\u0275nextContext"](2);w["\u0275\u0275property"]("disabled",!e.areAnyExpensesSelected)}}function ae(e,t){if(1&e&&(w["\u0275\u0275elementStart"](0,"div",73),w["\u0275\u0275template"](1,ne,5,1,"div",74),w["\u0275\u0275template"](2,ie,1,0,"mat-spinner",75),w["\u0275\u0275template"](3,oe,4,1,"button",76),w["\u0275\u0275elementEnd"]()),2&e){const e=w["\u0275\u0275nextContext"]();w["\u0275\u0275advance"](1),w["\u0275\u0275property"]("ngIf",e.areAnyExpensesSelected),w["\u0275\u0275advance"](1),w["\u0275\u0275property"]("ngIf",e.isCreatingMilestone),w["\u0275\u0275advance"](1),w["\u0275\u0275property"]("ngIf",!e.isCreatingMilestone)}}function le(e,t){if(1&e&&(w["\u0275\u0275elementStart"](0,"div",83),w["\u0275\u0275element"](1,"img",84),w["\u0275\u0275elementStart"](2,"p",4),w["\u0275\u0275text"](3,"No PR Invoices Here"),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementStart"](4,"p",85),w["\u0275\u0275text"](5),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"]()),2&e){const e=w["\u0275\u0275nextContext"]();w["\u0275\u0275advance"](5),w["\u0275\u0275textInterpolate1"]("Populate PR Invoice list by selecting a ",e.costCenterFieldLabel?e.costCenterFieldLabel:"Cost center","!")}}let re=(()=>{class e{constructor(e,t,n,i,o,l,s,c){this.dialogRef=e,this.inData=t,this.utilityService=n,this.p2pBillablePRService=i,this.authService=o,this._p2pGeneral=l,this.spinnerService=s,this.$snackBar=c,this.token=this.authService.getToken(),this.searchSubGroups=window.location.origin+"/api/purchaseRequest/getSubGroupforBilling",this.searchParameter="",this.milestoneName="",this.isPONumberAvailable=!1,this.projectName="",this.itemName="",this.projectId=0,this.itemId=0,this.gantt_id=0,this.isProjectAndItemVisible=!1,this.areExpenseMilestonesLoading=!1,this.PRList=[],this.totalClaimValue="0",this.costCenterPRList=[],this.duration={startDate:"",endDate:""},this.durationRanges={"This Month":[a().startOf("month"),a().endOf("month")],"Last Month":[a().subtract(1,"month").startOf("month"),a().subtract(1,"month").endOf("month")],"Next Month":[a().add(1,"month").startOf("month"),a().add(1,"month").endOf("month")],"Upcoming 3 Months":[a().startOf("month"),a().add(2,"month").endOf("month")],"This Year":[a().startOf("year"),a().endOf("year")],"Previous Year":[a().subtract(1,"year").startOf("year"),a().subtract(1,"year").endOf("year")]},this.isProjectAndItemApisLoading=!1,this._onDestroy=new r.b,this.startIndex=0,this.noOfRecords=10,this.areAnyExpensesSelected=!1,this.isCreatingMilestone=!1,this.costCentreListStartIndex=0,this.costCenterNofRecords=50,this.subjectKeyUp=new r.b,this.subs=new h.a,this.subGroupList=[],this.isLoadingSpinner=!1,this.$onDestroy=new r.b,this.subjectKeyUp.pipe(Object(m.a)(100),Object(d.a)()).subscribe(e=>{this.getCostCenterSearchResult(e)})}ngOnInit(){return Object(i.c)(this,void 0,void 0,(function*(){this.getFieldConfig(),this.isLoadingSpinner=!0,yield this.getBillablePRGroupByCostCenter(),this.modalParams=this.inData.modalParams,this.duration={startDate:a().subtract(2,"year").startOf("year").format(this.modalParams.defaultDateFormat),endDate:a().endOf("year").format(this.modalParams.defaultDateFormat)}}))}getBillablePRGroupByCostCenter(){return Object(i.c)(this,void 0,void 0,(function*(){let e;yield this.p2pBillablePRService.getBillablePRByCostCenter(this.costCentreListStartIndex,this.costCenterNofRecords,this.costCenterSearchParameter).pipe(Object(l.a)(this.$onDestroy)).subscribe(t=>Object(i.c)(this,void 0,void 0,(function*(){"S"==t.messType?(this.isLoadingSpinner=!1,e=t.data,this.costCenterPRList=this.costCenterPRList.concat(e)):(e=[],this.isLoadingSpinner=!1)})))}))}getCostCenterSearchResult(e){return Object(i.c)(this,void 0,void 0,(function*(){let t;this.costCenterPRList=[],this.costCenterSearchParameter=e,yield this.p2pBillablePRService.getBillablePRByCostCenter(this.costCentreListStartIndex,this.costCenterNofRecords,this.costCenterSearchParameter).pipe(Object(l.a)(this.$onDestroy)).subscribe(e=>Object(i.c)(this,void 0,void 0,(function*(){"S"==e.messType?(t=e.data,this.costCenterPRList=t):t=[]})))}))}onCostCenterSearchParameterChange(){this.costCenterPRList=[],this.getBillablePRGroupByCostCenter()}getCostCenterList(){this.costCentreListStartIndex+=50,this.getBillablePRGroupByCostCenter()}clearCostCenterSearch(){this.subjectKeyUp.next("")}getFieldConfig(){return Object(i.c)(this,void 0,void 0,(function*(){yield this.p2pBillablePRService.getExpenseFieldConfig().pipe(Object(l.a)(this._onDestroy)).subscribe(e=>Object(i.c)(this,void 0,void 0,(function*(){this.formFieldConfig=e.data,this.formFieldConfig&&null!=this.formFieldConfig&&s.each(this.formFieldConfig,e=>{"costCenterCode"==e.field_key&&(this.costCenterFieldLabel=null!=e.field_label?e.field_label:"Cost center")})})),e=>{console.log(e)})}))}showBillableClaims(e){this.selectedCCItem=e.name,this.costCentre=JSON.stringify(e),this.onCostCentreChange()}onSearchCostCenter(e){this.subjectKeyUp.next(e.target.value)}onCostCentreChange(){this.costCentre?(this.isProjectAndItemVisible=!1,this.isProjectAndItemApisLoading=!0,this.costCentreTemp=JSON.parse(this.costCentre),this.changeCostCentre()):(this.costCentreTemp=null,this.isProjectAndItemVisible=!1,this.projectName="",this.itemName="",this.projectId=0,this.itemId=0,this.gantt_id=0,this.areAnyExpensesSelected=!1,this.PRList=[])}changeCostCentre(){this.getCostCentreProjectItemName()}getCostCentreProjectItemName(){return Object(i.c)(this,void 0,void 0,(function*(){if(null!=this.costCentreTemp){let e={cost_center:this.costCentreTemp.name};this.isPONumberAvailable=!1,yield this.p2pBillablePRService.getCostCentreProjectItemName(e).pipe(Object(l.a)(this._onDestroy)).subscribe(e=>Object(i.c)(this,void 0,void 0,(function*(){"S"==e.messType&&e.data&&e.data.length>0?(this.projectName=e.data[0].project_name,this.itemName=e.data[0].item_name,this.projectId=e.data[0].project_id,this.itemId=e.data[0].item_id,this.gantt_id=e.data[0].gantt_id,this.getPRListBasedOnCostCenter(!0)):"S"==e.messType&&e.data&&0==e.data.length?(this.projectName="N/A",this.itemName="N/A",this.projectId=0,this.itemId=0,this.gantt_id=0,this.getPRListBasedOnCostCenter(!0)):"E"==e.messType&&(this._p2pGeneral.showMessage(e.messText),this.projectName="",this.itemName="",this.isProjectAndItemVisible=!1,this.isProjectAndItemApisLoading=!1)})),e=>{this.isProjectAndItemVisible=!1,this.isProjectAndItemApisLoading=!1,this.projectName="",this.itemName="",console.log(e),this._p2pGeneral.showMessage(e)})}}))}getPRListBasedOnCostCenter(e){if(null!=this.costCentreTemp){e?this.startIndex=0:this.areExpenseMilestonesLoading=!0;let t={cost_center:this.costCentreTemp.name,start_index:this.startIndex,no_of_records:this.noOfRecords,search_parameter:this.searchParameter,filter_params:{subgroup_list:this.subGroupList,t_approved_on_duration:{start_date:a(this.duration.startDate,this.modalParams.defaultDateFormat).format("YYYY-MM-DD"),end_date:a(this.duration.endDate,this.modalParams.defaultDateFormat).format("YYYY-MM-DD")}}};this.p2pBillablePRService.getPRListBasedOnCostCenter(t).pipe(Object(l.a)(this._onDestroy)).subscribe(t=>Object(i.c)(this,void 0,void 0,(function*(){if("S"==t.messType&&t.data&&t.data.length>0){this.PRList=e?t.data:this.PRList.concat(t.data),this.startIndex=this.PRList.length;for(let e of this.PRList)e.billed_on=a(e.billed_on).format(this.modalParams.defaultDateFormat),e.expense_request_amount=this.resolveAmountAndCurrency(e.amount_requested),e.expense_approved_amount=this.resolveAmountAndValue(e.total_approved_amount),e.expense_claim_amount=this.resolveAmountAndCurrency(e.total_approved_amount),e.isChecked||(e.isChecked=!1)}else"E"==t.messType?this._p2pGeneral.showMessage(t.messText):"S"==t.messType&&t.data&&0==t.data.length&&e&&(this.PRList=[]);this.isProjectAndItemVisible=!0,this.areExpenseMilestonesLoading=!1,this.isProjectAndItemApisLoading=!1})),e=>{this.isProjectAndItemVisible=!1,this.areExpenseMilestonesLoading=!1,this.isProjectAndItemApisLoading=!1,this.PRList=[],console.log(e),this._p2pGeneral.showMessage(e)})}}resolveAmountAndCurrency(e){let t=s.where(e,{currency_code:this.modalParams.defaultCurrency}),n=t.length>0?t[0].value:0;return n=this.utilityService.getAmountCurrencyFormat(n,this.modalParams.defaultCurrency),this.modalParams.defaultCurrency+" "+n}resolveAmountAndValue(e){let t=s.where(e,{currency_code:this.modalParams.defaultCurrency});return t.length>0?t[0].value:0}selectSubGroup(e){this.subGroupList=s.pluck(e,"name"),this.areExpenseMilestonesLoading=!0,this.getPRListBasedOnCostCenter(!0)}createBillablePR(){if(""==this.milestoneName)this.utilityService.showToastMessage("Milestone Name is mandatory!");else if(null==this.costCentreTemp||""==this.costCentreTemp.name)this.utilityService.showToastMessage("Cost Centre is mandatory!");else if(this.areAnyExpensesSelected){if(null!=this.costCentreTemp){let e={user_oid:this.authService.getProfile().profile.oid,params:{project_id:this.projectId,project_item_id:this.itemId,milestone_name:this.milestoneName,attachment_id:this.gantt_id,cost_center:this.costCentreTemp.name,total_selected_claims_approved_value:this.resolveClaimApprovedValue(),expense_list:this.resolveExpenseList()}};this.utilityService.openConfirmationSweetAlertWithCustom("Create New PR Milestone ?","").then(t=>{1==t?(this.isCreatingMilestone=!0,this.p2pBillablePRService.billableMilestoneCreation(e).pipe(Object(l.a)(this._onDestroy)).subscribe(e=>Object(i.c)(this,void 0,void 0,(function*(){"S"==e.messType?(p.a.fire({title:"Milestone Created Successfully !",icon:"success",showConfirmButton:!0}),this.clearMilestoneName(),this.isCreatingMilestone=!1,this.closeModal()):"E"==e.messType&&(p.a.fire({title:"Milestone Creation Failed !",text:e.errMessage,icon:"info",showConfirmButton:!0}),this.isCreatingMilestone=!1)})),e=>{console.error(e),p.a.fire({title:"Milestone Creation Failed !",icon:"info",showConfirmButton:!0}),this.isCreatingMilestone=!1,console.log(e)})):this.isCreatingMilestone=!1})}}else this.utilityService.showToastMessage("At least one expense must be selected!")}resolveClaimApprovedValue(){let e=[];for(let t of this.PRList)if(t.isChecked)for(let n of t.total_approved_amount){let t=s.where(e,{currency_code:n.currency_code});0==t.length?e.push({currency_code:n.currency_code,value:n.value}):t[0].value+=n.value}return e}resolveExpenseList(){let e=[];for(let t of this.PRList)t.isChecked&&e.push({p2p_header_id:t.p2p_header_id,p2p_item_id:t.p2p_item_id});return e}callSearchApi(){this.getPRListBasedOnCostCenter(!0)}clearSearch(){this.searchParameter="",this.callSearchApi()}onSearchParameterChange(){""==this.searchParameter&&this.callSearchApi()}clearMilestoneName(){this.milestoneName=""}selectExpenseListItem(e){e.isChecked=!e.isChecked,this.areAnyExpensesSelected=!1,this.totalClaimValue=this.utilityService.getAmountCurrencyFormat(0,this.modalParams.defaultCurrency);let t=0;for(let n of this.PRList)n.isChecked&&(t+=n.expense_approved_amount,this.areAnyExpensesSelected=!0);this.totalClaimValue=this.modalParams.defaultCurrency+" "+this.utilityService.getAmountCurrencyFormat(t,this.modalParams.defaultCurrency)}selectDuration(e){if(e.startDate){let t=a(e.startDate).startOf("day"),n=a(e.endDate).endOf("day");this.duration={startDate:a(t).format("DD MMM YYYY"),endDate:a(n).format("DD MMM YYYY")}}this.areExpenseMilestonesLoading=!0,this.getPRListBasedOnCostCenter(!0)}closeModal(){this.dialogRef.close({event:"Milestone Created"})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}}return e.\u0275fac=function(t){return new(t||e)(w["\u0275\u0275directiveInject"](o.h),w["\u0275\u0275directiveInject"](o.a),w["\u0275\u0275directiveInject"](O.a),w["\u0275\u0275directiveInject"](L.a),w["\u0275\u0275directiveInject"](E.a),w["\u0275\u0275directiveInject"](A.a),w["\u0275\u0275directiveInject"](I.c),w["\u0275\u0275directiveInject"](j.a))},e.\u0275cmp=w["\u0275\u0275defineComponent"]({type:e,selectors:[["app-create-billable-prs-popup"]],decls:45,vars:20,consts:[[1,"create-billable-prs-popup-styles","row"],[1,"col-3","p-0","left-half"],[1,"col-12","p-0","pl-3","pr-3","pt-3",2,"height","100%"],[2,"height","75%"],[1,"create-milestone","col-12","p-0","m-0"],[1,"milestone-header","col-12","p-0","pt-4","m-0","mb-1"],[1,"mandatory-red"],["appearance","outline",2,"width","100%","height","45px"],["matInput","","placeholder","Milestone Name","autocomplete","off",2,"padding-bottom","1px !important",3,"ngModel","ngModelChange"],["matSuffix",""],["mat-button","","matSuffix","","mat-icon-button","","style","height: 30px; width: 30px; line-height: 1",3,"click",4,"ngIf"],[1,"milestone-header","col-12","p-0","pt-3","m-0","mb-1"],["appearance","outline",1,"ml-auto","mr-auto",2,"width","100%"],["matPrefix",""],[2,"font-size","20px !important","color","black !important"],["matInput","","placeholder","Search","autocomplete","off",2,"padding-bottom","1px !important",3,"ngModel","ngModelChange","keyup.enter","keyup"],[4,"ngIf"],["infinite-scroll","","cdkScrollable","",3,"class","infiniteScrollDistance","scrollWindow","stopPropagation","scrolled",4,"ngIf"],[2,"height","25%"],["class","mt-4 ml-auto mr-auto","diameter","25","matTooltip","Loading ...",4,"ngIf"],["class","milestone-header col-12 p-0 pt-3 m-0 mb-1",4,"ngIf"],["class","milestone-description col-12 p-0 m-0",3,"matTooltip",4,"ngIf"],[1,"col-9","p-0"],[1,"col-12","p-0","row","pl-3","pt-2","pb-3"],["appearance","outline","style","width: 35%; height: 45px;","class","mr-4",4,"ngIf"],["style","width: 26%; height: 45px;","class","mr-4","label","SubGroup",3,"token","optionLabel","API_URL","lazyLoadingCount","selectedValues",4,"ngIf"],["matTooltip","Duration","type","text","style","width: 26%; height: 41px;","class","mr-3 dp-class","ngxDaterangepickerMd","","placeholder","Duration",3,"showCustomRangeLabel","locale","alwaysShowCalendars","showCancel","ranges","linkedCalendars","ngModel","ngModelChange","change",4,"ngIf"],["mat-icon-button","","matTooltip","Close",1,"align-right","mr-2"],[1,"close-button",3,"click"],["class","col-12 p-0 milestone-list-loading",4,"ngIf"],["infinite-scroll","","cdkScrollable","",3,"class","infiniteScrollDistance","scrollWindow","scrolled",4,"ngIf"],["class","mt-3 ml-auto mr-auto","diameter","25","matTooltip","Loading ...",4,"ngIf"],["class","col-12 row p-0 pl-3 pr-3 mt-3",4,"ngIf"],["class","col-12 p-0 milestone-list-loading row","style","text-align: center;",4,"ngIf"],["mat-button","","matSuffix","","mat-icon-button","",2,"height","30px","width","30px","line-height","1",3,"click"],["matTooltip","Clear",2,"font-size","20px !important","color","#66615b !important"],["matTooltip","Clear Search",2,"font-size","20px !important","color","#66615b !important"],["diameter","30",1,"spinner-align",2,"margin-left","auto","margin-right","auto",3,"matTooltip"],["infinite-scroll","","cdkScrollable","",3,"infiniteScrollDistance","scrollWindow","stopPropagation","scrolled"],["class","cost-centre-item",3,"ngClass",4,"ngFor","ngForOf"],[1,"cost-centre-item",3,"ngClass"],[1,"row","pt-2","pb-2"],[1,"col","p-0","pl-1",2,"white-space","nowrap","overflow","hidden","text-overflow","ellipsis","width","100%",3,"matTooltip","click"],[2,"color","#45546e"],[1,"pl-1",2,"color","#cf0001"],["style","margin-top: 0%; margin-bottom: 0%;",4,"ngIf"],[2,"margin-top","0%","margin-bottom","0%"],["diameter","25","matTooltip","Loading ...",1,"mt-4","ml-auto","mr-auto"],[1,"milestone-description","col-12","p-0","m-0",3,"matTooltip"],["appearance","outline",1,"mr-4",2,"width","35%","height","45px"],["matInput","","placeholder","Search and press Enter","autocomplete","off",2,"padding-bottom","1px !important",3,"ngModel","ngModelChange","keyup.enter"],["label","SubGroup",1,"mr-4",2,"width","26%","height","45px",3,"token","optionLabel","API_URL","lazyLoadingCount","selectedValues"],["matTooltip","Duration","type","text","ngxDaterangepickerMd","","placeholder","Duration",1,"mr-3","dp-class",2,"width","26%","height","41px",3,"showCustomRangeLabel","locale","alwaysShowCalendars","showCancel","ranges","linkedCalendars","ngModel","ngModelChange","change"],[1,"col-12","p-0","milestone-list-loading"],["diameter","25","matTooltip","Loading ...",1,"mt-auto","mb-auto","ml-auto","mr-auto"],["infinite-scroll","","cdkScrollable","",3,"infiniteScrollDistance","scrollWindow","scrolled"],["class","col-12 p-0 mb-2 milestone-list-item",3,"click",4,"ngFor","ngForOf"],[1,"col-12","p-0","mb-2","milestone-list-item",3,"click"],[1,"col-12","p-2","row"],["disabled","",1,"mt-1","mr-3",2,"cursor","pointer",3,"checked"],[1,"milestone-category","col-3","p-0","m-0",3,"matTooltip"],[1,"milestone-department","p-0","m-0","col-1"],[1,"milestone-department","col-7","p-0","m-0",3,"matTooltip"],[1,"col-12","p-2","row","milestone-list-bottom"],[1,"col-2-5","milestone-item"],[1,"milestone-item-header","col-12","p-0","m-0"],[1,"milestone-item-description","col-12","p-0","m-0",3,"matTooltip"],[1,"col-2-5","milestone-item","ml-2"],[1,"col-12","p-0","row",2,"align-items","center",3,"matTooltip"],["content-type","template",1,"mr-2",3,"id","imgWidth","imgHeight"],[1,"milestone-item-description","m-0","col-9","p-0"],[1,"col-2-5","ml-2"],["diameter","25","matTooltip","Loading ...",1,"mt-3","ml-auto","mr-auto"],[1,"col-12","row","p-0","pl-3","pr-3","mt-3"],["class","align-left col-8 p-0",4,"ngIf"],["class","mr-4 mt-2 align-right","diameter","25","matTooltip","Creating Milestone ...",4,"ngIf"],["mat-flat-button","","class","pl-0 pr-0 mr-2 red-btn align-right",3,"disabled","click",4,"ngIf"],[1,"align-left","col-8","p-0"],[1,"total-milestone-header","col-12","p-0","m-0"],[1,"total-milestone-description","col-12","p-0","m-0"],["diameter","25","matTooltip","Creating Milestone ...",1,"mr-4","mt-2","align-right"],["mat-flat-button","",1,"pl-0","pr-0","mr-2","red-btn","align-right",3,"disabled","click"],["matListIcon","",1,"save-btn-icon"],[1,"col-12","p-0","milestone-list-loading","row",2,"text-align","center"],["src","https://assets.kebs.app/images/no_claims_here.png"],[1,"milestone-header-bigger","col-12","p-0","m-0"]],template:function(e,t){1&e&&(w["\u0275\u0275elementStart"](0,"div",0),w["\u0275\u0275elementStart"](1,"div",1),w["\u0275\u0275elementStart"](2,"div",2),w["\u0275\u0275elementStart"](3,"div",3),w["\u0275\u0275elementStart"](4,"p",4),w["\u0275\u0275text"](5,"Create Milestone"),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementStart"](6,"p",5),w["\u0275\u0275text"](7,"Milestone Name"),w["\u0275\u0275elementStart"](8,"span",6),w["\u0275\u0275text"](9,"*"),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementStart"](10,"mat-form-field",7),w["\u0275\u0275elementStart"](11,"input",8),w["\u0275\u0275listener"]("ngModelChange",(function(e){return t.milestoneName=e})),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementStart"](12,"mat-icon",9),w["\u0275\u0275template"](13,T,3,0,"button",10),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementStart"](14,"p",11),w["\u0275\u0275text"](15),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementStart"](16,"div"),w["\u0275\u0275elementStart"](17,"mat-form-field",12),w["\u0275\u0275elementStart"](18,"span",13),w["\u0275\u0275elementStart"](19,"mat-icon",14),w["\u0275\u0275text"](20,"search"),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementStart"](21,"input",15),w["\u0275\u0275listener"]("ngModelChange",(function(e){return t.costCenterSearchParameter=e}))("keyup.enter",(function(){return t.callSearchApi()}))("keyup",(function(e){return t.onSearchCostCenter(e)})),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementStart"](22,"mat-icon",9),w["\u0275\u0275template"](23,V,3,0,"button",10),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"](),w["\u0275\u0275template"](24,D,2,1,"div",16),w["\u0275\u0275template"](25,F,4,7,"div",17),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementStart"](26,"div",18),w["\u0275\u0275template"](27,G,1,0,"mat-spinner",19),w["\u0275\u0275template"](28,Y,2,0,"p",20),w["\u0275\u0275template"](29,z,2,2,"p",21),w["\u0275\u0275template"](30,q,2,0,"p",20),w["\u0275\u0275template"](31,U,2,2,"p",21),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementStart"](32,"div",22),w["\u0275\u0275elementStart"](33,"div",23),w["\u0275\u0275template"](34,K,7,2,"mat-form-field",24),w["\u0275\u0275template"](35,$,1,5,"kebs-mul-sel-inf-search",25),w["\u0275\u0275template"](36,H,1,8,"input",26),w["\u0275\u0275elementStart"](37,"button",27),w["\u0275\u0275elementStart"](38,"mat-icon",28),w["\u0275\u0275listener"]("click",(function(){return t.closeModal()})),w["\u0275\u0275text"](39,"close"),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"](),w["\u0275\u0275template"](40,Q,2,0,"div",29),w["\u0275\u0275template"](41,ee,2,6,"div",30),w["\u0275\u0275template"](42,te,1,0,"mat-spinner",31),w["\u0275\u0275template"](43,ae,4,3,"div",32),w["\u0275\u0275template"](44,le,6,1,"div",33),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"]()),2&e&&(w["\u0275\u0275advance"](11),w["\u0275\u0275property"]("ngModel",t.milestoneName),w["\u0275\u0275advance"](2),w["\u0275\u0275property"]("ngIf",t.milestoneName&&""!=t.milestoneName),w["\u0275\u0275advance"](2),w["\u0275\u0275textInterpolate"](t.costCenterFieldLabel?t.costCenterFieldLabel:"Cost center"),w["\u0275\u0275advance"](6),w["\u0275\u0275property"]("ngModel",t.costCenterSearchParameter),w["\u0275\u0275advance"](2),w["\u0275\u0275property"]("ngIf",t.costCenterSearchParameter&&""!=t.costCenterSearchParameter),w["\u0275\u0275advance"](1),w["\u0275\u0275property"]("ngIf",t.isLoadingSpinner),w["\u0275\u0275advance"](1),w["\u0275\u0275property"]("ngIf",t.costCenterPRList.length>0&&!t.isLoadingSpinner),w["\u0275\u0275advance"](2),w["\u0275\u0275property"]("ngIf",t.isProjectAndItemApisLoading&&!t.isProjectAndItemVisible),w["\u0275\u0275advance"](1),w["\u0275\u0275property"]("ngIf",!t.isProjectAndItemApisLoading&&t.isProjectAndItemVisible),w["\u0275\u0275advance"](1),w["\u0275\u0275property"]("ngIf",!t.isProjectAndItemApisLoading&&t.isProjectAndItemVisible),w["\u0275\u0275advance"](1),w["\u0275\u0275property"]("ngIf",!t.isProjectAndItemApisLoading&&t.isProjectAndItemVisible),w["\u0275\u0275advance"](1),w["\u0275\u0275property"]("ngIf",!t.isProjectAndItemApisLoading&&t.isProjectAndItemVisible),w["\u0275\u0275advance"](3),w["\u0275\u0275property"]("ngIf",!t.isProjectAndItemApisLoading&&t.isProjectAndItemVisible),w["\u0275\u0275advance"](1),w["\u0275\u0275property"]("ngIf",!t.isProjectAndItemApisLoading&&t.isProjectAndItemVisible),w["\u0275\u0275advance"](1),w["\u0275\u0275property"]("ngIf",!t.isProjectAndItemApisLoading&&t.isProjectAndItemVisible),w["\u0275\u0275advance"](4),w["\u0275\u0275property"]("ngIf",t.isProjectAndItemApisLoading&&!t.isProjectAndItemVisible),w["\u0275\u0275advance"](1),w["\u0275\u0275property"]("ngIf",!t.isProjectAndItemApisLoading&&t.isProjectAndItemVisible&&t.PRList.length>0),w["\u0275\u0275advance"](1),w["\u0275\u0275property"]("ngIf",1==t.areExpenseMilestonesLoading),w["\u0275\u0275advance"](1),w["\u0275\u0275property"]("ngIf",!t.isProjectAndItemApisLoading&&t.isProjectAndItemVisible&&t.PRList.length>0),w["\u0275\u0275advance"](1),w["\u0275\u0275property"]("ngIf",!t.isProjectAndItemApisLoading&&!t.isProjectAndItemVisible))},directives:[b.c,x.b,y.e,y.v,y.y,f.a,b.i,u.NgIf,b.h,g.a,C.a,_.c,S.a,k.b,u.NgForOf,u.NgClass,M.a,v.b,P.a,R.a],styles:[".create-billable-prs-popup-styles[_ngcontent-%COMP%]{min-height:100%}.create-billable-prs-popup-styles[_ngcontent-%COMP%]   .left-half[_ngcontent-%COMP%]{background-color:#f6f6f0}.create-billable-prs-popup-styles[_ngcontent-%COMP%]   .create-milestone[_ngcontent-%COMP%]{font-weight:500;font-size:15px}.create-billable-prs-popup-styles[_ngcontent-%COMP%]   .milestone-header[_ngcontent-%COMP%]{font-weight:450;font-size:13px;color:#a3a3a3}.create-billable-prs-popup-styles[_ngcontent-%COMP%]   .milestone-header-bigger[_ngcontent-%COMP%]{font-weight:450;font-size:14px;color:#a3a3a3}.create-billable-prs-popup-styles[_ngcontent-%COMP%]   .milestone-category[_ngcontent-%COMP%], .create-billable-prs-popup-styles[_ngcontent-%COMP%]   .milestone-description[_ngcontent-%COMP%]{font-weight:500;font-size:13px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.create-billable-prs-popup-styles[_ngcontent-%COMP%]   .milestone-category[_ngcontent-%COMP%]{color:#f15b64;margin-top:.15rem!important}.create-billable-prs-popup-styles[_ngcontent-%COMP%]     .mat-checkbox-checked.mat-accent .mat-checkbox-background:disabled, .create-billable-prs-popup-styles[_ngcontent-%COMP%]     .mat-checkbox-disabled.mat-checkbox-checked .mat-checkbox-background, .create-billable-prs-popup-styles[_ngcontent-%COMP%]     .mat-checkbox .mat-ripple-element{background:#f15b64!important}.create-billable-prs-popup-styles[_ngcontent-%COMP%]   .milestone-list-item[_ngcontent-%COMP%]{border:1px solid #d6cece;cursor:pointer}.create-billable-prs-popup-styles[_ngcontent-%COMP%]   .milestone-item[_ngcontent-%COMP%]{border-right:1px solid #d6cece}.create-billable-prs-popup-styles[_ngcontent-%COMP%]   .milestone-item-header[_ngcontent-%COMP%]{font-weight:450;font-size:12px;color:#a3a3a3}.create-billable-prs-popup-styles[_ngcontent-%COMP%]   .milestone-item-description[_ngcontent-%COMP%]{font-weight:500;font-size:12px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.create-billable-prs-popup-styles[_ngcontent-%COMP%]   .milestone-department[_ngcontent-%COMP%]{font-weight:500;font-size:13px;margin-top:.15rem!important;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.create-billable-prs-popup-styles[_ngcontent-%COMP%]   .milestone-list[_ngcontent-%COMP%]{height:67vh!important;overflow-y:scroll}.create-billable-prs-popup-styles[_ngcontent-%COMP%]   .milestone-list-loading[_ngcontent-%COMP%]{height:67vh!important;display:flex;align-items:center;justify-content:center}.create-billable-prs-popup-styles[_ngcontent-%COMP%]   .milestone-list-loading-more[_ngcontent-%COMP%]{height:60vh!important;overflow-y:scroll}.create-billable-prs-popup-styles[_ngcontent-%COMP%]   .claim-list-with-project[_ngcontent-%COMP%]{height:35vh!important;overflow-y:scroll}.create-billable-prs-popup-styles[_ngcontent-%COMP%]   .claim-list[_ngcontent-%COMP%]{height:50vh!important;overflow-y:scroll}.create-billable-prs-popup-styles[_ngcontent-%COMP%]   .milestone-list-bottom[_ngcontent-%COMP%]{background-color:#f6f6f0}.create-billable-prs-popup-styles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{color:#868683;font-size:20px}.create-billable-prs-popup-styles[_ngcontent-%COMP%]   .align-right[_ngcontent-%COMP%]{margin-right:0;margin-left:auto}.create-billable-prs-popup-styles[_ngcontent-%COMP%]   .save-btn-icon[_ngcontent-%COMP%]{height:24px;font-size:20px;width:30px;margin-left:5px}.create-billable-prs-popup-styles[_ngcontent-%COMP%]   .mandatory-red[_ngcontent-%COMP%]{margin-left:1%;color:#cf0001}.create-billable-prs-popup-styles[_ngcontent-%COMP%]   .custom-ts-select-option[_ngcontent-%COMP%]{font-size:13px;text-align:center;color:#1a1a1a}.create-billable-prs-popup-styles[_ngcontent-%COMP%]   .col-2-5[_ngcontent-%COMP%]{flex:0 0 19%;max-width:19%}.create-billable-prs-popup-styles[_ngcontent-%COMP%]   .red-btn[_ngcontent-%COMP%]{width:100%;height:45px;max-width:100px;background:#f15b64;font-size:13px;color:#fff;font-weight:500}.create-billable-prs-popup-styles[_ngcontent-%COMP%]   .red-btn[_ngcontent-%COMP%]:disabled{background:grey}.create-billable-prs-popup-styles[_ngcontent-%COMP%]   .total-milestone-header[_ngcontent-%COMP%]{font-weight:450;font-size:14px;color:#a3a3a3}.create-billable-prs-popup-styles[_ngcontent-%COMP%]   .total-milestone-description[_ngcontent-%COMP%]{font-weight:450;font-size:14px}.create-billable-prs-popup-styles[_ngcontent-%COMP%]   .dp-class[_ngcontent-%COMP%]{background:0 0;border:1px solid #d3d3d3;border-radius:4px;font-size:12px;font-weight:500;height:40px;margin-top:3px;cursor:pointer;text-align:center}.create-billable-prs-popup-styles[_ngcontent-%COMP%]     .md-drppicker{top:17px!important;left:270px!important}.create-billable-prs-popup-styles[_ngcontent-%COMP%]   .app-input-search-class[_ngcontent-%COMP%]     .mat-form-field-flex{padding-bottom:3px!important}.create-billable-prs-popup-styles[_ngcontent-%COMP%]   .app-input-search-class[_ngcontent-%COMP%]     input.mat-input-element{font-size:14px!important;padding-top:4px!important}.create-billable-prs-popup-styles[_ngcontent-%COMP%]   .app-input-search-class[_ngcontent-%COMP%]     .mat-form-field-infix{font-size:14px!important}.create-billable-prs-popup-styles[_ngcontent-%COMP%]   .cost-centre-item[_ngcontent-%COMP%], .create-billable-prs-popup-styles[_ngcontent-%COMP%]   .cost-centre-item[_ngcontent-%COMP%]:hover{cursor:pointer;border-radius:6px}.create-billable-prs-popup-styles[_ngcontent-%COMP%]   .pr-cc-is-active[_ngcontent-%COMP%]{background-color:#faeaea;border-left:4px solid red}"]}),e})()}}]);