(window.webpackJsonp=window.webpackJsonp||[]).push([[984,418,535,631,634,858],{DlyV:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),i("QtPd")},GMZp:function(e,t,i){"use strict";t.isObject=function(e){return null!==e&&"object"==typeof e}},LBXl:function(e,t,i){"use strict";t.UnsubscriptionError=function(){function e(e){return Error.call(this),this.message=e?e.length+" errors occurred during unsubscription:\n"+e.map((function(e,t){return t+1+") "+e.toString()})).join("\n  "):"",this.name="UnsubscriptionError",this.errors=e,this}return e.prototype=Object.create(Error.prototype),e}()},"LOr+":function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var s=i("qCKp"),l=i("kU1M");t.debounceTime=function(e,t){return void 0===t&&(t=s.asyncScheduler),l.debounceTime(e,t)(this)}},NJ67:function(e,t,i){"use strict";i.d(t,"a",(function(){return s}));class s{constructor(){this.disabled=!1}onChange(e){}onTouched(e){}writeValue(e){this.value=e}registerOnChange(e){this.onChange=e}registerOnTouched(e){this.onTouched=e}setDisabledState(e){this.disabled=e}}},QtPd:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var s=i("qCKp"),l=i("LOr+");s.Observable.prototype.debounceTime=l.debounceTime},"TmG/":function(e,t,i){"use strict";i.d(t,"a",(function(){return b}));var s=i("fXoL"),l=i("3Pt+"),a=i("jtHE"),r=i("XNiG"),n=i("NJ67"),o=i("1G5W"),d=i("kmnG"),u=i("ofXK"),f=i("d3UM"),c=i("FKr1"),h=i("WJ5W"),p=i("Qu3c");function m(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"mat-label"),s["\u0275\u0275text"](1),s["\u0275\u0275elementEnd"]()),2&e){const e=s["\u0275\u0275nextContext"]();s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate"](e.placeholder)}}function g(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"mat-option",7),s["\u0275\u0275text"](1),s["\u0275\u0275elementEnd"]()),2&e){const e=s["\u0275\u0275nextContext"]();s["\u0275\u0275property"]("value",null),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate"](e.disableNone?"Select One":"None")}}function v(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"mat-option",8),s["\u0275\u0275listener"]("click",(function(){s["\u0275\u0275restoreView"](e);const i=t.$implicit;return s["\u0275\u0275nextContext"]().emitChanges(i)})),s["\u0275\u0275text"](1),s["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;s["\u0275\u0275property"]("value",e.id||e._id)("matTooltip",e.name),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let b=(()=>{class e extends n.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new l.j,this.fieldFilterCtrl=new l.j,this.list=[],this.required=!1,this.disableNone=!1,this.valueChange=new s.EventEmitter,this.disabled=!1,this.hasNoneOption=!0,this.filteredList=new a.a,this.change=new s.EventEmitter,this.hideMatLabel=!1,this._onDestroy=new r.b,this.emitChanges=e=>{this.change.emit(e)}}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(o.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(o.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275directiveInject"](s.Renderer2))},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search"]],inputs:{list:"list",placeholder:"placeholder",required:"required",disableNone:"disableNone",disabled:"disabled",hasNoneOption:"hasNoneOption",hideMatLabel:"hideMatLabel"},outputs:{valueChange:"valueChange",change:"change"},features:[s["\u0275\u0275ProvidersFeature"]([{provide:l.t,useExisting:Object(s.forwardRef)(()=>e),multi:!0}]),s["\u0275\u0275InheritDefinitionFeature"],s["\u0275\u0275NgOnChangesFeature"]],decls:9,vars:11,consts:[["appearance","outline"],[4,"ngIf"],[3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value",4,"ngIf"],[3,"value","matTooltip","click",4,"ngFor","ngForOf"],[3,"value"],[3,"value","matTooltip","click"]],template:function(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"mat-form-field",0),s["\u0275\u0275template"](1,m,2,1,"mat-label",1),s["\u0275\u0275elementStart"](2,"mat-select",2,3),s["\u0275\u0275elementStart"](4,"mat-option"),s["\u0275\u0275element"](5,"ngx-mat-select-search",4),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](6,g,2,2,"mat-option",5),s["\u0275\u0275template"](7,v,2,3,"mat-option",6),s["\u0275\u0275pipe"](8,"async"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e&&(s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",!t.hideMatLabel),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",t.hasNoneOption),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngForOf",s["\u0275\u0275pipeBind1"](8,9,t.filteredList)))},directives:[d.c,u.NgIf,f.c,l.v,l.k,l.F,c.p,h.a,u.NgForOf,d.g,p.a],pipes:[u.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%}"]}),e})()},fbp6:function(e,t,i){"use strict";i.d(t,"a",(function(){return r}));var s=i("fXoL"),l=i("tk/3"),a=i("dNgK");let r=(()=>{class e{constructor(e,t){this.$http=e,this._snack=t,this.showSnack=e=>{this._snack.open(e,"Dismiss",{duration:2e3})}}getAppsData(e){return this.$http.get("/api/appBuilder/apps",{params:{searchText:e}})}getCreationIcons(){return this.$http.get("/api/appBuilder/creationIcons")}getCreationKEBSPlugins(){return this.$http.get("/api/appBuilder/creationKEBSPlugins")}getAllForms(){return this.$http.get("/api/appBuilder/formBuilder/formsData")}getFormByApp(e){return this.$http.get("/api/appBuilder/formBuilder/formsByApp/"+e)}deleteForm(e){return console.log("delete form"+e),this.$http.delete("/api/appBuilder/formBuilder/form/"+e)}deleteAppAndForms(e){return this.$http.delete("/api/appBuilder/formBuilder/formsByApp/"+e)}createApp(e){return this.$http.post("/api/appBuilder/createApp",e)}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275inject"](l.c),s["\u0275\u0275inject"](a.a))},e.\u0275prov=s["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},l9wN:function(e,t,i){"use strict";i.d(t,"a",(function(){return d}));var s=i("2Vo4"),l=i("XNiG"),a=i("xG9w"),r=i("fXoL"),n=i("tk/3"),o=i("tyNb");let d=(()=>{class e{constructor(e,t){this.$http=e,this.route=t,this.layout=new s.a(""),this.fields=new s.a([]),this.msFields=new s.a([]),this.msFieldsArray=new Array([]),this.fieldObject=new l.b,this.fieldsArray=new Array,this.multiStepInit=new s.a({}),this.currentMSIndex=0,this.mode="",this.fieldEdit=!1,this.msg=!0,this.isFeedbackForm=!1,this.fieldsList=[],this.initArray()}initArray(){for(let e=0;e<5;e++)this.msFieldsArray[e]=new Array}setFormId(e){this.formId=e,console.log(e)}setLayout(e){this.layoutCopy=e,this.layout.next(e)}getLayout(){return this.layout.asObservable()}clearLayout(){this.layout.next("")}setMultiStepData(e){this.multiStepInit.next(e)}clearMultiStepData(){this.multiStepInit.next("")}getMultiStepData(){return this.multiStepInit.asObservable()}addField(e){"multiStepLayout"!=this.layoutCopy?(0==this.fieldEdit?this.fieldsArray.push(e):this.fieldsArray[this.fieldId]=e,this.fieldEdit=!1,this.fields.next(this.fieldsArray)):this.addMSField(e,this.currentMSIndex)}deleteField(e){if("multiStepLayout"==this.layoutCopy)return this.msFieldsArray[this.currentMSIndex].splice(e,1),void this.fields.next(this.fieldsArray);this.fieldsArray.splice(e,1),this.fields.next(this.fieldsArray)}callEditField(e,t){this.fieldObject.next(e),this.fieldId=t,this.fieldEdit=!0}getCallEditField(){return this.fieldObject.asObservable()}editCheck(e){this.fieldEdit=e}swapFields(e,t){let i;i=this.fieldsArray[e],this.fieldsArray[e]=this.fieldsArray[t],this.fieldsArray[t]=i,this.fields.next(this.fieldsArray)}swapMSFields(e,t,i){let s;s=this.msFieldsArray[e][t],this.msFieldsArray[e][t]=this.msFieldsArray[e][i],this.msFieldsArray[e][i]=s,this.msFields.next(this.msFieldsArray)}getFields(){return this.fields.asObservable()}addMSField(e,t){this.fieldEdit?this.msFieldsArray[t][this.fieldId]=e:this.msFieldsArray[t].push(e),this.fieldEdit=!1,this.msFields.next(this.msFieldsArray)}validateMSArray(e){for(let t=e;t<5;t++)this.msFieldsArray[t]=new Array;this.msFields.next(this.msFieldsArray)}patchMSField(e,t){this.msFieldsArray[t].push(e),this.msFields.next(this.msFieldsArray)}getMSFields(){return this.msFields.asObservable()}clearData(){this.initArray(),this.clearMultiStepData(),this.clearLayout(),this.currentMSIndex=0,this.fieldsArray=[],this.fields.next(this.fieldsArray),this.msFields.next([]),this.layoutCopy="",this.fieldEdit=!1,this.msg=!0}setAppId(e){this.appId=e}getMasterData(){return this.$http.get("/api/appBuilder/fields")}getMasterForm(e){return this.$http.post("/api/appBuilder/formBuilder/getForm",e)}updateMasterForm(e){return this.$http.put("/api/appBuilder/formBuilder/updateForm",e)}createApp(e){return this.$http.post("/api/appBuilder/app",{appName:e})}createForm(e){return this.$http.post("/api/appBuilder/formBuilder/createForm",e)}createFormSubmission(e){return this.$http.post("/api/appBuilder/formSubmission/createSubmission",e)}updateFormSubmission(e){return this.$http.put("/api/appBuilder/formSubmission/updateSubmission",e)}updateAutogenValue(e,t){return this.$http.put("/api/appBuilder/fields/autogen/updateAutogenValue",{formId:e,fields:t})}createAdminTagsData(e){return this.$http.post("/api/appBuilder/fields/tags/createAdminTagsData",{tags:e})}checkForUniqueValues(e,t,i,s,l=null){return this.$http.post("/api/appBuilder/fields/checkForUniqueValues",{appId:e,formId:t,fieldId:i,fieldValue:s,recordId:l})}checkIfFeedbackForm(e){return this.$http.post("/api/appBuilder/formBuilder/checkIfFeedbackForm",{formId:e})}setIsFeedBackFormValue(e=!1){this.isFeedbackForm=e}checkIfRecurrenceConfigActive(e){return this.$http.post("/api/appBuilder/formBuilder/checkIfRecurrenceConfigActive",{formId:e})}createFormRecurrenceSubmission(e,t,i){return this.$http.post("/api/appBuilder/formSubmission/createRecurrenceSubmission",{appId:e,formId:t,submissionId:i})}getFieldsByApp(){return this.$http.post("/api/appBuilder/getFieldsByApp",{appId:this.appId})}setFieldsList(e){this.fieldsList=e}callExternalApi(e){let t=e.reqMethod,i=new URL(e.serverUrl);return this.$http[t](i.pathname,e.apiParams||{})}getObjectEntriesForFieldAuth(e,t){return this.$http.post("/api/appBuilder/lcdp/getObjectEntriesForFieldAuth",{applicationId:e,fieldId:t})}checkIfEmpty(e){return"number"!=typeof e&&a.isEmpty(e)}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275inject"](n.c),r["\u0275\u0275inject"](o.a))},e.\u0275prov=r["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},mGD6:function(e,t,i){"use strict";i.r(t),i.d(t,"CustomFormModalComponent",(function(){return ae}));var s=i("mrSG"),l=i("1G5W"),a=i("XNiG"),r=i("3Pt+"),n=i("0IaG"),o=i("xG9w"),d=i("zB/H"),u=(i("DlyV"),i("Ljk9")),f=i("8hBH"),c=i("7pIB"),h=i("ofXK"),p=i("f0Cb"),m=i("xHqg"),g=i("bTqV"),v=i("NFeN"),b=i("Xa2L"),F=i("kmnG"),y=(i("bSwM"),i("qFsG")),C=i("1jcm"),S=(i("Xi0T"),i("iadO")),_=(i("ptd3"),i("1yaQ"),i("wd/R")),w=i("fXoL"),x=i("l9wN"),I=i("dNgK"),V=i("z52X"),D=i("LcQX"),O=i("w2pn"),A=i("tKbL"),E=i("XXEo"),k=i("tyNb"),L=i("TmG/"),M=i("LP0t"),j=i("Nfhe");function P(e,t){if(1&e){const e=w["\u0275\u0275getCurrentView"]();w["\u0275\u0275elementStart"](0,"button",23),w["\u0275\u0275listener"]("click",(function(){return w["\u0275\u0275restoreView"](e),w["\u0275\u0275nextContext"](),w["\u0275\u0275reference"](8).toggle()})),w["\u0275\u0275elementStart"](1,"div",24),w["\u0275\u0275elementStart"](2,"strong"),w["\u0275\u0275text"](3,"Attachments"),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementStart"](4,"mat-icon",25),w["\u0275\u0275text"](5,"attach_file"),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"]()}if(2&e){w["\u0275\u0275nextContext"]();const e=w["\u0275\u0275reference"](8);w["\u0275\u0275property"]("satPopoverAnchor",e)}}function T(e,t){1&e&&(w["\u0275\u0275elementContainerStart"](0,26),w["\u0275\u0275elementStart"](1,"div",27),w["\u0275\u0275element"](2,"img",28),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementStart"](3,"div",29),w["\u0275\u0275elementStart"](4,"span"),w["\u0275\u0275text"](5,"No Attachments found"),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementContainerEnd"]())}function N(e,t){if(1&e){const e=w["\u0275\u0275getCurrentView"]();w["\u0275\u0275elementStart"](0,"div",32),w["\u0275\u0275elementStart"](1,"div",33),w["\u0275\u0275elementStart"](2,"span",34),w["\u0275\u0275text"](3),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementStart"](4,"span",35),w["\u0275\u0275elementStart"](5,"button",36),w["\u0275\u0275listener"]("click",(function(){w["\u0275\u0275restoreView"](e);const i=t.index;return w["\u0275\u0275nextContext"](3).removeFile(i)})),w["\u0275\u0275elementStart"](6,"mat-icon",37),w["\u0275\u0275text"](7,"close"),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;w["\u0275\u0275advance"](3),w["\u0275\u0275textInterpolate"](e.file.name)}}function $(e,t){if(1&e&&(w["\u0275\u0275elementContainerStart"](0,30),w["\u0275\u0275template"](1,N,8,1,"div",31),w["\u0275\u0275elementContainerEnd"]()),2&e){const e=w["\u0275\u0275nextContext"](2);w["\u0275\u0275advance"](1),w["\u0275\u0275property"]("ngForOf",e.fileList)}}function B(e,t){if(1&e){const e=w["\u0275\u0275getCurrentView"]();w["\u0275\u0275elementStart"](0,"button",23),w["\u0275\u0275listener"]("click",(function(){return w["\u0275\u0275restoreView"](e),w["\u0275\u0275nextContext"](),w["\u0275\u0275reference"](19).toggle()})),w["\u0275\u0275elementStart"](1,"div",24),w["\u0275\u0275elementStart"](2,"strong"),w["\u0275\u0275text"](3,"Recurrence Config"),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementStart"](4,"mat-icon",25),w["\u0275\u0275text"](5,"settings"),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"]()}if(2&e){w["\u0275\u0275nextContext"]();const e=w["\u0275\u0275reference"](19);w["\u0275\u0275property"]("satPopoverAnchor",e)}}function R(e,t){1&e&&(w["\u0275\u0275elementContainerStart"](0,38),w["\u0275\u0275elementStart"](1,"div",27),w["\u0275\u0275element"](2,"img",28),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementStart"](3,"div",29),w["\u0275\u0275elementStart"](4,"span"),w["\u0275\u0275text"](5,"Recurring is turned off. Please turn on and set appropriate config."),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementContainerEnd"]())}function q(e,t){if(1&e&&(w["\u0275\u0275elementStart"](0,"div",43),w["\u0275\u0275element"](1,"app-input-search",52),w["\u0275\u0275elementEnd"]()),2&e){const e=w["\u0275\u0275nextContext"](3);w["\u0275\u0275advance"](1),w["\u0275\u0275property"]("list",e.dateFieldsList)}}function G(e,t){if(1&e&&(w["\u0275\u0275elementStart"](0,"div",43),w["\u0275\u0275element"](1,"app-common-dropdown",53),w["\u0275\u0275elementEnd"]()),2&e){const e=w["\u0275\u0275nextContext"](3);w["\u0275\u0275advance"](1),w["\u0275\u0275property"]("label","Assign to field")("isMultiple",!1)("fieldName","name")("fieldValue","id")("needsAPICall",!1)("itemsList",e.assignToFieldsList)("fieldIcon","arrow_drop_down")}}function H(e,t){if(1&e&&(w["\u0275\u0275elementStart"](0,"div",43),w["\u0275\u0275element"](1,"app-common-dropdown",54),w["\u0275\u0275elementEnd"]()),2&e){const e=w["\u0275\u0275nextContext"](3);w["\u0275\u0275advance"](1),w["\u0275\u0275property"]("apiURI",e.assignToRecurrenceApiUri)("label","Select Employees")("isMultiple",!0)("fieldName","name")("fieldValue","id")("apiParams",e.assignToRecurrenceParams)("needsAPICall",!0)("fieldIcon","arrow_drop_down")}}function U(e,t){if(1&e&&(w["\u0275\u0275elementContainerStart"](0),w["\u0275\u0275elementStart"](1,"div",39),w["\u0275\u0275elementStart"](2,"div",40),w["\u0275\u0275elementStart"](3,"mat-icon",41),w["\u0275\u0275text"](4,"info"),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementStart"](5,"span",42),w["\u0275\u0275text"](6,"Recurrence Config"),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementStart"](7,"div",43),w["\u0275\u0275element"](8,"app-input-search",44),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementStart"](9,"div",43),w["\u0275\u0275elementStart"](10,"mat-form-field",45),w["\u0275\u0275elementStart"](11,"mat-label"),w["\u0275\u0275text"](12,"Set Occurence"),w["\u0275\u0275elementEnd"](),w["\u0275\u0275element"](13,"input",46),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementStart"](14,"div",43),w["\u0275\u0275elementStart"](15,"mat-form-field",45),w["\u0275\u0275elementStart"](16,"mat-label"),w["\u0275\u0275text"](17,"End Date"),w["\u0275\u0275elementEnd"](),w["\u0275\u0275element"](18,"input",47),w["\u0275\u0275element"](19,"mat-datepicker-toggle",48),w["\u0275\u0275element"](20,"mat-datepicker",null,49),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"](),w["\u0275\u0275template"](22,q,2,1,"div",50),w["\u0275\u0275template"](23,G,2,7,"div",50),w["\u0275\u0275template"](24,H,2,8,"div",50),w["\u0275\u0275elementStart"](25,"div",43),w["\u0275\u0275element"](26,"app-common-dropdown",51),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementContainerEnd"]()),2&e){const e=w["\u0275\u0275reference"](21),t=w["\u0275\u0275nextContext"](2);w["\u0275\u0275advance"](8),w["\u0275\u0275property"]("list",t.recurrenceFrequency),w["\u0275\u0275advance"](10),w["\u0275\u0275property"]("min",t.minDate)("matDatepicker",e),w["\u0275\u0275advance"](1),w["\u0275\u0275property"]("for",e),w["\u0275\u0275advance"](3),w["\u0275\u0275property"]("ngIf",t.dateFieldsList.length),w["\u0275\u0275advance"](1),w["\u0275\u0275property"]("ngIf",t.assignToFieldsList.length),w["\u0275\u0275advance"](1),w["\u0275\u0275property"]("ngIf",t.recurrenceConfigForm.get("assignToField").value),w["\u0275\u0275advance"](2),w["\u0275\u0275property"]("apiURI",t.statusDropdownRecurrenceApiUri)("label","Select Default Status")("isMultiple",!1)("fieldName","name")("fieldValue","id")("apiParams",t.statusDropdownRecurrenceParams)("needsAPICall",!0)("fieldIcon","arrow_drop_down")}}function W(e,t){if(1&e){const e=w["\u0275\u0275getCurrentView"]();w["\u0275\u0275elementStart"](0,"gridster-item",60),w["\u0275\u0275elementStart"](1,"app-input-single-select",61),w["\u0275\u0275listener"]("valueUpdate",(function(i){w["\u0275\u0275restoreView"](e);const s=t.index,l=t.$implicit;return w["\u0275\u0275nextContext"](3).onValueUpdateGL(i,s,l)}))("validationsUpdate",(function(i){w["\u0275\u0275restoreView"](e);const s=t.index,l=t.$implicit;return w["\u0275\u0275nextContext"](3).onValidationsUpdate(i,{i:s},l)}))("subformFields",(function(i){w["\u0275\u0275restoreView"](e);const s=t.index;return w["\u0275\u0275nextContext"](3).addSubformFields(i,s,null)}))("editorHeight",(function(i){w["\u0275\u0275restoreView"](e);const s=t.$implicit,l=t.index;return w["\u0275\u0275nextContext"](3).getEditorHeight(i,s,l)}))("customErrorTextUpdate",(function(i){w["\u0275\u0275restoreView"](e);const s=t.$implicit;return w["\u0275\u0275nextContext"](3).onCustomErrorTextUpdate(i,s)})),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,i=t.index,s=w["\u0275\u0275nextContext"](3);w["\u0275\u0275property"]("item",e),w["\u0275\u0275advance"](1),w["\u0275\u0275property"]("ngClass","display"==s.mode?"prevent-click":"")("field",e)("id",i)("isPreview",!0)("patchValue",null==e?null:e.patchValue)("clearFieldData",(null==e?null:e.clearFieldData)||!1)("apiParams",null==e?null:e.apiParams)("isEditMode","edit"===s.mode)("externalApiConfig",null==e?null:e.externalApiConfig)("fieldControlConfig",null==e?null:e.fieldControlConfig)}}function z(e,t){if(1&e){const e=w["\u0275\u0275getCurrentView"]();w["\u0275\u0275elementStart"](0,"button",62),w["\u0275\u0275listener"]("click",(function(){return w["\u0275\u0275restoreView"](e),w["\u0275\u0275nextContext"](3).submitEntryData()})),w["\u0275\u0275elementStart"](1,"mat-icon"),w["\u0275\u0275text"](2,"done_all"),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"]()}if(2&e){const e=w["\u0275\u0275nextContext"](3);w["\u0275\u0275property"]("disabled",e.isValueSubmitting||e.isValidating)}}function X(e,t){if(1&e&&(w["\u0275\u0275elementContainerStart"](0),w["\u0275\u0275elementStart"](1,"div",55),w["\u0275\u0275elementStart"](2,"gridster",56),w["\u0275\u0275template"](3,W,2,11,"gridster-item",57),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementStart"](4,"div",58),w["\u0275\u0275template"](5,z,3,1,"button",59),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementContainerEnd"]()),2&e){const e=w["\u0275\u0275nextContext"](2);w["\u0275\u0275advance"](2),w["\u0275\u0275property"]("options",e.options),w["\u0275\u0275advance"](1),w["\u0275\u0275property"]("ngForOf",e.fieldsObject),w["\u0275\u0275advance"](2),w["\u0275\u0275property"]("ngIf","display"!=e.mode&&!e.restrictEdit)}}function J(e,t){if(1&e&&(w["\u0275\u0275elementStart"](0,"div"),w["\u0275\u0275text"](1),w["\u0275\u0275elementEnd"]()),2&e){const e=w["\u0275\u0275nextContext"]().index,t=w["\u0275\u0275nextContext"](3);w["\u0275\u0275advance"](1),w["\u0275\u0275textInterpolate"](t.msData.multiStepNames[e])}}function K(e,t){if(1&e){const e=w["\u0275\u0275getCurrentView"]();w["\u0275\u0275elementStart"](0,"gridster-item",60),w["\u0275\u0275elementStart"](1,"app-input-single-select",70),w["\u0275\u0275listener"]("valueUpdate",(function(i){w["\u0275\u0275restoreView"](e);const s=t.index,l=t.$implicit,a=w["\u0275\u0275nextContext"]().index;return w["\u0275\u0275nextContext"](3).onValueUpdateMS(i,a,s,l)}))("validationsUpdate",(function(i){w["\u0275\u0275restoreView"](e);const s=t.index,l=t.$implicit,a=w["\u0275\u0275nextContext"]().index;return w["\u0275\u0275nextContext"](3).onValidationsUpdate(i,{i:a,j:s},l)}))("subformFields",(function(i){w["\u0275\u0275restoreView"](e);const s=t.index,l=w["\u0275\u0275nextContext"]().index,a=w["\u0275\u0275nextContext"](3);return a.addSubformFields(i,s,l,a.msData.gridOptions[l])}))("editorHeight",(function(i){w["\u0275\u0275restoreView"](e);const s=t.$implicit,l=t.index,a=w["\u0275\u0275nextContext"]().index,r=w["\u0275\u0275nextContext"](3);return r.getEditorHeight(i,s,l,a,r.msData.gridOptions[a])}))("customErrorTextUpdate",(function(i){w["\u0275\u0275restoreView"](e);const s=t.$implicit;return w["\u0275\u0275nextContext"](4).onCustomErrorTextUpdate(i,s)})),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,i=t.index,s=w["\u0275\u0275nextContext"](4);w["\u0275\u0275property"]("item",e),w["\u0275\u0275advance"](1),w["\u0275\u0275property"]("ngClass","display"==s.mode?"prevent-click":"")("field",e)("id",i)("isPreview",!0)("patchValue",null==e?null:e.patchValue)("apiParams",null==e?null:e.apiParams)("clearFieldData",(null==e?null:e.clearFieldData)||!1)("isEditMode","edit"===s.mode)("externalApiConfig",null==e?null:e.externalApiConfig)("fieldControlConfig",null==e?null:e.fieldControlConfig)}}function Q(e,t){if(1&e){const e=w["\u0275\u0275getCurrentView"]();w["\u0275\u0275elementStart"](0,"button",62),w["\u0275\u0275listener"]("click",(function(){return w["\u0275\u0275restoreView"](e),w["\u0275\u0275nextContext"](4).submitEntryData()})),w["\u0275\u0275elementStart"](1,"mat-icon"),w["\u0275\u0275text"](2,"done_all"),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"]()}if(2&e){const e=w["\u0275\u0275nextContext"](4);w["\u0275\u0275property"]("disabled",e.isValueSubmitting||e.isValidating)}}function Y(e,t){1&e&&(w["\u0275\u0275elementStart"](0,"button",71),w["\u0275\u0275elementStart"](1,"mat-icon"),w["\u0275\u0275text"](2,"keyboard_arrow_right"),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"]())}function Z(e,t){1&e&&(w["\u0275\u0275elementStart"](0,"button",72),w["\u0275\u0275elementStart"](1,"mat-icon"),w["\u0275\u0275text"](2,"keyboard_arrow_left"),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"]())}function ee(e,t){if(1&e&&(w["\u0275\u0275elementStart"](0,"mat-step",64),w["\u0275\u0275template"](1,J,2,1,"ng-template",65),w["\u0275\u0275elementStart"](2,"div",66),w["\u0275\u0275elementStart"](3,"gridster",56),w["\u0275\u0275template"](4,K,2,11,"gridster-item",57),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementStart"](5,"div",67),w["\u0275\u0275template"](6,Q,3,1,"button",59),w["\u0275\u0275template"](7,Y,3,0,"button",68),w["\u0275\u0275template"](8,Z,3,0,"button",69),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"]()),2&e){const e=t.index,i=w["\u0275\u0275nextContext"](3);w["\u0275\u0275property"]("stepControl",i.msData.multiStepNames[e]),w["\u0275\u0275advance"](3),w["\u0275\u0275property"]("options",i.msData.gridOptions[e]),w["\u0275\u0275advance"](1),w["\u0275\u0275property"]("ngForOf",i.msFields[e]),w["\u0275\u0275advance"](2),w["\u0275\u0275property"]("ngIf","display"!=i.mode&&!i.restrictEdit),w["\u0275\u0275advance"](1),w["\u0275\u0275property"]("ngIf",e!=i.msData.multiStepCount-1),w["\u0275\u0275advance"](1),w["\u0275\u0275property"]("ngIf",0!=e)}}function te(e,t){if(1&e&&(w["\u0275\u0275elementContainerStart"](0),w["\u0275\u0275elementStart"](1,"mat-horizontal-stepper"),w["\u0275\u0275template"](2,ee,9,6,"mat-step",63),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementContainerEnd"]()),2&e){const e=w["\u0275\u0275nextContext"](2);w["\u0275\u0275advance"](2),w["\u0275\u0275property"]("ngForOf"," ".repeat(e.msData.multiStepCount).split(""))}}const ie=function(){return["*/*"]};function se(e,t){if(1&e){const e=w["\u0275\u0275getCurrentView"]();w["\u0275\u0275elementStart"](0,"div",2),w["\u0275\u0275elementStart"](1,"div",3),w["\u0275\u0275elementStart"](2,"div",4),w["\u0275\u0275elementStart"](3,"div"),w["\u0275\u0275text"](4),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementStart"](5,"div"),w["\u0275\u0275template"](6,P,6,1,"button",5),w["\u0275\u0275elementStart"](7,"sat-popover",6,7),w["\u0275\u0275elementStart"](9,"div",8),w["\u0275\u0275elementStart"](10,"div",9),w["\u0275\u0275elementStart"](11,"button",10),w["\u0275\u0275listener"]("click",(function(){return w["\u0275\u0275restoreView"](e),w["\u0275\u0275reference"](14).click()})),w["\u0275\u0275text"](12," Add Files "),w["\u0275\u0275elementEnd"](),w["\u0275\u0275element"](13,"input",11,12),w["\u0275\u0275elementEnd"](),w["\u0275\u0275template"](15,T,6,0,"ng-container",13),w["\u0275\u0275template"](16,$,2,1,"ng-container",14),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"](),w["\u0275\u0275template"](17,B,6,1,"button",5),w["\u0275\u0275elementStart"](18,"sat-popover",6,15),w["\u0275\u0275elementStart"](20,"div",16),w["\u0275\u0275elementStart"](21,"mat-slide-toggle",17),w["\u0275\u0275text"](22,"Is Recurring? "),w["\u0275\u0275elementEnd"](),w["\u0275\u0275template"](23,R,6,0,"ng-container",18),w["\u0275\u0275template"](24,U,27,15,"ng-container",19),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementStart"](25,"div"),w["\u0275\u0275elementStart"](26,"button",20),w["\u0275\u0275listener"]("click",(function(){return w["\u0275\u0275restoreView"](e),w["\u0275\u0275nextContext"]().closeDialog()})),w["\u0275\u0275elementStart"](27,"mat-icon",21),w["\u0275\u0275text"](28,"close"),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"](),w["\u0275\u0275elementEnd"](),w["\u0275\u0275element"](29,"mat-divider",22),w["\u0275\u0275template"](30,X,6,3,"ng-container",19),w["\u0275\u0275template"](31,te,3,1,"ng-container",19),w["\u0275\u0275elementEnd"]()}if(2&e){const e=w["\u0275\u0275nextContext"]();w["\u0275\u0275advance"](4),w["\u0275\u0275textInterpolate1"](" ",e.formName?e.formName:"Creation Form",""),w["\u0275\u0275advance"](2),w["\u0275\u0275property"]("ngIf","creation"==e.mode),w["\u0275\u0275advance"](7),w["\u0275\u0275property"]("uploader",e.uploader)("accept",w["\u0275\u0275pureFunction0"](12,ie)),w["\u0275\u0275advance"](2),w["\u0275\u0275property"]("ngIf",!(null!=e.fileList&&e.fileList.length)>0),w["\u0275\u0275advance"](1),w["\u0275\u0275property"]("ngIf",(null==e.fileList?null:e.fileList.length)>0),w["\u0275\u0275advance"](1),w["\u0275\u0275property"]("ngIf",e.isRecurrenceConfigActive),w["\u0275\u0275advance"](3),w["\u0275\u0275property"]("formGroup",e.recurrenceConfigForm),w["\u0275\u0275advance"](3),w["\u0275\u0275property"]("ngIf",!e.recurrenceConfigForm.get("isRecurring").value),w["\u0275\u0275advance"](1),w["\u0275\u0275property"]("ngIf",e.recurrenceConfigForm.get("isRecurring").value),w["\u0275\u0275advance"](6),w["\u0275\u0275property"]("ngIf","generalLayout"==e.selectedLayout),w["\u0275\u0275advance"](1),w["\u0275\u0275property"]("ngIf","multiStepLayout"==e.selectedLayout)}}function le(e,t){1&e&&(w["\u0275\u0275elementStart"](0,"div",73),w["\u0275\u0275element"](1,"mat-spinner",74),w["\u0275\u0275elementEnd"]()),2&e&&(w["\u0275\u0275advance"](1),w["\u0275\u0275property"]("diameter",20))}let ae=(()=>{class e{constructor(e,t,i,n,o,u,f,h,p,m,g,v){this._CustomFieldService=e,this.matDialogRef=t,this.inData=i,this.fb=n,this.snackBar=o,this._lcdpService=u,this._util=f,this._fieldLibraryService=h,this._wfPluginService=p,this._login=m,this.dialog=g,this.$router=v,this.dashboard=[],this.isPageLoading=!0,this.isEditMode=!1,this.isDisplayMode=!1,this.isSubmitted=!1,this.formDataGL=new r.g([]),this.formDataMS=new r.g([]),this.destroy$=new a.b,this.fieldConfigurations=[],this.allowedToProceed=!0,this.uniqueValueCheckPassed=!0,this.valueChangeSubscription=new d.Subscription,this.isValueSubmitting=!1,this.isValidating=!1,this.isUpdateFormCalled=!1,this.recurrenceFrequency=[],this.isRecurrenceConfigActive=!1,this.restrictEdit=!1,this.isWorkflowPassed=!1,this.recurrenceConfigForm=new r.m({isRecurring:new r.j(!1),frequency:new r.j(""),occurence:new r.j(""),endDate:new r.j(""),defaultStatus:new r.j(""),statusField:new r.j(""),dateField:new r.j(""),assignToField:new r.j(""),assignToList:new r.j(""),dateValueFromField:new r.j("")}),this.parentRecordId=null,this.currentUser=this._login.getProfile().profile,this.lcdpApplicationId=null,this.statusList=[],this.assignToFieldsList=[],this.dateFieldsList=[],this.assignToRecurrenceApiUri="/api/appBuilder/fields/assignto/getAllEmployees",this.assignToRecurrenceParams={},this.statusDropdownRecurrenceApiUri="/api/appBuilder/fields/dropdown/getMasterTableRecordsData",this.statusDropdownRecurrenceParams={},this.recurrenceConfigBeforeEdit={},this.unauthorizedSteps=[],this.enableCustomApiValidation=!1,this.apiConfigForCustomApiValidation={},this.enableRealTimeSLATrigger=!1,this.enableRecordDisplayInHeader=!1,this.uploader=new c.d({url:"/api/appBuilder/lcdp/attachments/uploadAttachment",authToken:"Bearer "+this._login.getToken(),disableMultipart:!1,maxFileSize:20971520,headers:[{name:"user",value:this.currentUser.name}]}),this.fileList=[],this.filesJsonConcat=[],this.fixedRowHeight=55,this.fieldCustomErrorText={},this.errorTextQueue=new r.g([]),this.fieldCustomErrorTextSubscription=new d.Subscription,this.fieldCustomErrorTextSnackbarOpen=!1,this.validateDate=e=>""==e.value||_(e.value).isValid()?null:{invalid:!0},this.getMaxRows=e=>{let t=0;for(const i of e){const e=i.y+i.rows-1;e>t&&(t=e)}return t},this.fileUploadChanges=()=>{this.uploader.onAfterAddingFile=e=>{this.fileList.push(e)},this.uploader.onCompleteItem=(e,t,i,l)=>Object(s.c)(this,void 0,void 0,(function*(){if(200==i&&t&&t.length>0){let e=JSON.parse(t);e.error?this._util.showToastMessage("Unable to upload"):(this.filesJsonConcat=this.filesJsonConcat.concat(e.data),this.uploader.queue.length==this.filesJsonConcat.length&&this.updateFileInRecord())}else this._util.showToastMessage("Unable to upload")}))},this.updateFileInRecord=()=>Object(s.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>{this.filesJsonConcat.length?this._lcdpService.addAttachment(this.lcdpApplicationId,this.recordId,this.filesJsonConcat).subscribe(i=>Object(s.c)(this,void 0,void 0,(function*(){"S"==i.messType?(this._util.showToastMessage("File(s) uploaded Successfully"),e(!0)):(this._util.showToastMessage(i.messText),t(i.messText))})),e=>{this._lcdpService.showErrorMessage(e),t(e)}):e(!0)})})),this.checkForSLATrigger=(e,t,i)=>{this.lcdpApplicationId&&i._id&&e&&t&&this._lcdpService.checkForSLATrigger(this.lcdpApplicationId,i._id,e,t).pipe(Object(l.a)(this.destroy$)).subscribe(e=>{if("S"==e.messType)return!0},e=>{console.log(e),this._util.showErrorMessage(e,"KEBS")})}}ngOnInit(){return Object(s.c)(this,void 0,void 0,(function*(){this.options={gridType:u.b.VerticalFixed,fixedRowHeight:this.fixedRowHeight,compactType:u.a.None,pushItems:!1,draggable:{enabled:!1},resizable:{enabled:!1},minCols:12,maxCols:12,minRows:5,setGridSize:!0},yield this.patchForm(),this.fileUploadChanges(),this.lcdpApplicationId&&(yield this.getAppBuilderAppConfig(this.lcdpApplicationId)),this.minDate=_().startOf("day"),this.fieldCustomErrorTextSubscription=this.errorTextQueue.valueChanges.pipe(Object(l.a)(this.destroy$)).subscribe(e=>{let t=this.errorTextQueue;if(t.length){let e=t.value[0];if(!this.fieldCustomErrorTextSnackbarOpen){let t=this.snackBar.open(e,"Dismiss",{duration:2e3});this.fieldCustomErrorTextSnackbarOpen=!0,t.afterDismissed().subscribe(()=>{this.fieldCustomErrorTextSnackbarOpen=!1,this.errorTextQueue.removeAt(0)})}}})}))}getAppBuilderAppConfig(e){return Object(s.c)(this,void 0,void 0,(function*(){this._lcdpService.getAppBuilderAppConfig(e).pipe(Object(l.a)(this.destroy$)).subscribe(e=>{"S"==e.messType&&e.data?(this.enableCustomApiValidation=e.data.enableCustomApiValidation,this.apiConfigForCustomApiValidation=e.data.apiConfigForCustomApiValidation,this.enableRealTimeSLATrigger=e.data.enableRealTimeSLATrigger||!1,this.enableRecordDisplayInHeader=e.data.enableRecordDisplayInHeader||!1):this._util.showToastMessage("AppConfig not found")},e=>{this._util.showErrorMessage(e,"KEBS")})}))}resolveSubscriptions(e){if(this.valueChangeSubscription&&e&&(this.valueChangeSubscription.unsubscribe(),this.valueChangeSubscription=new d.Subscription),"generalLayout"==this.selectedLayout)for(let t=0;t<this.formDataGL.controls.length;t++)this.valueChangeSubscription.add(this.formDataGL.controls[t].valueChanges.debounceTime(10).pipe(Object(l.a)(this.destroy$)).subscribe(e=>{e&&this.resolveValueBasedOnFieldConfiguration(!0,e)}));else for(let t=0;t<this.formDataMS.controls.length;t++)for(let e=0;e<this.formDataMS.controls[t].controls.length;e++)this.valueChangeSubscription.add(this.formDataMS.controls[t].controls[e].valueChanges.debounceTime(10).pipe(Object(l.a)(this.destroy$)).subscribe(e=>{e&&this.resolveValueBasedOnFieldConfiguration(!1,e)}))}resolveValueBasedOnFieldConfiguration(e,t){if(this.fieldConfigurations.length>0){let i=o.filter(this.fieldConfigurations,e=>"patch"==e.configuration_type);if(i.length>0)for(let l of i){let i=o.pluck(l.ref_fields,"field_id");if(o.contains(i,t.id.toString())){let t=e?this.formDataGL.value:o.flatten(this.formDataMS.value);for(let i of t)if(i.id.toString()==l.field_id.toString()){let i=e?this.fieldsObject:o.flatten(this.msFields),s=o.filter(i,e=>e._id.toString()==l.field_id.toString());s.length>0&&this.patchOperatorField(l,s[0],t,e)}}}let s=o.filter(this.fieldConfigurations,e=>"default"==e.configuration_type);if(s.length>0)for(let l of s)if(l.field_dependencies&&l.field_dependencies.length&&l.field_dependencies[0].field_id&&l.config_value){let i=null;i="current_date"==l.config_value?_().startOf("day").add(_().local().utcOffset(),"minutes"):"current_user"==l.config_value?this.currentUser.aid:l.config_value;let s=o.pluck(l.field_dependencies,"field_id");if(o.contains(s,t.id.toString())){let t=e?this.formDataGL.value:o.flatten(this.formDataMS.value),s=[];for(let e of l.field_dependencies){let i=!1,l=o.filter(t,t=>t.id.toString()==e.field_id.toString());i=!(!l.length||l[0].value!=e.field_value),s.push(i)}if(!o.contains(s,!1)){this.patchValueInForm(l.field_id,i,e,!0);break}this.patchValueInForm(l.field_id,"",e,!0)}}this.resolveApiTypeConfig(e,t)}}resolveApiTypeConfig(e,t,i=!1){let s=o.filter(this.fieldConfigurations,e=>"api"==e.configuration_type);if(s.length>0)for(let l of s){let s=o.pluck(l.ref_fields,"field_id");if(o.contains(s,t.id.toString())){let t=e?this.fieldsObject:o.flatten(this.msFields),s=o.filter(t,e=>e._id.toString()==l.field_id.toString());if(s.length>0&&6==s[0].fieldId){let t=e?this.formDataGL.value:o.flatten(this.formDataMS.value),a=[];for(let e of l.ref_fields)if(e.api_params_field_name)for(let l of t)l.value&&e.field_id.toString()==l.id.toString()&&(i||(s[0].clearFieldData=!0),a.push({key:e.api_params_field_name,value:l.value}));a.length>0&&this.patchApiParamsInForm(l.field_id,a,e)}}}}patchOperatorField(e,t,i,s){let l=null;switch(e.operator){case"add":for(let a of e.ref_fields){let r=o.filter(i,e=>e.id.toString()==a.field_id.toString());if(r.length>0&&r[0].value)if(5==t.fieldId){if(_(r[0].value).isValid()){let i=_(r[0].value).add(e.config_value?e.config_value:0,"days");this.patchValueInForm(t._id,i,s,!1)}}else l?l+=r[0].value:l=r[0].value}null!=l&&null!=l&&(e.config_value&&(l+=e.config_value),e.value_to_percentage&&(l=parseFloat((100*l).toFixed(2))),this.patchValueInForm(t._id,l,s,!1));break;case"subtract":for(let a of e.ref_fields){let r=o.filter(i,e=>e.id.toString()==a.field_id.toString());if(r.length>0&&r[0].value)if(5==t.fieldId){if(_(r[0].value).isValid()){let i=_(r[0].value).subtract(e.config_value?e.config_value:0,"days");this.patchValueInForm(t._id,i,s,!1)}}else l?l-=r[0].value:l=r[0].value}null!=l&&null!=l&&(e.config_value&&(l-=e.config_value),e.value_to_percentage&&(l=parseFloat((100*l).toFixed(2))),this.patchValueInForm(t._id,l,s,!1));break;case"multiply":for(let s of e.ref_fields){let e=o.filter(i,e=>e.id.toString()==s.field_id.toString());e.length>0&&e[0].value&&5!=t.fieldId&&(l?l*=e[0].value:l=e[0].value)}null!=l&&null!=l&&(e.config_value&&(l*=e.config_value),e.value_to_percentage&&(l=parseFloat((100*l).toFixed(2))),this.patchValueInForm(t._id,l,s,!1));break;case"divide":for(let s of e.ref_fields){let e=o.filter(i,e=>e.id.toString()==s.field_id.toString());e.length>0&&e[0].value&&5!=t.fieldId&&(l?l/=e[0].value:l=e[0].value)}null!=l&&null!=l&&(e.config_value&&(l/=e.config_value),e.value_to_percentage&&(l=parseFloat((100*l).toFixed(2))),this.patchValueInForm(t._id,l,s,!1));break;case"equal to":for(let l of e.ref_fields){let e=o.filter(i,e=>e.id.toString()==l.field_id.toString());e.length>0&&e[0].value&&this.patchValueInForm(t._id,e[0].value,s,!1)}}}patchValueInForm(e,t,i,s){if(i){for(let i of this.fieldsObject)i._id.toString()==e.toString()&&(!i.allowMultiSelect||t instanceof Array||(t=[t]),i.patchValue=t);for(let i=0;i<this.formDataGL.controls.length;i++)this.formDataGL.controls[i].value&&this.formDataGL.controls[i].get("id").value==e.toString()&&this.formDataGL.controls[i].get("value").patchValue(t)}else{for(let i=0;i<this.msFields.length;i++)for(let s=0;s<this.msFields[i].length;s++)this.msFields[i][s]._id.toString()==e.toString()&&(!this.msFields[i][s].allowMultiSelect||t instanceof Array||(t=[t]),this.msFields[i][s].patchValue=t);for(let i=0;i<this.formDataMS.controls.length;i++)for(let s=0;s<this.formDataMS.controls[i].controls.length;s++)this.formDataMS.controls[i].controls[s].value&&this.formDataMS.controls[i].controls[s].get("id").value==e.toString()&&this.formDataMS.controls[i].controls[s].get("value").patchValue(t)}}patchApiParamsInForm(e,t,i){if(i)for(let s of this.fieldsObject)s._id.toString()==e.toString()&&(s.apiParams=t);else for(let s=0;s<this.msFields.length;s++)for(let i=0;i<this.msFields[s].length;i++)this.msFields[s][i]._id.toString()==e.toString()&&(this.msFields[s][i].apiParams=t)}patchForm(){return Object(s.c)(this,void 0,void 0,(function*(){this.formId=this.inData.formId,this.entryForId=this.inData.entryForId,this.formName="formName"in this.inData?this.inData.formName:"Creation Form";let e={formId:this.formId,entryForId:this.entryForId};this.inData.lcdpApplicationId&&(this.lcdpApplicationId=this.inData.lcdpApplicationId),this._CustomFieldService.getMasterForm(e).pipe(Object(l.a)(this.destroy$)).subscribe(e=>{var t,i,s;this.masterForm=e;let a=e.data;if(this._CustomFieldService.clearData(),this._CustomFieldService.setAppId(a.applicationId),this.selectedLayout=a.layout,this.mode=a.mode,this._CustomFieldService.checkIfRecurrenceConfigActive(this.formId).pipe(Object(l.a)(this.destroy$)).subscribe(e=>{e.data&&e.data.is_recurrence_config_active&&(this.isRecurrenceConfigActive=!0,this.getMasterRecurrenceFrequency())}),(null===(t=this.inData)||void 0===t?void 0:t.isEdit)&&"creation"!=this.mode?this.mode="edit":(null===(i=this.inData)||void 0===i?void 0:i.isEdit)&&"creation"==this.mode?this.mode="creation":(null===(s=this.inData)||void 0===s?void 0:s.isEdit)||"creation"!=this.mode||(this.mode="display"),this.submission_id=a.submission_id,a&&a.formEntry&&(this.mainCollectionSubmissionId=a.formEntry._id,this.recordId=a.formEntry._id),this._CustomFieldService.setLayout(this.selectedLayout),this.appId=a.applicationId,this.formName=a.formName,this.restrictEdit=a.restrictEdit,"generalLayout"==this.selectedLayout){this.fieldsObject=[];for(let e of a.fields)5==e.fieldId&&e.fieldValue&&(e.useDateRange?(e.fieldValue.start=e.fieldValue.start?_(e.fieldValue.start).utc():"",e.fieldValue.end=e.fieldValue.end?_(e.fieldValue.end).utc():""):e.allowTimeSelection||(e.fieldValue=_(e.fieldValue).utc())),this.fieldsObject.push(e),"1"==e.fieldId?this.assignToFieldsList.push({id:e._id,name:e.fieldName}):"5"==e.fieldId&&this.dateFieldsList.push({id:e._id,name:e.fieldName}),e.isStatusField&&(this.recurrenceConfigForm.get("statusField").setValue(e._id),this.statusDropdownRecurrenceParams={table_name:e.masterData,key_field:e.masterDataKeyField,value_field:e.masterDataValueField,api_params:null,keyId:null});for(let e=0;e<this.fieldsObject.length;e++)this.fieldCustomErrorText[this.fieldsObject[e]._id]=null,this.createFormControlGL(this.fieldsObject[e],e)}else{this.msData=a.msData,this.msFields=a.fields,this.msData.gridOptions=[];for(let e=0;e<this.msData.multiStepCount;e++)this.msData.gridOptions.push(Object.assign({},this.options));for(let e=0;e<this.msFields.length;e++){this.formDataMS.push(new r.g([]));for(let t=0;t<this.msFields[e].length;t++)"1"==this.msFields[e][t].fieldId?this.assignToFieldsList.push({id:this.msFields[e][t]._id,name:this.msFields[e][t].fieldName}):5==this.msFields[e][t].fieldId&&this.msFields[e][t].fieldValue&&(this.msFields[e][t].useDateRange?(this.msFields[e][t].fieldValue.start=this.msFields[e][t].fieldValue.start?_(this.msFields[e][t].fieldValue.start).utc():"",this.msFields[e][t].fieldValue.end=this.msFields[e][t].fieldValue.end?_(this.msFields[e][t].fieldValue.end).utc():""):this.msFields[e][t].allowTimeSelection||(this.msFields[e][t].fieldValue=_(this.msFields[e][t].fieldValue).utc())),this.msFields[e][t].isStatusField&&(this.recurrenceConfigForm.get("statusField").setValue(this.msFields[e][t]._id),this.statusDropdownRecurrenceParams={table_name:this.msFields[e][t].masterData,key_field:this.msFields[e][t].masterDataKeyField,value_field:this.msFields[e][t].masterDataValueField,api_params:null,keyId:null}),this.fieldCustomErrorText[this.msFields[e][t]._id]=null,this.createFormControlMS(e,t,this.msFields[e][t])}}if(this.unauthorizedSteps=a.unauthorized_steps,this.patchInputData(),this.filterApiConfigFields(!0),this.filterFieldControlConfiguration(),a&&a.recurrenceConfig){let e=o.pick(a.recurrenceConfig,["isRecurring","frequency","occurence","endDate","defaultStatus","statusField","dateField","assignToField","assignToList"]);this.recurrenceConfigForm.patchValue(e),this.recurrenceConfigBeforeEdit=this.recurrenceConfigForm.value,this.recurrenceConfigForm.get("assignToList").value&&(this.assignToRecurrenceParams.keyId=this.recurrenceConfigForm.get("assignToList").value),this.recurrenceConfigForm.get("defaultStatus").value&&(this.statusDropdownRecurrenceParams.keyId=this.recurrenceConfigForm.get("defaultStatus").value)}this.isPageLoading=!1,this.getFieldConfigurations()},e=>{this.isPageLoading=!1,console.error(e),this.snackBar.open("Error retrieving forms!","Dismiss",{duration:4e3})})}))}patchInputData(){if(this.inData&&this.inData.inputData&&(this.inData.inputData.parentRecordId&&(this.parentRecordId=this.inData.inputData.parentRecordId),setTimeout(()=>{if(this.inData.inputData.fieldMappedInput&&Object.keys(this.inData.inputData.fieldMappedInput).length)for(const[e,t]of Object.entries(this.inData.inputData.fieldMappedInput))this.patchValueInForm(e,t,"generalLayout"===this.selectedLayout,!1)},10)),this.inData.isFromTimelineView&&this.inData.inputFields)for(const[e,t]of Object.entries(this.inData.inputFields))this.patchValueInForm(e,t,"generalLayout"===this.selectedLayout,!1)}getFieldConfigurations(){this.lcdpApplicationId&&this._lcdpService.getFieldConfigurations(this.lcdpApplicationId).pipe(Object(l.a)(this.destroy$)).subscribe(e=>{"S"==e.messType&&e.data.length>0&&(this.fieldConfigurations=e.data,this.applyDefaultValues(),this.resolveSubscriptions(!1))},e=>{this._lcdpService.showErrorMessage(e)})}applyDefaultValues(){console.log("default applied");let e=o.filter(this.fieldConfigurations,e=>"default"==e.configuration_type),t="generalLayout"===this.selectedLayout;if(e.length>0)for(let s of e)if(s.config_value&&(!s.hasOwnProperty("field_dependencies")||0==s.field_dependencies.length||null==s.field_dependencies[0].field_id)){let e=null;if("current_date"==s.config_value){e=_().startOf("day").add(_().local().utcOffset(),"minutes");const i=o.findWhere(o.flatten(t?this.fieldsObject:this.msFields),{_id:s.field_id});i&&i.allowTimeSelection&&(e=_())}else e="current_user"==s.config_value?this.currentUser.aid:s.config_value;this.canDefaultValueBePatched(s.field_id,t)&&this.patchValueInForm(s.field_id,e,t,!0)}let i=o.filter(this.fieldConfigurations,e=>"patch"==e.configuration_type);if(i.length>0)for(let s of i)if(this.canDefaultValueBePatched(s.field_id,t)){let e=t?this.fieldsObject:o.flatten(this.msFields),i=o.filter(e,e=>e._id.toString()==s.field_id.toString());if(i.length){let e=t?this.formDataGL.value:o.flatten(this.formDataMS.value);this.patchOperatorField(s,i[0],e,t)}}}canDefaultValueBePatched(e,t){if(t){for(let i=0;i<this.formDataGL.controls.length;i++)if(this.formDataGL.controls[i].value&&this.formDataGL.controls[i].get("id").value==e.toString())return!this.formDataGL.controls[i].get("value").value}else for(let i=0;i<this.formDataMS.controls.length;i++)for(let t=0;t<this.formDataMS.controls[i].controls.length;t++)if(this.formDataMS.controls[i].controls[t].value&&this.formDataMS.controls[i].controls[t].get("id").value==e.toString())return!this.formDataMS.controls[i].controls[t].get("value").value}organizeTabFields(e){if(e){let e=o.uniq(o.pluck(this.fieldsObject,"y")),t=o.sortBy(e,e=>e),i=0;for(let s of t)for(let e=0;e<this.fieldsObject.length;e++)this.fieldsObject[e].y==s&&(this.fieldsObject[e].tabIndex=i++)}else{let e=0;for(let t=0;t<this.msFields.length;t++){let i=o.uniq(o.pluck(this.msFields[t],"y")),s=o.sortBy(i,e=>e);for(let l of s)for(let i=0;i<this.msFields[t].length;i++)this.msFields[t][i].y==l&&(this.msFields[t][i].tabIndex=e++)}}}createFormControlGL(e,t){var i,s,l,a,n,o,d,u;switch(parseInt(this.fieldsObject[t].fieldId)){case 5:this.formDataGL.insert(t,this.fb.group(this.fieldsObject[t].useDateRange?this.fieldsObject[t].mandatoryField?{id:e._id,fieldName:e.fieldName,value:this.fb.group({start:[(null===(i=e.fieldValue)||void 0===i?void 0:i.start)||"",[r.H.required,this.validateDate]],end:[(null===(s=e.fieldValue)||void 0===s?void 0:s.end)||"",[r.H.required,this.validateDate]]})}:{id:e._id,fieldName:e.fieldName,value:this.fb.group({start:[(null===(l=null==e?void 0:e.fieldValue)||void 0===l?void 0:l.start)||"",[this.validateDate]],end:[(null===(a=null==e?void 0:e.fieldValue)||void 0===a?void 0:a.end)||"",[this.validateDate]]})}:this.fieldsObject[t].mandatoryField?{id:e._id,fieldName:e.fieldName,value:[e.fieldValue||"",[r.H.required,this.validateDate]]}:{id:e._id,fieldName:e.fieldName,value:[e.fieldValue||"",[this.validateDate]]}));break;case 7:this.formDataGL.insert(t,this.fb.group(this.fieldsObject[t].mandatoryField?{id:e._id,fieldName:e.fieldName,value:[e.fieldValue,[r.H.required,r.H.email]]}:{id:e._id,fieldName:e.fieldName,value:[e.fieldValue,r.H.email]}));break;case 10:this.formDataGL.insert(t,this.fb.group(this.fieldsObject[t].addPhoneExtension?this.fieldsObject[t].mandatoryField?{id:e._id,fieldName:e.fieldName,value:[e.fieldValue,[r.H.required]]}:{id:e._id,fieldName:e.fieldName,value:e.fieldValue}:this.fieldsObject[t].mandatoryField?{id:e._id,fieldName:e.fieldName,value:this.fb.group({extension:[null===(n=e.fieldValue)||void 0===n?void 0:n.extension,[r.H.required,r.H.pattern("^[0-9]*$")]],phone:[null===(o=e.fieldValue)||void 0===o?void 0:o.phone,[r.H.required,r.H.pattern("^[0-9]*$")]]})}:{id:e._id,fieldName:e.fieldName,value:this.fb.group({extension:[null===(d=e.fieldValue)||void 0===d?void 0:d.extension,r.H.pattern("^[0-9]*$")],phone:[null===(u=e.fieldValue)||void 0===u?void 0:u.phone,r.H.pattern("^[0-9]*$")]})}));break;case 17:this.formDataGL.insert(t,this.fb.group(this.fieldsObject[t].mandatoryField?{id:e._id,fieldName:e.fieldName,value:[e.fieldValue,[r.H.required,r.H.pattern("(https?://)?([\\da-z.-]+)\\.([a-z.]{2,6})[/\\w .-]*/?")]]}:{id:e._id,fieldName:e.fieldName,value:[e.fieldValue,r.H.pattern("(https?://)?([\\da-z.-]+)\\.([a-z.]{2,6})[/\\w .-]*/?")]}));break;default:this.formDataGL.insert(t,this.fb.group(this.fieldsObject[t].allowMultiSelect?this.fieldsObject[t].mandatoryField?{id:e._id,fieldName:e.fieldName,value:[[e.fieldValue],[r.H.required,r.H.pattern(/^(\s+\S+\s*)*(?!\s).*$/)]]}:{id:e._id,fieldName:e.fieldName,value:[e.fieldValue]}:this.fieldsObject[t].mandatoryField?{id:e._id,fieldName:e.fieldName,value:[e.fieldValue,[r.H.required,r.H.pattern(/^(\s+\S+\s*)*(?!\s).*$/)]]}:{id:e._id,fieldName:e.fieldName,value:e.fieldValue}))}null!=e.fieldValue&&null!=e.fieldValue&&""!==e.fieldValue&&setTimeout(()=>{this.resolveApiTypeConfig(!0,this.formDataGL.at(t).value,!0)},100)}createFormControlMS(e,t,i){var s,l,a,n,o,d,u,f;let c=this.formDataMS.controls[e];switch(parseInt(this.msFields[e][t].fieldId)){case 5:c.insert(t,this.fb.group(this.msFields[e][t].useDateRange?this.msFields[e][t].mandatoryField?{id:i._id,fieldName:i.fieldName,value:this.fb.group({start:[(null===(s=i.fieldValue)||void 0===s?void 0:s.start)||"",[r.H.required,this.validateDate]],end:[(null===(l=i.fieldValue)||void 0===l?void 0:l.end)||"",[r.H.required,this.validateDate]]})}:{id:i._id,fieldName:i.fieldName,value:this.fb.group({start:[(null===(a=null==i?void 0:i.fieldValue)||void 0===a?void 0:a.start)||"",[this.validateDate]],end:[(null===(n=null==i?void 0:i.fieldValue)||void 0===n?void 0:n.end)||"",[this.validateDate]]})}:this.msFields[e][t].mandatoryField?{id:i._id,fieldName:i.fieldName,value:[i.fieldValue||"",[r.H.required,this.validateDate]]}:{id:i._id,fieldName:i.fieldName,value:[i.fieldValue||"",[this.validateDate]]}));break;case 7:c.insert(t,this.fb.group(this.msFields[e][t].mandatoryField?{id:i._id,fieldName:i.fieldName,value:[i.fieldValue,[r.H.required,r.H.email]]}:{id:i._id,fieldName:i.fieldName,value:[i.fieldValue,r.H.email]}));break;case 10:c.insert(t,this.fb.group(this.msFields[e][t].addPhoneExtension?this.msFields[e][t].mandatoryField?{id:i._id,fieldName:i.fieldName,value:this.fb.group({extension:[null===(o=i.fieldValue)||void 0===o?void 0:o.extension,[r.H.required,r.H.pattern("^[0-9]*$")]],phone:[null===(d=i.fieldValue)||void 0===d?void 0:d.phone,[r.H.required,r.H.pattern("^[0-9]*$")]]})}:{id:i._id,fieldName:i.fieldName,value:this.fb.group({extension:[null===(u=i.fieldValue)||void 0===u?void 0:u.extension,r.H.pattern("^[0-9]*$")],phone:[null===(f=i.fieldValue)||void 0===f?void 0:f.phone,r.H.pattern("^[0-9]*$")]})}:this.msFields[e][t].mandatoryField?{id:i._id,fieldName:i.fieldName,value:[i.fieldValue,[r.H.required]]}:{id:i._id,fieldName:i.fieldName,value:[i.fieldValue]}));break;case 17:c.insert(t,this.fb.group(this.msFields[e][t].mandatoryField?{id:i._id,fieldName:i.fieldName,value:[i.fieldValue,[r.H.required,r.H.pattern("(https?://)?([\\da-z.-]+)\\.([a-z.]{2,6})[/\\w .-]*/?")]]}:{id:i._id,fieldName:i.fieldName,value:[i.fieldValue,r.H.pattern("(https?://)?([\\da-z.-]+)\\.([a-z.]{2,6})[/\\w .-]*/?")]}));break;default:c.insert(t,this.fb.group(this.msFields[e][t].allowMultiSelect?this.msFields[e][t].mandatoryField?{id:i._id,fieldName:i.fieldName,value:[[i.fieldValue],[r.H.required,r.H.pattern(/^(\s+\S+\s*)*(?!\s).*$/)]]}:{id:i._id,fieldName:i.fieldName,value:[i.fieldValue]}:this.msFields[e][t].mandatoryField?{id:i._id,fieldName:i.fieldName,value:[i.fieldValue,[r.H.required,r.H.pattern(/^(\s+\S+\s*)*(?!\s).*$/)]]}:{id:i._id,fieldName:i.fieldName,value:i.fieldValue}))}null!=i.fieldValue&&null!=i.fieldValue&&""!==i.fieldValue&&setTimeout(()=>{this.resolveApiTypeConfig(!1,c.at(t).value,!0)},100)}onValueUpdateGL(e,t,i){if(""===e||null==e)this.formDataGL.controls[t].get("value").setValue(e,{emitEvent:!1});else if(5!=i.fieldId||i.allowTimeSelection)10==i.fieldId&&i.addPhoneExtension?this.formDataGL.controls[t].get("value").setValue({extension:e.extension,phone:e.phone},{emitEvent:!1}):this.formDataGL.controls[t].get("value").setValue(e,{emitEvent:!1});else{let s=_().local().utcOffset();i.useDateRange?this.formDataGL.controls[t].get("value").setValue({start:_(e.start).add(s,"minutes"),end:_(e.end).add(s,"minutes")},{emitEvent:!1}):this.formDataGL.controls[t].get("value").setValue(_(e).add(s,"minutes"),{emitEvent:!1})}this.resolveValueBasedOnFieldConfiguration(!0,this.formDataGL.controls[t].value),e&&"string"==typeof e&&"\n"!=e&&e.includes("\n")&&this.formDataGL.controls[t].get("value").setErrors(null),i.fieldValue=e,null!=e&&null!=e&&""!==e&&this.validateFromFieldConfiguration(i,e),this.checkExternalApiConfiguration(i),e&&this.checkFieldControlConfiguration(i,e)}onValueUpdateMS(e,t,i,s){if(""===e||null==e)this.formDataMS.controls[t].controls[i].get("value").setValue(e,{emitEvent:!1});else if(5!=s.fieldId||s.allowTimeSelection)10==s.fieldId&&s.addPhoneExtension?this.formDataMS.controls[t].controls[i].get("value").setValue({extension:e.extension,end:e.phone},{emitEvent:!1}):this.formDataMS.controls[t].controls[i].get("value").setValue(e,{emitEvent:!1});else{let l=_().local().utcOffset();s.useDateRange?this.formDataMS.controls[t].controls[i].get("value").setValue({start:_(e.start).add(l,"minutes"),end:_(e.end).add(l,"minutes")},{emitEvent:!1}):this.formDataMS.controls[t].controls[i].get("value").setValue(_(e).add(l,"minutes"),{emitEvent:!1})}this.resolveValueBasedOnFieldConfiguration(!1,this.formDataMS.controls[t].controls[i].value),e&&"string"==typeof e&&"\n"!=e&&e.includes("\n")&&this.formDataMS.controls[t].controls[i].get("value").setErrors(null),s.fieldValue=e,null!=e&&null!=e&&""!==e&&this.validateFromFieldConfiguration(s,e),this.checkExternalApiConfiguration(s),e&&this.checkFieldControlConfiguration(s,e)}validateFromFieldConfiguration(e,t){if(this.fieldConfigurations.length>0){let i=o.filter(this.fieldConfigurations,t=>t.field_id.toString()==e._id.toString());if(i.length>0)for(let s of i)if("validation"==s.configuration_type)for(let i of s.ref_fields)if("generalLayout"==this.selectedLayout){let l=this.formDataGL.value,a=null;for(let e of l)e.id==i.field_id&&(a=e.value);if(a){let l=o.filter(this.fieldsObject,e=>e._id.toString()==i.field_id.toString());if(5==l[0].fieldId&&!l[0].allowTimeSelection){let e=_().local().utcOffset();a=_(a).add(e,"minutes"),a=a._i,a=_(a).toISOString()}if(5==e.fieldId&&!e.allowTimeSelection){let e=_().local().utcOffset();t=_(t).add(e,"minutes"),t=_(t=t._i).toISOString()}if(this.applyOperatorForValidationOnFields(l[0],a,e,t,s),!this.allowedToProceed)break}}else if("multiStepLayout"==this.selectedLayout){let l=this.formDataMS.value;l=o.flatten(l);let a=null;for(let e of l)e.id==i.field_id&&(a=e.value);if(a){let l=o.flatten(this.msFields),r=o.filter(l,e=>e._id.toString()==i.field_id.toString());if(5==r[0].fieldId&&!r[0].allowTimeSelection){let e=_().local().utcOffset();a=_(a).add(e,"minutes"),a=a._i,a=_(a).toISOString()}if(5==e.fieldId&&!e.allowTimeSelection){let e=_().local().utcOffset();t=_(t).add(e,"minutes"),t=_(t=t._i).toISOString()}if(this.applyOperatorForValidationOnFields(r[0],a,e,t,s),!this.allowedToProceed)break}}}}applyOperatorForValidationOnFields(e,t,i,s,l){const a=5==e.fieldId,r=5==i.fieldId;switch((a&&r&&e.allowTimeSelection&&!i.allowTimeSelection||a&&r&&!e.allowTimeSelection&&i.allowTimeSelection||a&&r&&!e.allowTimeSelection&&!i.allowTimeSelection)&&(t=_(t).format("YYYY-MM-DD"),s=_(s).format("YYYY-MM-DD")),l.operator){case"greater than":if(5==e.fieldId){let a=this.getDateCheckPrecision(e,i);_(s).isAfter(t,a)||(this.allowedToProceed=!1,this.showMessage(`${i.fieldName} should be ${l.operator} ${e.fieldName}`))}else s>t||(this.allowedToProceed=!1,this.showMessage(`${i.fieldName} should be ${l.operator} ${e.fieldName}`));break;case"greater than or equal to":if(5==e.fieldId){let a=this.getDateCheckPrecision(e,i);_(s).isSameOrAfter(t,a)||(this.allowedToProceed=!1,this.showMessage(`${i.fieldName} should be ${l.operator} ${e.fieldName}`))}else s>=t||(this.allowedToProceed=!1,this.showMessage(`${i.fieldName} should be ${l.operator} ${e.fieldName}`));break;case"lesser than":if(5==e.fieldId){let a=this.getDateCheckPrecision(e,i);_(s).isBefore(t,a)||(this.allowedToProceed=!1,this.showMessage(`${i.fieldName} should be ${l.operator} ${e.fieldName}`))}else s<t||(this.allowedToProceed=!1,this.showMessage(`${i.fieldName} should be ${l.operator} ${e.fieldName}`));break;case"lesser than or equal to":if(5==e.fieldId){let a=this.getDateCheckPrecision(e,i);_(s).isSameOrBefore(t,a)||(this.allowedToProceed=!1,this.showMessage(`${i.fieldName} should be ${l.operator} ${e.fieldName}`))}else s<=t||(this.allowedToProceed=!1,this.showMessage(`${i.fieldName} should be ${l.operator} ${e.fieldName}`));break;case"equal to":if(5==e.fieldId){let a=this.getDateCheckPrecision(e,i);_(s).isSame(t,a)||(this.allowedToProceed=!1,this.showMessage(`${i.fieldName} should be ${l.operator} ${e.fieldName}`))}else s!=t&&(this.allowedToProceed=!1,this.showMessage(`${i.fieldName} should be ${l.operator} ${e.fieldName}`));break;case"not equal to":if(5==e.fieldId){let a=this.getDateCheckPrecision(e,i);_(s).isSame(t,a)&&(this.allowedToProceed=!1,this.showMessage(`${i.fieldName} should be ${l.operator} ${e.fieldName}`))}else s==t&&(this.allowedToProceed=!1,this.showMessage(`${i.fieldName} should be ${l.operator} ${e.fieldName}`))}}showMessage(e){this.snackBar.open(e,"Dismiss",{duration:2e3})}getDateCheckPrecision(e,t){let i=null;return(e.allowTimeSelection&&!t.allowTimeSelection||!e.allowTimeSelection&&t.allowTimeSelection)&&(i="day"),i}showCustomFieldValidationAlert(e){return Object(s.c)(this,void 0,void 0,(function*(){if(this.allowedToProceed=!0,this.uniqueValueCheckPassed=!0,e){let e=this.formDataGL.value;for(let t of e)if(null!=t.value&&null!=t.value&&""!==t.value){let e=o.filter(this.fieldsObject,e=>e._id.toString()==t.id.toString());if(e.length>0&&(this.validateFromFieldConfiguration(e[0],t.value),yield this.checkForUniqueValues(e[0],t.value),!this.uniqueValueCheckPassed))break}}else{let e=this.formDataMS.value;e=o.flatten(e);let t=o.flatten(this.msFields);for(let i of e)if(null!=i.value&&null!=i.value&&""!==i.value){let e=o.filter(t,e=>e._id.toString()==i.id.toString());if(e.length>0&&(this.validateFromFieldConfiguration(e[0],i.value),yield this.checkForUniqueValues(e[0],i.value),!this.uniqueValueCheckPassed))break}}}))}enableSubmit(){return this.isValueSubmitting=!1,!0}submitEntryData(){return Object(s.c)(this,void 0,void 0,(function*(){if(this.isValueSubmitting=!0,this.restrictEdit)return this.enableSubmit();if(this.formId){if(!(yield this.checkCustomApiValidation().catch(e=>(this.snackBar.open(e,"Dismiss",{duration:2e3}),!1))))return this.enableSubmit();if(this.isSubmitted=!0,this.isValidating=!0,"generalLayout"==this.selectedLayout){if("INVALID"==this.formDataGL.status)return this.showValidationAlert(!0),this.isValidating=!1,this.enableSubmit();if(yield this.showCustomFieldValidationAlert(!0),this.isValidating=!1,!this.allowedToProceed)return this.enableSubmit();if(!this.uniqueValueCheckPassed)return this.enableSubmit();if("edit"==this.mode){let e=this.getWFCallConfig(this.formDataGL.value);e.isStatusAvailable?e.newStatusValue==e.statusField.value?this.isWorkflowPassed=!0:yield this.callInlineEditApi(e):this.isWorkflowPassed=!0}else this.isWorkflowPassed=!0;if(!this.isWorkflowPassed)return this.enableSubmit();"display"==this.mode||"creation"==this.mode?yield this.createFormSubmission(this.formDataGL.value):"edit"==this.mode&&(yield this.updateFormSubmission(this.formDataGL.value))}else{if("INVALID"==this.formDataMS.status)return this.showValidationAlert(!1),this.isValidating=!1,this.enableSubmit();if(yield this.showCustomFieldValidationAlert(!1),this.isValidating=!1,!this.allowedToProceed)return this.enableSubmit();if(!this.uniqueValueCheckPassed)return this.enableSubmit();if("edit"==this.mode){let e=this.getWFCallConfig(this.formDataMS.value);e.isStatusAvailable?e.newStatusValue==e.statusField.value?this.isWorkflowPassed=!0:yield this.callInlineEditApi(e):this.isWorkflowPassed=!0}else this.isWorkflowPassed=!0;if(!this.isWorkflowPassed)return this.enableSubmit();"display"==this.mode||"creation"==this.mode?yield this.createFormSubmission(this.formDataMS.value):"edit"==this.mode&&(yield this.updateFormSubmission(this.formDataMS.value))}this.isSubmitted=!0,this.closeDialog()}else this.snackBar.open("Form is not available in the database","Dismiss",{duration:2e3})}))}showValidationAlert(e){if(e){for(let t of this.formDataGL.controls)if("INVALID"==t.status){let e=this.snackBar.open(`Please enter value for ${t.value.fieldName} field`,"Dismiss",{duration:3e3});return void(null!=this.fieldCustomErrorText[t.value.id]&&e.afterDismissed().subscribe(()=>{this.snackBar.open(this.fieldCustomErrorText[t.value.id],"Dismiss",{duration:2e3})}))}}else for(const[t,i]of this.formDataMS.controls.entries())for(let e of i.controls)if("INVALID"==e.status){let i=this.snackBar.open(`Please enter value for ${e.value.fieldName} field in Step ${t+1}`,"Dismiss",{duration:3e3});return void(null!=this.fieldCustomErrorText[e.value.id]&&i.afterDismissed().subscribe(()=>{this.snackBar.open(this.fieldCustomErrorText[e.value.id],"Dismiss",{duration:2e3})}))}}createFormSubmission(e){this.isValueSubmitting=!0,this.recurrenceConfigForm.get("dateField").value&&this.recurrenceConfigForm.get("dateValueFromField").setValue(this.getFieldValue(this.recurrenceConfigForm.get("dateField").value));let t={appId:this.appId,formId:this.formId,entryForId:this.entryForId,data:e,isLcdpApplication:!!this.lcdpApplicationId,recurrenceConfig:this.recurrenceConfigForm.value,parentRecordId:this.parentRecordId};return this.inData.recordReferenceId&&(t.recordReferenceId=this.inData.recordReferenceId),t.isRecurrenceModified=!0,new Promise((e,i)=>{this._CustomFieldService.createFormSubmission(t).subscribe(t=>Object(s.c)(this,void 0,void 0,(function*(){let i="Form values are filled successfully";if(this.inData.formSubmissionMessage&&(i=this.inData.formSubmissionMessage),this.inData.recordReferenceId&&(i="Response submitted Successfully !"),this.snackBar.open(i,"Dismiss",{duration:2e3}),this.responseData=t.data,this.checkForNewTags(),this.masterForm.data.formEntry=t.data.formEntry||null,this.masterForm.data.formEntry&&this.masterForm.data.formEntry._id&&(this.recordId=this.masterForm.data.formEntry._id),this.uploadFiles(),t.data.formEntry&&(this.mainCollectionSubmissionId=t.data.formEntry._id),null!=this.masterForm.data.formEntry){let e=this.getWFCallConfig(this.formDataMS.value);e.isStatusAvailable&&e.newStatusValue&&(yield this.callInlineEditApi(e,!0))}e(!0)})),e=>{this.isValueSubmitting=!1,console.error(e),this.snackBar.open("Error while Creating form Submission","Dismiss",{duration:4e3}),i(e)})})}updateFormSubmission(e){this.isValueSubmitting=!0,this.recurrenceConfigForm.get("dateField").value&&this.recurrenceConfigForm.get("dateValueFromField").setValue(this.getFieldValue(this.recurrenceConfigForm.get("dateField").value));let t={submission_id:this.submission_id,appId:this._CustomFieldService.appId,formId:this.formId,data:e,entryForId:this.entryForId,isLcdpApplication:!!this.lcdpApplicationId,recurrenceConfig:this.recurrenceConfigForm.value,parentRecordId:this.parentRecordId};return o.isEqual(this.recurrenceConfigBeforeEdit,this.recurrenceConfigForm.value)||(t.isRecurrenceModified=!0),new Promise((e,i)=>{this._CustomFieldService.updateFormSubmission(t).subscribe(t=>{let i="Form values are updated successfully";this.inData.formUpdationMessage&&(i=this.inData.formUpdationMessage),this.inData.recordReferenceId&&(i="Response updated Successfully !"),this.snackBar.open(i,"Dismiss",{duration:2e3}),this.responseData=t.data,this.isUpdateFormCalled=!0,this.enableRecordDisplayInHeader&&this._lcdpService.setHeaderRecordObservable(!0),e(!0)},e=>{this.isValueSubmitting=!1,console.error(e),this.snackBar.open("Error Occured during updation!","Dismiss",{duration:2e3}),i(e)})})}checkForAutogenValue(){let e=!1;if("generalLayout"==this.selectedLayout){for(let t=0;t<this.fieldsObject.length;t++)"2"==this.fieldsObject[t].fieldId&&(this.fieldsObject[t].currentValue=parseInt(this.fieldsObject[t].currentValue+1),e=!0);e&&this._CustomFieldService.updateAutogenValue(this.formId,this.fieldsObject).subscribe(e=>{})}else{for(let t=0;t<this.msFields.length;t++)for(let i=0;i<this.msFields[t].length;i++)"2"==this.msFields[t][i].fieldId&&(this.msFields[t][i].currentValue=parseInt(this.msFields[t][i].currentValue+1),e=!0);e&&this._CustomFieldService.updateAutogenValue(this.formId,this.msFields).subscribe(e=>{})}}checkForNewTags(){let e=[];if("generalLayout"==this.selectedLayout){for(let t=0;t<this.fieldsObject.length;t++)if("14"==this.fieldsObject[t].fieldId){let i=this.formDataGL.get(""+t).value;for(let t in i)e.push(i[t])}}else for(let t=0;t<this.msFields.length;t++)for(let i=0;i<this.msFields[t].length;i++)if("14"==this.msFields[t][i].fieldId){let s=this.formDataMS.controls[t].get(""+i).value;for(let t in s)e.push(s[t])}e=[...new Set(e)],e.length>0&&this._CustomFieldService.createAdminTagsData(e).subscribe(e=>{})}closeDialog(){return Object(s.c)(this,void 0,void 0,(function*(){if(this.isSubmitted){let e={};if(e="generalLayout"==this.selectedLayout?this.formDataGL.value:this.formDataMS.value,e.mData=this.masterForm.data,e.messType="S",e.data=this.responseData,this.matDialogRef.close(e),this.isUpdateFormCalled)if(e.data&&e.data.feedbackFormsToTrigger&&e.data.feedbackFormsToTrigger.length){let t=e.data.feedbackFormsToTrigger[0];yield this.openFeedbackFormDialog(t.formId,null,this.recordId)}else this._lcdpService.setCreateDialogObservable(!0)}else this.matDialogRef.close({messType:"E"})}))}addSubformFields(e,t,i,s=null){if("generalLayout"==this.selectedLayout){const i=this.removeSubformFields(this.fieldsObject,this.fieldsObject[t]._id,null);let s=this.fieldsObject[t].y?this.fieldsObject[t].y:0;if(e&&e.fields&&e.fields instanceof Array&&e.fields.length){const l=this.getMaxRows(e.fields);for(let e=t+1;e<this.fieldsObject.length;e++)this.fieldsObject[e].y>this.fieldsObject[t].y&&(this.fieldsObject[e].y=i?this.fieldsObject[e].y-i:this.fieldsObject[e].y,this.fieldsObject[e].y+=l+1);for(let i of e.fields)i.y=0==i.y&&0==s?1:0==i.y?s+1:0==s?i.y+1:i.y+s+1,i.parentFieldId=this.fieldsObject[t]._id;this.fieldsObject.splice(t+1,0,...e.fields);for(let i=t+1;i<t+1+e.fields.length;i++)this.createFormControlGL(this.fieldsObject[i],i);this.filterApiConfigFields(!0,[],0,t+1,e.fields.length,!0),this.applyDefaultValues(),this.resolveSubscriptions(!0)}else for(let e=t+1;e<this.fieldsObject.length;e++)this.fieldsObject[e].y>this.fieldsObject[t].y&&(this.fieldsObject[e].y=i?this.fieldsObject[e].y-i:this.fieldsObject[e].y);this.options.api.optionsChanged()}else if("multiStepLayout"==this.selectedLayout){const l=this.removeSubformFields(this.msFields[i],this.msFields[i][t]._id,i);let a=this.msFields[i][t].y?this.msFields[i][t].y:0;if(e&&e.fields&&e.fields instanceof Array&&e.fields.length){if(o.includes(this.unauthorizedSteps,i+1))for(let t=0;t<e.fields.length;t++)e.fields[t]&&(e.fields[t].fieldValue="",e.fields[t].isDisabled=!0,e.fields[t].mandatoryField=!1);const s=this.getMaxRows(e.fields);for(let e=t+1;e<this.msFields[i].length;e++)this.msFields[i][e].y>this.msFields[i][t].y&&(this.msFields[i][e].y=l?this.msFields[i][e].y-l:this.msFields[i][e].y,this.msFields[i][e].y+=s+1);for(let l of e.fields)l.y=0==l.y&&0==a?1:0==l.y?a+1:0==a?l.y+1:l.y+a+1,l.parentFieldId=this.msFields[i][t]._id;this.msFields[i].splice(t+1,0,...e.fields);for(let l=t+1;l<t+1+e.fields.length;l++)this.createFormControlMS(i,l,this.msFields[i][l]);this.filterApiConfigFields(!0,[],i,t+1,e.fields.length,!0),this.applyDefaultValues(),this.resolveSubscriptions(!0)}else for(let e=t+1;e<this.msFields[i].length;e++)this.msFields[i][e].y>this.msFields[i][t].y&&(this.msFields[i][e].y=l?this.msFields[i][e].y-l:this.msFields[i][e].y);s?s.api.optionsChanged():this.options.api.optionsChanged()}}removeSubformFields(e,t,i,s=0,l={}){for(let a=e.length-1;a>=0;a--)if(e[a].parentFieldId){let r=null;e[a].parentFieldId==t&&(l.hasOwnProperty(e[a].y)?e[a].rows>l[e[a].y]&&(s-=l[e[a].y],s+=e[a].rows,l[e[a].y]=e[a].rows):(s+=e[a].rows,l[e[a].y]=e[a].rows),r=e[a]._id,e.splice(a,1),null!=i&&null!=i?this.removeFormControlMS(i,a):this.removeFormControlGL(a),r&&(s=this.removeSubformFields(e,r,i,s,l)))}return s}removeFormControlGL(e){this.formDataGL.removeAt(e)}removeFormControlMS(e,t){this.formDataMS.controls[e].removeAt(t)}checkForUniqueValues(e,t){if(e.uniqueField)return new Promise((i,s)=>{this._CustomFieldService.checkForUniqueValues(this.appId,this.formId,e._id,t,this.mainCollectionSubmissionId).pipe(Object(l.a)(this.destroy$)).subscribe(s=>{0==s.err&&s.data&&0==s.data.uniqueField&&(this.uniqueValueCheckPassed=!1,this.showMessage(`${e.fieldName} field has a value ${t} which is not unique`)),i(!0)},e=>{console.error(e),this.snackBar.open("Error checking for unique value","Dismiss",{duration:4e3}),s(e)})})}getMasterRecurrenceFrequency(){this._lcdpService.getMasterRecurrenceFrequency().pipe(Object(l.a)(this.destroy$)).subscribe(e=>{e&&e.data&&e.data.length&&(this.recurrenceFrequency=e.data)})}createFormRecurrenceSubmission(e){return new Promise((t,i)=>{this._CustomFieldService.createFormRecurrenceSubmission(this.appId,this.formId,e).subscribe(e=>{t(!0)},e=>{this.isValueSubmitting=!1,console.error(e),this.snackBar.open("Error while Creating form recurrence Submission","Dismiss",{duration:4e3}),i(e)})})}callWfPluginFunction(e,t,i){return Object(s.c)(this,void 0,void 0,(function*(){let l=[],a=[],r=[],n=[],d=[],u=[],f=[];"generalLayout"==this.masterForm.data.layout?f=this.masterForm.data.fields:"multiStepLayout"==this.masterForm.data.layout&&(f=o.flatten(this.masterForm.data.fields));for(let e of f)"6"==e.fieldId&&(e.chooseFromMasterData||e.isOrgDropdown||e.isCCDropdown)&&((e.masterData&&e.masterData.endsWith("_organization")||e.isOrgDropdown)&&n.push(e.fieldValue),(e.masterData&&e.masterData.endsWith("_cost_center")||e.isCCDropdown)&&r.push(e.fieldValue)),"1"==e.fieldId&&(u.push(e._id),d.push(e.fieldValue));if(r.length&&(l=yield this.getCostCenterMasterList(r)),l&&Array.isArray(l)&&l.length)for(let e of r){let t=o.find(l,{cost_center:e});t&&a.push({costCentre:t.cost_center,costCentreDescription:t.cost_center_description,costCentreType:t.cost_center_type})}let c={applicationId:this.masterForm.data.appId,triggerObjectLevel:1,uniq_record_ref_id:e._id,triggerObjectId:{1:e._id},triggerObjectValue:i,appActivityId:null,appStatusHistoryId:null,workflowId:null,initiatorOid:this.currentUser.oid,costCentreList:a,submission:{record_item:e},workflow_object_filter:{},appDeterminedOrgCodes:this._lcdpService.reformatArrayInput(n),appDeterminedAssociateIds:d,createdByOid:e.created_by,assign_to_fields:u};for(let t of f)t.isWorkflowElement&&(c.workflow_object_filter[t._id]=e[t._id]);yield this._wfPluginService.checkIfWfNeedsToBeTriggered(c).then(l=>Object(s.c)(this,void 0,void 0,(function*(){l.error||(yield this.updateLCDPStatus(l.data,e,t,i))&&(this.isWorkflowPassed=!0)}))).catch(e=>{console.log(e),e.messText&&"string"==typeof e.messText&&this._util.showMessage(e.messText,"Dismiss",3e3)})}))}updateLCDPStatus(e,t,i,l){return Object(s.c)(this,void 0,void 0,(function*(){return new Promise((l,a)=>{null!=e?this._lcdpService.updateLCDPWorkflowData(this.lcdpApplicationId,e,t._id).subscribe(t=>Object(s.c)(this,void 0,void 0,(function*(){"S"==t.messType?(this._util.showMessage(" Workflow Triggered Successfully "+e.wf_header_id,"Dismiss",3e3),l(!0)):(this._util.showMessage("Cannot update "+i.fieldName,"Dismiss",3e3),l(!1))})),e=>{this._util.showMessage(e,"dismiss",3e3),l(!1)}):(this.isWorkflowPassed=!0,l(!0))})}))}getWFCallConfig(e){let t=this.masterForm.data.fields,i={},s={},l=null,a=!1;if("generalLayout"==this.masterForm.data.layout){for(let e of t)if(e.isStatusField){s=e;break}if(s&&s._id)if(this.masterForm.data.result){i=o.find(this.masterForm.data.result,{id:s._id}),i&&i.id&&(a=!0);for(let t of e)t.id==i.id&&(l=t.value)}else a=!0,l=s.fieldValue||null,i={id:s._id,value:s.fieldValue,fieldName:s.fieldName}}else if("multiStepLayout"==this.masterForm.data.layout){for(let e of o.flatten(t))if(e.isStatusField){s=e;break}if(s&&s._id)if(this.masterForm.data.result){i=o.find(o.flatten(this.masterForm.data.result),{id:s._id}),i&&i.id&&(a=!0);for(let t of e)for(let e of t)e.id==i.id&&(l=e.value)}else a=!0,l=s.fieldValue||null,i={id:s._id,value:s.fieldValue,fieldName:s.fieldName}}return{isStatusAvailable:a,statusFieldMaster:s,statusField:i,newStatusValue:l}}getCostCenterMasterList(e=[]){return Object(s.c)(this,void 0,void 0,(function*(){return new Promise((t,i)=>Object(s.c)(this,void 0,void 0,(function*(){let i=[];e&&e.length?this._lcdpService.getCostCenterListDetail(e).pipe(Object(l.a)(this.destroy$)).subscribe(e=>{e.data&&e.data.length&&(i=e.data),t(i)}):t(!0)})))}))}callInlineEditApi(e,t=!1){return Object(s.c)(this,void 0,void 0,(function*(){o.isEmpty(this.masterForm.data.formEntry)?this._util.showMessage("The record is not found in the main collection","Dismiss",3e3):t?yield this.callWfPluginFunction(this.masterForm.data.formEntry,e.statusFieldMaster,e.newStatusValue):yield this._lcdpService.checkIfTaskExisted(this.lcdpApplicationId,this.masterForm.data.formEntry,e.statusFieldMaster.isTaskBasedStatus,e.statusField.id).then(t=>Object(s.c)(this,void 0,void 0,(function*(){t?yield this._lcdpService.checkIfWorkflowExists(this.lcdpApplicationId,this.masterForm.data.formEntry).then(t=>Object(s.c)(this,void 0,void 0,(function*(){t.val?(yield this.callWfPluginFunction(this.masterForm.data.formEntry,e.statusFieldMaster,e.newStatusValue),this.enableRealTimeSLATrigger&&this.checkForSLATrigger(e.statusFieldMaster._id,e.newStatusValue,this.masterForm.data.formEntry)):this._util.showMessage("Workflow In-Progress! Status cannot be changed!"+t.data,"Dismiss",3e3)}))):this._util.showMessage(e.statusFieldMaster.fieldName+" cannot be changed! Kindly complete the Subtask!","Dismiss",5e3)})))}))}filterApiConfigFields(e=!1,t=[],i=0,s=0,l=0,a=!1){let r=[];const n=t=>{if(t.valueFromApi){let i=t.apiConfig,s=!0;if(i.apiParams&&i.apiParams.length){for(let e of i.apiParams)if(e.valueFromField){let t=this.getFieldValue(e.fieldValue);if(!e.allowNullValuesToApi&&null==t){s=!1;break}}}else e||(s=!1);s&&r.push(t)}};if(e){if("generalLayout"==this.selectedLayout){const e=a&&l?l:this.fieldsObject.length;for(let t=s;t<s+e;t++)n(this.fieldsObject[t])}else if("multiStepLayout"==this.selectedLayout)for(let o=i;o<this.msFields.length;o++){const e=a&&l?l:this.msFields[o].length;if(!a||o==i)for(let t=s;t<s+e;t++)n(this.msFields[o][t])}}else for(let o of t)n(o);this.patchExternalApiParams(r,e)}getFieldValue(e){let t=null;if("generalLayout"==this.selectedLayout){for(let i of this.formDataGL.value)if(i.id==e&&!this._CustomFieldService.checkIfEmpty(i.value)){t=i.value;break}}else if("multiStepLayout"==this.selectedLayout)for(let i of this.formDataMS.value)for(let s of i)if(s.id==e&&!this._CustomFieldService.checkIfEmpty(s.value)){t=s.value;break}return t}patchExternalApiConfigInField(e,t){if("generalLayout"==this.selectedLayout)for(let i of this.fieldsObject)i._id.toString()==e.toString()&&(i.externalApiConfig=t);else for(let i=0;i<this.msFields.length;i++)for(let s=0;s<this.msFields[i].length;s++)this.msFields[i][s]._id.toString()==e.toString()&&(this.msFields[i][s].externalApiConfig=t)}patchExternalApiParams(e,t=!1){for(let i of e){let e=Object.create(i.apiConfig),s=e,l={};if(e.apiParams&&e.apiParams.length)for(let t of e.apiParams){let e,i=t.fieldName;e=t.valueFromField?this.getFieldValue(t.fieldValue):t.fieldValue,this._CustomFieldService.checkIfEmpty(e)||(l[i]=e)}s.apiParams=l,t&&(s.initialLoad=!0),this.patchExternalApiConfigInField(i._id,s)}}checkExternalApiConfiguration(e){let t=[];if("generalLayout"==this.selectedLayout){for(let i of this.fieldsObject)if(i.valueFromApi){let s=i.apiConfig.apiParams;for(let l of s)l.valueFromField&&l.fieldValue==e._id&&t.push(i)}}else if("multiStepLayout"==this.selectedLayout)for(let i of this.msFields)for(let s of i)if(s.valueFromApi){let i=s.apiConfig.apiParams;for(let l of i)l.valueFromField&&l.fieldValue==e._id&&t.push(s)}this.filterApiConfigFields(!1,t)}onValidationsUpdate(e,t,i){let s;"generalLayout"==this.selectedLayout?s=this.formDataGL.controls[t.i]:"multiStepLayout"==this.selectedLayout&&(s=this.formDataMS.controls[t.i].controls[t.j]),"mandatory"==e?(s.get("value").setValidators(r.H.required),s.get("value").updateValueAndValidity()):"nonMandatory"==e&&(s.get("value").clearValidators(),s.get("value").updateValueAndValidity())}filterFieldControlConfiguration(){if("generalLayout"==this.selectedLayout){for(let[e,t]of Object.entries(this.fieldsObject))if(t.allowFieldControl){let i=t.fieldControlMapping;for(let t of i){let i=this.getFieldValue(t.fieldId);if(Array.isArray(i)){if(Array.isArray(t.fieldValue)&&0==o.difference(i,t.fieldValue).length){this.fieldsObject[e].fieldControlConfig={action:"update",fieldConfig:t};break}}else if(Array.isArray(t.fieldValue)){if(t.fieldValue.includes(i)){this.fieldsObject[e].fieldControlConfig={action:"update",fieldConfig:t};break}}else if(t.fieldValue==i){this.fieldsObject[e].fieldControlConfig={action:"update",fieldConfig:t};break}}}}else if("multiStepLayout"==this.selectedLayout)for(let[e,t]of Object.entries(this.msFields))for(let[i,s]of Object.entries(t))if(s.allowFieldControl){let t=s.fieldControlMapping;for(let s of t){let t=this.getFieldValue(s.fieldId);if(Array.isArray(t)){if(Array.isArray(s.fieldValue)&&0==o.difference(t,s.fieldValue).length){this.msFields[e][i].fieldControlConfig={action:"update",fieldConfig:s};break}}else if(Array.isArray(s.fieldValue)){if(s.fieldValue.includes(t)){this.msFields[e][i].fieldControlConfig={action:"update",fieldConfig:s};break}}else if(s.fieldValue==t){this.msFields[e][i].fieldControlConfig={action:"update",fieldConfig:s};break}}}}checkFieldControlConfiguration(e,t){if("generalLayout"==this.selectedLayout){for(let[i,s]of Object.entries(this.fieldsObject))if(s.allowFieldControl){let l=s.fieldControlMapping;for(let s of l)s.fieldId==e._id&&(this.fieldsObject[i].fieldControlConfig=Array.isArray(t)?Array.isArray(s.fieldValue)&&0==o.difference(t,s.fieldValue).length?{action:"update",fieldConfig:s}:{action:"restore",fieldConfig:s}:Array.isArray(s.fieldValue)?s.fieldValue.includes(t)?{action:"update",fieldConfig:s}:{action:"restore",fieldConfig:s}:s.fieldValue==t?{action:"update",fieldConfig:s}:{action:"restore",fieldConfig:s})}}else if("multiStepLayout"==this.selectedLayout)for(let[i,s]of Object.entries(this.msFields))for(let[l,a]of Object.entries(s))if(a.allowFieldControl){let s=a.fieldControlMapping;for(let a of s)a.fieldId==e._id&&(this.msFields[i][l].fieldControlConfig=Array.isArray(t)?Array.isArray(a.fieldValue)&&0==o.difference(t,a.fieldValue).length?{action:"update",fieldConfig:a}:{action:"restore",fieldConfig:a}:Array.isArray(a.fieldValue)?a.fieldValue.includes(t)?{action:"update",fieldConfig:a}:{action:"restore",fieldConfig:a}:a.fieldValue==t?{action:"update",fieldConfig:a}:{action:"restore",fieldConfig:a})}}uploadFiles(){this.recordId&&this.lcdpApplicationId&&(this.uploader.setOptions({headers:[{name:"lcdp-application-id",value:this.lcdpApplicationId},{name:"record-id",value:this.recordId}]}),this.uploader.queue.length>0&&this.uploader.uploadAll())}removeFile(e){this.fileList.splice(e,1),this.uploader.queue.splice(e,1)}openFeedbackFormDialog(e,t,l=null){return Object(s.c)(this,void 0,void 0,(function*(){const{CustomFormModalComponent:a}=yield Promise.resolve().then(i.bind(null,"mGD6"));this.dialog.open(a,{height:"75%",width:"75%",data:{lcdpApplicationId:this.lcdpApplicationId,formId:e,isEdit:!0,entryForId:null,recordReferenceId:l},disableClose:!0}).afterClosed().subscribe(e=>Object(s.c)(this,void 0,void 0,(function*(){this._lcdpService.setCreateDialogObservable(!0),t&&(this.$router.navigate(["../"],{relativeTo:t}),e&&"S"==e.messType?this._lcdpService.setCreationObservable(!0):this.$router.navigate(["../"],{relativeTo:t}))})))}))}checkCustomApiValidation(){return Object(s.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>Object(s.c)(this,void 0,void 0,(function*(){if(this.enableCustomApiValidation)if(this.apiConfigForCustomApiValidation)if(this.apiConfigForCustomApiValidation.triggerForAllValues)yield this.callExternalApi().catch(e=>{t(e)}),e(!0);else if(this.apiConfigForCustomApiValidation.triggerForSelectiveValues){let l=!0;if(this.apiConfigForCustomApiValidation.fieldDependencies&&this.apiConfigForCustomApiValidation.fieldDependencies.length)for(let e of this.apiConfigForCustomApiValidation.fieldDependencies)if(i=e.fieldValue,s=this.getFieldValue(e.fieldId),!(Array.isArray(i)?Array.isArray(s)?0==o.difference(s,i).length:i.includes(s):i==s)){l=!1;break}l?(yield this.callExternalApi().catch(e=>t(e)),e(!0)):e(!0)}else t("Api validation Config is invalid");else t("Api config not found");else e(!0);var i,s})))}))}callExternalApi(){return Object(s.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>{let i=Object.create(this.apiConfigForCustomApiValidation),s=i,a={};if(i.apiParams&&i.apiParams.length)for(let l of i.apiParams){let e,t=l.fieldName;e=l.valueFromField?this.getFieldValue(l.fieldValue):l.fieldValue,e&&(a[t]=e)}a.formId=this.formId,a.recordId=this.recordId,s.apiParams=a,this._CustomFieldService.callExternalApi(s).pipe(Object(l.a)(this.destroy$)).subscribe(s=>{if(s.data&&i.responseMapping&&i.responseMapping.keyField&&s.data.hasOwnProperty(i.responseMapping.keyField)&&i.responseMapping.fieldValue&&s.data[i.responseMapping.keyField]==i.responseMapping.fieldValue)e(!0);else{let e="The validation API response does not satisfy the response mapping condition";s.data&&s.data.hasOwnProperty("messText")&&s.data.messText&&(e=s.data.messText),t(e)}},e=>{t(JSON.stringify(JSON.parse(e.error)))})})}))}getEditorHeight(e,t,i,s=null,l=null){if(e&&t){t.initialRows=t.rows,t.rows=Math.ceil(e/this.fixedRowHeight);const a=t.rows-t.initialRows;if(null!=s){for(let e=i+1;e<this.msFields[s].length;e++)this.msFields[s][e].y>t.y&&(this.msFields[s][e].y+=a);l?l.api.optionsChanged():this.options.api.optionsChanged()}else{for(let e=i+1;e<this.fieldsObject.length;e++)this.fieldsObject[e].y>t.y&&(this.fieldsObject[e].y+=a);this.options.api.optionsChanged()}}}onCustomErrorTextUpdate(e,t){t._id&&(this.fieldCustomErrorText[t._id]=e,null!=e&&this.errorTextQueue.push(new r.j(e)))}ngOnDestroy(){this._CustomFieldService.clearData(),this.destroy$.next(!0),this.destroy$.unsubscribe(),this.valueChangeSubscription&&this.valueChangeSubscription.unsubscribe(),this.fieldCustomErrorTextSubscription&&this.fieldCustomErrorTextSubscription.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(w["\u0275\u0275directiveInject"](x.a),w["\u0275\u0275directiveInject"](n.h),w["\u0275\u0275directiveInject"](n.a),w["\u0275\u0275directiveInject"](r.i),w["\u0275\u0275directiveInject"](I.a),w["\u0275\u0275directiveInject"](V.a),w["\u0275\u0275directiveInject"](D.a),w["\u0275\u0275directiveInject"](O.a),w["\u0275\u0275directiveInject"](A.a),w["\u0275\u0275directiveInject"](E.a),w["\u0275\u0275directiveInject"](n.b),w["\u0275\u0275directiveInject"](k.g))},e.\u0275cmp=w["\u0275\u0275defineComponent"]({type:e,selectors:[["app-custom-form-modal"]],viewQuery:function(e,t){if(1&e&&w["\u0275\u0275viewQuery"](f.a,!0),2&e){let e;w["\u0275\u0275queryRefresh"](e=w["\u0275\u0275loadQuery"]())&&(t.popover=e.first)}},features:[w["\u0275\u0275ProvidersFeature"]([])],decls:2,vars:2,consts:[["style","background : #fff;","class","preview-styles",4,"ngIf"],["class","row w-100 justify-content-center mt-5",4,"ngIf"],[1,"preview-styles",2,"background","#fff"],[1,"p-2","mx-3","d-flex","justify-content-between","align-items-center",2,"color","#cf0000","font-weight","500","font-size","15px"],[1,"d-flex","justify-content-between","align-items-center",2,"flex","0.9"],["mat-button","","class","config-button",3,"satPopoverAnchor","click",4,"ngIf"],["horizontalAlign","center","verticalAlign","below","hasBackdrop",""],["uploadConfig",""],[1,"config-wrapper","d-flex","flex-column","align-items-center"],[1,"row","pb-2"],["mat-raised-button","","matTooltip","Add new file",1,"mt-2",2,"color","white","background","#cf0001",3,"click"],["hidden","","type","file","ng2FileSelect","",3,"uploader","accept"],["fileInput",""],["class","row d-flex flex-column justify-content-between align-items-center",4,"ngIf"],["class","row",4,"ngIf"],["recurrenceConfig",""],[1,"config-wrapper",3,"formGroup"],["formControlName","isRecurring"],["class","d-flex flex-column justify-content-between align-items-center",4,"ngIf"],[4,"ngIf"],["mat-icon-button","",3,"click"],[2,"color","#908b8b"],[1,"mx-3"],["mat-button","",1,"config-button",3,"satPopoverAnchor","click"],[1,"d-flex","flex-row","justify-content-between","align-items-center"],[1,"pl-2"],[1,"row","d-flex","flex-column","justify-content-between","align-items-center"],[2,"padding-top","3rem !important","display","grid","place-items","center"],["src","https://assets.kebs.app/images/create_cta.png","alt","",2,"width","50%"],[1,"pb-3","font-weight-bold"],[1,"row"],["class","col-12",4,"ngFor","ngForOf"],[1,"col-12"],[1,"row","pt-3"],[1,"col-10","d-flex"],[1,"col-1"],["mat-icon-button","","matTooltip","Remove Attachment",1,"ml-auto","close-button","mt-1",3,"click"],[1,"close-Icon"],[1,"d-flex","flex-column","justify-content-between","align-items-center"],[2,"padding-top","1rem !important"],[1,"heading-class"],[1,"heading-icon"],[1,"heading-text"],[2,"padding","10px"],["placeholder","Select frequency","formControlName","frequency",3,"list"],["appearance","outline",2,"font-size","13px","width","100%"],["type","number","matInput","","min","1","formControlName","occurence"],["matInput","","datePickerFormat","'DD-MM-YY'","formControlName","endDate",3,"min","matDatepicker"],["matSuffix","",3,"for"],["picker",""],["style","padding: 10px",4,"ngIf"],["formControlName","defaultStatus",3,"apiURI","label","isMultiple","fieldName","fieldValue","apiParams","needsAPICall","fieldIcon"],["placeholder","Date field","formControlName","dateField",3,"list"],["formControlName","assignToField",3,"label","isMultiple","fieldName","fieldValue","needsAPICall","itemsList","fieldIcon"],["formControlName","assignToList",3,"apiURI","label","isMultiple","fieldName","fieldValue","apiParams","needsAPICall","fieldIcon"],[1,"categories","mt-2",2,"height","80vh"],[2,"background-color","white",3,"options"],[3,"item",4,"ngFor","ngForOf"],[1,"mb-5","mr-5","mt-2"],["mat-icon-button","","class","done-icon float-right ml-2",3,"disabled","click",4,"ngIf"],[3,"item"],[3,"ngClass","field","id","isPreview","patchValue","clearFieldData","apiParams","isEditMode","externalApiConfig","fieldControlConfig","valueUpdate","validationsUpdate","subformFields","editorHeight","customErrorTextUpdate"],["mat-icon-button","",1,"done-icon","float-right","ml-2",3,"disabled","click"],[3,"stepControl",4,"ngFor","ngForOf"],[3,"stepControl"],["matStepLabel",""],[1,"categories","mt-2",2,"height","68vh","overflow-x","hidden"],[2,"margin-bottom","35px"],["mat-icon-button","","matStepperNext","","class","ml-2 float-right nav-icon-btn",4,"ngIf"],["mat-icon-button","","matStepperPrevious","","class","float-right nav-icon-btn",4,"ngIf"],[3,"ngClass","field","id","isPreview","patchValue","apiParams","clearFieldData","isEditMode","externalApiConfig","fieldControlConfig","valueUpdate","validationsUpdate","subformFields","editorHeight","customErrorTextUpdate"],["mat-icon-button","","matStepperNext","",1,"ml-2","float-right","nav-icon-btn"],["mat-icon-button","","matStepperPrevious","",1,"float-right","nav-icon-btn"],[1,"row","w-100","justify-content-center","mt-5"],[2,"color","#cf0000",3,"diameter"]],template:function(e,t){1&e&&(w["\u0275\u0275template"](0,se,32,13,"div",0),w["\u0275\u0275template"](1,le,2,1,"div",1)),2&e&&(w["\u0275\u0275property"]("ngIf",!t.isPageLoading),w["\u0275\u0275advance"](1),w["\u0275\u0275property"]("ngIf",t.isPageLoading))},directives:[h.NgIf,f.a,g.a,c.b,r.w,r.n,C.a,r.v,r.l,v.a,p.a,f.b,h.NgForOf,L.a,F.c,F.g,r.A,y.b,r.e,S.g,S.i,F.i,S.f,M.a,u.c,u.d,j.a,h.NgClass,m.a,m.b,m.c,m.g,m.h,b.c],styles:[".preview-styles[_ngcontent-%COMP%]   .gl-wrapper[_ngcontent-%COMP%]{display:grid;grid-template-rows:repeat(6,minmax(55px,1fr));grid-column-gap:80px;grid-row-gap:10px;grid-auto-flow:column;grid-auto-columns:250px;overflow-x:auto}.preview-styles[_ngcontent-%COMP%]   .done-icon[_ngcontent-%COMP%]{height:30px;width:30px;line-height:8px;border-radius:50%;background:#cf0001;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.preview-styles[_ngcontent-%COMP%]   .done-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:20px;height:20px;width:20px;line-height:20px;color:#f5f5f5}.preview-styles[_ngcontent-%COMP%]     .mat-horizontal-content-container{overflow-x:auto!important}.preview-styles[_ngcontent-%COMP%]   .categories[_ngcontent-%COMP%]{display:flex;flex-direction:column;height:426px;flex-wrap:wrap;overflow-x:auto}.preview-styles[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]{min-height:55px;margin-right:5rem;width:250px}.preview-styles[_ngcontent-%COMP%]   .prevent-click[_ngcontent-%COMP%]{pointer-events:none!important}.preview-styles[_ngcontent-%COMP%]   .nav-icon-btn[_ngcontent-%COMP%]{height:30px;width:30px;line-height:8px;border-radius:50%;background:#cf0001;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.preview-styles[_ngcontent-%COMP%]   .nav-icon-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:20px;height:20px;width:20px;line-height:20px;color:#f5f5f5}.config-wrapper[_ngcontent-%COMP%]{display:block;padding:15px;background:#fff;width:350px;box-shadow:3px 3px 5px 3px #ccc;right:30px}.config-button[_ngcontent-%COMP%]{box-shadow:1px 5px 9px 1px #ccc}.heading-icon[_ngcontent-%COMP%]{padding-left:17px;padding-top:4px;font-size:18px;vertical-align:bottom;color:#f5f5f5}.heading-text[_ngcontent-%COMP%]{padding-left:21px;font-size:13px;color:#f5f5f5}.heading-class[_ngcontent-%COMP%]{height:28px;background:#2d2d2d;border-radius:16px;padding-right:2rem;margin:10px}"]}),e})()},mbIT:function(e,t,i){"use strict";t.isArray=function(){return Array.isArray||function(e){return e&&"number"==typeof e.length}}()},pshJ:function(e,t,i){"use strict";t.isFunction=function(e){return"function"==typeof e}},tKbL:function(e,t,i){"use strict";i.d(t,"a",(function(){return n}));var s=i("xG9w"),l=i("fXoL"),a=i("tk/3"),r=i("BVzC");let n=(()=>{class e{constructor(e,t){this.http=e,this._ErrorService=t}checkIfWfNeedsToBeTriggered(e){return new Promise((t,i)=>{this.http.post("/api/wf/getApplicationWfConfigDetails",{application_id:e.applicationId}).subscribe(l=>{if(!("S"==l.messType&&l.data.length>0))return t({error:!1,messText:"Workflow Config does not exist for application !",data:null});{let a=s.filter(l.data,t=>t.trigger_object_value instanceof Array?t.trigger_object_level==e.triggerObjectLevel&&s.contains(t.trigger_object_value,e.triggerObjectValue):t.trigger_object_level==e.triggerObjectLevel&&t.trigger_object_value==e.triggerObjectValue);if(!(a&&a.length>0))return t({error:!1,messText:"Workflow Config does not exist !",data:null});this.triggerWfBasedOnConfig(a[0],e).then(e=>t({error:!1,messText:"Workflow created Successfully !",data:e})).catch(e=>(this._ErrorService.userErrorAlert(e.error?e.error.errorCode:e.errorCode,"Error creating workflow !",e.error?e.error.errMessage:e.errMessage),i({error:!0,messText:e.messText,data:null})))}},e=>(this._ErrorService.userErrorAlert(e.error?e.error.errorCode:e.errorCode,"Error getting application workflow configs !",e.error?e.error.errMessage:e.errMessage),i({error:!0,messText:e,data:null})))})}triggerWfBasedOnConfig(e,t){return new Promise((i,s)=>{this.http.post("/api/wf/triggerWfBasedOnConfig",{current_wf_config:e,app_details:t}).subscribe(e=>"S"==e.messType&&e.data?i(e.data):s(e),e=>s(e))})}getWfApproverDetails(e,t){return this.http.post("/api/wf/getWfApproverDetails",{wf_header_id:t,wf_plugin_id:e})}updateWfBasedOnConfig(e,t,i,s,l,a){return this.http.post("/api/wf/updateWfBasedOnConfig",{wf_update_action:t,wf_plugin_id:e,approver_oid:i,approver_comments:s,isAdmin:l,adminOId:a})}}return e.\u0275fac=function(t){return new(t||e)(l["\u0275\u0275inject"](a.c),l["\u0275\u0275inject"](r.a))},e.\u0275prov=l["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},"zB/H":function(e,t,i){"use strict";var s=i("mbIT"),l=i("GMZp"),a=i("pshJ"),r=i("LBXl");function n(e){return e.reduce((function(e,t){return e.concat(t instanceof r.UnsubscriptionError?t.errors:t)}),[])}t.Subscription=function(){function e(e){this.closed=!1,this._parentOrParents=null,this._subscriptions=null,e&&(this._ctorUnsubscribe=!0,this._unsubscribe=e)}var t;return e.prototype.unsubscribe=function(){var t;if(!this.closed){var i=this,o=i._parentOrParents,d=i._ctorUnsubscribe,u=i._unsubscribe,f=i._subscriptions;if(this.closed=!0,this._parentOrParents=null,this._subscriptions=null,o instanceof e)o.remove(this);else if(null!==o)for(var c=0;c<o.length;++c)o[c].remove(this);if(a.isFunction(u)){d&&(this._unsubscribe=void 0);try{u.call(this)}catch(m){t=m instanceof r.UnsubscriptionError?n(m.errors):[m]}}if(s.isArray(f)){c=-1;for(var h=f.length;++c<h;){var p=f[c];if(l.isObject(p))try{p.unsubscribe()}catch(m){t=t||[],m instanceof r.UnsubscriptionError?t=t.concat(n(m.errors)):t.push(m)}}}if(t)throw new r.UnsubscriptionError(t)}},e.prototype.add=function(t){var i=t;if(!t)return e.EMPTY;switch(typeof t){case"function":i=new e(t);case"object":if(i===this||i.closed||"function"!=typeof i.unsubscribe)return i;if(this.closed)return i.unsubscribe(),i;if(!(i instanceof e)){var s=i;(i=new e)._subscriptions=[s]}break;default:throw new Error("unrecognized teardown "+t+" added to Subscription.")}var l=i._parentOrParents;if(null===l)i._parentOrParents=this;else if(l instanceof e){if(l===this)return i;i._parentOrParents=[l,this]}else{if(-1!==l.indexOf(this))return i;l.push(this)}var a=this._subscriptions;return null===a?this._subscriptions=[i]:a.push(i),i},e.prototype.remove=function(e){var t=this._subscriptions;if(t){var i=t.indexOf(e);-1!==i&&t.splice(i,1)}},e.EMPTY=((t=new e).closed=!0,t),e}()}}]);