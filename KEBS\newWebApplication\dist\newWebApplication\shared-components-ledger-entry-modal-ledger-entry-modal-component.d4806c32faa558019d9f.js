(window.webpackJsonp=window.webpackJsonp||[]).push([[891,535,631,634,858],{"7W/N":function(e,t,n){"use strict";n.r(t),n.d(t,"LedgerEntryModalComponent",(function(){return vt})),n.d(t,"LedgerEntryModalModule",(function(){return ft}));var i=n("mrSG"),a=n("fXoL"),o=n("xG9w"),r=n("3CC3"),l=n("ofXK"),s=n("0IaG"),d=n("kmnG"),c=n("qFsG"),m=n("3Pt+"),p=n("jaxi"),u=n("iadO"),y=n("FKr1"),_=n("lVl8"),h=n("NFeN"),g=n("bTqV"),x=n("Xi0T"),v=n("Xa2L"),f=n("Qu3c"),E=n("xHqg"),b=n("XNiG"),S=n("1G5W"),D=n("Kj3r"),C=n("2ChS"),T=n("bSwM"),P=n("a1r6"),A=n("LcQX"),k=n("TmG/");const I=["stepper"];function w(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"div",3),a["\u0275\u0275element"](1,"mat-spinner",4),a["\u0275\u0275elementEnd"]())}function M(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"div",26),a["\u0275\u0275elementStart"](1,"button",27),a["\u0275\u0275listener"]("click",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"](2).closeLedgerEntry("close")})),a["\u0275\u0275elementStart"](2,"mat-icon",28),a["\u0275\u0275text"](3,"close"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()}}function O(e,t){1&e&&a["\u0275\u0275text"](0,"Expense Entries")}function V(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"th",37),a["\u0275\u0275text"](1,"Nature of Payment"),a["\u0275\u0275elementEnd"]())}function N(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"th",37),a["\u0275\u0275text"](1,"Taxability Type"),a["\u0275\u0275elementEnd"]())}function L(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"th",37),a["\u0275\u0275text"](1,"Taxabaility Rate"),a["\u0275\u0275elementEnd"]())}function G(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"th",37),a["\u0275\u0275text"](1,"HSN/SAC"),a["\u0275\u0275elementEnd"]())}function F(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"td"),a["\u0275\u0275text"](1),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"]().$implicit;a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate1"](" ",e.tds_nature_gl_name?e.tds_nature_gl_name:"-"," ")}}function q(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"td"),a["\u0275\u0275text"](1),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"]().$implicit;a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate1"](" ",e.taxability_type_name?e.taxability_type_name:"-"," ")}}function j(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"td"),a["\u0275\u0275text"](1),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"]().$implicit;a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate1"](" ",e.taxability_type_rate?e.taxability_type_rate:"-"," ")}}function R(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"td"),a["\u0275\u0275text"](1),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"]().$implicit;a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate1"](" ",e.hsn_sac_value?e.hsn_sac_value:"-"," ")}}function B(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"tr"),a["\u0275\u0275elementStart"](1,"th",39),a["\u0275\u0275text"](2),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](3,"td"),a["\u0275\u0275text"](4),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](5,"td"),a["\u0275\u0275text"](6),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](7,"td"),a["\u0275\u0275text"](8),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](9,"td"),a["\u0275\u0275text"](10),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](11,"td"),a["\u0275\u0275text"](12),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](13,F,2,1,"td",17),a["\u0275\u0275template"](14,q,2,1,"td",17),a["\u0275\u0275template"](15,j,2,1,"td",17),a["\u0275\u0275template"](16,R,2,1,"td",17),a["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=t.index,i=a["\u0275\u0275nextContext"](5);a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate"](n+1),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate"](null==e.cost_center?null:e.cost_center.name),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate"](null==e.subGroupDetail?null:e.subGroupDetail.name),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate"](null==e.sourceEntityDetail?null:e.sourceEntityDetail.name),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate1"](" ",null==e.sourceEntityDetail?null:e.sourceEntityDetail.tally_entity_name," "),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate1"](" ",e.description?e.description:"-"," "),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",i.tdsNatureOnLedgerEntry),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",i.taxabilityTypeonLedgerEntry),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",i.taxabilityTypeonLedgerEntry),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",i.HSNSAConLedgerEntry)}}function J(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div"),a["\u0275\u0275elementStart"](1,"div",33),a["\u0275\u0275elementStart"](2,"div",34),a["\u0275\u0275elementStart"](3,"strong"),a["\u0275\u0275text"](4),a["\u0275\u0275elementEnd"](),a["\u0275\u0275text"](5),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](6,"div",33),a["\u0275\u0275elementStart"](7,"div",34),a["\u0275\u0275elementStart"](8,"div",35),a["\u0275\u0275elementStart"](9,"table",36),a["\u0275\u0275elementStart"](10,"thead"),a["\u0275\u0275elementStart"](11,"tr"),a["\u0275\u0275elementStart"](12,"th",37),a["\u0275\u0275text"](13,"#"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](14,"th",37),a["\u0275\u0275text"](15,"Cost center"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](16,"th",37),a["\u0275\u0275text"](17,"Subgroup"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](18,"th",37),a["\u0275\u0275text"](19,"Source Entity"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](20,"th",37),a["\u0275\u0275text"](21,"Entity Name"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](22,"th",37),a["\u0275\u0275text"](23,"Description"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](24,V,2,0,"th",38),a["\u0275\u0275template"](25,N,2,0,"th",38),a["\u0275\u0275template"](26,L,2,0,"th",38),a["\u0275\u0275template"](27,G,2,0,"th",38),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](28,"tbody"),a["\u0275\u0275template"](29,B,17,10,"tr",32),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=a["\u0275\u0275nextContext"](4);a["\u0275\u0275advance"](4),a["\u0275\u0275textInterpolate"](e.milestone_name),a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate1"]("\xa0\xa0(",e.milestone_amount,") "),a["\u0275\u0275advance"](19),a["\u0275\u0275property"]("ngIf",n.tdsNatureOnLedgerEntry),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",n.taxabilityTypeonLedgerEntry),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",n.taxabilityTypeonLedgerEntry),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",n.HSNSAConLedgerEntry),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("ngForOf",e.milestone_entries)}}function $(e,t){if(1&e&&(a["\u0275\u0275elementContainerStart"](0),a["\u0275\u0275template"](1,J,30,7,"div",32),a["\u0275\u0275elementContainerEnd"]()),2&e){const e=a["\u0275\u0275nextContext"](3);a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngForOf",null==e.inPrefilledJson?null:e.inPrefilledJson.StatutoryDetails.expenseEntry)}}function H(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"th",37),a["\u0275\u0275text"](1,"Nature of Payment"),a["\u0275\u0275elementEnd"]())}function W(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"th",37),a["\u0275\u0275text"](1,"Taxability Type"),a["\u0275\u0275elementEnd"]())}function z(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"th",37),a["\u0275\u0275text"](1,"Taxabaility Rate"),a["\u0275\u0275elementEnd"]())}function U(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"th",37),a["\u0275\u0275text"](1,"HSN/SAC"),a["\u0275\u0275elementEnd"]())}function K(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"td"),a["\u0275\u0275text"](1),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"]().$implicit;a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate1"](" ",e.tds_nature_gl_name?e.tds_nature_gl_name:"-"," ")}}function X(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"td"),a["\u0275\u0275text"](1),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"]().$implicit;a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate1"](" ",e.taxability_type_name?e.taxability_type_name:"-"," ")}}function Y(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"td"),a["\u0275\u0275text"](1),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"]().$implicit;a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate1"](" ",e.taxability_type_rate?e.taxability_type_rate:"-"," ")}}function Q(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"td"),a["\u0275\u0275text"](1),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"]().$implicit;a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate1"](" ",e.hsn_sac_value?e.hsn_sac_value:"-"," ")}}function Z(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"tr"),a["\u0275\u0275elementStart"](1,"th",39),a["\u0275\u0275text"](2),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](3,"td"),a["\u0275\u0275text"](4),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](5,"td"),a["\u0275\u0275text"](6),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](7,"td"),a["\u0275\u0275text"](8),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](9,"td"),a["\u0275\u0275text"](10),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](11,"td"),a["\u0275\u0275text"](12),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](13,"td"),a["\u0275\u0275text"](14),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](15,K,2,1,"td",17),a["\u0275\u0275template"](16,X,2,1,"td",17),a["\u0275\u0275template"](17,Y,2,1,"td",17),a["\u0275\u0275template"](18,Q,2,1,"td",17),a["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=t.index,i=a["\u0275\u0275nextContext"](5);a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate"](n+1),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate"](null==e.cost_center?null:e.cost_center.name),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate"](e.cc_amount),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate1"](" ",e.gl_account_name," "),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate1"](" ",e.sourceEntityDetail.name," "),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate1"](" ",e.sourceEntityDetail.tally_entity_name," "),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate1"](" ",e.description?e.description:"-"," "),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",i.tdsNatureOnLedgerEntry),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",i.taxabilityTypeonLedgerEntry),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",i.taxabilityTypeonLedgerEntry),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",i.HSNSAConLedgerEntry)}}function ee(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div"),a["\u0275\u0275elementStart"](1,"div",33),a["\u0275\u0275elementStart"](2,"div",34),a["\u0275\u0275elementStart"](3,"strong"),a["\u0275\u0275text"](4),a["\u0275\u0275elementEnd"](),a["\u0275\u0275text"](5),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](6,"div",33),a["\u0275\u0275elementStart"](7,"div",34),a["\u0275\u0275elementStart"](8,"div",35),a["\u0275\u0275elementStart"](9,"table",36),a["\u0275\u0275elementStart"](10,"thead"),a["\u0275\u0275elementStart"](11,"tr"),a["\u0275\u0275elementStart"](12,"th",37),a["\u0275\u0275text"](13,"#"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](14,"th",37),a["\u0275\u0275text"](15,"Cost center"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](16,"th",37),a["\u0275\u0275text"](17,"Amount"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](18,"th",37),a["\u0275\u0275text"](19,"GL Account"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](20,"th",37),a["\u0275\u0275text"](21,"Source Entity"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](22,"th",37),a["\u0275\u0275text"](23,"Entity Name"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](24,"th",37),a["\u0275\u0275text"](25,"Description"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](26,H,2,0,"th",38),a["\u0275\u0275template"](27,W,2,0,"th",38),a["\u0275\u0275template"](28,z,2,0,"th",38),a["\u0275\u0275template"](29,U,2,0,"th",38),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](30,"tbody"),a["\u0275\u0275template"](31,Z,19,11,"tr",32),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=a["\u0275\u0275nextContext"](4);a["\u0275\u0275advance"](4),a["\u0275\u0275textInterpolate"](e.sub_group_name),a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate2"]("\xa0\xa0(",e.sub_group_amount,"",e.vendor_currency,") "),a["\u0275\u0275advance"](21),a["\u0275\u0275property"]("ngIf",n.tdsNatureOnLedgerEntry),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",n.taxabilityTypeonLedgerEntry),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",n.taxabilityTypeonLedgerEntry),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",n.HSNSAConLedgerEntry),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("ngForOf",e.sub_group_entries)}}function te(e,t){if(1&e&&(a["\u0275\u0275elementContainerStart"](0),a["\u0275\u0275template"](1,ee,32,8,"div",32),a["\u0275\u0275elementContainerEnd"]()),2&e){const e=a["\u0275\u0275nextContext"](3);a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngForOf",null==e.inPrefilledJson?null:e.inPrefilledJson.StatutoryDetails.expenseEntry)}}function ne(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"mat-icon",46),a["\u0275\u0275text"](1,"save"),a["\u0275\u0275elementEnd"]())}function ie(e,t){1&e&&a["\u0275\u0275element"](0,"mat-spinner",47)}function ae(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"div",55),a["\u0275\u0275elementStart"](1,"div",51),a["\u0275\u0275elementStart"](2,"div",56),a["\u0275\u0275elementStart"](3,"mat-icon",57),a["\u0275\u0275text"](4," error_outline "),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](5,"div",58),a["\u0275\u0275text"](6," Amount limit reached "),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]())}function oe(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"div"),a["\u0275\u0275elementStart"](1,"mat-form-field",64),a["\u0275\u0275elementStart"](2,"mat-label"),a["\u0275\u0275text"](3,"Target entity"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275element"](4,"input",73),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]())}function re(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div"),a["\u0275\u0275element"](1,"app-input-search",74),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"](8);a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("list",e.legalEntityMasterData)}}function le(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"div",62),a["\u0275\u0275elementStart"](1,"app-input-search",75),a["\u0275\u0275listener"]("change",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"](8).onDeductionClick()})),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()}if(2&e){const e=a["\u0275\u0275nextContext"]().index,t=a["\u0275\u0275nextContext"](2).index,n=a["\u0275\u0275nextContext"](5);a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("required",n.paymentEntry.controls.expenseEntry.controls[t].controls.sub_group_entries.controls[e].controls.gl_account.value)("list",n.tdsNatureList)}}function se(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"div",62),a["\u0275\u0275elementStart"](1,"app-input-search",76),a["\u0275\u0275listener"]("change",(function(){a["\u0275\u0275restoreView"](e);const t=a["\u0275\u0275nextContext"]().index,n=a["\u0275\u0275nextContext"](2).index,i=a["\u0275\u0275nextContext"](5);return i.onTaxabilityChange(i.paymentEntry.controls.expenseEntry.controls[n].controls.sub_group_entries.controls[t].controls.taxability_type_id.value,n,t,i.itemDetails.payment_model_id)})),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()}if(2&e){const e=a["\u0275\u0275nextContext"](8);a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("required",!1)("list",e.taxabilityTypeDetails)}}function de(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",62),a["\u0275\u0275elementStart"](1,"mat-form-field",64),a["\u0275\u0275elementStart"](2,"mat-label"),a["\u0275\u0275text"](3),a["\u0275\u0275elementEnd"](),a["\u0275\u0275element"](4,"input",77),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"]().index,t=a["\u0275\u0275nextContext"](2).index,n=a["\u0275\u0275nextContext"](5);a["\u0275\u0275advance"](3),a["\u0275\u0275textInterpolate"](n.taxabilityTypeDetailsPlaceHolder),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("min",0)("max",100)("readonly",n.isTaxabilityRateReadOnly(n.paymentEntry.controls.expenseEntry.controls[t].controls.sub_group_entries.controls[e].controls.taxability_type_rate.value,t,e,n.itemDetails.payment_model_id))}}function ce(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"div",62),a["\u0275\u0275elementStart"](1,"mat-form-field",64),a["\u0275\u0275elementStart"](2,"mat-label"),a["\u0275\u0275text"](3,"HSN/SAC"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](4,"input",78),a["\u0275\u0275listener"]("keydown.enter",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"](8).moveToNextStep()})),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()}2&e&&(a["\u0275\u0275advance"](4),a["\u0275\u0275property"]("maxLength",8))}function me(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"div",61),a["\u0275\u0275elementStart"](1,"div",62),a["\u0275\u0275element"](2,"app-input-search",63),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](3,"div",62),a["\u0275\u0275elementStart"](4,"mat-form-field",64),a["\u0275\u0275elementStart"](5,"mat-label"),a["\u0275\u0275text"](6,"Amount *"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275element"](7,"input",65),a["\u0275\u0275elementStart"](8,"span",66),a["\u0275\u0275text"](9),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](10,"div",62),a["\u0275\u0275template"](11,oe,5,0,"div",17),a["\u0275\u0275template"](12,re,2,1,"div",17),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](13,"div",62),a["\u0275\u0275element"](14,"app-input-search",67),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](15,"div",62),a["\u0275\u0275elementStart"](16,"mat-form-field",64),a["\u0275\u0275elementStart"](17,"mat-label"),a["\u0275\u0275text"](18,"Description"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](19,"input",68),a["\u0275\u0275listener"]("keydown.enter",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"](7).moveToNextStep()})),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](20,le,2,2,"div",69),a["\u0275\u0275template"](21,se,2,2,"div",69),a["\u0275\u0275template"](22,de,5,4,"div",69),a["\u0275\u0275template"](23,ce,5,1,"div",69),a["\u0275\u0275elementStart"](24,"div",70),a["\u0275\u0275elementStart"](25,"div",71),a["\u0275\u0275listener"]("click",(function(){a["\u0275\u0275restoreView"](e);const t=a["\u0275\u0275nextContext"](2).index;return a["\u0275\u0275nextContext"](5).addSubGroupEntry(t)})),a["\u0275\u0275elementStart"](26,"mat-icon"),a["\u0275\u0275text"](27,"add"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](28,"div",72),a["\u0275\u0275listener"]("click",(function(){a["\u0275\u0275restoreView"](e);const n=t.index,i=a["\u0275\u0275nextContext"](2).index;return a["\u0275\u0275nextContext"](5).removeSubGroupEntry(i,n)})),a["\u0275\u0275elementStart"](29,"mat-icon"),a["\u0275\u0275text"](30,"delete"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()}if(2&e){const e=t.index,n=a["\u0275\u0275nextContext"](2).index,i=a["\u0275\u0275nextContext"](5);a["\u0275\u0275property"]("formGroupName",e),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("list",i.ccExpenseEntry),a["\u0275\u0275advance"](7),a["\u0275\u0275textInterpolate"](i.itemDetails.vendor_preferred_currency),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("ngIf",0!=e),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",0==e),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("list",i.paymentEntry.controls.expenseEntry.controls[n].controls.sub_group_entries.controls[e].controls.gl_account_master_data),a["\u0275\u0275advance"](6),a["\u0275\u0275property"]("ngIf",!i.paymentEntry.get("isChecked").value&&i.tdsNatureOnLedgerEntry&&i.isTDSNAtureAllowed(i.paymentEntry.controls.expenseEntry.controls[n].controls.sub_group_entries.controls[e].controls.gl_account.value,n,e,i.itemDetails.payment_model_id)),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",!i.paymentEntry.get("isChecked").value&&i.taxabilityTypeonLedgerEntry&&i.isGSTRateonLedgerAllowed(i.paymentEntry.controls.expenseEntry.controls[n].controls.sub_group_entries.controls[e].controls.gl_account.value,n,e,i.itemDetails.payment_model_id)),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",!i.paymentEntry.get("isChecked").value&&i.taxabilityTypeonLedgerEntry&&i.isGSTRateonLedgerAllowed(i.paymentEntry.controls.expenseEntry.controls[n].controls.sub_group_entries.controls[e].controls.gl_account.value,n,e,i.itemDetails.payment_model_id)),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",!i.paymentEntry.get("isChecked").value&&i.HSNSAConLedgerEntry&&i.isHSNSAConLedgerAllowed(i.paymentEntry.controls.expenseEntry.controls[n].controls.sub_group_entries.controls[e].controls.gl_account.value,n,e,i.itemDetails.payment_model_id))}}function pe(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",59),a["\u0275\u0275template"](1,me,31,10,"div",60),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"]().$implicit;a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngForOf",e.controls.sub_group_entries.controls)}}function ue(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",50),a["\u0275\u0275elementStart"](1,"div",51),a["\u0275\u0275elementStart"](2,"div"),a["\u0275\u0275text"](3),a["\u0275\u0275elementStart"](4,"span",52),a["\u0275\u0275text"](5),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](6,ae,7,0,"div",53),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](7,pe,2,1,"div",54),a["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=t.index,i=a["\u0275\u0275nextContext"](5);a["\u0275\u0275property"]("formGroupName",n),a["\u0275\u0275advance"](3),a["\u0275\u0275textInterpolate1"](" ",e.value.sub_group_name," "),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate1"]("(",e.value.sub_group_amount+" "+e.value.vendor_currency,") "),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",1==i.paymentEntry.controls.expenseEntry.controls[n].controls.is_sub_group_amt_exceeded),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",e.controls.sub_group_entries.controls.length>0)}}function ye(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",48),a["\u0275\u0275template"](1,ue,8,5,"div",49),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"](4);a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngForOf",e.expenseEntrySubGroupControl)}}function _e(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"div",55),a["\u0275\u0275elementStart"](1,"div",51),a["\u0275\u0275elementStart"](2,"div",56),a["\u0275\u0275elementStart"](3,"mat-icon",57),a["\u0275\u0275text"](4," error_outline "),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](5,"div",58),a["\u0275\u0275text"](6," Amount limit reached "),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]())}function he(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"div"),a["\u0275\u0275elementStart"](1,"mat-form-field",64),a["\u0275\u0275elementStart"](2,"mat-label"),a["\u0275\u0275text"](3,"Target entity"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275element"](4,"input",73),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]())}function ge(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div"),a["\u0275\u0275element"](1,"app-input-search",74),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"](8);a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("list",e.legalEntityMasterData)}}function xe(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"div",62),a["\u0275\u0275elementStart"](1,"app-input-search",75),a["\u0275\u0275listener"]("change",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"](8).onDeductionClick()})),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()}if(2&e){const e=a["\u0275\u0275nextContext"]().index,t=a["\u0275\u0275nextContext"](2).index,n=a["\u0275\u0275nextContext"](5);a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("required",n.paymentEntry.controls.expenseEntry.controls[t].controls.milestone_entries.controls[e].controls.gl_account.value)("list",n.tdsNatureList)}}function ve(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"div",62),a["\u0275\u0275elementStart"](1,"app-input-search",76),a["\u0275\u0275listener"]("change",(function(){a["\u0275\u0275restoreView"](e);const t=a["\u0275\u0275nextContext"]().index,n=a["\u0275\u0275nextContext"](2).index,i=a["\u0275\u0275nextContext"](5);return i.onTaxabilityChange(i.paymentEntry.controls.expenseEntry.controls[n].controls.milestone_entries.controls[t].controls.taxability_type_id.value,n,t,i.itemDetails.payment_model_id)})),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()}if(2&e){const e=a["\u0275\u0275nextContext"](8);a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("required",!1)("list",e.taxabilityTypeDetails)}}function fe(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",62),a["\u0275\u0275elementStart"](1,"mat-form-field",64),a["\u0275\u0275elementStart"](2,"mat-label"),a["\u0275\u0275text"](3),a["\u0275\u0275elementEnd"](),a["\u0275\u0275element"](4,"input",77),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"]().index,t=a["\u0275\u0275nextContext"](2).index,n=a["\u0275\u0275nextContext"](5);a["\u0275\u0275advance"](3),a["\u0275\u0275textInterpolate"](n.taxabilityTypeDetailsPlaceHolder),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("min",0)("max",100)("readonly",n.isTaxabilityRateReadOnly(n.paymentEntry.controls.expenseEntry.controls[t].controls.milestone_entries.controls[e].controls.taxability_type_rate.value,t,e,n.itemDetails.payment_model_id))}}function Ee(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"div",62),a["\u0275\u0275elementStart"](1,"mat-form-field",64),a["\u0275\u0275elementStart"](2,"mat-label"),a["\u0275\u0275text"](3,"HSN/SAC"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](4,"input",83),a["\u0275\u0275listener"]("keydown.enter",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"](8).moveToNextStep()})),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()}2&e&&(a["\u0275\u0275advance"](4),a["\u0275\u0275property"]("maxLength",8))}function be(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"div",61),a["\u0275\u0275elementStart"](1,"div",62),a["\u0275\u0275element"](2,"app-input-search",63),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](3,"div",62),a["\u0275\u0275elementStart"](4,"mat-form-field",64),a["\u0275\u0275elementStart"](5,"mat-label"),a["\u0275\u0275text"](6,"Amount"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275element"](7,"input",81),a["\u0275\u0275elementStart"](8,"span",66),a["\u0275\u0275text"](9),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](10,"div",62),a["\u0275\u0275element"](11,"app-input-search",82),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](12,"div",62),a["\u0275\u0275template"](13,he,5,0,"div",17),a["\u0275\u0275template"](14,ge,2,1,"div",17),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](15,"div",62),a["\u0275\u0275element"](16,"app-input-search",67),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](17,"div",62),a["\u0275\u0275elementStart"](18,"mat-form-field",64),a["\u0275\u0275elementStart"](19,"mat-label"),a["\u0275\u0275text"](20,"Description"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](21,"input",68),a["\u0275\u0275listener"]("keydown.enter",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"](7).moveToNextStep()})),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](22,xe,2,2,"div",69),a["\u0275\u0275template"](23,ve,2,2,"div",69),a["\u0275\u0275template"](24,fe,5,4,"div",69),a["\u0275\u0275template"](25,Ee,5,1,"div",69),a["\u0275\u0275elementStart"](26,"div",70),a["\u0275\u0275elementStart"](27,"div",71),a["\u0275\u0275listener"]("click",(function(){a["\u0275\u0275restoreView"](e);const t=a["\u0275\u0275nextContext"](2).index;return a["\u0275\u0275nextContext"](5).addSubGroupEntry(t)})),a["\u0275\u0275elementStart"](28,"mat-icon"),a["\u0275\u0275text"](29,"add"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](30,"div",72),a["\u0275\u0275listener"]("click",(function(){a["\u0275\u0275restoreView"](e);const n=t.index,i=a["\u0275\u0275nextContext"](2).index;return a["\u0275\u0275nextContext"](5).removeSubGroupEntry(i,n)})),a["\u0275\u0275elementStart"](31,"mat-icon"),a["\u0275\u0275text"](32,"delete"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()}if(2&e){const e=t.index,n=a["\u0275\u0275nextContext"](2).index,i=a["\u0275\u0275nextContext"](5);a["\u0275\u0275property"]("formGroupName",e),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("list",i.ccExpenseEntry),a["\u0275\u0275advance"](7),a["\u0275\u0275textInterpolate"](i.itemDetails.vendor_preferred_currency),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("list",i.subGroupMasterData),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("ngIf",0!=e),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",0==e),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("list",i.paymentEntry.controls.expenseEntry.controls[n].controls.milestone_entries.controls[e].controls.gl_account_master_data),a["\u0275\u0275advance"](6),a["\u0275\u0275property"]("ngIf",!i.paymentEntry.get("isChecked").value&&i.tdsNatureOnLedgerEntry&&i.isTDSNAtureAllowed(i.paymentEntry.controls.expenseEntry.controls[n].controls.milestone_entries.controls[e].controls.gl_account.value,n,e,i.itemDetails.payment_model_id)),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",!i.paymentEntry.get("isChecked").value&&i.taxabilityTypeonLedgerEntry&&i.isGSTRateonLedgerAllowed(i.paymentEntry.controls.expenseEntry.controls[n].controls.milestone_entries.controls[e].controls.gl_account.value,n,e,i.itemDetails.payment_model_id)),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",!i.paymentEntry.get("isChecked").value&&i.taxabilityTypeonLedgerEntry&&i.isGSTRateonLedgerAllowed(i.paymentEntry.controls.expenseEntry.controls[n].controls.milestone_entries.controls[e].controls.gl_account.value,n,e,i.itemDetails.payment_model_id)),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",!i.paymentEntry.get("isChecked").value&&i.HSNSAConLedgerEntry&&i.isHSNSAConLedgerAllowed(i.paymentEntry.controls.expenseEntry.controls[n].controls.milestone_entries.controls[e].controls.gl_account.value,n,e,i.itemDetails.payment_model_id))}}function Se(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",80),a["\u0275\u0275template"](1,be,33,11,"div",60),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"]().$implicit;a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngForOf",e.controls.milestone_entries.controls)}}function De(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",50),a["\u0275\u0275elementStart"](1,"div",51),a["\u0275\u0275elementStart"](2,"div"),a["\u0275\u0275text"](3),a["\u0275\u0275elementStart"](4,"span",52),a["\u0275\u0275text"](5),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](6,_e,7,0,"div",53),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](7,Se,2,1,"div",79),a["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=t.index,i=a["\u0275\u0275nextContext"](5);a["\u0275\u0275property"]("formGroupName",n),a["\u0275\u0275advance"](3),a["\u0275\u0275textInterpolate1"](" ",e.value.milestone_name," "),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate1"]("(",e.value.milestone_amount+" "+i.itemDetails.vendor_preferred_currency,") "),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",1==i.paymentEntry.controls.expenseEntry.controls[n].controls.is_milestone_amount_exceeded),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",e.controls.milestone_entries.controls.length>0)}}function Ce(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",48),a["\u0275\u0275template"](1,De,8,5,"div",49),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"](4);a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngForOf",e.expenseEntrySubGroupControl)}}function Te(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementContainerStart"](0),a["\u0275\u0275elementStart"](1,"div",40),a["\u0275\u0275elementStart"](2,"div"),a["\u0275\u0275elementStart"](3,"mat-checkbox",41),a["\u0275\u0275listener"]("click",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"](3).check()})),a["\u0275\u0275text"](4,"Advance"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](5,"div"),a["\u0275\u0275elementStart"](6,"button",42),a["\u0275\u0275listener"]("click",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"](3).saveDraft()})),a["\u0275\u0275template"](7,ne,2,0,"mat-icon",43),a["\u0275\u0275template"](8,ie,1,0,"mat-spinner",44),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](9,ye,2,1,"div",45),a["\u0275\u0275template"](10,Ce,2,1,"div",45),a["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=a["\u0275\u0275nextContext"](3);a["\u0275\u0275advance"](6),a["\u0275\u0275property"]("ngClass",e.isDraftSpinnerLoading?"save-draft-btn-loading":"save-draft-btn"),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",!e.isDraftSpinnerLoading),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",e.isDraftSpinnerLoading),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",e.expenseEntrySubGroupControl.length>0&&1===e.itemDetails.payment_model_id),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",e.expenseEntrySubGroupControl.length>0&&(2===e.itemDetails.payment_model_id||3===e.itemDetails.payment_model_id))}}function Pe(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"mat-step"),a["\u0275\u0275template"](1,O,1,0,"ng-template",18),a["\u0275\u0275template"](2,$,2,1,"ng-container",17),a["\u0275\u0275template"](3,te,2,1,"ng-container",17),a["\u0275\u0275template"](4,Te,11,5,"ng-container",17),a["\u0275\u0275elementStart"](5,"div",14),a["\u0275\u0275element"](6,"div",29),a["\u0275\u0275elementStart"](7,"div",30),a["\u0275\u0275elementStart"](8,"button",31),a["\u0275\u0275listener"]("click",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"](),a["\u0275\u0275reference"](15).next()})),a["\u0275\u0275elementStart"](9,"mat-icon"),a["\u0275\u0275text"](10,"navigate_next"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()}if(2&e){const e=a["\u0275\u0275nextContext"](2);a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("ngIf","readOnly"==e.statutoryViewMode&&e.isTaxDetailMasterDataAvailable&&e.isGlAccMasterDataAvailable&&e.isLegalEntityMasterDataAvailable&&(2===e.itemDetails.payment_model_id||3===e.itemDetails.payment_model_id)),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf","readOnly"==e.statutoryViewMode&&e.isTaxDetailMasterDataAvailable&&e.isGlAccMasterDataAvailable&&e.isLegalEntityMasterDataAvailable&&1===e.itemDetails.payment_model_id),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf","edit"==e.statutoryViewMode)}}function Ae(e,t){1&e&&a["\u0275\u0275text"](0,"Statutory details")}function ke(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"tr"),a["\u0275\u0275elementStart"](1,"th",39),a["\u0275\u0275text"](2),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](3,"td"),a["\u0275\u0275text"](4),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](5,"td"),a["\u0275\u0275text"](6),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](7,"td"),a["\u0275\u0275text"](8),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](9,"td"),a["\u0275\u0275text"](10),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=t.index;a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate"](n+1),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate"](e.tax_type_name),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate"](e.tax_percentage),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate"](e.tax_amount),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate"](e.tax_addition_gl_name)}}function Ie(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div"),a["\u0275\u0275elementStart"](1,"div",33),a["\u0275\u0275elementStart"](2,"div",34),a["\u0275\u0275elementStart"](3,"strong"),a["\u0275\u0275text"](4),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](5,"div",33),a["\u0275\u0275elementStart"](6,"div",34),a["\u0275\u0275elementStart"](7,"table",36),a["\u0275\u0275elementStart"](8,"thead"),a["\u0275\u0275elementStart"](9,"tr"),a["\u0275\u0275elementStart"](10,"th",37),a["\u0275\u0275text"](11,"#"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](12,"th",37),a["\u0275\u0275text"](13,"Tax Type"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](14,"th",37),a["\u0275\u0275text"](15,"Tax percentage"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](16,"th",37),a["\u0275\u0275text"](17,"Tax Amount"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](18,"th",37),a["\u0275\u0275text"](19,"Gl Name"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](20,"tbody"),a["\u0275\u0275template"](21,ke,11,5,"tr",32),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;a["\u0275\u0275advance"](4),a["\u0275\u0275textInterpolate"](e.sub_group_name),a["\u0275\u0275advance"](17),a["\u0275\u0275property"]("ngForOf",e.sub_group_tax_entries)}}function we(e,t){if(1&e&&(a["\u0275\u0275elementContainerStart"](0,90),a["\u0275\u0275elementStart"](1,"div",91),a["\u0275\u0275elementStart"](2,"div",34),a["\u0275\u0275elementStart"](3,"span",92),a["\u0275\u0275elementStart"](4,"strong"),a["\u0275\u0275text"](5,"Tax Additions"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](6,Ie,22,2,"div",32),a["\u0275\u0275elementContainerEnd"]()),2&e){const e=a["\u0275\u0275nextContext"](3);a["\u0275\u0275advance"](6),a["\u0275\u0275property"]("ngForOf",null==e.inPrefilledJson?null:e.inPrefilledJson.StatutoryDetails.taxAddition)}}function Me(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"mat-icon",46),a["\u0275\u0275text"](1,"save"),a["\u0275\u0275elementEnd"]())}function Oe(e,t){1&e&&a["\u0275\u0275element"](0,"mat-spinner",47)}function Ve(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"div",61),a["\u0275\u0275elementStart"](1,"div",34),a["\u0275\u0275elementStart"](2,"div",33),a["\u0275\u0275element"](3,"div",101),a["\u0275\u0275elementStart"](4,"div",30),a["\u0275\u0275element"](5,"app-input-search",102),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](6,"div",30),a["\u0275\u0275elementStart"](7,"mat-form-field",103),a["\u0275\u0275elementStart"](8,"mat-label"),a["\u0275\u0275text"](9,"Tax percentage"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](10,"input",104),a["\u0275\u0275listener"]("input",(function(n){a["\u0275\u0275restoreView"](e);const i=t.index,o=a["\u0275\u0275nextContext"](2).index;return a["\u0275\u0275nextContext"](5).onTaxAdditionPercentageChange(o,i,n.target.value)})),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](11,"div",30),a["\u0275\u0275elementStart"](12,"mat-form-field",103),a["\u0275\u0275elementStart"](13,"mat-label"),a["\u0275\u0275text"](14,"Tax amount"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275element"](15,"input",105),a["\u0275\u0275elementStart"](16,"span",66),a["\u0275\u0275text"](17),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](18,"div",30),a["\u0275\u0275element"](19,"app-input-search",106),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](20,"div",30),a["\u0275\u0275elementStart"](21,"div",70),a["\u0275\u0275elementStart"](22,"div",71),a["\u0275\u0275listener"]("click",(function(){a["\u0275\u0275restoreView"](e);const t=a["\u0275\u0275nextContext"](2).index;return a["\u0275\u0275nextContext"](5).addTaxAdditionEntry(t)})),a["\u0275\u0275elementStart"](23,"mat-icon"),a["\u0275\u0275text"](24,"add"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](25,"div",72),a["\u0275\u0275listener"]("click",(function(){a["\u0275\u0275restoreView"](e);const n=t.index,i=a["\u0275\u0275nextContext"](2).index;return a["\u0275\u0275nextContext"](5).removeTaxAdditionEntry(i,n)})),a["\u0275\u0275elementStart"](26,"mat-icon"),a["\u0275\u0275text"](27,"delete"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()}if(2&e){const e=t.index,n=a["\u0275\u0275nextContext"](2).index,i=a["\u0275\u0275nextContext"](5);a["\u0275\u0275property"]("formGroupName",e),a["\u0275\u0275advance"](5),a["\u0275\u0275property"]("list",i.taxAdditionType),a["\u0275\u0275advance"](5),a["\u0275\u0275property"]("min",0)("max",100)("readOnly",!i.paymentEntry.controls.taxAddition.controls[n].controls.sub_group_tax_entries.controls[e].controls.is_tax_addition_gl_field_mandatory.value),a["\u0275\u0275advance"](7),a["\u0275\u0275textInterpolate"](i.itemDetails.vendor_preferred_currency),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("required",i.paymentEntry.controls.taxAddition.controls[n].controls.sub_group_tax_entries.controls[e].controls.is_tax_addition_gl_field_mandatory.value)("list",i.glAccountForAddition)("disabled",!i.paymentEntry.controls.taxAddition.controls[n].controls.sub_group_tax_entries.controls[e].controls.is_tax_addition_gl_field_mandatory.value)}}function Ne(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",100),a["\u0275\u0275template"](1,Ve,28,9,"div",60),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"]().$implicit;a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngForOf",e.controls.sub_group_tax_entries.controls)}}function Le(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"div",50),a["\u0275\u0275elementStart"](1,"div",33),a["\u0275\u0275elementStart"](2,"div",97),a["\u0275\u0275element"](3,"app-input-search",98),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](4,"div",97),a["\u0275\u0275elementStart"](5,"div",70),a["\u0275\u0275elementStart"](6,"div",71),a["\u0275\u0275listener"]("click",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"](5).addTaxSubGroupAddition()})),a["\u0275\u0275elementStart"](7,"mat-icon"),a["\u0275\u0275text"](8,"add"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](9,"div",72),a["\u0275\u0275listener"]("click",(function(){a["\u0275\u0275restoreView"](e);const n=t.index;return a["\u0275\u0275nextContext"](5).removeTaxSubGroupAddition(n)})),a["\u0275\u0275elementStart"](10,"mat-icon"),a["\u0275\u0275text"](11,"delete"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](12,Ne,2,1,"div",99),a["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=t.index,i=a["\u0275\u0275nextContext"](5);a["\u0275\u0275property"]("formGroupName",n),a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("list",i.selectedGlAccount),a["\u0275\u0275advance"](9),a["\u0275\u0275property"]("ngIf",e.controls.sub_group_tax_entries.controls.length>0)}}function Ge(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",95),a["\u0275\u0275elementStart"](1,"div",96),a["\u0275\u0275text"](2,"Tax addition (+)"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](3,Le,13,3,"div",49),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"](4);a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("ngForOf",e.taxAdditionSubGroupControl)}}function Fe(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementContainerStart"](0),a["\u0275\u0275elementStart"](1,"div",93),a["\u0275\u0275elementStart"](2,"button",42),a["\u0275\u0275listener"]("click",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"](3).saveDraft()})),a["\u0275\u0275template"](3,Me,2,0,"mat-icon",43),a["\u0275\u0275template"](4,Oe,1,0,"mat-spinner",44),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](5,Ge,4,1,"div",94),a["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=a["\u0275\u0275nextContext"](3);a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("ngClass",e.isDraftSpinnerLoading?"save-draft-btn-loading":"save-draft-btn"),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",!e.isDraftSpinnerLoading),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",e.isDraftSpinnerLoading),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",e.taxAdditionSubGroupControl.length>0)}}function qe(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"tr"),a["\u0275\u0275elementStart"](1,"th",39),a["\u0275\u0275text"](2),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](3,"td"),a["\u0275\u0275text"](4),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](5,"td"),a["\u0275\u0275text"](6),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](7,"td"),a["\u0275\u0275text"](8),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](9,"td"),a["\u0275\u0275text"](10),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=t.index;a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate"](n+1),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate"](e.tax_type_name),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate"](e.tax_percentage),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate"](e.tax_amount),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate"](e.tax_deduction_gl_name)}}function je(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div"),a["\u0275\u0275elementStart"](1,"div",33),a["\u0275\u0275elementStart"](2,"div",34),a["\u0275\u0275elementStart"](3,"strong"),a["\u0275\u0275text"](4),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](5,"div",33),a["\u0275\u0275elementStart"](6,"div",34),a["\u0275\u0275elementStart"](7,"table",36),a["\u0275\u0275elementStart"](8,"thead"),a["\u0275\u0275elementStart"](9,"tr"),a["\u0275\u0275elementStart"](10,"th",37),a["\u0275\u0275text"](11,"#"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](12,"th",37),a["\u0275\u0275text"](13,"Tax Type"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](14,"th",37),a["\u0275\u0275text"](15,"Tax percentage"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](16,"th",37),a["\u0275\u0275text"](17,"Tax Amount"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](18,"th",37),a["\u0275\u0275text"](19,"Gl Name"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](20,"tbody"),a["\u0275\u0275template"](21,qe,11,5,"tr",32),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;a["\u0275\u0275advance"](4),a["\u0275\u0275textInterpolate"](e.sub_group_name),a["\u0275\u0275advance"](17),a["\u0275\u0275property"]("ngForOf",e.sub_group_tax_entries)}}function Re(e,t){if(1&e&&(a["\u0275\u0275elementContainerStart"](0,90),a["\u0275\u0275elementStart"](1,"div",91),a["\u0275\u0275elementStart"](2,"div",34),a["\u0275\u0275elementStart"](3,"span",92),a["\u0275\u0275elementStart"](4,"strong"),a["\u0275\u0275text"](5,"Tax Deductions"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](6,je,22,2,"div",32),a["\u0275\u0275elementContainerEnd"]()),2&e){const e=a["\u0275\u0275nextContext"](3);a["\u0275\u0275advance"](6),a["\u0275\u0275property"]("ngForOf",null==e.inPrefilledJson?null:e.inPrefilledJson.StatutoryDetails.taxDeduction)}}function Be(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"div",61),a["\u0275\u0275elementStart"](1,"div",34),a["\u0275\u0275elementStart"](2,"div",33),a["\u0275\u0275element"](3,"div",101),a["\u0275\u0275elementStart"](4,"div",30),a["\u0275\u0275element"](5,"app-input-search",102),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](6,"div",30),a["\u0275\u0275elementStart"](7,"mat-form-field",103),a["\u0275\u0275elementStart"](8,"mat-label"),a["\u0275\u0275text"](9,"Tax percentage"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](10,"input",104),a["\u0275\u0275listener"]("input",(function(n){a["\u0275\u0275restoreView"](e);const i=t.index,o=a["\u0275\u0275nextContext"](2).index;return a["\u0275\u0275nextContext"](5).onTaxDeductionPercentageChange(o,i,n.target.value)})),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](11,"div",30),a["\u0275\u0275elementStart"](12,"mat-form-field",103),a["\u0275\u0275elementStart"](13,"mat-label"),a["\u0275\u0275text"](14,"Tax amount"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275element"](15,"input",105),a["\u0275\u0275elementStart"](16,"span",66),a["\u0275\u0275text"](17),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](18,"div",30),a["\u0275\u0275elementStart"](19,"app-input-search",109),a["\u0275\u0275listener"]("change",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"](7).onDeductionClick()})),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](20,"div",30),a["\u0275\u0275elementStart"](21,"div",70),a["\u0275\u0275elementStart"](22,"div",71),a["\u0275\u0275listener"]("click",(function(){a["\u0275\u0275restoreView"](e);const t=a["\u0275\u0275nextContext"](2).index;return a["\u0275\u0275nextContext"](5).addTaxDeductionEntry(t)})),a["\u0275\u0275elementStart"](23,"mat-icon"),a["\u0275\u0275text"](24,"add"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](25,"div",72),a["\u0275\u0275listener"]("click",(function(){a["\u0275\u0275restoreView"](e);const n=t.index,i=a["\u0275\u0275nextContext"](2).index;return a["\u0275\u0275nextContext"](5).removeTaxDeductionEntry(i,n)})),a["\u0275\u0275elementStart"](26,"mat-icon"),a["\u0275\u0275text"](27,"delete"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()}if(2&e){const e=t.index,n=a["\u0275\u0275nextContext"](2).index,i=a["\u0275\u0275nextContext"](5);a["\u0275\u0275property"]("formGroupName",e),a["\u0275\u0275advance"](5),a["\u0275\u0275property"]("list",i.taxDeductionType),a["\u0275\u0275advance"](5),a["\u0275\u0275property"]("min",0)("max",100)("readOnly",!i.paymentEntry.controls.taxDeduction.controls[n].controls.sub_group_tax_entries.controls[e].controls.is_tax_deduction_gl_field_mandatory.value),a["\u0275\u0275advance"](7),a["\u0275\u0275textInterpolate"](i.itemDetails.vendor_preferred_currency),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("required",i.paymentEntry.controls.taxDeduction.controls[n].controls.sub_group_tax_entries.controls[e].controls.is_tax_deduction_gl_field_mandatory.value)("list",i.glAccountForDeduction)("disabled",!i.paymentEntry.controls.taxDeduction.controls[n].controls.sub_group_tax_entries.controls[e].controls.is_tax_deduction_gl_field_mandatory.value)}}function Je(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",100),a["\u0275\u0275template"](1,Be,28,9,"div",60),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"]().$implicit;a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngForOf",e.controls.sub_group_tax_entries.controls)}}function $e(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"div",50),a["\u0275\u0275elementStart"](1,"div",33),a["\u0275\u0275elementStart"](2,"div",97),a["\u0275\u0275element"](3,"app-input-search",98),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](4,"div",97),a["\u0275\u0275elementStart"](5,"div",70),a["\u0275\u0275elementStart"](6,"div",71),a["\u0275\u0275listener"]("click",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"](5).addTaxSubGroupDeduction()})),a["\u0275\u0275elementStart"](7,"mat-icon"),a["\u0275\u0275text"](8,"add"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](9,"div",72),a["\u0275\u0275listener"]("click",(function(){a["\u0275\u0275restoreView"](e);const n=t.index;return a["\u0275\u0275nextContext"](5).removeTaxSubGroupDeduction(n)})),a["\u0275\u0275elementStart"](10,"mat-icon"),a["\u0275\u0275text"](11,"delete"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](12,Je,2,1,"div",99),a["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=t.index,i=a["\u0275\u0275nextContext"](5);a["\u0275\u0275property"]("formGroupName",n),a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("list",i.selectedGlAccount),a["\u0275\u0275advance"](9),a["\u0275\u0275property"]("ngIf",e.controls.sub_group_tax_entries.controls.length>0)}}function He(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",108),a["\u0275\u0275elementStart"](1,"div",96),a["\u0275\u0275text"](2,"Tax deduction (-)"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](3,$e,13,3,"div",49),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"](4);a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("ngForOf",e.taxDeductionSubGroupControl)}}function We(e,t){if(1&e&&(a["\u0275\u0275elementContainerStart"](0),a["\u0275\u0275template"](1,He,4,1,"div",107),a["\u0275\u0275elementContainerEnd"]()),2&e){const e=a["\u0275\u0275nextContext"](3);a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",e.taxDeductionSubGroupControl.length>0)}}function ze(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"mat-step"),a["\u0275\u0275template"](1,Ae,1,0,"ng-template",18),a["\u0275\u0275template"](2,we,7,1,"ng-container",84),a["\u0275\u0275template"](3,Fe,6,4,"ng-container",17),a["\u0275\u0275elementStart"](4,"div",85),a["\u0275\u0275elementStart"](5,"div",86),a["\u0275\u0275elementStart"](6,"div",87),a["\u0275\u0275text"](7,"Total tax addition"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](8,"div",88),a["\u0275\u0275text"](9),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](10,Re,7,1,"ng-container",84),a["\u0275\u0275template"](11,We,2,1,"ng-container",17),a["\u0275\u0275elementStart"](12,"div",85),a["\u0275\u0275elementStart"](13,"div",86),a["\u0275\u0275elementStart"](14,"div",87),a["\u0275\u0275text"](15,"Total tax deduction"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](16,"div",88),a["\u0275\u0275text"](17),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](18,"div",89),a["\u0275\u0275element"](19,"div",29),a["\u0275\u0275elementStart"](20,"div",30),a["\u0275\u0275elementStart"](21,"button",31),a["\u0275\u0275listener"]("click",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"](),a["\u0275\u0275reference"](15).next()})),a["\u0275\u0275elementStart"](22,"mat-icon"),a["\u0275\u0275text"](23,"navigate_next"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()}if(2&e){const e=a["\u0275\u0275nextContext"](2);a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("ngIf","readOnly"==e.statutoryViewMode),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf","edit"==e.statutoryViewMode),a["\u0275\u0275advance"](6),a["\u0275\u0275textInterpolate1"](" ",e.paymentEntry.value.totalTaxAddition+" "+e.itemDetails.vendor_preferred_currency," "),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf","readOnly"==e.statutoryViewMode),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf","edit"==e.statutoryViewMode),a["\u0275\u0275advance"](6),a["\u0275\u0275textInterpolate1"](" ",e.paymentEntry.value.totalTaxDeduction+" "+e.itemDetails.vendor_preferred_currency," ")}}function Ue(e,t){1&e&&a["\u0275\u0275text"](0,"Transaction")}function Ke(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"mat-icon",46),a["\u0275\u0275text"](1,"save"),a["\u0275\u0275elementEnd"]())}function Xe(e,t){1&e&&a["\u0275\u0275element"](0,"mat-spinner",47)}function Ye(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"div",110),a["\u0275\u0275elementStart"](1,"button",42),a["\u0275\u0275listener"]("click",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"](2).saveDraft()})),a["\u0275\u0275template"](2,Ke,2,0,"mat-icon",43),a["\u0275\u0275template"](3,Xe,1,0,"mat-spinner",44),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()}if(2&e){const e=a["\u0275\u0275nextContext"](2);a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngClass",e.isDraftSpinnerLoading?"save-draft-btn-loading":"save-draft-btn"),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",!e.isDraftSpinnerLoading),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",e.isDraftSpinnerLoading)}}function Qe(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"app-input-search",121),a["\u0275\u0275listener"]("change",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"](3).formSelectedFb()})),a["\u0275\u0275elementEnd"]()}if(2&e){const e=a["\u0275\u0275nextContext"](3);a["\u0275\u0275property"]("list",e.financialBooksMasterData)}}function Ze(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"div",141),a["\u0275\u0275elementStart"](1,"button",142),a["\u0275\u0275listener"]("click",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"](4).chooseBank()})),a["\u0275\u0275text"](2," Choose bank "),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()}}function et(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div"),a["\u0275\u0275elementStart"](1,"div",143),a["\u0275\u0275elementStart"](2,"div",144),a["\u0275\u0275text"](3,"Bank"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](4,"div",145),a["\u0275\u0275text"](5),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](6,"div",146),a["\u0275\u0275elementStart"](7,"div",144),a["\u0275\u0275text"](8,"Bank Acc No"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](9,"div",145),a["\u0275\u0275text"](10),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](11,"div",147),a["\u0275\u0275elementStart"](12,"div",148),a["\u0275\u0275text"](13,"Bank branch"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](14,"div",145),a["\u0275\u0275text"](15),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"](4);a["\u0275\u0275advance"](5),a["\u0275\u0275textInterpolate1"](" ",e.paymentEntry.value.bank.bank_name," "),a["\u0275\u0275advance"](5),a["\u0275\u0275textInterpolate1"](" ",e.paymentEntry.value.bank.bank_acc_no," "),a["\u0275\u0275advance"](5),a["\u0275\u0275textInterpolate1"](" ",e.paymentEntry.value.bank.bank_branch," ")}}function tt(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",149),a["\u0275\u0275elementStart"](1,"div",150),a["\u0275\u0275elementStart"](2,"div",151),a["\u0275\u0275elementStart"](3,"div",152),a["\u0275\u0275elementStart"](4,"mat-icon",153),a["\u0275\u0275text"](5,"warning"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275text"](6,"Warning ! "),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](7,"div",33),a["\u0275\u0275elementStart"](8,"div",154),a["\u0275\u0275text"](9," This Invoice of"),a["\u0275\u0275elementStart"](10,"span",155),a["\u0275\u0275text"](11),a["\u0275\u0275elementEnd"](),a["\u0275\u0275text"](12," is being processed by "),a["\u0275\u0275elementStart"](13,"span",155),a["\u0275\u0275text"](14),a["\u0275\u0275elementEnd"](),a["\u0275\u0275text"](15),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"](4);a["\u0275\u0275advance"](11),a["\u0275\u0275textInterpolate1"](" PR ",e.activePR,""),a["\u0275\u0275advance"](3),a["\u0275\u0275textInterpolate"](e.billPaymentProcessingBy),a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate1"](" on the ",e.billPaymentMode," screen ")}}function nt(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"button",156),a["\u0275\u0275listener"]("click",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"](4).VerifyStatutoryDetails()})),a["\u0275\u0275text"](1," Verify "),a["\u0275\u0275elementEnd"]()}}function it(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"button",157),a["\u0275\u0275text"](1," \u2714 Verified "),a["\u0275\u0275elementEnd"]())}function at(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"button",158),a["\u0275\u0275listener"]("click",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"](4).moveToBank()})),a["\u0275\u0275text"](1," Move to bank "),a["\u0275\u0275elementEnd"]()}}function ot(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"button",159),a["\u0275\u0275listener"]("click",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"](4).moveToBank()})),a["\u0275\u0275text"](1," \u2714 Moved to bank "),a["\u0275\u0275elementEnd"]()}}function rt(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"button",158),a["\u0275\u0275listener"]("click",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"](4).takeAdvance()})),a["\u0275\u0275text"](1," Take Advance "),a["\u0275\u0275elementEnd"]()}}const lt=function(){return{"margin-left":"10px"}};function st(e,t){1&e&&a["\u0275\u0275element"](0,"mat-spinner",160),2&e&&a["\u0275\u0275property"]("ngStyle",a["\u0275\u0275pureFunction0"](1,lt))}function dt(e,t){if(1&e&&(a["\u0275\u0275elementContainerStart"](0,33),a["\u0275\u0275elementStart"](1,"div",122),a["\u0275\u0275elementStart"](2,"div",123),a["\u0275\u0275text"](3," Payment "),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](4,"div",124),a["\u0275\u0275elementStart"](5,"mat-icon",125),a["\u0275\u0275text"](6," error_outline "),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](7,"span",126),a["\u0275\u0275text"](8),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](9,"div",127),a["\u0275\u0275elementStart"](10,"div",22),a["\u0275\u0275template"](11,Ze,3,0,"div",128),a["\u0275\u0275template"](12,et,16,3,"div",17),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](13,"div",117),a["\u0275\u0275elementStart"](14,"div",33),a["\u0275\u0275elementStart"](15,"div",129),a["\u0275\u0275text"](16,"Invoice No"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](17,"div",130),a["\u0275\u0275elementStart"](18,"mat-form-field",64),a["\u0275\u0275elementStart"](19,"mat-label"),a["\u0275\u0275text"](20,"Invoice No"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275element"](21,"input",131),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](22,"div",33),a["\u0275\u0275elementStart"](23,"div",129),a["\u0275\u0275text"](24,"Cheque no / Ref no"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](25,"div",130),a["\u0275\u0275elementStart"](26,"mat-form-field",64),a["\u0275\u0275elementStart"](27,"mat-label"),a["\u0275\u0275text"](28,"Cheque No"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275element"](29,"input",132),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](30,"div",33),a["\u0275\u0275elementStart"](31,"div",129),a["\u0275\u0275text"](32,"Cheque amount"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](33,"div",130),a["\u0275\u0275elementStart"](34,"mat-form-field",64),a["\u0275\u0275elementStart"](35,"mat-label"),a["\u0275\u0275text"](36,"Cheque amount *"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275element"](37,"input",133),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](38,tt,16,3,"div",134),a["\u0275\u0275elementStart"](39,"div",135),a["\u0275\u0275template"](40,nt,2,0,"button",136),a["\u0275\u0275template"](41,it,2,0,"button",137),a["\u0275\u0275template"](42,at,2,0,"button",138),a["\u0275\u0275template"](43,ot,2,0,"button",139),a["\u0275\u0275template"](44,rt,2,0,"button",138),a["\u0275\u0275template"](45,st,1,2,"mat-spinner",140),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementContainerEnd"]()),2&e){const e=a["\u0275\u0275nextContext"](3);a["\u0275\u0275advance"](8),a["\u0275\u0275textInterpolate1"](" ",e.paymentChequeMsg," "),a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("ngIf",!e.paymentEntry.value.bank),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",e.paymentEntry.value.bank),a["\u0275\u0275advance"](17),a["\u0275\u0275property"]("required",e.isChequeNoMandatory),a["\u0275\u0275advance"](9),a["\u0275\u0275property"]("ngIf",""!=e.activeBill&&null!=e.activeBill&&null!=e.activeBill),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("ngIf",!(0!=e.isMovedToBank&&4!=e.isMovedToBank||e.paymentEntry.get("isChecked").value||e.isLoading)),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",2==e.isMovedToBank||1==e.isMovedToBank),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",!e.isPaymentCompleted&&!e.paymentEntry.get("isChecked").value&&!e.isLoading),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",e.isPaymentCompleted),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",e.paymentEntry.get("isChecked").value),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",e.isLoading)}}function ct(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",111),a["\u0275\u0275elementStart"](1,"div",112),a["\u0275\u0275text"](2," Vendor account "),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](3,"div",33),a["\u0275\u0275elementStart"](4,"div",113),a["\u0275\u0275elementStart"](5,"mat-form-field",103),a["\u0275\u0275elementStart"](6,"mat-label"),a["\u0275\u0275text"](7,"Vendor account"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275element"](8,"input",114),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](9,"div",33),a["\u0275\u0275elementStart"](10,"div",115),a["\u0275\u0275element"](11,"app-input-search",116),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](12,"div",117),a["\u0275\u0275elementStart"](13,"mat-form-field",118),a["\u0275\u0275elementStart"](14,"mat-label"),a["\u0275\u0275text"](15,"Vendor description"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275element"](16,"textarea",119),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](17,"div",33),a["\u0275\u0275elementStart"](18,"div",115),a["\u0275\u0275template"](19,Qe,1,1,"app-input-search",120),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](20,dt,46,11,"ng-container",23),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"](2);a["\u0275\u0275advance"](11),a["\u0275\u0275property"]("list",e.vendorReferenceTypes),a["\u0275\u0275advance"](8),a["\u0275\u0275property"]("ngIf",e.financeBooksAvailable),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",e.showTransationSummary)}}function mt(e,t){}function pt(e,t){if(1&e&&(a["\u0275\u0275elementContainerStart"](0,33),a["\u0275\u0275template"](1,mt,0,0,"ng-template",161),a["\u0275\u0275elementContainerEnd"]()),2&e){a["\u0275\u0275nextContext"]();const e=a["\u0275\u0275reference"](27);a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngTemplateOutlet",e)}}function ut(e,t){}function yt(e,t){if(1&e&&(a["\u0275\u0275elementContainerStart"](0,33),a["\u0275\u0275template"](1,ut,0,0,"ng-template",161),a["\u0275\u0275elementContainerEnd"]()),2&e){a["\u0275\u0275nextContext"]();const e=a["\u0275\u0275reference"](29);a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngTemplateOutlet",e)}}function _t(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",162),a["\u0275\u0275elementStart"](1,"div",163),a["\u0275\u0275elementStart"](2,"mat-icon",164),a["\u0275\u0275text"](3," summarize "),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](4,"span",165),a["\u0275\u0275text"](5,"Summary"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](6,"div",166),a["\u0275\u0275elementStart"](7,"div",113),a["\u0275\u0275elementStart"](8,"div",166),a["\u0275\u0275elementStart"](9,"div",167),a["\u0275\u0275text"](10," Total Expense amount : "),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](11,"div",168),a["\u0275\u0275text"](12),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](13,"div",166),a["\u0275\u0275elementStart"](14,"div",167),a["\u0275\u0275text"](15,"Total Tax added :"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](16,"div",168),a["\u0275\u0275text"](17),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](18,"div",166),a["\u0275\u0275elementStart"](19,"div",167),a["\u0275\u0275text"](20," Total Tax deduced : "),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](21,"div",168),a["\u0275\u0275text"](22),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](23,"div",166),a["\u0275\u0275elementStart"](24,"div",167),a["\u0275\u0275text"](25,"Overall tax :"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](26,"div",168),a["\u0275\u0275text"](27),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](28,"div",166),a["\u0275\u0275elementStart"](29,"div",167),a["\u0275\u0275text"](30,"Total amount :"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](31,"div",169),a["\u0275\u0275text"](32),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"](2);a["\u0275\u0275advance"](12),a["\u0275\u0275textInterpolate1"](" ",e.paymentEntry.value.totalExpenseAmount+" "+e.itemDetails.vendor_preferred_currency," "),a["\u0275\u0275advance"](5),a["\u0275\u0275textInterpolate1"](" ",e.paymentEntry.value.totalTaxAddition+" "+e.itemDetails.vendor_preferred_currency," "),a["\u0275\u0275advance"](5),a["\u0275\u0275textInterpolate1"](" ",e.paymentEntry.value.totalTaxDeduction+" "+e.itemDetails.vendor_preferred_currency," "),a["\u0275\u0275advance"](5),a["\u0275\u0275textInterpolate1"](" ",e.paymentEntry.value.overallTaxAmount+" "+e.itemDetails.vendor_preferred_currency," "),a["\u0275\u0275advance"](5),a["\u0275\u0275textInterpolate1"](" ",e.paymentEntry.value.finalAmount+" "+e.itemDetails.vendor_preferred_currency," ")}}function ht(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"tr"),a["\u0275\u0275elementStart"](1,"th",39),a["\u0275\u0275text"](2),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](3,"td"),a["\u0275\u0275text"](4),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](5,"td"),a["\u0275\u0275text"](6),a["\u0275\u0275pipe"](7,"date"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](8,"td"),a["\u0275\u0275text"](9),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=t.index,i=a["\u0275\u0275nextContext"](3);a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate"](n+1),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate"](e.amount_paid?e.amount_paid+" "+i.itemDetails.vendor_preferred_currency:"-"),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate"](e.payment_date?a["\u0275\u0275pipeBind2"](7,4,e.payment_date,"dd MMM YYYY"):"-"),a["\u0275\u0275advance"](3),a["\u0275\u0275textInterpolate"](e.type)}}function gt(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div"),a["\u0275\u0275elementStart"](1,"div",170),a["\u0275\u0275elementStart"](2,"mat-icon",164),a["\u0275\u0275text"](3," history "),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](4,"span",165),a["\u0275\u0275text"](5," Partial payment history"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](6,"div",33),a["\u0275\u0275elementStart"](7,"table",36),a["\u0275\u0275elementStart"](8,"thead"),a["\u0275\u0275elementStart"](9,"tr"),a["\u0275\u0275elementStart"](10,"th",37),a["\u0275\u0275text"](11,"#"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](12,"th",37),a["\u0275\u0275text"](13,"Amount paid"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](14,"th",37),a["\u0275\u0275text"](15,"Payment date"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](16,"th",37),a["\u0275\u0275text"](17,"Payment Type"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](18,"tbody"),a["\u0275\u0275template"](19,ht,10,7,"tr",32),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"](2);a["\u0275\u0275advance"](19),a["\u0275\u0275property"]("ngForOf",e.partialPaymentDetails)}}function xt(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"form",5),a["\u0275\u0275elementStart"](1,"div",6),a["\u0275\u0275elementStart"](2,"div",7),a["\u0275\u0275elementStart"](3,"div",8),a["\u0275\u0275elementStart"](4,"mat-icon",9),a["\u0275\u0275text"](5," credit_card "),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](6,"span",10),a["\u0275\u0275text"](7," Payment entry "),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](8,"span",11),a["\u0275\u0275text"](9),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](10,"div",12),a["\u0275\u0275text"](11),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](12,M,4,0,"div",13),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](13,"div",14),a["\u0275\u0275elementStart"](14,"mat-horizontal-stepper",15,16),a["\u0275\u0275listener"]("selectionChange",(function(t){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"]().selectionChange(t)})),a["\u0275\u0275template"](16,Pe,11,3,"mat-step",17),a["\u0275\u0275template"](17,ze,24,6,"mat-step",17),a["\u0275\u0275elementStart"](18,"mat-step"),a["\u0275\u0275template"](19,Ue,1,0,"ng-template",18),a["\u0275\u0275template"](20,Ye,4,3,"div",19),a["\u0275\u0275elementStart"](21,"div",20),a["\u0275\u0275template"](22,ct,21,3,"div",21),a["\u0275\u0275elementStart"](23,"div",22),a["\u0275\u0275template"](24,pt,2,1,"ng-container",23),a["\u0275\u0275template"](25,yt,2,1,"ng-container",23),a["\u0275\u0275template"](26,_t,33,5,"ng-template",null,24,a["\u0275\u0275templateRefExtractor"]),a["\u0275\u0275template"](28,gt,20,1,"ng-template",null,25,a["\u0275\u0275templateRefExtractor"]),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](30,"pre"),a["\u0275\u0275text"](31,"                        "),a["\u0275\u0275text"](32,"\n\n\n                    "),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()}if(2&e){const e=a["\u0275\u0275nextContext"]();a["\u0275\u0275property"]("formGroup",e.paymentEntry),a["\u0275\u0275advance"](9),a["\u0275\u0275textInterpolate1"](" ",e.itemDetails.description," "),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate1"](" (Total : ",e.totalVendorAmount,") "),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",e.isPopUpMode),a["\u0275\u0275advance"](4),a["\u0275\u0275property"]("ngIf",0==e.blockTreasuryFeatures),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",0==e.blockTreasuryFeatures),a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("ngIf","edit"==e.statutoryViewMode),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("ngIf",0==e.blockTreasuryFeatures),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("ngIf",e.showTransationSummary),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",e.showPartialPaymentSummary)}}let vt=(()=>{class e{constructor(e,t,n,r,l,s){this.dialogRef=e,this.fb=t,this.inData=n,this._bottomSheet=r,this._p2pGeneralService=l,this._util=s,this.isPopUpMode=!0,this.vendorInvoiceLedgerResult=new a.EventEmitter,this.paymentEntry=this.fb.group({isChecked:[!1],expenseEntry:this.fb.array([]),totalExpenseAmount:[0],taxAddition:this.fb.array([]),totalTaxAddition:[0],taxDeduction:this.fb.array([]),totalTaxDeduction:[0],overallTaxAmount:[0],vendorAccount:["",m.H.required],chequeNo:[""],chequeAmount:["",m.H.required],voucherNo:[""],bank:[""],vendorDescription:["",[m.H.required,m.H.maxLength(254)]],vendorRefType:[""],finalAmount:[0],financeBooksfm:[""]}),this.financeBooksAvailable=!1,this.financialBooksMasterData=[],this.ccExpenseEntry=[],this.subGroupMasterData=[],this.legalEntityMasterData=[],this.taxAdditionType=[],this.taxDeductionType=[],this.glAccountForAddition=[],this.glAccountForDeduction=[],this.selectedGlAccount=[],this.entitySelectionArray=[],this.taxAdditionForConfig=[],this.showTransationSummary=!0,this.showPartialPaymentSummary=!1,this.$onDestroy=new b.b,this.statutoryViewMode="edit",this.is_partial_payment=!1,this.isTaxDetailMasterDataAvailable=!1,this.isGlAccMasterDataAvailable=!1,this.isLegalEntityMasterDataAvailable=!1,this.blockTreasuryFeatures=!1,this.vendorReferenceTypes=[],this.partialPaymentDetails=[],this.isPaymentCompleted=!1,this.isChequeNoMandatory=!1,this.isChecked=!1,this.paymentsSum=0,this.advCheck=!1,this.isLoading=!1,this.isPatchingInvoicePrEnabled=!1,this.isDraftSpinnerLoading=!1,this.isLedgerPopUpSpinnerLoading=!1,this.tdsNatureOnLedgerEntry=!1,this.tdsNatureList=[],this.isTDSNatureApplicable=!1,this.taxabilityTypeonLedgerEntry=!1,this.isTaxabilityTypeApplicable=!1,this.HSNSAConLedgerEntry=!1,this.isHSNSACApplicable=!1,this.isHSNSACDetailsApplicable=!1,this.sessionId="",this.getPartialPaymentDetails=()=>Object(i.c)(this,void 0,void 0,(function*(){let e=1==this.itemDetails.payment_model_id?0:this.itemDetails.selectedMilestone[0].milestone_id;yield this._p2pGeneralService.getPartialPaymentDetails(this.itemDetails.p2p_header_id,e).subscribe(e=>{if("S"==e.messType){this.partialPaymentDetails=e.data,console.log("partial payments list",e.data),this.paymentsSum=0;for(let t of e.data)this.paymentsSum+=t.amount_paid,"Advance"==t.type&&(this.advCheck=!0);console.log("payments sum",this.paymentsSum),this.showPartialPaymentSummary=!0}this.checkIfPaymentCompleted()},e=>{console.log(e)})})),this.checkIfPaymentCompleted=()=>{var e,t;if(1==this.isMovedToBank)this.isPaymentCompleted=!0;else if(3==this.isMovedToBank){let t=0;this.partialPaymentDetails.forEach(e=>{t+=e.amount_paid});let n=parseFloat(null===(e=this.inPrefilledJson)||void 0===e?void 0:e.StatutoryDetails.finalAmount);this.isPaymentCompleted=t==n}else if(2==this.isMovedToBank){let e=0;this.partialPaymentDetails.forEach(t=>{e+=t.amount_paid});let n=parseFloat(null===(t=this.inPrefilledJson)||void 0===t?void 0:t.StatutoryDetails.finalAmount);this.isPaymentCompleted=e==n}else this.isPaymentCompleted=!1},this.setExpenseEntryViewModeData=()=>{this.inPrefilledJson.StatutoryDetails.expenseEntry.forEach((e,t)=>{2==this.itemDetails.payment_model_id||3==this.itemDetails.payment_model_id?e.milestone_entries.forEach(e=>{let t=o.where(this.ccExpenseEntry,{id:e.cost_center})[0];e.cost_center=t;let n=o.where(this.subGroupMasterData,{id:e.sub_group_id})[0];e.subGroupDetail=n;let i=o.where(this.legalEntityMasterData,{id:e.source_entity_id})[0];e.sourceEntityDetail=i}):1==this.itemDetails.payment_model_id&&e.sub_group_entries.forEach(e=>{let t=o.where(this.ccExpenseEntry,{id:e.cost_center})[0];e.cost_center=t;let n=o.where(this.legalEntityMasterData,{id:e.source_entity_id})[0];e.sourceEntityDetail=n,this._p2pGeneralService.getPRTypeLedgerMaster(e.source_entity_id).pipe(Object(S.a)(this.$onDestroy)).subscribe(t=>{if("S"==t.messType){let n=o.where(t.data,{id:e.gl_account})[0].name;e.gl_account_name=n}})})})},this.setBankDetailViewModeData=()=>{var e,t,n,i,a,r,l,s,d,c,m,p,u;let y;this.paymentEntry.get("chequeNo").patchValue(null===(e=this.inPrefilledJson)||void 0===e?void 0:e.StatutoryDetails.chequeNo),this.paymentEntry.get("voucherNo").patchValue(null===(t=this.inPrefilledJson)||void 0===t?void 0:t.StatutoryDetails.voucherNo),this.paymentEntry.get("finalAmount").patchValue(null===(n=this.inPrefilledJson)||void 0===n?void 0:n.StatutoryDetails.finalAmount),this.paymentEntry.get("overallTaxAmount").patchValue(null===(i=this.inPrefilledJson)||void 0===i?void 0:i.StatutoryDetails.overallTaxAmount),this.paymentEntry.get("totalTaxAddition").patchValue(null===(a=this.inPrefilledJson)||void 0===a?void 0:a.StatutoryDetails.totalTaxAddition),this.paymentEntry.get("totalTaxDeduction").patchValue(null===(r=this.inPrefilledJson)||void 0===r?void 0:r.StatutoryDetails.totalTaxDeduction),this.paymentEntry.get("totalExpenseAmount").patchValue(null===(l=this.inPrefilledJson)||void 0===l?void 0:l.StatutoryDetails.totalExpenseAmount),this.paymentEntry.get("bank").patchValue(null===(s=this.inPrefilledJson)||void 0===s?void 0:s.StatutoryDetails.bank),this.paymentEntry.get("vendorDescription").patchValue((null===(d=this.inPrefilledJson)||void 0===d?void 0:d.StatutoryDetails.vendorDescription)?null===(c=this.inPrefilledJson)||void 0===c?void 0:c.StatutoryDetails.vendorDescription:""),this.paymentEntry.get("vendorDescription").disable({onlySelf:!0}),o.each(this.vendorReferenceTypes,e=>{var t;e.name===(null===(t=this.inPrefilledJson)||void 0===t?void 0:t.StatutoryDetails.vendorRefType)&&(y=e.id)}),this.paymentEntry.get("vendorRefType").patchValue((null===(m=this.inPrefilledJson)||void 0===m?void 0:m.StatutoryDetails.vendorDescription)?y:" "),this.paymentEntry.get("vendorRefType").disable({onlySelf:!0}),this.paymentEntry.get("financeBooksfm").patchValue((null===(p=this.inPrefilledJson)||void 0===p?void 0:p.financeBooks)?null===(u=this.inPrefilledJson)||void 0===u?void 0:u.financeBooks:" "),this.paymentEntry.get("financeBooksfm").disable({onlySelf:!0})},this.setCcDropDownData=()=>{let e=[];e=o.uniq(this.itemDetails.l2,e=>e.cost_center);let t=0;e=e.map(e=>(t++,{id:t,name:e.cost_center_name,code:e.cost_center_code,source_entity_id:e.source_entity_id,source_tally_entity_name:e.source_tally_entity_name})),this.ccExpenseEntry=e},this.setSubgroupDropDownData=()=>{let e=[];o.each(this.itemDetails.l2,t=>{e.push({id:t.sub_group_id,name:t.sub_group_name})}),this.subGroupMasterData=e},this.patchPrAndInvoiceNumber=()=>{var e,t,n,i,a,o,r,l,s,d;this.isPatchingInvoicePrEnabled&&(this.patchedPrAndInvoiceValue=1==(null===(e=this.itemDetails)||void 0===e?void 0:e.payment_model_id)?(null===(t=this.itemDetails)||void 0===t?void 0:t.p2p_header_id)&&(null===(n=this.itemDetails)||void 0===n?void 0:n.invoice_id)?"PR - "+this.itemDetails.p2p_header_id+" Inv Id - "+this.itemDetails.invoice_id:null:(2==(null===(i=this.itemDetails)||void 0===i?void 0:i.payment_model_id)||3==(null===(a=this.itemDetails)||void 0===a?void 0:a.payment_model_id))&&(null===(o=this.itemDetails)||void 0===o?void 0:o.p2p_header_id)&&(null===(l=null===(r=this.itemDetails)||void 0===r?void 0:r.selectedMilestone[0])||void 0===l?void 0:l.invoice_id)?"PR - "+this.itemDetails.p2p_header_id+" Inv Id - "+(null===(d=null===(s=this.itemDetails)||void 0===s?void 0:s.selectedMilestone[0])||void 0===d?void 0:d.invoice_id):null,console.log(this.patchedPrAndInvoiceValue),this.paymentEntry.get("vendorDescription").patchValue(this.patchedPrAndInvoiceValue))},this.inPrefilledJson=this.inData.jsonData,console.log("LedgerEntry Section prefilledJson : ",this.inPrefilledJson),null!=this.inPrefilledJson&&(this.statutoryViewMode="readOnly"),this.persona=this.inData.persona,"requestorORapprover"===this.persona&&(this.blockTreasuryFeatures=!0),this.inData&&this.inData.modalParams&&(this.itemDetails=this.inData.modalParams),console.log("LedgerEntry Section disbrusal : ",this.itemDetails),this.itemDetails&&(this.itemDetails.vendor_preferred_currency=this.itemDetails.vendor_preferred_currency?this.itemDetails.vendor_preferred_currency:"INR"),this.getSessionID()}ngOnInit(){return Object(i.c)(this,void 0,void 0,(function*(){this.isPopUpMode||(console.log("this.isPopUpMode value : ",this.isPopUpMode),this.itemDetails=this.vendorInvoiceDetail,this.inPrefilledJson=this.jsonData,null!=this.inPrefilledJson&&(this.statutoryViewMode="readOnly"),this.itemDetails.vendor_preferred_currency=this.itemDetails.vendor_preferred_currency?this.itemDetails.vendor_preferred_currency:"INR",console.log("this.itemDetails : ",this.itemDetails),console.log("this.inPrefilledJson : ",this.inPrefilledJson)),yield this._p2pGeneralService.getP2pTenantConfig("LPTN").pipe(Object(S.a)(this.$onDestroy)).subscribe(e=>{"S"==e.messType&&(this.tdsNatureOnLedgerEntry=!0,this._p2pGeneralService.getTDSNatureDetails().subscribe(e=>{this.tdsNatureList="S"==e.messType?e.data:[]},e=>{console.log(e)}))},e=>{console.log(e)}),yield this._p2pGeneralService.getP2pTenantConfig("TTLE").pipe(Object(S.a)(this.$onDestroy)).subscribe(e=>Object(i.c)(this,void 0,void 0,(function*(){"S"==e.messType&&(this.taxabilityTypeonLedgerEntry=!0,yield this._p2pGeneralService.getTaxabilityTypeList().subscribe(e=>{"S"==e.messType?(this.taxabilityTypeDetails=e.data,this.taxabilityTypeDetailsPlaceHolder=this.taxabilityTypeDetails[0].placeholder_name):(this.taxabilityTypeDetails=[],this.taxabilityTypeDetailsPlaceHolder="GST Rate")},e=>{console.log(e)}))})),e=>{console.log(e)}),yield this._p2pGeneralService.getP2pTenantConfig("HSNSACLE").pipe(Object(S.a)(this.$onDestroy)).subscribe(e=>{"S"==e.messType&&(this.HSNSAConLedgerEntry=!0)},e=>{console.log(e)}),yield this._p2pGeneralService.getLegalEntityMasterData().pipe(Object(S.a)(this.$onDestroy)).subscribe(e=>Object(i.c)(this,void 0,void 0,(function*(){"S"==e.messType&&(this.legalEntityMasterData=e.data,this.isLegalEntityMasterDataAvailable=!0,console.log("getLegalEntityMasterData on ngOnInit: ",this.legalEntityMasterData),yield this.getDraftedDataforInvoice())})),e=>{console.log(e)}),yield this._p2pGeneralService.getP2pTenantConfig("EFB").pipe(Object(S.a)(this.$onDestroy)).subscribe(e=>{var t,n;if("S"==e.messType){if(this.financeBooksAvailable=!0,this._p2pGeneralService.getFinancialBooks().pipe(Object(S.a)(this.$onDestroy)).subscribe(e=>{"S"==e.messType&&(this.financialBooksMasterData=e.data,this.financialBooksMasterDataList=this.financialBooksMasterData,console.log("ent",this.financialBooksMasterDataList))},e=>{console.log(e)}),this.paymentEntry.get("financeBooksfm").setValidators([m.H.required]),null===(t=this.inPrefilledJson)||void 0===t?void 0:t.financeBooks){let e=null===(n=this.inPrefilledJson)||void 0===n?void 0:n.financeBooks;this.paymentEntry.get("financeBooksfm").patchValue(e),this.paymentEntry.get("financeBooksfm").setValue(e),console.log(this.paymentEntry)}}else this.financeBooksAvailable=!1,this.paymentEntry.get("financeBooksfm").clearValidators(),console.log("false",this.financeBooksAvailable)},e=>{console.log(e)});try{let e=yield this._p2pGeneralService.getP2pTenantConfig("IPIN").pipe(Object(S.a)(this.$onDestroy)).toPromise();this.isPatchingInvoicePrEnabled="S"==e.messType}catch(e){console.log(e),this.isPatchingInvoicePrEnabled=!1}yield this._p2pGeneralService.getP2pTenantConfig("RNM").pipe(Object(S.a)(this.$onDestroy)).subscribe(e=>{"S"==e.messType?(this.isChequeNoMandatory=!0,this.paymentEntry.get("chequeNo").setValidators([m.H.required])):(this.isChequeNoMandatory=!1,this.paymentEntry.get("chequeNo").clearValidators())},e=>{console.log(e)}),yield this._p2pGeneralService.getP2pTenantConfig("EBT").pipe(Object(S.a)(this.$onDestroy)).subscribe(e=>{"S"==e.messType?(this.isTaxConfigured=!0,console.log("is tax config true ->",this.isTaxConfigured)):(this.isTaxConfigured=!1,console.log("is tax config false->",this.isTaxConfigured))},e=>{console.log(e)}),this.getMasterDatasForDropDown(),null!=this.retrievedDraftedData&&null!=this.retrievedDraftedData||this.setExpenseEntryDetails(),null!=this.retrievedDraftedData&&null!=this.retrievedDraftedData||this.setStatutoryEntryDetails(),this.setTransactionEntryDetails(),this.calculateTotalVendorPreferredAmount(),this.patchValueOnChanges(),this.isMovedToBank=1==this.itemDetails.payment_model_id?this.itemDetails.is_moved_to_bank:this.itemDetails.selectedMilestone[0].is_moved_to_bank,this.patchPrAndInvoiceNumber(),this.isLedgerPopUpSpinnerLoading=!0}))}get expenseEntrySubGroupControl(){return this.paymentEntry.controls.expenseEntry.controls}get taxAdditionSubGroupControl(){return this.paymentEntry.controls.taxAddition.controls}get taxDeductionSubGroupControl(){return this.paymentEntry.controls.taxDeduction.controls}get subGroupEntries(){let e=!this.paymentEntry.get("isChecked").value&&this.tdsNatureOnLedgerEntry?{tds_nature_gl_id:[""],tds_nature_gl_name:[""]}:{},t=!this.paymentEntry.get("isChecked").value&&this.taxabilityTypeonLedgerEntry?{taxability_type_id:[""],taxability_type_name:[""],taxability_type_rate:[""]}:{},n=!this.paymentEntry.get("isChecked").value&&this.HSNSAConLedgerEntry?{hsn_sac_value:["",m.H.pattern("^[0-9]*$")]}:{};return this.fb.group(Object.assign(Object.assign(Object.assign({cost_center:["",m.H.required],cc_amount:["",m.H.required],source_entity_id:["",m.H.required],source_entity_name:[""],gl_account:["",m.H.required],gl_account_master_data:[],description:["",m.H.required],sub_group_id:[""]},e),t),n))}get milestoneEntries(){let e=!this.paymentEntry.get("isChecked").value&&this.tdsNatureOnLedgerEntry?{tds_nature_gl_id:[""],tds_nature_gl_name:[""]}:{},t=!this.paymentEntry.get("isChecked").value&&this.taxabilityTypeonLedgerEntry?{taxability_type_id:[""],taxability_type_name:[""],taxability_type_rate:[""]}:{},n=!this.paymentEntry.get("isChecked").value&&this.HSNSAConLedgerEntry?{hsn_sac_value:["",m.H.pattern("^[0-9]*$")]}:{};return this.fb.group(Object.assign(Object.assign(Object.assign({cost_center:["",m.H.required],amount:["",m.H.required],sub_group_id:["",m.H.required],source_entity_id:["",m.H.required],source_entity_name:[""],source_tally_entity_name:[""],gl_account:[""],gl_account_master_data:[],description:["",m.H.required]},e),t),n))}get taxSubGroupStructure(){return this.fb.group({sub_group_ref_id:[""],sub_group_name:[""],sub_group_amount:[""],sub_group_tax_entries:this.fb.array([])})}get taxDedutionEntries(){return this.fb.group({tax_type:[""],tax_type_name:[""],tax_type_code:[""],tax_percentage:[""],tax_amount:[""],tax_deduction_gl_id:[""],tax_deduction_gl_name:[""],is_tax_deduction_gl_field_mandatory:!0,is_tax_percentage_entered_by_user:!1})}get taxAdditionEntries(){return this.fb.group({tax_type:[""],tax_type_name:[""],tax_type_code:[""],tax_percentage:[""],tax_amount:[""],tax_addition_gl_id:[""],tax_addition_gl_name:[""],is_tax_addition_gl_field_mandatory:!0,is_tax_percentage_entered_by_user:!1})}selectionChange(e){1==e.selectedIndex&&this.getExpenseTypeDropdown()}getMasterDatasForDropDown(){return Object(i.c)(this,void 0,void 0,(function*(){this.getPartialPaymentDetails(),yield this._p2pGeneralService.getTaxDetailMasterData().pipe(Object(S.a)(this.$onDestroy)).subscribe(e=>Object(i.c)(this,void 0,void 0,(function*(){if("S"==e.messType){let t=[],n=[];for(let i of e.data)i.is_deduction?t.push({id:i.id,name:i.description,percentage:i.tax_percentage,code:i.tax_code,entity_id:i.entity_id,is_deduction:i.is_deduction}):n.push({id:i.id,name:i.description,percentage:i.tax_percentage,code:i.tax_code,entity_id:i.entity_id,is_deduction:i.is_deduction});this.taxDeductionType=t,this.taxAdditionType=n,this.taxAdditionForConfig=this.taxAdditionType,this.isTaxDetailMasterDataAvailable=!0}yield this._p2pGeneralService.getTaxGLAccountDetails().pipe(Object(S.a)(this.$onDestroy)).subscribe(e=>Object(i.c)(this,void 0,void 0,(function*(){if("S"==e.messType){let t=[],n=[];for(let i of e.data)"add"==i.gl_type?t.push({id:i.id,name:i.gl_name}):n.push({id:i.id,name:i.gl_name});this.glAccountForAddition=t,this.glAccountForDeduction=n,this.isGlAccMasterDataAvailable=!0}yield this._p2pGeneralService.getLegalEntityMasterData().pipe(Object(S.a)(this.$onDestroy)).subscribe(e=>{"S"==e.messType&&(this.legalEntityMasterData=e.data,this.isLegalEntityMasterDataAvailable=!0),this.setCcDropDownData(),this.setSubgroupDropDownData(),"readOnly"==this.statutoryViewMode&&(this.getPartialPaymentDetails(),this.setExpenseEntryViewModeData(),this.setBankDetailViewModeData(),this.patchFinalAmount())},e=>{console.log(e)})})),e=>{console.log(e)})})),e=>{console.log(e)}),yield this._p2pGeneralService.getVendorReferenceTypes().subscribe(e=>{"S"==e.messType&&(this.vendorReferenceTypes=e.data)},e=>{console.log(e)})}))}getExpenseTypeDropdown(){let e=[];if(1==this.itemDetails.payment_model_id){let t=1;for(let n=0;n<this.paymentEntry.value.expenseEntry.length;n++){let i=this.paymentEntry.value.expenseEntry[n];for(let a=0;a<i.sub_group_entries.length;a++){let r=i.sub_group_entries[a];if(""!=r.gl_account&&null!=r.gl_account){let i=JSON.parse(JSON.stringify(this.paymentEntry.controls.expenseEntry.controls[n].controls.sub_group_entries.controls[a].controls.gl_account_master_data));if(i.length>0){let n=o.findWhere(i,{id:r.gl_account});n.sub_group_id=n.id,n.id=t++;let a=Object.assign({sub_group_amount:r.cc_amount},n);e.push(a)}}}}}else{let t=1;for(let n=0;n<this.paymentEntry.value.expenseEntry.length;n++){let i=this.paymentEntry.value.expenseEntry[n];for(let a=0;a<i.milestone_entries.length;a++){let r=i.milestone_entries[a];if(""!=r.gl_account&&null!=r.gl_account){let i=JSON.parse(JSON.stringify(this.paymentEntry.controls.expenseEntry.controls[n].controls.milestone_entries.controls[a].controls.gl_account_master_data));if(i.length>0){let n=o.findWhere(i,{id:r.gl_account});n.sub_group_id=n.id,n.id=t++;let a=Object.assign({sub_group_amount:r.amount},n);e.push(a)}}}}}this.selectedGlAccount=e}calculateTotalVendorPreferredAmount(){let e=this.itemDetails.vendor_preferred_currency,t=o.findWhere(this.itemDetails.amount,{currency_code:e});this.totalVendorAmount=t?t.value+" "+e:"-"}calculateSubGroupItemTotal(e){e.amount="string"==typeof e.amount?JSON.parse(e.amount):e.amount;let t=o.findWhere(e.amount,{currency_code:this.itemDetails.vendor_preferred_currency});return t?t.value:0}getMilestoneAmount(e){if(e.milestone_amount){e.milestone_amount="string"==typeof e.milestone_amount?JSON.parse(e.milestone_amount):e.milestone_amount;let t=o.findWhere(e.milestone_amount,{currency_code:this.itemDetails.vendor_preferred_currency});return t?t.value:0}return e.amount}patchValueOnChanges(){this.paymentEntry.get("expenseEntry").valueChanges.pipe(Object(D.a)(300)).subscribe(e=>{this.onExpenseEntryValueChange(e)}),this.paymentEntry.get("taxAddition").valueChanges.subscribe(e=>{this.onTaxAdditionValueChange(e)}),this.paymentEntry.get("taxDeduction").valueChanges.subscribe(e=>{this.onTaxDeductionValueChange(e)}),this.paymentEntry.get("chequeAmount").valueChanges.subscribe(e=>{this.handleChequeAmountChange(e)})}setExpenseEntryDetails(){let e=this.paymentEntry.controls.expenseEntry;2==this.itemDetails.payment_model_id||3==this.itemDetails.payment_model_id?this.itemDetails.selectedMilestone.forEach((t,n)=>{e.push(this.fb.group({milestone_name:t.milestone_name,milestone_amount:this.getMilestoneAmount(t),is_milestone_amount_exceeded:!1,milestone_entries:this.fb.array([])})),t.ccSplitupDetails.forEach((e,t)=>{let i=this.paymentEntry.controls.expenseEntry.controls[n].controls.milestone_entries;const a=this.milestoneEntries;e.amount="string"==typeof e.amount?JSON.parse(e.amount):e.amount;let r=o.findWhere(e.amount,{currency_code:this.itemDetails.vendor_preferred_currency});a.get("amount").patchValue(r.value),i.push(a)})}):this.itemDetails.l2.forEach((t,n)=>{e.push(this.fb.group({sub_group_id:t.sub_group_id,sub_group_name:t.sub_group_name,sub_group_amount:this.calculateSubGroupItemTotal(t),vendor_currency:this.itemDetails.vendor_preferred_currency,is_sub_group_amt_exceeded:!1,sub_group_entries:this.fb.array([])})),this.paymentEntry.controls.expenseEntry.controls[n].controls.sub_group_entries.push(this.subGroupEntries)})}addSubGroupEntry(e){1==this.itemDetails.payment_model_id?this.paymentEntry.controls.expenseEntry.controls[e].controls.is_sub_group_amt_exceeded?this._p2pGeneralService.showMessage("Cannot add fields, since maximum amount is reached"):this.paymentEntry.controls.expenseEntry.controls[e].controls.sub_group_entries.push(this.subGroupEntries):this.paymentEntry.controls.expenseEntry.controls[e].controls.milestone_entries.push(this.milestoneEntries)}removeSubGroupEntry(e,t){1==this.itemDetails.payment_model_id?this.paymentEntry.controls.expenseEntry.controls[e].controls.sub_group_entries.length>1&&this.paymentEntry.controls.expenseEntry.controls[e].controls.sub_group_entries.removeAt(t):this.paymentEntry.controls.expenseEntry.controls[e].controls.milestone_entries.length>1&&this.paymentEntry.controls.expenseEntry.controls[e].controls.milestone_entries.removeAt(t)}setStatutoryEntryDetails(){this.paymentEntry.controls.taxAddition.push(this.taxSubGroupStructure),this.addTaxAdditionEntry(0),this.paymentEntry.controls.taxDeduction.push(this.taxSubGroupStructure),this.addTaxDeductionEntry(0)}addTaxSubGroupAddition(){let e=this.paymentEntry.controls.taxAddition;e.push(this.taxSubGroupStructure),this.addTaxAdditionEntry(e.length-1)}removeTaxSubGroupAddition(e){let t=this.paymentEntry.controls.taxAddition;t.length>1&&t.removeAt(e)}addTaxAdditionEntry(e){this.paymentEntry.controls.taxAddition.controls[e].controls.sub_group_tax_entries.push(this.taxAdditionEntries)}removeTaxAdditionEntry(e,t){let n=this.paymentEntry.controls.taxAddition.controls[e].controls.sub_group_tax_entries;n.length>1&&n.removeAt(t)}addTaxSubGroupDeduction(){let e=this.paymentEntry.controls.taxDeduction;e.push(this.taxSubGroupStructure),this.addTaxDeductionEntry(e.length-1)}removeTaxSubGroupDeduction(e){let t=this.paymentEntry.controls.taxDeduction;t.length>1&&t.removeAt(e)}addTaxDeductionEntry(e){this.paymentEntry.controls.taxDeduction.controls[e].controls.sub_group_tax_entries.push(this.taxDedutionEntries)}removeTaxDeductionEntry(e,t){let n=this.paymentEntry.controls.taxDeduction.controls[e].controls.sub_group_tax_entries;n.length>1&&n.removeAt(t)}setTransactionEntryDetails(){if(this.paymentEntry.get("vendorAccount").patchValue(this.itemDetails.vendor_name),1==this.itemDetails.payment_model_id)this.paymentEntry.get("voucherNo").patchValue(this.itemDetails.invoice_number);else if(2==this.itemDetails.payment_model_id||3==this.itemDetails.payment_model_id){let e=this.itemDetails.selectedMilestone[0].invoice_number;this.paymentEntry.get("voucherNo").patchValue(e)}}onExpenseEntryValueChange(e){return Object(i.c)(this,void 0,void 0,(function*(){if(console.log("onExpenseEntryValueChange data : ",e),this.targetEntityClick(),1==this.itemDetails.payment_model_id){let t=0;for(let n=0;n<e.length;n++){let i,a=0;for(let r of e[n].sub_group_entries){let l=e[n].sub_group_entries.indexOf(r);if(0==l&&""!=r.source_entity_id&&(i=o.findWhere(this.legalEntityMasterData,{id:r.source_entity_id})),i&&0!=l&&this.paymentEntry.controls.expenseEntry.controls[n].controls.sub_group_entries.controls[l].controls.source_entity_id.patchValue(i.id,{emitEvent:!1}),this.paymentEntry.controls.expenseEntry.controls[n].controls.sub_group_entries.controls[l].controls.source_entity_name.patchValue(i.name,{emitEvent:!1}),yield this.getGLAccountMasterData(r,n,l,this.itemDetails.payment_model_id),""!=r.cc_amount&&""!=r.c_currency&&(r.cc_amount=parseFloat(r.cc_amount),a+=parseFloat(r.cc_amount.toFixed(2)),this.paymentEntry.controls.expenseEntry.controls[n].controls.is_sub_group_amt_exceeded=a>e[n].sub_group_amount,t+=parseFloat(r.cc_amount.toFixed(2)),this.paymentEntry.get("totalExpenseAmount").patchValue(t,{eventEmit:!1}),this.patchFinalAmount()),this.tdsNatureOnLedgerEntry&&this.isTDSNAtureAllowed(this.paymentEntry.controls.expenseEntry.controls[n].controls.sub_group_entries.controls[l].controls.gl_account.value,n,l,this.itemDetails.payment_model_id)&&""!=this.paymentEntry.controls.expenseEntry.controls[n].controls.sub_group_entries.controls[l].controls.tds_nature_gl_id.value&&null!=this.paymentEntry.controls.expenseEntry.controls[n].controls.sub_group_entries.controls[l].controls.tds_nature_gl_id.value){let e=o.findWhere(this.tdsNatureList,{id:r.tds_nature_gl_id});this.paymentEntry.controls.expenseEntry.controls[n].controls.sub_group_entries.controls[l].controls.tds_nature_gl_name.patchValue(e.name,{emitEvent:!1})}if(this.taxabilityTypeonLedgerEntry&&this.isGSTRateonLedgerAllowed(this.paymentEntry.controls.expenseEntry.controls[n].controls.sub_group_entries.controls[l].controls.gl_account.value,n,l,this.itemDetails.payment_model_id)&&""!=this.paymentEntry.controls.expenseEntry.controls[n].controls.sub_group_entries.controls[l].controls.taxability_type_id.value&&null!=this.paymentEntry.controls.expenseEntry.controls[n].controls.sub_group_entries.controls[l].controls.taxability_type_id.value){let e=o.findWhere(this.taxabilityTypeDetails,{id:r.taxability_type_id});this.paymentEntry.controls.expenseEntry.controls[n].controls.sub_group_entries.controls[l].controls.taxability_type_name.patchValue(e.name,{emitEvent:!1})}}}}else{let t=0;for(let n=0;n<e.length;n++){let i,a=0;for(let r of e[n].milestone_entries){let l=e[n].milestone_entries.indexOf(r);if(0==l&&""!=r.source_entity_id&&(i=o.findWhere(this.legalEntityMasterData,{id:r.source_entity_id})),i&&0!=l&&this.paymentEntry.controls.expenseEntry.controls[n].controls.milestone_entries.controls[l].controls.source_entity_id.patchValue(i.id,{emitEvent:!1}),this.paymentEntry.controls.expenseEntry.controls[n].controls.milestone_entries.controls[l].controls.source_entity_name.patchValue(i.name,{emitEvent:!1}),yield this.getGLAccountMasterData(r,n,l,this.itemDetails.payment_model_id),""!=r.amount){r.amount=parseFloat(r.amount),a+=parseFloat(r.amount.toFixed(2));let i=a>parseFloat(e[n].milestone_amount);this.paymentEntry.controls.expenseEntry.controls[n].controls.is_milestone_amount_exceeded=i,t+=parseFloat(r.amount.toFixed(2)),this.paymentEntry.get("totalExpenseAmount").patchValue(t,{eventEmit:!1}),this.patchFinalAmount()}if(this.tdsNatureOnLedgerEntry&&this.isTDSNAtureAllowed(this.paymentEntry.controls.expenseEntry.controls[n].controls.milestone_entries.controls[l].controls.gl_account.value,n,l,this.itemDetails.payment_model_id)&&""!=this.paymentEntry.controls.expenseEntry.controls[n].controls.milestone_entries.controls[l].controls.tds_nature_gl_id.value&&null!=this.paymentEntry.controls.expenseEntry.controls[n].controls.milestone_entries.controls[l].controls.tds_nature_gl_id.value){let e=o.findWhere(this.tdsNatureList,{id:r.tds_nature_gl_id});this.paymentEntry.controls.expenseEntry.controls[n].controls.milestone_entries.controls[l].controls.tds_nature_gl_name.patchValue(e.name,{emitEvent:!1})}if(this.taxabilityTypeonLedgerEntry&&this.isGSTRateonLedgerAllowed(this.paymentEntry.controls.expenseEntry.controls[n].controls.milestone_entries.controls[l].controls.gl_account.value,n,l,this.itemDetails.payment_model_id)&&""!=this.paymentEntry.controls.expenseEntry.controls[n].controls.milestone_entries.controls[l].controls.taxability_type_id.value&&null!=this.paymentEntry.controls.expenseEntry.controls[n].controls.milestone_entries.controls[l].controls.taxability_type_id.value){let e=o.findWhere(this.taxabilityTypeDetails,{id:r.taxability_type_id});this.paymentEntry.controls.expenseEntry.controls[n].controls.milestone_entries.controls[l].controls.taxability_type_name.patchValue(e.name,{emitEvent:!1})}}}}}))}patchTargetEntity(e,t,n,i){0==e&&""!=n.source_entity_id&&(i=o.findWhere(this.legalEntityMasterData,{id:n.source_entity_id})),i&&0!=e&&(this.paymentEntry.controls.expenseEntry.controls[t].controls.sub_group_entries.controls[e].controls.source_entity_id.patchValue(i.id,{emitEvent:!1}),this.paymentEntry.controls.expenseEntry.controls[t].controls.sub_group_entries.controls[e].controls.source_entity_name.patchValue(i.name,{emitEvent:!1}))}getGLAccountMasterData(e,t,n,a){return Object(i.c)(this,void 0,void 0,(function*(){1==a?yield this._p2pGeneralService.getPRTypeLedgerMaster(e.source_entity_id).pipe(Object(S.a)(this.$onDestroy)).subscribe(e=>{"S"==e.messType&&(this.paymentEntry.controls.expenseEntry.controls[t].controls.sub_group_entries.controls[n].controls.gl_account_master_data=e.data)}):yield this._p2pGeneralService.getPRTypeLedgerMaster(e.source_entity_id).pipe(Object(S.a)(this.$onDestroy)).subscribe(e=>{"S"==e.messType&&(this.paymentEntry.controls.expenseEntry.controls[t].controls.milestone_entries.controls[n].controls.gl_account_master_data=e.data)})}))}patchFinalAmount(){let e=parseFloat(this.paymentEntry.get("overallTaxAmount").value),t=this.paymentEntry.get("totalExpenseAmount").value,n=parseFloat(e+t).toFixed(2);if(this.paymentEntry.get("finalAmount").patchValue(n),this.paymentsSum>0){console.log("paymes",parseFloat(n)-this.paymentsSum);let e=parseFloat(n)-this.paymentsSum;this.paymentEntry.get("chequeAmount").patchValue(e.toString())}else this.paymentEntry.get("chequeAmount").patchValue(n)}onTaxAdditionValueChange(e){return Object(i.c)(this,void 0,void 0,(function*(){let t=0;for(let i=0;i<e.length;i++){let n=e[i];if(""!=n.sub_group_ref_id){let a=o.findWhere(this.selectedGlAccount,{id:n.sub_group_ref_id});this.paymentEntry.controls.taxAddition.controls[i].controls.sub_group_name.patchValue(a.name,{emitEvent:!1}),this.paymentEntry.controls.taxAddition.controls[i].controls.sub_group_amount.patchValue(a.sub_group_amount,{emitEvent:!1});for(let r=0;r<e[i].sub_group_tax_entries.length;r++){let a=e[i].sub_group_tax_entries[r];if(""!=a.tax_addition_gl_id&&null!=a.tax_addition_gl_id){let e=o.findWhere(this.glAccountForAddition,{id:a.tax_addition_gl_id});this.paymentEntry.controls.taxAddition.controls[i].controls.sub_group_tax_entries.controls[r].controls.tax_addition_gl_name.patchValue(e.name,{emitEvent:!1})}if(""!=a.tax_type&&null!=a.tax_type){let e=o.findWhere(this.taxAdditionType,{id:a.tax_type});this.paymentEntry.controls.taxAddition.controls[i].controls.sub_group_tax_entries.controls[r].controls.tax_type_name.patchValue(e.name,{emitEvent:!1}),this.paymentEntry.controls.taxAddition.controls[i].controls.sub_group_tax_entries.controls[r].controls.tax_type_code.patchValue(e.code,{emitEvent:!1}),"NTA"==e.code?(this.paymentEntry.controls.taxAddition.controls[i].controls.sub_group_tax_entries.controls[r].controls.is_tax_percentage_entered_by_user.patchValue(!1,{emitEvent:!1}),this.paymentEntry.controls.taxAddition.controls[i].controls.sub_group_tax_entries.controls[r].controls.tax_percentage.patchValue(0,{emitEvent:!1}),this.paymentEntry.controls.taxAddition.controls[i].controls.sub_group_tax_entries.controls[r].controls.tax_amount.patchValue(0,{emitEvent:!1}),this.paymentEntry.controls.taxAddition.controls[i].controls.sub_group_tax_entries.controls[r].controls.tax_addition_gl_id.patchValue("",{emitEvent:!1}),this.paymentEntry.controls.taxAddition.controls[i].controls.sub_group_tax_entries.controls[r].controls.is_tax_addition_gl_field_mandatory.patchValue(!1,{emitEvent:!1})):this.paymentEntry.controls.taxAddition.controls[i].controls.sub_group_tax_entries.controls[r].controls.is_tax_addition_gl_field_mandatory.patchValue(!0,{emitEvent:!1})}if(a.is_tax_percentage_entered_by_user)t+=a.tax_amount;else if(""!=a.tax_type&&null!=a.tax_type){let e=o.findWhere(this.taxAdditionType,{id:a.tax_type}),l=parseFloat((e.percentage/100*n.sub_group_amount).toFixed(2));this.paymentEntry.controls.taxAddition.controls[i].controls.sub_group_tax_entries.controls[r].controls.tax_percentage.patchValue(e.percentage,{emitEvent:!1}),this.paymentEntry.controls.taxAddition.controls[i].controls.sub_group_tax_entries.controls[r].controls.tax_amount.patchValue(l,{emitEvent:!1}),t+=l}}}}t=parseFloat(t.toFixed(2)),this.paymentEntry.get("totalTaxAddition").patchValue(t,{emitEvent:!1});let n=this.paymentEntry.get("totalTaxDeduction").value;this.patchOverallValues(t,n)}))}onTaxDeductionValueChange(e){return Object(i.c)(this,void 0,void 0,(function*(){let t=0;for(let i=0;i<e.length;i++){let n=e[i];if(""!=n.sub_group_ref_id){let a=o.findWhere(this.selectedGlAccount,{id:n.sub_group_ref_id});this.paymentEntry.controls.taxDeduction.controls[i].controls.sub_group_name.patchValue(a.name,{emitEvent:!1}),this.paymentEntry.controls.taxDeduction.controls[i].controls.sub_group_amount.patchValue(a.sub_group_amount,{emitEvent:!1});for(let r=0;r<e[i].sub_group_tax_entries.length;r++){let a=e[i].sub_group_tax_entries[r];if(""!=a.tax_deduction_gl_id&&null!=a.tax_deduction_gl_id){let e=o.findWhere(this.glAccountForDeduction,{id:a.tax_deduction_gl_id});this.paymentEntry.controls.taxDeduction.controls[i].controls.sub_group_tax_entries.controls[r].controls.tax_deduction_gl_name.patchValue(e.name,{emitEvent:!1})}else console.log("gl None");if(""!=a.tax_type&&null!=a.tax_type){let e=o.findWhere(this.taxDeductionType,{id:a.tax_type});this.paymentEntry.controls.taxDeduction.controls[i].controls.sub_group_tax_entries.controls[r].controls.tax_type_name.patchValue(e.name,{emitEvent:!1}),this.paymentEntry.controls.taxDeduction.controls[i].controls.sub_group_tax_entries.controls[r].controls.tax_type_code.patchValue(e.code,{emitEvent:!1}),"NTD"==e.code?(this.paymentEntry.controls.taxDeduction.controls[i].controls.sub_group_tax_entries.controls[r].controls.is_tax_percentage_entered_by_user.patchValue(!1,{emitEvent:!1}),this.paymentEntry.controls.taxDeduction.controls[i].controls.sub_group_tax_entries.controls[r].controls.tax_percentage.patchValue(0,{emitEvent:!1}),this.paymentEntry.controls.taxDeduction.controls[i].controls.sub_group_tax_entries.controls[r].controls.tax_amount.patchValue(0,{emitEvent:!1}),this.paymentEntry.controls.taxDeduction.controls[i].controls.sub_group_tax_entries.controls[r].controls.tax_deduction_gl_id.patchValue("",{emitEvent:!1}),this.paymentEntry.controls.taxDeduction.controls[i].controls.sub_group_tax_entries.controls[r].controls.is_tax_deduction_gl_field_mandatory.patchValue(!1,{emitEvent:!1})):this.paymentEntry.controls.taxDeduction.controls[i].controls.sub_group_tax_entries.controls[r].controls.is_tax_deduction_gl_field_mandatory.patchValue(!0,{emitEvent:!1})}if(a.is_tax_percentage_entered_by_user)t+=a.tax_amount;else if(""!=a.tax_type&&null!=a.tax_type){let e=o.findWhere(this.taxDeductionType,{id:a.tax_type}),l=parseFloat((e.percentage/100*n.sub_group_amount).toFixed(2));this.paymentEntry.controls.taxDeduction.controls[i].controls.sub_group_tax_entries.controls[r].controls.tax_percentage.patchValue(e.percentage,{emitEvent:!1}),this.paymentEntry.controls.taxDeduction.controls[i].controls.sub_group_tax_entries.controls[r].controls.tax_amount.patchValue(l,{emitEvent:!1}),t+=l}}}}t=parseFloat(t.toFixed(2)),this.paymentEntry.get("totalTaxDeduction").patchValue(t,{emitEvent:!1});let n=this.paymentEntry.get("totalTaxAddition").value;this.patchOverallValues(n,t)}))}onTaxAdditionPercentageChange(e,t,n){let i=parseFloat((n/100*this.paymentEntry.value.taxAddition[e].sub_group_amount).toFixed(2));this.paymentEntry.controls.taxAddition.controls[e].controls.sub_group_tax_entries.controls[t].controls.is_tax_percentage_entered_by_user.patchValue(!0,{emitEvent:!1}),this.paymentEntry.controls.taxAddition.controls[e].controls.sub_group_tax_entries.controls[t].controls.tax_amount.patchValue(i)}onTaxDeductionPercentageChange(e,t,n){let i=parseFloat((n/100*this.paymentEntry.value.taxDeduction[e].sub_group_amount).toFixed(2));this.paymentEntry.controls.taxDeduction.controls[e].controls.sub_group_tax_entries.controls[t].controls.is_tax_percentage_entered_by_user.patchValue(!0,{emitEvent:!1}),this.paymentEntry.controls.taxDeduction.controls[e].controls.sub_group_tax_entries.controls[t].controls.tax_amount.patchValue(i)}patchOverallValues(e,t){let n=(e-t).toFixed(2);this.paymentEntry.get("overallTaxAmount").patchValue(n),this.patchFinalAmount()}chooseBank(){this._bottomSheet.open(r.a,{panelClass:"custom-bank-bottom-sheet"}).afterDismissed().subscribe(e=>{e&&this.patchBankDetail(e)})}patchBankDetail(e){this.paymentEntry.get("bank").patchValue(e)}VerifyStatutoryDetails(){var e,t;if("Rejected"==(null===(e=this.itemDetails)||void 0===e?void 0:e.status)||"Closed"==(null===(t=this.itemDetails)||void 0===t?void 0:t.status))console.log("read"),this._p2pGeneralService.showMessage("This Entry cannot be posted");else if(console.log("inside verify button"),"VALID"==this.paymentEntry.status){let e={};this.isLoading=!0;let t=this.getTallyEntryFormat(),n={payment_model_id:this.itemDetails.payment_model_id,milestone_id:1==this.itemDetails.payment_model_id?null:this.itemDetails.selectedMilestone[0].milestone_id,items:this.itemDetails.l2,tallyEntry:t};this.paymentEntry.value.vendor_preferred_currency=this.itemDetails.vendor_preferred_currency;let i=o.findWhere(this.vendorReferenceTypes,{id:this.paymentEntry.value.vendorRefType});i=i?i.name:"",this.paymentEntry.value.vendorRefType=i,e=Object.assign(Object.assign({},n),this.paymentEntry.value),this._p2pGeneralService.VerifyStatutoryDetails(this.itemDetails.p2p_header_id,e,this.paymentEntry.value).pipe(Object(S.a)(this.$onDestroy)).subscribe(e=>{"S"==e.messType?(this.isMovedToBank=2,this._p2pGeneralService.showMessage(e.messText),this.closeLedgerEntry({messType:e.messType,is_moved_to_bank:e.is_moved_to_bank,payment_status:e.payment_status,headerStatus:e.headerStatus}),this.isLoading=!1):(this._p2pGeneralService.showMessage(e.messText),this.isLoading=!1)})}else{if(this.paymentEntry.get("vendorDescription").hasError("maxlength"))return void this._p2pGeneralService.showMessage("Vendor Description cannot be more than 255 characters !");if(this.HSNSAConLedgerEntry){let e=[];1==this.itemDetails.payment_model_id&&this.paymentEntry.value.expenseEntry.forEach((t,n)=>{t.sub_group_entries.forEach((t,n)=>{e.push(t.hsn_sac_value)})}),2!=this.itemDetails.payment_model_id&&3!=this.itemDetails.payment_model_id||this.paymentEntry.value.expenseEntry.forEach((t,n)=>{t.milestone_entries.forEach((t,n)=>{e.push(t.hsn_sac_value)})});const t=/^[0-9]*$/;let n=!0;for(let i of e)if(!t.test(i)){n=!1;break}if(!n)return void this._p2pGeneralService.showMessage("Please fill valid HSN/SAC value !")}else this._p2pGeneralService.showMessage("Please fill mandatory fields !"),this.isLoading=!1}}moveToBank(){var e,t;return Object(i.c)(this,void 0,void 0,(function*(){if("Rejected"==(null===(e=this.itemDetails)||void 0===e?void 0:e.status)||"Closed"==(null===(t=this.itemDetails)||void 0===t?void 0:t.status))console.log("read"),this._p2pGeneralService.showMessage("This Entry cannot be posted");else{let e={};if(""!=sessionStorage.getItem("P2PpaymentBillsSession")&&null!=sessionStorage.getItem("P2PpaymentBillsSession")&&null!=sessionStorage.getItem("P2PpaymentBillsSession")){if(this.validateMoveToBank()&&this.itemDetails){let t,n,a,o;this.itemDetails.invoice_id&&0!=this.itemDetails.is_moved_to_bank&&1!=this.itemDetails.is_moved_to_bank?(console.log("Ledger making for onetime payment"),t=this.itemDetails.invoice_id,n=this.itemDetails.milestone_id):this.itemDetails.selectedMilestone[0].invoice_id&&0!=this.itemDetails.selectedMilestone[0].is_moved_to_bank&&1!=this.itemDetails.selectedMilestone[0].is_moved_to_bank&&(console.log("Ledger making for Recurring/Split payment"),t=this.itemDetails.selectedMilestone[0].invoice_id,n=this.itemDetails.selectedMilestone[0].milestone_id),console.log("Ledger Making Invoice ID, Milestone ID : ",t,n),this._p2pGeneralService.getInvoiceAdvanceDetails(this.itemDetails.p2p_header_id,n).pipe(Object(S.a)(this.$onDestroy)).subscribe(n=>Object(i.c)(this,void 0,void 0,(function*(){if("S"==n.messType){if(a=!0,a){this.isLoading=!0,e=1==this.itemDetails.payment_model_id?{pr_id:this.itemDetails.p2p_header_id,source_entity_id:this.inPrefilledJson.StatutoryDetails.expenseEntry[0].sub_group_entries[0].sourceEntityDetail.id?this.inPrefilledJson.StatutoryDetails.expenseEntry[0].sub_group_entries[0].sourceEntityDetail.id:"",source_tally_entity_name:this.inPrefilledJson.StatutoryDetails.expenseEntry[0].sub_group_entries[0].sourceEntityDetail.tally_entity_name?this.inPrefilledJson.StatutoryDetails.expenseEntry[0].sub_group_entries[0].sourceEntityDetail.tally_entity_name:"",value:this.paymentEntry.get("chequeAmount").value,voucherNo:this.inPrefilledJson.StatutoryDetails.voucherNo?this.inPrefilledJson.StatutoryDetails.voucherNo:"",vendor_ledger_name:this.inPrefilledJson.cheque.vendorAccount?this.inPrefilledJson.cheque.vendorAccount:"",bank_ledger_name:this.paymentEntry.value.bank.bank_ledger_name?this.paymentEntry.value.bank.bank_ledger_name:"",payment_type:this.itemDetails.TYPE?this.itemDetails.TYPE:"",vendor_ref_type:this.inPrefilledJson.StatutoryDetails.vendorRefType?this.inPrefilledJson.StatutoryDetails.vendorRefType:"",vendor_description:this.inPrefilledJson.StatutoryDetails.vendorDescription?this.inPrefilledJson.StatutoryDetails.vendorDescription:""}:{pr_id:this.itemDetails.p2p_header_id,source_entity_id:this.inPrefilledJson.StatutoryDetails.expenseEntry[0].milestone_entries[0].sourceEntityDetail.id?this.inPrefilledJson.StatutoryDetails.expenseEntry[0].milestone_entries[0].sourceEntityDetail.id:"",source_tally_entity_name:this.inPrefilledJson.StatutoryDetails.expenseEntry[0].milestone_entries[0].sourceEntityDetail.tally_entity_name?this.inPrefilledJson.StatutoryDetails.expenseEntry[0].milestone_entries[0].sourceEntityDetail.tally_entity_name:"",value:this.paymentEntry.get("chequeAmount").value,voucherNo:this.inPrefilledJson.StatutoryDetails.voucherNo?this.inPrefilledJson.StatutoryDetails.voucherNo:"",vendor_ledger_name:this.inPrefilledJson.cheque.vendorAccount?this.inPrefilledJson.cheque.vendorAccount:"",bank_ledger_name:this.paymentEntry.value.bank.bank_ledger_name?this.paymentEntry.value.bank.bank_ledger_name:"",payment_type:this.itemDetails.TYPE?this.itemDetails.TYPE:"",vendor_description:this.inPrefilledJson.StatutoryDetails.vendorDescription?this.inPrefilledJson.StatutoryDetails.vendorDescription:""};let t=1==this.itemDetails.payment_model_id?0:this.itemDetails.selectedMilestone[0].milestone_id;yield this._p2pGeneralService.paymentEntryToTally(this.itemDetails.p2p_header_id,this.itemDetails.payment_model_id,t,e,this.is_partial_payment,this.isPartialPaymentCompleted).pipe(Object(S.a)(this.$onDestroy)).subscribe(e=>{"S"==e.messType?(this.isMovedToBank=e.moved_to_bank,this.closeLedgerEntry({messType:e.messType,is_moved_to_bank:e.moved_to_bank,payment_status:e.payment_status,vendor_invoice_status:e.vendor_invoice_status,headerStatus:e.headerStatus}),this._p2pGeneralService.showMessage(e.messData)):this._p2pGeneralService.showMessage("Failed to process the request !")},e=>{this._p2pGeneralService.showMessage("Payment process failed, contact KEBS team !"),console.log(e)})}}else a=!1,a||this._p2pGeneralService.getSessionIDforInvoice(t).pipe(Object(S.a)(this.$onDestroy)).subscribe(n=>Object(i.c)(this,void 0,void 0,(function*(){if("S"==n.messType)if(o=n.data[0].session_id,console.log("Retrieved Session inside API call : ",o),sessionStorage.getItem("P2PpaymentBillsSession")==o){this.isLoading=!0,e=1==this.itemDetails.payment_model_id?{pr_id:this.itemDetails.p2p_header_id,source_entity_id:this.inPrefilledJson.StatutoryDetails.expenseEntry[0].sub_group_entries[0].sourceEntityDetail.id?this.inPrefilledJson.StatutoryDetails.expenseEntry[0].sub_group_entries[0].sourceEntityDetail.id:"",source_tally_entity_name:this.inPrefilledJson.StatutoryDetails.expenseEntry[0].sub_group_entries[0].sourceEntityDetail.tally_entity_name?this.inPrefilledJson.StatutoryDetails.expenseEntry[0].sub_group_entries[0].sourceEntityDetail.tally_entity_name:"",value:this.paymentEntry.get("chequeAmount").value,voucherNo:this.inPrefilledJson.StatutoryDetails.voucherNo?this.inPrefilledJson.StatutoryDetails.voucherNo:"",vendor_ledger_name:this.inPrefilledJson.cheque.vendorAccount?this.inPrefilledJson.cheque.vendorAccount:"",bank_ledger_name:this.paymentEntry.value.bank.bank_ledger_name?this.paymentEntry.value.bank.bank_ledger_name:"",payment_type:this.itemDetails.TYPE?this.itemDetails.TYPE:"",vendor_ref_type:this.inPrefilledJson.StatutoryDetails.vendorRefType?this.inPrefilledJson.StatutoryDetails.vendorRefType:"",vendor_description:this.inPrefilledJson.StatutoryDetails.vendorDescription?this.inPrefilledJson.StatutoryDetails.vendorDescription:""}:{pr_id:this.itemDetails.p2p_header_id,source_entity_id:this.inPrefilledJson.StatutoryDetails.expenseEntry[0].milestone_entries[0].sourceEntityDetail.id?this.inPrefilledJson.StatutoryDetails.expenseEntry[0].milestone_entries[0].sourceEntityDetail.id:"",source_tally_entity_name:this.inPrefilledJson.StatutoryDetails.expenseEntry[0].milestone_entries[0].sourceEntityDetail.tally_entity_name?this.inPrefilledJson.StatutoryDetails.expenseEntry[0].milestone_entries[0].sourceEntityDetail.tally_entity_name:"",value:this.paymentEntry.get("chequeAmount").value,voucherNo:this.inPrefilledJson.StatutoryDetails.voucherNo?this.inPrefilledJson.StatutoryDetails.voucherNo:"",vendor_ledger_name:this.inPrefilledJson.cheque.vendorAccount?this.inPrefilledJson.cheque.vendorAccount:"",bank_ledger_name:this.paymentEntry.value.bank.bank_ledger_name?this.paymentEntry.value.bank.bank_ledger_name:"",payment_type:this.itemDetails.TYPE?this.itemDetails.TYPE:"",vendor_description:this.inPrefilledJson.StatutoryDetails.vendorDescription?this.inPrefilledJson.StatutoryDetails.vendorDescription:""};let t=1==this.itemDetails.payment_model_id?0:this.itemDetails.selectedMilestone[0].milestone_id;yield this._p2pGeneralService.paymentEntryToTally(this.itemDetails.p2p_header_id,this.itemDetails.payment_model_id,t,e,this.is_partial_payment,this.isPartialPaymentCompleted).pipe(Object(S.a)(this.$onDestroy)).subscribe(e=>{"S"==e.messType?(this.isMovedToBank=e.moved_to_bank,this.closeLedgerEntry({messType:e.messType,is_moved_to_bank:e.moved_to_bank,payment_status:e.payment_status,vendor_invoice_status:e.vendor_invoice_status,headerStatus:e.headerStatus}),this._p2pGeneralService.showMessage(e.messData)):this._p2pGeneralService.showMessage("Failed to process the request !")},e=>{this._p2pGeneralService.showMessage("Payment process failed, contact KEBS team !"),console.log(e)})}else this.activeBill=t,this.billPaymentProcessingBy=n.data[0].payment_processing_by,this.billPaymentMode=n.data[0].mode_of_payment,this.activePR=n.data[0].p2p_header_id,console.log(`This PR ${this.activePR}'s Invoice is under ${this.billPaymentProcessingBy} processing on the ${this.billPaymentMode} screen`);else 1==(yield this._util.openConfirmationSweetAlertWithCustom("We couldn't locate a Session ID for the selected PR Invoice !","Please refresh the page !"))?(this.dialogRef.close(),location.reload()):this.dialogRef.close()})))})))}}else 1==(yield this._util.openConfirmationSweetAlertWithCustom("We couldn't locate a Session ID for the selected Bill !","Please refresh the page !"))?(this.dialogRef.close(),location.reload()):this.dialogRef.close()}}))}getTallyEntryFormat(){let e=[];return this.paymentEntry.value.expenseEntry.forEach(1==this.itemDetails.payment_model_id?(t,n)=>{t.sub_group_entries.forEach((i,a)=>{let r=o.findWhere(this.ccExpenseEntry,{id:i.cost_center}),l=o.findWhere(this.paymentEntry.controls.expenseEntry.controls[n].controls.sub_group_entries.controls[a].controls.gl_account_master_data,{id:i.gl_account}),s=o.findWhere(this.legalEntityMasterData,{id:i.source_entity_id});e.push({sub_group_name:l.name?l.name:"",cost_center:r.code?r.code:i.cost_center,amount:i.cc_amount,source_entity_id:i.source_entity_id,source_tally_entity_name:s.tally_entity_name,description:i.description,sub_group_id:t.sub_group_id,currency_code:this.itemDetails.vendor_preferred_currency,tds_nature_gl_id:i.tds_nature_gl_id,tds_nature_gl_name:i.tds_nature_gl_name,taxability_type_name:i.taxability_type_name,taxability_type_rate:i.taxability_type_rate,hsn_sac_value:i.hsn_sac_value})})}:(t,n)=>{t.milestone_entries.forEach((t,i)=>{let a=o.findWhere(this.ccExpenseEntry,{id:t.cost_center}),r=o.findWhere(this.paymentEntry.controls.expenseEntry.controls[n].controls.milestone_entries.controls[i].controls.gl_account_master_data,{id:t.gl_account}),l=o.findWhere(this.legalEntityMasterData,{id:t.source_entity_id});e.push({sub_group_name:r.name?r.name:"",cost_center:a.code?a.code:t.cost_center,amount:t.amount,source_entity_id:t.source_entity_id,source_tally_entity_name:l.tally_entity_name,description:t.description,sub_group_id:t.sub_group_id,currency_code:this.itemDetails.vendor_preferred_currency,tds_nature_gl_id:t.tds_nature_gl_id,tds_nature_gl_name:t.tds_nature_gl_name,taxability_type_name:t.taxability_type_name,taxability_type_rate:t.taxability_type_rate,hsn_sac_value:t.hsn_sac_value})})}),e}handleChequeAmountChange(e){if(console.log("checked",this.paymentEntry.get("isChecked").value),this.advCheck){let t=parseFloat(e)+this.paymentsSum;console.log("totak",t),t<parseFloat(this.paymentEntry.get("finalAmount").value)?(this.is_partial_payment=!0,this.paymentChequeMsg="Partial Payment"):t>parseFloat(this.paymentEntry.get("finalAmount").value)?this.paymentChequeMsg="Payment amount is not matching":t==parseFloat(this.paymentEntry.get("finalAmount").value)?(this.is_partial_payment=!1,this.paymentChequeMsg="Full payment"):""==e&&(this.paymentChequeMsg="Please enter the cheque amount")}else parseFloat(e)<parseFloat(this.paymentEntry.get("finalAmount").value)?(this.is_partial_payment=!0,this.paymentChequeMsg="Partial payment"):parseFloat(e)>parseFloat(this.paymentEntry.get("finalAmount").value)?this.paymentChequeMsg="Payment amount is not matching":parseFloat(e)==parseFloat(this.paymentEntry.get("finalAmount").value)?(this.is_partial_payment=!1,this.paymentChequeMsg="Full payment"):""==e&&(this.paymentChequeMsg="Please enter the cheque amount")}targetEntityClick(){var e,t,n,i,a,o,r,l;let s;s=(null===(n=null===(t=null===(e=this.paymentEntry)||void 0===e?void 0:e.controls)||void 0===t?void 0:t.expenseEntry.value[0])||void 0===n?void 0:n.milestone_entries)?null===(o=null===(a=null===(i=this.paymentEntry)||void 0===i?void 0:i.controls)||void 0===a?void 0:a.expenseEntry.value[0])||void 0===o?void 0:o.milestone_entries[0].source_entity_id:null===(l=null===(r=this.paymentEntry)||void 0===r?void 0:r.controls)||void 0===l?void 0:l.expenseEntry.value[0].sub_group_entries[0].source_entity_id,this.financeBooksAvailable&&(this.financialBooksMasterData=this.financialBooksMasterDataList.filter(e=>{if(null!=s&&JSON.parse(e.entity_id).includes(s))return!0}),console.log("fb after target entity",this.financialBooksMasterData),console.log()),this.isTaxConfigured&&(this.taxAdditionType=this.taxAdditionForConfig.filter(e=>{if(null!=s&&JSON.parse(e.entity_id).includes(s))return!0})),console.log("financeBook",this.paymentEntry),console.log("LM master data",this.legalEntityMasterData)}validateMoveToBank(){let e;if(this.paymentEntry.value.bank&&""!=this.paymentEntry.value.bank)if(this.paymentEntry.value.bank.bank_ledger_name&&""!=this.paymentEntry.value.bank.bank_ledger_name){let t=0;this.partialPaymentDetails.forEach(e=>{t+=e.amount_paid}),t+=parseFloat(this.paymentEntry.get("chequeAmount").value),t<=parseFloat(this.paymentEntry.get("finalAmount").value)?(this.isPartialPaymentCompleted=t==parseFloat(this.paymentEntry.get("finalAmount").value),e=!0):(this._p2pGeneralService.showMessage("Payment amount is not matching !"),e=!1)}else this._p2pGeneralService.showMessage("Bank ledger name not found, Kindly contact KEBS team !"),e=!1;else this._p2pGeneralService.showMessage("Please choose the bank to proceed !"),e=!1;return e}closeLedgerEntry(e){""!=this.activeBill&&null!=this.activeBill&&null!=this.activeBill&&(this.dialogRef.close(e),location.reload()),this.isPopUpMode?this.dialogRef.close(e):this.vendorInvoiceLedgerResult.emit(e)}formSelectedFb(){console.log("onClick",this.paymentEntry),console.log("ent",this.legalEntityMasterData),console.log(this.paymentEntry.controls.expenseEntry.value[0].milestone_entries[0].source_entity_id)}ngOnDestroy(){this.$onDestroy.next(),this.$onDestroy.complete()}check(){console.log("Advance checkbox value : ",this.paymentEntry.get("isChecked").value)}takeAdvance(){if(console.log("is checked",this.paymentEntry.get("isChecked").value),console.log("PaymentEntry Form Details : ",this.paymentEntry.value),"VALID"==this.paymentEntry.status){let e={},t=this.paymentEntry.value.bank.bank_ledger_name?this.paymentEntry.value.bank.bank_ledger_name:"",n=this.getTallyEntryFormat(),i={payment_model_id:this.itemDetails.payment_model_id,milestone_id:1==this.itemDetails.payment_model_id?null:this.itemDetails.selectedMilestone[0].milestone_id,items:this.itemDetails.l2,tallyEntry:n,bank_ledger_name:this.paymentEntry.value.bank.bank_ledger_name?this.paymentEntry.value.bank.bank_ledger_name:""};this.paymentEntry.value.vendor_preferred_currency=this.itemDetails.vendor_preferred_currency,this.paymentEntry.value.isPartialPayment=!0,this.paymentEntry.value.isPartialPaymentCompleted=!1;let a=o.findWhere(this.vendorReferenceTypes,{id:this.paymentEntry.value.vendorRefType});a=a?a.name:"",this.paymentEntry.value.vendorRefType=a,e=Object.assign(Object.assign({},i),this.paymentEntry.value),t&&""!=t?this._p2pGeneralService.takeAdvance(this.itemDetails.p2p_header_id,e,this.paymentEntry.value).pipe(Object(S.a)(this.$onDestroy)).subscribe(e=>{"S"==e.messType?(this.paymentEntry.get("isChecked").setValue(!1),this.closeLedgerEntry({messType:e.messType,is_moved_to_bank:e.is_moved_to_bank,payment_status:e.payment_status,headerStatus:e.headerStatus})):this._p2pGeneralService.showMessage(e.messText)}):this._p2pGeneralService.showMessage("Choose Bank")}else this._p2pGeneralService.showMessage("Please fill mandatory fields !")}moveToNextStep(){this.stepper.selectedIndex<this.stepper._steps.length-1&&this.stepper.next()}saveDraft(){let e,t,n=this.paymentEntry.value;console.log("Save Button Clicked... : ",n),this.itemDetails&&(null!=this.itemDetails.invoice_id&&null!=this.itemDetails.invoice_id&&this.itemDetails.invoice_id?(console.log("Ledger making for onetime payment"),e=this.itemDetails.invoice_id,t=this.itemDetails.p2p_header_id):null!=this.itemDetails.selectedMilestone[0].invoice_id&&null!=this.itemDetails.selectedMilestone[0].invoice_id&&this.itemDetails.selectedMilestone[0].invoice_id&&(console.log("Ledger making for Recurring/Split payment"),e=this.itemDetails.selectedMilestone[0].invoice_id,t=this.itemDetails.p2p_header_id)),this.itemDetails&&e&&t?(this.isDraftSpinnerLoading=!0,this._p2pGeneralService.saveLedgerDraft(n,e,t).pipe(Object(S.a)(this.$onDestroy)).subscribe(e=>{"S"==e.messType?(this.isDraftSpinnerLoading=!1,this._p2pGeneralService.showMessage("Draft saved successfully !")):this._p2pGeneralService.showMessage(e.messText)})):this._p2pGeneralService.showMessage("Draft Data/Invoice ID/PR ID not found...! kindly reach out to KEBS team!")}getDraftedDataforInvoice(){let e,t;this.itemDetails&&(null!=this.itemDetails.invoice_id&&null!=this.itemDetails.invoice_id&&this.itemDetails.invoice_id?(console.log("Ledger making for onetime payment"),e=this.itemDetails.invoice_id,t=this.itemDetails.p2p_header_id):null!=this.itemDetails.selectedMilestone[0].invoice_id&&null!=this.itemDetails.selectedMilestone[0].invoice_id&&this.itemDetails.selectedMilestone[0].invoice_id&&(console.log("Ledger making for Recurring/Split payment"),e=this.itemDetails.selectedMilestone[0].invoice_id,t=this.itemDetails.p2p_header_id)),this.itemDetails&&e&&t?this._p2pGeneralService.getDraftedDataforInvoice(e,t).pipe(Object(S.a)(this.$onDestroy)).subscribe(e=>Object(i.c)(this,void 0,void 0,(function*(){"S"==e.messType?(this.retrievedDraftedData=e.data,console.log("Retrieved Drafted Ledger Entries : ",this.retrievedDraftedData),"edit"==this.statutoryViewMode&&(this.paymentEntry.patchValue({isChecked:this.retrievedDraftedData.isChecked,bank:this.retrievedDraftedData.bank,chequeNo:this.retrievedDraftedData.chequeNo,voucherNo:this.retrievedDraftedData.voucherNo,chequeAmount:this.retrievedDraftedData.chequeAmount,vendorAccount:this.retrievedDraftedData.vendorAccount,vendorRefType:this.retrievedDraftedData.vendorRefType,financeBooksfm:this.retrievedDraftedData.financeBooksfm,vendorDescription:this.retrievedDraftedData.vendorDescription,totalExpenseAmount:this.retrievedDraftedData.totalExpenseAmount,finalAmount:this.retrievedDraftedData.finalAmount,expenseEntry:[],taxAddition:[],taxDeduction:[]}),this.patchFormArray(this.retrievedDraftedData.expenseEntry,"expenseEntry"),this.patchFormArray(this.retrievedDraftedData.taxAddition,"taxAddition"),this.patchFormArray(this.retrievedDraftedData.taxDeduction,"taxDeduction"),this.paymentEntry.patchValue({totalTaxAddition:this.retrievedDraftedData.totalTaxAddition,totalTaxDeduction:this.retrievedDraftedData.totalTaxDeduction,overallTaxAmount:this.retrievedDraftedData.overallTaxAmount}),this.isLedgerPopUpSpinnerLoading=!0)):this.isLedgerPopUpSpinnerLoading=!0}))):this._p2pGeneralService.showMessage("Invoice ID/PR ID not found...! kindly reach out to KEBS team!")}patchFormArray(e,t){if(this.paymentEntry.get(t).clear(),"expenseEntry"==t){let t=this.paymentEntry.controls.expenseEntry;1==this.itemDetails.payment_model_id?this.itemDetails.l2.forEach((n,i)=>{t.push(this.fb.group({sub_group_id:n.sub_group_id,sub_group_name:n.sub_group_name,sub_group_amount:this.calculateSubGroupItemTotal(n),vendor_currency:this.itemDetails.vendor_preferred_currency,is_sub_group_amt_exceeded:!1,sub_group_entries:this.fb.array([])}));const a=t.at(i).get("sub_group_entries");e[i].sub_group_entries.forEach((e,t)=>{let n=!this.paymentEntry.get("isChecked").value&&this.tdsNatureOnLedgerEntry?{tds_nature_gl_id:e.tds_nature_gl_id,tds_nature_gl_name:e.tds_nature_gl_name}:{},i=!this.paymentEntry.get("isChecked").value&&this.taxabilityTypeonLedgerEntry?{taxability_type_id:e.taxability_type_id,taxability_type_name:e.taxability_type_name,taxability_type_rate:e.taxability_type_rate}:{},o=!this.paymentEntry.get("isChecked").value&&this.HSNSAConLedgerEntry?{hsn_sac_value:e.hsn_sac_value}:{};const r=this.fb.group(Object.assign(Object.assign(Object.assign({cost_center:e.cost_center,cc_amount:e.cc_amount,source_entity_id:e.source_entity_id,source_entity_name:e.source_entity_name,gl_account:e.gl_account,gl_account_master_data:(null==e?void 0:e.gl_account_master_data)?e.gl_account_master_data:[],description:e.description,sub_group_id:e.sub_group_id},n),i),o));a.push(r),this.onExpenseEntryValueChange(this.paymentEntry.get("expenseEntry").value)})}):this.itemDetails.selectedMilestone.forEach((n,i)=>{t.push(this.fb.group({milestone_name:n.milestone_name,milestone_amount:this.getMilestoneAmount(n),is_milestone_amount_exceeded:!1,milestone_entries:this.fb.array([])}));const a=t.at(i).get("milestone_entries");e[i].milestone_entries.forEach((e,t)=>{let n=this.tdsNatureOnLedgerEntry?{tds_nature_gl_id:e.tds_nature_gl_id,tds_nature_gl_name:e.tds_nature_gl_name}:{},i=!this.paymentEntry.get("isChecked").value&&this.taxabilityTypeonLedgerEntry?{taxability_type_id:e.taxability_type_id,taxability_type_name:e.taxability_type_name,taxability_type_rate:e.taxability_type_rate}:{},o=!this.paymentEntry.get("isChecked").value&&this.HSNSAConLedgerEntry?{hsn_sac_value:e.hsn_sac_value}:{};const r=this.fb.group(Object.assign(Object.assign(Object.assign({cost_center:e.cost_center,amount:e.amount,sub_group_id:e.sub_group_id,source_entity_id:e.source_entity_id,source_entity_name:e.source_entity_name,source_tally_entity_name:e.source_tally_entity_name,gl_account:e.gl_account,gl_account_master_data:(null==e?void 0:e.gl_account_master_data)?e.gl_account_master_data:[],description:e.description},n),i),o));a.push(r),this.onExpenseEntryValueChange(this.paymentEntry.get("expenseEntry").value)})})}if("taxAddition"==t){let t=this.paymentEntry.controls.taxAddition;e.forEach((e,n)=>{console.log("Tax Addition Structure Data : ",e),t.push(this.fb.group({sub_group_ref_id:e.sub_group_ref_id,sub_group_name:e.sub_group_name,sub_group_amount:e.sub_group_amount,sub_group_tax_entries:this.fb.array([])}));const i=t.at(n).get("sub_group_tax_entries");e.sub_group_tax_entries.forEach(e=>{const t=this.fb.group({tax_type:e.tax_type,tax_type_name:e.tax_type_name,tax_type_code:e.tax_type_code,tax_percentage:e.tax_percentage,tax_amount:e.tax_amount,tax_addition_gl_id:e.tax_addition_gl_id,tax_addition_gl_name:e.tax_addition_gl_name,is_tax_addition_gl_field_mandatory:e.is_tax_addition_gl_field_mandatory,is_tax_percentage_entered_by_user:e.is_tax_percentage_entered_by_user});i.push(t)})})}if("taxDeduction"==t){let t=this.paymentEntry.controls.taxDeduction;e.forEach((e,n)=>{console.log("Tax Deduction Structure Data : ",e),t.push(this.fb.group({sub_group_ref_id:e.sub_group_ref_id,sub_group_name:e.sub_group_name,sub_group_amount:e.sub_group_amount,sub_group_tax_entries:this.fb.array([])}));const i=t.at(n).get("sub_group_tax_entries");e.sub_group_tax_entries.forEach(e=>{const t=this.fb.group({tax_type:e.tax_type,tax_type_name:e.tax_type_name,tax_type_code:e.tax_type_code,tax_percentage:e.tax_percentage,tax_amount:e.tax_amount,tax_deduction_gl_id:e.tax_deduction_gl_id,tax_deduction_gl_name:e.tax_deduction_gl_name,is_tax_deduction_gl_field_mandatory:e.is_tax_deduction_gl_field_mandatory,is_tax_percentage_entered_by_user:e.is_tax_percentage_entered_by_user});i.push(t)})})}}getSessionID(){this._p2pGeneralService.getSessionIdForBills().pipe(Object(S.a)(this.$onDestroy)).subscribe(e=>Object(i.c)(this,void 0,void 0,(function*(){if("S"==e.messType){if(this.sessionId=e.session_id,sessionStorage.setItem("P2PpaymentBillsSession",this.sessionId),console.log("Generated Session ID : ",sessionStorage.getItem("P2PpaymentBillsSession")),this.itemDetails){let e,t=!1;null!=this.itemDetails.invoice_id&&null!=this.itemDetails.invoice_id&&this.itemDetails.invoice_id&&0!=this.itemDetails.is_moved_to_bank&&1!=this.itemDetails.is_moved_to_bank&&4!=this.itemDetails.is_moved_to_bank?(console.log("Ledger making for onetime payment"),e=this.itemDetails.invoice_id,t=!0):null!=this.itemDetails.selectedMilestone[0].invoice_id&&null!=this.itemDetails.selectedMilestone[0].invoice_id&&this.itemDetails.selectedMilestone[0].invoice_id&&0!=this.itemDetails.selectedMilestone[0].is_moved_to_bank&&1!=this.itemDetails.selectedMilestone[0].is_moved_to_bank&&4!=this.itemDetails.selectedMilestone[0].is_moved_to_bank&&(console.log("Ledger making for Recurring/Split payment"),e=this.itemDetails.selectedMilestone[0].invoice_id,t=!0),1==t&&(console.log("Ledger Making Invoice ID : ",e),this._p2pGeneralService.updateSessionIdForBillsFromP2P(this.sessionId,e,"P2P").pipe(Object(S.a)(this.$onDestroy)).subscribe(e=>Object(i.c)(this,void 0,void 0,(function*(){"S"==e.messType?console.log("Session ID has been updated against the selected Bill(Invoice)"):console.log("Session ID has not been updated against the selected Bill(s)")}))))}}else this.sessionId="",console.log("Session ID was not Generated..."),window.alert("Session ID was not Genegerated...\nKindly Refresh the Page !")})))}isTDSNAtureAllowed(e,t,n,i){if(""!=e&&null!=e&&null!=e){let e=!1;return 1==i?(e=o.where(this.paymentEntry.controls.expenseEntry.controls[t].controls.sub_group_entries.controls[n].controls.gl_account_master_data,{gl_type:"Expense",id:this.paymentEntry.controls.expenseEntry.controls[t].controls.sub_group_entries.controls[n].controls.gl_account.value}).length>0,this.isTDSNatureApplicable=!!e,e):(e=o.where(this.paymentEntry.controls.expenseEntry.controls[t].controls.milestone_entries.controls[n].controls.gl_account_master_data,{gl_type:"Expense",id:this.paymentEntry.controls.expenseEntry.controls[t].controls.milestone_entries.controls[n].controls.gl_account.value}).length>0,this.isTDSNatureApplicable=!!e,e)}return!1}isGSTRateonLedgerAllowed(e,t,n,i){if(""!=e&&null!=e&&null!=e){let e=!1;return 1==i?(e=o.where(this.paymentEntry.controls.expenseEntry.controls[t].controls.sub_group_entries.controls[n].controls.gl_account_master_data,{gl_type:"Expense",id:this.paymentEntry.controls.expenseEntry.controls[t].controls.sub_group_entries.controls[n].controls.gl_account.value}).length>0,this.isTaxabilityTypeApplicable=!!e,e):(e=o.where(this.paymentEntry.controls.expenseEntry.controls[t].controls.milestone_entries.controls[n].controls.gl_account_master_data,{gl_type:"Expense",id:this.paymentEntry.controls.expenseEntry.controls[t].controls.milestone_entries.controls[n].controls.gl_account.value}).length>0,this.isTaxabilityTypeApplicable=!!e,e)}return!1}isHSNSAConLedgerAllowed(e,t,n,i){if(""!=e&&null!=e&&null!=e){let e=!1;return 1==i?(e=o.where(this.paymentEntry.controls.expenseEntry.controls[t].controls.sub_group_entries.controls[n].controls.gl_account_master_data,{gl_type:"Expense",id:this.paymentEntry.controls.expenseEntry.controls[t].controls.sub_group_entries.controls[n].controls.gl_account.value}).length>0,this.isHSNSACDetailsApplicable=!!e,e):(e=o.where(this.paymentEntry.controls.expenseEntry.controls[t].controls.milestone_entries.controls[n].controls.gl_account_master_data,{gl_type:"Expense",id:this.paymentEntry.controls.expenseEntry.controls[t].controls.milestone_entries.controls[n].controls.gl_account.value}).length>0,this.isHSNSACDetailsApplicable=!!e,e)}return!1}onTaxabilityChange(e,t,n,i){if(""==e||null==e||null==e)return!1;{let a=this.taxabilityTypeDetails.filter(t=>t.id==e);1==i?this.paymentEntry.controls.expenseEntry.controls[t].controls.sub_group_entries.controls[n].controls.taxability_type_rate.patchValue(a[0].tax_rate,{emitEvent:!1}):this.paymentEntry.controls.expenseEntry.controls[t].controls.milestone_entries.controls[n].controls.taxability_type_rate.patchValue(a[0].tax_rate,{emitEvent:!1})}}isTaxabilityRateReadOnly(e,t,n,i){return""!=e&&null!=e&&null!=e?e<=0:1==i?!this.paymentEntry.controls.expenseEntry.controls[t].controls.sub_group_entries.controls[n].controls.taxability_type_id.value:2==i||3==i?!this.paymentEntry.controls.expenseEntry.controls[t].controls.milestone_entries.controls[n].controls.taxability_type_id.value:void 0}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275directiveInject"](s.h),a["\u0275\u0275directiveInject"](m.i),a["\u0275\u0275directiveInject"](s.a),a["\u0275\u0275directiveInject"](C.b),a["\u0275\u0275directiveInject"](P.a),a["\u0275\u0275directiveInject"](A.a))},e.\u0275cmp=a["\u0275\u0275defineComponent"]({type:e,selectors:[["app-ledger-entry-modal"]],viewQuery:function(e,t){if(1&e&&a["\u0275\u0275viewQuery"](I,!0),2&e){let e;a["\u0275\u0275queryRefresh"](e=a["\u0275\u0275loadQuery"]())&&(t.stepper=e.first)}},inputs:{vendorInvoiceDetail:"vendorInvoiceDetail",jsonData:"jsonData",isPopUpMode:"isPopUpMode"},outputs:{vendorInvoiceLedgerResult:"vendorInvoiceLedgerResult"},decls:3,vars:2,consts:[[1,"container-fluid","ledger-entry-modal"],["class","row justify-content-center","style","padding-top: 10vh;",4,"ngIf"],[3,"formGroup",4,"ngIf"],[1,"row","justify-content-center",2,"padding-top","10vh"],["diameter","30"],[3,"formGroup"],[1,"row","mt-2","pb-1",2,"border-bottom","solid 1px #cacaca"],[1,"col-11","d-flex"],[1,"raised-circle","my-auto"],[1,"logo-icon"],[1,"txt-bold","my-auto","ml-3","mr-2"],[1,"txt-highlighted","my-auto"],[1,"row","px-3","my-auto",2,"color","gray","font-size","13px"],["class","col-1 d-flex",4,"ngIf"],[1,"row","mt-2"],[3,"selectionChange"],["stepper",""],[4,"ngIf"],["matStepLabel",""],["class","col-12 d-flex justify-content-end","style","margin-bottom: 10px;",4,"ngIf"],[1,"transaction-stepper","row"],["class","col-7",4,"ngIf"],[1,"col-5"],["class","row",4,"ngIf"],["transationSummary",""],["partialPaymentSummary",""],[1,"col-1","d-flex"],["mat-icon-button","",1,"ml-auto","close-button",3,"click"],[1,"close-icon"],[1,"col-10"],[1,"col-2"],["mat-mini-fab","","matTooltip","Next",2,"background-color","#cf0001","color","white",3,"click"],[4,"ngFor","ngForOf"],[1,"row"],[1,"col-12"],[1,"table-wrapperReadOnly"],[1,"table","table-hover"],["scope","col"],["scope","col",4,"ngIf"],["scope","row"],[1,"col-12","d-flex","justify-content-between",2,"margin-bottom","10px"],["formControlName","isChecked",1,"example-margin","ml-2",3,"click"],["mat-icon-button","","mat-mini-fab","",1,"save-draft-btn",3,"ngClass","click"],["style","font-size: 20px; margin-top:-8px;","matTooltip","Save as Draft",4,"ngIf"],["matTooltip","Please wait...","class","spinner-align","diameter","20",4,"ngIf"],["style","min-height: 40vh","class","col-12 expense-entry-style","formArrayName","expenseEntry",4,"ngIf"],["matTooltip","Save as Draft",2,"font-size","20px","margin-top","-8px"],["matTooltip","Please wait...","diameter","20",1,"spinner-align"],["formArrayName","expenseEntry",1,"col-12","expense-entry-style",2,"min-height","40vh"],["class","my-2",3,"formGroupName",4,"ngFor","ngForOf"],[1,"my-2",3,"formGroupName"],[1,"d-flex"],[1,"pl-3",2,"color","gray","font-size","13px"],["class","mx-3",4,"ngIf"],["formArrayName","sub_group_entries",4,"ngIf"],[1,"mx-3"],[1,"mx-2"],[2,"color","#cf0001","font-size","18xp"],[2,"color","#cf0001","font-size","12px"],["formArrayName","sub_group_entries"],["class","row my-1",3,"formGroupName",4,"ngFor","ngForOf"],[1,"row","my-1",3,"formGroupName"],[1,"mr-2"],["required","true","placeholder","Cost Center","formControlName","cost_center",1,"drop-down",3,"list"],["appearance","outline",1,"create-account-field"],["matInput","","placeholder","Eg: 1000","type","number","formControlName","cc_amount"],["matSuffix",""],["required","true","placeholder","GL Account","formControlName","gl_account",1,"drop-down",3,"list"],["matInput","","required","true","formControlName","description",3,"keydown.enter"],["class","mr-2",4,"ngIf"],[1,"d-flex","pt-1"],[1,"add_btn",3,"click"],[1,"add_btn","ml-3",3,"click"],["matInput","","readonly","","formControlName","source_entity_name"],["required","true","placeholder","Target entity","formControlName","source_entity_id",1,"drop-down",3,"list"],["placeholder","Nature of Payment","formControlName","tds_nature_gl_id",1,"drop-down",3,"required","list","change"],["placeholder","Taxability Type","formControlName","taxability_type_id",1,"drop-down",3,"required","list","change"],["matInput","","type","number","placeholder","Eg: 18%","required","false","formControlName","taxability_type_rate",3,"min","max","readonly"],["matInput","","type","text","placeholder","HSN/SAC","required","false","pattern","[0-9]*","formControlName","hsn_sac_value",3,"maxLength","keydown.enter"],["formArrayName","milestone_entries",4,"ngIf"],["formArrayName","milestone_entries"],["matInput","","placeholder","Eg: 1000","formControlName","amount"],["required","true","placeholder","Sub group","formControlName","sub_group_id",1,"drop-down",3,"list"],["matInput","","type","text","placeholder","HSN/SAC","required","false","formControlName","hsn_sac_value","pattern","[0-9]*",3,"maxLength","keydown.enter"],["&&","","isTaxDetailMasterDataAvailable","","&&","","isGlAccMasterDataAvailable","","&&","","isLegalEntityMasterDataAvailable","",4,"ngIf"],[1,"d-flex","flex-row-reverse",2,"padding-right","100px"],[1,"pr-2","my-auto","tax-dynamic-box"],[1,"tax-title"],[1,"tax-value"],[1,"row","mt-4"],["&&","","isTaxDetailMasterDataAvailable","","&&","","isGlAccMasterDataAvailable","","&&","","isLegalEntityMasterDataAvailable",""],[1,"row","mb-2"],[2,"font-size","large"],[1,"col-12","d-flex","justify-content-end"],["class","col-12 statutory-entry-style","formArrayName","taxAddition",4,"ngIf"],["formArrayName","taxAddition",1,"col-12","statutory-entry-style"],[2,"font-weight","500"],[1,"col-3"],["required","true","placeholder","Expense type","formControlName","sub_group_ref_id",1,"drop-down",3,"list"],["formArrayName","sub_group_tax_entries",4,"ngIf"],["formArrayName","sub_group_tax_entries"],[1,"col-1","border-left-bottom"],["required","true","placeholder","Tax type","formControlName","tax_type",1,"drop-down",3,"list"],["appearance","outline",1,"create-account-field",2,"width","100%"],["matInput","","type","number","placeholder","Eg: 18%","formControlName","tax_percentage",3,"min","max","readOnly","input"],["readonly","","matInput","","formControlName","tax_amount"],["placeholder","GL account","formControlName","tax_addition_gl_id",1,"drop-down",3,"required","list","disabled"],["class","col-12","formArrayName","taxDeduction",4,"ngIf"],["formArrayName","taxDeduction",1,"col-12"],["placeholder","GL account","formControlName","tax_deduction_gl_id",1,"drop-down",3,"required","list","disabled","change"],[1,"col-12","d-flex","justify-content-end",2,"margin-bottom","10px"],[1,"col-7"],[1,"row","mb-3",2,"font-weight","100","font-size","18px"],[1,"col-12","px-0"],["readonly","","matInput","","placeholder","","formControlName","vendorAccount"],[1,"col-6","px-0"],["placeholder","Vendor Reference","formControlName","vendorRefType",1,"drop-down",3,"list"],[1,"col-6"],["appearance","outline",2,"width","100%"],["required","true","matInput","","formControlName","vendorDescription"],["class","drop-down","placeholder","Financial Books","formControlName","financeBooksfm",3,"list","change",4,"ngIf"],["placeholder","Financial Books","formControlName","financeBooksfm",1,"drop-down",3,"list","change"],[1,"d-flex","flex-row","mt-4"],[2,"font-weight","100","font-size","18px"],[1,"ml-auto"],[2,"color","gray","font-size","21px"],[1,"cheque-status-msg"],[1,"row","my-2","py-3","cheque-card"],["class","d-flex justify-content-center align-items-center pt-5",4,"ngIf"],[1,"col-6","p-0","bank-title"],[1,"col-6","p-0"],["matInput","","readonly","","formControlName","voucherNo"],["matInput","","formControlName","chequeNo",3,"required"],["matInput","","type","number","formControlName","chequeAmount"],["class","row mt-3 mb-2 pt-2 pb-2 session-alert",4,"ngIf"],[1,"d-flex","justify-content-center"],["mat-raised-button","","class","btn-active justify-content-center",3,"click",4,"ngIf"],["mat-raised-button","","class","btn-active justify-content-center","style","background-color: green","disabled","",4,"ngIf"],["mat-raised-button","","class","btn-active justify-content-center ml-3",3,"click",4,"ngIf"],["mat-raised-button","","class","btn-active justify-content-center ml-3","style","background-color: green","disabled","",3,"click",4,"ngIf"],["matTooltip","Please wait...","class","spinner-align","diameter","30",3,"ngStyle",4,"ngIf"],[1,"d-flex","justify-content-center","align-items-center","pt-5"],["mat-raised-button","",1,"btn-not-active",3,"click"],[1,"row","pt-2","pb-2"],[1,"col-4","pl-0","bank-title"],[1,"col-8","p-0","bank-value"],[1,"row","pt-3","pb-2"],[1,"row","pt-3"],[1,"col-4","bank-title","pl-0"],[1,"row","mt-3","mb-2","pt-2","pb-2","session-alert"],[1,"col-12","p-0"],[1,"row","pb-2"],[1,"col","active-bill"],[1,"mr-2",2,"color","#cf0001","font-size","22px","vertical-align","middle"],[1,"col","processed-session"],[2,"color","red"],["mat-raised-button","",1,"btn-active","justify-content-center",3,"click"],["mat-raised-button","","disabled","",1,"btn-active","justify-content-center",2,"background-color","green"],["mat-raised-button","",1,"btn-active","justify-content-center","ml-3",3,"click"],["mat-raised-button","","disabled","",1,"btn-active","justify-content-center","ml-3",2,"background-color","green",3,"click"],["matTooltip","Please wait...","diameter","30",1,"spinner-align",3,"ngStyle"],[3,"ngTemplateOutlet"],[1,"summary-card","py-3"],[1,"row","px-3"],[2,"color","gray"],[1,"px-2",2,"font-weight","100","font-size","18px"],[1,"row","my-2"],[1,"col-6","summary-title"],[1,"col-6","px-0","summary-value"],[1,"col-6","px-0","value-account"],[1,"row","my-3","pt-3"]],template:function(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"div",0),a["\u0275\u0275template"](1,w,2,0,"div",1),a["\u0275\u0275template"](2,xt,33,10,"form",2),a["\u0275\u0275elementEnd"]()),2&e&&(a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",!t.isLedgerPopUpSpinnerLoading),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",t.isLedgerPopUpSpinnerLoading))},directives:[l.NgIf,v.c,m.J,m.w,m.n,h.a,E.a,E.b,E.c,g.a,f.a,l.NgForOf,T.a,m.v,m.l,l.NgClass,m.h,m.o,k.a,m.F,d.c,d.g,c.b,m.A,m.e,d.i,m.B,l.NgStyle,l.NgTemplateOutlet],pipes:[l.DatePipe],styles:[".ledger-entry-modal[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.ledger-entry-modal[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}.ledger-entry-modal[_ngcontent-%COMP%]   .txt-bold[_ngcontent-%COMP%]{font-weight:500;color:#000}.ledger-entry-modal[_ngcontent-%COMP%]   .txt-highlighted[_ngcontent-%COMP%]{color:#cf0001;font-weight:500}.ledger-entry-modal[_ngcontent-%COMP%]   .raised-circle[_ngcontent-%COMP%]{display:flex;border-radius:50%;background-color:#fbfbfb;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12);width:30px;height:30px}.ledger-entry-modal[_ngcontent-%COMP%]   .logo-icon[_ngcontent-%COMP%]{color:#cf0001;font-size:20px;padding-left:4px;padding-top:4px}.ledger-entry-modal[_ngcontent-%COMP%]   mat-horizontal-stepper[_ngcontent-%COMP%]{width:100%}.ledger-entry-modal[_ngcontent-%COMP%]     .mat-form-field-outline-thick .mat-form-field-outline{height:41px!important}.ledger-entry-modal[_ngcontent-%COMP%]   .drop-down[_ngcontent-%COMP%]     .mat-form-field-flex{height:44px}.ledger-entry-modal[_ngcontent-%COMP%]   .expense-entry-style[_ngcontent-%COMP%]{background-image:url(https://assets.kebs.app/images/coinstack.png);background-size:225px 225px;background-repeat:no-repeat;overflow-y:hidden!important;background-position:100% 100%}.ledger-entry-modal[_ngcontent-%COMP%]   .add_btn[_ngcontent-%COMP%]{height:39px;width:39px;display:flex;cursor:pointer;border:1px solid #efa6a6;align-items:center;border-radius:4px;justify-content:center}.ledger-entry-modal[_ngcontent-%COMP%]   .tax-title[_ngcontent-%COMP%]{color:grey;font-size:12px}.ledger-entry-modal[_ngcontent-%COMP%]   .tax-value[_ngcontent-%COMP%]{font-size:20px;font-weight:500;color:#cf0001}.ledger-entry-modal[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]{background-color:#fff!important;box-shadow:0 3px 6px hsla(0,0%,41.2%,.19),0 1px 4px hsla(0,0%,41.2%,.19);border:1px solid rgba(0,0,0,.125);border-radius:.25rem}.ledger-entry-modal[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .value-account[_ngcontent-%COMP%]{font-weight:500;color:#cf0001;font-size:15px}.ledger-entry-modal[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .summary-title[_ngcontent-%COMP%]{color:grey;font-size:13px}.ledger-entry-modal[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .summary-value[_ngcontent-%COMP%]{font-weight:500;font-size:15px}.ledger-entry-modal[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff;width:12rem;height:2rem}.ledger-entry-modal[_ngcontent-%COMP%]   .bank-title[_ngcontent-%COMP%]{color:grey;font-size:13px}.ledger-entry-modal[_ngcontent-%COMP%]   .bank-value[_ngcontent-%COMP%]{font-weight:500;font-size:15px}.ledger-entry-modal[_ngcontent-%COMP%]   .tax-dynamic-box[_ngcontent-%COMP%]{padding:10px;min-width:150px;background-color:#fff!important;box-shadow:0 3px 6px hsla(0,0%,41.2%,.19),0 1px 4px hsla(0,0%,41.2%,.19);border:1px solid rgba(0,0,0,.125);border-radius:.25rem}.ledger-entry-modal[_ngcontent-%COMP%]   .border-left-bottom[_ngcontent-%COMP%]{border-left:1px solid #dee2e6;border-bottom:1px solid #dee2e6}.ledger-entry-modal[_ngcontent-%COMP%]   .cheque-card[_ngcontent-%COMP%]{background:#fdf7f7;border-radius:10px;box-shadow:0 2px 1px -1px rgba(0,0,0,.2),0 1px 1px 0 rgba(0,0,0,.14),0 1px 3px 0 rgba(0,0,0,.12)}.ledger-entry-modal[_ngcontent-%COMP%]   .cheque-status-msg[_ngcontent-%COMP%]{font-size:11px;font-weight:500;color:grey}.ledger-entry-modal[_ngcontent-%COMP%]   .save-draft-btn[_ngcontent-%COMP%]{height:35px;width:35px;background-color:#cf0001;color:#fff}.ledger-entry-modal[_ngcontent-%COMP%]   .save-draft-btn-loading[_ngcontent-%COMP%]{height:35px;width:35px;background-color:#fff}.ledger-entry-modal[_ngcontent-%COMP%]   .table-wrapperReadOnly[_ngcontent-%COMP%]{overflow-x:auto;width:100%;margin:0 auto}.ledger-entry-modal[_ngcontent-%COMP%]   .table-wrapperReadOnly[_ngcontent-%COMP%]   table[_ngcontent-%COMP%]{width:100%;min-width:70%}.ledger-entry-modal[_ngcontent-%COMP%]   .table-wrapperReadOnly[_ngcontent-%COMP%]   td[_ngcontent-%COMP%], .ledger-entry-modal[_ngcontent-%COMP%]   .table-wrapperReadOnly[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{padding:8px;text-align:left}.ledger-entry-modal[_ngcontent-%COMP%]   .table-wrapperReadOnly[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{white-space:nowrap}.active-bill[_ngcontent-%COMP%]{color:#cf0001;font-weight:500}.session-alert[_ngcontent-%COMP%]{border-radius:7px;width:100%;border:2px solid #cf0001}.processed-session[_ngcontent-%COMP%]{font-weight:500}.session-note[_ngcontent-%COMP%]{color:#b37b14}"]}),e})(),ft=(()=>{class e{}return e.\u0275mod=a["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=a["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},providers:[],imports:[[l.CommonModule,s.g,d.e,c.c,m.E,m.p,p.c,u.h,y.x,_.b,h.b,g.b,x.a,v.b,f.b,E.f,C.c,T.b]]}),e})()},NJ67:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));class i{constructor(){this.disabled=!1}onChange(e){}onTouched(e){}writeValue(e){this.value=e}registerOnChange(e){this.onChange=e}registerOnTouched(e){this.onTouched=e}setDisabledState(e){this.disabled=e}}},"TmG/":function(e,t,n){"use strict";n.d(t,"a",(function(){return x}));var i=n("fXoL"),a=n("3Pt+"),o=n("jtHE"),r=n("XNiG"),l=n("NJ67"),s=n("1G5W"),d=n("kmnG"),c=n("ofXK"),m=n("d3UM"),p=n("FKr1"),u=n("WJ5W"),y=n("Qu3c");function _(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-label"),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](e.placeholder)}}function h(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-option",7),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275property"]("value",null),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](e.disableNone?"Select One":"None")}}function g(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"mat-option",8),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const n=t.$implicit;return i["\u0275\u0275nextContext"]().emitChanges(n)})),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;i["\u0275\u0275property"]("value",e.id||e._id)("matTooltip",e.name),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let x=(()=>{class e extends l.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new a.j,this.fieldFilterCtrl=new a.j,this.list=[],this.required=!1,this.disableNone=!1,this.valueChange=new i.EventEmitter,this.disabled=!1,this.hasNoneOption=!0,this.filteredList=new o.a,this.change=new i.EventEmitter,this.hideMatLabel=!1,this._onDestroy=new r.b,this.emitChanges=e=>{this.change.emit(e)}}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(s.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(s.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](i.Renderer2))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search"]],inputs:{list:"list",placeholder:"placeholder",required:"required",disableNone:"disableNone",disabled:"disabled",hasNoneOption:"hasNoneOption",hideMatLabel:"hideMatLabel"},outputs:{valueChange:"valueChange",change:"change"},features:[i["\u0275\u0275ProvidersFeature"]([{provide:a.t,useExisting:Object(i.forwardRef)(()=>e),multi:!0}]),i["\u0275\u0275InheritDefinitionFeature"],i["\u0275\u0275NgOnChangesFeature"]],decls:9,vars:11,consts:[["appearance","outline"],[4,"ngIf"],[3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value",4,"ngIf"],[3,"value","matTooltip","click",4,"ngFor","ngForOf"],[3,"value"],[3,"value","matTooltip","click"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"mat-form-field",0),i["\u0275\u0275template"](1,_,2,1,"mat-label",1),i["\u0275\u0275elementStart"](2,"mat-select",2,3),i["\u0275\u0275elementStart"](4,"mat-option"),i["\u0275\u0275element"](5,"ngx-mat-select-search",4),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](6,h,2,2,"mat-option",5),i["\u0275\u0275template"](7,g,2,3,"mat-option",6),i["\u0275\u0275pipe"](8,"async"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",!t.hideMatLabel),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.hasNoneOption),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngForOf",i["\u0275\u0275pipeBind1"](8,9,t.filteredList)))},directives:[d.c,c.NgIf,m.c,a.v,a.k,a.F,p.p,u.a,c.NgForOf,d.g,y.a],pipes:[c.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%}"]}),e})()},qFYv:function(e,t,n){"use strict";n.d(t,"a",(function(){return S}));var i=n("fXoL"),a=n("tk/3"),o=n("XNiG"),r=n("3Pt+"),l=n("NJ67"),s=n("1G5W"),d=n("Kj3r"),c=n("XXEo"),m=n("kmnG"),p=n("ofXK"),u=n("qFsG"),y=n("/1cH"),_=n("bTqV"),h=n("NFeN"),g=n("Qu3c"),x=n("FKr1");function v(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",7),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"]().checkAndClearInput(!0)})),i["\u0275\u0275elementStart"](1,"mat-icon",8),i["\u0275\u0275text"](2," close "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}}function f(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"mat-option",9),i["\u0275\u0275elementStart"](1,"div",10),i["\u0275\u0275element"](2,"div"),i["\u0275\u0275element"](3,"div"),i["\u0275\u0275element"](4,"div"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]())}function E(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"mat-option",12),i["\u0275\u0275listener"]("onSelectionChange",(function(){i["\u0275\u0275restoreView"](e);const n=t.$implicit;return i["\u0275\u0275nextContext"](2).resultClicked(n)})),i["\u0275\u0275elementStart"](1,"span"),i["\u0275\u0275elementStart"](2,"small",13),i["\u0275\u0275text"](3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](2),i["\u0275\u0275classMap"](n.ngClasses),i["\u0275\u0275propertyInterpolate3"]("matTooltip","",e[n.optionLabel[0]],"",e[n.optionLabel[1]]?" - ":"","",e[n.optionLabel[1]]?e[n.optionLabel[1]]:"",""),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate3"]("",e[n.optionLabel[0]],"",e[n.optionLabel[1]]?" - ":"","",e[n.optionLabel[1]]?e[n.optionLabel[1]]:"","")}}function b(e,t){if(1&e&&(i["\u0275\u0275elementContainerStart"](0),i["\u0275\u0275template"](1,E,4,9,"mat-option",11),i["\u0275\u0275elementContainerEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngForOf",e.searchResult)}}let S=(()=>{class e extends l.a{constructor(e,t){super(),this.http=e,this._login=t,this.label="",this.placeholder="",this.isRequired=!1,this.isReadOnly=!1,this.emitVal=new i.EventEmitter,this.optClicked=!1,this.searchTextControl=new r.j,this.searchResult=[],this.isLoading=!1,this.ifPlaceholderExists=!1,this._onDestroy=new o.b,this.resultClicked=e=>{setTimeout(()=>{this.optClicked=!0,this.outputParam?(this.onChange(e[this.outputParam]),this.emitVal.emit(e[this.outputParam])):(this.onChange(JSON.stringify(e)),this.emitVal.emit(JSON.stringify(e))),setTimeout(()=>{document.getElementById("searchTextInput").blur()},100),this.searchTextControl.patchValue(e[this.optionLabel[0]]+(this.optionLabel.length>1?" - "+e[this.optionLabel[1]]:""))},100)}}ngOnInit(){""!=this.placeholder&&""==this.label&&(this.label=this.placeholder,this.ifPlaceholderExists=!0),this.searchTxtControlSubscription=this.searchTextControl.valueChanges.pipe(Object(s.a)(this._onDestroy),Object(d.a)(700)).subscribe(e=>{e&&e.length>0&&this.callApiFn(e)})}callInitApi(){null==this.searchTextControl.value&&0==this.searchResult.length&&this.callApiFn("")}callApiFn(e){this.isLoading=!0;let t={searchParameter:e,options:this.bodyParams},n={headers:new a.f({Authorization:"Bearer "+this._login.getToken()})};this.http.post(this.apiUri,t,n).pipe(Object(s.a)(this._onDestroy)).subscribe(e=>{this.isLoading=!1,this.searchResult=e},e=>{console.error(e)})}checkAndClearInput(e){this.optClicked&&!e||0!=this.isReadOnly||(this.searchTextControl.reset(),this.onChange(null),this.emitVal.emit(null)),e&&setTimeout(()=>{document.getElementById("searchTextInput").blur(),setTimeout(()=>{document.getElementById("searchTextInput").focus()},100)},100)}writeValue(e){if(e){let t=JSON.parse(e);this.searchTextControl.patchValue(t[this.optionLabel[0]]+(this.optionLabel.length>1?" - "+t[this.optionLabel[1]]:""))}else this.searchTextControl.patchValue("")}resetSuggestion(){this.searchTextControl.patchValue("")}ngOnDestroy(){this.searchTxtControlSubscription&&this.searchTxtControlSubscription.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](a.c),i["\u0275\u0275directiveInject"](c.a))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search-huge-input"]],inputs:{apiUri:"apiUri",bodyParams:"bodyParams",outputParam:"outputParam",optionLabel:"optionLabel",optionValue:"optionValue",label:"label",placeholder:"placeholder",ngClasses:"ngClasses",isRequired:"isRequired",isReadOnly:"isReadOnly"},outputs:{emitVal:"emitVal"},features:[i["\u0275\u0275ProvidersFeature"]([{provide:r.t,useExisting:Object(i.forwardRef)(()=>e),multi:!0}]),i["\u0275\u0275InheritDefinitionFeature"]],decls:9,vars:11,consts:[["appearance","outline",3,"ngClass"],["id","searchTextInput","matInput","","autocomplete","off",3,"placeholder","matAutocomplete","formControl","required","readonly","focusout","focusin","keyup"],["mat-button","","matSuffix","","mat-icon-button","","style","height: 30px; width: 30px; line-height: 1",3,"click",4,"ngIf"],[3,"displayWith"],["auto","matAutocomplete"],["class","is-loading",4,"ngIf"],[4,"ngIf"],["mat-button","","matSuffix","","mat-icon-button","",2,"height","30px","width","30px","line-height","1",3,"click"],["matTooltip","Clear",2,"font-size","20px !important","color","#66615b !important"],[1,"is-loading"],[1,"lds-facebook"],[3,"onSelectionChange",4,"ngFor","ngForOf"],[3,"onSelectionChange"],[2,"padding-left","12px",3,"matTooltip"]],template:function(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-form-field",0),i["\u0275\u0275elementStart"](1,"mat-label"),i["\u0275\u0275text"](2),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](3,"input",1),i["\u0275\u0275listener"]("focusout",(function(){return t.checkAndClearInput(!1)}))("focusin",(function(){return t.callInitApi()}))("keyup",(function(){return t.optClicked=!1})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](4,v,3,0,"button",2),i["\u0275\u0275elementStart"](5,"mat-autocomplete",3,4),i["\u0275\u0275template"](7,f,5,0,"mat-option",5),i["\u0275\u0275template"](8,b,2,1,"ng-container",6),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275reference"](6);i["\u0275\u0275property"]("ngClass",t.ifPlaceholderExists?"label-no-float":""),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate"](t.label),i["\u0275\u0275advance"](1),i["\u0275\u0275propertyInterpolate"]("placeholder",t.label),i["\u0275\u0275property"]("matAutocomplete",e)("formControl",t.searchTextControl)("required",t.isRequired)("readonly",t.isReadOnly),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null!=t.searchTextControl.value),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("displayWith",t.displayFn),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf",t.isLoading),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",0==t.isLoading)}},directives:[m.c,p.NgClass,m.g,u.b,y.d,r.e,r.v,r.k,r.F,p.NgIf,y.b,_.a,m.i,h.a,g.a,x.p,p.NgForOf],styles:[".label-no-float.mat-form-field-appearance-outline.mat-form-field-should-float .mat-form-field-label{display:none}  .label-no-float.mat-form-field-appearance-outline.mat-form-field-should-float .mat-form-field-outline-gap{border-top-color:initial}.mat-form-field[_ngcontent-%COMP%]{width:100%}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding-bottom:8px!important}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-underline{bottom:0}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-infix{padding:.65em 0!important;border-top:.54375em solid transparent!important;font-size:13px}.mat-form-field[_ngcontent-%COMP%]     .mat-select-arrow{margin:0 4px!important}.lds-facebook[_ngcontent-%COMP%]{display:inline-block;position:sticky;width:80px;height:24px;margin-left:110px}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{display:inline-block;position:absolute;left:4px;width:5px;background:#cf0001;animation:lds-facebook 1.2s cubic-bezier(0,.5,.5,1) infinite}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:first-child{left:8px;animation-delay:-.24s}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:nth-child(2){left:16px;animation-delay:-.12s}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:nth-child(3){left:24px;animation-delay:0}@keyframes lds-facebook{0%{top:8px;height:32px}50%,to{top:12px;height:16px}}"]}),e})()}}]);