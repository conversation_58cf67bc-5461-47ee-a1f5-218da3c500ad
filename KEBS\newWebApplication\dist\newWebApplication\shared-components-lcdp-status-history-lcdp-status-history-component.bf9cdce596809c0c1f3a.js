(window.webpackJsonp=window.webpackJsonp||[]).push([[890],{diiJ:function(t,e,n){"use strict";n.r(e),n.d(e,"LcdpStatusHistoryComponent",(function(){return C}));var o=n("0IaG"),i=n("1G5W"),r=n("XNiG"),a=n("ofXK"),l=n("NFeN"),s=n("bTqV"),d=n("Qu3c"),c=(n("lVl8"),n("Xa2L")),m=(n("mD+J"),n("fXoL")),p=n("z52X"),g=n("LcQX");function h(t,e){1&t&&(m["\u0275\u0275elementStart"](0,"div",13),m["\u0275\u0275element"](1,"div",14),m["\u0275\u0275elementStart"](2,"div",15),m["\u0275\u0275element"](3,"mat-spinner",16),m["\u0275\u0275elementEnd"](),m["\u0275\u0275element"](4,"div",14),m["\u0275\u0275elementEnd"]())}function f(t,e){1&t&&(m["\u0275\u0275elementStart"](0,"div",27),m["\u0275\u0275element"](1,"span",28),m["\u0275\u0275elementEnd"]())}function y(t,e){1&t&&(m["\u0275\u0275elementStart"](0,"div",27),m["\u0275\u0275element"](1,"span",28),m["\u0275\u0275elementEnd"]())}function u(t,e){if(1&t&&(m["\u0275\u0275elementStart"](0,"div",29),m["\u0275\u0275elementStart"](1,"div",17),m["\u0275\u0275elementStart"](2,"div",30),m["\u0275\u0275elementStart"](3,"div",31),m["\u0275\u0275elementStart"](4,"div",32),m["\u0275\u0275elementStart"](5,"div",33),m["\u0275\u0275text"](6," Start time "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](7,"div",34),m["\u0275\u0275text"](8),m["\u0275\u0275pipe"](9,"date"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](10,"div",32),m["\u0275\u0275elementStart"](11,"div",33),m["\u0275\u0275text"](12," End time "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](13,"div",34),m["\u0275\u0275text"](14),m["\u0275\u0275pipe"](15,"date"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&t){const t=m["\u0275\u0275nextContext"]().$implicit;m["\u0275\u0275advance"](8),m["\u0275\u0275textInterpolate1"](" ",t.start_time?m["\u0275\u0275pipeBind2"](9,2,t.start_time,"dd-MMM-yy hh:mm a"):"-"," "),m["\u0275\u0275advance"](6),m["\u0275\u0275textInterpolate1"](" ",t.end_time?m["\u0275\u0275pipeBind2"](15,5,t.end_time,"dd-MMM-yy hh:mm a"):"-"," ")}}function x(t,e){1&t&&(m["\u0275\u0275elementStart"](0,"div",27),m["\u0275\u0275element"](1,"span",28),m["\u0275\u0275elementEnd"]())}const _=function(t){return{background:t}},v=function(t){return{"bold-font":t}};function b(t,e){if(1&t){const t=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div",17),m["\u0275\u0275elementStart"](1,"div",18),m["\u0275\u0275elementStart"](2,"div",19),m["\u0275\u0275element"](3,"span",20),m["\u0275\u0275elementStart"](4,"mat-icon",21),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](t);const n=e.index;return m["\u0275\u0275nextContext"]().toggleDetailMode(n)})),m["\u0275\u0275text"](5),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](6,"span",22),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](t);const n=e.index;return m["\u0275\u0275nextContext"]().toggleDetailMode(n)})),m["\u0275\u0275text"](7),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](8,"div",23),m["\u0275\u0275elementStart"](9,"span",24),m["\u0275\u0275text"](10),m["\u0275\u0275pipe"](11,"date"),m["\u0275\u0275elementStart"](12,"small"),m["\u0275\u0275text"](13),m["\u0275\u0275pipe"](14,"date"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](15,f,2,0,"div",25),m["\u0275\u0275template"](16,y,2,0,"div",25),m["\u0275\u0275template"](17,u,16,8,"div",26),m["\u0275\u0275template"](18,x,2,0,"div",25),m["\u0275\u0275elementEnd"]()}if(2&t){const t=e.$implicit,n=e.index,o=m["\u0275\u0275nextContext"]();m["\u0275\u0275advance"](3),m["\u0275\u0275property"]("ngStyle",m["\u0275\u0275pureFunction1"](17,_,null==t?null:t.status_color)),m["\u0275\u0275advance"](2),m["\u0275\u0275textInterpolate1"](" ",t.detail_mode_activated?"keyboard_arrow_down":"keyboard_arrow_right"," "),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngClass",m["\u0275\u0275pureFunction1"](19,v,t.detail_mode_activated))("matTooltip",null==t?null:t.status_name),m["\u0275\u0275advance"](1),m["\u0275\u0275textInterpolate1"](" ",null==t?null:t.status_name," "),m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate1"](" ",t.start_time?m["\u0275\u0275pipeBind2"](11,11,t.start_time,"dd-MMM-yy"):"-"," "),m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate"](t.start_time?m["\u0275\u0275pipeBind2"](14,14,t.start_time,"hh:mm a"):"-"),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",n<o.statusHistory.length-1),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",n==o.statusHistory.length-1&&t.detail_mode_activated),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",t.detail_mode_activated),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",n<o.statusHistory.length-1&&t.detail_mode_activated)}}function M(t,e){1&t&&(m["\u0275\u0275elementStart"](0,"div",35),m["\u0275\u0275elementStart"](1,"div",36),m["\u0275\u0275elementStart"](2,"span",37),m["\u0275\u0275text"](3,"No Status History found ! "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](4,"div",38),m["\u0275\u0275element"](5,"img",39),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]())}let C=(()=>{class t{constructor(t,e,n,o){this.dialogRef=t,this.inData=e,this._lcdpService=n,this._util=o,this._onDestroy=new r.b,this.isHistoryLoading=!1,this.statusHistory=[]}ngOnInit(){this.applicationId=this.inData.applicationId,this.recordId=this.inData.recordId,this.getStatusHistory();for(let t of this.statusHistory)t.detail_mode_activated=!1}getStatusHistory(){this.isHistoryLoading=!0,this._lcdpService.getStatusHistory(this.applicationId,this.recordId).pipe(Object(i.a)(this._onDestroy)).subscribe(t=>{this.isHistoryLoading=!1,"S"==t.messType&&t.data.length>0?this.statusHistory=t.data:this._util.showToastMessage(t.messText)},t=>{this.isHistoryLoading=!1,this._lcdpService.showErrorMessage(t)})}toggleDetailMode(t){this.statusHistory[t].detail_mode_activated=!this.statusHistory[t].detail_mode_activated}closeRecordHistoryModal(){this.dialogRef.close({event:"Close"})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}}return t.\u0275fac=function(e){return new(e||t)(m["\u0275\u0275directiveInject"](o.h),m["\u0275\u0275directiveInject"](o.a),m["\u0275\u0275directiveInject"](p.a),m["\u0275\u0275directiveInject"](g.a))},t.\u0275cmp=m["\u0275\u0275defineComponent"]({type:t,selectors:[["app-lcdp-status-history"]],decls:16,vars:3,consts:[[1,"container-fluid","record-history-styles"],[1,"row",2,"border-bottom","solid 1px #cacaca"],[1,"col-11","pt-2","pb-2","d-flex"],["mat-icon-button","",1,"bubble","mt-1"],[1,"iconButton"],[1,"headingBold","pl-2","pt-2"],[1,"col-1","d-flex"],["mat-icon-button","",1,"ml-auto","close-button","mt-1",3,"click"],["matTooltip","Close",1,"close-Icon"],["class","container d-flex h-100 flex-column",4,"ngIf"],[1,"row","pt-3"],["class","col-12",4,"ngFor","ngForOf"],["style","text-align: center;padding-top: 3rem;",4,"ngIf"],[1,"container","d-flex","h-100","flex-column"],[1,"flex-grow-1"],[1,"row","pt-4","justify-content-center"],["matTooltip","Loading...","diameter","30"],[1,"col-12"],[1,"row","p-0",2,"height","18px"],[1,"col-6","d-flex"],[1,"status-dot",3,"ngStyle"],[2,"font-size","15px","color","#4d4d4b","vertical-align","sub","cursor","pointer",3,"click"],[1,"pl-1","normalFont",2,"cursor","pointer",3,"ngClass","matTooltip","click"],[1,"col-6"],[1,"pl-2","normalFont","pt-1"],["class","row line-height",4,"ngIf"],["class","row",4,"ngIf"],[1,"row","line-height"],[2,"border-left","1px solid #c7c4c4"],[1,"row"],[1,"card","header-card","slide-in-top"],[1,"card-body","p-2"],[1,"row","p-2"],[1,"col-3","header"],[1,"col-9","normalFont","bold-font"],[2,"text-align","center","padding-top","3rem"],[1,"d-flex","pb-2","justify-content-center","align-items-center","slide-in-top"],[2,"font-size","15px","font-weight","normal"],[1,"d-flex","justify-content-center","align-items-center","slide-from-down","pt-2","pb-2"],["src","https://assets.kebs.app/images/nomilestone.png","height","170","width","200",1,"mt-2","mb-2"]],template:function(t,e){1&t&&(m["\u0275\u0275elementStart"](0,"div",0),m["\u0275\u0275elementStart"](1,"div",1),m["\u0275\u0275elementStart"](2,"div",2),m["\u0275\u0275elementStart"](3,"div",3),m["\u0275\u0275elementStart"](4,"mat-icon",4),m["\u0275\u0275text"](5,"timer"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](6,"span",5),m["\u0275\u0275text"](7," Status history "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](8,"div",6),m["\u0275\u0275elementStart"](9,"button",7),m["\u0275\u0275listener"]("click",(function(){return e.closeRecordHistoryModal()})),m["\u0275\u0275elementStart"](10,"mat-icon",8),m["\u0275\u0275text"](11,"close"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](12,h,5,0,"div",9),m["\u0275\u0275elementStart"](13,"div",10),m["\u0275\u0275template"](14,b,19,21,"div",11),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](15,M,6,0,"div",12),m["\u0275\u0275elementEnd"]()),2&t&&(m["\u0275\u0275advance"](12),m["\u0275\u0275property"]("ngIf",e.isHistoryLoading),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngForOf",e.statusHistory),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",0==e.statusHistory.length&&!e.isHistoryLoading))},directives:[l.a,s.a,d.a,a.NgIf,a.NgForOf,c.c,a.NgStyle,a.NgClass],pipes:[a.DatePipe],styles:[".record-history-styles[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]{display:flex;line-height:26px;border-radius:50%;cursor:context-menu!important;width:26px;height:26px;background-color:#fbfbfb;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.record-history-styles[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#cf0001;font-size:18px;margin-top:3px!important;margin-left:4px!important}.record-history-styles[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.record-history-styles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}.record-history-styles[_ngcontent-%COMP%]   .headingBold[_ngcontent-%COMP%]{color:#cf0001;font-weight:500}.record-history-styles[_ngcontent-%COMP%]   .headingBold[_ngcontent-%COMP%], .record-history-styles[_ngcontent-%COMP%]   .normalFont[_ngcontent-%COMP%]{font-size:14px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.record-history-styles[_ngcontent-%COMP%]   .normalFont[_ngcontent-%COMP%]{color:#181818;font-weight:400}.record-history-styles[_ngcontent-%COMP%]   .status-dot[_ngcontent-%COMP%]{height:13px;width:13px;border-radius:50%;display:inline-block;vertical-align:middle}.record-history-styles[_ngcontent-%COMP%]   .line-height[_ngcontent-%COMP%]{height:15px;padding-left:20px}.record-history-styles[_ngcontent-%COMP%]   .bold-font[_ngcontent-%COMP%]{font-weight:600}.record-history-styles[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-flex{position:relative;height:2.5rem;width:100%}.record-history-styles[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:100%;font-size:13px}.record-history-styles[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding-bottom:8px!important}.record-history-styles[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-form-field-underline{bottom:0}.record-history-styles[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-form-field-infix{padding:.65em 0!important;border-top:.54375em solid transparent!important;font-size:13px}.record-history-styles[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-select-arrow{margin:0 4px!important}.record-history-styles[_ngcontent-%COMP%]   .mini-tick[_ngcontent-%COMP%]{height:42px!important;width:42px!important;line-height:42px;background-color:#cf0001!important;color:#fff!important;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.record-history-styles[_ngcontent-%COMP%]   .header-card[_ngcontent-%COMP%]{transition:all .25s linear;height:auto}.record-history-styles[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{color:#5f5f5f;font-size:11px!important}.record-history-styles[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-20px);opacity:0}to{transform:translateY(0);opacity:1}}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}"]}),t})()}}]);