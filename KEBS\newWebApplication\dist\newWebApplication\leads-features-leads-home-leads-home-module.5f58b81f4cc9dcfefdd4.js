(window.webpackJsonp=window.webpackJsonp||[]).push([[787,535,631,634,765,858,861,981],{"/5jE":function(e,t,i){"use strict";i.d(t,"a",(function(){return o}));var a=i("mrSG"),n=i("XNiG"),r=i("2Vo4"),s=i("fXoL");let o=(()=>{class e{constructor(){this.reloadSubject=new n.b,this.searchSubject=new n.b,this.leadsReportGovernanceTypeSubject=new r.a([])}sendNotification(e){console.log(e),this.reloadSubject.next(e)}getNotification(){return this.reloadSubject.asObservable()}sendSearchContent(e){console.log(e),this.searchSubject.next(e)}getSearchContent(){return this.searchSubject.asObservable()}sendLeadsGovTypes(e){console.log(e),this.leadsReportGovernanceTypeSubject.next(e)}getLeadsGovTypes(){return this.leadsReportGovernanceTypeSubject.asObservable()}getLeadsGovTypesValue(){return Object(a.c)(this,void 0,void 0,(function*(){return this.leadsReportGovernanceTypeSubject.getValue()}))}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275prov=s["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},H44p:function(e,t,i){"use strict";i.d(t,"a",(function(){return g}));var a=i("xG9w"),n=i("fXoL"),r=i("flaP"),s=i("ofXK"),o=i("Qu3c"),d=i("NFeN");function l(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"div",9),n["\u0275\u0275elementStart"](1,"div",10),n["\u0275\u0275elementStart"](2,"div"),n["\u0275\u0275text"](3),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](4,"div"),n["\u0275\u0275elementStart"](5,"p",11),n["\u0275\u0275text"](6),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](7,"p",12),n["\u0275\u0275text"](8),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e){const e=n["\u0275\u0275nextContext"](2);n["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),n["\u0275\u0275advance"](3),n["\u0275\u0275textInterpolate"](null==e.currency[e.index]?null:e.currency[e.index].currency_code),n["\u0275\u0275advance"](3),n["\u0275\u0275textInterpolate"](e.label),n["\u0275\u0275advance"](2),n["\u0275\u0275textInterpolate1"](" ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function c(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"div",13),n["\u0275\u0275elementStart"](1,"span"),n["\u0275\u0275text"](2),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e){const e=n["\u0275\u0275nextContext"](2);n["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),n["\u0275\u0275advance"](2),n["\u0275\u0275textInterpolate1"](" ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function u(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"div",14),n["\u0275\u0275elementStart"](1,"span",15),n["\u0275\u0275text"](2),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e){const e=n["\u0275\u0275nextContext"](2);n["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),n["\u0275\u0275advance"](2),n["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function h(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"div",16),n["\u0275\u0275elementStart"](1,"span",15),n["\u0275\u0275text"](2),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e){const e=n["\u0275\u0275nextContext"](2);n["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),n["\u0275\u0275advance"](2),n["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function f(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"div",17),n["\u0275\u0275elementStart"](1,"span",18),n["\u0275\u0275text"](2),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e){const e=n["\u0275\u0275nextContext"](2);n["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),n["\u0275\u0275advance"](2),n["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function p(e,t){1&e&&(n["\u0275\u0275elementStart"](0,"mat-icon",19),n["\u0275\u0275text"](1,"loop"),n["\u0275\u0275elementEnd"]())}function m(e,t){if(1&e){const e=n["\u0275\u0275getCurrentView"]();n["\u0275\u0275elementStart"](0,"div",1),n["\u0275\u0275listener"]("click",(function(){return n["\u0275\u0275restoreView"](e),n["\u0275\u0275nextContext"]().change()})),n["\u0275\u0275template"](1,l,9,4,"div",2),n["\u0275\u0275template"](2,c,3,2,"div",3),n["\u0275\u0275template"](3,u,3,3,"div",4),n["\u0275\u0275template"](4,h,3,3,"div",5),n["\u0275\u0275template"](5,f,3,3,"div",6),n["\u0275\u0275elementStart"](6,"div",7),n["\u0275\u0275template"](7,p,2,0,"mat-icon",8),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()}if(2&e){const e=n["\u0275\u0275nextContext"]();n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf","big"==e.type),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf","small"==e.type),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf","medium"==e.type),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf","large"==e.type),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf","overview"==e.type),n["\u0275\u0275advance"](2),n["\u0275\u0275property"]("ngIf",e.toDisplay)}}let g=(()=>{class e{constructor(e){this.roleService=e,this.showActualAmount=!1,this.currency=[]}set currencyList(e){if(this.currency=e,e){const t=a.findIndex(e,e=>e.currency_code==this.currency_code);-1!=t&&(this.index=t,this.currency[this.index].value=Math.trunc(this.currency[this.index].value))}}ngOnInit(){null==this.toDisplay&&(this.toDisplay=!0),this.tenant_info=this.roleService.currency_info,this.tenantName=this.tenant_info.tenant_name,this.currency_code=this.defaultCurrencyCode?this.defaultCurrencyCode:this.tenant_info.default_currency?this.tenant_info.default_currency:"INR",this.setCurrency(),this.isConvertValue=null==this.tenant_info.is_to_convert_currency_value||this.tenant_info.is_to_convert_currency_value}setCurrency(){if(this.currency){const e=a.findIndex(this.currency,e=>e.currency_code==this.currency_code);-1!=e&&(this.index=e,this.currency[this.index].value=Math.trunc(this.currency[this.index].value))}}change(){void 0!==event.stopPropagation?event.stopPropagation():void 0!==event.cancelBubble&&(event.cancelBubble=!0),this.currency.length>1&&(this.index=++this.index%this.currency.length),this.currency[this.index].value=Math.trunc(this.currency[this.index].value)}convert(e,t){return e?(this.showActualAmount?e=new Intl.NumberFormat("INR"==t?"en-IN":"en-US",{}).format(e)+" "+t:1==this.isConvertValue?e="INR"==t?(e/1e7).toFixed(2)+" Cr":(e/1e6).toFixed(2)+" M":0!=this.isConvertValue||a.contains(["big","small"],this.type)?0==this.isConvertValue&&a.contains(["big","small"],this.type)&&(e="INR"==t?"big"!=this.type?"INR "+new Intl.NumberFormat("en-IN",{}).format(e):new Intl.NumberFormat("en-IN",{}).format(e):"big"!=this.type?t+" "+new Intl.NumberFormat("en-US",{}).format(e):new Intl.NumberFormat("en-US",{}).format(e)):e="INR"==t?(e/1e7).toFixed(2)+" Cr":(e/1e6).toFixed(2)+" M",e):"-"}}return e.\u0275fac=function(t){return new(t||e)(n["\u0275\u0275directiveInject"](r.a))},e.\u0275cmp=n["\u0275\u0275defineComponent"]({type:e,selectors:[["app-currency"]],inputs:{type:"type",label:"label",showActualAmount:"showActualAmount",acl:"acl",toDisplay:"toDisplay",defaultCurrencyCode:"defaultCurrencyCode",currencyList:"currencyList"},decls:1,vars:1,consts:[["class","d-flex currency-wrapper",3,"click",4,"ngIf"],[1,"d-flex","currency-wrapper",3,"click"],["class","d-flex",3,"matTooltip",4,"ngIf"],["class","data-label",3,"matTooltip",4,"ngIf"],["class","data-medium d-flex",3,"matTooltip",4,"ngIf"],["class","data-large d-flex",3,"matTooltip",4,"ngIf"],["class","justify-content-center",3,"matTooltip",4,"ngIf"],[1,"d-flex","align-items-center"],["class","change-icon pl-2","matTooltip","Next currency",4,"ngIf"],[1,"d-flex",3,"matTooltip"],[1,"pr-3","my-auto","currency-code","justify-content-center"],[1,"m-0","header"],[1,"m-0","data-label","pt-1"],[1,"data-label",3,"matTooltip"],[1,"data-medium","d-flex",3,"matTooltip"],[1,"justify-content-center"],[1,"data-large","d-flex",3,"matTooltip"],[1,"justify-content-center",3,"matTooltip"],[1,"data-overview"],["matTooltip","Next currency",1,"change-icon","pl-2"]],template:function(e,t){1&e&&n["\u0275\u0275template"](0,m,8,6,"div",0),2&e&&n["\u0275\u0275property"]("ngIf",t.currency)},directives:[s.NgIf,o.a,d.a],styles:[".header[_ngcontent-%COMP%]{color:#5f5f5f;font-size:11px!important}.data-label[_ngcontent-%COMP%]{font-size:13px!important}.change-icon[_ngcontent-%COMP%]{font-size:18px;width:auto;height:auto;visibility:hidden;cursor:default}.currency-code[_ngcontent-%COMP%]{padding-right:8px;font-size:14px!important}.currency-wrapper[_ngcontent-%COMP%]{white-space:nowrap!important;height:100%}.currency-wrapper[_ngcontent-%COMP%]:hover   .change-icon[_ngcontent-%COMP%]{visibility:visible}.data-medium[_ngcontent-%COMP%]{color:#252422!important;font-size:15px!important}.data-large[_ngcontent-%COMP%], .data-medium[_ngcontent-%COMP%]{text-align:center!important;font-weight:440!important;padding-right:4px!important}.data-large[_ngcontent-%COMP%]{font-size:large!important}.data-overview[_ngcontent-%COMP%]{color:#252422!important;font-size:15px!important;padding-left:24px!important;font-weight:500!important;padding-right:4px!important;text-align:center!important;margin:auto!important}"]}),e})()},NJ67:function(e,t,i){"use strict";i.d(t,"a",(function(){return a}));class a{constructor(){this.disabled=!1}onChange(e){}onTouched(e){}writeValue(e){this.value=e}registerOnChange(e){this.onChange=e}registerOnTouched(e){this.onTouched=e}setDisabledState(e){this.disabled=e}}},R3G1:function(e,t,i){"use strict";i.d(t,"a",(function(){return r}));var a=i("xG9w"),n=i("fXoL");let r=(()=>{class e{transform(e,t,i){let n=a.findWhere(t,{field_name:e,type:i});return!!n&&!!n.is_active}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=n["\u0275\u0275definePipe"]({name:"checkActive",type:e,pure:!0}),e})()},"TmG/":function(e,t,i){"use strict";i.d(t,"a",(function(){return y}));var a=i("fXoL"),n=i("3Pt+"),r=i("jtHE"),s=i("XNiG"),o=i("NJ67"),d=i("1G5W"),l=i("kmnG"),c=i("ofXK"),u=i("d3UM"),h=i("FKr1"),f=i("WJ5W"),p=i("Qu3c");function m(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"mat-label"),a["\u0275\u0275text"](1),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"]();a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate"](e.placeholder)}}function g(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"mat-option",7),a["\u0275\u0275text"](1),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"]();a["\u0275\u0275property"]("value",null),a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate"](e.disableNone?"Select One":"None")}}function v(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"mat-option",8),a["\u0275\u0275listener"]("click",(function(){a["\u0275\u0275restoreView"](e);const i=t.$implicit;return a["\u0275\u0275nextContext"]().emitChanges(i)})),a["\u0275\u0275text"](1),a["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;a["\u0275\u0275property"]("value",e.id||e._id)("matTooltip",e.name),a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let y=(()=>{class e extends o.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new n.j,this.fieldFilterCtrl=new n.j,this.list=[],this.required=!1,this.disableNone=!1,this.valueChange=new a.EventEmitter,this.disabled=!1,this.hasNoneOption=!0,this.filteredList=new r.a,this.change=new a.EventEmitter,this.hideMatLabel=!1,this._onDestroy=new s.b,this.emitChanges=e=>{this.change.emit(e)}}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(d.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(d.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275directiveInject"](a.Renderer2))},e.\u0275cmp=a["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search"]],inputs:{list:"list",placeholder:"placeholder",required:"required",disableNone:"disableNone",disabled:"disabled",hasNoneOption:"hasNoneOption",hideMatLabel:"hideMatLabel"},outputs:{valueChange:"valueChange",change:"change"},features:[a["\u0275\u0275ProvidersFeature"]([{provide:n.t,useExisting:Object(a.forwardRef)(()=>e),multi:!0}]),a["\u0275\u0275InheritDefinitionFeature"],a["\u0275\u0275NgOnChangesFeature"]],decls:9,vars:11,consts:[["appearance","outline"],[4,"ngIf"],[3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value",4,"ngIf"],[3,"value","matTooltip","click",4,"ngFor","ngForOf"],[3,"value"],[3,"value","matTooltip","click"]],template:function(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"mat-form-field",0),a["\u0275\u0275template"](1,m,2,1,"mat-label",1),a["\u0275\u0275elementStart"](2,"mat-select",2,3),a["\u0275\u0275elementStart"](4,"mat-option"),a["\u0275\u0275element"](5,"ngx-mat-select-search",4),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](6,g,2,2,"mat-option",5),a["\u0275\u0275template"](7,v,2,3,"mat-option",6),a["\u0275\u0275pipe"](8,"async"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e&&(a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",!t.hideMatLabel),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",t.hasNoneOption),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngForOf",a["\u0275\u0275pipeBind1"](8,9,t.filteredList)))},directives:[l.c,c.NgIf,u.c,n.v,n.k,n.F,h.p,f.a,c.NgForOf,l.g,p.a],pipes:[c.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%}"]}),e})()},hJL4:function(e,t,i){"use strict";i.d(t,"a",(function(){return u}));var a=i("mrSG"),n=i("XNiG"),r=i("xG9w"),s=i("fXoL"),o=i("tk/3"),d=i("LcQX"),l=i("XXEo"),c=i("flaP");let u=(()=>{class e{constructor(e,t,i,a){this.http=e,this.UtilityService=t,this.loginService=i,this.roleService=a,this.msg=new n.b,this.taskStatusColor=[],this.approveRequest=(e,t)=>this.http.post("/api/isa/request/approveResourceRequest",{requestDetails:e,approverOid:t}),this.rejectRequest=(e,t)=>this.http.post("/api/isa/request/rejectResourceRequest",{requestDetails:e,approverOid:t}),this.getTaskStatusColor()}getAllRequestsOfUser(e,t,i,a,n,r,s){let o=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getAllRequestsOfUser",{oid:e,statusId:t,statusCode:i,objectIds:a,skip:n,limit:r,filterConfig:s,orgIds:o})}getAllRoleAccess(){return r.where(this.roleService.roles,{application_id:139})}getRequestsBasedOnStatus(e,t,i,a,n,r,s){let o=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getRequestsBasedOnStatus",{oid:e,statusId:t,statusCode:i,objectIds:a,skip:n,limit:r,filterConfig:s,orgIds:o})}getRequestsForAwaitingApproval(e,t,i,a){return this.http.post("/api/isa/request/getRequestsForAwaitingApproval",{oid:e,skip:t,limit:i,filterConfig:a})}getObjectIdsBasedOnOid(e){return this.http.post("/api/isa/request/getObjectIdsBasedOnOid",{oid:e})}getStatusCountBasedOnOid(e,t,i,a){let n=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getStatusCountBasedOnOid",{oid:e,objectIds:t,dataTypeArray:i,filterConfig:a,orgIds:n})}getHeaderCount(e){return this.http.post("/api/isa/request/getHeaderCount",{oid:e})}getRequestBasedOnStatus(e,t){return this.http.post("/api/isa/request/getRequestBasedOnStatus",{oid:e,status:t})}getDataTypeArray(){return this.http.post("/api/isa/request/getISAStatusMasterDataUDRF",{})}sendMsg(e){this.msg.next(e)}getMsg(){return this.msg.asObservable()}getTaskStatusColor(){return new Promise((e,t)=>{this.http.post("/api/isa/request/getTaskStatusNameColor",{}).subscribe(t=>(this.taskStatusColor=t,e(t)),e=>{})})}getAllRequestsForRmg(e){return this.http.post("/api/isa/request/getAllRequestsForRmg",{filterConfig:e})}getTotalForRmg(e){return new Promise((t,i)=>{this.http.post("/api/isa/request/getTotalForRmg",{filterConfig:e}).subscribe(e=>t(e),e=>{})})}getDefaultISAStatusToDisplay(){return this.http.post("/api/isa/request/getISADefaultStatusToDisplay",{})}getCTCbreakUpForSkillSet(e){return new Promise((t,i)=>{this.http.post("/api/isa/request/getCTCbreakUpForSkillSet",e).subscribe(e=>t(e.data),e=>{i(e)})})}getCurrentUserRole(){return this.roleService&&this.roleService.roles&&this.roleService.roles.length>0?this.roleService.roles[0].role_id:null}getTemplateForUser(e){return this.http.post("/api/isa/request/getVisibleFormFieldsForUser",e)}saveAndApproveRequest(e){return this.http.post("/api/isa/request/updBudgetInfoByRequestId",e)}getVisibilityMatrix(e){return this.http.post("/api/isa/request/getAuthorzedFieldsByRoleId",e)}getISAAttachmentFromS3(e){return this.http.post("/api/isa/attachment/getISAAttachmentFromS3",{key:e})}getSkillType(){return this.http.post("/api/isa/request/getSkillType",{})}getSkillLevel(){return this.http.post("/api/isa/request/getSkillLevel",{})}getSkillExperience(){return this.http.post("/api/isa/request/getSkillExperience",{})}getSkillName(e){return this.http.post("/api/isa/request/getSkillName",{skill_type_id:e})}getIsaActivityReportDownload(){return this.http.post("/api/isa/request/getIsaActivityReportDownload",{})}getISASLAreport(e){return this.http.post("/api/isa/request/getISASLAreport",{filterConfig:e})}getISACustomReport(e){return this.http.post("/api/isa/request/getISACustomReport",{filterConfig:e})}getPositionType(){return this.http.post("/api/employee360/masterData/getPositionType",{})}calculateNetCostBasedOnPosition(e,t,i,n,s,o,d){return Object(a.c)(this,void 0,void 0,(function*(){let a;a=o&&o.length>1&&(yield this.getManpowerCostByOId(o,i,s,2))||(yield this.getManpowerCostBasedOnPosition(e,t,i,s,d));let l=yield this.getNonManpowerCost(t,i,n,s,2),c=yield this.getAllocatedCost(),u=0;u=(a?a.cost:0)+l.length>0?r.reduce(r.pluck(l,"cost"),(e,t)=>e+t,0):0;let h=c.length>0?r.reduce(r.pluck(c,"percentage"),(e,t)=>e+t,0):0;return{cost:u,currency:a&&a.currency_code?a.currency_code:"",manpowerCost:a,nonManpowerCost:l,allocatedCost:c,allocatedCostValue:u*(h/100)}}))}getManpowerCostBasedOnPosition(e,t,i,a,n){return new Promise((r,s)=>{this.http.post("/api/isa/configuration/getManpowerCostBasedOnPosition",{skillRoleId:e,nationalityId:t,locationGroupId:i,unit:a,position:n}).subscribe(e=>r(e),e=>(console.log(e),s(e)))})}getNonManpowerCost(e,t,i,a,n){return new Promise((r,s)=>{this.http.post("/api/project/getNonManpowerCost",{nationalityId:e,locationGroupId:t,locationId:i,unit:a,currency_id:n}).subscribe(e=>r(e),e=>(console.log(e),s(e)))})}getAllocatedCost(){return new Promise((e,t)=>{this.http.post("/api/project/getAllocatedCost","").subscribe(t=>e(t),e=>(console.log(e),t(e)))})}getManpowerCostByOId(e,t,i,a){return new Promise((n,r)=>{this.http.post("/api/project/getEmployeeSalary",{oid:e,location_group_id:t,unit_id:i,currency_id:a}).subscribe(e=>n(e),e=>(console.log(e),r(e)))})}getSkillNameMaster(){return this.http.post("/api/isa/configuration/getSkillName",{})}getVendorOid(e,t){return this.http.post("/api/isa/request/getVendorOid",{vendorNames:e,docID:t})}getVendorList(e,t){return this.http.post("/api/isa/request/getVendorList",{documentID:e,limitval:t})}removeVendor(e,t){return this.http.post("/api/isa/request/removeVendors",{vendorOid:e,documentID:t})}checkVendorOrNot(){return this.http.post("/api/isa/request/checkVendorOrNot",{})}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275inject"](o.c),s["\u0275\u0275inject"](d.a),s["\u0275\u0275inject"](l.a),s["\u0275\u0275inject"](c.a))},e.\u0275prov=s["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},os0P:function(e,t,i){"use strict";i.d(t,"a",(function(){return b}));var a=i("mrSG"),n=i("fXoL"),r=i("3Pt+"),s=i("jtHE"),o=i("XNiG"),d=i("NJ67"),l=i("1G5W"),c=i("xG9w"),u=i("t44d"),h=i("kmnG"),f=i("ofXK"),p=i("d3UM"),m=i("FKr1"),g=i("WJ5W"),v=i("Qu3c");const y=["singleSelect"];function S(e,t){1&e&&(n["\u0275\u0275elementStart"](0,"mat-option",6),n["\u0275\u0275text"](1,"Select one"),n["\u0275\u0275elementEnd"]()),2&e&&n["\u0275\u0275property"]("value",null)}function C(e,t){if(1&e){const e=n["\u0275\u0275getCurrentView"]();n["\u0275\u0275elementStart"](0,"mat-option",7),n["\u0275\u0275listener"]("click",(function(){n["\u0275\u0275restoreView"](e);const i=t.$implicit;return n["\u0275\u0275nextContext"]().emitChanges(i)})),n["\u0275\u0275text"](1),n["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;n["\u0275\u0275propertyInterpolate"]("matTooltip",e.name),n["\u0275\u0275property"]("value",e.id),n["\u0275\u0275advance"](1),n["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let b=(()=>{class e extends d.a{constructor(e,t){super(),this.renderer=e,this.pmMasterService=t,this.fieldCtrl=new r.j,this.fieldFilterCtrl=new r.j,this.list=[],this.required=!1,this.valueChange=new n.EventEmitter,this.disabled=!1,this.disableColor="#E8E9EE",this.disableField=!1,this.showSelect=!0,this.uniqueList=[],this.filteredList=new s.a,this.change=new n.EventEmitter,this._onDestroy=new o.b,this.formConfig=[],this.emitChanges=e=>{this.change.emit(e)}}ngOnInit(){return Object(a.c)(this,void 0,void 0,(function*(){yield this.pmMasterService.getPMFormCustomizeConfigV().then(e=>{e&&(this.formConfig=e)});const e=c.where(this.formConfig,{type:"project-theme",field_name:"styles",is_active:!0});this.fontStyle=e.length>0&&e[0].data.font_style?e[0].data.font_style:"Roboto",document.documentElement.style.setProperty("--inputSearchFont",this.fontStyle),this.fieldFilterCtrl.valueChanges.pipe(Object(l.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(l.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)}),this.updateFilteredList()}))}ngOnChanges(){this.updateFilteredList()}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value,t=this.list.slice();e&&(e=e.toLowerCase(),t=t.filter(t=>t.name.toLowerCase().indexOf(e)>-1)),this.uniqueList.length>0&&(t=t.filter(e=>this.uniqueList.includes(e))),this.filteredList.next(t)}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}isDisabled(){return this.disableField?{"background-color":null!=this.disableColor?this.disableColor:"#E8E9EE","pointer-events":"none",color:"#6E7B8F"}:{}}writeValue(e){this.fieldCtrl.setValue(e)}updateFilteredList(){this.uniqueList.length>0?this.filterBanks():this.filteredList.next(this.list.slice())}}return e.\u0275fac=function(t){return new(t||e)(n["\u0275\u0275directiveInject"](n.Renderer2),n["\u0275\u0275directiveInject"](u.a))},e.\u0275cmp=n["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search-name"]],viewQuery:function(e,t){if(1&e&&n["\u0275\u0275viewQuery"](y,!0),2&e){let e;n["\u0275\u0275queryRefresh"](e=n["\u0275\u0275loadQuery"]())&&(t.singleSelect=e.first)}},inputs:{list:"list",placeholder:"placeholder",required:"required",disabled:"disabled",disableColor:"disableColor",disableField:"disableField",showSelect:"showSelect",uniqueList:"uniqueList"},outputs:{valueChange:"valueChange",change:"change"},features:[n["\u0275\u0275ProvidersFeature"]([{provide:r.t,useExisting:Object(n.forwardRef)(()=>e),multi:!0}]),n["\u0275\u0275InheritDefinitionFeature"],n["\u0275\u0275NgOnChangesFeature"]],decls:8,vars:11,consts:[["appearance","outline",1,"app-input-search",3,"ngStyle"],["nameOnly","",3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value",4,"ngIf"],[3,"value","matTooltip","click",4,"ngFor","ngForOf"],[3,"value"],[3,"value","matTooltip","click"]],template:function(e,t){1&e&&(n["\u0275\u0275elementStart"](0,"mat-form-field",0),n["\u0275\u0275elementStart"](1,"mat-select",1,2),n["\u0275\u0275elementStart"](3,"mat-option"),n["\u0275\u0275element"](4,"ngx-mat-select-search",3),n["\u0275\u0275elementEnd"](),n["\u0275\u0275template"](5,S,2,1,"mat-option",4),n["\u0275\u0275template"](6,C,2,3,"mat-option",5),n["\u0275\u0275pipe"](7,"async"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e&&(n["\u0275\u0275property"]("ngStyle",t.isDisabled()),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),n["\u0275\u0275advance"](3),n["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf",t.showSelect),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngForOf",n["\u0275\u0275pipeBind1"](7,9,t.filteredList)))},directives:[h.c,f.NgStyle,p.c,r.v,r.k,r.F,m.p,g.a,f.NgIf,f.NgForOf,v.a],pipes:[f.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}.mat-form-field[_ngcontent-%COMP%]:disabled{background-color:#0ff}.app-input-search[_ngcontent-%COMP%]     .mat-select-value{color:var(--blue-grey-80,#5f6c81);font-family:var(--inputSearchFont)!important;font-size:13px;font-style:normal;font-weight:400;line-height:16px}"]}),e})()},vCyf:function(e,t,i){"use strict";i.r(t),i.d(t,"LeadsHomeModule",(function(){return j}));var a=i("ofXK"),n=i("tyNb"),r=i("mrSG"),s=i("xG9w"),o=i("wd/R"),d=i("1G5W"),l=i("XNiG"),c=i("fXoL"),u=i("+yIk"),h=i("dNgK"),f=i("GnQ3"),p=i("LcQX"),m=i("F97M"),g=i("pgif"),v=i("0IaG"),y=i("/5jE"),S=i("xi/V"),C=i("Wk3H");const b=[{path:"",component:(()=>{class e{constructor(e,t,i,a,n,d,c,u,h){this.leadService=e,this.snackBar=t,this.udrfService=i,this.utilityService=a,this.graphService=n,this.opportunityService=d,this.dialog=c,this.reloadService=u,this.router=h,this.applicationId=35,this.selectedCard=[],this.isCardClicked=!1,this.WLItemDataCurrentIndex=0,this.current_year_start=o(),this.current_year_end=o(),this._onAppApiCalled=new l.b,this._onDestroy=new l.b,this.dataTypeArray=[],this.udrfBodyColumns=[],this.udrfItemStatusColor=[{status:"Open",color:"#e2e2e2"},{status:"In Progress",color:"#ffa502"},{status:"Completed",color:"#009432"}],this.categorisedDataTypeArray=[],this.minNoOfVisibleSummaryCards=3,this.maxNoOfVisibleSummaryCards=15,this.resolveVisibleDataTypeArray=()=>{for(let e of this.udrfService.udrfUiData.summaryCards){let t;this.udrfService.udrfData.udrfSummaryCardCodes.length>0&&(t=s.contains(this.udrfService.udrfData.udrfSummaryCardCodes,e.dataTypeCode),e.isVisible=t,t&&(this.udrfService.udrfUiData.summaryCardsItem=e))}},this.dataTypeCardSelected=()=>{this.udrfService.udrfData.isItemDataLoading=!0,this.udrfService.udrfData.noItemDataFound=!1;let e=this.udrfService.udrfUiData.summaryCardsItem;for(let t=0;t<this.dataTypeArray.length;t++)this.dataTypeArray[t].dataTypeCode!=e.dataTypeCode&&(this.dataTypeArray[t].isActive=!1);e.isActive=!e.isActive,this.isCardClicked=!0,this.WLItemDataCurrentIndex=0,this.udrfService.udrfBodyData=[],this.getLeadsList()},this.deleteLead=()=>{let e=this.udrfService.udrfUiData.openDeleteButtonData.data;console.log(e),this.utilityService.openConfirmationSweetAlertWithCustom("Are you sure you want to delete ?","Once you confirm, this lead will be deleted !").then(t=>Object(r.c)(this,void 0,void 0,(function*(){t&&this.leadService.deleteLead(e.lead_id).then(e=>Object(r.c)(this,void 0,void 0,(function*(){"S"==e.messType?(this.snackBar.open(e.description,"Dismiss",{duration:2e3}),this.initReport()):this.snackBar.open(e.description,"Dismiss",{duration:2e3})})))})))},this.current_year_start=o().startOf("year"),this.current_year_end=o().endOf("year")}ngOnInit(){return Object(r.c)(this,void 0,void 0,(function*(){yield this.leadService.getUDRFLeadConfig().then(e=>{if(console.log("yessssssssssss"),"S"==e.messType){for(var t of e.result)"openLeads"==(t.onColumnClick?t.onColumnClick:"")&&(t.onColumnClick=this.openLead.bind(this));this.udrfBodyColumns=e.result}}).catch(e=>{console.log(e)}),this.leadService.getLeadsSalesStatus().then(e=>{let t=[];s.each(e,e=>{if(null!=e.udrf_summary_card){let i=JSON.parse(e.udrf_summary_card);t.push(i.dataTypeCode),this.dataTypeArray.push(i),this.udrfItemStatusColor.push({color:i.color,status:i.dataTypeCode})}}),this.categorisedDataTypeArray.push({categoryType:"Status Cards",categoryCardCodes:t,categoryCards:[]}),console.log(this.udrfItemStatusColor)}),this.leadService.getLeadSource().then(e=>{let t=[];s.each(e,e=>{if(null!=e.udrf_summary_card){let i=JSON.parse(e.udrf_summary_card);t.push(i.dataTypeCode),this.dataTypeArray.push(i),this.udrfItemStatusColor.push({color:i.color,status:i.dataTypeCode})}}),this.categorisedDataTypeArray.push({categoryType:"Source Cards",categoryCardCodes:t,categoryCards:[]}),console.log(this.udrfItemStatusColor)}),this.leadService.leadsChannelMaster().subscribe(e=>{let t=[];s.each(e,e=>{if(null!=e.udrf_summary_card){let i=JSON.parse(e.udrf_summary_card);t.push(i.dataTypeCode),this.dataTypeArray.push(i),this.udrfItemStatusColor.push({color:i.color,status:i.dataTypeCode})}}),this.categorisedDataTypeArray.push({categoryType:"Channel Cards",categoryCardCodes:t,categoryCards:[]}),console.log(this.udrfItemStatusColor)}),this.reloadService.getNotification().subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){console.log(e),"reload leads"==e&&this.initReport()})));let e=[{checkboxId:"CDCRD",checkboxName:"Current Year",checkboxStartValue:this.current_year_start,checkboxEndValue:this.current_year_end,isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD2",checkboxName:"This Week",checkboxStartValue:this.utilityService.getFormattedDate(o().startOf("week"),o(o().startOf("week")).date,0,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(o().endOf("week"),o(o().endOf("week")).date,0,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD3",checkboxName:"This Month",checkboxStartValue:this.utilityService.getFormattedDate(o().startOf("month"),o(o().startOf("month")).date,0,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(o().endOf("month"),o(o().endOf("month")).date,0,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD4",checkboxName:"Next Month",checkboxStartValue:this.utilityService.getFormattedDate(o().add(1,"month").startOf("month"),o(o().add(1,"month").startOf("month")).date,0,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(o().add(1,"month").endOf("month"),o(o().add(1,"month").endOf("month")).date,0,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD5",checkboxName:"Upcoming 3 Months",checkboxStartValue:this.utilityService.getFormattedDate(o().startOf("month"),o(o().startOf("month")).date,0,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(o().add(2,"month").endOf("month"),o(o().add(2,"month").endOf("month")).date,0,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD6",checkboxName:"Previous Month",checkboxStartValue:this.utilityService.getFormattedDate(o().subtract(1,"month").startOf("month"),o(o().subtract(1,"month").startOf("month")).date,0,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(o().subtract(1,"month").endOf("month"),o(o().subtract(1,"month").endOf("month")).date,0,0,0,0),isCheckboxDefaultSelected:!1}],t=[{checkboxId:"CDCRD",checkboxName:"Current Year",checkboxStartValue:this.current_year_start,checkboxEndValue:this.current_year_end,isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD2",checkboxName:"This Week",checkboxStartValue:this.utilityService.getFormattedDate(o().startOf("week"),o(o().startOf("week")).date,0,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(o().endOf("week"),o(o().endOf("week")).date,0,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD3",checkboxName:"This Month",checkboxStartValue:this.utilityService.getFormattedDate(o().startOf("month"),o(o().startOf("month")).date,0,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(o().endOf("month"),o(o().endOf("month")).date,0,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD4",checkboxName:"Next Month",checkboxStartValue:this.utilityService.getFormattedDate(o().add(1,"month").startOf("month"),o(o().add(1,"month").startOf("month")).date,0,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(o().add(1,"month").endOf("month"),o(o().add(1,"month").endOf("month")).date,0,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD6",checkboxName:"Previous Month",checkboxStartValue:this.utilityService.getFormattedDate(o().subtract(1,"month").startOf("month"),o(o().subtract(1,"month").startOf("month")).date,0,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(o().subtract(1,"month").endOf("month"),o(o().subtract(1,"month").endOf("month")).date,0,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD5",checkboxName:"Upcoming 3 Months",checkboxStartValue:this.utilityService.getFormattedDate(o().startOf("month"),o(o().startOf("month")).date,0,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(o().add(2,"month").endOf("month"),o(o().add(2,"month").endOf("month")).date,0,0,0,0),isCheckboxDefaultSelected:!1}],i=[{checkboxId:"CONCB",checkboxName:"Current Year",checkboxStartValue:this.current_year_start,checkboxEndValue:this.current_year_end,isCheckboxDefaultSelected:!1},{checkboxId:"CONCB1",checkboxName:"This Week",checkboxStartValue:this.utilityService.getFormattedDate(o().startOf("week"),o(o().startOf("week")).date,0,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(o().endOf("week"),o(o().endOf("week")).date,0,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CONCB2",checkboxName:"This Month",checkboxStartValue:this.utilityService.getFormattedDate(o().startOf("month"),o(o().startOf("month")).date,0,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(o().endOf("month"),o(o().endOf("month")).date,0,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CONCB3",checkboxName:"Next Month",checkboxStartValue:this.utilityService.getFormattedDate(o().add(1,"month").startOf("month"),o(o().add(1,"month").startOf("month")).date,0,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(o().add(1,"month").endOf("month"),o(o().add(1,"month").endOf("month")).date,0,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CONCB4",checkboxName:"Previous Month",checkboxStartValue:this.utilityService.getFormattedDate(o().subtract(1,"month").startOf("month"),o(o().subtract(1,"month").startOf("month")).date,0,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(o().subtract(1,"month").endOf("month"),o(o().subtract(1,"month").endOf("month")).date,0,0,0,0),isCheckboxDefaultSelected:!1}];this.udrfService.udrfFunctions.constructCustomRangeData(23,"date",e),this.udrfService.udrfFunctions.constructCustomRangeData(24,"date",t),this.udrfService.udrfFunctions.constructCustomRangeData(25,"date",i),this.udrfService.udrfBodyData=[],this.udrfService.udrfData.applicationId=this.applicationId,this.udrfService.udrfUiData.showNewReleasesButton=!0,this.udrfService.udrfUiData.showItemDataCount=!0,this.udrfService.udrfUiData.itemDataType="",this.udrfService.udrfUiData.bookmarkId="id",this.udrfService.udrfUiData.totalItemDataCount=0,this.udrfService.udrfUiData.showSearchBar=!0,this.udrfService.udrfUiData.showActionButtons=!0,this.udrfService.udrfUiData.showUdrfModalButton=!0,this.udrfService.udrfUiData.showSettingsModalButton=!0,this.udrfService.udrfUiData.isReportDownloading=!1,this.udrfService.udrfUiData.showReportDownloadButton=!1,this.udrfService.udrfUiData.showColumnConfigButton=!0,this.udrfService.udrfUiData.showGroupByButton=!0,this.udrfService.udrfUiData.itemHasOpenInNewTab=!0,this.udrfService.udrfUiData.itemHasStarRating=!0,this.udrfService.udrfUiData.openInNewTab=this.openLeadsPageinNewTab.bind(this),this.udrfService.udrfUiData.itemHasOpenModal=!0,this.udrfService.udrfUiData.openModal=this.openEditScreen.bind(this),this.udrfService.udrfUiData.openModalName="Edit",this.udrfService.udrfUiData.openModalMatIcon="edit",this.udrfService.udrfUiData.udrfBodyColumns=this.udrfBodyColumns,this.udrfService.udrfUiData.horizontalScroll=!0,this.udrfService.udrfUiData.itemHasDeleteButton=this.leadService.getDeleteLeadRole(),this.udrfService.udrfUiData.openDeleteButton=this.deleteLead.bind(this),this.udrfService.udrfUiData.resolveVisibleSummaryCards=this.resolveVisibleDataTypeArray.bind(this),this.udrfService.udrfUiData.summaryCardsSelected=this.dataTypeCardSelected.bind(this),this.udrfService.udrfUiData.itemcardSelected=this.openLead.bind(this),this.udrfService.udrfUiData.itemDataScrollDown=this.onLeadListScrollDown.bind(this),this.udrfService.udrfFunctions.resolveVisibleColumnConfigItems(),this.udrfService.udrfUiData.summaryCardsItem={},this.udrfService.udrfUiData.callInlineEditApi=this.callInlineEditApi.bind(this),this.udrfService.udrfUiData.inlineEditData={},this.udrfService.udrfUiData.inlineEditDropDownMasterDatas={},this.udrfService.udrfUiData.udrfItemStatusColor=this.udrfItemStatusColor,this.udrfService.udrfUiData.summaryCards=this.dataTypeArray,this.udrfService.udrfUiData.udrfVisibleBodyColumns=this.udrfService.udrfUiData.udrfVisibleBodyColumns,this.udrfService.udrfUiData.udrfInvisibleBodyColumns=this.udrfService.udrfUiData.udrfInvisibleBodyColumns,this.udrfService.udrfUiData.categorisedSummaryCards=this.categorisedDataTypeArray,this.udrfService.udrfUiData.minNoOfVisibleSummaryCards=this.minNoOfVisibleSummaryCards,this.udrfService.udrfUiData.maxNoOfVisibleSummaryCards=this.maxNoOfVisibleSummaryCards,this.udrfService.udrfUiData.selectedCard=this.selectedCard,this.udrfService.udrfUiData.variant=0,this.udrfService.udrfUiData.itemHasQuickCta=!1,this.udrfService.udrfUiData.itemHasComments=!1,this.udrfService.udrfUiData.itemHasHierarchyView=!1,this.udrfService.udrfUiData.itemHasBookmark=!1,this.udrfService.udrfUiData.quickCTAInput={},this.udrfService.udrfUiData.commentsInput={},this.udrfService.udrfUiData.commentsContext={},this.udrfService.udrfUiData.onColumnClickItem="",this.udrfService.udrfUiData.countForOnlyThisReport=!1,this.udrfService.udrfUiData.isHeaderSort=!0,this.udrfService.udrfUiData.starRating=this.onStarClick.bind(this),this.udrfService.getAppUdrfConfig(this.applicationId,this.initReport.bind(this)),this.editLeadValues(),this.udrfService.getNotifyReleasesUDRF()}))}leadsCardClicked(){let e=window.location.origin+"/main/leads/"+this.udrfService.udrfUiData.itemCardSelecteditem.lead_id+"/"+this.encodeURIComponent(this.udrfService.udrfUiData.itemCardSelecteditem.lead_name);window.open(e)}encodeURIComponent(e){return encodeURIComponent(e).replace(/[!'()*]/g,(function(e){return"%"+e.charCodeAt(0).toString(16)}))}initReport(){return Object(r.c)(this,void 0,void 0,(function*(){console.log("InitReport"),this._onAppApiCalled.next(),this.WLItemDataCurrentIndex=0,this.isCardClicked=!1,this.udrfService.udrfBodyData=[];for(let e=0;e<this.dataTypeArray.length;e++)this.dataTypeArray[e].isActive=!1;this.udrfService.udrfUiData.resolveColumnConfig(),this.getLeadsList()}))}getLeadsList(){return Object(r.c)(this,void 0,void 0,(function*(){let e=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),t=s.where(this.dataTypeArray,{isActive:!0}),i=[],a=!1;if(t.length>0&&(i=s.where(t,{cardType:"status"}),console.log("summary",i),i.length>0))if(e.length>0){for(let i of e)(i.filterName==t[0].cardFilter||e.filterName==t[0].cardFilter)&&(i.multiOptionSelectSearchValues=[t[0].dataTypeCode],a=!0);if(0==a){let i=JSON.parse(JSON.stringify(s.where(this.udrfService.udrfData.filterTypeArray,{filterName:t[0].cardFilter})));e.push(i[0]);for(let n of e)(n.filterName==t[0].cardFilter||e.filterName==t[0].cardFilter)&&(n.multiOptionSelectSearchValues=[t[0].dataTypeCode],a=!0)}}else console.log(this.udrfService.udrfData.filterTypeArray),e=JSON.parse(JSON.stringify(s.where(this.udrfService.udrfData.filterTypeArray,{filterName:t[0].cardFilter}))),e[0].multiOptionSelectSearchValues=[t[0].dataTypeCode];console.log("Fileter Array",e);let n={startIndex:this.WLItemDataCurrentIndex,startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,mainFilterArray:e,txTableDetails:this.udrfService.udrfData.txTableDetails,mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,searchTableDetails:this.udrfService.udrfData.searchTableDetails};this.selectedCard=this.udrfService.udrfData.udrfSummaryCardCodes,this.leadService.getLeadsList(n).pipe(Object(d.a)(this._onDestroy)).pipe(Object(d.a)(this._onAppApiCalled)).subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){if("S"==e.messType&&e.messData&&e.messData.length>0){for(let i of e.messData)for(let[e,a]of Object.entries(i))if("object"==typeof a&&null!=a&&null!=a)try{"date"==a.type&&(i[e]=o(a.date).utc().format("DD - MMM - YYYY"))}catch(t){console.log(t)}this.udrfService.udrfBodyData=this.udrfService.udrfBodyData.concat(e.messData),this.isCardClicked||this.initWLCard()}else this.udrfService.udrfBodyData=this.udrfService.udrfBodyData.concat([]),this.isCardClicked||this.initWLCard(),this.udrfService.udrfData.noItemDataFound=!0;this.udrfService.udrfData.isItemDataLoading=!1})),e=>{this.showErrorMessage(e)})}))}initWLCard(){return Object(r.c)(this,void 0,void 0,(function*(){this.leadService.getLeadsCard({startIndex:this.WLItemDataCurrentIndex,startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,mainFilterArray:this.udrfService.udrfData.mainFilterArray,txTableDetails:this.udrfService.udrfData.txTableDetails,mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,searchTableDetails:this.udrfService.udrfData.searchTableDetails}).pipe(Object(d.a)(this._onDestroy)).pipe(Object(d.a)(this._onAppApiCalled)).subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){if(this.udrfService.udrfData.udrfSummaryCardCodes.length>0&&(console.log(this.udrfService.udrfData.udrfSummaryCardCodes),this.resolveVisibleDataTypeArray()),"S"==e.messType&&e.messData&&e.messData.length>0){for(let e=0;e<this.dataTypeArray.length;e++)this.dataTypeArray[e].isActive=!1,this.dataTypeArray[e].dataTypeValue="0";this.dataTypeArray=this.dataTypeArray.map((t,i)=>{const a=e.messData.find(e=>t.dataType==e.dataType);return Object.assign(Object.assign({},t),a)}),this.udrfService.udrfUiData.summaryCards=this.dataTypeArray,this.udrfService.udrfUiData.totalItemDataCount=e.total}else{for(let e=0;e<this.dataTypeArray.length;e++)this.dataTypeArray[e].isActive=!1,this.dataTypeArray[e].dataTypeValue="0";this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}})),e=>{this.showErrorMessage(e)})}))}showErrorMessage(e){this.utilityService.showErrorMessage("Error:Unable to fetch Data","KEBS")}openLeadsPageinNewTab(){let e=this.udrfService.udrfUiData.openinNewTabData.data?this.udrfService.udrfUiData.openinNewTabData.data:this.udrfService.udrfUiData.itemCardSelecteditem,t=`${window.location.origin}/main/leads/${e.lead_id}/${e.lead_name}/overview`;this.udrfService.udrfUiData.openinNewTabData={},window.open(t)}openEditScreen(){return Object(r.c)(this,void 0,void 0,(function*(){let e=this.udrfService.udrfUiData.openModalData?this.udrfService.udrfUiData.openModalData:this.udrfService.udrfUiData.udrfUiOpenModalDataItem;if(null!=e){const{CreateLeadComponent:t}=yield Promise.all([i.e(8),i.e(45),i.e(54),i.e(62),i.e(80),i.e(79),i.e(135),i.e(336)]).then(i.bind(null,"9oms"));this.dialog.open(t,{height:"100%",width:"80%",position:{right:"0px"},data:{checked:!1,leadDetail:e,mode:"Edit"},disableClose:!0}).afterClosed().subscribe(e=>{console.log(e),"update required"==e&&this.initReport()})}}))}openLead(){this.router.navigateByUrl("/main/leads/"+this.udrfService.udrfUiData.itemCardSelecteditem.lead_id+"/"+this.encodeURIComponent(this.udrfService.udrfUiData.itemCardSelecteditem.lead_name))}editLeadValues(){return Object(r.c)(this,void 0,void 0,(function*(){this.udrfService.udrfUiData.inlineEditDropDownMasterDatas={},yield this.leadService.getLeadSource().then(e=>{this.leadSourceList=e},e=>{console.log(e)}),yield this.leadService.getLeadScore().then(e=>{this.leadScoreList=e},e=>{console.log(e)}),yield this.leadService.getLeadsSalesStatus().then(e=>{this.leadStatus=e},e=>{console.log(e)}),yield this.leadService.leadsChannelMaster().subscribe(e=>{this.leadChannel=e},e=>{console.log(e)}),this.udrfService.udrfUiData.inlineEditDropDownMasterDatas={"sales-source":this.leadSourceList,"status-lead-score":this.leadScoreList,"status-lead":this.leadStatus,"leads-channel":this.leadChannel}}))}onLeadListScrollDown(){this.udrfService.udrfData.noItemDataFound||(this.WLItemDataCurrentIndex+=this.udrfService.udrfData.defaultRecordsPerFetch,console.log("Next",this.WLItemDataCurrentIndex),this.udrfService.udrfData.isItemDataLoading=!0,this.getLeadsList())}onStarClick(){let e=this.udrfService.udrfUiData.starRatingData.data;0==e.is_sales_qualified_lead?this.leadService.markAsSalesQualified(e.lead_id).subscribe(t=>Object(r.c)(this,void 0,void 0,(function*(){let t=s.indexOf(this.udrfService.udrfBodyData,e);this.udrfService.udrfBodyData[t].is_sales_qualified_lead=1,this.udrfService.udrfBodyData[t].to_display_qualified_unqualified="Mark as unqualified",this.udrfService.udrfBodyData[t].is_sales_qualified_lead_result=!0,this.snackBar.open("Marked as sales qualified lead !","close",{duration:2e3})})),e=>{console.error(e)}):1==e.is_sales_qualified_lead&&this.leadService.unmarkAsSalesQualified(e.lead_id).subscribe(t=>Object(r.c)(this,void 0,void 0,(function*(){let t=s.indexOf(this.udrfService.udrfBodyData,e);this.udrfService.udrfBodyData[t].is_sales_qualified_lead=0,this.udrfService.udrfBodyData[t].to_display_qualified_unqualified="Mark as sales Qualified",this.udrfService.udrfBodyData[t].is_sales_qualified_lead_result=!1,this.snackBar.open("Removed from sales qualified lead","close",{duration:2e3})})),e=>{console.error(e)})}callInlineEditApi(){if(console.log("InlineEdit response data",this.udrfService.udrfUiData.inlineEditData),"l1"==this.udrfService.udrfUiData.inlineEditData.hierarchyLevel)if("Lead Marketing Owner"==this.udrfService.udrfUiData.inlineEditData.inlineEditField)this.udrfService.udrfUiData.inlineEditData.dataSelected.marketing_owner_oid!=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.oid&&this.leadService.updateLeadMarketingOwner(this.udrfService.udrfUiData.inlineEditData.dataSelected.lead_id,this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.oid).subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){if("F"!=e){this.utilityService.showMessage("Marketing Owner Updated Successfully","Dismiss",3e3);let e=s.indexOf(this.udrfService.udrfBodyData,this.udrfService.udrfUiData.inlineEditData.dataSelected);this.udrfService.udrfBodyData[e].marketing_owner_oid=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.oid,this.udrfService.udrfBodyData[e].marketing_owner_name=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.name}else this.utilityService.showMessage("Cannot update Marketing Owner","Dismiss",3e3)})),e=>{this.utilityService.showMessage(e,"dismiss",3e3)});else if("Lead Sales Owner"==this.udrfService.udrfUiData.inlineEditData.inlineEditField)this.udrfService.udrfUiData.inlineEditData.dataSelected.sales_owner_oid!=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.oid&&this.leadService.updateLeadSalesOwner(this.udrfService.udrfUiData.inlineEditData.dataSelected.lead_id,this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.oid).subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){this.utilityService.showMessage("Sales Owner Updated Successfully","Dismiss",3e3);let e=s.indexOf(this.udrfService.udrfBodyData,this.udrfService.udrfUiData.inlineEditData.dataSelected);this.udrfService.udrfBodyData[e].sales_owner_oid=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.oid,this.udrfService.udrfBodyData[e].sales_owner_name=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.name})),e=>{this.utilityService.showMessage(e,"dismiss",3e3)});else if("Lead Owner"==this.udrfService.udrfUiData.inlineEditData.inlineEditField)this.udrfService.udrfUiData.inlineEditData.dataSelected.lead_owner_oid!=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.oid&&this.leadService.updateLeadOwner(this.udrfService.udrfUiData.inlineEditData.dataSelected.lead_id,this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.oid).subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){this.utilityService.showMessage("Lead Owner Updated Successfully","Dismiss",3e3);let e=s.indexOf(this.udrfService.udrfBodyData,this.udrfService.udrfUiData.inlineEditData.dataSelected);this.udrfService.udrfBodyData[e].lead_owner_oid=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.oid,this.udrfService.udrfBodyData[e].lead_owner_name=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.name})),e=>{this.utilityService.showMessage(e,"dismiss",3e3)});else if("status-lead-score"==this.udrfService.udrfUiData.inlineEditData.inlineEditField)this.udrfService.udrfUiData.inlineEditData.dataSelected.lead_score_id!=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.id&&this.leadService.updateLeadScore(this.udrfService.udrfUiData.inlineEditData.dataSelected.lead_id,this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.id).subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){this.utilityService.showMessage("Lead Score Updated Successfully","Dismiss",3e3);let e=s.indexOf(this.udrfService.udrfBodyData,this.udrfService.udrfUiData.inlineEditData.dataSelected);this.udrfService.udrfBodyData[e].lead_score_id=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.id,this.udrfService.udrfBodyData[e].lead_score_name=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.name})),e=>{this.utilityService.showMessage(e,"dismiss",3e3)});else if("sales-source"==this.udrfService.udrfUiData.inlineEditData.inlineEditField)this.udrfService.udrfUiData.inlineEditData.dataSelected.lead_source_id!=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.id&&this.leadService.updateLeadScore(this.udrfService.udrfUiData.inlineEditData.dataSelected.lead_source_id,this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.id).subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){this.utilityService.showMessage("Lead Source Updated Successfully","Dismiss",3e3);let e=s.indexOf(this.udrfService.udrfBodyData,this.udrfService.udrfUiData.inlineEditData.dataSelected);this.udrfService.udrfBodyData[e].lead_source_id=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.id,this.udrfService.udrfBodyData[e].source_name=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.name})),e=>{this.utilityService.showMessage(e,"dismiss",3e3)});else if("End Date"==this.udrfService.udrfUiData.inlineEditData.inlineEditField){let e=this.utilityService.convertToLocalTime(this.udrfService.udrfUiData.inlineEditData.inlineEditResponse["End Date"]);this.udrfService.udrfUiData.inlineEditData.dataSelected.leads_closure_date!=e&&this.leadService.updateLeadsClosureDate(this.udrfService.udrfUiData.inlineEditData.dataSelected.lead_id,e).subscribe(t=>Object(r.c)(this,void 0,void 0,(function*(){this.utilityService.showMessage("Lead closure date Updated Successfully","Dismiss",3e3);let t=s.indexOf(this.udrfService.udrfBodyData,this.udrfService.udrfUiData.inlineEditData.dataSelected);this.udrfService.udrfBodyData[t].end_date=o(e).format("DD - MMM - YYYY"),this.udrfService.udrfBodyData[t].leads_closure_date=e})),e=>{this.utilityService.showMessage(e,"dismiss",3e3)})}else"Lead Name"==this.udrfService.udrfUiData.inlineEditData.inlineEditField?this.udrfService.udrfUiData.inlineEditData.dataSelected.lead_name!=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse["Lead Name"]&&this.leadService.updateLeadName(this.udrfService.udrfUiData.inlineEditData.dataSelected.lead_id,this.udrfService.udrfUiData.inlineEditData.inlineEditResponse["Lead Name"]).subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){this.utilityService.showMessage("Lead Name Updated Successfully","Dismiss",3e3);let e=s.indexOf(this.udrfService.udrfBodyData,this.udrfService.udrfUiData.inlineEditData.dataSelected);this.udrfService.udrfBodyData[e].lead_name=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse["Lead Name"]})),e=>{this.utilityService.showMessage(e,"dismiss",3e3)}):"status-lead"==this.udrfService.udrfUiData.inlineEditData.inlineEditField?this.udrfService.udrfUiData.inlineEditData.dataSelected.status_id!=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.id&&this.leadService.updateLeadStatus(this.udrfService.udrfUiData.inlineEditData.dataSelected.lead_id,this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.id,this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.name,this.udrfService.udrfUiData.inlineEditData.dataSelected.status_id).subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){if("S"==e.messType){let t=s.indexOf(this.udrfService.udrfBodyData,this.udrfService.udrfUiData.inlineEditData.dataSelected);this.udrfService.udrfBodyData[t].sales_status_name=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.name,this.udrfService.udrfBodyData[t].status_id=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.id,2==this.udrfService.udrfBodyData[t].status_id?(yield this.moveToOpportunity(this.udrfService.udrfUiData.inlineEditData.dataSelected.lead_id),this.utilityService.showMessage(e.description,"Dismiss",3e3)):this.utilityService.showMessage(e.description,"Dismiss",3e3)}else this.utilityService.showMessage(e.description,"Dismiss",3e3);this.initWLCard()})),e=>{this.utilityService.showMessage(e,"dismiss",3e3)}):"leads-channel"==this.udrfService.udrfUiData.inlineEditData.inlineEditField&&this.udrfService.udrfUiData.inlineEditData.dataSelected.channel_id!=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.id&&this.leadService.updateLeadChannel(this.udrfService.udrfUiData.inlineEditData.dataSelected.lead_id,this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.id).subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){this.utilityService.showMessage("Lead Channel Updated Successfully","Dismiss",3e3);let e=s.indexOf(this.udrfService.udrfBodyData,this.udrfService.udrfUiData.inlineEditData.dataSelected);this.udrfService.udrfBodyData[e].channel_id=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.id,this.udrfService.udrfBodyData[e].channel_name=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.name})),e=>{this.utilityService.showMessage(e,"dismiss",3e3)})}moveToOpportunity(e){return Object(r.c)(this,void 0,void 0,(function*(){console.log("Create Opportunity");const{CreateOpportunityComponent:t}=yield Promise.all([i.e(8),i.e(15),i.e(29),i.e(45),i.e(49),i.e(56),i.e(62),i.e(81),i.e(136),i.e(947)]).then(i.bind(null,"URk0"));this.dialog.open(t,{height:"100%",width:"80%",position:{right:"0px"},data:{leadid:e},disableClose:!0}).afterClosed().subscribe(e=>{console.log(e)})}))}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete(),this.udrfService.resetUdrfData()}}return e.\u0275fac=function(t){return new(t||e)(c["\u0275\u0275directiveInject"](u.a),c["\u0275\u0275directiveInject"](h.a),c["\u0275\u0275directiveInject"](f.a),c["\u0275\u0275directiveInject"](p.a),c["\u0275\u0275directiveInject"](m.a),c["\u0275\u0275directiveInject"](g.a),c["\u0275\u0275directiveInject"](v.b),c["\u0275\u0275directiveInject"](y.a),c["\u0275\u0275directiveInject"](n.g))},e.\u0275cmp=c["\u0275\u0275defineComponent"]({type:e,selectors:[["app-leads-new-landing-page"]],decls:3,vars:0,consts:[[1,"container-fluid","leads-report-styles","pl-0","pr-0"]],template:function(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"div",0),c["\u0275\u0275element"](1,"udrf-header"),c["\u0275\u0275element"](2,"udrf-body"),c["\u0275\u0275elementEnd"]())},directives:[S.a,C.a],styles:[".leads-report-styles[_ngcontent-%COMP%]   .status-circular[_ngcontent-%COMP%]{height:13px;width:13px;margin-top:4px;border-radius:50%;box-shadow:0 2px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.leads-report-styles[_ngcontent-%COMP%]   .is-submitted[_ngcontent-%COMP%]{background:#ff7200;color:#fff!important}.leads-report-styles[_ngcontent-%COMP%]   .is-draft[_ngcontent-%COMP%]{background:#c7c4c4;color:#fff!important}.leads-report-styles[_ngcontent-%COMP%]   .is-approved[_ngcontent-%COMP%]{background:#009432;color:#fff!important}.leads-report-styles[_ngcontent-%COMP%]   .is-wfh[_ngcontent-%COMP%]{background:#9980fa;color:#fff!important}.leads-report-styles[_ngcontent-%COMP%]   .is-rejected[_ngcontent-%COMP%]{background:#cf0001;color:#fff!important}.leads-report-styles[_ngcontent-%COMP%]   .is-cancelled[_ngcontent-%COMP%]{background:#9f2825;color:#fff!important}.leads-report-styles[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field{width:40vw;padding-top:25px}.leads-report-styles[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-infix{font-size:14px!important;padding:.5em 0!important;border-top:.54375em solid transparent!important}.leads-report-styles[_ngcontent-%COMP%]   .spinner-align[_ngcontent-%COMP%]{margin:auto;display:inline-block}.leads-report-styles[_ngcontent-%COMP%]   .infinite-scroll-fixed[_ngcontent-%COMP%]{height:425px;overflow-y:scroll}.leads-report-styles[_ngcontent-%COMP%]   .infinite-scroll-fixed-shortened[_ngcontent-%COMP%]{height:435px;overflow-y:scroll}.leads-report-styles[_ngcontent-%COMP%]   .ta-r[_ngcontent-%COMP%]{text-align:right!important}.leads-report-styles[_ngcontent-%COMP%]   .ta-l[_ngcontent-%COMP%]{text-align:left}.leads-report-styles[_ngcontent-%COMP%]   .is-normal-probability-text[_ngcontent-%COMP%]{color:grey!important}.leads-report-styles[_ngcontent-%COMP%]   .is-low-probability-text[_ngcontent-%COMP%]{color:#cf0001!important}.leads-report-styles[_ngcontent-%COMP%]   .is-moderate-probability-text[_ngcontent-%COMP%]{color:#ff7200!important}.leads-report-styles[_ngcontent-%COMP%]   .is-high-probability-text[_ngcontent-%COMP%]{color:#009432!important}.leads-report-styles[_ngcontent-%COMP%]   .is-normal-probability[_ngcontent-%COMP%]{border-left-color:grey!important}.leads-report-styles[_ngcontent-%COMP%]   .is-low-probability[_ngcontent-%COMP%]{border-left-color:#cf0001!important}.leads-report-styles[_ngcontent-%COMP%]   .is-moderate-probability[_ngcontent-%COMP%]{border-left-color:#ff7200!important}.leads-report-styles[_ngcontent-%COMP%]   .is-high-probability[_ngcontent-%COMP%]{border-left-color:#009432!important}.leads-report-styles[_ngcontent-%COMP%]   .value13Bold[_ngcontent-%COMP%]{font-size:13px!important}.leads-report-styles[_ngcontent-%COMP%]   .value13Bold[_ngcontent-%COMP%], .leads-report-styles[_ngcontent-%COMP%]   .value14Bold[_ngcontent-%COMP%]{color:#000;font-weight:500!important;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.leads-report-styles[_ngcontent-%COMP%]   .value14Bold[_ngcontent-%COMP%]{font-size:14px!important}.leads-report-styles[_ngcontent-%COMP%]   .listcard[_ngcontent-%COMP%]{transition:all .25s linear;height:auto}.leads-report-styles[_ngcontent-%COMP%]   .smallSubtleText[_ngcontent-%COMP%]{color:#66615b;font-size:11px}.leads-report-styles[_ngcontent-%COMP%]   .data-type-card[_ngcontent-%COMP%]{min-height:85px;transition:all .3s linear}.leads-report-styles[_ngcontent-%COMP%]   .data-type-card-is-active[_ngcontent-%COMP%]{border-color:#cf0001;background-color:#fff2f2}.leads-report-styles[_ngcontent-%COMP%]   .headingBold[_ngcontent-%COMP%]{color:#1a1a1a;font-weight:600;font-size:22px}.leads-report-styles[_ngcontent-%COMP%]   .valueGrey14[_ngcontent-%COMP%]{font-size:14px;font-weight:300}.leads-report-styles[_ngcontent-%COMP%]   .valueGrey12Normal[_ngcontent-%COMP%], .leads-report-styles[_ngcontent-%COMP%]   .valueGrey14[_ngcontent-%COMP%]{color:#4a4a4a;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.leads-report-styles[_ngcontent-%COMP%]   .valueGrey12Normal[_ngcontent-%COMP%]{font-size:12px;font-weight:400}.leads-report-styles[_ngcontent-%COMP%]   .cp[_ngcontent-%COMP%]{cursor:pointer!important}.leads-report-styles[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]{margin-top:5px!important;line-height:8px;width:26px;height:26px;margin-right:10px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12);animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}.leads-report-styles[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%], .leads-report-styles[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.leads-report-styles[_ngcontent-%COMP%]   .ta-c[_ngcontent-%COMP%]{text-align:center;padding-left:0!important;padding-right:0!important}.leads-report-styles[_ngcontent-%COMP%]   .divAlignItemCenter[_ngcontent-%COMP%]{display:flex;align-items:center}.leads-report-styles[_ngcontent-%COMP%]   .menu-icons[_ngcontent-%COMP%]{font-size:4px}.leads-report-styles[_ngcontent-%COMP%]   .example-container[_ngcontent-%COMP%]{width:400px;max-width:100%;margin:0 25px 25px 0;display:inline-block;vertical-align:top}.leads-report-styles[_ngcontent-%COMP%]   .example-list[_ngcontent-%COMP%]{border:1px solid #ccc;min-height:60px;background:#fff;border-radius:4px;overflow:hidden;display:block}.leads-report-styles[_ngcontent-%COMP%]     .example-box{border-bottom:1px solid #ccc;color:rgba(0,0,0,.87);display:flex;flex-direction:row;align-items:left;justify-content:space-between;box-sizing:border-box;cursor:move;background:#fff;font-size:4px}.leads-report-styles[_ngcontent-%COMP%]     .menu-list{cursor:move;font-size:8px}.leads-report-styles[_ngcontent-%COMP%]   .cdk-drag-preview[_ngcontent-%COMP%]{box-sizing:border-box;border-radius:4px;box-shadow:0 5px 5px -3px rgba(0,0,0,.2),0 8px 10px 1px rgba(0,0,0,.14),0 3px 14px 2px rgba(0,0,0,.12)}.leads-report-styles[_ngcontent-%COMP%]   .cdk-drag-placeholder[_ngcontent-%COMP%]{opacity:0}.leads-report-styles[_ngcontent-%COMP%]   .cdk-drag-animating[_ngcontent-%COMP%]{transition:transform .25s cubic-bezier(0,0,.2,1)}.leads-report-styles[_ngcontent-%COMP%]   .example-box[_ngcontent-%COMP%]:last-child{border:none}.leads-report-styles[_ngcontent-%COMP%]   .example-list.cdk-drop-list-dragging[_ngcontent-%COMP%]   .example-box[_ngcontent-%COMP%]:not(.cdk-drag-placeholder){transition:transform .25s cubic-bezier(0,0,.2,1)}"]}),e})()}];let D=(()=>{class e{}return e.\u0275mod=c["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=c["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[n.k.forChild(b)],n.k]}),e})();var O=i("vxfF"),x=i("bTqV"),_=i("STbY"),w=i("Qu3c"),E=i("NFeN"),I=i("iadO"),k=i("d3UM"),M=i("kmnG"),U=i("qFsG"),T=i("Xa2L"),P=i("MutI"),R=i("3Pt+"),N=i("KHjE"),L=i("FKr1"),F=i("1yaQ"),A=i("/1cH"),B=i("Xi0T");let j=(()=>{class e{}return e.\u0275mod=c["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=c["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[a.CommonModule,D,h.b,O.g,x.b,_.e,w.b,E.b,M.e,U.c,T.b,P.d,N.a,L.n,F.b,I.h,R.p,R.E,k.d,A.c,B.a]]}),e})()},w76M:function(e,t,i){"use strict";i.d(t,"a",(function(){return l})),i.d(t,"b",(function(){return c}));var a=i("jhN1"),n=i("fXoL"),r=i("oHs6"),s=i("PVOt"),o=i("6t9p");const d=["*"];let l=(()=>{let e=class extends s.b{constructor(e,t,i,a,n,r,s,o){super(e,t,i,a,s,o),this._watcherHelper=a,this._idh=n,this._createEventEmitters([{subscribe:"contentReady",emit:"onContentReady"},{subscribe:"disposing",emit:"onDisposing"},{subscribe:"hidden",emit:"onHidden"},{subscribe:"hiding",emit:"onHiding"},{subscribe:"initialized",emit:"onInitialized"},{subscribe:"optionChanged",emit:"onOptionChanged"},{subscribe:"resize",emit:"onResize"},{subscribe:"resizeEnd",emit:"onResizeEnd"},{subscribe:"resizeStart",emit:"onResizeStart"},{subscribe:"showing",emit:"onShowing"},{subscribe:"shown",emit:"onShown"},{subscribe:"titleRendered",emit:"onTitleRendered"},{emit:"accessKeyChange"},{emit:"animationChange"},{emit:"closeOnOutsideClickChange"},{emit:"containerChange"},{emit:"contentTemplateChange"},{emit:"deferRenderingChange"},{emit:"disabledChange"},{emit:"dragEnabledChange"},{emit:"elementAttrChange"},{emit:"focusStateEnabledChange"},{emit:"fullScreenChange"},{emit:"heightChange"},{emit:"hintChange"},{emit:"hoverStateEnabledChange"},{emit:"maxHeightChange"},{emit:"maxWidthChange"},{emit:"minHeightChange"},{emit:"minWidthChange"},{emit:"positionChange"},{emit:"resizeEnabledChange"},{emit:"rtlEnabledChange"},{emit:"shadingChange"},{emit:"shadingColorChange"},{emit:"showCloseButtonChange"},{emit:"showTitleChange"},{emit:"tabIndexChange"},{emit:"titleChange"},{emit:"titleTemplateChange"},{emit:"toolbarItemsChange"},{emit:"visibleChange"},{emit:"widthChange"}]),this._idh.setHost(this),r.setHost(this)}get accessKey(){return this._getOption("accessKey")}set accessKey(e){this._setOption("accessKey",e)}get animation(){return this._getOption("animation")}set animation(e){this._setOption("animation",e)}get closeOnOutsideClick(){return this._getOption("closeOnOutsideClick")}set closeOnOutsideClick(e){this._setOption("closeOnOutsideClick",e)}get container(){return this._getOption("container")}set container(e){this._setOption("container",e)}get contentTemplate(){return this._getOption("contentTemplate")}set contentTemplate(e){this._setOption("contentTemplate",e)}get deferRendering(){return this._getOption("deferRendering")}set deferRendering(e){this._setOption("deferRendering",e)}get disabled(){return this._getOption("disabled")}set disabled(e){this._setOption("disabled",e)}get dragEnabled(){return this._getOption("dragEnabled")}set dragEnabled(e){this._setOption("dragEnabled",e)}get elementAttr(){return this._getOption("elementAttr")}set elementAttr(e){this._setOption("elementAttr",e)}get focusStateEnabled(){return this._getOption("focusStateEnabled")}set focusStateEnabled(e){this._setOption("focusStateEnabled",e)}get fullScreen(){return this._getOption("fullScreen")}set fullScreen(e){this._setOption("fullScreen",e)}get height(){return this._getOption("height")}set height(e){this._setOption("height",e)}get hint(){return this._getOption("hint")}set hint(e){this._setOption("hint",e)}get hoverStateEnabled(){return this._getOption("hoverStateEnabled")}set hoverStateEnabled(e){this._setOption("hoverStateEnabled",e)}get maxHeight(){return this._getOption("maxHeight")}set maxHeight(e){this._setOption("maxHeight",e)}get maxWidth(){return this._getOption("maxWidth")}set maxWidth(e){this._setOption("maxWidth",e)}get minHeight(){return this._getOption("minHeight")}set minHeight(e){this._setOption("minHeight",e)}get minWidth(){return this._getOption("minWidth")}set minWidth(e){this._setOption("minWidth",e)}get position(){return this._getOption("position")}set position(e){this._setOption("position",e)}get resizeEnabled(){return this._getOption("resizeEnabled")}set resizeEnabled(e){this._setOption("resizeEnabled",e)}get rtlEnabled(){return this._getOption("rtlEnabled")}set rtlEnabled(e){this._setOption("rtlEnabled",e)}get shading(){return this._getOption("shading")}set shading(e){this._setOption("shading",e)}get shadingColor(){return this._getOption("shadingColor")}set shadingColor(e){this._setOption("shadingColor",e)}get showCloseButton(){return this._getOption("showCloseButton")}set showCloseButton(e){this._setOption("showCloseButton",e)}get showTitle(){return this._getOption("showTitle")}set showTitle(e){this._setOption("showTitle",e)}get tabIndex(){return this._getOption("tabIndex")}set tabIndex(e){this._setOption("tabIndex",e)}get title(){return this._getOption("title")}set title(e){this._setOption("title",e)}get titleTemplate(){return this._getOption("titleTemplate")}set titleTemplate(e){this._setOption("titleTemplate",e)}get toolbarItems(){return this._getOption("toolbarItems")}set toolbarItems(e){this._setOption("toolbarItems",e)}get visible(){return this._getOption("visible")}set visible(e){this._setOption("visible",e)}get width(){return this._getOption("width")}set width(e){this._setOption("width",e)}get toolbarItemsChildren(){return this._getOption("toolbarItems")}set toolbarItemsChildren(e){this.setChildren("toolbarItems",e)}_createInstance(e,t){return new r.a(e,t)}ngOnDestroy(){this._destroyWidget()}ngOnChanges(e){super.ngOnChanges(e),this.setupChanges("toolbarItems",e)}setupChanges(e,t){e in this._optionsToUpdate||this._idh.setup(e,t)}ngDoCheck(){this._idh.doCheck("toolbarItems"),this._watcherHelper.checkWatchers(),super.ngDoCheck(),super.clearChangedOptions()}_setOption(e,t){let i=this._idh.setupSingle(e,t),a=null!==this._idh.getChanges(e,t);(i||a)&&super._setOption(e,t)}};return e.\u0275fac=function(t){return new(t||e)(n["\u0275\u0275directiveInject"](n.ElementRef),n["\u0275\u0275directiveInject"](n.NgZone),n["\u0275\u0275directiveInject"](s.e),n["\u0275\u0275directiveInject"](s.j),n["\u0275\u0275directiveInject"](s.g),n["\u0275\u0275directiveInject"](s.i),n["\u0275\u0275directiveInject"](a.h),n["\u0275\u0275directiveInject"](n.PLATFORM_ID))},e.\u0275cmp=n["\u0275\u0275defineComponent"]({type:e,selectors:[["dx-popup"]],contentQueries:function(e,t,i){if(1&e&&n["\u0275\u0275contentQuery"](i,o.L,!1),2&e){let e;n["\u0275\u0275queryRefresh"](e=n["\u0275\u0275loadQuery"]())&&(t.toolbarItemsChildren=e)}},inputs:{accessKey:"accessKey",animation:"animation",closeOnOutsideClick:"closeOnOutsideClick",container:"container",contentTemplate:"contentTemplate",deferRendering:"deferRendering",disabled:"disabled",dragEnabled:"dragEnabled",elementAttr:"elementAttr",focusStateEnabled:"focusStateEnabled",fullScreen:"fullScreen",height:"height",hint:"hint",hoverStateEnabled:"hoverStateEnabled",maxHeight:"maxHeight",maxWidth:"maxWidth",minHeight:"minHeight",minWidth:"minWidth",position:"position",resizeEnabled:"resizeEnabled",rtlEnabled:"rtlEnabled",shading:"shading",shadingColor:"shadingColor",showCloseButton:"showCloseButton",showTitle:"showTitle",tabIndex:"tabIndex",title:"title",titleTemplate:"titleTemplate",toolbarItems:"toolbarItems",visible:"visible",width:"width"},outputs:{onContentReady:"onContentReady",onDisposing:"onDisposing",onHidden:"onHidden",onHiding:"onHiding",onInitialized:"onInitialized",onOptionChanged:"onOptionChanged",onResize:"onResize",onResizeEnd:"onResizeEnd",onResizeStart:"onResizeStart",onShowing:"onShowing",onShown:"onShown",onTitleRendered:"onTitleRendered",accessKeyChange:"accessKeyChange",animationChange:"animationChange",closeOnOutsideClickChange:"closeOnOutsideClickChange",containerChange:"containerChange",contentTemplateChange:"contentTemplateChange",deferRenderingChange:"deferRenderingChange",disabledChange:"disabledChange",dragEnabledChange:"dragEnabledChange",elementAttrChange:"elementAttrChange",focusStateEnabledChange:"focusStateEnabledChange",fullScreenChange:"fullScreenChange",heightChange:"heightChange",hintChange:"hintChange",hoverStateEnabledChange:"hoverStateEnabledChange",maxHeightChange:"maxHeightChange",maxWidthChange:"maxWidthChange",minHeightChange:"minHeightChange",minWidthChange:"minWidthChange",positionChange:"positionChange",resizeEnabledChange:"resizeEnabledChange",rtlEnabledChange:"rtlEnabledChange",shadingChange:"shadingChange",shadingColorChange:"shadingColorChange",showCloseButtonChange:"showCloseButtonChange",showTitleChange:"showTitleChange",tabIndexChange:"tabIndexChange",titleChange:"titleChange",titleTemplateChange:"titleTemplateChange",toolbarItemsChange:"toolbarItemsChange",visibleChange:"visibleChange",widthChange:"widthChange"},features:[n["\u0275\u0275ProvidersFeature"]([s.e,s.j,s.i,s.g]),n["\u0275\u0275InheritDefinitionFeature"],n["\u0275\u0275NgOnChangesFeature"]],ngContentSelectors:d,decls:1,vars:0,template:function(e,t){1&e&&(n["\u0275\u0275projectionDef"](),n["\u0275\u0275projection"](0))},encapsulation:2}),e})(),c=(()=>{let e=class{};return e.\u0275mod=n["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=n["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[o.bb,o.Gc,o.Vd,o.vd,o.hb,o.lb,o.sb,o.id,o.jd,o.M,s.c,s.f,a.b],o.bb,o.Gc,o.Vd,o.vd,o.hb,o.lb,o.sb,o.id,o.jd,o.M,s.f]}),e})()}}]);