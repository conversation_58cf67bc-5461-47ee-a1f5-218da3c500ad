import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { ReportsComponent } from './reports.component';
import { ReportsAuthGuard } from './reports-auth.guard';
const routes: Routes = [
  {
    path: '',
    component: ReportsComponent,
  },
  {
    path: 'timesheet-stats',
    loadChildren: () =>
      import('./features/timesheet-stats/timesheet-stats.module').then(
        (m) => m.TimesheetStatsModule
      ),
    data: { breadcrumb: 'Payroll Statistics' },
  },
  {
    path: 'sales-dashboard',
    loadChildren: () =>
      import('../sales-dashboard/sales-dashboard.module').then(
        (m) => m.SalesDashboardModule
      ),
    data: { breadcrumb: 'Sales Dashboard' },
  },
  {
    path: 'hr-dashboard',
    loadChildren: () =>
      import('../hr-dashboard/hr-dashboard.module').then(
        (m) => m.HrDashboardModule
      ),
    canActivate: [ReportsAuthGuard],
    data: { breadcrumb: 'HR Dashboard', id: 349 },
  },
  {
    path: 'timesheet-stats-new',
    loadChildren: () =>
      import('./features/timesheet-stats-new/timesheet-stats-new.module').then(
        (m) => m.TimesheetStatsNewModule
      ),
    data: { breadcrumb: 'Timesheet Statistics' },
  },
  {
    path: 'mis',
    loadChildren: () =>
      import('./features/mis-report/mis-report.module').then(
        (m) => m.MisReportModule
      ),
    data: { breadcrumb: 'MIS' },
  },
  {
    path: 'management',
    loadChildren: () =>
      import('./features/management-report/management-report.module').then(
        (m) => m.ManagementReportModule
      ),
    data: { breadcrumb: 'Management' },
  },
  {
    path: 'governance',
    loadChildren: () =>
      import('./features/governance-report/governance-report.module').then(
        (m) => m.GovernanceReportModule
      ),
    data: { breadcrumb: 'Governance' },
  },
  {
    path: 'ar',
    loadChildren: () =>
      import('./features/ar-report/ar-report.module').then(
        (m) => m.ArReportModule
      ),
    data: { breadcrumb: 'AR' },
  },
  {
    path: 'ar-ubr',
    loadChildren: () =>
      import('./features/ar-ubr/ar-ubr.module').then((m) => m.ArUbrModule),

    data: { breadcrumb: 'AR-UBR Days' },
  },
  {
    path: 'projectReport',
    loadChildren: () =>
      import('./features/project-report/project-report.module').then(
        (m) => m.ProjectReportModule
      ),
    data: { breadcrumb: 'Project' },
  },
  {
    path: 'projectGovernanceReport',
    loadChildren: () =>
      import(
        './features/project-governance-report/project-governance-report.module'
      ).then((m) => m.ProjectGovernanceReportModule),
    data: { breadcrumb: 'Project Gov.' },
  },
  {
    path: 'practiceGov',
    loadChildren: () =>
      import('./features/activity-governance/activity-governance.module').then(
        (m) => m.ActivityGovernanceModule
      ),
    data: { breadcrumb: 'Practice Governance (LG)' },
  },
  {
    path: 'practiceGovNew',
    loadChildren: () =>
      import(
        './features/activity-governance-new/activity-governance-new.module'
      ).then((m) => m.ActivityGovernanceNewModule),
    data: { breadcrumb: 'Practice Governance' },
  },
  {
    path: 'dfrReport',
    loadChildren: () =>
      import('./features/dfr-report/dfr-report.module').then(
        (m) => m.DfrReportModule
      ),
    data: { breadcrumb: 'DFR' },
  },
  {
    path: 'obv',
    loadChildren: () =>
      import('./features/obv-report/obv-report.module').then(
        (m) => m.ObvReportModule
      ),
    data: { breadcrumb: 'OBV' },
  },
  {
    path: 'timesheet-payroll',
    loadChildren: () =>
      import('./features/payroll-report/payroll-report.module').then(
        (m) => m.PayrollReportModule
      ),
    data: { breadcrumb: 'Payroll' },
  },
  {
    path: 'sales',
    loadChildren: () =>
      import('./features/sales-report/sales-report.module').then(
        (m) => m.SalesReportModule
      ),
    data: { breadcrumb: 'Sales Report' },
  },
  {
    path: 'leadsGovernanceReport',
    loadChildren: () =>
      import('./features/lead-governance/lead-governance.module').then(
        (m) => m.LeadGovernanceModule
      ),
    data: { breadcrumb: 'Leads Governance' },
  },
  {
    path: 'marketingGovernance',
    loadChildren: () =>
      import(
        './features/marketing-governance/marketing-governance.module'
      ).then((m) => m.MarketingGovernanceModule),
    data: { breadcrumb: 'Marketing Governance' },
  },
  {
    path: 'salesGovernanceReportNew',
    loadChildren: () =>
      import(
        './features/sales-governance-new/sales-governance-new.module'
      ).then((m) => m.SalesGovernanceNewModule),
    data: { breadcrumb: 'Opportunity Governance' },
  },
  {
    path: 'salesGovernanceReport',
    loadChildren: () =>
      import('./features/sales-governance/sales-governance.module').then(
        (m) => m.SalesGovernanceModule
      ),
    data: { breadcrumb: 'Sales Governance' },
  },
  {
    path: 'opportunityGovernanceReport',
    loadChildren: () =>
      import(
        './features/opportunity-governance/opportunity-governance.module'
      ).then((m) => m.OpportunityGovernanceModule),
    data: { breadcrumb: 'Opportunity Governance (Legacy)' },
  },
  {
    path: 'book_and_bill',
    loadChildren: () =>
      import(
        './features/book-and-bill-report/book-and-bill-report.module'
      ).then((m) => m.BookAndBillReportModule),
    data: { breadcrumb: 'Book and Bill' },
  },
  {
    path: 'bidManager',
    loadChildren: () =>
      import('./features/bid-manager/bid-manager.module').then(
        (m) => m.BidManagerModule
      ),
    data: { breadcrumb: 'Bid Manager' },
  },
  {
    path: 'practice-mis',
    loadChildren: () =>
      import('./features/practice-mis/practice-mis.module').then(
        (m) => m.PracticeMisModule
      ),
    data: { breadcrumb: 'Practice MIS' },
  },
  {
    path: 'wfh-report',
    loadChildren: () =>
      import('./features/wfh-report/wfh-report.module').then(
        (m) => m.WfhReportModule
      ),
    data: { breadcrumb: 'WFH Report' },
  },
  {
    path: 'cta-report-new',
    loadChildren: () =>
      import('./features/cta-report-new/cta-report-new.module').then(
        (m) => m.CtaReportNewModule
      ),
    data: { breadcrumb: 'CTA Report' },
  },
  {
    path: 'appraisal-report',
    loadChildren: () =>
      import('./features/appraisal-report/appraisal-report.module').then(
        (m) => m.AppraisalReportModule
      ),
    data: { breadcrumb: 'Appraisal Report' },
  },
  {
    path: 'expense-report',
    loadChildren: () =>
      import('./features/expense-report/expense-report.module').then(
        (m) => m.ExpenseReportModule
      ),
    data: { breadcrumb: 'Expense Report' },
  },
  {
    path: 'arubrwctrend-report',
    loadChildren: () =>
      import(
        './features/ar-ubr-wc-trend-report/ar-ubr-wc-trend-report.module'
      ).then((m) => m.ArUbrWcTrendReportModule),
    data: { breadcrumb: 'AR UBR WC Trend' },
  },
  {
    path: 'sga-report',
    loadChildren: () =>
      import('./features/sga-report/sga-report.module').then(
        (m) => m.SgaReportModule
      ),
    data: { breadcrumb: 'SG&A Report' },
  },
  {
    path: 'plMisDifference-report',
    loadChildren: () =>
      import(
        './features/pl-mis-difference-report/pl-mis-difference-report.module'
      ).then((m) => m.PlMisDifferenceReportModule),
    data: { breadcrumb: 'P&L MIS Difference' },
  },
  {
    path: 'ams-report',
    loadChildren: () =>
      import('./features/ams-report/ams-report.module').then(
        (m) => m.AmsReportModule
      ),
    data: { breadcrumb: 'AMS Report' },
  },
  {
    path: 'ams-report-new',
    loadChildren: () =>
      import('./features/ams-report-new/ams-report-new.module').then(
        (m) => m.AmsReportNewModule
      ),
    data: { breadcrumb: 'AMS Report (BETA)' },
  },
  {
    path: 'tm-profitability-report',
    loadChildren: () =>
      import(
        './features/tm-profitability-report/tm-profitability-report.module'
      ).then((m) => m.TmProfitabilityReportModule),
    data: { breadcrumb: 'T&M Profitability Report' },
  },
  // {
  //   path: 'okrReport',
  //   loadChildren: () => import('./features/okr-report/okr-report.module').then(m => m.OkrReportModule),
  //   data: { breadcrumb: 'OKR Report (BETA)' }
  // },
  {
    path: 'isa-data-upload-report',
    loadChildren: () =>
      import('./features/isa-upload-report/isa-upload-report.module').then(
        (m) => m.IsaUploadReportModule
      ),
    data: { breadcrumb: 'ISA Data Upload' },
  },
  {
    path: 'winLossReport',
    loadChildren: () =>
      import('./features/win-loss-report/win-loss-report.module').then(
        (m) => m.WinLossReportModule
      ),
    data: { breadcrumb: 'Win-Loss Report' },
  },
  {
    path: 'book_and_bill_beta',
    loadChildren: () =>
      import(
        './features/book-and-bill-report-beta/book-and-bill-report-beta.module'
      ).then((m) => m.BookAndBillReportBetaModule),
    data: { breadcrumb: 'Book and Bill (BETA)' },
  },
  {
    path: 'timesheet_testcase_report',
    loadChildren: () =>
      import(
        './features/timesheet-testcase-report/timesheet-testcase-report.module'
      ).then((m) => m.TimesheetTestcaseReportModule),
    data: { breadcrumb: 'TS Testcase Report' },
  },
  {
    path: 'lmsUserCertificationsReport',
    loadChildren: () =>
      import(
        './features/lms-user-cert-report/lms-user-cert-report.module'
      ).then((m) => m.LmsUserCertReportModule),
    data: { breadcrumb: 'Learning Report' },
  },
  {
    path: 'awards-report',
    loadChildren: () =>
      import('./features/awards-report/awards-report.module').then(
        (m) => m.AwardsReportModule
      ),
    data: { breadcrumb: 'Awards Report' },
  },
  {
    path: 'contrary_book_bill',
    loadChildren: () =>
      import('./features/contrary-book-bill/contrary-book-bill.module').then(
        (m) => m.ContraryBookBillModule
      ),
    data: { breadcrumb: 'Contrary Book and Bill' },
  },
  {
    path: 'pmo-dashboard',
    loadChildren: () =>
      import('./features/pmo-dashboard/pmo-dashboard.module').then(
        (m) => m.PmoDashboardModule
      ),
    data: { breadcrumb: 'PMO Dashboard' },
  },
  {
    path: 'pmo-dashboard-v1',
    loadChildren: () =>
      import(
        './features/pmo-dashboard-version1/pmo-dashboard-version1.module'
      ).then((m) => m.PmoDashboardVersion1Module),
    data: { breadcrumb: 'Project Portfolio Dashboard' },
  },
  {
    path: 'bugReport',
    loadChildren: () =>
      import('./features/bug-report/bug-report.module').then(
        (m) => m.BugReportModule
      ),
    data: { breadcrumb: 'Project Bug Report' },
  },
  {
    path: 'taskReport',
    loadChildren: () =>
      import('./features/task-report/task-report.module').then(
        (m) => m.TaskReportModule
      ),
    data: { breadcrumb: 'Project Task Report' },
  },
  {
    path: 'testCaseReport',
    loadChildren: () =>
      import('./features/test-case-report/test-case-report.module').then(
        (m) => m.TestCaseReportModule
      ),
    data: { breadcrumb: 'Project Test Case Report' },
  },
  {
    path: 'pbiReport',
    loadChildren: () =>
      import('./features/pbi-report/pbi-report.module').then(
        (m) => m.PbiReportModule
      ),
    data: { breadcrumb: 'Project PBI Report' },
  },
  {
    path: 'pgReport',
    loadChildren: () =>
      import('./features/project-gantt/project-gantt.module').then(
        (m) => m.ProjectGanttModule
      ),
    data: { breadcrumb: 'Project Gantt Report' },
  },
  {
    path: 'onboarding-report',
    loadChildren: () =>
      import('./features/onboarding-report/onboarding-report.module').then(
        (m) => m.OnboardingReportModule
      ),
    data: { breadcrumb: 'Onboarding Report' },
  },
  {
    path: 'income_statement',
    loadChildren: () =>
      import('./features/tally-gl-report/tally-gl-report.module').then(
        (m) => m.TallyGlReportModule
      ),
    data: { breadcrumb: 'Income Statement' },
  },
  {
    path: 'management_budget',
    loadChildren: () =>
      import(
        './features/management-budget-report/management-budget-report.module'
      ).then((m) => m.ManagementBudgetReportModule),
    data: { breadcrumb: 'Management Budget Report' },
  },
  {
    path: 'productBugReport',
    loadChildren: () =>
      import('./features/product-bug-report/product-bug-report.module').then(
        (m) => m.ProductBugReportModule
      ),
    data: { breadcrumb: 'Product Bug Report' },
  },
  {
    path: 'productTaskReport',
    loadChildren: () =>
      import('./features/product-task-report/product-task-report.module').then(
        (m) => m.ProductTaskReportModule
      ),
    data: { breadcrumb: 'Product Task Report' },
  },
  {
    path: 'mis_functions',
    loadChildren: () =>
      import('./features/mis-functions/mis-functions.module').then(
        (m) => m.MisFunctionsModule
      ),
    data: { breadcrumb: 'MIS Functions' },
  },
  {
    path: 'lcdp-effort-report',
    loadChildren: () =>
      import('./features/lcdp-effort-report/lcdp-effort-report.module').then(
        (m) => m.LcdpEffortReportModule
      ),
    data: { breadcrumb: 'Effort Report' },
  },
  {
    path: 'productGanttReport',
    loadChildren: () =>
      import('./features/product-gantt/product-gantt.module').then(
        (m) => m.ProductGanttModule
      ),
    data: { breadcrumb: 'Product Gantt Report' },
  },
  {
    path: 'goverance_projects',
    loadChildren: () =>
      import('./features/goverance-project/goverance-project.module').then(
        (m) => m.GoveranceProjectModule
      ),
    data: { breadcrumb: 'Governance Project' },
  },
  {
    path: 'pl-country-level-mis-report',
    loadChildren: () =>
      import(
        './features/pl-country-level-mis-report/pl-country-level-mis-report.module'
      ).then((m) => m.PlCountryLevelMisReportModule),
    data: { breadcrumb: 'Country Level MIS' },
  },
  {
    path: 'org-head-count-report',
    loadChildren: () =>
      import(
        './features/org-head-count-report/org-head-count-report.module'
      ).then((m) => m.OrgHeadCountReportModule),
    data: { breadcrumb: 'Org Head Count' },
  },
  {
    path: 'sales-purchase-report',
    loadChildren: () =>
      import(
        './features/sales-purchase-report/sales-purchase-report.module'
      ).then((m) => m.SalesPurchaseReportModule),
    data: { breadcrumb: 'Sales & Purchase' },
  },
  {
    path: 'salary-statement',
    loadChildren: () =>
      import(
        './features/salary-statement-report/salary-statement-report.module'
      ).then((m) => m.SalaryStatementReportModule),
    data: { breadcrumb: 'Salary Statement' },
  },
  {
    path: 'rfidReport',
    loadChildren: () =>
      import('./features/rfid-report/rfid-report.module').then(
        (m) => m.RfidReportModule
      ),
    data: { breadcrumb: 'RFID Data Report' },
  },
  {
    path: 'rmg-governance',
    loadChildren: () =>
      import('./features/rmg-governance/rmg-governance.module').then(
        (m) => m.RmgGovernanceModule
      ),
    data: { breadcrumb: 'RMG Governance' },
  },
  {
    path: 'productUserStoryReport',
    loadChildren: () =>
      import(
        './features/product-user-story-report/product-user-story-report.module'
      ).then((m) => m.ProductUserStoryReportModule),
    data: { breadcrumb: 'PBI Report' },
  },
  {
    path: 'productVelocityReport',
    loadChildren: () =>
      import('./features/product-velocity/product-velocity.module').then(
        (m) => m.ProductVelocityModule
      ),
    data: { breadcrumb: 'Product Velocity' },
  },
  {
    path: 'productBurndownReport',
    loadChildren: () =>
      import(
        './features/product-burndown-landing-page/product-burndown-landing-page.module'
      ).then((m) => m.ProductBurndownLandingPageModule),
    data: { breadcrumb: 'Product Burndown' },
  },
  {
    path: 'contractTracker',
    loadChildren: () =>
      import('./features/acv-tracker/acv-tracker.module').then(
        (m) => m.AcvTrackerModule
      ),
    data: { breadcrumb: 'Contract Tracker' },
  },
  {
    path: 'timesheetNotification',
    loadChildren: () =>
      import(
        './features/timesheet-notification-report/timesheet-notification-report.module'
      ).then((m) => m.TimesheetNotificationReportModule),
    data: { breadcrumb: 'Timesheet Notification' },
  },
  {
    path: 'rfidfiloReport',
    loadChildren: () =>
      import('./features/rfid-fifo-report/rfid-fifo-report.module').then(
        (m) => m.RfidFifoReportModule
      ),
    data: { breadcrumb: 'RFID FILO Report' },
  },
  {
    path: 'pmsAnnualCycle',
    loadChildren: () =>
      import(
        './features/pms-annual-cycle-report/pms-annual-cycle-report.module'
      ).then((m) => m.PmsAnnualCycleReportModule),
    data: { breadcrumb: 'PMS Annual Cycle Report' },
  },
  {
    path: 'ec-report',
    loadChildren: () =>
      import('./features/e360-report/e360-report.module').then(
        (m) => m.E360ReportModule
      ),
    canActivate: [ReportsAuthGuard],
    data: { breadcrumb: 'EC Report', id: 406 },
  },
  {
    path: 'projectdashboard',
    loadChildren: () =>
      import('./features/project-dashboard/project-dashboard.module').then(
        (m) => m.ProjectDashboardModule
      ),
    data: { breadcrumb: 'Project Dashboard' },
  },
  {
    path: 'timesheetprojectreport',
    loadChildren: () =>
      import(
        './features/timesheet-project-report/timesheet-project-report.module'
      ).then((m) => m.TimesheetProjectReportModule),
    data: { breadcrumb: 'Product Timesheet Report' },
  },
  {
    path: 'productTestCaseReport',
    loadChildren: () =>
      import('./features/product-test-case/product-test-case.module').then(
        (m) => m.ProductTestCaseModule
      ),
    data: { breadcrumb: 'Product Test Case Report' },
  },
  {
    path: 'employee-Leave-report',
    loadChildren: () =>
      import(
        './features/employee-leave-report/employee-leave-report.module'
      ).then((m) => m.EmployeeLeaveReportModule),
    data: { breadcrumb: 'Employee Leave Report' },
  },
  {
    path: 'tds-report',
    loadChildren: () =>
      import('./features/tds-report/tds-report.module').then(
        (m) => m.TdsReportModule
      ),
    data: { breadcrumb: 'Tds Report' },
  },
  {
    path: 'integration-exception-report',
    loadChildren: () =>
      import(
        './features/integration-exception-report/integration-exception-report.module'
      ).then((m) => m.IntegrationExceptionReportModule),
    data: { breadcrumb: 'Integration Exception Report' },
  },
  {
    path: 'integration-check-report',
    loadChildren: () =>
      import(
        './features/integration-check-report/integration-check-report.module'
      ).then((m) => m.IntegrationCheckReportModule),
    data: { breadcrumb: 'Integration Check Report' },
  },
  {
    path: 'people-allocation-dashboard',
    loadChildren: () =>
      import('./features/rm-dashboard/rm-dashboard.module').then(
        (m) => m.RmDashboardModule
      ),
    data: { breadcrumb: 'People Allocation Dashboard' },
  },
  {
    path: 'azure-logs-report',
    loadChildren: () =>
      import(
        './features/azure-integration-logs-report/azure-integration-logs-report.module'
      ).then((m) => m.AzureIntegrationLogsReportModule),
    data: { breadcrumb: 'Azure Integration logs Report' },
  },
  {
    path: 'notification-logs-report',
    loadChildren: () =>
      import(
        './features/notification-logs-report/notification-logs-report.module'
      ).then((m) => m.NotificationLogsReportModule),
    data: { breadcrumb: 'Notification Logs Report' },
  },
  {
    path: 'error-logging-report',
    loadChildren: () =>
      import(
        './features/error-logging-report/error-logging-report.module'
      ).then((m) => m.ErrorLoggingReportModule),
    data: { breadcrumb: 'Integration Check Report' },
  },
  {
    path: 'internal-stakeholders-report',
    loadChildren: () =>
      import(
        './features/internal-stakeholders-report/internal-stakeholders-report.module'
      ).then((m) => m.InternalStakeholdersReportModule),
    data: { breadcrumb: 'Project ISA Report' },
  },
  {
    path: 'ubr-report',
    loadChildren: () =>
      import('./features/ubr-report/ubr-report.module').then(
        (m) => m.UbrReportModule
      ),
    data: { breadcrumb: 'Ubr Report' },
  },
  {
    path: 'timesheet-dashboard',
    loadChildren: () =>
      import('./features/timesheet-dashboard/timesheet-dashboard.module').then(
        (m) => m.TimesheetDashboardModule
      ),
    data: { breadcrumb: 'Timesheet Dashboard' },
  },
  {
    path: 'timesheet-v2-report',
    loadChildren: () =>
      import('./features/timesheet-v2-report/timesheet-v2-report.module').then(
        (m) => m.TimesheetV2ReportModule
      ),
    data: { breadcrumb: 'Timesheet V2 Report' },
  },
  {
    path: 'employee-Leave-report-Sub-Division',
    loadChildren: () =>
      import(
        './features/employee-leave-report-subdivision/employee-leave-report-subdivision.module'
      ).then((m) => m.EmployeeLeaveReportSubdivisionModule),
    data: { breadcrumb: 'Employee Leave Report Sub Division' },
  },
  {
    path: 'people-allocation-gantt',
    loadChildren: () =>
      import('./features/rmg-gantt/rmg-gantt.module').then(
        (m) => m.RmgGanttModule
      ),
    data: { breadcrumb: 'People Allocation Gantt' },
  },
  {
    path: 'projectTrackerReport',
    loadChildren: () =>
      import('./features/overall-project-report/overall-project-report.module').then(
        (m) => m.OverallProjectReportModule
      ),
    data: { breadcrumb: "Project Tracker" }
  },
  {
    path: 'timesheet-v2-stats-report',
    loadChildren: () =>
      import(
        './features/timesheet-v2-stats-report/timesheet-v2-stats-report.module'
      ).then((m) => m.TimesheetV2StatsReportModule),
    data: { breadcrumb: 'Timesheet Stats Report' },
  },
  {
    path: 'ts-query-report',
    loadChildren: () =>
      import(
        './features/timesheet-query-report/timesheet-query-report.module'
      ).then((m) => m.TimesheetQueryReportModule),
    data: { breadcrumb: 'Timesheet Query Report' },
  },
  {
    path: 'findash',
    loadChildren: () =>
      import(
        './features/finance-dashboard/finance-dashboard.module'
      ).then((m) => m.FinanceDashboardModule),
    data: { breadcrumb: 'Finance Dashboard' },
  },
  {
    path: 'ar-history-report',
    loadChildren: () =>
      import('./features/ar-history-report/ar-history-report.module').then(
        (m) => m.ArHistoryReportModule
      ),
    data: { breadcrumb: 'AR History Report' },
  },
  {
    path: 'productDashboard',
    loadChildren: () =>
      import(
        './features/product-dashboard/product-dashboard.module'
      ).then((m) => m.ProductDashboardModule),
    data: { breadcrumb: 'Product Dashboard' },
  },
  {
    path: 'ap-transaction-report',
    loadChildren: () =>
      import(
        './features/ap-transaction-report/ap-transaction-report.module'
      ).then((m) => m.APTransactionReportModule),
    data: { breadcrumb: 'AP Transaction Report' },
  },
  {
    path: 'p-and-l-report',
    loadChildren: () =>
      import('./features/p-and-l-report/p-and-l-report.module').then(
        (m) => m.PAndLReportModule
      ),
    data: { breadcrumb: 'Profit and Loss Report (Schedule III)' },
  },
  {
    path: 'balance-sheet-report',
    loadChildren: () =>
      import('./features/balance-sheet-report/balance-sheet-report.module').then(
        (m) => m.BalanceSheetReportModule
      ),
    data: { breadcrumb: 'Balance Sheet Report (Schedule III)' },
  },
  {
    path: 'cashflow-report',
    loadChildren: () =>
      import('./features/cashflow-report/cashflow-report.module').then(
        (m) => m.CashflowReportModule
      ),
    data: { breadcrumb: 'Cashflow Report (Schedule III)' },
  },
  {
    path: 'leave-error-report',
    loadChildren: () =>
      import('./features/leave-error-report/leave-error-report.module').then(
        (m) => m.LeaveErrorReportModule
      ),
    data: { breadcrumb: "Leave Error Report" }
  },
  {
    path: 'finance-report',
    loadChildren: () =>
      import('./features/finance-report/finance-report.module').then(
        (m) => m.FinanceReportModule
      ),
    data: { breadcrumb: "Finance Report" }
  },
  {    
    path: 'defect-report',
    loadChildren: () =>
      import('./features/defect-report/defect-report.module').then(
        (m) => m.DefectReportModule
      ),
      data: { breadcrumb: "Defect Report"}
  },
  {
    path: 'expenseItemReport',
    loadChildren: () =>
      import(
        './features/expense-item-report/expense-item-report.module'
      ).then((m) => m.ExpenseItemReportModule),
    data: { breadcrumb: 'Expense Item Report' },
  },
  {
    path: 'timesheet-daily-log-report-table-view',
    loadChildren: () =>
      import(
        './features/timesheet-v2-daily-log-report-table-view/timesheet-v2-daily-log-report-table-view.module'
      ).then((m) => m.TimesheetV2DailyLogReportTableViewModule),
    data: { breadcrumb: 'Timesheet Daily Log Report - Table View' },
  },
  {
    path: 'attrition-dashboard',
    loadChildren: () =>
      import('../exit-dashboard/exit-dashboard.module').then(
        (m) => m.ExitDashboardModule
      ),
    canActivate: [ReportsAuthGuard],
    data: { breadcrumb: 'Attrition Dashboard', id: 962 },
  },
  {
    path: 'compliance-dashboard',
    loadChildren: () =>
      import('./features/compliance-dashboard/compliance-dashboard.module').then(
        (m) => m.ComplianceDashboardModule
      ),
    data: { breadcrumb: 'Compliance Dashboard'},
  },
  {
    path: 'rr-projections',
    loadChildren: () =>
      import(
        './features/rr-projections-report/rr-projections-report.module'
      ).then((m) => m.RrProjectionsReportModule),
    data: { breadcrumb: 'RR Projections' },
  },
  {
    path: 'practice-mis-report',
    loadChildren: () =>
      import(
        './features/practice-mis-report/practice-mis-report.module'
      ).then((m) => m.PracticeMisReportModule),
    data: { breadcrumb: 'Sub Division MIS' },
  },
  {
    path: 'timesheet-export-report',
    loadChildren: () =>
      import(
        './features/timesheet-export-report/timesheet-export-report.module'
      ).then((m) => m.TimesheetExportReportModule),
    data: { breadcrumb: 'Timesheet Export Report' },
  },
  {
    path: 'project-time-tracker',
        loadChildren: () =>
          import(
            './features/project-time-tracker/project-time-tracker.module'
          ).then((m) => m.ProjectTimeTrackerModule),
        data: { breadcrumb: 'Project Time Tracker' },
  },
  {
    path: 'project-header-details',
        loadChildren: () =>
          import(
            './features/pm-header-details-report/pm-header-details-report.module'
          ).then((m) => m.PmHeaderDetailsReportModule),
        data: { breadcrumb: 'Project Header Details Report' },
  },
  {
    path: 'project-header',
        loadChildren: () =>
          import(
            './features/pm-header-details-download-report/pm-header-details-download-report.module'
          ).then((m) => m.PmHeaderDetailDownloadModule),
          canActivate: [ReportsAuthGuard],
        data: { breadcrumb: 'Project Header', id : 1030},
  },
  {
    path: 'project-employee-details',
        loadChildren: () =>
          import(
            './features/pm-employee-details-report/pm-employee-details-report.module'
          ).then((m) => m.PmEmployeeDetailsReportModule),
        data: { breadcrumb: 'Project Employee Details Report' },
  },
  {
    path: 'employee-details',
        loadChildren: () =>
          import(
            './features/pm-employee-summary-details-report/pm-employee-summary-details-report.module'
          ).then((m) => m.PmEmployeeSummaryDetailsReportModule),
        data: { breadcrumb: 'Employee Summary Details Report' },
  },
  {
    path: 'activity-dashboard',
    loadChildren: () =>
      import('./features/activity-dashboard/activity-dashboard.module').then(
        (m) => m.ActivityDashboardModule
      ),
    data: { breadcrumb: 'Activity Dashboard' },
  },
  {
    path: 'timesheet-urs-report',
    loadChildren: () =>
      import(
        './features/timesheet-urs-report/timesheet-urs-report.module'
      ).then((m) => m.TimesheetUrsReportModule),
    data: { breadcrumb: 'Timesheet URS Report' },
  },
  {
        path: 'people-allocation-status-report',
        loadChildren: () =>
          import(
            './features/rmg-soft-booking-report/rmg-soft-booking-report.module'
          ).then((m) => m.RmgSoftBookingReportModule),
        data: { breadcrumb: 'People Allocation Request Status Report' },
  },
  {
        path: 'sow-dashboard',
        loadChildren: () =>
          import(
            './features/sow-dashboard/sow-dashboard.module'
          ).then((m) => m.SowDashboardModule),
        canActivate: [ReportsAuthGuard],
        data: { breadcrumb: 'Project Efficiency Dashboard', id: 1028 },
  },
  {
    path: 'p2p-logs-report',
    loadChildren: () =>
      import('./features/p2p-logs-report/p2p-logs-report.module').then(
        (m) => m.P2pLogsReportModule
      ),
    data: { breadcrumb: 'P2P Logs'},
  },
  {
    path: 'rr-reports',
    loadChildren: () =>
      import(
        './features/rr-reports/rr-reports-routing.module'
      ).then((m) => m.RrReportsRoutingModule),
    data: { breadcrumb: 'RR Report' },
  },
  {
    path: 'timesheet-daily-log-report',
    loadChildren: () =>
      import(
        './features/timesheet-v2-daily-log-report/timesheet-v2-daily-log-report.module'
      ).then((m) => m.TimesheetV2DailyLogReportModule),
    data: { breadcrumb: 'Timesheet Daily Log Report - Calendar View' },
  },
  {
    path: 'rr-projections-position',
    loadChildren: () =>
      import(
        './features/rr-projections-position-report/rr-projections-position-report.module'
      ).then((m) => m.RrProjectionsPositionReportModule),
    data: { breadcrumb: 'Projection Report' },
  },
  {
    path: 'quote-monthly-projection',
    loadChildren: () =>
      import(
        './features/quote-monthly-projection/quote-monthly-projection.module'
      ).then((m) => m.QuoteMonthlyProjectionModule),
    data: { breadcrumb: 'Quote Monthly Projection' },
  },
  {
  path: 'sales-report',
  loadChildren: () =>
    import('./features/sales-report-version2/sales-report-version2.module').then(
      (m) => m.SalesReportVersion2Module
    ),
  data: { breadcrumb: 'Sales Report V2' },
},
{
  path: 'schedule3-admin-functions',
    loadChildren: () =>
      import('./features/schedule3-admin-functions/schedule3-admin-functions.module').then(
        (m) => m.Schedule3AdminFunctionsModule
      ),
    data: { breadcrumb: 'Schedule III Admin Functions' },
},
{
  path: 'project-milestone-report',
  loadChildren: () =>
    import('./features/project-v2-report/projects-v2/projects-v2.module').then(
      (m) => m.ProjectsV2Module
    ),
  data: { breadcrumb: 'Project Milestone Report' },
},
  {
    path: 'project-header-report-details',
    loadChildren: () =>
      import('./features/project-header-report/project-header/project-header.module').then(
        (m) => m.ProjectHeaderModule
      ),
    data: { breadcrumb: 'Project Header Report' },
  },
{
  path: 'ils-zb-exception-report',
  loadChildren: () =>
    import(
      './features/zb-ils-ledgers-exception-report/zb-ils-ledgers-exception-report.module'
    ).then((m) => m.ZbIlsLedgersExceptionReportModule),
  data: { breadcrumb: 'ZohoBooks GL Exception Report' },
},
{
  path: 'ils-admin-reports',
  loadChildren: () =>
    import(
      './features/ils-admin-reports/ils-admin-reports.module'
    ).then((m) => m.IlsAdminReportsModule),
  data: { breadcrumb: 'ILS Admin Reports' },
},
{
  path: 'zb-reports-sync-logs',
  loadChildren: () =>
    import(
      './features/zohobooks-reports-sync-log/zohobooks-reports-sync-log.module'
    ).then((m) => m.ZohobooksReportsSyncLogModule),
  data: { breadcrumb: 'ZohoBooks Sync Log' },
},
{
  path: 'zb-exception-report-logs',
  loadChildren: () =>
    import(
      './features/zohobooks-gl-exception-report-log/zohobooks-gl-exception-report-log.module'
    ).then((m) => m.ZohobooksGlExceptionReportLogModule),
  data: { breadcrumb: 'ZohoBooks GL Exception Report Log' },
},
{
  path: 'report-dashboard',
  loadChildren: () =>
    import('./features/report-dashboard/report-dashboard.module').then(
      (m) => m.ReportDashboardModule
    ),
    canActivate: [ReportsAuthGuard],
  data: { breadcrumb: 'Utilization Report', reportId:1, id: 1070 },
  },
  {
    path: 'revenue-report',
    loadChildren: () =>
      import(
        './features/revenue-actuals/revenue-actuals.module'
      ).then((m) => m.RevenueActualsModule),
    data: { breadcrumb: 'Revenue Report' },
  },
  {
    path: 'ubr-posting',
    loadChildren: () =>
      import(
        './features/ubr-posting-report/ubr-posting-report.module'
      ).then((m) => m.UbrPostingReportModule),
    data: { breadcrumb: 'UBR Posting Report' },
  },
  {
    path: 'ubr-report-new',
    loadChildren: () =>
      import(
        './features/ubr-report-new/ubr-report-new.module'
      ).then((m) => m.UbrReportNewModule),
    data: { breadcrumb: 'UBR Report (New)' },
  },
  {
    path: 'revenue-forecast',
    loadChildren: () =>
      import(
        './features/revenue-report-v2/revenue-report-v2.module'
      ).then((m) => m.RevenueReportV2Module),
    data: { breadcrumb: 'Revenue Forecast' },
  },
  {
    path: 'intercompany-reports',
    loadChildren: () =>
      import('./features/intercompany-reports/intercompany-reports.module').then(
        (m) => m.IntercompanyReportsModule
      ),
      data: { breadcrumb: 'Intercompany reports' },
    },
    {
      path: 'billing-tracker-report',
      loadChildren: () =>
        import('./features/report-dashboard/report-dashboard.module').then(
          (m) => m.ReportDashboardModule
        ),
      canActivate: [ReportsAuthGuard],
      data: { breadcrumb: 'Billing Tracker Report', reportId:2, id: 3002 },
    },
  {
    path: 'greyt-leave-report',
    loadChildren: () =>
      import('./features/greyt-leave-integration/greyt-leave-integration.module').then(
        (m) => m.GreytLeaveIntegrationModule
      ),
    data: { breadcrumb: 'GreyT Leave Integration' },
  },
  {
    path: 'qb-reports-sync-logs',
    loadChildren: () =>
      import(
        './features/quickbooks-reports-sync-log/quickbooks-reports-sync-log.module'
      ).then((m) => m.QuickbooksReportsSyncLogModule),
    data: { breadcrumb: 'QuickBooks Sync Log' },
  },
  {
    path: 'ils-qb-exception-report',
    loadChildren: () =>
      import(
        './features/qb-ils-ledgers-exception-report/qb-ils-ledgers-exception-report.module'
      ).then((m) => m.QbIlsLedgersExceptionReportModule),
    data: { breadcrumb: 'QuickBooks GL Exception Report' },
  },
  {
    path: 'qb-exception-report-logs',
    loadChildren: () =>
      import(
        './features/quickbooks-gl-exception-report-log/quickbooks-gl-exception-report-log.module'
      ).then((m) => m.QuickbooksGlExceptionReportLogModule),
    data: { breadcrumb: 'QuickBooks GL Exception Report Log' },
  },
  {
    path: 'mis-cockpit',
    loadChildren: () =>
      import('./features/mis-cockpit/mis-cockpit.module').then(
        (m) => m.MisCockpitModule
      ),
    data: { breadcrumb: 'MIS Cockpit' }
  },
  {
    path: 'employee-level-ap-report',
    loadChildren: () =>
      import(
        './features/employee-level-ap-report/employee-level-ap-report.module'
      ).then((m) => m.EmployeeLevelApReportModule),
    data: { breadcrumb: 'Employee level AP report' }
  },
  {
    path: 'ar-aging',
    loadChildren: () =>
      import('./features/ar-aging-report/ar-aging-report.module').then(
        (m) => m.ArAgingReportModule
      ),
      canActivate: [ReportsAuthGuard],
    data: { breadcrumb: 'AR Aging Report',id : 3021 },
  },
  {
    path: 'invoice-details',
    loadChildren: () =>
      import('./features/invoice-details-report/invoice-details-report.module').then(
        (m) => m.InvoiceDetailsReportModule
      ),
    data: { breadcrumb: 'Invoice Details Report' },
  },
  {
    path: 'ar-aging-pivot',
    loadChildren: () =>
      import('./features/ar-aging-pivot/ar-aging-pivot/ar-aging-pivot.module').then(
        (m) => m.ArAgingPivotModule
      ),
      canActivate: [ReportsAuthGuard],
    data: { breadcrumb: 'AR Aging Report' , id : 3021 },
  },
  {
    path: 'employee-allocation-status-report',
    loadChildren: () =>
      import('./features/people-report/people-report/people-report.module').then(
        (m) => m.PeopleReportModule
      ),
    data: { breadcrumb: 'Allocation Status Report' },
  },
  {
    path: 'soft-booking-employee-report',
    loadChildren: () =>
      import('./features/soft-booked-employee-report/soft-booked-employee-report/soft-booked-employee-report.module').then(
        (m) => m.SoftBookedEmployeeReportModule
      ),
    data: { breadcrumb: 'Employee Soft Booking Report' },
  },
  {
    path: 'project-billing-plan',
    loadChildren: () =>
      import('./features/project-billing-report/project-billing-report/project-billing-report.module').then(
        (m) => m.ProjectBillingReportModule
      ),
    data: { breadcrumb: 'Project Billing Plan Report' },
  },
  {
    path: 'ils-ad-ed-exception-report',
    loadChildren: () =>
      import('./features/integration-ad-ed-exception-report/integration-ad-ed-exception-report.module').then(
        (m) => m.IntegrationAdEdExceptionReportModule
      ),
    data: { breadcrumb: 'AD-ED Exception Report' },
  },
  {
    path: 'ils-ad-ed-exception-report-logs',
    loadChildren: () =>
      import('./features/ils-ad-ed-exception-report-logs/ils-ad-ed-exception-report-logs.module').then(
        (m) => m.IlsAdEdExceptionReportLogsModule
      ),
    data: { breadcrumb: 'AD-ED Exception Report Logs' },
  },
  {
    path: 'people-allocation-dashboard-v2',
    loadChildren: () =>
      import('./features/project-v2-dashboard/project-v2-dashboard.module').then(
        (m) => m.ProjectV2DashboardModule
      ),
    data: { breadcrumb: 'People Allocation Dashboard', reportId:4003 },
  },
  {
    path: 'revenue-movement',
    loadChildren: () =>
      import('./features/revenue-movement/revenue-movement.module').then(
        (m) => m.RevenueMovementModule
      ),
    data: { breadcrumb: 'Revenue Movement Report' },
  },
  {
    path: 'integration-trace-report',
    loadChildren: () =>
      import('./features/integration-trace-log-report/integration-trace-log-report.module').then(
        (m) => m.IntegrationTraceLogReportModule
      ),
      data: { breadcrumb: 'Integration Trace Logs' },
  },
  {
    path: 'ap-aging',
    loadChildren: () =>
      import('./features/ap-aging-report/ap-aging-report.module').then(
        (m) => m.ApAgingReportModule
      ),
    data: { breadcrumb: 'AP Aging Report' }
  },
  {
    path: 'country-level-mis-pivot',
    loadChildren: () =>
      import('./features/country-level-mis-pivot-framework/country-level-mis-pivot-framework.module').then(
        (m) => m.CountryLevelMisPivotFrameworkModule
      ),
    data: { breadcrumb: 'Country Level MIS' },
  },
  { 
    path: 'claim-summary-report',
    loadChildren: () =>
      import('./features/claim-summary-report/claim-summary-report.module').then(
        (m) => m.ClaimSummaryReportModule
      ),
    data: { breadcrumb: 'Claim Summary Report' },
  },
  {
    path: 'claim-details-report',
    loadChildren: () =>
      import('./features/claim-details-report/claim-details-report.module').then(
        (m) => m.ClaimDetailsReportModule
      ),
    data: { breadcrumb: 'Claim Details Report' },
  },
  {
    path: 'project-allocation-report',
    loadChildren: () =>
      import('./features/project-allocation-report/project-allocation-report/project-allocation-report.module').then(
        (m) => m.ProjectAllocationReportModule
      ),
    data: { breadcrumb: 'Project Allocation Report' },
  },
  {
    path: 'resource-demand-report',
    loadChildren: () =>
      import('./features/demand-request-report/demand-request.module').then(
        (m) => m.DemandRequestModule
      ),
    data: { breadcrumb: 'Resource Demand Report' },
  },
  {
    path: 'invoice-template',
    loadChildren: () =>
      import('./features/invoice-template-portal/invoice-template-portal.module').then(
        (m) => m.InvoiceTemplatePortalModule
      ),
    data: { breadcrumb: 'Invoice Template Portal' },
  },
  {
    path: 'unassigned-aging-report',
    loadChildren: () =>
      import('./features/unassigned-aging-report/unassigned-aging-report.module').then(
        (m) => m.UnassignedAgingReportModule
      ),
    data: { breadcrumb: 'Bench Aging Report' },
  },
  {
    path: 'project-revenue-report',
    loadChildren: () =>
      import('./features/project-revenue-report/project-revenue-report/project-revenue-report.module').then(
        (m) => m.ProjectRevenueReportModule
      ),
    data: { breadcrumb: 'Project Revenue Report' },
  },
  {
    path: 'employee-capacity-report',
    loadChildren: () =>
      import('./features/employee-capacity-report/employee-capacity-report.module').then(
        (m) => m.EmployeeCapacityReportModule
      ),
    canActivate: [ReportsAuthGuard],
    data: { breadcrumb: 'Employee Capacity Report', id : 8222 },
  },
  {
    path: 'mis-custom-report',
    loadChildren: () =>
      import('./features/ts-mis-custom-report/ts-mis-custom-report.module').then(
        (m) => m.TsMisCustomReportModule
      ),
    data: { breadcrumb: 'MIS Custom Report' },
  },
  {
    path: 'custom-financial-report',
    loadChildren: () =>
      import('./features/custom-financial-report/custom-financial-report.module').then(
        (m) => m.CustomFinancialReportModule
      ),
    data: { breadcrumb: 'Zifo Allocation Custom Report - 1' },
  },
  {
    path: 'project-demobilization-report',
    loadChildren: () =>
      import('./features/project-demobilization-report/project-demobilization-report.module').then(
        (m) => m.ProjectDemobilizationReportModule
      ),
    data: { breadcrumb: 'Project Demobilization Report' },
  },
  {
    path: 'project-milestone-report-v2',
    loadChildren: () =>
      import('./features/milestone-tracker-report-v2/milestone-tracker-report/milestone-tracker-report.module').then(
        (m) => m.MilestoneTrackerReportModule
      ),
    data: { breadcrumb: 'Project Milestone Report' },
  },
  {
    path: 'project-header-report-details-v2',
    loadChildren: () =>
      import('./features/project-header-report-v2/project-header-report-v2/project-header-report-v2.module').then(
        (m) => m.ProjectHeaderReportV2Module
      ),
    data: { breadcrumb: 'Project Header Report' },
  },
  {
    path: 'employee-activity-report',
    loadChildren: () =>
      import('./features/employee-activity-report/employee-activity-report.module').then(
        (m) => m.EmployeeActivityReportModule
      ),
    data: { breadcrumb: 'Employee Activity Report' },
  },
  {
    path: 'billing-advice-report',
    loadChildren: () =>
      import('./features/billing-advice-report/billing-advice-report.module').then(
        (m) => m.BillingAdviceModule
      ),
    data: { breadcrumb: 'Billing Advice Report - T&M/FC' },
  },
  {
    path: 'employee-pop-up',
    loadChildren: () =>
      import('./features/ed-event-wishes-report/ed-event-wishes-report.module').then(
        (m) => m.EdEventWishesReportModule
      ),
    data: { breadcrumb: 'Employee Pop Up Report' },
  },{
    path: 'allocation-efficiency-report',
    loadChildren: () =>
      import('./features/project-efficiency-report-v2/project-efficiency-report-v2.module').then(
        (m) => m.ProjectEfficiencyReportV2Module
      ),
    data: { breadcrumb: 'Allocation Efficiency Report' },
  },
  {
    path: 'rfid-attendance-report',
    loadChildren: () =>
      import('./features/timesheet-attendance-report/timesheet-attendance-report.module').then(
        (m) => m.TimesheetAttendanceReportModule
      ),
    data: { breadcrumb: 'RFID Attendance Report' },
  },
  {
    path: 'leave-report',
    loadChildren: () =>
      import('./features/leave-report/leave-report.module').then(
        (m) => m.LeaveReportModule
      ),
    data: { breadcrumb: 'Leave Report' },
  },
  {
     path: 'ubr-aging-report',
    loadChildren: () =>
      import('./features/ubr-aging-report/ubr-aging-report.module').then(
        (m) => m.UbrAgingReportModule
      ),
    data: { breadcrumb: 'UBR Aging Report' },
  },
  {
    path: 'notifications-report',
    loadChildren: () =>
      import('./features/notifications-reports/notifications-reports.module').then(
        (m) => m.NotificationsReportsModule
      ),
    data: { breadcrumb: 'Notifications Report' },
  },
  {
    path: 'role-access-control-report',
    loadChildren: () =>
      import('./features/role-and-access-control-report/role-and-access-control-report.module').then(
        (m) => m.RoleAndAccessControlReportModule
      ),
    data: { breadcrumb: 'Role & Access Control Report' },
  }
];



@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ReportsRoutingModule { }
