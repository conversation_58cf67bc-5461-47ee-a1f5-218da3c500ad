(window.webpackJsonp=window.webpackJsonp||[]).push([[944],{Ei1k:function(e,t,n){"use strict";n.r(t),n.d(t,"CreateCollectorTaskComponent",(function(){return H})),n.d(t,"CreateCollectorTaskModule",(function(){return $}));var a=n("3Pt+"),o=n("wd/R"),l=n("1G5W"),i=n("XNiG"),r=n("7pIB"),s=n("0IaG"),d=n("ofXK"),c=n("STbY"),m=n("kmnG"),p=n("qFsG"),u=n("bTqV"),g=n("NFeN"),h=n("Qu3c"),f=n("/1cH"),v=n("d3UM"),k=n("iadO"),b=n("Xa2L"),x=n("4/q7"),y=n("Xi0T"),D=n("fXoL"),C=n("XXEo"),_=n("6xF0"),M=n("BVzC"),S=n("dNgK"),E=n("6t9p"),P=n("8SgF"),T=n("me71"),O=n("FKr1");function w(e,t){if(1&e){const e=D["\u0275\u0275getCurrentView"]();D["\u0275\u0275elementStart"](0,"div"),D["\u0275\u0275elementStart"](1,"button",52),D["\u0275\u0275listener"]("click",(function(){D["\u0275\u0275restoreView"](e);const n=t.index;return D["\u0275\u0275nextContext"]().startDatesClicked(n)})),D["\u0275\u0275text"](2),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;D["\u0275\u0275advance"](1),D["\u0275\u0275propertyInterpolate2"]("matTooltip","",e.duration,"\n",e.displayValue,""),D["\u0275\u0275property"]("ngClass",e.clicked?"btn-active":"btn-not-active"),D["\u0275\u0275advance"](1),D["\u0275\u0275textInterpolate1"](" ",e.label," ")}}function F(e,t){if(1&e){const e=D["\u0275\u0275getCurrentView"]();D["\u0275\u0275elementStart"](0,"div"),D["\u0275\u0275elementStart"](1,"button",52),D["\u0275\u0275listener"]("click",(function(){D["\u0275\u0275restoreView"](e);const n=t.index;return D["\u0275\u0275nextContext"]().endDatesClicked(n)})),D["\u0275\u0275text"](2),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;D["\u0275\u0275advance"](1),D["\u0275\u0275propertyInterpolate2"]("matTooltip","",e.duration,"\n",e.displayValue,""),D["\u0275\u0275property"]("ngClass",e.clicked?"btn-active":"btn-not-active"),D["\u0275\u0275advance"](1),D["\u0275\u0275textInterpolate1"](" ",e.label," ")}}function I(e,t){if(1&e&&(D["\u0275\u0275elementStart"](0,"mat-option",53),D["\u0275\u0275text"](1),D["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;D["\u0275\u0275property"]("value",e.id),D["\u0275\u0275advance"](1),D["\u0275\u0275textInterpolate"](e.name)}}function Y(e,t){1&e&&(D["\u0275\u0275elementStart"](0,"mat-error",54),D["\u0275\u0275text"](1," Planned Hours should not be '-ve' "),D["\u0275\u0275elementEnd"]())}function V(e,t){if(1&e){const e=D["\u0275\u0275getCurrentView"]();D["\u0275\u0275elementStart"](0,"div",55),D["\u0275\u0275listener"]("fileOver",(function(t){return D["\u0275\u0275restoreView"](e),D["\u0275\u0275nextContext"]().fileOverDropZone(t)}))("onFileDrop",(function(t){return D["\u0275\u0275restoreView"](e),D["\u0275\u0275nextContext"]().filedrop(t)})),D["\u0275\u0275elementStart"](1,"div",56),D["\u0275\u0275elementStart"](2,"div",57),D["\u0275\u0275elementStart"](3,"small",36),D["\u0275\u0275text"](4,"Drag and drop your attachments here"),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementStart"](5,"div",58),D["\u0275\u0275elementStart"](6,"button",59),D["\u0275\u0275listener"]("click",(function(){return D["\u0275\u0275restoreView"](e),D["\u0275\u0275reference"](9).click()})),D["\u0275\u0275text"](7," Upload "),D["\u0275\u0275elementEnd"](),D["\u0275\u0275element"](8,"input",60,61),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementStart"](10,"div",57),D["\u0275\u0275elementStart"](11,"div",62),D["\u0275\u0275elementStart"](12,"mat-icon",63),D["\u0275\u0275text"](13,"cloud_upload"),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"]()}if(2&e){const e=D["\u0275\u0275nextContext"]();D["\u0275\u0275property"]("ngClass",e.isFileOverDropZone?"file-present":"file-empty")("uploader",e.uploader),D["\u0275\u0275advance"](8),D["\u0275\u0275property"]("uploader",e.uploader)("accept",e.allowedMimeType)}}function j(e,t){if(1&e){const e=D["\u0275\u0275getCurrentView"]();D["\u0275\u0275elementStart"](0,"div"),D["\u0275\u0275elementStart"](1,"div",36),D["\u0275\u0275elementStart"](2,"span",69),D["\u0275\u0275text"](3),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementStart"](4,"span",65),D["\u0275\u0275elementStart"](5,"button",70),D["\u0275\u0275listener"]("click",(function(){D["\u0275\u0275restoreView"](e);const n=t.$implicit;return D["\u0275\u0275nextContext"](2).uploader.removeFromQueue(n)})),D["\u0275\u0275elementStart"](6,"mat-icon",8),D["\u0275\u0275text"](7,"close"),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;D["\u0275\u0275advance"](2),D["\u0275\u0275property"]("matTooltip",null==e||null==e.file?null:e.file.name),D["\u0275\u0275advance"](1),D["\u0275\u0275textInterpolate1"](" ",null==e||null==e.file?null:e.file.name,"")}}function q(e,t){if(1&e){const e=D["\u0275\u0275getCurrentView"]();D["\u0275\u0275elementStart"](0,"div"),D["\u0275\u0275elementStart"](1,"div",36),D["\u0275\u0275elementStart"](2,"div",64),D["\u0275\u0275text"](3," Files attached "),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementStart"](4,"div",65),D["\u0275\u0275elementStart"](5,"button",66),D["\u0275\u0275listener"]("click",(function(){return D["\u0275\u0275restoreView"](e),D["\u0275\u0275reference"](9).click()})),D["\u0275\u0275elementStart"](6,"mat-icon",4),D["\u0275\u0275text"](7,"cloud_upload"),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275element"](8,"input",60,67),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementStart"](10,"div",68),D["\u0275\u0275template"](11,j,8,2,"div",37),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"]()}if(2&e){const e=D["\u0275\u0275nextContext"]();D["\u0275\u0275advance"](8),D["\u0275\u0275property"]("uploader",e.uploader)("accept",e.allowedMimeType),D["\u0275\u0275advance"](3),D["\u0275\u0275property"]("ngForOf",e.uploader.queue)}}function L(e,t){1&e&&(D["\u0275\u0275elementStart"](0,"mat-icon"),D["\u0275\u0275text"](1,"done_all"),D["\u0275\u0275elementEnd"]())}function N(e,t){1&e&&D["\u0275\u0275element"](0,"mat-spinner",71)}const B=function(e){return{"background-color":e}},z=function(e){return{"is-disabled":e}};let H=(()=>{class e{constructor(e,t,n,l,s,d,c){this._auth=e,this.dialogRef=t,this.inData=n,this.fb=l,this._billedInvoiceService=s,this._ErrorService=d,this.snackBar=c,this.currentDate=o(),this.currentUser=this._auth.getProfile().profile,this.$onDestroy=new i.b,this.locationList=[],this.isLoading=!1,this.allowedMimeType=["*/*"],this.filesJsonConcat=[],this.taskTypes=[],this.title="Create Task",this.taskForm=this.fb.group({taskName:[null,a.H.required],taskDesc:[null],assignedTo:[null,a.H.required],startDate:[null,a.H.required],endDate:[null,a.H.required],plannedHours:[null,a.H.min(0)],taskType:[null,a.H.required],attachments:[null]}),this.minDate=o(),this.startDateTypes=[{label:"Today",value:o(),duration:"0 days",displayValue:"Due on : "+o().format("DD-MMM-YY"),clicked:!1},{label:"1 D",value:o().add(1,"d"),duration:"1 Day",displayValue:"Due on : "+o().add(1,"d").format("DD-MMM-YY"),clicked:!1},{label:"2 D",value:o().add(2,"d"),duration:"2 Days",displayValue:"Due on : "+o().add(2,"d").format("DD-MMM-YY"),clicked:!1},{label:"1 W",value:o().add(1,"w"),duration:"1 Week",displayValue:"Due on : "+o().add(1,"w").format("DD-MMM-YY"),clicked:!1}],this.endDateTypes=[{label:"Today",value:o(),duration:"0 days",displayValue:"Due on : "+o().format("DD-MMM-YY"),clicked:!1},{label:"1 D",value:o().add(1,"d"),duration:"1 Day",displayValue:"Due on : "+o().add(1,"d").format("DD-MMM-YY"),clicked:!1},{label:"2 D",value:o().add(2,"d"),duration:"2 Days",displayValue:"Due on : "+o().add(2,"d").format("DD-MMM-YY"),clicked:!1},{label:"1 W",value:o().add(1,"w"),duration:"1 Week",displayValue:"Due on : "+o().add(1,"w").format("DD-MMM-YY"),clicked:!1}],this.maxFileSize=10485760,this.isFileOverDropZone=!1,this.uploader=new r.d({url:"/api/collector/uploadCollectorAttachment",authToken:"Bearer "+this._auth.getToken(),disableMultipart:!1,maxFileSize:this.maxFileSize,headers:[{name:"user",value:this.currentUser.name}]}),this.showValidationAlert=()=>{let e=this.getTaskDetails();this.snackBar.open(null==e.task_name?"Kindly enter Task name":null==e.assigned_to[0].employee_oid?"Kindly assign a person for the Task":null==e.start_date?"Kindly enter valid start date for the Task":null==e.end_date?"Kindly enter valid end date for the Task":null==e.task_type?"Kindly select type of task":"Kindly check if all values are filled","Dismiss",{duration:3e3})},this.fileUploadChanges()}ngOnInit(){var e,t;this.modalParams=this.inData.modalParams,(null===(e=this.modalParams)||void 0===e?void 0:e.is_add_task)&&!(null===(t=this.modalParams)||void 0===t?void 0:t.billedInvoiceDetails)&&(this.title="Create Sub Task"),this.detectFormControlChanges(),this.taskForm.get("startDate").patchValue(this.currentDate),this.getTaskTypes()}getTaskTypes(){this._billedInvoiceService.getTaskTypes().pipe(Object(l.a)(this.$onDestroy)).subscribe(e=>{"S"==e.messType&&e.data.length>0&&(this.taskTypes=e.data)},e=>{console.log(e)})}detectFormControlChanges(){this.taskForm.get("startDate").valueChanges.pipe(Object(l.a)(this.$onDestroy)).subscribe(e=>{for(let t=0;t<this.startDateTypes.length;t++)this.startDateTypes[t].clicked=o(e).format("YYYY-MM-DD")===o(this.startDateTypes[t].value).format("YYYY-MM-DD")}),this.taskForm.get("endDate").valueChanges.pipe(Object(l.a)(this.$onDestroy)).subscribe(e=>{for(let t=0;t<this.endDateTypes.length;t++)this.endDateTypes[t].clicked=o(e).format("YYYY-MM-DD")===o(this.endDateTypes[t].value).format("YYYY-MM-DD")})}setCurrentEmployee(e){e&&e!=this.taskForm.get("assignedTo").value&&this.taskForm.get("assignedTo").patchValue(e)}startDatesClicked(e){for(let t=0;t<this.startDateTypes.length;t++)this.startDateTypes[t].clicked=t===e;this.taskForm.patchValue({startDate:this.startDateTypes[e].value})}endDatesClicked(e){for(let t=0;t<this.endDateTypes.length;t++)this.endDateTypes[t].clicked=t===e;this.taskForm.patchValue({endDate:this.endDateTypes[e].value})}getTaskDetails(){var e,t,n,a,l,i,r,s;return{task_name:this.taskForm.get("taskName").value,task_description:this.taskForm.get("taskDesc").value,status_id:0,status_name:"Open",assigned_to:[{employee_oid:this.taskForm.get("assignedTo").value,is_active:!0}],start_date:this.taskForm.get("startDate").value?o(this.taskForm.get("startDate").value).format("YYYY-MM-DD"):null,end_date:this.taskForm.get("endDate").value?o(this.taskForm.get("endDate").value).format("YYYY-MM-DD"):null,planned_hours:this.taskForm.get("plannedHours").value?this.taskForm.get("plannedHours").value:0,task_type:this.taskForm.get("taskType").value,attachments:this.taskForm.get("attachments").value?this.taskForm.get("attachments").value:[],comments:[],created_on:this.currentDate,created_by:this.currentUser.oid,parent_id:this.modalParams.parent_id?this.modalParams.parent_id:null,project_name:(null===(t=null===(e=this.modalParams)||void 0===e?void 0:e.billedInvoiceDetails)||void 0===t?void 0:t.project_name)?this.modalParams.billedInvoiceDetails.project_name:"",milestone_name:(null===(a=null===(n=this.modalParams)||void 0===n?void 0:n.billedInvoiceDetails)||void 0===a?void 0:a.milestone_name)?this.modalParams.billedInvoiceDetails.milestone_name:"",pl_id:(null===(i=null===(l=this.modalParams)||void 0===l?void 0:l.billedInvoiceDetails)||void 0===i?void 0:i.pl_id)?this.modalParams.billedInvoiceDetails.pl:"",customer_name:(null===(s=null===(r=this.modalParams)||void 0===r?void 0:r.billedInvoiceDetails)||void 0===s?void 0:s.customer_name)?this.modalParams.billedInvoiceDetails.customer_name:"",is_active:!0}}saveTask(){"VALID"==this.taskForm.status?(console.log("task form",this.taskForm),this.uploader.queue.length>0?this.uploader.uploadAll():this.modalParams.is_add_task?this.addTask():this.createTask()):this.showValidationAlert()}fileUploadChanges(){this.uploader.onProgressItem=e=>{this.isLoading=!0},this.uploader.onCompleteItem=(e,t,n,a)=>{if(200==n&&t&&t.length>0){this.isLoading=!1;let e=JSON.parse(t);this.filesJsonConcat=this.filesJsonConcat.concat(e.files_json),this.uploader.queue.length==this.filesJsonConcat.length&&(this.taskForm.get("attachments").patchValue(this.filesJsonConcat),this.modalParams.is_add_task?this.addTask():this.createTask())}else this.snackBar.open("Unable to upload","Dismiss",{duration:3e3})}}addTask(){this.isLoading=!0,this._billedInvoiceService.addBilledInvoiceTask(this.modalParams.billing_id,this.modalParams.customer_id,this.getTaskDetails()).pipe(Object(l.a)(this.$onDestroy)).subscribe(e=>{this.isLoading=!1,"S"==e.messType&&(this.snackBar.open(e.userMess,"Dismiss",{duration:3e3}),this.dialogRef.close({event:"submit",res:e,taskDetails:this.getTaskDetails()}),this.taskForm.reset())},e=>{this.isLoading=!1,this.snackBar.open("Something went wrong!","Dismiss",{duration:3e3}),console.log(e)})}createTask(){this.isLoading=!0,this._billedInvoiceService.createBilledInvoiceTask(this.modalParams.billing_id,this.modalParams.customer_id,this.getTaskDetails()).pipe(Object(l.a)(this.$onDestroy)).subscribe(e=>{this.isLoading=!1,"S"==e.messType&&(this.snackBar.open(e.userMess,"Dismiss",{duration:3e3}),this.dialogRef.close({event:"submit",res:e,taskDetails:this.getTaskDetails()}),this.taskForm.reset())},e=>{this.isLoading=!1,this.snackBar.open("Something went wrong!","Dismiss",{duration:3e3}),console.log(e)})}closeTaskModal(){this.dialogRef.close({event:"close"})}ngOnDestroy(){this.$onDestroy.next(),this.$onDestroy.complete()}}return e.\u0275fac=function(t){return new(t||e)(D["\u0275\u0275directiveInject"](C.a),D["\u0275\u0275directiveInject"](s.h),D["\u0275\u0275directiveInject"](s.a),D["\u0275\u0275directiveInject"](a.i),D["\u0275\u0275directiveInject"](_.a),D["\u0275\u0275directiveInject"](M.a),D["\u0275\u0275directiveInject"](S.a))},e.\u0275cmp=D["\u0275\u0275defineComponent"]({type:e,selectors:[["app-create-collector-task"]],decls:95,vars:30,consts:[[1,"container-fluid","create-collector-task"],[1,"row",2,"border-bottom","solid 1px #cacaca"],[1,"col-11","pt-1","pb-2","d-flex","pl-2"],["mat-icon-button","",1,"bubble","mt-1"],[1,"iconButton"],[1,"headingBold","pt-2","ml-3"],[1,"col-1","d-flex"],["matTooltip","Close","mat-icon-button","",1,"ml-auto","close-button","mt-1",3,"click"],[1,"close-Icon"],[3,"formGroup"],[1,"row","pt-2"],[1,"col-10"],["appearance","outline",1,"form-field-class"],["matInput","","placeholder","Name","formControlName","taskName",3,"required"],["formControlName","taskDesc","placeholder","Task description",1,"dev-extreme-styles"],["name","bold"],["name","italic"],["name","strike"],["name","underline"],["name","separator"],["name","alignLeft"],["name","alignCenter"],["name","alignRight"],["name","alignJustify"],["name","orderedList"],["name","bulletList"],[1,"row","pt-3"],[1,"col-5"],["label","Assign to","formControlName","assignedTo",2,"width","100% !important",3,"isAutocomplete","required"],[1,"col-1","mt-2"],["imgWidth","30px","imgHeight","30px",2,"cursor","pointer",3,"matTooltip","id","click"],[1,"col-4"],["matInput","","formControlName","startDate",3,"min","matDatepicker","required"],["matSuffix","",3,"for"],["startDatepicker",""],[1,"col-5","pr-0","pl-0","pt-1"],[1,"row"],[4,"ngFor","ngForOf"],["matInput","","formControlName","endDate",3,"min","matDatepicker","required"],["endDatepicker",""],[1,"headingBold","pl-1"],["formControlName","taskType","required","true"],[3,"value",4,"ngFor","ngForOf"],[1,"headingBold"],["matInput","","type","number","placeholder","Planned Hours","formControlName","plannedHours","min","0"],["class","pt-2",4,"ngIf"],[1,"col-8"],["ng2FileDrop","","style","height: 6rem",3,"ngClass","uploader","fileOver","onFileDrop",4,"ngIf"],[4,"ngIf"],[1,"col-2","d-flex","my-auto","justify-content-center"],["mat-icon-button","","matTooltip","Create Task",1,"iconbtn",3,"ngStyle","ngClass","disabled","click"],["diameter","30","class","spinner-align",4,"ngIf"],["mat-raised-button","","matTooltipPosition","above","matTooltipClass","my-tooltip-multi-line",1,"ml-2",3,"matTooltip","ngClass","click"],[3,"value"],[1,"pt-2"],["ng2FileDrop","",2,"height","6rem",3,"ngClass","uploader","fileOver","onFileDrop"],[1,"row","justify-content-center","pt-2"],[1,"col-6"],[1,"row","pt-3","justify-content-center"],["mat-raised-button","",1,"upload-btn",3,"click"],["hidden","","type","file","ng2FileSelect","",3,"uploader","accept"],["fileInput",""],[1,"row","d-flex","justify-content-center",2,"min-height","5rem"],[1,"my-auto","cloud-icon"],[1,"col-11","pl-0","headingBold"],[1,"col-1","pl-0","pr-0"],["mat-icon-button","","matTooltip","Add more files",1,"view-button-inactive",3,"click"],["moreFileInput",""],[1,"scroll-window"],[1,"col-11","pl-2","pt-2","pr-0","normalFont",3,"matTooltip"],["mat-icon-button","","matTooltip","Remove file",1,"ml-auto","close-button",3,"click"],["diameter","30",1,"spinner-align"]],template:function(e,t){if(1&e&&(D["\u0275\u0275elementStart"](0,"div",0),D["\u0275\u0275elementStart"](1,"div",1),D["\u0275\u0275elementStart"](2,"div",2),D["\u0275\u0275elementStart"](3,"div",3),D["\u0275\u0275elementStart"](4,"mat-icon",4),D["\u0275\u0275text"](5,"add_task"),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementStart"](6,"span",5),D["\u0275\u0275text"](7),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementStart"](8,"div",6),D["\u0275\u0275elementStart"](9,"button",7),D["\u0275\u0275listener"]("click",(function(){return t.closeTaskModal()})),D["\u0275\u0275elementStart"](10,"mat-icon",8),D["\u0275\u0275text"](11,"close"),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementStart"](12,"form",9),D["\u0275\u0275elementStart"](13,"div",10),D["\u0275\u0275elementStart"](14,"div",11),D["\u0275\u0275elementStart"](15,"mat-form-field",12),D["\u0275\u0275elementStart"](16,"mat-label"),D["\u0275\u0275text"](17,"Task name"),D["\u0275\u0275elementEnd"](),D["\u0275\u0275element"](18,"input",13),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementStart"](19,"div",10),D["\u0275\u0275elementStart"](20,"div",11),D["\u0275\u0275elementStart"](21,"dx-html-editor",14),D["\u0275\u0275elementStart"](22,"dxo-toolbar"),D["\u0275\u0275element"](23,"dxi-item",15),D["\u0275\u0275element"](24,"dxi-item",16),D["\u0275\u0275element"](25,"dxi-item",17),D["\u0275\u0275element"](26,"dxi-item",18),D["\u0275\u0275element"](27,"dxi-item",19),D["\u0275\u0275element"](28,"dxi-item",20),D["\u0275\u0275element"](29,"dxi-item",21),D["\u0275\u0275element"](30,"dxi-item",22),D["\u0275\u0275element"](31,"dxi-item",23),D["\u0275\u0275element"](32,"dxi-item",19),D["\u0275\u0275element"](33,"dxi-item",24),D["\u0275\u0275element"](34,"dxi-item",25),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementStart"](35,"div",26),D["\u0275\u0275elementStart"](36,"div",27),D["\u0275\u0275element"](37,"app-search-user",28),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementStart"](38,"div",29),D["\u0275\u0275elementStart"](39,"app-user-image",30),D["\u0275\u0275listener"]("click",(function(){return t.setCurrentEmployee(t.currentUser.oid)})),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275element"](40,"div",31),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementStart"](41,"div",10),D["\u0275\u0275elementStart"](42,"div",27),D["\u0275\u0275elementStart"](43,"mat-form-field",12),D["\u0275\u0275elementStart"](44,"mat-label"),D["\u0275\u0275text"](45,"Start date"),D["\u0275\u0275elementEnd"](),D["\u0275\u0275element"](46,"input",32),D["\u0275\u0275element"](47,"mat-datepicker-toggle",33),D["\u0275\u0275element"](48,"mat-datepicker",null,34),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementStart"](50,"div",35),D["\u0275\u0275elementStart"](51,"div",36),D["\u0275\u0275template"](52,w,3,4,"div",37),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementStart"](53,"div",10),D["\u0275\u0275elementStart"](54,"div",27),D["\u0275\u0275elementStart"](55,"mat-form-field",12),D["\u0275\u0275elementStart"](56,"mat-label"),D["\u0275\u0275text"](57,"End date"),D["\u0275\u0275elementEnd"](),D["\u0275\u0275element"](58,"input",38),D["\u0275\u0275element"](59,"mat-datepicker-toggle",33),D["\u0275\u0275element"](60,"mat-datepicker",null,39),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementStart"](62,"div",35),D["\u0275\u0275elementStart"](63,"div",36),D["\u0275\u0275template"](64,F,3,4,"div",37),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementStart"](65,"div",36),D["\u0275\u0275elementStart"](66,"div",11),D["\u0275\u0275elementStart"](67,"span",40),D["\u0275\u0275text"](68,"Task Type"),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementStart"](69,"div",26),D["\u0275\u0275elementStart"](70,"div",27),D["\u0275\u0275elementStart"](71,"mat-form-field",12),D["\u0275\u0275elementStart"](72,"mat-label"),D["\u0275\u0275text"](73,"Type"),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementStart"](74,"mat-select",41),D["\u0275\u0275template"](75,I,2,2,"mat-option",42),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementStart"](76,"div",10),D["\u0275\u0275elementStart"](77,"div",27),D["\u0275\u0275elementStart"](78,"span",43),D["\u0275\u0275text"](79,"Hours"),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementStart"](80,"div",10),D["\u0275\u0275elementStart"](81,"div",27),D["\u0275\u0275elementStart"](82,"mat-form-field",12),D["\u0275\u0275elementStart"](83,"mat-label"),D["\u0275\u0275text"](84,"Planned Hours"),D["\u0275\u0275elementEnd"](),D["\u0275\u0275element"](85,"input",44),D["\u0275\u0275template"](86,Y,2,0,"mat-error",45),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementStart"](87,"div",10),D["\u0275\u0275elementStart"](88,"div",46),D["\u0275\u0275template"](89,V,14,4,"div",47),D["\u0275\u0275template"](90,q,12,3,"div",48),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementStart"](91,"div",49),D["\u0275\u0275elementStart"](92,"button",50),D["\u0275\u0275listener"]("click",(function(){return t.saveTask()})),D["\u0275\u0275template"](93,L,2,0,"mat-icon",48),D["\u0275\u0275template"](94,N,1,0,"mat-spinner",51),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"](),D["\u0275\u0275elementEnd"]()),2&e){const e=D["\u0275\u0275reference"](49),n=D["\u0275\u0275reference"](61);let a=null;D["\u0275\u0275advance"](7),D["\u0275\u0275textInterpolate1"](" ",t.title," "),D["\u0275\u0275advance"](5),D["\u0275\u0275property"]("formGroup",t.taskForm),D["\u0275\u0275advance"](6),D["\u0275\u0275property"]("required",!0),D["\u0275\u0275advance"](19),D["\u0275\u0275property"]("isAutocomplete",!0)("required",!0),D["\u0275\u0275advance"](2),D["\u0275\u0275property"]("matTooltip",t.currentUser.name)("id",t.currentUser.oid),D["\u0275\u0275advance"](7),D["\u0275\u0275property"]("min",t.minDate)("matDatepicker",e)("required",!0),D["\u0275\u0275advance"](1),D["\u0275\u0275property"]("for",e),D["\u0275\u0275advance"](5),D["\u0275\u0275property"]("ngForOf",t.startDateTypes),D["\u0275\u0275advance"](6),D["\u0275\u0275property"]("min",t.taskForm.get("startDate").value)("matDatepicker",n)("required",!0),D["\u0275\u0275advance"](1),D["\u0275\u0275property"]("for",n),D["\u0275\u0275advance"](5),D["\u0275\u0275property"]("ngForOf",t.endDateTypes),D["\u0275\u0275advance"](11),D["\u0275\u0275property"]("ngForOf",t.taskTypes),D["\u0275\u0275advance"](11),D["\u0275\u0275property"]("ngIf",null==(a=t.taskForm.get("plannedHours"))?null:a.hasError("min")),D["\u0275\u0275advance"](3),D["\u0275\u0275property"]("ngIf",0==t.uploader.queue.length),D["\u0275\u0275advance"](1),D["\u0275\u0275property"]("ngIf",t.uploader.queue.length>0),D["\u0275\u0275advance"](2),D["\u0275\u0275property"]("ngStyle",D["\u0275\u0275pureFunction1"](26,B,t.isLoading?"#f3f3f3":"#cf0001"))("ngClass",D["\u0275\u0275pureFunction1"](28,z,t.isLoading))("disabled",t.isLoading),D["\u0275\u0275advance"](1),D["\u0275\u0275property"]("ngIf",!t.isLoading),D["\u0275\u0275advance"](1),D["\u0275\u0275property"]("ngIf",t.isLoading)}},directives:[g.a,u.a,h.a,a.J,a.w,a.n,m.c,m.g,p.b,a.e,a.v,a.l,a.F,x.a,E.Ge,E.o,P.a,T.a,k.g,k.i,m.i,k.f,d.NgForOf,v.c,a.A,d.NgIf,d.NgStyle,d.NgClass,O.p,m.b,r.a,r.b,b.c],styles:[".create-collector-task[_ngcontent-%COMP%]   .headingBold[_ngcontent-%COMP%]{color:#cf0001;font-weight:500;font-size:14px}.create-collector-task[_ngcontent-%COMP%]   .dev-extreme-styles[_ngcontent-%COMP%]     .dx-htmleditor-toolbar-wrapper .dx-toolbar-items-container{height:48px!important}.create-collector-task[_ngcontent-%COMP%]   .dev-extreme-styles[_ngcontent-%COMP%]     .dx-htmleditor-toolbar-wrapper .dx-button-content .dx-icon{font-size:16px!important;color:#66615b!important}.create-collector-task[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:100%;font-size:13px}.create-collector-task[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff}.create-collector-task[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%], .create-collector-task[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]{font-weight:400;font-size:13px!important;min-width:60px;line-height:33px;padding:0 8px;border-radius:4px;margin-right:6px!important;margin-bottom:3px}.create-collector-task[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]{color:rgba(0,0,0,.534);background-color:rgba(194,193,193,.24)}.create-collector-task[_ngcontent-%COMP%]   .file-present[_ngcontent-%COMP%]{border:3px dotted #e44a4a;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.create-collector-task[_ngcontent-%COMP%]   .file-present[_ngcontent-%COMP%]   .cloud-icon[_ngcontent-%COMP%]{color:#e44a4a}.create-collector-task[_ngcontent-%COMP%]   .file-empty[_ngcontent-%COMP%]{border:2px solid #cacaca}.create-collector-task[_ngcontent-%COMP%]   .cloud-icon[_ngcontent-%COMP%]{font-size:4rem;color:#b8b7b5;display:flex;align-items:center;justify-content:center}.create-collector-task[_ngcontent-%COMP%]   .upload-btn[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff;font-weight:400;margin-bottom:1%;width:7rem;text-align:center;overflow:hidden;height:30px;font-size:12px;line-height:10px;text-overflow:ellipsis;white-space:nowrap}.create-collector-task[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]{display:flex;line-height:26px;border-radius:50%;cursor:context-menu!important;width:26px;height:26px;background-color:#fbfbfb;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.create-collector-task[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#cf0001;font-size:18px;margin-top:3px!important;margin-left:4px!important}.create-collector-task[_ngcontent-%COMP%]   .normalFont[_ngcontent-%COMP%]{color:#181818;font-size:14px;font-weight:400;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.create-collector-task[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-flex{position:relative;height:2.5rem;width:100%}.create-collector-task[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding-bottom:8px!important}.create-collector-task[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-form-field-underline{bottom:0}.create-collector-task[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-form-field-infix{padding:.65em 0!important;border-top:.54375em solid transparent!important;font-size:13px}.create-collector-task[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-select-arrow{margin:0 4px!important}.create-collector-task[_ngcontent-%COMP%]   .is-disabled[_ngcontent-%COMP%]{pointer-events:none}.create-collector-task[_ngcontent-%COMP%]   .scroll-window[_ngcontent-%COMP%]{border:2px solid #cacaca;height:8rem;overflow:scroll;overflow-x:hidden;margin-top:10px}.create-collector-task[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]{line-height:8px;width:26px;height:26px;position:absolute;margin-right:5px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12);animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}.create-collector-task[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.create-collector-task[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.create-collector-task[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}.create-collector-task[_ngcontent-%COMP%]   .spinner-align[_ngcontent-%COMP%]{margin:auto;display:inline-block}.create-collector-task[_ngcontent-%COMP%]   .iconbtn[_ngcontent-%COMP%]{background-color:#c92020;color:#fff;padding:0;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.create-collector-task[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}.create-collector-task[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}"]}),e})(),$=(()=>{class e{}return e.\u0275mod=D["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=D["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[d.CommonModule,g.b,u.b,h.b,b.b,c.e,m.e,p.c,s.g,a.p,a.E,f.c,v.d,k.h,x.b,y.a,r.c],d.CommonModule,g.b,u.b,h.b,b.b,c.e,m.e,p.c,s.g,a.p,a.E,f.c,v.d,k.h,x.b,y.a,r.c]}),e})()}}]);