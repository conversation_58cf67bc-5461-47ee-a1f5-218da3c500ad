(window.webpackJsonp=window.webpackJsonp||[]).push([[829],{Jzeh:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var i=n("yuIm"),c=n("fXoL"),o=n("ofXK");function s(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"div",4),c["\u0275\u0275text"](1," Oops! You're not authorized to view this content. Contact your administrator for access. "),c["\u0275\u0275elementEnd"]())}function r(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"div",4),c["\u0275\u0275text"](1," KEBS System is unavailable, kindly try after sometime! "),c["\u0275\u0275elementEnd"]())}let a=(()=>{class e{constructor(){this.isRDSPeak=!1}ngOnInit(){this.isRDSPeak=null==i?void 0:i.is_rds_peak}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=c["\u0275\u0275defineComponent"]({type:e,selectors:[["app-access-denied"]],decls:5,vars:2,consts:[[1,"bg-container"],[1,"contents"],["src","https://assets.kebs.app/ATS-noAccess.png",1,"image-styles"],["class","message",4,"ngIf"],[1,"message"]],template:function(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"div",0),c["\u0275\u0275elementStart"](1,"div",1),c["\u0275\u0275element"](2,"img",2),c["\u0275\u0275template"](3,s,2,0,"div",3),c["\u0275\u0275template"](4,r,2,0,"div",3),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e&&(c["\u0275\u0275advance"](3),c["\u0275\u0275property"]("ngIf",!t.isRDSPeak),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",t.isRDSPeak))},directives:[o.NgIf],styles:[".bg-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:10px;align-items:center;justify-content:center;background-color:#fff;height:var(--dynamicAccessDeniedHeight)}.bg-container[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:500;color:#111434;text-align:center}.bg-container[_ngcontent-%COMP%]   .image-styles[_ngcontent-%COMP%]{height:200px;width:200px}.bg-container[_ngcontent-%COMP%]   .contents[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center}"]}),e})()},blun:function(e,t,n){"use strict";n.r(t),n.d(t,"OnboardingChecklistsModule",(function(){return we}));var i=n("ofXK"),c=n("tyNb"),o=n("mrSG"),s=n("XNiG"),r=n("1G5W"),a=n("xG9w"),l=n("3Pt+"),d=n("+rOU"),h=n("5+WD"),g=n("yuIm"),p=n("fXoL"),u=n("XNFG"),k=n("rQiX"),f=n("oyK8"),C=n("rDax"),m=n("0IaG"),v=n("Jzeh"),_=n("NFeN"),x=n("Qu3c"),O=n("vzmP"),w=n("vxfF"),M=n("f0Cb"),P=n("pEYl"),y=n("/rGH");const b=["triggerActivateChange"],S=["triggerTasksChecklist"];function E(e,t){1&e&&(p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275element"](1,"app-access-denied"),p["\u0275\u0275elementContainerEnd"]())}function T(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",23),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](2).switchToPreview()})),p["\u0275\u0275text"](1," Preview "),p["\u0275\u0275elementEnd"]()}}const V=function(e){return{"save-btn-disabled":e}};function D(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",24),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](2).saveChecklist()})),p["\u0275\u0275text"](1),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"](2);p["\u0275\u0275property"]("ngClass",p["\u0275\u0275pureFunction1"](2,V,e.isSaveDisabled||!e.currentChecklistTasks||0==e.currentChecklistTasks.length||e.checklistFormGroup.invalid)),p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" ","create"==e.currentView?"Create":"Update"," ")}}const I=function(e){return{"activate-btn-disabled":e}};function F(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",25,26),p["\u0275\u0275listener"]("click",(function(){p["\u0275\u0275restoreView"](e);const t=p["\u0275\u0275reference"](2),n=p["\u0275\u0275nextContext"](2),i=p["\u0275\u0275reference"](5);return n.openOverlay(t,i)})),p["\u0275\u0275text"](3),p["\u0275\u0275pipe"](4,"masterData"),p["\u0275\u0275elementStart"](5,"mat-icon",27),p["\u0275\u0275text"](6,"keyboard_arrow_down"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"](2);let t=null;p["\u0275\u0275property"]("ngClass",p["\u0275\u0275pureFunction1"](6,I,e.isSaveDisabled||!e.currentChecklistTasks||0==e.currentChecklistTasks.length||e.checklistFormGroup.invalid)),p["\u0275\u0275advance"](3),p["\u0275\u0275textInterpolate1"](" ",p["\u0275\u0275pipeBind3"](4,2,null==(t=e.checklistFormGroup.get("checklist_status"))?null:t.value,e.checklistStatusMasterData,"display_name")," ")}}function A(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",32),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](3).addNewChecklist()})),p["\u0275\u0275text"](1," + New Checklist "),p["\u0275\u0275elementEnd"]()}}const j=function(e,t){return[e,t,0,0,"C"]};function L(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",28),p["\u0275\u0275elementStart"](1,"div",29),p["\u0275\u0275elementStart"](2,"div",9),p["\u0275\u0275text"](3,"Checklists"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](4,"div",10),p["\u0275\u0275text"](5," Your existing checklists are listed below "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](6,"div",30),p["\u0275\u0275template"](7,A,2,0,"div",31),p["\u0275\u0275pipe"](8,"access"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](2);p["\u0275\u0275advance"](4),p["\u0275\u0275property"]("matTooltip","Your existing checklists are listed below"),p["\u0275\u0275advance"](3),p["\u0275\u0275property"]("ngIf",p["\u0275\u0275pipeBindV"](8,2,p["\u0275\u0275pureFunction2"](8,j,e.access.moduleId.settings,e.access.subModuleId.onboardingChecklistSettings))&&!e.isLoading)}}function z(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"app-list-view",36),p["\u0275\u0275listener"]("onScroll",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](3).onDataScroll()}))("onClick",(function(t){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](3).onClickRowData(t)})),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"](3);p["\u0275\u0275property"]("list",e.data)("fieldConfig",e.fieldConfig)("variant",2)("isCheckboxActive",!1)}}function G(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"div",37),p["\u0275\u0275element"](1,"img",38),p["\u0275\u0275elementStart"](2,"div",39),p["\u0275\u0275text"](3,"No Checklists Found"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]())}function N(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",37),p["\u0275\u0275elementStart"](1,"div",40),p["\u0275\u0275element"](2,"img",41),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](3,"div",42),p["\u0275\u0275elementStart"](4,"div",43),p["\u0275\u0275text"](5,"Loading..."),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](3);p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("src",e.uiTextConfig["UI-LOADING-001"],p["\u0275\u0275sanitizeUrl"])}}function H(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",33),p["\u0275\u0275template"](1,z,1,4,"app-list-view",34),p["\u0275\u0275template"](2,G,4,0,"div",35),p["\u0275\u0275template"](3,N,6,1,"div",35),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](2);p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!e.isLoading&&e.data&&e.data.length>0),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!(e.isLoading||e.data&&0!=e.data.length)),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",e.isLoading)}}function R(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",60),p["\u0275\u0275elementStart"](1,"div",61),p["\u0275\u0275text"](2),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](3,"mat-icon",62),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](3).switchToChecklistNameEditMode()})),p["\u0275\u0275text"](4," edit "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"](3);let t=null,n=null;p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("matTooltip",null==(t=e.checklistFormGroup.get("checklist"))?null:t.value),p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" ",null==(n=e.checklistFormGroup.get("checklist_name"))?null:n.value," ")}}function B(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",63),p["\u0275\u0275elementStart"](1,"input",64),p["\u0275\u0275listener"]("keydown.enter",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](3).switchToChecklistNameViewMode()})),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](2,"mat-icon",65),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](3).switchToChecklistNameViewMode()})),p["\u0275\u0275text"](3," check_circle "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}2&e&&(p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("id","#checklistNameInput"))}function U(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",66),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](3).resetChecklist()})),p["\u0275\u0275text"](1," Reset All "),p["\u0275\u0275elementEnd"]()}}function W(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275elementStart"](1,"div",67),p["\u0275\u0275elementStart"](2,"div",57),p["\u0275\u0275text"](3),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](4,"div",68),p["\u0275\u0275elementStart"](5,"div",69),p["\u0275\u0275elementStart"](6,"div",70),p["\u0275\u0275elementStart"](7,"mat-icon",27),p["\u0275\u0275text"](8,"drag_indicator"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](9,"div",71),p["\u0275\u0275text"](10),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](11,"mat-icon",72),p["\u0275\u0275listener"]("click",(function(){p["\u0275\u0275restoreView"](e);const n=t.index;return p["\u0275\u0275nextContext"](3).removeAddedTask(n)})),p["\u0275\u0275text"](12," close "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=t.$implicit,n=t.index;p["\u0275\u0275advance"](3),p["\u0275\u0275textInterpolate1"]("Step ",n+1,""),p["\u0275\u0275advance"](6),p["\u0275\u0275property"]("matTooltip",e.task_name),p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" ",e.task_name," ")}}function Y(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",44),p["\u0275\u0275elementStart"](1,"div",45),p["\u0275\u0275elementStart"](2,"div",46),p["\u0275\u0275template"](3,R,5,2,"div",47),p["\u0275\u0275template"](4,B,4,1,"div",48),p["\u0275\u0275elementStart"](5,"div",49),p["\u0275\u0275text"](6," Enter the name of your checklist above "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](7,"div",50),p["\u0275\u0275template"](8,U,2,0,"div",51),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](9,"div",52),p["\u0275\u0275elementStart"](10,"div",53),p["\u0275\u0275text"](11,"Add Tasks To Checklist"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](12,"div",54),p["\u0275\u0275listener"]("cdkDropListDropped",(function(t){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](2).dragDropTasks(t)})),p["\u0275\u0275template"](13,W,13,3,"ng-container",55),p["\u0275\u0275elementStart"](14,"div",56),p["\u0275\u0275elementStart"](15,"div",57),p["\u0275\u0275text"](16),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](17,"div",58,59),p["\u0275\u0275listener"]("click",(function(){p["\u0275\u0275restoreView"](e);const t=p["\u0275\u0275nextContext"](2),n=p["\u0275\u0275reference"](7);return t.openTasksDialog(n)})),p["\u0275\u0275text"](20," + Add Tasks "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"](2);p["\u0275\u0275property"]("formGroup",e.checklistFormGroup),p["\u0275\u0275advance"](3),p["\u0275\u0275property"]("ngIf",!e.isChecklistNameEditInProgress),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",e.isChecklistNameEditInProgress),p["\u0275\u0275advance"](4),p["\u0275\u0275property"]("ngIf",("create"==e.currentView||"edit"==e.currentView)&&e.currentChecklistTasks.length>0),p["\u0275\u0275advance"](5),p["\u0275\u0275property"]("ngForOf",e.currentChecklistTasks),p["\u0275\u0275advance"](3),p["\u0275\u0275textInterpolate1"](" Step ",e.currentChecklistTasks.length+1," ")}}function q(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",73),p["\u0275\u0275elementStart"](1,"div",74),p["\u0275\u0275text"](2),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](3,"div",75),p["\u0275\u0275text"](4),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](2);p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("matTooltip","preview"==e.previousView?null==e.editChecklistData?null:e.editChecklistData.checklist_name:e.checklistFormGroup.get("checklist_name").value),p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" ","preview"==e.previousView?null==e.editChecklistData?null:e.editChecklistData.checklist_name:e.checklistFormGroup.get("checklist_name").value," "),p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate2"](" ",e.currentChecklistTasks.length," Task",e.currentChecklistTasks.length>1?"s":""," ")}}function X(e,t){1&e&&p["\u0275\u0275element"](0,"mat-divider",76)}const K=function(e){return{height:e}};function Q(e,t){if(1&e&&p["\u0275\u0275element"](0,"div",83),2&e){const e=p["\u0275\u0275nextContext"]().index,t=p["\u0275\u0275nextContext"](3);p["\u0275\u0275property"]("ngStyle",p["\u0275\u0275pureFunction1"](1,K,t.calculateHeightOfDivider(e)))}}function Z(e,t){if(1&e&&(p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275elementStart"](1,"div",78),p["\u0275\u0275elementStart"](2,"mat-icon",79),p["\u0275\u0275text"](3,"check_circle"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](4,"div",8),p["\u0275\u0275elementStart"](5,"div",80),p["\u0275\u0275text"](6),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](7,"div",81),p["\u0275\u0275text"](8),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](9,Q,1,3,"div",82),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit,n=t.index,i=p["\u0275\u0275nextContext"](3);p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("id","single-task-"+n),p["\u0275\u0275advance"](5),p["\u0275\u0275textInterpolate1"]("Step - ",n+1,""),p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate"](e.task_name),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",i.currentChecklistTasks.length-1!=n)}}function J(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",77),p["\u0275\u0275template"](1,Z,10,4,"ng-container",55),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](2);p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngForOf",e.currentChecklistTasks)}}function $(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",84),p["\u0275\u0275elementStart"](1,"div",40),p["\u0275\u0275element"](2,"img",41),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](3,"div",42),p["\u0275\u0275elementStart"](4,"div",43),p["\u0275\u0275text"](5,"Loading..."),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](2);p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("src",e.uiTextConfig["UI-LOADING-001"],p["\u0275\u0275sanitizeUrl"])}}const ee=function(e){return{"checklist-container-preview":e}};function te(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275elementStart"](1,"div",4),p["\u0275\u0275elementStart"](2,"div",5),p["\u0275\u0275elementStart"](3,"div",6),p["\u0275\u0275elementStart"](4,"mat-icon",7),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"]().goBackToPreviousRoute()})),p["\u0275\u0275text"](5," chevron_left "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](6,"div",8),p["\u0275\u0275elementStart"](7,"div",9),p["\u0275\u0275text"](8),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](9,"div",10),p["\u0275\u0275text"](10),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](11,"div",11),p["\u0275\u0275template"](12,T,2,0,"div",12),p["\u0275\u0275template"](13,D,2,4,"div",13),p["\u0275\u0275template"](14,F,7,8,"div",14),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](15,"div",15),p["\u0275\u0275template"](16,L,9,11,"div",16),p["\u0275\u0275template"](17,H,4,3,"div",17),p["\u0275\u0275template"](18,Y,21,6,"div",18),p["\u0275\u0275template"](19,q,5,4,"div",19),p["\u0275\u0275template"](20,X,1,0,"mat-divider",20),p["\u0275\u0275template"](21,J,2,1,"div",21),p["\u0275\u0275template"](22,$,6,1,"div",22),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"]();p["\u0275\u0275advance"](8),p["\u0275\u0275textInterpolate1"](" ","view"==e.currentView?"Checklist Creation":"create"==e.currentView?"New Checklist":"edit"==e.currentView?"Edit Checklist":"Checklist Preview"," "),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("matTooltip","view"==e.currentView?"Create a bundle of tasks as a single checklist to assign to candidates":"edit"==e.currentView||"create"==e.currentView?"Create your own checklists based on your requirements from the list of tasks and assign to the candidates":"View your created checklist"),p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" ","view"==e.currentView?"Create a bundle of tasks as a single checklist to assign to candidates":"edit"==e.currentView||"create"==e.currentView?"Create your own checklists based on your requirements from the list of tasks and assign to the candidates":"View your created checklist"," "),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngIf",("create"==e.currentView||"edit"==e.currentView)&&!e.isLoading),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",("create"==e.currentView||"edit"==e.currentView)&&!e.isLoading),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf","edit"==e.currentView&&!e.isLoading),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngClass",p["\u0275\u0275pureFunction1"](14,ee,"preview"==e.currentView&&!e.isLoading)),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf","view"==e.currentView),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf","view"==e.currentView),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",("create"==e.currentView||"edit"==e.currentView)&&!e.isLoading),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf","preview"==e.currentView&&!e.isLoading),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf","preview"==e.currentView&&!e.isLoading),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf","preview"==e.currentView&&!e.isLoading),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",e.isLoading&&"view"!=e.currentView)}}function ne(e,t){1&e&&p["\u0275\u0275element"](0,"mat-divider",90)}const ie=function(e){return{background:e}};function ce(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275elementStart"](1,"div",86),p["\u0275\u0275listener"]("click",(function(){p["\u0275\u0275restoreView"](e);const n=t.$implicit;return p["\u0275\u0275nextContext"](2).onChecklistStatusChange(n.id)})),p["\u0275\u0275element"](2,"div",87),p["\u0275\u0275elementStart"](3,"div",88),p["\u0275\u0275text"](4),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](5,ne,1,0,"mat-divider",89),p["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=t.$implicit,n=t.index,i=p["\u0275\u0275nextContext"](2);let c=null;p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngStyle",p["\u0275\u0275pureFunction1"](4,ie,e.id==(null==(c=i.checklistFormGroup.get("checklist_status"))?null:c.value)?e.bg_color:"#fff")),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngStyle",p["\u0275\u0275pureFunction1"](6,ie,e.color)),p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate"](e.display_name),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",i.checklistStatusMasterData.length-1!=n)}}function oe(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",85),p["\u0275\u0275template"](1,ce,6,8,"ng-container",55),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"]();p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngForOf",e.checklistStatusMasterData)}}function se(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"app-list-view",97),p["\u0275\u0275listener"]("onCheckboxValueChanges",(function(t){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](2).onSingleCheckboxValueChanges(t)}))("onChangeToggleAll",(function(t){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](2).onChangeToggleAll(t)})),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"](2);p["\u0275\u0275property"]("list",e.currentActiveTasksData)("fieldConfig",e.taskFieldConfig)("variant",3)("bulkSelectActive",!0)("isAllChecked",e.isAllChecked)}}function re(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",98),p["\u0275\u0275elementStart"](1,"div",40),p["\u0275\u0275element"](2,"img",41),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](3,"div",42),p["\u0275\u0275elementStart"](4,"div",43),p["\u0275\u0275text"](5,"Loading..."),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](2);p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("src",e.uiTextConfig["UI-LOADING-001"],p["\u0275\u0275sanitizeUrl"])}}const ae=function(e){return{visibility:e}};function le(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",91),p["\u0275\u0275elementStart"](1,"div",88),p["\u0275\u0275text"](2,"Choose tasks to form a checklist"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](3,se,1,5,"app-list-view",92),p["\u0275\u0275template"](4,re,6,1,"div",93),p["\u0275\u0275elementStart"](5,"div",94),p["\u0275\u0275elementStart"](6,"div",95),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"]().closeTasksDialog()})),p["\u0275\u0275text"](7,"Cancel"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](8,"div",96),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"]().addTasksFromDialog()})),p["\u0275\u0275text"](9),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"]();p["\u0275\u0275advance"](3),p["\u0275\u0275property"]("ngIf",!e.isActiveTaskLoading),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",e.isActiveTaskLoading),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngStyle",p["\u0275\u0275pureFunction1"](4,ae,e.isActiveTaskLoading?"hidden":"")),p["\u0275\u0275advance"](4),p["\u0275\u0275textInterpolate1"](" Add (",e.currentSelectedChecklistTasksCount,") ")}}const de=function(e,t){return[e,t,0,0,"V"]},he=[{path:"",component:(()=>{class e{constructor(e,t,n,i,c,o,r,a){this._toaster=e,this._masterDataService=t,this._onboardingSettingService=n,this._fb=i,this._overlay=c,this._viewContainerRef=o,this._dialog=r,this._el=a,this._onDestroy=new s.b,this.access=g,this.skip=0,this.limit=15,this.data=[],this.activeTasksData=[],this.currentActiveTasksData=[],this.fieldConfig=[],this.taskFieldConfig=[],this.uiTextConfig={},this.isLoading=!0,this.isSaveDisabled=!1,this.isChecklistNameEditInProgress=!1,this.isAllChecked=!1,this.isActiveTaskLoading=!1,this.currentView="view",this.previousView="view",this.editChecklistData={},this.checklistStatusMasterData=[],this.currentChecklistTasks=[],this.currentSelectedChecklistTasks=[],this.currentSelectedChecklistTasksCount=0,this.checklistFormGroup=this._fb.group({})}ngOnInit(){return Object(o.c)(this,void 0,void 0,(function*(){this.calculateDynamicContentHeight(),yield this.getAtsMasterUiConfig("onboardingChecklistSettings"),this.retrieveOnboardingChecklistStatusMaster(),this.retrieveAllActiveTasksForChecklist(),yield this.retrieveAllOnboardingChecklists(!0)}))}onResize(){this.calculateDynamicContentHeight()}calculateDynamicContentHeight(){document.documentElement.style.setProperty("--dynamicHeight",window.innerHeight-57+"px"),document.documentElement.style.setProperty("--dynamicSubHeight",window.innerHeight-57-162+"px"),document.documentElement.style.setProperty("--dynamicAccessDeniedHeight",window.innerHeight-57+"px")}goBackToPreviousRoute(){"view"==this.currentView?history.back():"create"==this.currentView||"edit"==this.currentView?(this.previousView=this.currentView,this.currentView="view",this.retrieveAllOnboardingChecklists(!0)):"preview"==this.currentView&&(this.currentView=this.previousView)}onClickRowData(e){return Object(o.c)(this,void 0,void 0,(function*(){e.functionName&&""!=e.functionName&&(yield this[e.functionName](e.data))}))}switchToPreview(e){return Object(o.c)(this,void 0,void 0,(function*(){if("create"==this.currentView||"edit"==this.currentView){if(this.checklistFormGroup.invalid)return this._toaster.showWarning("Warning \u26a0\ufe0f","Kindly fill all mandatory fields to view the preview!",7e3);if(!this.currentChecklistTasks||0==this.currentChecklistTasks.length)return this._toaster.showWarning("Warning \u26a0\ufe0f","Atleast one task is mandatory to view the preview!",7e3);this.previousView=this.currentView,this.currentView="preview"}else"view"==this.currentView&&(this.isLoading=!0,this.currentView="preview",this.previousView="view",yield this.retrieveSingleOnboardingChecklistData({_id:null==e?void 0:e._id}),yield this.patchForm(),this.isLoading=!1)}))}onEditChecklist(e){return Object(o.c)(this,void 0,void 0,(function*(){return g.checkAccessForGeneralRole(g.moduleId.settings,g.subModuleId.onboardingChecklistSettings,0,0,"E")?(null==e?void 0:e.isEditAllowed)?(this.isLoading=!0,this.isSaveDisabled=!0,this.previousView=this.currentView,this.currentView="edit",yield this.retrieveSingleOnboardingChecklistData({_id:null==e?void 0:e._id}),yield this.patchForm(),this.isLoading=!1,void(this.isSaveDisabled=!1)):this._toaster.showInfo("Checklist Already Assigned!","Oops! This checklist is already assigned to a candidate and cannot be edited.",7e3):this._toaster.showInfo("Access Restricted!","Oops! You're not authorized to view this content. Contact your administrator for access.",7e3)}))}addNewChecklist(){return Object(o.c)(this,void 0,void 0,(function*(){yield this.createForm(),this.previousView=this.currentView,this.currentView="create",this.switchToChecklistNameEditMode()}))}saveChecklist(){var e,t;if(this.checklistFormGroup.invalid)return this._toaster.showWarning("Warning \u26a0\ufe0f","Kindly Fill All Mandatory Fields!",7e3);if(!this.currentChecklistTasks||0==this.currentChecklistTasks.length)return this._toaster.showWarning("Warning \u26a0\ufe0f","Atleast one task is mandatory!",7e3);if("create"==this.currentView){let e={data:{checklist_name:this.checklistFormGroup.get("checklist_name").value,checklist_status:this.checklistFormGroup.get("checklist_status").value,tasks:this.currentChecklistTasks}};this.createAndUpdateOnboardingChecklist(e)}else if("edit"==this.currentView){let n={data:{_id:null===(e=this.editChecklistData)||void 0===e?void 0:e._id,id:null===(t=this.editChecklistData)||void 0===t?void 0:t.id,checklist_name:this.checklistFormGroup.get("checklist_name").value,checklist_status:this.checklistFormGroup.get("checklist_status").value,tasks:this.currentChecklistTasks}};this.createAndUpdateOnboardingChecklist(n)}}displaySuccessMessage(){return Object(o.c)(this,void 0,void 0,(function*(){const{SuccessMsgComponent:e}=yield n.e(940).then(n.bind(null,"oA7u"));this._dialog.open(e,{data:{icon:'<svg width="35" height="34" viewBox="0 0 35 34" fill="none"><path d="M15.1673 24.6654L26.9173 12.9154L24.584 10.582L15.1673 19.9987L10.4173 15.2487L8.08398 17.582L15.1673 24.6654ZM17.5007 33.6654C15.1951 33.6654 13.0284 33.2279 11.0007 32.3529C8.97287 31.4779 7.20898 30.2904 5.70898 28.7904C4.20898 27.2904 3.02148 25.5265 2.14648 23.4987C1.27148 21.4709 0.833984 19.3043 0.833984 16.9987C0.833984 14.6931 1.27148 12.5265 2.14648 10.4987C3.02148 8.47092 4.20898 6.70703 5.70898 5.20703C7.20898 3.70703 8.97287 2.51953 11.0007 1.64453C13.0284 0.769531 15.1951 0.332031 17.5007 0.332031C19.8062 0.332031 21.9729 0.769531 24.0007 1.64453C26.0284 2.51953 27.7923 3.70703 29.2923 5.20703C30.7923 6.70703 31.9798 8.47092 32.8548 10.4987C33.7298 12.5265 34.1673 14.6931 34.1673 16.9987C34.1673 19.3043 33.7298 21.4709 32.8548 23.4987C31.9798 25.5265 30.7923 27.2904 29.2923 28.7904C27.7923 30.2904 26.0284 31.4779 24.0007 32.3529C21.9729 33.2279 19.8062 33.6654 17.5007 33.6654Z" fill="#79BA44"/></svg>',text:"Your checklist has been",boldText:"Activated Successfully."},disableClose:!0})}))}calculateHeightOfDivider(e){const t=this._el.nativeElement.querySelector("#single-task-"+e);if(t)return t.offsetHeight-16+"px"}resetChecklist(){this.currentChecklistTasks=[]}removeAddedTask(e){this.currentChecklistTasks.splice(e,1)}openTasksDialog(e){var t;return Object(o.c)(this,void 0,void 0,(function*(){if(!this.activeTasksData||0==this.activeTasksData.length)return this._toaster.showInfo("Info \ud83d\udcdd","No Active Tasks Found!",7e3);if(this.isActiveTaskLoading=!0,!(null===(t=this.overlayRef)||void 0===t?void 0:t.hasAttached())){const t=this._overlay.position().global().centerHorizontally().centerVertically(),n=this._overlay.scrollStrategies.block();this.overlayRef=this._overlay.create({positionStrategy:t,scrollStrategy:n,hasBackdrop:!0,backdropClass:"",panelClass:["pop-up"],width:"80%"});const i=new d.h(e,this._viewContainerRef);this.overlayRef.attach(i),this.overlayRef.backdropClick().subscribe(()=>{this.closeOverlay()})}this.currentChecklistTasks.forEach(e=>{e.isChecked=!0}),this.currentSelectedChecklistTasks=[...this.currentChecklistTasks];let n=a.pluck(this.currentChecklistTasks,"_id"),i=this.activeTasksData.filter(e=>!n.includes(e._id));i.forEach(e=>{e.isChecked=!1}),this.currentActiveTasksData=[...this.currentChecklistTasks,...i],this.currentSelectedChecklistTasksCount=this.currentActiveTasksData.filter(e=>e.isChecked).length,this.isAllChecked=this.currentSelectedChecklistTasksCount==this.currentActiveTasksData.length,this.isActiveTaskLoading=!1}))}closeTasksDialog(){this.closeOverlay(),this.currentSelectedChecklistTasks=[],this.currentSelectedChecklistTasksCount=0}addTasksFromDialog(){this.currentChecklistTasks=this.currentSelectedChecklistTasks,this.closeTasksDialog()}dragDropTasks(e){Object(h.h)(this.currentChecklistTasks,e.previousIndex,e.currentIndex)}onSingleCheckboxValueChanges(e){this.currentSelectedChecklistTasks=e.selectedDetails,this.currentSelectedChecklistTasksCount=e.selectedDetails.length,this.currentSelectedChecklistTasksCount||(this.isAllChecked=!1)}onChangeToggleAll(e){this.isAllChecked=e,this.currentActiveTasksData.forEach(e=>e.isChecked=this.isAllChecked);let t=this.currentActiveTasksData.filter(e=>1==e.isChecked);this.currentSelectedChecklistTasks=t,this.currentSelectedChecklistTasksCount=t.length}onChecklistStatusChange(e){var t,i;return Object(o.c)(this,void 0,void 0,(function*(){if("create"==this.currentView)this.checklistFormGroup.get("checklist_status").setValue(e),this.closeOverlay();else if("edit"==this.currentView){if((null===(t=this.checklistFormGroup.get("checklist_status"))||void 0===t?void 0:t.value)==e)return void this.closeOverlay();if(2==e)this.checklistFormGroup.get("checklist_status").setValue(e),this.updateOnboardingChecklistStatus({_id:null===(i=this.editChecklistData)||void 0===i?void 0:i._id,checklist_status:e}),this.closeOverlay();else if(3==e){this.closeOverlay(),this.checklistFormGroup.get("checklist_status").setValue(e);const{DynamicContentDialogComponent:t}=yield Promise.all([n.e(0),n.e(935)]).then(n.bind(null,"3KYs"));this._dialog.open(t,{width:"450px",data:{title:"Are You Sure You Want To Deactivate This Checklist ?",content:'Once you click on "Deactivate," this checklist will be disabled, and team members will no longer be able to make updates.',yesBtnText:"Deactivate",isIconVisible:!0,svg:'<svg width="40" height="40" viewBox="0 0 40 40" fill="none">\n                  <g clip-path="url(#clip0_25217_158403)">\n                  <path d="M6.66927 13.332H33.3359V34.9987C33.3359 35.4407 33.1603 35.8647 32.8478 36.1772C32.5352 36.4898 32.1113 36.6654 31.6693 36.6654H8.33594C7.89391 36.6654 7.46999 36.4898 7.15743 36.1772C6.84487 35.8647 6.66927 35.4407 6.66927 34.9987V13.332ZM11.6693 8.33203V4.9987C11.6693 4.55667 11.8449 4.13275 12.1574 3.82019C12.47 3.50763 12.8939 3.33203 13.3359 3.33203H26.6693C27.1113 3.33203 27.5352 3.50763 27.8478 3.82019C28.1603 4.13275 28.3359 4.55667 28.3359 4.9987V8.33203H36.6693V11.6654H3.33594V8.33203H11.6693ZM15.0026 6.66536V8.33203H25.0026V6.66536H15.0026ZM15.0026 19.9987V29.9987H18.3359V19.9987H15.0026ZM21.6693 19.9987V29.9987H25.0026V19.9987H21.6693Z" fill="#1F2347"/>\n                  </g>\n                  <defs>\n                  <clipPath id="clip0_25217_158403">\n                  <rect width="40" height="40" fill="white"/>\n                  </clipPath>\n                  </defs>\n                  </svg>\n                 '},disableClose:!0}).afterClosed().subscribe(t=>Object(o.c)(this,void 0,void 0,(function*(){var n;t?this.updateOnboardingChecklistStatus({_id:null===(n=this.editChecklistData)||void 0===n?void 0:n._id,checklist_status:e}):this.checklistFormGroup.get("checklist_status").setValue(2)})))}}}))}createForm(){return Object(o.c)(this,void 0,void 0,(function*(){this.checklistFormGroup=this._fb.group({}),this.checklistFormGroup.addControl("checklist_name",new l.j(null,[l.H.required])),this.checklistFormGroup.addControl("checklist_status",new l.j(2,[l.H.required])),this.currentChecklistTasks=[]}))}patchForm(){var e,t,n;return Object(o.c)(this,void 0,void 0,(function*(){this.checklistFormGroup=this._fb.group({}),this.checklistFormGroup.addControl("checklist_name",new l.j(null===(e=this.editChecklistData)||void 0===e?void 0:e.checklist_name,[l.H.required])),this.checklistFormGroup.addControl("checklist_status",new l.j(null===(t=this.editChecklistData)||void 0===t?void 0:t.checklist_status,[l.H.required])),this.currentChecklistTasks=(null===(n=this.editChecklistData)||void 0===n?void 0:n.tasks)||[],this.isAllChecked=this.currentChecklistTasks.length==this.activeTasksData.length}))}openOverlay(e,t){var n;if(!(null===(n=this.overlayRef)||void 0===n?void 0:n.hasAttached())){const n=this._overlay.position().flexibleConnectedTo(e).withFlexibleDimensions(!0).withPush(!0).withViewportMargin(10).withGrowAfterOpen(!0).withPositions([{originX:"center",originY:"bottom",overlayX:"center",overlayY:"top"}]);n.withDefaultOffsetY(5);const i=this._overlay.scrollStrategies.close();this.overlayRef=this._overlay.create({positionStrategy:n,scrollStrategy:i,hasBackdrop:!0,backdropClass:"",panelClass:["pop-up"]});const c=new d.h(t,this._viewContainerRef);this.overlayRef.attach(c),this.overlayRef.backdropClick().subscribe(()=>{this.closeOverlay()})}}closeOverlay(){var e;null===(e=this.overlayRef)||void 0===e||e.dispose()}switchToChecklistNameEditMode(){this.isChecklistNameEditInProgress=!0,setTimeout(e=>{const t=document.getElementById("#checklistNameInput");t.focus(),t.select()},50)}switchToChecklistNameViewMode(){var e,t,n;if(this.checklistFormGroup.get("checklist_name").invalid||!this.checklistFormGroup.get("checklist_name").value||0==(null===(n=null===(t=null===(e=this.checklistFormGroup.get("checklist_name"))||void 0===e?void 0:e.value)||void 0===t?void 0:t.trim())||void 0===n?void 0:n.length))return this._toaster.showWarning("Warning \u26a0\ufe0f","Invalid Checklist Name!",7e3);this.isChecklistNameEditInProgress=!1}onDataScroll(){this.skip+=this.limit,this.retrieveAllOnboardingChecklists(!1)}getAtsMasterUiConfig(e){return Object(o.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._masterDataService.getAtsMasterUiConfig(e).pipe(Object(r.a)(this._onDestroy)).subscribe({next:n=>{0==n.err?"onboardingChecklistSettings"==e&&(this.fieldConfig=n.data.fieldConfig,this.taskFieldConfig=n.data.taskFieldConfig,this.uiTextConfig=n.data.uiTextConfig):this._toaster.showError("Error",n.msg,7e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Master Data Retrieval Failed!",7e3),n()}}))}))}retrieveAllOnboardingChecklists(e){return Object(o.c)(this,void 0,void 0,(function*(){e&&(this.skip=0,this.isLoading=!0,this.data=[]);let t={skip:this.skip,limit:this.limit};return new Promise((e,n)=>this._onboardingSettingService.retrieveAllOnboardingChecklists(t).pipe(Object(r.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.data=[...this.data,...t.data]:this._toaster.showError("Error",t.msg,7e3),this.isLoading=!1,e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Onboarding Checklists Data Retrieval Failed!",7e3),this.isLoading=!1,n()}}))}))}retrieveOnboardingChecklistStatusMaster(){return Object(o.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._onboardingSettingService.retrieveOnboardingChecklistStatusMaster({}).pipe(Object(r.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.checklistStatusMasterData=t.data.filter(e=>1!=e.id):this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Onboarding Checklist Status Data Retrieval Failed!",7e3),t()}}))}))}createAndUpdateOnboardingChecklist(e){return Object(o.c)(this,void 0,void 0,(function*(){return this.isSaveDisabled=!0,new Promise((t,n)=>this._onboardingSettingService.createAndUpdateOnboardingChecklist(e).pipe(Object(r.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?(this._toaster.showSuccess("Success \u2705",e.msg,7e3),"create"==this.currentView&&this.goBackToPreviousRoute()):this._toaster.showError("Error",e.msg,7e3),this.isSaveDisabled=!1,t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Checklist API Failed!",7e3),this.isSaveDisabled=!1,n()}}))}))}retrieveSingleOnboardingChecklistData(e){return Object(o.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._onboardingSettingService.retrieveSingleOnboardingChecklistData(e).pipe(Object(r.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?this.editChecklistData=e.data:this._toaster.showError("Error",e.msg,7e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Checklist Retrieval Failed!",7e3),n()}}))}))}updateOnboardingChecklistStatus(e){return Object(o.c)(this,void 0,void 0,(function*(){return this.isSaveDisabled=!0,this.isLoading=!0,new Promise((t,n)=>this._onboardingSettingService.updateOnboardingChecklistStatus(e).pipe(Object(r.a)(this._onDestroy)).subscribe({next:n=>{0==n.err?(2==(null==e?void 0:e.checklist_status)?(this.displaySuccessMessage(),this.checklistFormGroup.get("checklist_status").setValue(2)):3==(null==e?void 0:e.checklist_status)&&(this._toaster.showSuccess("Success \u2705",n.msg,7e3),this.checklistFormGroup.get("checklist_status").setValue(3)),this.goBackToPreviousRoute()):this._toaster.showError("Error",n.msg,7e3),this.isSaveDisabled=!1,t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Checklist Status Updation Failed!",7e3),this.isSaveDisabled=!1,n()}}))}))}retrieveAllActiveTasksForChecklist(){return Object(o.c)(this,void 0,void 0,(function*(){return this.isActiveTaskLoading=!0,this.activeTasksData=[],this.currentActiveTasksData=[],new Promise((e,t)=>this._onboardingSettingService.retrieveAllActiveTasksForChecklist({}).pipe(Object(r.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?(this.activeTasksData=[...this.activeTasksData,...t.data],this.currentActiveTasksData=[...this.activeTasksData]):this._toaster.showError("Error",t.msg,7e3),this.isActiveTaskLoading=!1,e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Onboarding Active Tasks Data Retrieval Failed!",7e3),this.isActiveTaskLoading=!1,t()}}))}))}}return e.\u0275fac=function(t){return new(t||e)(p["\u0275\u0275directiveInject"](u.a),p["\u0275\u0275directiveInject"](k.a),p["\u0275\u0275directiveInject"](f.a),p["\u0275\u0275directiveInject"](l.i),p["\u0275\u0275directiveInject"](C.e),p["\u0275\u0275directiveInject"](p.ViewContainerRef),p["\u0275\u0275directiveInject"](m.b),p["\u0275\u0275directiveInject"](p.ElementRef))},e.\u0275cmp=p["\u0275\u0275defineComponent"]({type:e,selectors:[["app-landing-page"]],viewQuery:function(e,t){if(1&e&&(p["\u0275\u0275viewQuery"](b,!0),p["\u0275\u0275viewQuery"](S,!0)),2&e){let e;p["\u0275\u0275queryRefresh"](e=p["\u0275\u0275loadQuery"]())&&(t.triggerActivateChange=e.first),p["\u0275\u0275queryRefresh"](e=p["\u0275\u0275loadQuery"]())&&(t.triggerTasksChecklist=e.first)}},hostBindings:function(e,t){1&e&&p["\u0275\u0275listener"]("resize",(function(){return t.onResize()}),!1,p["\u0275\u0275resolveWindow"])},decls:8,vars:22,consts:[[4,"ngIf"],["cdkConnectedOverlay","",3,"cdkConnectedOverlayOrigin"],["triggerActivateChange",""],["triggerTasksChecklist",""],[1,"bg-container"],[1,"header"],[1,"header-1"],[1,"back-icon",3,"click"],[1,"content"],[1,"content-title"],[1,"content-sub-title",3,"matTooltip"],[1,"header-2"],["class","preview",3,"click",4,"ngIf"],["class","save-btn",3,"ngClass","click",4,"ngIf"],["class","activate-btn","cdkOverlayOrigin","",3,"ngClass","click",4,"ngIf"],[1,"checklist-container",3,"ngClass"],["class","checklist-header",4,"ngIf"],["class","checklist-list",4,"ngIf"],["class","form",3,"formGroup",4,"ngIf"],["class","checklist-preview-header",4,"ngIf"],["class","checklist-preview-divider",4,"ngIf"],["class","checklist-preview-content",4,"ngIf"],["class","loading-state-ui",4,"ngIf"],[1,"preview",3,"click"],[1,"save-btn",3,"ngClass","click"],["cdkOverlayOrigin","",1,"activate-btn",3,"ngClass","click"],["triggerActivate","cdkOverlayOrigin","triggerActivateField",""],[1,"icon"],[1,"checklist-header"],[1,"checklist-header-1"],[1,"checklist-header-2"],["class","add-checklist",3,"click",4,"ngIf"],[1,"add-checklist",3,"click"],[1,"checklist-list"],[3,"list","fieldConfig","variant","isCheckboxActive","onScroll","onClick",4,"ngIf"],["class","checklist-list-empty",4,"ngIf"],[3,"list","fieldConfig","variant","isCheckboxActive","onScroll","onClick"],[1,"checklist-list-empty"],["src","https://assets.kebs.app/ATS-Empty-State.png"],[1,"no-data-title"],[2,"max-width","60px","max-height","60px"],[2,"max-width","100%","max-height","100%",3,"src"],[1,"loading-wrapper"],[1,"loading"],[1,"form",3,"formGroup"],[1,"create-checklist-header"],[1,"create-checklist-header-1"],["class","view-checklist",4,"ngIf"],["class","edit-checklist",4,"ngIf"],[1,"checklist-name-desc"],[1,"create-checklist-header-2"],["class","reset",3,"click",4,"ngIf"],["cdkScrollable","",1,"create-checklist-content"],[1,"checklist-title"],["cdkScrollable","","cdkDropList","",1,"checklist-tasks",3,"cdkDropListDropped"],[4,"ngFor","ngForOf"],[1,"single-checklist-task"],[1,"step-index"],["cdkOverlayOrigin","",1,"add-task",3,"click"],["triggerAddTask","cdkOverlayOrigin","triggerAddTasksField",""],[1,"view-checklist"],[1,"checklist-name",3,"matTooltip"],[1,"edit-icon",3,"click"],[1,"edit-checklist"],["type","text","maxlength","75","formControlName","checklist_name","placeholder","Enter Checklist Name",3,"id","keydown.enter"],[1,"tick-icon",3,"click"],[1,"reset",3,"click"],["cdkDrag","",1,"single-checklist-task"],[1,"step-content"],[1,"step-data"],["cdkDragHandle","",1,"drag"],[1,"task-name",3,"matTooltip"],[1,"close-icon",3,"click"],[1,"checklist-preview-header"],[1,"header-title",3,"matTooltip"],[1,"task-count"],[1,"checklist-preview-divider"],[1,"checklist-preview-content"],[1,"single-task",3,"id"],[1,"check"],[1,"step-text"],[1,"task-text"],["class","space",3,"ngStyle",4,"ngIf"],[1,"space",3,"ngStyle"],[1,"loading-state-ui"],[1,"activation-overlay-container"],[1,"single-item",3,"ngStyle","click"],[1,"circle",3,"ngStyle"],[1,"text"],["class","divider",4,"ngIf"],[1,"divider"],[1,"active-tasks"],[3,"list","fieldConfig","variant","bulkSelectActive","isAllChecked","onCheckboxValueChanges","onChangeToggleAll",4,"ngIf"],["class","loading-ui",4,"ngIf"],[1,"buttons",3,"ngStyle"],[1,"cancel-btn",3,"click"],[1,"add-btn",3,"click"],[3,"list","fieldConfig","variant","bulkSelectActive","isAllChecked","onCheckboxValueChanges","onChangeToggleAll"],[1,"loading-ui"]],template:function(e,t){1&e&&(p["\u0275\u0275template"](0,E,2,0,"ng-container",0),p["\u0275\u0275pipe"](1,"access"),p["\u0275\u0275template"](2,te,23,16,"ng-container",0),p["\u0275\u0275pipe"](3,"access"),p["\u0275\u0275template"](4,oe,2,1,"ng-template",1,2,p["\u0275\u0275templateRefExtractor"]),p["\u0275\u0275template"](6,le,10,6,"ng-template",1,3,p["\u0275\u0275templateRefExtractor"])),2&e&&(p["\u0275\u0275property"]("ngIf",!p["\u0275\u0275pipeBindV"](1,4,p["\u0275\u0275pureFunction2"](16,de,t.access.moduleId.settings,t.access.subModuleId.onboardingChecklistSettings))),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngIf",p["\u0275\u0275pipeBindV"](3,10,p["\u0275\u0275pureFunction2"](19,de,t.access.moduleId.settings,t.access.subModuleId.onboardingChecklistSettings))),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("cdkConnectedOverlayOrigin",t.triggerActivate),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("cdkConnectedOverlayOrigin",t.triggerAddTask))},directives:[i.NgIf,C.a,v.a,_.a,x.a,i.NgClass,C.b,O.a,l.w,l.n,w.b,h.e,i.NgForOf,l.e,l.q,l.v,l.l,h.a,h.b,M.a,i.NgStyle],pipes:[P.a,y.a],styles:['.bg-container[_ngcontent-%COMP%]{background-color:#f1f3f8;height:var(--dynamicHeight)}.header[_ngcontent-%COMP%]{justify-content:space-between;background-color:#fff;padding:6px 16px;max-height:52px}.header[_ngcontent-%COMP%], .header[_ngcontent-%COMP%]   .header-1[_ngcontent-%COMP%]{display:flex;align-items:center}.header[_ngcontent-%COMP%]   .header-1[_ngcontent-%COMP%]{gap:16px;padding-left:24px;width:50%}.header[_ngcontent-%COMP%]   .header-1[_ngcontent-%COMP%]   .back-icon[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;font-size:18px;width:18px;height:18px;color:#526179;border-radius:4px;border:1px solid #526179;cursor:pointer}.header[_ngcontent-%COMP%]   .header-1[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:100%}.header[_ngcontent-%COMP%]   .header-1[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .content-title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:16px;font-weight:700;color:#111434}.header[_ngcontent-%COMP%]   .header-1[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .content-sub-title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:11px;font-weight:400;color:#8b95a5;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.header[_ngcontent-%COMP%]   .header-2[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px}.header[_ngcontent-%COMP%]   .header-2[_ngcontent-%COMP%]   .preview[_ngcontent-%COMP%]{text-decoration:underline}.header[_ngcontent-%COMP%]   .header-2[_ngcontent-%COMP%]   .preview[_ngcontent-%COMP%], .header[_ngcontent-%COMP%]   .header-2[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:700;color:#45546e;cursor:pointer}.header[_ngcontent-%COMP%]   .header-2[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%]{border:1px solid #45546e;border-radius:8px;padding:7px 11px}.header[_ngcontent-%COMP%]   .header-2[_ngcontent-%COMP%]   .save-btn-disabled[_ngcontent-%COMP%]{background:#b9c0ca;color:#fff;pointer-events:none;padding:8px 12px;border:none}.header[_ngcontent-%COMP%]   .header-2[_ngcontent-%COMP%]   .activate-btn[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px;font-family:var(--atsfontFamily);font-size:14px;font-weight:700;color:#fff;cursor:pointer;padding:8px 12px 8px 16px;background-color:var(--atsprimaryColor);border-radius:8px}.header[_ngcontent-%COMP%]   .header-2[_ngcontent-%COMP%]   .activate-btn[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{font-size:16px;width:16px;height:16px;color:#fff}.header[_ngcontent-%COMP%]   .header-2[_ngcontent-%COMP%]   .activate-btn-disabled[_ngcontent-%COMP%]{background:#b9c0ca;color:#fff;pointer-events:none;padding:8px 12px 8px 16px}.checklist-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;background-color:#fff;margin:16px;padding:16px}.checklist-container[_ngcontent-%COMP%]   .checklist-header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;max-height:52px}.checklist-container[_ngcontent-%COMP%]   .checklist-header[_ngcontent-%COMP%]   .checklist-header-1[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:50%}.checklist-container[_ngcontent-%COMP%]   .checklist-header[_ngcontent-%COMP%]   .checklist-header-1[_ngcontent-%COMP%]   .content-title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:16px;font-weight:500;color:#111434}.checklist-container[_ngcontent-%COMP%]   .checklist-header[_ngcontent-%COMP%]   .checklist-header-1[_ngcontent-%COMP%]   .content-sub-title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:11px;font-weight:400;color:#b9c0ca;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.checklist-container[_ngcontent-%COMP%]   .checklist-header[_ngcontent-%COMP%]   .checklist-header-2[_ngcontent-%COMP%]   .add-checklist[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:700;color:#fff;background-color:var(--atsprimaryColor);padding:8px 12px;border-radius:8px;cursor:pointer}.checklist-container[_ngcontent-%COMP%]   .checklist-list[_ngcontent-%COMP%]{padding:8px 0 0}.checklist-container[_ngcontent-%COMP%]   .checklist-list-empty[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:8px 0 0;height:var(--dynamicSubHeight)}.checklist-container[_ngcontent-%COMP%]   .checklist-list-empty[_ngcontent-%COMP%]   .no-data-title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:500;color:#1b2140}.checklist-container[_ngcontent-%COMP%]   .checklist-list-empty[_ngcontent-%COMP%]   .loading-wrapper[_ngcontent-%COMP%]{display:flex;vertical-align:center;justify-content:center;align-items:center}.checklist-container[_ngcontent-%COMP%]   .checklist-list-empty[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]{color:rgba(0,0,0,.3);font-size:16px}.checklist-container[_ngcontent-%COMP%]   .checklist-list-empty[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]:before{content:"Loading...";position:absolute;overflow:hidden;max-width:7em;white-space:nowrap;background:linear-gradient(270deg,var(--atsprimaryColor),var(--atssecondaryColor) 105.29%);background-clip:text;-webkit-background-clip:text;-webkit-text-fill-color:transparent;animation:loading 6s linear infinite}.checklist-container[_ngcontent-%COMP%]   .loading-state-ui[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;flex-direction:column;background-color:#fff;height:calc(var(--dynamicHeight) - 112px)}.checklist-container[_ngcontent-%COMP%]   .loading-state-ui[_ngcontent-%COMP%]   .no-data-title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:500;color:#1b2140}.checklist-container[_ngcontent-%COMP%]   .loading-state-ui[_ngcontent-%COMP%]   .loading-wrapper[_ngcontent-%COMP%]{display:flex;vertical-align:center;justify-content:center;align-items:center}.checklist-container[_ngcontent-%COMP%]   .loading-state-ui[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]{color:rgba(0,0,0,.3);font-size:16px}.checklist-container[_ngcontent-%COMP%]   .loading-state-ui[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]:before{content:"Loading...";position:absolute;overflow:hidden;max-width:7em;white-space:nowrap;background:linear-gradient(270deg,var(--atsprimaryColor),var(--atssecondaryColor) 105.29%);background-clip:text;-webkit-background-clip:text;-webkit-text-fill-color:transparent;animation:loading 6s linear infinite}.checklist-container[_ngcontent-%COMP%]   .checklist-preview-header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between}.checklist-container[_ngcontent-%COMP%]   .checklist-preview-header[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:500;color:#1b2140;max-width:70%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.checklist-container[_ngcontent-%COMP%]   .checklist-preview-header[_ngcontent-%COMP%]   .task-count[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:500;color:#7d838b}.checklist-container[_ngcontent-%COMP%]   .checklist-preview-divider[_ngcontent-%COMP%]{color:#dadce2;margin:16px 0}.checklist-container[_ngcontent-%COMP%]   .checklist-preview-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:24px;height:var(--dynamicSubHeight);overflow-y:auto}.checklist-container[_ngcontent-%COMP%]   .checklist-preview-content[_ngcontent-%COMP%]   .single-task[_ngcontent-%COMP%]{display:flex;align-items:start;position:relative;gap:8px}.checklist-container[_ngcontent-%COMP%]   .checklist-preview-content[_ngcontent-%COMP%]   .single-task[_ngcontent-%COMP%]   .check[_ngcontent-%COMP%]{font-size:24px;width:24px;height:24px;color:#b9c0ca;margin-top:2px}.checklist-container[_ngcontent-%COMP%]   .checklist-preview-content[_ngcontent-%COMP%]   .single-task[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{display:flex;flex-direction:column}.checklist-container[_ngcontent-%COMP%]   .checklist-preview-content[_ngcontent-%COMP%]   .single-task[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .step-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:12px;font-weight:400;color:#7d838b}.checklist-container[_ngcontent-%COMP%]   .checklist-preview-content[_ngcontent-%COMP%]   .single-task[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .task-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:400;color:#1b2140}.checklist-container[_ngcontent-%COMP%]   .checklist-preview-content[_ngcontent-%COMP%]   .space[_ngcontent-%COMP%]{background-color:#dadce2;width:2px;height:24px;margin-left:11px;position:absolute;margin-top:34px}.checklist-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]{display:flex;flex-direction:column}.checklist-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-checklist-header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;height:46px}.checklist-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-checklist-header[_ngcontent-%COMP%]   .create-checklist-header-1[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:50%}.checklist-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-checklist-header[_ngcontent-%COMP%]   .create-checklist-header-1[_ngcontent-%COMP%]   .view-checklist[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;min-width:30%;max-width:90%}.checklist-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-checklist-header[_ngcontent-%COMP%]   .create-checklist-header-1[_ngcontent-%COMP%]   .view-checklist[_ngcontent-%COMP%]   .checklist-name[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:16px;font-weight:500;color:#111434;max-width:90%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;padding-left:8px}.checklist-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-checklist-header[_ngcontent-%COMP%]   .create-checklist-header-1[_ngcontent-%COMP%]   .view-checklist[_ngcontent-%COMP%]   .edit-icon[_ngcontent-%COMP%]{font-size:16px;width:16px;height:16px;color:#b9c0ca;margin-left:8px;cursor:pointer}.checklist-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-checklist-header[_ngcontent-%COMP%]   .create-checklist-header-1[_ngcontent-%COMP%]   .edit-checklist[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;min-width:30%;max-width:90%}.checklist-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-checklist-header[_ngcontent-%COMP%]   .create-checklist-header-1[_ngcontent-%COMP%]   .edit-checklist[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{height:100%;width:70%;padding:4px 8px;font-family:var(--atsfontFamily);font-size:14px;font-weight:400;color:#45546e;outline:none;border:none;border-bottom:1px solid #111434;background-color:#f7f9fb}.checklist-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-checklist-header[_ngcontent-%COMP%]   .create-checklist-header-1[_ngcontent-%COMP%]   .edit-checklist[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus, .checklist-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-checklist-header[_ngcontent-%COMP%]   .create-checklist-header-1[_ngcontent-%COMP%]   .edit-checklist[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:hover{border-bottom:1px solid #1890ff}.checklist-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-checklist-header[_ngcontent-%COMP%]   .create-checklist-header-1[_ngcontent-%COMP%]   .edit-checklist[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{font-family:var(--atsfontFamily);font-size:14px;font-weight:400;color:#6e7b8f}.checklist-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-checklist-header[_ngcontent-%COMP%]   .create-checklist-header-1[_ngcontent-%COMP%]   .edit-checklist[_ngcontent-%COMP%]   .tick-icon[_ngcontent-%COMP%]{font-size:16px;width:16px;height:16px;color:#111434;cursor:pointer}.checklist-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-checklist-header[_ngcontent-%COMP%]   .create-checklist-header-1[_ngcontent-%COMP%]   .checklist-name-desc[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:11px;font-weight:400;color:#b9c0ca;padding-left:8px}.checklist-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-checklist-header[_ngcontent-%COMP%]   .create-checklist-header-2[_ngcontent-%COMP%]{display:flex;align-items:end;gap:16px}.checklist-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-checklist-header[_ngcontent-%COMP%]   .create-checklist-header-2[_ngcontent-%COMP%]   .reset[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:700;color:#45546e;text-decoration:underline;cursor:pointer;white-space:nowrap}.checklist-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-checklist-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:8px;margin-top:16px;padding:16px 32px;height:calc(var(--dynamicSubHeight) - 8px);background-color:#fafafa;border:1px solid #f3f4f6;overflow-y:auto}.checklist-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-checklist-content[_ngcontent-%COMP%]   .checklist-title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:700;color:#45546e}.checklist-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-checklist-content[_ngcontent-%COMP%]   .checklist-tasks[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:24px}.single-checklist-task[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.single-checklist-task[_ngcontent-%COMP%]   .step-index[_ngcontent-%COMP%]{min-width:70px;border-radius:8px;border:1px solid #dadce2;font-size:12px;font-weight:500;color:#5f6c81}.single-checklist-task[_ngcontent-%COMP%]   .add-task[_ngcontent-%COMP%], .single-checklist-task[_ngcontent-%COMP%]   .step-index[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;height:40px;font-family:var(--atsfontFamily)}.single-checklist-task[_ngcontent-%COMP%]   .add-task[_ngcontent-%COMP%]{border-radius:8px;border:1.5px dashed #6e7b8f;cursor:pointer;font-size:14px;font-weight:400;color:#6e7b8f;min-width:calc(100% - 70px)}.single-checklist-task[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;height:40px;min-width:calc(100% - 70px);border:1px solid #dadce2;border-radius:4px}.single-checklist-task[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .step-data[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px;width:80%}.single-checklist-task[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .step-data[_ngcontent-%COMP%]   .drag[_ngcontent-%COMP%]{width:32px;height:36px;background-color:#f3f3f9;border-radius:4px;margin-left:2px;display:flex;align-items:center;justify-content:center;cursor:move}.single-checklist-task[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .step-data[_ngcontent-%COMP%]   .drag[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{font-size:24px;width:24px;height:24px;color:#94a2ab}.single-checklist-task[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .step-data[_ngcontent-%COMP%]   .task-name[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:12px;font-weight:500;color:#526179;width:calc(100% - 100px);overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.single-checklist-task[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%]{font-size:12px;width:12px;height:12px;color:#b9c0ca;cursor:pointer;margin-right:16px}.checklist-container-preview[_ngcontent-%COMP%]{box-shadow:0 4px 6px 0 rgba(0,0,0,.12156862745098039)}.activation-overlay-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:4px;border:1.5px solid #b9c0ca;border-radius:8px;padding:8px;background-color:#fff}.activation-overlay-container[_ngcontent-%COMP%]   .single-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;border-radius:4px;padding:4px 8px;cursor:pointer}.activation-overlay-container[_ngcontent-%COMP%]   .single-item[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{min-width:8px;min-height:8px;border-radius:50%}.activation-overlay-container[_ngcontent-%COMP%]   .single-item[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:400;color:#5f6c81}.activation-overlay-container[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]{color:#eaeaea}.active-tasks[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px;border-radius:16px;border:1px solid #8b95a5;padding:16px;width:100%;background:#fff}.active-tasks[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:700;color:#1b2140}.active-tasks[_ngcontent-%COMP%]   .buttons[_ngcontent-%COMP%]{width:100%;display:flex;align-items:center;justify-content:end;gap:8px}.active-tasks[_ngcontent-%COMP%]   .buttons[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{border-radius:8px;border:1px solid #5f6c81;padding:7px 15px;color:#5f6c81}.active-tasks[_ngcontent-%COMP%]   .buttons[_ngcontent-%COMP%]   .add-btn[_ngcontent-%COMP%], .active-tasks[_ngcontent-%COMP%]   .buttons[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:400;cursor:pointer}.active-tasks[_ngcontent-%COMP%]   .buttons[_ngcontent-%COMP%]   .add-btn[_ngcontent-%COMP%]{border-radius:8px;padding:8px 16px;color:#fff;background-color:var(--atsprimaryColor)}.active-tasks[_ngcontent-%COMP%]   .loading-ui[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;height:var(--dynamicSubHeight)}.active-tasks[_ngcontent-%COMP%]   .loading-ui[_ngcontent-%COMP%]   .no-data-title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:500;color:#1b2140}.active-tasks[_ngcontent-%COMP%]   .loading-ui[_ngcontent-%COMP%]   .loading-wrapper[_ngcontent-%COMP%]{display:flex;vertical-align:center;justify-content:center;align-items:center}.active-tasks[_ngcontent-%COMP%]   .loading-ui[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]{color:rgba(0,0,0,.3);font-size:16px}.active-tasks[_ngcontent-%COMP%]   .loading-ui[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]:before{content:"Loading...";position:absolute;overflow:hidden;max-width:7em;white-space:nowrap;background:linear-gradient(270deg,var(--atsprimaryColor),var(--atssecondaryColor) 105.29%);background-clip:text;-webkit-background-clip:text;-webkit-text-fill-color:transparent;animation:loading 6s linear infinite}@keyframes loading{0%{max-width:0}}']}),e})()}];let ge=(()=>{class e{}return e.\u0275mod=p["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=p["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[c.k.forChild(he)],c.k]}),e})();var pe=n("1+mW"),ue=n("Xi0T"),ke=n("lVl8"),fe=n("qFsG"),Ce=n("1jcm"),me=n("iadO"),ve=n("FKr1"),_e=n("bSwM"),xe=n("QibW"),Oe=n("d3UM");let we=(()=>{class e{}return e.\u0275mod=p["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=p["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,ge,pe.ApplicantTrackingSystemModule,ue.a,C.h,ke.b,fe.c,l.p,l.E,_.b,Ce.b,me.h,ve.n,_e.b,xe.c,Oe.d,h.g,M.b,x.b,w.g]]}),e})()},pEYl:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var i=n("yuIm"),c=n("fXoL");let o=(()=>{class e{transform(e=0,t=0,n=0,c=0,o=""){if(!o||""==o)return!1;let s={module_id:e,sub_module_id:t,section_id:n,sub_section_id:c};"V"==o&&(s.view_permission=1),"C"==o&&(s.create_permission=1),"E"==o&&(s.edit_permission=1),"DE"==o&&(s.delete_permission=1),"DO"==o&&(s.download_permission=1),"U"==o&&(s.upload_permission=1),"B"==o&&(s.bulk_operation=1);const r=Object.keys(s);return i.roleAccessList.find(e=>r.every(t=>e[t]===s[t]))}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=c["\u0275\u0275definePipe"]({name:"access",type:e,pure:!0}),e})()}}]);