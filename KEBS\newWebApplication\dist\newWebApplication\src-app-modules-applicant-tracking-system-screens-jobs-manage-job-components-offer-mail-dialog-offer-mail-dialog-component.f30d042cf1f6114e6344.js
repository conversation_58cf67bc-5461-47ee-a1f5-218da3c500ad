(window.webpackJsonp=window.webpackJsonp||[]).push([[914,634,858],{NJ67:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));class i{constructor(){this.disabled=!1}onChange(e){}onTouched(e){}writeValue(e){this.value=e}registerOnChange(e){this.onChange=e}registerOnTouched(e){this.onTouched=e}setDisabledState(e){this.disabled=e}}},PMS8:function(e,t,n){"use strict";n.r(t),n.d(t,"MY_FORMATS",(function(){return Et})),n.d(t,"OfferMailDialogComponent",(function(){return bt}));var i=n("mrSG"),r=n("5+WD"),a=n("3Pt+"),o=n("0IaG"),l=n("XNiG"),s=n("1G5W"),c=n("1yaQ"),d=n("FKr1"),p=n("wd/R"),m=n.n(p),f=n("xG9w"),u=n("fXoL"),g=n("YVm3"),v=n("rQiX"),h=n("XNFG"),C=n("JLuW"),M=n("URR/"),F=n("nT5s"),y=n("NFeN"),x=n("ofXK"),S=n("Xa2L"),D=n("UVjm"),O=n("kmnG"),_=n("qFsG"),E=n("IDGp"),b=n("iadO"),w=n("f0Cb"),P=n("su5B"),I=n("1jcm"),V=n("bSwM"),A=n("II/y"),L=n("pPzn"),z=n("zGnX");function B(e,t){if(1&e&&(u["\u0275\u0275elementStart"](0,"div"),u["\u0275\u0275text"](1),u["\u0275\u0275elementEnd"]()),2&e){const e=u["\u0275\u0275nextContext"](),t=e.index;u["\u0275\u0275classMap"](e.$implicit.isSelected?"circle-selected":"circle"),u["\u0275\u0275advance"](1),u["\u0275\u0275textInterpolate1"](" ",t+1," ")}}function k(e,t){1&e&&(u["\u0275\u0275elementStart"](0,"div"),u["\u0275\u0275namespaceSVG"](),u["\u0275\u0275elementStart"](1,"svg",29),u["\u0275\u0275element"](2,"circle",30),u["\u0275\u0275elementStart"](3,"mask",31),u["\u0275\u0275element"](4,"rect",32),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](5,"g",33),u["\u0275\u0275element"](6,"path",34),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]())}function T(e,t){1&e&&u["\u0275\u0275element"](0,"div",35)}function j(e,t){if(1&e){const e=u["\u0275\u0275getCurrentView"]();u["\u0275\u0275elementContainerStart"](0),u["\u0275\u0275elementStart"](1,"div",25),u["\u0275\u0275listener"]("click",(function(){u["\u0275\u0275restoreView"](e);const n=t.index;return u["\u0275\u0275nextContext"](2).changeSection(n)})),u["\u0275\u0275template"](2,B,2,3,"div",26),u["\u0275\u0275template"](3,k,7,0,"div",27),u["\u0275\u0275elementStart"](4,"div"),u["\u0275\u0275text"](5),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275template"](6,T,1,0,"div",28),u["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=t.$implicit,n=t.index,i=u["\u0275\u0275nextContext"](2);u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",i.offerMailForm.get(e.formGroup).invalid),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf",i.offerMailForm.get(e.formGroup).valid),u["\u0275\u0275advance"](1),u["\u0275\u0275classMap"](e.isSelected?"section-text-selected":"section-text"),u["\u0275\u0275advance"](1),u["\u0275\u0275textInterpolate1"](" ",e.name," "),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf",n<i.sections.length-1)}}function Y(e,t){if(1&e&&(u["\u0275\u0275elementStart"](0,"div",23),u["\u0275\u0275template"](1,j,7,6,"ng-container",24),u["\u0275\u0275elementEnd"]()),2&e){const e=u["\u0275\u0275nextContext"]();u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngForOf",e.sections)}}function H(e,t){1&e&&(u["\u0275\u0275elementStart"](0,"div",36),u["\u0275\u0275element"](1,"mat-spinner",37),u["\u0275\u0275elementEnd"]())}function R(e,t){if(1&e&&(u["\u0275\u0275elementStart"](0,"div",48),u["\u0275\u0275elementStart"](1,"div",49),u["\u0275\u0275text"](2),u["\u0275\u0275pipe"](3,"columnCustomization"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](4,"div",50),u["\u0275\u0275text"](5),u["\u0275\u0275pipe"](6,"columnCustomization"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()),2&e){const e=u["\u0275\u0275nextContext"](3);u["\u0275\u0275advance"](2),u["\u0275\u0275textInterpolate1"](" ",u["\u0275\u0275pipeBind3"](3,2,e.offerMailFormFields[0].formSectionFields,"salaryText","label")," "),u["\u0275\u0275advance"](3),u["\u0275\u0275textInterpolate1"](" ",u["\u0275\u0275pipeBind3"](6,6,e.offerMailFormFields[0].formSectionFields,"salaryText","description")," ")}}function G(e,t){}function q(e,t){if(1&e&&u["\u0275\u0275template"](0,G,0,0,"ng-template",55),2&e){u["\u0275\u0275nextContext"](4);const e=u["\u0275\u0275reference"](27);u["\u0275\u0275property"]("ngTemplateOutlet",e)}}const N=function(){return{section:"general",field:"designation"}};function J(e,t){if(1&e){const e=u["\u0275\u0275getCurrentView"]();u["\u0275\u0275elementStart"](0,"app-single-select-chip",56),u["\u0275\u0275listener"]("onValueChange",(function(t){return u["\u0275\u0275restoreView"](e),u["\u0275\u0275nextContext"](4).onCustomSelectValueChange(t)})),u["\u0275\u0275pipe"](1,"columnCustomization"),u["\u0275\u0275elementEnd"]()}if(2&e){const e=u["\u0275\u0275nextContext"](4);u["\u0275\u0275property"]("masterData",e.designationMasterData)("placeholder",u["\u0275\u0275pipeBind3"](1,5,e.offerMailFormFields[0].formSectionFields,"designation","placeholder"))("selectedValue",e.offerMailForm.get("general").get("designation").value)("displayClose",!1)("data",u["\u0275\u0275pureFunction0"](9,N))}}function U(e,t){if(1&e&&(u["\u0275\u0275elementStart"](0,"mat-form-field",57),u["\u0275\u0275element"](1,"input",58),u["\u0275\u0275pipe"](2,"columnCustomization"),u["\u0275\u0275elementEnd"]()),2&e){const e=u["\u0275\u0275nextContext"](4);u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("placeholder",u["\u0275\u0275pipeBind3"](2,1,e.offerMailFormFields[0].formSectionFields,"designation","placeholder"))}}function W(e,t){if(1&e&&(u["\u0275\u0275elementStart"](0,"div",51),u["\u0275\u0275elementStart"](1,"div",52),u["\u0275\u0275text"](2),u["\u0275\u0275pipe"](3,"columnCustomization"),u["\u0275\u0275template"](4,q,1,1,void 0,27),u["\u0275\u0275pipe"](5,"columnCustomization"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275template"](6,J,2,10,"app-single-select-chip",53),u["\u0275\u0275pipe"](7,"columnCustomization"),u["\u0275\u0275template"](8,U,3,5,"mat-form-field",54),u["\u0275\u0275pipe"](9,"columnCustomization"),u["\u0275\u0275elementEnd"]()),2&e){const e=u["\u0275\u0275nextContext"](3);u["\u0275\u0275advance"](2),u["\u0275\u0275textInterpolate1"](" ",u["\u0275\u0275pipeBind3"](3,4,e.offerMailFormFields[0].formSectionFields,"designation","label")," "),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",u["\u0275\u0275pipeBind3"](5,8,e.offerMailFormFields[0].formSectionFields,"designation","isMandatory")),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf","single-select"==u["\u0275\u0275pipeBind3"](7,12,e.offerMailFormFields[0].formSectionFields,"designation","fieldType")),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf","text"==u["\u0275\u0275pipeBind3"](9,16,e.offerMailFormFields[0].formSectionFields,"designation","fieldType"))}}function Z(e,t){}function X(e,t){if(1&e&&u["\u0275\u0275template"](0,Z,0,0,"ng-template",55),2&e){u["\u0275\u0275nextContext"](4);const e=u["\u0275\u0275reference"](27);u["\u0275\u0275property"]("ngTemplateOutlet",e)}}function K(e,t){if(1&e){const e=u["\u0275\u0275getCurrentView"]();u["\u0275\u0275elementStart"](0,"div",51),u["\u0275\u0275elementStart"](1,"div",52),u["\u0275\u0275text"](2),u["\u0275\u0275pipe"](3,"columnCustomization"),u["\u0275\u0275template"](4,X,1,1,void 0,27),u["\u0275\u0275pipe"](5,"columnCustomization"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](6,"mat-form-field",57),u["\u0275\u0275elementStart"](7,"input",59),u["\u0275\u0275listener"]("keydown",(function(t){return u["\u0275\u0275restoreView"](e),u["\u0275\u0275nextContext"](3).preventInvalidValues(t)}))("paste",(function(t){return u["\u0275\u0275restoreView"](e),u["\u0275\u0275nextContext"](3).preventPaste(t)})),u["\u0275\u0275pipe"](8,"columnCustomization"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()}if(2&e){const e=u["\u0275\u0275nextContext"](3);u["\u0275\u0275advance"](2),u["\u0275\u0275textInterpolate1"](" ",u["\u0275\u0275pipeBind3"](3,3,e.offerMailFormFields[0].formSectionFields,"amount","label")," "),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",u["\u0275\u0275pipeBind3"](5,7,e.offerMailFormFields[0].formSectionFields,"amount","isMandatory")),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("placeholder",u["\u0275\u0275pipeBind3"](8,11,e.offerMailFormFields[0].formSectionFields,"amount","placeholder"))}}function Q(e,t){}function $(e,t){if(1&e&&u["\u0275\u0275template"](0,Q,0,0,"ng-template",55),2&e){u["\u0275\u0275nextContext"](4);const e=u["\u0275\u0275reference"](27);u["\u0275\u0275property"]("ngTemplateOutlet",e)}}const ee=function(){return{section:"general",field:"frequency"}};function te(e,t){if(1&e){const e=u["\u0275\u0275getCurrentView"]();u["\u0275\u0275elementStart"](0,"div",51),u["\u0275\u0275elementStart"](1,"div",52),u["\u0275\u0275text"](2),u["\u0275\u0275pipe"](3,"columnCustomization"),u["\u0275\u0275template"](4,$,1,1,void 0,27),u["\u0275\u0275pipe"](5,"columnCustomization"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](6,"app-single-select-chip",56),u["\u0275\u0275listener"]("onValueChange",(function(t){return u["\u0275\u0275restoreView"](e),u["\u0275\u0275nextContext"](3).onCustomSelectValueChange(t)})),u["\u0275\u0275pipe"](7,"columnCustomization"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()}if(2&e){const e=u["\u0275\u0275nextContext"](3);u["\u0275\u0275advance"](2),u["\u0275\u0275textInterpolate1"](" ",u["\u0275\u0275pipeBind3"](3,7,e.offerMailFormFields[0].formSectionFields,"frequency","label")," "),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",u["\u0275\u0275pipeBind3"](5,11,e.offerMailFormFields[0].formSectionFields,"frequency","isMandatory")),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("masterData",e.frequencyMasterData)("placeholder",u["\u0275\u0275pipeBind3"](7,15,e.offerMailFormFields[0].formSectionFields,"frequency","placeholder"))("selectedValue",e.offerMailForm.get("general").get("frequency").value)("displayClose",!1)("data",u["\u0275\u0275pureFunction0"](19,ee))}}function ne(e,t){}function ie(e,t){if(1&e&&u["\u0275\u0275template"](0,ne,0,0,"ng-template",55),2&e){u["\u0275\u0275nextContext"](4);const e=u["\u0275\u0275reference"](27);u["\u0275\u0275property"]("ngTemplateOutlet",e)}}const re=function(){return{section:"general",field:"currency"}};function ae(e,t){if(1&e){const e=u["\u0275\u0275getCurrentView"]();u["\u0275\u0275elementStart"](0,"div",51),u["\u0275\u0275elementStart"](1,"div",52),u["\u0275\u0275text"](2),u["\u0275\u0275pipe"](3,"columnCustomization"),u["\u0275\u0275template"](4,ie,1,1,void 0,27),u["\u0275\u0275pipe"](5,"columnCustomization"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](6,"app-single-select-chip",56),u["\u0275\u0275listener"]("onValueChange",(function(t){return u["\u0275\u0275restoreView"](e),u["\u0275\u0275nextContext"](3).onCustomSelectValueChange(t)})),u["\u0275\u0275pipe"](7,"columnCustomization"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()}if(2&e){const e=u["\u0275\u0275nextContext"](3);u["\u0275\u0275advance"](2),u["\u0275\u0275textInterpolate1"](" ",u["\u0275\u0275pipeBind3"](3,7,e.offerMailFormFields[0].formSectionFields,"currency","label")," "),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",u["\u0275\u0275pipeBind3"](5,11,e.offerMailFormFields[0].formSectionFields,"currency","isMandatory")),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("masterData",e.currencyMasterData)("placeholder",u["\u0275\u0275pipeBind3"](7,15,e.offerMailFormFields[0].formSectionFields,"currency","placeholder"))("selectedValue",e.offerMailForm.get("general").get("currency").value)("displayClose",!1)("data",u["\u0275\u0275pureFunction0"](19,re))}}function oe(e,t){}function le(e,t){if(1&e&&u["\u0275\u0275template"](0,oe,0,0,"ng-template",55),2&e){u["\u0275\u0275nextContext"](4);const e=u["\u0275\u0275reference"](27);u["\u0275\u0275property"]("ngTemplateOutlet",e)}}function se(e,t){if(1&e){const e=u["\u0275\u0275getCurrentView"]();u["\u0275\u0275elementStart"](0,"div",51),u["\u0275\u0275elementStart"](1,"div",52),u["\u0275\u0275text"](2),u["\u0275\u0275pipe"](3,"columnCustomization"),u["\u0275\u0275template"](4,le,1,1,void 0,27),u["\u0275\u0275pipe"](5,"columnCustomization"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](6,"mat-form-field",57),u["\u0275\u0275elementStart"](7,"input",60),u["\u0275\u0275listener"]("keydown",(function(t){return u["\u0275\u0275restoreView"](e),u["\u0275\u0275nextContext"](3).preventInvalidValues(t)}))("paste",(function(t){return u["\u0275\u0275restoreView"](e),u["\u0275\u0275nextContext"](3).preventPaste(t)})),u["\u0275\u0275pipe"](8,"columnCustomization"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()}if(2&e){const e=u["\u0275\u0275nextContext"](3);u["\u0275\u0275advance"](2),u["\u0275\u0275textInterpolate1"](" ",u["\u0275\u0275pipeBind3"](3,3,e.offerMailFormFields[0].formSectionFields,"oneTimeBonus","label")," "),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",u["\u0275\u0275pipeBind3"](5,7,e.offerMailFormFields[0].formSectionFields,"oneTimeBonus","isMandatory")),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("placeholder",u["\u0275\u0275pipeBind3"](8,11,e.offerMailFormFields[0].formSectionFields,"oneTimeBonus","placeholder"))}}function ce(e,t){}function de(e,t){if(1&e&&u["\u0275\u0275template"](0,ce,0,0,"ng-template",55),2&e){u["\u0275\u0275nextContext"](4);const e=u["\u0275\u0275reference"](27);u["\u0275\u0275property"]("ngTemplateOutlet",e)}}function pe(e,t){if(1&e&&(u["\u0275\u0275elementStart"](0,"div",51),u["\u0275\u0275elementStart"](1,"div",52),u["\u0275\u0275text"](2),u["\u0275\u0275pipe"](3,"columnCustomization"),u["\u0275\u0275template"](4,de,1,1,void 0,27),u["\u0275\u0275pipe"](5,"columnCustomization"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275element"](6,"app-month-year-picker",61),u["\u0275\u0275elementEnd"]()),2&e){const e=u["\u0275\u0275nextContext"](3);u["\u0275\u0275advance"](2),u["\u0275\u0275textInterpolate1"](" ",u["\u0275\u0275pipeBind3"](3,2,e.offerMailFormFields[0].formSectionFields,"driveMonthYear","label")," "),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",u["\u0275\u0275pipeBind3"](5,6,e.offerMailFormFields[0].formSectionFields,"driveMonthYear","isMandatory"))}}function me(e,t){}function fe(e,t){if(1&e&&u["\u0275\u0275template"](0,me,0,0,"ng-template",55),2&e){u["\u0275\u0275nextContext"](4);const e=u["\u0275\u0275reference"](27);u["\u0275\u0275property"]("ngTemplateOutlet",e)}}function ue(e,t){if(1&e&&(u["\u0275\u0275elementStart"](0,"div",51),u["\u0275\u0275elementStart"](1,"div",52),u["\u0275\u0275text"](2),u["\u0275\u0275pipe"](3,"columnCustomization"),u["\u0275\u0275template"](4,fe,1,1,void 0,27),u["\u0275\u0275pipe"](5,"columnCustomization"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](6,"mat-form-field",57),u["\u0275\u0275elementStart"](7,"div",62),u["\u0275\u0275element"](8,"input",63),u["\u0275\u0275pipe"](9,"columnCustomization"),u["\u0275\u0275elementStart"](10,"mat-datepicker-toggle",64),u["\u0275\u0275elementStart"](11,"mat-icon",65),u["\u0275\u0275text"](12," calendar_today "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275element"](13,"mat-datepicker",66,67),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()),2&e){const e=u["\u0275\u0275reference"](14),t=u["\u0275\u0275nextContext"](3);u["\u0275\u0275advance"](2),u["\u0275\u0275textInterpolate1"](" ",u["\u0275\u0275pipeBind3"](3,6,t.offerMailFormFields[0].formSectionFields,"dateOfJoining","label")," "),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",u["\u0275\u0275pipeBind3"](5,10,t.offerMailFormFields[0].formSectionFields,"dateOfJoining","isMandatory")),u["\u0275\u0275advance"](4),u["\u0275\u0275property"]("min",t.currentDate)("placeholder",u["\u0275\u0275pipeBind3"](9,14,t.offerMailFormFields[0].formSectionFields,"dateOfJoining","placeholder"))("matDatepicker",e),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("for",e)}}function ge(e,t){1&e&&u["\u0275\u0275element"](0,"mat-divider",68)}function ve(e,t){if(1&e&&(u["\u0275\u0275elementStart"](0,"div",48),u["\u0275\u0275elementStart"](1,"div",49),u["\u0275\u0275text"](2),u["\u0275\u0275pipe"](3,"columnCustomization"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](4,"div",50),u["\u0275\u0275text"](5),u["\u0275\u0275pipe"](6,"columnCustomization"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()),2&e){const e=u["\u0275\u0275nextContext"](3);u["\u0275\u0275advance"](2),u["\u0275\u0275textInterpolate1"](" ",u["\u0275\u0275pipeBind3"](3,2,e.offerMailFormFields[0].formSectionFields,"reportingText","label")," "),u["\u0275\u0275advance"](3),u["\u0275\u0275textInterpolate1"](" ",u["\u0275\u0275pipeBind3"](6,6,e.offerMailFormFields[0].formSectionFields,"reportingText","description")," ")}}function he(e,t){}function Ce(e,t){if(1&e&&u["\u0275\u0275template"](0,he,0,0,"ng-template",55),2&e){u["\u0275\u0275nextContext"](4);const e=u["\u0275\u0275reference"](27);u["\u0275\u0275property"]("ngTemplateOutlet",e)}}const Me=function(){return{section:"general",field:"reportingTo"}};function Fe(e,t){if(1&e){const e=u["\u0275\u0275getCurrentView"]();u["\u0275\u0275elementStart"](0,"div",69),u["\u0275\u0275elementStart"](1,"div",52),u["\u0275\u0275text"](2),u["\u0275\u0275pipe"](3,"columnCustomization"),u["\u0275\u0275template"](4,Ce,1,1,void 0,27),u["\u0275\u0275pipe"](5,"columnCustomization"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](6,"app-multi-select-chip",70),u["\u0275\u0275listener"]("onValueChange",(function(t){return u["\u0275\u0275restoreView"](e),u["\u0275\u0275nextContext"](3).onCustomSelectValueChange(t)})),u["\u0275\u0275pipe"](7,"columnCustomization"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()}if(2&e){const e=u["\u0275\u0275nextContext"](3);u["\u0275\u0275advance"](2),u["\u0275\u0275textInterpolate1"](" ",u["\u0275\u0275pipeBind3"](3,8,e.offerMailFormFields[0].formSectionFields,"reportingTo","label")," "),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",u["\u0275\u0275pipeBind3"](5,12,e.offerMailFormFields[0].formSectionFields,"reportingTo","isMandatory")),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("type",2)("placeholder",u["\u0275\u0275pipeBind3"](7,16,e.offerMailFormFields[0].formSectionFields,"reportingTo","placeholder"))("masterData",e.reportingToMasterData)("selectedValues",e.offerMailForm.get("general").get("reportingTo").value)("displayClose",!1)("data",u["\u0275\u0275pureFunction0"](20,Me))}}function ye(e,t){1&e&&u["\u0275\u0275element"](0,"mat-divider",68)}function xe(e,t){if(1&e&&(u["\u0275\u0275elementStart"](0,"div",48),u["\u0275\u0275elementStart"](1,"div",49),u["\u0275\u0275text"](2),u["\u0275\u0275pipe"](3,"columnCustomization"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](4,"div",50),u["\u0275\u0275text"](5),u["\u0275\u0275pipe"](6,"columnCustomization"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()),2&e){const e=u["\u0275\u0275nextContext"](3);u["\u0275\u0275advance"](2),u["\u0275\u0275textInterpolate1"](" ",u["\u0275\u0275pipeBind3"](3,2,e.offerMailFormFields[0].formSectionFields,"internshipText","label")," "),u["\u0275\u0275advance"](3),u["\u0275\u0275textInterpolate1"](" ",u["\u0275\u0275pipeBind3"](6,6,e.offerMailFormFields[0].formSectionFields,"internshipText","description")," ")}}function Se(e,t){}function De(e,t){if(1&e&&u["\u0275\u0275template"](0,Se,0,0,"ng-template",55),2&e){u["\u0275\u0275nextContext"](4);const e=u["\u0275\u0275reference"](27);u["\u0275\u0275property"]("ngTemplateOutlet",e)}}const Oe=function(){return{section:"general",field:"internshipDuration"}};function _e(e,t){if(1&e){const e=u["\u0275\u0275getCurrentView"]();u["\u0275\u0275elementStart"](0,"div",51),u["\u0275\u0275elementStart"](1,"div",52),u["\u0275\u0275text"](2),u["\u0275\u0275pipe"](3,"columnCustomization"),u["\u0275\u0275template"](4,De,1,1,void 0,27),u["\u0275\u0275pipe"](5,"columnCustomization"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](6,"app-single-select-chip",56),u["\u0275\u0275listener"]("onValueChange",(function(t){return u["\u0275\u0275restoreView"](e),u["\u0275\u0275nextContext"](3).onCustomSelectValueChange(t)})),u["\u0275\u0275pipe"](7,"columnCustomization"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()}if(2&e){const e=u["\u0275\u0275nextContext"](3);u["\u0275\u0275advance"](2),u["\u0275\u0275textInterpolate1"](" ",u["\u0275\u0275pipeBind3"](3,7,e.offerMailFormFields[0].formSectionFields,"internshipDuration","label")," "),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",u["\u0275\u0275pipeBind3"](5,11,e.offerMailFormFields[0].formSectionFields,"internshipDuration","isMandatory")),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("masterData",e.internshipDurationMasterData)("placeholder",u["\u0275\u0275pipeBind3"](7,15,e.offerMailFormFields[0].formSectionFields,"internshipDuration","placeholder"))("selectedValue",e.offerMailForm.get("general").get("internshipDuration").value)("displayClose",!1)("data",u["\u0275\u0275pureFunction0"](19,Oe))}}function Ee(e,t){}function be(e,t){if(1&e&&u["\u0275\u0275template"](0,Ee,0,0,"ng-template",55),2&e){u["\u0275\u0275nextContext"](4);const e=u["\u0275\u0275reference"](27);u["\u0275\u0275property"]("ngTemplateOutlet",e)}}function we(e,t){if(1&e){const e=u["\u0275\u0275getCurrentView"]();u["\u0275\u0275elementStart"](0,"div",51),u["\u0275\u0275elementStart"](1,"div",52),u["\u0275\u0275text"](2),u["\u0275\u0275pipe"](3,"columnCustomization"),u["\u0275\u0275template"](4,be,1,1,void 0,27),u["\u0275\u0275pipe"](5,"columnCustomization"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](6,"mat-form-field",57),u["\u0275\u0275elementStart"](7,"input",71),u["\u0275\u0275listener"]("keydown",(function(t){return u["\u0275\u0275restoreView"](e),u["\u0275\u0275nextContext"](3).preventInvalidValues(t)}))("paste",(function(t){return u["\u0275\u0275restoreView"](e),u["\u0275\u0275nextContext"](3).preventPaste(t)})),u["\u0275\u0275pipe"](8,"columnCustomization"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()}if(2&e){const e=u["\u0275\u0275nextContext"](3);u["\u0275\u0275advance"](2),u["\u0275\u0275textInterpolate1"](" ",u["\u0275\u0275pipeBind3"](3,3,e.offerMailFormFields[0].formSectionFields,"stipendAmount","label")," "),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",u["\u0275\u0275pipeBind3"](5,7,e.offerMailFormFields[0].formSectionFields,"stipendAmount","isMandatory")),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("placeholder",u["\u0275\u0275pipeBind3"](8,11,e.offerMailFormFields[0].formSectionFields,"stipendAmount","placeholder"))}}function Pe(e,t){}function Ie(e,t){if(1&e&&u["\u0275\u0275template"](0,Pe,0,0,"ng-template",55),2&e){u["\u0275\u0275nextContext"](4);const e=u["\u0275\u0275reference"](27);u["\u0275\u0275property"]("ngTemplateOutlet",e)}}const Ve=function(){return{section:"general",field:"stipendFrequency"}};function Ae(e,t){if(1&e){const e=u["\u0275\u0275getCurrentView"]();u["\u0275\u0275elementStart"](0,"div",51),u["\u0275\u0275elementStart"](1,"div",52),u["\u0275\u0275text"](2),u["\u0275\u0275pipe"](3,"columnCustomization"),u["\u0275\u0275template"](4,Ie,1,1,void 0,27),u["\u0275\u0275pipe"](5,"columnCustomization"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](6,"app-single-select-chip",56),u["\u0275\u0275listener"]("onValueChange",(function(t){return u["\u0275\u0275restoreView"](e),u["\u0275\u0275nextContext"](3).onCustomSelectValueChange(t)})),u["\u0275\u0275pipe"](7,"columnCustomization"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()}if(2&e){const e=u["\u0275\u0275nextContext"](3);u["\u0275\u0275advance"](2),u["\u0275\u0275textInterpolate1"](" ",u["\u0275\u0275pipeBind3"](3,7,e.offerMailFormFields[0].formSectionFields,"stipendFrequency","label")," "),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",u["\u0275\u0275pipeBind3"](5,11,e.offerMailFormFields[0].formSectionFields,"stipendFrequency","isMandatory")),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("masterData",e.frequencyMasterData)("placeholder",u["\u0275\u0275pipeBind3"](7,15,e.offerMailFormFields[0].formSectionFields,"stipendFrequency","placeholder"))("selectedValue",e.offerMailForm.get("general").get("stipendFrequency").value)("displayClose",!1)("data",u["\u0275\u0275pureFunction0"](19,Ve))}}function Le(e,t){}function ze(e,t){if(1&e&&u["\u0275\u0275template"](0,Le,0,0,"ng-template",55),2&e){u["\u0275\u0275nextContext"](4);const e=u["\u0275\u0275reference"](27);u["\u0275\u0275property"]("ngTemplateOutlet",e)}}function Be(e,t){if(1&e&&(u["\u0275\u0275elementStart"](0,"div",51),u["\u0275\u0275elementStart"](1,"div",52),u["\u0275\u0275text"](2),u["\u0275\u0275pipe"](3,"columnCustomization"),u["\u0275\u0275template"](4,ze,1,1,void 0,27),u["\u0275\u0275pipe"](5,"columnCustomization"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](6,"mat-form-field",57),u["\u0275\u0275element"](7,"input",72),u["\u0275\u0275pipe"](8,"columnCustomization"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()),2&e){const e=u["\u0275\u0275nextContext"](3);u["\u0275\u0275advance"](2),u["\u0275\u0275textInterpolate1"](" ",u["\u0275\u0275pipeBind3"](3,3,e.offerMailFormFields[0].formSectionFields,"internshipStartAt","label")," "),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",u["\u0275\u0275pipeBind3"](5,7,e.offerMailFormFields[0].formSectionFields,"internshipStartAt","isMandatory")),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("placeholder",u["\u0275\u0275pipeBind3"](8,11,e.offerMailFormFields[0].formSectionFields,"internshipStartAt","placeholder"))}}function ke(e,t){1&e&&u["\u0275\u0275element"](0,"mat-divider",68)}function Te(e,t){}function je(e,t){if(1&e&&u["\u0275\u0275template"](0,Te,0,0,"ng-template",55),2&e){u["\u0275\u0275nextContext"](4);const e=u["\u0275\u0275reference"](27);u["\u0275\u0275property"]("ngTemplateOutlet",e)}}function Ye(e,t){if(1&e&&(u["\u0275\u0275elementStart"](0,"div",51),u["\u0275\u0275elementStart"](1,"div",52),u["\u0275\u0275text"](2),u["\u0275\u0275pipe"](3,"columnCustomization"),u["\u0275\u0275template"](4,je,1,1,void 0,27),u["\u0275\u0275pipe"](5,"columnCustomization"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](6,"mat-form-field",57),u["\u0275\u0275element"](7,"input",73),u["\u0275\u0275pipe"](8,"columnCustomization"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()),2&e){const e=u["\u0275\u0275nextContext"](3);u["\u0275\u0275advance"](2),u["\u0275\u0275textInterpolate1"](" ",u["\u0275\u0275pipeBind3"](3,3,e.offerMailFormFields[0].formSectionFields,"probationPeriod","label")," "),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",u["\u0275\u0275pipeBind3"](5,7,e.offerMailFormFields[0].formSectionFields,"probationPeriod","isMandatory")),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("placeholder",u["\u0275\u0275pipeBind3"](8,11,e.offerMailFormFields[0].formSectionFields,"probationPeriod","placeholder"))}}function He(e,t){}function Re(e,t){if(1&e&&u["\u0275\u0275template"](0,He,0,0,"ng-template",55),2&e){u["\u0275\u0275nextContext"](4);const e=u["\u0275\u0275reference"](27);u["\u0275\u0275property"]("ngTemplateOutlet",e)}}function Ge(e,t){if(1&e){const e=u["\u0275\u0275getCurrentView"]();u["\u0275\u0275elementStart"](0,"div",51),u["\u0275\u0275elementStart"](1,"div",52),u["\u0275\u0275text"](2),u["\u0275\u0275pipe"](3,"columnCustomization"),u["\u0275\u0275template"](4,Re,1,1,void 0,27),u["\u0275\u0275pipe"](5,"columnCustomization"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](6,"mat-form-field",57),u["\u0275\u0275elementStart"](7,"input",74),u["\u0275\u0275listener"]("keydown",(function(t){return u["\u0275\u0275restoreView"](e),u["\u0275\u0275nextContext"](3).preventInvalidValues(t)}))("paste",(function(t){return u["\u0275\u0275restoreView"](e),u["\u0275\u0275nextContext"](3).preventPaste(t)})),u["\u0275\u0275pipe"](8,"columnCustomization"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()}if(2&e){const e=u["\u0275\u0275nextContext"](3);u["\u0275\u0275advance"](2),u["\u0275\u0275textInterpolate1"](" ",u["\u0275\u0275pipeBind3"](3,3,e.offerMailFormFields[0].formSectionFields,"leaveDays","label")," "),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",u["\u0275\u0275pipeBind3"](5,7,e.offerMailFormFields[0].formSectionFields,"leaveDays","isMandatory")),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("placeholder",u["\u0275\u0275pipeBind3"](8,11,e.offerMailFormFields[0].formSectionFields,"leaveDays","placeholder"))}}function qe(e,t){}function Ne(e,t){if(1&e&&u["\u0275\u0275template"](0,qe,0,0,"ng-template",55),2&e){u["\u0275\u0275nextContext"](4);const e=u["\u0275\u0275reference"](27);u["\u0275\u0275property"]("ngTemplateOutlet",e)}}function Je(e,t){if(1&e&&(u["\u0275\u0275elementStart"](0,"div",51),u["\u0275\u0275elementStart"](1,"div",52),u["\u0275\u0275text"](2),u["\u0275\u0275pipe"](3,"columnCustomization"),u["\u0275\u0275template"](4,Ne,1,1,void 0,27),u["\u0275\u0275pipe"](5,"columnCustomization"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](6,"mat-form-field",57),u["\u0275\u0275elementStart"](7,"div",62),u["\u0275\u0275element"](8,"input",75),u["\u0275\u0275pipe"](9,"columnCustomization"),u["\u0275\u0275elementStart"](10,"mat-datepicker-toggle",64),u["\u0275\u0275elementStart"](11,"mat-icon",65),u["\u0275\u0275text"](12,"calendar_today"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275element"](13,"mat-datepicker",66,67),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()),2&e){const e=u["\u0275\u0275reference"](14),t=u["\u0275\u0275nextContext"](3);u["\u0275\u0275advance"](2),u["\u0275\u0275textInterpolate1"](" ",u["\u0275\u0275pipeBind3"](3,6,t.offerMailFormFields[0].formSectionFields,"expiryDate","label")," "),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",u["\u0275\u0275pipeBind3"](5,10,t.offerMailFormFields[0].formSectionFields,"expiryDate","isMandatory")),u["\u0275\u0275advance"](4),u["\u0275\u0275property"]("min",t.currentDate)("placeholder",u["\u0275\u0275pipeBind3"](9,14,t.offerMailFormFields[0].formSectionFields,"expiryDate","placeholder"))("matDatepicker",e),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("for",e)}}function Ue(e,t){1&e&&u["\u0275\u0275element"](0,"mat-divider",68)}function We(e,t){}function Ze(e,t){if(1&e&&u["\u0275\u0275template"](0,We,0,0,"ng-template",55),2&e){u["\u0275\u0275nextContext"](4);const e=u["\u0275\u0275reference"](27);u["\u0275\u0275property"]("ngTemplateOutlet",e)}}const Xe=function(){return{section:"general",field:"payscalearea"}};function Ke(e,t){if(1&e){const e=u["\u0275\u0275getCurrentView"]();u["\u0275\u0275elementStart"](0,"div",51),u["\u0275\u0275elementStart"](1,"div",52),u["\u0275\u0275text"](2),u["\u0275\u0275pipe"](3,"columnCustomization"),u["\u0275\u0275template"](4,Ze,1,1,void 0,27),u["\u0275\u0275pipe"](5,"columnCustomization"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](6,"app-single-select-chip",56),u["\u0275\u0275listener"]("onValueChange",(function(t){return u["\u0275\u0275restoreView"](e),u["\u0275\u0275nextContext"](3).onCustomSelectValueChange(t)})),u["\u0275\u0275pipe"](7,"columnCustomization"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()}if(2&e){const e=u["\u0275\u0275nextContext"](3);u["\u0275\u0275advance"](2),u["\u0275\u0275textInterpolate1"](" ",u["\u0275\u0275pipeBind3"](3,7,e.offerMailFormFields[0].formSectionFields,"payscalearea","label")," "),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",u["\u0275\u0275pipeBind3"](5,11,e.offerMailFormFields[0].formSectionFields,"payscalearea","isMandatory")),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("masterData",e.payscaleAreaData)("placeholder",u["\u0275\u0275pipeBind3"](7,15,e.offerMailFormFields[0].formSectionFields,"payscalearea","placeholder"))("selectedValue",e.offerMailForm.get("general").get("payscalearea").value)("displayClose",!1)("data",u["\u0275\u0275pureFunction0"](19,Xe))}}function Qe(e,t){}function $e(e,t){if(1&e&&u["\u0275\u0275template"](0,Qe,0,0,"ng-template",55),2&e){u["\u0275\u0275nextContext"](4);const e=u["\u0275\u0275reference"](27);u["\u0275\u0275property"]("ngTemplateOutlet",e)}}const et=function(){return{section:"general",field:"payscalegroup"}};function tt(e,t){if(1&e){const e=u["\u0275\u0275getCurrentView"]();u["\u0275\u0275elementStart"](0,"div",51),u["\u0275\u0275elementStart"](1,"div",52),u["\u0275\u0275text"](2),u["\u0275\u0275pipe"](3,"columnCustomization"),u["\u0275\u0275template"](4,$e,1,1,void 0,27),u["\u0275\u0275pipe"](5,"columnCustomization"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](6,"app-single-select-chip",56),u["\u0275\u0275listener"]("onValueChange",(function(t){return u["\u0275\u0275restoreView"](e),u["\u0275\u0275nextContext"](3).onCustomSelectValueChange(t)})),u["\u0275\u0275pipe"](7,"columnCustomization"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()}if(2&e){const e=u["\u0275\u0275nextContext"](3);u["\u0275\u0275advance"](2),u["\u0275\u0275textInterpolate1"](" ",u["\u0275\u0275pipeBind3"](3,7,e.offerMailFormFields[0].formSectionFields,"payscalegroup","label")," "),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",u["\u0275\u0275pipeBind3"](5,11,e.offerMailFormFields[0].formSectionFields,"payscalegroup","isMandatory")),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("masterData",e.payscaleGroupData)("placeholder",u["\u0275\u0275pipeBind3"](7,15,e.offerMailFormFields[0].formSectionFields,"payscalegroup","placeholder"))("selectedValue",e.offerMailForm.get("general").get("payscalegroup").value)("displayClose",!1)("data",u["\u0275\u0275pureFunction0"](19,et))}}function nt(e,t){}function it(e,t){if(1&e&&u["\u0275\u0275template"](0,nt,0,0,"ng-template",55),2&e){u["\u0275\u0275nextContext"](4);const e=u["\u0275\u0275reference"](27);u["\u0275\u0275property"]("ngTemplateOutlet",e)}}const rt=function(){return{section:"general",field:"ctcsplitup"}};function at(e,t){if(1&e){const e=u["\u0275\u0275getCurrentView"]();u["\u0275\u0275elementStart"](0,"div",51),u["\u0275\u0275elementStart"](1,"div",52),u["\u0275\u0275text"](2),u["\u0275\u0275pipe"](3,"columnCustomization"),u["\u0275\u0275template"](4,it,1,1,void 0,27),u["\u0275\u0275pipe"](5,"columnCustomization"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](6,"app-single-select-chip",56),u["\u0275\u0275listener"]("onValueChange",(function(t){return u["\u0275\u0275restoreView"](e),u["\u0275\u0275nextContext"](3).onCustomSelectValueChange(t)})),u["\u0275\u0275pipe"](7,"columnCustomization"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()}if(2&e){const e=u["\u0275\u0275nextContext"](3);u["\u0275\u0275advance"](2),u["\u0275\u0275textInterpolate1"](" ",u["\u0275\u0275pipeBind3"](3,7,e.offerMailFormFields[0].formSectionFields,"ctcsplitup","label")," "),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",u["\u0275\u0275pipeBind3"](5,11,e.offerMailFormFields[0].formSectionFields,"ctcsplitup","isMandatory")),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("masterData",e.payscaleLevelData)("placeholder",u["\u0275\u0275pipeBind3"](7,15,e.offerMailFormFields[0].formSectionFields,"ctcsplitup","placeholder"))("selectedValue",e.offerMailForm.get("general").get("ctcsplitup").value)("displayClose",!1)("data",u["\u0275\u0275pureFunction0"](19,rt))}}function ot(e,t){1&e&&u["\u0275\u0275element"](0,"mat-divider",68)}function lt(e,t){}function st(e,t){}function ct(e,t){if(1&e&&(u["\u0275\u0275elementStart"](0,"div",52),u["\u0275\u0275text"](1," Approver "),u["\u0275\u0275template"](2,st,0,0,"ng-template",55),u["\u0275\u0275elementEnd"]()),2&e){u["\u0275\u0275nextContext"](6);const e=u["\u0275\u0275reference"](27);u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngTemplateOutlet",e)}}function dt(e,t){}function pt(e,t){if(1&e&&(u["\u0275\u0275elementStart"](0,"div",52),u["\u0275\u0275text"](1," Level "),u["\u0275\u0275template"](2,dt,0,0,"ng-template",55),u["\u0275\u0275elementEnd"]()),2&e){u["\u0275\u0275nextContext"](6);const e=u["\u0275\u0275reference"](27);u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngTemplateOutlet",e)}}function mt(e,t){1&e&&(u["\u0275\u0275elementStart"](0,"div",94),u["\u0275\u0275text"](1," Is Mandatory Approver "),u["\u0275\u0275elementEnd"]())}function ft(e,t){if(1&e){const e=u["\u0275\u0275getCurrentView"]();u["\u0275\u0275elementStart"](0,"div",95),u["\u0275\u0275listener"]("click",(function(){u["\u0275\u0275restoreView"](e);const t=u["\u0275\u0275nextContext"]().index;return u["\u0275\u0275nextContext"](5).createDynamicFormArray(t+1)})),u["\u0275\u0275namespaceSVG"](),u["\u0275\u0275elementStart"](1,"svg",96),u["\u0275\u0275elementStart"](2,"mask",97),u["\u0275\u0275element"](3,"rect",98),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](4,"g",99),u["\u0275\u0275element"](5,"path",100),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()}}function ut(e,t){if(1&e){const e=u["\u0275\u0275getCurrentView"]();u["\u0275\u0275elementStart"](0,"div",95),u["\u0275\u0275listener"]("click",(function(){u["\u0275\u0275restoreView"](e);const t=u["\u0275\u0275nextContext"]().index;return u["\u0275\u0275nextContext"](5).deleteDynamicFormArray(t)})),u["\u0275\u0275namespaceSVG"](),u["\u0275\u0275elementStart"](1,"svg",101),u["\u0275\u0275elementStart"](2,"mask",102),u["\u0275\u0275element"](3,"rect",32),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](4,"g",103),u["\u0275\u0275element"](5,"path",104),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()}}function gt(e,t){1&e&&(u["\u0275\u0275elementStart"](0,"div",105),u["\u0275\u0275elementStart"](1,"mat-icon",106),u["\u0275\u0275text"](2,"drag_indicator"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]())}function vt(e,t){1&e&&u["\u0275\u0275element"](0,"mat-divider",107)}const ht=function(e){return{section:"general",field:"approvers",index:e}};function Ct(e,t){if(1&e){const e=u["\u0275\u0275getCurrentView"]();u["\u0275\u0275elementContainerStart"](0,81),u["\u0275\u0275elementStart"](1,"div",82),u["\u0275\u0275elementStart"](2,"div",51),u["\u0275\u0275template"](3,ct,3,1,"div",83),u["\u0275\u0275elementStart"](4,"app-single-select-chip",84),u["\u0275\u0275listener"]("onValueChange",(function(t){return u["\u0275\u0275restoreView"](e),u["\u0275\u0275nextContext"](5).onCustomSelectValueChange(t)})),u["\u0275\u0275pipe"](5,"filterMasterData"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](6,"div",85),u["\u0275\u0275template"](7,pt,3,1,"div",83),u["\u0275\u0275elementStart"](8,"mat-form-field",57),u["\u0275\u0275element"](9,"input",86),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](10,"div",87),u["\u0275\u0275template"](11,mt,2,0,"div",88),u["\u0275\u0275element"](12,"mat-checkbox",89),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](13,"div",90),u["\u0275\u0275template"](14,ft,6,0,"div",91),u["\u0275\u0275template"](15,ut,6,0,"div",91),u["\u0275\u0275template"](16,gt,3,0,"div",92),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275template"](17,vt,1,0,"mat-divider",93),u["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=t.index,n=u["\u0275\u0275nextContext"](5);u["\u0275\u0275property"]("formGroupName",e),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("ngIf",0==e),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("placeholder","Select Approver")("masterData",u["\u0275\u0275pipeBind4"](5,15,n.approversMasterData,n.offerMailForm.get("general").get("approvers"),n.offerMailForm.get("general").get("approvers").get(e.toString()).get("approver").value,"approver"))("selectedValue",n.offerMailForm.get("general").get("approvers").get(e.toString()).get("approver").value)("displayClose",!1)("data",u["\u0275\u0275pureFunction1"](20,ht,e))("executeMasterOnChanges",!1),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("ngIf",0==e),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("placeholder","Level"),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",0==e),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("ngIf",n.offerMailForm.get("general").get("approvers").controls.length<=n.approversMasterData.length-1),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf",n.offerMailForm.get("general").get("approvers").controls.length>1),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf",2==n.offerMailForm.get("general").get("approvalType").value),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf",n.offerMailForm.get("general").get("approvers").controls.length-1!=e)}}const Mt=function(){return{section:"general",field:"approvalType"}};function Ft(e,t){if(1&e){const e=u["\u0275\u0275getCurrentView"]();u["\u0275\u0275elementContainerStart"](0),u["\u0275\u0275elementStart"](1,"div",48),u["\u0275\u0275elementStart"](2,"div",51),u["\u0275\u0275elementStart"](3,"div",52),u["\u0275\u0275text"](4," Approval Type "),u["\u0275\u0275template"](5,lt,0,0,"ng-template",55),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](6,"app-single-select-chip",78),u["\u0275\u0275listener"]("onValueChange",(function(t){return u["\u0275\u0275restoreView"](e),u["\u0275\u0275nextContext"](4).onCustomSelectValueChange(t)})),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](7,"div",79),u["\u0275\u0275listener"]("cdkDropListDropped",(function(t){return u["\u0275\u0275restoreView"](e),u["\u0275\u0275nextContext"](4).dragDropApprovers(t)})),u["\u0275\u0275template"](8,Ct,18,22,"ng-container",80),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=u["\u0275\u0275nextContext"](4),t=u["\u0275\u0275reference"](27);u["\u0275\u0275advance"](5),u["\u0275\u0275property"]("ngTemplateOutlet",t),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("masterData",e.approvalTypeMasterData)("placeholder","Select Approval type")("selectedValue",e.offerMailForm.get("general").get("approvalType").value)("displayClose",!1)("data",u["\u0275\u0275pureFunction0"](8,Mt))("executeMasterOnChanges",!1),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngForOf",e.offerMailForm.get("general").get("approvers").controls)}}function yt(e,t){if(1&e){const e=u["\u0275\u0275getCurrentView"]();u["\u0275\u0275elementContainerStart"](0),u["\u0275\u0275elementStart"](1,"div",76),u["\u0275\u0275elementStart"](2,"div",49),u["\u0275\u0275text"](3),u["\u0275\u0275pipe"](4,"columnCustomization"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](5,"mat-slide-toggle",77),u["\u0275\u0275listener"]("change",(function(){return u["\u0275\u0275restoreView"](e),u["\u0275\u0275nextContext"](3).onChangeApproversActive()})),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275template"](6,Ft,9,9,"ng-container",27),u["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=u["\u0275\u0275nextContext"](3);u["\u0275\u0275advance"](3),u["\u0275\u0275textInterpolate1"](" ",u["\u0275\u0275pipeBind3"](4,2,e.offerMailFormFields[0].formSectionFields,"approvals","label")," "),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("ngIf",e.offerMailForm.get("general").get("isApprovalActive").value)}}function xt(e,t){if(1&e&&(u["\u0275\u0275elementContainerStart"](0,41),u["\u0275\u0275template"](1,R,7,10,"div",42),u["\u0275\u0275pipe"](2,"columnCustomization"),u["\u0275\u0275elementStart"](3,"div",43),u["\u0275\u0275template"](4,W,10,20,"div",44),u["\u0275\u0275pipe"](5,"columnCustomization"),u["\u0275\u0275template"](6,K,9,15,"div",44),u["\u0275\u0275pipe"](7,"columnCustomization"),u["\u0275\u0275template"](8,te,8,20,"div",44),u["\u0275\u0275pipe"](9,"columnCustomization"),u["\u0275\u0275template"](10,ae,8,20,"div",44),u["\u0275\u0275pipe"](11,"columnCustomization"),u["\u0275\u0275template"](12,se,9,15,"div",44),u["\u0275\u0275pipe"](13,"columnCustomization"),u["\u0275\u0275template"](14,pe,7,10,"div",44),u["\u0275\u0275pipe"](15,"columnCustomization"),u["\u0275\u0275template"](16,ue,15,18,"div",44),u["\u0275\u0275pipe"](17,"columnCustomization"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275template"](18,ge,1,0,"mat-divider",45),u["\u0275\u0275pipe"](19,"columnCustomization"),u["\u0275\u0275template"](20,ve,7,10,"div",42),u["\u0275\u0275pipe"](21,"columnCustomization"),u["\u0275\u0275elementStart"](22,"div",46),u["\u0275\u0275template"](23,Fe,8,21,"div",47),u["\u0275\u0275pipe"](24,"columnCustomization"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275template"](25,ye,1,0,"mat-divider",45),u["\u0275\u0275pipe"](26,"columnCustomization"),u["\u0275\u0275template"](27,xe,7,10,"div",42),u["\u0275\u0275pipe"](28,"columnCustomization"),u["\u0275\u0275elementStart"](29,"div",46),u["\u0275\u0275template"](30,_e,8,20,"div",44),u["\u0275\u0275pipe"](31,"columnCustomization"),u["\u0275\u0275template"](32,we,9,15,"div",44),u["\u0275\u0275pipe"](33,"columnCustomization"),u["\u0275\u0275template"](34,Ae,8,20,"div",44),u["\u0275\u0275pipe"](35,"columnCustomization"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](36,"div",46),u["\u0275\u0275template"](37,Be,9,15,"div",44),u["\u0275\u0275pipe"](38,"columnCustomization"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275template"](39,ke,1,0,"mat-divider",45),u["\u0275\u0275pipe"](40,"columnCustomization"),u["\u0275\u0275elementStart"](41,"div",46),u["\u0275\u0275template"](42,Ye,9,15,"div",44),u["\u0275\u0275pipe"](43,"columnCustomization"),u["\u0275\u0275template"](44,Ge,9,15,"div",44),u["\u0275\u0275pipe"](45,"columnCustomization"),u["\u0275\u0275template"](46,Je,15,18,"div",44),u["\u0275\u0275pipe"](47,"columnCustomization"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275template"](48,Ue,1,0,"mat-divider",45),u["\u0275\u0275pipe"](49,"columnCustomization"),u["\u0275\u0275elementStart"](50,"div",46),u["\u0275\u0275template"](51,Ke,8,20,"div",44),u["\u0275\u0275pipe"](52,"columnCustomization"),u["\u0275\u0275template"](53,tt,8,20,"div",44),u["\u0275\u0275pipe"](54,"columnCustomization"),u["\u0275\u0275template"](55,at,8,20,"div",44),u["\u0275\u0275pipe"](56,"columnCustomization"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275template"](57,ot,1,0,"mat-divider",45),u["\u0275\u0275pipe"](58,"columnCustomization"),u["\u0275\u0275template"](59,yt,7,6,"ng-container",27),u["\u0275\u0275pipe"](60,"columnCustomization"),u["\u0275\u0275elementContainerEnd"]()),2&e){const e=u["\u0275\u0275nextContext"](2);u["\u0275\u0275property"]("formGroup",e.offerMailForm.get("general")),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf",u["\u0275\u0275pipeBind3"](2,28,e.offerMailFormFields[0].formSectionFields,"salaryText","isActive")),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("ngIf",u["\u0275\u0275pipeBind3"](5,32,e.offerMailFormFields[0].formSectionFields,"designation","isActive")),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",u["\u0275\u0275pipeBind3"](7,36,e.offerMailFormFields[0].formSectionFields,"amount","isActive")),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",u["\u0275\u0275pipeBind3"](9,40,e.offerMailFormFields[0].formSectionFields,"frequency","isActive")),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",u["\u0275\u0275pipeBind3"](11,44,e.offerMailFormFields[0].formSectionFields,"currency","isActive")),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",u["\u0275\u0275pipeBind3"](13,48,e.offerMailFormFields[0].formSectionFields,"oneTimeBonus","isActive")),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",u["\u0275\u0275pipeBind3"](15,52,e.offerMailFormFields[0].formSectionFields,"driveMonthYear","isActive")),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",u["\u0275\u0275pipeBind3"](17,56,e.offerMailFormFields[0].formSectionFields,"dateOfJoining","isActive")),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",u["\u0275\u0275pipeBind3"](19,60,e.offerMailFormFields[0].formSectionFields,"reportingText","isActive")),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",u["\u0275\u0275pipeBind3"](21,64,e.offerMailFormFields[0].formSectionFields,"reportingText","isActive")),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("ngIf",u["\u0275\u0275pipeBind3"](24,68,e.offerMailFormFields[0].formSectionFields,"reportingTo","isActive")),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",u["\u0275\u0275pipeBind3"](26,72,e.offerMailFormFields[0].formSectionFields,"internshipText","isActive")),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",u["\u0275\u0275pipeBind3"](28,76,e.offerMailFormFields[0].formSectionFields,"internshipText","isActive")),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("ngIf",u["\u0275\u0275pipeBind3"](31,80,e.offerMailFormFields[0].formSectionFields,"internshipDuration","isActive")),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",u["\u0275\u0275pipeBind3"](33,84,e.offerMailFormFields[0].formSectionFields,"stipendAmount","isActive")),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",u["\u0275\u0275pipeBind3"](35,88,e.offerMailFormFields[0].formSectionFields,"stipendFrequency","isActive")),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("ngIf",u["\u0275\u0275pipeBind3"](38,92,e.offerMailFormFields[0].formSectionFields,"internshipStartAt","isActive")),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",u["\u0275\u0275pipeBind3"](40,96,e.offerMailFormFields[0].formSectionFields,"expiryDate","isActive")),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("ngIf",u["\u0275\u0275pipeBind3"](43,100,e.offerMailFormFields[0].formSectionFields,"probationPeriod","isActive")),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",u["\u0275\u0275pipeBind3"](45,104,e.offerMailFormFields[0].formSectionFields,"leaveDays","isActive")),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",u["\u0275\u0275pipeBind3"](47,108,e.offerMailFormFields[0].formSectionFields,"expiryDate","isActive")),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",u["\u0275\u0275pipeBind3"](49,112,e.offerMailFormFields[0].formSectionFields,"ctcsplitup","isActive")),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("ngIf",u["\u0275\u0275pipeBind3"](52,116,e.offerMailFormFields[0].formSectionFields,"payscalearea","isActive")),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",u["\u0275\u0275pipeBind3"](54,120,e.offerMailFormFields[0].formSectionFields,"payscalegroup","isActive")),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",u["\u0275\u0275pipeBind3"](56,124,e.offerMailFormFields[0].formSectionFields,"ctcsplitup","isActive")),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",u["\u0275\u0275pipeBind3"](58,128,e.offerMailFormFields[0].formSectionFields,"approvals","isActive")),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",u["\u0275\u0275pipeBind3"](60,132,e.offerMailFormFields[0].formSectionFields,"approvals","isActive"))}}function St(e,t){if(1&e){const e=u["\u0275\u0275getCurrentView"]();u["\u0275\u0275elementContainerStart"](0,41),u["\u0275\u0275elementStart"](1,"app-mail-preview",108),u["\u0275\u0275listener"]("attachmentChanges",(function(t){return u["\u0275\u0275restoreView"](e),u["\u0275\u0275nextContext"](2).onAttachmentChanges(t)}))("htmlTemplateChanges",(function(t){return u["\u0275\u0275restoreView"](e),u["\u0275\u0275nextContext"](2).onHtmlTemplateChanges(t)})),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=u["\u0275\u0275nextContext"](2);u["\u0275\u0275property"]("formGroup",e.offerMailForm.get("mail")),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("mailFormGroup",e.offerMailForm.get("mail"))("mailFormConfig",e.offerMailFormFields[1].formSectionFields)("templateMasterData",e.templateMasterData)("fromMailPlaceHolderMasterData",e.fromMailPlaceHolderMasterData)("mailPlaceHolderMasterData",e.mailPlaceHolderMasterData)("placeholdersMasterData",e.placeholdersMasterData)("attachmentPlaceholderMasterData",e.attachmentPlaceholderMasterData)("attachmentCount",e.attachmentCount)("attachmentConfig",e.attachmentConfig)("attachmentPath",e.attachmentPath)("filesUploaded",e.filesUploaded)("previewImg",e.data.previewImg)("companyLogo",e.data.companyLogo)("showDocPreview",!0)("attachmentMasterDataForReplacement",e.attachmentMasterDataForReplacement)("previewToggleContent","Show Offer Letter Preview")}}function Dt(e,t){if(1&e&&(u["\u0275\u0275elementStart"](0,"div",38),u["\u0275\u0275elementStart"](1,"div",39),u["\u0275\u0275template"](2,xt,61,136,"ng-container",40),u["\u0275\u0275template"](3,St,2,17,"ng-container",40),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()),2&e){const e=u["\u0275\u0275nextContext"]();u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("formGroup",e.offerMailForm),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf",0==e.currentSection),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf",1==e.currentSection)}}function Ot(e,t){1&e&&(u["\u0275\u0275elementStart"](0,"span",109),u["\u0275\u0275text"](1,"*"),u["\u0275\u0275elementEnd"]())}const _t=function(e){return{"pointer-events":e}},Et={parse:{dateInput:"DD MMM YYYY"},display:{dateInput:"DD MMM YYYY",monthYearLabel:"MMM YYYY",dateA11yLabel:"LL",monthYearA11yLabel:"MMMM YYYY"}};let bt=(()=>{class e{constructor(e,t,n,i,r,a,o,s,c){this.data=e,this._dialogRef=t,this._fb=n,this._atsJobService=i,this._atsMasterService=r,this._toaster=a,this._sharedService=o,this._atsTemplateSettingsService=s,this._atsTemplateDocumentService=c,this._onDestroy=new l.b,this.isLoading=!0,this.offerMailFormFields=[],this.designationMasterData=[],this.currencyMasterData=[],this.reportingToMasterData=[],this.internshipDurationMasterData=[],this.approversMasterData=[],this.templateMasterData=[],this.fromMailPlaceHolderMasterData=[],this.mailPlaceHolderMasterData=[],this.placeholdersMasterData=[],this.frequencyMasterData=[],this.attachmentPlaceholderMasterData=[],this.approvalTypeMasterData=[{id:1,name:"All At Once"},{id:2,name:"In Order"}],this.payscaleLevelData=[],this.payscaleAreaData=[],this.payscaleGroupData=[],this.currentSection=0,this.attachmentConfig={},this.attachmentCount=0,this.filesUploaded=[],this.attachmentPath=null,this.attachmentMasterDataForReplacement={},this.offerAttachmentTemplate="",this.offerId=null,this.sections=[{name:"General",isSelected:!0,formGroup:"general"},{name:"Offer Mail",isSelected:!1,formGroup:"mail"}],this.finalCandidatesList=[],this.currentDate=m()(),this.ctcSplitupData={},this.offerMailForm=this._fb.group({})}ngOnInit(){var e,t;return Object(i.c)(this,void 0,void 0,(function*(){yield this.generateSelectedCandidatesId(),this.offerId=yield this.generateOfferId(),yield this.getAttachmentConfig(),yield this.getAtsFormsConfig("offerMail"),yield this.createOfferMailForm(),this.initializeFormMaster(),null===(t=null===(e=this.offerMailForm.get("general"))||void 0===e?void 0:e.get("approvalType"))||void 0===t||t.valueChanges.subscribe(e=>{this.updateLevelOfApprovers()}),this.offerMailForm.valueChanges.subscribe(e=>Object(i.c)(this,void 0,void 0,(function*(){let e=this.offerMailForm.get("general").getRawValue(),t=this.offerMailForm.get("mail").getRawValue(),n=this.data.selectedDetails&&this.data.selectedDetails.length>0?this.data.selectedDetails[0]:{};this.attachmentMasterDataForReplacement=Object.assign(Object.assign(Object.assign({},e),n),{offerId:this.offerId,currentDate:m()().format("YYYY-MM-DD"),frequencyMasterData:this.frequencyMasterData,currencyMasterData:this.currencyMasterData,designationMasterData:this.designationMasterData});let i=this.templateMasterData.find(e=>e.id==(null==t?void 0:t.template));this.attachmentMasterDataForReplacement=Object.assign(Object.assign({},this.attachmentMasterDataForReplacement),this.ctcSplitupData),this.offerAttachmentTemplate=yield this._atsTemplateDocumentService.replaceAllPlaceholders(null==i?void 0:i.attachment_html_template,this.attachmentMasterDataForReplacement,this.attachmentPlaceholderMasterData)})))}))}canDeactivate(){return!this.isLoading}initializeFormMaster(){Promise.all([this.getCurrency(),this.getAssociateDetailsInSystem(),this.getPlaceHolders(),this.getReceiverEmail(),this.getSenderEmail(),this.fetchAllEmailTemplates(),this.getDesignation(),this.getIntershipDuration(),this.getFrequency(),this.getOfferLetterPlaceholders(),this.getPayrollMasterData()]).then(e=>{this.isLoading=!1})}generateSelectedCandidatesId(){var e,t;return Object(i.c)(this,void 0,void 0,(function*(){if(this.data.isBulkSelectActive){let n=(null===(e=this.data)||void 0===e?void 0:e.candidatesExcluded)||[];this.finalCandidatesList=null===(t=this.data)||void 0===t?void 0:t.allCandidates.filter(e=>!n.includes(e))}else this.finalCandidatesList=f.pluck(this.data.selectedDetails,"candidate_id")}))}changeSection(e){var t,n,r,a,o;return Object(i.c)(this,void 0,void 0,(function*(){this.currentSection!=e&&(!(null===(t=this.data)||void 0===t?void 0:t.jobType)&&(null===(r=null===(n=this.data)||void 0===n?void 0:n.generalUiConfig)||void 0===r?void 0:r.offerApprovalMandatoryForLateralJob)&&(null===(o=null===(a=this.offerMailForm.get("general"))||void 0===a?void 0:a.get("isApprovalActive"))||void 0===o?void 0:o.invalid)&&this.offerMailForm.get("general").invalid?this._toaster.showWarning("Warning \u26a0\ufe0f","Approvals is Mandatory!",7e3):0==this.currentSection&&this.offerMailForm.get("general").invalid?this._toaster.showWarning("Warning \u26a0\ufe0f","Kindly fill all Mandatory Fields!",7e3):(this.currentSection=e,this.sections.forEach(e=>{e.isSelected=!1}),this.sections[e].isSelected=!0,yield this.generateCTCSplittup()))}))}onAttachmentChanges(e){this.attachmentCount=e.attachmentCount,this.filesUploaded=e.filesUploaded,this.attachmentPath=e.attachmentPath}onClose(e){this.isLoading&&!window.confirm("WARNING: An offer letter is currently being generated. Press Cancel to return and wait for its completion, or press OK to terminate the process.")||this._dialogRef.close(e)}preventPaste(e){e.preventDefault()}preventInvalidValues(e){["0","1","2","3","4","5","6","7","8","9","Backspace","Delete","ArrowLeft","ArrowRight","ArrowUp","ArrowDown","Tab"].includes(e.key)||e.preventDefault()}onCustomSelectValueChange(e){0==e.data.index||e.data.index?this.offerMailForm.get(e.data.section).get(e.data.field).get(e.data.index.toString()).get("approver").setValue(e.val):this.offerMailForm.get(e.data.section).get(e.data.field).setValue(e.val)}dragDropApprovers(e){Object(r.h)(this.offerMailForm.get("general").get("approvers").controls,e.previousIndex,e.currentIndex);let t=this.offerMailForm.get("general").get("approvalType").value,n=this.offerMailForm.get("general").get("approvers");if(1==t&&n)for(let i=0;i<n.length;i++)this.offerMailForm.get("general").get("approvers").get(i.toString()).get("level").setValue(1);else if(2==t&&n)for(let i=0;i<n.length;i++)this.offerMailForm.get("general").get("approvers").get(i.toString()).get("level").setValue(i+1)}createDynamicFormArray(e){const t=this.offerMailForm.get("general").get("approvers"),n=this._fb.group({approver:[null,[a.H.required]],level:[null,[a.H.required]],isMandatoryApprover:[!1]});t.insert(e,n),this.updateLevelOfApprovers()}deleteDynamicFormArray(e){this.offerMailForm.get("general").get("approvers").removeAt(e),this.updateLevelOfApprovers()}updateLevelOfApprovers(){let e=this.offerMailForm.get("general").get("approvalType").value,t=this.offerMailForm.get("general").get("approvers");if(1==e&&t)for(let n=0;n<t.length;n++)this.offerMailForm.get("general").get("approvers").get(n.toString()).get("level").setValue(1);else if(2==e&&t)for(let n=0;n<t.length;n++)this.offerMailForm.get("general").get("approvers").get(n.toString()).get("level").setValue(n+1)}onChangeApproversActive(){const e=this.offerMailForm.get("general"),t=e.get("isApprovalActive").value,n=e.get("approvalType"),i=e.get("approvers");if(t){const e=this._fb.group({approver:[null,a.H.required],level:[null,a.H.required],isMandatoryApprover:[!1]});i.push(e),n.setValidators([a.H.required])}else n.clearValidators(),n.setErrors(null),n.setValue(null),i.clear();n.updateValueAndValidity()}createOfferMailForm(){var e,t,n;return Object(i.c)(this,void 0,void 0,(function*(){for(let i=0;i<this.offerMailFormFields.length;i++){const r=this.offerMailFormFields[i].formSectionFields,o=this._fb.group({});for(let i=0;i<r.length;i++){if("display-text"!=r[i].fieldType&&"formArray"!=r[i].fieldType)r[i].isForCampusJob?this.data.jobType?o.addControl(r[i].key,this._fb.control(null,[r[i].isMandatory?a.H.required:null].filter(e=>null!=e))):r[i].isActive=!1:o.addControl(r[i].key,this._fb.control(null,[r[i].isMandatory?a.H.required:null].filter(e=>null!=e)));else if("formArray"==r[i].fieldType){const i=this._fb.array([]);o.addControl("approvers",i),o.addControl("approvalType",this._fb.control(null)),o.addControl("isApprovalActive",this._fb.control(!1,[!(null===(e=this.data)||void 0===e?void 0:e.jobType)&&(null===(n=null===(t=this.data)||void 0===t?void 0:t.generalUiConfig)||void 0===n?void 0:n.offerApprovalMandatoryForLateralJob)?a.H.requiredTrue:null].filter(e=>null!=e)))}"signature"==r[i].key&&o.addControl("isSignatureOn",this._fb.control(!1)),o.addControl("header",this._fb.control(null))}this.offerMailForm.addControl(this.offerMailFormFields[i].key,o)}}))}sendOffer(){var e,t,n,r,a,o,l,s,c,d,p,u,g;return Object(i.c)(this,void 0,void 0,(function*(){if(this.offerMailForm.invalid)return void this._toaster.showWarning("Warning \u26a0\ufe0f","Kindly fill all Mandatory Fields",7e3);let i=yield this._atsTemplateDocumentService.checkWhetherAllPlaceholdersAreReplaced(this.offerAttachmentTemplate);if(i&&i.length>0)return void this._toaster.showWarning("Warning \u26a0\ufe0f","Invalid Placeholders: "+i.join(", "),7e3);let v=this.offerMailForm.getRawValue(),h=null===(e=null==v?void 0:v.general)||void 0===e?void 0:e.approvers;if(h.length>0){let e=f.pluck(h,"isMandatoryApprover");if(e=e.filter(e=>1==e),0==e.length)return void this._toaster.showWarning("Warning \u26a0\ufe0f","Atleast one approver must be mandatory!",7e3)}v.mail.attachment=this.attachmentPath,v.mail.logo=this.data.companyLogo,v.general.expiryDate=(null===(n=null===(t=this.offerMailForm.get("general"))||void 0===t?void 0:t.get("expiryDate"))||void 0===n?void 0:n.value)?m()(null===(a=null===(r=this.offerMailForm.get("general"))||void 0===r?void 0:r.get("expiryDate"))||void 0===a?void 0:a.value).format("YYYY-MM-DD"):null,v.general.driveMonthYear=(null===(l=null===(o=this.offerMailForm.get("general"))||void 0===o?void 0:o.get("driveMonthYear"))||void 0===l?void 0:l.value)?m()(null===(c=null===(s=this.offerMailForm.get("general"))||void 0===s?void 0:s.get("driveMonthYear"))||void 0===c?void 0:c.value).format("YYYY-MM-DD"):null,v.general.dateOfJoining=(null===(p=null===(d=this.offerMailForm.get("general"))||void 0===d?void 0:d.get("dateOfJoining"))||void 0===p?void 0:p.value)?m()(null===(g=null===(u=this.offerMailForm.get("general"))||void 0===u?void 0:u.get("dateOfJoining"))||void 0===g?void 0:g.value).format("YYYY-MM-DD"):null,v.jobId=this.data.jobId,v.stageId=this.data.stageId,v.filter=this.data.filter,v.search_params=this.data.search_params,v.currentDate=m()().format("YYYY-MM-DD"),this.isLoading=!0;let C=[];for(let e=0;e<this.finalCandidatesList.length;e++){v.candidateIds=[this.finalCandidatesList[e]];let t=yield this.sendOfferToCandidates(v);C.push(t)}this.isLoading=!1,C.some(e=>!0===e)&&this.onClose(!0)}))}onHtmlTemplateChanges(e){this.offerAttachmentTemplate=e}getAtsFormsConfig(e){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._atsMasterService.getAtsFormsConfig(e).pipe(Object(s.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?(this.offerMailFormFields=e.data.formSection,t(!0)):(this._toaster.showError("Error",e.msg,7e3),t(!1))},error:e=>{this._toaster.showError("Error",e.message?e.message:"Forms Configuration Retrieval Failed!",7e3),n()}}))}))}getAttachmentConfig(){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getAttachmentConfig(1).pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?t.data&&t.data.length>0&&(this.attachmentConfig=t.data[0]):this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Master Data Retrieval Failed!",7e3),t()}}))}))}getCurrency(){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getCurrency().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.currencyMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Currency Master Data Retrieval Failed!",7e3),t()}}))}))}getAssociateDetailsInSystem(){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getAssociateDetailsInSystem().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?(this.reportingToMasterData=t.data,this.approversMasterData=t.data):this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Team Master Data Retrieval Failed!",7e3),t()}}))}))}getAtsEmployee(){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getAtsEmployee().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.approversMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Employee Master Data Retrieval Failed!",7e3),t()}}))}))}getSenderEmail(){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getSenderEmail().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.fromMailPlaceHolderMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Master Data Retrieval Failed!",7e3),t()}}))}))}getReceiverEmail(){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getReceiverEmail().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.mailPlaceHolderMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Master Data Retrieval Failed!",7e3),t()}}))}))}getPlaceHolders(){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getPlaceHolders().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.placeholdersMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Master Data Retrieval Failed!",7e3),t()}}))}))}fetchAllEmailTemplates(){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsTemplateSettingsService.fetchAllEmailTemplates(null,null,5).pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.templateMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Master Data Retrieval Failed!",7e3),t()}}))}))}getDesignation(){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getDesignation().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.designationMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Master Data Retrieval Failed!",7e3),t()}}))}))}getIntershipDuration(){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getIntershipDuration().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.internshipDurationMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Master Data Retrieval Failed!",7e3),t()}}))}))}getFrequency(){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getFrequency().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.frequencyMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Frequency Master Data Retrieval Failed!",7e3),t()}}))}))}sendOfferToCandidates(e){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._atsJobService.sendOfferToCandidates(e).pipe(Object(s.a)(this._onDestroy)).subscribe({next:n=>{var i;0==n.err?((null===(i=null==e?void 0:e.general)||void 0===i?void 0:i.isApprovalActive)?this._toaster.showSuccess("Success \u2705","Offer approval triggered successfully for Candidate ID - "+e.candidateIds[0],7e3):this._toaster.showSuccess("Success \u2705","Offer triggered successfully for Candidate ID - "+e.candidateIds[0],7e3),t(!0)):(this._toaster.showError("Error: Candidate ID - "+e.candidateIds[0],n.msg,7e3),t(!1))},error:e=>{this._toaster.showError("Error","KEBS system is currently unavailable. Kindly try after sometime!",7e3),this._dialogRef.close(),n()}}))}))}getOfferLetterPlaceholders(){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsTemplateSettingsService.getOfferLetterPlaceholders().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.attachmentPlaceholderMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Placeholder Master Data Retrieval Failed!",7e3),t()}}))}))}generateOfferId(){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsJobService.generateOfferId(this.data.jobId,this.finalCandidatesList[0]).pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{e(t.data)},error:e=>{this._toaster.showError("Error","KEBS system is currently unavailable. Kindly try after sometime!",7e3),this._dialogRef.close(),t()}}))}))}getPayrollMasterData(){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getPayrollMasterData().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{var n,i,r;"S"==t.messType?(this.payscaleAreaData=null===(n=t.data)||void 0===n?void 0:n.payscaleArea,this.payscaleGroupData=null===(i=t.data)||void 0===i?void 0:i.payscaleGroup,this.payscaleLevelData=null===(r=t.data)||void 0===r?void 0:r.payscaleLevel):this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Payscale Master Data Retrieval Failed!",7e3),t()}}))}))}generateCTCSplittup(){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>{var n,i,r,a,o,l,c,d,p,m;return this._atsMasterService.generateCTCSplittup(null===(i=null===(n=this.offerMailForm.get("general"))||void 0===n?void 0:n.get("amount"))||void 0===i?void 0:i.value,null===(a=null===(r=this.offerMailForm.get("general"))||void 0===r?void 0:r.get("payscalearea"))||void 0===a?void 0:a.value,null===(l=null===(o=this.offerMailForm.get("general"))||void 0===o?void 0:o.get("payscalegroup"))||void 0===l?void 0:l.value,null===(d=null===(c=this.offerMailForm.get("general"))||void 0===c?void 0:c.get("ctcsplitup"))||void 0===d?void 0:d.value,null===(m=null===(p=this.offerMailForm.get("general"))||void 0===p?void 0:p.get("currency"))||void 0===m?void 0:m.value).pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{t.err?this._toaster.showError("Error",t.msg,7e3):this.ctcSplitupData=t.data,e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Payscale Master Data Retrieval Failed!",7e3),t()}})})}))}}return e.\u0275fac=function(t){return new(t||e)(u["\u0275\u0275directiveInject"](o.a),u["\u0275\u0275directiveInject"](o.h),u["\u0275\u0275directiveInject"](a.i),u["\u0275\u0275directiveInject"](g.a),u["\u0275\u0275directiveInject"](v.a),u["\u0275\u0275directiveInject"](h.a),u["\u0275\u0275directiveInject"](C.a),u["\u0275\u0275directiveInject"](M.a),u["\u0275\u0275directiveInject"](F.a))},e.\u0275cmp=u["\u0275\u0275defineComponent"]({type:e,selectors:[["app-offer-mail-dialog"]],hostBindings:function(e,t){1&e&&u["\u0275\u0275listener"]("beforeunload",(function(){return t.canDeactivate()}),!1,u["\u0275\u0275resolveWindow"])},features:[u["\u0275\u0275ProvidersFeature"]([{provide:d.c,useClass:c.c,deps:[d.f,c.a]},{provide:d.e,useValue:Et}])],decls:28,vars:7,consts:[[1,"main-container"],[1,"d-flex","align-items-center","justify-content-between","header"],[1,"title","p-0"],[1,"d-flex","align-items-center"],["width","269","height","56","viewBox","0 0 269 56","fill","none","xmlns","http://www.w3.org/2000/svg"],["d","M133.603 -22.5168V4.76469C133.603 8.35397 132.087 11.811 129.39 14.4067C125.91 17.7302 120.857 19.1903 115.947 18.2782L115.946 18.2781L93.9778 14.2877L103.74 5.17581L116.468 7.51274L116.468 7.51278C119.364 8.04124 122.088 5.97651 122.088 3.19398V-11.8208L133.603 -22.5168Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M179.019 14.2879L157.079 18.2781L157.078 18.2782C152.174 19.1903 147.127 17.73 143.651 14.4063L143.649 14.4043C140.909 11.856 139.394 8.40031 139.394 4.81089V-22.5164L150.895 -11.8208V2.96299C150.895 5.88334 153.764 8.04193 156.758 7.46638C156.758 7.46632 156.758 7.46626 156.759 7.46621L169.221 5.17578L179.019 14.2879Z","fill","#EAEDF3","stroke","#DADCE2","stroke-width","0.421296"],["d","M133.603 37.6429V64.9403L122.088 54.256V38.8889C122.088 36.2929 119.563 34.3707 116.817 34.8512L116.816 34.8515L103.74 37.2322L93.9781 28.1306L115.946 24.1447L115.947 24.1445C120.857 23.2335 125.91 24.6921 129.391 28.012C132.087 30.6048 133.603 34.0579 133.603 37.6429Z","fill","#EAEDF3","stroke","#DADCE2","stroke-width","0.421296"],["d","M139.394 64.9399V37.6584C139.394 34.0692 140.91 30.6122 143.607 28.0166C147.087 24.693 152.141 23.2328 157.05 24.1448L157.051 24.145L179.018 28.1352L169.258 37.2012L155.933 34.7718C155.933 34.7718 155.933 34.7718 155.933 34.7718C153.335 34.2893 150.91 36.1234 150.91 38.6285V54.2438L139.394 64.9399Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M40.0758 14.7878V42.0694C40.0758 45.6587 38.5596 49.1157 35.8631 51.7114C32.3828 55.0348 27.3294 56.495 22.4198 55.5829L22.419 55.5828L0.45043 51.5924L10.2125 42.4805L22.9402 44.8174L22.9404 44.8175C25.8368 45.3459 28.5604 43.2812 28.5604 40.4987V25.4839L40.0758 14.7878Z","fill","#F3F5FA","stroke","#DADCE2","stroke-width","0.421296"],["d","M85.4914 51.5926L63.5512 55.5828L63.5504 55.5829C58.6468 56.495 53.5996 55.0347 50.1236 51.711L50.1214 51.709C47.3813 49.1606 45.8669 45.705 45.8669 42.1156V14.7883L57.3676 25.4838V40.2677C57.3676 43.188 60.2364 45.3466 63.2304 44.7711C63.2307 44.771 63.231 44.771 63.2313 44.7709L75.6933 42.4805L85.4914 51.5926Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M40.0758 14.4832V41.7647C40.0758 45.354 38.5596 48.811 35.8631 51.4067C32.3828 54.7302 27.3294 56.1903 22.4198 55.2782L22.419 55.2781L0.45043 51.2877L10.2125 42.1758L22.9402 44.5127L22.9404 44.5128C25.8368 45.0412 28.5604 42.9765 28.5604 40.194V25.1792L40.0758 14.4832Z","fill","#EAEDF3","fill-opacity","0.5","stroke","#DADCE2","stroke-width","0.421296"],["d","M85.4914 51.2879L63.5512 55.2781L63.5504 55.2782C58.6468 56.1903 53.5996 54.73 50.1236 51.4063L50.1214 51.4043C47.3813 48.856 45.8669 45.4003 45.8669 41.8109V14.4836L57.3676 25.1792V39.963C57.3676 42.8833 60.2364 45.0419 63.2304 44.4664C63.2307 44.4663 63.231 44.4663 63.2313 44.4662L75.6933 42.1758L85.4914 51.2879Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M227.13 14.7878V42.0694C227.13 45.6587 225.614 49.1157 222.918 51.7114C219.437 55.0348 214.384 56.495 209.475 55.5829L209.474 55.5828L187.505 51.5924L197.267 42.4805L209.995 44.8174L209.995 44.8175C212.892 45.3459 215.615 43.2812 215.615 40.4987V25.4839L227.13 14.7878Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M272.546 51.5926L250.606 55.5828L250.605 55.5829C245.702 56.495 240.654 55.0347 237.178 51.711L237.176 51.709C234.436 49.1606 232.922 45.705 232.922 42.1156V14.7883L244.422 25.4838V40.2677C244.422 43.188 247.291 45.3466 250.285 44.7711C250.285 44.771 250.286 44.771 250.286 44.7709L262.748 42.4805L272.546 51.5926Z","stroke","#DADCE2","stroke-width","0.421296"],[1,"icon",3,"click"],[1,"icon"],["class","d-flex align-items-center section",4,"ngIf"],["class","d-flex align-items-center justify-content-center loading-screen",4,"ngIf"],["class","main-screen",4,"ngIf"],[1,"d-flex","align-items-center","justify-content-end","footer"],[1,"button",3,"ngStyle","click"],["mandatoryTemplate",""],[1,"d-flex","align-items-center","section"],[4,"ngFor","ngForOf"],[1,"d-flex","align-items-center","space",3,"click"],[3,"class",4,"ngIf"],[4,"ngIf"],["class","border",4,"ngIf"],["width","30","height","30","viewBox","0 0 24 24"],["cx","12","cy","12","r","12","fill","#79BA44"],["id","mask0_11331_113242","maskUnits","userSpaceOnUse","x","0","y","0","width","24","height","24",2,"mask-type","alpha"],["width","24","height","24","fill","#D9D9D9"],["mask","url(#mask0_11331_113242)"],["d","M10.0008 13.5998L15.9008 7.6998C16.0841 7.51647 16.3174 7.4248 16.6008 7.4248C16.8841 7.4248 17.1174 7.51647 17.3008 7.6998C17.4841 7.88314 17.5758 8.11647 17.5758 8.3998C17.5758 8.68314 17.4841 8.91647 17.3008 9.0998L10.7008 15.6998C10.5008 15.8998 10.2674 15.9998 10.0008 15.9998C9.73411 15.9998 9.50078 15.8998 9.30078 15.6998L6.70078 13.0998C6.51745 12.9165 6.42578 12.6831 6.42578 12.3998C6.42578 12.1165 6.51745 11.8831 6.70078 11.6998C6.88411 11.5165 7.11745 11.4248 7.40078 11.4248C7.68411 11.4248 7.91745 11.5165 8.10078 11.6998L10.0008 13.5998Z","fill","white"],[1,"border"],[1,"d-flex","align-items-center","justify-content-center","loading-screen"],["diameter","40",1,"green-spinner"],[1,"main-screen"],[1,"d-flex","flex-column","forms",3,"formGroup"],[3,"formGroup",4,"ngIf"],[3,"formGroup"],["class","col-12 p-0",4,"ngIf"],[1,"d-flex","flex-wrap","align-items-center","col-12","p-0"],["class","d-flex flex-column col-4 pl-0",4,"ngIf"],["class","divider",4,"ngIf"],[1,"d-flex","align-items-center","col-12","p-0"],["class","d-flex flex-column col-10 pl-0",4,"ngIf"],[1,"col-12","p-0"],[1,"label-text"],[1,"description-text"],[1,"d-flex","flex-column","col-4","pl-0"],[1,"form-label"],[3,"masterData","placeholder","selectedValue","displayClose","data","onValueChange",4,"ngIf"],["class","form-field-class","appearance","outline",4,"ngIf"],[3,"ngTemplateOutlet"],[3,"masterData","placeholder","selectedValue","displayClose","data","onValueChange"],["appearance","outline",1,"form-field-class"],["type","text","maxlength","255","matInput","","formControlName","designation",3,"placeholder"],["type","number","min","0","matInput","","formControlName","amount",3,"placeholder","keydown","paste"],["type","number","min","0","matInput","","formControlName","oneTimeBonus",3,"placeholder","keydown","paste"],["formControlName","driveMonthYear"],[1,"date-picker"],["matInput","","formControlName","dateOfJoining","disabled","",3,"min","placeholder","matDatepicker"],[3,"for"],["matDatepickerToggleIcon","",1,"calendar-icon"],["disabled","false"],["dp",""],[1,"divider"],[1,"d-flex","flex-column","col-10","pl-0"],[3,"type","placeholder","masterData","selectedValues","displayClose","data","onValueChange"],["type","number","min","0","matInput","","formControlName","stipendAmount",3,"placeholder","keydown","paste"],["type","text","maxlength","255","matInput","","formControlName","internshipStartAt",3,"placeholder"],["type","text","maxlength","255","matInput","","formControlName","probationPeriod",3,"placeholder"],["type","number","min","0","matInput","","formControlName","leaveDays",3,"placeholder","keydown","paste"],["matInput","","formControlName","expiryDate","disabled","",3,"min","placeholder","matDatepicker"],[1,"d-flex","align-items-center","justify-content-between","col-5","p-0"],["formControlName","isApprovalActive",3,"change"],[3,"masterData","placeholder","selectedValue","displayClose","data","executeMasterOnChanges","onValueChange"],["formArrayName","approvers","cdkDropList","",1,"d-flex","flex-column","col-12","p-0",2,"gap","16px",3,"cdkDropListDropped"],[3,"formGroupName",4,"ngFor","ngForOf"],[3,"formGroupName"],["cdkDrag","",1,"d-flex","flex-row","align-items-center"],["class","form-label",4,"ngIf"],[3,"placeholder","masterData","selectedValue","displayClose","data","executeMasterOnChanges","onValueChange"],[1,"d-flex","flex-column","col-4","p-0"],["type","text","matInput","","formControlName","level","readonly","",3,"placeholder"],[1,"d-flex","flex-column","justify-content-center","col-2","p-0",2,"gap","12px"],["class","form-label d-flex justify-content-center",4,"ngIf"],["formControlName","isMandatoryApprover",1,"d-flex","justify-content-center"],[1,"d-flex","flex-row","col-2","p-0","approvers-icon"],["class","approvers-svg",3,"click",4,"ngIf"],["class","drag-icon","cdkDragHandle","",4,"ngIf"],["class","approvers-divider",4,"ngIf"],[1,"form-label","d-flex","justify-content-center"],[1,"approvers-svg",3,"click"],["width","24","height","25","viewBox","0 0 24 25","fill","none"],["id","mask0_9363_262427","maskUnits","userSpaceOnUse","x","0","y","0","width","24","height","25",2,"mask-type","alpha"],["y","0.5","width","24","height","24","fill","#D9D9D9"],["mask","url(#mask0_9363_262427)"],["d","M11 13.5H5V11.5H11V5.5H13V11.5H19V13.5H13V19.5H11V13.5Z","fill","#1C1B1F"],["width","24","height","24","viewBox","0 0 24 24","fill","none"],["id","mask0_9363_262414","maskUnits","userSpaceOnUse","x","0","y","0","width","24","height","24",2,"mask-type","alpha"],["mask","url(#mask0_9363_262414)"],["d","M7 21C6.45 21 5.97917 20.8042 5.5875 20.4125C5.19583 20.0208 5 19.55 5 19V6H4V4H9V3H15V4H20V6H19V19C19 19.55 18.8042 20.0208 18.4125 20.4125C18.0208 20.8042 17.55 21 17 21H7ZM17 6H7V19H17V6ZM9 17H11V8H9V17ZM13 17H15V8H13V17Z","fill","#1C1B1F"],["cdkDragHandle","",1,"drag-icon"],[1,"drag-icon"],[1,"approvers-divider"],[3,"mailFormGroup","mailFormConfig","templateMasterData","fromMailPlaceHolderMasterData","mailPlaceHolderMasterData","placeholdersMasterData","attachmentPlaceholderMasterData","attachmentCount","attachmentConfig","attachmentPath","filesUploaded","previewImg","companyLogo","showDocPreview","attachmentMasterDataForReplacement","previewToggleContent","attachmentChanges","htmlTemplateChanges"],[2,"color","#cf0001"]],template:function(e,t){1&e&&(u["\u0275\u0275elementStart"](0,"div",0),u["\u0275\u0275elementStart"](1,"div",1),u["\u0275\u0275elementStart"](2,"div",2),u["\u0275\u0275text"](3,"Send Offer"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](4,"div",3),u["\u0275\u0275elementStart"](5,"div"),u["\u0275\u0275namespaceSVG"](),u["\u0275\u0275elementStart"](6,"svg",4),u["\u0275\u0275element"](7,"path",5),u["\u0275\u0275element"](8,"path",6),u["\u0275\u0275element"](9,"path",7),u["\u0275\u0275element"](10,"path",8),u["\u0275\u0275element"](11,"path",9),u["\u0275\u0275element"](12,"path",10),u["\u0275\u0275element"](13,"path",11),u["\u0275\u0275element"](14,"path",12),u["\u0275\u0275element"](15,"path",13),u["\u0275\u0275element"](16,"path",14),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275namespaceHTML"](),u["\u0275\u0275elementStart"](17,"div",15),u["\u0275\u0275listener"]("click",(function(){return t.onClose(!1)})),u["\u0275\u0275elementStart"](18,"mat-icon",16),u["\u0275\u0275text"](19,"close"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275template"](20,Y,2,1,"div",17),u["\u0275\u0275template"](21,H,2,0,"div",18),u["\u0275\u0275template"](22,Dt,4,3,"div",19),u["\u0275\u0275elementStart"](23,"div",20),u["\u0275\u0275elementStart"](24,"div",21),u["\u0275\u0275listener"]("click",(function(){return 0==t.currentSection?t.changeSection(1):t.sendOffer()})),u["\u0275\u0275text"](25),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275template"](26,Ot,2,0,"ng-template",null,22,u["\u0275\u0275templateRefExtractor"])),2&e&&(u["\u0275\u0275advance"](20),u["\u0275\u0275property"]("ngIf",!t.isLoading),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf",t.isLoading),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf",!t.isLoading),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngStyle",u["\u0275\u0275pureFunction1"](5,_t,t.isLoading?"none":"")),u["\u0275\u0275advance"](1),u["\u0275\u0275textInterpolate1"](" ",0==t.currentSection?"Continue":"Send Offer"," "))},directives:[y.a,x.NgIf,x.NgStyle,x.NgForOf,S.c,a.w,a.n,x.NgTemplateOutlet,D.a,O.c,_.b,a.e,a.q,a.v,a.l,a.A,E.a,b.g,b.i,b.j,b.f,w.a,P.a,I.a,a.h,r.e,a.o,r.a,V.a,r.b,A.a],pipes:[L.a,z.a],styles:[".main-container[_ngcontent-%COMP%]{overflow:hidden}.main-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{width:78%;height:56px;padding:8px 24px;background-color:#f4f4f6;position:absolute;z-index:1}.main-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:18px;font-weight:700;color:#111434}.main-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{font-size:18px;width:18px;height:18px;color:#1c1b1f;cursor:pointer}.main-container[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]{width:78%;height:50px;padding:0 24px;background-color:#f7f9fb;position:absolute;z-index:1;top:56px;gap:16px}.main-container[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]   .space[_ngcontent-%COMP%]{cursor:pointer;gap:8px}.main-container[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]   .space[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{border:1px solid #515965;border-radius:50%;background-color:#eee;color:#515965}.main-container[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]   .space[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%], .main-container[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]   .space[_ngcontent-%COMP%]   .circle-selected[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-weight:700;font-size:12px;width:30px;height:30px;display:flex;align-items:center;justify-content:center}.main-container[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]   .space[_ngcontent-%COMP%]   .circle-selected[_ngcontent-%COMP%]{border:2px solid var(--atsprimaryColor);border-radius:50%;background-color:var(--atssecondaryColor2);color:var(--atsprimaryColor)}.main-container[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]   .space[_ngcontent-%COMP%]   .section-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:400;color:#515965}.main-container[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]   .space[_ngcontent-%COMP%]   .section-text-selected[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:700;color:var(--atsprimaryColor)}.main-container[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]   .border[_ngcontent-%COMP%]{width:40px;height:1px;border:1px dashed #515965!important}.main-container[_ngcontent-%COMP%]   .loading-screen[_ngcontent-%COMP%]{margin-top:20%}.main-container[_ngcontent-%COMP%]   .loading-screen[_ngcontent-%COMP%]     .green-spinner circle{stroke:var(--atsprimaryColor)!important}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]{padding:20px 24px;margin-top:106px;margin-bottom:56px}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .forms[_ngcontent-%COMP%]{width:100%;gap:16px}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .forms[_ngcontent-%COMP%]     .mat-slide-toggle-thumb{width:12px!important;height:12px!important;transform:translate(50%,50%)}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .forms[_ngcontent-%COMP%]     .mat-slide-toggle-bar{background-color:#dadce2;border-radius:15px!important;height:16px!important;width:32px!important}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .forms[_ngcontent-%COMP%]     .mat-slide-toggle-thumb-container{top:-4px!important;left:-5px!important}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .forms[_ngcontent-%COMP%]     .mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-bar{background-color:var(--atssecondaryColor)}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .forms[_ngcontent-%COMP%]     .mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-thumb{background-color:#fff}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .forms[_ngcontent-%COMP%]     .mat-checkbox-checked.mat-accent .mat-checkbox-background{background-color:var(--atssecondaryColor)!important}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .forms[_ngcontent-%COMP%]     .mat-checkbox-frame{border-color:#b9c0ca!important}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .forms[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]{color:#b9c0ca}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .forms[_ngcontent-%COMP%]   .label-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:500;color:#111434}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .forms[_ngcontent-%COMP%]   .description-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:400;color:#8b95a5}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .forms[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:12px;font-weight:400;color:#5f6c81}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .forms[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]{font-size:12px;font-family:var(--atsfontFamily)}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .forms[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-input-element{height:17px!important}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .forms[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-form-field{font-size:12px!important;font-family:var(--atsfontFamily)!important}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .forms[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-form-field-wrapper, .main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .forms[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-wrapper{padding-bottom:0!important}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .forms[_ngcontent-%COMP%]   .date-picker[_ngcontent-%COMP%]{display:flex;align-items:center;height:17px}.main-container[_ngcontent-%COMP%]   .footer[_ngcontent-%COMP%]{width:78%;height:56px;padding:8px 24px;background-color:#fff;position:absolute;z-index:1;bottom:0}.main-container[_ngcontent-%COMP%]   .footer[_ngcontent-%COMP%]   .button[_ngcontent-%COMP%]{background-color:var(--atsprimaryColor);border-radius:8px;padding:8px 14px;cursor:pointer;font-family:var(--atsfontFamily);font-size:14px;font-weight:700;color:#fff}.approvers-icon[_ngcontent-%COMP%]{align-items:center;gap:15px}.approvers-svg[_ngcontent-%COMP%]{opacity:.5;cursor:pointer}.drag-icon[_ngcontent-%COMP%]{width:16px;height:16px;font-size:16px;color:#111434;opacity:.5;cursor:move}"]}),e})()},pPzn:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var i=n("fXoL");let r=(()=>{class e{transform(e,t,n){var i;return!!(e&&t&&n)&&(null===(i=e.find(e=>e.key==t))||void 0===i?void 0:i[n])}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=i["\u0275\u0275definePipe"]({name:"columnCustomization",type:e,pure:!0}),e})()},zGnX:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var i=n("xG9w"),r=n("fXoL");let a=(()=>{class e{transform(e=[],t,n,r){let a=i.pluck(t.getRawValue(),r);return a=a.filter(e=>null!=e&&e!=n),e.filter(e=>!a.includes(e.id))}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=r["\u0275\u0275definePipe"]({name:"filterMasterData",type:e,pure:!1}),e})()}}]);