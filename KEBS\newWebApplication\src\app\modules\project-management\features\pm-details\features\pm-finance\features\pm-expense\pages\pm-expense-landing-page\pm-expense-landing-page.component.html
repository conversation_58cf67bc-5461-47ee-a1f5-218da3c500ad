<div class="expense-landing-container">
  <!-- Header Section -->
  <div class="header-section">
    <h2>Expense Management</h2>
    <div class="action-buttons">
      <button
        mat-raised-button
        color="primary"
        [disabled]="selectedExpenses.length === 0 || isLoading"
        (click)="openMilestoneCreationDialog()">
        <mat-icon>add</mat-icon>
        Create Milestone ({{ selectedExpenses.length }})
      </button>
    </div>
  </div>

  <!-- Summary Section -->
  <div class="summary-section" *ngIf="selectedExpenses.length > 0">
    <mat-card class="summary-card">
      <mat-card-content>
        <div class="summary-content">
          <div class="summary-item">
            <span class="label">Selected Items:</span>
            <span class="value">{{ selectedExpenses.length }}</span>
          </div>
          <div class="summary-item">
            <span class="label">Total Amount:</span>
            <span class="value total-amount">{{ getTotalSelectedAmount() | currency:'USD':'symbol':'1.2-2' }}</span>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Loading Spinner -->
  <div *ngIf="isLoading" class="loading-container">
    <mat-progress-spinner mode="indeterminate" diameter="50"></mat-progress-spinner>
    <p>Loading expense data...</p>
  </div>

  <!-- Data Table -->
  <div class="table-container" *ngIf="!isLoading">
    <dx-data-grid
      id="expenseDataGrid"
      [dataSource]="expenseData"
      [showBorders]="true"
      [rowAlternationEnabled]="true"
      [showColumnLines]="true"
      [showRowLines]="true"
      [allowColumnResizing]="true"
      [columnAutoWidth]="true"
      [hoverStateEnabled]="true"
      keyExpr="id"
      (onSelectionChanged)="onSelectionChanged($event)">

      <!-- Selection Column -->
      <dxo-selection
        mode="multiple"
        [selectAllMode]="'allPages'"
        [showCheckBoxesMode]="'always'">
      </dxo-selection>

      <!-- Paging -->
      <dxo-paging [pageSize]="20" [pageIndex]="0"></dxo-paging>

      <!-- Search Panel -->
      <dxo-search-panel [visible]="true" [width]="240" placeholder="Search expenses..."></dxo-search-panel>

      <!-- Header Filter -->
      <dxo-header-filter [visible]="true"></dxo-header-filter>

      <!-- Filter Row -->
      <dxo-filter-row [visible]="true"></dxo-filter-row>

      <!-- Export -->
      <dxo-export [enabled]="true" fileName="expense-data"></dxo-export>

      <!-- Columns -->
      <dxi-column
        dataField="id"
        caption="Expense ID"
        [width]="120"
        alignment="center">
      </dxi-column>

      <dxi-column
        dataField="total_amount"
        caption="Total Amount"
        dataType="number"
        format="currency"
        [width]="140"
        alignment="right">
      </dxi-column>

      <dxi-column
        dataField="amount_claimed"
        caption="Amount Claimed"
        dataType="number"
        format="currency"
        [width]="140"
        alignment="right">
      </dxi-column>

      <dxi-column
        dataField="created_on"
        caption="Created On"
        dataType="date"
        format="dd-MMM-yyyy"
        [width]="120"
        alignment="center">
      </dxi-column>

      <dxi-column
        dataField="requested_by"
        caption="Requested By"
        [minWidth]="150">
      </dxi-column>

      <dxi-column
        dataField="status_display"
        caption="Status"
        [width]="100"
        alignment="center"
        cellTemplate="statusTemplate">
      </dxi-column>

      <!-- Status Template -->
      <div *dxTemplate="let data of 'statusTemplate'">
        <span
          class="status-badge"
          [ngClass]="{
            'status-open': data.value === 'Open',
            'status-billing': data.value === 'Billing',
            'status-sow': data.value === 'SOW',
            'clickable': data.value === 'Open'
          }"
          [style.cursor]="data.value === 'Open' ? 'pointer' : 'default'"
          (click)="data.value === 'Open' ? onStatusClick(data.data) : null"
          [title]="data.value === 'Open' ? 'Click to change status' : ''">
          {{ data.value }}
        </span>
      </div>
    </dx-data-grid>
  </div>

  <!-- Empty State -->
  <div *ngIf="!isLoading && expenseData.length === 0" class="empty-state">
    <mat-icon class="empty-icon">receipt_long</mat-icon>
    <h3>No Expense Data Found</h3>
    <p>There are no billable expenses available for this project.</p>
  </div>
</div>
