(window.webpackJsonp=window.webpackJsonp||[]).push([[1007,209,765,821,822,861,981,983,987,990,991],{WlQZ:function(e,t,n){"use strict";n.r(t),n.d(t,"default",(function(){return r}));var i=n("hM+/"),r=function(e){Object(i.b)(e,"SpriteState")&&(e.transitionDuration=400),Object(i.b)(e,"Component")&&(e.rangeChangeDuration=500,e.interpolationDuration=500,e.sequencedInterpolation=!1,Object(i.b)(e,"SankeyDiagram")&&(e.sequencedInterpolation=!0),Object(i.b)(e,"FunnelSeries")&&(e.sequencedInterpolation=!0)),Object(i.b)(e,"Chart")&&(e.defaultState.transitionDuration=2e3,e.hiddenState.transitionDuration=1e3),Object(i.b)(e,"Tooltip")&&(e.animationDuration=400,e.defaultState.transitionDuration=400,e.hiddenState.transitionDuration=400),Object(i.b)(e,"Scrollbar")&&(e.animationDuration=500),Object(i.b)(e,"Series")&&(e.defaultState.transitionDuration=1e3,e.hiddenState.transitionDuration=700,e.hiddenState.properties.opacity=1,e.showOnInit=!0),Object(i.b)(e,"MapSeries")&&(e.hiddenState.properties.opacity=0),Object(i.b)(e,"PercentSeries")&&(e.hiddenState.properties.opacity=0),Object(i.b)(e,"FunnelSlice")&&(e.defaultState.transitionDuration=800,e.hiddenState.transitionDuration=1e3,e.hiddenState.properties.opacity=1),Object(i.b)(e,"Slice")&&(e.defaultState.transitionDuration=700,e.hiddenState.transitionDuration=1e3,e.hiddenState.properties.opacity=1),Object(i.b)(e,"Preloader")&&(e.hiddenState.transitionDuration=2e3),Object(i.b)(e,"Column")&&(e.defaultState.transitionDuration=700,e.hiddenState.transitionDuration=1e3,e.hiddenState.properties.opacity=1),Object(i.b)(e,"Column3D")&&(e.hiddenState.properties.opacity=0)}},hJL4:function(e,t,n){"use strict";n.d(t,"a",(function(){return h}));var i=n("mrSG"),r=n("XNiG"),o=n("xG9w"),a=n("fXoL"),s=n("tk/3"),l=n("LcQX"),d=n("XXEo"),c=n("flaP");let h=(()=>{class e{constructor(e,t,n,i){this.http=e,this.UtilityService=t,this.loginService=n,this.roleService=i,this.msg=new r.b,this.taskStatusColor=[],this.approveRequest=(e,t)=>this.http.post("/api/isa/request/approveResourceRequest",{requestDetails:e,approverOid:t}),this.rejectRequest=(e,t)=>this.http.post("/api/isa/request/rejectResourceRequest",{requestDetails:e,approverOid:t}),this.getTaskStatusColor()}getAllRequestsOfUser(e,t,n,i,r,o,a){let s=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getAllRequestsOfUser",{oid:e,statusId:t,statusCode:n,objectIds:i,skip:r,limit:o,filterConfig:a,orgIds:s})}getAllRoleAccess(){return o.where(this.roleService.roles,{application_id:139})}getRequestsBasedOnStatus(e,t,n,i,r,o,a){let s=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getRequestsBasedOnStatus",{oid:e,statusId:t,statusCode:n,objectIds:i,skip:r,limit:o,filterConfig:a,orgIds:s})}getRequestsForAwaitingApproval(e,t,n,i){return this.http.post("/api/isa/request/getRequestsForAwaitingApproval",{oid:e,skip:t,limit:n,filterConfig:i})}getObjectIdsBasedOnOid(e){return this.http.post("/api/isa/request/getObjectIdsBasedOnOid",{oid:e})}getStatusCountBasedOnOid(e,t,n,i){let r=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getStatusCountBasedOnOid",{oid:e,objectIds:t,dataTypeArray:n,filterConfig:i,orgIds:r})}getHeaderCount(e){return this.http.post("/api/isa/request/getHeaderCount",{oid:e})}getRequestBasedOnStatus(e,t){return this.http.post("/api/isa/request/getRequestBasedOnStatus",{oid:e,status:t})}getDataTypeArray(){return this.http.post("/api/isa/request/getISAStatusMasterDataUDRF",{})}sendMsg(e){this.msg.next(e)}getMsg(){return this.msg.asObservable()}getTaskStatusColor(){return new Promise((e,t)=>{this.http.post("/api/isa/request/getTaskStatusNameColor",{}).subscribe(t=>(this.taskStatusColor=t,e(t)),e=>{})})}getAllRequestsForRmg(e){return this.http.post("/api/isa/request/getAllRequestsForRmg",{filterConfig:e})}getTotalForRmg(e){return new Promise((t,n)=>{this.http.post("/api/isa/request/getTotalForRmg",{filterConfig:e}).subscribe(e=>t(e),e=>{})})}getDefaultISAStatusToDisplay(){return this.http.post("/api/isa/request/getISADefaultStatusToDisplay",{})}getCTCbreakUpForSkillSet(e){return new Promise((t,n)=>{this.http.post("/api/isa/request/getCTCbreakUpForSkillSet",e).subscribe(e=>t(e.data),e=>{n(e)})})}getCurrentUserRole(){return this.roleService&&this.roleService.roles&&this.roleService.roles.length>0?this.roleService.roles[0].role_id:null}getTemplateForUser(e){return this.http.post("/api/isa/request/getVisibleFormFieldsForUser",e)}saveAndApproveRequest(e){return this.http.post("/api/isa/request/updBudgetInfoByRequestId",e)}getVisibilityMatrix(e){return this.http.post("/api/isa/request/getAuthorzedFieldsByRoleId",e)}getISAAttachmentFromS3(e){return this.http.post("/api/isa/attachment/getISAAttachmentFromS3",{key:e})}getSkillType(){return this.http.post("/api/isa/request/getSkillType",{})}getSkillLevel(){return this.http.post("/api/isa/request/getSkillLevel",{})}getSkillExperience(){return this.http.post("/api/isa/request/getSkillExperience",{})}getSkillName(e){return this.http.post("/api/isa/request/getSkillName",{skill_type_id:e})}getIsaActivityReportDownload(){return this.http.post("/api/isa/request/getIsaActivityReportDownload",{})}getISASLAreport(e){return this.http.post("/api/isa/request/getISASLAreport",{filterConfig:e})}getISACustomReport(e){return this.http.post("/api/isa/request/getISACustomReport",{filterConfig:e})}getPositionType(){return this.http.post("/api/employee360/masterData/getPositionType",{})}calculateNetCostBasedOnPosition(e,t,n,r,a,s,l){return Object(i.c)(this,void 0,void 0,(function*(){let i;i=s&&s.length>1&&(yield this.getManpowerCostByOId(s,n,a,2))||(yield this.getManpowerCostBasedOnPosition(e,t,n,a,l));let d=yield this.getNonManpowerCost(t,n,r,a,2),c=yield this.getAllocatedCost(),h=0;h=(i?i.cost:0)+d.length>0?o.reduce(o.pluck(d,"cost"),(e,t)=>e+t,0):0;let p=c.length>0?o.reduce(o.pluck(c,"percentage"),(e,t)=>e+t,0):0;return{cost:h,currency:i&&i.currency_code?i.currency_code:"",manpowerCost:i,nonManpowerCost:d,allocatedCost:c,allocatedCostValue:h*(p/100)}}))}getManpowerCostBasedOnPosition(e,t,n,i,r){return new Promise((o,a)=>{this.http.post("/api/isa/configuration/getManpowerCostBasedOnPosition",{skillRoleId:e,nationalityId:t,locationGroupId:n,unit:i,position:r}).subscribe(e=>o(e),e=>(console.log(e),a(e)))})}getNonManpowerCost(e,t,n,i,r){return new Promise((o,a)=>{this.http.post("/api/project/getNonManpowerCost",{nationalityId:e,locationGroupId:t,locationId:n,unit:i,currency_id:r}).subscribe(e=>o(e),e=>(console.log(e),a(e)))})}getAllocatedCost(){return new Promise((e,t)=>{this.http.post("/api/project/getAllocatedCost","").subscribe(t=>e(t),e=>(console.log(e),t(e)))})}getManpowerCostByOId(e,t,n,i){return new Promise((r,o)=>{this.http.post("/api/project/getEmployeeSalary",{oid:e,location_group_id:t,unit_id:n,currency_id:i}).subscribe(e=>r(e),e=>(console.log(e),o(e)))})}getSkillNameMaster(){return this.http.post("/api/isa/configuration/getSkillName",{})}getVendorOid(e,t){return this.http.post("/api/isa/request/getVendorOid",{vendorNames:e,docID:t})}getVendorList(e,t){return this.http.post("/api/isa/request/getVendorList",{documentID:e,limitval:t})}removeVendor(e,t){return this.http.post("/api/isa/request/removeVendors",{vendorOid:e,documentID:t})}checkVendorOrNot(){return this.http.post("/api/isa/request/checkVendorOrNot",{})}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275inject"](s.c),a["\u0275\u0275inject"](l.a),a["\u0275\u0275inject"](d.a),a["\u0275\u0275inject"](c.a))},e.\u0275prov=a["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},tKbL:function(e,t,n){"use strict";n.d(t,"a",(function(){return s}));var i=n("xG9w"),r=n("fXoL"),o=n("tk/3"),a=n("BVzC");let s=(()=>{class e{constructor(e,t){this.http=e,this._ErrorService=t}checkIfWfNeedsToBeTriggered(e){return new Promise((t,n)=>{this.http.post("/api/wf/getApplicationWfConfigDetails",{application_id:e.applicationId}).subscribe(r=>{if(!("S"==r.messType&&r.data.length>0))return t({error:!1,messText:"Workflow Config does not exist for application !",data:null});{let o=i.filter(r.data,t=>t.trigger_object_value instanceof Array?t.trigger_object_level==e.triggerObjectLevel&&i.contains(t.trigger_object_value,e.triggerObjectValue):t.trigger_object_level==e.triggerObjectLevel&&t.trigger_object_value==e.triggerObjectValue);if(!(o&&o.length>0))return t({error:!1,messText:"Workflow Config does not exist !",data:null});this.triggerWfBasedOnConfig(o[0],e).then(e=>t({error:!1,messText:"Workflow created Successfully !",data:e})).catch(e=>(this._ErrorService.userErrorAlert(e.error?e.error.errorCode:e.errorCode,"Error creating workflow !",e.error?e.error.errMessage:e.errMessage),n({error:!0,messText:e.messText,data:null})))}},e=>(this._ErrorService.userErrorAlert(e.error?e.error.errorCode:e.errorCode,"Error getting application workflow configs !",e.error?e.error.errMessage:e.errMessage),n({error:!0,messText:e,data:null})))})}triggerWfBasedOnConfig(e,t){return new Promise((n,i)=>{this.http.post("/api/wf/triggerWfBasedOnConfig",{current_wf_config:e,app_details:t}).subscribe(e=>"S"==e.messType&&e.data?n(e.data):i(e),e=>i(e))})}getWfApproverDetails(e,t){return this.http.post("/api/wf/getWfApproverDetails",{wf_header_id:t,wf_plugin_id:e})}updateWfBasedOnConfig(e,t,n,i,r,o){return this.http.post("/api/wf/updateWfBasedOnConfig",{wf_update_action:t,wf_plugin_id:e,approver_oid:n,approver_comments:i,isAdmin:r,adminOId:o})}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275inject"](o.c),r["\u0275\u0275inject"](a.a))},e.\u0275prov=r["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},ucYs:function(e,t,n){"use strict";n.d(t,"a",(function(){return l}));var i=n("mrSG"),r=n("xG9w"),o=n("fXoL"),a=n("tk/3"),s=n("BVzC");let l=(()=>{class e{constructor(e,t){this.http=e,this.errorService=t,this.workflowStatusList=[],this.getWorkflowStatusList()}getWorkflowStatusList(){this.http.post("/api/wfPrimary/getWorkflowStatusList","").subscribe(e=>{this.workflowStatusList=e.data},e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server While Getting Workflow Status List",e&&e.params?e.params:e&&e.error?e.error.params:{})})}getWorkflowProperties(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/getWorkflowProperties",{applicationId:e}).subscribe(e=>t(e),e=>n(e))})}getWorkflowPropertiesByWorkflowId(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/getWorkflowPropertiesByWorkflowId",{workflowId:e}).subscribe(e=>t(e),e=>n(e))})}getApproversHierarchy(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/getApproversHierarchy",e).subscribe(e=>t(e),e=>n(e))})}createWorkflowItems(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/createWorkflowItems",e).subscribe(e=>t(e),e=>n(e))})}getWorkflowDetails(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/getWorkflowDetails",{workflowId:e}).subscribe(e=>t(e),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while getting workflow details",e&&e.params?e.params:e&&e.error?e.error.params:{})})})}formatApproversHierarchy(e,t){return Object(i.c)(this,void 0,void 0,(function*(){0==e.length&&t.cc0&&t.cc0.appr0&&t.cc0.appr0.length>0&&e.push([{approvalType:"I",purposeOfInsertingIntoArray:"Just to ensure that 'I' type data is used, if it exists"}]);for(let n=0;n<e.length;n++){let i=[],o=r.keys(t["cc"+n]);for(let r=0;r<o.length;r++)for(let a=0;a<t["cc"+n][o[r]].length;a++){let s={name:t["cc"+n][o[r]][a].DELEGATE_NAME,oid:t["cc"+n][o[r]][a].DELEGATE_OID,level:r+1,designation:t["cc"+n][o[r]][a].DELEGATE_DESIGNATION_NAME,is_delegated:t["cc"+n][o[r]][a].IS_DELEGATED,role:t["cc"+n][o[r]][a].DELEGATE_ROLE_NAME};if(1==t["cc"+n][o[r]][a].IS_DELEGATED&&(s.delegated_by={name:t["cc"+n][o[r]][a].APPROVER_NAME,oid:t["cc"+n][o[r]][a].APPROVER_OID,level:r+1,designation:t["cc"+n][o[r]][a].APPROVER_DESIGNATION_NAME}),i.push(s),n==e.length-1&&r==o.length-1&&a==t["cc"+n][o[r]].length-1)return i}}}))}storeComments(e,t,n){return new Promise((i,r)=>{this.http.post("/api/wfPrimary/updateComments",{workflowHeaderId:e,newComments:t,commentor:n}).subscribe(e=>i(e),e=>(this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server Error while Updating Workflow Comments",e&&e.params?e.params:e&&e.error?e.error.params:{}),r(e)))})}updateWorkflowItems(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/updateWorkflowItems",e).subscribe(e=>t(e),e=>(console.log(e),n(e)))})}formatApproversHierarchyForOpportunityApprovalActivity(e){return Object(i.c)(this,void 0,void 0,(function*(){for(let t=0;t<1;t++){let n=[],i=r.keys(e["cc"+t]);for(let r=0;r<i.length;r++)for(let o=0;o<e["cc"+t][i[r]].length;o++){let a={name:e["cc"+t][i[r]][o].DELEGATE_NAME,oid:e["cc"+t][i[r]][o].DELEGATE_OID,level:e["cc"+t][i[r]][o].APPROVAL_ORDER,designation:e["cc"+t][i[r]][o].DELEGATE_DESIGNATION_NAME,is_delegated:e["cc"+t][i[r]][o].IS_DELEGATED};if(1==e["cc"+t][i[r]][o].IS_DELEGATED&&(a.delegated_by={name:e["cc"+t][i[r]][o].APPROVER_NAME,oid:e["cc"+t][i[r]][o].APPROVER_OID,level:e["cc"+t][i[r]][o].APPROVAL_ORDER,designation:e["cc"+t][i[r]][o].APPROVER_DESIGNATION_NAME}),n.push(a),r==i.length-1&&o==e["cc"+t][i[r]].length-1)return n}}}))}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275inject"](a.c),o["\u0275\u0275inject"](s.a))},e.\u0275prov=o["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},w76M:function(e,t,n){"use strict";n.d(t,"a",(function(){return d})),n.d(t,"b",(function(){return c}));var i=n("jhN1"),r=n("fXoL"),o=n("oHs6"),a=n("PVOt"),s=n("6t9p");const l=["*"];let d=(()=>{let e=class extends a.b{constructor(e,t,n,i,r,o,a,s){super(e,t,n,i,a,s),this._watcherHelper=i,this._idh=r,this._createEventEmitters([{subscribe:"contentReady",emit:"onContentReady"},{subscribe:"disposing",emit:"onDisposing"},{subscribe:"hidden",emit:"onHidden"},{subscribe:"hiding",emit:"onHiding"},{subscribe:"initialized",emit:"onInitialized"},{subscribe:"optionChanged",emit:"onOptionChanged"},{subscribe:"resize",emit:"onResize"},{subscribe:"resizeEnd",emit:"onResizeEnd"},{subscribe:"resizeStart",emit:"onResizeStart"},{subscribe:"showing",emit:"onShowing"},{subscribe:"shown",emit:"onShown"},{subscribe:"titleRendered",emit:"onTitleRendered"},{emit:"accessKeyChange"},{emit:"animationChange"},{emit:"closeOnOutsideClickChange"},{emit:"containerChange"},{emit:"contentTemplateChange"},{emit:"deferRenderingChange"},{emit:"disabledChange"},{emit:"dragEnabledChange"},{emit:"elementAttrChange"},{emit:"focusStateEnabledChange"},{emit:"fullScreenChange"},{emit:"heightChange"},{emit:"hintChange"},{emit:"hoverStateEnabledChange"},{emit:"maxHeightChange"},{emit:"maxWidthChange"},{emit:"minHeightChange"},{emit:"minWidthChange"},{emit:"positionChange"},{emit:"resizeEnabledChange"},{emit:"rtlEnabledChange"},{emit:"shadingChange"},{emit:"shadingColorChange"},{emit:"showCloseButtonChange"},{emit:"showTitleChange"},{emit:"tabIndexChange"},{emit:"titleChange"},{emit:"titleTemplateChange"},{emit:"toolbarItemsChange"},{emit:"visibleChange"},{emit:"widthChange"}]),this._idh.setHost(this),o.setHost(this)}get accessKey(){return this._getOption("accessKey")}set accessKey(e){this._setOption("accessKey",e)}get animation(){return this._getOption("animation")}set animation(e){this._setOption("animation",e)}get closeOnOutsideClick(){return this._getOption("closeOnOutsideClick")}set closeOnOutsideClick(e){this._setOption("closeOnOutsideClick",e)}get container(){return this._getOption("container")}set container(e){this._setOption("container",e)}get contentTemplate(){return this._getOption("contentTemplate")}set contentTemplate(e){this._setOption("contentTemplate",e)}get deferRendering(){return this._getOption("deferRendering")}set deferRendering(e){this._setOption("deferRendering",e)}get disabled(){return this._getOption("disabled")}set disabled(e){this._setOption("disabled",e)}get dragEnabled(){return this._getOption("dragEnabled")}set dragEnabled(e){this._setOption("dragEnabled",e)}get elementAttr(){return this._getOption("elementAttr")}set elementAttr(e){this._setOption("elementAttr",e)}get focusStateEnabled(){return this._getOption("focusStateEnabled")}set focusStateEnabled(e){this._setOption("focusStateEnabled",e)}get fullScreen(){return this._getOption("fullScreen")}set fullScreen(e){this._setOption("fullScreen",e)}get height(){return this._getOption("height")}set height(e){this._setOption("height",e)}get hint(){return this._getOption("hint")}set hint(e){this._setOption("hint",e)}get hoverStateEnabled(){return this._getOption("hoverStateEnabled")}set hoverStateEnabled(e){this._setOption("hoverStateEnabled",e)}get maxHeight(){return this._getOption("maxHeight")}set maxHeight(e){this._setOption("maxHeight",e)}get maxWidth(){return this._getOption("maxWidth")}set maxWidth(e){this._setOption("maxWidth",e)}get minHeight(){return this._getOption("minHeight")}set minHeight(e){this._setOption("minHeight",e)}get minWidth(){return this._getOption("minWidth")}set minWidth(e){this._setOption("minWidth",e)}get position(){return this._getOption("position")}set position(e){this._setOption("position",e)}get resizeEnabled(){return this._getOption("resizeEnabled")}set resizeEnabled(e){this._setOption("resizeEnabled",e)}get rtlEnabled(){return this._getOption("rtlEnabled")}set rtlEnabled(e){this._setOption("rtlEnabled",e)}get shading(){return this._getOption("shading")}set shading(e){this._setOption("shading",e)}get shadingColor(){return this._getOption("shadingColor")}set shadingColor(e){this._setOption("shadingColor",e)}get showCloseButton(){return this._getOption("showCloseButton")}set showCloseButton(e){this._setOption("showCloseButton",e)}get showTitle(){return this._getOption("showTitle")}set showTitle(e){this._setOption("showTitle",e)}get tabIndex(){return this._getOption("tabIndex")}set tabIndex(e){this._setOption("tabIndex",e)}get title(){return this._getOption("title")}set title(e){this._setOption("title",e)}get titleTemplate(){return this._getOption("titleTemplate")}set titleTemplate(e){this._setOption("titleTemplate",e)}get toolbarItems(){return this._getOption("toolbarItems")}set toolbarItems(e){this._setOption("toolbarItems",e)}get visible(){return this._getOption("visible")}set visible(e){this._setOption("visible",e)}get width(){return this._getOption("width")}set width(e){this._setOption("width",e)}get toolbarItemsChildren(){return this._getOption("toolbarItems")}set toolbarItemsChildren(e){this.setChildren("toolbarItems",e)}_createInstance(e,t){return new o.a(e,t)}ngOnDestroy(){this._destroyWidget()}ngOnChanges(e){super.ngOnChanges(e),this.setupChanges("toolbarItems",e)}setupChanges(e,t){e in this._optionsToUpdate||this._idh.setup(e,t)}ngDoCheck(){this._idh.doCheck("toolbarItems"),this._watcherHelper.checkWatchers(),super.ngDoCheck(),super.clearChangedOptions()}_setOption(e,t){let n=this._idh.setupSingle(e,t),i=null!==this._idh.getChanges(e,t);(n||i)&&super._setOption(e,t)}};return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](r.ElementRef),r["\u0275\u0275directiveInject"](r.NgZone),r["\u0275\u0275directiveInject"](a.e),r["\u0275\u0275directiveInject"](a.j),r["\u0275\u0275directiveInject"](a.g),r["\u0275\u0275directiveInject"](a.i),r["\u0275\u0275directiveInject"](i.h),r["\u0275\u0275directiveInject"](r.PLATFORM_ID))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["dx-popup"]],contentQueries:function(e,t,n){if(1&e&&r["\u0275\u0275contentQuery"](n,s.L,!1),2&e){let e;r["\u0275\u0275queryRefresh"](e=r["\u0275\u0275loadQuery"]())&&(t.toolbarItemsChildren=e)}},inputs:{accessKey:"accessKey",animation:"animation",closeOnOutsideClick:"closeOnOutsideClick",container:"container",contentTemplate:"contentTemplate",deferRendering:"deferRendering",disabled:"disabled",dragEnabled:"dragEnabled",elementAttr:"elementAttr",focusStateEnabled:"focusStateEnabled",fullScreen:"fullScreen",height:"height",hint:"hint",hoverStateEnabled:"hoverStateEnabled",maxHeight:"maxHeight",maxWidth:"maxWidth",minHeight:"minHeight",minWidth:"minWidth",position:"position",resizeEnabled:"resizeEnabled",rtlEnabled:"rtlEnabled",shading:"shading",shadingColor:"shadingColor",showCloseButton:"showCloseButton",showTitle:"showTitle",tabIndex:"tabIndex",title:"title",titleTemplate:"titleTemplate",toolbarItems:"toolbarItems",visible:"visible",width:"width"},outputs:{onContentReady:"onContentReady",onDisposing:"onDisposing",onHidden:"onHidden",onHiding:"onHiding",onInitialized:"onInitialized",onOptionChanged:"onOptionChanged",onResize:"onResize",onResizeEnd:"onResizeEnd",onResizeStart:"onResizeStart",onShowing:"onShowing",onShown:"onShown",onTitleRendered:"onTitleRendered",accessKeyChange:"accessKeyChange",animationChange:"animationChange",closeOnOutsideClickChange:"closeOnOutsideClickChange",containerChange:"containerChange",contentTemplateChange:"contentTemplateChange",deferRenderingChange:"deferRenderingChange",disabledChange:"disabledChange",dragEnabledChange:"dragEnabledChange",elementAttrChange:"elementAttrChange",focusStateEnabledChange:"focusStateEnabledChange",fullScreenChange:"fullScreenChange",heightChange:"heightChange",hintChange:"hintChange",hoverStateEnabledChange:"hoverStateEnabledChange",maxHeightChange:"maxHeightChange",maxWidthChange:"maxWidthChange",minHeightChange:"minHeightChange",minWidthChange:"minWidthChange",positionChange:"positionChange",resizeEnabledChange:"resizeEnabledChange",rtlEnabledChange:"rtlEnabledChange",shadingChange:"shadingChange",shadingColorChange:"shadingColorChange",showCloseButtonChange:"showCloseButtonChange",showTitleChange:"showTitleChange",tabIndexChange:"tabIndexChange",titleChange:"titleChange",titleTemplateChange:"titleTemplateChange",toolbarItemsChange:"toolbarItemsChange",visibleChange:"visibleChange",widthChange:"widthChange"},features:[r["\u0275\u0275ProvidersFeature"]([a.e,a.j,a.i,a.g]),r["\u0275\u0275InheritDefinitionFeature"],r["\u0275\u0275NgOnChangesFeature"]],ngContentSelectors:l,decls:1,vars:0,template:function(e,t){1&e&&(r["\u0275\u0275projectionDef"](),r["\u0275\u0275projection"](0))},encapsulation:2}),e})(),c=(()=>{let e=class{};return e.\u0275mod=r["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=r["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[s.bb,s.Gc,s.Vd,s.vd,s.hb,s.lb,s.sb,s.id,s.jd,s.M,a.c,a.f,i.b],s.bb,s.Gc,s.Vd,s.vd,s.hb,s.lb,s.sb,s.id,s.jd,s.M,a.f]}),e})()},y176:function(e,t,n){"use strict";n.r(t),n.d(t,"ComplianceOverviewModule",(function(){return q}));var i=n("ofXK"),r=n("tyNb"),o=n("fXoL"),a=n("LcQX"),s=n("BVzC"),l=n("WGBV"),d=n("GnQ3"),c=n("XXEo"),h=n("R/Xf"),p=n("JLuW"),m=n("tKbL"),u=n("NFeN"),g=n("1jcm"),f=n("Qu3c"),b=n("cclQ"),C=n("xJfa"),v=n("WlQZ"),S=n("Ra5f"),O=n("wd/R"),w=n("1G5W"),y=n("XNiG");const _=["chart"];let E=(()=>{class e{constructor(e,t){this.zone=e,this._cmsService=t,this.total=0,this.isSummaryValueChart=!0,this.monthBased=!1,this.statusBased=!1,this.statusName="Open",this.startDate=O().startOf("year").format(),this.endDate=O().endOf("year").format(),this._onDestroy=new y.b,this.summaryChartValueData=[],this.summaryChartCountData=[],this.summaryData=[]}ngOnInit(){this.resolveSubscription()}resolveSubscription(){this._cmsService.getMonthObservable.pipe(Object(w.a)(this._onDestroy)).subscribe(e=>{if(console.log("hi"),e&&Object.keys(e).length>0){console.log("data",e.data);let t=e.data;1==t.status?(console.log("status"),this.statusName=t.isChecked?"Approved by Manager":"Open",this.ngAfterViewInit()):1==t.month&&(console.log("month"),t.isChecked?(this.startDate=O().startOf("month").format(),this.endDate=O().endOf("month").format()):(this.startDate=O().startOf("year").format(),this.endDate=O().endOf("year").format()),this.ngAfterViewInit())}})}ngAfterViewInit(){let e={start_date:this.startDate,end_date:this.endDate,status_name:this.statusName};console.log(e,"data for api"),this._cmsService.getComplianceByCountry(e).subscribe(e=>{console.log(e.data,"Request for Closure"),this.summaryData=e.data,this.total=0;for(let t=0;t<this.summaryData.length;t++)this.total+=this.summaryData[t].count;this.zone.runOutsideAngular(()=>{b.useTheme(v.default),b.useTheme(S.a),b.options.autoDispose=!0,this.summaryChart=b.create(this.chartDom.nativeElement,C.PieChart),this.summaryChart.data=this.summaryData;let e=this.summaryChart.series.push(new C.PieSeries);this.summaryChart.radius=100,this.summaryChart.hideCredits=!0;let t=this.summaryChart.seriesContainer.createChild(b.Label);t.text=this.total,t.horizontalCenter="middle",t.verticalCenter="middle",t.fontSize=20,e.dataFields.value="count",e.dataFields.category="country",this.summaryChart.innerRadius=75,e.hiddenState.properties.endAngle=-90,e.labels.template.disabled=!0,e.ticks.template.disabled=!0,this.summaryChart.legend=new C.Legend,this.summaryChart.legend.position="bottom",this.summaryChart.legend.valueLabels.template.align="center",this.summaryChart.legend.maxHeight=80,this.summaryChart.legend.scrollable=!0,this.summaryChart.legend.labels.template.fill=b.color("#727170"),this.summaryChart.legend.itemContainers.template.paddingLeft=25,this.summaryChart.legend.itemContainers.template.paddingBottom=15;let n=this.summaryChart.legend.markers.template.children.getIndex(0);n.cornerRadius(12,12,12,12),n.strokeWidth=2,n.strokeOpacity=1;let i=this.summaryChart.legend.markers.template;i.width=14,i.height=14})},e=>{console.log(e)})}ngOnDestroy(){this.zone.runOutsideAngular(()=>{this.chart&&this.chart.dispose()})}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](o.NgZone),o["\u0275\u0275directiveInject"](h.a))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-pie-chart"]],viewQuery:function(e,t){if(1&e&&o["\u0275\u0275staticViewQuery"](_,!0),2&e){let e;o["\u0275\u0275queryRefresh"](e=o["\u0275\u0275loadQuery"]())&&(t.chartDom=e.first)}},decls:2,vars:0,consts:[[2,"height","21rem"],["chart",""]],template:function(e,t){1&e&&o["\u0275\u0275element"](0,"div",0,1)},styles:[""]}),e})();const I=["chart"];let x=(()=>{class e{constructor(e,t){this.zone=e,this._cmsService=t,this.isSummaryValueChart=!0}ngOnInit(){}ngAfterViewInit(){this._cmsService.getComplianceByStatusForDB().subscribe(e=>{console.log(e.data,"getComplianceByStatusForDB"),this.zone.runOutsideAngular(()=>{b.useTheme(v.default);let t=b.create(this.chartDom.nativeElement,C.XYChart);t.data=e.data,t.hiddenState.properties.opacity=0,t.zoomOutButton.disabled=!0;let n=t.xAxes.push(new C.CategoryAxis);n.dataFields.category="status",n.renderer.grid.template.strokeOpacity=0,n.renderer.minGridDistance=30,n.renderer.cellStartLocation=.1,n.renderer.cellEndLocation=.9,n.renderer.grid.template.location=0,t.yAxes.push(new C.ValueAxis),t.colors.list=[b.color("#845EC2"),b.color("#d65d5f"),b.color("#FF6F91"),b.color("#FF9671"),b.color("#FFC75F"),b.color("#F9F871")];var i=t.series.push(new C.ColumnSeries);i.dataFields.valueY="count",i.dataFields.categoryX="status",i.sequencedInterpolation=!0;let r=i.columns.template;r.width=30,r.column.cornerRadiusTopLeft=20,r.column.cornerRadiusTopRight=20,r.strokeOpacity=0,r.adapter.add("fill",(function(e,n){return t.colors.getIndex(n.dataItem.index)})),r.adapter.add("stroke",(function(e,n){return t.colors.getIndex(n.dataItem.index)}));let o=new C.XYCursor;o.behavior="panX",t.cursor=o,o.lineX.disabled=!0,i.columns.template.tooltipText="[bold]Status Name : {status}[/]\n          [bold]Count : {count}\n          ",i.columns.template.tooltipY=0,i.tooltip.pointerOrientation="vertical",i.tooltip.background.cornerRadius=20,i.tooltip.background.strokeOpacity=0,i.tooltip.label.minWidth=40,i.tooltip.label.minHeight=40;var a=new C.Legend;a.parent=t.chartContainer,a.marginTop=20,i.events.on("ready",(function(e){var t=[];console.log("namess",i.columns),i.columns.each((function(e){t.push({name:e.dataItem.categories.categoryX,fill:e.fill,columnDataItem:e.dataItem})})),a.data=t})),a.itemContainers.template.events.on("hit",(function(e){console.log("Clicked on ",e.target.dataItem.dataContext.columnDataItem.hide()),console.log("item",e.target),e.target.isActive?e.target.dataItem.dataContext.columnDataItem.show():e.target.dataItem.dataContext.columnDataItem.hide()})),a.itemContainers.template.events.on("over",(function(e){e.target.dataItem.dataContext.columnDataItem.column.isHover=!0,e.target.dataItem.dataContext.columnDataItem.column.showTooltip()})),a.itemContainers.template.events.on("out",(function(e){e.target.dataItem.dataContext.columnDataItem.column.isHover=!1,e.target.dataItem.dataContext.columnDataItem.column.hideTooltip()}))})},e=>{console.log(e)})}ngOnDestroy(){this.zone.runOutsideAngular(()=>{this.chart&&this.chart.dispose()})}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](o.NgZone),o["\u0275\u0275directiveInject"](h.a))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-bar-chart"]],viewQuery:function(e,t){if(1&e&&o["\u0275\u0275staticViewQuery"](I,!0),2&e){let e;o["\u0275\u0275queryRefresh"](e=o["\u0275\u0275loadQuery"]())&&(t.chartDom=e.first)}},decls:2,vars:0,consts:[[2,"height","21rem"],["chart",""]],template:function(e,t){1&e&&o["\u0275\u0275element"](0,"div",0,1)},styles:[""]}),e})();var k=n("dlKe"),D=n("bTqV");function A(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div",28),o["\u0275\u0275elementStart"](1,"div",16),o["\u0275\u0275text"](2),o["\u0275\u0275pipe"](3,"date"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](4,"div",29),o["\u0275\u0275text"](5),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](6,"div",18),o["\u0275\u0275elementStart"](7,"button",30),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](e);const n=t.$implicit;return o["\u0275\u0275nextContext"](2).openInNewTab(n._id)})),o["\u0275\u0275elementStart"](8,"mat-icon",31),o["\u0275\u0275text"](9,"visibility"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275element"](10,"div",19),o["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate"](o["\u0275\u0275pipeBind2"](3,2,e.end_date,"dd-MMM-yy")),o["\u0275\u0275advance"](3),o["\u0275\u0275textInterpolate1"](" ",e.name," ")}}function R(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div",32),o["\u0275\u0275elementStart"](1,"div",16),o["\u0275\u0275text"](2),o["\u0275\u0275pipe"](3,"date"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](4,"div",29),o["\u0275\u0275text"](5),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](6,"div",18),o["\u0275\u0275elementStart"](7,"button",30),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](e);const n=t.$implicit;return o["\u0275\u0275nextContext"](2).openInNewTab(n._id)})),o["\u0275\u0275elementStart"](8,"mat-icon",31),o["\u0275\u0275text"](9,"visibility"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275element"](10,"div",19),o["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate"](o["\u0275\u0275pipeBind2"](3,2,e.end_date,"dd-MMM-yy")),o["\u0275\u0275advance"](3),o["\u0275\u0275textInterpolate1"](" ",e.name," ")}}function T(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"div",33),o["\u0275\u0275elementStart"](1,"span",34),o["\u0275\u0275elementStart"](2,"mat-icon",35),o["\u0275\u0275text"](3,"thumb_down"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275text"](4," Sorry! No Updates "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]())}function j(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",37),o["\u0275\u0275elementStart"](1,"div",38),o["\u0275\u0275elementStart"](2,"span",39),o["\u0275\u0275text"](3,"Compliance Name :"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](4,"span",40),o["\u0275\u0275text"](5),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](6,"div",38),o["\u0275\u0275elementStart"](7,"span",39),o["\u0275\u0275text"](8,"Action type :"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](9,"span",41),o["\u0275\u0275text"](10),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](11,"div",38),o["\u0275\u0275elementStart"](12,"span",39),o["\u0275\u0275text"](13,"Action By:"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](14,"span",42),o["\u0275\u0275text"](15),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](16,"div",43),o["\u0275\u0275elementStart"](17,"span",39),o["\u0275\u0275text"](18,"Action date :"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](19,"span",44),o["\u0275\u0275text"](20),o["\u0275\u0275pipe"](21,"date"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;o["\u0275\u0275advance"](4),o["\u0275\u0275property"]("matTooltip",null==e?null:e.before_upd),o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate"](e.compliance_name),o["\u0275\u0275advance"](4),o["\u0275\u0275property"]("matTooltip",null==e?null:e.after_upd),o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate"](e.action),o["\u0275\u0275advance"](4),o["\u0275\u0275property"]("matTooltip",null==e?null:e.created_by_name),o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" ",null==e?null:e.created_by_name," "),o["\u0275\u0275advance"](5),o["\u0275\u0275textInterpolate"](o["\u0275\u0275pipeBind2"](21,7,null==e?null:e.created_at,"dd-MMM-yy hh:mm a"))}}function P(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div"),o["\u0275\u0275template"](1,j,22,10,"div",36),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngForOf",e.updates)}}function M(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div",1),o["\u0275\u0275elementStart"](1,"div",2),o["\u0275\u0275elementStart"](2,"div",3),o["\u0275\u0275elementStart"](3,"div",4),o["\u0275\u0275elementStart"](4,"div",5),o["\u0275\u0275elementStart"](5,"div",6),o["\u0275\u0275elementStart"](6,"span",7),o["\u0275\u0275elementStart"](7,"mat-icon",8),o["\u0275\u0275text"](8,"pie_chart"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275text"](9," Overall Compliance "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](10,"div",9),o["\u0275\u0275elementStart"](11,"mat-slide-toggle",10),o["\u0275\u0275listener"]("change",(function(t){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"]().onChangeMonth(t)})),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](12,"mat-slide-toggle",11),o["\u0275\u0275listener"]("change",(function(t){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"]().onChangeStatus(t)})),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275element"](13,"app-pie-chart"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](14,"div",12),o["\u0275\u0275element"](15,"app-bar-chart"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](16,"div",13),o["\u0275\u0275elementStart"](17,"div",3),o["\u0275\u0275elementStart"](18,"div",4),o["\u0275\u0275elementStart"](19,"div",14),o["\u0275\u0275elementStart"](20,"span"),o["\u0275\u0275text"](21,"Request For Closure"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](22,"div",15),o["\u0275\u0275elementStart"](23,"div",16),o["\u0275\u0275text"](24,"Date"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](25,"div",17),o["\u0275\u0275text"](26,"Compliance Name"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](27,"div",18),o["\u0275\u0275text"](28,"Action"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275element"](29,"div",19),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](30,"div",20),o["\u0275\u0275listener"]("scrolled",(function(){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"]().onRFCScroll()})),o["\u0275\u0275template"](31,A,11,5,"div",21),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](32,"div",22),o["\u0275\u0275elementStart"](33,"div",23),o["\u0275\u0275elementStart"](34,"div",14),o["\u0275\u0275elementStart"](35,"span"),o["\u0275\u0275text"](36,"Open Compliance"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](37,"div",15),o["\u0275\u0275elementStart"](38,"div",16),o["\u0275\u0275text"](39,"Date"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](40,"div",17),o["\u0275\u0275text"](41,"Compliance Name"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](42,"div",18),o["\u0275\u0275text"](43,"Action"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275element"](44,"div",19),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](45,"div",20),o["\u0275\u0275listener"]("scrolled",(function(){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"]().onOpenScroll()})),o["\u0275\u0275template"](46,R,11,5,"div",24),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](47,"div",23),o["\u0275\u0275elementStart"](48,"div",14),o["\u0275\u0275elementStart"](49,"span"),o["\u0275\u0275text"](50,"Activities"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](51,"div",25),o["\u0275\u0275template"](52,T,5,0,"div",26),o["\u0275\u0275template"](53,P,2,1,"div",27),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](11),o["\u0275\u0275property"]("matTooltip",e.checkMonth?"Disable Month View":"Month view"),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("matTooltip",e.checkStatus?"Disable Completed Status":"Completed Status"),o["\u0275\u0275advance"](18),o["\u0275\u0275property"]("infiniteScrollDistance",2)("scrollWindow",!1),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngForOf",e.pending),o["\u0275\u0275advance"](14),o["\u0275\u0275property"]("infiniteScrollDistance",2)("scrollWindow",!1),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngForOf",e.overdue),o["\u0275\u0275advance"](6),o["\u0275\u0275property"]("ngIf",0==e.updates.length),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",e.updates.length>0)}}const N=[{path:"",component:(()=>{class e{constructor(e,t,n,i,r,o,a,s,l){this.zone=e,this._util=t,this._ErrorService=n,this.accountService=i,this._udrfService=r,this._loginService=o,this._cmsService=a,this._lazyService=s,this._wfPlugin=l,this.isLoading=!1,this.isOverview=!1,this.checkMonth=!1,this.checkStatus=!1,this.isSummaryValueChart=!0,this.updates=[],this.pending=[],this.overdue=[],this.openSkip=0,this.openLimit=5,this.rfcSkip=0,this.rfcLimit=5}openInNewTab(e){console.log(e),window.open("/main/compliance/complianceDetails/"+e,"_blank")}getActivities(){this._cmsService.getActivityForDB().subscribe(e=>{console.log(e.data),this.updates=e.data},e=>{})}getOverDueCmp(){this._cmsService.getComplianceByStatus({status_name:"Open",skip:this.openSkip,limit:this.openLimit}).subscribe(e=>{console.log(e.data,"Open"),this.overdue.push(...e.data)},e=>{})}getPendingCmp(){this._cmsService.getComplianceByStatus({status_name:"Request for Closure",skip:this.rfcSkip,limit:this.rfcLimit}).subscribe(e=>{console.log(e.data,"Request for Closure"),this.pending.push(...e.data)},e=>{})}onChangeMonth(e){console.log("slider",e.checked),e.checked?(console.log("month check"),this.checkMonth=!0,this._cmsService.setMonthObservable({data:{month:!0,isChecked:e.checked}})):0==e.checked&&(this.checkMonth=!1,this._cmsService.setMonthObservable({data:{month:!0,isChecked:e.checked}}))}onChangeStatus(e){console.log("slider",e.checked),e.checked?(console.log("status check"),this.checkStatus=!0,this._cmsService.setMonthObservable({data:{status:!0,isChecked:e.checked}})):0==e.checked&&(this.checkStatus=!1,this._cmsService.setMonthObservable({data:{status:!0,isChecked:e.checked}}))}ngOnInit(){this.getActivities(),this.getOverDueCmp(),this.getPendingCmp()}onOpenScroll(){this.openSkip=this.overdue.length,this.openLimit=5,this.getOverDueCmp()}onRFCScroll(){this.rfcSkip=this.pending.length,this.rfcLimit=5,this.getPendingCmp()}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](o.NgZone),o["\u0275\u0275directiveInject"](a.a),o["\u0275\u0275directiveInject"](s.a),o["\u0275\u0275directiveInject"](l.a),o["\u0275\u0275directiveInject"](d.a),o["\u0275\u0275directiveInject"](c.a),o["\u0275\u0275directiveInject"](h.a),o["\u0275\u0275directiveInject"](p.a),o["\u0275\u0275directiveInject"](m.a))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["overview-landing-page"]],decls:1,vars:1,consts:[["class","container-fluid",4,"ngIf"],[1,"container-fluid"],[1,"row","mt-3"],[1,"col-12","d-flex"],[1,"col-4","creation-card"],[1,"p-0","col-12","d-flex"],[1,"col-7","p-0"],[1,"mt-2"],[2,"font-size","18px"],[1,"col-4","pt-1"],[3,"matTooltip","change"],[2,"padding-left","5px",3,"matTooltip","change"],[1,"col-8","ml-3","creation-card"],[1,"row","pt-3","mb-2"],[1,"row"],[1,"row","mt-3","align-items-center","creation-card1"],[1,"col-4"],[1,"col-5"],[1,"col-2"],[1,"col-1"],["infiniteScroll","",1,"search-results",3,"infiniteScrollDistance","scrollWindow","scrolled"],["style","\n            border-left: rebeccapurple 2px solid;\n            border-bottom: 1px solid gray;\n          ","class","row align-items-center creation-card2",4,"ngFor","ngForOf"],[1,"col-8","d-flex"],[1,"col-6","ml-3","creation-card"],["style","\n              background-color: #faeded;\n              border-left: red 2px solid;\n              border-bottom: 1px solid gray;\n            ","class","row align-items-center creation-card2",4,"ngFor","ngForOf"],[1,"card-body","p-2",2,"overflow-y","scroll"],["style","\n                background-color: #f3eeee;\n                height: 2rem;\n                margin-top: 6px;\n                padding-left: 10px;\n                padding-top: 7px;\n                border-radius: 3px;\n              ",4,"ngIf"],[4,"ngIf"],[1,"row","align-items-center","creation-card2",2,"border-left","rebeccapurple 2px solid","border-bottom","1px solid gray"],[1,"col-5",2,"overflow","hidden","white-space","nowrap","text-overflow","ellipsis"],["mat-icon-button","",3,"click"],[2,"color","#1e2733 !important","font-size","18px !important"],[1,"row","align-items-center","creation-card2",2,"background-color","#faeded","border-left","red 2px solid","border-bottom","1px solid gray"],[2,"background-color","#f3eeee","height","2rem","margin-top","6px","padding-left","10px","padding-top","7px","border-radius","3px"],[2,"color","rgb(140, 137, 137)"],[1,"pr-2",2,"font-size","14px"],["class","mt-2","style","\n                  background-color: #f3eeee;\n                  min-height: 2rem;\n                  margin-top: 6px;\n                  padding-left: 10px;\n                  padding-top: 7px;\n                  border-radius: 3px;\n                ",4,"ngFor","ngForOf"],[1,"mt-2",2,"background-color","#f3eeee","min-height","2rem","margin-top","6px","padding-left","10px","padding-top","7px","border-radius","3px"],[1,"pt-2","overflow-ctrl"],[2,"font-weight","500"],[1,"pl-2",2,"overflow","hidden","white-space","nowrap","text-overflow","ellipsis",3,"matTooltip"],[1,"pl-2",3,"matTooltip"],[1,"pl-2",2,"font-weight","400 !important",3,"matTooltip"],[1,"pt-2","pb-2"],[1,"pl-2"]],template:function(e,t){1&e&&o["\u0275\u0275template"](0,M,54,10,"div",0),2&e&&o["\u0275\u0275property"]("ngIf",!t.isOverview)},directives:[i.NgIf,u.a,g.a,f.a,E,x,k.a,i.NgForOf,D.a],pipes:[i.DatePipe],styles:[".creation-card[_ngcontent-%COMP%]{position:relative;background-color:#fff;border-radius:10px;box-shadow:0 1px 2px 0 #ccc;height:23rem}.creation-card1[_ngcontent-%COMP%]{background-color:#e7e6e6;box-shadow:0 1px 2px 0 #ccc}.creation-card1[_ngcontent-%COMP%], .creation-card2[_ngcontent-%COMP%]{position:relative;border-radius:2px;height:2rem}.creation-card2[_ngcontent-%COMP%]{background-color:#fcfafa;box-shadow:1px 1px 2px 0 #ccc}.slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}.search-results[_ngcontent-%COMP%]{max-height:10rem;overflow-y:auto}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}.slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}[_nghost-%COMP%]     .mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-bar{background-color:#cf0001}[_nghost-%COMP%]     .mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-thumb{background-color:#ed6565}"]}),e})()}];let W=(()=>{class e{}return e.\u0275mod=o["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=o["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[r.k.forChild(N)],r.k]}),e})(),q=(()=>{class e{}return e.\u0275mod=o["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=o["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,W,u.b,D.b,f.b,g.b,k.b]]}),e})()}}]);