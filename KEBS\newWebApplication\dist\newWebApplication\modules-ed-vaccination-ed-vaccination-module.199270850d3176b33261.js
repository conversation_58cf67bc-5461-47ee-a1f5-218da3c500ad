(window.webpackJsonp=window.webpackJsonp||[]).push([[819],{FKDU:function(e,t,n){"use strict";n.r(t),n.d(t,"EdVaccinationModule",(function(){return y}));var o=n("ofXK"),i=n("tyNb"),c=n("fXoL"),r=n("33Jv"),a=n("jAlA"),s=n("1A3m");const l=["contentContainer"],d=[{path:"",component:(()=>{class e{constructor(e,t,n,o,i){this._router=e,this._edService=t,this._route=n,this._toaster=o,this._compiler=i,this.subs=new r.a}ngOnInit(){this.getEmployeeId(),this.loadVaccinationDetailContainer()}getEmployeeId(){this.subs.sink=this._edService.getEmployeeId().subscribe(e=>{console.log(e),this.associateId=e})}loadVaccinationDetailContainer(){this.contentContainerRef&&this.contentContainerRef.clear(),Promise.all([n.e(20),n.e(25),n.e(706)]).then(n.bind(null,"nM4h")).then(e=>{const t=this._compiler.compileModuleSync(e.VaccinationDetailsModule).create(this.contentContainerRef.injector).componentFactoryResolver.resolveComponentFactory(e.VaccinationDetailsComponent);this.contentContainerRef.createComponent(t).instance.associateId=this.associateId})}ngOnDestroy(){this.subs.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(c["\u0275\u0275directiveInject"](i.g),c["\u0275\u0275directiveInject"](a.a),c["\u0275\u0275directiveInject"](i.a),c["\u0275\u0275directiveInject"](s.a),c["\u0275\u0275directiveInject"](c.Compiler))},e.\u0275cmp=c["\u0275\u0275defineComponent"]({type:e,selectors:[["app-ed-vaccination-landing-page"]],viewQuery:function(e,t){if(1&e&&c["\u0275\u0275viewQuery"](l,!0,c.ViewContainerRef),2&e){let e;c["\u0275\u0275queryRefresh"](e=c["\u0275\u0275loadQuery"]())&&(t.contentContainerRef=e.first)}},decls:5,vars:0,consts:[[1,"container-fluid","ed-vaccination-detail-item-styles"],[1,"row"],[1,"col-12","p-0"],["contentContainer",""]],template:function(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"div",0),c["\u0275\u0275elementStart"](1,"div",1),c["\u0275\u0275elementStart"](2,"div",2),c["\u0275\u0275elementContainer"](3,null,3),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]())},styles:[".ed-vaccination-detail-item-styles[_ngcontent-%COMP%]   .overflow[_ngcontent-%COMP%]{height:28rem;overflow:scroll;overflow-x:hidden}.ed-vaccination-detail-item-styles[_ngcontent-%COMP%]   .normalFont[_ngcontent-%COMP%]{color:#181818;font-size:14px;font-weight:600;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}"]}),e})()}];let u=(()=>{class e{}return e.\u0275mod=c["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=c["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.k.forChild(d)],i.k]}),e})();var f=n("bTqV"),m=n("NFeN"),p=n("Qu3c"),h=n("jaxi"),v=n("STbY"),w=n("MutI"),C=n("0IaG");let y=(()=>{class e{}return e.\u0275mod=c["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=c["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[o.CommonModule,u,m.b,f.b,p.b,h.c,v.e,w.d,C.g]]}),e})()}}]);