(window.webpackJsonp=window.webpackJsonp||[]).push([[728,535,631,634,858],{NJ67:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));class i{constructor(){this.disabled=!1}onChange(e){}onTouched(e){}writeValue(e){this.value=e}registerOnChange(e){this.onChange=e}registerOnTouched(e){this.onTouched=e}setDisabledState(e){this.disabled=e}}},"TmG/":function(e,t,n){"use strict";n.d(t,"a",(function(){return _}));var i=n("fXoL"),o=n("3Pt+"),s=n("jtHE"),a=n("XNiG"),r=n("NJ67"),l=n("1G5W"),c=n("kmnG"),d=n("ofXK"),p=n("d3UM"),h=n("FKr1"),u=n("WJ5W"),m=n("Qu3c");function g(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-label"),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](e.placeholder)}}function f(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-option",7),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275property"]("value",null),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](e.disableNone?"Select One":"None")}}function y(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"mat-option",8),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const n=t.$implicit;return i["\u0275\u0275nextContext"]().emitChanges(n)})),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;i["\u0275\u0275property"]("value",e.id||e._id)("matTooltip",e.name),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let _=(()=>{class e extends r.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new o.j,this.fieldFilterCtrl=new o.j,this.list=[],this.required=!1,this.disableNone=!1,this.valueChange=new i.EventEmitter,this.disabled=!1,this.hasNoneOption=!0,this.filteredList=new s.a,this.change=new i.EventEmitter,this.hideMatLabel=!1,this._onDestroy=new a.b,this.emitChanges=e=>{this.change.emit(e)}}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(l.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(l.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](i.Renderer2))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search"]],inputs:{list:"list",placeholder:"placeholder",required:"required",disableNone:"disableNone",disabled:"disabled",hasNoneOption:"hasNoneOption",hideMatLabel:"hideMatLabel"},outputs:{valueChange:"valueChange",change:"change"},features:[i["\u0275\u0275ProvidersFeature"]([{provide:o.t,useExisting:Object(i.forwardRef)(()=>e),multi:!0}]),i["\u0275\u0275InheritDefinitionFeature"],i["\u0275\u0275NgOnChangesFeature"]],decls:9,vars:11,consts:[["appearance","outline"],[4,"ngIf"],[3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value",4,"ngIf"],[3,"value","matTooltip","click",4,"ngFor","ngForOf"],[3,"value"],[3,"value","matTooltip","click"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"mat-form-field",0),i["\u0275\u0275template"](1,g,2,1,"mat-label",1),i["\u0275\u0275elementStart"](2,"mat-select",2,3),i["\u0275\u0275elementStart"](4,"mat-option"),i["\u0275\u0275element"](5,"ngx-mat-select-search",4),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](6,f,2,2,"mat-option",5),i["\u0275\u0275template"](7,y,2,3,"mat-option",6),i["\u0275\u0275pipe"](8,"async"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",!t.hideMatLabel),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.hasNoneOption),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngForOf",i["\u0275\u0275pipeBind1"](8,9,t.filteredList)))},directives:[c.c,d.NgIf,p.c,o.v,o.k,o.F,h.p,u.a,d.NgForOf,c.g,m.a],pipes:[d.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%}"]}),e})()},"l5o+":function(e,t,n){"use strict";n.r(t),n.d(t,"IntegrationLayerModule",(function(){return hi}));var i=n("ofXK"),o=n("tyNb"),s=n("fXoL");let a=(()=>{class e{constructor(e){this.router=e}ngOnInit(){}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275directiveInject"](o.g))},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-il-landing-page"]],decls:1,vars:0,template:function(e,t){1&e&&s["\u0275\u0275element"](0,"router-outlet")},directives:[o.l],styles:[""]}),e})();var r=n("mrSG"),l=n("R0Ic"),c=n("3Pt+"),d=n("M9IT"),p=n("+0xr"),h=n("nYR2"),u=n("XNiG"),m=n("1G5W"),g=n("0IaG"),f=n("tk/3");let y=(()=>{class e{constructor(e){this.http=e,this.url="/api/integrationLayer/",this.getUserSuggestionsFromDB=(e,t)=>{console.log(e),console.log(t);try{return new Promise((n,i)=>{this.http.post(this.url+"getUserSuggestionsFromDB",{searchText:e,vcoe_flag:t}).subscribe(e=>n(e),e=>(console.log(e),i(e)))})}catch(n){return Promise.reject()}}}getProjects(e,t){return this.http.post(this.url+"getProjectList",{searchString:e,lastIndex:t})}getProjectData(e){return console.log(e),this.http.post(this.url+"getProject",{project_id:e})}postNewProject(e){return this.http.post(this.url+"newProject",e)}getLastConnectionId(e){return this.http.post(this.url+"getLastConnectionId",{project_id:e})}getConnection(e){return this.http.post(this.url+"getConnection",{connection_id:e})}getConnectionList(e){return this.http.post(this.url+"getConnectionList",{project_id:e})}postConnectionFields(e){return this.http.post(this.url+"postConnectionFields/",e)}postNewConnection(e){return this.http.post(this.url+"newConnection",e)}getSystems(){return this.http.post(this.url+"getSystems",{})}getBaseProduct(e){return this.http.post(this.url+"getBaseProduct",{system_name:e})}getResource(e){return this.http.post(this.url+"getResource",{base_product:e})}getMethod(){return this.http.get(this.url+"getMethod")}postSourceApiCall(e,t,n,i){return this.http.post(this.url+e,t,{headers:i,params:n})}postPerPersonAPI(e){return this.http.post(this.url+"postToPerPerson",e)}postUserAPI(e){return this.http.post(this.url+"postToUsers",e)}postEmpJobSF(e){return this.http.post(this.url+"postEmpJobSF",e)}postToSUCCESSFACTOR(e){return this.http.post(this.url+"postToSUCCESSFACTOR",e)}postToMICROSOFT(e){return this.http.post(this.url+"postToMICROSOFT",e)}sf_api_hit(e){return this.http.post(this.url+"sf_api_hit",e)}qb_api_hit(e){return this.http.post(this.url+"qb_api_hit",e)}getURL(e){return this.http.get(this.url+`getURL/${e[0]}/${e[1]}`)}getURLDest(e){return this.http.get(this.url+"getURLDest/"+e[0])}getQueryParams(e){return this.http.post(this.url+"getQueryParams",{connection_id:e[0],source_endpoint:e[1]})}getBasicAuth(e){return console.log("Connection ID on API Service : ",e),this.http.post(this.url+"getBasicAuth",e)}postBasicAuth(e){return this.http.post(this.url+"postBasicAuth",e)}getSourceList(e){return this.http.post(this.url+"getSourceList",{connection_id:e})}getSyncFunction(e){return this.http.post(this.url+"getSyncFunction",e)}getConnectionItems(e){return this.http.post(this.url+"getConnectionItems",{connection_id:e})}getConnectionItem(e){return this.http.post(this.url+"getConnectionItem",{source_id:e})}postConnectionItems(e){return this.http.post(this.url+"postConnectionItems",e)}disableConnectionId(e){return this.http.post(this.url+"disableConnectionId",{connection_id:e.connection_id,project_id:e.project_id})}disabledConnectionList(){return this.http.post(this.url+"disabledConnectionList",{})}enableConnectionId(e){return this.http.post(this.url+"enableConnectionId",{connection_id:e.connection_id,project_id:e.project_id})}getBearerToken(e){return this.http.post(this.url+"getTokenAuth",e)}postAppId(e){return this.http.post(this.url+"postAppId",e)}getAppId(e){return this.http.post(this.url+"getAppId",e)}getDestinationFields(e){return this.http.post(this.url+"getDestinationFields",e)}postProServProjectSet(e){return this.http.post(this.url+"postProServProjectSet",e)}postProServProjectRoleSet(e){return this.http.post(this.url+"postProServProjectRoleSet",e)}postProServWorkpackageSet(e){return this.http.post(this.url+"postProServWorkpackageSet",e)}postProServWorkItemSet(e){return this.http.post(this.url+"postProServWorkItemSet",e)}postProServWorkPackageFunctionSet(e){return this.http.post(this.url+"postProServWorkPackageFunctionSet",e)}postSfBusinessUnit(e){return this.http.post(this.url+"postSfBusinessUnit",e)}postSfCompany(e){return this.http.post(this.url+"postSfCompany",e)}postSfTerritory(e){return this.http.post(this.url+"postSfTerritory",e)}postSfDepartment(e){return this.http.post(this.url+"postSfDepartment",e)}postSfDivision(e){return this.http.post(this.url+"postSfDivision",e)}postSfJobCode(e){return this.http.post(this.url+"postSfJobCode",e)}postSfCostCenter(e){return this.http.post(this.url+"postSfCostCenter",e)}postSfPickListValue(e){return this.http.post(this.url+"postSfPickListValue",e)}postSfPosition(e){return this.http.post(this.url+"postSfPosition",e)}postOnSync(e){return e.hostname=window.location.origin+this.url,this.http.post(this.url+"postOnSync",e)}getProjectLength(){return this.http.post(this.url+"getProjectLength",{})}postOAuthDetails(e){return this.http.post(this.url+"postOAuthDetails",e)}getOAuthDetails(e){return this.http.post(this.url+"getOAuthDetails",e)}getOAuthTokenPopUP(e){return this.http.post(this.url+"getOAuth",e)}getQuickBookToken(e){return this.http.post(this.url+"getQuickBookToken",e)}postQBJournals(e){return this.http.post(this.url+"postQBJourlas",e)}postQBCustomers(e){return this.http.post(this.url+"postQBCustomers",e)}getIntegrationErroLogs(e){return this.http.post(this.url+"getIntegrationErroLogs",e)}getIlsBgj(e){return this.http.post(this.url+"getIlsBgj",e)}getEDBgj(e){return this.http.post(this.url+"getEDBgj",e)}getbatchSize(){return this.http.post(this.url+"getbatchSize",{})}getQBEntityName(e){return this.http.post(this.url+"getQBEntityName",{company_id:e})}getSAMLAssertionAccessToken(e){return this.http.post(this.url+"getSAMLAssertionAccessToken",e)}postOAuthSAPDetails(e){return e.hostname=window.location.origin+this.url,this.http.post(this.url+"postOAuthSAPDetails",e)}getSourceData(e){return this.http.get(e)}getTableMasterData(e){return this.http.post(this.url+"getTableMasterData",{application_id:e})}getTableData(e,t,n=null,i=null,o=null,s=null){return this.http.post(this.url+"getTableData",{filterConfig:e,latestRunId:t,foreign_key_value:n,foreign_key:i,process_data_configuration:o,process_data_field_mapping:s})}getAdminAccess(){return this.http.post(this.url+"getAdminAccess",{})}getExceptionCustomerData(){return this.http.post(this.url+"ExceptionalReportCustomer",{})}getExceptionDepartmentData(){return this.http.post(this.url+"ExceptionalReportDepartment",{})}getExceptionVendorData(){return this.http.post(this.url+"ExceptionalReportVendor",{})}getExceptionAccountData(){return this.http.post(this.url+"ExceptionalReportAccount",{})}getExceptionTaxData(){return this.http.post(this.url+"ExceptionalReportTax",{})}getExceptionCostCenterData(){return this.http.post(this.url+"ExceptionalReportClass",{})}getBaseProducts(){return this.http.post(this.url+"getBaseProducts",{})}insertNotif(e){return this.http.post(this.url+"insertNotif",{usersList:e})}getNotifResult(){return this.http.post(this.url+"getNotifData",{})}updateNotif(e){return this.http.post(this.url+"updateNotif",{usersList:e})}getIlsTenantConfig(){return this.http.post(this.url+"getIlsTenantConfig",{})}getMongoList(){return this.http.post(this.url+"getMongoList",{})}updateMongoConfig(e,t,n){return this.http.post(this.url+"updateMongoList",{is_active:e,cron_pattern:t,messgae_key:n})}allowCrudApi(e,t,n,i){return this.http.post(this.url+"allowCrudApi",{data:e,primary_key:t,filterConfig:n,type:i})}refreshTableConfigData(e){return this.http.post(this.url+"refreshTableConfigData",{item_id:e})}subsequentEndpointCall(e,t,n,i,o){return this.http.post(this.url+""+e,{offset:t,lower_limit:n,item_id:i,rerun_employee_id:o})}ilsEDComparision(){return this.http.post(this.url+"ilsEDComparision",{})}checkForILSAccess(){return this.http.post(this.url+"checkForILSAccess",{})}getPopUpLog(e,t,n,i,o){return this.http.post(this.url+"getPopUpLog",{id:e,table_name:t,primary_field:n,log_field:i,is_sql:o})}updateRunIdLogs(e){return this.http.post(this.url+"updateRunIdLogs",e)}changeVoucherStatus(e){return this.http.post(this.url+"changeVoucherStatus",{voucher_no:e.voucher_no,currentConnectionItem:e.currentConnectionItem,connection_id:e.connection_id})}saveQueryParams(e,t){return this.http.post(this.url+"saveQueryParams",{queryParams:e,currentSourceDetails:t})}getQueryParamsKeyValuePair(e){return this.http.post(this.url+"getQueryParamsKeyValuePair",{source_id:e.source_id,connection_id:e.connection_id,project_id:e.project_id})}callback(e){return this.http.post(this.url+"callback",e)}callbackZoho(e){return this.http.post(this.url+"callbackZoho",e)}getZBVoucherTypes(){return this.http.post(this.url+"getZBVoucherTypes",{})}getZBReportsData(e,t,n,i){return this.http.post(this.url+"getZBReportsData",t,{headers:i,params:n})}syncZBReportsData(e,t,n){return this.http.post(this.url+"syncZBReportsData",{url:e,voucher_type:t,voucher_data:n})}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275inject"](f.c))},e.\u0275prov=s["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var _=n("NFeN"),b=n("bTqV"),v=n("Qu3c"),S=n("xHqg"),C=n("kmnG"),x=n("qFsG"),O=n("d3UM"),w=n("FKr1"),E=n("TmG/"),P=n("Xa2L");function I(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"mat-option",45),s["\u0275\u0275listener"]("click",(function(){s["\u0275\u0275restoreView"](e);const n=t.$implicit;return s["\u0275\u0275nextContext"](2).src_option(n)})),s["\u0275\u0275text"](1),s["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;s["\u0275\u0275property"]("value",e),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate1"](" ",e,"")}}function M(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"mat-option",28),s["\u0275\u0275text"](1),s["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;s["\u0275\u0275property"]("value",e),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate1"](" ",e," ")}}function D(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"div",46),s["\u0275\u0275elementStart"](1,"div",47),s["\u0275\u0275element"](2,"app-input-search",48),s["\u0275\u0275elementStart"](3,"button",49),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"](2).addNewResource()})),s["\u0275\u0275elementStart"](4,"mat-icon"),s["\u0275\u0275text"](5,"add"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](6,"button",49),s["\u0275\u0275listener"]("click",(function(){s["\u0275\u0275restoreView"](e);const n=t.index;return s["\u0275\u0275nextContext"](2).DeleteResource(n)})),s["\u0275\u0275elementStart"](7,"mat-icon"),s["\u0275\u0275text"](8,"delete"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}if(2&e){const e=t.index,n=s["\u0275\u0275nextContext"](2);s["\u0275\u0275property"]("formGroupName",e),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("list",n.src_resource_dropdown)("disableNone",!0)}}function k(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"mat-option",45),s["\u0275\u0275listener"]("click",(function(){s["\u0275\u0275restoreView"](e);const n=t.$implicit;return s["\u0275\u0275nextContext"](2).dest_option(n)})),s["\u0275\u0275text"](1),s["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;s["\u0275\u0275property"]("value",e),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate1"](" ",e,"")}}function j(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"mat-option",28),s["\u0275\u0275text"](1),s["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;s["\u0275\u0275property"]("value",e),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate1"](" ",e," ")}}function A(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"div",3),s["\u0275\u0275elementStart"](1,"div",4),s["\u0275\u0275elementStart"](2,"div",5),s["\u0275\u0275elementStart"](3,"div",6),s["\u0275\u0275elementStart"](4,"mat-icon",7),s["\u0275\u0275text"](5,"api"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](6,"span",8),s["\u0275\u0275text"](7),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](8,"div",9),s["\u0275\u0275elementStart"](9,"button",10),s["\u0275\u0275elementStart"](10,"mat-icon",11),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"]().close_connection()})),s["\u0275\u0275text"](11,"close"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](12,"mat-horizontal-stepper",12,13),s["\u0275\u0275elementStart"](14,"mat-step",14),s["\u0275\u0275elementStart"](15,"form",15),s["\u0275\u0275elementStart"](16,"mat-form-field",16),s["\u0275\u0275element"](17,"input",17),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](18,"mat-form-field",16),s["\u0275\u0275elementStart"](19,"input",18),s["\u0275\u0275listener"]("ngModelChange",(function(t){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"]().Conn_Name=t})),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](20,"mat-form-field",16),s["\u0275\u0275element"](21,"textarea",19),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](22,"mat-form-field",16),s["\u0275\u0275elementStart"](23,"mat-select",20),s["\u0275\u0275elementStart"](24,"mat-option",21),s["\u0275\u0275text"](25,"API"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](26,"div",22),s["\u0275\u0275elementStart"](27,"button",23),s["\u0275\u0275elementStart"](28,"mat-icon"),s["\u0275\u0275text"](29,"navigate_next"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](30,"mat-step",24),s["\u0275\u0275elementStart"](31,"form",15),s["\u0275\u0275elementStart"](32,"mat-form-field",16),s["\u0275\u0275elementStart"](33,"mat-select",25),s["\u0275\u0275template"](34,I,2,2,"mat-option",26),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](35,"mat-form-field",16),s["\u0275\u0275elementStart"](36,"mat-select",27),s["\u0275\u0275elementStart"](37,"mat-option",28),s["\u0275\u0275text"](38),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](39,"mat-form-field",16),s["\u0275\u0275elementStart"](40,"mat-select",29),s["\u0275\u0275template"](41,M,2,2,"mat-option",30),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](42,"form",15),s["\u0275\u0275elementStart"](43,"div",31),s["\u0275\u0275template"](44,D,9,3,"div",32),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](45,"div",33),s["\u0275\u0275elementStart"](46,"button",34),s["\u0275\u0275elementStart"](47,"mat-icon"),s["\u0275\u0275text"](48,"navigate_before"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](49,"button",35),s["\u0275\u0275elementStart"](50,"mat-icon"),s["\u0275\u0275text"](51,"navigate_next"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](52,"mat-step",36),s["\u0275\u0275elementStart"](53,"form",37),s["\u0275\u0275listener"]("ngSubmit",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"]().saveConnection()})),s["\u0275\u0275elementStart"](54,"mat-form-field",16),s["\u0275\u0275elementStart"](55,"mat-select",38),s["\u0275\u0275template"](56,k,2,2,"mat-option",26),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](57,"div",39),s["\u0275\u0275element"](58,"app-input-search",40),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](59,"mat-form-field",16),s["\u0275\u0275elementStart"](60,"mat-select",41),s["\u0275\u0275elementStart"](61,"mat-option",28),s["\u0275\u0275text"](62),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](63,"mat-form-field",16),s["\u0275\u0275elementStart"](64,"mat-select",42),s["\u0275\u0275template"](65,j,2,2,"mat-option",30),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](66,"div",33),s["\u0275\u0275elementStart"](67,"button",34),s["\u0275\u0275elementStart"](68,"mat-icon"),s["\u0275\u0275text"](69,"navigate_before"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](70,"button",43),s["\u0275\u0275elementStart"](71,"mat-icon",44),s["\u0275\u0275text"](72,"done_all"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275nextContext"]();s["\u0275\u0275advance"](7),s["\u0275\u0275textInterpolate"](e.connection_count),s["\u0275\u0275advance"](7),s["\u0275\u0275property"]("stepControl",e.connectionConfig),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("formGroup",e.connectionConfig),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("value",e.connection_count),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("ngModel",e.Conn_Name),s["\u0275\u0275advance"](11),s["\u0275\u0275property"]("stepControl",e.sourceConfig)("stepControl",e.sourceResourceConfig),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("formGroup",e.sourceConfig),s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("ngForOf",e.system),s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("value",e.src_uri_data),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate1"](" ",e.src_uri_data," "),s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("ngForOf",e.src_method),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("formGroup",e.sourceResourceConfig),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("ngForOf",e.getControlsSource()),s["\u0275\u0275advance"](8),s["\u0275\u0275property"]("stepControl",e.destinationConfig),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("formGroup",e.destinationConfig),s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("ngForOf",e.system),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("list",e.dest_resource_dropdown)("disableNone",!0),s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("value",e.dest_uri_data),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate1"](" ",e.dest_uri_data," "),s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("ngForOf",e.dest_method)}}function F(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"div",50),s["\u0275\u0275elementStart"](1,"div",51),s["\u0275\u0275elementContainerStart"](2),s["\u0275\u0275element"](3,"mat-spinner"),s["\u0275\u0275elementContainerEnd"](),s["\u0275\u0275elementStart"](4,"p"),s["\u0275\u0275text"](5,"Kindly Don't refresh the page...! Connection Being Created... "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]())}let R=(()=>{class e{constructor(e,t,n,i){this.data=e,this.fb=t,this.api=n,this.dialogRef=i,this.$onDestroy=new u.b,this.$onAppApiCalled=new u.b,this.system_resource=[],this.system=[],this.addNewConnection=!1,this.fieldArray=[],this.src_resource_dropdown=[],this.dest_resource_dropdown=[],this.isLoading=!1}ngOnDestroy(){this.$onDestroy.next(),this.$onDestroy.complete()}ngOnInit(){this.pro_ID=this.data.pro_id,this.connection_count=this.data.connection_count,this.system=this.data.system,console.log("pro_ID Data : ",this.pro_ID," connection_count : ",this.connection_count," system : ",this.system),this.connectionConfig=this.fb.group({connection_name:["",c.H.required],connection_description:["",c.H.required],connection_type:["",c.H.required]}),this.sourceConfig=this.fb.group({source_system:["",c.H.required],source_request_url:[""],source_method:["",c.H.required]}),this.sourceResourceConfig=new c.m({itemRows:new c.g([new c.m({source_resource:new c.j("",c.H.required)})])}),this.destinationConfig=this.fb.group({destination_system:["",c.H.required],destination_resource:["",c.H.required],destination_request_url:[""],destination_method:["",c.H.required]}),this.connectionField=new c.m({itemRows:new c.g([new c.m({field_name:new c.j("",c.H.required),field_description:new c.j("",c.H.required),field_type:new c.j("",c.H.required),data_type:new c.j("",c.H.required),length:new c.j("",c.H.required),is_key:new c.j(!1),is_filter:new c.j(!1),is_null:new c.j(!1),is_union:new c.j(!1)})])})}src_option(e){this.src_resource_dropdown=[],this.src_resource=[],this.src_uri_data="",this.src_method=[];let t=!1,n={};this.api.getBaseProduct(e).pipe(Object(h.a)(()=>{for(let e of this.src_baseProduct_data)this.api.getResource(e).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{t||(console.log("URL data getting...",e[0]),t=!0,this.src_resource_option(e[0])),this.src_resource.push(...e);let i=[];for(let t=0;t<this.src_resource.length;t++)n={id:this.src_resource[t].resource,name:this.src_resource[t].resource},i.push(n);this.src_resource_dropdown=i,console.log("res in src : ",this.src_resource_dropdown)});this.api.getMethod().pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{this.src_method.push(e[0])}),console.log("Resouse Value on Source System for "," : ",e," is : ",this.src_resource)})).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(t=>{this.src_baseProduct_data=t,console.log("src_baseProduct_data Value on Source System for "," : ",e," is : ",this.src_baseProduct_data)})}dest_option(e){this.dest_resource_dropdown=[],this.dest_resource=[],this.dest_uri_data="",this.dest_method=[];let t=!1,n={};this.api.getBaseProduct(e).pipe(Object(h.a)(()=>{for(let e of this.dest_baseProduct_data)this.api.getResource(e).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{t||(console.log("URL data getting...",e[0]),t=!0,this.dest_resource_option(e[0])),this.dest_resource.push(...e);let i=[];for(let t=0;t<this.dest_resource.length;t++)n={id:this.dest_resource[t].resource,name:this.dest_resource[t].resource},i.push(n);this.dest_resource_dropdown=i,console.log("res in src : ",this.dest_resource_dropdown)});this.api.getMethod().pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{this.dest_method.push(e[1])}),console.log("Resouse Value on Destination System for "," : ",e," is : ",this.dest_resource)})).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(t=>{this.dest_baseProduct_data=t,console.log("dest_baseProduct_data Value on Destination System for "," : ",e," is : ",this.dest_baseProduct_data)})}src_resource_option(e){this.src_uri_data=null==e.base_uri?"None":e.base_uri,console.log("Base URI for src system is : ",this.src_uri_data)}dest_resource_option(e){this.dest_uri_data=null==e.base_uri?"None":e.base_uri,console.log("Base URI for dest system is : ",this.dest_uri_data)}addNewResource(){this.sourceResourceConfig.controls.itemRows.push(new c.m({source_resource:new c.j("",c.H.required)}))}DeleteResource(e){const t=this.sourceResourceConfig.controls.itemRows;t.length<=1?(t.clear(),this.addNewResource()):(t.removeAt(e),console.log("Source Resource After Delete : ",this.sourceResourceConfig.value))}getControlsSource(){return this.sourceResourceConfig.get("itemRows").controls}saveConnection(){var e,t;return Object(r.c)(this,void 0,void 0,(function*(){if(this.isLoading=!0,this.destinationConfig.invalid)return;let n={project_id:"",connection_id:"",connection_description:"",connection_name:"",connection_type:"",destination_method:"",destination_request_url:"",destination_resource:"",destination_system:"",source_method:"",source_request_url:"",source_resource:{},source_system:"",active:"Active",created_by:"",created_on:"",created_at:""},[i,o]=(new Date).toLocaleString().split(",");o=o.replace(" AM",""),o=o.replace(" ",""),n.project_id=this.pro_ID,n.connection_id=this.connection_count,n.connection_description=this.connectionConfig.value.connection_description,n.connection_name=this.connectionConfig.value.connection_name,n.connection_type=this.connectionConfig.value.connection_type,n.destination_method=this.destinationConfig.value.destination_method,n.destination_request_url="None"===this.destinationConfig.value.destination_request_url?"":this.destinationConfig.value.destination_request_url,n.destination_resource=this.destinationConfig.value.destination_resource,n.destination_system=this.destinationConfig.value.destination_system,n.source_method=this.sourceConfig.value.source_method,n.source_request_url="None"===this.sourceConfig.value.source_request_url?"":this.sourceConfig.value.source_request_url;let s=this.sourceResourceConfig.value,a={src_list:[]};console.log("Source Resource List : ",s);try{for(var l,c=Object(r.b)(Object.values(s.itemRows));!(l=yield c.next()).done;){let e=l.value;a.src_list.push(e.source_resource),console.log("saveConnection for iter sourceList.itemRows : ",e)}}catch(d){e={error:d}}finally{try{l&&!l.done&&(t=c.return)&&(yield t.call(c))}finally{if(e)throw e.error}}console.log("JSON format of Source List : ",a),n.source_resource=a,n.source_system=this.sourceConfig.value.source_system,n.created_on=i,n.created_at=o,console.log("Connection Header Data : ",n),yield this.postNewConnection(n)}))}postNewConnection(e){return Object(r.c)(this,void 0,void 0,(function*(){console.log("Connection Header Data inside postNewConnection async function : ",e),this.api.postNewConnection(e).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){this.isLoading=!1,this.addNewConnection=!1,this.dialogRef.close()})))}))}close_connection(){this.dialogRef.close()}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275directiveInject"](g.a),s["\u0275\u0275directiveInject"](c.i),s["\u0275\u0275directiveInject"](y),s["\u0275\u0275directiveInject"](g.h))},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-new-connection"]],decls:3,vars:2,consts:[[1,"mat-typography",2,"display","block","padding","24px","border-radius","4px","box-sizing","border-box","overflow","auto","outline","0","width","100%","height","100%","min-height","inherit","max-height","inherit"],["class","container",4,"ngIf"],["class","content d-flex justify-content-center mt-5 mb-5",4,"ngIf"],[1,"container"],[1,"row","border-bottom","solid"],[1,"col-11","pt-2","pb-2","d-flex"],["mat-icon-button","",1,"bubble","mt-1"],[1,"iconButton"],[1,"name","my-auto","ml-2"],[1,"col-1","d-flex","pt-2"],["mat-icon-button","",1,"ml-auto","close-button","mt-1"],["matTooltip","Close",1,"close-Icon",2,"cursor","pointer","color","#4d4d4b",3,"click"],["linear",""],["stepper",""],["label","Connection Configuration",3,"stepControl"],[3,"formGroup"],["appearance","outline",1,"col-sm-8"],["matInput","","placeholder","Connection ID","readonly","",3,"value"],["matInput","","placeholder","Connection Name","formControlName","connection_name","required","",3,"ngModel","ngModelChange"],["placeholder","Connection Description","matInput","","formControlName","connection_description","required",""],["matInput","","formControlName","connection_type","placeholder","Connection Type","required",""],["value","API"],[1,"mt-5","d-flex","align-items-end","flex-column"],["mat-raised-button","","matStepperNext","","mat-mini-fab","","matTooltip","Next",1,"create-pr-btn",2,"background-color","#cf0001","color","#fff"],["label","Source System",3,"stepControl"],["matInput","","formControlName","source_system","placeholder","Source System","required",""],[3,"value","click",4,"ngFor","ngForOf"],["matInput","","formControlName","source_request_url","placeholder","Source Request URL"],[3,"value"],["matInput","","formControlName","source_method","placeholder","Source Method","required",""],[3,"value",4,"ngFor","ngForOf"],["formArrayName","itemRows"],[3,"formGroupName",4,"ngFor","ngForOf"],[1,"d-flex","justify-content-between","mt-5"],["mat-raised-button","","matStepperPrevious","","mat-mini-fab","","matTooltip","Back","align","start",1,"create-pr-btn",2,"background-color","#cf0001","color","#fff"],["mat-raised-button","","matStepperNext","","mat-mini-fab","","matTooltip","Next","align","end",1,"create-pr-btn",2,"background-color","#cf0001","color","#fff"],["label","Destination System",3,"stepControl"],[3,"formGroup","ngSubmit"],["matInput","","formControlName","destination_system","placeholder","Destination System","required",""],[1,"col-8"],["placeholder","Destination Resource","required","","formControlName","destination_resource",2,"width","66%","height","45px",3,"list","disableNone"],["matInput","","formControlName","destination_request_url","placeholder","Destination Request URL"],["matInput","","formControlName","destination_method","placeholder","Destination Method","required",""],["mat-mini-fab","","align","end",1,"saveconnection",2,"background-color","#cf0001","color","#fff"],["matTooltip","Create Connection"],[3,"value","click"],[3,"formGroupName"],[1,"col-12","d-flex","justify-content-between"],["placeholder","Source Resource","required","","formControlName","source_resource",2,"width","66%","height","45px",3,"list","disableNone"],["mat-flat-button","",2,"color","#fff","background-color","#cf0001","border-color","#cf0001","font-weight","400","font-size","12px","border-radius","4px","height","35px","margin-top","5px",3,"click"],[1,"content","d-flex","justify-content-center","mt-5","mb-5"],[2,"align-items","center","display","flex","flex-direction","column"]],template:function(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"mat-dialog-content",0),s["\u0275\u0275template"](1,A,73,22,"div",1),s["\u0275\u0275template"](2,F,6,0,"div",2),s["\u0275\u0275elementEnd"]()),2&e&&(s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",!1===t.isLoading),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",!0===t.isLoading))},directives:[g.f,i.NgIf,_.a,b.a,v.a,S.a,S.b,c.J,c.w,c.n,C.c,x.b,c.e,c.v,c.l,c.F,O.c,w.p,S.g,i.NgForOf,c.h,S.h,E.a,c.o,P.c],styles:[""]}),e})();var T=n("xG9w"),L=n("u47x"),N=n("1A3m"),B=n("mS9j"),q=n("dlKe");function $(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"button",42),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"](2).clearSearch()})),s["\u0275\u0275elementStart"](1,"mat-icon",43),s["\u0275\u0275text"](2,"close "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}}function V(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"div",64),s["\u0275\u0275elementContainerStart"](1),s["\u0275\u0275element"](2,"mat-spinner",65),s["\u0275\u0275elementContainerEnd"](),s["\u0275\u0275elementEnd"]())}function z(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"th",66),s["\u0275\u0275text"](1," Connection ID "),s["\u0275\u0275elementEnd"]())}function H(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"td",67),s["\u0275\u0275text"](1),s["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;s["\u0275\u0275propertyInterpolate"]("title",e.connection_id),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate1"](" ",e.connection_id," ")}}function J(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"th",66),s["\u0275\u0275text"](1," Connection Name "),s["\u0275\u0275elementEnd"]())}function U(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"td",67),s["\u0275\u0275text"](1),s["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;s["\u0275\u0275propertyInterpolate"]("title",e.connection_name),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate1"](" ",e.connection_name," ")}}function Y(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"th",66),s["\u0275\u0275text"](1," Connection Desc "),s["\u0275\u0275elementEnd"]())}function K(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"td",67),s["\u0275\u0275text"](1),s["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;s["\u0275\u0275propertyInterpolate"]("title",e.connection_description),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate1"](" ",e.connection_description," ")}}function Q(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"th",66),s["\u0275\u0275text"](1," Source System "),s["\u0275\u0275elementEnd"]())}function G(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"td",67),s["\u0275\u0275text"](1),s["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;s["\u0275\u0275propertyInterpolate"]("title",e.source_system),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate1"](" ",e.source_system," ")}}function Z(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"th",66),s["\u0275\u0275text"](1,"Destination System"),s["\u0275\u0275elementEnd"]())}function W(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"td",67),s["\u0275\u0275text"](1),s["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;s["\u0275\u0275propertyInterpolate"]("title",e.destination_system),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate1"](" ",e.destination_system," ")}}function X(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"th",68),s["\u0275\u0275text"](1,"Active"),s["\u0275\u0275elementEnd"]())}function ee(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"td",69),s["\u0275\u0275text"](1),s["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;s["\u0275\u0275propertyInterpolate"]("title",e.active),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate1"](" ",e.active," ")}}function te(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"th",66),s["\u0275\u0275text"](1,"Public URL"),s["\u0275\u0275elementEnd"]())}function ne(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"td",67),s["\u0275\u0275text"](1),s["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;s["\u0275\u0275propertyInterpolate"]("title",e.public_url),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate1"](" ",e.public_url," ")}}function ie(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"th",66),s["\u0275\u0275text"](1," API "),s["\u0275\u0275elementEnd"]())}function oe(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"td",70),s["\u0275\u0275elementStart"](1,"button",71),s["\u0275\u0275listener"]("click",(function(){s["\u0275\u0275restoreView"](e);const n=t.$implicit;return s["\u0275\u0275nextContext"](3).RowClick(n)})),s["\u0275\u0275text"](2,"API"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}}function se(e,t){1&e&&s["\u0275\u0275element"](0,"tr",72)}function ae(e,t){1&e&&s["\u0275\u0275element"](0,"tr",73)}function re(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"tr",74),s["\u0275\u0275elementStart"](1,"td",75),s["\u0275\u0275elementStart"](2,"div"),s["\u0275\u0275elementStart"](3,"h4",76),s["\u0275\u0275text"](4," No Data found ! "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](5,"div",77),s["\u0275\u0275element"](6,"img",78),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]())}function le(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"div",44),s["\u0275\u0275elementStart"](1,"div",45),s["\u0275\u0275template"](2,V,3,0,"div",46),s["\u0275\u0275elementStart"](3,"table",47),s["\u0275\u0275elementContainerStart"](4,48),s["\u0275\u0275template"](5,z,2,0,"th",49),s["\u0275\u0275template"](6,H,2,2,"td",50),s["\u0275\u0275elementContainerEnd"](),s["\u0275\u0275elementContainerStart"](7,51),s["\u0275\u0275template"](8,J,2,0,"th",49),s["\u0275\u0275template"](9,U,2,2,"td",50),s["\u0275\u0275elementContainerEnd"](),s["\u0275\u0275elementContainerStart"](10,52),s["\u0275\u0275template"](11,Y,2,0,"th",49),s["\u0275\u0275template"](12,K,2,2,"td",50),s["\u0275\u0275elementContainerEnd"](),s["\u0275\u0275elementContainerStart"](13,53),s["\u0275\u0275template"](14,Q,2,0,"th",49),s["\u0275\u0275template"](15,G,2,2,"td",50),s["\u0275\u0275elementContainerEnd"](),s["\u0275\u0275elementContainerStart"](16,54),s["\u0275\u0275template"](17,Z,2,0,"th",49),s["\u0275\u0275template"](18,W,2,2,"td",50),s["\u0275\u0275elementContainerEnd"](),s["\u0275\u0275elementContainerStart"](19,55),s["\u0275\u0275template"](20,X,2,0,"th",56),s["\u0275\u0275template"](21,ee,2,2,"td",57),s["\u0275\u0275elementContainerEnd"](),s["\u0275\u0275elementContainerStart"](22,58),s["\u0275\u0275template"](23,te,2,0,"th",49),s["\u0275\u0275template"](24,ne,2,2,"td",50),s["\u0275\u0275elementContainerEnd"](),s["\u0275\u0275elementContainerStart"](25,59),s["\u0275\u0275template"](26,ie,2,0,"th",49),s["\u0275\u0275template"](27,oe,3,0,"td",60),s["\u0275\u0275elementContainerEnd"](),s["\u0275\u0275template"](28,se,1,0,"tr",61),s["\u0275\u0275template"](29,ae,1,0,"tr",62),s["\u0275\u0275template"](30,re,7,0,"tr",63),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e){const e=s["\u0275\u0275nextContext"](2);s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("ngIf",e.isLoading),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("dataSource",e.dataSource),s["\u0275\u0275advance"](25),s["\u0275\u0275property"]("matHeaderRowDef",e.displayedColumns)("matHeaderRowDefSticky",!0),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("matRowDefColumns",e.displayedColumns)}}function ce(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"button",42),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"](2).clearSearch()})),s["\u0275\u0275elementStart"](1,"mat-icon",43),s["\u0275\u0275text"](2,"close "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}}function de(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"div",64),s["\u0275\u0275elementContainerStart"](1),s["\u0275\u0275element"](2,"mat-spinner",65),s["\u0275\u0275elementContainerEnd"](),s["\u0275\u0275elementEnd"]())}function pe(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"th",66),s["\u0275\u0275text"](1," Error code "),s["\u0275\u0275elementEnd"]())}function he(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"td",93),s["\u0275\u0275text"](1),s["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;s["\u0275\u0275propertyInterpolate"]("matTooltip",e.code),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate1"](" ",e.code," ")}}function ue(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"th",66),s["\u0275\u0275text"](1," Project ID "),s["\u0275\u0275elementEnd"]())}function me(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"td",93),s["\u0275\u0275text"](1),s["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;s["\u0275\u0275propertyInterpolate"]("matTooltip",e.project_id),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate1"](" ",e.project_id," ")}}function ge(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"th",66),s["\u0275\u0275text"](1,"Connection ID"),s["\u0275\u0275elementEnd"]())}function fe(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"td",93),s["\u0275\u0275text"](1),s["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;s["\u0275\u0275propertyInterpolate"]("matTooltip",e.connection_id),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate1"](" ",e.connection_id," ")}}function ye(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"th",66),s["\u0275\u0275text"](1,"Error Description"),s["\u0275\u0275elementEnd"]())}function _e(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"td",94),s["\u0275\u0275pipe"](1,"json"),s["\u0275\u0275text"](2),s["\u0275\u0275pipe"](3,"json"),s["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;s["\u0275\u0275propertyInterpolate"]("matTooltip",s["\u0275\u0275pipeBind1"](1,2,e.error_params)),s["\u0275\u0275advance"](2),s["\u0275\u0275textInterpolate1"](" ",s["\u0275\u0275pipeBind1"](3,4,e.error_params)," ")}}function be(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"th",66),s["\u0275\u0275text"](1,"Error"),s["\u0275\u0275elementEnd"]())}function ve(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"td",94),s["\u0275\u0275pipe"](1,"json"),s["\u0275\u0275text"](2),s["\u0275\u0275pipe"](3,"json"),s["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;s["\u0275\u0275propertyInterpolate"]("matTooltip",s["\u0275\u0275pipeBind1"](1,2,e.error)),s["\u0275\u0275advance"](2),s["\u0275\u0275textInterpolate1"](" ",s["\u0275\u0275pipeBind1"](3,4,e.error)," ")}}function Se(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"th",68),s["\u0275\u0275text"](1,"Created By"),s["\u0275\u0275elementEnd"]())}function Ce(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"td",95),s["\u0275\u0275text"](1),s["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;s["\u0275\u0275propertyInterpolate"]("matTooltip",e.created_by),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate1"](" ",e.created_by," ")}}function xe(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"th",68),s["\u0275\u0275text"](1," Time Stamp "),s["\u0275\u0275elementEnd"]())}function Oe(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"td",95),s["\u0275\u0275text"](1),s["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;s["\u0275\u0275propertyInterpolate"]("matTooltip",e.timestamp),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate1"](" ",e.timestamp," ")}}function we(e,t){1&e&&s["\u0275\u0275element"](0,"tr",72)}function Ee(e,t){1&e&&s["\u0275\u0275element"](0,"tr",73)}function Pe(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"div",81),s["\u0275\u0275listener"]("scrolled",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"](3).onScrollDown()})),s["\u0275\u0275elementStart"](1,"table",82),s["\u0275\u0275elementContainerStart"](2,83),s["\u0275\u0275template"](3,pe,2,0,"th",49),s["\u0275\u0275template"](4,he,2,2,"td",84),s["\u0275\u0275elementContainerEnd"](),s["\u0275\u0275elementContainerStart"](5,85),s["\u0275\u0275template"](6,ue,2,0,"th",49),s["\u0275\u0275template"](7,me,2,2,"td",84),s["\u0275\u0275elementContainerEnd"](),s["\u0275\u0275elementContainerStart"](8,48),s["\u0275\u0275template"](9,ge,2,0,"th",49),s["\u0275\u0275template"](10,fe,2,2,"td",84),s["\u0275\u0275elementContainerEnd"](),s["\u0275\u0275elementContainerStart"](11,86),s["\u0275\u0275template"](12,ye,2,0,"th",49),s["\u0275\u0275template"](13,_e,4,6,"td",87),s["\u0275\u0275elementContainerEnd"](),s["\u0275\u0275elementContainerStart"](14,88),s["\u0275\u0275template"](15,be,2,0,"th",49),s["\u0275\u0275template"](16,ve,4,6,"td",87),s["\u0275\u0275elementContainerEnd"](),s["\u0275\u0275elementContainerStart"](17,89),s["\u0275\u0275template"](18,Se,2,0,"th",56),s["\u0275\u0275template"](19,Ce,2,2,"td",90),s["\u0275\u0275elementContainerEnd"](),s["\u0275\u0275elementContainerStart"](20,91),s["\u0275\u0275template"](21,xe,2,0,"th",56),s["\u0275\u0275template"](22,Oe,2,2,"td",92),s["\u0275\u0275elementContainerEnd"](),s["\u0275\u0275template"](23,we,1,0,"tr",61),s["\u0275\u0275template"](24,Ee,1,0,"tr",62),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275nextContext"](3);s["\u0275\u0275property"]("infiniteScrollDistance",2)("scrollWindow",!1),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("dataSource",e.dataErrorSource),s["\u0275\u0275advance"](22),s["\u0275\u0275property"]("matHeaderRowDef",e.displayedErrorColumns)("matHeaderRowDefSticky",!0),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("matRowDefColumns",e.displayedErrorColumns)}}function Ie(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"div",75),s["\u0275\u0275elementStart"](1,"div"),s["\u0275\u0275elementStart"](2,"h4",76),s["\u0275\u0275text"](3," No Data found ! "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](4,"div",77),s["\u0275\u0275element"](5,"img",78),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]())}function Me(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"div",44),s["\u0275\u0275elementStart"](1,"div",45),s["\u0275\u0275template"](2,de,3,0,"div",46),s["\u0275\u0275template"](3,Pe,25,6,"div",79),s["\u0275\u0275template"](4,Ie,6,0,"div",80),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e){const e=s["\u0275\u0275nextContext"](2);s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("ngIf",e.isLoading),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",0!=e.errorResponse.length&&0==e.isLoading),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",0==e.errorResponse.length&&0==e.isLoading)}}const De=function(){return{"is-approved-bg":!0}},ke=function(e){return{color:e}},je=function(e){return{"background-color":e}};function Ae(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"div",1),s["\u0275\u0275elementStart"](1,"div",2),s["\u0275\u0275elementStart"](2,"div",3),s["\u0275\u0275elementStart"](3,"div",4),s["\u0275\u0275elementStart"](4,"div",5),s["\u0275\u0275elementStart"](5,"div",6),s["\u0275\u0275elementStart"](6,"div",2),s["\u0275\u0275elementStart"](7,"span",7),s["\u0275\u0275text"](8," Project - "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](9,"span",8),s["\u0275\u0275text"](10," Connections "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](11,"div",9),s["\u0275\u0275elementStart"](12,"div",10),s["\u0275\u0275elementStart"](13,"span",11),s["\u0275\u0275text"](14," Project ID "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](15,"div",12),s["\u0275\u0275elementStart"](16,"span",13),s["\u0275\u0275text"](17),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](18,"div",9),s["\u0275\u0275elementStart"](19,"div",10),s["\u0275\u0275elementStart"](20,"span",11),s["\u0275\u0275text"](21," Project Name "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](22,"div",12),s["\u0275\u0275elementStart"](23,"span",13),s["\u0275\u0275text"](24),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](25,"div",9),s["\u0275\u0275elementStart"](26,"div",10),s["\u0275\u0275elementStart"](27,"span",11),s["\u0275\u0275text"](28," Project Description "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](29,"div",12),s["\u0275\u0275elementStart"](30,"span",13),s["\u0275\u0275text"](31),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](32,"div",9),s["\u0275\u0275elementStart"](33,"div",10),s["\u0275\u0275elementStart"](34,"span",11),s["\u0275\u0275text"](35," Created By "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](36,"div",12),s["\u0275\u0275elementStart"](37,"span",13),s["\u0275\u0275element"](38,"app-user-profile",14),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](39,"div",9),s["\u0275\u0275elementStart"](40,"div",10),s["\u0275\u0275elementStart"](41,"span",11),s["\u0275\u0275text"](42," Created On "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](43,"div",12),s["\u0275\u0275elementStart"](44,"span",13),s["\u0275\u0275text"](45),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](46,"div",9),s["\u0275\u0275elementStart"](47,"div",10),s["\u0275\u0275elementStart"](48,"span",11),s["\u0275\u0275text"](49," Created At "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](50,"div",12),s["\u0275\u0275elementStart"](51,"span",13),s["\u0275\u0275text"](52),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](53,"div",9),s["\u0275\u0275elementStart"](54,"div",10),s["\u0275\u0275elementStart"](55,"span",11),s["\u0275\u0275text"](56," Changed By "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](57,"div",12),s["\u0275\u0275elementStart"](58,"span",13),s["\u0275\u0275text"](59),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](60,"div",9),s["\u0275\u0275elementStart"](61,"div",10),s["\u0275\u0275elementStart"](62,"span",11),s["\u0275\u0275text"](63," Changed On "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](64,"div",12),s["\u0275\u0275elementStart"](65,"span",13),s["\u0275\u0275text"](66),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](67,"div",9),s["\u0275\u0275elementStart"](68,"div",10),s["\u0275\u0275elementStart"](69,"span",11),s["\u0275\u0275text"](70," Changed At "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](71,"div",12),s["\u0275\u0275elementStart"](72,"span",13),s["\u0275\u0275text"](73),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](74,"div",15),s["\u0275\u0275elementStart"](75,"div",10),s["\u0275\u0275elementStart"](76,"span",11),s["\u0275\u0275text"](77," Status "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](78,"div",12),s["\u0275\u0275element"](79,"div",16),s["\u0275\u0275elementStart"](80,"span",17),s["\u0275\u0275text"](81),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](82,"div",18),s["\u0275\u0275elementStart"](83,"span",19),s["\u0275\u0275text"](84,"Total No of Connections "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](85,"div",15),s["\u0275\u0275elementStart"](86,"span",20),s["\u0275\u0275text"](87),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](88,"div",21),s["\u0275\u0275elementStart"](89,"mat-horizontal-stepper",22,23),s["\u0275\u0275listener"]("selectionChange",(function(t){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"]().selectionChange(t)})),s["\u0275\u0275elementStart"](91,"mat-step",24),s["\u0275\u0275elementStart"](92,"div",25),s["\u0275\u0275elementStart"](93,"div",26),s["\u0275\u0275elementStart"](94,"div",27),s["\u0275\u0275element"](95,"span"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](96,"div",28),s["\u0275\u0275elementStart"](97,"mat-form-field",29),s["\u0275\u0275elementStart"](98,"span",30),s["\u0275\u0275elementStart"](99,"mat-icon",31),s["\u0275\u0275text"](100,"search"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](101,"input",32),s["\u0275\u0275listener"]("keyup",(function(t){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"]().setSearchText(t)})),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](102,"mat-icon",33),s["\u0275\u0275template"](103,$,3,0,"button",34),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](104,"div",35),s["\u0275\u0275elementStart"](105,"button",36),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"]().refresh()})),s["\u0275\u0275elementStart"](106,"mat-icon",37),s["\u0275\u0275text"](107,"loop"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](108,"button",38),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"]().add_new_connection()})),s["\u0275\u0275elementStart"](109,"mat-icon",37),s["\u0275\u0275text"](110,"add_circle_outline"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](111,le,31,5,"div",39),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](112,"mat-step",40),s["\u0275\u0275elementStart"](113,"div",25),s["\u0275\u0275elementStart"](114,"div",26),s["\u0275\u0275elementStart"](115,"div",27),s["\u0275\u0275element"](116,"span"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](117,"div",28),s["\u0275\u0275elementStart"](118,"mat-form-field",29),s["\u0275\u0275elementStart"](119,"span",30),s["\u0275\u0275elementStart"](120,"mat-icon",31),s["\u0275\u0275text"](121,"search"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](122,"input",41),s["\u0275\u0275listener"]("keyup",(function(t){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"]().setErrorSearchText(t)})),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](123,"mat-icon",33),s["\u0275\u0275template"](124,ce,3,0,"button",34),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](125,"div",35),s["\u0275\u0275elementStart"](126,"button",36),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"]().refreshErrorLogs()})),s["\u0275\u0275elementStart"](127,"mat-icon",37),s["\u0275\u0275text"](128,"loop"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](129,Me,5,3,"div",39),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275nextContext"]();s["\u0275\u0275advance"](5),s["\u0275\u0275property"]("ngClass",s["\u0275\u0275pureFunction0"](20,De)),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("ngStyle",s["\u0275\u0275pureFunction1"](21,ke,e.getColor(e.projectData.status))),s["\u0275\u0275advance"](10),s["\u0275\u0275textInterpolate1"](" ",e.projectData.project_id," "),s["\u0275\u0275advance"](7),s["\u0275\u0275textInterpolate1"](" ",e.projectData.project_name," "),s["\u0275\u0275advance"](7),s["\u0275\u0275textInterpolate1"](" ",e.projectData.project_description," "),s["\u0275\u0275advance"](7),s["\u0275\u0275property"]("oid",e.projectData.created_by),s["\u0275\u0275advance"](7),s["\u0275\u0275textInterpolate1"](" ",e.projectData.created_on," "),s["\u0275\u0275advance"](7),s["\u0275\u0275textInterpolate1"](" ",e.projectData.created_at," "),s["\u0275\u0275advance"](7),s["\u0275\u0275textInterpolate1"](" ",e.projectData.changed_by," "),s["\u0275\u0275advance"](7),s["\u0275\u0275textInterpolate1"](" ",e.projectData.changed_on," "),s["\u0275\u0275advance"](7),s["\u0275\u0275textInterpolate1"](" ",e.projectData.changed_at," "),s["\u0275\u0275advance"](6),s["\u0275\u0275property"]("ngStyle",s["\u0275\u0275pureFunction1"](23,je,e.getColor(e.projectData.status))),s["\u0275\u0275advance"](2),s["\u0275\u0275textInterpolate1"](" ",e.projectData.status," "),s["\u0275\u0275advance"](6),s["\u0275\u0275textInterpolate1"]("",e.total_connection," "),s["\u0275\u0275advance"](14),s["\u0275\u0275property"]("formControl",e.searchText),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("ngIf",null!=e.searchText.value&&""!=e.searchText.value),s["\u0275\u0275advance"](8),s["\u0275\u0275property"]("ngIf",!e.isLoading),s["\u0275\u0275advance"](11),s["\u0275\u0275property"]("formControl",e.searchText),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("ngIf",null!=e.searchText.value&&""!=e.searchText.value),s["\u0275\u0275advance"](5),s["\u0275\u0275property"]("ngIf",!e.isLoading)}}let Fe=(()=>{class e{constructor(e,t,n,i,o,s,a){this.activatedRoute=e,this.fb=t,this.router=n,this.dialog=i,this.api=o,this._liveAnnouncer=s,this._toaster=a,this.displayedColumns=["connection_id","connection_name","connection_description","source_system","destination_system","acvtive","public_url","edit_connection"],this.displayedErrorColumns=["code","project_id","connection_id","error_params","error","timestamp","created_by"],this.errorResponse=[],this.Conn_Name="",this.fieldArray=[],this.src_resource=[],this.dest_resource=[],this.searchString="",this.selectedIndex=0,this.system_resource=[],this.system=[],this.searchText=new c.j,this.isLoading=!0,this.submitted=!1,this.submittedFields=!1,this.$onDestroy=new u.b,this.$onAppApiCalled=new u.b,this.displaymenuMasterData=[],this.tableData=[],this.filterConfig=[],this.displayConfig=[],this.isLoader=!0,this.application_id=418,this.secondHit=!0,this.headerShow=!1,this.latestRunId=0,this.projectData={project_id:"",project_name:"",project_description:"",created_by:"",created_on:"",created_at:""},this.addNewConnection=!1,this.lastPosition=1,this.project_flag=!1,this.clearSearch=()=>{this.searchText.patchValue(""),this.searchString=""},this.getColor=e=>"Active"==e?"#009432":"Inactive"==e?"#CF0001":void 0,this.loadTableView=e=>Object(r.c)(this,void 0,void 0,(function*(){this.filterConfig=T.filter(this.displaymenuMasterData,(function(t){return t.sequence_id==e})),this.tableData=yield this.getTableData(this.filterConfig),this.displayConfig=JSON.parse(this.filterConfig[0].fields),this.displayConfig=yield this.changeConfig(this.displayConfig)}))}ngOnInit(){this.project_flag=!1,this.isLoading=!0,this.pro_ID=this.activatedRoute.snapshot.paramMap.get("project_id"),this.api.getIntegrationErroLogs({project_id:this.pro_ID,current_id:this.pro_ID,lastIndex:this.lastPosition}).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{this.errorResponse=e,this.dataErrorSource=new p.l(e),this.dataErrorSource.paginator=this.paginator}),this.api.getSystems().pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{this.system=e}),console.log("Query Parameter ID : ",this.pro_ID),this.api.getProjectData(this.pro_ID).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{this.projectData=e[0],console.log("Particular Project : ",this.projectData),this.project_flag=!0}),this.api.getConnectionList(this.pro_ID).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).pipe(Object(h.a)(()=>{this.isLoading=!1})).subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){0!=e.length?(this.total_connection=e.length,Object.keys(e[0]).push("edit_connection"),console.log("Connection List : ",e),this.dataSource=new p.l(e),console.log("Data Source ConnectionList : ",this.dataSource),this.dataSource.paginator=this.paginator):(this.total_connection=0,console.log("No Data Found"),this.dataSource=new p.l(e),this.dataSource.paginator=this.paginator)})))}ngAfterViewInit(){}ngOnDestroy(){this.$onDestroy.next(),this.$onDestroy.complete()}setSearchText(e){return Object(r.c)(this,void 0,void 0,(function*(){this.isLoading=!0,this.searchText.patchValue(e.target.value),console.log("current searchText : ",this.searchText.value),this.searchString=this.searchText.value,this.dataSource.filter=e.target.value.trim().toLowerCase(),this.dataSource.paginator&&this.dataSource.paginator.firstPage(),this.isLoading=!1}))}setErrorSearchText(e){return Object(r.c)(this,void 0,void 0,(function*(){this.isLoading=!0,this.lastPosition=1,this.searchText.patchValue(e.target.value),console.log("current searchText : ",this.searchText.value),this.searchString=this.searchText.value.toUpperCase(),""==this.searchString.trim()&&this.refreshErrorLogs(),this.api.getIntegrationErroLogs({project_id:this.searchString,current_id:this.pro_ID,lastIndex:this.lastPosition}).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{this.errorResponse=e,this.lastPosition=Object.entries(this.errorResponse).length+1,this.dataErrorSource=new p.l(e),this.dataErrorSource.paginator=this.paginator,this.isLoading=!1})}))}onScrollDown(){this.api.getIntegrationErroLogs({project_id:this.searchString,current_id:this.pro_ID,lastIndex:this.lastPosition}).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{0!=e.length&&(this.errorResponse.push(...e),this.lastPosition=this.errorResponse.length+1,this.dataErrorSource=new p.l(this.errorResponse),this.dataErrorSource.paginator=this.paginator,console.log("Final error response length : ",this.errorResponse.length," Error response : ",this.errorResponse))})}RowClick(e){console.log("Row data  : ",e),this.router.navigateByUrl(`/main/ils/home/<USER>/${e.connection_id}/${e.source_system}`)}add_new_connection(){this.api.getLastConnectionId(this.pro_ID).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{this.connection_count=e[0].length,this.connection_count="CON-"+(this.connection_count+100),console.log("Source System Option : ",this.system),this.dialog.open(R,{height:"100%",width:"80%",position:{right:"0px"},data:{pro_id:this.pro_ID,connection_count:this.connection_count,system:this.system}}).afterClosed().pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){yield this.refresh()})))})}refresh(){return Object(r.c)(this,void 0,void 0,(function*(){this.isLoading=!0,this.api.getConnectionList(this.pro_ID).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){0!=e.length?(this.total_connection=e.length,Object.keys(e[0]).push("edit_connection"),console.log("Connection List : ",e),this.dataSource=new p.l(e),console.log("Data Source ConnectionList : ",this.dataSource),this.dataSource.paginator=this.paginator,this.isLoading=!1):(this.total_connection=0,console.log("No Data Found"),this.dataSource=new p.l(e),this.dataSource.paginator=this.paginator,this.isLoading=!1)})))}))}refreshErrorLogs(){return Object(r.c)(this,void 0,void 0,(function*(){this.isLoading=!0,this.lastPosition=1,this.api.getIntegrationErroLogs({project_id:this.pro_ID,current_id:this.pro_ID,lastIndex:this.lastPosition}).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{this.errorResponse=e,this.lastPosition=this.errorResponse.length+1,this.dataErrorSource=new p.l(e),this.dataErrorSource.paginator=this.paginator,this.isLoading=!1})}))}displayTableMasterData(){return new Promise((e,t)=>{this.api.getTableMasterData(this.application_id).subscribe(t=>{"S"==t.messType&&e(t.data)},e=>{console.log(e),t(e)})})}getTableData(e){return this.isLoader=!0,new Promise((t,n)=>{this.api.getTableData(e,this.latestRunId).subscribe(e=>{"S"==e.messType&&e.data.length>0&&(t(e.data),this.isLoader=!1,this._toaster.showSuccess(e.messText,"Success",2e3)),"E"==e.messType&&0==e.data.length&&(this.isLoader=!1,this._toaster.showWarning(e.messText,"Warning"),t(e.data))},e=>{this.isLoader=!1,console.log(e),n(e)})})}changeConfig(e){for(let t of e)t.allowEditing="true"==t.allowEditing&&"string"==typeof t.allowEditing;return e}selectionChange(e){return Object(r.c)(this,void 0,void 0,(function*(){this.clearSearch(),"Project Data"==e.selectedStep.label&&this.secondHit&&(this.secondHit=!1,this.headerShow=!0,this.displaymenuMasterData=yield this.displayTableMasterData(),this.loadTableView(this.displaymenuMasterData[0].sequence_id))}))}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275directiveInject"](o.a),s["\u0275\u0275directiveInject"](c.i),s["\u0275\u0275directiveInject"](o.g),s["\u0275\u0275directiveInject"](g.b),s["\u0275\u0275directiveInject"](y),s["\u0275\u0275directiveInject"](L.l),s["\u0275\u0275directiveInject"](N.a))},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-connection-list"]],viewQuery:function(e,t){if(1&e&&s["\u0275\u0275viewQuery"](d.a,!0),2&e){let e;s["\u0275\u0275queryRefresh"](e=s["\u0275\u0275loadQuery"]())&&(t.paginator=e.first)}},decls:1,vars:1,consts:[["class","container-fluid il-connection-list connection-details-styles pl-0 pr-0",4,"ngIf"],[1,"container-fluid","il-connection-list","connection-details-styles","pl-0","pr-0"],[1,"row"],[1,"col-3","pl-0","slide-in-right"],[1,"card","expense-details-card"],[1,"card-body","p-0"],[1,"pt-2","pb-2","pl-3","pr-0",3,"ngClass"],[1,"expense-category","mr-2",3,"ngStyle"],[1,"expense-sub-category"],[1,"row","pt-3"],[1,"col-4","d-flex","pl-0"],[1,"heading","my-auto"],[1,"col-8","d-flex","pl-2","pr-0"],[1,"content","my-auto"],["type","expense-card-data","imgHeight","28px","imgWidth","28px",3,"oid"],[1,"row","pt-2","pl-3"],[1,"status-circular",3,"ngStyle"],[1,"content","my-auto","ml-3"],[1,"row","pt-3","pl-3"],[1,"heading"],["type","simpleAndBold",1,"flex-1"],[1,"col-9","d-flex","justify-content-center","flex-column","align-self-start"],["linear","",2,"width","70vw",3,"selectionChange"],["stepper",""],["label","Project Connection"],[1,"row","pt-1"],[1,"col-2"],[1,"pl-3","pt-3","pb-3","col-md-12","col-3","title"],[1,"search-bar","d-flex","col-8","pt-1"],["appearance","outline",1,"col-12","ml-auto","mr-auto"],["matPrefix",""],[2,"font-size","21px !important","color","#66615b !important"],["matInput","","placeholder","Search Connections",3,"formControl","keyup"],["matSuffix",""],["mat-button","","matSuffix","","mat-icon-button","","aria-label","Clear","style","height: 30px; width: 30px; line-height: 1;",3,"click",4,"ngIf"],[1,"col-2","px-0","pt-2","pl-2","d-flex"],["mat-icon-button","","matTooltip","Refresh",1,"trend-button-inactive",3,"click"],[1,"iconButton"],["mat-icon-button","","matTooltip","Add New Connection",1,"trend-button-inactive",3,"click"],["class","drow col-12",4,"ngIf"],["label","Project Errors"],["matInput","","placeholder","Search Errors",3,"formControl","keyup"],["mat-button","","matSuffix","","mat-icon-button","","aria-label","Clear",2,"height","30px","width","30px","line-height","1",3,"click"],["matTooltip","Clear Search",2,"font-size","18px !important","color","#66615b !important"],[1,"drow","col-12"],[1,"row","pt-1","d-flex","justify-content-center"],["class","mt-2 mb-2",4,"ngIf"],["mat-table","","tabindex","0",1,"col-12",2,"max-height","50vh","z-index","1",3,"dataSource"],["matColumnDef","connection_id"],["class","col-2","mat-header-cell","",4,"matHeaderCellDef"],["class","col-2","mat-cell","",3,"title",4,"matCellDef"],["matColumnDef","connection_name"],["matColumnDef","connection_description"],["matColumnDef","source_system"],["matColumnDef","destination_system"],["matColumnDef","acvtive"],["class","col-1","mat-header-cell","",4,"matHeaderCellDef"],["class","col-1","mat-cell","",3,"title",4,"matCellDef"],["matColumnDef","public_url"],["matColumnDef","edit_connection"],["class","col-2","mat-cell","",4,"matCellDef"],["class","col-2","mat-header-row","",4,"matHeaderRowDef","matHeaderRowDefSticky"],["class","col-2","mat-row","",4,"matRowDef","matRowDefColumns"],["class","mat-row",4,"matNoDataRow"],[1,"mt-2","mb-2"],["diameter","60"],["mat-header-cell","",1,"col-2"],["mat-cell","",1,"col-2",3,"title"],["mat-header-cell","",1,"col-1"],["mat-cell","",1,"col-1",3,"title"],["mat-cell","",1,"col-2"],["mat-flat-button","",2,"color","#fff","background-color","#cf0001","border-color","#cf0001","font-weight","400","font-size","12px","border-radius","4px",3,"click"],["mat-header-row","",1,"col-2"],["mat-row","",1,"col-2"],[1,"mat-row"],["colspan","12",1,"mat-cell",2,"text-align","center"],[1,"d-flex","justify-content-center","align-items-center","mt-5","slide-in-top"],[1,"d-flex","justify-content-center","align-items-center","slide-from-down"],["src","https://assets.kebs.app/images/nomilestone.png","height","240","width","340",1,"mt-4","mb-3"],["class","col-12","class","infinite-scroll-auto","infinite-scroll","",3,"infiniteScrollDistance","scrollWindow","scrolled",4,"ngIf"],["class","mat-cell","style","text-align: center;","colspan","12",4,"ngIf"],["infinite-scroll","",1,"infinite-scroll-auto",3,"infiniteScrollDistance","scrollWindow","scrolled"],["mat-table","",1,"col-lg-12",3,"dataSource"],["matColumnDef","code"],["class","col-2","style","font-size: xx-small;","mat-cell","",3,"matTooltip",4,"matCellDef"],["matColumnDef","project_id"],["matColumnDef","error_params"],["class","col-2  text-truncate","style","max-width: 16vw;font-size: xx-small;","mat-cell","",3,"matTooltip",4,"matCellDef"],["matColumnDef","error"],["matColumnDef","created_by"],["class","col-1  text-truncate","mat-cell","","style","max-width: 6vw;font-size: xx-small;",3,"matTooltip",4,"matCellDef"],["matColumnDef","timestamp"],["style","max-width: 6vw;font-size: xx-small;","class","col-1  text-truncate","mat-cell","",3,"matTooltip",4,"matCellDef"],["mat-cell","",1,"col-2",2,"font-size","xx-small",3,"matTooltip"],["mat-cell","",1,"col-2","text-truncate",2,"max-width","16vw","font-size","xx-small",3,"matTooltip"],["mat-cell","",1,"col-1","text-truncate",2,"max-width","6vw","font-size","xx-small",3,"matTooltip"]],template:function(e,t){1&e&&s["\u0275\u0275template"](0,Ae,130,25,"div",0),2&e&&s["\u0275\u0275property"]("ngIf",t.project_flag)},directives:[i.NgIf,i.NgClass,i.NgStyle,B.a,S.a,S.b,C.c,C.h,_.a,x.b,c.e,c.v,c.k,C.i,b.a,v.a,p.k,p.c,p.e,p.b,p.g,p.j,p.h,P.c,p.d,p.a,p.f,p.i,q.a],pipes:[i.JsonPipe],styles:['.connection-details-styles[_ngcontent-%COMP%]   table[_ngcontent-%COMP%], .connection-details-styles[_ngcontent-%COMP%]   td[_ngcontent-%COMP%], .connection-details-styles[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{border-collapse:collapse}.connection-details-styles[_ngcontent-%COMP%]   .infinite-scroll-auto[_ngcontent-%COMP%]{height:400px;overflow:scroll;width:100%}.connection-details-styles[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{background-color:#d5acac;color:#181a19;font-weight:700}.connection-details-styles[_ngcontent-%COMP%]   .expense-details-card[_ngcontent-%COMP%]{min-height:91vh}.connection-details-styles[_ngcontent-%COMP%]   .expense-category[_ngcontent-%COMP%]{font-size:14px;font-weight:500}.connection-details-styles[_ngcontent-%COMP%]   .expense-sub-category[_ngcontent-%COMP%]{font-size:14px;color:#1a1a1a;font-weight:500;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.connection-details-styles[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]{color:#6f6e6c}.connection-details-styles[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%], .connection-details-styles[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]{font-size:14px;font-weight:400;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.connection-details-styles[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{color:#1a1a1a}.connection-details-styles[_ngcontent-%COMP%]   .overflow-ctrl[_ngcontent-%COMP%]{width:85%;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.connection-details-styles[_ngcontent-%COMP%]   .required-amount[_ngcontent-%COMP%]{font-weight:600;font-size:18px}.connection-details-styles[_ngcontent-%COMP%]   .status-in-thread[_ngcontent-%COMP%]{color:#1a1a1a}.connection-details-styles[_ngcontent-%COMP%]   .status-in-thread[_ngcontent-%COMP%], .connection-details-styles[_ngcontent-%COMP%]   .table-heading[_ngcontent-%COMP%]{font-size:13px;font-weight:400;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.connection-details-styles[_ngcontent-%COMP%]   .table-heading[_ngcontent-%COMP%]{color:#6f6e6c}.connection-details-styles[_ngcontent-%COMP%]   .grey-heading[_ngcontent-%COMP%]{font-size:14px;font-weight:400;color:#6f6e6c;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.connection-details-styles[_ngcontent-%COMP%]   .status-circular[_ngcontent-%COMP%]{background-color:#ffb142}.connection-details-styles[_ngcontent-%COMP%]   .status-circular[_ngcontent-%COMP%], .connection-details-styles[_ngcontent-%COMP%]   .status-circular-in-thread[_ngcontent-%COMP%]{height:13px;width:13px;margin-top:3px;border-radius:50%;box-shadow:0 2px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.connection-details-styles[_ngcontent-%COMP%]   .status-circular-in-thread[_ngcontent-%COMP%]{background-color:#c7c4c4}.connection-details-styles[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]{border-bottom:1px solid #ddd}.connection-details-styles[_ngcontent-%COMP%]   .col-1x[_ngcontent-%COMP%]{flex:0 0 6.333333%;max-width:7.333333%}.connection-details-styles[_ngcontent-%COMP%]   .done-icon[_ngcontent-%COMP%]{color:#009432;font-size:21px}.connection-details-styles[_ngcontent-%COMP%]   .is-submitted-bg[_ngcontent-%COMP%]{background:#fffaf1}.connection-details-styles[_ngcontent-%COMP%]   .is-approved-bg[_ngcontent-%COMP%]{background:#e6fbe1}.connection-details-styles[_ngcontent-%COMP%]   .is-rejected-bg[_ngcontent-%COMP%], .connection-details-styles[_ngcontent-%COMP%]   .is-verified-bg[_ngcontent-%COMP%]{background:#ffebea}.connection-details-styles[_ngcontent-%COMP%]   .is-payed-bg[_ngcontent-%COMP%]{background:#e6fbe1}.connection-details-styles[_ngcontent-%COMP%]   .is-ko-submitted-bg[_ngcontent-%COMP%]{background:#ffebd3}.connection-details-styles[_ngcontent-%COMP%]   .is-ko-approved-bg[_ngcontent-%COMP%]{background:#e2f5f6}.connection-details-styles[_ngcontent-%COMP%]   .is-closed-bg[_ngcontent-%COMP%]{background:#e6ffef}.connection-details-styles[_ngcontent-%COMP%]   .is-submitted-circular[_ngcontent-%COMP%]{background:#ffb142}.connection-details-styles[_ngcontent-%COMP%]   .is-approved-circular[_ngcontent-%COMP%]{background:#badc58}.connection-details-styles[_ngcontent-%COMP%]   .is-verified-circular[_ngcontent-%COMP%]{background:#9f2825}.connection-details-styles[_ngcontent-%COMP%]   .is-payed-circular[_ngcontent-%COMP%]{background:#079992}.connection-details-styles[_ngcontent-%COMP%]   .is-ko-submitted-circular[_ngcontent-%COMP%]{background:#e58e26}.connection-details-styles[_ngcontent-%COMP%]   .is-ko-approved-circular[_ngcontent-%COMP%]{background:#5ce88c}.connection-details-styles[_ngcontent-%COMP%]   .is-closed-circular[_ngcontent-%COMP%]{background:#009432}.connection-details-styles[_ngcontent-%COMP%]   .slide-in-right[_ngcontent-%COMP%]{animation:slide-in-right .3s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-right{0%{transform:translateX(-15px);opacity:0}to{transform:translateX(0);opacity:1}}.connection-details-styles[_ngcontent-%COMP%]   .expenses-card[_ngcontent-%COMP%]:hover{box-shadow:0 5px 8px 0 rgba(0,0,0,.1),0 4px 5px -1px rgba(0,0,0,.1);cursor:pointer;transition:box-shadow .3s}.connection-details-styles[_ngcontent-%COMP%]   .expenses-card[_ngcontent-%COMP%]:hover   .expense-item-button[_ngcontent-%COMP%]{visibility:visible;animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}.connection-details-styles[_ngcontent-%COMP%]   .expenses-card[_ngcontent-%COMP%]   .col[_ngcontent-%COMP%], .connection-details-styles[_ngcontent-%COMP%]   .expenses-card[_ngcontent-%COMP%]   .col-auto[_ngcontent-%COMP%], .connection-details-styles[_ngcontent-%COMP%]   .expenses-card[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]{padding:0!important;margin:0!important}.connection-details-styles[_ngcontent-%COMP%]   .expenses-card[_ngcontent-%COMP%]   .third-row[_ngcontent-%COMP%]{padding-top:1px!important;padding-bottom:2px!important;background-color:#f1f2f6}.connection-details-styles[_ngcontent-%COMP%]   .button-col[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{border-right:2px solid #c1c1c1;padding:4px!important}.connection-details-styles[_ngcontent-%COMP%]   .expenses-status[_ngcontent-%COMP%]{border-radius:0 5px 5px 0;box-shadow:0 2px 1px 0 rgba(0,0,0,.2),0 1px 1px 0 rgba(0,0,0,.19);font-size:13px!important}.connection-details-styles[_ngcontent-%COMP%]   .expenses-status[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{padding-top:3px;padding-bottom:3px;text-align:center}.connection-details-styles[_ngcontent-%COMP%]   .expenses-name[_ngcontent-%COMP%]{color:#cf000f!important;font-size:15px!important}.connection-details-styles[_ngcontent-%COMP%]   .customer-name[_ngcontent-%COMP%]{color:#1c1a1a!important;font-size:14px!important;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.connection-details-styles[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{color:#5f5f5f;font-size:11px!important}.connection-details-styles[_ngcontent-%COMP%]   .data-label[_ngcontent-%COMP%]{font-size:13px!important;white-space:nowrap;max-width:125px;overflow:hidden;text-overflow:ellipsis}.connection-details-styles[_ngcontent-%COMP%]   .initName[_ngcontent-%COMP%]{align-self:center;padding-left:5px}.connection-details-styles[_ngcontent-%COMP%]   .milestone[_ngcontent-%COMP%]{font-size:12px}.connection-details-styles[_ngcontent-%COMP%]   .milestone[_ngcontent-%COMP%]   .milestone-name[_ngcontent-%COMP%]{color:#cf000f!important;font-size:14px!important}.connection-details-styles[_ngcontent-%COMP%]   .milestone[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:18px!important;vertical-align:middle}.connection-details-styles[_ngcontent-%COMP%]   .miles-col[_ngcontent-%COMP%]{min-height:40vh;max-height:40vh;overflow:scroll}.connection-details-styles[_ngcontent-%COMP%]   .miles-col[_ngcontent-%COMP%]   mat-card[_ngcontent-%COMP%]{min-height:40vh}.connection-details-styles[_ngcontent-%COMP%]   .miles-col[_ngcontent-%COMP%]   .mat-card-subtitle[_ngcontent-%COMP%]{margin-bottom:4px!important}.connection-details-styles[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]{height:28px;line-height:28px}.connection-details-styles[_ngcontent-%COMP%]   .expenses-code[_ngcontent-%COMP%]{font-size:14px;font-weight:420}.connection-details-styles[_ngcontent-%COMP%]   .expenses-card[_ngcontent-%COMP%]   [_ngcontent-%COMP%]:hover{cursor:pointer}.connection-details-styles[_ngcontent-%COMP%]   .chat-button[_ngcontent-%COMP%]{width:32px!important;height:32px!important;line-height:32px!important;position:absolute;right:5px;top:4px}@media (min-width:992px){.connection-details-styles[_ngcontent-%COMP%]   .col-2x[_ngcontent-%COMP%]{flex:0 0 13.666667%;max-width:13.666667%}.connection-details-styles[_ngcontent-%COMP%]   .col-1x[_ngcontent-%COMP%]{flex:0 0 12.333333%;max-width:12.333333%}.connection-details-styles[_ngcontent-%COMP%]   .col-3x[_ngcontent-%COMP%]{flex:0 0 20.666667%;max-width:20.666667%}.connection-details-styles[_ngcontent-%COMP%]   .third-row[_ngcontent-%COMP%]   .col-1x[_ngcontent-%COMP%]:not(:last-child), .connection-details-styles[_ngcontent-%COMP%]   .third-row[_ngcontent-%COMP%]   .col-2x[_ngcontent-%COMP%]:not(:last-child), .connection-details-styles[_ngcontent-%COMP%]   .third-row[_ngcontent-%COMP%]   .col-3x[_ngcontent-%COMP%]:not(:last-child){border-right:2px solid #c1c1c1}.connection-details-styles[_ngcontent-%COMP%]   .third-row[_ngcontent-%COMP%]   .col-1x[_ngcontent-%COMP%]:not(:first-child)   mat-icon[_ngcontent-%COMP%], .connection-details-styles[_ngcontent-%COMP%]   .third-row[_ngcontent-%COMP%]   .col-2x[_ngcontent-%COMP%]:not(:first-child)   mat-icon[_ngcontent-%COMP%], .connection-details-styles[_ngcontent-%COMP%]   .third-row[_ngcontent-%COMP%]   .col-3x[_ngcontent-%COMP%]:not(:first-child)   mat-icon[_ngcontent-%COMP%]{font-size:22px!important;color:#474444!important}}.connection-details-styles[_ngcontent-%COMP%]   .data-label-partners[_ngcontent-%COMP%]{font-size:13px!important;font-weight:420!important;color:#1c1a1a!important;margin-bottom:3px!important}.connection-details-styles[_ngcontent-%COMP%]   .header-row[_ngcontent-%COMP%]{justify-content:space-evenly;color:#9a9a9a}.connection-details-styles[_ngcontent-%COMP%]   .header-row[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#252422;font-weight:400}.connection-details-styles[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{font-size:12px;color:#746f6f!important;font-weight:400}.connection-details-styles[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{font-size:12px}.connection-details-styles[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%], .connection-details-styles[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{border-top:none!important;text-align:center}.connection-details-styles[_ngcontent-%COMP%]   .text-overflow[_ngcontent-%COMP%]{width:calc(100% - 20px)!important}.connection-details-styles[_ngcontent-%COMP%]   .text-overflow[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .connection-details-styles[_ngcontent-%COMP%]   .text-overflow[_ngcontent-%COMP%]   text[_ngcontent-%COMP%]{white-space:nowrap;width:95%;overflow:hidden;text-overflow:ellipsis}.connection-details-styles[_ngcontent-%COMP%]   .location-overflow[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;font-size:14px!important}.connection-details-styles[_ngcontent-%COMP%]   td[_ngcontent-%COMP%], .connection-details-styles[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{text-align:left!important}.connection-details-styles[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:last-child, .connection-details-styles[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:last-child{padding-left:8px!important}.connection-details-styles[_ngcontent-%COMP%]   .expense-item-button[_ngcontent-%COMP%]{height:26px!important;width:26px!important;line-height:26px;visibility:hidden}.connection-details-styles[_ngcontent-%COMP%]   .expense-item-button[_ngcontent-%COMP%]   .expense-item-button-icon[_ngcontent-%COMP%]{color:#66615b!important;font-size:19px!important}.connection-details-styles[_ngcontent-%COMP%]   .expense-item-table-button[_ngcontent-%COMP%]{height:28px!important;width:28px!important;line-height:28px}.connection-details-styles[_ngcontent-%COMP%]   .expense-item-table-button[_ngcontent-%COMP%]   .expense-item-table-icon[_ngcontent-%COMP%]{color:#66615b!important;font-size:18px!important}@keyframes slide-top{0%{transform:translateY(10px)}to{transform:translateY(0)}}.connection-details-styles[_ngcontent-%COMP%]   .status-text-Verified[_ngcontent-%COMP%]{color:#9f2825}.connection-details-styles[_ngcontent-%COMP%]   .status-text-Active[_ngcontent-%COMP%]{color:#009432}.connection-details-styles[_ngcontent-%COMP%]   .status-text-Inactive[_ngcontent-%COMP%]{color:#cf0001}.connection-details-styles[_ngcontent-%COMP%]   .is-draft[_ngcontent-%COMP%]{background:#a9a9a9;color:#fff!important}.connection-details-styles[_ngcontent-%COMP%]   .is-submitted[_ngcontent-%COMP%]{background:#ffa502;color:#fff!important}.connection-details-styles[_ngcontent-%COMP%]   .is-approved[_ngcontent-%COMP%]{background:#badc58;color:#fff!important}.connection-details-styles[_ngcontent-%COMP%]   .is-verified[_ngcontent-%COMP%]{background:#9f2825;color:#fff!important}.connection-details-styles[_ngcontent-%COMP%]   .is-rejected[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff}.connection-details-styles[_ngcontent-%COMP%]   .is-payed[_ngcontent-%COMP%]{background:#079992;color:#fff!important}.connection-details-styles[_ngcontent-%COMP%]   .is-ko-submitted[_ngcontent-%COMP%]{background:#e58e26;color:#fff!important}.connection-details-styles[_ngcontent-%COMP%]   .is-ko-approved[_ngcontent-%COMP%]{background:#5ce88c;color:#fff!important}.connection-details-styles[_ngcontent-%COMP%]   .is-closed[_ngcontent-%COMP%]{background:#009432;color:#fff!important}.connection-details-styles[_ngcontent-%COMP%]   .tooltip-text[_ngcontent-%COMP%]{font-size:12px;font-family:Roboto,Helvetica Neue,"sans-serif"!important}.connection-details-styles[_ngcontent-%COMP%]   .tooltip-status-indicator[_ngcontent-%COMP%]{font-size:12px;margin-top:9px}.connection-details-styles[_ngcontent-%COMP%]   .headerClass[_ngcontent-%COMP%]{font-size:medium;font-weight:500;color:red;padding-left:240px}.connection-details-styles[_ngcontent-%COMP%]   .trend-button-inactive[_ngcontent-%COMP%]{line-height:8px;width:26px;height:26px;margin-right:12px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.connection-details-styles[_ngcontent-%COMP%]   .trend-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.connection-details-styles[_ngcontent-%COMP%]   .trend-button-inactive[_ngcontent-%COMP%]   .iconButtonWarn[_ngcontent-%COMP%]{color:#cf0001;font-size:18px}.connection-details-styles[_ngcontent-%COMP%]   .trend-button-inactive[_ngcontent-%COMP%]   .iconButtonWarnAnim[_ngcontent-%COMP%]{color:#cf0001;font-size:18px;animation:blink 1.1s cubic-bezier(.5,0,1,1) infinite alternate}.connection-details-styles[_ngcontent-%COMP%]   .trend-button-active[_ngcontent-%COMP%]{background-color:#cf0001;line-height:8px;width:26px;height:26px;margin-right:12px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.connection-details-styles[_ngcontent-%COMP%]   .trend-button-active[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#fff;font-size:18px}'],data:{animation:[Object(l.o)("slideInOut",[Object(l.l)("in",Object(l.m)({height:"*",overflow:"hidden"})),Object(l.l)("out",Object(l.m)({height:0,overflow:"hidden"})),Object(l.n)("* => in",[Object(l.m)({height:0}),Object(l.e)(120,Object(l.m)({height:"*"}))]),Object(l.n)("in=> *",[Object(l.m)({height:"*"}),Object(l.e)(120,Object(l.m)({height:0}))])]),Object(l.o)("fadeInOut",[Object(l.l)("void",Object(l.m)({opacity:0})),Object(l.n)("void <=> *",Object(l.e)(400))])]}}),e})(),Re=(()=>{class e{constructor(){}ngOnInit(){}ngOnDestroy(){}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-runapi"]],decls:0,vars:0,template:function(e,t){},styles:["table[_ngcontent-%COMP%]{width:100%}.row[_ngcontent-%COMP%]{margin-top:50px}.filter[_ngcontent-%COMP%]{padding-bottom:20px}table[_ngcontent-%COMP%], td[_ngcontent-%COMP%], th[_ngcontent-%COMP%]{border-collapse:collapse}th[_ngcontent-%COMP%]{background-color:#d5acac;color:#181a19;font-weight:700}.mat-row[_ngcontent-%COMP%]   .mat-cell[_ngcontent-%COMP%]{border-bottom:1px solid transparent;border-top:1px solid transparent}.mat-row[_ngcontent-%COMP%]:hover   .mat-cell[_ngcontent-%COMP%]{border-color:currentColor;background-color:#f6eee3}.demo-row-is-clicked[_ngcontent-%COMP%]{font-weight:700}"]}),e})();var Te=n("Dh3D"),Le=n("wd/R"),Ne=n.n(Le);let Be=(()=>{class e{constructor(e,t,n,i,o){this.data=e,this.datePipe=t,this.fb=n,this.api=i,this.dialogRef=o,this.submitted=!1,this.$onDestroy=new u.b,this.$onAppApiCalled=new u.b}ngOnInit(){this.date=String(Ne()(Date.now()).format("YYYY-MM-DD")),this.time=String(Ne()(Date.now()).format("hh:mm:ss")),this.projectIDdata="PRO-"+Number(this.data.modalparams+100),this.saveProject=this.fb.group({projectName:["",c.H.required],projectDescription:["",c.H.required]})}get f(){return this.saveProject.controls}ngOnDestroy(){this.$onDestroy.next(),this.$onDestroy.complete()}save(){var e,t;if(this.submitted=!0,!this.saveProject.invalid){var n={project_id:"",project_name:"",project_description:"",created_on:"",created_at:""};n.project_id="PRO-"+Number(this.data.modalparams+100),n.project_name=null===(e=this.saveProject.get("projectName"))||void 0===e?void 0:e.value,n.project_description=null===(t=this.saveProject.get("projectDescription"))||void 0===t?void 0:t.value,n.created_on=this.date,n.created_at=this.time,this.api.postNewProject(n).pipe(Object(h.a)(()=>{this.dialogRef.close()})).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{console.log("New Project Insertion Status : ",e)})}}close_connection(){this.dialogRef.close()}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275directiveInject"](g.a),s["\u0275\u0275directiveInject"](i.DatePipe),s["\u0275\u0275directiveInject"](c.i),s["\u0275\u0275directiveInject"](y),s["\u0275\u0275directiveInject"](g.h))},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-newproject"]],features:[s["\u0275\u0275ProvidersFeature"]([i.DatePipe])],decls:53,vars:4,consts:[[1,"mat-typography",2,"display","block","padding","24px","border-radius","4px","box-sizing","border-box","overflow","auto","outline","0","width","100%","height","100%","min-height","inherit","max-height","inherit"],[1,"ecternal-container"],[1,"container"],[1,"row","border-bottom","solid",2,"font-size","18px","color","#CF0001"],[1,"col-11","pt-2","pb-2","d-flex"],["mat-icon-button","",1,"bubble","mt-1"],[1,"iconButton"],[1,"name","my-auto","ml-2"],[1,"col-1","d-flex","pt-2"],["mat-icon-button","",1,"ml-auto","close-button","mt-1"],["matTooltip","Close",1,"close-Icon",2,"cursor","pointer","color","#4d4d4b",3,"click"],[1,""],[1,"example-form",3,"formGroup","ngSubmit"],[1,"row"],["appearance","outline",1,"col-12"],["matInput","","readonly","","placeholder","Project ID",3,"value"],["matSuffix",""],["matInput","","formControlName","projectName","placeholder","Project Name","required",""],["matInput","","formControlName","projectDescription","placeholder","Project Name","required",""],["matInput","","placeholder","Created On","readonly","",3,"value"],["matInput","","placeholder","Created At","readonly","",3,"value"],[1,"d-flex","align-items-end","flex-column"],["_ngcontent-wgq-c234","","mat-icon-button","","mattooltip","Save Project","aria-describedby","cdk-describedby-message-16","cdk-describedby-host","",1,"align-content-end","mat-focus-indicator","mat-tooltip-trigger","iconbtn","ml-4","mr-5","mt-1","mb-1","ng-tns-c234-16","mat-icon-button","mat-button-base",2,"background-color","rgb(207, 0, 1)"],[1,"mat-button-wrapper"],["_ngcontent-wgq-c234","","role","img","aria-hidden","true","data-mat-icon-type","font",1,"align-content-end","mat-icon","notranslate","ng-tns-c234-16","material-icons","mat-icon-no-color","ng-star-inserted",2,"color","white"],["matripple","",1,"mat-ripple","mat-button-ripple","mat-button-ripple-round"],[1,"align-content-end","mat-button-focus-overlay"]],template:function(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"mat-dialog-content",0),s["\u0275\u0275elementStart"](1,"div",1),s["\u0275\u0275elementStart"](2,"div",2),s["\u0275\u0275elementStart"](3,"div",3),s["\u0275\u0275elementStart"](4,"div",4),s["\u0275\u0275elementStart"](5,"div",5),s["\u0275\u0275elementStart"](6,"mat-icon",6),s["\u0275\u0275text"](7,"note_add"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](8,"span",7),s["\u0275\u0275text"](9,"Create New Project"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](10,"div",8),s["\u0275\u0275elementStart"](11,"button",9),s["\u0275\u0275elementStart"](12,"mat-icon",10),s["\u0275\u0275listener"]("click",(function(){return t.close_connection()})),s["\u0275\u0275text"](13,"close"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](14,"div",11),s["\u0275\u0275elementStart"](15,"form",12),s["\u0275\u0275listener"]("ngSubmit",(function(){return t.save()})),s["\u0275\u0275elementStart"](16,"div",13),s["\u0275\u0275elementStart"](17,"mat-form-field",14),s["\u0275\u0275elementStart"](18,"mat-label"),s["\u0275\u0275text"](19,"Project ID"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275element"](20,"input",15),s["\u0275\u0275element"](21,"mat-icon",16),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](22,"mat-form-field",14),s["\u0275\u0275elementStart"](23,"mat-label"),s["\u0275\u0275text"](24,"Project Name"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275element"](25,"input",17),s["\u0275\u0275element"](26,"mat-icon",16),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](27,"mat-form-field",14),s["\u0275\u0275elementStart"](28,"mat-label"),s["\u0275\u0275text"](29,"Project Description"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275element"](30,"textarea",18),s["\u0275\u0275element"](31,"mat-icon",16),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](32,"mat-form-field",14),s["\u0275\u0275elementStart"](33,"mat-label"),s["\u0275\u0275text"](34,"Created On"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275element"](35,"input",19),s["\u0275\u0275elementStart"](36,"mat-icon",16),s["\u0275\u0275text"](37,"calendar_today"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275element"](38,"mat-icon",16),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](39,"mat-form-field",14),s["\u0275\u0275elementStart"](40,"mat-label"),s["\u0275\u0275text"](41,"Created At"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275element"](42,"input",20),s["\u0275\u0275elementStart"](43,"mat-icon",16),s["\u0275\u0275text"](44,"watch_later"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275element"](45,"mat-icon",16),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](46,"div",21),s["\u0275\u0275elementStart"](47,"button",22),s["\u0275\u0275elementStart"](48,"span",23),s["\u0275\u0275elementStart"](49,"mat-icon",24),s["\u0275\u0275text"](50," done_all"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275element"](51,"span",25),s["\u0275\u0275element"](52,"span",26),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e&&(s["\u0275\u0275advance"](15),s["\u0275\u0275property"]("formGroup",t.saveProject),s["\u0275\u0275advance"](5),s["\u0275\u0275property"]("value",t.projectIDdata),s["\u0275\u0275advance"](15),s["\u0275\u0275property"]("value",t.date),s["\u0275\u0275advance"](7),s["\u0275\u0275property"]("value",t.time))},directives:[g.f,_.a,b.a,v.a,c.J,c.w,c.n,C.c,C.g,x.b,C.i,c.e,c.v,c.l,c.F],styles:[".head[_ngcontent-%COMP%]{height:30px;background-color:#f94747;color:#fff;text-align:center}.example-form[_ngcontent-%COMP%]{margin-top:30px}.new-project[_ngcontent-%COMP%]{display:flex;justify-content:space-around;flex-direction:column;margin-bottom:0}.row[_ngcontent-%COMP%]{margin-bottom:20px;display:flex;align-items:baseline}.saveproject[_ngcontent-%COMP%]{float:right;margin-right:0}"]}),e})();var qe=n("STbY"),$e=n("Wp6s");function Ve(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"button",26),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"]().showItems()})),s["\u0275\u0275elementStart"](1,"mat-icon",27),s["\u0275\u0275text"](2),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275nextContext"]();s["\u0275\u0275advance"](2),s["\u0275\u0275textInterpolate"](e.expand?"keyboard_arrow_up":"keyboard_arrow_down")}}function ze(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"tbody"),s["\u0275\u0275elementStart"](1,"tr",34),s["\u0275\u0275elementStart"](2,"td",35),s["\u0275\u0275text"](3),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](4,"td",36),s["\u0275\u0275text"](5),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](6,"td",35),s["\u0275\u0275text"](7),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](8,"td",35),s["\u0275\u0275text"](9),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](10,"td",35),s["\u0275\u0275text"](11),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](12,"td",35),s["\u0275\u0275text"](13),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](14,"td",35),s["\u0275\u0275text"](15),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](16,"td",35),s["\u0275\u0275elementStart"](17,"span",37),s["\u0275\u0275text"](18),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](19,"td",35),s["\u0275\u0275text"](20),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=t.index;s["\u0275\u0275advance"](3),s["\u0275\u0275textInterpolate"](n+1),s["\u0275\u0275advance"](1),s["\u0275\u0275propertyInterpolate"]("matTooltip",e.connection_description),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate"](e.connection_id),s["\u0275\u0275advance"](2),s["\u0275\u0275textInterpolate"](e.project_id),s["\u0275\u0275advance"](2),s["\u0275\u0275textInterpolate"](e.connection_name),s["\u0275\u0275advance"](2),s["\u0275\u0275textInterpolate1"](" ",e.source_system," "),s["\u0275\u0275advance"](2),s["\u0275\u0275textInterpolate1"](" ",e.destination_system," "),s["\u0275\u0275advance"](2),s["\u0275\u0275textInterpolate1"](" ",e.connection_type," "),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("ngClass","status-text-"+e.active),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate1"](" ",e.active," "),s["\u0275\u0275advance"](2),s["\u0275\u0275textInterpolate1"](" ",e.connection_type," ")}}function He(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"tbody"),s["\u0275\u0275elementStart"](1,"tr"),s["\u0275\u0275element"](2,"mat-spinner",38),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]())}function Je(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"div",28),s["\u0275\u0275elementStart"](1,"div",29),s["\u0275\u0275elementStart"](2,"table",30),s["\u0275\u0275elementStart"](3,"thead"),s["\u0275\u0275elementStart"](4,"tr"),s["\u0275\u0275elementStart"](5,"th",31),s["\u0275\u0275text"](6,"S.no"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](7,"th",31),s["\u0275\u0275text"](8,"Connection ID"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](9,"th",31),s["\u0275\u0275text"](10,"Project ID"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](11,"th",31),s["\u0275\u0275text"](12,"Connection Name"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](13,"th",31),s["\u0275\u0275text"](14,"Source System"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](15,"th",31),s["\u0275\u0275text"](16,"Destination System"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](17,"th",31),s["\u0275\u0275text"](18,"Connection Type"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](19,"th",31),s["\u0275\u0275text"](20,"Active status"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](21,"th",31),s["\u0275\u0275text"](22,"Source Resources"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](23,ze,21,11,"tbody",32),s["\u0275\u0275template"](24,He,3,0,"tbody",33),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e){const e=s["\u0275\u0275nextContext"]();s["\u0275\u0275property"]("@slideInOut",e.expand?"in":"out"),s["\u0275\u0275advance"](23),s["\u0275\u0275property"]("ngForOf",e.itemDetails),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",null==e.itemDetails)}}const Ue=function(e,t){return{"is-rejected":e,"is-closed":t}};let Ye=(()=>{class e{constructor(e,t){this.api=e,this.router=t,this.itemDetails=null,this.expand=!1,this.$onDestroy=new u.b,this.$onAppApiCalled=new u.b}ngOnInit(){console.log("On Init : ",this.expenseItem)}ngOnDestroy(){this.$onDestroy.next(),this.$onDestroy.complete()}onExpenseItemClick(){this.router.navigateByUrl("main/ils/home/<USER>/"+this.expenseItem.project_id)}showItems(){event.preventDefault(),event.stopPropagation(),this.expand=!this.expand,this.getItemDetails()}getItemDetails(){this.itemDetails=null,this.api.getConnectionList(this.expenseItem.project_id).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{this.itemDetails=e})}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275directiveInject"](y),s["\u0275\u0275directiveInject"](o.g))},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-project-card"]],inputs:{expenseItem:"expenseItem"},decls:55,vars:21,consts:[[1,"il-body-styles"],["fxLayoutAlign","start end",1,"expenses-card","container-fluid","p-0","pt-2","mt-1","mb-1",2,"width","96% !important"],[1,"row","pb-1"],[1,"col-6","col-md-2","col-2x","p-0","pb-2","my-auto"],[1,"w-75","expenses-status"],[3,"ngClass"],[1,"col-6","col-md-5","col-lg-7","customer-name","d-flex","align-items-center","pl-0",2,"padding-bottom","0.5rem !important",3,"click"],[1,"pr-1","expenses-name"],[1,"pr-1","description",2,"white-space","nowrap","overflow","hidden","text-overflow","ellipsis","width","35rem"],[1,"col-6","order-2","pl-0","pr-1","order-md-3","col-md-3","col-2x","d-flex","justify-content-start","ml-0",2,"font-size","15px","padding-bottom","0.5rem !important",3,"click"],[1,"location-overflow","my-auto",3,"matTooltip"],[1,"row","third-row"],[1,"col-6","col-md-2","col-1x","p-0","d-flex","flex-row","pb-2","pb-lg-0","px-3"],[1,"my-auto","pt-4","pr-3","pl-0"],[2,"color","#474444","font-size","14px"],[1,"m-0","header","pt-1"],[1,"m-0","data-label","pt-1",3,"matTooltip"],[1,"col-6","col-md-2","col-2x","p-0","d-flex","flex-row","pb-2","pb-lg-0","px-3","pt-1",2,"padding-left","10px !important"],[1,"col-6","col-md-2","col-3x","p-0","d-flex","flex-row","pb-2","pb-lg-0","px-3"],[1,"row"],["type","expense-card-data","imgHeight","28px","imgWidth","28px",3,"oid"],[1,"col-6","col-md-2","col-2","p-0","d-flex","flex-row","pb-2","pb-lg-0","px-3"],[1,"w-100"],[1,"w-25","d-flex","justify-content-end","align-items-center"],["mat-icon-button","","class","ml-auto mr-2","matTooltip","View Connections","style","\n                        height: 25px !important;\n                        line-height: 25px !important;\n                        width: 25px !important;\n                    ",3,"click",4,"ngIf"],["class","col-12 pl-5 pt-0",4,"ngIf"],["mat-icon-button","","matTooltip","View Connections",1,"ml-auto","mr-2",2,"height","25px !important","line-height","25px !important","width","25px !important",3,"click"],[2,"color","#66615b !important","font-size","21px !important"],[1,"col-12","pl-5","pt-0"],[1,"pt-1",2,"background-color","transparent !important","width","96% !important"],[1,"table","table-sm"],["scope","col"],[4,"ngFor","ngForOf"],[4,"ngIf"],[1,"pt-3","pb-2"],[1,"pt-2"],[1,"pt-2",3,"matTooltip"],["content-type","template","matTooltip","Active Status",3,"ngClass"],["diameter","20",1,"mt-1"]],template:function(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"div",0),s["\u0275\u0275elementStart"](1,"mat-card",1),s["\u0275\u0275elementStart"](2,"div",2),s["\u0275\u0275elementStart"](3,"div",3),s["\u0275\u0275elementStart"](4,"div",4),s["\u0275\u0275elementStart"](5,"div",5),s["\u0275\u0275text"](6),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](7,"div",6),s["\u0275\u0275listener"]("click",(function(){return t.onExpenseItemClick()})),s["\u0275\u0275elementStart"](8,"span",7),s["\u0275\u0275text"](9),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](10,"span",7),s["\u0275\u0275text"](11,"-"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](12,"span",7),s["\u0275\u0275text"](13),s["\u0275\u0275elementEnd"](),s["\u0275\u0275element"](14,"span",8),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](15,"div",9),s["\u0275\u0275listener"]("click",(function(){return t.onExpenseItemClick()})),s["\u0275\u0275elementStart"](16,"span",10),s["\u0275\u0275text"](17),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](18,"div",11),s["\u0275\u0275elementStart"](19,"div",12),s["\u0275\u0275elementStart"](20,"div",13),s["\u0275\u0275element"](21,"span",14),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](22,"div"),s["\u0275\u0275elementStart"](23,"p",15),s["\u0275\u0275text"](24,"Project Id"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](25,"p",16),s["\u0275\u0275text"](26),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](27,"div",17),s["\u0275\u0275elementStart"](28,"div"),s["\u0275\u0275elementStart"](29,"p",15),s["\u0275\u0275text"](30,"Project Name"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](31,"p",16),s["\u0275\u0275text"](32),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](33,"div",18),s["\u0275\u0275elementStart"](34,"div"),s["\u0275\u0275elementStart"](35,"p",15),s["\u0275\u0275text"](36,"Project Description"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](37,"p",16),s["\u0275\u0275text"](38),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](39,"div",18),s["\u0275\u0275elementStart"](40,"div"),s["\u0275\u0275elementStart"](41,"p",15),s["\u0275\u0275text"](42,"Created By"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](43,"div",19),s["\u0275\u0275element"](44,"app-user-profile",20),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](45,"div",21),s["\u0275\u0275elementStart"](46,"div",22),s["\u0275\u0275elementStart"](47,"p",15),s["\u0275\u0275text"](48,"Created on"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](49,"div",19),s["\u0275\u0275elementStart"](50,"p",16),s["\u0275\u0275text"](51),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](52,"div",23),s["\u0275\u0275template"](53,Ve,3,1,"button",24),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](54,Je,25,3,"div",25),s["\u0275\u0275elementEnd"]()),2&e&&(s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("@fadeInOut",void 0),s["\u0275\u0275advance"](4),s["\u0275\u0275property"]("ngClass",s["\u0275\u0275pureFunction2"](18,Ue,"Inactive"==t.expenseItem.status,"Active"==t.expenseItem.status)),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate1"](" ",t.expenseItem.status," "),s["\u0275\u0275advance"](3),s["\u0275\u0275textInterpolate"](t.expenseItem.project_id),s["\u0275\u0275advance"](4),s["\u0275\u0275textInterpolate"](t.expenseItem.project_name),s["\u0275\u0275advance"](3),s["\u0275\u0275propertyInterpolate"]("matTooltip",t.expenseItem.project_description),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate1"](" ",t.expenseItem.project_description," "),s["\u0275\u0275advance"](8),s["\u0275\u0275property"]("matTooltip",t.expenseItem.project_id),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate1"](" # ",t.expenseItem.project_id," "),s["\u0275\u0275advance"](5),s["\u0275\u0275property"]("matTooltip",t.expenseItem.project_name),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate1"](" ",t.expenseItem.project_name," "),s["\u0275\u0275advance"](5),s["\u0275\u0275property"]("matTooltip",t.expenseItem.project_description),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate1"](" ",t.expenseItem.project_description," "),s["\u0275\u0275advance"](6),s["\u0275\u0275property"]("oid",t.expenseItem.created_by),s["\u0275\u0275advance"](6),s["\u0275\u0275property"]("matTooltip",t.expenseItem.created_on),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate1"](" ",t.expenseItem.created_on," "),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("ngIf","Draft"!=t.expenseItem.status),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",null!=t.itemDetails))},directives:[$e.a,i.NgClass,v.a,B.a,i.NgIf,b.a,_.a,i.NgForOf,P.c],styles:['.il-body-styles[_ngcontent-%COMP%]   .expenses-card[_ngcontent-%COMP%]:hover{box-shadow:0 5px 8px 0 rgba(0,0,0,.1),0 4px 5px -1px rgba(0,0,0,.1);cursor:pointer;transition:box-shadow .3s}.il-body-styles[_ngcontent-%COMP%]   .expenses-card[_ngcontent-%COMP%]:hover   .expense-item-button[_ngcontent-%COMP%]{visibility:visible;animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}.il-body-styles[_ngcontent-%COMP%]   .expenses-card[_ngcontent-%COMP%]   .col[_ngcontent-%COMP%], .il-body-styles[_ngcontent-%COMP%]   .expenses-card[_ngcontent-%COMP%]   .col-auto[_ngcontent-%COMP%], .il-body-styles[_ngcontent-%COMP%]   .expenses-card[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]{padding:0!important;margin:0!important}.il-body-styles[_ngcontent-%COMP%]   .expenses-card[_ngcontent-%COMP%]   .third-row[_ngcontent-%COMP%]{padding-top:1px!important;padding-bottom:2px!important;background-color:#f1f2f6}.il-body-styles[_ngcontent-%COMP%]   .button-col[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{border-right:2px solid #c1c1c1;padding:4px!important}.il-body-styles[_ngcontent-%COMP%]   .expenses-status[_ngcontent-%COMP%]{border-radius:0 5px 5px 0;box-shadow:0 2px 1px 0 rgba(0,0,0,.2),0 1px 1px 0 rgba(0,0,0,.19);font-size:13px!important}.il-body-styles[_ngcontent-%COMP%]   .expenses-status[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{padding-top:3px;padding-bottom:3px;text-align:center}.il-body-styles[_ngcontent-%COMP%]   .expenses-name[_ngcontent-%COMP%]{color:#cf000f!important;font-size:15px!important}.il-body-styles[_ngcontent-%COMP%]   .customer-name[_ngcontent-%COMP%]{color:#1c1a1a!important;font-size:14px!important;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.il-body-styles[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{color:#5f5f5f;font-size:11px!important}.il-body-styles[_ngcontent-%COMP%]   .data-label[_ngcontent-%COMP%]{font-size:13px!important;white-space:nowrap;max-width:125px;overflow:hidden;text-overflow:ellipsis}.il-body-styles[_ngcontent-%COMP%]   .initName[_ngcontent-%COMP%]{align-self:center;padding-left:5px}.il-body-styles[_ngcontent-%COMP%]   .milestone[_ngcontent-%COMP%]{font-size:12px}.il-body-styles[_ngcontent-%COMP%]   .milestone[_ngcontent-%COMP%]   .milestone-name[_ngcontent-%COMP%]{color:#cf000f!important;font-size:14px!important}.il-body-styles[_ngcontent-%COMP%]   .milestone[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:18px!important;vertical-align:middle}.il-body-styles[_ngcontent-%COMP%]   .miles-col[_ngcontent-%COMP%]{min-height:40vh;max-height:40vh;overflow:scroll}.il-body-styles[_ngcontent-%COMP%]   .miles-col[_ngcontent-%COMP%]   mat-card[_ngcontent-%COMP%]{min-height:40vh}.il-body-styles[_ngcontent-%COMP%]   .miles-col[_ngcontent-%COMP%]   .mat-card-subtitle[_ngcontent-%COMP%]{margin-bottom:4px!important}.il-body-styles[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]{height:28px;line-height:28px}.il-body-styles[_ngcontent-%COMP%]   .expenses-code[_ngcontent-%COMP%]{font-size:14px;font-weight:420}.il-body-styles[_ngcontent-%COMP%]   .expenses-card[_ngcontent-%COMP%]   [_ngcontent-%COMP%]:hover{cursor:pointer}.il-body-styles[_ngcontent-%COMP%]   .chat-button[_ngcontent-%COMP%]{width:32px!important;height:32px!important;line-height:32px!important;position:absolute;right:5px;top:4px}@media (min-width:992px){.il-body-styles[_ngcontent-%COMP%]   .col-2x[_ngcontent-%COMP%]{flex:0 0 13.666667%;max-width:13.666667%}.il-body-styles[_ngcontent-%COMP%]   .col-1x[_ngcontent-%COMP%]{flex:0 0 12.333333%;max-width:12.333333%}.il-body-styles[_ngcontent-%COMP%]   .col-3x[_ngcontent-%COMP%]{flex:0 0 20.666667%;max-width:20.666667%}.il-body-styles[_ngcontent-%COMP%]   .third-row[_ngcontent-%COMP%]   .col-1x[_ngcontent-%COMP%]:not(:last-child), .il-body-styles[_ngcontent-%COMP%]   .third-row[_ngcontent-%COMP%]   .col-2x[_ngcontent-%COMP%]:not(:last-child), .il-body-styles[_ngcontent-%COMP%]   .third-row[_ngcontent-%COMP%]   .col-3x[_ngcontent-%COMP%]:not(:last-child){border-right:2px solid #c1c1c1}.il-body-styles[_ngcontent-%COMP%]   .third-row[_ngcontent-%COMP%]   .col-1x[_ngcontent-%COMP%]:not(:first-child)   mat-icon[_ngcontent-%COMP%], .il-body-styles[_ngcontent-%COMP%]   .third-row[_ngcontent-%COMP%]   .col-2x[_ngcontent-%COMP%]:not(:first-child)   mat-icon[_ngcontent-%COMP%], .il-body-styles[_ngcontent-%COMP%]   .third-row[_ngcontent-%COMP%]   .col-3x[_ngcontent-%COMP%]:not(:first-child)   mat-icon[_ngcontent-%COMP%]{font-size:22px!important;color:#474444!important}}.il-body-styles[_ngcontent-%COMP%]   .data-label-partners[_ngcontent-%COMP%]{font-size:13px!important;font-weight:420!important;color:#1c1a1a!important;margin-bottom:3px!important}.il-body-styles[_ngcontent-%COMP%]   .header-row[_ngcontent-%COMP%]{justify-content:space-evenly;color:#9a9a9a}.il-body-styles[_ngcontent-%COMP%]   .header-row[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{color:#252422;font-weight:400}.il-body-styles[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{font-size:12px;color:#746f6f!important;font-weight:400}.il-body-styles[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{font-size:12px}.il-body-styles[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%], .il-body-styles[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{border-top:none!important;text-align:center}.il-body-styles[_ngcontent-%COMP%]   .text-overflow[_ngcontent-%COMP%]{width:calc(100% - 20px)!important}.il-body-styles[_ngcontent-%COMP%]   .text-overflow[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .il-body-styles[_ngcontent-%COMP%]   .text-overflow[_ngcontent-%COMP%]   text[_ngcontent-%COMP%]{white-space:nowrap;width:95%;overflow:hidden;text-overflow:ellipsis}.il-body-styles[_ngcontent-%COMP%]   .location-overflow[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;font-size:14px!important}.il-body-styles[_ngcontent-%COMP%]   td[_ngcontent-%COMP%], .il-body-styles[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{text-align:left!important}.il-body-styles[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:last-child, .il-body-styles[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:last-child{padding-left:8px!important}.il-body-styles[_ngcontent-%COMP%]   .expense-item-button[_ngcontent-%COMP%]{height:26px!important;width:26px!important;line-height:26px;visibility:hidden}.il-body-styles[_ngcontent-%COMP%]   .expense-item-button[_ngcontent-%COMP%]   .expense-item-button-icon[_ngcontent-%COMP%]{color:#66615b!important;font-size:19px!important}.il-body-styles[_ngcontent-%COMP%]   .expense-item-table-button[_ngcontent-%COMP%]{height:28px!important;width:28px!important;line-height:28px}.il-body-styles[_ngcontent-%COMP%]   .expense-item-table-button[_ngcontent-%COMP%]   .expense-item-table-icon[_ngcontent-%COMP%]{color:#66615b!important;font-size:18px!important}@keyframes slide-top{0%{transform:translateY(10px)}to{transform:translateY(0)}}.il-body-styles[_ngcontent-%COMP%]   .status-text-Verified[_ngcontent-%COMP%]{color:#9f2825}.il-body-styles[_ngcontent-%COMP%]   .status-text-Active[_ngcontent-%COMP%]{color:#009432}.il-body-styles[_ngcontent-%COMP%]   .status-text-Inactive[_ngcontent-%COMP%]{color:#cf0001}.il-body-styles[_ngcontent-%COMP%]   .is-draft[_ngcontent-%COMP%]{background:#a9a9a9;color:#fff!important}.il-body-styles[_ngcontent-%COMP%]   .is-submitted[_ngcontent-%COMP%]{background:#ffa502;color:#fff!important}.il-body-styles[_ngcontent-%COMP%]   .is-approved[_ngcontent-%COMP%]{background:#badc58;color:#fff!important}.il-body-styles[_ngcontent-%COMP%]   .is-verified[_ngcontent-%COMP%]{background:#9f2825;color:#fff!important}.il-body-styles[_ngcontent-%COMP%]   .is-rejected[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff}.il-body-styles[_ngcontent-%COMP%]   .is-payed[_ngcontent-%COMP%]{background:#079992;color:#fff!important}.il-body-styles[_ngcontent-%COMP%]   .is-ko-submitted[_ngcontent-%COMP%]{background:#e58e26;color:#fff!important}.il-body-styles[_ngcontent-%COMP%]   .is-ko-approved[_ngcontent-%COMP%]{background:#5ce88c;color:#fff!important}.il-body-styles[_ngcontent-%COMP%]   .is-closed[_ngcontent-%COMP%]{background:#009432;color:#fff!important}.il-body-styles[_ngcontent-%COMP%]   .tooltip-text[_ngcontent-%COMP%]{font-size:12px;font-family:Roboto,Helvetica Neue,"sans-serif"!important}.il-body-styles[_ngcontent-%COMP%]   .tooltip-status-indicator[_ngcontent-%COMP%]{font-size:12px;margin-top:9px}'],data:{animation:[Object(l.o)("slideInOut",[Object(l.l)("in",Object(l.m)({height:"*",overflow:"hidden"})),Object(l.l)("out",Object(l.m)({height:0,overflow:"hidden"})),Object(l.n)("* => in",[Object(l.m)({height:0}),Object(l.e)(120,Object(l.m)({height:"*"}))]),Object(l.n)("in=> *",[Object(l.m)({height:"*"}),Object(l.e)(120,Object(l.m)({height:0}))])]),Object(l.o)("fadeInOut",[Object(l.l)("void",Object(l.m)({opacity:0})),Object(l.n)("void <=> *",Object(l.e)(400))])]}}),e})();function Ke(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"button",21),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"]().clearSearch()})),s["\u0275\u0275elementStart"](1,"mat-icon",22),s["\u0275\u0275text"](2,"close "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}}function Qe(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"button",23),s["\u0275\u0275elementStart"](1,"mat-icon",13),s["\u0275\u0275text"](2,"settings"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e){s["\u0275\u0275nextContext"]();const e=s["\u0275\u0275reference"](22);s["\u0275\u0275property"]("matMenuTriggerFor",e)}}function Ge(e,t){1&e&&s["\u0275\u0275element"](0,"app-project-card",28),2&e&&s["\u0275\u0275property"]("expenseItem",t.$implicit)}function Ze(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"div",26),s["\u0275\u0275listener"]("scrolled",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"](2).onScrollDown()})),s["\u0275\u0275template"](1,Ge,1,1,"app-project-card",27),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275nextContext"](2);s["\u0275\u0275property"]("infiniteScrollDistance",2)("scrollWindow",!1),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngForOf",e.expenseList)("ngForTrackBy",e.trackByIdx)}}function We(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"div",29),s["\u0275\u0275elementStart"](1,"div"),s["\u0275\u0275elementStart"](2,"h4",30),s["\u0275\u0275text"](3," No Data found ! "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](4,"div",31),s["\u0275\u0275element"](5,"img",32),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]())}function Xe(e,t){if(1&e&&(s["\u0275\u0275elementContainerStart"](0),s["\u0275\u0275template"](1,Ze,2,4,"div",24),s["\u0275\u0275template"](2,We,6,0,"div",25),s["\u0275\u0275elementContainerEnd"]()),2&e){const e=s["\u0275\u0275nextContext"]();s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",e.expenseList.length>0&&"Invalid"!=e.expenseList),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf","Invalid"==e.expenseList&&0==e.isLoading)}}function et(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"div",33),s["\u0275\u0275element"](1,"div",34),s["\u0275\u0275elementStart"](2,"div",35),s["\u0275\u0275element"](3,"mat-spinner",36),s["\u0275\u0275elementEnd"](),s["\u0275\u0275element"](4,"div",34),s["\u0275\u0275elementEnd"]())}let tt=(()=>{class e{constructor(e,t,n,i){this.router=e,this.dialog=t,this.api=n,this._liveAnnouncer=i,this.displayedColumns=["project_id","project_name","project_description","created_by","created_on","created_at","edit_project","status"],this.searchString="",this.lastPosition=1,this.isLoading=!0,this.$onDestroy=new u.b,this.$onAppApiCalled=new u.b,this.searchFieldValue="",this.searchText=new c.j,this.isAdmin=!1,this.tenant_config_array=[{is_admin:0,is_except:0}],this.onScrollDown=()=>{this.isLoading=!0,this.lastPosition=this.expenseList.length+1,console.log("On Scroll length : ",this.lastPosition),this.api.getProjectLength().pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{this.idCount=e.length}),this.api.getProjects(this.searchString,this.lastPosition).pipe(Object(h.a)(()=>{console.log("Current expenseList on Scroll : ",this.expenseList),this.isLoading=!1})).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{console.log("On Scroll response : ",e),"Invalid"!=e&&(this.expenseList.push(...e),this.lastPosition=this.expenseList.length+1)})},this.navigateToAdmin=()=>{this.router.navigateByUrl("main/ils/home/<USER>")},this.navigateToReport=()=>{this.router.navigateByUrl("main/ils/home/<USER>")}}ngOnInit(){this.isLoading=!0,this.api.getProjectLength().pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{this.idCount=e.length}),this.api.getProjects(this.searchString,this.lastPosition).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{if(this.expenseList=e,"Invalid"==e)console.log("No Data Found");else{const t=Object.keys(e[0]);t.push("edit_project"),this.displayedColumns=t,console.log("Project Keys : ",this.displayedColumns),console.log("Project List : ",e),this.dataSource=new p.l(e),this.lastPosition=Object.entries(this.expenseList).length+1,this.dataSource.paginator=this.paginator,this.dataSource.sort=this.sort}this.isLoading=!1}),this.api.getAdminAccess().pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{this.isAdmin=e.data}),this.api.getIlsTenantConfig().pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{if("S"==e.messType){let t=e.data;for(let e=0;e<t.length;e++){const n=t[e];this.tenant_config_array[0][n.function_name]=n.is_true}console.log("tenant Config",this.tenant_config_array)}})}ngOnDestroy(){this.$onDestroy.next(),this.$onDestroy.complete()}applyFilter(e){this.dataSource.filter=e.target.value.trim().toLowerCase(),this.dataSource.paginator&&this.dataSource.paginator.firstPage()}announceSortChange(e){this.isLoading=!0,e.direction?(this._liveAnnouncer.announce(`Sorted ${e.direction}ending`),this.isLoading=!1):(this._liveAnnouncer.announce("Sorting cleared"),this.isLoading=!1)}add_new_project(){this.dialog.open(Be,{height:"100%",width:"60%",position:{right:"0px"},data:{modalparams:this.idCount}}).afterClosed().subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){yield this.refresh()})))}RowClick(e){this.router.navigateByUrl("main/ils/home/<USER>/"+e.project_id)}clearSearch(){this.searchText.patchValue(""),this.searchString="",this.isLoading=!0,this.expenseList={},this.lastPosition=1,this.api.getProjectLength().pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{this.idCount=e.length}),this.api.getProjects(this.searchString,this.lastPosition).pipe(Object(h.a)(()=>{console.log("Current expenseList : ",this.expenseList),this.isLoading=!1})).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{this.expenseList=e,this.lastPosition="Invalid"!=e?this.expenseList.length+1:1})}setSearchText(e){return Object(r.c)(this,void 0,void 0,(function*(){this.searchText.patchValue(e.target.value),console.log("current searchText : ",this.searchText.value),this.searchString=this.searchText.value,yield this.searchFilter()}))}searchFilter(){return Object(r.c)(this,void 0,void 0,(function*(){this.isLoading=!0,this.expenseList={},this.lastPosition=0,this.api.getProjectLength().pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{this.idCount=e.length}),this.api.getProjects(this.searchString,this.lastPosition).pipe(Object(h.a)(()=>{console.log("Current expenseList : ",this.expenseList),this.isLoading=!1})).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{this.expenseList=e,this.lastPosition=Object.entries(this.expenseList).length+1})}))}refresh(){return Object(r.c)(this,void 0,void 0,(function*(){this.isLoading=!0,this.clearSearch(),this.expenseList={},this.lastPosition=1,this.api.getProjectLength().pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{this.idCount=e.length}),this.api.getProjects(this.searchString,this.lastPosition).pipe(Object(h.a)(()=>{console.log("Current expenseList : ",this.expenseList),this.isLoading=!1})).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{this.expenseList=e,this.lastPosition="Invalid"!=e?this.expenseList.length+1:1})}))}summaryCardVisibility(){console.log("Summary Under maintenance")}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275directiveInject"](o.g),s["\u0275\u0275directiveInject"](g.b),s["\u0275\u0275directiveInject"](y),s["\u0275\u0275directiveInject"](L.l))},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-project"]],viewQuery:function(e,t){if(1&e&&(s["\u0275\u0275viewQuery"](d.a,!0),s["\u0275\u0275viewQuery"](Te.a,!0)),2&e){let e;s["\u0275\u0275queryRefresh"](e=s["\u0275\u0275loadQuery"]())&&(t.paginator=e.first),s["\u0275\u0275queryRefresh"](e=s["\u0275\u0275loadQuery"]())&&(t.sort=e.first)}},decls:31,vars:7,consts:[[1,"expenses-styles"],[1,"row","pt-1"],[1,"col-md-3"],[1,"pl-3","pt-3","pb-3","col-md-12","col-3","title"],[1,"search-bar","d-flex","col-5","pt-1"],["appearance","outline",1,"ml-auto","mr-auto"],["matPrefix",""],[2,"font-size","21px !important","color","#66615b !important"],["matInput","","placeholder","Search Ils Projects",3,"formControl","keyup"],["matSuffix",""],["mat-button","","matSuffix","","mat-icon-button","","aria-label","Clear","style","height: 30px; width: 30px; line-height: 1;",3,"click",4,"ngIf"],[1,"col-3","px-0","pt-2","pl-2"],["mat-icon-button","","matTooltip","Refresh",1,"trend-button-inactive",3,"click"],[1,"iconButton"],["mat-icon-button","","matTooltip","Add Project",1,"trend-button-inactive",3,"click"],["mat-icon-button","","matTooltip","settings","class","trend-button-inactive",3,"matMenuTriggerFor",4,"ngIf"],["menu","matMenu"],[3,"hidden"],["mat-menu-item","",3,"click"],[4,"ngIf"],["class","container d-flex h-100 flex-column",4,"ngIf"],["mat-button","","matSuffix","","mat-icon-button","","aria-label","Clear",2,"height","30px","width","30px","line-height","1",3,"click"],["matTooltip","Clear Search",2,"font-size","18px !important","color","#66615b !important"],["mat-icon-button","","matTooltip","settings",1,"trend-button-inactive",3,"matMenuTriggerFor"],["class","infinite-scroll-auto","infinite-scroll","",3,"infiniteScrollDistance","scrollWindow","scrolled",4,"ngIf"],["style","text-align: center;",4,"ngIf"],["infinite-scroll","",1,"infinite-scroll-auto",3,"infiniteScrollDistance","scrollWindow","scrolled"],[3,"expenseItem",4,"ngFor","ngForOf","ngForTrackBy"],[3,"expenseItem"],[2,"text-align","center"],[1,"d-flex","justify-content-center","align-items-center","mt-5","slide-in-top"],[1,"d-flex","justify-content-center","align-items-center","slide-from-down"],["src","https://assets.kebs.app/images/nomilestone.png","height","240","width","340",1,"mt-4","mb-3"],[1,"container","d-flex","h-100","flex-column"],[1,"flex-grow-1"],[1,"row","justify-content-center"],["diameter","30"]],template:function(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"div",0),s["\u0275\u0275elementStart"](1,"div",1),s["\u0275\u0275elementStart"](2,"div",2),s["\u0275\u0275elementStart"](3,"div",3),s["\u0275\u0275element"](4,"span"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](5,"div",4),s["\u0275\u0275elementStart"](6,"mat-form-field",5),s["\u0275\u0275elementStart"](7,"span",6),s["\u0275\u0275elementStart"](8,"mat-icon",7),s["\u0275\u0275text"](9,"search"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](10,"input",8),s["\u0275\u0275listener"]("keyup",(function(e){return t.setSearchText(e)})),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](11,"mat-icon",9),s["\u0275\u0275template"](12,Ke,3,0,"button",10),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](13,"div",11),s["\u0275\u0275elementStart"](14,"button",12),s["\u0275\u0275listener"]("click",(function(){return t.refresh()})),s["\u0275\u0275elementStart"](15,"mat-icon",13),s["\u0275\u0275text"](16,"loop"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](17,"button",14),s["\u0275\u0275listener"]("click",(function(){return t.add_new_project()})),s["\u0275\u0275elementStart"](18,"mat-icon",13),s["\u0275\u0275text"](19,"add_circle_outline"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](20,Qe,3,1,"button",15),s["\u0275\u0275elementStart"](21,"mat-menu",null,16),s["\u0275\u0275elementStart"](23,"div",17),s["\u0275\u0275elementStart"](24,"button",18),s["\u0275\u0275listener"]("click",(function(){return t.navigateToAdmin()})),s["\u0275\u0275text"](25,"Admin Functions"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](26,"div",17),s["\u0275\u0275elementStart"](27,"button",18),s["\u0275\u0275listener"]("click",(function(){return t.navigateToReport()})),s["\u0275\u0275text"](28,"Exception Report"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](29,Xe,3,2,"ng-container",19),s["\u0275\u0275template"](30,et,5,0,"div",20),s["\u0275\u0275elementEnd"]()),2&e&&(s["\u0275\u0275advance"](10),s["\u0275\u0275property"]("formControl",t.searchText),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("ngIf",null!=t.searchText.value&&""!=t.searchText.value),s["\u0275\u0275advance"](8),s["\u0275\u0275property"]("ngIf",t.isAdmin),s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("hidden",!t.tenant_config_array[0].is_admin),s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("hidden",!t.tenant_config_array[0].is_except),s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("ngIf",t.expenseList),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",t.isLoading))},directives:[C.c,C.h,_.a,x.b,c.e,c.v,c.k,C.i,i.NgIf,b.a,v.a,qe.g,qe.d,qe.f,q.a,i.NgForOf,Ye,P.c],styles:[".il-body-styles[_ngcontent-%COMP%]   table[_ngcontent-%COMP%]{width:100%}.il-body-styles[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]{margin-top:10px}.il-body-styles[_ngcontent-%COMP%]   .filter[_ngcontent-%COMP%]{padding-bottom:20px}.il-body-styles[_ngcontent-%COMP%]   table[_ngcontent-%COMP%], .il-body-styles[_ngcontent-%COMP%]   td[_ngcontent-%COMP%], .il-body-styles[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{border-collapse:collapse}.il-body-styles[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{background-color:#d5acac;color:#181a19;font-weight:700}.il-body-styles[_ngcontent-%COMP%]   .mat-row[_ngcontent-%COMP%]   .mat-cell[_ngcontent-%COMP%]{border-bottom:1px solid transparent;border-top:1px solid transparent}.il-body-styles[_ngcontent-%COMP%]   .mat-row[_ngcontent-%COMP%]:hover   .mat-cell[_ngcontent-%COMP%]{border-color:currentColor;background-color:#f6eee3}.il-body-styles[_ngcontent-%COMP%]   .demo-row-is-clicked[_ngcontent-%COMP%]{font-weight:700}.il-body-styles[_ngcontent-%COMP%]   .bottom_btns[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;align-content:stretch;justify-content:space-between;align-items:flex-end}.expenses-styles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff;font-weight:400;font-size:13px!important;min-width:60px;line-height:30px;padding:0 25px;border-radius:0!important}.expenses-styles[_ngcontent-%COMP%]   .infinite-scroll-auto[_ngcontent-%COMP%]{height:523px;overflow:scroll}.expenses-styles[_ngcontent-%COMP%]   .trend-button-inactive[_ngcontent-%COMP%]{line-height:8px;width:26px;height:26px;margin-right:12px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.expenses-styles[_ngcontent-%COMP%]   .trend-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.expenses-styles[_ngcontent-%COMP%]   .trend-button-inactive[_ngcontent-%COMP%]   .iconButtonWarn[_ngcontent-%COMP%]{color:#cf0001;font-size:18px}.expenses-styles[_ngcontent-%COMP%]   .trend-button-inactive[_ngcontent-%COMP%]   .iconButtonWarnAnim[_ngcontent-%COMP%]{color:#cf0001;font-size:18px;animation:blink 1.1s cubic-bezier(.5,0,1,1) infinite alternate}.expenses-styles[_ngcontent-%COMP%]   .trend-button-active[_ngcontent-%COMP%]{background-color:#cf0001;line-height:8px;width:26px;height:26px;margin-right:12px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.expenses-styles[_ngcontent-%COMP%]   .trend-button-active[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#fff;font-size:18px}.expenses-styles[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field{width:40vw}.expenses-styles[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-infix{font-size:14px!important;padding:.5em 0!important;border-top:.54375em solid transparent!important}.expenses-styles[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{color:#66615b;font-size:14px!important;font-weight:400}.expenses-styles[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%], .expenses-styles[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.expenses-styles[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]{color:#1a1a1a;font-size:14px;font-weight:500}.expenses-styles[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#3c3c3c;font-weight:400;font-size:18px}.expenses-styles[_ngcontent-%COMP%]   .headingBold[_ngcontent-%COMP%]{color:#1a1a1a;font-weight:600;font-size:18px}.expenses-styles[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{color:#181818;font-size:14px;font-weight:400;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.expenses-styles[_ngcontent-%COMP%]   .col-left-border[_ngcontent-%COMP%]{border-right:1px solid #ddd}.expenses-styles[_ngcontent-%COMP%]   .card-summary-bg[_ngcontent-%COMP%]{background-image:url(expense_bg.64ac4aa5cba931ab8a07.png);background-size:237px 166px;background-repeat:no-repeat;overflow-y:hidden!important;background-position:96% 100%}.expenses-styles[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}.expenses-styles[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}@keyframes blink{0%{opacity:1}to{opacity:0}}"],data:{animation:[Object(l.o)("slideInOut",[Object(l.l)("in",Object(l.m)({height:"*",overflow:"hidden"})),Object(l.l)("out",Object(l.m)({height:0,overflow:"hidden"})),Object(l.n)("* => in",[Object(l.m)({height:0}),Object(l.e)(250,Object(l.m)({height:"*"}))]),Object(l.n)("in=> *",[Object(l.m)({height:"*"}),Object(l.e)(250,Object(l.m)({height:0}))])]),Object(l.o)("smallCardAnimation",[Object(l.n)("* => *",[Object(l.i)(":leave",[Object(l.k)(100,[Object(l.e)("0.5s",Object(l.m)({opacity:0}))])],{optional:!0}),Object(l.i)(":enter",[Object(l.m)({opacity:0}),Object(l.k)(100,[Object(l.e)("0.5s",Object(l.m)({opacity:1}))])],{optional:!0})])])]}}),e})();function nt(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"mat-option",35),s["\u0275\u0275text"](1),s["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;s["\u0275\u0275property"]("value",e),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate1"](" ",e," ")}}const it=function(e){return{backgroundColor:e}};function ot(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"div",24),s["\u0275\u0275elementStart"](1,"div",25),s["\u0275\u0275element"](2,"app-input-search",26),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](3,"div",27),s["\u0275\u0275elementStart"](4,"mat-form-field",28),s["\u0275\u0275elementStart"](5,"mat-select",29),s["\u0275\u0275template"](6,nt,2,2,"mat-option",30),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](7,"div",31),s["\u0275\u0275elementStart"](8,"mat-form-field",32),s["\u0275\u0275element"](9,"input",33),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](10,"button",34),s["\u0275\u0275listener"]("click",(function(){s["\u0275\u0275restoreView"](e);const n=t.index;return s["\u0275\u0275nextContext"](2).clearField(n)})),s["\u0275\u0275text"](11,"-"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}if(2&e){const e=t.index,n=s["\u0275\u0275nextContext"](2);s["\u0275\u0275property"]("formGroupName",e),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("list",n.src_list_data)("disableNone",!0),s["\u0275\u0275advance"](4),s["\u0275\u0275property"]("ngForOf",n.destination_field_type[e]),s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("value",n.destination_field[e]),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngStyle",s["\u0275\u0275pureFunction1"](6,it,n.buttonColor[e]?"red":"white"))}}function st(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"div",14),s["\u0275\u0275elementStart"](1,"form",15),s["\u0275\u0275elementStart"](2,"div",16),s["\u0275\u0275elementStart"](3,"div",17),s["\u0275\u0275elementStart"](4,"div",18),s["\u0275\u0275elementStart"](5,"h1",19),s["\u0275\u0275text"](6,"Source Fields"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275element"](7,"h1",20),s["\u0275\u0275elementStart"](8,"h1",21),s["\u0275\u0275text"](9,"Destination Fields"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](10,"div",22),s["\u0275\u0275elementStart"](11,"div",18),s["\u0275\u0275template"](12,ot,12,8,"div",23),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e){const e=s["\u0275\u0275nextContext"]();s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("formGroup",e.sourceFieldMapping),s["\u0275\u0275advance"](11),s["\u0275\u0275property"]("ngForOf",e.sourceGetControls())}}function at(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"button",36),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"]().bindField()})),s["\u0275\u0275text"](1,"Type Conversion"),s["\u0275\u0275elementEnd"]()}}function rt(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"button",36),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"]().submitData()})),s["\u0275\u0275text"](1,"Submit"),s["\u0275\u0275elementEnd"]()}}let lt=(()=>{class e{constructor(e,t,n,i,o,s,a){this.dialogRef=e,this.data=t,this.fb=n,this.router=i,this.dialog=o,this.api=s,this._liveAnnouncer=a,this.$onDestroy=new u.b,this.$onAppApiCalled=new u.b,this.destination_field_type=[],this.bindSourceData=!1,this.FieldBinded=!1,this.finalBinding=[],this.finalResponse=[],this.flagConversionError=!1,this.submitFlag=!1,this.src_list_data=[],this.BindButtonFlag=!1,this.color=[],this.buttonColor=[],this.sourceFieldMapping=new c.m({itemRows:new c.g([new c.m({src_field:new c.j(null),dest_type_field:new c.j(null,c.H.required)})])}),this.src_data={}}ngOnInit(){this.sourceData=this.data.Response,this.sourceURL=this.data.sourceURL,this.source_field=this.data.source_key;for(let e=0;e<this.source_field.length;e++)this.src_data={id:this.source_field[e],name:this.source_field[e]},this.src_list_data.push(this.src_data);this.clicked_source_id=this.data.clicked_source_id,console.log("Source Data : ",this.sourceData," source URL : ",this.sourceURL,"source keys : ",this.source_field,"clicked_source_id : ",this.clicked_source_id),this.getSource()}ngOnDestroy(){this.$onDestroy.next(),this.$onDestroy.complete()}getSource(){return Object(r.c)(this,void 0,void 0,(function*(){return this.sourceData=this.data.Response,this.sourceURL=this.data.sourceURL,this.source_field=this.data.source_key,this.clicked_source_id=this.data.clicked_source_id,this.bindSourceData=!1,yield this.getDestinationData(),!0}))}restSource(){return Object(r.c)(this,void 0,void 0,(function*(){return this.sourceData=this.data.Response,this.sourceURL=this.data.sourceURL,this.source_field=this.data.source_key,this.clicked_source_id=this.data.clicked_source_id,this.bindSourceData=!1,!0}))}checkFieldMappingData(){return Object(r.c)(this,void 0,void 0,(function*(){this.api.getConnectionItem(this.clicked_source_id).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{let t=e[0].field_mapping;if(null!=t){this.finalBinding=JSON.parse(t);const e=this.sourceFieldMapping.get("itemRows");console.log("Saved field binding : ",this.finalBinding);for(let t of this.finalBinding)e.at(t.row_index).patchValue({src_field:t.src_field}),e.at(t.row_index).patchValue({dest_type_field:t.type_cast}),this.color[t.row_index]=t.color}console.log("CONTROL ARRAY  :  ",this.sourceFieldMapping)})}))}getDestinationData(){return Object(r.c)(this,void 0,void 0,(function*(){this.destination_field=[],this.api.getDestinationFields({source_url:this.sourceURL}).pipe(Object(h.a)(()=>Object(r.c)(this,void 0,void 0,(function*(){yield this.checkFieldMappingData()})))).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{var t,n;return Object(r.c)(this,void 0,void 0,(function*(){var i,o;let s=[];this.destination_field_type=[],console.log("Destination API response : ",e);try{for(t=Object(r.b)(e);!(n=yield t.next()).done;){let e=n.value;s.push(e.field),this.destination_field_type.push(["none",e.type])}}catch(l){i={error:l}}finally{try{n&&!n.done&&(o=t.return)&&(yield o.call(t))}finally{if(i)throw i.error}}this.destination_field=s,console.log("Destination Data : ",this.destination_field),console.log("Destination Data Type : ",this.destination_field_type),this.BindButtonFlag=!0,console.log("Bind Data : ","working"),console.log("Destination field length : ",this.destination_field.length);const a=this.sourceFieldMapping.controls.itemRows;a.clear(),this.color=[],this.buttonColor=[];for(let e=0;e<this.destination_field.length;e++)this.color.push(!1),this.buttonColor.push(!1),a.push(new c.m({src_field:new c.j(null),dest_type_field:new c.j(this.destination_field_type[e][1],c.H.required)}))}))})}))}clearField(e){this.sourceFieldMapping.controls.itemRows.at(e).reset(),this.buttonColor[e]=!1,this.color[e]=!1}sourceGetControls(){return this.sourceFieldMapping.get("itemRows").controls}get BindControl(){return this.sourceFieldMapping.controls}bindField(){this.finalBinding=[],this.FieldBinded=!0;let e={src_field:"",dest_field:"",type_cast:"",row_index:0,color:!1,error_flag:!1};for(let t=0;t<this.destination_field.length;t++)e.src_field=this.sourceFieldMapping.controls.itemRows.value[t].src_field,e.dest_field=this.destination_field[t],e.color=this.color[t],e.type_cast=this.sourceFieldMapping.controls.itemRows.value[t].dest_type_field,e.row_index=t,null!=e.src_field&&this.finalBinding.push(e),e=e={src_field:"",dest_field:"",type_cast:"",row_index:0,color:!1,error_flag:!1};console.log("Final Binding : ",this.finalBinding),this.typeConversion(this.finalBinding)}typeConversion(e){for(let t in e){switch(console.log("Index : ",t," Type : ",e[t].type_cast),e[t].type_cast){case"varchar":case"char":this.convertTovarchar(e[t].src_field);break;case"int":this.convertToint(e[t].src_field);break;case"json":this.convertTojson(e[t].src_field);break;case"date":this.convertTodate(e[t].src_field);break;case"datetime":this.convertTodatetime(e[t].src_field)}this.flagConversionError&&(e[t].error_flag=!0,this.buttonColor[e[t].row_index]=!e[t].error_flag||"none"!==e[t].type_cast)}for(let t of e)if(t.error_flag&&"none"===t.type_cast)t.error_flag=!1,t.color=!1,this.flagConversionError=!1;else{if(t.error_flag){this.flagConversionError=!0;break}this.flagConversionError=!1}if(console.log("this flag error ",this.flagConversionError),this.flagConversionError)this.submitFlag=!1,this.finalBinding=[],this.restSource();else if(!this.flagConversionError){this.finalResponse=[];for(let[t,n]of Object.entries(this.sourceData)){let t={};for(let i in e)t[e[i].dest_field]=n[e[i].src_field];this.finalResponse.push(t)}console.log("Final Response : ",this.finalResponse),this.submitFlag=!0}console.log("this submit status ",this.submitFlag)}convertTovarchar(e){return Object(r.c)(this,void 0,void 0,(function*(){this.flagConversionError=!1;for(let n in this.sourceData)try{if("string"==typeof this.sourceData[n][e])break;this.sourceData[n][e]="object"==typeof this.sourceData[n][e]?JSON.stringify(this.sourceData[n][e]):this.sourceData[n][e].toString()}catch(t){this.flagConversionError=!0,alert(e+" cannot be converted into String");break}console.log("After Type Convertion to VarChar: ",this.sourceData)}))}convertToint(e){return Object(r.c)(this,void 0,void 0,(function*(){this.flagConversionError=!1;for(let n in this.sourceData)try{if(null===this.sourceData[n][e])continue;if("number"==typeof this.sourceData[n][e])break;if(this.sourceData[n][e]=Number(this.sourceData[n][e]),isNaN(this.sourceData[n][e])){this.flagConversionError=!0,alert(e+" cannot be converted into Integer");break}}catch(t){this.flagConversionError=!0,alert(e+" cannot be converted into Integer");break}console.log("After Type Convertion to Int: ",this.sourceData)}))}convertTojson(e){return Object(r.c)(this,void 0,void 0,(function*(){this.flagConversionError=!1;for(let n in this.sourceData)try{if("object"==typeof this.sourceData[n][e])break;if(this.sourceData[n][e]=JSON.parse(this.sourceData[n][e]),"object"!=typeof this.sourceData[n][e]){this.flagConversionError=!0,alert(e+" cannot be converted into JSON");break}}catch(t){this.flagConversionError=!0,alert(e+" cannot be converted into JSON");break}console.log("After Type Convertion to JSON: ",this.sourceData)}))}convertTodate(e){return Object(r.c)(this,void 0,void 0,(function*(){this.flagConversionError=!1;for(let n in this.sourceData)try{if(null===this.sourceData[n][e])continue;if(void 0===this.sourceData[n][e]){this.sourceData[n][e]=Le.utc(null).format("YYYY-MM-DD");continue}if("Invalid date"===this.sourceData[n][e]){this.sourceData[n][e]=Le.utc(null).format("YYYY-MM-DD");continue}let t="value "+this.sourceData[n][e];if(this.sourceData[n][e]=Le.utc(this.sourceData[n][e]).format("YYYY-MM-DD"),"Invalid date"===this.sourceData[n][e]){this.flagConversionError=!0,alert(`${e} cannot be converted into date ${this.sourceData[n][e]}, with \n          ${t}`);break}}catch(t){this.flagConversionError=!0,alert(`${e} cannot be converted into date ${this.sourceData[n][e]}`);break}console.log("After Type Convertion to date: ",this.sourceData)}))}convertTodatetime(e){return Object(r.c)(this,void 0,void 0,(function*(){this.flagConversionError=!1;for(let n in this.sourceData)try{if(null===this.sourceData[n][e])continue;if(void 0===this.sourceData[n][e]){this.sourceData[n][e]=Le.utc(null).format("YYYY-MM-DD HH:mm:ss");continue}if("Invalid date"===this.sourceData[n][e]){this.sourceData[n][e]=Le.utc(null).format("YYYY-MM-DD HH:mm:ss");continue}let t="value "+this.sourceData[n][e];if(this.sourceData[n][e]=Le.utc(this.sourceData[n][e]).format("YYYY-MM-DD HH:mm:ss"),"Invalid date"===this.sourceData[n][e]){this.flagConversionError=!0,alert(`${e} cannot be converted into dateTime ${this.sourceData[n][e]}, with \n          ${t}`);break}}catch(t){this.flagConversionError=!0,alert(`${e} cannot be converted into dateTime ${this.sourceData[n][e]}`);break}console.log("After Type Convertion to dateTime: ",this.sourceData)}))}submitData(){this.api.postConnectionItems({source_id:this.clicked_source_id,field_mapping:this.finalBinding}).subscribe(e=>{this.dialogRef.close({data:{flag:!0,result:this.finalResponse}})})}close_FieldMapping(){this.dialogRef.close()}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275directiveInject"](g.h),s["\u0275\u0275directiveInject"](g.a),s["\u0275\u0275directiveInject"](c.i),s["\u0275\u0275directiveInject"](o.g),s["\u0275\u0275directiveInject"](g.b),s["\u0275\u0275directiveInject"](y),s["\u0275\u0275directiveInject"](L.l))},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-fieldmapping"]],decls:18,vars:3,consts:[[1,"mat-typography",2,"display","block","padding","24px","border-radius","4px","box-sizing","border-box","overflow","auto","outline","0","width","100%","min-width","90vw","height","100%","min-height","inherit","max-height","inherit"],[1,"ecternal-container"],[1,"container","mb-3"],[1,"row","border-bottom","solid",2,"font-size","18px","color","#CF0001"],[1,"col-11","pt-2","pb-2","d-flex"],["mat-icon-button","",1,"bubble","mt-1"],[1,"iconButton"],[1,"name","my-auto","ml-2"],[1,"col-1","d-flex","pt-2"],["mat-icon-button","",1,"ml-auto","close-button","mt-1"],["matTooltip","Close",1,"close-Icon",2,"cursor","pointer","color","#4d4d4b",3,"click"],["class","content d-flex justify-content-evenly ",4,"ngIf"],["align","end"],["class","addNewField","mat-flat-button","","color","primary",3,"click",4,"ngIf"],[1,"content","d-flex","justify-content-evenly"],[1,"d-flex","justify-content-evenly",3,"formGroup"],[1,"formDiv","gap-5","d-flex","justify-content-evenly"],["formArrayName","itemRows",1,"",2,"text-align","center"],[1,"row"],[1,"col-6"],[1,"col-2"],[1,"col-4"],[1,"col-12"],["class","fiel d-flex  col-12","style","align-items: center;",3,"formGroupName",4,"ngFor","ngForOf"],[1,"fiel","d-flex","col-12",2,"align-items","center",3,"formGroupName"],[1,"col-7",2,"width","100%"],["placeholder","Source Field","formControlName","src_field",2,"width","100%",3,"list","disableNone"],[1,"col-2",2,"width","100%"],["appearance","outline",1,"col-12","pl-0","pr-0"],["matInput","","placeholder","Type Conversion","formControlName","dest_type_field","required","",1,"not-empty-select"],[3,"value",4,"ngFor","ngForOf"],[1,"d-flex","fiel","col-3",2,"width","100%"],["appearance","outline"],["matInput","","placeholder","value","readonly","",3,"value"],["mat-stroked-button","","color","primary",1,"addNewField","text-dark","border","border-dark","rounded",2,"height","35px","margin-top","5px","margin-left","5px",3,"ngStyle","click"],[3,"value"],["mat-flat-button","","color","primary",1,"addNewField",3,"click"]],template:function(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"mat-dialog-content",0),s["\u0275\u0275elementStart"](1,"div",1),s["\u0275\u0275elementStart"](2,"div",2),s["\u0275\u0275elementStart"](3,"div",3),s["\u0275\u0275elementStart"](4,"div",4),s["\u0275\u0275elementStart"](5,"div",5),s["\u0275\u0275elementStart"](6,"mat-icon",6),s["\u0275\u0275text"](7,"note_add"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](8,"span",7),s["\u0275\u0275text"](9,"Field Mapping"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](10,"div",8),s["\u0275\u0275elementStart"](11,"button",9),s["\u0275\u0275elementStart"](12,"mat-icon",10),s["\u0275\u0275listener"]("click",(function(){return t.close_FieldMapping()})),s["\u0275\u0275text"](13,"close"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](14,st,13,2,"div",11),s["\u0275\u0275elementStart"](15,"mat-dialog-actions",12),s["\u0275\u0275template"](16,at,2,0,"button",13),s["\u0275\u0275template"](17,rt,2,0,"button",13),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e&&(s["\u0275\u0275advance"](14),s["\u0275\u0275property"]("ngIf",t.BindButtonFlag),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("ngIf",!t.submitFlag),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",t.submitFlag))},directives:[g.f,_.a,b.a,v.a,i.NgIf,g.c,c.J,c.w,c.n,c.h,i.NgForOf,c.o,E.a,c.v,c.l,C.c,O.c,c.F,x.b,i.NgStyle,w.p],styles:[".mat-typography[_ngcontent-%COMP%]   .tool-tip-class[_ngcontent-%COMP%]{word-break:break-all!important;white-space:normal!important}"]}),e})();var ct=n("HDdC"),dt=n("PSD3"),pt=n.n(dt),ht=n("1yaQ");let ut=(()=>{class e{constructor(){this.convertKeyValueToObject=e=>(console.log("Pair : ",e),[...e].reduce((e,t)=>{console.log("Pair : ",t);const n=t.key,i=t.value;return""===n?e:Object.assign(Object.assign({},e),{[n]:i})},{}))}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275prov=s["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"any"}),e})();var mt=n("bv9b"),gt=n("wZkO"),ft=n("iadO");function yt(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"div",12),s["\u0275\u0275elementStart"](1,"div",13),s["\u0275\u0275elementStart"](2,"span",14),s["\u0275\u0275text"](3),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](4,"div",15),s["\u0275\u0275elementStart"](5,"span",16),s["\u0275\u0275elementStart"](6,"button",17),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"]().onIntegAPI()})),s["\u0275\u0275text"](7,"Integrate Data"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275nextContext"]();s["\u0275\u0275advance"](2),s["\u0275\u0275propertyInterpolate"]("matTooltip",e.systemName),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate1"](" ",e.systemName," ")}}function _t(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"div"),s["\u0275\u0275elementStart"](1,"div",12),s["\u0275\u0275elementStart"](2,"div",13),s["\u0275\u0275elementStart"](3,"span",14),s["\u0275\u0275text"](4),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](5,"div",15),s["\u0275\u0275elementStart"](6,"span",16),s["\u0275\u0275elementStart"](7,"button",17),s["\u0275\u0275listener"]("click",(function(){s["\u0275\u0275restoreView"](e);const n=t.index;return s["\u0275\u0275nextContext"]().testAPI(n)})),s["\u0275\u0275text"](8,"Test API"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;s["\u0275\u0275advance"](3),s["\u0275\u0275propertyInterpolate"]("matTooltip",e[1]),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate1"](" ",e[1]," ")}}function bt(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"div",18),s["\u0275\u0275elementContainerStart"](1),s["\u0275\u0275element"](2,"mat-spinner"),s["\u0275\u0275elementContainerEnd"](),s["\u0275\u0275elementEnd"]())}function vt(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"button",42),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"](2).onChangeStatusFlag()})),s["\u0275\u0275elementStart"](1,"mat-icon",22),s["\u0275\u0275text"](2,"expand_circle_down"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}}const St=function(e){return{"fa-circle-o-notch fa-spin":e}};function Ct(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"button",43),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"](2).onEDBgjProcess()})),s["\u0275\u0275element"](1,"i",44),s["\u0275\u0275text"](2," ED BGJ"),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275nextContext"](2);s["\u0275\u0275property"]("disabled",e.EDBGJLoading),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngClass",s["\u0275\u0275pureFunction1"](2,St,e.EDBGJLoading))}}function xt(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"button",45),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"](2).onEDBgjProcess()})),s["\u0275\u0275element"](1,"i",44),s["\u0275\u0275text"](2," BGJ Processing"),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275nextContext"](2);s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngClass",s["\u0275\u0275pureFunction1"](1,St,e.EDBGJLoading))}}function Ot(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"button",43),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"](2).employeeSync()})),s["\u0275\u0275element"](1,"i",44),s["\u0275\u0275text"](2," Employee Sync"),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275nextContext"](2);s["\u0275\u0275property"]("disabled",e.employeeSyncLoading),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngClass",s["\u0275\u0275pureFunction1"](2,St,e.employeeSyncLoading))}}function wt(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"button",43),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"](2).employeeSync()})),s["\u0275\u0275element"](1,"i",44),s["\u0275\u0275text"](2," Sync In Process"),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275nextContext"](2);s["\u0275\u0275property"]("disabled",e.employeeSyncLoading),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngClass",s["\u0275\u0275pureFunction1"](2,St,e.employeeSyncLoading))}}function Et(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"button",43),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"](2).onBgjProcess()})),s["\u0275\u0275element"](1,"i",44),s["\u0275\u0275text"](2," Initiate BGJ"),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275nextContext"](2);s["\u0275\u0275property"]("disabled",e.bgjLoading),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngClass",s["\u0275\u0275pureFunction1"](2,St,e.bgjLoading))}}function Pt(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"button",43),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"](2).onBgjProcess()})),s["\u0275\u0275element"](1,"i",44),s["\u0275\u0275text"](2," BGJ Processing"),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275nextContext"](2);s["\u0275\u0275property"]("disabled",e.bgjLoading),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngClass",s["\u0275\u0275pureFunction1"](2,St,e.bgjLoading))}}function It(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"mat-option",46),s["\u0275\u0275text"](1),s["\u0275\u0275elementEnd"]()),2&e){const e=s["\u0275\u0275nextContext"](2);s["\u0275\u0275property"]("disabled",!0)("value",e.reqMethod),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate1"](" ",e.reqMethod," ")}}function Mt(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"div",18),s["\u0275\u0275elementContainerStart"](1),s["\u0275\u0275element"](2,"mat-spinner"),s["\u0275\u0275elementContainerEnd"](),s["\u0275\u0275elementEnd"]())}const Dt=function(e){return{left:e}};function kt(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"div",18),s["\u0275\u0275elementStart"](1,"div",47),s["\u0275\u0275element"](2,"mat-progress-bar",48),s["\u0275\u0275elementStart"](3,"span",49),s["\u0275\u0275text"](4),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](5,"p"),s["\u0275\u0275text"](6,"Kindly Don't refresh the page...! Data under sync... "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e){const e=s["\u0275\u0275nextContext"](2);s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("value",e.syncProgress)("color",e.Warn),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngStyle",s["\u0275\u0275pureFunction1"](4,Dt,e.syncProgress+"%")),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate1"]("",e.syncProgress,"%")}}function jt(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"div",18),s["\u0275\u0275elementStart"](1,"div",47),s["\u0275\u0275element"](2,"mat-progress-bar",48),s["\u0275\u0275elementStart"](3,"span",49),s["\u0275\u0275text"](4),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](5,"p"),s["\u0275\u0275text"](6,"Kindly Don't refresh the page...! Employee Data under sync... "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e){const e=s["\u0275\u0275nextContext"](2);s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("value",e.syncProgress)("color",e.Warn),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngStyle",s["\u0275\u0275pureFunction1"](4,Dt,e.syncProgress+"%")),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate1"]("",e.syncProgress,"%")}}function At(e,t){if(1&e&&s["\u0275\u0275text"](0),2&e){const e=s["\u0275\u0275nextContext"]().$implicit;s["\u0275\u0275textInterpolate"](e.label)}}function Ft(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"mat-option",68),s["\u0275\u0275listener"]("click",(function(){s["\u0275\u0275restoreView"](e);const n=t.$implicit;return s["\u0275\u0275nextContext"](6).queryKeyField(n)})),s["\u0275\u0275text"](1),s["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;s["\u0275\u0275property"]("value",e),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate1"](" ",e," ")}}function Rt(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"mat-form-field",69),s["\u0275\u0275elementStart"](1,"div",39),s["\u0275\u0275elementStart"](2,"input",70),s["\u0275\u0275listener"]("change",(function(t){s["\u0275\u0275restoreView"](e);const n=s["\u0275\u0275nextContext"]().index;return s["\u0275\u0275nextContext"](5).queryValueField(t,n)})),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}}function Tt(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"mat-form-field",71),s["\u0275\u0275element"](1,"input",72),s["\u0275\u0275element"](2,"mat-datepicker-toggle",73),s["\u0275\u0275element"](3,"mat-datepicker",null,74),s["\u0275\u0275elementEnd"]()),2&e){const e=s["\u0275\u0275reference"](4);s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("matDatepicker",e),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("for",e)}}function Lt(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"div",66),s["\u0275\u0275elementStart"](1,"input",75),s["\u0275\u0275listener"]("dateChange",(function(t){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"](6).addQueryDateEvent("change",t)})),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](2,"mat-datepicker-toggle",76),s["\u0275\u0275elementStart"](3,"mat-icon",77),s["\u0275\u0275text"](4,"calendar_month"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275element"](5,"mat-datepicker",null,78),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275reference"](6);s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("matDatepicker",e),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("for",e)}}function Nt(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"div",58),s["\u0275\u0275elementStart"](1,"div",59),s["\u0275\u0275elementStart"](2,"mat-form-field",60),s["\u0275\u0275elementStart"](3,"mat-select",61),s["\u0275\u0275template"](4,Ft,2,2,"mat-option",62),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](5,"mat-icon",63),s["\u0275\u0275text"](6," insert_link"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](7,Rt,3,0,"mat-form-field",64),s["\u0275\u0275template"](8,Tt,5,2,"mat-form-field",65),s["\u0275\u0275elementStart"](9,"div",66),s["\u0275\u0275elementStart"](10,"button",56),s["\u0275\u0275listener"]("click",(function(){s["\u0275\u0275restoreView"](e);const n=t.index;return s["\u0275\u0275nextContext"](5).onQueryAddRow(n)})),s["\u0275\u0275elementStart"](11,"mat-icon"),s["\u0275\u0275text"](12,"add_box"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](13,"div",66),s["\u0275\u0275elementStart"](14,"button",56),s["\u0275\u0275listener"]("click",(function(){s["\u0275\u0275restoreView"](e);const n=t.index;return s["\u0275\u0275nextContext"](5).onQueryRemoveRow(n)})),s["\u0275\u0275elementStart"](15,"mat-icon"),s["\u0275\u0275text"](16,"delete"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](17,Lt,7,2,"div",67),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}if(2&e){const e=t.index,n=s["\u0275\u0275nextContext"](5);s["\u0275\u0275property"]("formGroupName",e),s["\u0275\u0275advance"](4),s["\u0275\u0275property"]("ngForOf",n.queryParamsKeyFields),s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("ngIf","start_date"!=n.queryParamsControl.value[e].key&&"end_date"!=n.queryParamsControl.value[e].key&&"from_date"!=n.queryParamsControl.value[e].key&&"to_date"!=n.queryParamsControl.value[e].key&&"date_end"!=n.queryParamsControl.value[e].key&&"date_start"!=n.queryParamsControl.value[e].key),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf","start_date"==n.queryParamsControl.value[e].key||"end_date"==n.queryParamsControl.value[e].key||"from_date"==n.queryParamsControl.value[e].key||"to_date"==n.queryParamsControl.value[e].key||"date_end"==n.queryParamsControl.value[e].key||"date_start"==n.queryParamsControl.value[e].key),s["\u0275\u0275advance"](9),s["\u0275\u0275property"]("ngIf",n.queryCalendarFlag&&n.isQbFlag)}}function Bt(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"p",79),s["\u0275\u0275elementStart"](1,"mat-icon"),s["\u0275\u0275text"](2,"check"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275text"](3,"Query Params Added Successfully...!"),s["\u0275\u0275elementEnd"]())}function qt(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"div"),s["\u0275\u0275elementStart"](1,"div",51),s["\u0275\u0275elementStart"](2,"form",52),s["\u0275\u0275elementStart"](3,"div",53),s["\u0275\u0275template"](4,Nt,18,5,"div",54),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](5,"div",55),s["\u0275\u0275elementStart"](6,"button",56),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"](4).onSubmit()})),s["\u0275\u0275text"](7," submit"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](8,Bt,4,0,"p",57),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275nextContext"](4);s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("formGroup",e.queryFieldMapping),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("ngForOf",e.queryGetControls()),s["\u0275\u0275advance"](4),s["\u0275\u0275property"]("ngIf",e.querySubmitFlag)}}function $t(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"mat-option",68),s["\u0275\u0275listener"]("click",(function(){s["\u0275\u0275restoreView"](e);const n=t.$implicit;return s["\u0275\u0275nextContext"](5).onOptionChange(n)})),s["\u0275\u0275text"](1),s["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;s["\u0275\u0275property"]("value",e),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate1"](" ",e," ")}}function Vt(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"div",87),s["\u0275\u0275elementStart"](1,"mat-form-field",88),s["\u0275\u0275elementStart"](2,"input",89),s["\u0275\u0275listener"]("change",(function(t){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"](5).authenticationKeyField(t)})),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](3,"mat-form-field",88),s["\u0275\u0275elementStart"](4,"input",90),s["\u0275\u0275listener"]("change",(function(t){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"](5).authenticationValueField(t)})),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}}function zt(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"p",79),s["\u0275\u0275elementStart"](1,"mat-icon"),s["\u0275\u0275text"](2,"check"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275text"](3,"Token Generated Successfully...!"),s["\u0275\u0275elementEnd"]())}function Ht(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"div"),s["\u0275\u0275elementStart"](1,"mat-form-field",88),s["\u0275\u0275elementStart"](2,"input",93),s["\u0275\u0275listener"]("keyup",(function(t){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"](6).app_idChange(t)})),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](3,"button",56),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"](6).getBearerTokenPost()})),s["\u0275\u0275text"](4,"Get BearerToken"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](5,zt,4,0,"p",57),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275nextContext"](6);s["\u0275\u0275advance"](5),s["\u0275\u0275property"]("ngIf",e.refreshTokenFlag)}}function Jt(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"p",79),s["\u0275\u0275elementStart"](1,"mat-icon"),s["\u0275\u0275text"](2,"check"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275text"](3,"Token Generated Successfully...!"),s["\u0275\u0275elementEnd"]())}function Ut(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"div",94),s["\u0275\u0275elementStart"](1,"button",56),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"](6).getBearerToken()})),s["\u0275\u0275text"](2,"refresh"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](3,Jt,4,0,"p",57),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275nextContext"](6);s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("ngIf",e.refreshTokenFlag)}}function Yt(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"div",87),s["\u0275\u0275elementStart"](1,"mat-form-field",88),s["\u0275\u0275elementStart"](2,"input",91),s["\u0275\u0275listener"]("change",(function(t){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"](5).authenticationValueField(t)})),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](3,Ht,6,1,"div",11),s["\u0275\u0275template"](4,Ut,4,1,"div",92),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275nextContext"](5);s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("ngIf",!e.appIdFlag),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",e.appIdFlag)}}function Kt(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"div",87),s["\u0275\u0275elementStart"](1,"mat-form-field",88),s["\u0275\u0275elementStart"](2,"input",95),s["\u0275\u0275listener"]("change",(function(t){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"](5).authenticationKeyField(t)})),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](3,"mat-form-field",88),s["\u0275\u0275elementStart"](4,"input",96),s["\u0275\u0275listener"]("change",(function(t){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"](5).authenticationValueField(t)})),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}}function Qt(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"div"),s["\u0275\u0275elementStart"](1,"div",97),s["\u0275\u0275elementStart"](2,"label",102),s["\u0275\u0275text"](3,"Client ID"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](4,"mat-form-field",88),s["\u0275\u0275element"](5,"input",103),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](6,"div",97),s["\u0275\u0275elementStart"](7,"label",104),s["\u0275\u0275text"](8,"Client Secret"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](9,"mat-form-field",88),s["\u0275\u0275element"](10,"input",103),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](11,"div",105),s["\u0275\u0275elementStart"](12,"button",56),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"](6).getOAuthToken()})),s["\u0275\u0275text"](13,"Get New Access Token"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275nextContext"](6);s["\u0275\u0275advance"](5),s["\u0275\u0275property"]("formControl",e.client_id_oauth_2_0),s["\u0275\u0275advance"](5),s["\u0275\u0275property"]("formControl",e.client_secret_oauth_2_0)}}function Gt(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"div"),s["\u0275\u0275elementStart"](1,"div",97),s["\u0275\u0275elementStart"](2,"label",102),s["\u0275\u0275text"](3,"Client ID"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](4,"mat-form-field",88),s["\u0275\u0275element"](5,"input",103),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](6,"div",97),s["\u0275\u0275elementStart"](7,"label",104),s["\u0275\u0275text"](8,"Client Secret"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](9,"mat-form-field",88),s["\u0275\u0275element"](10,"input",103),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](11,"div",105),s["\u0275\u0275elementStart"](12,"a",106),s["\u0275\u0275elementStart"](13,"button",107),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"](6).getOAuthToken()})),s["\u0275\u0275text"](14," Get New Access Token"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275nextContext"](6);s["\u0275\u0275advance"](5),s["\u0275\u0275property"]("formControl",e.client_id_oauth_2_0),s["\u0275\u0275advance"](5),s["\u0275\u0275property"]("formControl",e.client_secret_oauth_2_0)}}function Zt(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"div",87),s["\u0275\u0275elementStart"](1,"div",97),s["\u0275\u0275elementStart"](2,"label",98),s["\u0275\u0275text"](3,"Access Token"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](4,"div",99),s["\u0275\u0275elementStart"](5,"mat-form-field",100),s["\u0275\u0275elementStart"](6,"input",91),s["\u0275\u0275listener"]("change",(function(t){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"](5).authenticationValueField(t)})),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](7,"button",101),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"](5).getQuickBookTokenOnSync()})),s["\u0275\u0275element"](8,"i",44),s["\u0275\u0275text"](9),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](10,"h1"),s["\u0275\u0275text"](11,"Configure New Token"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](12,Qt,14,2,"div",11),s["\u0275\u0275template"](13,Gt,15,2,"div",11),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275nextContext"](5);s["\u0275\u0275advance"](7),s["\u0275\u0275property"]("disabled",e.accesstokenflagonrefresh),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngClass",s["\u0275\u0275pureFunction1"](5,St,e.accesstokenflagonrefresh)),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate1"](" ",e.accesstokenflagonrefresh?"Processing access token":"Refresh token"," "),s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("ngIf",e.OAuthInput.Success),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",!e.OAuthInput.Success)}}function Wt(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"div"),s["\u0275\u0275elementStart"](1,"div",97),s["\u0275\u0275elementStart"](2,"label",102),s["\u0275\u0275text"](3,"Client ID"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](4,"mat-form-field",88),s["\u0275\u0275element"](5,"input",103),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](6,"div",97),s["\u0275\u0275elementStart"](7,"label",108),s["\u0275\u0275text"](8,"User Id"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](9,"mat-form-field",88),s["\u0275\u0275element"](10,"input",103),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](11,"div",97),s["\u0275\u0275elementStart"](12,"label",109),s["\u0275\u0275text"](13,"Company Id"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](14,"mat-form-field",88),s["\u0275\u0275element"](15,"input",103),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](16,"div",97),s["\u0275\u0275elementStart"](17,"label",110),s["\u0275\u0275text"](18,"Private Key"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](19,"mat-form-field",88),s["\u0275\u0275element"](20,"input",103),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](21,"div",105),s["\u0275\u0275elementStart"](22,"button",56),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"](6).postSAPSFTokenOnSync()})),s["\u0275\u0275text"](23,"Get New Access Token"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275nextContext"](6);s["\u0275\u0275advance"](5),s["\u0275\u0275property"]("formControl",e.client_id_oauth_sap),s["\u0275\u0275advance"](5),s["\u0275\u0275property"]("formControl",e.user_id_oauth_sap),s["\u0275\u0275advance"](5),s["\u0275\u0275property"]("formControl",e.company_id_oauth_sap),s["\u0275\u0275advance"](5),s["\u0275\u0275property"]("formControl",e.private_key_oauth_sap)}}function Xt(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"div"),s["\u0275\u0275elementStart"](1,"div",97),s["\u0275\u0275elementStart"](2,"label",102),s["\u0275\u0275text"](3,"Client ID"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](4,"mat-form-field",88),s["\u0275\u0275element"](5,"input",103),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](6,"div",97),s["\u0275\u0275elementStart"](7,"label",108),s["\u0275\u0275text"](8,"User Id"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](9,"mat-form-field",88),s["\u0275\u0275element"](10,"input",103),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](11,"div",97),s["\u0275\u0275elementStart"](12,"label",108),s["\u0275\u0275text"](13,"Company Id"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](14,"mat-form-field",88),s["\u0275\u0275element"](15,"input",103),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](16,"div",97),s["\u0275\u0275elementStart"](17,"label",110),s["\u0275\u0275text"](18,"Private Key"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](19,"mat-form-field",88),s["\u0275\u0275element"](20,"input",103),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](21,"div",105),s["\u0275\u0275elementStart"](22,"button",56),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"](6).postSAPSFTokenOnSync()})),s["\u0275\u0275text"](23,"Get New Access Token"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275nextContext"](6);s["\u0275\u0275advance"](5),s["\u0275\u0275property"]("formControl",e.client_id_oauth_sap),s["\u0275\u0275advance"](5),s["\u0275\u0275property"]("formControl",e.user_id_oauth_sap),s["\u0275\u0275advance"](5),s["\u0275\u0275property"]("formControl",e.company_id_oauth_sap),s["\u0275\u0275advance"](5),s["\u0275\u0275property"]("formControl",e.private_key_oauth_sap)}}function en(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"div",87),s["\u0275\u0275elementStart"](1,"div",97),s["\u0275\u0275elementStart"](2,"label",98),s["\u0275\u0275text"](3,"Access Token"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](4,"div",99),s["\u0275\u0275elementStart"](5,"mat-form-field",100),s["\u0275\u0275elementStart"](6,"input",91),s["\u0275\u0275listener"]("change",(function(t){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"](5).authenticationValueField(t)})),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](7,"button",101),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"](5).getSAPSFTokenOnSync()})),s["\u0275\u0275element"](8,"i",44),s["\u0275\u0275text"](9),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](10,"h1"),s["\u0275\u0275text"](11,"Configure New Token"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](12,Wt,24,4,"div",11),s["\u0275\u0275template"](13,Xt,24,4,"div",11),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275nextContext"](5);s["\u0275\u0275advance"](7),s["\u0275\u0275property"]("disabled",e.accesstokenflagonrefresh),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngClass",s["\u0275\u0275pureFunction1"](5,St,e.accesstokenflagonrefresh)),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate1"](" ",e.accesstokenflagonrefresh?"Processing access token":"Refresh token"," "),s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("ngIf",e.OAuthInputSAP.Success),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",!e.OAuthInputSAP.Success)}}function tn(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"p",79),s["\u0275\u0275elementStart"](1,"mat-icon"),s["\u0275\u0275text"](2,"check"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275text"](3,"Authentication Submitted...!"),s["\u0275\u0275elementEnd"]())}function nn(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"div"),s["\u0275\u0275elementStart"](1,"div",80),s["\u0275\u0275elementStart"](2,"form",52),s["\u0275\u0275elementStart"](3,"div",81),s["\u0275\u0275elementStart"](4,"div",82),s["\u0275\u0275elementStart"](5,"mat-form-field",83),s["\u0275\u0275elementStart"](6,"mat-select",84),s["\u0275\u0275template"](7,$t,2,2,"mat-option",62),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275element"](8,"div",85),s["\u0275\u0275template"](9,Vt,5,0,"div",86),s["\u0275\u0275template"](10,Yt,5,2,"div",86),s["\u0275\u0275template"](11,Kt,5,0,"div",86),s["\u0275\u0275template"](12,Zt,14,7,"div",86),s["\u0275\u0275template"](13,en,14,7,"div",86),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](14,"div",55),s["\u0275\u0275elementStart"](15,"button",56),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"](4).onAuthenticationSubmit()})),s["\u0275\u0275text"](16," submit"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](17,tn,4,0,"p",57),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275nextContext"](4);s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("formGroup",e.authenticationFieldMapping),s["\u0275\u0275advance"](4),s["\u0275\u0275property"]("value",e.currentAuthenticationOption),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngForOf",e.authenticationOptions),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("ngIf","APIKey"==e.currentAuthenticationOption),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf","Bearer"==e.currentAuthenticationOption),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf","Basic"==e.currentAuthenticationOption),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf","OAuth 2.0"==e.currentAuthenticationOption),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf","OAuth SAP"==e.currentAuthenticationOption),s["\u0275\u0275advance"](4),s["\u0275\u0275property"]("ngIf",e.authenticationSubmitFlag)}}function on(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"div",58),s["\u0275\u0275elementStart"](1,"div",112),s["\u0275\u0275elementStart"](2,"mat-form-field",60),s["\u0275\u0275elementStart"](3,"mat-select",113),s["\u0275\u0275elementStart"](4,"mat-option",114),s["\u0275\u0275text"](5," voucher_type "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](6,"mat-icon",63),s["\u0275\u0275text"](7," insert_link"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275element"](8,"app-input-search",115),s["\u0275\u0275elementStart"](9,"div",66),s["\u0275\u0275elementStart"](10,"button",56),s["\u0275\u0275listener"]("click",(function(){s["\u0275\u0275restoreView"](e);const n=t.index;return s["\u0275\u0275nextContext"](5).onVoucherTypeAddRow(n)})),s["\u0275\u0275elementStart"](11,"mat-icon"),s["\u0275\u0275text"](12,"add_box"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](13,"div",66),s["\u0275\u0275elementStart"](14,"button",56),s["\u0275\u0275listener"]("click",(function(){s["\u0275\u0275restoreView"](e);const n=t.index;return s["\u0275\u0275nextContext"](5).onVoucherTypeRemoveRow(n)})),s["\u0275\u0275elementStart"](15,"mat-icon"),s["\u0275\u0275text"](16,"delete"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}if(2&e){const e=t.index,n=s["\u0275\u0275nextContext"](5);s["\u0275\u0275property"]("formGroupName",e),s["\u0275\u0275advance"](8),s["\u0275\u0275property"]("list",n.availableZBReportVoucherTypes)}}function sn(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"p",79),s["\u0275\u0275elementStart"](1,"mat-icon"),s["\u0275\u0275text"](2,"check"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275text"](3,"Voucher Types Selected...!"),s["\u0275\u0275elementEnd"]())}function an(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"div"),s["\u0275\u0275elementStart"](1,"div",111),s["\u0275\u0275elementStart"](2,"form",52),s["\u0275\u0275elementStart"](3,"div",53),s["\u0275\u0275template"](4,on,17,2,"div",54),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](5,"div",55),s["\u0275\u0275elementStart"](6,"button",56),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"](4).onVoucherTypesSubmit()})),s["\u0275\u0275text"](7," submit"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](8,sn,4,0,"p",57),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275nextContext"](4);s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("formGroup",e.voucherTypesFieldMapping),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("ngForOf",e.voucherTypeGetControl()),s["\u0275\u0275advance"](4),s["\u0275\u0275property"]("ngIf",e.voucherTypesAppliedFlag)}}function rn(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"mat-tab"),s["\u0275\u0275template"](1,At,1,1,"ng-template",50),s["\u0275\u0275template"](2,qt,9,3,"div",11),s["\u0275\u0275template"](3,nn,18,9,"div",11),s["\u0275\u0275template"](4,an,9,3,"div",11),s["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=s["\u0275\u0275nextContext"](3);s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("ngIf",1==e.content),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",3==e.content),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",5==e.content&&n.isZBVoucherTypesTabVisible)}}function ln(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"mat-tab-group"),s["\u0275\u0275template"](1,rn,5,3,"mat-tab",8),s["\u0275\u0275pipe"](2,"async"),s["\u0275\u0275elementEnd"]()),2&e){const e=s["\u0275\u0275nextContext"](2);s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngForOf",s["\u0275\u0275pipeBind1"](2,1,e.asyncTabs))}}function cn(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"div",18),s["\u0275\u0275elementStart"](1,"div",47),s["\u0275\u0275elementContainerStart"](2),s["\u0275\u0275element"](3,"mat-spinner"),s["\u0275\u0275elementContainerEnd"](),s["\u0275\u0275elementStart"](4,"p"),s["\u0275\u0275text"](5,"Kindly Don't refresh the page...! Retriving Data... "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]())}function dn(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"textarea",116),s["\u0275\u0275pipe"](1,"json"),s["\u0275\u0275text"](2,"        \n                "),s["\u0275\u0275elementEnd"]()),2&e){const e=s["\u0275\u0275nextContext"](2);s["\u0275\u0275property"]("innerHTML",s["\u0275\u0275pipeBind1"](1,1,e.sampleResponse),s["\u0275\u0275sanitizeHtml"])}}function pn(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"button",117),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"](2).onFieldMapping()})),s["\u0275\u0275text"](1," Field Mapping "),s["\u0275\u0275elementEnd"]()}}function hn(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"button",118),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"](2).onSync()})),s["\u0275\u0275text"](1," Sync "),s["\u0275\u0275elementEnd"]()}}function un(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"div"),s["\u0275\u0275elementStart"](1,"div",19),s["\u0275\u0275elementStart"](2,"div",20),s["\u0275\u0275elementStart"](3,"div",21),s["\u0275\u0275elementStart"](4,"mat-icon",22),s["\u0275\u0275text"](5,"api"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](6,"span",23),s["\u0275\u0275text"](7),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](8,"div",24),s["\u0275\u0275template"](9,vt,3,0,"button",25),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](10,"div",26),s["\u0275\u0275elementStart"](11,"span",16),s["\u0275\u0275template"](12,Ct,3,4,"button",27),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](13,"span",16),s["\u0275\u0275template"](14,xt,3,3,"button",28),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](15,"span",16),s["\u0275\u0275template"](16,Ot,3,4,"button",27),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](17,"span",16),s["\u0275\u0275template"](18,wt,3,4,"button",27),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](19,"span",16),s["\u0275\u0275template"](20,Et,3,4,"button",27),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](21,"span",16),s["\u0275\u0275template"](22,Pt,3,4,"button",27),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](23,"form",29),s["\u0275\u0275elementStart"](24,"mat-form-field",30),s["\u0275\u0275elementStart"](25,"mat-select",31),s["\u0275\u0275template"](26,It,2,3,"mat-option",32),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](27,"mat-form-field",33),s["\u0275\u0275elementStart"](28,"input",34),s["\u0275\u0275listener"]("ngModelChange",(function(t){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"]().url=t}))("keyup",(function(t){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"]().setUrl(t)})),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](29,"div",35),s["\u0275\u0275elementStart"](30,"button",36),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"]().handleOnInputSend()})),s["\u0275\u0275text"](31," Send "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](32,Mt,3,0,"div",10),s["\u0275\u0275pipe"](33,"async"),s["\u0275\u0275template"](34,kt,7,6,"div",10),s["\u0275\u0275template"](35,jt,7,6,"div",10),s["\u0275\u0275template"](36,ln,3,3,"mat-tab-group",11),s["\u0275\u0275elementStart"](37,"div",37),s["\u0275\u0275template"](38,cn,6,0,"div",10),s["\u0275\u0275template"](39,dn,3,3,"textarea",38),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](40,"div",39),s["\u0275\u0275template"](41,pn,2,0,"button",40),s["\u0275\u0275template"](42,hn,2,0,"button",41),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275nextContext"]();s["\u0275\u0275advance"](7),s["\u0275\u0275textInterpolate"](e.qbHeaderFlag?e.qbHeaderName:e.headerName),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("ngIf",e.qbHeaderOutboundFlag),s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("ngIf",("SAP_SF"==e.systemName||"Office_365"==e.systemName)&&e.EDBGJFlag&&0==e.EDBGJLoading),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("ngIf",("SAP_SF"==e.systemName||"Office_365"==e.systemName)&&e.EDBGJFlag&&1==e.EDBGJLoading),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("ngIf",("SAP_SF"==e.systemName||"Office_365"==e.systemName)&&0==e.employeeSyncLoading),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("ngIf",("SAP_SF"==e.systemName||"Office_365"==e.systemName)&&1==e.employeeSyncLoading),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("ngIf",e.bgjFlag&&0==e.bgjLoading),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("ngIf",e.bgjFlag&&1==e.bgjLoading),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("formGroup",e.hitapi),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("disabled",!0)("value",e.reqMethod),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngForOf",e.requestMethods),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("disabled",e.onSyncFlag)("ngModel",e.url),s["\u0275\u0275advance"](4),s["\u0275\u0275property"]("ngIf",null===s["\u0275\u0275pipeBind1"](33,22,e.asyncTabs)),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("ngIf",e.onSyncFlag&&!e.subsequentFlag),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",e.onSyncFlag&&e.subsequentFlag),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",!e.onSyncFlag),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("ngIf",!1===e.responseProcess),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",!0===e.responseProcess),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("ngIf",e.syncFlag&&"Intuit"!=e.systemName&&e.syncFlag&&"ZOHOBOOKS"!=e.systemName&&e.syncFlag&&"KEBS_ZB"!=e.systemName&&e.syncFlag&&"KEBS_QB"!=e.systemName),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",e.syncFlag)}}const mn=function(){return{"is-submitted-bg":!0}},gn=[{key:"",value:""}];let fn=(()=>{class e{constructor(e,t,n,i,o,s,a,r){this.activatedRoute=e,this.helpers=t,this.fb=n,this.router=i,this.dialog=o,this._toaster=s,this.api=a,this._liveAnnouncer=r,this.$onDestroy=new u.b,this.$onAppApiCalled=new u.b,this.csrf_token=void 0,this.authorization=void 0,this.headerName="Integrate Data",this.batch_size=10,this.onSyncBatchSize=10,this.responseProcess=!0,this.queryKeyPairInitState=[],this.headerKeyPairInitState=[],this.bodyKeyPairInitState=[],this.source_field=[],this.url="",this.temp_URL_users="",this.reqMethod="GET",this.queryParams=gn,this.headers=gn,this.authentication=gn,this.body={url:this.url},this.bodyTemp="",this.response=[],this.tempResponse=[],this.tempResponseHeader=[],this.sampleResponse=[],this.queryKey="",this.queryValue="",this.headerKey="",this.headerValue="",this.authenticationOptions=["APIKey","Bearer","Basic","OAuth 2.0","OAuth SAP"],this.grantType=["Authorization Code","Authorization Code (With PKCE)","Implicit","Password Credentials","Client Credentials"],this.grantOption=this.grantType[0],this.currentAuthenticationOption=this.authenticationOptions[0],this.authenticationKey="",this.authenticationValue="",this.APIKeyFlag=!1,this.BodyFlag=!1,this.syncFlag=!1,this.onSyncFlag=!1,this.loopUsers=0,this.app_ID="",this.appIdFlag=!1,this.fieldMappingFlag=!1,this.getToken=!1,this.accessToken="",this.isLoadingEndpoint=!0,this.newApiHit=!0,this.first_limit=0,this.isQbFlag=!1,this.querySubmitFlag=!1,this.queryCalendarFlag=!1,this.qbHeaderFlag=!1,this.qbHeaderOutboundFlag=!1,this.qbHeaderName="",this.accesstokenflagonrefresh=!1,this.syncProgress=0,this.lowerLimitOnSubSeqSync=0,this.lowerLimitOnSubSeqSyncPayloadSize=0,this.subsequentFlag=!1,this.currentConnectionItem=!1,this.changeVoucherStatusFlag=!1,this.isZBVoucherTypesTabVisible=!1,this.availableZBReportVoucherTypes=[],this.voucherTypesAppliedFlag=!1,this.appliedVoucherTypesforZB=[],this.onSyncBatchSizeZBReports=10,this.OAuthInputDetails={callbackurl:window.location.origin+"/main/ils/home/<USER>",clientID:"",clientSecret:""},this.OAuthSAPInputDetails={private_key:"",clientID:"",user_id:"",company_id:""},this.requestMethods=[{slug:"get",method:"GET"},{slug:"post",method:"POST"}],this.hitapi=this.fb.group({method:[this.reqMethod,c.H.required],url:[this.url,c.H.required]}),this.queryFieldMapping=new c.m({itemRows:new c.g([new c.m({key:new c.j("",c.H.required),value:new c.j("",c.H.required)})])}),this.headerFieldMapping=new c.m({itemRows:new c.g([new c.m({key:new c.j("",c.H.required),value:new c.j("",c.H.required)})])}),this.bodyFieldMapping=new c.m({body:new c.j(this.bodyTemp,c.H.required)}),this.authenticationFieldMapping=new c.m({key:new c.j(this.authenticationKey,c.H.required),value:new c.j(this.authenticationValue,c.H.required)}),this.voucherTypesFieldMapping=new c.m({itemRows:new c.g([new c.m({voucher_category:new c.j("",c.H.required),voucher_type:new c.j("",c.H.required)})])}),this.client_id_oauth_2_0=new c.j("",c.H.required),this.client_secret_oauth_2_0=new c.j("",c.H.required),this.client_id_oauth_sap=new c.j("",c.H.required),this.user_id_oauth_sap=new c.j("",c.H.required),this.company_id_oauth_sap=new c.j("",c.H.required),this.private_key_oauth_sap=new c.j("",c.H.required),this.bgjFlag=!1,this.EDBGJFlag=!1,this.bgjLoading=!1,this.EDBGJLoading=!1,this.bgjEndpoint="",this.EDBGJEndPoint="",this.employeeSyncLoading=!1,this.authenticationSubmitFlag=!1,this.field_map_response=[],this.index=0,this.batchresponse=["0"],this.single_entity=[],this.qb_processed_data_length=0,this.errorOnSyncJournalAccountFlag=!1,this.onSyncJournalAccountIter=0,this.batchSyncResponse=[],this.onSyncIter=0,this.getSyncUrlEndpoint={},this.refreshTokenFlag=!1,this.asyncTabs=new ct.a(e=>{setTimeout(()=>{e.next([{label:"Query Params",content:1},{label:"Authentication",content:3}])},1e3)})}ngOnInit(){this.queryParamsControl=this.queryFieldMapping.controls.itemRows,console.log("queryParamsControl : ",this.queryParamsControl),console.log("this.queryParamsControl.value : ",this.queryParamsControl.value),this.currentConnectionId=this.activatedRoute.snapshot.params.connection_id,this.systemName=this.activatedRoute.snapshot.params.systemName,console.log("System Name : ",this.systemName),"Intuit"==this.systemName&&(this.isQbFlag=!0),this.api.getbatchSize().pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{console.log("Batch Size for processing : ",e),this.batch_size=e.Result.batch_size,this.onSyncBatchSize=e.Result.on_sync_batch_size,"Intuit"==this.systemName?(this.onSyncBatchSize=e.Result.on_sync_batch_qb_size,this.onSyncBatchSize=Math.ceil(this.onSyncBatchSize),this.batchSpliceZbLineItemSize=e.Result.on_sync_batch_qb_size):"ZOHOBOOKS"==this.systemName?(this.onSyncBatchSize=e.Result.on_sync_batch_zb_size,this.onSyncBatchSize=Math.ceil(this.onSyncBatchSize),this.batchSpliceZbLineItemSize=e.Result.on_sync_batch_zb_size,this.onSyncBatchSizeZBReports=e.Result.on_sync_zb_reports_size):"KEBS_QB"!=this.systemName&&"KEBS_ZB"!=this.systemName||(this.onSyncBatchSize=e.Result.api_hit_limit,this.onSyncBatchSize=Math.ceil(this.onSyncBatchSize),this.batchSpliceZbLineItemSize=e.Result.api_hit_limit),console.log("this.batch_size : ",this.batch_size," and this.onSyncBatchSize : ",this.onSyncBatchSize)}),console.log("Host name : ",window.location.origin),this.api.getIlsBgj({source_system:this.systemName}).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{0!=e&&(this.bgjFlag=!0,this.bgjEndpoint=e,this.bgjLoading=!1,console.log("Bgj Endpoint : ",this.bgjEndpoint))}),this.api.getEDBgj({source_system:this.systemName}).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{1==e.Success&&(this.EDBGJFlag=!0,this.EDBGJEndPoint=e.endpoint,this.EDBGJLoading=!1,console.log("ED - Bgj Endpoint : ",this.EDBGJEndPoint))}),console.log("Connection ID : ",this.currentConnectionId),this.api.getConnection(this.currentConnectionId).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{this.reqMethod=e[0].source_method,this.api.getConnectionItems(this.currentConnectionId).pipe(Object(h.a)(()=>{this.postApiHit()})).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{this.sourceResource_List=e,console.log("Source Resource List inside ngOnInit : ",this.sourceResource_List),1==this.sourceResource_List.length||"SAP_SF"!=this.systemName?(this.clicked_source_resource=this.sourceResource_List[0][1],this.clicked_source_id=this.sourceResource_List[0][0]):(this.clicked_source_resource=this.systemName,this.clicked_source_id=""),console.log("clicked_source_resource inside ngOnInit : ",this.clicked_source_resource," with clicked_source_id : ",this.clicked_source_id),this.headerName=this.clicked_source_resource.replace("/","")})})}addQueryDateEvent(e,t){let n=Le(""+t.value).format("YYYY-MM-DDThh:mm:ss");this.queryDateStringClause=` where MetaData.LastUpdatedTime >= '${n}'`,console.log("queryDateStringClause generated from addQueryDateEvent() : ",this.queryDateStringClause),this.url+="="+n,this.body.url=this.url,this.queryCalendarFlag=!1}ngOnDestroy(){this.$onDestroy.next(),this.$onDestroy.complete()}onBgjProcess(){this.bgjLoading=!0;let e={connection_id:this.currentConnectionId,APIKey:!0,key:"null",method:"POST",body:{},data:{},url:String(window.location.origin)+"/api/integrationLayer/"+this.bgjEndpoint};console.log("BGJ Data API Hit",e);let t=[];"ZOHOBOOKS"==this.systemName&&(t=T.pluck(this.availableZBReportVoucherTypes,"name"),e.uniqueVoucherTypes=t),this.api.postSourceApiCall("postApiHit",e,{},{}).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){this.bgjLoading=!1,console.log("BGJ response : ",e),null!=e.errorStatus?this._toaster.showInfo("BGJ Status for "+this.systemName,"Error Info : "+e.errorStatus+" Error : "+e,5e3):this._toaster.showSuccess("BGJ Status for "+this.systemName,"Success",5e3)})),e=>{console.log("oops",e),this.bgjLoading=!1,this._toaster.showInfo("BGJ Status for "+this.systemName,"Background Job Under Progress",5e3)})}onEDBgjProcess(){return Object(r.c)(this,void 0,void 0,(function*(){this.EDBGJLoading=!0;let e={connection_id:this.currentConnectionId,APIKey:!0,key:"null",method:"POST",body:{},data:{},url:String(window.location.origin)+"/api/integrationLayer/"+this.EDBGJEndPoint};console.log("BGJ Data API Hit",e),this.api.postSourceApiCall("postApiHit",e,{},{}).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){return console.log("BGJ response : ",e),"S"!=e.Success?(this.EDBGJLoading=!1,this._toaster.showSuccess("BGJ Status for "+this.systemName,"Status Info : "+String(null==e?void 0:e.msg).replace("No","No more"),5e3),!0):(yield this.onEDBgjProcess(),!0)})),e=>(console.log("oops",e),this.EDBGJLoading=!1,this._toaster.showInfo("BGJ Status for "+this.systemName,"Background Job Under Progress",5e3),!0))}))}employeeSync(){return Object(r.c)(this,void 0,void 0,(function*(){this.employeeSyncLoading=!0,this.api.subsequentEndpointCall("postToUsers",this.onSyncBatchSize,this.lowerLimitOnSubSeqSync,null,null).subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){return 0==e.Success?(this._toaster.showInfo("Employee Data Sync Status - "+e.Success,""+e.Result,5e3),this.employeeSyncLoading=!1,this.lowerLimitOnSubSeqSync=0,this.lowerLimitOnSubSeqSyncPayloadSize=0,e.Result):e.Success&&0!=e.Size?(console.log("Sync Progress for Endpoint postToUsers - "+(Math.ceil(this.lowerLimitOnSubSeqSync/this.lowerLimitOnSubSeqSyncPayloadSize*100)>100?100:Math.ceil(this.lowerLimitOnSubSeqSync/this.lowerLimitOnSubSeqSyncPayloadSize*100))),this.lowerLimitOnSubSeqSyncPayloadSize=e.total_payload_size,this.lowerLimitOnSubSeqSync+=this.onSyncBatchSize,yield this.employeeSync(),e.Result):(this._toaster.showSuccess("Employee Data Sync Status - "+e.Success,""+e.Result,5e3),this.employeeSyncLoading=!1,this.lowerLimitOnSubSeqSync=0,this.lowerLimitOnSubSeqSyncPayloadSize=0,e.Result)})),e=>{this.employeeSyncLoading=!1,this.lowerLimitOnSubSeqSync=0,this.lowerLimitOnSubSeqSyncPayloadSize=0,this._toaster.showInfo("Employee Data Sync Status - processing","Employee Data Sync Inprogress",5e3)})}))}postApiHit(){return Object(r.c)(this,void 0,void 0,(function*(){this.isLoadingEndpoint=!0,console.log("Field Mapping Flag : ",this.fieldMappingFlag),console.log("clicked_source_id : ",this.clicked_source_id),this.api.getSourceList(this.currentConnectionId).pipe(Object(h.a)(()=>{if(console.log("Endpoint onInit source list : ",this.clicked_source_resource," is it present in this.sourceResource_List_array ? : ",this.sourceResource_List_array.includes(this.clicked_source_resource)),this.sourceResource_List_array.includes(this.clicked_source_resource)){let e=[this.currentConnectionId,this.clicked_source_resource.replace("/","")];console.log("Source Index in RUN API page : ",this.clicked_source_resource),this.api.getConnectionItem(this.clicked_source_id).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{let t,n;if(console.log("Connection item response for id : ",this.clicked_source_id," is ~ ",e),this.currentConnectionItem=e,this.currentSourceDetails=e,console.log("Current Source Details : ",this.currentSourceDetails),t=e[0].destination_url,n=e[0].source_url,this.api.getQueryParamsKeyValuePair(this.currentSourceDetails[0]).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{if(1==e.Success){this.currentQueryParamsPairValues=JSON.parse(e.Result),console.log(`Retrieved Query Params data for Connection ID ${this.clicked_source_id} : ${JSON.stringify(this.currentQueryParamsPairValues)}`),this.queryFieldMapping.get("itemRows");for(let e=0;e<this.currentQueryParamsPairValues.length;e++)this.onQueryAddRow(e,this.currentQueryParamsPairValues[e]);this.onQueryRemoveRow(this.currentQueryParamsPairValues.length)}else this.currentQueryParamsPairValues=[]}),"KEBS_QB"==this.systemName)if(t.includes("intuit.com")){let e;this.qbHeaderFlag=!0,this.qbHeaderOutboundFlag=!0,e=String(t).split("/"),e=e[e.length-2],console.log("splitDestUrl : ",e),this.qbHeaderName=e,this.api.getQBEntityName(this.qbHeaderName).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{this.qbHeaderName=this.headerName+" - "+e,console.log("qbHeaderName on getQBEntityName call : ",this.qbHeaderName)})}else this.qbHeaderFlag=!1,this.qbHeaderOutboundFlag=!1;else if("Intuit"==this.systemName){let e;this.qbHeaderFlag=!0,this.qbHeaderOutboundFlag=!1,e=String(n).split("/"),e=e[e.length-2],console.log("splitSourceUrl : ",e),this.qbHeaderName=e,this.api.getQBEntityName(this.qbHeaderName).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{this.qbHeaderName=this.headerName+" - "+e,console.log("qbHeaderName on getQBEntityName call : ",this.qbHeaderName)})}else if("ZOHOBOOKS"==this.systemName){let e;this.qbHeaderFlag=!0,this.qbHeaderOutboundFlag=!1,e=String(n).split("?")[1],e=e.replace("organization_id=",""),console.log("splitSourceUrl : ",e),this.qbHeaderName=e,this.api.getQBEntityName(this.qbHeaderName).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{this.qbHeaderName=this.headerName+" - "+e,console.log("qbHeaderName on getQBEntityName call : ",this.qbHeaderName)}),"getZBReports"==this.headerName&&(this.isZBVoucherTypesTabVisible=!0,this.asyncTabs.subscribe(e=>{if(!e.some(e=>"Voucher Types"===e.label)){const t=[...e,{label:"Voucher Types",content:5}];this.asyncTabs=new ct.a(e=>{e.next(t),e.complete()})}}),this.api.getZBVoucherTypes().pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{"S"==e.messType&&(this.availableZBReportVoucherTypes=e.data)}))}else if("KEBS_ZB"==this.systemName)if(t.includes("books.zoho")){let e;this.qbHeaderFlag=!0,this.qbHeaderOutboundFlag=!0,e=String(t).split("?")[1],e=e.replace("organization_id=",""),console.log("splitDestUrl : ",e),this.qbHeaderName=e,this.api.getQBEntityName(this.qbHeaderName).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{this.qbHeaderName=this.headerName+" - "+e,console.log("qbHeaderName on getQBEntityName call : ",this.qbHeaderName)})}else this.qbHeaderFlag=!1,this.qbHeaderOutboundFlag=!1;else this.qbHeaderFlag=!1,this.qbHeaderOutboundFlag=!1;this.url=e[0].source_url,this.body.url=this.url,console.log("URL FOR : ",this.clicked_source_resource," with clicked_source_id of : ",this.clicked_source_id," is : ",this.body.url," Has response : ",e)}),this.api.getQueryParams(e).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{this.queryParamsKeyFields=e,this.queryParamsKeyFields=this.queryParamsKeyFields.toString(),this.queryParamsKeyFields=this.queryParamsKeyFields.split(","),console.log("Query Parameters Key fields : ",this.queryParamsKeyFields)})}else this.qbHeaderFlag=!1,this.qbHeaderOutboundFlag=!1,this.api.getBaseProduct(this.clicked_source_resource).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{console.log("clicked_source_resource inside postApiHit else part : ",this.clicked_source_resource," with clicked_source_id : ",this.clicked_source_id),console.log("Res inside else of postapihit : ",e),this.url=window.location.origin+"/api/integrationLayer/"+e[0],this.queryParams="",this.queryParamsKeyFields="",this.body.url=this.url});this.isLoadingEndpoint=!1})).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{if(this.sourceResource_List_array=e,console.log("sourceResource_List_array Resource List inside postAPIhit : ",this.sourceResource_List_array),"Intuit"===this.systemName){console.log("this.systemName inside modifiedResourceListArray : ",this.systemName,"sourceResource_List_array : "+this.sourceResource_List_array);let e=[];for(let t of this.sourceResource_List_array)e.push("/"+String(t).split("/").pop());this.sourceResource_List_array=e,console.log("this.sourceResource_List_array modified : ",this.sourceResource_List_array)}else if("ZOHOBOOKS"===this.systemName){console.log("this.systemName inside modifiedResourceListArray : ",this.systemName,"sourceResource_List_array : "+this.sourceResource_List_array);let e=[];for(let t of this.sourceResource_List_array)e.push(String(t).split("?")[0]);this.sourceResource_List_array=e,console.log("this.sourceResource_List_array modified : ",this.sourceResource_List_array)}}),console.log("Type of clicked_source_resource : ",typeof this.clicked_source_resource," with value : ",this.clicked_source_resource),this.api.getAppId({connection_id:this.currentConnectionId}).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{console.log("Response getAppId : ",e),this.appIdFlag=e.Success,this.appIdFlag&&(this.app_ID=e.Result),console.log("Response getAppId flag : ",this.appIdFlag)})}))}testAPI(e){return Object(r.c)(this,void 0,void 0,(function*(){this.isLoadingEndpoint=!0,this.clickedIndex=this.sourceResource_List[e][1],this.current_src_id=this.sourceResource_List[e][0],this.clicked_source_resource=this.clickedIndex,this.clicked_source_id=this.current_src_id,this.headerName=this.clicked_source_resource.replace("/",""),this.syncFlag=!1,this.authenticationSubmitFlag=!1,this.querySubmitFlag=!1,this.voucherTypesAppliedFlag=!1,this.voucherTypesFieldMapping.reset(),this.appliedVoucherTypesforZB=[],this.asyncTabs.subscribe(e=>{const t=e.filter(e=>"Voucher Types"!==e.label);this.asyncTabs=new ct.a(e=>{e.next(t),e.complete()})}),this.queryFieldMapping.reset(),this.authenticationKey=null,this.authenticationValue=null,"Intuit"!=this.systemName&&"KEBS_ZB"!=this.systemName&&"ZOHOBOOKS"!=this.systemName&&"KEBS_QB"!=this.systemName||this.authenticationFieldMapping.controls.value.setValue(""),console.log("Clicked Resource on testAPI : ",this.clicked_source_resource," , at index : ",e," with source_id : ",this.clicked_source_id),yield this.postApiHit().then(()=>{console.log("Current url testAPI : ",this.url),this.sampleResponse=[]})}))}onIntegAPI(){return Object(r.c)(this,void 0,void 0,(function*(){this.isLoadingEndpoint=!0,this.clickedIndex=1==this.sourceResource_List.length?this.sourceResource_List[0][0]:-1,this.clickedIndex=-1==this.clickedIndex?this.systemName:this.sourceResource_List[0][1],this.current_src_id=-1==this.clickedIndex?"":this.current_src_id,this.clicked_source_resource=this.clickedIndex,this.clicked_source_id=this.current_src_id,this.syncFlag=!1,"Intuit"!=this.systemName&&"KEBS_ZB"!=this.systemName&&"ZOHOBOOKS"!=this.systemName&&"KEBS_QB"!=this.systemName||this.authenticationFieldMapping.controls.value.setValue(""),this.headerName=this.clicked_source_resource.replace("/",""),console.log("Clicked onIntegAPI : "," source resource : ",this.clicked_source_resource," , at index : ",this.clicked_source_id),yield this.postApiHit().then(()=>{console.log("Current url onIntegAPI : ",this.url),this.sampleResponse=[]})}))}clientID_Change(e){this.OAuthInputDetails.clientID=e.target.value.toString()}clientsecret_Change(e){this.OAuthInputDetails.clientSecret=e.target.value.toString()}clientID_SAP_Change(e){this.OAuthSAPInputDetails.clientID=e.target.value.toString()}private_key_SAP_Change(e){this.OAuthSAPInputDetails.private_key=e.target.value.toString()}user_id_SAP_Change(e){this.OAuthSAPInputDetails.user_id=e.target.value.toString()}company_id_SAP_Change(e){this.OAuthSAPInputDetails.company_id=e.target.value.toString()}setUrl(e){this.url=e.target.value,this.body.url=this.url,console.log("current url : ",this.url),console.log("current body : ",this.body)}setQueryParams(e){this.queryParams=e;let t=[];t.push(this.queryParams),console.log("on query submit : ",this.queryParams),console.log("After slicing 1st element from query Params : ",t);let n=!0;if(null!=this.currentQueryParamsPairValues&&0!=this.currentQueryParamsPairValues.length){console.log("QueryParams Comparision section....");let i=JSON.stringify(this.currentQueryParamsPairValues);i=JSON.parse(i);for(let e of i)e.value="="+e.value;if(console.log("After Modifying QueryParams : ",i),t[0].length==i.length){console.log("Same QueryParams Length found...");for(let e in t[0])if(console.log("Comparision Query on iteration ",e," => ",t[0][e]),t[0][e].key!=i[e].key||t[0][e].value!=i[e].value){n=!1,console.log("Not Matching New QueryParams Data : ",t[0][e].key," => ",t[0][e].value),console.log("Not Matching Existing QueryParams Data : ",i[e].key," => ",i[e].value);break}}else n=!1;0==n&&(console.log("Updating Query Params if Not Matching..."+JSON.stringify(e)),this.api.saveQueryParams(e,this.currentSourceDetails[0]).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{}))}console.log("This log should not be captured on Update when query params not matching"),0==this.currentQueryParamsPairValues.length&&(console.log("Saving QueryParams first time...."),this.api.saveQueryParams(e,this.currentSourceDetails[0]).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{})),this.queryParams.length>0&&(this.queryCalendarFlag=!0);for(var i=0;i<e.length;i++)0==i&&this.url.includes("?")?this.url+="&"+e[i].key+e[i].value:this.url+=0==i?"?"+e[i].key+e[i].value:"&"+e[i].key+e[i].value;this.body.url=this.url,console.log("Appended Query Parames at iteration ",i," : ",this.body.url),this.queryKeyPairInitState=[]}setHeaders(e){this.headers=e,console.log("on Header submit : ",this.headers),this.headerKeyPairInitState=[]}setResponse(e){return Object(r.c)(this,void 0,void 0,(function*(){return this.tempResponse=e,this.response=e,console.log("on respose Submit : ",this.response),void 0===this.response.errorStatus&&void 0===this.response.errno&&void 0===this.response.error_type_name?this._toaster.showSuccess("Data Received",this.url,5e3):this._toaster.showError(null!=this.response.errno?"Error Status : "+this.response.errorStatus+"\nError Description : "+this.response.errno:null!=this.response.errorStatus?"Error Status : "+this.response.code+"\nError Description : "+this.response.errorStatus:"Error Status : "+this.response.code+"\nError Description : "+this.response.error_type_name,this.url,5e3),!0}))}generateKeys(e){return Object(r.c)(this,void 0,void 0,(function*(){this.sampleResponse=[];let t,n,i=0;if((null==e?void 0:e.length)>10)for(let[o,s]of Object.entries(e)){if(!(i<10))break;this.sampleResponse.push(s),i++}else this.sampleResponse=[e];if(this.source_field=[],void 0===this.sampleResponse.errorStatus&&void 0===this.sampleResponse.errno&&void 0===this.sampleResponse.error_type_name){if(null!=e.length)if(0==e.length)n=[];else{console.log("Source Data : ",e," source length : ",e.length);let i=0;for(let n in e)i<Object.values(e[n]).length&&(i=Object.values(e[n]).length,t=n);n=Object.keys(e[t]),console.log("Source Field Keys : ",n)}else{console.log("Source Data : ",e," source length : ",e.length);let i=0;for(let[n,o]of Object.entries(e))i<Object.values(e[n]).length&&(i=Object.values(e[n]).length,t=n);n=Object.keys(e[t]),console.log("Source Field Keys : ",n)}for(let e of n)this.source_field.push(e);console.log("source data : ",e),console.log("Source Field  : ",this.source_field)}return!0}))}setBody(){try{return this.bodyTemp=JSON.parse(this.bodyTemp),!0}catch(e){return alert("Invalid JSON Body"),!1}}setAuthentication(e,t){this.authenticationKey=t,this.authenticationValue=e,this.authentication=[{key:t,value:e}],console.log("on authentication Submit before converting into Object : ",this.authentication),this.authentication=this.helpers.convertKeyValueToObject(this.authentication),console.log("on authentication Submit after converting into Object : ",this.authentication)}queryGetControls(){return this.queryFieldMapping.get("itemRows").controls}headerGetControls(){return this.headerFieldMapping.get("itemRows").controls}onQueryRemoveRow(e){this.queryParamsControl=this.queryFieldMapping.controls.itemRows,console.log("Control length : ",this.queryParamsControl.length),this.queryParamsControl.length<=1?(this.queryParamsControl.clear(),this.onQueryAddRow(0)):(this.queryParamsControl.removeAt(e),console.log("Query Fields afer deleting rows : ",this.queryFieldMapping.value))}onHeaderRemoveRow(e){const t=this.headerFieldMapping.controls.itemRows;t.length<=1?this.headerFieldMapping.reset():(t.removeAt(e),console.log("Header Fields afer deleting rows : ",this.headerFieldMapping.value))}queryKeyField(e){this.queryKey=e,console.log(this.queryKey," type : ",typeof this.queryKey)}queryValueField(e,t){this.queryValue=e.target.value,this.queryParamsControl.value[t].value=this.queryValue,console.log(this.queryValue)}headerKeyField(e){this.headerKey=e.target.value,console.log(this.headerKey)}headerValueField(e){this.headerValue=e.target.value,console.log(this.headerValue)}bodyKeyField(e){this.bodyTemp=e.target.value,console.log("Change in Body text area event : ",this.bodyTemp)}onOptionChange(e){this.authenticationSubmitFlag=!1,this.currentAuthenticationOption=e,"OAuth 2.0"==this.currentAuthenticationOption?this.api.getOAuthDetails({source_id:this.clicked_source_id}).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{this.OAuthInput=e,console.log("OAuth Details : ",this.OAuthInput),this.OAuthInput.Success&&(this.client_id_oauth_2_0.setValue(this.OAuthInput.Result.clientID),this.client_secret_oauth_2_0.setValue(this.OAuthInput.Result.clientSecret)),this.getQuickBookToken()}):"OAuth SAP"==this.currentAuthenticationOption?this.api.getSAMLAssertionAccessToken({source_id:this.clicked_source_id}).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{if(this.OAuthInputSAP=e,console.log("OAuthInputSAP Details : ",this.OAuthInputSAP),console.log("response on getSAMLAssertionAccessToken : ",e),!(this.OAuthInputSAP.Result.clientID&&this.OAuthInputSAP.Result.user_id&&this.OAuthInputSAP.Result.company_id&&this.OAuthInputSAP.Result.private_key))return this._toaster.showWarning("Authentication details can't be empty !","Kindly give valid details to generate access token ",3e3);null!=e.access_token?(this.authenticationFieldMapping.patchValue({value:e.access_token,key:"Bearer"}),this.client_id_oauth_sap.setValue(e.Result.clientID),this.user_id_oauth_sap.setValue(e.Result.user_id),this.company_id_oauth_sap.setValue(e.Result.company_id),this.private_key_oauth_sap.setValue(e.Result.private_key),console.log("AuthenticationFieldMapping Token Value : ",this.authenticationFieldMapping.value)):console.log("OAuthInputSAP else response no access_token Details : ",this.OAuthInputSAP)}):"Basic"==this.currentAuthenticationOption&&this.api.getBasicAuth({connection_id:this.currentConnectionId}).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{this.resultBasic=e,this.resultBasic.Success&&this.authenticationFieldMapping.patchValue({key:this.resultBasic.Result.user_name,value:this.resultBasic.Result.password})}),console.log("Change in Authentication Option : ",this.currentAuthenticationOption),this.authenticationFieldMapping.reset()}authenticationKeyField(e){this.authenticationKey=e.target.value,console.log(this.authenticationKey)}authenticationValueField(e){this.authenticationValue=e.target.value,console.log(this.authenticationValue)}onQueryAddRow(e,t=null){null!=t&&""!=t&&this.queryFieldMapping.get("itemRows").at(e).patchValue(t),this.queryKey=null,console.log("Number : ",e),this.queryParamsControl=this.queryFieldMapping.controls.itemRows,console.log("data : added : ",this.queryParamsControl.value[e]),this.queryParamsControl.push(new c.m({key:new c.j("",c.H.required),value:new c.j("",c.H.required)}))}onHeaderAddRow(e){const t=this.headerFieldMapping.controls.itemRows;console.log("data : added : ",t.value[e]),t.push(new c.m({key:new c.j("",c.H.required),value:new c.j("",c.H.required)}))}onSubmit(){this.querySubmitFlag=!1;let e=this.queryFieldMapping.controls.itemRows;for(let t of e.value)console.log("data on submit query params : ",t),console.log("data.value on submit query params : ",t.value),console.log("data.key on submit query params : ",t.key),"$"!=t.key&&"="!=t.value&&null!=t.key&&null!=t.value&&String(t.key).includes("date")&&(t.value=Le(t.value).format("YYYY-MM-DD")),t.value="="+t.value,this.queryKeyPairInitState.push(t);this.setQueryParams(this.queryKeyPairInitState),this.querySubmitFlag=!0,e.clear(),this.onQueryAddRow(0)}onHeaderSubmit(){let e=this.headerFieldMapping.controls.itemRows;for(let t of e.value)null!=t.key&&null!=t.value&&""!=t.key.replace(/ /g,"")&&""!=t.value.replace(/ /g,"")&&this.headerKeyPairInitState.push(t);this.setHeaders(this.headerKeyPairInitState),this.headerFieldMapping.reset()}onBodySubmit(){this.BodyFlag=this.setBody()}onAuthenticationSubmit(){return Object(r.c)(this,void 0,void 0,(function*(){let e=this.authenticationFieldMapping.controls;if(console.log("Auth Details : ",this.authenticationFieldMapping.value),"APIKey"==this.currentAuthenticationOption){if(!e.value.value||!e.key.value)return this._toaster.showWarning("Authentication details can't be empty !","Kindly give valid authentication details",3e3);this.authenticationSubmitFlag=!1,this.setAuthentication(e.value.value,e.key.value),this.authenticationSubmitFlag=!0}else if("Bearer"==this.currentAuthenticationOption){if(!e.value.value)return this._toaster.showWarning("Authentication details can't be empty !","Kindly give valid authentication details",3e3);this.authenticationSubmitFlag=!1,this.setAuthentication("Bearer "+e.value.value,"Authorization"),this.authenticationSubmitFlag=!0}else if("OAuth 2.0"==this.currentAuthenticationOption){if(!e.value.value)return this._toaster.showWarning("Authentication details can't be empty !","Kindly give valid authentication details",3e3);this.setAuthentication("Bearer "+e.value.value,"Authorization"),console.log("OAuth 2.0 authentication submitted"),this.authenticationSubmitFlag=!0}else if("OAuth SAP"==this.currentAuthenticationOption){if(!e.value.value)return this._toaster.showWarning("Authentication details can't be empty !","Kindly give valid authentication details",3e3);this.setAuthentication("Bearer "+e.value.value,"Authorization"),console.log("OAuth SAP authentication submitted"),this.authenticationSubmitFlag=!0}else if("Basic"==this.currentAuthenticationOption){if(!e.value.value||!e.key.value)return this._toaster.showWarning("Authentication details can't be empty !","Kindly give valid authentication details",3e3);let t={};if(this.authenticationSubmitFlag=!1,this.resultBasic.Success&&this.resultBasic.Result.user_name==e.key.value&&this.resultBasic.Result.password==e.value.value){if(!e.value.value||!e.key.value)return this._toaster.showWarning("Authentication details can't be empty !","Kindly give valid authentication details",3e3);let t=btoa(e.key.value+":"+e.value.value);this.setAuthentication("Basic "+t,"Authorization"),this.authenticationSubmitFlag=!0}else{console.log("New login Credentials...!"),t.connection_id=this.currentConnectionId,t.user_name=e.key.value,t.password=e.value.value,this.api.postBasicAuth({data:t}).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{});let n=btoa(e.key.value+":"+e.value.value);this.setAuthentication("Basic "+n,"Authorization"),this.authenticationSubmitFlag=!0}}}))}batchProcessing(e){return Object(r.c)(this,void 0,void 0,(function*(){let t=this.tempResponse.splice(this.index,this.batch_size);if(console.log("This response length in batch processing : ",this.response.length,"tempResponse array being sliced len : ",this.tempResponse.length,"Sliced array size ",t.length,"field batch mapping size : ",this.field_map_response.length),0==t.length)return console.log("Sliced array size ",t.length,"tempResponse array being sliced len : ",this.tempResponse.length,"final mapping size : ",this.field_map_response.length),this.setResponse(this.field_map_response),console.log("Response from batchProcessing : ",this.response),this.generateKeys(this.response),console.log("Index else : ",this.index),this.first_limit=0,this.syncFlag=!0,this.newApiHit=!0,this.responseProcess=!0,this.hitapi.get("url").enable(),!0;this.api.sf_api_hit({option:e,body:t}).pipe(Object(h.a)(()=>Object(r.c)(this,void 0,void 0,(function*(){console.log("Batch processing response length : ",this.response.length,"tempResponse array being sliced len : ",this.tempResponse.length),yield this.batchProcessing(e)})))).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{this.batchresponse=null!=e.response?e.response:[],this.field_map_response.push(...e.response)})}))}qbBatchProcessing(e){var t;return Object(r.c)(this,void 0,void 0,(function*(){let n=this.tempResponse.splice(this.index,1);if(console.log("Temp Data : ",n),0==n.length)return this.setResponse(this.field_map_response),console.log("Response from batchProcessing : ",this.response),this.generateKeys(this.response),console.log("Index else : ",this.index),this.first_limit=0,this.syncFlag=!0,this.newApiHit=!0,this.responseProcess=!0,this.hitapi.get("url").enable(),!0;if(null!=n[0].ColData){let i=0;for(let e of n[0].ColData)if(e.value.length>0&&null!=(null===(t=e.value)||void 0===t?void 0:t.length)){i+=1;break}0==i?this.api.qb_api_hit({option:e,body:{rows:this.single_entity,columns:this.tempResponseHeader}}).pipe(Object(h.a)(()=>Object(r.c)(this,void 0,void 0,(function*(){this.qb_processed_data_length+=this.single_entity.length,console.log("Batch processed data length of ",this.qb_processed_data_length," remaining in payload ",this.tempResponse.length),this.single_entity=[],yield this.qbBatchProcessing(e)})))).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{console.log("Batch processing response length of ",this.single_entity.length," remaining  in payload ",this.tempResponse.length),this.batchresponse=null!=e.response?e.response:[],this.field_map_response.push(...e.response)}):(this.single_entity.push(...n),console.log("this.single_entity : ",this.single_entity),yield this.qbBatchProcessing(e))}else this.api.qb_api_hit({option:e,body:{rows:this.single_entity,columns:this.tempResponseHeader}}).pipe(Object(h.a)(()=>Object(r.c)(this,void 0,void 0,(function*(){this.qb_processed_data_length+=this.single_entity.length,console.log("Batch processed data length of ",this.qb_processed_data_length," remaining in payload ",this.tempResponse.length),this.single_entity=[],yield this.qbBatchProcessing(e)})))).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{console.log("Batch processing response length of ",this.single_entity.length," remaining  in payload ",this.tempResponse.length),this.batchresponse=null!=e.response?e.response:[],this.field_map_response.push(...e.response)})}))}handleOnInputSend(){return Object(r.c)(this,void 0,void 0,(function*(){if("getZBReports"===this.headerName){if(console.log("Selected Voucher Types for ZB sync Retrieveing Data from Source : ",this.appliedVoucherTypesforZB),!(this.appliedVoucherTypesforZB&&this.appliedVoucherTypesforZB.length>0))return this.responseProcess=!0,this._toaster.showWarning("No voucher types selected for ZohoBooks sync !","Kindly select the voucher type to proceed !",5e3);this.body.uniqueVoucherTypes=this.appliedVoucherTypesforZB}this.hitapi.get("url").disable(),this.newApiHit?(this.response=[],this.body.url=this.url,Object.assign(this.body,{newApiHit:!0})):Object.assign(this.body,{newApiHit:!1}),this.syncFlag=!1,this.responseProcess=!1,this.changeVoucherStatusFlag=!1,Object.assign(this.body,{method:this.reqMethod}),"APIKey"===this.currentAuthenticationOption?(Object.assign(this.body,{APIKey:!0}),Object.assign(this.body,{key:this.authenticationKey}),Object.assign(this.body,{value:this.authenticationValue})):(Object.assign(this.body,{APIKey:!1}),Object.assign(this.body,{key:this.authenticationKey}),Object.assign(this.body,{value:this.authenticationValue})),this.BodyFlag?(Object.assign(this.body,{body:this.bodyTemp}),console.log("on Body Submit : ",this.body," and it type : ",typeof this.body)):(this.bodyTemp={},Object.assign(this.body,{body:this.bodyTemp})),Object.assign(this.body,{endpoints:this.sourceResource_List}),Object.assign(this.body,{first_limit:this.first_limit}),Object.assign(this.body,{connection_id:this.currentConnectionId});let e,t=JSON.stringify(this.body);console.log("Body : ",this.body),console.log("http method",this.reqMethod),console.log("headers",this.headers),console.log("query params ",this.queryParams),console.log("body ",t);try{e=JSON.parse(t),console.log("data body : ",e)}catch(n){alert("Something is wrong with the JSON data."+n)}try{console.log("header : ",this.helpers.convertKeyValueToObject(this.headers)),console.log("this.authentication : ",this.authentication),null!==this.authentication.null&&"Bearer null"!==this.authentication.Authorization&&"Basic bnVsbDpudWxs"!==this.authentication.Authorization||(console.log("Inside "),this.authentication={});let t=this.helpers.convertKeyValueToObject(this.queryParams),n=Object.assign(this.helpers.convertKeyValueToObject(this.headers),this.authentication),i="postApiHit";this.authorization=void 0,this.csrf_token=void 0,"getZBReports"===this.headerName?(this.sampleResponse=[],this.response=[],this.api.getZBReportsData(i,e,t,n).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){if("S"===e.messType){this.responseProcess=!0,this.response.push(e.data),this.tempResponse=Object.assign([],this.response);let t=0,n={},i=Object.keys(this.response[0]);T.each(i,e=>{n[e]=[]});for(let e of Object.keys(this.response[0]))if(t+=this.response[0][e].length,this.response[0][e].length>10){let t=0;for(let i of this.response[0][e]){if(!(t<10))break;n[e].push(i),t++}}else n[e].push(...this.response[0][e]);return console.log("Response : ",this.response[0]," with length : ",t),this.newApiHit=!1,this.sampleResponse=[n],this.hitapi.get("url").enable(),t&&(this.syncFlag=!0),this._toaster.showSuccess("Data Retrieved Successfully !","",5e3)}if("E"===e.messType)return this.responseProcess=!0,this.response.push(e.error),this.tempResponse=Object.assign([],this.response),console.log("Error on Response : ",this.response[0]),this.response=this.response[0],this.newApiHit=!1,this.sampleResponse=this.response,this.hitapi.get("url").enable(),this._toaster.showError("Error Status : \nError Description : "+this.response.msg,this.url,5e3)})))):this.api.postSourceApiCall(i,e,t,n).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(t=>Object(r.c)(this,void 0,void 0,(function*(){var n,i,o,s,a,r,l,c,d,p,h;if(!1===t.flag)if(null!=t["x-csrf-token"]?this.csrf_token=t["x-csrf-token"]:this.authorization=t.authorization,this.response.push(...t.response),this.tempResponse=Object.assign([],this.response),console.log("Response : ",this.response," with length : ",this.response.length),this.newApiHit=!1,console.log("NEXT URL AVAILABLE : ",this.body.url," with response length : ",this.response.length),console.log("response : ",t),null!=t.next_url&&null!=t.next_url)"number"==typeof t.next_url?(this.first_limit+=t.next_url,this.responseProcess=!1,this.newApiHit=!1,yield this.handleOnInputSend()):(this.body.url=t.next_url,this.responseProcess=!1,this.newApiHit=!1,yield this.handleOnInputSend());else if(null!=t.field_map&&null!=t.field_map){let n=!1;t.field_map&&(this.field_map_response=[],this.batchresponse=["0"],n=yield this.batchProcessing(e))}else if(null!=t.qb_field_map&&null!=t.qb_field_map){let n=!1;t.qb_field_map&&(this.field_map_response=[],this.batchresponse=["0"],this.qb_processed_data_length=0,this.tempResponse=Object.assign([],this.response[0].Rows.Row),this.tempResponseHeader=Object.assign([],this.response[0].Columns.Column),n=yield this.qbBatchProcessing(e))}else this.body.url=this.url,void 0!==(null===(i=null===(n=this.response)||void 0===n?void 0:n[0])||void 0===i?void 0:i.errno)||void 0!==(null===(s=null===(o=this.response)||void 0===o?void 0:o[0])||void 0===s?void 0:s.errorStatus)||void 0!==(null===(r=null===(a=this.response)||void 0===a?void 0:a[0])||void 0===r?void 0:r.error_type_name)||(null===(c=null===JSON||void 0===JSON?void 0:JSON.stringify(null===(l=this.response)||void 0===l?void 0:l[0]))||void 0===c?void 0:c.includes("error_type"))?JSON.stringify(this.response[0]).includes("voucher_no")||JSON.stringify(this.response[0]).includes("invoice_no")?(this.setResponse(this.response),this.generateKeys(this.response),this.syncFlag=!1,this.changeVoucherStatusFlag=!0):(this.setResponse(this.response[0]),this.generateKeys(this.response),this.syncFlag=!1):"0"==(null===(d=this.response)||void 0===d?void 0:d[0])||"{}"==(null===JSON||void 0===JSON?void 0:JSON.stringify(null===(p=this.response)||void 0===p?void 0:p[0]))||"[]"==(null===JSON||void 0===JSON?void 0:JSON.stringify(null===(h=this.response)||void 0===h?void 0:h[0]))||"{}"==(null===JSON||void 0===JSON?void 0:JSON.stringify(this.response))||"[]"==(null===JSON||void 0===JSON?void 0:JSON.stringify(this.response))?(this.setResponse(this.response),this.generateKeys(this.response),this.syncFlag=!1):(this.setResponse(this.response),this.generateKeys(this.response),this.syncFlag=!0),this.first_limit=0,this.responseProcess=!0,this.newApiHit=!0,this.hitapi.get("url").enable();null==t.flag&&(void 0===t.errorStatus&&void 0===t.errno&&void 0===t.error_type_name?(this.setResponse(t),this.syncFlag=!0,this.hitapi.get("url").enable()):(this.setResponse(t),this.syncFlag=!1,this.hitapi.get("url").enable()),this.generateKeys(t),this.first_limit=0,this.newApiHit=!0,this.responseProcess=!0,this.hitapi.get("url").enable()),this.responseStatus=this.response.status,this.fieldMappingFlag=!1})),e=>{this.hitapi.get("url").enable(),console.log("oops",e),this.responseProcess=!0,this.newApiHit=!0,this._toaster.showError(`Error On Post API ${e.status} - ${e.statusText}`,"Error Info : "+e.error+" Error On URl : "+e.url,5e3)})}catch(n){this.hitapi.get("url").enable(),console.log(n)}}))}onFieldMapping(){this.api.getSyncFunction({url:this.url}).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){this.getSyncUrlEndpoint=e,console.log("Get Sync URL : ",this.getSyncUrlEndpoint),console.log("Field Mapping Flag : ",this.fieldMappingFlag),console.log("response.status : ",this.responseStatus);let t=Object.keys(this.getSyncUrlEndpoint)[0],n=Object.values(this.getSyncUrlEndpoint)[0];if(console.log("Key : ",t," and Value : ",n),!this.url.includes(t)||this.responseStatus)return this._toaster.showError("Sync Failed",null==e?void 0:e.msg,5e3),this.syncFlag=!1,this.onSyncFlag=!1,this.hitapi.get("url").enable(),!1;this.dialog.open(lt,{height:"100%",width:"100%",minWidth:"90vw",maxWidth:"90vw",position:{right:"0px"},data:{Response:this.response,sourceURL:this.url,source_key:this.source_field,clicked_source_id:this.clicked_source_id}}).afterClosed().subscribe(e=>{this.response=e.data.result,this.generateKeys(this.response),this.fieldMappingFlag=e.data.flag,console.log("Field Mapping Flag : ",this.fieldMappingFlag),console.log("Final Response : ",e.data.result)})})))}spliceBatchJournalAccountItem(e,t,n,i){var o,s;return Object(r.c)(this,void 0,void 0,(function*(){if("journals"==n){this.errorOnSyncJournalAccountFlag=!1;let s=!1;this.api.postOnSync({url:this.url,response:e,fieldMappingFlag:this.fieldMappingFlag,connection_id:this.currentConnectionId,"x-csrf-token":this.csrf_token,source_id:this.clicked_source_id,authorization:this.authorization,current_run_id:null===(o=this.getSyncUrlEndpoint)||void 0===o?void 0:o.run_id,processed_journals:i}).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(o=>Object(r.c)(this,void 0,void 0,(function*(){var a;return console.log("Data insertion with response : ",o),0==o.Success?(this.errorOnSyncJournalAccountFlag=!0,this._toaster.showError("Error On Sync","Error Info : "+o.Result,5e3),console.log("Error Inside spliceBatchJournalAccount 1 : ",o),this.sampleResponse=[],this.sampleResponse.push(o),this.syncFlag=!1,this.onSyncFlag=!1,this.hitapi.get("url").enable(),!1):null==o.Success?(this.errorOnSyncJournalAccountFlag=!0,this._toaster.showError("Error On Sync","Error Info : "+o,5e3),console.log("Error Inside spliceBatchJournalAccount 2 : ",o),this.sampleResponse=[],this.sampleResponse.push(o),this.syncFlag=!1,this.onSyncFlag=!1,this.hitapi.get("url").enable(),!1):(i=null===(a=null==o?void 0:o.postToDestinationResult)||void 0===a?void 0:a.processed_journals,this.onSyncJournalAccountIter<this.syncJorunalAccountCount?(this.onSyncJournalAccountIter+=1,e[0].same_journal=!0,e[0].line_items=t.splice(0,this.batchSpliceZbLineItemSize),console.log(`${this.onSyncJournalAccountIter} out of ${this.syncJorunalAccountCount} account line item is synced for ZB Journal - ${this.onSyncIter} out of ${this.syncCount}`),yield this.spliceBatchJournalAccountItem(e,t,n,i),void console.log("Data batch synced")):(console.log(`${this.onSyncJournalAccountIter} out of ${this.syncJorunalAccountCount} account line item  is synced for ZB Journal - ${this.onSyncIter} out of ${this.syncCount}`),s=!0,yield this.spliceBatchSplice(this.sliceSampleResponse,n,i),!0))})),e=>(console.log("oops",e),this.errorOnSyncJournalAccountFlag=!0,this.syncFlag=!1,this.onSyncFlag=!1,this.hitapi.get("url").enable(),this._toaster.showError(`Error On Sync ${e.status} - ${e.statusText}`,"Error Info : "+e.error+" Error On URl : "+e.url,5e3),!1))}else{this.errorOnSyncJournalAccountFlag=!1;let o=!1;this.api.postOnSync({url:this.url,response:e,fieldMappingFlag:this.fieldMappingFlag,connection_id:this.currentConnectionId,"x-csrf-token":this.csrf_token,source_id:this.clicked_source_id,authorization:this.authorization,current_run_id:null===(s=this.getSyncUrlEndpoint)||void 0===s?void 0:s.run_id,processed_journals:i}).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(s=>Object(r.c)(this,void 0,void 0,(function*(){var a;return console.log("Data insertion with response : ",s),0==s.Success?(this.errorOnSyncJournalAccountFlag=!0,this._toaster.showError("Error On Sync","Error Info : "+s.Result,5e3),console.log("Error Inside spliceBatchJournalAccount 3 : ",s),this.sampleResponse=[],this.sampleResponse.push(s),this.syncFlag=!1,this.onSyncFlag=!1,this.hitapi.get("url").enable(),!1):null==s.Success?(this.errorOnSyncJournalAccountFlag=!0,this._toaster.showError("Error On Sync","Error Info : "+s,5e3),console.log("Error Inside spliceBatchJournalAccount 3 : ",s),this.sampleResponse=[],this.sampleResponse.push(s),this.syncFlag=!1,this.onSyncFlag=!1,this.hitapi.get("url").enable(),!1):(i=null===(a=null==s?void 0:s.postToDestinationResult)||void 0===a?void 0:a.processed_journals,this.onSyncJournalAccountIter<this.syncJorunalAccountCount?(this.onSyncJournalAccountIter+=1,e[0].same_journal=!0,e[0].account_transactions=t.splice(0,this.batchSpliceZbLineItemSize),console.log(`${this.onSyncJournalAccountIter} out of ${this.syncJorunalAccountCount} account line item is synced for ZB Journal - ${this.onSyncIter} out of ${this.syncCount}`),yield this.spliceBatchJournalAccountItem(e,t,n,i),void console.log("Data batch synced")):(console.log(`${this.onSyncJournalAccountIter} out of ${this.syncJorunalAccountCount} account line item  is synced for ZB Journal - ${this.onSyncIter} out of ${this.syncCount}`),o=!0,yield this.spliceBatchSplice(this.sliceSampleResponse,n,i),!0))})),e=>(console.log("oops",e),this.errorOnSyncJournalAccountFlag=!0,this.syncFlag=!1,this.onSyncFlag=!1,this.hitapi.get("url").enable(),this._toaster.showError(`Error On Sync ${e.status} - ${e.statusText}`,"Error Info : "+e.error+" Error On URl : "+e.url,5e3),!1))}}))}spliceBatchSplice(e,t,n){var i,o,s;return Object(r.c)(this,void 0,void 0,(function*(){if(!(e.length>0))return this.errorOnSyncJournalAccountFlag?(this._toaster.showError("Error On Sync ",this.url,5e3),this.syncFlag=!1,this.onSyncFlag=!1,this.hitapi.get("url").enable(),this.updateRunIdLogs("Error on sync for URL - "+this.url,this.url)):(this._toaster.showSuccess("Data Synced",this.url,5e3),this.syncFlag=!1,this.onSyncFlag=!1,this.hitapi.get("url").enable(),this.updateRunIdLogs("Data synced for URL - "+this.url,this.url)),!0;{this.syncFlag=!1;let a=!1;if("journals"!=t){for(this.onSyncJournalAccountIter=0,console.log(`current transaction_type - ${e[0].transaction_type} - entity_id - ${e[0].entity_id}  - entity_id - ${e[0].entity_id} - transaction_number - ${e[0].transaction_number}`);e.length>0&&"journal"==(null===(o=e[0])||void 0===o?void 0:o.transaction_type);)console.log(`Skipping transaction_type - ${e[0].transaction_type} - entity_id - ${e[0].entity_id}  - entity_id - ${e[0].entity_id} - transaction_number - ${e[0].transaction_number}`),this.sliceSampleResponse=this.sampleOnSyncResponse.splice(0,this.onSyncBatchSize),this.syncProgress=Math.ceil((this.onSyncIter+1)/this.syncCount*100),this.onSyncIter+=1,e=this.sliceSampleResponse;if(e.length>0){let i=Object.assign([],e[0].account_transactions);console.log("account_transactions on spliceBatchSplice : ",i);let o=i.splice(0,this.batchSpliceZbLineItemSize);return e[0].account_transactions=o,e[0].same_journal=!1,e[0].same_journal=!!(null==n?void 0:n.includes(null===(s=null==e?void 0:e[0])||void 0===s?void 0:s.entity_id)),console.log("spliceBatchSplice Batch Response : ",e),console.log("Field Mapping Flag : ",this.fieldMappingFlag),console.log("response.status : ",this.responseStatus),null!=i?this.onSyncIter<this.syncCount?(this.syncProgress=this.syncProgress>=100?0:Math.ceil((this.onSyncIter+1)/this.syncCount*100),console.log(`${this.onSyncIter} out of ${this.syncCount} is synced`),this.onSyncSliceJournalAccountSize=Object.values(i).length,this.syncJorunalAccountCount=Math.ceil(this.onSyncSliceJournalAccountSize/this.batchSpliceZbLineItemSize),this.sliceSampleResponse=this.sampleOnSyncResponse.splice(0,this.onSyncBatchSize),this.onSyncIter+=1,yield this.spliceBatchJournalAccountItem(e,i,t,n),!0):(this.errorOnSyncJournalAccountFlag?(this._toaster.showError("Error On Sync ",this.url,5e3),a=!0,this.syncFlag=!1,this.onSyncFlag=!1,this.hitapi.get("url").enable(),this.updateRunIdLogs("Error on sync for URL - "+this.url,this.url)):(this.syncProgress=this.syncProgress>=100?0:Math.ceil((this.onSyncIter+1)/this.syncCount*100),this._toaster.showSuccess("Data Synced",this.url,5e3),a=!0,this.syncFlag=!1,this.onSyncFlag=!1,this.hitapi.get("url").enable(),this.updateRunIdLogs("Data synced for URL - "+this.url,this.url)),!0):(this.errorOnSyncJournalAccountFlag?(this._toaster.showError("Error On Sync ",this.url,5e3),a=!0,this.syncFlag=!1,this.onSyncFlag=!1,this.hitapi.get("url").enable(),this.updateRunIdLogs("Error on sync for URL - "+this.url,this.url)):(this._toaster.showSuccess("Data Synced",this.url,5e3),a=!0,this.syncFlag=!1,this.onSyncFlag=!1,this.hitapi.get("url").enable(),this.updateRunIdLogs("Data synced for URL - "+this.url,this.url)),!0)}return this.errorOnSyncJournalAccountFlag?(this._toaster.showError("Error On Sync ",this.url,5e3),this.syncFlag=!1,this.onSyncFlag=!1,this.hitapi.get("url").enable(),this.updateRunIdLogs("Error on sync for URL - "+this.url,this.url)):(this._toaster.showSuccess("Data Synced",this.url,5e3),this.syncFlag=!1,this.onSyncFlag=!1,this.hitapi.get("url").enable(),this.updateRunIdLogs("Data synced for URL - "+this.url,this.url)),!0}{this.onSyncJournalAccountIter=0,console.log("current journal_id - "+e[0].journal_id);let o=Object.assign([],e[0].line_items);console.log("line_items on spliceBatchSplice : ",o);let s=o.splice(0,this.batchSpliceZbLineItemSize);if(e[0].line_items=s,e[0].same_journal=!1,e[0].same_journal=!!(null==n?void 0:n.includes(null===(i=null==e?void 0:e[0])||void 0===i?void 0:i.journal_id)),console.log("spliceBatchSplice Batch Response : ",e),null==o)return this.errorOnSyncJournalAccountFlag?(this._toaster.showError("Error On Sync ",this.url,5e3),a=!0,this.syncFlag=!1,this.onSyncFlag=!1,this.hitapi.get("url").enable(),this.updateRunIdLogs("Error on sync for URL - "+this.url,this.url)):(this._toaster.showSuccess("Data Synced",this.url,5e3),a=!0,this.syncFlag=!1,this.onSyncFlag=!1,this.hitapi.get("url").enable(),this.updateRunIdLogs("Data synced for URL - "+this.url,this.url)),!0;if(!(this.onSyncIter<this.syncCount))return this.errorOnSyncJournalAccountFlag?(this._toaster.showError("Error On Sync ",this.url,5e3),a=!0,this.syncFlag=!1,this.onSyncFlag=!1,this.hitapi.get("url").enable(),this.updateRunIdLogs("Error on sync for URL - "+this.url,this.url)):(this.syncProgress=this.syncProgress>=100?0:Math.ceil((this.onSyncIter+1)/this.syncCount*100),this._toaster.showSuccess("Data Synced",this.url,5e3),a=!0,this.syncFlag=!1,this.onSyncFlag=!1,this.hitapi.get("url").enable(),this.updateRunIdLogs("Data synced for URL - "+this.url,this.url)),!0;this.syncProgress=this.syncProgress>=100?0:Math.ceil((this.onSyncIter+1)/this.syncCount*100),console.log(`${this.onSyncIter} out of ${this.syncCount} is synced`),this.onSyncSliceJournalAccountSize=Object.values(o).length,this.syncJorunalAccountCount=Math.ceil(this.onSyncSliceJournalAccountSize/this.batchSpliceZbLineItemSize),this.sliceSampleResponse=this.sampleOnSyncResponse.splice(0,this.onSyncBatchSize),this.onSyncIter+=1,yield this.spliceBatchJournalAccountItem(e,o,t,n)}}}))}onSyncBatch(e){var t,n,i;return Object(r.c)(this,void 0,void 0,(function*(){this.syncFlag=!1;let o=!1;console.log("OnSync Batch Response : ",e),console.log("Field Mapping Flag : ",this.fieldMappingFlag),console.log("response.status : ",this.responseStatus);let s=!1,a=Object.keys(this.getSyncUrlEndpoint)[0],l=Object.values(this.getSyncUrlEndpoint)[0];return console.log("Key : ",a," and Value : ",l),!this.url.includes(a)||this.responseStatus?(this._toaster.showError("Sync Failed",""+(null===(n=this.getSyncUrlEndpoint)||void 0===n?void 0:n.msg),5e3),this.syncFlag=!1,this.onSyncFlag=!1,console.log("Here Ends 4"),this.hitapi.get("url").enable(),this.updateRunIdLogs("Error on sync",""+(null===(i=this.getSyncUrlEndpoint)||void 0===i?void 0:i.msg)),!1):Object.entries(e).length>0?void this.api.postOnSync({url:this.url,response:e,fieldMappingFlag:this.fieldMappingFlag,connection_id:this.currentConnectionId,"x-csrf-token":this.csrf_token,source_id:this.clicked_source_id,authorization:this.authorization,current_run_id:null===(t=this.getSyncUrlEndpoint)||void 0===t?void 0:t.run_id}).pipe(Object(h.a)(()=>Object(r.c)(this,void 0,void 0,(function*(){return o?(this.syncFlag=!1,this.onSyncFlag=!1,this.hitapi.get("url").enable(),console.log("Here Ends 0"),!1):this.onSyncIter<this.syncCount?(this.syncProgress=this.syncProgress>=100?0:Math.ceil((this.onSyncIter+1)/this.syncCount*100),console.log(`Sync Progress - ${this.syncProgress}, onSyscIter - ${this.onSyncIter}, synccount - ${this.syncCount}, Math.ceil(((this.onSyncIter+1)/this.syncCount)*100) - ${Math.ceil((this.onSyncIter+1)/this.syncCount*100)}, (this.onSyncIter+1)/this.syncCount - ${(this.onSyncIter+1)/this.syncCount}`),this.sliceSampleResponse=this.sampleOnSyncResponse.splice(0,this.onSyncBatchSize),this.onSyncIter+=1,yield this.onSyncBatch(this.sliceSampleResponse),console.log("Here Ends 1"),!0):(this.syncProgress=this.syncProgress>=100?0:Math.ceil((this.onSyncIter+1)/this.syncCount*100),console.log(`Sync Progress - ${this.syncProgress}, onSyscIter - ${this.onSyncIter}, synccount - ${this.syncCount}, Math.ceil(((this.onSyncIter+1)/this.syncCount)*100) - ${Math.ceil((this.onSyncIter+1)/this.syncCount*100)}, (this.onSyncIter+1)/this.syncCount - ${(this.onSyncIter+1)/this.syncCount}`),s=!0,this.syncFlag=!String(this.body.url).toLowerCase().includes("journalreport")||!String(this.body.url).toLowerCase().includes("intuit.com"),this.onSyncFlag=!1,this.hitapi.get("url").enable(),console.log("Here Ends 2"),!0)})))).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{console.log("Data insertion with response : ",e),0==e.Success?(o=!0,this._toaster.showError("Error On Sync","Error Info : "+e.Result,5e3),this.updateRunIdLogs("Error on sync for URL - "+this.url,this.url)):null==e.Success&&(o=!0,this._toaster.showError("Error On Sync","Error Info : "+e,5e3),this.updateRunIdLogs("Error on sync for URL - "+this.url,this.url))},e=>{console.log("oops",e),o=!0,this.syncFlag=!1,this.onSyncFlag=!1,this.hitapi.get("url").enable(),this._toaster.showError(`Error On Sync ${e.status} - ${e.statusText}`,"Error Info : "+e.error+" Error On URl : "+e.url,5e3),this.updateRunIdLogs("Error on sync for URL - "+this.url,this.url)}):(s=!0,console.log("Here Ends 3"),console.log("getSyncUrlEndpoint - "+this.getSyncUrlEndpoint.subsequent_endpoint),null==this.getSyncUrlEndpoint.subsequent_endpoint||this.employeeSyncLoading?(o||(this._toaster.showSuccess("Data Synced",this.url,5e3),this.updateRunIdLogs("Data synced for URL - "+this.url,this.url)),this.syncFlag=!String(this.body.url).toLowerCase().includes("journalreport")||!String(this.body.url).toLowerCase().includes("intuit.com"),this.onSyncFlag=!1,this.hitapi.get("url").enable(),!0):(this.lowerLimitOnSubSeqSync=0,this.lowerLimitOnSubSeqSyncPayloadSize=0,this.subsequentFlag=!0,this.syncProgress=0,yield this.subsequentProcessSync(this.getSyncUrlEndpoint.subsequent_endpoint),!0))}))}onSync(){return Object(r.c)(this,void 0,void 0,(function*(){let e;if("getZBReports"==this.headerName){this.syncProgress=0,this.subsequentFlag=!1,this.onSyncFlag=!0,this.hitapi.get("url").disable(),this.onSyncIter=0,console.log("this.response on sync : ",this.response[0]),this.sampleOnSyncResponse=yield this.onReturnArrayResponse(this.response[0]),console.log("this.sampleOnSyncResponse on sync : ",this.sampleOnSyncResponse),this.onSyncSliceSize=0;for(let i of Object.keys(this.response[0]))this.onSyncSliceSize+=this.response[0][i].length;let e=Object.keys(this.response[0]),t=0,n=0;for(let i of Object.keys(this.response[0])){t+=1;let e=i,o=this.response[0][i].length,s=this.response[0][i],a=0;for(let t=0;t<o;t+=this.onSyncBatchSizeZBReports){let o=s.slice(t,t+this.onSyncBatchSizeZBReports),r=o.length,l=yield this.voucherDataSyncforZB(this.url,i,o);if(console.log("SyncResult : ",l),"S"==l.messType)a+=r,n+=r,console.log(`Current voucher : ${e} with length ${a} is synced !`),console.log(`${n} out of ${this.onSyncSliceSize} records synced...`),this.syncProgress=Math.ceil(n/this.onSyncSliceSize*100);else if("E"==l.messType)return console.log(`Error on ${e} data sync`),this.syncFlag=!1,this.onSyncFlag=!1,this.hitapi.get("url").enable(),this._toaster.showError(l.messText,"Status Info : "+l.error,5e3)}this.syncProgress>=100&&(this.syncProgress=0),this.syncCount=Math.ceil(this.onSyncSliceSize/this.onSyncBatchSizeZBReports)}if(t==e.length)return this.syncFlag=!1,this.onSyncFlag=!1,this.hitapi.get("url").enable(),this._toaster.showSuccess("Data Synced",this.url,5e3)}else this.api.getbatchSize().pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{console.log("Batch Size for processing : ",e),"ZOHOBOOKS"==this.systemName&&String(this.body.url).toLowerCase().includes("activitylogs")&&(this.response=T.sortBy(this.response,"date_formatted"),this.onSyncBatchSize=e.Result.delete_batch_size,this.onSyncBatchSize=Math.ceil(this.onSyncBatchSize),this.batchSpliceZbLineItemSize=e.Result.delete_batch_size,console.log("this.batch_size : ",this.batch_size," and modified this.onSyncBatchSize upon delete batch size : ",this.onSyncBatchSize))}),void 0===this.response.errorStatus&&void 0===this.response.errno&&void 0===this.response.error_type_name?(this.syncProgress=0,this.subsequentFlag=!1,this.onSyncFlag=!0,this.hitapi.get("url").disable(),console.log("this.onSyncBatchSize on sync : ",this.onSyncBatchSize),this.api.getSyncFunction({url:this.url}).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(t=>Object(r.c)(this,void 0,void 0,(function*(){var n,i;this.getSyncUrlEndpoint=t,console.log("Get Sync URL : ",this.getSyncUrlEndpoint),1==t.has_error?(this.hitapi.get("url").disable(),this._toaster.showError("Sync Failed",""+(null===(n=this.getSyncUrlEndpoint)||void 0===n?void 0:n.msg),5e3),this.syncFlag=!1,this.onSyncFlag=!1,this.hitapi.get("url").enable(),this.updateRunIdLogs("Error on sync",""+(null===(i=this.getSyncUrlEndpoint)||void 0===i?void 0:i.msg))):(this.onSyncIter=0,this.response=T.sortBy(this.response,"last_modified_time"),console.log("this.response on sync : ",this.response),this.sampleOnSyncResponse=yield this.onReturnArrayResponse(this.response),console.log("this.sampleOnSyncResponse on sync : ",this.sampleOnSyncResponse),this.onSyncSliceSize=this.sampleOnSyncResponse.length,String(this.body.url).toLowerCase().includes("journal")&&String(this.body.url).toLowerCase().includes("books.zoho")?(this.onSyncBatchSize=1,this.syncCount=Math.ceil(this.onSyncSliceSize/this.onSyncBatchSize),this.sliceSampleResponse=this.sampleOnSyncResponse.splice(0,this.onSyncBatchSize)):(this.syncCount=Math.ceil(this.onSyncSliceSize/this.onSyncBatchSize),this.sliceSampleResponse=this.sampleOnSyncResponse.splice(0,this.onSyncBatchSize)),0==t.Success?e=yield this.onDirectSyncBatch(this.sliceSampleResponse):(this.hitapi.get("url").disable(),e=String(this.body.url).toLowerCase().includes("journal")&&String(this.body.url).toLowerCase().includes("books.zoho")&&!String(this.body.url).toLowerCase().includes("journals")?yield this.spliceBatchSplice(this.sliceSampleResponse,"journal",[]):String(this.body.url).toLowerCase().includes("journals")&&String(this.body.url).toLowerCase().includes("books.zoho")?yield this.spliceBatchSplice(this.sliceSampleResponse,"journals",[]):yield this.onSyncBatch(this.sliceSampleResponse)))})))):(this.syncFlag=!1,this.onSyncFlag=!1,this.hitapi.get("url").enable())}))}onDirectSyncBatch(e){var t;return Object(r.c)(this,void 0,void 0,(function*(){{this.syncFlag=!1;let n=!1;console.log("OnSync Batch Response : ",e),console.log("Field Mapping Flag : ",this.fieldMappingFlag),console.log("response.status : ",this.responseStatus);let i=!1;if(!(Object.entries(e).length>0))return n||this._toaster.showSuccess("Data Synced",this.url,5e3),i=!0,this.syncFlag=!0,this.onSyncFlag=!1,this.hitapi.get("url").enable(),!0;this.api.postOnSync({url:this.url,response:e,fieldMappingFlag:this.fieldMappingFlag,connection_id:this.currentConnectionId,"x-csrf-token":this.csrf_token,source_id:this.clicked_source_id,authorization:this.authorization,current_run_id:null===(t=this.getSyncUrlEndpoint)||void 0===t?void 0:t.run_id}).pipe(Object(h.a)(()=>Object(r.c)(this,void 0,void 0,(function*(){return n?(this.syncFlag=!1,this.onSyncFlag=!1,this.hitapi.get("url").enable(),!1):this.onSyncIter<this.syncCount?(this.syncProgress=this.syncProgress>=100?0:Math.ceil((this.onSyncIter+1)/this.syncCount*100),console.log(`Sync Progress - ${this.syncProgress}, onSyscIter - ${this.onSyncIter}, synccount - ${this.syncCount}, Percentage - ${Math.ceil((this.onSyncIter+1)/this.syncCount*100)}`),this.sliceSampleResponse=this.sampleOnSyncResponse.splice(0,this.onSyncBatchSize),this.onSyncIter+=1,void(yield this.onDirectSyncBatch(this.sliceSampleResponse))):(this.syncProgress=this.syncProgress>=100?0:Math.ceil((this.onSyncIter+1)/this.syncCount*100),console.log(`Sync Progress - ${this.syncProgress}, onSyscIter - ${this.onSyncIter}, synccount - ${this.syncCount}, Math.ceil(((this.onSyncIter+1)/this.syncCount)*100) - ${Math.ceil((this.onSyncIter+1)/this.syncCount*100)}, (this.onSyncIter+1)/this.syncCount - ${(this.onSyncIter+1)/this.syncCount}`),i=!0,this.syncFlag=!0,this.onSyncFlag=!1,this.hitapi.get("url").enable(),!0)})))).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{console.log("Data insertion with response : ",e),0==e.Success?(n=!0,this._toaster.showError("Error On Sync","Error Info : "+e.Result,5e3),"KEBS_QB"!==this.systemName&&"KEBS"!==this.systemName&&"KEBS_ZB"!==this.systemName||(console.log("Error Inside onDirectSyncBatch 1 : ",e),this.sampleResponse=[],this.sampleResponse.push(e))):null==e.Success&&(n=!0,this._toaster.showError("Error On Sync","Error Info : "+e,5e3),"KEBS_QB"!==this.systemName&&"KEBS"!==this.systemName&&"KEBS_ZB"!==this.systemName||(console.log("Error Inside onDirectSyncBatch 2 : ",e),this.sampleResponse=[],this.sampleResponse.push(e)))},e=>{console.log("oops",e),n=!0,this.syncFlag=!1,this.onSyncFlag=!1,this.hitapi.get("url").enable(),this._toaster.showError(`Error On Sync ${e.status} - ${e.statusText}`,"Error Info : "+e.error+" Error On URl : "+e.url,5e3)})}}))}getOAuthToken(){return Object(r.c)(this,void 0,void 0,(function*(){this.OAuthInputDetails.clientID=this.client_id_oauth_2_0.value,this.OAuthInputDetails.clientSecret=this.client_secret_oauth_2_0.value;let e={source_id:this.clicked_source_id,details:this.OAuthInputDetails,systemName:this.systemName};if(!this.client_id_oauth_2_0.value||!this.client_secret_oauth_2_0.value)return this._toaster.showWarning("Authentication details can't be empty !","Kindly give valid details to generate access token ",3e3);if(this.OAuthInput.Success){let e;alert("Token Generation Screen");var t="location=1,width=800,height=650";t+=",left="+(screen.width-800)/2+",top="+(screen.height-650)/2,this.api.getOAuthTokenPopUP({source_id:this.clicked_source_id,details:this.OAuthInputDetails}).pipe(Object(h.a)(()=>{})).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(n=>{e=String(n.url),localStorage.setItem("OAuthURLCallbackILS",e),window.open(e,"connectPopup",t)})}else yield this.api.getOAuthDetails({source_id:this.clicked_source_id}).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(t=>{this.OAuthInput=t,console.log("OAuth Details : ",this.OAuthInput),this.api.postOAuthDetails(e).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){let e;alert("Token Generation Screen");var t="location=1,width=800,height=650";t+=",left="+(screen.width-800)/2+",top="+(screen.height-650)/2,yield this.api.getOAuthTokenPopUP({source_id:this.clicked_source_id}).pipe(Object(h.a)(()=>{})).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(n=>{e=String(n.url),localStorage.setItem("OAuthURLCallbackILS",e),window.open(e,"connectPopup",t)})})))})}))}getQuickBookToken(){this.api.getQuickBookToken({source_id:this.clicked_source_id,systemName:this.systemName}).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{console.log("response on getQuickBookToken : ",e),null!=e&&(this.authenticationFieldMapping.patchValue({value:e,key:"Bearer"}),console.log("AuthenticationFieldMapping Token Value : ",this.authenticationFieldMapping.value))})}getQuickBookTokenOnSync(){this.authenticationFieldMapping.reset(),this.authenticationSubmitFlag=!1,this.accesstokenflagonrefresh=!0,console.log("Submitted Body on Token : ",this.body),this.api.getQuickBookToken({source_id:this.clicked_source_id,systemName:this.systemName}).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{console.log("response on getQuickBookTokenOnSync : ",e),null!=e?(this.authenticationFieldMapping.patchValue({value:e}),this.authenticationKey=null,this.authenticationValue=null,console.log("Refersh Token Authentication : ",this.authenticationFieldMapping.value),this.accesstokenflagonrefresh=!1):(this._toaster.showError(("Intuit"==this.systemName||"KEBS_QB"==this.systemName?"Quick Book":this.systemName)+" Token Status","Token Not found kindly create a new token",5e3),this.accesstokenflagonrefresh=!1)},e=>{console.log("oops",e),this._toaster.showError(("Intuit"==this.systemName||"KEBS_QB"==this.systemName?"Quick Book":this.systemName)+" Token Status","Token Not found kindly create a new token",5e3),this.accesstokenflagonrefresh=!1})}postSAPSFTokenOnSync(){this.OAuthSAPInputDetails.clientID=this.client_id_oauth_sap.value,this.OAuthSAPInputDetails.user_id=this.user_id_oauth_sap.value,this.OAuthSAPInputDetails.company_id=this.company_id_oauth_sap.value,this.OAuthSAPInputDetails.private_key=this.private_key_oauth_sap.value;let e={source_id:this.clicked_source_id,details:this.OAuthSAPInputDetails,systemName:this.systemName};if(!(this.client_id_oauth_sap.value&&this.user_id_oauth_sap.value&&this.company_id_oauth_sap.value&&this.private_key_oauth_sap.value))return this._toaster.showWarning("Authentication details can't be empty !","Kindly give valid details to generate access token ",3e3);this.api.postOAuthSAPDetails(e).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{this.OAuthInputSAP=e,this.authenticationFieldMapping.patchValue({value:e.access_token}),console.log("OAuth Details : ",this.OAuthInputSAP),this.OAuthInputSAP.Success?this._toaster.showSuccess("SAP SF Token Status","Token Retrieved",5e3):this._toaster.showError("SAP SF Token Status",""+this.OAuthInputSAP.response,5e3)})}getSAPSFTokenOnSync(){this.authenticationFieldMapping.reset(),this.authenticationSubmitFlag=!1,this.accesstokenflagonrefresh=!0,console.log("Submitted Body on Token : ",this.body),this.api.getSAMLAssertionAccessToken({source_id:this.clicked_source_id,systemName:this.systemName}).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{console.log("response on getSAMLAssertionAccessToken : ",e),null!=e.access_token?(this.authenticationFieldMapping.patchValue({value:e.access_token}),this.OAuthSAPInputDetails=e.Result,this.authenticationKey=null,this.authenticationValue=null,console.log("Refersh Token Authentication : ",this.authenticationFieldMapping.value),this.accesstokenflagonrefresh=!1,this._toaster.showSuccess("SAP SF Token Status","Token Refreshed",5e3)):(this._toaster.showError("OAuth SAP Token Status","Token Not found kindly create a new token",5e3),this.accesstokenflagonrefresh=!1)},e=>{console.log("oops",e),this._toaster.showError("OAuth SAP Token Status","Token Not found kindly create a new token",5e3),this.accesstokenflagonrefresh=!1})}app_idChange(e){this.app_ID=e.target.value.toString(),this.getToken=0!=this.app_ID.length}getBearerTokenPost(){if(!this.app_ID)return this._toaster.showWarning("Authentication details can't be empty !","Kindly give valid details to generate access token ",3e3);console.log("App ID for Token generation : ",this.app_ID),this.api.getBearerToken({o365_trusted_app_id:this.app_ID,connection_id:this.currentConnectionId}).pipe(Object(h.a)(()=>{this.api.postAppId({connection_id:this.currentConnectionId,o365_trusted_app_id:this.app_ID}).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(()=>{this.appIdFlag=!0})})).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{this.accessToken=e.token_res.access_token,console.log("Access Token : ",this.accessToken),this.authenticationFieldMapping.controls.value.setValue(this.accessToken),this.refreshTokenFlag=!0})}getBearerToken(){console.log("App ID for Token generation : ",this.app_ID),this.api.getBearerToken({o365_trusted_app_id:this.app_ID,connection_id:this.currentConnectionId}).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{this.accessToken=e.token_res.access_token,console.log("Access Token : ",this.accessToken),this.authenticationFieldMapping.controls.value.setValue(this.accessToken),this.refreshTokenFlag=!0})}subsequentProcessSync(e){return Object(r.c)(this,void 0,void 0,(function*(){this.api.subsequentEndpointCall(e,this.onSyncBatchSize,this.lowerLimitOnSubSeqSync,null,null).subscribe(t=>Object(r.c)(this,void 0,void 0,(function*(){return 0==t.Success?(this._toaster.showError("Error Status : "+t.Success,"Error Description - "+t.Result,5e3),this.syncFlag=!0,this.onSyncFlag=!1,this.hitapi.get("url").enable(),this.subsequentFlag=!1,this.lowerLimitOnSubSeqSync=0,t.Result):t.Success&&0!=t.Size?(this.lowerLimitOnSubSeqSyncPayloadSize=t.total_payload_size,this.syncProgress=this.syncProgress>=100?0:Math.ceil(this.lowerLimitOnSubSeqSync/this.lowerLimitOnSubSeqSyncPayloadSize*100)>100?100:Math.ceil(this.lowerLimitOnSubSeqSync/this.lowerLimitOnSubSeqSyncPayloadSize*100),this.lowerLimitOnSubSeqSync+=this.onSyncBatchSize,console.log(`Sync Progress for Endpoint ${e} - ${this.syncProgress}`),yield this.subsequentProcessSync(e),t.Result):(this.syncFlag=!0,this.onSyncFlag=!1,this.hitapi.get("url").enable(),this.subsequentFlag=!1,this.lowerLimitOnSubSeqSync=0,this._toaster.showSuccess(`${e} Sync Status : ${t.Success}`,"Sync Description - "+t.Result,5e3),yield this.onEDBgjProcess(),t.Result)})),e=>{this._toaster.showError("Error Status : "+e.Success,"Error Description - "+e.Result,5e3),this.syncFlag=!0,this.subsequentFlag=!1,this.onSyncFlag=!1,this.hitapi.get("url").enable(),this.lowerLimitOnSubSeqSync=0})}))}updateRunIdLogs(e,t){var n,i;return Object(r.c)(this,void 0,void 0,(function*(){this.hitapi.get("url").enable(),this.api.updateRunIdLogs({run_log_table:null===(n=this.getSyncUrlEndpoint)||void 0===n?void 0:n.run_log_table,run_id:null===(i=this.getSyncUrlEndpoint)||void 0===i?void 0:i.run_id,current_url:t,payload_size:this.onSyncSliceSize,message:e}).subscribe(e=>{console.log("log captured",e)},e=>{console.log("oops on capturing log",e),this._toaster.showError("Error Status : "+e.Success,"Error Description - "+e.Result,5e3)})}))}onChangeStatusFlag(){pt.a.fire({title:"All/Submit list of voucher_no/invoice_no for status change(Separate with comma's - ,)",input:"text",inputAttributes:{autocapitalize:"off"},showCancelButton:!0,confirmButtonText:"Change Status",showLoaderOnConfirm:!0,preConfirm:e=>new Promise((t,n)=>{if(e){let n="^[a-zA-Z0-9/\\\\ ,_-]*$";String(e).match(n)?this.api.changeVoucherStatus({voucher_no:e,currentConnectionItem:this.currentConnectionItem,connection_id:this.currentConnectionId}).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{console.log("response, "+JSON.stringify(e)),t(1!=e.Success?pt.a.showValidationMessage("Request failed: "+JSON.stringify(e.statusText)):e)},e=>{console.log("oops, "+JSON.stringify(e)),t(pt.a.showValidationMessage("Request failed: "+JSON.stringify(e.statusText)))}):t(pt.a.showValidationMessage(e+" - contains invalid character"))}else t(pt.a.showValidationMessage("Please Enter a valid data"))}),allowOutsideClick:()=>!pt.a.isLoading()}).then(e=>{e.isConfirmed?(console.log("isConfirmed : ",e),this._toaster.showSuccess("Successfully processed changeVoucherStatus "+e.value.Success,`Status Info : ${e.value.info} ${e.value.statusText}`,5e3)):e.isDismissed?console.log("isDismissed : ",e):console.log("Else stage : ",e)})}onReturnArrayResponse(e){return Object(r.c)(this,void 0,void 0,(function*(){return null===JSON||void 0===JSON?void 0:JSON.parse(null===JSON||void 0===JSON?void 0:JSON.stringify(e))}))}onVoucherTypesSubmit(){this.appliedVoucherTypesforZB=[],this.voucherTypesAppliedFlag=!0;let e=this.voucherTypesFieldMapping.value,t=[];e=null==e?void 0:e.itemRows.filter(e=>null!=e.voucher_type||null!=e.voucher_type),e=T.pluck(e,"voucher_type"),t=[...new Set(e)],t&&t.length>0&&(T.each(t,e=>{var t;let n=this.availableZBReportVoucherTypes.filter(t=>t.id==e);this.appliedVoucherTypesforZB.push(null===(t=n[0])||void 0===t?void 0:t.name)}),console.log("Selected Voucher Types for ZB sync : ",this.appliedVoucherTypesforZB))}onVoucherTypeAddRow(e,t=null){console.log("Number : ",e);let n=this.voucherTypesFieldMapping.controls.itemRows;console.log("data : added : ",n.value[e]),n.push(new c.m({voucher_category:new c.j("",c.H.required),voucher_type:new c.j("",c.H.required)}))}onVoucherTypeRemoveRow(e){let t=this.voucherTypesFieldMapping.controls.itemRows;console.log("Control length : ",t.length),t.length<=1?(t.clear(),this.onVoucherTypeAddRow(0)):(t.removeAt(e),console.log("Voucher Type afer deleting rows : ",this.voucherTypesFieldMapping.value))}voucherTypeGetControl(){return this.voucherTypesFieldMapping.get("itemRows").controls}voucherDataSyncforZB(e,t,n){return Object(r.c)(this,void 0,void 0,(function*(){try{return yield this.api.syncZBReportsData(e,t,n).pipe(Object(m.a)(this.$onDestroy)).pipe(Object(m.a)(this.$onAppApiCalled)).toPromise()}catch(i){throw console.error("Error syncing data:",i),i}}))}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275directiveInject"](o.a),s["\u0275\u0275directiveInject"](ut),s["\u0275\u0275directiveInject"](c.i),s["\u0275\u0275directiveInject"](o.g),s["\u0275\u0275directiveInject"](g.b),s["\u0275\u0275directiveInject"](N.a),s["\u0275\u0275directiveInject"](y),s["\u0275\u0275directiveInject"](L.l))},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-testapi"]],features:[s["\u0275\u0275ProvidersFeature"]([{provide:w.c,useClass:ht.c,deps:[w.f,ht.a]},{provide:w.e,useValue:{parse:{dateInput:"DD-MM-YYYY"},display:{dateInput:"DD-MM-YYYY",monthYearLabel:"MMM YYYY"}}}])],decls:14,vars:6,consts:[["rel","stylesheet","href",s["\u0275\u0275trustConstantResourceUrl"]("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css")],[1,"externalContainer","d-flex","gap-1"],[1,"Endpoints","d-flex","flex-column","align-items-start"],[1,"pt-2","pb-2","pl-3","pr-0",2,"width","20vw",3,"ngClass"],[1,"row"],[1,"expense-category","mr-2"],[1,"expense-sub-category"],["class","row pt-3",4,"ngIf"],[4,"ngFor","ngForOf"],[1,"container","col-9",2,"margin-top","20px"],["class","content d-flex justify-content-center mt-5 mb-5",4,"ngIf"],[4,"ngIf"],[1,"row","pt-3"],[1,"col-6","d-flex","pl-0"],[1,"heading","my-auto",3,"matTooltip"],[1,"col-0","d-flex","pl-0","pr-0"],[1,"content","my-auto"],["mat-flat-button","",2,"color","#fff","background-color","#cf0001","border-color","#cf0001","font-weight","400","font-size","12px","border-radius","4px","margin-left","15px","width","8vw",3,"click"],[1,"content","d-flex","justify-content-center","mt-5","mb-5"],[1,"row","border-bottom","solid","d-flex","justify-content-between"],[1,"col-5","pt-2","pb-2","d-flex"],["mat-icon-button","",1,"bubble"],[1,"iconButton"],[1,"name","my-auto","ml-2"],[1,"col-1","d-flex","pt-2"],["mat-icon-button","","class","trend-button-inactive","matTooltip","Change Outbound Status",3,"click",4,"ngIf"],[1,"col-6","d-flex","justify-content-end",2,"gap","1vw"],["mat-flat-button","","style","color: #fff; background-color: #cf0001; border-color: #cf0001;",3,"disabled","click",4,"ngIf"],["mat-flat-button","","style","color: #fff; background-color: #cf0001; border-color: #cf0001;",3,"click",4,"ngIf"],["className","api",1,"d-flex","justify-content-between","gap-1","mt-3",3,"formGroup"],["appearance","outline",1,"col-2"],["formControlName","method",3,"disabled","value"],[3,"disabled","value",4,"ngFor","ngForOf"],["appearance","outline",1,"col-9"],["formControlName","url","matInput","","type","text",3,"disabled","ngModel","ngModelChange","keyup"],[1,"col-1"],["mat-flat-button","",1,"btn","p-1",2,"color","#fff","background-color","#cf0001","border-color","#cf0001","font-weight","400","font-size","12px","border-radius","4px",3,"click"],[1,"response","mt-3"],["readonly","","placeholder","key","rows","15","cols","130",3,"innerHTML",4,"ngIf"],[1,"d-flex","justify-content-end"],["class","btn btn-md col-md-2 mt-1","mat-flat-button","","style","color: #fff; background-color: #cf0001; border-color: #cf0001;\n                font-weight: 400; font-size: 12px; border-radius: 4px;",3,"click",4,"ngIf"],["class","btn btn-md ml-3 col-md-1 mt-1","mat-flat-button","","style","color: #fff; background-color: #cf0001; border-color: #cf0001;\n                font-weight: 400; font-size: 12px; border-radius: 4px;",3,"click",4,"ngIf"],["mat-icon-button","","matTooltip","Change Outbound Status",1,"trend-button-inactive",3,"click"],["mat-flat-button","",2,"color","#fff","background-color","#cf0001","border-color","#cf0001",3,"disabled","click"],[1,"fa",2,"color","#fff",3,"ngClass"],["mat-flat-button","",2,"color","#fff","background-color","#cf0001","border-color","#cf0001",3,"click"],[3,"disabled","value"],[2,"align-items","center","display","flex","flex-direction","column"],["mode","determinate",3,"value","color"],[2,"top","0.5em","bottom","0.5em",3,"ngStyle"],["mat-tab-label",""],[1,"content","d-flex","flex-column","justify-content-between"],[3,"formGroup"],["formArrayName","itemRows",1,"fields"],["class","fiel",3,"formGroupName",4,"ngFor","ngForOf"],[1,"d-flex","flex-column","align-items-center"],["mat-flat-button","",2,"color","#fff","background-color","#cf0001","border-color","#cf0001","font-weight","400","font-size","12px","border-radius","4px",3,"click"],["style","color: rgb(51, 226, 12);",4,"ngIf"],[1,"fiel",3,"formGroupName"],[1,"d-flex","justify-content-even","mt-2"],["appearance","outline",1,"col-sm-3"],["matInput","","required","","formControlName","key","placeholder","QueryParams","appearance","outline"],[3,"value","click",4,"ngFor","ngForOf"],[1,"col-sm-1","mt-3"],["class","col-sm-4","appearance","outline",4,"ngIf"],["class","col-sm-4","appearance","outline","style","height:42px;",4,"ngIf"],[1,"col-sm-1"],["class","col-sm-1",4,"ngIf"],[3,"value","click"],["appearance","outline",1,"col-sm-4"],["matInput","","id","searchfield","formControlName","value","required","","placeholder","value",3,"change"],["appearance","outline",1,"col-sm-4",2,"height","42px"],["matInput","","formControlName","value","placeholder","Select Date",3,"matDatepicker"],["matSuffix","",3,"for"],["picker1",""],["matInput","",3,"matDatepicker","dateChange"],["matIconSuffix","",2,"border-color","#cf0001","font-weight","400","font-size","12px","border-radius","4px",3,"for"],["matDatepickerToggleIcon",""],["picker",""],[2,"color","rgb(51, 226, 12)"],[1,"content","d-flex","flex-column","justify-content-center","col-12"],[1,"content","d-flex","justify-content-evenly","gap-5","mt-5"],[1,"col-3"],["appearance","outline",1,"col-12",2,"width","450px"],["matInput","","required","",3,"value"],[1,"vr","mb-3"],["class","content d-flex flex-column align-items-center mb-5 col-9",4,"ngIf"],[1,"content","d-flex","flex-column","align-items-center","mb-5","col-9"],["appearance","outline",1,"col-sm-5",2,"width","450px"],["matInput","","placeholder","key","formControlName","key","required","",3,"change"],["matInput","","placeholder","value","formControlName","value","required","",3,"change"],["matInput","","placeholder","Token","formControlName","value","required","",3,"change"],["class","d-flex flex-column align-items-center col-9",4,"ngIf"],["matInput","","placeholder","App ID",3,"keyup"],[1,"d-flex","flex-column","align-items-center","col-9"],["matInput","","placeholder","Username","formControlName","key","required","",3,"change"],["matInput","","placeholder","Password","type","password","formControlName","value","required","",3,"change"],[1,"d-flex","justify-content-between","align-items-baseline"],["for","Access Token"],[1,"d-flex","justify-content-end","col-sm-9"],["appearance","outline",1,"col-8",2,"width","450px"],["mat-flat-button","",1,"col-4","mt-1",2,"color","#fff","background-color","#cf0001","border-color","#cf0001","height","fit-content","font-weight","400","font-size","12px","border-radius","4px",3,"disabled","click"],["for","clientID"],["matInput","","placeholder","",3,"formControl"],["for","clientSecret"],[1,"d-flex","justify-content-center"],["onclick","launchPopup('/sign_in_with_intuit')"],["type","button","mat-flat-button","",2,"color","#fff","background-color","#cf0001","border-color","#cf0001","font-weight","400","font-size","12px","border-radius","4px",3,"click"],["for","user_id"],["for","company_id"],["for","private_key"],[1,"content","d-flex","flex-column","justify-content-center","col-12","mt-2"],[1,"content","d-flex","justify-content-evenly","gap-5",2,"padding-top","5px"],["matInput","","required","","formControlName","voucher_category","placeholder","Select Types","appearance","outline"],["value","voucher_type_list"],["required","","placeholder","Voucher Types","formControlName","voucher_type",1,"col-sm-4",3,"list"],["readonly","","placeholder","key","rows","15","cols","130",3,"innerHTML"],["mat-flat-button","",1,"btn","btn-md","col-md-2","mt-1",2,"color","#fff","background-color","#cf0001","border-color","#cf0001","font-weight","400","font-size","12px","border-radius","4px",3,"click"],["mat-flat-button","",1,"btn","btn-md","ml-3","col-md-1","mt-1",2,"color","#fff","background-color","#cf0001","border-color","#cf0001","font-weight","400","font-size","12px","border-radius","4px",3,"click"]],template:function(e,t){1&e&&(s["\u0275\u0275element"](0,"link",0),s["\u0275\u0275elementStart"](1,"div",1),s["\u0275\u0275elementStart"](2,"div",2),s["\u0275\u0275elementStart"](3,"div",3),s["\u0275\u0275elementStart"](4,"div",4),s["\u0275\u0275elementStart"](5,"span",5),s["\u0275\u0275text"](6," Connection - "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](7,"span",6),s["\u0275\u0275text"](8," Endpoints "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](9,yt,8,2,"div",7),s["\u0275\u0275template"](10,_t,9,2,"div",8),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](11,"div",9),s["\u0275\u0275template"](12,bt,3,0,"div",10),s["\u0275\u0275template"](13,un,43,24,"div",11),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e&&(s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("ngClass",s["\u0275\u0275pureFunction0"](5,mn)),s["\u0275\u0275advance"](6),s["\u0275\u0275property"]("ngIf","SAP_SF"==t.systemName),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngForOf",t.sourceResource_List),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("ngIf",t.isLoadingEndpoint),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",!t.isLoadingEndpoint))},directives:[i.NgClass,i.NgIf,i.NgForOf,v.a,b.a,P.c,_.a,c.J,c.w,c.n,C.c,O.c,c.v,c.l,c.e,x.b,w.p,mt.a,i.NgStyle,gt.c,gt.a,gt.d,c.h,c.o,c.F,ft.g,ft.i,C.i,ft.f,ft.j,c.k,E.a],pipes:[i.AsyncPipe,i.JsonPipe],styles:[".externalContainer[_ngcontent-%COMP%]   .expense-details-card[_ngcontent-%COMP%]{min-height:91vh}.externalContainer[_ngcontent-%COMP%]   .expense-category[_ngcontent-%COMP%]{font-size:14px;color:#cf0001;font-weight:500}.externalContainer[_ngcontent-%COMP%]   .expense-sub-category[_ngcontent-%COMP%]{color:#1a1a1a;font-weight:500}.externalContainer[_ngcontent-%COMP%]   .expense-sub-category[_ngcontent-%COMP%], .externalContainer[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]{font-size:14px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.externalContainer[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]{font-weight:400;color:#6f6e6c}.externalContainer[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{font-size:14px;font-weight:400;color:#1a1a1a;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.externalContainer[_ngcontent-%COMP%]   .overflow-ctrl[_ngcontent-%COMP%]{width:85%;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.externalContainer[_ngcontent-%COMP%]   .required-amount[_ngcontent-%COMP%]{font-weight:600;font-size:18px}.externalContainer[_ngcontent-%COMP%]   .status-in-thread[_ngcontent-%COMP%]{color:#1a1a1a}.externalContainer[_ngcontent-%COMP%]   .status-in-thread[_ngcontent-%COMP%], .externalContainer[_ngcontent-%COMP%]   .table-heading[_ngcontent-%COMP%]{font-size:13px;font-weight:400;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.externalContainer[_ngcontent-%COMP%]   .table-heading[_ngcontent-%COMP%]{color:#6f6e6c}.externalContainer[_ngcontent-%COMP%]   .grey-heading[_ngcontent-%COMP%]{font-size:14px;font-weight:400;color:#6f6e6c;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.externalContainer[_ngcontent-%COMP%]   .status-circular[_ngcontent-%COMP%]{background-color:#ffb142}.externalContainer[_ngcontent-%COMP%]   .status-circular[_ngcontent-%COMP%], .externalContainer[_ngcontent-%COMP%]   .status-circular-in-thread[_ngcontent-%COMP%]{height:13px;width:13px;margin-top:3px;border-radius:50%;box-shadow:0 2px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.externalContainer[_ngcontent-%COMP%]   .status-circular-in-thread[_ngcontent-%COMP%]{background-color:#c7c4c4}.externalContainer[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]{border-bottom:1px solid #ddd}.externalContainer[_ngcontent-%COMP%]   .col-1x[_ngcontent-%COMP%]{flex:0 0 6.333333%;max-width:7.333333%}.externalContainer[_ngcontent-%COMP%]   .expense-item-table-button[_ngcontent-%COMP%]{height:28px!important;width:28px!important;line-height:28px}.externalContainer[_ngcontent-%COMP%]   .expense-item-table-button[_ngcontent-%COMP%]   .expense-item-table-icon[_ngcontent-%COMP%]{color:#66615b!important;font-size:18px!important}.externalContainer[_ngcontent-%COMP%]   .done-icon[_ngcontent-%COMP%]{color:#009432;font-size:21px}.externalContainer[_ngcontent-%COMP%]   .is-submitted-bg[_ngcontent-%COMP%]{background:#fffaf1}.externalContainer[_ngcontent-%COMP%]   .is-approved-bg[_ngcontent-%COMP%]{background:#f4ffd4}.externalContainer[_ngcontent-%COMP%]   .is-rejected-bg[_ngcontent-%COMP%], .externalContainer[_ngcontent-%COMP%]   .is-verified-bg[_ngcontent-%COMP%]{background:#ffebea}.externalContainer[_ngcontent-%COMP%]   .is-payed-bg[_ngcontent-%COMP%]{background:#e6fbe1}.externalContainer[_ngcontent-%COMP%]   .is-ko-submitted-bg[_ngcontent-%COMP%]{background:#ffebd3}.externalContainer[_ngcontent-%COMP%]   .is-ko-approved-bg[_ngcontent-%COMP%]{background:#e2f5f6}.externalContainer[_ngcontent-%COMP%]   .is-closed-bg[_ngcontent-%COMP%]{background:#e6ffef}.externalContainer[_ngcontent-%COMP%]   .is-submitted-circular[_ngcontent-%COMP%]{background:#ffb142}.externalContainer[_ngcontent-%COMP%]   .is-approved-circular[_ngcontent-%COMP%]{background:#badc58}.externalContainer[_ngcontent-%COMP%]   .is-verified-circular[_ngcontent-%COMP%]{background:#9f2825}.externalContainer[_ngcontent-%COMP%]   .is-payed-circular[_ngcontent-%COMP%]{background:#079992}.externalContainer[_ngcontent-%COMP%]   .is-ko-submitted-circular[_ngcontent-%COMP%]{background:#e58e26}.externalContainer[_ngcontent-%COMP%]   .is-ko-approved-circular[_ngcontent-%COMP%]{background:#5ce88c}.externalContainer[_ngcontent-%COMP%]   .is-closed-circular[_ngcontent-%COMP%]{background:#009432}.externalContainer[_ngcontent-%COMP%]   .slide-in-right[_ngcontent-%COMP%]{animation:slide-in-right .3s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-right{0%{transform:translateX(-15px);opacity:0}to{transform:translateX(0);opacity:1}}"]}),e})();var yn=n("Iab2"),_n=n("dNgK"),bn=n("ZzPI"),vn=n("6t9p");function Sn(e,t){if(1&e&&s["\u0275\u0275element"](0,"dxi-column",12),2&e){const e=t.$implicit,n=s["\u0275\u0275nextContext"](2);s["\u0275\u0275property"]("dataField",e.dataField)("allowReordering",!0)("caption",e.caption)("cellTemplate",e.dataField==n.view_log_field?n.statusCellTemplate:null)}}function Cn(e,t){1&e&&s["\u0275\u0275element"](0,"dxi-total-item",13),2&e&&s["\u0275\u0275property"]("column",t.$implicit.dataField)}function xn(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"div",1),s["\u0275\u0275elementStart"](1,"div",2),s["\u0275\u0275text"](2),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](3,"dx-data-grid",3),s["\u0275\u0275listener"]("onCellClick",(function(t){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"]().onColumnNameCellClick(t)}))("onCellPrepared",(function(t){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"]().onCellPrepared(t)})),s["\u0275\u0275element"](4,"dxo-column-chooser",4),s["\u0275\u0275element"](5,"dxo-column-fixing",5),s["\u0275\u0275element"](6,"dxi-column",6),s["\u0275\u0275element"](7,"dxo-export",5),s["\u0275\u0275element"](8,"dxo-search-panel",7),s["\u0275\u0275element"](9,"dxo-selection",8),s["\u0275\u0275element"](10,"dxo-header-filter",9),s["\u0275\u0275element"](11,"dxo-filter-row",9),s["\u0275\u0275template"](12,Sn,1,4,"dxi-column",10),s["\u0275\u0275elementStart"](13,"dxo-summary"),s["\u0275\u0275template"](14,Cn,1,1,"dxi-total-item",11),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275nextContext"]();s["\u0275\u0275advance"](2),s["\u0275\u0275textInterpolate1"](" ",e.displayname," "),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("allowColumnResizing",!0)("dataSource",e.logTemplate)("showBorders",!0)("hoverStateEnabled",!0)("columnAutoWidth",!0),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("enabled",!0),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("enabled",!0),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("allowFixing",!1),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("enabled",!0),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("visible",!0)("width",240),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("visible",!0),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("visible",!0),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngForOf",e.displayConfig),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("ngForOf",e.displayConfig)}}function On(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"div",28),s["\u0275\u0275elementStart"](1,"div",29),s["\u0275\u0275elementStart"](2,"span",30),s["\u0275\u0275text"](3,"Loading..."),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]())}function wn(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"div",33),s["\u0275\u0275elementStart"](1,"div"),s["\u0275\u0275elementStart"](2,"b"),s["\u0275\u0275text"](3),s["\u0275\u0275elementEnd"](),s["\u0275\u0275text"](4," : "),s["\u0275\u0275elementStart"](5,"pre"),s["\u0275\u0275text"](6),s["\u0275\u0275pipe"](7,"json"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;s["\u0275\u0275advance"](3),s["\u0275\u0275textInterpolate"](null==e?null:e.key),s["\u0275\u0275advance"](3),s["\u0275\u0275textInterpolate"](s["\u0275\u0275pipeBind1"](7,2,null==e?null:e.value))}}function En(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"div",31),s["\u0275\u0275elementStart"](1,"mat-card"),s["\u0275\u0275elementStart"](2,"div",24),s["\u0275\u0275elementStart"](3,"div",15),s["\u0275\u0275elementStart"](4,"div"),s["\u0275\u0275elementStart"](5,"b"),s["\u0275\u0275text"](6),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](7,wn,8,4,"div",32),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=t.index;s["\u0275\u0275advance"](6),s["\u0275\u0275textInterpolate1"]("Record ",n+1,""),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngForOf",e)}}function Pn(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"div"),s["\u0275\u0275elementStart"](1,"div",34),s["\u0275\u0275elementStart"](2,"mat-icon",35),s["\u0275\u0275text"](3,"running_with_errors"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](4,"span",36),s["\u0275\u0275text"](5,"No History Found!"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]())}function In(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"div",1),s["\u0275\u0275elementStart"](1,"div",14),s["\u0275\u0275elementStart"](2,"div",15),s["\u0275\u0275elementStart"](3,"mat-icon",16),s["\u0275\u0275text"](4,"history"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](5,"span",17),s["\u0275\u0275text"](6,"Log History"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](7,"div",15),s["\u0275\u0275elementStart"](8,"span",18),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"]().downloadContent()})),s["\u0275\u0275elementStart"](9,"mat-icon",19),s["\u0275\u0275text"](10,"description"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](11,"div",20),s["\u0275\u0275elementStart"](12,"span",18),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"]().closeLogDialog()})),s["\u0275\u0275elementStart"](13,"mat-icon",19),s["\u0275\u0275text"](14,"close"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275element"](15,"hr",21),s["\u0275\u0275elementStart"](16,"div",22),s["\u0275\u0275template"](17,On,4,0,"div",23),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](18,"div"),s["\u0275\u0275elementStart"](19,"div",24),s["\u0275\u0275elementStart"](20,"div",25),s["\u0275\u0275template"](21,En,8,2,"div",26),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](22,Pn,6,0,"div",27),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275nextContext"]();s["\u0275\u0275advance"](17),s["\u0275\u0275property"]("ngIf",1==e.logSpinner),s["\u0275\u0275advance"](4),s["\u0275\u0275property"]("ngForOf",e.logTemplate),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",0==e.logHistoryFound)}}let Mn=(()=>{class e{constructor(t,n,i,o,s,a){this.data=t,this.api=n,this.snackBar=i,this.openMatDialog=o,this._toaster=s,this.dialogRef=a,this.logHistoryFound=!1,this.logSpinner=!1,this.event_data={},this.is_table_overlay=0,this.logTemplate=[],this.arraydata=[],this.displayConfig={},this.currentTableName="",this.newTableName="",this.isLoaded=!1,this._onDestroy=new u.b,this.loadTableView=e=>Object(r.c)(this,void 0,void 0,(function*(){console.log("current displaymenuMasterData - ",this.displaymenuMasterData),this.filterConfig=T.filter(this.displaymenuMasterData,(function(t){return t.table_name==e})),console.log("current filterCOnfig - ",this.filterConfig),this.displayConfig=JSON.parse(this.filterConfig[0].fields),this.view_log_field=this.filterConfig[0].log_field,this.displayname=this.filterConfig[0].display_name,console.log("current filterCOnfig displayname - ",this.displayname)})),this.onColumnNameCellClick=t=>Object(r.c)(this,void 0,void 0,(function*(){var n,i,o,s,a,r,l,c,d,p,h,u,g,f;console.log(t),console.log("view_log_field : ",this.view_log_field),console.log("this.event_data : ",this.event_data),(null===(n=null==t?void 0:t.column)||void 0===n?void 0:n.dataField)==this.view_log_field&&(this.is_json_to_table_view?this.openMatDialog.open(e,{height:"80%",width:"60%",position:{right:"0px"},data:{popup_data:null,is_table_overlay:this.is_table_overlay,event_data:t.data,overlay_table_property:this.overlay_table_property,table_name:this.data.table_name,overlay_description:this.displayname,sync_status:this.sync_status}}):(null===(i=null==t?void 0:t.data)||void 0===i?void 0:i[null===(s=null===(o=this.filterConfig)||void 0===o?void 0:o[0])||void 0===s?void 0:s.is_primary_key])&&(this.overlay_table_property=this.parseJson(null===(r=null===(a=this.filterConfig)||void 0===a?void 0:a[0])||void 0===r?void 0:r.overlay_table_property),(null===(l=this.overlay_table_property)||void 0===l?void 0:l.is_json_to_table_view)?this.openMatDialog.open(e,{height:"80%",width:"75%",position:{left:"23%"},data:{popup_data:null,is_table_overlay:null===(d=null===(c=this.filterConfig)||void 0===c?void 0:c[0])||void 0===d?void 0:d.is_tableoverlay,event_data:t.data,overlay_table_property:this.overlay_table_property,table_name:this.newTableName,overlay_description:this.displayname,sync_status:this.sync_status}}):(console.log("Primary key Clicked : ",null===(h=null===(p=this.filterConfig)||void 0===p?void 0:p[0])||void 0===h?void 0:h.is_primary_key),this.api.getPopUpLog(null===(u=null==t?void 0:t.data)||void 0===u?void 0:u[null===(f=null===(g=this.filterConfig)||void 0===g?void 0:g[0])||void 0===f?void 0:f.is_primary_key],this.filterConfig[0].table_name,this.filterConfig[0].is_primary_key,this.filterConfig[0].log_field,this.filterConfig[0].is_sql).pipe(Object(m.a)(this._onDestroy)).subscribe(n=>{if(0==n.Success)return this.snackBar.open(n.PopUP_Data,"Dismiss",{duration:3e3});console.log("PopUp Logs : ",n),this.openMatDialog.open(e,{height:"80%",width:"60%",position:{left:"30%"},data:{popup_data:n.PopUP_Data,is_table_overlay:0,event_data:null==t?void 0:t.data,overlay_table_property:this.filterConfig[0].overlay_table_property,table_name:this.data.table_name,overlay_description:this.displayname}})},e=>{this._toaster.showError("Error In API","Error",3e3)}))))})),this.statusCellTemplate=(e,t)=>{let n=document.createElement("i");n.className="dx-icon-info",e.appendChild(n)},this.parseJson=e=>{try{return JSON.parse(e)}catch(t){return e}}}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}ngOnInit(){var e,t,n,i,o,s,a,l,c,d,p,h,u,m,g,f,y,_,b,v,S,C,x,O,w,E,P,I,M,D,k,j,A,F;return Object(r.c)(this,void 0,void 0,(function*(){if(console.log("Data from PopUp Component : ",this.data),this.logSpinner=!1,this.logData=null===(e=this.data)||void 0===e?void 0:e.popup_data,this.is_table_overlay=this.data.is_table_overlay,console.log("is_table_overlay",this.is_table_overlay),this.sync_status=null===(t=this.data)||void 0===t?void 0:t.sync_status,this.is_json_to_table_view=null===(i=null===(n=this.data)||void 0===n?void 0:n.overlay_table_property)||void 0===i?void 0:i.is_json_to_table_view,this.overlay_table_property=null===(o=this.data)||void 0===o?void 0:o.overlay_table_property,this.is_table_overlay)if(this.is_json_to_table_view){this.event_data=this.data.event_data,this.primary_key=null===(a=null===(s=this.data)||void 0===s?void 0:s.overlay_table_property)||void 0===a?void 0:a.primary_key,this.foreign_key=null===(c=null===(l=this.data)||void 0===l?void 0:l.overlay_table_property)||void 0===c?void 0:c.foreign_key;let e=null===(f=null===(m=null===(h=null===(p=null===(d=this.data)||void 0===d?void 0:d.overlay_table_property)||void 0===p?void 0:p.captured_from)||void 0===h?void 0:h[null===(u=this.event_data)||void 0===u?void 0:u.captured_from])||void 0===m?void 0:m[null===(g=this.event_data)||void 0===g?void 0:g.sync_type])||void 0===f?void 0:f.config,t=null===(x=null===(S=null===(b=null===(_=null===(y=this.data)||void 0===y?void 0:y.overlay_table_property)||void 0===_?void 0:_.captured_from)||void 0===b?void 0:b[null===(v=this.event_data)||void 0===v?void 0:v.captured_from])||void 0===S?void 0:S[null===(C=this.event_data)||void 0===C?void 0:C.sync_type])||void 0===x?void 0:x.field_mapping;e?(this.currentTableName=null===(O=this.data)||void 0===O?void 0:O.table_name,this.displaymenuMasterData=yield this.displayTableMasterData(),yield this.loadTableView(this.currentTableName),this.api.getTableData(this.filterConfig,null===(w=this.event_data)||void 0===w?void 0:w.run_id,null===(E=this.event_data)||void 0===E?void 0:E[this.primary_key],this.foreign_key,e,t).subscribe(t=>{this.isLoaded=!0,this.logTemplate=null==t?void 0:t.data,this.displayConfig=e})):this._toaster.showError("Invalid Configuration","Check the Configuration",3e3)}else this.event_data=this.data.event_data,this.currentTableName=this.data.table_name,this.newTableName=null===(I=null===(P=this.data)||void 0===P?void 0:P.overlay_table_property)||void 0===I?void 0:I.table_name,this.foreign_key=null===(D=null===(M=this.data)||void 0===M?void 0:M.overlay_table_property)||void 0===D?void 0:D.foreign_key,this.primary_key=null===(j=null===(k=this.data)||void 0===k?void 0:k.overlay_table_property)||void 0===j?void 0:j.primary_key,console.log("currentTableName",this.currentTableName),console.log("newTableName",this.newTableName),console.log("foreign_key",this.foreign_key),this.displaymenuMasterData=yield this.displayTableMasterData(),yield this.loadTableView(this.newTableName),this.api.getTableData(this.filterConfig,null===(A=this.event_data)||void 0===A?void 0:A.run_id,null===(F=this.event_data)||void 0===F?void 0:F[this.primary_key],this.foreign_key).subscribe(e=>{this.isLoaded=!0,this.logTemplate=null==e?void 0:e.data});else if(this.logData="string"==typeof this.logData?JSON.parse(this.logData):this.logData,console.log(this.logData),this.logData.length>0)if(this.logHistoryFound=!0,1==this.logData.length){this.logData=Object.entries(this.logData[0]);for(let[e,t]of this.logData)this.arraydata.push({key:e,value:t});this.logTemplate.push(this.arraydata),this.arraydata=[]}else for(let e of this.logData){e=Object.entries(e);for(let[t,n]of e)this.arraydata.push({key:t,value:n});this.logTemplate.push(this.arraydata),this.arraydata=[]}else this.logHistoryFound=!1;console.log(this.logTemplate),console.log("Stringify Value : ",JSON.stringify(this.logTemplate))}))}closeLogDialog(){this.dialogRef.close()}displayTableMasterData(){return new Promise((e,t)=>{this.api.getTableMasterData(0).subscribe(t=>{"S"==t.messType&&e(t.data)},e=>{console.log(e),t(e)})})}downloadContent(){console.log("DownloadContent Clicked...");let e=JSON.stringify(this.logTemplate,null,3);const t=new Blob([e],{type:"text/plain;charset=utf-8"});let n=Ne()(new Date).format("YYYY-MM-DD");console.log("Current Date : ",n),Object(yn.saveAs)(t,`AuditLog - ${n}.txt`)}onCellPrepared(e){var t,n,i;"data"===e.rowType&&("sync_status"===e.column.dataField?"Success"==(null===(t=null==e?void 0:e.data)||void 0===t?void 0:t.sync_status)?e.cellElement.style.cssText="color: white; background-color: green":"Failed"==(null===(n=null==e?void 0:e.data)||void 0===n?void 0:n.sync_status)?e.cellElement.style.cssText="color: white; background-color: red":"Skipped"==(null===(i=null==e?void 0:e.data)||void 0===i?void 0:i.sync_status)&&(e.cellElement.style.cssText="color: white; background-color: #FFBF00"):e.column.dataField==this.foreign_key&&("Success"==this.sync_status?e.cellElement.style.cssText="color: white; background-color: green":"Failed"==this.sync_status?e.cellElement.style.cssText="color: white; background-color: red":"Skipped"==this.sync_status&&(e.cellElement.style.cssText="color: white; background-color: #FFBF00")))}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275directiveInject"](g.a),s["\u0275\u0275directiveInject"](y),s["\u0275\u0275directiveInject"](_n.a),s["\u0275\u0275directiveInject"](g.b),s["\u0275\u0275directiveInject"](N.a),s["\u0275\u0275directiveInject"](g.h))},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-ils-grid-log-component"]],decls:2,vars:2,consts:[["class","container-fluid log-history mb-3",4,"ngIf"],[1,"container-fluid","log-history","mb-3"],[1,"col-12",2,"padding-top","25px","display","flex","align-items","center","color","red","flex-direction","column"],["id","gridContainer",1,"dev-style",3,"allowColumnResizing","dataSource","showBorders","hoverStateEnabled","columnAutoWidth","onCellClick","onCellPrepared"],["mode","select",3,"enabled"],[3,"enabled"],[3,"allowFixing"],["placeholder","Search...",3,"visible","width"],["mode","single"],[3,"visible"],[3,"dataField","allowReordering","caption","cellTemplate",4,"ngFor","ngForOf"],["summaryType","",3,"column",4,"ngFor","ngForOf"],[3,"dataField","allowReordering","caption","cellTemplate"],["summaryType","",3,"column"],[1,"row","p-1","pt-3","pb-3"],[1,"col"],[2,"font-size","21px","color","#cf0001","vertical-align","middle"],[1,"pl-1","heading-top"],[1,"vertical-align:middle",2,"cursor","pointer",3,"click"],[1,"icon-cancel"],[1,"col-1"],[1,"m-0"],[1,"spinner-class"],["class","pt-4 pb-4 text-center",4,"ngIf"],[1,"row"],[1,"col","p-0"],["class","mt-4",4,"ngFor","ngForOf"],[4,"ngIf"],[1,"pt-4","pb-4","text-center"],["role","status",1,"spinner-border"],[1,"sr-only"],[1,"mt-4"],["class","mt-1",4,"ngFor","ngForOf"],[1,"mt-1"],[1,"row","mt-5","d-flex","justify-content-center"],[1,"no-history-icon"],[1,"pl-2","no-history"]],template:function(e,t){1&e&&(s["\u0275\u0275template"](0,xn,15,16,"div",0),s["\u0275\u0275template"](1,In,23,3,"div",0)),2&e&&(s["\u0275\u0275property"]("ngIf",t.is_table_overlay&&t.isLoaded),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",!t.is_table_overlay))},directives:[i.NgIf,bn.a,vn.tb,vn.vb,vn.g,vn.Sb,vn.Md,vn.Od,vn.Cc,vn.dc,i.NgForOf,vn.ve,vn.N,_.a,$e.a],pipes:[i.JsonPipe],styles:[".ellipsis[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;width:100%}.ellipsis[_ngcontent-%COMP%], .margin[_ngcontent-%COMP%]{margin-top:5px}"]}),e})();function Dn(e,t){1&e&&s["\u0275\u0275element"](0,"dxo-editing",12),2&e&&s["\u0275\u0275property"]("allowUpdating",!0)}function kn(e,t){1&e&&s["\u0275\u0275element"](0,"dxo-editing",13),2&e&&s["\u0275\u0275property"]("allowAdding",!0)}function jn(e,t){1&e&&s["\u0275\u0275element"](0,"dxo-editing",14),2&e&&s["\u0275\u0275property"]("allowDeleting",!0)}function An(e,t){if(1&e&&s["\u0275\u0275element"](0,"dxi-column",15),2&e){const e=t.$implicit,n=s["\u0275\u0275nextContext"]();s["\u0275\u0275property"]("dataField",e.dataField)("allowReordering",!0)("caption",e.caption)("allowEditing",e.allowEditing)("cellTemplate",e.dataField==n.view_log_field?n.statusCellTemplate:null)}}function Fn(e,t){1&e&&s["\u0275\u0275element"](0,"dxi-total-item",16),2&e&&s["\u0275\u0275property"]("column",t.$implicit.dataField)}let Rn=(()=>{class e{constructor(e,t,n,i){this.api=e,this._toaster=t,this.snackBar=n,this.openMatDialog=i,this._onDestroy=new u.b,this.is_edit=0,this.is_add=0,this.is_delete=0,this.is_primary_key=0,this.view_log_field=0,this.overlay_table_property={},this.is_tableoverlay=0,this.current_table_name="",this.pop_up_description="",this.sync_status="",this.statusCellTemplate=(e,t)=>{let n=document.createElement("i");n.className="dx-icon-info",e.appendChild(n)}}ngOnInit(){var e,t,n,i,o,s;console.log("FilterCongig for Table : ",this.filterConfig),this.current_table_name=this.filterConfig[0].table_name,this.is_edit=this.filterConfig[0].is_edit,this.is_add=this.filterConfig[0].is_add,this.is_delete=this.filterConfig[0].is_delete,this.is_primary_key=this.filterConfig[0].is_primary_key,this.view_log_field=this.filterConfig[0].log_field,this.view_log_field=this.filterConfig[0].log_field,this.overlay_table_property=null===JSON||void 0===JSON?void 0:JSON.parse(this.filterConfig[0].overlay_table_property),this.pop_up_description=null===(e=this.overlay_table_property)||void 0===e?void 0:e.overlay_description,this.is_json_to_table_view=null===(t=this.overlay_table_property)||void 0===t?void 0:t.is_json_to_table_view,this.is_tableoverlay=this.filterConfig[0].is_tableoverlay,this.is_edit=null!=(null===(i=null===(n=this.filterConfig)||void 0===n?void 0:n[0])||void 0===i?void 0:i.is_tableoverlay)&&1==(null===(s=null===(o=this.filterConfig)||void 0===o?void 0:o[0])||void 0===s?void 0:s.is_tableoverlay)?0:this.is_edit,console.log(`is_edit disabled status - ${this.is_edit} when is_tableoverlay - ${this.is_tableoverlay}`)}valueChanged(e){console.log(e)}logEvent(e){this.api.allowCrudApi(e.changes[0].data,this.is_primary_key,this.filterConfig,e.changes[0].type).pipe(Object(m.a)(this._onDestroy)).subscribe(e=>{"S"==e.messType?this._toaster.showSuccess(e.messText,"Success",3e3):this._toaster.showWarning(e.messText,"warning")},e=>{this._toaster.showError("Error In API","Error",3e3)})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}onColumnNameCellClick(e){var t,n,i,o;return Object(r.c)(this,void 0,void 0,(function*(){if(console.log("Clicked Data log : ",e.data),console.log("Clicked Column log : ",e.column),console.log("current_table_name : ",this.current_table_name),this.rowInstance=e,this.rowInstance.column.dataField!=this.view_log_field||this.is_json_to_table_view)this.is_tableoverlay&&null!=e.data&&(this.sync_status=null===(o=null==e?void 0:e.data)||void 0===o?void 0:o.sync_status,this.openMatDialog.open(Mn,{height:"100%",width:"80%",position:{right:"0px"},data:{popup_data:null,is_table_overlay:this.is_tableoverlay,event_data:e.data,overlay_table_property:this.overlay_table_property,table_name:this.current_table_name,overlay_description:this.pop_up_description,sync_status:this.sync_status}}));else if(e.data){this.rowInstanceData=e.data,this.sync_status=null===(t=this.rowInstanceData)||void 0===t?void 0:t.sync_status,this.id=this.rowInstanceData[null===(n=this.filterConfig[0])||void 0===n?void 0:n.is_primary_key];let o=null===(i=this.filterConfig[0])||void 0===i?void 0:i.is_sql;this.id&&(console.log("ID Clicked : ",this.id),this.api.getPopUpLog(this.id,this.filterConfig[0].table_name,this.filterConfig[0].is_primary_key,this.filterConfig[0].log_field,o).pipe(Object(m.a)(this._onDestroy)).subscribe(t=>{if(console.log("Res Data : "+JSON.stringify(t)),this.getChangeLog=t,0==this.getChangeLog.Success)return this.snackBar.open(this.getChangeLog.PopUP_Data,"Dismiss",{duration:3e3});console.log("PopUp Logs : ",this.getChangeLog),this.openMatDialog.open(Mn,{height:"80%",width:"70%",data:{popup_data:this.getChangeLog.PopUP_Data,is_table_overlay:this.is_tableoverlay,event_data:e.data,overlay_table_property:this.overlay_table_property,table_name:this.current_table_name,overlay_description:this.pop_up_description,sync_status:this.sync_status}})},e=>{this._toaster.showError("Error In API","Error",3e3)}))}}))}onCellPrepared(e){var t,n,i;"data"===e.rowType&&"sync_status"===e.column.dataField&&("Success"==(null===(t=null==e?void 0:e.data)||void 0===t?void 0:t.sync_status)?e.cellElement.style.cssText="color: white; background-color: green":"Failed"==(null===(n=null==e?void 0:e.data)||void 0===n?void 0:n.sync_status)?e.cellElement.style.cssText="color: white; background-color: red":"Skipped"==(null===(i=null==e?void 0:e.data)||void 0===i?void 0:i.sync_status)&&(e.cellElement.style.cssText="color: white; background-color: #FFBF00"))}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275directiveInject"](y),s["\u0275\u0275directiveInject"](N.a),s["\u0275\u0275directiveInject"](_n.a),s["\u0275\u0275directiveInject"](g.b))},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-ils-data-grid-devextreme"]],inputs:{data:"data",columnConfig:"columnConfig",filterConfig:"filterConfig"},decls:15,vars:18,consts:[["id","gridContainer",1,"dev-style",3,"allowColumnResizing","dataSource","showBorders","hoverStateEnabled","columnAutoWidth","onCellClick","onRowInserted","onRowInserting","onSaved","onCellPrepared"],["mode","select",3,"enabled"],[3,"enabled"],[3,"allowFixing"],["placeholder","Search...",3,"visible","width"],["mode","single"],[3,"visible"],["mode","row",3,"allowUpdating",4,"ngIf"],["mode","row",3,"allowAdding",4,"ngIf"],["mode","row",3,"allowDeleting",4,"ngIf"],[3,"dataField","allowReordering","caption","allowEditing","cellTemplate",4,"ngFor","ngForOf"],["summaryType","",3,"column",4,"ngFor","ngForOf"],["mode","row",3,"allowUpdating"],["mode","row",3,"allowAdding"],["mode","row",3,"allowDeleting"],[3,"dataField","allowReordering","caption","allowEditing","cellTemplate"],["summaryType","",3,"column"]],template:function(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"dx-data-grid",0),s["\u0275\u0275listener"]("onCellClick",(function(e){return t.onColumnNameCellClick(e)}))("onRowInserted",(function(){return t.logEvent("plus_icon")}))("onRowInserting",(function(){return t.logEvent("RowInserting")}))("onSaved",(function(e){return t.logEvent(e)}))("onCellPrepared",(function(e){return t.onCellPrepared(e)})),s["\u0275\u0275element"](1,"dxo-column-chooser",1),s["\u0275\u0275element"](2,"dxo-column-fixing",2),s["\u0275\u0275element"](3,"dxi-column",3),s["\u0275\u0275element"](4,"dxo-export",2),s["\u0275\u0275element"](5,"dxo-search-panel",4),s["\u0275\u0275element"](6,"dxo-selection",5),s["\u0275\u0275element"](7,"dxo-header-filter",6),s["\u0275\u0275element"](8,"dxo-filter-row",6),s["\u0275\u0275template"](9,Dn,1,1,"dxo-editing",7),s["\u0275\u0275template"](10,kn,1,1,"dxo-editing",8),s["\u0275\u0275template"](11,jn,1,1,"dxo-editing",9),s["\u0275\u0275template"](12,An,1,5,"dxi-column",10),s["\u0275\u0275elementStart"](13,"dxo-summary"),s["\u0275\u0275template"](14,Fn,1,1,"dxi-total-item",11),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e&&(s["\u0275\u0275property"]("allowColumnResizing",!0)("dataSource",t.data)("showBorders",!0)("hoverStateEnabled",!0)("columnAutoWidth",!0),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("enabled",!0),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("enabled",!0),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("allowFixing",!1),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("enabled",!0),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("visible",!0)("width",240),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("visible",!0),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("visible",!0),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",t.is_edit),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",t.is_add),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",t.is_delete),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngForOf",t.columnConfig),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("ngForOf",t.columnConfig))},directives:[bn.a,vn.tb,vn.vb,vn.g,vn.Sb,vn.Md,vn.Od,vn.Cc,vn.dc,i.NgIf,i.NgForOf,vn.ve,vn.Qb,vn.N],styles:[""]}),e})();function Tn(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"button",21),s["\u0275\u0275elementStart"](1,"div",22),s["\u0275\u0275elementStart"](2,"div",23),s["\u0275\u0275listener"]("click",(function(){s["\u0275\u0275restoreView"](e);const n=t.$implicit,i=t.index;return s["\u0275\u0275nextContext"]().loadTableView(n.sequence_id,1,i)})),s["\u0275\u0275text"](3),s["\u0275\u0275elementEnd"](),s["\u0275\u0275element"](4,"div",24),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;s["\u0275\u0275advance"](3),s["\u0275\u0275textInterpolate"](e.display_name)}}function Ln(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"div",3),s["\u0275\u0275elementStart"](1,"button",25),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"]().refreshIcon()})),s["\u0275\u0275elementStart"](2,"mat-icon"),s["\u0275\u0275text"](3,"refresh"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275nextContext"]();s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("disabled",e.loadingFlag)}}function Nn(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"div",26),s["\u0275\u0275elementStart"](1,"mat-form-field",27),s["\u0275\u0275elementStart"](2,"mat-label"),s["\u0275\u0275text"](3,"Date Filter"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275element"](4,"input",28),s["\u0275\u0275element"](5,"mat-datepicker-toggle",29),s["\u0275\u0275elementStart"](6,"mat-datepicker",30,31),s["\u0275\u0275listener"]("monthSelected",(function(t){s["\u0275\u0275restoreView"](e);const n=s["\u0275\u0275reference"](7);return s["\u0275\u0275nextContext"]().selectedDate(t,n)})),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275reference"](7),t=s["\u0275\u0275nextContext"]();s["\u0275\u0275advance"](4),s["\u0275\u0275property"]("matDatepicker",e)("formControl",t.date),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("for",e)}}function Bn(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"div",32),s["\u0275\u0275elementStart"](1,"mat-form-field",33),s["\u0275\u0275elementStart"](2,"mat-label"),s["\u0275\u0275text"](3,"Date Range"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](4,"mat-date-range-input",34),s["\u0275\u0275element"](5,"input",35),s["\u0275\u0275element"](6,"input",36),s["\u0275\u0275elementEnd"](),s["\u0275\u0275element"](7,"mat-datepicker-toggle",29),s["\u0275\u0275element"](8,"mat-date-range-picker",null,37),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e){const e=s["\u0275\u0275reference"](9),t=s["\u0275\u0275nextContext"]();s["\u0275\u0275advance"](4),s["\u0275\u0275property"]("formGroup",t.range)("rangePicker",e),s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("for",e)}}function qn(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"button",38),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"]().onDateRangeClick()})),s["\u0275\u0275text"](1,"Send"),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275nextContext"]();s["\u0275\u0275property"]("disabled",e.loadingFlag)}}function $n(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"button",39),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"]().refreshUIData()})),s["\u0275\u0275elementStart"](1,"mat-icon",40),s["\u0275\u0275text"](2,"loop"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275nextContext"]();s["\u0275\u0275property"]("disabled",e.loadingFlag)}}function Vn(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"div",3),s["\u0275\u0275elementStart"](1,"button",41),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"]().latestRunCheck()})),s["\u0275\u0275text"](2,"Run Id"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}}function zn(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"div",42),s["\u0275\u0275elementStart"](1,"button",41),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"]().rerunForCustomEmployee()})),s["\u0275\u0275text"](2,"Employee Id"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}}const Hn=function(e){return{"fa-circle-o-notch fa-spin":e}};function Jn(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"div",43),s["\u0275\u0275elementStart"](1,"button",44),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"]().ilsEDComparision()})),s["\u0275\u0275text"](2,"Report"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](3,"button",38),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"]().onRunIdApiHit()})),s["\u0275\u0275element"](4,"i",45),s["\u0275\u0275text"](5,"ED Sync"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275nextContext"]();s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("disabled",e.ilsEDComaprionLoadingFlag),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("disabled",e.runApiLoadingFlag),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngClass",s["\u0275\u0275pureFunction1"](3,Hn,e.runApiLoadingFlag))}}function Un(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"div",48),s["\u0275\u0275text"](1),s["\u0275\u0275elementEnd"]()),2&e){const e=s["\u0275\u0275nextContext"](2);s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate2"](" ",e.displayStart," -- ",e.displayEnd," ")}}function Yn(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"div",2),s["\u0275\u0275element"](1,"div",46),s["\u0275\u0275template"](2,Un,2,2,"div",47),s["\u0275\u0275elementEnd"]()),2&e){const e=s["\u0275\u0275nextContext"]();s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("ngIf",e.toggleFlag)}}function Kn(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"div",49),s["\u0275\u0275elementStart"](1,"div",50),s["\u0275\u0275element"](2,"mat-spinner",51),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]())}function Qn(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"div",2),s["\u0275\u0275elementStart"](1,"div",1),s["\u0275\u0275element"](2,"app-ils-data-grid-devextreme",52),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e){const e=s["\u0275\u0275nextContext"]();s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("data",e.tableData)("columnConfig",e.displayConfig)("filterConfig",e.filterConfig)}}const Gn=Ne.a||Le,Zn={parse:{dateInput:"MMM-YYYY"},display:{dateInput:"MMM-YYYY",monthYearLabel:"MMM YYYY",dateA11yLabel:"LL",monthYearA11yLabel:"MMMM YYYY"}};function Wn(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"mat-option",12),s["\u0275\u0275text"](1),s["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;s["\u0275\u0275property"]("value",e.value),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate"](e.key)}}function Xn(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"div",13),s["\u0275\u0275text"](1),s["\u0275\u0275elementEnd"]()),2&e){const e=s["\u0275\u0275nextContext"]();s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate1"](" Count:",e.totalCount," ")}}function ei(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"div",14),s["\u0275\u0275text"](1," Exception Report "),s["\u0275\u0275elementEnd"]())}function ti(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"div",15),s["\u0275\u0275elementStart"](1,"div",16),s["\u0275\u0275element"](2,"mat-spinner",17),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]())}function ni(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"div",3),s["\u0275\u0275elementStart"](1,"div",18),s["\u0275\u0275element"](2,"app-ils-data-grid-devextreme",19),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e){const e=s["\u0275\u0275nextContext"]();s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("data",e.dataSource)("columnConfig",e.displayColumn)}}const ii=[{path:"",component:a,redirectTo:"home",pathMatch:"full"},{path:"home",children:[{path:"",component:tt,data:{breadcrumb:"ILS"}},{path:"connectionlist/:project_id",component:Fe,data:{breadcrumb:"ILS-Connections"}},{path:"testapi/:connection_id/:systemName",component:fn,data:{breadcrumb:"ILS-testapi"}},{path:"runapi/:connection_id/:clickedIndex/:clicked_source_id",component:Re,data:{breadcrumb:"ILS-runapi"}},{path:"admin",component:(()=>{class e{constructor(e,t){this.api=e,this._toaster=t,this.date=new c.j(Gn()),this.range=new c.m({start:new c.j(null),end:new c.j(null)}),this._onDestroy=new u.b,this.displaymenuMasterData=[],this.tableData=[],this.filterConfig=[],this.displayConfig=[],this.isLoading=!0,this.application_id=0,this.displayName="",this.isDateFilter=0,this.isDateQuery="",this.tableNameDisplay="",this.toggleFlag=!0,this.displayStart="",this.displayEnd="",this.isDaterangeQuery="",this.isDateRangeFilter=0,this.showTags=!1,this.loadingFlag=!0,this.isRunIdApiHit=!1,this.runApiLoadingFlag=!0,this.currentItemId=null,this.onSyncBatchSize=2,this.lowerLimitOnSubSeqSync=0,this.lowerLimitOnSubSeqSyncPayloadSize=0,this.latestRunId=0,this.ilsEDComaprionFlag=!0,this.ilsEDComaprionLoadingFlag=!1,this.is_runid_based=!1,this.loadTableView=e=>Object(r.c)(this,void 0,void 0,(function*(){this.latestRunId=0,this.filterConfig=T.filter(this.displaymenuMasterData,(function(t){return t.sequence_id==e})),this.tableNameDisplay=this.filterConfig[0].table_name,this.tableData=yield this.getTableData(this.filterConfig),this.displayConfig=JSON.parse(this.filterConfig[0].fields),this.displayConfig=yield this.changeConfig(this.displayConfig)}))}ngOnInit(){return Object(r.c)(this,void 0,void 0,(function*(){this.displaymenuMasterData=yield this.displayTableMasterData(),this.loadTableView(this.displaymenuMasterData[0].sequence_id)}))}displayTableMasterData(){return new Promise((e,t)=>{this.api.getTableMasterData(this.application_id).subscribe(t=>{"S"==t.messType&&e(t.data)},e=>{console.log(e),t(e)})})}getTableData(e){return Object(r.c)(this,void 0,void 0,(function*(){return this.loadingFlag=!0,this.isLoading=!0,this.displayName=e[0].display_name,this.isDateQuery=JSON.parse(e[0].is_front_end_filter),this.isDaterangeQuery=JSON.parse(e[0].is_date_range_filter),this.is_runid_based=e[0].is_runid_based,console.log(e),yield this.processRunApi(e),this.isDateQuery.length>0?(this.isDateFilter=Number(this.isDateQuery[0]),e[0].monthAndDate=Gn(this.date.value).format("YYYY-MM-DD"),e[0].isMonthActive=1):(this.isDateFilter=0,e[0].isMonthActive=0),JSON.parse(e[0].is_date_range_filter).length>1&&null==this.range.value.start&&null==this.range.value.end&&1==Number(this.isDaterangeQuery[0])?(this.isDateRangeFilter=Number(this.isDaterangeQuery[0]),e[0].isDateRangeActive=1,this.isLoading=!0,this.showTags=!0,this.range.get("end").setValue(Gn().add(1,"days")),this.range.get("start").setValue(Gn().subtract(1,"days")),this.isDateRangeFilter=Number(this.isDaterangeQuery[0]),this.filterConfig[0].startDate=Gn(this.range.value.start).format("YYYY-MM-DD"),this.filterConfig[0].enddate=Gn(this.range.value.end).format("YYYY-MM-DD"),this.displayStart=Gn(this.range.value.start).format("YYYY-MM-DD"),this.displayEnd=Gn(this.range.value.end).format("YYYY-MM-DD"),e[0].isDateRangeActive=1,this.loadingFlag=!0,new Promise((t,n)=>{this.api.getTableData(e,this.latestRunId).subscribe(e=>{"S"==e.messType&&e.data.length>0&&(t(e.data),this.isLoading=!1,this.showTags=!1,this._toaster.showSuccess(e.messText,"Success",2e3),this.loadingFlag=!1),"E"==e.messType&&0==e.data.length&&(this.isLoading=!1,this.showTags=!1,this._toaster.showWarning(e.messText,"Warning"),t(e.data),this.loadingFlag=!1)},e=>{this.isLoading=!1,n(e),this.loadingFlag=!1})})):(this.isDaterangeQuery.length>0?(this.isDateRangeFilter=Number(this.isDaterangeQuery[0]),this.filterConfig[0].startDate=Gn(this.range.value.start).format("YYYY-MM-DD"),this.filterConfig[0].enddate=Gn(this.range.value.end).format("YYYY-MM-DD"),this.displayStart=Gn(this.range.value.start).format("YYYY-MM-DD"),this.displayEnd=Gn(this.range.value.end).format("YYYY-MM-DD"),e[0].isDateRangeActive=1):(this.isDateRangeFilter=0,e[0].isDateRangeActive=0),this.loadingFlag=!0,new Promise((t,n)=>{this.api.getTableData(e,this.latestRunId).subscribe(e=>{"S"==e.messType&&e.data.length>0&&(t(e.data),this.isLoading=!1,this.showTags=!1,this._toaster.showSuccess(e.messText,"Success",2e3),this.loadingFlag=!1),"E"==e.messType&&0==e.data.length&&(this.isLoading=!1,this.showTags=!1,this._toaster.showWarning(e.messText,"Warning"),t(e.data),this.loadingFlag=!1)},e=>{this.isLoading=!1,console.log(e),n(e),this.loadingFlag=!1})}))}))}changeConfig(e){for(let t of e)t.allowEditing="true"==t.allowEditing&&"string"==typeof t.allowEditing;return e}selectedDate(e,t){return Object(r.c)(this,void 0,void 0,(function*(){const n=this.date.value;n.month(e.month()),n.year(e.year()),this.date.setValue(n),t.close(),this.tableData=yield this.getTableData(this.filterConfig)}))}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}onDateRangeClick(){return Object(r.c)(this,void 0,void 0,(function*(){if(null==this.range.value.start)this._toaster.showWarning("Please choose a start date to Continue","warning");else if(null==this.range.value.end)this._toaster.showWarning("Please choose a End date to Continue","warning");else{let e=Gn(this.range.value.start,"DD-MM-YYYY");Gn(this.range.value.end,"DD-MM-YYYY").diff(e,"days")>3?this._toaster.showWarning("Please choose a Range of Maximum 3 days","warning"):this.tableData=yield this.getTableData(this.filterConfig)}}))}refreshIcon(){return Object(r.c)(this,void 0,void 0,(function*(){console.log("refresh working"),this.tableData=yield this.getTableData(this.filterConfig)}))}processRunApi(e){return Object(r.c)(this,void 0,void 0,(function*(){let t=null;this.currentItemId=e[0].id,this.api.getbatchSize().subscribe(e=>{var t,n;this.onSyncBatchSize=null!=(null===(t=null==e?void 0:e.Result)||void 0===t?void 0:t.on_sync_batch_size)?null===(n=null==e?void 0:e.Result)||void 0===n?void 0:n.on_sync_batch_size:this.onSyncBatchSize}),this.api.refreshTableConfigData(e[0].id).subscribe(e=>{var n,i,o,s;t=e.data,0==(null===(i=JSON.parse(null===(n=t[0])||void 0===n?void 0:n.run_api_flag))||void 0===i?void 0:i.in_progress)&&1==(null===(s=JSON.parse(null===(o=t[0])||void 0===o?void 0:o.run_api_flag))||void 0===s?void 0:s.is_active)?(this.isRunIdApiHit=!0,this.runApiLoadingFlag=!1,this.lowerLimitOnSubSeqSync=0,this.lowerLimitOnSubSeqSyncPayloadSize=0):(this.isRunIdApiHit=!1,this.runApiLoadingFlag=!0)},e=>{this.isRunIdApiHit=!1,this.runApiLoadingFlag=!0})}))}onRunIdApiHit(){return Object(r.c)(this,void 0,void 0,(function*(){this.runApiLoadingFlag=!0,this.api.subsequentEndpointCall("postToUsers",this.onSyncBatchSize,this.lowerLimitOnSubSeqSync,this.currentItemId,null).subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){return 0==e.Success?(this._toaster.showInfo("Employee Data Sync Status - "+e.Success,""+e.Result,5e3),this.runApiLoadingFlag=!1,this.lowerLimitOnSubSeqSync=0,this.lowerLimitOnSubSeqSyncPayloadSize=0,e.Result):e.Success&&0!=e.Size?(this.lowerLimitOnSubSeqSyncPayloadSize=e.total_payload_size,this.lowerLimitOnSubSeqSync+=this.onSyncBatchSize,console.log("Sync Progress for Endpoint postToUsers - "+Math.ceil(this.lowerLimitOnSubSeqSync/this.lowerLimitOnSubSeqSyncPayloadSize*100)),yield this.onRunIdApiHit(),e.Result):(this._toaster.showSuccess("Employee Data Sync Status - "+e.Success,""+e.Result,5e3),this.runApiLoadingFlag=!1,this.lowerLimitOnSubSeqSync=0,this.lowerLimitOnSubSeqSyncPayloadSize=0,e.Result)})),e=>{this.runApiLoadingFlag=!1,this.lowerLimitOnSubSeqSync=0,this.lowerLimitOnSubSeqSyncPayloadSize=0,this._toaster.showInfo("Employee Data Sync Status - processing","Employee Data Sync Inprogress",5e3)})}))}latestRunCheck(){pt.a.fire({title:"Enter Run Id",input:"text",inputPlaceholder:"Run Id",showCancelButton:!0,confirmButtonText:"Submit",cancelButtonText:"Cancel"}).then(e=>Object(r.c)(this,void 0,void 0,(function*(){e.isConfirmed&&(this.latestRunId=Number(e.value),this.tableData=yield this.getTableData(this.filterConfig))})))}ilsEDComparision(){return Object(r.c)(this,void 0,void 0,(function*(){this.ilsEDComaprionLoadingFlag=!0,this.api.ilsEDComparision().subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){1==e.Success&&(this.refreshUIData(),this._toaster.showSuccess("Comparision Process Completed","ILS - ED",5e3),this.ilsEDComaprionLoadingFlag=!1)})),e=>{this.ilsEDComaprionLoadingFlag=!1,this._toaster.showInfo("Data Comparision - processing","Employee Data Comparision Inprogress",5e3)})}))}rerunForCustomEmployee(){pt.a.fire({title:"Enter Employee Id to perform rerun",input:"text",inputPlaceholder:"Employee Id",showCancelButton:!0,confirmButtonText:"Submit",cancelButtonText:"Cancel"}).then(e=>Object(r.c)(this,void 0,void 0,(function*(){if(e.isConfirmed){const t=e.value;this.runApiLoadingFlag=!0,this.api.subsequentEndpointCall("postToUsers",this.onSyncBatchSize,this.lowerLimitOnSubSeqSync,this.currentItemId,t).subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){1==e.Success?(this._toaster.showSuccess(`Employee Data Sync Status - ${e.Success} for employee_id - ${t}`,""+e.Result,5e3),this.runApiLoadingFlag=!1):(this._toaster.showInfo(`Employee Data Sync Status - ${e.Success} for employee_id - ${t}`,""+e.Result,5e3),this.runApiLoadingFlag=!1)})),e=>{this.runApiLoadingFlag=!1,this._toaster.showInfo("Employee Data Sync Status - processing Status","Error on Employee Data Sync for employee_id - "+t,5e3)})}})))}refreshUIData(){return Object(r.c)(this,void 0,void 0,(function*(){this.latestRunId=0,this.tableData=yield this.getTableData(this.filterConfig)}))}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275directiveInject"](y),s["\u0275\u0275directiveInject"](N.a))},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-ils-grid-data-landing-page"]],features:[s["\u0275\u0275ProvidersFeature"]([{provide:w.c,useClass:ht.c,deps:[w.f,ht.a]},{provide:w.e,useValue:Zn}])],decls:27,vars:14,consts:[[1,"container-fluid"],[1,"col-12"],[1,"row"],[1,"col-1"],["mat-raised-button","","matTooltip","ILS data grid",1,"btn-active",2,"font-weight","normal","margin-left","1%","margin-bottom","1%","margin-top","6px","color","white","background-color","red",3,"matMenuTriggerFor"],[1,"h-50"],["menu","matMenu"],["mat-menu-item","","class","matOptions mr-5",4,"ngFor","ngForOf"],["class","col-1",4,"ngIf"],[1,"col-2",2,"padding-top","15px","color","red"],[1,"col-2","pt-3"],[1,"headerClass"],["class","col-2 p-2 pr-0",4,"ngIf"],["class","col-4 pt-3",4,"ngIf"],[1,"col-1","pt-2"],["class","btn btn-danger",3,"disabled","click",4,"ngIf"],["class","btn btn-danger","mat-icon-button","","class","trend-button-inactive","matTooltip","Refresh",3,"disabled","click",4,"ngIf"],["class","col-2",4,"ngIf"],["class","col-2 pt-1 pb-1 ng-star-inserted","style","\n                display: flex;\n                justify-content: end;",4,"ngIf"],["class","row",4,"ngIf"],["class","row container d-flex mb-2 mt-6 flex-column horizontal-item-loading",4,"ngIf"],["mat-menu-item","",1,"matOptions","mr-5"],[1,"row","p-1"],[1,"col-8","p-0",3,"click"],[1,"col","p-0","d-flex","justify-content-end"],[1,"refreshIcon",3,"disabled","click"],[1,"col-2","p-2","pr-0"],["appearance","outline",2,"width","80%"],["matInput","","placeholder","MMMM-YYYY",3,"matDatepicker","formControl"],["matSuffix","",3,"for"],["startView","multi-year",3,"monthSelected"],["datepicker",""],[1,"col-4","pt-3"],["appearance","outline"],[3,"formGroup","rangePicker"],["matStartDate","","formControlName","start","placeholder","Start date"],["matEndDate","","formControlName","end","placeholder","End date"],["picker",""],[1,"btn","btn-danger",3,"disabled","click"],["mat-icon-button","","matTooltip","Refresh",1,"trend-button-inactive",3,"disabled","click"],[1,"iconButton"],[1,"runIdButton",3,"click"],[1,"col-2"],[1,"col-2","pt-1","pb-1","ng-star-inserted",2,"display","flex","justify-content","end"],[1,"btn","btn-danger",2,"margin-right","5px",3,"disabled","click"],[1,"fa",2,"color","#fff",3,"ngClass"],[1,"col-4"],["class","col-3","style","padding-top: 10px;color: red;",4,"ngIf"],[1,"col-3",2,"padding-top","10px","color","red"],[1,"row","container","d-flex","mb-2","mt-6","flex-column","horizontal-item-loading"],[1,"row","justify-content-center"],["diameter","40","matTooltip","Loading ..."],[3,"data","columnConfig","filterConfig"]],template:function(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"div",0),s["\u0275\u0275elementStart"](1,"div",1),s["\u0275\u0275elementStart"](2,"div",2),s["\u0275\u0275elementStart"](3,"div",3),s["\u0275\u0275elementStart"](4,"button",4),s["\u0275\u0275text"](5,"Tables"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](6,"mat-menu",5,6),s["\u0275\u0275template"](8,Tn,5,1,"button",7),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](9,Ln,4,1,"div",8),s["\u0275\u0275elementStart"](10,"div",9),s["\u0275\u0275text"](11),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](12,"div",10),s["\u0275\u0275elementStart"](13,"span",11),s["\u0275\u0275text"](14,"ILS Admin Functions"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](15,Nn,8,3,"div",12),s["\u0275\u0275template"](16,Bn,10,3,"div",13),s["\u0275\u0275elementStart"](17,"div",14),s["\u0275\u0275template"](18,qn,2,1,"button",15),s["\u0275\u0275template"](19,$n,3,1,"button",16),s["\u0275\u0275elementEnd"](),s["\u0275\u0275element"](20,"div",3),s["\u0275\u0275template"](21,Vn,3,0,"div",8),s["\u0275\u0275template"](22,zn,3,0,"div",17),s["\u0275\u0275template"](23,Jn,6,5,"div",18),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](24,Yn,3,1,"div",19),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](25,Kn,3,0,"div",20),s["\u0275\u0275template"](26,Qn,3,3,"div",19)),2&e){const e=s["\u0275\u0275reference"](7);s["\u0275\u0275advance"](4),s["\u0275\u0275property"]("matMenuTriggerFor",e),s["\u0275\u0275advance"](4),s["\u0275\u0275property"]("ngForOf",t.displaymenuMasterData),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",t.isDateFilter),s["\u0275\u0275advance"](2),s["\u0275\u0275textInterpolate1"](" ",t.displayName," "),s["\u0275\u0275advance"](4),s["\u0275\u0275property"]("ngIf",t.isDateFilter),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",t.isDateRangeFilter),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("ngIf",1==t.isDateRangeFilter),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",0==t.isDateRangeFilter),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("ngIf",t.isRunIdApiHit||t.is_runid_based),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",!t.runApiLoadingFlag),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",t.isRunIdApiHit),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",t.isDateRangeFilter),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",t.isLoading),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",!t.isLoading&&!t.showTags)}},directives:[b.a,v.a,qe.f,qe.g,i.NgForOf,i.NgIf,qe.d,_.a,C.c,C.g,x.b,ft.g,c.e,c.v,c.k,ft.i,C.i,ft.f,ft.d,c.w,c.n,ft.l,c.l,ft.k,ft.e,i.NgClass,P.c,Rn],styles:[".container-fluid[_ngcontent-%COMP%]   .headerClass[_ngcontent-%COMP%]{font-size:medium;font-weight:500;color:red}.container-fluid[_ngcontent-%COMP%]   .matOptions[_ngcontent-%COMP%]:hover   .newTabIcon[_ngcontent-%COMP%]{font-size:21px!important;color:#545352!important}.container-fluid[_ngcontent-%COMP%]   .newTabIcon[_ngcontent-%COMP%]{color:#fff}.container-fluid[_ngcontent-%COMP%]   .example-month-picker[_ngcontent-%COMP%]   .mat-calendar-period-button[_ngcontent-%COMP%]{pointer-events:none}.container-fluid[_ngcontent-%COMP%]   .example-month-picker[_ngcontent-%COMP%]   .mat-calendar-arrow[_ngcontent-%COMP%]{display:none}.container-fluid[_ngcontent-%COMP%]   .dateRangeButton[_ngcontent-%COMP%], .refreshIcon[_ngcontent-%COMP%]{border:none;background-color:initial}.refreshIcon[_ngcontent-%COMP%]{margin-right:10px;margin-top:-3px;color:#66615b;padding-top:11px}.runIdButton[_ngcontent-%COMP%]{background-color:initial;margin-top:9px;margin-left:12px;border:1px solid red}"]}),e})(),data:{breadcrumb:"admin"}},{path:"reports",component:(()=>{class e{constructor(e,t,n){this.api=e,this._toaster=t,this.fb=n,this._onDestroy=new u.b,this.dataSource=[],this.columnName=[],this.displayedColumns=[],this.noData=!1,this.isLoading=!1,this.displayColumn=[],this.masterData=[{key:"Master Tax",value:1},{key:"Master Customer",value:2},{key:"Master Department",value:3},{key:"Master Vendor",value:4},{key:"Master Account",value:5},{key:"Master Cost Center",value:6}],this.ngOnDestroy=()=>{this._onDestroy.next(),this._onDestroy.complete()}}ngOnInit(){this.dropDownForm=this.fb.group({tableSelect:[1]}),this.getTableChange()}getTableChange(){2==this.dropDownForm.value.tableSelect?(this.isLoading=!0,this.noData=!0,this.displayColumn=[],this.api.getExceptionCustomerData().pipe(Object(m.a)(this._onDestroy)).subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){if(1==e.Success){this.columnName=T.keys(e.Exceptional_Customer_Report[0]);for(let e=0;e<this.columnName.length;e++)this.displayColumn.push({caption:this.columnName[e],dataField:this.columnName[e]});this.dataSource=e.Exceptional_Customer_Report,this.isLoading=!1,this.noData=!1,this.totalCount=e.Exceptional_Customer_Report.length}else this.isLoading=!1,this.noData=!0,this._toaster.showWarning("No data Found!","warning")})),e=>{console.log(e)})):3==this.dropDownForm.value.tableSelect?(this.isLoading=!0,this.noData=!0,this.displayColumn=[],this.api.getExceptionDepartmentData().pipe(Object(m.a)(this._onDestroy)).subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){if(1==e.Success){this.columnName=T.keys(e.Exceptional_Department_Report[0]);for(let e=0;e<this.columnName.length;e++)this.displayColumn.push({caption:this.columnName[e],dataField:this.columnName[e]});this.dataSource=e.Exceptional_Department_Report,this.isLoading=!1,this.totalCount=e.Exceptional_Department_Report.length}else this.isLoading=!1,this.noData=!0,this._toaster.showWarning("No data Found!","warning")})),e=>{console.log(e)})):4==this.dropDownForm.value.tableSelect?(this.isLoading=!0,this.noData=!0,this.displayColumn=[],this.api.getExceptionVendorData().pipe(Object(m.a)(this._onDestroy)).subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){if(1==e.Success){console.log(e.Exceptional_Vendor_Report),this.columnName=T.keys(e.Exceptional_Vendor_Report[0]);for(let e=0;e<this.columnName.length;e++)this.displayColumn.push({caption:this.columnName[e],dataField:this.columnName[e]});this.dataSource=e.Exceptional_Vendor_Report,this.isLoading=!1,this.noData=!1,this.totalCount=e.Exceptional_Vendor_Report.length}else this.isLoading=!1,this.noData=!0,this._toaster.showWarning("No data Found!","warning")})),e=>{console.log(e)})):5==this.dropDownForm.value.tableSelect?(this.isLoading=!0,this.noData=!0,this.displayColumn=[],this.api.getExceptionAccountData().pipe(Object(m.a)(this._onDestroy)).subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){if(1==e.Success){console.log(e.Exceptional_Account_Report),this.columnName=T.keys(e.Exceptional_Account_Report[0]);for(let e=0;e<this.columnName.length;e++)this.displayColumn.push({caption:this.columnName[e],dataField:this.columnName[e]});this.dataSource=e.Exceptional_Account_Report,this.isLoading=!1,this.noData=!1,this.totalCount=e.Exceptional_Account_Report.length}else this.isLoading=!1,this.noData=!0,this._toaster.showWarning("No data Found!","warning")})),e=>{console.log(e)})):1==this.dropDownForm.value.tableSelect?(this.isLoading=!0,this.noData=!0,this.displayColumn=[],this.api.getExceptionTaxData().pipe(Object(m.a)(this._onDestroy)).subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){if(1==e.Success){this.columnName=T.keys(e.Exceptional_Tax_Report[0]);for(let e=0;e<this.columnName.length;e++)this.displayColumn.push({caption:this.columnName[e],dataField:this.columnName[e]});this.dataSource=e.Exceptional_Tax_Report,this.isLoading=!1,this.noData=!1,this.totalCount=e.Exceptional_Tax_Report.length}else this.isLoading=!1,this.noData=!0,this._toaster.showWarning("No data Found!","warning")})),e=>{console.log(e)})):(this.isLoading=!0,this.noData=!0,this.displayColumn=[],this.api.getExceptionCostCenterData().pipe(Object(m.a)(this._onDestroy)).subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){if(1==e.Success){console.log(e.Exceptional_Class_Report),this.columnName=T.keys(e.Exceptional_Class_Report[0]);for(let e=0;e<this.columnName.length;e++)this.displayColumn.push({caption:this.columnName[e],dataField:this.columnName[e]});this.dataSource=e.Exceptional_Class_Report,this.isLoading=!1,this.noData=!1,this.totalCount=e.Exceptional_Class_Report.length}else this.isLoading=!1,this.noData=!0,this._toaster.showWarning("No data Found!","warning")})),e=>{console.log(e)}))}setSearchText(e){this.dataSource.filter=e.trim().toLowerCase(),this.dataSource.paginator&&this.dataSource.paginator.firstPage()}clearSearch(){this.dataSource.filter=""}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275directiveInject"](y),s["\u0275\u0275directiveInject"](N.a),s["\u0275\u0275directiveInject"](c.i))},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-exception-report"]],decls:12,vars:6,consts:[[1,"container-fliud"],[3,"formGroup"],[1,"col-12",2,"margin-top","20px"],[1,"row"],[1,"col-2"],["appearance","outline",2,"width","200px","height","100%"],["formControlName","tableSelect","required","true","placeholder","Master Data",3,"selectionChange"],[3,"value",4,"ngFor","ngForOf"],["class","col-3 ml-4 heading",4,"ngIf"],["class","col-3 heading",4,"ngIf"],["class","row container d-flex mb-2 mt-6 flex-column horizontal-item-loading",4,"ngIf"],["class","row",4,"ngIf"],[3,"value"],[1,"col-3","ml-4","heading"],[1,"col-3","heading"],[1,"row","container","d-flex","mb-2","mt-6","flex-column","horizontal-item-loading"],[1,"row","justify-content-center"],["diameter","40","matTooltip","Loading ..."],[1,"col-12"],[3,"data","columnConfig"]],template:function(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"div",0),s["\u0275\u0275elementStart"](1,"form",1),s["\u0275\u0275elementStart"](2,"div",2),s["\u0275\u0275elementStart"](3,"div",3),s["\u0275\u0275elementStart"](4,"div",4),s["\u0275\u0275elementStart"](5,"mat-form-field",5),s["\u0275\u0275elementStart"](6,"mat-select",6),s["\u0275\u0275listener"]("selectionChange",(function(){return t.getTableChange()})),s["\u0275\u0275template"](7,Wn,2,2,"mat-option",7),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](8,Xn,2,1,"div",8),s["\u0275\u0275template"](9,ei,2,0,"div",9),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](10,ti,3,0,"div",10),s["\u0275\u0275template"](11,ni,3,2,"div",11),s["\u0275\u0275elementEnd"]()),2&e&&(s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("formGroup",t.dropDownForm),s["\u0275\u0275advance"](6),s["\u0275\u0275property"]("ngForOf",t.masterData),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",!t.isLoading),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",!t.isLoading),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",t.isLoading),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",!t.isLoading))},directives:[c.J,c.w,c.n,C.c,O.c,c.v,c.l,c.F,i.NgForOf,i.NgIf,w.p,P.c,v.a,Rn],styles:["table[_ngcontent-%COMP%]{width:100%}td.mat-cell[_ngcontent-%COMP%], th.mat-header-cell[_ngcontent-%COMP%]{padding:20px}.editIcon[_ngcontent-%COMP%]{cursor:pointer}.infinite-scroll-auto[_ngcontent-%COMP%]{height:470px;overflow:scroll}.configIcon[_ngcontent-%COMP%]{border:none;background-color:initial;margin-top:10px;float:right}.heading[_ngcontent-%COMP%]{width:263px;height:24px;font-family:Roboto;font-style:normal;font-weight:500;font-size:14px;line-height:50px;display:flex;align-items:center;text-transform:capitalize;text-align:center;color:#ff3a46!important;margin-top:13px}"]}),e})(),data:{breadcrumb:"reports"}},{path:"oAuth",component:(()=>{class e{constructor(e,t){this.route=e,this.api=t,this.$onDestroy=new u.b,this.$onAppApiCalled=new u.b}ngOnInit(){var e,t;const n=window.location;console.log("current_location - "+n);const i=window.location.search;console.log("current_location_search - "+i);const o=new URLSearchParams(i);console.log("current urlParams - ",o);const s=o.get("code");console.log("current code - ",s);const a=o.get("realmId");console.log("current realmId - ",a);const r=o.get("state");console.log("current state - ",r),(null===(e=localStorage.getItem("OAuthURLCallbackILS"))||void 0===e?void 0:e.includes("intuit"))?this.api.callback({code:s,realmId:a,state:r}).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{console.log("token here for qb response - ",e),localStorage.setItem("OAuthURLCallbackILS","none"),window.close()}):(null===(t=localStorage.getItem("OAuthURLCallbackILS"))||void 0===t?void 0:t.includes("zoho"))?this.api.callbackZoho({code:s,realmId:a,state:r}).pipe(Object(m.a)(this.$onAppApiCalled)).subscribe(e=>{console.log("token here for zb response - ",e),localStorage.setItem("OAuthURLCallbackILS","none"),window.close()}):(console.log("neither Zoho nor Quickbooks"),localStorage.setItem("OAuthURLCallbackILS","none"),window.close())}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275directiveInject"](o.a),s["\u0275\u0275directiveInject"](y))},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-oauth2-token"]],decls:2,vars:0,template:function(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"p"),s["\u0275\u0275text"](1,"Token Binded"),s["\u0275\u0275elementEnd"]())},styles:[""]}),e})(),data:{breadcrumb:"oAuth"}}]}];let oi=(()=>{class e{}return e.\u0275mod=s["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=s["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[o.k.forChild(ii)],o.k]}),e})();var si=n("XhcP"),ai=n("lVl8"),ri=n("bSwM"),li=n("Xi0T"),ci=n("1jcm"),di=n("/1cH"),pi=n("f0Cb");let hi=(()=>{class e{}return e.\u0275mod=s["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=s["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,li.a,oi,c.p,c.E,b.b,g.g,x.c,d.b,p.m,C.e,Te.c,_.b,si.g,S.f,$e.d,O.d,ri.b,gt.g,P.b,ft.h,w.n,ht.b,v.b,ai.b,q.b,qe.e,bn.b,ci.b,mt.b,di.c,pi.b]]}),e})()}}]);