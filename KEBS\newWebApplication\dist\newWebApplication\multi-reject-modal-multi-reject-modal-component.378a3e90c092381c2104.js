(window.webpackJsonp=window.webpackJsonp||[]).push([[823],{ClqF:function(e,t,n){"use strict";n.r(t),n.d(t,"MultiRejectModalComponent",(function(){return h}));var l=n("0IaG"),i=n("XNiG"),o=n("xG9w"),a=n("fXoL"),r=n("1A3m"),s=n("ofXK"),c=n("kmnG"),d=n("qFsG"),m=n("3Pt+"),p=n("me71");const g=function(){return{background:"#D4D6D8"}},u=function(){return{background:"#FFFFFF"}};function f(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"div",14),a["\u0275\u0275elementStart"](1,"button",15),a["\u0275\u0275listener"]("click",(function(){a["\u0275\u0275restoreView"](e);const n=t.$implicit;return a["\u0275\u0275nextContext"]().rejectOnlySelectedItem(n)})),a["\u0275\u0275element"](2,"app-user-image",16),a["\u0275\u0275elementStart"](3,"div",17),a["\u0275\u0275text"](4),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngStyle",e.selected?a["\u0275\u0275pureFunction0"](3,g):a["\u0275\u0275pureFunction0"](4,u)),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("id",e.oid),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate"](e.name?e.name:e.employeeName)}}let h=(()=>{class e{constructor(e,t,n,l){this.dialogRef=e,this.rejectModalData=t,this.dialog=n,this._toasterService=l,this.reason="",this.deatilViewData=[],this.text="everyone",this._onDestroy=new i.b}ngOnInit(){this.deatilViewData=this.rejectModalData.modalParams.data;for(let e of this.deatilViewData)e.selected=!1}closeModal(){this.dialogRef.close({event:"close"})}rejectLeaveRequest(){if(""==this.reason)return this._toasterService.showWarning("Inbox Message","Kindly Fill The Reason For Rejection");let e=o.default.filter(this.deatilViewData,{selected:!0});e=e.length>0?e:this.deatilViewData,this.dialogRef.close({event:"Reject",reason:this.reason,data:this.deatilViewData})}rejectOnlySelectedItem(e){0==o.default.filter(this.deatilViewData,{selected:!0})?(e.selected=!e.selected,this.text=e.selected?e.name:"everyone"):this._toasterService.showInfo("Inbox Message","You can reject only single person at a time",3e3)}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275directiveInject"](l.h),a["\u0275\u0275directiveInject"](l.a),a["\u0275\u0275directiveInject"](l.b),a["\u0275\u0275directiveInject"](r.a))},e.\u0275cmp=a["\u0275\u0275defineComponent"]({type:e,selectors:[["app-multi-reject-modal"]],decls:25,vars:3,consts:[[1,"container-fluild","pl-2","pr-2","multi-reject-modal-styles"],[1,"col-12","p-2"],[1,"row"],[1,"col-12","pb-2",2,"display","flex"],["class","pr-2",4,"ngFor","ngForOf"],[1,"col-12","pb-2"],[1,"mainText"],[1,"subText","pt-2"],[1,"col-12"],[1,"msgText"],["appearance","outline","floatLabel","always",2,"width","450px"],["matInput","","rows","4","cols","50","placeholder","Type Your Message Here","required","",3,"ngModel","ngModelChange"],[1,"btn",2,"background","#DADCE2","color","#45546E",3,"click"],[1,"btn",2,"background","#FFFFFF","border","1px solid #FF3A46","color","#FF3A46","margin-left","15px",3,"click"],[1,"pr-2"],[1,"round-btn",3,"ngStyle","click"],["max-width","300","placement","top","imgWidth","23px","imgHeight","23px",2,"margin","2px",3,"id"],[1,"btn-name","pl-2"]],template:function(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"div",0),a["\u0275\u0275elementStart"](1,"div",1),a["\u0275\u0275elementStart"](2,"div",2),a["\u0275\u0275elementStart"](3,"div",3),a["\u0275\u0275template"](4,f,5,5,"div",4),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](5,"div",2),a["\u0275\u0275elementStart"](6,"div",5),a["\u0275\u0275elementStart"](7,"span",6),a["\u0275\u0275text"](8),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](9,"div",7),a["\u0275\u0275text"](10," This will help them to resolve the issue "),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](11,"div",2),a["\u0275\u0275elementStart"](12,"div",8),a["\u0275\u0275elementStart"](13,"span",9),a["\u0275\u0275text"](14,"Message"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](15,"div",2),a["\u0275\u0275elementStart"](16,"div",8),a["\u0275\u0275elementStart"](17,"mat-form-field",10),a["\u0275\u0275elementStart"](18,"textarea",11),a["\u0275\u0275listener"]("ngModelChange",(function(e){return t.reason=e})),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](19,"div",2),a["\u0275\u0275elementStart"](20,"div",8),a["\u0275\u0275elementStart"](21,"button",12),a["\u0275\u0275listener"]("click",(function(){return t.closeModal()})),a["\u0275\u0275text"](22,"Cancel"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](23,"button",13),a["\u0275\u0275listener"]("click",(function(){return t.rejectLeaveRequest()})),a["\u0275\u0275text"](24,"Reject"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e&&(a["\u0275\u0275advance"](4),a["\u0275\u0275property"]("ngForOf",t.deatilViewData),a["\u0275\u0275advance"](4),a["\u0275\u0275textInterpolate1"]("Let ",t.text," know the reason"),a["\u0275\u0275advance"](10),a["\u0275\u0275property"]("ngModel",t.reason))},directives:[s.NgForOf,c.c,d.b,m.e,m.F,m.v,m.y,s.NgStyle,p.a],styles:[".multi-reject-modal-styles[_ngcontent-%COMP%]   .round-btn[_ngcontent-%COMP%]{width:auto;height:48px;border:1px solid #6e7b8f;border-radius:31px;display:flex;padding-top:10px}.multi-reject-modal-styles[_ngcontent-%COMP%]   .mainText[_ngcontent-%COMP%]{font-family:DM Sans;font-style:normal;font-weight:700;font-size:18px;line-height:24px;letter-spacing:.02em;color:#111434}.multi-reject-modal-styles[_ngcontent-%COMP%]   .subText[_ngcontent-%COMP%]{font-family:DM Sans;font-style:normal;font-weight:400;font-size:12px;line-height:20px;color:#8b95a5}.multi-reject-modal-styles[_ngcontent-%COMP%]   .msgText[_ngcontent-%COMP%]{font-weight:700;font-size:15px;letter-spacing:.02em;color:#6e7b8f}.multi-reject-modal-styles[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%], .multi-reject-modal-styles[_ngcontent-%COMP%]   .msgText[_ngcontent-%COMP%]{font-family:DM Sans;font-style:normal;line-height:16px;text-transform:capitalize}.multi-reject-modal-styles[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{width:77px;height:40px;border-radius:8px;font-weight:500;font-size:14px;letter-spacing:-.02em}.multi-reject-modal-styles[_ngcontent-%COMP%]   .btn-name[_ngcontent-%COMP%]{font-family:DM Sans;font-style:normal;font-weight:700;font-size:16px;line-height:24px;align-items:center;text-transform:capitalize;color:#26303e}"]}),e})()}}]);