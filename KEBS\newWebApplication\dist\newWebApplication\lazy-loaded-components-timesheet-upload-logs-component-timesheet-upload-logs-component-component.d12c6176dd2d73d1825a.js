(window.webpackJsonp=window.webpackJsonp||[]).push([[764],{rwNs:function(e,t,n){"use strict";n.r(t),n.d(t,"TimesheetUploadLogsComponentComponent",(function(){return w}));var o=n("mrSG"),l=n("XNiG"),r=n("1G5W"),i=n("wd/R"),a=n("fXoL"),s=n("/xgZ"),d=n("LcQX"),m=n("BVzC"),p=n("0IaG"),c=n("NFeN"),g=n("Qu3c"),v=n("jaxi"),u=n("ofXK");function h(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",22),a["\u0275\u0275elementStart"](1,"div",23),a["\u0275\u0275text"](2),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](3,"div",24),a["\u0275\u0275text"](4),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](5,"div",24),a["\u0275\u0275text"](6),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](7,"div",23),a["\u0275\u0275text"](8),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate1"](" ",e.associateId," "),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("matTooltip",e.associateName),a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate1"](" ",e.associateName," "),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("matTooltip",e.fileName),a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate1"](" ",e.fileName," "),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate1"](" ",e.timestamp," ")}}function f(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",18),a["\u0275\u0275elementStart"](1,"div",19),a["\u0275\u0275elementStart"](2,"div",20),a["\u0275\u0275text"](3," Employee Id "),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](4,"div",20),a["\u0275\u0275text"](5," Employee Name "),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](6,"div",20),a["\u0275\u0275text"](7," File Name "),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](8,"div",20),a["\u0275\u0275text"](9," Date "),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](10,h,9,6,"div",21),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"](2);a["\u0275\u0275advance"](10),a["\u0275\u0275property"]("ngForOf",e.uploadLogs)}}function x(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"div",25),a["\u0275\u0275elementStart"](1,"div"),a["\u0275\u0275elementStart"](2,"h4",26),a["\u0275\u0275text"](3," No Logs Found! "),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](4,"div",27),a["\u0275\u0275element"](5,"img",28),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]())}function S(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",15),a["\u0275\u0275template"](1,f,11,1,"div",16),a["\u0275\u0275template"](2,x,6,0,"div",17),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"]();a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",e.uploadLogs.length>0),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",0==e.uploadLogs.length)}}function E(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",22),a["\u0275\u0275elementStart"](1,"div",33),a["\u0275\u0275text"](2),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](3,"div",34),a["\u0275\u0275text"](4),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](5,"div",35),a["\u0275\u0275text"](6),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](7,"div",36),a["\u0275\u0275text"](8),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate1"](" ",e.id," "),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("matTooltip",e.error_code),a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate1"](" ",e.error_code," "),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("matTooltip",e.error_description),a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate1"](" ",e.error_description," "),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("matTooltip",e.solution),a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate1"](" ",e.solution," ")}}function y(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",18),a["\u0275\u0275elementStart"](1,"div",19),a["\u0275\u0275elementStart"](2,"div",29),a["\u0275\u0275text"](3," S No "),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](4,"div",30),a["\u0275\u0275text"](5," Error Code "),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](6,"div",31),a["\u0275\u0275text"](7," Error Description "),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](8,"div",32),a["\u0275\u0275text"](9," Solution "),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](10,E,9,7,"div",21),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"](2);a["\u0275\u0275advance"](10),a["\u0275\u0275property"]("ngForOf",e.errorLogs)}}function b(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"div",25),a["\u0275\u0275elementStart"](1,"div"),a["\u0275\u0275elementStart"](2,"h4",26),a["\u0275\u0275text"](3," No Logs Found! "),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](4,"div",27),a["\u0275\u0275element"](5,"img",28),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]())}function T(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",15),a["\u0275\u0275template"](1,y,11,1,"div",16),a["\u0275\u0275template"](2,b,6,0,"div",17),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"]();a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",e.errorLogs.length>0),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",0==e.errorLogs.length)}}const C=function(e){return{"btn-toggle-selected":e}};let w=(()=>{class e{constructor(e,t,n,o){this._timesheetUploadService=e,this.utilityService=t,this.errorService=n,this.dialogRef=o,this.selectedToggle="upload-logs",this._onAppApiCalled=new l.b,this._onDestroy=new l.b,this.uploadLogs=[],this.errorLogs=[]}ngOnInit(){this._onAppApiCalled.next(),this.getTimesheetUploadLogs()}closeForm(){this.dialogRef.close()}selectToggle(e){this.selectedToggle=e.value,"upload-logs"==this.selectedToggle&&this.getTimesheetUploadLogs(),"error-logs"==this.selectedToggle&&this.getTimesheetErrorLogs()}getTimesheetUploadLogs(){this._timesheetUploadService.getTimesheetLogsData(1).pipe(Object(r.a)(this._onDestroy)).pipe(Object(r.a)(this._onAppApiCalled)).subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){if(e.data&&e.data.length>0&&"S"==e.messType){this.uploadLogs=e.data;for(let e of this.uploadLogs)e.timestamp=i(e.timestamp).utc().format("DD - MMM - YYYY HH : mm : ss")}else this.utilityService.showToastMessage(e.messText)})),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error in Getting Logs",e&&e.params?e.params:e&&e.error?e.error.params:{})})}getTimesheetErrorLogs(){this._timesheetUploadService.getTimesheetErrorCode().pipe(Object(r.a)(this._onDestroy)).pipe(Object(r.a)(this._onAppApiCalled)).subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){e.data&&e.data.length>0&&"S"==e.messType?this.errorLogs=e.data:this.utilityService.showToastMessage(e.messText)})),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error in Getting Logs",e&&e.params?e.params:e&&e.error?e.error.params:{})})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275directiveInject"](s.a),a["\u0275\u0275directiveInject"](d.a),a["\u0275\u0275directiveInject"](m.a),a["\u0275\u0275directiveInject"](p.h))},e.\u0275cmp=a["\u0275\u0275defineComponent"]({type:e,selectors:[["app-timesheet-upload-logs-component"]],decls:21,vars:9,consts:[[1,"timesheet-upload-logs","container-fluid","pt-3"],[1,"col-12"],[1,"row","border-bottom","solid"],[1,"col-11","pt-2","pb-2","d-flex"],["mat-icon-button","",1,"bubble"],[1,"iconButton"],[1,"name","my-auto","ml-2"],[1,"col-1","d-flex","pt-2"],["matTooltip","Close",1,"close-Icon",2,"cursor","pointer","color","#4d4d4b",3,"click"],[1,"col-6","pt-3"],["name","fontStyle","aria-label","Font Style",3,"value","change"],["group","matButtonToggleGroup"],["value","upload-logs",2,"padding","2px 10px !important",3,"ngClass"],["value","error-logs",2,"padding","2px 30px !important",3,"ngClass"],["class","container-fluid my-team-styles pl-0 pr-0",4,"ngIf"],[1,"container-fluid","my-team-styles","pl-0","pr-0"],["class","col-12 pt-3",4,"ngIf"],["class","pt-4",4,"ngIf"],[1,"col-12","pt-3"],[1,"row","headerSection"],[1,"col-3"],["class","row pt-3",4,"ngFor","ngForOf"],[1,"row","pt-3"],[1,"col-3","pt-1","pb-1","item-text"],[1,"col-3","pt-1","pb-1","item-text",3,"matTooltip"],[1,"pt-4"],[1,"d-flex","justify-content-center","align-items-center","mt-0","mb-0","slide-in-top"],[1,"d-flex","justify-content-center","align-items-center","slide-from-down"],["src","https://assets.kebs.app/images/nomilestone.png","height","220","width","250",1,"mt-3"],[1,"col-1"],[1,"col-2"],[1,"col-4"],[1,"col-5"],[1,"col-1","pt-1","pb-1","item-text"],[1,"col-2","pt-1","pb-1","item-text",3,"matTooltip"],[1,"col-4","pt-1","pb-1","item-text",3,"matTooltip"],[1,"col-5","pt-1","pb-1","item-text",3,"matTooltip"]],template:function(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"div",0),a["\u0275\u0275elementStart"](1,"div",1),a["\u0275\u0275elementStart"](2,"div",2),a["\u0275\u0275elementStart"](3,"div",3),a["\u0275\u0275elementStart"](4,"div",4),a["\u0275\u0275elementStart"](5,"mat-icon",5),a["\u0275\u0275text"](6,"history"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](7,"span",6),a["\u0275\u0275text"](8,"Timesheet Upload Logs"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](9,"div",7),a["\u0275\u0275elementStart"](10,"mat-icon",8),a["\u0275\u0275listener"]("click",(function(){return t.closeForm()})),a["\u0275\u0275text"](11,"close"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](12,"div",9),a["\u0275\u0275elementStart"](13,"mat-button-toggle-group",10,11),a["\u0275\u0275listener"]("change",(function(e){return t.selectToggle(e)})),a["\u0275\u0275elementStart"](15,"mat-button-toggle",12),a["\u0275\u0275text"](16,"Timesheet Upload History"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](17,"mat-button-toggle",13),a["\u0275\u0275text"](18,"Timesheet Error Codes "),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](19,S,3,2,"div",14),a["\u0275\u0275template"](20,T,3,2,"div",14),a["\u0275\u0275elementEnd"]()),2&e&&(a["\u0275\u0275advance"](13),a["\u0275\u0275property"]("value",t.selectedToggle),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("ngClass",a["\u0275\u0275pureFunction1"](5,C,"upload-logs"==t.selectedToggle)),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("ngClass",a["\u0275\u0275pureFunction1"](7,C,"error-logs"==t.selectedToggle)),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("ngIf","upload-logs"==t.selectedToggle),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf","error-logs"==t.selectedToggle))},directives:[c.a,g.a,v.b,v.a,u.NgClass,u.NgIf,u.NgForOf],styles:[".timesheet-upload-logs[_ngcontent-%COMP%]   .btn-toggle-selected[_ngcontent-%COMP%]{background:#ee4961;border-radius:4px 0 0 4px;font-family:Roboto;font-style:normal;font-weight:700;font-size:12px;line-height:16px;letter-spacing:-.02em;text-transform:capitalize;color:#fff}.timesheet-upload-logs[_ngcontent-%COMP%]     .mat-button-toggle-checked{background-color:#ef4a61!important}.timesheet-upload-logs[_ngcontent-%COMP%]   .headerSection[_ngcontent-%COMP%]{font-size:15px;color:#26303e}.timesheet-upload-logs[_ngcontent-%COMP%]   .headerSection[_ngcontent-%COMP%], .timesheet-upload-logs[_ngcontent-%COMP%]   .item-text[_ngcontent-%COMP%]{font-family:Roboto;overflow:hidden;text-overflow:ellipsis;font-weight:500;white-space:nowrap}.timesheet-upload-logs[_ngcontent-%COMP%]   .item-text[_ngcontent-%COMP%]{font-size:13px;color:#4c4d4e}.timesheet-upload-logs[_ngcontent-%COMP%]   .name[_ngcontent-%COMP%]{font-size:14px;color:#ef4a61;font-family:Roboto;font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}"]}),e})()}}]);