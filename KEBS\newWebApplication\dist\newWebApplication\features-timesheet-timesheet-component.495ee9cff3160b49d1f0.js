(window.webpackJsonp=window.webpackJsonp||[]).push([[683],{cQQ7:function(e,t,n){"use strict";n.r(t),n.d(t,"TimesheetComponent",(function(){return w})),n.d(t,"timesheetModule",(function(){return M}));var o=n("fXoL"),i=n("ofXK"),s=n("Xi0T"),l=n("kmnG"),r=n("qFsG"),c=n("iadO"),a=n("FKr1"),d=n("NFeN"),m=n("bTqV"),g=n("Xa2L"),u=n("Qu3c"),p=n("d3UM"),h=n("jaxi"),f=n("1yaQ");const C=["contentContainer"],v=function(e){return{"btn-toggle-selected":e}};function b(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"mat-button-toggle",9),o["\u0275\u0275text"](1),o["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=o["\u0275\u0275nextContext"]();o["\u0275\u0275property"]("value",e.name)("ngClass",o["\u0275\u0275pureFunction1"](3,v,n.selectedToggle==e.name)),o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" ",e.name,"")}}let w=(()=>{class e{constructor(e){this._compiler=e,this.sectionList=[{id:1,name:"Awaiting Requests",isSelected:!0},{id:2,name:"Previous Requests",isSelected:!1}]}ngOnInit(){this.selectedToggle=this.sectionList[0].name,this.loadSectionTab({value:this.selectedToggle})}handleSectionSelect(e){this.sectionList.forEach((t,n)=>{t.isSelected=t.id==e.id}),this.loadSectionTab(e)}loadSectionTab(e){switch(this.selectedToggle=e.value,this.contentContainerRef&&this.contentContainerRef.clear(),this.selectedToggle){case"Awaiting Requests":this.loadAwaitingRequestContainer();break;case"Previous Requests":this.loadPreviousRequestContainer()}}loadAwaitingRequestContainer(){Promise.all([n.e(0),n.e(254)]).then(n.bind(null,"Tndo")).then(e=>{const t=this._compiler.compileModuleSync(e.AwaitingRequestModule).create(this.contentContainerRef.injector).componentFactoryResolver.resolveComponentFactory(e.AwaitingRequestComponent);this.contentContainerRef.createComponent(t)})}loadPreviousRequestContainer(){Promise.all([n.e(0),n.e(313)]).then(n.bind(null,"XAQM")).then(e=>{const t=this._compiler.compileModuleSync(e.PreviousRequestModule).create(this.contentContainerRef.injector).componentFactoryResolver.resolveComponentFactory(e.PreviousRequestComponent);this.contentContainerRef.createComponent(t)})}ngOnDestroy(){this.contentContainerRef&&this.contentContainerRef.clear()}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](o.Compiler))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-timesheet"]],viewQuery:function(e,t){if(1&e&&o["\u0275\u0275viewQuery"](C,!0,o.ViewContainerRef),2&e){let e;o["\u0275\u0275queryRefresh"](e=o["\u0275\u0275loadQuery"]())&&(t.contentContainerRef=e.first)}},decls:11,vars:2,consts:[[1,"container-fluid","timesheet-styles"],[1,"row","pt-0"],[1,"col-9","pl-0","pr-0"],[1,"row"],[1,"col-10","pl-0","pr-0"],[3,"value","change"],["class","toggle-btn",3,"value","ngClass",4,"ngFor","ngForOf"],[1,"col-12","p-0"],["contentContainer",""],[1,"toggle-btn",3,"value","ngClass"]],template:function(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"div",0),o["\u0275\u0275elementStart"](1,"div",1),o["\u0275\u0275elementStart"](2,"div",2),o["\u0275\u0275elementStart"](3,"div",3),o["\u0275\u0275elementStart"](4,"div",4),o["\u0275\u0275elementStart"](5,"mat-button-toggle-group",5),o["\u0275\u0275listener"]("change",(function(e){return t.handleSectionSelect(e)})),o["\u0275\u0275template"](6,b,2,5,"mat-button-toggle",6),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](7,"div",3),o["\u0275\u0275elementStart"](8,"div",7),o["\u0275\u0275elementContainer"](9,null,8),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e&&(o["\u0275\u0275advance"](5),o["\u0275\u0275property"]("value",t.selectedToggle),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngForOf",t.sectionList))},directives:[h.b,i.NgForOf,h.a,i.NgClass],styles:[".timesheet-styles[_ngcontent-%COMP%]   .toggle-btn[_ngcontent-%COMP%]{min-width:7rem}.timesheet-styles[_ngcontent-%COMP%]   .btn-toggle-selected[_ngcontent-%COMP%]{font-size:12px!important;background-color:#ee4961!important;color:#fff}.timesheet-styles[_ngcontent-%COMP%]     .mat-button-toggle-label-content{font-size:12px!important}.timesheet-styles[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{color:#5f5f5f;font-size:11px!important}.timesheet-styles[_ngcontent-%COMP%]   .overflow[_ngcontent-%COMP%]{height:28rem;overflow:scroll;overflow-x:hidden}.timesheet-styles[_ngcontent-%COMP%]   .normalFont[_ngcontent-%COMP%]{color:#181818;font-size:14px;font-weight:600;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.timesheet-styles[_ngcontent-%COMP%]   .desc[_ngcontent-%COMP%]{max-height:8rem;overflow:scroll;overflow-x:hidden;overflow-y:auto}.timesheet-styles[_ngcontent-%COMP%]   .icon-button-inactive[_ngcontent-%COMP%]{background-color:#f1f1f1;line-height:8px;width:26px;height:26px;margin-right:15px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.timesheet-styles[_ngcontent-%COMP%]   .icon-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}"]}),e})(),M=(()=>{class e{}return e.\u0275mod=o["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=o["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},providers:[{provide:a.c,useClass:f.c,deps:[a.f,f.a]},{provide:a.e,useValue:{parse:{dateInput:"DD-MM-YYYY"},display:{dateInput:"DD-MM-YYYY",monthYearLabel:"MMM YYYY"}}}],imports:[[i.CommonModule,s.a,l.e,r.c,c.h,a.x,d.b,m.b,g.b,u.b,p.d,h.c]]}),e})()}}]);