(window.webpackJsonp=window.webpackJsonp||[]).push([[862,634,765,783,821,822,858,861,977,981,983,987,990,991],{H44p:function(e,t,i){"use strict";i.d(t,"a",(function(){return f}));var r=i("xG9w"),n=i("fXoL"),a=i("flaP"),s=i("ofXK"),o=i("Qu3c"),l=i("NFeN");function c(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"div",9),n["\u0275\u0275elementStart"](1,"div",10),n["\u0275\u0275elementStart"](2,"div"),n["\u0275\u0275text"](3),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](4,"div"),n["\u0275\u0275elementStart"](5,"p",11),n["\u0275\u0275text"](6),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](7,"p",12),n["\u0275\u0275text"](8),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e){const e=n["\u0275\u0275nextContext"](2);n["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),n["\u0275\u0275advance"](3),n["\u0275\u0275textInterpolate"](null==e.currency[e.index]?null:e.currency[e.index].currency_code),n["\u0275\u0275advance"](3),n["\u0275\u0275textInterpolate"](e.label),n["\u0275\u0275advance"](2),n["\u0275\u0275textInterpolate1"](" ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function d(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"div",13),n["\u0275\u0275elementStart"](1,"span"),n["\u0275\u0275text"](2),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e){const e=n["\u0275\u0275nextContext"](2);n["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),n["\u0275\u0275advance"](2),n["\u0275\u0275textInterpolate1"](" ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function u(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"div",14),n["\u0275\u0275elementStart"](1,"span",15),n["\u0275\u0275text"](2),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e){const e=n["\u0275\u0275nextContext"](2);n["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),n["\u0275\u0275advance"](2),n["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function h(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"div",16),n["\u0275\u0275elementStart"](1,"span",15),n["\u0275\u0275text"](2),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e){const e=n["\u0275\u0275nextContext"](2);n["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),n["\u0275\u0275advance"](2),n["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function p(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"div",17),n["\u0275\u0275elementStart"](1,"span",18),n["\u0275\u0275text"](2),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e){const e=n["\u0275\u0275nextContext"](2);n["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),n["\u0275\u0275advance"](2),n["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function g(e,t){1&e&&(n["\u0275\u0275elementStart"](0,"mat-icon",19),n["\u0275\u0275text"](1,"loop"),n["\u0275\u0275elementEnd"]())}function m(e,t){if(1&e){const e=n["\u0275\u0275getCurrentView"]();n["\u0275\u0275elementStart"](0,"div",1),n["\u0275\u0275listener"]("click",(function(){return n["\u0275\u0275restoreView"](e),n["\u0275\u0275nextContext"]().change()})),n["\u0275\u0275template"](1,c,9,4,"div",2),n["\u0275\u0275template"](2,d,3,2,"div",3),n["\u0275\u0275template"](3,u,3,3,"div",4),n["\u0275\u0275template"](4,h,3,3,"div",5),n["\u0275\u0275template"](5,p,3,3,"div",6),n["\u0275\u0275elementStart"](6,"div",7),n["\u0275\u0275template"](7,g,2,0,"mat-icon",8),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()}if(2&e){const e=n["\u0275\u0275nextContext"]();n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf","big"==e.type),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf","small"==e.type),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf","medium"==e.type),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf","large"==e.type),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf","overview"==e.type),n["\u0275\u0275advance"](2),n["\u0275\u0275property"]("ngIf",e.toDisplay)}}let f=(()=>{class e{constructor(e){this.roleService=e,this.showActualAmount=!1,this.currency=[]}set currencyList(e){if(this.currency=e,e){const t=r.findIndex(e,e=>e.currency_code==this.currency_code);-1!=t&&(this.index=t,this.currency[this.index].value=Math.trunc(this.currency[this.index].value))}}ngOnInit(){null==this.toDisplay&&(this.toDisplay=!0),this.tenant_info=this.roleService.currency_info,this.tenantName=this.tenant_info.tenant_name,this.currency_code=this.defaultCurrencyCode?this.defaultCurrencyCode:this.tenant_info.default_currency?this.tenant_info.default_currency:"INR",this.setCurrency(),this.isConvertValue=null==this.tenant_info.is_to_convert_currency_value||this.tenant_info.is_to_convert_currency_value}setCurrency(){if(this.currency){const e=r.findIndex(this.currency,e=>e.currency_code==this.currency_code);-1!=e&&(this.index=e,this.currency[this.index].value=Math.trunc(this.currency[this.index].value))}}change(){void 0!==event.stopPropagation?event.stopPropagation():void 0!==event.cancelBubble&&(event.cancelBubble=!0),this.currency.length>1&&(this.index=++this.index%this.currency.length),this.currency[this.index].value=Math.trunc(this.currency[this.index].value)}convert(e,t){return e?(this.showActualAmount?e=new Intl.NumberFormat("INR"==t?"en-IN":"en-US",{}).format(e)+" "+t:1==this.isConvertValue?e="INR"==t?(e/1e7).toFixed(2)+" Cr":(e/1e6).toFixed(2)+" M":0!=this.isConvertValue||r.contains(["big","small"],this.type)?0==this.isConvertValue&&r.contains(["big","small"],this.type)&&(e="INR"==t?"big"!=this.type?"INR "+new Intl.NumberFormat("en-IN",{}).format(e):new Intl.NumberFormat("en-IN",{}).format(e):"big"!=this.type?t+" "+new Intl.NumberFormat("en-US",{}).format(e):new Intl.NumberFormat("en-US",{}).format(e)):e="INR"==t?(e/1e7).toFixed(2)+" Cr":(e/1e6).toFixed(2)+" M",e):"-"}}return e.\u0275fac=function(t){return new(t||e)(n["\u0275\u0275directiveInject"](a.a))},e.\u0275cmp=n["\u0275\u0275defineComponent"]({type:e,selectors:[["app-currency"]],inputs:{type:"type",label:"label",showActualAmount:"showActualAmount",acl:"acl",toDisplay:"toDisplay",defaultCurrencyCode:"defaultCurrencyCode",currencyList:"currencyList"},decls:1,vars:1,consts:[["class","d-flex currency-wrapper",3,"click",4,"ngIf"],[1,"d-flex","currency-wrapper",3,"click"],["class","d-flex",3,"matTooltip",4,"ngIf"],["class","data-label",3,"matTooltip",4,"ngIf"],["class","data-medium d-flex",3,"matTooltip",4,"ngIf"],["class","data-large d-flex",3,"matTooltip",4,"ngIf"],["class","justify-content-center",3,"matTooltip",4,"ngIf"],[1,"d-flex","align-items-center"],["class","change-icon pl-2","matTooltip","Next currency",4,"ngIf"],[1,"d-flex",3,"matTooltip"],[1,"pr-3","my-auto","currency-code","justify-content-center"],[1,"m-0","header"],[1,"m-0","data-label","pt-1"],[1,"data-label",3,"matTooltip"],[1,"data-medium","d-flex",3,"matTooltip"],[1,"justify-content-center"],[1,"data-large","d-flex",3,"matTooltip"],[1,"justify-content-center",3,"matTooltip"],[1,"data-overview"],["matTooltip","Next currency",1,"change-icon","pl-2"]],template:function(e,t){1&e&&n["\u0275\u0275template"](0,m,8,6,"div",0),2&e&&n["\u0275\u0275property"]("ngIf",t.currency)},directives:[s.NgIf,o.a,l.a],styles:[".header[_ngcontent-%COMP%]{color:#5f5f5f;font-size:11px!important}.data-label[_ngcontent-%COMP%]{font-size:13px!important}.change-icon[_ngcontent-%COMP%]{font-size:18px;width:auto;height:auto;visibility:hidden;cursor:default}.currency-code[_ngcontent-%COMP%]{padding-right:8px;font-size:14px!important}.currency-wrapper[_ngcontent-%COMP%]{white-space:nowrap!important;height:100%}.currency-wrapper[_ngcontent-%COMP%]:hover   .change-icon[_ngcontent-%COMP%]{visibility:visible}.data-medium[_ngcontent-%COMP%]{color:#252422!important;font-size:15px!important}.data-large[_ngcontent-%COMP%], .data-medium[_ngcontent-%COMP%]{text-align:center!important;font-weight:440!important;padding-right:4px!important}.data-large[_ngcontent-%COMP%]{font-size:large!important}.data-overview[_ngcontent-%COMP%]{color:#252422!important;font-size:15px!important;padding-left:24px!important;font-weight:500!important;padding-right:4px!important;text-align:center!important;margin:auto!important}"]}),e})()},HmYF:function(e,t,i){"use strict";i.d(t,"a",(function(){return c}));var r=i("mrSG"),n=i("Iab2"),a=i("EUZL"),s=i("wd/R"),o=i("xG9w"),l=i("fXoL");let c=(()=>{class e{constructor(){this.formatColumn=(e,t,i)=>{const r=a.utils.decode_range(e["!ref"]);for(let n=r.s.r+1;n<=r.e.r;++n){const r=a.utils.encode_cell({r:n,c:t});e[r]&&e[r].v&&(e[r].t="d",e[r].z=i)}}}exportAsExcelFile(e,t,i,r,n){console.log("Excel to JSON Service",e);const s=a.utils.json_to_sheet(e);if(n&&n.length){const e=a.utils.sheet_to_json(s,{header:1}).shift();for(const t of n){const i=e.indexOf(t.fieldKey);this.formatColumn(s,i,t.fieldFormat)}}null==i&&(i=[]),null==r&&(r="DD-MM-YYYY"),this.formatExcelDateData(s,i,r);const o=a.write({Sheets:{data:s},SheetNames:["data"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(o,t)}formatExcelDateData(e,t,i){for(let a of Object.keys(e))if(null!=e[a]&&null!=e[a].t&&null!=e[a].v&&s(e[a].v,i,!0).isValid()){let r=a.replace(/[0-9]/g,"")+"1";0==o.where(t,{value:e[r].v}).length&&null!=e[r]&&null!=e[r].t&&t.push({value:e[r].v,format:i})}let r=[],n=1;for(let a of t)for(let t of Object.keys(e)){let i=parseInt(t.replace(/[^0-9]/g,""));i>n&&(n=i),null!=e[t]&&null!=e[t].v&&e[t].v==a.value&&r.push({value:t.replace(/[0-9]/g,""),format:a.format})}for(let a of r)for(let t=2;t<=n;t++)null!=e[a.value+""+t]&&null!=e[a.value+""+t].t&&(e[a.value+""+t].t="d",null!=e[a.value+""+t].v&&"Invalid date"!=e[a.value+""+t].v?e[a.value+""+t].v=s(e[a.value+""+t].v,a.format).format("YYYY/MM/DD"):(console.log(e[a.value+""+t].t),e[a.value+""+t].v="",e[a.value+""+t].t="s"))}saveAsExcelFile(e,t){const i=new Blob([e],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8"});n.saveAs(i,t.concat(".xlsx"))}exportAsExcelFileWFH(e,t,i){const r=a.utils.json_to_sheet(e),n=a.utils.json_to_sheet(t),s=a.write({Sheets:{All_Approvals:r,Pending_Approvals:n},SheetNames:["All_Approvals","Pending_Approvals"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(s,i)}exportAsExcelFileForPayroll(e,t,i,r,n,s){const o=a.utils.json_to_sheet(e),l=a.utils.json_to_sheet(t),c=a.utils.json_to_sheet(i),d=a.utils.json_to_sheet(r),u=a.utils.json_to_sheet(n),h=a.write({Sheets:{Regular_Report:o,Intern_Report:l,Contract_Report:c,Perdiem_Report:d,RP_Report:u},SheetNames:["Regular_Report","Intern_Report","Contract_Report","Perdiem_Report","RP_Report"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(h,s)}exportAsCsvFileWithSheetName(e,t){return Object(r.c)(this,void 0,void 0,(function*(){let i=a.utils.book_new();for(let t of e){let e=a.utils.json_to_sheet(t.data);a.utils.book_append_sheet(i,e,t.sheetName)}let r=a.write(i,{bookType:"xlsx",type:"binary"});yield this.saveAsCsvFile(r,t)}))}saveAsCsvFile(e,t){return Object(r.c)(this,void 0,void 0,(function*(){const i=new Blob([yield this.s2ab(e)],{type:"application/octet-stream"});n.saveAs(i,t.concat(".csv"))}))}s2ab(e){return Object(r.c)(this,void 0,void 0,(function*(){for(var t=new ArrayBuffer(e.length),i=new Uint8Array(t),r=0;r<e.length;r++)i[r]=255&e.charCodeAt(r);return t}))}exportAsExcelFileWithCellMerge(e,t,i){const r=a.utils.json_to_sheet(e);r["!merges"]=i;const n=a.write({Sheets:{data:r},SheetNames:["data"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(n,t)}exportJsonToExcelWithMultipleSheets(e,t){return Object(r.c)(this,void 0,void 0,(function*(){let i=a.utils.book_new();for(let t of e){let e=a.utils.json_to_sheet(t.data);a.utils.book_append_sheet(i,e,t.sheetName)}let r=a.write(i,{bookType:"xlsx",type:"array"});this.saveAsExcelFile(r,t)}))}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275prov=l["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},"N1+M":function(e,t,i){"use strict";i.r(t),i.d(t,"PrModule",(function(){return x}));var r=i("ofXK"),n=i("tyNb"),a=i("mrSG"),s=i("R0Ic"),o=i("XNiG"),l=i("xG9w"),c=i("wd/R"),d=i("1G5W"),u=i("fXoL"),h=i("GnQ3"),p=i("BVzC"),g=i("LcQX"),m=i("tk/3");let f=(()=>{class e{constructor(e){this.$http=e,this.getRequestorListUdrf=(e,t)=>this.$http.post("/api/purchaseRequest/getRequestorListUdrf",{filterConfig:e,type:t}),this.getRequestorSummaryCardUdrf=(e,t)=>this.$http.post("/api/purchaseRequest/getRequestorSummaryUdrf",{filterConfig:e,type:t}),this.getVendorType=()=>this.$http.post("/api/purchaseRequest/getVendorType",{})}}return e.\u0275fac=function(t){return new(t||e)(u["\u0275\u0275inject"](m.c))},e.\u0275prov=u["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var y=i("0IaG"),v=i("a1r6"),_=i("HmYF"),C=i("xi/V"),b=i("Wk3H");const S=[{path:"",component:(()=>{class e{constructor(e,t,r,n,s,u,h,p){this._udrfService=e,this._errorService=t,this._utilityService=r,this._prReportService=n,this.dialog=s,this.router=u,this._p2pGeneralService=h,this._excelService=p,this.applicationId=252,this.$onAppApiCalled=new o.b,this.selectedCard=[],this.$onDestroy=new o.b,this.udrfBodyColumns=[{item:"pr_code",header:"PR code",isActive:!0,isVisible:"true",type:"text",position:1,colSize:1,sortOrder:"N",width:75,textClass:"value13Bold cp"},{item:"vendor_name",header:"Vendor Name",isActive:!0,isVisible:"true",type:"text",textClass:"value13Bold cp colorRed",position:2,colSize:3,sortOrder:"N",width:300},{item:"is_msme",header:"Vendor Type",isActive:!0,isVisible:"false",type:"text",textClass:"value13Bold cp colorRed",position:3,colSize:3,sortOrder:"N",width:300},{item:"description",header:"PR Description",isActive:!0,isVisible:"true",type:"text",position:4,colSize:3,sortOrder:"N",width:300},{item:"amount",header:"Amount",isActive:!0,isVisible:"true",type:"currency",textClass:"value13Bold cp colorRed",position:5,colSize:3,sortOrder:"N",width:240},{item:"status",header:"Status",isActive:!0,isVisible:"true",type:"status",position:6,colSize:2,sortOrder:"N",width:180},{item:"payment_model",header:"Payment model",isActive:!0,isVisible:"true",type:"text",position:7,colSize:3,sortOrder:"N",width:240},{item:"pref_amt",header:"Vendor Currency",isActive:!0,isVisible:"true",type:"currency",position:8,colSize:3,sortOrder:"N",width:240,textClass:"value13Bold cp colorRed text-right"},{item:"created_on",header:"Created on",isActive:!0,isVisible:"true",type:"date",textClass:"value13Bold cp",position:9,colSize:1,sortOrder:"N",width:130},{item:"created_by_name",header:"Created by",isActive:!0,isVisible:"true",type:"text",position:10,colSize:1,sortOrder:"N",width:180},{item:"invoice_date",header:"Invoice Date",isActive:!0,isVisible:"false",type:"date",textClass:"value13Bold cp",position:11,colSize:1,sortOrder:"N",width:130},{item:"action",header:"Actions",isActive:!0,isVisible:"false",type:"action",position:12,colSize:3,width:240,sortOrder:"N"},{item:"po_number",header:"PO Number",isActive:!0,isVisible:"true",type:"text",position:13,colSize:3,sortOrder:"N",width:240},{item:"cost_center_name",header:"Cost center",isActive:!0,isVisible:"true",type:"text",textClass:"value13Bold cp colorRed",position:14,colSize:3,sortOrder:"N",width:240},{item:"sub_group_name",header:"Sub group",isActive:!0,isVisible:"true",type:"text",position:15,colSize:3,sortOrder:"N",width:240},{item:"people_involved_names",header:"People Involved",isActive:!0,isVisible:"false",type:"text",position:16,colSize:3,sortOrder:"N",width:240}],this.categorisedDataTypeArray=[],this.dataTypeArray=[],this.udrfItemStatusColor=[],this.minNoOfVisibleSummaryCards=3,this.maxNoOfVisibleSummaryCards=6,this.isCardClicked=!1,this.cardClicked="",this.itemDataCurrentIndex=0,this.quickCTAInput={},this.commentsInput={},this.commentsContext={},this.initStatusData=e=>{let t=[],i=[];e.forEach((e,r)=>{t.push({status:e.name,color:e.color}),i.push({dataType:e.name,dataTypeValue:"0",isActive:!1,isVisible:!!e.is_visible,cardType:"status",statusColor:e.color,dataTypeCode:e.code})}),this.dataTypeArray=i,this.udrfItemStatusColor=t,this.categorisedDataTypeArray=[{categoryType:"Status Cards",categoryCardCodes:l.pluck(e,"code"),categoryCards:[]}]},this.initUdrfConfig=()=>Object(a.c)(this,void 0,void 0,(function*(){let e=[{checkboxId:"CDCRD",checkboxName:"Current Year",checkboxStartValue:c().startOf("year"),checkboxEndValue:c().endOf("year"),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD2",checkboxName:"This Week",checkboxStartValue:this._utilityService.getFormattedDate(c().startOf("week"),c(c().startOf("week")).date,15,0,0,0),checkboxEndValue:this._utilityService.getFormattedDate(c().endOf("week"),c(c().endOf("week")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD3",checkboxName:"This Month",checkboxStartValue:this._utilityService.getFormattedDate(c().startOf("month"),c(c().startOf("month")).date,15,0,0,0),checkboxEndValue:this._utilityService.getFormattedDate(c().endOf("month"),c(c().endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD4",checkboxName:"Previous Month",checkboxStartValue:this._utilityService.getFormattedDate(c().subtract(1,"months").startOf("month"),c(c().subtract(1,"months").startOf("month")).date,15,0,0,0),checkboxEndValue:this._utilityService.getFormattedDate(c().subtract(1,"months").endOf("month"),c(c().subtract(1,"months").endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD5",checkboxName:"All",checkboxStartValue:c("1920-01-01"),checkboxEndValue:c("2100-12-12"),isCheckboxDefaultSelected:!0}];this._udrfService.udrfFunctions.constructCustomRangeData(4,"date",e),this.itemDataCurrentIndex=0,this._udrfService.udrfBodyData=[],this._udrfService.udrfData.applicationId=this.applicationId,this._udrfService.udrfUiData.showNewReleasesButton=!1,this._udrfService.udrfUiData.showItemDataCount=!0,this._udrfService.udrfUiData.itemDataType="",this._udrfService.udrfUiData.totalItemDataCount=0,this._udrfService.udrfUiData.showSearchBar=!0,this._udrfService.udrfUiData.showActionButtons=!0,this._udrfService.udrfUiData.showUdrfModalButton=!0,this._udrfService.udrfUiData.showSettingsModalButton=!0,this._udrfService.udrfUiData.isReportDownloading=!1,this._udrfService.udrfUiData.showReportDownloadButton=!1,this._udrfService.udrfUiData.showColumnConfigButton=!0,this._udrfService.udrfUiData.udrfItemStatusColor=this.udrfItemStatusColor,this._udrfService.udrfUiData.horizontalScroll=!0,this._udrfService.udrfUiData.resolveVisibleSummaryCards=this.resolveVisibleDataTypeArray.bind(this),this._udrfService.udrfUiData.summaryCardsSelected=this.dataTypeCardSelected.bind(this),this._udrfService.udrfUiData.summaryCardsItem={},this._udrfService.udrfUiData.downloadItemDataReport=()=>{},this._udrfService.udrfUiData.itemDataScrollDown=this.itemDataScrollDown.bind(this),this._udrfService.udrfUiData.summaryCards=this.dataTypeArray,this._udrfService.udrfUiData.udrfBodyColumns=this.udrfBodyColumns,this._udrfService.udrfUiData.udrfVisibleBodyColumns=this._udrfService.udrfUiData.udrfVisibleBodyColumns,this._udrfService.udrfUiData.udrfInvisibleBodyColumns=this._udrfService.udrfUiData.udrfInvisibleBodyColumns,this._udrfService.udrfUiData.categorisedSummaryCards=this.categorisedDataTypeArray,this._udrfService.udrfUiData.minNoOfVisibleSummaryCards=this.minNoOfVisibleSummaryCards,this._udrfService.udrfUiData.maxNoOfVisibleSummaryCards=this.maxNoOfVisibleSummaryCards,this._udrfService.udrfUiData.selectedCard=this.selectedCard,this._udrfService.udrfUiData.variant=1,this._udrfService.udrfUiData.itemcardSelected=this.itemCardClicked.bind(this),this._udrfService.udrfUiData.quickCTAInput=this.quickCTAInput,this._udrfService.udrfUiData.commentsInput=this.commentsInput,this._udrfService.udrfUiData.commentsContext=this.commentsContext,this._udrfService.udrfUiData.collapseAll=!0,this._udrfService.udrfUiData.showCollapseButton=!0,this._udrfService.udrfUiData.showCreateNewComponentButton=!0,this._udrfService.udrfUiData.createNewComponent=this.openCreateRequest.bind(this),this._udrfService.udrfUiData.showActualCurrencyForCurrencyComponent=!0,this._udrfService.udrfUiData.itemHasComments=!0,this._udrfService.udrfUiData.openComments=this.openComments.bind(this),this._udrfService.udrfUiData.openCommentsData={},this._udrfService.udrfFunctions.resolveVisibleColumnConfigItems(),this._udrfService.udrfUiData.isReportDownloading=!1,this._udrfService.udrfUiData.showReportDownloadButton=!0,this._udrfService.udrfUiData.downloadItemDataReport=this.downloadItemDataReport.bind(this),this._udrfService.getAppUdrfConfig(this.applicationId,this.initReport.bind(this)),this._udrfService.getNotifyReleasesUDRF(),this._udrfService.udrfUiData.resolveColumnConfig(),this._udrfService.udrfUiData.togglePRTypes=!0,this._udrfService.udrfUiData.togglePRDetails=this.togglePRTypeFun.bind(this),this._udrfService.udrfUiData.togglePRTypeSelect="PR"})),this.initReport=()=>{this._udrfService.udrfData.isItemDataLoading=!0,this.$onAppApiCalled.next(),this.itemDataCurrentIndex=0,this._udrfService.udrfBodyData=[];for(let e=0;e<this.dataTypeArray.length;e++)this.dataTypeArray[e].isActive=!1;this.isCardClicked=!1,this.cardClicked="",this._udrfService.udrfUiData.resolveColumnConfig(),this.getP2pPrList()},this.resolveVisibleDataTypeArray=()=>{for(let e of this._udrfService.udrfUiData.summaryCards){let t;this._udrfService.udrfData.udrfSummaryCardCodes.length>0&&(t=l.contains(this._udrfService.udrfData.udrfSummaryCardCodes,e.dataTypeCode),e.isVisible=t,t&&(this._udrfService.udrfUiData.summaryCardsItem=e))}},this.dataTypeCardSelected=()=>{this._udrfService.udrfData.isItemDataLoading=!0,this._udrfService.udrfData.noItemDataFound=!1;let e=this._udrfService.udrfUiData.summaryCardsItem;for(let t=0;t<this.dataTypeArray.length;t++)this.dataTypeArray[t].dataType!=e.dataType&&(this.dataTypeArray[t].isActive=!1);e.isActive=!e.isActive,this.isCardClicked=!0,this.cardClicked=e.dataType,this.itemDataCurrentIndex=0,this._udrfService.udrfBodyData=[],this.getP2pPrList()},this.itemDataScrollDown=()=>Object(a.c)(this,void 0,void 0,(function*(){this._udrfService.udrfData.noItemDataFound||this._udrfService.udrfData.isItemDataLoading||(this.itemDataCurrentIndex+=this._udrfService.udrfData.defaultRecordsPerFetch,this._udrfService.udrfData.isItemDataLoading=!0,yield this.getP2pPrList())})),this.itemCardClicked=()=>{this.router.navigateByUrl("/main/p2p/requestorDetail/"+this._udrfService.udrfUiData.itemCardSelecteditem.p2p_header_id)},this.encodeURIComponent=e=>encodeURIComponent(e).replace(/[!'()*]/g,(function(e){return"%"+e.charCodeAt(0).toString(16)})),this.getP2pPrList=()=>{let e=JSON.parse(JSON.stringify(this._udrfService.udrfData.mainFilterArray)),t=l.where(this.dataTypeArray,{isActive:!0}),i=[],r=!1;if(t.length>0&&(i=l.where(t,{cardType:"status"}),i.length>0))if(e.length>0){for(let i of e)"Status"==i.filterName&&(i.multiOptionSelectSearchValues=[t[0].dataType],r=!0);if(0==r){let i=JSON.parse(JSON.stringify(l.where(this._udrfService.udrfData.filterTypeArray,{filterName:"Status"})));e.push(i[0]);for(let n of e)"Status"==n.filterName&&(n.multiOptionSelectSearchValues=[t[0].dataType],r=!0)}}else e=JSON.parse(JSON.stringify(l.where(this._udrfService.udrfData.filterTypeArray,{filterName:"Status"}))),e[0].multiOptionSelectSearchValues=[t[0].dataType];let n={startIndex:this.itemDataCurrentIndex,startDate:this._udrfService.udrfData.mainApiDateRangeStart,endDate:this._udrfService.udrfData.mainApiDateRangeEnd,mainFilterArray:e,txTableDetails:this._udrfService.udrfData.txTableDetails,mainSearchParameter:this._udrfService.udrfData.mainSearchParameter,searchTableDetails:this._udrfService.udrfData.searchTableDetails};this.selectedCard=this._udrfService.udrfData.udrfSummaryCardCodes,this.requestDataSubscription&&this.requestDataSubscription.unsubscribe(),"PR"==this._udrfService.udrfUiData.togglePRTypeSelect?this.requestDataSubscription=this._prReportService.getRequestorListUdrf(n,"PR").pipe(Object(d.a)(this.$onDestroy)).pipe(Object(d.a)(this.$onAppApiCalled)).subscribe(e=>Object(a.c)(this,void 0,void 0,(function*(){this._prReportService.getVendorType().pipe(Object(d.a)(this.$onDestroy)).pipe(Object(d.a)(this.$onAppApiCalled)).subscribe(t=>Object(a.c)(this,void 0,void 0,(function*(){if(this.vendorTypeDetails=t,"S"==e.messType&&e.data.length>0){let t;l.each(e.data,e=>{null!=e.is_msme?(t=this.vendorTypeDetails.filter(t=>t.is_msme===e.is_msme),e.is_msme=t[0].vendortype):e.is_msme="-",e.expanded=this._udrfService.udrfUiData.collapseAll}),this._udrfService.udrfBodyData=this._udrfService.udrfBodyData.concat(e.data),this.isCardClicked||0!=this.itemDataCurrentIndex||(yield this.getP2pPrSummaryCardData())}else this._udrfService.udrfBodyData=this._udrfService.udrfBodyData.concat([]),this._udrfService.udrfData.noItemDataFound=!0;this._udrfService.udrfData.isItemDataLoading=!1})))})),e=>{this.showErrorMessage(e)}):"ICPR"==this._udrfService.udrfUiData.togglePRTypeSelect&&(this.requestDataSubscription=this._prReportService.getRequestorListUdrf(n,"ICPR").pipe(Object(d.a)(this.$onDestroy)).pipe(Object(d.a)(this.$onAppApiCalled)).subscribe(e=>Object(a.c)(this,void 0,void 0,(function*(){this._prReportService.getVendorType().pipe(Object(d.a)(this.$onDestroy)).pipe(Object(d.a)(this.$onAppApiCalled)).subscribe(t=>Object(a.c)(this,void 0,void 0,(function*(){if(this.vendorTypeDetails=t,"S"==e.messType&&e.data.length>0){let t;l.each(e.data,e=>{null!=e.is_msme?(t=this.vendorTypeDetails.filter(t=>t.is_msme===e.is_msme),e.is_msme=t[0].vendortype):e.is_msme="-",e.expanded=this._udrfService.udrfUiData.collapseAll}),this._udrfService.udrfBodyData=this._udrfService.udrfBodyData.concat(e.data),this.isCardClicked||0!=this.itemDataCurrentIndex||(yield this.getP2pPrSummaryCardData())}else this._udrfService.udrfBodyData=this._udrfService.udrfBodyData.concat([]),this._udrfService.udrfData.noItemDataFound=!0;this._udrfService.udrfData.isItemDataLoading=!1})))})),e=>{this.showErrorMessage(e)}))},this.getP2pPrSummaryCardData=()=>{this._udrfService.udrfUiData.totalItemDataCount=0;let e=JSON.parse(JSON.stringify(this._udrfService.udrfData.mainFilterArray)),t={startIndex:this.itemDataCurrentIndex,startDate:this._udrfService.udrfData.mainApiDateRangeStart,endDate:this._udrfService.udrfData.mainApiDateRangeEnd,mainFilterArray:e,txTableDetails:this._udrfService.udrfData.txTableDetails,mainSearchParameter:this._udrfService.udrfData.mainSearchParameter,searchTableDetails:this._udrfService.udrfData.searchTableDetails};"PR"==this._udrfService.udrfUiData.togglePRTypeSelect?this._prReportService.getRequestorSummaryCardUdrf(t,"PR").pipe(Object(d.a)(this.$onDestroy)).pipe(Object(d.a)(this.$onAppApiCalled)).subscribe(e=>Object(a.c)(this,void 0,void 0,(function*(){if("S"==e.messType&&e.messData&&e.messData.length>0){for(let e=0;e<this.dataTypeArray.length;e++)this.dataTypeArray[e].isActive=!1,this.dataTypeArray[e].dataTypeValue="0";this._udrfService.udrfUiData.totalItemDataCount=e.total,this.dataTypeArray=this.dataTypeArray.map((t,i)=>{const r=e.messData.find(e=>t.dataType==e.dataType);return Object.assign(Object.assign({},t),r)}),this._udrfService.udrfUiData.summaryCards=this.dataTypeArray}else{for(let e=0;e<this.dataTypeArray.length;e++)this.dataTypeArray[e].isActive=!1,this.dataTypeArray[e].dataTypeValue="0";this._udrfService.udrfUiData.summaryCards=this.dataTypeArray}}))):"ICPR"==this._udrfService.udrfUiData.togglePRTypeSelect&&this._prReportService.getRequestorSummaryCardUdrf(t,"ICPR").pipe(Object(d.a)(this.$onDestroy)).pipe(Object(d.a)(this.$onAppApiCalled)).subscribe(e=>Object(a.c)(this,void 0,void 0,(function*(){if("S"==e.messType&&e.messData&&e.messData.length>0){for(let e=0;e<this.dataTypeArray.length;e++)this.dataTypeArray[e].isActive=!1,this.dataTypeArray[e].dataTypeValue="0";this._udrfService.udrfUiData.totalItemDataCount=e.total,this.dataTypeArray=this.dataTypeArray.map((t,i)=>{const r=e.messData.find(e=>t.dataType==e.dataType);return Object.assign(Object.assign({},t),r)}),this._udrfService.udrfUiData.summaryCards=this.dataTypeArray}else{for(let e=0;e<this.dataTypeArray.length;e++)this.dataTypeArray[e].isActive=!1,this.dataTypeArray[e].dataTypeValue="0";this._udrfService.udrfUiData.summaryCards=this.dataTypeArray}})))},this.openCreateRequest=()=>Object(a.c)(this,void 0,void 0,(function*(){const{CreateRequestComponent:e}=yield i.e(885).then(i.bind(null,"DzVq"));this.dialog.open(e,{width:"88%",height:"100%",position:{right:"0px"},autoFocus:!1,maxWidth:"90vw",data:null}).afterClosed().subscribe(e=>{"S"==e.messType&&this.initReport()})})),this.showErrorMessage=e=>{this._utilityService.showErrorMessage("Error:Unable to fetch Data","KEBS")},this.openComments=()=>Object(a.c)(this,void 0,void 0,(function*(){let e,t=this._udrfService.udrfUiData.openCommentsData.data;if(t){e={application_id:244,unique_id_1:t.p2p_header_id,application_name:"Procure To Pay",title:t.pr_code?t.pr_code:""};let r={inputData:e,context:{"Vendor name":t.vendor_name?t.vendor_name:"","Payment term":t.payment_term?t.payment_term:"","Invoice Date":t.invoice_date?c(t.invoice_date).format("DD-MMM-YY"):""},commentBoxHeight:"100vh",commentBoxScrollHeight:"80%"};const{ChatCommentContextModalComponent:n}=yield Promise.all([i.e(4),i.e(63),i.e(75),i.e(983)]).then(i.bind(null,"vg2w"));this.dialog.open(n,{height:"100%",width:"65%",position:{right:"0px"},data:{modalParams:r}})}})),this.downloadItemDataReport=()=>{this._udrfService.udrfUiData.isReportDownloading=!0;let e=JSON.parse(JSON.stringify(this._udrfService.udrfData.mainFilterArray)),t={startIndex:"D",startDate:this._udrfService.udrfData.mainApiDateRangeStart,endDate:this._udrfService.udrfData.mainApiDateRangeEnd,mainFilterArray:e,txTableDetails:this._udrfService.udrfData.txTableDetails,mainSearchParameter:this._udrfService.udrfData.mainSearchParameter,searchTableDetails:this._udrfService.udrfData.searchTableDetails};"PR"==this._udrfService.udrfUiData.togglePRTypeSelect?this._prReportService.getRequestorListUdrf(t,"PR").pipe(Object(d.a)(this.$onDestroy)).pipe(Object(d.a)(this.$onAppApiCalled)).subscribe(e=>Object(a.c)(this,void 0,void 0,(function*(){this._prReportService.getVendorType().pipe(Object(d.a)(this.$onDestroy)).pipe(Object(d.a)(this.$onAppApiCalled)).subscribe(t=>Object(a.c)(this,void 0,void 0,(function*(){if(this.vendorTypeDetails=t,"S"==e.messType&&e.data.length>0){let t;l.each(e.data,e=>{e.created_on=null!=e.created_on?c(e.created_on).format("DD-MM-YYYY"):e.created_on;let t="";"string"!=typeof e.amount&&(l.each(e.amount,e=>{t+=`${e.value} ${e.currency_code}  `}),e.amount=t),delete e.l2}),l.each(e.data,e=>{null!=e.is_msme&&(t=this.vendorTypeDetails.filter(t=>t.is_msme===e.is_msme),e.is_msme=t[0].vendortype)}),console.log("Global Vendor Type on PR : ",e.data),this._excelService.exportAsExcelFile(e.data,e.fileName),this._utilityService.showToastMessage(e.messText)}else this._p2pGeneralService.showMessage("Failed to download report, Kindly contact KEBS team !");this._udrfService.udrfUiData.isReportDownloading=!1})))})),e=>{this._udrfService.udrfUiData.isReportDownloading=!1,this._utilityService.showErrorMessage(e,"KEBS")}):"ICPR"==this._udrfService.udrfUiData.togglePRTypeSelect&&this._prReportService.getRequestorListUdrf(t,"ICPR").pipe(Object(d.a)(this.$onDestroy)).pipe(Object(d.a)(this.$onAppApiCalled)).subscribe(e=>Object(a.c)(this,void 0,void 0,(function*(){this._prReportService.getVendorType().pipe(Object(d.a)(this.$onDestroy)).pipe(Object(d.a)(this.$onAppApiCalled)).subscribe(t=>Object(a.c)(this,void 0,void 0,(function*(){if(this.vendorTypeDetails=t,"S"==e.messType&&e.data.length>0){let t;l.each(e.data,e=>{e.created_on=null!=e.created_on?c(e.created_on).format("DD-MM-YYYY"):e.created_on;let t="";"string"!=typeof e.amount&&(l.each(e.amount,e=>{t+=`${e.value} ${e.currency_code}  `}),e.amount=t),delete e.l2}),l.each(e.data,e=>{null!=e.is_msme&&(t=this.vendorTypeDetails.filter(t=>t.is_msme===e.is_msme),e.is_msme=t[0].vendortype)}),console.log("Global Vendor Type on PR : ",e.data),this._excelService.exportAsExcelFile(e.data,e.fileName),this._utilityService.showToastMessage(e.messText)}else this._p2pGeneralService.showMessage("Failed to download report, Kindly contact KEBS team !");this._udrfService.udrfUiData.isReportDownloading=!1})))})),e=>{this._udrfService.udrfUiData.isReportDownloading=!1,this._utilityService.showErrorMessage(e,"KEBS")})},this.getPrStatusMasterData=()=>new Promise((e,t)=>{this._p2pGeneralService.getPurchaseRequestHeaderStatus().pipe(Object(d.a)(this.$onDestroy)).subscribe(i=>{i.err?t(i):e(i.data)},e=>{console.log(e)})}),this.ngOnDestroy=()=>{this.$onDestroy.next(),this.$onDestroy.complete(),this._udrfService.resetUdrfData(),this.requestDataSubscription&&this.requestDataSubscription.unsubscribe()},this.togglePRTypeFun=()=>{console.log("Toggle Event Value : ",this._udrfService.udrfUiData.togglePRTypeSelect),this.initReport()}}ngOnInit(){return Object(a.c)(this,void 0,void 0,(function*(){this.resolveObservables();let e=yield this.getPrStatusMasterData();this.initStatusData(e),this.initUdrfConfig()}))}resolveObservables(){this._p2pGeneralService.$compDestroyObservable.subscribe(e=>{"prs"==e&&"all"!=e||this.ngOnDestroy()})}}return e.\u0275fac=function(t){return new(t||e)(u["\u0275\u0275directiveInject"](h.a),u["\u0275\u0275directiveInject"](p.a),u["\u0275\u0275directiveInject"](g.a),u["\u0275\u0275directiveInject"](f),u["\u0275\u0275directiveInject"](y.b),u["\u0275\u0275directiveInject"](n.g),u["\u0275\u0275directiveInject"](v.a),u["\u0275\u0275directiveInject"](_.a))},e.\u0275cmp=u["\u0275\u0275defineComponent"]({type:e,selectors:[["app-pr-report-landing-page"]],decls:3,vars:0,consts:[[1,"container-fluid","pr-report-landing-page","pl-0","pr-0"]],template:function(e,t){1&e&&(u["\u0275\u0275elementStart"](0,"div",0),u["\u0275\u0275element"](1,"udrf-header"),u["\u0275\u0275element"](2,"udrf-body"),u["\u0275\u0275elementEnd"]())},directives:[C.a,b.a],styles:[".pr-report-landing-page[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field{width:40vw;padding-top:5px}.pr-report-landing-page[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-infix{font-size:14px!important;padding:.5em 0!important;border-top:.54375em solid transparent!important}.pr-report-landing-page[_ngcontent-%COMP%]     .udrf-header-card{background-color:#fafafa!important}.pr-report-landing-page[_ngcontent-%COMP%]   .spinner-align[_ngcontent-%COMP%]{margin:auto;display:inline-block}.pr-report-landing-page[_ngcontent-%COMP%]     .infinite-scroll-fixed{max-height:55vh!important;min-height:55vh!important;overflow-y:scroll}.pr-report-landing-page[_ngcontent-%COMP%]     .infinite-scroll-fixed-shortened{max-height:55vh!important;min-height:55vh!important}.pr-report-landing-page[_ngcontent-%COMP%]   .infinite-scroll-fixed-shortened[_ngcontent-%COMP%]{height:345px;overflow-y:scroll}.pr-report-landing-page[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff;font-weight:400;font-size:13px!important;min-width:60px;line-height:30px;padding:0 25px;border-radius:0!important}.pr-report-landing-page[_ngcontent-%COMP%]   .ta-r[_ngcontent-%COMP%]{text-align:right!important}.pr-report-landing-page[_ngcontent-%COMP%]   .ta-l[_ngcontent-%COMP%]{text-align:left}.pr-report-landing-page[_ngcontent-%COMP%]   .value13Bold[_ngcontent-%COMP%]{color:#000;font-weight:500!important}.pr-report-landing-page[_ngcontent-%COMP%]   .value13Bold[_ngcontent-%COMP%], .pr-report-landing-page[_ngcontent-%COMP%]   .value13light[_ngcontent-%COMP%]{font-size:13px!important;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.pr-report-landing-page[_ngcontent-%COMP%]   .value13light[_ngcontent-%COMP%]{color:#4e4c4c;font-weight:400!important}.pr-report-landing-page[_ngcontent-%COMP%]   .value14Bold[_ngcontent-%COMP%]{color:#000;font-size:14px!important;font-weight:500!important;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.pr-report-landing-page[_ngcontent-%COMP%]   .listcard[_ngcontent-%COMP%]{transition:all .25s linear;height:auto}.pr-report-landing-page[_ngcontent-%COMP%]   .smallSubtleText[_ngcontent-%COMP%]{color:#66615b;font-size:11px}.pr-report-landing-page[_ngcontent-%COMP%]   .data-type-card[_ngcontent-%COMP%]{min-height:85px;transition:all .3s linear}.pr-report-landing-page[_ngcontent-%COMP%]   .data-type-card-is-active[_ngcontent-%COMP%]{border-color:#cf0001;background-color:#fff2f2}.pr-report-landing-page[_ngcontent-%COMP%]   .headingBold[_ngcontent-%COMP%]{color:#1a1a1a;font-weight:600;font-size:20px}.pr-report-landing-page[_ngcontent-%COMP%]   .valueGrey14[_ngcontent-%COMP%]{font-size:14px}.pr-report-landing-page[_ngcontent-%COMP%]   .valueGrey12Normal[_ngcontent-%COMP%], .pr-report-landing-page[_ngcontent-%COMP%]   .valueGrey14[_ngcontent-%COMP%]{color:#4a4a4a;font-weight:400;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.pr-report-landing-page[_ngcontent-%COMP%]   .valueGrey12Normal[_ngcontent-%COMP%]{font-size:12px}.pr-report-landing-page[_ngcontent-%COMP%]   .cp[_ngcontent-%COMP%]{cursor:pointer!important}.pr-report-landing-page[_ngcontent-%COMP%]   .col-2-2[_ngcontent-%COMP%]{flex:0 0 11%;max-width:11%}.pr-report-landing-page[_ngcontent-%COMP%]   .col-2-8[_ngcontent-%COMP%]{flex:0 0 21.666667%;max-width:21.666667%}.pr-report-landing-page[_ngcontent-%COMP%]   .alignCenter[_ngcontent-%COMP%]{margin:0 auto;display:block}.pr-report-landing-page[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]{margin-top:5px!important;line-height:8px;width:26px;height:26px;margin-right:10px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12);animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}.pr-report-landing-page[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%], .pr-report-landing-page[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.pr-report-landing-page[_ngcontent-%COMP%]   .ta-c[_ngcontent-%COMP%]{text-align:center;padding-left:0!important;padding-right:0!important}.pr-report-landing-page[_ngcontent-%COMP%]   .divAlignItemCenter[_ngcontent-%COMP%]{display:flex;align-items:center}.pr-report-landing-page[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#3c3c3c;font-weight:400;font-size:18px}.pr-report-landing-page[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}.pr-report-landing-page[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}.cp[_ngcontent-%COMP%]{cursor:pointer!important}.value14Bold[_ngcontent-%COMP%]{color:#000;font-size:14px!important;font-weight:500!important;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}"],data:{animation:[Object(s.o)("slideInOut",[Object(s.l)("in",Object(s.m)({height:"*",overflow:"hidden"})),Object(s.l)("out",Object(s.m)({height:0,overflow:"hidden"})),Object(s.n)("* => in",[Object(s.m)({height:0}),Object(s.e)(250,Object(s.m)({height:"*"}))]),Object(s.n)("in=> *",[Object(s.m)({height:"*"}),Object(s.e)(250,Object(s.m)({height:0}))])]),Object(s.o)("smallCardAnimation",[Object(s.n)("* => *",[Object(s.i)(":leave",[Object(s.k)(100,[Object(s.e)("0.5s",Object(s.m)({opacity:0}))])],{optional:!0}),Object(s.i)(":enter",[Object(s.m)({opacity:0}),Object(s.k)(100,[Object(s.e)("0.5s",Object(s.m)({opacity:1}))])],{optional:!0})])])]}}),e})()}];let O=(()=>{class e{}return e.\u0275mod=u["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=u["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[n.k.forChild(S)],n.k]}),e})();var D=i("Xi0T");let x=(()=>{class e{}return e.\u0275mod=u["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=u["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[r.CommonModule,O,D.a]]}),e})()},NJ67:function(e,t,i){"use strict";i.d(t,"a",(function(){return r}));class r{constructor(){this.disabled=!1}onChange(e){}onTouched(e){}writeValue(e){this.value=e}registerOnChange(e){this.onChange=e}registerOnTouched(e){this.onTouched=e}setDisabledState(e){this.disabled=e}}},hJL4:function(e,t,i){"use strict";i.d(t,"a",(function(){return u}));var r=i("mrSG"),n=i("XNiG"),a=i("xG9w"),s=i("fXoL"),o=i("tk/3"),l=i("LcQX"),c=i("XXEo"),d=i("flaP");let u=(()=>{class e{constructor(e,t,i,r){this.http=e,this.UtilityService=t,this.loginService=i,this.roleService=r,this.msg=new n.b,this.taskStatusColor=[],this.approveRequest=(e,t)=>this.http.post("/api/isa/request/approveResourceRequest",{requestDetails:e,approverOid:t}),this.rejectRequest=(e,t)=>this.http.post("/api/isa/request/rejectResourceRequest",{requestDetails:e,approverOid:t}),this.getTaskStatusColor()}getAllRequestsOfUser(e,t,i,r,n,a,s){let o=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getAllRequestsOfUser",{oid:e,statusId:t,statusCode:i,objectIds:r,skip:n,limit:a,filterConfig:s,orgIds:o})}getAllRoleAccess(){return a.where(this.roleService.roles,{application_id:139})}getRequestsBasedOnStatus(e,t,i,r,n,a,s){let o=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getRequestsBasedOnStatus",{oid:e,statusId:t,statusCode:i,objectIds:r,skip:n,limit:a,filterConfig:s,orgIds:o})}getRequestsForAwaitingApproval(e,t,i,r){return this.http.post("/api/isa/request/getRequestsForAwaitingApproval",{oid:e,skip:t,limit:i,filterConfig:r})}getObjectIdsBasedOnOid(e){return this.http.post("/api/isa/request/getObjectIdsBasedOnOid",{oid:e})}getStatusCountBasedOnOid(e,t,i,r){let n=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getStatusCountBasedOnOid",{oid:e,objectIds:t,dataTypeArray:i,filterConfig:r,orgIds:n})}getHeaderCount(e){return this.http.post("/api/isa/request/getHeaderCount",{oid:e})}getRequestBasedOnStatus(e,t){return this.http.post("/api/isa/request/getRequestBasedOnStatus",{oid:e,status:t})}getDataTypeArray(){return this.http.post("/api/isa/request/getISAStatusMasterDataUDRF",{})}sendMsg(e){this.msg.next(e)}getMsg(){return this.msg.asObservable()}getTaskStatusColor(){return new Promise((e,t)=>{this.http.post("/api/isa/request/getTaskStatusNameColor",{}).subscribe(t=>(this.taskStatusColor=t,e(t)),e=>{})})}getAllRequestsForRmg(e){return this.http.post("/api/isa/request/getAllRequestsForRmg",{filterConfig:e})}getTotalForRmg(e){return new Promise((t,i)=>{this.http.post("/api/isa/request/getTotalForRmg",{filterConfig:e}).subscribe(e=>t(e),e=>{})})}getDefaultISAStatusToDisplay(){return this.http.post("/api/isa/request/getISADefaultStatusToDisplay",{})}getCTCbreakUpForSkillSet(e){return new Promise((t,i)=>{this.http.post("/api/isa/request/getCTCbreakUpForSkillSet",e).subscribe(e=>t(e.data),e=>{i(e)})})}getCurrentUserRole(){return this.roleService&&this.roleService.roles&&this.roleService.roles.length>0?this.roleService.roles[0].role_id:null}getTemplateForUser(e){return this.http.post("/api/isa/request/getVisibleFormFieldsForUser",e)}saveAndApproveRequest(e){return this.http.post("/api/isa/request/updBudgetInfoByRequestId",e)}getVisibilityMatrix(e){return this.http.post("/api/isa/request/getAuthorzedFieldsByRoleId",e)}getISAAttachmentFromS3(e){return this.http.post("/api/isa/attachment/getISAAttachmentFromS3",{key:e})}getSkillType(){return this.http.post("/api/isa/request/getSkillType",{})}getSkillLevel(){return this.http.post("/api/isa/request/getSkillLevel",{})}getSkillExperience(){return this.http.post("/api/isa/request/getSkillExperience",{})}getSkillName(e){return this.http.post("/api/isa/request/getSkillName",{skill_type_id:e})}getIsaActivityReportDownload(){return this.http.post("/api/isa/request/getIsaActivityReportDownload",{})}getISASLAreport(e){return this.http.post("/api/isa/request/getISASLAreport",{filterConfig:e})}getISACustomReport(e){return this.http.post("/api/isa/request/getISACustomReport",{filterConfig:e})}getPositionType(){return this.http.post("/api/employee360/masterData/getPositionType",{})}calculateNetCostBasedOnPosition(e,t,i,n,s,o,l){return Object(r.c)(this,void 0,void 0,(function*(){let r;r=o&&o.length>1&&(yield this.getManpowerCostByOId(o,i,s,2))||(yield this.getManpowerCostBasedOnPosition(e,t,i,s,l));let c=yield this.getNonManpowerCost(t,i,n,s,2),d=yield this.getAllocatedCost(),u=0;u=(r?r.cost:0)+c.length>0?a.reduce(a.pluck(c,"cost"),(e,t)=>e+t,0):0;let h=d.length>0?a.reduce(a.pluck(d,"percentage"),(e,t)=>e+t,0):0;return{cost:u,currency:r&&r.currency_code?r.currency_code:"",manpowerCost:r,nonManpowerCost:c,allocatedCost:d,allocatedCostValue:u*(h/100)}}))}getManpowerCostBasedOnPosition(e,t,i,r,n){return new Promise((a,s)=>{this.http.post("/api/isa/configuration/getManpowerCostBasedOnPosition",{skillRoleId:e,nationalityId:t,locationGroupId:i,unit:r,position:n}).subscribe(e=>a(e),e=>(console.log(e),s(e)))})}getNonManpowerCost(e,t,i,r,n){return new Promise((a,s)=>{this.http.post("/api/project/getNonManpowerCost",{nationalityId:e,locationGroupId:t,locationId:i,unit:r,currency_id:n}).subscribe(e=>a(e),e=>(console.log(e),s(e)))})}getAllocatedCost(){return new Promise((e,t)=>{this.http.post("/api/project/getAllocatedCost","").subscribe(t=>e(t),e=>(console.log(e),t(e)))})}getManpowerCostByOId(e,t,i,r){return new Promise((n,a)=>{this.http.post("/api/project/getEmployeeSalary",{oid:e,location_group_id:t,unit_id:i,currency_id:r}).subscribe(e=>n(e),e=>(console.log(e),a(e)))})}getSkillNameMaster(){return this.http.post("/api/isa/configuration/getSkillName",{})}getVendorOid(e,t){return this.http.post("/api/isa/request/getVendorOid",{vendorNames:e,docID:t})}getVendorList(e,t){return this.http.post("/api/isa/request/getVendorList",{documentID:e,limitval:t})}removeVendor(e,t){return this.http.post("/api/isa/request/removeVendors",{vendorOid:e,documentID:t})}checkVendorOrNot(){return this.http.post("/api/isa/request/checkVendorOrNot",{})}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275inject"](o.c),s["\u0275\u0275inject"](l.a),s["\u0275\u0275inject"](c.a),s["\u0275\u0275inject"](d.a))},e.\u0275prov=s["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},ucYs:function(e,t,i){"use strict";i.d(t,"a",(function(){return l}));var r=i("mrSG"),n=i("xG9w"),a=i("fXoL"),s=i("tk/3"),o=i("BVzC");let l=(()=>{class e{constructor(e,t){this.http=e,this.errorService=t,this.workflowStatusList=[],this.getWorkflowStatusList()}getWorkflowStatusList(){this.http.post("/api/wfPrimary/getWorkflowStatusList","").subscribe(e=>{this.workflowStatusList=e.data},e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server While Getting Workflow Status List",e&&e.params?e.params:e&&e.error?e.error.params:{})})}getWorkflowProperties(e){return new Promise((t,i)=>{this.http.post("/api/wfPrimary/getWorkflowProperties",{applicationId:e}).subscribe(e=>t(e),e=>i(e))})}getWorkflowPropertiesByWorkflowId(e){return new Promise((t,i)=>{this.http.post("/api/wfPrimary/getWorkflowPropertiesByWorkflowId",{workflowId:e}).subscribe(e=>t(e),e=>i(e))})}getApproversHierarchy(e){return new Promise((t,i)=>{this.http.post("/api/wfPrimary/getApproversHierarchy",e).subscribe(e=>t(e),e=>i(e))})}createWorkflowItems(e){return new Promise((t,i)=>{this.http.post("/api/wfPrimary/createWorkflowItems",e).subscribe(e=>t(e),e=>i(e))})}getWorkflowDetails(e){return new Promise((t,i)=>{this.http.post("/api/wfPrimary/getWorkflowDetails",{workflowId:e}).subscribe(e=>t(e),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while getting workflow details",e&&e.params?e.params:e&&e.error?e.error.params:{})})})}formatApproversHierarchy(e,t){return Object(r.c)(this,void 0,void 0,(function*(){0==e.length&&t.cc0&&t.cc0.appr0&&t.cc0.appr0.length>0&&e.push([{approvalType:"I",purposeOfInsertingIntoArray:"Just to ensure that 'I' type data is used, if it exists"}]);for(let i=0;i<e.length;i++){let r=[],a=n.keys(t["cc"+i]);for(let n=0;n<a.length;n++)for(let s=0;s<t["cc"+i][a[n]].length;s++){let o={name:t["cc"+i][a[n]][s].DELEGATE_NAME,oid:t["cc"+i][a[n]][s].DELEGATE_OID,level:n+1,designation:t["cc"+i][a[n]][s].DELEGATE_DESIGNATION_NAME,is_delegated:t["cc"+i][a[n]][s].IS_DELEGATED,role:t["cc"+i][a[n]][s].DELEGATE_ROLE_NAME};if(1==t["cc"+i][a[n]][s].IS_DELEGATED&&(o.delegated_by={name:t["cc"+i][a[n]][s].APPROVER_NAME,oid:t["cc"+i][a[n]][s].APPROVER_OID,level:n+1,designation:t["cc"+i][a[n]][s].APPROVER_DESIGNATION_NAME}),r.push(o),i==e.length-1&&n==a.length-1&&s==t["cc"+i][a[n]].length-1)return r}}}))}storeComments(e,t,i){return new Promise((r,n)=>{this.http.post("/api/wfPrimary/updateComments",{workflowHeaderId:e,newComments:t,commentor:i}).subscribe(e=>r(e),e=>(this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server Error while Updating Workflow Comments",e&&e.params?e.params:e&&e.error?e.error.params:{}),n(e)))})}updateWorkflowItems(e){return new Promise((t,i)=>{this.http.post("/api/wfPrimary/updateWorkflowItems",e).subscribe(e=>t(e),e=>(console.log(e),i(e)))})}formatApproversHierarchyForOpportunityApprovalActivity(e){return Object(r.c)(this,void 0,void 0,(function*(){for(let t=0;t<1;t++){let i=[],r=n.keys(e["cc"+t]);for(let n=0;n<r.length;n++)for(let a=0;a<e["cc"+t][r[n]].length;a++){let s={name:e["cc"+t][r[n]][a].DELEGATE_NAME,oid:e["cc"+t][r[n]][a].DELEGATE_OID,level:e["cc"+t][r[n]][a].APPROVAL_ORDER,designation:e["cc"+t][r[n]][a].DELEGATE_DESIGNATION_NAME,is_delegated:e["cc"+t][r[n]][a].IS_DELEGATED};if(1==e["cc"+t][r[n]][a].IS_DELEGATED&&(s.delegated_by={name:e["cc"+t][r[n]][a].APPROVER_NAME,oid:e["cc"+t][r[n]][a].APPROVER_OID,level:e["cc"+t][r[n]][a].APPROVAL_ORDER,designation:e["cc"+t][r[n]][a].APPROVER_DESIGNATION_NAME}),i.push(s),n==r.length-1&&a==e["cc"+t][r[n]].length-1)return i}}}))}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275inject"](s.c),a["\u0275\u0275inject"](o.a))},e.\u0275prov=a["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},w76M:function(e,t,i){"use strict";i.d(t,"a",(function(){return c})),i.d(t,"b",(function(){return d}));var r=i("jhN1"),n=i("fXoL"),a=i("oHs6"),s=i("PVOt"),o=i("6t9p");const l=["*"];let c=(()=>{let e=class extends s.b{constructor(e,t,i,r,n,a,s,o){super(e,t,i,r,s,o),this._watcherHelper=r,this._idh=n,this._createEventEmitters([{subscribe:"contentReady",emit:"onContentReady"},{subscribe:"disposing",emit:"onDisposing"},{subscribe:"hidden",emit:"onHidden"},{subscribe:"hiding",emit:"onHiding"},{subscribe:"initialized",emit:"onInitialized"},{subscribe:"optionChanged",emit:"onOptionChanged"},{subscribe:"resize",emit:"onResize"},{subscribe:"resizeEnd",emit:"onResizeEnd"},{subscribe:"resizeStart",emit:"onResizeStart"},{subscribe:"showing",emit:"onShowing"},{subscribe:"shown",emit:"onShown"},{subscribe:"titleRendered",emit:"onTitleRendered"},{emit:"accessKeyChange"},{emit:"animationChange"},{emit:"closeOnOutsideClickChange"},{emit:"containerChange"},{emit:"contentTemplateChange"},{emit:"deferRenderingChange"},{emit:"disabledChange"},{emit:"dragEnabledChange"},{emit:"elementAttrChange"},{emit:"focusStateEnabledChange"},{emit:"fullScreenChange"},{emit:"heightChange"},{emit:"hintChange"},{emit:"hoverStateEnabledChange"},{emit:"maxHeightChange"},{emit:"maxWidthChange"},{emit:"minHeightChange"},{emit:"minWidthChange"},{emit:"positionChange"},{emit:"resizeEnabledChange"},{emit:"rtlEnabledChange"},{emit:"shadingChange"},{emit:"shadingColorChange"},{emit:"showCloseButtonChange"},{emit:"showTitleChange"},{emit:"tabIndexChange"},{emit:"titleChange"},{emit:"titleTemplateChange"},{emit:"toolbarItemsChange"},{emit:"visibleChange"},{emit:"widthChange"}]),this._idh.setHost(this),a.setHost(this)}get accessKey(){return this._getOption("accessKey")}set accessKey(e){this._setOption("accessKey",e)}get animation(){return this._getOption("animation")}set animation(e){this._setOption("animation",e)}get closeOnOutsideClick(){return this._getOption("closeOnOutsideClick")}set closeOnOutsideClick(e){this._setOption("closeOnOutsideClick",e)}get container(){return this._getOption("container")}set container(e){this._setOption("container",e)}get contentTemplate(){return this._getOption("contentTemplate")}set contentTemplate(e){this._setOption("contentTemplate",e)}get deferRendering(){return this._getOption("deferRendering")}set deferRendering(e){this._setOption("deferRendering",e)}get disabled(){return this._getOption("disabled")}set disabled(e){this._setOption("disabled",e)}get dragEnabled(){return this._getOption("dragEnabled")}set dragEnabled(e){this._setOption("dragEnabled",e)}get elementAttr(){return this._getOption("elementAttr")}set elementAttr(e){this._setOption("elementAttr",e)}get focusStateEnabled(){return this._getOption("focusStateEnabled")}set focusStateEnabled(e){this._setOption("focusStateEnabled",e)}get fullScreen(){return this._getOption("fullScreen")}set fullScreen(e){this._setOption("fullScreen",e)}get height(){return this._getOption("height")}set height(e){this._setOption("height",e)}get hint(){return this._getOption("hint")}set hint(e){this._setOption("hint",e)}get hoverStateEnabled(){return this._getOption("hoverStateEnabled")}set hoverStateEnabled(e){this._setOption("hoverStateEnabled",e)}get maxHeight(){return this._getOption("maxHeight")}set maxHeight(e){this._setOption("maxHeight",e)}get maxWidth(){return this._getOption("maxWidth")}set maxWidth(e){this._setOption("maxWidth",e)}get minHeight(){return this._getOption("minHeight")}set minHeight(e){this._setOption("minHeight",e)}get minWidth(){return this._getOption("minWidth")}set minWidth(e){this._setOption("minWidth",e)}get position(){return this._getOption("position")}set position(e){this._setOption("position",e)}get resizeEnabled(){return this._getOption("resizeEnabled")}set resizeEnabled(e){this._setOption("resizeEnabled",e)}get rtlEnabled(){return this._getOption("rtlEnabled")}set rtlEnabled(e){this._setOption("rtlEnabled",e)}get shading(){return this._getOption("shading")}set shading(e){this._setOption("shading",e)}get shadingColor(){return this._getOption("shadingColor")}set shadingColor(e){this._setOption("shadingColor",e)}get showCloseButton(){return this._getOption("showCloseButton")}set showCloseButton(e){this._setOption("showCloseButton",e)}get showTitle(){return this._getOption("showTitle")}set showTitle(e){this._setOption("showTitle",e)}get tabIndex(){return this._getOption("tabIndex")}set tabIndex(e){this._setOption("tabIndex",e)}get title(){return this._getOption("title")}set title(e){this._setOption("title",e)}get titleTemplate(){return this._getOption("titleTemplate")}set titleTemplate(e){this._setOption("titleTemplate",e)}get toolbarItems(){return this._getOption("toolbarItems")}set toolbarItems(e){this._setOption("toolbarItems",e)}get visible(){return this._getOption("visible")}set visible(e){this._setOption("visible",e)}get width(){return this._getOption("width")}set width(e){this._setOption("width",e)}get toolbarItemsChildren(){return this._getOption("toolbarItems")}set toolbarItemsChildren(e){this.setChildren("toolbarItems",e)}_createInstance(e,t){return new a.a(e,t)}ngOnDestroy(){this._destroyWidget()}ngOnChanges(e){super.ngOnChanges(e),this.setupChanges("toolbarItems",e)}setupChanges(e,t){e in this._optionsToUpdate||this._idh.setup(e,t)}ngDoCheck(){this._idh.doCheck("toolbarItems"),this._watcherHelper.checkWatchers(),super.ngDoCheck(),super.clearChangedOptions()}_setOption(e,t){let i=this._idh.setupSingle(e,t),r=null!==this._idh.getChanges(e,t);(i||r)&&super._setOption(e,t)}};return e.\u0275fac=function(t){return new(t||e)(n["\u0275\u0275directiveInject"](n.ElementRef),n["\u0275\u0275directiveInject"](n.NgZone),n["\u0275\u0275directiveInject"](s.e),n["\u0275\u0275directiveInject"](s.j),n["\u0275\u0275directiveInject"](s.g),n["\u0275\u0275directiveInject"](s.i),n["\u0275\u0275directiveInject"](r.h),n["\u0275\u0275directiveInject"](n.PLATFORM_ID))},e.\u0275cmp=n["\u0275\u0275defineComponent"]({type:e,selectors:[["dx-popup"]],contentQueries:function(e,t,i){if(1&e&&n["\u0275\u0275contentQuery"](i,o.L,!1),2&e){let e;n["\u0275\u0275queryRefresh"](e=n["\u0275\u0275loadQuery"]())&&(t.toolbarItemsChildren=e)}},inputs:{accessKey:"accessKey",animation:"animation",closeOnOutsideClick:"closeOnOutsideClick",container:"container",contentTemplate:"contentTemplate",deferRendering:"deferRendering",disabled:"disabled",dragEnabled:"dragEnabled",elementAttr:"elementAttr",focusStateEnabled:"focusStateEnabled",fullScreen:"fullScreen",height:"height",hint:"hint",hoverStateEnabled:"hoverStateEnabled",maxHeight:"maxHeight",maxWidth:"maxWidth",minHeight:"minHeight",minWidth:"minWidth",position:"position",resizeEnabled:"resizeEnabled",rtlEnabled:"rtlEnabled",shading:"shading",shadingColor:"shadingColor",showCloseButton:"showCloseButton",showTitle:"showTitle",tabIndex:"tabIndex",title:"title",titleTemplate:"titleTemplate",toolbarItems:"toolbarItems",visible:"visible",width:"width"},outputs:{onContentReady:"onContentReady",onDisposing:"onDisposing",onHidden:"onHidden",onHiding:"onHiding",onInitialized:"onInitialized",onOptionChanged:"onOptionChanged",onResize:"onResize",onResizeEnd:"onResizeEnd",onResizeStart:"onResizeStart",onShowing:"onShowing",onShown:"onShown",onTitleRendered:"onTitleRendered",accessKeyChange:"accessKeyChange",animationChange:"animationChange",closeOnOutsideClickChange:"closeOnOutsideClickChange",containerChange:"containerChange",contentTemplateChange:"contentTemplateChange",deferRenderingChange:"deferRenderingChange",disabledChange:"disabledChange",dragEnabledChange:"dragEnabledChange",elementAttrChange:"elementAttrChange",focusStateEnabledChange:"focusStateEnabledChange",fullScreenChange:"fullScreenChange",heightChange:"heightChange",hintChange:"hintChange",hoverStateEnabledChange:"hoverStateEnabledChange",maxHeightChange:"maxHeightChange",maxWidthChange:"maxWidthChange",minHeightChange:"minHeightChange",minWidthChange:"minWidthChange",positionChange:"positionChange",resizeEnabledChange:"resizeEnabledChange",rtlEnabledChange:"rtlEnabledChange",shadingChange:"shadingChange",shadingColorChange:"shadingColorChange",showCloseButtonChange:"showCloseButtonChange",showTitleChange:"showTitleChange",tabIndexChange:"tabIndexChange",titleChange:"titleChange",titleTemplateChange:"titleTemplateChange",toolbarItemsChange:"toolbarItemsChange",visibleChange:"visibleChange",widthChange:"widthChange"},features:[n["\u0275\u0275ProvidersFeature"]([s.e,s.j,s.i,s.g]),n["\u0275\u0275InheritDefinitionFeature"],n["\u0275\u0275NgOnChangesFeature"]],ngContentSelectors:l,decls:1,vars:0,template:function(e,t){1&e&&(n["\u0275\u0275projectionDef"](),n["\u0275\u0275projection"](0))},encapsulation:2}),e})(),d=(()=>{let e=class{};return e.\u0275mod=n["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=n["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[o.bb,o.Gc,o.Vd,o.vd,o.hb,o.lb,o.sb,o.id,o.jd,o.M,s.c,s.f,r.b],o.bb,o.Gc,o.Vd,o.vd,o.hb,o.lb,o.sb,o.id,o.jd,o.M,s.f]}),e})()}}]);