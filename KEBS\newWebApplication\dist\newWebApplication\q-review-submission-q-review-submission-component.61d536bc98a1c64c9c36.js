(window.webpackJsonp=window.webpackJsonp||[]).push([[857],{"1fQ3":function(e,t,n){"use strict";n.r(t),n.d(t,"QReviewSubmissionComponent",(function(){return O}));var r=n("0IaG"),i=n("fXoL"),o=n("bTqV"),l=n("NFeN"),a=n("ofXK"),d=n("FKr1"),m=n("xG9w");let c=(()=>{class e{transform(e,t){console.log(e,t);let n=[];return t.forEach((t,r)=>{let i=m.where(e,{uuidLeft:t.uuidLeft});n.push(i[0].valueRight)}),n}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=i["\u0275\u0275definePipe"]({name:"arrangeMatchAnswer",type:e,pure:!0}),e})();function p(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"mat-icon",20),i["\u0275\u0275text"](1," done "),i["\u0275\u0275elementEnd"]())}function s(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"mat-icon",21),i["\u0275\u0275text"](1," close "),i["\u0275\u0275elementEnd"]())}function f(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"span"),i["\u0275\u0275template"](1,p,2,0,"mat-icon",18),i["\u0275\u0275template"](2,s,2,0,"mat-icon",19),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]().$implicit,t=i["\u0275\u0275nextContext"](2).$implicit;i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",e.uuid==t.submittedAnswer.uuid&&e.isCorrect),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",e.uuid==t.submittedAnswer.uuid&&!e.isCorrect)}}function v(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",15),i["\u0275\u0275elementStart"](1,"div",16),i["\u0275\u0275elementStart"](2,"div",17),i["\u0275\u0275text"](3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"div",3),i["\u0275\u0275text"](5),i["\u0275\u0275template"](6,f,3,2,"span",12),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=t.index,r=i["\u0275\u0275nextContext"](2).$implicit,o=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](2),i["\u0275\u0275styleProp"]("background-color",e.isCorrect?"#29903b":"#e0e0e0")("color",e.isCorrect?"white":"black"),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" ",o.getCharCode(65+n)," "),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"](" ",e.choiceName," "),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",r.submittedAnswer)}}function x(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",13),i["\u0275\u0275elementStart"](1,"div",1),i["\u0275\u0275template"](2,v,7,7,"div",14),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]().$implicit;i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngForOf",e.choices)}}function u(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"mat-icon",20),i["\u0275\u0275text"](1," done "),i["\u0275\u0275elementEnd"]())}function g(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"mat-icon",21),i["\u0275\u0275text"](1," close "),i["\u0275\u0275elementEnd"]())}function S(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",28),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" ",e.left," ")}}function E(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",28),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" ",e.right," ")}}function w(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",28),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" ",e," ")}}function b(e,t){if(1&e&&(i["\u0275\u0275elementContainerStart"](0),i["\u0275\u0275elementStart"](1,"div",13),i["\u0275\u0275elementStart"](2,"div",22),i["\u0275\u0275text"](3,"Question"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"div",23),i["\u0275\u0275text"](5,"Correct Answer"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](6,"div",24),i["\u0275\u0275text"](7," Your Answer "),i["\u0275\u0275template"](8,u,2,0,"mat-icon",18),i["\u0275\u0275template"](9,g,2,0,"mat-icon",19),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](10,"div",25),i["\u0275\u0275elementStart"](11,"div",22),i["\u0275\u0275elementStart"](12,"div",26),i["\u0275\u0275template"](13,S,2,1,"div",27),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](14,"div",22),i["\u0275\u0275elementStart"](15,"div",26),i["\u0275\u0275template"](16,E,2,1,"div",27),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](17,"div",22),i["\u0275\u0275elementStart"](18,"div",26),i["\u0275\u0275template"](19,w,2,1,"div",27),i["\u0275\u0275pipe"](20,"arrangeMatchAnswer"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementContainerEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]().$implicit;i["\u0275\u0275advance"](8),i["\u0275\u0275property"]("ngIf",e.isAnswerCorrect),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",!e.isAnswerCorrect),i["\u0275\u0275advance"](4),i["\u0275\u0275property"]("ngForOf",e.pairs),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngForOf",e.pairs),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngForOf",i["\u0275\u0275pipeBind2"](20,5,e.submittedAnswer,e.pairs))}}function C(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"mat-icon",20),i["\u0275\u0275text"](1," done "),i["\u0275\u0275elementEnd"]())}function y(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"mat-icon",21),i["\u0275\u0275text"](1," close "),i["\u0275\u0275elementEnd"]())}function h(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",28),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" ",e.item," ")}}function I(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",28),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" ",e.item," ")}}function A(e,t){if(1&e&&(i["\u0275\u0275elementContainerStart"](0),i["\u0275\u0275elementStart"](1,"div",13),i["\u0275\u0275elementStart"](2,"div",23),i["\u0275\u0275text"](3,"Correct Answer"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"div",24),i["\u0275\u0275text"](5," Your Answer "),i["\u0275\u0275template"](6,C,2,0,"mat-icon",18),i["\u0275\u0275template"](7,y,2,0,"mat-icon",19),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](8,"div",25),i["\u0275\u0275elementStart"](9,"div",22),i["\u0275\u0275elementStart"](10,"div",26),i["\u0275\u0275template"](11,h,2,1,"div",27),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](12,"div",22),i["\u0275\u0275elementStart"](13,"div",26),i["\u0275\u0275template"](14,I,2,1,"div",27),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](15,"pre"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementContainerEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]().$implicit;i["\u0275\u0275advance"](6),i["\u0275\u0275property"]("ngIf",e.isAnswerCorrect),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",!e.isAnswerCorrect),i["\u0275\u0275advance"](4),i["\u0275\u0275property"]("ngForOf",e.orderedAnswers),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngForOf",e.submittedAnswer)}}function F(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",8),i["\u0275\u0275elementStart"](1,"div",1),i["\u0275\u0275elementStart"](2,"div",0),i["\u0275\u0275elementStart"](3,"div",9),i["\u0275\u0275elementStart"](4,"strong"),i["\u0275\u0275text"](5),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](6,"div",2),i["\u0275\u0275elementStart"](7,"div",10),i["\u0275\u0275text"](8),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](9,x,3,1,"div",11),i["\u0275\u0275template"](10,b,21,8,"ng-container",12),i["\u0275\u0275template"](11,A,16,4,"ng-container",12),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=t.index,r=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](5),i["\u0275\u0275textInterpolate2"]("Q. ",n+1," of ",r.data.length,""),i["\u0275\u0275advance"](3),i["\u0275\u0275textInterpolate1"](" ",null==e?null:e.questionName," "),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",1==e.questionTypeId),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",2==e.questionTypeId),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",3==e.questionTypeId)}}let O=(()=>{class e{constructor(e,t){this.dialogRef=e,this.data=t,this.getCharCode=e=>String.fromCharCode(e)}ngOnInit(){}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](r.h),i["\u0275\u0275directiveInject"](r.a))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["learner-q-review-submission"]],decls:11,vars:1,consts:[[1,"row"],[1,"col-12"],[1,"row","mt-3"],[1,"col","d-flex","align-items-center"],[2,"font-size","18px"],[1,"col-0","d-flex","align-items-center"],["mat-icon-button","",2,"color","gray",3,"click"],["class","row mt-3 pb-4 border-bottom solid",4,"ngFor","ngForOf"],[1,"row","mt-3","pb-4","border-bottom","solid"],[1,"col-12",2,"font-size","16px"],[1,"col-12",2,"font-size","17px"],["class","row mt-4",4,"ngIf"],[4,"ngIf"],[1,"row","mt-4"],["class","row mb-3",4,"ngFor","ngForOf"],[1,"row","mb-3"],[1,"col-0"],["matRipple","",1,"circle-choice"],["style","color: green; margin-left: 13px",4,"ngIf"],["style","color: #cf0001; margin-left: 13px",4,"ngIf"],[2,"color","green","margin-left","13px"],[2,"color","#cf0001","margin-left","13px"],[1,"col-4"],[1,"col-4",2,"color","green"],[1,"col-4","d-flex","align-items-center",2,"color","#cf0001"],[1,"row","mt-2"],[1,"example-list"],["class","example-box",4,"ngFor","ngForOf"],[1,"example-box"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275elementStart"](1,"div",1),i["\u0275\u0275elementStart"](2,"div",2),i["\u0275\u0275elementStart"](3,"div",3),i["\u0275\u0275elementStart"](4,"span",4),i["\u0275\u0275text"](5,"Review Answers"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](6,"div",5),i["\u0275\u0275elementStart"](7,"button",6),i["\u0275\u0275listener"]("click",(function(){return t.dialogRef.close()})),i["\u0275\u0275elementStart"](8,"mat-icon"),i["\u0275\u0275text"](9,"cancel"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](10,F,12,6,"div",7)),2&e&&(i["\u0275\u0275advance"](10),i["\u0275\u0275property"]("ngForOf",t.data))},directives:[o.a,l.a,a.NgForOf,a.NgIf,d.u],pipes:[c],styles:[".circle-choice[_ngcontent-%COMP%]{height:30px;width:30px;background-color:#e0e0e0;display:flex;align-items:center;font-weight:800;justify-content:center;border-radius:50%;margin-right:13px}.example-list[_ngcontent-%COMP%]{width:500px;max-width:100%;border:1px solid #ccc;min-height:60px;display:block;background:#fff;border-radius:4px;overflow:hidden}.example-box[_ngcontent-%COMP%]{padding:20px 10px;border-bottom:1px solid #ccc;color:rgba(0,0,0,.87);display:flex;flex-direction:row;align-items:center;justify-content:space-between;box-sizing:border-box;background:#fff;font-size:14px}"]}),e})()}}]);