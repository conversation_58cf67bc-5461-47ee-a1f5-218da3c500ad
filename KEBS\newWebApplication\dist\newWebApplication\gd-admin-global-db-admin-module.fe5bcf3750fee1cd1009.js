(window.webpackJsonp=window.webpackJsonp||[]).push([[721],{T1jG:function(t,e,n){"use strict";n.r(e),n.d(e,"GlobalDbAdminModule",(function(){return nt}));var o=n("ofXK"),a=n("tyNb"),i=n("mrSG"),r=n("XNiG"),l=n("1G5W"),s=n("fXoL"),d=n("ye8K"),c=n("1A3m"),m=n("dlKe"),g=n("f0Cb");function h(t,e){if(1&t&&(s["\u0275\u0275elementStart"](0,"div"),s["\u0275\u0275elementStart"](1,"div",7),s["\u0275\u0275elementStart"](2,"div",8),s["\u0275\u0275text"](3),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](4,"div",8),s["\u0275\u0275text"](5),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](6,"div",9),s["\u0275\u0275text"](7),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](8,"div",8),s["\u0275\u0275text"](9),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](10,"div",10),s["\u0275\u0275text"](11),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275element"](12,"mat-divider"),s["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit;s["\u0275\u0275advance"](3),s["\u0275\u0275textInterpolate1"](" ",t.oid," "),s["\u0275\u0275advance"](2),s["\u0275\u0275textInterpolate1"](" ",t.db_name," "),s["\u0275\u0275advance"](2),s["\u0275\u0275textInterpolate1"](" ",t.table_name," "),s["\u0275\u0275advance"](2),s["\u0275\u0275textInterpolate1"](" ",t.updated_time," "),s["\u0275\u0275advance"](2),s["\u0275\u0275textInterpolate1"](" ",t.description," ")}}let p=(()=>{class t{constructor(t,e){this._apiService=t,this._toaster=e,this._onDestroy=new r.b,this.index=0,this.searchText="",this.selectLogs=[]}ngOnInit(){this.getSelectLogs(this.index,this.searchText)}getSelectLogs(t,e){this._apiService.getSelectLogs(t,e).pipe(Object(l.a)(this._onDestroy)).subscribe(t=>Object(i.c)(this,void 0,void 0,(function*(){"S"==t.messType?this.selectLogs.push(...t.data):this._toaster.showError(t.messText,"Error",3e3)})),t=>{this._toaster.showError(t,"Error",3e3),console.log(t)})}onScrollDown(){this.index=this.index+30,this.getSelectLogs(this.index,this.searchText)}}return t.\u0275fac=function(e){return new(e||t)(s["\u0275\u0275directiveInject"](d.a),s["\u0275\u0275directiveInject"](c.a))},t.\u0275cmp=s["\u0275\u0275defineComponent"]({type:t,selectors:[["app-selectlogview"]],decls:19,vars:3,consts:[[1,"col-12","pt-2"],[1,"row","pb-2","pt-2","mt-3",2,"background-color","white","border-radius","5px"],[1,"col-2"],[1,"tableHeader"],[1,"col-4"],["infinite-scroll","",1,"col-12","infinite-scroll-auto",3,"infiniteScrollDistance","scrollWindow","scrolled"],[4,"ngFor","ngForOf"],[1,"row","pb-2","pt-3"],[1,"col-2","tableData"],[1,"col-2","tableData",2,"overflow","overlay"],[1,"col-4","tableData",2,"overflow","overlay"]],template:function(t,e){1&t&&(s["\u0275\u0275elementStart"](0,"div",0),s["\u0275\u0275elementStart"](1,"div",1),s["\u0275\u0275elementStart"](2,"div",2),s["\u0275\u0275elementStart"](3,"span",3),s["\u0275\u0275text"](4,"OID"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](5,"div",2),s["\u0275\u0275elementStart"](6,"span",3),s["\u0275\u0275text"](7,"DB NAME"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](8,"div",2),s["\u0275\u0275elementStart"](9,"span",3),s["\u0275\u0275text"](10,"TABLE NAME"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](11,"div",2),s["\u0275\u0275elementStart"](12,"span",3),s["\u0275\u0275text"](13,"UPDATED TIME"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](14,"div",4),s["\u0275\u0275elementStart"](15,"span",3),s["\u0275\u0275text"](16,"DESCRIPTION"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](17,"div",5),s["\u0275\u0275listener"]("scrolled",(function(){return e.onScrollDown()})),s["\u0275\u0275template"](18,h,13,5,"div",6),s["\u0275\u0275elementEnd"]()),2&t&&(s["\u0275\u0275advance"](17),s["\u0275\u0275property"]("infiniteScrollDistance",2)("scrollWindow",!1),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngForOf",e.selectLogs))},directives:[m.a,o.NgForOf,g.a],styles:[".tableHeader[_ngcontent-%COMP%]{width:92px;height:16px;font-weight:400;font-size:11px;text-transform:uppercase;color:#b9c0ca}.tableData[_ngcontent-%COMP%], .tableHeader[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;line-height:16px;letter-spacing:.02em}.tableData[_ngcontent-%COMP%]{font-weight:500;font-size:12px;text-transform:capitalize;color:#526179}.navigateButton[_ngcontent-%COMP%]{border:none;background-color:initial}.tableClass[_ngcontent-%COMP%]{width:1240px;height:70vh;background:#fff;box-shadow:0 2px 1px rgba(0,0,0,.12);border-radius:4px;overflow-y:scroll}.infinite-scroll-auto[_ngcontent-%COMP%]{height:470px;overflow:scroll}"]}),t})();var u=n("3Pt+"),f=n("xG9w"),b=n("+0xr"),x=n("0IaG"),_=n("kmnG"),v=n("qFsG");function w(t,e){if(1&t&&(s["\u0275\u0275elementStart"](0,"div",9),s["\u0275\u0275elementStart"](1,"mat-form-field",10),s["\u0275\u0275elementStart"](2,"mat-label"),s["\u0275\u0275text"](3),s["\u0275\u0275elementEnd"](),s["\u0275\u0275element"](4,"input",11),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit;s["\u0275\u0275advance"](3),s["\u0275\u0275textInterpolate"](t.key),s["\u0275\u0275advance"](1),s["\u0275\u0275propertyInterpolate"]("formControlName",t.key)}}let C=(()=>{class t{constructor(t,e,n,o){this.dialogRef=t,this.data=e,this._apiservice=n,this._toaster=o,this.form={},this.numarr=[],this.flag=0,this.typecheckFL=0,this.canSubmit=0,this.dataKeys=[],this.dataArray=[],this.saveButtonClick=!1,this._onDestroy=new r.b}ngOnInit(){for(let t=0;t<this.data.tableData.length;t++)this.form[this.data.tableData[t].key]=new u.j(this.data.tableData[t].value);this.editForm=new u.m(this.form)}saveForm(){this.saveButtonClick=!0,console.log(this.editForm),this._apiservice.editFormDetails(this.editForm.value,this.data.db_name,this.data.table_name).pipe(Object(l.a)(this._onDestroy)).subscribe(t=>Object(i.c)(this,void 0,void 0,(function*(){"S"==t.messType?(this.saveButtonClick=!1,this._toaster.showSuccess(t.messText,"Success",3e3),this.dialogRef.close({event:"close"})):(this.saveButtonClick=!1,this._toaster.showError(t.messText,"Error",3e3))})),t=>{this.saveButtonClick=!1,this._toaster.showError("Something went wrong !","Error",3e3),console.log(t)})}closeDialog(){this.dialogRef.close({event:"close"})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}}return t.\u0275fac=function(e){return new(e||t)(s["\u0275\u0275directiveInject"](x.h),s["\u0275\u0275directiveInject"](x.a),s["\u0275\u0275directiveInject"](d.a),s["\u0275\u0275directiveInject"](c.a))},t.\u0275cmp=s["\u0275\u0275defineComponent"]({type:t,selectors:[["app-edit-dialog"]],decls:14,vars:2,consts:[[1,"heading"],[3,"formGroup"],[1,"col-12","formEdit"],[1,"row"],["class","col-4",4,"ngFor","ngForOf"],[1,"col-12"],[1,"col-2"],[1,"saveButton",3,"disabled","click"],[1,"closeButton",3,"click"],[1,"col-4"],["appearance","outline",1,"form-field"],["type","text","matInput","",3,"formControlName"]],template:function(t,e){1&t&&(s["\u0275\u0275elementStart"](0,"div",0),s["\u0275\u0275text"](1," Edit From\n"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](2,"form",1),s["\u0275\u0275elementStart"](3,"div",2),s["\u0275\u0275elementStart"](4,"div",3),s["\u0275\u0275template"](5,w,5,2,"div",4),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](6,"div",5),s["\u0275\u0275elementStart"](7,"div",3),s["\u0275\u0275elementStart"](8,"div",6),s["\u0275\u0275elementStart"](9,"button",7),s["\u0275\u0275listener"]("click",(function(){return e.saveForm()})),s["\u0275\u0275text"](10,"Save"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](11,"div",6),s["\u0275\u0275elementStart"](12,"button",8),s["\u0275\u0275listener"]("click",(function(){return e.closeDialog()})),s["\u0275\u0275text"](13,"Close"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&t&&(s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("formGroup",e.editForm),s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("ngForOf",e.data.tableData))},directives:[u.J,u.w,u.n,o.NgForOf,_.c,_.g,v.b,u.e,u.v,u.l],styles:['@charset "UTF-8";.closeBtn[_ngcontent-%COMP%]{color:red;margin-left:384px}.btn-danger[_ngcontent-%COMP%]{margin-left:392px;--bs-btn-bg:#fff}.bi-x-circle[_ngcontent-%COMP%]:before{content:"\uf623";color:red}.closeButton[_ngcontent-%COMP%]{box-sizing:border-box;flex-direction:row;align-items:center;width:74px;height:40px;text-align:center;border:1px solid #45546e;border-radius:4px}.saveButton[_ngcontent-%COMP%]{width:78px;height:40px;font-size:14px;line-height:16px;background-color:#ee4961;letter-spacing:-.02em;border:none;border-radius:5px;color:#fff}.heading[_ngcontent-%COMP%], .saveButton[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;font-weight:700;align-items:center;text-transform:capitalize;text-align:center}.heading[_ngcontent-%COMP%]{width:263px;height:24px;font-size:20px;line-height:24px;display:flex;justify-content:center;color:#26303e}.formEdit[_ngcontent-%COMP%]{margin-top:70px}']}),t})();var O=n("EUZL"),y=n("XXEo"),S=n("XhcP"),M=n("NFeN"),P=n("d3UM"),E=n("FKr1"),D=n("bTqV"),F=n("Xa2L");const k=["drawer"];function N(t,e){1&t&&s["\u0275\u0275element"](0,"i",24)}function I(t,e){if(1&t&&(s["\u0275\u0275elementStart"](0,"mat-option",33),s["\u0275\u0275text"](1),s["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit;s["\u0275\u0275property"]("value",t.db_name),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate"](t.db_name)}}function T(t,e){if(1&t&&(s["\u0275\u0275elementStart"](0,"mat-option",33),s["\u0275\u0275text"](1),s["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit;s["\u0275\u0275property"]("value",t.TABLE_NAME),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate"](t.TABLE_NAME)}}function V(t,e){if(1&t){const t=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"button",34),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](t),s["\u0275\u0275nextContext"](2),s["\u0275\u0275reference"](4).toggle()})),s["\u0275\u0275elementStart"](1,"mat-icon"),s["\u0275\u0275text"](2,"description"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}}function L(t,e){if(1&t){const t=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"div",26),s["\u0275\u0275elementStart"](1,"button",35),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](t),s["\u0275\u0275nextContext"](2).editConfig()})),s["\u0275\u0275elementStart"](2,"mat-icon"),s["\u0275\u0275text"](3,"manage_accounts"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}}function j(t,e){if(1&t){const t=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"form",11),s["\u0275\u0275elementStart"](1,"div",25),s["\u0275\u0275elementStart"](2,"div",5),s["\u0275\u0275elementStart"](3,"div",26),s["\u0275\u0275elementStart"](4,"mat-form-field",13),s["\u0275\u0275elementStart"](5,"mat-select",27),s["\u0275\u0275elementStart"](6,"mat-option"),s["\u0275\u0275text"](7,"None"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](8,I,2,2,"mat-option",28),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](9,"div",29),s["\u0275\u0275elementStart"](10,"mat-form-field",13),s["\u0275\u0275elementStart"](11,"mat-select",30),s["\u0275\u0275listener"]("selectionChange",(function(e){return s["\u0275\u0275restoreView"](t),s["\u0275\u0275nextContext"]().getTableChange(e)})),s["\u0275\u0275elementStart"](12,"mat-option"),s["\u0275\u0275text"](13,"None"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](14,T,2,2,"mat-option",28),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275element"](15,"div",16),s["\u0275\u0275elementStart"](16,"div",26),s["\u0275\u0275template"](17,V,3,0,"button",31),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](18,L,4,0,"div",32),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}if(2&t){const t=s["\u0275\u0275nextContext"]();s["\u0275\u0275property"]("formGroup",t.dropDownForm),s["\u0275\u0275advance"](8),s["\u0275\u0275property"]("ngForOf",t.dbNameList),s["\u0275\u0275advance"](6),s["\u0275\u0275property"]("ngForOf",t.tableNameList),s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("ngIf",t.tableLoad&&t.is_read&&t.noData),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",t.is_config)}}function A(t,e){if(1&t&&(s["\u0275\u0275elementStart"](0,"th",44),s["\u0275\u0275text"](1),s["\u0275\u0275pipe"](2,"uppercase"),s["\u0275\u0275elementEnd"]()),2&t){const t=s["\u0275\u0275nextContext"]().$implicit;s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate1"](" ",s["\u0275\u0275pipeBind1"](2,1,t)," ")}}function B(t,e){if(1&t&&(s["\u0275\u0275elementStart"](0,"td",45),s["\u0275\u0275text"](1),s["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit,n=s["\u0275\u0275nextContext"]().$implicit;s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate1"](" ",t[n]," ")}}function U(t,e){1&t&&(s["\u0275\u0275elementContainerStart"](0,43),s["\u0275\u0275template"](1,A,3,3,"th",39),s["\u0275\u0275template"](2,B,2,1,"td",40),s["\u0275\u0275elementContainerEnd"]()),2&t&&s["\u0275\u0275propertyInterpolate"]("matColumnDef",e.$implicit)}function R(t,e){1&t&&(s["\u0275\u0275elementStart"](0,"th",44),s["\u0275\u0275text"](1,"-"),s["\u0275\u0275elementEnd"]())}function z(t,e){if(1&t){const t=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"td",45),s["\u0275\u0275elementStart"](1,"button",46),s["\u0275\u0275listener"]("click",(function(){s["\u0275\u0275restoreView"](t);const n=e.$implicit;return s["\u0275\u0275nextContext"](2).getRowDetails(n)})),s["\u0275\u0275elementStart"](2,"mat-icon"),s["\u0275\u0275text"](3,"edit"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}}function K(t,e){1&t&&s["\u0275\u0275element"](0,"tr",47)}function W(t,e){1&t&&s["\u0275\u0275element"](0,"tr",48)}function G(t,e){if(1&t&&(s["\u0275\u0275elementStart"](0,"div"),s["\u0275\u0275elementStart"](1,"table",36),s["\u0275\u0275template"](2,U,3,1,"ng-container",37),s["\u0275\u0275elementContainerStart"](3,38),s["\u0275\u0275template"](4,R,2,0,"th",39),s["\u0275\u0275template"](5,z,4,0,"td",40),s["\u0275\u0275elementContainerEnd"](),s["\u0275\u0275template"](6,K,1,0,"tr",41),s["\u0275\u0275template"](7,W,1,0,"tr",42),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&t){const t=s["\u0275\u0275nextContext"]();s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("dataSource",t.dataSource),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngForOf",t.columnName),s["\u0275\u0275advance"](4),s["\u0275\u0275property"]("matHeaderRowDef",t.displayedColumns)("matHeaderRowDefSticky",!0),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("matRowDefColumns",t.displayedColumns)}}function H(t,e){1&t&&(s["\u0275\u0275elementStart"](0,"div",49),s["\u0275\u0275elementStart"](1,"div",50),s["\u0275\u0275element"](2,"mat-spinner",51),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]())}let $=(()=>{class t{constructor(t,e,n,o,a,i){this._apiService=t,this.fb=e,this.dialog=n,this._toaster=o,this._router=a,this.authService=i,this._onDestroy=new r.b,this.myControl=new u.j(""),this.dbNameList=[],this.tableNameList=[],this.tableValue=[],this.columnName=[],this.index=0,this.clickedRows=new Set,this.isLoading=!1,this.tableLoad=!1,this.dataArray=[],this.dataTypeName=[],this.displayedColumns=[],this.noData=!1,this.dataIndex=0,this.is_config=!1,this.is_write=!1,this.is_read=!1,this.currentUser={},this.downloadData=[],this.downloadDisable=!1}ngOnInit(){return Object(i.c)(this,void 0,void 0,(function*(){this.currentUser=this.authService.getProfile().profile,console.log(this.currentUser),this.authKey=yield this.getAuthKey(),this.getDBAndTableListrecord()}))}getDBAndTableListrecord(){console.log(this.authKey),this._apiService.getDBAndTableList(this.currentUser,this.authKey).pipe(Object(l.a)(this._onDestroy)).subscribe(t=>Object(i.c)(this,void 0,void 0,(function*(){if("S"==t.messType&&t.data&&t.data.length>0){for(let e of JSON.parse(t.data[0].table_name_1?t.data[0].table_name_1:[""]))this.tableNameList.push({TABLE_NAME:e});this.dbNameList.push({db_name:t.data[0].db_name}),this.is_config=!!t.data[0].is_config,this.is_read=!!t.data[0].is_read,this.is_write=!!t.data[0].is_write,this.is_read?this.createForm():this._toaster.showError("You have no access to this page !","Error",3e3),console.log(this.tableNameList),console.log(this.dbNameList)}else this._toaster.showError(t.messText,"Error",3e3),this.tableNameList=[]})),t=>{console.log(t)})}createForm(){this.dropDownForm=this.fb.group({dbName:[""],tableName:[""]}),this.countForm=this.fb.group({minValue:[""],maxValue:[""]})}getTableChange(){this.index=0,this.tableLoad=!1,this.tableValue=[],this.isLoading=!0,null!=this.dropDownForm.value.dbName&&null!=this.dropDownForm.value.tableName?this._apiService.getTableDataForAdmin(this.dropDownForm.value.tableName,this.dropDownForm.value.dbName,this.index,this.currentUser,this.authKey).pipe(Object(l.a)(this._onDestroy)).subscribe(t=>Object(i.c)(this,void 0,void 0,(function*(){"S"==t.messType&&t.data&&t.data.length>0?(this.tableValue=t.data,console.log(t.data),console.log(this.tableValue),this.isLoading=!1,this.tableLoad=!0,this.noData=!0,this.columnName=f.keys(t.data[0]),this.displayedColumns=f.keys(t.data[0]),this.dataTypeName=t.type,console.log(this.dataTypeName),this.countList=t.count,this.countForm.setValue({minValue:1,maxValue:Number(t.count)}),console.log(this.displayedColumns),console.log(this.columnName),this.dataSource=new b.l(this.tableValue),console.log(this.dataSource)):(this.noData=!1,this._toaster.showWarning(t.messText,"Warning"),this.isLoading=!1,this.tableLoad=!0,this.tableValue=[])})),t=>{this.isLoading=!1,this.tableLoad=!0,this._toaster.showError(t,"Error",3e3),console.log(t),this.tableValue=[]}):(this.isLoading=!1,this._toaster.showWarning("Please Select DB name or TableName to Continue","Warning"))}getRowDetails(t){this.dataIndex=0,this.clickedRows.clear(),this.dataArray=[];for(let e of f.keys(t))this.dataArray.push({key:e,value:t[""+e],type:this.dataTypeName[this.dataIndex].DATA_TYPE}),this.dataIndex=this.dataIndex+1;console.log(this.dataArray),this.dialog.open(C,{height:"100%",width:"55%",position:{right:"0px"},data:{tableData:this.dataArray,db_name:this.dropDownForm.value.dbName,table_name:this.dropDownForm.value.tableName}}).afterClosed().subscribe(t=>{this.getTableChange()})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}onScrollDown(){this.index=this.index+30,console.log("scroll works"),this._apiService.getTableDataForAdmin(this.dropDownForm.value.tableName,this.dropDownForm.value.dbName,this.index,this.currentUser,this.authKey).pipe(Object(l.a)(this._onDestroy)).subscribe(t=>Object(i.c)(this,void 0,void 0,(function*(){"S"==t.messType&&t.data&&t.data.length>0?(this.tableValue.push(...t.data),this.dataSource=new b.l(this.tableValue),console.log(this.dataSource)):this.tableValue=[]})),t=>{console.log(t)})}editConfig(){this._router.navigateByUrl("/main/gdadmin/home/<USER>")}getAuthKey(){return new Promise((t,e)=>{this._apiService.getAuthingKey(this.currentUser).pipe(Object(l.a)(this._onDestroy)).subscribe(n=>Object(i.c)(this,void 0,void 0,(function*(){if("S"==n.messType&&n.data.length>0)return t(n.data);this._toaster.showError(n.messText,"Error",3e3),e()})),t=>{console.log(t),e()})})}closedrawer(){this.drawer.toggle()}exceldownload(){return Object(i.c)(this,void 0,void 0,(function*(){if(this.downloadDisable=!0,0!=this.countForm.value.minValue&&0!=this.countForm.value.maxValue||(this._toaster.showWarning("No Zero Values are allowed","Warrning"),this.downloadDisable=!1),this.countForm.value.minValue>this.countForm.value.maxValue)this._toaster.showWarning("Min Value Should not be Exceed than Max Value","Warrning"),this.downloadDisable=!1;else if(this.countForm.value.maxValue<this.countForm.value.minValue)this._toaster.showWarning("Max Value Should not be Lower than Min Value","Warrning"),this.downloadDisable=!1;else if(null==this.countForm.value.minValue)this._toaster.showWarning("Min Value Cannot be Empty","Warrning"),this.downloadDisable=!1;else if(null==this.countForm.value.maxValue)this._toaster.showWarning("Max Value Cannot be Empty","Warrning");else if(this.countForm.value.maxValue>this.countList)this._toaster.showWarning("Max Value Cannot be exceed total Count","Warning"),this.downloadDisable=!1;else if(this.countForm.value.maxValue-this.countForm.value.minValue>5e4)this._toaster.showWarning("Please Choose the limit of 50000","Warning"),this.downloadDisable=!1;else{let t=yield this.getBatchData(this.countForm.value.minValue-1,this.countForm.value.maxValue);console.log(t)}}))}getBatchData(t,e){return Object(i.c)(this,void 0,void 0,(function*(){if(!(t<=this.countForm.value.maxValue)){let t=".xlsx";t=this.dropDownForm.value.tableName.concat(t);const e=String(t),n=O.utils.json_to_sheet(this.downloadData),o=O.utils.book_new();return O.utils.book_append_sheet(o,n,"test"),O.writeFile(o,e),this.drawer.toggle(),this.downloadData=[],this.downloadDisable=!1,!0}this._apiService.getdataForDownload(t,100,this.dropDownForm.value.tableName,this.currentUser,this.authKey,e,this.countForm.value.maxValue).pipe(Object(l.a)(this._onDestroy)).subscribe(n=>Object(i.c)(this,void 0,void 0,(function*(){"S"==n.messType&&n.data&&n.data.length>0?(this.downloadData.push(...n.data),console.log(this.downloadData),t+=100,e-=100,yield this.getBatchData(t,e)):(t+=100,console.log(this.downloadData),yield this.getBatchData(t,e))})),t=>{console.log(t)})}))}}return t.\u0275fac=function(e){return new(e||t)(s["\u0275\u0275directiveInject"](d.a),s["\u0275\u0275directiveInject"](u.i),s["\u0275\u0275directiveInject"](x.b),s["\u0275\u0275directiveInject"](c.a),s["\u0275\u0275directiveInject"](a.g),s["\u0275\u0275directiveInject"](y.a))},t.\u0275cmp=s["\u0275\u0275defineComponent"]({type:t,selectors:[["app-gd-admin-home"]],viewQuery:function(t,e){if(1&t&&s["\u0275\u0275viewQuery"](k,!0),2&t){let t;s["\u0275\u0275queryRefresh"](t=s["\u0275\u0275loadQuery"]())&&(e.drawer=t.first)}},decls:43,vars:10,consts:[["rel","stylesheet","href",s["\u0275\u0275trustConstantResourceUrl"]("https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css")],["hasBackdrop","false",1,"drawer","gd-admin-landing"],["mode","over","position","end",1,"side-drawer"],["drawer",""],[1,"col-12"],[1,"row"],[1,"col-3"],[1,"col-2","pt-2"],[1,"closeButton",3,"click"],[1,"col-7","heading"],[1,"col-2",2,"display","none"],[3,"formGroup"],[1,"col-6","pl-3","pt-3"],["appearance","outline",2,"width","200px","height","50px"],["type","number","matInput","","formControlName","minValue","oninput","if(this.value<0){this.value= this.value * -1}"],["type","number","matInput","","formControlName","maxValue","oninput","if(this.value<0){this.value= this.value * -1}"],[1,"col-4"],[1,"saveButton",3,"disabled","click"],["class","fa fa-spinner fa-spin",4,"ngIf"],[2,"margin-left","0px !important"],[3,"formGroup",4,"ngIf"],["infinite-scroll","",1,"col-12","infinite-scroll-auto",3,"infiniteScrollDistance","scrollWindow","scrolled"],[4,"ngIf"],["class","row container d-flex mb-2 mt-6 flex-column horizontal-item-loading",4,"ngIf"],[1,"fa","fa-spinner","fa-spin"],[1,"col-12",2,"margin-top","20px"],[1,"col-2"],["formControlName","dbName","required","true","placeholder","DB Name"],[3,"value",4,"ngFor","ngForOf"],[1,"col-2","pl-4"],["formControlName","tableName","required","true","placeholder","Table Name",3,"selectionChange"],["class","configIcon","matTooltip","Download Excel",3,"click",4,"ngIf"],["class","col-2",4,"ngIf"],[3,"value"],["matTooltip","Download Excel",1,"configIcon",3,"click"],[1,"configIcon",3,"click"],["mat-table","",1,"mat-elevation-z8",3,"dataSource"],[3,"matColumnDef",4,"ngFor","ngForOf"],["matColumnDef","button"],["mat-header-cell","",4,"matHeaderCellDef"],["mat-cell","",4,"matCellDef"],["mat-header-row","",4,"matHeaderRowDef","matHeaderRowDefSticky"],["mat-row","","matTooltip","Click to edit","matTooltipPosition","right",4,"matRowDef","matRowDefColumns"],[3,"matColumnDef"],["mat-header-cell",""],["mat-cell",""],["mat-icon-button","",1,"editIcon",3,"click"],["mat-header-row",""],["mat-row","","matTooltip","Click to edit","matTooltipPosition","right"],[1,"row","container","d-flex","mb-2","mt-6","flex-column","horizontal-item-loading"],[1,"row","justify-content-center"],["diameter","40","matTooltip","Loading ..."]],template:function(t,e){1&t&&(s["\u0275\u0275elementStart"](0,"head"),s["\u0275\u0275element"](1,"link",0),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](2,"mat-drawer-container",1),s["\u0275\u0275elementStart"](3,"mat-drawer",2,3),s["\u0275\u0275elementStart"](5,"div",4),s["\u0275\u0275elementStart"](6,"div",5),s["\u0275\u0275element"](7,"div",6),s["\u0275\u0275elementStart"](8,"div",7),s["\u0275\u0275elementStart"](9,"button",8),s["\u0275\u0275listener"]("click",(function(){return e.closedrawer()})),s["\u0275\u0275text"](10,"close"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](11,"div",4),s["\u0275\u0275elementStart"](12,"div",5),s["\u0275\u0275elementStart"](13,"div",9),s["\u0275\u0275text"](14),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](15,"div",10),s["\u0275\u0275text"](16),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](17,"form",11),s["\u0275\u0275elementStart"](18,"div",4),s["\u0275\u0275elementStart"](19,"div",5),s["\u0275\u0275elementStart"](20,"div",12),s["\u0275\u0275elementStart"](21,"mat-form-field",13),s["\u0275\u0275elementStart"](22,"mat-label"),s["\u0275\u0275text"](23,"Minimium"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275element"](24,"input",14),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](25,"div",12),s["\u0275\u0275elementStart"](26,"mat-form-field",13),s["\u0275\u0275elementStart"](27,"mat-label"),s["\u0275\u0275text"](28,"Maximum"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275element"](29,"input",15),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](30,"div",4),s["\u0275\u0275elementStart"](31,"div",5),s["\u0275\u0275element"](32,"div",16),s["\u0275\u0275elementStart"](33,"div",6),s["\u0275\u0275elementStart"](34,"button",17),s["\u0275\u0275listener"]("click",(function(){return e.exceldownload()})),s["\u0275\u0275template"](35,N,1,0,"i",18),s["\u0275\u0275elementStart"](36,"mat-icon"),s["\u0275\u0275text"](37,"download"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](38,"mat-sidenav-content",19),s["\u0275\u0275template"](39,j,19,5,"form",20),s["\u0275\u0275elementStart"](40,"div",21),s["\u0275\u0275listener"]("scrolled",(function(){return e.onScrollDown()})),s["\u0275\u0275template"](41,G,8,5,"div",22),s["\u0275\u0275template"](42,H,3,0,"div",23),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&t&&(s["\u0275\u0275advance"](14),s["\u0275\u0275textInterpolate1"](" Total Count:",e.countList," "),s["\u0275\u0275advance"](2),s["\u0275\u0275textInterpolate1"](" ",e.countForm.value.maxValue," "),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("formGroup",e.countForm),s["\u0275\u0275advance"](17),s["\u0275\u0275property"]("disabled",e.downloadDisable),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",e.downloadDisable),s["\u0275\u0275advance"](4),s["\u0275\u0275property"]("ngIf",e.is_read),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("infiniteScrollDistance",2)("scrollWindow",!1),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",e.tableLoad&&e.is_read&&e.noData),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",e.isLoading&&e.is_read))},directives:[S.b,S.a,u.J,u.w,u.n,_.c,_.g,u.A,v.b,u.e,u.v,u.l,o.NgIf,M.a,S.f,m.a,P.c,u.F,E.p,o.NgForOf,b.k,b.c,b.e,b.b,b.g,b.j,b.d,b.a,D.a,b.f,b.i,F.c],pipes:[o.UpperCasePipe],styles:[".gd-admin-landing[_ngcontent-%COMP%]   table[_ngcontent-%COMP%]{width:100%}.gd-admin-landing[_ngcontent-%COMP%]   td.mat-cell[_ngcontent-%COMP%], .gd-admin-landing[_ngcontent-%COMP%]   th.mat-header-cell[_ngcontent-%COMP%]{padding:20px}.gd-admin-landing[_ngcontent-%COMP%]   .editIcon[_ngcontent-%COMP%]{cursor:pointer}.gd-admin-landing[_ngcontent-%COMP%]   .infinite-scroll-auto[_ngcontent-%COMP%]{height:470px;overflow:scroll}.gd-admin-landing[_ngcontent-%COMP%]   .configIcon[_ngcontent-%COMP%]{border:none;background-color:initial;margin-top:10px;float:right;color:#66615b}.gd-admin-landing[_ngcontent-%COMP%]   .drawer[_ngcontent-%COMP%]{width:100%;height:150vh}.gd-admin-landing[_ngcontent-%COMP%]   .drawer[_ngcontent-%COMP%]   .card-design[_ngcontent-%COMP%]{width:190px!important;max-height:270px!important;padding:0!important}.gd-admin-landing[_ngcontent-%COMP%]   .drawer[_ngcontent-%COMP%]   input[type=number][_ngcontent-%COMP%]::-webkit-inner-spin-button, .gd-admin-landing[_ngcontent-%COMP%]   .drawer[_ngcontent-%COMP%]   input[type=number][_ngcontent-%COMP%]::-webkit-outer-spin-button{-webkit-appearance:none}.gd-admin-landing[_ngcontent-%COMP%]   .drawer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:disabled{background:hsla(0,0%,40.4%,.7490196078431373)}.gd-admin-landing[_ngcontent-%COMP%]   .drawer[_ngcontent-%COMP%]   .tileName[_ngcontent-%COMP%]{color:#9a9a9a;font-size:15px!important}.gd-admin-landing[_ngcontent-%COMP%]   .drawer[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-prefix, .gd-admin-landing[_ngcontent-%COMP%]   .drawer[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-suffix{margin:auto}.gd-admin-landing[_ngcontent-%COMP%]   .drawer[_ngcontent-%COMP%]   .side-drawer[_ngcontent-%COMP%]{width:40vw!important}.gd-admin-landing[_ngcontent-%COMP%]   .drawer[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{font-weight:400;color:#fff;background-color:#cf0001;height:30px;line-height:3px}.gd-admin-landing[_ngcontent-%COMP%]   .drawer[_ngcontent-%COMP%]   .btn-fab[_ngcontent-%COMP%]{height:35px;width:35px}.gd-admin-landing[_ngcontent-%COMP%]   .drawer[_ngcontent-%COMP%]   .btn-attach[_ngcontent-%COMP%], .gd-admin-landing[_ngcontent-%COMP%]   .drawer[_ngcontent-%COMP%]   .btn-fab[_ngcontent-%COMP%]{line-height:1px;color:#fff;background-color:#cf0001;box-shadow:0 3px 1px -2px rgba(0,0,0,.2),0 2px 2px 0 rgba(0,0,0,.14),0 1px 5px 0 rgba(0,0,0,.12)}.gd-admin-landing[_ngcontent-%COMP%]   .drawer[_ngcontent-%COMP%]   .btn-attach[_ngcontent-%COMP%]{height:36px;width:36px}.gd-admin-landing[_ngcontent-%COMP%]   .drawer[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]{margin-top:0!important;margin-bottom:1px!important;padding:0 5px!important;font-size:13px!important}.gd-admin-landing[_ngcontent-%COMP%]   .drawer[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}.gd-admin-landing[_ngcontent-%COMP%]   .drawer[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}.gd-admin-landing[_ngcontent-%COMP%]   .drawer[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#3c3c3c;font-weight:400;font-size:19px}.gd-admin-landing[_ngcontent-%COMP%]   .card-design[_ngcontent-%COMP%]{width:190px!important;max-height:270px!important;padding:0!important}.gd-admin-landing[_ngcontent-%COMP%]   input[type=number][_ngcontent-%COMP%]::-webkit-inner-spin-button, .gd-admin-landing[_ngcontent-%COMP%]   input[type=number][_ngcontent-%COMP%]::-webkit-outer-spin-button{-webkit-appearance:none}.gd-admin-landing[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:disabled{background:#ddd}.gd-admin-landing[_ngcontent-%COMP%]   .tileName[_ngcontent-%COMP%]{color:#9a9a9a;font-size:15px!important}.gd-admin-landing[_ngcontent-%COMP%]   .mat-drawer-content[_ngcontent-%COMP%]{position:relative;z-index:1;height:max-content!important;display:block;overflow-y:hidden!important}.gd-admin-landing[_ngcontent-%COMP%]   .side-drawer[_ngcontent-%COMP%]{width:39vw!important}.gd-admin-landing[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{font-weight:400;color:#fff;background-color:#cf0001;height:30px;line-height:3px}.gd-admin-landing[_ngcontent-%COMP%]   .btn-fab[_ngcontent-%COMP%]{height:35px;width:35px;border-radius:20px;border:none}.gd-admin-landing[_ngcontent-%COMP%]   .btn-attach[_ngcontent-%COMP%], .gd-admin-landing[_ngcontent-%COMP%]   .btn-fab[_ngcontent-%COMP%]{line-height:1px;color:#fff;background-color:#cf0001;box-shadow:0 3px 1px -2px rgba(0,0,0,.2),0 2px 2px 0 rgba(0,0,0,.14),0 1px 5px 0 rgba(0,0,0,.12)}.gd-admin-landing[_ngcontent-%COMP%]   .btn-attach[_ngcontent-%COMP%]{height:36px;width:36px}.gd-admin-landing[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]{margin-top:0!important;margin-bottom:1px!important;padding:0 5px!important;font-size:13px!important}.gd-admin-landing[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}.gd-admin-landing[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}.gd-admin-landing[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#3c3c3c;font-weight:400;font-size:19px}.gd-admin-landing[_ngcontent-%COMP%]   .closeButton[_ngcontent-%COMP%]{background-color:initial;width:30px;height:16px;font-weight:500;font-size:12px;color:#45546e;margin-top:10px;margin-left:300px}.gd-admin-landing[_ngcontent-%COMP%]   .closeButton[_ngcontent-%COMP%], .gd-admin-landing[_ngcontent-%COMP%]   .saveButton[_ngcontent-%COMP%]{border:none;font-family:Roboto;font-style:normal;line-height:16px;letter-spacing:-.02em;text-transform:capitalize}.gd-admin-landing[_ngcontent-%COMP%]   .saveButton[_ngcontent-%COMP%]{width:78px;height:40px;font-weight:700;font-size:14px;background-color:#ee4961;align-items:center;border-radius:5px;color:#fff;text-align:center}.gd-admin-landing[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]{width:263px;height:24px;font-family:Roboto;font-style:normal;font-weight:700;font-size:16px;line-height:50px;display:flex;align-items:center;text-transform:capitalize;text-align:center;color:#26303e;margin-top:20px}"]}),t})();const X=[{path:"",component:(()=>{class t{constructor(t){this.router=t}ngOnInit(){}}return t.\u0275fac=function(e){return new(e||t)(s["\u0275\u0275directiveInject"](a.g))},t.\u0275cmp=s["\u0275\u0275defineComponent"]({type:t,selectors:[["app-gb-landing-page"]],decls:1,vars:0,template:function(t,e){1&t&&s["\u0275\u0275element"](0,"router-outlet")},directives:[a.l],styles:[""]}),t})(),redirectTo:"home",pathMatch:"full"},{path:"home",children:[{path:"",component:$,data:{breadcrumb:"gdadmin"}},{path:"config",loadChildren:()=>Promise.all([n.e(126),n.e(720)]).then(n.bind(null,"DnxM")).then(t=>t.EditConfigModule),data:{breadcrumb:"config"}},{path:"logs",component:p,data:{breadcrumb:"config"}}]}];let q=(()=>{class t{}return t.\u0275mod=s["\u0275\u0275defineNgModule"]({type:t}),t.\u0275inj=s["\u0275\u0275defineInjector"]({factory:function(e){return new(e||t)},imports:[[a.k.forChild(X)],a.k]}),t})();var Y=n("M9IT"),J=n("Dh3D"),Q=n("xHqg"),Z=n("bSwM"),tt=n("wZkO"),et=n("/1cH");let nt=(()=>{class t{}return t.\u0275mod=s["\u0275\u0275defineNgModule"]({type:t}),t.\u0275inj=s["\u0275\u0275defineInjector"]({factory:function(e){return new(e||t)},imports:[[o.CommonModule,q,u.p,u.E,D.b,x.g,v.c,Y.b,b.m,_.e,J.c,M.b,Q.f,P.d,Z.b,tt.g,F.b,et.c,m.b,S.g]]}),t})()},ye8K:function(t,e,n){"use strict";n.d(e,"a",(function(){return i}));var o=n("fXoL"),a=n("tk/3");let i=(()=>{class t{constructor(t){this._http=t,this.url="/api/gdadmin/",this.getUserSuggestionsFromDB=(t,e)=>{console.log(t),console.log(e);try{return new Promise((n,o)=>{this._http.post(this.url+"getUserSuggestionsFromDB",{searchText:t,vcoe_flag:e}).subscribe(t=>n(t),t=>(console.log(t),o(t)))})}catch(n){return Promise.reject()}}}getTableName(t){return this._http.post(this.url+"getTableDataForAdmin",{dbName:t})}getTableDataForAdmin(t,e,n,o,a){return this._http.post(this.url+"getTableValue",{tableName:t,db_name:e,index:n,currentUser:o,authKey:a})}editFormDetails(t,e,n){return this._http.post(this.url+"editTableDetailsDbAdmin",{tableValue:t,db_name:e,table_name:n})}getAdminUserRoles(t,e){return this._http.post(this.url+"getAdminRoles",{currentUser:t,authKey:e})}insertUserRoles(t,e,n,o){return this._http.post(this.url+"insertUserRoles",{userData:t,roleid:e,currentUser:n,authKey:o})}getDBAndTableList(t,e){return console.log(e),this._http.post(this.url+"getDBAndTableList",{currentUser:t,authKey:e})}getUserRoles(t,e){return this._http.post(this.url+"getUserRoles",{currentUser:t,authKey:e})}updateUserRoles(t,e,n,o){return console.log(o),console.log(e),this._http.post(this.url+"updateUserRoles",{userRole:t,db_name:e,items:n,table_name:o})}checkConfigAccess(){return this._http.post(this.url+"checkConfigAccess",{})}deleteUserRoles(t,e,n){return this._http.post(this.url+"deleteUserRoles",{created_by:t,currentUser:e,authKey:n})}getDbNameConfig(t,e){return this._http.post(this.url+"getDbName",{currentUser:t,authKey:e})}getAuthingKey(t){return this._http.post(this.url+"getAuthKey",{oid:t.oid})}getSelectLogs(t,e){return this._http.post(this.url+"getSelectLogs",{index:t,searchText:e})}getdataForDownload(t,e,n,o,a,i,r){return this._http.post(this.url+"getdatadownload",{startindex:t,endindex:e,tableName:n,currentUser:o,authKey:a,maxValue:i,totalValue:r})}}return t.\u0275fac=function(e){return new(e||t)(o["\u0275\u0275inject"](a.c))},t.\u0275prov=o["\u0275\u0275defineInjectable"]({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()}}]);