(window.webpackJsonp=window.webpackJsonp||[]).push([[932],{Dk4j:function(e,t,n){"use strict";n.r(t),n.d(t,"AddUserComponent",(function(){return z}));var r=n("mrSG"),o=n("3Pt+"),i=n("0IaG"),a=n("XNiG"),s=n("1G5W"),c=n("fXoL"),d=n("rQiX"),l=n("XNFG"),g=n("XQl4"),m=n("ofXK"),p=n("kmnG"),u=n("qFsG"),f=n("g25w"),h=n("TmG/"),C=n("NFeN"),M=n("bSwM"),_=n("Qu3c");function v(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"div",38),c["\u0275\u0275text"](1,"Assign User"),c["\u0275\u0275elementEnd"]())}function O(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"div",38),c["\u0275\u0275text"](1,"Edit User"),c["\u0275\u0275elementEnd"]())}function P(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"div",39),c["\u0275\u0275text"](1,"ASSIGN USER"),c["\u0275\u0275elementEnd"]())}function w(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"div",39),c["\u0275\u0275text"](1,"EDIT USER"),c["\u0275\u0275elementEnd"]())}function x(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"sup",52),c["\u0275\u0275text"](1,"*"),c["\u0275\u0275elementEnd"]())}function y(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div",50),c["\u0275\u0275text"](1),c["\u0275\u0275elementStart"](2,"span"),c["\u0275\u0275template"](3,x,2,0,"sup",51),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"](2).$implicit;c["\u0275\u0275advance"](1),c["\u0275\u0275textInterpolate1"](" ",e.label," "),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("ngIf",e.isMandatory)}}function b(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div"),c["\u0275\u0275elementStart"](1,"mat-form-field",28),c["\u0275\u0275element"](2,"input",53),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"](2).$implicit;c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("placeholder",e.placeholder)("formControlName",e.key)}}function E(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div"),c["\u0275\u0275element"](1,"app-multi-email-chip-list",54),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"](2).$implicit;c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("formControlName",e.key)("placeholder",e.placeholder)}}function L(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div"),c["\u0275\u0275elementStart"](1,"mat-form-field",28),c["\u0275\u0275element"](2,"input",55),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"](2).$implicit;c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("placeholder",e.placeholder)("formControlName",e.key)}}function k(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div"),c["\u0275\u0275element"](1,"app-input-search",56),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"](2).$implicit,t=c["\u0275\u0275nextContext"](3);c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("list",t.statusList)("disableNone",!0)("placeholder",e.placeholder)("formControlName",e.key)("hideMatLabel",!0)}}function F(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"div",57),c["\u0275\u0275elementStart"](1,"mat-form-field",28),c["\u0275\u0275element"](2,"input",58),c["\u0275\u0275elementStart"](3,"mat-icon",59),c["\u0275\u0275listener"]("click",(function(){c["\u0275\u0275restoreView"](e);const t=c["\u0275\u0275nextContext"](3).index;return c["\u0275\u0275nextContext"](2).togglePasswordVisibility(t)})),c["\u0275\u0275text"](4),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()}if(2&e){const e=c["\u0275\u0275nextContext"](2).$implicit,t=c["\u0275\u0275nextContext"]().index,n=c["\u0275\u0275nextContext"](2);c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("type",n.showPassword[t]?"text":"password")("formControlName",e.key),c["\u0275\u0275advance"](2),c["\u0275\u0275textInterpolate1"](" ",n.showPassword[t]?"visibility":"visibility_off"," ")}}function S(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div",60),c["\u0275\u0275elementStart"](1,"mat-checkbox",61),c["\u0275\u0275text"](2),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"](2).$implicit;c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("formControlName",e.key),c["\u0275\u0275advance"](1),c["\u0275\u0275textInterpolate"](e.label)}}const D=function(e,t){return{display:e,"align-items":t}};function V(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div",45),c["\u0275\u0275elementStart"](1,"div",46),c["\u0275\u0275template"](2,y,4,2,"div",47),c["\u0275\u0275template"](3,b,3,2,"div",1),c["\u0275\u0275template"](4,E,2,2,"div",1),c["\u0275\u0275template"](5,L,3,2,"div",1),c["\u0275\u0275template"](6,k,2,5,"div",1),c["\u0275\u0275template"](7,F,5,3,"div",48),c["\u0275\u0275template"](8,S,3,2,"div",49),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]().$implicit,t=c["\u0275\u0275nextContext"](3);c["\u0275\u0275classMapInterpolate1"]("col-",e.col," over-all-field "),c["\u0275\u0275property"]("ngStyle",c["\u0275\u0275pureFunction2"](11,D,"checkbox"===e.fieldType?"flex":"","checkbox"===e.fieldType?"center":"")),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("ngIf",!(e.isDisabled||"status"==e.key&&!t.isEditMode||"checkbox"==e.fieldType)),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","text"===e.fieldType||"email"===e.fieldType||"phonenumber"===e.fieldType),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","secondary_email"===e.fieldType),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","display"===e.fieldType),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","single-select"===e.fieldType&&!("status"==e.key&&!t.isEditMode)),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","password"===e.fieldType),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","checkbox"===e.fieldType)}}function I(e,t){if(1&e&&(c["\u0275\u0275elementContainerStart"](0),c["\u0275\u0275template"](1,V,9,14,"div",44),c["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit,n=c["\u0275\u0275nextContext"](3);c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",!("status"==e.key&&!n.isEditMode))}}function A(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"mat-icon",62),c["\u0275\u0275listener"]("click",(function(){c["\u0275\u0275restoreView"](e);const t=c["\u0275\u0275nextContext"]().index;return c["\u0275\u0275nextContext"](2).deleteDynamicFormArray(t)})),c["\u0275\u0275text"](1," delete "),c["\u0275\u0275elementEnd"]()}}function U(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div",40),c["\u0275\u0275elementStart"](1,"div",41),c["\u0275\u0275template"](2,I,2,1,"ng-container",42),c["\u0275\u0275elementEnd"](),c["\u0275\u0275template"](3,A,2,0,"mat-icon",43),c["\u0275\u0275elementEnd"]()),2&e){const e=t.index,n=c["\u0275\u0275nextContext"](2);let r=null;c["\u0275\u0275property"]("formGroupName",e),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("ngForOf",n.formFields),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",(null==(r=n.UserForm.get("FormArray"))?null:r.controls.length)>1)}}function N(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"div",63),c["\u0275\u0275listener"]("click",(function(){return c["\u0275\u0275restoreView"](e),c["\u0275\u0275nextContext"](2).onClickAddUser()})),c["\u0275\u0275text"](1," + "),c["\u0275\u0275elementStart"](2,"span",64),c["\u0275\u0275text"](3,"Add User"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()}}function j(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"div",65),c["\u0275\u0275listener"]("click",(function(){return c["\u0275\u0275restoreView"](e),c["\u0275\u0275nextContext"](2).onClickSave()})),c["\u0275\u0275text"](1," Save & Invite "),c["\u0275\u0275elementEnd"]()}}function T(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"div",66),c["\u0275\u0275listener"]("click",(function(){return c["\u0275\u0275restoreView"](e),c["\u0275\u0275nextContext"](2).onClickSaveUpdate()})),c["\u0275\u0275text"](1," Save "),c["\u0275\u0275elementEnd"]()}}function G(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"div",2),c["\u0275\u0275elementStart"](1,"div",3),c["\u0275\u0275template"](2,v,2,0,"div",4),c["\u0275\u0275template"](3,O,2,0,"div",4),c["\u0275\u0275elementStart"](4,"div"),c["\u0275\u0275namespaceSVG"](),c["\u0275\u0275elementStart"](5,"svg",5),c["\u0275\u0275element"](6,"path",6),c["\u0275\u0275element"](7,"path",7),c["\u0275\u0275element"](8,"path",8),c["\u0275\u0275element"](9,"path",9),c["\u0275\u0275element"](10,"path",10),c["\u0275\u0275element"](11,"path",11),c["\u0275\u0275element"](12,"path",12),c["\u0275\u0275element"](13,"path",13),c["\u0275\u0275element"](14,"path",14),c["\u0275\u0275element"](15,"path",15),c["\u0275\u0275element"](16,"path",16),c["\u0275\u0275element"](17,"path",17),c["\u0275\u0275element"](18,"path",18),c["\u0275\u0275element"](19,"path",19),c["\u0275\u0275element"](20,"path",20),c["\u0275\u0275element"](21,"path",21),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](22,"svg",22),c["\u0275\u0275listener"]("click",(function(){return c["\u0275\u0275restoreView"](e),c["\u0275\u0275nextContext"]().onClickClose()})),c["\u0275\u0275element"](23,"path",23),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275namespaceHTML"](),c["\u0275\u0275elementStart"](24,"div",24),c["\u0275\u0275elementStart"](25,"div",25),c["\u0275\u0275text"](26,"VENDOR MANAGEMENT USER"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](27,"div",26),c["\u0275\u0275elementStart"](28,"div",27),c["\u0275\u0275text"](29,"Vendor Name"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](30,"div"),c["\u0275\u0275elementStart"](31,"mat-form-field",28),c["\u0275\u0275element"](32,"input",29),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](33,"div",30),c["\u0275\u0275template"](34,P,2,0,"div",31),c["\u0275\u0275template"](35,w,2,0,"div",31),c["\u0275\u0275elementStart"](36,"form",32),c["\u0275\u0275elementStart"](37,"div",33),c["\u0275\u0275template"](38,U,4,3,"div",34),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275template"](39,N,4,0,"div",35),c["\u0275\u0275template"](40,j,2,0,"div",36),c["\u0275\u0275template"](41,T,2,0,"div",37),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()}if(2&e){const e=c["\u0275\u0275nextContext"]();let t=null;c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("ngIf",!e.isEditMode),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",e.isEditMode),c["\u0275\u0275advance"](29),c["\u0275\u0275property"]("value",e.vendorName),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("ngIf",!e.isEditMode),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",e.isEditMode),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("formGroup",e.UserForm),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("ngForOf",null==(t=e.UserForm.get("FormArray"))?null:t.controls),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",!e.isEditMode),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",!e.isEditMode),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",e.isEditMode)}}function Z(e,t){if(1&e&&(c["\u0275\u0275elementContainerStart"](0),c["\u0275\u0275elementStart"](1,"div",67),c["\u0275\u0275elementStart"](2,"div",68),c["\u0275\u0275element"](3,"img",69),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](4,"div",70),c["\u0275\u0275elementStart"](5,"div",71),c["\u0275\u0275text"](6,"Loading..."),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementContainerEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275advance"](3),c["\u0275\u0275property"]("src",e.uiTextConfig["UI-LOADING-001"],c["\u0275\u0275sanitizeUrl"])}}let z=(()=>{class e{constructor(e,t,n,r,o,i,s){this.dialogRef=e,this.fb=t,this._atsMasterService=n,this._toaster=r,this._settingService=o,this._dialog=i,this.data=s,this._onDestroy=new a.b,this.formFields=[],this.roleList=[],this.isLoading=!0,this.uiTextConfig={},this.showPassword=[],this.userId=1,this.assignUserValue=[],this.statusList=[],this.UserForm=this.fb.group({FormArray:this.fb.array([])})}ngOnInit(){return Object(r.c)(this,void 0,void 0,(function*(){this.vendorId=this.data.vendorId,this.vendorName=this.data.vendorName,yield this.getAtsMasterUiConfig("vendorSettingConfig"),null!=this.data.userId?(this.UserForm=this.fb.group({FormArray:this.fb.array([])}),this.isEditMode=!0,yield this.getAtsFormsConfig("vendorUserEditDetailForm"),yield this.fetchVendorUserDetailById(this.data.userId)):(this.UserForm=this.fb.group({FormArray:this.fb.array([])}),this.isEditMode=!1,yield this.getAtsFormsConfig("vendorUserDetailForm"),yield this.onClickAddUser()),yield this.createFormWithValue(),this.isLoading=!1}))}togglePasswordVisibility(e){this.showPassword[e]=!this.showPassword[e]}onClickClose(){this.dialogRef.close(!1)}onClickAddUser(){return Object(r.c)(this,void 0,void 0,(function*(){yield this.generatePassword();const e=this.createFormGroup(this.formFields);this.UserForm.get("FormArray").push(e)}))}deleteDynamicFormArray(e){return Object(r.c)(this,void 0,void 0,(function*(){const t=this.UserForm.get("FormArray");e>=0&&e<t.length&&(t.removeAt(e),this.showPassword.splice(e,1))}))}createFormGroup(e){const t={};return e.forEach(e=>{const n=[];e.isMandatory&&n.push(o.H.required),"userName"===e.key&&n.push(o.H.pattern(/[a-zA-Z ]+/)),"phonenumber"===e.fieldType&&n.push(o.H.pattern(/^\d{10}$/)),"email"===e.fieldType&&n.push(o.H.pattern(/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/)),t[e.key]=this.fb.control("",n)}),this.showPassword.push(!1),t.password.setValue(this.password.raw),t.hashedPassword.setValue(this.password.hashed),t.userRole.setValue("Vendor"),this.fb.group(t)}createFormWithValue(){return Object(r.c)(this,void 0,void 0,(function*(){yield this.generatePassword(),this.assignUserValue.forEach(e=>{const t=this.createFormGroup(this.formFields);Object.keys(e).forEach(n=>{var r,o;t.get(n)&&(null!=this.data.userId?null===(r=t.get(n))||void 0===r||r.setValue(e[n]):"password"!=n&&"hashedPassword"!=n&&(null===(o=t.get(n))||void 0===o||o.setValue(e[n])))}),this.UserForm.get("FormArray").push(t)})}))}onClickSaveUpdate(){return Object(r.c)(this,void 0,void 0,(function*(){if(this.UserForm.valid){const e=this.checkDuplicateEmails(),t=this.checkDuplicatePhoneNumbers();0===e.length&&0===t.length?(this.isLoading=!0,this.assignUserValue=this.UserForm.get("FormArray").value,4==this.assignUserValue[0].status?this.openWarningDialog(this.vendorId):(yield this.updateVendorUserDetails(this.vendorId,this.data.userId,this.assignUserValue),this.dialogRef.close(!0))):[...e,...t].forEach(e=>{this._toaster.showError("Warning",e.message,7e3)})}else{const e=[];this.UserForm.get("FormArray").controls.forEach((t,n)=>{Object.keys(t.controls).forEach(r=>{const o=t.get(r);if(o.errors){const t=this.formFields.find(e=>e.key===r),i=t?t.label:r;o.errors.required?e.push({message:i+" is Mandatory",index:n}):o.errors.pattern&&e.push({message:"Invalid "+i,index:n})}})}),e.reverse(),e.forEach(e=>{this._toaster.showError("Warning",e.message,7e3)})}}))}openWarningDialog(e){return Object(r.c)(this,void 0,void 0,(function*(){const{DeleteDialogComponent:t}=yield n.e(934).then(n.bind(null,"IicH"));this._dialog.open(t,{width:"450px",disableClose:!0,data:{id:e,title:"Selecting Inactive status will remove the user from the list. Are you sure you want to Inactive the user?",subTitle:null,content:"Select 'Yes' if you are sure you want to inactivate the user.",yesBtnText:"Yes, Inactivate",isIconVisible:!1,isDelete:!1,vendorId:this.vendorId,svg:""}}).afterClosed().subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){e&&(yield this.updateVendorUserDetails(this.vendorId,this.data.userId,this.assignUserValue),this.dialogRef.close(!0))})))}))}onClickSave(){return Object(r.c)(this,void 0,void 0,(function*(){if(this.UserForm.valid){let e=window.location.host;const t=this.checkDuplicateEmails(),n=this.checkDuplicatePhoneNumbers();0===t.length&&0===n.length?(this.isLoading=!0,this.assignUserValue=this.UserForm.get("FormArray").value,yield this.assignUserToVendor(this.vendorId,this.assignUserValue,e),this.dialogRef.close(!0)):[...t,...n].forEach(e=>{this._toaster.showError("Warning",e.message,7e3)})}else{const e=[];this.UserForm.get("FormArray").controls.forEach((t,n)=>{Object.keys(t.controls).forEach(r=>{const o=t.get(r);if(o.errors){const t=this.formFields.find(e=>e.key===r),i=t?t.label:r;o.errors.required?e.push({message:i+" is Mandatory",index:n}):o.errors.pattern&&e.push({message:"Invalid "+i,index:n})}})}),e.reverse(),e.forEach(e=>{this._toaster.showError("Warning",e.message,7e3)})}}))}checkDuplicateEmails(){const e=this.UserForm.get("FormArray"),t=[],n=new Set;return e.controls.forEach((e,r)=>{var o;const i=e.get("email"),a=(null===(o=e.get("secondaryEmail"))||void 0===o?void 0:o.value)||[];if(i&&i.value){const e=i.value.trim().toLowerCase();n.has(e)?t.push({message:`Email '${e}' is duplicated`,index:r}):n.add(e),a.forEach(e=>{const o=e.trim().toLowerCase();n.has(o)?t.push({message:`Email '${o}' is duplicated`,index:r}):n.add(o)})}}),t}checkDuplicatePhoneNumbers(){const e=this.UserForm.get("FormArray"),t=[],n=new Set;return e.controls.forEach((e,r)=>{const o=e.get("phonenumber");if(o&&o.value){const e=o.value;n.has(e)?t.push({message:`Phone number '${e}' is duplicated`,index:r}):n.add(e)}}),t}assignUserToVendor(e,t,n){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((r,o)=>this._settingService.assignUserToVendor(e,t,n).pipe(Object(s.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?this._toaster.showSuccess("Success",e.msg,3e3):this._toaster.showError("Error",e.msg,3e3),r(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Update Vendor User Detail Failed!",3e3),o()}}))}))}updateVendorUserDetails(e,t,n){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((r,o)=>this._settingService.updateVendorUserDetails(e,t,n).pipe(Object(s.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?this._toaster.showSuccess("Success",e.msg,3e3):this._toaster.showError("Error",e.msg,3e3),r(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Update Vendor User Detail Failed!",3e3),o()}}))}))}fetchVendorUserDetailById(e){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._settingService.fetchVendorUserDetailById(e).pipe(Object(s.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?(this.assignUserValue.push(e.data.userDetail),this.statusList=e.data.statusList):this._toaster.showError("Error",e.msg,3e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Fetch User Detail Failed!",3e3),n()}}))}))}generatePassword(){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._settingService.generatePassword().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.password=t:this._toaster.showError("Error",t.msg,3e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Generate Password Failed!",3e3),t()}}))}))}getAtsFormsConfig(e){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._atsMasterService.getAtsFormsConfig(e).pipe(Object(s.a)(this._onDestroy)).subscribe({next:n=>{0==n.err?"vendorUserDetailForm"!=e&&"vendorUserEditDetailForm"!=e||(this.formFields=n.data.form[0].formFields[0].formArrayFields):this._toaster.showError("Error",n.msg,7e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Forms Configuration Retrieval Failed!",7e3),n()}}))}))}getAtsMasterUiConfig(e){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._atsMasterService.getAtsMasterUiConfig(e).pipe(Object(s.a)(this._onDestroy)).subscribe({next:n=>{0==n.err?"vendorSettingConfig"==e&&(this.uiTextConfig=n.data.assignUserVendorConfigurations.uiTextConfig):this._toaster.showError("Error",n.msg,7e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Master Data Retrieval Failed!",7e3),n()}}))}))}}return e.\u0275fac=function(t){return new(t||e)(c["\u0275\u0275directiveInject"](i.h),c["\u0275\u0275directiveInject"](o.i),c["\u0275\u0275directiveInject"](d.a),c["\u0275\u0275directiveInject"](l.a),c["\u0275\u0275directiveInject"](g.a),c["\u0275\u0275directiveInject"](i.b),c["\u0275\u0275directiveInject"](i.a))},e.\u0275cmp=c["\u0275\u0275defineComponent"]({type:e,selectors:[["app-add-user"]],decls:2,vars:2,consts:[["class","add-user-container",4,"ngIf"],[4,"ngIf"],[1,"add-user-container"],[1,"header"],["class","assign-user-header",4,"ngIf"],["width","273","height","60","viewBox","0 0 273 72","fill","none",1,"kebs-svg"],["d","M133.604 -22.5168V4.76469C133.604 8.35397 132.088 11.811 129.391 14.4067C125.911 17.7302 120.858 19.1903 115.948 18.2782L115.947 18.2781L93.9788 14.2877L103.741 5.17581L116.468 7.51274L116.469 7.51278C119.365 8.04124 122.089 5.97651 122.089 3.19398V-11.8208L133.604 -22.5168Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M179.022 14.2879L157.082 18.2781L157.081 18.2782C152.177 19.1903 147.13 17.73 143.654 14.4063L143.652 14.4043C140.912 11.856 139.397 8.40031 139.397 4.81089V-22.5164L150.898 -11.8208V2.96299C150.898 5.88334 153.767 8.04193 156.761 7.46638C156.761 7.46632 156.761 7.46626 156.762 7.46621L169.224 5.17578L179.022 14.2879Z","fill","#EAEDF3","stroke","#DADCE2","stroke-width","0.421296"],["d","M133.604 37.6432V64.9405L122.089 54.2563V38.8891C122.089 36.2932 119.564 34.3709 116.818 34.8515L116.817 34.8517L103.741 37.2324L93.9791 28.1309L115.947 24.1449L115.948 24.1448C120.858 23.2337 125.911 24.6924 129.392 28.0123C132.088 30.6051 133.604 34.0581 133.604 37.6432Z","fill","#EAEDF3","stroke","#DADCE2","stroke-width","0.421296"],["d","M139.397 64.9402V37.6586C139.397 34.0695 140.913 30.6125 143.61 28.0168C147.09 24.6932 152.143 23.233 157.053 24.1451L157.054 24.1452L179.021 28.1354L169.261 37.2015L155.936 34.7721C155.936 34.7721 155.936 34.772 155.936 34.772C153.338 34.2895 150.913 36.1237 150.913 38.6288V54.2441L139.397 64.9402Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M40.0758 14.7878V42.0694C40.0758 45.6587 38.5596 49.1157 35.8631 51.7114C32.3828 55.0348 27.3294 56.495 22.4198 55.5829L22.419 55.5828L0.45043 51.5924L10.2125 42.4805L22.9402 44.8174L22.9404 44.8175C25.8368 45.3459 28.5604 43.2812 28.5604 40.4987V25.4839L40.0758 14.7878Z","fill","#F3F5FA","stroke","#DADCE2","stroke-width","0.421296"],["d","M85.4934 51.5924L63.5532 55.5825L63.5524 55.5827C58.6488 56.4948 53.6015 55.0345 50.1255 51.7107L50.1234 51.7087C47.3832 49.1604 45.8689 45.7048 45.8689 42.1153V14.788L57.3696 25.4836V40.2674C57.3696 43.1878 60.2384 45.3464 63.2324 44.7708C63.2327 44.7708 63.233 44.7707 63.2333 44.7707L75.6953 42.4802L85.4934 51.5924Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M40.0758 74.9476V102.245L28.5604 91.5607V76.1936C28.5604 73.5976 26.0352 71.6754 23.29 72.1559L23.2886 72.1562L10.2125 74.5368L0.450787 65.4353L22.419 61.4494L22.4198 61.4492C27.3296 60.5382 32.3831 61.9968 35.8634 65.3167C38.5597 67.9095 40.0758 71.3626 40.0758 74.9476Z","fill","#F7F8FA","stroke","#DADCE2","stroke-width","0.421296"],["d","M45.8689 102.245V74.9631C45.8689 71.3739 47.385 67.9169 50.0813 65.3213C53.5617 61.9977 58.6152 60.5375 63.5248 61.4495L63.5256 61.4497L85.493 65.4399L75.7324 74.5059L62.4081 72.0765C62.408 72.0765 62.4078 72.0765 62.4077 72.0764C59.8092 71.5939 57.3843 73.4281 57.3843 75.9332V91.5485L45.8689 102.245Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M40.0758 14.4832V41.7647C40.0758 45.354 38.5596 48.811 35.8631 51.4067C32.3828 54.7302 27.3294 56.1903 22.4198 55.2782L22.419 55.2781L0.45043 51.2877L10.2125 42.1758L22.9402 44.5127L22.9404 44.5128C25.8368 45.0412 28.5604 42.9765 28.5604 40.194V25.1792L40.0758 14.4832Z","fill","#EAEDF3","fill-opacity","0.5","stroke","#DADCE2","stroke-width","0.421296"],["d","M85.4934 51.2877L63.5532 55.2779L63.5524 55.278C58.6488 56.1901 53.6015 54.7298 50.1255 51.406L50.1234 51.4041C47.3832 48.8557 45.8689 45.4001 45.8689 41.8106V14.4833L57.3696 25.1789V39.9627C57.3696 42.8831 60.2384 45.0417 63.2324 44.4661C63.2327 44.4661 63.233 44.466 63.2333 44.466L75.6953 42.1755L85.4934 51.2877Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M40.0758 74.6429V101.94L28.5604 91.256V75.8889C28.5604 73.2929 26.0352 71.3707 23.29 71.8512L23.2886 71.8515L10.2125 74.2322L0.450787 65.1306L22.419 61.1447L22.4198 61.1445C27.3296 60.2335 32.3831 61.6921 35.8634 65.012C38.5597 67.6048 40.0758 71.0579 40.0758 74.6429Z","fill","#EAEDF3","stroke","#DADCE2","stroke-width","0.421296"],["d","M45.8689 101.94V74.6584C45.8689 71.0692 47.385 67.6122 50.0813 65.0166C53.5617 61.693 58.6152 60.2328 63.5248 61.1448L63.5256 61.145L85.493 65.1352L75.7324 74.2012L62.4081 71.7718C62.408 71.7718 62.4078 71.7718 62.4077 71.7718C59.8092 71.2893 57.3843 73.1234 57.3843 75.6285V91.2438L45.8689 101.94Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M227.131 14.7876V42.0691C227.131 45.6584 225.615 49.1155 222.919 51.7112C219.438 55.0346 214.385 56.4947 209.476 55.5827L209.475 55.5825L187.506 51.5921L197.268 42.4803L209.996 44.8172L209.996 44.8172C212.893 45.3457 215.616 43.281 215.616 40.4984V25.4837L227.131 14.7876Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M272.549 51.5921L250.609 55.5823L250.608 55.5824C245.704 56.4945 240.657 55.0342 237.181 51.7105L237.179 51.7085C234.439 49.1602 232.925 45.7045 232.925 42.1151V14.7878L244.425 25.4834V40.2672C244.425 43.1875 247.294 45.3461 250.288 44.7706C250.288 44.7705 250.289 44.7705 250.289 44.7704L262.751 42.48L272.549 51.5921Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M227.131 74.9474V102.245L215.616 91.5605V76.1933C215.616 73.5974 213.091 71.6751 210.346 72.1557L210.344 72.1559L197.268 74.5366L187.506 65.4351L209.475 61.4491L209.475 61.449C214.385 60.5379 219.439 61.9966 222.919 65.3165C225.615 67.9093 227.131 71.3623 227.131 74.9474Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M232.925 102.244V74.9628C232.925 71.3737 234.441 67.9167 237.137 65.321C240.617 61.9974 245.671 60.5372 250.58 61.4493L250.581 61.4494L272.549 65.4396L262.788 74.5057L249.464 72.0763C249.464 72.0763 249.464 72.0762 249.463 72.0762C246.865 71.5937 244.44 73.4279 244.44 75.933V91.5483L232.925 102.244Z","fill","#EAEDF3","fill-opacity","0.5","stroke","#DADCE2","stroke-width","0.421296"],["width","10","height","10","viewBox","0 0 10 10","fill","none",1,"close-svg",3,"click"],["d","M1.26465 9.83653L0.196289 8.7731L3.98689 5L0.196289 1.2519L1.26465 0.188477L5.05525 3.96158L8.82073 0.188477L9.88909 1.2519L6.09849 5L9.88909 8.7731L8.82073 9.83653L5.05525 6.06343L1.26465 9.83653Z","fill","#1C1B1F"],[1,"vendor-name-container"],[1,"entity"],[1,"col-4","vendor-name"],[1,"vendor-name-label"],["appearance","outline","color","warn",1,"mat-form-field"],["matInput","","readonly","",3,"value"],[1,"add-user-inner-container"],["class","assign-user",4,"ngIf"],[3,"formGroup"],["formArrayName","FormArray"],["class","user-assign-form-container",3,"formGroupName",4,"ngFor","ngForOf"],["class","add-user",3,"click",4,"ngIf"],["class","save--invite-button",3,"click",4,"ngIf"],["class","save-button",3,"click",4,"ngIf"],[1,"assign-user-header"],[1,"assign-user"],[1,"user-assign-form-container",3,"formGroupName"],[1,"row"],[4,"ngFor","ngForOf"],["class","delete-form-array-icon","matTooltip","Delete",3,"click",4,"ngIf"],[3,"class","ngStyle",4,"ngIf"],[3,"ngStyle"],[1,"d-flex","flex-column","label-value-box"],["class","form-label",4,"ngIf"],["class","password-input-wrapper",4,"ngIf"],["class","checkbox",4,"ngIf"],[1,"form-label"],["class","required-field",4,"ngIf"],[1,"required-field"],["matInput","",3,"placeholder","formControlName"],[3,"formControlName","placeholder"],["matInput","","readonly","",3,"placeholder","formControlName"],[1,"assignUserDropDown",3,"list","disableNone","placeholder","formControlName","hideMatLabel"],[1,"password-input-wrapper"],["matInput","","readonly","",3,"type","formControlName"],[1,"visibility",3,"click"],[1,"checkbox"],[3,"formControlName"],["matTooltip","Delete",1,"delete-form-array-icon",3,"click"],[1,"add-user",3,"click"],[1,"word-add-user"],[1,"save--invite-button",3,"click"],[1,"save-button",3,"click"],[1,"loading-img"],[2,"max-width","60px","max-height","60px"],[2,"max-width","100%","max-height","100%",3,"src"],[1,"loading-wrapper"],[1,"loading"]],template:function(e,t){1&e&&(c["\u0275\u0275template"](0,G,42,10,"div",0),c["\u0275\u0275template"](1,Z,7,1,"ng-container",1)),2&e&&(c["\u0275\u0275property"]("ngIf",!t.isLoading),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",t.isLoading))},directives:[m.NgIf,p.c,u.b,o.J,o.w,o.n,o.h,m.NgForOf,o.o,m.NgStyle,o.e,o.v,o.l,f.a,h.a,C.a,M.a,_.a],styles:['.add-user-container[_ngcontent-%COMP%]{position:relative;min-height:100%}.add-user-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{padding-inline:20px;display:flex;justify-content:space-between;align-items:center;background-color:#f4f4f6}.add-user-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .assign-user-header[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:20px;font-weight:700;line-height:24px;letter-spacing:-.02em;text-align:left;color:#111434}.add-user-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .close-svg[_ngcontent-%COMP%]{cursor:pointer}.add-user-container[_ngcontent-%COMP%]   .vendor-name-container[_ngcontent-%COMP%]{padding:20px 20px 0 5px}.add-user-container[_ngcontent-%COMP%]   .vendor-name-container[_ngcontent-%COMP%]   .entity[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:16px;font-weight:700;line-height:20.83px;color:var(--atsprimaryColor);padding:0 15px 5px}.add-user-container[_ngcontent-%COMP%]   .vendor-name-container[_ngcontent-%COMP%]   .vendor-name-label[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:11px;font-weight:400;line-height:16px;letter-spacing:.02em;color:#5f6c81}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]{padding:20px}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .assign-user[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:16px;font-weight:700;line-height:20.83px;text-align:left;color:var(--atsprimaryColor)}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .user-assign-form-container[_ngcontent-%COMP%]{display:flex;align-items:center}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .user-assign-form-container[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]{width:92%}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .user-assign-form-container[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .over-all-field[_ngcontent-%COMP%]{padding:10px 10px 10px 0}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .user-assign-form-container[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .over-all-field[_ngcontent-%COMP%]   .label-value-box[_ngcontent-%COMP%]{gap:3px}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .user-assign-form-container[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .over-all-field[_ngcontent-%COMP%]   .label-input-value[_ngcontent-%COMP%]{gap:4px!important}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .user-assign-form-container[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .over-all-field[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:11px;font-weight:400;line-height:16px;letter-spacing:.02em;color:#5f6c81}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .user-assign-form-container[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .over-all-field[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%]   .required-field[_ngcontent-%COMP%]{color:red;font-size:15px;top:0}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .user-assign-form-container[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .over-all-field[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]{width:100%;font-family:var(--atsfontFamily);font-size:12px;font-weight:400;line-height:16px;letter-spacing:.02em;color:#45546e}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .user-assign-form-container[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .over-all-field[_ngcontent-%COMP%]   .password-input-wrapper[_ngcontent-%COMP%]{position:relative;padding:0}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .user-assign-form-container[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .over-all-field[_ngcontent-%COMP%]   .password-input-wrapper[_ngcontent-%COMP%]     .mat-form-field.mat-warn .mat-input-element{width:-moz-fit-content;width:fit-content}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .user-assign-form-container[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .over-all-field[_ngcontent-%COMP%]   .password-input-wrapper[_ngcontent-%COMP%]   .mat-icon.visibility[_ngcontent-%COMP%]{cursor:pointer;position:absolute;right:10px;top:50%;transform:translateY(-50%);color:rgba(0,0,0,.54)}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .user-assign-form-container[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .over-all-field[_ngcontent-%COMP%]   .password-input-wrapper[_ngcontent-%COMP%]   .mat-icon.visibility[_ngcontent-%COMP%]:hover{color:rgba(0,0,0,.87)}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .user-assign-form-container[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .over-all-field[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-flex, .add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .user-assign-form-container[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .over-all-field[_ngcontent-%COMP%]     .mat-form-field-type-mat-select:not(.mat-form-field-disabled) .mat-form-field-flex{height:40px!important;display:flex!important;align-items:center!important}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .user-assign-form-container[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .over-all-field[_ngcontent-%COMP%]     .mat-select-value-text{width:100%!important;font-family:var(--atsfontFamily)!important;font-size:12px!important;font-weight:400!important;line-height:16px!important;letter-spacing:.02em!important;color:#45546e!important}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .user-assign-form-container[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .over-all-field[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-outline{height:40px!important}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .user-assign-form-container[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .over-all-field[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-wrapper{margin:0!important}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .user-assign-form-container[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .over-all-field[_ngcontent-%COMP%]   .checkbox[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:12px;font-weight:400;line-height:16px;letter-spacing:.02em;color:#45546e;margin-top:15px}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .user-assign-form-container[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .over-all-field[_ngcontent-%COMP%]   .checkbox[_ngcontent-%COMP%]     .mat-checkbox-inner-container{height:16px!important;width:16px!important}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .user-assign-form-container[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .over-all-field[_ngcontent-%COMP%]   .checkbox[_ngcontent-%COMP%]     .mat-checkbox-checked.mat-accent .mat-checkbox-background{background-color:var(--atssecondaryColor)!important}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .user-assign-form-container[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .over-all-field[_ngcontent-%COMP%]   .checkbox[_ngcontent-%COMP%]     .mat-checkbox-inner-container{width:14px!important;height:14px!important}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .user-assign-form-container[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .over-all-field[_ngcontent-%COMP%]   .checkbox[_ngcontent-%COMP%]     .mat-checkbox-frame{border-color:#b9c0ca!important}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .user-assign-form-container[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .over-all-field[_ngcontent-%COMP%]   .phone-number[_ngcontent-%COMP%]{display:flex}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .user-assign-form-container[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .over-all-field[_ngcontent-%COMP%]   .phone-number[_ngcontent-%COMP%]   .country-code[_ngcontent-%COMP%]{padding:0;width:25%}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .user-assign-form-container[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .over-all-field[_ngcontent-%COMP%]   .phone-number[_ngcontent-%COMP%]   .country-code[_ngcontent-%COMP%]     .mat-form-field-wrapper{width:100%!important}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .user-assign-form-container[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .over-all-field[_ngcontent-%COMP%]   .phone-number[_ngcontent-%COMP%]   .country-code[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-infix{height:44px!important}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .user-assign-form-container[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .over-all-field[_ngcontent-%COMP%]   .phone-number[_ngcontent-%COMP%]   .country-code[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-wrapper{padding-bottom:6px!important}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .user-assign-form-container[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .over-all-field[_ngcontent-%COMP%]   .phone-number[_ngcontent-%COMP%]   .country-code[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-outline-end{border-radius:0!important}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .user-assign-form-container[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .over-all-field[_ngcontent-%COMP%]   .phone-number[_ngcontent-%COMP%]   .number[_ngcontent-%COMP%]{padding:0;width:75%}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .user-assign-form-container[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .over-all-field[_ngcontent-%COMP%]   .phone-number[_ngcontent-%COMP%]   .number[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-outline-start{border-radius:0!important}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .user-assign-form-container[_ngcontent-%COMP%]   .delete-form-array-icon[_ngcontent-%COMP%]{margin:95px 20px 20px;cursor:pointer}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .save--invite-button[_ngcontent-%COMP%]{width:-moz-fit-content;width:fit-content;right:15px;bottom:10px}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .save--invite-button[_ngcontent-%COMP%], .add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);color:#fff;background:linear-gradient(270deg,var(--atsprimaryColor),var(--atssecondaryColor) 105.29%);border-radius:5px;padding:10px;display:flex;align-items:center;position:absolute;justify-content:center;cursor:pointer}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%]{width:-moz-fit-content;width:fit-content;right:20px;bottom:15px;width:65px}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .add-user[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:12px;font-weight:700;line-height:16px;letter-spacing:-.02em;text-align:left;color:#111434;gap:5px;display:flex;cursor:pointer;width:-moz-fit-content;width:fit-content}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .word-add-user[_ngcontent-%COMP%]{text-decoration:underline}.loading-img[_ngcontent-%COMP%]{height:100%;flex-direction:column;background-color:#fff}.loading-img[_ngcontent-%COMP%], .loading-img[_ngcontent-%COMP%]   .loading-wrapper[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center}.loading-img[_ngcontent-%COMP%]   .loading-wrapper[_ngcontent-%COMP%]{vertical-align:center}.loading-img[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]{color:rgba(0,0,0,.3);font-size:16px}.loading-img[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]:before{content:"Loading...";position:absolute;overflow:hidden;max-width:7em;white-space:nowrap;background:linear-gradient(270deg,var(--atsprimaryColor),var(--atssecondaryColor) 105.29%);background-clip:text;-webkit-background-clip:text;-webkit-text-fill-color:transparent;animation:loading 6s linear infinite}@keyframes loading{0%{max-width:0}}']}),e})()}}]);