(window.webpackJsonp=window.webpackJsonp||[]).push([[880],{VSH1:function(e,n,l){"use strict";l.r(n),l.d(n,"SettingsModule",(function(){return u}));var a=l("ofXK"),o=l("3Pt+"),t=l("tyNb"),d=l("fXoL");const r=[{path:"",pathMatch:"full",redirectTo:"template"},{path:"template",loadChildren:()=>Promise.all([l.e(4),l.e(19),l.e(26),l.e(33),l.e(64),l.e(1015)]).then(l.bind(null,"KVz4")).then(e=>e.TemplatesModule),data:{breadcrumb:"Templates"}},{path:"organization",loadChildren:()=>Promise.all([l.e(19),l.e(93),l.e(233)]).then(l.bind(null,"g5Tb")).then(e=>e.CompanyModule),data:{breadcrumb:"Organization"}},{path:"activity-logs",loadChildren:()=>Promise.all([l.e(68),l.e(206)]).then(l.bind(null,"IqCk")).then(e=>e.ActivityLogsModule),data:{breadcrumb:"Activity Logs"}},{path:"profile",loadChildren:()=>Promise.all([l.e(6),l.e(19),l.e(93),l.e(852)]).then(l.bind(null,"j9Fs")).then(e=>e.ProfileModule),data:{breadcrumb:"Profile"}},{path:"vendor",loadChildren:()=>Promise.all([l.e(39),l.e(43),l.e(93),l.e(1019)]).then(l.bind(null,"on56")).then(e=>e.VendorModule),data:{breadcrumb:"Vendor Management"}},{path:"user",loadChildren:()=>Promise.all([l.e(33),l.e(39),l.e(43),l.e(1018)]).then(l.bind(null,"T1tr")).then(e=>e.UserModule),data:{breadcrumb:"User"}},{path:"role",loadChildren:()=>Promise.all([l.e(39),l.e(43),l.e(869)]).then(l.bind(null,"TWZM")).then(e=>e.RolesModule),data:{breadcrumb:"Roles & Permissions"}},{path:"college",loadChildren:()=>Promise.all([l.e(19),l.e(33),l.e(39),l.e(43),l.e(0),l.e(232)]).then(l.bind(null,"o9QW")).then(e=>e.CollegeModule),data:{breadcrumb:"Colleges"}},{path:"onboarding-tasks",loadChildren:()=>Promise.all([l.e(19),l.e(39),l.e(43),l.e(0),l.e(830)]).then(l.bind(null,"7kmN")).then(e=>e.OnboardingTasksModule),data:{breadcrumb:"Onboarding Setting"}},{path:"onboarding-checklists",loadChildren:()=>Promise.all([l.e(19),l.e(39),l.e(43),l.e(0),l.e(829)]).then(l.bind(null,"blun")).then(e=>e.OnboardingChecklistsModule),data:{breadcrumb:"Onboarding Setting"}},{path:"course-degree",loadChildren:()=>Promise.all([l.e(33),l.e(39),l.e(132),l.e(332)]).then(l.bind(null,"Wo+Q")).then(e=>e.courseDegreeModule),data:{breadcrumb:"Degree & Course"}},{path:"location",loadChildren:()=>Promise.all([l.e(33),l.e(39),l.e(132),l.e(793)]).then(l.bind(null,"8Fxm")).then(e=>e.LocationModule),data:{breadcrumb:"Location"}}];let i=(()=>{class e{}return e.\u0275mod=d["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=d["\u0275\u0275defineInjector"]({factory:function(n){return new(n||e)},imports:[[t.k.forChild(r)],t.k]}),e})(),u=(()=>{class e{}return e.\u0275mod=d["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=d["\u0275\u0275defineInjector"]({factory:function(n){return new(n||e)},imports:[[a.CommonModule,i,o.E]]}),e})()}}]);