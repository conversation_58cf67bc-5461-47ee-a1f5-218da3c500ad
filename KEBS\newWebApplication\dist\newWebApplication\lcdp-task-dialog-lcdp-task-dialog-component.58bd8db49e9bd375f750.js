(window.webpackJsonp=window.webpackJsonp||[]).push([[785],{"5jfm":function(t,e,n){"use strict";n.r(e),n.d(e,"LcdpTaskDialogComponent",(function(){return d}));var o=n("fXoL"),i=n("0IaG"),a=(n("ofXK"),n("kmnG"),n("qFsG"),n("bTqV")),l=n("NFeN"),c=n("Qu3c"),s=n("z52X");const r=["taskContentContainer"];let d=(()=>{class t{constructor(t,e,n,o){this.dialogRef=t,this.inData=e,this.compiler=n,this._lcdpService=o,this.closeTaskModal=()=>{this.dialogRef.close({event:"Close"})}}ngOnInit(){if(this.inData&&this.inData.modalParams){let t=this.inData.modalParams;this._lcdpService.lcdpDetails.lcdpApplicationId=t.lcdpApplicationId,this._lcdpService.lcdpDetails.applicationId=t.appId,this._lcdpService.lcdpDetails.recordId=t.itemData._id,this.loadLcdpTasksContainer()}}loadLcdpTasksContainer(){Promise.all([n.e(129),n.e(0)]).then(n.bind(null,"onu9")).then(t=>{const e=this.compiler.compileModuleSync(t.LcdpTasksModule).create(this.taskContentContainer.injector).componentFactoryResolver.resolveComponentFactory(t.LcdpTasksComponent);this.taskContentContainer.createComponent(e)})}ngOnDestroy(){this._lcdpService.resetLcdpDetails()}}return t.\u0275fac=function(e){return new(e||t)(o["\u0275\u0275directiveInject"](i.h),o["\u0275\u0275directiveInject"](i.a),o["\u0275\u0275directiveInject"](o.Compiler),o["\u0275\u0275directiveInject"](s.a))},t.\u0275cmp=o["\u0275\u0275defineComponent"]({type:t,selectors:[["app-lcdp-task-dialog"]],viewQuery:function(t,e){if(1&t&&o["\u0275\u0275viewQuery"](r,!0,o.ViewContainerRef),2&t){let t;o["\u0275\u0275queryRefresh"](t=o["\u0275\u0275loadQuery"]())&&(e.taskContentContainer=t.first)}},decls:14,vars:0,consts:[[1,"container-fluid","lcdp-task-dialog-styles"],[1,"row","pb-2",2,"border-bottom","solid 1px #cacaca"],[1,"col-11","pt-1","pb-2","d-flex","pl-2"],["mat-icon-button","",1,"bubble","mt-1"],[1,"iconButton"],[1,"headingBold","my-auto","ml-3","pt-1"],[1,"col-1","d-flex"],["matTooltip","Close","mat-icon-button","",1,"ml-auto","close-button","mt-1",3,"click"],[1,"close-Icon"],["taskContentContainer",""]],template:function(t,e){1&t&&(o["\u0275\u0275elementStart"](0,"div",0),o["\u0275\u0275elementStart"](1,"div",1),o["\u0275\u0275elementStart"](2,"div",2),o["\u0275\u0275elementStart"](3,"div",3),o["\u0275\u0275elementStart"](4,"mat-icon",4),o["\u0275\u0275text"](5,"fact_check"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](6,"span",5),o["\u0275\u0275text"](7," Tasks "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](8,"div",6),o["\u0275\u0275elementStart"](9,"button",7),o["\u0275\u0275listener"]("click",(function(){return e.closeTaskModal()})),o["\u0275\u0275elementStart"](10,"mat-icon",8),o["\u0275\u0275text"](11,"close"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementContainer"](12,null,9),o["\u0275\u0275elementEnd"]())},directives:[l.a,a.a,c.a],styles:[".lcdp-task-dialog-styles[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]{display:flex;line-height:26px;border-radius:50%;cursor:context-menu!important;width:26px;height:26px;background-color:#fbfbfb;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.lcdp-task-dialog-styles[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#cf0001;font-size:18px;margin-top:3px!important;margin-left:4px!important}.lcdp-task-dialog-styles[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.lcdp-task-dialog-styles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}.lcdp-task-dialog-styles[_ngcontent-%COMP%]   .headingBold[_ngcontent-%COMP%]{color:#cf0001;font-weight:500;font-size:14px}"]}),t})()}}]);