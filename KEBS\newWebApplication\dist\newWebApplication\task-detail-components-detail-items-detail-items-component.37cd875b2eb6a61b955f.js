(window.webpackJsonp=window.webpackJsonp||[]).push([[1014],{"30bQ":function(e,t,n){"use strict";n.r(t),n.d(t,"DetailItemsComponent",(function(){return y})),n.d(t,"DetailItemsModule",(function(){return T}));var o=n("fXoL"),i=n("XNiG"),s=n("ofXK"),l=n("bTqV"),r=n("NFeN"),a=n("Qu3c"),c=n("jaxi"),d=n("STbY"),m=n("MutI"),u=n("0IaG"),p=n("BVzC"),g=n("0qc8");const C=["requestContentContainer"],h=["scrollFrame"],v=function(e){return{"btn-toggle-selected":e}};function f(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"mat-button-toggle",13),o["\u0275\u0275text"](1),o["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=o["\u0275\u0275nextContext"]();o["\u0275\u0275property"]("disabled",n.isComponentLoading)("value",e.label)("ngClass",o["\u0275\u0275pureFunction1"](4,v,n.selectedToggle==e.label)),o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" ",e.label,"")}}function b(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"button",14),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](e);const t=o["\u0275\u0275nextContext"]();return t.createItem(t.selectedToggle,!1)})),o["\u0275\u0275elementStart"](1,"mat-icon",15),o["\u0275\u0275text"](2,"add_circle_outline"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275property"]("matTooltip","To do"==e.selectedToggle?"Create To do":"CTA"==e.selectedToggle?"Create CTA":"Task"==e.selectedToggle?"Create Task":"")}}let y=(()=>{class e{constructor(e,t,n){this.compiler=e,this._ErrorService=t,this._detailTaskService=n,this._onDestroy=new i.b,this.resourceRequestTabs=[{label:"Overview",key_name:"overview"},{label:"Attachments",key_name:"attachments"},{label:"Activities",key_name:"activity_id"},{label:"CTA",key_name:"cta_id"}],this.activityTypesList=[],this.taskTemplateList=[],this.isRequestEditable=!0,this.isComponentLoading=!1}ngOnInit(){this.selectedToggle=this.resourceRequestTabs[0].label,this.loadRequestTabContent()}ngAfterViewInit(){this.scrollContainer=this.scrollFrame.nativeElement}scrollToBottom(){this.scrollContainer.scroll({top:this.scrollContainer.scrollHeight,left:0,behavior:"smooth"})}selectToggle(e){this.selectedToggle=e.value,this.loadRequestTabContent()}applyActivityTypesConfig(e){this._detailTaskService.setActivityObservable({type:"Activity View",data:e}),setTimeout(()=>{this.scrollToBottom()},500);for(let t of this.activityTypesList)t._id==e.value&&(t.is_activity_visible=e.selected)}loadRequestTabContent(){this.isComponentLoading=!0,this.requestContentContainerRef&&this.requestContentContainerRef.clear(),"Overview"==this.selectedToggle?this.loadRequestOverviewContainer():"Task"==this.selectedToggle?this.loadRequestTaskContainer():"Attachments"==this.selectedToggle?this.loadAttachmentsContainer():"Activities"==this.selectedToggle?this.loadActivityContainer():"CTA"==this.selectedToggle&&this.loadCtaContainer()}loadRequestOverviewContainer(){Promise.all([n.e(1),n.e(4),n.e(29),n.e(34),n.e(0),n.e(427)]).then(n.bind(null,"Rbfp")).then(e=>{const t=this.compiler.compileModuleSync(e.DetailOverviewModule).create(this.requestContentContainerRef.injector).componentFactoryResolver.resolveComponentFactory(e.DetailOverviewComponent),n=this.requestContentContainerRef.createComponent(t);this.isComponentLoading=!1,n.instance.requestItem=this.requestItem})}loadRequestTaskContainer(){Promise.all([n.e(1),n.e(4),n.e(29),n.e(34),n.e(0),n.e(428)]).then(n.bind(null,"p9+R")).then(e=>{const t=this.compiler.compileModuleSync(e.DetailTaskModule).create(this.requestContentContainerRef.injector).componentFactoryResolver.resolveComponentFactory(e.DetailTaskComponent),n=this.requestContentContainerRef.createComponent(t);this.isComponentLoading=!1,n.instance.requestItem=this.requestItem})}loadAttachmentsContainer(){n.e(424).then(n.bind(null,"b8J6")).then(e=>{const t=this.compiler.compileModuleSync(e.DetailAttachmentsModule).create(this.requestContentContainerRef.injector).componentFactoryResolver.resolveComponentFactory(e.DetailAttachmentComponent),n=this.requestContentContainerRef.createComponent(t);this.isComponentLoading=!1,n.instance.requestItem=this.requestItem})}loadActivityContainer(){Promise.all([n.e(1),n.e(0),n.e(423)]).then(n.bind(null,"OFNZ")).then(e=>{const t=this.compiler.compileModuleSync(e.DetailActivitiesModule).create(this.requestContentContainerRef.injector).componentFactoryResolver.resolveComponentFactory(e.DetailActivitiesComponent),n=this.requestContentContainerRef.createComponent(t);this.isComponentLoading=!1,n.instance.requestItem=this.requestItem})}loadCtaContainer(){Promise.all([n.e(0),n.e(426)]).then(n.bind(null,"qVeU")).then(e=>{const t=this.compiler.compileModuleSync(e.DetailCtaModule).create(this.requestContentContainerRef.injector).componentFactoryResolver.resolveComponentFactory(e.DetailCtaComponent),n=this.requestContentContainerRef.createComponent(t);this.isComponentLoading=!1,n.instance.requestItem=this.requestItem})}createItem(e,t){this._detailTaskService.setActivityObservable(e?{type:"CTA"==e?"Create CTA":"Task"==e?"Create Task":"",data:null}:{type:"Create task from template",data:t})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](o.Compiler),o["\u0275\u0275directiveInject"](p.a),o["\u0275\u0275directiveInject"](g.a))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-detail-items"]],viewQuery:function(e,t){if(1&e&&(o["\u0275\u0275viewQuery"](C,!0,o.ViewContainerRef),o["\u0275\u0275viewQuery"](h,!0)),2&e){let e;o["\u0275\u0275queryRefresh"](e=o["\u0275\u0275loadQuery"]())&&(t.requestContentContainerRef=e.first),o["\u0275\u0275queryRefresh"](e=o["\u0275\u0275loadQuery"]())&&(t.scrollFrame=e.first)}},inputs:{requestItem:"requestItem"},decls:14,vars:4,consts:[[1,"container-fluid","request-detail-item-styles"],[1,"row","pt-3"],[1,"col-12","pl-0","pr-0"],[1,"row"],[1,"col-10","pt-2","pb-2","pl-0","pr-0"],[3,"disabled","value","change"],["class","toggle-btn",3,"disabled","value","ngClass",4,"ngFor","ngForOf"],[1,"col-2",2,"text-align","right"],["mat-icon-button","","class","icon-button-inactive",3,"matTooltip","click",4,"ngIf"],[1,"row","pt-2"],[1,"col-12","pl-0","pr-0","overflow"],["scrollFrame",""],["requestContentContainer",""],[1,"toggle-btn",3,"disabled","value","ngClass"],["mat-icon-button","",1,"icon-button-inactive",3,"matTooltip","click"],[1,"iconButton"]],template:function(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"div",0),o["\u0275\u0275elementStart"](1,"div",1),o["\u0275\u0275elementStart"](2,"div",2),o["\u0275\u0275elementStart"](3,"div",3),o["\u0275\u0275elementStart"](4,"div",4),o["\u0275\u0275elementStart"](5,"mat-button-toggle-group",5),o["\u0275\u0275listener"]("change",(function(e){return t.selectToggle(e)})),o["\u0275\u0275template"](6,f,2,6,"mat-button-toggle",6),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](7,"div",7),o["\u0275\u0275template"](8,b,3,1,"button",8),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](9,"div",9),o["\u0275\u0275elementStart"](10,"div",10,11),o["\u0275\u0275elementContainer"](12,null,12),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e&&(o["\u0275\u0275advance"](5),o["\u0275\u0275property"]("disabled",t.isComponentLoading)("value",t.selectedToggle),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngForOf",t.resourceRequestTabs),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngIf",t.isRequestEditable&&("To do"==t.selectedToggle||"CTA"==t.selectedToggle||"TR Number"==t.selectedToggle||"Task"==t.selectedToggle)))},directives:[c.b,s.NgForOf,s.NgIf,c.a,s.NgClass,l.a,a.a,r.a],styles:[".request-detail-item-styles[_ngcontent-%COMP%]   .toggle-btn[_ngcontent-%COMP%]{min-width:8rem;min-height:2rem}.request-detail-item-styles[_ngcontent-%COMP%]   .btn-toggle-selected[_ngcontent-%COMP%]{font-size:12px!important;background-color:#c92020!important;color:#fff}.request-detail-item-styles[_ngcontent-%COMP%]     .mat-button-toggle-label-content{font-size:12px!important}.request-detail-item-styles[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{color:#5f5f5f;font-size:11px!important}.request-detail-item-styles[_ngcontent-%COMP%]   .overflow[_ngcontent-%COMP%]{height:28rem;overflow:scroll;overflow-x:hidden}.request-detail-item-styles[_ngcontent-%COMP%]   .normalFont[_ngcontent-%COMP%]{color:#181818;font-size:14px;font-weight:600;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.request-detail-item-styles[_ngcontent-%COMP%]   .sla-breached-bg[_ngcontent-%COMP%]{background-color:#fff2f2}.request-detail-item-styles[_ngcontent-%COMP%]   .sla-breached-color[_ngcontent-%COMP%]{color:#c92020}.request-detail-item-styles[_ngcontent-%COMP%]   .desc[_ngcontent-%COMP%]{max-height:8rem;overflow:scroll;overflow-x:hidden;overflow-y:auto}.request-detail-item-styles[_ngcontent-%COMP%]   .icon-button-inactive[_ngcontent-%COMP%]{background-color:#f1f1f1;line-height:8px;width:26px;height:26px;margin-right:15px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.request-detail-item-styles[_ngcontent-%COMP%]   .icon-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.request-check[_ngcontent-%COMP%]     .mat-pseudo-checkbox{display:none!important}.request-check[_ngcontent-%COMP%]     .mat-list-text{font-size:12px!important}.request-check[_ngcontent-%COMP%]     .check .mat-list-base{padding-top:0!important}.request-icon-btn[_ngcontent-%COMP%]{font-size:20px;padding-left:20px;color:#139940}.overflow-ctrl[_ngcontent-%COMP%]{width:90%;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}"]}),e})(),T=(()=>{class e{}return e.\u0275mod=o["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=o["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[s.CommonModule,r.b,l.b,a.b,c.c,d.e,m.d,u.g]]}),e})()}}]);