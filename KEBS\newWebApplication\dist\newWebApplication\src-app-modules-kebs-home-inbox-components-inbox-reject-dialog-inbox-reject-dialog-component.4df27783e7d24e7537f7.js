(window.webpackJsonp=window.webpackJsonp||[]).push([[957],{"0IPq":function(n,t,e){"use strict";e.r(t),e.d(t,"InboxRejectDialogComponent",(function(){return d}));var o=e("0IaG"),i=e("3Pt+"),c=e("fXoL"),a=e("XNFG"),r=e("NFeN"),l=e("ofXK");function s(n,t){if(1&n){const n=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"div",14),c["\u0275\u0275listener"]("click",(function(){c["\u0275\u0275restoreView"](n);const e=t.$implicit;return c["\u0275\u0275nextContext"](2).patchValue(e)})),c["\u0275\u0275text"](1),c["\u0275\u0275elementEnd"]()}if(2&n){const n=t.$implicit;c["\u0275\u0275advance"](1),c["\u0275\u0275textInterpolate1"](" ",n," ")}}function g(n,t){if(1&n&&(c["\u0275\u0275elementStart"](0,"div",12),c["\u0275\u0275template"](1,s,2,1,"div",13),c["\u0275\u0275elementEnd"]()),2&n){const n=c["\u0275\u0275nextContext"]();c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngForOf",null==n.data?null:n.data.data)}}let d=(()=>{class n{constructor(n,t,e){this._dialogRef=n,this._toaster=t,this.data=e,this.comments=new i.j(null,[i.H.required])}ngOnInit(){}onClose(){this._dialogRef.close(!1)}patchValue(n){this.comments.setValue(n)}sendBtn(){null!=this.comments.value&&""!=this.comments.value&&""!=this.comments.value.trim()?this._dialogRef.close(this.comments.value):this._toaster.showWarning("Warning \u26a0\ufe0f","Comments is Mandatory!",7e3)}}return n.\u0275fac=function(t){return new(t||n)(c["\u0275\u0275directiveInject"](o.h),c["\u0275\u0275directiveInject"](a.a),c["\u0275\u0275directiveInject"](o.a))},n.\u0275cmp=c["\u0275\u0275defineComponent"]({type:n,selectors:[["app-inbox-reject-dialog"]],decls:17,vars:2,consts:[[1,"bg-container"],[1,"d-flex","align-items-center","justify-content-between"],[1,"title","p-0"],[1,"icon"],[1,"icon",3,"click"],[1,"d-flex","flex-column"],[1,"form-label"],["placeholder","Enter Rejection Comments here",3,"formControl"],["class","suggestions",4,"ngIf"],[1,"d-flex","justify-content-end"],[1,"cancel-btn",3,"click"],[1,"add-btn",3,"click"],[1,"suggestions"],["class","suggestion",3,"click",4,"ngFor","ngForOf"],[1,"suggestion",3,"click"]],template:function(n,t){1&n&&(c["\u0275\u0275elementStart"](0,"div",0),c["\u0275\u0275elementStart"](1,"div",1),c["\u0275\u0275elementStart"](2,"div",2),c["\u0275\u0275text"](3,"Reason For Rejection"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](4,"div",3),c["\u0275\u0275elementStart"](5,"mat-icon",4),c["\u0275\u0275listener"]("click",(function(){return t.onClose()})),c["\u0275\u0275text"](6,"close "),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](7,"div",5),c["\u0275\u0275elementStart"](8,"span",6),c["\u0275\u0275text"](9,"Comments"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275element"](10,"textarea",7),c["\u0275\u0275elementEnd"](),c["\u0275\u0275template"](11,g,2,1,"div",8),c["\u0275\u0275elementStart"](12,"div",9),c["\u0275\u0275elementStart"](13,"div",10),c["\u0275\u0275listener"]("click",(function(){return t.onClose()})),c["\u0275\u0275text"](14,"Cancel"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](15,"div",11),c["\u0275\u0275listener"]("click",(function(){return t.sendBtn()})),c["\u0275\u0275text"](16,"Reject"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&n&&(c["\u0275\u0275advance"](10),c["\u0275\u0275property"]("formControl",t.comments),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",(null==t.data?null:t.data.data)&&(null==t.data?null:t.data.data.length)>0))},directives:[r.a,i.e,i.v,i.k,l.NgIf,l.NgForOf],styles:[".bg-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:16px;padding:20px}.bg-container[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-size:18px;font-weight:700;color:#111434}.bg-container[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{width:18px;height:18px;font-size:18px;color:#1b2140;cursor:pointer}.bg-container[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%]{color:#5f6c81;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.bg-container[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%], .bg-container[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-size:12px;font-weight:400;width:100%}.bg-container[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{resize:none;height:92px;border:1px solid #d2d2d2;outline:none;border-radius:8px;color:#111434;padding:8px}.bg-container[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]::placeholder{font-family:var(--kebsFontFamily);font-size:12px;font-weight:400;color:#b9c0ca}.bg-container[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]::-webkit-scrollbar{width:3px!important}.bg-container[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{min-height:20px!important}.bg-container[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{color:#45546e;padding:9px 15px;border:1px solid #45546e;border-radius:8px;margin-right:8px}.bg-container[_ngcontent-%COMP%]   .add-btn[_ngcontent-%COMP%], .bg-container[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-size:14px;font-weight:700;cursor:pointer}.bg-container[_ngcontent-%COMP%]   .add-btn[_ngcontent-%COMP%]{color:#fff;padding:10px 16px;border-radius:8px;background:var(--kebsPrimaryColor)}.bg-container[_ngcontent-%COMP%]   .suggestions[_ngcontent-%COMP%]{display:flex;align-items:center;flex-wrap:wrap;gap:8px}.bg-container[_ngcontent-%COMP%]   .suggestions[_ngcontent-%COMP%]   .suggestion[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-size:12px;font-weight:500;color:#111434;border:1px solid #111434;border-radius:60px;padding:4px 8px;width:-moz-fit-content;width:fit-content}.bg-container[_ngcontent-%COMP%]   .suggestions[_ngcontent-%COMP%]   .suggestion[_ngcontent-%COMP%]:hover{cursor:pointer;background-color:#f2f3f6}"]}),n})()}}]);