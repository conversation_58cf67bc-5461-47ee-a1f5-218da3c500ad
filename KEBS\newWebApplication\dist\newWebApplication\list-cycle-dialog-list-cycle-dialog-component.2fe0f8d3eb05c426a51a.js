(window.webpackJsonp=window.webpackJsonp||[]).push([[790],{Dxrg:function(e,t,a){"use strict";a.r(t),a.d(t,"ListCycleDialogComponent",(function(){return Oe}));var n=a("0IaG"),l=a("fXoL"),o=a("7xhW"),i=a("LcQX"),r=a("mS9j"),s=a("bTqV"),p=a("NFeN"),c=a("ofXK"),m=a("mrSG"),u=a("1G5W"),d=a("XNiG"),v=a("1S+Y"),_=a("BVzC"),y=a("XXEo"),f=a("3Pt+"),g=a("860k"),x=a("y9TG");function E(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"app-type-star",6),l["\u0275\u0275listener"]("starsResponse",(function(t){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"]().getStarRating(t)})),l["\u0275\u0275elementEnd"]()}if(2&e){const e=l["\u0275\u0275nextContext"]();l["\u0275\u0275property"]("metricName",null==e.metriceData?null:e.metriceData.appraisal_metric_name)("readOnly","submitted"!=(null==e.employeeEvaluationMetricesData?null:e.employeeEvaluationMetricesData.employee_appraisal_metrices_evaluation_status.toLowerCase())&&"open"!=(null==e.employeeEvaluationMetricesData?null:e.employeeEvaluationMetricesData.employee_appraisal_metrices_evaluation_status.toLowerCase())&&"approved"!=(null==e.employeeEvaluationMetricesData?null:e.employeeEvaluationMetricesData.employee_appraisal_metrices_evaluation_status.toLowerCase()))("totalScore","OR"==(null==e.employeeEvaluationMetricesData?null:e.employeeEvaluationMetricesData.employee_appraisal_metrices_evaluation_operation)?null==e.employeeEvaluationMetricesData?null:e.employeeEvaluationMetricesData.employee_appraisal_metrices_scored_obtained:e.currentEvaluatorScore)("totalStars",null==e.metriceData?null:e.metriceData.appraisal_metric_max_score)}}function h(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",7),l["\u0275\u0275elementStart"](1,"div",8),l["\u0275\u0275elementStart"](2,"div",1),l["\u0275\u0275elementStart"](3,"div",8),l["\u0275\u0275elementStart"](4,"span",9),l["\u0275\u0275text"](5),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](6,"div",7),l["\u0275\u0275elementStart"](7,"div",8),l["\u0275\u0275elementStart"](8,"app-type-comment",10),l["\u0275\u0275listener"]("commentResponse",(function(t){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"]().getComment(t)})),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&e){const e=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](5),l["\u0275\u0275textInterpolate1"](' "',null==e.metriceData?null:e.metriceData.appraisal_metric_name,'" '),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("readOnly","submitted"!=(null==e.employeeEvaluationMetricesData?null:e.employeeEvaluationMetricesData.employee_appraisal_metrices_evaluation_status.toLowerCase())&&"open"!=(null==e.employeeEvaluationMetricesData?null:e.employeeEvaluationMetricesData.employee_appraisal_metrices_evaluation_status.toLowerCase()))("label","Your Feedback")("comment","OR"==(null==e.employeeEvaluationMetricesData?null:e.employeeEvaluationMetricesData.employee_appraisal_metrices_evaluation_operation)?null==e.employeeEvaluationMetricesData?null:e.employeeEvaluationMetricesData.approver_comment:e.currentEvaluatorComment)}}let M=(()=>{class e{constructor(e,t){this.fb=e,this._AppraisalEvaluatorsService=t,this.evaluatorResponse=new l.EventEmitter,this.currentEvaluatorScore=0,this.evaluatedValue=new f.j("",f.H.required)}getTotalScore(){return Object(m.c)(this,void 0,void 0,(function*(){this.employeeEvaluationMetricesData.employee_appraisal_metrices_evaluators_details.forEach(e=>Object(m.c)(this,void 0,void 0,(function*(){"manager"==e.employee_appraisal_metrices_evaluator_type&&e.employee_appraisal_metrices_evaluators.forEach(e=>Object(m.c)(this,void 0,void 0,(function*(){e.employee_appraisal_metrices_evaluator_oid==(yield this._AppraisalEvaluatorsService.getCurrentUserOID())&&(this.currentEvaluatorScore=e.employee_appraisal_metrices_evaluator_score_awarded,this.currentEvaluatorComment=e.employee_appraisal_metrices_evaluator_comment)})))})))}))}ngOnInit(){this.getTotalScore(),this.commentsFormGroup=this.fb.group({comment:[""],reasons:this.fb.array([])});let e=this.commentsFormGroup.controls.reasons;if(this.appraisalCycleData)for(let t of this.appraisalCycleData.appraisal_cycles){t.detailed_mode_activated=t.appraisal_cycle_active;for(let a of t.appraisal_metrices)e.push(this.fb.group({comment:[{value:a.metric_score,disabled:!t.appraisal_cycle_editable}]}))}}getStatusColor(e){return"Open"==e||"Pending"==e?"#808080":"Submitted"==e?"#FF7200":"Approved"==e?"#009432":"Rejected"==e?"#cf0001":"#808080"}detailClicked(e){this.appraisalCycleData.appraisal_cycles[e].detailed_mode_activated=!this.appraisalCycleData.appraisal_cycles[e].detailed_mode_activated}saveResponse(){console.log(this.commentsFormGroup)}getStarRating(e){return Object(m.c)(this,void 0,void 0,(function*(){console.log(e);let t={employee_evaluation_metrices_id:this.employeeEvaluationMetricesData._id,evaluator_oid:yield this._AppraisalEvaluatorsService.getCurrentUserOID(),evaluator_score_awarded:parseInt(e),approver_action_date:new Date,approver_action_is_active:!0,evaluator_comment:"",employe_oid:this.employeeEvaluationMetricesData.employee_oid,appraisal_metrices_name:this.metriceData.appraisal_metric_name};this.evaluatorResponse.emit(t)}))}getComment(e){return Object(m.c)(this,void 0,void 0,(function*(){console.log(e,"Comment From Comment Box");let t={employee_evaluation_metrices_id:this.employeeEvaluationMetricesData._id,evaluator_oid:yield this._AppraisalEvaluatorsService.getCurrentUserOID(),evaluator_score_awarded:0,approver_action_date:new Date,evaluator_comment:e,approver_action_is_active:!0,employe_oid:this.employeeEvaluationMetricesData.employee_oid,appraisal_metrices_name:this.metriceData.appraisal_metric_name};this.evaluatorResponse.emit(t)}))}}return e.\u0275fac=function(t){return new(t||e)(l["\u0275\u0275directiveInject"](f.i),l["\u0275\u0275directiveInject"](v.a))},e.\u0275cmp=l["\u0275\u0275defineComponent"]({type:e,selectors:[["app-evaluation-type-cyclic-rating-and-feedback"]],inputs:{appraisalCycleData:"appraisalCycleData",metriceData:"metriceData",employeeEvaluationMetricesData:"employeeEvaluationMetricesData"},outputs:{evaluatorResponse:"evaluatorResponse"},decls:6,vars:2,consts:[[1,"container-fluid","cycle-styles"],[1,"row"],[1,"col-4"],[3,"metricName","readOnly","totalScore","totalStars","starsResponse",4,"ngIf"],[1,"col-8",2,"top","-11rem"],["class","row pt-2",4,"ngIf"],[3,"metricName","readOnly","totalScore","totalStars","starsResponse"],[1,"row","pt-2"],[1,"col-12"],[1,"default-font","highlighted-font"],[3,"readOnly","label","comment","commentResponse"]],template:function(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"div",0),l["\u0275\u0275elementStart"](1,"div",1),l["\u0275\u0275elementStart"](2,"div",2),l["\u0275\u0275template"](3,E,1,4,"app-type-star",3),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](4,"div",4),l["\u0275\u0275template"](5,h,9,4,"div",5),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&e&&(l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("ngIf","rating"==(null==t.metriceData?null:t.metriceData.appraisal_metric_response_type)),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf","feedback"==(null==t.metriceData?null:t.metriceData.appraisal_metric_response_type)))},directives:[c.NgIf,g.a,x.a],styles:[".cycle-styles[_ngcontent-%COMP%]   .status-circular[_ngcontent-%COMP%]{height:13px;width:13px;z-index:1;border-radius:50%;background-color:#c7c4c4;box-shadow:0 2px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.cycle-styles[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.cycle-styles[_ngcontent-%COMP%]   .default-font[_ngcontent-%COMP%]{font-size:14px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.cycle-styles[_ngcontent-%COMP%]   .highlighted-font[_ngcontent-%COMP%]{font-weight:600}.cycle-styles[_ngcontent-%COMP%]   .line-height[_ngcontent-%COMP%]{height:17px;padding-left:5px}.cycle-styles[_ngcontent-%COMP%]   .bold-font[_ngcontent-%COMP%]{font-size:14px;color:#cf0001;font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.cycle-styles[_ngcontent-%COMP%]   .bg-color[_ngcontent-%COMP%]{background-color:hsla(0,0%,92.5%,.30196078431372547)!important;box-shadow:0 3px 6px hsla(0,0%,41.2%,.19),0 1px 4px hsla(0,0%,41.2%,.19)}.cycle-styles[_ngcontent-%COMP%]   .text-area-class[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:100%;font-size:13px}.cycle-styles[_ngcontent-%COMP%]   .mini-tick[_ngcontent-%COMP%]{height:42px!important;width:42px!important;line-height:42px;background-color:#cf0001!important;color:#fff!important;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}"]}),e})();var D=a("7EHt"),C=a("Qu3c"),S=a("me71"),b=a("lVl8"),w=a("1d+P"),I=a("kmnG"),O=a("qFsG");function P(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",3),l["\u0275\u0275elementStart"](1,"span",24),l["\u0275\u0275element"](2,"i",25),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](3,"span",26),l["\u0275\u0275listener"]("click",(function(){l["\u0275\u0275restoreView"](e);const t=l["\u0275\u0275nextContext"]().$implicit;return l["\u0275\u0275nextContext"](2).getFileFromS3(t.attachment_json)})),l["\u0275\u0275text"](4),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&e){const e=l["\u0275\u0275nextContext"]().$implicit,t=l["\u0275\u0275nextContext"](2);l["\u0275\u0275advance"](4),l["\u0275\u0275textInterpolate"](t.getFileName(e.attachment_json))}}function R(e,t){if(1&e&&l["\u0275\u0275element"](0,"span",27),2&e){const e=l["\u0275\u0275nextContext"]().$implicit;l["\u0275\u0275property"]("innerHtml",null==e?null:e.comment,l["\u0275\u0275sanitizeHtml"])}}function F(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div"),l["\u0275\u0275elementStart"](1,"div",15),l["\u0275\u0275elementStart"](2,"div",16),l["\u0275\u0275template"](3,P,5,1,"div",17),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](4,"div",18),l["\u0275\u0275elementStart"](5,"div",19),l["\u0275\u0275element"](6,"app-user-image",20),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](7,"div",21),l["\u0275\u0275template"](8,R,1,1,"ng-template",null,22,l["\u0275\u0275templateRefExtractor"]),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](10,"div",23),l["\u0275\u0275elementStart"](11,"small",24),l["\u0275\u0275text"](12),l["\u0275\u0275pipe"](13,"date"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,a=l["\u0275\u0275reference"](9),n=l["\u0275\u0275nextContext"](2);l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("ngIf",e.attachment_json),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("id",null==n.employeeEvaluationMetricesData?null:n.employeeEvaluationMetricesData.employee_oid),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("innerHtml",null==e?null:e.comment,l["\u0275\u0275sanitizeHtml"])("tooltip",a),l["\u0275\u0275advance"](5),l["\u0275\u0275textInterpolate"](l["\u0275\u0275pipeBind2"](13,5,null==e?null:e.createdAt,"dd-MM-yy hh:mm a"))}}function k(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div",12),l["\u0275\u0275elementStart"](1,"div",13),l["\u0275\u0275template"](2,F,14,8,"div",14),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngForOf",e.employeeEvaluationMetricesData.employee_evaluation_metric_response_data)}}function j(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"mat-form-field",28),l["\u0275\u0275element"](1,"input",29),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](1),l["\u0275\u0275propertyInterpolate1"]("placeholder","Out of ",null==e.metriceData?null:e.metriceData.appraisal_metric_max_score,""),l["\u0275\u0275property"]("formControl",e.evaluatedValue)}}function A(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"span",30),l["\u0275\u0275text"](1),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](1),l["\u0275\u0275textInterpolate"](e.getCalculatedScore(null==e.employeeEvaluationMetricesData?null:e.employeeEvaluationMetricesData.employee_appraisal_metrices_scored_obtained,null==e.metriceData?null:e.metriceData.appraisal_metric_max_score))}}function V(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",4),l["\u0275\u0275elementStart"](1,"div",31),l["\u0275\u0275elementStart"](2,"app-type-comment",32),l["\u0275\u0275listener"]("commentResponse",(function(t){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"]().getComment(t)})),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](3,"div",33),l["\u0275\u0275elementStart"](4,"button",34),l["\u0275\u0275listener"]("click",(function(){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"]().saveResponse()})),l["\u0275\u0275elementStart"](5,"mat-icon"),l["\u0275\u0275text"](6,"done_all"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}2&e&&(l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("label","Your comment"))}const N=function(e){return{color:e}};function L(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div",40),l["\u0275\u0275element"](1,"app-user-profile",41),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](2,"div",42),l["\u0275\u0275element"](3,"app-user-profile",41),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](4,"div",42),l["\u0275\u0275elementStart"](5,"mat-icon",43),l["\u0275\u0275text"](6,"fiber_manual_record"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275text"](7),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"](2);l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("type","name")("oid",null==e.employeeEvaluationMetricesData?null:e.employeeEvaluationMetricesData.approved_by),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("type","role")("oid",null==e.employeeEvaluationMetricesData?null:e.employeeEvaluationMetricesData.approved_by),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngStyle",l["\u0275\u0275pureFunction1"](6,N,e.getStatusColor(null==e.employeeEvaluationMetricesData?null:e.employeeEvaluationMetricesData.employee_appraisal_metrices_evaluation_status))),l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate1"]("",null==e.employeeEvaluationMetricesData?null:e.employeeEvaluationMetricesData.employee_appraisal_metrices_evaluation_status," ")}}function z(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"span",38),l["\u0275\u0275text"](1),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"](2);l["\u0275\u0275advance"](1),l["\u0275\u0275textInterpolate"](null==e.employeeEvaluationMetricesData?null:e.employeeEvaluationMetricesData.approver_comment)}}function H(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div",35),l["\u0275\u0275elementStart"](1,"div",3),l["\u0275\u0275element"](2,"app-user-image",36),l["\u0275\u0275template"](3,L,8,8,"ng-template",null,37,l["\u0275\u0275templateRefExtractor"]),l["\u0275\u0275elementStart"](5,"span",38),l["\u0275\u0275text"](6),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](7,z,2,1,"span",39),l["\u0275\u0275elementStart"](8,"small",24),l["\u0275\u0275text"](9),l["\u0275\u0275pipe"](10,"date"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275reference"](4),t=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("id",null==t.employeeEvaluationMetricesData?null:t.employeeEvaluationMetricesData.approved_by)("tooltip",e)("borderColor",t.getStatusColor(null==t.employeeEvaluationMetricesData?null:t.employeeEvaluationMetricesData.employee_appraisal_metrices_evaluation_status)),l["\u0275\u0275advance"](4),l["\u0275\u0275textInterpolate"](null==t.employeeEvaluationMetricesData?null:t.employeeEvaluationMetricesData.employee_appraisal_metrices_evaluation_status),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",null==t.employeeEvaluationMetricesData?null:t.employeeEvaluationMetricesData.approver_comment),l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate"](l["\u0275\u0275pipeBind2"](10,6,null==t.employeeEvaluationMetricesData?null:t.employeeEvaluationMetricesData.approver_action_date,"dd-MM-yy hh:mm a"))}}let T=(()=>{class e{constructor(e,t,a){this._util=e,this._evalService=t,this._fileSaver=a,this.evaluatorResponse=new l.EventEmitter,this.evaluatedValue=new f.j("",f.H.required),this.evaluatorComment=new f.j,this.isEmployeeResponse=!1,this.appraisersList=[],this.currDate=new Date}ngOnInit(){console.log(this.metriceData,this.employeeEvaluationMetricesData,"Input to et"),this.employeeEvaluationMetricesData.employee_evaluation_metric_response_data.length>0&&(this.isEmployeeResponse=!0),this.metriceData&&this.metriceData.appraisal_metric_max_score&&this.evaluatedValue.setValidators([f.H.max(this.metriceData.appraisal_metric_max_score),f.H.required])}getEvaluatorScoreAwarded(e,t){return parseFloat(e)*(100/parseFloat(t))}getCalculatedScore(e,t){return 0==e?0:parseFloat(e)/(100/parseFloat(t))}saveResponse(){return Object(m.c)(this,void 0,void 0,(function*(){if("VALID"==this.evaluatedValue.status){let e={employee_evaluation_metrices_id:this.employeeEvaluationMetricesData._id,evaluator_oid:yield this._evalService.getCurrentUserOID(),evaluator_score_awarded:this.getEvaluatorScoreAwarded(this.evaluatedValue.value,this.metriceData.appraisal_metric_max_score),evaluator_comment:this.evaluatorComment.value,approver_action_is_active:!0,approver_action_date:new Date,employe_oid:this.employeeEvaluationMetricesData.employee_oid,appraisal_metrices_name:this.metriceData.appraisal_metric_name};this.evaluatorResponse.emit(e),this.employeeEvaluationMetricesData.employee_appraisal_metrices_evaluation_status="Approved",this.employeeEvaluationMetricesData.employee_appraisal_metrices_scored_obtained=e.evaluator_score_awarded,this.employeeEvaluationMetricesData.approver_comment=this.evaluatorComment.value}else this._util.showMessage("Kindly enter/check Evaluated value","Dismiss")}))}getComment(e){this.evaluatorComment.patchValue(e)}getFormattedComment(e){if(console.log("Watch me"),e)return e.replace(/<\/?[^>]+(>|$)/g,"")}getFileName(e){return(e="string"==typeof e?JSON.parse(e):e).files_json.fileName}getFileFromS3(e){e="string"==typeof e?JSON.parse(e):e,this._evalService.getFileFromS3(e.files_json.key).then(t=>{window.open("").document.write("<iframe width='100%' height='100%' src='data:application/pdf;base64, "+t.data.fileData+"'></iframe>"),this._fileSaver.saveAsFile(t.data.fileData,e.files_json.fileName,e.files_json.type)})}getStatusColor(e){return this._evalService.getStatusColor(e)}}return e.\u0275fac=function(t){return new(t||e)(l["\u0275\u0275directiveInject"](i.a),l["\u0275\u0275directiveInject"](v.a),l["\u0275\u0275directiveInject"](w.a))},e.\u0275cmp=l["\u0275\u0275defineComponent"]({type:e,selectors:[["app-evaluation-type-scoring"]],inputs:{metriceData:"metriceData",employeeEvaluationMetricesData:"employeeEvaluationMetricesData"},outputs:{evaluatorResponse:"evaluatorResponse"},decls:17,vars:6,consts:[[1,"container-fluid","scoring-styles"],["class","pb-2",4,"ngIf"],[1,"row","pt-2","pl-2","d-flex"],[1,"col-12"],[1,"row"],[1,"col-3","pl-2","default-font","my-auto",2,"font-weight","600"],[1,"col-2","pl-2","default-font","my-auto"],[1,"col-3","pl-2","default-font"],["class","form-field-class","appearance","outline",4,"ngIf","ngIfElse"],["otherStatus",""],["class","row",4,"ngIf"],["class","row pt-2",4,"ngIf"],[1,"pb-2"],[1,"card"],[4,"ngFor","ngForOf"],[1,"card-body","card-bg-color"],[1,"row","pt-2","pb-2","d-flex"],["class","col-12",4,"ngIf"],[1,"row","pt-2","pb-2"],[1,"col-1"],["imgWidth","28px","imgHeight","28px",3,"id"],["content-type","template","placement","top",1,"col-5","pl-0","pt-2","ellipsis","default-font",2,"left","-29px",3,"innerHtml","tooltip"],["fulltext",""],[1,"col-6","pt-2"],[1,"pl-2"],["aria-hidden","true",1,"fa","fa-file-pdf-o","fa-2x",2,"color","red"],[1,"pl-4","default-font","my-auto",2,"cursor","pointer",3,"click"],[3,"innerHtml"],["appearance","outline",1,"form-field-class"],["matInput","","type","number",3,"placeholder","formControl"],[1,"d-flex","my-auto"],[1,"col-11","pl-0","pr-0"],[3,"label","commentResponse"],[1,"col-1","d-flex"],["matTooltip","Save","mat-mini-fab","",1,"ml-auto","my-auto","mini-tick",3,"click"],[1,"row","pt-2"],["imgWidth","33px","imgHeight","33px","placement","right","content-type","template","max-width","300","borderStyle","solid","borderWidth","2px",3,"id","tooltip","borderColor"],["appraiserTooltip",""],[1,"pl-2","default-font","my-auto"],["class","pl-2 default-font my-auto",4,"ngIf"],[1,"row","tooltip-text",2,"text-align","center"],[3,"type","oid"],[1,"row","tooltip-text"],[1,"tooltip-status-indicator","p-0","m-0",3,"ngStyle"]],template:function(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div",0),l["\u0275\u0275template"](1,k,3,1,"div",1),l["\u0275\u0275elementStart"](2,"div",2),l["\u0275\u0275elementStart"](3,"div",3),l["\u0275\u0275elementStart"](4,"div",4),l["\u0275\u0275elementStart"](5,"div",5),l["\u0275\u0275text"](6," Max value "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](7,"div",6),l["\u0275\u0275text"](8),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](9,"div",5),l["\u0275\u0275text"](10," Evaluated value "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](11,"div",7),l["\u0275\u0275template"](12,j,2,2,"mat-form-field",8),l["\u0275\u0275template"](13,A,2,1,"ng-template",null,9,l["\u0275\u0275templateRefExtractor"]),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](15,V,7,1,"div",10),l["\u0275\u0275template"](16,H,11,9,"div",11),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275reference"](14);l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",t.isEmployeeResponse),l["\u0275\u0275advance"](7),l["\u0275\u0275textInterpolate1"](" ",null==t.metriceData?null:t.metriceData.appraisal_metric_max_score," "),l["\u0275\u0275advance"](4),l["\u0275\u0275property"]("ngIf","submitted"==(null==t.employeeEvaluationMetricesData?null:t.employeeEvaluationMetricesData.employee_appraisal_metrices_evaluation_status.toLowerCase())||"open"==(null==t.employeeEvaluationMetricesData?null:t.employeeEvaluationMetricesData.employee_appraisal_metrices_evaluation_status.toLowerCase()))("ngIfElse",e),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("ngIf","submitted"==(null==t.employeeEvaluationMetricesData?null:t.employeeEvaluationMetricesData.employee_appraisal_metrices_evaluation_status.toLowerCase())||"open"==(null==t.employeeEvaluationMetricesData?null:t.employeeEvaluationMetricesData.employee_appraisal_metrices_evaluation_status.toLowerCase())),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf","approved"==(null==t.employeeEvaluationMetricesData?null:t.employeeEvaluationMetricesData.employee_appraisal_metrices_evaluation_status.toLowerCase()))}},directives:[c.NgIf,c.NgForOf,S.a,b.a,I.c,O.b,f.A,f.e,f.v,f.k,x.a,s.a,C.a,p.a,r.a,c.NgStyle],pipes:[c.DatePipe],styles:[".scoring-styles[_ngcontent-%COMP%]   .card-bg-color[_ngcontent-%COMP%]{background-color:hsla(0,0%,92.5%,.30196078431372547)!important;box-shadow:0 3px 6px hsla(0,0%,41.2%,.19),0 1px 4px hsla(0,0%,41.2%,.19)}.scoring-styles[_ngcontent-%COMP%]   .default-font[_ngcontent-%COMP%]{font-size:14px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.scoring-styles[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:100%;font-size:13px}.scoring-styles[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding-bottom:8px!important}.scoring-styles[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-form-field-underline{bottom:0}.scoring-styles[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-form-field-infix{padding:.65em 0!important;border-top:.54375em solid transparent!important;font-size:13px}.scoring-styles[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-select-arrow{margin:0 4px!important}.scoring-styles[_ngcontent-%COMP%]   .mini-tick[_ngcontent-%COMP%]{height:42px!important;width:42px!important;line-height:42px;background-color:#cf0001!important;color:#fff!important;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.scoring-styles[_ngcontent-%COMP%]   .ellipsis[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}"]}),e})();function B(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div"),l["\u0275\u0275elementStart"](1,"mat-form-field",18),l["\u0275\u0275elementStart"](2,"input",19),l["\u0275\u0275listener"]("ngModelChange",(function(t){l["\u0275\u0275restoreView"](e);const a=l["\u0275\u0275nextContext"](2).index;return l["\u0275\u0275nextContext"]().points[a].value=t})),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&e){const e=l["\u0275\u0275nextContext"](2).index,t=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngModel",t.points[e].value)}}const $=function(e){return{visibility:e}};function U(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"button",20),l["\u0275\u0275listener"]("click",(function(){l["\u0275\u0275restoreView"](e);const t=l["\u0275\u0275nextContext"](2).index;return l["\u0275\u0275nextContext"]().approveActivity(t)})),l["\u0275\u0275elementStart"](1,"mat-icon",21),l["\u0275\u0275text"](2,"done"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&e){const e=l["\u0275\u0275nextContext"](2).$implicit;l["\u0275\u0275property"]("ngStyle",l["\u0275\u0275pureFunction1"](1,$,null!=e&&e.is_comment_visible?"visible":"hidden"))}}function W(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"button",22),l["\u0275\u0275listener"]("click",(function(){l["\u0275\u0275restoreView"](e);const t=l["\u0275\u0275nextContext"](2).index;return l["\u0275\u0275nextContext"]().rejectActivity(t)})),l["\u0275\u0275elementStart"](1,"mat-icon",21),l["\u0275\u0275text"](2,"close"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&e){const e=l["\u0275\u0275nextContext"](2).$implicit;l["\u0275\u0275property"]("ngStyle",l["\u0275\u0275pureFunction1"](1,$,null!=e&&e.is_comment_visible?"visible":"hidden"))}}const G=function(e){return{color:e}};function X(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",13),l["\u0275\u0275template"](1,B,3,1,"div",2),l["\u0275\u0275template"](2,U,3,3,"button",14),l["\u0275\u0275template"](3,W,3,3,"button",15),l["\u0275\u0275elementStart"](4,"button",16),l["\u0275\u0275listener"]("click",(function(){l["\u0275\u0275restoreView"](e);const t=l["\u0275\u0275nextContext"]().index;return l["\u0275\u0275nextContext"]().addCommentForActivity(t)})),l["\u0275\u0275elementStart"](5,"mat-icon",17),l["\u0275\u0275text"](6,"forum"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&e){const e=l["\u0275\u0275nextContext"]().$implicit,t=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",t.points.length>0),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",t.showStatus),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",t.showStatus),l["\u0275\u0275advance"](1),l["\u0275\u0275propertyInterpolate"]("matTooltip",null!=e&&e.is_comment_visible?"Hide comment":"Add Comment"),l["\u0275\u0275property"]("ngStyle",l["\u0275\u0275pureFunction1"](6,$,null!=e&&e.is_comment_visible?"visible":"hidden")),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngStyle",l["\u0275\u0275pureFunction1"](8,G,null!=e&&e.is_comment_visible?"#cf0001":"#868683"))}}function Y(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",23),l["\u0275\u0275elementStart"](1,"button",24),l["\u0275\u0275listener"]("click",(function(){l["\u0275\u0275restoreView"](e);const t=l["\u0275\u0275nextContext"]().$implicit;return l["\u0275\u0275nextContext"]().getFileFromS3(t.attachmentData)})),l["\u0275\u0275elementStart"](2,"mat-icon"),l["\u0275\u0275text"](3,"cloud_download"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}}function q(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div",34),l["\u0275\u0275element"](1,"app-user-profile",35),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](2,"div",36),l["\u0275\u0275element"](3,"app-user-profile",35),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](4,"div",36),l["\u0275\u0275elementStart"](5,"mat-icon",37),l["\u0275\u0275text"](6,"fiber_manual_record"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275text"](7),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"](2).$implicit,t=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("type","name")("oid",null==e?null:e.activity_action_by),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("type","role")("oid",null==e?null:e.activity_action_by),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngStyle",l["\u0275\u0275pureFunction1"](6,G,t.getStatusColor(null==e?null:e.activity_status))),l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate1"]("",null==e?null:e.activity_status," ")}}function J(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"span",27),l["\u0275\u0275text"](1),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"](2).$implicit;l["\u0275\u0275advance"](1),l["\u0275\u0275textInterpolate1"](", ",null==e?null:e.activity_comment,"")}}const K=function(e){return{"background-color":e}};function Q(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div",25),l["\u0275\u0275element"](1,"span",26),l["\u0275\u0275elementStart"](2,"span",27),l["\u0275\u0275text"](3),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](4,"div",28),l["\u0275\u0275elementStart"](5,"span",29),l["\u0275\u0275element"](6,"app-user-image",30),l["\u0275\u0275template"](7,q,8,8,"ng-template",null,31,l["\u0275\u0275templateRefExtractor"]),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](9,J,2,1,"span",32),l["\u0275\u0275elementStart"](10,"small",33),l["\u0275\u0275text"](11),l["\u0275\u0275pipe"](12,"date"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275reference"](8),t=l["\u0275\u0275nextContext"]().$implicit,a=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngStyle",l["\u0275\u0275pureFunction1"](10,K,a.getStatusColor(null==t?null:t.activity_status))),l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate"](null==t?null:t.activity_status),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("id",null==t?null:t.activity_action_by)("tooltip",e)("borderColor",a.getStatusColor(null==t?null:t.activity_status)),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("ngIf",null==t?null:t.activity_comment),l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate"](l["\u0275\u0275pipeBind2"](12,7,null==t?null:t.activity_action_date,"dd-MM-yy hh:mm a"))}}function Z(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",38),l["\u0275\u0275elementStart"](1,"div",39),l["\u0275\u0275elementStart"](2,"app-type-comment",40),l["\u0275\u0275listener"]("commentResponse",(function(t){l["\u0275\u0275restoreView"](e);const a=l["\u0275\u0275nextContext"]().index;return l["\u0275\u0275nextContext"]().getComment(t,a)})),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}2&e&&(l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("label","Your comment"))}function ee(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div",3),l["\u0275\u0275elementStart"](1,"div",4),l["\u0275\u0275elementStart"](2,"div",5),l["\u0275\u0275elementStart"](3,"mat-icon",6),l["\u0275\u0275text"](4,"flag"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](5,"div",7),l["\u0275\u0275text"](6),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](7,"div",8),l["\u0275\u0275text"](8),l["\u0275\u0275pipe"](9,"date"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](10,X,7,10,"div",9),l["\u0275\u0275template"](11,Y,4,0,"div",10),l["\u0275\u0275template"](12,Q,13,12,"ng-template",null,11,l["\u0275\u0275templateRefExtractor"]),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](14,Z,3,1,"div",12),l["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,a=l["\u0275\u0275reference"](13),n=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("ngStyle",l["\u0275\u0275pureFunction1"](11,G,n.getStatusColor(null==e?null:e.activity_status))),l["\u0275\u0275advance"](3),l["\u0275\u0275textInterpolate1"](" ",null==e?null:e.activity_description," "),l["\u0275\u0275advance"](1),l["\u0275\u0275propertyInterpolate1"]("matTooltip"," ",null==e?null:e.activity_description,""),l["\u0275\u0275advance"](1),l["\u0275\u0275textInterpolate1"](" ",l["\u0275\u0275pipeBind2"](9,8,null==e?null:e.activity_date,"dd-MMM-yy")," "),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf","submitted"==(null==e||null==e.activity_status?null:e.activity_status.toLowerCase()))("ngIfElse",a),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",""!=(null==e?null:e.attachmentData)),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("ngIf",null==e?null:e.is_comment_visible)}}function te(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"div"),l["\u0275\u0275elementStart"](1,"div"),l["\u0275\u0275elementStart"](2,"h4",41),l["\u0275\u0275text"](3," No Activities Found ! "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](4,"div",42),l["\u0275\u0275element"](5,"img",43),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]())}let ae=(()=>{class e{constructor(e,t,a){this._evalService=e,this._util=t,this._fileSaver=a,this.evaluatorResponseForActivity=new l.EventEmitter,this.evaluatorComment=new f.j,this.appraisersList=[],this.points=[],this.status=["submitted","approved","rejected"]}ngOnInit(){if(console.log("boolean",this.metriceData),console.log("booleanemp",this.employeeEvaluationMetricesData),this.employeeEvaluationMetricesData){if(this.employeeEvaluationMetricesData.employee_evaluation_metric_response_data.length>1){let e=this.employeeEvaluationMetricesData.employee_evaluation_metric_response_data;this.employeeEvaluationMetricesData.employee_evaluation_metric_response_data=[];for(let t=0;t<this.status.length;t++)for(let a of e)a.activity_status.toLowerCase()==this.status[t]&&this.employeeEvaluationMetricesData.employee_evaluation_metric_response_data.push(a)}for(let e of this.employeeEvaluationMetricesData.employee_evaluation_metric_response_data)e.is_comment_visible=!1,this.points.push({value:this.metriceData.appraisal_metric_max_score})}}getComment(e,t){this.employeeEvaluationMetricesData.employee_evaluation_metric_response_data[t].activity_comment=e}getFormattedComment(e){if(e)return e.replace(/<\/?[^>]+(>|$)/g,"")}getStatusColor(e){return this._evalService.getStatusColor(e)}getFileFromS3(e){e="string"==typeof e?JSON.parse(e):e,console.log(e),this._evalService.getFileFromS3(e.files_json.key).then(t=>{let a;"application/pdf"==e.files_json.type&&(a=window.open(""),a.document.write("<iframe width='100%' height='100%' src='data:application/pdf;base64, "+t.data.fileData+"'></iframe>")),this._fileSaver.saveAsFile(t.data.fileData,e.files_json.fileName,e.files_json.type)})}approveActivity(e){return Object(m.c)(this,void 0,void 0,(function*(){if(this.points[e].value<=this.metriceData.appraisal_metric_max_score){let t={employee_evaluation_metrices_id:this.employeeEvaluationMetricesData._id,activity_id:this.employeeEvaluationMetricesData.employee_evaluation_metric_response_data[e]._id,activity_max_point:this.points[e].value,activity_comment:this.employeeEvaluationMetricesData.employee_evaluation_metric_response_data[e].activity_comment,approver_id:yield this._evalService.getCurrentUserOID(),approver_response:"Approved",updated_date:new Date,activity_action_date:new Date,activity_name:this.metriceData.appraisal_metric_name||null,employee_oid:this.employeeEvaluationMetricesData.employee_oid||null};this.evaluatorResponseForActivity.emit({event:"Submit",response:t}),this.updateActivity(e,t)}else this._util.showMessage("Maximum points excedeed","Dismiss",2e3)}))}rejectActivity(e){return Object(m.c)(this,void 0,void 0,(function*(){if(this.employeeEvaluationMetricesData.employee_evaluation_metric_response_data[e].is_comment_visible=!0,this.employeeEvaluationMetricesData.employee_evaluation_metric_response_data[e].activity_comment)if(this.points[e].value<=this.metriceData.appraisal_metric_max_score)if(this.employeeEvaluationMetricesData.employee_evaluation_metric_response_data[e].is_comment_visible=!0,this.employeeEvaluationMetricesData.employee_evaluation_metric_response_data[e].activity_comment){let t={employee_evaluation_metrices_id:this.employeeEvaluationMetricesData._id,activity_id:this.employeeEvaluationMetricesData.employee_evaluation_metric_response_data[e]._id,activity_max_point:this.points[e].value,activity_comment:this.employeeEvaluationMetricesData.employee_evaluation_metric_response_data[e].activity_comment,approver_id:yield this._evalService.getCurrentUserOID(),approver_response:"Rejected",updated_date:new Date,activity_action_date:new Date,activity_name:this.metriceData.appraisal_metric_name||null,employee_oid:this.employeeEvaluationMetricesData.employee_oid||null};this.evaluatorResponseForActivity.emit({event:"Submit",response:t}),this.updateActivity(e,t)}else this._util.showMessage("Kindly give a comment before rejecting","Dismiss");else this._util.showMessage("Maximum points Exceeded","Dismiss",2e3);else this._util.showMessage("Kindly give a comment before rejecting","Dismiss")}))}updateActivity(e,t){this.employeeEvaluationMetricesData.employee_evaluation_metric_response_data[e].is_comment_visible=!1,this.employeeEvaluationMetricesData.employee_evaluation_metric_response_data[e].activity_status=t.approver_response,this.employeeEvaluationMetricesData.employee_evaluation_metric_response_data[e].activity_comment=t.activity_comment,this.employeeEvaluationMetricesData.employee_evaluation_metric_response_data[e].activity_action_date=t.activity_action_date,this.employeeEvaluationMetricesData.employee_evaluation_metric_response_data[e].activity_action_by=t.approver_id}addCommentForActivity(e){this.employeeEvaluationMetricesData.employee_evaluation_metric_response_data[e].is_comment_visible=!this.employeeEvaluationMetricesData.employee_evaluation_metric_response_data[e].is_comment_visible}}return e.\u0275fac=function(t){return new(t||e)(l["\u0275\u0275directiveInject"](v.a),l["\u0275\u0275directiveInject"](i.a),l["\u0275\u0275directiveInject"](w.a))},e.\u0275cmp=l["\u0275\u0275defineComponent"]({type:e,selectors:[["app-evaluation-type-boolean"]],inputs:{metriceData:"metriceData",employeeEvaluationMetricesData:"employeeEvaluationMetricesData",showStatus:"showStatus"},outputs:{evaluatorResponseForActivity:"evaluatorResponseForActivity"},decls:3,vars:2,consts:[[1,"container-fluid","pt-2","boolean-styles"],["class","pt-2 pb-2",4,"ngFor","ngForOf"],[4,"ngIf"],[1,"pt-2","pb-2"],[1,"row","d-flex","listcard"],[1,"col-1"],[3,"ngStyle"],[1,"col-3","d-flex","default-font"],[1,"col-2","d-flex","default-font",3,"matTooltip"],["class","col-3 d-flex justify-content-center",4,"ngIf","ngIfElse"],["class","col-2 pl-5",4,"ngIf"],["otherStatus",""],["class","row pt-2",4,"ngIf"],[1,"col-3","d-flex","justify-content-center"],["mat-icon-button","","matTooltip","Approve","class","icon-tray-button mr-2",3,"ngStyle","click",4,"ngIf"],["mat-icon-button","","matTooltip","Reject","class","icon-tray-button mr-2",3,"ngStyle","click",4,"ngIf"],["mat-icon-button","",1,"icon-tray-button",3,"matTooltip","ngStyle","click"],[1,"smallCardIcon",3,"ngStyle"],[1,"pointInput"],["matInput","","placeholder","Enter Point",3,"ngModel","ngModelChange"],["mat-icon-button","","matTooltip","Approve",1,"icon-tray-button","mr-2",3,"ngStyle","click"],[1,"smallCardIcon"],["mat-icon-button","","matTooltip","Reject",1,"icon-tray-button","mr-2",3,"ngStyle","click"],[1,"col-2","pl-5"],["mat-icon-button","",2,"color","gray",3,"click"],[1,"col-2","d-flex"],[1,"mt-1","status-circular",3,"ngStyle"],[1,"pl-2","default-font"],[1,"col-4","d-flex"],[1,"pl-2"],["imgWidth","28px","imgHeight","28px","placement","right","content-type","template","max-width","300","borderWidth","2px",3,"id","tooltip","borderColor"],["appraiserTooltip",""],["class","pl-2 default-font",4,"ngIf"],[1,"pl-2","pt-1"],[1,"row","tooltip-text",2,"text-align","center"],[3,"type","oid"],[1,"row","tooltip-text"],[1,"tooltip-status-indicator","p-0","m-0",3,"ngStyle"],[1,"row","pt-2"],[1,"col-11","pl-0","pr-0"],[3,"label","commentResponse"],[1,"d-flex","justify-content-center","align-items-center","bold-font"],[1,"d-flex","justify-content-center","align-items-center"],["src","https://assets.kebs.app/images/under_png.png","height","250","width","400"]],template:function(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"div",0),l["\u0275\u0275template"](1,ee,15,13,"div",1),l["\u0275\u0275template"](2,te,6,0,"div",2),l["\u0275\u0275elementEnd"]()),2&e&&(l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngForOf",null==t.employeeEvaluationMetricesData?null:t.employeeEvaluationMetricesData.employee_evaluation_metric_response_data),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",0==(null==t.employeeEvaluationMetricesData?null:t.employeeEvaluationMetricesData.employee_evaluation_metric_response_data.length)))},directives:[c.NgForOf,c.NgIf,p.a,c.NgStyle,C.a,s.a,I.c,O.b,f.e,f.v,f.y,S.a,b.a,r.a,x.a],pipes:[c.DatePipe],styles:[".pointInput[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-flex{position:relative;height:.5rem;width:.5rem}.boolean-styles[_ngcontent-%COMP%]   .bold-font[_ngcontent-%COMP%], .boolean-styles[_ngcontent-%COMP%]   .default-font[_ngcontent-%COMP%]{font-size:14px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.boolean-styles[_ngcontent-%COMP%]   .bold-font[_ngcontent-%COMP%]{color:#cf0001;font-weight:500}.boolean-styles[_ngcontent-%COMP%]   .approve-btn[_ngcontent-%COMP%]{background-color:#64d464;line-height:37px}.boolean-styles[_ngcontent-%COMP%]   .approve-btn[_ngcontent-%COMP%], .boolean-styles[_ngcontent-%COMP%]   .reject-btn[_ngcontent-%COMP%]{font-size:18px;color:#fff;width:30px;height:30px;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 3px 0 rgba(0,0,0,.14),0 2px 14px 0 rgba(0,0,0,.12)}.boolean-styles[_ngcontent-%COMP%]   .reject-btn[_ngcontent-%COMP%]{background-color:#fc3939;line-height:40px}.boolean-styles[_ngcontent-%COMP%]   .mini-tick[_ngcontent-%COMP%]{height:42px!important;width:42px!important;line-height:42px;background-color:#cf0001!important;color:#fff!important;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.boolean-styles[_ngcontent-%COMP%]   .icon-tray-button[_ngcontent-%COMP%]{width:27px!important;height:27px!important;line-height:25px!important;visibility:visible!important}.boolean-styles[_ngcontent-%COMP%]   .smallCardIcon[_ngcontent-%COMP%]{font-size:18px!important;color:#868683}.boolean-styles[_ngcontent-%COMP%]   .status-circular[_ngcontent-%COMP%]{height:13px;width:13px;z-index:1;border-radius:50%;background-color:#c7c4c4;box-shadow:0 2px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}"]}),e})();function ne(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div",6),l["\u0275\u0275elementStart"](1,"div",13),l["\u0275\u0275element"](2,"app-user-image",14),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](3,"div",15),l["\u0275\u0275element"](4,"span",16),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](5,"div",17),l["\u0275\u0275text"](6),l["\u0275\u0275pipe"](7,"date"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("id",null==e.employeeEvaluationMetricesData?null:e.employeeEvaluationMetricesData.employee_oid),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("innerHtml",null==e.employeeEvaluationMetricesData?null:e.employeeEvaluationMetricesData.employee_evaluation_metric_response_data[0].comment,l["\u0275\u0275sanitizeHtml"]),l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate1"](" Commented on ",l["\u0275\u0275pipeBind2"](7,3,null==e.employeeEvaluationMetricesData?null:e.employeeEvaluationMetricesData.employee_evaluation_metric_response_data[0].updatedAt,"short")," ")}}function le(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"div",18),l["\u0275\u0275elementStart"](1,"div",19),l["\u0275\u0275text"](2," No response from employee "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]())}function oe(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"div",1),l["\u0275\u0275elementStart"](1,"span",2),l["\u0275\u0275text"](2," Your rating and feedback "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]())}function ie(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div",20),l["\u0275\u0275elementStart"](1,"div",21),l["\u0275\u0275element"](2,"app-user-image",14),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](3,"div",22),l["\u0275\u0275elementStart"](4,"span",23),l["\u0275\u0275text"](5),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("id",null==e.currentUser?null:e.currentUser.oid),l["\u0275\u0275advance"](3),l["\u0275\u0275textInterpolate1"](" ",null==e.currentUser?null:e.currentUser.name," ")}}function re(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",24),l["\u0275\u0275elementStart"](1,"app-type-comment",25),l["\u0275\u0275listener"]("commentResponse",(function(t){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"]().getComment(t)})),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&e){const e=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("readOnly","submitted"!=(null==e.employeeEvaluationMetricesData?null:e.employeeEvaluationMetricesData.employee_appraisal_metrices_evaluation_status.toLowerCase())&&"open"!=(null==e.employeeEvaluationMetricesData?null:e.employeeEvaluationMetricesData.employee_appraisal_metrices_evaluation_status.toLowerCase()))("label","Your Feedback")("comment",null==e.employeeEvaluationMetricesData?null:e.employeeEvaluationMetricesData.approver_comment)}}function se(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",26),l["\u0275\u0275elementStart"](1,"button",27),l["\u0275\u0275listener"]("click",(function(){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"]().saveResponse()})),l["\u0275\u0275elementStart"](2,"mat-icon"),l["\u0275\u0275text"](3,"done_all"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}}const pe=function(e){return{color:e}};function ce(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div",33),l["\u0275\u0275element"](1,"app-user-profile",34),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](2,"div",35),l["\u0275\u0275element"](3,"app-user-profile",34),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](4,"div",35),l["\u0275\u0275elementStart"](5,"mat-icon",36),l["\u0275\u0275text"](6,"fiber_manual_record"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275text"](7),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"](2);l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("type","name")("oid",null==e.employeeEvaluationMetricesData?null:e.employeeEvaluationMetricesData.approved_by),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("type","role")("oid",null==e.employeeEvaluationMetricesData?null:e.employeeEvaluationMetricesData.approved_by),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngStyle",l["\u0275\u0275pureFunction1"](6,pe,e.getStatusColor(null==e.employeeEvaluationMetricesData?null:e.employeeEvaluationMetricesData.employee_appraisal_metrices_evaluation_status))),l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate1"]("",null==e.employeeEvaluationMetricesData?null:e.employeeEvaluationMetricesData.employee_appraisal_metrices_evaluation_status," ")}}function me(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"span",23),l["\u0275\u0275text"](1),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"](2);l["\u0275\u0275advance"](1),l["\u0275\u0275textInterpolate"](null==e.employeeEvaluationMetricesData?null:e.employeeEvaluationMetricesData.approver_comment)}}function ue(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div",28),l["\u0275\u0275elementStart"](1,"div",19),l["\u0275\u0275elementStart"](2,"span",29),l["\u0275\u0275element"](3,"app-user-image",30),l["\u0275\u0275template"](4,ce,8,8,"ng-template",null,31,l["\u0275\u0275templateRefExtractor"]),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](6,"span",23),l["\u0275\u0275text"](7),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](8,me,2,1,"span",32),l["\u0275\u0275elementStart"](9,"small",29),l["\u0275\u0275text"](10),l["\u0275\u0275pipe"](11,"date"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275reference"](5),t=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("id",null==t.employeeEvaluationMetricesData?null:t.employeeEvaluationMetricesData.approved_by)("tooltip",e)("borderColor",t.getStatusColor(null==t.employeeEvaluationMetricesData?null:t.employeeEvaluationMetricesData.employee_appraisal_metrices_evaluation_status)),l["\u0275\u0275advance"](4),l["\u0275\u0275textInterpolate"](null==t.employeeEvaluationMetricesData?null:t.employeeEvaluationMetricesData.employee_appraisal_metrices_evaluation_status),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",null==t.employeeEvaluationMetricesData?null:t.employeeEvaluationMetricesData.approver_comment),l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate"](l["\u0275\u0275pipeBind2"](11,6,t.employeeEvaluationMetricesData.approver_action_date,"dd-MM-yy hh:mm a"))}}let de=(()=>{class e{constructor(e,t){this._login=e,this._evalService=t,this.evaluatorResponse=new l.EventEmitter,this.currentUser={},this.starRatingValue=0,this.evaluatorComment=new f.j,this.currDate=new Date}ngOnInit(){this.currentUser=this._login.getProfile().profile}saveResponse(){return Object(m.c)(this,void 0,void 0,(function*(){let e={employee_evaluation_metrices_id:this.employeeEvaluationMetricesData._id,evaluator_oid:yield this._evalService.getCurrentUserOID(),evaluator_score_awarded:this.starRatingValue,evaluator_comment:this.evaluatorComment.value,approver_action_is_active:!0,approver_action_date:new Date,employe_oid:this.employeeEvaluationMetricesData.employee_oid,appraisal_metrices_name:this.metriceData.appraisal_metric_name};this.evaluatorResponse.emit(e),this.employeeEvaluationMetricesData.employee_appraisal_metrices_evaluation_status="Approved",this.employeeEvaluationMetricesData.employee_appraisal_metrices_scored_obtained=e.evaluator_score_awarded,this.employeeEvaluationMetricesData.approver_comment=this.evaluatorComment.value}))}getComment(e){this.evaluatorComment.patchValue(e)}getStarRating(e){this.starRatingValue=e}getStatusColor(e){return this._evalService.getStatusColor(e)}}return e.\u0275fac=function(t){return new(t||e)(l["\u0275\u0275directiveInject"](y.a),l["\u0275\u0275directiveInject"](v.a))},e.\u0275cmp=l["\u0275\u0275defineComponent"]({type:e,selectors:[["app-evaluation-type-rating"]],inputs:{employeeEvaluationMetricesData:"employeeEvaluationMetricesData",metriceData:"metriceData"},outputs:{evaluatorResponse:"evaluatorResponse"},decls:15,vars:11,consts:[[1,"container-fluid","rating-feedback-styles","pt-2"],[1,"row","d-flex","pt-2"],[1,"pl-2","bold-font"],["class","row d-flex pt-3",4,"ngIf","ngIfElse"],["noResponseFromEmplyee",""],["class","row d-flex pt-2",4,"ngIf"],[1,"row","d-flex","pt-3"],[1,"col-4","pt-2"],["class","row d-flex pb-2",4,"ngIf"],[3,"totalStars","metricName","totalScore","readOnly","starsResponse"],["class","col-7 pt-2",4,"ngIf"],["class","col-1 d-flex",4,"ngIf"],["class","row",4,"ngIf"],[1,"col-1",2,"padding-left","32px"],["imgWidth","30px","imgHeight","30px",3,"id"],[1,"col-7"],[3,"innerHtml"],[1,"col",2,"color","gray","font-size","12px"],[1,"row","d-flex","pt-3","pb-3",2,"color","gray"],[1,"col-12"],[1,"row","d-flex","pb-2"],[1,"col-3"],[1,"col-8"],[1,"pl-2","default-font","my-auto"],[1,"col-7","pt-2"],[3,"readOnly","label","comment","commentResponse"],[1,"col-1","d-flex"],["matTooltip","Save","mat-mini-fab","",1,"ml-auto","my-auto","mini-tick",3,"click"],[1,"row"],[1,"pl-2"],["imgWidth","33px","imgHeight","33px","placement","right","content-type","template","max-width","300","borderStyle","solid","borderWidth","2px",3,"id","tooltip","borderColor"],["appraiserTooltip",""],["class","pl-2 default-font my-auto",4,"ngIf"],[1,"row","tooltip-text",2,"text-align","center"],[3,"type","oid"],[1,"row","tooltip-text"],[1,"tooltip-status-indicator","p-0","m-0",3,"ngStyle"]],template:function(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div",0),l["\u0275\u0275elementStart"](1,"div",1),l["\u0275\u0275elementStart"](2,"span",2),l["\u0275\u0275text"](3," Employee Response "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](4,ne,8,6,"div",3),l["\u0275\u0275template"](5,le,3,0,"ng-template",null,4,l["\u0275\u0275templateRefExtractor"]),l["\u0275\u0275template"](7,oe,3,0,"div",5),l["\u0275\u0275elementStart"](8,"div",6),l["\u0275\u0275elementStart"](9,"div",7),l["\u0275\u0275template"](10,ie,6,2,"div",8),l["\u0275\u0275elementStart"](11,"app-type-star",9),l["\u0275\u0275listener"]("starsResponse",(function(e){return t.getStarRating(e)})),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](12,re,2,3,"div",10),l["\u0275\u0275template"](13,se,4,0,"div",11),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](14,ue,12,9,"div",12),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275reference"](6);l["\u0275\u0275advance"](4),l["\u0275\u0275property"]("ngIf",t.employeeEvaluationMetricesData.employee_evaluation_metric_response_data.length>0)("ngIfElse",e),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("ngIf","approved"!=(null==t.employeeEvaluationMetricesData?null:t.employeeEvaluationMetricesData.employee_appraisal_metrices_evaluation_status.toLowerCase())&&"closed"!=(null==t.employeeEvaluationMetricesData?null:t.employeeEvaluationMetricesData.employee_appraisal_metrices_evaluation_status.toLowerCase())),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("ngIf","approved"!=(null==t.employeeEvaluationMetricesData?null:t.employeeEvaluationMetricesData.employee_appraisal_metrices_evaluation_status.toLowerCase())&&"closed"!=(null==t.employeeEvaluationMetricesData?null:t.employeeEvaluationMetricesData.employee_appraisal_metrices_evaluation_status.toLowerCase())),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("totalStars",5)("metricName","Rating")("totalScore",null==t.employeeEvaluationMetricesData?null:t.employeeEvaluationMetricesData.employee_appraisal_metrices_scored_obtained)("readOnly","submitted"!=(null==t.employeeEvaluationMetricesData?null:t.employeeEvaluationMetricesData.employee_appraisal_metrices_evaluation_status.toLowerCase())&&"open"!=(null==t.employeeEvaluationMetricesData?null:t.employeeEvaluationMetricesData.employee_appraisal_metrices_evaluation_status.toLowerCase())),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf","approved"!=(null==t.employeeEvaluationMetricesData?null:t.employeeEvaluationMetricesData.employee_appraisal_metrices_evaluation_status.toLowerCase())&&"closed"!=(null==t.employeeEvaluationMetricesData?null:t.employeeEvaluationMetricesData.employee_appraisal_metrices_evaluation_status.toLowerCase())),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf","submitted"==(null==t.employeeEvaluationMetricesData?null:t.employeeEvaluationMetricesData.employee_appraisal_metrices_evaluation_status.toLowerCase())||"open"==(null==t.employeeEvaluationMetricesData?null:t.employeeEvaluationMetricesData.employee_appraisal_metrices_evaluation_status.toLowerCase())),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf","approved"==(null==t.employeeEvaluationMetricesData?null:t.employeeEvaluationMetricesData.employee_appraisal_metrices_evaluation_status.toLowerCase()))}},directives:[c.NgIf,g.a,S.a,x.a,s.a,C.a,p.a,b.a,r.a,c.NgStyle],pipes:[c.DatePipe],styles:[".rating-feedback-styles[_ngcontent-%COMP%]   .bold-font[_ngcontent-%COMP%]{color:#cf0001;font-weight:500}.rating-feedback-styles[_ngcontent-%COMP%]   .bold-font[_ngcontent-%COMP%], .rating-feedback-styles[_ngcontent-%COMP%]   .default-font[_ngcontent-%COMP%]{font-size:14px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.rating-feedback-styles[_ngcontent-%COMP%]   .text-area-class[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:100%;font-size:13px}.rating-feedback-styles[_ngcontent-%COMP%]   .mini-tick[_ngcontent-%COMP%]{height:42px!important;width:42px!important;line-height:42px;background-color:#cf0001!important;color:#fff!important;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}"]}),e})();function ve(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"app-evaluation-type-cyclic-rating-and-feedback",4),l["\u0275\u0275listener"]("evaluatorResponse",(function(t){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"](2).saveEvaluatorResponse(t)})),l["\u0275\u0275elementEnd"]()}if(2&e){const e=l["\u0275\u0275nextContext"](2);l["\u0275\u0275property"]("metriceData",e.evaluationMetricesData)("employeeEvaluationMetricesData",e.employeeEvaluationMetrices)}}function _e(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div",20),l["\u0275\u0275element"](1,"app-user-profile",21),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](2,"div",22),l["\u0275\u0275element"](3,"app-user-profile",21),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"]().$implicit;l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("type","name")("oid",e.employee_appraisal_metrices_evaluator_oid),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("type","role")("oid",e.employee_appraisal_metrices_evaluator_oid)}}function ye(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"span"),l["\u0275\u0275element"](1,"app-user-image",18),l["\u0275\u0275template"](2,_e,4,4,"ng-template",null,19,l["\u0275\u0275templateRefExtractor"]),l["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,a=l["\u0275\u0275reference"](3),n=l["\u0275\u0275nextContext"](3);l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("id",e.employee_appraisal_metrices_evaluator_oid)("tooltip",a)("borderColor",n.getStatusColor(e.employee_appraisal_metrices_evaluator_status))}}function fe(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"app-evaluation-type-scoring",4),l["\u0275\u0275listener"]("evaluatorResponse",(function(t){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"](3).saveEvaluatorResponse(t)})),l["\u0275\u0275elementEnd"]()}if(2&e){const e=l["\u0275\u0275nextContext"](3);l["\u0275\u0275property"]("metriceData",e.evaluationMetricesData)("employeeEvaluationMetricesData",e.employeeEvaluationMetrices)}}function ge(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"app-evaluation-type-boolean",23),l["\u0275\u0275listener"]("evaluatorResponseForActivity",(function(t){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"](3).saveEvaluatorActivityResponse(t)})),l["\u0275\u0275elementEnd"]()}if(2&e){const e=l["\u0275\u0275nextContext"](3);l["\u0275\u0275property"]("metriceData",e.evaluationMetricesData)("employeeEvaluationMetricesData",e.employeeEvaluationMetrices)}}function xe(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"app-evaluation-type-rating",4),l["\u0275\u0275listener"]("evaluatorResponse",(function(t){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"](3).saveEvaluatorResponse(t)})),l["\u0275\u0275elementEnd"]()}if(2&e){const e=l["\u0275\u0275nextContext"](3);l["\u0275\u0275property"]("metriceData",e.evaluationMetricesData)("employeeEvaluationMetricesData",e.employeeEvaluationMetrices)}}const Ee=function(e){return{"border-left-color":e}},he=function(e){return{background:e}};function Me(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"mat-accordion",5),l["\u0275\u0275elementStart"](1,"mat-expansion-panel",6),l["\u0275\u0275elementStart"](2,"mat-expansion-panel-header",7),l["\u0275\u0275listener"]("click",(function(){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"](2).toggleClicked()})),l["\u0275\u0275elementStart"](3,"div",8),l["\u0275\u0275elementStart"](4,"div",9),l["\u0275\u0275elementStart"](5,"mat-icon",10),l["\u0275\u0275text"](6),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](7,"span",11),l["\u0275\u0275text"](8),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](9,"div",12),l["\u0275\u0275element"](10,"span",13),l["\u0275\u0275elementStart"](11,"span",14),l["\u0275\u0275text"](12),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](13,"div",15),l["\u0275\u0275template"](14,ye,4,3,"span",16),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](15,fe,1,2,"app-evaluation-type-scoring",2),l["\u0275\u0275template"](16,ge,1,2,"app-evaluation-type-boolean",17),l["\u0275\u0275template"](17,xe,1,2,"app-evaluation-type-rating",2),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&e){const e=l["\u0275\u0275nextContext"](2);l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngStyle",l["\u0275\u0275pureFunction1"](10,Ee,e.getStatusColor(null==e.employeeEvaluationMetrices?null:e.employeeEvaluationMetrices.employee_appraisal_metrices_evaluation_status))),l["\u0275\u0275advance"](5),l["\u0275\u0275textInterpolate"](e.employeeEvaluationMetrices.detail_mode_activated?"keyboard_arrow_down":"keyboard_arrow_right"),l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate"](null==e.evaluationMetricesData?null:e.evaluationMetricesData.appraisal_metric_name),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngStyle",l["\u0275\u0275pureFunction1"](12,he,e.getStatusColor(null==e.employeeEvaluationMetrices?null:e.employeeEvaluationMetrices.employee_appraisal_metrices_evaluation_status))),l["\u0275\u0275advance"](1),l["\u0275\u0275propertyInterpolate"]("matTooltip",null==e.employeeEvaluationMetrices?null:e.employeeEvaluationMetrices.employee_appraisal_metrices_evaluation_status),l["\u0275\u0275advance"](1),l["\u0275\u0275textInterpolate1"](" ",null==e.employeeEvaluationMetrices?null:e.employeeEvaluationMetrices.employee_appraisal_metrices_evaluation_status,""),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngForOf",e.appraisersList),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf","scoring"==(null==e.evaluationMetricesData?null:e.evaluationMetricesData.appraisal_metric_evaluation_type)),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf","boolean"==(null==e.evaluationMetricesData?null:e.evaluationMetricesData.appraisal_metric_evaluation_type)),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf","rating"==(null==e.evaluationMetricesData?null:e.evaluationMetricesData.appraisal_metric_evaluation_type))}}function De(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div"),l["\u0275\u0275elementStart"](1,"div",1),l["\u0275\u0275template"](2,ve,1,2,"app-evaluation-type-cyclic-rating-and-feedback",2),l["\u0275\u0275template"](3,Me,18,14,"mat-accordion",3),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf","ratingAndFeedback"==(null==e.evaluationMetricesData?null:e.evaluationMetricesData.appraisal_metric_evaluation_type)),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf","ratingAndFeedback"!=(null==e.evaluationMetricesData?null:e.evaluationMetricesData.appraisal_metric_evaluation_type))}}function Ce(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",25),l["\u0275\u0275elementStart"](1,"button",26),l["\u0275\u0275listener"]("click",(function(){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"](2).openDialogCustomerReview()})),l["\u0275\u0275text"](2,"View Customer Rating"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}}function Se(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"app-evaluation-type-cyclic-rating-and-feedback",4),l["\u0275\u0275listener"]("evaluatorResponse",(function(t){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"](2).saveEvaluatorResponseRFB(t)})),l["\u0275\u0275elementEnd"]()}if(2&e){const e=l["\u0275\u0275nextContext"](2);l["\u0275\u0275property"]("metriceData",e.evaluationMetricesData)("employeeEvaluationMetricesData",e.employeeEvaluationMetrices)}}function be(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div"),l["\u0275\u0275elementStart"](1,"div",1),l["\u0275\u0275template"](2,Ce,3,0,"div",24),l["\u0275\u0275template"](3,Se,1,2,"app-evaluation-type-cyclic-rating-and-feedback",2),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf","ratingAndFeedback"==(null==e.evaluationMetricesData?null:e.evaluationMetricesData.appraisal_metric_evaluation_type)&&0==e.ind),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf","ratingAndFeedback"==(null==e.evaluationMetricesData?null:e.evaluationMetricesData.appraisal_metric_evaluation_type))}}let we=(()=>{class e{constructor(e,t,n,l,o,i){this._EmployeeAppraisalsService=e,this._evalService=t,this._util=n,this._ErrorService=l,this.dialog=o,this._auth=i,this.appraisersList=[],this._onDestroy=new d.b,this.openDialogCustomerReview=()=>Object(m.c)(this,void 0,void 0,(function*(){const{CustomerViewPopupComponent:e}=yield a.e(342).then(a.bind(null,"8njp"));this._EmployeeAppraisalsService.getEmployeeMetricesForCustomer(this.employeeEvaluationMetrices.employee_oid,this.employeeEvaluationMetrices.appraisal_cycle_id,this.employeeEvaluationMetrices.appraisal_module_id).subscribe(t=>{this.dialog.open(e,{data:t.data,height:"34rem",width:"72rem"}).afterClosed().subscribe(e=>{console.log("Dialog result: "+e)})},e=>{console.error(e),this._util.showMessage("Error in fetching customer review","dismiss",2e3)})})),this.oid=this._auth.getProfile().profile.oid}getEmployeeAppraisalMetrices(){this._EmployeeAppraisalsService.getEmployeeAppraisalMetricesById(this.evaluationMetricesId).pipe(Object(u.a)(this._onDestroy)).subscribe(e=>Object(m.c)(this,void 0,void 0,(function*(){"N"==e.error&&(this.employeeEvaluationMetrices=e.data,this.appraisersList=this._evalService.getAppraisers(this.employeeEvaluationMetrices,"manager"),this.employeeEvaluationMetrices.detail_mode_activated=!1,this.getEvaluationMetricesById(this.employeeEvaluationMetrices.appraisal_metrices_id)),console.log(e,"Evaluation Metices Data")})),e=>{console.log(e)})}getEvaluationMetricesById(e){this._EmployeeAppraisalsService.getAppraisalMetricesById(e).pipe(Object(u.a)(this._onDestroy)).subscribe(e=>{"N"==e.error&&(this.evaluationMetricesData=e.data),console.log(e)},e=>{console.log(e)})}saveEvaluatorResponse(e){console.log(e,"Evaluator REsponse"),this._EmployeeAppraisalsService.updateEvaluatorScoreAwarded(e).subscribe(e=>{this._util.showMessage("Response Updated Successfully","Dismiss"),console.log(e)},e=>{console.log(e),this._util.showMessage("Error Updating response. Pls try later","Dismiss")})}saveEvaluatorActivityResponse(e){console.log("Evaluator response activity",e),this._evalService.saveEvaluatorActivityResponse(e.response).subscribe(t=>{console.log(t),this._util.showMessage("Activity has been "+e.response.approver_response+"!","Dismiss",1e3)},e=>{console.log(e),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity")})}getStatusColor(e){return this._evalService.getStatusColor(e)}toggleClicked(){this.employeeEvaluationMetrices.detail_mode_activated=!this.employeeEvaluationMetrices.detail_mode_activated}ngOnInit(){this.getEmployeeAppraisalMetrices()}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}saveEvaluatorResponseRFB(e){console.log(e,"event"),this._EmployeeAppraisalsService.updateEvaluatorScoreAwarded(e).subscribe(e=>{this._util.showMessage("Feedback Saved Successfully","dismiss",2e3)},e=>{this._util.showMessage("Error Saving Response","dismiss",2e3)})}}return e.\u0275fac=function(t){return new(t||e)(l["\u0275\u0275directiveInject"](o.a),l["\u0275\u0275directiveInject"](v.a),l["\u0275\u0275directiveInject"](i.a),l["\u0275\u0275directiveInject"](_.a),l["\u0275\u0275directiveInject"](n.b),l["\u0275\u0275directiveInject"](y.a))},e.\u0275cmp=l["\u0275\u0275defineComponent"]({type:e,selectors:[["app-list-evaluation-metrices"]],inputs:{evaluationMetricesId:"evaluationMetricesId",ind:"ind"},decls:2,vars:2,consts:[[4,"ngIf"],[1,"container-fluid","assessment-item-styles","pt-2"],[3,"metriceData","employeeEvaluationMetricesData","evaluatorResponse",4,"ngIf"],["class","listcard",4,"ngIf"],[3,"metriceData","employeeEvaluationMetricesData","evaluatorResponse"],[1,"listcard"],["hideToggle","",2,"border-left","3px solid #9a9a9a","padding","1px",3,"ngStyle"],[3,"click"],[1,"row","p-0",2,"width","100% !important"],[1,"col-3","pl-0","d-flex"],[1,"iconButton","mt-1"],[1,"default-font","pl-2","my-auto"],[1,"col-2","d-flex","pl-0","pr-0","pt-1"],[1,"status-circular",3,"ngStyle"],[1,"ml-2",3,"matTooltip"],[1,"col-3","pl-2"],[4,"ngFor","ngForOf"],[3,"metriceData","employeeEvaluationMetricesData","evaluatorResponseForActivity",4,"ngIf"],["imgWidth","33px","imgHeight","33px","placement","right","content-type","template","max-width","300","borderStyle","solid","borderWidth","2px",2,"margin","4px",3,"id","tooltip","borderColor"],["appraiserTooltip",""],[1,"row","tooltip-text",2,"text-align","center"],[3,"type","oid"],[1,"row","tooltip-text"],[3,"metriceData","employeeEvaluationMetricesData","evaluatorResponseForActivity"],["class","d-flex justify-content-end",4,"ngIf"],[1,"d-flex","justify-content-end"],["mat-button","",3,"click"]],template:function(e,t){1&e&&(l["\u0275\u0275template"](0,De,4,2,"div",0),l["\u0275\u0275template"](1,be,4,2,"div",0)),2&e&&(l["\u0275\u0275property"]("ngIf","OR"==(null==t.employeeEvaluationMetrices?null:t.employeeEvaluationMetrices.employee_appraisal_metrices_evaluation_operation)),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf","AND"==(null==t.employeeEvaluationMetrices?null:t.employeeEvaluationMetrices.employee_appraisal_metrices_evaluation_operation)))},directives:[c.NgIf,M,D.a,D.c,c.NgStyle,D.g,p.a,C.a,c.NgForOf,S.a,b.a,r.a,T,ae,de,s.a],styles:[".assessment-item-styles[_ngcontent-%COMP%]   .listcard[_ngcontent-%COMP%]{transition:all .25s linear;height:auto;max-height:39px!important}.assessment-item-styles[_ngcontent-%COMP%]   .listcard[_ngcontent-%COMP%]:hover{box-shadow:0 4px 8px 0 rgba(0,0,0,.2),0 6px 20px 0 rgba(0,0,0,.19)}.assessment-item-styles[_ngcontent-%COMP%]   .listcard[_ngcontent-%COMP%]:hover   .icon-tray-button[_ngcontent-%COMP%]{visibility:visible!important;animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}.assessment-item-styles[_ngcontent-%COMP%]   .bold-font[_ngcontent-%COMP%]{color:#cf0001;font-weight:500}.assessment-item-styles[_ngcontent-%COMP%]   .bold-font[_ngcontent-%COMP%], .assessment-item-styles[_ngcontent-%COMP%]   .default-font[_ngcontent-%COMP%]{font-size:14px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.assessment-item-styles[_ngcontent-%COMP%]   .status-circular[_ngcontent-%COMP%]{height:13px;width:13px;margin-top:4px;border-radius:50%;box-shadow:0 2px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.assessment-item-styles[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}"]}),e})();function Ie(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div"),l["\u0275\u0275element"](1,"app-list-evaluation-metrices",7),l["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,a=t.index;l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ind",a)("evaluationMetricesId",e)}}let Oe=(()=>{class e{constructor(e,t,a,n){this.data=e,this._EmployeeAppraisalsService=t,this._util=a,this._dialogRef=n,this.elaResArray=[],this.employeeDetails=this.data.employeeDetails,this.evaluationData=this.data.evaluationData,this.elaResArray=this.data.elaResArray}ngOnInit(){}closeDialog(){this._dialogRef.close()}}return e.\u0275fac=function(t){return new(t||e)(l["\u0275\u0275directiveInject"](n.a),l["\u0275\u0275directiveInject"](o.a),l["\u0275\u0275directiveInject"](i.a),l["\u0275\u0275directiveInject"](n.h))},e.\u0275cmp=l["\u0275\u0275defineComponent"]({type:e,selectors:[["app-list-cycle-dialog"]],decls:11,vars:3,consts:[[1,"row","mt-3","pt-2",2,"background-color","#eeeeee"],[1,"col-11"],["type","name",3,"oid"],[1,"col-1"],["mat-icon-button","",3,"click"],[1,"mb-2",2,"overflow","scroll"],[4,"ngFor","ngForOf"],[3,"ind","evaluationMetricesId"]],template:function(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"div",0),l["\u0275\u0275elementStart"](1,"div",1),l["\u0275\u0275elementStart"](2,"h2"),l["\u0275\u0275text"](3),l["\u0275\u0275element"](4,"app-user-profile",2),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](5,"div",3),l["\u0275\u0275elementStart"](6,"button",4),l["\u0275\u0275listener"]("click",(function(){return t.closeDialog()})),l["\u0275\u0275elementStart"](7,"mat-icon"),l["\u0275\u0275text"](8,"close"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](9,"div",5),l["\u0275\u0275template"](10,Ie,2,2,"div",6),l["\u0275\u0275elementEnd"]()),2&e&&(l["\u0275\u0275advance"](3),l["\u0275\u0275textInterpolate1"](" Evaluate ",t.data.cycleName," For "),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("oid",t.data.employeename),l["\u0275\u0275advance"](6),l["\u0275\u0275property"]("ngForOf",t.employeeDetails))},directives:[r.a,s.a,p.a,c.NgForOf,we],styles:[""]}),e})()}}]);