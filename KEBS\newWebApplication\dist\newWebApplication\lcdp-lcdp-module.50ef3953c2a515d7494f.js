(window.webpackJsonp=window.webpackJsonp||[]).push([[783,634,765,821,822,858,861,977,981,983,987,990,991],{H44p:function(e,t,n){"use strict";n.d(t,"a",(function(){return f}));var i=n("xG9w"),r=n("fXoL"),o=n("flaP"),s=n("ofXK"),a=n("Qu3c"),l=n("NFeN");function c(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",9),r["\u0275\u0275elementStart"](1,"div",10),r["\u0275\u0275elementStart"](2,"div"),r["\u0275\u0275text"](3),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](4,"div"),r["\u0275\u0275elementStart"](5,"p",11),r["\u0275\u0275text"](6),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](7,"p",12),r["\u0275\u0275text"](8),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),r["\u0275\u0275advance"](3),r["\u0275\u0275textInterpolate"](null==e.currency[e.index]?null:e.currency[e.index].currency_code),r["\u0275\u0275advance"](3),r["\u0275\u0275textInterpolate"](e.label),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate1"](" ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function u(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",13),r["\u0275\u0275elementStart"](1,"span"),r["\u0275\u0275text"](2),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate1"](" ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function h(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",14),r["\u0275\u0275elementStart"](1,"span",15),r["\u0275\u0275text"](2),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function d(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",16),r["\u0275\u0275elementStart"](1,"span",15),r["\u0275\u0275text"](2),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function p(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",17),r["\u0275\u0275elementStart"](1,"span",18),r["\u0275\u0275text"](2),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function g(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"mat-icon",19),r["\u0275\u0275text"](1,"loop"),r["\u0275\u0275elementEnd"]())}function m(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"div",1),r["\u0275\u0275listener"]("click",(function(){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"]().change()})),r["\u0275\u0275template"](1,c,9,4,"div",2),r["\u0275\u0275template"](2,u,3,2,"div",3),r["\u0275\u0275template"](3,h,3,3,"div",4),r["\u0275\u0275template"](4,d,3,3,"div",5),r["\u0275\u0275template"](5,p,3,3,"div",6),r["\u0275\u0275elementStart"](6,"div",7),r["\u0275\u0275template"](7,g,2,0,"mat-icon",8),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&e){const e=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","big"==e.type),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","small"==e.type),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","medium"==e.type),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","large"==e.type),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","overview"==e.type),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngIf",e.toDisplay)}}let f=(()=>{class e{constructor(e){this.roleService=e,this.showActualAmount=!1,this.currency=[]}set currencyList(e){if(this.currency=e,e){const t=i.findIndex(e,e=>e.currency_code==this.currency_code);-1!=t&&(this.index=t,this.currency[this.index].value=Math.trunc(this.currency[this.index].value))}}ngOnInit(){null==this.toDisplay&&(this.toDisplay=!0),this.tenant_info=this.roleService.currency_info,this.tenantName=this.tenant_info.tenant_name,this.currency_code=this.defaultCurrencyCode?this.defaultCurrencyCode:this.tenant_info.default_currency?this.tenant_info.default_currency:"INR",this.setCurrency(),this.isConvertValue=null==this.tenant_info.is_to_convert_currency_value||this.tenant_info.is_to_convert_currency_value}setCurrency(){if(this.currency){const e=i.findIndex(this.currency,e=>e.currency_code==this.currency_code);-1!=e&&(this.index=e,this.currency[this.index].value=Math.trunc(this.currency[this.index].value))}}change(){void 0!==event.stopPropagation?event.stopPropagation():void 0!==event.cancelBubble&&(event.cancelBubble=!0),this.currency.length>1&&(this.index=++this.index%this.currency.length),this.currency[this.index].value=Math.trunc(this.currency[this.index].value)}convert(e,t){return e?(this.showActualAmount?e=new Intl.NumberFormat("INR"==t?"en-IN":"en-US",{}).format(e)+" "+t:1==this.isConvertValue?e="INR"==t?(e/1e7).toFixed(2)+" Cr":(e/1e6).toFixed(2)+" M":0!=this.isConvertValue||i.contains(["big","small"],this.type)?0==this.isConvertValue&&i.contains(["big","small"],this.type)&&(e="INR"==t?"big"!=this.type?"INR "+new Intl.NumberFormat("en-IN",{}).format(e):new Intl.NumberFormat("en-IN",{}).format(e):"big"!=this.type?t+" "+new Intl.NumberFormat("en-US",{}).format(e):new Intl.NumberFormat("en-US",{}).format(e)):e="INR"==t?(e/1e7).toFixed(2)+" Cr":(e/1e6).toFixed(2)+" M",e):"-"}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](o.a))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["app-currency"]],inputs:{type:"type",label:"label",showActualAmount:"showActualAmount",acl:"acl",toDisplay:"toDisplay",defaultCurrencyCode:"defaultCurrencyCode",currencyList:"currencyList"},decls:1,vars:1,consts:[["class","d-flex currency-wrapper",3,"click",4,"ngIf"],[1,"d-flex","currency-wrapper",3,"click"],["class","d-flex",3,"matTooltip",4,"ngIf"],["class","data-label",3,"matTooltip",4,"ngIf"],["class","data-medium d-flex",3,"matTooltip",4,"ngIf"],["class","data-large d-flex",3,"matTooltip",4,"ngIf"],["class","justify-content-center",3,"matTooltip",4,"ngIf"],[1,"d-flex","align-items-center"],["class","change-icon pl-2","matTooltip","Next currency",4,"ngIf"],[1,"d-flex",3,"matTooltip"],[1,"pr-3","my-auto","currency-code","justify-content-center"],[1,"m-0","header"],[1,"m-0","data-label","pt-1"],[1,"data-label",3,"matTooltip"],[1,"data-medium","d-flex",3,"matTooltip"],[1,"justify-content-center"],[1,"data-large","d-flex",3,"matTooltip"],[1,"justify-content-center",3,"matTooltip"],[1,"data-overview"],["matTooltip","Next currency",1,"change-icon","pl-2"]],template:function(e,t){1&e&&r["\u0275\u0275template"](0,m,8,6,"div",0),2&e&&r["\u0275\u0275property"]("ngIf",t.currency)},directives:[s.NgIf,a.a,l.a],styles:[".header[_ngcontent-%COMP%]{color:#5f5f5f;font-size:11px!important}.data-label[_ngcontent-%COMP%]{font-size:13px!important}.change-icon[_ngcontent-%COMP%]{font-size:18px;width:auto;height:auto;visibility:hidden;cursor:default}.currency-code[_ngcontent-%COMP%]{padding-right:8px;font-size:14px!important}.currency-wrapper[_ngcontent-%COMP%]{white-space:nowrap!important;height:100%}.currency-wrapper[_ngcontent-%COMP%]:hover   .change-icon[_ngcontent-%COMP%]{visibility:visible}.data-medium[_ngcontent-%COMP%]{color:#252422!important;font-size:15px!important}.data-large[_ngcontent-%COMP%], .data-medium[_ngcontent-%COMP%]{text-align:center!important;font-weight:440!important;padding-right:4px!important}.data-large[_ngcontent-%COMP%]{font-size:large!important}.data-overview[_ngcontent-%COMP%]{color:#252422!important;font-size:15px!important;padding-left:24px!important;font-weight:500!important;padding-right:4px!important;text-align:center!important;margin:auto!important}"]}),e})()},HmYF:function(e,t,n){"use strict";n.d(t,"a",(function(){return c}));var i=n("mrSG"),r=n("Iab2"),o=n("EUZL"),s=n("wd/R"),a=n("xG9w"),l=n("fXoL");let c=(()=>{class e{constructor(){this.formatColumn=(e,t,n)=>{const i=o.utils.decode_range(e["!ref"]);for(let r=i.s.r+1;r<=i.e.r;++r){const i=o.utils.encode_cell({r:r,c:t});e[i]&&e[i].v&&(e[i].t="d",e[i].z=n)}}}exportAsExcelFile(e,t,n,i,r){console.log("Excel to JSON Service",e);const s=o.utils.json_to_sheet(e);if(r&&r.length){const e=o.utils.sheet_to_json(s,{header:1}).shift();for(const t of r){const n=e.indexOf(t.fieldKey);this.formatColumn(s,n,t.fieldFormat)}}null==n&&(n=[]),null==i&&(i="DD-MM-YYYY"),this.formatExcelDateData(s,n,i);const a=o.write({Sheets:{data:s},SheetNames:["data"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(a,t)}formatExcelDateData(e,t,n){for(let o of Object.keys(e))if(null!=e[o]&&null!=e[o].t&&null!=e[o].v&&s(e[o].v,n,!0).isValid()){let i=o.replace(/[0-9]/g,"")+"1";0==a.where(t,{value:e[i].v}).length&&null!=e[i]&&null!=e[i].t&&t.push({value:e[i].v,format:n})}let i=[],r=1;for(let o of t)for(let t of Object.keys(e)){let n=parseInt(t.replace(/[^0-9]/g,""));n>r&&(r=n),null!=e[t]&&null!=e[t].v&&e[t].v==o.value&&i.push({value:t.replace(/[0-9]/g,""),format:o.format})}for(let o of i)for(let t=2;t<=r;t++)null!=e[o.value+""+t]&&null!=e[o.value+""+t].t&&(e[o.value+""+t].t="d",null!=e[o.value+""+t].v&&"Invalid date"!=e[o.value+""+t].v?e[o.value+""+t].v=s(e[o.value+""+t].v,o.format).format("YYYY/MM/DD"):(console.log(e[o.value+""+t].t),e[o.value+""+t].v="",e[o.value+""+t].t="s"))}saveAsExcelFile(e,t){const n=new Blob([e],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8"});r.saveAs(n,t.concat(".xlsx"))}exportAsExcelFileWFH(e,t,n){const i=o.utils.json_to_sheet(e),r=o.utils.json_to_sheet(t),s=o.write({Sheets:{All_Approvals:i,Pending_Approvals:r},SheetNames:["All_Approvals","Pending_Approvals"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(s,n)}exportAsExcelFileForPayroll(e,t,n,i,r,s){const a=o.utils.json_to_sheet(e),l=o.utils.json_to_sheet(t),c=o.utils.json_to_sheet(n),u=o.utils.json_to_sheet(i),h=o.utils.json_to_sheet(r),d=o.write({Sheets:{Regular_Report:a,Intern_Report:l,Contract_Report:c,Perdiem_Report:u,RP_Report:h},SheetNames:["Regular_Report","Intern_Report","Contract_Report","Perdiem_Report","RP_Report"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(d,s)}exportAsCsvFileWithSheetName(e,t){return Object(i.c)(this,void 0,void 0,(function*(){let n=o.utils.book_new();for(let t of e){let e=o.utils.json_to_sheet(t.data);o.utils.book_append_sheet(n,e,t.sheetName)}let i=o.write(n,{bookType:"xlsx",type:"binary"});yield this.saveAsCsvFile(i,t)}))}saveAsCsvFile(e,t){return Object(i.c)(this,void 0,void 0,(function*(){const n=new Blob([yield this.s2ab(e)],{type:"application/octet-stream"});r.saveAs(n,t.concat(".csv"))}))}s2ab(e){return Object(i.c)(this,void 0,void 0,(function*(){for(var t=new ArrayBuffer(e.length),n=new Uint8Array(t),i=0;i<e.length;i++)n[i]=255&e.charCodeAt(i);return t}))}exportAsExcelFileWithCellMerge(e,t,n){const i=o.utils.json_to_sheet(e);i["!merges"]=n;const r=o.write({Sheets:{data:i},SheetNames:["data"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(r,t)}exportJsonToExcelWithMultipleSheets(e,t){return Object(i.c)(this,void 0,void 0,(function*(){let n=o.utils.book_new();for(let t of e){let e=o.utils.json_to_sheet(t.data);o.utils.book_append_sheet(n,e,t.sheetName)}let i=o.write(n,{bookType:"xlsx",type:"array"});this.saveAsExcelFile(i,t)}))}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275prov=l["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},NJ67:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));class i{constructor(){this.disabled=!1}onChange(e){}onTouched(e){}writeValue(e){this.value=e}registerOnChange(e){this.onChange=e}registerOnTouched(e){this.onTouched=e}setDisabledState(e){this.disabled=e}}},hJL4:function(e,t,n){"use strict";n.d(t,"a",(function(){return h}));var i=n("mrSG"),r=n("XNiG"),o=n("xG9w"),s=n("fXoL"),a=n("tk/3"),l=n("LcQX"),c=n("XXEo"),u=n("flaP");let h=(()=>{class e{constructor(e,t,n,i){this.http=e,this.UtilityService=t,this.loginService=n,this.roleService=i,this.msg=new r.b,this.taskStatusColor=[],this.approveRequest=(e,t)=>this.http.post("/api/isa/request/approveResourceRequest",{requestDetails:e,approverOid:t}),this.rejectRequest=(e,t)=>this.http.post("/api/isa/request/rejectResourceRequest",{requestDetails:e,approverOid:t}),this.getTaskStatusColor()}getAllRequestsOfUser(e,t,n,i,r,o,s){let a=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getAllRequestsOfUser",{oid:e,statusId:t,statusCode:n,objectIds:i,skip:r,limit:o,filterConfig:s,orgIds:a})}getAllRoleAccess(){return o.where(this.roleService.roles,{application_id:139})}getRequestsBasedOnStatus(e,t,n,i,r,o,s){let a=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getRequestsBasedOnStatus",{oid:e,statusId:t,statusCode:n,objectIds:i,skip:r,limit:o,filterConfig:s,orgIds:a})}getRequestsForAwaitingApproval(e,t,n,i){return this.http.post("/api/isa/request/getRequestsForAwaitingApproval",{oid:e,skip:t,limit:n,filterConfig:i})}getObjectIdsBasedOnOid(e){return this.http.post("/api/isa/request/getObjectIdsBasedOnOid",{oid:e})}getStatusCountBasedOnOid(e,t,n,i){let r=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getStatusCountBasedOnOid",{oid:e,objectIds:t,dataTypeArray:n,filterConfig:i,orgIds:r})}getHeaderCount(e){return this.http.post("/api/isa/request/getHeaderCount",{oid:e})}getRequestBasedOnStatus(e,t){return this.http.post("/api/isa/request/getRequestBasedOnStatus",{oid:e,status:t})}getDataTypeArray(){return this.http.post("/api/isa/request/getISAStatusMasterDataUDRF",{})}sendMsg(e){this.msg.next(e)}getMsg(){return this.msg.asObservable()}getTaskStatusColor(){return new Promise((e,t)=>{this.http.post("/api/isa/request/getTaskStatusNameColor",{}).subscribe(t=>(this.taskStatusColor=t,e(t)),e=>{})})}getAllRequestsForRmg(e){return this.http.post("/api/isa/request/getAllRequestsForRmg",{filterConfig:e})}getTotalForRmg(e){return new Promise((t,n)=>{this.http.post("/api/isa/request/getTotalForRmg",{filterConfig:e}).subscribe(e=>t(e),e=>{})})}getDefaultISAStatusToDisplay(){return this.http.post("/api/isa/request/getISADefaultStatusToDisplay",{})}getCTCbreakUpForSkillSet(e){return new Promise((t,n)=>{this.http.post("/api/isa/request/getCTCbreakUpForSkillSet",e).subscribe(e=>t(e.data),e=>{n(e)})})}getCurrentUserRole(){return this.roleService&&this.roleService.roles&&this.roleService.roles.length>0?this.roleService.roles[0].role_id:null}getTemplateForUser(e){return this.http.post("/api/isa/request/getVisibleFormFieldsForUser",e)}saveAndApproveRequest(e){return this.http.post("/api/isa/request/updBudgetInfoByRequestId",e)}getVisibilityMatrix(e){return this.http.post("/api/isa/request/getAuthorzedFieldsByRoleId",e)}getISAAttachmentFromS3(e){return this.http.post("/api/isa/attachment/getISAAttachmentFromS3",{key:e})}getSkillType(){return this.http.post("/api/isa/request/getSkillType",{})}getSkillLevel(){return this.http.post("/api/isa/request/getSkillLevel",{})}getSkillExperience(){return this.http.post("/api/isa/request/getSkillExperience",{})}getSkillName(e){return this.http.post("/api/isa/request/getSkillName",{skill_type_id:e})}getIsaActivityReportDownload(){return this.http.post("/api/isa/request/getIsaActivityReportDownload",{})}getISASLAreport(e){return this.http.post("/api/isa/request/getISASLAreport",{filterConfig:e})}getISACustomReport(e){return this.http.post("/api/isa/request/getISACustomReport",{filterConfig:e})}getPositionType(){return this.http.post("/api/employee360/masterData/getPositionType",{})}calculateNetCostBasedOnPosition(e,t,n,r,s,a,l){return Object(i.c)(this,void 0,void 0,(function*(){let i;i=a&&a.length>1&&(yield this.getManpowerCostByOId(a,n,s,2))||(yield this.getManpowerCostBasedOnPosition(e,t,n,s,l));let c=yield this.getNonManpowerCost(t,n,r,s,2),u=yield this.getAllocatedCost(),h=0;h=(i?i.cost:0)+c.length>0?o.reduce(o.pluck(c,"cost"),(e,t)=>e+t,0):0;let d=u.length>0?o.reduce(o.pluck(u,"percentage"),(e,t)=>e+t,0):0;return{cost:h,currency:i&&i.currency_code?i.currency_code:"",manpowerCost:i,nonManpowerCost:c,allocatedCost:u,allocatedCostValue:h*(d/100)}}))}getManpowerCostBasedOnPosition(e,t,n,i,r){return new Promise((o,s)=>{this.http.post("/api/isa/configuration/getManpowerCostBasedOnPosition",{skillRoleId:e,nationalityId:t,locationGroupId:n,unit:i,position:r}).subscribe(e=>o(e),e=>(console.log(e),s(e)))})}getNonManpowerCost(e,t,n,i,r){return new Promise((o,s)=>{this.http.post("/api/project/getNonManpowerCost",{nationalityId:e,locationGroupId:t,locationId:n,unit:i,currency_id:r}).subscribe(e=>o(e),e=>(console.log(e),s(e)))})}getAllocatedCost(){return new Promise((e,t)=>{this.http.post("/api/project/getAllocatedCost","").subscribe(t=>e(t),e=>(console.log(e),t(e)))})}getManpowerCostByOId(e,t,n,i){return new Promise((r,o)=>{this.http.post("/api/project/getEmployeeSalary",{oid:e,location_group_id:t,unit_id:n,currency_id:i}).subscribe(e=>r(e),e=>(console.log(e),o(e)))})}getSkillNameMaster(){return this.http.post("/api/isa/configuration/getSkillName",{})}getVendorOid(e,t){return this.http.post("/api/isa/request/getVendorOid",{vendorNames:e,docID:t})}getVendorList(e,t){return this.http.post("/api/isa/request/getVendorList",{documentID:e,limitval:t})}removeVendor(e,t){return this.http.post("/api/isa/request/removeVendors",{vendorOid:e,documentID:t})}checkVendorOrNot(){return this.http.post("/api/isa/request/checkVendorOrNot",{})}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275inject"](a.c),s["\u0275\u0275inject"](l.a),s["\u0275\u0275inject"](c.a),s["\u0275\u0275inject"](u.a))},e.\u0275prov=s["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},ucYs:function(e,t,n){"use strict";n.d(t,"a",(function(){return l}));var i=n("mrSG"),r=n("xG9w"),o=n("fXoL"),s=n("tk/3"),a=n("BVzC");let l=(()=>{class e{constructor(e,t){this.http=e,this.errorService=t,this.workflowStatusList=[],this.getWorkflowStatusList()}getWorkflowStatusList(){this.http.post("/api/wfPrimary/getWorkflowStatusList","").subscribe(e=>{this.workflowStatusList=e.data},e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server While Getting Workflow Status List",e&&e.params?e.params:e&&e.error?e.error.params:{})})}getWorkflowProperties(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/getWorkflowProperties",{applicationId:e}).subscribe(e=>t(e),e=>n(e))})}getWorkflowPropertiesByWorkflowId(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/getWorkflowPropertiesByWorkflowId",{workflowId:e}).subscribe(e=>t(e),e=>n(e))})}getApproversHierarchy(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/getApproversHierarchy",e).subscribe(e=>t(e),e=>n(e))})}createWorkflowItems(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/createWorkflowItems",e).subscribe(e=>t(e),e=>n(e))})}getWorkflowDetails(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/getWorkflowDetails",{workflowId:e}).subscribe(e=>t(e),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while getting workflow details",e&&e.params?e.params:e&&e.error?e.error.params:{})})})}formatApproversHierarchy(e,t){return Object(i.c)(this,void 0,void 0,(function*(){0==e.length&&t.cc0&&t.cc0.appr0&&t.cc0.appr0.length>0&&e.push([{approvalType:"I",purposeOfInsertingIntoArray:"Just to ensure that 'I' type data is used, if it exists"}]);for(let n=0;n<e.length;n++){let i=[],o=r.keys(t["cc"+n]);for(let r=0;r<o.length;r++)for(let s=0;s<t["cc"+n][o[r]].length;s++){let a={name:t["cc"+n][o[r]][s].DELEGATE_NAME,oid:t["cc"+n][o[r]][s].DELEGATE_OID,level:r+1,designation:t["cc"+n][o[r]][s].DELEGATE_DESIGNATION_NAME,is_delegated:t["cc"+n][o[r]][s].IS_DELEGATED,role:t["cc"+n][o[r]][s].DELEGATE_ROLE_NAME};if(1==t["cc"+n][o[r]][s].IS_DELEGATED&&(a.delegated_by={name:t["cc"+n][o[r]][s].APPROVER_NAME,oid:t["cc"+n][o[r]][s].APPROVER_OID,level:r+1,designation:t["cc"+n][o[r]][s].APPROVER_DESIGNATION_NAME}),i.push(a),n==e.length-1&&r==o.length-1&&s==t["cc"+n][o[r]].length-1)return i}}}))}storeComments(e,t,n){return new Promise((i,r)=>{this.http.post("/api/wfPrimary/updateComments",{workflowHeaderId:e,newComments:t,commentor:n}).subscribe(e=>i(e),e=>(this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server Error while Updating Workflow Comments",e&&e.params?e.params:e&&e.error?e.error.params:{}),r(e)))})}updateWorkflowItems(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/updateWorkflowItems",e).subscribe(e=>t(e),e=>(console.log(e),n(e)))})}formatApproversHierarchyForOpportunityApprovalActivity(e){return Object(i.c)(this,void 0,void 0,(function*(){for(let t=0;t<1;t++){let n=[],i=r.keys(e["cc"+t]);for(let r=0;r<i.length;r++)for(let o=0;o<e["cc"+t][i[r]].length;o++){let s={name:e["cc"+t][i[r]][o].DELEGATE_NAME,oid:e["cc"+t][i[r]][o].DELEGATE_OID,level:e["cc"+t][i[r]][o].APPROVAL_ORDER,designation:e["cc"+t][i[r]][o].DELEGATE_DESIGNATION_NAME,is_delegated:e["cc"+t][i[r]][o].IS_DELEGATED};if(1==e["cc"+t][i[r]][o].IS_DELEGATED&&(s.delegated_by={name:e["cc"+t][i[r]][o].APPROVER_NAME,oid:e["cc"+t][i[r]][o].APPROVER_OID,level:e["cc"+t][i[r]][o].APPROVAL_ORDER,designation:e["cc"+t][i[r]][o].APPROVER_DESIGNATION_NAME}),n.push(s),r==i.length-1&&o==e["cc"+t][i[r]].length-1)return n}}}))}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275inject"](s.c),o["\u0275\u0275inject"](a.a))},e.\u0275prov=o["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},w76M:function(e,t,n){"use strict";n.d(t,"a",(function(){return c})),n.d(t,"b",(function(){return u}));var i=n("jhN1"),r=n("fXoL"),o=n("oHs6"),s=n("PVOt"),a=n("6t9p");const l=["*"];let c=(()=>{let e=class extends s.b{constructor(e,t,n,i,r,o,s,a){super(e,t,n,i,s,a),this._watcherHelper=i,this._idh=r,this._createEventEmitters([{subscribe:"contentReady",emit:"onContentReady"},{subscribe:"disposing",emit:"onDisposing"},{subscribe:"hidden",emit:"onHidden"},{subscribe:"hiding",emit:"onHiding"},{subscribe:"initialized",emit:"onInitialized"},{subscribe:"optionChanged",emit:"onOptionChanged"},{subscribe:"resize",emit:"onResize"},{subscribe:"resizeEnd",emit:"onResizeEnd"},{subscribe:"resizeStart",emit:"onResizeStart"},{subscribe:"showing",emit:"onShowing"},{subscribe:"shown",emit:"onShown"},{subscribe:"titleRendered",emit:"onTitleRendered"},{emit:"accessKeyChange"},{emit:"animationChange"},{emit:"closeOnOutsideClickChange"},{emit:"containerChange"},{emit:"contentTemplateChange"},{emit:"deferRenderingChange"},{emit:"disabledChange"},{emit:"dragEnabledChange"},{emit:"elementAttrChange"},{emit:"focusStateEnabledChange"},{emit:"fullScreenChange"},{emit:"heightChange"},{emit:"hintChange"},{emit:"hoverStateEnabledChange"},{emit:"maxHeightChange"},{emit:"maxWidthChange"},{emit:"minHeightChange"},{emit:"minWidthChange"},{emit:"positionChange"},{emit:"resizeEnabledChange"},{emit:"rtlEnabledChange"},{emit:"shadingChange"},{emit:"shadingColorChange"},{emit:"showCloseButtonChange"},{emit:"showTitleChange"},{emit:"tabIndexChange"},{emit:"titleChange"},{emit:"titleTemplateChange"},{emit:"toolbarItemsChange"},{emit:"visibleChange"},{emit:"widthChange"}]),this._idh.setHost(this),o.setHost(this)}get accessKey(){return this._getOption("accessKey")}set accessKey(e){this._setOption("accessKey",e)}get animation(){return this._getOption("animation")}set animation(e){this._setOption("animation",e)}get closeOnOutsideClick(){return this._getOption("closeOnOutsideClick")}set closeOnOutsideClick(e){this._setOption("closeOnOutsideClick",e)}get container(){return this._getOption("container")}set container(e){this._setOption("container",e)}get contentTemplate(){return this._getOption("contentTemplate")}set contentTemplate(e){this._setOption("contentTemplate",e)}get deferRendering(){return this._getOption("deferRendering")}set deferRendering(e){this._setOption("deferRendering",e)}get disabled(){return this._getOption("disabled")}set disabled(e){this._setOption("disabled",e)}get dragEnabled(){return this._getOption("dragEnabled")}set dragEnabled(e){this._setOption("dragEnabled",e)}get elementAttr(){return this._getOption("elementAttr")}set elementAttr(e){this._setOption("elementAttr",e)}get focusStateEnabled(){return this._getOption("focusStateEnabled")}set focusStateEnabled(e){this._setOption("focusStateEnabled",e)}get fullScreen(){return this._getOption("fullScreen")}set fullScreen(e){this._setOption("fullScreen",e)}get height(){return this._getOption("height")}set height(e){this._setOption("height",e)}get hint(){return this._getOption("hint")}set hint(e){this._setOption("hint",e)}get hoverStateEnabled(){return this._getOption("hoverStateEnabled")}set hoverStateEnabled(e){this._setOption("hoverStateEnabled",e)}get maxHeight(){return this._getOption("maxHeight")}set maxHeight(e){this._setOption("maxHeight",e)}get maxWidth(){return this._getOption("maxWidth")}set maxWidth(e){this._setOption("maxWidth",e)}get minHeight(){return this._getOption("minHeight")}set minHeight(e){this._setOption("minHeight",e)}get minWidth(){return this._getOption("minWidth")}set minWidth(e){this._setOption("minWidth",e)}get position(){return this._getOption("position")}set position(e){this._setOption("position",e)}get resizeEnabled(){return this._getOption("resizeEnabled")}set resizeEnabled(e){this._setOption("resizeEnabled",e)}get rtlEnabled(){return this._getOption("rtlEnabled")}set rtlEnabled(e){this._setOption("rtlEnabled",e)}get shading(){return this._getOption("shading")}set shading(e){this._setOption("shading",e)}get shadingColor(){return this._getOption("shadingColor")}set shadingColor(e){this._setOption("shadingColor",e)}get showCloseButton(){return this._getOption("showCloseButton")}set showCloseButton(e){this._setOption("showCloseButton",e)}get showTitle(){return this._getOption("showTitle")}set showTitle(e){this._setOption("showTitle",e)}get tabIndex(){return this._getOption("tabIndex")}set tabIndex(e){this._setOption("tabIndex",e)}get title(){return this._getOption("title")}set title(e){this._setOption("title",e)}get titleTemplate(){return this._getOption("titleTemplate")}set titleTemplate(e){this._setOption("titleTemplate",e)}get toolbarItems(){return this._getOption("toolbarItems")}set toolbarItems(e){this._setOption("toolbarItems",e)}get visible(){return this._getOption("visible")}set visible(e){this._setOption("visible",e)}get width(){return this._getOption("width")}set width(e){this._setOption("width",e)}get toolbarItemsChildren(){return this._getOption("toolbarItems")}set toolbarItemsChildren(e){this.setChildren("toolbarItems",e)}_createInstance(e,t){return new o.a(e,t)}ngOnDestroy(){this._destroyWidget()}ngOnChanges(e){super.ngOnChanges(e),this.setupChanges("toolbarItems",e)}setupChanges(e,t){e in this._optionsToUpdate||this._idh.setup(e,t)}ngDoCheck(){this._idh.doCheck("toolbarItems"),this._watcherHelper.checkWatchers(),super.ngDoCheck(),super.clearChangedOptions()}_setOption(e,t){let n=this._idh.setupSingle(e,t),i=null!==this._idh.getChanges(e,t);(n||i)&&super._setOption(e,t)}};return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](r.ElementRef),r["\u0275\u0275directiveInject"](r.NgZone),r["\u0275\u0275directiveInject"](s.e),r["\u0275\u0275directiveInject"](s.j),r["\u0275\u0275directiveInject"](s.g),r["\u0275\u0275directiveInject"](s.i),r["\u0275\u0275directiveInject"](i.h),r["\u0275\u0275directiveInject"](r.PLATFORM_ID))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["dx-popup"]],contentQueries:function(e,t,n){if(1&e&&r["\u0275\u0275contentQuery"](n,a.L,!1),2&e){let e;r["\u0275\u0275queryRefresh"](e=r["\u0275\u0275loadQuery"]())&&(t.toolbarItemsChildren=e)}},inputs:{accessKey:"accessKey",animation:"animation",closeOnOutsideClick:"closeOnOutsideClick",container:"container",contentTemplate:"contentTemplate",deferRendering:"deferRendering",disabled:"disabled",dragEnabled:"dragEnabled",elementAttr:"elementAttr",focusStateEnabled:"focusStateEnabled",fullScreen:"fullScreen",height:"height",hint:"hint",hoverStateEnabled:"hoverStateEnabled",maxHeight:"maxHeight",maxWidth:"maxWidth",minHeight:"minHeight",minWidth:"minWidth",position:"position",resizeEnabled:"resizeEnabled",rtlEnabled:"rtlEnabled",shading:"shading",shadingColor:"shadingColor",showCloseButton:"showCloseButton",showTitle:"showTitle",tabIndex:"tabIndex",title:"title",titleTemplate:"titleTemplate",toolbarItems:"toolbarItems",visible:"visible",width:"width"},outputs:{onContentReady:"onContentReady",onDisposing:"onDisposing",onHidden:"onHidden",onHiding:"onHiding",onInitialized:"onInitialized",onOptionChanged:"onOptionChanged",onResize:"onResize",onResizeEnd:"onResizeEnd",onResizeStart:"onResizeStart",onShowing:"onShowing",onShown:"onShown",onTitleRendered:"onTitleRendered",accessKeyChange:"accessKeyChange",animationChange:"animationChange",closeOnOutsideClickChange:"closeOnOutsideClickChange",containerChange:"containerChange",contentTemplateChange:"contentTemplateChange",deferRenderingChange:"deferRenderingChange",disabledChange:"disabledChange",dragEnabledChange:"dragEnabledChange",elementAttrChange:"elementAttrChange",focusStateEnabledChange:"focusStateEnabledChange",fullScreenChange:"fullScreenChange",heightChange:"heightChange",hintChange:"hintChange",hoverStateEnabledChange:"hoverStateEnabledChange",maxHeightChange:"maxHeightChange",maxWidthChange:"maxWidthChange",minHeightChange:"minHeightChange",minWidthChange:"minWidthChange",positionChange:"positionChange",resizeEnabledChange:"resizeEnabledChange",rtlEnabledChange:"rtlEnabledChange",shadingChange:"shadingChange",shadingColorChange:"shadingColorChange",showCloseButtonChange:"showCloseButtonChange",showTitleChange:"showTitleChange",tabIndexChange:"tabIndexChange",titleChange:"titleChange",titleTemplateChange:"titleTemplateChange",toolbarItemsChange:"toolbarItemsChange",visibleChange:"visibleChange",widthChange:"widthChange"},features:[r["\u0275\u0275ProvidersFeature"]([s.e,s.j,s.i,s.g]),r["\u0275\u0275InheritDefinitionFeature"],r["\u0275\u0275NgOnChangesFeature"]],ngContentSelectors:l,decls:1,vars:0,template:function(e,t){1&e&&(r["\u0275\u0275projectionDef"](),r["\u0275\u0275projection"](0))},encapsulation:2}),e})(),u=(()=>{let e=class{};return e.\u0275mod=r["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=r["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[a.bb,a.Gc,a.Vd,a.vd,a.hb,a.lb,a.sb,a.id,a.jd,a.M,s.c,s.f,i.b],a.bb,a.Gc,a.Vd,a.vd,a.hb,a.lb,a.sb,a.id,a.jd,a.M,s.f]}),e})()}}]);