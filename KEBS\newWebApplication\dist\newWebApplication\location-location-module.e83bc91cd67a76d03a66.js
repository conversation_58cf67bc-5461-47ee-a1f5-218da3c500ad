(window.webpackJsonp=window.webpackJsonp||[]).push([[793],{"8Fxm":function(t,e,i){"use strict";i.r(e),i.d(e,"LocationModule",(function(){return j}));var s=i("ofXK"),r=i("tyNb"),n=i("mrSG"),a=i("1G5W"),o=i("XNiG"),c=i("fXoL"),h=i("rQiX"),d=i("XNFG"),l=i("XQl4"),g=i("0IaG"),u=i("IRv6"),m=i("e37m");function y(t,e){if(1&t){const t=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"div"),c["\u0275\u0275elementStart"](1,"app-settings-list-view",11),c["\u0275\u0275listener"]("tabSelected",(function(e){return c["\u0275\u0275restoreView"](t),c["\u0275\u0275nextContext"]().onTabSelected(e)}))("onSort",(function(e){return c["\u0275\u0275restoreView"](t),c["\u0275\u0275nextContext"]().onSort(e)}))("onClick",(function(e){return c["\u0275\u0275restoreView"](t),c["\u0275\u0275nextContext"]().onClick(e)}))("performCreation",(function(e){return c["\u0275\u0275restoreView"](t),c["\u0275\u0275nextContext"]().performCreation(e)}))("performUpdation",(function(e){return c["\u0275\u0275restoreView"](t),c["\u0275\u0275nextContext"]().performUpdation(e)}))("searchText",(function(e){return c["\u0275\u0275restoreView"](t),c["\u0275\u0275nextContext"]().onSearchApplied(e)}))("onDataScroll",(function(){return c["\u0275\u0275restoreView"](t),c["\u0275\u0275nextContext"]().onScrollApplied()})),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()}if(2&t){const t=c["\u0275\u0275nextContext"]();c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("tabsConfig",t.tabsConfig)("createActionName",t.createActionName)("editActionName",t.editActionName)("fieldConfigs",t.fieldConfigs)("dataValue",t.currentData)("formConfigs",t.formConfigs)("statusMaster",t.statusMaster)("countryMaster",t.countryMaster)("cityMaster",t.cityMaster)("stateMaster",t.stateMaster)("isFromMappingTab",t.isFromMappingTab)("previouslySearchedText",t.searchParams)("currentSelectedTab",t.currentTab)}}function f(t,e){if(1&t&&(c["\u0275\u0275elementStart"](0,"div",12),c["\u0275\u0275elementStart"](1,"div",13),c["\u0275\u0275element"](2,"img",14),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](3,"div",15),c["\u0275\u0275elementStart"](4,"div",16),c["\u0275\u0275text"](5,"Loading..."),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&t){const t=c["\u0275\u0275nextContext"]();c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("src",t.uiTextConfig["UI-LOADING-001"],c["\u0275\u0275sanitizeUrl"])}}const p=[{path:"",component:(()=>{class t{constructor(t,e,i,s){this._atsMasterService=t,this._toaster=e,this._atsSettingsService=i,this._dialog=s,this._onDestroy=new o.b,this.tabsConfig=[],this.currentData=[],this.isFromMappingTab=!1,this.statusMaster=[{id:1,name:"Active"},{id:2,name:"In Active"}],this.searchParams="",this.limit=15,this.skip=0}ngOnInit(){return Object(n.c)(this,void 0,void 0,(function*(){this.isDataLoading=!0,yield this.getMasterUIConfigurations("locationSettingsNavConfig"),yield this.calculateDynamicContentHeight(),yield this.getMasterUIConfigurations("countrySettings"),this.currentTab="countrySettings",yield this.getCountryDetails(),yield this.getCountryMaster(),yield this.getCityMaster(),this.isDataLoading=!1}))}onTabSelected(t){return Object(n.c)(this,void 0,void 0,(function*(){"stateSettings"!=this.currentTab&&"countrySettings"!=this.currentTab&&"regionSettings"!=this.currentTab&&"citySettings"!=this.currentTab||(this.isDataLoading=!0,yield this.getMasterUIConfigurations(t),this.currentTab=t,"stateSettings"==this.currentTab?(this.currentData=[],this.skip=0,this.sortParams=[],this.searchParams="",yield this.getStateDetails(),yield this.getCountryMaster()):"countrySettings"==this.currentTab?(this.currentData=[],this.skip=0,this.sortParams=[],this.searchParams="",yield this.getCountryDetails()):"regionSettings"==this.currentTab?(this.currentData=[],this.skip=0,this.sortParams=[],this.searchParams="",yield this.getCityMaster(),yield this.getRegionDetails()):"citySettings"==this.currentTab&&(this.currentData=[],this.skip=0,this.sortParams=[],this.searchParams="",yield this.getCityDetails(),yield this.getStateMaster(),yield this.getCountryMaster()),this.isDataLoading=!1)}))}getMasterUIConfigurations(t){return Object(n.c)(this,void 0,void 0,(function*(){return new Promise((e,i)=>this._atsMasterService.getAtsMasterUiConfig(t).pipe(Object(a.a)(this._onDestroy)).subscribe({next:i=>{0==i.err?(this.uiTextConfig=i.data.uiTextConfig,"locationSettingsNavConfig"==t?this.tabsConfig=i.data.fieldConfig:(this.fieldConfigs=i.data.fieldConfig,this.createActionName=i.data.createActionName,this.editActionName=i.data.editActionName,this.formConfigs=i.data.formConfigs)):this._toaster.showError("Error",i.msg,7e3),e(!0)},error:t=>{this._toaster.showError("Error",t.message?t.message:"ATS Master Data Retrieval Failed!",7e3),i()}}))}))}goBackToPreviousRoute(){history.back()}getStateDetails(){return Object(n.c)(this,void 0,void 0,(function*(){return new Promise((t,e)=>this._atsMasterService.getStateListForSettings(this.searchParams,this.sortParams,this.skip,this.limit).pipe(Object(a.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?this.currentData=[...this.currentData,...e.data]:this._toaster.showError("Error",e.msg,7e3),t(!0)},error:t=>{this._toaster.showError("Error",t.message?t.message:"ATS Master Data Retrieval Failed!",7e3),e()}}))}))}getCountryDetails(){return Object(n.c)(this,void 0,void 0,(function*(){return new Promise((t,e)=>this._atsMasterService.getCountryListForSettings(this.searchParams,this.sortParams,this.skip,this.limit).pipe(Object(a.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?this.currentData=[...this.currentData,...e.data]:this._toaster.showError("Error",e.msg,7e3),t(!0)},error:t=>{this._toaster.showError("Error",t.message?t.message:"ATS Master Data Retrieval Failed!",7e3),e()}}))}))}getRegionDetails(){return Object(n.c)(this,void 0,void 0,(function*(){return new Promise((t,e)=>this._atsMasterService.getRegionListForSettings(this.searchParams,this.sortParams,this.skip,this.limit).pipe(Object(a.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?this.currentData=[...this.currentData,...e.data]:this._toaster.showError("Error",e.msg,7e3),t(!0)},error:t=>{this._toaster.showError("Error",t.message?t.message:"ATS Master Data Retrieval Failed!",7e3),e()}}))}))}getCityDetails(){return Object(n.c)(this,void 0,void 0,(function*(){return new Promise((t,e)=>this._atsMasterService.getCityListForSettings(this.searchParams,this.sortParams,this.skip,this.limit).pipe(Object(a.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?this.currentData=[...this.currentData,...e.data]:this._toaster.showError("Error",e.msg,7e3),t(!0)},error:t=>{this._toaster.showError("Error",t.message?t.message:"ATS Master Data Retrieval Failed!",7e3),e()}}))}))}onSort(t){return Object(n.c)(this,void 0,void 0,(function*(){this.isDataLoading=!0;let e=this.fieldConfigs.find(e=>e.key==t.fieldConfigIndex.key),i=null==e?void 0:e.sortOrder,s=null==t?void 0:t.sortOrder;this.fieldConfigs.forEach(t=>{t.sortOrder=0}),this.sortParams=[],i==s?e.sortOrder=0:(e.sortOrder=s,this.sortParams.push(t)),"stateSettings"==this.currentTab?(this.currentData=[],this.skip=0,yield this.getStateDetails()):"countrySettings"==this.currentTab?(this.currentData=[],this.skip=0,yield this.getCountryDetails()):"regionSettings"==this.currentTab?(this.currentData=[],this.skip=0,yield this.getRegionDetails()):"citySettings"==this.currentTab&&(this.currentData=[],this.skip=0,yield this.getCityDetails()),this.isDataLoading=!1}))}onClick(t){return Object(n.c)(this,void 0,void 0,(function*(){t.functionName&&""!=t.functionName&&(yield this[t.functionName](t.data))}))}openCityViewComponent(t){return Object(n.c)(this,void 0,void 0,(function*(){const{TagsDialogComponent:e}=yield Promise.all([i.e(0),i.e(941)]).then(i.bind(null,"gaFh"));this._dialog.open(e,{width:"450px",data:{module:"",id:"",bulkIds:null,mode:"V",title:"Associated Cities",subTitle:t&&t.cities?(null==t?void 0:t.cities.length)+" city(s)":"0 city(s)",tags:(null==t?void 0:t.cities)&&Array.isArray(null==t?void 0:t.cities)?t.cities.filter(t=>null!==t.id):[],removeAccess:!1},disableClose:!0}).afterClosed().subscribe(t=>Object(n.c)(this,void 0,void 0,(function*(){})))}))}onSearchApplied(t){return Object(n.c)(this,void 0,void 0,(function*(){this.isDataLoading=!0,this.searchParams=t,"stateSettings"==this.currentTab?(this.currentData=[],this.skip=0,yield this.getStateDetails()):"countrySettings"==this.currentTab?(this.currentData=[],this.skip=0,yield this.getCountryDetails()):"regionSettings"==this.currentTab?(this.currentData=[],this.skip=0,yield this.getRegionDetails()):"citySettings"==this.currentTab&&(this.currentData=[],this.skip=0,yield this.getCityDetails()),this.isDataLoading=!1}))}getCountryMaster(){return Object(n.c)(this,void 0,void 0,(function*(){return new Promise((t,e)=>this._atsMasterService.getCountryMasterForSettings().pipe(Object(a.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?this.countryMaster=e.data:this._toaster.showError("Error",e.msg,7e3),t(!0)},error:t=>{this._toaster.showError("Error",t.message?t.message:"ATS Master Data Retrieval Failed!",7e3),e()}}))}))}getStateMaster(){return Object(n.c)(this,void 0,void 0,(function*(){return new Promise((t,e)=>this._atsMasterService.getStateMasterForSettings().pipe(Object(a.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?this.stateMaster=e.data:this._toaster.showError("Error",e.msg,7e3),t(!0)},error:t=>{this._toaster.showError("Error",t.message?t.message:"ATS Master Data Retrieval Failed!",7e3),e()}}))}))}getCityMaster(){return Object(n.c)(this,void 0,void 0,(function*(){return new Promise((t,e)=>this._atsMasterService.getCityMasterForSettings().pipe(Object(a.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?this.cityMaster=e.data:this._toaster.showError("Error",e.msg,7e3),t(!0)},error:t=>{this._toaster.showError("Error",t.message?t.message:"ATS Master Data Retrieval Failed!",7e3),e()}}))}))}onResize(){this.calculateDynamicContentHeight()}onScrollApplied(){return Object(n.c)(this,void 0,void 0,(function*(){"stateSettings"==this.currentTab?(this.skip+=15,this.getStateDetails()):"countrySettings"==this.currentTab?(this.skip+=15,this.getCountryDetails()):"citySettings"==this.currentTab&&(this.skip+=15,this.getCityDetails())}))}calculateDynamicContentHeight(){return Object(n.c)(this,void 0,void 0,(function*(){this.dynamicHeight=window.innerHeight-57-56+"px",document.documentElement.style.setProperty("--dynamicHeight",this.dynamicHeight),this.dynamicSubHeight=window.innerHeight-57-63-128-153+"px",document.documentElement.style.setProperty("--dynamicSubHeight",this.dynamicSubHeight),this.dynamicTabHeight=window.innerHeight-57-63-127+59+"px",document.documentElement.style.setProperty("--dynamicTabHeight",this.dynamicTabHeight)}))}performCreation(t){return Object(n.c)(this,void 0,void 0,(function*(){if("stateSettings"==this.currentTab){this.isDataLoading=!0;let e=this.countryMaster.find(e=>(null==e?void 0:e.id)==(null==t?void 0:t.associatedCountry)),i=null==e?void 0:e.code;return t.countryName=null==e?void 0:e.name,t.countryCode=i,new Promise((e,i)=>this._atsSettingsService.createStateDetails(t).pipe(Object(a.a)(this._onDestroy)).subscribe({next:t=>Object(n.c)(this,void 0,void 0,(function*(){0==t.err?(t.isDuplicate?this._toaster.showWarning("Warning \u26a0\ufe0f",t.msg,5e3):(this._toaster.showSuccess("Success","State Created Successfully!",3e3),this.currentData=[],this.skip=0,yield this.getStateDetails()),this.isDataLoading=!1):(this._toaster.showError("Error",t.msg,7e3),this.isDataLoading=!1),e(!0)})),error:t=>{this.isDataLoading=!1,this._toaster.showError("Error",t.message?t.message:"Could not create state!",7e3),i()}}))}if("countrySettings"==this.currentTab)return this.isDataLoading=!0,new Promise((e,i)=>this._atsSettingsService.createCountryDetails(t).pipe(Object(a.a)(this._onDestroy)).subscribe({next:t=>Object(n.c)(this,void 0,void 0,(function*(){0==t.err?(t.isDuplicate?this._toaster.showWarning("Warning \u26a0\ufe0f",t.msg,5e3):(this._toaster.showSuccess("Success","Country Created Successfully!",3e3),this.currentData=[],this.skip=0,yield this.getCountryDetails()),this.isDataLoading=!1):(this._toaster.showError("Error",t.msg,7e3),this.isDataLoading=!1),e(!0)})),error:t=>{this.isDataLoading=!1,this._toaster.showError("Error",t.message?t.message:"Could not create country!",7e3),i()}}));if("regionSettings"==this.currentTab)return this.isDataLoading=!0,new Promise((e,i)=>this._atsSettingsService.createRegionDetails(t).pipe(Object(a.a)(this._onDestroy)).subscribe({next:t=>Object(n.c)(this,void 0,void 0,(function*(){0==t.err?(t.isDuplicate?this._toaster.showWarning("Warning \u26a0\ufe0f",t.msg,5e3):(this._toaster.showSuccess("Success","Region Created Successfully!",3e3),this.currentData=[],this.skip=0,yield this.getRegionDetails()),this.isDataLoading=!1):(this._toaster.showError("Error",t.msg,7e3),this.isDataLoading=!1),e(!0)})),error:t=>{this.isDataLoading=!1,this._toaster.showError("Error",t.message?t.message:"Could not create region!",7e3),i()}}));if("citySettings"==this.currentTab){this.isDataLoading=!0;let e=this.countryMaster.find(e=>(null==e?void 0:e.id)==(null==t?void 0:t.associatedCountry)),i=null==e?void 0:e.code;t.countryName=null==e?void 0:e.name,t.countryCode=i;let s=this.stateMaster.find(e=>(null==e?void 0:e.id)==(null==t?void 0:t.associatedState)),r=null==s?void 0:s.state_code;return t.stateName=null==s?void 0:s.name,t.stateCode=r,new Promise((e,i)=>this._atsSettingsService.createCityDetails(t).pipe(Object(a.a)(this._onDestroy)).subscribe({next:t=>Object(n.c)(this,void 0,void 0,(function*(){0==t.err?(t.isDuplicate?this._toaster.showWarning("Warning \u26a0\ufe0f",t.msg,5e3):(this._toaster.showSuccess("Success","City Created Successfully!",3e3),this.currentData=[],this.skip=0,yield this.getCityDetails()),this.isDataLoading=!1):(this._toaster.showError("Error",t.msg,7e3),this.isDataLoading=!1),e(!0)})),error:t=>{this.isDataLoading=!1,this._toaster.showError("Error",t.message?t.message:"Could not create city!",7e3),i()}}))}}))}performUpdation(t){return Object(n.c)(this,void 0,void 0,(function*(){if("stateSettings"==this.currentTab){this.isDataLoading=!0;let e=this.countryMaster.find(e=>(null==e?void 0:e.id)==(null==t?void 0:t.associatedCountry)),i=null==e?void 0:e.code;return t.countryName=null==e?void 0:e.name,t.countryCode=i,new Promise((e,i)=>this._atsSettingsService.updateStateDetails(t).pipe(Object(a.a)(this._onDestroy)).subscribe({next:t=>Object(n.c)(this,void 0,void 0,(function*(){0==t.err?(t.isDuplicate?this._toaster.showWarning("Warning \u26a0\ufe0f",t.msg,5e3):(this._toaster.showSuccess("Success","State Updated Successfully!",3e3),this.currentData=[],this.skip=0,yield this.getStateDetails()),this.isDataLoading=!1):(this._toaster.showError("Error",t.msg,7e3),this.isDataLoading=!1),e(!0)})),error:t=>{this._toaster.showError("Error",t.message?t.message:"Could not update state details!",7e3),i()}}))}if("countrySettings"==this.currentTab)return this.isDataLoading=!0,new Promise((e,i)=>this._atsSettingsService.updateCountryDetails(t).pipe(Object(a.a)(this._onDestroy)).subscribe({next:t=>Object(n.c)(this,void 0,void 0,(function*(){0==t.err?(t.isDuplicate?this._toaster.showWarning("Warning \u26a0\ufe0f",t.msg,5e3):(this._toaster.showSuccess("Success","Country Updated Successfully!",3e3),this.currentData=[],this.skip=0,yield this.getCountryDetails()),this.isDataLoading=!1):(this._toaster.showError("Error",t.msg,7e3),this.isDataLoading=!1),e(!0)})),error:t=>{this._toaster.showError("Error",t.message?t.message:"Could not update country details!",7e3),i()}}));if("regionSettings"==this.currentTab)return this.isDataLoading=!0,new Promise((e,i)=>this._atsSettingsService.updateRegionDetails(t).pipe(Object(a.a)(this._onDestroy)).subscribe({next:t=>Object(n.c)(this,void 0,void 0,(function*(){0==t.err?(t.isDuplicate?this._toaster.showWarning("Warning \u26a0\ufe0f",t.msg,5e3):(this._toaster.showSuccess("Success","Region Updated Successfully!",3e3),this.currentData=[],this.skip=0,yield this.getRegionDetails()),this.isDataLoading=!1):(this._toaster.showError("Error",t.msg,7e3),this.isDataLoading=!1),e(!0)})),error:t=>{this._toaster.showError("Error",t.message?t.message:"Could not update country details!",7e3),i()}}));if("citySettings"==this.currentTab){this.isDataLoading=!0;let e=this.countryMaster.find(e=>(null==e?void 0:e.id)==(null==t?void 0:t.associatedCountry)),i=null==e?void 0:e.code;t.countryName=null==e?void 0:e.name,t.countryCode=i;let s=this.stateMaster.find(e=>(null==e?void 0:e.id)==(null==t?void 0:t.associatedState)),r=null==s?void 0:s.state_code;return t.stateName=null==s?void 0:s.name,t.stateCode=r,new Promise((e,i)=>this._atsSettingsService.updateCityDetails(t).pipe(Object(a.a)(this._onDestroy)).subscribe({next:t=>Object(n.c)(this,void 0,void 0,(function*(){0==t.err?(t.isDuplicate?this._toaster.showWarning("Warning \u26a0\ufe0f",t.msg,5e3):(this._toaster.showSuccess("Success","City Updated Successfully!",3e3),this.currentData=[],this.skip=0,yield this.getCityDetails()),this.isDataLoading=!1):(this._toaster.showError("Error",t.msg,7e3),this.isDataLoading=!1),e(!0)})),error:t=>{this._toaster.showError("Error",t.message?t.message:"Could not update city details!",7e3),i()}}))}}))}}return t.\u0275fac=function(e){return new(e||t)(c["\u0275\u0275directiveInject"](h.a),c["\u0275\u0275directiveInject"](d.a),c["\u0275\u0275directiveInject"](l.a),c["\u0275\u0275directiveInject"](g.b))},t.\u0275cmp=c["\u0275\u0275defineComponent"]({type:t,selectors:[["app-landing-page"]],hostBindings:function(t,e){1&t&&c["\u0275\u0275listener"]("resize",(function(){return e.onResize()}),!1,c["\u0275\u0275resolveWindow"])},decls:14,vars:2,consts:[[1,"overall-container"],[1,"heading-content"],[3,"click"],["width","16","height","16","viewBox","0 0 16 16","fill","none",1,"arrow-svg"],["d","M6.00065 14.6668H10.0007C13.334 14.6668 14.6673 13.3335 14.6673 10.0002V6.00016C14.6673 2.66683 13.334 1.3335 10.0007 1.3335H6.00065C2.66732 1.3335 1.33398 2.66683 1.33398 6.00016V10.0002C1.33398 13.3335 2.66732 14.6668 6.00065 14.6668Z","stroke","#526179","stroke-width","0.8","stroke-linecap","round","stroke-linejoin","round"],["d","M8.84081 10.3532L6.49414 7.99982L8.84081 5.64648","stroke","#526179","stroke-width","0.8","stroke-linecap","round","stroke-linejoin","round"],[1,"location-setting-lines"],[1,"location-settings-title"],[1,"location-settings-subtitle"],[4,"ngIf"],["class","d-flex flex-column align-items-center justify-content-center list-view-loading-container",4,"ngIf"],[3,"tabsConfig","createActionName","editActionName","fieldConfigs","dataValue","formConfigs","statusMaster","countryMaster","cityMaster","stateMaster","isFromMappingTab","previouslySearchedText","currentSelectedTab","tabSelected","onSort","onClick","performCreation","performUpdation","searchText","onDataScroll"],[1,"d-flex","flex-column","align-items-center","justify-content-center","list-view-loading-container"],[2,"max-width","60px","max-height","60px"],[2,"max-width","100%","max-height","100%",3,"src"],[1,"loading-wrapper"],[1,"loading"]],template:function(t,e){1&t&&(c["\u0275\u0275element"](0,"app-setting-header-overall"),c["\u0275\u0275elementStart"](1,"div",0),c["\u0275\u0275elementStart"](2,"div",1),c["\u0275\u0275elementStart"](3,"div",2),c["\u0275\u0275listener"]("click",(function(){return e.goBackToPreviousRoute()})),c["\u0275\u0275namespaceSVG"](),c["\u0275\u0275elementStart"](4,"svg",3),c["\u0275\u0275element"](5,"path",4),c["\u0275\u0275element"](6,"path",5),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275namespaceHTML"](),c["\u0275\u0275elementStart"](7,"div",6),c["\u0275\u0275elementStart"](8,"div",7),c["\u0275\u0275text"](9,"Location Setting"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](10,"div",8),c["\u0275\u0275text"](11," Streamline location settings for seamless management and customization. "),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275template"](12,y,2,13,"div",9),c["\u0275\u0275template"](13,f,6,1,"div",10),c["\u0275\u0275elementEnd"]()),2&t&&(c["\u0275\u0275advance"](12),c["\u0275\u0275property"]("ngIf",!e.isDataLoading&&e.tabsConfig&&e.tabsConfig.length>0&&("stateSettings"==e.currentTab?e.countryMaster&&e.countryMaster.length>0:1)&&("countrySettings"==e.currentTab?e.countryMaster&&e.countryMaster.length>0:1)&&("citySettings"==e.currentTab?e.countryMaster&&e.countryMaster.length>0&&e.stateMaster&&e.stateMaster.length>0:1)),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",e.isDataLoading&&e.uiTextConfig))},directives:[u.a,s.NgIf,m.a],styles:['.overall-container[_ngcontent-%COMP%]{padding:15px;background:#f1f3f8;height:var(--dynamicHeight);overflow:hidden}.overall-container[_ngcontent-%COMP%]   .heading-content[_ngcontent-%COMP%]{display:flex;padding:15px;background:#f9fafc}.overall-container[_ngcontent-%COMP%]   .heading-content[_ngcontent-%COMP%]   .arrow-svg[_ngcontent-%COMP%]{margin-top:2px;cursor:pointer}.overall-container[_ngcontent-%COMP%]   .heading-content[_ngcontent-%COMP%]   .location-setting-lines[_ngcontent-%COMP%]{margin-left:10px}.overall-container[_ngcontent-%COMP%]   .heading-content[_ngcontent-%COMP%]   .location-setting-lines[_ngcontent-%COMP%]   .location-settings-title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:16px;font-weight:700;line-height:20.83px;text-align:left;color:#111434;width:-moz-fit-content;width:fit-content;height:21px}.overall-container[_ngcontent-%COMP%]   .heading-content[_ngcontent-%COMP%]   .location-setting-lines[_ngcontent-%COMP%]   .location-settings-subtitle[_ngcontent-%COMP%]{color:#8b95a5;font-family:var(--atsfontFamily);font-size:11px;font-weight:400;line-height:16px;letter-spacing:.02em;text-align:left;width:-moz-fit-content;width:fit-content;height:16px}.overall-container[_ngcontent-%COMP%]   .list-view-loading-container[_ngcontent-%COMP%]{height:var(--dynamicTabHeight);background-color:#fff}.overall-container[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]{background-color:#fff;overflow:hidden;height:var(--dynamicHeight)}.overall-container[_ngcontent-%COMP%]   .loading-wrapper[_ngcontent-%COMP%]{display:flex;vertical-align:center;justify-content:center;align-items:center}.overall-container[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]{color:rgba(0,0,0,.3);font-size:16px}.overall-container[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]:before{content:"Loading...";position:absolute;overflow:hidden;max-width:7em;white-space:nowrap;background:linear-gradient(270deg,var(--atsprimaryColor),var(--atssecondaryColor) 105.29%);background-clip:text;-webkit-background-clip:text;-webkit-text-fill-color:transparent;animation:loading 6s linear infinite}@keyframes loading{0%{max-width:0}}@keyframes gradientLoading{0%{background-position:200% 0}to{background-position:-200% 0}}']}),t})()}];let v=(()=>{class t{}return t.\u0275mod=c["\u0275\u0275defineNgModule"]({type:t}),t.\u0275inj=c["\u0275\u0275defineInjector"]({factory:function(e){return new(e||t)},imports:[[r.k.forChild(p)],r.k]}),t})();var b=i("Xi0T"),S=i("rDax"),C=i("kmnG"),D=i("1+mW"),w=i("vxfF"),_=i("3Pt+"),M=i("qFsG"),x=i("Qu3c"),E=i("d3UM"),O=i("wZkO"),P=i("f0Cb"),k=i("NFeN"),T=i("bSwM");let j=(()=>{class t{}return t.\u0275mod=c["\u0275\u0275defineNgModule"]({type:t}),t.\u0275inj=c["\u0275\u0275defineInjector"]({factory:function(e){return new(e||t)},imports:[[s.CommonModule,v,_.E,b.a,S.h,C.e,w.g,_.p,M.c,x.b,E.d,O.g,O.g,P.b,k.b,D.ApplicantTrackingSystemModule,T.b]]}),t})()}}]);