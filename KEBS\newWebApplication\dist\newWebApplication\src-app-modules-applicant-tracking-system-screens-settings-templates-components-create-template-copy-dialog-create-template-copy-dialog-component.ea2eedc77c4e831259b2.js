(window.webpackJsonp=window.webpackJsonp||[]).push([[929,634,858],{"1q9G":function(t,e,n){"use strict";n.r(e),n.d(e,"CreateTemplateCopyDialogComponent",(function(){return O}));var o=n("mrSG"),i=n("3Pt+"),r=n("1G5W"),a=n("XNiG"),c=n("fXoL"),l=n("0IaG"),s=n("XNFG"),m=n("XQl4"),f=n("URR/"),g=n("kmnG"),d=n("ofXK"),p=n("qFsG"),h=n("L9uM");function u(t,e){}function C(t,e){}function b(t,e){1&t&&(c["\u0275\u0275elementStart"](0,"span",14),c["\u0275\u0275text"](1,"*"),c["\u0275\u0275elementEnd"]())}let O=(()=>{class t{constructor(t,e,n,o,i){this.formBuilder=t,this.dialogRef=e,this._toaster=n,this._atsSettingsService=o,this._atsTemplateSettingsService=i,this._onDestroy=new a.b,this.masterlist=[]}ngOnInit(){this.form=this.formBuilder.group({templateName:["",i.H.required],template:["",i.H.required]}),this.fetchAllEmailTemplates()}onSubmit(){this.form.valid?this.dialogRef.close(this.form.value):(this.markFormGroupTouched(this.form),this._toaster.showError("Error","Please Fill all the Mandatory Fields",7e3))}onNoClick(){this.dialogRef.close()}markFormGroupTouched(t){Object.values(t.controls).forEach(t=>{t.markAsTouched(),t instanceof i.m&&this.markFormGroupTouched(t)})}fetchAllEmailTemplates(){return Object(o.c)(this,void 0,void 0,(function*(){return new Promise((t,e)=>this._atsTemplateSettingsService.fetchAllEmailTemplates(null,null,null).pipe(Object(r.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?this.masterlist=e.data:this._toaster.showError("Error",e.msg,7e3),t(!0)},error:t=>{this._toaster.showError("Error",t.message?t.message:"ATS Master Data Retrieval Failed!",7e3),e()}}))}))}}return t.\u0275fac=function(e){return new(e||t)(c["\u0275\u0275directiveInject"](i.i),c["\u0275\u0275directiveInject"](l.h),c["\u0275\u0275directiveInject"](s.a),c["\u0275\u0275directiveInject"](m.a),c["\u0275\u0275directiveInject"](f.a))},t.\u0275cmp=c["\u0275\u0275defineComponent"]({type:t,selectors:[["app-create-template-copy-dialog"]],decls:25,vars:5,consts:[[1,"bg-container"],[1,"d-flex","flex-column","duplicate-dialog"],["mat-dialog-title","",1,"form-title"],[1,"form",3,"formGroup"],[1,"form-lable"],[3,"ngTemplateOutlet"],["appearance","outline",1,"form-field-class-wo-width",2,"width","100%"],["type","text","maxlength","255","matInput","","placeholder","Type Here","formControlName","templateName"],[1,"field-description"],["placeholder","Select Existing Template","formControlName","template",1,"form-field-class",3,"list","hideMatLabel"],[1,"d-flex","flex-row-reverse",2,"padding","2px","gap","22px"],["type","submit",1,"submit-button",3,"click"],[1,"cancel-button",3,"click"],["mandatoryTemplate",""],[2,"color","#cf0001"]],template:function(t,e){if(1&t&&(c["\u0275\u0275elementStart"](0,"div",0),c["\u0275\u0275elementStart"](1,"div",1),c["\u0275\u0275elementStart"](2,"span",2),c["\u0275\u0275text"](3,"Duplicate from an existing template"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](4,"form",3),c["\u0275\u0275elementStart"](5,"mat-label",4),c["\u0275\u0275text"](6," Template Name "),c["\u0275\u0275template"](7,u,0,0,"ng-template",5),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](8,"mat-form-field",6),c["\u0275\u0275element"](9,"input",7),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](10,"div",8),c["\u0275\u0275text"](11,"Enter The Template Name Here"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](12,"mat-label",4),c["\u0275\u0275text"](13," Choose Template "),c["\u0275\u0275template"](14,C,0,0,"ng-template",5),c["\u0275\u0275elementEnd"](),c["\u0275\u0275element"](15,"app-input-search-single-select",9),c["\u0275\u0275elementStart"](16,"div",8),c["\u0275\u0275text"](17,"Select Template to go with creation"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](18,"div",10),c["\u0275\u0275elementStart"](19,"div",11),c["\u0275\u0275listener"]("click",(function(){return e.onSubmit()})),c["\u0275\u0275text"](20," Create "),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](21,"div",12),c["\u0275\u0275listener"]("click",(function(){return e.onNoClick()})),c["\u0275\u0275text"](22,"Cancel"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275template"](23,b,2,0,"ng-template",null,13,c["\u0275\u0275templateRefExtractor"])),2&t){const t=c["\u0275\u0275reference"](24);c["\u0275\u0275advance"](4),c["\u0275\u0275property"]("formGroup",e.form),c["\u0275\u0275advance"](3),c["\u0275\u0275property"]("ngTemplateOutlet",t),c["\u0275\u0275advance"](7),c["\u0275\u0275property"]("ngTemplateOutlet",t),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("list",e.masterlist)("hideMatLabel",!0)}},directives:[i.J,i.w,i.n,g.g,d.NgTemplateOutlet,g.c,p.b,i.e,i.q,i.v,i.l,h.a],styles:[".bg-container[_ngcontent-%COMP%]{padding:15px}.bg-container[_ngcontent-%COMP%]   .form-title[_ngcontent-%COMP%]{font-size:16px;font-weight:700;letter-spacing:2%;line-height:24px;font-family:var(--atsfontFamily);padding-bottom:12px}.bg-container[_ngcontent-%COMP%]     .mat-form-field-wrapper{margin-bottom:-7px!important}.bg-container[_ngcontent-%COMP%]   .field-description[_ngcontent-%COMP%]{font-weight:400;font-size:10px;color:#b9c0ca;font-family:var(--atsfontFamily);margin-bottom:14px}.bg-container[_ngcontent-%COMP%]   .form-lable[_ngcontent-%COMP%]{font-weight:500;font-size:12px;font-family:var(--atsfontFamily);color:#6e7b8f}.bg-container[_ngcontent-%COMP%]   .cancel-button[_ngcontent-%COMP%]{color:#45546e;padding:8px 12px;border-radius:8px;border:1px solid #111434;background:#fff}.bg-container[_ngcontent-%COMP%]   .cancel-button[_ngcontent-%COMP%], .bg-container[_ngcontent-%COMP%]   .submit-button[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;font-family:var(--atsfontFamily);font-weight:700;cursor:pointer;font-size:14px}.bg-container[_ngcontent-%COMP%]   .submit-button[_ngcontent-%COMP%]{color:#fff;padding:10px 14px;border-radius:8px;background-color:var(--atsprimaryColor)}.bg-container[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]{width:15%;background-color:#fff;margin-right:2%;max-height:var(--dynamicSubHeight);padding-top:16px;padding-bottom:16px;overflow-y:scroll}.bg-container[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .section-highlight[_ngcontent-%COMP%]{background-color:var(--atssecondaryColor4)}.bg-container[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .align-items-column[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:flex-start}.bg-container[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .align-section-center[_ngcontent-%COMP%]{display:flex;align-items:center;gap:10px;height:32px;border-radius:4px;padding-left:10%;padding-right:10%;cursor:pointer;width:-webkit-fill-available}.bg-container[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .align-section-center[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{width:8px;height:8px;border-radius:50%;background-color:#d9d9d9}.bg-container[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .align-section-center[_ngcontent-%COMP%]   .section-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:12px;font-weight:500;color:#6e7b8f;width:85%;padding-right:2%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.bg-container[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]{color:#dadce2;height:20px;padding-left:10%}.bg-container[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background-color:initial!important}.bg-container[_ngcontent-%COMP%]   .form-section-fields[_ngcontent-%COMP%]{padding:16px;width:83%;background-color:#fff;max-height:var(--dynamicSubHeight);overflow-y:scroll}.bg-container[_ngcontent-%COMP%]   .form-section-fields[_ngcontent-%COMP%]   .form-title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:16px;font-weight:500}.bg-container[_ngcontent-%COMP%]   .form-section-fields[_ngcontent-%COMP%]   .orange-sub-title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:500;color:#f27a6c}.bg-container[_ngcontent-%COMP%]   .form-section-fields[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;width:100%}.bg-container[_ngcontent-%COMP%]   .form-section-fields[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%]{color:#5f6c81}.bg-container[_ngcontent-%COMP%]   .form-section-fields[_ngcontent-%COMP%]   .form-description[_ngcontent-%COMP%], .bg-container[_ngcontent-%COMP%]   .form-section-fields[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:11px;font-weight:400;margin:0;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;width:90%}.bg-container[_ngcontent-%COMP%]   .form-section-fields[_ngcontent-%COMP%]   .form-description[_ngcontent-%COMP%]{color:#b9c0ca}.bg-container[_ngcontent-%COMP%]   .form-section-fields[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]{width:90%;font-size:12px;font-family:var(--atsfontFamily)}.bg-container[_ngcontent-%COMP%]   .form-section-fields[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-form-field{width:90%!important;font-size:12px!important;font-family:var(--atsfontFamily)!important}.bg-container[_ngcontent-%COMP%]   .form-section-fields[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-form-field-wrapper, .bg-container[_ngcontent-%COMP%]   .form-section-fields[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-wrapper{padding-bottom:0!important}.bg-container[_ngcontent-%COMP%]   .form-section-fields[_ngcontent-%COMP%]   .app-input-search-field-highlight[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-outline, .bg-container[_ngcontent-%COMP%]   .form-section-fields[_ngcontent-%COMP%]   .app-input-search-field-highlight[_ngcontent-%COMP%]     .mat-form-field-appearance-outline:hover .mat-form-field-outline{color:#d63031!important;border:1px solid #d63031!important;border-radius:5px!important}.bg-container[_ngcontent-%COMP%]   .form-section-fields[_ngcontent-%COMP%]     .dx-htmleditor.dx-htmleditor-outlined{box-shadow:none!important}"]}),t})()},NJ67:function(t,e,n){"use strict";n.d(e,"a",(function(){return o}));class o{constructor(){this.disabled=!1}onChange(t){}onTouched(t){}writeValue(t){this.value=t}registerOnChange(t){this.onChange=t}registerOnTouched(t){this.onTouched=t}setDisabledState(t){this.disabled=t}}}}]);