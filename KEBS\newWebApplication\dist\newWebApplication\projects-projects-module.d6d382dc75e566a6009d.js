(window.webpackJsonp=window.webpackJsonp||[]).push([[856],{l1Fo:function(e,t,n){"use strict";n.r(t),n.d(t,"ProjectsModule",(function(){return i}));var o=n("ofXK"),l=n("tyNb"),r=n("Vym5"),a=n("fXoL");const d=[{path:"",component:(()=>{class e{constructor(){}ngOnInit(){}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=a["\u0275\u0275defineComponent"]({type:e,selectors:[["app-projects"]],decls:1,vars:0,template:function(e,t){1&e&&a["\u0275\u0275element"](0,"router-outlet")},directives:[l.l],styles:[""]}),e})(),children:[{path:"",redirectTo:"project-list",pathMatch:"full"},{path:"create/:projectId",loadChildren:()=>Promise.all([n.e(2),n.e(3),n.e(4),n.e(5),n.e(6),n.e(7),n.e(8),n.e(20),n.e(21),n.e(23),n.e(26),n.e(30),n.e(36),n.e(38),n.e(42),n.e(45),n.e(46),n.e(55),n.e(58),n.e(62),n.e(85),n.e(84),n.e(103),n.e(92),n.e(102),n.e(0),n.e(414)]).then(n.bind(null,"tDXQ")).then(e=>e.CreateProjectModule),resolve:{projectData:r.a}},{path:"project-list",loadChildren:()=>Promise.all([n.e(1),n.e(2),n.e(3),n.e(5),n.e(6),n.e(7),n.e(8),n.e(9),n.e(10),n.e(11),n.e(12),n.e(16),n.e(17),n.e(20),n.e(21),n.e(23),n.e(37),n.e(55),n.e(84),n.e(0),n.e(603)]).then(n.bind(null,"v318")).then(e=>e.ProjectListModule),resolve:{projectList:r.a}},{path:":projectId/:label",children:[{path:"",loadChildren:()=>Promise.all([n.e(2),n.e(3),n.e(5),n.e(6),n.e(7),n.e(8),n.e(20),n.e(21),n.e(37),n.e(84),n.e(102),n.e(163),n.e(0),n.e(599)]).then(n.bind(null,"5Y/C")).then(e=>e.ProjectHeaderModule),resolve:{projectData:r.a}},{path:":itemIndex/:label/:itemId",children:[{path:"",loadChildren:()=>Promise.all([n.e(1),n.e(2),n.e(3),n.e(4),n.e(5),n.e(6),n.e(7),n.e(8),n.e(9),n.e(10),n.e(11),n.e(12),n.e(16),n.e(17),n.e(18),n.e(20),n.e(19),n.e(21),n.e(23),n.e(26),n.e(30),n.e(32),n.e(37),n.e(36),n.e(38),n.e(42),n.e(45),n.e(46),n.e(55),n.e(58),n.e(62),n.e(85),n.e(84),n.e(103),n.e(102),n.e(157),n.e(139),n.e(163),n.e(0),n.e(602)]).then(n.bind(null,"tSp0")).then(e=>e.ProjectItemModule),resolve:{projectData:r.a}},{path:":waveIndex/:label/:waveId",loadChildren:()=>Promise.all([n.e(1),n.e(2),n.e(3),n.e(4),n.e(5),n.e(6),n.e(7),n.e(8),n.e(9),n.e(10),n.e(11),n.e(12),n.e(16),n.e(17),n.e(18),n.e(20),n.e(19),n.e(21),n.e(23),n.e(26),n.e(30),n.e(32),n.e(37),n.e(36),n.e(38),n.e(42),n.e(45),n.e(46),n.e(55),n.e(58),n.e(62),n.e(85),n.e(84),n.e(103),n.e(102),n.e(157),n.e(139),n.e(163),n.e(0),n.e(602)]).then(n.bind(null,"tSp0")).then(e=>e.ProjectItemModule),resolve:{projectData:r.a}}]}]}]}];let c=(()=>{class e{}return e.\u0275mod=a["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=a["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[l.k.forChild(d)],l.k]}),e})(),i=(()=>{class e{}return e.\u0275mod=a["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=a["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[o.CommonModule,c]]}),e})()}}]);