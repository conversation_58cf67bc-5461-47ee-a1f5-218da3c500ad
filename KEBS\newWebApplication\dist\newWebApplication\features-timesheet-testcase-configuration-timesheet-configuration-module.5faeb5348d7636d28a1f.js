(window.webpackJsonp=window.webpackJsonp||[]).push([[681,765,821,822,861,981,983,987,990,991],{hJL4:function(e,t,n){"use strict";n.d(t,"a",(function(){return c}));var i=n("mrSG"),r=n("XNiG"),s=n("xG9w"),o=n("fXoL"),a=n("tk/3"),l=n("LcQX"),d=n("XXEo"),h=n("flaP");let c=(()=>{class e{constructor(e,t,n,i){this.http=e,this.UtilityService=t,this.loginService=n,this.roleService=i,this.msg=new r.b,this.taskStatusColor=[],this.approveRequest=(e,t)=>this.http.post("/api/isa/request/approveResourceRequest",{requestDetails:e,approverOid:t}),this.rejectRequest=(e,t)=>this.http.post("/api/isa/request/rejectResourceRequest",{requestDetails:e,approverOid:t}),this.getTaskStatusColor()}getAllRequestsOfUser(e,t,n,i,r,s,o){let a=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getAllRequestsOfUser",{oid:e,statusId:t,statusCode:n,objectIds:i,skip:r,limit:s,filterConfig:o,orgIds:a})}getAllRoleAccess(){return s.where(this.roleService.roles,{application_id:139})}getRequestsBasedOnStatus(e,t,n,i,r,s,o){let a=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getRequestsBasedOnStatus",{oid:e,statusId:t,statusCode:n,objectIds:i,skip:r,limit:s,filterConfig:o,orgIds:a})}getRequestsForAwaitingApproval(e,t,n,i){return this.http.post("/api/isa/request/getRequestsForAwaitingApproval",{oid:e,skip:t,limit:n,filterConfig:i})}getObjectIdsBasedOnOid(e){return this.http.post("/api/isa/request/getObjectIdsBasedOnOid",{oid:e})}getStatusCountBasedOnOid(e,t,n,i){let r=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getStatusCountBasedOnOid",{oid:e,objectIds:t,dataTypeArray:n,filterConfig:i,orgIds:r})}getHeaderCount(e){return this.http.post("/api/isa/request/getHeaderCount",{oid:e})}getRequestBasedOnStatus(e,t){return this.http.post("/api/isa/request/getRequestBasedOnStatus",{oid:e,status:t})}getDataTypeArray(){return this.http.post("/api/isa/request/getISAStatusMasterDataUDRF",{})}sendMsg(e){this.msg.next(e)}getMsg(){return this.msg.asObservable()}getTaskStatusColor(){return new Promise((e,t)=>{this.http.post("/api/isa/request/getTaskStatusNameColor",{}).subscribe(t=>(this.taskStatusColor=t,e(t)),e=>{})})}getAllRequestsForRmg(e){return this.http.post("/api/isa/request/getAllRequestsForRmg",{filterConfig:e})}getTotalForRmg(e){return new Promise((t,n)=>{this.http.post("/api/isa/request/getTotalForRmg",{filterConfig:e}).subscribe(e=>t(e),e=>{})})}getDefaultISAStatusToDisplay(){return this.http.post("/api/isa/request/getISADefaultStatusToDisplay",{})}getCTCbreakUpForSkillSet(e){return new Promise((t,n)=>{this.http.post("/api/isa/request/getCTCbreakUpForSkillSet",e).subscribe(e=>t(e.data),e=>{n(e)})})}getCurrentUserRole(){return this.roleService&&this.roleService.roles&&this.roleService.roles.length>0?this.roleService.roles[0].role_id:null}getTemplateForUser(e){return this.http.post("/api/isa/request/getVisibleFormFieldsForUser",e)}saveAndApproveRequest(e){return this.http.post("/api/isa/request/updBudgetInfoByRequestId",e)}getVisibilityMatrix(e){return this.http.post("/api/isa/request/getAuthorzedFieldsByRoleId",e)}getISAAttachmentFromS3(e){return this.http.post("/api/isa/attachment/getISAAttachmentFromS3",{key:e})}getSkillType(){return this.http.post("/api/isa/request/getSkillType",{})}getSkillLevel(){return this.http.post("/api/isa/request/getSkillLevel",{})}getSkillExperience(){return this.http.post("/api/isa/request/getSkillExperience",{})}getSkillName(e){return this.http.post("/api/isa/request/getSkillName",{skill_type_id:e})}getIsaActivityReportDownload(){return this.http.post("/api/isa/request/getIsaActivityReportDownload",{})}getISASLAreport(e){return this.http.post("/api/isa/request/getISASLAreport",{filterConfig:e})}getISACustomReport(e){return this.http.post("/api/isa/request/getISACustomReport",{filterConfig:e})}getPositionType(){return this.http.post("/api/employee360/masterData/getPositionType",{})}calculateNetCostBasedOnPosition(e,t,n,r,o,a,l){return Object(i.c)(this,void 0,void 0,(function*(){let i;i=a&&a.length>1&&(yield this.getManpowerCostByOId(a,n,o,2))||(yield this.getManpowerCostBasedOnPosition(e,t,n,o,l));let d=yield this.getNonManpowerCost(t,n,r,o,2),h=yield this.getAllocatedCost(),c=0;c=(i?i.cost:0)+d.length>0?s.reduce(s.pluck(d,"cost"),(e,t)=>e+t,0):0;let p=h.length>0?s.reduce(s.pluck(h,"percentage"),(e,t)=>e+t,0):0;return{cost:c,currency:i&&i.currency_code?i.currency_code:"",manpowerCost:i,nonManpowerCost:d,allocatedCost:h,allocatedCostValue:c*(p/100)}}))}getManpowerCostBasedOnPosition(e,t,n,i,r){return new Promise((s,o)=>{this.http.post("/api/isa/configuration/getManpowerCostBasedOnPosition",{skillRoleId:e,nationalityId:t,locationGroupId:n,unit:i,position:r}).subscribe(e=>s(e),e=>(console.log(e),o(e)))})}getNonManpowerCost(e,t,n,i,r){return new Promise((s,o)=>{this.http.post("/api/project/getNonManpowerCost",{nationalityId:e,locationGroupId:t,locationId:n,unit:i,currency_id:r}).subscribe(e=>s(e),e=>(console.log(e),o(e)))})}getAllocatedCost(){return new Promise((e,t)=>{this.http.post("/api/project/getAllocatedCost","").subscribe(t=>e(t),e=>(console.log(e),t(e)))})}getManpowerCostByOId(e,t,n,i){return new Promise((r,s)=>{this.http.post("/api/project/getEmployeeSalary",{oid:e,location_group_id:t,unit_id:n,currency_id:i}).subscribe(e=>r(e),e=>(console.log(e),s(e)))})}getSkillNameMaster(){return this.http.post("/api/isa/configuration/getSkillName",{})}getVendorOid(e,t){return this.http.post("/api/isa/request/getVendorOid",{vendorNames:e,docID:t})}getVendorList(e,t){return this.http.post("/api/isa/request/getVendorList",{documentID:e,limitval:t})}removeVendor(e,t){return this.http.post("/api/isa/request/removeVendors",{vendorOid:e,documentID:t})}checkVendorOrNot(){return this.http.post("/api/isa/request/checkVendorOrNot",{})}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275inject"](a.c),o["\u0275\u0275inject"](l.a),o["\u0275\u0275inject"](d.a),o["\u0275\u0275inject"](h.a))},e.\u0275prov=o["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},ucYs:function(e,t,n){"use strict";n.d(t,"a",(function(){return l}));var i=n("mrSG"),r=n("xG9w"),s=n("fXoL"),o=n("tk/3"),a=n("BVzC");let l=(()=>{class e{constructor(e,t){this.http=e,this.errorService=t,this.workflowStatusList=[],this.getWorkflowStatusList()}getWorkflowStatusList(){this.http.post("/api/wfPrimary/getWorkflowStatusList","").subscribe(e=>{this.workflowStatusList=e.data},e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server While Getting Workflow Status List",e&&e.params?e.params:e&&e.error?e.error.params:{})})}getWorkflowProperties(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/getWorkflowProperties",{applicationId:e}).subscribe(e=>t(e),e=>n(e))})}getWorkflowPropertiesByWorkflowId(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/getWorkflowPropertiesByWorkflowId",{workflowId:e}).subscribe(e=>t(e),e=>n(e))})}getApproversHierarchy(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/getApproversHierarchy",e).subscribe(e=>t(e),e=>n(e))})}createWorkflowItems(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/createWorkflowItems",e).subscribe(e=>t(e),e=>n(e))})}getWorkflowDetails(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/getWorkflowDetails",{workflowId:e}).subscribe(e=>t(e),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while getting workflow details",e&&e.params?e.params:e&&e.error?e.error.params:{})})})}formatApproversHierarchy(e,t){return Object(i.c)(this,void 0,void 0,(function*(){0==e.length&&t.cc0&&t.cc0.appr0&&t.cc0.appr0.length>0&&e.push([{approvalType:"I",purposeOfInsertingIntoArray:"Just to ensure that 'I' type data is used, if it exists"}]);for(let n=0;n<e.length;n++){let i=[],s=r.keys(t["cc"+n]);for(let r=0;r<s.length;r++)for(let o=0;o<t["cc"+n][s[r]].length;o++){let a={name:t["cc"+n][s[r]][o].DELEGATE_NAME,oid:t["cc"+n][s[r]][o].DELEGATE_OID,level:r+1,designation:t["cc"+n][s[r]][o].DELEGATE_DESIGNATION_NAME,is_delegated:t["cc"+n][s[r]][o].IS_DELEGATED,role:t["cc"+n][s[r]][o].DELEGATE_ROLE_NAME};if(1==t["cc"+n][s[r]][o].IS_DELEGATED&&(a.delegated_by={name:t["cc"+n][s[r]][o].APPROVER_NAME,oid:t["cc"+n][s[r]][o].APPROVER_OID,level:r+1,designation:t["cc"+n][s[r]][o].APPROVER_DESIGNATION_NAME}),i.push(a),n==e.length-1&&r==s.length-1&&o==t["cc"+n][s[r]].length-1)return i}}}))}storeComments(e,t,n){return new Promise((i,r)=>{this.http.post("/api/wfPrimary/updateComments",{workflowHeaderId:e,newComments:t,commentor:n}).subscribe(e=>i(e),e=>(this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server Error while Updating Workflow Comments",e&&e.params?e.params:e&&e.error?e.error.params:{}),r(e)))})}updateWorkflowItems(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/updateWorkflowItems",e).subscribe(e=>t(e),e=>(console.log(e),n(e)))})}formatApproversHierarchyForOpportunityApprovalActivity(e){return Object(i.c)(this,void 0,void 0,(function*(){for(let t=0;t<1;t++){let n=[],i=r.keys(e["cc"+t]);for(let r=0;r<i.length;r++)for(let s=0;s<e["cc"+t][i[r]].length;s++){let o={name:e["cc"+t][i[r]][s].DELEGATE_NAME,oid:e["cc"+t][i[r]][s].DELEGATE_OID,level:e["cc"+t][i[r]][s].APPROVAL_ORDER,designation:e["cc"+t][i[r]][s].DELEGATE_DESIGNATION_NAME,is_delegated:e["cc"+t][i[r]][s].IS_DELEGATED};if(1==e["cc"+t][i[r]][s].IS_DELEGATED&&(o.delegated_by={name:e["cc"+t][i[r]][s].APPROVER_NAME,oid:e["cc"+t][i[r]][s].APPROVER_OID,level:e["cc"+t][i[r]][s].APPROVAL_ORDER,designation:e["cc"+t][i[r]][s].APPROVER_DESIGNATION_NAME}),n.push(o),r==i.length-1&&s==e["cc"+t][i[r]].length-1)return n}}}))}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275inject"](o.c),s["\u0275\u0275inject"](a.a))},e.\u0275prov=s["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},w76M:function(e,t,n){"use strict";n.d(t,"a",(function(){return d})),n.d(t,"b",(function(){return h}));var i=n("jhN1"),r=n("fXoL"),s=n("oHs6"),o=n("PVOt"),a=n("6t9p");const l=["*"];let d=(()=>{let e=class extends o.b{constructor(e,t,n,i,r,s,o,a){super(e,t,n,i,o,a),this._watcherHelper=i,this._idh=r,this._createEventEmitters([{subscribe:"contentReady",emit:"onContentReady"},{subscribe:"disposing",emit:"onDisposing"},{subscribe:"hidden",emit:"onHidden"},{subscribe:"hiding",emit:"onHiding"},{subscribe:"initialized",emit:"onInitialized"},{subscribe:"optionChanged",emit:"onOptionChanged"},{subscribe:"resize",emit:"onResize"},{subscribe:"resizeEnd",emit:"onResizeEnd"},{subscribe:"resizeStart",emit:"onResizeStart"},{subscribe:"showing",emit:"onShowing"},{subscribe:"shown",emit:"onShown"},{subscribe:"titleRendered",emit:"onTitleRendered"},{emit:"accessKeyChange"},{emit:"animationChange"},{emit:"closeOnOutsideClickChange"},{emit:"containerChange"},{emit:"contentTemplateChange"},{emit:"deferRenderingChange"},{emit:"disabledChange"},{emit:"dragEnabledChange"},{emit:"elementAttrChange"},{emit:"focusStateEnabledChange"},{emit:"fullScreenChange"},{emit:"heightChange"},{emit:"hintChange"},{emit:"hoverStateEnabledChange"},{emit:"maxHeightChange"},{emit:"maxWidthChange"},{emit:"minHeightChange"},{emit:"minWidthChange"},{emit:"positionChange"},{emit:"resizeEnabledChange"},{emit:"rtlEnabledChange"},{emit:"shadingChange"},{emit:"shadingColorChange"},{emit:"showCloseButtonChange"},{emit:"showTitleChange"},{emit:"tabIndexChange"},{emit:"titleChange"},{emit:"titleTemplateChange"},{emit:"toolbarItemsChange"},{emit:"visibleChange"},{emit:"widthChange"}]),this._idh.setHost(this),s.setHost(this)}get accessKey(){return this._getOption("accessKey")}set accessKey(e){this._setOption("accessKey",e)}get animation(){return this._getOption("animation")}set animation(e){this._setOption("animation",e)}get closeOnOutsideClick(){return this._getOption("closeOnOutsideClick")}set closeOnOutsideClick(e){this._setOption("closeOnOutsideClick",e)}get container(){return this._getOption("container")}set container(e){this._setOption("container",e)}get contentTemplate(){return this._getOption("contentTemplate")}set contentTemplate(e){this._setOption("contentTemplate",e)}get deferRendering(){return this._getOption("deferRendering")}set deferRendering(e){this._setOption("deferRendering",e)}get disabled(){return this._getOption("disabled")}set disabled(e){this._setOption("disabled",e)}get dragEnabled(){return this._getOption("dragEnabled")}set dragEnabled(e){this._setOption("dragEnabled",e)}get elementAttr(){return this._getOption("elementAttr")}set elementAttr(e){this._setOption("elementAttr",e)}get focusStateEnabled(){return this._getOption("focusStateEnabled")}set focusStateEnabled(e){this._setOption("focusStateEnabled",e)}get fullScreen(){return this._getOption("fullScreen")}set fullScreen(e){this._setOption("fullScreen",e)}get height(){return this._getOption("height")}set height(e){this._setOption("height",e)}get hint(){return this._getOption("hint")}set hint(e){this._setOption("hint",e)}get hoverStateEnabled(){return this._getOption("hoverStateEnabled")}set hoverStateEnabled(e){this._setOption("hoverStateEnabled",e)}get maxHeight(){return this._getOption("maxHeight")}set maxHeight(e){this._setOption("maxHeight",e)}get maxWidth(){return this._getOption("maxWidth")}set maxWidth(e){this._setOption("maxWidth",e)}get minHeight(){return this._getOption("minHeight")}set minHeight(e){this._setOption("minHeight",e)}get minWidth(){return this._getOption("minWidth")}set minWidth(e){this._setOption("minWidth",e)}get position(){return this._getOption("position")}set position(e){this._setOption("position",e)}get resizeEnabled(){return this._getOption("resizeEnabled")}set resizeEnabled(e){this._setOption("resizeEnabled",e)}get rtlEnabled(){return this._getOption("rtlEnabled")}set rtlEnabled(e){this._setOption("rtlEnabled",e)}get shading(){return this._getOption("shading")}set shading(e){this._setOption("shading",e)}get shadingColor(){return this._getOption("shadingColor")}set shadingColor(e){this._setOption("shadingColor",e)}get showCloseButton(){return this._getOption("showCloseButton")}set showCloseButton(e){this._setOption("showCloseButton",e)}get showTitle(){return this._getOption("showTitle")}set showTitle(e){this._setOption("showTitle",e)}get tabIndex(){return this._getOption("tabIndex")}set tabIndex(e){this._setOption("tabIndex",e)}get title(){return this._getOption("title")}set title(e){this._setOption("title",e)}get titleTemplate(){return this._getOption("titleTemplate")}set titleTemplate(e){this._setOption("titleTemplate",e)}get toolbarItems(){return this._getOption("toolbarItems")}set toolbarItems(e){this._setOption("toolbarItems",e)}get visible(){return this._getOption("visible")}set visible(e){this._setOption("visible",e)}get width(){return this._getOption("width")}set width(e){this._setOption("width",e)}get toolbarItemsChildren(){return this._getOption("toolbarItems")}set toolbarItemsChildren(e){this.setChildren("toolbarItems",e)}_createInstance(e,t){return new s.a(e,t)}ngOnDestroy(){this._destroyWidget()}ngOnChanges(e){super.ngOnChanges(e),this.setupChanges("toolbarItems",e)}setupChanges(e,t){e in this._optionsToUpdate||this._idh.setup(e,t)}ngDoCheck(){this._idh.doCheck("toolbarItems"),this._watcherHelper.checkWatchers(),super.ngDoCheck(),super.clearChangedOptions()}_setOption(e,t){let n=this._idh.setupSingle(e,t),i=null!==this._idh.getChanges(e,t);(n||i)&&super._setOption(e,t)}};return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](r.ElementRef),r["\u0275\u0275directiveInject"](r.NgZone),r["\u0275\u0275directiveInject"](o.e),r["\u0275\u0275directiveInject"](o.j),r["\u0275\u0275directiveInject"](o.g),r["\u0275\u0275directiveInject"](o.i),r["\u0275\u0275directiveInject"](i.h),r["\u0275\u0275directiveInject"](r.PLATFORM_ID))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["dx-popup"]],contentQueries:function(e,t,n){if(1&e&&r["\u0275\u0275contentQuery"](n,a.L,!1),2&e){let e;r["\u0275\u0275queryRefresh"](e=r["\u0275\u0275loadQuery"]())&&(t.toolbarItemsChildren=e)}},inputs:{accessKey:"accessKey",animation:"animation",closeOnOutsideClick:"closeOnOutsideClick",container:"container",contentTemplate:"contentTemplate",deferRendering:"deferRendering",disabled:"disabled",dragEnabled:"dragEnabled",elementAttr:"elementAttr",focusStateEnabled:"focusStateEnabled",fullScreen:"fullScreen",height:"height",hint:"hint",hoverStateEnabled:"hoverStateEnabled",maxHeight:"maxHeight",maxWidth:"maxWidth",minHeight:"minHeight",minWidth:"minWidth",position:"position",resizeEnabled:"resizeEnabled",rtlEnabled:"rtlEnabled",shading:"shading",shadingColor:"shadingColor",showCloseButton:"showCloseButton",showTitle:"showTitle",tabIndex:"tabIndex",title:"title",titleTemplate:"titleTemplate",toolbarItems:"toolbarItems",visible:"visible",width:"width"},outputs:{onContentReady:"onContentReady",onDisposing:"onDisposing",onHidden:"onHidden",onHiding:"onHiding",onInitialized:"onInitialized",onOptionChanged:"onOptionChanged",onResize:"onResize",onResizeEnd:"onResizeEnd",onResizeStart:"onResizeStart",onShowing:"onShowing",onShown:"onShown",onTitleRendered:"onTitleRendered",accessKeyChange:"accessKeyChange",animationChange:"animationChange",closeOnOutsideClickChange:"closeOnOutsideClickChange",containerChange:"containerChange",contentTemplateChange:"contentTemplateChange",deferRenderingChange:"deferRenderingChange",disabledChange:"disabledChange",dragEnabledChange:"dragEnabledChange",elementAttrChange:"elementAttrChange",focusStateEnabledChange:"focusStateEnabledChange",fullScreenChange:"fullScreenChange",heightChange:"heightChange",hintChange:"hintChange",hoverStateEnabledChange:"hoverStateEnabledChange",maxHeightChange:"maxHeightChange",maxWidthChange:"maxWidthChange",minHeightChange:"minHeightChange",minWidthChange:"minWidthChange",positionChange:"positionChange",resizeEnabledChange:"resizeEnabledChange",rtlEnabledChange:"rtlEnabledChange",shadingChange:"shadingChange",shadingColorChange:"shadingColorChange",showCloseButtonChange:"showCloseButtonChange",showTitleChange:"showTitleChange",tabIndexChange:"tabIndexChange",titleChange:"titleChange",titleTemplateChange:"titleTemplateChange",toolbarItemsChange:"toolbarItemsChange",visibleChange:"visibleChange",widthChange:"widthChange"},features:[r["\u0275\u0275ProvidersFeature"]([o.e,o.j,o.i,o.g]),r["\u0275\u0275InheritDefinitionFeature"],r["\u0275\u0275NgOnChangesFeature"]],ngContentSelectors:l,decls:1,vars:0,template:function(e,t){1&e&&(r["\u0275\u0275projectionDef"](),r["\u0275\u0275projection"](0))},encapsulation:2}),e})(),h=(()=>{let e=class{};return e.\u0275mod=r["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=r["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[a.bb,a.Gc,a.Vd,a.vd,a.hb,a.lb,a.sb,a.id,a.jd,a.M,o.c,o.f,i.b],a.bb,a.Gc,a.Vd,a.vd,a.hb,a.lb,a.sb,a.id,a.jd,a.M,o.f]}),e})()},x4Kz:function(e,t,n){"use strict";n.r(t),n.d(t,"TimesheetTestcaseConfigurationModule",(function(){return x}));var i=n("ofXK"),r=n("3Pt+"),s=n("+0xr"),o=n("bTqV"),a=n("NFeN"),l=n("Qu3c"),d=n("STbY"),h=n("kmnG"),c=n("qFsG"),p=n("d3UM"),g=n("tyNb"),m=n("wd/R"),u=n("fXoL"),C=n("tk/3");let b=(()=>{class e{constructor(e){this.$http=e,this.getTimesheetTestCaseConfiguration=(e,t,n)=>this.$http.post("/api/tsPrimary/getTimesheetTestcaseForOid",{month_sub:e,associate_oid:t,year:n}),this.makeAllDuplicateItemInActive=(e,t,n)=>this.$http.post("/api/tsPrimary/makeAllDuplicateItemInActive",{month_sub:e,associate_oid:t,year:n})}}return e.\u0275fac=function(t){return new(t||e)(u["\u0275\u0275inject"](C.c))},e.\u0275prov=u["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var f=n("dNgK"),S=n("GnQ3"),E=n("BVzC"),v=n("JLuW"),O=n("LcQX");function I(e,t){if(1&e&&(u["\u0275\u0275elementStart"](0,"div"),u["\u0275\u0275elementStart"](1,"div",1),u["\u0275\u0275elementStart"](2,"div",17),u["\u0275\u0275elementStart"](3,"div",18),u["\u0275\u0275elementStart"](4,"div",19),u["\u0275\u0275elementStart"](5,"div",20),u["\u0275\u0275elementStart"](6,"div",21),u["\u0275\u0275elementStart"](7,"span"),u["\u0275\u0275text"](8),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](9,"div",21),u["\u0275\u0275elementStart"](10,"span"),u["\u0275\u0275text"](11),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](12,"div",21),u["\u0275\u0275elementStart"](13,"span"),u["\u0275\u0275text"](14),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](15,"div",21),u["\u0275\u0275elementStart"](16,"span"),u["\u0275\u0275text"](17),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](18,"div",22),u["\u0275\u0275elementStart"](19,"span"),u["\u0275\u0275text"](20),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](21,"div",22),u["\u0275\u0275elementStart"](22,"span"),u["\u0275\u0275text"](23),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](24,"div",22),u["\u0275\u0275elementStart"](25,"span"),u["\u0275\u0275text"](26),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](27,"div",22),u["\u0275\u0275elementStart"](28,"span"),u["\u0275\u0275text"](29),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=u["\u0275\u0275nextContext"](2);u["\u0275\u0275advance"](8),u["\u0275\u0275textInterpolate"](e.employee_name),u["\u0275\u0275advance"](3),u["\u0275\u0275textInterpolate"](e.object_value),u["\u0275\u0275advance"](3),u["\u0275\u0275textInterpolate"](e.object_description),u["\u0275\u0275advance"](3),u["\u0275\u0275textInterpolate"](e.location),u["\u0275\u0275advance"](3),u["\u0275\u0275textInterpolate"](e.day_type),u["\u0275\u0275advance"](3),u["\u0275\u0275textInterpolate"](e.hours),u["\u0275\u0275advance"](3),u["\u0275\u0275textInterpolate"](e.status_code),u["\u0275\u0275advance"](3),u["\u0275\u0275textInterpolate"](e.date?n.getLocalDate(e.date):"-")}}function _(e,t){if(1&e&&(u["\u0275\u0275elementStart"](0,"div"),u["\u0275\u0275template"](1,I,30,8,"div",16),u["\u0275\u0275elementEnd"]()),2&e){const e=u["\u0275\u0275nextContext"]();u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngForOf",e.udrfBodyData)}}function w(e,t){1&e&&(u["\u0275\u0275elementStart"](0,"div"),u["\u0275\u0275elementStart"](1,"h4",23),u["\u0275\u0275text"](2," Oops ! No Data Found ! "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](3,"div",24),u["\u0275\u0275element"](4,"img",25),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]())}const y=[{path:"",component:(()=>{class e{constructor(e,t,n,i,r,s){this.tsService=e,this._snackBar=t,this.udrfService=n,this.errorService=i,this.sharedLazyLoadedComponentsService=r,this.utilityService=s,this.noDataFound=!1}ngOnInit(){}openSnackBar(e,t){this._snackBar.open(e,t,{duration:2e3})}getTimesheetTestcaseConfiguration(){this.tsService.getTimesheetTestCaseConfiguration(this.month_sub,this.associate_oid,this.year).subscribe(e=>{e.data&&e.data.length>0?(console.log(e.data),this.udrfBodyData=e.data,this.noDataFound=!1):"SN"==e.messText&&(this.noDataFound=!0)},e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error in Report ",e&&e.params?e.params:e&&e.error?e.error.params:{})})}makeAllDuplicateItemInActive(){this.tsService.makeAllDuplicateItemInActive(this.month_sub,this.associate_oid,this.year).subscribe(e=>{"S"==e.messText&&this.utilityService.showMessage("Duplicate Entries are set to Is Active 0 Successfully !!","Dismiss",3e3)},e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error in Report ",e&&e.params?e.params:e&&e.error?e.error.params:{})})}getLocalDate(e){return m(e).format("DD-MM-YYYY")}}return e.\u0275fac=function(t){return new(t||e)(u["\u0275\u0275directiveInject"](b),u["\u0275\u0275directiveInject"](f.a),u["\u0275\u0275directiveInject"](S.a),u["\u0275\u0275directiveInject"](E.a),u["\u0275\u0275directiveInject"](v.a),u["\u0275\u0275directiveInject"](O.a))},e.\u0275cmp=u["\u0275\u0275defineComponent"]({type:e,selectors:[["timesheet-testcase-configuration-landing-page"]],decls:34,vars:5,consts:[[1,"p-0","container-fluid"],[1,"row"],[1,"col-4","center","pt-2"],[2,"font-size","medium","font-weight","500","color","#cf0001"],[1,"col-2"],["mat-icon-button","","matTooltip","Submit",1,"iconsSize","ml-2",3,"click"],[1,"iconsSize"],["mat-icon-button","","matTooltip","Make all Duplicate Inactive",1,"iconsSize","ml-2",3,"click"],[1,"col-12","pl-2"],[1,"headingBold","my-auto","ml-3"],[1,"row","pt-2"],[1,"col-3"],["appearance","outline","floatLabel","always"],["matInput","","type","text",3,"ngModel","ngModelChange"],["matInput","","type","number","required","",3,"ngModel","ngModelChange"],[4,"ngIf"],[4,"ngFor","ngForOf"],[1,"col-12"],[1,"card","listcard",2,"border-left","3px solid #9a9a9a","padding","1px"],[1,"card-body",2,"padding","2px !important"],[1,"row","card-details","p-0"],[1,"col-2","pl-0","d-flex","name"],[1,"col-1","pl-0","d-flex","name"],[1,"d-flex","justify-content-center","align-items-center","mt-0","mb-0","slide-in-top"],[1,"d-flex","justify-content-center","align-items-center","slide-from-down"],["src","assets/images/nomilestone.png","height","220","width","250",1,"mt-3"]],template:function(e,t){1&e&&(u["\u0275\u0275elementStart"](0,"div",0),u["\u0275\u0275elementStart"](1,"div",1),u["\u0275\u0275elementStart"](2,"div",2),u["\u0275\u0275elementStart"](3,"span",3),u["\u0275\u0275text"](4," Timesheet Testcase Configuration "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](5,"div",4),u["\u0275\u0275elementStart"](6,"button",5),u["\u0275\u0275listener"]("click",(function(){return t.getTimesheetTestcaseConfiguration()})),u["\u0275\u0275elementStart"](7,"mat-icon",6),u["\u0275\u0275text"](8,"done_all"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](9,"div",4),u["\u0275\u0275elementStart"](10,"button",7),u["\u0275\u0275listener"]("click",(function(){return t.makeAllDuplicateItemInActive()})),u["\u0275\u0275elementStart"](11,"mat-icon",6),u["\u0275\u0275text"](12,"done"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](13,"div",8),u["\u0275\u0275elementStart"](14,"span",9),u["\u0275\u0275text"](15," Testcase Check and Update "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](16,"div",10),u["\u0275\u0275elementStart"](17,"div",11),u["\u0275\u0275elementStart"](18,"mat-form-field",12),u["\u0275\u0275elementStart"](19,"mat-label"),u["\u0275\u0275text"](20,"Associate Oid"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](21,"input",13),u["\u0275\u0275listener"]("ngModelChange",(function(e){return t.associate_oid=e})),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](22,"div",11),u["\u0275\u0275elementStart"](23,"mat-form-field",12),u["\u0275\u0275elementStart"](24,"mat-label"),u["\u0275\u0275text"](25,"Month Submission"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](26,"input",14),u["\u0275\u0275listener"]("ngModelChange",(function(e){return t.month_sub=e})),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](27,"div",11),u["\u0275\u0275elementStart"](28,"mat-form-field",12),u["\u0275\u0275elementStart"](29,"mat-label"),u["\u0275\u0275text"](30,"Year"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](31,"input",14),u["\u0275\u0275listener"]("ngModelChange",(function(e){return t.year=e})),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275template"](32,_,2,1,"div",15),u["\u0275\u0275template"](33,w,5,0,"div",15),u["\u0275\u0275elementEnd"]()),2&e&&(u["\u0275\u0275advance"](21),u["\u0275\u0275property"]("ngModel",t.associate_oid),u["\u0275\u0275advance"](5),u["\u0275\u0275property"]("ngModel",t.month_sub),u["\u0275\u0275advance"](5),u["\u0275\u0275property"]("ngModel",t.year),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf",!t.noDataFound),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf",t.noDataFound))},directives:[o.a,l.a,a.a,h.c,h.g,c.b,r.e,r.v,r.y,r.A,r.F,i.NgIf,i.NgForOf],styles:["table[_ngcontent-%COMP%]{width:99%}.iconsSize[_ngcontent-%COMP%]{background:transparent;font-size:21px!important;color:#545352!important}.headingBold[_ngcontent-%COMP%]{color:#cf0001;font-weight:500;font-size:14px}.mat-field-name[_ngcontent-%COMP%]{font-size:15px}.btn-active[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff;font-weight:400;font-size:13px!important;min-width:60px;line-height:33px;padding:0 8px;border-radius:4px;margin-right:6px!important;margin-bottom:3px}.mat-header-row[_ngcontent-%COMP%], .mat-row[_ngcontent-%COMP%]{min-height:30px}.center[_ngcontent-%COMP%]{text-align:center}"]}),e})()}];let A=(()=>{class e{}return e.\u0275mod=u["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=u["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[g.k.forChild(y)],g.k]}),e})();var k=n("Xi0T");let x=(()=>{class e{}return e.\u0275mod=u["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=u["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,r.p,r.E,s.m,A,o.b,a.b,l.b,c.c,h.e,p.d,d.e,k.a]]}),e})()}}]);