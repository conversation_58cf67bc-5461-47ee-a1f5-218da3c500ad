(window.webpackJsonp=window.webpackJsonp||[]).push([[849],{"H3+L":function(e,t,n){"use strict";n.r(t),n.d(t,"PmColumnConfigComponent",(function(){return x}));var o=n("mrSG"),i=n("xG9w"),c=n("STbY"),r=n("fXoL"),l=n("0IaG"),a=n("sRDL"),s=n("t44d"),p=n("NFeN"),m=n("Qu3c"),g=n("vxfF"),d=n("5+WD"),f=n("ofXK"),u=n("1jcm");function h(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"div",9),r["\u0275\u0275elementStart"](1,"button",10),r["\u0275\u0275elementStart"](2,"mat-icon",11),r["\u0275\u0275text"](3,"drag_indicator "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](4,"mat-slide-toggle",12),r["\u0275\u0275listener"]("change",(function(){r["\u0275\u0275restoreView"](e);const n=t.$implicit;return r["\u0275\u0275nextContext"]().configureColumns(n)})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](5,"span",13),r["\u0275\u0275text"](6),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;r["\u0275\u0275advance"](4),r["\u0275\u0275property"]("checked",e.isVisible)("disabled",e.freezed),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate"](e.header)}}function C(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div",9),r["\u0275\u0275elementStart"](1,"button",14),r["\u0275\u0275element"](2,"span"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]())}let x=(()=>{class e{constructor(e,t,n){this.dialogRef=e,this.pmService=t,this.pmMasterService=n,this.totalColSize=0,this.colSize=0,this.checked=!1,this.disabled=!1,this.formConfig=[]}ngOnInit(){return Object(o.c)(this,void 0,void 0,(function*(){yield this.pmMasterService.getPMFormCustomizeConfigV().then(e=>{e&&(this.formConfig=e)});const e=i.where(this.formConfig,{type:"project-theme",field_name:"styles",is_active:!0});this.button=e.length>0&&e[0].data.button_color?e[0].data.button_color:"#90ee90",this.fontStyle=e.length>0&&e[0].data.font_style?e[0].data.font_style:"Roboto",document.documentElement.style.setProperty("--columnConfigFont",this.fontStyle)}))}drop(e){console.log("Event",e),this.configureColumn()}configureColumn(){return Object(o.c)(this,void 0,void 0,(function*(){for(let e=0;e<this.pmService.projectHeaderConfig.columnList.length;e++)this.pmService.projectHeaderConfig.columnList[e]={type:this.pmService.projectHeaderConfig.columnList[e].type,isActive:this.pmService.projectHeaderConfig.columnList[e].isActive,isVisible:"true"}}))}updateColumnConfig(){this.pmService.projectHeaderConfig.updateUserConfig()}closeModal(){this.updateColumnConfig(),this.dialogRef.close({event:"Close"})}configureColumns(e){e.isVisible="false"===e.isVisible?"true":"false",this.configureColumn()}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](l.h),r["\u0275\u0275directiveInject"](a.a),r["\u0275\u0275directiveInject"](s.a))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["app-pm-column-config"]],viewQuery:function(e,t){if(1&e&&r["\u0275\u0275staticViewQuery"](c.b,!0),2&e){let e;r["\u0275\u0275queryRefresh"](e=r["\u0275\u0275loadQuery"]())&&(t.menu=e.first)}},decls:13,vars:3,consts:[[1,"project-column-config-style","slide-in-top"],[1,"header","pl-4","pt-3"],[1,"mat-icon-button"],["matTooltip","Close",1,"pl-2","iconButton",3,"click"],[1,"sub-header","pl-4","pt-2"],["cdkScrollable","",1,"example-container","pl-1","pt-3"],["cdkDropList","",1,"example-list",3,"cdkDropListData","cdkDropListDropped"],["cdkDrag","","class","example-box",4,"ngFor","ngForOf"],["class","example-box","cdkDrag","",4,"ngIf"],["cdkDrag","",1,"example-box"],["mat-menu-item","",2,"height","40px"],[1,"menu-icons",2,"font-size","13px","margin","0px","width","15px"],["color","primary",1,"example-margin",2,"margin","0px",3,"checked","disabled","change"],[1,"sub-header","pl-2"],["mat-menu-item",""]],template:function(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div",0),r["\u0275\u0275elementStart"](1,"div",1),r["\u0275\u0275text"](2," Configure Table "),r["\u0275\u0275elementStart"](3,"button",2),r["\u0275\u0275elementStart"](4,"mat-icon",3),r["\u0275\u0275listener"]("click",(function(){return t.closeModal()})),r["\u0275\u0275text"](5,"close"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](6,"div",4),r["\u0275\u0275text"](7," Choose the instances to present in the table list "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](8,"div"),r["\u0275\u0275elementStart"](9,"div",5),r["\u0275\u0275elementStart"](10,"div",6),r["\u0275\u0275listener"]("cdkDropListDropped",(function(e){return t.drop(e)})),r["\u0275\u0275template"](11,h,7,3,"div",7),r["\u0275\u0275template"](12,C,3,0,"div",8),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e&&(r["\u0275\u0275advance"](10),r["\u0275\u0275property"]("cdkDropListData",t.pmService.projectHeaderConfig.columnList),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngForOf",t.pmService.projectHeaderConfig.columnList),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",0==t.pmService.projectHeaderConfig.columnList.length))},directives:[p.a,m.a,g.b,d.e,f.NgForOf,f.NgIf,d.a,c.d,u.a],styles:[".project-column-config-style[_ngcontent-%COMP%]{position:relative}.project-column-config-style[_ngcontent-%COMP%]   .example-container[_ngcontent-%COMP%]{width:400px;max-width:100%;margin:0 25px 25px 0;display:inline-block;vertical-align:top;max-height:61vh;overflow-y:scroll}.project-column-config-style[_ngcontent-%COMP%]   .example-list[_ngcontent-%COMP%]{min-height:60px;background:#fff;border-radius:4px;overflow:hidden;display:block}.project-column-config-style[_ngcontent-%COMP%]     .example-box{color:#5f6c81;display:flex;flex-direction:row;align-items:left;justify-content:space-between;box-sizing:border-box;cursor:move;background:#fff;font-size:12px;height:30px}.project-column-config-style[_ngcontent-%COMP%]     .menu-list{cursor:move;font-size:8px}.project-column-config-style[_ngcontent-%COMP%]   .cdk-drag-preview[_ngcontent-%COMP%]{box-sizing:border-box;border-radius:4px;box-shadow:0 5px 5px -3px rgba(0,0,0,.2),0 8px 10px 1px rgba(0,0,0,.14),0 3px 14px 2px rgba(0,0,0,.12)}.project-column-config-style[_ngcontent-%COMP%]   .cdk-drag-placeholder[_ngcontent-%COMP%]{opacity:0}.project-column-config-style[_ngcontent-%COMP%]   .cdk-drag-animating[_ngcontent-%COMP%]{transition:transform .25s cubic-bezier(0,0,.2,1)}.project-column-config-style[_ngcontent-%COMP%]   .example-box[_ngcontent-%COMP%]:last-child{border:none}.project-column-config-style[_ngcontent-%COMP%]   .example-list.cdk-drop-list-dragging[_ngcontent-%COMP%]   .example-box[_ngcontent-%COMP%]:not(.cdk-drag-placeholder){transition:transform .25s cubic-bezier(0,0,.2,1)}.project-column-config-style[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{font-size:14px;letter-spacing:-.02em;text-transform:capitalize;color:#45546e}.project-column-config-style[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%], .project-column-config-style[_ngcontent-%COMP%]   .sub-header[_ngcontent-%COMP%]{font-family:var(--columnConfigFont)!important;font-style:normal;font-weight:700;line-height:16px}.project-column-config-style[_ngcontent-%COMP%]   .sub-header[_ngcontent-%COMP%]{color:#2c2b2b;font-size:12px}.project-column-config-style[_ngcontent-%COMP%]   .example-margin[_ngcontent-%COMP%]{margin:10px}.project-column-config-style[_ngcontent-%COMP%]   .mat-slide-toggle[_ngcontent-%COMP%]{transform:scale(.8)}.project-column-config-style[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]{position:absolute;right:10px}.project-column-config-style[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}.project-column-config-style[_ngcontent-%COMP%]   .mat-icon-button[_ngcontent-%COMP%]{float:right;right:30px}.project-column-config-style[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{font-size:16px;color:#45546e}"]}),e})()}}]);