(window.webpackJsonp=window.webpackJsonp||[]).push([[795],{"0IiT":function(e,t,n){"use strict";n.d(t,"a",(function(){return u}));var i=n("mrSG"),a=n("fXoL"),o=n("5+WD"),r=n("3Pt+"),s=n("ofXK"),c=n("bSwM"),l=n("Qu3c"),d=n("NFeN");function g(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"span"),a["\u0275\u0275text"](1),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"]().$implicit;a["\u0275\u0275classMap"](e.isDefaultVisible?"text-default-visible":"text-not-default-visible"),a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate"](e.label)}}function p(e,t){if(1&e&&(a["\u0275\u0275elementContainerStart"](0),a["\u0275\u0275elementStart"](1,"div",6),a["\u0275\u0275elementStart"](2,"div",7),a["\u0275\u0275elementStart"](3,"div"),a["\u0275\u0275element"](4,"mat-checkbox",8),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](5,"div",9),a["\u0275\u0275text"](6),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](7,"div",10),a["\u0275\u0275elementStart"](8,"mat-icon",11),a["\u0275\u0275text"](9,"drag_indicator"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](10,g,2,3,"span",12),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;a["\u0275\u0275advance"](3),a["\u0275\u0275classMap"](e.isDefaultVisible?"checkbox-disabled":"checkbox"),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("disabled",e.isDefaultVisible)("formControlName",e.key),a["\u0275\u0275advance"](1),a["\u0275\u0275classMap"](e.isDefaultVisible?"text-default-visible":"text-not-default-visible"),a["\u0275\u0275property"]("matTooltip",e.label),a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate1"](" ",e.label," ")}}let u=(()=>{class e{constructor(e){this._fb=e,this.onApply=new a.EventEmitter,this.customizationData=[],this.columnCustomForms=this._fb.group({})}ngOnInit(){return Object(i.c)(this,void 0,void 0,(function*(){this.customizationData=[...this.customization];for(let e=0;e<this.customizationData.length;e++)this.columnCustomForms.addControl(this.customizationData[e].key,new r.j(this.customizationData[e].isVisible))}))}dropField(e){let t=this.customizationData;Object(o.h)(t,e.previousIndex,e.currentIndex),t.forEach((e,n)=>{t[n].position=n}),this.customizationData=t}applyChanges(){let e=this.columnCustomForms.getRawValue();this.customizationData.forEach((t,n)=>{this.customizationData[n].isVisible=e[this.customizationData[n].key]}),this.onApply.emit(this.customizationData)}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275directiveInject"](r.i))},e.\u0275cmp=a["\u0275\u0275defineComponent"]({type:e,selectors:[["app-column-customization"]],inputs:{customization:"customization"},outputs:{onApply:"onApply"},decls:8,vars:2,consts:[[1,"bg-container",3,"formGroup"],[1,"d-flex","align-items-center","justify-content-between"],[1,"title-text"],[1,"apply-btn",3,"click"],["cdkDropList","",1,"d-flex","flex-column","list",3,"cdkDropListDropped"],[4,"ngFor","ngForOf"],["cdkDrag","",1,"d-flex","align-items-center","justify-content-between"],[1,"d-flex","align-items-center"],[3,"disabled","formControlName"],[3,"matTooltip"],["cdkDragHandle",""],[1,"icon"],[3,"class",4,"cdkDragPreview"]],template:function(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"div",0),a["\u0275\u0275elementStart"](1,"div",1),a["\u0275\u0275elementStart"](2,"div",2),a["\u0275\u0275text"](3,"Column Customization"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](4,"div",3),a["\u0275\u0275listener"]("click",(function(){return t.applyChanges()})),a["\u0275\u0275text"](5,"Apply"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](6,"div",4),a["\u0275\u0275listener"]("cdkDropListDropped",(function(e){return t.dropField(e)})),a["\u0275\u0275template"](7,p,11,8,"ng-container",5),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e&&(a["\u0275\u0275property"]("formGroup",t.columnCustomForms),a["\u0275\u0275advance"](7),a["\u0275\u0275property"]("ngForOf",t.customizationData))},directives:[r.w,r.n,o.e,s.NgForOf,o.a,c.a,r.v,r.l,l.a,o.b,d.a,o.d],styles:[".bg-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;border:3px solid #b9c0ca;border-radius:8px;padding:16px;width:225px;gap:8px;background-color:#fff}.title-text[_ngcontent-%COMP%]{font-weight:400;color:#b9c0ca}.apply-btn[_ngcontent-%COMP%], .title-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:12px}.apply-btn[_ngcontent-%COMP%]{font-weight:700;padding:4px 8px;border-radius:4px;background:linear-gradient(270deg,var(--atsprimaryColor),var(--atssecondaryColor) 105.29%);cursor:pointer;color:#fff}.checkbox[_ngcontent-%COMP%]{height:16px;width:16px;margin-right:8px}.checkbox[_ngcontent-%COMP%]     .mat-checkbox-checked.mat-accent .mat-checkbox-background{background-color:var(--atssecondaryColor)!important}.checkbox[_ngcontent-%COMP%]     .mat-checkbox-frame{border-color:#b9c0ca!important}.checkbox-disabled[_ngcontent-%COMP%]{height:16px;width:16px;margin-right:8px}.checkbox-disabled[_ngcontent-%COMP%]     .mat-checkbox-disabled .mat-checkbox-background{background-color:#b9c0ca!important}.checkbox-disabled[_ngcontent-%COMP%]     .mat-checkbox-frame{border-color:#b9c0ca!important}.text-default-visible[_ngcontent-%COMP%]{color:#b9c0ca}.text-default-visible[_ngcontent-%COMP%], .text-not-default-visible[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:400}.text-not-default-visible[_ngcontent-%COMP%]{color:var(--atssecondaryColor)}.icon[_ngcontent-%COMP%]{width:16px;height:16px;font-size:16px;color:#d4d6d8;cursor:move}.list[_ngcontent-%COMP%]{gap:8px;max-height:300px;overflow-y:scroll}.list[_ngcontent-%COMP%]::-webkit-scrollbar{width:6px!important}"]}),e})()},FJZW:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var i=n("fXoL");let a=(()=>{class e{constructor(e){this.elementRef=e,this.scrollUnit=0}get element(){return this.elementRef.nativeElement}get isOverflow(){return this.element.scrollWidth>this.element.clientWidth}scroll(e){this.element.scrollLeft+=this.scrollUnit*e}get canScrollStart(){return this.element.scrollLeft>0}get canScrollEnd(){return!(Math.abs(this.element.scrollWidth-Math.round(this.element.scrollLeft+this.element.offsetWidth))<=2)}onWindowResize(){}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](i.ElementRef))},e.\u0275dir=i["\u0275\u0275defineDirective"]({type:e,selectors:[["","appNextPreviousScrollbar",""]],hostBindings:function(e,t){1&e&&i["\u0275\u0275listener"]("resize",(function(){return t.onWindowResize()}),!1,i["\u0275\u0275resolveWindow"])},inputs:{scrollUnit:"scrollUnit"},exportAs:["appNextPreviousScrollbar"]}),e})()},IqKi:function(e,t,n){"use strict";n.r(t),n.d(t,"MainOnboardingModule",(function(){return si}));var i=n("ofXK"),a=n("tyNb"),o=n("mrSG"),r=n("XNiG"),s=n("1G5W"),c=n("xG9w"),l=n("+rOU"),d=n("yuIm"),g=n("fXoL"),p=n("rQiX"),u=n("c7zN"),h=n("RThm"),C=n("XXEo"),m=n("XNFG"),f=n("rDax"),v=n("0IaG"),x=n("Jzeh"),b=n("jaxi"),y=n("3Pt+"),S=n("NFeN"),O=n("12LZ"),M=n("VQAg"),_=n("lVl8"),w=n("vzmP"),k=n("aclQ"),P=n("0IiT"),D=n("IeBn"),E=n("pEYl"),I=n("yx4D");const V=["triggerChecklistsColumnCustomizationTemplateRef"],T=["triggerChecklistsSearchBarTemplateRef"],F=["triggerChecklistsSearchBar"],L=["triggerCandidatesColumnCustomizationTemplateRef"],G=["triggerCandidatesSearchBarTemplateRef"],A=["triggerCandidatesSearchBar"],j=["triggerGroupsColumnCustomizationTemplateRef"],R=["triggerGroupsSearchBarTemplateRef"],H=["triggerGroupsSearchBar"],z=["triggerGroupCandidateSearchBarTemplateRef"],B=["triggerGroupCandidateSearchBar"];function N(e,t){1&e&&(g["\u0275\u0275elementStart"](0,"div",10),g["\u0275\u0275element"](1,"app-access-denied"),g["\u0275\u0275elementEnd"]())}function Z(e,t){if(1&e&&(g["\u0275\u0275elementStart"](0,"div",10),g["\u0275\u0275elementStart"](1,"div",12),g["\u0275\u0275element"](2,"img",13),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](3,"div",14),g["\u0275\u0275elementStart"](4,"div",15),g["\u0275\u0275text"](5,"Loading..."),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()),2&e){const e=g["\u0275\u0275nextContext"](2);g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("src",e.uiTextConfig["UI-LOADING-001"],g["\u0275\u0275sanitizeUrl"])}}const $=function(e,t){return{"selected-menu":e,"unselected-menu":t}},W=function(e){return{stroke:e}};function Y(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275elementStart"](1,"div",20),g["\u0275\u0275listener"]("click",(function(){g["\u0275\u0275restoreView"](e);const n=t.index;return g["\u0275\u0275nextContext"](3).switchToSelectedMenu(n)})),g["\u0275\u0275element"](2,"div",27),g["\u0275\u0275pipe"](3,"svgSecurityBypass"),g["\u0275\u0275elementStart"](4,"div"),g["\u0275\u0275text"](5),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=t.$implicit,n=g["\u0275\u0275nextContext"](3);g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngClass",g["\u0275\u0275pureFunction2"](8,$,e.isSelected&&!n.displayHistoryLogs,!e.isSelected||n.displayHistoryLogs)),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngStyle",g["\u0275\u0275pureFunction1"](11,W,e.isSelected&&!n.displayHistoryLogs?"#111434":"#8B95A5"))("innerHTML",g["\u0275\u0275pipeBind1"](3,6,e.icon),g["\u0275\u0275sanitizeHtml"]),g["\u0275\u0275advance"](2),g["\u0275\u0275classMap"](e.isSelected&&!n.displayHistoryLogs?"selected-text":"unselected-text"),g["\u0275\u0275advance"](1),g["\u0275\u0275textInterpolate1"](" ",e.name," ")}}function U(e,t){if(1&e&&(g["\u0275\u0275elementStart"](0,"div",41),g["\u0275\u0275text"](1),g["\u0275\u0275elementEnd"]()),2&e){const e=g["\u0275\u0275nextContext"](6);g["\u0275\u0275advance"](1),g["\u0275\u0275textInterpolate1"](" ",e.totalCandidatesCount," ")}}function Q(e,t){if(1&e&&(g["\u0275\u0275elementStart"](0,"div",42),g["\u0275\u0275text"](1),g["\u0275\u0275elementEnd"]()),2&e){const e=g["\u0275\u0275nextContext"](6);g["\u0275\u0275advance"](1),g["\u0275\u0275textInterpolate1"](" ",e.totalCandidatesCount&&e.totalCandidatesCount-1?"Candidates":"Candidate"," ")}}function X(e,t){if(1&e&&(g["\u0275\u0275elementStart"](0,"div",41),g["\u0275\u0275text"](1),g["\u0275\u0275elementEnd"]()),2&e){const e=g["\u0275\u0275nextContext"](6);g["\u0275\u0275advance"](1),g["\u0275\u0275textInterpolate1"](" ",e.totalGroupsCount," ")}}function q(e,t){if(1&e&&(g["\u0275\u0275elementStart"](0,"div",42),g["\u0275\u0275text"](1),g["\u0275\u0275elementEnd"]()),2&e){const e=g["\u0275\u0275nextContext"](6);g["\u0275\u0275advance"](1),g["\u0275\u0275textInterpolate1"](" ",e.totalGroupsCount&&e.totalGroupsCount-1?"Groups":"Group"," ")}}function K(e,t){1&e&&(g["\u0275\u0275namespaceSVG"](),g["\u0275\u0275elementStart"](0,"svg",50),g["\u0275\u0275element"](1,"path",51),g["\u0275\u0275elementEnd"]())}function J(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275namespaceSVG"](),g["\u0275\u0275elementStart"](0,"svg",52),g["\u0275\u0275listener"]("click",(function(t){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](7).onEnterCandidatesSearch("",t)})),g["\u0275\u0275elementStart"](1,"g",53),g["\u0275\u0275element"](2,"path",54),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](3,"defs"),g["\u0275\u0275elementStart"](4,"clipPath",55),g["\u0275\u0275element"](5,"rect",56),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}}function ee(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div",43,44),g["\u0275\u0275listener"]("click",(function(){g["\u0275\u0275restoreView"](e);const t=g["\u0275\u0275reference"](2);return g["\u0275\u0275nextContext"](6).openCandidatesSearchBarOverlay(t,-37,0)})),g["\u0275\u0275elementStart"](3,"div",45),g["\u0275\u0275elementStart"](4,"input",46),g["\u0275\u0275listener"]("ngModelChange",(function(t){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](6).candidatesSearchParams=t}))("keydown.backspace",(function(){g["\u0275\u0275restoreView"](e);const t=g["\u0275\u0275nextContext"](6);return t.onEnterCandidatesSearch(t.candidatesSearchParams)})),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](5,"div",47),g["\u0275\u0275template"](6,K,2,0,"svg",48),g["\u0275\u0275template"](7,J,6,0,"svg",49),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}if(2&e){const e=g["\u0275\u0275nextContext"](6);g["\u0275\u0275advance"](4),g["\u0275\u0275property"]("ngModel",e.candidatesSearchParams),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("ngIf",""==e.candidatesSearchParams),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",e.candidatesSearchParams&&""!=e.candidatesSearchParams)}}function te(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div",57,44),g["\u0275\u0275listener"]("click",(function(){g["\u0275\u0275restoreView"](e);const t=g["\u0275\u0275reference"](2);return g["\u0275\u0275nextContext"](6).openCandidatesSearchBarOverlay(t,-30,-325)})),g["\u0275\u0275namespaceSVG"](),g["\u0275\u0275elementStart"](3,"svg",50),g["\u0275\u0275element"](4,"path",51),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}}function ne(e,t){1&e&&(g["\u0275\u0275namespaceSVG"](),g["\u0275\u0275elementStart"](0,"svg",50),g["\u0275\u0275element"](1,"path",51),g["\u0275\u0275elementEnd"]())}function ie(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275namespaceSVG"](),g["\u0275\u0275elementStart"](0,"svg",52),g["\u0275\u0275listener"]("click",(function(t){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](7).onEnterGroupsSearch("",t)})),g["\u0275\u0275elementStart"](1,"g",53),g["\u0275\u0275element"](2,"path",54),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](3,"defs"),g["\u0275\u0275elementStart"](4,"clipPath",55),g["\u0275\u0275element"](5,"rect",56),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}}function ae(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div",43,58),g["\u0275\u0275listener"]("click",(function(){g["\u0275\u0275restoreView"](e);const t=g["\u0275\u0275reference"](2);return g["\u0275\u0275nextContext"](6).openGroupsSearchBarOverlay(t,-37,0)})),g["\u0275\u0275elementStart"](3,"div",45),g["\u0275\u0275elementStart"](4,"input",46),g["\u0275\u0275listener"]("ngModelChange",(function(t){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](6).groupsSearchParams=t}))("keydown.backspace",(function(){g["\u0275\u0275restoreView"](e);const t=g["\u0275\u0275nextContext"](6);return t.onEnterGroupsSearch(t.groupsSearchParams)})),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](5,"div",47),g["\u0275\u0275template"](6,ne,2,0,"svg",48),g["\u0275\u0275template"](7,ie,6,0,"svg",49),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}if(2&e){const e=g["\u0275\u0275nextContext"](6);g["\u0275\u0275advance"](4),g["\u0275\u0275property"]("ngModel",e.groupsSearchParams),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("ngIf",""==e.groupsSearchParams),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",e.groupsSearchParams&&""!=e.groupsSearchParams)}}function oe(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div",57,58),g["\u0275\u0275listener"]("click",(function(){g["\u0275\u0275restoreView"](e);const t=g["\u0275\u0275reference"](2);return g["\u0275\u0275nextContext"](6).openGroupsSearchBarOverlay(t,-30,-325)})),g["\u0275\u0275namespaceSVG"](),g["\u0275\u0275elementStart"](3,"svg",50),g["\u0275\u0275element"](4,"path",51),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}}function re(e,t){if(1&e&&(g["\u0275\u0275elementStart"](0,"mat-button-toggle",59),g["\u0275\u0275elementStart"](1,"mat-icon"),g["\u0275\u0275text"](2),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;g["\u0275\u0275property"]("value",e.id),g["\u0275\u0275advance"](1),g["\u0275\u0275classMap"](e.class),g["\u0275\u0275advance"](1),g["\u0275\u0275textInterpolate"](e.icon)}}function se(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div",57,60),g["\u0275\u0275listener"]("click",(function(){g["\u0275\u0275restoreView"](e);const t=g["\u0275\u0275reference"](2);return g["\u0275\u0275nextContext"](6).openCandidatesCustomizationOverlay(t)})),g["\u0275\u0275namespaceSVG"](),g["\u0275\u0275elementStart"](3,"svg",61),g["\u0275\u0275element"](4,"path",62),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}}function ce(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div",57,63),g["\u0275\u0275listener"]("click",(function(){g["\u0275\u0275restoreView"](e);const t=g["\u0275\u0275reference"](2);return g["\u0275\u0275nextContext"](6).openGroupsCustomizationOverlay(t)})),g["\u0275\u0275namespaceSVG"](),g["\u0275\u0275elementStart"](3,"svg",61),g["\u0275\u0275element"](4,"path",62),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}}function le(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div",64),g["\u0275\u0275listener"]("click",(function(){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](6).createNewGroup()})),g["\u0275\u0275elementStart"](1,"mat-icon",65),g["\u0275\u0275text"](2,"add"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](3,"div",66),g["\u0275\u0275text"](4,"New Group"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}}const de=function(e){return{"pointer-events":e}},ge=function(e,t){return[e,t,0,0,"C"]};function pe(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div",31),g["\u0275\u0275elementStart"](1,"div",32),g["\u0275\u0275template"](2,U,2,1,"div",33),g["\u0275\u0275template"](3,Q,2,1,"div",34),g["\u0275\u0275template"](4,X,2,1,"div",33),g["\u0275\u0275template"](5,q,2,1,"div",34),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](6,"div",35),g["\u0275\u0275template"](7,ee,8,3,"div",36),g["\u0275\u0275template"](8,te,5,0,"div",37),g["\u0275\u0275template"](9,ae,8,3,"div",36),g["\u0275\u0275template"](10,oe,5,0,"div",37),g["\u0275\u0275elementStart"](11,"mat-button-toggle-group",38),g["\u0275\u0275listener"]("change",(function(t){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](5).toggleSelectedCandidateViewType(t)}))("ngModelChange",(function(t){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](5).selectedCandidateViewType=t})),g["\u0275\u0275template"](12,re,3,4,"mat-button-toggle",39),g["\u0275\u0275elementEnd"](),g["\u0275\u0275template"](13,se,5,0,"div",37),g["\u0275\u0275template"](14,ce,5,0,"div",37),g["\u0275\u0275template"](15,le,5,0,"div",40),g["\u0275\u0275pipe"](16,"access"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}if(2&e){const e=g["\u0275\u0275nextContext"](5);g["\u0275\u0275property"]("ngStyle",g["\u0275\u0275pureFunction1"](21,de,e.isSectionLoading?"none":"")),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("ngIf",!e.isSectionLoading&&1==e.selectedCandidateViewType),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",1==e.selectedCandidateViewType),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",!e.isSectionLoading&&2==e.selectedCandidateViewType),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",2==e.selectedCandidateViewType),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("ngIf",""!=e.candidatesSearchParams&&null!=e.candidatesSearchParams&&1==e.selectedCandidateViewType),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",(""==e.candidatesSearchParams||null==e.candidatesSearchParams)&&1==e.selectedCandidateViewType),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",""!=e.groupsSearchParams&&null!=e.groupsSearchParams&&2==e.selectedCandidateViewType),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",(""==e.groupsSearchParams||null==e.groupsSearchParams)&&2==e.selectedCandidateViewType),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("value",e.selectedCandidateViewType)("ngModel",e.selectedCandidateViewType),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngForOf",e.candidateViewList),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",1==e.selectedCandidateViewType),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",2==e.selectedCandidateViewType),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",g["\u0275\u0275pipeBindV"](16,15,g["\u0275\u0275pureFunction2"](23,ge,e.access.moduleId.onboarding,e.access.subModuleId.onboardingCandidates)))}}function ue(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div",67),g["\u0275\u0275elementStart"](1,"app-toolbar",68),g["\u0275\u0275listener"]("onClose",(function(t){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](5).onCloseCandidatesToolbar(t)})),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}if(2&e){const e=g["\u0275\u0275nextContext"](5);g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("module","onboardingDetailsCandidate")("height","44px")("selectedDetails",e.selectedCandidatesDetails)("unselectedDetails",e.unselectedCandidatesDetails)("toolbarConfig",e.candidatesToolbarConfig)("count",e.selectedCandidatesCount)("uiTextConfig",e.uiTextConfig)("generalUiConfig",e.generalUiConfig)("isBulkSelectActive",e.isCandidatesAllChecked)("searchQuery",e.candidatesSearchParams)("currentSelectedOnboardingStatusId",e.currentSelectedOnboardingStatusId)("onboardingStatusMasterData",e.onboardingStatusMasterData)("checklistsPriorityMasterData",e.checklistsPriorityMasterData)("checklistMasterData",e.checklistMasterData)("employeeMasterData",e.employeeMasterData)("tagsMasterData",e.tagsMasterData)("groupsMasterData",e.groupsMasterData)}}function he(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div",67),g["\u0275\u0275elementStart"](1,"app-toolbar",69),g["\u0275\u0275listener"]("onClose",(function(t){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](5).onCloseGroupsToolbar(t)})),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}if(2&e){const e=g["\u0275\u0275nextContext"](5);g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("module","onboardingDetailsGroup")("height","44px")("selectedDetails",e.selectedGroupsDetails)("unselectedDetails",e.unselectedGroupsDetails)("toolbarConfig",e.groupsToolbarConfig)("count",e.selectedGroupsCount)("uiTextConfig",e.uiTextConfig)("generalUiConfig",e.generalUiConfig)("isBulkSelectActive",e.isGroupsAllChecked)("searchQuery",e.groupsSearchParams)("onboardingStatusMasterData",e.onboardingStatusMasterData)("tagsMasterData",e.groupTagsMasterData)("employeeMasterData",e.employeeMasterData)}}function Ce(e,t){if(1&e&&(g["\u0275\u0275elementStart"](0,"span",77),g["\u0275\u0275text"](1),g["\u0275\u0275elementEnd"]()),2&e){const e=g["\u0275\u0275nextContext"]().$implicit;g["\u0275\u0275advance"](1),g["\u0275\u0275textInterpolate2"](" ",(null==e?null:e.count)||0," - ",(null==e?null:e.name)||""," ")}}const me=function(e,t){return{"selected-stage":e,"unselected-stage":t}},fe=function(e,t){return{"selected-stage-text":e,"unselected-stage-text":t}};function ve(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275elementStart"](1,"div",71),g["\u0275\u0275listener"]("click",(function(){g["\u0275\u0275restoreView"](e);const n=t.index;return g["\u0275\u0275nextContext"](6).switchToSelectedOnboardingStatus(!1,!0,n)})),g["\u0275\u0275elementStart"](2,"div",72),g["\u0275\u0275element"](3,"div",73),g["\u0275\u0275pipe"](4,"svgSecurityBypass"),g["\u0275\u0275elementStart"](5,"div",24),g["\u0275\u0275text"](6),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](7,"div",74),g["\u0275\u0275text"](8),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275template"](9,Ce,2,2,"ng-template",75,76,g["\u0275\u0275templateRefExtractor"]),g["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=t.$implicit,n=t.index,i=g["\u0275\u0275reference"](10),a=g["\u0275\u0275nextContext"](6);g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngClass",g["\u0275\u0275pureFunction2"](9,me,a.currentSelectedOnboardingStatusIndex==n,a.currentSelectedOnboardingStatusIndex!=n))("tooltip",i)("options",a.customTooltipOptions),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("innerHTML",g["\u0275\u0275pipeBind1"](4,7,e.icon),g["\u0275\u0275sanitizeHtml"]),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("ngClass",g["\u0275\u0275pureFunction2"](12,fe,a.currentSelectedOnboardingStatusIndex==n,a.currentSelectedOnboardingStatusIndex!=n)),g["\u0275\u0275advance"](1),g["\u0275\u0275textInterpolate1"](" ",(null==e?null:e.count)||0," "),g["\u0275\u0275advance"](2),g["\u0275\u0275textInterpolate1"](" ",(null==e?null:e.name)||""," ")}}function xe(e,t){if(1&e&&(g["\u0275\u0275elementStart"](0,"div",70),g["\u0275\u0275elementStart"](1,"app-previous-next-scroll-carousel"),g["\u0275\u0275template"](2,ve,11,15,"ng-container",19),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()),2&e){const e=g["\u0275\u0275nextContext"](5);g["\u0275\u0275property"]("ngStyle",g["\u0275\u0275pureFunction1"](2,de,e.isSectionLoading?"none":"")),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("ngForOf",e.candidatesCountBasedOnStatus)}}const be=function(e,t){return[e,t,0,0,"B"]};function ye(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"app-list-view",80),g["\u0275\u0275listener"]("onCheckboxValueChanges",(function(t){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](6).onCandidatesCheckboxValueChanges(t)}))("onChangeToggleAll",(function(t){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](6).onCandidatesChangeToggleAll(t)}))("onScroll",(function(){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](6).onCandidatesDataScroll()}))("onClick",(function(t){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](6).onClickRowData(t)})),g["\u0275\u0275pipe"](1,"access"),g["\u0275\u0275pipe"](2,"access"),g["\u0275\u0275elementEnd"]()}if(2&e){const e=g["\u0275\u0275nextContext"](6);g["\u0275\u0275property"]("list",e.candidatesData)("fieldConfig",e.candidatesFieldConfig)("variant",2)("headerHeight","32px")("totalCount",e.currentSelectedOnboardingStatusCount)("isAllChecked",e.isCandidatesAllChecked)("bulkSelectActive",g["\u0275\u0275pipeBindV"](1,8,g["\u0275\u0275pureFunction2"](20,be,e.access.moduleId.onboarding,e.access.subModuleId.onboardingCandidates)))("isCheckboxActive",g["\u0275\u0275pipeBindV"](2,14,g["\u0275\u0275pureFunction2"](23,be,e.access.moduleId.onboarding,e.access.subModuleId.onboardingCandidates)))}}function Se(e,t){1&e&&(g["\u0275\u0275elementStart"](0,"div",81),g["\u0275\u0275element"](1,"img",82),g["\u0275\u0275elementStart"](2,"div",83),g["\u0275\u0275text"](3,"No Candidates Found"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]())}function Oe(e,t){if(1&e&&(g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275template"](1,ye,3,26,"app-list-view",78),g["\u0275\u0275template"](2,Se,4,0,"div",79),g["\u0275\u0275elementContainerEnd"]()),2&e){const e=g["\u0275\u0275nextContext"](5);g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",e.candidatesData&&e.candidatesData.length>0),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",0==e.candidatesData.length)}}function Me(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"app-list-view",80),g["\u0275\u0275listener"]("onCheckboxValueChanges",(function(t){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](6).onGroupsCheckboxValueChanges(t)}))("onChangeToggleAll",(function(t){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](6).onGroupsChangeToggleAll(t)}))("onScroll",(function(){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](6).onGroupsDataScroll()}))("onClick",(function(t){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](6).onClickRowData(t)})),g["\u0275\u0275pipe"](1,"access"),g["\u0275\u0275pipe"](2,"access"),g["\u0275\u0275elementEnd"]()}if(2&e){const e=g["\u0275\u0275nextContext"](6);g["\u0275\u0275property"]("list",e.groupsData)("fieldConfig",e.groupsFieldConfig)("variant",2)("headerHeight","32px")("totalCount",e.totalGroupsCount)("isAllChecked",e.isGroupsAllChecked)("bulkSelectActive",g["\u0275\u0275pipeBindV"](1,8,g["\u0275\u0275pureFunction2"](20,be,e.access.moduleId.onboarding,e.access.subModuleId.onboardingCandidates)))("isCheckboxActive",g["\u0275\u0275pipeBindV"](2,14,g["\u0275\u0275pureFunction2"](23,be,e.access.moduleId.onboarding,e.access.subModuleId.onboardingCandidates)))}}function _e(e,t){1&e&&(g["\u0275\u0275elementStart"](0,"div",81),g["\u0275\u0275element"](1,"img",82),g["\u0275\u0275elementStart"](2,"div",83),g["\u0275\u0275text"](3,"No Groups Found"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]())}function we(e,t){if(1&e&&(g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275template"](1,Me,3,26,"app-list-view",78),g["\u0275\u0275template"](2,_e,4,0,"div",79),g["\u0275\u0275elementContainerEnd"]()),2&e){const e=g["\u0275\u0275nextContext"](5);g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",e.groupsData&&e.groupsData.length>0),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",0==e.groupsData.length)}}function ke(e,t){if(1&e&&(g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275template"](1,pe,17,26,"div",28),g["\u0275\u0275template"](2,ue,2,17,"div",29),g["\u0275\u0275template"](3,he,2,13,"div",29),g["\u0275\u0275template"](4,xe,3,4,"div",30),g["\u0275\u0275template"](5,Oe,3,2,"ng-container",1),g["\u0275\u0275template"](6,we,3,2,"ng-container",1),g["\u0275\u0275elementContainerEnd"]()),2&e){const e=g["\u0275\u0275nextContext"](4);g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",!e.isCandidatesToolbarVisible&&!e.isGroupsToolbarVisible),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",e.isCandidatesToolbarVisible&&1==e.selectedCandidateViewType),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",e.isGroupsToolbarVisible&&2==e.selectedCandidateViewType),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",1==e.selectedCandidateViewType),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",!e.isSectionLoading&&1==e.selectedCandidateViewType),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",!e.isSectionLoading&&2==e.selectedCandidateViewType)}}function Pe(e,t){1&e&&(g["\u0275\u0275namespaceSVG"](),g["\u0275\u0275elementStart"](0,"svg",50),g["\u0275\u0275element"](1,"path",51),g["\u0275\u0275elementEnd"]())}function De(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275namespaceSVG"](),g["\u0275\u0275elementStart"](0,"svg",52),g["\u0275\u0275listener"]("click",(function(t){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](7).onEnterGroupCandidateSearch("",t)})),g["\u0275\u0275elementStart"](1,"g",53),g["\u0275\u0275element"](2,"path",54),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](3,"defs"),g["\u0275\u0275elementStart"](4,"clipPath",55),g["\u0275\u0275element"](5,"rect",56),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}}function Ee(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div",43,89),g["\u0275\u0275listener"]("click",(function(){g["\u0275\u0275restoreView"](e);const t=g["\u0275\u0275reference"](2);return g["\u0275\u0275nextContext"](6).openGroupCandidateSearchBarOverlay(t,-37,34)})),g["\u0275\u0275elementStart"](3,"div",45),g["\u0275\u0275elementStart"](4,"input",46),g["\u0275\u0275listener"]("ngModelChange",(function(t){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](6).groupCandidateSearchParams=t}))("keydown.backspace",(function(){g["\u0275\u0275restoreView"](e);const t=g["\u0275\u0275nextContext"](6);return t.onEnterGroupCandidateSearch(t.groupCandidateSearchParams)})),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](5,"div",47),g["\u0275\u0275template"](6,Pe,2,0,"svg",48),g["\u0275\u0275template"](7,De,6,0,"svg",49),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}if(2&e){const e=g["\u0275\u0275nextContext"](6);g["\u0275\u0275advance"](4),g["\u0275\u0275property"]("ngModel",e.groupCandidateSearchParams),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("ngIf",""==e.groupCandidateSearchParams),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",e.groupCandidateSearchParams&&""!=e.groupCandidateSearchParams)}}function Ie(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div",57,89),g["\u0275\u0275listener"]("click",(function(){g["\u0275\u0275restoreView"](e);const t=g["\u0275\u0275reference"](2);return g["\u0275\u0275nextContext"](6).openGroupCandidateSearchBarOverlay(t,-30,40)})),g["\u0275\u0275namespaceSVG"](),g["\u0275\u0275elementStart"](3,"svg",50),g["\u0275\u0275element"](4,"path",51),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}}function Ve(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div",85),g["\u0275\u0275elementStart"](1,"div",86),g["\u0275\u0275elementStart"](2,"mat-icon",87),g["\u0275\u0275listener"]("click",(function(){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](5).goBackToCandidates()})),g["\u0275\u0275text"](3," arrow_back "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](4,"div",41),g["\u0275\u0275text"](5),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](6,"div",42),g["\u0275\u0275text"](7),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](8,"div",88),g["\u0275\u0275template"](9,Ee,8,3,"div",36),g["\u0275\u0275template"](10,Ie,5,0,"div",37),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}if(2&e){const e=g["\u0275\u0275nextContext"](5);g["\u0275\u0275advance"](5),g["\u0275\u0275textInterpolate1"](" ",e.totalGroupCandidateCount," "),g["\u0275\u0275advance"](2),g["\u0275\u0275textInterpolate1"](" ",e.currentSelectedGroupName," "),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("ngIf",""!=e.groupCandidateSearchParams&&null!=e.groupCandidateSearchParams),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",""==e.groupCandidateSearchParams||null==e.groupCandidateSearchParams)}}function Te(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div",67),g["\u0275\u0275elementStart"](1,"app-toolbar",90),g["\u0275\u0275listener"]("onClose",(function(t){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](5).onCloseGroupCandidateToolbar(t)})),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}if(2&e){const e=g["\u0275\u0275nextContext"](5);g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("module","onboardingDetailsCandidate")("height","44px")("selectedDetails",e.selectedGroupCandidateDetails)("unselectedDetails",e.unselectedGroupCandidateDetails)("toolbarConfig",e.groupDetailCandidatesToolbarConfig)("count",e.selectedGroupCandidateCount)("uiTextConfig",e.uiTextConfig)("generalUiConfig",e.generalUiConfig)("isBulkSelectActive",e.isGroupCandidateAllChecked)("searchQuery",e.groupCandidateSearchParams)("onboardingStatusMasterData",e.onboardingStatusMasterData)("checklistsPriorityMasterData",e.checklistsPriorityMasterData)("checklistMasterData",e.checklistMasterData)("employeeMasterData",e.employeeMasterData)("tagsMasterData",e.tagsMasterData)("groupsMasterData",e.groupsMasterData)("currentSelectedGroupId",e.currentSelectedGroupId)}}function Fe(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"app-list-view",80),g["\u0275\u0275listener"]("onCheckboxValueChanges",(function(t){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](6).onGroupCandidateCheckboxValueChanges(t)}))("onChangeToggleAll",(function(t){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](6).onGroupCandidateChangeToggleAll(t)}))("onScroll",(function(){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](6).onGroupCandidateDataScroll()}))("onClick",(function(t){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](6).onClickRowData(t)})),g["\u0275\u0275pipe"](1,"access"),g["\u0275\u0275pipe"](2,"access"),g["\u0275\u0275elementEnd"]()}if(2&e){const e=g["\u0275\u0275nextContext"](6);g["\u0275\u0275property"]("list",e.groupDetailCandidateData)("fieldConfig",e.groupDetailCandidateFieldConfig)("variant",2)("headerHeight","32px")("totalCount",e.totalGroupCandidateCount)("isAllChecked",e.isGroupCandidateAllChecked)("bulkSelectActive",g["\u0275\u0275pipeBindV"](1,8,g["\u0275\u0275pureFunction2"](20,be,e.access.moduleId.onboarding,e.access.subModuleId.onboardingCandidates)))("isCheckboxActive",g["\u0275\u0275pipeBindV"](2,14,g["\u0275\u0275pureFunction2"](23,be,e.access.moduleId.onboarding,e.access.subModuleId.onboardingCandidates)))}}function Le(e,t){1&e&&(g["\u0275\u0275elementStart"](0,"div",81),g["\u0275\u0275element"](1,"img",82),g["\u0275\u0275elementStart"](2,"div",83),g["\u0275\u0275text"](3,"No Candidates Found"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]())}function Ge(e,t){if(1&e&&(g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275template"](1,Fe,3,26,"app-list-view",78),g["\u0275\u0275template"](2,Le,4,0,"div",79),g["\u0275\u0275elementContainerEnd"]()),2&e){const e=g["\u0275\u0275nextContext"](5);g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",e.groupDetailCandidateData&&e.groupDetailCandidateData.length>0),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",0==e.groupDetailCandidateData.length)}}function Ae(e,t){if(1&e&&(g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275template"](1,Ve,11,4,"div",84),g["\u0275\u0275template"](2,Te,2,17,"div",29),g["\u0275\u0275template"](3,Ge,3,2,"ng-container",1),g["\u0275\u0275elementContainerEnd"]()),2&e){const e=g["\u0275\u0275nextContext"](4);g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",!e.isGroupCandidateToolbarVisible),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",e.isGroupCandidateToolbarVisible),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",!e.isSectionLoading)}}function je(e,t){if(1&e&&(g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275template"](1,ke,7,6,"ng-container",1),g["\u0275\u0275template"](2,Ae,4,3,"ng-container",1),g["\u0275\u0275elementContainerEnd"]()),2&e){const e=g["\u0275\u0275nextContext"](3);g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf","candidate-view"==e.selectedOnboardingCandidatesView),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf","group-detail-view"==e.selectedOnboardingCandidatesView)}}function Re(e,t){if(1&e&&(g["\u0275\u0275elementStart"](0,"div",41),g["\u0275\u0275text"](1),g["\u0275\u0275elementEnd"]()),2&e){const e=g["\u0275\u0275nextContext"](5);g["\u0275\u0275advance"](1),g["\u0275\u0275textInterpolate1"](" ",e.totalChecklistsCount," ")}}function He(e,t){1&e&&(g["\u0275\u0275namespaceSVG"](),g["\u0275\u0275elementStart"](0,"svg",50),g["\u0275\u0275element"](1,"path",51),g["\u0275\u0275elementEnd"]())}function ze(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275namespaceSVG"](),g["\u0275\u0275elementStart"](0,"svg",52),g["\u0275\u0275listener"]("click",(function(t){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](6).onEnterChecklistsSearch("",t)})),g["\u0275\u0275elementStart"](1,"g",53),g["\u0275\u0275element"](2,"path",54),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](3,"defs"),g["\u0275\u0275elementStart"](4,"clipPath",55),g["\u0275\u0275element"](5,"rect",56),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}}function Be(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div",43,96),g["\u0275\u0275listener"]("click",(function(){g["\u0275\u0275restoreView"](e);const t=g["\u0275\u0275reference"](2);return g["\u0275\u0275nextContext"](5).openChecklistsSearchBarOverlay(t,-37,0)})),g["\u0275\u0275elementStart"](3,"div",45),g["\u0275\u0275elementStart"](4,"input",46),g["\u0275\u0275listener"]("ngModelChange",(function(t){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](5).checklistsSearchParams=t}))("keydown.backspace",(function(){g["\u0275\u0275restoreView"](e);const t=g["\u0275\u0275nextContext"](5);return t.onEnterChecklistsSearch(t.checklistsSearchParams)})),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](5,"div",47),g["\u0275\u0275template"](6,He,2,0,"svg",48),g["\u0275\u0275template"](7,ze,6,0,"svg",49),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}if(2&e){const e=g["\u0275\u0275nextContext"](5);g["\u0275\u0275advance"](4),g["\u0275\u0275property"]("ngModel",e.checklistsSearchParams),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("ngIf",""==e.checklistsSearchParams),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",e.checklistsSearchParams&&""!=e.checklistsSearchParams)}}function Ne(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div",57,96),g["\u0275\u0275listener"]("click",(function(){g["\u0275\u0275restoreView"](e);const t=g["\u0275\u0275reference"](2);return g["\u0275\u0275nextContext"](5).openChecklistsSearchBarOverlay(t,-30,0)})),g["\u0275\u0275namespaceSVG"](),g["\u0275\u0275elementStart"](3,"svg",50),g["\u0275\u0275element"](4,"path",51),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}}function Ze(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div",92),g["\u0275\u0275elementStart"](1,"div",93),g["\u0275\u0275template"](2,Re,2,1,"div",33),g["\u0275\u0275elementStart"](3,"div",42),g["\u0275\u0275text"](4),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](5,"div",94),g["\u0275\u0275template"](6,Be,8,3,"div",36),g["\u0275\u0275template"](7,Ne,5,0,"div",37),g["\u0275\u0275elementStart"](8,"div",57,95),g["\u0275\u0275listener"]("click",(function(){g["\u0275\u0275restoreView"](e);const t=g["\u0275\u0275reference"](10);return g["\u0275\u0275nextContext"](4).openChecklistsCustomizationOverlay(t)})),g["\u0275\u0275namespaceSVG"](),g["\u0275\u0275elementStart"](11,"svg",61),g["\u0275\u0275element"](12,"path",62),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}if(2&e){const e=g["\u0275\u0275nextContext"](4);g["\u0275\u0275property"]("ngStyle",g["\u0275\u0275pureFunction1"](5,de,e.isSectionLoading?"none":"")),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("ngIf",!e.isSectionLoading),g["\u0275\u0275advance"](2),g["\u0275\u0275textInterpolate1"](" ",e.totalChecklistsCount&&e.totalChecklistsCount-1?"Checklists":"Checklist"," "),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("ngIf",""!=e.checklistsSearchParams&&null!=e.checklistsSearchParams),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",""==e.checklistsSearchParams||null==e.checklistsSearchParams)}}function $e(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div",67),g["\u0275\u0275elementStart"](1,"app-toolbar",97),g["\u0275\u0275listener"]("onClose",(function(t){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](4).onCloseChecklistsToolbar(t)})),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}if(2&e){const e=g["\u0275\u0275nextContext"](4);g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("module","candidateChecklists")("height","44px")("selectedDetails",e.selectedChecklistsDetails)("unselectedDetails",e.unselectedChecklistsDetails)("toolbarConfig",e.checklistsToolbarConfig)("count",e.selectedChecklistsCount)("uiTextConfig",e.uiTextConfig)("isBulkSelectActive",e.isChecklistsAllChecked)("searchQuery",e.checklistsSearchParams)("checklistsPriorityMasterData",e.checklistsPriorityMasterData)}}function We(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"app-list-view",99),g["\u0275\u0275listener"]("onCheckboxValueChanges",(function(t){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](5).onChecklistsCheckboxValueChanges(t)}))("onChangeToggleAll",(function(t){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](5).onChecklistsChangeToggleAll(t)}))("onScroll",(function(){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](5).onChecklistDataScroll()}))("onClick",(function(t){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](5).onClickRowData(t)})),g["\u0275\u0275pipe"](1,"access"),g["\u0275\u0275pipe"](2,"access"),g["\u0275\u0275elementEnd"]()}if(2&e){const e=g["\u0275\u0275nextContext"](5);g["\u0275\u0275property"]("list",e.checklistsData)("fieldConfig",e.checklistsFieldConfig)("uiTextConfig",e.uiTextConfig)("variant",2)("headerHeight","32px")("totalCount",e.totalChecklistsCount)("isAllChecked",e.isChecklistsAllChecked)("bulkSelectActive",g["\u0275\u0275pipeBindV"](1,10,g["\u0275\u0275pureFunction2"](22,be,e.access.moduleId.onboarding,e.access.subModuleId.onboardingChecklists)))("isCheckboxActive",g["\u0275\u0275pipeBindV"](2,16,g["\u0275\u0275pureFunction2"](25,be,e.access.moduleId.onboarding,e.access.subModuleId.onboardingChecklists)))("checklistsPriorityMasterData",e.checklistsPriorityMasterData)}}function Ye(e,t){1&e&&(g["\u0275\u0275elementStart"](0,"div",81),g["\u0275\u0275element"](1,"img",82),g["\u0275\u0275elementStart"](2,"div",83),g["\u0275\u0275text"](3,"Assigned Checklists Not Found"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]())}function Ue(e,t){if(1&e&&(g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275template"](1,We,3,28,"app-list-view",98),g["\u0275\u0275template"](2,Ye,4,0,"div",79),g["\u0275\u0275elementContainerEnd"]()),2&e){const e=g["\u0275\u0275nextContext"](4);g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",e.checklistsData&&e.checklistsData.length>0),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",0==e.checklistsData.length)}}function Qe(e,t){if(1&e&&(g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275template"](1,Ze,13,7,"div",91),g["\u0275\u0275template"](2,$e,2,10,"div",29),g["\u0275\u0275template"](3,Ue,3,2,"ng-container",1),g["\u0275\u0275elementContainerEnd"]()),2&e){const e=g["\u0275\u0275nextContext"](3);g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",!e.isChecklistsToolbarVisible),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",e.isChecklistsToolbarVisible),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",!e.isSectionLoading)}}function Xe(e,t){if(1&e&&(g["\u0275\u0275elementStart"](0,"div",100),g["\u0275\u0275elementStart"](1,"div",12),g["\u0275\u0275element"](2,"img",13),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](3,"div",14),g["\u0275\u0275elementStart"](4,"div",15),g["\u0275\u0275text"](5,"Loading..."),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()),2&e){const e=g["\u0275\u0275nextContext"](3);g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("src",e.uiTextConfig["UI-LOADING-001"],g["\u0275\u0275sanitizeUrl"])}}const qe=function(){return[12,13,1,2,5,6]};function Ke(e,t){if(1&e&&(g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275elementStart"](1,"div",101),g["\u0275\u0275element"](2,"app-history-logs",102),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementContainerEnd"]()),2&e){const e=g["\u0275\u0275nextContext"](3);g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("module","onboarding")("displayHeader",!0)("displayDateRangeFilter",!0)("displaySearch",!0)("showDateRangeCalendar",!0)("dateRangeFilterValue",g["\u0275\u0275pureFunction0"](8,qe))("defaultDateRangeFilter",1)("uiTextConfig",e.uiTextConfig)}}const Je=function(e,t){return{"selected-text":e,"unselected-text":t}};function et(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div",16),g["\u0275\u0275elementStart"](1,"div",17),g["\u0275\u0275elementStart"](2,"div",18),g["\u0275\u0275template"](3,Y,6,13,"ng-container",19),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](4,"div",18),g["\u0275\u0275elementStart"](5,"div",20),g["\u0275\u0275listener"]("click",(function(){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](2).displayHistoryLogs=!0})),g["\u0275\u0275namespaceSVG"](),g["\u0275\u0275elementStart"](6,"svg",21),g["\u0275\u0275element"](7,"path",22),g["\u0275\u0275element"](8,"path",23),g["\u0275\u0275elementEnd"](),g["\u0275\u0275namespaceHTML"](),g["\u0275\u0275elementStart"](9,"div",24),g["\u0275\u0275text"](10," History Log "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](11,"div",25),g["\u0275\u0275template"](12,je,3,2,"ng-container",1),g["\u0275\u0275template"](13,Qe,4,3,"ng-container",1),g["\u0275\u0275template"](14,Xe,6,1,"div",26),g["\u0275\u0275template"](15,Ke,3,9,"ng-container",1),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}if(2&e){const e=g["\u0275\u0275nextContext"](2);g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngStyle",g["\u0275\u0275pureFunction1"](9,de,e.isSectionLoading?"none":"")),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("ngForOf",e.menuList),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("ngClass",g["\u0275\u0275pureFunction2"](11,$,e.displayHistoryLogs,!e.displayHistoryLogs)),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngStyle",g["\u0275\u0275pureFunction1"](14,W,e.displayHistoryLogs?"#111434":"#8B95A5")),g["\u0275\u0275advance"](3),g["\u0275\u0275property"]("ngClass",g["\u0275\u0275pureFunction2"](16,Je,e.displayHistoryLogs,!e.displayHistoryLogs)),g["\u0275\u0275advance"](3),g["\u0275\u0275property"]("ngIf",!e.displayHistoryLogs&&2==e.selectedMenu),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",!e.displayHistoryLogs&&3==e.selectedMenu),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",e.isSectionLoading),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",e.displayHistoryLogs)}}function tt(e,t){if(1&e&&(g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275template"](1,Z,6,1,"div",0),g["\u0275\u0275template"](2,et,16,19,"div",11),g["\u0275\u0275elementContainerEnd"]()),2&e){const e=g["\u0275\u0275nextContext"]();g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",e.isLoading),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",!e.isLoading)}}function nt(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"app-column-customization",103),g["\u0275\u0275listener"]("onApply",(function(t){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"]().onApplyChecklistColumnCustomization(t)})),g["\u0275\u0275elementEnd"]()}if(2&e){const e=g["\u0275\u0275nextContext"]();g["\u0275\u0275property"]("customization",e.checklistsFieldConfig)}}const it=function(){return[]};function at(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"app-search-overlay",104),g["\u0275\u0275listener"]("onEnter",(function(t){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"]().onEnterChecklistsSearch(t)})),g["\u0275\u0275elementEnd"]()}if(2&e){const e=g["\u0275\u0275nextContext"]();g["\u0275\u0275property"]("currentSearchText",e.checklistsSearchParams)("recentSearch",g["\u0275\u0275pureFunction0"](2,it))}}function ot(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"app-column-customization",103),g["\u0275\u0275listener"]("onApply",(function(t){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"]().onApplyCandidateColumnCustomization(t)})),g["\u0275\u0275elementEnd"]()}if(2&e){const e=g["\u0275\u0275nextContext"]();g["\u0275\u0275property"]("customization",e.candidatesFieldConfig)}}function rt(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"app-search-overlay",104),g["\u0275\u0275listener"]("onEnter",(function(t){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"]().onEnterCandidatesSearch(t)})),g["\u0275\u0275elementEnd"]()}if(2&e){const e=g["\u0275\u0275nextContext"]();g["\u0275\u0275property"]("currentSearchText",e.candidatesSearchParams)("recentSearch",g["\u0275\u0275pureFunction0"](2,it))}}function st(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"app-column-customization",103),g["\u0275\u0275listener"]("onApply",(function(t){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"]().onApplyGroupColumnCustomization(t)})),g["\u0275\u0275elementEnd"]()}if(2&e){const e=g["\u0275\u0275nextContext"]();g["\u0275\u0275property"]("customization",e.groupsFieldConfig)}}function ct(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"app-search-overlay",104),g["\u0275\u0275listener"]("onEnter",(function(t){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"]().onEnterGroupsSearch(t)})),g["\u0275\u0275elementEnd"]()}if(2&e){const e=g["\u0275\u0275nextContext"]();g["\u0275\u0275property"]("currentSearchText",e.groupsSearchParams)("recentSearch",g["\u0275\u0275pureFunction0"](2,it))}}function lt(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"app-search-overlay",104),g["\u0275\u0275listener"]("onEnter",(function(t){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"]().onEnterGroupCandidateSearch(t)})),g["\u0275\u0275elementEnd"]()}if(2&e){const e=g["\u0275\u0275nextContext"]();g["\u0275\u0275property"]("currentSearchText",e.groupCandidateSearchParams)("recentSearch",g["\u0275\u0275pureFunction0"](2,it))}}const dt=function(e){return[e,0,0,0,"V"]};let gt=(()=>{class e{constructor(e,t,n,i,a,o,s,c,l,g){this._atsMasterService=e,this._utilService=t,this._onboardingService=n,this._loginService=i,this._toaster=a,this._overlay=o,this._viewContainerRef=s,this._dialog=c,this._router=l,this._route=g,this._onDestroy=new r.b,this.access=d,this.menuList=[],this.uiTextConfig={},this.generalUiConfig={},this.checklistsPriorityMasterData=[],this.onboardingStatusMasterData=[],this.employeeMasterData=[],this.checklistMasterData=[],this.tagsMasterData=[],this.groupsMasterData=[],this.groupTagsMasterData=[],this.checklistsData=[],this.checklistsFieldConfig=[],this.checklistsToolbarConfig=[],this.selectedChecklistsDetails=[],this.unselectedChecklistsDetails=[],this.selectedChecklistsCount=0,this.totalChecklistsCount=0,this.isChecklistsToolbarVisible=!1,this.isChecklistsAllChecked=!1,this.checklistsSearchParams="",this.checklistsSkip=0,this.checklistsLimit=15,this.candidatesData=[],this.candidatesFieldConfig=[],this.candidatesToolbarConfig=[],this.selectedCandidatesDetails=[],this.unselectedCandidatesDetails=[],this.candidatesCountBasedOnStatus=[],this.currentSelectedOnboardingStatusCandidates=[],this.currentSelectedOnboardingStatusId=0,this.selectedCandidatesCount=0,this.totalCandidatesCount=0,this.currentSelectedOnboardingStatusCount=0,this.currentSelectedOnboardingStatusIndex=null,this.isCandidatesToolbarVisible=!1,this.isCandidatesAllChecked=!1,this.candidatesSearchParams="",this.candidateSkip=0,this.candidateLimit=15,this.groupsData=[],this.groupsFieldConfig=[],this.groupsToolbarConfig=[],this.selectedGroupsDetails=[],this.unselectedGroupsDetails=[],this.selectedGroupsCount=0,this.totalGroupsCount=0,this.isGroupsToolbarVisible=!1,this.isGroupsAllChecked=!1,this.groupsSearchParams="",this.groupSkip=0,this.groupLimit=15,this.currentSelectedGroupId=null,this.currentSelectedGroupName=null,this.groupDetailCandidateData=[],this.groupDetailCandidateFieldConfig=[],this.groupDetailCandidatesToolbarConfig=[],this.selectedGroupCandidateDetails=[],this.unselectedGroupCandidateDetails=[],this.selectedGroupCandidateCount=0,this.totalGroupCandidateCount=0,this.isGroupCandidateToolbarVisible=!1,this.isGroupCandidateAllChecked=!1,this.groupCandidateSearchParams="",this.groupCandidateSkip=0,this.groupCandidateLimit=15,this.currentSelectedCandidateId=null,this.selectedMenu=null,this.selectedOnboardingCandidatesView="candidate-view",this.selectedCandidateViewType=1,this.candidateViewList=[{id:1,icon:"person",class:"toggle-button-icon-2"},{id:2,icon:"groups",class:"toggle-button-icon-1"}],this.isLoading=!0,this.isSectionLoading=!0,this.displayHistoryLogs=!1,this.customTooltipOptions={placement:"bottom"}}ngOnInit(){return Object(o.c)(this,void 0,void 0,(function*(){this.calculateDynamicContentHeight(),this.getCandidateOnboardingChecklistPrioriyMaster(),this.getCandidateOnboardingStatusMaster(),this.getAssociateDetailsInSystem(),this.getChecklistMaster(),yield this.getAtsMasterUiConfig("onboardingLandingPage"),this.getAtsMasterUiConfig("generalUiConfig"),this.isLoading=!1,this.menuList.length>0&&(yield this.switchToSelectedMenu(0))}))}onResize(){this.calculateDynamicContentHeight()}calculateDynamicContentHeight(){document.documentElement.style.setProperty("--dynamicHeight",window.innerHeight-57+"px"),3==this.selectedMenu?document.documentElement.style.setProperty("--dynamicSubHeight",window.innerHeight-57-136+"px"):2==this.selectedMenu&&(1==this.selectedCandidateViewType?document.documentElement.style.setProperty("--dynamicSubHeight",window.innerHeight-57-200+"px"):document.documentElement.style.setProperty("--dynamicSubHeight",window.innerHeight-57-136+"px")),document.documentElement.style.setProperty("--dynamicAccessDeniedHeight",window.innerHeight-57+"px"),document.documentElement.style.setProperty("--dynamicActivityLogHeight",window.innerHeight-147+"px"),document.documentElement.style.setProperty("--dynamicActivityLogSubHeight",window.innerHeight-182+"px")}appendChecklistLocalStorageConfig(e){let t=JSON.parse(localStorage.getItem("onboarding-candidate-checklist-customization-"+this._loginService.getProfile().profile.aid));if(t&&t.length>0){let n=c.pluck(e,"key"),i=c.pluck(t,"key");return n.every(e=>i.includes(e))?t:(localStorage.removeItem("onboarding-candidate-checklist-customization-"+this._loginService.getProfile().profile.aid),e)}return e}appendCandidateLocalStorageConfig(e){let t=JSON.parse(localStorage.getItem("onboarding-candidate-customization-"+this._loginService.getProfile().profile.aid));if(t&&t.length>0){let n=c.pluck(e,"key"),i=c.pluck(t,"key");return n.every(e=>i.includes(e))?t:(localStorage.removeItem("onboarding-candidate-customization-"+this._loginService.getProfile().profile.aid),e)}return e}appendGroupLocalStorageConfig(e){let t=JSON.parse(localStorage.getItem("onboarding-group-customization-"+this._loginService.getProfile().profile.aid));if(t&&t.length>0){let n=c.pluck(e,"key"),i=c.pluck(t,"key");return n.every(e=>i.includes(e))?t:(localStorage.removeItem("onboarding-group-customization-"+this._loginService.getProfile().profile.aid),e)}return e}switchToSelectedMenu(e){return Object(o.c)(this,void 0,void 0,(function*(){this.displayHistoryLogs=!1,this.menuList.forEach(e=>e.isSelected=!1),this.menuList[e].isSelected=!0,this.selectedMenu=this.menuList[e].id,this.calculateDynamicContentHeight(),1==this.selectedMenu?this.isSectionLoading=!1:2==this.selectedMenu?(this.isCandidatesToolbarVisible=!1,this.isCandidatesAllChecked=!1,this.isGroupsToolbarVisible=!1,this.isGroupsAllChecked=!1,this.isGroupCandidateToolbarVisible=!1,this.isGroupCandidateAllChecked=!1,this.selectedOnboardingCandidatesView="candidate-view",1==this.selectedCandidateViewType?yield this.switchToSelectedOnboardingStatus(!0,!0,0):yield this.getCandidateGroupsOnboardingDetails(!0)):3==this.selectedMenu?(this.isChecklistsToolbarVisible=!1,this.isChecklistsAllChecked=!1,this.getCandidateChecklistDetails(!0)):4==this.selectedMenu&&(this.isSectionLoading=!1)}))}openChecklistsCustomizationOverlay(e){var t;if(!(null===(t=this.overlayRef)||void 0===t?void 0:t.hasAttached())){const t=this._overlay.position().flexibleConnectedTo(e).withFlexibleDimensions(!0).withPush(!0).withViewportMargin(25).withGrowAfterOpen(!0).withPositions([{originX:"center",originY:"bottom",overlayX:"center",overlayY:"top"}]);t.withDefaultOffsetY(20);const n=this._overlay.scrollStrategies.close();this.overlayRef=this._overlay.create({positionStrategy:t,scrollStrategy:n,hasBackdrop:!0,panelClass:["pop-up"]});const i=new l.h(this.triggerChecklistsColumnCustomizationTemplateRef,this._viewContainerRef);this.overlayRef.attach(i),this.overlayRef.backdropClick().subscribe(()=>{this.closeOverlay()})}}onApplyChecklistColumnCustomization(e){return Object(o.c)(this,void 0,void 0,(function*(){this.checklistsFieldConfig=[...e],localStorage.setItem("onboarding-candidate-checklist-customization-"+this._loginService.getProfile().profile.aid,JSON.stringify(this.checklistsFieldConfig)),this.closeOverlay()}))}openCandidatesCustomizationOverlay(e){var t;if(!(null===(t=this.overlayRef)||void 0===t?void 0:t.hasAttached())){const t=this._overlay.position().flexibleConnectedTo(e).withFlexibleDimensions(!0).withPush(!0).withViewportMargin(25).withGrowAfterOpen(!0).withPositions([{originX:"center",originY:"bottom",overlayX:"center",overlayY:"top"}]);t.withDefaultOffsetY(20);const n=this._overlay.scrollStrategies.close();this.overlayRef=this._overlay.create({positionStrategy:t,scrollStrategy:n,hasBackdrop:!0,panelClass:["pop-up"]});const i=new l.h(this.triggerCandidatesColumnCustomizationTemplateRef,this._viewContainerRef);this.overlayRef.attach(i),this.overlayRef.backdropClick().subscribe(()=>{this.closeOverlay()})}}onApplyCandidateColumnCustomization(e){return Object(o.c)(this,void 0,void 0,(function*(){this.candidatesFieldConfig=[...e],localStorage.setItem("onboarding-candidate-customization-"+this._loginService.getProfile().profile.aid,JSON.stringify(this.candidatesFieldConfig)),this.closeOverlay()}))}openGroupsCustomizationOverlay(e){var t;if(!(null===(t=this.overlayRef)||void 0===t?void 0:t.hasAttached())){const t=this._overlay.position().flexibleConnectedTo(e).withFlexibleDimensions(!0).withPush(!0).withViewportMargin(25).withGrowAfterOpen(!0).withPositions([{originX:"center",originY:"bottom",overlayX:"center",overlayY:"top"}]);t.withDefaultOffsetY(20);const n=this._overlay.scrollStrategies.close();this.overlayRef=this._overlay.create({positionStrategy:t,scrollStrategy:n,hasBackdrop:!0,panelClass:["pop-up"]});const i=new l.h(this.triggerGroupsColumnCustomizationTemplateRef,this._viewContainerRef);this.overlayRef.attach(i),this.overlayRef.backdropClick().subscribe(()=>{this.closeOverlay()})}}onApplyGroupColumnCustomization(e){return Object(o.c)(this,void 0,void 0,(function*(){this.groupsFieldConfig=[...e],localStorage.setItem("onboarding-group-customization-"+this._loginService.getProfile().profile.aid,JSON.stringify(this.groupsFieldConfig)),this.closeOverlay()}))}closeOverlay(){var e;null===(e=this.overlayRef)||void 0===e||e.dispose()}filterAndSortBasedOnPosition(e){return e.filter(e=>1==e.isActive).sort((function(e,t){return e.position-t.position}))}openChecklistsSearchBarOverlay(e,t,n){var i;if(!(null===(i=this.overlayRef)||void 0===i?void 0:i.hasAttached())){const i=this._overlay.position().flexibleConnectedTo(e).withFlexibleDimensions(!0).withPush(!0).withViewportMargin(25).withGrowAfterOpen(!0).withPositions([{originX:"start",originY:"bottom",overlayX:"start",overlayY:"top"}]);i.withDefaultOffsetY(t),n&&i.withDefaultOffsetX(n);const a=this._overlay.scrollStrategies.close();this.overlayRef=this._overlay.create({positionStrategy:i,scrollStrategy:a,hasBackdrop:!0,backdropClass:"",panelClass:["pop-up"]});const o=new l.h(this.triggerChecklistsSearchBarTemplateRef,this._viewContainerRef);this.overlayRef.attach(o),this.overlayRef.backdropClick().subscribe(()=>{this.closeOverlay()})}}onEnterChecklistsSearch(e,t){return Object(o.c)(this,void 0,void 0,(function*(){t&&(t.stopPropagation(),t.preventDefault()),this.checklistsSearchParams=e,this.closeOverlay(),this.isSectionLoading=!0,yield this.getCandidateChecklistDetails(!0),this.isSectionLoading=!1}))}onCloseChecklistsToolbar(e){return Object(o.c)(this,void 0,void 0,(function*(){this.isChecklistsToolbarVisible=!1,this.isChecklistsAllChecked=!1,this.checklistsData&&this.checklistsData.length>0&&this.checklistsData.forEach(e=>e.isChecked=!1),e&&this.getCandidateChecklistDetails(!0)}))}onChecklistsChangeToggleAll(e){this.isChecklistsAllChecked=e,this.checklistsData.forEach(e=>e.isChecked=this.isChecklistsAllChecked),this.isChecklistsToolbarVisible=!!this.isChecklistsAllChecked,this.selectedChecklistsDetails=this.checklistsData.filter(e=>1==e.isChecked)}onChecklistsCheckboxValueChanges(e){this.selectedChecklistsDetails=e.selectedDetails,this.unselectedChecklistsDetails=e.unselectedDetails,this.selectedChecklistsCount=e.selectedCount,this.isChecklistsAllChecked=e.isAllChecked,this.isChecklistsToolbarVisible=e.isToolbarActive}openCandidatesSearchBarOverlay(e,t,n){var i;if(!(null===(i=this.overlayRef)||void 0===i?void 0:i.hasAttached())){const i=this._overlay.position().flexibleConnectedTo(e).withFlexibleDimensions(!0).withPush(!0).withViewportMargin(25).withGrowAfterOpen(!0).withPositions([{originX:"start",originY:"bottom",overlayX:"start",overlayY:"top"}]);i.withDefaultOffsetY(t),n&&i.withDefaultOffsetX(n);const a=this._overlay.scrollStrategies.close();this.overlayRef=this._overlay.create({positionStrategy:i,scrollStrategy:a,hasBackdrop:!0,backdropClass:"",panelClass:["pop-up"]});const o=new l.h(this.triggerCandidatesSearchBarTemplateRef,this._viewContainerRef);this.overlayRef.attach(o),this.overlayRef.backdropClick().subscribe(()=>{this.closeOverlay()})}}onEnterCandidatesSearch(e,t){return Object(o.c)(this,void 0,void 0,(function*(){t&&(t.stopPropagation(),t.preventDefault()),this.candidatesSearchParams=e,this.closeOverlay(),yield this.switchToSelectedOnboardingStatus(!0,!0,this.currentSelectedOnboardingStatusIndex)}))}onCloseCandidatesToolbar(e){return Object(o.c)(this,void 0,void 0,(function*(){this.isCandidatesToolbarVisible=!1,this.isCandidatesAllChecked=!1,this.candidatesData&&this.candidatesData.length>0&&this.candidatesData.forEach(e=>e.isChecked=!1),e&&(yield this.switchToSelectedOnboardingStatus(!0,!0,this.currentSelectedOnboardingStatusIndex))}))}onCandidatesChangeToggleAll(e){this.isCandidatesAllChecked=e,this.candidatesData.forEach(e=>e.isChecked=this.isCandidatesAllChecked),this.isCandidatesToolbarVisible=!!this.isCandidatesAllChecked,this.selectedCandidatesDetails=this.candidatesData.filter(e=>1==e.isChecked)}onCandidatesCheckboxValueChanges(e){this.selectedCandidatesDetails=e.selectedDetails,this.unselectedCandidatesDetails=e.unselectedDetails,this.selectedCandidatesCount=e.selectedCount,this.isCandidatesAllChecked=e.isAllChecked,this.isCandidatesToolbarVisible=e.isToolbarActive}onChecklistDataScroll(){this.checklistsSkip+=this.checklistsLimit,this.getCandidateChecklistDetails(!1)}onClickRowData(e){return Object(o.c)(this,void 0,void 0,(function*(){e.functionName&&""!=e.functionName&&(yield this[e.functionName](e.data,e.index))}))}openDetailViewOfCandidate(e){return Object(o.c)(this,void 0,void 0,(function*(){this.currentSelectedCandidateId=(null==e?void 0:e.candidate_id)||null,this.currentSelectedCandidateId?this._router.navigate([this.currentSelectedCandidateId],{relativeTo:this._route}):this._toaster.showWarning("Warning \u26a0\ufe0f","Candidate ID Not Found!",7e3)}))}switchToSelectedOnboardingStatus(e,t,n){return Object(o.c)(this,void 0,void 0,(function*(){this.isCandidatesToolbarVisible=!1,this.isCandidatesAllChecked=!1,(e||t)&&(this.isSectionLoading=!0),this.currentSelectedOnboardingStatusIndex=n,e&&(this.getOnboardingTagsMaster(),this.getOnboardingGroupsMaster(),yield this.getCandidateOnboardingCount()),this.currentSelectedOnboardingStatusId=this.candidatesCountBasedOnStatus[n].id,this.currentSelectedOnboardingStatusCount=this.candidatesCountBasedOnStatus[n].count,this.currentSelectedOnboardingStatusCandidates=this.candidatesCountBasedOnStatus[n].candidateIds,(e||t)&&(this.candidateSkip=0,this.candidatesData=[]),yield this.getCandidateOnboardingDetails()}))}onCandidatesDataScroll(){this.candidateSkip+=this.candidateLimit,this.switchToSelectedOnboardingStatus(!1,!1,this.currentSelectedOnboardingStatusIndex)}openGroupsSearchBarOverlay(e,t,n){var i;if(!(null===(i=this.overlayRef)||void 0===i?void 0:i.hasAttached())){const i=this._overlay.position().flexibleConnectedTo(e).withFlexibleDimensions(!0).withPush(!0).withViewportMargin(25).withGrowAfterOpen(!0).withPositions([{originX:"start",originY:"bottom",overlayX:"start",overlayY:"top"}]);i.withDefaultOffsetY(t),n&&i.withDefaultOffsetX(n);const a=this._overlay.scrollStrategies.close();this.overlayRef=this._overlay.create({positionStrategy:i,scrollStrategy:a,hasBackdrop:!0,backdropClass:"",panelClass:["pop-up"]});const o=new l.h(this.triggerGroupsSearchBarTemplateRef,this._viewContainerRef);this.overlayRef.attach(o),this.overlayRef.backdropClick().subscribe(()=>{this.closeOverlay()})}}onEnterGroupsSearch(e,t){return Object(o.c)(this,void 0,void 0,(function*(){t&&(t.stopPropagation(),t.preventDefault()),this.groupsSearchParams=e,this.closeOverlay(),yield this.getCandidateGroupsOnboardingDetails(!0)}))}onCloseGroupsToolbar(e){return Object(o.c)(this,void 0,void 0,(function*(){this.isGroupsToolbarVisible=!1,this.isGroupsAllChecked=!1,this.groupsData&&this.groupsData.length>0&&this.groupsData.forEach(e=>e.isChecked=!1),e&&(yield this.getCandidateGroupsOnboardingDetails(!0))}))}onGroupsChangeToggleAll(e){this.isGroupsAllChecked=e,this.groupsData.forEach(e=>e.isChecked=this.isGroupsAllChecked),this.isGroupsToolbarVisible=!!this.isGroupsAllChecked,this.selectedGroupsDetails=this.groupsData.filter(e=>1==e.isChecked)}onGroupsCheckboxValueChanges(e){this.selectedGroupsDetails=e.selectedDetails,this.unselectedGroupsDetails=e.unselectedDetails,this.selectedGroupsCount=e.selectedCount,this.isGroupsAllChecked=e.isAllChecked,this.isGroupsToolbarVisible=e.isToolbarActive}onGroupsDataScroll(){this.groupSkip+=this.groupLimit,this.getCandidateGroupsOnboardingDetails(!1)}openGroupCandidateSearchBarOverlay(e,t,n){var i;if(!(null===(i=this.overlayRef)||void 0===i?void 0:i.hasAttached())){const i=this._overlay.position().flexibleConnectedTo(e).withFlexibleDimensions(!0).withPush(!0).withViewportMargin(25).withGrowAfterOpen(!0).withPositions([{originX:"start",originY:"bottom",overlayX:"start",overlayY:"top"}]);i.withDefaultOffsetY(t),n&&i.withDefaultOffsetX(n);const a=this._overlay.scrollStrategies.close();this.overlayRef=this._overlay.create({positionStrategy:i,scrollStrategy:a,hasBackdrop:!0,backdropClass:"",panelClass:["pop-up"]});const o=new l.h(this.triggerGroupCandidateSearchBarTemplateRef,this._viewContainerRef);this.overlayRef.attach(o),this.overlayRef.backdropClick().subscribe(()=>{this.closeOverlay()})}}onEnterGroupCandidateSearch(e,t){return Object(o.c)(this,void 0,void 0,(function*(){t&&(t.stopPropagation(),t.preventDefault()),this.groupCandidateSearchParams=e,this.closeOverlay(),yield this.getCandidatesInGroupData(!0)}))}onCloseGroupCandidateToolbar(e){return Object(o.c)(this,void 0,void 0,(function*(){this.isGroupCandidateToolbarVisible=!1,this.isGroupCandidateAllChecked=!1,this.groupDetailCandidateData&&this.groupDetailCandidateData.length>0&&this.groupDetailCandidateData.forEach(e=>e.isChecked=!1),e&&(yield this.getCandidatesInGroupData(!0))}))}onGroupCandidateChangeToggleAll(e){this.isGroupCandidateAllChecked=e,this.groupDetailCandidateData.forEach(e=>e.isChecked=this.isGroupCandidateAllChecked),this.isGroupCandidateToolbarVisible=!!this.isGroupCandidateAllChecked,this.selectedGroupCandidateDetails=this.groupDetailCandidateData.filter(e=>1==e.isChecked)}onGroupCandidateCheckboxValueChanges(e){this.selectedGroupCandidateDetails=e.selectedDetails,this.unselectedGroupCandidateDetails=e.unselectedDetails,this.selectedGroupCandidateCount=e.selectedCount,this.isGroupCandidateAllChecked=e.isAllChecked,this.isGroupCandidateToolbarVisible=e.isToolbarActive}onGroupCandidateDataScroll(){this.groupCandidateSkip+=this.groupCandidateLimit,this.getCandidatesInGroupData(!1)}openGroupCandidateTagViewComponent(e){return Object(o.c)(this,void 0,void 0,(function*(){let t=d.checkAccessForGeneralRole(d.moduleId.onboarding,d.subModuleId.onboardingCandidates,0,0,"DE");const{TagsDialogComponent:i}=yield Promise.all([n.e(0),n.e(941)]).then(n.bind(null,"gaFh"));this._dialog.open(i,{width:"450px",data:{module:"onboardingDetailsCandidate",id:e.candidate_id,bulkIds:null,mode:"V",title:"Candidate Tags",subTitle:e.tags.length+" tag(s)",tags:e.tags,removeAccess:t},disableClose:!0}).afterClosed().subscribe(t=>Object(o.c)(this,void 0,void 0,(function*(){if(t){let n=this.groupDetailCandidateData.findIndex(t=>t.candidate_id==e.candidate_id);-1!=n&&(this.groupDetailCandidateData[n].tags=t||[])}})))}))}openGroupTagViewComponent(e){return Object(o.c)(this,void 0,void 0,(function*(){let t=d.checkAccessForGeneralRole(d.moduleId.onboarding,d.subModuleId.onboardingCandidates,0,0,"DE");const{TagsDialogComponent:i}=yield Promise.all([n.e(0),n.e(941)]).then(n.bind(null,"gaFh"));this._dialog.open(i,{width:"450px",data:{module:"onboardingDetailsGroup",id:e.group_id,bulkIds:null,mode:"V",title:"Group Tags",subTitle:e.tags.length+" tag(s)",tags:e.tags,removeAccess:t},disableClose:!0}).afterClosed().subscribe(t=>Object(o.c)(this,void 0,void 0,(function*(){if(t){let n=this.groupsData.findIndex(t=>t.group_id==e.group_id);-1!=n&&(this.groupsData[n].tags=t||[])}})))}))}openTagViewComponent(e){return Object(o.c)(this,void 0,void 0,(function*(){let t=d.checkAccessForGeneralRole(d.moduleId.onboarding,d.subModuleId.onboardingCandidates,0,0,"DE");const{TagsDialogComponent:i}=yield Promise.all([n.e(0),n.e(941)]).then(n.bind(null,"gaFh"));this._dialog.open(i,{width:"450px",data:{module:"onboardingDetailsCandidate",id:e.candidate_id,bulkIds:null,mode:"V",title:"Candidate Tags",subTitle:e.tags.length+" tag(s)",tags:e.tags,removeAccess:t},disableClose:!0}).afterClosed().subscribe(t=>Object(o.c)(this,void 0,void 0,(function*(){if(t){let n=this.candidatesData.findIndex(t=>t.candidate_id==e.candidate_id);-1!=n&&(this.candidatesData[n].tags=t||[])}})))}))}toggleSelectedCandidateViewType(e){return Object(o.c)(this,void 0,void 0,(function*(){this.selectedCandidateViewType=e.value,this.calculateDynamicContentHeight(),1==this.selectedCandidateViewType?(this.isCandidatesToolbarVisible=!1,this.isCandidatesAllChecked=!1,yield this.switchToSelectedOnboardingStatus(!0,!0,0)):(this.calculateDynamicContentHeight(),this.isGroupsToolbarVisible=!1,this.isGroupsAllChecked=!1,yield this.getCandidateGroupsOnboardingDetails(!0))}))}openGroupDetailView(e){return Object(o.c)(this,void 0,void 0,(function*(){e&&(null==e?void 0:e.group_id)&&(this.isGroupCandidateToolbarVisible=!1,this.isGroupCandidateAllChecked=!1,this.groupCandidateSearchParams="",this.currentSelectedGroupId=e.group_id,this.currentSelectedGroupName=e.group_name,this.selectedOnboardingCandidatesView="group-detail-view",yield this.getCandidatesInGroupData(!0))}))}goBackToCandidates(){return Object(o.c)(this,void 0,void 0,(function*(){this.isGroupsToolbarVisible=!1,this.isGroupsAllChecked=!1,this.selectedOnboardingCandidatesView="candidate-view",this.currentSelectedGroupId=null,this.currentSelectedGroupName=null,this.calculateDynamicContentHeight(),yield this.getCandidateGroupsOnboardingDetails(!0)}))}createNewGroup(){return Object(o.c)(this,void 0,void 0,(function*(){const{CreateEditGroupComponent:e}=yield Promise.all([n.e(33),n.e(922)]).then(n.bind(null,"Vth9"));this._dialog.open(e,{height:"100vh",width:"60%",data:{mode:"C",uiTextConfig:this.uiTextConfig,groupId:null},position:{right:"0px"},disableClose:!0}).afterClosed().subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){e&&2==this.selectedCandidateViewType&&(yield this.getCandidateGroupsOnboardingDetails(!0))})))}))}editGroup(e){return Object(o.c)(this,void 0,void 0,(function*(){const{CreateEditGroupComponent:t}=yield Promise.all([n.e(33),n.e(922)]).then(n.bind(null,"Vth9"));this._dialog.open(t,{height:"100vh",width:"60%",data:{mode:"E",uiTextConfig:this.uiTextConfig,groupId:null==e?void 0:e.group_id},position:{right:"0px"},disableClose:!0}).afterClosed().subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){e&&2==this.selectedCandidateViewType&&(yield this.getCandidateGroupsOnboardingDetails(!0))})))}))}getAtsMasterUiConfig(e){return Object(o.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._atsMasterService.getAtsMasterUiConfig(e).pipe(Object(s.a)(this._onDestroy)).subscribe({next:n=>{0==n.err?"onboardingLandingPage"==e?(this.uiTextConfig=n.data.uiTextConfig,this.menuList=n.data.menuList,this.menuList=this.menuList.filter(e=>this._utilService.filterSectionsBasedOnRole(null==e?void 0:e.access_permission,this.access.roleAccessList)&&(null==e?void 0:e.isActive)),this.checklistsFieldConfig=this.filterAndSortBasedOnPosition(n.data.checklists.fieldConfig),this.checklistsToolbarConfig=this.filterAndSortBasedOnPosition(n.data.checklists.toolbarConfig),this.checklistsFieldConfig=this.appendChecklistLocalStorageConfig(this.checklistsFieldConfig),this.candidatesFieldConfig=this.filterAndSortBasedOnPosition(n.data.onboardingCandidates.fieldConfig),this.candidatesToolbarConfig=this.filterAndSortBasedOnPosition(n.data.onboardingCandidates.toolbarConfig),this.candidatesFieldConfig=this.appendCandidateLocalStorageConfig(this.candidatesFieldConfig),this.groupsFieldConfig=this.filterAndSortBasedOnPosition(n.data.onboardingGroups.fieldConfig),this.groupsToolbarConfig=this.filterAndSortBasedOnPosition(n.data.onboardingGroups.toolbarConfig),this.groupsFieldConfig=this.appendCandidateLocalStorageConfig(this.groupsFieldConfig),this.groupDetailCandidateFieldConfig=this.filterAndSortBasedOnPosition(n.data.onboardingGroupCandidates.fieldConfig),this.groupDetailCandidatesToolbarConfig=this.filterAndSortBasedOnPosition(n.data.onboardingGroupCandidates.toolbarConfig),this.groupDetailCandidateFieldConfig=this.appendCandidateLocalStorageConfig(this.groupDetailCandidateFieldConfig)):"generalUiConfig"==e&&(this.generalUiConfig=n.data):this._toaster.showError("Error",n.msg,7e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Master Data Retrieval Failed!",7e3),n()}}))}))}getCandidateChecklistDetails(e){return Object(o.c)(this,void 0,void 0,(function*(){e&&(this.checklistsSkip=0,this.checklistsData=[],this.getCandidateChecklistCount(),this.isSectionLoading=!0);let t={skip:this.checklistsSkip,limit:this.checklistsLimit,searchParams:this.checklistsSearchParams};return new Promise((e,n)=>this._onboardingService.getCandidateChecklistDetails(t).pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.checklistsData=[...this.checklistsData,...t.data]:this._toaster.showError("Error",t.msg,7e3),this.isSectionLoading=!1,e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Candidate Checklists Data Retrieval Failed!",7e3),this.isSectionLoading=!1,n()}}))}))}getCandidateChecklistCount(){return Object(o.c)(this,void 0,void 0,(function*(){let e={searchParams:this.checklistsSearchParams};return new Promise((t,n)=>this._onboardingService.getCandidateChecklistCount(e).pipe(Object(s.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?this.totalChecklistsCount=e.data:this._toaster.showError("Error",e.msg,7e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Candidate Checklists Data Retrieval Failed!",7e3),this.isSectionLoading=!1,n()}}))}))}getCandidateOnboardingChecklistPrioriyMaster(){return Object(o.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._onboardingService.getCandidateOnboardingChecklistPrioriyMaster({}).pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.checklistsPriorityMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Checklists Priority Data Retrieval Failed!",7e3),t()}}))}))}getCandidateOnboardingStatusMaster(){return Object(o.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._onboardingService.getCandidateOnboardingStatusMaster({}).pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.onboardingStatusMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Candidate Onboarding Status Data Retrieval Failed!",7e3),t()}}))}))}getCandidateOnboardingDetails(){return Object(o.c)(this,void 0,void 0,(function*(){let e={skip:this.candidateSkip,limit:this.candidateLimit,searchParams:this.candidatesSearchParams,onboardingStatus:this.currentSelectedOnboardingStatusId};return new Promise((t,n)=>this._onboardingService.getCandidateOnboardingDetails(e).pipe(Object(s.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?this.candidatesData=[...this.candidatesData,...e.data]:this._toaster.showError("Error",e.msg,7e3),this.isSectionLoading=!1,t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Candidate Onboarding Data Retrieval Failed!",7e3),this.isSectionLoading=!1,n()}}))}))}getCandidateOnboardingCount(){return Object(o.c)(this,void 0,void 0,(function*(){let e={searchParams:this.candidatesSearchParams};return new Promise((t,n)=>this._onboardingService.getCandidateOnboardingCount(e).pipe(Object(s.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?(this.candidatesCountBasedOnStatus=e.data,this.candidatesCountBasedOnStatus&&this.candidatesCountBasedOnStatus.length>0&&(this.totalCandidatesCount=this.candidatesCountBasedOnStatus[0].count)):this._toaster.showError("Error",e.msg,7e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Candidate Checklists Data Retrieval Failed!",7e3),this.isSectionLoading=!1,n()}}))}))}getAssociateDetailsInSystem(){return Object(o.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getAssociateDetailsInSystem().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.employeeMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Employee Master Data Retrieval Failed!",7e3),t()}}))}))}getChecklistMaster(){return Object(o.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._onboardingService.getChecklistMaster({}).pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.checklistMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Checklist Master Data Retrieval Failed!",7e3),t()}}))}))}getOnboardingTagsMaster(){return Object(o.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._onboardingService.getOnboardingTagsMaster({}).pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.tagsMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Tags Master Data Retrieval Failed!",7e3),t()}}))}))}getOnboardingGroupsMaster(){return Object(o.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._onboardingService.getOnboardingGroupsMaster({}).pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.groupsMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Group Master Data Retrieval Failed!",7e3),t()}}))}))}getCandidateGroupsOnboardingDetails(e){return Object(o.c)(this,void 0,void 0,(function*(){e&&(this.groupSkip=0,this.groupsData=[],this.isSectionLoading=!0,this.getOnboardingGroupTagsMaster(),yield this.getCandidateGroupsOnboardingCount());let t={skip:this.groupSkip,limit:this.groupLimit,searchParams:this.groupsSearchParams};return new Promise((e,n)=>this._onboardingService.getCandidateGroupsOnboardingDetails(t).pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.groupsData=[...this.groupsData,...t.data]:this._toaster.showError("Error",t.msg,7e3),this.isSectionLoading=!1,e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Groups Onboarding Data Retrieval Failed!",7e3),this.isSectionLoading=!1,n()}}))}))}getCandidateGroupsOnboardingCount(){return Object(o.c)(this,void 0,void 0,(function*(){let e={searchParams:this.groupsSearchParams};return new Promise((t,n)=>this._onboardingService.getCandidateGroupsOnboardingCount(e).pipe(Object(s.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?this.totalGroupsCount=e.data:this._toaster.showError("Error",e.msg,7e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Candidate Groups Count Retrieval Failed!",7e3),this.isSectionLoading=!1,n()}}))}))}getCandidatesInGroupData(e){return Object(o.c)(this,void 0,void 0,(function*(){e&&(this.groupCandidateSkip=0,this.groupDetailCandidateData=[],this.isSectionLoading=!0,yield this.getCandidatesInGroupCount());let t={skip:this.groupCandidateSkip,limit:this.groupCandidateLimit,groupId:this.currentSelectedGroupId,searchParams:this.groupCandidateSearchParams};return new Promise((e,n)=>this._onboardingService.getCandidatesInGroupData(t).pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.groupDetailCandidateData=[...this.groupDetailCandidateData,...t.data]:this._toaster.showError("Error",t.msg,7e3),this.isSectionLoading=!1,e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Group Candidate Onboarding Data Retrieval Failed!",7e3),this.isSectionLoading=!1,n()}}))}))}getCandidatesInGroupCount(){return Object(o.c)(this,void 0,void 0,(function*(){let e={groupId:this.currentSelectedGroupId,searchParams:this.groupCandidateSearchParams};return new Promise((t,n)=>this._onboardingService.getCandidatesInGroupCount(e).pipe(Object(s.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?this.totalGroupCandidateCount=e.data:this._toaster.showError("Error",e.msg,7e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Candidate Groups Count Retrieval Failed!",7e3),this.isSectionLoading=!1,n()}}))}))}getOnboardingGroupTagsMaster(){return Object(o.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._onboardingService.getOnboardingGroupTagsMaster({}).pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.groupTagsMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Tags Master Data Retrieval Failed!",7e3),t()}}))}))}}return e.\u0275fac=function(t){return new(t||e)(g["\u0275\u0275directiveInject"](p.a),g["\u0275\u0275directiveInject"](u.a),g["\u0275\u0275directiveInject"](h.a),g["\u0275\u0275directiveInject"](C.a),g["\u0275\u0275directiveInject"](m.a),g["\u0275\u0275directiveInject"](f.e),g["\u0275\u0275directiveInject"](g.ViewContainerRef),g["\u0275\u0275directiveInject"](v.b),g["\u0275\u0275directiveInject"](a.g),g["\u0275\u0275directiveInject"](a.a))},e.\u0275cmp=g["\u0275\u0275defineComponent"]({type:e,selectors:[["app-landing-page"]],viewQuery:function(e,t){if(1&e&&(g["\u0275\u0275viewQuery"](V,!0),g["\u0275\u0275viewQuery"](T,!0),g["\u0275\u0275viewQuery"](F,!0),g["\u0275\u0275viewQuery"](L,!0),g["\u0275\u0275viewQuery"](G,!0),g["\u0275\u0275viewQuery"](A,!0),g["\u0275\u0275viewQuery"](j,!0),g["\u0275\u0275viewQuery"](R,!0),g["\u0275\u0275viewQuery"](H,!0),g["\u0275\u0275viewQuery"](z,!0),g["\u0275\u0275viewQuery"](B,!0)),2&e){let e;g["\u0275\u0275queryRefresh"](e=g["\u0275\u0275loadQuery"]())&&(t.triggerChecklistsColumnCustomizationTemplateRef=e.first),g["\u0275\u0275queryRefresh"](e=g["\u0275\u0275loadQuery"]())&&(t.triggerChecklistsSearchBarTemplateRef=e.first),g["\u0275\u0275queryRefresh"](e=g["\u0275\u0275loadQuery"]())&&(t.triggerChecklistsSearchBar=e.first),g["\u0275\u0275queryRefresh"](e=g["\u0275\u0275loadQuery"]())&&(t.triggerCandidatesColumnCustomizationTemplateRef=e.first),g["\u0275\u0275queryRefresh"](e=g["\u0275\u0275loadQuery"]())&&(t.triggerCandidatesSearchBarTemplateRef=e.first),g["\u0275\u0275queryRefresh"](e=g["\u0275\u0275loadQuery"]())&&(t.triggerCandidatesSearchBar=e.first),g["\u0275\u0275queryRefresh"](e=g["\u0275\u0275loadQuery"]())&&(t.triggerGroupsColumnCustomizationTemplateRef=e.first),g["\u0275\u0275queryRefresh"](e=g["\u0275\u0275loadQuery"]())&&(t.triggerGroupsSearchBarTemplateRef=e.first),g["\u0275\u0275queryRefresh"](e=g["\u0275\u0275loadQuery"]())&&(t.triggerGroupsSearchBar=e.first),g["\u0275\u0275queryRefresh"](e=g["\u0275\u0275loadQuery"]())&&(t.triggerGroupCandidateSearchBarTemplateRef=e.first),g["\u0275\u0275queryRefresh"](e=g["\u0275\u0275loadQuery"]())&&(t.triggerGroupCandidateSearchBar=e.first)}},hostBindings:function(e,t){1&e&&g["\u0275\u0275listener"]("resize",(function(){return t.onResize()}),!1,g["\u0275\u0275resolveWindow"])},decls:18,vars:25,consts:[["class","loading-container",4,"ngIf"],[4,"ngIf"],["cdkConnectedOverlay","",3,"cdkConnectedOverlayOrigin"],["triggerChecklistsColumnCustomizationTemplateRef",""],["triggerChecklistsSearchBarTemplateRef",""],["triggerCandidatesColumnCustomizationTemplateRef",""],["triggerCandidatesSearchBarTemplateRef",""],["triggerGroupsColumnCustomizationTemplateRef",""],["triggerGroupsSearchBarTemplateRef",""],["triggerGroupCandidateSearchBarTemplateRef",""],[1,"loading-container"],["class","bg-container",4,"ngIf"],[2,"max-width","60px","max-height","60px"],[2,"max-width","100%","max-height","100%",3,"src"],[1,"loading-wrapper"],[1,"loading"],[1,"bg-container"],[1,"menu",3,"ngStyle"],[1,"sub-menu"],[4,"ngFor","ngForOf"],[1,"single-menu",3,"ngClass","click"],["width","18","height","18","viewBox","0 0 18 18","fill","none",3,"ngStyle"],["d","M10.9125 16.2525C14.13 15.405 16.5 12.48 16.5 9C16.5 4.86 13.17 1.5 9 1.5C3.9975 1.5 1.5 5.67 1.5 5.67M1.5 5.67V2.25M1.5 5.67H3.0075H4.83","stroke-width","1.5","stroke-linecap","round","stroke-linejoin","round"],["opacity","0.4","d","M1.5 9C1.5 13.14 4.86 16.5 9 16.5","stroke-width","1.5","stroke-linecap","round","stroke-linejoin","round","stroke-dasharray","3 3"],[3,"ngClass"],[1,"section-container"],["class","loading-section-container",4,"ngIf"],[3,"ngStyle","innerHTML"],["class","candidate-header",3,"ngStyle",4,"ngIf"],["class","toolbar",4,"ngIf"],["class","stages",3,"ngStyle",4,"ngIf"],[1,"candidate-header",3,"ngStyle"],[1,"candidate-header-1"],["class","count",4,"ngIf"],["class","count-text",4,"ngIf"],[1,"candidate-header-2"],["class","d-flex align-items-center justify-content-between search-ui","cdkOverlayOrigin","",3,"click",4,"ngIf"],["class","svg-icon","cdkOverlayOrigin","",3,"click",4,"ngIf"],[3,"value","ngModel","change","ngModelChange"],[3,"value",4,"ngFor","ngForOf"],["class","new-group",3,"click",4,"ngIf"],[1,"count"],[1,"count-text"],["cdkOverlayOrigin","",1,"d-flex","align-items-center","justify-content-between","search-ui",3,"click"],["triggerCandidatesSearchBar","cdkOverlayOrigin","triggerCandidatesSearchField",""],[1,"search-bar"],["type","text","placeholder","Search Here...",3,"ngModel","ngModelChange","keydown.backspace"],[1,"svg-icon"],["width","18","height","18","viewBox","0 0 18 18","fill","none",4,"ngIf"],["style","cursor: pointer","width","18","height","18","viewBox","0 0 18 18","fill","none",3,"click",4,"ngIf"],["width","18","height","18","viewBox","0 0 18 18","fill","none"],["d","M7.01916 12.6153C5.31148 12.6153 3.86533 12.023 2.68073 10.8384C1.49612 9.65383 0.903809 8.20768 0.903809 6.49999C0.903809 4.79231 1.49612 3.34616 2.68073 2.16154C3.86533 0.976943 5.31148 0.384644 7.01916 0.384644C8.72684 0.384644 10.173 0.976943 11.3576 2.16154C12.5422 3.34616 13.1345 4.79231 13.1345 6.49999C13.1345 7.21409 13.0147 7.89614 12.7749 8.54614C12.5352 9.19614 12.2153 9.76153 11.8153 10.2423L17.5692 15.9961C17.7076 16.1346 17.7784 16.3087 17.7816 16.5183C17.7849 16.7279 17.714 16.9051 17.5692 17.05C17.4243 17.1948 17.2486 17.2673 17.0422 17.2673C16.8358 17.2673 16.6602 17.1948 16.5153 17.05L10.7615 11.2961C10.2615 11.7089 9.68646 12.032 9.03646 12.2653C8.38646 12.4987 7.71403 12.6153 7.01916 12.6153ZM7.01916 11.1154C8.30763 11.1154 9.39897 10.6683 10.2932 9.77404C11.1874 8.87981 11.6346 7.78846 11.6346 6.49999C11.6346 5.21153 11.1874 4.12018 10.2932 3.22594C9.39897 2.33171 8.30763 1.88459 7.01916 1.88459C5.73069 1.88459 4.63934 2.33171 3.74511 3.22594C2.85089 4.12018 2.40378 5.21153 2.40378 6.49999C2.40378 7.78846 2.85089 8.87981 3.74511 9.77404C4.63934 10.6683 5.73069 11.1154 7.01916 11.1154Z","fill","#45546E"],["width","18","height","18","viewBox","0 0 18 18","fill","none",2,"cursor","pointer",3,"click"],["clip-path","url(#clip0_22386_12410)"],["d","M8.00048 7.05781L11.3005 3.75781L12.2431 4.70048L8.94315 8.00048L12.2431 11.3005L11.3005 12.2431L8.00048 8.94315L4.70048 12.2431L3.75781 11.3005L7.05781 8.00048L3.75781 4.70048L4.70048 3.75781L8.00048 7.05781Z","fill","#6E7B8F"],["id","clip0_22386_12410"],["width","16","height","16","fill","white"],["cdkOverlayOrigin","",1,"svg-icon",3,"click"],["triggerGroupsSearchBar","cdkOverlayOrigin","triggerGroupsSearchField",""],[3,"value"],["triggerCandidatesColumnCustomization","cdkOverlayOrigin","triggerCandidatesColumnCustomizationField",""],["width","19","height","18","viewBox","0 0 19 18","fill","none"],["d","M9.49965 17.75C9.28705 17.75 9.10896 17.6781 8.96538 17.5344C8.82179 17.3906 8.75 17.2125 8.75 17V13C8.75 12.7875 8.8219 12.6094 8.9657 12.4657C9.10952 12.3219 9.28772 12.25 9.5003 12.25C9.7129 12.25 9.89099 12.3219 10.0346 12.4657C10.1782 12.6094 10.25 12.7875 10.25 13V14.25H17.5C17.7125 14.25 17.8906 14.3219 18.0344 14.4657C18.1781 14.6096 18.25 14.7877 18.25 15.0003C18.25 15.2129 18.1781 15.391 18.0344 15.5346C17.8906 15.6782 17.7125 15.75 17.5 15.75H10.25V17C10.25 17.2125 10.1781 17.3906 10.0343 17.5344C9.89043 17.6781 9.71223 17.75 9.49965 17.75ZM1.49997 15.75C1.28747 15.75 1.10936 15.6781 0.965625 15.5343C0.821875 15.3905 0.75 15.2123 0.75 14.9997C0.75 14.7871 0.821875 14.609 0.965625 14.4654C1.10936 14.3218 1.28747 14.25 1.49997 14.25H5.49998C5.71248 14.25 5.89059 14.3219 6.03433 14.4657C6.17808 14.6096 6.24995 14.7877 6.24995 15.0003C6.24995 15.2129 6.17808 15.391 6.03433 15.5346C5.89059 15.6782 5.71248 15.75 5.49998 15.75H1.49997ZM5.49965 11.75C5.28705 11.75 5.10896 11.6781 4.96537 11.5344C4.82179 11.3906 4.75 11.2125 4.75 11V9.74998H1.49997C1.28747 9.74998 1.10936 9.67808 0.965625 9.53428C0.821875 9.39046 0.75 9.21227 0.75 8.99968C0.75 8.78708 0.821875 8.60899 0.965625 8.46541C1.10936 8.32182 1.28747 8.25003 1.49997 8.25003H4.75V7.00001C4.75 6.78751 4.8219 6.60938 4.9657 6.46563C5.10952 6.3219 5.28772 6.25003 5.5003 6.25003C5.7129 6.25003 5.89099 6.3219 6.03458 6.46563C6.17816 6.60938 6.24995 6.78751 6.24995 7.00001V11C6.24995 11.2125 6.17805 11.3906 6.03425 11.5344C5.89043 11.6781 5.71223 11.75 5.49965 11.75ZM9.49998 9.74998C9.28748 9.74998 9.10936 9.67808 8.96563 9.53428C8.82188 9.39046 8.75 9.21227 8.75 8.99968C8.75 8.78708 8.82188 8.60899 8.96563 8.46541C9.10936 8.32182 9.28748 8.25003 9.49998 8.25003H17.5C17.7125 8.25003 17.8906 8.32193 18.0344 8.46573C18.1781 8.60955 18.25 8.78775 18.25 9.00033C18.25 9.21293 18.1781 9.39102 18.0344 9.53461C17.8906 9.67819 17.7125 9.74998 17.5 9.74998H9.49998ZM13.4997 5.74998C13.2871 5.74998 13.109 5.67811 12.9654 5.53438C12.8218 5.39063 12.75 5.21251 12.75 5.00001V1.00001C12.75 0.787506 12.8219 0.609381 12.9657 0.465631C13.1095 0.321898 13.2877 0.250031 13.5003 0.250031C13.7129 0.250031 13.891 0.321898 14.0346 0.465631C14.1782 0.609381 14.25 0.787506 14.25 1.00001V2.25003H17.5C17.7125 2.25003 17.8906 2.32193 18.0344 2.46573C18.1781 2.60955 18.25 2.78775 18.25 3.00033C18.25 3.21293 18.1781 3.39102 18.0344 3.53461C17.8906 3.67819 17.7125 3.74998 17.5 3.74998H14.25V5.00001C14.25 5.21251 14.1781 5.39063 14.0343 5.53438C13.8904 5.67811 13.7122 5.74998 13.4997 5.74998ZM1.49997 3.74998C1.28747 3.74998 1.10936 3.67808 0.965625 3.53428C0.821875 3.39046 0.75 3.21226 0.75 2.99968C0.75 2.78708 0.821875 2.60899 0.965625 2.46541C1.10936 2.32182 1.28747 2.25003 1.49997 2.25003H9.49998C9.71247 2.25003 9.89059 2.32193 10.0343 2.46573C10.1781 2.60955 10.25 2.78775 10.25 3.00033C10.25 3.21293 10.1781 3.39102 10.0343 3.53461C9.89059 3.67819 9.71247 3.74998 9.49998 3.74998H1.49997Z","fill","#45546E"],["triggerGroupsColumnCustomization","cdkOverlayOrigin","triggerGroupsColumnCustomizationField",""],[1,"new-group",3,"click"],[1,"icon"],[1,"text"],[1,"toolbar"],[2,"width","100%",3,"module","height","selectedDetails","unselectedDetails","toolbarConfig","count","uiTextConfig","generalUiConfig","isBulkSelectActive","searchQuery","currentSelectedOnboardingStatusId","onboardingStatusMasterData","checklistsPriorityMasterData","checklistMasterData","employeeMasterData","tagsMasterData","groupsMasterData","onClose"],[2,"width","100%",3,"module","height","selectedDetails","unselectedDetails","toolbarConfig","count","uiTextConfig","generalUiConfig","isBulkSelectActive","searchQuery","onboardingStatusMasterData","tagsMasterData","employeeMasterData","onClose"],[1,"stages",3,"ngStyle"],["content-type","template",1,"d-flex","flex-column",3,"ngClass","tooltip","options","click"],[1,"d-flex","align-items-center",2,"gap","4px"],[3,"innerHTML"],[1,"unselected-stage-text"],["style","background-color: #111434"],["customTooltipContent",""],[1,"custom-count-tooltip"],[3,"list","fieldConfig","variant","headerHeight","totalCount","isAllChecked","bulkSelectActive","isCheckboxActive","onCheckboxValueChanges","onChangeToggleAll","onScroll","onClick",4,"ngIf"],["class","empty-state",4,"ngIf"],[3,"list","fieldConfig","variant","headerHeight","totalCount","isAllChecked","bulkSelectActive","isCheckboxActive","onCheckboxValueChanges","onChangeToggleAll","onScroll","onClick"],[1,"empty-state"],["src","https://assets.kebs.app/ATS-Empty-State.png"],[1,"no-data-title"],["class","group-detail-header",4,"ngIf"],[1,"group-detail-header"],[1,"group-detail-header-1"],[1,"back-icon",3,"click"],[1,"group-detail-header-2"],["triggerGroupCandidateSearchBar","cdkOverlayOrigin","triggerGroupCandidateSearchField",""],[2,"width","100%",3,"module","height","selectedDetails","unselectedDetails","toolbarConfig","count","uiTextConfig","generalUiConfig","isBulkSelectActive","searchQuery","onboardingStatusMasterData","checklistsPriorityMasterData","checklistMasterData","employeeMasterData","tagsMasterData","groupsMasterData","currentSelectedGroupId","onClose"],["class","checklist-header",3,"ngStyle",4,"ngIf"],[1,"checklist-header",3,"ngStyle"],[1,"checklist-header-1"],[1,"checklist-header-2"],["triggerChecklistsColumnCustomization","cdkOverlayOrigin","triggerChecklistsColumnCustomizationField",""],["triggerChecklistsSearchBar","cdkOverlayOrigin","triggerChecklistsSearchField",""],[2,"width","100%",3,"module","height","selectedDetails","unselectedDetails","toolbarConfig","count","uiTextConfig","isBulkSelectActive","searchQuery","checklistsPriorityMasterData","onClose"],[3,"list","fieldConfig","uiTextConfig","variant","headerHeight","totalCount","isAllChecked","bulkSelectActive","isCheckboxActive","checklistsPriorityMasterData","onCheckboxValueChanges","onChangeToggleAll","onScroll","onClick",4,"ngIf"],[3,"list","fieldConfig","uiTextConfig","variant","headerHeight","totalCount","isAllChecked","bulkSelectActive","isCheckboxActive","checklistsPriorityMasterData","onCheckboxValueChanges","onChangeToggleAll","onScroll","onClick"],[1,"loading-section-container"],[1,"history-log"],[3,"module","displayHeader","displayDateRangeFilter","displaySearch","showDateRangeCalendar","dateRangeFilterValue","defaultDateRangeFilter","uiTextConfig"],[3,"customization","onApply"],[3,"currentSearchText","recentSearch","onEnter"]],template:function(e,t){1&e&&(g["\u0275\u0275template"](0,N,2,0,"div",0),g["\u0275\u0275pipe"](1,"access"),g["\u0275\u0275template"](2,tt,3,2,"ng-container",1),g["\u0275\u0275pipe"](3,"access"),g["\u0275\u0275template"](4,nt,1,1,"ng-template",2,3,g["\u0275\u0275templateRefExtractor"]),g["\u0275\u0275template"](6,at,1,3,"ng-template",2,4,g["\u0275\u0275templateRefExtractor"]),g["\u0275\u0275template"](8,ot,1,1,"ng-template",2,5,g["\u0275\u0275templateRefExtractor"]),g["\u0275\u0275template"](10,rt,1,3,"ng-template",2,6,g["\u0275\u0275templateRefExtractor"]),g["\u0275\u0275template"](12,st,1,1,"ng-template",2,7,g["\u0275\u0275templateRefExtractor"]),g["\u0275\u0275template"](14,ct,1,3,"ng-template",2,8,g["\u0275\u0275templateRefExtractor"]),g["\u0275\u0275template"](16,lt,1,3,"ng-template",2,9,g["\u0275\u0275templateRefExtractor"])),2&e&&(g["\u0275\u0275property"]("ngIf",!(g["\u0275\u0275pipeBindV"](1,9,g["\u0275\u0275pureFunction1"](21,dt,t.access.moduleId.onboarding))&&t.menuList&&0!=t.menuList.length||t.isLoading)),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("ngIf",g["\u0275\u0275pipeBindV"](3,15,g["\u0275\u0275pureFunction1"](23,dt,t.access.moduleId.onboarding))&&t.menuList.length>0||t.isLoading),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("cdkConnectedOverlayOrigin",t.triggerChecklistsColumnCustomization),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("cdkConnectedOverlayOrigin",t.triggerChecklistsSearchBar),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("cdkConnectedOverlayOrigin",t.triggerCandidatesColumnCustomization),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("cdkConnectedOverlayOrigin",t.triggerCandidatesSearchBar),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("cdkConnectedOverlayOrigin",t.triggerGroupsColumnCustomization),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("cdkConnectedOverlayOrigin",t.triggerGroupsSearchBar),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("cdkConnectedOverlayOrigin",t.triggerGroupCandidateSearchBar))},directives:[i.NgIf,f.a,x.a,i.NgStyle,i.NgForOf,i.NgClass,b.b,y.v,y.y,f.b,y.e,b.a,S.a,O.a,M.a,_.a,w.a,k.a,P.a,D.a],pipes:[E.a,I.a],styles:['.loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;background-color:#fff;overflow:hidden;height:var(--dynamicHeight)}.loading-container[_ngcontent-%COMP%]   .loading-wrapper[_ngcontent-%COMP%]{display:flex;vertical-align:center;justify-content:center;align-items:center}.loading-container[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]{color:rgba(0,0,0,.3);font-size:16px}.loading-container[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]:before{content:"Loading...";position:absolute;overflow:hidden;max-width:7em;white-space:nowrap;background:linear-gradient(270deg,var(--atsprimaryColor),var(--atssecondaryColor) 105.29%);background-clip:text;-webkit-background-clip:text;-webkit-text-fill-color:transparent;animation:loading 6s linear infinite}.bg-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px;background-color:#fff;overflow:hidden;height:var(--dynamicHeight)}.bg-container[_ngcontent-%COMP%]   .menu[_ngcontent-%COMP%]{display:flex;justify-content:space-between;background-color:#111434;min-height:48px;padding:0 24px}.bg-container[_ngcontent-%COMP%]   .menu[_ngcontent-%COMP%]   .sub-menu[_ngcontent-%COMP%]{display:flex;align-items:end;gap:16px;height:100%}.bg-container[_ngcontent-%COMP%]   .menu[_ngcontent-%COMP%]   .sub-menu[_ngcontent-%COMP%]   .single-menu[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px;cursor:pointer}.bg-container[_ngcontent-%COMP%]   .menu[_ngcontent-%COMP%]   .sub-menu[_ngcontent-%COMP%]   .selected-menu[_ngcontent-%COMP%]{background-color:#fff;border-radius:12px 12px 0 0;height:80%;padding:8px 16px;gap:4px}.bg-container[_ngcontent-%COMP%]   .menu[_ngcontent-%COMP%]   .sub-menu[_ngcontent-%COMP%]   .unselected-menu[_ngcontent-%COMP%]{height:80%;padding:8px;gap:4px}.bg-container[_ngcontent-%COMP%]   .menu[_ngcontent-%COMP%]   .sub-menu[_ngcontent-%COMP%]   .selected-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:700;color:#111434;cursor:pointer}.bg-container[_ngcontent-%COMP%]   .menu[_ngcontent-%COMP%]   .sub-menu[_ngcontent-%COMP%]   .unselected-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:500;color:#8b95a5;cursor:pointer}.bg-container[_ngcontent-%COMP%]   .loading-section-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;height:var(--dynamicSubHeight)}.bg-container[_ngcontent-%COMP%]   .loading-section-container[_ngcontent-%COMP%]   .loading-wrapper[_ngcontent-%COMP%]{display:flex;vertical-align:center;justify-content:center;align-items:center}.bg-container[_ngcontent-%COMP%]   .loading-section-container[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]{color:rgba(0,0,0,.3);font-size:16px}.bg-container[_ngcontent-%COMP%]   .loading-section-container[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]:before{content:"Loading...";position:absolute;overflow:hidden;max-width:7em;white-space:nowrap;background:linear-gradient(270deg,var(--atsprimaryColor),var(--atssecondaryColor) 105.29%);background-clip:text;-webkit-background-clip:text;-webkit-text-fill-color:transparent;animation:loading 6s linear infinite}@keyframes loading{0%{max-width:0}}.bg-container[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;height:var(--dynamicSubHeight)}.bg-container[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .no-data-title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:500;color:#1b2140;margin-bottom:4px}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:16px;padding:0 16px}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .toolbar[_ngcontent-%COMP%]{width:100%;height:44px}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .checklist-header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;height:44px}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .checklist-header[_ngcontent-%COMP%]   .checklist-header-1[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .checklist-header[_ngcontent-%COMP%]   .checklist-header-1[_ngcontent-%COMP%]   .count[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:16px;font-weight:700;color:#111434;padding:4px 8px;border-radius:4px;background-color:#e8e9ee}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .checklist-header[_ngcontent-%COMP%]   .checklist-header-1[_ngcontent-%COMP%]   .count-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:16px;font-weight:700;color:#111434}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .checklist-header[_ngcontent-%COMP%]   .checklist-header-2[_ngcontent-%COMP%]{display:flex;align-items:center;gap:20px}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .checklist-header[_ngcontent-%COMP%]   .checklist-header-2[_ngcontent-%COMP%]   .svg-icon[_ngcontent-%COMP%]{cursor:pointer}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .checklist-header[_ngcontent-%COMP%]   .checklist-header-2[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]{width:350px;height:36px;padding:0 8px;border:2px solid #dadce2;border-radius:8px;cursor:text}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .checklist-header[_ngcontent-%COMP%]   .checklist-header-2[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]{width:100%}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .checklist-header[_ngcontent-%COMP%]   .checklist-header-2[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{height:100%;width:95%;font-family:var(--atsfontFamily);font-size:11px;font-weight:400;color:#6e7b8f;outline:none;border:none}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .checklist-header[_ngcontent-%COMP%]   .checklist-header-2[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{font-family:var(--atsfontFamily);font-size:11px;font-weight:400;color:#6e7b8f}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .checklist-header[_ngcontent-%COMP%]   .checklist-header-2[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]   .search-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:11px;font-weight:400;color:#5f6c81;width:280px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .candidate-header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;height:44px}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .candidate-header[_ngcontent-%COMP%]   .candidate-header-1[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .candidate-header[_ngcontent-%COMP%]   .candidate-header-1[_ngcontent-%COMP%]   .count[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:16px;font-weight:700;color:#111434;padding:4px 8px;border-radius:4px;background-color:#e8e9ee}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .candidate-header[_ngcontent-%COMP%]   .candidate-header-1[_ngcontent-%COMP%]   .count-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:16px;font-weight:700;color:#111434}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .candidate-header[_ngcontent-%COMP%]   .candidate-header-2[_ngcontent-%COMP%]{display:flex;align-items:center;gap:20px}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .candidate-header[_ngcontent-%COMP%]   .candidate-header-2[_ngcontent-%COMP%]   .mat-button-toggle-group[_ngcontent-%COMP%]{height:33px!important}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .candidate-header[_ngcontent-%COMP%]   .candidate-header-2[_ngcontent-%COMP%]   .toggle-button-icon-1[_ngcontent-%COMP%]{font-size:20px;width:20px;height:20px}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .candidate-header[_ngcontent-%COMP%]   .candidate-header-2[_ngcontent-%COMP%]   .toggle-button-icon-2[_ngcontent-%COMP%]{font-size:16px;width:16px;height:16px;margin:0 2px}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .candidate-header[_ngcontent-%COMP%]   .candidate-header-2[_ngcontent-%COMP%]   .mat-button-toggle-checked[_ngcontent-%COMP%]{border:1px solid var(--atssecondaryColor)!important;background:var(--atssecondaryColor2)!important;color:var(--atssecondaryColor)!important}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .candidate-header[_ngcontent-%COMP%]   .candidate-header-2[_ngcontent-%COMP%]   .mat-button-toggle-appearance-standard[_ngcontent-%COMP%]{border:1px solid #dadce2;background:transparent;color:#5f6c81}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .candidate-header[_ngcontent-%COMP%]   .candidate-header-2[_ngcontent-%COMP%]     .mat-button-toggle-appearance-standard .mat-button-toggle-label-content{height:32px!important;line-height:30px!important;padding:0 6px!important;font-weight:400!important;font-size:14px!important}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .candidate-header[_ngcontent-%COMP%]   .candidate-header-2[_ngcontent-%COMP%]   .svg-icon[_ngcontent-%COMP%]{cursor:pointer}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .candidate-header[_ngcontent-%COMP%]   .candidate-header-2[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]{width:350px;height:36px;padding:0 8px;border:2px solid #dadce2;border-radius:8px;cursor:text}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .candidate-header[_ngcontent-%COMP%]   .candidate-header-2[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]{width:100%}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .candidate-header[_ngcontent-%COMP%]   .candidate-header-2[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{height:100%;width:95%;font-family:var(--atsfontFamily);font-size:11px;font-weight:400;color:#6e7b8f;outline:none;border:none}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .candidate-header[_ngcontent-%COMP%]   .candidate-header-2[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{font-family:var(--atsfontFamily);font-size:11px;font-weight:400;color:#6e7b8f}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .candidate-header[_ngcontent-%COMP%]   .candidate-header-2[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]   .search-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:11px;font-weight:400;color:#5f6c81;width:280px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .candidate-header[_ngcontent-%COMP%]   .candidate-header-2[_ngcontent-%COMP%]   .new-group[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;gap:8px;border-radius:8px;border:1px solid #45546e;height:32px;cursor:pointer;padding:0 12px}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .candidate-header[_ngcontent-%COMP%]   .candidate-header-2[_ngcontent-%COMP%]   .new-group[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{font-size:14px;width:14px;height:14px;font-weight:600;color:#45546e}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .candidate-header[_ngcontent-%COMP%]   .candidate-header-2[_ngcontent-%COMP%]   .new-group[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:700;color:#45546e}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .stages[_ngcontent-%COMP%]{gap:12px;width:100%;overflow-x:auto}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .stages[_ngcontent-%COMP%]::-webkit-scrollbar{height:0!important}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .selected-stage[_ngcontent-%COMP%]{background-color:var(--atssecondaryColor2);box-shadow:0 4px 4px 0 var(--atssecondaryColor2);border:1px solid var(--atsprimaryColor);border-radius:4px;cursor:pointer;height:48px;padding:4px;min-width:80px;max-width:80px}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .unselected-stage[_ngcontent-%COMP%]{background-color:#f6f6f6;border:1px solid #e8e9ee;border-radius:4px;cursor:pointer;height:48px;padding:4px;min-width:80px;max-width:80px}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .selected-stage-text[_ngcontent-%COMP%]{font-weight:700;color:#111434}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .selected-stage-text[_ngcontent-%COMP%], .bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .unselected-stage-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:12px;width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .unselected-stage-text[_ngcontent-%COMP%]{font-weight:500;color:#7d838b}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .group-detail-header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;height:44px}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .group-detail-header[_ngcontent-%COMP%]   .group-detail-header-1[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .group-detail-header[_ngcontent-%COMP%]   .group-detail-header-1[_ngcontent-%COMP%]   .back-icon[_ngcontent-%COMP%]{font-size:18px;width:18px;height:18px;color:#1c1b1f;cursor:pointer}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .group-detail-header[_ngcontent-%COMP%]   .group-detail-header-1[_ngcontent-%COMP%]   .count[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:16px;font-weight:700;color:#111434;padding:4px 8px;border-radius:4px;background-color:#e8e9ee}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .group-detail-header[_ngcontent-%COMP%]   .group-detail-header-1[_ngcontent-%COMP%]   .count-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:16px;font-weight:700;color:#111434}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .group-detail-header[_ngcontent-%COMP%]   .group-detail-header-2[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .group-detail-header[_ngcontent-%COMP%]   .group-detail-header-2[_ngcontent-%COMP%]   .svg-icon[_ngcontent-%COMP%]{cursor:pointer}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .group-detail-header[_ngcontent-%COMP%]   .group-detail-header-2[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]{width:350px;height:36px;padding:0 8px;border:2px solid #dadce2;border-radius:8px;cursor:text}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .group-detail-header[_ngcontent-%COMP%]   .group-detail-header-2[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]{width:100%}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .group-detail-header[_ngcontent-%COMP%]   .group-detail-header-2[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{height:100%;width:95%;font-family:var(--atsfontFamily);font-size:11px;font-weight:400;color:#6e7b8f;outline:none;border:none}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .group-detail-header[_ngcontent-%COMP%]   .group-detail-header-2[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{font-family:var(--atsfontFamily);font-size:11px;font-weight:400;color:#6e7b8f}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .group-detail-header[_ngcontent-%COMP%]   .group-detail-header-2[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]   .search-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:11px;font-weight:400;color:#5f6c81;width:280px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.bg-container[_ngcontent-%COMP%]   .section-container[_ngcontent-%COMP%]   .history-log[_ngcontent-%COMP%]{padding:16px}.custom-count-tooltip[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:500;color:#fff;text-align:center}']}),e})();var pt=n("me71"),ut=n("Qu3c"),ht=n("bSwM"),Ct=n("Ot3k"),mt=n("wC0v"),ft=n("/rGH");const vt=["triggerCandidateOnboardingStatusChange"];function xt(e,t){1&e&&(g["\u0275\u0275elementStart"](0,"div",5),g["\u0275\u0275element"](1,"app-access-denied"),g["\u0275\u0275elementEnd"]())}function bt(e,t){if(1&e&&(g["\u0275\u0275elementStart"](0,"div",5),g["\u0275\u0275elementStart"](1,"div",6),g["\u0275\u0275element"](2,"img",7),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](3,"div",8),g["\u0275\u0275elementStart"](4,"div",9),g["\u0275\u0275text"](5,"Loading..."),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()),2&e){const e=g["\u0275\u0275nextContext"](2);g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("src",e.uiTextConfig["UI-LOADING-001"],g["\u0275\u0275sanitizeUrl"])}}function yt(e,t){if(1&e&&g["\u0275\u0275element"](0,"img",25),2&e){const e=g["\u0275\u0275nextContext"](3);g["\u0275\u0275property"]("src",null==e.basicData?null:e.basicData.profile_image,g["\u0275\u0275sanitizeUrl"])}}function St(e,t){1&e&&g["\u0275\u0275element"](0,"app-user-image",26)}const Ot=function(e,t){return{fill:e,stroke:t}},Mt=function(e){return{border:e}};function _t(e,t){if(1&e&&(g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275elementStart"](1,"div",27),g["\u0275\u0275element"](2,"div",28),g["\u0275\u0275pipe"](3,"svgSecurityBypass"),g["\u0275\u0275elementStart"](4,"div",29),g["\u0275\u0275text"](5),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit,n=t.index,i=g["\u0275\u0275nextContext"](3);g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("ngStyle",g["\u0275\u0275pureFunction2"](7,Ot,null!=e&&e.isFill?"var(--atsprimaryColor)":"",null!=e&&e.isFill?"":"var(--atsprimaryColor)"))("innerHTML",g["\u0275\u0275pipeBind1"](3,5,e.icon),g["\u0275\u0275sanitizeHtml"]),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("ngStyle",g["\u0275\u0275pureFunction1"](10,Mt,(n+1)%3==0?"none":""))("matTooltip",i.basicData[e.columnKey]),g["\u0275\u0275advance"](1),g["\u0275\u0275textInterpolate1"](" ",i.basicData[e.columnKey]," ")}}function wt(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275namespaceSVG"](),g["\u0275\u0275elementStart"](0,"svg",30),g["\u0275\u0275listener"]("click",(function(){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](3).openMarkAsHireDialog()})),g["\u0275\u0275element"](1,"rect",31),g["\u0275\u0275element"](2,"rect",32),g["\u0275\u0275elementStart"](3,"mask",33),g["\u0275\u0275element"](4,"rect",34),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](5,"g",35),g["\u0275\u0275element"](6,"path",36),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}}const kt=function(e,t){return{"selected-menu":e,"unselected-menu":t}},Pt=function(e){return{stroke:e}};function Dt(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275elementStart"](1,"div",37),g["\u0275\u0275listener"]("click",(function(){g["\u0275\u0275restoreView"](e);const n=t.index;return g["\u0275\u0275nextContext"](3).switchToSelectedMenu(n)})),g["\u0275\u0275element"](2,"div",38),g["\u0275\u0275pipe"](3,"svgSecurityBypass"),g["\u0275\u0275elementStart"](4,"div"),g["\u0275\u0275text"](5),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=t.$implicit;g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngClass",g["\u0275\u0275pureFunction2"](8,kt,e.isSelected,!e.isSelected)),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngStyle",g["\u0275\u0275pureFunction1"](11,Pt,e.isSelected?"#111434":"#8B95A5"))("innerHTML",g["\u0275\u0275pipeBind1"](3,6,e.icon),g["\u0275\u0275sanitizeHtml"]),g["\u0275\u0275advance"](2),g["\u0275\u0275classMap"](e.isSelected?"selected-text":"unselected-text"),g["\u0275\u0275advance"](1),g["\u0275\u0275textInterpolate1"](" ",e.name," ")}}function Et(e,t){if(1&e&&(g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275elementStart"](1,"div",39),g["\u0275\u0275elementStart"](2,"div",6),g["\u0275\u0275element"](3,"img",7),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](4,"div",8),g["\u0275\u0275elementStart"](5,"div",9),g["\u0275\u0275text"](6,"Loading..."),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementContainerEnd"]()),2&e){const e=g["\u0275\u0275nextContext"](3);g["\u0275\u0275advance"](3),g["\u0275\u0275property"]("src",e.uiTextConfig["UI-LOADING-001"],g["\u0275\u0275sanitizeUrl"])}}function It(e,t){if(1&e&&(g["\u0275\u0275elementStart"](0,"div",57),g["\u0275\u0275text"](1),g["\u0275\u0275elementEnd"]()),2&e){const e=g["\u0275\u0275nextContext"]().index;g["\u0275\u0275advance"](1),g["\u0275\u0275textInterpolate1"](" ",e+1," ")}}function Vt(e,t){1&e&&(g["\u0275\u0275elementStart"](0,"div",58),g["\u0275\u0275namespaceSVG"](),g["\u0275\u0275elementStart"](1,"svg",59),g["\u0275\u0275elementStart"](2,"mask",60),g["\u0275\u0275element"](3,"path",61),g["\u0275\u0275elementEnd"](),g["\u0275\u0275element"](4,"path",62),g["\u0275\u0275element"](5,"path",63),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]())}const Tt=function(e,t){return{color:e,background:t}};function Ft(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div",64),g["\u0275\u0275listener"]("click",(function(){g["\u0275\u0275restoreView"](e);const t=g["\u0275\u0275nextContext"]().$implicit;return g["\u0275\u0275nextContext"](6).openDetailViewOfChecklist(t)})),g["\u0275\u0275element"](1,"div",65),g["\u0275\u0275pipe"](2,"svgSecurityBypass"),g["\u0275\u0275text"](3),g["\u0275\u0275elementEnd"]()}if(2&e){const e=g["\u0275\u0275nextContext"]().$implicit;g["\u0275\u0275property"]("ngStyle",g["\u0275\u0275pureFunction2"](5,Tt,null==e?null:e.action_color,null==e?null:e.action_bg_color)),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("innerHTML",g["\u0275\u0275pipeBind1"](2,3,null==e?null:e.action_icon),g["\u0275\u0275sanitizeHtml"]),g["\u0275\u0275advance"](2),g["\u0275\u0275textInterpolate1"](" ",(null==e?null:e.action_name)||"-"," ")}}const Lt=function(e,t){return{border:e,"pointer-events":t}};function Gt(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div",66),g["\u0275\u0275listener"]("click",(function(){g["\u0275\u0275restoreView"](e);const t=g["\u0275\u0275nextContext"]().$implicit;return g["\u0275\u0275nextContext"](6).openDetailViewOfChecklist(t)})),g["\u0275\u0275text"](1),g["\u0275\u0275elementEnd"]()}if(2&e){const e=g["\u0275\u0275nextContext"]().$implicit;g["\u0275\u0275property"]("ngStyle",g["\u0275\u0275pureFunction2"](2,Lt,null!=e&&e.action_name?"":"none",null!=e&&e.action_name?"":"none")),g["\u0275\u0275advance"](1),g["\u0275\u0275textInterpolate1"](" ",(null==e?null:e.action_name)||"-"," ")}}const At=function(e){return{display:e}};function jt(e,t){if(1&e&&(g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275elementStart"](1,"div",45),g["\u0275\u0275elementStart"](2,"div",46),g["\u0275\u0275template"](3,It,2,1,"div",47),g["\u0275\u0275template"](4,Vt,6,0,"div",48),g["\u0275\u0275elementStart"](5,"div",49),g["\u0275\u0275elementStart"](6,"div",50),g["\u0275\u0275text"](7),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](8,"div",51),g["\u0275\u0275text"](9),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](10,"div",52),g["\u0275\u0275elementStart"](11,"div",53),g["\u0275\u0275text"](12),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](13,"div",52),g["\u0275\u0275template"](14,Ft,4,8,"div",54),g["\u0275\u0275template"](15,Gt,2,5,"div",55),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275element"](16,"div",56),g["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit,n=t.index,i=g["\u0275\u0275nextContext"](6);g["\u0275\u0275advance"](3),g["\u0275\u0275property"]("ngIf",1!=(null==e?null:e.action_id)),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",1==(null==e?null:e.action_id)),g["\u0275\u0275advance"](3),g["\u0275\u0275textInterpolate1"]("Step - ",n+1,""),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("matTooltip",null==e?null:e.label),g["\u0275\u0275advance"](1),g["\u0275\u0275textInterpolate1"](" ",null==e?null:e.label," "),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("ngStyle",g["\u0275\u0275pureFunction2"](10,Tt,null==e?null:e.status_color,null==e?null:e.status_bg_color)),g["\u0275\u0275advance"](1),g["\u0275\u0275textInterpolate1"](" ",(null==e?null:e.status_name)||"-"," "),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("ngIf",null==e?null:e.action_icon),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",!(null!=e&&e.action_icon)),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngStyle",g["\u0275\u0275pureFunction1"](13,At,i.candidateChecklistsCurrentStatus.length-1==n?"none":""))}}function Rt(e,t){if(1&e&&(g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275elementStart"](1,"div",41),g["\u0275\u0275elementStart"](2,"div",42),g["\u0275\u0275text"](3,"Task"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](4,"div",43),g["\u0275\u0275text"](5,"Status"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](6,"div",43),g["\u0275\u0275text"](7,"Action"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](8,"div",44),g["\u0275\u0275template"](9,jt,17,15,"ng-container",18),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementContainerEnd"]()),2&e){const e=g["\u0275\u0275nextContext"](5);g["\u0275\u0275advance"](9),g["\u0275\u0275property"]("ngForOf",e.candidateChecklistsCurrentStatus)}}function Ht(e,t){}function zt(e,t){if(1&e&&g["\u0275\u0275template"](0,Ht,0,0,"ng-template",76),2&e){g["\u0275\u0275nextContext"](9);const e=g["\u0275\u0275reference"](5);g["\u0275\u0275property"]("ngTemplateOutlet",e)}}function Bt(e,t){if(1&e&&(g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275elementStart"](1,"div",77),g["\u0275\u0275text"](2),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementContainerEnd"]()),2&e){const e=g["\u0275\u0275nextContext"]().$implicit,t=g["\u0275\u0275nextContext"]().$implicit,n=g["\u0275\u0275nextContext"](7);g["\u0275\u0275advance"](2),g["\u0275\u0275textInterpolate1"](" ",n.currentSelectedSectionData.response[t.key][e.key]||"-"," ")}}function Nt(e,t){if(1&e&&(g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275elementStart"](1,"div",77),g["\u0275\u0275text"](2),g["\u0275\u0275pipe"](3,"dateFormat"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementContainerEnd"]()),2&e){const e=g["\u0275\u0275nextContext"]().$implicit,t=g["\u0275\u0275nextContext"]().$implicit,n=g["\u0275\u0275nextContext"](7);g["\u0275\u0275advance"](2),g["\u0275\u0275textInterpolate1"](" ",g["\u0275\u0275pipeBind2"](3,1,n.currentSelectedSectionData.response[t.key][e.key],"DD MMM YYYY")," ")}}function Zt(e,t){if(1&e&&(g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275elementStart"](1,"div",77),g["\u0275\u0275text"](2),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementContainerEnd"]()),2&e){const e=g["\u0275\u0275nextContext"]().$implicit,t=g["\u0275\u0275nextContext"]().$implicit,n=g["\u0275\u0275nextContext"](7);g["\u0275\u0275advance"](2),g["\u0275\u0275textInterpolate1"](" ",n.displayCheckboxValues(e,n.currentSelectedSectionData.response[t.key][e.key])," ")}}function $t(e,t){if(1&e&&(g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275element"](1,"div",78),g["\u0275\u0275elementContainerEnd"]()),2&e){const e=g["\u0275\u0275nextContext"]().$implicit,t=g["\u0275\u0275nextContext"]().$implicit,n=g["\u0275\u0275nextContext"](7);g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("innerHTML",n.currentSelectedSectionData.response[t.key][e.key]||"-",g["\u0275\u0275sanitizeHtml"])}}function Wt(e,t){if(1&e&&(g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275elementStart"](1,"a",79),g["\u0275\u0275text"](2),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("href",e.key,g["\u0275\u0275sanitizeUrl"]),g["\u0275\u0275advance"](1),g["\u0275\u0275textInterpolate1"](" ",e.original_name," ")}}function Yt(e,t){1&e&&(g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275elementStart"](1,"div",77),g["\u0275\u0275text"](2,"No Files Uploaded"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementContainerEnd"]())}function Ut(e,t){if(1&e&&(g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275template"](1,Wt,3,2,"ng-container",18),g["\u0275\u0275template"](2,Yt,3,0,"ng-container",1),g["\u0275\u0275elementContainerEnd"]()),2&e){const e=g["\u0275\u0275nextContext"]().$implicit,t=g["\u0275\u0275nextContext"]().$implicit,n=g["\u0275\u0275nextContext"](7);g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngForOf",n.getFileArray(n.currentSelectedSectionData.response[t.key][e.key])),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",0==n.getFileArray(n.currentSelectedSectionData.response[t.key][e.key]).length)}}const Qt=function(){return["rich-text","file-upload"]},Xt=function(){return["text","number","url","multiple-choice","dropdown"]},qt=function(){return["date"]},Kt=function(){return["checkbox"]},Jt=function(){return["rich-text"]},en=function(){return["file-upload"]};function tn(e,t){if(1&e&&(g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275elementStart"](1,"div"),g["\u0275\u0275elementStart"](2,"div",74),g["\u0275\u0275elementStart"](3,"div",75),g["\u0275\u0275text"](4),g["\u0275\u0275template"](5,zt,1,1,void 0,1),g["\u0275\u0275elementEnd"](),g["\u0275\u0275template"](6,Bt,3,1,"ng-container",1),g["\u0275\u0275template"](7,Nt,4,4,"ng-container",1),g["\u0275\u0275template"](8,Zt,3,1,"ng-container",1),g["\u0275\u0275template"](9,$t,2,1,"ng-container",1),g["\u0275\u0275template"](10,Ut,3,2,"ng-container",1),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;g["\u0275\u0275advance"](1),g["\u0275\u0275classMapInterpolate1"]("col-",g["\u0275\u0275pureFunction0"](11,Qt).includes(e.fieldType)?"12":"7"," p-0"),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("matTooltip",e.label),g["\u0275\u0275advance"](1),g["\u0275\u0275textInterpolate1"](" ",e.label," "),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",e.isMandatory),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",g["\u0275\u0275pureFunction0"](12,Xt).includes(e.fieldType)),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",g["\u0275\u0275pureFunction0"](13,qt).includes(e.fieldType)),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",g["\u0275\u0275pureFunction0"](14,Kt).includes(e.fieldType)),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",g["\u0275\u0275pureFunction0"](15,Jt).includes(e.fieldType)),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",g["\u0275\u0275pureFunction0"](16,en).includes(e.fieldType))}}function nn(e,t){if(1&e&&(g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275elementStart"](1,"div",73),g["\u0275\u0275text"](2),g["\u0275\u0275elementEnd"](),g["\u0275\u0275template"](3,tn,11,17,"ng-container",18),g["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;g["\u0275\u0275advance"](2),g["\u0275\u0275textInterpolate1"](" ",e.label," "),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngForOf",null==e?null:e.fields)}}function an(e,t){if(1&e&&(g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275template"](1,nn,4,2,"ng-container",18),g["\u0275\u0275elementContainerEnd"]()),2&e){const e=g["\u0275\u0275nextContext"](6);g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngForOf",e.currentSelectedSectionData.master.tasks)}}function on(e,t){if(1&e&&(g["\u0275\u0275elementStart"](0,"div",83),g["\u0275\u0275text"](1),g["\u0275\u0275elementEnd"]()),2&e){const e=g["\u0275\u0275nextContext"](2).$implicit;g["\u0275\u0275property"]("matTooltip",e.label),g["\u0275\u0275advance"](1),g["\u0275\u0275textInterpolate1"](" ",e.label," ")}}function rn(e,t){}function sn(e,t){if(1&e&&g["\u0275\u0275template"](0,rn,0,0,"ng-template",76),2&e){g["\u0275\u0275nextContext"](10);const e=g["\u0275\u0275reference"](5);g["\u0275\u0275property"]("ngTemplateOutlet",e)}}function cn(e,t){if(1&e&&(g["\u0275\u0275elementStart"](0,"div",75),g["\u0275\u0275text"](1),g["\u0275\u0275template"](2,sn,1,1,void 0,1),g["\u0275\u0275elementEnd"]()),2&e){const e=g["\u0275\u0275nextContext"](2).$implicit;g["\u0275\u0275property"]("matTooltip",e.label),g["\u0275\u0275advance"](1),g["\u0275\u0275textInterpolate1"](" ",e.label," "),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",e.isMandatory)}}function ln(e,t){if(1&e&&(g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275elementStart"](1,"div",77),g["\u0275\u0275text"](2),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementContainerEnd"]()),2&e){const e=g["\u0275\u0275nextContext"](2).$implicit,t=g["\u0275\u0275nextContext"](7);g["\u0275\u0275advance"](2),g["\u0275\u0275textInterpolate1"](" ",t.currentSelectedSectionData.response[e.key]," ")}}function dn(e,t){if(1&e&&(g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275elementStart"](1,"div",77),g["\u0275\u0275text"](2),g["\u0275\u0275pipe"](3,"masterData"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementContainerEnd"]()),2&e){const e=g["\u0275\u0275nextContext"](2).$implicit,t=g["\u0275\u0275nextContext"](7);g["\u0275\u0275advance"](2),g["\u0275\u0275textInterpolate1"](" ",g["\u0275\u0275pipeBind2"](3,1,t.currentSelectedSectionData.response[e.key],t.getDynamicProperty(e.masterDataKey))," ")}}function gn(e,t){if(1&e&&(g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275elementStart"](1,"div",77),g["\u0275\u0275text"](2),g["\u0275\u0275pipe"](3,"dateFormat"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementContainerEnd"]()),2&e){const e=g["\u0275\u0275nextContext"](2).$implicit,t=g["\u0275\u0275nextContext"](7);g["\u0275\u0275advance"](2),g["\u0275\u0275textInterpolate1"](" ",g["\u0275\u0275pipeBind2"](3,1,t.currentSelectedSectionData.response[e.key],"DD MMM YYYY")," ")}}function pn(e,t){if(1&e&&(g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275elementStart"](1,"div",77),g["\u0275\u0275text"](2),g["\u0275\u0275pipe"](3,"dateFormat"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementContainerEnd"]()),2&e){const e=g["\u0275\u0275nextContext"](2).$implicit,t=g["\u0275\u0275nextContext"](7);g["\u0275\u0275advance"](2),g["\u0275\u0275textInterpolate1"](" ",g["\u0275\u0275pipeBind2"](3,1,t.currentSelectedSectionData.response[e.key],"MMM YYYY")," ")}}function un(e,t){if(1&e&&(g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275elementStart"](1,"mat-checkbox",84),g["\u0275\u0275elementStart"](2,"span",85),g["\u0275\u0275text"](3),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementContainerEnd"]()),2&e){const e=g["\u0275\u0275nextContext"](2).$implicit,t=g["\u0275\u0275nextContext"](7);g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("checked",t.currentSelectedSectionData.response[e.key])("disabled",!0),g["\u0275\u0275advance"](2),g["\u0275\u0275textInterpolate1"](" ",e.label," ")}}const hn=function(){return["section-title","checkbox"]},Cn=function(){return["text-no-space","alpha-numeric-text","text","email","phone","ifsc","number","text-number"]},mn=function(){return["single-select"]},fn=function(){return["month-year-picker"]};function vn(e,t){if(1&e&&(g["\u0275\u0275elementStart"](0,"div"),g["\u0275\u0275elementStart"](1,"div",74),g["\u0275\u0275template"](2,on,2,2,"div",81),g["\u0275\u0275template"](3,cn,3,3,"div",82),g["\u0275\u0275template"](4,ln,3,1,"ng-container",1),g["\u0275\u0275template"](5,dn,4,4,"ng-container",1),g["\u0275\u0275template"](6,gn,4,4,"ng-container",1),g["\u0275\u0275template"](7,pn,4,4,"ng-container",1),g["\u0275\u0275template"](8,un,4,3,"ng-container",1),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()),2&e){const e=g["\u0275\u0275nextContext"]().$implicit;g["\u0275\u0275classMapInterpolate1"]("col-",e.col," p-0"),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("ngIf","section-title"==e.fieldType),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",!g["\u0275\u0275pureFunction0"](10,hn).includes(e.fieldType)),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",g["\u0275\u0275pureFunction0"](11,Cn).includes(e.fieldType)),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",g["\u0275\u0275pureFunction0"](12,mn).includes(e.fieldType)),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",g["\u0275\u0275pureFunction0"](13,qt).includes(e.fieldType)),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",g["\u0275\u0275pureFunction0"](14,fn).includes(e.fieldType)),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",g["\u0275\u0275pureFunction0"](15,Kt).includes(e.fieldType))}}function xn(e,t){if(1&e&&(g["\u0275\u0275elementStart"](0,"div",87),g["\u0275\u0275text"](1),g["\u0275\u0275elementEnd"]()),2&e){const e=g["\u0275\u0275nextContext"](2).$implicit;g["\u0275\u0275advance"](1),g["\u0275\u0275textInterpolate1"](" ",e.label," ")}}function bn(e,t){}function yn(e,t){if(1&e&&g["\u0275\u0275template"](0,bn,0,0,"ng-template",76),2&e){g["\u0275\u0275nextContext"](11);const e=g["\u0275\u0275reference"](5);g["\u0275\u0275property"]("ngTemplateOutlet",e)}}function Sn(e,t){if(1&e&&(g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275elementStart"](1,"div",77),g["\u0275\u0275text"](2),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementContainerEnd"]()),2&e){const e=g["\u0275\u0275nextContext"]().$implicit,t=g["\u0275\u0275nextContext"]().index,n=g["\u0275\u0275nextContext"](2).$implicit,i=g["\u0275\u0275nextContext"](7);g["\u0275\u0275advance"](2),g["\u0275\u0275textInterpolate1"](" ",i.currentSelectedSectionData.response[n.key][t][e.key]," ")}}function On(e,t){if(1&e&&(g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275elementStart"](1,"div",77),g["\u0275\u0275text"](2),g["\u0275\u0275pipe"](3,"masterData"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementContainerEnd"]()),2&e){const e=g["\u0275\u0275nextContext"]().$implicit,t=g["\u0275\u0275nextContext"]().index,n=g["\u0275\u0275nextContext"](2).$implicit,i=g["\u0275\u0275nextContext"](7);g["\u0275\u0275advance"](2),g["\u0275\u0275textInterpolate1"](" ",g["\u0275\u0275pipeBind2"](3,1,i.currentSelectedSectionData.response[n.key][t][e.key],i.getDynamicProperty(e.masterDataKey))," ")}}function Mn(e,t){if(1&e&&(g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275elementStart"](1,"div",77),g["\u0275\u0275text"](2),g["\u0275\u0275pipe"](3,"dateFormat"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementContainerEnd"]()),2&e){const e=g["\u0275\u0275nextContext"]().$implicit,t=g["\u0275\u0275nextContext"]().index,n=g["\u0275\u0275nextContext"](2).$implicit,i=g["\u0275\u0275nextContext"](7);g["\u0275\u0275advance"](2),g["\u0275\u0275textInterpolate1"](" ",g["\u0275\u0275pipeBind2"](3,1,i.currentSelectedSectionData.response[n.key][t][e.key],"DD MMM YYYY")," ")}}function _n(e,t){if(1&e&&(g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275elementStart"](1,"div",77),g["\u0275\u0275text"](2),g["\u0275\u0275pipe"](3,"dateFormat"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementContainerEnd"]()),2&e){const e=g["\u0275\u0275nextContext"]().$implicit,t=g["\u0275\u0275nextContext"]().index,n=g["\u0275\u0275nextContext"](2).$implicit,i=g["\u0275\u0275nextContext"](7);g["\u0275\u0275advance"](2),g["\u0275\u0275textInterpolate1"](" ",g["\u0275\u0275pipeBind2"](3,1,i.currentSelectedSectionData.response[n.key][t][e.key],"MMM YYYY")," ")}}function wn(e,t){if(1&e&&(g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275elementStart"](1,"mat-checkbox",84),g["\u0275\u0275elementStart"](2,"span",85),g["\u0275\u0275text"](3),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementContainerEnd"]()),2&e){const e=g["\u0275\u0275nextContext"]().$implicit,t=g["\u0275\u0275nextContext"]().index,n=g["\u0275\u0275nextContext"](2).$implicit,i=g["\u0275\u0275nextContext"](7);g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("checked",i.currentSelectedSectionData.response[n.key][t][e.key])("disabled",!0),g["\u0275\u0275advance"](2),g["\u0275\u0275textInterpolate1"](" ",e.label," ")}}const kn=function(){return["text-no-space","alpha-numeric-text","text","email","phone","percentage"]};function Pn(e,t){if(1&e&&(g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275elementStart"](1,"div"),g["\u0275\u0275elementStart"](2,"div",74),g["\u0275\u0275elementStart"](3,"div",75),g["\u0275\u0275text"](4),g["\u0275\u0275template"](5,yn,1,1,void 0,1),g["\u0275\u0275elementEnd"](),g["\u0275\u0275template"](6,Sn,3,1,"ng-container",1),g["\u0275\u0275template"](7,On,4,4,"ng-container",1),g["\u0275\u0275template"](8,Mn,4,4,"ng-container",1),g["\u0275\u0275template"](9,_n,4,4,"ng-container",1),g["\u0275\u0275template"](10,wn,4,3,"ng-container",1),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;g["\u0275\u0275advance"](1),g["\u0275\u0275classMapInterpolate1"]("col-",e.col," p-0"),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("matTooltip",e.label),g["\u0275\u0275advance"](1),g["\u0275\u0275textInterpolate1"](" ",e.label," "),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",e.isMandatory),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",g["\u0275\u0275pureFunction0"](11,kn).includes(e.fieldType)),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",g["\u0275\u0275pureFunction0"](12,mn).includes(e.fieldType)),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",g["\u0275\u0275pureFunction0"](13,qt).includes(e.fieldType)),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",g["\u0275\u0275pureFunction0"](14,fn).includes(e.fieldType)),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",g["\u0275\u0275pureFunction0"](15,Kt).includes(e.fieldType))}}function Dn(e,t){if(1&e&&(g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275elementStart"](1,"div",88),g["\u0275\u0275template"](2,Pn,11,16,"ng-container",18),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementContainerEnd"]()),2&e){const e=g["\u0275\u0275nextContext"](2).index,t=g["\u0275\u0275nextContext"](7);g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("ngForOf",t.currentSelectedSectionData.master.formSectionFields[e].formArrayFields)}}function En(e,t){if(1&e&&(g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275template"](1,xn,2,1,"div",86),g["\u0275\u0275template"](2,Dn,3,1,"ng-container",18),g["\u0275\u0275elementContainerEnd"]()),2&e){const e=g["\u0275\u0275nextContext"]().$implicit,t=g["\u0275\u0275nextContext"](7);g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf","document-details"==e.formArrayFieldType),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngForOf",t.currentSelectedSectionData.response[e.key])}}function In(e,t){if(1&e&&(g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275template"](1,vn,9,16,"div",80),g["\u0275\u0275template"](2,En,3,2,"ng-container",1),g["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf","form-array"!=e.fieldType),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf","form-array"==e.fieldType)}}function Vn(e,t){if(1&e&&(g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275template"](1,In,3,2,"ng-container",18),g["\u0275\u0275elementContainerEnd"]()),2&e){const e=g["\u0275\u0275nextContext"](6);g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngForOf",e.currentSelectedSectionData.master.formSectionFields)}}function Tn(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div",89),g["\u0275\u0275elementStart"](1,"div",90),g["\u0275\u0275listener"]("click",(function(){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](6).openRejectDialog()})),g["\u0275\u0275text"](2,"Reject"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](3,"div",91),g["\u0275\u0275listener"]("click",(function(){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](6).approveOnboardingSection()})),g["\u0275\u0275text"](4," Approve "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](5,"div",92,93),g["\u0275\u0275listener"]("click",(function(){g["\u0275\u0275restoreView"](e);const t=g["\u0275\u0275reference"](7),n=g["\u0275\u0275nextContext"](6),i=g["\u0275\u0275reference"](7);return n.openWorkflowStatusOverlay(t,i)})),g["\u0275\u0275elementStart"](8,"mat-icon",94),g["\u0275\u0275text"](9,"arrow_drop_up"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}}const Fn=function(){return[1,2]},Ln=function(e,t,n){return[e,t,n,0,"E"]};function Gn(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275elementStart"](1,"div",67),g["\u0275\u0275elementStart"](2,"mat-icon",68),g["\u0275\u0275listener"]("click",(function(){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](5).closeDetailViewOfChecklist()})),g["\u0275\u0275text"](3," arrow_back "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](4,"span",69),g["\u0275\u0275text"](5),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](6,"div",70),g["\u0275\u0275elementStart"](7,"div",71),g["\u0275\u0275template"](8,an,2,1,"ng-container",1),g["\u0275\u0275template"](9,Vn,2,1,"ng-container",1),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275template"](10,Tn,10,0,"div",72),g["\u0275\u0275pipe"](11,"access"),g["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=g["\u0275\u0275nextContext"](5);g["\u0275\u0275advance"](5),g["\u0275\u0275textInterpolate"](e.currentSelectedSectionData.master.label),g["\u0275\u0275advance"](3),g["\u0275\u0275property"]("ngIf",null==e.currentSectionDetails?null:e.currentSectionDetails.is_checklist),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",!(null!=e.currentSectionDetails&&e.currentSectionDetails.is_checklist)),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",g["\u0275\u0275pipeBindV"](11,4,g["\u0275\u0275pureFunction3"](11,Ln,!g["\u0275\u0275pureFunction0"](10,Fn).includes(null==e.currentSectionDetails?null:e.currentSectionDetails.action_id)&&e.access.moduleId.onboarding,e.access.subModuleId.onboardingCandidates,e.access.sectionId.onboardingCandidateDetailView)))}}function An(e,t){if(1&e&&(g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275template"](1,Rt,10,1,"ng-container",1),g["\u0275\u0275template"](2,Gn,12,15,"ng-container",1),g["\u0275\u0275elementContainerEnd"]()),2&e){const e=g["\u0275\u0275nextContext"](4);g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf","list-view"==e.checklistViewType),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf","detail-view"==e.checklistViewType)}}function jn(e,t){1&e&&(g["\u0275\u0275elementStart"](0,"div",95),g["\u0275\u0275element"](1,"img",96),g["\u0275\u0275elementStart"](2,"div",97),g["\u0275\u0275text"](3,"Checklists Data Not Found"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]())}function Rn(e,t){if(1&e&&(g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275template"](1,An,3,2,"ng-container",1),g["\u0275\u0275template"](2,jn,4,0,"div",40),g["\u0275\u0275elementContainerEnd"]()),2&e){const e=g["\u0275\u0275nextContext"](3);g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",e.candidateChecklistsCurrentStatus.length>0),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",0==e.candidateChecklistsCurrentStatus.length)}}const Hn=function(e,t,n){return[e,t,n,0,"C"]},zn=function(e){return{"pointer-events":e}};function Bn(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275elementStart"](1,"div",10),g["\u0275\u0275elementStart"](2,"div",11),g["\u0275\u0275elementStart"](3,"mat-icon",12),g["\u0275\u0275listener"]("click",(function(){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](2).navigateToHome()})),g["\u0275\u0275text"](4," keyboard_arrow_left "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275template"](5,yt,1,1,"img",13),g["\u0275\u0275template"](6,St,1,0,"app-user-image",14),g["\u0275\u0275elementStart"](7,"div",15),g["\u0275\u0275elementStart"](8,"div",16),g["\u0275\u0275text"](9),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](10,"div",17),g["\u0275\u0275template"](11,_t,6,12,"ng-container",18),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](12,"div",19),g["\u0275\u0275template"](13,wt,7,0,"svg",20),g["\u0275\u0275pipe"](14,"access"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275element"](15,"img",21),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](16,"div",22),g["\u0275\u0275elementStart"](17,"div",23),g["\u0275\u0275template"](18,Dt,6,13,"ng-container",18),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](19,"div",24),g["\u0275\u0275template"](20,Et,7,1,"ng-container",1),g["\u0275\u0275template"](21,Rn,3,2,"ng-container",1),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=g["\u0275\u0275nextContext"](2);g["\u0275\u0275advance"](5),g["\u0275\u0275property"]("ngIf",null==e.basicData?null:e.basicData.profile_image),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",!(null!=e.basicData&&e.basicData.profile_image)),g["\u0275\u0275advance"](3),g["\u0275\u0275textInterpolate"](null==e.basicData?null:e.basicData.candidate_full_name),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("ngForOf",e.headerDetails),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("ngIf",g["\u0275\u0275pipeBindV"](14,9,g["\u0275\u0275pureFunction3"](15,Hn,!(null!=e.basicData&&e.basicData.date_of_joining)&&e.access.moduleId.onboarding,e.access.subModuleId.onboardingCandidates,e.access.sectionId.onboardingCandidateDetailView))),g["\u0275\u0275advance"](3),g["\u0275\u0275property"]("ngStyle",g["\u0275\u0275pureFunction1"](19,zn,e.isSectionLoading?"none":"")),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("ngForOf",e.menuList),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("ngIf",e.isSectionLoading),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",!e.isSectionLoading&&1==e.selectedMenu)}}function Nn(e,t){if(1&e&&(g["\u0275\u0275elementContainerStart"](0),g["\u0275\u0275template"](1,bt,6,1,"div",0),g["\u0275\u0275template"](2,Bn,22,21,"ng-container",1),g["\u0275\u0275elementContainerEnd"]()),2&e){const e=g["\u0275\u0275nextContext"]();g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",e.isLoading),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",!e.isLoading)}}function Zn(e,t){1&e&&(g["\u0275\u0275elementStart"](0,"span",98),g["\u0275\u0275text"](1,"*"),g["\u0275\u0275elementEnd"]())}function $n(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"app-menu-list-overlay",99),g["\u0275\u0275listener"]("onConfirmation",(function(t){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"]().onCandidateOnboardingWorkflowStatusChange(t)})),g["\u0275\u0275elementEnd"]()}if(2&e){const e=g["\u0275\u0275nextContext"]();g["\u0275\u0275property"]("list",e.onboardingTempWorkflowStatusMaster)("currentSelectedId",-1)}}const Wn=function(e,t,n){return[e,t,n,0,"V"]},Yn=[{path:"",component:gt},{path:":candidateId",component:(()=>{class e{constructor(e,t,n,i,a,o,s,c,l){this._atsMasterService=e,this._utilService=t,this._onboardingService=n,this._toaster=i,this._activatedRoute=a,this._router=o,this._viewContainerRef=s,this._overlay=c,this._dialog=l,this._onDestroy=new r.b,this.access=d,this.menuList=[],this.uiTextConfig={},this.headerDetails=[{icon:'<svg width="16" height="12" viewBox="0 0 16 12">\n              <path d="M2.23078 11.625C1.85193 11.625 1.53125 11.4937 1.26875 11.2312C1.00625 10.9687 0.875 10.648 0.875 10.2692V1.73078C0.875 1.35193 1.00625 1.03125 1.26875 0.76875C1.53125 0.50625 1.85193 0.375 2.23078 0.375H13.7692C14.148 0.375 14.4687 0.50625 14.7312 0.76875C14.9937 1.03125 15.125 1.35193 15.125 1.73078V10.2692C15.125 10.648 14.9937 10.9687 14.7312 11.2312C14.4687 11.4937 14.148 11.625 13.7692 11.625H2.23078ZM14 2.58169L8.36488 6.18891C8.30719 6.22159 8.24758 6.24731 8.18604 6.26606C8.1245 6.28481 8.06248 6.29419 7.99998 6.29419C7.93748 6.29419 7.87546 6.28481 7.81393 6.26606C7.75239 6.24731 7.69278 6.22159 7.63509 6.18891L1.99998 2.58169V10.2692C1.99998 10.3365 2.02162 10.3918 2.06489 10.4351C2.10817 10.4783 2.16346 10.5 2.23078 10.5H13.7692C13.8365 10.5 13.8918 10.4783 13.9351 10.4351C13.9783 10.3918 14 10.3365 14 10.2692V2.58169ZM7.99998 5.24998L13.8846 1.49998H2.11537L7.99998 5.24998ZM1.99998 2.75475V1.89733V1.91968V1.89589V2.75475Z"/>\n            </svg>',columnKey:"candidate_email",isFill:!0},{icon:'<svg width="14" height="14" viewBox="0 0 14 14">\n              <path d="M12.5802 13.375C11.1668 13.375 9.74685 13.0464 8.32041 12.3891C6.89398 11.7319 5.58341 10.8048 4.38871 9.60766C3.19399 8.41054 2.26803 7.09998 1.61082 5.67595C0.953606 4.25191 0.625 2.83317 0.625 1.41972C0.625 1.19266 0.7 1.00344 0.85 0.852063C1 0.700688 1.1875 0.625 1.4125 0.625H3.85862C4.04805 0.625 4.21512 0.686781 4.35983 0.810344C4.50454 0.933906 4.59661 1.08655 4.63602 1.26828L5.06583 3.47498C5.09564 3.67979 5.08939 3.85576 5.04708 4.00287C5.00478 4.14998 4.92881 4.27354 4.81919 4.37354L3.08699 6.0596C3.36584 6.57017 3.68435 7.0531 4.04253 7.50839C4.4007 7.96369 4.78844 8.39855 5.20574 8.81298C5.61729 9.22451 6.05479 9.60673 6.51826 9.95961C6.98172 10.3125 7.4822 10.6409 8.0197 10.9447L9.70287 9.24711C9.82018 9.125 9.96225 9.03943 10.1291 8.99039C10.2959 8.94135 10.4692 8.92933 10.649 8.95433L12.7317 9.37836C12.9211 9.42836 13.0757 9.525 13.1954 9.66826C13.3151 9.81153 13.375 9.97404 13.375 10.1558V12.5875C13.375 12.8125 13.2993 13 13.1479 13.15C12.9965 13.3 12.8073 13.375 12.5802 13.375ZM2.55479 4.99519L3.89326 3.71442C3.91729 3.69518 3.93292 3.66874 3.94013 3.63509C3.94734 3.60143 3.94614 3.57018 3.93653 3.54134L3.61056 1.86537C3.60095 1.82691 3.58413 1.79806 3.56009 1.77884C3.53605 1.7596 3.5048 1.74998 3.46634 1.74998H1.86248C1.83364 1.74998 1.80961 1.7596 1.79037 1.77884C1.77113 1.79806 1.76151 1.8221 1.76151 1.85095C1.79998 2.36345 1.88387 2.88413 2.01319 3.41298C2.14253 3.94183 2.32306 4.46923 2.55479 4.99519ZM9.07977 11.4769C9.57689 11.7086 10.0954 11.8858 10.6353 12.0084C11.1752 12.131 11.6798 12.2038 12.149 12.2269C12.1779 12.2269 12.2019 12.2173 12.2211 12.1981C12.2404 12.1788 12.25 12.1548 12.25 12.1259V10.5481C12.25 10.5096 12.2404 10.4783 12.2211 10.4543C12.2019 10.4303 12.1731 10.4134 12.1346 10.4038L10.5596 10.0836C10.5308 10.074 10.5055 10.0728 10.4839 10.08C10.4622 10.0872 10.4394 10.1029 10.4154 10.1269L9.07977 11.4769Z"/>\n            </svg>',columnKey:"phone",isFill:!0},{icon:'<svg width="16" height="16" viewBox="0 0 16 16" fill="none">\n              <path d="M6.16699 7.33594H9.83366" stroke-width="1.5" stroke-linecap="round"/>\n              <path d="M2.41379 5.66261C3.72712 -0.110728 12.2805 -0.104061 13.5871 5.66927C14.3538 9.05594 12.2471 11.9226 10.4005 13.6959C9.06046 14.9893 6.94046 14.9893 5.59379 13.6959C3.75379 11.9226 1.64712 9.04927 2.41379 5.66261Z" stroke-width="1.5"/>\n            </svg>',columnKey:"location",isFill:!1},{icon:'<svg width="16" height="14" viewBox="0 0 16 14">\n              <path d="M2.23078 13.3749C1.85193 13.3749 1.53125 13.2437 1.26875 12.9812C1.00625 12.7187 0.875 12.398 0.875 12.0191V4.23074C0.875 3.85189 1.00625 3.53121 1.26875 3.26871C1.53125 3.00621 1.85193 2.87496 2.23078 2.87496H5.375V1.60576C5.375 1.22692 5.50625 0.90625 5.76875 0.64375C6.03125 0.38125 6.35192 0.25 6.73077 0.25H9.26919C9.64804 0.25 9.96871 0.38125 10.2312 0.64375C10.4937 0.90625 10.625 1.22692 10.625 1.60576V2.87496H13.7692C14.148 2.87496 14.4687 3.00621 14.7312 3.26871C14.9937 3.53121 15.125 3.85189 15.125 4.23074V12.0191C15.125 12.398 14.9937 12.7187 14.7312 12.9812C14.4687 13.2437 14.148 13.3749 13.7692 13.3749H2.23078ZM2.23078 12.2499H13.7692C13.8269 12.2499 13.8798 12.2259 13.9279 12.1778C13.9759 12.1297 14 12.0768 14 12.0191V4.23074C14 4.17304 13.9759 4.12014 13.9279 4.07206C13.8798 4.02398 13.8269 3.99994 13.7692 3.99994H2.23078C2.17308 3.99994 2.12018 4.02398 2.07209 4.07206C2.02402 4.12014 1.99998 4.17304 1.99998 4.23074V12.0191C1.99998 12.0768 2.02402 12.1297 2.07209 12.1778C2.12018 12.2259 2.17308 12.2499 2.23078 12.2499ZM6.49998 2.87496H9.49998V1.60576C9.49998 1.54806 9.47594 1.49517 9.42787 1.44709C9.37978 1.39901 9.32689 1.37496 9.26919 1.37496H6.73077C6.67308 1.37496 6.62018 1.39901 6.57209 1.44709C6.52402 1.49517 6.49998 1.54806 6.49998 1.60576V2.87496Z"/>\n            </svg>',columnKey:"job_details",isFill:!0},{icon:'<svg width="16" height="14" viewBox="0 0 16 14">\n              <path d="M1.9138 13.4297C1.59746 13.4297 1.32848 13.3188 1.10685 13.0972C0.885227 12.8756 0.774414 12.6066 0.774414 12.2903V1.44409C0.774414 1.12775 0.885227 0.858762 1.10685 0.637125C1.32848 0.4155 1.59746 0.304688 1.9138 0.304688H6.75998C7.07631 0.304688 7.3453 0.4155 7.56694 0.637125C7.78856 0.858762 7.89938 1.12775 7.89938 1.44409V3.30469H14.0869C14.4032 3.30469 14.6722 3.4155 14.8938 3.63712C15.1155 3.85876 15.2263 4.12775 15.2263 4.44409V12.2903C15.2263 12.6066 15.1155 12.8756 14.8938 13.0972C14.6722 13.3188 14.4032 13.4297 14.0869 13.4297H1.9138ZM1.89938 12.3047H6.77439V10.4297H1.89938V12.3047ZM1.89938 9.30469H6.77439V7.42967H1.89938V9.30469ZM1.89938 6.30469H6.77439V4.42967H1.89938V6.30469ZM1.89938 3.30469H6.77439V1.42967H1.89938V3.30469ZM7.89938 12.3047H14.1013V4.42967H7.89938V12.3047ZM10.1061 7.42967C9.94673 7.42967 9.81314 7.37574 9.70534 7.26788C9.59753 7.16001 9.54362 7.02636 9.54362 6.86693C9.54362 6.70748 9.59753 6.57391 9.70534 6.46622C9.81314 6.35853 9.94673 6.30469 10.1061 6.30469H11.7503C11.9097 6.30469 12.0433 6.35862 12.1511 6.46648C12.2589 6.57433 12.3128 6.70798 12.3128 6.86743C12.3128 7.02687 12.2589 7.16044 12.1511 7.26814C12.0433 7.37583 11.9097 7.42967 11.7503 7.42967H10.1061ZM10.1061 10.4297C9.94673 10.4297 9.81314 10.3757 9.70534 10.2679C9.59753 10.16 9.54362 10.0264 9.54362 9.86693C9.54362 9.70747 9.59753 9.57391 9.70534 9.46622C9.81314 9.35853 9.94673 9.30469 10.1061 9.30469H11.7503C11.9097 9.30469 12.0433 9.35862 12.1511 9.46648C12.2589 9.57433 12.3128 9.70798 12.3128 9.86743C12.3128 10.0269 12.2589 10.1604 12.1511 10.2681C12.0433 10.3758 11.9097 10.4297 11.7503 10.4297H10.1061Z"/>\n            </svg>',columnKey:"designation",isFill:!0}],this.isLoading=!0,this.isSectionLoading=!0,this.currentCandidateId=null,this.selectedMenu=null,this.checklistViewType="list-view",this.currentSectionDetails=null,this.currentSelectedSectionData={},this.nationalityMasterData=[],this.genderMasterData=[],this.salutationMasterData=[],this.relationshipStatusMasterData=[],this.relationshipTypeMasterData=[],this.candidateNationalityDocumentsMasterData=[],this.bloodGroupMasterData=[],this.skillsMasterData=[],this.skillLevelMasterData=[],this.educationLevelMasterData=[],this.onboardingWorkflowStatusMaster=[],this.onboardingTempWorkflowStatusMaster=[],this.countryMasterData=[],this.stateMasterData=[],this.cityMasterData=[],this.basicData={},this.candidateChecklistsCurrentStatus=[]}ngOnInit(){return Object(o.c)(this,void 0,void 0,(function*(){this._activatedRoute.params.subscribe(e=>{this.currentCandidateId=parseInt(e.candidateId)}),this.calculateDynamicContentHeight(),Promise.all([this.getNationality(),this.getGenderDetails(),this.getSalutationMaster(),this.getRelationshipStatusMaster(),this.getRelationshipTypeMaster(),this.getCandidateNationalityDocumentsMaster(),this.getBloodGroupMaster(),this.getSkills(),this.getSkillLevelMaster(),this.getEducationLevelList(),this.getCandidateOnboardingChecklistWorkflowStatusMaster(),this.getCountry(),this.getStateMasterForSettings(),this.getCityMasterForSettings()]),yield this.getAtsMasterUiConfig("onboardingDetailsPage"),yield this.getCandidateHeaderDetails(),this.isLoading=!1,this.menuList.length>0&&(yield this.switchToSelectedMenu(0))}))}onResize(){this.calculateDynamicContentHeight()}calculateDynamicContentHeight(){document.documentElement.style.setProperty("--dynamicHeight",window.innerHeight-57+"px"),document.documentElement.style.setProperty("--dynamicSubHeight",window.innerHeight-57-136+"px"),document.documentElement.style.setProperty("--dynamicAccessDeniedHeight",window.innerHeight-57+"px")}navigateToHome(){this._router.navigateByUrl("/main/ats/onboarding")}switchToSelectedMenu(e){return Object(o.c)(this,void 0,void 0,(function*(){this.menuList.forEach(e=>e.isSelected=!1),this.menuList[e].isSelected=!0,this.selectedMenu=this.menuList[e].id,this.calculateDynamicContentHeight(),1==this.selectedMenu&&this.closeDetailViewOfChecklist()}))}openDetailViewOfChecklist(e){return Object(o.c)(this,void 0,void 0,(function*(){this.isSectionLoading=!0,this.checklistViewType="detail-view",this.currentSectionDetails=e,yield this.getCandidateSingleSectionDetails()}))}closeDetailViewOfChecklist(){this.checklistViewType="list-view",this.getCandidateChecklistsCurrentStatus()}getDynamicProperty(e){return this[e]}displayCheckboxValues(e,t){let n="";return t&&t.length>0&&t.forEach((t,i)=>{(null==t?void 0:t.value)&&(0!=i&&(n+=", "),n+=e.fieldValues[i])}),n||"No Values Selected"}getFileArray(e){let t=[];return e&&"object"==typeof e?(Object.values(e).forEach(e=>{e&&t.push(e)}),t):t}approveOnboardingSection(){var e;if(1==(null===(e=this.currentSectionDetails)||void 0===e?void 0:e.action_id))return this.closeOverlay(),this._toaster.showInfo("No Changes Needed \u270b","The workflow is already set to selected status. No updates are necessary.",7e3);let t={candidateId:this.currentCandidateId,workflowDetails:Object.assign(Object.assign({},this.currentSectionDetails),{action_id:1})};this.triggerOnboardingCandidateSectionWorkflow(t)}openRejectDialog(){var e;return Object(o.c)(this,void 0,void 0,(function*(){if(2==(null===(e=this.currentSectionDetails)||void 0===e?void 0:e.action_id))return this.closeOverlay(),this._toaster.showInfo("No Changes Needed \u270b","The workflow is already set to selected status. No updates are necessary.",7e3);const{InboxRejectDialogComponent:t}=yield n.e(957).then(n.bind(null,"0IPq"));this._dialog.open(t,{width:"800px",disableClose:!0,data:{data:[]}}).afterClosed().subscribe(e=>{e&&this.rejectOnboardingSection(e)})}))}rejectOnboardingSection(e){var t;if(2==(null===(t=this.currentSectionDetails)||void 0===t?void 0:t.action_id))return this.closeOverlay(),this._toaster.showInfo("No Changes Needed \u270b","The workflow is already set to selected status. No updates are necessary.",7e3);let n={candidateId:this.currentCandidateId,workflowDetails:Object.assign(Object.assign({},this.currentSectionDetails),{action_id:2,comments:e})};this.triggerOnboardingCandidateSectionWorkflow(n)}onCandidateOnboardingWorkflowStatusChange(e){this.updateWorkflowOfOnboardingSection(e.id)}updateWorkflowOfOnboardingSection(e){var t;if((null===(t=this.currentSectionDetails)||void 0===t?void 0:t.action_id)==e)return this._toaster.showInfo("No Changes Needed \u270b","The workflow is already set to selected status. No updates are necessary.",7e3);this.closeOverlay();let n={candidateId:this.currentCandidateId,workflowDetails:Object.assign(Object.assign({},this.currentSectionDetails),{action_id:e})};this.triggerOnboardingCandidateSectionWorkflow(n)}openWorkflowStatusOverlay(e,t){var n;if(!(null===(n=this.overlayRef)||void 0===n?void 0:n.hasAttached())){const n=this._overlay.position().flexibleConnectedTo(e).withFlexibleDimensions(!0).withPush(!0).withViewportMargin(10).withGrowAfterOpen(!0).withPositions([{originX:"center",originY:"top",overlayX:"center",overlayY:"bottom"}]);n.withDefaultOffsetY(-5);const i=this._overlay.scrollStrategies.close();this.overlayRef=this._overlay.create({positionStrategy:n,scrollStrategy:i,hasBackdrop:!0,backdropClass:"",panelClass:["pop-up"]});const a=new l.h(t,this._viewContainerRef);this.overlayRef.attach(a),this.overlayRef.backdropClick().subscribe(()=>{this.closeOverlay()})}}closeOverlay(){var e;null===(e=this.overlayRef)||void 0===e||e.dispose()}openMarkAsHireDialog(){var e;return Object(o.c)(this,void 0,void 0,(function*(){const{HireCandidateComponent:t}=yield n.e(923).then(n.bind(null,"YEgr"));this._dialog.open(t,{height:"100%",width:"78%",maxWidth:"100vw",maxHeight:"100vw",position:{bottom:"0px",right:"0px"},disableClose:!0,data:{candidateId:this.currentCandidateId,jobId:null===(e=this.basicData)||void 0===e?void 0:e.job_id,uiTextConfig:this.uiTextConfig}}).afterClosed().subscribe(()=>{this.getCandidateHeaderDetails()})}))}getAtsMasterUiConfig(e){return Object(o.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._atsMasterService.getAtsMasterUiConfig(e).pipe(Object(s.a)(this._onDestroy)).subscribe({next:n=>{0==n.err?"onboardingDetailsPage"==e&&(this.uiTextConfig=n.data.uiTextConfig,this.menuList=n.data.menuList,this.menuList=this.menuList.filter(e=>this._utilService.filterSectionsBasedOnRole(null==e?void 0:e.access_permission,this.access.roleAccessList)&&(null==e?void 0:e.isActive))):this._toaster.showError("Error",n.msg,7e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Master Data Retrieval Failed!",7e3),n()}}))}))}getCandidateHeaderDetails(){return Object(o.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._onboardingService.getCandidateHeaderDetails(this.currentCandidateId).pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.basicData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Header Details API Failed!",7e3),t()}}))}))}getCandidateChecklistsCurrentStatus(){return Object(o.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._onboardingService.getCandidateChecklistsCurrentStatus(this.currentCandidateId).pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.candidateChecklistsCurrentStatus=t.data:this._toaster.showError("Error",t.msg,7e3),this.isSectionLoading=!1,e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Failed To Retrieve Workflow Details!",7e3),this.isSectionLoading=!1,t()}}))}))}getCandidateSingleSectionDetails(){var e,t;return Object(o.c)(this,void 0,void 0,(function*(){let n={candidateId:this.currentCandidateId,sectionKey:null===(e=this.currentSectionDetails)||void 0===e?void 0:e.section_key,isChecklist:null===(t=this.currentSectionDetails)||void 0===t?void 0:t.is_checklist};return this.currentSelectedSectionData={},new Promise((e,t)=>this._onboardingService.getCandidateSingleSectionDetails(n).pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.currentSelectedSectionData=t.data:this._toaster.showError("Error",t.msg,7e3),this.isSectionLoading=!1,e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Failed To Retrieve Section Details!",7e3),this.isSectionLoading=!1,t()}}))}))}getNationality(){return Object(o.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getNationality().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.nationalityMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Nationality Master Data Retrieval Failed!",7e3),t()}}))}))}getGenderDetails(){return Object(o.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getGenderDetails().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.genderMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Gender Master Data Retrieval Failed!",7e3),t()}}))}))}getSalutationMaster(){return Object(o.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getSalutationMaster().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.salutationMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Salutation Master Data Retrieval Failed!",7e3),t()}}))}))}getRelationshipStatusMaster(){return Object(o.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getRelationshipStatusMaster().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.relationshipStatusMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Relationship Status Master Data Retrieval Failed!",7e3),t()}}))}))}getRelationshipTypeMaster(){return Object(o.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getRelationshipTypeMaster().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.relationshipTypeMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Relationship Type Master Data Retrieval Failed!",7e3),t()}}))}))}getCandidateNationalityDocumentsMaster(){return Object(o.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getCandidateNationalityDocumentsMaster().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.candidateNationalityDocumentsMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Candidate Nationality Documents Master Data Retrieval Failed!",7e3),t()}}))}))}getBloodGroupMaster(){return Object(o.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getBloodGroupMaster().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.bloodGroupMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Blood Group Master Data Retrieval Failed!",7e3),t()}}))}))}getSkills(){return Object(o.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getSkills().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.skillsMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Skill Master Data Retrieval Failed!",7e3),t()}}))}))}getSkillLevelMaster(){return Object(o.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getSkillLevelMaster().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.skillLevelMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Skill Level Master Data Retrieval Failed!",7e3),t()}}))}))}getEducationLevelList(){return Object(o.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getEducationLevelList().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.educationLevelMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Education Level Master Data Retrieval Failed!",7e3),t()}}))}))}getCandidateOnboardingChecklistWorkflowStatusMaster(){return Object(o.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._onboardingService.getCandidateOnboardingChecklistWorkflowStatusMaster().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?(this.onboardingWorkflowStatusMaster=t.data,this.onboardingTempWorkflowStatusMaster=t.data.filter(e=>![1,2].includes(e.id))):this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Master Data Retrieval Failed!",7e3),t()}}))}))}triggerOnboardingCandidateSectionWorkflow(e){return Object(o.c)(this,void 0,void 0,(function*(){return this.isSectionLoading=!0,new Promise((t,n)=>this._onboardingService.triggerOnboardingCandidateSectionWorkflow(e).pipe(Object(s.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?(this.closeDetailViewOfChecklist(),this._toaster.showSuccess("Success \u2705",e.msg,7e3)):this._toaster.showError("Error",e.msg,7e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Failed To Update Workflow Details!",7e3),this.isSectionLoading=!1,n()}}))}))}getCountry(){return Object(o.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getCountry().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.countryMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Country Master Data Retrieval Failed!",7e3),t()}}))}))}getStateMasterForSettings(){return Object(o.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getStateMasterForSettings().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.stateMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"State Master Data Retrieval Failed!",7e3),t()}}))}))}getCityMasterForSettings(){return Object(o.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getCityMasterForSettings().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.cityMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"City Master Data Retrieval Failed!",7e3),t()}}))}))}}return e.\u0275fac=function(t){return new(t||e)(g["\u0275\u0275directiveInject"](p.a),g["\u0275\u0275directiveInject"](u.a),g["\u0275\u0275directiveInject"](h.a),g["\u0275\u0275directiveInject"](m.a),g["\u0275\u0275directiveInject"](a.a),g["\u0275\u0275directiveInject"](a.g),g["\u0275\u0275directiveInject"](g.ViewContainerRef),g["\u0275\u0275directiveInject"](f.e),g["\u0275\u0275directiveInject"](v.b))},e.\u0275cmp=g["\u0275\u0275defineComponent"]({type:e,selectors:[["app-details-page"]],viewQuery:function(e,t){if(1&e&&g["\u0275\u0275viewQuery"](vt,!0),2&e){let e;g["\u0275\u0275queryRefresh"](e=g["\u0275\u0275loadQuery"]())&&(t.triggerCandidateOnboardingWorkflowStatusChange=e.first)}},hostBindings:function(e,t){1&e&&g["\u0275\u0275listener"]("resize",(function(){return t.onResize()}),!1,g["\u0275\u0275resolveWindow"])},decls:8,vars:23,consts:[["class","loading-container",4,"ngIf"],[4,"ngIf"],["mandatoryTemplate",""],["cdkConnectedOverlay","",3,"cdkConnectedOverlayOrigin"],["triggerCandidateOnboardingWorkflowStatusChange",""],[1,"loading-container"],[2,"max-width","60px","max-height","60px"],[2,"max-width","100%","max-height","100%",3,"src"],[1,"loading-wrapper"],[1,"loading"],[1,"header"],[1,"header-1"],[1,"back-icon",3,"click"],["class","icon",3,"src",4,"ngIf"],["imgWidth","56px","imgHeight","56px",4,"ngIf"],[1,"content"],[1,"name"],[1,"header-details"],[4,"ngFor","ngForOf"],[1,"header-2"],["style","cursor: pointer; z-index: 1","width","42","height","42","viewBox","0 0 42 42","fill","none",3,"click",4,"ngIf"],["src","https://assets.kebs.app/KEBS Background Candidate Details Page.png",1,"bg-image"],[1,"menu",3,"ngStyle"],[1,"sub-menu"],[1,"bg-container"],[1,"icon",3,"src"],["imgWidth","56px","imgHeight","56px"],[1,"single-item","col-4","p-0"],[1,"icon",3,"ngStyle","innerHTML"],[1,"text",3,"ngStyle","matTooltip"],["width","42","height","42","viewBox","0 0 42 42","fill","none",2,"cursor","pointer","z-index","1",3,"click"],["x","0.5","y","0.5","width","41","height","41","rx","20.5","fill","white"],["x","0.5","y","0.5","width","41","height","41","rx","20.5","stroke","#DADCE2"],["id","mask0_20300_249498","maskUnits","userSpaceOnUse","x","12","y","12","width","18","height","18",2,"mask-type","alpha"],["x","12","y","12","width","18","height","18","fill","#D9D9D9"],["mask","url(#mask0_20300_249498)"],["d","M21 28.125C20.0144 28.125 19.0881 27.938 18.2213 27.564C17.3544 27.19 16.6003 26.6824 15.9591 26.0413C15.3178 25.4001 14.8102 24.6462 14.4361 23.7795C14.062 22.9128 13.875 21.9867 13.875 21.0012C13.875 20.0158 14.062 19.0895 14.4361 18.2224C14.8102 17.3553 15.3178 16.601 15.9591 15.9596C16.6003 15.3182 17.3544 14.8104 18.2213 14.4362C19.0881 14.0621 20.0144 13.875 21 13.875C21.624 13.875 22.2288 13.9546 22.8144 14.1137C23.4 14.2728 23.9601 14.5029 24.4947 14.8039C24.6389 14.8846 24.7332 15.001 24.7774 15.1529C24.8216 15.3048 24.7957 15.4466 24.6995 15.5784C24.6034 15.7101 24.4762 15.7925 24.318 15.8257C24.1599 15.8589 24.0072 15.8351 23.8601 15.7543C23.4216 15.5091 22.9603 15.3221 22.4762 15.1933C21.9921 15.0644 21.5 15 21 15C19.325 15 17.9062 15.5812 16.7437 16.7437C15.5812 17.9062 15 19.325 15 21C15 22.675 15.5812 24.0937 16.7437 25.2562C17.9062 26.4187 19.325 27 21 27C21.3595 27 21.7121 26.969 22.0579 26.9069C22.4036 26.8449 22.7447 26.7519 23.0813 26.6279C23.2409 26.5798 23.3978 26.5755 23.5522 26.6149C23.7065 26.6543 23.8259 26.7441 23.9104 26.8844C23.9855 27.0153 24.0014 27.1564 23.9582 27.3079C23.9149 27.4593 23.8163 27.5634 23.6625 27.6201C23.2375 27.7923 22.8012 27.9194 22.3536 28.0016C21.906 28.0839 21.4548 28.125 21 28.125ZM26.4375 24.5625H24.75C24.5906 24.5625 24.457 24.5085 24.3492 24.4007C24.2414 24.2928 24.1875 24.1592 24.1875 23.9997C24.1875 23.8403 24.2414 23.7067 24.3492 23.599C24.457 23.4913 24.5906 23.4375 24.75 23.4375H26.4375V21.75C26.4375 21.5906 26.4914 21.457 26.5993 21.3492C26.7071 21.2414 26.8408 21.1875 27.0002 21.1875C27.1597 21.1875 27.2932 21.2414 27.4009 21.3492C27.5086 21.457 27.5625 21.5906 27.5625 21.75V23.4375H29.25C29.4094 23.4375 29.543 23.4914 29.6508 23.5993C29.7586 23.7071 29.8125 23.8408 29.8125 24.0002C29.8125 24.1597 29.7586 24.2932 29.6508 24.4009C29.543 24.5086 29.4094 24.5625 29.25 24.5625H27.5625V26.25C27.5625 26.4094 27.5085 26.5429 27.4007 26.6507C27.2928 26.7586 27.1592 26.8125 26.9997 26.8125C26.8403 26.8125 26.7067 26.7586 26.599 26.6507C26.4913 26.5429 26.4375 26.4094 26.4375 26.25V24.5625ZM19.9356 22.6096L26.9394 15.5942C27.0433 15.4904 27.1738 15.4373 27.331 15.4349C27.4882 15.4325 27.6211 15.4856 27.7298 15.5942C27.8384 15.7029 27.8927 15.8346 27.8927 15.9894C27.8927 16.1442 27.8384 16.2759 27.7298 16.3846L20.4101 23.7158C20.2745 23.8514 20.1163 23.9192 19.9356 23.9192C19.7548 23.9192 19.5966 23.8514 19.461 23.7158L17.4029 21.6577C17.299 21.5538 17.2459 21.4233 17.2435 21.2661C17.2411 21.1089 17.2942 20.976 17.4029 20.8673C17.5115 20.7586 17.6432 20.7043 17.7981 20.7043C17.9529 20.7043 18.0846 20.7586 18.1932 20.8673L19.9356 22.6096Z","fill","#526179"],[1,"single-menu",3,"ngClass","click"],[3,"ngStyle","innerHTML"],[1,"loading-section-container"],["class","empty-state",4,"ngIf"],[1,"checklist-header"],[1,"col-6","pl-0","header-text",2,"left","56px"],[1,"col-3","pl-0","header-text"],[1,"checklist-content"],[1,"single-checklist-content"],[1,"col-6","pl-0","step-row"],["class","step-count",4,"ngIf"],["class","step-count-completed",4,"ngIf"],[1,"step-column"],[1,"step-text"],[1,"section-text",3,"matTooltip"],[1,"col-3","pl-0"],[1,"status",3,"ngStyle"],["class","action-status",3,"ngStyle","click",4,"ngIf"],["class","action-btn",3,"ngStyle","click",4,"ngIf"],[1,"full-divider",3,"ngStyle"],[1,"step-count"],[1,"step-count-completed"],["width","12","height","9","viewBox","0 0 12 9","fill","none"],["id","path-1-inside-1_24674_146504","fill","white"],["d","M4.09711 6.79881L10.6635 0.232438C10.8184 0.0774793 11.0025 0 11.2155 0C11.4286 0 11.6126 0.0774793 11.7676 0.232438C11.9225 0.387397 12 0.57141 12 0.784478C12 0.997546 11.9225 1.18156 11.7676 1.33652L4.63946 8.46462C4.4845 8.61958 4.30372 8.69706 4.09711 8.69706C3.8905 8.69706 3.70971 8.61958 3.55475 8.46462L0.223141 5.13301C0.0681818 4.97805 -0.00606921 4.79403 0.000387397 4.58097C0.00684401 4.3679 0.0875517 4.18388 0.24251 4.02893C0.397469 3.87397 0.581482 3.79649 0.794551 3.79649C1.00762 3.79649 1.19163 3.87397 1.34659 4.02893L4.09711 6.79881Z"],["d","M4.09711 6.79881L10.6635 0.232438C10.8184 0.0774793 11.0025 0 11.2155 0C11.4286 0 11.6126 0.0774793 11.7676 0.232438C11.9225 0.387397 12 0.57141 12 0.784478C12 0.997546 11.9225 1.18156 11.7676 1.33652L4.63946 8.46462C4.4845 8.61958 4.30372 8.69706 4.09711 8.69706C3.8905 8.69706 3.70971 8.61958 3.55475 8.46462L0.223141 5.13301C0.0681818 4.97805 -0.00606921 4.79403 0.000387397 4.58097C0.00684401 4.3679 0.0875517 4.18388 0.24251 4.02893C0.397469 3.87397 0.581482 3.79649 0.794551 3.79649C1.00762 3.79649 1.19163 3.87397 1.34659 4.02893L4.09711 6.79881Z","fill","white"],["d","M4.09711 6.79881L1.25877 9.6173L4.08717 12.4656L6.92553 9.62724L4.09711 6.79881ZM10.6635 0.232438L7.83505 -2.59599L10.6635 0.232438ZM11.7676 1.33652L8.93913 -1.49191L11.7676 1.33652ZM4.63946 8.46462L7.46789 11.293L4.63946 8.46462ZM3.55475 8.46462L0.726325 11.293L3.55475 8.46462ZM0.223141 5.13301L3.05157 2.30458L0.223141 5.13301ZM1.34659 4.02893L4.18494 1.21042L4.17502 1.2005L1.34659 4.02893ZM6.92553 9.62724L13.4919 3.06087L7.83505 -2.59599L1.26868 3.97038L6.92553 9.62724ZM13.4919 3.06087C13.2393 3.31344 12.8994 3.56382 12.4699 3.74465C12.0382 3.92642 11.6058 4 11.2155 4V-4C9.90821 -4 8.71622 -3.47716 7.83505 -2.59599L13.4919 3.06087ZM11.2155 4C10.8252 4 10.3928 3.92642 9.9611 3.74465C9.53163 3.56382 9.19171 3.31344 8.93913 3.06087L14.596 -2.59599C13.7148 -3.47715 12.5228 -4 11.2155 -4V4ZM8.93913 3.06087C8.68656 2.80829 8.43618 2.46837 8.25535 2.0389C8.07358 1.60719 8 1.1748 8 0.784478H16C16 -0.522837 15.4772 -1.71482 14.596 -2.59599L8.93913 3.06087ZM8 0.784478C8 0.394158 8.07358 -0.0382324 8.25535 -0.469939C8.43618 -0.899416 8.68656 -1.23933 8.93913 -1.49191L14.596 4.16495C15.4772 3.28378 16 2.09179 16 0.784478H8ZM8.93913 -1.49191L1.81104 5.63619L7.46789 11.293L14.596 4.16495L8.93913 -1.49191ZM1.81104 5.63619C2.05237 5.39486 2.38412 5.14573 2.81198 4.96237C3.24445 4.77702 3.68654 4.69706 4.09711 4.69706V12.6971C5.43393 12.6971 6.61329 12.1476 7.46789 11.293L1.81104 5.63619ZM4.09711 4.69706C4.50767 4.69706 4.94976 4.77702 5.38224 4.96237C5.81009 5.14573 6.14185 5.39486 6.38318 5.63619L0.726325 11.293C1.58093 12.1476 2.76028 12.6971 4.09711 12.6971V4.69706ZM6.38318 5.63619L3.05157 2.30458L-2.60529 7.96143L0.726325 11.293L6.38318 5.63619ZM3.05157 2.30458C3.3144 2.56742 3.57651 2.9257 3.76064 3.382C3.94577 3.84081 4.01083 4.29684 3.99855 4.70212L-3.99778 4.45981C-4.03868 5.80975 -3.51541 7.05131 -2.60529 7.96143L3.05157 2.30458ZM3.99855 4.70212C3.98722 5.07608 3.90664 5.48459 3.72891 5.8898C3.55203 6.29309 3.31327 6.61502 3.07094 6.85735L-2.58592 1.2005C-3.43897 2.05355 -3.95951 3.19703 -3.99778 4.45981L3.99855 4.70212ZM3.07094 6.85735C2.81836 7.10993 2.47845 7.36031 2.04897 7.54114C1.61726 7.72291 1.18487 7.79649 0.794551 7.79649V-0.203512C-0.512765 -0.203512 -1.70475 0.319333 -2.58592 1.2005L3.07094 6.85735ZM0.794551 7.79649C0.404231 7.79649 -0.0281603 7.72291 -0.459867 7.54114C-0.889345 7.36031 -1.22926 7.10993 -1.48184 6.85735L4.17502 1.2005C3.29385 0.319333 2.10187 -0.203512 0.794551 -0.203512V7.79649ZM-1.49174 6.84741L1.25877 9.6173L6.93544 3.98033L4.18492 1.21044L-1.49174 6.84741Z","fill","white","mask","url(#path-1-inside-1_24674_146504)"],[1,"action-status",3,"ngStyle","click"],[3,"innerHTML"],[1,"action-btn",3,"ngStyle","click"],[1,"detail-header"],[1,"icon",3,"click"],[1,"text"],[1,"detail-content-main"],[1,"detail-content"],["class","detail-footer",4,"ngIf"],[1,"form-label-section"],[1,"field-width"],[1,"form-label",3,"matTooltip"],[3,"ngTemplateOutlet"],[1,"form-response"],[1,"form-response",3,"innerHTML"],["target","_blank",1,"link-text",3,"href"],[3,"class",4,"ngIf"],["class","form-label-section",3,"matTooltip",4,"ngIf"],["class","form-label",3,"matTooltip",4,"ngIf"],[1,"form-label-section",3,"matTooltip"],[3,"checked","disabled"],[1,"form-label"],["class","col-12 p-0 form-label-section",4,"ngIf"],[1,"col-12","p-0","form-label-section"],[1,"single-form-array"],[1,"detail-footer"],[1,"reject-btn",3,"click"],[1,"approve-btn",3,"click"],["cdkOverlayOrigin","",1,"more-btn",3,"click"],["triggerCandidateOnboardingWorkflowStatus","cdkOverlayOrigin","triggerCandidateOnboardingWorkflowStatusField",""],[1,"icon"],[1,"empty-state"],["src","https://assets.kebs.app/ATS-Empty-State.png"],[1,"no-data-title"],[1,"required-template"],[3,"list","currentSelectedId","onConfirmation"]],template:function(e,t){1&e&&(g["\u0275\u0275template"](0,xt,2,0,"div",0),g["\u0275\u0275pipe"](1,"access"),g["\u0275\u0275template"](2,Nn,3,2,"ng-container",1),g["\u0275\u0275pipe"](3,"access"),g["\u0275\u0275template"](4,Zn,2,0,"ng-template",null,2,g["\u0275\u0275templateRefExtractor"]),g["\u0275\u0275template"](6,$n,1,2,"ng-template",3,4,g["\u0275\u0275templateRefExtractor"])),2&e&&(g["\u0275\u0275property"]("ngIf",!g["\u0275\u0275pipeBindV"](1,3,g["\u0275\u0275pureFunction3"](15,Wn,t.access.moduleId.onboarding,t.access.subModuleId.onboardingCandidates,t.access.sectionId.onboardingCandidateDetailView))),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("ngIf",g["\u0275\u0275pipeBindV"](3,9,g["\u0275\u0275pureFunction3"](19,Wn,t.access.moduleId.onboarding,t.access.subModuleId.onboardingCandidates,t.access.sectionId.onboardingCandidateDetailView))),g["\u0275\u0275advance"](4),g["\u0275\u0275property"]("cdkConnectedOverlayOrigin",t.triggerCandidateOnboardingWorkflowStatus))},directives:[i.NgIf,f.a,x.a,S.a,i.NgForOf,i.NgStyle,pt.a,ut.a,i.NgClass,i.NgTemplateOutlet,ht.a,f.b,Ct.a],pipes:[E.a,I.a,mt.a,ft.a],styles:['.loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;background-color:#fff;overflow:hidden;height:var(--dynamicHeight)}.loading-container[_ngcontent-%COMP%]   .loading-wrapper[_ngcontent-%COMP%]{display:flex;vertical-align:center;justify-content:center;align-items:center}.loading-container[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]{color:rgba(0,0,0,.3);font-size:16px}.loading-container[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]:before{content:"Loading...";position:absolute;overflow:hidden;max-width:7em;white-space:nowrap;background:linear-gradient(270deg,var(--atsprimaryColor),var(--atssecondaryColor) 105.29%);background-clip:text;-webkit-background-clip:text;-webkit-text-fill-color:transparent;animation:loading 6s linear infinite}.header[_ngcontent-%COMP%]{justify-content:space-between;padding:12px 16px;height:82px;position:relative}.header[_ngcontent-%COMP%], .header[_ngcontent-%COMP%]   .header-1[_ngcontent-%COMP%]{display:flex;align-items:center}.header[_ngcontent-%COMP%]   .header-1[_ngcontent-%COMP%]{gap:24px;width:70%}.header[_ngcontent-%COMP%]   .header-1[_ngcontent-%COMP%]   .back-icon[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;border:1px solid #6e7b8f;border-radius:4px;font-size:14px;width:18px;height:18px;color:#6e7b8f;cursor:pointer}.header[_ngcontent-%COMP%]   .header-1[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{border-radius:50%;width:56px;height:56px}.header[_ngcontent-%COMP%]   .header-1[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{display:flex;gap:8px;flex-direction:column;width:calc(100% - 120px)}.header[_ngcontent-%COMP%]   .header-1[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .name[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:18px;font-weight:800;color:#1b2140}.header[_ngcontent-%COMP%]   .header-1[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .header-details[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;align-items:center;width:100%}.header[_ngcontent-%COMP%]   .header-1[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .header-details[_ngcontent-%COMP%]   .single-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.header[_ngcontent-%COMP%]   .header-1[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .header-details[_ngcontent-%COMP%]   .single-item[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{width:20px;height:20px}.header[_ngcontent-%COMP%]   .header-1[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .header-details[_ngcontent-%COMP%]   .single-item[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:400;color:#7d838b;text-overflow:ellipsis;overflow:hidden;white-space:nowrap;width:80%;border-right:2px solid #e8e9ee;padding-right:4%}.header[_ngcontent-%COMP%]   .header-2[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:end;gap:24px;width:30%}.header[_ngcontent-%COMP%]   .bg-image[_ngcontent-%COMP%]{position:absolute;right:0;height:100%}.menu[_ngcontent-%COMP%]{display:flex;justify-content:space-between;background-color:#111434;height:48px;padding:0 24px}.menu[_ngcontent-%COMP%]   .sub-menu[_ngcontent-%COMP%]{display:flex;align-items:end;gap:16px;height:100%}.menu[_ngcontent-%COMP%]   .sub-menu[_ngcontent-%COMP%]   .single-menu[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px;cursor:pointer}.menu[_ngcontent-%COMP%]   .sub-menu[_ngcontent-%COMP%]   .selected-menu[_ngcontent-%COMP%]{background-color:#fff;border-radius:12px 12px 0 0;height:80%;padding:8px 16px;gap:4px}.menu[_ngcontent-%COMP%]   .sub-menu[_ngcontent-%COMP%]   .unselected-menu[_ngcontent-%COMP%]{height:80%;padding:8px;gap:4px}.menu[_ngcontent-%COMP%]   .sub-menu[_ngcontent-%COMP%]   .selected-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:700;color:#111434;cursor:pointer}.menu[_ngcontent-%COMP%]   .sub-menu[_ngcontent-%COMP%]   .unselected-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:500;color:#8b95a5;cursor:pointer}.bg-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px;padding:16px;background-color:#fff;overflow:hidden}.bg-container[_ngcontent-%COMP%]   .loading-section-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;height:calc(var(--dynamicSubHeight) - 40px)}.bg-container[_ngcontent-%COMP%]   .loading-section-container[_ngcontent-%COMP%]   .loading-wrapper[_ngcontent-%COMP%]{display:flex;vertical-align:center;justify-content:center;align-items:center}.bg-container[_ngcontent-%COMP%]   .loading-section-container[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]{color:rgba(0,0,0,.3);font-size:16px}.bg-container[_ngcontent-%COMP%]   .loading-section-container[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]:before{content:"Loading...";position:absolute;overflow:hidden;max-width:7em;white-space:nowrap;background:linear-gradient(270deg,var(--atsprimaryColor),var(--atssecondaryColor) 105.29%);background-clip:text;-webkit-background-clip:text;-webkit-text-fill-color:transparent;animation:loading 6s linear infinite}@keyframes loading{0%{max-width:0}}.bg-container[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;height:var(--dynamicSubHeight)}.bg-container[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .no-data-title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:500;color:#1b2140;margin-bottom:4px}.bg-container[_ngcontent-%COMP%]   .checklist-header[_ngcontent-%COMP%]{display:flex;align-items:center;width:100%}.bg-container[_ngcontent-%COMP%]   .checklist-header[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:500;color:#7d838b}.bg-container[_ngcontent-%COMP%]   .checklist-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;height:calc(var(--dynamicSubHeight) - 60px);overflow-y:auto}.bg-container[_ngcontent-%COMP%]   .checklist-content[_ngcontent-%COMP%]   .single-checklist-content[_ngcontent-%COMP%]{display:flex;align-items:center;width:100%}.bg-container[_ngcontent-%COMP%]   .checklist-content[_ngcontent-%COMP%]   .single-checklist-content[_ngcontent-%COMP%]   .step-row[_ngcontent-%COMP%]{display:flex;align-items:center;gap:24px}.bg-container[_ngcontent-%COMP%]   .checklist-content[_ngcontent-%COMP%]   .single-checklist-content[_ngcontent-%COMP%]   .step-row[_ngcontent-%COMP%]   .step-count[_ngcontent-%COMP%]{background-color:#e8e9ee}.bg-container[_ngcontent-%COMP%]   .checklist-content[_ngcontent-%COMP%]   .single-checklist-content[_ngcontent-%COMP%]   .step-row[_ngcontent-%COMP%]   .step-count[_ngcontent-%COMP%], .bg-container[_ngcontent-%COMP%]   .checklist-content[_ngcontent-%COMP%]   .single-checklist-content[_ngcontent-%COMP%]   .step-row[_ngcontent-%COMP%]   .step-count-completed[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;border-radius:50%;min-width:32px;height:32px;font-family:var(--atsfontFamily);font-size:14px;font-weight:500;color:#7d838b}.bg-container[_ngcontent-%COMP%]   .checklist-content[_ngcontent-%COMP%]   .single-checklist-content[_ngcontent-%COMP%]   .step-row[_ngcontent-%COMP%]   .step-count-completed[_ngcontent-%COMP%]{background-color:var(--atsprimaryColor)}.bg-container[_ngcontent-%COMP%]   .checklist-content[_ngcontent-%COMP%]   .single-checklist-content[_ngcontent-%COMP%]   .step-row[_ngcontent-%COMP%]   .step-column[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:calc(100% - 56px)}.bg-container[_ngcontent-%COMP%]   .checklist-content[_ngcontent-%COMP%]   .single-checklist-content[_ngcontent-%COMP%]   .step-row[_ngcontent-%COMP%]   .step-column[_ngcontent-%COMP%]   .step-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:12px;font-weight:400;color:#a8acb2}.bg-container[_ngcontent-%COMP%]   .checklist-content[_ngcontent-%COMP%]   .single-checklist-content[_ngcontent-%COMP%]   .step-row[_ngcontent-%COMP%]   .step-column[_ngcontent-%COMP%]   .section-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:500;color:#272a47;overflow:hidden;white-space:nowrap;text-overflow:ellipsis}.bg-container[_ngcontent-%COMP%]   .checklist-content[_ngcontent-%COMP%]   .single-checklist-content[_ngcontent-%COMP%]   .status[_ngcontent-%COMP%]{border-radius:8px;padding:8px 12px;font-family:var(--atsfontFamily);font-size:12px;font-weight:500;width:-moz-fit-content;width:fit-content}.bg-container[_ngcontent-%COMP%]   .checklist-content[_ngcontent-%COMP%]   .single-checklist-content[_ngcontent-%COMP%]   .action-status[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;border-radius:8px;padding:8px 12px;font-family:var(--atsfontFamily);font-size:12px;font-weight:700;width:-moz-fit-content;width:fit-content;cursor:pointer}.bg-container[_ngcontent-%COMP%]   .checklist-content[_ngcontent-%COMP%]   .single-checklist-content[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%]{border:1px solid #1b2140;border-radius:8px;padding:7px 11px;cursor:pointer;font-family:var(--atsfontFamily);font-size:12px;font-weight:700;color:#45546e;width:-moz-fit-content;width:fit-content}.bg-container[_ngcontent-%COMP%]   .checklist-content[_ngcontent-%COMP%]   .full-divider[_ngcontent-%COMP%]{background-color:#8b95a5;min-width:2px;max-width:2px;margin-left:16px;min-height:24px}.bg-container[_ngcontent-%COMP%]   .checklist-content[_ngcontent-%COMP%]::-webkit-scrollbar{width:6px!important;height:0!important}.bg-container[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.bg-container[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{font-size:18px;width:18px;height:18px;color:#1c1b1f;cursor:pointer}.bg-container[_ngcontent-%COMP%]   .detail-header[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:18px;font-weight:700;color:#111434}.bg-container[_ngcontent-%COMP%]   .detail-content-main[_ngcontent-%COMP%]{height:calc(var(--dynamicSubHeight) - 110px);overflow-y:auto}.bg-container[_ngcontent-%COMP%]   .detail-content-main[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;padding-left:30px;row-gap:12px}.bg-container[_ngcontent-%COMP%]   .detail-content-main[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .form-label-section[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);color:#5f6c81;font-size:14px;font-weight:600;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;width:100%}.bg-container[_ngcontent-%COMP%]   .detail-content-main[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .single-form-array[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;align-items:center;width:100%;height:-moz-fit-content;height:fit-content;row-gap:12px}.bg-container[_ngcontent-%COMP%]   .detail-content-main[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .field-width[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:90%}.bg-container[_ngcontent-%COMP%]   .detail-content-main[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .field-width[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);color:#5f6c81;font-size:12px;font-weight:400;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;width:100%}.bg-container[_ngcontent-%COMP%]   .detail-content-main[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .field-width[_ngcontent-%COMP%]   .link-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);color:#1890ff;font-size:12px;font-weight:400;width:100%}.bg-container[_ngcontent-%COMP%]   .detail-content-main[_ngcontent-%COMP%]   .detail-content[_ngcontent-%COMP%]   .field-width[_ngcontent-%COMP%]   .form-response[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);color:#45546e;font-size:12px;font-weight:400;width:100%;word-wrap:break-word}.bg-container[_ngcontent-%COMP%]   .detail-footer[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:end;gap:8px}.bg-container[_ngcontent-%COMP%]   .detail-footer[_ngcontent-%COMP%]   .reject-btn[_ngcontent-%COMP%]{padding:7px 11px;background-color:#fff;border-radius:8px;border:1px solid #1b2140;font-family:var(--atsfontFamily);font-size:14px;font-weight:700;color:#1b2140;cursor:pointer}.bg-container[_ngcontent-%COMP%]   .detail-footer[_ngcontent-%COMP%]   .approve-btn[_ngcontent-%COMP%]{padding:8px 12px;background-color:var(--atsprimaryColor);border-radius:8px;font-family:var(--atsfontFamily);font-size:14px;font-weight:700;color:#fff;cursor:pointer}.bg-container[_ngcontent-%COMP%]   .detail-footer[_ngcontent-%COMP%]   .more-btn[_ngcontent-%COMP%]{height:36px;display:flex;align-items:center;justify-content:center;background-color:#fff;border-radius:8px;padding:0 11px;border:1px solid #7d838b;cursor:pointer}.bg-container[_ngcontent-%COMP%]   .detail-footer[_ngcontent-%COMP%]   .more-btn[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{font-size:20px;width:20px;height:20px;color:#7d838b}.required-template[_ngcontent-%COMP%]{color:#cf0001}']}),e})()}];let Un=(()=>{class e{}return e.\u0275mod=g["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=g["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[a.k.forChild(Yn)],a.k]}),e})();var Qn=n("1+mW"),Xn=n("Xi0T"),qn=n("qFsG"),Kn=n("1jcm"),Jn=n("iadO"),ei=n("FKr1"),ti=n("QibW"),ni=n("d3UM"),ii=n("5+WD"),ai=n("f0Cb"),oi=n("vxfF"),ri=n("Xa2L");let si=(()=>{class e{}return e.\u0275mod=g["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=g["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,Un,Qn.ApplicantTrackingSystemModule,Xn.a,f.h,_.b,qn.c,y.p,y.E,S.b,Kn.b,Jn.h,ei.n,ht.b,ti.c,ni.d,ii.g,ai.b,ut.b,oi.g,b.c,ri.b]]}),e})()}}]);