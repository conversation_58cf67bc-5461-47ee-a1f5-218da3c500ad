(window.webpackJsonp=window.webpackJsonp||[]).push([[908,907],{Vdaw:function(e,t,n){"use strict";n.d(t,"a",(function(){return v})),n.d(t,"b",(function(){return h}));var i=n("fXoL"),o=n("rDax"),r=n("+rOU"),l=n("3Pt+"),a=n("ofXK"),s=n("NFeN"),c=n("bTqV"),d=n("qFsG"),p=n("kmnG");const u=["field"];function f(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"span",4),i["\u0275\u0275text"](1," change_history "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](2,"div",5),i["\u0275\u0275elementStart"](3,"form",6),i["\u0275\u0275elementStart"](4,"div",7),i["\u0275\u0275elementStart"](5,"div",8),i["\u0275\u0275elementStart"](6,"div",9),i["\u0275\u0275elementStart"](7,"mat-form-field",10),i["\u0275\u0275elementStart"](8,"mat-label"),i["\u0275\u0275text"](9),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](10,"input",11),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](11,"div",12),i["\u0275\u0275elementStart"](12,"mat-form-field",10),i["\u0275\u0275elementStart"](13,"mat-label"),i["\u0275\u0275text"](14),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](15,"input",13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](16,"button",14),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"]().updateVal()})),i["\u0275\u0275text"](17,"Update"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("formGroup",e.inputDetail),i["\u0275\u0275advance"](6),i["\u0275\u0275textInterpolate"](e.label1),i["\u0275\u0275advance"](5),i["\u0275\u0275textInterpolate"](e.label2)}}let v=(()=>{class e{constructor(e,t,n){this.viewContainerRef=e,this.overlay=t,this.fb=n,this.viewPort=70,this.uptValChange=new i.EventEmitter,this.inputDetail=this.fb.group({input1:["",l.H.required],input2:["",l.H.required]}),this.updateVal=()=>{var e,t;null===(e=this.uptValChange)||void 0===e||e.emit(this.inputDetail.value),null===(t=this.overlayRef)||void 0===t||t.dispose()}}ngOnInit(){var e,t;null===(e=this.inputDetail.get("input1"))||void 0===e||e.patchValue(this.inputText1),null===(t=this.inputDetail.get("input2"))||void 0===t||t.patchValue(this.inputText2)}openOverlay(){var e,t,n;if(null==(null===(e=this.overlayRef)||void 0===e?void 0:e.hasAttached())||0==(null===(t=this.overlayRef)||void 0===t?void 0:t.hasAttached())){const e=this.overlay.position().flexibleConnectedTo(this.fieldRef.nativeElement).withFlexibleDimensions(!0).withPush(!0).withViewportMargin(this.viewPort).withGrowAfterOpen(!0).withPositions([{originX:"end",originY:"bottom",overlayX:"end",overlayY:"top"}]),t=this.overlay.scrollStrategies.close();this.overlayRef=this.overlay.create({positionStrategy:e,scrollStrategy:t,hasBackdrop:!0});const n=new r.h(this.templateRef,this.viewContainerRef);this.overlayRef.attach(n),this.overlayRef.backdropClick().subscribe(()=>{var e;null===(e=this.overlayRef)||void 0===e||e.dispose()})}console.log(null===(n=this.overlayRef)||void 0===n?void 0:n.hasAttached())}closeOverlay(){var e;null===(e=this.overlayRef)||void 0===e||e.dispose()}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](i.ViewContainerRef),i["\u0275\u0275directiveInject"](o.e),i["\u0275\u0275directiveInject"](l.i))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["kebs-multi-text-inline-edit-d1"]],viewQuery:function(e,t){if(1&e&&(i["\u0275\u0275staticViewQuery"](u,!0),i["\u0275\u0275staticViewQuery"](i.TemplateRef,!0)),2&e){let e;i["\u0275\u0275queryRefresh"](e=i["\u0275\u0275loadQuery"]())&&(t.fieldRef=e.first),i["\u0275\u0275queryRefresh"](e=i["\u0275\u0275loadQuery"]())&&(t.templateRef=e.first)}},inputs:{viewPort:"viewPort",text1:"text1",text2:"text2",inputText1:"inputText1",inputText2:"inputText2",label1:"label1",label2:"label2"},outputs:{uptValChange:"uptValChange"},decls:8,vars:5,consts:[["cdkOverlayOrigin","",2,"cursor","pointer",3,"click"],["trigger","cdkOverlayOrigin","field",""],[1,"pl-2"],["cdkConnectedOverlay","",3,"cdkConnectedOverlayOrigin"],[1,"material-icons","triangle"],[1,"pop-up"],[3,"formGroup"],[1,"row"],[1,"col-12","d-flex","p-0"],[1,"col-6","pl-1"],["appearance","outline",1,"custom-field-class"],["matInput","","formControlName","input1","type","number"],[1,"col-6","pl-2"],["matInput","","formControlName","input2","type","number"],["mat-flat-button","",1,"update-btn","pt-1",3,"click"]],template:function(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"span",0,1),i["\u0275\u0275listener"]("click",(function(){return t.openOverlay()})),i["\u0275\u0275elementStart"](3,"span"),i["\u0275\u0275text"](4),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"span",2),i["\u0275\u0275text"](6),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](7,f,18,3,"ng-template",3)),2&e){const e=i["\u0275\u0275reference"](1);i["\u0275\u0275advance"](4),i["\u0275\u0275textInterpolate2"](" ",t.text1," ",t.inputText1," "),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate2"](" ",t.text2," ",t.inputText2," "),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("cdkConnectedOverlayOrigin",e)}},directives:[o.b,o.a,l.J,l.w,l.n,p.c,p.g,d.b,l.e,l.A,l.v,l.l,c.a],styles:[".cdk-overlay-backdrop.cdk-overlay-backdrop-showing{opacity:0}.pop-up[_ngcontent-%COMP%]{width:17rem;border:1px solid #ccc;border-radius:5px;background:#fff;padding:10px;margin:0;box-shadow:0 4px 8px 0 rgba(0,0,0,.2),0 6px 20px 0 rgba(0,0,0,.19)}  .cdk-overlay-pane{margin-top:7px}.triangle[_ngcontent-%COMP%]{color:#bbb1b1;position:relative;top:-10px;left:136px;z-index:-1;font-size:15px}.custom-field-class[_ngcontent-%COMP%]{width:7rem}.update-btn[_ngcontent-%COMP%]{width:100%;background-color:#cf0001;color:#fff}"]}),e})(),h=(()=>{class e{}return e.\u0275mod=i["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=i["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[a.CommonModule,c.b,l.E,s.b,l.p,d.c,o.h,p.e]]}),e})()},aQly:function(e,t,n){"use strict";n.d(t,"a",(function(){return v})),n.d(t,"b",(function(){return h}));var i=n("fXoL"),o=n("3Pt+"),r=n("rDax"),l=n("+rOU"),a=n("ofXK"),s=n("NFeN");const c=["field"],d=function(e){return{"background-color":e}};function p(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"li",7),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const n=t.$implicit,o=i["\u0275\u0275nextContext"](2);return o.optionSelected(n),o.closeOverlay()})),i["\u0275\u0275element"](1,"span",2),i["\u0275\u0275text"](2),i["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngStyle",i["\u0275\u0275pureFunction1"](2,d,e.color)),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"]("\xa0\xa0 ",e.name," ")}}function u(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"span",4),i["\u0275\u0275text"](1," change_history "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](2,"ul",5),i["\u0275\u0275template"](3,p,3,4,"li",6),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngForOf",e.list)}}const f={provide:o.t,useExisting:Object(i.forwardRef)(()=>v),multi:!0};let v=(()=>{class e{constructor(e,t){this.viewContainerRef=e,this.overlay=t,this.selectedStatus=new o.j,this.isOpen=!1}ngOnInit(){}optionSelected(e){this.selectedStatus.patchValue(e),this.isOpen=!1,this.onChange&&this.onChange(e)}registerOnChange(e){this.onChange=e}registerOnTouched(e){this.onTouched=e}writeValue(e){this.selectedStatus.patchValue(e)}openOverlay(){var e,t,n;if(null==(null===(e=this.overlayRef)||void 0===e?void 0:e.hasAttached())||0==(null===(t=this.overlayRef)||void 0===t?void 0:t.hasAttached())){const e=this.overlay.position().flexibleConnectedTo(this.fieldRef.nativeElement).withFlexibleDimensions(!0).withPush(!0).withViewportMargin(50).withGrowAfterOpen(!0).withPositions([{originX:"end",originY:"bottom",overlayX:"end",overlayY:"top"}]),t=this.overlay.scrollStrategies.close();this.overlayRef=this.overlay.create({positionStrategy:e,scrollStrategy:t,hasBackdrop:!0});const n=new l.h(this.templateRef,this.viewContainerRef);this.overlayRef.attach(n),this.overlayRef.backdropClick().subscribe(()=>{var e;null===(e=this.overlayRef)||void 0===e||e.dispose()})}console.log(null===(n=this.overlayRef)||void 0===n?void 0:n.hasAttached())}closeOverlay(){var e;null===(e=this.overlayRef)||void 0===e||e.dispose()}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](i.ViewContainerRef),i["\u0275\u0275directiveInject"](r.e))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["d1-Kebs-statusInline-tooltip"]],viewQuery:function(e,t){if(1&e&&(i["\u0275\u0275staticViewQuery"](c,!0),i["\u0275\u0275staticViewQuery"](i.TemplateRef,!0)),2&e){let e;i["\u0275\u0275queryRefresh"](e=i["\u0275\u0275loadQuery"]())&&(t.fieldRef=e.first),i["\u0275\u0275queryRefresh"](e=i["\u0275\u0275loadQuery"]())&&(t.templateRef=e.first)}},inputs:{list:"list"},features:[i["\u0275\u0275ProvidersFeature"]([f])],decls:6,vars:5,consts:[["cdkOverlayOrigin","",2,"cursor","pointer",3,"click"],["trigger","cdkOverlayOrigin","field",""],[1,"dot",3,"ngStyle"],["cdkConnectedOverlay","",3,"cdkConnectedOverlayOrigin"],[1,"material-icons","triangle"],[1,"example-list"],[3,"click",4,"ngFor","ngForOf"],[3,"click"]],template:function(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"span",0,1),i["\u0275\u0275listener"]("click",(function(){return t.openOverlay()})),i["\u0275\u0275element"](3,"span",2),i["\u0275\u0275text"](4),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](5,u,4,1,"ng-template",3)),2&e){const e=i["\u0275\u0275reference"](1);i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngStyle",i["\u0275\u0275pureFunction1"](3,d,t.selectedStatus.value.color)),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"]("\xa0\xa0 ",t.selectedStatus.value.name,""),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("cdkConnectedOverlayOrigin",e)}},directives:[r.b,a.NgStyle,r.a,a.NgForOf],styles:[".example-list[_ngcontent-%COMP%]{width:145px;border:1px solid #ccc;border-radius:5px;background:#fff;padding:10px;margin:0;box-shadow:0 4px 8px 0 rgba(0,0,0,.2),0 6px 20px 0 rgba(0,0,0,.19)}  .cdk-overlay-pane{margin-top:7px}.example-list[_ngcontent-%COMP%] > li[_ngcontent-%COMP%]{list-style-type:none;padding:8px 0;cursor:pointer}.example-list[_ngcontent-%COMP%] > li[_ngcontent-%COMP%]:last-child{border-bottom:none}.dot[_ngcontent-%COMP%]{height:15px;width:15px;background-color:#bbb;border-radius:50%;display:inline-block}.triangle[_ngcontent-%COMP%]{color:#bbb1b1;position:relative;top:-11px;left:77px;z-index:-1;font-size:15px} .cdk-overlay-backdrop.cdk-overlay-backdrop-showing{opacity:0}"]}),e})(),h=(()=>{class e{}return e.\u0275mod=i["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=i["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[a.CommonModule,r.h,o.E,o.p,s.b]]}),e})()}}]);