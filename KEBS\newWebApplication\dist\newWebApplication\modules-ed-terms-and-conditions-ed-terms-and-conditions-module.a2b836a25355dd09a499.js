(window.webpackJsonp=window.webpackJsonp||[]).push([[817],{kLEv:function(e,t,n){"use strict";n.r(t),n.d(t,"EdTermsAndConditionsModule",(function(){return F}));var i=n("ofXK"),o=n("tyNb"),s=n("mrSG"),r=n("33Jv"),a=n("xG9w"),d=n("fXoL"),l=n("jAlA"),c=n("1A3m"),p=n("0IaG"),m=n("XXEo"),g=n("Wp6s"),f=n("Xa2L"),h=n("Qu3c"),x=n("bTqV"),C=n("NFeN"),u=n("Rubt");const b=["scrollFrame"];function y(e,t){1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275elementStart"](1,"div",5),d["\u0275\u0275element"](2,"mat-spinner",6),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementContainerEnd"]())}function _(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",7),d["\u0275\u0275elementStart"](1,"div",8),d["\u0275\u0275elementStart"](2,"span",9),d["\u0275\u0275text"](3,"Policies / Guidelines"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function v(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",23),d["\u0275\u0275listener"]("click",(function(){d["\u0275\u0275restoreView"](e);const n=t.$implicit;return d["\u0275\u0275nextContext"](2).handleSectionSelect(n.tab_name)})),d["\u0275\u0275elementStart"](1,"div",24),d["\u0275\u0275elementStart"](2,"span",25),d["\u0275\u0275elementStart"](3,"span",26),d["\u0275\u0275text"](4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](5,"span",27),d["\u0275\u0275text"](6),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=t.index;d["\u0275\u0275property"]("ngClass",e.isSelected?"selected row":"not-selected row"),d["\u0275\u0275advance"](4),d["\u0275\u0275textInterpolate"](n+1),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate"](e.tab_name)}}const w=function(e){return{"pointer-events":e}};function O(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275elementStart"](1,"div",28),d["\u0275\u0275elementStart"](2,"div",29),d["\u0275\u0275elementStart"](3,"button",30),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](2).agree()})),d["\u0275\u0275text"](4," I Agree > "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngClass",e.isFromMyProfile?"create-employee-btn":"disable-employee-btn")("disabled",e.loaderObject.isFormSubmitLoading||!e.isFromMyProfile)("ngStyle",d["\u0275\u0275pureFunction1"](3,w,e.isFromMyProfile?"visible":"none"))}}function P(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"mat-icon",34),d["\u0275\u0275text"](1,"done_all"),d["\u0275\u0275elementEnd"]())}function M(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"button",32),d["\u0275\u0275text"](1," Agreed "),d["\u0275\u0275template"](2,P,2,0,"mat-icon",33),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](3);d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",e.isAgree)}}function S(e,t){if(1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275elementStart"](1,"div",28),d["\u0275\u0275elementStart"](2,"div",29),d["\u0275\u0275template"](3,M,3,1,"button",31),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementContainerEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngIf",e.isAgree)}}function E(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",10),d["\u0275\u0275elementStart"](1,"div",11),d["\u0275\u0275elementContainerStart"](2),d["\u0275\u0275template"](3,v,7,3,"div",12),d["\u0275\u0275elementContainerEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](4,"div",13),d["\u0275\u0275elementContainerStart"](5,null,14),d["\u0275\u0275elementStart"](7,"div",15),d["\u0275\u0275elementStart"](8,"div",16),d["\u0275\u0275elementStart"](9,"span",17),d["\u0275\u0275text"](10),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](11,"div",18),d["\u0275\u0275elementStart"](12,"span",19),d["\u0275\u0275text"](13),d["\u0275\u0275pipe"](14,"date"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](15,"div",20),d["\u0275\u0275elementStart"](16,"div",21),d["\u0275\u0275element"](17,"span",22),d["\u0275\u0275pipe"](18,"safeHtml"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementContainerEnd"](),d["\u0275\u0275template"](19,O,5,5,"ng-container",2),d["\u0275\u0275template"](20,S,4,1,"ng-container",2),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngForOf",e.sectionList),d["\u0275\u0275advance"](7),d["\u0275\u0275textInterpolate"](e.showContent.tab_name),d["\u0275\u0275advance"](3),d["\u0275\u0275textInterpolate1"]("Updated On ",d["\u0275\u0275pipeBind2"](14,6,e.showContent.updated_on,"longDate"),""),d["\u0275\u0275advance"](4),d["\u0275\u0275property"]("innerHTML",d["\u0275\u0275pipeBind1"](18,9,e.showContent.tab_content),d["\u0275\u0275sanitizeHtml"]),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",!e.isActionComplete),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isActionComplete)}}const I=[{path:"",component:(()=>{class e{constructor(e,t,n,i,o,s){this._router=e,this._edService=t,this._route=n,this._toaster=i,this.dialog=o,this._loginService=s,this.loaderObject={isComponentLoading:!1,isFormSubmitLoading:!1},this.isAgree=!1,this.isActionComplete=!1,this.isFromMyProfile=!1,this.sectionList=[],this.showContent={},this.subs=new r.a}ngOnInit(){return Object(s.c)(this,void 0,void 0,(function*(){this.loaderObject.isComponentLoading=!0,this.getEmployeeId(),this.isFromMyProfile=this._loginService.getProfile().profile.aid==this.associateId,this.sectionList=yield this.getSectionList(),this.isAgree=yield this.CheckTermsAndConditionStatus(),this.isActionComplete=!!this.isAgree,this.loaderObject.isComponentLoading=!1,this.showContent=this.sectionList[0],this.updateIsSelectedField(),this.sectionList[0].isSelected=!0}))}updateIsSelectedField(){this.sectionList.forEach((e,t)=>{e.isSelected=!1})}getSectionList(){return new Promise((e,t)=>{this.subs.sink=this._edService.getTermsAndConditionDetails().subscribe(t=>{e(t.data)},e=>{this._toaster.showError("Error","Failed to retrieve employee !",2e3),console.log(e),t(e)})})}handleSectionSelect(e){this.showContent=a.where(this.sectionList,{tab_name:e})[0],this.sectionList.forEach((t,n)=>{t.isSelected=t.tab_name==e})}getEmployeeId(){this.subs.sink=this._edService.getEmployeeId().subscribe(e=>{this.associateId=parseInt(e)})}decline(){this.isActionComplete=!0,this.isAgree=!1,this.loaderObject.isFormSubmitLoading=!0}agree(){this.loaderObject.isFormSubmitLoading=!0;let e={associate_id:this.associateId,is_agreed:!0};return new Promise((t,n)=>{this.subs.sink=this._edService.agreeTermsAndCondition(e).subscribe(e=>{e.err||(this.isActionComplete=!0,this.isAgree=!0,this._toaster.showSuccess("Success","Terms and Condition Updated successfully!",2e3),this.loaderObject.isFormSubmitLoading=!1,this.routeToProfile(),t(e.data))},e=>{this.loaderObject.isFormSubmitLoading=!1,this._toaster.showError("Error","Failed to Update Terms and Condition!",2e3),console.log(e),n(e)})})}CheckTermsAndConditionStatus(){let e={associate_id:this.associateId};return new Promise((t,n)=>{this.subs.sink=this._edService.checkTermsAndConditionStatus(e).subscribe(e=>{t(e.has_agreed)},e=>{this._toaster.showError("Error","Failed to retrieve terms and condition status !",2e3),console.log(e),n(e)})})}routeToProfile(){this._edService.setIsTermsAndCoditionAgreedObservable({is_agreed:!0}),this._router.navigateByUrl("/main/employee-central/employeeCentralDetail/"+this.associateId+"/overview/profile")}ngOnDestroy(){this.subs.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](o.g),d["\u0275\u0275directiveInject"](l.a),d["\u0275\u0275directiveInject"](o.a),d["\u0275\u0275directiveInject"](c.a),d["\u0275\u0275directiveInject"](p.b),d["\u0275\u0275directiveInject"](m.a))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-ed-terms-and-conditions-landing-page"]],viewQuery:function(e,t){if(1&e&&d["\u0275\u0275viewQuery"](b,!0),2&e){let e;d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.scrollFrame=e.first)}},decls:5,vars:3,consts:[[1,"container-fluid","ed-terms-and-conditions-styles","mt-3"],[1,"p-4","mat-card","slide-from-down",2,"min-height","75vh"],[4,"ngIf"],["class","row slide-from-down",4,"ngIf"],["class","row pt-3 slide-from-down",4,"ngIf"],[1,"d-flex","justify-content-center","mt-3"],["matTooltip","Please wait...","diameter","30"],[1,"row","slide-from-down"],[1,"col-12"],[1,"section-heading"],[1,"row","pt-3","slide-from-down"],[1,"col-3","py-3","px-3","side-nav"],[3,"ngClass","click",4,"ngFor","ngForOf"],[1,"col-9","pt-2","pl-4"],["scrollFrame",""],[1,"col-12","p-0","detail-text"],[1,"row"],[1,"title"],[1,"row","pt-2"],[1,"updated-date"],[1,"row","pt-3"],[1,"col-12","p-0","pt-1","content-text"],[3,"innerHTML"],[3,"ngClass","click"],[1,"d-flex","pb-3","flex-row","slide-from-down",2,"width","100%"],[1,"circle"],[1,"circle-text"],[1,"pl-2","tab-text",2,"padding-top","1px"],[1,"row","pt-3",2,"float","right"],[1,"d-flex","flex-row"],["type","button","mat-raised-button","",1,"ml-3",3,"ngClass","disabled","ngStyle","click"],["style","pointer-events: none","class","approve-previous",4,"ngIf"],[1,"approve-previous",2,"pointer-events","none"],["style","font-size: 15px; line-height: 32px; padding-left: 4px",4,"ngIf"],[2,"font-size","15px","line-height","32px","padding-left","4px"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",0),d["\u0275\u0275elementStart"](1,"mat-card",1),d["\u0275\u0275template"](2,y,3,0,"ng-container",2),d["\u0275\u0275template"](3,_,4,0,"div",3),d["\u0275\u0275template"](4,E,21,11,"div",4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e&&(d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",t.loaderObject.isComponentLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!t.loaderObject.isComponentLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!t.loaderObject.isComponentLoading))},directives:[g.a,i.NgIf,f.c,h.a,i.NgForOf,i.NgClass,x.a,i.NgStyle,C.a],pipes:[i.DatePipe,u.a],styles:[".ed-terms-and-conditions-styles[_ngcontent-%COMP%]{overflow-x:auto;height:72vh;scrollbar-width:none}.ed-terms-and-conditions-styles[_ngcontent-%COMP%]::-webkit-scrollbar{display:none}.ed-terms-and-conditions-styles[_ngcontent-%COMP%]   .overflow[_ngcontent-%COMP%]{height:28rem;overflow:scroll;overflow-x:hidden}.ed-terms-and-conditions-styles[_ngcontent-%COMP%]   .section-heading[_ngcontent-%COMP%]{color:#ee4961;font-size:15px;font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;text-transform:capitalize;max-width:96%}.ed-terms-and-conditions-styles[_ngcontent-%COMP%]   .mat-card[_ngcontent-%COMP%]{width:100%;border:1px solid #fff;background-color:#fff;border-radius:5px}.ed-terms-and-conditions-styles[_ngcontent-%COMP%]   .side-nav[_ngcontent-%COMP%]{background:#f7f9fb;overflow-y:auto;height:60vh;scrollbar-width:none}.ed-terms-and-conditions-styles[_ngcontent-%COMP%]   .side-nav[_ngcontent-%COMP%]::-webkit-scrollbar{display:none}.ed-terms-and-conditions-styles[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-weight:700;font-size:22px;line-height:24px;color:#45546e;display:flex;align-items:center;text-transform:capitalize}.ed-terms-and-conditions-styles[_ngcontent-%COMP%]   .updated-date[_ngcontent-%COMP%]{font-weight:400;font-size:14px;line-height:16px;display:flex;align-items:center;text-transform:capitalize;color:#8b95a5}.ed-terms-and-conditions-styles[_ngcontent-%COMP%]   .content-title[_ngcontent-%COMP%]{font-weight:700;font-size:14px;line-height:16px;display:flex;align-items:center;text-transform:capitalize;color:#45546e}.ed-terms-and-conditions-styles[_ngcontent-%COMP%]   .content-text[_ngcontent-%COMP%]{font-weight:400;font-size:14px;line-height:24px;color:#8b95a5}.ed-terms-and-conditions-styles[_ngcontent-%COMP%]   .not-selected[_ngcontent-%COMP%]   .tab-text[_ngcontent-%COMP%]{font-weight:500;cursor:pointer;font-size:14px;line-height:16px;display:flex;align-items:center;text-transform:capitalize;color:#b9c0ca;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:85%}.ed-terms-and-conditions-styles[_ngcontent-%COMP%]   .not-selected[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{display:flex;flex-direction:column;justify-content:space-between;align-items:center;padding:2px 4px;gap:8px;width:24px;height:24px;background:#dadce2;border-radius:12px}.ed-terms-and-conditions-styles[_ngcontent-%COMP%]   .not-selected[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]   .circle-text[_ngcontent-%COMP%]{font-weight:500;font-size:13px;line-height:20px;display:flex;align-items:center;text-align:center;text-transform:capitalize;color:#8b95a5}.ed-terms-and-conditions-styles[_ngcontent-%COMP%]   .selected[_ngcontent-%COMP%]   .tab-text[_ngcontent-%COMP%]{font-weight:500;cursor:pointer;font-size:14px;line-height:16px;display:flex;align-items:center;text-transform:capitalize;color:#45546e;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:85%}.ed-terms-and-conditions-styles[_ngcontent-%COMP%]   .selected[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{display:flex;flex-direction:column;justify-content:space-between;align-items:center;padding:2px 4px;gap:8px;width:24px;height:24px;background:#6e7b8f;border-radius:12px}.ed-terms-and-conditions-styles[_ngcontent-%COMP%]   .selected[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]   .circle-text[_ngcontent-%COMP%]{font-weight:500;font-size:13px;line-height:20px;display:flex;align-items:center;text-align:center;text-transform:capitalize;color:#fff}.ed-terms-and-conditions-styles[_ngcontent-%COMP%]   .not-selected[_ngcontent-%COMP%]:hover{transition:.2s}.ed-terms-and-conditions-styles[_ngcontent-%COMP%]   .not-selected[_ngcontent-%COMP%]:hover   .tab-text[_ngcontent-%COMP%]{font-weight:500;cursor:pointer;font-size:14px;line-height:16px;display:flex;align-items:center;text-transform:capitalize;color:#45546e}.ed-terms-and-conditions-styles[_ngcontent-%COMP%]   .not-selected[_ngcontent-%COMP%]:hover   .circle[_ngcontent-%COMP%]{display:flex;flex-direction:column;justify-content:space-between;align-items:center;padding:2px 4px;gap:8px;width:24px;height:24px;background:#6e7b8f;border-radius:12px}.ed-terms-and-conditions-styles[_ngcontent-%COMP%]   .not-selected[_ngcontent-%COMP%]:hover   .circle[_ngcontent-%COMP%]   .circle-text[_ngcontent-%COMP%]{font-weight:500;font-size:13px;line-height:20px;display:flex;align-items:center;text-align:center;text-transform:capitalize;color:#fff}.ed-terms-and-conditions-styles[_ngcontent-%COMP%]   .detail-text[_ngcontent-%COMP%]{overflow-y:auto;height:50vh;scrollbar-width:none}.ed-terms-and-conditions-styles[_ngcontent-%COMP%]   .detail-text[_ngcontent-%COMP%]::-webkit-scrollbar{display:none}.ed-terms-and-conditions-styles[_ngcontent-%COMP%]   .create-employee-btn[_ngcontent-%COMP%]{background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%)!important;color:#fff;height:42px}.ed-terms-and-conditions-styles[_ngcontent-%COMP%]   .disable-employee-btn[_ngcontent-%COMP%]{background:#dadce2;color:#45546e;height:42px}.ed-terms-and-conditions-styles[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{color:#45546e;height:42px}.ed-terms-and-conditions-styles[_ngcontent-%COMP%]   .reject-previous[_ngcontent-%COMP%]{letter-spacing:-.02em;border:1px solid #ff3a46;color:#ff3a46}.ed-terms-and-conditions-styles[_ngcontent-%COMP%]   .approve-previous[_ngcontent-%COMP%], .ed-terms-and-conditions-styles[_ngcontent-%COMP%]   .reject-previous[_ngcontent-%COMP%]{text-transform:capitalize;background:#fff;font-weight:700;height:40px;display:flex;line-height:33px;font-size:15px;padding:2px 10px;justify-content:center}.ed-terms-and-conditions-styles[_ngcontent-%COMP%]   .approve-previous[_ngcontent-%COMP%]{border:1px solid #52c41a;border-radius:4px;color:#52c41a}.ed-terms-and-conditions-styles[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}.ed-terms-and-conditions-styles[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}"]}),e})()}];let j=(()=>{class e{}return e.\u0275mod=d["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=d["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[o.k.forChild(I)],o.k]}),e})();var k=n("jaxi"),z=n("STbY"),L=n("MutI"),A=n("jW8c");let F=(()=>{class e{}return e.\u0275mod=d["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=d["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,j,C.b,x.b,h.b,k.c,z.e,L.d,p.g,g.d,f.b,A.a]]}),e})()}}]);