(window.webpackJsonp=window.webpackJsonp||[]).push([[744],{"2glj":function(e,t,i){"use strict";i.r(t),i.d(t,"CallLogFormComponent",(function(){return U}));var n=i("mrSG"),o=i("fXoL"),a=i("3Pt+"),r=i("xG9w"),l=i("ofXK"),s=i("jtHE"),c=i("XNiG"),d=i("NJ67"),p=i("1G5W"),m=i("kmnG"),h=i("d3UM"),u=i("FKr1"),g=i("WJ5W");const f=["singleSelect"];function v(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"mat-option",6),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](e);const i=t.$implicit;return o["\u0275\u0275nextContext"]().emitChanges(i)})),o["\u0275\u0275text"](1),o["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;o["\u0275\u0275property"]("value",e.id),o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let C=(()=>{class e extends d.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new a.j,this.fieldFilterCtrl=new a.j,this.list=[],this.required=!1,this.valueChange=new o.EventEmitter,this.disabled=!1,this.filteredList=new s.a,this.change=new o.EventEmitter,this._onDestroy=new c.b,this.emitChanges=e=>{this.change.emit(e)}}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(p.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(p.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){null!=this.list&&this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](o.Renderer2))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search"]],viewQuery:function(e,t){if(1&e&&o["\u0275\u0275viewQuery"](f,!0),2&e){let e;o["\u0275\u0275queryRefresh"](e=o["\u0275\u0275loadQuery"]())&&(t.singleSelect=e.first)}},inputs:{list:"list",placeholder:"placeholder",required:"required",disabled:"disabled"},outputs:{valueChange:"valueChange",change:"change"},features:[o["\u0275\u0275ProvidersFeature"]([{provide:a.t,useExisting:Object(o.forwardRef)(()=>e),multi:!0}]),o["\u0275\u0275InheritDefinitionFeature"],o["\u0275\u0275NgOnChangesFeature"]],decls:11,vars:11,consts:[["appearance","outline"],[3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value"],[3,"value","click",4,"ngFor","ngForOf"],[3,"value","click"]],template:function(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"mat-form-field",0),o["\u0275\u0275elementStart"](1,"mat-label"),o["\u0275\u0275text"](2),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](3,"mat-select",1,2),o["\u0275\u0275elementStart"](5,"mat-option"),o["\u0275\u0275element"](6,"ngx-mat-select-search",3),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](7,"mat-option",4),o["\u0275\u0275text"](8,"None"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](9,v,2,2,"mat-option",5),o["\u0275\u0275pipe"](10,"async"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e&&(o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate"](t.placeholder),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("value",null),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngForOf",o["\u0275\u0275pipeBind1"](10,9,t.filteredList)))},directives:[m.c,m.g,h.c,a.v,a.k,a.F,u.p,g.a,l.NgForOf],pipes:[l.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}"]}),e})();var b=i("Kj3r"),y=i("F97M"),S=i("XVR1"),w=i("qFsG"),x=i("/1cH"),E=i("NFeN");function _(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"mat-label"),o["\u0275\u0275text"](1),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate"](e.label)}}function L(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"mat-option",7),o["\u0275\u0275elementStart"](1,"small"),o["\u0275\u0275text"](2),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,i=o["\u0275\u0275nextContext"]();o["\u0275\u0275property"]("hidden",!i.isAutocomplete)("value",e),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate2"]("",e.displayName," | ",e.mail,"")}}let F=(()=>{class e extends d.a{constructor(e,t){super(),this.graphApi=e,this._AppShareService=t,this.userSearchSubject=new c.b,this.isAutocomplete=!1,this.Formwidth="",this.showUserList=new o.EventEmitter,this.selectedUser=new o.EventEmitter,this.label="",this.blur=new o.EventEmitter,this.required=!1,this.fieldCtrl=new a.j,this.disabled=!1,this.isGraphApi=0,this._onDestroy=new c.b}ngOnInit(){this.userSearchSubject.pipe(Object(b.a)(600)).subscribe(e=>Object(n.c)(this,void 0,void 0,(function*(){if(console.log(e),this.isGraphApi)this.graphApi.getUserSuggestions(e).then(t=>Object(n.c)(this,void 0,void 0,(function*(){if(t&&t.length>0)this.showUserList.emit(t);else{let t=yield this.getUserSuggestionsFromDB(e);this.showUserList.emit(t)}})),t=>Object(n.c)(this,void 0,void 0,(function*(){console.log(t);let i=yield this.getUserSuggestionsFromDB(e);this.showUserList.emit(i)})));else{let t=yield this.getUserSuggestionsFromDB(e);this.showUserList.emit(t)}}))),this.fieldCtrl.valueChanges.pipe(Object(p.a)(this._onDestroy)).subscribe(e=>{console.log(e),e?(this.value=e.id,this.onChange(e.id)):(this.value="",this.onChange(""))})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}ngAfterViewInit(){}searchUser(e){if(!e)return this.graphApi.userSuggestions=[];this.userSearchSubject.next(e)}resetSuggestion(){this.graphApi.userSuggestions=[]}selectedOption(e){this.selectedUser.emit(e.option.value),this.value=e.option.value,this.blur.emit()}displayUserName(e){return e?e.displayName:""}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){return Object(n.c)(this,void 0,void 0,(function*(){if(console.log("inisde writevalue"),console.log(e),e)if(this.isGraphApi)this.graphApi.getUserProfile(e).then(t=>Object(n.c)(this,void 0,void 0,(function*(){if(t)this.fieldCtrl.setValue(t);else{let t=yield this.getUserProfileFromDB(e);this.fieldCtrl.setValue(t)}})));else{let t=yield this.getUserProfileFromDB(e);console.log(t),this.fieldCtrl.setValue(t)}else this.fieldCtrl.setValue("")}))}getUserSuggestionsFromDB(e){return Object(n.c)(this,void 0,void 0,(function*(){let t=yield this._AppShareService.getUserSuggestionsFromDB(e);return this.graphApi.userSuggestions=t.value,t.value}))}getUserProfileFromDB(e){return Object(n.c)(this,void 0,void 0,(function*(){let t=yield this._AppShareService.getUserProfileFromDB(e);return console.log(t),t&&t.length>0?t[0]:""}))}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](y.a),o["\u0275\u0275directiveInject"](S.a))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-search-user"]],inputs:{isAutocomplete:"isAutocomplete",Formwidth:"Formwidth",label:"label",required:"required",disabled:"disabled",isGraphApi:"isGraphApi"},outputs:{showUserList:"showUserList",selectedUser:"selectedUser",blur:"blur"},features:[o["\u0275\u0275ProvidersFeature"]([{provide:a.t,useExisting:Object(o.forwardRef)(()=>e),multi:!0}]),o["\u0275\u0275InheritDefinitionFeature"]],decls:9,vars:9,consts:[["appearance","outline","width","Formwidth"],[4,"ngIf"],["matInput","",3,"matAutocomplete","placeholder","required","formControl","disabled","keyup","focus"],["matSuffix","",2,"color","#66615B !important","font-size","21px !important"],[3,"hidden","displayWith","optionSelected"],["auto","matAutocomplete"],[3,"hidden","value",4,"ngFor","ngForOf"],[3,"hidden","value"]],template:function(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div"),o["\u0275\u0275elementStart"](1,"mat-form-field",0),o["\u0275\u0275template"](2,_,2,1,"mat-label",1),o["\u0275\u0275elementStart"](3,"input",2),o["\u0275\u0275listener"]("keyup",(function(e){return t.searchUser(e.target.value)}))("focus",(function(){return t.resetSuggestion()})),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](4,"mat-icon",3),o["\u0275\u0275text"](5,"person_pin"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](6,"mat-autocomplete",4,5),o["\u0275\u0275listener"]("optionSelected",(function(e){return t.selectedOption(e)})),o["\u0275\u0275template"](8,L,3,4,"mat-option",6),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275reference"](7);o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngIf",t.label),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("matAutocomplete",e)("placeholder",t.label)("required",t.required)("formControl",t.fieldCtrl)("disabled",t.disabled),o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("hidden",!t.isAutocomplete)("displayWith",t.displayUserName),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngForOf",t.graphApi.userSuggestions)}},directives:[m.c,l.NgIf,w.b,x.d,a.e,a.F,a.v,a.k,E.a,m.i,x.b,l.NgForOf,m.g,u.p],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding-bottom:8px!important}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-underline{bottom:0}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-infix{padding:.65em 0!important;border-top:.54375em solid transparent!important;font-size:13px}.mat-form-field[_ngcontent-%COMP%]     .mat-select-arrow{margin:0 4px!important}"]}),e})();var O=i("dNgK"),I=i("bTqV"),P=(i("iadO"),i("0IaG")),M=i("8uEH"),D=i("pgif"),A=i("ihCf");function k(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"button",29),o["\u0275\u0275listener"]("click",(function(){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"]().createCallLog()})),o["\u0275\u0275elementStart"](1,"mat-icon"),o["\u0275\u0275text"](2," done_all"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}}function j(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"button",30),o["\u0275\u0275listener"]("click",(function(){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"]().editCallLog()})),o["\u0275\u0275elementStart"](1,"mat-icon"),o["\u0275\u0275text"](2," done_all"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}}let U=(()=>{class e{constructor(e,t,i,n,r,l,s){this.dialogRef=e,this.dialogData=t,this.dialog=i,this.fb=n,this.activityToMainService=r,this.snackBar=l,this.opportunityService=s,this.close=new o.EventEmitter,this.currentUser=this.opportunityService.currentUser.oid,this.callLogForm=this.fb.group({phase:[""],title:["",a.H.required],description:[""],contactType:["",a.H.required],contactedPerson:[""],response:[""],contactedBy:[""],notes:[""],dueDate:[""],remindMe:[""],applicationName:[""],applicationReferenceId:[""],applicationId:[""],governanceType:[""]}),this.getPhase=()=>(console.log("I've been called"),new Promise((e,t)=>{this.opportunityService.getActivityPhase(this.opportunityId).then(t=>{e(t)},e=>{throw console.error(e),e})})),this.editCallLog=()=>{this.callLogForm.valid?this.opportunityService.editCallLog(this.activityId,this.callLogForm.value).then(e=>{this.callLogForm.reset(),this.snackBar.open("Call Log edited Successfully!","Dismiss",{duration:2e3}),this.close.emit("update required"),this.dialogRef.close("Update Required"),this.activityToMainService.sendMsg("reload")},e=>{this.snackBar.open("Failed to edit call log.","Dismiss",{duration:2e3}),console.error(e)}):this.snackBar.open("Enter all Mandatory Fields!","Dismiss",{duration:2e3})},this.createCallLog=()=>{this.callLogForm.valid?this.opportunityService.createCallLog(this.callLogForm.value).subscribe(e=>{this.callLogForm.reset(),this.snackBar.open("Call Log Created Successfully!","Dismiss",{duration:2e3}),this.close.emit("close"),this.dialogRef.close("Update Required"),this.activityToMainService.sendMsg("reload"),console.log(e)},e=>{this.snackBar.open("Failed to create call log.","Dismiss",{duration:2e3}),console.error(e)}):this.snackBar.open("Enter all Mandatory Fields!","Dismiss",{duration:2e3})},this.closeClicked=()=>{this.close.emit(),this.dialogRef.close()}}ngOnChanges(){}updateFormWithCallLogDetails(){if(console.log(this.mode,this.activityId),"Edit"==this.mode){let e=this.data?this.data:null;console.log(e),e&&this.callLogForm.patchValue({phase:parseInt(e.phase),title:e.title,description:e.description,contactType:e.activity_sub_type_id,contactedPerson:e.contacted_person,response:e.response_type_id,contactedBy:e.contacted_by,governanceType:e.governance_activity_id,notes:e.notes?e.notes[0].notes:e.notes,remindMe:e.reminder})}else this.callLogForm.reset()}ngOnInit(){return Object(n.c)(this,void 0,void 0,(function*(){let e;console.log("Check",this.dialogData),this.mode=this.dialogData.mode,this.activityId=this.dialogData.activityId,this.data=this.dialogData.data,this.opportunityId=this.dialogData.opportunityId,"Edit"==this.mode&&this.updateFormWithCallLogDetails(),console.log("Check",this.dialogData),yield this.opportunityService.contactType().then(e=>{this.contactTypeList=e}),yield this.opportunityService.responseType().then(e=>{this.responseMasterList=e}),yield this.opportunityService.getLeadsGovernanceType(72).then(e=>{this.leadGovernanceTypes=e},e=>{console.log(e)}),"leads"==window.location.pathname.split("/")[2]?e=35:"opportunities"==window.location.pathname.split("/")[2]&&(e=36),yield this.opportunityService.getActivityPhase(this.opportunityId).then(e=>{this.phase=e},e=>{console.log(e)}),this.patchValue(e)}))}patchValue(e){let t=r.where(this.leadGovernanceTypes,{name:"Call Logs"}),i=r.where(this.contactTypeList,{name:"Outgoing Call"}),n=r.where(this.phase,{name:"Logs"});this.callLogForm.patchValue({applicationName:"opportunities",applicationReferenceId:this.opportunityId,applicationId:e,contactedBy:this.currentUser,governanceType:t.length>0?t[0].id:null,contactType:i.length>0?i[0].id:null,phase:n.length>0?n[0].id:null})}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](P.h),o["\u0275\u0275directiveInject"](P.a),o["\u0275\u0275directiveInject"](P.b),o["\u0275\u0275directiveInject"](a.i),o["\u0275\u0275directiveInject"](M.a),o["\u0275\u0275directiveInject"](O.a),o["\u0275\u0275directiveInject"](D.a))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-call-log-form"]],outputs:{close:"close"},features:[o["\u0275\u0275NgOnChangesFeature"]],decls:54,vars:8,consts:[[1,"container","createCallLogStyles"],[3,"formGroup"],[1,"row","border-bottom","solid"],[1,"col-11","pt-2","createAccount"],[1,"col-1","d-flex"],["mat-icon-button","",1,"ml-auto","close-button",3,"click"],[1,"close-Icon"],[1,"row","pt-2"],[1,"col-8","pl-0","pr-0"],[1,"row"],[1,"col-12"],["placeholder","Phase","formControlName","phase",1,"create-account-field","title",3,"list"],["appearance","outline",1,"create-account-field","title"],["matInput","","placeholder","title *","formControlName","title"],["matInput","","placeholder","Description","formControlName","description"],["required","true","placeholder","Contact Type","formControlName","contactType",1,"create-account-field","title",3,"list"],[1,"row","pt-1"],["matInput","","placeholder","Contacted person","formControlName","contactedPerson"],["placeholder","Response","formControlName","response",1,"create-account-field","title",3,"list"],["label","Contacted By","required","true","formControlName","contactedBy",1,"contact-person",3,"isAutocomplete"],["appearance","outline",1,"textArea",2,"width","28rem","font-size","14px !important"],["matInput","","cdkTextareaAutosize","","cdkAutosizeMinRows","4","cdkAutosizeMaxRows","","placeholder","Notes","formControlName","notes"],["autosize","cdkTextareaAutosize"],[1,"col-4"],[1,"row","pt-3"],[1,"col-9","pl-4","quotes"],[1,"col-3"],["mat-icon-button","","class","iconbtn ml-2 mt-0","matTooltip","Create Call Log","type","submit",3,"click",4,"ngIf"],["mat-icon-button","","class","iconbtn ml-2 mt-0","matTooltip","Edit Call Log","type","submit",3,"click",4,"ngIf"],["mat-icon-button","","matTooltip","Create Call Log","type","submit",1,"iconbtn","ml-2","mt-0",3,"click"],["mat-icon-button","","matTooltip","Edit Call Log","type","submit",1,"iconbtn","ml-2","mt-0",3,"click"]],template:function(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"div",0),o["\u0275\u0275elementStart"](1,"form",1),o["\u0275\u0275elementStart"](2,"div",2),o["\u0275\u0275elementStart"](3,"div",3),o["\u0275\u0275text"](4),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](5,"div",4),o["\u0275\u0275elementStart"](6,"button",5),o["\u0275\u0275listener"]("click",(function(){return t.closeClicked()})),o["\u0275\u0275elementStart"](7,"mat-icon",6),o["\u0275\u0275text"](8,"close"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](9,"div",7),o["\u0275\u0275elementStart"](10,"div",8),o["\u0275\u0275elementStart"](11,"div",9),o["\u0275\u0275elementStart"](12,"div",10),o["\u0275\u0275element"](13,"app-input-search",11),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](14,"div",9),o["\u0275\u0275elementStart"](15,"div",10),o["\u0275\u0275elementStart"](16,"mat-form-field",12),o["\u0275\u0275elementStart"](17,"mat-label"),o["\u0275\u0275text"](18,"Title *"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275element"](19,"input",13),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](20,"div",9),o["\u0275\u0275elementStart"](21,"div",10),o["\u0275\u0275elementStart"](22,"mat-form-field",12),o["\u0275\u0275elementStart"](23,"mat-label"),o["\u0275\u0275text"](24,"Description"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275element"](25,"input",14),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](26,"div",9),o["\u0275\u0275elementStart"](27,"div",10),o["\u0275\u0275element"](28,"app-input-search",15),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](29,"div",16),o["\u0275\u0275elementStart"](30,"div",10),o["\u0275\u0275elementStart"](31,"mat-form-field",12),o["\u0275\u0275elementStart"](32,"mat-label"),o["\u0275\u0275text"](33,"Contacted person "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275element"](34,"input",17),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](35,"div",9),o["\u0275\u0275elementStart"](36,"div",10),o["\u0275\u0275element"](37,"app-input-search",18),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](38,"div",16),o["\u0275\u0275elementStart"](39,"div",10),o["\u0275\u0275element"](40,"app-search-user",19),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](41,"div",16),o["\u0275\u0275elementStart"](42,"div",10),o["\u0275\u0275elementStart"](43,"mat-form-field",20),o["\u0275\u0275element"](44,"textarea",21,22),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275element"](46,"div",23),o["\u0275\u0275elementEnd"](),o["\u0275\u0275element"](47,"div",24),o["\u0275\u0275elementStart"](48,"div",24),o["\u0275\u0275elementStart"](49,"div",25),o["\u0275\u0275text"](50,' "In the Middle of difficulty, lies Opportunity" '),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](51,"div",26),o["\u0275\u0275template"](52,k,3,0,"button",27),o["\u0275\u0275template"](53,j,3,0,"button",28),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e&&(o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("formGroup",t.callLogForm),o["\u0275\u0275advance"](3),o["\u0275\u0275textInterpolate1"]("",t.mode," call log"),o["\u0275\u0275advance"](9),o["\u0275\u0275property"]("list",t.phase),o["\u0275\u0275advance"](15),o["\u0275\u0275property"]("list",t.contactTypeList),o["\u0275\u0275advance"](9),o["\u0275\u0275property"]("list",t.responseMasterList),o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("isAutocomplete",!0),o["\u0275\u0275advance"](12),o["\u0275\u0275property"]("ngIf","Edit"!=t.mode),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf","Edit"==t.mode))},directives:function(){return[a.J,a.w,a.n,I.a,E.a,C,a.v,a.l,m.c,m.g,w.b,a.e,a.F,F,A.b,l.NgIf]},styles:[".createCallLogStyles[_ngcontent-%COMP%]{background-image:url(callLog.1c9f524a019fcbfa6980.png);background-size:253px 200px;background-repeat:no-repeat;overflow-y:hidden!important;background-position:90% 70%;min-height:95vh}.createCallLogStyles[_ngcontent-%COMP%]   .createAccount[_ngcontent-%COMP%]{font-size:14px;color:#cf0001;font-family:Roboto;font-weight:500}.createCallLogStyles[_ngcontent-%COMP%]   .create-account-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%!important;padding-top:4px}.createCallLogStyles[_ngcontent-%COMP%]   .vip-contact[_ngcontent-%COMP%]{color:#1a1a1a;font-size:14px}.createCallLogStyles[_ngcontent-%COMP%]   .check-box-text[_ngcontent-%COMP%]{color:#66615b;font-size:14px}.createCallLogStyles[_ngcontent-%COMP%]   .quotes[_ngcontent-%COMP%]{font-style:italic;font-weight:500;color:#66615b;font-size:14px}.createCallLogStyles[_ngcontent-%COMP%]   .iconbtn[_ngcontent-%COMP%]{background-color:#c92020;color:#fff;padding:0;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.createCallLogStyles[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]     .mat-form-field-wrapper{width:28rem}.createCallLogStyles[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]     .mat-form-field-wrapper .mat-form-field-flex{height:40px}.createCallLogStyles[_ngcontent-%COMP%]   .contact-person[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-flex{padding:0 .75em;margin-top:-.25em;position:relative;width:28rem;height:40px}.createCallLogStyles[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.createCallLogStyles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}"]}),e})()},"8uEH":function(e,t,i){"use strict";i.d(t,"a",(function(){return r}));var n=i("XNiG"),o=i("fXoL"),a=i("tk/3");let r=(()=>{class e{constructor(e){this.http=e,this.toMain=new n.b,this.sendMsg=e=>{this.toMain.next(e)},this.getMsg=()=>this.toMain.asObservable()}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275inject"](a.c))},e.\u0275prov=o["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},DxYt:function(e,t,i){"use strict";i.r(t),i.d(t,"CallLogFormComponent",(function(){return U}));var n=i("fXoL"),o=i("3Pt+"),a=i("ofXK"),r=i("jtHE"),l=i("XNiG"),s=i("NJ67"),c=i("1G5W"),d=i("kmnG"),p=i("d3UM"),m=i("FKr1"),h=i("WJ5W");const u=["singleSelect"];function g(e,t){if(1&e){const e=n["\u0275\u0275getCurrentView"]();n["\u0275\u0275elementStart"](0,"mat-option",6),n["\u0275\u0275listener"]("click",(function(){n["\u0275\u0275restoreView"](e);const i=t.$implicit;return n["\u0275\u0275nextContext"]().emitChanges(i)})),n["\u0275\u0275text"](1),n["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;n["\u0275\u0275property"]("value",e.id),n["\u0275\u0275advance"](1),n["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let f=(()=>{class e extends s.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new o.j,this.fieldFilterCtrl=new o.j,this.list=[],this.required=!1,this.valueChange=new n.EventEmitter,this.disabled=!1,this.filteredList=new r.a,this.change=new n.EventEmitter,this._onDestroy=new l.b,this.emitChanges=e=>{this.change.emit(e)}}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(c.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(c.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){null!=this.list&&this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}}return e.\u0275fac=function(t){return new(t||e)(n["\u0275\u0275directiveInject"](n.Renderer2))},e.\u0275cmp=n["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search"]],viewQuery:function(e,t){if(1&e&&n["\u0275\u0275viewQuery"](u,!0),2&e){let e;n["\u0275\u0275queryRefresh"](e=n["\u0275\u0275loadQuery"]())&&(t.singleSelect=e.first)}},inputs:{list:"list",placeholder:"placeholder",required:"required",disabled:"disabled"},outputs:{valueChange:"valueChange",change:"change"},features:[n["\u0275\u0275ProvidersFeature"]([{provide:o.t,useExisting:Object(n.forwardRef)(()=>e),multi:!0}]),n["\u0275\u0275InheritDefinitionFeature"],n["\u0275\u0275NgOnChangesFeature"]],decls:11,vars:11,consts:[["appearance","outline"],[3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value"],[3,"value","click",4,"ngFor","ngForOf"],[3,"value","click"]],template:function(e,t){1&e&&(n["\u0275\u0275elementStart"](0,"mat-form-field",0),n["\u0275\u0275elementStart"](1,"mat-label"),n["\u0275\u0275text"](2),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](3,"mat-select",1,2),n["\u0275\u0275elementStart"](5,"mat-option"),n["\u0275\u0275element"](6,"ngx-mat-select-search",3),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](7,"mat-option",4),n["\u0275\u0275text"](8,"None"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275template"](9,g,2,2,"mat-option",5),n["\u0275\u0275pipe"](10,"async"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e&&(n["\u0275\u0275advance"](2),n["\u0275\u0275textInterpolate"](t.placeholder),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),n["\u0275\u0275advance"](3),n["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("value",null),n["\u0275\u0275advance"](2),n["\u0275\u0275property"]("ngForOf",n["\u0275\u0275pipeBind1"](10,9,t.filteredList)))},directives:[d.c,d.g,p.c,o.v,o.k,o.F,m.p,h.a,a.NgForOf],pipes:[a.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}"]}),e})();var v=i("mrSG"),C=i("Kj3r"),b=i("F97M"),y=i("XVR1"),S=i("qFsG"),w=i("/1cH"),x=i("NFeN");function E(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"mat-label"),n["\u0275\u0275text"](1),n["\u0275\u0275elementEnd"]()),2&e){const e=n["\u0275\u0275nextContext"]();n["\u0275\u0275advance"](1),n["\u0275\u0275textInterpolate"](e.label)}}function _(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"mat-option",7),n["\u0275\u0275elementStart"](1,"small"),n["\u0275\u0275text"](2),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,i=n["\u0275\u0275nextContext"]();n["\u0275\u0275property"]("hidden",!i.isAutocomplete)("value",e),n["\u0275\u0275advance"](2),n["\u0275\u0275textInterpolate2"]("",e.displayName," | ",e.mail,"")}}let L=(()=>{class e extends s.a{constructor(e,t){super(),this.graphApi=e,this._AppShareService=t,this.userSearchSubject=new l.b,this.isAutocomplete=!1,this.Formwidth="",this.showUserList=new n.EventEmitter,this.selectedUser=new n.EventEmitter,this.label="",this.blur=new n.EventEmitter,this.required=!1,this.fieldCtrl=new o.j,this.disabled=!1,this.isGraphApi=0,this._onDestroy=new l.b}ngOnInit(){this.userSearchSubject.pipe(Object(C.a)(600)).subscribe(e=>Object(v.c)(this,void 0,void 0,(function*(){if(console.log(e),this.isGraphApi)this.graphApi.getUserSuggestions(e).then(t=>Object(v.c)(this,void 0,void 0,(function*(){if(t&&t.length>0)this.showUserList.emit(t);else{let t=yield this.getUserSuggestionsFromDB(e);this.showUserList.emit(t)}})),t=>Object(v.c)(this,void 0,void 0,(function*(){console.log(t);let i=yield this.getUserSuggestionsFromDB(e);this.showUserList.emit(i)})));else{let t=yield this.getUserSuggestionsFromDB(e);this.showUserList.emit(t)}}))),this.fieldCtrl.valueChanges.pipe(Object(c.a)(this._onDestroy)).subscribe(e=>{console.log(e),e?(this.value=e.id,this.onChange(e.id)):(this.value="",this.onChange(""))})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}ngAfterViewInit(){}searchUser(e){if(!e)return this.graphApi.userSuggestions=[];this.userSearchSubject.next(e)}resetSuggestion(){this.graphApi.userSuggestions=[]}selectedOption(e){this.selectedUser.emit(e.option.value),this.value=e.option.value,this.blur.emit()}displayUserName(e){return e?e.displayName:""}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){return Object(v.c)(this,void 0,void 0,(function*(){if(console.log("inisde writevalue"),console.log(e),e)if(this.isGraphApi)this.graphApi.getUserProfile(e).then(t=>Object(v.c)(this,void 0,void 0,(function*(){if(t)this.fieldCtrl.setValue(t);else{let t=yield this.getUserProfileFromDB(e);this.fieldCtrl.setValue(t)}})));else{let t=yield this.getUserProfileFromDB(e);console.log(t),this.fieldCtrl.setValue(t)}else this.fieldCtrl.setValue("")}))}getUserSuggestionsFromDB(e){return Object(v.c)(this,void 0,void 0,(function*(){let t=yield this._AppShareService.getUserSuggestionsFromDB(e);return this.graphApi.userSuggestions=t.value,t.value}))}getUserProfileFromDB(e){return Object(v.c)(this,void 0,void 0,(function*(){let t=yield this._AppShareService.getUserProfileFromDB(e);return console.log(t),t&&t.length>0?t[0]:""}))}}return e.\u0275fac=function(t){return new(t||e)(n["\u0275\u0275directiveInject"](b.a),n["\u0275\u0275directiveInject"](y.a))},e.\u0275cmp=n["\u0275\u0275defineComponent"]({type:e,selectors:[["app-search-user"]],inputs:{isAutocomplete:"isAutocomplete",Formwidth:"Formwidth",label:"label",required:"required",disabled:"disabled",isGraphApi:"isGraphApi"},outputs:{showUserList:"showUserList",selectedUser:"selectedUser",blur:"blur"},features:[n["\u0275\u0275ProvidersFeature"]([{provide:o.t,useExisting:Object(n.forwardRef)(()=>e),multi:!0}]),n["\u0275\u0275InheritDefinitionFeature"]],decls:9,vars:9,consts:[["appearance","outline","width","Formwidth"],[4,"ngIf"],["matInput","",3,"matAutocomplete","placeholder","required","formControl","disabled","keyup","focus"],["matSuffix","",2,"color","#66615B !important","font-size","21px !important"],[3,"hidden","displayWith","optionSelected"],["auto","matAutocomplete"],[3,"hidden","value",4,"ngFor","ngForOf"],[3,"hidden","value"]],template:function(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"div"),n["\u0275\u0275elementStart"](1,"mat-form-field",0),n["\u0275\u0275template"](2,E,2,1,"mat-label",1),n["\u0275\u0275elementStart"](3,"input",2),n["\u0275\u0275listener"]("keyup",(function(e){return t.searchUser(e.target.value)}))("focus",(function(){return t.resetSuggestion()})),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](4,"mat-icon",3),n["\u0275\u0275text"](5,"person_pin"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](6,"mat-autocomplete",4,5),n["\u0275\u0275listener"]("optionSelected",(function(e){return t.selectedOption(e)})),n["\u0275\u0275template"](8,_,3,4,"mat-option",6),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e){const e=n["\u0275\u0275reference"](7);n["\u0275\u0275advance"](2),n["\u0275\u0275property"]("ngIf",t.label),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("matAutocomplete",e)("placeholder",t.label)("required",t.required)("formControl",t.fieldCtrl)("disabled",t.disabled),n["\u0275\u0275advance"](3),n["\u0275\u0275property"]("hidden",!t.isAutocomplete)("displayWith",t.displayUserName),n["\u0275\u0275advance"](2),n["\u0275\u0275property"]("ngForOf",t.graphApi.userSuggestions)}},directives:[d.c,a.NgIf,S.b,w.d,o.e,o.F,o.v,o.k,x.a,d.i,w.b,a.NgForOf,d.g,m.p],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding-bottom:8px!important}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-underline{bottom:0}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-infix{padding:.65em 0!important;border-top:.54375em solid transparent!important;font-size:13px}.mat-form-field[_ngcontent-%COMP%]     .mat-select-arrow{margin:0 4px!important}"]}),e})();var F=i("dNgK"),O=i("bTqV"),I=(i("iadO"),i("0IaG")),P=i("RUbJ"),M=i("WGBV"),D=i("XXEo"),A=i("ihCf");function k(e,t){if(1&e){const e=n["\u0275\u0275getCurrentView"]();n["\u0275\u0275elementStart"](0,"button",28),n["\u0275\u0275listener"]("click",(function(){return n["\u0275\u0275restoreView"](e),n["\u0275\u0275nextContext"]().createCallLog()})),n["\u0275\u0275elementStart"](1,"mat-icon"),n["\u0275\u0275text"](2," done_all"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()}}function j(e,t){if(1&e){const e=n["\u0275\u0275getCurrentView"]();n["\u0275\u0275elementStart"](0,"button",29),n["\u0275\u0275listener"]("click",(function(){return n["\u0275\u0275restoreView"](e),n["\u0275\u0275nextContext"]().editCallLog()})),n["\u0275\u0275elementStart"](1,"mat-icon"),n["\u0275\u0275text"](2," done_all"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()}}let U=(()=>{class e{constructor(e,t,i,a,r,l,s,c){this.dialogRef=e,this.dialogData=t,this.dialog=i,this.fb=a,this.activityToMainService=r,this.snackBar=l,this.accountService=s,this.loginService=c,this.close=new n.EventEmitter,this.callLogForm=this.fb.group({title:["",o.H.required],description:[""],contactType:["",o.H.required],contactedPerson:[""],response:["",o.H.required],contactedBy:["",o.H.required],notes:[""],dueDate:[""],remindMe:[""],applicationName:[""],accountId:[""],applicationId:[""],governanceType:[""]}),this.currentUser=this.loginService.getProfile().profile,this.editCallLog=()=>{this.callLogForm.valid?this.accountService.editCallLog(this.activityId,this.callLogForm.value).then(e=>{this.callLogForm.reset(),this.snackBar.open("Call Log edited Successfully!","Dismiss",{duration:2e3}),this.close.emit("update required"),this.dialogRef.close("Update Required"),this.activityToMainService.sendMsg("reload")},e=>{this.snackBar.open("Failed to edit call log.","Dismiss",{duration:2e3}),console.error(e)}):this.snackBar.open("Enter all Mandatory Fields!","Dismiss",{duration:2e3})},this.createCallLog=()=>{this.callLogForm.valid?this.accountService.createCallLog(this.callLogForm.value).subscribe(e=>{this.callLogForm.reset(),this.snackBar.open("Call Log Created Successfully!","Dismiss",{duration:2e3}),this.close.emit("close"),this.dialogRef.close("Update Required"),this.activityToMainService.sendMsg("reload"),console.log(e)},e=>{this.snackBar.open("Failed to create call log.","Dismiss",{duration:2e3}),console.error(e)}):this.snackBar.open("Enter all Mandatory Fields!","Dismiss",{duration:2e3})},this.closeClicked=()=>{this.close.emit(),this.dialogRef.close()}}ngOnChanges(){}updateFormWithCallLogDetails(){if(console.log(this.mode,this.activityId),"Edit"==this.mode){let e=this.data?this.data:null;console.log(e),e&&this.callLogForm.patchValue({title:e.title,description:e.description,contactType:e.activity_sub_type_id,contactedPerson:e.contacted_person,response:e.response_type_id,contactedBy:e.contacted_by,governanceType:e.governance_activity_id,notes:e.notes?e.notes[0].notes:e.notes,remindMe:e.reminder})}else this.callLogForm.reset()}ngOnInit(){let e;this.mode=this.dialogData.mode,this.activityId=this.dialogData.activityId,this.data=this.dialogData.data,"Edit"==this.mode&&this.updateFormWithCallLogDetails(),this.accountService.contactType().then(e=>{this.contactTypeList=e}),this.accountService.responseType().then(e=>{this.responseMasterList=e}),this.accountService.getAccountGovernanceType(33).subscribe(e=>{this.accountGovernanceTypes=e},e=>{console.log(e)}),"accounts"==window.location.pathname.split("/")[2]&&(e=33),this.callLogForm.patchValue({applicationName:window.location.pathname.split("/")[2],accountId:window.location.pathname.split("/")[3],applicationId:e,governanceType:50,contactedBy:this.currentUser.oid,contactType:5})}}return e.\u0275fac=function(t){return new(t||e)(n["\u0275\u0275directiveInject"](I.h),n["\u0275\u0275directiveInject"](I.a),n["\u0275\u0275directiveInject"](I.b),n["\u0275\u0275directiveInject"](o.i),n["\u0275\u0275directiveInject"](P.a),n["\u0275\u0275directiveInject"](F.a),n["\u0275\u0275directiveInject"](M.a),n["\u0275\u0275directiveInject"](D.a))},e.\u0275cmp=n["\u0275\u0275defineComponent"]({type:e,selectors:[["app-call-log-form"]],outputs:{close:"close"},features:[n["\u0275\u0275NgOnChangesFeature"]],decls:51,vars:7,consts:[[1,"container","createCallLogStyles"],[3,"formGroup"],[1,"row","border-bottom","solid"],[1,"col-11","pt-2","createAccount"],[1,"col-1","d-flex"],["mat-icon-button","",1,"ml-auto","close-button",3,"click"],[1,"close-Icon"],[1,"row","pt-2"],[1,"col-8","pl-0","pr-0"],[1,"row"],[1,"col-12"],["appearance","outline",1,"create-account-field","title"],["matInput","","placeholder","title *","formControlName","title"],["matInput","","placeholder","Description","formControlName","description"],["required","true","placeholder","Contact Type","formControlName","contactType",1,"create-account-field","title",3,"list"],[1,"row","pt-1"],["matInput","","placeholder","Contacted person","required","true","formControlName","contactedPerson"],["required","true","placeholder","Response","formControlName","response",1,"create-account-field","title",3,"list"],["label","Contacted By","required","true","formControlName","contactedBy",1,"contact-person",3,"isAutocomplete"],["appearance","outline",1,"textArea",2,"width","28rem","font-size","14px !important"],["matInput","","cdkTextareaAutosize","","cdkAutosizeMinRows","4","cdkAutosizeMaxRows","","placeholder","Notes","formControlName","notes"],["autosize","cdkTextareaAutosize"],[1,"col-4"],[1,"row","pt-3"],[1,"col-9","pl-4","quotes"],[1,"col-3"],["mat-icon-button","","class","iconbtn ml-2 mt-0","matTooltip","Create Call Log","type","submit",3,"click",4,"ngIf"],["mat-icon-button","","class","iconbtn ml-2 mt-0","matTooltip","Edit Call Log","type","submit",3,"click",4,"ngIf"],["mat-icon-button","","matTooltip","Create Call Log","type","submit",1,"iconbtn","ml-2","mt-0",3,"click"],["mat-icon-button","","matTooltip","Edit Call Log","type","submit",1,"iconbtn","ml-2","mt-0",3,"click"]],template:function(e,t){1&e&&(n["\u0275\u0275elementStart"](0,"div",0),n["\u0275\u0275elementStart"](1,"form",1),n["\u0275\u0275elementStart"](2,"div",2),n["\u0275\u0275elementStart"](3,"div",3),n["\u0275\u0275text"](4),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](5,"div",4),n["\u0275\u0275elementStart"](6,"button",5),n["\u0275\u0275listener"]("click",(function(){return t.closeClicked()})),n["\u0275\u0275elementStart"](7,"mat-icon",6),n["\u0275\u0275text"](8,"close"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](9,"div",7),n["\u0275\u0275elementStart"](10,"div",8),n["\u0275\u0275elementStart"](11,"div",9),n["\u0275\u0275elementStart"](12,"div",10),n["\u0275\u0275elementStart"](13,"mat-form-field",11),n["\u0275\u0275elementStart"](14,"mat-label"),n["\u0275\u0275text"](15,"Title *"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275element"](16,"input",12),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](17,"div",9),n["\u0275\u0275elementStart"](18,"div",10),n["\u0275\u0275elementStart"](19,"mat-form-field",11),n["\u0275\u0275elementStart"](20,"mat-label"),n["\u0275\u0275text"](21,"Description"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275element"](22,"input",13),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](23,"div",9),n["\u0275\u0275elementStart"](24,"div",10),n["\u0275\u0275element"](25,"app-input-search",14),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](26,"div",15),n["\u0275\u0275elementStart"](27,"div",10),n["\u0275\u0275elementStart"](28,"mat-form-field",11),n["\u0275\u0275elementStart"](29,"mat-label"),n["\u0275\u0275text"](30,"Contacted person "),n["\u0275\u0275elementEnd"](),n["\u0275\u0275element"](31,"input",16),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](32,"div",9),n["\u0275\u0275elementStart"](33,"div",10),n["\u0275\u0275element"](34,"app-input-search",17),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](35,"div",15),n["\u0275\u0275elementStart"](36,"div",10),n["\u0275\u0275element"](37,"app-search-user",18),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](38,"div",15),n["\u0275\u0275elementStart"](39,"div",10),n["\u0275\u0275elementStart"](40,"mat-form-field",19),n["\u0275\u0275element"](41,"textarea",20,21),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275element"](43,"div",22),n["\u0275\u0275elementEnd"](),n["\u0275\u0275element"](44,"div",23),n["\u0275\u0275elementStart"](45,"div",23),n["\u0275\u0275elementStart"](46,"div",24),n["\u0275\u0275text"](47,' "In the Middle of difficulty, lies Opportunity" '),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](48,"div",25),n["\u0275\u0275template"](49,k,3,0,"button",26),n["\u0275\u0275template"](50,j,3,0,"button",27),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e&&(n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("formGroup",t.callLogForm),n["\u0275\u0275advance"](3),n["\u0275\u0275textInterpolate1"]("",t.mode," call log"),n["\u0275\u0275advance"](21),n["\u0275\u0275property"]("list",t.contactTypeList),n["\u0275\u0275advance"](9),n["\u0275\u0275property"]("list",t.responseMasterList),n["\u0275\u0275advance"](3),n["\u0275\u0275property"]("isAutocomplete",!0),n["\u0275\u0275advance"](12),n["\u0275\u0275property"]("ngIf","Edit"!=t.mode),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf","Edit"==t.mode))},directives:function(){return[o.J,o.w,o.n,O.a,x.a,d.c,d.g,S.b,o.e,o.v,o.l,f,o.F,L,A.b,a.NgIf]},styles:[".createCallLogStyles[_ngcontent-%COMP%]{background-image:url(callLog.1c9f524a019fcbfa6980.png);background-size:253px 200px;background-repeat:no-repeat;overflow-y:hidden!important;background-position:90% 70%;min-height:95vh}.createCallLogStyles[_ngcontent-%COMP%]   .createAccount[_ngcontent-%COMP%]{font-size:14px;color:#cf0001;font-family:Roboto;font-weight:500}.createCallLogStyles[_ngcontent-%COMP%]   .create-account-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%!important;padding-top:4px}.createCallLogStyles[_ngcontent-%COMP%]   .vip-contact[_ngcontent-%COMP%]{color:#1a1a1a;font-size:14px}.createCallLogStyles[_ngcontent-%COMP%]   .check-box-text[_ngcontent-%COMP%]{color:#66615b;font-size:14px}.createCallLogStyles[_ngcontent-%COMP%]   .quotes[_ngcontent-%COMP%]{font-style:italic;font-weight:500;color:#66615b;font-size:14px}.createCallLogStyles[_ngcontent-%COMP%]   .iconbtn[_ngcontent-%COMP%]{background-color:#c92020;color:#fff;padding:0;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.createCallLogStyles[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]     .mat-form-field-wrapper{width:28rem}.createCallLogStyles[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]     .mat-form-field-wrapper .mat-form-field-flex{height:40px}.createCallLogStyles[_ngcontent-%COMP%]   .contact-person[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-flex{padding:0 .75em;margin-top:-.25em;position:relative;width:28rem;height:40px}.createCallLogStyles[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.createCallLogStyles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}"]}),e})()},U10Z:function(e,t,i){"use strict";i.r(t),i.d(t,"CallLogFormComponent",(function(){return U}));var n=i("fXoL"),o=i("3Pt+"),a=i("ofXK"),r=i("jtHE"),l=i("XNiG"),s=i("NJ67"),c=i("1G5W"),d=i("kmnG"),p=i("d3UM"),m=i("FKr1"),h=i("WJ5W");const u=["singleSelect"];function g(e,t){if(1&e){const e=n["\u0275\u0275getCurrentView"]();n["\u0275\u0275elementStart"](0,"mat-option",6),n["\u0275\u0275listener"]("click",(function(){n["\u0275\u0275restoreView"](e);const i=t.$implicit;return n["\u0275\u0275nextContext"]().emitChanges(i)})),n["\u0275\u0275text"](1),n["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;n["\u0275\u0275property"]("value",e.id),n["\u0275\u0275advance"](1),n["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let f=(()=>{class e extends s.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new o.j,this.fieldFilterCtrl=new o.j,this.list=[],this.required=!1,this.valueChange=new n.EventEmitter,this.disabled=!1,this.filteredList=new r.a,this.change=new n.EventEmitter,this._onDestroy=new l.b,this.emitChanges=e=>{this.change.emit(e)}}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(c.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(c.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){null!=this.list&&this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}}return e.\u0275fac=function(t){return new(t||e)(n["\u0275\u0275directiveInject"](n.Renderer2))},e.\u0275cmp=n["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search"]],viewQuery:function(e,t){if(1&e&&n["\u0275\u0275viewQuery"](u,!0),2&e){let e;n["\u0275\u0275queryRefresh"](e=n["\u0275\u0275loadQuery"]())&&(t.singleSelect=e.first)}},inputs:{list:"list",placeholder:"placeholder",required:"required",disabled:"disabled"},outputs:{valueChange:"valueChange",change:"change"},features:[n["\u0275\u0275ProvidersFeature"]([{provide:o.t,useExisting:Object(n.forwardRef)(()=>e),multi:!0}]),n["\u0275\u0275InheritDefinitionFeature"],n["\u0275\u0275NgOnChangesFeature"]],decls:11,vars:11,consts:[["appearance","outline"],[3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value"],[3,"value","click",4,"ngFor","ngForOf"],[3,"value","click"]],template:function(e,t){1&e&&(n["\u0275\u0275elementStart"](0,"mat-form-field",0),n["\u0275\u0275elementStart"](1,"mat-label"),n["\u0275\u0275text"](2),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](3,"mat-select",1,2),n["\u0275\u0275elementStart"](5,"mat-option"),n["\u0275\u0275element"](6,"ngx-mat-select-search",3),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](7,"mat-option",4),n["\u0275\u0275text"](8,"None"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275template"](9,g,2,2,"mat-option",5),n["\u0275\u0275pipe"](10,"async"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e&&(n["\u0275\u0275advance"](2),n["\u0275\u0275textInterpolate"](t.placeholder),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),n["\u0275\u0275advance"](3),n["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("value",null),n["\u0275\u0275advance"](2),n["\u0275\u0275property"]("ngForOf",n["\u0275\u0275pipeBind1"](10,9,t.filteredList)))},directives:[d.c,d.g,p.c,o.v,o.k,o.F,m.p,h.a,a.NgForOf],pipes:[a.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}"]}),e})();var v=i("mrSG"),C=i("Kj3r"),b=i("F97M"),y=i("XVR1"),S=i("qFsG"),w=i("/1cH"),x=i("NFeN");function E(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"mat-label"),n["\u0275\u0275text"](1),n["\u0275\u0275elementEnd"]()),2&e){const e=n["\u0275\u0275nextContext"]();n["\u0275\u0275advance"](1),n["\u0275\u0275textInterpolate"](e.label)}}function _(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"mat-option",7),n["\u0275\u0275elementStart"](1,"small"),n["\u0275\u0275text"](2),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,i=n["\u0275\u0275nextContext"]();n["\u0275\u0275property"]("hidden",!i.isAutocomplete)("value",e),n["\u0275\u0275advance"](2),n["\u0275\u0275textInterpolate2"]("",e.displayName," | ",e.mail,"")}}let L=(()=>{class e extends s.a{constructor(e,t){super(),this.graphApi=e,this._AppShareService=t,this.userSearchSubject=new l.b,this.isAutocomplete=!1,this.Formwidth="",this.showUserList=new n.EventEmitter,this.selectedUser=new n.EventEmitter,this.label="",this.blur=new n.EventEmitter,this.required=!1,this.fieldCtrl=new o.j,this.disabled=!1,this.isGraphApi=0,this._onDestroy=new l.b}ngOnInit(){this.userSearchSubject.pipe(Object(C.a)(600)).subscribe(e=>Object(v.c)(this,void 0,void 0,(function*(){if(console.log(e),this.isGraphApi)this.graphApi.getUserSuggestions(e).then(t=>Object(v.c)(this,void 0,void 0,(function*(){if(t&&t.length>0)this.showUserList.emit(t);else{let t=yield this.getUserSuggestionsFromDB(e);this.showUserList.emit(t)}})),t=>Object(v.c)(this,void 0,void 0,(function*(){console.log(t);let i=yield this.getUserSuggestionsFromDB(e);this.showUserList.emit(i)})));else{let t=yield this.getUserSuggestionsFromDB(e);this.showUserList.emit(t)}}))),this.fieldCtrl.valueChanges.pipe(Object(c.a)(this._onDestroy)).subscribe(e=>{console.log(e),e?(this.value=e.id,this.onChange(e.id)):(this.value="",this.onChange(""))})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}ngAfterViewInit(){}searchUser(e){if(!e)return this.graphApi.userSuggestions=[];this.userSearchSubject.next(e)}resetSuggestion(){this.graphApi.userSuggestions=[]}selectedOption(e){this.selectedUser.emit(e.option.value),this.value=e.option.value,this.blur.emit()}displayUserName(e){return e?e.displayName:""}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){return Object(v.c)(this,void 0,void 0,(function*(){if(console.log("inisde writevalue"),console.log(e),e)if(this.isGraphApi)this.graphApi.getUserProfile(e).then(t=>Object(v.c)(this,void 0,void 0,(function*(){if(t)this.fieldCtrl.setValue(t);else{let t=yield this.getUserProfileFromDB(e);this.fieldCtrl.setValue(t)}})));else{let t=yield this.getUserProfileFromDB(e);console.log(t),this.fieldCtrl.setValue(t)}else this.fieldCtrl.setValue("")}))}getUserSuggestionsFromDB(e){return Object(v.c)(this,void 0,void 0,(function*(){let t=yield this._AppShareService.getUserSuggestionsFromDB(e);return this.graphApi.userSuggestions=t.value,t.value}))}getUserProfileFromDB(e){return Object(v.c)(this,void 0,void 0,(function*(){let t=yield this._AppShareService.getUserProfileFromDB(e);return console.log(t),t&&t.length>0?t[0]:""}))}}return e.\u0275fac=function(t){return new(t||e)(n["\u0275\u0275directiveInject"](b.a),n["\u0275\u0275directiveInject"](y.a))},e.\u0275cmp=n["\u0275\u0275defineComponent"]({type:e,selectors:[["app-search-user"]],inputs:{isAutocomplete:"isAutocomplete",Formwidth:"Formwidth",label:"label",required:"required",disabled:"disabled",isGraphApi:"isGraphApi"},outputs:{showUserList:"showUserList",selectedUser:"selectedUser",blur:"blur"},features:[n["\u0275\u0275ProvidersFeature"]([{provide:o.t,useExisting:Object(n.forwardRef)(()=>e),multi:!0}]),n["\u0275\u0275InheritDefinitionFeature"]],decls:9,vars:9,consts:[["appearance","outline","width","Formwidth"],[4,"ngIf"],["matInput","",3,"matAutocomplete","placeholder","required","formControl","disabled","keyup","focus"],["matSuffix","",2,"color","#66615B !important","font-size","21px !important"],[3,"hidden","displayWith","optionSelected"],["auto","matAutocomplete"],[3,"hidden","value",4,"ngFor","ngForOf"],[3,"hidden","value"]],template:function(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"div"),n["\u0275\u0275elementStart"](1,"mat-form-field",0),n["\u0275\u0275template"](2,E,2,1,"mat-label",1),n["\u0275\u0275elementStart"](3,"input",2),n["\u0275\u0275listener"]("keyup",(function(e){return t.searchUser(e.target.value)}))("focus",(function(){return t.resetSuggestion()})),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](4,"mat-icon",3),n["\u0275\u0275text"](5,"person_pin"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](6,"mat-autocomplete",4,5),n["\u0275\u0275listener"]("optionSelected",(function(e){return t.selectedOption(e)})),n["\u0275\u0275template"](8,_,3,4,"mat-option",6),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e){const e=n["\u0275\u0275reference"](7);n["\u0275\u0275advance"](2),n["\u0275\u0275property"]("ngIf",t.label),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("matAutocomplete",e)("placeholder",t.label)("required",t.required)("formControl",t.fieldCtrl)("disabled",t.disabled),n["\u0275\u0275advance"](3),n["\u0275\u0275property"]("hidden",!t.isAutocomplete)("displayWith",t.displayUserName),n["\u0275\u0275advance"](2),n["\u0275\u0275property"]("ngForOf",t.graphApi.userSuggestions)}},directives:[d.c,a.NgIf,S.b,w.d,o.e,o.F,o.v,o.k,x.a,d.i,w.b,a.NgForOf,d.g,m.p],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding-bottom:8px!important}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-underline{bottom:0}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-infix{padding:.65em 0!important;border-top:.54375em solid transparent!important;font-size:13px}.mat-form-field[_ngcontent-%COMP%]     .mat-select-arrow{margin:0 4px!important}"]}),e})();var F=i("dNgK"),O=i("bTqV"),I=(i("iadO"),i("0IaG")),P=i("RJSY"),M=i("+yIk"),D=i("XXEo"),A=i("ihCf");function k(e,t){if(1&e){const e=n["\u0275\u0275getCurrentView"]();n["\u0275\u0275elementStart"](0,"button",28),n["\u0275\u0275listener"]("click",(function(){return n["\u0275\u0275restoreView"](e),n["\u0275\u0275nextContext"]().createCallLog()})),n["\u0275\u0275elementStart"](1,"mat-icon"),n["\u0275\u0275text"](2," done_all"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()}}function j(e,t){if(1&e){const e=n["\u0275\u0275getCurrentView"]();n["\u0275\u0275elementStart"](0,"button",29),n["\u0275\u0275listener"]("click",(function(){return n["\u0275\u0275restoreView"](e),n["\u0275\u0275nextContext"]().editCallLog()})),n["\u0275\u0275elementStart"](1,"mat-icon"),n["\u0275\u0275text"](2," done_all"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()}}let U=(()=>{class e{constructor(e,t,i,a,r,l,s,c){this.dialogRef=e,this.dialogData=t,this.dialog=i,this.fb=a,this.activityToMainService=r,this.snackBar=l,this.leadService=s,this.loginService=c,this.close=new n.EventEmitter,this.callLogForm=this.fb.group({title:["",o.H.required],description:[""],contactType:["",o.H.required],contactedPerson:[""],response:["",o.H.required],contactedBy:["",o.H.required],notes:[""],dueDate:[""],remindMe:[""],applicationName:[""],applicationReferenceId:[""],applicationId:[""],governanceType:[""]}),this.currentUser=this.loginService.getProfile().profile,this.editCallLog=()=>{this.callLogForm.valid?this.leadService.editCallLog(this.activityId,this.callLogForm.value).then(e=>{this.callLogForm.reset(),this.snackBar.open("Call Log edited Successfully!","Dismiss",{duration:2e3}),this.close.emit("update required"),this.dialogRef.close("Update Required"),this.activityToMainService.sendMsg("reload")},e=>{this.snackBar.open("Failed to edit call log.","Dismiss",{duration:2e3}),console.error(e)}):this.snackBar.open("Enter all Mandatory Fields!","Dismiss",{duration:2e3})},this.createCallLog=()=>{this.callLogForm.valid?this.leadService.createCallLog(this.callLogForm.value).subscribe(e=>{this.callLogForm.reset(),this.snackBar.open("Call Log Created Successfully!","Dismiss",{duration:2e3}),this.close.emit("close"),this.dialogRef.close("Update Required"),this.activityToMainService.sendMsg("reload"),console.log(e)},e=>{this.snackBar.open("Failed to create call log.","Dismiss",{duration:2e3}),console.error(e)}):this.snackBar.open("Enter all Mandatory Fields!","Dismiss",{duration:2e3})},this.closeClicked=()=>{this.close.emit(),this.dialogRef.close()}}ngOnChanges(){}updateFormWithCallLogDetails(){if(console.log(this.mode,this.activityId),"Edit"==this.mode){let e=this.data?this.data:null;console.log(e),e&&this.callLogForm.patchValue({title:e.title,description:e.description,contactType:e.activity_sub_type_id,contactedPerson:e.contacted_person,response:e.response_type_id,contactedBy:e.contacted_by,governanceType:e.governance_activity_id,notes:e.notes?e.notes[0].notes:e.notes,remindMe:e.reminder})}else this.callLogForm.reset()}ngOnInit(){let e;this.mode=this.dialogData.mode,this.activityId=this.dialogData.activityId,this.data=this.dialogData.data,"Edit"==this.mode&&this.updateFormWithCallLogDetails(),this.leadService.contactType().then(e=>{this.contactTypeList=e}),this.leadService.responseType().then(e=>{this.responseMasterList=e}),this.leadService.getLeadsGovernanceType(75).subscribe(e=>{this.leadGovernanceTypes=e},e=>{console.log(e)}),"leads"==window.location.pathname.split("/")[2]?e=35:"opportunities"==window.location.pathname.split("/")[2]&&(e=36),this.callLogForm.patchValue({applicationName:window.location.pathname.split("/")[2],applicationReferenceId:window.location.pathname.split("/")[3],applicationId:e,governanceType:36,contactedBy:this.currentUser.oid,contactType:5})}}return e.\u0275fac=function(t){return new(t||e)(n["\u0275\u0275directiveInject"](I.h),n["\u0275\u0275directiveInject"](I.a),n["\u0275\u0275directiveInject"](I.b),n["\u0275\u0275directiveInject"](o.i),n["\u0275\u0275directiveInject"](P.a),n["\u0275\u0275directiveInject"](F.a),n["\u0275\u0275directiveInject"](M.a),n["\u0275\u0275directiveInject"](D.a))},e.\u0275cmp=n["\u0275\u0275defineComponent"]({type:e,selectors:[["app-call-log-form"]],outputs:{close:"close"},features:[n["\u0275\u0275NgOnChangesFeature"]],decls:51,vars:7,consts:[[1,"container","createCallLogStyles"],[3,"formGroup"],[1,"row","border-bottom","solid"],[1,"col-11","pt-2","createAccount"],[1,"col-1","d-flex"],["mat-icon-button","",1,"ml-auto","close-button",3,"click"],[1,"close-Icon"],[1,"row","pt-2"],[1,"col-8","pl-0","pr-0"],[1,"row"],[1,"col-12"],["appearance","outline",1,"create-account-field","title"],["matInput","","placeholder","title *","formControlName","title"],["matInput","","placeholder","Description","formControlName","description"],["required","true","placeholder","Contact Type","formControlName","contactType",1,"create-account-field","title",3,"list"],[1,"row","pt-1"],["matInput","","placeholder","Contacted person","required","true","formControlName","contactedPerson"],["required","true","placeholder","Response","formControlName","response",1,"create-account-field","title",3,"list"],["label","Contacted By","required","true","formControlName","contactedBy",1,"contact-person",3,"isAutocomplete"],["appearance","outline",1,"textArea",2,"width","28rem","font-size","14px !important"],["matInput","","cdkTextareaAutosize","","cdkAutosizeMinRows","4","cdkAutosizeMaxRows","","placeholder","Notes","formControlName","notes"],["autosize","cdkTextareaAutosize"],[1,"col-4"],[1,"row","pt-3"],[1,"col-9","pl-4","quotes"],[1,"col-3"],["mat-icon-button","","class","iconbtn ml-2 mt-0","matTooltip","Create Call Log","type","submit",3,"click",4,"ngIf"],["mat-icon-button","","class","iconbtn ml-2 mt-0","matTooltip","Edit Call Log","type","submit",3,"click",4,"ngIf"],["mat-icon-button","","matTooltip","Create Call Log","type","submit",1,"iconbtn","ml-2","mt-0",3,"click"],["mat-icon-button","","matTooltip","Edit Call Log","type","submit",1,"iconbtn","ml-2","mt-0",3,"click"]],template:function(e,t){1&e&&(n["\u0275\u0275elementStart"](0,"div",0),n["\u0275\u0275elementStart"](1,"form",1),n["\u0275\u0275elementStart"](2,"div",2),n["\u0275\u0275elementStart"](3,"div",3),n["\u0275\u0275text"](4),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](5,"div",4),n["\u0275\u0275elementStart"](6,"button",5),n["\u0275\u0275listener"]("click",(function(){return t.closeClicked()})),n["\u0275\u0275elementStart"](7,"mat-icon",6),n["\u0275\u0275text"](8,"close"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](9,"div",7),n["\u0275\u0275elementStart"](10,"div",8),n["\u0275\u0275elementStart"](11,"div",9),n["\u0275\u0275elementStart"](12,"div",10),n["\u0275\u0275elementStart"](13,"mat-form-field",11),n["\u0275\u0275elementStart"](14,"mat-label"),n["\u0275\u0275text"](15,"Title *"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275element"](16,"input",12),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](17,"div",9),n["\u0275\u0275elementStart"](18,"div",10),n["\u0275\u0275elementStart"](19,"mat-form-field",11),n["\u0275\u0275elementStart"](20,"mat-label"),n["\u0275\u0275text"](21,"Description"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275element"](22,"input",13),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](23,"div",9),n["\u0275\u0275elementStart"](24,"div",10),n["\u0275\u0275element"](25,"app-input-search",14),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](26,"div",15),n["\u0275\u0275elementStart"](27,"div",10),n["\u0275\u0275elementStart"](28,"mat-form-field",11),n["\u0275\u0275elementStart"](29,"mat-label"),n["\u0275\u0275text"](30,"Contacted person "),n["\u0275\u0275elementEnd"](),n["\u0275\u0275element"](31,"input",16),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](32,"div",9),n["\u0275\u0275elementStart"](33,"div",10),n["\u0275\u0275element"](34,"app-input-search",17),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](35,"div",15),n["\u0275\u0275elementStart"](36,"div",10),n["\u0275\u0275element"](37,"app-search-user",18),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](38,"div",15),n["\u0275\u0275elementStart"](39,"div",10),n["\u0275\u0275elementStart"](40,"mat-form-field",19),n["\u0275\u0275element"](41,"textarea",20,21),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275element"](43,"div",22),n["\u0275\u0275elementEnd"](),n["\u0275\u0275element"](44,"div",23),n["\u0275\u0275elementStart"](45,"div",23),n["\u0275\u0275elementStart"](46,"div",24),n["\u0275\u0275text"](47,' "In the Middle of difficulty, lies Opportunity" '),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](48,"div",25),n["\u0275\u0275template"](49,k,3,0,"button",26),n["\u0275\u0275template"](50,j,3,0,"button",27),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e&&(n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("formGroup",t.callLogForm),n["\u0275\u0275advance"](3),n["\u0275\u0275textInterpolate1"]("",t.mode," call log"),n["\u0275\u0275advance"](21),n["\u0275\u0275property"]("list",t.contactTypeList),n["\u0275\u0275advance"](9),n["\u0275\u0275property"]("list",t.responseMasterList),n["\u0275\u0275advance"](3),n["\u0275\u0275property"]("isAutocomplete",!0),n["\u0275\u0275advance"](12),n["\u0275\u0275property"]("ngIf","Edit"!=t.mode),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf","Edit"==t.mode))},directives:function(){return[o.J,o.w,o.n,O.a,x.a,d.c,d.g,S.b,o.e,o.v,o.l,f,o.F,L,A.b,a.NgIf]},styles:[".createCallLogStyles[_ngcontent-%COMP%]{background-image:url(callLog.1c9f524a019fcbfa6980.png);background-size:253px 200px;background-repeat:no-repeat;overflow-y:hidden!important;background-position:90% 70%;min-height:95vh}.createCallLogStyles[_ngcontent-%COMP%]   .createAccount[_ngcontent-%COMP%]{font-size:14px;color:#cf0001;font-family:Roboto;font-weight:500}.createCallLogStyles[_ngcontent-%COMP%]   .create-account-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%!important;padding-top:4px}.createCallLogStyles[_ngcontent-%COMP%]   .vip-contact[_ngcontent-%COMP%]{color:#1a1a1a;font-size:14px}.createCallLogStyles[_ngcontent-%COMP%]   .check-box-text[_ngcontent-%COMP%]{color:#66615b;font-size:14px}.createCallLogStyles[_ngcontent-%COMP%]   .quotes[_ngcontent-%COMP%]{font-style:italic;font-weight:500;color:#66615b;font-size:14px}.createCallLogStyles[_ngcontent-%COMP%]   .iconbtn[_ngcontent-%COMP%]{background-color:#c92020;color:#fff;padding:0;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.createCallLogStyles[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]     .mat-form-field-wrapper{width:28rem}.createCallLogStyles[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]     .mat-form-field-wrapper .mat-form-field-flex{height:40px}.createCallLogStyles[_ngcontent-%COMP%]   .contact-person[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-flex{padding:0 .75em;margin-top:-.25em;position:relative;width:28rem;height:40px}.createCallLogStyles[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.createCallLogStyles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}"]}),e})()},VsNQ:function(e,t,i){"use strict";i.d(t,"a",(function(){return c}));var n=i("mrSG"),o=i("xG9w"),a=i("XNiG"),r=i("fXoL"),l=i("tk/3"),s=i("flaP");let c=(()=>{class e{constructor(e,t){this.http=e,this._roleService=t,this.contactReload=new a.b,this.isHierarchyVisible=(e,t)=>o.where(this._roleService.roles,{application_id:e,object_id:t}).length>0,this.getContactsByAccounts=e=>this.http.post("api/accounts/getContactsByAccounts",{account_id:e}),this.saveContact=e=>this.http.post("/api/contacts/createContact",{contactDetails:e}),this.deactivateAccess=()=>{let e=o.where(this._roleService.roles,{application_id:34,object_id:29370});return console.log("accessList",e),e.length>0},this.getContactGovernanceType=e=>this.http.post("/api/activity/getGovernanceActivityType",{applicationId:e}),this.getActivityFilterMasterDate=()=>this.http.post("/api/activity/activityFilterMasterData",{}),this.updateTaskDuedate=(e,t)=>this.http.post("/api/contacts/updateTaskDueDate",{activity_id:e,date:t}),this.getActivityNotes=e=>(console.log(e),this.http.post("/api/contacts/activityNotes",{activity_id:e,operation_id:2})),this.createActivityNote=(e,t,i,n)=>this.http.post("/api/contacts/activityNotes",{operation_id:1,activity_id:e,message:n,color_code:t,title:i}),this.editActivityNote=(e,t,i,n)=>this.http.post("/api/contacts/activityNotes",{activity_id:e,operation_id:3,note_id:t,message:i,title:n}),this.deleteActivityNote=(e,t)=>this.http.post("/api/contacts/activityNotes",{activity_id:e,operation_id:4,note_id:t}),this.getUserProfileFromContacts=e=>{try{return new Promise((t,i)=>{this.http.post("/api/contacts/getContactInfo",{oid:e}).subscribe(e=>t(e),e=>(console.log(e),i(e)))})}catch(t){return Promise.reject()}},this.updateTaskDetailInline=(e,t)=>this.http.post("/api/contacts/editTaskInline",{taskFormDetails:t,activityId:e}),this.saveImage=(e,t)=>this.http.post("/api/contacts/saveImage",{result:e,contactId:t}),this.getContactsList=(e,t)=>this.http.post("/api/contacts/getContactsList",{org_codes:t,filterConfig:e}),this.getTotalContacts=(e,t)=>new Promise((i,n)=>{this.http.post("/api/contacts/getTotalContacts",{org_codes:t,filterConfig:e}).subscribe(e=>i(e),e=>n(e))}),this.getContactPersona=()=>new Promise((e,t)=>{this.http.post("/api/contacts/getReportFilterPersonainContact",{}).subscribe(t=>e(t),e=>t(e))}),this.getAccessTokenInfo=()=>this.http.post("/api/collector/getTokenConfigDetails",{}),this.getMailConfigFlag=()=>this.http.post("/api/contacts/getMailConfigFlag",{}),this.getLabelForOpportunity=(e,t)=>{let i=o.where(this._roleService.label,{application_id:e,id:t});return i.length>0?i[0].label_name:""},this.getUDRFContactConfig=()=>new Promise((e,t)=>{this.http.post("/api/contacts/getUDRFContactConfig",{}).subscribe(t=>{e(t)},e=>{t(e)})}),this.getContactHierarchyData=(e,t)=>this.http.post("/api/contacts/getContactHierarchyData",{application_reference_id:e,application_id:t}),this.downloadContactsList=(e,t)=>this.http.post("/api/contacts/downloadContactsList",{org_codes:t,filterConfig:e}),this.checkIfAccountExist=e=>this.http.post("/api/accounts/checkIfAccountExist",{accountId:e})}getAllAccounts(e){return new Promise((t,i)=>{this.http.post("/api/contacts/getAllAccounts",{orgCodes:e}).subscribe(e=>t(e),e=>i(e))})}getContactsByAccountIds(e){return new Promise((t,i)=>{this.http.post("/api/contacts/contactsByAccountIds",{orgCodes:e}).subscribe(e=>t(e),e=>i(e))})}getFilterMaster(){return this.http.get("/api/contacts/masterDataForContactFilter")}getFilterData(e,t){return this.http.post("/api/contacts/ContactsFilter",{filterData:e,orgCodes:t})}getUserProfileFromDB(e){return Object(n.c)(this,void 0,void 0,(function*(){try{return new Promise((t,i)=>{this.http.post("/api/project/getUserProfileFromDB",{oid:e}).subscribe(e=>t(e),e=>(console.log(e),i(e)))})}catch(t){return Promise.reject()}}))}removeMember(e){return Object(n.c)(this,void 0,void 0,(function*(){return new Promise((t,i)=>{this.http.post("/api/project/removeMember",{stakeholder_table_id:e}).subscribe(e=>t(e))})}))}setContactVipStatus(e,t){return this.http.post("/api/contacts/AddOrRemoveContactFav",{flag:e,contact_id:t})}getContacts(e){return this.http.post("/api/contacts/allActiveContacts",{orgCodes:e})}updateContactById(e,t){return this.http.post("/api/contacts/updateContact",{contact_id:e,contact_details:t})}performAddNotesOperation(e,t,i,n){return this.http.post("/api/contacts/noteOperationsContacts",{contact_id:e,operation_id:1,message:t,title:i,color_code:n})}performGetNotes(e){return this.http.post("/api/contacts/noteOperationsContacts",{contact_id:e,operation_id:2})}performEditNotes(e,t,i,n,o){return this.http.post("/api/contacts/noteOperationsContacts",{operation_id:3,note_id:e,message:t,title:i,color_code:n,contact_id:o})}performDeleteNotes(e,t){return this.http.post("/api/contacts/noteOperationsContacts",{operation_id:4,contact_id:e,note_id:t})}getContactsDetailsById(e){return this.http.post("/api/contacts/getContactDetailsById",{contact_id:e})}getContactsOverview(e){return this.http.post("api/contacts/getContactDetailsById",{contact_id:e})}personaMaster(){return new Promise((e,t)=>{this.http.post("/api/salesMaster/contactPersona",{}).subscribe(t=>e(t),e=>t(e))})}getContactAttachments(e){return this.http.post("/api/contacts/getContactAttachments",{contact_id:e})}getCRMAttachmentsConfigs(){return new Promise((e,t)=>{this.http.get("/api/salesMaster/getCRMAttachmentsConfigs").subscribe(t=>e(t),e=>t(e))})}updateContactAttachment(e,t){return this.http.post("/api/contacts/updateContactAttachment",{contact_id:e,file:t})}deleteContactAttachment(e,t){return this.http.post("/api/contacts/deleteContactAttachment",{contact_id:e,file:t})}searchContact(e,t){return this.http.post("/api/contacts/ContactsSearch",{search_parameter:e,orgCodes:t})}deleteContact(e){return this.http.post("/api/contacts/deactivateContact",{contact_id:e})}responseType(){return new Promise((e,t)=>{this.http.post("/api/salesMaster/getResponseMaster",{}).subscribe(t=>e(t),e=>t(e))})}contactType(){return new Promise((e,t)=>{this.http.post("/api/salesMaster/getContactType",{}).subscribe(t=>e(t),e=>t(e))})}editCallLog(e,t){return new Promise((i,n)=>{this.http.post("/api/contacts/editCallLog",{activity_id:e,callLogFormDetails:t}).subscribe(e=>i(e),e=>n(e))})}createCallLog(e){return this.http.post("/api/contacts/createCallLog",{callLogFormDetails:e})}editMail(e,t){return new Promise((i,n)=>{this.http.post("/api/contacts/editMail",{activity_id:e,mailFormDetails:t}).subscribe(e=>i(e),e=>n(e))})}createMail(e){return this.http.post("/api/contacts/createMail",{mailFormDetails:e})}meetingTypeList(){return new Promise((e,t)=>{this.http.post("/api/salesMaster/getSalesActivityLocationType",{}).subscribe(t=>e(t),e=>t(e))})}locationList(){return new Promise((e,t)=>{this.http.post("/api/salesMaster/getActivityLocations",{}).subscribe(t=>e(t),e=>t(e))})}peopleList(){return new Promise((e,t)=>{this.http.post("/api/salesMaster/peopleList",{}).subscribe(t=>e(t),e=>t(e))})}editMeeting(e,t){return new Promise((i,n)=>{this.http.post("/api/contacts/editMeeting",{activity_id:e,meetingFormDetails:t}).subscribe(e=>i(e),e=>n(e))})}createMeeting(e){return this.http.post("/api/contacts/createMeeting",{meetingFormDetails:e})}editTask(e,t){return new Promise((i,n)=>{this.http.post("/api/contacts/editTask",{activity_id:e,taskFormDetails:t}).subscribe(e=>i(e),e=>n(e))})}createTask(e){return this.http.post("/api/contacts/createTask",{taskFormDetails:e})}activityList(e,t){return new Promise((i,n)=>{this.http.post("/api/contacts/getActivityList",{application_id:e,contact_id:t}).subscribe(e=>i(e),e=>n(e))})}searchContactActivity(e,t){return new Promise((i,n)=>{this.http.post("/api/contacts/searchContactActivity",{search_parameter:e,contact_id:t}).subscribe(e=>i(e),e=>n(e))})}getFullDetailOfActivity(e){return this.http.post("/api/contacts/getActivityDetail",{activity_id:e})}openActivity(e){return new Promise((t,i)=>{this.http.post("/api/contacts/openActivity",{activity_id:e}).subscribe(e=>t(e),e=>i(e))})}activityDetails(e){return new Promise((t,i)=>{this.http.post("/api/contacts/getActivityDetails",{activity_id:e}).subscribe(e=>(console.log("activitydetail",e),t(e)),e=>i(e))})}completeActivity(e,t){return new Promise((i,n)=>{this.http.post("/api/contacts/completeActivity",{activity_id:e,is_completed:t}).subscribe(e=>i(e),e=>n(e))})}startActivity(e){return new Promise((t,i)=>{this.http.post("/api/contacts/startActivity",{activity_id:e}).subscribe(e=>t(e),e=>i(e))})}taskFormDetails(e){return new Promise((t,i)=>{this.http.post("/api/activity/taskFormDetails",{activity_id:e}).subscribe(e=>t(e),e=>i(e))})}meetingFormDetails(e){return new Promise((t,i)=>{this.http.post("/api/activity/meetingFormDetails",{activity_id:e}).subscribe(e=>t(e),e=>i(e))})}callLogFormDetails(e){return new Promise((t,i)=>{this.http.post("/api/activity/callLogFormDetails",{activity_id:e}).subscribe(e=>t(e),e=>i(e))})}mailFormDetails(e){return new Promise((t,i)=>{this.http.post("/api/activity/mailFormDetails",{activity_id:e}).subscribe(e=>t(e),e=>i(e))})}deleteActivity(e){return new Promise((t,i)=>{this.http.post("/api/contacts/deleteActivity",{activity_id:e}).subscribe(e=>t(e),e=>i(e))})}updateActivityAssignedTo(e,t){return this.http.post("/api/contacts/editActivityAssignedTo",{activityId:e,oid:t})}updatePlannedHours(e,t){return this.http.post("api/contacts/updatePlannedHrsForContactAct",{activity_id:e,planned_hours:t})}updateJobTitle(e,t){return this.http.post("/api/contacts/updateJobTitle",{id:e,jobTitle:t})}updateEmail(e,t){return this.http.post("/api/contacts/updateEmail",{id:e,email:t})}updateMobileNumber(e,t){return this.http.post("/api/contacts/updateMobileNumber",{id:e,mobileNumber:t})}updatePersona(e,t){return this.http.post("/api/contacts/updatePersona",{id:e,personaId:t})}updateContactOwner(e,t,i){return this.http.post("/api/contacts/updateContactOwner",{id:e,name:t,ownerOid:i})}updateLinkedinProfile(e,t){return this.http.post("/api/contacts/updateLinkedinProfile",{id:e,linkedinProfile:t})}updateContactAddress(e,t){return this.http.post("/api/contacts/updateContactAddress",{id:e,address:t})}marketSegment(){return new Promise((e,t)=>{this.http.post("/api/contacts/getMarketSegment",{}).subscribe(t=>e(t),e=>t(e))})}updateMarketSegment(e,t){return this.http.post("/api/contacts/updateMarketSegment",{id:e,market_segment:t})}setContactDunningEmail(e,t){return this.http.post("/api/contacts/setContactAsDunning",{flag:e,contact_id:t})}getFormFieldCollection(){return new Promise((e,t)=>{this.http.post("/api/contacts/getFormFieldCollection",{}).subscribe(t=>{e(t)},e=>{t(e)})})}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275inject"](l.c),r["\u0275\u0275inject"](s.a))},e.\u0275prov=r["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},mux4:function(e,t,i){"use strict";i.r(t),i.d(t,"CallLogFormComponent",(function(){return U}));var n=i("fXoL"),o=i("3Pt+"),a=i("ofXK"),r=i("jtHE"),l=i("XNiG"),s=i("NJ67"),c=i("1G5W"),d=i("kmnG"),p=i("d3UM"),m=i("FKr1"),h=i("WJ5W");const u=["singleSelect"];function g(e,t){if(1&e){const e=n["\u0275\u0275getCurrentView"]();n["\u0275\u0275elementStart"](0,"mat-option",6),n["\u0275\u0275listener"]("click",(function(){n["\u0275\u0275restoreView"](e);const i=t.$implicit;return n["\u0275\u0275nextContext"]().emitChanges(i)})),n["\u0275\u0275text"](1),n["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;n["\u0275\u0275property"]("value",e.id),n["\u0275\u0275advance"](1),n["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let f=(()=>{class e extends s.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new o.j,this.fieldFilterCtrl=new o.j,this.list=[],this.required=!1,this.valueChange=new n.EventEmitter,this.disabled=!1,this.filteredList=new r.a,this.change=new n.EventEmitter,this._onDestroy=new l.b,this.emitChanges=e=>{this.change.emit(e)}}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(c.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(c.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){null!=this.list&&this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}}return e.\u0275fac=function(t){return new(t||e)(n["\u0275\u0275directiveInject"](n.Renderer2))},e.\u0275cmp=n["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search"]],viewQuery:function(e,t){if(1&e&&n["\u0275\u0275viewQuery"](u,!0),2&e){let e;n["\u0275\u0275queryRefresh"](e=n["\u0275\u0275loadQuery"]())&&(t.singleSelect=e.first)}},inputs:{list:"list",placeholder:"placeholder",required:"required",disabled:"disabled"},outputs:{valueChange:"valueChange",change:"change"},features:[n["\u0275\u0275ProvidersFeature"]([{provide:o.t,useExisting:Object(n.forwardRef)(()=>e),multi:!0}]),n["\u0275\u0275InheritDefinitionFeature"],n["\u0275\u0275NgOnChangesFeature"]],decls:11,vars:11,consts:[["appearance","outline"],[3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value"],[3,"value","click",4,"ngFor","ngForOf"],[3,"value","click"]],template:function(e,t){1&e&&(n["\u0275\u0275elementStart"](0,"mat-form-field",0),n["\u0275\u0275elementStart"](1,"mat-label"),n["\u0275\u0275text"](2),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](3,"mat-select",1,2),n["\u0275\u0275elementStart"](5,"mat-option"),n["\u0275\u0275element"](6,"ngx-mat-select-search",3),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](7,"mat-option",4),n["\u0275\u0275text"](8,"None"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275template"](9,g,2,2,"mat-option",5),n["\u0275\u0275pipe"](10,"async"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e&&(n["\u0275\u0275advance"](2),n["\u0275\u0275textInterpolate"](t.placeholder),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),n["\u0275\u0275advance"](3),n["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("value",null),n["\u0275\u0275advance"](2),n["\u0275\u0275property"]("ngForOf",n["\u0275\u0275pipeBind1"](10,9,t.filteredList)))},directives:[d.c,d.g,p.c,o.v,o.k,o.F,m.p,h.a,a.NgForOf],pipes:[a.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}"]}),e})();var v=i("mrSG"),C=i("Kj3r"),b=i("F97M"),y=i("XVR1"),S=i("qFsG"),w=i("/1cH"),x=i("NFeN");function E(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"mat-label"),n["\u0275\u0275text"](1),n["\u0275\u0275elementEnd"]()),2&e){const e=n["\u0275\u0275nextContext"]();n["\u0275\u0275advance"](1),n["\u0275\u0275textInterpolate"](e.label)}}function _(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"mat-option",7),n["\u0275\u0275elementStart"](1,"small"),n["\u0275\u0275text"](2),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,i=n["\u0275\u0275nextContext"]();n["\u0275\u0275property"]("hidden",!i.isAutocomplete)("value",e),n["\u0275\u0275advance"](2),n["\u0275\u0275textInterpolate2"]("",e.displayName," | ",e.mail,"")}}let L=(()=>{class e extends s.a{constructor(e,t){super(),this.graphApi=e,this._AppShareService=t,this.userSearchSubject=new l.b,this.isAutocomplete=!1,this.Formwidth="",this.showUserList=new n.EventEmitter,this.selectedUser=new n.EventEmitter,this.label="",this.blur=new n.EventEmitter,this.required=!1,this.fieldCtrl=new o.j,this.disabled=!1,this.isGraphApi=0,this._onDestroy=new l.b}ngOnInit(){this.userSearchSubject.pipe(Object(C.a)(600)).subscribe(e=>Object(v.c)(this,void 0,void 0,(function*(){if(console.log(e),this.isGraphApi)this.graphApi.getUserSuggestions(e).then(t=>Object(v.c)(this,void 0,void 0,(function*(){if(t&&t.length>0)this.showUserList.emit(t);else{let t=yield this.getUserSuggestionsFromDB(e);this.showUserList.emit(t)}})),t=>Object(v.c)(this,void 0,void 0,(function*(){console.log(t);let i=yield this.getUserSuggestionsFromDB(e);this.showUserList.emit(i)})));else{let t=yield this.getUserSuggestionsFromDB(e);this.showUserList.emit(t)}}))),this.fieldCtrl.valueChanges.pipe(Object(c.a)(this._onDestroy)).subscribe(e=>{console.log(e),e?(this.value=e.id,this.onChange(e.id)):(this.value="",this.onChange(""))})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}ngAfterViewInit(){}searchUser(e){if(!e)return this.graphApi.userSuggestions=[];this.userSearchSubject.next(e)}resetSuggestion(){this.graphApi.userSuggestions=[]}selectedOption(e){this.selectedUser.emit(e.option.value),this.value=e.option.value,this.blur.emit()}displayUserName(e){return e?e.displayName:""}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){return Object(v.c)(this,void 0,void 0,(function*(){if(console.log("inisde writevalue"),console.log(e),e)if(this.isGraphApi)this.graphApi.getUserProfile(e).then(t=>Object(v.c)(this,void 0,void 0,(function*(){if(t)this.fieldCtrl.setValue(t);else{let t=yield this.getUserProfileFromDB(e);this.fieldCtrl.setValue(t)}})));else{let t=yield this.getUserProfileFromDB(e);console.log(t),this.fieldCtrl.setValue(t)}else this.fieldCtrl.setValue("")}))}getUserSuggestionsFromDB(e){return Object(v.c)(this,void 0,void 0,(function*(){let t=yield this._AppShareService.getUserSuggestionsFromDB(e);return this.graphApi.userSuggestions=t.value,t.value}))}getUserProfileFromDB(e){return Object(v.c)(this,void 0,void 0,(function*(){let t=yield this._AppShareService.getUserProfileFromDB(e);return console.log(t),t&&t.length>0?t[0]:""}))}}return e.\u0275fac=function(t){return new(t||e)(n["\u0275\u0275directiveInject"](b.a),n["\u0275\u0275directiveInject"](y.a))},e.\u0275cmp=n["\u0275\u0275defineComponent"]({type:e,selectors:[["app-search-user"]],inputs:{isAutocomplete:"isAutocomplete",Formwidth:"Formwidth",label:"label",required:"required",disabled:"disabled",isGraphApi:"isGraphApi"},outputs:{showUserList:"showUserList",selectedUser:"selectedUser",blur:"blur"},features:[n["\u0275\u0275ProvidersFeature"]([{provide:o.t,useExisting:Object(n.forwardRef)(()=>e),multi:!0}]),n["\u0275\u0275InheritDefinitionFeature"]],decls:9,vars:9,consts:[["appearance","outline","width","Formwidth"],[4,"ngIf"],["matInput","",3,"matAutocomplete","placeholder","required","formControl","disabled","keyup","focus"],["matSuffix","",2,"color","#66615B !important","font-size","21px !important"],[3,"hidden","displayWith","optionSelected"],["auto","matAutocomplete"],[3,"hidden","value",4,"ngFor","ngForOf"],[3,"hidden","value"]],template:function(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"div"),n["\u0275\u0275elementStart"](1,"mat-form-field",0),n["\u0275\u0275template"](2,E,2,1,"mat-label",1),n["\u0275\u0275elementStart"](3,"input",2),n["\u0275\u0275listener"]("keyup",(function(e){return t.searchUser(e.target.value)}))("focus",(function(){return t.resetSuggestion()})),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](4,"mat-icon",3),n["\u0275\u0275text"](5,"person_pin"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](6,"mat-autocomplete",4,5),n["\u0275\u0275listener"]("optionSelected",(function(e){return t.selectedOption(e)})),n["\u0275\u0275template"](8,_,3,4,"mat-option",6),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e){const e=n["\u0275\u0275reference"](7);n["\u0275\u0275advance"](2),n["\u0275\u0275property"]("ngIf",t.label),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("matAutocomplete",e)("placeholder",t.label)("required",t.required)("formControl",t.fieldCtrl)("disabled",t.disabled),n["\u0275\u0275advance"](3),n["\u0275\u0275property"]("hidden",!t.isAutocomplete)("displayWith",t.displayUserName),n["\u0275\u0275advance"](2),n["\u0275\u0275property"]("ngForOf",t.graphApi.userSuggestions)}},directives:[d.c,a.NgIf,S.b,w.d,o.e,o.F,o.v,o.k,x.a,d.i,w.b,a.NgForOf,d.g,m.p],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding-bottom:8px!important}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-underline{bottom:0}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-infix{padding:.65em 0!important;border-top:.54375em solid transparent!important;font-size:13px}.mat-form-field[_ngcontent-%COMP%]     .mat-select-arrow{margin:0 4px!important}"]}),e})();var F=i("dNgK"),O=i("bTqV"),I=(i("iadO"),i("0IaG")),P=i("9jeV"),M=i("VsNQ"),D=i("XXEo"),A=i("ihCf");function k(e,t){if(1&e){const e=n["\u0275\u0275getCurrentView"]();n["\u0275\u0275elementStart"](0,"button",28),n["\u0275\u0275listener"]("click",(function(){return n["\u0275\u0275restoreView"](e),n["\u0275\u0275nextContext"]().createCallLog()})),n["\u0275\u0275elementStart"](1,"mat-icon"),n["\u0275\u0275text"](2," done_all"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()}}function j(e,t){if(1&e){const e=n["\u0275\u0275getCurrentView"]();n["\u0275\u0275elementStart"](0,"button",29),n["\u0275\u0275listener"]("click",(function(){return n["\u0275\u0275restoreView"](e),n["\u0275\u0275nextContext"]().editCallLog()})),n["\u0275\u0275elementStart"](1,"mat-icon"),n["\u0275\u0275text"](2," done_all"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()}}let U=(()=>{class e{constructor(e,t,i,a,r,l,s,c){this.dialogRef=e,this.dialogData=t,this.dialog=i,this.fb=a,this.activityToMainService=r,this.snackBar=l,this.contactService=s,this.loginService=c,this.close=new n.EventEmitter,this.currentUser=this.loginService.getProfile().profile,this.callLogForm=this.fb.group({title:["",o.H.required],description:[""],contactType:["",o.H.required],contactedPerson:[""],response:["",o.H.required],contactedBy:["",o.H.required],notes:[""],dueDate:[""],remindMe:[""],applicationName:[""],contactId:[""],applicationId:[""],governanceType:[""]}),this.editCallLog=()=>{this.callLogForm.valid?this.contactService.editCallLog(this.activityId,this.callLogForm.value).then(e=>{this.callLogForm.reset(),this.snackBar.open("Call Log edited Successfully!","Dismiss",{duration:2e3}),this.close.emit("update required"),this.dialogRef.close("Update Required"),this.activityToMainService.sendMsg("reload")},e=>{this.snackBar.open("Failed to edit call log.","Dismiss",{duration:2e3}),console.error(e)}):this.snackBar.open("Enter all Mandatory Fields!","Dismiss",{duration:2e3})},this.createCallLog=()=>{this.callLogForm.valid?this.contactService.createCallLog(this.callLogForm.value).subscribe(e=>{this.callLogForm.reset(),this.snackBar.open("Call Log Created Successfully!","Dismiss",{duration:2e3}),this.close.emit("close"),this.dialogRef.close("Update Required"),this.activityToMainService.sendMsg("reload"),console.log(e)},e=>{this.snackBar.open("Failed to create call log.","Dismiss",{duration:2e3}),console.error(e)}):this.snackBar.open("Enter all Mandatory Fields!","Dismiss",{duration:2e3})},this.closeClicked=()=>{this.close.emit(),this.dialogRef.close()}}ngOnChanges(){}updateFormWithCallLogDetails(){if(console.log(this.mode,this.activityId),"Edit"==this.mode){let e=this.data?this.data:null;console.log(e),e&&this.callLogForm.patchValue({title:e.title,description:e.description,contactType:e.activity_sub_type_id,contactedPerson:e.contacted_person,response:e.response_type_id,contactedBy:e.contacted_by,governanceType:e.governance_activity_id,notes:e.notes?e.notes[0].notes:e.notes,remindMe:e.reminder})}else this.callLogForm.reset()}ngOnInit(){let e;this.mode=this.dialogData.mode,this.activityId=this.dialogData.activityId,this.data=this.dialogData.data,"Edit"==this.mode&&this.updateFormWithCallLogDetails(),this.contactService.contactType().then(e=>{this.contactTypeList=e}),this.contactService.responseType().then(e=>{this.responseMasterList=e}),this.contactService.getContactGovernanceType(34).subscribe(e=>{this.contactGovernanceTypes=e},e=>{console.log(e)}),"contacts"==window.location.pathname.split("/")[2]&&(e=34),this.callLogForm.patchValue({applicationName:window.location.pathname.split("/")[2],contactId:window.location.pathname.split("/")[3],applicationId:e,contactedBy:this.currentUser.oid,governanceType:46,contactType:5})}}return e.\u0275fac=function(t){return new(t||e)(n["\u0275\u0275directiveInject"](I.h),n["\u0275\u0275directiveInject"](I.a),n["\u0275\u0275directiveInject"](I.b),n["\u0275\u0275directiveInject"](o.i),n["\u0275\u0275directiveInject"](P.a),n["\u0275\u0275directiveInject"](F.a),n["\u0275\u0275directiveInject"](M.a),n["\u0275\u0275directiveInject"](D.a))},e.\u0275cmp=n["\u0275\u0275defineComponent"]({type:e,selectors:[["app-call-log-form"]],outputs:{close:"close"},features:[n["\u0275\u0275NgOnChangesFeature"]],decls:51,vars:7,consts:[[1,"container","createCallLogStyles"],[3,"formGroup"],[1,"row","border-bottom","solid"],[1,"col-11","pt-2","createAccount"],[1,"col-1","d-flex"],["mat-icon-button","",1,"ml-auto","close-button",3,"click"],[1,"close-Icon"],[1,"row","pt-2"],[1,"col-8","pl-0","pr-0"],[1,"row"],[1,"col-12"],["appearance","outline",1,"create-account-field","title"],["matInput","","placeholder","title *","formControlName","title"],["matInput","","placeholder","Description","formControlName","description"],["required","true","placeholder","Contact Type","formControlName","contactType",1,"create-account-field","title",3,"list"],[1,"row","pt-1"],["matInput","","placeholder","Contacted person","required","true","formControlName","contactedPerson"],["required","true","placeholder","Response","formControlName","response",1,"create-account-field","title",3,"list"],["label","Contacted By","required","true","formControlName","contactedBy",1,"contact-person",3,"isAutocomplete"],["appearance","outline",1,"textArea",2,"width","28rem","font-size","14px !important"],["matInput","","cdkTextareaAutosize","","cdkAutosizeMinRows","4","cdkAutosizeMaxRows","","placeholder","Notes","formControlName","notes"],["autosize","cdkTextareaAutosize"],[1,"col-4"],[1,"row","pt-3"],[1,"col-9","pl-4","quotes"],[1,"col-3"],["mat-icon-button","","class","iconbtn ml-2 mt-0","matTooltip","Create Call Log","type","submit",3,"click",4,"ngIf"],["mat-icon-button","","class","iconbtn ml-2 mt-0","matTooltip","Edit Call Log","type","submit",3,"click",4,"ngIf"],["mat-icon-button","","matTooltip","Create Call Log","type","submit",1,"iconbtn","ml-2","mt-0",3,"click"],["mat-icon-button","","matTooltip","Edit Call Log","type","submit",1,"iconbtn","ml-2","mt-0",3,"click"]],template:function(e,t){1&e&&(n["\u0275\u0275elementStart"](0,"div",0),n["\u0275\u0275elementStart"](1,"form",1),n["\u0275\u0275elementStart"](2,"div",2),n["\u0275\u0275elementStart"](3,"div",3),n["\u0275\u0275text"](4),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](5,"div",4),n["\u0275\u0275elementStart"](6,"button",5),n["\u0275\u0275listener"]("click",(function(){return t.closeClicked()})),n["\u0275\u0275elementStart"](7,"mat-icon",6),n["\u0275\u0275text"](8,"close"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](9,"div",7),n["\u0275\u0275elementStart"](10,"div",8),n["\u0275\u0275elementStart"](11,"div",9),n["\u0275\u0275elementStart"](12,"div",10),n["\u0275\u0275elementStart"](13,"mat-form-field",11),n["\u0275\u0275elementStart"](14,"mat-label"),n["\u0275\u0275text"](15,"Title *"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275element"](16,"input",12),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](17,"div",9),n["\u0275\u0275elementStart"](18,"div",10),n["\u0275\u0275elementStart"](19,"mat-form-field",11),n["\u0275\u0275elementStart"](20,"mat-label"),n["\u0275\u0275text"](21,"Description"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275element"](22,"input",13),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](23,"div",9),n["\u0275\u0275elementStart"](24,"div",10),n["\u0275\u0275element"](25,"app-input-search",14),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](26,"div",15),n["\u0275\u0275elementStart"](27,"div",10),n["\u0275\u0275elementStart"](28,"mat-form-field",11),n["\u0275\u0275elementStart"](29,"mat-label"),n["\u0275\u0275text"](30,"Contacted person "),n["\u0275\u0275elementEnd"](),n["\u0275\u0275element"](31,"input",16),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](32,"div",9),n["\u0275\u0275elementStart"](33,"div",10),n["\u0275\u0275element"](34,"app-input-search",17),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](35,"div",15),n["\u0275\u0275elementStart"](36,"div",10),n["\u0275\u0275element"](37,"app-search-user",18),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](38,"div",15),n["\u0275\u0275elementStart"](39,"div",10),n["\u0275\u0275elementStart"](40,"mat-form-field",19),n["\u0275\u0275element"](41,"textarea",20,21),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275element"](43,"div",22),n["\u0275\u0275elementEnd"](),n["\u0275\u0275element"](44,"div",23),n["\u0275\u0275elementStart"](45,"div",23),n["\u0275\u0275elementStart"](46,"div",24),n["\u0275\u0275text"](47,' "In the Middle of difficulty, lies Opportunity" '),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](48,"div",25),n["\u0275\u0275template"](49,k,3,0,"button",26),n["\u0275\u0275template"](50,j,3,0,"button",27),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e&&(n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("formGroup",t.callLogForm),n["\u0275\u0275advance"](3),n["\u0275\u0275textInterpolate1"]("",t.mode," call log"),n["\u0275\u0275advance"](21),n["\u0275\u0275property"]("list",t.contactTypeList),n["\u0275\u0275advance"](9),n["\u0275\u0275property"]("list",t.responseMasterList),n["\u0275\u0275advance"](3),n["\u0275\u0275property"]("isAutocomplete",!0),n["\u0275\u0275advance"](12),n["\u0275\u0275property"]("ngIf","Edit"!=t.mode),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf","Edit"==t.mode))},directives:function(){return[o.J,o.w,o.n,O.a,x.a,d.c,d.g,S.b,o.e,o.v,o.l,f,o.F,L,A.b,a.NgIf]},styles:[".createCallLogStyles[_ngcontent-%COMP%]{background-image:url(callLog.1c9f524a019fcbfa6980.png);background-size:253px 200px;background-repeat:no-repeat;overflow-y:hidden!important;background-position:90% 70%;min-height:95vh}.createCallLogStyles[_ngcontent-%COMP%]   .createAccount[_ngcontent-%COMP%]{font-size:14px;color:#cf0001;font-family:Roboto;font-weight:500}.createCallLogStyles[_ngcontent-%COMP%]   .create-account-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%!important;padding-top:4px}.createCallLogStyles[_ngcontent-%COMP%]   .vip-contact[_ngcontent-%COMP%]{color:#1a1a1a;font-size:14px}.createCallLogStyles[_ngcontent-%COMP%]   .check-box-text[_ngcontent-%COMP%]{color:#66615b;font-size:14px}.createCallLogStyles[_ngcontent-%COMP%]   .quotes[_ngcontent-%COMP%]{font-style:italic;font-weight:500;color:#66615b;font-size:14px}.createCallLogStyles[_ngcontent-%COMP%]   .iconbtn[_ngcontent-%COMP%]{background-color:#c92020;color:#fff;padding:0;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.createCallLogStyles[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]     .mat-form-field-wrapper{width:28rem}.createCallLogStyles[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]     .mat-form-field-wrapper .mat-form-field-flex{height:40px}.createCallLogStyles[_ngcontent-%COMP%]   .contact-person[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-flex{padding:0 .75em;margin-top:-.25em;position:relative;width:28rem;height:40px}.createCallLogStyles[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.createCallLogStyles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}"]}),e})()}}]);