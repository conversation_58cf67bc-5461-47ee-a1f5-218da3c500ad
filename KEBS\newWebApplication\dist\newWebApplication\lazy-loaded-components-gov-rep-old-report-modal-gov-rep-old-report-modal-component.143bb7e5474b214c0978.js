(window.webpackJsonp=window.webpackJsonp||[]).push([[753],{FoS8:function(e,t,i){"use strict";i.r(t),i.d(t,"GovRepOldReportModalComponent",(function(){return Q}));var n=i("mrSG"),a=i("fXoL"),o=i("0IaG"),l=i("R0Ic"),r=i("qD4H"),s=i("ClZT"),d=i("VI6+"),c=i("ySCK"),u=i("xG9w"),h=i("wd/R"),m=i("3Pt+"),p=i("ofXK"),g=i("bTqV"),f=i("Xa2L"),w=i("NFeN"),v=i("TU8p"),S=i("kmnG"),y=i("qFsG"),C=i("STbY"),F=i("WYlB"),b=(i("XPKZ"),i("8hBH")),x=i("YhS8"),D=i("Qu3c"),V=i("Vpr3"),E=i("DIFc"),O=i("G7BD"),k=i("dNgK"),I=i("XXEo"),R=i("hpGE"),_=i("LcQX"),T=i("GnQ3"),P=i("6t9p"),M=i("PVOt");const N=["drillDownDataGrid"],B=["cardScroll"];function Y(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"div",11),a["\u0275\u0275element"](1,"mat-progress-spinner",12),a["\u0275\u0275elementEnd"]())}function z(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"button",59),a["\u0275\u0275listener"]("click",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"](2).scrollLeft()})),a["\u0275\u0275elementStart"](1,"mat-icon",60),a["\u0275\u0275text"](2,"chevron_left"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()}}function j(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"mat-icon",63),a["\u0275\u0275listener"]("click",(function(){a["\u0275\u0275restoreView"](e);const t=a["\u0275\u0275nextContext"]().index;return a["\u0275\u0275nextContext"](2).deleteVariant(t)})),a["\u0275\u0275text"](1," delete"),a["\u0275\u0275elementEnd"]()}}function A(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"button",61),a["\u0275\u0275listener"]("click",(function(){a["\u0275\u0275restoreView"](e);const i=t.index;return a["\u0275\u0275nextContext"](2).changeView(i)})),a["\u0275\u0275text"](1),a["\u0275\u0275template"](2,j,2,0,"mat-icon",62),a["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,i=t.index,n=a["\u0275\u0275nextContext"](2);a["\u0275\u0275property"]("ngClass",i==n.currentView?"btn-active my-2 version-button":"btn-not-active my-2 version-button"),a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate1"](" ",null==e?null:e.config_name," "),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",e.visibleDeleteView)}}function U(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"button",64),a["\u0275\u0275listener"]("click",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"](2).scrollRight()})),a["\u0275\u0275elementStart"](1,"mat-icon",60),a["\u0275\u0275text"](2," chevron_right"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()}}function L(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"mat-error"),a["\u0275\u0275text"](1,"Name is required"),a["\u0275\u0275elementEnd"]())}function G(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div"),a["\u0275\u0275elementStart"](1,"div",65),a["\u0275\u0275text"](2),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate"](e.text)}}const W=function(){return{applyLabel:"Apply",format:"DD-MM-YYYY",displayFormat:"DD-MMM-YYYY",customRangeLabel:"Custom Range"}};function H(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"div",13),a["\u0275\u0275elementStart"](1,"div",14),a["\u0275\u0275elementStart"](2,"div",15),a["\u0275\u0275elementStart"](3,"div",16),a["\u0275\u0275elementStart"](4,"div",17),a["\u0275\u0275elementStart"](5,"div",18),a["\u0275\u0275elementStart"](6,"div",19),a["\u0275\u0275elementStart"](7,"div",20),a["\u0275\u0275elementStart"](8,"input",21),a["\u0275\u0275listener"]("ngModelChange",(function(t){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"]().daterange=t}))("change",(function(t){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"]().selectedDate(t)})),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](9,z,3,0,"button",22),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](10,"div",23,24),a["\u0275\u0275template"](12,A,3,3,"button",25),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](13,U,3,0,"button",26),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](14,"div",27),a["\u0275\u0275elementStart"](15,"button",28),a["\u0275\u0275elementStart"](16,"mat-icon",29),a["\u0275\u0275text"](17,"create"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](18,"mat-menu",30,31),a["\u0275\u0275elementStart"](20,"div",32),a["\u0275\u0275elementStart"](21,"div",33),a["\u0275\u0275elementStart"](22,"div",16),a["\u0275\u0275elementStart"](23,"div",34),a["\u0275\u0275elementStart"](24,"button",35),a["\u0275\u0275listener"]("click",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"]().showDataFieldsFn()})),a["\u0275\u0275elementStart"](25,"mat-icon",29),a["\u0275\u0275text"](26,"menu_open"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](27,"button",36),a["\u0275\u0275listener"]("click",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"]().showRowFieldsFn()})),a["\u0275\u0275elementStart"](28,"mat-icon",29),a["\u0275\u0275text"](29,"menu_open"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](30,"button",37),a["\u0275\u0275listener"]("click",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"]().showColumnFieldsFn()})),a["\u0275\u0275elementStart"](31,"mat-icon",29),a["\u0275\u0275text"](32,"view_column"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](33,"button",38),a["\u0275\u0275listener"]("click",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"]().showFilterFieldsFn()})),a["\u0275\u0275elementStart"](34,"mat-icon",29),a["\u0275\u0275text"](35,"filter_list"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](36,"button",39),a["\u0275\u0275listener"]("click",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"]().stateUpdate()})),a["\u0275\u0275elementStart"](37,"mat-icon",40),a["\u0275\u0275text"](38,"save"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](39,"button",41),a["\u0275\u0275listener"]("click",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275reference"](43).toggle()})),a["\u0275\u0275elementStart"](40,"mat-icon",40),a["\u0275\u0275text"](41,"move_to_inbox"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](42,"sat-popover",42,43),a["\u0275\u0275elementStart"](44,"div",44),a["\u0275\u0275elementStart"](45,"div",16),a["\u0275\u0275elementStart"](46,"mat-form-field"),a["\u0275\u0275element"](47,"input",45),a["\u0275\u0275template"](48,L,2,0,"mat-error",46),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](49,"div",16),a["\u0275\u0275elementStart"](50,"button",47),a["\u0275\u0275listener"]("click",(function(){a["\u0275\u0275restoreView"](e);const t=a["\u0275\u0275reference"](43);return a["\u0275\u0275nextContext"]().saveState(),t.toggle()})),a["\u0275\u0275text"](51," Save "),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](52,"dx-button",48),a["\u0275\u0275listener"]("onClick",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"]().reset()})),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](53,"dx-button",49),a["\u0275\u0275listener"]("onClick",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"]().refresh()})),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](54,"dx-button",50),a["\u0275\u0275listener"]("onClick",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"]().pivotGrid1.getFieldChooserPopup().show()})),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](55,"dx-button",51),a["\u0275\u0275listener"]("onClick",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"]().pivotGrid1.exportToExcel()})),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](56,"dx-pivot-grid",52),a["\u0275\u0275listener"]("onCellPrepared",(function(t){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"]().onCellPrepared(t)}))("onInitialized",(function(t){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"]().onInitialized(t)}))("onExporting",(function(t){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"]().onExporting(t)})),a["\u0275\u0275element"](57,"dxo-export",53),a["\u0275\u0275element"](58,"dxo-field-chooser",54),a["\u0275\u0275element"](59,"dxo-header-filter",55),a["\u0275\u0275element"](60,"dxo-field-panel",56),a["\u0275\u0275element"](61,"dxo-state-storing",57),a["\u0275\u0275template"](62,G,3,1,"div",58),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()}if(2&e){const e=a["\u0275\u0275reference"](19),t=a["\u0275\u0275reference"](43),i=a["\u0275\u0275nextContext"]();a["\u0275\u0275advance"](8),a["\u0275\u0275property"]("showCustomRangeLabel",!0)("locale",a["\u0275\u0275pureFunction0"](46,W))("alwaysShowCalendars",!0)("showCancel",!0)("ranges",i.dateRangePickerRanges)("linkedCalendars",!0)("ngModel",i.daterange),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",(null==i.views||null==i.views.activeView?null:i.views.activeView.length)>0),a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("ngForOf",null==i.views?null:i.views.allViews),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",(null==i.views||null==i.views.activeView?null:i.views.activeView.length)>0),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("matMenuTriggerFor",e),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("matBadgeHidden",i.displayView),a["\u0275\u0275advance"](9),a["\u0275\u0275property"]("matBadgeHidden",i.showDataFields),a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("matBadgeHidden",i.showRowFields),a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("matBadgeHidden",i.showColumnFields),a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("matBadgeHidden",i.showFilterFields),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("disabled",0==(null==i.views||null==i.views.allViews?null:i.views.allViews.length)),a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("satPopoverAnchor",t),a["\u0275\u0275advance"](8),a["\u0275\u0275property"]("formControl",i.versionName),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",i.versionName.hasError("required")),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("disabled",!i.versionName.value),a["\u0275\u0275advance"](6),a["\u0275\u0275property"]("allowSortingBySummary",!0)("allowSorting",!0)("allowFiltering",!0)("allowExpandAll",!0)("showBorders",!0)("dataSource",i.sgSource)("wordWrapEnabled",!0)("height","100%")("showColumnTotals",!1)("showColumnGrandTotals",!1)("showRowTotals",!1),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("enabled",!1),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("enabled",!1)("allowSearch",!0),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("allowSearch",i.allowSearch)("width",300)("height",400),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("showDataFields",i.showDataFields)("showRowFields",i.showRowFields)("showColumnFields",i.showColumnFields)("showFilterFields",i.showFilterFields)("allowFieldDragging",!0)("visible",!0),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("enabled",!0),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("dxTemplateOf","cellTemplate")}}let Q=(()=>{class e{constructor(e,t,i,n,a,o,l,r,s,d){this.dialogRef=e,this.reportsService=t,this.governanceReportService=i,this.governanceReportSubService=n,this.snackBar=a,this.authService=o,this.userExperience=l,this.utilityService=r,this.udrfService=s,this.inData=d,this.appAppliedConfig=null,this.applicationId=374,this.currentUser={},this.udrfData={applicationId:this.applicationId,currentUserOId:"",appliedConfig:{defaultCurrency:"USD"}},this.showDataFields=!0,this.showRowFields=!0,this.showColumnFields=!0,this.showFilterFields=!0,this.displayView=!0,this.salesPopupVisible=!1,this.allowSearch=!0,this.salesPopupTitle="",this.sgDataSet=[],this.isLoaded=!1,this.versionName=new m.j("",[m.H.required]),this.daterange={},this.dateRangePickerRanges={},this.loadState=()=>{},this.convertToLocalTime=e=>{let t=new Date(e),i=6e4*t.getTimezoneOffset();return t.setTime(t.getTime()-i),t},this.reportsService.getMisFlags().subscribe(e=>{"S"==e.messType?(this.misFlags=e.misFlags,this.currentUser=this.authService.getProfile().profile,this.getAppUdrfConfig(n),this.updateMisFlags(),this.isLoaded=!0):(this.isLoaded=!0,this.utilityService.showErrorMessage(e,"KEBS"))},e=>{this.isLoaded=!0,this.utilityService.showErrorMessage(e,"KEBS")})}getAppUdrfConfig(e){this.udrfData.appliedConfig.defaultCurrency=this.inData.defaultCurrency,this.udrfService.udrfData.appliedConfig.customFields.isFullValue=this.inData.isFullValue,this.udrfData.currentUserOId=this.currentUser.oid,this.sourceApi(e)}getUserUdrfConfig(e){this.udrfData.currentUserOId=this.currentUser.oid,this.udrfService.getUdrfUserConfig(this.udrfData).subscribe(t=>Object(n.c)(this,void 0,void 0,(function*(){"S"==t.messType&&t.data.length>0?(this.udrfData.appliedConfig=t.data[0].applied_config?JSON.parse(t.data[0].applied_config):{},this.udrfData.appliedConfig.defaultCurrency||(this.udrfData.appliedConfig.defaultCurrency=this.misFlags.defaultCurrency)):this.udrfData.appliedConfig=this.appAppliedConfig,this.sourceApi(e)})),e=>{this.utilityService.showErrorMessage(e,"KEBS")})}sourceApi(e){this.dateRangePickerRanges=this.utilityService.dateRangePickerRanges,this.start_date=this.inData.currDate?h(this.inData.currDate).startOf("month"):h().startOf("month"),this.end_date=this.inData.currDate?h(this.inData.currDate).endOf("month"):h().endOf("month"),this.daterange={startDate:h(this.start_date).format("DD-MM-YYYY"),endDate:h(this.end_date).format("DD-MM-YYYY")},this.refreshButtonOptions={icon:"refresh",onClick:()=>{this.refresh()}},this.sgSource=new r.a({fields:[{caption:"P & L",width:120,dataField:"pl",dataType:"string",area:"filter"},{caption:"Sub P & L",width:120,dataField:"sub_pl",dataType:"string",area:"filter",selector:e=>e.pl==e.sub_pl?"":e.sub_pl},{caption:"P & L Id",width:120,dataField:"pl_id",dataType:"string"},{caption:"Vertical/Cluster Id",width:120,dataField:"plId",dataType:"string"},{caption:"Vertical",width:120,dataField:"vertical",dataType:"string",area:"row"},{caption:"Customer name",dataField:"customerName",dataType:"string",width:150,area:"row"},{caption:"Portfolio/Opportunity name",dataField:"projectName",dataType:"string",width:150,area:"row"},{caption:"Opportunity Code",dataField:"opportunityCode",dataType:"string",width:150,area:"row"},{caption:"Project name",dataType:"string",dataField:"itemName",width:150,area:"filter"},{caption:"Milestone name",dataField:"milestoneName",width:150,area:"filter"},{caption:"Milestone Status",dataField:"milestoneStatusName",dataType:"string",width:150,area:"filter"},{caption:"Sales Org.",dataField:"salesOrganization",dataType:"string",width:150,area:"filter"},{dataField:"date",dataType:"date",area:"filter",groupInterval:"year",expanded:!0},{caption:"Type Description",dataField:"typeName",area:"column",expanded:!0},{dataField:"subType",area:"column",dataType:"string",sortOrder:"desc",expanded:!0},{caption:"Month",dataField:"date",dataType:"date",groupInterval:"month",expanded:!0,area:"column"},{caption:"Week",dataField:"date",dataType:"date",groupInterval:"day",area:"filter",selector:e=>{let t=h(e.date);return"week "+h(t).week()}},{caption:"Date",dataField:"date",dataType:"date",groupInterval:"day",expanded:!0,area:"filter",selector:function(e){return h(e.date).format("DD-MMM-YYYY")}},{caption:"Scenario",dataField:"color_scheme",dataType:"string",area:"filter",selector:function(e){return e&&e.color_scheme?e.color_scheme.scenario:"Intended to close in current month"}},{caption:"Milestone Type",dataField:"milestoneType",dataType:"string",area:"filter",expanded:!0},{caption:"Project Code",dataField:"profitCenter",dataType:"string",area:"filter",expanded:!0},{caption:this.udrfData.appliedConfig.defaultCurrency?"Total in "+this.udrfData.appliedConfig.defaultCurrency:"Total",dataField:"value",dataType:"number",summaryType:"sum",format:{formatter:e=>Number(e).toFixed(2),parser:e=>Number(e)},customizeText:e=>{let t=e.value?Number(e.value):0;if(this.misFlags.unitInSymbol){if("INR"==this.udrfData.appliedConfig.defaultCurrency){let e=this.utilityService.getValueWithComma(this.udrfService.udrfData.appliedConfig.customFields.isFullValue,t,"INR","Ngr",this.misFlags);return"-"==e?"0.00":e}if("USD"==this.udrfData.appliedConfig.defaultCurrency){let e=this.utilityService.getValueWithComma(this.udrfService.udrfData.appliedConfig.customFields.isFullValue,t,"USD","Ngr",this.misFlags);return"-"==e?"0.00":e}{let e=this.utilityService.getValueWithComma(this.udrfService.udrfData.appliedConfig.customFields.isFullValue,t,"INR","Ngr",this.misFlags);return"-"==e?"0.00":e}}if("INR"==this.udrfData.appliedConfig.defaultCurrency){let e=this.utilityService.getValueWithComma(this.udrfService.udrfData.appliedConfig.customFields.isFullValue,t,"INR","Ngr",this.misFlags);return"-"==e?"0.00":e}if("USD"==this.udrfData.appliedConfig.defaultCurrency){let e=this.utilityService.getValueWithComma(this.udrfService.udrfData.appliedConfig.customFields.isFullValue,t,"USD","Ngr",this.misFlags);return"-"==e?"0.00":e}{let e=this.utilityService.getValueWithComma(this.udrfService.udrfData.appliedConfig.customFields.isFullValue,t,"INR","Ngr",this.misFlags);return"-"==e?"0.00":e}},area:"data"},{caption:"Sales Owner",dataField:"salesOwnerName",dataType:"string",width:150,area:"filter"},{caption:"Sales Owner A.ID",dataField:"salesOwnerAid",dataType:"string",width:150,area:"filter"}],store:new c.a({load:(function(){let t={startLimit:{date:h(this.start_date).utc().add(h().utcOffset(),"minutes").format("YYYY-MM-DD"),year:h(this.start_date).year(),week:h(this.start_date).week()},endLimit:{date:h(this.end_date).utc().add(h().utcOffset(),"minutes").format("YYYY-MM-DD"),year:h(this.end_date).year(),week:h(this.end_date).week()},defaultCurrency:this.udrfData.appliedConfig.defaultCurrency};return e.getSundayGovPivotData(t).toPromise().then(e=>{this.isLoaded=!0;let t=[],i=u.where(this.modalParams.userRolePlIds,{object_id:168});return i&&i.length>0&&(t=i[0].pl_ids),"ALL"!=t&&(e.data=t.length>0?u.filter(e.data,(function(e){if(e.pl_id&&u.contains(t,e.pl_id)||e.sub_pl_id&&u.contains(t,e.sub_pl_id)||e.vertical_id&&u.contains(t,e.vertical_id))return e}),this):[]),e},this)}).bind(this)})})}closeModal(){this.dialogRef.close({event:"Close"})}selectedDate(e){e.startDate&&(this.start_date=h(e.startDate).startOf("day"),this.end_date=h(e.endDate).endOf("day"),this.refresh(),this.daterange={startDate:h(this.start_date).format("DD-MM-YYYY"),endDate:h(this.end_date).format("DD-MM-YYYY")})}onInitialized(e){this.pivotGrid1=e.component}onExporting(e){}refresh(){this.pivotGrid1.getDataSource().reload()}reset(){this.pivotGrid1.getDataSource().state({})}onCellPrepared(e){if(e.component.getDataSource(),"0"==e.cell.value&&(e.cellElement.style.backgroundColor="#ff9999"),(this.utilityService.checkIfStringExistsInArrayOfStrings(e.cell.columnPath,"Collection")||this.utilityService.checkIfStringExistsInArrayOfStrings(e.cell.columnPath,"Payment")||this.utilityService.checkIfStringExistsInArrayOfStrings(e.cell.columnPath,"Billing")||this.utilityService.checkIfStringExistsInArrayOfStrings(e.cell.columnPath,"Invoice")||this.utilityService.checkIfStringExistsInArrayOfStrings(e.cell.columnPath,"OBV")||this.utilityService.checkIfStringExistsInArrayOfStrings(e.cell.columnPath,"Opportunity"))&&this.utilityService.checkIfStringExistsInArrayOfStrings(e.cell.columnPath,"Actual")&&e.cell.value&&e.cell.value>0&&(e.cellElement.style.backgroundColor="#00ff55"),"data"==e.area&&e.cell.value&&null!=e.cell.value){let t,i=0;i=e.cell.value,e.cellElement.setAttribute("title",this.misFlags.unitInSymbol?"INR"==this.udrfData.appliedConfig.defaultCurrency?"\u20b9 "+this.utilityService.getValueTooltipWithComma(i,"INR","Ngr",this.misFlags):"USD"==this.udrfData.appliedConfig.defaultCurrency?"$ "+this.utilityService.getValueTooltipWithComma(i,"USD","Ngr",this.misFlags):this.utilityService.getValueTooltipWithComma(i,"INR","Ngr",this.misFlags):"INR"==this.udrfData.appliedConfig.defaultCurrency?this.utilityService.getValueTooltipWithComma(i,"INR","Ngr",this.misFlags)+" INR":"USD"==this.udrfData.appliedConfig.defaultCurrency?this.utilityService.getValueTooltipWithComma(i,"USD","Ngr",this.misFlags)+" USD":this.utilityService.getValueTooltipWithComma(i,"INR","Ngr",this.misFlags));for(let n=0;n<e.cell.rowPath.length;n++)u.contains(u.pluck(this.governanceReportSubService.getStatusList(),"name"),e.cell.rowPath[n])&&(t=n);if(e.cell.rowPath.length==t+1){let i=e.cell.rowPath[t],n=e.cell.columnPath[0];"Collection"==n&&"Billed"==i&&(e.cellElement.style.backgroundColor="none"),"Collection"==n&&"Partial Payment"==i&&(e.cellElement.style.backgroundColor="#ffff4d"),"Collection"==n&&"Payment Recieved"==i&&(e.cellElement.style.backgroundColor="#ffc266"),"Billing"!=n&&"Invoice"!=n||"Billed"!=i&&"Partial Payment"!=i&&"Payment Recieved"!=i||(e.cellElement.style.backgroundColor="#00ff55")}}}ngOnInit(){return Object(n.c)(this,void 0,void 0,(function*(){this.modalParams=this.inData.modalParams,this.start_date=this.inData.currDate?this.inData.currDate:this.start_date,this.end_date=this.inData.currDate?this.inData.currDate:this.end_date,yield this.getConfigData()}))}setFieldChoosers(e){this.showDataFields=e.showDataFields,this.showRowFields=e.showRowFields,this.showColumnFields=e.showColumnFields,this.showFilterFields=e.showFilterFields}updateMisFlags(){this.misFlags.divByDuringRetrievalForInrNgr=this.udrfService.udrfData.appliedConfig.customFields.isFullValue?1:1e7,this.misFlags.divByDuringRetrievalForUsdNgr=this.udrfService.udrfData.appliedConfig.customFields.isFullValue?1:1e6}getConfigData(){return Object(n.c)(this,void 0,void 0,(function*(){yield this.governanceReportSubService.getSundayReportViews().subscribe(e=>{if(this.views=e,this.views.activeView.length>0&&this.views){for(let e=0;e<this.views.allViews.length;e++)this.views.allViews[e].visibleDeleteView=!1;this.enableDisplayView(),this.currentView=0,localStorage.setItem("Report_374",this.views.activeView[0].saved_config),this.pivotGrid1.getDataSource().state(JSON.parse(this.views.activeView[0].saved_config)),this.setFieldChoosers(this.views.activeView[0].field_config.length>0?JSON.parse(this.views.activeView[0].field_config)[0]:{showDataFields:!0,showRowFields:!0,showColumnFields:!0,showFilterFields:!0})}},e=>{this.enableDisplayView(),this.snackBar.open("Error Retrieving Report Views! Try Refreshing","Dismiss",{duration:3e3})})}))}stateUpdate(){let e=localStorage.getItem("Report_374");"string"==typeof e&&(e=JSON.parse(e)),e&&this.views&&this.views.allViews.length>0?this.governanceReportSubService.updateSundayGovState(e,this.views.allViews[this.currentView].customization_id,[{showDataFields:this.showDataFields,showRowFields:this.showRowFields,showColumnFields:this.showColumnFields,showFilterFields:this.showFilterFields}]).subscribe(e=>{this.enableDisplayView(),this.snackBar.open("Report Version - "+this.views.allViews[this.currentView].config_name+" was updated Successfully!","Dismiss",{duration:3e3}),this.getConfigData()},e=>{this.snackBar.open("Unable to Update the Current Report Version! Try Again","Dismiss",{duration:3e3})}):this.snackBar.open("No Report Versions Found.Kindly Use SaveAs!","Dismiss",{duration:3e3})}saveState(){let e=localStorage.getItem("Report_374");"string"==typeof e&&(e=JSON.parse(e)),this.governanceReportSubService.saveSundayGovState(e,this.versionName.value,[{showDataFields:this.showDataFields,showRowFields:this.showRowFields,showColumnFields:this.showColumnFields,showFilterFields:this.showFilterFields}]).subscribe(e=>{this.enableDisplayView(),this.snackBar.open("Report Version - "+this.versionName.value+" was created Successfully!","Dismiss",{duration:3e3}),this.getConfigData()},e=>{this.snackBar.open("Unable to create the Report Version! Try Again","Dismiss",{duration:3e3})})}changeView(e){this.currentView=e,localStorage.setItem("Report_374",this.views.allViews[e].saved_config),localStorage.getItem("Report_374"),this.pivotGrid1.getDataSource().state(JSON.parse(this.views.allViews[e].saved_config)),this.setFieldChoosers(JSON.parse(this.views.allViews[e].field_config)[0]),this.enableDisplayView()}showDataFieldsFn(){this.showDataFields=!this.showDataFields,this.snackBar.open(1==this.showDataFields?"Displaying Data Fields!":"Data Fields Hidden!","Dismiss",{duration:1e3})}showRowFieldsFn(){this.showRowFields=!this.showRowFields,this.snackBar.open(1==this.showRowFields?"Displaying Row Fields!":"Row Fields Hidden!","Dismiss",{duration:1e3})}showColumnFieldsFn(){this.showColumnFields=!this.showColumnFields,this.snackBar.open(1==this.showColumnFields?"Displaying Column Fields!":"Column Fields Hidden!","Dismiss",{duration:1e3})}showFilterFieldsFn(){this.showFilterFields=!this.showFilterFields,this.snackBar.open(1==this.showFilterFields?"Displaying Filter Fields!":"Filter Fields Hidden!","Dismiss",{duration:1e3})}toggleEditView(){this.displayView=!this.displayView}enableDisplayView(){this.displayView=!0}scrollRight(){this.cardScroll.nativeElement.scrollTo({left:this.cardScroll.nativeElement.scrollLeft+1060,behavior:"smooth"})}scrollLeft(){this.cardScroll.nativeElement.scrollTo({left:this.cardScroll.nativeElement.scrollLeft-1060,behavior:"smooth"})}deleteVariant(e){let t=this.views.allViews[e].config_name;this.utilityService.openConfirmationSweetAlertWithCustom("Do you want to delete "+t+" variant ?","").then(i=>Object(n.c)(this,void 0,void 0,(function*(){i&&this.userExperience.deleteVariant(48,this.views.allViews[e].customization_id).subscribe(e=>{this.getConfigData(),this.snackBar.open("Variant "+t+" was Deleted Succesfully","Dismiss",{duration:3e3})},e=>{this.snackBar.open("Failed to delete Variant "+t+".","Dismiss",{duration:3e3})})})))}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275directiveInject"](o.h),a["\u0275\u0275directiveInject"](V.a),a["\u0275\u0275directiveInject"](E.a),a["\u0275\u0275directiveInject"](O.a),a["\u0275\u0275directiveInject"](k.a),a["\u0275\u0275directiveInject"](I.a),a["\u0275\u0275directiveInject"](R.a),a["\u0275\u0275directiveInject"](_.a),a["\u0275\u0275directiveInject"](T.a),a["\u0275\u0275directiveInject"](o.a))},e.\u0275cmp=a["\u0275\u0275defineComponent"]({type:e,selectors:[["gov-rep-old-report-modal"]],viewQuery:function(e,t){if(1&e&&(a["\u0275\u0275viewQuery"](N,!0),a["\u0275\u0275viewQuery"](s.a,!0),a["\u0275\u0275viewQuery"](d.a,!0),a["\u0275\u0275viewQuery"](B,!0,a.ElementRef)),2&e){let e;a["\u0275\u0275queryRefresh"](e=a["\u0275\u0275loadQuery"]())&&(t.drillDownDataGrid=e.first),a["\u0275\u0275queryRefresh"](e=a["\u0275\u0275loadQuery"]())&&(t.pivotGrid=e.first),a["\u0275\u0275queryRefresh"](e=a["\u0275\u0275loadQuery"]())&&(t.chart=e.first),a["\u0275\u0275queryRefresh"](e=a["\u0275\u0275loadQuery"]())&&(t.cardScroll=e.first)}},features:[a["\u0275\u0275ProvidersFeature"]([])],decls:14,vars:2,consts:[[1,"container-fluid","old-governance-report-modal-styles","h-100","d-flex","flex-column"],[1,"row","pt-2","pb-2",2,"border-bottom","solid 1px #cacaca"],[1,"col-1","d-flex"],["mat-icon-button","",1,"bubble"],[1,"iconButton"],[1,"col-8","col-md-9","pl-0","mt-1","name"],[1,"col-3","col-md-2"],["mat-icon-button","","matTooltip","Close",1,"d-flex","ml-auto","mr-2","close-button",3,"click"],[1,"close-Icon"],["class","d-flex flex-row h-100",4,"ngIf"],["class","sunday-gov-styles flex-grow-1 d-flex flex-column","style","overflow-y: auto",4,"ngIf"],[1,"d-flex","flex-row","h-100"],["color","#cf0001","mode","indeterminate","diameter","40",1,"m-auto"],[1,"sunday-gov-styles","flex-grow-1","d-flex","flex-column",2,"overflow-y","auto"],[2,"min-height","48px !important"],[1,"p-1"],[1,"row"],[1,"col-12","d-flex","pl-0","pr-0"],[1,"row","w-100"],[1,"col-12","col-md-9","pl-0","pr-0","d-flex","button-row"],[1,"d-flex","p-1","flex-grow-1"],["type","text","ngxDaterangepickerMd","","placeholder","Choose Date",1,"input-class",3,"showCustomRangeLabel","locale","alwaysShowCalendars","showCancel","ranges","linkedCalendars","ngModel","ngModelChange","change"],["mat-icon-button","","class","iconsSize btn-fab-left",3,"click",4,"ngIf"],[1,"d-flex","overflow-hidden","flex-grow-1",2,"max-width","80%","min-width","70%"],["cardScroll",""],["mat-raised-button","","style","font-weight: normal; margin-left: 1%",3,"ngClass","click",4,"ngFor","ngForOf"],["mat-icon-button","","class","iconsSize btn-fab-right",3,"click",4,"ngIf"],[1,"col-12","col-md-3","pr-0","pl-0"],["mat-icon-button","","matTooltip","Edit View",1,"iconsSize","ml-auto",3,"matMenuTriggerFor"],["matBadge","!","matBadgeSize","small","matBadgeColor","warn",1,"iconsSize",3,"matBadgeHidden"],[1,"card-panel"],["options","matMenu"],[1,"card"],[1,"card-body","pl-3","pr-3","pt-2","pb-2"],[1,"col-12"],["mat-icon-button","","matTooltip","Hide Data Fields",1,"iconsSize","ml-1",3,"click"],["mat-icon-button","","matTooltip","Hide Row Fields",1,"iconsSize","ml-1",3,"click"],["mat-icon-button","","matTooltip","Hide Column Fields",1,"iconsSize","ml-1",3,"click"],["mat-icon-button","","matTooltip","Hide Filter Fields",1,"iconsSize","ml-1",3,"click"],["mat-icon-button","","matTooltip","Save view",1,"iconsSize","ml-1",3,"disabled","click"],[1,"iconsSize"],["mat-icon-button","","matTooltip","Save view As",1,"iconsSize","ml-1",3,"satPopoverAnchor","click"],["horizontalAlign","after","verticalAlign","below"],["saveAs",""],[1,"popover-content"],["matInput","","placeholder","Save as - Name","required","",3,"formControl"],[4,"ngIf"],["mat-raised-button","","color","warn",3,"disabled","click"],["icon","clear","matTooltip","Reset view",1,"iconsSize","ml-1",3,"onClick"],["icon","refresh","matTooltip","Reload data",1,"iconsSize","ml-1",3,"onClick"],["icon","columnchooser","matTooltip","Column Config",1,"iconsSize","ml-1",3,"onClick"],["icon","exportxlsx","matTooltip","Export as Excel",1,"iconsSize","ml-1",3,"onClick"],["id","gov",1,"flex-grow-1",3,"allowSortingBySummary","allowSorting","allowFiltering","allowExpandAll","showBorders","dataSource","wordWrapEnabled","height","showColumnTotals","showColumnGrandTotals","showRowTotals","onCellPrepared","onInitialized","onExporting"],["fileName","SG Report",3,"enabled"],[3,"enabled","allowSearch"],[3,"allowSearch","width","height"],[3,"showDataFields","showRowFields","showColumnFields","showFilterFields","allowFieldDragging","visible"],["type","localStorage","storageKey","Report_374",3,"enabled"],[4,"dxTemplate","dxTemplateOf"],["mat-icon-button","",1,"iconsSize","btn-fab-left",3,"click"],[2,"color","#1e2733 !important","font-size","21px !important"],["mat-raised-button","",2,"font-weight","normal","margin-left","1%",3,"ngClass","click"],["matTooltip","Delete view","class","resource-costing-icon ml-1",3,"click",4,"ngIf"],["matTooltip","Delete view",1,"resource-costing-icon","ml-1",3,"click"],["mat-icon-button","",1,"iconsSize","btn-fab-right",3,"click"],[1,"cell-content"]],template:function(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"div",0),a["\u0275\u0275elementStart"](1,"div",1),a["\u0275\u0275elementStart"](2,"div",2),a["\u0275\u0275elementStart"](3,"div",3),a["\u0275\u0275elementStart"](4,"mat-icon",4),a["\u0275\u0275text"](5,"report"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](6,"div",5),a["\u0275\u0275text"](7,"Governance Report Pivot"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](8,"div",6),a["\u0275\u0275elementStart"](9,"button",7),a["\u0275\u0275listener"]("click",(function(){return t.closeModal()})),a["\u0275\u0275elementStart"](10,"mat-icon",8),a["\u0275\u0275text"](11,"close"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](12,Y,2,0,"div",9),a["\u0275\u0275template"](13,H,63,47,"div",10),a["\u0275\u0275elementEnd"]()),2&e&&(a["\u0275\u0275advance"](12),a["\u0275\u0275property"]("ngIf",!t.isLoaded),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",t.isLoaded))},directives:[w.a,g.a,D.a,p.NgIf,f.a,x.b,m.e,m.v,m.y,p.NgForOf,C.f,v.a,C.g,b.b,b.a,S.c,y.b,m.F,m.k,F.a,s.a,P.Sb,P.Ub,P.Cc,P.Wb,P.le,M.d,p.NgClass,S.b],styles:[".old-governance-report-modal-styles[_ngcontent-%COMP%]{background-size:200px 181px;background-repeat:no-repeat;overflow-y:hidden!important;min-height:95vh;background-position:99% 105%;padding-left:0!important;padding-right:0!important;display:flex;flex-direction:column}.old-governance-report-modal-styles[_ngcontent-%COMP%]   .input-class[_ngcontent-%COMP%]{background:0 0;border:none!important;font-size:12px;font-weight:500;width:70%!important;cursor:pointer;text-align:center}.old-governance-report-modal-styles[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]{display:flex;line-height:26px;border-radius:50%;cursor:context-menu!important;width:26px;height:26px;background-color:#fbfbfb;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.old-governance-report-modal-styles[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#cf0001;font-size:18px;margin-top:5px!important;margin-left:4px!important}.old-governance-report-modal-styles[_ngcontent-%COMP%]   .name[_ngcontent-%COMP%]{font-size:14px;color:#cf0001;font-family:Roboto;font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.old-governance-report-modal-styles[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.old-governance-report-modal-styles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:30px!important;height:30px!important;line-height:30px!important}.old-governance-report-modal-styles[_ngcontent-%COMP%]   .popover-content[_ngcontent-%COMP%]{box-shadow:0 3px 1px -2px rgba(0,0,0,.2),0 2px 2px 0 rgba(0,0,0,.14),0 1px 5px 0 rgba(0,0,0,.12)!important;padding:16px!important;background-color:#fff!important;max-width:300px!important;line-height:33px!important}.old-governance-report-modal-styles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff}.old-governance-report-modal-styles[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]{background-color:#fbfbfb;color:#000}@media (max-width:767px){.old-governance-report-modal-styles[_ngcontent-%COMP%]   .input-class[_ngcontent-%COMP%]{width:100%!important}.old-governance-report-modal-styles[_ngcontent-%COMP%]   .iconsSize[_ngcontent-%COMP%]{width:24px!important;height:24px!important;line-height:24px!important;font-size:16px!important}}.old-governance-report-modal-styles[_ngcontent-%COMP%]   .flex-grow-1[_ngcontent-%COMP%]{flex-grow:1;overflow-y:auto}.old-governance-report-modal-styles[_ngcontent-%COMP%]   .cell-content[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;width:100%}"],data:{animation:[Object(l.o)("slideInOut",[Object(l.l)("in",Object(l.m)({height:"*",overflow:"hidden"})),Object(l.l)("out",Object(l.m)({height:0,overflow:"hidden"})),Object(l.n)("* => in",[Object(l.m)({height:0}),Object(l.e)(250,Object(l.m)({height:"*"}))]),Object(l.n)("in=> *",[Object(l.m)({height:"*"}),Object(l.e)(250,Object(l.m)({height:0}))])])]}}),e})()}}]);