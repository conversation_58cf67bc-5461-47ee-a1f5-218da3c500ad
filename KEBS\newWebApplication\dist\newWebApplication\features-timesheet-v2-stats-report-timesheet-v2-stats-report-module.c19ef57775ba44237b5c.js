(window.webpackJsonp=window.webpackJsonp||[]).push([[689,214,878],{"1+mW":function(e,t,i){"use strict";i.r(t),i.d(t,"ApplicantTrackingSystemModule",(function(){return z}));var n=i("ofXK"),a=i("tyNb"),s=i("mrSG"),o=i("1G5W"),d=i("XNiG"),r=i("yuIm"),l=i("fXoL"),c=i("c7zN"),h=i("XXEo"),g=i("XNFG");let m=(()=>{class e{constructor(e,t,i){this._utilitiesService=e,this._loginService=t,this._toaster=i,this._onDestroy=new d.b}canActivate(e,t){return Object(s.c)(this,void 0,void 0,(function*(){return yield this.checkAccessForAssociate(),!0}))}checkAccessForAssociate(){return Object(s.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._utilitiesService.checkAccessForAssociate(this._loginService.getProfile().profile.aid).pipe(Object(o.a)(this._onDestroy)).subscribe({next:t=>{if(0==t.err)r.setRoleAccessList(t.data);else{let e=!(!t||!t.hasOwnProperty("is_rds_peak"))&&t.is_rds_peak;r.changeRDSvalue(e||!1),r.setRoleAccessList([]),this._toaster.showError("Error",t.msg,7e3)}e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Role Data Retrieval Failed!",7e3),r.setRoleAccessList([]),t()}}))}))}}return e.\u0275fac=function(t){return new(t||e)(l["\u0275\u0275inject"](c.a),l["\u0275\u0275inject"](h.a),l["\u0275\u0275inject"](g.a))},e.\u0275prov=l["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();const u=[{path:"",canActivate:[m],pathMatch:"full",redirectTo:"jobs"},{path:"jobs",canActivate:[m],loadChildren:()=>i.e(872).then(i.bind(null,"Lp0Z")).then(e=>e.JobsModule),data:{breadcrumb:"Jobs"}},{path:"candidates",canActivate:[m],loadChildren:()=>i.e(871).then(i.bind(null,"X1Mi")).then(e=>e.CandidatesModule),data:{breadcrumb:"Candidates"}},{path:"settings",canActivate:[m],loadChildren:()=>i.e(880).then(i.bind(null,"VSH1")).then(e=>e.SettingsModule),data:{breadcrumb:"Settings"}},{path:"reports",canActivate:[],loadChildren:()=>i.e(0).then(i.bind(null,"Utwq")).then(e=>e.ProductReportModule),data:{breadcrumb:"Reports"}},{path:"onboarding",canActivate:[m],loadChildren:()=>i.e(873).then(i.bind(null,"vMvN")).then(e=>e.OnboardingModule),data:{breadcrumb:"Onboarding"}}];let p=(()=>{class e{}return e.\u0275mod=l["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=l["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[a.k.forChild(u)],a.k]}),e})();var C=i("Xi0T"),b=i("0IaG"),w=i("bSwM"),f=i("Qu3c"),D=i("lVl8"),v=i("NFeN"),O=i("5+WD"),_=i("3Pt+"),V=i("kmnG"),S=i("qFsG"),J=i("rDax"),E=i("Wp6s"),I=i("iadO"),A=i("Xa2L"),y=i("1jcm"),k=i("4/q7"),j=i("f0Cb"),R=i("A5z7"),P=i("d3UM"),T=i("vxfF"),H=i("wZkO"),W=i("dlKe"),x=i("cZdB"),M=i("w4ga"),L=i("WJ5W"),F=i("YhS8"),N=i("pzj6"),G=i("mgaL"),X=i("3beV");let z=(()=>{class e{}return e.\u0275mod=l["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=l["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[n.CommonModule,X.a,p,C.a,b.g,w.b,f.b,D.b,v.b,W.b,O.g,_.E,_.p,V.e,S.c,J.h,E.d,I.h,x.b,A.b,k.b,y.b,M.b,j.b,R.e,P.d,L.b,F.c.forRoot(),T.g,H.g,N.b,G.a]]}),e})()},XPKZ:function(e,t,i){"use strict";i.d(t,"a",(function(){return l})),i.d(t,"b",(function(){return c}));var n=i("jhN1"),a=i("fXoL"),s=i("l9Wm"),o=i("PVOt"),d=i("6t9p");const r=["*"];let l=(()=>{let e=class extends o.b{constructor(e,t,i,n,a,s,o){super(e,t,i,n,s,o),this._createEventEmitters([{subscribe:"contentReady",emit:"onContentReady"},{subscribe:"disposing",emit:"onDisposing"},{subscribe:"hidden",emit:"onHidden"},{subscribe:"hiding",emit:"onHiding"},{subscribe:"initialized",emit:"onInitialized"},{subscribe:"optionChanged",emit:"onOptionChanged"},{subscribe:"showing",emit:"onShowing"},{subscribe:"shown",emit:"onShown"},{emit:"animationChange"},{emit:"closeOnOutsideClickChange"},{emit:"containerChange"},{emit:"contentTemplateChange"},{emit:"deferRenderingChange"},{emit:"disabledChange"},{emit:"elementAttrChange"},{emit:"heightChange"},{emit:"hideEventChange"},{emit:"hintChange"},{emit:"hoverStateEnabledChange"},{emit:"maxHeightChange"},{emit:"maxWidthChange"},{emit:"minHeightChange"},{emit:"minWidthChange"},{emit:"positionChange"},{emit:"rtlEnabledChange"},{emit:"shadingChange"},{emit:"shadingColorChange"},{emit:"showEventChange"},{emit:"targetChange"},{emit:"visibleChange"},{emit:"widthChange"}]),a.setHost(this)}get animation(){return this._getOption("animation")}set animation(e){this._setOption("animation",e)}get closeOnOutsideClick(){return this._getOption("closeOnOutsideClick")}set closeOnOutsideClick(e){this._setOption("closeOnOutsideClick",e)}get container(){return this._getOption("container")}set container(e){this._setOption("container",e)}get contentTemplate(){return this._getOption("contentTemplate")}set contentTemplate(e){this._setOption("contentTemplate",e)}get deferRendering(){return this._getOption("deferRendering")}set deferRendering(e){this._setOption("deferRendering",e)}get disabled(){return this._getOption("disabled")}set disabled(e){this._setOption("disabled",e)}get elementAttr(){return this._getOption("elementAttr")}set elementAttr(e){this._setOption("elementAttr",e)}get height(){return this._getOption("height")}set height(e){this._setOption("height",e)}get hideEvent(){return this._getOption("hideEvent")}set hideEvent(e){this._setOption("hideEvent",e)}get hint(){return this._getOption("hint")}set hint(e){this._setOption("hint",e)}get hoverStateEnabled(){return this._getOption("hoverStateEnabled")}set hoverStateEnabled(e){this._setOption("hoverStateEnabled",e)}get maxHeight(){return this._getOption("maxHeight")}set maxHeight(e){this._setOption("maxHeight",e)}get maxWidth(){return this._getOption("maxWidth")}set maxWidth(e){this._setOption("maxWidth",e)}get minHeight(){return this._getOption("minHeight")}set minHeight(e){this._setOption("minHeight",e)}get minWidth(){return this._getOption("minWidth")}set minWidth(e){this._setOption("minWidth",e)}get position(){return this._getOption("position")}set position(e){this._setOption("position",e)}get rtlEnabled(){return this._getOption("rtlEnabled")}set rtlEnabled(e){this._setOption("rtlEnabled",e)}get shading(){return this._getOption("shading")}set shading(e){this._setOption("shading",e)}get shadingColor(){return this._getOption("shadingColor")}set shadingColor(e){this._setOption("shadingColor",e)}get showEvent(){return this._getOption("showEvent")}set showEvent(e){this._setOption("showEvent",e)}get target(){return this._getOption("target")}set target(e){this._setOption("target",e)}get visible(){return this._getOption("visible")}set visible(e){this._setOption("visible",e)}get width(){return this._getOption("width")}set width(e){this._setOption("width",e)}_createInstance(e,t){return new s.a(e,t)}ngOnDestroy(){this._destroyWidget()}};return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275directiveInject"](a.ElementRef),a["\u0275\u0275directiveInject"](a.NgZone),a["\u0275\u0275directiveInject"](o.e),a["\u0275\u0275directiveInject"](o.j),a["\u0275\u0275directiveInject"](o.i),a["\u0275\u0275directiveInject"](n.h),a["\u0275\u0275directiveInject"](a.PLATFORM_ID))},e.\u0275cmp=a["\u0275\u0275defineComponent"]({type:e,selectors:[["dx-tooltip"]],inputs:{animation:"animation",closeOnOutsideClick:"closeOnOutsideClick",container:"container",contentTemplate:"contentTemplate",deferRendering:"deferRendering",disabled:"disabled",elementAttr:"elementAttr",height:"height",hideEvent:"hideEvent",hint:"hint",hoverStateEnabled:"hoverStateEnabled",maxHeight:"maxHeight",maxWidth:"maxWidth",minHeight:"minHeight",minWidth:"minWidth",position:"position",rtlEnabled:"rtlEnabled",shading:"shading",shadingColor:"shadingColor",showEvent:"showEvent",target:"target",visible:"visible",width:"width"},outputs:{onContentReady:"onContentReady",onDisposing:"onDisposing",onHidden:"onHidden",onHiding:"onHiding",onInitialized:"onInitialized",onOptionChanged:"onOptionChanged",onShowing:"onShowing",onShown:"onShown",animationChange:"animationChange",closeOnOutsideClickChange:"closeOnOutsideClickChange",containerChange:"containerChange",contentTemplateChange:"contentTemplateChange",deferRenderingChange:"deferRenderingChange",disabledChange:"disabledChange",elementAttrChange:"elementAttrChange",heightChange:"heightChange",hideEventChange:"hideEventChange",hintChange:"hintChange",hoverStateEnabledChange:"hoverStateEnabledChange",maxHeightChange:"maxHeightChange",maxWidthChange:"maxWidthChange",minHeightChange:"minHeightChange",minWidthChange:"minWidthChange",positionChange:"positionChange",rtlEnabledChange:"rtlEnabledChange",shadingChange:"shadingChange",shadingColorChange:"shadingColorChange",showEventChange:"showEventChange",targetChange:"targetChange",visibleChange:"visibleChange",widthChange:"widthChange"},features:[a["\u0275\u0275ProvidersFeature"]([o.e,o.j,o.i]),a["\u0275\u0275InheritDefinitionFeature"]],ngContentSelectors:r,decls:1,vars:0,template:function(e,t){1&e&&(a["\u0275\u0275projectionDef"](),a["\u0275\u0275projection"](0))},encapsulation:2}),e})(),c=(()=>{let e=class{};return e.\u0275mod=a["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=a["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[d.bb,d.Gc,d.Vd,d.Fc,d.vd,d.hb,d.lb,d.sb,d.id,d.jd,d.Ud,o.c,o.f,n.b],d.bb,d.Gc,d.Vd,d.Fc,d.vd,d.hb,d.lb,d.sb,d.id,d.jd,d.Ud,o.f]}),e})()},kZtD:function(e,t,i){"use strict";i.r(t),i.d(t,"TimesheetV2StatsReportModule",(function(){return _}));var n=i("ofXK"),a=i("tyNb"),s=i("fXoL"),o=i("4qBo");const d=[{path:"",component:(()=>{class e{constructor(){}ngOnInit(){}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-static-report-landing-page"]],decls:1,vars:2,consts:[[3,"applicationId","reportId"]],template:function(e,t){1&e&&s["\u0275\u0275element"](0,"app-details-page",0),2&e&&s["\u0275\u0275property"]("applicationId",544)("reportId",569)},directives:[o.a],styles:[""]}),e})()}];let r=(()=>{class e{}return e.\u0275mod=s["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=s["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[a.k.forChild(d)],a.k]}),e})();var l=i("ZzPI"),c=i("PVOt"),h=i("Xa2L"),g=i("kmnG"),m=i("qFsG"),u=i("FKr1"),p=i("iadO"),C=i("3Pt+"),b=i("NFeN"),w=i("d3UM"),f=i("Qu3c"),D=i("0IaG"),v=i("bSwM"),O=i("yq0e");let _=(()=>{class e{}return e.\u0275mod=s["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=s["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[n.CommonModule,r,l.b,h.b,g.e,m.c,u.n,p.h,C.p,C.E,b.b,w.d,c.f,f.b,D.g,v.b,O.ReportLandingModule]]}),e})()},l9Wm:function(e,t,i){"use strict";var n=i("pG26");t.a=n.a},pG26:function(e,t,i){"use strict";var n=i("W2II"),a=i("3VAS"),s=i("CF5h"),o=i("ua+q"),d=i("KckG"),r=i("v5W6"),l=d.a.inherit({_getDefaultOptions:function(){return Object(o.a)(this.callBase(),{toolbarItems:[],showCloseButton:!1,showTitle:!1,title:null,titleTemplate:null,onTitleRendered:null,bottomTemplate:null,propagateOutsideClick:!0})},_render:function(){this.$element().addClass("dx-tooltip"),this.$wrapper().addClass("dx-tooltip-wrapper"),this.callBase()},_renderContent:function(){this.callBase(),this._contentId="dx-"+new a.a,this.$overlayContent().attr({id:this._contentId,role:"tooltip"}),this._toggleAriaDescription(!0)},_toggleAriaDescription:function(e){var t=Object(n.a)(this.option("target")),i=e?this._contentId:void 0;Object(r.p)(t.get(0))||this.setAria("describedby",i,t)}});Object(s.a)("dxTooltip",l),t.a=l},yuIm:function(e,t,i){"use strict";i.r(t),i.d(t,"roleAccessList",(function(){return n})),i.d(t,"currentJobRoleAccessList",(function(){return a})),i.d(t,"is_rds_peak",(function(){return s})),i.d(t,"moduleId",(function(){return o})),i.d(t,"subModuleId",(function(){return d})),i.d(t,"sectionId",(function(){return r})),i.d(t,"subSectionId",(function(){return l})),i.d(t,"getRoleAccessList",(function(){return c})),i.d(t,"setRoleAccessList",(function(){return h})),i.d(t,"getCurrentJobRoleAccessList",(function(){return g})),i.d(t,"setCurrentJobRoleAccessList",(function(){return m})),i.d(t,"checkAccessForGeneralRole",(function(){return u})),i.d(t,"checkAccessForJobRole",(function(){return p})),i.d(t,"changeRDSvalue",(function(){return C}));let n=[],a=[],s=!1;const o={jobs:1,candidates:2,reports:3,dashboard:4,settings:5,campusJobs:6,onboarding:8},d={manageJob:1,newJob:2,draftJob:3,manageCandidate:4,talentPipeline:5,templateSettings:10,collegeSettings:17,manageCampusJob:15,campusTalentPipeline:16,activityLogs:20,userSettings:6,profileSettings:7,notificationSettings:8,emailSettings:11,companySettings:12,vendorSettings:13,rolesAndPermissions:9,onboardingTaskSettings:28,onboardingChecklistSettings:29,onboardingChecklists:30,onboardingCandidates:31},r={manageJobDetailView:17,manageCampusJobDetailView:18,manageJobDetailViewOverview:1,manageJobDetailViewCandidate:2,manageJobDetailViewScorecard:3,manageJobDetailViewAdverts:4,manageJobDetailViewInsights:5,manageJobDetailViewEmailLogs:15,manageJobDetailViewHistoryLogs:19,manageCampusJobDetailViewOverview:6,manageCampusJobDetailViewCandidate:7,manageCampusJobDetailViewScorecard:8,manageCampusJobDetailViewAdverts:9,manageCampusJobDetailViewInsights:10,manageCampusJobDetailViewEmailLogs:16,manageCampusJobDetailViewHistoryLogs:20,manageJobCandidateDetailView:11,allCandidatesCandidateDetailView:12,talentPipelineCandidateDetailView:13,manageCampusJobCandidateDetailView:14,userSettingsDetailView:21,generalDetailView:22,themeSettings:23,emailSync:24,emailSignature:25,blockemail:26,calendarIntegration:27,organizationSettings:28,vendorDetailView:29,vendorAssignUser:30,campusTalentPipelineCandidateDetailView:31,onboardingCandidateDetailView:32},l={manageJobCandidateDetailViewDetails:1,manageJobCandidateDetailViewResume:2,manageJobCandidateDetailViewScorecard:3,manageJobCandidateDetailViewComments:4,manageJobCandidateDetailViewInterview:5,manageJobCandidateDetailViewDocuments:6,manageJobCandidateDetailViewHistory:7,manageJobCandidateDetailViewApplications:8,manageJobCandidateDetailViewInterviewScheduling:9,manageJobCandidateDetailViewSendOffer:10,manageJobCandidateDetailViewCustomQuestions:49,allCandidatesDetailViewDetails:37,allCandidatesDetailViewResume:11,allCandidatesDetailViewScorecard:12,allCandidatesDetailViewComments:13,allCandidatesDetailViewInterview:14,allCandidatesDetailViewDocuments:15,allCandidatesDetailViewHistory:16,allCandidatesDetailViewApplications:17,allCandidatesDetailViewCertificates:18,talentPipelineCandidatesDetailViewDetails:38,talentPipelineCandidatesDetailViewResume:19,talentPipelineCandidatesDetailViewScorecard:20,talentPipelineCandidatesDetailViewComments:21,talentPipelineCandidatesDetailViewInterview:22,talentPipelineCandidatesDetailViewDocuments:23,talentPipelineCandidatesDetailViewHistory:24,talentPipelineCandidatesDetailViewApplications:25,talentPipelineCandidatesDetailViewCertificates:26,manageCampusJobCandidateDetailViewDetails:27,manageCampusJobCandidateDetailViewResume:28,manageCampusJobCandidateDetailViewScorecard:29,manageCampusJobCandidateDetailViewComments:30,manageCampusJobCandidateDetailViewInterview:31,manageCampusJobCandidateDetailViewDocuments:32,manageCampusJobCandidateDetailViewHistory:33,manageCampusJobCandidateDetailViewApplications:34,manageCampusJobCandidateDetailViewCustomQuestions:50,manageCampusJobCandidateDetailViewInterviewScheduling:35,manageCampusJobCandidateDetailViewSendOffer:36,manageJobCandidateDetailViewOfferTrack:63,manageCampusJobCandidateDetailViewOfferTrack:64,allCandidatesDetailViewOfferTrack:65,talentPipelineCandidatesDetailViewOfferTrack:66,campusTalentPipelineCandidatesDetailViewDetails:67,campusTalentPipelineCandidatesDetailViewResume:68,campusTalentPipelineCandidatesDetailViewScorecard:69,campusTalentPipelineCandidatesDetailViewComments:70,campusTalentPipelineCandidatesDetailViewInterview:71,campusTalentPipelineCandidatesDetailViewDocuments:72,campusTalentPipelineCandidatesDetailViewHistory:73,campusTalentPipelineCandidatesDetailViewApplications:74,campusTalentPipelineCandidatesDetailViewCertificates:75,campusTalentPipelineCandidatesDetailViewOfferTrack:76,onboardingCandidateDetailViewChecklists:77,onboardingCandidateDetailViewDocuments:78};function c(){return n}function h(e){n=e}function g(){return a}function m(e){a=e}function u(e=0,t=0,i=0,a=0,s=""){if(!s||""==s)return!1;let o={module_id:e,sub_module_id:t,section_id:i,sub_section_id:a};"V"==s&&(o.view_permission=1),"C"==s&&(o.create_permission=1),"E"==s&&(o.edit_permission=1),"DE"==s&&(o.delete_permission=1),"DO"==s&&(o.download_permission=1),"U"==s&&(o.upload_permission=1),"B"==s&&(o.bulk_operation=1);const d=Object.keys(o);return n.find(e=>d.every(t=>e[t]===o[t]))}function p(e=0,t=0,i=0,n=0,s=""){if(!s||""==s)return!1;let o={module_id:e,sub_module_id:t,section_id:i,sub_section_id:n};"V"==s&&(o.view_permission=1),"C"==s&&(o.create_permission=1),"E"==s&&(o.edit_permission=1),"DE"==s&&(o.delete_permission=1),"DO"==s&&(o.download_permission=1),"U"==s&&(o.upload_permission=1),"B"==s&&(o.bulk_operation=1);const d=Object.keys(o);return a.find(e=>d.every(t=>e[t]===o[t]))}function C(e){return s=e,s}}}]);