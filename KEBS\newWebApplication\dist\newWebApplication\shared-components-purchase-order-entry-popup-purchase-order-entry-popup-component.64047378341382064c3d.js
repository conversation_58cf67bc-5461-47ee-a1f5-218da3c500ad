(window.webpackJsonp=window.webpackJsonp||[]).push([[895],{oG8U:function(e,t,n){"use strict";n.r(t),n.d(t,"PurchaseOrderEntryPopupComponent",(function(){return _})),n.d(t,"PurchaseOrderEntryPopupModule",(function(){return O}));var a=n("mrSG"),o=n("7pIB"),r=n("ofXK"),i=n("Qu3c"),s=n("bTqV"),l=n("NFeN"),p=n("Kb4U"),c=n("Xi0T"),m=n("kmnG"),d=n("qFsG"),u=n("Xa2L"),h=n("3Pt+"),g=n("fXoL"),f=n("a1r6"),b=n("XXEo");function v(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"button",16),g["\u0275\u0275listener"]("click",(function(){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275reference"](3).click()})),g["\u0275\u0275text"](1," Attach "),g["\u0275\u0275elementStart"](2,"input",17,18),g["\u0275\u0275listener"]("change",(function(){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"]().onSingleFileAdd()})),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}if(2&e){const e=g["\u0275\u0275nextContext"]();g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("uploader",e.uploader)}}function y(e,t){1&e&&g["\u0275\u0275element"](0,"mat-spinner",19)}function S(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div"),g["\u0275\u0275elementStart"](1,"span",20),g["\u0275\u0275text"](2),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](3,"span",21),g["\u0275\u0275elementStart"](4,"button",22),g["\u0275\u0275listener"]("click",(function(){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"]().removeAttachment()})),g["\u0275\u0275elementStart"](5,"mat-icon",23),g["\u0275\u0275text"](6,"close"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}if(2&e){const e=g["\u0275\u0275nextContext"]();g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("matTooltip",null==e.poEntry.get("attachmentData").value.files_json?null:e.poEntry.get("attachmentData").value.files_json.fileName),g["\u0275\u0275advance"](1),g["\u0275\u0275textInterpolate1"](" ",null==e.poEntry.get("attachmentData").value.files_json?null:e.poEntry.get("attachmentData").value.files_json.fileName," ")}}function P(e,t){1&e&&(g["\u0275\u0275elementStart"](0,"mat-icon",24),g["\u0275\u0275text"](1," done_all "),g["\u0275\u0275elementEnd"]())}function E(e,t){1&e&&g["\u0275\u0275element"](0,"mat-spinner",19)}let _=(()=>{class e{constructor(e,t,n){this._p2pGeneralService=e,this.fb=t,this._auth=n,this.poEntry=this.fb.group({poNumber:["",h.H.required],attachmentData:["",h.H.required]}),this.isSubmitOnProgress=!1,this.isAttachmentUploadOnProgress=!1,this.UPLOAD_ATTACHMENT_URL="/api/purchaseRequest/uploadPRAttachment",this.getPoSubjectData()}ngOnInit(){this.uploader=new o.d({url:this.UPLOAD_ATTACHMENT_URL,authToken:"Bearer "+this._auth.getToken(),disableMultipart:!1}),this.detectUploadChanges()}detectUploadChanges(){this.uploader.onProgressItem=e=>{this.isAttachmentUploadOnProgress=!0},this.uploader.onCompleteItem=(e,t,n,a)=>{200==n&&t&&t.length>0?(t=JSON.parse(t),this.poEntry.get("attachmentData").patchValue(t),this.isAttachmentUploadOnProgress=!1,console.log(this.poEntry),console.log(t),this.uploader.clearQueue()):this._p2pGeneralService.showMessage("Unable to upload")},this.uploader.onWhenAddingFileFailed=()=>{console.log("Failed to upload")},this.uploader.onAfterAddingFile=e=>{this.uploader.uploadAll()}}getPoSubjectData(){this._p2pGeneralService.getPOPopupSubject().subscribe(e=>{console.log(e),this.p2p_header_id=e.p2p_header_id,this.overlayRef=e.overlayRef})}onSingleFileAdd(){this.uploader.uploadAll()}removeAttachment(){return Object(a.c)(this,void 0,void 0,(function*(){this.isAttachmentUploadOnProgress=!0;let e=this.poEntry.get("attachmentData").value;yield this._p2pGeneralService.deletePRAttachment(e).subscribe(e=>{"S"==e.messType?this.poEntry.get("attachmentData").patchValue(""):this._p2pGeneralService.showMessage("Failed to remove attachment !"),this.isAttachmentUploadOnProgress=!1},e=>{this.isAttachmentUploadOnProgress=!1,console.log(e)})}))}submitPurchaseOrder(){return Object(a.c)(this,void 0,void 0,(function*(){this.isSubmitOnProgress=!0,"VALID"==this.poEntry.status?yield this._p2pGeneralService.enterPurchaseOrderDetails(this.p2p_header_id,this.poEntry.value.poNumber,this.poEntry.value.attachmentData).subscribe(e=>{var t;"S"==e.messType?(this._p2pGeneralService.setPODataSubject({po_number:this.poEntry.value.poNumber,po_attachment:this.poEntry.value.attachmentData,po_enabled:1}),this._p2pGeneralService.showMessage(e.userMess),null===(t=this.overlayRef)||void 0===t||t.dispose()):this._p2pGeneralService.showMessage(e.userMess),this.isSubmitOnProgress=!1},e=>{console.log(e),this.isSubmitOnProgress=!1}):this._p2pGeneralService.showMessage("Please enter mandatory fields !")}))}}return e.\u0275fac=function(t){return new(t||e)(g["\u0275\u0275directiveInject"](f.a),g["\u0275\u0275directiveInject"](h.i),g["\u0275\u0275directiveInject"](b.a))},e.\u0275cmp=g["\u0275\u0275defineComponent"]({type:e,selectors:[["purchase-order-entry-popup"]],decls:22,vars:7,consts:[[1,"pop-up","d-flex","flex-column","p-2","po-entry-popup"],[2,"width","250px","max-height","220px"],[1,"row","head-txt"],[3,"formGroup"],[1,"row","mt-4"],[1,"col-12"],["appearance","outline",1,"create-account-field",2,"width","100%"],["matInput","","required","true","formControlName","poNumber"],[1,"row"],["mat-stroked-button","",3,"click",4,"ngIf"],["matTooltip","Please wait...","class","spinner-align","diameter","20",4,"ngIf"],[4,"ngIf"],[1,"col-9"],[1,"col-3"],["mat-mini-fab","",1,"create-pr-btn",3,"ngClass","click"],["matTooltip","Submit",4,"ngIf"],["mat-stroked-button","",3,"click"],["type","file","ng2FileSelect","",2,"display","none",3,"uploader","change"],["fileInput",""],["matTooltip","Please wait...","diameter","20",1,"spinner-align"],[1,"file-name",3,"matTooltip"],["matTooltip","Remove attachment"],["mat-icon-button","",3,"click"],[2,"font-size","20px"],["matTooltip","Submit"]],template:function(e,t){1&e&&(g["\u0275\u0275elementStart"](0,"div",0),g["\u0275\u0275elementStart"](1,"div",1),g["\u0275\u0275elementStart"](2,"div",2),g["\u0275\u0275text"](3," Purchase Order "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](4,"form",3),g["\u0275\u0275elementStart"](5,"div",4),g["\u0275\u0275elementStart"](6,"div",5),g["\u0275\u0275elementStart"](7,"mat-form-field",6),g["\u0275\u0275elementStart"](8,"mat-label"),g["\u0275\u0275text"](9,"PO number"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275element"](10,"input",7),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](11,"div",8),g["\u0275\u0275elementStart"](12,"div",5),g["\u0275\u0275template"](13,v,4,1,"button",9),g["\u0275\u0275template"](14,y,1,0,"mat-spinner",10),g["\u0275\u0275template"](15,S,7,2,"div",11),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](16,"div",8),g["\u0275\u0275element"](17,"div",12),g["\u0275\u0275elementStart"](18,"div",13),g["\u0275\u0275elementStart"](19,"button",14),g["\u0275\u0275listener"]("click",(function(){return t.submitPurchaseOrder()})),g["\u0275\u0275template"](20,P,2,0,"mat-icon",15),g["\u0275\u0275template"](21,E,1,0,"mat-spinner",10),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()),2&e&&(g["\u0275\u0275advance"](4),g["\u0275\u0275property"]("formGroup",t.poEntry),g["\u0275\u0275advance"](9),g["\u0275\u0275property"]("ngIf",!t.isAttachmentUploadOnProgress&&!t.poEntry.get("attachmentData").value.files_json),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",t.isAttachmentUploadOnProgress),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",t.poEntry.get("attachmentData").value.files_json&&!t.isAttachmentUploadOnProgress),g["\u0275\u0275advance"](4),g["\u0275\u0275property"]("ngClass",t.isSubmitOnProgress?"submit-btn-loading":"submit-btn"),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",!t.isSubmitOnProgress),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",t.isSubmitOnProgress))},directives:[h.J,h.w,h.n,m.c,m.g,d.b,h.e,h.F,h.v,h.l,r.NgIf,s.a,r.NgClass,o.b,u.c,i.a,l.a],styles:[".cdk-overlay-backdrop.cdk-overlay-backdrop-showing{opacity:0!important}.po-entry-popup[_ngcontent-%COMP%]{background-color:#fff;box-shadow:0 4px 8px 0 rgba(0,0,0,.2),0 6px 20px 0 rgba(136,94,94,.19)!important;width:auto}.po-entry-popup[_ngcontent-%COMP%]   .head-txt[_ngcontent-%COMP%]{font-size:18px;font-weight:100}.po-entry-popup[_ngcontent-%COMP%]   .submit-btn[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff}.po-entry-popup[_ngcontent-%COMP%]   .submit-btn-loading[_ngcontent-%COMP%]{background-color:#fff}.po-entry-popup[_ngcontent-%COMP%]   .file-name[_ngcontent-%COMP%]{width:110px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline-block}"]}),e})(),O=(()=>{class e{}return e.\u0275mod=g["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=g["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},providers:[],imports:[[r.CommonModule,s.b,i.b,l.b,p.b,c.a,m.e,h.E,h.p,d.c,o.c,u.b]]}),e})()}}]);