(window.webpackJsonp=window.webpackJsonp||[]).push([[855],{eOIJ:function(e,t,n){"use strict";n.r(t),n.d(t,"ProjectStakeholdersHistoryComponent",(function(){return s}));var l=n("0IaG"),a=n("fXoL"),r=n("ofXK"),d=n("bTqV"),o=n("Qu3c");const i=function(e){return{backgroundColor:e}};function m(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",9),a["\u0275\u0275elementStart"](1,"div",10),a["\u0275\u0275elementStart"](2,"div",11),a["\u0275\u0275elementStart"](3,"div",2),a["\u0275\u0275elementStart"](4,"span",3),a["\u0275\u0275text"](5,"Status"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](6,"div",11),a["\u0275\u0275elementStart"](7,"div",2),a["\u0275\u0275elementStart"](8,"span"),a["\u0275\u0275text"](9),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275element"](10,"div",12),a["\u0275\u0275elementStart"](11,"div",10),a["\u0275\u0275elementStart"](12,"div",11),a["\u0275\u0275elementStart"](13,"div",2),a["\u0275\u0275elementStart"](14,"span",3),a["\u0275\u0275text"](15,"Start Date"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](16,"div",11),a["\u0275\u0275elementStart"](17,"div",2),a["\u0275\u0275elementStart"](18,"span"),a["\u0275\u0275text"](19),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](20,"div",10),a["\u0275\u0275elementStart"](21,"div",11),a["\u0275\u0275elementStart"](22,"div",2),a["\u0275\u0275elementStart"](23,"span",3),a["\u0275\u0275text"](24,"End Date"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](25,"div",11),a["\u0275\u0275elementStart"](26,"div",2),a["\u0275\u0275elementStart"](27,"span"),a["\u0275\u0275text"](28),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](29,"div",13),a["\u0275\u0275elementStart"](30,"div",11),a["\u0275\u0275elementStart"](31,"div",2),a["\u0275\u0275elementStart"](32,"span",3),a["\u0275\u0275text"](33,"Allocated By"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](34,"div",11),a["\u0275\u0275elementStart"](35,"div",2),a["\u0275\u0275elementStart"](36,"span"),a["\u0275\u0275text"](37),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;a["\u0275\u0275advance"](9),a["\u0275\u0275textInterpolate1"](" ",e.internal_stakeholders_status_name,""),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngStyle",a["\u0275\u0275pureFunction1"](5,i,e.internal_stakeholders_status_color)),a["\u0275\u0275advance"](9),a["\u0275\u0275textInterpolate"](e.start_date_val),a["\u0275\u0275advance"](9),a["\u0275\u0275textInterpolate"](e.end_date_val),a["\u0275\u0275advance"](9),a["\u0275\u0275textInterpolate"](e.created_by_name)}}let s=(()=>{class e{constructor(e,t,n){this.dialogRef=e,this.dialogData=t,this.dialog=n,this.employeeHistory=[]}ngOnInit(){console.log(this.dialogData),this.employeeHistory=this.dialogData.employeeHistory}viewTeamMember(){this.dialogRef.close({messType:"S",data:"team details"})}goProjectOverview(){this.dialogRef.close({messType:"S",data:"overview"})}closeClicked(){this.dialogRef.close()}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275directiveInject"](l.h),a["\u0275\u0275directiveInject"](l.a),a["\u0275\u0275directiveInject"](l.b))},e.\u0275cmp=a["\u0275\u0275defineComponent"]({type:e,selectors:[["app-project-stakeholders-history"]],decls:11,vars:1,consts:[[1,"container","resource-updated-styles"],[1,"row","pt-2","pb-2"],[1,"col-12"],[1,"field-title"],["class","row pt-2 pb-3 border-bottom",4,"ngFor","ngForOf"],[1,"row","pt-2"],[1,"col-2","ml-auto"],["mat-raised-button","","matTooltip","Cancel","type","submit",1,"iconbtnCancel",3,"click"],[2,"color","#9DA8B5"],[1,"row","pt-2","pb-3","border-bottom"],[1,"col-3"],[1,"row"],[1,"col-1x","my-auto","status-circular",3,"ngStyle"],[1,"col-2"]],template:function(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"div",0),a["\u0275\u0275elementStart"](1,"div",1),a["\u0275\u0275elementStart"](2,"div",2),a["\u0275\u0275elementStart"](3,"span",3),a["\u0275\u0275text"](4,"People Allocation History"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](5,m,38,7,"div",4),a["\u0275\u0275elementStart"](6,"div",5),a["\u0275\u0275elementStart"](7,"div",6),a["\u0275\u0275elementStart"](8,"button",7),a["\u0275\u0275listener"]("click",(function(){return t.closeClicked()})),a["\u0275\u0275elementStart"](9,"span",8),a["\u0275\u0275text"](10,"Cancel"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e&&(a["\u0275\u0275advance"](5),a["\u0275\u0275property"]("ngForOf",t.employeeHistory))},directives:[r.NgForOf,d.a,o.a,r.NgStyle],styles:[".resource-updated-styles[_ngcontent-%COMP%]   .field-title[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#6e7b8f}.resource-updated-styles[_ngcontent-%COMP%]   .status-circular[_ngcontent-%COMP%]{height:10px;line-height:13px;width:10px;border-radius:50%}"]}),e})()}}]);