(window.webpackJsonp=window.webpackJsonp||[]).push([[804],{Wurt:function(e,t,a){"use strict";a.r(t),a.d(t,"AppraisalConfigurationModule",(function(){return p}));var n=a("ofXK"),l=a("tyNb"),r=a("fXoL"),i=a("Wp6s");function o(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"div"),r["\u0275\u0275elementStart"](1,"mat-card",6),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const a=t.$implicit;return r["\u0275\u0275nextContext"]().apprasalMetrices(a.value)})),r["\u0275\u0275text"](2),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;r["\u0275\u0275advance"](1),r["\u0275\u0275styleProp"]("border-left-color",e.isSelected?"red":"gray"),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"](" ",e.name," ")}}const s=[{path:"",component:(()=>{class e{constructor(e,t,a){this.router=e,this.currentActivatedRoute=t,this.cdref=a,this.configureOptions=[{name:"Appraisal Modules",value:"modules",isSelected:!1},{name:"Appraisal Competencies",value:"metrices",isSelected:!1},{name:"Appraisal Cycles",value:"cycles",isSelected:!1},{name:"Appraisal Template",value:"template",isSelected:!1},{name:"Employee Appraisals",value:"employeeAppraisals",isSelected:!1},{name:"Annual Appraisals",value:"annualCycle",isSelected:!1},{name:"Admin Actions",value:"adminActions",isSelected:!1}]}apprasalMetrices(e){for(let t=0;t<this.configureOptions.length;t++)this.configureOptions[t].isSelected=this.configureOptions[t].value==e;console.log(this.configureOptions),this.router.navigate([e],{relativeTo:this.currentActivatedRoute})}ngOnInit(){for(let e=0;e<this.configureOptions.length;e++)this.configureOptions[e].isSelected="metrices"==this.configureOptions[e].value;this.router.navigateByUrl("/main/pms/appraisal/configure/metrices")}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](l.g),r["\u0275\u0275directiveInject"](l.a),r["\u0275\u0275directiveInject"](r.ChangeDetectorRef))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["app-appraisal-configuration"]],decls:8,vars:1,consts:[[1,""],[1,"row"],[1,"col-12"],[1,"card-tile"],[4,"ngFor","ngForOf"],[1,"mt-2"],[1,"cardView_matCard",3,"click"]],template:function(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div",0),r["\u0275\u0275elementStart"](1,"div",1),r["\u0275\u0275elementStart"](2,"div",2),r["\u0275\u0275elementStart"](3,"div",3),r["\u0275\u0275template"](4,o,3,3,"div",4),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275element"](5,"hr"),r["\u0275\u0275elementStart"](6,"div",5),r["\u0275\u0275element"](7,"router-outlet"),r["\u0275\u0275elementEnd"]()),2&e&&(r["\u0275\u0275advance"](4),r["\u0275\u0275property"]("ngForOf",t.configureOptions))},directives:[n.NgForOf,l.l,i.a],styles:[".card-tile[_ngcontent-%COMP%]{width:100%;display:flex;flex-direction:row;flex-wrap:wrap}.cardView_matCard[_ngcontent-%COMP%]{max-width:12rem;height:4rem;margin-top:1rem;margin-right:1.3rem;background-color:#fff;cursor:pointer;border-left:3px solid}"]}),e})(),children:[{path:"modules",loadChildren:()=>Promise.all([a.e(19),a.e(37),a.e(65),a.e(218)]).then(a.bind(null,"04mE")).then(e=>e.AppraisalModulesModule),data:{breadcrumb:"Appraisal Modules"}},{path:"metrices",loadChildren:()=>Promise.all([a.e(19),a.e(217)]).then(a.bind(null,"tqVv")).then(e=>e.AppraisalMetricesModule),data:{breadcrumb:"Appraisal Metrices"}},{path:"cycles",loadChildren:()=>Promise.all([a.e(19),a.e(65)]).then(a.bind(null,"qjrr")).then(e=>e.AppraisalCyclesModule),data:{breadcrumb:"Appraisal cycles"}},{path:"employeeAppraisals",loadChildren:()=>Promise.all([a.e(19),a.e(65),a.e(125),a.e(0),a.e(220)]).then(a.bind(null,"oaMd")).then(e=>e.EmployeeAppraisalsModule),data:{breadcrumb:"Employee Appraisals"}},{path:"evaluatorAppraisals",loadChildren:()=>a.e(221).then(a.bind(null,"O+rP")).then(e=>e.EvaluatorAppraisalsModule),data:{breadcrumb:"Evaluator Appraisals"}},{path:"template",loadChildren:()=>Promise.all([a.e(19),a.e(37),a.e(65),a.e(219)]).then(a.bind(null,"t4Xu")).then(e=>e.AppraisalTemplateModule),data:{breadcrumb:"Appraisal Template"}},{path:"annualCycle",loadChildren:()=>Promise.all([a.e(19),a.e(37),a.e(65),a.e(0),a.e(216)]).then(a.bind(null,"KyKM")).then(e=>e.AnnualAppraisalModule),data:{breadcrumb:"Annual Appraisal Cycle"}},{path:"adminActions",loadChildren:()=>Promise.all([a.e(19),a.e(37),a.e(65),a.e(125),a.e(215)]).then(a.bind(null,"mlSC")).then(e=>e.AdminActionsModule),data:{breadcrumb:"Admin Actions"}}]}];let d=(()=>{class e{}return e.\u0275mod=r["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=r["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[l.k.forChild(s)],l.k]}),e})();var c=a("Xi0T");let p=(()=>{class e{}return e.\u0275mod=r["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=r["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[n.CommonModule,d,i.d,c.a]]}),e})()}}]);