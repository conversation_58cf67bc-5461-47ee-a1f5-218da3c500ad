(window.webpackJsonp=window.webpackJsonp||[]).push([[879,861,977,981],{HmYF:function(e,t,n){"use strict";n.d(t,"a",(function(){return c}));var i=n("mrSG"),r=n("Iab2"),o=n("EUZL"),s=n("wd/R"),a=n("xG9w"),l=n("fXoL");let c=(()=>{class e{constructor(){this.formatColumn=(e,t,n)=>{const i=o.utils.decode_range(e["!ref"]);for(let r=i.s.r+1;r<=i.e.r;++r){const i=o.utils.encode_cell({r:r,c:t});e[i]&&e[i].v&&(e[i].t="d",e[i].z=n)}}}exportAsExcelFile(e,t,n,i,r){console.log("Excel to JSON Service",e);const s=o.utils.json_to_sheet(e);if(r&&r.length){const e=o.utils.sheet_to_json(s,{header:1}).shift();for(const t of r){const n=e.indexOf(t.fieldKey);this.formatColumn(s,n,t.fieldFormat)}}null==n&&(n=[]),null==i&&(i="DD-MM-YYYY"),this.formatExcelDateData(s,n,i);const a=o.write({Sheets:{data:s},SheetNames:["data"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(a,t)}formatExcelDateData(e,t,n){for(let o of Object.keys(e))if(null!=e[o]&&null!=e[o].t&&null!=e[o].v&&s(e[o].v,n,!0).isValid()){let i=o.replace(/[0-9]/g,"")+"1";0==a.where(t,{value:e[i].v}).length&&null!=e[i]&&null!=e[i].t&&t.push({value:e[i].v,format:n})}let i=[],r=1;for(let o of t)for(let t of Object.keys(e)){let n=parseInt(t.replace(/[^0-9]/g,""));n>r&&(r=n),null!=e[t]&&null!=e[t].v&&e[t].v==o.value&&i.push({value:t.replace(/[0-9]/g,""),format:o.format})}for(let o of i)for(let t=2;t<=r;t++)null!=e[o.value+""+t]&&null!=e[o.value+""+t].t&&(e[o.value+""+t].t="d",null!=e[o.value+""+t].v&&"Invalid date"!=e[o.value+""+t].v?e[o.value+""+t].v=s(e[o.value+""+t].v,o.format).format("YYYY/MM/DD"):(console.log(e[o.value+""+t].t),e[o.value+""+t].v="",e[o.value+""+t].t="s"))}saveAsExcelFile(e,t){const n=new Blob([e],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8"});r.saveAs(n,t.concat(".xlsx"))}exportAsExcelFileWFH(e,t,n){const i=o.utils.json_to_sheet(e),r=o.utils.json_to_sheet(t),s=o.write({Sheets:{All_Approvals:i,Pending_Approvals:r},SheetNames:["All_Approvals","Pending_Approvals"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(s,n)}exportAsExcelFileForPayroll(e,t,n,i,r,s){const a=o.utils.json_to_sheet(e),l=o.utils.json_to_sheet(t),c=o.utils.json_to_sheet(n),p=o.utils.json_to_sheet(i),u=o.utils.json_to_sheet(r),h=o.write({Sheets:{Regular_Report:a,Intern_Report:l,Contract_Report:c,Perdiem_Report:p,RP_Report:u},SheetNames:["Regular_Report","Intern_Report","Contract_Report","Perdiem_Report","RP_Report"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(h,s)}exportAsCsvFileWithSheetName(e,t){return Object(i.c)(this,void 0,void 0,(function*(){let n=o.utils.book_new();for(let t of e){let e=o.utils.json_to_sheet(t.data);o.utils.book_append_sheet(n,e,t.sheetName)}let i=o.write(n,{bookType:"xlsx",type:"binary"});yield this.saveAsCsvFile(i,t)}))}saveAsCsvFile(e,t){return Object(i.c)(this,void 0,void 0,(function*(){const n=new Blob([yield this.s2ab(e)],{type:"application/octet-stream"});r.saveAs(n,t.concat(".csv"))}))}s2ab(e){return Object(i.c)(this,void 0,void 0,(function*(){for(var t=new ArrayBuffer(e.length),n=new Uint8Array(t),i=0;i<e.length;i++)n[i]=255&e.charCodeAt(i);return t}))}exportAsExcelFileWithCellMerge(e,t,n){const i=o.utils.json_to_sheet(e);i["!merges"]=n;const r=o.write({Sheets:{data:i},SheetNames:["data"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(r,t)}exportJsonToExcelWithMultipleSheets(e,t){return Object(i.c)(this,void 0,void 0,(function*(){let n=o.utils.book_new();for(let t of e){let e=o.utils.json_to_sheet(t.data);o.utils.book_append_sheet(n,e,t.sheetName)}let i=o.write(n,{bookType:"xlsx",type:"array"});this.saveAsExcelFile(i,t)}))}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275prov=l["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},R3G1:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var i=n("xG9w"),r=n("fXoL");let o=(()=>{class e{transform(e,t,n){let r=i.findWhere(t,{field_name:e,type:n});return!!r&&!!r.is_active}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=r["\u0275\u0275definePipe"]({name:"checkActive",type:e,pure:!0}),e})()},g0Rd:function(e,t,n){"use strict";n.r(t),n.d(t,"ServicesModule",(function(){return W}));var i=n("ofXK"),r=n("bTqV"),o=n("NFeN"),s=n("Qu3c"),a=n("kmnG"),l=n("qFsG"),c=n("3Pt+"),p=n("0IaG"),u=n("1jcm"),h=n("rDax"),d=n("Xa2L"),g=n("tyNb"),m=n("fXoL"),v=n("mbKZ");const f=function(){return[84,85]};let C=(()=>{class e{constructor(){}ngOnInit(){}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=m["\u0275\u0275defineComponent"]({type:e,selectors:[["app-upload-page"]],decls:1,vars:2,consts:[[3,"app_id_list"]],template:function(e,t){1&e&&m["\u0275\u0275element"](0,"migration-cockpit",0),2&e&&m["\u0275\u0275property"]("app_id_list",m["\u0275\u0275pureFunction0"](1,f))},directives:[v.a],styles:[""]}),e})();var b=n("+rOU"),y=n("1G5W"),_=n("Kj3r"),S=n("XNiG"),O=n("quSY"),x=n("wd/R"),w=n("jr6c"),E=n("1A3m"),D=n("pgif");const q=["serviceTemplateRef"],M=["rollupTemplateRef"];function I(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div",8),m["\u0275\u0275elementStart"](1,"div",9),m["\u0275\u0275elementStart"](2,"mat-icon",10),m["\u0275\u0275text"](3,"search"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275element"](4,"input",11),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](5,"div",12),m["\u0275\u0275elementStart"](6,"button",13),m["\u0275\u0275listener"]("click",(function(){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"]().openServiceOverlay()})),m["\u0275\u0275text"](7," Create Service "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}if(2&e){const e=m["\u0275\u0275nextContext"]();m["\u0275\u0275advance"](4),m["\u0275\u0275property"]("formControl",e.searchFormControl)}}function R(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div",24),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275nextContext"]().$implicit;return m["\u0275\u0275nextContext"](2).sortThisThing(t)})),m["\u0275\u0275elementStart"](1,"mat-icon"),m["\u0275\u0275text"](2,"arrow_upward"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](3,"mat-icon"),m["\u0275\u0275text"](4,"arrow_downward"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}if(2&e){const e=m["\u0275\u0275nextContext"]().$implicit,t=m["\u0275\u0275nextContext"](2);m["\u0275\u0275styleProp"]("pointer-events",t.limitLoader?"none":"auto"),m["\u0275\u0275property"]("matTooltip",t.getSortTooltip(e.sortOrder))}}function P(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",22),m["\u0275\u0275elementStart"](1,"div"),m["\u0275\u0275text"](2),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](3,R,5,3,"div",23),m["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;m["\u0275\u0275classMap"](e.class),m["\u0275\u0275advance"](2),m["\u0275\u0275textInterpolate"](e.name),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",e.has_sort)}}function k(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementContainerStart"](0),m["\u0275\u0275elementStart"](1,"div",25),m["\u0275\u0275elementStart"](2,"div",26),m["\u0275\u0275text"](3),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](4,"div",27),m["\u0275\u0275text"](5),m["\u0275\u0275pipe"](6,"date"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](7,"div",28),m["\u0275\u0275element"](8,"img",29),m["\u0275\u0275elementStart"](9,"span",30),m["\u0275\u0275text"](10),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](11,"div",31),m["\u0275\u0275element"](12,"mat-slide-toggle",32),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](13,"div",33,34),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const n=t.$implicit,i=m["\u0275\u0275reference"](14);return m["\u0275\u0275nextContext"](2).openRollupOverlay(i,n)})),m["\u0275\u0275text"](16),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=t.$implicit,n=m["\u0275\u0275nextContext"](2);m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("matTooltip",null==e?null:e.service_name),m["\u0275\u0275advance"](1),m["\u0275\u0275textInterpolate1"](" ",null==e?null:e.service_name," "),m["\u0275\u0275advance"](2),m["\u0275\u0275textInterpolate1"](" ",m["\u0275\u0275pipeBind2"](6,8,null==e?null:e.updated_on,"dd-MMM-yy hh:mm a")," "),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("matTooltip",null==e?null:e.updated_by),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("src",e.updated_by_profile_url?n.quote.updated_by_profile_url:"https://assets.kebs.app/images/User.png",m["\u0275\u0275sanitizeUrl"]),m["\u0275\u0275advance"](2),m["\u0275\u0275textInterpolate1"](" ",null==e?null:e.updated_by," "),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("formControl",e.status),m["\u0275\u0275advance"](4),m["\u0275\u0275textInterpolate1"](" ",(null==e?null:e.roll_up_service_name)||"-"," ")}}function F(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementContainerStart"](0),m["\u0275\u0275elementStart"](1,"div",37),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const n=t.$implicit;return m["\u0275\u0275nextContext"](3).updateServiceRollup(n)})),m["\u0275\u0275text"](2),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=t.$implicit;m["\u0275\u0275advance"](2),m["\u0275\u0275textInterpolate1"](" ",null==e?null:e.service_name," ")}}function $(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",35),m["\u0275\u0275elementStart"](1,"div",36),m["\u0275\u0275template"](2,F,3,1,"ng-container",19),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"](2);m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngForOf",e.serviceList)}}function L(e,t){if(1&e&&(m["\u0275\u0275elementContainerStart"](0),m["\u0275\u0275elementStart"](1,"div",14),m["\u0275\u0275elementStart"](2,"div",15),m["\u0275\u0275elementStart"](3,"div",16),m["\u0275\u0275template"](4,P,4,4,"div",17),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](5,"div",18),m["\u0275\u0275template"](6,k,17,11,"ng-container",19),m["\u0275\u0275template"](7,$,3,1,"ng-template",20,21,m["\u0275\u0275templateRefExtractor"]),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementContainerEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]();m["\u0275\u0275advance"](4),m["\u0275\u0275property"]("ngForOf",e.columns),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngForOf",e.serviceList),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("cdkConnectedOverlayOrigin",e.overlayTrigger)}}function T(e,t){1&e&&(m["\u0275\u0275elementStart"](0,"div",38),m["\u0275\u0275element"](1,"mat-spinner",39),m["\u0275\u0275elementEnd"]()),2&e&&(m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("diameter",20))}function Q(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementContainerStart"](0),m["\u0275\u0275elementStart"](1,"div",40),m["\u0275\u0275element"](2,"img",41),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](3,"div",42),m["\u0275\u0275elementStart"](4,"span",43),m["\u0275\u0275text"](5,"No Services Here"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](6,"span",44),m["\u0275\u0275text"](7," Start creating the first service "),m["\u0275\u0275element"](8,"br"),m["\u0275\u0275text"](9," for your organisation, now. "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](10,"div",45),m["\u0275\u0275elementStart"](11,"button",46),m["\u0275\u0275listener"]("click",(function(){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"]().openServiceOverlay()})),m["\u0275\u0275text"](12," Create Service "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementContainerEnd"]()}}function j(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div",47),m["\u0275\u0275elementStart"](1,"div",15),m["\u0275\u0275elementStart"](2,"div",48),m["\u0275\u0275elementStart"](3,"div",49),m["\u0275\u0275text"](4," New Service "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](5,"div",50),m["\u0275\u0275elementStart"](6,"button",51),m["\u0275\u0275listener"]("click",(function(){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"]().closeServiceDialog()})),m["\u0275\u0275elementStart"](7,"mat-icon",52),m["\u0275\u0275text"](8,"close"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](9,"div",53),m["\u0275\u0275text"](10," Service Name "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](11,"div",54),m["\u0275\u0275elementStart"](12,"mat-form-field",55),m["\u0275\u0275element"](13,"input",56),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](14,"div",57),m["\u0275\u0275elementStart"](15,"button",58),m["\u0275\u0275listener"]("click",(function(){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"]().closeServiceDialog()})),m["\u0275\u0275text"](16," Cancel "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](17,"button",59),m["\u0275\u0275listener"]("click",(function(){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"]().addSevice()})),m["\u0275\u0275text"](18," Create "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}if(2&e){const e=m["\u0275\u0275nextContext"]();m["\u0275\u0275advance"](13),m["\u0275\u0275property"]("formControl",e.addServiceFormControl),m["\u0275\u0275advance"](4),m["\u0275\u0275property"]("disabled",!e.addServiceFormControl.value)("ngClass",e.addServiceFormControl.value?"create-btn":"create-btn-disabled")}}const A=[{path:"",redirectTo:"list"},{path:"upload",component:C},{path:"list",component:(()=>{class e{constructor(e,t,n,i,r){this.overlay=e,this.viewContainerRef=t,this._quoteService=n,this._toaster=i,this.opportunityService=r,this._onDestroy=new S.b,this.serviceList=[],this.serviceListForSearch=[],this.searchFormControl=new c.j(""),this.addServiceFormControl=new c.j(null),this.valueChangeSubscription=new O.a,this.isDataLoading=!1,this.currentServiceItem=null,this.columns=[{name:"SERVICE NAME",class:"col-3",key_name:"service_name",has_sort:!0},{name:"UPDATED ON",class:"col-2",key_name:"updated_on",has_sort:!0},{name:"UPDATED BY",class:"col-2",key_name:"updated_by",has_sort:!0},{name:"STATUS",class:"col-2",key_name:"status",has_sort:!1},{name:"ROLL UP SERVICE",class:"col-3",key_name:"roll_up_service_name",has_sort:!0}],this.getServices=()=>{this.serviceList=[],this.isDataLoading=!0,this._quoteService.getServices().pipe(Object(y.a)(this._onDestroy)).subscribe(e=>{"S"==e.messType&&e.data&&e.data.length?this.resolveServiceList(e.data):this._toaster.showInfo("No Services","No Services found !",this.opportunityService.mediumInterval),this.isDataLoading=!1},e=>{console.log(e),this.isDataLoading=!1,this._toaster.showError("Error","Error in getting Services list",this.opportunityService.mediumInterval)})},this.resolveServiceList=e=>{for(const t of e){const e={service_header_id:t.service_header_id,service_name:t.service_name,updated_on:t.updated_on,updated_by:t.updated_by,status:new c.j(1===t.status),roll_up_service_id:t.roll_up_service_id,roll_up_service_name:t.roll_up_service_name};this.valueChangeSubscription.add(e.status.valueChanges.pipe(Object(_.a)(500),Object(y.a)(this._onDestroy)).subscribe(t=>{this.updateServiceStatus(e.service_header_id,t?1:0)})),this.serviceList.push(e)}this.serviceListForSearch=this.serviceList},this.openServiceOverlay=()=>{var e;if(!(null===(e=this.serviceOverlayRef)||void 0===e?void 0:e.hasAttached())){this.addServiceFormControl.reset();const e=this.overlay.position().global().centerHorizontally().centerVertically();this.serviceOverlayRef=this.overlay.create({positionStrategy:e,hasBackdrop:!0});const t=new b.h(this.templateRef,this.viewContainerRef);this.serviceOverlayRef.attach(t),this.serviceOverlayRef.backdropClick().subscribe(()=>{this.closeServiceDialog()})}},this.closeServiceDialog=()=>{var e;this.addServiceFormControl.reset(),null===(e=this.serviceOverlayRef)||void 0===e||e.dispose()},this.addSevice=()=>{this._quoteService.createService(this.addServiceFormControl.value).pipe(Object(y.a)(this._onDestroy)).subscribe(e=>{"S"==e.messType&&e.data?(this.getServices(),this.closeServiceDialog(),this._toaster.showSuccess("Success",e.messText,2e3)):this._toaster.showError("Error",e.messText,this.opportunityService.mediumInterval)},e=>{console.log(e),this._toaster.showError("Error","Error in adding Service",this.opportunityService.mediumInterval)})},this.updateServiceStatus=(e,t)=>{this._quoteService.editServiceStatus(e,t).pipe(Object(y.a)(this._onDestroy)).subscribe(e=>{"S"==e.messType&&this._toaster.showSuccess("Updated","Service status updated Successfully !",2e3)},e=>{console.log(e),this._toaster.showError("Error","Error in updating Service status",this.opportunityService.mediumInterval)})},this.closeRollupOverlay=()=>{var e;this.currentServiceItem=null,null===(e=this.rollupOverlayRef)||void 0===e||e.dispose()},this.updateServiceRollup=e=>{if(this.currentServiceItem&&e){if(e.service_header_id===this.currentServiceItem.service_header_id)return this._toaster.showError("Error","Cannot Rollup to Same Service",this.opportunityService.mediumInterval);if(e.roll_up_service_id===this.currentServiceItem.service_header_id)return this._toaster.showError("Error","Cannot Rollup to Parent Service",this.opportunityService.mediumInterval);if(e.service_header_id===this.currentServiceItem.roll_up_service_id)return this.closeRollupOverlay();this._quoteService.updateServiceRollup(this.currentServiceItem.service_header_id,e.service_header_id).pipe(Object(y.a)(this._onDestroy)).subscribe(t=>{"S"==t.messType?(this.currentServiceItem.roll_up_service_id=e.service_header_id,this.currentServiceItem.roll_up_service_name=e.service_name,this._toaster.showSuccess("Updated","Service Rollup Updated Successfully !",2e3)):this._toaster.showError("Error","Error in updating Service rollup",this.opportunityService.mediumInterval),this.closeRollupOverlay()},e=>{console.log(e),this._toaster.showError("Error","Error in updating Service rollup",this.opportunityService.mediumInterval),this.closeRollupOverlay()})}else this._toaster.showError("Error","Service not Selected Properly !",this.opportunityService.mediumInterval)}}ngOnInit(){this.getServices(),this.valueChangeSubscription=this.searchFormControl.valueChanges.pipe(Object(_.a)(500),Object(y.a)(this._onDestroy)).subscribe(e=>{if(e){this.dynamicSearchParameter=e;const t=e.replace(/[-/\\^$*+?.()|[\]{}]/g,"\\$&"),n=new RegExp(t,"i");this.serviceList=this.serviceListForSearch.filter(t=>{const r=Object(i.formatDate)(t.updated_on,"dd-MMM-yy","en-GB");let o=!1;return["DD MMM YYYY","DD-MMM-YYYY","DD/MM/YYYY","dd-MMM-yy","dd MMM yy"].forEach(n=>{x(t.updated_on,n,!0).isValid()&&x(t.updated_on,n).format("DD/MM/YYYY").includes(e)&&(o=!0)}),n.test(t.service_name)||n.test(t.roll_up_service_name)||n.test(t.updated_by)||n.test(r)||o})}else this.serviceList=this.serviceListForSearch})}openRollupOverlay(e,t){var n;if(!(null===(n=this.rollupOverlayRef)||void 0===n?void 0:n.hasAttached())){this.currentServiceItem=t;const n=this.overlay.position().flexibleConnectedTo(e).withFlexibleDimensions(!0).withPush(!0).withViewportMargin(25).withGrowAfterOpen(!0).withPositions([{originX:"start",originY:"bottom",overlayX:"start",overlayY:"top"}]),i=this.overlay.scrollStrategies.close();this.rollupOverlayRef=this.overlay.create({positionStrategy:n,scrollStrategy:i,hasBackdrop:!0,backdropClass:"service-transparent-overlay-backdrop"});const r=new b.h(this.rollupTemplateRef,this.viewContainerRef);this.rollupOverlayRef.attach(r),this.rollupOverlayRef.backdropClick().subscribe(()=>{this.closeRollupOverlay()})}}sortThisThing(e){e.sortOrder=e.sortOrder?"A"===e.sortOrder?"D":"D"===e.sortOrder?"N":"A":"A","A"===e.sortOrder?this.serviceList=this.serviceList.sort((t,n)=>{const i=this.getKeyName(t,e.key_name),r=this.getKeyName(n,e.key_name);return this.compare(i,r)}):"D"===e.sortOrder?this.serviceList=this.serviceList.sort((t,n)=>{const i=this.getKeyName(t,e.key_name),r=this.getKeyName(n,e.key_name);return this.compare(r,i)}):"N"===e.sortOrder?this.serviceList.sort((e,t)=>{const n=new Date(e.updated_on).getTime();return new Date(t.updated_on).getTime()-n}):null==this.dynamicSearchParameter&&""==this.dynamicSearchParameter||(this.serviceList=[...this.serviceListForSearch])}getKeyName(e,t){const n=e[t];return"number"==typeof n?n:"string"==typeof n?n.toLowerCase():n||""}compare(e,t){return e<t?-1:e>t?1:0}getSortTooltip(e){switch(e){case"A":return"Sort Descending";case"D":return"Remove Sort";case"N":return"Sort Ascending";default:return"Sort"}}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}}return e.\u0275fac=function(t){return new(t||e)(m["\u0275\u0275directiveInject"](h.e),m["\u0275\u0275directiveInject"](m.ViewContainerRef),m["\u0275\u0275directiveInject"](w.a),m["\u0275\u0275directiveInject"](E.a),m["\u0275\u0275directiveInject"](D.a))},e.\u0275cmp=m["\u0275\u0275defineComponent"]({type:e,selectors:[["app-landing-page"]],viewQuery:function(e,t){if(1&e&&(m["\u0275\u0275viewQuery"](q,!0),m["\u0275\u0275viewQuery"](M,!0)),2&e){let e;m["\u0275\u0275queryRefresh"](e=m["\u0275\u0275loadQuery"]())&&(t.templateRef=e.first),m["\u0275\u0275queryRefresh"](e=m["\u0275\u0275loadQuery"]())&&(t.rollupTemplateRef=e.first)}},decls:11,vars:5,consts:[[1,"container-fluid","service-def-styles"],[1,"page-header"],[1,"service-count"],["class","toolbar",4,"ngIf"],[4,"ngIf"],["class","row w-100 justify-content-center mt-5",4,"ngIf"],["cdkConnectedOverlay",""],["serviceTemplateRef",""],[1,"toolbar"],[1,"search-container"],[2,"font-size","18px"],["placeholder","Search",1,"input-search",3,"formControl"],[1,"create-action"],["mat-button","",1,"create-btn",3,"click"],[1,"card","ml-2","mr-2"],[1,"card-body","p-2"],[1,"row","pt-2","pb-2","label-class","bottom-border"],["class","column-header",3,"class",4,"ngFor","ngForOf"],[2,"height","65vh","overflow-x","hidden"],[4,"ngFor","ngForOf"],["cdkConnectedOverlay","",3,"cdkConnectedOverlayOrigin"],["rollupTemplateRef",""],[1,"column-header"],["class","sorting-thing",3,"pointer-events","matTooltip","click",4,"ngIf"],[1,"sorting-thing",3,"matTooltip","click"],[1,"row","pt-2","pb-2","bottom-border","d-flex","align-items-center"],[1,"col-3","service-name",3,"matTooltip"],[1,"col-2","other-name"],[1,"col-2","other-name",3,"matTooltip"],["height","28px","width","28px",3,"src"],[1,"pl-2"],[1,"col-2",2,"padding-left","22px"],[3,"formControl"],["cdkOverlayOrigin","",1,"col-3",2,"cursor","pointer",3,"click"],["rollupContainer","","overlayTrigger","cdkOverlayOrigin"],[1,"card",2,"min-width","18vw"],[1,"card-body","p-1",2,"max-height","30vh","overflow-y","scroll"],[1,"row","p-1","pl-4","col-12",2,"cursor","pointer",3,"click"],[1,"row","w-100","justify-content-center","mt-5"],[2,"color","#cf0000",3,"diameter"],[1,"d-flex","justify-content-center","align-items-center","slide-from-down"],["src","https://assets.kebs.app/images/no_data_found.png","height","150","width","200",1,"mt-5","mb-3"],[1,"d-flex","flex-column","pb-2","justify-content-center","align-items-center","slide-in-top"],[2,"font-weight","600","color","#191729"],[2,"font-size","13px","color","#5f5f5f","font-weight","500"],[1,"d-flex","justify-content-center","slign-items-center","slide-from-down"],["mat-raised-button","",1,"create-btn","mt-2",3,"click"],[1,"card",2,"min-width","25vw"],[1,"row","pt-2","pb-1"],[1,"col-10","d-flex","align-items-center",2,"font-weight","500","color","#45546E"],[1,"col-2"],["mat-icon-button","",3,"click"],[2,"font-size","15px","color","#1C1B1F"],[1,"row","col-12","service-label"],[1,"row","col-12"],["appearance","outline",2,"width","100%","font-size","13px"],["matInput","","placeholder","Enter here",3,"formControl"],[1,"row","col-12","pt-1","pb-2","pl-2"],["mat-raised-button","",1,"cancel-btn","ml-2","mt-2",3,"click"],["mat-raised-button","",1,"ml-2","mt-2",3,"disabled","ngClass","click"]],template:function(e,t){1&e&&(m["\u0275\u0275elementStart"](0,"div",0),m["\u0275\u0275elementStart"](1,"div",1),m["\u0275\u0275elementStart"](2,"div",2),m["\u0275\u0275elementStart"](3,"p"),m["\u0275\u0275text"](4),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](5,I,8,1,"div",3),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](6,L,9,3,"ng-container",4),m["\u0275\u0275template"](7,T,2,1,"div",5),m["\u0275\u0275template"](8,Q,13,0,"ng-container",4),m["\u0275\u0275template"](9,j,19,3,"ng-template",6,7,m["\u0275\u0275templateRefExtractor"]),m["\u0275\u0275elementEnd"]()),2&e&&(m["\u0275\u0275advance"](4),m["\u0275\u0275textInterpolate1"]("",t.serviceList.length," Services"),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",t.serviceListForSearch.length),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",t.serviceList.length),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",t.isDataLoading),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",0==(null==t.serviceList?null:t.serviceList.length)&&!t.isDataLoading))},directives:[i.NgIf,h.a,o.a,c.e,c.v,c.k,r.a,i.NgForOf,s.a,u.a,h.b,d.c,a.c,l.b,i.NgClass],pipes:[i.DatePipe],styles:[".service-def-styles[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding:15px}.service-def-styles[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .toolbar[_ngcontent-%COMP%]{flex:1;justify-content:end;grid-template-columns:repeat(4,auto);grid-gap:24px;display:grid;padding-inline:24px;align-items:center}.service-def-styles[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .toolbar[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:18px;cursor:pointer}.service-def-styles[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .toolbar[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]{color:#45546e;padding-right:12px;border-right:2px solid #e8e9ee;font-size:16px;height:32px;display:flex;justify-content:end;align-items:center}.service-def-styles[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .toolbar[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:18px;margin:0}.service-def-styles[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .toolbar[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .input-search[_ngcontent-%COMP%]{font-size:14px;outline:none;border:none;min-width:65px;max-width:65px;background:none;color:inherit;transition:all 1.5s;width:auto;white-space:nowrap}.service-def-styles[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .toolbar[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .input-search[_ngcontent-%COMP%]:focus, .service-def-styles[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .toolbar[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .input-search[_ngcontent-%COMP%]:not(:placeholder-shown){min-width:150px;max-width:400px;width:auto;white-space:normal}.service-def-styles[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .toolbar[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .input-search[_ngcontent-%COMP%]::placeholder{color:inherit;opacity:1}.service-def-styles[_ngcontent-%COMP%]   .page-header[_ngcontent-%COMP%]   .service-count[_ngcontent-%COMP%]{color:#45546e;font-size:14px;font-weight:600}.service-def-styles[_ngcontent-%COMP%]   .service-label[_ngcontent-%COMP%]{font-size:12px;color:#5f6c81;line-height:8px}.service-def-styles[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{background:#fff;font-size:12px!important;line-height:35px;border-radius:4px}.service-def-styles[_ngcontent-%COMP%]   .label-class[_ngcontent-%COMP%]{font-size:11px;color:#5f6c81;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;line-height:10px}.service-def-styles[_ngcontent-%COMP%]   .label-class[_ngcontent-%COMP%]   .sorting-thing[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;margin-top:-1%}.service-def-styles[_ngcontent-%COMP%]   .label-class[_ngcontent-%COMP%]   .sorting-thing[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:12px;color:#b9c0ca;font-weight:600;width:10px;cursor:pointer}.service-def-styles[_ngcontent-%COMP%]   .column-header[_ngcontent-%COMP%]{display:flex;align-items:start;justify-content:start;column-gap:2%}.service-def-styles[_ngcontent-%COMP%]   .bottom-border[_ngcontent-%COMP%]{border-bottom:1px solid #dadce2}.service-def-styles[_ngcontent-%COMP%]     .mat-slide-toggle-thumb{width:10px!important;height:10px!important;transform:translate(50%,50%)}.service-def-styles[_ngcontent-%COMP%]     .mat-slide-toggle-bar{background-color:#dadce2;border-radius:15px!important;height:16px!important}.service-def-styles[_ngcontent-%COMP%]     .mat-slide-toggle-thumb-container{top:-2px!important}.service-def-styles[_ngcontent-%COMP%]     .mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-bar{background-color:#52c41a}.service-def-styles[_ngcontent-%COMP%]     .mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-thumb{background-color:#fff}.service-def-styles[_ngcontent-%COMP%]   .service-name[_ngcontent-%COMP%]{font-weight:600}.service-def-styles[_ngcontent-%COMP%]   .other-name[_ngcontent-%COMP%], .service-def-styles[_ngcontent-%COMP%]   .service-name[_ngcontent-%COMP%]{color:#45546e;font-size:13px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.create-btn[_ngcontent-%COMP%]{font-size:13px;color:#fff;font-weight:700!important;background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%);border-radius:4px}.create-btn-disabled[_ngcontent-%COMP%]{font-size:12px!important;line-height:30px;border-radius:4px}.service-transparent-overlay-backdrop[_ngcontent-%COMP%]    .cdk-overlay-backdrop.cdk-overlay-backdrop-showing{opacity:0}"]}),e})()}];let N=(()=>{class e{}return e.\u0275mod=m["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=m["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[g.k.forChild(A)],g.k]}),e})();var z=n("Xi0T");let W=(()=>{class e{}return e.\u0275mod=m["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=m["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,N,z.a,r.b,o.b,s.b,a.e,l.c,c.E,p.g,u.b,h.h,d.b]]}),e})()},jr6c:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var i=n("mrSG"),r=n("2Vo4"),o=n("fXoL"),s=n("tk/3");let a=(()=>{class e{constructor(e){this.$http=e,this.initialQuoteDetail$=new r.a({quoteName:"",currency:"",deliveryStartDate:null,deliveryEndDate:null,businessType:null,serviceTypeId:null,quoteType:""}),this.intialQuoteDetail=this.initialQuoteDetail$.asObservable(),this.getCurrentQuoteConfig=(e,t)=>{for(const n of e)if(n.quote_config_name===t)return n;return null},this.getOpportunityDocumentTypes=()=>Object(i.c)(this,void 0,void 0,(function*(){try{return yield this.$http.post("/api/opportunity/getOpportunityDocumentTypes",{}).toPromise()}catch(e){throw console.error("Error retrieving document types:",e),e}})),this.formActionSubject=new r.a(this.getStoredFormAction()),this.formAction$=this.formActionSubject.asObservable()}setInitalQuoteDetail(e){this.initialQuoteDetail$.next(e)}getNationalityList(){return this.$http.post("/api/qb/master/nationalityList",{})}getWorkLocation(){return this.$http.post("/api/qb/master/workLocation",{})}getPositionList(){return this.$http.post("/api/qb/master/positionList",{})}getSkillList(){return this.$http.post("/api/qb/master/skillList",{})}getSkillExperience(){return this.$http.post("/api/qb/master/skillExperience",{})}getWorkExperience(){return this.$http.post("/api/qb/master/workExperience",{})}getUOM(){return this.$http.post("/api/qb/master/getUOM",{})}getCustomPercentage(){return this.$http.post("/api/qb/master/getCustomPercentage",{})}getTaxDetails(){return this.$http.post("/api/qb/master/getTaxDetails",{})}getDiscountDetails(){return this.$http.post("/api/qb/master/getDiscountDetails",{})}getCurrencyList(){return this.$http.post("/api/qb/master/getCurrencyList",{})}getServiceList(e,t=[],n,i,r){return this.$http.post("/api/qb/serviceConfig/getServiceList",{opportunityId:e,mandatoryFields:t,defaultCurrency:n,quoteCurrency:i,conversionTypeId:r})}getDivision(){return this.$http.post("/api/qb/master/getDivision",{})}getSubDivision(){return this.$http.post("/api/qb/master/getSubDivision",{})}getNonManPowerList(e,t,n,i){return this.$http.post("/api/qb/master/getNonManPowerList",{opportunityId:e,defaultCurrency:t,quoteCurrency:n,conversionTypeId:i})}createQuote(e){return this.$http.post("/api/qb/quote/createQuote",{quoteDetails:e})}getEntity(){return this.$http.post("/api/qb/master/getEntity",{})}getQuoteDetails(e){return this.$http.post("/api/qb/quote/getQuoteDetails",{quoteId:e})}getQuoteCalendarData(e,t,n,i=!0,r=1,o=1){return this.$http.post("/api/qb/quote/getQuoteCalendarData",{startDate:e,endDate:t,positionDetails:n,fetchSavedData:i,calendarId:r,workScheduleId:o})}getOpportunityMetaDetailsForQuote(e){return this.$http.post("/api/qb/quote/getOpportunityMetaDetailsForQuote",{opportunityId:e})}getQuoteTypeConfig(e){return this.$http.post("/api/qb/quote/getQuoteTypeConfig",{quoteType:e})}updateQuote(e){return this.$http.post("/api/qb/quote/updateQuote",{quoteDetails:e})}getQuoteActivity(e,t=!1){return this.$http.post("/api/qb/quote/getQuoteActivity",{quoteId:e,showQuoteValue:t})}resolveQuoteOppIntegration(e,t,n,i=!0,r){return this.$http.post("/api/qb/quote/resolveQuoteOppIntegration",{opportunityId:e,opportunityStatus:t,changeType:n,updateValue:i,quoteDetails:r})}updateValueInOpportunity(e,t,n){return this.$http.post("/api/qb/quote/updateValueInOpportunity",{opportunityId:e,quoteId:t,quoteDetails:n})}getOpportunityFYData(e,t,n){return this.$http.post("/api/qb/quote/getOpportunityFYData",{opportunityId:e,opportunityCurrency:t,isFrom:n})}getQuoteConfiguration(e=[]){return this.$http.post("/api/qb/master/getQuoteConfiguration",{config:e})}updateQuoteConfiguration(e=[]){return this.$http.post("/api/qb/master/updateQuoteConfiguration",{config:e})}getServices(e=!1){return this.$http.post("/api/qb/serviceConfig/getServices",{activeService:e})}createService(e){return this.$http.post("/api/qb/serviceConfig/createService",{serviceName:e})}editServiceStatus(e,t){return this.$http.post("/api/qb/serviceConfig/editServiceStatus",{serviceId:e,status:t})}getCustomerMaster(){return this.$http.post("/api/invoice/customerMasterData",{})}getLicenseConfiguration(){return this.$http.post("/api/qb/master/getLicenseConfiguration",{})}insertLicenseConfiguration(e){return this.$http.post("/api/qb/master/insertLicenseConfiguration",{licenseData:e})}updateLicenseConfiguration(e){return this.$http.post("/api/qb/master/updateLicenseConfiguration",{licenseData:e})}getLicenseList(e,t,n,i){return this.$http.post("/api/qb/master/getLicenseList",{opportunityId:e,defaultCurrency:t,quoteCurrency:n,conversionTypeId:i})}getRate(e,t,n,i,r){return this.$http.post("/api/qb/rateCard/getRate",{rateFieldDetails:e,opportunityId:t,defaultCurrency:n,quoteCurrency:i,conversionTypeId:r})}getQuantityForPosition(e,t,n,i=1,r=1){return this.$http.post("/api/qb/quote/getQuantityForPosition",{startDate:e,endDate:t,unitConfig:n,calendarId:i,workScheduleId:r})}getOpportunityValueChangeLog(e){return this.$http.post("/api/qb/quote/getOpportunityValueChangeLog",{opportunityId:e})}getOpportunityAuditChangeLogforField(e,t){return this.$http.post("/api/opportunity/getOpportunityAuditChangeLogforField",{opportunityId:e,fieldName:t})}getOppQuoteValueStatus(e){return this.$http.post("/api/qb/quote/getOppQuoteValueStatus",{opportunityId:e})}getUserFieldConfig(){return this.$http.post("/api/qb/quote/getUserFieldConfig",{})}updateUserFieldConfig(e){return this.$http.post("/api/qb/quote/updateUserFieldConfig",{fieldConfig:e})}updateInActiveQuoteInOpportunity(e){return this.$http.post("/api/qb/quote/updateInActiveQuoteInOpportunity",{opportunityId:e})}updateDateChangeInQuote(e,t=null){return this.$http.post("/api/qb/quote/updateDateChangeInQuote",{opportunityId:e,quoteId:t})}createQuoteFromSourceQuote(e,t,n,i=null,r){return this.$http.post("/api/qb/quote/createQuoteFromSourceQuote",{sourceQuoteId:e,quoteName:t,oppDetails:n,copyOption:i,quoteType:r})}updateServiceRollup(e,t){return this.$http.post("/api/qb/serviceConfig/updateServiceRollup",{serviceId:e,rollupServiceId:t})}insertQuoteUOM(e){return this.$http.post("/api/qb/master/insertQuoteUOM",{uomData:e})}updateQuoteUOM(e){return this.$http.post("/api/qb/master/updateQuoteUOM",{uomData:e})}insertCustomPercentage(e){return this.$http.post("/api/qb/master/insertCustomPercentage",{cpData:e})}updateCustomPercentage(e){return this.$http.post("/api/qb/master/updateCustomPercentage",{cpData:e})}getOpportunityQuoteList(e,t,n,i){return this.$http.post("/api/qb/quote/getOpportunityQuoteList",{opportunityId:e,limit:t,offset:n,searchQuery:i})}getOppStatusConfig(){return this.$http.post("/api/qb/master/getOppStatusConfig",{})}updateOppStatusConfig(e){return this.$http.post("/api/qb/master/updateOppStatusConfig",{statusConfig:e})}insertDiscount(e){return this.$http.post("/api/qb/master/insertDiscount",{discountData:e})}updateDiscount(e){return this.$http.post("/api/qb/master/updateDiscount",{discountData:e})}insertTax(e){return this.$http.post("/api/qb/master/insertTax",{taxData:e})}updateTax(e){return this.$http.post("/api/qb/master/updateTax",{taxData:e})}deleteQuote(e,t){return this.$http.post("/api/qb/quote/deleteQuote",{quoteId:e,opportunityId:t})}getQuoteAccessPrivilege(e){return this.$http.post("/api/qb/quote/getQuoteAccessPrivilege",{opportunityId:e})}getQuotePosition(){return this.$http.post("/api/qb/master/getQuotePosition",{})}insertQuotePosition(e){return this.$http.post("/api/qb/master/insertQuotePosition",{positionData:e})}updateQuotePosition(e){return this.$http.post("/api/qb/master/updateQuotePosition",{positionData:e})}getQuoteWorkLocationEntityMapping(){return this.$http.post("/api/qb/master/getQuoteWorkLocationEntityMapping",{})}getRevenueRegionEntitySalesMappingList(){return this.$http.post("/api/qb/master/getRevenueRegionEntitySalesMappingList",{})}insertQuoteWorkLocationEntityMapping(e){return this.$http.post("/api/qb/master/insertQuoteWorkLocationEntityMapping",{mappingData:e})}updateQuoteWorkLocationEntityMapping(e){return this.$http.post("/api/qb/master/updateQuoteWorkLocationEntityMapping",{mappingData:e})}insertQuoteEntitySalesRegionRevenueRegionMapping(e){return this.$http.post("/api/qb/master/insertQuoteEntitySalesRegionRevenueRegionMapping",{mappingData:e})}updateQuoteEntitySalesRegionRevenueRegionMapping(e){return this.$http.post("/api/qb/master/updateQuoteEntitySalesRegionRevenueRegionMapping",{mappingData:e})}getQuoteServiceDivisionMapping(){return this.$http.post("/api/qb/master/getQuoteServiceDivisionMapping",{})}insertQuoteServiceDivisionMapping(e){return this.$http.post("/api/qb/master/insertQuoteServiceDivisionMapping",{mappingData:e})}updateQuoteServiceDivisionMapping(e){return this.$http.post("/api/qb/master/updateQuoteServiceDivisionMapping",{mappingData:e})}getMonthlyRevenueProjection(){return this.$http.post("/api/qb/quote/getMonthlyRevenueProjection",{})}getRcFieldConfiguration(e){return this.$http.post("/api/qb/master/getRcFieldConfiguration",{rcType:e})}updateRcFieldConfig(e){return this.$http.post("/api/qb/master/updateRcFieldConfig",{fieldConfig:e})}getRateCardConfigurationDataConfig(){return this.$http.post("/api/qb/master/getRateCardConfigurationDataConfig",{})}getRCMasterDataList(e){return this.$http.post("/api/qb/master/getRCMasterDataList",{id:e})}insertRCMasterData(e,t){return this.$http.post("/api/qb/master/insertRCMasterData",{data:e,tableData:t})}updateRCMasterData(e,t){return this.$http.post("/api/qb/master/updateRCMasterData",{data:e,tableData:t})}getSBHBProjection(){return this.$http.post("/api/qb/quote/getSBHBProjection",{})}getNonEditableStatus(){return this.$http.post("/api/qb/master/getNonEditableStatus",{})}getFinancialDocumentConfig(){return this.$http.post("/api/opportunity/FD/config",{})}getFinancialDocumentSalesOrgConfig(){return this.$http.post("/api/opportunity/FD/approver/sales-org-config",{})}getDocUploadStageWiseConfig(){return this.$http.post("/api/opportunity/FD/getDocUploadStageWiseConfig",{})}stageWiseDocUploadUpdate(e){return this.$http.post("/api/opportunity/FD/stageWiseDocUploadUpdate",{config:e})}updateFinancialDocApproverConfig(e){return this.$http.post("/api/opportunity/FD/config/update",{config:e})}updateQuoteActivationConfig(e){return this.$http.post("/api/qb/quote/updateQuoteActivationConfig",{config:e})}getQuoteActivationApproverConfig(){return this.$http.post("/api/qb/quote/getQuoteActivationApproverConfig",{})}updateFinancialDocSalesOrgApproverConfig(e){return this.$http.post("/api/opportunity/FD/approver/sales-org-config/update",{config:e})}getAllMembers(){return this.$http.post("/api/salesMaster/getAllUserFromDB",{config:{}})}getSalesRegionMaster(){return this.$http.post("/api/opportunity/getSalesRegionMaster",{config:{}})}getCalendarCombinationConfig(){return this.$http.post("/api/qb/quote/getCalendarCombinationConfig",{})}getRevenueRegionCombinationConfig(){return this.$http.post("/api/qb/quote/getRevenueRegionCombinationConfig",{})}checkMilestoneApplicable(e,t){return this.$http.post("/api/qb/quote/checkOpportunityIsApplicableForMileStone",{opportunity_id:e,quote_id:t})}getMilestoneListQB(e){return this.$http.post("/api/qb/master/getMilestoneList",{quote_position_ids:e})}checkForPercentageApproval(e,t=!1){return new Promise((n,i)=>{this.$http.post("/api/qb/quote/checkForPercentageApproval",{quote:e,is_from_edit:t}).subscribe(e=>{n("S"===e.messType&&e)},e=>{i(e)})})}checkQuoteAllocation(e){return new Promise((t,n)=>{this.$http.post("/api/qb/quote/checkAllocation",{quote_header_id:e}).subscribe(e=>{t(e||!1)},e=>{n(e)})})}setFormAction(e,t,n,i){const r={action:e,rcType:t,rcLabel:n,forUpdate:i};this.formActionSubject.next(r),localStorage.setItem("formAction",JSON.stringify(r))}getStoredFormAction(){const e=localStorage.getItem("formAction");return e?JSON.parse(e):{action:"",rcType:null,rcLabel:null}}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275inject"](s.c))},e.\u0275prov=o["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},os0P:function(e,t,n){"use strict";n.d(t,"a",(function(){return _}));var i=n("mrSG"),r=n("fXoL"),o=n("3Pt+"),s=n("jtHE"),a=n("XNiG"),l=n("NJ67"),c=n("1G5W"),p=n("xG9w"),u=n("t44d"),h=n("kmnG"),d=n("ofXK"),g=n("d3UM"),m=n("FKr1"),v=n("WJ5W"),f=n("Qu3c");const C=["singleSelect"];function b(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"mat-option",6),r["\u0275\u0275text"](1,"Select one"),r["\u0275\u0275elementEnd"]()),2&e&&r["\u0275\u0275property"]("value",null)}function y(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"mat-option",7),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const n=t.$implicit;return r["\u0275\u0275nextContext"]().emitChanges(n)})),r["\u0275\u0275text"](1),r["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;r["\u0275\u0275propertyInterpolate"]("matTooltip",e.name),r["\u0275\u0275property"]("value",e.id),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let _=(()=>{class e extends l.a{constructor(e,t){super(),this.renderer=e,this.pmMasterService=t,this.fieldCtrl=new o.j,this.fieldFilterCtrl=new o.j,this.list=[],this.required=!1,this.valueChange=new r.EventEmitter,this.disabled=!1,this.disableColor="#E8E9EE",this.disableField=!1,this.showSelect=!0,this.uniqueList=[],this.filteredList=new s.a,this.change=new r.EventEmitter,this._onDestroy=new a.b,this.formConfig=[],this.emitChanges=e=>{this.change.emit(e)}}ngOnInit(){return Object(i.c)(this,void 0,void 0,(function*(){yield this.pmMasterService.getPMFormCustomizeConfigV().then(e=>{e&&(this.formConfig=e)});const e=p.where(this.formConfig,{type:"project-theme",field_name:"styles",is_active:!0});this.fontStyle=e.length>0&&e[0].data.font_style?e[0].data.font_style:"Roboto",document.documentElement.style.setProperty("--inputSearchFont",this.fontStyle),this.fieldFilterCtrl.valueChanges.pipe(Object(c.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(c.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)}),this.updateFilteredList()}))}ngOnChanges(){this.updateFilteredList()}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value,t=this.list.slice();e&&(e=e.toLowerCase(),t=t.filter(t=>t.name.toLowerCase().indexOf(e)>-1)),this.uniqueList.length>0&&(t=t.filter(e=>this.uniqueList.includes(e))),this.filteredList.next(t)}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}isDisabled(){return this.disableField?{"background-color":null!=this.disableColor?this.disableColor:"#E8E9EE","pointer-events":"none",color:"#6E7B8F"}:{}}writeValue(e){this.fieldCtrl.setValue(e)}updateFilteredList(){this.uniqueList.length>0?this.filterBanks():this.filteredList.next(this.list.slice())}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](r.Renderer2),r["\u0275\u0275directiveInject"](u.a))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search-name"]],viewQuery:function(e,t){if(1&e&&r["\u0275\u0275viewQuery"](C,!0),2&e){let e;r["\u0275\u0275queryRefresh"](e=r["\u0275\u0275loadQuery"]())&&(t.singleSelect=e.first)}},inputs:{list:"list",placeholder:"placeholder",required:"required",disabled:"disabled",disableColor:"disableColor",disableField:"disableField",showSelect:"showSelect",uniqueList:"uniqueList"},outputs:{valueChange:"valueChange",change:"change"},features:[r["\u0275\u0275ProvidersFeature"]([{provide:o.t,useExisting:Object(r.forwardRef)(()=>e),multi:!0}]),r["\u0275\u0275InheritDefinitionFeature"],r["\u0275\u0275NgOnChangesFeature"]],decls:8,vars:11,consts:[["appearance","outline",1,"app-input-search",3,"ngStyle"],["nameOnly","",3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value",4,"ngIf"],[3,"value","matTooltip","click",4,"ngFor","ngForOf"],[3,"value"],[3,"value","matTooltip","click"]],template:function(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"mat-form-field",0),r["\u0275\u0275elementStart"](1,"mat-select",1,2),r["\u0275\u0275elementStart"](3,"mat-option"),r["\u0275\u0275element"](4,"ngx-mat-select-search",3),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](5,b,2,1,"mat-option",4),r["\u0275\u0275template"](6,y,2,3,"mat-option",5),r["\u0275\u0275pipe"](7,"async"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e&&(r["\u0275\u0275property"]("ngStyle",t.isDisabled()),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",t.showSelect),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngForOf",r["\u0275\u0275pipeBind1"](7,9,t.filteredList)))},directives:[h.c,d.NgStyle,g.c,o.v,o.k,o.F,m.p,v.a,d.NgIf,d.NgForOf,f.a],pipes:[d.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}.mat-form-field[_ngcontent-%COMP%]:disabled{background-color:#0ff}.app-input-search[_ngcontent-%COMP%]     .mat-select-value{color:var(--blue-grey-80,#5f6c81);font-family:var(--inputSearchFont)!important;font-size:13px;font-style:normal;font-weight:400;line-height:16px}"]}),e})()},w76M:function(e,t,n){"use strict";n.d(t,"a",(function(){return c})),n.d(t,"b",(function(){return p}));var i=n("jhN1"),r=n("fXoL"),o=n("oHs6"),s=n("PVOt"),a=n("6t9p");const l=["*"];let c=(()=>{let e=class extends s.b{constructor(e,t,n,i,r,o,s,a){super(e,t,n,i,s,a),this._watcherHelper=i,this._idh=r,this._createEventEmitters([{subscribe:"contentReady",emit:"onContentReady"},{subscribe:"disposing",emit:"onDisposing"},{subscribe:"hidden",emit:"onHidden"},{subscribe:"hiding",emit:"onHiding"},{subscribe:"initialized",emit:"onInitialized"},{subscribe:"optionChanged",emit:"onOptionChanged"},{subscribe:"resize",emit:"onResize"},{subscribe:"resizeEnd",emit:"onResizeEnd"},{subscribe:"resizeStart",emit:"onResizeStart"},{subscribe:"showing",emit:"onShowing"},{subscribe:"shown",emit:"onShown"},{subscribe:"titleRendered",emit:"onTitleRendered"},{emit:"accessKeyChange"},{emit:"animationChange"},{emit:"closeOnOutsideClickChange"},{emit:"containerChange"},{emit:"contentTemplateChange"},{emit:"deferRenderingChange"},{emit:"disabledChange"},{emit:"dragEnabledChange"},{emit:"elementAttrChange"},{emit:"focusStateEnabledChange"},{emit:"fullScreenChange"},{emit:"heightChange"},{emit:"hintChange"},{emit:"hoverStateEnabledChange"},{emit:"maxHeightChange"},{emit:"maxWidthChange"},{emit:"minHeightChange"},{emit:"minWidthChange"},{emit:"positionChange"},{emit:"resizeEnabledChange"},{emit:"rtlEnabledChange"},{emit:"shadingChange"},{emit:"shadingColorChange"},{emit:"showCloseButtonChange"},{emit:"showTitleChange"},{emit:"tabIndexChange"},{emit:"titleChange"},{emit:"titleTemplateChange"},{emit:"toolbarItemsChange"},{emit:"visibleChange"},{emit:"widthChange"}]),this._idh.setHost(this),o.setHost(this)}get accessKey(){return this._getOption("accessKey")}set accessKey(e){this._setOption("accessKey",e)}get animation(){return this._getOption("animation")}set animation(e){this._setOption("animation",e)}get closeOnOutsideClick(){return this._getOption("closeOnOutsideClick")}set closeOnOutsideClick(e){this._setOption("closeOnOutsideClick",e)}get container(){return this._getOption("container")}set container(e){this._setOption("container",e)}get contentTemplate(){return this._getOption("contentTemplate")}set contentTemplate(e){this._setOption("contentTemplate",e)}get deferRendering(){return this._getOption("deferRendering")}set deferRendering(e){this._setOption("deferRendering",e)}get disabled(){return this._getOption("disabled")}set disabled(e){this._setOption("disabled",e)}get dragEnabled(){return this._getOption("dragEnabled")}set dragEnabled(e){this._setOption("dragEnabled",e)}get elementAttr(){return this._getOption("elementAttr")}set elementAttr(e){this._setOption("elementAttr",e)}get focusStateEnabled(){return this._getOption("focusStateEnabled")}set focusStateEnabled(e){this._setOption("focusStateEnabled",e)}get fullScreen(){return this._getOption("fullScreen")}set fullScreen(e){this._setOption("fullScreen",e)}get height(){return this._getOption("height")}set height(e){this._setOption("height",e)}get hint(){return this._getOption("hint")}set hint(e){this._setOption("hint",e)}get hoverStateEnabled(){return this._getOption("hoverStateEnabled")}set hoverStateEnabled(e){this._setOption("hoverStateEnabled",e)}get maxHeight(){return this._getOption("maxHeight")}set maxHeight(e){this._setOption("maxHeight",e)}get maxWidth(){return this._getOption("maxWidth")}set maxWidth(e){this._setOption("maxWidth",e)}get minHeight(){return this._getOption("minHeight")}set minHeight(e){this._setOption("minHeight",e)}get minWidth(){return this._getOption("minWidth")}set minWidth(e){this._setOption("minWidth",e)}get position(){return this._getOption("position")}set position(e){this._setOption("position",e)}get resizeEnabled(){return this._getOption("resizeEnabled")}set resizeEnabled(e){this._setOption("resizeEnabled",e)}get rtlEnabled(){return this._getOption("rtlEnabled")}set rtlEnabled(e){this._setOption("rtlEnabled",e)}get shading(){return this._getOption("shading")}set shading(e){this._setOption("shading",e)}get shadingColor(){return this._getOption("shadingColor")}set shadingColor(e){this._setOption("shadingColor",e)}get showCloseButton(){return this._getOption("showCloseButton")}set showCloseButton(e){this._setOption("showCloseButton",e)}get showTitle(){return this._getOption("showTitle")}set showTitle(e){this._setOption("showTitle",e)}get tabIndex(){return this._getOption("tabIndex")}set tabIndex(e){this._setOption("tabIndex",e)}get title(){return this._getOption("title")}set title(e){this._setOption("title",e)}get titleTemplate(){return this._getOption("titleTemplate")}set titleTemplate(e){this._setOption("titleTemplate",e)}get toolbarItems(){return this._getOption("toolbarItems")}set toolbarItems(e){this._setOption("toolbarItems",e)}get visible(){return this._getOption("visible")}set visible(e){this._setOption("visible",e)}get width(){return this._getOption("width")}set width(e){this._setOption("width",e)}get toolbarItemsChildren(){return this._getOption("toolbarItems")}set toolbarItemsChildren(e){this.setChildren("toolbarItems",e)}_createInstance(e,t){return new o.a(e,t)}ngOnDestroy(){this._destroyWidget()}ngOnChanges(e){super.ngOnChanges(e),this.setupChanges("toolbarItems",e)}setupChanges(e,t){e in this._optionsToUpdate||this._idh.setup(e,t)}ngDoCheck(){this._idh.doCheck("toolbarItems"),this._watcherHelper.checkWatchers(),super.ngDoCheck(),super.clearChangedOptions()}_setOption(e,t){let n=this._idh.setupSingle(e,t),i=null!==this._idh.getChanges(e,t);(n||i)&&super._setOption(e,t)}};return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](r.ElementRef),r["\u0275\u0275directiveInject"](r.NgZone),r["\u0275\u0275directiveInject"](s.e),r["\u0275\u0275directiveInject"](s.j),r["\u0275\u0275directiveInject"](s.g),r["\u0275\u0275directiveInject"](s.i),r["\u0275\u0275directiveInject"](i.h),r["\u0275\u0275directiveInject"](r.PLATFORM_ID))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["dx-popup"]],contentQueries:function(e,t,n){if(1&e&&r["\u0275\u0275contentQuery"](n,a.L,!1),2&e){let e;r["\u0275\u0275queryRefresh"](e=r["\u0275\u0275loadQuery"]())&&(t.toolbarItemsChildren=e)}},inputs:{accessKey:"accessKey",animation:"animation",closeOnOutsideClick:"closeOnOutsideClick",container:"container",contentTemplate:"contentTemplate",deferRendering:"deferRendering",disabled:"disabled",dragEnabled:"dragEnabled",elementAttr:"elementAttr",focusStateEnabled:"focusStateEnabled",fullScreen:"fullScreen",height:"height",hint:"hint",hoverStateEnabled:"hoverStateEnabled",maxHeight:"maxHeight",maxWidth:"maxWidth",minHeight:"minHeight",minWidth:"minWidth",position:"position",resizeEnabled:"resizeEnabled",rtlEnabled:"rtlEnabled",shading:"shading",shadingColor:"shadingColor",showCloseButton:"showCloseButton",showTitle:"showTitle",tabIndex:"tabIndex",title:"title",titleTemplate:"titleTemplate",toolbarItems:"toolbarItems",visible:"visible",width:"width"},outputs:{onContentReady:"onContentReady",onDisposing:"onDisposing",onHidden:"onHidden",onHiding:"onHiding",onInitialized:"onInitialized",onOptionChanged:"onOptionChanged",onResize:"onResize",onResizeEnd:"onResizeEnd",onResizeStart:"onResizeStart",onShowing:"onShowing",onShown:"onShown",onTitleRendered:"onTitleRendered",accessKeyChange:"accessKeyChange",animationChange:"animationChange",closeOnOutsideClickChange:"closeOnOutsideClickChange",containerChange:"containerChange",contentTemplateChange:"contentTemplateChange",deferRenderingChange:"deferRenderingChange",disabledChange:"disabledChange",dragEnabledChange:"dragEnabledChange",elementAttrChange:"elementAttrChange",focusStateEnabledChange:"focusStateEnabledChange",fullScreenChange:"fullScreenChange",heightChange:"heightChange",hintChange:"hintChange",hoverStateEnabledChange:"hoverStateEnabledChange",maxHeightChange:"maxHeightChange",maxWidthChange:"maxWidthChange",minHeightChange:"minHeightChange",minWidthChange:"minWidthChange",positionChange:"positionChange",resizeEnabledChange:"resizeEnabledChange",rtlEnabledChange:"rtlEnabledChange",shadingChange:"shadingChange",shadingColorChange:"shadingColorChange",showCloseButtonChange:"showCloseButtonChange",showTitleChange:"showTitleChange",tabIndexChange:"tabIndexChange",titleChange:"titleChange",titleTemplateChange:"titleTemplateChange",toolbarItemsChange:"toolbarItemsChange",visibleChange:"visibleChange",widthChange:"widthChange"},features:[r["\u0275\u0275ProvidersFeature"]([s.e,s.j,s.i,s.g]),r["\u0275\u0275InheritDefinitionFeature"],r["\u0275\u0275NgOnChangesFeature"]],ngContentSelectors:l,decls:1,vars:0,template:function(e,t){1&e&&(r["\u0275\u0275projectionDef"](),r["\u0275\u0275projection"](0))},encapsulation:2}),e})(),p=(()=>{let e=class{};return e.\u0275mod=r["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=r["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[a.bb,a.Gc,a.Vd,a.vd,a.hb,a.lb,a.sb,a.id,a.jd,a.M,s.c,s.f,i.b],a.bb,a.Gc,a.Vd,a.vd,a.hb,a.lb,a.sb,a.id,a.jd,a.M,s.f]}),e})()}}]);