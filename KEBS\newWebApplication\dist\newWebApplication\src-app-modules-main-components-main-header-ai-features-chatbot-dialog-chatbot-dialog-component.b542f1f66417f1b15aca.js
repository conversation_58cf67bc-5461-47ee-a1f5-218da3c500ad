(window.webpackJsonp=window.webpackJsonp||[]).push([[963],{R2cK:function(t,e,n){"use strict";n.r(e),n.d(e,"ChatbotDialogComponent",(function(){return sn}));var o=n("mrSG"),i=n("rDax"),r=n("+rOU"),a=n("0IaG"),c=n("STbY"),s=n("wd/R"),l=n("xG9w"),d=n("XNiG"),p=n("1G5W"),g=n("ZzAl"),h=n("fXoL"),m=n("dra5"),u=n("XXEo"),C=n("XNFG"),f=n("WYLN"),x=n("jhN1"),_=n("ofXK"),v=n("lVl8"),y=n("3Pt+"),P=n("NFeN"),M=n("dlKe"),O=n("Qu3c"),b=n("f0Cb"),S=n("me71"),w=n("kmnG"),E=n("d3UM"),k=n("FKr1");function L(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",4),h["\u0275\u0275elementStart"](1,"span",21),h["\u0275\u0275listener"]("click",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"]().modeEnable("E")})),h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275elementStart"](2,"svg",22),h["\u0275\u0275element"](3,"path",23),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}}function I(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",4),h["\u0275\u0275elementStart"](1,"span",21),h["\u0275\u0275listener"]("click",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"]().modeEnable("R")})),h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275elementStart"](2,"svg",5),h["\u0275\u0275elementStart"](3,"mask",24),h["\u0275\u0275element"](4,"rect",25),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](5,"g",26),h["\u0275\u0275element"](6,"path",27),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}}function D(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",40),h["\u0275\u0275listener"]("click",(function(){h["\u0275\u0275restoreView"](t);const n=e.$implicit,o=h["\u0275\u0275nextContext"](3);return o.selectDuration(n),o.dateOverlayEnable=!1})),h["\u0275\u0275elementStart"](1,"span"),h["\u0275\u0275text"](2),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}if(2&t){const t=e.$implicit;h["\u0275\u0275advance"](2),h["\u0275\u0275textInterpolate"](null==t?null:t.name)}}function V(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275namespaceHTML"](),h["\u0275\u0275elementStart"](0,"div",37),h["\u0275\u0275listener"]("click",(function(t){return t.stopPropagation()})),h["\u0275\u0275elementStart"](1,"div",38),h["\u0275\u0275listener"]("mouseLeave",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](2).dateOverlayEnable=!1})),h["\u0275\u0275template"](2,D,3,1,"div",39),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"](2);h["\u0275\u0275advance"](2),h["\u0275\u0275property"]("ngForOf",t.durationList)}}function T(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",28),h["\u0275\u0275listener"]("mouseLeave",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"]().dateOverlayEnable=!1})),h["\u0275\u0275elementStart"](1,"div",29),h["\u0275\u0275listener"]("click",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"]().openDateOverlay()})),h["\u0275\u0275elementStart"](2,"div",30),h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275elementStart"](3,"svg",31),h["\u0275\u0275element"](4,"path",32),h["\u0275\u0275elementEnd"](),h["\u0275\u0275namespaceHTML"](),h["\u0275\u0275elementStart"](5,"span",33),h["\u0275\u0275text"](6),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275elementStart"](7,"svg",34),h["\u0275\u0275element"](8,"path",35),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](9,V,3,1,"div",36),h["\u0275\u0275elementEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"]();h["\u0275\u0275advance"](6),h["\u0275\u0275textInterpolate"](t.durationName),h["\u0275\u0275advance"](3),h["\u0275\u0275property"]("ngIf",t.dateOverlayEnable)}}function H(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",42),h["\u0275\u0275elementStart"](1,"div",30),h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275elementStart"](2,"svg",31),h["\u0275\u0275element"](3,"path",32),h["\u0275\u0275elementEnd"](),h["\u0275\u0275namespaceHTML"](),h["\u0275\u0275elementStart"](4,"span",33),h["\u0275\u0275text"](5),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"](2);h["\u0275\u0275advance"](5),h["\u0275\u0275textInterpolate"](t.durationName)}}function F(t,e){if(1&t&&h["\u0275\u0275template"](0,H,6,1,"div",41),2&t){const t=h["\u0275\u0275nextContext"]();h["\u0275\u0275property"]("ngIf",!t.dataLoading)}}function R(t,e){1&t&&(h["\u0275\u0275elementStart"](0,"div",47),h["\u0275\u0275text"](1,"Configurations"),h["\u0275\u0275elementEnd"]())}const z=function(t){return{color:t}};function A(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",52),h["\u0275\u0275elementStart"](1,"input",58),h["\u0275\u0275listener"]("ngModelChange",(function(t){return e.$implicit.is_checked=t}))("change",(function(){h["\u0275\u0275restoreView"](t);const n=e.$implicit,o=h["\u0275\u0275nextContext"]().$implicit;return h["\u0275\u0275nextContext"](3).changeResponseData(o,"filter",n)})),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](2,"span",59),h["\u0275\u0275text"](3),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}if(2&t){const t=e.$implicit;h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngModel",t.is_checked),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngStyle",h["\u0275\u0275pureFunction1"](3,z,null!=t&&t.is_checked?"#1B2140":"#5F6C81")),h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate"](null==t?null:t.name)}}function B(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div"),h["\u0275\u0275elementStart"](1,"div",50),h["\u0275\u0275text"](2),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](3,A,4,5,"div",51),h["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit;h["\u0275\u0275advance"](2),h["\u0275\u0275textInterpolate"](null==t?null:t.name),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngForOf",t.master_data)}}function j(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",52),h["\u0275\u0275elementStart"](1,"input",58),h["\u0275\u0275listener"]("ngModelChange",(function(t){return e.$implicit.is_checked=t}))("change",(function(){h["\u0275\u0275restoreView"](t);const n=e.$implicit;return h["\u0275\u0275nextContext"](3).changeResponseData(n,"group_by")})),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](2,"span",59),h["\u0275\u0275text"](3),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}if(2&t){const t=e.$implicit;h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngModel",t.is_checked),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngStyle",h["\u0275\u0275pureFunction1"](3,z,null!=t&&t.is_checked?"#1B2140":"#5F6C81")),h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate"](null==t?null:t.name)}}function Z(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",52),h["\u0275\u0275elementStart"](1,"input",60),h["\u0275\u0275listener"]("ngModelChange",(function(e){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](3).selectedSort=e}))("click",(function(){h["\u0275\u0275restoreView"](t);const n=e.$implicit;return h["\u0275\u0275nextContext"](3).changeResponseData(n,"sort")})),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](2,"span",59),h["\u0275\u0275text"](3),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](4,"input",61),h["\u0275\u0275listener"]("keypress",(function(e){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](3).preventInvalidInput(e)}))("ngModelChange",(function(t){return e.$implicit.value=t}))("input",(function(n){h["\u0275\u0275restoreView"](t);const o=e.$implicit;return h["\u0275\u0275nextContext"](3).inputSort(n,o)})),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}if(2&t){const t=e.$implicit,n=h["\u0275\u0275nextContext"](3);h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("name","sortGroup")("ngModel",n.selectedSort)("value",null==t?null:t.id),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngStyle",h["\u0275\u0275pureFunction1"](8,z,(null==t?null:t.id)==n.selectedSort?"#1B2140":"#5F6C81")),h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate"](null==t?null:t.name),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngModel",t.value)("min",1)("max",100)}}function G(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"mat-option",56),h["\u0275\u0275text"](1),h["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit;h["\u0275\u0275property"]("value",t.id),h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate1"](" ",t.name," ")}}function N(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",48),h["\u0275\u0275template"](1,B,4,2,"div",49),h["\u0275\u0275elementStart"](2,"div",50),h["\u0275\u0275text"](3,"Group By"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](4,j,4,5,"div",51),h["\u0275\u0275elementStart"](5,"div",50),h["\u0275\u0275text"](6,"Customization"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](7,Z,5,10,"div",51),h["\u0275\u0275elementStart"](8,"div",52),h["\u0275\u0275elementStart"](9,"span",53),h["\u0275\u0275text"](10,"Efficiency"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](11,"mat-form-field",54),h["\u0275\u0275elementStart"](12,"mat-select",55),h["\u0275\u0275listener"]("ngModelChange",(function(e){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](2).selectedEfficiency=e}))("selectionChange",(function(e){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](2).onEfficiencyChange(e.value)})),h["\u0275\u0275elementStart"](13,"mat-option",56),h["\u0275\u0275text"](14,"Select None"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](15,G,2,2,"mat-option",57),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"](2);h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngForOf",t.filterList),h["\u0275\u0275advance"](3),h["\u0275\u0275property"]("ngForOf",t.groupByList),h["\u0275\u0275advance"](3),h["\u0275\u0275property"]("ngForOf",t.sortList),h["\u0275\u0275advance"](5),h["\u0275\u0275property"]("ngModel",t.selectedEfficiency),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("value",null),h["\u0275\u0275advance"](2),h["\u0275\u0275property"]("ngForOf",t.efficiencyList)}}function U(t,e){1&t&&(h["\u0275\u0275elementStart"](0,"div",47),h["\u0275\u0275text"](1,"Configurations"),h["\u0275\u0275elementEnd"]())}function $(t,e){1&t&&(h["\u0275\u0275elementStart"](0,"span"),h["\u0275\u0275text"](1,", "),h["\u0275\u0275elementEnd"]())}function W(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",52),h["\u0275\u0275elementStart"](1,"span",68),h["\u0275\u0275text"](2),h["\u0275\u0275template"](3,$,2,0,"span",69),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit,n=e.last;h["\u0275\u0275styleProp"]("display",null!=t&&t.is_checked?"":"none"),h["\u0275\u0275advance"](2),h["\u0275\u0275textInterpolate"](t.name),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!n)}}function Y(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div"),h["\u0275\u0275elementStart"](1,"div",66),h["\u0275\u0275text"](2),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](3,W,4,4,"div",67),h["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit,n=h["\u0275\u0275nextContext"](5);h["\u0275\u0275advance"](2),h["\u0275\u0275textInterpolate"](null==t?null:t.name),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngForOf",n.checkedList(t.master_data))}}function X(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",65),h["\u0275\u0275template"](1,Y,4,2,"div",49),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"](4);h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngForOf",t.checkedList(t.filterList))}}function Q(t,e){1&t&&(h["\u0275\u0275elementStart"](0,"span"),h["\u0275\u0275text"](1,", "),h["\u0275\u0275elementEnd"]())}function q(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",52),h["\u0275\u0275elementStart"](1,"span",68),h["\u0275\u0275text"](2),h["\u0275\u0275template"](3,Q,2,0,"span",69),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit,n=e.last;h["\u0275\u0275styleProp"]("display",null!=t&&t.is_checked?"":"none"),h["\u0275\u0275advance"](2),h["\u0275\u0275textInterpolate"](t.name),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!n)}}function K(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",65),h["\u0275\u0275elementStart"](1,"div",66),h["\u0275\u0275text"](2,"Group By"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](3,q,4,4,"div",67),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"](4);h["\u0275\u0275advance"](3),h["\u0275\u0275property"]("ngForOf",t.checkedList(t.groupByList))}}function J(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"span",68),h["\u0275\u0275text"](1),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"](5);h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate1"]("Efficiency is ",null==t.efficiency_data?null:t.efficiency_data.name,"")}}function tt(t,e){1&t&&(h["\u0275\u0275elementStart"](0,"span",68),h["\u0275\u0275text"](1," , "),h["\u0275\u0275elementEnd"]())}function et(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"span",68),h["\u0275\u0275text"](1),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"](5);h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate2"]("",null==t.sortValue?null:t.sortValue.name," ",null==t.sortValue?null:t.sortValue.value,"")}}function nt(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",65),h["\u0275\u0275elementStart"](1,"div",66),h["\u0275\u0275text"](2,"Customization"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](3,"div",70),h["\u0275\u0275template"](4,J,2,1,"span",71),h["\u0275\u0275template"](5,tt,2,0,"span",71),h["\u0275\u0275template"](6,et,2,2,"span",71),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"](4);h["\u0275\u0275advance"](4),h["\u0275\u0275property"]("ngIf",t.efficiency_data),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",t.efficiency_data&&t.selectedSort),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",t.selectedSort)}}function ot(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",63),h["\u0275\u0275template"](1,U,2,0,"div",44),h["\u0275\u0275template"](2,X,2,1,"div",64),h["\u0275\u0275template"](3,K,4,1,"div",64),h["\u0275\u0275template"](4,nt,7,3,"div",64),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"](3);h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",(null==t.responseData?null:t.responseData.filter_ids.length)>0||(null==t.responseData?null:t.responseData.group_by_ids.length)>0||t.selectedSort||t.efficiency_data),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",(null==t.responseData?null:t.responseData.filter_ids.length)>0),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",(null==t.responseData?null:t.responseData.group_by_ids.length)>0),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",t.efficiency_data||t.selectedSort)}}function it(t,e){if(1&t&&h["\u0275\u0275template"](0,ot,5,4,"div",62),2&t){const t=h["\u0275\u0275nextContext"](2);h["\u0275\u0275property"]("ngIf",!t.dataLoading)}}function rt(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",43),h["\u0275\u0275template"](1,R,2,0,"div",44),h["\u0275\u0275template"](2,N,16,6,"div",45),h["\u0275\u0275template"](3,it,1,1,"ng-template",null,46,h["\u0275\u0275templateRefExtractor"]),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275reference"](4),e=h["\u0275\u0275nextContext"]();h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf","R"!=e.mode),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf","R"!=e.mode)("ngIfElse",t)}}function at(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",76),h["\u0275\u0275listener"]("click",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](2).showAdvanceOptions()})),h["\u0275\u0275text"](1," Hide Advanced Options "),h["\u0275\u0275elementEnd"]()}}function ct(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",76),h["\u0275\u0275listener"]("click",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](2).showAdvanceOptions()})),h["\u0275\u0275text"](1," Show Advanced Options "),h["\u0275\u0275elementEnd"]()}}function st(t,e){1&t&&(h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275namespaceHTML"](),h["\u0275\u0275elementStart"](0,"span"),h["\u0275\u0275text"](1,"Generate"),h["\u0275\u0275elementEnd"]())}function lt(t,e){1&t&&(h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275namespaceHTML"](),h["\u0275\u0275elementStart"](0,"span"),h["\u0275\u0275text"](1,"Re-Generate"),h["\u0275\u0275elementEnd"]())}function dt(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",77),h["\u0275\u0275listener"]("click",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](2).generateResponse()})),h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275elementStart"](1,"svg",78),h["\u0275\u0275element"](2,"path",79),h["\u0275\u0275element"](3,"path",80),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](4,st,2,0,"span",69),h["\u0275\u0275template"](5,lt,2,0,"span",69),h["\u0275\u0275elementEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"](2);h["\u0275\u0275advance"](4),h["\u0275\u0275property"]("ngIf","E"!=t.mode),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf","E"==t.mode)}}function pt(t,e){1&t&&(h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275namespaceHTML"](),h["\u0275\u0275elementStart"](0,"span"),h["\u0275\u0275text"](1,"Generate"),h["\u0275\u0275elementEnd"]())}function gt(t,e){1&t&&(h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275namespaceHTML"](),h["\u0275\u0275elementStart"](0,"span"),h["\u0275\u0275text"](1,"Re-Generate"),h["\u0275\u0275elementEnd"]())}function ht(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",81),h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275elementStart"](1,"svg",78),h["\u0275\u0275element"](2,"path",82),h["\u0275\u0275element"](3,"path",83),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](4,pt,2,0,"span",69),h["\u0275\u0275template"](5,gt,2,0,"span",69),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"](2);h["\u0275\u0275advance"](4),h["\u0275\u0275property"]("ngIf","E"!=t.mode),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf","E"==t.mode)}}function mt(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",72),h["\u0275\u0275template"](1,at,2,0,"div",73),h["\u0275\u0275template"](2,ct,2,0,"div",73),h["\u0275\u0275template"](3,dt,6,2,"div",74),h["\u0275\u0275template"](4,ht,6,2,"div",75),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"]();h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",t.showAdvanceFilters),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!t.showAdvanceFilters),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!t.isPromptApiInProgress),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",t.isPromptApiInProgress)}}let ut=(()=>{class t{constructor(t,e,n){this._aiService=t,this.eRef=e,this._toaster=n,this.moduleListData=[],this.customizePromptConfig=null,this.mode="C",this.currentChatData=[],this.aiIconShort="",this.isPromptApiInProgress=!1,this.generateResponseEmitter=new h.EventEmitter,this.showAdvanceFilters=!1,this.locationOverlayEnable=!1,this.dateOverlayEnable=!1,this.durationList=[],this.groupByList=[],this.filterList=[],this.sortList=[],this.efficiencyList=[],this.responseData={is_custom_prompt:!0,module_id:null,duration_id:null,filter_ids:[],sort_by_id:null,group_by_ids:[],efficiency_data:null,selectedEfficiency:null,sortValue:null},this.moduleName="",this.durationName="",this.customDescription="",this.selectedEfficiency=null,this.sortValue=null,this.efficiency_data=null,this.selectedSort=null,this.clickedInside=!1,this.editIconVisible=!1,this.dataLoading=!1,this.originalPromptResponse={}}ngOnInit(){var t,e,n,i;return Object(o.c)(this,void 0,void 0,(function*(){this.dataLoading=!0,"R"==this.mode&&(this.editIconVisible=!0),(null===(e=null===(t=this.currentChatData)||void 0===t?void 0:t.filter_config)||void 0===e?void 0:e.data)&&(this.originalPromptResponse=JSON.parse(JSON.stringify(null===(i=null===(n=this.currentChatData)||void 0===n?void 0:n.filter_config)||void 0===i?void 0:i.data))),yield this.iniateDataFromPrompt(),this.dataLoading=!1}))}showAdvanceOptions(){this.showAdvanceFilters=!this.showAdvanceFilters}selectModule(t){this.responseData.module_id=null==t?void 0:t.id,this.moduleName=null==t?void 0:t.name,this.locationOverlayEnable=!1}selectDuration(t){this.responseData.duration_id=null==t?void 0:t.id,this.durationName=null==t?void 0:t.name,this.dateOverlayEnable=!1}iniateDataFromPrompt(){var t,e,n,i;return Object(o.c)(this,void 0,void 0,(function*(){if(this.customDescription=(null===(t=this.customizePromptConfig)||void 0===t?void 0:t.report_header)||"Customize Report",this.durationList=this.customizePromptConfig.date_duration_master||[],this.durationList.length>0){let t=(null===(e=this.customizePromptConfig)||void 0===e?void 0:e.default_date_id)||0;this.durationName=null===(n=this.durationList.find(e=>e.id==t))||void 0===n?void 0:n.name,this.responseData.duration_id=t}if(this.groupByList=[],(this.customizePromptConfig.group_by_config||[]).forEach(t=>{(null==t?void 0:t.is_checked)?(this.groupByList.push(Object.assign({},t)),"R"!=this.mode&&this.responseData.group_by_ids.push(t.id)):this.groupByList.push(Object.assign(Object.assign({},t),{is_checked:!1}))}),this.sortList=(this.customizePromptConfig.sort_by||[]).map(t=>{var e;return Object.assign(Object.assign({},t),{is_checked:null!==(e=t.is_checked)&&void 0!==e&&e})}),this.efficiencyList=(this.customizePromptConfig.efficiency||[]).map(t=>Object.assign({},t)),yield this.loadFilterMasterData(),"R"==this.mode){if(this.originalPromptResponse){let t=this.originalPromptResponse;this.responseData=JSON.parse(JSON.stringify(t)),this.durationName=null===(i=this.durationList.find(e=>e.id==(null==t?void 0:t.duration_id)))||void 0===i?void 0:i.name}this.showAdvanceFilters=!0,yield this.setAdvanceFilterData()}}))}loadFilterMasterData(){return Object(o.c)(this,void 0,void 0,(function*(){const t=(this.customizePromptConfig.filter_config||[]).map(t=>Object(o.c)(this,void 0,void 0,(function*(){if(null==t?void 0:t.api_url)try{const e=yield this._aiService.getMasterDataUsingApi(t.api_url);e&&"S"===e.messType?(t.master_data=e.data||[],t.master_data=t.master_data.map(e=>{var n;return Object.assign(Object.assign({},e),{is_checked:(null===(n=null==t?void 0:t.default_ids)||void 0===n?void 0:n.includes(e.id))||!1})})):t.master_data=[]}catch(e){console.error("Error fetching master data:",e),t.master_data=[]}this.filterList.push(t)})));yield Promise.all(t)}))}generateResponse(){var t,e,n;this.currentChatData.filter_config||(this.currentChatData.filter_config={}),(null===(t=this.responseData)||void 0===t?void 0:t.sort_by_id)?(null===(e=this.responseData.sort_by_id)||void 0===e?void 0:e.value)>=1&&(null===(n=this.responseData.sort_by_id)||void 0===n?void 0:n.value)<=100?(this.currentChatData.filter_config.data=this.responseData,this.generateResponseEmitter.emit({data:this.responseData,mode:this.mode,currentChat:this.currentChatData})):this._toaster.showWarning("Warning","Selected Sort Value must be between 1-100",3e3):(this.currentChatData.filter_config.data=this.responseData,this.generateResponseEmitter.emit({data:this.responseData,mode:this.mode,currentChat:this.currentChatData}))}changeResponseData(t,e,n){if("filter"==e){this.responseData.filter_ids||(this.responseData.filter_ids=[]);let e=this.responseData.filter_ids.find(e=>e.id===t.id);n.is_checked?e?e.values.includes(n.id)||e.values.push(n.id):this.responseData.filter_ids.push({id:t.id,values:[n.id]}):e?(e.values=e.values.filter(t=>t!==n.id),0===e.values.length&&(this.responseData.filter_ids=this.responseData.filter_ids.filter(e=>e.id!==t.id))):this.responseData.filter_ids.push({id:t.id,values:[n.id]})}else if("sort"==e)this.sortValue&&this.sortValue.id==t.id?(this.selectedSort=null,this.sortValue=null,this.responseData.sort_by_id=null,this.responseData.sortValue=null):(this.selectedSort=t.id,this.sortValue=t,this.responseData.sort_by_id=t,this.responseData.sortValue=null==t?void 0:t.value);else if("group_by"==e){let e=t.id;const n=this.responseData.group_by_ids.indexOf(e);-1===n?this.responseData.group_by_ids.push(e):this.responseData.group_by_ids.splice(n,1)}}modeEnable(t){var e;return Object(o.c)(this,void 0,void 0,(function*(){if(this.mode=t,"R"==t){if(this.dataLoading=!0,this.originalPromptResponse){let t=this.originalPromptResponse;this.responseData=JSON.parse(JSON.stringify(t)),this.durationName=null===(e=this.durationList.find(e=>e.id==(null==t?void 0:t.duration_id)))||void 0===e?void 0:e.name}this.showAdvanceFilters=!0,yield this.setAdvanceFilterData(),this.dataLoading=!1}}))}openDateOverlay(){this.clickedInside=!0,this.dateOverlayEnable=!0}clickOutside(t){if(this.clickedInside)return void(this.clickedInside=!1);const e=this.eRef.nativeElement.querySelector(".overlay");(null==e?void 0:e.contains(t.target))||(this.dateOverlayEnable=!1)}setAdvanceFilterData(){var t,e,n;this.responseData||(this.responseData={});const o=this.responseData.filter_ids||[],i=this.responseData.group_by_ids||[];if(this.groupByList=(this.groupByList||[]).map(t=>Object.assign(Object.assign({},t),{is_checked:i.length>0&&i.includes(t.id)})),o&&0==o.length)for(let c of this.filterList)c.is_checked=!1,c.master_data=c.master_data.map(t=>Object.assign(Object.assign({},t),{is_checked:!1}));for(let c of o){const t=this.filterList.find(t=>t.id===c.id)||{};t?(t.is_checked=!0,t.master_data=t.master_data.map(t=>Object.assign(Object.assign({},t),{is_checked:c.values.includes(t.id)}))):(t.is_checked=!1,t.master_data=t.master_data.map(t=>Object.assign(Object.assign({},t),{is_checked:c.values.includes(t.id)})))}let r=null===(t=this.responseData)||void 0===t?void 0:t.sort_by_id;const a=this.sortList.find(t=>(null==t?void 0:t.id)===(null==r?void 0:r.id));a?(a.value=null==r?void 0:r.value,this.selectedSort=a.id,this.sortValue=a):(this.selectedSort=null,this.sortValue=null),this.efficiency_data=(null===(e=this.responseData)||void 0===e?void 0:e.efficiency_data)||null,this.selectedEfficiency=(null===(n=this.responseData)||void 0===n?void 0:n.selectedEfficiency)||null}onEfficiencyChange(t){if(null===t)this.efficiency_data=null,this.responseData.efficiency_data=null,this.responseData.selectedEfficiency=null;else{const e=isNaN(t)?t:Number(t);this.efficiency_data=this.efficiencyList.find(t=>t.id===e)||{},this.responseData.efficiency_data=this.efficiency_data,this.responseData.selectedEfficiency=e}}checkedList(t){return t.filter(t=>t.is_checked)}inputSort(t,e){let n=Number(null==e?void 0:e.value);(isNaN(n)||null==n)&&this._toaster.showWarning("Warning","Value must be between 1-100",3e3),n>100?(n=100,this._toaster.showWarning("Warning","Value must not be greater than 100",3e3)):n<1&&(n=1,this._toaster.showWarning("Warning","Value must not be less than 1",3e3));let o=this.sortList.find(t=>t.id===(null==e?void 0:e.id));o&&(o.value=t.target.value?n:null),t.target.value&&(t.target.value=n)}preventInvalidInput(t){/^[0-9]$/.test(t.key)||t.preventDefault()}}return t.\u0275fac=function(e){return new(e||t)(h["\u0275\u0275directiveInject"](m.a),h["\u0275\u0275directiveInject"](h.ElementRef),h["\u0275\u0275directiveInject"](C.a))},t.\u0275cmp=h["\u0275\u0275defineComponent"]({type:t,selectors:[["app-chat-custom-report"]],hostBindings:function(t,e){1&t&&h["\u0275\u0275listener"]("click",(function(t){return e.clickOutside(t)}),!1,h["\u0275\u0275resolveDocument"])},inputs:{moduleListData:"moduleListData",customizePromptConfig:"customizePromptConfig",mode:"mode",currentChatData:"currentChatData",aiIconShort:"aiIconShort",isPromptApiInProgress:"isPromptApiInProgress"},outputs:{generateResponseEmitter:"generateResponseEmitter"},decls:26,vars:8,consts:[[1,"custom-report-class"],[1,"ai-icon",3,"src"],[1,"custom-card"],[1,"custom-header"],[1,"header-align-class"],["width","24","height","24","viewBox","0 0 24 24","fill","none","xmlns","http://www.w3.org/2000/svg"],["d","M22 6V8.42C22 10 21 11 19.42 11H16V4.01C16 2.9 16.91 2 18.02 2C19.11 2.01 20.11 2.45 20.83 3.17C21.55 3.9 22 4.9 22 6Z","fill","#526179"],["opacity","0.4","d","M2 7V21C2 21.83 2.94001 22.3 3.60001 21.8L5.31 20.52C5.71 20.22 6.27 20.26 6.63 20.62L8.28999 22.29C8.67999 22.68 9.32001 22.68 9.71001 22.29L11.39 20.61C11.74 20.26 12.3 20.22 12.69 20.52L14.4 21.8C15.06 22.29 16 21.82 16 21V4C16 2.9 16.9 2 18 2H7H6C3 2 2 3.79 2 6V7Z","fill","#526179"],["d","M12 9.75H6C5.59 9.75 5.25 9.41 5.25 9C5.25 8.59 5.59 8.25 6 8.25H12C12.41 8.25 12.75 8.59 12.75 9C12.75 9.41 12.41 9.75 12 9.75Z","fill","#526179"],["d","M11.25 13.75H6.75C6.34 13.75 6 13.41 6 13C6 12.59 6.34 12.25 6.75 12.25H11.25C11.66 12.25 12 12.59 12 13C12 13.41 11.66 13.75 11.25 13.75Z","fill","#526179"],[1,"header-text"],["class","header-align-class",4,"ngIf"],[1,"custom-body-class"],[1,"row","content-section"],[1,"col","p-0","m-0","content-class"],[1,"content-header"],[1,"asterick-class"],["class","input-section",3,"mouseLeave",4,"ngIf","ngIfElse"],["readModeDurationTemplate",""],["class","advance-section",4,"ngIf"],["class","row button-class",4,"ngIf"],[1,"edit-class",3,"click"],["width","18","height","18","viewBox","0 0 18 18","fill","none","xmlns","http://www.w3.org/2000/svg"],["d","M2 16H3.2615L13.498 5.7635L12.2365 4.502L2 14.7385V16ZM1.404 17.5C1.14783 17.5 0.933167 17.4133 0.76 17.24C0.586667 17.0668 0.5 16.8522 0.5 16.596V14.8635C0.5 14.6197 0.546833 14.3873 0.6405 14.1663C0.734 13.9453 0.862833 13.7527 1.027 13.5885L13.6905 0.93075C13.8417 0.793416 14.0086 0.687333 14.1913 0.6125C14.3741 0.5375 14.5658 0.5 14.7663 0.5C14.9668 0.5 15.1609 0.535584 15.3488 0.60675C15.5367 0.677917 15.7032 0.791083 15.848 0.946249L17.0693 2.18275C17.2244 2.32758 17.335 2.49425 17.401 2.68275C17.467 2.87125 17.5 3.05975 17.5 3.24825C17.5 3.44942 17.4657 3.64133 17.397 3.824C17.3283 4.00683 17.2191 4.17383 17.0693 4.325L4.4115 16.973C4.24733 17.1372 4.05475 17.266 3.83375 17.3595C3.61275 17.4532 3.38033 17.5 3.1365 17.5H1.404ZM12.8562 5.14375L12.2365 4.502L13.498 5.7635L12.8562 5.14375Z","fill","#45546E"],["id","mask0_3897_208580","maskUnits","userSpaceOnUse","x","0","y","0","width","24","height","24",2,"mask-type","alpha"],["width","24","height","24","fill","#D9D9D9"],["mask","url(#mask0_3897_208580)"],["d","M11.9997 13.0635L8.7534 16.3095C8.60857 16.4545 8.43298 16.5254 8.22665 16.522C8.02015 16.5189 7.84448 16.4449 7.69965 16.3C7.55482 16.1552 7.4824 15.978 7.4824 15.7683C7.4824 15.5586 7.55482 15.3814 7.69965 15.2365L10.9362 12L7.69015 8.7788C7.54515 8.63396 7.47432 8.45671 7.47765 8.24705C7.48082 8.03755 7.55482 7.86038 7.69965 7.71555C7.84448 7.57055 8.02173 7.49805 8.2314 7.49805C8.44107 7.49805 8.61832 7.57055 8.76315 7.71555L11.9997 10.9615L15.2209 7.71555C15.3657 7.57055 15.5413 7.49805 15.7477 7.49805C15.9542 7.49805 16.1298 7.57055 16.2747 7.71555C16.4298 7.87055 16.5074 8.0503 16.5074 8.2548C16.5074 8.4593 16.4298 8.63396 16.2747 8.7788L13.0382 12L16.2842 15.2463C16.4292 15.3911 16.5017 15.5667 16.5017 15.773C16.5017 15.9795 16.4292 16.1552 16.2842 16.3C16.1292 16.4552 15.9494 16.5328 15.7449 16.5328C15.5404 16.5328 15.3657 16.4552 15.2209 16.3L11.9997 13.0635Z","fill","#45546E"],[1,"input-section",3,"mouseLeave"],[1,"row","d-flex","w-100","align-items-center",3,"click"],[1,"input-left-align"],["width","12","height","14","viewBox","0 0 12 14","fill","none","xmlns","http://www.w3.org/2000/svg"],["d","M1.5372 13.3333C1.20042 13.3333 0.915365 13.2167 0.682031 12.9833C0.448698 12.75 0.332031 12.465 0.332031 12.1282V3.20518C0.332031 2.8684 0.448698 2.58334 0.682031 2.35001C0.915365 2.11668 1.20042 2.00001 1.5372 2.00001H2.4602V1.10251C2.4602 0.956399 2.50914 0.834399 2.60703 0.73651C2.70492 0.638732 2.82692 0.589844 2.97303 0.589844C3.11925 0.589844 3.24125 0.638732 3.33903 0.73651C3.43692 0.834399 3.48586 0.956399 3.48586 1.10251V2.00001H8.5372V1.08984C8.5372 0.947955 8.58503 0.829121 8.6807 0.733344C8.77647 0.637677 8.89531 0.589844 9.0372 0.589844C9.17909 0.589844 9.29786 0.637677 9.39353 0.733344C9.48931 0.829121 9.5372 0.947955 9.5372 1.08984V2.00001H10.4602C10.797 2.00001 11.082 2.11668 11.3154 2.35001C11.5487 2.58334 11.6654 2.8684 11.6654 3.20518V12.1282C11.6654 12.465 11.5487 12.75 11.3154 12.9833C11.082 13.2167 10.797 13.3333 10.4602 13.3333H1.5372ZM1.5372 12.3333H10.4602C10.5115 12.3333 10.5585 12.312 10.6012 12.2692C10.644 12.2265 10.6654 12.1795 10.6654 12.1282V5.87184H1.33203V12.1282C1.33203 12.1795 1.35342 12.2265 1.3962 12.2692C1.43886 12.312 1.48586 12.3333 1.5372 12.3333ZM1.33203 4.87184H10.6654V3.20518C10.6654 3.15384 10.644 3.10684 10.6012 3.06418C10.5585 3.0214 10.5115 3.00001 10.4602 3.00001H1.5372C1.48586 3.00001 1.43886 3.0214 1.3962 3.06418C1.35342 3.10684 1.33203 3.15384 1.33203 3.20518V4.87184ZM5.9987 8.38468C5.83548 8.38468 5.69636 8.32718 5.58136 8.21218C5.46648 8.09729 5.40903 7.95818 5.40903 7.79484C5.40903 7.63162 5.46648 7.49251 5.58136 7.37751C5.69636 7.26262 5.83548 7.20518 5.9987 7.20518C6.16192 7.20518 6.30103 7.26262 6.41603 7.37751C6.53092 7.49251 6.58836 7.63162 6.58836 7.79484C6.58836 7.95818 6.53092 8.09729 6.41603 8.21218C6.30103 8.32718 6.16192 8.38468 5.9987 8.38468ZM3.33203 8.38468C3.16881 8.38468 3.0297 8.32718 2.9147 8.21218C2.79981 8.09729 2.74236 7.95818 2.74236 7.79484C2.74236 7.63162 2.79981 7.49251 2.9147 7.37751C3.0297 7.26262 3.16881 7.20518 3.33203 7.20518C3.49525 7.20518 3.63436 7.26262 3.74936 7.37751C3.86425 7.49251 3.9217 7.63162 3.9217 7.79484C3.9217 7.95818 3.86425 8.09729 3.74936 8.21218C3.63436 8.32718 3.49525 8.38468 3.33203 8.38468ZM8.66536 8.38468C8.50214 8.38468 8.36303 8.32718 8.24803 8.21218C8.13314 8.09729 8.0757 7.95818 8.0757 7.79484C8.0757 7.63162 8.13314 7.49251 8.24803 7.37751C8.36303 7.26262 8.50214 7.20518 8.66536 7.20518C8.82859 7.20518 8.9677 7.26262 9.0827 7.37751C9.19759 7.49251 9.25503 7.63162 9.25503 7.79484C9.25503 7.95818 9.19759 8.09729 9.0827 8.21218C8.9677 8.32718 8.82859 8.38468 8.66536 8.38468ZM5.9987 11C5.83548 11 5.69636 10.9425 5.58136 10.8275C5.46648 10.7126 5.40903 10.5736 5.40903 10.4103C5.40903 10.247 5.46648 10.1079 5.58136 9.99301C5.69636 9.87801 5.83548 9.82051 5.9987 9.82051C6.16192 9.82051 6.30103 9.87801 6.41603 9.99301C6.53092 10.1079 6.58836 10.247 6.58836 10.4103C6.58836 10.5736 6.53092 10.7126 6.41603 10.8275C6.30103 10.9425 6.16192 11 5.9987 11ZM3.33203 11C3.16881 11 3.0297 10.9425 2.9147 10.8275C2.79981 10.7126 2.74236 10.5736 2.74236 10.4103C2.74236 10.247 2.79981 10.1079 2.9147 9.99301C3.0297 9.87801 3.16881 9.82051 3.33203 9.82051C3.49525 9.82051 3.63436 9.87801 3.74936 9.99301C3.86425 10.1079 3.9217 10.247 3.9217 10.4103C3.9217 10.5736 3.86425 10.7126 3.74936 10.8275C3.63436 10.9425 3.49525 11 3.33203 11ZM8.66536 11C8.50214 11 8.36303 10.9425 8.24803 10.8275C8.13314 10.7126 8.0757 10.5736 8.0757 10.4103C8.0757 10.247 8.13314 10.1079 8.24803 9.99301C8.36303 9.87801 8.50214 9.82051 8.66536 9.82051C8.82859 9.82051 8.9677 9.87801 9.0827 9.99301C9.19759 10.1079 9.25503 10.247 9.25503 10.4103C9.25503 10.5736 9.19759 10.7126 9.0827 10.8275C8.9677 10.9425 8.82859 11 8.66536 11Z","fill","#8B95A5"],[1,"text-value"],["width","8","height","4","viewBox","0 0 8 4","fill","none","xmlns","http://www.w3.org/2000/svg"],["d","M4.0013 3.99999L0.667969 0.666656H7.33463L4.0013 3.99999Z","fill","#B9C0CA"],["class","overlay",3,"click",4,"ngIf"],[1,"overlay",3,"click"],[1,"date-content",3,"mouseLeave"],["class","date-text",3,"click",4,"ngFor","ngForOf"],[1,"date-text",3,"click"],["class","input-section read-mode",4,"ngIf"],[1,"input-section","read-mode"],[1,"advance-section"],["class","advance-header",4,"ngIf"],["class","configuration-section",4,"ngIf","ngIfElse"],["readModeTemplate",""],[1,"advance-header"],[1,"configuration-section"],[4,"ngFor","ngForOf"],[1,"configure-header"],["class","section-class",4,"ngFor","ngForOf"],[1,"section-class"],[1,"value-text"],["appearance","outline",1,"custom-mat-select"],[3,"ngModel","ngModelChange","selectionChange"],[3,"value"],[3,"value",4,"ngFor","ngForOf"],["type","checkbox",3,"ngModel","ngModelChange","change"],[1,"value-text",3,"ngStyle"],["type","radio",3,"name","ngModel","value","ngModelChange","click"],["type","number","pattern","[0-9]*","digitOnly","",1,"custom-number-type",3,"ngModel","min","max","keypress","ngModelChange","input"],["class","configuration-section read-template",4,"ngIf"],[1,"configuration-section","read-template"],["class","config-section",4,"ngIf"],[1,"config-section"],[1,"configure-header","read-mode"],["class","section-class",3,"display",4,"ngFor","ngForOf"],[1,"value-text","read-mode"],[4,"ngIf"],[1,"d-flex","ml-2"],["class","value-text read-mode",4,"ngIf"],[1,"row","button-class"],["class","advance-btn",3,"click",4,"ngIf"],["class","generate-btn",3,"click",4,"ngIf"],["class","generate-btn disable",4,"ngIf"],[1,"advance-btn",3,"click"],[1,"generate-btn",3,"click"],["width","18","height","16","viewBox","0 0 18 16","fill","none","xmlns","http://www.w3.org/2000/svg"],["d","M10.7797 -0.000457764L12.3824 5.06322L17.4461 6.66593L12.3824 8.26864L10.7797 13.3323L9.17696 8.26864L4.11328 6.66593L9.17696 5.06322L10.7797 -0.000457764Z","fill","#F6F6F7"],["d","M3.88788 9.33408L4.68924 11.8659L7.22108 12.6673L4.68924 13.4686L3.88788 16.0005L3.08653 13.4686L0.554688 12.6673L3.08653 11.8659L3.88788 9.33408Z","fill","#F7F9FB"],[1,"generate-btn","disable"],["d","M10.7797 -0.000457764L12.3824 5.06322L17.4461 6.66593L12.3824 8.26864L10.7797 13.3323L9.17696 8.26864L4.11328 6.66593L9.17696 5.06322L10.7797 -0.000457764Z","fill","#a8acb2"],["d","M3.88788 9.33408L4.68924 11.8659L7.22108 12.6673L4.68924 13.4686L3.88788 16.0005L3.08653 13.4686L0.554688 12.6673L3.08653 11.8659L3.88788 9.33408Z","fill","#a8acb2"]],template:function(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",0),h["\u0275\u0275element"](1,"img",1),h["\u0275\u0275elementStart"](2,"div",2),h["\u0275\u0275elementStart"](3,"div",3),h["\u0275\u0275elementStart"](4,"div",4),h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275elementStart"](5,"svg",5),h["\u0275\u0275element"](6,"path",6),h["\u0275\u0275element"](7,"path",7),h["\u0275\u0275element"](8,"path",8),h["\u0275\u0275element"](9,"path",9),h["\u0275\u0275elementEnd"](),h["\u0275\u0275namespaceHTML"](),h["\u0275\u0275elementStart"](10,"span",10),h["\u0275\u0275text"](11),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](12,L,4,0,"div",11),h["\u0275\u0275template"](13,I,7,0,"div",11),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](14,"div",12),h["\u0275\u0275elementStart"](15,"div",13),h["\u0275\u0275elementStart"](16,"div",14),h["\u0275\u0275elementStart"](17,"div",15),h["\u0275\u0275text"](18," Duration "),h["\u0275\u0275elementStart"](19,"span",16),h["\u0275\u0275text"](20,"*"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](21,T,10,2,"div",17),h["\u0275\u0275template"](22,F,1,1,"ng-template",null,18,h["\u0275\u0275templateRefExtractor"]),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](24,rt,5,3,"div",19),h["\u0275\u0275template"](25,mt,5,4,"div",20),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275reference"](23);h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("src",e.aiIconShort,h["\u0275\u0275sanitizeUrl"]),h["\u0275\u0275advance"](10),h["\u0275\u0275textInterpolate"](e.customDescription),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf","R"==e.mode&&!e.isPromptApiInProgress),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf","E"==e.mode&&!e.isPromptApiInProgress),h["\u0275\u0275advance"](8),h["\u0275\u0275property"]("ngIf","R"!=e.mode)("ngIfElse",t),h["\u0275\u0275advance"](3),h["\u0275\u0275property"]("ngIf",e.showAdvanceFilters),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf","R"!=e.mode)}},directives:[_.NgIf,_.NgForOf,w.c,E.c,y.v,y.y,k.p,y.b,_.NgStyle,y.C,y.e,y.A,y.B],styles:['@charset "UTF-8";.custom-card[_ngcontent-%COMP%]{display:flex;box-shadow:0 4px 6px 0 rgba(0,0,0,.12156862745098039);box-shadow:0 2px 1px 0 rgba(0,0,0,.12156862745098039);border:1px solid #dadce2;border-radius:4px;flex-direction:column;width:80%}.custom-card[_ngcontent-%COMP%]   .custom-header[_ngcontent-%COMP%]{display:flex;background-color:#fafafa;flex-direction:row;gap:4px;border-bottom:1px solid #dadce2;padding:14px;justify-content:space-between;align-items:center}.custom-card[_ngcontent-%COMP%]   .custom-header[_ngcontent-%COMP%]   .header-align-class[_ngcontent-%COMP%]{display:flex;align-items:center;flex-direction:row;gap:10px}.custom-card[_ngcontent-%COMP%]   .custom-header[_ngcontent-%COMP%]   .edit-class[_ngcontent-%COMP%]{cursor:pointer}.custom-card[_ngcontent-%COMP%]   .custom-header[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-weight:600;font-size:14px;letter-spacing:2%;color:#45546e}.custom-card[_ngcontent-%COMP%]   .custom-body-class[_ngcontent-%COMP%]{display:flex;padding:14px;flex-direction:column;gap:10px}.custom-card[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]{display:flex}.custom-card[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .content-class[_ngcontent-%COMP%]{display:flex;flex-direction:column;row-gap:8px}.custom-card[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .content-header[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-weight:500;font-size:14px;letter-spacing:2%;color:#45546e}.custom-card[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .content-header[_ngcontent-%COMP%]   .asterick-class[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-weight:500;font-size:14px;color:#ff3a46;margin-left:1px}.custom-card[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .input-section[_ngcontent-%COMP%]{border-radius:16px;border:1px solid #8b95a5;min-height:32px;display:flex;align-items:center;justify-content:space-between;padding:6px 12px;cursor:pointer}.custom-card[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .input-section.read-mode[_ngcontent-%COMP%]{border:none}.custom-card[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .input-section[_ngcontent-%COMP%]   .input-left-align[_ngcontent-%COMP%]{display:flex;align-items:center;gap:6px;width:92%}.custom-card[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .input-section[_ngcontent-%COMP%]   .input-left-align[_ngcontent-%COMP%]   .text-value[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-weight:400;font-size:12px;color:#1b2140;max-width:74%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;position:relative}.custom-card[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .input-section[_ngcontent-%COMP%]   .overlay[_ngcontent-%COMP%]{align-items:center;max-height:232px;z-index:5;overflow:hidden;position:absolute;top:57px;left:4px;width:95%;background:#fff;border-radius:8px;box-shadow:0 2px 1px 0 rgba(0,0,0,.12156862745098039);border:1px solid #e8e9ee;min-height:90px;overflow-y:auto;display:flex;pointer-events:all!important}.custom-card[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .input-section[_ngcontent-%COMP%]   .overlay[_ngcontent-%COMP%]   .date-content[_ngcontent-%COMP%]{overflow-y:auto;display:flex;flex-direction:column;padding:12px 6px;margin-right:2px;gap:0;max-height:200px;width:100%;position:relative}.custom-card[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .input-section[_ngcontent-%COMP%]   .overlay[_ngcontent-%COMP%]   .date-content[_ngcontent-%COMP%]   .date-text[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);padding:3px;border-radius:3px;cursor:pointer;width:100%;font-weight:400;font-size:13px;color:#5f6c81}.custom-card[_ngcontent-%COMP%]   .content-section[_ngcontent-%COMP%]   .input-section[_ngcontent-%COMP%]   .overlay[_ngcontent-%COMP%]   .date-content[_ngcontent-%COMP%]   .date-text[_ngcontent-%COMP%]:hover{background:#ffecee}.custom-card[_ngcontent-%COMP%]   .advance-section[_ngcontent-%COMP%]{margin-top:12px}.custom-card[_ngcontent-%COMP%]   .advance-section[_ngcontent-%COMP%]   .advance-header[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-weight:400;font-size:14px;color:#515965;margin-top:6px;margin-bottom:14px}.custom-card[_ngcontent-%COMP%]   .advance-section[_ngcontent-%COMP%]   .configuration-section[_ngcontent-%COMP%]{margin-left:14px}.custom-card[_ngcontent-%COMP%]   .advance-section[_ngcontent-%COMP%]   .configuration-section.read-template[_ngcontent-%COMP%]{margin-left:0}.custom-card[_ngcontent-%COMP%]   .advance-section[_ngcontent-%COMP%]   .configuration-section[_ngcontent-%COMP%]   .config-section[_ngcontent-%COMP%]{margin-left:14px}.custom-card[_ngcontent-%COMP%]   .advance-section[_ngcontent-%COMP%]   .configuration-section[_ngcontent-%COMP%]   .section-data[_ngcontent-%COMP%]{margin-bottom:16px}.custom-card[_ngcontent-%COMP%]   .advance-section[_ngcontent-%COMP%]   .configuration-section[_ngcontent-%COMP%]   .configure-header[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-weight:500;font-size:12px;color:#45546e;padding:2px 0;margin-top:18px}.custom-card[_ngcontent-%COMP%]   .advance-section[_ngcontent-%COMP%]   .configuration-section[_ngcontent-%COMP%]   .configure-header.read-mode[_ngcontent-%COMP%]{color:#5f6c81!important}.custom-card[_ngcontent-%COMP%]   .advance-section[_ngcontent-%COMP%]   .configuration-section[_ngcontent-%COMP%]   .section-class[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin-left:8px;padding:4px 0}.custom-card[_ngcontent-%COMP%]   .advance-section[_ngcontent-%COMP%]   .configuration-section[_ngcontent-%COMP%]   .value-text[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-weight:400;font-size:13px;color:#5f6c81}.custom-card[_ngcontent-%COMP%]   .advance-section[_ngcontent-%COMP%]   .configuration-section[_ngcontent-%COMP%]   .value-text.read-mode[_ngcontent-%COMP%]{color:#45546e}.custom-card[_ngcontent-%COMP%]   .advance-section[_ngcontent-%COMP%]   input[type=checkbox][_ngcontent-%COMP%]{appearance:none;-webkit-appearance:none;-moz-appearance:none;width:16px;height:16px;background-color:#fff;border:1.5px solid #dadce2;border-radius:4px;cursor:pointer;position:relative;transition:background-color .2s}.custom-card[_ngcontent-%COMP%]   .advance-section[_ngcontent-%COMP%]   input[type=checkbox][_ngcontent-%COMP%]:checked{background-color:#ee4961;border:none}.custom-card[_ngcontent-%COMP%]   .advance-section[_ngcontent-%COMP%]   input[type=checkbox][_ngcontent-%COMP%]:checked:after{content:"\u2714";font-size:11px;color:#fff;position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);font-weight:700}.custom-card[_ngcontent-%COMP%]   .advance-section[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]{width:15px;height:15px;accent-color:#ee4961;display:flex;cursor:pointer}.custom-card[_ngcontent-%COMP%]   .advance-section[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]:checked{background-color:#ee4961;border-color:#ee4961}.custom-card[_ngcontent-%COMP%]   .button-class[_ngcontent-%COMP%]{display:flex;flex-direction:column;row-gap:14px;margin-top:20px;margin-bottom:4px}.custom-card[_ngcontent-%COMP%]   .button-class[_ngcontent-%COMP%]   .advance-btn[_ngcontent-%COMP%]{border:.5px solid #526179;border-radius:8px;color:#526179;font-family:var(--kebsFontFamily);font-weight:500;font-size:14px}.custom-card[_ngcontent-%COMP%]   .button-class[_ngcontent-%COMP%]   .advance-btn[_ngcontent-%COMP%], .custom-card[_ngcontent-%COMP%]   .button-class[_ngcontent-%COMP%]   .generate-btn[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;min-height:32px;cursor:pointer}.custom-card[_ngcontent-%COMP%]   .button-class[_ngcontent-%COMP%]   .generate-btn[_ngcontent-%COMP%]{gap:4px;border-radius:8px;background-color:#ee4961;color:#fff}.custom-card[_ngcontent-%COMP%]   .button-class[_ngcontent-%COMP%]   .generate-btn.disable[_ngcontent-%COMP%]{color:#a8acb2;background:#e8e9ee;cursor:auto}.custom-card[_ngcontent-%COMP%]   .custom-mat-select[_ngcontent-%COMP%]{width:33%;margin-top:11px}.custom-card[_ngcontent-%COMP%]   .custom-mat-select[_ngcontent-%COMP%]   .mat-select[_ngcontent-%COMP%]{background-color:#fff;font-size:13px;cursor:pointer}.custom-card[_ngcontent-%COMP%]   .mat-option[_ngcontent-%COMP%]{font-size:13px;padding:12px}.custom-card[_ngcontent-%COMP%]   .mat-option[_ngcontent-%COMP%]:hover{background-color:#ee4961;color:#fff}.custom-card[_ngcontent-%COMP%]   .mat-option.mat-selected[_ngcontent-%COMP%]{background-color:#ee4961!important;color:#fff!important}.custom-card[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-infix{padding:6px!important}.custom-card[_ngcontent-%COMP%]     .mat-select-value-text{color:#5f6c81!important}.custom-report-class[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:4px}.custom-report-class[_ngcontent-%COMP%]   .ai-icon[_ngcontent-%COMP%]{width:32px;height:32px}.custom-number-type[_ngcontent-%COMP%]{width:18%;padding:5px;border:1px solid #e8e9ee;border-radius:4px;font-size:14px;transition:border-color .3s,color .3s;color:#5f6c81}.custom-number-type[_ngcontent-%COMP%]:hover{border-color:#e8e9ee;color:#5f6c81}.custom-number-type[_ngcontent-%COMP%]:focus{border-color:#e8e9ee;outline:none}']}),t})();var Ct=n("XTQC"),ft=n("AFgX");const xt=function(t){return{"pointer-events":t}};function _t(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div"),h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275elementStart"](1,"svg",15),h["\u0275\u0275listener"]("click",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](4).regenerateResponse()})),h["\u0275\u0275element"](2,"path",16),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"](4);h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngStyle",h["\u0275\u0275pureFunction1"](1,xt,t.isThreadLoading||t.isPromptApiInProgress?"none":""))}}const vt=function(t,e){return{fill:t,cursor:e}};function yt(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275elementStart"](0,"svg",17),h["\u0275\u0275listener"]("click",(function(){h["\u0275\u0275restoreView"](t);const e=h["\u0275\u0275nextContext"](3).ngIf;return h["\u0275\u0275nextContext"]().likeAndDislikeResponse(!0!==e.is_liked||null,e)})),h["\u0275\u0275element"](1,"path",18),h["\u0275\u0275elementEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"](3).ngIf;h["\u0275\u0275property"]("ngStyle",h["\u0275\u0275pureFunction2"](1,vt,!0===t.is_liked?"#1C1B1F":"#7D838B",1==t.is_liked?"":"pointer"))}}function Pt(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275elementStart"](0,"svg",17),h["\u0275\u0275listener"]("click",(function(){h["\u0275\u0275restoreView"](t);const e=h["\u0275\u0275nextContext"](3).ngIf;return h["\u0275\u0275nextContext"]().likeAndDislikeResponse(!1===e.is_liked&&null,e)})),h["\u0275\u0275element"](1,"path",19),h["\u0275\u0275elementEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"](3).ngIf;h["\u0275\u0275property"]("ngStyle",h["\u0275\u0275pureFunction2"](1,vt,!1===t.is_liked?"#1C1B1F":"#7D838B",0==t.is_liked?"":"pointer"))}}const Mt=function(t){return{display:t}},Ot=function(t,e){return{"pointer-events":t,color:e}};function bt(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",8),h["\u0275\u0275elementStart"](1,"div",9),h["\u0275\u0275elementStart"](2,"mat-icon",10),h["\u0275\u0275listener"]("click",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](3).goToPrevious()})),h["\u0275\u0275text"](3," chevron_left "),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](4,"span",11),h["\u0275\u0275text"](5),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](6,"mat-icon",10),h["\u0275\u0275listener"]("click",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](3).goToNext()})),h["\u0275\u0275text"](7," chevron_right "),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](8,"div",12),h["\u0275\u0275template"](9,_t,3,3,"div",13),h["\u0275\u0275template"](10,yt,2,4,"svg",14),h["\u0275\u0275template"](11,Pt,2,4,"svg",14),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"](2).ngIf,e=h["\u0275\u0275nextContext"]();h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngStyle",h["\u0275\u0275pureFunction1"](7,Mt,e.flatList&&e.flatList.length>1?"":"none")),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngStyle",h["\u0275\u0275pureFunction2"](9,Ot,0==e.currentIndex?"none":"",0==e.currentIndex?"#B9C0CA":"")),h["\u0275\u0275advance"](3),h["\u0275\u0275textInterpolate"](e.currentIndex+1),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngStyle",h["\u0275\u0275pureFunction2"](12,Ot,e.currentIndex==e.flatList.length-1?"none":"",e.currentIndex==e.flatList.length-1?"#B9C0CA":"")),h["\u0275\u0275advance"](3),h["\u0275\u0275property"]("ngIf",!e.hideRegenerate),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!0===t.is_liked||null===t.is_liked),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!1===t.is_liked||null===t.is_liked)}}function St(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",5),h["\u0275\u0275element"](1,"div",6),h["\u0275\u0275pipe"](2,"safeHtml"),h["\u0275\u0275pipe"](3,"markdownCustom"),h["\u0275\u0275template"](4,bt,12,15,"div",7),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"]().ngIf,e=h["\u0275\u0275nextContext"]();h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("innerHTML",e.errorMessage(null==t?null:t.response_content)?h["\u0275\u0275pipeBind1"](2,2,e.errorMessage(null==t?null:t.response_content)):h["\u0275\u0275pipeBind1"](3,4,null==t?null:t.response_content),h["\u0275\u0275sanitizeHtml"]),h["\u0275\u0275advance"](3),h["\u0275\u0275property"]("ngIf",e.errorMessage(null==t?null:t.response_content,!0))}}function wt(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",8),h["\u0275\u0275elementStart"](1,"div",9),h["\u0275\u0275elementStart"](2,"mat-icon",10),h["\u0275\u0275listener"]("click",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](3).goToPrevious()})),h["\u0275\u0275text"](3," chevron_left "),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](4,"span",11),h["\u0275\u0275text"](5),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](6,"mat-icon",10),h["\u0275\u0275listener"]("click",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](3).goToNext()})),h["\u0275\u0275text"](7," chevron_right "),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](8,"div",12),h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275elementStart"](9,"svg",15),h["\u0275\u0275listener"]("click",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](3).regenerateResponse()})),h["\u0275\u0275element"](10,"path",16),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"](3);h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngStyle",h["\u0275\u0275pureFunction1"](5,Mt,t.flatList&&t.flatList.length>1?"":"none")),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngStyle",h["\u0275\u0275pureFunction2"](7,Ot,0==t.currentIndex?"none":"",0==t.currentIndex?"#B9C0CA":"")),h["\u0275\u0275advance"](3),h["\u0275\u0275textInterpolate"](t.currentIndex+1),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngStyle",h["\u0275\u0275pureFunction2"](10,Ot,t.currentIndex==t.flatList.length-1?"none":"",t.currentIndex==t.flatList.length-1?"#B9C0CA":"")),h["\u0275\u0275advance"](3),h["\u0275\u0275property"]("ngStyle",h["\u0275\u0275pureFunction1"](13,xt,t.isThreadLoading||t.isPromptApiInProgress?"none":""))}}function Et(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",20),h["\u0275\u0275pipe"](1,"safeHtml"),h["\u0275\u0275template"](2,wt,11,15,"div",7),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"]().ngIf,e=h["\u0275\u0275nextContext"]();h["\u0275\u0275property"]("innerHTML",h["\u0275\u0275pipeBind1"](1,2,e.errorMessage(e.errorCode)),h["\u0275\u0275sanitizeHtml"]),h["\u0275\u0275advance"](2),h["\u0275\u0275property"]("ngIf",e.errorMessage(null==t?null:t.response_content,!0))}}function kt(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",1),h["\u0275\u0275element"](1,"img",2),h["\u0275\u0275template"](2,St,5,6,"div",3),h["\u0275\u0275template"](3,Et,3,4,"div",4),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"]();h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("src",t.aiIconShort,h["\u0275\u0275sanitizeUrl"]),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!t.responseError),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",t.responseError)}}let Lt=(()=>{class t{constructor(t,e){this._aiService=t,this._toaster=e,this.item={},this.aiIconShort="",this.aid=null,this.oid="",this.isThreadLoading=!1,this.isPromptApiInProgress=!1,this.responseError=!1,this.errorCode="ERR_API_001",this.responseText=[],this.hideRegenerate=!1,this.regenerateResponseEmitter=new h.EventEmitter,this._onDestroy=new d.b,this.flatList=[],this.currentIndex=0,this.childrenKey="children"}ngOnInit(){this.flattenTree([this.item]),this.setCurrentIndexToLatest()}flattenTree(t){for(const e of t)this.flatList.push(e),e[this.childrenKey]&&this.flattenTree(e[this.childrenKey])}setCurrentIndexToLatest(){if(this.flatList.length>0){let t=0,e=new Date(this.flatList[0].created_at);for(let n=1;n<this.flatList.length;n++){const o=new Date(this.flatList[n].created_at);o>e&&(e=o,t=n)}this.currentIndex=t}}getCurrentNode(){return this.flatList[this.currentIndex]||null}modifyCurrentNode(t,e){const n=this.getCurrentNode();n&&(n[t]=e)}goToNext(){this.currentIndex<this.flatList.length-1&&this.currentIndex++}goToPrevious(){this.currentIndex>0&&this.currentIndex--}copyToClipboard(){this.modifyCurrentNode("isCopying",!0),setTimeout(()=>{this.modifyCurrentNode("isCopying",!1)},2e3)}regenerateResponse(){const t=this.getCurrentNode();this.regenerateResponseEmitter.emit(t)}likeAndDislikeResponse(t,e){return Object(o.c)(this,void 0,void 0,(function*(){if(null!==t){let n={aid:this.aid,oid:this.oid,chat_id:e._id,is_liked:t};return new Promise((e,o)=>{this._aiService.likeAndDislikeResponse(n).pipe(Object(p.a)(this._onDestroy)).subscribe({next:n=>{0==n.err?this.modifyCurrentNode("is_liked",t):this._toaster.showError("Error",n.msg,7e3),e(!0)},error:t=>{this._toaster.showError("Error","Error in Updating Response",7e3),o()}})})}}))}errorMessage(t,e=!1){var n,o;t=t||"ERR_API_001";const i=null===(n=this._aiService)||void 0===n?void 0:n.aiThemeConfig["RESPONSE-TEXT"].find(e=>(null==e?void 0:e.info_code)===t);return e?!i||null===(o=null==i?void 0:i.icon_enable)||void 0===o||o:i?null==i?void 0:i.info_html:null}}return t.\u0275fac=function(e){return new(e||t)(h["\u0275\u0275directiveInject"](m.a),h["\u0275\u0275directiveInject"](C.a))},t.\u0275cmp=h["\u0275\u0275defineComponent"]({type:t,selectors:[["app-chat-response-structure"]],inputs:{item:"item",aiIconShort:"aiIconShort",aid:"aid",oid:"oid",isThreadLoading:"isThreadLoading",isPromptApiInProgress:"isPromptApiInProgress",responseError:"responseError",errorCode:"errorCode",responseText:"responseText",hideRegenerate:"hideRegenerate"},outputs:{regenerateResponseEmitter:"regenerateResponseEmitter"},decls:1,vars:1,consts:[["class","response-ui",4,"ngIf"],[1,"response-ui"],[1,"ai-icon",3,"src"],["class","response-data",4,"ngIf"],["class","response-data",3,"innerHTML",4,"ngIf"],[1,"response-data"],[1,"response-text",3,"innerHTML"],["class","icons",4,"ngIf"],[1,"icons"],[1,"left-icons",3,"ngStyle"],[1,"icon",3,"ngStyle","click"],[1,"text"],[1,"right-icons"],[4,"ngIf"],["width","14","height","13","viewBox","0 0 14 13",3,"ngStyle","click",4,"ngIf"],["width","14","height","15","viewBox","0 0 14 15","fill","none",1,"icon",3,"ngStyle","click"],["d","M5.43583 14.5391C4.05875 14.1898 2.93354 13.4604 2.06021 12.3508C1.18674 11.2413 0.75 9.95745 0.75 8.49911C0.75 7.75023 0.880625 7.03418 1.14187 6.35099C1.40312 5.66766 1.76875 5.04078 2.23875 4.47036C2.35417 4.34106 2.49361 4.27425 2.65708 4.26995C2.82056 4.26564 2.97118 4.33245 3.10896 4.47036C3.22438 4.58564 3.28424 4.72634 3.28854 4.89245C3.29271 5.0587 3.23715 5.21495 3.12188 5.3612C2.75646 5.79717 2.47813 6.2828 2.28688 6.81807C2.09563 7.35335 2 7.9137 2 8.49911C2 9.6455 2.3459 10.665 3.03771 11.5577C3.72951 12.4503 4.61493 13.0429 5.69396 13.3356C5.83174 13.3752 5.945 13.4527 6.03375 13.5681C6.12236 13.6834 6.16667 13.8099 6.16667 13.9479C6.16667 14.1562 6.09479 14.3202 5.95104 14.4397C5.80743 14.5595 5.63569 14.5926 5.43583 14.5391ZM8.56417 14.5552C8.36431 14.6086 8.19257 14.5734 8.04896 14.4493C7.90521 14.3254 7.83333 14.1593 7.83333 13.951C7.83333 13.8217 7.87764 13.6994 7.96625 13.5841C8.055 13.4687 8.16826 13.3886 8.30604 13.3437C9.37979 13.0156 10.2639 12.4115 10.9583 11.5312C11.6528 10.6508 12 9.64009 12 8.49911C12 7.11023 11.5139 5.92967 10.5417 4.95745C9.56944 3.98523 8.38889 3.49911 7 3.49911H6.70521L7.39104 4.18495C7.51174 4.30578 7.57208 4.45217 7.57208 4.62411C7.57208 4.7962 7.51174 4.94259 7.39104 5.06328C7.27035 5.18398 7.12396 5.24432 6.95188 5.24432C6.77993 5.24432 6.63361 5.18398 6.51292 5.06328L4.85104 3.40141C4.77299 3.32335 4.71792 3.24106 4.68583 3.15453C4.65389 3.068 4.63792 2.97453 4.63792 2.87411C4.63792 2.7737 4.65389 2.68023 4.68583 2.5937C4.71792 2.50717 4.77299 2.42488 4.85104 2.34682L6.51292 0.684948C6.63361 0.564254 6.77993 0.503906 6.95188 0.503906C7.12396 0.503906 7.27035 0.564254 7.39104 0.684948C7.51174 0.805781 7.57208 0.952171 7.57208 1.12411C7.57208 1.2962 7.51174 1.44259 7.39104 1.56328L6.70521 2.24911H7C8.74361 2.24911 10.2212 2.85488 11.4327 4.06641C12.6442 5.27793 13.25 6.7555 13.25 8.49911C13.25 9.94356 12.8119 11.2213 11.9358 12.3324C11.0597 13.4436 9.93583 14.1845 8.56417 14.5552Z","fill","#1C1B1F"],["width","14","height","13","viewBox","0 0 14 13",3,"ngStyle","click"],["d","M12.7942 4.33308C13.1113 4.33308 13.3914 4.45469 13.6345 4.69791C13.8777 4.94102 13.9993 5.22114 13.9993 5.53825V6.61508C13.9993 6.68508 13.9903 6.76008 13.9723 6.84008C13.9545 6.91997 13.9344 6.99458 13.9122 7.06391L12.0015 11.5722C11.9061 11.7855 11.746 11.9656 11.5212 12.1126C11.2964 12.2596 11.0618 12.3331 10.8175 12.3331H3.80702V4.33308L7.68518 0.488247C7.81774 0.355803 7.97118 0.276748 8.14552 0.251081C8.31985 0.225414 8.4869 0.255303 8.64668 0.340748C8.80657 0.426303 8.92368 0.547692 8.99802 0.704914C9.07235 0.862136 9.08818 1.02497 9.04552 1.19341L8.32752 4.33308H12.7942ZM4.80701 4.75875V11.3331H10.8198C10.8668 11.3331 10.9149 11.3202 10.964 11.2946C11.0132 11.2689 11.0507 11.2262 11.0763 11.1664L12.9993 6.66641V5.53825C12.9993 5.47836 12.9801 5.42919 12.9417 5.39075C12.9032 5.3523 12.8541 5.33308 12.7942 5.33308H7.06352L7.89935 1.67925L4.80701 4.75875ZM1.87118 12.3331C1.53974 12.3331 1.25602 12.2151 1.02002 11.9791C0.784016 11.7431 0.666016 11.4594 0.666016 11.1279V5.53825C0.666016 5.2068 0.784016 4.92308 1.02002 4.68708C1.25602 4.45108 1.53974 4.33308 1.87118 4.33308H3.80702V5.33308H1.87118C1.81129 5.33308 1.76213 5.3523 1.72368 5.39075C1.68524 5.42919 1.66602 5.47836 1.66602 5.53825V11.1279C1.66602 11.1878 1.68524 11.237 1.72368 11.2754C1.76213 11.3139 1.81129 11.3331 1.87118 11.3331H3.80702V12.3331H1.87118Z"],["d","M1.20517 8C0.888056 8 0.607944 7.87839 0.364833 7.63517C0.121611 7.39206 0 7.11194 0 6.79483V5.718C0 5.648 0.00899998 5.573 0.027 5.493C0.0448889 5.41311 0.0649445 5.3385 0.0871667 5.26917L1.99783 0.760834C2.09328 0.547612 2.25339 0.3675 2.47817 0.2205C2.70294 0.0734997 2.9375 0 3.18183 0H10.1923V8L6.31417 11.8448C6.18161 11.9773 6.02817 12.0563 5.85383 12.082C5.6795 12.1077 5.51244 12.0778 5.35267 11.9923C5.19278 11.9068 5.07567 11.7854 5.00133 11.6282C4.927 11.4709 4.91117 11.3081 4.95383 11.1397L5.67183 8H1.20517ZM9.19233 7.57433V1H3.1795C3.1325 1 3.08444 1.01283 3.03533 1.0385C2.98611 1.06417 2.94867 1.10689 2.923 1.16667L1 5.66667V6.79483C1 6.85472 1.01922 6.90389 1.05767 6.94233C1.09611 6.98078 1.14528 7 1.20517 7H6.93583L6.1 10.6538L9.19233 7.57433ZM12.1282 0C12.4596 0 12.7433 0.118 12.9793 0.354C13.2153 0.59 13.3333 0.873722 13.3333 1.20517V6.79483C13.3333 7.12628 13.2153 7.41 12.9793 7.646C12.7433 7.882 12.4596 8 12.1282 8H10.1923V7H12.1282C12.1881 7 12.2372 6.98078 12.2757 6.94233C12.3141 6.90389 12.3333 6.85472 12.3333 6.79483V1.20517C12.3333 1.14528 12.3141 1.09611 12.2757 1.05767C12.2372 1.01922 12.1881 1 12.1282 1H10.1923V0H12.1282Z"],[1,"response-data",3,"innerHTML"]],template:function(t,e){1&t&&h["\u0275\u0275template"](0,kt,4,3,"div",0),2&t&&h["\u0275\u0275property"]("ngIf",e.getCurrentNode())},directives:[_.NgIf,_.NgStyle,P.a],pipes:[Ct.a,ft.a],styles:[".response-ui[_ngcontent-%COMP%]{display:flex;justify-content:space-between;gap:8px;width:85%}.response-ui[_ngcontent-%COMP%]   .ai-icon[_ngcontent-%COMP%]{width:32px;height:32px}.response-ui[_ngcontent-%COMP%]   .response-data[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:8px;padding:0 8px;width:100%;overflow:hidden}.response-ui[_ngcontent-%COMP%]   .response-data[_ngcontent-%COMP%]   .response-text[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-size:12px;font-weight:400;color:#45546e;overflow:auto}.response-ui[_ngcontent-%COMP%]   .response-data[_ngcontent-%COMP%]   .icons[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;border:1px solid #dadce2;border-radius:6px;padding:6px 16px;gap:8px;width:-moz-fit-content;width:fit-content}.response-ui[_ngcontent-%COMP%]   .response-data[_ngcontent-%COMP%]   .icons[_ngcontent-%COMP%]   .left-icons[_ngcontent-%COMP%]{display:flex;align-items:center;gap:6px}.response-ui[_ngcontent-%COMP%]   .response-data[_ngcontent-%COMP%]   .icons[_ngcontent-%COMP%]   .left-icons[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{font-size:17px;width:17px;height:17px;color:#1c1b1f;cursor:pointer}.response-ui[_ngcontent-%COMP%]   .response-data[_ngcontent-%COMP%]   .icons[_ngcontent-%COMP%]   .left-icons[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-size:12px;font-weight:500;color:#45546e}.response-ui[_ngcontent-%COMP%]   .response-data[_ngcontent-%COMP%]   .icons[_ngcontent-%COMP%]   .right-icons[_ngcontent-%COMP%]{display:flex;align-items:center;gap:10px}.response-ui[_ngcontent-%COMP%]   .response-data[_ngcontent-%COMP%]   .icons[_ngcontent-%COMP%]   .right-icons[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{cursor:pointer}.response-ui[_ngcontent-%COMP%]   .response-data[_ngcontent-%COMP%]   .icons[_ngcontent-%COMP%]   .right-icons[_ngcontent-%COMP%]   .tick-icon-copy[_ngcontent-%COMP%]{max-width:14px}.response-ui[_ngcontent-%COMP%]     .align-items-same-line{display:flex;flex-direction:row;gap:4px;align-items:center}.response-ui[_ngcontent-%COMP%]     .align-items-same-line .error-text-header{font-family:var(--kebsFontFamily);font-weight:700;font-size:14px;color:#fa8c16}.response-ui[_ngcontent-%COMP%]    .error-sub-text{font-family:var(--kebsFontFamily);font-weight:400;font-size:14px;color:#272a47}.response-ui[_ngcontent-%COMP%]     .kais-pivot-table{border-collapse:collapse;width:100%;font-family:Segoe UI,Tahoma,Geneva,Verdana,sans-serif;background-color:#1e1e2f;color:#f0f0f0}.response-ui[_ngcontent-%COMP%]     .kais-pivot-table th{background-color:#2d2d3a;font-weight:700;color:#00bcd4;padding:10px;border:1px solid #444;text-align:center}.response-ui[_ngcontent-%COMP%]     .kais-pivot-table td{border:1px solid #333;padding:8px;text-align:right}.response-ui[_ngcontent-%COMP%]     .kais-pivot-table td:first-child{text-align:left;font-weight:700;color:#fc0}.response-ui[_ngcontent-%COMP%]     .kais-pivot-table tr:nth-child(2n){background-color:#252536}.response-ui[_ngcontent-%COMP%]     .kais-pivot-table tr:nth-child(odd){background-color:#1e1e2f}"]}),t})();function It(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementContainerStart"](0),h["\u0275\u0275elementStart"](1,"app-chat-custom-report",9),h["\u0275\u0275listener"]("generateResponseEmitter",(function(e){h["\u0275\u0275restoreView"](t);const n=h["\u0275\u0275nextContext"](3).index;return h["\u0275\u0275nextContext"]().regenerateResponse(e,n,!1)})),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementContainerEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"](3).$implicit,e=h["\u0275\u0275nextContext"]();h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("moduleListData",e.moduleList)("customizePromptConfig",e.customizePromptConfig)("mode","C")("currentChatData",t)("aiIconShort",e.aiIconShort)("isPromptApiInProgress",e.isPromptApiInProgress)}}function Dt(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementContainerStart"](0),h["\u0275\u0275elementStart"](1,"app-chat-custom-report",9),h["\u0275\u0275listener"]("generateResponseEmitter",(function(e){h["\u0275\u0275restoreView"](t);const n=h["\u0275\u0275nextContext"](4).index;return h["\u0275\u0275nextContext"]().regenerateResponse(e,n,!1)})),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementContainerEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"](4).$implicit,e=h["\u0275\u0275nextContext"]();h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("moduleListData",e.moduleList)("customizePromptConfig",null==t||null==t.filter_config?null:t.filter_config.config)("mode","R")("currentChatData",t)("aiIconShort",e.aiIconShort)("isPromptApiInProgress",e.isPromptApiInProgress)}}function Vt(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementContainerStart"](0),h["\u0275\u0275template"](1,Dt,2,6,"ng-container",7),h["\u0275\u0275elementStart"](2,"app-chat-response-structure",10),h["\u0275\u0275listener"]("regenerateResponseEmitter",(function(e){h["\u0275\u0275restoreView"](t);const n=h["\u0275\u0275nextContext"](3).index;return h["\u0275\u0275nextContext"]().regenerateResponse(e,n)})),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementContainerEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"](3).$implicit,e=h["\u0275\u0275nextContext"]();h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",null==t?null:t.is_custom_prompt),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("item",t)("aiIconShort",e.aiIconShort)("aid",e.aid)("oid",e.oid)("isThreadLoading",e.isThreadLoading)("hideRegenerate",null==t?null:t.is_custom_prompt)("isPromptApiInProgress",e.isPromptApiInProgress)}}function Tt(t,e){if(1&t&&(h["\u0275\u0275elementContainerStart"](0),h["\u0275\u0275template"](1,It,2,6,"ng-container",7),h["\u0275\u0275template"](2,Vt,3,8,"ng-container",7),h["\u0275\u0275elementContainerEnd"]()),2&t){const t=h["\u0275\u0275nextContext"](2).$implicit;h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",null==t?null:t.isCustomPrompt),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!(null!=t&&t.isCustomPrompt))}}function Ht(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",11),h["\u0275\u0275element"](1,"img",12),h["\u0275\u0275elementStart"](2,"div",13),h["\u0275\u0275element"](3,"img",14),h["\u0275\u0275elementStart"](4,"span",15),h["\u0275\u0275text"](5,"Compiling"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"](3);h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("src",t.aiIconShort,h["\u0275\u0275sanitizeUrl"]),h["\u0275\u0275advance"](2),h["\u0275\u0275property"]("src",t.chatLoader,h["\u0275\u0275sanitizeUrl"])}}function Ft(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",11),h["\u0275\u0275element"](1,"img",12),h["\u0275\u0275elementStart"](2,"div",13),h["\u0275\u0275element"](3,"img",16),h["\u0275\u0275elementStart"](4,"span",15),h["\u0275\u0275text"](5,"Ready"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"](3);h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("src",t.aiIconShort,h["\u0275\u0275sanitizeUrl"]),h["\u0275\u0275advance"](2),h["\u0275\u0275property"]("src",t.aiIconShort,h["\u0275\u0275sanitizeUrl"])}}function Rt(t,e){if(1&t&&(h["\u0275\u0275elementContainerStart"](0),h["\u0275\u0275template"](1,Tt,3,2,"ng-container",7),h["\u0275\u0275template"](2,Ht,6,2,"div",8),h["\u0275\u0275template"](3,Ft,6,2,"div",8),h["\u0275\u0275elementContainerEnd"]()),2&t){const t=h["\u0275\u0275nextContext"]().$implicit;h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!(null!=t&&t.isLoading||null!=t&&t.isReady)),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",null==t?null:t.isLoading),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",null==t?null:t.isReady)}}function zt(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementContainerStart"](0),h["\u0275\u0275elementStart"](1,"app-chat-response-structure",17),h["\u0275\u0275listener"]("regenerateResponseEmitter",(function(e){h["\u0275\u0275restoreView"](t);const n=h["\u0275\u0275nextContext"]().index;return h["\u0275\u0275nextContext"]().regenerateResponse(e,n)})),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementContainerEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"]().$implicit,e=h["\u0275\u0275nextContext"]();h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("item",t)("aiIconShort",e.aiIconShort)("aid",e.aid)("oid",e.oid)("isThreadLoading",e.isThreadLoading)("isPromptApiInProgress",e.isPromptApiInProgress)("responseError",!0)("errorCode",null==t?null:t.ErrorCode)("responseText",e.chatResponseText)}}function At(t,e){if(1&t&&(h["\u0275\u0275elementContainerStart"](0),h["\u0275\u0275elementStart"](1,"div",2),h["\u0275\u0275elementStart"](2,"div",3),h["\u0275\u0275elementStart"](3,"div",4),h["\u0275\u0275elementStart"](4,"div",5),h["\u0275\u0275text"](5),h["\u0275\u0275elementEnd"](),h["\u0275\u0275element"](6,"app-user-image",6),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](7,Rt,4,3,"ng-container",7),h["\u0275\u0275template"](8,zt,2,9,"ng-container",7),h["\u0275\u0275elementContainerEnd"]()),2&t){const t=e.$implicit,n=h["\u0275\u0275nextContext"]();h["\u0275\u0275advance"](5),h["\u0275\u0275textInterpolate"](t.prompt),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("oid",n.oid),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!(null!=t&&t.isError)),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",null==t?null:t.isError)}}let Bt=(()=>{class t{constructor(t,e){this._aiService=t,this._toaster=e,this.chatLoader="",this.aiIconShort="",this.aid=null,this.oid="",this.currentThreadData=[],this.isThreadLoading=!1,this.isPromptApiInProgress=!1,this.chatResponseText=[],this.moduleList=[],this.customizePromptConfig=null,this.regenerateResponseEmitter=new h.EventEmitter,this.generateResponseEmitter=new h.EventEmitter,this._onDestroy=new d.b}ngOnInit(){}copyToClipboard(t){t.isCopying=!0,setTimeout(()=>{t.isCopying=!1},2e3)}regenerateResponse(t,e,n=!0){this.regenerateResponseEmitter.emit({data:t,index:e,regenerate:n,mode:null==t?void 0:t.mode})}likeAndDislikeResponse(t,e){return Object(o.c)(this,void 0,void 0,(function*(){if(null!==t){let n={aid:this.aid,oid:this.oid,chat_id:e._id,is_liked:t};return new Promise((o,i)=>{this._aiService.likeAndDislikeResponse(n).pipe(Object(p.a)(this._onDestroy)).subscribe({next:n=>{0==n.err?e.is_liked=t:this._toaster.showError("Error",n.msg,7e3),o(!0)},error:t=>{this._toaster.showError("Error","Error in Updating Response",7e3),i()}})})}}))}}return t.\u0275fac=function(e){return new(e||t)(h["\u0275\u0275directiveInject"](m.a),h["\u0275\u0275directiveInject"](C.a))},t.\u0275cmp=h["\u0275\u0275defineComponent"]({type:t,selectors:[["app-chat-ui"]],inputs:{chatLoader:"chatLoader",aiIconShort:"aiIconShort",aid:"aid",oid:"oid",currentThreadData:"currentThreadData",isThreadLoading:"isThreadLoading",isPromptApiInProgress:"isPromptApiInProgress",chatResponseText:"chatResponseText",moduleList:"moduleList",customizePromptConfig:"customizePromptConfig"},outputs:{regenerateResponseEmitter:"regenerateResponseEmitter",generateResponseEmitter:"generateResponseEmitter"},decls:2,vars:1,consts:[[1,"chat-ui-container"],[4,"ngFor","ngForOf"],[1,"prompt-outer-ui"],[1,"prompt-column-align"],[1,"prompt-ui"],[1,"prompt-text"],["imgWidth","32px","imgHeight","32px",3,"oid"],[4,"ngIf"],["class","compiling-ui",4,"ngIf"],[3,"moduleListData","customizePromptConfig","mode","currentChatData","aiIconShort","isPromptApiInProgress","generateResponseEmitter"],[3,"item","aiIconShort","aid","oid","isThreadLoading","hideRegenerate","isPromptApiInProgress","regenerateResponseEmitter"],[1,"compiling-ui"],[1,"ai-icon",3,"src"],[1,"chat-loader"],["width","44px","height","44px",3,"src"],[1,"compile-text"],["width","32px","height","32px",3,"src"],[3,"item","aiIconShort","aid","oid","isThreadLoading","isPromptApiInProgress","responseError","errorCode","responseText","regenerateResponseEmitter"]],template:function(t,e){1&t&&(h["\u0275\u0275elementStart"](0,"div",0),h["\u0275\u0275template"](1,At,9,4,"ng-container",1),h["\u0275\u0275elementEnd"]()),2&t&&(h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngForOf",e.currentThreadData))},directives:[_.NgForOf,S.a,_.NgIf,ut,Lt],styles:[".chat-ui-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:24px}.compiling-ui[_ngcontent-%COMP%]{display:flex;gap:8px}.compiling-ui[_ngcontent-%COMP%]   .ai-icon[_ngcontent-%COMP%]{width:32px;height:32px}.compiling-ui[_ngcontent-%COMP%]   .chat-loader[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;width:-moz-fit-content;width:fit-content;gap:8px;height:44px;border:.5px solid #e8e9ee;background-color:#f6f6f6;padding:0 10px;border-radius:0 8px 8px 8px}.compiling-ui[_ngcontent-%COMP%]   .chat-loader[_ngcontent-%COMP%]   .compile-text[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-size:12px;font-weight:400;color:#515965}.prompt-outer-ui[_ngcontent-%COMP%]{display:flex;justify-content:end}.prompt-outer-ui[_ngcontent-%COMP%]   .prompt-column-align[_ngcontent-%COMP%]{display:flex;align-items:end;flex-direction:column;gap:4px;width:100%}.prompt-outer-ui[_ngcontent-%COMP%]   .prompt-column-align[_ngcontent-%COMP%]   .prompt-ui[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:end;gap:8px;width:85%}.prompt-outer-ui[_ngcontent-%COMP%]   .prompt-column-align[_ngcontent-%COMP%]   .prompt-ui[_ngcontent-%COMP%]   .prompt-text[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-size:12px;font-weight:400;color:#45546e;border-radius:8px;padding:10px;max-width:100%;background-color:#f6f6f6;text-align:justify;word-wrap:break-word;border:1px solid #e8e9ee}.prompt-outer-ui[_ngcontent-%COMP%]   .prompt-column-align[_ngcontent-%COMP%]   .error-msg[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px;font-family:var(--kebsFontFamily);font-size:12px;font-weight:400;color:#45546e}.prompt-outer-ui[_ngcontent-%COMP%]   .prompt-column-align[_ngcontent-%COMP%]   .error-msg[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{font-size:16px;width:16px;height:16px;color:#ee4961}.no-internet-class[_ngcontent-%COMP%]{display:flex;width:100%;align-items:center}.no-internet-class[_ngcontent-%COMP%]   .no-internet-section[_ngcontent-%COMP%]{border-radius:8px;border:1.5px solid #f6f6f6;width:384px;display:flex;flex-direction:column;row-gap:4px}.no-internet-class[_ngcontent-%COMP%]   .no-internet-section[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%], .no-internet-class[_ngcontent-%COMP%]   .no-internet-section[_ngcontent-%COMP%]   .sub-text[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-weight:700;font-size:14px;color:#45546e}"]}),t})();var jt=n("UXJo"),Zt=n("cZdB");const Gt=["chatScrollContainer"],Nt=["triggerModulesOverlayChange"],Ut=["cardWrapper"];function $t(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",43),h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275elementStart"](1,"svg",44),h["\u0275\u0275listener"]("click",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](2).onEnterSearchHistory()})),h["\u0275\u0275elementStart"](2,"mask",45),h["\u0275\u0275element"](3,"rect",46),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](4,"g",47),h["\u0275\u0275element"](5,"path",48),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}}function Wt(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"mat-icon",49),h["\u0275\u0275listener"]("click",(function(){h["\u0275\u0275restoreView"](t);const e=h["\u0275\u0275nextContext"](2);return e.historySearchParams="",e.onEnterSearchHistory()})),h["\u0275\u0275text"](1," close "),h["\u0275\u0275elementEnd"]()}}function Yt(t,e){1&t&&h["\u0275\u0275element"](0,"div",52)}function Xt(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",50),h["\u0275\u0275template"](1,Yt,1,0,"div",51),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"](2);h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngForOf",t.repeatArray)}}function Qt(t,e){1&t&&(h["\u0275\u0275elementStart"](0,"div",53),h["\u0275\u0275text"](1," -No History- "),h["\u0275\u0275elementEnd"]())}const qt=function(t){return{"pointer-events":t}};function Kt(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",67),h["\u0275\u0275listener"]("click",(function(){h["\u0275\u0275restoreView"](t);const e=h["\u0275\u0275nextContext"]().$implicit;return h["\u0275\u0275nextContext"](4).setSelectedHistoryThread(null==e?null:e._id)})),h["\u0275\u0275text"](1),h["\u0275\u0275elementEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"]().$implicit,e=h["\u0275\u0275nextContext"](4);h["\u0275\u0275property"]("matTooltip",null==t?null:t.short_description)("ngStyle",h["\u0275\u0275pureFunction1"](3,qt,e.isThreadLoading||e.isPromptApiInProgress?"none":"")),h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate1"](" ",null==t?null:t.short_description," ")}}function Jt(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",68),h["\u0275\u0275elementStart"](1,"input",69),h["\u0275\u0275listener"]("ngModelChange",(function(e){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"]().$implicit.short_description=e}))("keydown.enter",(function(){h["\u0275\u0275restoreView"](t);const e=h["\u0275\u0275nextContext"]().$implicit;return h["\u0275\u0275nextContext"](4).onRenameThread(null==e?null:e._id,null==e?null:e.short_description)})),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"](),e=t.index,n=t.$implicit;h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("id","#pinnedHistoryInputSearch-"+e)("ngModel",n.short_description)}}function te(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",70),h["\u0275\u0275listener"]("click",(function(){h["\u0275\u0275restoreView"](t);const e=h["\u0275\u0275nextContext"](),n=e.$implicit,o=e.index;return h["\u0275\u0275nextContext"](4).setSelectedHistoryThreadData(n,-1,o)})),h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275elementStart"](1,"svg",71),h["\u0275\u0275elementStart"](2,"mask",72),h["\u0275\u0275element"](3,"rect",73),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](4,"g",74),h["\u0275\u0275element"](5,"path",75),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}if(2&t){h["\u0275\u0275nextContext"](5);const t=h["\u0275\u0275reference"](29);h["\u0275\u0275property"]("matMenuTriggerFor",t)}}function ee(t,e){if(1&t&&(h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275namespaceHTML"](),h["\u0275\u0275elementStart"](0,"div",63),h["\u0275\u0275template"](1,Kt,2,5,"div",64),h["\u0275\u0275template"](2,Jt,2,2,"div",65),h["\u0275\u0275template"](3,te,6,1,"div",66),h["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit,n=h["\u0275\u0275nextContext"](4);h["\u0275\u0275property"]("ngClass",(null==t?null:t._id)==n.historySelectedThreadId?"thread-selected":""),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!(null!=t&&t.isRenameInProgress)),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",null==t?null:t.isRenameInProgress),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!(null!=t&&t.isRenameInProgress))}}function ne(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",58),h["\u0275\u0275elementStart"](1,"div",59),h["\u0275\u0275text"](2," Pinned "),h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275elementStart"](3,"svg",60),h["\u0275\u0275element"](4,"path",61),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](5,ee,4,4,"div",62),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"](3);h["\u0275\u0275advance"](5),h["\u0275\u0275property"]("ngForOf",t.pinnedHistoryData)}}function oe(t,e){1&t&&h["\u0275\u0275element"](0,"mat-divider",76)}function ie(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",67),h["\u0275\u0275listener"]("click",(function(){h["\u0275\u0275restoreView"](t);const e=h["\u0275\u0275nextContext"]().$implicit;return h["\u0275\u0275nextContext"](4).setSelectedHistoryThread(null==e?null:e._id)})),h["\u0275\u0275text"](1),h["\u0275\u0275elementEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"]().$implicit,e=h["\u0275\u0275nextContext"](4);h["\u0275\u0275property"]("matTooltip",null==t?null:t.short_description)("ngStyle",h["\u0275\u0275pureFunction1"](3,qt,e.isThreadLoading||e.isPromptApiInProgress?"none":"")),h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate1"](" ",null==t?null:t.short_description," ")}}function re(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",68),h["\u0275\u0275elementStart"](1,"input",69),h["\u0275\u0275listener"]("ngModelChange",(function(e){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"]().$implicit.short_description=e}))("keydown.enter",(function(){h["\u0275\u0275restoreView"](t);const e=h["\u0275\u0275nextContext"]().$implicit;return h["\u0275\u0275nextContext"](4).onRenameThread(null==e?null:e._id,null==e?null:e.short_description)})),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"](),e=t.index,n=t.$implicit,o=h["\u0275\u0275nextContext"]().index;h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("id","#historyInputSearch-"+o+"-"+e)("ngModel",n.short_description)}}function ae(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",70),h["\u0275\u0275listener"]("click",(function(){h["\u0275\u0275restoreView"](t);const e=h["\u0275\u0275nextContext"](),n=e.$implicit,o=e.index,i=h["\u0275\u0275nextContext"]().index;return h["\u0275\u0275nextContext"](3).setSelectedHistoryThreadData(n,i,o)})),h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275elementStart"](1,"svg",71),h["\u0275\u0275elementStart"](2,"mask",72),h["\u0275\u0275element"](3,"rect",73),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](4,"g",74),h["\u0275\u0275element"](5,"path",75),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}if(2&t){h["\u0275\u0275nextContext"](5);const t=h["\u0275\u0275reference"](29);h["\u0275\u0275property"]("matMenuTriggerFor",t)}}function ce(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",63),h["\u0275\u0275template"](1,ie,2,5,"div",64),h["\u0275\u0275template"](2,re,2,2,"div",65),h["\u0275\u0275template"](3,ae,6,1,"div",66),h["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit,n=h["\u0275\u0275nextContext"](4);h["\u0275\u0275property"]("ngClass",(null==t?null:t._id)==n.historySelectedThreadId?"thread-selected":""),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!(null!=t&&t.isRenameInProgress)),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",null==t?null:t.isRenameInProgress),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!(null!=t&&t.isRenameInProgress))}}function se(t,e){1&t&&h["\u0275\u0275element"](0,"mat-divider",76)}function le(t,e){if(1&t&&(h["\u0275\u0275elementContainerStart"](0),h["\u0275\u0275elementStart"](1,"div",58),h["\u0275\u0275elementStart"](2,"div",59),h["\u0275\u0275text"](3),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](4,ce,4,4,"div",62),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](5,se,1,0,"mat-divider",56),h["\u0275\u0275elementContainerEnd"]()),2&t){const t=e.$implicit,n=e.index,o=h["\u0275\u0275nextContext"](3);h["\u0275\u0275advance"](3),h["\u0275\u0275textInterpolate1"](" ",t," "),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngForOf",o.historyGroupedData[t]),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",o.historyGroupedDateData.length-1!=n)}}function de(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",54),h["\u0275\u0275listener"]("scrolled",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](2).onHistoryDataScroll()})),h["\u0275\u0275template"](1,ne,6,1,"div",55),h["\u0275\u0275template"](2,oe,1,0,"mat-divider",56),h["\u0275\u0275template"](3,le,6,3,"ng-container",57),h["\u0275\u0275elementEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"](2);h["\u0275\u0275property"]("infiniteScrollDistance",3)("infiniteScrollThrottle",100)("scrollWindow",!1),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",t.pinnedHistoryData.length),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",t.pinnedHistoryData.length),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngForOf",t.historyGroupedDateData)}}function pe(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",29),h["\u0275\u0275elementStart"](1,"div",30),h["\u0275\u0275elementStart"](2,"div",31),h["\u0275\u0275text"](3,"History"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](4,"div",32),h["\u0275\u0275listener"]("click",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"]().closeHistory()})),h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275elementStart"](5,"svg",33),h["\u0275\u0275element"](6,"path",34),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275namespaceHTML"](),h["\u0275\u0275elementStart"](7,"div",35),h["\u0275\u0275elementStart"](8,"div",36),h["\u0275\u0275elementStart"](9,"input",37),h["\u0275\u0275listener"]("ngModelChange",(function(e){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"]().historySearchParams=e}))("keydown.enter",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"]().onEnterSearchHistory()})),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](10,$t,6,0,"div",38),h["\u0275\u0275template"](11,Wt,2,0,"mat-icon",39),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](12,Xt,2,1,"div",40),h["\u0275\u0275template"](13,Qt,2,0,"div",41),h["\u0275\u0275template"](14,de,4,6,"div",42),h["\u0275\u0275elementEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"]();h["\u0275\u0275advance"](9),h["\u0275\u0275property"]("ngModel",t.historySearchParams),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!t.historySearchParams),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",t.historySearchParams),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",t.isHistoryLoading),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!(t.historyGroupedDateData&&0!=t.historyGroupedDateData.length||t.pinnedHistoryData&&0!=t.pinnedHistoryData.length||t.isHistoryLoading)),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",(t.historyGroupedDateData&&t.historyGroupedDateData.length>0||t.pinnedHistoryData&&t.pinnedHistoryData.length>0)&&!t.isHistoryLoading)}}function ge(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",77),h["\u0275\u0275listener"]("click",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"]().openHistory()})),h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275elementStart"](1,"svg",13),h["\u0275\u0275elementStart"](2,"mask",78),h["\u0275\u0275element"](3,"rect",15),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](4,"g",79),h["\u0275\u0275element"](5,"path",80),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}}function he(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",32),h["\u0275\u0275listener"]("click",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"]().closeHistory()})),h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275elementStart"](1,"svg",13),h["\u0275\u0275element"](2,"rect",81),h["\u0275\u0275elementStart"](3,"g",82),h["\u0275\u0275element"](4,"rect",83),h["\u0275\u0275elementStart"](5,"g",84),h["\u0275\u0275element"](6,"rect",85),h["\u0275\u0275element"](7,"g",86),h["\u0275\u0275elementEnd"](),h["\u0275\u0275element"](8,"rect",87),h["\u0275\u0275elementStart"](9,"g",88),h["\u0275\u0275elementStart"](10,"g",89),h["\u0275\u0275element"](11,"rect",90),h["\u0275\u0275element"](12,"rect",91),h["\u0275\u0275element"](13,"g",92),h["\u0275\u0275elementStart"](14,"mask",93),h["\u0275\u0275element"](15,"path",94),h["\u0275\u0275elementEnd"](),h["\u0275\u0275element"](16,"path",95),h["\u0275\u0275elementStart"](17,"mask",96),h["\u0275\u0275element"](18,"path",97),h["\u0275\u0275elementEnd"](),h["\u0275\u0275element"](19,"path",98),h["\u0275\u0275elementEnd"](),h["\u0275\u0275element"](20,"rect",99),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](21,"defs"),h["\u0275\u0275elementStart"](22,"filter",100),h["\u0275\u0275element"](23,"feFlood",101),h["\u0275\u0275element"](24,"feColorMatrix",102),h["\u0275\u0275element"](25,"feMorphology",103),h["\u0275\u0275element"](26,"feOffset",104),h["\u0275\u0275element"](27,"feGaussianBlur",105),h["\u0275\u0275element"](28,"feComposite",106),h["\u0275\u0275element"](29,"feColorMatrix",107),h["\u0275\u0275element"](30,"feBlend",108),h["\u0275\u0275element"](31,"feColorMatrix",102),h["\u0275\u0275element"](32,"feOffset",109),h["\u0275\u0275element"](33,"feGaussianBlur",110),h["\u0275\u0275element"](34,"feComposite",106),h["\u0275\u0275element"](35,"feColorMatrix",111),h["\u0275\u0275element"](36,"feBlend",112),h["\u0275\u0275element"](37,"feColorMatrix",102),h["\u0275\u0275element"](38,"feOffset",113),h["\u0275\u0275element"](39,"feGaussianBlur",114),h["\u0275\u0275element"](40,"feComposite",106),h["\u0275\u0275element"](41,"feColorMatrix",115),h["\u0275\u0275element"](42,"feBlend",116),h["\u0275\u0275element"](43,"feBlend",117),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](44,"linearGradient",118),h["\u0275\u0275element"](45,"stop",119),h["\u0275\u0275element"](46,"stop",120),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](47,"clipPath",121),h["\u0275\u0275element"](48,"rect",122),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](49,"clipPath",123),h["\u0275\u0275element"](50,"rect",124),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](51,"clipPath",125),h["\u0275\u0275element"](52,"rect",90),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}}function me(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275elementStart"](0,"svg",129),h["\u0275\u0275listener"]("click",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](2).openPromptLibrary()})),h["\u0275\u0275element"](1,"path",130),h["\u0275\u0275elementStart"](2,"defs"),h["\u0275\u0275elementStart"](3,"linearGradient",131),h["\u0275\u0275element"](4,"stop",132),h["\u0275\u0275element"](5,"stop",133),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}}function ue(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275elementStart"](0,"svg",134),h["\u0275\u0275listener"]("click",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](2).openNewChat()})),h["\u0275\u0275elementStart"](1,"mask",135),h["\u0275\u0275element"](2,"rect",136),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](3,"g",137),h["\u0275\u0275element"](4,"path",138),h["\u0275\u0275elementEnd"](),h["\u0275\u0275element"](5,"line",139),h["\u0275\u0275elementStart"](6,"defs"),h["\u0275\u0275elementStart"](7,"linearGradient",140),h["\u0275\u0275element"](8,"stop",132),h["\u0275\u0275element"](9,"stop",133),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](10,"linearGradient",141),h["\u0275\u0275element"](11,"stop",132),h["\u0275\u0275element"](12,"stop",133),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}}function Ce(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",126),h["\u0275\u0275template"](1,me,6,0,"svg",127),h["\u0275\u0275template"](2,ue,13,0,"svg",128),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"]();h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!t.isPromptLibraryVisible),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",t.isPromptLibraryVisible)}}function fe(t,e){1&t&&(h["\u0275\u0275elementStart"](0,"div",142),h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275elementStart"](1,"svg",143),h["\u0275\u0275element"](2,"path",144),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]())}function xe(t,e){1&t&&(h["\u0275\u0275elementStart"](0,"div",145),h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275elementStart"](1,"svg",143),h["\u0275\u0275element"](2,"path",146),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]())}function _e(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",150),h["\u0275\u0275elementStart"](1,"div",151),h["\u0275\u0275elementStart"](2,"div",152),h["\u0275\u0275text"](3),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](4,"div",153),h["\u0275\u0275text"](5),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"](2);h["\u0275\u0275advance"](1),h["\u0275\u0275classProp"]("slide",t.isSliding),h["\u0275\u0275advance"](2),h["\u0275\u0275textInterpolate1"](" ",t.threadTexts[t.currentThreadLoadTextIndex].text," "),h["\u0275\u0275advance"](2),h["\u0275\u0275textInterpolate1"](" ",t.threadTexts[t.currentThreadLoadTextIndex].sub_text," ")}}function ve(t,e){if(1&t&&(h["\u0275\u0275elementContainerStart"](0),h["\u0275\u0275elementStart"](1,"div",147),h["\u0275\u0275element"](2,"img",148),h["\u0275\u0275template"](3,_e,6,4,"div",149),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementContainerEnd"]()),2&t){const t=h["\u0275\u0275nextContext"]();h["\u0275\u0275advance"](2),h["\u0275\u0275property"]("src",null==t.data?null:t.data.aiThemeConfig["AI-MAIN-LOADER"],h["\u0275\u0275sanitizeUrl"]),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!t.isLoading)}}function ye(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",164),h["\u0275\u0275text"](1),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"](3);h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate1"](" ",null==t.data?null:t.data.aiThemeConfig["WELCOME-TEXT-007"]," ")}}function Pe(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",169),h["\u0275\u0275listener"]("click",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](4).scroll(-1)})),h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275elementStart"](1,"svg",170),h["\u0275\u0275element"](2,"path",171),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}}function Me(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",175),h["\u0275\u0275listener"]("click",(function(){h["\u0275\u0275restoreView"](t);const n=e.$implicit,o=h["\u0275\u0275nextContext"](5);return o.resetUiState("isChatVisible"),o.setVisibility(null==n?null:n.visibility)})),h["\u0275\u0275elementStart"](1,"div",176),h["\u0275\u0275element"](2,"div",177),h["\u0275\u0275pipe"](3,"safeHtml"),h["\u0275\u0275elementStart"](4,"div",178),h["\u0275\u0275elementStart"](5,"div",179),h["\u0275\u0275text"](6),h["\u0275\u0275elementEnd"](),h["\u0275\u0275text"](7),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](8,"span"),h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275elementStart"](9,"svg",180),h["\u0275\u0275element"](10,"path",181),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}if(2&t){const t=e.$implicit;h["\u0275\u0275advance"](2),h["\u0275\u0275property"]("innerHtml",h["\u0275\u0275pipeBind1"](3,3,null==t?null:t.svg),h["\u0275\u0275sanitizeHtml"]),h["\u0275\u0275advance"](4),h["\u0275\u0275textInterpolate1"](" ",null==t?null:t.header," "),h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate1"](" ",null==t?null:t.sub_text," ")}}function Oe(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",172,173),h["\u0275\u0275template"](2,Me,11,5,"div",174),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"](4);h["\u0275\u0275advance"](2),h["\u0275\u0275property"]("ngForOf",null==t.data?null:t.data.aiThemeConfig["FEATURE-LIST"])}}function be(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",182),h["\u0275\u0275listener"]("click",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](4).scroll(1)})),h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275elementStart"](1,"svg",170),h["\u0275\u0275element"](2,"path",183),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}}function Se(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",165),h["\u0275\u0275template"](1,Pe,3,0,"div",166),h["\u0275\u0275template"](2,Oe,3,1,"div",167),h["\u0275\u0275template"](3,be,3,0,"div",168),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"](3);h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",t.canScrollLeft),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",(null==t.data?null:t.data.aiThemeConfig["FEATURE-LIST"])&&(null==t.data?null:t.data.aiThemeConfig["FEATURE-LIST"].length)>0),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",t.canScrollRight)}}function we(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",192),h["\u0275\u0275listener"]("click",(function(){h["\u0275\u0275restoreView"](t);const n=e.$implicit;return h["\u0275\u0275nextContext"](4).onEnterSearchPrompt(null==n?null:n._id,null==n?null:n.prompt,!1,null,null,null)})),h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275elementStart"](1,"svg",193),h["\u0275\u0275elementStart"](2,"mask",194),h["\u0275\u0275element"](3,"rect",195),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](4,"g",196),h["\u0275\u0275element"](5,"path",197),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275namespaceHTML"](),h["\u0275\u0275element"](6,"div",198),h["\u0275\u0275elementEnd"]()}if(2&t){const t=e.$implicit,n=h["\u0275\u0275nextContext"](4);h["\u0275\u0275property"]("ngStyle",h["\u0275\u0275pureFunction1"](2,qt,n.isThreadLoading||n.isPromptApiInProgress?"none":"")),h["\u0275\u0275advance"](6),h["\u0275\u0275property"]("innerHTML",n.highlightPromptKey(t.prompt),h["\u0275\u0275sanitizeHtml"])}}function Ee(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",184),h["\u0275\u0275elementStart"](1,"div",185),h["\u0275\u0275elementStart"](2,"div",164),h["\u0275\u0275text"](3),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](4,"div",186,187),h["\u0275\u0275listener"]("click",(function(){h["\u0275\u0275restoreView"](t);const e=h["\u0275\u0275reference"](6),n=h["\u0275\u0275nextContext"](3),o=h["\u0275\u0275reference"](32);return n.openModulesOverlay(e,o)})),h["\u0275\u0275text"](7),h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275elementStart"](8,"svg",188),h["\u0275\u0275element"](9,"path",189),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275namespaceHTML"](),h["\u0275\u0275elementStart"](10,"div",190),h["\u0275\u0275template"](11,we,7,4,"div",191),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"](3);h["\u0275\u0275advance"](3),h["\u0275\u0275textInterpolate1"](" ",null==t.data?null:t.data.aiThemeConfig["WELCOME-TEXT-003"]," "),h["\u0275\u0275advance"](4),h["\u0275\u0275textInterpolate1"](" ",t.currentSelectedModuleName," "),h["\u0275\u0275advance"](4),h["\u0275\u0275property"]("ngForOf",t.suggestedPrompts)}}function ke(t,e){if(1&t&&(h["\u0275\u0275elementContainerStart"](0),h["\u0275\u0275elementStart"](1,"div",156),h["\u0275\u0275element"](2,"img",157),h["\u0275\u0275elementStart"](3,"div",158),h["\u0275\u0275elementStart"](4,"div",159),h["\u0275\u0275text"](5),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](6,"div",160),h["\u0275\u0275text"](7),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](8,ye,2,1,"div",161),h["\u0275\u0275template"](9,Se,4,3,"div",162),h["\u0275\u0275template"](10,Ee,12,3,"div",163),h["\u0275\u0275elementContainerEnd"]()),2&t){const t=h["\u0275\u0275nextContext"](2);h["\u0275\u0275advance"](2),h["\u0275\u0275property"]("src",null==t.data?null:t.data.aiThemeConfig["AI-ICON-SHORT"],h["\u0275\u0275sanitizeUrl"]),h["\u0275\u0275advance"](2),h["\u0275\u0275property"]("ngClass",t.dialogExpanded?"header-content":""),h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate1"](" ",t.getGreetingWithName(null==t.data?null:t.data.aiThemeConfig["WELCOME-TEXT-001"],null==t.profile?null:t.profile.name)," "),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngClass",t.dialogExpanded?"header-content":""),h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate1"](" ",null==t.data?null:t.data.aiThemeConfig["WELCOME-TEXT-002"]," "),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",(null==t.data?null:t.data.aiThemeConfig["FEATURE-LIST"])&&(null==t.data?null:t.data.aiThemeConfig["FEATURE-LIST"].length)>0),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",(null==t.data?null:t.data.aiThemeConfig["FEATURE-LIST"])&&(null==t.data?null:t.data.aiThemeConfig["FEATURE-LIST"].length)>0),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",t.modules&&t.modules.length>0)}}function Le(t,e){if(1&t&&(h["\u0275\u0275elementContainerStart"](0),h["\u0275\u0275elementStart"](1,"div",200),h["\u0275\u0275elementStart"](2,"div",201),h["\u0275\u0275elementStart"](3,"span",202),h["\u0275\u0275text"](4),h["\u0275\u0275elementEnd"](),h["\u0275\u0275text"](5,"\ud83d\udca1 "),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementContainerEnd"]()),2&t){const t=h["\u0275\u0275nextContext"](3);h["\u0275\u0275advance"](2),h["\u0275\u0275property"]("ngClass",t.dialogExpanded?"header-content":""),h["\u0275\u0275advance"](2),h["\u0275\u0275textInterpolate"](t.getFeatureValue("chat_header")?t.getFeatureValue("chat_header"):"")}}function Ie(t,e){if(1&t&&(h["\u0275\u0275elementContainerStart"](0),h["\u0275\u0275elementStart"](1,"div",200),h["\u0275\u0275elementStart"](2,"div",201),h["\u0275\u0275elementStart"](3,"span",202),h["\u0275\u0275text"](4),h["\u0275\u0275elementEnd"](),h["\u0275\u0275text"](5,"\ud83d\udca1 "),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](6,"div",203),h["\u0275\u0275text"](7),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementContainerEnd"]()),2&t){const t=h["\u0275\u0275nextContext"](3);h["\u0275\u0275advance"](2),h["\u0275\u0275property"]("ngClass",t.dialogExpanded?"header-content":""),h["\u0275\u0275advance"](2),h["\u0275\u0275textInterpolate"](t.getFeatureValue("chat_header")?t.getFeatureValue("chat_header"):""),h["\u0275\u0275advance"](2),h["\u0275\u0275property"]("ngClass",t.dialogExpanded?"header-content":""),h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate1"](" ",t.getFeatureValue("sub_text")?t.getFeatureValue("sub_text"):""," ")}}function De(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",204),h["\u0275\u0275elementStart"](1,"app-chat-ui",205),h["\u0275\u0275listener"]("regenerateResponseEmitter",(function(e){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](3).regenerateResponse(e)})),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"](3);h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("aid",t.profile.aid)("oid",t.profile.oid)("chatLoader",null==t.data?null:t.data.aiThemeConfig["CHATBOT-CHAT-LOADER"])("aiIconShort",null==t.data?null:t.data.aiThemeConfig["AI-ICON-SHORT"])("currentThreadData",t.currentThreadData)("isThreadLoading",t.isThreadLoading)("isPromptApiInProgress",t.isPromptApiInProgress)("chatResponseText",null==t.data?null:t.data.aiThemeConfig["RESPONSE-TEXT"])("moduleList",t.modules)("customizePromptConfig",t.customizePromptData)}}function Ve(t,e){if(1&t&&(h["\u0275\u0275elementContainerStart"](0),h["\u0275\u0275template"](1,Le,6,2,"ng-container",19),h["\u0275\u0275template"](2,Ie,8,4,"ng-container",19),h["\u0275\u0275template"](3,De,2,10,"div",199),h["\u0275\u0275elementContainerEnd"]()),2&t){const t=h["\u0275\u0275nextContext"](2);h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",t.isAgentVisible),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",t.isReportsVisible||t.isWidgetsVisible),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",t.isChatVisible)}}const Te=function(t){return{height:t}};function He(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",154,155),h["\u0275\u0275template"](2,ke,11,8,"ng-container",19),h["\u0275\u0275template"](3,Ve,4,3,"ng-container",19),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"]();h["\u0275\u0275property"]("ngStyle",h["\u0275\u0275pureFunction1"](3,Te,t.isChatVisible?"calc(var(--kebsChatbotContentHeight) + 26px)":"calc(var(--kebsChatbotContentHeight) + 28px)")),h["\u0275\u0275advance"](2),h["\u0275\u0275property"]("ngIf",t.isHomeScreenVisible),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",t.isChatVisible||t.isReportsVisible||t.isWidgetsVisible)}}function Fe(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275elementStart"](0,"svg",229),h["\u0275\u0275listener"]("click",(function(){h["\u0275\u0275restoreView"](t);const e=h["\u0275\u0275nextContext"](2);return e.promptLibrarySearchParams="",e.onSearchParamsChange()})),h["\u0275\u0275elementStart"](1,"mask",14),h["\u0275\u0275element"](2,"rect",15),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](3,"g",16),h["\u0275\u0275element"](4,"path",230),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}}const Re=function(t){return{"selected-category-list":t}},ze=function(t){return{"selected-count":t}};function Ae(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementContainerStart"](0),h["\u0275\u0275elementStart"](1,"div",231),h["\u0275\u0275listener"]("click",(function(){h["\u0275\u0275restoreView"](t);const n=e.index,o=e.$implicit,i=h["\u0275\u0275nextContext"](2);return i.setPromptLibraryIndex(n),i.setPromptLibraryCategory(o.name)})),h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275elementStart"](2,"svg",232),h["\u0275\u0275element"](3,"path",233),h["\u0275\u0275elementEnd"](),h["\u0275\u0275namespaceHTML"](),h["\u0275\u0275elementStart"](4,"div",234),h["\u0275\u0275text"](5),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](6,"div",235),h["\u0275\u0275text"](7),h["\u0275\u0275pipe"](8,"filterPromptLibrary"),h["\u0275\u0275pipe"](9,"filterPromptLibrary"),h["\u0275\u0275pipe"](10,"filterPromptLibrary"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementContainerEnd"]()}if(2&t){const t=e.$implicit,n=e.index,o=h["\u0275\u0275nextContext"](2);h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngClass",h["\u0275\u0275pureFunction1"](14,Re,o.currentSelectedPromptLibraryCategory==t.name)),h["\u0275\u0275advance"](3),h["\u0275\u0275property"]("matTooltip",t.name),h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate1"](" ",t.name," "),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngClass",h["\u0275\u0275pureFunction1"](16,ze,o.currentSelectedPromptLibraryCategory==t.name)),h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate1"](" ",h["\u0275\u0275pipeBind2"](8,5,o.promptLibraryData,o.promptLibrarySearchParams)[n].prompts.length>9?h["\u0275\u0275pipeBind2"](9,8,o.promptLibraryData,o.promptLibrarySearchParams)[n].prompts.length:"0"+h["\u0275\u0275pipeBind2"](10,11,o.promptLibraryData,o.promptLibrarySearchParams)[n].prompts.length," ")}}function Be(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",238),h["\u0275\u0275listener"]("click",(function(){h["\u0275\u0275restoreView"](t);const n=e.$implicit;return h["\u0275\u0275nextContext"](3).copyPromptToClipboard(n)})),h["\u0275\u0275element"](1,"div",239),h["\u0275\u0275elementStart"](2,"div",240),h["\u0275\u0275element"](3,"img",241),h["\u0275\u0275text"](4),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}if(2&t){const t=e.$implicit,n=h["\u0275\u0275nextContext"](3);h["\u0275\u0275property"]("cdkCopyToClipboard",(null==t?null:t.prompt)||""),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("innerHTML",n.highlightPromptKey(t.prompt),h["\u0275\u0275sanitizeHtml"]),h["\u0275\u0275advance"](2),h["\u0275\u0275property"]("src","https://assets.kebs.app/document-copy.png",h["\u0275\u0275sanitizeUrl"]),h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate1"](" ",null!=t&&t.isCopyInProgress?"Copied!":"Copy"," ")}}function je(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",236),h["\u0275\u0275template"](1,Be,5,4,"div",237),h["\u0275\u0275pipe"](2,"filterPromptLibrary"),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"](2);h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngForOf",h["\u0275\u0275pipeBind2"](2,1,t.promptLibraryData,t.promptLibrarySearchParams)[t.currentSelectedPromptLibraryCategoryIndex].prompts)}}function Ze(t,e){1&t&&(h["\u0275\u0275elementStart"](0,"div",242),h["\u0275\u0275text"](1," No Categories Found! "),h["\u0275\u0275elementEnd"]())}function Ge(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",206),h["\u0275\u0275elementStart"](1,"div",30),h["\u0275\u0275elementStart"](2,"div",207),h["\u0275\u0275elementStart"](3,"mat-icon",208),h["\u0275\u0275listener"]("click",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"]().openNewChat()})),h["\u0275\u0275text"](4,"arrow_back"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](5,"div",209),h["\u0275\u0275text"](6,"AI Prompt Library"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](7,"div",36),h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275elementStart"](8,"svg",210),h["\u0275\u0275elementStart"](9,"g",211),h["\u0275\u0275element"](10,"path",212),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](11,"defs"),h["\u0275\u0275elementStart"](12,"clipPath",213),h["\u0275\u0275element"](13,"rect",214),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275namespaceHTML"](),h["\u0275\u0275elementStart"](14,"input",215),h["\u0275\u0275listener"]("ngModelChange",(function(e){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"]().promptLibrarySearchParams=e}))("ngModelChange",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"]().onSearchParamsChange()})),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](15,Fe,5,0,"svg",216),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](16,"div",217),h["\u0275\u0275elementStart"](17,"div",218),h["\u0275\u0275elementStart"](18,"div",30),h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275elementStart"](19,"svg",219),h["\u0275\u0275element"](20,"path",220),h["\u0275\u0275element"](21,"path",221),h["\u0275\u0275element"](22,"path",222),h["\u0275\u0275element"](23,"path",223),h["\u0275\u0275elementEnd"](),h["\u0275\u0275namespaceHTML"](),h["\u0275\u0275elementStart"](24,"div",224),h["\u0275\u0275text"](25,"Categories"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](26,"div",225),h["\u0275\u0275text"](27),h["\u0275\u0275pipe"](28,"filterPromptLibrary"),h["\u0275\u0275pipe"](29,"filterPromptLibrary"),h["\u0275\u0275pipe"](30,"filterPromptLibrary"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](31,"div",226),h["\u0275\u0275template"](32,Ae,11,18,"ng-container",57),h["\u0275\u0275pipe"](33,"filterPromptLibrary"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](34,je,3,4,"div",227),h["\u0275\u0275pipe"](35,"filterPromptLibrary"),h["\u0275\u0275template"](36,Ze,2,0,"div",228),h["\u0275\u0275pipe"](37,"filterPromptLibrary"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"]();h["\u0275\u0275advance"](14),h["\u0275\u0275property"]("ngModel",t.promptLibrarySearchParams),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",t.promptLibrarySearchParams&&t.promptLibrarySearchParams.length>0),h["\u0275\u0275advance"](12),h["\u0275\u0275textInterpolate1"](" ",h["\u0275\u0275pipeBind2"](28,6,t.promptLibraryData,t.promptLibrarySearchParams).length>9?h["\u0275\u0275pipeBind2"](29,9,t.promptLibraryData,t.promptLibrarySearchParams).length:"0"+h["\u0275\u0275pipeBind2"](30,12,t.promptLibraryData,t.promptLibrarySearchParams).length," "),h["\u0275\u0275advance"](5),h["\u0275\u0275property"]("ngForOf",h["\u0275\u0275pipeBind2"](33,15,t.promptLibraryData,t.promptLibrarySearchParams)),h["\u0275\u0275advance"](2),h["\u0275\u0275property"]("ngIf",h["\u0275\u0275pipeBind2"](35,18,t.promptLibraryData,t.promptLibrarySearchParams).length>0),h["\u0275\u0275advance"](2),h["\u0275\u0275property"]("ngIf",0==h["\u0275\u0275pipeBind2"](37,21,t.promptLibraryData,t.promptLibrarySearchParams).length)}}function Ne(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",251),h["\u0275\u0275listener"]("click",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](2).openNewChat()})),h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275elementStart"](1,"svg",252),h["\u0275\u0275element"](2,"path",253),h["\u0275\u0275element"](3,"path",254),h["\u0275\u0275element"](4,"path",255),h["\u0275\u0275element"](5,"path",256),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}}function Ue(t,e){1&t&&(h["\u0275\u0275elementStart"](0,"div"),h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275elementStart"](1,"svg",252),h["\u0275\u0275element"](2,"path",257),h["\u0275\u0275element"](3,"path",258),h["\u0275\u0275element"](4,"path",259),h["\u0275\u0275element"](5,"path",260),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]())}function $e(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",265),h["\u0275\u0275listener"]("click",(function(n){h["\u0275\u0275restoreView"](t);const o=e.$implicit,i=e.index;return h["\u0275\u0275nextContext"](4).selectSearchedPrompt(n,o,i)})),h["\u0275\u0275elementStart"](1,"span"),h["\u0275\u0275text"](2),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}if(2&t){const t=e.$implicit;h["\u0275\u0275advance"](2),h["\u0275\u0275textInterpolate"](null==t?null:t.prompt)}}function We(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",263),h["\u0275\u0275listener"]("scrolled",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](3).onPromptDataScroll()})),h["\u0275\u0275template"](1,$e,3,1,"div",264),h["\u0275\u0275elementEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"](3);h["\u0275\u0275property"]("infiniteScrollDistance",3)("infiniteScrollThrottle",100)("scrollWindow",!1),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngForOf",t.searchLazyLoadedList)}}function Ye(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",261),h["\u0275\u0275listener"]("click",(function(t){return t.stopPropagation()})),h["\u0275\u0275template"](1,We,2,4,"div",262),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"](2);h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",(null==t.searchLazyLoadedList?null:t.searchLazyLoadedList.length)>0)}}function Xe(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",9),h["\u0275\u0275listener"]("click",(function(){h["\u0275\u0275restoreView"](t);const e=h["\u0275\u0275nextContext"](2);return e.onEnterSearchPrompt(null,e.promptSearchParams,!1,null,null,null)})),h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275elementStart"](1,"svg",266),h["\u0275\u0275element"](2,"rect",267),h["\u0275\u0275element"](3,"path",268),h["\u0275\u0275element"](4,"path",269),h["\u0275\u0275elementStart"](5,"defs"),h["\u0275\u0275elementStart"](6,"linearGradient",270),h["\u0275\u0275element"](7,"stop",132),h["\u0275\u0275element"](8,"stop",133),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}}function Qe(t,e){1&t&&(h["\u0275\u0275elementStart"](0,"div"),h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275elementStart"](1,"svg",266),h["\u0275\u0275element"](2,"rect",271),h["\u0275\u0275element"](3,"path",268),h["\u0275\u0275element"](4,"path",269),h["\u0275\u0275elementStart"](5,"defs"),h["\u0275\u0275elementStart"](6,"linearGradient",270),h["\u0275\u0275element"](7,"stop",132),h["\u0275\u0275element"](8,"stop",133),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]())}function qe(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",243),h["\u0275\u0275elementStart"](1,"div",4),h["\u0275\u0275template"](2,Ne,6,0,"div",244),h["\u0275\u0275template"](3,Ue,6,0,"div",19),h["\u0275\u0275elementStart"](4,"div",245),h["\u0275\u0275listener"]("mouseleave",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"]().isTyping=!1})),h["\u0275\u0275template"](5,Ye,2,1,"div",246),h["\u0275\u0275elementStart"](6,"div",35),h["\u0275\u0275elementStart"](7,"div",247),h["\u0275\u0275listener"]("mouseenter",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"]().setTypingTrue()})),h["\u0275\u0275elementStart"](8,"input",248),h["\u0275\u0275listener"]("focus",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"]().setTypingTrue()}))("blur",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"]().clearSearchInput()}))("ngModelChange",(function(e){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"]().promptSearchParams=e}))("keydown.enter",(function(){h["\u0275\u0275restoreView"](t);const e=h["\u0275\u0275nextContext"]();return e.onEnterSearchPrompt(null,e.promptSearchParams,!1,null,null,null)}))("input",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"]().setTypingTrue()})),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](9,Xe,9,0,"div",249),h["\u0275\u0275template"](10,Qe,9,0,"div",19),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](11,"div",250),h["\u0275\u0275text"](12,"Kais can make mistakes. Check important info."),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"]();h["\u0275\u0275advance"](2),h["\u0275\u0275property"]("ngIf",!t.isHomeScreenVisible&&!t.isPromptApiInProgress),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",t.isHomeScreenVisible||t.isPromptApiInProgress),h["\u0275\u0275advance"](2),h["\u0275\u0275property"]("ngIf",t.isTyping&&(null==t.searchLazyLoadedList?null:t.searchLazyLoadedList.length)>0&&!t.isThreadLoading&&!t.isLoading&&!t.isPromptApiInProgress),h["\u0275\u0275advance"](3),h["\u0275\u0275property"]("ngModel",t.promptSearchParams),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!t.isPromptApiInProgress&&t.promptSearchParams&&t.promptSearchParams.length>0),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",t.isPromptApiInProgress||!t.promptSearchParams||0==t.promptSearchParams.length)}}function Ke(t,e){1&t&&(h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275elementStart"](0,"svg",274),h["\u0275\u0275element"](1,"path",280),h["\u0275\u0275elementEnd"]())}function Je(t,e){1&t&&(h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275elementStart"](0,"svg",274),h["\u0275\u0275element"](1,"path",61),h["\u0275\u0275elementEnd"]())}function tn(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",272),h["\u0275\u0275elementStart"](1,"button",273),h["\u0275\u0275listener"]("click",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"]().renameThread()})),h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275elementStart"](2,"svg",274),h["\u0275\u0275element"](3,"path",275),h["\u0275\u0275elementEnd"](),h["\u0275\u0275text"](4," Rename "),h["\u0275\u0275elementEnd"](),h["\u0275\u0275namespaceHTML"](),h["\u0275\u0275element"](5,"mat-divider",276),h["\u0275\u0275elementStart"](6,"button",273),h["\u0275\u0275listener"]("click",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"]().archiveOrUnarchiveThread(!0)})),h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275elementStart"](7,"svg",274),h["\u0275\u0275element"](8,"path",277),h["\u0275\u0275elementEnd"](),h["\u0275\u0275text"](9," Archive "),h["\u0275\u0275elementEnd"](),h["\u0275\u0275namespaceHTML"](),h["\u0275\u0275element"](10,"mat-divider",276),h["\u0275\u0275elementStart"](11,"button",273),h["\u0275\u0275listener"]("click",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"]().deleteThread()})),h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275elementStart"](12,"svg",274),h["\u0275\u0275element"](13,"path",278),h["\u0275\u0275elementEnd"](),h["\u0275\u0275text"](14," Delete "),h["\u0275\u0275elementEnd"](),h["\u0275\u0275namespaceHTML"](),h["\u0275\u0275element"](15,"mat-divider",276),h["\u0275\u0275elementStart"](16,"button",273),h["\u0275\u0275listener"]("click",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"]().pinOrUnpinThread()})),h["\u0275\u0275template"](17,Ke,2,0,"svg",279),h["\u0275\u0275template"](18,Je,2,0,"svg",279),h["\u0275\u0275text"](19),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"]();h["\u0275\u0275advance"](17),h["\u0275\u0275property"]("ngIf",null==t.selectedHistoryThreadData?null:t.selectedHistoryThreadData.pinned),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!(null!=t.selectedHistoryThreadData&&t.selectedHistoryThreadData.pinned)),h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate1"](" ",null!=t.selectedHistoryThreadData&&t.selectedHistoryThreadData.pinned?"Unpin":"Pin"," ")}}function en(t,e){1&t&&(h["\u0275\u0275elementContainerStart"](0),h["\u0275\u0275elementStart"](1,"div",284),h["\u0275\u0275text"](2,"No Modules Found!"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementContainerEnd"]())}function nn(t,e){1&t&&h["\u0275\u0275element"](0,"mat-divider",76)}const on=function(t){return{"selected-module":t}};function rn(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementContainerStart"](0),h["\u0275\u0275elementStart"](1,"div",285),h["\u0275\u0275listener"]("click",(function(){h["\u0275\u0275restoreView"](t);const n=e.$implicit;return h["\u0275\u0275nextContext"](3).onClickModule(n.id,n.name)})),h["\u0275\u0275text"](2),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](3,nn,1,0,"mat-divider",56),h["\u0275\u0275pipe"](4,"filter"),h["\u0275\u0275elementContainerEnd"]()}if(2&t){const t=e.$implicit,n=e.index,o=h["\u0275\u0275nextContext"](3);h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngClass",h["\u0275\u0275pureFunction1"](6,on,o.currentSelectedModuleId==t.id)),h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate1"](" ",t.name," "),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",h["\u0275\u0275pipeBind2"](4,3,o.modules,o.moduleSearchParams).length-1!=n)}}function an(t,e){if(1&t&&(h["\u0275\u0275elementContainerStart"](0),h["\u0275\u0275template"](1,rn,5,8,"ng-container",57),h["\u0275\u0275pipe"](2,"filter"),h["\u0275\u0275elementContainerEnd"]()),2&t){const t=h["\u0275\u0275nextContext"](2);h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngForOf",h["\u0275\u0275pipeBind2"](2,1,t.modules,t.moduleSearchParams))}}function cn(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",281),h["\u0275\u0275elementStart"](1,"div",35),h["\u0275\u0275elementStart"](2,"input",282),h["\u0275\u0275listener"]("ngModelChange",(function(e){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"]().moduleSearchParams=e})),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](3,"div",283),h["\u0275\u0275template"](4,en,3,0,"ng-container",19),h["\u0275\u0275pipe"](5,"filter"),h["\u0275\u0275template"](6,an,3,4,"ng-container",19),h["\u0275\u0275pipe"](7,"filter"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"]();h["\u0275\u0275advance"](2),h["\u0275\u0275property"]("id","#moduleInputSearch")("ngModel",t.moduleSearchParams),h["\u0275\u0275advance"](2),h["\u0275\u0275property"]("ngIf",0==h["\u0275\u0275pipeBind2"](5,4,t.modules,t.moduleSearchParams).length),h["\u0275\u0275advance"](2),h["\u0275\u0275property"]("ngIf",h["\u0275\u0275pipeBind2"](7,7,t.modules,t.moduleSearchParams).length>0)}}let sn=(()=>{class t{constructor(t,e,n,o,i,r,a,c,s,l,p){this.data=t,this._dialogRef=e,this._aiService=n,this._loginService=o,this._toaster=i,this._viewContainerRef=r,this._overlay=a,this._stringSimilarity=c,this.sanitizer=s,this.filter=l,this.elementRef=p,this._onDestroy=new d.b,this.canScrollLeft=!1,this.canScrollRight=!1,this.profile={},this.promptSearchParams="",this.historySearchParams="",this.moduleSearchParams="",this.promptLibrarySearchParams="",this.isHistoryVisible=!1,this.isChatVisible=!1,this.isHomeScreenVisible=!0,this.isPromptLibraryVisible=!1,this.isReportsVisible=!1,this.isWidgetsVisible=!1,this.isHistoryLoading=!1,this.isThreadLoading=!1,this.isLoading=!0,this.isPromptApiInProgress=!1,this.isAgentVisible=!1,this.suggestedPrompts=[],this.modules=[],this.currentThreadData=[],this.historySkip=0,this.historyLimit=25,this.historyData=[],this.pinnedHistoryData=[],this.historyGroupedData={},this.historyGroupedDateData=[],this.currentThreadLoadTextIndex=0,this.threadTexts=[],this.isSliding=!1,this.repeatArray=[],this.currentSelectedModuleId=null,this.currentSelectedModuleName=null,this.selectedHistoryDateIndex=null,this.selectedHistoryThreadIndex=null,this.promptLibraryData=[],this.currentSelectedPromptLibraryCategoryIndex=null,this.currentSelectedPromptLibraryCategory=null,this.dialogExpanded=!1,this.historySelectedThreadId=null,this.isTyping=!1,this.searchDefaultPrompts=[],this.searchPromptSkip=0,this.searchLazyLoadedList=[],this.startIndex=0,this.skip=25,this.filteredPrompts=[],this.isCustomPrompt=!1,this.customizePromptData=null,this.responsePromptData=null,this.customResponseId="",this.responseCustomizeData=null}ngOnInit(){var t;return Object(o.c)(this,void 0,void 0,(function*(){this.profile=this._loginService.getProfile().profile,this.threadTexts=null===(t=this.data)||void 0===t?void 0:t.aiThemeConfig["THREAD-LOADER-TEXT"],this.onDialogResizing(!0),setInterval(()=>{this.currentThreadLoadTextIndex=(this.currentThreadLoadTextIndex+1)%this.threadTexts.length,this.isSliding=!0,setTimeout(()=>{this.isSliding=!1},500)},2e3),this.calculateDynamicContentHeight(),Promise.all([this.defaultPrompts(),this.getPromptLibraryData(),this.getChatbotModulesBasedOnRoleAccess()]).then(t=>Object(o.c)(this,void 0,void 0,(function*(){yield this.getDefaultPromptsBySearch(),yield this.onPromptDataScroll(!0),this.isLoading=!1}))).catch(t=>{this.isLoading=!1,this.onDialogClose()})}))}ngAfterViewInit(){const t=new MutationObserver(()=>{var e;(null===(e=this.cardWrapper)||void 0===e?void 0:e.nativeElement)&&(this.checkScroll(),t.disconnect())});t.observe(document.body,{childList:!0,subtree:!0})}onResize(){this.calculateDynamicContentHeight(),this.checkScroll()}calculateDynamicContentHeight(){let t=41;this.dialogExpanded&&(t=0),document.documentElement.style.setProperty("--kebsChatbotContentHeight",window.innerHeight-220-t+"px"),document.documentElement.style.setProperty("--kebsChatbotHistoryContentHeight",window.innerHeight-170-t+"px");let e=window.innerHeight-170-t,n=Math.floor(e/34);this.repeatArray=Array(n)}onDialogClose(){this._dialogRef.close()}scrollToBottom(){const t=this.chatScrollContainer.nativeElement;t.scrollTop=t.scrollHeight}groupHistoryData(){const t=s().startOf("day"),e=s().subtract(1,"days").startOf("day"),n=s().subtract(7,"days").startOf("day"),o=s().subtract(30,"days").startOf("day"),i={};this.historyData.forEach(r=>{const a=s(r.created_at).startOf("day");if(a.isSame(t,"day"))i.Today||(i.Today=[]),i.Today.push(r);else if(a.isSame(e,"day"))i.Yesterday||(i.Yesterday=[]),i.Yesterday.push(r);else if(a.isAfter(n))i["Previous 7 Days"]||(i["Previous 7 Days"]=[]),i["Previous 7 Days"].push(r);else if(a.isAfter(o))i["Previous 30 Days"]||(i["Previous 30 Days"]=[]),i["Previous 30 Days"].push(r);else{const t=a.format("MMMM YYYY");i[t]||(i[t]=[]),i[t].push(r)}}),this.historyGroupedData=i,this.historyGroupedDateData=Object.keys(i)}openHistory(){this.isHistoryVisible=!0,this.historyData=[],this.allHistory(!0)}closeHistory(){this.isHistoryVisible=!1}onHistoryDataScroll(){this.historySkip+=25,this.allHistory(!1)}openPromptLibrary(){this.promptLibrarySearchParams="",this.resetUiState(["isPromptLibraryVisible"]),this.promptLibraryData&&this.promptLibraryData.length>0&&(this.setPromptLibraryIndex(0),this.setPromptLibraryCategory(this.promptLibraryData[0].name))}setPromptLibraryIndex(t){this.currentSelectedPromptLibraryCategoryIndex=t}setPromptLibraryCategory(t){this.currentSelectedPromptLibraryCategory=t}findPromptLibraryIndex(t,e){var n;let o=t.findIndex(t=>t.id==e),i=null===(n=t.find(t=>t.id==e))||void 0===n?void 0:n.name;-1!=o&&(this.setPromptLibraryIndex(o),this.setPromptLibraryCategory(i))}onSearchParamsChange(){var t;const e=this.filter.transform(this.promptLibraryData,this.promptLibrarySearchParams);e.length>0?(null===(t=e.find(t=>t.name==this.currentSelectedPromptLibraryCategory))||void 0===t?void 0:t.name)||this.findPromptLibraryIndex(e,e[0].id):(this.currentSelectedPromptLibraryCategoryIndex=0,this.currentSelectedPromptLibraryCategory=null)}copyPromptToClipboard(t){return Object(o.c)(this,void 0,void 0,(function*(){t.isCopyInProgress=!0,this.promptSearchParams=null==t?void 0:t.prompt,setTimeout(()=>Object(o.c)(this,void 0,void 0,(function*(){t.isCopyInProgress=!1,yield this.onEnterSearchPrompt(null==t?void 0:t._id,null==t?void 0:t.prompt,!1,null,null,null)})),1e3)}))}highlightPromptKey(t){if(!t)return"-";const e=t.replace(/[{[]([^}\]]+)[}\]]/g,(t,e)=>`<span style="color: #ef4a61;">[${e}]</span>`);return this.sanitizer.bypassSecurityTrustHtml(e)}findMatchingPrompt(t,e,n=.9){return Object(o.c)(this,void 0,void 0,(function*(){let o={libraryIndex:-1,promptIndex:-1,similarity:0};return e.forEach((e,n)=>{e.prompts.forEach((e,i)=>{const r=this._stringSimilarity.similarity(t,e.prompt);r>o.similarity&&(o={libraryIndex:n,promptIndex:i,similarity:r})})}),o.similarity>=n?o:null}))}setSelectedModule(t,e){this.currentSelectedModuleId=t,this.currentSelectedModuleName=e,this.defaultPrompts()}openNewChat(){this.resetUiState(["isHomeScreenVisible"]),this.currentThreadData=[],this.defaultPrompts(),this.resetThreadId()}regenerateResponse(t){var e,n,i,r,a,c,s,l,d,p,g,h,m;return Object(o.c)(this,void 0,void 0,(function*(){(null==t?void 0:t.regenerate)?this.onEnterSearchPrompt(null,null===(e=null==t?void 0:t.data)||void 0===e?void 0:e.prompt,!0,null===(n=null==t?void 0:t.data)||void 0===n?void 0:n._id,null===(i=null==t?void 0:t.data)||void 0===i?void 0:i.response_content,t.index):(this.isCustomPrompt=!0,this.responseCustomizeData=null==t?void 0:t.data,"E"==(null==t?void 0:t.mode)?(this.customizePromptData=null===(c=null===(a=null===(r=null==t?void 0:t.data)||void 0===r?void 0:r.currentChat)||void 0===a?void 0:a.filter_config)||void 0===c?void 0:c.config,yield this.onEnterSearchPrompt(null,null===(l=null===(s=null==t?void 0:t.data)||void 0===s?void 0:s.currentChat)||void 0===l?void 0:l.prompt,!0,null===(p=null===(d=null==t?void 0:t.data)||void 0===d?void 0:d.currentChat)||void 0===p?void 0:p._id,null===(h=null===(g=null==t?void 0:t.data)||void 0===g?void 0:g.currentChat)||void 0===h?void 0:h.response_content,t.index)):yield this.onEnterSearchPrompt(this.customResponseId,null===(m=this.responsePromptData)||void 0===m?void 0:m.prompt,!1,null,null,t.index),this.responseCustomizeData=null,this.isCustomPrompt=!1)}))}addNewNodeForRegeneration(t,e,n){this.findAndInsertNewNode(this.currentThreadData[t],e,n)}findAndInsertNewNode(t,e,n){if(t._id===e)return t.children||(t.children=[]),t.children.push(n),!0;if(t.children&&Array.isArray(t.children))for(let o=0;o<t.children.length;o++){const i=t.children[o];if(i._id===e)return t.children.splice(o+1,0,n),!0;if(this.findAndInsertNewNode(i,e,n))return!0}return!1}onEnterSearchHistory(){this.allHistory(!0)}resetToNewChatBasedOnCurrentThread(t){!this.isHomeScreenVisible&&this.currentThreadData&&this.currentThreadData.length>0&&t.includes(this.currentThreadData[0].thread_id)&&this.openNewChat()}renameThread(){var t;let e=null===(t=this.selectedHistoryThreadData)||void 0===t?void 0:t._id,n=this.historyData.findIndex(t=>t._id==e),o=this.pinnedHistoryData.findIndex(t=>t._id==e);-1!=n?(this.historyData[n].isRenameInProgress=!0,this.groupHistoryData()):-1!=o&&(this.pinnedHistoryData[o].isRenameInProgress=!0,this.groupHistoryData()),setTimeout(t=>{if(-1!=n){const t=document.getElementById("#historyInputSearch-"+this.selectedHistoryDateIndex+"-"+this.selectedHistoryThreadIndex);t.focus(),t.select()}else if(-1!=o){const t=document.getElementById("#pinnedHistoryInputSearch-"+this.selectedHistoryThreadIndex);t.focus(),t.select()}},50)}setSelectedHistoryThreadData(t,e,n){this.selectedHistoryThreadData=t,this.selectedHistoryDateIndex=e,this.selectedHistoryThreadIndex=n}resetUiState(t){return Object(o.c)(this,void 0,void 0,(function*(){let e=["isChatVisible","isHomeScreenVisible","isPromptLibraryVisible"];for(let t=0;t<e.length;t++)this[e[t]]=!1;for(let n=0;n<t.length;n++)this[t[n]]=!0;"isHomeScreenVisible"==t&&(this.isReportsVisible=!1,this.isWidgetsVisible=!1,this.isAgentVisible=!1),yield this.getDefaultPromptsBySearch(),yield this.onPromptDataScroll(!0)}))}resetThreadId(){this.historySelectedThreadId=null}openModulesOverlay(t,e){var n;if(this.moduleSearchParams="",!(null===(n=this.overlayRef)||void 0===n?void 0:n.hasAttached())){const n=this._overlay.position().flexibleConnectedTo(t).withFlexibleDimensions(!0).withPush(!0).withViewportMargin(0).withGrowAfterOpen(!0).withPositions([{originX:"start",originY:"bottom",overlayX:"start",overlayY:"top"}]);n.withDefaultOffsetY(-26);const o=this._overlay.scrollStrategies.close();this.overlayRef=this._overlay.create({positionStrategy:n,scrollStrategy:o,hasBackdrop:!0,backdropClass:"",panelClass:["pop-up"]});const i=new r.h(e,this._viewContainerRef);this.overlayRef.attach(i),this.overlayRef.backdropClick().subscribe(()=>{this.closeOverlay()})}setTimeout(t=>{const e=document.getElementById("#moduleInputSearch");e.focus(),e.select()},50)}onClickModule(t,e){t!=this.currentSelectedModuleId&&(this.setSelectedModule(t,e),this.closeOverlay())}closeOverlay(){var t;null===(t=this.overlayRef)||void 0===t||t.dispose()}onRenameThread(t,e){let n={aid:this.profile.aid,thread_id:t,short_description:e};return new Promise((e,o)=>{this._aiService.changeDescriptionOfThread(n).pipe(Object(p.a)(this._onDestroy)).subscribe({next:n=>{if(0==n.err){let e=this.historyData.findIndex(e=>(null==e?void 0:e._id)==t);-1!=e&&(this.historyData[e].isRenameInProgress=!1,this.groupHistoryData());let n=this.pinnedHistoryData.findIndex(e=>(null==e?void 0:e._id)==t);-1!=n&&(this.pinnedHistoryData[n].isRenameInProgress=!1,this.groupHistoryData())}else this._toaster.showError("Error",n.msg,7e3);e(!0)},error:t=>{this._toaster.showError("Error","Error in Renaming Thread",7e3),o()}})})}archiveOrUnarchiveThread(t){var e;let n={aid:this.profile.aid,thread_id:[null===(e=this.selectedHistoryThreadData)||void 0===e?void 0:e._id],archive:t};return new Promise((t,e)=>{this._aiService.archiveOrUnarchiveThread(n).pipe(Object(p.a)(this._onDestroy)).subscribe({next:e=>{if(0==e.err){let t=this.historyData.findIndex(t=>(null==t?void 0:t._id)==(null==n?void 0:n.thread_id));-1!=t&&(this.historyData.splice(t,1),this.groupHistoryData());let e=this.pinnedHistoryData.findIndex(t=>(null==t?void 0:t._id)==(null==n?void 0:n.thread_id));-1!=e&&this.pinnedHistoryData.splice(e,1),this.resetToNewChatBasedOnCurrentThread(null==n?void 0:n.thread_id)}else this._toaster.showError("Error",e.msg,7e3);t(!0)},error:t=>{this._toaster.showError("Error","Error in Archiving Thread",7e3),e()}})})}pinOrUnpinThread(){var t,e,n;if(15==this.pinnedHistoryData.length&&!(null===(t=this.selectedHistoryThreadData)||void 0===t?void 0:t.pinned))return this._toaster.showInfo("Info \ud83d\udcdd","You can pin up to 15 threads!",7e3);let o={aid:this.profile.aid,thread_id:[null===(e=this.selectedHistoryThreadData)||void 0===e?void 0:e._id],pinned:!(null===(n=this.selectedHistoryThreadData)||void 0===n?void 0:n.pinned)};return new Promise((t,e)=>{this._aiService.pinOrUnpinThread(o).pipe(Object(p.a)(this._onDestroy)).subscribe({next:e=>{if(0==e.err)if(null==o?void 0:o.pinned){let t=this.historyData.findIndex(t=>(null==t?void 0:t._id)==(null==o?void 0:o.thread_id));if(-1!=t){let e=this.historyData.splice(t,1).map(t=>Object.assign(Object.assign({},t),{pinned:!0}));this.pinnedHistoryData=[...this.pinnedHistoryData,...e],this.groupHistoryData()}}else this.allHistory(!0);else this._toaster.showError("Error",e.msg,7e3);t(!0)},error:t=>{this._toaster.showError("Error","Error in Pin/Unpin Thread",7e3),e()}})})}deleteThread(){var t;let e={aid:this.profile.aid,thread_id:null===(t=this.selectedHistoryThreadData)||void 0===t?void 0:t._id};return new Promise((t,n)=>{this._aiService.deleteThread(e).pipe(Object(p.a)(this._onDestroy)).subscribe({next:n=>{var o;if(0==n.err){let t=this.historyData.findIndex(t=>(null==t?void 0:t._id)==(null==e?void 0:e.thread_id));-1!=t&&(this.historyData.splice(t,1),this.groupHistoryData());let n=this.pinnedHistoryData.findIndex(t=>(null==t?void 0:t._id)==(null==e?void 0:e.thread_id));-1!=n&&this.pinnedHistoryData.splice(n,1),this.resetToNewChatBasedOnCurrentThread([null===(o=this.selectedHistoryThreadData)||void 0===o?void 0:o._id])}else this._toaster.showError("Error",n.msg,7e3);t(!0)},error:t=>{this._toaster.showError("Error","Error in Deleting Thread",7e3),n()}})})}setSelectedHistoryThread(t){if(this.historySelectedThreadId=t,this.isThreadLoading)return;this.isThreadLoading=!0;let e={aid:this.profile.aid,oid:this.profile.oid,thread_id:t};return this.currentThreadData=[],this.isReportsVisible=!1,this.isWidgetsVisible=!1,new Promise((t,n)=>{this._aiService.retrieveThreadHistory(e).pipe(Object(p.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?(this.currentThreadData=e.data,this.resetUiState(["isChatVisible"]),setTimeout(()=>{this.scrollToBottom()},50)):(this.resetUiState(["isHomeScreenVisible"]),this._toaster.showWarning("Warning \u26a0\ufe0f",e.msg,7e3)),this.isThreadLoading=!1,t(!0)},error:t=>{this._toaster.showError("Error","Error in Fetching Session Data",7e3),this.resetUiState(["isHomeScreenVisible"]),this.isThreadLoading=!1,n()}})})}defaultPrompts(){return Object(o.c)(this,void 0,void 0,(function*(){let t={aid:this.profile.aid,oid:this.profile.oid,module_id:this.currentSelectedModuleId,accessible_module_ids:l.pluck(this.modules,"id")};return this.suggestedPrompts=[],new Promise((e,n)=>{this._aiService.defaultPrompts(t).pipe(Object(p.a)(this._onDestroy)).subscribe({next:t=>{0==t.err&&(this.suggestedPrompts=t.data),e(!0)},error:t=>{this._toaster.showError("Error","Error in Fetching Default Prompts",7e3),this.isLoading=!1,n()}})})}))}getChatbotModulesBasedOnRoleAccess(){return Object(o.c)(this,void 0,void 0,(function*(){let t={aid:this.profile.aid,oid:this.profile.oid};return this.modules=[],new Promise((e,n)=>{this._aiService.getChatbotModulesBasedOnRoleAccess(t).pipe(Object(p.a)(this._onDestroy)).subscribe({next:t=>{0==t.err&&(this.modules=t.data,this.modules&&this.modules.length>0&&this.setSelectedModule(this.modules[0].id,this.modules[0].name)),e(!0)},error:t=>{this._toaster.showError("Error","Error in Fetching Modules",7e3),this.isLoading=!1,n()}})})}))}allHistory(t){return Object(o.c)(this,void 0,void 0,(function*(){t&&(this.isHistoryLoading=!0,this.historySkip=0,this.historyData=[],this.historyGroupedData={},this.historyGroupedDateData=[]);let e={aid:this.profile.aid,oid:this.profile.oid,skip:this.historySkip,limit:this.historyLimit,searchParams:this.historySearchParams};return new Promise((t,n)=>{this._aiService.allHistory(e).pipe(Object(p.a)(this._onDestroy)).subscribe({next:e=>{0==e.err&&(this.historyData=[...this.historyData,...e.data],0==this.historySkip&&(this.pinnedHistoryData=e.pinned_data)),this.groupHistoryData(),this.isHistoryLoading=!1,t(!0)},error:t=>{this._toaster.showError("Error","Error in Fetching History Data",7e3),this.isHistoryLoading=!1,n()}})})}))}onEnterSearchPrompt(t,e,n,i,r,a,c=!1){var l,d,g,h;return Object(o.c)(this,void 0,void 0,(function*(){if(this.isTyping=!1,!this.isPromptApiInProgress&&e&&e.length>0&&e.trim().length>0){this.resetUiState(["isChatVisible"]);let o=!(!this.isReportsVisible||!c);if(n||this.isCustomPrompt?this.currentThreadData[a].isLoading=!0:(this.currentThreadData.push({prompt:e,isLoading:!o}),setTimeout(()=>{this.scrollToBottom()},50)),o)return this.isPromptApiInProgress=!1,this.promptSearchParams="",this.currentThreadData[this.currentThreadData.length-1].isCustomPrompt=!0,this.currentThreadData[this.currentThreadData.length-1].isReady=!1,void(this.currentThreadData[this.currentThreadData.length-1].isError=!1);let m=null,u=yield this.findMatchingPrompt(e,this.promptLibraryData,.9);u?(t=this.promptLibraryData[u.libraryIndex].prompts[u.promptIndex].id,m=this.promptLibraryData[u.libraryIndex].id):m=this.currentSelectedModuleId||2053;let C={aid:this.profile.aid,oid:this.profile.oid,prompt_id:t,prompt:e,module_id:m,is_regenerated:n,original_message_id:i,original_message_response:r,is_new_thread:1==this.currentThreadData.length&&!(null===(l=this.currentThreadData[0])||void 0===l?void 0:l.thread_id),thread_id:1!=this.currentThreadData.length||(null===(d=this.currentThreadData[0])||void 0===d?void 0:d.thread_id)?null===(g=this.currentThreadData[0])||void 0===g?void 0:g.thread_id:null,s3_link:1,current_date:s().format("YYYY-MM-DD"),is_report_prompt:this.isReportsVisible,is_widget_prompt:this.isWidgetsVisible,is_agent_prompt:this.isAgentVisible,chat_history:this.currentThreadData,is_custom_prompt:this.isCustomPrompt,filter_config:{data:null===(h=this.responseCustomizeData)||void 0===h?void 0:h.data,config:this.customizePromptData}};return this.promptSearchParams="",this.isPromptApiInProgress=!0,new Promise((t,e)=>{this._aiService.getPromptResponse(C).pipe(Object(p.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?(this.historySelectedThreadId=e.data.thread_id,n?(this.addNewNodeForRegeneration(a,i,e.data),this.currentThreadData[a].isLoading=!1,this.currentThreadData[a].isReady=!0,setTimeout(()=>{this.currentThreadData[a].isReady=!1},1500)):(this.isHistoryVisible&&(null==C?void 0:C.is_new_thread)&&(this.historySearchParams="",this.allHistory(!0)),this.currentThreadData[this.currentThreadData.length-1]=e.data,this.currentThreadData[this.currentThreadData.length-1].isReady=!0,setTimeout(()=>{this.currentThreadData[this.currentThreadData.length-1].isReady=!1},1500))):n?(this.currentThreadData[a].isLoading=!1,this.currentThreadData[a].isError=!0,this.currentThreadData[a].ErrorCode=(null==e?void 0:e.error_code)||"ERR_API_001"):(this.currentThreadData[this.currentThreadData.length-1].isLoading=!1,this.currentThreadData[this.currentThreadData.length-1].isError=!0,this.currentThreadData[this.currentThreadData.length-1].ErrorCode=(null==e?void 0:e.error_code)||"ERR_API_001"),this.resetCustomisePrompt(),setTimeout(()=>{this.isPromptApiInProgress=!1},1500),t(!0)},error:t=>{let o="ERR_API_001";504==(null==t?void 0:t.status)&&(o="ERR_SYSTEM_TIMEOUT"),n?(this.currentThreadData[a].isLoading=!1,this.currentThreadData[a].isError=!0,this.currentThreadData[a].ErrorCode=o):(this.currentThreadData[this.currentThreadData.length-1].isLoading=!1,this.currentThreadData[this.currentThreadData.length-1].isError=!0,this.currentThreadData[this.currentThreadData.length-1].ErrorCode=o),this.resetCustomisePrompt(),setTimeout(()=>{this.isPromptApiInProgress=!1},1500),e()}})})}}))}getPromptLibraryData(){return Object(o.c)(this,void 0,void 0,(function*(){let t={aid:this.profile.aid,oid:this.profile.oid};return this.promptLibraryData=[],new Promise((e,n)=>{this._aiService.getPromptLibraryData(t).pipe(Object(p.a)(this._onDestroy)).subscribe({next:t=>{0==t.err&&(this.promptLibraryData=t.data),e(!0)},error:t=>{this._toaster.showError("Error","Error in Fetching Modules",7e3),this.isLoading=!1,n()}})})}))}onDialogResizing(t=!1){t||(this.dialogExpanded=!this.dialogExpanded),this.calculateDynamicContentHeight();let e="40vw",n="98px";this.dialogExpanded&&(e="calc(-20vw - 84px + 100vw)",n="55px"),document.documentElement.style.setProperty("--kebsChatBotContentWidth",e),document.documentElement.style.setProperty("--chatBotAdjustedHeight",n),this.checkScroll()}getGreetingWithName(t,e){return(t||"Hello, ${name}").replace("${name}",e)}getSVGContent(t){if(t)return this.sanitizer.bypassSecurityTrustHtml(t)}scroll(t){if(this.cardWrapper&&this.cardWrapper.nativeElement){const e=this.cardWrapper.nativeElement;e.scrollBy({left:t*(e.offsetWidth/2),behavior:"smooth"}),this.checkScroll()}}checkScroll(){if(this.cardWrapper&&this.cardWrapper.nativeElement){const t=this.cardWrapper.nativeElement;this.canScrollLeft=t.scrollLeft>0;const e=t.scrollWidth-t.offsetWidth;this.canScrollRight=Math.ceil(t.scrollLeft)<Math.floor(e)}}setVisibility(t){return Object(o.c)(this,void 0,void 0,(function*(){"isReportsVisible"==t?this.isReportsVisible=!0:"isWidgetsVisible"==t?this.isWidgetsVisible=!0:"isAgentVisible"==t&&(this.isAgentVisible=!0),yield this.getDefaultPromptsBySearch(),yield this.onPromptDataScroll(!0)}))}clearSearchInput(t){if(!t)return;const e=t.target;e&&!e.closest(".overlay")&&setTimeout(()=>{this.promptSearchParams=null,this.isTyping=!1,this.searchPromptSkip=0},100)}onPromptDataScroll(t=!1){var e,n,i,r;return Object(o.c)(this,void 0,void 0,(function*(){if(null===(e=this.searchDefaultPrompts)||void 0===e?void 0:e.length)if(t){this.startIndex=0,this.skip=25,this.searchLazyLoadedList=[];const t=(null===(i=null===(n=this.promptSearchParams)||void 0===n?void 0:n.trim())||void 0===i?void 0:i.toLowerCase())||"";this.filteredPrompts=t?this.searchDefaultPrompts.filter(e=>{var n;return null===(n=e.prompt)||void 0===n?void 0:n.toLowerCase().includes(t)}):[...this.searchDefaultPrompts],this.assignSearchPromptsOnLazyLoading()}else{if(this.startIndex>=(null===(r=this.filteredPrompts)||void 0===r?void 0:r.length))return;this.startIndex=this.skip,this.skip+=25,this.assignSearchPromptsOnLazyLoading()}else this.searchLazyLoadedList=[]}))}assignSearchPromptsOnLazyLoading(){const t=this.filteredPrompts.slice(this.startIndex,this.skip);this.searchLazyLoadedList.push(...t)}getDefaultPromptsBySearch(){return Object(o.c)(this,void 0,void 0,(function*(){this.searchDefaultPrompts=[];let t={aid:this.profile.aid,oid:this.profile.oid,skip:this.searchPromptSkip,limit:25,module_id:l.pluck(this.modules,"id"),is_general_prompt:!this.isReportsVisible,is_report_prompt:this.isReportsVisible,is_agent_prompt:this.isAgentVisible,search_params:this.promptSearchParams?this.promptSearchParams:""};return new Promise((e,n)=>Object(o.c)(this,void 0,void 0,(function*(){yield this._aiService.getDefaultPromptsBySearch(t).pipe(Object(p.a)(this._onDestroy)).subscribe({next:t=>{0==t.err&&(this.searchDefaultPrompts=t.data),e(!0)},error:t=>{this._toaster.showError("Error","Error in Fetching Search Prompt Data",7e3),n()}})})))}))}selectSearchedPrompt(t,e,n){return Object(o.c)(this,void 0,void 0,(function*(){this.promptSearchParams=null==e?void 0:e.prompt;let t=!1;(null==e?void 0:e.is_custom_prompt)&&(t=!0,this.responsePromptData=e,this.customizePromptData=e.custom_config,this.customResponseId=null==e?void 0:e._id),setTimeout(()=>Object(o.c)(this,void 0,void 0,(function*(){yield this.onEnterSearchPrompt(null==e?void 0:e._id,null==e?void 0:e.prompt,!1,null,null,null,t)})),500)}))}onClickOutside(t){const e=t.target;this.elementRef.nativeElement.contains(e)||e.closest(".overlay")||(this.isTyping=!1)}getFeatureValue(t){var e,n,o;if(this.isReportsVisible){const n=null===(e=this.data)||void 0===e?void 0:e.aiThemeConfig["FEATURE-LIST"].find(t=>"isReportsVisible"===t.visibility);return n?n[t]:null}if(this.isWidgetsVisible){const e=null===(n=this.data)||void 0===n?void 0:n.aiThemeConfig["FEATURE-LIST"].find(t=>"isWidgetsVisible"===t.visibility);return e?e[t]:null}if(this.isAgentVisible){const e=null===(o=this.data)||void 0===o?void 0:o.aiThemeConfig["FEATURE-LIST"].find(t=>"isAgentVisible"===t.visibility);return e?e[t]:null}return null}setTypingTrue(){this.isTyping=!0,this.onPromptDataScroll(!0)}resetCustomisePrompt(){this.isCustomPrompt=!1,this.responsePromptData=null,this.customResponseId="",this.customizePromptData=null,this.responseCustomizeData=null}}return t.\u0275fac=function(e){return new(e||t)(h["\u0275\u0275directiveInject"](a.a),h["\u0275\u0275directiveInject"](a.h),h["\u0275\u0275directiveInject"](m.a),h["\u0275\u0275directiveInject"](u.a),h["\u0275\u0275directiveInject"](C.a),h["\u0275\u0275directiveInject"](h.ViewContainerRef),h["\u0275\u0275directiveInject"](i.e),h["\u0275\u0275directiveInject"](f.a),h["\u0275\u0275directiveInject"](x.c),h["\u0275\u0275directiveInject"](g.a),h["\u0275\u0275directiveInject"](h.ElementRef))},t.\u0275cmp=h["\u0275\u0275defineComponent"]({type:t,selectors:[["app-chatbot-dialog"]],viewQuery:function(t,e){if(1&t&&(h["\u0275\u0275viewQuery"](Gt,!0),h["\u0275\u0275viewQuery"](Nt,!0),h["\u0275\u0275viewQuery"](Ut,!0)),2&t){let t;h["\u0275\u0275queryRefresh"](t=h["\u0275\u0275loadQuery"]())&&(e.chatScrollContainer=t.first),h["\u0275\u0275queryRefresh"](t=h["\u0275\u0275loadQuery"]())&&(e.triggerModulesOverlayChange=t.first),h["\u0275\u0275queryRefresh"](t=h["\u0275\u0275loadQuery"]())&&(e.cardWrapper=t.first)}},hostBindings:function(t,e){1&t&&h["\u0275\u0275listener"]("resize",(function(){return e.onResize()}),!1,h["\u0275\u0275resolveWindow"])("click",(function(t){return e.onClickOutside(t)}),!1,h["\u0275\u0275resolveDocument"])},features:[h["\u0275\u0275ProvidersFeature"]([{provide:c.a,deps:[i.e],useFactory:t=>()=>t.scrollStrategies.reposition()},g.a])],decls:33,vars:17,consts:[[1,"bg-container"],["class","history-container",4,"ngIf"],[1,"chatbot-container"],[1,"chatbot-container-header"],[1,"align-items-with-gap"],["class","svg-icon","tooltip","History",3,"click",4,"ngIf"],["class","svg-icon","tooltip","Close History",3,"click",4,"ngIf"],[1,"ai-icon",3,"src"],["class","svg-icon","tooltip","FAQ",4,"ngIf"],[1,"svg-icon",3,"click"],["tooltip","Collapse",4,"ngIf","ngIfElse"],["expandTemplate",""],["tooltip","Close",1,"svg-icon",3,"click"],["width","24","height","25","viewBox","0 0 24 25","fill","none"],["id","mask0_1555_843","maskUnits","userSpaceOnUse","x","0","y","0","width","24","height","25",2,"mask-type","alpha"],["y","0.845703","width","24","height","24","fill","#D9D9D9"],["mask","url(#mask0_1555_843)"],["d","M8.227 17.6813L7.16357 16.6178L10.9367 12.8447L7.16357 9.09663L8.227 8.0332L12.0001 11.8063L15.7482 8.0332L16.8116 9.09663L13.0385 12.8447L16.8116 16.6178L15.7482 17.6813L12.0001 13.9082L8.227 17.6813Z","fill","#1C1B1F"],[1,"chatbot-container-content",3,"ngStyle"],[4,"ngIf"],["class","main-content",3,"ngStyle",4,"ngIf"],["class","prompt-library-content",4,"ngIf"],[1,"bg-image-1",3,"src"],[1,"bg-image-2",3,"src"],["class","chatbot-container-footer",4,"ngIf"],["historyMenu","matMenu"],["matMenuContent",""],["cdkConnectedOverlay","",3,"cdkConnectedOverlayOrigin"],["triggerModulesOverlayChange",""],[1,"history-container"],[1,"header"],[1,"main-title"],["tooltip","Close History",1,"svg-icon",3,"click"],["width","8","height","8","viewBox","0 0 8 8","fill","none"],["d","M1.59984 7.33359L0.666504 6.40026L3.0665 4.00026L0.666504 1.61693L1.59984 0.683594L3.99984 3.08359L6.38317 0.683594L7.3165 1.61693L4.9165 4.00026L7.3165 6.40026L6.38317 7.33359L3.99984 4.93359L1.59984 7.33359Z","fill","#111434"],[1,"search-ui"],[1,"search-bar"],["type","text","placeholder","Search From Threads",3,"ngModel","ngModelChange","keydown.enter"],["class","svg-icon",4,"ngIf"],["class","close-icon",3,"click",4,"ngIf"],["class","loading-state",4,"ngIf"],["class","no-history",4,"ngIf"],["class","content","infinite-scroll","",3,"infiniteScrollDistance","infiniteScrollThrottle","scrollWindow","scrolled",4,"ngIf"],[1,"svg-icon"],["width","16","height","16","viewBox","0 0 16 16","fill","none",3,"click"],["id","mask0_1126_15698","maskUnits","userSpaceOnUse","x","0","y","0","width","16","height","16",2,"mask-type","alpha"],["width","16","height","16","fill","#D9D9D9"],["mask","url(#mask0_1126_15698)"],["d","M12.7116 13.3178L8.7411 9.34708C8.40777 9.60008 8.04227 9.79603 7.6446 9.93492C7.24693 10.0738 6.83654 10.1432 6.41343 10.1432C5.37366 10.1432 4.48971 9.77931 3.7616 9.05142C3.03349 8.32353 2.66943 7.43997 2.66943 6.40075C2.66943 5.36164 3.03338 4.47775 3.76127 3.74908C4.48916 3.02053 5.37271 2.65625 6.41193 2.65625C7.45104 2.65625 8.33493 3.0203 9.0636 3.74842C9.79216 4.47653 10.1564 5.36047 10.1564 6.40025C10.1564 6.83614 10.0849 7.25292 9.94177 7.65058C9.79854 8.04836 9.60471 8.40747 9.36027 8.72792L13.3308 12.6984L12.7116 13.3178ZM6.41293 9.27675C7.21638 9.27675 7.89671 8.99808 8.45393 8.44075C9.01127 7.88353 9.28993 7.20319 9.28993 6.39975C9.28993 5.59631 9.01127 4.91597 8.45393 4.35875C7.89671 3.80142 7.21638 3.52275 6.41293 3.52275C5.60949 3.52275 4.92916 3.80142 4.37193 4.35875C3.8146 4.91597 3.53593 5.59631 3.53593 6.39975C3.53593 7.20319 3.8146 7.88353 4.37193 8.44075C4.92916 8.99808 5.60949 9.27675 6.41293 9.27675Z","fill","#7D838B"],[1,"close-icon",3,"click"],[1,"loading-state"],["class","loader",4,"ngFor","ngForOf"],[1,"loader"],[1,"no-history"],["infinite-scroll","",1,"content",3,"infiniteScrollDistance","infiniteScrollThrottle","scrollWindow","scrolled"],["class","single-date-log",4,"ngIf"],["class","divider",4,"ngIf"],[4,"ngFor","ngForOf"],[1,"single-date-log"],[1,"date"],["height","20px","viewBox","0 -960 960 960","width","20px","fill","#F27A6C",1,"pin-icon"],["d","m624-480 96 96v72H516v228l-36 36-36-36v-228H240v-72l96-96v-264h-48v-72h384v72h-48v264Zm-282 96h276l-66-66v-294H408v294l-66 66Zm138 0Z"],["class","single-history",3,"ngClass",4,"ngFor","ngForOf"],[1,"single-history",3,"ngClass"],["class","history-text",3,"matTooltip","ngStyle","click",4,"ngIf"],["class","inline-edit",4,"ngIf"],["class","svg-icon",3,"matMenuTriggerFor","click",4,"ngIf"],[1,"history-text",3,"matTooltip","ngStyle","click"],[1,"inline-edit"],["type","text","maxlength","150","placeholder","Enter a description...",3,"id","ngModel","ngModelChange","keydown.enter"],[1,"svg-icon",3,"matMenuTriggerFor","click"],["width","12","height","12","viewBox","0 0 12 12","fill","none"],["id","mask0_1126_15589","maskUnits","userSpaceOnUse","x","0","y","0","width","12","height","12",2,"mask-type","alpha"],["width","12","height","12","fill","#D9D9D9"],["mask","url(#mask0_1126_15589)"],["d","M6 9.63448C5.79375 9.63448 5.61721 9.56103 5.47038 9.41411C5.32346 9.26728 5.25 9.09073 5.25 8.88448C5.25 8.67823 5.32346 8.50165 5.47038 8.35473C5.61721 8.2079 5.79375 8.13448 6 8.13448C6.20625 8.13448 6.38279 8.2079 6.52962 8.35473C6.67654 8.50165 6.75 8.67823 6.75 8.88448C6.75 9.09073 6.67654 9.26728 6.52962 9.41411C6.38279 9.56103 6.20625 9.63448 6 9.63448ZM6 6.74986C5.79375 6.74986 5.61721 6.6764 5.47038 6.52948C5.32346 6.38265 5.25 6.20611 5.25 5.99986C5.25 5.79361 5.32346 5.61707 5.47038 5.47023C5.61721 5.32332 5.79375 5.24986 6 5.24986C6.20625 5.24986 6.38279 5.32332 6.52962 5.47023C6.67654 5.61707 6.75 5.79361 6.75 5.99986C6.75 6.20611 6.67654 6.38265 6.52962 6.52948C6.38279 6.6764 6.20625 6.74986 6 6.74986ZM6 3.86523C5.79375 3.86523 5.61721 3.79182 5.47038 3.64498C5.32346 3.49807 5.25 3.32148 5.25 3.11523C5.25 2.90898 5.32346 2.73244 5.47038 2.58561C5.61721 2.43869 5.79375 2.36523 6 2.36523C6.20625 2.36523 6.38279 2.43869 6.52962 2.58561C6.67654 2.73244 6.75 2.90898 6.75 3.11523C6.75 3.32148 6.67654 3.49807 6.52962 3.64498C6.38279 3.79182 6.20625 3.86523 6 3.86523Z","fill","#5F6C81"],[1,"divider"],["tooltip","History",1,"svg-icon",3,"click"],["id","mask0_1555_823","maskUnits","userSpaceOnUse","x","0","y","0","width","24","height","25",2,"mask-type","alpha"],["mask","url(#mask0_1555_823)"],["d","M11.9805 21.3457C9.97029 21.3457 8.1985 20.7322 6.66517 19.5053C5.13184 18.2783 4.14915 16.7085 3.71712 14.7957C3.66327 14.5969 3.69564 14.4143 3.81422 14.2476C3.9328 14.0809 4.10074 13.9841 4.31802 13.9572C4.52252 13.9303 4.70586 13.9707 4.86805 14.0784C5.03021 14.1861 5.14335 14.3444 5.20745 14.5534C5.58823 16.0982 6.40843 17.3665 7.66804 18.3582C8.92764 19.3498 10.3651 19.8457 11.9805 19.8457C13.9305 19.8457 15.5847 19.1665 16.943 17.8082C18.3014 16.4498 18.9805 14.7957 18.9805 12.8457C18.9805 10.8957 18.3014 9.24151 16.943 7.88318C15.5847 6.52485 13.9305 5.84568 11.9805 5.84568C10.8882 5.84568 9.86453 6.0883 8.9094 6.57355C7.95428 7.0588 7.13184 7.72643 6.4421 8.57643H8.30749C8.51998 8.57643 8.6981 8.64834 8.84185 8.79215C8.9856 8.93595 9.05747 9.11415 9.05747 9.32675C9.05747 9.53934 8.9856 9.71742 8.84185 9.861C8.6981 10.0046 8.51998 10.0764 8.30749 10.0764H4.88444C4.62836 10.0764 4.4137 9.98978 4.24047 9.81653C4.06724 9.6433 3.98062 9.42864 3.98062 9.17255V5.74953C3.98062 5.53703 4.05252 5.3589 4.19632 5.21515C4.34014 5.0714 4.51834 4.99953 4.73092 4.99953C4.94352 4.99953 5.12161 5.0714 5.2652 5.21515C5.40878 5.3589 5.48057 5.53703 5.48057 5.74953V7.36878C6.2921 6.4111 7.26389 5.66783 8.39594 5.13898C9.52799 4.61013 10.7229 4.3457 11.9805 4.3457C13.1605 4.3457 14.2658 4.56879 15.2963 5.01495C16.3268 5.4611 17.2254 6.0675 17.992 6.83415C18.7587 7.60082 19.3651 8.49935 19.8113 9.52975C20.2574 10.5602 20.4805 11.6653 20.4805 12.8451C20.4805 14.025 20.2574 15.1303 19.8113 16.1611C19.3651 17.1918 18.7587 18.0905 17.992 18.8572C17.2254 19.6239 16.3268 20.2303 15.2963 20.6764C14.2658 21.1226 13.1605 21.3457 11.9805 21.3457ZM12.7594 12.5418L15.5094 15.2919C15.6478 15.4303 15.7187 15.6044 15.7219 15.814C15.7251 16.0236 15.6543 16.2008 15.5094 16.3457C15.3645 16.4905 15.1889 16.563 14.9825 16.563C14.7761 16.563 14.6004 16.4905 14.4556 16.3457L11.5305 13.4207C11.437 13.3271 11.3684 13.2251 11.3248 13.1147C11.2812 13.0044 11.2594 12.8903 11.2594 12.7726V8.59565C11.2594 8.38317 11.3313 8.20505 11.4751 8.0613C11.6189 7.91755 11.7971 7.84568 12.0097 7.84568C12.2223 7.84568 12.4004 7.91755 12.544 8.0613C12.6876 8.20505 12.7594 8.38317 12.7594 8.59565V12.5418Z","fill","#1C1B1F"],["width","24","height","25","fill","#464646"],["clip-path","url(#clip0_1126_2981)"],["width","1360","height","768","transform","translate(-807 -119)","fill","#F9F9F9"],["clip-path","url(#clip1_1126_2981)"],["width","1295","height","110","transform","translate(-735 -50)","fill","url(#paint0_linear_1126_2981)"],["opacity","0.6"],["opacity","0.2","x","-832","y","-119","width","1385","height","768","fill","black"],["filter","url(#filter0_ddd_1126_2981)"],["clip-path","url(#clip2_1126_2981)"],["x","-24","y","-20","width","577","height","667","rx","8","fill","white"],["opacity","0.3","x","-24","y","-20","width","577","height","56","fill","#D9D9D9"],["opacity","0.1"],["id","path-5-inside-1_1126_2981","fill","white"],["d","M12 23.4688C6.07 23.4688 1.25 18.6488 1.25 12.7188C1.25 6.78875 6.07 1.96875 12 1.96875C17.93 1.96875 22.75 6.78875 22.75 12.7188C22.75 18.6488 17.93 23.4688 12 23.4688ZM12 3.46875C6.9 3.46875 2.75 7.61875 2.75 12.7188C2.75 17.8188 6.9 21.9688 12 21.9688C17.1 21.9688 21.25 17.8188 21.25 12.7188C21.25 7.61875 17.1 3.46875 12 3.46875Z"],["d","M12 23.4688C6.07 23.4688 1.25 18.6488 1.25 12.7188C1.25 6.78875 6.07 1.96875 12 1.96875C17.93 1.96875 22.75 6.78875 22.75 12.7188C22.75 18.6488 17.93 23.4688 12 23.4688ZM12 3.46875C6.9 3.46875 2.75 7.61875 2.75 12.7188C2.75 17.8188 6.9 21.9688 12 21.9688C17.1 21.9688 21.25 17.8188 21.25 12.7188C21.25 7.61875 17.1 3.46875 12 3.46875Z","fill","#292D32","stroke","#5F6C81","stroke-width","3","mask","url(#path-5-inside-1_1126_2981)"],["id","path-6-inside-2_1126_2981","fill","white"],["d","M13.2599 16.9989C13.0699 16.9989 12.8799 16.9289 12.7299 16.7789L9.19992 13.2489C8.90992 12.9589 8.90992 12.4789 9.19992 12.1889L12.7299 8.65891C13.0199 8.36891 13.4999 8.36891 13.7899 8.65891C14.0799 8.94891 14.0799 9.42891 13.7899 9.71891L10.7899 12.7189L13.7899 15.7189C14.0799 16.0089 14.0799 16.4889 13.7899 16.7789C13.6499 16.9289 13.4599 16.9989 13.2599 16.9989Z"],["d","M13.2599 16.9989C13.0699 16.9989 12.8799 16.9289 12.7299 16.7789L9.19992 13.2489C8.90992 12.9589 8.90992 12.4789 9.19992 12.1889L12.7299 8.65891C13.0199 8.36891 13.4999 8.36891 13.7899 8.65891C14.0799 8.94891 14.0799 9.42891 13.7899 9.71891L10.7899 12.7189L13.7899 15.7189C14.0799 16.0089 14.0799 16.4889 13.7899 16.7789C13.6499 16.9289 13.4599 16.9989 13.2599 16.9989Z","fill","#292D32","stroke","#5F6C81","stroke-width","3","mask","url(#path-6-inside-2_1126_2981)"],["x","-23.5","y","-19.5","width","576","height","666","rx","7.5","stroke","#E8E9EE"],["id","filter0_ddd_1126_2981","x","-51","y","-39","width","631","height","721","filterUnits","userSpaceOnUse","color-interpolation-filters","sRGB"],["flood-opacity","0","result","BackgroundImageFix"],["in","SourceAlpha","type","matrix","values","0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0","result","hardAlpha"],["radius","7","operator","dilate","in","SourceAlpha","result","effect1_dropShadow_1126_2981"],["dy","8"],["stdDeviation","10"],["in2","hardAlpha","operator","out"],["type","matrix","values","0 0 0 0 0.933333 0 0 0 0 0.286275 0 0 0 0 0.380392 0 0 0 0.2 0"],["mode","normal","in2","BackgroundImageFix","result","effect1_dropShadow_1126_2981"],["dy","1"],["stdDeviation","0.5"],["type","matrix","values","0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"],["mode","normal","in2","effect1_dropShadow_1126_2981","result","effect2_dropShadow_1126_2981"],["dy","4"],["stdDeviation","2"],["type","matrix","values","0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"],["mode","normal","in2","effect2_dropShadow_1126_2981","result","effect3_dropShadow_1126_2981"],["mode","normal","in","SourceGraphic","in2","effect3_dropShadow_1126_2981","result","shape"],["id","paint0_linear_1126_2981","x1","0","y1","55","x2","1295","y2","55","gradientUnits","userSpaceOnUse"],["stop-color","white"],["offset","1","stop-color","#FBDBE1","stop-opacity","0.8"],["id","clip0_1126_2981"],["width","1360","height","768","fill","white","transform","translate(-807 -119)"],["id","clip1_1126_2981"],["width","1295","height","110","fill","white","transform","translate(-735 -50)"],["id","clip2_1126_2981"],["tooltip","FAQ",1,"svg-icon"],["width","14","height","15","viewBox","0 0 14 15","fill","none",3,"click",4,"ngIf"],["width","28","height","25","viewBox","0 0 28 25","fill","none",3,"click",4,"ngIf"],["width","14","height","15","viewBox","0 0 14 15","fill","none",3,"click"],["d","M1.79492 10.9117C1.91026 10.857 2.02814 10.8111 2.14859 10.7739C2.26903 10.7367 2.39903 10.7182 2.53859 10.7182H3.25642V2.51302H2.53859C2.32781 2.51302 2.1512 2.58546 2.00876 2.73035C1.8662 2.87524 1.79492 3.05069 1.79492 3.25669V10.9117ZM2.53859 14.1797C2.05426 14.1797 1.64253 14.0114 1.30342 13.6749C0.964422 13.3383 0.794922 12.9296 0.794922 12.4489V3.25669C0.794922 2.77235 0.964422 2.36063 1.30342 2.02152C1.64253 1.68252 2.05426 1.51302 2.53859 1.51302H7.29492V2.51302H4.25642V10.7182H8.66675V8.34635H9.66675V11.7182H2.53859C2.33259 11.7182 2.15714 11.7881 2.01226 11.9279C1.86737 12.0677 1.79492 12.2411 1.79492 12.4479C1.79492 12.6545 1.86737 12.8282 2.01226 12.9689C2.15714 13.1094 2.33259 13.1797 2.53859 13.1797H11.1283V7.67969H12.1283V14.1797H2.53859ZM10.1283 7.67969C10.1283 6.70391 10.4676 5.87663 11.1464 5.19785C11.8252 4.51908 12.6525 4.17969 13.6283 4.17969C12.6525 4.17969 11.8252 3.8403 11.1464 3.16152C10.4676 2.48274 10.1283 1.65547 10.1283 0.679688C10.1283 1.65547 9.78887 2.48274 9.11009 3.16152C8.43131 3.8403 7.60403 4.17969 6.62825 4.17969C7.60403 4.17969 8.43131 4.51908 9.11009 5.19785C9.78887 5.87663 10.1283 6.70391 10.1283 7.67969Z","fill","url(#paint0_linear_2061_126464)"],["id","paint0_linear_2061_126464","x1","13.6283","y1","7.42969","x2","0.11551","y2","7.42969","gradientUnits","userSpaceOnUse"],["stop-color","#EF4A61"],["offset","1","stop-color","#F27A6C"],["width","28","height","25","viewBox","0 0 28 25","fill","none",3,"click"],["id","mask0_1534_29427","maskUnits","userSpaceOnUse","x","6","y","3","width","17","height","17",2,"mask-type","alpha"],["x","6.46204","y","3.84497","width","16","height","16","fill","#D9D9D9"],["mask","url(#mask0_1534_29427)"],["d","M11.2568 14.7167H12.2568V6.51156H11.2568V14.7167ZM20.1287 18.1782H10.539C10.0547 18.1782 9.64296 18.0087 9.30385 17.6697C8.96485 17.3306 8.79535 16.9189 8.79535 16.4346V7.25522C8.79535 6.77089 8.96485 6.35917 9.30385 6.02006C9.64296 5.68106 10.0547 5.51156 10.539 5.51156H14.6492C14.3526 5.87478 14.1212 6.28206 13.955 6.73339C13.7887 7.18461 13.7055 7.66622 13.7055 8.17822C13.7055 9.32689 14.0863 10.3177 14.8478 11.1507C15.6094 11.9836 16.5492 12.4573 17.6672 12.5719V15.7167H10.539C10.333 15.7167 10.1576 15.7866 10.0127 15.9264C9.86779 16.0663 9.79535 16.2396 9.79535 16.4464C9.79535 16.6531 9.86779 16.8267 10.0127 16.9674C10.1576 17.1079 10.333 17.1782 10.539 17.1782H19.1287V12.4847C19.3132 12.4419 19.4878 12.3883 19.6523 12.3237C19.8169 12.2592 19.9757 12.1816 20.1287 12.0911V18.1782ZM18.1287 11.6782C18.1287 10.7024 18.4681 9.87517 19.1468 9.19639C19.8256 8.51761 20.6529 8.17822 21.6287 8.17822C20.6529 8.17822 19.8256 7.83883 19.1468 7.16006C18.4681 6.48128 18.1287 5.654 18.1287 4.67822C18.1287 5.654 17.7893 6.48128 17.1105 7.16006C16.4317 7.83883 15.6045 8.17822 14.6287 8.17822C15.6045 8.17822 16.4317 8.51761 17.1105 9.19639C17.7893 9.87517 18.1287 10.7024 18.1287 11.6782Z","fill","url(#paint0_linear_1534_29427)"],["x1","11.962","y1","21.345","x2","16.962","y2","21.345","stroke","url(#paint1_linear_1534_29427)"],["id","paint0_linear_1534_29427","x1","21.6287","y1","11.4282","x2","8.11594","y2","11.4282","gradientUnits","userSpaceOnUse"],["id","paint1_linear_1534_29427","x1","16.962","y1","22.345","x2","11.6973","y2","22.345","gradientUnits","userSpaceOnUse"],["tooltip","Collapse"],["width","14","height","14","viewBox","0 0 14 14","fill","none","xmlns","http://www.w3.org/2000/svg"],["d","M6 7.84503V13.345H4.5V9.34503H0.5V7.84503H6ZM9.5 0.345032V4.34503H13.5V5.84503H8V0.345032H9.5Z","fill","#1C1B1F"],["tooltip","Expand"],["d","M0.5 13.345V7.84503H2V11.845H6V13.345H0.5ZM12 5.84503V1.84503H8V0.345032H13.5V5.84503H12Z","fill","#1C1B1F"],[1,"loading-container"],[1,"gif",3,"src"],["class","carousel-text",4,"ngIf"],[1,"carousel-text"],[1,"text-content"],[1,"main-text"],[1,"sub-text"],[1,"main-content",3,"ngStyle"],["chatScrollContainer",""],[1,"header-content-alignment"],[1,"ai-img-header",3,"src"],[1,"align-items-column"],[1,"main-text",3,"ngClass"],[1,"sub-text",3,"ngClass"],["class","section-title",4,"ngIf"],["class","features",4,"ngIf"],["class","predefined-sections",4,"ngIf"],[1,"section-title"],[1,"features"],["class","scroll-button scroll-left",3,"click",4,"ngIf"],["class","card-items-wrapper",4,"ngIf"],["class","scroll-button scroll-right",3,"click",4,"ngIf"],[1,"scroll-button","scroll-left",3,"click"],["width","10","height","16","viewBox","0 0 10 16","fill","none","xmlns","http://www.w3.org/2000/svg"],["d","M2.64083 8.00001L8.91812 14.2773C9.08367 14.4429 9.1643 14.64 9.15999 14.8686C9.15569 15.0972 9.07076 15.2943 8.9052 15.46C8.73965 15.6256 8.54256 15.7083 8.31395 15.7083C8.08534 15.7083 7.88819 15.6256 7.72249 15.46L1.3427 9.0673C1.19215 8.91661 1.08055 8.74779 1.00791 8.56084C0.935128 8.3739 0.89874 8.18695 0.89874 8.00001C0.89874 7.81307 0.935128 7.62612 1.00791 7.43918C1.08055 7.25223 1.19215 7.08341 1.3427 6.93272L7.73541 0.54C7.90096 0.374497 8.0959 0.293791 8.3202 0.298093C8.54465 0.302399 8.73965 0.38733 8.9052 0.552924C9.07076 0.718517 9.15353 0.915644 9.15353 1.14425C9.15353 1.37286 9.07076 1.56999 8.9052 1.73559L2.64083 8.00001Z","fill","#EE4961"],[1,"card-items-wrapper"],["cardWrapper",""],["class","feature-widget-class",3,"click",4,"ngFor","ngForOf"],[1,"feature-widget-class",3,"click"],[1,"align-content-left"],[1,"svg-container",3,"innerHtml"],[1,"feature-widget-text-class"],[1,"widget-header"],["width","16","height","16","viewBox","0 0 16 16","fill","none","xmlns","http://www.w3.org/2000/svg"],["d","M9.15542 8L6.87188 10.2838C6.79382 10.359 6.75479 10.4531 6.75479 10.566C6.75479 10.6791 6.79382 10.7762 6.87188 10.8573C6.95299 10.9492 7.05208 10.9925 7.16917 10.9871C7.28611 10.9818 7.38514 10.9385 7.46625 10.8573L9.8525 8.47125C9.98722 8.33653 10.0546 8.17944 10.0546 8C10.0546 7.82056 9.98722 7.66347 9.8525 7.52875L7.46146 5.13792C7.38035 5.05667 7.28208 5.01417 7.16667 5.01042C7.05125 5.00667 6.95299 5.05076 6.87188 5.14271C6.79382 5.22382 6.75667 5.32208 6.76042 5.4375C6.76417 5.55292 6.80667 5.65118 6.88792 5.73229L9.15542 8ZM8.00271 15.5C6.96562 15.5 5.99056 15.3032 5.0775 14.9096C4.16458 14.516 3.37042 13.9818 2.695 13.3071C2.01958 12.6324 1.48493 11.8389 1.09104 10.9267C0.697014 10.0146 0.5 9.03993 0.5 8.00271C0.5 6.96562 0.696805 5.99056 1.09042 5.0775C1.48403 4.16458 2.01819 3.37042 2.69292 2.695C3.36764 2.01958 4.16111 1.48493 5.07333 1.09104C5.98542 0.697014 6.96007 0.5 7.99729 0.5C9.03438 0.5 10.0094 0.696806 10.9225 1.09042C11.8354 1.48403 12.6296 2.01819 13.305 2.69292C13.9804 3.36764 14.5151 4.16111 14.909 5.07333C15.303 5.98542 15.5 6.96007 15.5 7.99729C15.5 9.03438 15.3032 10.0094 14.9096 10.9225C14.516 11.8354 13.9818 12.6296 13.3071 13.305C12.6324 13.9804 11.8389 14.5151 10.9267 14.909C10.0146 15.303 9.03993 15.5 8.00271 15.5ZM8 14.6667C9.86111 14.6667 11.4375 14.0208 12.7292 12.7292C14.0208 11.4375 14.6667 9.86111 14.6667 8C14.6667 6.13889 14.0208 4.5625 12.7292 3.27083C11.4375 1.97917 9.86111 1.33333 8 1.33333C6.13889 1.33333 4.5625 1.97917 3.27083 3.27083C1.97917 4.5625 1.33333 6.13889 1.33333 8C1.33333 9.86111 1.97917 11.4375 3.27083 12.7292C4.5625 14.0208 6.13889 14.6667 8 14.6667Z","fill","#7D838B"],[1,"scroll-button","scroll-right",3,"click"],["d","M7.35917 7.99999L1.08188 1.7227C0.916327 1.55714 0.835702 1.36006 0.840007 1.13145C0.844313 0.902837 0.929244 0.705684 1.0948 0.53999C1.26035 0.374434 1.45744 0.291656 1.68605 0.291656C1.91466 0.291656 2.11181 0.374434 2.27751 0.53999L8.6573 6.9327C8.80785 7.08339 8.91945 7.25221 8.99209 7.43916C9.06487 7.6261 9.10126 7.81305 9.10126 7.99999C9.10126 8.18693 9.06487 8.37388 8.99209 8.56082C8.91945 8.74777 8.80785 8.91659 8.6573 9.06728L2.26459 15.46C2.09904 15.6255 1.9041 15.7062 1.6798 15.7019C1.45535 15.6976 1.26035 15.6126 1.0948 15.4471C0.929244 15.2815 0.846466 15.0844 0.846466 14.8558C0.846466 14.6272 0.929244 14.4301 1.0948 14.2644L7.35917 7.99999Z","fill","#EE4961"],[1,"predefined-sections"],[1,"module-section"],["cdkOverlayOrigin","",1,"module-dropdown",3,"click"],["triggerModulesOverlay","cdkOverlayOrigin","triggerModulesOverlayField",""],["width","6","height","4","viewBox","0 0 6 4","fill","none"],["d","M2.64948 3.65033L0.232813 1.23366C0.199479 1.20033 0.174479 1.16421 0.157813 1.12533C0.141146 1.08644 0.132812 1.04477 0.132812 1.00033C0.132812 0.911437 0.163368 0.833659 0.224479 0.766992C0.28559 0.700326 0.366146 0.666992 0.466146 0.666992H5.53281C5.63281 0.666992 5.71337 0.700326 5.77448 0.766992C5.83559 0.833659 5.86615 0.911437 5.86615 1.00033C5.86615 1.02255 5.83281 1.10033 5.76615 1.23366L3.34948 3.65033C3.29392 3.70588 3.23837 3.74477 3.18281 3.76699C3.12726 3.78921 3.06615 3.80033 2.99948 3.80033C2.93281 3.80033 2.8717 3.78921 2.81615 3.76699C2.76059 3.74477 2.70503 3.70588 2.64948 3.65033Z","fill","#5F6C81"],[1,"section-content"],["class","section-content-align",3,"ngStyle","click",4,"ngFor","ngForOf"],[1,"section-content-align",3,"ngStyle","click"],["width","24","height","24","viewBox","0 0 24 24","fill","none",1,"icon"],["id","mask0_1603_776","maskUnits","userSpaceOnUse","x","0","y","0","width","24","height","24",2,"mask-type","alpha"],["width","24","height","24","fill","#D9D9D9"],["mask","url(#mask0_1603_776)"],["d","M12 21.5769C11.4949 21.5769 11.0593 21.4019 10.6932 21.0519C10.3272 20.7019 10.1282 20.2743 10.0961 19.7692H13.9038C13.8718 20.2743 13.6727 20.7019 13.3067 21.0519C12.9407 21.4019 12.5051 21.5769 12 21.5769ZM8.25 18.3846V16.8846H15.75V18.3846H8.25ZM8.40382 15.5C7.35641 14.8487 6.52725 13.9977 5.91635 12.9471C5.30545 11.8964 5 10.7474 5 9.49998C5 7.55128 5.67948 5.89743 7.03845 4.53845C8.39743 3.17948 10.0513 2.5 12 2.5C13.9487 2.5 15.6025 3.17948 16.9615 4.53845C18.3205 5.89743 19 7.55128 19 9.49998C19 10.7474 18.6945 11.8964 18.0836 12.9471C17.4727 13.9977 16.6435 14.8487 15.5961 15.5H8.40382ZM8.84997 14H15.15C15.9 13.4666 16.4791 12.8083 16.8875 12.025C17.2958 11.2416 17.5 10.4 17.5 9.49998C17.5 7.96664 16.9666 6.66664 15.9 5.59998C14.8333 4.53331 13.5333 3.99998 12 3.99998C10.4666 3.99998 9.16664 4.53331 8.09997 5.59998C7.03331 6.66664 6.49997 7.96664 6.49997 9.49998C6.49997 10.4 6.70414 11.2416 7.11247 12.025C7.52081 12.8083 8.09997 13.4666 8.84997 14Z","fill","#D4D6D8"],[1,"section-content-text",3,"innerHTML"],["class","chat-container",4,"ngIf"],[1,"chat-ui-header-section"],[1,"header",3,"ngClass"],[1,"text-color"],[1,"sub-header",3,"ngClass"],[1,"chat-container"],[3,"aid","oid","chatLoader","aiIconShort","currentThreadData","isThreadLoading","isPromptApiInProgress","chatResponseText","moduleList","customizePromptConfig","regenerateResponseEmitter"],[1,"prompt-library-content"],[1,"prompt-title"],[1,"prompt-title-icon",3,"click"],[1,"prompt-title-text"],["width","16","height","16","viewBox","0 0 16 16","fill","none"],["clip-path","url(#clip0_1497_30370)"],["d","M12.0207 11.0779L14.876 13.9326L13.9327 14.8759L11.078 12.0206C10.0158 12.8721 8.69468 13.3352 7.33334 13.3333C4.02134 13.3333 1.33334 10.6453 1.33334 7.33325C1.33334 4.02125 4.02134 1.33325 7.33334 1.33325C10.6453 1.33325 13.3333 4.02125 13.3333 7.33325C13.3353 8.69459 12.8722 10.0157 12.0207 11.0779ZM10.6833 10.5833C11.5294 9.71318 12.0019 8.54687 12 7.33325C12 4.75459 9.91134 2.66659 7.33334 2.66659C4.75468 2.66659 2.66668 4.75459 2.66668 7.33325C2.66668 9.91125 4.75468 11.9999 7.33334 11.9999C8.54696 12.0018 9.71327 11.5293 10.5833 10.6833L10.6833 10.5833Z","fill","#B9C0CA"],["id","clip0_1497_30370"],["width","16","height","16","fill","white"],["type","text","placeholder","Search Prompts...","maxlength","50",3,"ngModel","ngModelChange"],["style","cursor: pointer","width","24","height","25","viewBox","0 0 24 25","fill","none",3,"click",4,"ngIf"],[1,"content"],[1,"categories"],["width","16","height","16","viewBox","0 0 16 16","fill","none",2,"min-width","16px"],["d","M11.3333 6.66658H12.6667C14 6.66658 14.6667 5.99992 14.6667 4.66659V3.33325C14.6667 1.99992 14 1.33325 12.6667 1.33325H11.3333C9.99999 1.33325 9.33333 1.99992 9.33333 3.33325V4.66659C9.33333 5.99992 9.99999 6.66658 11.3333 6.66658Z","stroke","#111434","stroke-width","1.5","stroke-miterlimit","10","stroke-linecap","round","stroke-linejoin","round"],["d","M3.33333 14.6666H4.66666C5.99999 14.6666 6.66666 13.9999 6.66666 12.6666V11.3333C6.66666 9.99992 5.99999 9.33325 4.66666 9.33325H3.33333C1.99999 9.33325 1.33333 9.99992 1.33333 11.3333V12.6666C1.33333 13.9999 1.99999 14.6666 3.33333 14.6666Z","stroke","#111434","stroke-width","1.5","stroke-miterlimit","10","stroke-linecap","round","stroke-linejoin","round"],["d","M3.99999 6.66658C5.47275 6.66658 6.66666 5.47268 6.66666 3.99992C6.66666 2.52716 5.47275 1.33325 3.99999 1.33325C2.52724 1.33325 1.33333 2.52716 1.33333 3.99992C1.33333 5.47268 2.52724 6.66658 3.99999 6.66658Z","stroke","#111434","stroke-width","1.5","stroke-miterlimit","10","stroke-linecap","round","stroke-linejoin","round"],["d","M12 14.6666C13.4728 14.6666 14.6667 13.4727 14.6667 11.9999C14.6667 10.5272 13.4728 9.33325 12 9.33325C10.5272 9.33325 9.33333 10.5272 9.33333 11.9999C9.33333 13.4727 10.5272 14.6666 12 14.6666Z","stroke","#111434","stroke-width","1.5","stroke-miterlimit","10","stroke-linecap","round","stroke-linejoin","round"],[1,"categories-text"],[1,"count"],[1,"categories-list"],["class","category-content",4,"ngIf"],["class","category-content-empty-state",4,"ngIf"],["width","24","height","25","viewBox","0 0 24 25","fill","none",2,"cursor","pointer",3,"click"],["d","M8.227 17.6813L7.16357 16.6178L10.9367 12.8447L7.16357 9.09663L8.227 8.0332L12.0001 11.8063L15.7482 8.0332L16.8116 9.09663L13.0385 12.8447L16.8116 16.6178L15.7482 17.6813L12.0001 13.9082L8.227 17.6813Z","fill","#B9C0CA"],[1,"category-list",3,"ngClass","click"],["width","14","height","20","viewBox","0 0 14 20","fill","none",2,"min-width","14px"],["d","M7.00001 19.5769C6.49489 19.5769 6.05931 19.4019 5.69328 19.0519C5.32726 18.7019 5.12822 18.2743 5.09616 17.7692H8.90386C8.87179 18.2743 8.67275 18.7019 8.30673 19.0519C7.9407 19.4019 7.50512 19.5769 7.00001 19.5769ZM3.25003 16.3846V14.8846H10.75V16.3846H3.25003ZM3.40386 13.5C2.35644 12.8487 1.52728 11.9977 0.91638 10.9471C0.30548 9.89644 3.05176e-05 8.74741 3.05176e-05 7.49998C3.05176e-05 5.55128 0.679514 3.89743 2.03848 2.53845C3.39746 1.17948 5.05131 0.5 7.00001 0.5C8.94871 0.5 10.6025 1.17948 11.9615 2.53845C13.3205 3.89743 14 5.55128 14 7.49998C14 8.74741 13.6945 9.89644 13.0836 10.9471C12.4727 11.9977 11.6436 12.8487 10.5962 13.5H3.40386ZM3.85001 12H10.15C10.9 11.4666 11.4792 10.8083 11.8875 10.025C12.2958 9.24164 12.5 8.39998 12.5 7.49998C12.5 5.96664 11.9667 4.66664 10.9 3.59998C9.83334 2.53331 8.53334 1.99998 7.00001 1.99998C5.46667 1.99998 4.16667 2.53331 3.10001 3.59998C2.03334 4.66664 1.50001 5.96664 1.50001 7.49998C1.50001 8.39998 1.70417 9.24164 2.11251 10.025C2.52084 10.8083 3.10001 11.4666 3.85001 12Z","fill","#D4D6D8"],[1,"text",3,"matTooltip"],[1,"count",3,"ngClass"],[1,"category-content"],["class","single-prompt",3,"cdkCopyToClipboard","click",4,"ngFor","ngForOf"],[1,"single-prompt",3,"cdkCopyToClipboard","click"],[1,"text",3,"innerHTML"],[1,"copy"],["width","12px","height","12px",3,"src"],[1,"category-content-empty-state"],[1,"chatbot-container-footer"],["class","svg-icon","tooltip","New Chat",3,"click",4,"ngIf"],[1,"search-overlay-section",3,"mouseleave"],["class","overlay",3,"click",4,"ngIf"],[1,"search-bar",3,"mouseenter"],["type","text","maxlength","1000","placeholder","Enter Your Prompt Here",3,"ngModel","focus","blur","ngModelChange","keydown.enter","input"],["class","svg-icon",3,"click",4,"ngIf"],[1,"imp-text"],["tooltip","New Chat",1,"svg-icon",3,"click"],["width","24","height","24","viewBox","0 0 24 24","fill","none"],["d","M22 8C22 4 20 2 16 2H8C4 2 2 4 2 8V21C2 21.55 2.45 22 3 22H16C20 22 22 20 22 16V12","stroke","#526179","stroke-width","1.5","stroke-linecap","round","stroke-linejoin","round"],["d","M14.5 12H15.5","stroke","#526179","stroke-width","1.5","stroke-miterlimit","10","stroke-linecap","round","stroke-linejoin","round"],["d","M8.5 12H12","stroke","#526179","stroke-width","1.5","stroke-miterlimit","10","stroke-linecap","round","stroke-linejoin","round"],["d","M12 15.5V8.5","stroke","#526179","stroke-width","1.5","stroke-miterlimit","10","stroke-linecap","round","stroke-linejoin","round"],["d","M22 8C22 4 20 2 16 2H8C4 2 2 4 2 8V21C2 21.55 2.45 22 3 22H16C20 22 22 20 22 16V12","stroke","lightgrey","stroke-width","1.5","stroke-linecap","round","stroke-linejoin","round"],["d","M14.5 12H15.5","stroke","lightgrey","stroke-width","1.5","stroke-miterlimit","10","stroke-linecap","round","stroke-linejoin","round"],["d","M8.5 12H12","stroke","lightgrey","stroke-width","1.5","stroke-miterlimit","10","stroke-linecap","round","stroke-linejoin","round"],["d","M12 15.5V8.5","stroke","lightgrey","stroke-width","1.5","stroke-miterlimit","10","stroke-linecap","round","stroke-linejoin","round"],[1,"overlay",3,"click"],["class","prompt-content","infinite-scroll","",3,"infiniteScrollDistance","infiniteScrollThrottle","scrollWindow","scrolled",4,"ngIf"],["infinite-scroll","",1,"prompt-content",3,"infiniteScrollDistance","infiniteScrollThrottle","scrollWindow","scrolled"],["class","prompt-text",3,"click",4,"ngFor","ngForOf"],[1,"prompt-text",3,"click"],["width","28","height","28","viewBox","0 0 36 36","fill","none"],["width","36","height","36","rx","18","fill","url(#paint0_linear_1555_26595)"],["d","M11.807 21.3475L12.967 19.0275C13.287 18.3808 13.287 17.6275 12.967 16.9808L11.807 14.6541C10.8137 12.6675 12.9537 10.5675 14.9204 11.6075L15.947 12.1541C16.0937 12.2275 16.207 12.3475 16.267 12.4941L20.0604 20.9275C20.2137 21.2741 20.0737 21.6808 19.7404 21.8541L14.9137 24.3941C12.9537 25.4341 10.8137 23.3341 11.807 21.3475Z","fill","white"],["opacity","0.4","d","M20.8738 20.4003L18.3871 14.8803C18.1071 14.2603 18.7738 13.6336 19.3738 13.9536L23.2205 15.9803C24.8538 16.8403 24.8538 19.1736 23.2205 20.0336L21.8605 20.747C21.4938 20.9336 21.0471 20.7803 20.8738 20.4003Z","fill","white"],["id","paint0_linear_1555_26595","x1","36","y1","18","x2","-1.90588","y2","18","gradientUnits","userSpaceOnUse"],["width","36","height","36","rx","18","fill","grey"],[1,"menu-content"],["mat-menu-item","",1,"menu-item",3,"click"],["height","20px","viewBox","0 -960 960 960","width","20px","fill","#a8acb2",1,"menu-icon"],["d","M216-216h51l375-375-51-51-375 375v51Zm-72 72v-153l498-498q11-11 23.84-16 12.83-5 27-5 14.16 0 27.16 5t24 16l51 51q11 11 16 24t5 26.54q0 14.45-5.02 27.54T795-642L297-144H144Zm600-549-51-51 51 51Zm-127.95 76.95L591-642l51 51-25.95-25.05Z"],[1,"menu-divider"],["d","M240-144v-600q0-29.7 21.15-50.85Q282.3-816 312-816h336q29.7 0 50.85 21.15Q720-773.7 720-744v600l-240-96-240 96Zm72-107 168-67 168 67v-493H312v493Zm0-493h336-336Z"],["d","M312-144q-29.7 0-50.85-21.15Q240-186.3 240-216v-480h-48v-72h192v-48h192v48h192v72h-48v479.57Q720-186 698.85-165T648-144H312Zm336-552H312v480h336v-480ZM384-288h72v-336h-72v336Zm120 0h72v-336h-72v336ZM312-696v480-480Z"],["class","menu-icon","height","20px","viewBox","0 -960 960 960","width","20px","fill","#a8acb2",4,"ngIf"],["d","M672-816v72h-48v307l-72-72v-235H408v91l-90-90-30-31v-42h384ZM480-48l-36-36v-228H240v-72l96-96v-42.46L90-768l51-51 678 679-51 51-222-223h-30v228l-36 36ZM342-384h132l-66-66-66 66Zm137-192Zm-71 126Z"],[1,"modules-overlay"],["type","text","placeholder","Search",3,"id","ngModel","ngModelChange"],[1,"modules"],[1,"single-module"],[1,"single-module",3,"ngClass","click"]],template:function(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",0),h["\u0275\u0275template"](1,pe,15,6,"div",1),h["\u0275\u0275elementStart"](2,"div",2),h["\u0275\u0275elementStart"](3,"div",3),h["\u0275\u0275elementStart"](4,"div",4),h["\u0275\u0275template"](5,ge,6,0,"div",5),h["\u0275\u0275template"](6,he,53,0,"div",6),h["\u0275\u0275elementStart"](7,"div"),h["\u0275\u0275element"](8,"img",7),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](9,"div",4),h["\u0275\u0275template"](10,Ce,3,2,"div",8),h["\u0275\u0275elementStart"](11,"div",9),h["\u0275\u0275listener"]("click",(function(){return e.onDialogResizing()})),h["\u0275\u0275template"](12,fe,3,0,"div",10),h["\u0275\u0275template"](13,xe,3,0,"ng-template",null,11,h["\u0275\u0275templateRefExtractor"]),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](15,"div",12),h["\u0275\u0275listener"]("click",(function(){return e.onDialogClose()})),h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275elementStart"](16,"svg",13),h["\u0275\u0275elementStart"](17,"mask",14),h["\u0275\u0275element"](18,"rect",15),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](19,"g",16),h["\u0275\u0275element"](20,"path",17),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275namespaceHTML"](),h["\u0275\u0275elementStart"](21,"div",18),h["\u0275\u0275template"](22,ve,4,2,"ng-container",19),h["\u0275\u0275template"](23,He,4,5,"div",20),h["\u0275\u0275template"](24,Ge,38,24,"div",21),h["\u0275\u0275element"](25,"img",22),h["\u0275\u0275element"](26,"img",23),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](27,qe,13,6,"div",24),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](28,"mat-menu",null,25),h["\u0275\u0275template"](30,tn,20,3,"ng-template",26),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](31,cn,8,10,"ng-template",27,28,h["\u0275\u0275templateRefExtractor"])),2&t){const t=h["\u0275\u0275reference"](14);h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",e.isHistoryVisible),h["\u0275\u0275advance"](4),h["\u0275\u0275property"]("ngIf",!e.isHistoryVisible),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",e.isHistoryVisible),h["\u0275\u0275advance"](2),h["\u0275\u0275property"]("src",null==e.data?null:e.data.aiThemeConfig["AI-ICON"],h["\u0275\u0275sanitizeUrl"]),h["\u0275\u0275advance"](2),h["\u0275\u0275property"]("ngIf",!e.isLoading&&e.promptLibraryData.length>0),h["\u0275\u0275advance"](2),h["\u0275\u0275property"]("ngIf",e.dialogExpanded)("ngIfElse",t),h["\u0275\u0275advance"](9),h["\u0275\u0275property"]("ngStyle",h["\u0275\u0275pureFunction1"](15,Te,"calc(var(--kebsChatbotContentHeight) + 52px)")),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",e.isThreadLoading||e.isLoading),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!e.isThreadLoading&&!e.isLoading&&!e.isPromptLibraryVisible),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!e.isLoading&&e.isPromptLibraryVisible),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("src",null==e.data?null:e.data.aiThemeConfig["CHATBOT-BG-ICON"],h["\u0275\u0275sanitizeUrl"]),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("src",null==e.data?null:e.data.aiThemeConfig["CHATBOT-BG-ICON"],h["\u0275\u0275sanitizeUrl"]),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!e.isThreadLoading&&!e.isLoading),h["\u0275\u0275advance"](4),h["\u0275\u0275property"]("cdkConnectedOverlayOrigin",e.triggerModulesOverlay)}},directives:[_.NgIf,v.a,_.NgStyle,c.g,c.c,i.a,y.e,y.v,y.y,P.a,_.NgForOf,M.a,_.NgClass,O.a,y.q,c.f,b.a,i.b,Bt,jt.a,c.d],pipes:[Ct.a,g.a,Zt.a],styles:[".bg-container[_ngcontent-%COMP%]{display:flex}.history-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:20vw;height:calc(100vh - var(--chatBotAdjustedHeight));border-right:1px solid #e8e9ee;background-color:#f7f9fb}.history-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding:0 18px;height:59px;background:#e6e6e6}.history-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .main-title[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-weight:700;font-size:14px;color:#111434}.history-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .svg-icon[_ngcontent-%COMP%]{cursor:pointer}.history-container[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;gap:8px;border:.5px solid #d4d6d8;border-radius:4px;height:24px;padding-right:4px;margin:12px 16px}.history-container[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]{width:-webkit-fill-available;height:100%;padding:2px 6px}.history-container[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{height:100%;width:100%;font-family:var(--kebsFontFamily);font-size:10px;font-weight:400;color:#45546e;outline:none;border:none;background-color:#f7f9fb}.history-container[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{font-family:var(--kebsFontFamily);font-size:10px;font-weight:400;color:#d4d6d8}.history-container[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]   .svg-icon[_ngcontent-%COMP%]{cursor:pointer}.history-container[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%]{height:12px;width:12px;font-size:12px;color:#7d838b;cursor:pointer}.history-container[_ngcontent-%COMP%]   .no-history[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-size:10px;font-weight:400;color:#7d838b;width:100%;text-align:center}.history-container[_ngcontent-%COMP%]   .loading-state[_ngcontent-%COMP%]{display:flex;flex-direction:column;height:var(--kebsChatbotHistoryContentHeight);overflow:hidden;padding:0 8px 0 16px;margin-right:8px;gap:10px}.history-container[_ngcontent-%COMP%]   .loading-state[_ngcontent-%COMP%]   .loader[_ngcontent-%COMP%]{min-height:24px;width:100%;border-radius:6px;background:linear-gradient(90deg,#cdcdcd -24.18%,#f0f0f0 50.26%,#efefef 114.84%)}.history-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{display:flex;flex-direction:column;height:var(--kebsChatbotHistoryContentHeight);overflow-y:auto;padding:0 8px 0 16px;margin-right:8px;gap:10px}.history-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]{color:#d4d6d8}.history-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .single-date-log[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:4px}.history-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .single-date-log[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-family:var(--kebsFontFamily);font-weight:700;font-size:10px;color:#515965}.history-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .single-date-log[_ngcontent-%COMP%]   .pin-icon[_ngcontent-%COMP%]{font-size:14px;width:14px;height:14px}.history-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .single-date-log[_ngcontent-%COMP%]   .single-history[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;min-height:24px;gap:8px;padding:4px;border:1.5px solid #f7f9fb}.history-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .single-date-log[_ngcontent-%COMP%]   .single-history.thread-selected[_ngcontent-%COMP%]{border-radius:4px;background-color:#e6e6e6;border:1.5px solid #f7f9fb}.history-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .single-date-log[_ngcontent-%COMP%]   .single-history[_ngcontent-%COMP%]   .history-text[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-weight:400;font-size:10px;color:#515965;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;cursor:pointer;width:99%}.history-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .single-date-log[_ngcontent-%COMP%]   .single-history[_ngcontent-%COMP%]   .inline-edit[_ngcontent-%COMP%]{width:100%;height:24px}.history-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .single-date-log[_ngcontent-%COMP%]   .single-history[_ngcontent-%COMP%]   .inline-edit[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{height:100%;width:100%;font-family:var(--kebsFontFamily);font-size:10px;font-weight:400;color:#515965;outline:none;border:.7px solid #1890ff;border-radius:4px}.history-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .single-date-log[_ngcontent-%COMP%]   .single-history[_ngcontent-%COMP%]   .inline-edit[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{font-family:var(--kebsFontFamily);font-size:10px;font-weight:400;color:#6e7b8f}.history-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .single-date-log[_ngcontent-%COMP%]   .single-history[_ngcontent-%COMP%]   .svg-icon[_ngcontent-%COMP%]{cursor:pointer}.history-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .single-date-log[_ngcontent-%COMP%]   .single-history[_ngcontent-%COMP%]:hover{border:1.5px solid #e8e9ee;border-radius:4px}.history-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]::-webkit-scrollbar{width:6px!important;height:6px!important}.history-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{min-height:40px!important;max-height:40px!important}.chatbot-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;min-height:calc(-98px + 100vh);width:var(--kebsChatBotContentWidth);height:calc(-var(--chatBotAdjustedHeight) + 100vh)}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding:16px 24px;background-color:#f3f3f3}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-header[_ngcontent-%COMP%]   .align-items-with-gap[_ngcontent-%COMP%]{display:flex;align-items:center;gap:10px}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-header[_ngcontent-%COMP%]   .align-items-with-gap[_ngcontent-%COMP%]   .svg-icon[_ngcontent-%COMP%]{cursor:pointer}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-header[_ngcontent-%COMP%]   .align-items-with-gap[_ngcontent-%COMP%]   .ai-icon[_ngcontent-%COMP%]{width:60px;height:28px}.chatbot-container[_ngcontent-%COMP%]   .module-header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;font-family:var(--kebsFontFamily);font-size:12px;font-weight:500;color:#fff;text-transform:uppercase;height:24px;background:#5f6c81}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]{height:var(--kebsChatbotContentHeight);padding:12px 16px 12px 24px;overflow-y:auto;position:relative}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;gap:8px;height:calc(var(--kebsChatbotContentHeight) - 40px)}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .gif[_ngcontent-%COMP%]{height:80px;width:80px}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .carousel-text[_ngcontent-%COMP%]{position:relative;height:50px;width:100%;overflow:hidden}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]{width:100%;display:flex;flex-direction:column;align-items:center;justify-content:center;transition:transform .5s ease-in,opacity .5s ease-in;position:absolute;opacity:1;transition:opacity 2s ease}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]   .main-text[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-size:14px;font-weight:400;color:#7d838b}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]   .sub-text[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-size:14px;font-weight:600;color:#ee4961}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .slide[_ngcontent-%COMP%]{animation:slideUp 2s ease forwards}@keyframes slideUp{0%{transform:translateY(100%);opacity:0}20%{transform:translateY(0);opacity:1}80%{transform:translateY(0);opacity:1}to{transform:translateY(-100%);opacity:0}}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:16px;height:calc(var(--kebsChatbotContentHeight) - 24px);overflow-y:auto;overflow-x:hidden;padding-right:1%;position:relative;z-index:1}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .header-content-alignment[_ngcontent-%COMP%]{display:flex;gap:6px;align-items:center}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .header-content-alignment[_ngcontent-%COMP%]   .ai-img-header[_ngcontent-%COMP%]{height:60.12px;width:58.5px}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .align-items-column[_ngcontent-%COMP%]{display:flex;flex-direction:column;row-gap:0}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .align-items-column[_ngcontent-%COMP%]   .main-text[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-weight:600;font-size:16px;color:#ee4961;background:linear-gradient(270deg,#ffc12f -5.06%,#f16567 31.08%,#f16567 100.33%);-webkit-background-clip:text;-webkit-text-fill-color:transparent}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .align-items-column[_ngcontent-%COMP%]   .main-text.header-content[_ngcontent-%COMP%]{font-weight:700;font-size:21px;height:30px}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .align-items-column[_ngcontent-%COMP%]   .sub-text[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-weight:600;font-size:16px;color:#b9c0ca;height:20px}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .align-items-column[_ngcontent-%COMP%]   .sub-text.header-content[_ngcontent-%COMP%]{font-weight:700;font-size:23px}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .predefined-sections[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:10px}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .predefined-sections[_ngcontent-%COMP%]   .module-section[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .predefined-sections[_ngcontent-%COMP%]   .module-section[_ngcontent-%COMP%]   .module-dropdown[_ngcontent-%COMP%]{display:flex;align-items:center;gap:6px;border:1px solid #e8e9ee;border-radius:4px;padding:2px 8px;font-family:var(--kebsFontFamily);font-size:13px;font-weight:400;color:#1b2140;cursor:pointer}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .predefined-sections[_ngcontent-%COMP%]   .module-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-size:14px;font-weight:400;color:#515965}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .predefined-sections[_ngcontent-%COMP%]   .section-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:4px}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .predefined-sections[_ngcontent-%COMP%]   .section-content[_ngcontent-%COMP%]   .section-content-align[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;cursor:pointer;width:-moz-fit-content;width:fit-content}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .predefined-sections[_ngcontent-%COMP%]   .section-content[_ngcontent-%COMP%]   .section-content-align[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{min-width:24px;min-height:24px}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .predefined-sections[_ngcontent-%COMP%]   .section-content[_ngcontent-%COMP%]   .section-content-align[_ngcontent-%COMP%]   .section-content-text[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-size:12px;font-weight:400;color:#1b2140}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-size:14px;font-weight:400;color:#515965}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .features[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:flex-start;width:-moz-fit-content;width:fit-content;gap:12px;position:relative;width:calc(var(--kebsChatBotContentWidth) - 4vw)}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .features[_ngcontent-%COMP%]   .svg-container[_ngcontent-%COMP%]{display:flex;align-items:center}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .features[_ngcontent-%COMP%]   .report-feature[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-around;gap:2%;padding:8px;width:48%;border-radius:4px;border:.5px solid #e8e9ee;background-color:#ffeee8;cursor:pointer}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .features[_ngcontent-%COMP%]   .widget-feature[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-around;gap:2%;padding:8px;width:48%;border-radius:4px;border:.5px solid #e8e9ee;background-color:#f1eafa;cursor:pointer}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .features[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-size:14px;font-weight:500;color:#111434}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .features[_ngcontent-%COMP%]   .feature-widget-class[_ngcontent-%COMP%]{min-width:256px;border-radius:8px;border:2px solid #e8e9ee;display:flex;align-items:center;justify-content:space-between;padding:2px 12px 0;min-height:54px;cursor:pointer;max-width:260px}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .features[_ngcontent-%COMP%]   .feature-widget-class[_ngcontent-%COMP%]   .align-content-left[_ngcontent-%COMP%]{display:flex;width:95%;position:relative;padding-right:8px;gap:10px}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .features[_ngcontent-%COMP%]   .feature-widget-class[_ngcontent-%COMP%]   .feature-widget-text-class[_ngcontent-%COMP%]{display:flex;color:#6e7b8f;font-family:var(--kebsFontFamily);font-weight:400;font-size:10px;row-gap:4px;width:86%;position:relative;flex-direction:column;overflow:hidden;white-space:break-spaces;line-height:normal}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .features[_ngcontent-%COMP%]   .feature-widget-class[_ngcontent-%COMP%]   .feature-widget-text-class[_ngcontent-%COMP%]   .widget-header[_ngcontent-%COMP%]{color:#45546e;font-family:var(--kebsFontFamily);font-weight:600;font-size:12px;text-overflow:ellipsis;max-width:99%;white-space:nowrap;overflow:hidden}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .card-items-wrapper[_ngcontent-%COMP%]{display:flex;overflow-x:hidden;scroll-behavior:smooth;white-space:nowrap;flex-grow:1;gap:12px}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .scroll-button[_ngcontent-%COMP%]{justify-content:center;cursor:pointer;top:50%;transform:translateY(-50%);background-color:#fff;padding:5px;height:54px;display:flex;align-items:center;position:relative;border-radius:8px;border:1.5px solid #e8e9ee;width:28px}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .scroll-right[_ngcontent-%COMP%]{right:0}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .scroll-left[_ngcontent-%COMP%]{left:0}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .scroll-button.disable[_ngcontent-%COMP%]{opacity:.3;pointer-events:none}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .chat-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:24px}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .modules[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;flex-direction:column;gap:20px;height:calc(var(--kebsChatbotContentHeight) - 20px);overflow-y:auto}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .modules[_ngcontent-%COMP%]   .choose-module-text[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-size:16px;font-weight:700;text-align:center}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .modules-content[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;align-items:center;width:75%;gap:12px}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .modules-content[_ngcontent-%COMP%]   .module-chip[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;border:1px solid #b9c0ca;border-radius:4px;padding:4px 8px;cursor:pointer}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .modules-content[_ngcontent-%COMP%]   .module-chip[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{width:18px;height:18px;font-size:18px;color:#b9c0ca}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]   .modules-content[_ngcontent-%COMP%]   .module-chip[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-size:14px;font-weight:400;color:#7d838b}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]::-webkit-scrollbar{width:6px!important;height:6px!important}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{min-height:40px!important;max-height:40px!important}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:16px;height:calc(var(--kebsChatbotContentHeight) + 24px);overflow:hidden;position:relative;z-index:1}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .prompt-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .prompt-title[_ngcontent-%COMP%]   .prompt-title-icon[_ngcontent-%COMP%]{font-size:16px;width:16px;height:16px;color:#45546e;cursor:pointer}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .prompt-title[_ngcontent-%COMP%]   .prompt-title-text[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-size:16px;font-weight:600;color:#ee4961}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px;border:1px solid #b9c0ca;border-radius:60px;width:40%;padding:2px 12px;height:28px}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{height:100%;width:100%;font-family:var(--kebsFontFamily);font-size:12px;font-weight:400;color:#45546e;outline:none;border:none}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{font-family:var(--kebsFontFamily);font-size:12px;font-weight:400;color:#b9c0ca}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{display:flex;align-items:center;width:100%;gap:2%}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .categories[_ngcontent-%COMP%]{display:flex;flex-direction:column;height:calc(var(--kebsChatbotContentHeight) - 20px);border:.5px solid #e8e9ee;border-radius:8px;width:35%;padding:8px;gap:8px}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .categories[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{display:flex;justify-content:start;align-items:center;gap:8px;padding-bottom:8px;border-bottom:.5px solid #dadce2;margin-bottom:8px}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .categories[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .categories-text[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-size:12px;font-weight:400;color:#111434;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;max-width:70%}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .categories[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .count[_ngcontent-%COMP%]{background-color:#45546e;padding:2px 6px;border-radius:10px;font-family:var(--kebsFontFamily);font-size:10px;font-weight:500;color:#fff}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .categories[_ngcontent-%COMP%]   .categories-list[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:8px;height:calc(var(--kebsChatbotContentHeight) - 68px);overflow-y:auto;padding-right:2%}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .categories[_ngcontent-%COMP%]   .categories-list[_ngcontent-%COMP%]   .category-list[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;cursor:pointer;border-radius:4px;padding:4px}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .categories[_ngcontent-%COMP%]   .categories-list[_ngcontent-%COMP%]   .category-list[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-size:12px;font-weight:400;color:#111434;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;max-width:70%}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .categories[_ngcontent-%COMP%]   .categories-list[_ngcontent-%COMP%]   .category-list[_ngcontent-%COMP%]   .count[_ngcontent-%COMP%]{padding:2px 6px;border-radius:10px;font-family:var(--kebsFontFamily);font-size:10px;font-weight:500;color:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%);background:linear-gradient(270deg,rgba(239,74,97,.1),rgba(242,122,108,.1) 105.29%)}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .categories[_ngcontent-%COMP%]   .categories-list[_ngcontent-%COMP%]   .selected-category-list[_ngcontent-%COMP%]{background:linear-gradient(270deg,rgba(239,74,97,.1),rgba(242,122,108,.1) 105.29%)}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .categories[_ngcontent-%COMP%]   .categories-list[_ngcontent-%COMP%]   .selected-category-list[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%]{color:#fff;background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%)}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .categories[_ngcontent-%COMP%]   .categories-list[_ngcontent-%COMP%]::-webkit-scrollbar{width:6px!important;height:6px!important}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .categories[_ngcontent-%COMP%]   .categories-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{min-height:40px!important;max-height:40px!important}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .category-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;height:calc(var(--kebsChatbotContentHeight) - 20px);gap:6px;width:63%;padding-right:1%;overflow-y:auto}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .category-content[_ngcontent-%COMP%]   .single-prompt[_ngcontent-%COMP%]{display:flex;flex-direction:row;align-items:start;justify-content:space-between;gap:16px;border:.5px solid #dadce2;border-radius:4px;padding:8px;cursor:pointer}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .category-content[_ngcontent-%COMP%]   .single-prompt[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-size:10px;font-weight:400;color:#8b95a5;text-align:justify;line-height:1.5}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .category-content[_ngcontent-%COMP%]   .single-prompt[_ngcontent-%COMP%]   .copy[_ngcontent-%COMP%]{height:10px;display:none;align-items:center;justify-content:end;gap:4px;font-family:var(--kebsFontFamily);font-size:8px;font-weight:500;color:#ef4a61}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .category-content[_ngcontent-%COMP%]   .single-prompt[_ngcontent-%COMP%]:hover{border:.5px solid #ef4a61;background:linear-gradient(270deg,rgba(239,74,97,.05),rgba(242,122,108,.05) 105.29%)}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .category-content[_ngcontent-%COMP%]   .single-prompt[_ngcontent-%COMP%]:hover   .copy[_ngcontent-%COMP%]{display:flex}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .category-content[_ngcontent-%COMP%]::-webkit-scrollbar{width:6px!important;height:6px!important}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .category-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{min-height:40px!important;max-height:40px!important}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .category-content-empty-state[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;height:calc(var(--kebsChatbotContentHeight) - 20px);gap:6px;width:63%;padding-right:1%;overflow:hidden;font-family:var(--kebsFontFamily);font-size:14px;font-weight:700;color:#45546e}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .bg-image-1[_ngcontent-%COMP%]{position:absolute;top:0;right:0;height:170px;width:60px}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .bg-image-2[_ngcontent-%COMP%]{position:absolute;bottom:0;left:0;height:170px;width:60px;transform:rotate(180deg)}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-footer[_ngcontent-%COMP%]{display:flex;flex-direction:column;padding:0 24px}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-footer[_ngcontent-%COMP%]   .imp-text[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-size:10px;font-weight:500;color:#b9c0ca;text-align:center}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-footer[_ngcontent-%COMP%]   .align-items-with-gap[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-footer[_ngcontent-%COMP%]   .align-items-with-gap[_ngcontent-%COMP%]   .svg-icon[_ngcontent-%COMP%]{cursor:pointer}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-footer[_ngcontent-%COMP%]   .module-btn[_ngcontent-%COMP%]{background-color:#ffebec;color:#ee4961}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-footer[_ngcontent-%COMP%]   .module-btn[_ngcontent-%COMP%], .chatbot-container[_ngcontent-%COMP%]   .chatbot-container-footer[_ngcontent-%COMP%]   .module-btn-close[_ngcontent-%COMP%]{padding:4px 12px;border:1px solid #ee4961;border-radius:16px;font-family:var(--kebsFontFamily);font-size:14px;font-weight:400;width:-moz-fit-content;width:fit-content;cursor:pointer}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-footer[_ngcontent-%COMP%]   .module-btn-close[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;background-color:#ee4961;color:#fff}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-footer[_ngcontent-%COMP%]   .search-overlay-section[_ngcontent-%COMP%]{display:inline-block;position:relative;width:100%}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-footer[_ngcontent-%COMP%]   .search-overlay-section[_ngcontent-%COMP%]   .overlay[_ngcontent-%COMP%]{color:#515965;align-items:center;max-height:181px;overflow:hidden;padding-top:20px;bottom:20px;left:0;width:100%;background:#fff;border-radius:8px;box-shadow:0 2px 1px 0 rgba(0,0,0,.12156862745098039);border:1px solid #e8e9ee;z-index:5;min-height:62px;overflow-y:auto;display:flex;position:absolute;pointer-events:all!important}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-footer[_ngcontent-%COMP%]   .search-overlay-section[_ngcontent-%COMP%]   .overlay[_ngcontent-%COMP%]   .prompt-content[_ngcontent-%COMP%]{overflow-y:auto;display:flex;flex-direction:column;padding:0 2px 24px 8px;margin-right:2px;gap:4px;max-height:122px;width:100%;position:relative}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-footer[_ngcontent-%COMP%]   .search-overlay-section[_ngcontent-%COMP%]   .overlay[_ngcontent-%COMP%]   .prompt-content[_ngcontent-%COMP%]   .prompt-text[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-weight:400;font-size:12px;padding:6px;border-radius:3px;cursor:pointer;width:100%}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-footer[_ngcontent-%COMP%]   .search-overlay-section[_ngcontent-%COMP%]   .overlay[_ngcontent-%COMP%]   .prompt-content[_ngcontent-%COMP%]   .prompt-text[_ngcontent-%COMP%]:hover{background:#ffecee}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-footer[_ngcontent-%COMP%]   .search-overlay-section[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;gap:8px;border:1px solid #b9c0ca;border-radius:60px;height:36px;width:100%;padding-right:4px;z-index:10;position:relative;background:#fff}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-footer[_ngcontent-%COMP%]   .search-overlay-section[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]{width:-webkit-fill-available;height:100%;padding:4px 8px}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-footer[_ngcontent-%COMP%]   .search-overlay-section[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{height:100%;width:100%;font-family:var(--kebsFontFamily);font-size:12px;font-weight:400;color:#45546e;outline:none;border:none}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-footer[_ngcontent-%COMP%]   .search-overlay-section[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{font-family:var(--kebsFontFamily);font-size:12px;font-weight:400;color:#b9c0ca}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-footer[_ngcontent-%COMP%]   .search-overlay-section[_ngcontent-%COMP%]   .overlay[_ngcontent-%COMP%]::-webkit-scrollbar, .chatbot-container[_ngcontent-%COMP%]   .chatbot-container-footer[_ngcontent-%COMP%]   .search-overlay-section[_ngcontent-%COMP%]   .prompt-content[_ngcontent-%COMP%]::-webkit-scrollbar{width:6px!important;height:6px!important}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-footer[_ngcontent-%COMP%]   .search-overlay-section[_ngcontent-%COMP%]   .overlay[_ngcontent-%COMP%]::-webkit-scrollbar-track, .chatbot-container[_ngcontent-%COMP%]   .chatbot-container-footer[_ngcontent-%COMP%]   .search-overlay-section[_ngcontent-%COMP%]   .prompt-content[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:#f1f1f1;border-radius:3px;height:68px!important}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-footer[_ngcontent-%COMP%]   .search-overlay-section[_ngcontent-%COMP%]   .overlay[_ngcontent-%COMP%]::-webkit-scrollbar-thumb, .chatbot-container[_ngcontent-%COMP%]   .chatbot-container-footer[_ngcontent-%COMP%]   .search-overlay-section[_ngcontent-%COMP%]   .prompt-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#b9c0ca;border-radius:3px;min-height:50px}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-footer[_ngcontent-%COMP%]   .search-overlay-section[_ngcontent-%COMP%]   .overlay[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover, .chatbot-container[_ngcontent-%COMP%]   .chatbot-container-footer[_ngcontent-%COMP%]   .search-overlay-section[_ngcontent-%COMP%]   .prompt-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#999}.chatbot-container[_ngcontent-%COMP%]   .chat-ui-header-section[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{display:flex;gap:2px;font-family:var(--kebsFontFamily);font-weight:700;font-size:16px}.chatbot-container[_ngcontent-%COMP%]   .chat-ui-header-section[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .text-color[_ngcontent-%COMP%]{background:linear-gradient(270deg,#ffc12f -5.06%,#f16567 31.08%,#f16567 100.33%);-webkit-background-clip:text;-webkit-text-fill-color:transparent}.chatbot-container[_ngcontent-%COMP%]   .chat-ui-header-section[_ngcontent-%COMP%]   .header.header-content[_ngcontent-%COMP%]{font-weight:700;font-size:20px;height:30px}.chatbot-container[_ngcontent-%COMP%]   .chat-ui-header-section[_ngcontent-%COMP%]   .sub-header[_ngcontent-%COMP%]{color:#8b95a5;font-family:var(--kebsFontFamily);font-weight:400;font-size:14px}.chatbot-container[_ngcontent-%COMP%]   .chat-ui-header-section[_ngcontent-%COMP%]   .sub-header.header-content[_ngcontent-%COMP%]{font-weight:400;font-size:16px;height:30px}.menu-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;padding:0 12px;gap:4px}.menu-content[_ngcontent-%COMP%]   .menu-item[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-size:10px;font-weight:400;color:#515965;height:20px;display:flex;align-items:center;gap:12px;padding:0}.menu-content[_ngcontent-%COMP%]   .menu-icon[_ngcontent-%COMP%]{font-size:14px;width:14px;height:14px;color:#a8acb2;margin:0}.menu-content[_ngcontent-%COMP%]   .menu-divider[_ngcontent-%COMP%]{color:#e8e9ee}.modules-overlay[_ngcontent-%COMP%]{display:flex;flex-direction:column;border-radius:4px;border:1px solid #e8e9ee;min-width:130px;background:#fff}.modules-overlay[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]{background-color:#f7f9fb;height:28px}.modules-overlay[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{height:100%;width:100%;font-family:var(--kebsFontFamily);font-size:12px;font-weight:400;color:#45546e;outline:none;border:none;background-color:#f7f9fb;padding:0 8px}.modules-overlay[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{font-family:var(--kebsFontFamily);font-size:12px;font-weight:400;color:#d4d6d8}.modules-overlay[_ngcontent-%COMP%]   .modules[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:6px;padding-bottom:6px}.modules-overlay[_ngcontent-%COMP%]   .modules[_ngcontent-%COMP%]   .single-module[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-size:12px;font-weight:400;color:#45546e;cursor:pointer;padding:0 8px}.modules-overlay[_ngcontent-%COMP%]   .modules[_ngcontent-%COMP%]   .selected-module[_ngcontent-%COMP%]{color:#ee4961}.modules-overlay[_ngcontent-%COMP%]   .modules[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]{color:#e8e9ee}"]}),t})()}}]);