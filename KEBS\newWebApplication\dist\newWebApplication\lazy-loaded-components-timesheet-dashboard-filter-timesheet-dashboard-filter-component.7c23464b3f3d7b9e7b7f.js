(window.webpackJsonp=window.webpackJsonp||[]).push([[762,634,858],{NJ67:function(e,t,n){"use strict";n.d(t,"a",(function(){return l}));class l{constructor(){this.disabled=!1}onChange(e){}onTouched(e){}writeValue(e){this.value=e}registerOnChange(e){this.onChange=e}registerOnTouched(e){this.onTouched=e}setDisabledState(e){this.disabled=e}}},"sd8+":function(e,t,n){"use strict";n.r(t),n.d(t,"MONTH_YEAR_DATE_FORMAT",(function(){return Y})),n.d(t,"TimesheetDashboardFilterComponent",(function(){return _}));var l=n("3Pt+"),r=n("0IaG"),i=n("wd/R"),a=n("FKr1"),o=n("1yaQ"),s=n("fXoL"),d=n("NFeN"),c=n("Qu3c"),f=n("ofXK"),m=n("bTqV"),p=n("jtHE"),h=n("XNiG"),u=n("1G5W"),g=n("NJ67"),b=n("kmnG"),v=n("d3UM"),C=n("WJ5W");const y=["allSelected"],x=["singleSelect"];function M(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"mat-option",5),s["\u0275\u0275text"](1),s["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;s["\u0275\u0275property"]("value",e.id),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let w=(()=>{class e extends g.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new l.j,this.fieldFilterCtrl=new l.j,this.list=[],this.required=!1,this.valueChange=new s.EventEmitter,this.disabled=!1,this.filteredList=new p.a,this._onDestroy=new h.b}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(u.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(u.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}toggleAllSelection(){this.allSelected.selected&&this.fieldCtrl.patchValue([])}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275directiveInject"](s.Renderer2))},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-multi-select-filter"]],viewQuery:function(e,t){if(1&e&&(s["\u0275\u0275viewQuery"](y,!0),s["\u0275\u0275viewQuery"](x,!0)),2&e){let e;s["\u0275\u0275queryRefresh"](e=s["\u0275\u0275loadQuery"]())&&(t.allSelected=e.first),s["\u0275\u0275queryRefresh"](e=s["\u0275\u0275loadQuery"]())&&(t.singleSelect=e.first)}},inputs:{list:"list",placeholder:"placeholder",required:"required",disabled:"disabled"},outputs:{valueChange:"valueChange"},features:[s["\u0275\u0275ProvidersFeature"]([{provide:l.t,useExisting:Object(s.forwardRef)(()=>e),multi:!0}]),s["\u0275\u0275InheritDefinitionFeature"],s["\u0275\u0275NgOnChangesFeature"]],decls:9,vars:10,consts:[["appearance","outline",2,"width","100%"],["multiple","",3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value",4,"ngFor","ngForOf"],[3,"value"]],template:function(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"mat-form-field",0),s["\u0275\u0275elementStart"](1,"mat-label"),s["\u0275\u0275text"](2),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](3,"mat-select",1,2),s["\u0275\u0275elementStart"](5,"mat-option"),s["\u0275\u0275element"](6,"ngx-mat-select-search",3),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](7,M,2,2,"mat-option",4),s["\u0275\u0275pipe"](8,"async"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e&&(s["\u0275\u0275advance"](2),s["\u0275\u0275textInterpolate"](t.placeholder),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngForOf",s["\u0275\u0275pipeBind1"](8,8,t.filteredList)))},directives:[b.c,b.g,v.c,l.v,l.k,l.F,a.p,C.a,f.NgForOf],pipes:[f.AsyncPipe],styles:[""]}),e})();var S=n("qFsG"),D=n("iadO");function F(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"div"),s["\u0275\u0275elementStart"](1,"label",23),s["\u0275\u0275text"](2),s["\u0275\u0275elementEnd"](),s["\u0275\u0275element"](3,"app-multi-select-filter",24),s["\u0275\u0275elementEnd"]()),2&e){const e=s["\u0275\u0275nextContext"]().$implicit;s["\u0275\u0275advance"](2),s["\u0275\u0275textInterpolate"](e.filterLabel),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("formControlName",e.formControlName)("list",e.filterData)("placeholder",e.filterLabel)}}function E(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"div",25),s["\u0275\u0275elementStart"](1,"label",23),s["\u0275\u0275text"](2),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](3,"mat-form-field",26),s["\u0275\u0275elementStart"](4,"mat-label"),s["\u0275\u0275text"](5),s["\u0275\u0275elementEnd"](),s["\u0275\u0275element"](6,"input",27),s["\u0275\u0275element"](7,"mat-datepicker-toggle",28),s["\u0275\u0275elementStart"](8,"mat-datepicker",29,30),s["\u0275\u0275listener"]("monthSelected",(function(t){s["\u0275\u0275restoreView"](e);const n=s["\u0275\u0275reference"](9);return s["\u0275\u0275nextContext"](2).setMonthAndYear(t,n)})),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275reference"](9),t=s["\u0275\u0275nextContext"]().$implicit,n=s["\u0275\u0275nextContext"]();s["\u0275\u0275advance"](2),s["\u0275\u0275textInterpolate"](t.filterLabel),s["\u0275\u0275advance"](3),s["\u0275\u0275textInterpolate"](t.filterName),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("matDatepicker",e)("formControl",n.dateControl),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("for",e)}}function O(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"div",20),s["\u0275\u0275template"](1,F,4,4,"div",21),s["\u0275\u0275template"](2,E,10,5,"div",22),s["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;s["\u0275\u0275property"]("ngClass",13==e.id?"col-3 p-0 mr-2":"col-2 p-0 mr-2"),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf","Duration"!=e.filterName),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",13==e.id)}}const Y={parse:{dateInput:"MMMM YYYY"},display:{dateInput:"MMMM YYYY",monthYearLabel:"MMMM YYYY",dateA11yLabel:"LL",monthYearA11yLabel:"MMMM YYYY"}};let _=(()=>{class e{constructor(e,t,n){this.dialogRef=e,this.data=t,this.dialog=n,this.filterData=[],this.filterForm=[],this.dateControl=new l.j(i())}ngOnInit(){this.filterData=this.data.filterData,this.filterForm=this.data.formData,this.dateControl=new l.j(i(this.filterForm.get("dateRange").value.startDate))}closeForm(){this.dialog.closeAll()}onKeyDownMonthSelection(e){e.preventDefault()}setMonthAndYear(e,t){const n=this.dateControl.value;n.month(e.month()),n.year(e.year()),this.dateControl.setValue(n),this.selectedDate=i(this.dateControl.value).format("YYYY-MM-DD"),this.filterForm.get("dateRange").setValue({startDate:i(this.selectedDate).startOf("month").format("DD-MMM-YYYY"),endDate:i(this.selectedDate).endOf("month").format("DD-MMM-YYYY")}),t.close()}applyTimesheetDashboardFilters(){this.dialogRef.close({filterForm:this.filterForm,filterData:this.filterData,ops:1})}clearTimesheetDashboardFilters(){this.dialogRef.close({filterForm:this.filterForm,filterData:this.filterData,ops:2})}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275directiveInject"](r.h),s["\u0275\u0275directiveInject"](r.a),s["\u0275\u0275directiveInject"](r.b))},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-timesheet-dashboard-filter"]],features:[s["\u0275\u0275ProvidersFeature"]([{provide:a.c,useClass:o.c,deps:[a.f,o.a]},{provide:a.e,useValue:Y}])],decls:26,vars:2,consts:[[1,"fluid-container","dashboard-filter-style"],[1,"row","p-0"],[1,"col-12","p-2"],[1,"row","border-bottom","solid"],[1,"col-11","pt-2","pb-2","pl-0","d-flex"],[1,"headerText","my-auto","ml-2"],[1,"col-1","d-flex","pt-2","pb-2"],["matTooltip","Close",1,"view-button-inactive",3,"click"],[1,"col-12","row","p-0"],[1,"col-12","p-2","row","filterRow","p-0",3,"formGroup"],[3,"ngClass",4,"ngFor","ngForOf"],[1,"col-12"],[1,"row"],[1,"col-8"],[1,"col-1","mr-4"],["mat-flat-button","",1,"clear-btn","pt-2",3,"click"],[2,"color","#000000","font-size","17px","line-height","20px"],[1,"col-2"],["mat-flat-button","",1,"apply-btn","pt-2",3,"click"],[2,"color","#ffffff","font-size","17px","line-height","20px"],[3,"ngClass"],[4,"ngIf"],["style","display: grid",4,"ngIf"],[1,"label-text"],[3,"formControlName","list","placeholder"],[2,"display","grid"],["appearance","outline"],["matInput","","placeholder","MMM-YYYY","readonly","",3,"matDatepicker","formControl"],["matIconSuffix","",3,"for"],["startView","multi-year",3,"monthSelected"],["dp",""]],template:function(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"div",0),s["\u0275\u0275elementStart"](1,"div",1),s["\u0275\u0275elementStart"](2,"div",2),s["\u0275\u0275elementStart"](3,"div",3),s["\u0275\u0275elementStart"](4,"div",4),s["\u0275\u0275elementStart"](5,"span",5),s["\u0275\u0275text"](6,"Filters"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](7,"div",6),s["\u0275\u0275elementStart"](8,"mat-icon",7),s["\u0275\u0275listener"]("click",(function(){return t.closeForm()})),s["\u0275\u0275text"](9," close "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](10,"div",8),s["\u0275\u0275elementStart"](11,"form",9),s["\u0275\u0275template"](12,O,3,3,"div",10),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](13,"div",11),s["\u0275\u0275elementStart"](14,"div",12),s["\u0275\u0275element"](15,"div",13),s["\u0275\u0275elementStart"](16,"div",14),s["\u0275\u0275elementStart"](17,"button",15),s["\u0275\u0275listener"]("click",(function(){return t.clearTimesheetDashboardFilters()})),s["\u0275\u0275elementStart"](18,"mat-icon",16),s["\u0275\u0275text"](19,"delete"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275text"](20,"Clear "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](21,"div",17),s["\u0275\u0275elementStart"](22,"button",18),s["\u0275\u0275listener"]("click",(function(){return t.applyTimesheetDashboardFilters()})),s["\u0275\u0275elementStart"](23,"mat-icon",19),s["\u0275\u0275text"](24,"done_all"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275text"](25,"Apply "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e&&(s["\u0275\u0275advance"](11),s["\u0275\u0275property"]("formGroup",t.filterForm),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngForOf",t.filterData))},directives:[d.a,c.a,l.J,l.w,l.n,f.NgForOf,m.a,f.NgClass,f.NgIf,w,l.v,l.l,b.c,b.g,S.b,D.g,l.e,l.k,D.i,D.f],styles:[".dashboard-filter-style[_ngcontent-%COMP%]   .headerText[_ngcontent-%COMP%]{color:#000;font-family:Roboto;font-weight:700;font-size:14px;line-height:16px}.dashboard-filter-style[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]{color:#45546e;font-size:13px;font-weight:bolder;cursor:pointer}.dashboard-filter-style[_ngcontent-%COMP%]     .mat-form-field-infix{display:flex;height:40px}.dashboard-filter-style[_ngcontent-%COMP%]     .mat-icon-button{bottom:15px}.dashboard-filter-style[_ngcontent-%COMP%]   .apply-btn[_ngcontent-%COMP%]{background-color:#ee4961;color:#fff;border:1px solid #ee4961;border-radius:4px}.dashboard-filter-style[_ngcontent-%COMP%]   .apply-btn[_ngcontent-%COMP%], .dashboard-filter-style[_ngcontent-%COMP%]   .clear-btn[_ngcontent-%COMP%]{font-family:Roboto;font-weight:700;font-size:14px;line-height:16px;width:90px;height:40px}.dashboard-filter-style[_ngcontent-%COMP%]   .clear-btn[_ngcontent-%COMP%]{background-color:#f7f9fb;color:#000;border-radius:4px;border:1px solid #f7f9fb}.dashboard-filter-style[_ngcontent-%COMP%]   .label-text[_ngcontent-%COMP%]{font-family:Roboto;font-weight:500;font-size:12px;line-height:16px;color:rgba(0,0,0,.5019607843137255)}"]}),e})()}}]);