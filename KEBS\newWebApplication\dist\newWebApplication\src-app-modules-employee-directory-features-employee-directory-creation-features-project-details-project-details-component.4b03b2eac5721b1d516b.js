(window.webpackJsonp=window.webpackJsonp||[]).push([[953,267,535,631,634,853,858],{"8SgF":function(e,t,n){"use strict";n.d(t,"a",(function(){return C}));var i=n("mrSG"),a=n("fXoL"),l=n("XNiG"),r=n("Kj3r"),o=n("1G5W"),s=n("3Pt+"),d=n("NJ67"),c=n("F97M"),p=n("XVR1"),m=n("kmnG"),u=n("ofXK"),h=n("qFsG"),f=n("/1cH"),g=n("NFeN"),v=n("FKr1");function b(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"mat-label"),a["\u0275\u0275text"](1),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"]();a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate"](e.label)}}function y(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"mat-option",7),a["\u0275\u0275elementStart"](1,"small"),a["\u0275\u0275text"](2),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=a["\u0275\u0275nextContext"]();a["\u0275\u0275property"]("hidden",!n.isAutocomplete)("value",e),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate2"]("",e.displayName," | ",e.mail,"")}}let C=(()=>{class e extends d.a{constructor(e,t){super(),this.graphApi=e,this._AppShareService=t,this.userSearchSubject=new l.b,this.isAutocomplete=!1,this.Formwidth="",this.showUserList=new a.EventEmitter,this.selectedUser=new a.EventEmitter,this.label="",this.blur=new a.EventEmitter,this.required=!1,this.fieldCtrl=new s.j,this.disabled=!1,this.readonly=!1,this.isGraphApi=0,this.optClicked=!1,this._onDestroy=new l.b}ngOnInit(){this.userSearchSubject.pipe(Object(r.a)(600)).subscribe(e=>Object(i.c)(this,void 0,void 0,(function*(){if(console.log(e),this.isGraphApi)this.graphApi.getUserSuggestions(e).then(t=>Object(i.c)(this,void 0,void 0,(function*(){if(t&&t.length>0)this.showUserList.emit(t);else{let t=yield this.getUserSuggestionsFromDB(e);this.showUserList.emit(t)}})),t=>Object(i.c)(this,void 0,void 0,(function*(){console.log(t);let n=yield this.getUserSuggestionsFromDB(e);this.showUserList.emit(n)})));else{let t=yield this.getUserSuggestionsFromDB(e);this.showUserList.emit(t)}}))),this.fieldCtrl.valueChanges.pipe(Object(o.a)(this._onDestroy)).subscribe(e=>{console.log(e),e?(this.value=e.id,this.onChange(e.id)):(this.value="",this.onChange(""))})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}ngAfterViewInit(){}searchUser(e){if(this.text=e.target.value,this.optClicked="Enter"==e.key,!this.text)return this.graphApi.userSuggestions=[];this.userSearchSubject.next(this.text)}resetSuggestion(){this.graphApi.userSuggestions=[]}checkAndClearInput(){this.optClicked||0!=this.readonly||this.fieldCtrl.setValue("")}selectedOption(e){this.optClicked=!0,this.selectedUser.emit(e.option.value),this.value=e.option.value,this.blur.emit()}displayUserName(e){return e?e.displayName:""}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){return Object(i.c)(this,void 0,void 0,(function*(){if(console.log("inisde writevalue"),console.log(e),e)if(this.isGraphApi)this.graphApi.getUserProfile(e).then(t=>Object(i.c)(this,void 0,void 0,(function*(){if(t)this.fieldCtrl.setValue(t);else{let t=yield this.getUserProfileFromDB(e);this.fieldCtrl.setValue(t)}})),t=>Object(i.c)(this,void 0,void 0,(function*(){let t=yield this.getUserProfileFromDB(e);this.fieldCtrl.setValue(t)})));else{let t=yield this.getUserProfileFromDB(e);console.log(t),this.fieldCtrl.setValue(t)}else this.fieldCtrl.setValue("")}))}getUserSuggestionsFromDB(e){return Object(i.c)(this,void 0,void 0,(function*(){let t=yield this._AppShareService.getUserSuggestionsFromDB(e);return this.graphApi.userSuggestions=t.value,t.value}))}getUserProfileFromDB(e){return Object(i.c)(this,void 0,void 0,(function*(){let t=yield this._AppShareService.getUserProfileFromDB(e);return console.log(t),t&&t.length>0?t[0]:""}))}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275directiveInject"](c.a),a["\u0275\u0275directiveInject"](p.a))},e.\u0275cmp=a["\u0275\u0275defineComponent"]({type:e,selectors:[["app-search-user"]],inputs:{isAutocomplete:"isAutocomplete",Formwidth:"Formwidth",label:"label",required:"required",disabled:"disabled",readonly:"readonly",isGraphApi:"isGraphApi"},outputs:{showUserList:"showUserList",selectedUser:"selectedUser",blur:"blur"},features:[a["\u0275\u0275ProvidersFeature"]([{provide:s.t,useExisting:Object(a.forwardRef)(()=>e),multi:!0}]),a["\u0275\u0275InheritDefinitionFeature"]],decls:9,vars:10,consts:[["appearance","outline","width","Formwidth"],[4,"ngIf"],["matInput","",3,"matAutocomplete","placeholder","required","formControl","disabled","readonly","keyup","focus","focusout"],["matSuffix","",2,"color","#66615B !important","font-size","21px !important"],[3,"hidden","displayWith","optionSelected"],["auto","matAutocomplete"],[3,"hidden","value",4,"ngFor","ngForOf"],[3,"hidden","value"]],template:function(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div"),a["\u0275\u0275elementStart"](1,"mat-form-field",0),a["\u0275\u0275template"](2,b,2,1,"mat-label",1),a["\u0275\u0275elementStart"](3,"input",2),a["\u0275\u0275listener"]("keyup",(function(e){return t.searchUser(e)}))("focus",(function(){return t.resetSuggestion()}))("focusout",(function(){return t.checkAndClearInput()})),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](4,"mat-icon",3),a["\u0275\u0275text"](5,"person_pin"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](6,"mat-autocomplete",4,5),a["\u0275\u0275listener"]("optionSelected",(function(e){return t.selectedOption(e)})),a["\u0275\u0275template"](8,y,3,4,"mat-option",6),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275reference"](7);a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("ngIf",t.label),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("matAutocomplete",e)("placeholder",t.label)("required",t.required)("formControl",t.fieldCtrl)("disabled",t.disabled)("readonly",t.readonly),a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("hidden",!t.isAutocomplete)("displayWith",t.displayUserName),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("ngForOf",t.graphApi.userSuggestions)}},directives:[m.c,u.NgIf,h.b,f.d,s.e,s.F,s.v,s.k,g.a,m.i,f.b,u.NgForOf,m.g,v.p],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding-bottom:8px!important}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-underline{bottom:0}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-infix{padding:.65em 0!important;border-top:.54375em solid transparent!important;font-size:13px}.mat-form-field[_ngcontent-%COMP%]     .mat-select-arrow{margin:0 4px!important}"]}),e})()},NJ67:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));class i{constructor(){this.disabled=!1}onChange(e){}onTouched(e){}writeValue(e){this.value=e}registerOnChange(e){this.onChange=e}registerOnTouched(e){this.onTouched=e}setDisabledState(e){this.disabled=e}}},"TmG/":function(e,t,n){"use strict";n.d(t,"a",(function(){return b}));var i=n("fXoL"),a=n("3Pt+"),l=n("jtHE"),r=n("XNiG"),o=n("NJ67"),s=n("1G5W"),d=n("kmnG"),c=n("ofXK"),p=n("d3UM"),m=n("FKr1"),u=n("WJ5W"),h=n("Qu3c");function f(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-label"),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](e.placeholder)}}function g(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-option",7),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275property"]("value",null),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](e.disableNone?"Select One":"None")}}function v(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"mat-option",8),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const n=t.$implicit;return i["\u0275\u0275nextContext"]().emitChanges(n)})),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;i["\u0275\u0275property"]("value",e.id||e._id)("matTooltip",e.name),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let b=(()=>{class e extends o.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new a.j,this.fieldFilterCtrl=new a.j,this.list=[],this.required=!1,this.disableNone=!1,this.valueChange=new i.EventEmitter,this.disabled=!1,this.hasNoneOption=!0,this.filteredList=new l.a,this.change=new i.EventEmitter,this.hideMatLabel=!1,this._onDestroy=new r.b,this.emitChanges=e=>{this.change.emit(e)}}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(s.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(s.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](i.Renderer2))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search"]],inputs:{list:"list",placeholder:"placeholder",required:"required",disableNone:"disableNone",disabled:"disabled",hasNoneOption:"hasNoneOption",hideMatLabel:"hideMatLabel"},outputs:{valueChange:"valueChange",change:"change"},features:[i["\u0275\u0275ProvidersFeature"]([{provide:a.t,useExisting:Object(i.forwardRef)(()=>e),multi:!0}]),i["\u0275\u0275InheritDefinitionFeature"],i["\u0275\u0275NgOnChangesFeature"]],decls:9,vars:11,consts:[["appearance","outline"],[4,"ngIf"],[3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value",4,"ngIf"],[3,"value","matTooltip","click",4,"ngFor","ngForOf"],[3,"value"],[3,"value","matTooltip","click"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"mat-form-field",0),i["\u0275\u0275template"](1,f,2,1,"mat-label",1),i["\u0275\u0275elementStart"](2,"mat-select",2,3),i["\u0275\u0275elementStart"](4,"mat-option"),i["\u0275\u0275element"](5,"ngx-mat-select-search",4),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](6,g,2,2,"mat-option",5),i["\u0275\u0275template"](7,v,2,3,"mat-option",6),i["\u0275\u0275pipe"](8,"async"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",!t.hideMatLabel),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.hasNoneOption),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngForOf",i["\u0275\u0275pipeBind1"](8,9,t.filteredList)))},directives:[d.c,c.NgIf,p.c,a.v,a.k,a.F,m.p,u.a,c.NgForOf,d.g,h.a],pipes:[c.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%}"]}),e})()},qFYv:function(e,t,n){"use strict";n.d(t,"a",(function(){return F}));var i=n("fXoL"),a=n("tk/3"),l=n("XNiG"),r=n("3Pt+"),o=n("NJ67"),s=n("1G5W"),d=n("Kj3r"),c=n("XXEo"),p=n("kmnG"),m=n("ofXK"),u=n("qFsG"),h=n("/1cH"),f=n("bTqV"),g=n("NFeN"),v=n("Qu3c"),b=n("FKr1");function y(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",7),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"]().checkAndClearInput(!0)})),i["\u0275\u0275elementStart"](1,"mat-icon",8),i["\u0275\u0275text"](2," close "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}}function C(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"mat-option",9),i["\u0275\u0275elementStart"](1,"div",10),i["\u0275\u0275element"](2,"div"),i["\u0275\u0275element"](3,"div"),i["\u0275\u0275element"](4,"div"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]())}function x(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"mat-option",12),i["\u0275\u0275listener"]("onSelectionChange",(function(){i["\u0275\u0275restoreView"](e);const n=t.$implicit;return i["\u0275\u0275nextContext"](2).resultClicked(n)})),i["\u0275\u0275elementStart"](1,"span"),i["\u0275\u0275elementStart"](2,"small",13),i["\u0275\u0275text"](3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](2),i["\u0275\u0275classMap"](n.ngClasses),i["\u0275\u0275propertyInterpolate3"]("matTooltip","",e[n.optionLabel[0]],"",e[n.optionLabel[1]]?" - ":"","",e[n.optionLabel[1]]?e[n.optionLabel[1]]:"",""),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate3"]("",e[n.optionLabel[0]],"",e[n.optionLabel[1]]?" - ":"","",e[n.optionLabel[1]]?e[n.optionLabel[1]]:"","")}}function S(e,t){if(1&e&&(i["\u0275\u0275elementContainerStart"](0),i["\u0275\u0275template"](1,x,4,9,"mat-option",11),i["\u0275\u0275elementContainerEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngForOf",e.searchResult)}}let F=(()=>{class e extends o.a{constructor(e,t){super(),this.http=e,this._login=t,this.label="",this.placeholder="",this.isRequired=!1,this.isReadOnly=!1,this.emitVal=new i.EventEmitter,this.optClicked=!1,this.searchTextControl=new r.j,this.searchResult=[],this.isLoading=!1,this.ifPlaceholderExists=!1,this._onDestroy=new l.b,this.resultClicked=e=>{setTimeout(()=>{this.optClicked=!0,this.outputParam?(this.onChange(e[this.outputParam]),this.emitVal.emit(e[this.outputParam])):(this.onChange(JSON.stringify(e)),this.emitVal.emit(JSON.stringify(e))),setTimeout(()=>{document.getElementById("searchTextInput").blur()},100),this.searchTextControl.patchValue(e[this.optionLabel[0]]+(this.optionLabel.length>1?" - "+e[this.optionLabel[1]]:""))},100)}}ngOnInit(){""!=this.placeholder&&""==this.label&&(this.label=this.placeholder,this.ifPlaceholderExists=!0),this.searchTxtControlSubscription=this.searchTextControl.valueChanges.pipe(Object(s.a)(this._onDestroy),Object(d.a)(700)).subscribe(e=>{e&&e.length>0&&this.callApiFn(e)})}callInitApi(){null==this.searchTextControl.value&&0==this.searchResult.length&&this.callApiFn("")}callApiFn(e){this.isLoading=!0;let t={searchParameter:e,options:this.bodyParams},n={headers:new a.f({Authorization:"Bearer "+this._login.getToken()})};this.http.post(this.apiUri,t,n).pipe(Object(s.a)(this._onDestroy)).subscribe(e=>{this.isLoading=!1,this.searchResult=e},e=>{console.error(e)})}checkAndClearInput(e){this.optClicked&&!e||0!=this.isReadOnly||(this.searchTextControl.reset(),this.onChange(null),this.emitVal.emit(null)),e&&setTimeout(()=>{document.getElementById("searchTextInput").blur(),setTimeout(()=>{document.getElementById("searchTextInput").focus()},100)},100)}writeValue(e){if(e){let t=JSON.parse(e);this.searchTextControl.patchValue(t[this.optionLabel[0]]+(this.optionLabel.length>1?" - "+t[this.optionLabel[1]]:""))}else this.searchTextControl.patchValue("")}resetSuggestion(){this.searchTextControl.patchValue("")}ngOnDestroy(){this.searchTxtControlSubscription&&this.searchTxtControlSubscription.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](a.c),i["\u0275\u0275directiveInject"](c.a))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search-huge-input"]],inputs:{apiUri:"apiUri",bodyParams:"bodyParams",outputParam:"outputParam",optionLabel:"optionLabel",optionValue:"optionValue",label:"label",placeholder:"placeholder",ngClasses:"ngClasses",isRequired:"isRequired",isReadOnly:"isReadOnly"},outputs:{emitVal:"emitVal"},features:[i["\u0275\u0275ProvidersFeature"]([{provide:r.t,useExisting:Object(i.forwardRef)(()=>e),multi:!0}]),i["\u0275\u0275InheritDefinitionFeature"]],decls:9,vars:11,consts:[["appearance","outline",3,"ngClass"],["id","searchTextInput","matInput","","autocomplete","off",3,"placeholder","matAutocomplete","formControl","required","readonly","focusout","focusin","keyup"],["mat-button","","matSuffix","","mat-icon-button","","style","height: 30px; width: 30px; line-height: 1",3,"click",4,"ngIf"],[3,"displayWith"],["auto","matAutocomplete"],["class","is-loading",4,"ngIf"],[4,"ngIf"],["mat-button","","matSuffix","","mat-icon-button","",2,"height","30px","width","30px","line-height","1",3,"click"],["matTooltip","Clear",2,"font-size","20px !important","color","#66615b !important"],[1,"is-loading"],[1,"lds-facebook"],[3,"onSelectionChange",4,"ngFor","ngForOf"],[3,"onSelectionChange"],[2,"padding-left","12px",3,"matTooltip"]],template:function(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-form-field",0),i["\u0275\u0275elementStart"](1,"mat-label"),i["\u0275\u0275text"](2),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](3,"input",1),i["\u0275\u0275listener"]("focusout",(function(){return t.checkAndClearInput(!1)}))("focusin",(function(){return t.callInitApi()}))("keyup",(function(){return t.optClicked=!1})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](4,y,3,0,"button",2),i["\u0275\u0275elementStart"](5,"mat-autocomplete",3,4),i["\u0275\u0275template"](7,C,5,0,"mat-option",5),i["\u0275\u0275template"](8,S,2,1,"ng-container",6),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275reference"](6);i["\u0275\u0275property"]("ngClass",t.ifPlaceholderExists?"label-no-float":""),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate"](t.label),i["\u0275\u0275advance"](1),i["\u0275\u0275propertyInterpolate"]("placeholder",t.label),i["\u0275\u0275property"]("matAutocomplete",e)("formControl",t.searchTextControl)("required",t.isRequired)("readonly",t.isReadOnly),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null!=t.searchTextControl.value),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("displayWith",t.displayFn),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf",t.isLoading),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",0==t.isLoading)}},directives:[p.c,m.NgClass,p.g,u.b,h.d,r.e,r.v,r.k,r.F,m.NgIf,h.b,f.a,p.i,g.a,v.a,b.p,m.NgForOf],styles:[".label-no-float.mat-form-field-appearance-outline.mat-form-field-should-float .mat-form-field-label{display:none}  .label-no-float.mat-form-field-appearance-outline.mat-form-field-should-float .mat-form-field-outline-gap{border-top-color:initial}.mat-form-field[_ngcontent-%COMP%]{width:100%}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding-bottom:8px!important}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-underline{bottom:0}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-infix{padding:.65em 0!important;border-top:.54375em solid transparent!important;font-size:13px}.mat-form-field[_ngcontent-%COMP%]     .mat-select-arrow{margin:0 4px!important}.lds-facebook[_ngcontent-%COMP%]{display:inline-block;position:sticky;width:80px;height:24px;margin-left:110px}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{display:inline-block;position:absolute;left:4px;width:5px;background:#cf0001;animation:lds-facebook 1.2s cubic-bezier(0,.5,.5,1) infinite}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:first-child{left:8px;animation-delay:-.24s}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:nth-child(2){left:16px;animation-delay:-.12s}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:nth-child(3){left:24px;animation-delay:0}@keyframes lds-facebook{0%{top:8px;height:32px}50%,to{top:12px;height:16px}}"]}),e})()},tqTS:function(e,t,n){"use strict";n.r(t),n.d(t,"ProjectDetailsComponent",(function(){return me})),n.d(t,"ProjectDetailsModule",(function(){return ue}));var i=n("mrSG"),a=n("fXoL"),l=n("xG9w"),r=n("wd/R"),o=n("0IaG"),s=n("PSD3"),d=n.n(s),c=n("FKr1"),p=n("1yaQ"),m=n("ofXK"),u=n("Xi0T"),h=n("kmnG"),f=n("qFsG"),g=n("3Pt+"),v=n("iadO"),b=n("NFeN"),y=n("bTqV"),C=n("Xa2L"),x=n("Qu3c"),S=n("1jcm"),F=n("33Jv"),j=n("f0Cb"),E=n("QyBZ"),P=n("QibW"),D=n("jAlA"),w=n("1A3m"),I=n("qFYv"),O=n("fbGU"),M=n("TmG/"),_=n("8SgF");function L(e,t){1&e&&(a["\u0275\u0275elementContainerStart"](0),a["\u0275\u0275elementStart"](1,"div",2),a["\u0275\u0275element"](2,"mat-spinner",3),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementContainerEnd"]())}function A(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"span",19),a["\u0275\u0275text"](1," \xa0*"),a["\u0275\u0275elementEnd"]())}function k(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"div",17),a["\u0275\u0275elementStart"](1,"div",18),a["\u0275\u0275text"](2," Project Role "),a["\u0275\u0275template"](3,A,2,0,"span",36),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](4,"div",16),a["\u0275\u0275elementStart"](5,"app-input-search",37),a["\u0275\u0275listener"]("change",(function(){a["\u0275\u0275restoreView"](e);const t=a["\u0275\u0275nextContext"]().index;return a["\u0275\u0275nextContext"](2).handleProjectRoleChange(t)}))("keyup",(function(){a["\u0275\u0275restoreView"](e);const t=a["\u0275\u0275nextContext"]().index;return a["\u0275\u0275nextContext"](2).handleProjectRoleChange(t)})),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()}if(2&e){const e=a["\u0275\u0275nextContext"](3);a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("ngIf",null==e.tenantFields||null==e.tenantFields.projectRole?null:e.tenantFields.projectRole.isMandatory),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("required",null==e.tenantFields||null==e.tenantFields.projectRole?null:e.tenantFields.projectRole.isMandatory)("list",e.projectRoleMaster)("disableNone",!0)}}function N(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"span",19),a["\u0275\u0275text"](1," \xa0*"),a["\u0275\u0275elementEnd"]())}function T(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",17),a["\u0275\u0275elementStart"](1,"div",18),a["\u0275\u0275text"](2," Project Location "),a["\u0275\u0275template"](3,N,2,0,"span",36),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](4,"div",16),a["\u0275\u0275element"](5,"app-input-search",38),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"](3);a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("ngIf",null==e.tenantFields||null==e.tenantFields.projectLocation?null:e.tenantFields.projectLocation.isMandatory),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("required",null==e.tenantFields||null==e.tenantFields.projectLocation?null:e.tenantFields.projectLocation.isMandatory)("list",e.projectLocationTypeMaster)("disableNone",!0)}}function R(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",18),a["\u0275\u0275text"](1),a["\u0275\u0275elementStart"](2,"span",19),a["\u0275\u0275text"](3," \xa0*"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"](3);a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate1"](" ",e.tenantFieldsLable.splitPercentage.fieldLable,"")}}function V(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"div",18),a["\u0275\u0275text"](1," Split Percentage"),a["\u0275\u0275elementStart"](2,"span",19),a["\u0275\u0275text"](3," \xa0*"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]())}function q(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"span",19),a["\u0275\u0275text"](1," \xa0*"),a["\u0275\u0275elementEnd"]())}function G(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",18),a["\u0275\u0275text"](1),a["\u0275\u0275template"](2,q,2,0,"span",36),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"](3);a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate1"](" ",e.tenantFieldsLable.costCenter.fieldLable,""),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",null==e.tenantFields||null==e.tenantFields.costCenterDesc?null:e.tenantFields.costCenterDesc.isMandatory)}}function U(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"span",19),a["\u0275\u0275text"](1," \xa0*"),a["\u0275\u0275elementEnd"]())}function Y(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",18),a["\u0275\u0275text"](1," Project Cost Center "),a["\u0275\u0275template"](2,U,2,0,"span",36),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"](3);a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("ngIf",null==e.tenantFields||null==e.tenantFields.costCenterDesc?null:e.tenantFields.costCenterDesc.isMandatory)}}function B(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",17),a["\u0275\u0275elementStart"](1,"div",18),a["\u0275\u0275text"](2," Project Type "),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](3,"div",39),a["\u0275\u0275text"](4),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"]().$implicit;a["\u0275\u0275advance"](4),a["\u0275\u0275textInterpolate1"](" ",e.get("projectType").value?e.get("projectType").value:"-"," ")}}function J(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",17),a["\u0275\u0275elementStart"](1,"div",18),a["\u0275\u0275text"](2,"Project Industry Type"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](3,"div",16),a["\u0275\u0275elementStart"](4,"div",40),a["\u0275\u0275text"](5),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"]().$implicit;a["\u0275\u0275advance"](5),a["\u0275\u0275textInterpolate1"](" ",e.get("projectIndustryType").value?e.get("projectIndustryType").value:"-"," ")}}function W(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",17),a["\u0275\u0275elementStart"](1,"div",18),a["\u0275\u0275text"](2,"Client Name"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](3,"div",39),a["\u0275\u0275text"](4),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"]().$implicit;a["\u0275\u0275advance"](4),a["\u0275\u0275textInterpolate1"](" ",e.get("clientName").value?e.get("clientName").value:"-"," ")}}function K(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"span",19),a["\u0275\u0275text"](1," \xa0*"),a["\u0275\u0275elementEnd"]())}function X(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",17),a["\u0275\u0275elementStart"](1,"div",18),a["\u0275\u0275text"](2," Billable "),a["\u0275\u0275template"](3,K,2,0,"span",36),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](4,"div",16),a["\u0275\u0275elementStart"](5,"mat-radio-group",41),a["\u0275\u0275elementStart"](6,"mat-radio-button",42),a["\u0275\u0275text"](7,"Yes"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](8,"mat-radio-button",42),a["\u0275\u0275text"](9,"No"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"](3);a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("ngIf",null==e.tenantFields||null==e.tenantFields.billable?null:e.tenantFields.billable.isMandatory),a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("value",1),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("value",0)}}function z(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"span",19),a["\u0275\u0275text"](1," \xa0*"),a["\u0275\u0275elementEnd"]())}function H(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",17),a["\u0275\u0275elementStart"](1,"div",18),a["\u0275\u0275text"](2," Incentive "),a["\u0275\u0275template"](3,z,2,0,"span",36),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](4,"div",16),a["\u0275\u0275elementStart"](5,"mat-radio-group",43),a["\u0275\u0275elementStart"](6,"mat-radio-button",42),a["\u0275\u0275text"](7,"Yes"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](8,"mat-radio-button",42),a["\u0275\u0275text"](9,"No"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"](3);a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("ngIf",null==e.tenantFields||null==e.tenantFields.incentive?null:e.tenantFields.incentive.isMandatory),a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("value",1),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("value",0)}}function $(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"span",19),a["\u0275\u0275text"](1,"\xa0*"),a["\u0275\u0275elementEnd"]())}function Q(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",17),a["\u0275\u0275elementStart"](1,"div",18),a["\u0275\u0275text"](2," Practice "),a["\u0275\u0275template"](3,$,2,0,"span",36),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](4,"div",16),a["\u0275\u0275element"](5,"app-input-search",44),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"](3);a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("ngIf",null==e.tenantFields||null==e.tenantFields.practice?null:e.tenantFields.practice.isMandatory),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("required",null==e.tenantFields||null==e.tenantFields.practice?null:e.tenantFields.practice.isMandatory)("list",e.practiceListMaster)("disableNone",!0)}}function Z(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"span",19),a["\u0275\u0275text"](1," \xa0*"),a["\u0275\u0275elementEnd"]())}function ee(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",17),a["\u0275\u0275elementStart"](1,"div",18),a["\u0275\u0275text"](2," RMG SPOC "),a["\u0275\u0275template"](3,Z,2,0,"span",36),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](4,"div",16),a["\u0275\u0275element"](5,"app-search-user",45),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"](3);a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("ngIf",null==e.tenantFields||null==e.tenantFields.rmgSpoc?null:e.tenantFields.rmgSpoc.isMandatory),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("isAutocomplete",!0)("required",null==e.tenantFields||null==e.tenantFields.rmgSpoc?null:e.tenantFields.rmgSpoc.isMandatory)}}function te(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"span",19),a["\u0275\u0275text"](1," \xa0*"),a["\u0275\u0275elementEnd"]())}function ne(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",17),a["\u0275\u0275elementStart"](1,"div",18),a["\u0275\u0275text"](2," PMO "),a["\u0275\u0275template"](3,te,2,0,"span",36),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](4,"div",16),a["\u0275\u0275element"](5,"app-search-user",46),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"](3);a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("ngIf",null==e.tenantFields||null==e.tenantFields.pmo?null:e.tenantFields.pmo.isMandatory),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("isAutocomplete",!0)("required",null==e.tenantFields||null==e.tenantFields.pmo?null:e.tenantFields.pmo.isMandatory)}}function ie(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",18),a["\u0275\u0275text"](1),a["\u0275\u0275elementStart"](2,"span",19),a["\u0275\u0275text"](3," \xa0*"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"](4);a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate1"](" ",e.tenantFieldsLable.lineManager.fieldLable,"")}}function ae(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"span",19),a["\u0275\u0275text"](1," \xa0*"),a["\u0275\u0275elementEnd"]())}function le(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",18),a["\u0275\u0275text"](1,"Line Manager"),a["\u0275\u0275template"](2,ae,2,0,"span",36),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"](4);a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("ngIf",null==e.tenantFields||null==e.tenantFields.lineManager?null:e.tenantFields.lineManager.isMandatory)}}function re(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",17),a["\u0275\u0275template"](1,ie,4,1,"div",28),a["\u0275\u0275template"](2,le,3,1,"ng-template",null,47,a["\u0275\u0275templateRefExtractor"]),a["\u0275\u0275elementStart"](4,"div",16),a["\u0275\u0275element"](5,"app-search-user",48),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275reference"](3),t=a["\u0275\u0275nextContext"](3);a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",null==t.tenantFieldsLable||null==t.tenantFieldsLable.lineManager?null:t.tenantFieldsLable.lineManager.isActiveField)("ngIfElse",e),a["\u0275\u0275advance"](4),a["\u0275\u0275property"]("isAutocomplete",!0)("required",null==t.tenantFields||null==t.tenantFields.lineManager?null:t.tenantFields.lineManager.isMandatory)}}function oe(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"div",49),a["\u0275\u0275element"](1,"mat-divider"),a["\u0275\u0275elementEnd"]())}const se=function(){return["project_item_id","item_name"]};function de(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"div",14),a["\u0275\u0275elementStart"](1,"div",15),a["\u0275\u0275elementStart"](2,"div",16),a["\u0275\u0275elementStart"](3,"div",17),a["\u0275\u0275elementStart"](4,"div",18),a["\u0275\u0275text"](5," Assignment Start Date "),a["\u0275\u0275elementStart"](6,"span",19),a["\u0275\u0275text"](7," \xa0*"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](8,"div",16),a["\u0275\u0275elementStart"](9,"mat-form-field",20),a["\u0275\u0275element"](10,"input",21),a["\u0275\u0275element"](11,"mat-datepicker-toggle",22),a["\u0275\u0275element"](12,"mat-datepicker",null,23),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](14,"div",17),a["\u0275\u0275elementStart"](15,"div",18),a["\u0275\u0275text"](16," Assignment End Date "),a["\u0275\u0275elementStart"](17,"span",19),a["\u0275\u0275text"](18," \xa0*"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](19,"div",16),a["\u0275\u0275elementStart"](20,"mat-form-field",20),a["\u0275\u0275element"](21,"input",24),a["\u0275\u0275element"](22,"mat-datepicker-toggle",22),a["\u0275\u0275element"](23,"mat-datepicker",null,25),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](25,k,6,4,"div",26),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](26,"div",16),a["\u0275\u0275elementStart"](27,"div",17),a["\u0275\u0275elementStart"](28,"div",18),a["\u0275\u0275text"](29," Project Name "),a["\u0275\u0275elementStart"](30,"span",19),a["\u0275\u0275text"](31," \xa0*"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](32,"div",16),a["\u0275\u0275element"](33,"app-input-search-huge-input",27),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](34,T,6,4,"div",26),a["\u0275\u0275elementStart"](35,"div",17),a["\u0275\u0275template"](36,R,4,1,"div",28),a["\u0275\u0275template"](37,V,4,0,"ng-template",null,29,a["\u0275\u0275templateRefExtractor"]),a["\u0275\u0275elementStart"](39,"div",16),a["\u0275\u0275elementStart"](40,"mat-form-field",20),a["\u0275\u0275elementStart"](41,"input",30),a["\u0275\u0275listener"]("keydown",(function(t){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"](2).splitPercentKeyDown(t)})),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](42,"div",16),a["\u0275\u0275elementStart"](43,"div",17),a["\u0275\u0275template"](44,G,3,2,"div",28),a["\u0275\u0275template"](45,Y,3,1,"ng-template",null,31,a["\u0275\u0275templateRefExtractor"]),a["\u0275\u0275elementStart"](47,"div",32),a["\u0275\u0275text"](48),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](49,B,5,1,"div",26),a["\u0275\u0275template"](50,J,6,1,"div",26),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](51,"div",33),a["\u0275\u0275template"](52,W,5,1,"div",26),a["\u0275\u0275template"](53,X,10,3,"div",26),a["\u0275\u0275template"](54,H,10,3,"div",26),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](55,"div",16),a["\u0275\u0275template"](56,Q,6,4,"div",26),a["\u0275\u0275template"](57,ee,6,3,"div",26),a["\u0275\u0275template"](58,ne,6,3,"div",26),a["\u0275\u0275template"](59,re,6,4,"div",26),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](60,"div",16),a["\u0275\u0275elementStart"](61,"div",34),a["\u0275\u0275elementStart"](62,"span",8),a["\u0275\u0275listener"]("click",(function(){a["\u0275\u0275restoreView"](e);const n=t.index;return a["\u0275\u0275nextContext"](2).removeProject(n)})),a["\u0275\u0275text"](63,"- Remove This Project"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](64,oe,2,0,"div",35),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=t.index,i=a["\u0275\u0275reference"](13),l=a["\u0275\u0275reference"](24),r=a["\u0275\u0275reference"](38),o=a["\u0275\u0275reference"](46),s=a["\u0275\u0275nextContext"](2);a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("formGroupName",n),a["\u0275\u0275advance"](9),a["\u0275\u0275property"]("matDatepicker",i)("min",s.dateOfJoining)("max",s.getAssignmentEndDate(n)),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("for",i),a["\u0275\u0275advance"](10),a["\u0275\u0275property"]("matDatepicker",l)("min",s.getAssignmentStartDate(n)),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("for",l),a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("ngIf",null==s.tenantFields||null==s.tenantFields.projectRole?null:s.tenantFields.projectRole.isActiveField),a["\u0275\u0275advance"](8),a["\u0275\u0275property"]("label","Project")("optionLabel",a["\u0275\u0275pureFunction0"](29,se))("apiUri","/api/employee360/masterData/getProjectList"),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",null==s.tenantFields||null==s.tenantFields.projectLocation?null:s.tenantFields.projectLocation.isActiveField),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("ngIf",null==s.tenantFieldsLable||null==s.tenantFieldsLable.splitPercentage?null:s.tenantFieldsLable.splitPercentage.isActiveField)("ngIfElse",r),a["\u0275\u0275advance"](8),a["\u0275\u0275property"]("ngIf",null==s.tenantFieldsLable||null==s.tenantFieldsLable.costCenter?null:s.tenantFieldsLable.costCenter.isActiveField)("ngIfElse",o),a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("matTooltip",e.get("costCenterDesc").value?e.get("costCenterDesc").value:"-"),a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate1"](" ",e.get("costCenterDesc").value?e.get("costCenterDesc").value:"-"," "),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",null==s.tenantFields||null==s.tenantFields.projectType?null:s.tenantFields.projectType.isActiveField),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",null==s.tenantFields||null==s.tenantFields.projectIndustryType?null:s.tenantFields.projectIndustryType.isActiveField),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("ngIf",null==s.tenantFields||null==s.tenantFields.clientName?null:s.tenantFields.clientName.isActiveField),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",null==s.tenantFields||null==s.tenantFields.billable?null:s.tenantFields.billable.isActiveField),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",null==s.tenantFields||null==s.tenantFields.incentive?null:s.tenantFields.incentive.isActiveField),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("ngIf",null==s.tenantFields||null==s.tenantFields.practice?null:s.tenantFields.practice.isActiveField),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",null==s.tenantFields||null==s.tenantFields.rmgSpoc?null:s.tenantFields.rmgSpoc.isActiveField),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",null==s.tenantFields||null==s.tenantFields.pmo?null:s.tenantFields.pmo.isActiveField),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",null==s.tenantFields||null==s.tenantFields.lineManager?null:s.tenantFields.lineManager.isActiveField),a["\u0275\u0275advance"](5),a["\u0275\u0275property"]("ngIf",s.projectDetailsFormArray.controls.length>1)}}function ce(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",49),a["\u0275\u0275elementStart"](1,"mat-icon",50),a["\u0275\u0275text"](2," error_outline "),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](3,"span",51),a["\u0275\u0275text"](4),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;a["\u0275\u0275advance"](4),a["\u0275\u0275textInterpolate1"](" ",e.errorMsg," ")}}function pe(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementContainerStart"](0),a["\u0275\u0275elementStart"](1,"div",4),a["\u0275\u0275text"](2,"Project Details"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](3,"form",5),a["\u0275\u0275listener"]("keydown.enter",(function(e){return e.preventDefault()})),a["\u0275\u0275template"](4,de,65,30,"div",6),a["\u0275\u0275elementStart"](5,"div",7),a["\u0275\u0275elementStart"](6,"span",8),a["\u0275\u0275listener"]("click",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"]().addNewProject()})),a["\u0275\u0275text"](7,"+ Add New Project"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](8,"div",9),a["\u0275\u0275elementStart"](9,"div",10),a["\u0275\u0275elementStart"](10,"button",11),a["\u0275\u0275listener"]("click",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"]().cancel()})),a["\u0275\u0275text"](11," Cancel "),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](12,"button",12),a["\u0275\u0275listener"]("click",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"]().saveDetails()})),a["\u0275\u0275text"](13),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](14,ce,5,1,"ng-template",null,13,a["\u0275\u0275templateRefExtractor"]),a["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=a["\u0275\u0275nextContext"]();a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("formGroup",e.projectDetailsFormGroup),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngForOf",e.projectDetailsFormArray.controls),a["\u0275\u0275advance"](8),a["\u0275\u0275property"]("disabled",e.loaderObject.isFormSubmitLoading),a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate1"](" ",e.isFromModal?"Update":"Save & Next >"," ")}}let me=(()=>{class e{constructor(e,t,n,i){this._edService=e,this._toaster=t,this.fb=n,this.injector=i,this.dialogRef=null,this.isFromModal=!1,this.subs=new F.a,this.loaderObject={isComponentLoading:!1,isFormSubmitLoading:!1},this.projectDetailsRes=new a.EventEmitter,this.projectDetailsFormGroup=this.fb.group({projectDetailsArr:this.fb.array([])}),this.projectLocationTypeMaster=[],this.practiceListMaster=[],this.projectDetailsPayload=[],this.removedProjRecord=[],this.tenantFieldsLable={},this.tenantFields={},this.dialogRef=this.injector.get(o.h,null),this.dialogData=this.injector.get(o.a,null)}createNewProjectGroup(e){return this.fb.group({recordId:[e?e.recordId:""],assignmentStartDate:[e?e.assignmentStartDate:"",g.H.required],assignmentEndDate:[e?e.assignmentEndDate:"",g.H.required],project:[e?e.project:null,g.H.required],projectIndustryType:[e?e.projectIndustryType:""],projectLocation:[e?e.projectLocation:""],splitPercentage:[e?e.splitPercentage:"",[g.H.required,g.H.max(100)]],practice:[e?e.practice:""],costCenterDesc:[e?e.costCenterDesc:""],clientName:[e?e.clientName:""],billable:[e?e.billable:""],incentive:[e?e.incentive:""],rmgSpoc:[e?e.rmgSpoc:""],projectType:[e?e.projectType:""],pmo:[e?null==e?void 0:e.pmo:""],lineManager:[e?null==e?void 0:e.lineManager:""],projectRole:[e?e.projectRole:""]})}get projectDetailsFormArray(){return this.projectDetailsFormGroup.get("projectDetailsArr")}getAssignmentStartDate(e){return this.projectDetailsFormArray.at(e).get("assignmentStartDate").value}getAssignmentEndDate(e){return this.projectDetailsFormArray.at(e).get("assignmentEndDate").value}getProjectLocation(){return new Promise((e,t)=>{this.subs.sink=this._edService.getProjectLocation().subscribe(t=>{e(t.data)},e=>{console.log(e),t(e)})})}getProjPracticeList(){return new Promise((e,t)=>{this.subs.sink=this._edService.getProjPracticeList().subscribe(t=>{e(t.data)},e=>{console.log(e),t(e)})})}getProjectrole(){return new Promise((e,t)=>{this.subs.sink=this._edService.getProjectrole().subscribe(t=>{e(t.data)},e=>{console.log(e),t(e)})})}getFieldTenantConfig(e){return new Promise((t,n)=>{this.subs.sink=this._edService.getFieldTenantConfig(e).subscribe(e=>{t(e.data)},e=>{console.log(e),n(e)})})}getDateOfJoining(e){return new Promise((t,n)=>{this.subs.sink=this._edService.getDateOfJoining(e).subscribe(e=>{e.err?n(e):t(e.doj)},e=>{console.log(e),n(e)})})}validate(e){console.log(e)}ngOnInit(){var e,t,n,a,l,r,o,s;return Object(i.c)(this,void 0,void 0,(function*(){this.loaderObject.isComponentLoading=!0,yield this.handleTenantWiseFieldConfig(),yield this.handleTenantLable(),this.associateId=(null===(t=null===(e=this.dialogData)||void 0===e?void 0:e.modalParams)||void 0===t?void 0:t.associateId)?null===(a=null===(n=this.dialogData)||void 0===n?void 0:n.modalParams)||void 0===a?void 0:a.associateId:this.associateId,this.isFromModal=!!(null===(r=null===(l=this.dialogData)||void 0===l?void 0:l.modalParams)||void 0===r?void 0:r.isFromModal)&&(null===(s=null===(o=this.dialogData)||void 0===o?void 0:o.modalParams)||void 0===s?void 0:s.isFromModal),yield this.bindSavedResponse(),this.loaderObject.isComponentLoading=!1,this.projectLocationTypeMaster=yield this.getProjectLocation(),this.practiceListMaster=yield this.getProjPracticeList(),this.projectRoleMaster=yield this.getProjectrole(),this.dateOfJoining=yield this.getDateOfJoining(this.associateId),yield this.handleProjectChange(),this.createInitValue(),this.handleProjectDetailsPayloadObject(),this.valueChangeListener()}))}handleTenantLable(){return Object(i.c)(this,void 0,void 0,(function*(){let e=yield this.getFieldLableTenantConfig("project_details");e.length>0&&e.forEach(e=>{this.tenantFieldsLable[e.key_field_name]={fieldLable:e.field_lable,isActiveField:!!e.is_active}}),console.log(this.tenantFieldsLable)}))}getFieldLableTenantConfig(e){return new Promise((t,n)=>{this.subs.sink=this._edService.getFieldLableTenantConfig(e).subscribe(e=>{t(e.data)},e=>{console.log(e),n(e)})})}handleTenantWiseFieldConfig(){return Object(i.c)(this,void 0,void 0,(function*(){let e=yield this.getFieldTenantConfig("project_details");e.length>0&&e.forEach((e,t)=>{this.tenantFields[e.field]={isMandatory:!!e.is_mandatory,isActiveField:!!e.is_active_field},e.is_mandatory&&this.projectDetailsFormArray.controls.forEach(t=>{let n=t.get(e.field);n&&n.setValidators([g.H.required])})}),console.log(this.tenantFields)}))}setTenantFieldValidators(){Object.keys(this.tenantFields).forEach((e,t)=>{this.projectDetailsFormArray.controls.forEach(t=>{this.tenantFields[e].isMandatory?t.get(e).setValidators([g.H.required]):t.get(e).clearValidators()})})}valueChangeListener(){this.projectDetailsFormGroup.valueChanges.subscribe(e=>{this.handleProjectDetailsPayloadObject(),this.handleProjectChange()})}handleProjectDetailsPayloadObject(){let e=this.projectDetailsFormGroup.get("projectDetailsArr").value;this.projectDetailsPayload=[],e.forEach((e,t)=>{this.projectDetailsPayload[t]={value:e,isChanged:this.checkIfChanged(e,this.projectDetailsInitValue[t])}}),console.log(this.projectDetailsPayload)}checkIfChanged(e,t){return!l.isEqual(e,t)}createInitValue(){this.projectDetailsInitValue=JSON.parse(JSON.stringify(this.projectDetailsFormGroup.get("projectDetailsArr").value))}bindSavedResponse(){return new Promise((e,t)=>{this.subs.sink=this._edService.getProjectDetailsCP(this.associateId).subscribe(t=>{if(!t.err){let n=t.data;n&&n.length>0?n.forEach((e,t)=>{let n="object"==typeof e.project?JSON.stringify(e.project):e.project;this.projectDetailsFormArray.push(this.createNewProjectGroup({recordId:e?e.record_id:"",assignmentStartDate:e?e.assignment_start_date:"",assignmentEndDate:e?e.assignment_end_date:"",project:n||null,projectIndustryType:e?e.industry_type:"",projectLocation:e?e.e360_project_location:"",costCenterDesc:e?e.project.cost_center_desc:"",splitPercentage:e?e.split_percentage:"",clientName:e?e.customer_name:"",billable:e?e.billable:"",incentive:e?e.incentive:"",rmgSpoc:e?e.rmg_spoc:"",projectType:e?e.project_type:"",practice:e?e.practice_id:"",pmo:e?null==e?void 0:e.pmo:"",lineManager:e?null==e?void 0:e.lineManager:"",projectRole:e?e.project_role:""}))}):this.addNewProject(),e(!0)}},e=>{console.log(e),t(e)})})}handleProjectChange(){return Object(i.c)(this,void 0,void 0,(function*(){for(let e of this.projectDetailsFormArray.controls){let t=e.get("project").value;if(t&&(t="string"==typeof t?JSON.parse(t):t,t&&t.project_item_id)){let n=t.cost_center_desc?t.cost_center_desc:"",i=t.industry_type?t.industry_type:"",a=t.customer_name?t.customer_name:"",l=t.project_type?t.project_type:"";t.profit_center&&(e.get("costCenterDesc").patchValue(n,{emitEvent:!1}),e.get("projectIndustryType").patchValue(i,{emitEvent:!1}),e.get("clientName").patchValue(a,{emitEvent:!1}),e.get("projectType").patchValue(l,{emitEvent:!1}))}}}))}addNewProject(){this.projectDetailsFormArray.push(this.createNewProjectGroup(null)),this.setTenantFieldValidators()}removeProject(e){this.addDeletedRecordId(this.removedProjRecord,e,this.projectDetailsFormArray),this.projectDetailsFormArray.removeAt(e)}addDeletedRecordId(e,t,n){let i=n.controls[t].get("recordId").value;i&&e.push(i)}validateIfChanged(){let e=!1;for(let t of this.projectDetailsPayload)if(t.isChanged){e=!0;break}return this.removedProjRecord.length>0&&(e=!0),e}saveDetails(){if(this.projectDetailsFormGroup.valid)d.a.fire({title:"Are you sure want to proceed ?",text:"",icon:"warning",showCancelButton:!0,confirmButtonColor:"#3085d6",cancelButtonColor:"#d33",confirmButtonText:"Yes"}).then(e=>Object(i.c)(this,void 0,void 0,(function*(){e.isConfirmed&&(yield this.callSaveDetailsApi())})));else{let e=0;for(let t=0;t<this.projectDetailsFormGroup.value.projectDetailsArr.length;t++)if(this.projectDetailsFormGroup.value.projectDetailsArr[t].splitPercentage>100){e=1;break}this._toaster.showWarning("Invalid data",0==e?"Kindly fill all mandatory fields to proceed !":"Split Percentage should be less than 100!")}}callSaveDetailsApi(){if(this.validateIfChanged()){this.loaderObject.isFormSubmitLoading=!0;let e={associateId:this.associateId,projectArr:this.projectDetailsPayload,isProjectMobilizationAction:!1,removedProjRecord:this.removedProjRecord};this.handleDateFormatPayLoad(e),this.subs.sink=this._edService.saveProjectDetailsCP(e).subscribe(e=>{this.loaderObject.isFormSubmitLoading=!1,e.err?this._toaster.showError("Error","Failed to save., Kindly contact KEBS team to resolve",2e3):(this._toaster.showSuccess("Success","Project details updated successfully !",2e3),this.resetFormFields(),this.isFromModal?this.closeDialog("Updated"):this.projectDetailsRes.emit({isCompleted:!0}))},e=>{this.loaderObject.isFormSubmitLoading=!1,this._toaster.showError("Error","Failed to save. Kindly contact KEBS team to resolve",2e3),console.log(e)})}else this._toaster.showWarning("No changes","No new changes were made !")}resetFormFields(){this.projectDetailsFormGroup.reset()}closeDialog(e){this.dialogRef.close(e)}cancel(){this.isFromModal&&this.closeDialog("Close")}splitPercentKeyDown(e){console.log(e.target.value.toString().length),8!=e.keyCode&&e.target.value.toString().length>=1&&e.target.value>100&&(e.preventDefault(),this._toaster.showWarning("Invalid data","Split Percentage should be less than 100!"))}handleProjectRoleChange(e){console.log("changes",e);let t=this.projectDetailsFormArray.at(e).get("projectRole").value,n=l.findWhere(this.projectRoleMaster,{id:t});n&&n.practice_id?this.projectDetailsFormArray.at(e).get("practice").patchValue(n.practice_id):(this.projectDetailsFormArray.at(e).get("practice").patchValue(""),this._toaster.showWarning("Warning","Practice not found for the project role"))}handleDateFormatPayLoad(e){e.projectArr.forEach((e,t)=>{e.value.assignmentEndDate=e.value.assignmentEndDate?r(e.value.assignmentEndDate).format("YYYY-MM-DD"):"",e.value.assignmentStartDate=e.value.assignmentStartDate?r(e.value.assignmentStartDate).format("YYYY-MM-DD"):""})}ngOnDestroy(){this.subs.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275directiveInject"](D.a),a["\u0275\u0275directiveInject"](w.a),a["\u0275\u0275directiveInject"](g.i),a["\u0275\u0275directiveInject"](a.Injector))},e.\u0275cmp=a["\u0275\u0275defineComponent"]({type:e,selectors:[["ed-project-details"]],inputs:{associateId:"associateId"},outputs:{projectDetailsRes:"projectDetailsRes"},features:[a["\u0275\u0275ProvidersFeature"]([{provide:c.c,useClass:p.c,deps:[c.f,p.a]},{provide:c.e,useValue:{parse:{dateInput:"DD-MMM-YYYY"},display:{dateInput:"DD-MMM-YYYY",monthYearLabel:"MMM YYYY"}}}])],decls:3,vars:2,consts:[[1,"container-fluid","project-details"],[4,"ngIf"],[1,"d-flex","justify-content-center","mt-3"],["matTooltip","Please wait...","diameter","30"],[1,"row","section-header","mb-2"],[3,"formGroup","keydown.enter"],["class","row","formArrayName","projectDetailsArr",4,"ngFor","ngForOf"],[1,"row","mt-2"],[1,"add-link",3,"click"],[1,"row","mt-4"],[1,"d-flex","flex-row"],["type","button","mat-stroked-button","",1,"cancel-btn",3,"click"],["type","button","mat-raised-button","",1,"ml-3","create-employee-btn",3,"disabled","click"],["errorCard",""],["formArrayName","projectDetailsArr",1,"row"],[1,"col-12","px-0",3,"formGroupName"],[1,"row"],[1,"col-3","px-0","mr-4"],[1,"row","field-title"],[1,"required-star"],["appearance","outline",2,"width","100%"],["matInput","","required","","formControlName","assignmentStartDate","placeholder","DD-MMM-YYYY","readonly","",3,"matDatepicker","min","max"],["matSuffix","",3,"for"],["picker2",""],["matInput","","required","","formControlName","assignmentEndDate","placeholder","DD-MMM-YYYY","readonly","",3,"matDatepicker","min"],["picker3",""],["class","col-3 px-0 mr-4",4,"ngIf"],["formControlName","project",2,"width","100%",3,"label","optionLabel","apiUri"],["class","row field-title",4,"ngIf","ngIfElse"],["defaultsplitlable",""],["required","","matInput","","digitOnly","","maxlength","3","type","text","placeholder","Enter here","formControlName","splitPercentage","min","0","max","100",3,"keydown"],["defaultcostlable",""],[1,"row",2,"font-weight","500","color","#f27a6c",3,"matTooltip"],[1,"row","my-3"],[1,"col-6","my-auto","px-0"],["class","row my-2",4,"ngIf"],["class","required-star",4,"ngIf"],["hideMatLabel","false","placeholder","Select One","formControlName","projectRole",2,"width","100%",3,"required","list","disableNone","change","keyup"],["hideMatLabel","false","placeholder","Select One","formControlName","projectLocation",2,"width","100%",3,"required","list","disableNone"],[1,"row",2,"font-weight","500"],[1,"row",2,"font-weight","500","color","#1e1d1d"],["formControlName","billable"],[1,"mr-3",3,"value"],["formControlName","incentive"],["hideMatLabel","false","placeholder","Select One","formControlName","practice",2,"width","100%",3,"required","list","disableNone"],["label","RMG SPOC","formControlName","rmgSpoc","placeholder","Select one",2,"width","100% !important",3,"isAutocomplete","required"],["label","PMO","formControlName","pmo","placeholder","Select one",2,"width","100% !important",3,"isAutocomplete","required"],["defaultlineManagerlable",""],["label","Project Manager","formControlName","lineManager","placeholder","Select one",2,"width","100% !important",3,"isAutocomplete","required"],[1,"row","my-2"],[2,"color","#cf0001","font-size","21px"],[2,"color","#cf0001","font-weight","500"]],template:function(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"div",0),a["\u0275\u0275template"](1,L,3,0,"ng-container",1),a["\u0275\u0275template"](2,pe,16,4,"ng-container",1),a["\u0275\u0275elementEnd"]()),2&e&&(a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",t.loaderObject.isComponentLoading),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",!t.loaderObject.isComponentLoading))},directives:[m.NgIf,C.c,x.a,g.J,g.w,g.n,m.NgForOf,y.a,g.h,g.o,h.c,f.b,g.e,v.g,g.F,g.v,g.l,v.i,h.i,v.f,I.a,O.a,g.q,M.a,P.b,P.a,_.a,j.a,b.a],styles:[".project-details[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]{font-size:16px;font-weight:500;color:#45546e}.project-details[_ngcontent-%COMP%]   .field-title[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#6e7b8f}.project-details[_ngcontent-%COMP%]   .required-star[_ngcontent-%COMP%]{color:#cf0001}.project-details[_ngcontent-%COMP%]     .mat-form-field-outline, .project-details[_ngcontent-%COMP%]     mat-datepicker-toggle{color:#b9c0ca!important}.project-details[_ngcontent-%COMP%]   .add-link[_ngcontent-%COMP%]{text-decoration:underline;color:#f27a6c;font-size:14px;cursor:pointer;font-weight:500}.project-details[_ngcontent-%COMP%]   .create-employee-btn[_ngcontent-%COMP%]{background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%)!important;color:#fff;height:42px}.project-details[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{color:#45546e;height:42px}.project-details[_ngcontent-%COMP%]     .mat-form-field-disabled{opacity:.5}.project-details[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}"]}),e})(),ue=(()=>{class e{}return e.\u0275mod=a["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=a["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[m.CommonModule,u.a,h.e,f.c,g.E,g.p,v.h,c.x,b.b,y.b,C.b,x.b,S.b,j.b,E.a,P.c]]}),e})()}}]);