(window.webpackJsonp=window.webpackJsonp||[]).push([[853],{"8SgF":function(e,t,i){"use strict";i.d(t,"a",(function(){return y}));var s=i("mrSG"),r=i("fXoL"),n=i("XNiG"),o=i("Kj3r"),l=i("1G5W"),a=i("3Pt+"),d=i("NJ67"),c=i("F97M"),h=i("XVR1"),u=i("kmnG"),p=i("ofXK"),m=i("qFsG"),f=i("/1cH"),g=i("NFeN"),v=i("FKr1");function b(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"mat-label"),r["\u0275\u0275text"](1),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate"](e.label)}}function S(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"mat-option",7),r["\u0275\u0275elementStart"](1,"small"),r["\u0275\u0275text"](2),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,i=r["\u0275\u0275nextContext"]();r["\u0275\u0275property"]("hidden",!i.isAutocomplete)("value",e),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate2"]("",e.displayName," | ",e.mail,"")}}let y=(()=>{class e extends d.a{constructor(e,t){super(),this.graphApi=e,this._AppShareService=t,this.userSearchSubject=new n.b,this.isAutocomplete=!1,this.Formwidth="",this.showUserList=new r.EventEmitter,this.selectedUser=new r.EventEmitter,this.label="",this.blur=new r.EventEmitter,this.required=!1,this.fieldCtrl=new a.j,this.disabled=!1,this.readonly=!1,this.isGraphApi=0,this.optClicked=!1,this._onDestroy=new n.b}ngOnInit(){this.userSearchSubject.pipe(Object(o.a)(600)).subscribe(e=>Object(s.c)(this,void 0,void 0,(function*(){if(console.log(e),this.isGraphApi)this.graphApi.getUserSuggestions(e).then(t=>Object(s.c)(this,void 0,void 0,(function*(){if(t&&t.length>0)this.showUserList.emit(t);else{let t=yield this.getUserSuggestionsFromDB(e);this.showUserList.emit(t)}})),t=>Object(s.c)(this,void 0,void 0,(function*(){console.log(t);let i=yield this.getUserSuggestionsFromDB(e);this.showUserList.emit(i)})));else{let t=yield this.getUserSuggestionsFromDB(e);this.showUserList.emit(t)}}))),this.fieldCtrl.valueChanges.pipe(Object(l.a)(this._onDestroy)).subscribe(e=>{console.log(e),e?(this.value=e.id,this.onChange(e.id)):(this.value="",this.onChange(""))})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}ngAfterViewInit(){}searchUser(e){if(this.text=e.target.value,this.optClicked="Enter"==e.key,!this.text)return this.graphApi.userSuggestions=[];this.userSearchSubject.next(this.text)}resetSuggestion(){this.graphApi.userSuggestions=[]}checkAndClearInput(){this.optClicked||0!=this.readonly||this.fieldCtrl.setValue("")}selectedOption(e){this.optClicked=!0,this.selectedUser.emit(e.option.value),this.value=e.option.value,this.blur.emit()}displayUserName(e){return e?e.displayName:""}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){return Object(s.c)(this,void 0,void 0,(function*(){if(console.log("inisde writevalue"),console.log(e),e)if(this.isGraphApi)this.graphApi.getUserProfile(e).then(t=>Object(s.c)(this,void 0,void 0,(function*(){if(t)this.fieldCtrl.setValue(t);else{let t=yield this.getUserProfileFromDB(e);this.fieldCtrl.setValue(t)}})),t=>Object(s.c)(this,void 0,void 0,(function*(){let t=yield this.getUserProfileFromDB(e);this.fieldCtrl.setValue(t)})));else{let t=yield this.getUserProfileFromDB(e);console.log(t),this.fieldCtrl.setValue(t)}else this.fieldCtrl.setValue("")}))}getUserSuggestionsFromDB(e){return Object(s.c)(this,void 0,void 0,(function*(){let t=yield this._AppShareService.getUserSuggestionsFromDB(e);return this.graphApi.userSuggestions=t.value,t.value}))}getUserProfileFromDB(e){return Object(s.c)(this,void 0,void 0,(function*(){let t=yield this._AppShareService.getUserProfileFromDB(e);return console.log(t),t&&t.length>0?t[0]:""}))}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](c.a),r["\u0275\u0275directiveInject"](h.a))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["app-search-user"]],inputs:{isAutocomplete:"isAutocomplete",Formwidth:"Formwidth",label:"label",required:"required",disabled:"disabled",readonly:"readonly",isGraphApi:"isGraphApi"},outputs:{showUserList:"showUserList",selectedUser:"selectedUser",blur:"blur"},features:[r["\u0275\u0275ProvidersFeature"]([{provide:a.t,useExisting:Object(r.forwardRef)(()=>e),multi:!0}]),r["\u0275\u0275InheritDefinitionFeature"]],decls:9,vars:10,consts:[["appearance","outline","width","Formwidth"],[4,"ngIf"],["matInput","",3,"matAutocomplete","placeholder","required","formControl","disabled","readonly","keyup","focus","focusout"],["matSuffix","",2,"color","#66615B !important","font-size","21px !important"],[3,"hidden","displayWith","optionSelected"],["auto","matAutocomplete"],[3,"hidden","value",4,"ngFor","ngForOf"],[3,"hidden","value"]],template:function(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div"),r["\u0275\u0275elementStart"](1,"mat-form-field",0),r["\u0275\u0275template"](2,b,2,1,"mat-label",1),r["\u0275\u0275elementStart"](3,"input",2),r["\u0275\u0275listener"]("keyup",(function(e){return t.searchUser(e)}))("focus",(function(){return t.resetSuggestion()}))("focusout",(function(){return t.checkAndClearInput()})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](4,"mat-icon",3),r["\u0275\u0275text"](5,"person_pin"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](6,"mat-autocomplete",4,5),r["\u0275\u0275listener"]("optionSelected",(function(e){return t.selectedOption(e)})),r["\u0275\u0275template"](8,S,3,4,"mat-option",6),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275reference"](7);r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngIf",t.label),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("matAutocomplete",e)("placeholder",t.label)("required",t.required)("formControl",t.fieldCtrl)("disabled",t.disabled)("readonly",t.readonly),r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("hidden",!t.isAutocomplete)("displayWith",t.displayUserName),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngForOf",t.graphApi.userSuggestions)}},directives:[u.c,p.NgIf,m.b,f.d,a.e,a.F,a.v,a.k,g.a,u.i,f.b,p.NgForOf,u.g,v.p],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding-bottom:8px!important}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-underline{bottom:0}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-infix{padding:.65em 0!important;border-top:.54375em solid transparent!important;font-size:13px}.mat-form-field[_ngcontent-%COMP%]     .mat-select-arrow{margin:0 4px!important}"]}),e})()}}]);