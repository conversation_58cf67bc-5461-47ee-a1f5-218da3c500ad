(window.webpackJsonp=window.webpackJsonp||[]).push([[876,861,977,981,987,990,991],{HmYF:function(e,t,n){"use strict";n.d(t,"a",(function(){return d}));var i=n("mrSG"),o=n("Iab2"),a=n("EUZL"),r=n("wd/R"),l=n("xG9w"),s=n("fXoL");let d=(()=>{class e{constructor(){this.formatColumn=(e,t,n)=>{const i=a.utils.decode_range(e["!ref"]);for(let o=i.s.r+1;o<=i.e.r;++o){const i=a.utils.encode_cell({r:o,c:t});e[i]&&e[i].v&&(e[i].t="d",e[i].z=n)}}}exportAsExcelFile(e,t,n,i,o){console.log("Excel to JSON Service",e);const r=a.utils.json_to_sheet(e);if(o&&o.length){const e=a.utils.sheet_to_json(r,{header:1}).shift();for(const t of o){const n=e.indexOf(t.fieldKey);this.formatColumn(r,n,t.fieldFormat)}}null==n&&(n=[]),null==i&&(i="DD-MM-YYYY"),this.formatExcelDateData(r,n,i);const l=a.write({Sheets:{data:r},SheetNames:["data"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(l,t)}formatExcelDateData(e,t,n){for(let a of Object.keys(e))if(null!=e[a]&&null!=e[a].t&&null!=e[a].v&&r(e[a].v,n,!0).isValid()){let i=a.replace(/[0-9]/g,"")+"1";0==l.where(t,{value:e[i].v}).length&&null!=e[i]&&null!=e[i].t&&t.push({value:e[i].v,format:n})}let i=[],o=1;for(let a of t)for(let t of Object.keys(e)){let n=parseInt(t.replace(/[^0-9]/g,""));n>o&&(o=n),null!=e[t]&&null!=e[t].v&&e[t].v==a.value&&i.push({value:t.replace(/[0-9]/g,""),format:a.format})}for(let a of i)for(let t=2;t<=o;t++)null!=e[a.value+""+t]&&null!=e[a.value+""+t].t&&(e[a.value+""+t].t="d",null!=e[a.value+""+t].v&&"Invalid date"!=e[a.value+""+t].v?e[a.value+""+t].v=r(e[a.value+""+t].v,a.format).format("YYYY/MM/DD"):(console.log(e[a.value+""+t].t),e[a.value+""+t].v="",e[a.value+""+t].t="s"))}saveAsExcelFile(e,t){const n=new Blob([e],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8"});o.saveAs(n,t.concat(".xlsx"))}exportAsExcelFileWFH(e,t,n){const i=a.utils.json_to_sheet(e),o=a.utils.json_to_sheet(t),r=a.write({Sheets:{All_Approvals:i,Pending_Approvals:o},SheetNames:["All_Approvals","Pending_Approvals"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(r,n)}exportAsExcelFileForPayroll(e,t,n,i,o,r){const l=a.utils.json_to_sheet(e),s=a.utils.json_to_sheet(t),d=a.utils.json_to_sheet(n),c=a.utils.json_to_sheet(i),g=a.utils.json_to_sheet(o),p=a.write({Sheets:{Regular_Report:l,Intern_Report:s,Contract_Report:d,Perdiem_Report:c,RP_Report:g},SheetNames:["Regular_Report","Intern_Report","Contract_Report","Perdiem_Report","RP_Report"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(p,r)}exportAsCsvFileWithSheetName(e,t){return Object(i.c)(this,void 0,void 0,(function*(){let n=a.utils.book_new();for(let t of e){let e=a.utils.json_to_sheet(t.data);a.utils.book_append_sheet(n,e,t.sheetName)}let i=a.write(n,{bookType:"xlsx",type:"binary"});yield this.saveAsCsvFile(i,t)}))}saveAsCsvFile(e,t){return Object(i.c)(this,void 0,void 0,(function*(){const n=new Blob([yield this.s2ab(e)],{type:"application/octet-stream"});o.saveAs(n,t.concat(".csv"))}))}s2ab(e){return Object(i.c)(this,void 0,void 0,(function*(){for(var t=new ArrayBuffer(e.length),n=new Uint8Array(t),i=0;i<e.length;i++)n[i]=255&e.charCodeAt(i);return t}))}exportAsExcelFileWithCellMerge(e,t,n){const i=a.utils.json_to_sheet(e);i["!merges"]=n;const o=a.write({Sheets:{data:i},SheetNames:["data"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(o,t)}exportJsonToExcelWithMultipleSheets(e,t){return Object(i.c)(this,void 0,void 0,(function*(){let n=a.utils.book_new();for(let t of e){let e=a.utils.json_to_sheet(t.data);a.utils.book_append_sheet(n,e,t.sheetName)}let i=a.write(n,{bookType:"xlsx",type:"array"});this.saveAsExcelFile(i,t)}))}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275prov=s["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},ipAj:function(e,t,n){"use strict";n.r(t),n.d(t,"RateCardModule",(function(){return ce}));var i=n("ofXK"),o=n("bTqV"),a=n("NFeN"),r=n("Qu3c"),l=n("kmnG"),s=n("bSwM"),d=n("qFsG"),c=n("3Pt+"),g=n("iadO"),p=n("0IaG"),h=n("STbY"),m=n("rDax"),u=n("tyNb"),f=n("fXoL"),C=n("mbKZ");const b=function(){return[82,83]};let _=(()=>{class e{constructor(){}ngOnInit(){}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=f["\u0275\u0275defineComponent"]({type:e,selectors:[["app-upload-page"]],decls:1,vars:2,consts:[[3,"app_id_list"]],template:function(e,t){1&e&&f["\u0275\u0275element"](0,"migration-cockpit",0),2&e&&f["\u0275\u0275property"]("app_id_list",f["\u0275\u0275pureFunction0"](1,b))},directives:[C.a],styles:[""]}),e})();var y=n("mrSG"),v=n("1G5W"),w=n("XNiG"),E=n("jr6c"),x=n("1A3m"),O=n("0BBf"),M=n("flaP"),S=n("yTZu"),I=n("jaxi"),F=n("1jcm"),P=n("ZzPI"),R=n("6t9p");function k(e,t){if(1&e&&(f["\u0275\u0275elementStart"](0,"div",13),f["\u0275\u0275elementStart"](1,"mat-icon",14),f["\u0275\u0275text"](2,"search"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275element"](3,"input",15),f["\u0275\u0275elementEnd"]()),2&e){const e=f["\u0275\u0275nextContext"]();f["\u0275\u0275advance"](3),f["\u0275\u0275property"]("formControl",e.searchFormControl)}}const A=function(){return["../upload"]};function T(e,t){1&e&&(f["\u0275\u0275elementStart"](0,"span",16),f["\u0275\u0275elementStart"](1,"mat-icon"),f["\u0275\u0275text"](2,"upload"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]()),2&e&&f["\u0275\u0275property"]("routerLink",f["\u0275\u0275pureFunction0"](1,A))}function L(e,t){1&e&&f["\u0275\u0275elementContainer"](0)}function D(e,t){1&e&&(f["\u0275\u0275elementStart"](0,"button",17),f["\u0275\u0275elementStart"](1,"mat-icon"),f["\u0275\u0275text"](2,"more_vert"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]())}function j(e,t){1&e&&(f["\u0275\u0275elementStart"](0,"button",21),f["\u0275\u0275text"](1," Create Rate Card "),f["\u0275\u0275elementEnd"]())}function z(e,t){if(1&e){const e=f["\u0275\u0275getCurrentView"]();f["\u0275\u0275elementStart"](0,"button",22),f["\u0275\u0275listener"]("click",(function(){f["\u0275\u0275restoreView"](e);const t=f["\u0275\u0275nextContext"](2),n=f["\u0275\u0275reference"](15);return t.openMandatoryFieldsDialog(n)})),f["\u0275\u0275text"](1," Field Configuration "),f["\u0275\u0275elementEnd"]()}}function N(e,t){if(1&e){const e=f["\u0275\u0275getCurrentView"]();f["\u0275\u0275elementStart"](0,"button",22),f["\u0275\u0275listener"]("click",(function(){return f["\u0275\u0275restoreView"](e),f["\u0275\u0275nextContext"](2).openDataConfigDialog()})),f["\u0275\u0275text"](1," Data Configuration "),f["\u0275\u0275elementEnd"]()}}function W(e,t){if(1&e&&(f["\u0275\u0275elementStart"](0,"div",18),f["\u0275\u0275template"](1,j,2,0,"button",19),f["\u0275\u0275template"](2,z,2,0,"button",20),f["\u0275\u0275template"](3,N,2,0,"button",20),f["\u0275\u0275elementEnd"]()),2&e){const e=f["\u0275\u0275nextContext"]();f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",!e.isDataGridViewActive),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",e.isRateFieldConfigEnabled),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",e.isRateDataConfigEnabled)}}const V=function(e){return{"btn-toggle-selected":e}};function H(e,t){if(1&e){const e=f["\u0275\u0275getCurrentView"]();f["\u0275\u0275elementStart"](0,"mat-button-toggle",42),f["\u0275\u0275listener"]("change",(function(){f["\u0275\u0275restoreView"](e);const t=f["\u0275\u0275nextContext"]().$implicit;return f["\u0275\u0275nextContext"](2).selectToggle(t.type)})),f["\u0275\u0275text"](1),f["\u0275\u0275elementEnd"]()}if(2&e){const e=f["\u0275\u0275nextContext"]().$implicit,t=f["\u0275\u0275nextContext"](2);f["\u0275\u0275property"]("value",e.type)("ngClass",f["\u0275\u0275pureFunction1"](3,V,t.selectedToggle===e.type)),f["\u0275\u0275advance"](1),f["\u0275\u0275textInterpolate1"](" ",e.label," ")}}function G(e,t){if(1&e&&(f["\u0275\u0275elementContainerStart"](0),f["\u0275\u0275template"](1,H,2,5,"mat-button-toggle",41),f["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",e.is_active)}}function q(e,t){1&e&&(f["\u0275\u0275elementStart"](0,"div",43),f["\u0275\u0275text"](1," Mandatory in Quote "),f["\u0275\u0275elementEnd"]())}function Q(e,t){if(1&e&&f["\u0275\u0275element"](0,"mat-slide-toggle",48),2&e){const e=f["\u0275\u0275nextContext"](3).$implicit;f["\u0275\u0275property"]("formControl",e.isQuoteMandatory)}}function K(e,t){1&e&&f["\u0275\u0275text"](0,"-")}function B(e,t){if(1&e&&(f["\u0275\u0275elementStart"](0,"div",35),f["\u0275\u0275template"](1,Q,1,1,"mat-slide-toggle",50),f["\u0275\u0275template"](2,K,1,0,"ng-template",null,51,f["\u0275\u0275templateRefExtractor"]),f["\u0275\u0275elementEnd"]()),2&e){const e=f["\u0275\u0275reference"](3),t=f["\u0275\u0275nextContext"](2).$implicit;f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",t.rateFlowEnabled&&t.isMandatory.value)("ngIfElse",e)}}function U(e,t){if(1&e&&(f["\u0275\u0275elementStart"](0,"div",46),f["\u0275\u0275elementStart"](1,"div",47),f["\u0275\u0275text"](2),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](3,"div",35),f["\u0275\u0275element"](4,"mat-slide-toggle",48),f["\u0275\u0275elementEnd"](),f["\u0275\u0275template"](5,B,4,2,"div",49),f["\u0275\u0275elementEnd"]()),2&e){const e=f["\u0275\u0275nextContext"]().$implicit,t=f["\u0275\u0275nextContext"](3);f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngClass",t.isQuoteEnabled?"col-6":"col-9"),f["\u0275\u0275advance"](1),f["\u0275\u0275textInterpolate1"](" ",e.fieldLabel," "),f["\u0275\u0275advance"](2),f["\u0275\u0275property"]("formControl",e.isMandatory),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",t.isQuoteEnabled)}}function Y(e,t){if(1&e&&(f["\u0275\u0275elementContainerStart"](0),f["\u0275\u0275template"](1,U,6,4,"div",45),f["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf","id"!=e.fieldKey)}}function $(e,t){if(1&e&&(f["\u0275\u0275elementStart"](0,"div",44),f["\u0275\u0275template"](1,Y,2,1,"ng-container",31),f["\u0275\u0275elementEnd"]()),2&e){const e=f["\u0275\u0275nextContext"](2);f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngForOf",e.manpowerFieldsList)}}function J(e,t){if(1&e&&(f["\u0275\u0275elementStart"](0,"div",46),f["\u0275\u0275elementStart"](1,"div",47),f["\u0275\u0275text"](2),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](3,"div",35),f["\u0275\u0275element"](4,"mat-slide-toggle",48),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]()),2&e){const e=f["\u0275\u0275nextContext"]().$implicit;f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngClass","col-9"),f["\u0275\u0275advance"](1),f["\u0275\u0275textInterpolate1"](" ",e.fieldLabel," "),f["\u0275\u0275advance"](2),f["\u0275\u0275property"]("formControl",e.isMandatory)}}function X(e,t){if(1&e&&(f["\u0275\u0275elementContainerStart"](0),f["\u0275\u0275template"](1,J,5,3,"div",45),f["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf","id"!=e.fieldKey)}}function Z(e,t){if(1&e&&(f["\u0275\u0275elementStart"](0,"div",44),f["\u0275\u0275template"](1,X,2,1,"ng-container",31),f["\u0275\u0275elementEnd"]()),2&e){const e=f["\u0275\u0275nextContext"](2);f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngForOf",e.nonmanpowerFieldsList)}}function ee(e,t){if(1&e&&(f["\u0275\u0275elementStart"](0,"div",46),f["\u0275\u0275elementStart"](1,"div",47),f["\u0275\u0275text"](2),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](3,"div",35),f["\u0275\u0275element"](4,"mat-slide-toggle",48),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]()),2&e){const e=f["\u0275\u0275nextContext"]().$implicit;f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngClass","col-9"),f["\u0275\u0275advance"](1),f["\u0275\u0275textInterpolate1"](" ",e.fieldLabel," "),f["\u0275\u0275advance"](2),f["\u0275\u0275property"]("formControl",e.isMandatory)}}function te(e,t){if(1&e&&(f["\u0275\u0275elementContainerStart"](0),f["\u0275\u0275template"](1,ee,5,3,"div",45),f["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf","id"!=e.fieldKey)}}function ne(e,t){if(1&e&&(f["\u0275\u0275elementStart"](0,"div",44),f["\u0275\u0275template"](1,te,2,1,"ng-container",31),f["\u0275\u0275elementEnd"]()),2&e){const e=f["\u0275\u0275nextContext"](2);f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngForOf",e.licenseFieldsList)}}function ie(e,t){if(1&e){const e=f["\u0275\u0275getCurrentView"]();f["\u0275\u0275elementStart"](0,"div",23),f["\u0275\u0275elementStart"](1,"div",24),f["\u0275\u0275elementStart"](2,"div",25),f["\u0275\u0275text"](3," Rate Card Field Configuration "),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](4,"div",26),f["\u0275\u0275elementStart"](5,"button",27),f["\u0275\u0275listener"]("click",(function(){return f["\u0275\u0275restoreView"](e),f["\u0275\u0275nextContext"]().closeMandatoryFieldDialog()})),f["\u0275\u0275elementStart"](6,"mat-icon",28),f["\u0275\u0275text"](7,"close"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](8,"div",29),f["\u0275\u0275elementStart"](9,"mat-button-toggle-group",30),f["\u0275\u0275template"](10,G,2,1,"ng-container",31),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](11,"div",32),f["\u0275\u0275elementStart"](12,"div",33),f["\u0275\u0275elementStart"](13,"div",34),f["\u0275\u0275text"](14," Field Name "),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](15,"div",35),f["\u0275\u0275text"](16," Mandatory "),f["\u0275\u0275elementEnd"](),f["\u0275\u0275template"](17,q,2,0,"div",36),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275template"](18,$,2,1,"div",37),f["\u0275\u0275template"](19,Z,2,1,"div",37),f["\u0275\u0275template"](20,ne,2,1,"div",37),f["\u0275\u0275elementStart"](21,"div",38),f["\u0275\u0275elementStart"](22,"button",39),f["\u0275\u0275listener"]("click",(function(){return f["\u0275\u0275restoreView"](e),f["\u0275\u0275nextContext"]().closeMandatoryFieldDialog()})),f["\u0275\u0275text"](23," Cancel "),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](24,"button",40),f["\u0275\u0275listener"]("click",(function(){return f["\u0275\u0275restoreView"](e),f["\u0275\u0275nextContext"]().saveMandatoryFields()})),f["\u0275\u0275text"](25," Save "),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]()}if(2&e){const e=f["\u0275\u0275nextContext"]();f["\u0275\u0275advance"](9),f["\u0275\u0275property"]("value",e.selectedToggle),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngForOf",e.rcTypelist),f["\u0275\u0275advance"](3),f["\u0275\u0275property"]("ngClass",e.isQuoteEnabled&&1==e.selectedToggle?"col-6":"col-9"),f["\u0275\u0275advance"](4),f["\u0275\u0275property"]("ngIf",e.isQuoteEnabled&&1==e.selectedToggle),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",1==e.selectedToggle),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",2==e.selectedToggle),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",3==e.selectedToggle)}}function oe(e,t){if(1&e){const e=f["\u0275\u0275getCurrentView"]();f["\u0275\u0275elementStart"](0,"div",23),f["\u0275\u0275elementStart"](1,"div",24),f["\u0275\u0275elementStart"](2,"div",25),f["\u0275\u0275text"](3," Rate Card Data Configuration "),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](4,"div",26),f["\u0275\u0275elementStart"](5,"button",27),f["\u0275\u0275listener"]("click",(function(){return f["\u0275\u0275restoreView"](e),f["\u0275\u0275nextContext"]().closeMandatoryFieldDialog()})),f["\u0275\u0275elementStart"](6,"mat-icon",28),f["\u0275\u0275text"](7,"close"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](8,"dx-data-grid",52),f["\u0275\u0275listener"]("onRowInserted",(function(t){return f["\u0275\u0275restoreView"](e),f["\u0275\u0275nextContext"]().positionRowInserted(t)}))("onRowUpdated",(function(t){return f["\u0275\u0275restoreView"](e),f["\u0275\u0275nextContext"]().positionRowUpdated(t)}))("onEditorPreparing",(function(t){return f["\u0275\u0275restoreView"](e),f["\u0275\u0275nextContext"]().onEditorPreparing(t)}))("onInitNewRow",(function(t){return f["\u0275\u0275restoreView"](e),f["\u0275\u0275nextContext"]().onInitNewRow(t)})),f["\u0275\u0275element"](9,"dxo-paging",53),f["\u0275\u0275element"](10,"dxo-editing",54),f["\u0275\u0275element"](11,"dxo-sorting",55),f["\u0275\u0275element"](12,"dxo-filter-row",56),f["\u0275\u0275element"](13,"dxo-header-filter",57),f["\u0275\u0275element"](14,"dxo-scrolling",58),f["\u0275\u0275element"](15,"dxi-column",59),f["\u0275\u0275element"](16,"dxi-column",60),f["\u0275\u0275elementStart"](17,"dxi-column",61),f["\u0275\u0275element"](18,"dxo-lookup",62),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]()}if(2&e){const e=f["\u0275\u0275nextContext"]();f["\u0275\u0275advance"](8),f["\u0275\u0275property"]("dataSource",e.positionList)("allowColumnReordering",!0)("showBorders",!0)("showColumnLines",!0)("showRowLines",!1)("rowAlternationEnabled",!0)("allowColumnResizing",!0)("columnResizingMode",e.columnResizingMode),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("pageSize",15)("pageIndex",0),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("allowUpdating",!0)("allowAdding",!0),f["\u0275\u0275advance"](2),f["\u0275\u0275property"]("visible",!0)("applyFilter",!0),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("visible",!0),f["\u0275\u0275advance"](2),f["\u0275\u0275property"]("width",120)("allowSorting",!0)("allowEditing",!1),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("width",300)("allowSorting",!0),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("width",150)("allowSorting",!0),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("dataSource",e.booleanList)}}const ae=[{path:"",redirectTo:"list"},{path:"upload",component:_},{path:"list",component:(()=>{class e{constructor(e,t,n,i,o){this.dialog=e,this._quoteService=t,this._toaster=n,this._qbMasterService=i,this._roles=o,this.manpowerFieldsList=[],this.nonmanpowerFieldsList=[],this.licenseFieldsList=[],this._onDestroy=new w.b,this.searchFormControl=new c.j(""),this.quoteMandatoryFieldList=[],this.rcMandatoryFieldList=[],this.nmpMandatoryFieldList=[],this.licMandatoryFieldList=[],this.rcTypelist=[{label:"Manpower",type:1,is_active:!0},{label:"Non-Manpower",type:2,is_active:!0},{label:"License",type:3,is_active:!0}],this.selectedToggle=1,this.mandatoryConfigName="quote_mandatory_fields",this.isDataGridViewActive=!0,this.wEMappingEnabled=!1,this.sDMappingEnabled=!1,this.isQuoteEnabled=!1,this.isRateCardUploadEnabled=!1,this.isRateFieldConfigEnabled=!1,this.isRateDataConfigEnabled=!1,this.rateCardCount=0,this.resolveMandatoryFields=()=>{for(const e of this.manpowerFieldsList)e.isMandatory.patchValue(this.rcMandatoryFieldList.includes(e.fieldKey)),e.isQuoteMandatory.patchValue(!!this.isQuoteEnabled&&this.quoteMandatoryFieldList.includes(e.fieldKey));for(const e of this.nonmanpowerFieldsList)e.isMandatory.patchValue(this.nmpMandatoryFieldList.includes(e.fieldKey));for(const e of this.licenseFieldsList)e.isMandatory.patchValue(this.licMandatoryFieldList.includes(e.fieldKey))},this.closeMandatoryFieldDialog=()=>{var e;null===(e=this.mandatoryFieldDialogRef)||void 0===e||e.close(),this.resolveMandatoryFields()},this.saveMandatoryFields=()=>{let e=[],t=[];const n=1==this.selectedToggle?this.manpowerFieldsList:2==this.selectedToggle?this.nonmanpowerFieldsList:this.licenseFieldsList;for(const i of n)t.push({id:i.id,fieldKey:i.fieldKey,isMandatory:i.isMandatory.value}),this.isQuoteEnabled&&i.isMandatory.value&&i.rateFlowEnabled&&i.isQuoteMandatory.value&&e.push(i.fieldKey);this.isQuoteEnabled&&1==this.selectedToggle?this._quoteService.updateQuoteConfiguration([{config_name:this.mandatoryConfigName,config_value:e}]).pipe(Object(v.a)(this._onDestroy)).subscribe(n=>{"S"==n.messType?(this.quoteMandatoryFieldList=e,this.updateRcFieldConfiguration(t)):this._toaster.showError("Error","Error in updating Quote configuration",3e3)},e=>{console.log(e),this._toaster.showError("Error","Error in updating Quote configuration",3e3)}):this.updateRcFieldConfiguration(t)},this.updateRateCardCount=(e=0)=>{this.rateCardCount=e},this.updateRcFieldConfig=(e=[],t=1)=>{if(e.length){let n=[],i=[];for(const t of e){const e={id:t.id,fieldKey:t.field_key,fieldLabel:t.field_label,isMandatory:new c.j(t.is_mandatory),rateFlowEnabled:t.rate_flow_to_quote_enabled,isQuoteMandatory:new c.j(this.quoteMandatoryFieldList.includes(t.field_key))};t.is_mandatory&&i.push(t.field_key),n.push(e)}1==t?(this.manpowerFieldsList=n,this.rcMandatoryFieldList=i):2==t?(this.nonmanpowerFieldsList=n,this.nmpMandatoryFieldList=i):3==t&&(this.licenseFieldsList=n,this.licMandatoryFieldList=i)}},this.updateRcFieldConfiguration=(e=[])=>{this._quoteService.updateRcFieldConfig(e).pipe(Object(v.a)(this._onDestroy)).subscribe(t=>{var n;if("S"==t.messType){const t=e.map(e=>e.isMandatory?e.fieldKey:null).filter(e=>e);1==this.selectedToggle?this.rcMandatoryFieldList=t:2==this.selectedToggle?this.nmpMandatoryFieldList=t:3==this.selectedToggle&&(this.licMandatoryFieldList=t),this._toaster.showSuccess("Updated","Field Configuration updated Successfully !",2e3),null===(n=this.mandatoryFieldDialogRef)||void 0===n||n.close(),window.location.reload()}else this._toaster.showError("Error","Error in updating Field configuration",3e3)},e=>{console.log(e),this._toaster.showError("Error","Error in updating Field configuration",3e3)})}}ngOnInit(){this._qbMasterService.quoteEnabled.subscribe(e=>{this.isQuoteEnabled=e.quote_enabled});const e=this._roles.roles.filter(e=>908===e.application_id);e.length&&(this.isRateCardUploadEnabled=!!e.find(e=>90801===e.object_id),this.isRateFieldConfigEnabled=!!e.find(e=>90802===e.object_id),this.isRateDataConfigEnabled=!!e.find(e=>90803===e.object_id)),this._quoteService.getQuoteConfiguration([this.mandatoryConfigName,"quote_work_location_entity_mapping_enabled","quote_service_division_mapping_enabled"]).pipe(Object(v.a)(this._onDestroy)).subscribe(e=>{if("S"==e.messType&&e.data&&e.data.length){const t=e.data;for(const e of t)if(e&&e.quote_config_name&&e.hasOwnProperty("quote_config_value"))switch(e.quote_config_name){case"quote_mandatory_fields":this.quoteMandatoryFieldList=e.quote_config_value;break;case"quote_work_location_entity_mapping_enabled":this.wEMappingEnabled=e.quote_config_value||!1;break;case"quote_service_division_mapping_enabled":this.sDMappingEnabled=e.quote_config_value||!1}}},e=>{console.log(e),this._toaster.showError("Error","Error in getting Quote configuration",3e3)})}openMandatoryFieldsDialog(e){this.mandatoryFieldDialogRef=this.dialog.open(e,{height:"75%",width:this.isQuoteEnabled?"50vw":"30vw"}),this.mandatoryFieldDialogRef.afterClosed().subscribe(e=>{})}openDataConfigDialog(){return Object(y.c)(this,void 0,void 0,(function*(){const{DataConfigPageComponent:e}=yield n.e(345).then(n.bind(null,"rwb8"));this.mandatoryFieldDialogRef=this.dialog.open(e,{height:"100%",width:"73%",position:{right:"0px"}}),this.mandatoryFieldDialogRef.afterClosed().subscribe(e=>{})}))}selectToggle(e){this.selectedToggle=e}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}}return e.\u0275fac=function(t){return new(t||e)(f["\u0275\u0275directiveInject"](p.b),f["\u0275\u0275directiveInject"](E.a),f["\u0275\u0275directiveInject"](x.a),f["\u0275\u0275directiveInject"](O.a),f["\u0275\u0275directiveInject"](M.a))},e.\u0275cmp=f["\u0275\u0275defineComponent"]({type:e,selectors:[["app-landing-page"]],decls:22,vars:11,consts:[[1,"page-header"],[1,"total-count"],[1,"toolbar"],["class","search-container",4,"ngIf"],["matTooltip","Upload Rate card","routerLinkActive","router-link-active",3,"routerLink",4,"ngIf"],[4,"ngTemplateOutlet"],["class","more-btn","mat-stroked-button","",4,"ngIf"],[3,"isFromRateCard","manpowerMandatoryFields","nmpMandatoryFields","licMandatoryFields","wEMappingEnabled","sDMappingEnabled","rateCardCount","mpRcFieldConfig","nmpRcFieldConfig","liRcFieldConfig"],["createAction",""],["mandatoryFields",""],["dataConfig",""],["createMoreOptions","matMenu"],["mat-button","","routerLink","../upload"],[1,"search-container"],[2,"font-size","20px"],["placeholder","Search",1,"input-search",3,"formControl"],["matTooltip","Upload Rate card","routerLinkActive","router-link-active",3,"routerLink"],["mat-stroked-button","",1,"more-btn"],[1,"create-action"],["mat-button","","class","create-btn","routerLink","../create",4,"ngIf"],["class","ml-3","mat-stroked-button","",3,"click",4,"ngIf"],["mat-button","","routerLink","../create",1,"create-btn"],["mat-stroked-button","",1,"ml-3",3,"click"],[1,"container-fluid","mandatory-field-styles"],[1,"row","pt-2","pb-1"],[1,"col-10","d-flex","align-items-center",2,"font-weight","500","color","#45546E"],[1,"col-2","d-flex","justify-content-end"],["mat-icon-button","",3,"click"],[2,"font-size","15px","color","#1C1B1F"],[1,"row","col-12","pt-2","toggle-class"],[3,"value"],[4,"ngFor","ngForOf"],[1,"pt-2","header"],[1,"row"],[3,"ngClass"],[1,"col-3","d-flex","justify-content-center"],["class","col-3 d-flex justify-content-center","matTooltip","Mandatory Field in Quote, Also Fetches Rate in Quote when Changes are made to the field",4,"ngIf"],["class","pb-2","style","max-height: 50vh; overflow-x: hidden;",4,"ngIf"],[1,"row","col-12","pt-1","pb-2","pl-2"],["mat-raised-button","",1,"cancel-btn","ml-2","mt-2",3,"click"],["mat-raised-button","",1,"create-btn","ml-2","mt-2",3,"click"],["class","toggle-btn",3,"value","ngClass","change",4,"ngIf"],[1,"toggle-btn",3,"value","ngClass","change"],["matTooltip","Mandatory Field in Quote, Also Fetches Rate in Quote when Changes are made to the field",1,"col-3","d-flex","justify-content-center"],[1,"pb-2",2,"max-height","50vh","overflow-x","hidden"],["class","row pt-2",4,"ngIf"],[1,"row","pt-2"],[1,"text-class",3,"ngClass"],[3,"formControl"],["class","col-3 d-flex justify-content-center",4,"ngIf"],[3,"formControl",4,"ngIf","ngIfElse"],["showEmpty",""],["id","gridContainer","keyExpr","id",3,"dataSource","allowColumnReordering","showBorders","showColumnLines","showRowLines","rowAlternationEnabled","allowColumnResizing","columnResizingMode","onRowInserted","onRowUpdated","onEditorPreparing","onInitNewRow"],[3,"pageSize","pageIndex"],["mode","row",3,"allowUpdating","allowAdding"],["mode","multiple"],[3,"visible","applyFilter"],[3,"visible"],["columnRenderingMode","virtual"],["dataField","id","caption","Id",3,"width","allowSorting","allowEditing"],["dataField","name","caption","Name",3,"width","allowSorting"],["dataField","is_for_quote","caption","Is Active",3,"width","allowSorting"],["displayExpr","name","valueExpr","id",3,"dataSource"]],template:function(e,t){if(1&e&&(f["\u0275\u0275elementStart"](0,"div",0),f["\u0275\u0275elementStart"](1,"div",1),f["\u0275\u0275elementStart"](2,"p"),f["\u0275\u0275text"](3),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](4,"p"),f["\u0275\u0275text"](5,"Total Rate Cards(for Applied Filters)"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](6,"div",2),f["\u0275\u0275template"](7,k,4,1,"div",3),f["\u0275\u0275template"](8,T,3,2,"span",4),f["\u0275\u0275template"](9,L,1,0,"ng-container",5),f["\u0275\u0275template"](10,D,3,0,"button",6),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](11,"app-cost-details-config",7),f["\u0275\u0275listener"]("rateCardCount",(function(e){return t.updateRateCardCount(e)}))("mpRcFieldConfig",(function(e){return t.updateRcFieldConfig(e,1)}))("nmpRcFieldConfig",(function(e){return t.updateRcFieldConfig(e,2)}))("liRcFieldConfig",(function(e){return t.updateRcFieldConfig(e,3)})),f["\u0275\u0275elementEnd"](),f["\u0275\u0275template"](12,W,4,3,"ng-template",null,8,f["\u0275\u0275templateRefExtractor"]),f["\u0275\u0275template"](14,ie,26,7,"ng-template",null,9,f["\u0275\u0275templateRefExtractor"]),f["\u0275\u0275template"](16,oe,19,23,"ng-template",null,10,f["\u0275\u0275templateRefExtractor"]),f["\u0275\u0275elementStart"](18,"mat-menu",null,11),f["\u0275\u0275elementStart"](20,"button",12),f["\u0275\u0275text"](21,"Upload Rate Cards"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]()),2&e){const e=f["\u0275\u0275reference"](13);f["\u0275\u0275advance"](3),f["\u0275\u0275textInterpolate"](t.rateCardCount),f["\u0275\u0275advance"](4),f["\u0275\u0275property"]("ngIf",!t.isDataGridViewActive),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",t.isRateCardUploadEnabled),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngTemplateOutlet",e),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",!t.isDataGridViewActive),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("isFromRateCard",!0)("manpowerMandatoryFields",t.rcMandatoryFieldList)("nmpMandatoryFields",t.nmpMandatoryFieldList)("licMandatoryFields",t.licMandatoryFieldList)("wEMappingEnabled",t.wEMappingEnabled)("sDMappingEnabled",t.sDMappingEnabled)}},directives:[i.NgIf,i.NgTemplateOutlet,S.a,h.g,o.a,u.h,a.a,c.e,c.v,c.k,r.a,u.i,I.b,i.NgForOf,i.NgClass,I.a,F.a,P.a,R.od,R.Qb,R.ae,R.dc,R.Cc,R.Jd,R.g,R.Wc],styles:[".page-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;padding:24px;height:85px}.page-header[_ngcontent-%COMP%]   .total-count[_ngcontent-%COMP%]{color:#45546e;font-size:14px}.page-header[_ngcontent-%COMP%]   .total-count[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-bottom:0!important}.page-header[_ngcontent-%COMP%]   .toolbar[_ngcontent-%COMP%]{flex:1;justify-content:end;grid-template-columns:repeat(4,auto);grid-gap:24px;display:grid;padding-inline:24px;align-items:center}.page-header[_ngcontent-%COMP%]   .toolbar[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:18px;cursor:pointer}.page-header[_ngcontent-%COMP%]   .toolbar[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]{color:#45546e;padding-right:12px;border-right:2px solid #e8e9ee;font-size:16px;height:32px;display:flex;justify-content:end;align-items:center}.page-header[_ngcontent-%COMP%]   .toolbar[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:18px;margin:0}.page-header[_ngcontent-%COMP%]   .toolbar[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .input-search[_ngcontent-%COMP%]{outline:none;border:none;min-width:65px;max-width:65px;background:none;color:inherit;transition:all 1.5s;width:auto;white-space:nowrap}.page-header[_ngcontent-%COMP%]   .toolbar[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .input-search[_ngcontent-%COMP%]:focus, .page-header[_ngcontent-%COMP%]   .toolbar[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .input-search[_ngcontent-%COMP%]:not(:placeholder-shown){min-width:150px;max-width:400px;width:auto;white-space:normal}.page-header[_ngcontent-%COMP%]   .toolbar[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%]   .input-search[_ngcontent-%COMP%]::placeholder{color:inherit;opacity:1}.body-card[_ngcontent-%COMP%]{border-radius:4px;border:1px solid #e8e9ee;background:#fff;height:calc(100vh - 215px);margin-inline:24px;display:flex;justify-content:center;align-items:center;flex-direction:column}.body-card[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{margin-bottom:24px}.body-card[_ngcontent-%COMP%]   .text-wrapper[_ngcontent-%COMP%]{display:flex;flex-direction:column}.body-card[_ngcontent-%COMP%]   .text-wrapper[_ngcontent-%COMP%]   .text-main[_ngcontent-%COMP%]{color:#45546e;text-align:center;font-size:16px;font-weight:700;line-height:24px;letter-spacing:.32px;text-transform:capitalize}.body-card[_ngcontent-%COMP%]   .text-wrapper[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]{color:#45546e;text-align:center;font-size:14px;font-weight:400;margin-bottom:0!important}.body-card[_ngcontent-%COMP%]   .create-action[_ngcontent-%COMP%]{display:flex;justify-content:space-between;width:315px}.create-btn[_ngcontent-%COMP%]{font-size:13px;color:#fff;font-weight:700!important;background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%);border-radius:4px}.mandatory-fields-dialog[_ngcontent-%COMP%]   .dialog-title[_ngcontent-%COMP%]{color:#45546e;font-size:16px;font-weight:500;line-height:24px;text-transform:capitalize}.more-btn[_ngcontent-%COMP%],   .create-action .mat-stroked-button:not(.mat-button-disabled){border-color:#45546e}.more-btn[_ngcontent-%COMP%]{min-width:unset;width:40px;display:flex;justify-content:center}.mandatory-field-styles[_ngcontent-%COMP%]   .create-btn[_ngcontent-%COMP%]{background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%)!important;color:#fff}.mandatory-field-styles[_ngcontent-%COMP%]   .create-btn[_ngcontent-%COMP%], .mandatory-field-styles[_ngcontent-%COMP%]   .create-btn-disabled[_ngcontent-%COMP%]{font-size:12px!important;line-height:30px;border-radius:4px}.mandatory-field-styles[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{background:#fff;font-size:12px!important;line-height:35px;border-radius:4px}.mandatory-field-styles[_ngcontent-%COMP%]   .text-class[_ngcontent-%COMP%]{color:#5f6c81;font-size:12px;font-style:normal;font-weight:400}.mandatory-field-styles[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{font-size:12px;font-weight:400;color:#45546e}.mandatory-field-styles[_ngcontent-%COMP%]   .service-icon-btn[_ngcontent-%COMP%]{font-size:18px;color:#908b8b}.mandatory-field-styles[_ngcontent-%COMP%]     .mat-slide-toggle-thumb{width:10px!important;height:10px!important;transform:translate(50%,50%)}.mandatory-field-styles[_ngcontent-%COMP%]     .mat-slide-toggle-bar{background-color:#dadce2;border-radius:15px!important;height:16px!important}.mandatory-field-styles[_ngcontent-%COMP%]     .mat-slide-toggle-thumb-container{top:-2px!important}.mandatory-field-styles[_ngcontent-%COMP%]     .mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-bar{background-color:#f27a6c}.mandatory-field-styles[_ngcontent-%COMP%]     .mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-thumb{background-color:#fff}.toggle-class[_ngcontent-%COMP%]{font-family:Plus Jakarta Sans;font-size:14px;font-weight:700;line-height:16px}.toggle-class[_ngcontent-%COMP%]   .toggle-btn[_ngcontent-%COMP%]{min-width:7rem;font-family:Plus Jakarta Sans!important}.toggle-class[_ngcontent-%COMP%]   .btn-toggle-selected[_ngcontent-%COMP%]{font-size:12px!important;background-color:#f27a6c!important;color:#fff}"]}),e})()},{path:"create",component:(()=>{class e{constructor(){}ngOnInit(){}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=f["\u0275\u0275defineComponent"]({type:e,selectors:[["app-details-page"]],decls:2,vars:0,template:function(e,t){1&e&&(f["\u0275\u0275elementStart"](0,"p"),f["\u0275\u0275text"](1,"details-page works!"),f["\u0275\u0275elementEnd"]())},styles:[""]}),e})()}];let re=(()=>{class e{}return e.\u0275mod=f["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=f["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[u.k.forChild(ae)],u.k]}),e})();var le=n("Xi0T"),se=n("1CHW"),de=n("WYlB");let ce=(()=>{class e{}return e.\u0275mod=f["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=f["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,re,le.a,h.e,d.c,s.b,c.p,c.E,m.h,p.g,g.h,r.b,l.e,a.b,o.b,F.b,se.RmAdminModule,P.b,de.b,I.c]]}),e})()},ucYs:function(e,t,n){"use strict";n.d(t,"a",(function(){return s}));var i=n("mrSG"),o=n("xG9w"),a=n("fXoL"),r=n("tk/3"),l=n("BVzC");let s=(()=>{class e{constructor(e,t){this.http=e,this.errorService=t,this.workflowStatusList=[],this.getWorkflowStatusList()}getWorkflowStatusList(){this.http.post("/api/wfPrimary/getWorkflowStatusList","").subscribe(e=>{this.workflowStatusList=e.data},e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server While Getting Workflow Status List",e&&e.params?e.params:e&&e.error?e.error.params:{})})}getWorkflowProperties(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/getWorkflowProperties",{applicationId:e}).subscribe(e=>t(e),e=>n(e))})}getWorkflowPropertiesByWorkflowId(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/getWorkflowPropertiesByWorkflowId",{workflowId:e}).subscribe(e=>t(e),e=>n(e))})}getApproversHierarchy(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/getApproversHierarchy",e).subscribe(e=>t(e),e=>n(e))})}createWorkflowItems(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/createWorkflowItems",e).subscribe(e=>t(e),e=>n(e))})}getWorkflowDetails(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/getWorkflowDetails",{workflowId:e}).subscribe(e=>t(e),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while getting workflow details",e&&e.params?e.params:e&&e.error?e.error.params:{})})})}formatApproversHierarchy(e,t){return Object(i.c)(this,void 0,void 0,(function*(){0==e.length&&t.cc0&&t.cc0.appr0&&t.cc0.appr0.length>0&&e.push([{approvalType:"I",purposeOfInsertingIntoArray:"Just to ensure that 'I' type data is used, if it exists"}]);for(let n=0;n<e.length;n++){let i=[],a=o.keys(t["cc"+n]);for(let o=0;o<a.length;o++)for(let r=0;r<t["cc"+n][a[o]].length;r++){let l={name:t["cc"+n][a[o]][r].DELEGATE_NAME,oid:t["cc"+n][a[o]][r].DELEGATE_OID,level:o+1,designation:t["cc"+n][a[o]][r].DELEGATE_DESIGNATION_NAME,is_delegated:t["cc"+n][a[o]][r].IS_DELEGATED,role:t["cc"+n][a[o]][r].DELEGATE_ROLE_NAME};if(1==t["cc"+n][a[o]][r].IS_DELEGATED&&(l.delegated_by={name:t["cc"+n][a[o]][r].APPROVER_NAME,oid:t["cc"+n][a[o]][r].APPROVER_OID,level:o+1,designation:t["cc"+n][a[o]][r].APPROVER_DESIGNATION_NAME}),i.push(l),n==e.length-1&&o==a.length-1&&r==t["cc"+n][a[o]].length-1)return i}}}))}storeComments(e,t,n){return new Promise((i,o)=>{this.http.post("/api/wfPrimary/updateComments",{workflowHeaderId:e,newComments:t,commentor:n}).subscribe(e=>i(e),e=>(this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server Error while Updating Workflow Comments",e&&e.params?e.params:e&&e.error?e.error.params:{}),o(e)))})}updateWorkflowItems(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/updateWorkflowItems",e).subscribe(e=>t(e),e=>(console.log(e),n(e)))})}formatApproversHierarchyForOpportunityApprovalActivity(e){return Object(i.c)(this,void 0,void 0,(function*(){for(let t=0;t<1;t++){let n=[],i=o.keys(e["cc"+t]);for(let o=0;o<i.length;o++)for(let a=0;a<e["cc"+t][i[o]].length;a++){let r={name:e["cc"+t][i[o]][a].DELEGATE_NAME,oid:e["cc"+t][i[o]][a].DELEGATE_OID,level:e["cc"+t][i[o]][a].APPROVAL_ORDER,designation:e["cc"+t][i[o]][a].DELEGATE_DESIGNATION_NAME,is_delegated:e["cc"+t][i[o]][a].IS_DELEGATED};if(1==e["cc"+t][i[o]][a].IS_DELEGATED&&(r.delegated_by={name:e["cc"+t][i[o]][a].APPROVER_NAME,oid:e["cc"+t][i[o]][a].APPROVER_OID,level:e["cc"+t][i[o]][a].APPROVAL_ORDER,designation:e["cc"+t][i[o]][a].APPROVER_DESIGNATION_NAME}),n.push(r),o==i.length-1&&a==e["cc"+t][i[o]].length-1)return n}}}))}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275inject"](r.c),a["\u0275\u0275inject"](l.a))},e.\u0275prov=a["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},w76M:function(e,t,n){"use strict";n.d(t,"a",(function(){return d})),n.d(t,"b",(function(){return c}));var i=n("jhN1"),o=n("fXoL"),a=n("oHs6"),r=n("PVOt"),l=n("6t9p");const s=["*"];let d=(()=>{let e=class extends r.b{constructor(e,t,n,i,o,a,r,l){super(e,t,n,i,r,l),this._watcherHelper=i,this._idh=o,this._createEventEmitters([{subscribe:"contentReady",emit:"onContentReady"},{subscribe:"disposing",emit:"onDisposing"},{subscribe:"hidden",emit:"onHidden"},{subscribe:"hiding",emit:"onHiding"},{subscribe:"initialized",emit:"onInitialized"},{subscribe:"optionChanged",emit:"onOptionChanged"},{subscribe:"resize",emit:"onResize"},{subscribe:"resizeEnd",emit:"onResizeEnd"},{subscribe:"resizeStart",emit:"onResizeStart"},{subscribe:"showing",emit:"onShowing"},{subscribe:"shown",emit:"onShown"},{subscribe:"titleRendered",emit:"onTitleRendered"},{emit:"accessKeyChange"},{emit:"animationChange"},{emit:"closeOnOutsideClickChange"},{emit:"containerChange"},{emit:"contentTemplateChange"},{emit:"deferRenderingChange"},{emit:"disabledChange"},{emit:"dragEnabledChange"},{emit:"elementAttrChange"},{emit:"focusStateEnabledChange"},{emit:"fullScreenChange"},{emit:"heightChange"},{emit:"hintChange"},{emit:"hoverStateEnabledChange"},{emit:"maxHeightChange"},{emit:"maxWidthChange"},{emit:"minHeightChange"},{emit:"minWidthChange"},{emit:"positionChange"},{emit:"resizeEnabledChange"},{emit:"rtlEnabledChange"},{emit:"shadingChange"},{emit:"shadingColorChange"},{emit:"showCloseButtonChange"},{emit:"showTitleChange"},{emit:"tabIndexChange"},{emit:"titleChange"},{emit:"titleTemplateChange"},{emit:"toolbarItemsChange"},{emit:"visibleChange"},{emit:"widthChange"}]),this._idh.setHost(this),a.setHost(this)}get accessKey(){return this._getOption("accessKey")}set accessKey(e){this._setOption("accessKey",e)}get animation(){return this._getOption("animation")}set animation(e){this._setOption("animation",e)}get closeOnOutsideClick(){return this._getOption("closeOnOutsideClick")}set closeOnOutsideClick(e){this._setOption("closeOnOutsideClick",e)}get container(){return this._getOption("container")}set container(e){this._setOption("container",e)}get contentTemplate(){return this._getOption("contentTemplate")}set contentTemplate(e){this._setOption("contentTemplate",e)}get deferRendering(){return this._getOption("deferRendering")}set deferRendering(e){this._setOption("deferRendering",e)}get disabled(){return this._getOption("disabled")}set disabled(e){this._setOption("disabled",e)}get dragEnabled(){return this._getOption("dragEnabled")}set dragEnabled(e){this._setOption("dragEnabled",e)}get elementAttr(){return this._getOption("elementAttr")}set elementAttr(e){this._setOption("elementAttr",e)}get focusStateEnabled(){return this._getOption("focusStateEnabled")}set focusStateEnabled(e){this._setOption("focusStateEnabled",e)}get fullScreen(){return this._getOption("fullScreen")}set fullScreen(e){this._setOption("fullScreen",e)}get height(){return this._getOption("height")}set height(e){this._setOption("height",e)}get hint(){return this._getOption("hint")}set hint(e){this._setOption("hint",e)}get hoverStateEnabled(){return this._getOption("hoverStateEnabled")}set hoverStateEnabled(e){this._setOption("hoverStateEnabled",e)}get maxHeight(){return this._getOption("maxHeight")}set maxHeight(e){this._setOption("maxHeight",e)}get maxWidth(){return this._getOption("maxWidth")}set maxWidth(e){this._setOption("maxWidth",e)}get minHeight(){return this._getOption("minHeight")}set minHeight(e){this._setOption("minHeight",e)}get minWidth(){return this._getOption("minWidth")}set minWidth(e){this._setOption("minWidth",e)}get position(){return this._getOption("position")}set position(e){this._setOption("position",e)}get resizeEnabled(){return this._getOption("resizeEnabled")}set resizeEnabled(e){this._setOption("resizeEnabled",e)}get rtlEnabled(){return this._getOption("rtlEnabled")}set rtlEnabled(e){this._setOption("rtlEnabled",e)}get shading(){return this._getOption("shading")}set shading(e){this._setOption("shading",e)}get shadingColor(){return this._getOption("shadingColor")}set shadingColor(e){this._setOption("shadingColor",e)}get showCloseButton(){return this._getOption("showCloseButton")}set showCloseButton(e){this._setOption("showCloseButton",e)}get showTitle(){return this._getOption("showTitle")}set showTitle(e){this._setOption("showTitle",e)}get tabIndex(){return this._getOption("tabIndex")}set tabIndex(e){this._setOption("tabIndex",e)}get title(){return this._getOption("title")}set title(e){this._setOption("title",e)}get titleTemplate(){return this._getOption("titleTemplate")}set titleTemplate(e){this._setOption("titleTemplate",e)}get toolbarItems(){return this._getOption("toolbarItems")}set toolbarItems(e){this._setOption("toolbarItems",e)}get visible(){return this._getOption("visible")}set visible(e){this._setOption("visible",e)}get width(){return this._getOption("width")}set width(e){this._setOption("width",e)}get toolbarItemsChildren(){return this._getOption("toolbarItems")}set toolbarItemsChildren(e){this.setChildren("toolbarItems",e)}_createInstance(e,t){return new a.a(e,t)}ngOnDestroy(){this._destroyWidget()}ngOnChanges(e){super.ngOnChanges(e),this.setupChanges("toolbarItems",e)}setupChanges(e,t){e in this._optionsToUpdate||this._idh.setup(e,t)}ngDoCheck(){this._idh.doCheck("toolbarItems"),this._watcherHelper.checkWatchers(),super.ngDoCheck(),super.clearChangedOptions()}_setOption(e,t){let n=this._idh.setupSingle(e,t),i=null!==this._idh.getChanges(e,t);(n||i)&&super._setOption(e,t)}};return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](o.ElementRef),o["\u0275\u0275directiveInject"](o.NgZone),o["\u0275\u0275directiveInject"](r.e),o["\u0275\u0275directiveInject"](r.j),o["\u0275\u0275directiveInject"](r.g),o["\u0275\u0275directiveInject"](r.i),o["\u0275\u0275directiveInject"](i.h),o["\u0275\u0275directiveInject"](o.PLATFORM_ID))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["dx-popup"]],contentQueries:function(e,t,n){if(1&e&&o["\u0275\u0275contentQuery"](n,l.L,!1),2&e){let e;o["\u0275\u0275queryRefresh"](e=o["\u0275\u0275loadQuery"]())&&(t.toolbarItemsChildren=e)}},inputs:{accessKey:"accessKey",animation:"animation",closeOnOutsideClick:"closeOnOutsideClick",container:"container",contentTemplate:"contentTemplate",deferRendering:"deferRendering",disabled:"disabled",dragEnabled:"dragEnabled",elementAttr:"elementAttr",focusStateEnabled:"focusStateEnabled",fullScreen:"fullScreen",height:"height",hint:"hint",hoverStateEnabled:"hoverStateEnabled",maxHeight:"maxHeight",maxWidth:"maxWidth",minHeight:"minHeight",minWidth:"minWidth",position:"position",resizeEnabled:"resizeEnabled",rtlEnabled:"rtlEnabled",shading:"shading",shadingColor:"shadingColor",showCloseButton:"showCloseButton",showTitle:"showTitle",tabIndex:"tabIndex",title:"title",titleTemplate:"titleTemplate",toolbarItems:"toolbarItems",visible:"visible",width:"width"},outputs:{onContentReady:"onContentReady",onDisposing:"onDisposing",onHidden:"onHidden",onHiding:"onHiding",onInitialized:"onInitialized",onOptionChanged:"onOptionChanged",onResize:"onResize",onResizeEnd:"onResizeEnd",onResizeStart:"onResizeStart",onShowing:"onShowing",onShown:"onShown",onTitleRendered:"onTitleRendered",accessKeyChange:"accessKeyChange",animationChange:"animationChange",closeOnOutsideClickChange:"closeOnOutsideClickChange",containerChange:"containerChange",contentTemplateChange:"contentTemplateChange",deferRenderingChange:"deferRenderingChange",disabledChange:"disabledChange",dragEnabledChange:"dragEnabledChange",elementAttrChange:"elementAttrChange",focusStateEnabledChange:"focusStateEnabledChange",fullScreenChange:"fullScreenChange",heightChange:"heightChange",hintChange:"hintChange",hoverStateEnabledChange:"hoverStateEnabledChange",maxHeightChange:"maxHeightChange",maxWidthChange:"maxWidthChange",minHeightChange:"minHeightChange",minWidthChange:"minWidthChange",positionChange:"positionChange",resizeEnabledChange:"resizeEnabledChange",rtlEnabledChange:"rtlEnabledChange",shadingChange:"shadingChange",shadingColorChange:"shadingColorChange",showCloseButtonChange:"showCloseButtonChange",showTitleChange:"showTitleChange",tabIndexChange:"tabIndexChange",titleChange:"titleChange",titleTemplateChange:"titleTemplateChange",toolbarItemsChange:"toolbarItemsChange",visibleChange:"visibleChange",widthChange:"widthChange"},features:[o["\u0275\u0275ProvidersFeature"]([r.e,r.j,r.i,r.g]),o["\u0275\u0275InheritDefinitionFeature"],o["\u0275\u0275NgOnChangesFeature"]],ngContentSelectors:s,decls:1,vars:0,template:function(e,t){1&e&&(o["\u0275\u0275projectionDef"](),o["\u0275\u0275projection"](0))},encapsulation:2}),e})(),c=(()=>{let e=class{};return e.\u0275mod=o["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=o["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[l.bb,l.Gc,l.Vd,l.vd,l.hb,l.lb,l.sb,l.id,l.jd,l.M,r.c,r.f,i.b],l.bb,l.Gc,l.Vd,l.vd,l.hb,l.lb,l.sb,l.id,l.jd,l.M,r.f]}),e})()}}]);