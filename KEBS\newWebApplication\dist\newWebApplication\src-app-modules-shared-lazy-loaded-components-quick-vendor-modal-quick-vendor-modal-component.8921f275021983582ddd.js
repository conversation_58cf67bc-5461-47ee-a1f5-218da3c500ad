(window.webpackJsonp=window.webpackJsonp||[]).push([[993],{"TmG/":function(t,e,n){"use strict";n.d(e,"a",(function(){return C}));var o=n("fXoL"),i=n("3Pt+"),r=n("jtHE"),a=n("XNiG"),s=n("NJ67"),c=n("1G5W"),l=n("kmnG"),d=n("ofXK"),p=n("d3UM"),m=n("FKr1"),g=n("WJ5W"),u=n("Qu3c");function f(t,e){if(1&t&&(o["\u0275\u0275elementStart"](0,"mat-label"),o["\u0275\u0275text"](1),o["\u0275\u0275elementEnd"]()),2&t){const t=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate"](t.placeholder)}}function h(t,e){if(1&t&&(o["\u0275\u0275elementStart"](0,"mat-option",7),o["\u0275\u0275text"](1),o["\u0275\u0275elementEnd"]()),2&t){const t=o["\u0275\u0275nextContext"]();o["\u0275\u0275property"]("value",null),o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate"](t.disableNone?"Select One":"None")}}function b(t,e){if(1&t){const t=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"mat-option",8),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](t);const n=e.$implicit;return o["\u0275\u0275nextContext"]().emitChanges(n)})),o["\u0275\u0275text"](1),o["\u0275\u0275elementEnd"]()}if(2&t){const t=e.$implicit;o["\u0275\u0275property"]("value",t.id||t._id)("matTooltip",t.name),o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" ",t.name," ")}}let C=(()=>{class t extends s.a{constructor(t){super(),this.renderer=t,this.fieldCtrl=new i.j,this.fieldFilterCtrl=new i.j,this.list=[],this.required=!1,this.disableNone=!1,this.valueChange=new o.EventEmitter,this.disabled=!1,this.hasNoneOption=!0,this.filteredList=new r.a,this.change=new o.EventEmitter,this.hideMatLabel=!1,this._onDestroy=new a.b,this.emitChanges=t=>{this.change.emit(t)}}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(c.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(c.a)(this._onDestroy)).subscribe(t=>{this.value=t,this.onChange(t),this.valueChange.emit(t)})}ngOnChanges(){this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let t=this.fieldFilterCtrl.value;t?(t=t.toLowerCase(),this.filteredList.next(this.list.filter(e=>e.name.toLowerCase().indexOf(t)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(t){t?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(t){this.fieldCtrl.setValue(t)}}return t.\u0275fac=function(e){return new(e||t)(o["\u0275\u0275directiveInject"](o.Renderer2))},t.\u0275cmp=o["\u0275\u0275defineComponent"]({type:t,selectors:[["app-input-search"]],inputs:{list:"list",placeholder:"placeholder",required:"required",disableNone:"disableNone",disabled:"disabled",hasNoneOption:"hasNoneOption",hideMatLabel:"hideMatLabel"},outputs:{valueChange:"valueChange",change:"change"},features:[o["\u0275\u0275ProvidersFeature"]([{provide:i.t,useExisting:Object(o.forwardRef)(()=>t),multi:!0}]),o["\u0275\u0275InheritDefinitionFeature"],o["\u0275\u0275NgOnChangesFeature"]],decls:9,vars:11,consts:[["appearance","outline"],[4,"ngIf"],[3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value",4,"ngIf"],[3,"value","matTooltip","click",4,"ngFor","ngForOf"],[3,"value"],[3,"value","matTooltip","click"]],template:function(t,e){1&t&&(o["\u0275\u0275elementStart"](0,"mat-form-field",0),o["\u0275\u0275template"](1,f,2,1,"mat-label",1),o["\u0275\u0275elementStart"](2,"mat-select",2,3),o["\u0275\u0275elementStart"](4,"mat-option"),o["\u0275\u0275element"](5,"ngx-mat-select-search",4),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](6,h,2,2,"mat-option",5),o["\u0275\u0275template"](7,b,2,3,"mat-option",6),o["\u0275\u0275pipe"](8,"async"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&t&&(o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",!e.hideMatLabel),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("formControl",e.fieldCtrl)("placeholder",e.placeholder)("required",e.required)("disabled",e.disabled),o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("formControl",e.fieldFilterCtrl)("placeholderLabel",e.placeholder),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",e.hasNoneOption),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngForOf",o["\u0275\u0275pipeBind1"](8,9,e.filteredList)))},directives:[l.c,d.NgIf,p.c,i.v,i.k,i.F,m.p,g.a,d.NgForOf,l.g,u.a],pipes:[d.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%}"]}),t})()},"tx/t":function(t,e,n){"use strict";n.r(e),n.d(e,"QuickVendorModalComponent",(function(){return L})),n.d(e,"QuickVendorModalmodule",(function(){return F}));var o=n("mrSG"),i=n("33Jv"),r=n("PSD3"),a=n.n(r),s=n("Xa2L"),c=n("7EHt"),l=n("ofXK"),d=n("iadO"),p=n("bTqV"),m=n("Qu3c"),g=n("lVl8"),u=n("3Pt+"),f=n("STbY"),h=n("d3UM"),b=n("NFeN"),C=n("kmnG"),x=n("qFsG"),O=n("/1cH"),_=n("Xi0T"),y=n("1yaQ"),M=n("FKr1"),P=n("QibW"),k=n("7pIB"),v=n("0IaG"),q=n("fXoL"),w=n("JLuW"),S=n("1A3m"),E=n("TmG/");function I(t,e){if(1&t){const t=q["\u0275\u0275getCurrentView"]();q["\u0275\u0275elementStart"](0,"button",20),q["\u0275\u0275listener"]("click",(function(){return q["\u0275\u0275restoreView"](t),q["\u0275\u0275nextContext"]().saveVendor()})),q["\u0275\u0275elementStart"](1,"mat-icon"),q["\u0275\u0275text"](2,"done_all"),q["\u0275\u0275elementEnd"](),q["\u0275\u0275elementEnd"]()}}function z(t,e){1&t&&(q["\u0275\u0275elementStart"](0,"div",21),q["\u0275\u0275element"](1,"mat-spinner",22),q["\u0275\u0275elementEnd"]())}let L=(()=>{class t{constructor(t,e,n,o){this.fb=t,this.dialogRef=e,this.SharedLazyLoadedComponentsService=n,this._toaster=o,this.subs=new i.a,this.disableVendorType=!0,this.vendor="Vendor",this.kebsuserroles=[],this.isLoading=!1}ngOnInit(){return Object(o.c)(this,void 0,void 0,(function*(){this.createForm(),this.vendorquickForm.patchValue({employeetype:"Vendor"}),this.getSetAssocID(),this.kebsuserroles=yield this.getKebsUserRole(),this.kebsuserroles=this.kebsuserroles.data}))}getSetAssocID(){return Object(o.c)(this,void 0,void 0,(function*(){let t=yield this.getAssocID();t.err||(this.assoc_ID=t)}))}getKebsUserRole(){return new Promise((t,e)=>{this.subs.sink=this.SharedLazyLoadedComponentsService.getKebsuserrole().subscribe(n=>{n.err?(console.log("Response data rejected",n),e(n)):t(n)},t=>{console.log(t),e(t)})})}getAssocID(){return new Promise((t,e)=>{this.subs.sink=this.SharedLazyLoadedComponentsService.getVendorID().subscribe(n=>{n.err?e(n):t(n)},t=>{console.log(t),e(t)})})}closeQuickvendor(){this.resetControl(),this.dialogRef.close({event:"Close"})}resetControl(){this.vendorquickForm.enable({emitEvent:!1}),this.vendorquickForm.clearValidators()}createForm(){this.vendorquickForm=this.fb.group({employeename:["",u.H.required],employeetype:[{value:"",disabled:!0}],emailid:["",u.H.required],KebsUserRole:["",u.H.required]})}saveVendor(){if(this.vendorquickForm.valid){this.isLoading=!0,this.vendor_OID=this.assoc_ID;let t={email:this.vendorquickForm.get("emailid").value,vendorName:this.vendorquickForm.get("employeename").value,oid:this.vendor_OID.data,role_id:this.vendorquickForm.get("KebsUserRole").value};console.log("DATAVAUE",t),this.subs.sink=this.SharedLazyLoadedComponentsService.addNewVendor(t).subscribe(t=>{this.isLoading=!1,t.err||"S"!=t.messType?this._toaster.showError("Error","Failed to create vendor !"+t.messText,2e3):(a.a.fire("Vendor creation successful!"),this._toaster.showSuccess("Success","Vendor created successfully!",2e3),this.closeQuickvendor())},t=>{this.isLoading=!1,this._toaster.showError("Error","Failed to create vendor !, Kindly contact KEBS team to resolve",2e3)})}else this.markFormGroupTouched(this.vendorquickForm),this._toaster.showWarning("Invalid data","Kindly fill all mandatory fields to proceed !")}markFormGroupTouched(t){Object.values(t.controls).forEach(t=>{t.markAsTouched(),t.controls&&this.markFormGroupTouched(t)})}}return t.\u0275fac=function(e){return new(e||t)(q["\u0275\u0275directiveInject"](u.i),q["\u0275\u0275directiveInject"](v.h),q["\u0275\u0275directiveInject"](w.a),q["\u0275\u0275directiveInject"](S.a))},t.\u0275cmp=q["\u0275\u0275defineComponent"]({type:t,selectors:[["app-quick-vendor-modal"]],decls:34,vars:5,consts:[[1,"container-fluid","quick-isa-styles"],[1,"row",2,"border-bottom","solid 1px #cacaca"],[1,"col-9","pt-1","pb-2","d-flex","pl-2"],["mat-icon-button","",1,"bubble","mt-1"],[1,"iconButton"],[1,"headingBold","my-auto","pt-2","ml-3"],[1,"col-3","d-flex",2,"text-align","end"],["matTooltip","Close","mat-icon-button","",1,"ml-auto","close-button","mt-1",3,"click"],[1,"close-Icon"],[3,"formGroup"],[1,"row","pt-3"],[1,"col-6"],["appearance","outline",1,"panel-form","slide-in-right"],["type","text","matInput","","placeholder","Vendor Name","formControlName","employeename"],["type","text","matInput","","placeholder","Vendor Type","formControlName","employeetype","disabled","",3,"disabled"],["type","email","matInput","","placeholder","Email","formControlName","emailid"],["required","true","placeholder","KEBS User Role","formControlName","KebsUserRole",2,"width","100%",3,"list"],[2,"float","right"],["mat-icon-button","","matTooltip","Create Vendor","class","iconbtn mr-2","style","font-weight: normal; margin-left: 1%; margin-bottom: 1%;",3,"click",4,"ngIf"],["fxLayout","row","fxLayoutAlign","center","style","height:100%",4,"ngIf"],["mat-icon-button","","matTooltip","Create Vendor",1,"iconbtn","mr-2",2,"font-weight","normal","margin-left","1%","margin-bottom","1%",3,"click"],["fxLayout","row","fxLayoutAlign","center",2,"height","100%"],["diameter","30","strokeWidth","5"]],template:function(t,e){1&t&&(q["\u0275\u0275elementStart"](0,"div",0),q["\u0275\u0275elementStart"](1,"div",1),q["\u0275\u0275elementStart"](2,"div",2),q["\u0275\u0275elementStart"](3,"div",3),q["\u0275\u0275elementStart"](4,"mat-icon",4),q["\u0275\u0275text"](5,"person"),q["\u0275\u0275elementEnd"](),q["\u0275\u0275elementEnd"](),q["\u0275\u0275elementStart"](6,"span",5),q["\u0275\u0275text"](7," Create New Resource Vendor Login "),q["\u0275\u0275elementEnd"](),q["\u0275\u0275elementEnd"](),q["\u0275\u0275elementStart"](8,"div",6),q["\u0275\u0275elementStart"](9,"button",7),q["\u0275\u0275listener"]("click",(function(){return e.closeQuickvendor()})),q["\u0275\u0275elementStart"](10,"mat-icon",8),q["\u0275\u0275text"](11,"close"),q["\u0275\u0275elementEnd"](),q["\u0275\u0275elementEnd"](),q["\u0275\u0275elementEnd"](),q["\u0275\u0275elementEnd"](),q["\u0275\u0275elementStart"](12,"form",9),q["\u0275\u0275elementStart"](13,"div",10),q["\u0275\u0275elementStart"](14,"div",11),q["\u0275\u0275elementStart"](15,"mat-form-field",12),q["\u0275\u0275elementStart"](16,"mat-label"),q["\u0275\u0275text"](17,"Vendor Name *"),q["\u0275\u0275elementEnd"](),q["\u0275\u0275element"](18,"input",13),q["\u0275\u0275elementEnd"](),q["\u0275\u0275elementEnd"](),q["\u0275\u0275elementStart"](19,"div",11),q["\u0275\u0275elementStart"](20,"mat-form-field",12),q["\u0275\u0275elementStart"](21,"mat-label"),q["\u0275\u0275text"](22,"Vendor Type"),q["\u0275\u0275elementEnd"](),q["\u0275\u0275element"](23,"input",14),q["\u0275\u0275elementEnd"](),q["\u0275\u0275elementEnd"](),q["\u0275\u0275elementStart"](24,"div",11),q["\u0275\u0275elementStart"](25,"mat-form-field",12),q["\u0275\u0275elementStart"](26,"mat-label"),q["\u0275\u0275text"](27,"Email ID *"),q["\u0275\u0275elementEnd"](),q["\u0275\u0275element"](28,"input",15),q["\u0275\u0275elementEnd"](),q["\u0275\u0275elementEnd"](),q["\u0275\u0275elementStart"](29,"div",11),q["\u0275\u0275element"](30,"app-input-search",16),q["\u0275\u0275elementEnd"](),q["\u0275\u0275elementEnd"](),q["\u0275\u0275elementEnd"](),q["\u0275\u0275elementStart"](31,"div",17),q["\u0275\u0275template"](32,I,3,0,"button",18),q["\u0275\u0275template"](33,z,2,0,"div",19),q["\u0275\u0275elementEnd"](),q["\u0275\u0275elementEnd"]()),2&t&&(q["\u0275\u0275advance"](12),q["\u0275\u0275property"]("formGroup",e.vendorquickForm),q["\u0275\u0275advance"](11),q["\u0275\u0275property"]("disabled",!0),q["\u0275\u0275advance"](7),q["\u0275\u0275property"]("list",e.kebsuserroles),q["\u0275\u0275advance"](2),q["\u0275\u0275property"]("ngIf",!e.isLoading),q["\u0275\u0275advance"](1),q["\u0275\u0275property"]("ngIf",e.isLoading))},directives:[b.a,p.a,m.a,u.J,u.w,u.n,C.c,C.g,x.b,u.e,u.v,u.l,E.a,u.F,l.NgIf,s.c],styles:['.quick-isa-styles[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]{font-size:16px;font-weight:500;color:#45546e}.quick-isa-styles[_ngcontent-%COMP%]   .field-title[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#6e7b8f}.quick-isa-styles[_ngcontent-%COMP%]   .add-link[_ngcontent-%COMP%]{text-decoration:underline;color:#cf0001;font-size:14px;cursor:pointer;font-weight:500}.quick-isa-styles[_ngcontent-%COMP%]   .bg-image[_ngcontent-%COMP%]{background-image:url(create_cta.63f4b67d475babab024d.png);background-size:230px 200px;background-repeat:no-repeat;background-position:100% 78%}.quick-isa-styles[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]{display:flex;line-height:26px;border-radius:50%;cursor:context-menu!important;width:26px;height:26px;background-color:#fbfbfb;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.quick-isa-styles[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#cf0001;font-size:18px;margin-top:3px!important;margin-left:4px!important}.quick-isa-styles[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.quick-isa-styles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}.quick-isa-styles[_ngcontent-%COMP%]   .headingBold[_ngcontent-%COMP%]{color:#cf0001;font-weight:500;font-size:14px}.quick-isa-styles[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]{color:#cf0001;font-weight:500;font-size:13px}.quick-isa-styles[_ngcontent-%COMP%]   .nameBold[_ngcontent-%COMP%]{color:#000;font-weight:500;font-size:13px;display:flex;flex-direction:column}.quick-isa-styles[_ngcontent-%COMP%]   .name[_ngcontent-%COMP%]{color:#000}.quick-isa-styles[_ngcontent-%COMP%]   .cta-name[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:75%;font-size:13px}.quick-isa-styles[_ngcontent-%COMP%]   .cta-option[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:100%;font-size:13px}.quick-isa-styles[_ngcontent-%COMP%]   .cta-option[_ngcontent-%COMP%]     .mat-form-field-flex{height:38px}.quick-isa-styles[_ngcontent-%COMP%]   .radio-button-group[_ngcontent-%COMP%]     .mat-radio-checked .mat-radio-outer-circle{border-color:#cf0001}.quick-isa-styles[_ngcontent-%COMP%]   .radio-button-group[_ngcontent-%COMP%]     .mat-radio-checked .mat-radio-inner-circle{background-color:#cf0001}.quick-isa-styles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff}.quick-isa-styles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%], .quick-isa-styles[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]{font-weight:400;font-size:13px!important;min-width:60px;line-height:33px;padding:0 8px;border-radius:4px;margin-right:6px!important;margin-bottom:3px}.quick-isa-styles[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]{color:rgba(0,0,0,.534);background-color:rgba(194,193,193,.24)}.quick-isa-styles[_ngcontent-%COMP%]   .btn-spinner[_ngcontent-%COMP%]{margin-left:4px}.quick-isa-styles[_ngcontent-%COMP%]   .view-btn-not-active[_ngcontent-%COMP%]{color:#1a1a1a}.quick-isa-styles[_ngcontent-%COMP%]   .view-btn-active[_ngcontent-%COMP%], .quick-isa-styles[_ngcontent-%COMP%]   .view-btn-not-active[_ngcontent-%COMP%]{margin-right:6px!important;margin-bottom:3px;font-weight:400;font-size:13px!important;min-width:6rem;line-height:33px;padding:0 8px;border-radius:4px}.quick-isa-styles[_ngcontent-%COMP%]   .view-btn-active[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff}.quick-isa-styles[_ngcontent-%COMP%]   .custom-ts-select[_ngcontent-%COMP%]{border:1px solid grey;padding:5px 0!important}.quick-isa-styles[_ngcontent-%COMP%]   .custom-ts-select[_ngcontent-%COMP%], .quick-isa-styles[_ngcontent-%COMP%]   .custom-ts-select-option[_ngcontent-%COMP%]{text-align:center;font-size:13px;color:#1a1a1a}.quick-isa-styles[_ngcontent-%COMP%]   .custom-ts-search-input[_ngcontent-%COMP%]{padding:12px 5px 8px;cursor:pointer}.quick-isa-styles[_ngcontent-%COMP%]   .custom-ts-search[_ngcontent-%COMP%]{width:100%;text-align:center!important;font-size:14px;color:#1a1a1a}.quick-isa-styles[_ngcontent-%COMP%]   .custom-ts-search[_ngcontent-%COMP%]   .mat-form-field-wrapper[_ngcontent-%COMP%]   .mat-form-field-flex[_ngcontent-%COMP%]{padding:5px!important}.quick-isa-styles[_ngcontent-%COMP%]   .adjust-form-field[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding-bottom:8px!important}.quick-isa-styles[_ngcontent-%COMP%]   .adjust-form-field[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-form-field-underline{bottom:0}.quick-isa-styles[_ngcontent-%COMP%]   .adjust-form-field[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-form-field-infix{padding:.65em 0!important;border-top:.54375em solid transparent!important;font-size:13px}.quick-isa-styles[_ngcontent-%COMP%]   .adjust-form-field[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-select-arrow{margin:0 4px!important}.quick-isa-styles[_ngcontent-%COMP%]   .upload-box[_ngcontent-%COMP%]{min-height:128px;border:solid;border-color:#d6d6d6;border-width:1px;border-radius:4px}.quick-isa-styles[_ngcontent-%COMP%]   .my-drop-zone[_ngcontent-%COMP%]{border:3px dotted #d3d3d3}.quick-isa-styles[_ngcontent-%COMP%]   .nv-file-over[_ngcontent-%COMP%]{border:3px dotted red}.quick-isa-styles[_ngcontent-%COMP%]   .upload-icon[_ngcontent-%COMP%]{position:absolute;opacity:.1;cursor:pointer;font-size:6rem}.quick-isa-styles[_ngcontent-%COMP%]   .slide-in-right[_ngcontent-%COMP%]{animation:slide-in-right .3s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-right{0%{transform:translateX(30px);opacity:0}to{transform:translateX(0);opacity:1}}.quick-isa-styles[_ngcontent-%COMP%]   .person-pin[_ngcontent-%COMP%]{color:#66615b!important;font-size:22px!important}.quick-isa-styles[_ngcontent-%COMP%]   .info-text[_ngcontent-%COMP%]{color:#9a9a9a;font-size:12px;padding-left:0}.quick-isa-styles[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%]{width:100%;max-height:15%!important;font-size:13px}.quick-isa-styles[_ngcontent-%COMP%]   .raised-button[_ngcontent-%COMP%]{min-width:5rem}.quick-isa-styles[_ngcontent-%COMP%]   .mini-tick[_ngcontent-%COMP%]{height:42px!important;width:42px!important;line-height:42px;background-color:#cf0001!important;color:#fff!important;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.quick-isa-styles[_ngcontent-%COMP%]   .tooltip-text[_ngcontent-%COMP%]{font-size:12px;font-family:Roboto,Helvetica Neue,"sans-serif"!important}.quick-isa-styles[_ngcontent-%COMP%]   .headingText[_ngcontent-%COMP%]{color:#66615b;font-size:11px}.quick-isa-styles[_ngcontent-%COMP%]   .create-claim-field[_ngcontent-%COMP%]{font-size:14px!important;width:100%!important}.quick-isa-styles[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{color:#5f5f5f;font-size:12px!important}.quick-isa-styles[_ngcontent-%COMP%]   .listcard[_ngcontent-%COMP%]{transition:all .25s linear;height:42px;max-height:42px!important}.quick-isa-styles[_ngcontent-%COMP%]   .listcard[_ngcontent-%COMP%]:hover{box-shadow:0 4px 8px 0 rgba(0,0,0,.2),0 6px 20px 0 rgba(0,0,0,.19);cursor:pointer}.quick-isa-styles[_ngcontent-%COMP%]   .listcard[_ngcontent-%COMP%]:hover   .icon-tray-button[_ngcontent-%COMP%]{visibility:visible!important;animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}.quick-isa-styles[_ngcontent-%COMP%]   .icon-tray-button[_ngcontent-%COMP%]{width:27px!important;height:27px!important;line-height:25px!important;visibility:hidden}.quick-isa-styles[_ngcontent-%COMP%]   .mat-icon-tick[_ngcontent-%COMP%]{color:#9a9a9a;font-size:15px;text-align:left!important;font-weight:lighter}.quick-isa-styles[_ngcontent-%COMP%]   .normalText[_ngcontent-%COMP%]{font-size:14px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.quick-isa-styles[_ngcontent-%COMP%]   .adjust-inline-field[_ngcontent-%COMP%]{height:40px;width:100%}.quick-isa-styles[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#3c3c3c;font-weight:400;font-size:18px}.quick-isa-styles[_ngcontent-%COMP%]   .smallCardIcon[_ngcontent-%COMP%]{font-size:18px!important;color:#868683!important}.quick-isa-styles[_ngcontent-%COMP%]     .card-panel .mat-menu-content .col-styles-lead{cursor:pointer!important;border-radius:4px!important}.quick-isa-styles[_ngcontent-%COMP%]     .card-panel .mat-menu-content .col-styles-lead:hover{background-color:rgba(255,235,235,.6705882352941176)!important}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}.quick-isa-styles[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}.quick-isa-styles[_ngcontent-%COMP%]     .mat-expansion-panel{padding:0!important}.quick-isa-styles[_ngcontent-%COMP%]     .mat-expansion-panel-body{padding:0 24px!important}.quick-isa-styles[_ngcontent-%COMP%]     .mat-expansion-panel-header{background:#f5f5f5!important;height:auto!important}.quick-isa-styles[_ngcontent-%COMP%]   .panel-form[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:100%;font-size:14px}.quick-isa-styles[_ngcontent-%COMP%]   .file-present[_ngcontent-%COMP%]{border:3px dotted #e44a4a;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.quick-isa-styles[_ngcontent-%COMP%]   .file-present[_ngcontent-%COMP%]   .cloud-icon[_ngcontent-%COMP%]{color:#e44a4a}.quick-isa-styles[_ngcontent-%COMP%]   .file-empty[_ngcontent-%COMP%]{border:2px solid #cacaca}.quick-isa-styles[_ngcontent-%COMP%]   .cloud-icon[_ngcontent-%COMP%]{font-size:4rem;color:#b8b7b5;display:flex;align-items:center;justify-content:center}.quick-isa-styles[_ngcontent-%COMP%]   .upload-btn[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff;font-weight:400;width:7rem;line-height:10px;text-align:center;overflow:hidden;height:30px;font-size:12px;text-overflow:ellipsis;white-space:nowrap}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}.quick-isa-styles[_ngcontent-%COMP%]   .iconbtn[_ngcontent-%COMP%]{background-color:#c92020;color:#fff;padding:0;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}']}),t})(),F=(()=>{class t{}return t.\u0275mod=q["\u0275\u0275defineNgModule"]({type:t}),t.\u0275inj=q["\u0275\u0275defineInjector"]({factory:function(e){return new(e||t)},providers:[{provide:d.b,useValue:{}},{provide:O.a,useValue:{}},{provide:h.a,useValue:{}}],imports:[[l.CommonModule,p.b,y.b,c.b,M.n,u.E,m.b,g.b,b.b,h.d,f.e,d.h,s.b,C.e,x.c,O.c,_.a,v.g,P.c,k.c]]}),t})()}}]);