(window.webpackJsonp=window.webpackJsonp||[]).push([[743,733],{A6Kz:function(t,e,n){"use strict";n.d(e,"a",(function(){return c}));var o=n("XNiG"),i=n("z6cu"),a=n("xG9w"),s=n("fXoL"),r=n("tk/3"),d=n("flaP");let c=(()=>{class t{constructor(t,e){this.http=t,this.roleService=e,this.messageSharingSubject=new o.b,this.getTranslatedPdfDetail=(t,e,n)=>this.http.post("/api/invoice/otherLanguagePdfDetails",{billingId:t,languageId:e,billingIdFlag:n}),this.getAvailableBankAddress=t=>this.http.post("/api/invoice/stepperAddressChange",{legalEntityId:t,id:3}),this.saveEditedInvoice=(t,e,n)=>this.http.post("/api/invoice/editInvoicePdf",{billingId:t,pdfDetails:e,languageId:n}),this.getAvailableBanks=t=>this.http.post("/api/invoice/bankCrudOperations",{legalEntityId:t,crudId:2}),this.getAvailableFromAddress=t=>this.http.post("/api/invoice/stepperAddressChange",{legalEntityId:t,id:1}),this.getAvailableToAddress=t=>this.http.post("/api/invoice/stepperAddressChange",{customerId:t,id:2}),this.getTranslatedAddressForEnAddress=(t,e,n)=>this.http.post("/api/invoice/getFromAddressDetails",{legalEntityId:t,fromAddressId:e,languageId:n}),this.getTranslatedToAddressForEnAddress=(t,e,n)=>this.http.post("/api/invoice/getToAddressDetails",{customerId:t,toAddressId:e,languageId:n}),this.getTranslatedAddressForAbank=(t,e,n)=>this.http.post("/api/invoice/getBankLanguageDetails",{legalEntityId:t,bankId:e,languageId:n}),this.sendMsg=t=>{this.messageSharingSubject.next(t)},this.getMsg=()=>this.messageSharingSubject.asObservable(),this.generateEngPdf=t=>{this.http.post("/api/invoice/generateEnglishPDF",{pdf_details:t},{responseType:"blob"}).subscribe(t=>{var e=new Blob([t],{type:"application/pdf"}),n=URL.createObjectURL(e);window.open(n)},t=>{Object(i.a)(t)})},this.generateArabicPdf=t=>{this.http.post("/api/invoice/generateArabicPDF",{pdf_details:t},{responseType:"blob"}).subscribe(t=>{var e=new Blob([t],{type:"application/pdf"}),n=URL.createObjectURL(e);window.open(n)},t=>{Object(i.a)(t)})},this.getTenantDateFormats=()=>this.http.post("/api/invoice/v2/getTenantDateFormats",{}),this.syncInvoiceWithSharepoint=(t,e)=>this.http.post("/api/invoice/syncInvoiceWithSharepoint",{billingId:t,token:e})}static invoiceAuthGaurd(t,e,n,o){throw new Error("Method not implemented.")}viewInvoice(t){return this.http.post("api/invoice/viewInvoice",{billingId:t})}roleCheck(){return this.http.post("api/invoice/roleCheck",{})}getInvoiceListActivityList(t){return this.http.post("/api/invoice/getActivitiesInPopUp",{milestoneId:t})}updateInvoiceActivityList(t,e,n,o){return this.http.post("/api/invoice/updateActivities",{milestoneId:t,activities:e,oldPlannedOn:n,newPlannedOn:o})}getInvoicePdfConfig(t,e,n,o,i){return this.http.post("/api/invoice/getInvoicePdfConfig",{fromCompanyCode:t,itemId:e,projectId:n,customerId:o,legalEntityId:i})}getInvoiceTenantCheckDetail(t,e){return this.http.post("/api/invoice/getInvoiceTenantApplicationCheck",{tenantName:t,checkType:e})}getPaymentTerms(){return new Promise((t,e)=>{this.http.post("/api/master/getPaymentTerms",{}).subscribe(e=>t(e),e=>(console.log(e),t([])))})}invoiceUndoAccess(){return a.where(this.roleService.roles,{application_id:907,object_id:320}).length>0}getconsultantDetail(t){return this.http.post("/api/invoice/getFteDetails",{otherMilestoneDataToPdf:t})}getFteDetailForViewInvoice(t){return this.http.post("/api/invoice/getFteDetailForViewInvoice",{otherMilestoneDataToPdf:t})}getLegalEntityQRConfig(t){return this.http.post("/api/invoice/v2/getLegalEntityQRConfig",{legalEntityId:t})}getCurrencyDetails(){return new Promise((t,e)=>{this.http.post("/api/invoice/getCurrencyDetails",{}).subscribe(e=>(console.log(e),t(e.data)))})}invoiceAuthGaurd(t,e,n,o=null){return this.http.post("/api/invoice/checkRoleAccessForUser",{oid:t,aid:e,application_id:n,application_object:o})}}return t.\u0275fac=function(e){return new(e||t)(s["\u0275\u0275inject"](r.c),s["\u0275\u0275inject"](d.a))},t.\u0275prov=s["\u0275\u0275defineInjectable"]({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()},NKXf:function(t,e,n){"use strict";n.r(e),n.d(e,"AvailableFromAddressComponent",(function(){return p}));var o=n("2ChS"),i=n("ofXK"),a=n("bTqV"),s=(n("Qu3c"),n("NFeN")),r=n("fXoL"),d=n("A6Kz"),c=n("dNgK");function l(t,e){if(1&t){const t=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"div",5),r["\u0275\u0275elementStart"](1,"div",6),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](t);const n=e.$implicit;return r["\u0275\u0275nextContext"]().selectAddress(n)})),r["\u0275\u0275elementStart"](2,"div",7),r["\u0275\u0275elementStart"](3,"div",8),r["\u0275\u0275elementStart"](4,"div",9),r["\u0275\u0275text"](5," Legal Entity "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](6,"div",10),r["\u0275\u0275text"](7),r["\u0275\u0275elementEnd"](),r["\u0275\u0275element"](8,"div",11),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](9,"div",8),r["\u0275\u0275elementStart"](10,"div",9),r["\u0275\u0275text"](11," Address line 1 "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](12,"div",12),r["\u0275\u0275text"](13),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](14,"div",8),r["\u0275\u0275elementStart"](15,"div",9),r["\u0275\u0275text"](16," Address line 2 "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](17,"div",12),r["\u0275\u0275text"](18),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](19,"div",8),r["\u0275\u0275elementStart"](20,"div",9),r["\u0275\u0275text"](21," Address line 3 "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](22,"div",12),r["\u0275\u0275text"](23),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](24,"div",8),r["\u0275\u0275elementStart"](25,"div",9),r["\u0275\u0275text"](26," Address line 4 "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](27,"div",12),r["\u0275\u0275text"](28),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&t){const t=e.$implicit;r["\u0275\u0275advance"](7),r["\u0275\u0275textInterpolate1"](" ",t.legalEntityName," "),r["\u0275\u0275advance"](6),r["\u0275\u0275textInterpolate1"](" ",t.addressLine1," "),r["\u0275\u0275advance"](5),r["\u0275\u0275textInterpolate1"](" ",t.addressLine2," "),r["\u0275\u0275advance"](5),r["\u0275\u0275textInterpolate1"](" ",t.addressLine3," "),r["\u0275\u0275advance"](5),r["\u0275\u0275textInterpolate1"](" ",t.addressLine4," ")}}let p=(()=>{class t{constructor(t,e,n,o){this.data=t,this._bottomSheetRef=e,this._invoiceService=n,this._snackBar=o,this.syncAddress=()=>{this._invoiceService.getAvailableFromAddress(this.data.legalEntityId).subscribe(t=>{this.availableAddress=t,this._snackBar.open("Address synced successfully!","dismiss",{duration:1e3})},t=>{console.error(t)})},this.availableAddress=this.data.data}ngOnInit(){}selectAddress(t){this._bottomSheetRef.dismiss(t)}closeBottomSheet(){this._bottomSheetRef.dismiss()}}return t.\u0275fac=function(e){return new(e||t)(r["\u0275\u0275directiveInject"](o.a),r["\u0275\u0275directiveInject"](o.d),r["\u0275\u0275directiveInject"](d.a),r["\u0275\u0275directiveInject"](c.a))},t.\u0275cmp=r["\u0275\u0275defineComponent"]({type:t,selectors:[["app-available-from-address"]],decls:10,vars:1,consts:[[1,"row","pt-2","justify-content-end"],[1,"col-2"],["mat-icon-button","","color","primary",3,"click"],[1,"row","pt-2","account-address-style"],["class","col-4 pb-2",4,"ngFor","ngForOf"],[1,"col-4","pb-2"],[1,"card","address-card",2,"cursor","pointer",3,"click"],[1,"card-body","p-2"],[1,"row","pt-1"],[1,"col-5","address-title"],[1,"col-6","address-name","pl-0"],[1,"col-1"],[1,"col-7","addresses","pl-0"]],template:function(t,e){1&t&&(r["\u0275\u0275elementStart"](0,"div",0),r["\u0275\u0275elementStart"](1,"div",1),r["\u0275\u0275elementStart"](2,"button",2),r["\u0275\u0275listener"]("click",(function(){return e.syncAddress()})),r["\u0275\u0275elementStart"](3,"mat-icon"),r["\u0275\u0275text"](4,"sync"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](5,"button",2),r["\u0275\u0275listener"]("click",(function(){return e.closeBottomSheet()})),r["\u0275\u0275elementStart"](6,"mat-icon"),r["\u0275\u0275text"](7,"close"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](8,"div",3),r["\u0275\u0275template"](9,l,29,5,"div",4),r["\u0275\u0275elementEnd"]()),2&t&&(r["\u0275\u0275advance"](9),r["\u0275\u0275property"]("ngForOf",e.availableAddress))},directives:[a.a,s.a,i.NgForOf],styles:[".account-address-style[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#3c3c3c;font-weight:400;font-size:20px}.account-address-style[_ngcontent-%COMP%]   .drawer[_ngcontent-%COMP%]{width:100%}.account-address-style[_ngcontent-%COMP%]   .side-drawer[_ngcontent-%COMP%]{width:620px!important;background-image:url(address.589802d74b6c66ae85df.png);background-size:202px 182px;background-repeat:no-repeat;overflow-y:hidden!important;background-position:97% 81%}.account-address-style[_ngcontent-%COMP%]   .tileName[_ngcontent-%COMP%]{color:#9a9a9a;font-size:14px!important}.account-address-style[_ngcontent-%COMP%]   .address-card[_ngcontent-%COMP%]:hover   .more-button[_ngcontent-%COMP%]{visibility:visible}.account-address-style[_ngcontent-%COMP%]   .more-button[_ngcontent-%COMP%]{width:27px!important;height:27px!important;line-height:25px!important}.account-address-style[_ngcontent-%COMP%]   .smallCardIcon[_ngcontent-%COMP%]{font-size:18px!important;color:#797979!important}.account-address-style[_ngcontent-%COMP%]   .drop-btn[_ngcontent-%COMP%]{font-size:13px!important;height:38px!important;line-height:2!important;padding:0 13px!important}.account-address-style[_ngcontent-%COMP%]   .address-name[_ngcontent-%COMP%]{color:#cf0001;font-size:14px;font-weight:500}.account-address-style[_ngcontent-%COMP%]   .addresses[_ngcontent-%COMP%]{color:#1a1a1a;font-weight:400;font-size:14px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.account-address-style[_ngcontent-%COMP%]   .footer-text[_ngcontent-%COMP%]{color:#7b7b7a;font-weight:400;font-size:14px}.account-address-style[_ngcontent-%COMP%]   .footer-text[_ngcontent-%COMP%]   .mat-checkbox-label[_ngcontent-%COMP%]{padding-top:3px!important}.account-address-style[_ngcontent-%COMP%]   .address-title[_ngcontent-%COMP%]{color:#7b7b7a;font-size:14px}.account-address-style[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.account-address-style[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}.account-address-style[_ngcontent-%COMP%]   .create-account-field-inputsearch[_ngcontent-%COMP%]{font-size:13px!important}.account-address-style[_ngcontent-%COMP%]   .create-account-field-inputsearch[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]{width:70%!important}.account-address-style[_ngcontent-%COMP%]   .create-account-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%!important}.account-address-style[_ngcontent-%COMP%]   .iconbtn[_ngcontent-%COMP%]{background-color:#c92020;color:#fff;padding:0;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.account-address-style[_ngcontent-%COMP%]   .quotes[_ngcontent-%COMP%]{font-style:italic;font-weight:500;color:#4d4d4b;font-size:14px}.account-address-style[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}.account-address-style[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}"]}),t})()},gvgV:function(t,e,n){"use strict";n.r(e),n.d(e,"AvailableFromAddressComponent",(function(){return p}));var o=n("2ChS"),i=n("ofXK"),a=n("bTqV"),s=(n("Qu3c"),n("NFeN")),r=n("fXoL"),d=n("T4Li"),c=n("dNgK");function l(t,e){if(1&t){const t=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"div",5),r["\u0275\u0275elementStart"](1,"div",6),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](t);const n=e.$implicit;return r["\u0275\u0275nextContext"]().selectAddress(n)})),r["\u0275\u0275elementStart"](2,"div",7),r["\u0275\u0275elementStart"](3,"div",8),r["\u0275\u0275elementStart"](4,"div",9),r["\u0275\u0275text"](5," Legal Entity "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](6,"div",10),r["\u0275\u0275text"](7),r["\u0275\u0275elementEnd"](),r["\u0275\u0275element"](8,"div",1),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](9,"div",8),r["\u0275\u0275elementStart"](10,"div",9),r["\u0275\u0275text"](11," Address line 1 "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](12,"div",11),r["\u0275\u0275text"](13),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](14,"div",8),r["\u0275\u0275elementStart"](15,"div",9),r["\u0275\u0275text"](16," Address line 2 "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](17,"div",11),r["\u0275\u0275text"](18),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](19,"div",8),r["\u0275\u0275elementStart"](20,"div",9),r["\u0275\u0275text"](21," Address line 3 "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](22,"div",11),r["\u0275\u0275text"](23),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](24,"div",8),r["\u0275\u0275elementStart"](25,"div",9),r["\u0275\u0275text"](26," Address line 4 "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](27,"div",11),r["\u0275\u0275text"](28),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&t){const t=e.$implicit;r["\u0275\u0275advance"](7),r["\u0275\u0275textInterpolate1"](" ",t.legalEntityName," "),r["\u0275\u0275advance"](6),r["\u0275\u0275textInterpolate1"](" ",t.addressLine1," "),r["\u0275\u0275advance"](5),r["\u0275\u0275textInterpolate1"](" ",t.addressLine2," "),r["\u0275\u0275advance"](5),r["\u0275\u0275textInterpolate1"](" ",t.addressLine3," "),r["\u0275\u0275advance"](5),r["\u0275\u0275textInterpolate1"](" ",t.addressLine4," ")}}let p=(()=>{class t{constructor(t,e,n,o){this.data=t,this._bottomSheetRef=e,this._invoiceService=n,this._snackBar=o,this.syncAddress=()=>{this._invoiceService.getAvailableFromAddress(this.data.legalEntityId).subscribe(t=>{this.availableAddress=t,this._snackBar.open("Address synced successfully!","dismiss",{duration:1e3})},t=>{console.error(t)})},this.availableAddress=this.data.data}ngOnInit(){}selectAddress(t){this._bottomSheetRef.dismiss(t)}}return t.\u0275fac=function(e){return new(e||t)(r["\u0275\u0275directiveInject"](o.a),r["\u0275\u0275directiveInject"](o.d),r["\u0275\u0275directiveInject"](d.a),r["\u0275\u0275directiveInject"](c.a))},t.\u0275cmp=r["\u0275\u0275defineComponent"]({type:t,selectors:[["app-available-from-address"]],decls:7,vars:1,consts:[[1,"row","pt-2","justify-content-end"],[1,"col-1"],["mat-icon-button","","color","primary",3,"click"],[1,"row","pt-2","account-address-style"],["class","col-4 pb-2",4,"ngFor","ngForOf"],[1,"col-4","pb-2"],[1,"card","address-card",2,"cursor","pointer",3,"click"],[1,"card-body","p-2"],[1,"row","pt-1"],[1,"col-5","address-title"],[1,"col-6","address-name","pl-0"],[1,"col-7","addresses","pl-0"]],template:function(t,e){1&t&&(r["\u0275\u0275elementStart"](0,"div",0),r["\u0275\u0275elementStart"](1,"div",1),r["\u0275\u0275elementStart"](2,"button",2),r["\u0275\u0275listener"]("click",(function(){return e.syncAddress()})),r["\u0275\u0275elementStart"](3,"mat-icon"),r["\u0275\u0275text"](4,"sync"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](5,"div",3),r["\u0275\u0275template"](6,l,29,5,"div",4),r["\u0275\u0275elementEnd"]()),2&t&&(r["\u0275\u0275advance"](6),r["\u0275\u0275property"]("ngForOf",e.availableAddress))},directives:[a.a,s.a,i.NgForOf],styles:[".account-address-style[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#3c3c3c;font-weight:400;font-size:20px}.account-address-style[_ngcontent-%COMP%]   .drawer[_ngcontent-%COMP%]{width:100%}.account-address-style[_ngcontent-%COMP%]   .side-drawer[_ngcontent-%COMP%]{width:620px!important;background-image:url(address.589802d74b6c66ae85df.png);background-size:202px 182px;background-repeat:no-repeat;overflow-y:hidden!important;background-position:97% 81%}.account-address-style[_ngcontent-%COMP%]   .tileName[_ngcontent-%COMP%]{color:#9a9a9a;font-size:14px!important}.account-address-style[_ngcontent-%COMP%]   .address-card[_ngcontent-%COMP%]:hover   .more-button[_ngcontent-%COMP%]{visibility:visible}.account-address-style[_ngcontent-%COMP%]   .more-button[_ngcontent-%COMP%]{width:27px!important;height:27px!important;line-height:25px!important}.account-address-style[_ngcontent-%COMP%]   .smallCardIcon[_ngcontent-%COMP%]{font-size:18px!important;color:#797979!important}.account-address-style[_ngcontent-%COMP%]   .drop-btn[_ngcontent-%COMP%]{font-size:13px!important;height:38px!important;line-height:2!important;padding:0 13px!important}.account-address-style[_ngcontent-%COMP%]   .address-name[_ngcontent-%COMP%]{color:#cf0001;font-size:14px;font-weight:500}.account-address-style[_ngcontent-%COMP%]   .addresses[_ngcontent-%COMP%]{color:#1a1a1a;font-weight:400;font-size:14px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.account-address-style[_ngcontent-%COMP%]   .footer-text[_ngcontent-%COMP%]{color:#7b7b7a;font-weight:400;font-size:14px}.account-address-style[_ngcontent-%COMP%]   .footer-text[_ngcontent-%COMP%]   .mat-checkbox-label[_ngcontent-%COMP%]{padding-top:3px!important}.account-address-style[_ngcontent-%COMP%]   .address-title[_ngcontent-%COMP%]{color:#7b7b7a;font-size:14px}.account-address-style[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.account-address-style[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}.account-address-style[_ngcontent-%COMP%]   .create-account-field-inputsearch[_ngcontent-%COMP%]{font-size:13px!important}.account-address-style[_ngcontent-%COMP%]   .create-account-field-inputsearch[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]{width:70%!important}.account-address-style[_ngcontent-%COMP%]   .create-account-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%!important}.account-address-style[_ngcontent-%COMP%]   .iconbtn[_ngcontent-%COMP%]{background-color:#c92020;color:#fff;padding:0;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.account-address-style[_ngcontent-%COMP%]   .quotes[_ngcontent-%COMP%]{font-style:italic;font-weight:500;color:#4d4d4b;font-size:14px}.account-address-style[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}.account-address-style[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}"]}),t})()}}]);