(window.webpackJsonp=window.webpackJsonp||[]).push([[647],{v8fE:function(e,t,n){"use strict";n.r(t),n.d(t,"SalesReportModule",(function(){return U}));var i=n("ofXK"),o=n("ZzPI"),a=n("ClZT"),r=n("WYlB"),l=n("XPKZ"),d=n("tyNb"),s=n("3Pt+"),c=n("wd/R"),m=n("1yaQ"),p=n("FKr1"),u=n("fXoL"),h=n("JIr8"),w=n("EY2u"),v=n("tk/3"),g=n("BVzC");let b=(()=>{class e{constructor(e,t){this.http=e,this.errorService=t}getSalesReport(e,t){return this.http.post("api/invoice/salesReportApi",{start_date:e,end_date:t})}getAgingInvoices(){return this.http.post("api/invoice/getAgeingPayments",{})}getSalesPivotReport(e,t){return new Promise((n,i)=>{this.http.post("/api/invoice/salesReportApi",{start_date:e,end_date:t}).subscribe(e=>n(e),e=>i(e))})}saveReportState(e,t,n,i){return this.http.post("api/userExperience/saveReportState",{state:e,name:t,field_conf:n,application_id:i}).pipe(Object(h.a)(e=>(console.log(e),this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while retrieving Column info list",e&&e.params?e.params:e&&e.error?e.error.params:{}),w.a)))}updateReportState(e,t,n,i){return this.http.post("api/userExperience/updateReportState",{application_id:i,state:e,customization_id:t,field_conf:n}).pipe(Object(h.a)(e=>(console.log(e),this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while retrieving Column info list",e&&e.params?e.params:e&&e.error?e.error.params:{}),w.a)))}getReportUserViews(e){return this.http.post("api/userExperience/getReportUserViews",{application_id:e}).pipe(Object(h.a)(e=>(console.log(e),this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while retrieving Column info list",e&&e.params?e.params:e&&e.error?e.error.params:{}),w.a)))}deleteVariant(e,t){return this.http.post("api/userExperience/deleteVariant",{application_id:e,customization_id:t}).pipe(Object(h.a)(e=>(console.log(e),this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while retrieving Column info list",e&&e.params?e.params:e&&e.error?e.error.params:{}),w.a)))}}return e.\u0275fac=function(t){return new(t||e)(u["\u0275\u0275inject"](v.c),u["\u0275\u0275inject"](g.a))},e.\u0275prov=u["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var x=n("dNgK"),f=n("kmnG"),y=n("qFsG"),S=n("iadO"),F=n("bTqV"),R=n("Qu3c"),D=n("NFeN"),_=n("6t9p");let C=(()=>{class e{constructor(){}ngOnInit(){this.currentDate=c().format("DD-MM-YY")}ngOnChanges(e){console.log(this.data)}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=u["\u0275\u0275defineComponent"]({type:e,selectors:[["app-devxtreme"]],viewQuery:function(e,t){if(1&e&&u["\u0275\u0275viewQuery"](o.a,!0),2&e){let e;u["\u0275\u0275queryRefresh"](e=u["\u0275\u0275loadQuery"]())&&(t.dataGrid=e.first)}},inputs:{data:"data"},features:[u["\u0275\u0275NgOnChangesFeature"]],decls:99,vars:71,consts:[["id","gridContainer",1,"dev-style",3,"height","allowColumnResizing","dataSource","showBorders","hoverStateEnabled"],[3,"enabled","fileName","allowExportSelectedData"],["mode","select",3,"enabled"],[3,"enabled"],["placeholder","Search...",3,"visible","width"],["mode","single"],[3,"visible"],["dataField","billing_id","caption","Billing Id",3,"allowReordering","width"],["dataField","invoice_raised_month","caption","Invoice raised month",3,"allowReordering","width"],["dataField","service_type_name","caption","Project Type",3,"width","allowReordering"],["dataField","item_name","caption","Project Name",3,"width","allowReordering"],["dataField","pl_name","caption","P & L",3,"allowReordering","width"],["dataField","profit_center","caption","Profit Center",3,"allowReordering","width","visible"],["dataField","industry","caption","Industry",3,"allowReordering","width"],["dataField","project_customer","caption","Project Customer",3,"allowReordering","width"],["dataField","KT_to_sub_Invoice_No","caption","Inter. Comp. Invoice No",3,"allowReordering","width"],["dataField","sub_to_customer_Invoice_no","caption","Sub to Customer Invoice No",3,"allowReordering","width"],["dataField","KT_to_sub_invoice_value","caption","Inter. Comp. Invoice Value",3,"allowReordering","width"],["dataField","sub_to_customer_invoice_value","caption","Sub to Customer Invoice Value",3,"allowReordering","width"],["dataField","sub_to_customer_currency","caption","Sub to Customer Invoice Currency",2,"background-color","#cf0001 !important",3,"allowReordering","width"],["dataField","KT_to_sub_currency","caption","Inter. Comp. Invoice Currency",3,"allowReordering","width"],["dataField","KT_to_sub_description","caption","Inter. Comp. Invoice Description",3,"allowReordering","width"],["dataField","Sub_to_customer_description","caption","Sub to Customer Invoice Description",3,"allowReordering","width"],["dataField","company_code","caption","Project Region",3,"allowReordering","width","visible"],["dataField","invoice_raised_on","caption","Invoice raised on",3,"allowReordering","width"],["dataField","rbi_rate","caption","RBI rate",3,"allowReordering","width"],["dataField","global_value","caption","Global value",3,"allowReordering","width"],["dataField","inCrs","caption","Global value (Crs)",3,"allowReordering","width","visible"],["dataField","KT_value","caption","Inter. Comp. value",3,"allowReordering","width"],["dataField","KT_to_sub_poRef","caption","Inter. Comp. PoRef",3,"allowReordering","width"],["dataField","KT_to_sub_poDate","caption","Inter. Comp. PoDate",3,"allowReordering","width"],["dataField","Sub_to_customer_poRef","caption","Sub to Customer PoRef",3,"allowReordering","width"],["dataField","Sub_to_customer_poDate","caption","Sub to Customer PoDate",3,"allowReordering","width"],["dataField","tally_update_counter","caption","Tally update counter",3,"allowReordering","width"]],template:function(e,t){1&e&&(u["\u0275\u0275elementStart"](0,"dx-data-grid",0),u["\u0275\u0275text"](1,"\n  "),u["\u0275\u0275element"](2,"dxo-export",1),u["\u0275\u0275text"](3,"\n  "),u["\u0275\u0275elementStart"](4,"dxo-column-chooser",2),u["\u0275\u0275text"](5,"\n    "),u["\u0275\u0275text"](6,"\n  "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275text"](7,"\n  "),u["\u0275\u0275element"](8,"dxo-column-fixing",3),u["\u0275\u0275text"](9,"\n  "),u["\u0275\u0275element"](10,"dxo-search-panel",4),u["\u0275\u0275text"](11,"\n  "),u["\u0275\u0275element"](12,"dxo-selection",5),u["\u0275\u0275text"](13,"\n  "),u["\u0275\u0275element"](14,"dxo-header-filter",6),u["\u0275\u0275text"](15,"\n  "),u["\u0275\u0275element"](16,"dxo-filter-row",6),u["\u0275\u0275text"](17,"\n\n  "),u["\u0275\u0275elementStart"](18,"dxi-column",7),u["\u0275\u0275text"](19,"\n  "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275text"](20,"\n\n  "),u["\u0275\u0275elementStart"](21,"dxi-column",8),u["\u0275\u0275text"](22,"\n  "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275text"](23,"\n\n  "),u["\u0275\u0275elementStart"](24,"dxi-column",9),u["\u0275\u0275text"](25,"\n  "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275text"](26,"\n\n  "),u["\u0275\u0275elementStart"](27,"dxi-column",10),u["\u0275\u0275text"](28,"\n  "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275text"](29,"\n\n  "),u["\u0275\u0275elementStart"](30,"dxi-column",11),u["\u0275\u0275text"](31,"\n  "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275text"](32,"\n\n  "),u["\u0275\u0275elementStart"](33,"dxi-column",12),u["\u0275\u0275text"](34,"\n  "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275text"](35,"\n\n  "),u["\u0275\u0275elementStart"](36,"dxi-column",13),u["\u0275\u0275text"](37,"\n  "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275text"](38,"\n\n  "),u["\u0275\u0275elementStart"](39,"dxi-column",14),u["\u0275\u0275text"](40,"\n  "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275text"](41,"\n\n  "),u["\u0275\u0275elementStart"](42,"dxi-column",15),u["\u0275\u0275text"](43,"\n  "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275text"](44,"\n\n  "),u["\u0275\u0275elementStart"](45,"dxi-column",16),u["\u0275\u0275text"](46,"\n  "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275text"](47,"\n\n\n  "),u["\u0275\u0275elementStart"](48,"dxi-column",17),u["\u0275\u0275text"](49,"\n  "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275text"](50,"\n\n  "),u["\u0275\u0275elementStart"](51,"dxi-column",18),u["\u0275\u0275text"](52,"\n  "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275text"](53,"\n\n  "),u["\u0275\u0275elementStart"](54,"dxi-column",19),u["\u0275\u0275text"](55,"\n  "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275text"](56,"\n\n  "),u["\u0275\u0275elementStart"](57,"dxi-column",20),u["\u0275\u0275text"](58,"\n  "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275text"](59,"\n\n  "),u["\u0275\u0275elementStart"](60,"dxi-column",21),u["\u0275\u0275text"](61,"\n  "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275text"](62,"\n\n  "),u["\u0275\u0275elementStart"](63,"dxi-column",22),u["\u0275\u0275text"](64,"\n  "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275text"](65,"\n\n  "),u["\u0275\u0275elementStart"](66,"dxi-column",23),u["\u0275\u0275text"](67,"\n  "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275text"](68,"\n\n  "),u["\u0275\u0275elementStart"](69,"dxi-column",24),u["\u0275\u0275text"](70,"\n  "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275text"](71,"\n\n\n  "),u["\u0275\u0275elementStart"](72,"dxi-column",25),u["\u0275\u0275text"](73,"\n  "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275text"](74,"\n\n  "),u["\u0275\u0275elementStart"](75,"dxi-column",26),u["\u0275\u0275text"](76,"\n  "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275text"](77,"\n\n  "),u["\u0275\u0275elementStart"](78,"dxi-column",27),u["\u0275\u0275text"](79,"\n  "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275text"](80,"\n\n  "),u["\u0275\u0275elementStart"](81,"dxi-column",28),u["\u0275\u0275text"](82,"\n  "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275text"](83,"\n\n  "),u["\u0275\u0275elementStart"](84,"dxi-column",29),u["\u0275\u0275text"](85,"\n  "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275text"](86,"\n\n  "),u["\u0275\u0275elementStart"](87,"dxi-column",30),u["\u0275\u0275text"](88,"\n  "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275text"](89,"\n\n  "),u["\u0275\u0275elementStart"](90,"dxi-column",31),u["\u0275\u0275text"](91,"\n  "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275text"](92,"\n\n  "),u["\u0275\u0275elementStart"](93,"dxi-column",32),u["\u0275\u0275text"](94,"\n  "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275text"](95,"\n\n  "),u["\u0275\u0275elementStart"](96,"dxi-column",33),u["\u0275\u0275text"](97,"\n"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275text"](98,"\n\n"),u["\u0275\u0275elementEnd"]()),2&e&&(u["\u0275\u0275property"]("height",840)("allowColumnResizing",!0)("dataSource",t.data)("showBorders",!0)("hoverStateEnabled",!0),u["\u0275\u0275advance"](2),u["\u0275\u0275propertyInterpolate1"]("fileName","sales report - ",t.currentDate,""),u["\u0275\u0275property"]("enabled",!0)("allowExportSelectedData",!0),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("enabled",!0),u["\u0275\u0275advance"](4),u["\u0275\u0275property"]("enabled",!0),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("visible",!0)("width",240),u["\u0275\u0275advance"](4),u["\u0275\u0275property"]("visible",!0),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("visible",!0),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("allowReordering",!0)("width",135),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("allowReordering",!0)("width",135),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("width",135)("allowReordering",!0),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("width",135)("allowReordering",!0),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("allowReordering",!0)("width",135),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("allowReordering",!0)("width",135)("visible",!0),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("allowReordering",!0)("width",135),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("allowReordering",!0)("width",135),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("allowReordering",!0)("width",135),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("allowReordering",!0)("width",150),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("allowReordering",!0)("width",135),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("allowReordering",!0)("width",135),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("allowReordering",!0)("width",135),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("allowReordering",!0)("width",135),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("allowReordering",!0)("width",135),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("allowReordering",!0)("width",135),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("allowReordering",!0)("width",135)("visible",!0),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("allowReordering",!0)("width",135),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("allowReordering",!0)("width",135),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("allowReordering",!0)("width",135),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("allowReordering",!0)("width",135)("visible",!0),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("allowReordering",!0)("width",135),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("allowReordering",!0)("width",150),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("allowReordering",!0)("width",135),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("allowReordering",!0)("width",135),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("allowReordering",!0)("width",135),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("allowReordering",!0)("width",135))},directives:[o.a,_.Sb,_.tb,_.vb,_.Md,_.Od,_.Cc,_.dc,_.g],styles:[".dev-style[_ngcontent-%COMP%]     .dx-datagrid-action{background-color:#f1f1f1!important;font-weight:500!important;color:#000!important}.dev-style[_ngcontent-%COMP%]     td{background-color:#fff!important}.dev-style[_ngcontent-%COMP%]     .dx-popup-content{font-size:14px!important}.dev-style[_ngcontent-%COMP%]     .dx-popup-content   .dx-scrollable-container   .dx-list-select-all{padding:10px 0!important;font-size:14px!important}.dev-style[_ngcontent-%COMP%]     .dx-popup-content   .dx-list-item-content{padding:12px 14px 13px!important;font-size:14px!important}"]}),e})();const E=function(){return{type:"fixedPoint",precision:2}};let I=(()=>{class e{constructor(e,t,n){this._salesReportService=e,this.snackBar=t,this.fb=n,this.InvoiceList=[],this.salesReport=[],this.bucketInfo=[],this.startDate=c().format("YYYY-MM-01"),this.endDate=c(this.startDate).endOf("month").format("YYYY-MM-01"),this.minStartDate=c("2021-04-01"),this.minEndDate=c(this.minStartDate).endOf("month"),this.dateForm=this.fb.group({startDate:[this.startDate,s.H.required],endDate:[this.endDate,s.H.required]}),this.setCurrentFyData()}ngOnInit(){this.subscribeOnValueChanges(),this.getSalesChartData(),this.getAgingInvoices()}subscribeOnValueChanges(){this.dateForm.get("startDate").valueChanges.subscribe(e=>{e&&(this.startDate=c(e).format("YYYY-MM-01"),this.minEndDate=c(this.startDate).endOf("month"))}),this.dateForm.get("endDate").valueChanges.subscribe(e=>{e&&(this.endDate=e,this.endDate=c(this.endDate).endOf("month").format("YYYY-MM-DD"))})}salesReportDateUpdate(){localStorage.setItem("salesReportStartDate",this.dateForm.controls.startDate.value),localStorage.setItem("salesReportEndDate",this.dateForm.controls.endDate.value),this.getSalesChartData()}setCurrentFyData(){let e=localStorage.getItem("salesReportStartDate"),t=localStorage.getItem("salesReportEndDate");null!=e&&null!=e&&null!=t&&null!=t?(this.startDate=c(e).format("YYYY-MM-DD"),this.endDate=c(t).format("YYYY-MM-DD")):(this.startDate=c(c().subtract(2,"month")).startOf("month").format("YYYY-MM-DD"),this.endDate=c().endOf("month").format("YYYY-MM-DD")),this.minEndDate=c(this.startDate).endOf("month").format("YYYY-MM-DD"),this.dateForm.patchValue({startDate:this.startDate,endDate:this.endDate})}changeDate(e){return c(e).format("DD-MM-YY")}getColor(e){return 0==e?"#e2e2e2":1==e?"#009432":void 0}getSalesChartData(){this._salesReportService.getSalesReport(this.startDate,this.endDate).subscribe(e=>{console.log(e),this.salesChartData=e},e=>{console.error(e)})}getAgingInvoices(){this._salesReportService.getAgingInvoices().subscribe(e=>{this.ageingInvoice=e},e=>{console.error(e)})}ngOnDestroy(){}}return e.\u0275fac=function(t){return new(t||e)(u["\u0275\u0275directiveInject"](b),u["\u0275\u0275directiveInject"](x.a),u["\u0275\u0275directiveInject"](s.i))},e.\u0275cmp=u["\u0275\u0275defineComponent"]({type:e,selectors:[["app-sales-report-landing-page"]],features:[u["\u0275\u0275ProvidersFeature"]([{provide:p.c,useClass:m.c,deps:[p.f,m.a]},{provide:p.e,useValue:{parse:{dateInput:"DD-MM-YYYY"},display:{dateInput:"DD-MM-YYYY",monthYearLabel:"MMM YYYY"},useUtc:!0}}])],decls:108,vars:156,consts:[[3,"formGroup"],[1,"container-fluid","pl-0","pr-0"],[1,"row"],[1,"col-12","order-lg-1","order-md-2","col-lg-12","mainSection","pl-1","pr-2"],[1,"row","mt-3"],[1,"row","w-100","pt-2","pb-2"],[1,"col-2"],["appearance","outline",2,"width","80%"],["matInput","","formControlName","startDate",3,"matDatepicker","min"],["matSuffix","",3,"for"],["sd",""],["matInput","","formControlName","endDate","placeholder","MMMM-YYYY",3,"matDatepicker","min"],["ed",""],[1,"col-1"],["mat-icon-button","","matTooltip","Submit",1,"iconbtn","mr-2",2,"font-weight","normal","margin-left","1%","margin-bottom","1%",3,"click"],[1,"col"],["mat-icon-button","","routerLink","/main/reports/sales/pivot"],["matTooltip","Sales pivot"],[1,"row","pt-2"],[1,"col-12","h-100","pl-0","pr-0"],[1,"col-12","pl-0","pr-0"],[3,"data"],[1,"row","tileName","mt-3","pl-2","pb-1"],[1,"col-12","pl-0"],["id","gridContainer",3,"dataSource","showBorders","height","allowColumnResizing","hoverStateEnabled"],["fileName","Aging_Invoices",3,"enabled","allowExportSelectedData"],["mode","select",3,"enabled"],["mode","single"],[3,"visible"],[3,"enabled"],["dataField","item_name","caption","Item Name",3,"allowReordering","width"],["dataField","milestone_name","caption","Milestone Name",3,"allowReordering","width"],["dataField","Milestone AR","caption","Milestone AR (INR)",3,"allowReordering","width"],["type","fixedPoint",3,"precision"],["dataField","pl_name","caption","P & L",3,"allowReordering","groupIndex"],["dataField","days","caption","Age",3,"allowReordering","width"],["dataField","_0_30_days","caption","0-30 Days",3,"allowReordering","width"],["dataField","_31_60_days","caption","31-60 Days",3,"allowReordering","width"],["dataField","_61_90_days","caption","61-90 Days",3,"allowReordering","width"],["dataField","_91_180_days","caption","91-180 Days",3,"allowReordering","width"],["dataField","_180_above","caption"," 180 above",3,"allowReordering","width"],["dataField","Vertical","caption","Vertical",3,"allowReordering","width","visible"],["dataField","Project Region","caption","Project Region",3,"allowReordering","width","visible"],["dataField","Project Name","caption","Project Name",3,"allowReordering","width","visible"],["dataField","Customer Name","caption","Customer Name",3,"allowReordering","width","visible"],["dataField","Cost Center","caption","Cost Center",3,"allowReordering","width","visible"],["dataField","Invoice Raised Date","caption","Invoice Raised Date",3,"allowReordering","width","visible"],["dataField","Credit Period","caption","Credit Period",3,"allowReordering","width","visible"],["dataField","Actual End Date","caption","Actual End Date",3,"allowReordering","width","visible"],["dataField","Planned Collection Date","caption","Planned Collection Date",3,"allowReordering","width","visible"],["dataField","Planned Collection Month Year","caption","Planned Collection Month Year",3,"allowReordering","width","visible"],["dataField","product_category_name","caption","Product Category Name",3,"allowReordering","width","visible"],["dataField","Service Type","caption","Service Type",3,"allowReordering","width","visible"],["dataField","Rbi rate","caption","Rbi rate",3,"allowReordering","width","visible"],["dataField","Intercompany Invoice Description","caption","Intercompany Invoice Description",3,"allowReordering","width","visible"],["dataField","Intercompany Invoice No","caption","Intercompany Invoice No",3,"allowReordering","width","visible"],["dataField","Intercompany Currency","caption","Intercompany Currency",3,"allowReordering","width","visible"],["dataField","Intercompany Invoice Amount","caption","Intercompany Invoice Amount",3,"allowReordering","width","visible"],["dataField","KT Value","caption","KT Value",3,"allowReordering","width","visible"],["dataField","End Customer Invoice Description","caption","End Customer Invoice Description",3,"allowReordering","width","visible"],["dataField","End Customer Invoice No","caption","End Customer Invoice No",3,"allowReordering","width","visible"],["dataField","End Customer Currency","caption","End Customer Currency",3,"allowReordering","width","visible"],["dataField","End Customer Invoice Amount","caption","End Customer Invoice Amount",3,"allowReordering","width","visible"],["dataField","Global Value","caption","Global Value",3,"allowReordering","width","visible"],["dataField","project_id","caption","Project Id",3,"allowReordering","width","visible"],["dataField","milestone_id","caption","Milestone Id",3,"allowReordering","width","visible"],["dataField","milestone_status","caption","Milestone Status",3,"allowReordering","width","visible"],["dataField","tds_percent","caption","Tds Percent",3,"allowReordering","width","visible"],["dataField","tds_value","caption","Tds Value",3,"allowReordering","width","visible"],["column","_0_30_days","summaryType","sum","displayFormat","Total: {0} INR",3,"alignByColumn","showInGroupFooter","valueFormat"],["column","_31_60_days","summaryType","sum","displayFormat","Total: {0} INR",3,"alignByColumn","showInGroupFooter","valueFormat"],["column","_61_90_days","summaryType","sum","displayFormat","Total: {0} INR",3,"alignByColumn","showInGroupFooter","valueFormat"],["column","_91_180_days","summaryType","sum","displayFormat","Total: {0} INR",3,"alignByColumn","showInGroupFooter","valueFormat"],["column","_180_above","summaryType","sum","displayFormat","Total: {0} INR",3,"alignByColumn","showInGroupFooter","valueFormat"],["summaryItem","count"]],template:function(e,t){if(1&e&&(u["\u0275\u0275elementStart"](0,"form",0),u["\u0275\u0275elementStart"](1,"div",1),u["\u0275\u0275elementStart"](2,"div",2),u["\u0275\u0275elementStart"](3,"div",3),u["\u0275\u0275element"](4,"div",4),u["\u0275\u0275elementStart"](5,"div",5),u["\u0275\u0275elementStart"](6,"div",6),u["\u0275\u0275elementStart"](7,"mat-form-field",7),u["\u0275\u0275elementStart"](8,"mat-label"),u["\u0275\u0275text"](9,"Start Period"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275element"](10,"input",8),u["\u0275\u0275element"](11,"mat-datepicker-toggle",9),u["\u0275\u0275element"](12,"mat-datepicker",null,10),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](14,"div",6),u["\u0275\u0275elementStart"](15,"mat-form-field",7),u["\u0275\u0275elementStart"](16,"mat-label"),u["\u0275\u0275text"](17,"End Period"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275element"](18,"input",11),u["\u0275\u0275element"](19,"mat-datepicker-toggle",9),u["\u0275\u0275element"](20,"mat-datepicker",null,12),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](22,"div",13),u["\u0275\u0275elementStart"](23,"button",14),u["\u0275\u0275listener"]("click",(function(){return t.salesReportDateUpdate()})),u["\u0275\u0275elementStart"](24,"mat-icon"),u["\u0275\u0275text"](25,"done_all"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275element"](26,"div",15),u["\u0275\u0275elementStart"](27,"div",13),u["\u0275\u0275elementStart"](28,"button",16),u["\u0275\u0275elementStart"](29,"mat-icon",17),u["\u0275\u0275text"](30,"pivot_table_chart"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](31,"div",18),u["\u0275\u0275elementStart"](32,"div",19),u["\u0275\u0275elementStart"](33,"div",2),u["\u0275\u0275elementStart"](34,"div",20),u["\u0275\u0275element"](35,"app-devxtreme",21),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](36,"div",22),u["\u0275\u0275elementStart"](37,"div",23),u["\u0275\u0275text"](38," Aging Invoices "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](39,"div",2),u["\u0275\u0275elementStart"](40,"div",19),u["\u0275\u0275elementStart"](41,"div",2),u["\u0275\u0275elementStart"](42,"div",20),u["\u0275\u0275elementStart"](43,"dx-data-grid",24),u["\u0275\u0275element"](44,"dxo-export",25),u["\u0275\u0275element"](45,"dxo-column-chooser",26),u["\u0275\u0275element"](46,"dxo-selection",27),u["\u0275\u0275element"](47,"dxo-filter-row",28),u["\u0275\u0275element"](48,"dxo-column-fixing",29),u["\u0275\u0275element"](49,"dxo-header-filter",28),u["\u0275\u0275element"](50,"dxi-column",30),u["\u0275\u0275element"](51,"dxi-column",31),u["\u0275\u0275elementStart"](52,"dxi-column",32),u["\u0275\u0275element"](53,"dxo-format",33),u["\u0275\u0275elementEnd"](),u["\u0275\u0275element"](54,"dxi-column",34),u["\u0275\u0275element"](55,"dxi-column",35),u["\u0275\u0275elementStart"](56,"dxi-column",36),u["\u0275\u0275element"](57,"dxo-format",33),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](58,"dxi-column",37),u["\u0275\u0275element"](59,"dxo-format",33),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](60,"dxi-column",38),u["\u0275\u0275element"](61,"dxo-format",33),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](62,"dxi-column",39),u["\u0275\u0275element"](63,"dxo-format",33),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](64,"dxi-column",40),u["\u0275\u0275element"](65,"dxo-format",33),u["\u0275\u0275elementEnd"](),u["\u0275\u0275element"](66,"dxi-column",41),u["\u0275\u0275element"](67,"dxi-column",42),u["\u0275\u0275element"](68,"dxi-column",43),u["\u0275\u0275element"](69,"dxi-column",44),u["\u0275\u0275element"](70,"dxi-column",45),u["\u0275\u0275element"](71,"dxi-column",46),u["\u0275\u0275element"](72,"dxi-column",47),u["\u0275\u0275element"](73,"dxi-column",48),u["\u0275\u0275element"](74,"dxi-column",49),u["\u0275\u0275element"](75,"dxi-column",50),u["\u0275\u0275element"](76,"dxi-column",51),u["\u0275\u0275element"](77,"dxi-column",52),u["\u0275\u0275elementStart"](78,"dxi-column",53),u["\u0275\u0275element"](79,"dxo-format",33),u["\u0275\u0275elementEnd"](),u["\u0275\u0275element"](80,"dxi-column",54),u["\u0275\u0275element"](81,"dxi-column",55),u["\u0275\u0275element"](82,"dxi-column",56),u["\u0275\u0275elementStart"](83,"dxi-column",57),u["\u0275\u0275element"](84,"dxo-format",33),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](85,"dxi-column",58),u["\u0275\u0275element"](86,"dxo-format",33),u["\u0275\u0275elementEnd"](),u["\u0275\u0275element"](87,"dxi-column",59),u["\u0275\u0275element"](88,"dxi-column",60),u["\u0275\u0275element"](89,"dxi-column",61),u["\u0275\u0275elementStart"](90,"dxi-column",62),u["\u0275\u0275element"](91,"dxo-format",33),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](92,"dxi-column",63),u["\u0275\u0275element"](93,"dxo-format",33),u["\u0275\u0275elementEnd"](),u["\u0275\u0275element"](94,"dxi-column",64),u["\u0275\u0275element"](95,"dxi-column",65),u["\u0275\u0275element"](96,"dxi-column",66),u["\u0275\u0275elementStart"](97,"dxi-column",67),u["\u0275\u0275element"](98,"dxo-format",33),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](99,"dxi-column",68),u["\u0275\u0275element"](100,"dxo-format",33),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](101,"dxo-summary"),u["\u0275\u0275element"](102,"dxi-group-item",69),u["\u0275\u0275element"](103,"dxi-group-item",70),u["\u0275\u0275element"](104,"dxi-group-item",71),u["\u0275\u0275element"](105,"dxi-group-item",72),u["\u0275\u0275element"](106,"dxi-group-item",73),u["\u0275\u0275elementEnd"](),u["\u0275\u0275element"](107,"dxi-sort-by-group-summary-info",74),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()),2&e){const e=u["\u0275\u0275reference"](13),n=u["\u0275\u0275reference"](21);u["\u0275\u0275property"]("formGroup",t.dateForm),u["\u0275\u0275advance"](10),u["\u0275\u0275property"]("matDatepicker",e)("min",t.minStartDate),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("for",e),u["\u0275\u0275advance"](7),u["\u0275\u0275property"]("matDatepicker",n)("min",t.minEndDate),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("for",n),u["\u0275\u0275advance"](16),u["\u0275\u0275property"]("data",t.salesChartData),u["\u0275\u0275advance"](8),u["\u0275\u0275property"]("dataSource",t.ageingInvoice)("showBorders",!0)("height",640)("allowColumnResizing",!0)("hoverStateEnabled",!0),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("enabled",!0)("allowExportSelectedData",!0),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("enabled",!0),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("visible",!0),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("enabled",!0),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("visible",!0),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("allowReordering",!0)("width",220),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("allowReordering",!0)("width",220),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("allowReordering",!0)("width",150),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("precision",2),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("allowReordering",!0)("groupIndex",0),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("allowReordering",!0)("width",160),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("allowReordering",!0)("width",120),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("precision",2),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("allowReordering",!0)("width",120),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("precision",2),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("allowReordering",!0)("width",120),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("precision",2),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("allowReordering",!0)("width",120),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("precision",2),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("allowReordering",!0)("width",120),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("precision",2),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("allowReordering",!0)("width",135)("visible",!0),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("allowReordering",!0)("width",135)("visible",!0),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("allowReordering",!0)("width",135)("visible",!0),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("allowReordering",!0)("width",135)("visible",!0),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("allowReordering",!0)("width",135)("visible",!0),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("allowReordering",!0)("width",135)("visible",!0),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("allowReordering",!0)("width",135)("visible",!0),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("allowReordering",!0)("width",135)("visible",!0),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("allowReordering",!0)("width",135)("visible",!0),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("allowReordering",!0)("width",135)("visible",!0),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("allowReordering",!0)("width",135)("visible",!0),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("allowReordering",!0)("width",135)("visible",!0),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("allowReordering",!0)("width",135)("visible",!0),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("precision",2),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("allowReordering",!0)("width",135)("visible",!0),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("allowReordering",!0)("width",135)("visible",!0),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("allowReordering",!0)("width",135)("visible",!0),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("allowReordering",!0)("width",135)("visible",!0),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("precision",2),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("allowReordering",!0)("width",135)("visible",!0),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("precision",2),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("allowReordering",!0)("width",135)("visible",!0),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("allowReordering",!0)("width",135)("visible",!0),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("allowReordering",!0)("width",135)("visible",!0),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("allowReordering",!0)("width",135)("visible",!0),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("precision",2),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("allowReordering",!0)("width",135)("visible",!0),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("precision",2),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("allowReordering",!0)("width",135)("visible",!1),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("allowReordering",!0)("width",135)("visible",!1),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("allowReordering",!0)("width",135)("visible",!0),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("allowReordering",!0)("width",135)("visible",!0),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("precision",2),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("allowReordering",!0)("width",135)("visible",!0),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("precision",2),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("alignByColumn",!0)("showInGroupFooter",!1)("valueFormat",u["\u0275\u0275pureFunction0"](151,E)),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("alignByColumn",!0)("showInGroupFooter",!1)("valueFormat",u["\u0275\u0275pureFunction0"](152,E)),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("alignByColumn",!0)("showInGroupFooter",!1)("valueFormat",u["\u0275\u0275pureFunction0"](153,E)),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("alignByColumn",!0)("showInGroupFooter",!1)("valueFormat",u["\u0275\u0275pureFunction0"](154,E)),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("alignByColumn",!0)("showInGroupFooter",!1)("valueFormat",u["\u0275\u0275pureFunction0"](155,E))}},directives:[s.J,s.w,s.n,f.c,f.g,y.b,s.e,S.g,s.v,s.l,S.i,f.i,S.f,F.a,R.a,D.a,d.h,C,o.a,_.Sb,_.tb,_.Od,_.dc,_.vb,_.Cc,_.g,_.lc,_.ve,_.m,_.G],styles:[".iconbtn[_ngcontent-%COMP%]{background-color:#c92020;color:#fff;padding:0;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.trend-button-inactive[_ngcontent-%COMP%]{margin-top:3px!important;line-height:8px;width:26px;height:26px;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.trend-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}"]}),e})();var Y=n("mrSG"),M=n("ySCK"),V=n("qD4H"),P=n("PSD3"),k=n.n(P),O=n("8hBH");const T=["cardScroll"];function N(e,t){if(1&e){const e=u["\u0275\u0275getCurrentView"]();u["\u0275\u0275elementStart"](0,"button",42),u["\u0275\u0275listener"]("click",(function(){return u["\u0275\u0275restoreView"](e),u["\u0275\u0275nextContext"]().scrollLeft()})),u["\u0275\u0275elementStart"](1,"mat-icon",43),u["\u0275\u0275text"](2,"chevron_left"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()}}function z(e,t){if(1&e){const e=u["\u0275\u0275getCurrentView"]();u["\u0275\u0275elementStart"](0,"mat-icon",46),u["\u0275\u0275listener"]("click",(function(){u["\u0275\u0275restoreView"](e);const t=u["\u0275\u0275nextContext"]().index;return u["\u0275\u0275nextContext"]().deleteVariant(t)})),u["\u0275\u0275text"](1," delete"),u["\u0275\u0275elementEnd"]()}}function B(e,t){if(1&e){const e=u["\u0275\u0275getCurrentView"]();u["\u0275\u0275elementStart"](0,"button",44),u["\u0275\u0275listener"]("click",(function(){u["\u0275\u0275restoreView"](e);const n=t.index;return u["\u0275\u0275nextContext"]().changeView(n)}))("mouseenter",(function(){return t.$implicit.visibleDeleteView=!0}))("mouseleave",(function(){return t.$implicit.visibleDeleteView=!1})),u["\u0275\u0275text"](1),u["\u0275\u0275template"](2,z,2,0,"mat-icon",45),u["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=t.index,i=u["\u0275\u0275nextContext"]();u["\u0275\u0275property"]("ngClass",n==i.currentView?"btn-active my-2 version-button":"btn-not-active my-2  version-button"),u["\u0275\u0275advance"](1),u["\u0275\u0275textInterpolate1"](" ",null==e?null:e.config_name," "),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf",e.visibleDeleteView)}}function j(e,t){if(1&e){const e=u["\u0275\u0275getCurrentView"]();u["\u0275\u0275elementStart"](0,"button",47),u["\u0275\u0275listener"]("click",(function(){return u["\u0275\u0275restoreView"](e),u["\u0275\u0275nextContext"]().scrollRight()})),u["\u0275\u0275elementStart"](1,"mat-icon",43),u["\u0275\u0275text"](2,"chevron_right"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()}}function G(e,t){1&e&&(u["\u0275\u0275elementStart"](0,"mat-error"),u["\u0275\u0275text"](1," Name is required "),u["\u0275\u0275elementEnd"]())}const A=[{path:"",component:I},{path:"pivot",component:(()=>{class e{constructor(e,t,n){this._salesReportService=e,this.snackBar=t,this.fb=n,this.startDate=c().format("YYYY-MM-01"),this.endDate=c(this.startDate).endOf("month").format("YYYY-MM-01"),this.minStartDate=c("2021-04-01"),this.minEndDate=c(this.minStartDate).endOf("month"),this.dateForm=this.fb.group({startDate:[this.startDate,s.H.required],endDate:[this.endDate,s.H.required]}),this.storageKey="dx-widget-gallery-pivotgrid-storing-sales-report",this.applicationId=49,this.showDataFields=!0,this.showRowFields=!0,this.showColumnFields=!0,this.showFilterFields=!0,this.displayView=!0,this.versionName=new s.j("",[s.H.required]),this.fetchDfrReport=()=>Object(Y.c)(this,void 0,void 0,(function*(){this.setCurrentFyData(),this.filters="dfrReport",this.dataSource=new V.a({fields:[{caption:"PL Name",width:120,dataField:"pl_name",area:"row",expanded:!0},{caption:"Customer Name",width:120,dataField:"project_customer",area:"row",expanded:!0},{caption:"Customer invoice No",width:120,dataField:"sub_to_customer_Invoice_no",area:"row",expanded:!0},{caption:"Milestone Name",width:120,dataField:"milestone_name",area:"row",expanded:!0},{caption:"Milestone Value",dataField:"milestone_value_usd",dataType:"number",summaryType:"sum",format:{type:"fixedPoint",precision:2},area:"data"},{caption:"Customer Invoice Value",dataField:"sub_to_customer_invoice_value",dataType:"number",summaryType:"sum",format:{type:"fixedPoint",precision:2},area:"data"},{caption:"Year",dataField:"invoice_year",area:"column",expanded:!0},{caption:"Month",dataField:"invoice_month",area:"column",expanded:!0},{caption:"P and L ID",width:120,dataField:"p_and_l_id",area:"filter"},{caption:"Customer Currency",dataField:"sub_to_customer_currency",area:"filter",width:120},{caption:"Intercompany Currency",width:120,dataField:"KT_to_sub_currency",area:"filter"},{caption:"Intercompany Description",width:120,dataField:"KT_to_sub_description",area:"filter"},{caption:"Sub Po Date",width:120,dataField:"KT_to_sub_poDate",area:"filter",visible:!1},{caption:"Sub Po Reference",width:120,dataField:"KT_to_sub_poRef",area:"filter",visible:!1},{caption:"Intercompany invoice No",width:120,dataField:"KT_to_sub_Invoice_No",area:"filter"},{caption:"Intercompany Invoice Value",dataField:"KT_to_sub_invoice_value",dataType:"number",summaryType:"sum",format:{type:"fixedPoint",precision:2},width:120,area:"filter"},{caption:"Intercompany Value",width:120,dataField:"KT_value",area:"filter"}],store:new M.a({load:(function(){return this._salesReportService.getSalesPivotReport(this.startDate,this.endDate).then(e=>e)}).bind(this)})})})),this.stateUpdate=()=>Object(Y.c)(this,void 0,void 0,(function*(){})),this.setCurrentFyData()}ngOnInit(){return Object(Y.c)(this,void 0,void 0,(function*(){yield this.fetchDfrReport(),this.getConfigData()}))}setCurrentFyData(){let e=localStorage.getItem("salesReportStartDate"),t=localStorage.getItem("salesReportEndDate");null!=e&&null!=e&&null!=t&&null!=t?(this.startDate=c(e).format("YYYY-MM-DD"),this.endDate=c(t).format("YYYY-MM-DD")):(this.startDate=c(c().subtract(2,"month")).startOf("month").format("YYYY-MM-DD"),this.endDate=c().endOf("month").format("YYYY-MM-DD")),this.minEndDate=c(this.startDate).endOf("month").format("YYYY-MM-DD"),this.dateForm.patchValue({startDate:this.startDate,endDate:this.endDate})}onInitialized(e){this.pivotGrid1=e.component}refresh(){this.pivotGrid1.getDataSource().reload()}reset(){this.pivotGrid1.getDataSource().state({})}setFieldChoosers(e){this.showDataFields=e.showDataFields,this.showRowFields=e.showRowFields,this.showColumnFields=e.showColumnFields,this.showFilterFields=e.showFilterFields}resetFieldChoosers(){this.showDataFields=!1,this.showRowFields=!1,this.showColumnFields=!1,this.showFilterFields=!1}getConfigData(){return Object(Y.c)(this,void 0,void 0,(function*(){yield this._salesReportService.getReportUserViews(this.applicationId).subscribe(e=>{this.views=e;for(let t=0;t<this.views.allViews.length;t++)this.views.allViews[t].visibleDeleteView=!1;this.views.activeView.length>0&&this.views&&(console.log("YES"),this.enableDisplayView(),this.currentView=0,this.pivotGrid1.getDataSource().state(JSON.parse(this.views.activeView[0].saved_config)),this.setFieldChoosers(this.views.activeView[0].field_config.length>0?JSON.parse(this.views.activeView[0].field_config)[0]:{showDataFields:!0,showRowFields:!0,showColumnFields:!0,showFilterFields:!0}))},e=>{this.enableDisplayView(),this.snackBar.open("Error Retrieving Report Views! Try Refreshing","Dismiss"),console.log("Error retrieving views")})}))}saveState(){}changeView(e){this.currentView=e,this.pivotGrid1.getDataSource().state(JSON.parse(this.views.allViews[e].saved_config)),this.setFieldChoosers(JSON.parse(this.views.allViews[e].field_config)[0]),this.enableDisplayView()}deleteVariant(e){let t=this.views.allViews[e].config_name;this.confirmSweetAlert("Do you want to Delete "+t+" Variant?").then(n=>{n.value&&this._salesReportService.deleteVariant(this.applicationId,this.views.allViews[e].customization_id).subscribe(e=>{this.getConfigData(),this.snackBar.open("Variant "+t+" was Deleted Succesfully","Dismiss")},e=>{this.snackBar.open("Failed to delete Variant "+t+".","Dismiss")})})}showDataFieldsFn(){this.showDataFields=!this.showDataFields,this.snackBar.open(1==this.showDataFields?"Displaying Data Fields!":"Data Fields Hidden!","Dismiss",{duration:1e3})}showRowFieldsFn(){this.showRowFields=!this.showRowFields,this.snackBar.open(1==this.showRowFields?"Displaying Row Fields!":"Row Fields Hidden!","Dismiss",{duration:1e3})}showColumnFieldsFn(){this.showColumnFields=!this.showColumnFields,this.snackBar.open(1==this.showColumnFields?"Displaying Column Fields!":"Column Fields Hidden!","Dismiss",{duration:1e3})}showFilterFieldsFn(){this.showFilterFields=!this.showFilterFields,this.snackBar.open(1==this.showFilterFields?"Displaying Filter Fields!":"Filter Fields Hidden!","Dismiss",{duration:1e3})}toggleEditView(){this.displayView=!this.displayView}enableDisplayView(){this.displayView=!0}scrollRight(){this.cardScroll.nativeElement.scrollTo({left:this.cardScroll.nativeElement.scrollLeft+1060,behavior:"smooth"})}scrollLeft(){this.cardScroll.nativeElement.scrollTo({left:this.cardScroll.nativeElement.scrollLeft-1060,behavior:"smooth"})}confirmSweetAlert(e){return k.a.fire({customClass:{title:"title-class",confirmButton:"confirm-button-class",cancelButton:"confirm-button-class"},title:e,type:"warning",showConfirmButton:!0,showCancelButton:!0})}salesReportDateUpdate(){localStorage.setItem("salesReportStartDate",this.dateForm.controls.startDate.value),localStorage.setItem("salesReportEndDate",this.dateForm.controls.endDate.value),this.fetchDfrReport()}getSalesChartData(){this._salesReportService.getSalesReport(this.startDate,this.endDate).subscribe(e=>{this.salesChartData=e},e=>{console.error(e)})}}return e.\u0275fac=function(t){return new(t||e)(u["\u0275\u0275directiveInject"](b),u["\u0275\u0275directiveInject"](x.a),u["\u0275\u0275directiveInject"](s.i))},e.\u0275cmp=u["\u0275\u0275defineComponent"]({type:e,selectors:[["app-sales-pivot"]],viewQuery:function(e,t){if(1&e&&(u["\u0275\u0275viewQuery"](a.a,!0),u["\u0275\u0275viewQuery"](T,!0,u.ElementRef)),2&e){let e;u["\u0275\u0275queryRefresh"](e=u["\u0275\u0275loadQuery"]())&&(t.pivotGrid=e.first),u["\u0275\u0275queryRefresh"](e=u["\u0275\u0275loadQuery"]())&&(t.cardScroll=e.first)}},features:[u["\u0275\u0275ProvidersFeature"]([{provide:p.c,useClass:m.c,deps:[p.f,m.a]},{provide:p.e,useValue:{parse:{dateInput:"DD-MM-YYYY"},display:{dateInput:"DD-MM-YYYY",monthYearLabel:"MMM YYYY"},useUtc:!0}}])],decls:59,vars:39,consts:[[1,"container-fluid"],[3,"formGroup"],[1,"row"],[1,"col-12","order-lg-1","order-md-2","col-lg-12","mainSection","pl-1","pr-2"],[1,"row","mt-3"],[1,"row","w-100","pt-2","pb-2"],[1,"col-2"],["appearance","outline",2,"width","80%"],["matInput","","formControlName","startDate",3,"matDatepicker","min"],["matSuffix","",3,"for"],["sd",""],["matInput","","formControlName","endDate","placeholder","MMMM-YYYY",3,"matDatepicker","min"],["ed",""],[1,"col-1"],["mat-icon-button","","matToolTip","Submit",1,"iconbtn","mr-2",2,"font-weight","normal","margin-left","1%","margin-bottom","1%",3,"click"],[1,"row","w-100","pb-2"],[1,"col-9","pl-0","pr-0","d-flex"],[1,"d-flex","overflow-hidden",2,"max-width","100%","min-width","70%"],["cardScroll",""],["mat-icon-button","","class","iconsSize btn-fab-left",3,"click",4,"ngIf"],["mat-raised-button","","style","font-weight: normal; margin-left: 1%;",3,"ngClass","click","mouseenter","mouseleave",4,"ngFor","ngForOf"],["mat-icon-button","","class","iconsSize btn-fab-right pt-1",3,"click",4,"ngIf"],[1,"col-3","pr-0","pl-0","d-flex","justify-content-end","align-items-baseline"],["mat-icon-button","","matTooltip","Save",1,"iconsSize","ml-1","mt-1",3,"disabled","click"],["matTooltip","Save",1,"iconsSize"],["data-step","9","data-intro","Click to Save as a new version","data-position","right","mat-icon-button","","matTooltip","Save As",1,"iconsSize","ml-1","mt-1",3,"satPopoverAnchor","click"],[1,"iconsSize"],["horizontalAlign","after","verticalAlign","below"],["saveAs",""],[2,"box-shadow","0 3px 1px -2px rgba(0, 0, 0, 0.2),\n                0 2px 2px 0 rgba(0, 0, 0, 0.14),\n                0 1px 5px 0 rgba(0, 0, 0, 0.12) !important","padding","16px !important","background-color","white !important","max-width","300px !important","line-height","33px !important"],["matInput","","placeholder","Save as - Name","required","",3,"formControl"],[4,"ngIf"],["mat-raised-button","","color","warn",3,"disabled","click"],["icon","refresh",1,"ml-1","iconsSize",3,"onClick"],["icon","columnchooser",1,"iconsSize","ml-1",3,"onClick"],["icon","exportxlsx",1,"iconsSize","ml-1",3,"onClick"],["id","costing",3,"allowSortingBySummary","allowSorting","allowFiltering","allowExpandAll","showBorders","dataSource","wordWrapEnabled","height","showColumnTotals","showColumnGrandTotals","showRowTotals","onInitialized"],["fileName","DFR Report",3,"enabled"],[3,"enabled","allowSearch"],[3,"showDataFields","showRowFields","showColumnFields","showFilterFields","allowFieldDragging","visible"],[3,"allowSearch","width","height"],["type","localStorage","storageKey","dx-widget-gallery-pivotgrid-storing-project-report",3,"enabled"],["mat-icon-button","",1,"iconsSize","btn-fab-left",3,"click"],[2,"color","#1e2733 !important","font-size","21px !important"],["mat-raised-button","",2,"font-weight","normal","margin-left","1%",3,"ngClass","click","mouseenter","mouseleave"],["class","resource-costing-icon ml-1",3,"click",4,"ngIf"],[1,"resource-costing-icon","ml-1",3,"click"],["mat-icon-button","",1,"iconsSize","btn-fab-right","pt-1",3,"click"]],template:function(e,t){if(1&e){const e=u["\u0275\u0275getCurrentView"]();u["\u0275\u0275elementStart"](0,"div",0),u["\u0275\u0275elementStart"](1,"form",1),u["\u0275\u0275elementStart"](2,"div",2),u["\u0275\u0275elementStart"](3,"div",3),u["\u0275\u0275element"](4,"div",4),u["\u0275\u0275elementStart"](5,"div",5),u["\u0275\u0275elementStart"](6,"div",6),u["\u0275\u0275elementStart"](7,"mat-form-field",7),u["\u0275\u0275elementStart"](8,"mat-label"),u["\u0275\u0275text"](9,"Start Period"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275element"](10,"input",8),u["\u0275\u0275element"](11,"mat-datepicker-toggle",9),u["\u0275\u0275element"](12,"mat-datepicker",null,10),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](14,"div",6),u["\u0275\u0275elementStart"](15,"mat-form-field",7),u["\u0275\u0275elementStart"](16,"mat-label"),u["\u0275\u0275text"](17,"End Period"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275element"](18,"input",11),u["\u0275\u0275element"](19,"mat-datepicker-toggle",9),u["\u0275\u0275element"](20,"mat-datepicker",null,12),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](22,"div",13),u["\u0275\u0275elementStart"](23,"button",14),u["\u0275\u0275listener"]("click",(function(){return t.salesReportDateUpdate()})),u["\u0275\u0275elementStart"](24,"mat-icon"),u["\u0275\u0275text"](25,"done_all"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](26,"div",15),u["\u0275\u0275elementStart"](27,"div",16),u["\u0275\u0275elementStart"](28,"div",17,18),u["\u0275\u0275template"](30,N,3,0,"button",19),u["\u0275\u0275template"](31,B,3,3,"button",20),u["\u0275\u0275elementEnd"](),u["\u0275\u0275template"](32,j,3,0,"button",21),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](33,"div",22),u["\u0275\u0275elementStart"](34,"button",23),u["\u0275\u0275listener"]("click",(function(){return t.stateUpdate()})),u["\u0275\u0275elementStart"](35,"mat-icon",24),u["\u0275\u0275text"](36,"save"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](37,"button",25),u["\u0275\u0275listener"]("click",(function(){return u["\u0275\u0275restoreView"](e),u["\u0275\u0275reference"](41).toggle()})),u["\u0275\u0275elementStart"](38,"mat-icon",26),u["\u0275\u0275text"](39,"move_to_inbox"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](40,"sat-popover",27,28),u["\u0275\u0275elementStart"](42,"div",29),u["\u0275\u0275elementStart"](43,"div",2),u["\u0275\u0275elementStart"](44,"mat-form-field"),u["\u0275\u0275element"](45,"input",30),u["\u0275\u0275template"](46,G,2,0,"mat-error",31),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](47,"div",2),u["\u0275\u0275elementStart"](48,"button",32),u["\u0275\u0275listener"]("click",(function(){u["\u0275\u0275restoreView"](e);const n=u["\u0275\u0275reference"](41);return t.saveState(),n.toggle()})),u["\u0275\u0275text"](49," Save "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](50,"dx-button",33),u["\u0275\u0275listener"]("onClick",(function(){return t.refresh()})),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](51,"dx-button",34),u["\u0275\u0275listener"]("onClick",(function(){return t.pivotGrid1.getFieldChooserPopup().show()})),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](52,"dx-button",35),u["\u0275\u0275listener"]("onClick",(function(){return t.pivotGrid1.exportToExcel()})),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](53,"dx-pivot-grid",36),u["\u0275\u0275listener"]("onInitialized",(function(e){return t.onInitialized(e)})),u["\u0275\u0275element"](54,"dxo-export",37),u["\u0275\u0275element"](55,"dxo-field-chooser",38),u["\u0275\u0275element"](56,"dxo-field-panel",39),u["\u0275\u0275element"](57,"dxo-header-filter",40),u["\u0275\u0275element"](58,"dxo-state-storing",41),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()}if(2&e){const e=u["\u0275\u0275reference"](13),n=u["\u0275\u0275reference"](21),i=u["\u0275\u0275reference"](41);u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("formGroup",t.dateForm),u["\u0275\u0275advance"](9),u["\u0275\u0275property"]("matDatepicker",e)("min",t.minStartDate),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("for",e),u["\u0275\u0275advance"](7),u["\u0275\u0275property"]("matDatepicker",n)("min",t.minEndDate),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("for",n),u["\u0275\u0275advance"](11),u["\u0275\u0275property"]("ngIf",(null==t.views||null==t.views.activeView?null:t.views.activeView.length)>0),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngForOf",null==t.views?null:t.views.allViews),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf",(null==t.views||null==t.views.activeView?null:t.views.activeView.length)>0),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("disabled",0==(null==t.views||null==t.views.allViews?null:t.views.allViews.length)),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("satPopoverAnchor",i),u["\u0275\u0275advance"](8),u["\u0275\u0275property"]("formControl",t.versionName),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf",t.versionName.hasError("required")),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("disabled",!t.versionName.value),u["\u0275\u0275advance"](5),u["\u0275\u0275property"]("allowSortingBySummary",!0)("allowSorting",!0)("allowFiltering",!0)("allowExpandAll",!0)("showBorders",!0)("dataSource",t.dataSource)("wordWrapEnabled",!1)("height",580)("showColumnTotals",!1)("showColumnGrandTotals",!1)("showRowTotals",!1),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("enabled",!1),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("enabled",!1)("allowSearch",!0),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("showDataFields",t.showDataFields)("showRowFields",t.showRowFields)("showColumnFields",t.showColumnFields)("showFilterFields",t.showFilterFields)("allowFieldDragging",!0)("visible",!0),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("allowSearch",!0)("width",300)("height",400),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("enabled",!0)}},directives:[s.J,s.w,s.n,f.c,f.g,y.b,s.e,S.g,s.v,s.l,S.i,f.i,S.f,F.a,D.a,i.NgIf,i.NgForOf,R.a,O.b,O.a,s.F,s.k,r.a,a.a,_.Sb,_.Ub,_.Wb,_.Cc,_.le,i.NgClass,f.b],styles:[".btn-active[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff}.btn-active[_ngcontent-%COMP%], .btn-not-active[_ngcontent-%COMP%]{font-weight:400;font-size:13px!important;min-width:60px;line-height:33px;padding:0 8px;border-radius:4px;margin-right:6px!important;margin-bottom:3px}.btn-not-active[_ngcontent-%COMP%]{color:rgba(0,0,0,.534);background-color:rgba(194,193,193,.24)}.iconsSize[_ngcontent-%COMP%]{background:transparent;font-size:21px!important;color:#545352!important}.iconTray[_ngcontent-%COMP%]{width:35px!important;height:35px!important;line-height:35px!important}.iconTray[_ngcontent-%COMP%]   .input-class[_ngcontent-%COMP%]{background:transparent;border:none!important;font-size:12px;font-weight:500;width:13%!important}.pop-up-wrapper[_ngcontent-%COMP%]{box-shadow:0 3px 1px -2px rgba(0,0,0,.2),0 2px 2px 0 rgba(0,0,0,.14),0 1px 5px 0 rgba(0,0,0,.12)!important;padding:16px!important;background-color:#fff!important;max-width:300px!important;line-height:33px!important}.horizontal-wrapper[_ngcontent-%COMP%]{overflow:hidden}.version-button[_ngcontent-%COMP%]:hover{cursor:pointer}.version-button[_ngcontent-%COMP%]:hover   .resource-costing-icon[_ngcontent-%COMP%]{visibility:visible!important;font-size:20px!important;animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}.version-button[_ngcontent-%COMP%]   .resource-costing-icon[_ngcontent-%COMP%]{visibility:hidden}.iconbtn[_ngcontent-%COMP%]{background-color:#c92020;color:#fff;padding:0;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}"]}),e})(),data:{breadcrumb:"Pivot"}}];let K=(()=>{class e{}return e.\u0275mod=u["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=u["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[d.k.forChild(A)],d.k]}),e})();var L=n("STbY"),q=n("TU8p");let U=(()=>{class e{}return e.\u0275mod=u["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=u["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,K,o.b,f.e,y.c,s.p,s.E,q.b,R.b,S.h,D.b,F.b,L.e,O.c,a.b,r.b,l.b]]}),e})()}}]);