(window.webpackJsonp=window.webpackJsonp||[]).push([[839],{jZrm:function(e,t,n){"use strict";n.r(t),n.d(t,"IntegrationQuickbooksModule",(function(){return le}));var i=n("ofXK"),o=n("tyNb"),s=n("mrSG"),r=n("3Pt+"),a=n("1G5W"),l=n("XNiG"),c=n("xG9w"),d=n("FKr1"),p=n("1yaQ"),h=n("wd/R"),u=n("fXoL"),m=n("tk/3");let g=(()=>{class e{constructor(e){this.http=e,this.url="/api/integrationLayer/"}getAdminQBConnectionDetails(){return this.http.post(this.url+"getAdminQBConnectionDetails",{})}getQBVoucherTypes(){return this.http.post(this.url+"getQBVoucherTypes",{})}getTokenForAdminConnection(e,t){return this.http.post(this.url+"getTokenForAdminConnection",{book_id:e,company_id:t})}getLongPoolingConfig(){return this.http.post(this.url+"getLongPoolingConfig",{})}getbatchSize(){return this.http.post(this.url+"getbatchSize",{})}postNewSyncIDForQB(){return this.http.post(this.url+"postNewSyncIDForQB",{})}getQBDataFromSource(e){return this.http.post(this.url+"getQBDataFromSource",e)}}return e.\u0275fac=function(t){return new(t||e)(u["\u0275\u0275inject"](m.c))},e.\u0275prov=u["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var f=n("1A3m"),b=n("dNgK"),S=n("l5mm"),y=n("eIep");let v=(()=>{class e{constructor(e){this.http=e,this.apiUrl="/api/integrationLayer/longPollingServiceForQBSync"}longPollForQBSync(e,t){return Object(S.a)(e).pipe(Object(y.a)(()=>this.http.post(this.apiUrl,{sync_id:t})))}}return e.\u0275fac=function(t){return new(t||e)(u["\u0275\u0275inject"](m.c))},e.\u0275prov=u["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var k=n("Xa2L"),D=n("Wp6s"),C=n("TmG/"),T=n("kmnG"),_=n("qFsG"),I=n("iadO"),E=n("d3UM"),O=n("bSwM"),P=n("bTqV"),x=n("NFeN"),R=n("Qu3c");const B=["select"];function M(e,t){1&e&&(u["\u0275\u0275elementStart"](0,"div",3),u["\u0275\u0275element"](1,"mat-spinner",4),u["\u0275\u0275elementEnd"]())}function w(e,t){if(1&e&&(u["\u0275\u0275elementStart"](0,"mat-option",35),u["\u0275\u0275text"](1),u["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;u["\u0275\u0275property"]("value",e.id),u["\u0275\u0275advance"](1),u["\u0275\u0275textInterpolate"](e.name)}}function Q(e,t){1&e&&u["\u0275\u0275element"](0,"div",36)}function F(e,t){1&e&&u["\u0275\u0275element"](0,"div",37)}function j(e,t){1&e&&u["\u0275\u0275element"](0,"div",37)}function q(e,t){1&e&&(u["\u0275\u0275elementStart"](0,"mat-icon",41),u["\u0275\u0275text"](1,"refresh"),u["\u0275\u0275elementEnd"]())}function N(e,t){1&e&&u["\u0275\u0275element"](0,"mat-spinner",42)}function V(e,t){if(1&e){const e=u["\u0275\u0275getCurrentView"]();u["\u0275\u0275elementStart"](0,"div",7),u["\u0275\u0275elementStart"](1,"button",38),u["\u0275\u0275listener"]("click",(function(){return u["\u0275\u0275restoreView"](e),u["\u0275\u0275nextContext"](2).getQBRefreshToken()})),u["\u0275\u0275template"](2,q,2,0,"mat-icon",39),u["\u0275\u0275template"](3,N,1,0,"mat-spinner",40),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()}if(2&e){const e=u["\u0275\u0275nextContext"](2);u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngClass",e.isTokenGeneratedOnRefreshToken?"create-pr-btn-loading":"create-pr-btn"),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf",!e.isTokenGeneratedOnRefreshToken),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf",e.isTokenGeneratedOnRefreshToken)}}function G(e,t){if(1&e&&(u["\u0275\u0275elementStart"](0,"div",43),u["\u0275\u0275elementStart"](1,"div",44),u["\u0275\u0275element"](2,"mat-spinner",45),u["\u0275\u0275elementStart"](3,"p",46),u["\u0275\u0275text"](4),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()),2&e){const e=u["\u0275\u0275nextContext"](2);u["\u0275\u0275advance"](4),u["\u0275\u0275textInterpolate1"]("Kindly Don't refresh the page...! ",e.spinnerStatusText,"")}}const L=function(){return{standalone:!0}};function A(e,t){if(1&e){const e=u["\u0275\u0275getCurrentView"]();u["\u0275\u0275elementStart"](0,"div"),u["\u0275\u0275elementStart"](1,"mat-card",5),u["\u0275\u0275elementStart"](2,"div",6),u["\u0275\u0275elementStart"](3,"div",7),u["\u0275\u0275text"](4," Entity : "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](5,"div",8),u["\u0275\u0275elementStart"](6,"app-input-search",9),u["\u0275\u0275listener"]("change",(function(){return u["\u0275\u0275restoreView"](e),u["\u0275\u0275nextContext"]().changeEntity()})),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](7,"div",10),u["\u0275\u0275elementStart"](8,"form",11),u["\u0275\u0275elementStart"](9,"div",12),u["\u0275\u0275elementStart"](10,"div",13),u["\u0275\u0275elementStart"](11,"div",14),u["\u0275\u0275elementStart"](12,"div",15),u["\u0275\u0275text"](13,"Start Date : "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](14,"mat-form-field",16),u["\u0275\u0275elementStart"](15,"mat-label"),u["\u0275\u0275text"](16,"Select Date"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](17,"input",17),u["\u0275\u0275listener"]("dateChange",(function(){return u["\u0275\u0275restoreView"](e),u["\u0275\u0275nextContext"]().onStartDateChange()})),u["\u0275\u0275elementEnd"](),u["\u0275\u0275element"](18,"mat-datepicker-toggle",18),u["\u0275\u0275element"](19,"mat-datepicker",null,19),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](21,"div",14),u["\u0275\u0275elementStart"](22,"div",15),u["\u0275\u0275text"](23,"End Date : "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](24,"mat-form-field",16),u["\u0275\u0275elementStart"](25,"mat-label"),u["\u0275\u0275text"](26,"Select Date"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275element"](27,"input",20),u["\u0275\u0275element"](28,"mat-datepicker-toggle",18),u["\u0275\u0275element"](29,"mat-datepicker",null,21),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](31,"div",22),u["\u0275\u0275elementStart"](32,"div",14),u["\u0275\u0275elementStart"](33,"div",15),u["\u0275\u0275text"](34,"Voucher Type : "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](35,"mat-form-field",16),u["\u0275\u0275elementStart"](36,"mat-label"),u["\u0275\u0275text"](37,"Voucher Type "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](38,"mat-select",23,24),u["\u0275\u0275elementStart"](40,"div",25),u["\u0275\u0275elementStart"](41,"mat-checkbox",26),u["\u0275\u0275listener"]("ngModelChange",(function(t){return u["\u0275\u0275restoreView"](e),u["\u0275\u0275nextContext"]().allSelected=t}))("change",(function(){return u["\u0275\u0275restoreView"](e),u["\u0275\u0275nextContext"]().toggleAllSelection()})),u["\u0275\u0275text"](42,"Select All"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275template"](43,w,2,2,"mat-option",27),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275template"](44,Q,1,0,"div",28),u["\u0275\u0275elementStart"](45,"div",29),u["\u0275\u0275template"](46,F,1,0,"div",30),u["\u0275\u0275template"](47,j,1,0,"div",30),u["\u0275\u0275template"](48,V,4,3,"div",31),u["\u0275\u0275elementStart"](49,"div",7),u["\u0275\u0275elementStart"](50,"button",32),u["\u0275\u0275listener"]("click",(function(){return u["\u0275\u0275restoreView"](e),u["\u0275\u0275nextContext"]().getQBReportsData()})),u["\u0275\u0275elementStart"](51,"mat-icon",33),u["\u0275\u0275text"](52,"done_all"),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275template"](53,G,5,1,"div",34),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()}if(2&e){const e=u["\u0275\u0275reference"](20),t=u["\u0275\u0275reference"](30),n=u["\u0275\u0275nextContext"]();u["\u0275\u0275advance"](6),u["\u0275\u0275property"]("list",n.availableQBConnections)("formControl",n.selectedEntityBook),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("formGroup",n.queryParamsInputForm),u["\u0275\u0275advance"](9),u["\u0275\u0275property"]("matDatepicker",e)("min",n.minStartDate)("disabled",n.queryFormDisabled),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("for",e),u["\u0275\u0275advance"](9),u["\u0275\u0275property"]("matDatepicker",t)("min",n.minEndDate)("disabled",n.queryFormDisabled||!n.isStartDateSelected),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("for",t),u["\u0275\u0275advance"](10),u["\u0275\u0275property"]("disabled",n.queryFormDisabled),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("ngModel",n.allSelected)("ngModelOptions",u["\u0275\u0275pureFunction0"](22,L)),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngForOf",n.availableQBReportVoucherTypes),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf",n.isTokenNotAvailable),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngIf",!n.isTokenNotAvailable),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf",n.isTokenNotAvailable),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf",n.isTokenNotAvailable),u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngClass",n.isDataRetrived?"create-pr-btn-loading":"create-pr-btn")("disabled",n.isDataRetrived),u["\u0275\u0275advance"](3),u["\u0275\u0275property"]("ngIf",!n.isRetrievalCompletedforSpinner)}}const Y=[{path:"",component:(()=>{class e{constructor(e,t,n,i,o){this.fb=e,this.api=t,this._toaster=n,this.snackBar=i,this.longPollingService=o,this.isQBFormCreationCompleted=!1,this.availableQBConnections=[],this.selectedEntityBook=new r.j(""),this.userSelectedEntityName="",this.userSelectedEntityID="",this.userSelectedBookID=0,this.userSelectedSourceURL="",this.isTokenNotAvailable=!1,this.isTokenGeneratedOnRefreshToken=!1,this.availableQBReportVoucherTypes=[],this.appliedVoucherTypesforQB=[],this.authenticationFieldMapping={key:"Bearer",value:""},this.queryParamsInputForm=this.fb.group({date_start:["",r.H.required],date_end:["",r.H.required],voucher_type:["",r.H.required]}),this.minStartDate=new Date("1900-01-01"),this.isStartDateSelected=!1,this.queryFormDisabled=!0,this.responseProcess=!1,this.onSyncBatchSize=10,this.body={url:""},this.sampleResponse=[],this.response=[],this.tempResponse=[],this.viewLogs=!1,this.isDataRetrived=!1,this.isSyncOnProgress=!0,this.syncProgress=0,this.onSyncBatchSizeQBReports=10,this.onSyncFlag=!1,this.longPoolingTimerConfig=0,this.$onDestroy=new l.b,this.newSyncID=0,this.isRetrievalCompletedforSpinner=!0,this.spinnerStatusText="",this.allSelected=!1}ngOnInit(){return Object(s.c)(this,void 0,void 0,(function*(){yield this.api.getLongPoolingConfig().pipe(Object(a.a)(this.$onDestroy)).subscribe(e=>{"S"==e.messType&&(this.longPoolingTimerConfig=e.data)}),yield this.api.getAdminQBConnectionDetails().pipe(Object(a.a)(this.$onDestroy)).subscribe(e=>{"S"==e.messType?(this.isQBFormCreationCompleted=!0,this.availableQBConnections=e.data):(this.availableQBConnections=[],this.userSelectedEntityName="",this.userSelectedEntityID="")}),yield this.api.getQBVoucherTypes().pipe(Object(a.a)(this.$onDestroy)).subscribe(e=>{this.availableQBReportVoucherTypes="S"==e.messType?e.data:[]}),yield this.api.getbatchSize().pipe(Object(a.a)(this.$onDestroy)).subscribe(e=>{this.onSyncBatchSize=e.Result.on_sync_batch_size,this.onSyncBatchSizeQBReports=e.Result.on_sync_qb_reports_size})}))}changeEntity(){return Object(s.c)(this,void 0,void 0,(function*(){let e=this.selectedEntityBook.value,t=this.availableQBConnections.filter(t=>t.id==e);this.userSelectedEntityName=t[0].entity_name,this.userSelectedEntityID=t[0].company_id,this.userSelectedBookID=t[0].book_id,this.userSelectedSourceURL=t[0].source_url,this.queryFormDisabled=!1,console.log("Changed Entity Value : ",this.userSelectedEntityName,this.userSelectedEntityID,this.userSelectedBookID),yield this.getTokenDetails()}))}getTokenDetails(){return Object(s.c)(this,void 0,void 0,(function*(){yield this.api.getTokenForAdminConnection(this.userSelectedBookID,this.userSelectedEntityID).pipe(Object(a.a)(this.$onDestroy)).subscribe(e=>{"S"==e.messType?(this.isTokenNotAvailable=!1,this.isTokenGeneratedOnRefreshToken=!1,this.authenticationFieldMapping.value=e.tokenDetails.token):(this.isTokenNotAvailable=!0,this.isTokenGeneratedOnRefreshToken=!1,this.authenticationFieldMapping.value="",this._toaster.showError("Token Status","Token Not Generated! Kindly refresh the token ! ",3e3))})}))}getQBRefreshToken(){return Object(s.c)(this,void 0,void 0,(function*(){this.isTokenGeneratedOnRefreshToken=!0,yield this.api.getTokenForAdminConnection(this.userSelectedBookID,this.userSelectedEntityID).pipe(Object(a.a)(this.$onDestroy)).subscribe(e=>{"S"==e.messType?(this.isTokenNotAvailable=!1,this.isTokenGeneratedOnRefreshToken=!1,this.authenticationFieldMapping.value=e.tokenDetails.token):(this.isTokenNotAvailable=!0,this.isTokenGeneratedOnRefreshToken=!1,this.authenticationFieldMapping.value="",this._toaster.showError("Token Status","Token Not Generated for Sync ! Kindly reach out to KEBS team ! ",3e3))})}))}onStartDateChange(){let e=new Date(this.queryParamsInputForm.value.date_start),t=h(this.queryParamsInputForm.value.date_start);this.minEndDate=e,t&&(this.isStartDateSelected=!0)}getQBReportsData(){return Object(s.c)(this,void 0,void 0,(function*(){let e;if(!this.queryParamsInputForm.valid)return this.snackBar.open("Kindly give the values for all the required fields!","Dismiss",{duration:3e3});{this.isDataRetrived=!0,this.appliedVoucherTypesforQB=[],e=this.queryParamsInputForm.value,e.date_start_value=h(e.date_start).format("YYYY-MM-DD"),e.date_end_value=h(e.date_end).format("YYYY-MM-DD"),console.log("Submission QueryParams Details : ",e);let t=[];t=[...new Set(e.voucher_type)],t&&t.length>0&&c.each(t,e=>{var t;let n=this.availableQBReportVoucherTypes.filter(t=>t.id==e);this.appliedVoucherTypesforQB.push(null===(t=n[0])||void 0===t?void 0:t.name)}),e.selectedVouchers=this.appliedVoucherTypesforQB,console.log("User Given Query params Values : ",e);let n=this.selectedEntityBook.value,i=this.availableQBConnections.filter(e=>e.id==n);if(this.userSelectedSourceURL=i[0].source_url,this.userSelectedSourceURL.includes("date_start")||this.userSelectedSourceURL.includes("date_end")){let t=new URL(this.userSelectedSourceURL),n=t.searchParams.get("date_start"),i=t.searchParams.get("date_end");n!=e.date_start_value&&t.searchParams.set("date_start",e.date_start_value),i!=e.date_end_value&&t.searchParams.set("date_end",e.date_end_value),this.userSelectedSourceURL=String(t)}else this.userSelectedSourceURL+=`&date_start=${e.date_start_value}&date_end=${e.date_end_value}`;console.log("Source URL : ",this.userSelectedSourceURL),console.log("Selected Voucher Types : ",this.appliedVoucherTypesforQB),console.log("Authorization Details : ",this.authenticationFieldMapping),yield this.getQBDataFromSource()}}))}getQBDataFromSource(){return Object(s.c)(this,void 0,void 0,(function*(){this.isRetrievalCompletedforSpinner=!1,this.spinnerStatusText="Data is being retrieved from QuickBooks",yield this.api.postNewSyncIDForQB().pipe(Object(a.a)(this.$onDestroy)).subscribe(e=>Object(s.c)(this,void 0,void 0,(function*(){if("S"==e.messType){let t;this.newSyncID=e.data,this.isDataRetrived=!0,this.viewLogs=!1,this.responseProcess=!1,this.body.url=this.userSelectedSourceURL,this.sampleResponse=[],this.response=[],Object.assign(this.body,{method:"GET"}),Object.assign(this.body,{key:this.authenticationFieldMapping.key}),Object.assign(this.body,{value:this.authenticationFieldMapping.key+" "+this.authenticationFieldMapping.value}),Object.assign(this.body,{uniqueVoucherTypes:this.appliedVoucherTypesforQB}),Object.assign(this.body,{sync_id:this.newSyncID});let n=JSON.stringify(this.body);t=JSON.parse(n),yield this.api.getQBDataFromSource(t).pipe(Object(a.a)(this.$onDestroy)).subscribe(e=>Object(s.c)(this,void 0,void 0,(function*(){if("S"===e.messType){this.response.push(e.data),this.tempResponse=Object.assign([],this.response);let t=0,n={},i=Object.keys(this.response[0]);c.each(i,e=>{n[e]=[]});for(let e of Object.keys(this.response[0]))if(t+=this.response[0][e].length,this.response[0][e].length>10){let t=0;for(let i of this.response[0][e]){if(!(t<10))break;n[e].push(i),t++}}else n[e].push(...this.response[0][e]);console.log("Response : ",this.response[0]," with length : ",t),this.responseProcess=!0,this.sampleResponse=[n]}else"E"===e.messType&&(this.responseProcess=!1,this.isDataRetrived=!1,this.response.push(e.error),this.tempResponse=Object.assign([],this.response),console.log("Error on Response : ",this.response[0]),this.response=this.response[0],this.sampleResponse=this.response)}))),yield this.initLongPolling(this.newSyncID)}else this.newSyncID=0})))}))}initLongPolling(e){return Object(s.c)(this,void 0,void 0,(function*(){this.pollingSubscription=yield this.longPollingService.longPollForQBSync(this.longPoolingTimerConfig,e).subscribe(e=>Object(s.c)(this,void 0,void 0,(function*(){"Started"===e.messStatus||""===e.messStatus?this.updateSpinnerStatus("Data is being retrieved from QuickBooks"):"In-Progress"===e.messStatus?this.updateSpinnerStatus("Data is being synced in KEBS"):"S"!==e.messType||"In-Progress"!==e.messStatus&&"Completed"!==e.messStatus||!0!==e.isNoDataFound?"E"===e.messType||"Error"===e.messStatus?this.handleError(e):"S"===e.messType&&"Completed"===e.messStatus&&this.handleCompletedResponse(e):this.handleNoDataFoundResponse(e)})),e=>{console.error("Error fetching data:",e)})}))}updateSpinnerStatus(e){this.isRetrievalCompletedforSpinner=!1,this.spinnerStatusText=e}handleCompletedResponse(e){this.isDataRetrived=!1,this.responseProcess=!0,this.isRetrievalCompletedforSpinner=!0,this._toaster.showSuccess("QuickBooks Reports Synced Successfully !","",5e3),this.stopPolling()}handleNoDataFoundResponse(e){this.isDataRetrived=!1,this.responseProcess=!0,this.isRetrievalCompletedforSpinner=!0,this._toaster.showInfo("No Data Available to Sync !","",5e3),this.stopPolling()}handleError(e){this.isDataRetrived=!1,this.responseProcess=!0,this.isRetrievalCompletedforSpinner=!0,this._toaster.showError("Error on QuickBooks Sync !","",5e3),this.stopPolling()}stopPolling(){this.pollingSubscription&&(this.pollingSubscription.unsubscribe(),this.pollingSubscription=void 0)}toggleAllSelection(){this.select.options.forEach(this.allSelected?e=>e.select():e=>e.deselect())}}return e.\u0275fac=function(t){return new(t||e)(u["\u0275\u0275directiveInject"](r.i),u["\u0275\u0275directiveInject"](g),u["\u0275\u0275directiveInject"](f.a),u["\u0275\u0275directiveInject"](b.a),u["\u0275\u0275directiveInject"](v))},e.\u0275cmp=u["\u0275\u0275defineComponent"]({type:e,selectors:[["app-integration-quickbooks-landing-page"]],viewQuery:function(e,t){if(1&e&&u["\u0275\u0275viewQuery"](B,!0),2&e){let e;u["\u0275\u0275queryRefresh"](e=u["\u0275\u0275loadQuery"]())&&(t.select=e.first)}},features:[u["\u0275\u0275ProvidersFeature"]([{provide:d.c,useClass:p.c,deps:[d.f,p.a]},{provide:d.e,useValue:{parse:{dateInput:"DD-MM-YYYY"},display:{dateInput:"DD-MM-YYYY",monthYearLabel:"MMM YYYY"}}}])],decls:3,vars:2,consts:[[1,"container-fluid","qbIntegration"],["class","row justify-content-center","style","padding-top: 10vh;",4,"ngIf"],[4,"ngIf"],[1,"row","justify-content-center",2,"padding-top","10vh"],["diameter","30"],[1,"add-form-card","mt-2"],[1,"col","d-flex","align-items-baseline"],[1,"col-1"],[1,"col-6"],["placeholder","Select Entity","required","",2,"width","66%","height","45px",3,"list","formControl","change"],[1,"queryParamsInput"],[2,"margin-top","2%",3,"formGroup"],[1,"row","d-flex"],[1,"col-12","d-flex"],[1,"row","d-flex","align-items-center"],[1,"col"],["appearance","outline",1,"col"],["matInput","","required","","formControlName","date_start",3,"matDatepicker","min","disabled","dateChange"],["matSuffix","",3,"for"],["picker1",""],["matInput","","required","","formControlName","date_end",3,"matDatepicker","min","disabled"],["picker2",""],[1,"col-12","d-flex","align-items-center","mt-3"],["formControlName","voucher_type","multiple","","required","","placeholder","Voucher Type",3,"disabled"],["select",""],[1,"col","select-all",2,"margin-top","10px"],[3,"ngModel","ngModelOptions","ngModelChange","change"],[3,"value",4,"ngFor","ngForOf"],["class","tokenGenerationDetails",4,"ngIf"],[1,"col","d-flex",2,"position","relative","margin-top","5vh"],["class","col-10",4,"ngIf"],["class","col-1",4,"ngIf"],["mat-mini-fab","",1,"create-pr-btn",3,"ngClass","disabled","click"],["matTooltip","Sync Data"],["class","content d-flex justify-content-center mt-5 mb-2",4,"ngIf"],[3,"value"],[1,"tokenGenerationDetails"],[1,"col-10"],["mat-mini-fab","",1,"create-pr-btn",3,"ngClass","click"],["matTooltip","Refresh Token",4,"ngIf"],["matTooltip","Please wait...","class","spinner-align","diameter","20",4,"ngIf"],["matTooltip","Refresh Token"],["matTooltip","Please wait...","diameter","20",1,"spinner-align"],[1,"content","d-flex","justify-content-center","mt-5","mb-2"],[2,"align-items","center","display","flex","flex-direction","column"],["diameter","50"],[2,"margin-top","5px"]],template:function(e,t){1&e&&(u["\u0275\u0275elementStart"](0,"div",0),u["\u0275\u0275template"](1,M,2,0,"div",1),u["\u0275\u0275template"](2,A,54,23,"div",2),u["\u0275\u0275elementEnd"]()),2&e&&(u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf",!t.isQBFormCreationCompleted),u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf",t.isQBFormCreationCompleted))},directives:[i.NgIf,k.c,D.a,C.a,r.F,r.v,r.k,r.J,r.w,r.n,T.c,T.g,_.b,r.e,I.g,r.l,I.i,T.i,I.f,E.c,O.a,r.y,i.NgForOf,P.a,i.NgClass,x.a,R.a,d.p],styles:[".qbIntegration[_ngcontent-%COMP%]   .add_btn[_ngcontent-%COMP%]{height:33px;width:33px;display:flex;cursor:pointer;border:1px solid #efa6a6;align-items:center;border-radius:4px;justify-content:center}.qbIntegration[_ngcontent-%COMP%]   .submit-btn[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff}.qbIntegration[_ngcontent-%COMP%]   .submit-btn-loading[_ngcontent-%COMP%]{background-color:#fff}.qbIntegration[_ngcontent-%COMP%]   .delete-btn[_ngcontent-%COMP%]{cursor:pointer}.qbIntegration[_ngcontent-%COMP%]   .delete-btn[_ngcontent-%COMP%]   [_ngcontent-%COMP%]:hover{background-color:rgba(102,97,91,.13725490196078433);border-radius:2px}.qbIntegration[_ngcontent-%COMP%]   .create-pr-btn[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff}.qbIntegration[_ngcontent-%COMP%]   .create-pr-btn-loading[_ngcontent-%COMP%]{background-color:#fff}.qbIntegration[_ngcontent-%COMP%]   .create-pr-btn[_ngcontent-%COMP%]:disabled{background-color:#cf0001;color:#fff;opacity:.5}.qbIntegration[_ngcontent-%COMP%]   .button-base[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff}.qbIntegration[_ngcontent-%COMP%]   .button-base[_ngcontent-%COMP%]:disabled{background-color:#cf0001;color:#fff;opacity:.5}.qbIntegration[_ngcontent-%COMP%]   .title-name[_ngcontent-%COMP%]{color:#9a9a9a;font-size:18px!important}.qbIntegration[_ngcontent-%COMP%]   .card-header-text[_ngcontent-%COMP%]{color:#9a9a9a;font-size:14px!important;border-bottom:1px solid hsla(0,0%,70.6%,.8);margin-left:7px!important;margin-right:7px!important}.qbIntegration[_ngcontent-%COMP%]   .blacklist-mail-field-inputsearch[_ngcontent-%COMP%]{font-size:13px!important}"]}),e})(),redirectTo:"",pathMatch:"full"}];let U=(()=>{class e{}return e.\u0275mod=u["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=u["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[o.k.forChild(Y)],o.k]}),e})();var z=n("XhcP"),$=n("lVl8"),K=n("dlKe"),X=n("M9IT"),H=n("+0xr"),J=n("Dh3D"),Z=n("0IaG"),W=n("xHqg"),ee=n("wZkO"),te=n("Xi0T"),ne=n("STbY"),ie=n("ZzPI"),oe=n("1jcm"),se=n("bv9b"),re=n("/1cH"),ae=n("f0Cb");let le=(()=>{class e{}return e.\u0275mod=u["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=u["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,U,te.a,r.p,r.E,P.b,Z.g,_.c,X.b,H.m,T.e,J.c,x.b,z.g,W.f,D.d,E.d,O.b,ee.g,k.b,I.h,d.n,p.b,R.b,$.b,K.b,ne.e,ie.b,oe.b,se.b,re.c,ae.b]]}),e})()}}]);