(window.webpackJsonp=window.webpackJsonp||[]).push([[788],{JyBO:function(e,n,o){"use strict";o.r(n),o.d(n,"LeadsModule",(function(){return f}));var t=o("ofXK"),d=o("jhN1"),l=o("FKr1"),r=o("1yaQ"),a=o("3Pt+"),i=o("tyNb"),s=o("fXoL");const u=[{path:"",redirectTo:"leadsHome",pathMatch:"full"},{path:"leadsHome",loadChildren:()=>Promise.all([o.e(1),o.e(2),o.e(3),o.e(4),o.e(5),o.e(6),o.e(9),o.e(10),o.e(11),o.e(12),o.e(13),o.e(14),o.e(16),o.e(17),o.e(18),o.e(21),o.e(23),o.e(30),o.e(54),o.e(0),o.e(787)]).then(o.bind(null,"vCyf")).then(e=>e.LeadsHomeModule)},{path:":LeadId/:label",loadChildren:()=>Promise.all([o.e(1),o.e(2),o.e(3),o.e(4),o.e(5),o.e(6),o.e(7),o.e(9),o.e(10),o.e(11),o.e(12),o.e(13),o.e(14),o.e(16),o.e(17),o.e(18),o.e(21),o.e(23),o.e(26),o.e(30),o.e(29),o.e(31),o.e(34),o.e(45),o.e(50),o.e(54),o.e(56),o.e(66),o.e(71),o.e(0),o.e(786)]).then(o.bind(null,"4sa2")).then(e=>e.LeadsDetailModule)}];let c=(()=>{class e{}return e.\u0275mod=s["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=s["\u0275\u0275defineInjector"]({factory:function(n){return new(n||e)},imports:[[i.k.forChild(u)],i.k]}),e})(),f=(()=>{class e{}return e.\u0275mod=s["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=s["\u0275\u0275defineInjector"]({factory:function(n){return new(n||e)},imports:[[t.CommonModule,c,l.n,r.b,a.p,a.E,d.b]]}),e})()}}]);