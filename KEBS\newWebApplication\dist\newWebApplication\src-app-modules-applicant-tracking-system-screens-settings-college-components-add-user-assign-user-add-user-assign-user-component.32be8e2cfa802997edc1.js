(window.webpackJsonp=window.webpackJsonp||[]).push([[925],{iIi0:function(e,t,n){"use strict";n.r(t),n.d(t,"AddUserAssignUserComponent",(function(){return z}));var r=n("mrSG"),o=n("3Pt+"),i=n("0IaG"),a=n("XNiG"),s=n("1G5W"),l=n("fXoL"),c=n("rQiX"),d=n("XNFG"),g=n("XQl4"),m=n("ofXK"),p=n("kmnG"),u=n("qFsG"),h=n("g25w"),f=n("TmG/"),C=n("su5B"),v=n("NFeN"),w=n("Qu3c");function y(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"div",38),l["\u0275\u0275text"](1,"Assign User"),l["\u0275\u0275elementEnd"]())}function x(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"div",38),l["\u0275\u0275text"](1,"Edit User"),l["\u0275\u0275elementEnd"]())}function M(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"div",39),l["\u0275\u0275text"](1,"ASSIGN USER"),l["\u0275\u0275elementEnd"]())}function _(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"div",39),l["\u0275\u0275text"](1,"EDIT USER"),l["\u0275\u0275elementEnd"]())}function E(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"sup",49),l["\u0275\u0275text"](1,"*"),l["\u0275\u0275elementEnd"]())}function O(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div",47),l["\u0275\u0275text"](1),l["\u0275\u0275elementStart"](2,"span"),l["\u0275\u0275template"](3,E,2,0,"sup",48),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"]().$implicit;l["\u0275\u0275advance"](1),l["\u0275\u0275textInterpolate1"](" ",e.label," "),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf",e.isMandatory)}}function P(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div"),l["\u0275\u0275elementStart"](1,"mat-form-field",28),l["\u0275\u0275element"](2,"input",50),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"]().$implicit;l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("placeholder",e.placeholder)("formControlName",e.key)}}function L(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div"),l["\u0275\u0275elementStart"](1,"mat-form-field",51),l["\u0275\u0275elementStart"](2,"input",52),l["\u0275\u0275listener"]("keydown",(function(t){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"](4).preventInvalidValues(t)}))("paste",(function(t){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"](4).preventPaste(t)})),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&e){const e=l["\u0275\u0275nextContext"]().$implicit;l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("placeholder","Enter "+e.label)("formControlName",e.key)}}function b(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div"),l["\u0275\u0275element"](1,"app-multi-email-chip-list",53),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"]().$implicit;l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("formControlName",e.key)("placeholder",e.placeholder)}}function k(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div"),l["\u0275\u0275elementStart"](1,"mat-form-field",28),l["\u0275\u0275element"](2,"input",54),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"]().$implicit;l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("placeholder",e.placeholder)("formControlName",e.key)}}function F(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div"),l["\u0275\u0275element"](1,"app-input-search",55),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"]().$implicit,t=l["\u0275\u0275nextContext"](3);l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("list",t.roleList)("disableNone",!0)("placeholder",e.placeholder)("formControlName",e.key)("hideMatLabel",!0)}}const S=function(e,t){return{fieldKey:e,formGroupIndex:t}};function D(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div"),l["\u0275\u0275elementStart"](1,"app-multi-select-chip",56),l["\u0275\u0275listener"]("onValueChange",(function(t){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"](4).onMultiSelectChipChanges(t)})),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&e){const e=l["\u0275\u0275nextContext"]().$implicit,t=l["\u0275\u0275nextContext"]().index,n=l["\u0275\u0275nextContext"](2);l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("type",2)("placeholder",e.placeholder)("masterData",n.roleList)("selectedValues",n.UserForm.get("FormArray").get(t.toString()).get(e.key).value)("data",l["\u0275\u0275pureFunction2"](5,S,e.key,t))}}function A(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",57),l["\u0275\u0275elementStart"](1,"mat-form-field",28),l["\u0275\u0275element"](2,"input",58),l["\u0275\u0275elementStart"](3,"mat-icon",59),l["\u0275\u0275listener"]("click",(function(){l["\u0275\u0275restoreView"](e);const t=l["\u0275\u0275nextContext"](2).index;return l["\u0275\u0275nextContext"](2).togglePasswordVisibility(t)})),l["\u0275\u0275text"](4),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&e){const e=l["\u0275\u0275nextContext"]().$implicit,t=l["\u0275\u0275nextContext"]().index,n=l["\u0275\u0275nextContext"](2);l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("type",n.showPassword[t]?"text":"password")("formControlName",e.key),l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate1"](" ",n.showPassword[t]?"visibility":"visibility_off"," ")}}function V(e,t){if(1&e&&(l["\u0275\u0275elementContainerStart"](0),l["\u0275\u0275elementStart"](1,"div"),l["\u0275\u0275elementStart"](2,"div",44),l["\u0275\u0275template"](3,O,4,2,"div",45),l["\u0275\u0275template"](4,P,3,2,"div",1),l["\u0275\u0275template"](5,L,3,2,"div",1),l["\u0275\u0275template"](6,b,2,2,"div",1),l["\u0275\u0275template"](7,k,3,2,"div",1),l["\u0275\u0275template"](8,F,2,5,"div",1),l["\u0275\u0275template"](9,D,2,8,"div",1),l["\u0275\u0275template"](10,A,5,3,"div",46),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;l["\u0275\u0275advance"](1),l["\u0275\u0275classMapInterpolate1"]("col-",e.col," over-all-filed "),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf",!e.isDisabled),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf","text"===e.fieldType||"email"===e.fieldType),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf","phonenumber"===e.fieldType),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf","secondary_email"===e.fieldType),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf","display"===e.fieldType),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf","single-select"===e.fieldType),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf","multi-select"===e.fieldType),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf","password"===e.fieldType)}}function I(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"mat-icon",60),l["\u0275\u0275listener"]("click",(function(){l["\u0275\u0275restoreView"](e);const t=l["\u0275\u0275nextContext"]().index;return l["\u0275\u0275nextContext"](2).deleteDynamicFormArray(t)})),l["\u0275\u0275text"](1," delete "),l["\u0275\u0275elementEnd"]()}}function U(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div",40),l["\u0275\u0275elementStart"](1,"div",41),l["\u0275\u0275template"](2,V,11,11,"ng-container",42),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](3,I,2,0,"mat-icon",43),l["\u0275\u0275elementEnd"]()),2&e){const e=t.index,n=l["\u0275\u0275nextContext"](2);let r=null;l["\u0275\u0275property"]("formGroupName",e),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngForOf",n.formFields),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",(null==(r=n.UserForm.get("FormArray"))?null:r.controls.length)>1)}}function N(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",61),l["\u0275\u0275listener"]("click",(function(){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"](2).onClickAddUser()})),l["\u0275\u0275text"](1," + "),l["\u0275\u0275elementStart"](2,"span",62),l["\u0275\u0275text"](3,"Add User"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}}function j(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",63),l["\u0275\u0275listener"]("click",(function(){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"](2).onClickSave()})),l["\u0275\u0275text"](1," Save & Invite "),l["\u0275\u0275elementEnd"]()}}function T(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",64),l["\u0275\u0275listener"]("click",(function(){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"](2).onClickSaveUpdate()})),l["\u0275\u0275text"](1," Save "),l["\u0275\u0275elementEnd"]()}}function G(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",2),l["\u0275\u0275elementStart"](1,"div",3),l["\u0275\u0275template"](2,y,2,0,"div",4),l["\u0275\u0275template"](3,x,2,0,"div",4),l["\u0275\u0275elementStart"](4,"div"),l["\u0275\u0275namespaceSVG"](),l["\u0275\u0275elementStart"](5,"svg",5),l["\u0275\u0275element"](6,"path",6),l["\u0275\u0275element"](7,"path",7),l["\u0275\u0275element"](8,"path",8),l["\u0275\u0275element"](9,"path",9),l["\u0275\u0275element"](10,"path",10),l["\u0275\u0275element"](11,"path",11),l["\u0275\u0275element"](12,"path",12),l["\u0275\u0275element"](13,"path",13),l["\u0275\u0275element"](14,"path",14),l["\u0275\u0275element"](15,"path",15),l["\u0275\u0275element"](16,"path",16),l["\u0275\u0275element"](17,"path",17),l["\u0275\u0275element"](18,"path",18),l["\u0275\u0275element"](19,"path",19),l["\u0275\u0275element"](20,"path",20),l["\u0275\u0275element"](21,"path",21),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](22,"svg",22),l["\u0275\u0275listener"]("click",(function(){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"]().onClickClose()})),l["\u0275\u0275element"](23,"path",23),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275namespaceHTML"](),l["\u0275\u0275elementStart"](24,"div",24),l["\u0275\u0275elementStart"](25,"div",25),l["\u0275\u0275text"](26,"COLLEGE PLACEMENT USER"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](27,"div",26),l["\u0275\u0275elementStart"](28,"div",27),l["\u0275\u0275text"](29,"College Name"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](30,"div"),l["\u0275\u0275elementStart"](31,"mat-form-field",28),l["\u0275\u0275element"](32,"input",29),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](33,"div",30),l["\u0275\u0275template"](34,M,2,0,"div",31),l["\u0275\u0275template"](35,_,2,0,"div",31),l["\u0275\u0275elementStart"](36,"form",32),l["\u0275\u0275elementStart"](37,"div",33),l["\u0275\u0275template"](38,U,4,3,"div",34),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](39,N,4,0,"div",35),l["\u0275\u0275template"](40,j,2,0,"div",36),l["\u0275\u0275template"](41,T,2,0,"div",37),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&e){const e=l["\u0275\u0275nextContext"]();let t=null;l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf",!e.isEditMode),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",e.isEditMode),l["\u0275\u0275advance"](29),l["\u0275\u0275property"]("value",e.collegeName),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf",!e.isEditMode),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",e.isEditMode),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("formGroup",e.UserForm),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngForOf",null==(t=e.UserForm.get("FormArray"))?null:t.controls),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",!e.isEditMode),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",!e.isEditMode),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",e.isEditMode)}}function Z(e,t){if(1&e&&(l["\u0275\u0275elementContainerStart"](0),l["\u0275\u0275elementStart"](1,"div",65),l["\u0275\u0275elementStart"](2,"div",66),l["\u0275\u0275element"](3,"img",67),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](4,"div",68),l["\u0275\u0275elementStart"](5,"div",69),l["\u0275\u0275text"](6,"Loading..."),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementContainerEnd"]()),2&e){const e=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("src",e.uiTextConfig["UI-LOADING-001"],l["\u0275\u0275sanitizeUrl"])}}let z=(()=>{class e{constructor(e,t,n,r,o,i){this.dialogRef=e,this.fb=t,this._atsMasterService=n,this._toaster=r,this._settingService=o,this.data=i,this._onDestroy=new a.b,this.formFields=[],this.roleList=[],this.isLoading=!0,this.uiTextConfig={},this.showPassword=[],this.userId=1,this.assignUserValue=[],this.UserForm=this.fb.group({FormArray:this.fb.array([])})}ngOnInit(){return Object(r.c)(this,void 0,void 0,(function*(){this.collegeId=this.data.collegeId,this.collegeName=this.data.collegeName,yield this.getAtsMasterUiConfig("collegesettingconfig"),yield this.getAtsFormsConfig("collegeSettingAssignUser"),yield this.getAllRole(),null!=this.data.userId?(this.isEditMode=!0,yield this.fetchCollegeUserDetailById(this.data.userId)):(this.isEditMode=!1,yield this.onClickAddUser()),yield this.createFormWithValue(),this.isLoading=!1}))}onMultiSelectChipChanges(e){var t,n;null===(n=null===(t=this.UserForm.get("FormArray").get(e.data.formGroupIndex.toString()))||void 0===t?void 0:t.get(e.data.fieldKey))||void 0===n||n.setValue(e.val)}togglePasswordVisibility(e){this.showPassword[e]=!this.showPassword[e]}onClickClose(){this.dialogRef.close(!1)}getAtsFormsConfig(e){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._atsMasterService.getAtsFormsConfig(e).pipe(Object(s.a)(this._onDestroy)).subscribe({next:n=>{0==n.err?"collegeSettingAssignUser"==e&&(this.formFields=n.data.form[0].formFields[0].formArrayFields):this._toaster.showError("Error",n.msg,7e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Forms Configuration Retrieval Failed!",7e3),n()}}))}))}getAtsMasterUiConfig(e){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._atsMasterService.getAtsMasterUiConfig(e).pipe(Object(s.a)(this._onDestroy)).subscribe({next:n=>{0==n.err?"collegesettingconfig"==e&&(this.uiTextConfig=n.data.assignUserConfigurations.uiTextConfig):this._toaster.showError("Error",n.msg,7e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Master Data Retrieval Failed!",7e3),n()}}))}))}onClickAddUser(){return Object(r.c)(this,void 0,void 0,(function*(){yield this.generatePassword();const e=this.createFormGroup(this.formFields);this.UserForm.get("FormArray").push(e)}))}deleteDynamicFormArray(e){return Object(r.c)(this,void 0,void 0,(function*(){const t=this.UserForm.get("FormArray");e>=0&&e<t.length&&(t.removeAt(e),this.showPassword.splice(e,1))}))}preventInvalidValues(e){const t=e.ctrlKey||e.metaKey;["0","1","2","3","4","5","6","7","8","9","Backspace","Delete","ArrowLeft","ArrowRight","ArrowUp","ArrowDown","Tab"].includes(e.key)||t&&("a"===e.key||"c"===e.key)||e.preventDefault()}preventPaste(e){e.preventDefault()}createFormGroup(e){const t={};return e.forEach(e=>{const n=[];e.isMandatory&&n.push(o.H.required),"userName"===e.key&&n.push(o.H.pattern(/[a-zA-Z ]+/)),"phonenumber"===e.fieldType&&n.push(o.H.pattern(/[0-9]{10}/)),"email"===e.fieldType&&n.push(o.H.pattern(/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/)),t[e.key]=this.fb.control("",n)}),this.showPassword.push(!1),t.password.setValue(this.password.raw),t.hashedPassword.setValue(this.password.hashed),this.fb.group(t)}createFormWithValue(){return Object(r.c)(this,void 0,void 0,(function*(){yield this.generatePassword(),this.assignUserValue.forEach(e=>{const t=this.createFormGroup(this.formFields);Object.keys(e).forEach(n=>{var r,o;t.get(n)&&(null!=this.data.userId?null===(r=t.get(n))||void 0===r||r.setValue(e[n]):"password"!=n&&"hashedPassword"!=n&&(null===(o=t.get(n))||void 0===o||o.setValue(e[n])))}),this.UserForm.get("FormArray").push(t)})}))}onClickSaveUpdate(){return Object(r.c)(this,void 0,void 0,(function*(){if(this.UserForm.valid){const e=this.checkDuplicateEmails(),t=this.checkDuplicatePhoneNumbers();0===e.length&&0===t.length?(this.isLoading=!0,this.assignUserValue=this.UserForm.get("FormArray").value,yield this.updateCollegeUserDetails(this.collegeId,this.data.userId,this.assignUserValue),this.dialogRef.close(!0)):[...e,...t].forEach(e=>{this._toaster.showError("Warning",e.message,7e3)})}else{const e=[];this.UserForm.get("FormArray").controls.forEach((t,n)=>{Object.keys(t.controls).forEach(r=>{const o=t.get(r);if(o.errors){const t=this.formFields.find(e=>e.key===r),i=t?t.label:r;o.errors.required?e.push({message:i+" is Mandatory",index:n}):o.errors.pattern&&e.push({message:"Invalid "+i,index:n})}})}),e.reverse(),e.forEach(e=>{this._toaster.showError("Warning",e.message,7e3)})}}))}onClickSave(){return Object(r.c)(this,void 0,void 0,(function*(){if(this.UserForm.valid){const e=this.checkDuplicateEmails(),t=this.checkDuplicatePhoneNumbers();0===e.length&&0===t.length?(this.isLoading=!0,this.assignUserValue=this.UserForm.get("FormArray").value,yield this.assignUserToCollege(this.collegeId,this.assignUserValue),this.dialogRef.close(!0)):[...e,...t].forEach(e=>{this._toaster.showError("Warning",e.message,7e3)})}else{const e=[];this.UserForm.get("FormArray").controls.forEach((t,n)=>{Object.keys(t.controls).forEach(r=>{const o=t.get(r);if(o.errors){const t=this.formFields.find(e=>e.key===r),i=t?t.label:r;o.errors.required?e.push({message:i+" is Mandatory",index:n}):o.errors.pattern&&e.push({message:"Invalid "+i,index:n})}})}),e.reverse(),e.forEach(e=>{this._toaster.showError("Warning",e.message,7e3)})}}))}checkDuplicateEmails(){const e=this.UserForm.get("FormArray"),t=[],n=new Set;return e.controls.forEach((e,r)=>{var o;const i=e.get("email"),a=(null===(o=e.get("secondaryEmail"))||void 0===o?void 0:o.value)||[];if(i&&i.value){const e=i.value.trim().toLowerCase();n.has(e)?t.push({message:`Email '${e}' is duplicated`,index:r}):n.add(e),a.forEach(e=>{const o=e.trim().toLowerCase();n.has(o)?t.push({message:`Email '${o}' is duplicated`,index:r}):n.add(o)})}}),t}checkDuplicatePhoneNumbers(){const e=this.UserForm.get("FormArray"),t=[],n=new Set;return e.controls.forEach((e,r)=>{const o=e.get("phonenumber");if(o&&o.value){const e=o.value.trim().toLowerCase();n.has(e)?t.push({message:`Phone number '${e}' is duplicated`,index:r}):n.add(e)}}),t}assignUserToCollege(e,t){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((n,r)=>this._settingService.assignUserToCollege(e,t).pipe(Object(s.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?this._toaster.showSuccess("Success",e.msg,3e3):this._toaster.showError("Error",e.msg,3e3),n(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Update College User Detail Failed!",3e3),r()}}))}))}updateCollegeUserDetails(e,t,n){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((r,o)=>this._settingService.updateCollegeUserDetails(e,t,n).pipe(Object(s.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?this._toaster.showSuccess("Success",e.msg,3e3):this._toaster.showError("Error",e.msg,3e3),r(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Update College User Detail Failed!",3e3),o()}}))}))}fetchCollegeUserDetailById(e){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._settingService.fetchCollegeUserDetailById(e).pipe(Object(s.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?this.assignUserValue.push(e.data):this._toaster.showError("Error",e.msg,3e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Fetch User Detail Failed!",3e3),n()}}))}))}generatePassword(){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._settingService.generatePassword().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.password=t:this._toaster.showError("Error",t.msg,3e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Generate Password Failed!",3e3),t()}}))}))}getAllRole(){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getAllRole().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.roleList=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Master Data Retrieval Failed!",7e3),t()}}))}))}}return e.\u0275fac=function(t){return new(t||e)(l["\u0275\u0275directiveInject"](i.h),l["\u0275\u0275directiveInject"](o.i),l["\u0275\u0275directiveInject"](c.a),l["\u0275\u0275directiveInject"](d.a),l["\u0275\u0275directiveInject"](g.a),l["\u0275\u0275directiveInject"](i.a))},e.\u0275cmp=l["\u0275\u0275defineComponent"]({type:e,selectors:[["app-add-user-assign-user"]],decls:2,vars:2,consts:[["class","add-user-container",4,"ngIf"],[4,"ngIf"],[1,"add-user-container"],[1,"header"],["class","assign-user-header",4,"ngIf"],["width","273","height","60","viewBox","0 0 273 72","fill","none",1,"kebs-svg"],["d","M133.604 -22.5168V4.76469C133.604 8.35397 132.088 11.811 129.391 14.4067C125.911 17.7302 120.858 19.1903 115.948 18.2782L115.947 18.2781L93.9788 14.2877L103.741 5.17581L116.468 7.51274L116.469 7.51278C119.365 8.04124 122.089 5.97651 122.089 3.19398V-11.8208L133.604 -22.5168Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M179.022 14.2879L157.082 18.2781L157.081 18.2782C152.177 19.1903 147.13 17.73 143.654 14.4063L143.652 14.4043C140.912 11.856 139.397 8.40031 139.397 4.81089V-22.5164L150.898 -11.8208V2.96299C150.898 5.88334 153.767 8.04193 156.761 7.46638C156.761 7.46632 156.761 7.46626 156.762 7.46621L169.224 5.17578L179.022 14.2879Z","fill","#EAEDF3","stroke","#DADCE2","stroke-width","0.421296"],["d","M133.604 37.6432V64.9405L122.089 54.2563V38.8891C122.089 36.2932 119.564 34.3709 116.818 34.8515L116.817 34.8517L103.741 37.2324L93.9791 28.1309L115.947 24.1449L115.948 24.1448C120.858 23.2337 125.911 24.6924 129.392 28.0123C132.088 30.6051 133.604 34.0581 133.604 37.6432Z","fill","#EAEDF3","stroke","#DADCE2","stroke-width","0.421296"],["d","M139.397 64.9402V37.6586C139.397 34.0695 140.913 30.6125 143.61 28.0168C147.09 24.6932 152.143 23.233 157.053 24.1451L157.054 24.1452L179.021 28.1354L169.261 37.2015L155.936 34.7721C155.936 34.7721 155.936 34.772 155.936 34.772C153.338 34.2895 150.913 36.1237 150.913 38.6288V54.2441L139.397 64.9402Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M40.0758 14.7878V42.0694C40.0758 45.6587 38.5596 49.1157 35.8631 51.7114C32.3828 55.0348 27.3294 56.495 22.4198 55.5829L22.419 55.5828L0.45043 51.5924L10.2125 42.4805L22.9402 44.8174L22.9404 44.8175C25.8368 45.3459 28.5604 43.2812 28.5604 40.4987V25.4839L40.0758 14.7878Z","fill","#F3F5FA","stroke","#DADCE2","stroke-width","0.421296"],["d","M85.4934 51.5924L63.5532 55.5825L63.5524 55.5827C58.6488 56.4948 53.6015 55.0345 50.1255 51.7107L50.1234 51.7087C47.3832 49.1604 45.8689 45.7048 45.8689 42.1153V14.788L57.3696 25.4836V40.2674C57.3696 43.1878 60.2384 45.3464 63.2324 44.7708C63.2327 44.7708 63.233 44.7707 63.2333 44.7707L75.6953 42.4802L85.4934 51.5924Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M40.0758 74.9476V102.245L28.5604 91.5607V76.1936C28.5604 73.5976 26.0352 71.6754 23.29 72.1559L23.2886 72.1562L10.2125 74.5368L0.450787 65.4353L22.419 61.4494L22.4198 61.4492C27.3296 60.5382 32.3831 61.9968 35.8634 65.3167C38.5597 67.9095 40.0758 71.3626 40.0758 74.9476Z","fill","#F7F8FA","stroke","#DADCE2","stroke-width","0.421296"],["d","M45.8689 102.245V74.9631C45.8689 71.3739 47.385 67.9169 50.0813 65.3213C53.5617 61.9977 58.6152 60.5375 63.5248 61.4495L63.5256 61.4497L85.493 65.4399L75.7324 74.5059L62.4081 72.0765C62.408 72.0765 62.4078 72.0765 62.4077 72.0764C59.8092 71.5939 57.3843 73.4281 57.3843 75.9332V91.5485L45.8689 102.245Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M40.0758 14.4832V41.7647C40.0758 45.354 38.5596 48.811 35.8631 51.4067C32.3828 54.7302 27.3294 56.1903 22.4198 55.2782L22.419 55.2781L0.45043 51.2877L10.2125 42.1758L22.9402 44.5127L22.9404 44.5128C25.8368 45.0412 28.5604 42.9765 28.5604 40.194V25.1792L40.0758 14.4832Z","fill","#EAEDF3","fill-opacity","0.5","stroke","#DADCE2","stroke-width","0.421296"],["d","M85.4934 51.2877L63.5532 55.2779L63.5524 55.278C58.6488 56.1901 53.6015 54.7298 50.1255 51.406L50.1234 51.4041C47.3832 48.8557 45.8689 45.4001 45.8689 41.8106V14.4833L57.3696 25.1789V39.9627C57.3696 42.8831 60.2384 45.0417 63.2324 44.4661C63.2327 44.4661 63.233 44.466 63.2333 44.466L75.6953 42.1755L85.4934 51.2877Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M40.0758 74.6429V101.94L28.5604 91.256V75.8889C28.5604 73.2929 26.0352 71.3707 23.29 71.8512L23.2886 71.8515L10.2125 74.2322L0.450787 65.1306L22.419 61.1447L22.4198 61.1445C27.3296 60.2335 32.3831 61.6921 35.8634 65.012C38.5597 67.6048 40.0758 71.0579 40.0758 74.6429Z","fill","#EAEDF3","stroke","#DADCE2","stroke-width","0.421296"],["d","M45.8689 101.94V74.6584C45.8689 71.0692 47.385 67.6122 50.0813 65.0166C53.5617 61.693 58.6152 60.2328 63.5248 61.1448L63.5256 61.145L85.493 65.1352L75.7324 74.2012L62.4081 71.7718C62.408 71.7718 62.4078 71.7718 62.4077 71.7718C59.8092 71.2893 57.3843 73.1234 57.3843 75.6285V91.2438L45.8689 101.94Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M227.131 14.7876V42.0691C227.131 45.6584 225.615 49.1155 222.919 51.7112C219.438 55.0346 214.385 56.4947 209.476 55.5827L209.475 55.5825L187.506 51.5921L197.268 42.4803L209.996 44.8172L209.996 44.8172C212.893 45.3457 215.616 43.281 215.616 40.4984V25.4837L227.131 14.7876Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M272.549 51.5921L250.609 55.5823L250.608 55.5824C245.704 56.4945 240.657 55.0342 237.181 51.7105L237.179 51.7085C234.439 49.1602 232.925 45.7045 232.925 42.1151V14.7878L244.425 25.4834V40.2672C244.425 43.1875 247.294 45.3461 250.288 44.7706C250.288 44.7705 250.289 44.7705 250.289 44.7704L262.751 42.48L272.549 51.5921Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M227.131 74.9474V102.245L215.616 91.5605V76.1933C215.616 73.5974 213.091 71.6751 210.346 72.1557L210.344 72.1559L197.268 74.5366L187.506 65.4351L209.475 61.4491L209.475 61.449C214.385 60.5379 219.439 61.9966 222.919 65.3165C225.615 67.9093 227.131 71.3623 227.131 74.9474Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M232.925 102.244V74.9628C232.925 71.3737 234.441 67.9167 237.137 65.321C240.617 61.9974 245.671 60.5372 250.58 61.4493L250.581 61.4494L272.549 65.4396L262.788 74.5057L249.464 72.0763C249.464 72.0763 249.464 72.0762 249.463 72.0762C246.865 71.5937 244.44 73.4279 244.44 75.933V91.5483L232.925 102.244Z","fill","#EAEDF3","fill-opacity","0.5","stroke","#DADCE2","stroke-width","0.421296"],["width","10","height","10","viewBox","0 0 10 10","fill","none",1,"close-svg",3,"click"],["d","M1.26465 9.83653L0.196289 8.7731L3.98689 5L0.196289 1.2519L1.26465 0.188477L5.05525 3.96158L8.82073 0.188477L9.88909 1.2519L6.09849 5L9.88909 8.7731L8.82073 9.83653L5.05525 6.06343L1.26465 9.83653Z","fill","#1C1B1F"],[1,"college-name-container"],[1,"entity"],[1,"col-4","college-name"],[1,"college-name-label"],["appearance","outline","color","warn",1,"mat-form-field"],["matInput","","readonly","",3,"value"],[1,"add-user-inner-container"],["class","assign-user",4,"ngIf"],[3,"formGroup"],["formArrayName","FormArray"],["class","user-assign-form-container",3,"formGroupName",4,"ngFor","ngForOf"],["class","add-user",3,"click",4,"ngIf"],["class","save--invite-button",3,"click",4,"ngIf"],["class","save-button",3,"click",4,"ngIf"],[1,"assign-user-header"],[1,"assign-user"],[1,"user-assign-form-container",3,"formGroupName"],[1,"row"],[4,"ngFor","ngForOf"],["class","delete-form-array-icon","matTooltip","Delete",3,"click",4,"ngIf"],[1,"d-flex","flex-column"],["class","form-label",4,"ngIf"],["class","password-input-wrapper",4,"ngIf"],[1,"form-label"],["class","required-field",4,"ngIf"],[1,"required-field"],["matInput","",3,"placeholder","formControlName"],["appearance","outline",1,"form-field-class"],["type","text","maxlength","10","matInput","",3,"placeholder","formControlName","keydown","paste"],[3,"formControlName","placeholder"],["matInput","","readonly","",3,"placeholder","formControlName"],[1,"assignUserDropDown",3,"list","disableNone","placeholder","formControlName","hideMatLabel"],[3,"type","placeholder","masterData","selectedValues","data","onValueChange"],[1,"password-input-wrapper"],["matInput","","readonly","",3,"type","formControlName"],[1,"visibility",3,"click"],["matTooltip","Delete",1,"delete-form-array-icon",3,"click"],[1,"add-user",3,"click"],[1,"word-add-user"],[1,"save--invite-button",3,"click"],[1,"save-button",3,"click"],[1,"loading-img"],[2,"max-width","60px","max-height","60px"],[2,"max-width","100%","max-height","100%",3,"src"],[1,"loading-wrapper"],[1,"loading"]],template:function(e,t){1&e&&(l["\u0275\u0275template"](0,G,42,10,"div",0),l["\u0275\u0275template"](1,Z,7,1,"ng-container",1)),2&e&&(l["\u0275\u0275property"]("ngIf",!t.isLoading),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",t.isLoading))},directives:[m.NgIf,p.c,u.b,o.J,o.w,o.n,o.h,m.NgForOf,o.o,o.e,o.v,o.l,o.q,h.a,f.a,C.a,v.a,w.a],styles:['.add-user-container[_ngcontent-%COMP%]{position:relative;min-height:100%}.add-user-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{padding-inline:20px;display:flex;justify-content:space-between;align-items:center;background-color:#f4f4f6}.add-user-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .assign-user-header[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:20px;font-weight:700;line-height:24px;letter-spacing:-.02em;text-align:left;color:#111434}.add-user-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .close-svg[_ngcontent-%COMP%]{cursor:pointer}.add-user-container[_ngcontent-%COMP%]   .college-name-container[_ngcontent-%COMP%]{padding:20px 20px 0 5px}.add-user-container[_ngcontent-%COMP%]   .college-name-container[_ngcontent-%COMP%]   .entity[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:16px;font-weight:700;line-height:20.83px;color:#79ba44;padding:0 15px 5px}.add-user-container[_ngcontent-%COMP%]   .college-name-container[_ngcontent-%COMP%]   .college-name-label[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:11px;font-weight:400;line-height:16px;letter-spacing:.02em;color:#5f6c81}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]{padding:20px}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .assign-user[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:16px;font-weight:700;line-height:20.83px;text-align:left;color:#79ba44}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .user-assign-form-container[_ngcontent-%COMP%]{display:flex;align-items:center}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .user-assign-form-container[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]{width:92%}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .user-assign-form-container[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .over-all-filed[_ngcontent-%COMP%]{padding:10px 10px 10px 0}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .user-assign-form-container[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .over-all-filed[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:11px;font-weight:400;line-height:16px;letter-spacing:.02em;color:#5f6c81}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .user-assign-form-container[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .over-all-filed[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%]   .required-field[_ngcontent-%COMP%]{color:red;font-size:15px;top:0}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .user-assign-form-container[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .over-all-filed[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]{width:100%}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .user-assign-form-container[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .over-all-filed[_ngcontent-%COMP%]   .password-input-wrapper[_ngcontent-%COMP%]{position:relative;padding:0}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .user-assign-form-container[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .over-all-filed[_ngcontent-%COMP%]   .password-input-wrapper[_ngcontent-%COMP%]     .mat-form-field.mat-warn .mat-input-element{width:-moz-fit-content;width:fit-content}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .user-assign-form-container[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .over-all-filed[_ngcontent-%COMP%]   .password-input-wrapper[_ngcontent-%COMP%]   .mat-icon.visibility[_ngcontent-%COMP%]{cursor:pointer;position:absolute;right:10px;top:50%;transform:translateY(-50%);color:rgba(0,0,0,.54)}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .user-assign-form-container[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .over-all-filed[_ngcontent-%COMP%]   .password-input-wrapper[_ngcontent-%COMP%]   .mat-icon.visibility[_ngcontent-%COMP%]:hover{color:rgba(0,0,0,.87)}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .user-assign-form-container[_ngcontent-%COMP%]   .delete-form-array-icon[_ngcontent-%COMP%]{margin:95px 20px 20px;cursor:pointer}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .save--invite-button[_ngcontent-%COMP%]{width:-moz-fit-content;width:fit-content;right:15px;bottom:10px}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .save--invite-button[_ngcontent-%COMP%], .add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);color:#fff;background-color:#79ba44;border-radius:5px;padding:10px;display:flex;align-items:center;position:absolute;justify-content:center;cursor:pointer}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%]{width:-moz-fit-content;width:fit-content;right:20px;bottom:15px;width:65px}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .add-user[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:12px;font-weight:700;line-height:16px;letter-spacing:-.02em;text-align:left;color:#111434;gap:5px;display:flex;cursor:pointer;padding-left:10px;width:-moz-fit-content;width:fit-content}.add-user-container[_ngcontent-%COMP%]   .add-user-inner-container[_ngcontent-%COMP%]   .word-add-user[_ngcontent-%COMP%]{text-decoration:underline}.loading-img[_ngcontent-%COMP%]{height:100%;flex-direction:column;background-color:#fff}.loading-img[_ngcontent-%COMP%], .loading-img[_ngcontent-%COMP%]   .loading-wrapper[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center}.loading-img[_ngcontent-%COMP%]   .loading-wrapper[_ngcontent-%COMP%]{vertical-align:center}.loading-img[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]{color:rgba(0,0,0,.3);font-size:16px}.loading-img[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]:before{content:"Loading...";position:absolute;overflow:hidden;max-width:7em;white-space:nowrap;background:linear-gradient(270deg,var(--atsprimaryColor),var(--atssecondaryColor) 105.29%);background-clip:text;-webkit-background-clip:text;-webkit-text-fill-color:transparent;animation:loading 6s linear infinite}@keyframes loading{0%{max-width:0}}']}),e})()}}]);