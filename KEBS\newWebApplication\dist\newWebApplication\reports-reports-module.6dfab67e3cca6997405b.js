(window.webpackJsonp=window.webpackJsonp||[]).push([[863,861,977,981],{"6ADm":function(e,t,a){"use strict";a.d(t,"a",(function(){return r}));var n=a("ofXK"),o=a("fXoL");let r=(()=>{class e{}return e.\u0275mod=o["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=o["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[n.CommonModule]]}),e})()},HmYF:function(e,t,a){"use strict";a.d(t,"a",(function(){return p}));var n=a("mrSG"),o=a("Iab2"),r=a("EUZL"),i=a("wd/R"),s=a("xG9w"),l=a("fXoL");let p=(()=>{class e{constructor(){this.formatColumn=(e,t,a)=>{const n=r.utils.decode_range(e["!ref"]);for(let o=n.s.r+1;o<=n.e.r;++o){const n=r.utils.encode_cell({r:o,c:t});e[n]&&e[n].v&&(e[n].t="d",e[n].z=a)}}}exportAsExcelFile(e,t,a,n,o){console.log("Excel to JSON Service",e);const i=r.utils.json_to_sheet(e);if(o&&o.length){const e=r.utils.sheet_to_json(i,{header:1}).shift();for(const t of o){const a=e.indexOf(t.fieldKey);this.formatColumn(i,a,t.fieldFormat)}}null==a&&(a=[]),null==n&&(n="DD-MM-YYYY"),this.formatExcelDateData(i,a,n);const s=r.write({Sheets:{data:i},SheetNames:["data"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(s,t)}formatExcelDateData(e,t,a){for(let r of Object.keys(e))if(null!=e[r]&&null!=e[r].t&&null!=e[r].v&&i(e[r].v,a,!0).isValid()){let n=r.replace(/[0-9]/g,"")+"1";0==s.where(t,{value:e[n].v}).length&&null!=e[n]&&null!=e[n].t&&t.push({value:e[n].v,format:a})}let n=[],o=1;for(let r of t)for(let t of Object.keys(e)){let a=parseInt(t.replace(/[^0-9]/g,""));a>o&&(o=a),null!=e[t]&&null!=e[t].v&&e[t].v==r.value&&n.push({value:t.replace(/[0-9]/g,""),format:r.format})}for(let r of n)for(let t=2;t<=o;t++)null!=e[r.value+""+t]&&null!=e[r.value+""+t].t&&(e[r.value+""+t].t="d",null!=e[r.value+""+t].v&&"Invalid date"!=e[r.value+""+t].v?e[r.value+""+t].v=i(e[r.value+""+t].v,r.format).format("YYYY/MM/DD"):(console.log(e[r.value+""+t].t),e[r.value+""+t].v="",e[r.value+""+t].t="s"))}saveAsExcelFile(e,t){const a=new Blob([e],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8"});o.saveAs(a,t.concat(".xlsx"))}exportAsExcelFileWFH(e,t,a){const n=r.utils.json_to_sheet(e),o=r.utils.json_to_sheet(t),i=r.write({Sheets:{All_Approvals:n,Pending_Approvals:o},SheetNames:["All_Approvals","Pending_Approvals"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(i,a)}exportAsExcelFileForPayroll(e,t,a,n,o,i){const s=r.utils.json_to_sheet(e),l=r.utils.json_to_sheet(t),p=r.utils.json_to_sheet(a),d=r.utils.json_to_sheet(n),m=r.utils.json_to_sheet(o),h=r.write({Sheets:{Regular_Report:s,Intern_Report:l,Contract_Report:p,Perdiem_Report:d,RP_Report:m},SheetNames:["Regular_Report","Intern_Report","Contract_Report","Perdiem_Report","RP_Report"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(h,i)}exportAsCsvFileWithSheetName(e,t){return Object(n.c)(this,void 0,void 0,(function*(){let a=r.utils.book_new();for(let t of e){let e=r.utils.json_to_sheet(t.data);r.utils.book_append_sheet(a,e,t.sheetName)}let n=r.write(a,{bookType:"xlsx",type:"binary"});yield this.saveAsCsvFile(n,t)}))}saveAsCsvFile(e,t){return Object(n.c)(this,void 0,void 0,(function*(){const a=new Blob([yield this.s2ab(e)],{type:"application/octet-stream"});o.saveAs(a,t.concat(".csv"))}))}s2ab(e){return Object(n.c)(this,void 0,void 0,(function*(){for(var t=new ArrayBuffer(e.length),a=new Uint8Array(t),n=0;n<e.length;n++)a[n]=255&e.charCodeAt(n);return t}))}exportAsExcelFileWithCellMerge(e,t,a){const n=r.utils.json_to_sheet(e);n["!merges"]=a;const o=r.write({Sheets:{data:n},SheetNames:["data"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(o,t)}exportJsonToExcelWithMultipleSheets(e,t){return Object(n.c)(this,void 0,void 0,(function*(){let a=r.utils.book_new();for(let t of e){let e=r.utils.json_to_sheet(t.data);r.utils.book_append_sheet(a,e,t.sheetName)}let n=r.write(a,{bookType:"xlsx",type:"array"});this.saveAsExcelFile(n,t)}))}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275prov=l["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},fm87:function(e,t,a){"use strict";a.r(t),a.d(t,"ReportsModule",(function(){return le}));var n=a("ofXK"),o=a("STbY"),r=a("kmnG"),i=a("NFeN"),s=a("bTqV"),l=a("qFsG"),p=a("3Pt+"),d=a("Qu3c"),m=a("tyNb"),h=a("mrSG"),c=a("xG9w"),u=a("1G5W"),g=a("XNiG"),b=a("wd/R"),R=a("fXoL"),P=a("Vpr3"),v=a("flaP"),C=a("XXEo"),f=a("LcQX"),_=a("BVzC"),y=a("wiVK"),I=a("jr6c"),M=a("HmYF"),S=a("1A3m"),x=a("Xa2L");function D(e,t){if(1&e){const e=R["\u0275\u0275getCurrentView"]();R["\u0275\u0275elementStart"](0,"mat-icon",26),R["\u0275\u0275elementStart"](1,"button",27),R["\u0275\u0275listener"]("click",(function(){return R["\u0275\u0275restoreView"](e),R["\u0275\u0275nextContext"](2).clearSearch()})),R["\u0275\u0275elementStart"](2,"mat-icon",28),R["\u0275\u0275text"](3,"close "),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"]()}}function k(e,t){if(1&e){const e=R["\u0275\u0275getCurrentView"]();R["\u0275\u0275elementStart"](0,"button",29),R["\u0275\u0275listener"]("click",(function(){return R["\u0275\u0275restoreView"](e),R["\u0275\u0275nextContext"](2).viewByDepartment(!1)})),R["\u0275\u0275elementStart"](1,"mat-icon",20),R["\u0275\u0275text"](2,"settings_backup_restore"),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"]()}}function E(e,t){if(1&e){const e=R["\u0275\u0275getCurrentView"]();R["\u0275\u0275elementStart"](0,"div",8),R["\u0275\u0275elementStart"](1,"div",9),R["\u0275\u0275elementStart"](2,"span",10),R["\u0275\u0275text"](3,"Total Reports : "),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementStart"](4,"span",11),R["\u0275\u0275text"](5),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementStart"](6,"div",12),R["\u0275\u0275elementStart"](7,"mat-form-field",13),R["\u0275\u0275elementStart"](8,"span",14),R["\u0275\u0275elementStart"](9,"mat-icon",15),R["\u0275\u0275text"](10,"search"),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementStart"](11,"input",16),R["\u0275\u0275listener"]("ngModelChange",(function(t){return R["\u0275\u0275restoreView"](e),R["\u0275\u0275nextContext"]().searchReportsText=t}))("ngModelChange",(function(){return R["\u0275\u0275restoreView"](e),R["\u0275\u0275nextContext"]().searchReports()}))("ngModelOptions",(function(){return{debounce:200}})),R["\u0275\u0275elementEnd"](),R["\u0275\u0275template"](12,D,4,0,"mat-icon",17),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementStart"](13,"div",18),R["\u0275\u0275elementStart"](14,"button",19),R["\u0275\u0275elementStart"](15,"mat-icon",20),R["\u0275\u0275text"](16,"visibility"),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementStart"](17,"mat-menu",null,21),R["\u0275\u0275elementStart"](19,"button",22),R["\u0275\u0275listener"]("click",(function(){return R["\u0275\u0275restoreView"](e),R["\u0275\u0275nextContext"]().viewByDepartment(!0)})),R["\u0275\u0275text"](20," By Department "),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275template"](21,k,3,0,"button",23),R["\u0275\u0275elementStart"](22,"button",24),R["\u0275\u0275elementStart"](23,"mat-icon",25),R["\u0275\u0275listener"]("click",(function(){return R["\u0275\u0275restoreView"](e),R["\u0275\u0275nextContext"]().openHelpDialog()})),R["\u0275\u0275text"](24,"help_outline"),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"]()}if(2&e){const e=R["\u0275\u0275reference"](18),t=R["\u0275\u0275nextContext"]();R["\u0275\u0275advance"](5),R["\u0275\u0275textInterpolate"](t.unpinnedReports.length+t.pinnedReports.length),R["\u0275\u0275advance"](6),R["\u0275\u0275property"]("ngModel",t.searchReportsText),R["\u0275\u0275advance"](1),R["\u0275\u0275property"]("ngIf",t.searchReportsText&&""!=t.searchReportsText),R["\u0275\u0275advance"](2),R["\u0275\u0275property"]("matMenuTriggerFor",e),R["\u0275\u0275advance"](7),R["\u0275\u0275property"]("ngIf",!t.isSearchingReports&&t.viewByDepartmentFlag)}}function U(e,t){1&e&&R["\u0275\u0275element"](0,"mat-spinner",46)}function w(e,t){if(1&e){const e=R["\u0275\u0275getCurrentView"]();R["\u0275\u0275elementStart"](0,"div",36),R["\u0275\u0275listener"]("click",(function(){R["\u0275\u0275restoreView"](e);const a=t.$implicit;return R["\u0275\u0275nextContext"](2).routeToReport(a)})),R["\u0275\u0275elementStart"](1,"div",37),R["\u0275\u0275template"](2,U,1,0,"mat-spinner",38),R["\u0275\u0275elementStart"](3,"button",39),R["\u0275\u0275listener"]("click",(function(){R["\u0275\u0275restoreView"](e);const a=t.$implicit;return R["\u0275\u0275nextContext"](2).clickPinReport(a)})),R["\u0275\u0275elementStart"](4,"mat-icon",40),R["\u0275\u0275text"](5,"bookmark"),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementStart"](6,"div",41),R["\u0275\u0275element"](7,"img",42),R["\u0275\u0275elementEnd"](),R["\u0275\u0275element"](8,"div",43),R["\u0275\u0275elementStart"](9,"div",44),R["\u0275\u0275elementStart"](10,"span",45),R["\u0275\u0275text"](11),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;R["\u0275\u0275advance"](2),R["\u0275\u0275property"]("ngIf",null==e?null:e.isDownloading),R["\u0275\u0275advance"](5),R["\u0275\u0275property"]("src",e.imageUrl,R["\u0275\u0275sanitizeUrl"]),R["\u0275\u0275advance"](4),R["\u0275\u0275textInterpolate1"](" ",e.label,"")}}function O(e,t){if(1&e&&(R["\u0275\u0275elementStart"](0,"div",30),R["\u0275\u0275elementStart"](1,"div",31),R["\u0275\u0275elementStart"](2,"span",32),R["\u0275\u0275text"](3),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementStart"](4,"div",33),R["\u0275\u0275elementStart"](5,"div",34),R["\u0275\u0275template"](6,w,12,3,"div",35),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"]()),2&e){const e=R["\u0275\u0275nextContext"]();R["\u0275\u0275advance"](3),R["\u0275\u0275textInterpolate1"]("Pinned Reports : ",e.pinnedReports.length,""),R["\u0275\u0275advance"](3),R["\u0275\u0275property"]("ngForOf",e.pinnedReports)}}function A(e,t){if(1&e&&(R["\u0275\u0275elementStart"](0,"div",31),R["\u0275\u0275elementStart"](1,"span",32),R["\u0275\u0275text"](2),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"]()),2&e){const e=R["\u0275\u0275nextContext"](2);R["\u0275\u0275advance"](2),R["\u0275\u0275textInterpolate1"]("Other Reports : ",e.unpinnedReports.length,"")}}function j(e,t){1&e&&R["\u0275\u0275element"](0,"mat-spinner",46)}function N(e,t){if(1&e){const e=R["\u0275\u0275getCurrentView"]();R["\u0275\u0275elementStart"](0,"div",36),R["\u0275\u0275listener"]("click",(function(){R["\u0275\u0275restoreView"](e);const a=t.$implicit;return R["\u0275\u0275nextContext"](2).routeToReport(a)})),R["\u0275\u0275elementStart"](1,"div",37),R["\u0275\u0275template"](2,j,1,0,"mat-spinner",38),R["\u0275\u0275elementStart"](3,"button",49),R["\u0275\u0275listener"]("click",(function(){R["\u0275\u0275restoreView"](e);const a=t.$implicit;return R["\u0275\u0275nextContext"](2).clickPinReport(a)})),R["\u0275\u0275elementStart"](4,"mat-icon",40),R["\u0275\u0275text"](5,"bookmark_border"),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementStart"](6,"div",41),R["\u0275\u0275element"](7,"img",42),R["\u0275\u0275elementEnd"](),R["\u0275\u0275element"](8,"div",43),R["\u0275\u0275elementStart"](9,"div",44),R["\u0275\u0275elementStart"](10,"span",45),R["\u0275\u0275text"](11),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;R["\u0275\u0275advance"](2),R["\u0275\u0275property"]("ngIf",null==e?null:e.isDownloading),R["\u0275\u0275advance"](5),R["\u0275\u0275property"]("src",e.imageUrl,R["\u0275\u0275sanitizeUrl"]),R["\u0275\u0275advance"](4),R["\u0275\u0275textInterpolate1"](" ",e.label,"")}}function T(e,t){if(1&e&&(R["\u0275\u0275elementStart"](0,"div",47),R["\u0275\u0275template"](1,A,3,1,"div",48),R["\u0275\u0275elementStart"](2,"div",33),R["\u0275\u0275elementStart"](3,"div",34),R["\u0275\u0275template"](4,N,12,3,"div",35),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"]()),2&e){const e=R["\u0275\u0275nextContext"]();R["\u0275\u0275advance"](1),R["\u0275\u0275property"]("ngIf",e.pinnedReports.length>0),R["\u0275\u0275advance"](3),R["\u0275\u0275property"]("ngForOf",e.unpinnedReports)}}function L(e,t){1&e&&R["\u0275\u0275element"](0,"mat-spinner",46)}function q(e,t){1&e&&(R["\u0275\u0275elementStart"](0,"mat-icon",40),R["\u0275\u0275text"](1,"bookmark "),R["\u0275\u0275elementEnd"]())}function F(e,t){1&e&&(R["\u0275\u0275elementStart"](0,"mat-icon",40),R["\u0275\u0275text"](1," bookmark_border "),R["\u0275\u0275elementEnd"]())}function B(e,t){if(1&e){const e=R["\u0275\u0275getCurrentView"]();R["\u0275\u0275elementStart"](0,"div",54),R["\u0275\u0275listener"]("click",(function(){R["\u0275\u0275restoreView"](e);const a=t.$implicit;return R["\u0275\u0275nextContext"](3).routeToReport(a)})),R["\u0275\u0275elementStart"](1,"div",37),R["\u0275\u0275template"](2,L,1,0,"mat-spinner",38),R["\u0275\u0275elementStart"](3,"button",55),R["\u0275\u0275listener"]("click",(function(){R["\u0275\u0275restoreView"](e);const a=t.$implicit;return R["\u0275\u0275nextContext"](3).clickPinReport(a)})),R["\u0275\u0275template"](4,q,2,0,"mat-icon",56),R["\u0275\u0275template"](5,F,2,0,"mat-icon",56),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementStart"](6,"div",41),R["\u0275\u0275element"](7,"img",42),R["\u0275\u0275elementEnd"](),R["\u0275\u0275element"](8,"div",43),R["\u0275\u0275elementStart"](9,"div",44),R["\u0275\u0275elementStart"](10,"span",45),R["\u0275\u0275text"](11),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;R["\u0275\u0275advance"](2),R["\u0275\u0275property"]("ngIf",null==e?null:e.isDownloading),R["\u0275\u0275advance"](1),R["\u0275\u0275property"]("matTooltip",e.isPinned?"Unpin Report":"Pin Report")("ngClass",e.isPinned?"bookmark-button-marked":"bookmark-button"),R["\u0275\u0275advance"](1),R["\u0275\u0275property"]("ngIf",e.isPinned),R["\u0275\u0275advance"](1),R["\u0275\u0275property"]("ngIf",!e.isPinned),R["\u0275\u0275advance"](2),R["\u0275\u0275property"]("src",e.imageUrl,R["\u0275\u0275sanitizeUrl"]),R["\u0275\u0275advance"](4),R["\u0275\u0275textInterpolate1"](" ",e.label,"")}}function $(e,t){if(1&e&&(R["\u0275\u0275elementStart"](0,"div",51),R["\u0275\u0275elementStart"](1,"div",31),R["\u0275\u0275elementStart"](2,"span",32),R["\u0275\u0275text"](3),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementStart"](4,"div",52),R["\u0275\u0275elementStart"](5,"div",34),R["\u0275\u0275template"](6,B,12,7,"div",53),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;R["\u0275\u0275advance"](3),R["\u0275\u0275textInterpolate2"]("",e.departmentName," : ",e.reports.length,""),R["\u0275\u0275advance"](3),R["\u0275\u0275property"]("ngForOf",e.reports)}}function G(e,t){if(1&e&&(R["\u0275\u0275elementStart"](0,"div"),R["\u0275\u0275template"](1,$,7,3,"div",50),R["\u0275\u0275elementEnd"]()),2&e){const e=R["\u0275\u0275nextContext"]();R["\u0275\u0275advance"](1),R["\u0275\u0275property"]("ngForOf",e.departmentViewReports)}}function Q(e,t){1&e&&R["\u0275\u0275element"](0,"mat-spinner",46)}function V(e,t){1&e&&(R["\u0275\u0275elementStart"](0,"mat-icon",40),R["\u0275\u0275text"](1,"bookmark "),R["\u0275\u0275elementEnd"]())}function H(e,t){1&e&&(R["\u0275\u0275elementStart"](0,"mat-icon",40),R["\u0275\u0275text"](1,"bookmark_border "),R["\u0275\u0275elementEnd"]())}function z(e,t){if(1&e){const e=R["\u0275\u0275getCurrentView"]();R["\u0275\u0275elementStart"](0,"div",58),R["\u0275\u0275listener"]("click",(function(){R["\u0275\u0275restoreView"](e);const a=t.$implicit;return R["\u0275\u0275nextContext"](2).routeToReport(a)})),R["\u0275\u0275elementStart"](1,"div",37),R["\u0275\u0275template"](2,Q,1,0,"mat-spinner",38),R["\u0275\u0275elementStart"](3,"button",55),R["\u0275\u0275listener"]("click",(function(){R["\u0275\u0275restoreView"](e);const a=t.$implicit;return R["\u0275\u0275nextContext"](2).clickPinReport(a)})),R["\u0275\u0275template"](4,V,2,0,"mat-icon",56),R["\u0275\u0275template"](5,H,2,0,"mat-icon",56),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementStart"](6,"div",41),R["\u0275\u0275element"](7,"img",42),R["\u0275\u0275elementEnd"](),R["\u0275\u0275element"](8,"div",43),R["\u0275\u0275elementStart"](9,"div",44),R["\u0275\u0275elementStart"](10,"span",45),R["\u0275\u0275text"](11),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;R["\u0275\u0275advance"](2),R["\u0275\u0275property"]("ngIf",null==e?null:e.isDownloading),R["\u0275\u0275advance"](1),R["\u0275\u0275property"]("matTooltip",e.isPinned?"Unpin Report":"Pin Report")("ngClass",e.isPinned?"bookmark-button-marked":"bookmark-button"),R["\u0275\u0275advance"](1),R["\u0275\u0275property"]("ngIf",e.isPinned),R["\u0275\u0275advance"](1),R["\u0275\u0275property"]("ngIf",!e.isPinned),R["\u0275\u0275advance"](2),R["\u0275\u0275property"]("src",e.imageUrl,R["\u0275\u0275sanitizeUrl"]),R["\u0275\u0275advance"](4),R["\u0275\u0275textInterpolate1"](" ",e.label,"")}}function W(e,t){if(1&e&&(R["\u0275\u0275elementStart"](0,"div",51),R["\u0275\u0275elementStart"](1,"div",31),R["\u0275\u0275elementStart"](2,"span",32),R["\u0275\u0275text"](3),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementStart"](4,"div",52),R["\u0275\u0275elementStart"](5,"div",34),R["\u0275\u0275template"](6,z,12,7,"div",57),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"]()),2&e){const e=R["\u0275\u0275nextContext"]();R["\u0275\u0275advance"](3),R["\u0275\u0275textInterpolate1"]("Filtered Reports : ",e.searchingReports.length,""),R["\u0275\u0275advance"](3),R["\u0275\u0275property"]("ngForOf",e.searchingReports)}}function Y(e,t){1&e&&(R["\u0275\u0275elementStart"](0,"div",59),R["\u0275\u0275elementStart"](1,"div"),R["\u0275\u0275elementStart"](2,"h4",60),R["\u0275\u0275text"](3," No Reports Found ! "),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementStart"](4,"div",61),R["\u0275\u0275element"](5,"img",62),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"]())}function K(e,t){1&e&&(R["\u0275\u0275elementStart"](0,"div",63),R["\u0275\u0275elementStart"](1,"div",64),R["\u0275\u0275elementStart"](2,"span"),R["\u0275\u0275text"](3," Getting your reports..."),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementStart"](4,"div",65),R["\u0275\u0275element"](5,"mat-spinner",66),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"]())}let Z=(()=>{class e{constructor(e,t,a,n,o,r,i,s,l,p){this._reportsService=e,this._router=t,this.rolesService=a,this._auth=n,this._util=o,this.errorService=r,this._help=i,this._quoteService=s,this.exceltoJson=l,this._toaster=p,this.allReports=[{label:"MIS",path:"mis",imageUrl:"assets/reports_images/mis.png",applicationId:45,moduleName:"Management",isPinned:!1},{label:"Management",path:"management",imageUrl:"assets/reports_images/management.png",applicationId:46,moduleName:"Management",isPinned:!1},{label:"Governance",path:"governance",imageUrl:"assets/reports_images/governance.png",applicationId:71,moduleName:"Management",isPinned:!1},{label:"Governance (BETA)",path:"governance_beta",imageUrl:"assets/reports_images/governance.png",applicationId:220,moduleName:"Management",isPinned:!1},{label:"P & L Governance",path:"pl-governance",imageUrl:"assets/reports_images/governance.png",applicationId:91,moduleName:"Management",isPinned:!1},{label:"Practice Gov.(LG)",path:"practiceGov",imageUrl:"assets/reports_images/activity_gov.png",applicationId:287,moduleName:"Management",isPinned:!1},{label:"AR",path:"ar",imageUrl:"assets/reports_images/ar.png",applicationId:47,moduleName:"Finance",isPinned:!1},{label:"AR-UBR Days",path:"ar-ubr",imageUrl:"assets/reports_images/ar.png",applicationId:50,moduleName:"Finance",isPinned:!1},{label:"Sales Report",path:"sales",imageUrl:"assets/reports_images/sales_report.png",applicationId:49,moduleName:"Finance",isPinned:!1},{label:"OBV Report",path:"obv",imageUrl:"assets/reports_images/opportunities.png",applicationId:51,moduleName:"Sales",isPinned:!1},{label:"Leads Gov.",path:"leadsGovernanceReport",imageUrl:"assets/reports_images/sales_governance.png",applicationId:75,moduleName:"Management",isPinned:!1},{label:"Opportunity Gov.(LG)",path:"opportunityGovernanceReport",imageUrl:"assets/reports_images/sales_governance.png",applicationId:81,moduleName:"Management",isPinned:!1},{label:"Book and Bill",path:"book_and_bill",imageUrl:"assets/reports_images/sales_governance.png",applicationId:86,moduleName:"Management",isPinned:!1},{label:"Bid Manager",path:"bidManager",imageUrl:"assets/reports_images/sales_governance.png",applicationId:66,moduleName:"Management",isPinned:!1},{label:"Payroll report",path:"timesheet-payroll",imageUrl:"assets/reports_images/timesheet.png",applicationId:52,moduleName:"Human Resource",isPinned:!1},{label:"Payroll Statistics",path:"timesheet-stats",imageUrl:"assets/reports_images/time_stats.png",applicationId:64,moduleName:"Human Resource",isPinned:!1},{label:"DFR Report",path:"dfrReport",imageUrl:"assets/reports_images/dfr.png",applicationId:67,moduleName:"Management",isPinned:!1},{label:"Project Report",path:"projectReport",imageUrl:"assets/reports_images/project_report.png",applicationId:69,moduleName:"Finance",isPinned:!1},{label:"Marketing Gov.",path:"marketingGovernance",imageUrl:"assets/reports_images/project_report.png",applicationId:74,moduleName:"Marketing",isPinned:!1},{label:"Practice MIS",path:"practice-mis",imageUrl:"assets/reports_images/mis.png",applicationId:88,moduleName:"Management",isPinned:!1},{label:"WFH Report",path:"wfh-report",imageUrl:"assets/reports_images/time_stats.png",applicationId:93,moduleName:"WFH",isPinned:!1},{label:"CTA Report",path:"cta-report-new",imageUrl:"assets/reports_images/mis.png",applicationId:94,moduleName:"Management",isPinned:!1},{label:"Timesheet Statistics",path:"timesheet-stats-new",imageUrl:"assets/reports_images/time_stats.png",applicationId:64,moduleName:"Human Resource",isPinned:!1},{label:"Expense",path:"expense-report",imageUrl:"assets/reports_images/time_stats.png",applicationId:76,moduleName:"Expense",isPinned:!1},{label:"Opportunity Gov.",path:"salesGovernanceReportNew",imageUrl:"assets/reports_images/sales_governance.png",applicationId:72,moduleName:"Management",isPinned:!1},{label:"Practice Gov.",path:"practiceGovNew",imageUrl:"assets/reports_images/activity_gov.png",applicationId:70,moduleName:"Management",isPinned:!1},{label:"Appraisal",path:"appraisal-report",imageUrl:"assets/reports_images/activity_gov.png",applicationId:92,moduleName:"Management",isPinned:!1},{label:"AR UBR WC Trend",path:"arubrwctrend-report",imageUrl:"assets/reports_images/time_stats.png",applicationId:105,moduleName:"Management",isPinned:!1},{label:"SG&A Report",path:"sga-report",imageUrl:"assets/reports_images/time_stats.png",applicationId:106,moduleName:"Management",isPinned:!1},{label:"P&L MIS Difference",path:"plMisDifference-report",imageUrl:"assets/reports_images/time_stats.png",applicationId:107},{label:"AMS Report",path:"ams-report",imageUrl:"assets/reports_images/activity_gov.png",applicationId:108,moduleName:"Management",isPinned:!1},{label:"AMS Report (BETA)",path:"ams-report-new",imageUrl:"assets/reports_images/activity_gov.png",applicationId:149,moduleName:"Management",isPinned:!1},{label:"T&M Profitability",path:"tm-profitability-report",imageUrl:"assets/reports_images/activity_gov.png",applicationId:109,moduleName:"Management",isPinned:!1},{label:"Project Gov. Report",path:"projectGovernanceReport",imageUrl:"assets/reports_images/project_report.png",applicationId:151,moduleName:"Management",isPinned:!1},{label:"ISA Data Upload",path:"isa-data-upload-report",imageUrl:"assets/reports_images/project_report.png",applicationId:208,moduleName:"Management",isPinned:!1},{label:"Win Loss Report",path:"winLossReport",imageUrl:"assets/reports_images/project_report.png",applicationId:216,moduleName:"Management",isPinned:!1},{label:"Book and Bill (BETA)",path:"book_and_bill_beta",imageUrl:"assets/reports_images/sales_governance.png",applicationId:213,moduleName:"Management",isPinned:!1},{label:"TS Testcase Report",path:"timesheet_testcase_report",imageUrl:"assets/reports_images/time_stats.png",applicationId:219,moduleName:"Human Resource",isPinned:!1},{label:"Learning Report",path:"lmsUserCertificationsReport",imageUrl:"assets/reports_images/mis.png",applicationId:239,moduleName:"Human Resource",isPinned:!1},{label:"Rewards",path:"awards-report",imageUrl:"assets/reports_images/activity_gov.png",applicationId:101,moduleName:"Management",isPinned:!1},{label:"Contrary Book & Bill",path:"contrary_book_bill",imageUrl:"assets/reports_images/sales_governance.png",applicationId:242,moduleName:"Management",isPinned:!1},{label:"PMO Dashoboard",path:"pmo-dashboard",imageUrl:"assets/reports_images/sales_governance.png",applicationId:260,moduleName:"Report",isPinned:!1},{label:"Bug Report",path:"bugReport",imageUrl:"assets/reports_images/mis.png",applicationId:257,moduleName:"Reports",isPinned:!1},{label:"Task Report",path:"taskReport",imageUrl:"assets/reports_images/mis.png",applicationId:258,moduleName:"Reports",isPinned:!1},{label:"Test Case Report",path:"testCaseReport",imageUrl:"assets/reports_images/mis.png",applicationId:259,moduleName:"Reports",isPinned:!1},{label:"PBI Report",path:"pbiReport",imageUrl:"assets/reports_images/mis.png",applicationId:261,moduleName:"Reports",isPinned:!1},{label:"Project Gantt Report",path:"pgReport",imageUrl:"assets/reports_images/mis.png",applicationId:273,moduleName:"Reports",isPinned:!1},{label:"Onboarding",path:"onboarding-report",imageUrl:"assets/reports_images/time_stats.png",applicationId:255,moduleName:"RMG",isPinned:!1},{label:"Income Statement",path:"income_statement",imageUrl:"assets/reports_images/sales_governance.png",applicationId:256,moduleName:"Management",isPinned:!1},{label:"Management Budget",path:"management_budget",imageUrl:"assets/reports_images/management.png",applicationId:271,moduleName:"Management",isPinned:!1},{label:"Product Bug Report",path:"productBugReport",imageUrl:"assets/reports_images/mis.png",applicationId:285,moduleName:"Reports",isPinned:!1},{label:"Product Task Report",path:"productTaskReport",imageUrl:"assets/reports_images/mis.png",applicationId:286,moduleName:"Reports",isPinned:!1},{label:"MIS Functions",path:"mis_functions",imageUrl:"assets/reports_images/management.png",applicationId:283,moduleName:"Management",isPinned:!1},{label:"Effort Report",path:"lcdp-effort-report",imageUrl:"assets/reports_images/management.png",applicationId:284,moduleName:"Management",isPinned:!1},{label:"Product Gantt",path:"productGanttReport",imageUrl:"assets/reports_images/mis.png",applicationId:288,moduleName:"Reports",isPinned:!1},{label:"Governance Project",path:"goverance_projects",imageUrl:"assets/reports_images/project_report.png",applicationId:300,moduleName:"Reports",isPinned:!1},{label:"Country Level MIS",path:"pl-country-level-mis-report",imageUrl:"assets/reports_images/management.png",applicationId:289,moduleName:"Management",isPinned:!1},{label:"Org Head Count",path:"org-head-count-report",imageUrl:"assets/reports_images/management.png",applicationId:312,moduleName:"Management",isPinned:!1},{label:"Sales & Purchase",path:"sales-purchase-report",imageUrl:"assets/reports_images/management.png",applicationId:322,moduleName:"Reports",isPinned:!1},{label:"Salary Statement",path:"salary-statement",imageUrl:"assets/reports_images/management.png",applicationId:319,moduleName:"Management"},{label:"RFID Report",path:"rfidReport",imageUrl:"assets/reports_images/time_stats.png",applicationId:326,moduleName:"Reports",isPinned:!1},{label:"Product PBI Report",path:"productUserStoryReport",imageUrl:"assets/reports_images/mis.png",applicationId:331,moduleName:"Reports",isPinned:!1},{label:"Product Velocity",path:"productVelocityReport",imageUrl:"assets/reports_images/mis.png",applicationId:335,moduleName:"Reports",isPinned:!1},{label:"Product Burndown",path:"productBurndownReport",imageUrl:"assets/reports_images/mis.png",applicationId:337,moduleName:"Reports",isPinned:!1},{label:"Sales dashboard",path:"sales-dashboard",imageUrl:"assets/reports_images/management.png",applicationId:334,moduleName:"Reports",isPinned:!1},{label:"HR dashboard",path:"hr-dashboard",imageUrl:"assets/reports_images/management.png",applicationId:349,moduleName:"Reports",isPinned:!1},{label:"Contract Tracker",path:"contractTracker",imageUrl:"assets/reports_images/management.png",applicationId:347,moduleName:"Reports",isPinned:!1},{label:"TS Notification",path:"timesheetNotification",imageUrl:"assets/reports_images/management.png",applicationId:370,moduleName:"Reports",isPinned:!1},{label:"RFID FILO Report",path:"rfidfiloReport",imageUrl:"assets/reports_images/time_stats.png",applicationId:360,moduleName:"Reports",isPinned:!1},{label:"PMS Annual Cycle",path:"pmsAnnualCycle",imageUrl:"assets/reports_images/time_stats.png",applicationId:92,moduleName:"Reports"},{label:"Product Test Case",path:"productTestCaseReport",imageUrl:"assets/reports_images/mis.png",applicationId:408,moduleName:"Reports",isPinned:!1},{label:"RMG Governance",path:"rmg-governance",imageUrl:"assets/reports_images/time_stats.png",applicationId:348,moduleName:"Human Resource",isPinned:!1},{label:"Project Portfolio Dashboard",path:"pmo-dashboard-v1",imageUrl:"assets/reports_images/sales_governance.png",applicationId:372,moduleName:"Report",isPinned:!1},{label:"EC Report",path:"ec-report",imageUrl:"assets/reports_images/time_stats.png",applicationId:406,moduleName:"Human Resource",isPinned:!1},{label:"Project Dashboard",path:"projectdashboard",imageUrl:"assets/reports_images/time_stats.png",applicationId:404,moduleName:"Report",isPinned:!1},{label:"Product Timesheet Report",path:"timesheetprojectreport",imageUrl:"assets/reports_images/time_stats.png",applicationId:410,moduleName:"Report",isPinned:!1},{label:"TDS Report",path:"tds-report",imageUrl:"assets/reports_images/ar.png",applicationId:458,moduleName:"Reports",isPinned:!1},{label:"Integration Exception Report",path:"integration-exception-report",imageUrl:"assets/reports_images/ar.png",applicationId:521,moduleName:"Reports",isPinned:!1},{label:"Integration Check Report",path:"integration-check-report",imageUrl:"assets/reports_images/ar.png",applicationId:522,moduleName:"Reports",isPinned:!1},{label:"Resource Management Dashboard",path:"rm-dashboard",imageUrl:"assets/reports_images/ar.png",applicationId:542,moduleName:"Reports",isPinned:!1},{label:"People Allocation Dashboard",path:"people-allocation-dashboard",imageUrl:"assets/reports_images/ar.png",applicationId:542,moduleName:"Reports",isPinned:!1},{label:"Azure Integration logs Report",path:"azure-logs-report",imageUrl:"assets/reports_images/ar.png",applicationId:525,moduleName:"Reports",isPinned:!1},{label:"Notification Logs Report",path:"notification-logs-report",imageUrl:"assets/reports_images/ar.png",applicationId:526,moduleName:"Reports",isPinned:!1},{label:"Error Logs Report",path:"error-logging-report",imageUrl:"assets/reports_images/ar.png",applicationId:543,moduleName:"Reports",isPinned:!1},{label:"Project ISA Report",path:"internal-stakeholders-report",imageUrl:"https://assets.kebs.app/reports_images/undraw_project_team_lc5a.png",applicationId:562,moduleName:"Reports",isPinned:!1},{label:"UBR Report",path:"ubr-report",imageUrl:"assets/reports_images/ar.png",applicationId:556,moduleName:"Reports",isPinned:!1},{label:"Timesheet Dashboard",path:"timesheet-dashboard",imageUrl:"assets/reports_images/time_stats.png",applicationId:565,moduleName:"Reports",isPinned:!1},{label:"Timesheet Report",path:"timesheet-v2-report",imageUrl:"assets/reports_images/time_stats.png",applicationId:567,moduleName:"Reports",isPinned:!1},{label:"Timesheet Stats Report",path:"timesheet-v2-stats-report",imageUrl:"assets/reports_images/time_stats.png",applicationId:569,moduleName:"Reports",isPinned:!1},{label:"Leave Report(M)",path:"employee-Leave-report",imageUrl:"https://assets.kebs.app/reports_images/timesheet.png",applicationId:566,moduleName:"Reports",isPinned:!1},{label:"AR History Report",path:"ar-history-report",imageUrl:"assets/reports_images/ar.png",applicationId:563,moduleName:"Reports",isPinned:!1},{label:"Timesheet Query Report",path:"ts-query-report",imageUrl:"https://assets.kebs.app/reports_images/timesheet.png",applicationId:625,moduleName:"Reports",isPinned:!1},{label:"Leave Report(BU)",path:"employee-Leave-report-Sub-Division",imageUrl:"https://assets.kebs.app/reports_images/timesheet.png",applicationId:568,moduleName:"Reports",isPinned:!1},{label:"People Allocation Gantt",path:"people-allocation-gantt",imageUrl:"https://assets.kebs.app/images/rmg-report-gantt.png",applicationId:623,moduleName:"Reports",isPinned:!1},{label:"Project Tracker",path:"projectTrackerReport",imageUrl:"https://assets.kebs.app/images/rmg-report-gantt.png",applicationId:626,moduleName:"Reports",isPinned:!1},{label:"Finance Dashboard",path:"findash",imageUrl:"https://assets.kebs.app/images/rmg-report-gantt.png",applicationId:952,moduleName:"Reports",isPinned:!1},{label:"Product Dashboard",path:"productDashboard",imageUrl:"assets/reports_images/time_stats.png",applicationId:953,moduleName:"Reports",isPinned:!1},{label:"AP Transaction Report",path:"ap-transaction-report",imageUrl:"https://assets.kebs.app/reports_images/ar.png",applicationId:955,moduleName:"Reports",isPinned:!1},{label:"P&L (Schedule III)",path:"p-and-l-report",imageUrl:"https://assets.kebs.app/images/rmg-report-gantt.png",applicationId:624,moduleName:"Reports",isPinned:!1},{label:"Balance Sheet (Schedule III)",path:"balance-sheet-report",imageUrl:"https://assets.kebs.app/images/rmg-report-gantt.png",applicationId:641,moduleName:"Reports",isPinned:!1},{label:"Cashflow (Schedule III)",path:"cashflow-report",imageUrl:"https://assets.kebs.app/images/rmg-report-gantt.png",applicationId:906,moduleName:"Reports",isPinned:!1},{label:"Leave Error Report",path:"leave-error-report",imageUrl:"https://assets.kebs.app/reports_images/timesheet.png",applicationId:640,isPinned:!1},{label:"Finance Report",path:"finance-report",imageUrl:"https://assets.kebs.app/reports_images/ar.png",applicationId:970,isPinned:!1},{label:"Defect Report",path:"defect-report",imageUrl:"assets/reports_images/time_stats.png",applicationId:964,moduleName:"Reports",isPinned:!1},{label:"Expense Item",path:"expenseItemReport",imageUrl:"assets/reports_images/time_stats.png",applicationId:959,moduleName:"Reports",isPinned:!1},{label:"Timesheet Daily Log Report - Table View",path:"timesheet-daily-log-report-table-view",imageUrl:"assets/reports_images/time_stats.png",applicationId:571,moduleName:"Reports",isPinned:!1},{label:"Attrition Dashboard",path:"attrition-dashboard",imageUrl:"assets/reports_images/management.png",applicationId:962,isPinned:!1},{label:"Compliance Dashboard",path:"compliance-dashboard",imageUrl:"https://assets.kebs.app/reports_images/sales_report.png",applicationId:989,isPinned:!1},{label:"RR Projections",path:"rr-projections",imageUrl:"assets/reports_images/management.png",applicationId:1001,isPinned:!1},{label:"Sub Division MIS",path:"practice-mis-report",imageUrl:"assets/reports_images/ar.png",applicationId:973,moduleName:"Reports",isPinned:!1},{label:"Timesheet Export Report",path:"timesheet-export-report",imageUrl:"assets/reports_images/time_stats.png",applicationId:986,moduleName:"Reports",isPinned:!1},{label:"Project Time Tracker",path:"project-time-tracker",imageUrl:"assets/images/time.png",applicationId:1003,moduleName:"Reports",isPinned:!1},{label:"Project Header Details",path:"project-header-details",imageUrl:"assets/reports_images/project_report.png",applicationId:1005,moduleName:"Reports",isPinned:!1},{label:"Project Header Report",path:"project-header",imageUrl:"assets/reports_images/project_report.png",applicationId:1030,moduleName:"Reports",isPinned:!1},{label:"Project Employee Details",path:"project-employee-details",imageUrl:"assets/reports_images/project_report.png",applicationId:1006,moduleName:"Reports",isPinned:!1},{label:"Employee Summary Details",path:"employee-details",imageUrl:"assets/reports_images/project_report.png",applicationId:1022,moduleName:"Reports",isPinned:!1},{label:"Activity Dashboard",path:"activity-dashboard",imageUrl:"assets/reports_images/management.png",applicationId:1010,isPinned:!1},{label:"Timesheet URS Report",path:"timesheet-urs-report",imageUrl:"assets/reports_images/time_stats.png",applicationId:985,moduleName:"Reports",isPinned:!1},{label:"People Allocation Request Status Report",path:"people-allocation-status-report",imageUrl:"assets/reports_images/project_report.png",applicationId:1012,moduleName:"Reports",isPinned:!1},{label:"Project Efficiency Dashboard",path:"sow-dashboard",imageUrl:"assets/reports_images/project_report.png",applicationId:1028,moduleName:"Reports",isPinned:!1},{label:"P2P Logs Report",path:"p2p-logs-report",imageUrl:"https://assets.kebs.app/reports_images/ar.png",applicationId:1013,moduleName:"Reports",isPinned:!1},{label:"RR Reports",path:"rr-reports",imageUrl:"assets/reports_images/project_report.png",applicationId:1018,moduleName:"Reports",isPinned:!1},{label:"Timesheet Daily Log Report - Calendar View",path:"timesheet-daily-log-report",imageUrl:"assets/reports_images/ar.png",applicationId:600,moduleName:"Reports",isPinned:!1},{label:"Projections Report",path:"rr-projections-position",imageUrl:"assets/reports_images/management.png",applicationId:1031,isPinned:!1},{label:"Quote Monthly Projection",path:"quote-monthly-projection",imageUrl:"assets/reports_images/management.png",applicationId:2501,isPinned:!1},{label:"Sales Report",path:"sales-report",imageUrl:"assets/reports_images/sales_report.png",applicationId:1047,moduleName:"Finance",isPinned:!1},{label:"Soft & Hard Backlog Projection",path:"sbhb-projection",imageUrl:"assets/reports_images/management.png",applicationId:1033,isPinned:!1,isForDownload:!0},{label:"Soft & Hard Backlog Projection",path:"sbhb-projection",imageUrl:"assets/reports_images/management.png",applicationId:1033,isPinned:!1,isForDownload:!0},{label:"Project Milestone Report",path:"project-milestone-report",imageUrl:"assets/reports_images/management.png",applicationId:3001,isPinned:!1,isForDownload:!0},{label:"Project Header Report",path:"project-header-report-details",imageUrl:"assets/reports_images/management.png",applicationId:29388,isPinned:!1,isForDownload:!0},{label:"Schedule III Funcs.",path:"schedule3-admin-functions",imageUrl:"https://assets.kebs.app/images/rmg-report-gantt.png",applicationId:631,moduleName:"Reports",isPinned:!1},{label:"ZohoBooks GL Exception Report",path:"ils-zb-exception-report",imageUrl:"https://assets.kebs.app/reports_images/activity_gov.png",applicationId:1419,isPinned:!1},{label:"ILS Admin Reports",path:"ils-admin-reports",imageUrl:"https://assets.kebs.app/reports_images/activity_gov.png",applicationId:1420,isPinned:!1},{label:"ZohoBooks Sync Log",path:"zb-reports-sync-logs",imageUrl:"https://assets.kebs.app/reports_images/activity_gov.png",applicationId:1421,isPinned:!1},{label:"ZohoBooks GL Exception Report Log",path:"zb-exception-report-logs",imageUrl:"https://assets.kebs.app/reports_images/activity_gov.png",applicationId:1422,isPinned:!1},{label:"Utilization Report",path:"report-dashboard",imageUrl:"assets/reports_images/project_report.png",applicationId:1070,moduleName:"Reports",isPinned:!1},{label:"Revenue Report",path:"revenue-report",imageUrl:"assets/reports_images/management.png",applicationId:3005,moduleName:"Reports",isPinned:!1},{label:"UBR Report (New)",path:"ubr-report-new",imageUrl:"assets/reports_images/time_stats.png",applicationId:972,moduleName:"Reports",isPinned:!1},{label:"UBR Posting Report",path:"ubr-posting",imageUrl:"assets/reports_images/time_stats.png",applicationId:971,moduleName:"Reports",isPinned:!1},{label:"Revenue Forecast",path:"revenue-forecast",imageUrl:"assets/reports_images/time_stats.png",applicationId:3011,moduleName:"Reports",isPinned:!1},{label:"Intercompany Reports",path:"intercompany-reports",imageUrl:"https://assets.kebs.app/reports_images/activity_gov.png",applicationId:2003,moduleName:"Reports",isPinned:!1},{label:"Billing Tracker Report",path:"billing-tracker-report",imageUrl:"assets/reports_images/project_report.png",applicationId:3002,moduleName:"Reports"},{label:"GreyT KEBS Integration Leave Report",path:"greyt-leave-report",imageUrl:"https://assets.kebs.app/reports_images/timesheet.png",applicationId:9012,isPinned:!1},{label:"QuickBooks Sync Log",path:"qb-reports-sync-logs",imageUrl:"https://assets.kebs.app/reports_images/activity_gov.png",applicationId:1424,isPinned:!1},{label:"QuickBooks GL Exception Report",path:"ils-qb-exception-report",imageUrl:"https://assets.kebs.app/reports_images/activity_gov.png",applicationId:1425,isPinned:!1},{label:"QuickBooks GL Exception Report Log",path:"qb-exception-report-logs",imageUrl:"https://assets.kebs.app/reports_images/activity_gov.png",applicationId:1426,isPinned:!1},{label:"MIS Cockpit",path:"mis-cockpit",imageUrl:"https://assets.kebs.app/images/rmg-report-gantt.png",applicationId:630,moduleName:"Reports",isPinned:!1},{label:"Employee level AP report",path:"employee-level-ap-report",imageUrl:"assets/reports_images/time_stats.png",applicationId:3007,moduleName:"Reports",isPinned:!1},{label:"AR Aging Report",path:"ar-aging",imageUrl:"assets/reports_images/sales_report.png",applicationId:3010,moduleName:"Finance",isPinned:!1},{label:"Invoice Details Report",path:"invoice-details",imageUrl:"assets/reports_images/sales_report.png",applicationId:3015,moduleName:"Finance",isPinned:!1},{label:"AR Aging Report",path:"ar-aging-pivot",imageUrl:"assets/reports_images/sales_report.png",applicationId:3021,moduleName:"Finance",isPinned:!1},{label:"Allocation Status Report",path:"employee-allocation-status-report",imageUrl:"assets/reports_images/management.png",applicationId:4001,moduleName:"People Allocation",isPinned:!1},{label:"Employee Soft Booking Report",path:"soft-booking-employee-report",imageUrl:"assets/reports_images/management.png",applicationId:4002,moduleName:"People Allocation"},{label:"Project Billing Plan Report",path:"project-billing-plan",imageUrl:"assets/reports_images/sales_report.png",applicationId:90013,moduleName:"Reports",isPinned:!1},{label:"AD - ED Exception Report",path:"ils-ad-ed-exception-report",imageUrl:"https://assets.kebs.app/reports_images/activity_gov.png",applicationId:1431,moduleName:"Reports",isPinned:!1},{label:"AD - ED Exception Logs",path:"ils-ad-ed-exception-report-logs",imageUrl:"https://assets.kebs.app/reports_images/activity_gov.png",applicationId:1432,moduleName:"Reports",isPinned:!1},{label:"People Allocation Dashboard",path:"people-allocation-dashboard-v2",imageUrl:"assets/reports_images/sales_report.png",applicationId:4003,moduleName:"People Allocation",isPinned:!1},{label:"Revenue Movement Report",path:"revenue-movement",imageUrl:"assets/reports_images/sales_report.png",applicationId:3031,moduleName:"Finance",isPinned:!1},{label:"Integration Trace Logs",path:"integration-trace-report",imageUrl:"https://assets.kebs.app/reports_images/activity_gov.png",applicationId:1429,isPinned:!1},{label:"Ap Aging Report",path:"ap-aging",imageUrl:"assets/reports_images/sales_report.png",applicationId:5002,moduleName:"Finance"},{label:"Country Level MIS V1.2",path:"country-level-mis-pivot",imageUrl:"assets/reports_images/sales_report.png",applicationId:5003,moduleName:"Finance"},{label:"Project Allocation Report",path:"project-allocation-report",imageUrl:"assets/reports_images/management.png",applicationId:3003,moduleName:"Projects",isPinned:!1},{label:"Claim Summary Report",path:"claim-summary-report",imageUrl:"assets/reports_images/sales_report.png",applicationId:5001,moduleName:"Finance",isPinned:!1,isForDownload:!0},{label:"Claim details report",path:"claim-details-report",imageUrl:"assets/reports_images/sales_report.png",applicationId:5e3,moduleName:"Finance",isPinned:!1,isForDownload:!0},{label:"Resource Demand Report",path:"resource-demand-report",imageUrl:"assets/reports_images/management.png",applicationId:8223,moduleName:"People Allocation",isPinned:!1},{label:"Invoice Template Portal",path:"invoice-template",imageUrl:"assets/reports_images/sales_report.png",applicationId:3033,moduleName:"Finance",isPinned:!1},{label:"Bench Aging Report",path:"unassigned-aging-report",imageUrl:"assets/reports_images/management.png",applicationId:8224,moduleName:"People Allocation",isPinned:!1},{label:"Project Revenue Report",path:"project-revenue-report",imageUrl:"assets/reports_images/sales_report.png",applicationId:90014,moduleName:"Reports",isPinned:!1},{label:"Employee Capacity Report",path:"employee-capacity-report",imageUrl:"assets/reports_images/sales_report.png",applicationId:8222},{label:"MIS Custom Report",path:"mis-custom-report",imageUrl:"assets/reports_images/time_stats.png",applicationId:6664,moduleName:"Reports",isPinned:!1},{label:"Project Demobilization Report",path:"project-demobilization-report",imageUrl:"assets/reports_images/management.png",applicationId:90118,moduleName:"Reports",isPinned:!1},{label:"Zifo Allocation Custom Report - 1",path:"custom-financial-report",imageUrl:"assets/reports_images/management.png",applicationId:8324,moduleName:"Reports",isPinned:!1},{label:"Project Milestone Report",path:"project-milestone-report-v2",imageUrl:"assets/reports_images/management.png",applicationId:4444,isPinned:!1,isForDownload:!0},{label:"Project Header Report",path:"project-header-report-details-v2",imageUrl:"assets/reports_images/management.png",applicationId:4445,isPinned:!1,isForDownload:!0},{label:"Employee Activity Report",path:"employee-activity-report",imageUrl:"assets/reports_images/management.png",applicationId:90120,moduleName:"Reports",isPinned:!1},{label:"Billing Advice Report - T&M/FC",path:"billing-advice-report",imageUrl:"assets/reports_images/management.png",applicationId:8e3,moduleName:"Reports",isPinned:!1},{label:"Employee Pop Up Report",path:"employee-pop-up",imageUrl:"assets/reports_images/management.png",applicationId:8330,moduleName:"Employee Pop Up Report",isPinned:!1},{label:"Allocation Efficiency Report",path:"allocation-efficiency-report",imageUrl:"assets/reports_images/management.png",applicationId:3004,moduleName:"Projects"},{label:"RFID Attendance Report",path:"rfid-attendance-report",imageUrl:"assets/reports_images/time_stats.png",applicationId:6665,moduleName:"Reports",isPinned:!1},{label:"Leave Report",path:"leave-report",imageUrl:"assets/reports_images/time_stats.png",applicationId:6666,moduleName:"Reports",isPinned:!1},{label:"UBR Aging Report",path:"ubr-aging-report",imageUrl:"assets/reports_images/sales_report.png",applicationId:3040,moduleName:"Finance",isPinned:!1},{label:"Notifications Report",path:"notifications-report",imageUrl:"assets/reports_images/management.png",applicationId:90119,moduleName:"Reports",isPinned:!1},{label:"Role & Access Control Report",path:"role-access-control-report",imageUrl:"https://assets.kebs.app/reports_images/activity_gov.png",applicationId:1650,moduleName:"Role & Access Management",isPinned:!1}],this.pinnedReports=[],this.unpinnedReports=[],this.departmentViewReports=[],this.viewByDepartmentFlag=!1,this.isSearchingReports=!1,this.isReportsMainPage=!0,this.currentActiveReport="",this._onDestroy=new g.b,this.currentUser={},this.pinnedReportIds=[],this.searchingReports=[],this.searchReportsText="",this.isLoadingReports=!1,this.downloadSBHBReport=e=>{e.isDownloading=!0,this._quoteService.getSBHBProjection().pipe(Object(u.a)(this._onDestroy)).subscribe(t=>{if("S"==t.messType&&t.data&&t.data.length){const e=t.data;let a=[];for(const t of e)for(const[e,n]of Object.entries(t))n&&"object"==typeof n&&n.type&&"date"==n.type&&(a.find(t=>t.fieldKey==e)||a.push({fieldKey:e,fieldFormat:"DD-MMM-YYYY"}),t[e]=n.value?b(n.value).utc().format("YYYY/MM/DD"):null);this.exceltoJson.exportAsExcelFile(e,t.fileName,[],null,a)}else this._toaster.showError("Error",t.messText,2e3);e.isDownloading=!1},t=>{e.isDownloading=!1,console.log(t),this._toaster.showError("Error","Error in Downloading Revenue projection data",2e3)})}}ngOnInit(){return Object(h.c)(this,void 0,void 0,(function*(){this.currentUser=this._auth.getProfile().profile;let e=[];this.isLoadingReports=!0,yield this.getLCDPDashboardsInReports(),this._reportsService.getPinnedReports(this.currentUser.oid).pipe(Object(u.a)(this._onDestroy)).subscribe(t=>Object(h.c)(this,void 0,void 0,(function*(){this.isLoadingReports=!1,t.data&&t.data.length>0&&(this.pinnedReportIds=t.data);for(let t of this.allReports)this.rolesService.isApplicationVisibleForUser(t.applicationId)&&(c.contains(this.pinnedReportIds,t.applicationId)?(t.isPinned=!0,this.pinnedReports.push(t)):this.unpinnedReports.push(t),e.push(t));let a=c.uniq(c.pluck(e,"moduleName"));for(let t of a){let a=c.where(e,{moduleName:t});this.departmentViewReports.push({departmentName:t,reports:a})}})),e=>{console.log(e),this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while retrieving Master list",e&&e.params?e.params:e&&e.error?e.error.params:{})})}))}getLCDPDashboardsInReports(){return Object(h.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>{this._reportsService.getLCDPDashboardsInReports().pipe(Object(u.a)(this._onDestroy)).subscribe(t=>Object(h.c)(this,void 0,void 0,(function*(){t.data&&t.data.length>0&&(this.allReports=this.allReports.concat(t.data)),e(!0)})),t=>{console.log(t),this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Error while retrieving LCDP Dashboards list",t&&t.params?t.params:t&&t.error?t.error.params:{}),e(!0)})})}))}routeToReport(e){e.isLCDPApplication?(this.currentActiveReport=e.label,this._router.navigateByUrl("/main/dash/builder/"+e.lcdpApplicationId+"/1/listDash"),this.isReportsMainPage=!1):1033==e.applicationId&&e.isForDownload?this.downloadSBHBReport(e):(this.currentActiveReport=e.label,this._router.navigateByUrl("/main/reports/"+e.path),this.isReportsMainPage=!1)}backToReportsMainPage(){this._router.navigateByUrl("/main/reports"),this.isReportsMainPage=!0}viewByDepartment(e){this.viewByDepartmentFlag=e,this.isSearchingReports=!1}searchReports(){if(this.searchingReports=[],this.searchReportsText&&""!=this.searchReportsText){this.isSearchingReports=!0;for(let e of this.pinnedReports)(e.label.toLowerCase().includes(this.searchReportsText.toLowerCase())||e.moduleName&&e.moduleName.toLowerCase().includes(this.searchReportsText.toLowerCase()))&&this.searchingReports.push(e);for(let e of this.unpinnedReports)(e.label.toLowerCase().includes(this.searchReportsText.toLowerCase())||e.moduleName&&e.moduleName.toLowerCase().includes(this.searchReportsText.toLowerCase()))&&this.searchingReports.push(e)}else this.isSearchingReports=!1}clearSearch(){this.searchReportsText="",this.searchingReports=[],this.isSearchingReports=!1}clickPinReport(e){e.isPinned=!e.isPinned;let t=[];for(let a of this.unpinnedReports)t.push(a);for(let a of this.pinnedReports)t.push(a);this.pinnedReportIds=[],this.pinnedReports=[],this.unpinnedReports=[];for(let a of t)a.isPinned?(this.pinnedReportIds.push(a.applicationId),this.pinnedReports.push(a)):this.unpinnedReports.push(a);this._reportsService.updatePinnedReports(this.currentUser.oid,this.pinnedReportIds).pipe(Object(u.a)(this._onDestroy)).subscribe(e=>Object(h.c)(this,void 0,void 0,(function*(){e.messText&&this._util.showMessage(e.messText,"dismiss")})))}openHelpDialog(){this._help.openHelpDialog(this._auth.getToken(),17,this.currentUser)}ngOnDestroy(){}}return e.\u0275fac=function(t){return new(t||e)(R["\u0275\u0275directiveInject"](P.a),R["\u0275\u0275directiveInject"](m.g),R["\u0275\u0275directiveInject"](v.a),R["\u0275\u0275directiveInject"](C.a),R["\u0275\u0275directiveInject"](f.a),R["\u0275\u0275directiveInject"](_.a),R["\u0275\u0275directiveInject"](y.c),R["\u0275\u0275directiveInject"](I.a),R["\u0275\u0275directiveInject"](M.a),R["\u0275\u0275directiveInject"](S.a))},e.\u0275cmp=R["\u0275\u0275defineComponent"]({type:e,selectors:[["app-reports"]],decls:8,vars:7,consts:[[1,"container-fluid","reports-home-styles","pl-2","pr-0"],["class","row pb-2",4,"ngIf"],["class","row pt-2 pb-1",4,"ngIf"],["class","row pt-0 pb-2",4,"ngIf"],[4,"ngIf"],["class","row pt-2 pb-2",4,"ngIf"],["class","mt-2",4,"ngIf"],["class","container d-flex pt-1 mt-2 pb-1 flex-column",4,"ngIf"],[1,"row","pb-2"],[1,"col-3","d-flex","pl-0"],[1,"my-auto","sub-heading","pl-2"],[1,"my-auto","heading","pl-2"],[1,"col-6","d-flex","search-bar"],["appearance","outline",1,"ml-auto"],["matPrefix",""],[2,"font-size","21px !important","color","#66615b !important"],["matInput","","type","search","name","search","placeholder","Search Reports",3,"ngModel","ngModelChange","ngModelOptions"],["matSuffix","",4,"ngIf"],[1,"col-3","d-flex","pt-2"],["mat-icon-button","","matTooltip","View",1,"trend-button-inactive","my-auto",3,"matMenuTriggerFor"],[1,"iconButton"],["view","matMenu"],["mat-menu-item","",1,"drop-btn",3,"click"],["mat-icon-button","","matTooltip","Restore Default View","class","trend-button-inactive my-auto","style","margin-left: 10px",3,"click",4,"ngIf"],["mat-icon-button","","matTooltip","Help",1,"ml-2","trend-button-inactive","my-auto"],[1,"iconButton",3,"click"],["matSuffix",""],["mat-button","","matSuffix","","mat-icon-button","","aria-label","Clear",2,"height","30px","width","30px","line-height","1",3,"click"],["matTooltip","Clear Search",2,"font-size","18px !important","color","#66615b !important"],["mat-icon-button","","matTooltip","Restore Default View",1,"trend-button-inactive","my-auto",2,"margin-left","10px",3,"click"],[1,"row","pt-2","pb-1"],[1,"col-6","d-flex","pl-0","pb-2"],[1,"my-auto","sub-heading","pl-3"],[1,"col-12","pl-2","pr-2"],[1,"tiles-wrapper"],["class","card tiles mr-3 mb-3",3,"click",4,"ngFor","ngForOf"],[1,"card","tiles","mr-3","mb-3",3,"click"],[1,"card-body","pt-2","pb-1","pl-1","pr-1"],["diameter","20","matTooltip","Downloading...",4,"ngIf"],["mat-icon-button","","matTooltip","Unpin Report",1,"bookmark-button-marked",3,"click"],[1,"tile-bookmark-icon"],[1,"row","pt-2","pb-1","d-flex","justify-content-center"],["height","55px","width","55px",3,"src"],[1,"layer"],[1,"row","d-flex","pt-2","pb-2"],[1,"mx-auto","tiles-title"],["diameter","20","matTooltip","Downloading..."],[1,"row","pt-0","pb-2"],["class","col-6 d-flex pl-0 pb-2",4,"ngIf"],["mat-icon-button","","matTooltip","Pin Report",1,"bookmark-button",3,"click"],["class","row pt-2 pb-2",4,"ngFor","ngForOf"],[1,"row","pt-2","pb-2"],[1,"col-12"],["class","card tiles mr-3 mb-2",3,"click",4,"ngFor","ngForOf"],[1,"card","tiles","mr-3","mb-2",3,"click"],["mat-icon-button","",3,"matTooltip","ngClass","click"],["class","tile-bookmark-icon",4,"ngIf"],["class","card tiles mr-3",3,"click",4,"ngFor","ngForOf"],[1,"card","tiles","mr-3",3,"click"],[1,"mt-2"],[1,"d-flex","justify-content-center","align-items-center","mt-5","slide-in-top"],[1,"d-flex","justify-content-center","align-items-center","slide-in-bottom"],["src","https://assets.kebs.app/images/nomilestone.png","height","260","width","260",1,"mt-4"],[1,"container","d-flex","pt-1","mt-2","pb-1","flex-column"],[1,"row","justify-content-center","mt-4","mb-5","slide-in-top"],[1,"row","justify-content-center"],["diameter","25","matTooltip","Loading Reports ..."]],template:function(e,t){1&e&&(R["\u0275\u0275elementStart"](0,"div",0),R["\u0275\u0275template"](1,E,25,5,"div",1),R["\u0275\u0275template"](2,O,7,2,"div",2),R["\u0275\u0275template"](3,T,5,2,"div",3),R["\u0275\u0275template"](4,G,2,1,"div",4),R["\u0275\u0275template"](5,W,7,2,"div",5),R["\u0275\u0275template"](6,Y,6,0,"div",6),R["\u0275\u0275template"](7,K,6,0,"div",7),R["\u0275\u0275elementEnd"]()),2&e&&(R["\u0275\u0275advance"](1),R["\u0275\u0275property"]("ngIf",t.isReportsMainPage),R["\u0275\u0275advance"](1),R["\u0275\u0275property"]("ngIf",t.isReportsMainPage&&!t.isLoadingReports&&!t.isSearchingReports&&!t.viewByDepartmentFlag&&t.pinnedReports.length>0),R["\u0275\u0275advance"](1),R["\u0275\u0275property"]("ngIf",t.isReportsMainPage&&!t.isLoadingReports&&!t.isSearchingReports&&!t.viewByDepartmentFlag&&t.unpinnedReports.length>0),R["\u0275\u0275advance"](1),R["\u0275\u0275property"]("ngIf",t.isReportsMainPage&&!t.isLoadingReports&&!t.isSearchingReports&&t.viewByDepartmentFlag&&t.departmentViewReports.length>0),R["\u0275\u0275advance"](1),R["\u0275\u0275property"]("ngIf",t.isReportsMainPage&&!t.isLoadingReports&&t.isSearchingReports&&t.searchingReports.length>0),R["\u0275\u0275advance"](1),R["\u0275\u0275property"]("ngIf",t.isReportsMainPage&&!t.isLoadingReports&&!t.isSearchingReports&&!t.viewByDepartmentFlag&&t.pinnedReports.length+t.unpinnedReports.length==0||!t.isSearchingReports&&t.viewByDepartmentFlag&&0==t.departmentViewReports.length||t.isSearchingReports&&0==t.searchingReports.length),R["\u0275\u0275advance"](1),R["\u0275\u0275property"]("ngIf",t.isReportsMainPage&&t.isLoadingReports))},directives:[n.NgIf,r.c,r.h,i.a,l.b,p.e,p.v,p.y,s.a,d.a,o.f,o.g,o.d,r.i,n.NgForOf,x.c,n.NgClass],styles:[".reports-home-styles[_ngcontent-%COMP%]   .sub-heading[_ngcontent-%COMP%]{color:#66615b;font-size:14px;font-weight:400}.reports-home-styles[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]{color:#1a1a1a;font-size:14px;font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.reports-home-styles[_ngcontent-%COMP%]   .arrow-icons[_ngcontent-%COMP%]{color:#868683;font-size:18px}.reports-home-styles[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#3c3c3c;font-weight:400;font-size:18px}.reports-home-styles[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field{width:40vw;padding-top:5px}.reports-home-styles[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-infix{font-size:14px!important;padding:.5em 0!important;border-top:.54375em solid transparent!important}.reports-home-styles[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-wrapper{padding-bottom:0!important}.reports-home-styles[_ngcontent-%COMP%]   .trend-button-inactive[_ngcontent-%COMP%]{margin-top:3px!important;line-height:8px;width:26px;height:26px;margin-right:5px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.reports-home-styles[_ngcontent-%COMP%]   .trend-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.reports-home-styles[_ngcontent-%COMP%]   .trend-button-active[_ngcontent-%COMP%]{background-color:#cf0001!important;margin-top:3px!important;line-height:8px;width:26px;height:26px;margin-right:5px!important;box-shadow:0 3px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.reports-home-styles[_ngcontent-%COMP%]   .trend-button-active[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#fff;font-size:18px}.reports-home-styles[_ngcontent-%COMP%]   .drop-btn[_ngcontent-%COMP%]{font-size:13px!important;height:38px!important;line-height:2!important;padding:0 13px}.reports-home-styles[_ngcontent-%COMP%]   .tiles-wrapper[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap}.reports-home-styles[_ngcontent-%COMP%]   .tiles[_ngcontent-%COMP%]{width:140px;min-height:119px;height:auto!important;transition:all .3s;overflow:hidden;animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}.reports-home-styles[_ngcontent-%COMP%]   .tiles-title[_ngcontent-%COMP%]{z-index:5;position:relative;font-size:14px!important;font-weight:500!important;color:#66615b;transition:all .1s}.reports-home-styles[_ngcontent-%COMP%]   .bookmark-button[_ngcontent-%COMP%]{visibility:hidden}.reports-home-styles[_ngcontent-%COMP%]   .bookmark-button[_ngcontent-%COMP%], .reports-home-styles[_ngcontent-%COMP%]   .bookmark-button-marked[_ngcontent-%COMP%]{width:27px!important;height:27px!important;line-height:25px!important;position:absolute;right:0;top:-5px}.reports-home-styles[_ngcontent-%COMP%]   .tile-bookmark-icon[_ngcontent-%COMP%]{font-size:21px!important;color:#66615b!important}.reports-home-styles[_ngcontent-%COMP%]   .layer[_ngcontent-%COMP%]{height:100px;border-radius:50%;right:-5px;width:108%;bottom:-34px;position:absolute;background-color:rgba(207,0,1,.6705882352941176);visibility:hidden}.reports-home-styles[_ngcontent-%COMP%]   .layer[_ngcontent-%COMP%], .reports-home-styles[_ngcontent-%COMP%]   .tiles[_ngcontent-%COMP%]:hover{box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.reports-home-styles[_ngcontent-%COMP%]   .tiles[_ngcontent-%COMP%]:hover{cursor:pointer}.reports-home-styles[_ngcontent-%COMP%]   .tiles[_ngcontent-%COMP%]:hover   .tiles-title[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#fff}.reports-home-styles[_ngcontent-%COMP%]   .tiles[_ngcontent-%COMP%]:hover   .bookmark-button[_ngcontent-%COMP%]{visibility:visible;animation:slide-in-top-bookmark-icon .2s cubic-bezier(.25,.46,.45,.94) both}.reports-home-styles[_ngcontent-%COMP%]   .tiles[_ngcontent-%COMP%]:hover   .layer[_ngcontent-%COMP%]{visibility:visible;animation:slide-in-bottom .3s cubic-bezier(.25,.46,.45,.94) both}.reports-home-styles[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-20px);opacity:0}to{transform:translateY(0);opacity:1}}@keyframes slide-in-top-bookmark-icon{0%{transform:translateY(-7px);opacity:0}to{transform:translateY(0);opacity:1}}@keyframes slide-in-bottom{0%{transform:translateY(20px);opacity:0}to{transform:translateY(0);opacity:1}}"]}),e})(),X=(()=>{class e{constructor(e,t,a,n,o){this._auth=e,this._reportservice=t,this.router=a,this.activatedRoute=n,this.roleService=o}canActivate(e,t){return Object(h.c)(this,void 0,void 0,(function*(){if(yield this.checkEcReportAccess(e.data.id))return!0;this.router.navigateByUrl("/main/reports")}))}checkEcReportAccess(e){return!!this.roleService.isApplicationVisibleForUser(e)}}return e.\u0275fac=function(t){return new(t||e)(R["\u0275\u0275inject"](C.a),R["\u0275\u0275inject"](P.a),R["\u0275\u0275inject"](m.g),R["\u0275\u0275inject"](m.a),R["\u0275\u0275inject"](v.a))},e.\u0275prov=R["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();const J=[{path:"",component:Z},{path:"timesheet-stats",loadChildren:()=>Promise.all([a.e(4),a.e(680)]).then(a.bind(null,"ABNh")).then(e=>e.TimesheetStatsModule),data:{breadcrumb:"Payroll Statistics"}},{path:"sales-dashboard",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(4),a.e(5),a.e(6),a.e(9),a.e(10),a.e(11),a.e(12),a.e(15),a.e(23),a.e(30),a.e(50),a.e(54),a.e(0),a.e(870)]).then(a.bind(null,"4phY")).then(e=>e.SalesDashboardModule),data:{breadcrumb:"Sales Dashboard"}},{path:"hr-dashboard",loadChildren:()=>Promise.all([a.e(2),a.e(3),a.e(15),a.e(31),a.e(36),a.e(38),a.e(42),a.e(46),a.e(0),a.e(723)]).then(a.bind(null,"REeT")).then(e=>e.HrDashboardModule),canActivate:[X],data:{breadcrumb:"HR Dashboard",id:349}},{path:"timesheet-stats-new",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(4),a.e(5),a.e(6),a.e(9),a.e(10),a.e(11),a.e(12),a.e(13),a.e(14),a.e(16),a.e(17),a.e(18),a.e(679)]).then(a.bind(null,"JeY9")).then(e=>e.TimesheetStatsNewModule),data:{breadcrumb:"Timesheet Statistics"}},{path:"mis",loadChildren:()=>Promise.all([a.e(5),a.e(20),a.e(524)]).then(a.bind(null,"IUO2")).then(e=>e.MisReportModule),data:{breadcrumb:"MIS"}},{path:"management",loadChildren:()=>Promise.all([a.e(5),a.e(20),a.e(517)]).then(a.bind(null,"7MX6")).then(e=>e.ManagementReportModule),data:{breadcrumb:"Management"}},{path:"governance",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(4),a.e(5),a.e(6),a.e(7),a.e(9),a.e(10),a.e(11),a.e(12),a.e(13),a.e(14),a.e(15),a.e(16),a.e(17),a.e(18),a.e(21),a.e(60),a.e(145),a.e(0),a.e(467)]).then(a.bind(null,"suk2")).then(e=>e.GovernanceReportModule),data:{breadcrumb:"Governance"}},{path:"ar",loadChildren:()=>Promise.all([a.e(2),a.e(3),a.e(5),a.e(381)]).then(a.bind(null,"kOp7")).then(e=>e.ArReportModule),data:{breadcrumb:"AR"}},{path:"ar-ubr",loadChildren:()=>Promise.all([a.e(2),a.e(3),a.e(5),a.e(0),a.e(382)]).then(a.bind(null,"P+CQ")).then(e=>e.ArUbrModule),data:{breadcrumb:"AR-UBR Days"}},{path:"projectReport",loadChildren:()=>Promise.all([a.e(5),a.e(7),a.e(605)]).then(a.bind(null,"6Pkw")).then(e=>e.ProjectReportModule),data:{breadcrumb:"Project"}},{path:"projectGovernanceReport",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(4),a.e(5),a.e(6),a.e(7),a.e(9),a.e(10),a.e(11),a.e(12),a.e(13),a.e(14),a.e(15),a.e(16),a.e(17),a.e(18),a.e(23),a.e(30),a.e(29),a.e(34),a.e(162)]).then(a.bind(null,"L/uB")).then(e=>e.ProjectGovernanceReportModule),data:{breadcrumb:"Project Gov."}},{path:"practiceGov",loadChildren:()=>Promise.all([a.e(4),a.e(7),a.e(15),a.e(360)]).then(a.bind(null,"6dMV")).then(e=>e.ActivityGovernanceModule),data:{breadcrumb:"Practice Governance (LG)"}},{path:"practiceGovNew",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(4),a.e(5),a.e(6),a.e(7),a.e(9),a.e(10),a.e(11),a.e(12),a.e(13),a.e(14),a.e(15),a.e(16),a.e(17),a.e(18),a.e(20),a.e(139),a.e(140),a.e(361)]).then(a.bind(null,"PDL8")).then(e=>e.ActivityGovernanceNewModule),data:{breadcrumb:"Practice Governance"}},{path:"dfrReport",loadChildren:()=>Promise.all([a.e(5),a.e(7),a.e(431)]).then(a.bind(null,"HsKY")).then(e=>e.DfrReportModule),data:{breadcrumb:"DFR"}},{path:"obv",loadChildren:()=>Promise.all([a.e(2),a.e(3),a.e(5),a.e(7),a.e(15),a.e(533)]).then(a.bind(null,"xr2I")).then(e=>e.ObvReportModule),data:{breadcrumb:"OBV"}},{path:"timesheet-payroll",loadChildren:()=>Promise.all([a.e(5),a.e(7),a.e(15),a.e(547)]).then(a.bind(null,"96ZL")).then(e=>e.PayrollReportModule),data:{breadcrumb:"Payroll"}},{path:"sales",loadChildren:()=>Promise.all([a.e(5),a.e(7),a.e(647)]).then(a.bind(null,"v8fE")).then(e=>e.SalesReportModule),data:{breadcrumb:"Sales Report"}},{path:"leadsGovernanceReport",loadChildren:()=>Promise.all([a.e(4),a.e(7),a.e(13),a.e(14),a.e(15),a.e(507)]).then(a.bind(null,"f8UP")).then(e=>e.LeadGovernanceModule),data:{breadcrumb:"Leads Governance"}},{path:"marketingGovernance",loadChildren:()=>Promise.all([a.e(4),a.e(7),a.e(15),a.e(519)]).then(a.bind(null,"Bm4p")).then(e=>e.MarketingGovernanceModule),data:{breadcrumb:"Marketing Governance"}},{path:"salesGovernanceReportNew",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(4),a.e(5),a.e(6),a.e(7),a.e(9),a.e(10),a.e(11),a.e(12),a.e(13),a.e(14),a.e(15),a.e(16),a.e(17),a.e(18),a.e(23),a.e(30),a.e(29),a.e(34),a.e(168)]).then(a.bind(null,"jP7X")).then(e=>e.SalesGovernanceNewModule),data:{breadcrumb:"Opportunity Governance"}},{path:"salesGovernanceReport",loadChildren:()=>Promise.all([a.e(4),a.e(7),a.e(15),a.e(101),a.e(645)]).then(a.bind(null,"+WQJ")).then(e=>e.SalesGovernanceModule),data:{breadcrumb:"Sales Governance"}},{path:"opportunityGovernanceReport",loadChildren:()=>Promise.all([a.e(4),a.e(7),a.e(15),a.e(101),a.e(537)]).then(a.bind(null,"uvzL")).then(e=>e.OpportunityGovernanceModule),data:{breadcrumb:"Opportunity Governance (Legacy)"}},{path:"book_and_bill",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(4),a.e(5),a.e(6),a.e(9),a.e(10),a.e(11),a.e(12),a.e(13),a.e(14),a.e(16),a.e(17),a.e(18),a.e(50),a.e(397)]).then(a.bind(null,"FbuU")).then(e=>e.BookAndBillReportModule),data:{breadcrumb:"Book and Bill"}},{path:"bidManager",loadChildren:()=>Promise.all([a.e(4),a.e(7),a.e(101),a.e(393)]).then(a.bind(null,"n/L1")).then(e=>e.BidManagerModule),data:{breadcrumb:"Bid Manager"}},{path:"practice-mis",loadChildren:()=>Promise.all([a.e(5),a.e(7),a.e(108)]).then(a.bind(null,"3EOb")).then(e=>e.PracticeMisModule),data:{breadcrumb:"Practice MIS"}},{path:"wfh-report",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(4),a.e(5),a.e(6),a.e(9),a.e(10),a.e(11),a.e(12),a.e(13),a.e(14),a.e(16),a.e(17),a.e(18),a.e(173),a.e(712)]).then(a.bind(null,"Ggkt")).then(e=>e.WfhReportModule),data:{breadcrumb:"WFH Report"}},{path:"cta-report-new",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(4),a.e(5),a.e(6),a.e(9),a.e(10),a.e(11),a.e(12),a.e(13),a.e(14),a.e(16),a.e(17),a.e(18),a.e(82),a.e(416)]).then(a.bind(null,"VukI")).then(e=>e.CtaReportNewModule),data:{breadcrumb:"CTA Report"}},{path:"appraisal-report",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(4),a.e(5),a.e(6),a.e(9),a.e(10),a.e(11),a.e(12),a.e(13),a.e(14),a.e(16),a.e(17),a.e(18),a.e(0),a.e(375)]).then(a.bind(null,"lId6")).then(e=>e.AppraisalReportModule),data:{breadcrumb:"Appraisal Report"}},{path:"expense-report",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(4),a.e(5),a.e(6),a.e(9),a.e(10),a.e(11),a.e(12),a.e(13),a.e(14),a.e(16),a.e(17),a.e(18),a.e(26),a.e(458)]).then(a.bind(null,"kg6Q")).then(e=>e.ExpenseReportModule),data:{breadcrumb:"Expense Report"}},{path:"arubrwctrend-report",loadChildren:()=>Promise.all([a.e(36),a.e(38),a.e(42),a.e(46),a.e(383)]).then(a.bind(null,"2ItB")).then(e=>e.ArUbrWcTrendReportModule),data:{breadcrumb:"AR UBR WC Trend"}},{path:"sga-report",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(4),a.e(5),a.e(6),a.e(9),a.e(10),a.e(11),a.e(12),a.e(13),a.e(14),a.e(16),a.e(17),a.e(18),a.e(650)]).then(a.bind(null,"9ayS")).then(e=>e.SgaReportModule),data:{breadcrumb:"SG&A Report"}},{path:"plMisDifference-report",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(4),a.e(5),a.e(6),a.e(9),a.e(10),a.e(11),a.e(12),a.e(13),a.e(14),a.e(16),a.e(17),a.e(18),a.e(553)]).then(a.bind(null,"z19T")).then(e=>e.PlMisDifferenceReportModule),data:{breadcrumb:"P&L MIS Difference"}},{path:"ams-report",loadChildren:()=>Promise.all([a.e(5),a.e(7),a.e(15),a.e(369)]).then(a.bind(null,"/5M/")).then(e=>e.AmsReportModule),data:{breadcrumb:"AMS Report"}},{path:"ams-report-new",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(4),a.e(5),a.e(6),a.e(7),a.e(9),a.e(10),a.e(11),a.e(12),a.e(13),a.e(14),a.e(15),a.e(16),a.e(17),a.e(18),a.e(370)]).then(a.bind(null,"yxr/")).then(e=>e.AmsReportNewModule),data:{breadcrumb:"AMS Report (BETA)"}},{path:"tm-profitability-report",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(4),a.e(5),a.e(6),a.e(9),a.e(10),a.e(11),a.e(12),a.e(13),a.e(14),a.e(16),a.e(17),a.e(18),a.e(690)]).then(a.bind(null,"BH2e")).then(e=>e.TmProfitabilityReportModule),data:{breadcrumb:"T&M Profitability Report"}},{path:"isa-data-upload-report",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(4),a.e(5),a.e(6),a.e(9),a.e(10),a.e(11),a.e(12),a.e(13),a.e(14),a.e(16),a.e(17),a.e(18),a.e(499)]).then(a.bind(null,"qWGv")).then(e=>e.IsaUploadReportModule),data:{breadcrumb:"ISA Data Upload"}},{path:"winLossReport",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(4),a.e(5),a.e(6),a.e(9),a.e(10),a.e(11),a.e(12),a.e(13),a.e(14),a.e(16),a.e(17),a.e(18),a.e(23),a.e(30),a.e(713)]).then(a.bind(null,"uLHP")).then(e=>e.WinLossReportModule),data:{breadcrumb:"Win-Loss Report"}},{path:"book_and_bill_beta",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(4),a.e(5),a.e(6),a.e(9),a.e(10),a.e(11),a.e(12),a.e(13),a.e(14),a.e(16),a.e(17),a.e(18),a.e(50),a.e(396)]).then(a.bind(null,"FCHQ")).then(e=>e.BookAndBillReportBetaModule),data:{breadcrumb:"Book and Bill (BETA)"}},{path:"timesheet_testcase_report",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(4),a.e(5),a.e(6),a.e(9),a.e(10),a.e(11),a.e(12),a.e(13),a.e(14),a.e(16),a.e(17),a.e(18),a.e(682)]).then(a.bind(null,"0Ymc")).then(e=>e.TimesheetTestcaseReportModule),data:{breadcrumb:"TS Testcase Report"}},{path:"lmsUserCertificationsReport",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(4),a.e(5),a.e(6),a.e(9),a.e(10),a.e(11),a.e(12),a.e(13),a.e(14),a.e(16),a.e(17),a.e(18),a.e(152)]).then(a.bind(null,"5LLh")).then(e=>e.LmsUserCertReportModule),data:{breadcrumb:"Learning Report"}},{path:"awards-report",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(4),a.e(5),a.e(6),a.e(7),a.e(9),a.e(10),a.e(11),a.e(12),a.e(13),a.e(14),a.e(15),a.e(16),a.e(17),a.e(18),a.e(387)]).then(a.bind(null,"v7uh")).then(e=>e.AwardsReportModule),data:{breadcrumb:"Awards Report"}},{path:"contrary_book_bill",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(4),a.e(5),a.e(6),a.e(9),a.e(10),a.e(11),a.e(12),a.e(13),a.e(14),a.e(16),a.e(17),a.e(18),a.e(50),a.e(409)]).then(a.bind(null,"5vaL")).then(e=>e.ContraryBookBillModule),data:{breadcrumb:"Contrary Book and Bill"}},{path:"pmo-dashboard",loadChildren:()=>a.e(574).then(a.bind(null,"xIxa")).then(e=>e.PmoDashboardModule),data:{breadcrumb:"PMO Dashboard"}},{path:"pmo-dashboard-v1",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(5),a.e(6),a.e(9),a.e(10),a.e(11),a.e(12),a.e(13),a.e(14),a.e(16),a.e(17),a.e(0),a.e(575)]).then(a.bind(null,"Z9Ch")).then(e=>e.PmoDashboardVersion1Module),data:{breadcrumb:"Project Portfolio Dashboard"}},{path:"bugReport",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(4),a.e(5),a.e(6),a.e(9),a.e(10),a.e(11),a.e(12),a.e(13),a.e(14),a.e(16),a.e(17),a.e(18),a.e(398)]).then(a.bind(null,"weyz")).then(e=>e.BugReportModule),data:{breadcrumb:"Project Bug Report"}},{path:"taskReport",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(4),a.e(5),a.e(6),a.e(9),a.e(10),a.e(11),a.e(12),a.e(13),a.e(14),a.e(16),a.e(17),a.e(18),a.e(657)]).then(a.bind(null,"PPZ4")).then(e=>e.TaskReportModule),data:{breadcrumb:"Project Task Report"}},{path:"testCaseReport",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(4),a.e(5),a.e(6),a.e(9),a.e(10),a.e(11),a.e(12),a.e(13),a.e(14),a.e(16),a.e(17),a.e(18),a.e(660)]).then(a.bind(null,"cER/")).then(e=>e.TestCaseReportModule),data:{breadcrumb:"Project Test Case Report"}},{path:"pbiReport",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(4),a.e(5),a.e(6),a.e(9),a.e(10),a.e(11),a.e(12),a.e(13),a.e(14),a.e(16),a.e(17),a.e(18),a.e(548)]).then(a.bind(null,"W75G")).then(e=>e.PbiReportModule),data:{breadcrumb:"Project PBI Report"}},{path:"pgReport",loadChildren:()=>Promise.all([a.e(58),a.e(598)]).then(a.bind(null,"jZgl")).then(e=>e.ProjectGanttModule),data:{breadcrumb:"Project Gantt Report"}},{path:"onboarding-report",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(4),a.e(5),a.e(6),a.e(9),a.e(10),a.e(11),a.e(12),a.e(13),a.e(14),a.e(16),a.e(17),a.e(18),a.e(534)]).then(a.bind(null,"0Fdk")).then(e=>e.OnboardingReportModule),data:{breadcrumb:"Onboarding Report"}},{path:"income_statement",loadChildren:()=>Promise.all([a.e(5),a.e(7),a.e(656)]).then(a.bind(null,"pTSO")).then(e=>e.TallyGlReportModule),data:{breadcrumb:"Income Statement"}},{path:"management_budget",loadChildren:()=>Promise.all([a.e(5),a.e(516)]).then(a.bind(null,"Xukm")).then(e=>e.ManagementBudgetReportModule),data:{breadcrumb:"Management Budget Report"}},{path:"productBugReport",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(4),a.e(5),a.e(6),a.e(9),a.e(10),a.e(11),a.e(12),a.e(13),a.e(14),a.e(16),a.e(17),a.e(18),a.e(582)]).then(a.bind(null,"r4DG")).then(e=>e.ProductBugReportModule),data:{breadcrumb:"Product Bug Report"}},{path:"productTaskReport",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(4),a.e(5),a.e(6),a.e(9),a.e(10),a.e(11),a.e(12),a.e(13),a.e(14),a.e(16),a.e(17),a.e(18),a.e(586)]).then(a.bind(null,"IA/y")).then(e=>e.ProductTaskReportModule),data:{breadcrumb:"Product Task Report"}},{path:"mis_functions",loadChildren:()=>a.e(523).then(a.bind(null,"ErYW")).then(e=>e.MisFunctionsModule),data:{breadcrumb:"MIS Functions"}},{path:"lcdp-effort-report",loadChildren:()=>Promise.all([a.e(2),a.e(3),a.e(5),a.e(7),a.e(15),a.e(503)]).then(a.bind(null,"1dce")).then(e=>e.LcdpEffortReportModule),data:{breadcrumb:"Effort Report"}},{path:"productGanttReport",loadChildren:()=>Promise.all([a.e(58),a.e(585)]).then(a.bind(null,"qkzM")).then(e=>e.ProductGanttModule),data:{breadcrumb:"Product Gantt Report"}},{path:"goverance_projects",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(4),a.e(5),a.e(6),a.e(9),a.e(10),a.e(11),a.e(12),a.e(13),a.e(14),a.e(16),a.e(17),a.e(18),a.e(466)]).then(a.bind(null,"2F2A")).then(e=>e.GoveranceProjectModule),data:{breadcrumb:"Governance Project"}},{path:"pl-country-level-mis-report",loadChildren:()=>Promise.all([a.e(5),a.e(7),a.e(552)]).then(a.bind(null,"zenA")).then(e=>e.PlCountryLevelMisReportModule),data:{breadcrumb:"Country Level MIS"}},{path:"org-head-count-report",loadChildren:()=>Promise.all([a.e(5),a.e(7),a.e(539)]).then(a.bind(null,"5KqW")).then(e=>e.OrgHeadCountReportModule),data:{breadcrumb:"Org Head Count"}},{path:"sales-purchase-report",loadChildren:()=>a.e(646).then(a.bind(null,"KeMg")).then(e=>e.SalesPurchaseReportModule),data:{breadcrumb:"Sales & Purchase"}},{path:"salary-statement",loadChildren:()=>a.e(644).then(a.bind(null,"rgSI")).then(e=>e.SalaryStatementReportModule),data:{breadcrumb:"Salary Statement"}},{path:"rfidReport",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(4),a.e(5),a.e(6),a.e(9),a.e(10),a.e(11),a.e(12),a.e(13),a.e(14),a.e(16),a.e(17),a.e(18),a.e(630)]).then(a.bind(null,"sA6f")).then(e=>e.RfidReportModule),data:{breadcrumb:"RFID Data Report"}},{path:"rmg-governance",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(5),a.e(6),a.e(9),a.e(10),a.e(11),a.e(12),a.e(15),a.e(0),a.e(637)]).then(a.bind(null,"85KF")).then(e=>e.RmgGovernanceModule),data:{breadcrumb:"RMG Governance"}},{path:"productUserStoryReport",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(4),a.e(5),a.e(6),a.e(9),a.e(10),a.e(11),a.e(12),a.e(13),a.e(14),a.e(16),a.e(17),a.e(18),a.e(588)]).then(a.bind(null,"87ip")).then(e=>e.ProductUserStoryReportModule),data:{breadcrumb:"PBI Report"}},{path:"productVelocityReport",loadChildren:()=>Promise.all([a.e(2),a.e(3),a.e(589)]).then(a.bind(null,"rf3T")).then(e=>e.ProductVelocityModule),data:{breadcrumb:"Product Velocity"}},{path:"productBurndownReport",loadChildren:()=>Promise.all([a.e(0),a.e(583)]).then(a.bind(null,"yRj4")).then(e=>e.ProductBurndownLandingPageModule),data:{breadcrumb:"Product Burndown"}},{path:"contractTracker",loadChildren:()=>Promise.all([a.e(5),a.e(7),a.e(362)]).then(a.bind(null,"l/jG")).then(e=>e.AcvTrackerModule),data:{breadcrumb:"Contract Tracker"}},{path:"timesheetNotification",loadChildren:()=>a.e(676).then(a.bind(null,"QHCJ")).then(e=>e.TimesheetNotificationReportModule),data:{breadcrumb:"Timesheet Notification"}},{path:"rfidfiloReport",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(4),a.e(5),a.e(6),a.e(9),a.e(10),a.e(11),a.e(12),a.e(13),a.e(14),a.e(16),a.e(17),a.e(18),a.e(629)]).then(a.bind(null,"rwTK")).then(e=>e.RfidFifoReportModule),data:{breadcrumb:"RFID FILO Report"}},{path:"pmsAnnualCycle",loadChildren:()=>Promise.all([a.e(5),a.e(576)]).then(a.bind(null,"5OuD")).then(e=>e.PmsAnnualCycleReportModule),data:{breadcrumb:"PMS Annual Cycle Report"}},{path:"ec-report",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(5),a.e(6),a.e(9),a.e(10),a.e(11),a.e(12),a.e(13),a.e(14),a.e(16),a.e(17),a.e(32),a.e(434)]).then(a.bind(null,"2DLu")).then(e=>e.E360ReportModule),canActivate:[X],data:{breadcrumb:"EC Report",id:406}},{path:"projectdashboard",loadChildren:()=>Promise.all([a.e(2),a.e(3),a.e(0),a.e(594)]).then(a.bind(null,"UjKv")).then(e=>e.ProjectDashboardModule),data:{breadcrumb:"Project Dashboard"}},{path:"timesheetprojectreport",loadChildren:()=>Promise.all([a.e(13),a.e(0),a.e(677)]).then(a.bind(null,"Wnn1")).then(e=>e.TimesheetProjectReportModule),data:{breadcrumb:"Product Timesheet Report"}},{path:"productTestCaseReport",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(4),a.e(5),a.e(6),a.e(9),a.e(10),a.e(11),a.e(12),a.e(13),a.e(14),a.e(16),a.e(17),a.e(18),a.e(587)]).then(a.bind(null,"bRUK")).then(e=>e.ProductTestCaseModule),data:{breadcrumb:"Product Test Case Report"}},{path:"employee-Leave-report",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(5),a.e(6),a.e(9),a.e(10),a.e(11),a.e(12),a.e(13),a.e(14),a.e(16),a.e(17),a.e(32),a.e(445)]).then(a.bind(null,"0GR8")).then(e=>e.EmployeeLeaveReportModule),data:{breadcrumb:"Employee Leave Report"}},{path:"tds-report",loadChildren:()=>a.e(659).then(a.bind(null,"2Ag0")).then(e=>e.TdsReportModule),data:{breadcrumb:"Tds Report"}},{path:"integration-exception-report",loadChildren:()=>a.e(488).then(a.bind(null,"YtOm")).then(e=>e.IntegrationExceptionReportModule),data:{breadcrumb:"Integration Exception Report"}},{path:"integration-check-report",loadChildren:()=>a.e(487).then(a.bind(null,"DggX")).then(e=>e.IntegrationCheckReportModule),data:{breadcrumb:"Integration Check Report"}},{path:"people-allocation-dashboard",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(5),a.e(6),a.e(9),a.e(10),a.e(11),a.e(12),a.e(13),a.e(14),a.e(16),a.e(17),a.e(44),a.e(632)]).then(a.bind(null,"a3lr")).then(e=>e.RmDashboardModule),data:{breadcrumb:"People Allocation Dashboard"}},{path:"azure-logs-report",loadChildren:()=>a.e(388).then(a.bind(null,"wGUl")).then(e=>e.AzureIntegrationLogsReportModule),data:{breadcrumb:"Azure Integration logs Report"}},{path:"notification-logs-report",loadChildren:()=>a.e(531).then(a.bind(null,"H5Nj")).then(e=>e.NotificationLogsReportModule),data:{breadcrumb:"Notification Logs Report"}},{path:"error-logging-report",loadChildren:()=>a.e(450).then(a.bind(null,"PZ5v")).then(e=>e.ErrorLoggingReportModule),data:{breadcrumb:"Integration Check Report"}},{path:"internal-stakeholders-report",loadChildren:()=>Promise.all([a.e(7),a.e(13),a.e(14),a.e(35),a.e(41),a.e(51),a.e(150),a.e(492)]).then(a.bind(null,"Vnh8")).then(e=>e.InternalStakeholdersReportModule),data:{breadcrumb:"Project ISA Report"}},{path:"ubr-report",loadChildren:()=>a.e(701).then(a.bind(null,"c7Kn")).then(e=>e.UbrReportModule),data:{breadcrumb:"Ubr Report"}},{path:"timesheet-dashboard",loadChildren:()=>Promise.all([a.e(2),a.e(3),a.e(15),a.e(31),a.e(36),a.e(38),a.e(42),a.e(46),a.e(106),a.e(149),a.e(0),a.e(674)]).then(a.bind(null,"1Yg0")).then(e=>e.TimesheetDashboardModule),data:{breadcrumb:"Timesheet Dashboard"}},{path:"timesheet-v2-report",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(4),a.e(5),a.e(6),a.e(9),a.e(10),a.e(11),a.e(12),a.e(13),a.e(14),a.e(16),a.e(17),a.e(18),a.e(688)]).then(a.bind(null,"bPut")).then(e=>e.TimesheetV2ReportModule),data:{breadcrumb:"Timesheet V2 Report"}},{path:"employee-Leave-report-Sub-Division",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(5),a.e(6),a.e(9),a.e(10),a.e(11),a.e(12),a.e(13),a.e(14),a.e(16),a.e(17),a.e(32),a.e(446)]).then(a.bind(null,"XKm+")).then(e=>e.EmployeeLeaveReportSubdivisionModule),data:{breadcrumb:"Employee Leave Report Sub Division"}},{path:"people-allocation-gantt",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(4),a.e(5),a.e(6),a.e(9),a.e(10),a.e(11),a.e(12),a.e(13),a.e(14),a.e(16),a.e(17),a.e(18),a.e(32),a.e(89),a.e(112),a.e(167),a.e(0),a.e(636)]).then(a.bind(null,"YVar")).then(e=>e.RmgGanttModule),data:{breadcrumb:"People Allocation Gantt"}},{path:"projectTrackerReport",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(4),a.e(5),a.e(6),a.e(9),a.e(10),a.e(11),a.e(12),a.e(13),a.e(14),a.e(16),a.e(17),a.e(18),a.e(35),a.e(41),a.e(51),a.e(55),a.e(74),a.e(542)]).then(a.bind(null,"d9LO")).then(e=>e.OverallProjectReportModule),data:{breadcrumb:"Project Tracker"}},{path:"timesheet-v2-stats-report",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(7),a.e(8),a.e(15),a.e(22),a.e(24),a.e(27),a.e(28),a.e(0),a.e(689)]).then(a.bind(null,"kZtD")).then(e=>e.TimesheetV2StatsReportModule),data:{breadcrumb:"Timesheet Stats Report"}},{path:"ts-query-report",loadChildren:()=>a.e(678).then(a.bind(null,"PZeg")).then(e=>e.TimesheetQueryReportModule),data:{breadcrumb:"Timesheet Query Report"}},{path:"findash",loadChildren:()=>Promise.all([a.e(2),a.e(3),a.e(15),a.e(19),a.e(106),a.e(149),a.e(0),a.e(464)]).then(a.bind(null,"nfos")).then(e=>e.FinanceDashboardModule),data:{breadcrumb:"Finance Dashboard"}},{path:"ar-history-report",loadChildren:()=>a.e(380).then(a.bind(null,"IKh5")).then(e=>e.ArHistoryReportModule),data:{breadcrumb:"AR History Report"}},{path:"productDashboard",loadChildren:()=>Promise.all([a.e(2),a.e(3),a.e(0),a.e(584)]).then(a.bind(null,"MMEQ")).then(e=>e.ProductDashboardModule),data:{breadcrumb:"Product Dashboard"}},{path:"ap-transaction-report",loadChildren:()=>a.e(373).then(a.bind(null,"OQZj")).then(e=>e.APTransactionReportModule),data:{breadcrumb:"AP Transaction Report"}},{path:"p-and-l-report",loadChildren:()=>a.e(544).then(a.bind(null,"llUc")).then(e=>e.PAndLReportModule),data:{breadcrumb:"Profit and Loss Report (Schedule III)"}},{path:"balance-sheet-report",loadChildren:()=>a.e(390).then(a.bind(null,"Jgqp")).then(e=>e.BalanceSheetReportModule),data:{breadcrumb:"Balance Sheet Report (Schedule III)"}},{path:"cashflow-report",loadChildren:()=>a.e(400).then(a.bind(null,"yIlJ")).then(e=>e.CashflowReportModule),data:{breadcrumb:"Cashflow Report (Schedule III)"}},{path:"leave-error-report",loadChildren:()=>a.e(511).then(a.bind(null,"M0JQ")).then(e=>e.LeaveErrorReportModule),data:{breadcrumb:"Leave Error Report"}},{path:"finance-report",loadChildren:()=>Promise.all([a.e(2),a.e(0),a.e(465)]).then(a.bind(null,"sT3w")).then(e=>e.FinanceReportModule),data:{breadcrumb:"Finance Report"}},{path:"defect-report",loadChildren:()=>Promise.all([a.e(5),a.e(7),a.e(15),a.e(0),a.e(421)]).then(a.bind(null,"Y+zS")).then(e=>e.DefectReportModule),data:{breadcrumb:"Defect Report"}},{path:"expenseItemReport",loadChildren:()=>a.e(457).then(a.bind(null,"yP0P")).then(e=>e.ExpenseItemReportModule),data:{breadcrumb:"Expense Item Report"}},{path:"timesheet-daily-log-report-table-view",loadChildren:()=>Promise.all([a.e(8),a.e(686)]).then(a.bind(null,"w6wj")).then(e=>e.TimesheetV2DailyLogReportTableViewModule),data:{breadcrumb:"Timesheet Daily Log Report - Table View"}},{path:"attrition-dashboard",loadChildren:()=>Promise.all([a.e(2),a.e(3),a.e(15),a.e(354)]).then(a.bind(null,"iqOT")).then(e=>e.ExitDashboardModule),canActivate:[X],data:{breadcrumb:"Attrition Dashboard",id:962}},{path:"compliance-dashboard",loadChildren:()=>Promise.all([a.e(7),a.e(15),a.e(61),a.e(0),a.e(405)]).then(a.bind(null,"FMWK")).then(e=>e.ComplianceDashboardModule),data:{breadcrumb:"Compliance Dashboard"}},{path:"rr-projections",loadChildren:()=>Promise.all([a.e(7),a.e(642)]).then(a.bind(null,"Ejrh")).then(e=>e.RrProjectionsReportModule),data:{breadcrumb:"RR Projections"}},{path:"practice-mis-report",loadChildren:()=>Promise.all([a.e(2),a.e(3),a.e(5),a.e(6),a.e(7),a.e(15),a.e(579)]).then(a.bind(null,"klUe")).then(e=>e.PracticeMisReportModule),data:{breadcrumb:"Sub Division MIS"}},{path:"timesheet-export-report",loadChildren:()=>a.e(675).then(a.bind(null,"ELzc")).then(e=>e.TimesheetExportReportModule),data:{breadcrumb:"Timesheet Export Report"}},{path:"project-time-tracker",loadChildren:()=>Promise.all([a.e(7),a.e(23),a.e(35),a.e(41),a.e(51),a.e(607)]).then(a.bind(null,"+xci")).then(e=>e.ProjectTimeTrackerModule),data:{breadcrumb:"Project Time Tracker"}},{path:"project-header-details",loadChildren:()=>Promise.all([a.e(1),a.e(7),a.e(8),a.e(22),a.e(23),a.e(565)]).then(a.bind(null,"+Q8a")).then(e=>e.PmHeaderDetailsReportModule),data:{breadcrumb:"Project Header Details Report"}},{path:"project-header",loadChildren:()=>Promise.all([a.e(1),a.e(7),a.e(8),a.e(22),a.e(23),a.e(564)]).then(a.bind(null,"9JYb")).then(e=>e.PmHeaderDetailDownloadModule),canActivate:[X],data:{breadcrumb:"Project Header",id:1030}},{path:"project-employee-details",loadChildren:()=>Promise.all([a.e(1),a.e(7),a.e(8),a.e(22),a.e(23),a.e(560)]).then(a.bind(null,"qvGM")).then(e=>e.PmEmployeeDetailsReportModule),data:{breadcrumb:"Project Employee Details Report"}},{path:"employee-details",loadChildren:()=>Promise.all([a.e(1),a.e(7),a.e(8),a.e(22),a.e(23),a.e(561)]).then(a.bind(null,"w1g7")).then(e=>e.PmEmployeeSummaryDetailsReportModule),data:{breadcrumb:"Employee Summary Details Report"}},{path:"activity-dashboard",loadChildren:()=>Promise.all([a.e(2),a.e(3),a.e(7),a.e(15),a.e(0),a.e(359)]).then(a.bind(null,"DGVX")).then(e=>e.ActivityDashboardModule),data:{breadcrumb:"Activity Dashboard"}},{path:"timesheet-urs-report",loadChildren:()=>Promise.all([a.e(8),a.e(685)]).then(a.bind(null,"ttKn")).then(e=>e.TimesheetUrsReportModule),data:{breadcrumb:"Timesheet URS Report"}},{path:"people-allocation-status-report",loadChildren:()=>Promise.all([a.e(1),a.e(6),a.e(7),a.e(8),a.e(22),a.e(638)]).then(a.bind(null,"Ew/i")).then(e=>e.RmgSoftBookingReportModule),data:{breadcrumb:"People Allocation Request Status Report"}},{path:"sow-dashboard",loadChildren:()=>Promise.all([a.e(2),a.e(3),a.e(6),a.e(7),a.e(8),a.e(15),a.e(19),a.e(654)]).then(a.bind(null,"gol0")).then(e=>e.SowDashboardModule),canActivate:[X],data:{breadcrumb:"Project Efficiency Dashboard",id:1028}},{path:"p2p-logs-report",loadChildren:()=>a.e(545).then(a.bind(null,"7a+K")).then(e=>e.P2pLogsReportModule),data:{breadcrumb:"P2P Logs"}},{path:"rr-reports",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(5),a.e(6),a.e(9),a.e(10),a.e(11),a.e(12),a.e(643)]).then(a.bind(null,"scVR")).then(e=>e.RrReportsRoutingModule),data:{breadcrumb:"RR Report"}},{path:"timesheet-daily-log-report",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(5),a.e(6),a.e(9),a.e(10),a.e(11),a.e(12),a.e(15),a.e(19),a.e(687)]).then(a.bind(null,"UJ6N")).then(e=>e.TimesheetV2DailyLogReportModule),data:{breadcrumb:"Timesheet Daily Log Report - Calendar View"}},{path:"rr-projections-position",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(5),a.e(6),a.e(9),a.e(10),a.e(11),a.e(12),a.e(19),a.e(641)]).then(a.bind(null,"fyTr")).then(e=>e.RrProjectionsPositionReportModule),data:{breadcrumb:"Projection Report"}},{path:"quote-monthly-projection",loadChildren:()=>Promise.all([a.e(5),a.e(7),a.e(15),a.e(614)]).then(a.bind(null,"B7gI")).then(e=>e.QuoteMonthlyProjectionModule),data:{breadcrumb:"Quote Monthly Projection"}},{path:"sales-report",loadChildren:()=>Promise.all([a.e(5),a.e(7),a.e(648)]).then(a.bind(null,"b1E3")).then(e=>e.SalesReportVersion2Module),data:{breadcrumb:"Sales Report V2"}},{path:"schedule3-admin-functions",loadChildren:()=>Promise.all([a.e(6),a.e(146),a.e(649)]).then(a.bind(null,"tGI2")).then(e=>e.Schedule3AdminFunctionsModule),data:{breadcrumb:"Schedule III Admin Functions"}},{path:"project-milestone-report",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(7),a.e(8),a.e(15),a.e(22),a.e(24),a.e(27),a.e(28),a.e(0),a.e(609)]).then(a.bind(null,"UuJN")).then(e=>e.ProjectsV2Module),data:{breadcrumb:"Project Milestone Report"}},{path:"project-header-report-details",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(7),a.e(8),a.e(15),a.e(22),a.e(24),a.e(27),a.e(28),a.e(0),a.e(600)]).then(a.bind(null,"+wqg")).then(e=>e.ProjectHeaderModule),data:{breadcrumb:"Project Header Report"}},{path:"ils-zb-exception-report",loadChildren:()=>Promise.all([a.e(8),a.e(20),a.e(25),a.e(714)]).then(a.bind(null,"FASo")).then(e=>e.ZbIlsLedgersExceptionReportModule),data:{breadcrumb:"ZohoBooks GL Exception Report"}},{path:"ils-admin-reports",loadChildren:()=>Promise.all([a.e(8),a.e(20),a.e(25),a.e(471)]).then(a.bind(null,"CtMD")).then(e=>e.IlsAdminReportsModule),data:{breadcrumb:"ILS Admin Reports"}},{path:"zb-reports-sync-logs",loadChildren:()=>Promise.all([a.e(8),a.e(20),a.e(25),a.e(716)]).then(a.bind(null,"xbHw")).then(e=>e.ZohobooksReportsSyncLogModule),data:{breadcrumb:"ZohoBooks Sync Log"}},{path:"zb-exception-report-logs",loadChildren:()=>Promise.all([a.e(8),a.e(20),a.e(25),a.e(715)]).then(a.bind(null,"XVUi")).then(e=>e.ZohobooksGlExceptionReportLogModule),data:{breadcrumb:"ZohoBooks GL Exception Report Log"}},{path:"report-dashboard",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(6),a.e(7),a.e(8),a.e(15),a.e(19),a.e(22),a.e(23),a.e(24),a.e(160),a.e(615)]).then(a.bind(null,"7h26")).then(e=>e.ReportDashboardModule),canActivate:[X],data:{breadcrumb:"Utilization Report",reportId:1,id:1070}},{path:"revenue-report",loadChildren:()=>Promise.all([a.e(5),a.e(7),a.e(626)]).then(a.bind(null,"3wdk")).then(e=>e.RevenueActualsModule),data:{breadcrumb:"Revenue Report"}},{path:"ubr-posting",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(4),a.e(5),a.e(6),a.e(9),a.e(10),a.e(11),a.e(12),a.e(18),a.e(699)]).then(a.bind(null,"tltV")).then(e=>e.UbrPostingReportModule),data:{breadcrumb:"UBR Posting Report"}},{path:"ubr-report-new",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(4),a.e(5),a.e(6),a.e(9),a.e(10),a.e(11),a.e(12),a.e(18),a.e(700)]).then(a.bind(null,"UKro")).then(e=>e.UbrReportNewModule),data:{breadcrumb:"UBR Report (New)"}},{path:"revenue-forecast",loadChildren:()=>Promise.all([a.e(5),a.e(7),a.e(15),a.e(628)]).then(a.bind(null,"wMdd")).then(e=>e.RevenueReportV2Module),data:{breadcrumb:"Revenue Forecast"}},{path:"intercompany-reports",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(5),a.e(6),a.e(9),a.e(10),a.e(11),a.e(12),a.e(491)]).then(a.bind(null,"IlGN")).then(e=>e.IntercompanyReportsModule),data:{breadcrumb:"Intercompany reports"}},{path:"billing-tracker-report",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(6),a.e(7),a.e(8),a.e(15),a.e(19),a.e(22),a.e(23),a.e(24),a.e(160),a.e(615)]).then(a.bind(null,"7h26")).then(e=>e.ReportDashboardModule),canActivate:[X],data:{breadcrumb:"Billing Tracker Report",reportId:2,id:3002}},{path:"greyt-leave-report",loadChildren:()=>Promise.all([a.e(8),a.e(468)]).then(a.bind(null,"0QUp")).then(e=>e.GreytLeaveIntegrationModule),data:{breadcrumb:"GreyT Leave Integration"}},{path:"qb-reports-sync-logs",loadChildren:()=>Promise.all([a.e(8),a.e(20),a.e(25),a.e(613)]).then(a.bind(null,"oarY")).then(e=>e.QuickbooksReportsSyncLogModule),data:{breadcrumb:"QuickBooks Sync Log"}},{path:"ils-qb-exception-report",loadChildren:()=>Promise.all([a.e(8),a.e(20),a.e(25),a.e(611)]).then(a.bind(null,"3Iqi")).then(e=>e.QbIlsLedgersExceptionReportModule),data:{breadcrumb:"QuickBooks GL Exception Report"}},{path:"qb-exception-report-logs",loadChildren:()=>Promise.all([a.e(8),a.e(20),a.e(25),a.e(612)]).then(a.bind(null,"rVMF")).then(e=>e.QuickbooksGlExceptionReportLogModule),data:{breadcrumb:"QuickBooks GL Exception Report Log"}},{path:"mis-cockpit",loadChildren:()=>a.e(522).then(a.bind(null,"ded1")).then(e=>e.MisCockpitModule),data:{breadcrumb:"MIS Cockpit"}},{path:"employee-level-ap-report",loadChildren:()=>a.e(449).then(a.bind(null,"zFqt")).then(e=>e.EmployeeLevelApReportModule),data:{breadcrumb:"Employee level AP report"}},{path:"ar-aging",loadChildren:()=>a.e(379).then(a.bind(null,"A6o4")).then(e=>e.ArAgingReportModule),canActivate:[X],data:{breadcrumb:"AR Aging Report",id:3021}},{path:"invoice-details",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(7),a.e(8),a.e(15),a.e(22),a.e(24),a.e(27),a.e(28),a.e(0),a.e(493)]).then(a.bind(null,"61d6")).then(e=>e.InvoiceDetailsReportModule),data:{breadcrumb:"Invoice Details Report"}},{path:"ar-aging-pivot",loadChildren:()=>Promise.all([a.e(5),a.e(7),a.e(15),a.e(144),a.e(378)]).then(a.bind(null,"P2We")).then(e=>e.ArAgingPivotModule),canActivate:[X],data:{breadcrumb:"AR Aging Report",id:3021}},{path:"employee-allocation-status-report",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(7),a.e(8),a.e(15),a.e(22),a.e(24),a.e(27),a.e(28),a.e(0),a.e(549)]).then(a.bind(null,"N1O8")).then(e=>e.PeopleReportModule),data:{breadcrumb:"Allocation Status Report"}},{path:"soft-booking-employee-report",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(7),a.e(8),a.e(15),a.e(22),a.e(24),a.e(27),a.e(28),a.e(0),a.e(653)]).then(a.bind(null,"kxJp")).then(e=>e.SoftBookedEmployeeReportModule),data:{breadcrumb:"Employee Soft Booking Report"}},{path:"project-billing-plan",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(7),a.e(8),a.e(15),a.e(22),a.e(24),a.e(27),a.e(28),a.e(0),a.e(591)]).then(a.bind(null,"8YIl")).then(e=>e.ProjectBillingReportModule),data:{breadcrumb:"Project Billing Plan Report"}},{path:"ils-ad-ed-exception-report",loadChildren:()=>Promise.all([a.e(8),a.e(20),a.e(25),a.e(486)]).then(a.bind(null,"l1ra")).then(e=>e.IntegrationAdEdExceptionReportModule),data:{breadcrumb:"AD-ED Exception Report"}},{path:"ils-ad-ed-exception-report-logs",loadChildren:()=>Promise.all([a.e(8),a.e(20),a.e(25),a.e(470)]).then(a.bind(null,"qp4+")).then(e=>e.IlsAdEdExceptionReportLogsModule),data:{breadcrumb:"AD-ED Exception Report Logs"}},{path:"people-allocation-dashboard-v2",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(7),a.e(8),a.e(15),a.e(20),a.e(22),a.e(24),a.e(25),a.e(608)]).then(a.bind(null,"um4W")).then(e=>e.ProjectV2DashboardModule),data:{breadcrumb:"People Allocation Dashboard",reportId:4003}},{path:"revenue-movement",loadChildren:()=>Promise.all([a.e(9),a.e(627)]).then(a.bind(null,"+1OV")).then(e=>e.RevenueMovementModule),data:{breadcrumb:"Revenue Movement Report"}},{path:"integration-trace-report",loadChildren:()=>Promise.all([a.e(8),a.e(20),a.e(25),a.e(489)]).then(a.bind(null,"MjSy")).then(e=>e.IntegrationTraceLogReportModule),data:{breadcrumb:"Integration Trace Logs"}},{path:"ap-aging",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(7),a.e(8),a.e(15),a.e(22),a.e(24),a.e(27),a.e(28),a.e(0),a.e(372)]).then(a.bind(null,"RZXM")).then(e=>e.ApAgingReportModule),data:{breadcrumb:"AP Aging Report"}},{path:"country-level-mis-pivot",loadChildren:()=>Promise.all([a.e(5),a.e(7),a.e(15),a.e(411)]).then(a.bind(null,"tGLg")).then(e=>e.CountryLevelMisPivotFrameworkModule),data:{breadcrumb:"Country Level MIS"}},{path:"claim-summary-report",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(7),a.e(8),a.e(15),a.e(22),a.e(24),a.e(27),a.e(28),a.e(0),a.e(403)]).then(a.bind(null,"sc7y")).then(e=>e.ClaimSummaryReportModule),data:{breadcrumb:"Claim Summary Report"}},{path:"claim-details-report",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(7),a.e(8),a.e(15),a.e(22),a.e(24),a.e(27),a.e(28),a.e(0),a.e(402)]).then(a.bind(null,"pv+j")).then(e=>e.ClaimDetailsReportModule),data:{breadcrumb:"Claim Details Report"}},{path:"project-allocation-report",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(7),a.e(8),a.e(15),a.e(22),a.e(24),a.e(27),a.e(28),a.e(0),a.e(590)]).then(a.bind(null,"kWux")).then(e=>e.ProjectAllocationReportModule),data:{breadcrumb:"Project Allocation Report"}},{path:"resource-demand-report",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(7),a.e(8),a.e(15),a.e(22),a.e(24),a.e(27),a.e(28),a.e(0),a.e(422)]).then(a.bind(null,"Ha/I")).then(e=>e.DemandRequestModule),data:{breadcrumb:"Resource Demand Report"}},{path:"invoice-template",loadChildren:()=>Promise.all([a.e(6),a.e(9),a.e(19),a.e(497)]).then(a.bind(null,"jLnG")).then(e=>e.InvoiceTemplatePortalModule),data:{breadcrumb:"Invoice Template Portal"}},{path:"unassigned-aging-report",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(7),a.e(8),a.e(15),a.e(22),a.e(24),a.e(27),a.e(28),a.e(0),a.e(702)]).then(a.bind(null,"Albd")).then(e=>e.UnassignedAgingReportModule),data:{breadcrumb:"Bench Aging Report"}},{path:"project-revenue-report",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(7),a.e(8),a.e(15),a.e(22),a.e(24),a.e(27),a.e(28),a.e(0),a.e(606)]).then(a.bind(null,"hpB9")).then(e=>e.ProjectRevenueReportModule),data:{breadcrumb:"Project Revenue Report"}},{path:"employee-capacity-report",loadChildren:()=>Promise.all([a.e(1),a.e(7),a.e(8),a.e(15),a.e(19),a.e(22),a.e(24),a.e(0),a.e(441)]).then(a.bind(null,"JRKT")).then(e=>e.EmployeeCapacityReportModule),canActivate:[X],data:{breadcrumb:"Employee Capacity Report",id:8222}},{path:"mis-custom-report",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(7),a.e(8),a.e(15),a.e(22),a.e(24),a.e(27),a.e(28),a.e(0),a.e(693)]).then(a.bind(null,"r0uE")).then(e=>e.TsMisCustomReportModule),data:{breadcrumb:"MIS Custom Report"}},{path:"custom-financial-report",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(7),a.e(8),a.e(15),a.e(22),a.e(24),a.e(27),a.e(28),a.e(0),a.e(419)]).then(a.bind(null,"NN+I")).then(e=>e.CustomFinancialReportModule),data:{breadcrumb:"Zifo Allocation Custom Report - 1"}},{path:"project-demobilization-report",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(7),a.e(8),a.e(15),a.e(22),a.e(24),a.e(27),a.e(28),a.e(0),a.e(595)]).then(a.bind(null,"lued")).then(e=>e.ProjectDemobilizationReportModule),data:{breadcrumb:"Project Demobilization Report"}},{path:"project-milestone-report-v2",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(7),a.e(8),a.e(15),a.e(22),a.e(24),a.e(27),a.e(28),a.e(0),a.e(521)]).then(a.bind(null,"2l3l")).then(e=>e.MilestoneTrackerReportModule),data:{breadcrumb:"Project Milestone Report"}},{path:"project-header-report-details-v2",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(7),a.e(8),a.e(15),a.e(22),a.e(24),a.e(27),a.e(28),a.e(0),a.e(601)]).then(a.bind(null,"pEeA")).then(e=>e.ProjectHeaderReportV2Module),data:{breadcrumb:"Project Header Report"}},{path:"employee-activity-report",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(7),a.e(8),a.e(15),a.e(22),a.e(24),a.e(27),a.e(28),a.e(0),a.e(440)]).then(a.bind(null,"EbK/")).then(e=>e.EmployeeActivityReportModule),data:{breadcrumb:"Employee Activity Report"}},{path:"billing-advice-report",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(7),a.e(8),a.e(15),a.e(22),a.e(24),a.e(27),a.e(28),a.e(0),a.e(395)]).then(a.bind(null,"GlVj")).then(e=>e.BillingAdviceModule),data:{breadcrumb:"Billing Advice Report - T&M/FC"}},{path:"employee-pop-up",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(7),a.e(8),a.e(15),a.e(22),a.e(24),a.e(27),a.e(28),a.e(0),a.e(438)]).then(a.bind(null,"swz1")).then(e=>e.EdEventWishesReportModule),data:{breadcrumb:"Employee Pop Up Report"}},{path:"allocation-efficiency-report",loadChildren:()=>Promise.all([a.e(1),a.e(7),a.e(8),a.e(15),a.e(19),a.e(22),a.e(0),a.e(597)]).then(a.bind(null,"G9Mx")).then(e=>e.ProjectEfficiencyReportV2Module),data:{breadcrumb:"Allocation Efficiency Report"}},{path:"rfid-attendance-report",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(7),a.e(8),a.e(15),a.e(22),a.e(24),a.e(27),a.e(28),a.e(0),a.e(672)]).then(a.bind(null,"AAPp")).then(e=>e.TimesheetAttendanceReportModule),data:{breadcrumb:"RFID Attendance Report"}},{path:"leave-report",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(7),a.e(8),a.e(15),a.e(22),a.e(24),a.e(27),a.e(28),a.e(0),a.e(512)]).then(a.bind(null,"GNPt")).then(e=>e.LeaveReportModule),data:{breadcrumb:"Leave Report"}},{path:"ubr-aging-report",loadChildren:()=>Promise.all([a.e(5),a.e(7),a.e(15),a.e(144),a.e(698)]).then(a.bind(null,"96WA")).then(e=>e.UbrAgingReportModule),data:{breadcrumb:"UBR Aging Report"}},{path:"notifications-report",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(7),a.e(8),a.e(15),a.e(22),a.e(24),a.e(27),a.e(28),a.e(0),a.e(532)]).then(a.bind(null,"Yhcx")).then(e=>e.NotificationsReportsModule),data:{breadcrumb:"Notifications Report"}},{path:"role-access-control-report",loadChildren:()=>Promise.all([a.e(8),a.e(0),a.e(639)]).then(a.bind(null,"H/eT")).then(e=>e.RoleAndAccessControlReportModule),data:{breadcrumb:"Role & Access Control Report"}}];let ee=(()=>{class e{}return e.\u0275mod=R["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=R["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[m.k.forChild(J)],m.k]}),e})();var te=a("6ADm"),ae=a("3beV"),ne=a("7pIB"),oe=a("pA3K"),re=a("pzj6"),ie=a("ZzPI"),se=a("w76M");let le=(()=>{class e{}return e.\u0275mod=R["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=R["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[n.CommonModule,ee,d.b,o.e,r.e,l.c,s.b,i.b,p.p,p.E,te.a,x.b,ae.a,ne.c,oe.a,re.b,ie.b,se.b]]}),e})()},jr6c:function(e,t,a){"use strict";a.d(t,"a",(function(){return s}));var n=a("mrSG"),o=a("2Vo4"),r=a("fXoL"),i=a("tk/3");let s=(()=>{class e{constructor(e){this.$http=e,this.initialQuoteDetail$=new o.a({quoteName:"",currency:"",deliveryStartDate:null,deliveryEndDate:null,businessType:null,serviceTypeId:null,quoteType:""}),this.intialQuoteDetail=this.initialQuoteDetail$.asObservable(),this.getCurrentQuoteConfig=(e,t)=>{for(const a of e)if(a.quote_config_name===t)return a;return null},this.getOpportunityDocumentTypes=()=>Object(n.c)(this,void 0,void 0,(function*(){try{return yield this.$http.post("/api/opportunity/getOpportunityDocumentTypes",{}).toPromise()}catch(e){throw console.error("Error retrieving document types:",e),e}})),this.formActionSubject=new o.a(this.getStoredFormAction()),this.formAction$=this.formActionSubject.asObservable()}setInitalQuoteDetail(e){this.initialQuoteDetail$.next(e)}getNationalityList(){return this.$http.post("/api/qb/master/nationalityList",{})}getWorkLocation(){return this.$http.post("/api/qb/master/workLocation",{})}getPositionList(){return this.$http.post("/api/qb/master/positionList",{})}getSkillList(){return this.$http.post("/api/qb/master/skillList",{})}getSkillExperience(){return this.$http.post("/api/qb/master/skillExperience",{})}getWorkExperience(){return this.$http.post("/api/qb/master/workExperience",{})}getUOM(){return this.$http.post("/api/qb/master/getUOM",{})}getCustomPercentage(){return this.$http.post("/api/qb/master/getCustomPercentage",{})}getTaxDetails(){return this.$http.post("/api/qb/master/getTaxDetails",{})}getDiscountDetails(){return this.$http.post("/api/qb/master/getDiscountDetails",{})}getCurrencyList(){return this.$http.post("/api/qb/master/getCurrencyList",{})}getServiceList(e,t=[],a,n,o){return this.$http.post("/api/qb/serviceConfig/getServiceList",{opportunityId:e,mandatoryFields:t,defaultCurrency:a,quoteCurrency:n,conversionTypeId:o})}getDivision(){return this.$http.post("/api/qb/master/getDivision",{})}getSubDivision(){return this.$http.post("/api/qb/master/getSubDivision",{})}getNonManPowerList(e,t,a,n){return this.$http.post("/api/qb/master/getNonManPowerList",{opportunityId:e,defaultCurrency:t,quoteCurrency:a,conversionTypeId:n})}createQuote(e){return this.$http.post("/api/qb/quote/createQuote",{quoteDetails:e})}getEntity(){return this.$http.post("/api/qb/master/getEntity",{})}getQuoteDetails(e){return this.$http.post("/api/qb/quote/getQuoteDetails",{quoteId:e})}getQuoteCalendarData(e,t,a,n=!0,o=1,r=1){return this.$http.post("/api/qb/quote/getQuoteCalendarData",{startDate:e,endDate:t,positionDetails:a,fetchSavedData:n,calendarId:o,workScheduleId:r})}getOpportunityMetaDetailsForQuote(e){return this.$http.post("/api/qb/quote/getOpportunityMetaDetailsForQuote",{opportunityId:e})}getQuoteTypeConfig(e){return this.$http.post("/api/qb/quote/getQuoteTypeConfig",{quoteType:e})}updateQuote(e){return this.$http.post("/api/qb/quote/updateQuote",{quoteDetails:e})}getQuoteActivity(e,t=!1){return this.$http.post("/api/qb/quote/getQuoteActivity",{quoteId:e,showQuoteValue:t})}resolveQuoteOppIntegration(e,t,a,n=!0,o){return this.$http.post("/api/qb/quote/resolveQuoteOppIntegration",{opportunityId:e,opportunityStatus:t,changeType:a,updateValue:n,quoteDetails:o})}updateValueInOpportunity(e,t,a){return this.$http.post("/api/qb/quote/updateValueInOpportunity",{opportunityId:e,quoteId:t,quoteDetails:a})}getOpportunityFYData(e,t,a){return this.$http.post("/api/qb/quote/getOpportunityFYData",{opportunityId:e,opportunityCurrency:t,isFrom:a})}getQuoteConfiguration(e=[]){return this.$http.post("/api/qb/master/getQuoteConfiguration",{config:e})}updateQuoteConfiguration(e=[]){return this.$http.post("/api/qb/master/updateQuoteConfiguration",{config:e})}getServices(e=!1){return this.$http.post("/api/qb/serviceConfig/getServices",{activeService:e})}createService(e){return this.$http.post("/api/qb/serviceConfig/createService",{serviceName:e})}editServiceStatus(e,t){return this.$http.post("/api/qb/serviceConfig/editServiceStatus",{serviceId:e,status:t})}getCustomerMaster(){return this.$http.post("/api/invoice/customerMasterData",{})}getLicenseConfiguration(){return this.$http.post("/api/qb/master/getLicenseConfiguration",{})}insertLicenseConfiguration(e){return this.$http.post("/api/qb/master/insertLicenseConfiguration",{licenseData:e})}updateLicenseConfiguration(e){return this.$http.post("/api/qb/master/updateLicenseConfiguration",{licenseData:e})}getLicenseList(e,t,a,n){return this.$http.post("/api/qb/master/getLicenseList",{opportunityId:e,defaultCurrency:t,quoteCurrency:a,conversionTypeId:n})}getRate(e,t,a,n,o){return this.$http.post("/api/qb/rateCard/getRate",{rateFieldDetails:e,opportunityId:t,defaultCurrency:a,quoteCurrency:n,conversionTypeId:o})}getQuantityForPosition(e,t,a,n=1,o=1){return this.$http.post("/api/qb/quote/getQuantityForPosition",{startDate:e,endDate:t,unitConfig:a,calendarId:n,workScheduleId:o})}getOpportunityValueChangeLog(e){return this.$http.post("/api/qb/quote/getOpportunityValueChangeLog",{opportunityId:e})}getOpportunityAuditChangeLogforField(e,t){return this.$http.post("/api/opportunity/getOpportunityAuditChangeLogforField",{opportunityId:e,fieldName:t})}getOppQuoteValueStatus(e){return this.$http.post("/api/qb/quote/getOppQuoteValueStatus",{opportunityId:e})}getUserFieldConfig(){return this.$http.post("/api/qb/quote/getUserFieldConfig",{})}updateUserFieldConfig(e){return this.$http.post("/api/qb/quote/updateUserFieldConfig",{fieldConfig:e})}updateInActiveQuoteInOpportunity(e){return this.$http.post("/api/qb/quote/updateInActiveQuoteInOpportunity",{opportunityId:e})}updateDateChangeInQuote(e,t=null){return this.$http.post("/api/qb/quote/updateDateChangeInQuote",{opportunityId:e,quoteId:t})}createQuoteFromSourceQuote(e,t,a,n=null,o){return this.$http.post("/api/qb/quote/createQuoteFromSourceQuote",{sourceQuoteId:e,quoteName:t,oppDetails:a,copyOption:n,quoteType:o})}updateServiceRollup(e,t){return this.$http.post("/api/qb/serviceConfig/updateServiceRollup",{serviceId:e,rollupServiceId:t})}insertQuoteUOM(e){return this.$http.post("/api/qb/master/insertQuoteUOM",{uomData:e})}updateQuoteUOM(e){return this.$http.post("/api/qb/master/updateQuoteUOM",{uomData:e})}insertCustomPercentage(e){return this.$http.post("/api/qb/master/insertCustomPercentage",{cpData:e})}updateCustomPercentage(e){return this.$http.post("/api/qb/master/updateCustomPercentage",{cpData:e})}getOpportunityQuoteList(e,t,a,n){return this.$http.post("/api/qb/quote/getOpportunityQuoteList",{opportunityId:e,limit:t,offset:a,searchQuery:n})}getOppStatusConfig(){return this.$http.post("/api/qb/master/getOppStatusConfig",{})}updateOppStatusConfig(e){return this.$http.post("/api/qb/master/updateOppStatusConfig",{statusConfig:e})}insertDiscount(e){return this.$http.post("/api/qb/master/insertDiscount",{discountData:e})}updateDiscount(e){return this.$http.post("/api/qb/master/updateDiscount",{discountData:e})}insertTax(e){return this.$http.post("/api/qb/master/insertTax",{taxData:e})}updateTax(e){return this.$http.post("/api/qb/master/updateTax",{taxData:e})}deleteQuote(e,t){return this.$http.post("/api/qb/quote/deleteQuote",{quoteId:e,opportunityId:t})}getQuoteAccessPrivilege(e){return this.$http.post("/api/qb/quote/getQuoteAccessPrivilege",{opportunityId:e})}getQuotePosition(){return this.$http.post("/api/qb/master/getQuotePosition",{})}insertQuotePosition(e){return this.$http.post("/api/qb/master/insertQuotePosition",{positionData:e})}updateQuotePosition(e){return this.$http.post("/api/qb/master/updateQuotePosition",{positionData:e})}getQuoteWorkLocationEntityMapping(){return this.$http.post("/api/qb/master/getQuoteWorkLocationEntityMapping",{})}getRevenueRegionEntitySalesMappingList(){return this.$http.post("/api/qb/master/getRevenueRegionEntitySalesMappingList",{})}insertQuoteWorkLocationEntityMapping(e){return this.$http.post("/api/qb/master/insertQuoteWorkLocationEntityMapping",{mappingData:e})}updateQuoteWorkLocationEntityMapping(e){return this.$http.post("/api/qb/master/updateQuoteWorkLocationEntityMapping",{mappingData:e})}insertQuoteEntitySalesRegionRevenueRegionMapping(e){return this.$http.post("/api/qb/master/insertQuoteEntitySalesRegionRevenueRegionMapping",{mappingData:e})}updateQuoteEntitySalesRegionRevenueRegionMapping(e){return this.$http.post("/api/qb/master/updateQuoteEntitySalesRegionRevenueRegionMapping",{mappingData:e})}getQuoteServiceDivisionMapping(){return this.$http.post("/api/qb/master/getQuoteServiceDivisionMapping",{})}insertQuoteServiceDivisionMapping(e){return this.$http.post("/api/qb/master/insertQuoteServiceDivisionMapping",{mappingData:e})}updateQuoteServiceDivisionMapping(e){return this.$http.post("/api/qb/master/updateQuoteServiceDivisionMapping",{mappingData:e})}getMonthlyRevenueProjection(){return this.$http.post("/api/qb/quote/getMonthlyRevenueProjection",{})}getRcFieldConfiguration(e){return this.$http.post("/api/qb/master/getRcFieldConfiguration",{rcType:e})}updateRcFieldConfig(e){return this.$http.post("/api/qb/master/updateRcFieldConfig",{fieldConfig:e})}getRateCardConfigurationDataConfig(){return this.$http.post("/api/qb/master/getRateCardConfigurationDataConfig",{})}getRCMasterDataList(e){return this.$http.post("/api/qb/master/getRCMasterDataList",{id:e})}insertRCMasterData(e,t){return this.$http.post("/api/qb/master/insertRCMasterData",{data:e,tableData:t})}updateRCMasterData(e,t){return this.$http.post("/api/qb/master/updateRCMasterData",{data:e,tableData:t})}getSBHBProjection(){return this.$http.post("/api/qb/quote/getSBHBProjection",{})}getNonEditableStatus(){return this.$http.post("/api/qb/master/getNonEditableStatus",{})}getFinancialDocumentConfig(){return this.$http.post("/api/opportunity/FD/config",{})}getFinancialDocumentSalesOrgConfig(){return this.$http.post("/api/opportunity/FD/approver/sales-org-config",{})}getDocUploadStageWiseConfig(){return this.$http.post("/api/opportunity/FD/getDocUploadStageWiseConfig",{})}stageWiseDocUploadUpdate(e){return this.$http.post("/api/opportunity/FD/stageWiseDocUploadUpdate",{config:e})}updateFinancialDocApproverConfig(e){return this.$http.post("/api/opportunity/FD/config/update",{config:e})}updateQuoteActivationConfig(e){return this.$http.post("/api/qb/quote/updateQuoteActivationConfig",{config:e})}getQuoteActivationApproverConfig(){return this.$http.post("/api/qb/quote/getQuoteActivationApproverConfig",{})}updateFinancialDocSalesOrgApproverConfig(e){return this.$http.post("/api/opportunity/FD/approver/sales-org-config/update",{config:e})}getAllMembers(){return this.$http.post("/api/salesMaster/getAllUserFromDB",{config:{}})}getSalesRegionMaster(){return this.$http.post("/api/opportunity/getSalesRegionMaster",{config:{}})}getCalendarCombinationConfig(){return this.$http.post("/api/qb/quote/getCalendarCombinationConfig",{})}getRevenueRegionCombinationConfig(){return this.$http.post("/api/qb/quote/getRevenueRegionCombinationConfig",{})}checkMilestoneApplicable(e,t){return this.$http.post("/api/qb/quote/checkOpportunityIsApplicableForMileStone",{opportunity_id:e,quote_id:t})}getMilestoneListQB(e){return this.$http.post("/api/qb/master/getMilestoneList",{quote_position_ids:e})}checkForPercentageApproval(e,t=!1){return new Promise((a,n)=>{this.$http.post("/api/qb/quote/checkForPercentageApproval",{quote:e,is_from_edit:t}).subscribe(e=>{a("S"===e.messType&&e)},e=>{n(e)})})}checkQuoteAllocation(e){return new Promise((t,a)=>{this.$http.post("/api/qb/quote/checkAllocation",{quote_header_id:e}).subscribe(e=>{t(e||!1)},e=>{a(e)})})}setFormAction(e,t,a,n){const o={action:e,rcType:t,rcLabel:a,forUpdate:n};this.formActionSubject.next(o),localStorage.setItem("formAction",JSON.stringify(o))}getStoredFormAction(){const e=localStorage.getItem("formAction");return e?JSON.parse(e):{action:"",rcType:null,rcLabel:null}}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275inject"](i.c))},e.\u0275prov=r["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},w76M:function(e,t,a){"use strict";a.d(t,"a",(function(){return p})),a.d(t,"b",(function(){return d}));var n=a("jhN1"),o=a("fXoL"),r=a("oHs6"),i=a("PVOt"),s=a("6t9p");const l=["*"];let p=(()=>{let e=class extends i.b{constructor(e,t,a,n,o,r,i,s){super(e,t,a,n,i,s),this._watcherHelper=n,this._idh=o,this._createEventEmitters([{subscribe:"contentReady",emit:"onContentReady"},{subscribe:"disposing",emit:"onDisposing"},{subscribe:"hidden",emit:"onHidden"},{subscribe:"hiding",emit:"onHiding"},{subscribe:"initialized",emit:"onInitialized"},{subscribe:"optionChanged",emit:"onOptionChanged"},{subscribe:"resize",emit:"onResize"},{subscribe:"resizeEnd",emit:"onResizeEnd"},{subscribe:"resizeStart",emit:"onResizeStart"},{subscribe:"showing",emit:"onShowing"},{subscribe:"shown",emit:"onShown"},{subscribe:"titleRendered",emit:"onTitleRendered"},{emit:"accessKeyChange"},{emit:"animationChange"},{emit:"closeOnOutsideClickChange"},{emit:"containerChange"},{emit:"contentTemplateChange"},{emit:"deferRenderingChange"},{emit:"disabledChange"},{emit:"dragEnabledChange"},{emit:"elementAttrChange"},{emit:"focusStateEnabledChange"},{emit:"fullScreenChange"},{emit:"heightChange"},{emit:"hintChange"},{emit:"hoverStateEnabledChange"},{emit:"maxHeightChange"},{emit:"maxWidthChange"},{emit:"minHeightChange"},{emit:"minWidthChange"},{emit:"positionChange"},{emit:"resizeEnabledChange"},{emit:"rtlEnabledChange"},{emit:"shadingChange"},{emit:"shadingColorChange"},{emit:"showCloseButtonChange"},{emit:"showTitleChange"},{emit:"tabIndexChange"},{emit:"titleChange"},{emit:"titleTemplateChange"},{emit:"toolbarItemsChange"},{emit:"visibleChange"},{emit:"widthChange"}]),this._idh.setHost(this),r.setHost(this)}get accessKey(){return this._getOption("accessKey")}set accessKey(e){this._setOption("accessKey",e)}get animation(){return this._getOption("animation")}set animation(e){this._setOption("animation",e)}get closeOnOutsideClick(){return this._getOption("closeOnOutsideClick")}set closeOnOutsideClick(e){this._setOption("closeOnOutsideClick",e)}get container(){return this._getOption("container")}set container(e){this._setOption("container",e)}get contentTemplate(){return this._getOption("contentTemplate")}set contentTemplate(e){this._setOption("contentTemplate",e)}get deferRendering(){return this._getOption("deferRendering")}set deferRendering(e){this._setOption("deferRendering",e)}get disabled(){return this._getOption("disabled")}set disabled(e){this._setOption("disabled",e)}get dragEnabled(){return this._getOption("dragEnabled")}set dragEnabled(e){this._setOption("dragEnabled",e)}get elementAttr(){return this._getOption("elementAttr")}set elementAttr(e){this._setOption("elementAttr",e)}get focusStateEnabled(){return this._getOption("focusStateEnabled")}set focusStateEnabled(e){this._setOption("focusStateEnabled",e)}get fullScreen(){return this._getOption("fullScreen")}set fullScreen(e){this._setOption("fullScreen",e)}get height(){return this._getOption("height")}set height(e){this._setOption("height",e)}get hint(){return this._getOption("hint")}set hint(e){this._setOption("hint",e)}get hoverStateEnabled(){return this._getOption("hoverStateEnabled")}set hoverStateEnabled(e){this._setOption("hoverStateEnabled",e)}get maxHeight(){return this._getOption("maxHeight")}set maxHeight(e){this._setOption("maxHeight",e)}get maxWidth(){return this._getOption("maxWidth")}set maxWidth(e){this._setOption("maxWidth",e)}get minHeight(){return this._getOption("minHeight")}set minHeight(e){this._setOption("minHeight",e)}get minWidth(){return this._getOption("minWidth")}set minWidth(e){this._setOption("minWidth",e)}get position(){return this._getOption("position")}set position(e){this._setOption("position",e)}get resizeEnabled(){return this._getOption("resizeEnabled")}set resizeEnabled(e){this._setOption("resizeEnabled",e)}get rtlEnabled(){return this._getOption("rtlEnabled")}set rtlEnabled(e){this._setOption("rtlEnabled",e)}get shading(){return this._getOption("shading")}set shading(e){this._setOption("shading",e)}get shadingColor(){return this._getOption("shadingColor")}set shadingColor(e){this._setOption("shadingColor",e)}get showCloseButton(){return this._getOption("showCloseButton")}set showCloseButton(e){this._setOption("showCloseButton",e)}get showTitle(){return this._getOption("showTitle")}set showTitle(e){this._setOption("showTitle",e)}get tabIndex(){return this._getOption("tabIndex")}set tabIndex(e){this._setOption("tabIndex",e)}get title(){return this._getOption("title")}set title(e){this._setOption("title",e)}get titleTemplate(){return this._getOption("titleTemplate")}set titleTemplate(e){this._setOption("titleTemplate",e)}get toolbarItems(){return this._getOption("toolbarItems")}set toolbarItems(e){this._setOption("toolbarItems",e)}get visible(){return this._getOption("visible")}set visible(e){this._setOption("visible",e)}get width(){return this._getOption("width")}set width(e){this._setOption("width",e)}get toolbarItemsChildren(){return this._getOption("toolbarItems")}set toolbarItemsChildren(e){this.setChildren("toolbarItems",e)}_createInstance(e,t){return new r.a(e,t)}ngOnDestroy(){this._destroyWidget()}ngOnChanges(e){super.ngOnChanges(e),this.setupChanges("toolbarItems",e)}setupChanges(e,t){e in this._optionsToUpdate||this._idh.setup(e,t)}ngDoCheck(){this._idh.doCheck("toolbarItems"),this._watcherHelper.checkWatchers(),super.ngDoCheck(),super.clearChangedOptions()}_setOption(e,t){let a=this._idh.setupSingle(e,t),n=null!==this._idh.getChanges(e,t);(a||n)&&super._setOption(e,t)}};return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](o.ElementRef),o["\u0275\u0275directiveInject"](o.NgZone),o["\u0275\u0275directiveInject"](i.e),o["\u0275\u0275directiveInject"](i.j),o["\u0275\u0275directiveInject"](i.g),o["\u0275\u0275directiveInject"](i.i),o["\u0275\u0275directiveInject"](n.h),o["\u0275\u0275directiveInject"](o.PLATFORM_ID))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["dx-popup"]],contentQueries:function(e,t,a){if(1&e&&o["\u0275\u0275contentQuery"](a,s.L,!1),2&e){let e;o["\u0275\u0275queryRefresh"](e=o["\u0275\u0275loadQuery"]())&&(t.toolbarItemsChildren=e)}},inputs:{accessKey:"accessKey",animation:"animation",closeOnOutsideClick:"closeOnOutsideClick",container:"container",contentTemplate:"contentTemplate",deferRendering:"deferRendering",disabled:"disabled",dragEnabled:"dragEnabled",elementAttr:"elementAttr",focusStateEnabled:"focusStateEnabled",fullScreen:"fullScreen",height:"height",hint:"hint",hoverStateEnabled:"hoverStateEnabled",maxHeight:"maxHeight",maxWidth:"maxWidth",minHeight:"minHeight",minWidth:"minWidth",position:"position",resizeEnabled:"resizeEnabled",rtlEnabled:"rtlEnabled",shading:"shading",shadingColor:"shadingColor",showCloseButton:"showCloseButton",showTitle:"showTitle",tabIndex:"tabIndex",title:"title",titleTemplate:"titleTemplate",toolbarItems:"toolbarItems",visible:"visible",width:"width"},outputs:{onContentReady:"onContentReady",onDisposing:"onDisposing",onHidden:"onHidden",onHiding:"onHiding",onInitialized:"onInitialized",onOptionChanged:"onOptionChanged",onResize:"onResize",onResizeEnd:"onResizeEnd",onResizeStart:"onResizeStart",onShowing:"onShowing",onShown:"onShown",onTitleRendered:"onTitleRendered",accessKeyChange:"accessKeyChange",animationChange:"animationChange",closeOnOutsideClickChange:"closeOnOutsideClickChange",containerChange:"containerChange",contentTemplateChange:"contentTemplateChange",deferRenderingChange:"deferRenderingChange",disabledChange:"disabledChange",dragEnabledChange:"dragEnabledChange",elementAttrChange:"elementAttrChange",focusStateEnabledChange:"focusStateEnabledChange",fullScreenChange:"fullScreenChange",heightChange:"heightChange",hintChange:"hintChange",hoverStateEnabledChange:"hoverStateEnabledChange",maxHeightChange:"maxHeightChange",maxWidthChange:"maxWidthChange",minHeightChange:"minHeightChange",minWidthChange:"minWidthChange",positionChange:"positionChange",resizeEnabledChange:"resizeEnabledChange",rtlEnabledChange:"rtlEnabledChange",shadingChange:"shadingChange",shadingColorChange:"shadingColorChange",showCloseButtonChange:"showCloseButtonChange",showTitleChange:"showTitleChange",tabIndexChange:"tabIndexChange",titleChange:"titleChange",titleTemplateChange:"titleTemplateChange",toolbarItemsChange:"toolbarItemsChange",visibleChange:"visibleChange",widthChange:"widthChange"},features:[o["\u0275\u0275ProvidersFeature"]([i.e,i.j,i.i,i.g]),o["\u0275\u0275InheritDefinitionFeature"],o["\u0275\u0275NgOnChangesFeature"]],ngContentSelectors:l,decls:1,vars:0,template:function(e,t){1&e&&(o["\u0275\u0275projectionDef"](),o["\u0275\u0275projection"](0))},encapsulation:2}),e})(),d=(()=>{let e=class{};return e.\u0275mod=o["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=o["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[s.bb,s.Gc,s.Vd,s.vd,s.hb,s.lb,s.sb,s.id,s.jd,s.M,i.c,i.f,n.b],s.bb,s.Gc,s.Vd,s.vd,s.hb,s.lb,s.sb,s.id,s.jd,s.M,i.f]}),e})()}}]);