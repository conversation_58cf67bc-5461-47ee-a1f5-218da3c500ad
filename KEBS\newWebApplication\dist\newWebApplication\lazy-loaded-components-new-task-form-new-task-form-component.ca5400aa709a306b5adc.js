(window.webpackJsonp=window.webpackJsonp||[]).push([[759],{"0auA":function(t,e,n){"use strict";n.r(e),n.d(e,"NewTaskFormComponent",(function(){return q}));var i=n("fXoL"),o=n("3Pt+"),a=n("0IaG"),r=n("ofXK"),s=n("jtHE"),l=n("XNiG"),c=n("NJ67"),d=n("1G5W"),m=n("kmnG"),p=n("d3UM"),u=n("FKr1"),h=n("WJ5W");const g=["singleSelect"];function f(t,e){if(1&t){const t=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"mat-option",6),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](t);const n=e.$implicit;return i["\u0275\u0275nextContext"]().emitChanges(n)})),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()}if(2&t){const t=e.$implicit;i["\u0275\u0275property"]("value",t.id),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" ",t.name," ")}}let v=(()=>{class t extends c.a{constructor(t){super(),this.renderer=t,this.fieldCtrl=new o.j,this.fieldFilterCtrl=new o.j,this.list=[],this.required=!1,this.valueChange=new i.EventEmitter,this.disabled=!1,this.filteredList=new s.a,this.change=new i.EventEmitter,this._onDestroy=new l.b,this.emitChanges=t=>{this.change.emit(t)}}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(d.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(d.a)(this._onDestroy)).subscribe(t=>{this.value=t,this.onChange(t),this.valueChange.emit(t)})}ngOnChanges(){null!=this.list&&this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let t=this.fieldFilterCtrl.value;t?(t=t.toLowerCase(),this.filteredList.next(this.list.filter(e=>e.name.toLowerCase().indexOf(t)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(t){t?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(t){this.fieldCtrl.setValue(t)}}return t.\u0275fac=function(e){return new(e||t)(i["\u0275\u0275directiveInject"](i.Renderer2))},t.\u0275cmp=i["\u0275\u0275defineComponent"]({type:t,selectors:[["app-input-search"]],viewQuery:function(t,e){if(1&t&&i["\u0275\u0275viewQuery"](g,!0),2&t){let t;i["\u0275\u0275queryRefresh"](t=i["\u0275\u0275loadQuery"]())&&(e.singleSelect=t.first)}},inputs:{list:"list",placeholder:"placeholder",required:"required",disabled:"disabled"},outputs:{valueChange:"valueChange",change:"change"},features:[i["\u0275\u0275ProvidersFeature"]([{provide:o.t,useExisting:Object(i.forwardRef)(()=>t),multi:!0}]),i["\u0275\u0275InheritDefinitionFeature"],i["\u0275\u0275NgOnChangesFeature"]],decls:11,vars:11,consts:[["appearance","outline"],[3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value"],[3,"value","click",4,"ngFor","ngForOf"],[3,"value","click"]],template:function(t,e){1&t&&(i["\u0275\u0275elementStart"](0,"mat-form-field",0),i["\u0275\u0275elementStart"](1,"mat-label"),i["\u0275\u0275text"](2),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](3,"mat-select",1,2),i["\u0275\u0275elementStart"](5,"mat-option"),i["\u0275\u0275element"](6,"ngx-mat-select-search",3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](7,"mat-option",4),i["\u0275\u0275text"](8,"None"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](9,f,2,2,"mat-option",5),i["\u0275\u0275pipe"](10,"async"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&t&&(i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate"](e.placeholder),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("formControl",e.fieldCtrl)("placeholder",e.placeholder)("required",e.required)("disabled",e.disabled),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("formControl",e.fieldFilterCtrl)("placeholderLabel",e.placeholder),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("value",null),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngForOf",i["\u0275\u0275pipeBind1"](10,9,e.filteredList)))},directives:[m.c,m.g,p.c,o.v,o.k,o.F,u.p,h.a,r.NgForOf],pipes:[r.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}"]}),t})();var y=n("mrSG"),b=n("Kj3r"),w=n("F97M"),C=n("XVR1"),S=n("qFsG"),D=n("/1cH"),k=n("NFeN");function O(t,e){if(1&t&&(i["\u0275\u0275elementStart"](0,"mat-label"),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&t){const t=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](t.label)}}function E(t,e){if(1&t&&(i["\u0275\u0275elementStart"](0,"mat-option",7),i["\u0275\u0275elementStart"](1,"small"),i["\u0275\u0275text"](2),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit,n=i["\u0275\u0275nextContext"]();i["\u0275\u0275property"]("hidden",!n.isAutocomplete)("value",t),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate2"]("",t.displayName," | ",t.mail,"")}}let x=(()=>{class t extends c.a{constructor(t,e){super(),this.graphApi=t,this._AppShareService=e,this.userSearchSubject=new l.b,this.isAutocomplete=!1,this.Formwidth="",this.showUserList=new i.EventEmitter,this.selectedUser=new i.EventEmitter,this.label="",this.blur=new i.EventEmitter,this.required=!1,this.fieldCtrl=new o.j,this.disabled=!1,this.isGraphApi=0,this._onDestroy=new l.b}ngOnInit(){this.userSearchSubject.pipe(Object(b.a)(600)).subscribe(t=>Object(y.c)(this,void 0,void 0,(function*(){if(console.log(t),this.isGraphApi)this.graphApi.getUserSuggestions(t).then(e=>Object(y.c)(this,void 0,void 0,(function*(){if(e&&e.length>0)this.showUserList.emit(e);else{let e=yield this.getUserSuggestionsFromDB(t);this.showUserList.emit(e)}})),e=>Object(y.c)(this,void 0,void 0,(function*(){console.log(e);let n=yield this.getUserSuggestionsFromDB(t);this.showUserList.emit(n)})));else{let e=yield this.getUserSuggestionsFromDB(t);this.showUserList.emit(e)}}))),this.fieldCtrl.valueChanges.pipe(Object(d.a)(this._onDestroy)).subscribe(t=>{console.log(t),t?(this.value=t.id,this.onChange(t.id)):(this.value="",this.onChange(""))})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}ngAfterViewInit(){}searchUser(t){if(!t)return this.graphApi.userSuggestions=[];this.userSearchSubject.next(t)}resetSuggestion(){this.graphApi.userSuggestions=[]}selectedOption(t){this.selectedUser.emit(t.option.value),this.value=t.option.value,this.blur.emit()}displayUserName(t){return t?t.displayName:""}setDisabledState(t){t?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(t){return Object(y.c)(this,void 0,void 0,(function*(){if(console.log("inisde writevalue"),console.log(t),t)if(this.isGraphApi)this.graphApi.getUserProfile(t).then(e=>Object(y.c)(this,void 0,void 0,(function*(){if(e)this.fieldCtrl.setValue(e);else{let e=yield this.getUserProfileFromDB(t);this.fieldCtrl.setValue(e)}})));else{let e=yield this.getUserProfileFromDB(t);console.log(e),this.fieldCtrl.setValue(e)}else this.fieldCtrl.setValue("")}))}getUserSuggestionsFromDB(t){return Object(y.c)(this,void 0,void 0,(function*(){let e=yield this._AppShareService.getUserSuggestionsFromDB(t);return this.graphApi.userSuggestions=e.value,e.value}))}getUserProfileFromDB(t){return Object(y.c)(this,void 0,void 0,(function*(){let e=yield this._AppShareService.getUserProfileFromDB(t);return console.log(e),e&&e.length>0?e[0]:""}))}}return t.\u0275fac=function(e){return new(e||t)(i["\u0275\u0275directiveInject"](w.a),i["\u0275\u0275directiveInject"](C.a))},t.\u0275cmp=i["\u0275\u0275defineComponent"]({type:t,selectors:[["app-search-user"]],inputs:{isAutocomplete:"isAutocomplete",Formwidth:"Formwidth",label:"label",required:"required",disabled:"disabled",isGraphApi:"isGraphApi"},outputs:{showUserList:"showUserList",selectedUser:"selectedUser",blur:"blur"},features:[i["\u0275\u0275ProvidersFeature"]([{provide:o.t,useExisting:Object(i.forwardRef)(()=>t),multi:!0}]),i["\u0275\u0275InheritDefinitionFeature"]],decls:9,vars:9,consts:[["appearance","outline","width","Formwidth"],[4,"ngIf"],["matInput","",3,"matAutocomplete","placeholder","required","formControl","disabled","keyup","focus"],["matSuffix","",2,"color","#66615B !important","font-size","21px !important"],[3,"hidden","displayWith","optionSelected"],["auto","matAutocomplete"],[3,"hidden","value",4,"ngFor","ngForOf"],[3,"hidden","value"]],template:function(t,e){if(1&t&&(i["\u0275\u0275elementStart"](0,"div"),i["\u0275\u0275elementStart"](1,"mat-form-field",0),i["\u0275\u0275template"](2,O,2,1,"mat-label",1),i["\u0275\u0275elementStart"](3,"input",2),i["\u0275\u0275listener"]("keyup",(function(t){return e.searchUser(t.target.value)}))("focus",(function(){return e.resetSuggestion()})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"mat-icon",3),i["\u0275\u0275text"](5,"person_pin"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](6,"mat-autocomplete",4,5),i["\u0275\u0275listener"]("optionSelected",(function(t){return e.selectedOption(t)})),i["\u0275\u0275template"](8,E,3,4,"mat-option",6),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&t){const t=i["\u0275\u0275reference"](7);i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf",e.label),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("matAutocomplete",t)("placeholder",e.label)("required",e.required)("formControl",e.fieldCtrl)("disabled",e.disabled),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("hidden",!e.isAutocomplete)("displayWith",e.displayUserName),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngForOf",e.graphApi.userSuggestions)}},directives:[m.c,r.NgIf,S.b,D.d,o.e,o.F,o.v,o.k,k.a,m.i,D.b,r.NgForOf,m.g,u.p],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding-bottom:8px!important}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-underline{bottom:0}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-infix{padding:.65em 0!important;border-top:.54375em solid transparent!important;font-size:13px}.mat-form-field[_ngcontent-%COMP%]     .mat-select-arrow{margin:0 4px!important}"]}),t})();var _=n("dNgK"),T=n("bTqV"),F=n("iadO"),P=n("RJSY"),M=n("+yIk"),A=n("tyNb"),I=n("XXEo"),j=n("ihCf");function N(t,e){if(1&t){const t=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",40),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](t),i["\u0275\u0275nextContext"]().createTask()})),i["\u0275\u0275elementStart"](1,"mat-icon"),i["\u0275\u0275text"](2," done_all"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}}function U(t,e){if(1&t){const t=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",41),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](t),i["\u0275\u0275nextContext"]().editTask()})),i["\u0275\u0275elementStart"](1,"mat-icon"),i["\u0275\u0275text"](2," done_all"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}}const B=function(t,e){return{"btn-not-active":t,"btn-active":e}};let q=(()=>{class t{constructor(t,e,n,a,r,s,l,c,d){this.dialogRef=t,this.dialogData=e,this.dialog=n,this.fb=a,this.activityToMainService=r,this.snackBar=s,this.leadService=l,this.route=c,this.authService=d,this.close=new i.EventEmitter,this.submitted=!0,this.taskForm=this.fb.group({title:["",o.H.required],description:[""],assignedTo:["",o.H.required],createdBy:["",o.H.required],startDate:["",o.H.required],endDate:["",o.H.required],remindMe:[""],applicationName:[""],applicationReferenceId:[""],applicationId:[""],governanceType:[""]}),this.todaysOrTommorow=[{startDate:null,endDate:null}],this.resetButtons=()=>{this.todaysOrTommorow[0].startDate=this.todaysOrTommorow[0].endDate=null},this.dayClicked=(t,e)=>{if(console.log(t,e),"startDate"==t){if(this.todaysOrTommorow[0].startDate=e,"today"==e)this.taskForm.patchValue({startDate:new Date});else if("tommorow"==e){const t=new Date,e=new Date(t);e.setDate(t.getDate()+1),this.taskForm.patchValue({startDate:e})}}else if("endDate"==t)if(this.todaysOrTommorow[0].endDate=e,"today"==e)this.taskForm.patchValue({endDate:new Date});else if("tommorow"==e){const t=new Date,e=new Date(t);e.setDate(e.getDate()+1),this.taskForm.patchValue({endDate:e})}},this.closeClicked=()=>{this.close.emit(),this.dialogRef.close()},this.editTask=()=>{this.taskForm.valid?(this.submitted=!1,this.leadService.editTask(this.activityId,this.taskForm.value).then(t=>{this.taskForm.reset(),this.snackBar.open("Task updated Successfully!","Dismiss",{duration:2e3}),this.close.emit("close"),this.dialogRef.close("Update Required"),this.activityToMainService.sendMsg("reload"),this.submitted=!0,console.log(t)},t=>{this.snackBar.open("Failed to edit task.","Dismiss",{duration:2e3}),this.submitted=!0,console.error(t)})):(this.snackBar.open("Enter all Mandatory Fields!","Dismiss",{duration:2e3}),this.submitted=!0)},this.createTask=()=>{this.taskForm.valid?(this.submitted=!1,this.leadService.createTask(this.taskForm.value).subscribe(t=>{this.taskForm.reset(),this.snackBar.open("Task Created Successfully!","Dismiss",{duration:2e3}),this.close.emit("close"),this.dialogRef.close("Update Required"),this.activityToMainService.sendMsg("reload"),console.log(t),this.submitted=!0},t=>{this.snackBar.open("Failed to create task.","Dismiss",{duration:2e3}),console.error(t),this.submitted=!0})):(this.snackBar.open("Enter all Mandatory Fields!","Dismiss",{duration:2e3}),this.submitted=!0)}}ngOnChanges(){}updateFormWithTaskDetails(){if(console.log(this.mode,this.activityId),"Edit"==this.mode){let t=this.data?this.data:null;t?this.taskForm.patchValue({title:t.title,description:t.description,assignedTo:t.assigned_to,createdBy:t.task_created_by,startDate:t.start_date?t.start_date:t.planned_start_date,endDate:t.end_date?t.end_date:t.task_due_date,remindMe:t.reminder,governanceType:t.governance_activity_id}):console.log("waiting for data changes")}else this.taskForm.reset()}ngOnInit(){let t;this.mode=this.dialogData.mode,this.activityId=this.dialogData.activityId,this.data=this.dialogData.data,"Edit"==this.mode&&this.updateFormWithTaskDetails(),this.currentUser=this.authService.getProfile().profile,console.log("this.currentUser",this.currentUser),this.taskForm.patchValue({createdBy:this.currentUser.oid}),"leads"==window.location.pathname.split("/")[2]?t=35:"opportunities"==window.location.pathname.split("/")[2]&&(t=36),this.leadService.getLeadsGovernanceType(75).subscribe(t=>{this.leadGovernanceTypes=t},t=>{console.log(t)}),this.taskForm.patchValue({applicationName:window.location.pathname.split("/")[2],applicationReferenceId:window.location.pathname.split("/")[3],applicationId:t})}setDate(t,e){this.taskForm.get(t).patchValue(this.getDateFn(e))}getDateFn(t){const e=new Date,n=new Date(e);return console.log(n),console.log(t),"tommorow"==t?n.setDate(n.getDate()+1):"yesterday"!=t||n.setDate(n.getDate()-1),console.log(n),n}closeDialog(){this.close.emit("close"),this.dialogRef.close()}}return t.\u0275fac=function(e){return new(e||t)(i["\u0275\u0275directiveInject"](a.h),i["\u0275\u0275directiveInject"](a.a),i["\u0275\u0275directiveInject"](a.b),i["\u0275\u0275directiveInject"](o.i),i["\u0275\u0275directiveInject"](P.a),i["\u0275\u0275directiveInject"](_.a),i["\u0275\u0275directiveInject"](M.a),i["\u0275\u0275directiveInject"](A.a),i["\u0275\u0275directiveInject"](I.a))},t.\u0275cmp=i["\u0275\u0275defineComponent"]({type:t,selectors:[["app-new-task-form"]],outputs:{close:"close"},features:[i["\u0275\u0275NgOnChangesFeature"]],decls:79,vars:35,consts:[[3,"formGroup"],[1,"container","newTaskStyles"],[1,"row","border-bottom","solid"],[1,"col-11","pt-2","createAccount"],[1,"col-1","d-flex"],["mat-icon-button","",1,"ml-auto","close-button",3,"click"],[1,"close-Icon"],[1,"row","pt-2"],[1,"col-8","pl-0","pr-0"],[1,"row","pt-1"],[1,"col-12"],["appearance","outline",1,"create-account-field","title"],["matInput","","placeholder","title *","formControlName","title"],[1,"row"],["placeholder","Lead Governance Activity","formControlName","governanceType",1,"create-account-field","title",3,"list"],["appearance","outline",1,"textArea",2,"width","28rem","font-size","14px !important"],["matInput","","cdkTextareaAutosize","","cdkAutosizeMinRows","4","cdkAutosizeMaxRows","","placeholder","Description","formControlName","description"],["autosize","cdkTextareaAutosize"],["label","Assigned to","required","true","formControlName","assignedTo",1,"contact-person",3,"isAutocomplete"],["label","Created By","required","true","formControlName","createdBy",1,"contact-person",3,"isAutocomplete"],[1,"col-5"],["appearance","outline",1,"create-account-field"],["matInput","","required","true","formControlName","startDate",3,"matDatepicker"],["matSuffix","",3,"for"],["picker0",""],[1,"col-7"],[1,"row","p-0"],[1,"col-4","pl-0",2,"padding-top","14px"],["mat-raised-button","","color","primary",2,"margin-left","2%",3,"ngClass","click"],[1,"col-4",2,"padding-top","14px"],[1,"col-5","pt-2"],["matInput","","required","true","formControlName","endDate",3,"matDatepicker"],["picker1",""],[1,"col-4"],[1,"row","pt-3"],[1,"row","pt-4"],[1,"col-9","pl-4","quotes"],[1,"col-3"],["mat-icon-button","","class","iconbtn ml-2 mt-0","matTooltip","Create Task","type","submit",3,"click",4,"ngIf"],["mat-icon-button","","class","iconbtn ml-2 mt-0","matTooltip","Edit Task","type","submit",3,"click",4,"ngIf"],["mat-icon-button","","matTooltip","Create Task","type","submit",1,"iconbtn","ml-2","mt-0",3,"click"],["mat-icon-button","","matTooltip","Edit Task","type","submit",1,"iconbtn","ml-2","mt-0",3,"click"]],template:function(t,e){if(1&t&&(i["\u0275\u0275elementStart"](0,"form",0),i["\u0275\u0275elementStart"](1,"div",1),i["\u0275\u0275elementStart"](2,"div",2),i["\u0275\u0275elementStart"](3,"div",3),i["\u0275\u0275text"](4),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"div",4),i["\u0275\u0275elementStart"](6,"button",5),i["\u0275\u0275listener"]("click",(function(){return e.closeDialog()})),i["\u0275\u0275elementStart"](7,"mat-icon",6),i["\u0275\u0275text"](8,"close"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](9,"div",7),i["\u0275\u0275elementStart"](10,"div",8),i["\u0275\u0275elementStart"](11,"div",9),i["\u0275\u0275elementStart"](12,"div",10),i["\u0275\u0275elementStart"](13,"mat-form-field",11),i["\u0275\u0275elementStart"](14,"mat-label"),i["\u0275\u0275text"](15,"Title *"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](16,"input",12),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](17,"div",13),i["\u0275\u0275elementStart"](18,"div",10),i["\u0275\u0275element"](19,"app-input-search",14),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](20,"div",13),i["\u0275\u0275elementStart"](21,"div",10),i["\u0275\u0275elementStart"](22,"mat-form-field",15),i["\u0275\u0275element"](23,"textarea",16,17),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](25,"div",13),i["\u0275\u0275elementStart"](26,"div",10),i["\u0275\u0275element"](27,"app-search-user",18),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](28,"div",13),i["\u0275\u0275elementStart"](29,"div",10),i["\u0275\u0275element"](30,"app-search-user",19),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](31,"div",13),i["\u0275\u0275elementStart"](32,"div",20),i["\u0275\u0275elementStart"](33,"mat-form-field",21),i["\u0275\u0275elementStart"](34,"mat-label"),i["\u0275\u0275text"](35,"Start Date"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](36,"input",22),i["\u0275\u0275element"](37,"mat-datepicker-toggle",23),i["\u0275\u0275element"](38,"mat-datepicker",null,24),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](40,"div",25),i["\u0275\u0275elementStart"](41,"div",26),i["\u0275\u0275elementStart"](42,"div",27),i["\u0275\u0275elementStart"](43,"button",28),i["\u0275\u0275listener"]("click",(function(){return e.setDate("startDate","today")})),i["\u0275\u0275text"](44," Today"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](45,"div",27),i["\u0275\u0275elementStart"](46,"button",28),i["\u0275\u0275listener"]("click",(function(){return e.setDate("startDate","tommorow")})),i["\u0275\u0275text"](47," Tommorow"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](48,"div",29),i["\u0275\u0275elementStart"](49,"button",28),i["\u0275\u0275listener"]("click",(function(){return e.setDate("startDate","yesterday")})),i["\u0275\u0275text"](50," Yesterday"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](51,"div",13),i["\u0275\u0275elementStart"](52,"div",30),i["\u0275\u0275elementStart"](53,"mat-form-field",21),i["\u0275\u0275elementStart"](54,"mat-label"),i["\u0275\u0275text"](55,"End Date"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](56,"input",31),i["\u0275\u0275element"](57,"mat-datepicker-toggle",23),i["\u0275\u0275element"](58,"mat-datepicker",null,32),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](60,"div",25),i["\u0275\u0275elementStart"](61,"div",26),i["\u0275\u0275elementStart"](62,"div",27),i["\u0275\u0275elementStart"](63,"button",28),i["\u0275\u0275listener"]("click",(function(){return e.setDate("endDate","today")})),i["\u0275\u0275text"](64," Today"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](65,"div",27),i["\u0275\u0275elementStart"](66,"button",28),i["\u0275\u0275listener"]("click",(function(){return e.setDate("endDate","tommorow")})),i["\u0275\u0275text"](67," Tommorow"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](68,"div",29),i["\u0275\u0275elementStart"](69,"button",28),i["\u0275\u0275listener"]("click",(function(){return e.setDate("endDate","yesterday")})),i["\u0275\u0275text"](70," Yesterday"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](71,"div",33),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](72,"div",34),i["\u0275\u0275elementStart"](73,"div",35),i["\u0275\u0275elementStart"](74,"div",36),i["\u0275\u0275text"](75,' "In the Middle of difficulty, lies Opportunity" '),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](76,"div",37),i["\u0275\u0275template"](77,N,3,0,"button",38),i["\u0275\u0275template"](78,U,3,0,"button",39),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&t){const t=i["\u0275\u0275reference"](39),n=i["\u0275\u0275reference"](59);i["\u0275\u0275property"]("formGroup",e.taskForm),i["\u0275\u0275advance"](4),i["\u0275\u0275textInterpolate1"]("","Edit"==e.mode?"Edit":"Create new"," task"),i["\u0275\u0275advance"](15),i["\u0275\u0275property"]("list",e.leadGovernanceTypes),i["\u0275\u0275advance"](8),i["\u0275\u0275property"]("isAutocomplete",!0),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("isAutocomplete",!0),i["\u0275\u0275advance"](6),i["\u0275\u0275property"]("matDatepicker",t),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("for",t),i["\u0275\u0275advance"](6),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction2"](17,B,"tommorow"==e.todaysOrTommorow[0].startDate||null==e.todaysOrTommorow[0].startDate,"today"==e.todaysOrTommorow[0].startDate)),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction2"](20,B,"today"==e.todaysOrTommorow[0].startDate||null==e.todaysOrTommorow[0].startDate,"tommorow"==e.todaysOrTommorow[0].startDate)),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction2"](23,B,"today"==e.todaysOrTommorow[0].startDate||null==e.todaysOrTommorow[0].startDate,"yesterday"==e.todaysOrTommorow[0].startDate)),i["\u0275\u0275advance"](7),i["\u0275\u0275property"]("matDatepicker",n),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("for",n),i["\u0275\u0275advance"](6),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction2"](26,B,"tommorow"==e.todaysOrTommorow[0].endDate||null==e.todaysOrTommorow[0].endDate,"today"==e.todaysOrTommorow[0].endDate)),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction2"](29,B,"today"==e.todaysOrTommorow[0].endDate||null==e.todaysOrTommorow[0].endDate,"tommorow"==e.todaysOrTommorow[0].endDate)),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction2"](32,B,"today"==e.todaysOrTommorow[0].endDate||null==e.todaysOrTommorow[0].endDate,"yesterday"==e.todaysOrTommorow[0].endDate)),i["\u0275\u0275advance"](8),i["\u0275\u0275property"]("ngIf","Edit"!=e.mode&&e.submitted),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf","Edit"==e.mode&&e.submitted)}},directives:function(){return[o.J,o.w,o.n,T.a,k.a,m.c,m.g,S.b,o.e,o.v,o.l,v,j.b,x,o.F,F.g,F.i,m.i,F.f,r.NgClass,r.NgIf]},styles:[".newTaskStyles[_ngcontent-%COMP%]{background-image:url(createTask.757ceefab6f4ac37edbb.png);background-size:253px 200px;background-repeat:no-repeat;overflow-y:hidden!important;background-position:100% 60%;min-height:95vh}.newTaskStyles[_ngcontent-%COMP%]   .createAccount[_ngcontent-%COMP%]{font-size:14px;color:#cf0001;font-family:Roboto;font-weight:500}.newTaskStyles[_ngcontent-%COMP%]   .create-account-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%!important;padding-top:4px}.newTaskStyles[_ngcontent-%COMP%]   .contact-person[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-flex{padding:0 .75em;margin-top:-.25em;position:relative;width:28rem;height:40px}.newTaskStyles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff}.newTaskStyles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%], .newTaskStyles[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]{font-weight:400;font-size:13px!important;min-width:60px;line-height:33px;padding:0 8px;border-radius:4px;margin-right:6px!important;margin-bottom:3px}.newTaskStyles[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]{color:rgba(0,0,0,.534);background-color:rgba(194,193,193,.24)}.newTaskStyles[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]     .mat-form-field-wrapper{width:28rem}.newTaskStyles[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]     .mat-form-field-wrapper .mat-form-field-flex{height:40px}.newTaskStyles[_ngcontent-%COMP%]   .vip-contact[_ngcontent-%COMP%]{color:#1a1a1a;font-size:14px}.newTaskStyles[_ngcontent-%COMP%]   .check-box-text[_ngcontent-%COMP%], .newTaskStyles[_ngcontent-%COMP%]   .quotes[_ngcontent-%COMP%]{color:#66615b;font-size:14px}.newTaskStyles[_ngcontent-%COMP%]   .quotes[_ngcontent-%COMP%]{font-style:italic;font-weight:500}.newTaskStyles[_ngcontent-%COMP%]   .iconbtn[_ngcontent-%COMP%]{background-color:#c92020;color:#fff;padding:0;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.newTaskStyles[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.newTaskStyles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}"]}),t})()},"9jk4":function(t,e,n){"use strict";n.r(e),n.d(e,"NewTaskFormComponent",(function(){return q}));var i=n("fXoL"),o=n("3Pt+"),a=n("0IaG"),r=n("ofXK"),s=n("jtHE"),l=n("XNiG"),c=n("NJ67"),d=n("1G5W"),m=n("kmnG"),p=n("d3UM"),u=n("FKr1"),h=n("WJ5W");const g=["singleSelect"];function f(t,e){if(1&t){const t=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"mat-option",6),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](t);const n=e.$implicit;return i["\u0275\u0275nextContext"]().emitChanges(n)})),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()}if(2&t){const t=e.$implicit;i["\u0275\u0275property"]("value",t.id),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" ",t.name," ")}}let v=(()=>{class t extends c.a{constructor(t){super(),this.renderer=t,this.fieldCtrl=new o.j,this.fieldFilterCtrl=new o.j,this.list=[],this.required=!1,this.valueChange=new i.EventEmitter,this.disabled=!1,this.filteredList=new s.a,this.change=new i.EventEmitter,this._onDestroy=new l.b,this.emitChanges=t=>{this.change.emit(t)}}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(d.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(d.a)(this._onDestroy)).subscribe(t=>{this.value=t,this.onChange(t),this.valueChange.emit(t)})}ngOnChanges(){null!=this.list&&this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let t=this.fieldFilterCtrl.value;t?(t=t.toLowerCase(),this.filteredList.next(this.list.filter(e=>e.name.toLowerCase().indexOf(t)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(t){t?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(t){this.fieldCtrl.setValue(t)}}return t.\u0275fac=function(e){return new(e||t)(i["\u0275\u0275directiveInject"](i.Renderer2))},t.\u0275cmp=i["\u0275\u0275defineComponent"]({type:t,selectors:[["app-input-search"]],viewQuery:function(t,e){if(1&t&&i["\u0275\u0275viewQuery"](g,!0),2&t){let t;i["\u0275\u0275queryRefresh"](t=i["\u0275\u0275loadQuery"]())&&(e.singleSelect=t.first)}},inputs:{list:"list",placeholder:"placeholder",required:"required",disabled:"disabled"},outputs:{valueChange:"valueChange",change:"change"},features:[i["\u0275\u0275ProvidersFeature"]([{provide:o.t,useExisting:Object(i.forwardRef)(()=>t),multi:!0}]),i["\u0275\u0275InheritDefinitionFeature"],i["\u0275\u0275NgOnChangesFeature"]],decls:11,vars:11,consts:[["appearance","outline"],[3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value"],[3,"value","click",4,"ngFor","ngForOf"],[3,"value","click"]],template:function(t,e){1&t&&(i["\u0275\u0275elementStart"](0,"mat-form-field",0),i["\u0275\u0275elementStart"](1,"mat-label"),i["\u0275\u0275text"](2),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](3,"mat-select",1,2),i["\u0275\u0275elementStart"](5,"mat-option"),i["\u0275\u0275element"](6,"ngx-mat-select-search",3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](7,"mat-option",4),i["\u0275\u0275text"](8,"None"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](9,f,2,2,"mat-option",5),i["\u0275\u0275pipe"](10,"async"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&t&&(i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate"](e.placeholder),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("formControl",e.fieldCtrl)("placeholder",e.placeholder)("required",e.required)("disabled",e.disabled),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("formControl",e.fieldFilterCtrl)("placeholderLabel",e.placeholder),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("value",null),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngForOf",i["\u0275\u0275pipeBind1"](10,9,e.filteredList)))},directives:[m.c,m.g,p.c,o.v,o.k,o.F,u.p,h.a,r.NgForOf],pipes:[r.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}"]}),t})();var y=n("mrSG"),b=n("Kj3r"),w=n("F97M"),C=n("XVR1"),S=n("qFsG"),D=n("/1cH"),k=n("NFeN");function O(t,e){if(1&t&&(i["\u0275\u0275elementStart"](0,"mat-label"),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&t){const t=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](t.label)}}function E(t,e){if(1&t&&(i["\u0275\u0275elementStart"](0,"mat-option",7),i["\u0275\u0275elementStart"](1,"small"),i["\u0275\u0275text"](2),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit,n=i["\u0275\u0275nextContext"]();i["\u0275\u0275property"]("hidden",!n.isAutocomplete)("value",t),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate2"]("",t.displayName," | ",t.mail,"")}}let x=(()=>{class t extends c.a{constructor(t,e){super(),this.graphApi=t,this._AppShareService=e,this.userSearchSubject=new l.b,this.isAutocomplete=!1,this.Formwidth="",this.showUserList=new i.EventEmitter,this.selectedUser=new i.EventEmitter,this.label="",this.blur=new i.EventEmitter,this.required=!1,this.fieldCtrl=new o.j,this.disabled=!1,this.isGraphApi=0,this._onDestroy=new l.b}ngOnInit(){this.userSearchSubject.pipe(Object(b.a)(600)).subscribe(t=>Object(y.c)(this,void 0,void 0,(function*(){if(console.log(t),this.isGraphApi)this.graphApi.getUserSuggestions(t).then(e=>Object(y.c)(this,void 0,void 0,(function*(){if(e&&e.length>0)this.showUserList.emit(e);else{let e=yield this.getUserSuggestionsFromDB(t);this.showUserList.emit(e)}})),e=>Object(y.c)(this,void 0,void 0,(function*(){console.log(e);let n=yield this.getUserSuggestionsFromDB(t);this.showUserList.emit(n)})));else{let e=yield this.getUserSuggestionsFromDB(t);this.showUserList.emit(e)}}))),this.fieldCtrl.valueChanges.pipe(Object(d.a)(this._onDestroy)).subscribe(t=>{console.log(t),t?(this.value=t.id,this.onChange(t.id)):(this.value="",this.onChange(""))})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}ngAfterViewInit(){}searchUser(t){if(!t)return this.graphApi.userSuggestions=[];this.userSearchSubject.next(t)}resetSuggestion(){this.graphApi.userSuggestions=[]}selectedOption(t){this.selectedUser.emit(t.option.value),this.value=t.option.value,this.blur.emit()}displayUserName(t){return t?t.displayName:""}setDisabledState(t){t?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(t){return Object(y.c)(this,void 0,void 0,(function*(){if(console.log("inisde writevalue"),console.log(t),t)if(this.isGraphApi)this.graphApi.getUserProfile(t).then(e=>Object(y.c)(this,void 0,void 0,(function*(){if(e)this.fieldCtrl.setValue(e);else{let e=yield this.getUserProfileFromDB(t);this.fieldCtrl.setValue(e)}})));else{let e=yield this.getUserProfileFromDB(t);console.log(e),this.fieldCtrl.setValue(e)}else this.fieldCtrl.setValue("")}))}getUserSuggestionsFromDB(t){return Object(y.c)(this,void 0,void 0,(function*(){let e=yield this._AppShareService.getUserSuggestionsFromDB(t);return this.graphApi.userSuggestions=e.value,e.value}))}getUserProfileFromDB(t){return Object(y.c)(this,void 0,void 0,(function*(){let e=yield this._AppShareService.getUserProfileFromDB(t);return console.log(e),e&&e.length>0?e[0]:""}))}}return t.\u0275fac=function(e){return new(e||t)(i["\u0275\u0275directiveInject"](w.a),i["\u0275\u0275directiveInject"](C.a))},t.\u0275cmp=i["\u0275\u0275defineComponent"]({type:t,selectors:[["app-search-user"]],inputs:{isAutocomplete:"isAutocomplete",Formwidth:"Formwidth",label:"label",required:"required",disabled:"disabled",isGraphApi:"isGraphApi"},outputs:{showUserList:"showUserList",selectedUser:"selectedUser",blur:"blur"},features:[i["\u0275\u0275ProvidersFeature"]([{provide:o.t,useExisting:Object(i.forwardRef)(()=>t),multi:!0}]),i["\u0275\u0275InheritDefinitionFeature"]],decls:9,vars:9,consts:[["appearance","outline","width","Formwidth"],[4,"ngIf"],["matInput","",3,"matAutocomplete","placeholder","required","formControl","disabled","keyup","focus"],["matSuffix","",2,"color","#66615B !important","font-size","21px !important"],[3,"hidden","displayWith","optionSelected"],["auto","matAutocomplete"],[3,"hidden","value",4,"ngFor","ngForOf"],[3,"hidden","value"]],template:function(t,e){if(1&t&&(i["\u0275\u0275elementStart"](0,"div"),i["\u0275\u0275elementStart"](1,"mat-form-field",0),i["\u0275\u0275template"](2,O,2,1,"mat-label",1),i["\u0275\u0275elementStart"](3,"input",2),i["\u0275\u0275listener"]("keyup",(function(t){return e.searchUser(t.target.value)}))("focus",(function(){return e.resetSuggestion()})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"mat-icon",3),i["\u0275\u0275text"](5,"person_pin"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](6,"mat-autocomplete",4,5),i["\u0275\u0275listener"]("optionSelected",(function(t){return e.selectedOption(t)})),i["\u0275\u0275template"](8,E,3,4,"mat-option",6),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&t){const t=i["\u0275\u0275reference"](7);i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf",e.label),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("matAutocomplete",t)("placeholder",e.label)("required",e.required)("formControl",e.fieldCtrl)("disabled",e.disabled),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("hidden",!e.isAutocomplete)("displayWith",e.displayUserName),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngForOf",e.graphApi.userSuggestions)}},directives:[m.c,r.NgIf,S.b,D.d,o.e,o.F,o.v,o.k,k.a,m.i,D.b,r.NgForOf,m.g,u.p],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding-bottom:8px!important}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-underline{bottom:0}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-infix{padding:.65em 0!important;border-top:.54375em solid transparent!important;font-size:13px}.mat-form-field[_ngcontent-%COMP%]     .mat-select-arrow{margin:0 4px!important}"]}),t})();var _=n("dNgK"),T=n("bTqV"),F=n("iadO"),P=n("RUbJ"),M=n("WGBV"),A=n("tyNb"),I=n("XXEo"),j=n("ihCf");function N(t,e){if(1&t){const t=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",40),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](t),i["\u0275\u0275nextContext"]().createTask()})),i["\u0275\u0275elementStart"](1,"mat-icon"),i["\u0275\u0275text"](2," done_all"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}}function U(t,e){if(1&t){const t=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",41),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](t),i["\u0275\u0275nextContext"]().editTask()})),i["\u0275\u0275elementStart"](1,"mat-icon"),i["\u0275\u0275text"](2," done_all"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}}const B=function(t,e){return{"btn-not-active":t,"btn-active":e}};let q=(()=>{class t{constructor(t,e,n,a,r,s,l,c,d){this.dialogRef=t,this.dialogData=e,this.dialog=n,this.fb=a,this.activityToMainService=r,this.snackBar=s,this.accountService=l,this.route=c,this.authService=d,this.close=new i.EventEmitter,this.taskForm=this.fb.group({title:["",o.H.required],description:[""],assignedTo:["",o.H.required],createdBy:["",o.H.required],startDate:["",o.H.required],endDate:["",o.H.required],remindMe:[""],applicationName:[""],accountId:[""],applicationId:[""],governanceType:[""]}),this.todaysOrTommorow=[{startDate:null,endDate:null}],this.resetButtons=()=>{this.todaysOrTommorow[0].startDate=this.todaysOrTommorow[0].endDate=null},this.dayClicked=(t,e)=>{if(console.log(t,e),"startDate"==t){if(this.todaysOrTommorow[0].startDate=e,"today"==e)this.taskForm.patchValue({startDate:new Date});else if("tommorow"==e){const t=new Date,e=new Date(t);e.setDate(t.getDate()+1),this.taskForm.patchValue({startDate:e})}}else if("endDate"==t)if(this.todaysOrTommorow[0].endDate=e,"today"==e)this.taskForm.patchValue({endDate:new Date});else if("tommorow"==e){const t=new Date,e=new Date(t);e.setDate(e.getDate()+1),this.taskForm.patchValue({endDate:e})}},this.closeClicked=()=>{this.close.emit(),this.dialogRef.close()},this.editTask=()=>{this.taskForm.valid?this.accountService.editTask(this.activityId,this.taskForm.value).then(t=>{this.taskForm.reset(),this.snackBar.open("Task updated Successfully!","Dismiss",{duration:2e3}),this.close.emit("close"),this.dialogRef.close("Update Required"),this.activityToMainService.sendMsg("reload"),console.log(t)},t=>{this.snackBar.open("Failed to edit task.","Dismiss",{duration:2e3}),console.error(t)}):this.snackBar.open("Enter all Mandatory Fields!","Dismiss",{duration:2e3})},this.createTask=()=>{this.taskForm.valid?this.accountService.createTask(this.taskForm.value).subscribe(t=>{this.taskForm.reset(),this.snackBar.open("Task Created Successfully!","Dismiss",{duration:2e3}),this.close.emit("close"),this.dialogRef.close("Update Required"),this.activityToMainService.sendMsg("reload"),console.log(t)},t=>{this.snackBar.open("Failed to create task.","Dismiss",{duration:2e3}),console.error(t)}):this.snackBar.open("Enter all Mandatory Fields!","Dismiss",{duration:2e3})}}ngOnChanges(){}updateFormWithTaskDetails(){if(console.log(this.mode,this.activityId),"Edit"==this.mode){let t=this.data?this.data:null;t?this.taskForm.patchValue({title:t.title,description:t.description,assignedTo:t.assigned_to,createdBy:t.task_created_by,startDate:t.start_date?t.start_date:t.planned_start_date,endDate:t.end_date?t.end_date:t.task_due_date,remindMe:t.reminder,governanceType:t.governance_activity_id}):console.log("waiting for data changes")}else this.taskForm.reset()}ngOnInit(){let t;this.mode=this.dialogData.mode,this.activityId=this.dialogData.activityId,this.data=this.dialogData.data,"Edit"==this.mode&&this.updateFormWithTaskDetails(),this.currentUser=this.authService.getProfile().profile,console.log("this.currentUser",this.currentUser),this.taskForm.patchValue({createdBy:this.currentUser.oid}),"accounts"==window.location.pathname.split("/")[2]&&(t=33),this.accountService.getAccountGovernanceType(33).subscribe(t=>{this.accountGovernanceTypes=t},t=>{console.log(t)}),this.taskForm.patchValue({applicationName:window.location.pathname.split("/")[2],accountId:window.location.pathname.split("/")[3],applicationId:t})}setDate(t,e){this.taskForm.get(t).patchValue(this.getDateFn(e))}getDateFn(t){const e=new Date,n=new Date(e);return console.log(n),console.log(t),"tommorow"==t?n.setDate(n.getDate()+1):"yesterday"!=t||n.setDate(n.getDate()-1),console.log(n),n}closeDialog(){this.close.emit("close"),this.dialogRef.close()}}return t.\u0275fac=function(e){return new(e||t)(i["\u0275\u0275directiveInject"](a.h),i["\u0275\u0275directiveInject"](a.a),i["\u0275\u0275directiveInject"](a.b),i["\u0275\u0275directiveInject"](o.i),i["\u0275\u0275directiveInject"](P.a),i["\u0275\u0275directiveInject"](_.a),i["\u0275\u0275directiveInject"](M.a),i["\u0275\u0275directiveInject"](A.a),i["\u0275\u0275directiveInject"](I.a))},t.\u0275cmp=i["\u0275\u0275defineComponent"]({type:t,selectors:[["app-new-task-form"]],outputs:{close:"close"},features:[i["\u0275\u0275NgOnChangesFeature"]],decls:79,vars:35,consts:[[3,"formGroup"],[1,"container","newTaskStyles"],[1,"row","border-bottom","solid"],[1,"col-11","pt-2","createAccount"],[1,"col-1","d-flex"],["mat-icon-button","",1,"ml-auto","close-button",3,"click"],[1,"close-Icon"],[1,"row","pt-2"],[1,"col-8","pl-0","pr-0"],[1,"row","pt-1"],[1,"col-12"],["appearance","outline",1,"create-account-field","title"],["matInput","","placeholder","title *","formControlName","title"],[1,"row"],["placeholder","Account Governance Activity","formControlName","governanceType",1,"create-account-field","title",3,"list"],["appearance","outline",1,"textArea",2,"width","28rem","font-size","14px !important"],["matInput","","cdkTextareaAutosize","","cdkAutosizeMinRows","4","cdkAutosizeMaxRows","","placeholder","Description","formControlName","description"],["autosize","cdkTextareaAutosize"],["label","Assigned to","required","true","formControlName","assignedTo",1,"contact-person",3,"isAutocomplete"],["label","Created By","required","true","formControlName","createdBy",1,"contact-person",3,"isAutocomplete"],[1,"col-5"],["appearance","outline",1,"create-account-field"],["matInput","","required","true","formControlName","startDate",3,"matDatepicker"],["matSuffix","",3,"for"],["picker0",""],[1,"col-7"],[1,"row","p-0"],[1,"col-4","pl-0",2,"padding-top","14px"],["mat-raised-button","","color","primary",2,"margin-left","2%",3,"ngClass","click"],[1,"col-4",2,"padding-top","14px"],[1,"col-5","pt-2"],["matInput","","required","true","formControlName","endDate",3,"matDatepicker"],["picker1",""],[1,"col-4"],[1,"row","pt-3"],[1,"row","pt-4"],[1,"col-9","pl-4","quotes"],[1,"col-3"],["mat-icon-button","","class","iconbtn ml-2 mt-0","matTooltip","Create Task","type","submit",3,"click",4,"ngIf"],["mat-icon-button","","class","iconbtn ml-2 mt-0","matTooltip","Edit Task","type","submit",3,"click",4,"ngIf"],["mat-icon-button","","matTooltip","Create Task","type","submit",1,"iconbtn","ml-2","mt-0",3,"click"],["mat-icon-button","","matTooltip","Edit Task","type","submit",1,"iconbtn","ml-2","mt-0",3,"click"]],template:function(t,e){if(1&t&&(i["\u0275\u0275elementStart"](0,"form",0),i["\u0275\u0275elementStart"](1,"div",1),i["\u0275\u0275elementStart"](2,"div",2),i["\u0275\u0275elementStart"](3,"div",3),i["\u0275\u0275text"](4),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"div",4),i["\u0275\u0275elementStart"](6,"button",5),i["\u0275\u0275listener"]("click",(function(){return e.closeDialog()})),i["\u0275\u0275elementStart"](7,"mat-icon",6),i["\u0275\u0275text"](8,"close"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](9,"div",7),i["\u0275\u0275elementStart"](10,"div",8),i["\u0275\u0275elementStart"](11,"div",9),i["\u0275\u0275elementStart"](12,"div",10),i["\u0275\u0275elementStart"](13,"mat-form-field",11),i["\u0275\u0275elementStart"](14,"mat-label"),i["\u0275\u0275text"](15,"Title *"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](16,"input",12),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](17,"div",13),i["\u0275\u0275elementStart"](18,"div",10),i["\u0275\u0275element"](19,"app-input-search",14),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](20,"div",13),i["\u0275\u0275elementStart"](21,"div",10),i["\u0275\u0275elementStart"](22,"mat-form-field",15),i["\u0275\u0275element"](23,"textarea",16,17),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](25,"div",13),i["\u0275\u0275elementStart"](26,"div",10),i["\u0275\u0275element"](27,"app-search-user",18),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](28,"div",13),i["\u0275\u0275elementStart"](29,"div",10),i["\u0275\u0275element"](30,"app-search-user",19),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](31,"div",13),i["\u0275\u0275elementStart"](32,"div",20),i["\u0275\u0275elementStart"](33,"mat-form-field",21),i["\u0275\u0275elementStart"](34,"mat-label"),i["\u0275\u0275text"](35,"Start Date"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](36,"input",22),i["\u0275\u0275element"](37,"mat-datepicker-toggle",23),i["\u0275\u0275element"](38,"mat-datepicker",null,24),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](40,"div",25),i["\u0275\u0275elementStart"](41,"div",26),i["\u0275\u0275elementStart"](42,"div",27),i["\u0275\u0275elementStart"](43,"button",28),i["\u0275\u0275listener"]("click",(function(){return e.setDate("startDate","today")})),i["\u0275\u0275text"](44," Today"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](45,"div",27),i["\u0275\u0275elementStart"](46,"button",28),i["\u0275\u0275listener"]("click",(function(){return e.setDate("startDate","tommorow")})),i["\u0275\u0275text"](47," Tommorow"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](48,"div",29),i["\u0275\u0275elementStart"](49,"button",28),i["\u0275\u0275listener"]("click",(function(){return e.setDate("startDate","yesterday")})),i["\u0275\u0275text"](50," Yesterday"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](51,"div",13),i["\u0275\u0275elementStart"](52,"div",30),i["\u0275\u0275elementStart"](53,"mat-form-field",21),i["\u0275\u0275elementStart"](54,"mat-label"),i["\u0275\u0275text"](55,"End Date"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](56,"input",31),i["\u0275\u0275element"](57,"mat-datepicker-toggle",23),i["\u0275\u0275element"](58,"mat-datepicker",null,32),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](60,"div",25),i["\u0275\u0275elementStart"](61,"div",26),i["\u0275\u0275elementStart"](62,"div",27),i["\u0275\u0275elementStart"](63,"button",28),i["\u0275\u0275listener"]("click",(function(){return e.setDate("endDate","today")})),i["\u0275\u0275text"](64," Today"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](65,"div",27),i["\u0275\u0275elementStart"](66,"button",28),i["\u0275\u0275listener"]("click",(function(){return e.setDate("endDate","tommorow")})),i["\u0275\u0275text"](67," Tommorow"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](68,"div",29),i["\u0275\u0275elementStart"](69,"button",28),i["\u0275\u0275listener"]("click",(function(){return e.setDate("endDate","yesterday")})),i["\u0275\u0275text"](70," Yesterday"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](71,"div",33),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](72,"div",34),i["\u0275\u0275elementStart"](73,"div",35),i["\u0275\u0275elementStart"](74,"div",36),i["\u0275\u0275text"](75,' "In the Middle of difficulty, lies Opportunity" '),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](76,"div",37),i["\u0275\u0275template"](77,N,3,0,"button",38),i["\u0275\u0275template"](78,U,3,0,"button",39),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&t){const t=i["\u0275\u0275reference"](39),n=i["\u0275\u0275reference"](59);i["\u0275\u0275property"]("formGroup",e.taskForm),i["\u0275\u0275advance"](4),i["\u0275\u0275textInterpolate1"]("","Edit"==e.mode?"Edit":"Create new"," task"),i["\u0275\u0275advance"](15),i["\u0275\u0275property"]("list",e.accountGovernanceTypes),i["\u0275\u0275advance"](8),i["\u0275\u0275property"]("isAutocomplete",!0),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("isAutocomplete",!0),i["\u0275\u0275advance"](6),i["\u0275\u0275property"]("matDatepicker",t),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("for",t),i["\u0275\u0275advance"](6),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction2"](17,B,"tommorow"==e.todaysOrTommorow[0].startDate||null==e.todaysOrTommorow[0].startDate,"today"==e.todaysOrTommorow[0].startDate)),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction2"](20,B,"today"==e.todaysOrTommorow[0].startDate||null==e.todaysOrTommorow[0].startDate,"tommorow"==e.todaysOrTommorow[0].startDate)),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction2"](23,B,"today"==e.todaysOrTommorow[0].startDate||null==e.todaysOrTommorow[0].startDate,"yesterday"==e.todaysOrTommorow[0].startDate)),i["\u0275\u0275advance"](7),i["\u0275\u0275property"]("matDatepicker",n),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("for",n),i["\u0275\u0275advance"](6),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction2"](26,B,"tommorow"==e.todaysOrTommorow[0].endDate||null==e.todaysOrTommorow[0].endDate,"today"==e.todaysOrTommorow[0].endDate)),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction2"](29,B,"today"==e.todaysOrTommorow[0].endDate||null==e.todaysOrTommorow[0].endDate,"tommorow"==e.todaysOrTommorow[0].endDate)),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction2"](32,B,"today"==e.todaysOrTommorow[0].endDate||null==e.todaysOrTommorow[0].endDate,"yesterday"==e.todaysOrTommorow[0].endDate)),i["\u0275\u0275advance"](8),i["\u0275\u0275property"]("ngIf","Edit"!=e.mode),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf","Edit"==e.mode)}},directives:function(){return[o.J,o.w,o.n,T.a,k.a,m.c,m.g,S.b,o.e,o.v,o.l,v,j.b,x,o.F,F.g,F.i,m.i,F.f,r.NgClass,r.NgIf]},styles:[".newTaskStyles[_ngcontent-%COMP%]{background-image:url(createTask.757ceefab6f4ac37edbb.png);background-size:253px 200px;background-repeat:no-repeat;overflow-y:hidden!important;background-position:100% 60%;min-height:95vh}.newTaskStyles[_ngcontent-%COMP%]   .createAccount[_ngcontent-%COMP%]{font-size:14px;color:#cf0001;font-family:Roboto;font-weight:500}.newTaskStyles[_ngcontent-%COMP%]   .create-account-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%!important;padding-top:4px}.newTaskStyles[_ngcontent-%COMP%]   .contact-person[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-flex{padding:0 .75em;margin-top:-.25em;position:relative;width:28rem;height:40px}.newTaskStyles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff}.newTaskStyles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%], .newTaskStyles[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]{font-weight:400;font-size:13px!important;min-width:60px;line-height:33px;padding:0 8px;border-radius:4px;margin-right:6px!important;margin-bottom:3px}.newTaskStyles[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]{color:rgba(0,0,0,.534);background-color:rgba(194,193,193,.24)}.newTaskStyles[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]     .mat-form-field-wrapper{width:28rem}.newTaskStyles[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]     .mat-form-field-wrapper .mat-form-field-flex{height:40px}.newTaskStyles[_ngcontent-%COMP%]   .vip-contact[_ngcontent-%COMP%]{color:#1a1a1a;font-size:14px}.newTaskStyles[_ngcontent-%COMP%]   .check-box-text[_ngcontent-%COMP%], .newTaskStyles[_ngcontent-%COMP%]   .quotes[_ngcontent-%COMP%]{color:#66615b;font-size:14px}.newTaskStyles[_ngcontent-%COMP%]   .quotes[_ngcontent-%COMP%]{font-style:italic;font-weight:500}.newTaskStyles[_ngcontent-%COMP%]   .iconbtn[_ngcontent-%COMP%]{background-color:#c92020;color:#fff;padding:0;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.newTaskStyles[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.newTaskStyles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}"]}),t})()},VsNQ:function(t,e,n){"use strict";n.d(e,"a",(function(){return c}));var i=n("mrSG"),o=n("xG9w"),a=n("XNiG"),r=n("fXoL"),s=n("tk/3"),l=n("flaP");let c=(()=>{class t{constructor(t,e){this.http=t,this._roleService=e,this.contactReload=new a.b,this.isHierarchyVisible=(t,e)=>o.where(this._roleService.roles,{application_id:t,object_id:e}).length>0,this.getContactsByAccounts=t=>this.http.post("api/accounts/getContactsByAccounts",{account_id:t}),this.saveContact=t=>this.http.post("/api/contacts/createContact",{contactDetails:t}),this.deactivateAccess=()=>{let t=o.where(this._roleService.roles,{application_id:34,object_id:29370});return console.log("accessList",t),t.length>0},this.getContactGovernanceType=t=>this.http.post("/api/activity/getGovernanceActivityType",{applicationId:t}),this.getActivityFilterMasterDate=()=>this.http.post("/api/activity/activityFilterMasterData",{}),this.updateTaskDuedate=(t,e)=>this.http.post("/api/contacts/updateTaskDueDate",{activity_id:t,date:e}),this.getActivityNotes=t=>(console.log(t),this.http.post("/api/contacts/activityNotes",{activity_id:t,operation_id:2})),this.createActivityNote=(t,e,n,i)=>this.http.post("/api/contacts/activityNotes",{operation_id:1,activity_id:t,message:i,color_code:e,title:n}),this.editActivityNote=(t,e,n,i)=>this.http.post("/api/contacts/activityNotes",{activity_id:t,operation_id:3,note_id:e,message:n,title:i}),this.deleteActivityNote=(t,e)=>this.http.post("/api/contacts/activityNotes",{activity_id:t,operation_id:4,note_id:e}),this.getUserProfileFromContacts=t=>{try{return new Promise((e,n)=>{this.http.post("/api/contacts/getContactInfo",{oid:t}).subscribe(t=>e(t),t=>(console.log(t),n(t)))})}catch(e){return Promise.reject()}},this.updateTaskDetailInline=(t,e)=>this.http.post("/api/contacts/editTaskInline",{taskFormDetails:e,activityId:t}),this.saveImage=(t,e)=>this.http.post("/api/contacts/saveImage",{result:t,contactId:e}),this.getContactsList=(t,e)=>this.http.post("/api/contacts/getContactsList",{org_codes:e,filterConfig:t}),this.getTotalContacts=(t,e)=>new Promise((n,i)=>{this.http.post("/api/contacts/getTotalContacts",{org_codes:e,filterConfig:t}).subscribe(t=>n(t),t=>i(t))}),this.getContactPersona=()=>new Promise((t,e)=>{this.http.post("/api/contacts/getReportFilterPersonainContact",{}).subscribe(e=>t(e),t=>e(t))}),this.getAccessTokenInfo=()=>this.http.post("/api/collector/getTokenConfigDetails",{}),this.getMailConfigFlag=()=>this.http.post("/api/contacts/getMailConfigFlag",{}),this.getLabelForOpportunity=(t,e)=>{let n=o.where(this._roleService.label,{application_id:t,id:e});return n.length>0?n[0].label_name:""},this.getUDRFContactConfig=()=>new Promise((t,e)=>{this.http.post("/api/contacts/getUDRFContactConfig",{}).subscribe(e=>{t(e)},t=>{e(t)})}),this.getContactHierarchyData=(t,e)=>this.http.post("/api/contacts/getContactHierarchyData",{application_reference_id:t,application_id:e}),this.downloadContactsList=(t,e)=>this.http.post("/api/contacts/downloadContactsList",{org_codes:e,filterConfig:t}),this.checkIfAccountExist=t=>this.http.post("/api/accounts/checkIfAccountExist",{accountId:t})}getAllAccounts(t){return new Promise((e,n)=>{this.http.post("/api/contacts/getAllAccounts",{orgCodes:t}).subscribe(t=>e(t),t=>n(t))})}getContactsByAccountIds(t){return new Promise((e,n)=>{this.http.post("/api/contacts/contactsByAccountIds",{orgCodes:t}).subscribe(t=>e(t),t=>n(t))})}getFilterMaster(){return this.http.get("/api/contacts/masterDataForContactFilter")}getFilterData(t,e){return this.http.post("/api/contacts/ContactsFilter",{filterData:t,orgCodes:e})}getUserProfileFromDB(t){return Object(i.c)(this,void 0,void 0,(function*(){try{return new Promise((e,n)=>{this.http.post("/api/project/getUserProfileFromDB",{oid:t}).subscribe(t=>e(t),t=>(console.log(t),n(t)))})}catch(e){return Promise.reject()}}))}removeMember(t){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,n)=>{this.http.post("/api/project/removeMember",{stakeholder_table_id:t}).subscribe(t=>e(t))})}))}setContactVipStatus(t,e){return this.http.post("/api/contacts/AddOrRemoveContactFav",{flag:t,contact_id:e})}getContacts(t){return this.http.post("/api/contacts/allActiveContacts",{orgCodes:t})}updateContactById(t,e){return this.http.post("/api/contacts/updateContact",{contact_id:t,contact_details:e})}performAddNotesOperation(t,e,n,i){return this.http.post("/api/contacts/noteOperationsContacts",{contact_id:t,operation_id:1,message:e,title:n,color_code:i})}performGetNotes(t){return this.http.post("/api/contacts/noteOperationsContacts",{contact_id:t,operation_id:2})}performEditNotes(t,e,n,i,o){return this.http.post("/api/contacts/noteOperationsContacts",{operation_id:3,note_id:t,message:e,title:n,color_code:i,contact_id:o})}performDeleteNotes(t,e){return this.http.post("/api/contacts/noteOperationsContacts",{operation_id:4,contact_id:t,note_id:e})}getContactsDetailsById(t){return this.http.post("/api/contacts/getContactDetailsById",{contact_id:t})}getContactsOverview(t){return this.http.post("api/contacts/getContactDetailsById",{contact_id:t})}personaMaster(){return new Promise((t,e)=>{this.http.post("/api/salesMaster/contactPersona",{}).subscribe(e=>t(e),t=>e(t))})}getContactAttachments(t){return this.http.post("/api/contacts/getContactAttachments",{contact_id:t})}getCRMAttachmentsConfigs(){return new Promise((t,e)=>{this.http.get("/api/salesMaster/getCRMAttachmentsConfigs").subscribe(e=>t(e),t=>e(t))})}updateContactAttachment(t,e){return this.http.post("/api/contacts/updateContactAttachment",{contact_id:t,file:e})}deleteContactAttachment(t,e){return this.http.post("/api/contacts/deleteContactAttachment",{contact_id:t,file:e})}searchContact(t,e){return this.http.post("/api/contacts/ContactsSearch",{search_parameter:t,orgCodes:e})}deleteContact(t){return this.http.post("/api/contacts/deactivateContact",{contact_id:t})}responseType(){return new Promise((t,e)=>{this.http.post("/api/salesMaster/getResponseMaster",{}).subscribe(e=>t(e),t=>e(t))})}contactType(){return new Promise((t,e)=>{this.http.post("/api/salesMaster/getContactType",{}).subscribe(e=>t(e),t=>e(t))})}editCallLog(t,e){return new Promise((n,i)=>{this.http.post("/api/contacts/editCallLog",{activity_id:t,callLogFormDetails:e}).subscribe(t=>n(t),t=>i(t))})}createCallLog(t){return this.http.post("/api/contacts/createCallLog",{callLogFormDetails:t})}editMail(t,e){return new Promise((n,i)=>{this.http.post("/api/contacts/editMail",{activity_id:t,mailFormDetails:e}).subscribe(t=>n(t),t=>i(t))})}createMail(t){return this.http.post("/api/contacts/createMail",{mailFormDetails:t})}meetingTypeList(){return new Promise((t,e)=>{this.http.post("/api/salesMaster/getSalesActivityLocationType",{}).subscribe(e=>t(e),t=>e(t))})}locationList(){return new Promise((t,e)=>{this.http.post("/api/salesMaster/getActivityLocations",{}).subscribe(e=>t(e),t=>e(t))})}peopleList(){return new Promise((t,e)=>{this.http.post("/api/salesMaster/peopleList",{}).subscribe(e=>t(e),t=>e(t))})}editMeeting(t,e){return new Promise((n,i)=>{this.http.post("/api/contacts/editMeeting",{activity_id:t,meetingFormDetails:e}).subscribe(t=>n(t),t=>i(t))})}createMeeting(t){return this.http.post("/api/contacts/createMeeting",{meetingFormDetails:t})}editTask(t,e){return new Promise((n,i)=>{this.http.post("/api/contacts/editTask",{activity_id:t,taskFormDetails:e}).subscribe(t=>n(t),t=>i(t))})}createTask(t){return this.http.post("/api/contacts/createTask",{taskFormDetails:t})}activityList(t,e){return new Promise((n,i)=>{this.http.post("/api/contacts/getActivityList",{application_id:t,contact_id:e}).subscribe(t=>n(t),t=>i(t))})}searchContactActivity(t,e){return new Promise((n,i)=>{this.http.post("/api/contacts/searchContactActivity",{search_parameter:t,contact_id:e}).subscribe(t=>n(t),t=>i(t))})}getFullDetailOfActivity(t){return this.http.post("/api/contacts/getActivityDetail",{activity_id:t})}openActivity(t){return new Promise((e,n)=>{this.http.post("/api/contacts/openActivity",{activity_id:t}).subscribe(t=>e(t),t=>n(t))})}activityDetails(t){return new Promise((e,n)=>{this.http.post("/api/contacts/getActivityDetails",{activity_id:t}).subscribe(t=>(console.log("activitydetail",t),e(t)),t=>n(t))})}completeActivity(t,e){return new Promise((n,i)=>{this.http.post("/api/contacts/completeActivity",{activity_id:t,is_completed:e}).subscribe(t=>n(t),t=>i(t))})}startActivity(t){return new Promise((e,n)=>{this.http.post("/api/contacts/startActivity",{activity_id:t}).subscribe(t=>e(t),t=>n(t))})}taskFormDetails(t){return new Promise((e,n)=>{this.http.post("/api/activity/taskFormDetails",{activity_id:t}).subscribe(t=>e(t),t=>n(t))})}meetingFormDetails(t){return new Promise((e,n)=>{this.http.post("/api/activity/meetingFormDetails",{activity_id:t}).subscribe(t=>e(t),t=>n(t))})}callLogFormDetails(t){return new Promise((e,n)=>{this.http.post("/api/activity/callLogFormDetails",{activity_id:t}).subscribe(t=>e(t),t=>n(t))})}mailFormDetails(t){return new Promise((e,n)=>{this.http.post("/api/activity/mailFormDetails",{activity_id:t}).subscribe(t=>e(t),t=>n(t))})}deleteActivity(t){return new Promise((e,n)=>{this.http.post("/api/contacts/deleteActivity",{activity_id:t}).subscribe(t=>e(t),t=>n(t))})}updateActivityAssignedTo(t,e){return this.http.post("/api/contacts/editActivityAssignedTo",{activityId:t,oid:e})}updatePlannedHours(t,e){return this.http.post("api/contacts/updatePlannedHrsForContactAct",{activity_id:t,planned_hours:e})}updateJobTitle(t,e){return this.http.post("/api/contacts/updateJobTitle",{id:t,jobTitle:e})}updateEmail(t,e){return this.http.post("/api/contacts/updateEmail",{id:t,email:e})}updateMobileNumber(t,e){return this.http.post("/api/contacts/updateMobileNumber",{id:t,mobileNumber:e})}updatePersona(t,e){return this.http.post("/api/contacts/updatePersona",{id:t,personaId:e})}updateContactOwner(t,e,n){return this.http.post("/api/contacts/updateContactOwner",{id:t,name:e,ownerOid:n})}updateLinkedinProfile(t,e){return this.http.post("/api/contacts/updateLinkedinProfile",{id:t,linkedinProfile:e})}updateContactAddress(t,e){return this.http.post("/api/contacts/updateContactAddress",{id:t,address:e})}marketSegment(){return new Promise((t,e)=>{this.http.post("/api/contacts/getMarketSegment",{}).subscribe(e=>t(e),t=>e(t))})}updateMarketSegment(t,e){return this.http.post("/api/contacts/updateMarketSegment",{id:t,market_segment:e})}setContactDunningEmail(t,e){return this.http.post("/api/contacts/setContactAsDunning",{flag:t,contact_id:e})}getFormFieldCollection(){return new Promise((t,e)=>{this.http.post("/api/contacts/getFormFieldCollection",{}).subscribe(e=>{t(e)},t=>{e(t)})})}}return t.\u0275fac=function(e){return new(e||t)(r["\u0275\u0275inject"](s.c),r["\u0275\u0275inject"](l.a))},t.\u0275prov=r["\u0275\u0275defineInjectable"]({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()},bwhV:function(t,e,n){"use strict";n.r(e),n.d(e,"NewTaskFormComponent",(function(){return q}));var i=n("fXoL"),o=n("3Pt+"),a=n("0IaG"),r=n("ofXK"),s=n("jtHE"),l=n("XNiG"),c=n("NJ67"),d=n("1G5W"),m=n("kmnG"),p=n("d3UM"),u=n("FKr1"),h=n("WJ5W");const g=["singleSelect"];function f(t,e){if(1&t){const t=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"mat-option",6),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](t);const n=e.$implicit;return i["\u0275\u0275nextContext"]().emitChanges(n)})),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()}if(2&t){const t=e.$implicit;i["\u0275\u0275property"]("value",t.id),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" ",t.name," ")}}let v=(()=>{class t extends c.a{constructor(t){super(),this.renderer=t,this.fieldCtrl=new o.j,this.fieldFilterCtrl=new o.j,this.list=[],this.required=!1,this.valueChange=new i.EventEmitter,this.disabled=!1,this.filteredList=new s.a,this.change=new i.EventEmitter,this._onDestroy=new l.b,this.emitChanges=t=>{this.change.emit(t)}}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(d.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(d.a)(this._onDestroy)).subscribe(t=>{this.value=t,this.onChange(t),this.valueChange.emit(t)})}ngOnChanges(){null!=this.list&&this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let t=this.fieldFilterCtrl.value;t?(t=t.toLowerCase(),this.filteredList.next(this.list.filter(e=>e.name.toLowerCase().indexOf(t)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(t){t?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(t){this.fieldCtrl.setValue(t)}}return t.\u0275fac=function(e){return new(e||t)(i["\u0275\u0275directiveInject"](i.Renderer2))},t.\u0275cmp=i["\u0275\u0275defineComponent"]({type:t,selectors:[["app-input-search"]],viewQuery:function(t,e){if(1&t&&i["\u0275\u0275viewQuery"](g,!0),2&t){let t;i["\u0275\u0275queryRefresh"](t=i["\u0275\u0275loadQuery"]())&&(e.singleSelect=t.first)}},inputs:{list:"list",placeholder:"placeholder",required:"required",disabled:"disabled"},outputs:{valueChange:"valueChange",change:"change"},features:[i["\u0275\u0275ProvidersFeature"]([{provide:o.t,useExisting:Object(i.forwardRef)(()=>t),multi:!0}]),i["\u0275\u0275InheritDefinitionFeature"],i["\u0275\u0275NgOnChangesFeature"]],decls:11,vars:11,consts:[["appearance","outline"],[3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value"],[3,"value","click",4,"ngFor","ngForOf"],[3,"value","click"]],template:function(t,e){1&t&&(i["\u0275\u0275elementStart"](0,"mat-form-field",0),i["\u0275\u0275elementStart"](1,"mat-label"),i["\u0275\u0275text"](2),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](3,"mat-select",1,2),i["\u0275\u0275elementStart"](5,"mat-option"),i["\u0275\u0275element"](6,"ngx-mat-select-search",3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](7,"mat-option",4),i["\u0275\u0275text"](8,"None"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](9,f,2,2,"mat-option",5),i["\u0275\u0275pipe"](10,"async"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&t&&(i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate"](e.placeholder),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("formControl",e.fieldCtrl)("placeholder",e.placeholder)("required",e.required)("disabled",e.disabled),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("formControl",e.fieldFilterCtrl)("placeholderLabel",e.placeholder),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("value",null),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngForOf",i["\u0275\u0275pipeBind1"](10,9,e.filteredList)))},directives:[m.c,m.g,p.c,o.v,o.k,o.F,u.p,h.a,r.NgForOf],pipes:[r.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}"]}),t})();var y=n("mrSG"),b=n("Kj3r"),w=n("F97M"),C=n("XVR1"),S=n("qFsG"),D=n("/1cH"),k=n("NFeN");function O(t,e){if(1&t&&(i["\u0275\u0275elementStart"](0,"mat-label"),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&t){const t=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](t.label)}}function E(t,e){if(1&t&&(i["\u0275\u0275elementStart"](0,"mat-option",7),i["\u0275\u0275elementStart"](1,"small"),i["\u0275\u0275text"](2),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit,n=i["\u0275\u0275nextContext"]();i["\u0275\u0275property"]("hidden",!n.isAutocomplete)("value",t),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate2"]("",t.displayName," | ",t.mail,"")}}let x=(()=>{class t extends c.a{constructor(t,e){super(),this.graphApi=t,this._AppShareService=e,this.userSearchSubject=new l.b,this.isAutocomplete=!1,this.Formwidth="",this.showUserList=new i.EventEmitter,this.selectedUser=new i.EventEmitter,this.label="",this.blur=new i.EventEmitter,this.required=!1,this.fieldCtrl=new o.j,this.disabled=!1,this.isGraphApi=0,this._onDestroy=new l.b}ngOnInit(){this.userSearchSubject.pipe(Object(b.a)(600)).subscribe(t=>Object(y.c)(this,void 0,void 0,(function*(){if(console.log(t),this.isGraphApi)this.graphApi.getUserSuggestions(t).then(e=>Object(y.c)(this,void 0,void 0,(function*(){if(e&&e.length>0)this.showUserList.emit(e);else{let e=yield this.getUserSuggestionsFromDB(t);this.showUserList.emit(e)}})),e=>Object(y.c)(this,void 0,void 0,(function*(){console.log(e);let n=yield this.getUserSuggestionsFromDB(t);this.showUserList.emit(n)})));else{let e=yield this.getUserSuggestionsFromDB(t);this.showUserList.emit(e)}}))),this.fieldCtrl.valueChanges.pipe(Object(d.a)(this._onDestroy)).subscribe(t=>{console.log(t),t?(this.value=t.id,this.onChange(t.id)):(this.value="",this.onChange(""))})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}ngAfterViewInit(){}searchUser(t){if(!t)return this.graphApi.userSuggestions=[];this.userSearchSubject.next(t)}resetSuggestion(){this.graphApi.userSuggestions=[]}selectedOption(t){this.selectedUser.emit(t.option.value),this.value=t.option.value,this.blur.emit()}displayUserName(t){return t?t.displayName:""}setDisabledState(t){t?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(t){return Object(y.c)(this,void 0,void 0,(function*(){if(console.log("inisde writevalue"),console.log(t),t)if(this.isGraphApi)this.graphApi.getUserProfile(t).then(e=>Object(y.c)(this,void 0,void 0,(function*(){if(e)this.fieldCtrl.setValue(e);else{let e=yield this.getUserProfileFromDB(t);this.fieldCtrl.setValue(e)}})));else{let e=yield this.getUserProfileFromDB(t);console.log(e),this.fieldCtrl.setValue(e)}else this.fieldCtrl.setValue("")}))}getUserSuggestionsFromDB(t){return Object(y.c)(this,void 0,void 0,(function*(){let e=yield this._AppShareService.getUserSuggestionsFromDB(t);return this.graphApi.userSuggestions=e.value,e.value}))}getUserProfileFromDB(t){return Object(y.c)(this,void 0,void 0,(function*(){let e=yield this._AppShareService.getUserProfileFromDB(t);return console.log(e),e&&e.length>0?e[0]:""}))}}return t.\u0275fac=function(e){return new(e||t)(i["\u0275\u0275directiveInject"](w.a),i["\u0275\u0275directiveInject"](C.a))},t.\u0275cmp=i["\u0275\u0275defineComponent"]({type:t,selectors:[["app-search-user"]],inputs:{isAutocomplete:"isAutocomplete",Formwidth:"Formwidth",label:"label",required:"required",disabled:"disabled",isGraphApi:"isGraphApi"},outputs:{showUserList:"showUserList",selectedUser:"selectedUser",blur:"blur"},features:[i["\u0275\u0275ProvidersFeature"]([{provide:o.t,useExisting:Object(i.forwardRef)(()=>t),multi:!0}]),i["\u0275\u0275InheritDefinitionFeature"]],decls:9,vars:9,consts:[["appearance","outline","width","Formwidth"],[4,"ngIf"],["matInput","",3,"matAutocomplete","placeholder","required","formControl","disabled","keyup","focus"],["matSuffix","",2,"color","#66615B !important","font-size","21px !important"],[3,"hidden","displayWith","optionSelected"],["auto","matAutocomplete"],[3,"hidden","value",4,"ngFor","ngForOf"],[3,"hidden","value"]],template:function(t,e){if(1&t&&(i["\u0275\u0275elementStart"](0,"div"),i["\u0275\u0275elementStart"](1,"mat-form-field",0),i["\u0275\u0275template"](2,O,2,1,"mat-label",1),i["\u0275\u0275elementStart"](3,"input",2),i["\u0275\u0275listener"]("keyup",(function(t){return e.searchUser(t.target.value)}))("focus",(function(){return e.resetSuggestion()})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"mat-icon",3),i["\u0275\u0275text"](5,"person_pin"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](6,"mat-autocomplete",4,5),i["\u0275\u0275listener"]("optionSelected",(function(t){return e.selectedOption(t)})),i["\u0275\u0275template"](8,E,3,4,"mat-option",6),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&t){const t=i["\u0275\u0275reference"](7);i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf",e.label),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("matAutocomplete",t)("placeholder",e.label)("required",e.required)("formControl",e.fieldCtrl)("disabled",e.disabled),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("hidden",!e.isAutocomplete)("displayWith",e.displayUserName),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngForOf",e.graphApi.userSuggestions)}},directives:[m.c,r.NgIf,S.b,D.d,o.e,o.F,o.v,o.k,k.a,m.i,D.b,r.NgForOf,m.g,u.p],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding-bottom:8px!important}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-underline{bottom:0}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-infix{padding:.65em 0!important;border-top:.54375em solid transparent!important;font-size:13px}.mat-form-field[_ngcontent-%COMP%]     .mat-select-arrow{margin:0 4px!important}"]}),t})();var _=n("dNgK"),T=n("bTqV"),F=n("iadO"),P=n("9jeV"),M=n("VsNQ"),A=n("tyNb"),I=n("XXEo"),j=n("ihCf");function N(t,e){if(1&t){const t=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",40),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](t),i["\u0275\u0275nextContext"]().createTask()})),i["\u0275\u0275elementStart"](1,"mat-icon"),i["\u0275\u0275text"](2," done_all"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}}function U(t,e){if(1&t){const t=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",41),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](t),i["\u0275\u0275nextContext"]().editTask()})),i["\u0275\u0275elementStart"](1,"mat-icon"),i["\u0275\u0275text"](2," done_all"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}}const B=function(t,e){return{"btn-not-active":t,"btn-active":e}};let q=(()=>{class t{constructor(t,e,n,a,r,s,l,c,d){this.dialogRef=t,this.dialogData=e,this.dialog=n,this.fb=a,this.activityToMainService=r,this.snackBar=s,this.contactService=l,this.route=c,this.authService=d,this.close=new i.EventEmitter,this.taskForm=this.fb.group({title:["",o.H.required],description:[""],assignedTo:["",o.H.required],createdBy:["",o.H.required],startDate:["",o.H.required],endDate:["",o.H.required],remindMe:[""],applicationName:[""],contactId:[""],applicationId:[""],governanceType:[""]}),this.todaysOrTommorow=[{startDate:null,endDate:null}],this.resetButtons=()=>{this.todaysOrTommorow[0].startDate=this.todaysOrTommorow[0].endDate=null},this.dayClicked=(t,e)=>{if(console.log(t,e),"startDate"==t){if(this.todaysOrTommorow[0].startDate=e,"today"==e)this.taskForm.patchValue({startDate:new Date});else if("tommorow"==e){const t=new Date,e=new Date(t);e.setDate(t.getDate()+1),this.taskForm.patchValue({startDate:e})}}else if("endDate"==t)if(this.todaysOrTommorow[0].endDate=e,"today"==e)this.taskForm.patchValue({endDate:new Date});else if("tommorow"==e){const t=new Date,e=new Date(t);e.setDate(e.getDate()+1),this.taskForm.patchValue({endDate:e})}},this.closeClicked=()=>{this.close.emit(),this.dialogRef.close()},this.editTask=()=>{this.taskForm.valid?this.contactService.editTask(this.activityId,this.taskForm.value).then(t=>{this.taskForm.reset(),this.snackBar.open("Task updated Successfully!","Dismiss",{duration:2e3}),this.close.emit("close"),this.dialogRef.close("Update Required"),this.activityToMainService.sendMsg("reload"),console.log(t)},t=>{this.snackBar.open("Failed to edit task.","Dismiss",{duration:2e3}),console.error(t)}):this.snackBar.open("Enter all Mandatory Fields!","Dismiss",{duration:2e3})},this.createTask=()=>{this.taskForm.valid?this.contactService.createTask(this.taskForm.value).subscribe(t=>{this.taskForm.reset(),this.snackBar.open("Task Created Successfully!","Dismiss",{duration:2e3}),this.close.emit("close"),this.dialogRef.close("Update Required"),this.activityToMainService.sendMsg("reload"),console.log(t)},t=>{this.snackBar.open("Failed to create task.","Dismiss",{duration:2e3}),console.error(t)}):this.snackBar.open("Enter all Mandatory Fields!","Dismiss",{duration:2e3})}}ngOnChanges(){}updateFormWithTaskDetails(){if(console.log(this.mode,this.activityId),"Edit"==this.mode){let t=this.data?this.data:null;t?this.taskForm.patchValue({title:t.title,description:t.description,assignedTo:t.assigned_to,createdBy:t.task_created_by,startDate:t.start_date?t.start_date:t.planned_start_date,endDate:t.end_date?t.end_date:t.task_due_date,remindMe:t.reminder,governanceType:t.governance_activity_id}):console.log("waiting for data changes")}else this.taskForm.reset()}ngOnInit(){let t;this.mode=this.dialogData.mode,this.activityId=this.dialogData.activityId,this.data=this.dialogData.data,"Edit"==this.mode&&this.updateFormWithTaskDetails(),this.currentUser=this.authService.getProfile().profile,console.log("this.currentUser",this.currentUser),this.taskForm.patchValue({createdBy:this.currentUser.oid}),"contacts"==window.location.pathname.split("/")[2]&&(t=34),this.contactService.getContactGovernanceType(34).subscribe(t=>{this.contactGovernanceTypes=t},t=>{console.log(t)}),this.taskForm.patchValue({applicationName:window.location.pathname.split("/")[2],contactId:window.location.pathname.split("/")[3],applicationId:t})}setDate(t,e){this.taskForm.get(t).patchValue(this.getDateFn(e))}getDateFn(t){const e=new Date,n=new Date(e);return console.log(n),console.log(t),"tommorow"==t?n.setDate(n.getDate()+1):"yesterday"!=t||n.setDate(n.getDate()-1),console.log(n),n}closeDialog(){this.close.emit("close"),this.dialogRef.close()}}return t.\u0275fac=function(e){return new(e||t)(i["\u0275\u0275directiveInject"](a.h),i["\u0275\u0275directiveInject"](a.a),i["\u0275\u0275directiveInject"](a.b),i["\u0275\u0275directiveInject"](o.i),i["\u0275\u0275directiveInject"](P.a),i["\u0275\u0275directiveInject"](_.a),i["\u0275\u0275directiveInject"](M.a),i["\u0275\u0275directiveInject"](A.a),i["\u0275\u0275directiveInject"](I.a))},t.\u0275cmp=i["\u0275\u0275defineComponent"]({type:t,selectors:[["app-new-task-form"]],outputs:{close:"close"},features:[i["\u0275\u0275NgOnChangesFeature"]],decls:79,vars:35,consts:[[3,"formGroup"],[1,"container","newTaskStyles"],[1,"row","border-bottom","solid"],[1,"col-11","pt-2","createAccount"],[1,"col-1","d-flex"],["mat-icon-button","",1,"ml-auto","close-button",3,"click"],[1,"close-Icon"],[1,"row","pt-2"],[1,"col-8","pl-0","pr-0"],[1,"row","pt-1"],[1,"col-12"],["appearance","outline",1,"create-account-field","title"],["matInput","","placeholder","title *","formControlName","title"],[1,"row"],["placeholder","Contact Governance Activity","formControlName","governanceType",1,"create-account-field","title",3,"list"],["appearance","outline",1,"textArea",2,"width","28rem","font-size","14px !important"],["matInput","","cdkTextareaAutosize","","cdkAutosizeMinRows","4","cdkAutosizeMaxRows","","placeholder","Description","formControlName","description"],["autosize","cdkTextareaAutosize"],["label","Assigned to","required","true","formControlName","assignedTo",1,"contact-person",3,"isAutocomplete"],["label","Created By","required","true","formControlName","createdBy",1,"contact-person",3,"isAutocomplete"],[1,"col-5"],["appearance","outline",1,"create-account-field"],["matInput","","required","true","formControlName","startDate",3,"matDatepicker"],["matSuffix","",3,"for"],["picker0",""],[1,"col-7"],[1,"row","p-0"],[1,"col-4","pl-0",2,"padding-top","14px"],["mat-raised-button","","color","primary",2,"margin-left","2%",3,"ngClass","click"],[1,"col-4",2,"padding-top","14px"],[1,"col-5","pt-2"],["matInput","","required","true","formControlName","endDate",3,"matDatepicker"],["picker1",""],[1,"col-4"],[1,"row","pt-3"],[1,"row","pt-4"],[1,"col-9","pl-4","quotes"],[1,"col-3"],["mat-icon-button","","class","iconbtn ml-2 mt-0","matTooltip","Create Task","type","submit",3,"click",4,"ngIf"],["mat-icon-button","","class","iconbtn ml-2 mt-0","matTooltip","Edit Task","type","submit",3,"click",4,"ngIf"],["mat-icon-button","","matTooltip","Create Task","type","submit",1,"iconbtn","ml-2","mt-0",3,"click"],["mat-icon-button","","matTooltip","Edit Task","type","submit",1,"iconbtn","ml-2","mt-0",3,"click"]],template:function(t,e){if(1&t&&(i["\u0275\u0275elementStart"](0,"form",0),i["\u0275\u0275elementStart"](1,"div",1),i["\u0275\u0275elementStart"](2,"div",2),i["\u0275\u0275elementStart"](3,"div",3),i["\u0275\u0275text"](4),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"div",4),i["\u0275\u0275elementStart"](6,"button",5),i["\u0275\u0275listener"]("click",(function(){return e.closeDialog()})),i["\u0275\u0275elementStart"](7,"mat-icon",6),i["\u0275\u0275text"](8,"close"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](9,"div",7),i["\u0275\u0275elementStart"](10,"div",8),i["\u0275\u0275elementStart"](11,"div",9),i["\u0275\u0275elementStart"](12,"div",10),i["\u0275\u0275elementStart"](13,"mat-form-field",11),i["\u0275\u0275elementStart"](14,"mat-label"),i["\u0275\u0275text"](15,"Title *"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](16,"input",12),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](17,"div",13),i["\u0275\u0275elementStart"](18,"div",10),i["\u0275\u0275element"](19,"app-input-search",14),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](20,"div",13),i["\u0275\u0275elementStart"](21,"div",10),i["\u0275\u0275elementStart"](22,"mat-form-field",15),i["\u0275\u0275element"](23,"textarea",16,17),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](25,"div",13),i["\u0275\u0275elementStart"](26,"div",10),i["\u0275\u0275element"](27,"app-search-user",18),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](28,"div",13),i["\u0275\u0275elementStart"](29,"div",10),i["\u0275\u0275element"](30,"app-search-user",19),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](31,"div",13),i["\u0275\u0275elementStart"](32,"div",20),i["\u0275\u0275elementStart"](33,"mat-form-field",21),i["\u0275\u0275elementStart"](34,"mat-label"),i["\u0275\u0275text"](35,"Start Date"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](36,"input",22),i["\u0275\u0275element"](37,"mat-datepicker-toggle",23),i["\u0275\u0275element"](38,"mat-datepicker",null,24),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](40,"div",25),i["\u0275\u0275elementStart"](41,"div",26),i["\u0275\u0275elementStart"](42,"div",27),i["\u0275\u0275elementStart"](43,"button",28),i["\u0275\u0275listener"]("click",(function(){return e.setDate("startDate","today")})),i["\u0275\u0275text"](44," Today"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](45,"div",27),i["\u0275\u0275elementStart"](46,"button",28),i["\u0275\u0275listener"]("click",(function(){return e.setDate("startDate","tommorow")})),i["\u0275\u0275text"](47," Tommorow"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](48,"div",29),i["\u0275\u0275elementStart"](49,"button",28),i["\u0275\u0275listener"]("click",(function(){return e.setDate("startDate","yesterday")})),i["\u0275\u0275text"](50," Yesterday"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](51,"div",13),i["\u0275\u0275elementStart"](52,"div",30),i["\u0275\u0275elementStart"](53,"mat-form-field",21),i["\u0275\u0275elementStart"](54,"mat-label"),i["\u0275\u0275text"](55,"End Date"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](56,"input",31),i["\u0275\u0275element"](57,"mat-datepicker-toggle",23),i["\u0275\u0275element"](58,"mat-datepicker",null,32),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](60,"div",25),i["\u0275\u0275elementStart"](61,"div",26),i["\u0275\u0275elementStart"](62,"div",27),i["\u0275\u0275elementStart"](63,"button",28),i["\u0275\u0275listener"]("click",(function(){return e.setDate("endDate","today")})),i["\u0275\u0275text"](64," Today"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](65,"div",27),i["\u0275\u0275elementStart"](66,"button",28),i["\u0275\u0275listener"]("click",(function(){return e.setDate("endDate","tommorow")})),i["\u0275\u0275text"](67," Tommorow"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](68,"div",29),i["\u0275\u0275elementStart"](69,"button",28),i["\u0275\u0275listener"]("click",(function(){return e.setDate("endDate","yesterday")})),i["\u0275\u0275text"](70," Yesterday"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](71,"div",33),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](72,"div",34),i["\u0275\u0275elementStart"](73,"div",35),i["\u0275\u0275elementStart"](74,"div",36),i["\u0275\u0275text"](75,' "In the Middle of difficulty, lies Opportunity" '),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](76,"div",37),i["\u0275\u0275template"](77,N,3,0,"button",38),i["\u0275\u0275template"](78,U,3,0,"button",39),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&t){const t=i["\u0275\u0275reference"](39),n=i["\u0275\u0275reference"](59);i["\u0275\u0275property"]("formGroup",e.taskForm),i["\u0275\u0275advance"](4),i["\u0275\u0275textInterpolate1"]("","Edit"==e.mode?"Edit":"Create new"," task"),i["\u0275\u0275advance"](15),i["\u0275\u0275property"]("list",e.contactGovernanceTypes),i["\u0275\u0275advance"](8),i["\u0275\u0275property"]("isAutocomplete",!0),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("isAutocomplete",!0),i["\u0275\u0275advance"](6),i["\u0275\u0275property"]("matDatepicker",t),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("for",t),i["\u0275\u0275advance"](6),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction2"](17,B,"tommorow"==e.todaysOrTommorow[0].startDate||null==e.todaysOrTommorow[0].startDate,"today"==e.todaysOrTommorow[0].startDate)),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction2"](20,B,"today"==e.todaysOrTommorow[0].startDate||null==e.todaysOrTommorow[0].startDate,"tommorow"==e.todaysOrTommorow[0].startDate)),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction2"](23,B,"today"==e.todaysOrTommorow[0].startDate||null==e.todaysOrTommorow[0].startDate,"yesterday"==e.todaysOrTommorow[0].startDate)),i["\u0275\u0275advance"](7),i["\u0275\u0275property"]("matDatepicker",n),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("for",n),i["\u0275\u0275advance"](6),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction2"](26,B,"tommorow"==e.todaysOrTommorow[0].endDate||null==e.todaysOrTommorow[0].endDate,"today"==e.todaysOrTommorow[0].endDate)),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction2"](29,B,"today"==e.todaysOrTommorow[0].endDate||null==e.todaysOrTommorow[0].endDate,"tommorow"==e.todaysOrTommorow[0].endDate)),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction2"](32,B,"today"==e.todaysOrTommorow[0].endDate||null==e.todaysOrTommorow[0].endDate,"yesterday"==e.todaysOrTommorow[0].endDate)),i["\u0275\u0275advance"](8),i["\u0275\u0275property"]("ngIf","Edit"!=e.mode),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf","Edit"==e.mode)}},directives:function(){return[o.J,o.w,o.n,T.a,k.a,m.c,m.g,S.b,o.e,o.v,o.l,v,j.b,x,o.F,F.g,F.i,m.i,F.f,r.NgClass,r.NgIf]},styles:[".newTaskStyles[_ngcontent-%COMP%]{background-image:url(createTask.757ceefab6f4ac37edbb.png);background-size:253px 200px;background-repeat:no-repeat;overflow-y:hidden!important;background-position:100% 60%;min-height:95vh}.newTaskStyles[_ngcontent-%COMP%]   .createAccount[_ngcontent-%COMP%]{font-size:14px;color:#cf0001;font-family:Roboto;font-weight:500}.newTaskStyles[_ngcontent-%COMP%]   .create-account-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%!important;padding-top:4px}.newTaskStyles[_ngcontent-%COMP%]   .contact-person[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-flex{padding:0 .75em;margin-top:-.25em;position:relative;width:28rem;height:40px}.newTaskStyles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff}.newTaskStyles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%], .newTaskStyles[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]{font-weight:400;font-size:13px!important;min-width:60px;line-height:33px;padding:0 8px;border-radius:4px;margin-right:6px!important;margin-bottom:3px}.newTaskStyles[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]{color:rgba(0,0,0,.534);background-color:rgba(194,193,193,.24)}.newTaskStyles[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]     .mat-form-field-wrapper{width:28rem}.newTaskStyles[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]     .mat-form-field-wrapper .mat-form-field-flex{height:40px}.newTaskStyles[_ngcontent-%COMP%]   .vip-contact[_ngcontent-%COMP%]{color:#1a1a1a;font-size:14px}.newTaskStyles[_ngcontent-%COMP%]   .check-box-text[_ngcontent-%COMP%], .newTaskStyles[_ngcontent-%COMP%]   .quotes[_ngcontent-%COMP%]{color:#66615b;font-size:14px}.newTaskStyles[_ngcontent-%COMP%]   .quotes[_ngcontent-%COMP%]{font-style:italic;font-weight:500}.newTaskStyles[_ngcontent-%COMP%]   .iconbtn[_ngcontent-%COMP%]{background-color:#c92020;color:#fff;padding:0;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.newTaskStyles[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.newTaskStyles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}"]}),t})()}}]);