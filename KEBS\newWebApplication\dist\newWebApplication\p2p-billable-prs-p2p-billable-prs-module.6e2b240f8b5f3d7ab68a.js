(window.webpackJsonp=window.webpackJsonp||[]).push([[836,861,981],{OAbP:function(e,t,i){"use strict";i.r(t),i.d(t,"P2pBillablePrsModule",(function(){return G}));var n=i("ofXK"),l=i("tyNb"),a=i("mrSG"),o=i("1G5W"),s=i("XNiG"),r=i("wd/R"),c=i("xG9w"),d=i("fXoL"),p=i("GnQ3"),m=i("XXEo"),h=i("0IaG"),g=i("g1SM"),b=i("LcQX"),u=i("Vpr3"),C=i("Qu3c"),v=i("bTqV"),f=i("NFeN"),_=i("kmnG"),x=i("qFsG"),S=i("3Pt+"),O=i("dlKe"),y=i("vxfF"),M=i("Xa2L"),E=i("me71"),w=i("ZzPI"),P=i("6t9p");function B(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"mat-icon",29),d["\u0275\u0275text"](1," filter_list "),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275property"]("ngClass",e.isFilterActive?"iconButtonWarn":"iconButton")}}function I(e,t){1&e&&d["\u0275\u0275element"](0,"mat-spinner",30)}function R(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"button",31),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().clearSearch()})),d["\u0275\u0275elementStart"](1,"mat-icon",32),d["\u0275\u0275text"](2," close "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}}const T=function(e){return{background:e}};function j(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",33),d["\u0275\u0275elementStart"](1,"div",34),d["\u0275\u0275listener"]("click",(function(){d["\u0275\u0275restoreView"](e);const i=t.$implicit;return d["\u0275\u0275nextContext"]().selectClaimCard(i)})),d["\u0275\u0275elementStart"](2,"p",35),d["\u0275\u0275text"](3),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](4,"div"),d["\u0275\u0275elementStart"](5,"p",36),d["\u0275\u0275text"](6),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](7,"p",37),d["\u0275\u0275text"](8),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](9,"div"),d["\u0275\u0275elementStart"](10,"p",38),d["\u0275\u0275text"](11),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](12,"p",37),d["\u0275\u0275element"](13,"span",39),d["\u0275\u0275text"](14),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](15,"div",40),d["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngClass",e.isActive?"claim-is-active":""),d["\u0275\u0275advance"](1),d["\u0275\u0275classMapInterpolate1"]("claim-name ",e.isActive?"pl-2-3":"pl-3"," pr-3 pt-2 pb-1 m-0"),d["\u0275\u0275property"]("matTooltip",e.milestone_name),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",e.milestone_name,""),d["\u0275\u0275advance"](1),d["\u0275\u0275classMapInterpolate1"]("p-0 ",e.isActive?"pl-2-3":"pl-3"," pr-3 pb-1 row"),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("matTooltip",e.milestone_amount),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",e.milestone_amount,""),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("matTooltip",e.profit_center),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",e.profit_center," "),d["\u0275\u0275advance"](1),d["\u0275\u0275classMapInterpolate1"]("p-0 ",e.isActive?"pl-2-3":"pl-3"," pr-3 pb-2 row"),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("matTooltip",e.created_on),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",e.created_on,""),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("matTooltip",e.milestone_status_name),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngStyle",d["\u0275\u0275pureFunction1"](21,T,e.milestone_status_color)),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"]("",e.milestone_status_name," ")}}function F(e,t){1&e&&d["\u0275\u0275element"](0,"mat-spinner",41)}function A(e,t){if(1&e&&d["\u0275\u0275element"](0,"dxi-column",65),2&e){const e=t.$implicit;d["\u0275\u0275propertyInterpolate"]("dataField",e.dataField),d["\u0275\u0275propertyInterpolate"]("caption",e.caption),d["\u0275\u0275propertyInterpolate"]("alignment",e.alignment),d["\u0275\u0275property"]("visible",e.visible)}}function k(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",11),d["\u0275\u0275elementStart"](1,"p",45),d["\u0275\u0275text"](2),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](3,"div",46),d["\u0275\u0275elementStart"](4,"div",47),d["\u0275\u0275elementStart"](5,"div",48),d["\u0275\u0275elementStart"](6,"p",49),d["\u0275\u0275text"](7,"Milestone Amount"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](8,"p",50),d["\u0275\u0275text"](9),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](10,"div",51),d["\u0275\u0275elementStart"](11,"p",49),d["\u0275\u0275text"](12,"Project"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](13,"p",50),d["\u0275\u0275text"](14),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](15,"div",51),d["\u0275\u0275elementStart"](16,"p",49),d["\u0275\u0275text"](17,"Cost Center"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](18,"p",50),d["\u0275\u0275text"](19),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](20,"div",52),d["\u0275\u0275elementStart"](21,"p",49),d["\u0275\u0275text"](22,"Created On"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](23,"p",50),d["\u0275\u0275text"](24),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](25,"div",53),d["\u0275\u0275elementStart"](26,"div",48),d["\u0275\u0275elementStart"](27,"p",49),d["\u0275\u0275text"](28,"Milestone ID"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](29,"p",50),d["\u0275\u0275text"](30),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](31,"div",51),d["\u0275\u0275elementStart"](32,"p",49),d["\u0275\u0275text"](33,"Item"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](34,"p",50),d["\u0275\u0275text"](35),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](36,"div",51),d["\u0275\u0275elementStart"](37,"p",49),d["\u0275\u0275text"](38,"Created By"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](39,"div",54),d["\u0275\u0275element"](40,"app-user-image",55),d["\u0275\u0275elementStart"](41,"p",56),d["\u0275\u0275text"](42),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](43,"div",52),d["\u0275\u0275elementStart"](44,"p",49),d["\u0275\u0275text"](45,"Total Items"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](46,"p",50),d["\u0275\u0275text"](47),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](48,"dx-data-grid",57,58),d["\u0275\u0275element"](50,"dxo-filter-row",59),d["\u0275\u0275element"](51,"dxo-header-filter",60),d["\u0275\u0275element"](52,"dxo-paging",61),d["\u0275\u0275element"](53,"dxo-scrolling",62),d["\u0275\u0275element"](54,"dxo-column-chooser",63),d["\u0275\u0275element"](55,"dxo-export",61),d["\u0275\u0275template"](56,A,1,4,"dxi-column",64),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("matTooltip",e.activeBillableClaim.milestone_name),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate"](e.activeBillableClaim.milestone_name),d["\u0275\u0275advance"](6),d["\u0275\u0275property"]("matTooltip",e.activeBillableClaim.milestone_amount),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"]("",e.activeBillableClaim.milestone_amount," "),d["\u0275\u0275advance"](4),d["\u0275\u0275property"]("matTooltip",e.activeBillableClaim.project_name),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate"](e.activeBillableClaim.project_name),d["\u0275\u0275advance"](4),d["\u0275\u0275property"]("matTooltip",e.activeBillableClaim.profit_center),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate"](e.activeBillableClaim.profit_center),d["\u0275\u0275advance"](4),d["\u0275\u0275property"]("matTooltip",e.activeBillableClaim.created_on),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate"](e.activeBillableClaim.created_on),d["\u0275\u0275advance"](5),d["\u0275\u0275property"]("matTooltip",e.activeBillableClaim.milestone_id),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate"](e.activeBillableClaim.milestone_id),d["\u0275\u0275advance"](4),d["\u0275\u0275property"]("matTooltip",e.activeBillableClaim.item_name),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate"](e.activeBillableClaim.item_name),d["\u0275\u0275advance"](4),d["\u0275\u0275property"]("matTooltip",e.activeBillableClaim.created_by_name),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("id",e.activeBillableClaim?e.activeBillableClaim.created_by_oid:"")("imgWidth","25px")("imgHeight","25px"),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"](" ",e.activeBillableClaim.created_by_name," "),d["\u0275\u0275advance"](4),d["\u0275\u0275property"]("matTooltip",e.activeBillableClaim.expense_detail.length),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",e.activeBillableClaim.expense_detail.length,""),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("dataSource",e.activeBillableClaim.expense_detail)("allowColumnResizing",!0)("columnResizingMode","widget")("allowColumnReordering",!0)("showBorders",!0)("columnHidingEnabled",!1)("columnWidth",125),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("visible",!0)("applyFilter",!0),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("visible",!0),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("enabled",!1),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("enabled",!0),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("enabled",!0),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngForOf",e.expenseDetailColumnConfigs)}}function D(e,t){1&e&&d["\u0275\u0275element"](0,"mat-spinner",66)}function z(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",42),d["\u0275\u0275template"](1,k,57,35,"div",43),d["\u0275\u0275template"](2,D,1,0,"mat-spinner",44),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!e.isBillablePRMilestonesLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isBillablePRMilestonesLoading)}}function L(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275element"](1,"mat-spinner",66),d["\u0275\u0275elementEnd"]())}function H(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementStart"](1,"div"),d["\u0275\u0275elementStart"](2,"h4",69),d["\u0275\u0275text"](3," Oops ! No Data Found ! "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](4,"div",70),d["\u0275\u0275element"](5,"img",71),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function W(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",67),d["\u0275\u0275template"](1,L,2,0,"div",68),d["\u0275\u0275template"](2,H,6,0,"div",68),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.areBillableMilestonesLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!e.areBillableMilestonesLoading)}}const N=[{path:"",component:(()=>{class e{constructor(e,t,i,n,l,a,o){this.udrfService=e,this.authService=t,this.router=i,this.dialog=n,this.p2pBillablePRService=l,this.utilityService=a,this.reportsService=o,this.applicationId=1011,this.currentUser={},this.searchParameter="",this.billableClaims=[],this.expenseDetailColumnConfigs=[{dataField:"pr_code",caption:"PR Code",visible:!0,alignment:"left"},{dataField:"item_name",caption:"Name",visible:!0,alignment:"left"},{dataField:"expense_amount",caption:"Amount",visible:!0,alignment:"left"},{dataField:"service_name",caption:"Service",visible:!0,alignment:"left"},{dataField:"sub_group_name",caption:"Sub Group",visible:!0,alignment:"left"},{dataField:"p2p_header_id",caption:"PR Id",visible:!1,alignment:"left"},{dataField:"billed_on",caption:"Created On",visible:!0,alignment:"left"},{dataField:"requested_by",caption:"Requested By",visible:!0,alignment:"left"}],this.startIndex=0,this.noOfRecords=10,this.areBillableMilestonesLoading=!0,this.isMilestoneDetailVisible=!1,this.isBillablePRMilestonesLoading=!1,this.isBillablePRMilestonesAvailable=!1,this.defaultCurrency="INR",this.defaultDateFormat="DD MMM YYYY",this.billableExpenseMilestoneDetails={},this.misFlags={},this.statusMaster=[],this.isFilterActive=!1,this._onDestroy=new s.b}ngOnInit(){this.currentUser=this.authService.getProfile().profile,this.areBillableMilestonesLoading=!0,this.udrfService.udrfData.areSortAndFiltersLoading=!0,this.udrfService.udrfUiData.isModalSortVisible=!1,this.reportsService.getMisFlags().pipe(Object(o.a)(this._onDestroy)).subscribe(e=>{"S"==e.messType?(this.misFlags=e.misFlags,this.defaultCurrency=this.misFlags.defaultCurrency,this.retrieveExpBillMileStatuses()):this.utilityService.showErrorMessage(e,"KEBS")},e=>{this.utilityService.showErrorMessage(e,"KEBS")})}retrieveExpBillMileStatuses(){this.p2pBillablePRService.retrieveExpBillMileStatuses().pipe(Object(o.a)(this._onDestroy)).subscribe(e=>Object(a.c)(this,void 0,void 0,(function*(){"S"==e.messType?(this.statusMaster=e.data,this.udrfService.getAppUdrfConfig(this.applicationId,this.getBillableMilestones.bind(this,!0))):this.utilityService.showErrorMessage(e,"KEBS")})),e=>{this.areBillableMilestonesLoading=!1,console.log(e),this.utilityService.showErrorMessage(e,"KEBS")})}getBillableMilestones(e){this.isFilterActive=!1;let t={cost_center:[],milestone_status:[],project_name:[],milestone_name:[],milestone_id:[],pr_code:[]};for(let i of this.udrfService.udrfData.mainFilterArray)this.isFilterActive=!0,t[i.filterColumnName]=i.multiOptionSelectSearchValues;this.areBillableMilestonesLoading=!0,e&&(this.startIndex=0),this.p2pBillablePRService.getBillableMilestones({start_index:this.startIndex,no_of_records:this.noOfRecords,search_parameter:this.searchParameter,filter_params:t}).pipe(Object(o.a)(this._onDestroy)).subscribe(t=>Object(a.c)(this,void 0,void 0,(function*(){if("S"==t.messType&&t.data&&t.data.length>0){this.isBillablePRMilestonesAvailable=!0,this.billableClaims=e?t.data:this.billableClaims.concat(t.data),this.billableClaims.length>0&&this.selectClaimCard(this.billableClaims[0]),this.startIndex=this.billableClaims.length;for(let e of this.billableClaims)e.created_on=r(e.created_on).format(this.defaultDateFormat),e.milestone_status_color=this.getMilestoneStatusColor(e.milestone_status_id),e.milestone_amount=this.resolveAmountAndCurrency(e.milestone_value),e.isActive||(e.isActive=!1)}else if("E"==t.messType){let e="KEBS";this.isBillablePRMilestonesAvailable=!1,this.utilityService.showErrorMessage(t,e)}else"S"==t.messType&&t.data&&0==t.data.length&&e&&(this.billableClaims=[],this.isBillablePRMilestonesAvailable=!1);this.areBillableMilestonesLoading=!1})),e=>{this.areBillableMilestonesLoading=!1,console.log(e),this.utilityService.showErrorMessage(e,"KEBS")})}resolveAmountAndCurrency(e){let t=c.where(e,{currency_code:this.defaultCurrency}),i=t.length>0?t[0].value:0;return i=this.utilityService.getAmountCurrencyFormat(i,this.defaultCurrency),this.defaultCurrency+" "+i}getMilestoneStatusColor(e){let t=c.where(this.statusMaster,{id:e});return t.length>0?t[0].color:""}openFiltersAndSort(){return Object(a.c)(this,void 0,void 0,(function*(){const{UdrfModalComponent:e}=yield Promise.all([i.e(4),i.e(998)]).then(i.bind(null,"UIsE"));this.dialog.open(e,{minWidth:"100%",height:"84%",position:{top:"56px",left:"77px"},disableClose:!0})}))}callSearchApi(){this.getBillableMilestones(!0)}clearSearch(){this.searchParameter="",this.callSearchApi()}onSearchParameterChange(){""==this.searchParameter&&this.callSearchApi()}selectClaimCard(e){this.isBillablePRMilestonesLoading=!0,this.isMilestoneDetailVisible=!0;for(let t of this.billableClaims)t.isActive=!1;e.isActive=!0,this.activeBillableClaim=e,null==this.billableExpenseMilestoneDetails[e.milestone_id]?this.getBillablePRMilestoneDetail(e.milestone_id):(this.activeBillableClaim.expense_milestone_detail=this.billableExpenseMilestoneDetails[e.milestone_id],this.resolveMilestoneExpenseDetail(),this.isBillablePRMilestonesLoading=!1)}getBillablePRMilestoneDetail(e){this.p2pBillablePRService.getBillablePRMilestoneDetail({milestone_id:e}).pipe(Object(o.a)(this._onDestroy)).subscribe(t=>Object(a.c)(this,void 0,void 0,(function*(){"S"==t.messType&&null!=t.data?(this.billableExpenseMilestoneDetails[e]=t.data,this.activeBillableClaim.expense_milestone_detail=t.data,this.resolveMilestoneExpenseDetail(),console.log("getBillablePRMilestoneDetail value : ",this.activeBillableClaim)):"E"==t.messType&&this.utilityService.showErrorMessage(t,"KEBS"),this.isBillablePRMilestonesLoading=!1})),e=>{this.isBillablePRMilestonesLoading=!1,console.log(e),this.utilityService.showErrorMessage(e,"KEBS")})}resolveMilestoneExpenseDetail(){if(null!=this.activeBillableClaim.expense_milestone_detail.expense_detail){for(let e of this.activeBillableClaim.expense_milestone_detail.expense_detail)e.expense_amount=this.resolveAmountAndCurrency(e.total_approved_amount),e.billed_on=r(e.billed_on).format(this.defaultDateFormat);this.activeBillableClaim.expense_detail=this.activeBillableClaim.expense_milestone_detail.expense_detail}else this.activeBillableClaim.expense_detail=[];null!=this.activeBillableClaim.expense_milestone_detail.milestone_detail&&this.activeBillableClaim.expense_milestone_detail.milestone_detail.length>0&&(this.activeBillableClaim.created_by_name=this.activeBillableClaim.expense_milestone_detail.milestone_detail[0].created_by_name,this.activeBillableClaim.created_by_oid=this.activeBillableClaim.expense_milestone_detail.milestone_detail[0].created_by_oid,this.activeBillableClaim.item_name=this.activeBillableClaim.expense_milestone_detail.milestone_detail[0].item_name,this.activeBillableClaim.project_name=this.activeBillableClaim.expense_milestone_detail.milestone_detail[0].project_name,this.activeBillableClaim.project_id=this.activeBillableClaim.expense_milestone_detail.milestone_detail[0].project_id,this.activeBillableClaim.project_item_id=this.activeBillableClaim.expense_milestone_detail.milestone_detail[0].project_item_id)}createBillablePR(){return Object(a.c)(this,void 0,void 0,(function*(){let e={defaultCurrency:this.defaultCurrency,defaultDateFormat:this.defaultDateFormat};const{CreateBillablePrsPopupComponent:t}=yield Promise.all([i.e(0),i.e(749)]).then(i.bind(null,"u6//"));this.dialog.open(t,{height:"90%",width:"90%",maxWidth:"90%",minWidth:"90%",disableClose:!0,data:{modalParams:e}}).afterClosed().subscribe(e=>Object(a.c)(this,void 0,void 0,(function*(){"Milestone Created"==e.event&&this.getBillableMilestones(!0)})))}))}goBack(){this.router.navigateByUrl("/main/p2p/treasury")}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](p.a),d["\u0275\u0275directiveInject"](m.a),d["\u0275\u0275directiveInject"](l.g),d["\u0275\u0275directiveInject"](h.b),d["\u0275\u0275directiveInject"](g.a),d["\u0275\u0275directiveInject"](b.a),d["\u0275\u0275directiveInject"](u.a))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-p2p-billable-pr-milestones"]],decls:34,vars:15,consts:[[1,"p2p-billable-prs-styles"],[1,"col-12","pt-2","pb-2","pl-0","pr-0","row"],["matTooltip","Go Back",1,"col-2","p-0","row",3,"click"],["mat-icon-button",""],[1,"arrow-icon"],[1,"m-0","back-text","c-align"],[1,"col-8","p-0"],[1,"col-2","p-0",2,"text-align","center"],["mat-flat-button","",1,"pl-0","pr-0","red-btn",3,"click"],[1,"col-12","pl-3","pr-3","row","card-height"],[1,"card","col-3","p-0","mr-4"],[1,"card-body","p-0"],[1,"row","pl-4","pr-3","pt-2"],[1,"milestone-count","p-0","m-0",2,"line-height","3"],["mat-icon-button","","matTooltip","Apply Filters And Sort",1,"view-button-inactive","align-right",3,"disabled","click"],[3,"ngClass",4,"ngIf"],["matTooltip","Loading Filters And Sort  ...","class","spinner-align","diameter","18",4,"ngIf"],[1,"row","mb-1"],["appearance","outline",1,"ml-auto","mr-auto",2,"width","85%"],["matPrefix",""],[2,"font-size","20px !important","color","black !important"],["matInput","","placeholder","Search and press Enter","autocomplete","off",2,"padding-bottom","1px !important",3,"ngModel","ngModelChange","keyup.enter"],["matSuffix",""],["mat-button","","matSuffix","","mat-icon-button","","style","height: 30px; width: 30px; line-height: 1",3,"click",4,"ngIf"],["infinite-scroll","","cdkScrollable","",3,"infiniteScrollDistance","scrollWindow","scrolled"],["class","col-12 p-0 mb-2",4,"ngFor","ngForOf"],["class","mt-2 ml-auto mr-auto mb-2","diameter","25","matTooltip","Loading ...",4,"ngIf"],["class","card col-8-7 p-0",4,"ngIf"],["class","col-8-7 p-0 m-0 ca-m card-body-height align-items-center","style","margin-top:5% !important;",4,"ngIf"],[3,"ngClass"],["matTooltip","Loading Filters And Sort  ...","diameter","18",1,"spinner-align"],["mat-button","","matSuffix","","mat-icon-button","",2,"height","30px","width","30px","line-height","1",3,"click"],["matTooltip","Clear Search",2,"font-size","20px !important","color","#66615b !important"],[1,"col-12","p-0","mb-2"],[1,"col-12","p-0","claim-card",3,"ngClass","click"],[3,"matTooltip"],[1,"col-6","p-0","claim-amount","m-0","pr-1",3,"matTooltip"],[1,"col-6","p-0","claim-status","m-0",3,"matTooltip"],[1,"col-6","p-0","claim-date","m-0","pr-1",3,"matTooltip"],[1,"claim-status-dot","mr-2",3,"ngStyle"],[1,"claim-line","mt-2","col-9","p-0"],["diameter","25","matTooltip","Loading ...",1,"mt-2","ml-auto","mr-auto","mb-2"],[1,"card","col-8-7","p-0"],["class","card-body p-0",4,"ngIf"],["class","ml-auto mr-auto mb-auto mt-auto","diameter","25","matTooltip","Loading ...",4,"ngIf"],[1,"claim-item-name","col-12","p-0","pl-4","pr-4","pt-3","m-0",3,"matTooltip"],[1,"col-12","p-0","pt-3"],[1,"col-12","p-0","row"],[1,"col-3","p-0","pl-4","pr-2"],[1,"claim-item-header","col-12","p-0","m-0"],[1,"claim-item-description","col-12","p-0","m-0",3,"matTooltip"],[1,"col-3","p-0","pr-2"],[1,"col-3","p-0","pr-4"],[1,"col-12","p-0","pt-3","row"],[1,"col-12","p-0","row",2,"align-items","center",3,"matTooltip"],["content-type","template",1,"mr-2",3,"id","imgWidth","imgHeight"],[1,"claim-item-description","m-0","col-10","p-0"],[2,"height","53vh","margin-left","1.5rem","margin-right","1.5rem",3,"dataSource","allowColumnResizing","columnResizingMode","allowColumnReordering","showBorders","columnHidingEnabled","columnWidth"],["claimExpensesDataGrid",""],[3,"visible","applyFilter"],[3,"visible"],[3,"enabled"],["columnRenderingMode","virtual"],["mode","select",3,"enabled"],[3,"dataField","caption","alignment","visible",4,"ngFor","ngForOf"],[3,"dataField","caption","alignment","visible"],["diameter","25","matTooltip","Loading ...",1,"ml-auto","mr-auto","mb-auto","mt-auto"],[1,"col-8-7","p-0","m-0","ca-m","card-body-height","align-items-center",2,"margin-top","5% !important"],[4,"ngIf"],[1,"d-flex","justify-content-center","align-items-center","mt-0","mb-0","slide-in-top"],[1,"d-flex","justify-content-center","align-items-center","slide-from-down"],["src","assets/images/nomilestone.png","height","220","width","250",1,"mt-3"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",0),d["\u0275\u0275elementStart"](1,"div",1),d["\u0275\u0275elementStart"](2,"div",2),d["\u0275\u0275listener"]("click",(function(){return t.goBack()})),d["\u0275\u0275elementStart"](3,"button",3),d["\u0275\u0275elementStart"](4,"mat-icon",4),d["\u0275\u0275text"](5,"keyboard_arrow_left"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](6,"p",5),d["\u0275\u0275text"](7,"Back To PR"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](8,"div",6),d["\u0275\u0275elementStart"](9,"div",7),d["\u0275\u0275elementStart"](10,"button",8),d["\u0275\u0275listener"]("click",(function(){return t.createBillablePR()})),d["\u0275\u0275text"](11,"Create Billable PR"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](12,"div",9),d["\u0275\u0275elementStart"](13,"div",10),d["\u0275\u0275elementStart"](14,"div",11),d["\u0275\u0275elementStart"](15,"div",12),d["\u0275\u0275elementStart"](16,"p",13),d["\u0275\u0275text"](17),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](18,"button",14),d["\u0275\u0275listener"]("click",(function(){return t.openFiltersAndSort()})),d["\u0275\u0275template"](19,B,2,1,"mat-icon",15),d["\u0275\u0275template"](20,I,1,0,"mat-spinner",16),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](21,"div",17),d["\u0275\u0275elementStart"](22,"mat-form-field",18),d["\u0275\u0275elementStart"](23,"span",19),d["\u0275\u0275elementStart"](24,"mat-icon",20),d["\u0275\u0275text"](25,"search"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](26,"input",21),d["\u0275\u0275listener"]("ngModelChange",(function(e){return t.searchParameter=e}))("keyup.enter",(function(){return t.callSearchApi()}))("ngModelChange",(function(){return t.onSearchParameterChange()})),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](27,"mat-icon",22),d["\u0275\u0275template"](28,R,3,0,"button",23),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](29,"div",24),d["\u0275\u0275listener"]("scrolled",(function(){return t.getBillableMilestones(!1)})),d["\u0275\u0275template"](30,j,16,23,"div",25),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](31,F,1,0,"mat-spinner",26),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](32,z,3,2,"div",27),d["\u0275\u0275template"](33,W,3,2,"div",28),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e&&(d["\u0275\u0275advance"](17),d["\u0275\u0275textInterpolate1"]("",t.billableClaims.length," Milestones"),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("disabled",t.udrfService.udrfData.areSortAndFiltersLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!t.udrfService.udrfData.areSortAndFiltersLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.udrfService.udrfData.areSortAndFiltersLoading),d["\u0275\u0275advance"](6),d["\u0275\u0275property"]("ngModel",t.searchParameter),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",t.searchParameter&&""!=t.searchParameter),d["\u0275\u0275advance"](1),d["\u0275\u0275classMapInterpolate1"]("row ",t.areBillableMilestonesLoading?"claim-card-list-loading":"claim-card-list",""),d["\u0275\u0275property"]("infiniteScrollDistance",2)("scrollWindow",!1),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngForOf",t.billableClaims),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.areBillableMilestonesLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.isMilestoneDetailVisible&&t.isBillablePRMilestonesAvailable),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!t.isBillablePRMilestonesAvailable))},directives:[C.a,v.a,f.a,n.NgIf,_.c,_.h,x.b,S.e,S.v,S.y,_.i,O.a,y.b,n.NgForOf,n.NgClass,M.c,n.NgStyle,E.a,w.a,P.dc,P.Cc,P.od,P.Jd,P.tb,P.Sb,P.g],styles:[".p2p-billable-prs-styles[_ngcontent-%COMP%]   .spinner-align[_ngcontent-%COMP%]{margin:auto;display:inline-block}.p2p-billable-prs-styles[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]{margin-top:5px!important;line-height:8px;width:26px;height:26px;margin-right:10px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12);animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}.p2p-billable-prs-styles[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.p2p-billable-prs-styles[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]   .iconButtonWarn[_ngcontent-%COMP%]{color:#cf0001;font-size:18px}.p2p-billable-prs-styles[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]   .iconButtonWarnAnim[_ngcontent-%COMP%]{color:#cf0001;font-size:18px;animation:blink 1.1s cubic-bezier(.5,0,1,1) infinite alternate}.p2p-billable-prs-styles[_ngcontent-%COMP%]   .trend-button-inactive[_ngcontent-%COMP%]{line-height:8px;width:24px;height:24px;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.p2p-billable-prs-styles[_ngcontent-%COMP%]   .trend-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#868683;font-size:18px}.p2p-billable-prs-styles[_ngcontent-%COMP%]   .arrow-icon[_ngcontent-%COMP%]{color:#868683;font-size:24px}.p2p-billable-prs-styles[_ngcontent-%COMP%]   .back-text[_ngcontent-%COMP%]{color:#868683;font-size:13px;cursor:pointer}.p2p-billable-prs-styles[_ngcontent-%COMP%]   .c-align[_ngcontent-%COMP%]{text-align:center;display:flex;align-items:center;justify-content:center}.p2p-billable-prs-styles[_ngcontent-%COMP%]   .red-btn[_ngcontent-%COMP%]{width:100%;height:36px;max-width:150px;background:#f15b64;font-size:13px;color:#fff;font-weight:500}.p2p-billable-prs-styles[_ngcontent-%COMP%]   .milestone-count[_ngcontent-%COMP%]{font-weight:500;font-size:13px}.p2p-billable-prs-styles[_ngcontent-%COMP%]   .col-8-7[_ngcontent-%COMP%]{flex:0 0 73%;max-width:73%}.p2p-billable-prs-styles[_ngcontent-%COMP%]   .card-height[_ngcontent-%COMP%]{height:80vh}.p2p-billable-prs-styles[_ngcontent-%COMP%]   .filter-icon[_ngcontent-%COMP%]{font-size:18px;color:#514f4f}.p2p-billable-prs-styles[_ngcontent-%COMP%]   .align-right[_ngcontent-%COMP%]{margin-left:auto;margin-right:0}.p2p-billable-prs-styles[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-wrapper{padding-bottom:0!important}.p2p-billable-prs-styles[_ngcontent-%COMP%]   .claim-name[_ngcontent-%COMP%]{font-weight:500;font-size:13px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.p2p-billable-prs-styles[_ngcontent-%COMP%]   .claim-amount[_ngcontent-%COMP%]{font-weight:450;font-size:13px;color:#79cb51;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.p2p-billable-prs-styles[_ngcontent-%COMP%]   .claim-date[_ngcontent-%COMP%]{font-weight:400;font-size:13px;color:#a3a3a3;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.p2p-billable-prs-styles[_ngcontent-%COMP%]   .claim-status[_ngcontent-%COMP%]{font-weight:450;font-size:13px;color:#514f4f;text-align:right;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.p2p-billable-prs-styles[_ngcontent-%COMP%]   .claim-is-active[_ngcontent-%COMP%]{background-color:#f6f6f0;border-left:4px solid #f15b64}.p2p-billable-prs-styles[_ngcontent-%COMP%]   .claim-line[_ngcontent-%COMP%]{height:1px;background-color:#dbdbd6;margin-left:auto;margin-right:auto}.p2p-billable-prs-styles[_ngcontent-%COMP%]   .claim-card[_ngcontent-%COMP%]{cursor:pointer}.p2p-billable-prs-styles[_ngcontent-%COMP%]   .claim-card-list-loading[_ngcontent-%COMP%]{overflow-y:scroll;max-height:56vh}.p2p-billable-prs-styles[_ngcontent-%COMP%]   .claim-card-list[_ngcontent-%COMP%]{overflow-y:scroll;max-height:63vh}.p2p-billable-prs-styles[_ngcontent-%COMP%]   .claim-status-dot[_ngcontent-%COMP%]{height:10px;width:10px;border-radius:50%;display:inline-block;vertical-align:middle;margin-bottom:.1rem!important}.p2p-billable-prs-styles[_ngcontent-%COMP%]   .claim-item-name[_ngcontent-%COMP%]{font-weight:500;font-size:15px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.p2p-billable-prs-styles[_ngcontent-%COMP%]   .claim-item-header[_ngcontent-%COMP%]{font-weight:400;font-size:13px;color:#a3a3a3}.p2p-billable-prs-styles[_ngcontent-%COMP%]   .claim-item-description[_ngcontent-%COMP%]{font-weight:500;font-size:13px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.p2p-billable-prs-styles[_ngcontent-%COMP%]     .dx-widget{max-width:100%!important}.p2p-billable-prs-styles[_ngcontent-%COMP%]   .pl-2-3[_ngcontent-%COMP%]{padding-left:.75rem!important}"]}),e})(),children:[]}];let K=(()=>{class e{}return e.\u0275mod=d["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=d["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[l.k.forChild(N)],l.k]}),e})();var V=i("JqCM"),q=i("Xi0T");let G=(()=>{class e{}return e.\u0275mod=d["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=d["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[n.CommonModule,K,v.b,f.b,C.b,_.e,x.c,S.p,S.E,w.b,O.b,M.b,q.a,V.b]]}),e})()},g1SM:function(e,t,i){"use strict";i.d(t,"a",(function(){return a}));var n=i("fXoL"),l=i("tk/3");let a=(()=>{class e{constructor(e){this.http=e,this.getBillablePRByCostCenter=(e,t,i)=>this.http.post("/api/purchaseRequest/getBillablePRByCostCenter",{startIndex:e,noOfRecords:t,searchParameter:i}),this.getPONumberListForProject=(e,t)=>this.http.post("/api/purchaseRequest/getPONumberListForProject",{project_id:e,project_item_id:t})}retrieveExpBillMileStatuses(){return this.http.post("/api/exPrimary/retrieveExpBillMileStatuses",{})}getBillableMilestones(e){let t={};for(let i of Object.keys(e))t[i]=e[i];return this.http.post("/api/purchaseRequest/getBillableMilestones",t)}getBillablePRMilestoneDetail(e){let t={};for(let i of Object.keys(e))t[i]=e[i];return this.http.post("/api/purchaseRequest/getBillablePRMilestoneDetail",t)}getCostCentreProjectItemName(e){let t={};for(let i of Object.keys(e))t[i]=e[i];return this.http.post("/api/purchaseRequest/getCostCenterProjectItemName",t)}getPRListBasedOnCostCenter(e){let t={};for(let i of Object.keys(e))t[i]=e[i];return this.http.post("/api/purchaseRequest/getPRListBasedOnCostCenter",t)}billableMilestoneCreation(e){let t={};for(let i of Object.keys(e))t[i]=e[i];return this.http.post("/api/purchaseRequest/billableMilestoneCreation",t)}getExpenseFieldConfig(){return this.http.post("/api/exPrimary/getFormFieldConfig",{})}}return e.\u0275fac=function(t){return new(t||e)(n["\u0275\u0275inject"](l.c))},e.\u0275prov=n["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},w76M:function(e,t,i){"use strict";i.d(t,"a",(function(){return c})),i.d(t,"b",(function(){return d}));var n=i("jhN1"),l=i("fXoL"),a=i("oHs6"),o=i("PVOt"),s=i("6t9p");const r=["*"];let c=(()=>{let e=class extends o.b{constructor(e,t,i,n,l,a,o,s){super(e,t,i,n,o,s),this._watcherHelper=n,this._idh=l,this._createEventEmitters([{subscribe:"contentReady",emit:"onContentReady"},{subscribe:"disposing",emit:"onDisposing"},{subscribe:"hidden",emit:"onHidden"},{subscribe:"hiding",emit:"onHiding"},{subscribe:"initialized",emit:"onInitialized"},{subscribe:"optionChanged",emit:"onOptionChanged"},{subscribe:"resize",emit:"onResize"},{subscribe:"resizeEnd",emit:"onResizeEnd"},{subscribe:"resizeStart",emit:"onResizeStart"},{subscribe:"showing",emit:"onShowing"},{subscribe:"shown",emit:"onShown"},{subscribe:"titleRendered",emit:"onTitleRendered"},{emit:"accessKeyChange"},{emit:"animationChange"},{emit:"closeOnOutsideClickChange"},{emit:"containerChange"},{emit:"contentTemplateChange"},{emit:"deferRenderingChange"},{emit:"disabledChange"},{emit:"dragEnabledChange"},{emit:"elementAttrChange"},{emit:"focusStateEnabledChange"},{emit:"fullScreenChange"},{emit:"heightChange"},{emit:"hintChange"},{emit:"hoverStateEnabledChange"},{emit:"maxHeightChange"},{emit:"maxWidthChange"},{emit:"minHeightChange"},{emit:"minWidthChange"},{emit:"positionChange"},{emit:"resizeEnabledChange"},{emit:"rtlEnabledChange"},{emit:"shadingChange"},{emit:"shadingColorChange"},{emit:"showCloseButtonChange"},{emit:"showTitleChange"},{emit:"tabIndexChange"},{emit:"titleChange"},{emit:"titleTemplateChange"},{emit:"toolbarItemsChange"},{emit:"visibleChange"},{emit:"widthChange"}]),this._idh.setHost(this),a.setHost(this)}get accessKey(){return this._getOption("accessKey")}set accessKey(e){this._setOption("accessKey",e)}get animation(){return this._getOption("animation")}set animation(e){this._setOption("animation",e)}get closeOnOutsideClick(){return this._getOption("closeOnOutsideClick")}set closeOnOutsideClick(e){this._setOption("closeOnOutsideClick",e)}get container(){return this._getOption("container")}set container(e){this._setOption("container",e)}get contentTemplate(){return this._getOption("contentTemplate")}set contentTemplate(e){this._setOption("contentTemplate",e)}get deferRendering(){return this._getOption("deferRendering")}set deferRendering(e){this._setOption("deferRendering",e)}get disabled(){return this._getOption("disabled")}set disabled(e){this._setOption("disabled",e)}get dragEnabled(){return this._getOption("dragEnabled")}set dragEnabled(e){this._setOption("dragEnabled",e)}get elementAttr(){return this._getOption("elementAttr")}set elementAttr(e){this._setOption("elementAttr",e)}get focusStateEnabled(){return this._getOption("focusStateEnabled")}set focusStateEnabled(e){this._setOption("focusStateEnabled",e)}get fullScreen(){return this._getOption("fullScreen")}set fullScreen(e){this._setOption("fullScreen",e)}get height(){return this._getOption("height")}set height(e){this._setOption("height",e)}get hint(){return this._getOption("hint")}set hint(e){this._setOption("hint",e)}get hoverStateEnabled(){return this._getOption("hoverStateEnabled")}set hoverStateEnabled(e){this._setOption("hoverStateEnabled",e)}get maxHeight(){return this._getOption("maxHeight")}set maxHeight(e){this._setOption("maxHeight",e)}get maxWidth(){return this._getOption("maxWidth")}set maxWidth(e){this._setOption("maxWidth",e)}get minHeight(){return this._getOption("minHeight")}set minHeight(e){this._setOption("minHeight",e)}get minWidth(){return this._getOption("minWidth")}set minWidth(e){this._setOption("minWidth",e)}get position(){return this._getOption("position")}set position(e){this._setOption("position",e)}get resizeEnabled(){return this._getOption("resizeEnabled")}set resizeEnabled(e){this._setOption("resizeEnabled",e)}get rtlEnabled(){return this._getOption("rtlEnabled")}set rtlEnabled(e){this._setOption("rtlEnabled",e)}get shading(){return this._getOption("shading")}set shading(e){this._setOption("shading",e)}get shadingColor(){return this._getOption("shadingColor")}set shadingColor(e){this._setOption("shadingColor",e)}get showCloseButton(){return this._getOption("showCloseButton")}set showCloseButton(e){this._setOption("showCloseButton",e)}get showTitle(){return this._getOption("showTitle")}set showTitle(e){this._setOption("showTitle",e)}get tabIndex(){return this._getOption("tabIndex")}set tabIndex(e){this._setOption("tabIndex",e)}get title(){return this._getOption("title")}set title(e){this._setOption("title",e)}get titleTemplate(){return this._getOption("titleTemplate")}set titleTemplate(e){this._setOption("titleTemplate",e)}get toolbarItems(){return this._getOption("toolbarItems")}set toolbarItems(e){this._setOption("toolbarItems",e)}get visible(){return this._getOption("visible")}set visible(e){this._setOption("visible",e)}get width(){return this._getOption("width")}set width(e){this._setOption("width",e)}get toolbarItemsChildren(){return this._getOption("toolbarItems")}set toolbarItemsChildren(e){this.setChildren("toolbarItems",e)}_createInstance(e,t){return new a.a(e,t)}ngOnDestroy(){this._destroyWidget()}ngOnChanges(e){super.ngOnChanges(e),this.setupChanges("toolbarItems",e)}setupChanges(e,t){e in this._optionsToUpdate||this._idh.setup(e,t)}ngDoCheck(){this._idh.doCheck("toolbarItems"),this._watcherHelper.checkWatchers(),super.ngDoCheck(),super.clearChangedOptions()}_setOption(e,t){let i=this._idh.setupSingle(e,t),n=null!==this._idh.getChanges(e,t);(i||n)&&super._setOption(e,t)}};return e.\u0275fac=function(t){return new(t||e)(l["\u0275\u0275directiveInject"](l.ElementRef),l["\u0275\u0275directiveInject"](l.NgZone),l["\u0275\u0275directiveInject"](o.e),l["\u0275\u0275directiveInject"](o.j),l["\u0275\u0275directiveInject"](o.g),l["\u0275\u0275directiveInject"](o.i),l["\u0275\u0275directiveInject"](n.h),l["\u0275\u0275directiveInject"](l.PLATFORM_ID))},e.\u0275cmp=l["\u0275\u0275defineComponent"]({type:e,selectors:[["dx-popup"]],contentQueries:function(e,t,i){if(1&e&&l["\u0275\u0275contentQuery"](i,s.L,!1),2&e){let e;l["\u0275\u0275queryRefresh"](e=l["\u0275\u0275loadQuery"]())&&(t.toolbarItemsChildren=e)}},inputs:{accessKey:"accessKey",animation:"animation",closeOnOutsideClick:"closeOnOutsideClick",container:"container",contentTemplate:"contentTemplate",deferRendering:"deferRendering",disabled:"disabled",dragEnabled:"dragEnabled",elementAttr:"elementAttr",focusStateEnabled:"focusStateEnabled",fullScreen:"fullScreen",height:"height",hint:"hint",hoverStateEnabled:"hoverStateEnabled",maxHeight:"maxHeight",maxWidth:"maxWidth",minHeight:"minHeight",minWidth:"minWidth",position:"position",resizeEnabled:"resizeEnabled",rtlEnabled:"rtlEnabled",shading:"shading",shadingColor:"shadingColor",showCloseButton:"showCloseButton",showTitle:"showTitle",tabIndex:"tabIndex",title:"title",titleTemplate:"titleTemplate",toolbarItems:"toolbarItems",visible:"visible",width:"width"},outputs:{onContentReady:"onContentReady",onDisposing:"onDisposing",onHidden:"onHidden",onHiding:"onHiding",onInitialized:"onInitialized",onOptionChanged:"onOptionChanged",onResize:"onResize",onResizeEnd:"onResizeEnd",onResizeStart:"onResizeStart",onShowing:"onShowing",onShown:"onShown",onTitleRendered:"onTitleRendered",accessKeyChange:"accessKeyChange",animationChange:"animationChange",closeOnOutsideClickChange:"closeOnOutsideClickChange",containerChange:"containerChange",contentTemplateChange:"contentTemplateChange",deferRenderingChange:"deferRenderingChange",disabledChange:"disabledChange",dragEnabledChange:"dragEnabledChange",elementAttrChange:"elementAttrChange",focusStateEnabledChange:"focusStateEnabledChange",fullScreenChange:"fullScreenChange",heightChange:"heightChange",hintChange:"hintChange",hoverStateEnabledChange:"hoverStateEnabledChange",maxHeightChange:"maxHeightChange",maxWidthChange:"maxWidthChange",minHeightChange:"minHeightChange",minWidthChange:"minWidthChange",positionChange:"positionChange",resizeEnabledChange:"resizeEnabledChange",rtlEnabledChange:"rtlEnabledChange",shadingChange:"shadingChange",shadingColorChange:"shadingColorChange",showCloseButtonChange:"showCloseButtonChange",showTitleChange:"showTitleChange",tabIndexChange:"tabIndexChange",titleChange:"titleChange",titleTemplateChange:"titleTemplateChange",toolbarItemsChange:"toolbarItemsChange",visibleChange:"visibleChange",widthChange:"widthChange"},features:[l["\u0275\u0275ProvidersFeature"]([o.e,o.j,o.i,o.g]),l["\u0275\u0275InheritDefinitionFeature"],l["\u0275\u0275NgOnChangesFeature"]],ngContentSelectors:r,decls:1,vars:0,template:function(e,t){1&e&&(l["\u0275\u0275projectionDef"](),l["\u0275\u0275projection"](0))},encapsulation:2}),e})(),d=(()=>{let e=class{};return e.\u0275mod=l["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=l["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[s.bb,s.Gc,s.Vd,s.vd,s.hb,s.lb,s.sb,s.id,s.jd,s.M,o.c,o.f,n.b],s.bb,s.Gc,s.Vd,s.vd,s.hb,s.lb,s.sb,s.id,s.jd,s.M,o.f]}),e})()}}]);