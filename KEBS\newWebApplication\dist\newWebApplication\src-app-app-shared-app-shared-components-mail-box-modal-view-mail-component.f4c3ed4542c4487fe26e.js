(window.webpackJsonp=window.webpackJsonp||[]).push([[900],{Grxv:function(e,t,i){"use strict";i.d(t,"a",(function(){return l}));var n=i("ofXK"),a=i("fXoL");let l=(()=>{class e{}return e.\u0275mod=a["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=a["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[n.CommonModule]]}),e})()},gzal:function(e,t,i){"use strict";i.r(t),i.d(t,"ViewMailComponent",(function(){return at})),i.d(t,"ViewMailModule",(function(){return lt}));var n=i("mrSG"),a=i("ofXK"),l=i("bTqV"),r=i("NFeN"),o=i("Qu3c"),c=i("Xa2L"),m=i("f0Cb"),s=i("bv9b"),d=i("vxfF"),p=i("jaxi"),g=i("Grxv"),h=i("Xi0T"),u=i("4/q7"),v=i("ClZT"),y=i("ZzPI"),x=i("w76M"),f=i("VI6+"),b=i("XPKZ"),S=i("Gkpw"),w=i("we1Z"),C=i("QE0S"),M=i("k8YA"),U=i("WYlB"),O=i("Qlw+"),E=i("Kb4U"),P=i("3Pt+"),D=i("dlKe"),_=i("bSwM"),I=i("w4ga"),F=i("7pIB"),k=i("fXoL");let B=(()=>{class e{}return e.\u0275mod=k["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=k["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[a.CommonModule,l.b,r.b,o.b,c.b,m.b,s.b,d.g,g.a,p.c,u.b,v.b,y.b,x.b,f.b,b.b,S.a,w.a,C.b,M.b,U.b,O.a,E.b,P.p,P.E,D.b,h.a,_.b,I.b,F.c],a.CommonModule,l.b,r.b,o.b,c.b,m.b,s.b,d.g,g.a,p.c,u.b,v.b,y.b,x.b,f.b,b.b,S.a,w.a,C.b,M.b,U.b,O.a,E.b,P.p,P.E]}),e})();var j=i("FKr1"),V=i("Wp6s"),T=i("5+WD"),A=i("MutI"),N=i("XhcP"),R=i("0IaG"),z=i("5gEQ"),K=i("XNiG"),H=i("me71");function L(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"div",13),k["\u0275\u0275element"](1,"span",14),k["\u0275\u0275elementEnd"]())}function W(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"div"),k["\u0275\u0275elementStart"](1,"div",7),k["\u0275\u0275elementStart"](2,"div",8),k["\u0275\u0275elementStart"](3,"div",9),k["\u0275\u0275element"](4,"app-user-image",10),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](5,"div",11),k["\u0275\u0275elementStart"](6,"span"),k["\u0275\u0275text"](7," Sent By "),k["\u0275\u0275elementStart"](8,"strong"),k["\u0275\u0275text"](9),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](10,"span"),k["\u0275\u0275text"](11),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](12,L,2,0,"div",12),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,i=t.last;k["\u0275\u0275advance"](4),k["\u0275\u0275property"]("id",e.oid),k["\u0275\u0275advance"](5),k["\u0275\u0275textInterpolate"](e["Sent By"]),k["\u0275\u0275advance"](2),k["\u0275\u0275textInterpolate1"](" ",e["Sent On"]," "),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",!i)}}function Y(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"div"),k["\u0275\u0275elementStart"](1,"div",1),k["\u0275\u0275elementStart"](2,"span",2),k["\u0275\u0275text"](3,"Invoice Mail History"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](4,"span",3),k["\u0275\u0275listener"]("click",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"]().closeDialog()})),k["\u0275\u0275elementStart"](5,"mat-icon"),k["\u0275\u0275text"](6,"close"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275element"](7,"hr",4),k["\u0275\u0275elementStart"](8,"div",5),k["\u0275\u0275template"](9,W,13,4,"div",6),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}if(2&e){const e=k["\u0275\u0275nextContext"]();k["\u0275\u0275advance"](9),k["\u0275\u0275property"]("ngForOf",e.data)}}function X(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"div"),k["\u0275\u0275elementStart"](1,"div",15),k["\u0275\u0275elementStart"](2,"mat-icon",16),k["\u0275\u0275text"](3,"running_with_errors"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](4,"span",17),k["\u0275\u0275text"](5,"No History Found!"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]())}let G=(()=>{class e{constructor(e,t){this.dialogRe=e,this.logData=t,this.logHistoryFound=!1}ngOnInit(){this.data=this.logData.logData,this.data="string"==typeof this.data?JSON.parse(this.data):this.data,this.logHistoryFound=this.data.length>0,this.columnHeaders=Object.keys(this.data[0])}closeDialog(){this.dialogRe.close()}}return e.\u0275fac=function(t){return new(t||e)(k["\u0275\u0275directiveInject"](R.h),k["\u0275\u0275directiveInject"](R.a))},e.\u0275cmp=k["\u0275\u0275defineComponent"]({type:e,selectors:[["app-mail-history-log"]],decls:2,vars:2,consts:[[4,"ngIf"],[1,"mt-4",2,"color","#cf0001","display","flex","justify-content","space-between","align-items","center"],[1,"pl-3",2,"font-size","16px"],[1,"pr-3",2,"cursor","pointer",3,"click"],[1,"mt-2","mb-2"],[1,"timeline"],[4,"ngFor","ngForOf"],[1,"container"],[1,"displayFlex","mb-1","mt-1"],[1,"image-container"],["imgHeight","40px","imgWidth","40px",3,"id"],[2,"display","flex","flex-direction","column","margin-top","7px","padding-left","10px"],["class","line-container",4,"ngIf"],[1,"line-container"],[1,"vertical-line"],[1,"row","mt-5","d-flex","justify-content-center"],[1,"no-history-icon"],[1,"pl-2","no-history"]],template:function(e,t){1&e&&(k["\u0275\u0275template"](0,Y,10,1,"div",0),k["\u0275\u0275template"](1,X,6,0,"div",0)),2&e&&(k["\u0275\u0275property"]("ngIf",1==t.logHistoryFound),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",0==t.logHistoryFound))},directives:[a.NgIf,r.a,a.NgForOf,H.a],styles:[".container[_ngcontent-%COMP%]{display:flex;align-items:center;flex-direction:column}.image-container[_ngcontent-%COMP%]{flex:0}.line-container[_ngcontent-%COMP%]{flex:0;margin-right:10px}.vertical-line[_ngcontent-%COMP%]{border-left:1px solid #000;height:100px;margin-right:138px}.timeline[_ngcontent-%COMP%]{margin-top:2px;display:grid;justify-content:left}.displayFlex[_ngcontent-%COMP%]{display:flex}"]}),e})();var $=i("m5YA"),J=i("wO+i"),Q=i("R0Ic"),Z=i("dNgK"),q=i("XXEo"),ee=i("JLuW"),te=i("6t9p"),ie=i("wd/R");let ne=(()=>{class e{transform(e){return e?(e=new Date(e),this.isCurrentYear=ie(e).isSame(new Date,"year"),this.dateResult=this.isCurrentYear?ie(e).format("DD/MM hh:mm A"):ie(e).format("DD/MM/YY"),this.dateResult):""}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=k["\u0275\u0275definePipe"]({name:"dateFormat",type:e,pure:!0}),e})();var ae=i("jhN1");let le=(()=>{class e{constructor(e){this.sanitizer=e}transform(e){return this.sanitizer.bypassSecurityTrustHtml(e)}}return e.\u0275fac=function(t){return new(t||e)(k["\u0275\u0275directiveInject"](ae.c))},e.\u0275pipe=k["\u0275\u0275definePipe"]({name:"safeHTML",type:e,pure:!0}),e})(),re=(()=>{class e{transform(e){let t="";switch(e){case"docx":t="https://assets.kebs.app/docx.svg";break;case"doc":t="https://assets.kebs.app/doc.svg";break;case"pdf":t="https://assets.kebs.app/pdf.svg";break;case"png":t="https://assets.kebs.app/png.svg";break;case"csv":t="https://assets.kebs.app/csv.svg";break;case"xlsx":t="https://assets.kebs.app/xlsx.svg";break;case"jpg":t="https://assets.kebs.app/jpg.svg";break;case"jpeg":t="https://assets.kebs.app/jpeg.svg";break;case"txt":t="https://assets.kebs.app/txt.svg";break;case"zip":t="https://assets.kebs.app/zip.svg";break;case"mpp":t="https://assets.kebs.app/mpp.png";break;case"folder":t="https://assets.kebs.app/folder.svg"}return t}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=k["\u0275\u0275definePipe"]({name:"fileIcon",type:e,pure:!0}),e})();const oe=["fileInput"];function ce(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"span"),k["\u0275\u0275text"](1,","),k["\u0275\u0275elementEnd"]())}function me(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"div"),k["\u0275\u0275elementStart"](1,"span",26),k["\u0275\u0275text"](2),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](3,ce,2,0,"span",27),k["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,i=t.index,n=k["\u0275\u0275nextContext"](3);k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("matTooltip",null==e.emailAddress?null:e.emailAddress.address),k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate"](null==e.emailAddress?null:e.emailAddress.name),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",i!=n.mailUtilityService.mailUiData.selectedMailDataUi.toRecipients.length-1)}}function se(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"div",24),k["\u0275\u0275text"](1," To : \xa0 "),k["\u0275\u0275template"](2,me,4,3,"div",25),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](2);k["\u0275\u0275advance"](2),k["\u0275\u0275property"]("ngForOf",e.mailUtilityService.mailUiData.selectedMailDataUi.toRecipients)}}function de(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"span"),k["\u0275\u0275text"](1,","),k["\u0275\u0275elementEnd"]())}function pe(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"div"),k["\u0275\u0275elementStart"](1,"span",26),k["\u0275\u0275text"](2),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](3,de,2,0,"span",27),k["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,i=t.index,n=k["\u0275\u0275nextContext"](3);k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("matTooltip",null==e.emailAddress?null:e.emailAddress.address),k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate"](null==e.emailAddress?null:e.emailAddress.name),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",i!=n.mailUtilityService.mailUiData.selectedMailDataUi.ccRecipients.length-1)}}function ge(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"div",24),k["\u0275\u0275text"](1," Cc : \xa0 "),k["\u0275\u0275template"](2,pe,4,3,"div",25),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](2);k["\u0275\u0275advance"](2),k["\u0275\u0275property"]("ngForOf",e.mailUtilityService.mailUiData.selectedMailDataUi.ccRecipients)}}function he(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"div",2),k["\u0275\u0275elementStart"](1,"div",3),k["\u0275\u0275elementStart"](2,"div",4),k["\u0275\u0275elementStart"](3,"mat-button-toggle-group",5),k["\u0275\u0275elementStart"](4,"mat-button-toggle",6),k["\u0275\u0275listener"]("change",(function(t){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"]().actionToggleBtn(t)})),k["\u0275\u0275elementStart"](5,"span"),k["\u0275\u0275elementStart"](6,"mat-icon",7),k["\u0275\u0275text"](7,"reply"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](8,"span",8),k["\u0275\u0275text"](9,"Reply"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](10,"mat-button-toggle",9),k["\u0275\u0275listener"]("change",(function(t){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"]().actionToggleBtn(t)})),k["\u0275\u0275elementStart"](11,"span"),k["\u0275\u0275elementStart"](12,"mat-icon",7),k["\u0275\u0275text"](13,"reply_all"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](14,"span",8),k["\u0275\u0275text"](15,"Reply all"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](16,"mat-button-toggle",10),k["\u0275\u0275listener"]("change",(function(t){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"]().actionToggleBtn(t)})),k["\u0275\u0275elementStart"](17,"span"),k["\u0275\u0275elementStart"](18,"mat-icon",7),k["\u0275\u0275text"](19,"arrow_forward"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](20,"span",8),k["\u0275\u0275text"](21,"Forward"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](22,"div",11),k["\u0275\u0275elementStart"](23,"div",12),k["\u0275\u0275text"](24),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](25,"div",13),k["\u0275\u0275elementStart"](26,"div",14),k["\u0275\u0275element"](27,"img",15),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](28,"div",16),k["\u0275\u0275elementStart"](29,"div",17),k["\u0275\u0275elementStart"](30,"span",18),k["\u0275\u0275text"](31),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](32,"span"),k["\u0275\u0275text"](33),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](34,"div",19),k["\u0275\u0275elementStart"](35,"div",20),k["\u0275\u0275text"](36),k["\u0275\u0275pipe"](37,"dateFormat"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](38,se,3,1,"div",21),k["\u0275\u0275template"](39,ge,3,1,"div",21),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](40,"div",22),k["\u0275\u0275element"](41,"span",23),k["\u0275\u0275pipe"](42,"safeHTML"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}if(2&e){const e=k["\u0275\u0275nextContext"]();k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngClass",e.animate?"slide-in-top":""),k["\u0275\u0275advance"](23),k["\u0275\u0275textInterpolate1"](" ",e.mailUtilityService.mailUiData.selectedMailDataUi.subject?e.mailUtilityService.mailUiData.selectedMailDataUi.subject:""," "),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngClass",e.animate?"slide-in-top":""),k["\u0275\u0275advance"](2),k["\u0275\u0275styleProp"]("height",e.imgHeight)("width",e.imgWidth)("border-width",e.borderWidth)("border-style",e.borderStyle)("border-color",e.borderColor),k["\u0275\u0275property"]("src",e.imgSrc,k["\u0275\u0275sanitizeUrl"]),k["\u0275\u0275advance"](4),k["\u0275\u0275textInterpolate1"](" ",e.mailUtilityService.mailUiData.selectedMailDataUi.from.emailAddress.name?e.mailUtilityService.mailUiData.selectedMailDataUi.from.emailAddress.name:""," "),k["\u0275\u0275advance"](2),k["\u0275\u0275textInterpolate1"](" < ",e.mailUtilityService.mailUiData.selectedMailDataUi.from.emailAddress.address?e.mailUtilityService.mailUiData.selectedMailDataUi.from.emailAddress.address:""," > "),k["\u0275\u0275advance"](3),k["\u0275\u0275textInterpolate1"](" ",k["\u0275\u0275pipeBind1"](37,21,e.mailUtilityService.mailUiData.selectedMailDataUi.receivedDateTime)," "),k["\u0275\u0275advance"](2),k["\u0275\u0275property"]("ngIf",e.mailUtilityService.mailUiData.selectedMailDataUi.toRecipients.length>0),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.mailUtilityService.mailUiData.selectedMailDataUi.ccRecipients.length>0),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngClass",e.animate?"slide-from-down":""),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("innerHTML",k["\u0275\u0275pipeBind1"](42,23,e.mailUtilityService.mailUiData.selectedBodyDataUi),k["\u0275\u0275sanitizeHtml"])}}function ue(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"div",72),k["\u0275\u0275text"](1),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](2);k["\u0275\u0275property"]("matTooltip",e.mailUtilityService.mailUiData.title),k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate1"](" ",e.mailUtilityService.mailUiData.title," ")}}function ve(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"mat-button-toggle",37),k["\u0275\u0275listener"]("change",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"](2).showHistory()})),k["\u0275\u0275elementStart"](1,"span"),k["\u0275\u0275elementStart"](2,"mat-icon",7),k["\u0275\u0275text"](3,"history"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](4,"span",8),k["\u0275\u0275text"](5,"History"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}}function ye(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"div",73),k["\u0275\u0275elementStart"](1,"button",74),k["\u0275\u0275listener"]("click",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"](2).saveRecipientMailIds("toMailId")})),k["\u0275\u0275elementStart"](2,"mat-icon",75),k["\u0275\u0275text"](3," save "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}}function xe(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"div",79),k["\u0275\u0275elementStart"](1,"button",74),k["\u0275\u0275listener"]("click",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"](3).saveRecipientMailIds("ccMailId")})),k["\u0275\u0275elementStart"](2,"mat-icon",75),k["\u0275\u0275text"](3," save "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}}const fe=function(){return[32,188,186,13,9]};function be(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"div",44),k["\u0275\u0275elementStart"](1,"div",41),k["\u0275\u0275text"](2," Cc : "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](3,"div",76),k["\u0275\u0275element"](4,"tag-input",77),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](5,xe,4,0,"div",78),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](2);k["\u0275\u0275advance"](4),k["\u0275\u0275property"]("separatorKeyCodes",k["\u0275\u0275pureFunction0"](13,fe))("errorMessages",e.errorMessages)("validators",e.validators)("editable",!0)("placeholder","Add email")("secondaryPlaceholder","Enter Cc")("clearOnBlur",!0)("addOnPaste",!0)("addOnBlur",!0)("pasteSplitPattern",e.splitPattern)("identifyBy","id")("displayBy","name"),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.mailUtilityService.mUtilityData.isCcRecipientFieldHasSaveButton)}}function Se(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"div",79),k["\u0275\u0275elementStart"](1,"button",74),k["\u0275\u0275listener"]("click",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"](3).saveRecipientMailIds("bccMailIds")})),k["\u0275\u0275elementStart"](2,"mat-icon",75),k["\u0275\u0275text"](3," save "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}}function we(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"div",44),k["\u0275\u0275elementStart"](1,"div",41),k["\u0275\u0275text"](2," Bcc : "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](3,"div",76),k["\u0275\u0275element"](4,"tag-input",80),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](5,Se,4,0,"div",78),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](2);k["\u0275\u0275advance"](4),k["\u0275\u0275property"]("separatorKeyCodes",k["\u0275\u0275pureFunction0"](13,fe))("errorMessages",e.errorMessages)("validators",e.validators)("editable",!0)("placeholder","Add email")("secondaryPlaceholder","Enter Bcc")("clearOnBlur",!0)("addOnPaste",!0)("addOnBlur",!0)("pasteSplitPattern",e.splitPattern)("identifyBy","id")("displayBy","name"),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.mailUtilityService.mUtilityData.isCcRecipientFieldHasSaveButton)}}function Ce(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"div",44),k["\u0275\u0275elementStart"](1,"div",41),k["\u0275\u0275text"](2," Customer SPOC "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](3,"div",81),k["\u0275\u0275elementStart"](4,"div",82),k["\u0275\u0275element"](5,"input",83),k["\u0275\u0275elementEnd"](),k["\u0275\u0275element"](6,"hr",84),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](7,"div",73),k["\u0275\u0275elementStart"](8,"button",85),k["\u0275\u0275listener"]("click",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"](2).saveSpocName()})),k["\u0275\u0275elementStart"](9,"mat-icon",75),k["\u0275\u0275text"](10," save "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}}function Me(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"span",88),k["\u0275\u0275element"](1,"img",89),k["\u0275\u0275pipe"](2,"fileIcon"),k["\u0275\u0275elementStart"](3,"div",90),k["\u0275\u0275listener"]("click",(function(){k["\u0275\u0275restoreView"](e);const i=t.$implicit;return k["\u0275\u0275nextContext"](3).viewFile(i)})),k["\u0275\u0275text"](4),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](5,"mat-checkbox",91),k["\u0275\u0275listener"]("change",(function(i){k["\u0275\u0275restoreView"](e);const n=t.$implicit,a=k["\u0275\u0275nextContext"](3);return n.checked=i.checked,a.handleInputChange()})),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("src",k["\u0275\u0275pipeBind1"](2,4,null==e?null:e.file_format),k["\u0275\u0275sanitizeUrl"]),k["\u0275\u0275advance"](2),k["\u0275\u0275property"]("matTooltip",e.file_name),k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate"](e.file_name),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("checked",e.checked)}}function Ue(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"div",40),k["\u0275\u0275elementStart"](1,"div",41),k["\u0275\u0275text"](2),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](3,"div",86),k["\u0275\u0275template"](4,Me,6,6,"span",87),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](2);k["\u0275\u0275advance"](2),k["\u0275\u0275textInterpolate2"](" Attachments (",e.checkedFilesLength," / ",e.selectedFiles.length,") "),k["\u0275\u0275advance"](2),k["\u0275\u0275property"]("ngForOf",e.selectedFiles.slice(0,e.visibleCount))}}function Oe(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"button",93),k["\u0275\u0275listener"]("click",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"](3).toggleShowMore()})),k["\u0275\u0275text"](1),k["\u0275\u0275elementEnd"]()}if(2&e){const e=k["\u0275\u0275nextContext"](3);k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate"](e.showMoreText)}}function Ee(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"div",40),k["\u0275\u0275element"](1,"div",41),k["\u0275\u0275elementStart"](2,"div",86),k["\u0275\u0275template"](3,Oe,2,1,"button",92),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](2);k["\u0275\u0275advance"](3),k["\u0275\u0275property"]("ngIf",e.selectedFiles.length>5)}}const Pe=function(){return{color:"#2196f3"}};function De(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"dxi-item"),k["\u0275\u0275elementStart"](1,"div",48),k["\u0275\u0275elementStart"](2,"input",94,95),k["\u0275\u0275listener"]("change",(function(t){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"](2).onFileSelected(t)})),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](4,"button",96),k["\u0275\u0275listener"]("click",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"](2).triggerFileInput()}))("mouseenter",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"](2).showFilesList=!0}))("mouseleave",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"](2).showFilesList=!1})),k["\u0275\u0275elementStart"](5,"mat-icon"),k["\u0275\u0275text"](6,"attach_file"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}if(2&e){const e=k["\u0275\u0275nextContext"](2);k["\u0275\u0275advance"](2),k["\u0275\u0275property"]("uploader",e.uploader),k["\u0275\u0275advance"](2),k["\u0275\u0275property"]("ngStyle",e.showAttachment&&k["\u0275\u0275pureFunction0"](2,Pe))}}function _e(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"span"),k["\u0275\u0275text"](1),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](3);k["\u0275\u0275advance"](1),k["\u0275\u0275textInterpolate"](e.selectedFileNames.length)}}function Ie(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"dxi-item"),k["\u0275\u0275elementStart"](1,"div",48),k["\u0275\u0275elementStart"](2,"input",97,95),k["\u0275\u0275listener"]("change",(function(t){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"](2).onFileSelected(t)})),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](4,"button",98),k["\u0275\u0275listener"]("click",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"](2).triggerFileInput()}))("mouseenter",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"](2).showFilesList=!0}))("mouseleave",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"](2).showFilesList=!1})),k["\u0275\u0275elementStart"](5,"mat-icon"),k["\u0275\u0275text"](6,"attach_file"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](7,_e,2,1,"span",27),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}if(2&e){const e=k["\u0275\u0275nextContext"](2);k["\u0275\u0275advance"](4),k["\u0275\u0275property"]("ngStyle",e.showAttachment&&k["\u0275\u0275pureFunction0"](3,Pe))("matTooltip",e.showFilesList&&e.selectedFileNames.length>0?e.filesToTooltip(e.selectedFileNames):"Add Attachment"),k["\u0275\u0275advance"](3),k["\u0275\u0275property"]("ngIf",!e.showFilesList&&e.selectedFileNames.length>0)}}const Fe=function(){return{color:"#2196f3"}},ke=function(){return[13]},Be=function(e,t,i,n){return{"auto-layout":e,"fixed-layout":t,"dx-html-container-expanded":i,"dx-html-container":n}};function je(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"form",28),k["\u0275\u0275elementStart"](1,"div",29),k["\u0275\u0275elementStart"](2,"div",30),k["\u0275\u0275elementStart"](3,"button",31),k["\u0275\u0275listener"]("click",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"]().toggleRecipients()})),k["\u0275\u0275elementStart"](4,"mat-icon",32),k["\u0275\u0275text"](5),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](6,"button",33),k["\u0275\u0275listener"]("click",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"]().expandBody()})),k["\u0275\u0275elementStart"](7,"mat-icon",32),k["\u0275\u0275text"](8," fullscreen "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](9,ue,2,2,"div",34),k["\u0275\u0275elementStart"](10,"div",35),k["\u0275\u0275elementStart"](11,"mat-button-toggle-group",5),k["\u0275\u0275elementStart"](12,"mat-button-toggle",36),k["\u0275\u0275listener"]("change",(function(t){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"]().createMailToggleBtn(t)})),k["\u0275\u0275elementStart"](13,"span"),k["\u0275\u0275elementStart"](14,"mat-icon",7),k["\u0275\u0275text"](15,"send"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](16,"span",8),k["\u0275\u0275text"](17,"Send"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](18,"mat-button-toggle",37),k["\u0275\u0275listener"]("change",(function(t){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"]().createMailToggleBtn(t)})),k["\u0275\u0275elementStart"](19,"span"),k["\u0275\u0275elementStart"](20,"mat-icon",7),k["\u0275\u0275text"](21,"delete"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](22,"span",8),k["\u0275\u0275text"](23,"Discard"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](24,ve,6,0,"mat-button-toggle",38),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275element"](25,"mat-divider",39),k["\u0275\u0275elementStart"](26,"div"),k["\u0275\u0275elementStart"](27,"div",40),k["\u0275\u0275elementStart"](28,"div",41),k["\u0275\u0275text"](29," From : "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](30,"div",42),k["\u0275\u0275element"](31,"tag-input",43),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](32,"div",44),k["\u0275\u0275elementStart"](33,"div",41),k["\u0275\u0275text"](34," To : "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](35,"div",45),k["\u0275\u0275element"](36,"tag-input",46),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](37,ye,4,0,"div",47),k["\u0275\u0275elementStart"](38,"div",48),k["\u0275\u0275elementStart"](39,"button",49),k["\u0275\u0275listener"]("click",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"]().toggleCcBccField()})),k["\u0275\u0275text"](40,"Cc & Bcc"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](41,be,6,14,"div",50),k["\u0275\u0275template"](42,we,6,14,"div",50),k["\u0275\u0275elementStart"](43,"div",40),k["\u0275\u0275elementStart"](44,"div",41),k["\u0275\u0275text"](45," Subject "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](46,"div",42),k["\u0275\u0275element"](47,"tag-input",51),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](48,Ce,11,0,"div",50),k["\u0275\u0275template"](49,Ue,5,3,"div",52),k["\u0275\u0275template"](50,Ee,4,1,"div",52),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](51,"div",53),k["\u0275\u0275elementStart"](52,"dx-html-editor",54),k["\u0275\u0275elementStart"](53,"dxo-toolbar",55),k["\u0275\u0275element"](54,"dxi-item",56),k["\u0275\u0275element"](55,"dxi-item",57),k["\u0275\u0275element"](56,"dxi-item",58),k["\u0275\u0275element"](57,"dxi-item",59),k["\u0275\u0275element"](58,"dxi-item",60),k["\u0275\u0275element"](59,"dxi-item",61),k["\u0275\u0275element"](60,"dxi-item",62),k["\u0275\u0275element"](61,"dxi-item",63),k["\u0275\u0275element"](62,"dxi-item",64),k["\u0275\u0275element"](63,"dxi-item",60),k["\u0275\u0275element"](64,"dxi-item",65),k["\u0275\u0275element"](65,"dxi-item",66),k["\u0275\u0275element"](66,"dxi-item",60),k["\u0275\u0275element"](67,"dxi-item",67),k["\u0275\u0275element"](68,"dxi-item",68),k["\u0275\u0275element"](69,"dxi-item",69),k["\u0275\u0275element"](70,"dxi-item",60),k["\u0275\u0275element"](71,"dxi-item",70),k["\u0275\u0275template"](72,De,7,3,"dxi-item",27),k["\u0275\u0275template"](73,Ie,8,4,"dxi-item",27),k["\u0275\u0275elementEnd"](),k["\u0275\u0275element"](74,"dxo-media-resizing",71),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}if(2&e){const e=k["\u0275\u0275nextContext"]();k["\u0275\u0275property"]("formGroup",e.mailUtilityService.mailUiData.mailInputFields),k["\u0275\u0275advance"](3),k["\u0275\u0275property"]("matTooltip",e.showRecipientFields?"Hide recipients":"View recipients"),k["\u0275\u0275advance"](2),k["\u0275\u0275textInterpolate1"](" ",e.showRecipientFields?"keyboard_arrow_down":"keyboard_arrow_right"," "),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("matTooltip",e.mailUtilityService.mailUiData.isExpandBody?"Switch Normal view":"Switch expanded view")("ngClass",e.mailUtilityService.mailUiData.isExpandBody?"view-button-active":"view-button-inactive"),k["\u0275\u0275advance"](3),k["\u0275\u0275property"]("ngIf",e.mailUtilityService.mailUiData.showTitle),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngClass",e.mailUtilityService.mailUiData.isExpandBody?"col-lg-4":"col-lg-5"),k["\u0275\u0275advance"](14),k["\u0275\u0275property"]("ngIf",e.mailUtilityService.mailUiData.showHistoryButton),k["\u0275\u0275advance"](2),k["\u0275\u0275property"]("@slideInOut",e.showRecipientFields?"in":"out"),k["\u0275\u0275advance"](5),k["\u0275\u0275property"]("separatorKeyCodes",k["\u0275\u0275pureFunction0"](53,fe))("errorMessages",e.errorMessages)("validators",e.validators)("editable",!0)("placeholder","Add email")("secondaryPlaceholder","Enter to address")("clearOnBlur",!0)("addOnPaste",!0)("addOnBlur",!0)("pasteSplitPattern",e.splitPattern)("identifyBy","id")("displayBy","name")("disable",!0),k["\u0275\u0275advance"](5),k["\u0275\u0275property"]("separatorKeyCodes",k["\u0275\u0275pureFunction0"](54,fe))("errorMessages",e.errorMessages)("validators",e.validators)("editable",!0)("placeholder","Add email")("secondaryPlaceholder","Enter to address")("clearOnBlur",!0)("addOnPaste",!0)("addOnBlur",!0)("pasteSplitPattern",e.splitPattern)("identifyBy","id")("displayBy","name"),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.mailUtilityService.mUtilityData.isToRecipientFieldHasSaveButton),k["\u0275\u0275advance"](2),k["\u0275\u0275property"]("ngStyle",e.showCcBccField&&k["\u0275\u0275pureFunction0"](55,Fe)),k["\u0275\u0275advance"](2),k["\u0275\u0275property"]("ngIf",e.showCcBccField),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.showCcBccField),k["\u0275\u0275advance"](5),k["\u0275\u0275property"]("separatorKeyCodes",k["\u0275\u0275pureFunction0"](56,ke))("editable",!0)("secondaryPlaceholder","Subject")("clearOnBlur",!0)("addOnPaste",!0)("addOnBlur",!0)("pasteSplitPattern",e.splitPattern),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.mailUtilityService.mailUiData.showSpocName),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.mailUtilityService.mUtilityData.autoAttachmentApplicable),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e.mailUtilityService.mUtilityData.autoAttachmentApplicable),k["\u0275\u0275advance"](2),k["\u0275\u0275property"]("ngClass",k["\u0275\u0275pureFunction4"](57,Be,e.mailUtilityService.mUtilityData.isTableLayoutAuto,!e.mailUtilityService.mUtilityData.isTableLayoutAuto,e.mailUtilityService.mailUiData.isExpandBody,!e.mailUtilityService.mailUiData.isExpandBody)),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("multiline",e.isMultiline),k["\u0275\u0275advance"](19),k["\u0275\u0275property"]("ngIf",e.mailUtilityService.mUtilityData.autoAttachmentApplicable),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",!e.mailUtilityService.mUtilityData.autoAttachmentApplicable),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("enabled",!0)}}function Ve(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"div",2),k["\u0275\u0275elementStart"](1,"div",99),k["\u0275\u0275elementStart"](2,"div",100),k["\u0275\u0275text"](3,"Select a item to read"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](4,"div",101),k["\u0275\u0275text"](5,"Nothing is selected"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](6,"div",102),k["\u0275\u0275element"](7,"img",103),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]())}let Te=(()=>{class e{constructor(e,t,i,n,a){if(this.mailUtilityService=e,this.snackBar=t,this.openMatDialog=i,this._login=n,this._sharedService=a,this.contextId=null,this.isDialogOpened=!1,this.filesUploaded=[],this.URL="/api/exPrimary/uploadObjectFromDevice",this.selectedFiles=[],this.selectedFileNames=[],this.showFilesList=!1,this.showCcBccField=!1,this.showAttachment=!1,this.showRecipientFields=!0,this.animate=!0,this.isMultiline=!1,this._onDestroy=new K.b,this.checkedFilesLength=0,this.visibleCount=5,this.showMoreText="view more",this.imgWidth="37px",this.imgHeight="37px",this.borderWidth="0px",this.borderStyle="solid",this.borderColor="#000000",this.imgSrc="https://assets.kebs.app/images/User.png",this.validators=[this.must_be_email],this.errorMessages={must_be_email:"Please be sure to use a valid email format"},this.clicked=!1,this.containsSpecialChars=e=>/[`!@#$%^&*()_+\-=\[\]{};':"\\|,<>\/?~]/.test(e),this.retrieveUploadedFiles=(e,t)=>{this._sharedService.retrieveUploadedObjects(e,t).pipe(Object(J.a)("data")).subscribe(e=>{const t=e.map(e=>Object.assign(Object.assign({},e),{checked:!0}));this.selectedFiles.push(...t),this.mailUtilityService.mUtilityData.filesAttached=this.selectedFiles,this.handleInputChange()},e=>{console.error(e)})},this.handleViewMode(),this.mailUtilityService.mUtilityData.autoAttachmentApplicable){this.destinationBucket=this.mailUtilityService.mUtilityData.destinationBucket,this.routingKey=this.mailUtilityService.mUtilityData.routingKey,this.contextId=this.mailUtilityService.mUtilityData.contextId;let e=this.mailUtilityService.mUtilityData.attachmentRetrievalDetails;for(let t of e)t.contextId&&this.retrieveUploadedFiles(t.destinationBucket,t.contextId);this.uploader=new F.d({url:this.URL,authToken:"Bearer "+this._login.getToken(),disableMultipart:!1,headers:[{name:"context-id",value:this.contextId},{name:"routing-key",value:this.routingKey},{name:"bucket-name",value:this.destinationBucket}],maxFileSize:********}),this.detectUploadChanges()}}must_be_email(e){return""==e.value.length||/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,3}$/i.test(e.value)?null:{must_be_email:!0}}ngOnInit(){this.selectedFiles=[],this.selectedFileNames=[]}ngAfterViewInit(){}expandBody(){this.mailUtilityService.mailUiData.isExpandBody=!this.mailUtilityService.mailUiData.isExpandBody}handleViewMode(){this.mailUtilityService.mUtilityData.isLoading=!0,this.mailUtilityService.mUtilityData.currentMailMode&&"create"==this.mailUtilityService.mUtilityData.currentMailMode.mode||(this.mailUtilityService.mUtilityData.currentMailMode.mode="not selected"),this.mailUtilityService.mailFunctions.switchViewMode(),this.mailUtilityService.mUtilityData.isLoading=!1}toggleCcBccField(){this.showCcBccField=!this.showCcBccField}triggerFileInput(){this.showAttachment=!this.showAttachment,this.fileInput.nativeElement.click()}onFileSelected(e){var t;const i=e.target.files;for(let n=0;n<i.length;n++)this.selectedFiles.push(i[n]),console.log("Selected file:",i[n].name);this.mailUtilityService.mUtilityData.filesAttached=this.selectedFiles,this.selectedFileNames=null===(t=this.selectedFiles)||void 0===t?void 0:t.map(e=>e.name),this.handleFileVisibility()}handleInputChange(){var e,t;this.mailUtilityService.mUtilityData.filesAttached=null===(e=this.selectedFiles)||void 0===e?void 0:e.filter(e=>1==e.checked),this.checkedFilesLength=null===(t=this.selectedFiles)||void 0===t?void 0:t.filter(e=>e.checked).length}handleFileVisibility(){"view less"==this.showMoreText&&(this.visibleCount=this.selectedFiles.length)}filesToTooltip(e){return e.map(e=>`"${e}"`).join(",  ")}toggleRecipients(){this.showRecipientFields=!this.showRecipientFields}actionToggleBtn(e){this.mailUtilityService.mUtilityData.currentMailMode={mode:"action",actionType:e.value},this.mailUtilityService.mailFunctions.switchViewMode()}createMailToggleBtn(e){if(console.log(this.mailUtilityService.mUtilityData.currentMailMode),"send"==e.value){let e;"action"==this.mailUtilityService.mUtilityData.currentMailMode.mode?e=this.mailUtilityService.mUtilityData.currentMailMode.actionType:"create"==this.mailUtilityService.mUtilityData.currentMailMode.mode&&(e="createNew"),this.mailUtilityService.sendSweetAlert("Are you sure you want to send this email?","").then(t=>{t.value&&this.mailUtilityService.mailFunctions.initiateMailWriteOperation(e)})}else"discard"==e.value&&this.mailUtilityService.confirmSweetAlert("Are you sure want to discard this mail?","").then(e=>{e.value&&(this.mailUtilityService.mailUiData.mailInputFields.reset(),this.mailUtilityService.mUtilityData.currentMailMode={mode:"not selected"},this.mailUtilityService.mailFunctions.switchViewMode())})}showHistory(){return Object(n.c)(this,void 0,void 0,(function*(){if(this.mailUtilityService.mailUiData.showHistoryButton&&this.mailUtilityService.mUtilityData.retrieveHistoryFromApi)if(this.mailUtilityService.mUtilityData.retrieveHistoryFromApi.url)try{let e=this.mailUtilityService.mUtilityData.retrieveHistoryFromApi.paramsArr;const t=yield this.mailUtilityService.getMailHistory(e,this.mailUtilityService.mUtilityData.retrieveHistoryFromApi.url,this.mailUtilityService.mUtilityData.saveHistoryInKebsApiData.jwtToken);if("E"==t.messType)return this.snackBar.open(t.messText,"Dismiss");this.clicked||(this.clicked=!0,this.openMatDialog.open(G,{data:{logData:t.data},height:"97%",width:"35%",position:{top:"11px",right:"9px"}}).afterClosed().subscribe(()=>{this.clicked=!1}))}catch(e){this.snackBar.open("No Mail History Found","Dismiss",{duration:2e3})}else this.snackBar.open("No Mail History Found","Dismiss",{duration:2e3})}))}saveRecipientMailIds(e){this.mailUtilityService.mUtilityData.saveRecipientMailIdType={type:e,uniqueId:this.mailUtilityService.mUtilityData.selectedNewMailTemplateData.uniqueId,mailFields:this.mailUtilityService.mailUiData.mailInputFields},this.mailUtilityService.mUtilityData.saveRecipientMailIds()}saveSpocName(){this.mailUtilityService.mUtilityData.saveSpocName()}toggleShowMore(){this.visibleCount===this.selectedFiles.length?(this.visibleCount=5,this.showMoreText="view more"):(this.visibleCount=this.selectedFiles.length,this.showMoreText="view less")}ngOnDestroy(){}detectUploadChanges(){this.uploader.onProgressItem=e=>{console.log("in Progress")},this.uploader.onCompleteItem=(e,t,i,n)=>{this.uploader.removeFromQueue(e);let a=JSON.parse(t).data;a.isUploaded=!0,a.checked=!0,this.selectedFiles.push(a),this.mailUtilityService.mUtilityData.filesAttached=this.selectedFiles,this.handleFileVisibility(),this.handleInputChange()},this.uploader.onWhenAddingFileFailed=e=>{this.snackBar.open("Failed to upload file","dismiss",{duration:1e3})},this.uploader.onAfterAddingFile=e=>{{let t="";this.containsSpecialChars(e.file.name)?(t=e.file.name.replace(/[^a-zA-Z0-9. ]/g,"_"),e.file.name.split(".").pop(),this.uploader.uploadItem(e)):(t=e.file.name,e.file.name.split(".").pop(),this.uploader.uploadItem(e))}}}viewFile(e){let t=e.cdn_link,i=null;0==this.isDialogOpened&&(this.isDialogOpened=!0,this._sharedService.getDownloadUrl(t).subscribe(t=>{i=this.openMatDialog.open($.a,{width:"100%",height:"100%",data:{selectedFileUrl:t.data,fileFormat:e.file_format,expHeaderId:this.expHeaderId}}),i.afterClosed().subscribe(e=>{this.isDialogOpened=!1})}))}}return e.\u0275fac=function(t){return new(t||e)(k["\u0275\u0275directiveInject"](z.a),k["\u0275\u0275directiveInject"](Z.a),k["\u0275\u0275directiveInject"](R.b),k["\u0275\u0275directiveInject"](q.a),k["\u0275\u0275directiveInject"](ee.a))},e.\u0275cmp=k["\u0275\u0275defineComponent"]({type:e,selectors:[["app-mail-content-review"]],viewQuery:function(e,t){if(1&e&&k["\u0275\u0275viewQuery"](oe,!0),2&e){let e;k["\u0275\u0275queryRefresh"](e=k["\u0275\u0275loadQuery"]())&&(t.fileInput=e.first)}},decls:3,vars:3,consts:[["class","container-fluid px-1 mail-content",4,"ngIf"],["class","container-fluid px-1 my-2 mail-content",3,"formGroup",4,"ngIf"],[1,"container-fluid","px-1","mail-content"],[1,"row","mt-2",3,"ngClass"],[1,"col-lg-5","col-sm-12","my-auto","ml-auto","px-0"],["name","fontStyle","aria-label","Font Style"],["value","reply",3,"change"],[1,"mr-2","action-btn-icon"],[1,"mr-2","action-btn-txt"],["value","replyAll",3,"change"],["value","forward",3,"change"],[1,"row"],[1,"col-lg-7","col-sm-12","extra-dark-txt","my-2",2,"font-size","16px"],[1,"row","mt-2","px-0",2,"background-color","#F6F6F6",3,"ngClass"],[1,"col-1","py-3"],[3,"src"],[1,"col-11","my-auto","pl-0","pr-1","py-2"],[1,"row","my-1","extra-dark-txt",2,"font-size","14px"],[1,"pr-2"],[1,"row","my-1"],[1,"col-4","px-0","light-txt"],["class","row light-txt my-1",4,"ngIf"],[1,"row","my-3","px-3",3,"ngClass"],[1,"body-text-area",3,"innerHTML"],[1,"row","light-txt","my-1"],[4,"ngFor","ngForOf"],[1,"pr-1",3,"matTooltip"],[4,"ngIf"],[1,"container-fluid","px-1","my-2","mail-content",3,"formGroup"],[1,"row","d-flex","d-flex","justify-content-between","slide-in-right"],[1,"col-2","my-auto","px-0"],["mat-icon-button","",1,"view-button-inactive","mx-2",3,"matTooltip","click"],[1,"iconButton"],["mat-icon-button","",1,"view-button-inactive","mx-1",3,"matTooltip","ngClass","click"],["class","my-auto px-0","style","max-width: 300px; text-overflow: ellipsis; overflow: hidden;",3,"matTooltip",4,"ngIf"],[1,"col-sm-12","my-auto","px-0",3,"ngClass"],["value","send",3,"change"],["value","discard",3,"change"],["value","discard",3,"change",4,"ngIf"],[1,"my-2"],[1,"row","slide-in-right"],[1,"col-2","pr-0","my-auto","light-txt"],[1,"col-10","px-0"],["formControlName","fromMailIds",3,"separatorKeyCodes","errorMessages","validators","editable","placeholder","secondaryPlaceholder","clearOnBlur","addOnPaste","addOnBlur","pasteSplitPattern","identifyBy","displayBy","disable"],[1,"row","slide-in-right","mail-field"],[1,"col-8","px-0"],["formControlName","toRecipients",3,"separatorKeyCodes","errorMessages","validators","editable","placeholder","secondaryPlaceholder","clearOnBlur","addOnPaste","addOnBlur","pasteSplitPattern","identifyBy","displayBy"],["class","col-1 px-0","style","max-width: 4vw;",4,"ngIf"],[1,"col-1","my-auto","px-0"],["mat-button","",3,"ngStyle","click"],["class","row slide-in-right mail-field",4,"ngIf"],["placeholder","","formControlName","subject",3,"separatorKeyCodes","editable","secondaryPlaceholder","clearOnBlur","addOnPaste","addOnBlur","pasteSplitPattern"],["class","row slide-in-right",4,"ngIf"],[1,"row","mt-2","slide-from-down"],["valueType","html","formControlName","body",3,"ngClass"],[3,"multiline"],["name","bold"],["name","italic"],["name","strike"],["name","underline"],["name","separator"],["name","alignLeft"],["name","alignCenter"],["name","alignRight"],["name","alignJustify"],["name","orderedList"],["name","bulletList"],["name","color"],["name","background"],["name","blockquote"],["name","image"],[3,"enabled"],[1,"my-auto","px-0",2,"max-width","300px","text-overflow","ellipsis","overflow","hidden",3,"matTooltip"],[1,"col-1","px-0",2,"max-width","4vw"],["mat-icon-button","","matTooltip","Save recipients",1,"save-btn",3,"click"],[2,"color","#66615b !important","font-size","21px"],[1,"col-9","px-0"],["placeholder","","formControlName","ccRecipients",3,"separatorKeyCodes","errorMessages","validators","editable","placeholder","secondaryPlaceholder","clearOnBlur","addOnPaste","addOnBlur","pasteSplitPattern","identifyBy","displayBy"],["class","col-1 px-0",4,"ngIf"],[1,"col-1","px-0"],["placeholder","","formControlName","bccRecipients",3,"separatorKeyCodes","errorMessages","validators","editable","placeholder","secondaryPlaceholder","clearOnBlur","addOnPaste","addOnBlur","pasteSplitPattern","identifyBy","displayBy"],[1,"col-8","px-0","mt-2"],[2,"display","flex","align-items","center"],["type","text","placeholder","Enter spoc name","formControlName","spocName",1,"mt-1","pl-2",2,"display","block","align-items","center","justify-content","center","border","none","outline","none","width","900px"],[2,"border-top","1px solid #ccc"],["mat-icon-button","","matTooltip","Save Spoc Name",1,"save-btn",3,"click"],[1,"col-10","px-0",2,"display","flex","flex-wrap","wrap"],["class","file-item p-1 mb-1 mt-1","style","display: flex !important; border: 1px solid lightgray;",4,"ngFor","ngForOf"],[1,"file-item","p-1","mb-1","mt-1",2,"display","flex !important","border","1px solid lightgray"],["alt","",1,"pr-1",2,"height","20px","width","20px",3,"src"],[1,"filename",3,"matTooltip","click"],[1,"mt-2",3,"checked","change"],["style","border: none; background: none; color: blue; cursor: pointer;",3,"click",4,"ngIf"],[2,"border","none","background","none","color","blue","cursor","pointer",3,"click"],["type","file","multiple","","ng2FileSelect","",2,"display","none",3,"uploader","change"],["fileInput",""],["mat-button","",3,"ngStyle","click","mouseenter","mouseleave"],["type","file","multiple","",2,"display","none",3,"change"],["mat-button","",3,"ngStyle","matTooltip","click","mouseenter","mouseleave"],[1,"mt-5"],[1,"d-flex","justify-content-center","extra-dark-txt","slide-in-top"],[1,"d-flex","justify-content-center","light-txt","slide-in-top"],[1,"d-flex","justify-content-center","slide-from-down"],["_ngcontent-mla-c256","","src","https://assets.kebs.app/images/nomail.png","height","350","width","350",1,"mt-4"]],template:function(e,t){1&e&&(k["\u0275\u0275template"](0,he,43,25,"div",0),k["\u0275\u0275template"](1,je,75,62,"form",1),k["\u0275\u0275template"](2,Ve,8,0,"div",0)),2&e&&(k["\u0275\u0275property"]("ngIf","read"==t.mailUtilityService.mUtilityData.currentMailMode.mode),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf","create"==t.mailUtilityService.mUtilityData.currentMailMode.mode||"action"==t.mailUtilityService.mUtilityData.currentMailMode.mode),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf","not selected"==t.mailUtilityService.mUtilityData.currentMailMode.mode))},directives:[a.NgIf,a.NgClass,p.b,p.a,r.a,a.NgForOf,o.a,P.J,P.w,P.n,l.a,m.a,E.a,P.v,P.l,a.NgStyle,u.a,te.Ge,te.o,te.cd,P.e,_.a,F.b],pipes:[ne,le,re],styles:[".mail-content[_ngcontent-%COMP%]{overflow-y:scroll;height:100vh}.mail-content[_ngcontent-%COMP%]   .light-txt[_ngcontent-%COMP%]{color:#727170;font-size:13px}.mail-content[_ngcontent-%COMP%]   .dark-txt[_ngcontent-%COMP%]{color:#000}.mail-content[_ngcontent-%COMP%]   .extra-dark-txt[_ngcontent-%COMP%]{font-weight:500;color:#000}.mail-content[_ngcontent-%COMP%]   .fs-16[_ngcontent-%COMP%]{font-size:16px}.mail-content[_ngcontent-%COMP%]   .fs-18[_ngcontent-%COMP%]{font-size:18px}.mail-content[_ngcontent-%COMP%]   .account-circle[_ngcontent-%COMP%]{border-radius:50%;background-color:#a4d4ff;color:#000;font-weight:500;width:60px;height:60px;line-height:60px;text-align:center;font-size:18px}.mail-content[_ngcontent-%COMP%]   .action-btn-txt[_ngcontent-%COMP%]{vertical-align:text-top;color:#000}.mail-content[_ngcontent-%COMP%]   .action-btn-icon[_ngcontent-%COMP%]{vertical-align:-webkit-baseline-middle;font-size:22px;color:#727170}.mail-content[_ngcontent-%COMP%]   mat-button-toggle[_ngcontent-%COMP%]{width:8vw!important}.mail-content[_ngcontent-%COMP%]     .dx-toolbar-items-container{height:40px!important}.mail-content[_ngcontent-%COMP%]     .dx-htmleditor-toolbar-wrapper{border-bottom:1px solid rgba(114,113,112,.****************)!important}.mail-content[_ngcontent-%COMP%]     .dx-htmleditor.dx-htmleditor-outlined{box-shadow:inset 0 0 0 0 rgba(0,0,0,.42)!important;border-color:transparent!important;border-radius:4px!important}.mail-content[_ngcontent-%COMP%]   .auto-layout[_ngcontent-%COMP%]     .dx-htmleditor-content table{table-layout:auto!important}.mail-content[_ngcontent-%COMP%]   .fixed-layout[_ngcontent-%COMP%]     .dx-htmleditor-content table{table-layout:fixed!important}.mail-content[_ngcontent-%COMP%]   .dx-html-container[_ngcontent-%COMP%]{height:60vh!important;width:80vw!important}.mail-content[_ngcontent-%COMP%]   .dx-html-container-expanded[_ngcontent-%COMP%]{height:60vh!important;width:100vw!important}.mail-content[_ngcontent-%COMP%]   .slide-in-right[_ngcontent-%COMP%]{animation:slide-in-right .3s cubic-bezier(.25,.46,.45,.94) both}.mail-content[_ngcontent-%COMP%]   .mail-field[_ngcontent-%COMP%]:hover   .save-btn[_ngcontent-%COMP%]{visibility:visible!important;animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}.mail-content[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%]{visibility:hidden}.mail-content[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]{margin-top:5px!important;line-height:8px;width:26px;height:26px;margin-right:10px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12);animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}.mail-content[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.mail-content[_ngcontent-%COMP%]   .view-button-active[_ngcontent-%COMP%]{margin-top:5px!important;line-height:8px;width:26px;height:26px;margin-right:10px!important;background-color:#cf0001;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12);animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}.mail-content[_ngcontent-%COMP%]   .view-button-active[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#fff;font-size:18px}@keyframes slide-top{0%{transform:translateY(10px)}to{transform:translateY(0)}}@keyframes slide-in-right{0%{transform:translateX(30px);opacity:0}to{transform:translateX(0);opacity:1}}.mail-content[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}.mail-content[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}.mail-content[_ngcontent-%COMP%]   .body-text-area[_ngcontent-%COMP%]{word-wrap:break-word;width:100%}.mail-content[_ngcontent-%COMP%]   .mat-tooltip[_ngcontent-%COMP%]{white-space:pre-line!important}.mail-content[_ngcontent-%COMP%]     .dx-htmleditor-content a{color:inherit!important;text-decoration:none!important}.mail-content[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]{height:28px}.mail-content[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%], .mail-content[_ngcontent-%COMP%]   .filename[_ngcontent-%COMP%]{margin-right:10px;white-space:nowrap;align-items:center}.mail-content[_ngcontent-%COMP%]   .filename[_ngcontent-%COMP%]{max-width:188px;display:block;text-overflow:ellipsis;overflow:hidden}.mail-content[_ngcontent-%COMP%]     .mat-checkbox-checked.mat-accent .mat-checkbox-background, .mail-content[_ngcontent-%COMP%]     .mat-checkbox-indeterminate.mat-accent .mat-checkbox-background{background-color:#52c41a!important}"],data:{animation:[Object(Q.o)("slideInOut",[Object(Q.l)("in",Object(Q.m)({height:"*",overflow:"hidden"})),Object(Q.l)("out",Object(Q.m)({height:0,overflow:"hidden"})),Object(Q.n)("* => in",[Object(Q.m)({height:0}),Object(Q.e)(120,Object(Q.m)({height:"*"}))]),Object(Q.n)("in=> *",[Object(Q.m)({height:"*"}),Object(Q.e)(120,Object(Q.m)({height:0}))])])]}}),e})();function Ae(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"div",15),k["\u0275\u0275elementStart"](1,"button",16),k["\u0275\u0275listener"]("click",(function(){k["\u0275\u0275restoreView"](e);const t=k["\u0275\u0275nextContext"](2);return t._mailUtilityService.saveMailItemInKebs(t.mailData)})),k["\u0275\u0275elementStart"](2,"mat-icon",17),k["\u0275\u0275text"](3,"sync"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}}const Ne=function(e){return{"background-color":e}};function Re(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"div",2),k["\u0275\u0275elementStart"](1,"div",3),k["\u0275\u0275elementStart"](2,"div",4),k["\u0275\u0275listener"]("click",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"]().selectMailItem()})),k["\u0275\u0275element"](3,"img",5),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](4,"div",6),k["\u0275\u0275elementStart"](5,"div",7),k["\u0275\u0275elementStart"](6,"div",8),k["\u0275\u0275listener"]("click",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"]().selectMailItem()})),k["\u0275\u0275text"](7),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](8,Ae,4,0,"div",9),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](9,"div",10),k["\u0275\u0275listener"]("click",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"]().selectMailItem()})),k["\u0275\u0275elementStart"](10,"div",11),k["\u0275\u0275text"](11),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](12,"div",12),k["\u0275\u0275text"](13),k["\u0275\u0275pipe"](14,"dateFormat"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](15,"div",13),k["\u0275\u0275listener"]("click",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"]().selectMailItem()})),k["\u0275\u0275elementStart"](16,"div",14),k["\u0275\u0275text"](17),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}if(2&e){const e=k["\u0275\u0275nextContext"]();k["\u0275\u0275property"]("ngStyle",k["\u0275\u0275pureFunction1"](20,Ne,e.mailData[0].isSelected?"#FFF2F2":"")),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngClass",e.mailData[0].isFromKebs?"kebs-border":"ms-border"),k["\u0275\u0275advance"](2),k["\u0275\u0275styleProp"]("height",e.imgHeight)("width",e.imgWidth)("border-width",e.borderWidth)("border-style",e.borderStyle)("border-color",e.borderColor),k["\u0275\u0275property"]("src",e.imgSrc,k["\u0275\u0275sanitizeUrl"]),k["\u0275\u0275advance"](4),k["\u0275\u0275textInterpolate1"](" ",null!=e.mailData[0]&&e.mailData[0].from.emailAddress.name?null==e.mailData[0]?null:e.mailData[0].from.emailAddress.name:""," "),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",!e.mailData[0].isFromKebs&&e._mailUtilityService.mailUiData.isSyncWithKebsBtn),k["\u0275\u0275advance"](3),k["\u0275\u0275textInterpolate1"](" ",null==e.mailData[0]?null:e.mailData[0].subject," "),k["\u0275\u0275advance"](2),k["\u0275\u0275textInterpolate1"](" ",k["\u0275\u0275pipeBind1"](14,18,null==e.mailData[0]?null:e.mailData[0].receivedDateTime)," "),k["\u0275\u0275advance"](4),k["\u0275\u0275textInterpolate1"](" ",null==e.mailData[0]?null:e.mailData[0].bodyPreview," ")}}function ze(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"div",15),k["\u0275\u0275elementStart"](1,"button",16),k["\u0275\u0275listener"]("click",(function(){k["\u0275\u0275restoreView"](e);const t=k["\u0275\u0275nextContext"](2);return t._mailUtilityService.saveMailItemInKebs(t.mailData)})),k["\u0275\u0275elementStart"](2,"mat-icon",17),k["\u0275\u0275text"](3,"sync"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}}function Ke(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"div",28),k["\u0275\u0275listener"]("click",(function(){k["\u0275\u0275restoreView"](e);const i=t.$implicit;return k["\u0275\u0275nextContext"](3).threadItemClicked(i)})),k["\u0275\u0275element"](1,"div",29),k["\u0275\u0275elementStart"](2,"div",6),k["\u0275\u0275elementStart"](3,"div",7),k["\u0275\u0275elementStart"](4,"div",30),k["\u0275\u0275text"](5),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](6,"div",31),k["\u0275\u0275elementStart"](7,"div",32),k["\u0275\u0275text"](8),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](9,"div",12),k["\u0275\u0275text"](10),k["\u0275\u0275pipe"](11,"dateFormat"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,i=k["\u0275\u0275nextContext"](3);k["\u0275\u0275property"]("ngClass",e.isFromKebs?"kebs-border":"ms-border")("ngStyle",k["\u0275\u0275pureFunction1"](8,Ne,e.isSelected?"#FFF2F2":""))("@slideInOut",i.mailData[0].isExpanded?"in":"out"),k["\u0275\u0275advance"](5),k["\u0275\u0275textInterpolate1"](" ",e.from.emailAddress.name," "),k["\u0275\u0275advance"](3),k["\u0275\u0275textInterpolate1"](" ",null==e?null:e.bodyPreview," "),k["\u0275\u0275advance"](2),k["\u0275\u0275textInterpolate1"](" ",k["\u0275\u0275pipeBind1"](11,6,null==e?null:e.receivedDateTime)," ")}}function He(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"div"),k["\u0275\u0275template"](1,Ke,12,10,"div",27),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"](2);k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngForOf",e.mailData)}}const Le=function(e,t,i){return{"activate-card":e,"kebs-border":t,"ms-border":i}};function We(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"div",18),k["\u0275\u0275elementStart"](1,"div",19),k["\u0275\u0275listener"]("click",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"]().expandThread()})),k["\u0275\u0275elementStart"](2,"div",4),k["\u0275\u0275listener"]("click",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"]().selectMailItem()})),k["\u0275\u0275element"](3,"img",5),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](4,"div",6),k["\u0275\u0275elementStart"](5,"div",7),k["\u0275\u0275elementStart"](6,"div",8),k["\u0275\u0275listener"]("click",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"]().selectMailItem()})),k["\u0275\u0275text"](7),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](8,ze,4,0,"div",9),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](9,"div",20),k["\u0275\u0275listener"]("click",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"]().selectMailItem()})),k["\u0275\u0275elementStart"](10,"div"),k["\u0275\u0275elementStart"](11,"button",21),k["\u0275\u0275elementStart"](12,"mat-icon",22),k["\u0275\u0275text"](13),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](14,"div",23),k["\u0275\u0275text"](15),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](16,"div",24),k["\u0275\u0275text"](17),k["\u0275\u0275pipe"](18,"dateFormat"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](19,"div",13),k["\u0275\u0275listener"]("click",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"]().selectMailItem()})),k["\u0275\u0275elementStart"](20,"div",25),k["\u0275\u0275text"](21),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](22,He,2,1,"div",26),k["\u0275\u0275elementEnd"]()}if(2&e){const e=k["\u0275\u0275nextContext"]();k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngClass",k["\u0275\u0275pureFunction3"](23,Le,e.mailData[0].isExpanded,e.mailData[0].isFromKebs,!e.mailData[0].isFromKebs))("ngStyle",k["\u0275\u0275pureFunction1"](27,Ne,e.mailData[0].isExpanded?"#FFF2F2":"")),k["\u0275\u0275advance"](2),k["\u0275\u0275styleProp"]("height",e.imgHeight)("width",e.imgWidth)("border-width",e.borderWidth)("border-style",e.borderStyle)("border-color",e.borderColor),k["\u0275\u0275property"]("src",e.imgSrc,k["\u0275\u0275sanitizeUrl"]),k["\u0275\u0275advance"](4),k["\u0275\u0275textInterpolate1"](" ",null!=e.mailData[0]&&e.mailData[0].from.emailAddress.name?null==e.mailData[0]?null:e.mailData[0].from.emailAddress.name:""," "),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",!e.mailData[0].isFromKebs&&e._mailUtilityService.mailUiData.isSyncWithKebsBtn),k["\u0275\u0275advance"](3),k["\u0275\u0275property"]("matTooltip",e.mailData[0].isExpanded?"Hide":"show"),k["\u0275\u0275advance"](2),k["\u0275\u0275textInterpolate1"]("",null!=e.mailData[0]&&e.mailData[0].isExpanded?"keyboard_arrow_down":"keyboard_arrow_right"," "),k["\u0275\u0275advance"](2),k["\u0275\u0275textInterpolate1"](" ",null==e.mailData[0]?null:e.mailData[0].subject," "),k["\u0275\u0275advance"](2),k["\u0275\u0275textInterpolate1"](" ",k["\u0275\u0275pipeBind1"](18,21,null==e.mailData[0]?null:e.mailData[0].receivedDateTime)," "),k["\u0275\u0275advance"](4),k["\u0275\u0275textInterpolate1"](" ",null==e.mailData[0]?null:e.mailData[0].bodyPreview," "),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",1==e.mailData[0].isExpanded)}}let Ye=(()=>{class e{constructor(e){this._mailUtilityService=e,this.handleCardSelection=new k.EventEmitter,this.showThreadItems=!1,this.imgWidth="30px",this.imgHeight="30px",this.borderWidth="0px",this.borderStyle="solid",this.borderColor="#000000",this.imgSrc="https://assets.kebs.app/images/User.png"}ngOnInit(){}selectMailItem(){this.mailData[0].isExpanded=!this.mailData[0].isExpanded,this._mailUtilityService.mUtilityData.selectedMailData=this.mailData[0],this.handleCardSelection.emit(this.mailData[0]),this._mailUtilityService.mUtilityData.currentMailMode={mode:"read"},this._mailUtilityService.mailFunctions.switchViewMode()}expandThread(){this._mailUtilityService.mUtilityData.selectedMailData=this.mailData[0],this.handleCardSelection.emit(this.mailData[0]),this._mailUtilityService.mUtilityData.currentMailMode={mode:"read"},this._mailUtilityService.mailFunctions.switchViewMode()}threadItemClicked(e){this._mailUtilityService.mUtilityData.selectedMailData=e,this.handleCardSelection.emit(e),this._mailUtilityService.mUtilityData.currentMailMode={mode:"read"},this._mailUtilityService.mailFunctions.switchViewMode()}}return e.\u0275fac=function(t){return new(t||e)(k["\u0275\u0275directiveInject"](z.a))},e.\u0275cmp=k["\u0275\u0275defineComponent"]({type:e,selectors:[["app-mail-card-mini"]],inputs:{mailData:"mailData"},outputs:{handleCardSelection:"handleCardSelection"},decls:2,vars:2,consts:[["class","container-fluid px-0 mail-card-mini slide-from-down","matRipple","",3,"ngStyle",4,"ngIf"],["class","container-fluid px-0 mail-card-mini slide-from-down",4,"ngIf"],["matRipple","",1,"container-fluid","px-0","mail-card-mini","slide-from-down",3,"ngStyle"],[1,"row","mini-card","px-2",3,"ngClass"],[1,"col-lg-2","col-sm-12","pr-0","pl-2","py-3",3,"click"],[3,"src"],[1,"col-10","py-2","px-0"],[1,"row","d-flex","justify-content-between","mx-2"],[1,"extra-dark-txt",3,"click"],["class","floating-icons",4,"ngIf"],[1,"row","my-1","d-flex","justify-content-between",3,"click"],[1,"extra-dark-txt","body-preview","px-0",2,"max-width","60%"],[1,"light-txt","px-0"],[1,"row","my-1",3,"click"],[1,"body-preview","light-txt","px-0"],[1,"floating-icons"],["matTooltip","Sync with KEBS","mat-icon-button","",1,"my-auto","utility-btn",3,"click"],[1,"icon-btn"],[1,"container-fluid","px-0","mail-card-mini","slide-from-down"],[1,"row","mini-card","px-2",3,"ngClass","ngStyle","click"],[1,"row","my-1","d-flex",3,"click"],["mat-icon-button","",2,"height","25px !important","line-height","25px !important","width","25px !important",3,"matTooltip"],[2,"color","#66615b !important","font-size","21px !important"],[1,"extra-dark-txt","body-preview","px-0",2,"max-width","40%"],[1,"light-txt","ml-auto","px-0"],[1,"body-preview","light-txt"],[4,"ngIf"],["class","row px-2",3,"ngClass","ngStyle","click",4,"ngFor","ngForOf"],[1,"row","px-2",3,"ngClass","ngStyle","click"],[1,"col-lg-2","col-sm-12","pl-4","py-3"],[1,"extra-dark-txt"],[1,"row","my-1","d-flex","justify-content-between"],[1,"col-12","light-txt","body-preview","px-0",2,"max-width","60%"]],template:function(e,t){1&e&&(k["\u0275\u0275template"](0,Re,18,22,"div",0),k["\u0275\u0275template"](1,We,23,29,"div",1)),2&e&&(k["\u0275\u0275property"]("ngIf",1==(null==t.mailData?null:t.mailData.length)),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",(null==t.mailData?null:t.mailData.length)>1))},directives:[a.NgIf,a.NgStyle,a.NgClass,l.a,o.a,r.a,a.NgForOf],pipes:[ne],styles:[".mail-card-mini[_ngcontent-%COMP%]   .activate-card[_ngcontent-%COMP%], .mail-card-mini[_ngcontent-%COMP%]   .mini-card[_ngcontent-%COMP%]:hover{box-shadow:0 4px 8px 0 rgba(0,0,0,.2),0 6px 20px 0 rgba(0,0,0,.19)}.mail-card-mini[_ngcontent-%COMP%]   .account-circle[_ngcontent-%COMP%]{border-radius:50%;background-color:#a4d4ff;color:#000;font-weight:500;width:40px;height:40px;line-height:40px;text-align:center}.mail-card-mini[_ngcontent-%COMP%]   .light-txt[_ngcontent-%COMP%]{color:#727170;font-size:13px}.mail-card-mini[_ngcontent-%COMP%]   .ms-border[_ngcontent-%COMP%]{border-left:8px solid #2196f0;border-radius:5px}.mail-card-mini[_ngcontent-%COMP%]   .kebs-border[_ngcontent-%COMP%]{border-left:8px solid #cf0001;border-radius:5px}.mail-card-mini[_ngcontent-%COMP%]   .dark-txt[_ngcontent-%COMP%]{color:#000}.mail-card-mini[_ngcontent-%COMP%]   .extra-dark-txt[_ngcontent-%COMP%]{font-weight:500;color:#000;font-size:13px}.mail-card-mini[_ngcontent-%COMP%]   .icon-btn[_ngcontent-%COMP%]{color:#545352!important;font-size:18px}.mail-card-mini[_ngcontent-%COMP%]   .floating-icons[_ngcontent-%COMP%]     .mat-icon-button{padding:0;min-width:0;width:20px!important;height:20px!important;flex-shrink:0;line-height:20px!important;border-radius:50%}.mail-card-mini[_ngcontent-%COMP%]   .body-preview[_ngcontent-%COMP%]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:1;-webkit-box-orient:vertical;font-size:13px}.mail-card-mini[_ngcontent-%COMP%]   .utility-btn[_ngcontent-%COMP%]{visibility:hidden}.mail-card-mini[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}.mail-card-mini[_ngcontent-%COMP%]   [_ngcontent-%COMP%]:hover{background-color:#fff2f2;transition:.3s;cursor:pointer}.mail-card-mini[_ngcontent-%COMP%]   [_ngcontent-%COMP%]:hover   .utility-btn[_ngcontent-%COMP%]{visibility:visible!important;animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-top{0%{transform:translateY(10px)}to{transform:translateY(0)}}"],data:{animation:[Object(Q.o)("slideInOut",[Object(Q.l)("in",Object(Q.m)({height:"*",overflow:"hidden"})),Object(Q.l)("out",Object(Q.m)({height:0,overflow:"hidden"})),Object(Q.n)("* => in",[Object(Q.m)({height:0}),Object(Q.e)(120,Object(Q.m)({height:"*"}))]),Object(Q.n)("in=> *",[Object(Q.m)({height:"*"}),Object(Q.e)(120,Object(Q.m)({height:0}))])])]}}),e})();function Xe(e,t){1&e&&k["\u0275\u0275element"](0,"mat-progress-bar",4)}function Ge(e,t){1&e&&k["\u0275\u0275element"](0,"mat-divider",19),2&e&&k["\u0275\u0275property"]("inset",!0)}function $e(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"div"),k["\u0275\u0275elementStart"](1,"app-mail-card-mini",17),k["\u0275\u0275listener"]("handleCardSelection",(function(t){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"](3).handleSelectedMiniCard(t)})),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](2,Ge,1,1,"mat-divider",18),k["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,i=t.last,n=k["\u0275\u0275nextContext"](3);k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("mailData",n._mailUtilityService.mailUiData.miniCardInputData[e]),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",!i)}}function Je(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"div",20),k["\u0275\u0275elementStart"](1,"div",21),k["\u0275\u0275element"](2,"mat-spinner",22),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]())}function Qe(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"div",10),k["\u0275\u0275elementStart"](1,"div",11),k["\u0275\u0275text"](2),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](3,"div",12),k["\u0275\u0275element"](4,"mat-divider",13),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](5,"div",12),k["\u0275\u0275elementStart"](6,"div",14),k["\u0275\u0275listener"]("scrolled",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"](2)._mailUtilityService.mailListScrolledDown()})),k["\u0275\u0275template"](7,$e,3,2,"div",15),k["\u0275\u0275template"](8,Je,3,0,"div",16),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}if(2&e){const e=k["\u0275\u0275nextContext"](2);k["\u0275\u0275advance"](2),k["\u0275\u0275textInterpolate1"](" ",e._mailUtilityService.mailUiData.selectedMailFolder.label," "),k["\u0275\u0275advance"](4),k["\u0275\u0275property"]("infiniteScrollDistance",1)("infiniteScrollThrottle",500)("alwaysCallback",!0)("scrollWindow",!1),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngForOf",e._mailUtilityService.mailUiData.conversationIdKeys),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",e._mailUtilityService.mailUiData.isMailItemDataLoading)}}function Ze(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"div",23),k["\u0275\u0275element"](1,"mat-divider",24),k["\u0275\u0275elementEnd"]()),2&e&&(k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("vertical",!0))}function qe(e,t){if(1&e&&(k["\u0275\u0275elementStart"](0,"div",5),k["\u0275\u0275elementStart"](1,"div",6),k["\u0275\u0275template"](2,Qe,9,7,"div",7),k["\u0275\u0275template"](3,Ze,2,1,"div",8),k["\u0275\u0275elementStart"](4,"div",9),k["\u0275\u0275element"](5,"app-mail-content-review"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()),2&e){const e=k["\u0275\u0275nextContext"]();k["\u0275\u0275advance"](2),k["\u0275\u0275property"]("ngIf",!e._mailUtilityService.mailUiData.isExpandBody),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",!e._mailUtilityService.mailUiData.isExpandBody),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngClass",e._mailUtilityService.mailUiData.isExpandBody?"col-12 px-0":"col-9 px-0")}}function et(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"div",25),k["\u0275\u0275elementStart"](1,"div",26),k["\u0275\u0275elementStart"](2,"div",27),k["\u0275\u0275text"](3," Fetching mails, please wait... "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]())}let tt=(()=>{class e{constructor(e){this._mailUtilityService=e,this.isLoading=!0}ngOnInit(){this._mailUtilityService.mUtilityData.reloadBodyComponent=this.reload.bind(this),this._mailUtilityService.mailFunctions.getMailDataFromResources()}reload(){this._mailUtilityService.mailUiData.miniCardInputData={},this._mailUtilityService.mailFunctions.getMailDataFromResources()}handleSelectedMiniCard(e){for(let t of this._mailUtilityService.mailUiData.conversationIdKeys){this._mailUtilityService.mailUiData.miniCardInputData[t].length>1&&(this._mailUtilityService.mailUiData.miniCardInputData[t][0].conversationId==e.conversationId||(this._mailUtilityService.mailUiData.miniCardInputData[t][0].isExpanded=!1));for(let i of this._mailUtilityService.mailUiData.miniCardInputData[t])i.isSelected=i.id==e.id}}}return e.\u0275fac=function(t){return new(t||e)(k["\u0275\u0275directiveInject"](z.a))},e.\u0275cmp=k["\u0275\u0275defineComponent"]({type:e,selectors:[["app-mail-body"]],decls:4,vars:3,consts:[[1,"container-fluid","px-0"],["mode","indeterminate",4,"ngIf"],["class","container-fluid pl-0 pr-2 mail-body",4,"ngIf"],["class","container-fluid mail-box-body mt-5",4,"ngIf"],["mode","indeterminate"],[1,"container-fluid","pl-0","pr-2","mail-body"],[1,"row",2,"height","100vh"],["class","col-3 px-0",4,"ngIf"],["class","col-1 px-0","style","max-width: 0%;",4,"ngIf"],[3,"ngClass"],[1,"col-3","px-0"],[1,"row","my-3","pl-4","folder-name"],[1,"row"],[2,"width","100%"],["infinite-scroll","",1,"scroll-view",3,"infiniteScrollDistance","infiniteScrollThrottle","alwaysCallback","scrollWindow","scrolled"],[4,"ngFor","ngForOf"],["class","row container d-flex pt-1 mt-2  flex-column item-loading",4,"ngIf"],[3,"mailData","handleCardSelection"],["class","mx-0",3,"inset",4,"ngIf"],[1,"mx-0",3,"inset"],[1,"row","container","d-flex","pt-1","mt-2","flex-column","item-loading"],[1,"row","justify-content-center"],["diameter","25","matTooltip","Loading ..."],[1,"col-1","px-0",2,"max-width","0%"],[2,"height","100vh",3,"vertical"],[1,"container-fluid","mail-box-body","mt-5"],[1,"d-flex","justify-content-center","align-items-center"],[1,"txt-light",2,"margin-top","200px"]],template:function(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"div",0),k["\u0275\u0275template"](1,Xe,1,0,"mat-progress-bar",1),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](2,qe,6,3,"div",2),k["\u0275\u0275template"](3,et,4,0,"div",3)),2&e&&(k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",t._mailUtilityService.mUtilityData.isLoading),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",!t._mailUtilityService.mUtilityData.isLoading),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("ngIf",t._mailUtilityService.mUtilityData.isLoading&&0==(null==t._mailUtilityService.mailUiData.conversationIdKeys?null:t._mailUtilityService.mailUiData.conversationIdKeys.length)))},directives:[a.NgIf,s.a,a.NgClass,Te,m.a,D.a,a.NgForOf,Ye,c.c,o.a],styles:[".mail-body[_ngcontent-%COMP%]   .folder-name[_ngcontent-%COMP%]{font-weight:500;font-size:19px;color:#000}.mail-body[_ngcontent-%COMP%]   .txt-light[_ngcontent-%COMP%]{font-size:16px;color:#727170}.mail-body[_ngcontent-%COMP%]   .example-viewport[_ngcontent-%COMP%]{height:100vh}.mail-body[_ngcontent-%COMP%]   .scroll-view[_ngcontent-%COMP%]{overflow-y:scroll;height:75vh}"]}),e})();function it(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"div",15),k["\u0275\u0275listener"]("click",(function(){k["\u0275\u0275restoreView"](e);const i=t.index;return k["\u0275\u0275nextContext"]().onMenuClick(i)})),k["\u0275\u0275elementStart"](1,"mat-list-item"),k["\u0275\u0275element"](2,"div",16),k["\u0275\u0275elementStart"](3,"div",17),k["\u0275\u0275elementStart"](4,"mat-icon",18),k["\u0275\u0275text"](5),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](6,"p",19),k["\u0275\u0275text"](7),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;k["\u0275\u0275property"]("ngClass",e.isEnabled?"menu-enable":"menu-disable"),k["\u0275\u0275advance"](5),k["\u0275\u0275textInterpolate"](e.icon),k["\u0275\u0275advance"](2),k["\u0275\u0275textInterpolate"](e.label)}}function nt(e,t){if(1&e){const e=k["\u0275\u0275getCurrentView"]();k["\u0275\u0275elementStart"](0,"div",20),k["\u0275\u0275elementStart"](1,"button",21),k["\u0275\u0275listener"]("click",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"]().prevClicked()})),k["\u0275\u0275elementStart"](2,"mat-icon",13),k["\u0275\u0275text"](3,"navigate_before"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](4,"div",22),k["\u0275\u0275text"](5),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](6,"button",21),k["\u0275\u0275listener"]("click",(function(){return k["\u0275\u0275restoreView"](e),k["\u0275\u0275nextContext"]().nextClicked()})),k["\u0275\u0275elementStart"](7,"mat-icon",13),k["\u0275\u0275text"](8,"navigate_next"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()}if(2&e){const e=k["\u0275\u0275nextContext"]();k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("disabled",1==e.currentPage),k["\u0275\u0275advance"](4),k["\u0275\u0275textInterpolate2"](" Review - ",e.currentPage," of ",e.totalPage," "),k["\u0275\u0275advance"](1),k["\u0275\u0275property"]("disabled",e.currentPage==e.totalPage)}}let at=(()=>{class e{constructor(e,t){this.dialogRef=e,this.mailUtilityService=t,this.currentPage=1,this.reviewPageKeys=[]}ngOnInit(){return Object(n.c)(this,void 0,void 0,(function*(){this.handleBasedOnApplication(),this.mailUtilityService.mailUiData.selectedMailFolder=this.mailUtilityService.mailFolders[0],this.mailUtilityService.mUtilityData.reloadBodyComponent(),this.mailUtilityService.mailFunctions.initMiniCardSelection()}))}onMenuClick(e){this.mailUtilityService.mailFolders.forEach((t,i)=>{e==i?(t.isEnabled=!0,this.mailUtilityService.mailUiData.selectedMailFolder=t):t.isEnabled=!1}),this.mailUtilityService.cancelApiCall(),this.mailUtilityService.mUtilityData.reloadBodyComponent()}prevClicked(){this.currentPage-=1,this.mailUtilityService.mUtilityData.selectedDunningMailData=this.mailUtilityService.mUtilityData.reviewPagination[this.reviewPageKeys[this.currentPage-1]],this.mailUtilityService.mUtilityData.selectedNewMailTemplateData=this.mailUtilityService.mUtilityData.reviewPagination[this.reviewPageKeys[this.currentPage-1]],this.mailUtilityService.mUtilityData.recipientMailIdArr=this.mailUtilityService.mUtilityData.reviewPagination[this.reviewPageKeys[this.currentPage-1]].toMailIds,this.mailUtilityService.mailFunctions.initMiniCardSelection(),this.mailUtilityService.mUtilityData.reloadBodyComponent(),this.mailUtilityService.mUtilityData.currentMailMode={mode:"not selected"},this.mailUtilityService.mailFunctions.switchViewMode()}nextClicked(){this.currentPage+=1,this.mailUtilityService.mUtilityData.selectedDunningMailData=this.mailUtilityService.mUtilityData.reviewPagination[this.reviewPageKeys[this.currentPage-1]],this.mailUtilityService.mUtilityData.selectedNewMailTemplateData=this.mailUtilityService.mUtilityData.reviewPagination[this.reviewPageKeys[this.currentPage-1]],this.mailUtilityService.mUtilityData.recipientMailIdArr=this.mailUtilityService.mUtilityData.reviewPagination[this.reviewPageKeys[this.currentPage-1]].toMailIds,this.mailUtilityService.mailFunctions.initMiniCardSelection(),this.mailUtilityService.mUtilityData.reloadBodyComponent(),this.mailUtilityService.mUtilityData.currentMailMode={mode:"not selected"},this.mailUtilityService.mailFunctions.switchViewMode()}handleBasedOnApplication(){this.mailUtilityService.mUtilityData.hasInitiateNewMailTemplate&&(this.mailUtilityService.mUtilityData.newMailTemplateData.forEach((e,t)=>{this.mailUtilityService.mUtilityData.reviewPagination[t]=e}),this.reviewPageKeys=Object.keys(this.mailUtilityService.mUtilityData.reviewPagination),this.mailUtilityService.mUtilityData.recipientMailIdArr=this.mailUtilityService.mUtilityData.reviewPagination[0].toMailIds,this.mailUtilityService.mUtilityData.selectedNewMailTemplateData=this.mailUtilityService.mUtilityData.reviewPagination[0],this.totalPage=this.mailUtilityService.mUtilityData.newMailTemplateData.length,console.log(this.mailUtilityService.mUtilityData.selectedNewMailTemplateData))}changeMailFolder(){}ngOnDestroy(){this.mailUtilityService.mUtilityData.dunningMailData=[],this.mailUtilityService.mUtilityData.currentMailMode={}}closeDialog(){this.dialogRef.close(),this.mailUtilityService.mUtilityData.dunningMailData=[]}}return e.\u0275fac=function(t){return new(t||e)(k["\u0275\u0275directiveInject"](R.h),k["\u0275\u0275directiveInject"](z.a))},e.\u0275cmp=k["\u0275\u0275defineComponent"]({type:e,selectors:[["app-view-mail"]],decls:20,vars:3,consts:[[1,"view-mail","px-0"],[1,"row",2,"width","100vw"],[1,"col-1","px-0","mx-0",2,"max-width","70px"],[1,"nav-bar","px-1",2,"height","100vh"],[1,"row","justify-content-center","mb-5"],["mat-mini-fab","",1,"progress-btn"],["class","my-3 py-1",3,"ngClass","click",4,"ngFor","ngForOf"],[1,"col-11","pl-0","pr-2","mx-0"],[1,"d-flex","flex-row","mail-box-header","justify-content-between","py-2",2,"padding","0px"],["class","my-auto d-flex flex-row ml-auto",4,"ngIf"],[1,"d-flex","flex-row","ml-auto"],["mat-raised-button","",1,"mr-5","my-auto",3,"ngClass","click"],["matTooltip","close","mat-icon-button","",1,"my-auto",3,"click"],[1,"icon-btn"],[1,"px-0"],[1,"my-3","py-1",3,"ngClass","click"],[1,"selector-bar"],[1,"side-item","pt-1"],["matListIcon",""],["matLine",""],[1,"my-auto","d-flex","flex-row","ml-auto"],["mat-icon-button","",3,"disabled","click"],[1,"mx-3","my-auto","extra-dark-text"]],template:function(e,t){1&e&&(k["\u0275\u0275elementStart"](0,"div",0),k["\u0275\u0275elementStart"](1,"div",1),k["\u0275\u0275elementStart"](2,"div",2),k["\u0275\u0275elementStart"](3,"mat-card",3),k["\u0275\u0275elementStart"](4,"div",4),k["\u0275\u0275elementStart"](5,"button",5),k["\u0275\u0275elementStart"](6,"mat-icon"),k["\u0275\u0275text"](7,"email"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275template"](8,it,8,3,"div",6),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](9,"div",7),k["\u0275\u0275elementStart"](10,"div",8),k["\u0275\u0275template"](11,nt,9,4,"div",9),k["\u0275\u0275elementStart"](12,"div",10),k["\u0275\u0275elementStart"](13,"button",11),k["\u0275\u0275listener"]("click",(function(){return t.mailUtilityService.initiateNewMail()})),k["\u0275\u0275text"](14," Initiate a new mail "),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](15,"button",12),k["\u0275\u0275listener"]("click",(function(){return t.closeDialog()})),k["\u0275\u0275elementStart"](16,"mat-icon",13),k["\u0275\u0275text"](17,"close"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementStart"](18,"div",14),k["\u0275\u0275element"](19,"app-mail-body"),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"](),k["\u0275\u0275elementEnd"]()),2&e&&(k["\u0275\u0275advance"](8),k["\u0275\u0275property"]("ngForOf",t.mailUtilityService.mailFolders),k["\u0275\u0275advance"](3),k["\u0275\u0275property"]("ngIf",t.mailUtilityService.mUtilityData.newMailTemplateData.length>1),k["\u0275\u0275advance"](2),k["\u0275\u0275property"]("ngClass","create"==t.mailUtilityService.mUtilityData.currentMailMode.mode?"new-mail-btn-active":"new-mail-btn-inactive"))},directives:[V.a,l.a,r.a,a.NgForOf,a.NgIf,a.NgClass,o.a,tt,A.c,A.b,j.l],styles:[".view-mail[_ngcontent-%COMP%]{overflow:hidden!important}.view-mail[_ngcontent-%COMP%]   .example-sidenav[_ngcontent-%COMP%]{width:76px!important;min-width:76px!important}.view-mail[_ngcontent-%COMP%]   .example-sidenav[_ngcontent-%COMP%]     .list-style .mat-list-item-content{padding:0 10px!important}.view-mail[_ngcontent-%COMP%]   .menu-enable[_ngcontent-%COMP%]{background-color:hsla(0,0%,100%,.2)}.view-mail[_ngcontent-%COMP%]   .menu-enable[_ngcontent-%COMP%]   .selector-bar[_ngcontent-%COMP%]{height:50px;border-radius:5px;position:absolute;left:0;border-left:4px solid #b1b1b1}.view-mail[_ngcontent-%COMP%]   .menu-enable[_ngcontent-%COMP%]   .side-item[_ngcontent-%COMP%]{display:flex!important;flex-direction:column!important;width:100%}.view-mail[_ngcontent-%COMP%]   .menu-enable[_ngcontent-%COMP%]   .side-item[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%]{height:25px!important;width:25px!important;color:#b8b7b5;margin:auto}.view-mail[_ngcontent-%COMP%]   .menu-enable[_ngcontent-%COMP%]   .side-item[_ngcontent-%COMP%]   .mat-line[_ngcontent-%COMP%]{color:#a2a0a0;font-size:10px!important;margin:auto}.view-mail[_ngcontent-%COMP%]   .menu-disable[_ngcontent-%COMP%]   .side-item[_ngcontent-%COMP%]{display:flex!important;flex-direction:column!important;width:100%}.view-mail[_ngcontent-%COMP%]   .menu-disable[_ngcontent-%COMP%]   .side-item[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%]{height:25px!important;width:25px!important;color:#b8b7b5;margin:auto}.view-mail[_ngcontent-%COMP%]   .menu-disable[_ngcontent-%COMP%]   .side-item[_ngcontent-%COMP%]   .mat-line[_ngcontent-%COMP%]{color:#a2a0a0;font-size:10px!important;margin:auto}.view-mail[_ngcontent-%COMP%]   .menu-disable[_ngcontent-%COMP%]:hover{cursor:pointer;background-color:hsla(0,0%,100%,.2)}.view-mail[_ngcontent-%COMP%]   .menu-disable[_ngcontent-%COMP%]:hover   .side-item[_ngcontent-%COMP%]{display:flex!important;flex-direction:column!important;width:100%}.view-mail[_ngcontent-%COMP%]   .menu-disable[_ngcontent-%COMP%]:hover   .side-item[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%]{height:25px!important;width:25px!important;color:#b8b7b5;margin:auto}.view-mail[_ngcontent-%COMP%]   .menu-disable[_ngcontent-%COMP%]:hover   .side-item[_ngcontent-%COMP%]   .mat-line[_ngcontent-%COMP%]{color:#a2a0a0;font-size:10px!important;margin:auto}.view-mail[_ngcontent-%COMP%]   .nav-bar[_ngcontent-%COMP%]{background-color:#1e2733!important;height:100vh!important}.view-mail[_ngcontent-%COMP%]   .nav-bar[_ngcontent-%COMP%]   .progress-btn[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff}.view-mail[_ngcontent-%COMP%]   .example-list[_ngcontent-%COMP%]{width:76px;max-width:100%;border:none;min-height:60px;display:block;border-radius:4px;overflow:hidden}.view-mail[_ngcontent-%COMP%]   .mail-box-header[_ngcontent-%COMP%]{background-color:#f9f9f9}.view-mail[_ngcontent-%COMP%]   .mail-box-header[_ngcontent-%COMP%]   .icon-btn[_ngcontent-%COMP%]{color:#545352!important;font-size:20px}.view-mail[_ngcontent-%COMP%]   .mail-box-header[_ngcontent-%COMP%]   .new-mail-btn-active[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff}.view-mail[_ngcontent-%COMP%]   .mail-box-header[_ngcontent-%COMP%]   .new-mail-btn-inactive[_ngcontent-%COMP%]{background-color:#fff;color:#1a1a1a}.view-mail[_ngcontent-%COMP%]   .mail-box-header[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]{color:#000;font-size:16px;padding-left:23px}.view-mail[_ngcontent-%COMP%]   .mail-box-header[_ngcontent-%COMP%]   .extra-dark-text[_ngcontent-%COMP%]{color:#000;font-size:16px;font-weight:500}.view-mail[_ngcontent-%COMP%]   .mail-box-header[_ngcontent-%COMP%]   .dark-text[_ngcontent-%COMP%]{color:#000;font-size:16px}"]}),e})(),lt=(()=>{class e{}return e.\u0275mod=k["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=k["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[a.CommonModule,B,E.b,u.b,j.v,V.d,T.g,A.d,N.g,g.a,d.g,D.b]]}),e})()},m5YA:function(e,t,i){"use strict";i.d(t,"a",(function(){return h}));var n=i("0IaG"),a=i("fXoL"),l=i("tk/3"),r=i("bTqV"),o=i("NFeN"),c=i("Qu3c"),m=i("ofXK"),s=i("w4ga");function d(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"div"),a["\u0275\u0275elementStart"](1,"div",8),a["\u0275\u0275element"](2,"img",9),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](3,"div",8),a["\u0275\u0275text"](4," Please wait, we are opening the file! "),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]())}function p(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"ngx-doc-viewer",10),a["\u0275\u0275listener"]("loaded",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"]().isLoaded=!0})),a["\u0275\u0275elementEnd"]()}if(2&e){const e=a["\u0275\u0275nextContext"]();a["\u0275\u0275property"]("url",e.data.selectedFileUrl)}}function g(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",11),a["\u0275\u0275element"](1,"img",12),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"]();a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("src",e.data.selectedFileUrl,a["\u0275\u0275sanitizeUrl"])}}let h=(()=>{class e{constructor(e,t,i){this.data=e,this.dialogRef=t,this.http=i,this.isLoaded=!1,this.expHeaderId=e.expHeaderId}ngOnInit(){}getBlobUrl(e){return this.http.get(e,{responseType:"blob"})}downloadFile(e){if(this.expHeaderId){let t=e.substring(e.lastIndexOf("/")+1,e.indexOf("?"));this.getBlobUrl(e).subscribe(e=>{let i=URL.createObjectURL(e),n=document.createElement("a");n.href=i;let a=decodeURIComponent(t.replace(/%20/g," "));n.download="EX"+this.expHeaderId+"-"+a,n.click()})}else window.open(e)}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275directiveInject"](n.a),a["\u0275\u0275directiveInject"](n.h),a["\u0275\u0275directiveInject"](l.c))},e.\u0275cmp=a["\u0275\u0275defineComponent"]({type:e,selectors:[["attachment-doc-viewer"]],decls:13,vars:3,consts:[[1,"row"],[1,"col-10"],[1,"col-2"],["mat-icon-button","",3,"click"],["mat-icon-button","","matTooltip","Close",3,"click"],[4,"ngIf"],["viewer","google","style","height: 100%",3,"url","loaded",4,"ngIf","ngIfElse"],["showimg",""],[1,"d-flex","align-items-center","justify-content-center"],["src","https://assets.kebs.app/images/spinner.svg","alt",""],["viewer","google",2,"height","100%",3,"url","loaded"],[1,"d-flex","align-items-center","justify-content-center","mt-5"],["alt","",2,"height","80%","width","80%",3,"src"]],template:function(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",0),a["\u0275\u0275element"](1,"div",1),a["\u0275\u0275elementStart"](2,"div",2),a["\u0275\u0275elementStart"](3,"button",3),a["\u0275\u0275listener"]("click",(function(){return t.downloadFile(t.data.selectedFileUrl)})),a["\u0275\u0275elementStart"](4,"mat-icon"),a["\u0275\u0275text"](5,"download"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](6,"button",4),a["\u0275\u0275listener"]("click",(function(){return t.dialogRef.close()})),a["\u0275\u0275elementStart"](7,"mat-icon"),a["\u0275\u0275text"](8,"close"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](9,d,5,0,"div",5),a["\u0275\u0275template"](10,p,1,1,"ngx-doc-viewer",6),a["\u0275\u0275template"](11,g,2,1,"ng-template",null,7,a["\u0275\u0275templateRefExtractor"])),2&e){const e=a["\u0275\u0275reference"](12);a["\u0275\u0275advance"](9),a["\u0275\u0275property"]("ngIf",!t.isLoaded&&"png"!=t.data.fileFormat&&"jpg"!=t.data.fileFormat&&"jpeg"!=t.data.fileFormat&&"JPEG"!=t.data.fileFormat&&"JPG"!=t.data.fileFormat),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf","png"!=t.data.fileFormat&&"jpg"!=t.data.fileFormat&&"JPEG"!=t.data.fileFormat&&"JPG"!=t.data.fileFormat&&"jpeg"!=t.data.fileFormat)("ngIfElse",e)}},directives:[r.a,o.a,c.a,m.NgIf,s.a],styles:[""]}),e})()}}]);