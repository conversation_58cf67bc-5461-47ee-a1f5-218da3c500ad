(window.webpackJsonp=window.webpackJsonp||[]).push([[754],{"+efU":function(t,n,e){"use strict";e.r(n),e.d(n,"GovRepViewHistoryModalComponent",(function(){return v}));var o=e("0IaG"),a=e("xG9w"),i=e("wd/R"),r=e("ofXK"),s=e("bTqV"),l=(e("3Pt+"),e("NFeN")),c=(e("kmnG"),e("qFsG"),e("fXoL"));function p(t,n){if(1&t&&(c["\u0275\u0275elementStart"](0,"p",14),c["\u0275\u0275elementStart"](1,"span",15),c["\u0275\u0275text"](2),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](3,"span",15),c["\u0275\u0275text"](4," has moved milestone "),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](5,"span",16),c["\u0275\u0275text"](6),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](7,"span",15),c["\u0275\u0275text"](8," in project "),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](9,"span",16),c["\u0275\u0275text"](10),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](11,"span",15),c["\u0275\u0275text"](12," from "),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](13,"span",16),c["\u0275\u0275text"](14),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](15,"span",15),c["\u0275\u0275text"](16," to "),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](17,"span",16),c["\u0275\u0275text"](18),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&t){const t=c["\u0275\u0275nextContext"]().$implicit;c["\u0275\u0275advance"](2),c["\u0275\u0275textInterpolate1"](" ",t.changedByName?t.changedByName:t.changedBy,""),c["\u0275\u0275advance"](4),c["\u0275\u0275textInterpolate1"]("'",t.milestoneName,"'"),c["\u0275\u0275advance"](4),c["\u0275\u0275textInterpolate1"]("'",t.projectName,"'"),c["\u0275\u0275advance"](4),c["\u0275\u0275textInterpolate"](t.changedFrom),c["\u0275\u0275advance"](4),c["\u0275\u0275textInterpolate"](t.changedTo)}}function m(t,n){1&t&&(c["\u0275\u0275elementStart"](0,"span",15),c["\u0275\u0275text"](1," from "),c["\u0275\u0275elementEnd"]())}function d(t,n){if(1&t&&(c["\u0275\u0275elementStart"](0,"span",16),c["\u0275\u0275text"](1),c["\u0275\u0275pipe"](2,"date"),c["\u0275\u0275elementEnd"]()),2&t){const t=c["\u0275\u0275nextContext"](2).$implicit;c["\u0275\u0275advance"](1),c["\u0275\u0275textInterpolate"](c["\u0275\u0275pipeBind2"](2,1,t.changedFrom,"dd-MMM-yyyy"))}}function g(t,n){if(1&t&&(c["\u0275\u0275elementStart"](0,"p",14),c["\u0275\u0275elementStart"](1,"span",15),c["\u0275\u0275text"](2),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](3,"span",15),c["\u0275\u0275text"](4," has moved opportunity "),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](5,"span",16),c["\u0275\u0275text"](6),c["\u0275\u0275elementEnd"](),c["\u0275\u0275template"](7,m,2,0,"span",17),c["\u0275\u0275template"](8,d,3,4,"span",18),c["\u0275\u0275elementStart"](9,"span",15),c["\u0275\u0275text"](10," to "),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](11,"span",16),c["\u0275\u0275text"](12),c["\u0275\u0275pipe"](13,"date"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&t){const t=c["\u0275\u0275nextContext"]().$implicit;c["\u0275\u0275advance"](2),c["\u0275\u0275textInterpolate1"](" ",t.changedByName?t.changedByName:t.changedBy,""),c["\u0275\u0275advance"](4),c["\u0275\u0275textInterpolate2"]("'",t.opportunityId," - ",t.opportunityName,"'"),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",t.changedFrom&&"-"!=t.changedFrom),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",t.changedFrom&&"-"!=t.changedFrom),c["\u0275\u0275advance"](4),c["\u0275\u0275textInterpolate"](c["\u0275\u0275pipeBind2"](13,6,t.changedTo,"dd-MMM-yyyy"))}}function y(t,n){if(1&t&&(c["\u0275\u0275elementStart"](0,"div"),c["\u0275\u0275template"](1,p,19,5,"p",13),c["\u0275\u0275template"](2,g,14,9,"p",13),c["\u0275\u0275elementEnd"]()),2&t){const t=n.$implicit;c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","Billing"==t.type),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","OBV"==t.type)}}function h(t,n){if(1&t&&(c["\u0275\u0275elementStart"](0,"div"),c["\u0275\u0275elementStart"](1,"div",11),c["\u0275\u0275elementStart"](2,"p",12),c["\u0275\u0275text"](3),c["\u0275\u0275pipe"](4,"date"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275template"](5,y,3,2,"div",9),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&t){const t=n.$implicit;c["\u0275\u0275advance"](3),c["\u0275\u0275textInterpolate1"](" ",c["\u0275\u0275pipeBind2"](4,2,t.date,"dd-MMM-yyyy")," "),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("ngForOf",t.historyItems)}}function x(t,n){1&t&&(c["\u0275\u0275elementStart"](0,"div",19),c["\u0275\u0275elementStart"](1,"div"),c["\u0275\u0275elementStart"](2,"h4",20),c["\u0275\u0275text"](3," Oops ! No Changes Found ! "),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](4,"div",21),c["\u0275\u0275element"](5,"img",22),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]())}let v=(()=>{class t{constructor(t,n){this.dialogRef=t,this.inData=n}ngOnInit(){this.modalParams=this.inData.modalParams,this.modalParams.historyData=a.sortBy(this.modalParams.historyData,t=>{if(t.date)return i(t.date,"DD-MMM-YYYY").toDate()}),this.modalParams.historyData=this.modalParams.historyData.reverse()}closeModal(){this.dialogRef.close({event:"Close"})}}return t.\u0275fac=function(n){return new(n||t)(c["\u0275\u0275directiveInject"](o.h),c["\u0275\u0275directiveInject"](o.a))},t.\u0275cmp=c["\u0275\u0275defineComponent"]({type:t,selectors:[["gov-rep-view-history-modal"]],decls:14,vars:2,consts:[[1,"container-fluid","view-history-modal-styles"],[1,"row","pt-2","pb-2",2,"border-bottom","solid 1px #cacaca"],[1,"col-1","d-flex"],["mat-icon-button","",1,"bubble"],[1,"iconButton"],[1,"col-9","pl-0","mt-1","name"],[1,"col-2"],["mat-icon-button","","matTooltip","Close",1,"d-flex","ml-auto","mr-2","close-button",3,"click"],[1,"close-Icon"],[4,"ngFor","ngForOf"],["style","text-align: center",4,"ngIf"],[1,"ml-4","mr-4","mt-2","mb-1","pCenter13",2,"border-bottom","solid 1px #cacaca"],[1,"mb-1","mt-1","it13Grey"],["class","mb-1 mt-1",4,"ngIf"],[1,"mb-1","mt-1"],[1,"it13"],[1,"it13Red"],["class","it13",4,"ngIf"],["class","it13Red",4,"ngIf"],[2,"text-align","center"],[1,"d-flex","justify-content-center","align-items-center","mt-5","slide-in-top"],[1,"d-flex","justify-content-center","align-items-center","slide-from-down"],["src","https://assets.kebs.app/images/nomilestone.png","height","220","width","250",1,"mt-4"]],template:function(t,n){1&t&&(c["\u0275\u0275elementStart"](0,"div",0),c["\u0275\u0275elementStart"](1,"div",1),c["\u0275\u0275elementStart"](2,"div",2),c["\u0275\u0275elementStart"](3,"div",3),c["\u0275\u0275elementStart"](4,"mat-icon",4),c["\u0275\u0275text"](5,"history"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](6,"div",5),c["\u0275\u0275text"](7,"History"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](8,"div",6),c["\u0275\u0275elementStart"](9,"button",7),c["\u0275\u0275listener"]("click",(function(){return n.closeModal()})),c["\u0275\u0275elementStart"](10,"mat-icon",8),c["\u0275\u0275text"](11,"close"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275template"](12,h,6,5,"div",9),c["\u0275\u0275template"](13,x,6,0,"div",10),c["\u0275\u0275elementEnd"]()),2&t&&(c["\u0275\u0275advance"](12),c["\u0275\u0275property"]("ngForOf",n.modalParams.historyData),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",0==n.modalParams.historyData.length))},directives:[l.a,s.a,r.NgForOf,r.NgIf],pipes:[r.DatePipe],styles:[".view-history-modal-styles[_ngcontent-%COMP%]{background-size:200px 181px;background-repeat:no-repeat;overflow-y:hidden!important;min-height:95vh;background-position:99% 105%;padding-left:0!important;padding-right:0!important}.view-history-modal-styles[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]{display:flex;line-height:26px;border-radius:50%;cursor:context-menu!important;width:26px;height:26px;background-color:#fbfbfb;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.view-history-modal-styles[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#cf0001;font-size:16px;margin-top:4px!important;margin-left:5px!important}.view-history-modal-styles[_ngcontent-%COMP%]   .ta-c[_ngcontent-%COMP%]{text-align:center}.view-history-modal-styles[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.view-history-modal-styles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:30px!important;height:30px!important;line-height:30px!important}.view-history-modal-styles[_ngcontent-%COMP%]   .it13[_ngcontent-%COMP%]{color:#181818!important;font-weight:400!important;margin-bottom:0!important}.view-history-modal-styles[_ngcontent-%COMP%]   .it13[_ngcontent-%COMP%], .view-history-modal-styles[_ngcontent-%COMP%]   .it13Bold[_ngcontent-%COMP%]{font-size:13px!important;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.view-history-modal-styles[_ngcontent-%COMP%]   .it13Bold[_ngcontent-%COMP%]{color:#000!important;font-weight:500!important}.view-history-modal-styles[_ngcontent-%COMP%]   .it13Red[_ngcontent-%COMP%]{color:#cf0001!important;font-size:13px!important;font-weight:400!important}.view-history-modal-styles[_ngcontent-%COMP%]   .it13Grey[_ngcontent-%COMP%], .view-history-modal-styles[_ngcontent-%COMP%]   .it13Red[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline;margin-bottom:0!important}.view-history-modal-styles[_ngcontent-%COMP%]   .it13Grey[_ngcontent-%COMP%]{color:#1f1f1f!important;font-size:14px!important;font-weight:500!important}.view-history-modal-styles[_ngcontent-%COMP%]   .fs9[_ngcontent-%COMP%]{font-size:9px!important;align-self:center!important;padding-top:1px!important}.view-history-modal-styles[_ngcontent-%COMP%]   .pCenter13[_ngcontent-%COMP%]{line-height:26px}.view-history-modal-styles[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#3c3c3c;font-weight:400;font-size:18px}.view-history-modal-styles[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.view-history-modal-styles[_ngcontent-%COMP%]   .mr5[_ngcontent-%COMP%]{margin-right:5px!important}.view-history-modal-styles[_ngcontent-%COMP%]   .name[_ngcontent-%COMP%]{font-size:14px;color:#cf0001;font-family:Roboto;font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.view-history-modal-styles[_ngcontent-%COMP%]   .normal-font[_ngcontent-%COMP%]{color:#1a1a1a;font-size:13px;font-weight:400}.view-history-modal-styles[_ngcontent-%COMP%]   .arrow-in-sidnav[_ngcontent-%COMP%]{height:30px;width:30px;line-height:30px}.view-history-modal-styles[_ngcontent-%COMP%]   .arrow-in-sidnav[_ngcontent-%COMP%]   .expand-icons[_ngcontent-%COMP%]{font-size:18px}.sunday-gov-styles[_ngcontent-%COMP%]{min-height:48px!important;overflow-x:hidden}.sunday-gov-styles[_ngcontent-%COMP%]     .ngx-mat-drp-date-input{color:#66615b!important}.sunday-gov-styles[_ngcontent-%COMP%]     .ngx-mat-drp-controls{margin:57% auto!important}.sunday-gov-styles[_ngcontent-%COMP%]     .options{padding:20px;margin-top:20px;background-color:hsla(0,0%,74.9%,.15)}.sunday-gov-styles[_ngcontent-%COMP%]     .caption{font-size:14px;font-weight:500}.sunday-gov-styles[_ngcontent-%COMP%]     .option{width:10%;display:inline-block;margin-top:10px}.sunday-gov-styles[_ngcontent-%COMP%]   .input-class[_ngcontent-%COMP%]{background:0 0;border:none!important;font-size:12px;font-weight:500;width:70%!important;cursor:pointer;text-align:center}.sunday-gov-styles[_ngcontent-%COMP%]   .button-row[_ngcontent-%COMP%]{transition:all .2s}.sunday-gov-styles[_ngcontent-%COMP%]   .col-1x[_ngcontent-%COMP%]{flex:0 0 12.666667%;max-width:13.666667%}.sunday-gov-styles[_ngcontent-%COMP%]   .iconsSize[_ngcontent-%COMP%]{background:transparent;font-size:21px!important;color:#545352!important}.sunday-gov-styles[_ngcontent-%COMP%]     .card-panel .mat-menu-content .col-styles-lead{cursor:pointer!important;border-radius:4px!important}.sunday-gov-styles[_ngcontent-%COMP%]     .card-panel .mat-menu-content .col-styles-lead:hover{background-color:rgba(255,235,235,.6705882352941176)!important}.sunday-gov-styles[_ngcontent-%COMP%]   .mat-list-option[_ngcontent-%COMP%], .sunday-gov-styles[_ngcontent-%COMP%]   .mat-subheader[_ngcontent-%COMP%]{font-size:13px!important;height:24px!important;line-height:24px!important}.sunday-gov-styles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff;line-height:33px}.sunday-gov-styles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%], .sunday-gov-styles[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]{font-weight:400;font-size:13px!important;min-width:70px;padding:0 8px;line-height:31px;border-radius:4px;margin-right:6px!important;margin-bottom:3px}.sunday-gov-styles[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]{color:rgba(0,0,0,.534);background-color:rgba(194,193,193,.24)}.sunday-gov-styles[_ngcontent-%COMP%]   .pop-up-wrapper[_ngcontent-%COMP%]{box-shadow:0 3px 1px -2px rgba(0,0,0,.2),0 2px 2px 0 rgba(0,0,0,.14),0 1px 5px 0 rgba(0,0,0,.12)!important;padding:16px!important;background-color:#fff!important;max-width:300px!important;line-height:33px!important}.sunday-gov-styles[_ngcontent-%COMP%]   .horizontal-wrapper[_ngcontent-%COMP%]{overflow:hidden}.sunday-gov-styles[_ngcontent-%COMP%]   .version-button[_ngcontent-%COMP%]   .resource-costing-icon[_ngcontent-%COMP%]{visibility:hidden}.sunday-gov-styles[_ngcontent-%COMP%]   .version-button[_ngcontent-%COMP%]:hover{cursor:pointer}.sunday-gov-styles[_ngcontent-%COMP%]   .version-button[_ngcontent-%COMP%]:hover   .resource-costing-icon[_ngcontent-%COMP%]{visibility:visible!important;font-size:20px!important;animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-top{0%{transform:translateY(10px)}to{transform:translateY(0)}}"]}),t})()}}]);