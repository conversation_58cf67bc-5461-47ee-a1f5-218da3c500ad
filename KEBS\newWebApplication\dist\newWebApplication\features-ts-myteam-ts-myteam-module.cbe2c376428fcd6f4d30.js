(window.webpackJsonp=window.webpackJsonp||[]).push([[694],{HmYF:function(e,t,n){"use strict";n.d(t,"a",(function(){return c}));var a=n("mrSG"),i=n("Iab2"),o=n("EUZL"),r=n("wd/R"),s=n("xG9w"),l=n("fXoL");let c=(()=>{class e{constructor(){this.formatColumn=(e,t,n)=>{const a=o.utils.decode_range(e["!ref"]);for(let i=a.s.r+1;i<=a.e.r;++i){const a=o.utils.encode_cell({r:i,c:t});e[a]&&e[a].v&&(e[a].t="d",e[a].z=n)}}}exportAsExcelFile(e,t,n,a,i){console.log("Excel to JSON Service",e);const r=o.utils.json_to_sheet(e);if(i&&i.length){const e=o.utils.sheet_to_json(r,{header:1}).shift();for(const t of i){const n=e.indexOf(t.fieldKey);this.formatColumn(r,n,t.fieldFormat)}}null==n&&(n=[]),null==a&&(a="DD-MM-YYYY"),this.formatExcelDateData(r,n,a);const s=o.write({Sheets:{data:r},SheetNames:["data"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(s,t)}formatExcelDateData(e,t,n){for(let o of Object.keys(e))if(null!=e[o]&&null!=e[o].t&&null!=e[o].v&&r(e[o].v,n,!0).isValid()){let a=o.replace(/[0-9]/g,"")+"1";0==s.where(t,{value:e[a].v}).length&&null!=e[a]&&null!=e[a].t&&t.push({value:e[a].v,format:n})}let a=[],i=1;for(let o of t)for(let t of Object.keys(e)){let n=parseInt(t.replace(/[^0-9]/g,""));n>i&&(i=n),null!=e[t]&&null!=e[t].v&&e[t].v==o.value&&a.push({value:t.replace(/[0-9]/g,""),format:o.format})}for(let o of a)for(let t=2;t<=i;t++)null!=e[o.value+""+t]&&null!=e[o.value+""+t].t&&(e[o.value+""+t].t="d",null!=e[o.value+""+t].v&&"Invalid date"!=e[o.value+""+t].v?e[o.value+""+t].v=r(e[o.value+""+t].v,o.format).format("YYYY/MM/DD"):(console.log(e[o.value+""+t].t),e[o.value+""+t].v="",e[o.value+""+t].t="s"))}saveAsExcelFile(e,t){const n=new Blob([e],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8"});i.saveAs(n,t.concat(".xlsx"))}exportAsExcelFileWFH(e,t,n){const a=o.utils.json_to_sheet(e),i=o.utils.json_to_sheet(t),r=o.write({Sheets:{All_Approvals:a,Pending_Approvals:i},SheetNames:["All_Approvals","Pending_Approvals"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(r,n)}exportAsExcelFileForPayroll(e,t,n,a,i,r){const s=o.utils.json_to_sheet(e),l=o.utils.json_to_sheet(t),c=o.utils.json_to_sheet(n),m=o.utils.json_to_sheet(a),d=o.utils.json_to_sheet(i),p=o.write({Sheets:{Regular_Report:s,Intern_Report:l,Contract_Report:c,Perdiem_Report:m,RP_Report:d},SheetNames:["Regular_Report","Intern_Report","Contract_Report","Perdiem_Report","RP_Report"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(p,r)}exportAsCsvFileWithSheetName(e,t){return Object(a.c)(this,void 0,void 0,(function*(){let n=o.utils.book_new();for(let t of e){let e=o.utils.json_to_sheet(t.data);o.utils.book_append_sheet(n,e,t.sheetName)}let a=o.write(n,{bookType:"xlsx",type:"binary"});yield this.saveAsCsvFile(a,t)}))}saveAsCsvFile(e,t){return Object(a.c)(this,void 0,void 0,(function*(){const n=new Blob([yield this.s2ab(e)],{type:"application/octet-stream"});i.saveAs(n,t.concat(".csv"))}))}s2ab(e){return Object(a.c)(this,void 0,void 0,(function*(){for(var t=new ArrayBuffer(e.length),n=new Uint8Array(t),a=0;a<e.length;a++)n[a]=255&e.charCodeAt(a);return t}))}exportAsExcelFileWithCellMerge(e,t,n){const a=o.utils.json_to_sheet(e);a["!merges"]=n;const i=o.write({Sheets:{data:a},SheetNames:["data"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(i,t)}exportJsonToExcelWithMultipleSheets(e,t){return Object(a.c)(this,void 0,void 0,(function*(){let n=o.utils.book_new();for(let t of e){let e=o.utils.json_to_sheet(t.data);o.utils.book_append_sheet(n,e,t.sheetName)}let a=o.write(n,{bookType:"xlsx",type:"array"});this.saveAsExcelFile(a,t)}))}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275prov=l["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},sMdI:function(e,t,n){"use strict";n.r(t),n.d(t,"TsMyteamModule",(function(){return lt}));var a=n("ofXK"),i=n("tyNb"),o=n("mrSG"),r=n("fXoL"),s=n("3Pt+"),l=n("1yaQ"),c=n("FKr1"),m=n("wd/R"),d=n("xG9w"),p=n("1G5W"),h=n("XNiG"),u=n("Kh6U"),g=n("0IaG"),f=n("bTqV"),v=n("NFeN"),x=n("qFsG"),y=n("kmnG"),b=n("Rnji"),C=n("BVzC"),S=n("ihCf");let _=(()=>{class e{constructor(e,t,n,a,i){this.dialogRef=e,this.data=t,this.wfhService=n,this.fb=a,this.errorService=i,this.comeToOfficeForm=this.fb.group({reason:["",s.H.required]}),this.initForm=()=>{this.comeToOfficeForm.get("reason").valueChanges.subscribe(e=>{e&&(this.reason=e)})},this.closeDialog=()=>{this.dialogRef.close()},this.comeToOffice=()=>{console.log(this.reason),this.data.reason=this.reason,console.log("onclick comeToOffice"),console.log(this.data),this.comeToOfficeForm.valid?this.wfhService.comeToOffice(this.data).subscribe(e=>(console.log("comeToOffice response"),console.log(e),e&&this.dialogRef.close("success"),e),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Server Error while changing the Remote WorkX enddate",e&&e.params?e.params:e&&e.error?e.error.params:{})}):console.log("Please Fill the reason !")}}ngOnInit(){console.log(this.data),this.initForm()}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](g.h),r["\u0275\u0275directiveInject"](g.a),r["\u0275\u0275directiveInject"](b.a),r["\u0275\u0275directiveInject"](s.i),r["\u0275\u0275directiveInject"](C.a))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["app-wfh-reject-dialog"]],decls:24,vars:2,consts:[[3,"formGroup"],[1,"container-fluid","wfhInfoContainer"],[1,"row","pt-4"],[1,"col-11"],[1,"row"],[2,"font-weight","500","font-size","16px","color","#cf0001"],[1,"row","pt-2"],[1,"ml-0"],["appearance","outline",1,"textArea",2,"width","40rem"],["matInput","","cdkTextareaAutosize","","cdkAutosizeMinRows","6","cdkAutosizeMaxRows","","placeholder","Reason","formControlName","reason"],["autosize","cdkTextareaAutosize"],[1,"col-1"],[1,"ml-1"],["mat-icon-button","",1,"ml-auto","trend-button-inactive",3,"click"],[1,"iconButton"],[1,"col-9"],[1,"col-2",2,"padding-left","4.5rem !important"],["mat-icon-button","","matTooltip","Come to Office",1,"btn-active",3,"click"]],template:function(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"form",0),r["\u0275\u0275elementStart"](1,"div",1),r["\u0275\u0275elementStart"](2,"div",2),r["\u0275\u0275elementStart"](3,"div",3),r["\u0275\u0275elementStart"](4,"div",4),r["\u0275\u0275elementStart"](5,"span",5),r["\u0275\u0275text"](6),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](7,"div",6),r["\u0275\u0275elementStart"](8,"span",7),r["\u0275\u0275elementStart"](9,"mat-form-field",8),r["\u0275\u0275element"](10,"textarea",9,10),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](12,"div",11),r["\u0275\u0275elementStart"](13,"span",12),r["\u0275\u0275elementStart"](14,"button",13),r["\u0275\u0275listener"]("click",(function(){return t.closeDialog()})),r["\u0275\u0275elementStart"](15,"mat-icon",14),r["\u0275\u0275text"](16,"clear"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](17,"div",4),r["\u0275\u0275element"](18,"div",15),r["\u0275\u0275elementStart"](19,"div",16),r["\u0275\u0275elementStart"](20,"span"),r["\u0275\u0275elementStart"](21,"button",17),r["\u0275\u0275listener"]("click",(function(){return t.comeToOffice()})),r["\u0275\u0275elementStart"](22,"mat-icon"),r["\u0275\u0275text"](23,"exit_to_app"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e&&(r["\u0275\u0275property"]("formGroup",t.comeToOfficeForm),r["\u0275\u0275advance"](6),r["\u0275\u0275textInterpolate1"](" Kindly , let ",t.data.employee_name," know the reason for WFH Cancellation !"))},directives:[s.J,s.w,s.n,y.c,x.b,S.b,s.e,s.v,s.l,f.a,v.a],styles:[".wfhInfoContainer[_ngcontent-%COMP%]   .icon-btn[_ngcontent-%COMP%]{height:35px;width:35px;line-height:35px}.wfhInfoContainer[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%]{font-size:17px}.wfhInfoContainer[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff}.wfhInfoContainer[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%], .wfhInfoContainer[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]{font-weight:400;font-size:13px!important;line-height:31px;padding:0 8px;border-radius:30px;margin-right:6px!important;margin-bottom:3px;min-width:17%}.wfhInfoContainer[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]{color:rgba(0,0,0,.534);background-color:rgba(194,193,193,.24)}.wfhInfoContainer[_ngcontent-%COMP%]   .smallCardIcon[_ngcontent-%COMP%]{font-size:18px!important;color:#868683!important}.wfhInfoContainer[_ngcontent-%COMP%]   .trend-button-inactive[_ngcontent-%COMP%]{line-height:8px;width:26px;height:26px;margin-right:12px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.wfhInfoContainer[_ngcontent-%COMP%]   .trend-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.wfhInfoContainer[_ngcontent-%COMP%]   .create-account-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%!important}.wfhInfoContainer[_ngcontent-%COMP%]   .textArea[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-flex{min-width:30rem}"]}),e})();var w=n("EUZL"),D=n("ug40"),E=n("LcQX"),M=n("XXEo"),O=n("Z2Ud"),A=n("HmYF"),T=n("jaxi"),I=n("Qu3c"),P=n("iadO"),k=n("STbY"),V=n("Xa2L"),L=n("me71"),Y=n("lVl8"),j=n("bSwM"),R=n("/QRN");const N=["submissionView"],U=["fileInput"],F=function(e){return{"btn-toggle-selected":e}};function W(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"mat-button-toggle",9),r["\u0275\u0275text"](1),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275property"]("ngClass",r["\u0275\u0275pureFunction1"](2,F,"wfh"==e.selectedToggle)),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"]("Remote WorX (",e.myTeamWfhData?e.myTeamWfhData.length:0,") ")}}function B(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"mat-button-toggle",10),r["\u0275\u0275text"](1),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275property"]("ngClass",r["\u0275\u0275pureFunction1"](2,F,"lbDetails"==e.selectedToggle)),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"]("Leave Details (",e.leaveDetailData?e.leaveDetailData.length:0,") ")}}function z(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"div",2),r["\u0275\u0275elementStart"](1,"div",3),r["\u0275\u0275elementStart"](2,"mat-button-toggle-group",4,5),r["\u0275\u0275listener"]("change",(function(t){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"]().selectToggle(t)})),r["\u0275\u0275elementStart"](4,"mat-button-toggle",6),r["\u0275\u0275text"](5),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](6,W,2,4,"mat-button-toggle",7),r["\u0275\u0275template"](7,B,2,4,"mat-button-toggle",8),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&e){const e=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("value",e.selectedToggle),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngClass",r["\u0275\u0275pureFunction1"](5,F,"ts"==e.selectedToggle)),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"]("Time Sheet (",e.associateData?e.associateData.length:0,")"),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",e.needToShowRemoteWorkx),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",e.isLeaveDetAllowedInMyTeams)}}function $(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"button",43),r["\u0275\u0275listener"]("click",(function(){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"](3).viewByCostCenter("By Cost Center")})),r["\u0275\u0275text"](1," By Cost Center "),r["\u0275\u0275elementEnd"]()}}function H(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"button",43),r["\u0275\u0275listener"]("click",(function(){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"](3).viewByCostCenter("By Cost Center")})),r["\u0275\u0275text"](1),r["\u0275\u0275elementEnd"]()}if(2&e){const e=r["\u0275\u0275nextContext"](3);r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"](" By ",e.tsProperties.ts_ui_text," ")}}function G(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"button",43),r["\u0275\u0275listener"]("click",(function(){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"](3).downloadShiftScheduleTemplate()})),r["\u0275\u0275text"](1," Download Schedule Template "),r["\u0275\u0275elementEnd"]()}}function X(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"button",43),r["\u0275\u0275listener"]("click",(function(){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"](3).uploadShiftScheduleTemplate()})),r["\u0275\u0275text"](1," Upload Schedule "),r["\u0275\u0275elementEnd"]()}}function J(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"mat-icon",23),r["\u0275\u0275listener"]("click",(function(){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"](3).notifyAllSelected()})),r["\u0275\u0275text"](1,"send"),r["\u0275\u0275elementEnd"]()}}function Q(e,t){1&e&&r["\u0275\u0275element"](0,"mat-spinner",56)}function K(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"button",57),r["\u0275\u0275elementStart"](1,"mat-icon",58),r["\u0275\u0275listener"]("dblclick",(function(){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"](3).closeAdminSideNav()}))("click",(function(){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"](3).openAdminSidenav()})),r["\u0275\u0275text"](2,"person"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&e){const e=r["\u0275\u0275nextContext"](3);r["\u0275\u0275property"]("matTooltip",e.isAdminViewActivated?"Double Click to get back to your view":"Admin View")("ngClass",e.isAdminViewActivated?"view-button-active":"view-button-inactive")}}function q(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",62),r["\u0275\u0275text"](1),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](2,"div",62),r["\u0275\u0275text"](3),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](4);r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate"](null==e.adminBasedUser?null:e.adminBasedUser.name),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate1"](" ",null==e.adminBasedUser?null:e.adminBasedUser.role,"")}}function Z(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"span",59),r["\u0275\u0275element"](1,"app-user-image",60),r["\u0275\u0275template"](2,q,4,2,"ng-template",null,61,r["\u0275\u0275templateRefExtractor"]),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275reference"](3),t=r["\u0275\u0275nextContext"](3);r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("id",t.adminBasedUser?t.adminBasedUser.oid:"")("tooltip",e)}}function ee(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"button",63),r["\u0275\u0275elementStart"](1,"mat-icon",23),r["\u0275\u0275listener"]("click",(function(){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"](3).homeView()})),r["\u0275\u0275text"](2,"settings_backup_restore"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}}function te(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"span",64),r["\u0275\u0275text"](1),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](3);r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate"](e.currentView)}}function ne(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"button",65),r["\u0275\u0275listener"]("click",(function(){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"](3).expandAllMyTeams()})),r["\u0275\u0275text"](1),r["\u0275\u0275elementEnd"]()}if(2&e){const e=r["\u0275\u0275nextContext"](3);r["\u0275\u0275styleProp"]("opacity",e.costCenterViewActivated?.5:1),r["\u0275\u0275property"]("disabled",e.costCenterViewActivated),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate"](e.btnDisplayText)}}function ae(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div",72),r["\u0275\u0275text"](1,"Cost center"),r["\u0275\u0275elementEnd"]())}function ie(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",72),r["\u0275\u0275text"](1),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](5);r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate"](e.tsProperties.ts_ui_text)}}function oe(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"div",68),r["\u0275\u0275elementStart"](1,"div",69),r["\u0275\u0275elementStart"](2,"mat-checkbox",70),r["\u0275\u0275listener"]("ngModelChange",(function(t){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"](4).selectAllActivated=t}))("change",(function(){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"](4).selectAll()})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](3,"div",71),r["\u0275\u0275text"](4,"Name"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](5,"div",72),r["\u0275\u0275text"](6,"Details"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](7,ae,2,0,"div",73),r["\u0275\u0275template"](8,ie,2,1,"div",73),r["\u0275\u0275elementStart"](9,"div",72),r["\u0275\u0275text"](10,"Status"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](11,"div",72),r["\u0275\u0275text"](12,"Date"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&e){const e=r["\u0275\u0275nextContext"](4);r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngModel",e.selectAllActivated),r["\u0275\u0275advance"](5),r["\u0275\u0275property"]("ngIf",0==e.tsProperties.ts_ui_change_and_download_icon),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",1==e.tsProperties.ts_ui_change_and_download_icon)}}function re(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"button",101),r["\u0275\u0275elementStart"](1,"mat-icon",23),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const t=r["\u0275\u0275nextContext"](2).index;return r["\u0275\u0275nextContext"](4).bringReportees(t,!0)})),r["\u0275\u0275text"](2),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&e){const e=r["\u0275\u0275nextContext"](2).$implicit;r["\u0275\u0275property"]("matTooltip",e.hierarchyActivated?"Collapse":"Expand"),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate1"](" ",e.hierarchyActivated?"keyboard_arrow_down":"keyboard_arrow_right","")}}function se(e,t){1&e&&r["\u0275\u0275element"](0,"mat-spinner",102)}function le(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"span",103),r["\u0275\u0275text"](1),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2).$implicit;r["\u0275\u0275property"]("matTooltip",e.group_description),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"]("(",e.group_code,")")}}function ce(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div"),r["\u0275\u0275elementStart"](1,"div",104),r["\u0275\u0275elementStart"](2,"div",105),r["\u0275\u0275elementStart"](3,"div",106),r["\u0275\u0275elementStart"](4,"div",107),r["\u0275\u0275text"](5),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](6,"div",108),r["\u0275\u0275text"](7),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](8,"div",109),r["\u0275\u0275text"](9),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;r["\u0275\u0275advance"](5),r["\u0275\u0275textInterpolate1"]("",e.cc_hours?e.cc_hours:"-"," "),r["\u0275\u0275advance"](1),r["\u0275\u0275propertyInterpolate2"]("matTooltip","",e.cc_code?e.cc_code:"-"," - ",e.cc_name?e.cc_name:"-",""),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate2"](" ",e.cc_code?e.cc_code:"-"," - ",e.cc_name?e.cc_name:"-",""),r["\u0275\u0275advance"](1),r["\u0275\u0275propertyInterpolate"]("matTooltip",e.cc_location?e.cc_location:"-"),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"](" ",e.cc_location?e.cc_location:"-"," ")}}function me(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"button",110),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const t=r["\u0275\u0275nextContext"](2),n=t.$implicit,a=t.index;return r["\u0275\u0275nextContext"](4).makeAsAdminApprover("grant",n,a)})),r["\u0275\u0275elementStart"](1,"mat-icon",111),r["\u0275\u0275text"](2,"done"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}}function de(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"button",112),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const t=r["\u0275\u0275nextContext"](2),n=t.$implicit,a=t.index;return r["\u0275\u0275nextContext"](4).makeAsAdminApprover("revoke",n,a)})),r["\u0275\u0275elementStart"](1,"mat-icon",111),r["\u0275\u0275text"](2,"close"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}}function pe(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"button",113),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const t=r["\u0275\u0275nextContext"](2).index;return r["\u0275\u0275nextContext"](4).showSummary(!1,t)})),r["\u0275\u0275elementStart"](1,"mat-icon",111),r["\u0275\u0275text"](2,"show_chart"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}}function he(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"button",114),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const t=r["\u0275\u0275nextContext"](2).index;return r["\u0275\u0275nextContext"](4).notifyIndividual(!1,t)})),r["\u0275\u0275elementStart"](1,"mat-icon",111),r["\u0275\u0275text"](2,"send"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}}function ue(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"button",115),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const t=r["\u0275\u0275nextContext"](2).$implicit;return r["\u0275\u0275nextContext"](4).openEmployeeConfig(t)})),r["\u0275\u0275elementStart"](1,"mat-icon",111),r["\u0275\u0275text"](2,"app_registration"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}}const ge=function(e){return{"background-color":e}};function fe(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"div",75),r["\u0275\u0275elementStart"](1,"div",76),r["\u0275\u0275elementStart"](2,"div",77),r["\u0275\u0275elementStart"](3,"div",78),r["\u0275\u0275elementStart"](4,"div",79),r["\u0275\u0275elementStart"](5,"div",80),r["\u0275\u0275elementStart"](6,"span",81),r["\u0275\u0275elementStart"](7,"mat-checkbox",70),r["\u0275\u0275listener"]("ngModelChange",(function(t){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"]().$implicit.checked=t}))("change",(function(){r["\u0275\u0275restoreView"](e);const t=r["\u0275\u0275nextContext"]().$implicit;return r["\u0275\u0275nextContext"](4).selectIndividual(t.oid)})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](8,re,3,2,"button",82),r["\u0275\u0275template"](9,se,1,0,"mat-spinner",83),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](10,"div",84),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const t=r["\u0275\u0275nextContext"]().index;return r["\u0275\u0275nextContext"](4).cardClicked(!1,t)})),r["\u0275\u0275element"](11,"app-user-image",85),r["\u0275\u0275elementStart"](12,"span",86),r["\u0275\u0275text"](13),r["\u0275\u0275template"](14,le,2,2,"span",87),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](15,"div",88),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const t=r["\u0275\u0275nextContext"]().index;return r["\u0275\u0275nextContext"](4).cardClicked(!1,t)})),r["\u0275\u0275text"](16),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](17,"div",89),r["\u0275\u0275template"](18,ce,10,7,"div",67),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](19,"div",90),r["\u0275\u0275elementStart"](20,"span",91),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const t=r["\u0275\u0275nextContext"]().index;return r["\u0275\u0275nextContext"](4).cardClicked(!1,t)})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](21,"span",92),r["\u0275\u0275text"](22),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](23,"div",93),r["\u0275\u0275elementStart"](24,"span",94),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const t=r["\u0275\u0275nextContext"]().index;return r["\u0275\u0275nextContext"](4).cardClicked(!1,t)})),r["\u0275\u0275text"](25),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](26,"div",95),r["\u0275\u0275template"](27,me,3,0,"button",96),r["\u0275\u0275template"](28,de,3,0,"button",97),r["\u0275\u0275template"](29,pe,3,0,"button",98),r["\u0275\u0275template"](30,he,3,0,"button",99),r["\u0275\u0275template"](31,ue,3,0,"button",100),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&e){const e=r["\u0275\u0275nextContext"](),t=e.$implicit,n=e.index,a=r["\u0275\u0275nextContext"](4);r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngClass",a.getIndent(t.level)),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngClass","Submitted"==t.status?"is-submitted-side":"Approved"==t.status?"is-approved-side":"Rejected"==t.status?"is-rejected-side":"Draft"==t.status||"Unsubmitted"==t.status?"is-draft-side":"")("ngStyle",r["\u0275\u0275pureFunction1"](21,ge,1==t.only_approver_admin||a.index==n&&"grant"==a.operation?"#e3fbe3":"transparent")),r["\u0275\u0275advance"](5),r["\u0275\u0275property"]("ngModel",t.checked),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",1==t.is_org_head),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",t.hierarchyLoading),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("matTooltip",a.getLength(t.name?t.name:"a")>15?t.name:""),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("id",t.oid),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate1"]("",t.name," "),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","P"!=t.group_code),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("matTooltip",t.details?t.details:"-"),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"](" ",t.details?t.details:"-"," "),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngForOf",t.costCenterDetails),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngClass","Draft"==t.status?"is-draft":"Submitted"==t.status?"is-submitted":"Approved"==t.status?"is-approved":"Rejected"==t.status?"is-rejected":"Unsubmitted"==t.status?"is-draft":""),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate1"](" ",t.status,""),r["\u0275\u0275advance"](3),r["\u0275\u0275textInterpolate1"](" ",t.date?a.getLocalDate(t.date):"-",""),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngIf",a.tsProperties.approver_admin_config&&"Direct Reportee"==t.reportee_type&&0==t.level),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",a.tsProperties.approver_admin_config&&"Direct Reportee"==t.reportee_type&&0==t.level),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","Direct Reportee"==t.reportee_type),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","Draft"==t.status||"Unsubmitted"==t.status),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",a.tsProperties.show_teams_employee_config)}}function ve(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div"),r["\u0275\u0275template"](1,fe,32,23,"div",74),r["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",e.showLevel)}}function xe(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div"),r["\u0275\u0275template"](1,oe,13,3,"div",66),r["\u0275\u0275template"](2,ve,2,1,"div",67),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](3);r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",e.associateData.length>0),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngForOf",e.associateData)}}function ye(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div",72),r["\u0275\u0275text"](1,"Cost center"),r["\u0275\u0275elementEnd"]())}function be(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",72),r["\u0275\u0275text"](1),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](4);r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate"](e.tsProperties.ts_ui_text)}}function Ce(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"span",103),r["\u0275\u0275text"](1),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]().$implicit;r["\u0275\u0275property"]("matTooltip",e.group_description),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"]("(",e.group_code,")")}}function Se(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"button",132),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const t=r["\u0275\u0275nextContext"]().index,n=r["\u0275\u0275nextContext"]().$implicit;return r["\u0275\u0275nextContext"](4).showSummary(n.costcenter,t)})),r["\u0275\u0275elementStart"](1,"mat-icon",111),r["\u0275\u0275text"](2,"show_chart"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}}function _e(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"button",114),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const t=r["\u0275\u0275nextContext"]().index,n=r["\u0275\u0275nextContext"]().$implicit;return r["\u0275\u0275nextContext"](4).notifyIndividual(n.costcenter,t)})),r["\u0275\u0275elementStart"](1,"mat-icon",111),r["\u0275\u0275text"](2,"send"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}}function we(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"div",75),r["\u0275\u0275elementStart"](1,"div",122),r["\u0275\u0275elementStart"](2,"div",123),r["\u0275\u0275elementStart"](3,"div",78),r["\u0275\u0275elementStart"](4,"div",79),r["\u0275\u0275elementStart"](5,"div",124),r["\u0275\u0275elementStart"](6,"span",125),r["\u0275\u0275elementStart"](7,"mat-checkbox",70),r["\u0275\u0275listener"]("ngModelChange",(function(e){return t.$implicit.checked=e}))("change",(function(){r["\u0275\u0275restoreView"](e);const n=t.$implicit;return r["\u0275\u0275nextContext"](5).selectIndividual(n.oid)})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](8,"div",126),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const n=t.index,a=r["\u0275\u0275nextContext"]().$implicit;return r["\u0275\u0275nextContext"](4).cardClicked(a.costcenter,n)})),r["\u0275\u0275element"](9,"app-user-image",85),r["\u0275\u0275elementStart"](10,"span",86),r["\u0275\u0275text"](11),r["\u0275\u0275template"](12,Ce,2,2,"span",87),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](13,"div",127),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const n=t.index,a=r["\u0275\u0275nextContext"]().$implicit;return r["\u0275\u0275nextContext"](4).cardClicked(a.costcenter,n)})),r["\u0275\u0275text"](14),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](15,"div",128),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const n=t.index,a=r["\u0275\u0275nextContext"]().$implicit;return r["\u0275\u0275nextContext"](4).cardClicked(a.costcenter,n)})),r["\u0275\u0275text"](16),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](17,"div",90),r["\u0275\u0275elementStart"](18,"span",91),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const n=t.index,a=r["\u0275\u0275nextContext"]().$implicit;return r["\u0275\u0275nextContext"](4).cardClicked(a.costcenter,n)})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](19,"span",129),r["\u0275\u0275text"](20),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](21,"div",130),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const n=t.index,a=r["\u0275\u0275nextContext"]().$implicit;return r["\u0275\u0275nextContext"](4).cardClicked(a.costcenter,n)})),r["\u0275\u0275text"](22),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](23,"div",80),r["\u0275\u0275template"](24,Se,3,0,"button",131),r["\u0275\u0275template"](25,_e,3,0,"button",99),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=r["\u0275\u0275nextContext"](5);r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngClass","Submitted"==e.status?"is-submitted-side":"Approved"==e.status?"is-approved-side":"Rejected"==e.status?"is-rejected-side":"Draft"==e.status||"Unsubmitted"==e.status?"is-draft-side":""),r["\u0275\u0275advance"](5),r["\u0275\u0275property"]("ngModel",e.checked),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("matTooltip",n.getLength(e.name?e.name:"a")>15?e.name:""),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("id",e.oid),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate1"]("",e.name," "),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","P"!=e.group_code),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate1"](" ",e.details?e.details:"-"," "),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("matTooltip",n.getLength(e.cc_description?e.cc_description:"a")>10?e.cc_description:""),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate2"](" #",e.costcenter?e.costcenter:"-"," - ",e.cc_description?e.cc_description:"-"," "),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngClass","Draft"==e.status?"is-draft":"Submitted"==e.status?"is-submitted":"Approved"==e.status?"is-approved":"Rejected"==e.status?"is-rejected":"Unsubmitted"==e.status?"is-draft":""),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate1"](" ",e.status,""),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate1"](" ",e.date?n.getLocalDate(e.date):"-"," "),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngIf","Draft"==e.status||"Unsubmitted"==e.status),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","Draft"==e.status||"Unsubmitted"==e.status)}}function De(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"div"),r["\u0275\u0275elementStart"](1,"div",118),r["\u0275\u0275elementStart"](2,"div",119),r["\u0275\u0275elementStart"](3,"mat-checkbox",70),r["\u0275\u0275listener"]("ngModelChange",(function(e){return t.$implicit.checked=e}))("change",(function(){r["\u0275\u0275restoreView"](e);const n=t.$implicit;return r["\u0275\u0275nextContext"](4).selectAllOfCostCenters(n)})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](4,"span",120),r["\u0275\u0275text"](5),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](6,"div"),r["\u0275\u0275template"](7,we,26,15,"div",121),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=r["\u0275\u0275nextContext"](4);r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("ngModel",e.checked),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate4"]("#",e.costcenter?e.costcenter:"Unsubmitted"," - ",e.cc_name," : ",n.costCenterWiseAssociateData[e.costcenter].length," (",e.cc_total_hours," hours)"),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngForOf",n.costCenterWiseAssociateData[e.costcenter])}}function Ee(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div"),r["\u0275\u0275elementStart"](1,"div",68),r["\u0275\u0275element"](2,"div",116),r["\u0275\u0275elementStart"](3,"div",71),r["\u0275\u0275text"](4,"Name"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](5,"div",72),r["\u0275\u0275text"](6,"Details"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](7,ye,2,0,"div",73),r["\u0275\u0275template"](8,be,2,1,"div",73),r["\u0275\u0275elementStart"](9,"div",72),r["\u0275\u0275text"](10,"Status"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](11,"div",117),r["\u0275\u0275text"](12,"Date"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](13,De,8,6,"div",67),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](3);r["\u0275\u0275advance"](7),r["\u0275\u0275property"]("ngIf",0==e.tsProperties.ts_ui_change_and_download_icon),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",1==e.tsProperties.ts_ui_change_and_download_icon),r["\u0275\u0275advance"](5),r["\u0275\u0275property"]("ngForOf",e.allCostCenters)}}function Me(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div",72),r["\u0275\u0275text"](1,"Cost center"),r["\u0275\u0275elementEnd"]())}function Oe(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",72),r["\u0275\u0275text"](1),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](4);r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate"](e.tsProperties.ts_ui_text)}}function Ae(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div"),r["\u0275\u0275elementStart"](1,"div",134),r["\u0275\u0275elementStart"](2,"div",122),r["\u0275\u0275elementStart"](3,"div",135),r["\u0275\u0275elementStart"](4,"div",78),r["\u0275\u0275elementStart"](5,"div",79),r["\u0275\u0275elementStart"](6,"div",136),r["\u0275\u0275element"](7,"mat-checkbox"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](8,"div",137),r["\u0275\u0275elementStart"](9,"ngx-content-loading",138),r["\u0275\u0275namespaceSVG"](),r["\u0275\u0275element"](10,"g",139),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275namespaceHTML"](),r["\u0275\u0275elementStart"](11,"div",140),r["\u0275\u0275elementStart"](12,"ngx-content-loading",138),r["\u0275\u0275namespaceSVG"](),r["\u0275\u0275element"](13,"g",139),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275namespaceHTML"](),r["\u0275\u0275elementStart"](14,"div",141),r["\u0275\u0275elementStart"](15,"ngx-content-loading",138),r["\u0275\u0275namespaceSVG"](),r["\u0275\u0275element"](16,"g",139),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275namespaceHTML"](),r["\u0275\u0275elementStart"](17,"div",142),r["\u0275\u0275elementStart"](18,"ngx-content-loading",138),r["\u0275\u0275namespaceSVG"](),r["\u0275\u0275element"](19,"g",139),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275namespaceHTML"](),r["\u0275\u0275elementStart"](20,"div",143),r["\u0275\u0275elementStart"](21,"ngx-content-loading",138),r["\u0275\u0275namespaceSVG"](),r["\u0275\u0275element"](22,"g",139),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275namespaceHTML"](),r["\u0275\u0275elementStart"](23,"div",80),r["\u0275\u0275elementStart"](24,"button",144),r["\u0275\u0275elementStart"](25,"mat-icon",111),r["\u0275\u0275text"](26,"done"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](27,"button",145),r["\u0275\u0275elementStart"](28,"mat-icon",111),r["\u0275\u0275text"](29,"close"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e&&(r["\u0275\u0275advance"](9),r["\u0275\u0275property"]("speed","1500ms")("width",1e3)("height",100)("primaryColor","#ebebeb")("secondaryColor","#f7f7f7"),r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("speed","1500ms")("width",1e3)("height",100)("primaryColor","#ebebeb")("secondaryColor","#f7f7f7"),r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("speed","1500ms")("width",1e3)("height",100)("primaryColor","#ebebeb")("secondaryColor","#f7f7f7"),r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("speed","1500ms")("width",1e3)("height",100)("primaryColor","#ebebeb")("secondaryColor","#f7f7f7"),r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("speed","1500ms")("width",1e3)("height",100)("primaryColor","#ebebeb")("secondaryColor","#f7f7f7"))}function Te(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div"),r["\u0275\u0275elementStart"](1,"div",133),r["\u0275\u0275element"](2,"div",116),r["\u0275\u0275elementStart"](3,"div",71),r["\u0275\u0275text"](4,"Name"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](5,"div",72),r["\u0275\u0275text"](6,"Details"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](7,Me,2,0,"div",73),r["\u0275\u0275template"](8,Oe,2,1,"div",73),r["\u0275\u0275elementStart"](9,"div",72),r["\u0275\u0275text"](10,"Status"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](11,"div",72),r["\u0275\u0275text"](12,"Date"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](13,Ae,30,25,"div",67),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](3);r["\u0275\u0275advance"](7),r["\u0275\u0275property"]("ngIf",0==e.cc_config),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",1==e.cc_config),r["\u0275\u0275advance"](5),r["\u0275\u0275property"]("ngForOf",e.loadArr)}}function Ie(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div"),r["\u0275\u0275elementStart"](1,"div"),r["\u0275\u0275elementStart"](2,"h4",146),r["\u0275\u0275text"](3," No Team Member Found ! "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](4,"div",147),r["\u0275\u0275element"](5,"img",148),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]())}function Pe(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"div"),r["\u0275\u0275elementStart"](1,"div",14),r["\u0275\u0275elementStart"](2,"div",15),r["\u0275\u0275elementStart"](3,"span",16),r["\u0275\u0275text"](4," Total : "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](5,"span",17),r["\u0275\u0275text"](6),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](7,"div",18),r["\u0275\u0275elementStart"](8,"div",19),r["\u0275\u0275element"](9,"div",20),r["\u0275\u0275elementStart"](10,"div",21),r["\u0275\u0275elementStart"](11,"button",22),r["\u0275\u0275elementStart"](12,"mat-icon",23),r["\u0275\u0275listener"]("click",(function(){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"](2).goToPreviousMonth()})),r["\u0275\u0275text"](13,"keyboard_arrow_left"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](14,"div",24),r["\u0275\u0275elementStart"](15,"span",25),r["\u0275\u0275text"](16,"For : "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](17,"div",26),r["\u0275\u0275elementStart"](18,"mat-form-field",27),r["\u0275\u0275element"](19,"input",28),r["\u0275\u0275element"](20,"mat-datepicker-toggle",29),r["\u0275\u0275elementStart"](21,"mat-datepicker",30,31),r["\u0275\u0275listener"]("yearSelected",(function(t){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"](2).selectYear(t)}))("monthSelected",(function(t){r["\u0275\u0275restoreView"](e);const n=r["\u0275\u0275reference"](22);return r["\u0275\u0275nextContext"](2).selectMonth(t,n)})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](23,"div",32),r["\u0275\u0275elementStart"](24,"button",33),r["\u0275\u0275elementStart"](25,"mat-icon",23),r["\u0275\u0275listener"]("click",(function(){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"](2).goToNextMonth()})),r["\u0275\u0275text"](26,"keyboard_arrow_right"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](27,"div",34),r["\u0275\u0275elementStart"](28,"mat-form-field",35),r["\u0275\u0275elementStart"](29,"span",36),r["\u0275\u0275elementStart"](30,"mat-icon",37),r["\u0275\u0275text"](31,"search"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](32,"input",38),r["\u0275\u0275listener"]("keyup",(function(t){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"](2).applySearch(t.target.value)})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](33,"div",39),r["\u0275\u0275elementStart"](34,"button",40),r["\u0275\u0275elementStart"](35,"mat-icon",41),r["\u0275\u0275text"](36,"visibility"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](37,"mat-menu",null,42),r["\u0275\u0275elementStart"](39,"button",43),r["\u0275\u0275listener"]("click",(function(){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"](2).viewByDirectReportees("Direct Reportees")})),r["\u0275\u0275text"](40," Direct Reportees "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](41,"button",44),r["\u0275\u0275listener"]("click",(function(){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"](2).viewByMiscellaneous("Miscellaneous")})),r["\u0275\u0275text"](42," Miscellaneous "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](43,$,2,0,"button",45),r["\u0275\u0275template"](44,H,2,1,"button",45),r["\u0275\u0275template"](45,G,2,0,"button",45),r["\u0275\u0275elementStart"](46,"input",46,47),r["\u0275\u0275listener"]("change",(function(t){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"](2).onFileSelected(t)})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](48,X,2,0,"button",45),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](49,"button",48),r["\u0275\u0275template"](50,J,2,0,"mat-icon",49),r["\u0275\u0275template"](51,Q,1,0,"mat-spinner",50),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](52,K,3,2,"button",51),r["\u0275\u0275template"](53,Z,4,2,"span",52),r["\u0275\u0275template"](54,ee,3,0,"button",53),r["\u0275\u0275template"](55,te,2,1,"span",54),r["\u0275\u0275template"](56,ne,2,4,"button",55),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](57,"div"),r["\u0275\u0275template"](58,xe,3,2,"div",12),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](59,Ee,14,3,"div",12),r["\u0275\u0275template"](60,Te,14,3,"div",12),r["\u0275\u0275template"](61,Ie,6,0,"div",12),r["\u0275\u0275elementEnd"]()}if(2&e){const e=r["\u0275\u0275reference"](22),t=r["\u0275\u0275reference"](38),n=r["\u0275\u0275nextContext"](2);r["\u0275\u0275advance"](6),r["\u0275\u0275textInterpolate1"](" ",n.associateData?n.associateData.length:"-"," "),r["\u0275\u0275advance"](13),r["\u0275\u0275property"]("matDatepicker",e)("formControl",n.monthYearDate),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("for",e),r["\u0275\u0275advance"](14),r["\u0275\u0275property"]("ngClass",n.directReporteesViewActivated||n.miscellaneousViewActivated||n.costCenterViewActivated?"view-button-active":"view-button-inactive"),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("matMenuTriggerFor",t),r["\u0275\u0275advance"](6),r["\u0275\u0275property"]("matTooltip",0==n.cc_config?"Not direct Reportees, but booked in your cost center":"Not direct Reportees, but booked in your "+n.tsProperties.ts_ui_text),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngIf",0==n.cc_config),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",1==n.cc_config),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",n.showShiftSchedule),r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("ngIf",n.showShiftSchedule),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngIf",!n.notifyingActivated),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",n.notifyingActivated),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",n.isCurrentUserAdmin),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",n.isAdminViewActivated&&""!=n.adminBasedUser.oid&&""!=n.adminBasedUser.name),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",n.directReporteesViewActivated||n.miscellaneousViewActivated||n.costCenterViewActivated),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",n.directReporteesViewActivated||n.miscellaneousViewActivated||n.costCenterViewActivated),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",1==n.expandAllOption),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngIf",n.homeViewActivated||n.directReporteesViewActivated||n.miscellaneousViewActivated&&n.associateData.length>0),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",n.costCenterViewActivated&&n.associateData.length>0),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",0==n.associateData.length&&!n.noData),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",n.noData)}}function ke(e,t){1&e&&r["\u0275\u0275text"](0,"dajdka")}function Ve(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",11),r["\u0275\u0275template"](1,Pe,62,22,"div",12),r["\u0275\u0275template"](2,ke,1,0,"ng-template",null,13,r["\u0275\u0275templateRefExtractor"]),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",!e.detailComponentActivated&&!e.submissionComponentActivated)}}function Le(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"button",157),r["\u0275\u0275listener"]("click",(function(){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"](3).searchText=""})),r["\u0275\u0275elementStart"](1,"mat-icon",158),r["\u0275\u0275text"](2,"close "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}}function Ye(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",170),r["\u0275\u0275elementStart"](1,"span"),r["\u0275\u0275elementStart"](2,"b"),r["\u0275\u0275text"](3,"WFH Duration"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](4,"span",171),r["\u0275\u0275text"](5," :"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](6,"span",172),r["\u0275\u0275text"](7),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](8,"div",170),r["\u0275\u0275elementStart"](9,"span"),r["\u0275\u0275elementStart"](10,"b"),r["\u0275\u0275text"](11,"Reporting Days"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](12,"span",173),r["\u0275\u0275text"](13," : "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](14,"span",172),r["\u0275\u0275text"](15),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](16,"div",170),r["\u0275\u0275elementStart"](17,"span"),r["\u0275\u0275elementStart"](18,"b"),r["\u0275\u0275text"](19,"Reporting Weeks"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](20,"span",174),r["\u0275\u0275text"](21,":"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](22,"span",172),r["\u0275\u0275text"](23),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]().$implicit;r["\u0275\u0275advance"](7),r["\u0275\u0275textInterpolate1"]("",e.wfhDuration," "),r["\u0275\u0275advance"](8),r["\u0275\u0275textInterpolate1"](" ",e.reportingDays,""),r["\u0275\u0275advance"](8),r["\u0275\u0275textInterpolate"](e.reportingWeeks)}}function je(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"div",175),r["\u0275\u0275elementStart"](1,"button",176),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const t=r["\u0275\u0275nextContext"](),n=t.$implicit,a=t.index;return r["\u0275\u0275nextContext"](3).openComeToOfficeDialog(n,a)})),r["\u0275\u0275elementStart"](2,"mat-icon",111),r["\u0275\u0275text"](3,"exit_to_app"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](4,"button",177),r["\u0275\u0275listener"]("click",(function(t){r["\u0275\u0275restoreView"](e);const n=r["\u0275\u0275nextContext"]().$implicit;return r["\u0275\u0275nextContext"](3).openCommentsSideNav(n),t.stopPropagation()})),r["\u0275\u0275elementStart"](5,"mat-icon",111),r["\u0275\u0275text"](6,"question_answer"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}}const Re=function(e){return{color:e}};function Ne(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"div",75),r["\u0275\u0275elementStart"](1,"div",122),r["\u0275\u0275elementStart"](2,"mat-card",159),r["\u0275\u0275elementStart"](3,"div",78),r["\u0275\u0275elementStart"](4,"div",79),r["\u0275\u0275elementStart"](5,"div",160),r["\u0275\u0275element"](6,"app-user-image",85),r["\u0275\u0275elementStart"](7,"span",161),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const n=t.$implicit;return r["\u0275\u0275nextContext"](3).openWfhOverview(n.wfh_id)})),r["\u0275\u0275text"](8),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](9,"div",162),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const n=t.$implicit;return r["\u0275\u0275nextContext"](3).openWfhOverview(n.wfh_id)})),r["\u0275\u0275text"](10),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](11,Ye,24,3,"ng-template",null,163,r["\u0275\u0275templateRefExtractor"]),r["\u0275\u0275elementStart"](13,"span",164),r["\u0275\u0275elementStart"](14,"mat-icon",165),r["\u0275\u0275text"](15,"fiber_manual_record "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](16,"div",166),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const n=t.$implicit;return r["\u0275\u0275nextContext"](3).openWfhOverview(n.wfh_id)})),r["\u0275\u0275text"](17),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](18,"div",167),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const n=t.$implicit;return r["\u0275\u0275nextContext"](3).openWfhOverview(n.wfh_id)})),r["\u0275\u0275text"](19),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](20,"div",168),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const n=t.$implicit;return r["\u0275\u0275nextContext"](3).openWfhOverview(n.wfh_id)})),r["\u0275\u0275text"](21),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](22,je,7,0,"div",169),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=r["\u0275\u0275reference"](12),a=r["\u0275\u0275nextContext"](3);r["\u0275\u0275advance"](6),r["\u0275\u0275property"]("id",e.associate_oid),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate"](e.employee_name?e.employee_name:" - "),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("tooltip",n),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"](" ",e.duration_selection_text?e.duration_selection_text:" - "," "),r["\u0275\u0275advance"](4),r["\u0275\u0275property"]("ngStyle",r["\u0275\u0275pureFunction1"](9,Re,a.statusBasedColorSelector(e.status_id))),r["\u0275\u0275advance"](3),r["\u0275\u0275textInterpolate1"](" ",e.status_name," "),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate1"](" ",e.start_date+" to "+e.end_date," "),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate1"](" ",e.submitted_on," "),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",e.exitVisibility)}}function Ue(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"div"),r["\u0275\u0275elementStart"](1,"div",14),r["\u0275\u0275elementStart"](2,"div",149),r["\u0275\u0275elementStart"](3,"span",150),r["\u0275\u0275text"](4," Total : "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](5,"span",17),r["\u0275\u0275text"](6),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](7,"div",18),r["\u0275\u0275elementStart"](8,"div",19),r["\u0275\u0275element"](9,"div",20),r["\u0275\u0275elementStart"](10,"div",21),r["\u0275\u0275elementStart"](11,"button",22),r["\u0275\u0275elementStart"](12,"mat-icon",23),r["\u0275\u0275listener"]("click",(function(){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"](2).goToPreviousMonth()})),r["\u0275\u0275text"](13,"keyboard_arrow_left"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](14,"div",24),r["\u0275\u0275elementStart"](15,"span",25),r["\u0275\u0275text"](16,"For : "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](17,"div",26),r["\u0275\u0275elementStart"](18,"mat-form-field",27),r["\u0275\u0275element"](19,"input",28),r["\u0275\u0275element"](20,"mat-datepicker-toggle",29),r["\u0275\u0275elementStart"](21,"mat-datepicker",30,31),r["\u0275\u0275listener"]("yearSelected",(function(t){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"](2).selectYear(t)}))("monthSelected",(function(t){r["\u0275\u0275restoreView"](e);const n=r["\u0275\u0275reference"](22);return r["\u0275\u0275nextContext"](2).selectMonth(t,n)})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](23,"div",32),r["\u0275\u0275elementStart"](24,"button",33),r["\u0275\u0275elementStart"](25,"mat-icon",23),r["\u0275\u0275listener"]("click",(function(){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"](2).goToNextMonth()})),r["\u0275\u0275text"](26,"keyboard_arrow_right"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](27,"div",34),r["\u0275\u0275elementStart"](28,"mat-form-field",151),r["\u0275\u0275elementStart"](29,"span",36),r["\u0275\u0275elementStart"](30,"mat-icon",37),r["\u0275\u0275text"](31,"search"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275element"](32,"input",152),r["\u0275\u0275elementStart"](33,"mat-icon",153),r["\u0275\u0275template"](34,Le,3,0,"button",154),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](35,"div"),r["\u0275\u0275elementStart"](36,"div",68),r["\u0275\u0275elementStart"](37,"div",71),r["\u0275\u0275text"](38,"Name"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](39,"div",155),r["\u0275\u0275text"](40,"Details"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](41,"div",156),r["\u0275\u0275text"](42,"Status"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](43,"div",156),r["\u0275\u0275text"](44,"Duration"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](45,"div",156),r["\u0275\u0275text"](46,"Submitted On"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](47,Ne,23,11,"div",121),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&e){const e=r["\u0275\u0275reference"](22),t=r["\u0275\u0275nextContext"](2);r["\u0275\u0275advance"](6),r["\u0275\u0275textInterpolate1"](" ",t.myTeamWfhData?t.myTeamWfhData.length:0," "),r["\u0275\u0275advance"](13),r["\u0275\u0275property"]("matDatepicker",e)("formControl",t.monthYearDate),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("for",e),r["\u0275\u0275advance"](14),r["\u0275\u0275property"]("ngIf",t.isClearVisible),r["\u0275\u0275advance"](13),r["\u0275\u0275property"]("ngForOf",t.myTeamWfhData)}}function Fe(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div"),r["\u0275\u0275elementStart"](1,"div"),r["\u0275\u0275elementStart"](2,"h4",146),r["\u0275\u0275text"](3),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](4,"div",147),r["\u0275\u0275element"](5,"img",148),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275advance"](3),r["\u0275\u0275textInterpolate1"](" Team Members Remote WorX Data not found for ",e.getMonthName()," ! ")}}function We(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",11),r["\u0275\u0275template"](1,Ue,48,6,"div",12),r["\u0275\u0275template"](2,Fe,6,1,"div",12),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",!e.detailComponentActivated&&!e.submissionComponentActivated),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",e.myTeamWfhData&&0==e.myTeamWfhData.length)}}function Be(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"button",180),r["\u0275\u0275elementStart"](1,"mat-icon",158),r["\u0275\u0275text"](2,"close "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]())}function ze(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div",68),r["\u0275\u0275elementStart"](1,"div",181),r["\u0275\u0275text"](2,"Name"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](3,"div",182),r["\u0275\u0275text"](4,"Leave Type"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](5,"div",183),r["\u0275\u0275text"](6,"Leave Dates"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](7,"div",184),r["\u0275\u0275text"](8,"Leave Status"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]())}function $e(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",75),r["\u0275\u0275elementStart"](1,"div",122),r["\u0275\u0275elementStart"](2,"mat-card",185),r["\u0275\u0275elementStart"](3,"div",78),r["\u0275\u0275elementStart"](4,"div",79),r["\u0275\u0275elementStart"](5,"div",160),r["\u0275\u0275element"](6,"app-user-image",85),r["\u0275\u0275elementStart"](7,"span",186),r["\u0275\u0275text"](8),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](9,"div",187),r["\u0275\u0275elementStart"](10,"span",188),r["\u0275\u0275text"](11),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](12,"div",189),r["\u0275\u0275elementStart"](13,"span",188),r["\u0275\u0275text"](14),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](15,"div",190),r["\u0275\u0275elementStart"](16,"span",188),r["\u0275\u0275text"](17),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=r["\u0275\u0275nextContext"](3);r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngClass","l"==e.dayType?"is-leave-side":"fh"==e.dayType?"is-fh-side":"h"==e.dayType?"is-h-side":"pl"==e.dayType?"is-pl-side":"cl"==e.dayType?"is-cl-side":"oh"==e.dayType?"is-oh-side":"bl"==e.dayType?"is-bl-side":"wl"==e.dayType?"is-wl-side":"sl"==e.dayType?"is-sl-side":"cel"==e.dayType?"is-cel-side":""),r["\u0275\u0275advance"](4),r["\u0275\u0275property"]("id",e.oid),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("matTooltip",n.getLength(e.employee_name?e.employee_name:"a")>15?e.employee_name:""),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate"](e.employee_name?e.employee_name:" - "),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("matTooltip",e.leaveDescription),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate2"]("",e.leave_type," - ",e.leaveDescription,""),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("matTooltip",e.currentDate),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate"](e.currentDate),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("matTooltip",e.leaveStatus),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate"](e.leaveStatus)}}function He(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"div"),r["\u0275\u0275elementStart"](1,"div",14),r["\u0275\u0275elementStart"](2,"div",149),r["\u0275\u0275elementStart"](3,"span",150),r["\u0275\u0275text"](4," Total : "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](5,"span",17),r["\u0275\u0275text"](6),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](7,"div",18),r["\u0275\u0275elementStart"](8,"div",19),r["\u0275\u0275element"](9,"div",20),r["\u0275\u0275elementStart"](10,"div",21),r["\u0275\u0275elementStart"](11,"button",22),r["\u0275\u0275elementStart"](12,"mat-icon",23),r["\u0275\u0275listener"]("click",(function(){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"](2).goToPreviousMonth()})),r["\u0275\u0275text"](13,"keyboard_arrow_left"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](14,"div",24),r["\u0275\u0275elementStart"](15,"span",25),r["\u0275\u0275text"](16,"For : "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](17,"div",26),r["\u0275\u0275elementStart"](18,"mat-form-field",27),r["\u0275\u0275element"](19,"input",28),r["\u0275\u0275element"](20,"mat-datepicker-toggle",29),r["\u0275\u0275elementStart"](21,"mat-datepicker",30,31),r["\u0275\u0275listener"]("yearSelected",(function(t){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"](2).selectYear(t)}))("monthSelected",(function(t){r["\u0275\u0275restoreView"](e);const n=r["\u0275\u0275reference"](22);return r["\u0275\u0275nextContext"](2).selectMonth(t,n)})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](23,"div",32),r["\u0275\u0275elementStart"](24,"button",33),r["\u0275\u0275elementStart"](25,"mat-icon",23),r["\u0275\u0275listener"]("click",(function(){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"](2).goToNextMonth()})),r["\u0275\u0275text"](26,"keyboard_arrow_right"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](27,"div",34),r["\u0275\u0275elementStart"](28,"mat-form-field",151),r["\u0275\u0275elementStart"](29,"span",36),r["\u0275\u0275elementStart"](30,"mat-icon",37),r["\u0275\u0275text"](31,"search"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](32,"input",178),r["\u0275\u0275listener"]("keyup",(function(t){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"](2).searchLeaveText(t.target.value)})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](33,"mat-icon",153),r["\u0275\u0275template"](34,Be,3,0,"button",179),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](35,"div"),r["\u0275\u0275template"](36,ze,9,0,"div",66),r["\u0275\u0275template"](37,$e,18,11,"div",121),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&e){const e=r["\u0275\u0275reference"](22),t=r["\u0275\u0275nextContext"](2);r["\u0275\u0275advance"](6),r["\u0275\u0275textInterpolate1"](" ",t.leaveDetailData?t.leaveDetailData.length:0," "),r["\u0275\u0275advance"](13),r["\u0275\u0275property"]("matDatepicker",e)("formControl",t.monthYearDate),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("for",e),r["\u0275\u0275advance"](14),r["\u0275\u0275property"]("ngIf",t.isClearVisible),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngIf",t.leaveDetailData.length>0),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngForOf",t.leaveDetailData)}}function Ge(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div",193),r["\u0275\u0275text"](1,"Cost Centre"),r["\u0275\u0275elementEnd"]())}function Xe(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",193),r["\u0275\u0275text"](1),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](3);r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate"](e.tsProperties.ts_ui_text)}}function Je(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div"),r["\u0275\u0275elementStart"](1,"div",134),r["\u0275\u0275elementStart"](2,"div",122),r["\u0275\u0275elementStart"](3,"div",135),r["\u0275\u0275elementStart"](4,"div",78),r["\u0275\u0275elementStart"](5,"div",79),r["\u0275\u0275elementStart"](6,"div",136),r["\u0275\u0275element"](7,"mat-checkbox"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](8,"div",137),r["\u0275\u0275elementStart"](9,"ngx-content-loading",138),r["\u0275\u0275namespaceSVG"](),r["\u0275\u0275element"](10,"g",139),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275namespaceHTML"](),r["\u0275\u0275elementStart"](11,"div",140),r["\u0275\u0275elementStart"](12,"ngx-content-loading",138),r["\u0275\u0275namespaceSVG"](),r["\u0275\u0275element"](13,"g",139),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275namespaceHTML"](),r["\u0275\u0275elementStart"](14,"div",141),r["\u0275\u0275elementStart"](15,"ngx-content-loading",138),r["\u0275\u0275namespaceSVG"](),r["\u0275\u0275element"](16,"g",139),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275namespaceHTML"](),r["\u0275\u0275elementStart"](17,"div",142),r["\u0275\u0275elementStart"](18,"ngx-content-loading",138),r["\u0275\u0275namespaceSVG"](),r["\u0275\u0275element"](19,"g",139),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275namespaceHTML"](),r["\u0275\u0275elementStart"](20,"div",143),r["\u0275\u0275elementStart"](21,"ngx-content-loading",138),r["\u0275\u0275namespaceSVG"](),r["\u0275\u0275element"](22,"g",139),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275namespaceHTML"](),r["\u0275\u0275elementStart"](23,"div",80),r["\u0275\u0275elementStart"](24,"button",144),r["\u0275\u0275elementStart"](25,"mat-icon",111),r["\u0275\u0275text"](26,"done"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](27,"button",145),r["\u0275\u0275elementStart"](28,"mat-icon",111),r["\u0275\u0275text"](29,"close"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e&&(r["\u0275\u0275advance"](9),r["\u0275\u0275property"]("speed","1500ms")("width",1e3)("height",100)("primaryColor","#ebebeb")("secondaryColor","#f7f7f7"),r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("speed","1500ms")("width",1e3)("height",100)("primaryColor","#ebebeb")("secondaryColor","#f7f7f7"),r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("speed","1500ms")("width",1e3)("height",100)("primaryColor","#ebebeb")("secondaryColor","#f7f7f7"),r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("speed","1500ms")("width",1e3)("height",100)("primaryColor","#ebebeb")("secondaryColor","#f7f7f7"),r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("speed","1500ms")("width",1e3)("height",100)("primaryColor","#ebebeb")("secondaryColor","#f7f7f7"))}function Qe(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div"),r["\u0275\u0275elementStart"](1,"div",68),r["\u0275\u0275elementStart"](2,"div",191),r["\u0275\u0275text"](3,"Name"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](4,"div",155),r["\u0275\u0275text"](5,"Leave Type"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](6,"div",156),r["\u0275\u0275text"](7,"Leave Dates"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](8,Ge,2,0,"div",192),r["\u0275\u0275template"](9,Xe,2,1,"div",192),r["\u0275\u0275elementStart"](10,"div",156),r["\u0275\u0275text"](11,"Location"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](12,Je,30,25,"div",67),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275advance"](8),r["\u0275\u0275property"]("ngIf",0==e.tsProperties.ts_ui_change_and_download_icon),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",1==e.tsProperties.ts_ui_change_and_download_icon),r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("ngForOf",e.loadArr)}}function Ke(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div"),r["\u0275\u0275elementStart"](1,"div"),r["\u0275\u0275elementStart"](2,"h4",146),r["\u0275\u0275text"](3),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](4,"div",147),r["\u0275\u0275element"](5,"img",148),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275advance"](3),r["\u0275\u0275textInterpolate1"](" No Team Members Booked Leave On This Month ",e.getMonthName()," ! ")}}function qe(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",11),r["\u0275\u0275template"](1,He,38,7,"div",12),r["\u0275\u0275template"](2,Qe,13,3,"div",12),r["\u0275\u0275template"](3,Ke,6,1,"div",12),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",!e.detailComponentActivated&&!e.submissionComponentActivated),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",0==e.leaveDetailData.length&&!e.noData),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",e.noData)}}const Ze={parse:{dateInput:"MMMM YYYY"},display:{dateInput:"MMMM YYYY",monthYearLabel:"MMMM YYYY",dateA11yLabel:"LL",monthYearA11yLabel:"MMMM YYYY"}};let et=(()=>{class e{constructor(e,t,n,a,i,o,r,l,c,p,g){this.tsPrimaryService=e,this.utilityService=t,this.dialog=n,this.loginService=a,this.route=i,this.router=o,this.myTeamService=r,this.cfr=l,this.wfhService=c,this.errorService=p,this.excelService=g,this._onDestroy=new h.b,this.homeViewActivated=!1,this.directReporteesViewActivated=!1,this.miscellaneousViewActivated=!1,this.costCenterViewActivated=!1,this.detailComponentActivated=!1,this.submissionComponentActivated=!1,this.selectAllActivated=!1,this.noData=!1,this.notifyingActivated=!1,this.canPreviousMonthBeFilled=!0,this.canNextMonthBeFilled=!0,this.isCurrentUserAdmin=!1,this.isAdminViewActivated=!1,this.isSingleClick=!1,this.associateData=[],this.initialList=[],this.loadArr=[1,1,1,1,1],this.allCostCenters=[],this.costCenterWiseAssociateData=[],this.initialCCList=[],this.currentUserGroupDayQuota=[],this.currentUserMetaSubm=[],this.summaryOfTheYear=[],this.openDetails={},this.currentView={},this.currentUser={},this.adminBasedUser={},this.minDate=m(),this.maxDate=m(),this.monthYearDate=new s.j(m()),this.selectedToggle="ts",this.monthEndDateVal=31,this.monthEndDate=m().date(this.monthEndDateVal).hour(15).minute(0).second(0).millisecond(0),this.formattedMonthYearDate="",this.formattedMonthYearDateEnd="",this.leaveDetailData=[],this.needToShowRemoteWorkx=!1,this.index=0,this.operation="",this.isLeaveDetAllowedInMyTeams=!1,this.cc_config=0,this.expandAllOption=0,this.btnDisplayText="Expand All",this.expandAllData=[],this.showShiftSchedule=!1,this.scheduleMasterData=[],this.getMyTeamsWfhData=()=>{this.wfhService.getMyTeamsWfhData(this.currentUserOid,this.monthYearDate.value.month()+1,this.monthYearDate.value.year()).subscribe(e=>{d.map(e,e=>{e.exitVisibility=!0}),this.myTeamWfhData=e},e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server Error while retrieving Myteams WFH data",e&&e.params?e.params:e&&e.error?e.error.params:{})})},this.getMonthName=()=>`${m().month(this.monthYearDate.value.month()).format("MMMM")} - ${this.monthYearDate.value.year()}`,this.comeToOffice=e=>{this.wfhService.comeToOffice(this.myTeamWfhData[e]).subscribe(t=>(t&&(this.myTeamWfhData[e].status_id=7,this.myTeamWfhData[e].status_name="Cancelled by Approver",this.myTeamWfhData[e].end_date=m().format("DD-MMM-YY"),this.myTeamWfhData[e].exitVisibility=!1),t),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Server Error while changing the Remote WorkX enddate",e&&e.params?e.params:e&&e.error?e.error.params:{})})},this.openWfhOverview=e=>{this.dialog.open(u.a,{height:"95%",width:"80%",data:e})},this.openComeToOfficeDialog=(e,t)=>{this.dialog.open(_,{height:"35%",width:"50%",data:e}).afterClosed().subscribe(e=>{"success"==e&&(this.myTeamWfhData[t].status_id=7,this.myTeamWfhData[t].status_name="Cancelled by Approver",this.myTeamWfhData[t].end_date=m().format("DD-MMM-YY"),this.myTeamWfhData[t].exitVisibility=!1)})},this.openCommentsSideNav=e=>{}}ngOnInit(){this.profile=this.loginService.getProfile().profile,sessionStorage.getItem("ssMonthYearDate")&&this.monthYearDate.setValue(m(sessionStorage.getItem("ssMonthYearDate"))),this.route.params.subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){e.label?this.showTsSubmissionDetailsOfAssoc():(sessionStorage.removeItem("ssMonthYearDate"),this.initDetails(),this.homeViewActivated=!0,this.costCenterViewActivated=!1,this.miscellaneousViewActivated=!1,this.directReporteesViewActivated=!1,this.submissionComponentActivated=!1)}))),this.getTimesheetScheduleAndSession(this.currentUser.oid),this.checkIfCurrentUserAdmin(),this.getMyTeamsWfhData()}initDetails(){this.homeViewActivated=!0,this.currentUser=this.loginService.getProfile().profile,this.adminBasedUser=sessionStorage.getItem("myTeamAdmin")?JSON.parse(sessionStorage.getItem("myTeamAdmin")):{},this.adminBasedUser.oid?(this.currentUserOid=this.adminBasedUser.oid,this.isAdminViewActivated=!0):(this.currentUserOid=this.currentUser.oid,this.isAdminViewActivated=!1),this.getTimesheetProperties()}checkIfCurrentUserAdmin(){this.tsPrimaryService.checkIfTsAdmin(this.currentUser.oid).pipe(Object(p.a)(this._onDestroy)).subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){this.isCurrentUserAdmin="S"==e.messType&&"Admin"==e.messText})),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while Checking Current User is an Admin",e&&e.params?e.params:e&&e.error?e.error.params:{})})}initializeMonthData(){this.calculateFormattedDates(this.monthYearDate.value),this.associateData=[],this.initialList=[],this.initialCCList=[],this.noData=!1,this.expandAllData=[],this.btnDisplayText="Expand All",this.myTeamService.getMyTeamDetails(this.currentUserOid,this.formattedMonthYearDate,this.formattedMonthYearDateEnd,"Parent",!1).pipe(Object(p.a)(this._onDestroy)).subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){if("S"==e.messType&&e.data.length>0){this.associateData=e.data,this.associateData.forEach((e,t)=>{1==e.only_approver_admin&&this.associateData.unshift(this.associateData.splice(t,1)[0])}),this.noData=!1;for(let e=0;e<this.associateData.length;e++){if(this.associateData[e].level=0,this.associateData[e].showLevel=!0,this.associateData[e].hierarchyActivated=!1,this.associateData[e].hierarchyLoading=!1,this.associateData[e].checked=!1,this.associateData[e].costCenterDetails=[],null!=this.associateData[e].costcenter){let t=this.associateData[e].costcenter.split("~");for(let n=0;n<t.length;n++){let a=t[n].split("$");a[3]=Math.round(100*parseFloat(a[3]))/100,a[3]=a[3].toString(),this.associateData[e].costCenterDetails.push({cc_code:a[0],cc_name:"NULL"==a[1]?null:a[1],cc_location:a[2],cc_hours:a[3]})}}if(this.associateData[e].total_hours){this.associateData[e].total_hours=Math.round(100*this.associateData[e].total_hours)/100,this.associateData[e].details=this.associateData[e].total_hours+" hours";let t=[];t.push(this.associateData[e].leave_details_employee&&this.associateData[e].leave_details_employee.length>0?this.associateData[e].leave_details_employee:"");let n=t.length>0&&t[0].length>1?", "+t[0].slice(0,-1):"";this.associateData[e].details+=n}else{this.associateData[e].details="";let t=[];t.push(this.associateData[e].leave_details_employee&&this.associateData[e].leave_details_employee.length>0?this.associateData[e].leave_details_employee:"");let n=t.length>0&&t[0].length>1?t[0].slice(0,-1):"";this.associateData[e].details+=n}}this.initialList=this.associateData}else this.utilityService.showMessage(e.messText,"Dismiss",3e3),this.noData=!0})),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while getting Team Details",e&&e.params?e.params:e&&e.error?e.error.params:{}),this.noData=!0})}getLocalDate(e){return"-"==e?e:m(e,"DD - MMM - YYYY HH : mm : ss").utc(e).local().format("DD - MMM - YYYY HH : mm : ss")}selectMonth(e,t){const n=this.monthYearDate.value;n.month()!=e.month()&&(n.month(e.month()).year(e.year()).date(1),this.changeMonthYear(n)),t.close()}selectYear(e){const t=this.monthYearDate.value;t.year()!=e.year()&&(t.year(e.year()),this.changeMonthYear(t))}changeMonthYear(e){this.monthYearDate.setValue(e),this.initializeMonthData()}calculateFormattedDates(e){"END"!=this.monthEndDateVal?(this.monthEndDate=m(e).date(this.monthEndDateVal).hour(15).minute(0).second(0).millisecond(0),this.formattedMonthYearDate=m(e).subtract(1,"months").date(this.monthEndDateVal+1).hour(15).minute(0).second(0).millisecond(0).format("YYYY-MM-DD"),this.formattedMonthYearDateEnd=m(e).date(this.monthEndDateVal).hour(15).minute(0).second(0).millisecond(0).format("YYYY-MM-DD")):(this.monthEndDate=m(e).date(m().daysInMonth()).hour(15).minute(0).second(0).millisecond(0),this.formattedMonthYearDate=m(e).hour(15).minute(0).second(0).millisecond(0).format("YYYY-MM-DD"),this.formattedMonthYearDateEnd=m(e).date(m().daysInMonth()).hour(15).minute(0).second(0).millisecond(0).format("YYYY-MM-DD"))}goToPreviousMonth(){this.changeMonthYear(this.monthYearDate.value.subtract(1,"M")),this.getMyTeamsWfhData()}goToNextMonth(){this.changeMonthYear(this.monthYearDate.value.add(1,"M")),this.getMyTeamsWfhData()}getTimesheetProperties(){this.tsPrimaryService.getTimesheetProperties(this.currentUser.aid).pipe(Object(p.a)(this._onDestroy)).subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){"S"==e.messType&&e.data.length>0?(this.tsProperties=e.data[0],this.needToShowRemoteWorkx=1==this.tsProperties.need_to_show_remote_works,this.cc_config=this.tsProperties.ts_ui_change_and_download_icon,this.expandAllOption=this.tsProperties.show_expand_all_in_myteams?this.tsProperties.show_expand_all_in_myteams:0,this.monthEndDateVal="END"==this.tsProperties.month_end_date?"END":parseInt(this.tsProperties.month_end_date),this.isLeaveDetAllowedInMyTeams=1==this.tsProperties.is_leave_det_allowed_in_my_teams,this.showShiftSchedule=1==this.tsProperties.show_upload_schedule,this.initializeMonthData()):this.utilityService.showMessage(e.messText,"Dismiss",3e3)})),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while retrieving the Timesheet Properties",e&&e.params?e.params:e&&e.error?e.error.params:{})})}homeView(){var e;this.homeViewActivated=!0,this.directReporteesViewActivated=!1,this.miscellaneousViewActivated=!1,this.costCenterViewActivated=!1,this.noData=!(this.initialList.length>0);for(let t=0;t<this.initialList.length;t++)1==this.initialList[t].deactivateInHomeView&&"Unsubmitted"!=this.initialList[t].status&&this.initialList.splice(t,1),0==(null===(e=this.initialList[t])||void 0===e?void 0:e.costCenterDetails.length)&&"Unsubmitted"!=this.initialList[t].status&&this.initialList.splice(t,1);this.associateData=this.initialList;for(let t=0;t<this.associateData.length;t++)1==this.associateData[t].deactivateInHomeView&&"Unsubmitted"!=this.associateData[t].status&&this.associateData.splice(t,1)}viewByDirectReportees(e){this.currentView=e,this.homeViewActivated=!1,this.directReporteesViewActivated=!0,this.miscellaneousViewActivated=!1,this.costCenterViewActivated=!1,this.noData=!1,this.associateData=d.where(this.initialList,{reportee_type:"Direct Reportee",showLevel:!0}),0==this.associateData.length&&(this.noData=!0)}viewByMiscellaneous(e){this.currentView=e,this.homeViewActivated=!1,this.directReporteesViewActivated=!1,this.miscellaneousViewActivated=!0,this.costCenterViewActivated=!1,this.noData=!1,this.associateData=d.where(this.initialList,{reportee_type:"Cost Center Reportee",showLevel:!0}),0==this.associateData.length&&(this.noData=!0)}viewByCostCenter(e){this.currentView=e,this.homeViewActivated=!1,this.directReporteesViewActivated=!1,this.miscellaneousViewActivated=!1,this.costCenterViewActivated=!0,this.noData=!1,this.allCostCenters=[],this.costCenterWiseAssociateData=[];let t=JSON.parse(JSON.stringify(this.initialList));t=t.filter(e=>!0===e.showLevel);for(let a of t)if(1==a.costCenterDetails.length)a.costcenter=a.costCenterDetails[0].cc_code.replace(/\s+/g,""),a.cc_description=a.costCenterDetails[0].cc_name;else if(a.costCenterDetails.length>1){a.costcenter=a.costCenterDetails[0].cc_code.replace(/\s+/g,""),a.cc_description=a.costCenterDetails[0].cc_name,a.total_hours=parseInt(a.costCenterDetails[0].cc_hours);let e=parseInt(a.costCenterDetails[0].cc_hours)>0?parseInt(a.costCenterDetails[0].cc_hours)+" hours":"0 hours",n=[];n.push(a.leave_details_employee&&a.leave_details_employee.length>0?a.leave_details_employee:""),e+=n.length>0&&n[0].length>1?parseInt(a.costCenterDetails[0].cc_hours)>0?", "+n[0].slice(0,-1):n[0].slice(0,-1):"",a.details=e;for(let i=1;i<a.costCenterDetails.length;i++){let e="";e+=parseInt(a.costCenterDetails[i].cc_hours)>0?parseInt(a.costCenterDetails[i].cc_hours)+" hours":"0 hours",e+=n.length>i&&n[i].length>1?", "+n[i].slice(0,-1):"",t.push({cc_description:a.costCenterDetails[i].cc_name,checked:a.checked,cl_count:a.cl_count,comments:a.comments,costCenterDetails:[],costcenter:a.costCenterDetails[i].cc_code.replace(/\s+/g,""),date:a.date,details:e,fh_count:a.fh_count,group_code:a.group_code,group_description:a.group_description,h_count:a.h_count,hierarchyActivated:a.hierarchyActivated,hierarchyLoading:a.hierarchyLoading,is_org_head:a.is_org_head,leave_count:a.leave_count,level:a.level,location:a.location,month_sub:a.month_sub,name:a.name,oh_count:a.oh_count,oid:a.oid,pl_count:a.pl_count,reportee_type:a.reportee_type,showLevel:a.showLevel,status:a.status,total_hours:parseInt(a.costCenterDetails[i].cc_hours),wo_count:a.wo_count,year:a.year,deactivateInHomeView:!0,hc_count:a.hc_count,hp_count:a.hp_count})}}let n=d.uniq(d.pluck(t,"costcenter"));d.each(n,e=>{let n=d.where(t,{costcenter:e}),a=0;d.each(n,e=>{a+=e.total_hours}),this.allCostCenters.push({costcenter:e,cc_name:n.length>0?n[0].cc_description:"-",cc_total_hours:a,checked:!1})}),this.costCenterWiseAssociateData=d.groupBy(d.where(t,{showLevel:!0}),e=>e.costcenter),this.initialCCList=this.costCenterWiseAssociateData,this.noData=!(this.initialList.length>0)}notifyAllSelected(){let e=d.where(this.associateData,{checked:!0,status:"Unsubmitted"});e.length>0?(this.notifyingActivated=!0,this.myTeamService.notifyUnsubmittedAssociates(e,null).pipe(Object(p.a)(this._onDestroy)).subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){"S"==e.messType?(this.notifyingActivated=!1,this.utilityService.showMessage(e.messText,"Dismiss",3e3)):this.utilityService.showMessage(e.messText,"Dismiss",3e3)})),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error in Notifying Unsubmiited Associates",e&&e.params?e.params:e&&e.error?e.error.params:{})})):this.utilityService.showMessage("Please select associates who have not yet submitted","Dismiss",3e3)}applySearch(e){var t=new RegExp(e,"i");if(this.costCenterViewActivated)if(e){this.initialCCList=d.groupBy(this.associateData,e=>e.costcenter);let e=!1,n=!1;d.each(this.allCostCenters,a=>{this.costCenterWiseAssociateData[a.costcenter]=d.filter(this.initialCCList[a.costcenter],e=>e.name&&t.test(e.name)||e.costcenter&&t.test(e.costcenter)||e.cc_description&&t.test(e.cc_description)||e.status&&t.test(e.status)),0==this.costCenterWiseAssociateData[a.costcenter].length?n=!0:e=!0}),this.noData=!e}else this.costCenterWiseAssociateData=d.groupBy(this.associateData,e=>e.costcenter),this.initialCCList=this.costCenterWiseAssociateData,this.noData=!1;else this.initialList.length>0&&(e?(this.associateData=d.filter(this.initialList,e=>e.name&&t.test(e.name)||e.costcenter&&t.test(e.costcenter)||e.cc_description&&t.test(e.cc_description)||e.status&&t.test(e.status)),0==this.associateData.length?this.noData=!0:(d.each(this.associateData,e=>{e.showLevel=!0}),this.noData=!1)):(this.associateData=this.initialList,this.noData=!1))}cardClicked(e,t){let n=[e?this.costCenterWiseAssociateData[e][t]:this.associateData[t]];sessionStorage.setItem("myTeamSubAssocData",JSON.stringify(n)),sessionStorage.setItem("ssMonthYearDate",this.monthYearDate.value),this.submissionComponentActivated=!0,this.router.navigateByUrl("/main/timesheet/myteam/"+this.encodeURIComponent(n[0].name))}showTsSubmissionDetailsOfAssoc(){this.homeViewActivated=!1,this.costCenterViewActivated=!1,this.miscellaneousViewActivated=!1,this.directReporteesViewActivated=!1,this.submissionComponentActivated=!0;let e=sessionStorage.getItem("myTeamSubAssocData")?JSON.parse(sessionStorage.getItem("myTeamSubAssocData")):[],t=sessionStorage.getItem("ssMonthYearDate")?m(sessionStorage.getItem("ssMonthYearDate")):this.monthYearDate.value;e.length>0?(this.submissionView&&this.submissionView.clear(),Promise.all([n.e(4),n.e(29),n.e(34),n.e(40),n.e(171)]).then(n.bind(null,"9iwy")).then(n=>{const a=this.submissionView.createComponent(this.cfr.resolveComponentFactory(n.TsSubmissionLandingPageComponent));a.instance.submissionData={monthYearDateValue:t,associateData:e[0]},a.instance.close.subscribe(e=>{"close"==e.event&&this.closeDetailComponent()})})):this.utilityService.showMessage("Associate Details Invalid !","Dismiss",3e3)}encodeURIComponent(e){return encodeURIComponent(e).replace(/[!'()*]/g,(function(e){return"%"+e.charCodeAt(0).toString(16)}))}closeDetailComponent(){this.submissionView.clear(),this.submissionComponentActivated=!1,this.router.navigateByUrl("/main/timesheet/myteam")}showSummary(e,t){this.getAssocMetaSubm(e?this.costCenterWiseAssociateData[e][t]:this.associateData[t])}getAssocMetaSubm(e){this.tsPrimaryService.getAssocMetaSubm(e.oid,this.formattedMonthYearDateEnd).pipe(Object(p.a)(this._onDestroy)).subscribe(t=>Object(o.c)(this,void 0,void 0,(function*(){this.currentUserMetaSubm="S"==t.messType&&t.data.length>0?t.data:[],this.getAssocGroupDayQuota(e)})),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while retrieving the Timesheet Properties",e&&e.params?e.params:e&&e.error?e.error.params:{})})}getAssocGroupDayQuota(e){this.myTeamService.getTimesheetAndLeaveSummaryDetails(e.oid,this.formattedMonthYearDate,this.formattedMonthYearDateEnd).pipe(Object(p.a)(this._onDestroy)).subscribe(t=>Object(o.c)(this,void 0,void 0,(function*(){if("S"==t.messType&&t.data.length>0){let a={sidenavName:"Submission-Summary-Modal",summaryOfTheYear:t.hours,associateName:e.name?e.name:"",currentAssociateOId:e.oid,currentYear:m(this.monthYearDate.value).year(),leaveData:t.data};const{TsSubmissionSummaryModalComponent:i}=yield n.e(773).then(n.bind(null,"hHH2"));this.dialog.open(i,{height:"100%",width:"95%",position:{right:"0px"},data:{modalParams:a}})}else this.utilityService.showMessage(t.messText,"Dismiss",3e3)})),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while retrieving the Timesheet Properties",e&&e.params?e.params:e&&e.error?e.error.params:{})})}notifyIndividual(e,t){this.myTeamService.notifyUnsubmittedAssociates([e?this.costCenterWiseAssociateData[e][t]:this.associateData[t]],null).pipe(Object(p.a)(this._onDestroy)).subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){this.utilityService.showMessage(e.messText,"Dismiss",3e3)})),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error in Notifying Unsubmitted Associates",e&&e.params?e.params:e&&e.error?e.error.params:{})})}seeComment(e){}selectAll(){d.each(this.associateData,this.selectAllActivated?e=>{e.checked=!0}:e=>{e.checked=!1})}selectAllOfCostCenters(e){d.each(this.costCenterWiseAssociateData[e.costcenter],e.checked?e=>{e.checked=!0}:e=>{e.checked=!1})}selectIndividual(e){if(this.homeViewActivated){let e=[];d.each(this.associateData,t=>{e.push(t.checked)}),this.selectAllActivated=!d.contains(e,!1)}else for(let t=0;t<this.allCostCenters.length;t++){let e=[];for(let n=0;n<this.costCenterWiseAssociateData[this.allCostCenters[t].costcenter].length;n++)e.push(this.costCenterWiseAssociateData[this.allCostCenters[t].costcenter][n].checked);this.allCostCenters[t].checked=!d.contains(e,!1)}}getLength(e){return e.length}bringReportees(e,t){if(!this.associateData[e].hierarchyLoading&&(this.associateData[e].hierarchyActivated=!this.associateData[e].hierarchyActivated,1==this.associateData[e].is_org_head)){let n=this.associateData[e].level;if(this.associateData[e].hierarchyActivated)if(this.associateData[e+1]&&this.associateData[e+1].level==n+1)for(let t=e+1;t<this.associateData.length&&(this.associateData[t].level==n+1&&(this.associateData[t].showLevel=!0),this.associateData[t].level!=n);t++);else this.associateData[e].hierarchyLoading=!0,this.myTeamService.getMyTeamDetails(this.associateData[e].oid,this.formattedMonthYearDate,this.formattedMonthYearDateEnd,"Parent",!1).pipe(Object(p.a)(this._onDestroy)).subscribe(a=>Object(o.c)(this,void 0,void 0,(function*(){if(this.associateData[e].hierarchyLoading=!1,"S"==a.messType&&a.data.length>0){let i=a.data;i.forEach((e,t)=>{1==e.only_approver_admin&&i.unshift(i.splice(t,1)[0])});for(let a=0;a<i.length;a++){if(i[a].level=n+1,i[a].showLevel=!0,i[a].hierarchyActivated=!1,i[a].hierarchyLoading=!1,i[a].checked=!1,i[a].costCenterDetails=[],null!=i[a].costcenter){let e=i[a].costcenter.split("~");for(let t=0;t<e.length;t++){let n=e[t].split("$");i[a].costCenterDetails.push({cc_code:n[0],cc_name:"NULL"==n[1]?null:n[1],cc_location:n[2],cc_hours:n[3]})}}if(i[a].total_hours){i[a].details=i[a].total_hours+" hours";let e=[];e.push((i[a].leave_details_employee&&i[a].leave_details_employee.length)>0?i[a].leave_details_employee:"");let t=e.length>0&&e[0].length>1?", "+e[0].slice(0,-1):"";i[a].details+=t}else{i[a].details="";let e=[];e.push((i[a].leave_details_employee&&i[a].leave_details_employee.length)>0?i[a].leave_details_employee:"");let t=e.length>0&&e[0].length>1?e[0].slice(0,-1):"";i[a].details+=t}t?this.associateData.splice(e+a+1,0,i[a]):this.expandAllData.push(i[a])}if(!t)for(let e=0;e<this.associateData.length;e++){for(let t=0;t<this.expandAllData.length;t++)this.associateData[e].oid==this.expandAllData[t].dmOid&&0==d.filter(this.associateData,{oid:this.expandAllData[t].oid}).length&&(this.associateData.splice(e+1,0,this.expandAllData[t]),this.associateData[e].hierarchyLoading=!1);this.associateData[e].hierarchyLoading=!1}this.initialList=this.associateData}else this.associateData[e].hierarchyLoading=!1,this.associateData[e].hierarchyActivated=!1,this.utilityService.showMessage(a.messText,"Dismiss",3e3)})),t=>{this.associateData[e].hierarchyActivated=!1,this.associateData[e].hierarchyLoading=!1,this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Error while getting Team Details",t&&t.params?t.params:t&&t.error?t.error.params:{})});else{if(t)for(let t=e+1;t<this.associateData.length&&this.associateData[t].level>=n+1;t++)this.associateData[t].showLevel=!1,this.associateData[t].hierarchyActivated=!1;if(!t)for(let e=0;e<this.associateData.length;e++)0!=this.associateData[e].level&&(this.associateData[e].showLevel=!1,this.associateData[e].hierarchyActivated=!1,this.associateData[e].hierarchyLoading=!1)}}}getIndent(e){return 1==e?"indent-level-1":2==e?"indent-level-2":3==e?"indent-level-3":4==e?"indent-level-4":5==e?"indent-level-5":""}openAdminSidenav(){this.isSingleClick=!0,setTimeout(()=>Object(o.c)(this,void 0,void 0,(function*(){if(this.isSingleClick){let e={currentView:"My Team"};const{TsAdminViewModalComponent:t}=yield Promise.all([n.e(4),n.e(0),n.e(765)]).then(n.bind(null,"Fh+A"));this.dialog.open(t,{height:"100%",width:"50%",position:{right:"0px"},data:{modalParams:e}}).afterClosed().subscribe(e=>{"Submit"==e.event&&this.adminBasedUser.oid!=e.data.adminViewResponse.oid&&(this.adminBasedUser={},this.adminBasedUser=e.data.adminViewResponse,this.currentUserOid=this.adminBasedUser.oid,this.isAdminViewActivated=!0,sessionStorage.setItem("myTeamAdmin",JSON.stringify(this.adminBasedUser)),this.initializeMonthData())})}})),250)}closeAdminSideNav(){this.isSingleClick=!1,this.currentUserOid!=this.currentUser.oid&&(this.adminBasedUser={},this.currentUserOid=this.currentUser.oid,this.initializeMonthData(),sessionStorage.removeItem("myTeamAdmin"),this.isAdminViewActivated=!1)}statusBasedColorSelector(e){if(this.myTeamWfhData)switch(e){case 1:return"#faab23";case 2:return"#da7cf7";case 3:return"#209606";case 4:return"#c92020";case 5:return"#209606";case 6:return"#e81022";case 7:return"#209606";case 8:case 9:return"#c92020";case 10:return"#eb6315"}}selectToggle(e){this.selectedToggle=e.value,"wfh"==this.selectedToggle&&this.getMyTeamsWfhData(),"lbDetails"==this.selectedToggle&&this.getLeaveDetails()}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}getLeaveDetails(){this.myTeamService.getLeaveDetails(this.currentUser.aid,this.currentUserOid,this.formattedMonthYearDate,this.formattedMonthYearDateEnd).pipe(Object(p.a)(this._onDestroy)).subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){"S"==e.messType&&e.data&&e.data.length>0?(this.leaveDetailData=e.data,this.initialList=this.leaveDetailData,this.noData=!1):(this.utilityService.showMessage("No Data Found","Dismiss",3e3),this.noData=!0)})))}searchLeaveText(e){var t=new RegExp(e,"i");this.initialList.length>0&&(e?(this.leaveDetailData=d.filter(this.initialList,e=>e.employee_name&&t.test(e.employee_name)||e.leaveDescription&&t.test(e.leaveDescription)||e.leave_type&&t.test(e.leave_type)||e.leaveStatus&&t.test(e.leaveStatus)),0==this.leaveDetailData.length?this.noData=!0:(d.each(this.associateData,e=>{e.showLevel=!0}),this.noData=!1)):(this.leaveDetailData=this.initialList,this.noData=!1))}makeAsAdminApprover(e,t,n){try{"grant"==e?(this.operation=e,this.associateData.unshift(this.associateData.splice(n,1)[0]),this.index=0):(this.index=n,this.operation=e),this.myTeamService.performAdminApprovalAccess(t.oid,t.name,e).subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){e.data&&this.utilityService.showMessage(e.data,"Dismiss",3e3)})),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Server Error while allowing the associate as an Admin Approver ",e&&e.params?e.params:e&&e.error?e.error.params:{})})}catch(a){console.log(a)}}openEmployeeConfig(e){return Object(o.c)(this,void 0,void 0,(function*(){const{TsEmployeeConfigComponent:t}=yield n.e(768).then(n.bind(null,"nYau"));this.dialog.open(t,{height:"100%",width:"55%",position:{right:"0px"},data:{modalParams:e}}).afterClosed().subscribe(e=>{})}))}expandAllMyTeams(){for(let e=this.associateData.length-1;e>=0;e--)1==this.associateData[e].is_org_head&&("Expand All"==this.btnDisplayText?!this.associateData[e].hierarchyActivated:this.associateData[e].hierarchyActivated)&&this.bringReportees(e,!1);this.btnDisplayText=d.filter(this.associateData,{hierarchyActivated:!0}).length>0?"Collapse All":"Expand All"}downloadShiftScheduleTemplate(){this.excelService.exportJsonToExcelWithMultipleSheets([{data:[{associate_id:"",schedule_id:""}],sheetName:"Upload Template"},{data:this.scheduleMasterData,sheetName:"Master Data"}],"Schedule Template")}uploadShiftScheduleTemplate(){this.fileInput.nativeElement.click(),this.fileInput.nativeElement.value=null}onFileSelected(e){const t=e.target.files[0];t&&(this.isExcelFile(t)?this.parseExcelFile(t):this.utilityService.showMessage("Invalid file selected. Please select an Excel file.","Dismiss",3e3))}isExcelFile(e){const t=e.name.toLowerCase();return[".xlsx",".xls"].some(e=>t.endsWith(e))}parseExcelFile(e){const t=new FileReader;let n=[];t.onload=e=>{const t=w.read(e.target.result,{type:"binary"}),a=w.utils.sheet_to_json(t.Sheets[t.SheetNames[0]],{raw:!0});n=a,this.myTeamService.uploadShiftScheduleTemplate(n).pipe(Object(p.a)(this._onDestroy)).subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){e&&e.data&&"S"==e.messType?(this.utilityService.showMessage(e.messText,"Dismiss",3e3),this.excelService.exportAsExcelFile(e.data,"Upload Status")):this.utilityService.showMessage(e.messText,"Dismiss",3e3)})),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Server Error while allowing the associate as an Admin Approver ",e&&e.params?e.params:e&&e.error?e.error.params:{})})},t.readAsBinaryString(e)}getTimesheetScheduleAndSession(e){this.myTeamService.getTimesheetScheduleAndSession(e).pipe(Object(p.a)(this._onDestroy)).subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){this.scheduleMasterData=e&&"S"==e.messType&&e.schedule.length>0?e.schedule:[]})),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Server Error while allowing the associate as an Admin Approver ",e&&e.params?e.params:e&&e.error?e.error.params:{})})}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](D.a),r["\u0275\u0275directiveInject"](E.a),r["\u0275\u0275directiveInject"](g.b),r["\u0275\u0275directiveInject"](M.a),r["\u0275\u0275directiveInject"](i.a),r["\u0275\u0275directiveInject"](i.g),r["\u0275\u0275directiveInject"](O.a),r["\u0275\u0275directiveInject"](r.ComponentFactoryResolver),r["\u0275\u0275directiveInject"](b.a),r["\u0275\u0275directiveInject"](C.a),r["\u0275\u0275directiveInject"](A.a))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["app-ts-myteam-landing-page"]],viewQuery:function(e,t){if(1&e&&(r["\u0275\u0275viewQuery"](N,!0,r.ViewContainerRef),r["\u0275\u0275viewQuery"](U,!0)),2&e){let e;r["\u0275\u0275queryRefresh"](e=r["\u0275\u0275loadQuery"]())&&(t.submissionView=e.first),r["\u0275\u0275queryRefresh"](e=r["\u0275\u0275loadQuery"]())&&(t.fileInput=e.first)}},features:[r["\u0275\u0275ProvidersFeature"]([{provide:c.c,useClass:l.c,deps:[c.f,l.a]},{provide:c.e,useValue:Ze}])],decls:4,vars:4,consts:[["class","row pt-2",4,"ngIf"],["class","container-fluid my-team-styles pl-0 pr-0",4,"ngIf"],[1,"row","pt-2"],[1,"col-6"],["name","fontStyle","aria-label","Font Style",3,"value","change"],["group","matButtonToggleGroup"],["value","ts",2,"padding","2px 10px !important",3,"ngClass"],["value","wfh","style","padding: 2px 30px !important",3,"ngClass",4,"ngIf"],["value","lbDetails","style","padding: 2px 30px !important",3,"ngClass",4,"ngIf"],["value","wfh",2,"padding","2px 30px !important",3,"ngClass"],["value","lbDetails",2,"padding","2px 30px !important",3,"ngClass"],[1,"container-fluid","my-team-styles","pl-0","pr-0"],[4,"ngIf"],["submissionView",""],[1,"row","pt-2","pb-2"],[1,"col-1","d-flex","pt-2","pl-0","pr-0"],[1,"mediumSubtleText","pl-4"],[1,"heading","pl-2"],[1,"col-3"],[1,"row","pl-0","pr-0"],[1,"col-1","p-0"],[1,"col-1","pl-0","pr-0","mr-2"],["mat-icon-button","","matTooltip","Previous month"],[1,"iconButton",3,"click"],[1,"col-2","pr-0","pt-2"],["for","monthYearDp",1,"title"],[1,"col-6","pr-0","pl-0","pt-2"],[2,"width","100% !important"],["id","monthYearDpInput","matInput","","readonly","",1,"ib13",3,"matDatepicker","formControl"],["matSuffix","","matTooltip","Select from Calendar",3,"for"],["disabled","false","startView","year","panelClass","example-month-picker",3,"yearSelected","monthSelected"],["monthYearDp",""],[1,"col-1","pl-0","ml-2"],["mat-icon-button","","matTooltip","Next month"],[1,"d-flex","col","col-5","search-bar"],["appearance","outline",1,"ml-auto"],["matPrefix",""],[2,"font-size","21px !important","color","#66615b !important"],["matInput","","type","search","name","search","placeholder","Search Associates",3,"keyup"],[1,"col-3","d-flex"],["mat-icon-button","","matTooltip","View By",2,"margin-left","9px",3,"ngClass"],[1,"iconButton",3,"matMenuTriggerFor"],["visibility","matMenu"],["mat-menu-item","",1,"drop-btn",3,"click"],["mat-menu-item","",1,"drop-btn",3,"matTooltip","click"],["mat-menu-item","","class","drop-btn",3,"click",4,"ngIf"],["type","file","accept",".xlsx, .xls",2,"display","none",3,"change"],["fileInput",""],["mat-icon-button","","matTooltip","Notify via KEBS-Bot",1,"view-button-inactive",2,"margin-left","9px"],["class","iconButton",3,"click",4,"ngIf"],["class","spinner-align","diameter","18",4,"ngIf"],["mat-icon-button","","style","margin-left:9px",3,"matTooltip","ngClass",4,"ngIf"],["style","margin-left:9px",4,"ngIf"],["mat-icon-button","","matTooltip","Back to Home View","class","view-button-inactive","style","margin-left:9px",4,"ngIf"],["class","heading my-auto pl-2",4,"ngIf"],["mat-raised-button","","style","background-color: #c92020 !important; color: white;",3,"opacity","disabled","click",4,"ngIf"],["diameter","18",1,"spinner-align"],["mat-icon-button","",2,"margin-left","9px",3,"matTooltip","ngClass"],[1,"iconButton",3,"dblclick","click"],[2,"margin-left","9px"],["content-type","template",3,"id","tooltip"],["approverTooltip",""],[1,"row","tooltip-text"],["mat-icon-button","","matTooltip","Back to Home View",1,"view-button-inactive",2,"margin-left","9px"],[1,"heading","my-auto","pl-2"],["mat-raised-button","",2,"background-color","#c92020 !important","color","white",3,"disabled","click"],["class","row pl-4 pt-2 pb-1",4,"ngIf"],[4,"ngFor","ngForOf"],[1,"row","pl-4","pt-2","pb-1"],[1,"col-1","pl-4","smallSubtleText"],[3,"ngModel","ngModelChange","change"],[1,"col-2","smallSubtleText"],[1,"col-2","smallSubtleText","pl-0"],["class","col-2 smallSubtleText pl-0",4,"ngIf"],["class","row","style","padding-bottom: 1px;",4,"ngIf"],[1,"row",2,"padding-bottom","1px"],[1,"col-12","pl-4","pr-4",3,"ngClass"],[1,"card","listcard",2,"border-left","3px solid #9a9a9a","padding","1px",3,"ngClass","ngStyle"],[1,"card-body",2,"padding","2px !important"],[1,"row","card-details","p-0"],[1,"col-1","d-flex"],[1,"pl-1",2,"margin-top","6px"],["class","arrow-button ml-2 my-auto","mat-icon-button","",3,"matTooltip",4,"ngIf"],["matTooltip","Loading Reportees","class","spinner-align","diameter","18",4,"ngIf"],[1,"col-2","pl-0","my-auto","name",3,"matTooltip","click"],["imgWidth","28px","imgHeight","28px",3,"id"],[1,"name","ml-3"],[3,"matTooltip",4,"ngIf"],[1,"col-2","details","my-auto","pl-0",3,"matTooltip","click"],[1,"col-2","details","costcenter","pt-1"],[1,"col-2","d-flex","my-auto"],[1,"status-circular",3,"ngClass","click"],[1,"location","ml-2"],[1,"col-2","date","pl-0","pl-4","pr-4","pt-1","my-auto","d-flex",2,"text-align","center"],[1,"pr-2",3,"click"],[1,"col-1","pl-0","d-flex"],["mat-icon-button","","matTooltip","Grant Approver Admin Access","class","icon-tray-button",3,"click",4,"ngIf"],["mat-icon-button","","matTooltip","Revoke Approver Admin Access","class","icon-tray-button",3,"click",4,"ngIf"],["mat-icon-button","","matTooltip","Summary","class","icon-tray-button",3,"click",4,"ngIf"],["mat-icon-button","","matTooltip","Notify via KEBS-Bot","class","icon-tray-button ml-auto",3,"click",4,"ngIf"],["mat-icon-button","","matTooltip","Employee Config","class","icon-tray-button ml-auto",3,"click",4,"ngIf"],["mat-icon-button","",1,"arrow-button","ml-2","my-auto",3,"matTooltip"],["matTooltip","Loading Reportees","diameter","18",1,"spinner-align"],[3,"matTooltip"],[1,"row","pb-1"],[1,"col-8","pl-0","pr-0"],[1,"row"],[1,"col-2","pl-0","pr-1",2,"font-weight","500 !important"],[1,"col-10","costcenter",2,"padding-left","30px",3,"matTooltip"],[1,"col-4","pl-0","pr-0","location",3,"matTooltip"],["mat-icon-button","","matTooltip","Grant Approver Admin Access",1,"icon-tray-button",3,"click"],[1,"smallCardIcon"],["mat-icon-button","","matTooltip","Revoke Approver Admin Access",1,"icon-tray-button",3,"click"],["mat-icon-button","","matTooltip","Summary",1,"icon-tray-button",3,"click"],["mat-icon-button","","matTooltip","Notify via KEBS-Bot",1,"icon-tray-button","ml-auto",3,"click"],["mat-icon-button","","matTooltip","Employee Config",1,"icon-tray-button","ml-auto",3,"click"],[1,"col-1","ml-1","smallSubtleText"],[1,"col-2","smallSubtleText","pl-0","ml-5"],[1,"row","pt-2","pb-2","pl-4"],[1,"col-12","pl-4"],[1,"ml-2","heading",2,"vertical-align","text-bottom"],["class","row","style","padding-bottom: 1px;",4,"ngFor","ngForOf"],[1,"col-12","pl-4","pr-4"],[1,"card","listcard",2,"border-left","3px solid #9a9a9a","padding","1px",3,"ngClass"],[1,"col-1"],[1,"pl-1"],[1,"col-2","pl-0","name",3,"matTooltip","click"],[1,"col-2","details","pt-1","pl-0",3,"click"],[1,"col-2","costcenter","pt-1","pl-0",3,"matTooltip","click"],[1,"location","ml-1"],[1,"col-2","date","pl-0","pt-1",2,"text-align","center",3,"click"],["mat-icon-button","","matTooltip","Summary","class","icon-tray-button ml-auto mr-2",3,"click",4,"ngIf"],["mat-icon-button","","matTooltip","Summary",1,"icon-tray-button","ml-auto","mr-2",3,"click"],[1,"row","pl-4","pt-2","pb-2"],[1,"row","list-card-styles"],[1,"card","listcard",2,"border-left","3px solid #009432 !important","padding","5px"],[1,"col-1","pt-1","accountId"],[1,"col-2","pl-3","name"],[3,"speed","width","height","primaryColor","secondaryColor"],["ngx-rect","","width","1000","height","70","y","50","x","0","rx","5","ry","5"],[1,"col-2","saleUnit"],[1,"col-2","pl-2","industry"],[1,"col-2","accountOwner"],[1,"col-2","parent",2,"padding-left","37px"],["mat-icon-button","","matTooltip","Edit accounts",1,"icon-tray-button","ml-auto"],["mat-icon-button","","matTooltip","View more",1,"icon-tray-button","ml-auto"],[1,"d-flex","justify-content-center","align-items-center","mt-5","slide-in-top"],[1,"d-flex","justify-content-center","align-items-center","slide-from-down"],["src","https://assets.kebs.app/images/assembleteam.png","height","240","width","280",1,"mt-4"],[1,"col-1","d-flex","pt-2","pl-3"],[1,"mediumSubtleText","pl-1"],["appearance","outline",1,"ml-0"],["id","searchId","matInput","","type","search","name","search","placeholder","Search Associates"],["matSuffix",""],["mat-button","","matSuffix","","mat-icon-button","","aria-label","Clear","style","height: 30px; width: 30px; line-height: 1",3,"click",4,"ngIf"],[1,"col-3","ml-2","smallSubtleText","pl-0"],[1,"col-2","ml-3","smallSubtleText","pl-0"],["mat-button","","matSuffix","","mat-icon-button","","aria-label","Clear",2,"height","30px","width","30px","line-height","1",3,"click"],["matTooltip","Clear search",2,"font-size","18px !important","color","#66615b !important"],[1,"card","listcard",2,"border-left","3px solid #da7cf7 !important","padding","1px"],[1,"col-2","pl-0","pt-1","ml-2","name"],[1,"name","ml-3","text-padding",3,"click"],["content-type","template","max-width","300",1,"col-3","details","pt-1","pl-0","text","text-padding",3,"tooltip","click"],["wfhTooltip",""],[1,"ml-3",2,"padding-top","3px"],[1,"status-icon","content-center",3,"ngStyle"],[1,"col-2","costcenter","pt-1","pl-0","pr-0","text","text-padding",3,"click"],[1,"col-2","ml-2","costcenter","pt-1","pl-0","pr-0","text","text-padding",3,"click"],[1,"col-1","ml-2","location","pt-1","text","text-padding",3,"click"],["class","col-1 ml-2 location text","style","padding-top:3px !important",4,"ngIf"],[1,"row",2,"font-size","12px"],[2,"padding-left","1.5rem !important"],[1,"ml-2"],[2,"padding-left","1.1rem !important"],[2,"padding-left","0.5rem !important"],[1,"col-1","ml-2","location","text",2,"padding-top","3px !important"],["mat-icon-button","","matTooltip","Come to Office",1,"icon-tray-button-visible","ml-auto","mr-2",3,"click"],["mat-icon-button","","matTooltip","Comments",1,"icon-tray-button-visible","ml-auto","mr-2",3,"click"],["id","searchId","matInput","","type","search","name","search","placeholder","Search Associates",3,"keyup"],["mat-button","","matSuffix","","mat-icon-button","","aria-label","Clear","style","height: 30px; width: 30px; line-height: 1",4,"ngIf"],["mat-button","","matSuffix","","mat-icon-button","","aria-label","Clear",2,"height","30px","width","30px","line-height","1"],[1,"col-2","pl-2","pt-1","ml-2","smallSubtleText"],[1,"col-4","pl-3","pt-1","ml-2","smallSubtleText"],[1,"col-3","pl-0","pt-1","ml-2","smallSubtleText"],[1,"col-2","pl-0","pt-1","ml-2","smallSubtleText","pl-0"],[1,"card","listcard",2,"padding","1px",3,"ngClass"],[1,"name","ml-3","text-padding",3,"matTooltip"],[1,"col-4","pl-0","pt-1","ml-2"],[1,"ml-3","text-padding",3,"matTooltip"],[1,"col-3","pl-0","pt-1","ml-2","costcenter"],[1,"col-2","pl-0","pt-1","ml-2","costcenter"],[1,"col-2","smallSubtleText",2,"text-align","center"],["class","col-2 ml-4 smallSubtleText pl-0",4,"ngIf"],[1,"col-2","ml-4","smallSubtleText","pl-0"]],template:function(e,t){1&e&&(r["\u0275\u0275template"](0,z,8,7,"div",0),r["\u0275\u0275template"](1,Ve,4,1,"div",1),r["\u0275\u0275template"](2,We,3,2,"div",1),r["\u0275\u0275template"](3,qe,4,3,"div",1)),2&e&&(r["\u0275\u0275property"]("ngIf",!t.submissionComponentActivated),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","ts"==t.selectedToggle),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","wfh"==t.selectedToggle),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","lbDetails"==t.selectedToggle))},directives:[a.NgIf,T.b,T.a,a.NgClass,f.a,I.a,v.a,y.c,x.b,P.g,s.e,s.v,s.k,P.i,y.i,P.f,y.h,k.f,k.g,k.d,V.c,L.a,Y.a,a.NgForOf,j.a,s.y,a.NgStyle,R.b,R.c],styles:[".my-team-styles[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]{color:#1a1a1a;font-size:14px;font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.my-team-styles[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field{width:35vw}.my-team-styles[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-infix{font-size:14px!important;padding:.5em 0!important;border-top:.54375em solid transparent!important}.my-team-styles[_ngcontent-%COMP%]   .content-center[_ngcontent-%COMP%]{align-items:center;display:flex}.my-team-styles[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:14px;color:#66615b;font-weight:400}.my-team-styles[_ngcontent-%COMP%]   .ib13[_ngcontent-%COMP%]{font-size:14px;font-weight:400;text-align:center;color:#1a1a1a;display:inline}.my-team-styles[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]{margin-top:5px!important;line-height:8px;width:26px;height:26px;margin-right:5px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.my-team-styles[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.my-team-styles[_ngcontent-%COMP%]   .view-button-active[_ngcontent-%COMP%]{background-color:#cf0001!important;margin-top:5px!important;line-height:8px;width:26px;height:26px;margin-right:5px!important;box-shadow:0 3px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.my-team-styles[_ngcontent-%COMP%]   .view-button-active[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#fff;font-size:18px}.my-team-styles[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.my-team-styles[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#3c3c3c;font-weight:400;font-size:22px}.my-team-styles[_ngcontent-%COMP%]   .mediumSubtleText[_ngcontent-%COMP%]{font-size:14px;color:#66615b}.my-team-styles[_ngcontent-%COMP%]   .smallSubtleText[_ngcontent-%COMP%]{color:#66615b;font-size:11px}.my-team-styles[_ngcontent-%COMP%]   .drop-btn[_ngcontent-%COMP%]{font-size:13px!important;height:38px!important;line-height:2!important;padding:0 13px}@keyframes slide-top{0%{transform:translateY(10px)}to{transform:translateY(0)}}.my-team-styles[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}.my-team-styles[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}.my-team-styles[_ngcontent-%COMP%]   .listcard[_ngcontent-%COMP%]{transition:all .25s linear;height:auto}.my-team-styles[_ngcontent-%COMP%]   .listcard[_ngcontent-%COMP%]:hover{box-shadow:0 4px 8px 0 rgba(0,0,0,.2),0 6px 20px 0 rgba(0,0,0,.19);cursor:pointer}.my-team-styles[_ngcontent-%COMP%]   .listcard[_ngcontent-%COMP%]:hover   .icon-tray-button[_ngcontent-%COMP%]{visibility:visible!important;animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}.my-team-styles[_ngcontent-%COMP%]   .icon-tray-button-visible[_ngcontent-%COMP%]{width:21px!important;height:21px!important;line-height:25px!important;visibility:visible!important}.my-team-styles[_ngcontent-%COMP%]   .arrow-button[_ngcontent-%COMP%]{height:27px;width:27px;line-height:27px}.my-team-styles[_ngcontent-%COMP%]   .smallCardIcon[_ngcontent-%COMP%]{font-size:18px!important;color:#868683!important}.my-team-styles[_ngcontent-%COMP%]   .name[_ngcontent-%COMP%]{font-size:14px;color:#cf0001;font-family:Roboto;font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.my-team-styles[_ngcontent-%COMP%]   .icon-tray-button[_ngcontent-%COMP%]{width:21px!important;height:21px!important;line-height:25px!important;visibility:hidden}.my-team-styles[_ngcontent-%COMP%]   .text-padding[_ngcontent-%COMP%]{padding-top:6px!important}.my-team-styles[_ngcontent-%COMP%]   .costcenter[_ngcontent-%COMP%], .my-team-styles[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%], .my-team-styles[_ngcontent-%COMP%]   .details[_ngcontent-%COMP%], .my-team-styles[_ngcontent-%COMP%]   .location[_ngcontent-%COMP%]{font-size:14px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.my-team-styles[_ngcontent-%COMP%]   .spinner-align[_ngcontent-%COMP%]{margin:auto;display:inline-block}.my-team-styles[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]{font-size:14px!important}.my-team-styles[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-form-field-infix{padding:0!important;border-top:0!important}.my-team-styles[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding-bottom:0!important}.my-team-styles[_ngcontent-%COMP%]     .mat-form-field-underline{display:none}.my-team-styles[_ngcontent-%COMP%]   .is-draft[_ngcontent-%COMP%]{background:grey;color:#fff!important}.my-team-styles[_ngcontent-%COMP%]   .is-submitted[_ngcontent-%COMP%]{background:#ff7200;color:#fff!important}.my-team-styles[_ngcontent-%COMP%]   .is-approved[_ngcontent-%COMP%]{background:#009432;color:#fff!important}.my-team-styles[_ngcontent-%COMP%]   .is-rejected[_ngcontent-%COMP%]{background:#cf0001;color:#fff!important}.my-team-styles[_ngcontent-%COMP%]   .status-circular[_ngcontent-%COMP%]{height:13px;width:13px;margin-top:4px;border-radius:50%;box-shadow:0 2px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.my-team-styles[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%]{font-size:17px}.my-team-styles[_ngcontent-%COMP%]   .is-draft-side[_ngcontent-%COMP%]{border-left-color:grey!important}.my-team-styles[_ngcontent-%COMP%]   .is-submitted-side[_ngcontent-%COMP%]{border-left-color:#ff7200!important}.my-team-styles[_ngcontent-%COMP%]   .is-approved-side[_ngcontent-%COMP%]{border-left-color:#009432!important}.my-team-styles[_ngcontent-%COMP%]   .is-rejected-side[_ngcontent-%COMP%]{border-left-color:#cf0001!important}.my-team-styles[_ngcontent-%COMP%]   .col-1x[_ngcontent-%COMP%]{flex:0 0 5.333333%;max-width:5.333333%}.my-team-styles[_ngcontent-%COMP%]   .col-2x[_ngcontent-%COMP%]{flex:0 0 10.333333%;max-width:10.333333%}.my-team-styles[_ngcontent-%COMP%]   .is-head[_ngcontent-%COMP%]{color:#cf0001!important}.my-team-styles[_ngcontent-%COMP%]   .indent-level-1[_ngcontent-%COMP%]{padding-left:calc(24px * 2)!important;border-left:1px solid #cacaca}.my-team-styles[_ngcontent-%COMP%]   .indent-level-2[_ngcontent-%COMP%]{padding-left:calc(24px * 3)!important;border-left:1px solid #cacaca}.my-team-styles[_ngcontent-%COMP%]   .indent-level-3[_ngcontent-%COMP%]{padding-left:calc(24px * 4)!important;border-left:1px solid #cacaca}.my-team-styles[_ngcontent-%COMP%]   .indent-level-4[_ngcontent-%COMP%]{padding-left:calc(24px * 5)!important;border-left:1px solid #cacaca}.my-team-styles[_ngcontent-%COMP%]   .indent-level-5[_ngcontent-%COMP%]{padding-left:calc(24px * 6)!important;border-left:1px solid #cacaca}.my-team-styles[_ngcontent-%COMP%]   .is-fh-day[_ngcontent-%COMP%]{border-radius:50%;border:5px solid #6ab04c;background:#6ab04c}.my-team-styles[_ngcontent-%COMP%]   .is-fh-day[_ngcontent-%COMP%], .my-team-styles[_ngcontent-%COMP%]   .is-leave-day[_ngcontent-%COMP%]{font-size:12px;color:#fff;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.my-team-styles[_ngcontent-%COMP%]   .is-leave-day[_ngcontent-%COMP%]{border-radius:50%;border:5px solid #eb3b5a;background:#eb3b5a}.my-team-styles[_ngcontent-%COMP%]   .is-leave-side[_ngcontent-%COMP%]{border-left:3px solid #eb3b5a!important}.my-team-styles[_ngcontent-%COMP%]   .is-fh-side[_ngcontent-%COMP%], .my-team-styles[_ngcontent-%COMP%]   .is-h-side[_ngcontent-%COMP%]{border-left:3px solid #6ab04c!important}.my-team-styles[_ngcontent-%COMP%]   .is-pl-side[_ngcontent-%COMP%]{border-left:3px solid #feca57!important}.my-team-styles[_ngcontent-%COMP%]   .is-oh-side[_ngcontent-%COMP%]{border-left:3px solid #575afe!important}.my-team-styles[_ngcontent-%COMP%]   .is-cl-side[_ngcontent-%COMP%]{border-left:3px solid #7f7f7f!important}.my-team-styles[_ngcontent-%COMP%]   .is-pl-day[_ngcontent-%COMP%]{border-radius:50%;color:#1a1a1a!important;border:5px solid #feca57;background:#feca57}.my-team-styles[_ngcontent-%COMP%]   .is-oh-day[_ngcontent-%COMP%], .my-team-styles[_ngcontent-%COMP%]   .is-pl-day[_ngcontent-%COMP%]{font-size:12px;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.my-team-styles[_ngcontent-%COMP%]   .is-oh-day[_ngcontent-%COMP%]{border-radius:50%;color:#1a1a1a;border:5px solid #575afe;background:#575afe}.my-team-styles[_ngcontent-%COMP%]   .is-cl-day[_ngcontent-%COMP%]{border-radius:50%;border:5px solid #7f7f7f;background:#7f7f7f}.my-team-styles[_ngcontent-%COMP%]   .is-cl-day[_ngcontent-%COMP%], .my-team-styles[_ngcontent-%COMP%]   .is-h-day[_ngcontent-%COMP%]{font-size:12px;color:#fff;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.my-team-styles[_ngcontent-%COMP%]   .is-h-day[_ngcontent-%COMP%]{border-radius:50%;border:5px solid #6ab04c;background:#6ab04c}.my-team-styles[_ngcontent-%COMP%]   .is-bl-day[_ngcontent-%COMP%]{border-radius:50%;border:5px solid #d82909;background:#d82909}.my-team-styles[_ngcontent-%COMP%]   .is-bl-day[_ngcontent-%COMP%], .my-team-styles[_ngcontent-%COMP%]   .is-cel-day[_ngcontent-%COMP%]{font-size:12px;color:#fff;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.my-team-styles[_ngcontent-%COMP%]   .is-cel-day[_ngcontent-%COMP%]{border-radius:50%;border:5px solid purple;background:purple}.my-team-styles[_ngcontent-%COMP%]   .is-sl-day[_ngcontent-%COMP%]{border-radius:50%;border:5px solid #45546e;background:#45546e}.my-team-styles[_ngcontent-%COMP%]   .is-sl-day[_ngcontent-%COMP%], .my-team-styles[_ngcontent-%COMP%]   .is-wl-day[_ngcontent-%COMP%]{font-size:12px;color:#fff;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.my-team-styles[_ngcontent-%COMP%]   .is-wl-day[_ngcontent-%COMP%]{border-radius:50%;border:15px solid #b7d251;background:#b7d251}.btn-toggle-selected[_ngcontent-%COMP%]{font-size:12px!important;background-color:#c92020!important;color:#fff}"]}),e})();const tt=[{path:"",children:[{path:"",component:et},{path:":label",component:et}]}];let nt=(()=>{class e{}return e.\u0275mod=r["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=r["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.k.forChild(tt)],i.k]}),e})();var at=n("Xi0T"),it=n("d3UM"),ot=n("/1cH"),rt=n("MutI"),st=n("wZkO");let lt=(()=>{class e{}return e.\u0275mod=r["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=r["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[a.CommonModule,nt,at.a,f.b,I.b,s.p,s.E,v.b,it.d,ot.c,k.e,c.v,rt.d,y.e,x.c,P.h,V.b,g.g,R.a,Y.b,T.c,j.b,st.g]]}),e})()}}]);