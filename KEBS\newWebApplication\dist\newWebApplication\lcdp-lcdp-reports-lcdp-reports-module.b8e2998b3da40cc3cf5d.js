(window.webpackJsonp=window.webpackJsonp||[]).push([[784],{Huuk:function(e,t,o){"use strict";o.r(t),o.d(t,"LcdpReportsModule",(function(){return d}));var n=o("ofXK"),r=o("tyNb"),i=o("fXoL"),p=o("z52X"),c=o("LcQX");const s=["reportContentContainer"],l=[{path:"",component:(()=>{class e{constructor(e,t,o,n){this._lcdpService=e,this._util=t,this.route=o,this.compiler=n}ngOnInit(){this.routeSubscription=this.route.parent.params.subscribe(e=>{e.lcdpApplicationId&&e.reportId?(this._lcdpService.lcdpDetails.lcdpApplicationId=e.lcdpApplicationId,this._lcdpService.lcdpDetails.applicationId=e.appId,this.resolveComponent(e.reportId)):this._util.showToastMessage("Application ID/Report ID not Found, Kindly contact KEBS Team !")})}resolveComponent(e){switch(this.reportContentContainer&&this.reportContentContainer.clear(),this._lcdpService.setTabLoadingObservable(!0),parseInt(e)){case 1:this.loadSLAReportComponent();break;case 2:this.loadSLABotReportComponent()}}loadSLAReportComponent(){Promise.all([o.e(7),o.e(15),o.e(297)]).then(o.bind(null,"Ux3F")).then(e=>{const t=this.compiler.compileModuleSync(e.LcdpSlaReportModule).create(this.reportContentContainer.injector).componentFactoryResolver.resolveComponentFactory(e.LcdpSlaReportComponent);this.reportContentContainer.createComponent(t)})}loadSLABotReportComponent(){Promise.all([o.e(8),o.e(20),o.e(19),o.e(44),o.e(48),o.e(52),o.e(0),o.e(296)]).then(o.bind(null,"glTm")).then(e=>{const t=this.compiler.compileModuleSync(e.LcdpSlaBotReportModule).create(this.reportContentContainer.injector).componentFactoryResolver.resolveComponentFactory(e.LcdpSlaBotReportComponent);this.reportContentContainer.createComponent(t)})}ngOnDestroy(){this.routeSubscription&&this.routeSubscription.unsubscribe(),this._lcdpService.resetLcdpDetails()}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](p.a),i["\u0275\u0275directiveInject"](c.a),i["\u0275\u0275directiveInject"](r.a),i["\u0275\u0275directiveInject"](i.Compiler))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-lcdp-reports-landing-page"]],viewQuery:function(e,t){if(1&e&&i["\u0275\u0275viewQuery"](s,!0,i.ViewContainerRef),2&e){let e;i["\u0275\u0275queryRefresh"](e=i["\u0275\u0275loadQuery"]())&&(t.reportContentContainer=e.first)}},decls:2,vars:0,consts:[["reportContentContainer",""]],template:function(e,t){1&e&&i["\u0275\u0275elementContainer"](0,null,0)},styles:[""]}),e})()}];let a=(()=>{class e{}return e.\u0275mod=i["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=i["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[r.k.forChild(l)],r.k]}),e})(),d=(()=>{class e{}return e.\u0275mod=i["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=i["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[n.CommonModule,a]]}),e})()}}]);