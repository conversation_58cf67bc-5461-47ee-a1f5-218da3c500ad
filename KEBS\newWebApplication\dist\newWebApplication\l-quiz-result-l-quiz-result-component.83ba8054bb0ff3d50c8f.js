(window.webpackJsonp=window.webpackJsonp||[]).push([[736],{pAR7:function(e,t,n){"use strict";n.r(t),n.d(t,"LQuizResultComponent",(function(){return d}));var r=n("0IaG"),o={};!function e(t,n,r,o){var a=!!(t.Worker&&t.Blob&&t.Promise&&t.OffscreenCanvas&&t.OffscreenCanvasRenderingContext2D&&t.HTMLCanvasElement&&t.HTMLCanvasElement.prototype.transferControlToOffscreen&&t.URL&&t.URL.createObjectURL);function s(){}function i(e){var r=n.exports.Promise,o=void 0!==r?r:t.Promise;return"function"==typeof o?new o(e):(e(s,s),null)}var c,g,l,d,p,f,h,u,m=(l=Math.floor(1e3/60),d={},p=0,"function"==typeof requestAnimationFrame&&"function"==typeof cancelAnimationFrame?(c=function(e){var t=Math.random();return d[t]=requestAnimationFrame((function n(r){p===r||p+l-1<r?(p=r,delete d[t],e()):d[t]=requestAnimationFrame(n)})),t},g=function(e){d[e]&&cancelAnimationFrame(d[e])}):(c=function(e){return setTimeout(e,l)},g=function(e){return clearTimeout(e)}),{frame:c,cancel:g}),M=(u={},function(){if(f)return f;if(!r&&a){var t=["var CONFETTI, SIZE = {}, module = {};","("+e.toString()+")(this, module, true, SIZE);","onmessage = function(msg) {","  if (msg.data.options) {","    CONFETTI(msg.data.options).then(function () {","      if (msg.data.callback) {","        postMessage({ callback: msg.data.callback });","      }","    });","  } else if (msg.data.reset) {","    CONFETTI.reset();","  } else if (msg.data.resize) {","    SIZE.width = msg.data.resize.width;","    SIZE.height = msg.data.resize.height;","  } else if (msg.data.canvas) {","    SIZE.width = msg.data.canvas.width;","    SIZE.height = msg.data.canvas.height;","    CONFETTI = module.exports.create(msg.data.canvas);","  }","}"].join("\n");try{f=new Worker(URL.createObjectURL(new Blob([t])))}catch(n){return void 0!==typeof console&&"function"==typeof console.warn&&console.warn("\ud83c\udf8a Could not load worker",n),null}!function(e){function t(t,n){e.postMessage({options:t||{},callback:n})}e.init=function(t){var n=t.transferControlToOffscreen();e.postMessage({canvas:n},[n])},e.fire=function(n,r,o){if(h)return t(n,null),h;var a=Math.random().toString(36).slice(2);return h=i((function(r){function s(t){t.data.callback===a&&(delete u[a],e.removeEventListener("message",s),h=null,o(),r())}e.addEventListener("message",s),t(n,a),u[a]=s.bind(null,{data:{callback:a}})}))},e.reset=function(){for(var t in e.postMessage({reset:!0}),u)u[t](),delete u[t]}}(f)}return f}),C={particleCount:50,angle:90,spread:45,startVelocity:45,decay:.9,gravity:1,drift:0,ticks:200,x:.5,y:.5,shapes:["square","circle"],zIndex:100,colors:["#26ccff","#a25afd","#ff5e7e","#88ff5a","#fcff42","#ffa62d","#ff36ff"],disableForReducedMotion:!1,scalar:1};function P(e,t,n){return function(e,t){return t?t(e):e}(e&&null!=e[t]?e[t]:C[t],n)}function b(e){return e<0?0:Math.floor(e)}function O(e){return parseInt(e,16)}function _(e){return e.map(v)}function v(e){var t=String(e).replace(/[^0-9a-f]/gi,"");return t.length<6&&(t=t[0]+t[0]+t[1]+t[1]+t[2]+t[2]),{r:O(t.substring(0,2)),g:O(t.substring(2,4)),b:O(t.substring(4,6))}}function w(e){e.width=document.documentElement.clientWidth,e.height=document.documentElement.clientHeight}function y(e){var t=e.getBoundingClientRect();e.width=t.width,e.height=t.height}function x(e,n){var s,c=!e,g=!!P(n||{},"resize"),l=P(n,"disableForReducedMotion",Boolean),d=a&&P(n||{},"useWorker")?M():null,p=c?w:y,f=!(!e||!d||!e.__confetti_initialized),h="function"==typeof matchMedia&&matchMedia("(prefers-reduced-motion)").matches;function u(n){var a=l||P(n,"disableForReducedMotion",Boolean),u=P(n,"zIndex",Number);if(a&&h)return i((function(e){e()}));c&&s?e=s.canvas:c&&!e&&(e=function(e){var t=document.createElement("canvas");return t.style.position="fixed",t.style.top="0px",t.style.left="0px",t.style.pointerEvents="none",t.style.zIndex=e,t}(u),document.body.appendChild(e)),g&&!f&&p(e);var M={width:e.width,height:e.height};function C(){if(d){var t={getBoundingClientRect:function(){if(!c)return e.getBoundingClientRect()}};return p(t),void d.postMessage({resize:{width:t.width,height:t.height}})}M.width=M.height=null}function O(){s=null,g&&t.removeEventListener("resize",C),c&&e&&(document.body.removeChild(e),e=null,f=!1)}return d&&!f&&d.init(e),f=!0,d&&(e.__confetti_initialized=!0),g&&t.addEventListener("resize",C,!1),d?d.fire(n,M,O):function(t,n,a){for(var c,g,l,d,f=P(t,"particleCount",b),h=P(t,"angle",Number),u=P(t,"spread",Number),M=P(t,"startVelocity",Number),C=P(t,"decay",Number),O=P(t,"gravity",Number),v=P(t,"drift",Number),w=P(t,"colors",_),y=P(t,"ticks",Number),x=P(t,"shapes"),k=P(t,"scalar"),S=function(e){var t=P(e,"origin",Object);return t.x=P(t,"x",Number),t.y=P(t,"y",Number),t}(t),E=f,I=[],T=e.width*S.x,z=e.height*S.y;E--;)I.push((g=(c={x:T,y:z,angle:h,spread:u,startVelocity:M,color:w[E%w.length],shape:x[(d=x.length,Math.floor(Math.random()*(d-0))+0)],ticks:y,decay:C,gravity:O,drift:v,scalar:k}).angle*(Math.PI/180),l=c.spread*(Math.PI/180),{x:c.x,y:c.y,wobble:10*Math.random(),velocity:.5*c.startVelocity+Math.random()*c.startVelocity,angle2D:-g+(.5*l-Math.random()*l),tiltAngle:Math.random()*Math.PI,color:c.color,shape:c.shape,tick:0,totalTicks:c.ticks,decay:c.decay,drift:c.drift,random:Math.random()+5,tiltSin:0,tiltCos:0,wobbleX:0,wobbleY:0,gravity:3*c.gravity,ovalScalar:.6,scalar:c.scalar}));return s?s.addFettis(I):(s=function(e,t,n,a,s){var c,g,l=t.slice(),d=e.getContext("2d"),p=i((function(t){function i(){c=g=null,d.clearRect(0,0,a.width,a.height),s(),t()}c=m.frame((function t(){!r||a.width===o.width&&a.height===o.height||(a.width=e.width=o.width,a.height=e.height=o.height),a.width||a.height||(n(e),a.width=e.width,a.height=e.height),d.clearRect(0,0,a.width,a.height),(l=l.filter((function(e){return function(e,t){t.x+=Math.cos(t.angle2D)*t.velocity+t.drift,t.y+=Math.sin(t.angle2D)*t.velocity+t.gravity,t.wobble+=.1,t.velocity*=t.decay,t.tiltAngle+=.1,t.tiltSin=Math.sin(t.tiltAngle),t.tiltCos=Math.cos(t.tiltAngle),t.random=Math.random()+5,t.wobbleX=t.x+10*t.scalar*Math.cos(t.wobble),t.wobbleY=t.y+10*t.scalar*Math.sin(t.wobble);var n=t.tick++/t.totalTicks,r=t.x+t.random*t.tiltCos,o=t.y+t.random*t.tiltSin,a=t.wobbleX+t.random*t.tiltCos,s=t.wobbleY+t.random*t.tiltSin;return e.fillStyle="rgba("+t.color.r+", "+t.color.g+", "+t.color.b+", "+(1-n)+")",e.beginPath(),"circle"===t.shape?e.ellipse?e.ellipse(t.x,t.y,Math.abs(a-r)*t.ovalScalar,Math.abs(s-o)*t.ovalScalar,Math.PI/10*t.wobble,0,2*Math.PI):function(e,t,n,r,o,a,s,i,c){e.save(),e.translate(t,n),e.rotate(a),e.scale(r,o),e.arc(0,0,1,0,i,void 0),e.restore()}(e,t.x,t.y,Math.abs(a-r)*t.ovalScalar,Math.abs(s-o)*t.ovalScalar,Math.PI/10*t.wobble,0,2*Math.PI):(e.moveTo(Math.floor(t.x),Math.floor(t.y)),e.lineTo(Math.floor(t.wobbleX),Math.floor(o)),e.lineTo(Math.floor(a),Math.floor(s)),e.lineTo(Math.floor(r),Math.floor(t.wobbleY))),e.closePath(),e.fill(),t.tick<t.totalTicks}(d,e)}))).length?c=m.frame(t):i()})),g=i}));return{addFettis:function(e){return l=l.concat(e),p},canvas:e,promise:p,reset:function(){c&&m.cancel(c),g&&g()}}}(e,I,p,n,a)).promise}(n,M,O)}return u.reset=function(){d&&d.reset(),s&&s.reset()},u}n.exports=x(null,{useWorker:!0,resize:!0}),n.exports.create=x}(function(){return"undefined"!=typeof window?window:"undefined"!=typeof self?self:this||{}}(),o,!1);var a=o.exports.create,s=n("fXoL"),i=n("bTqV"),c=n("NFeN"),g=n("ofXK");const l=function(e){return{borderColor:e}};let d=(()=>{class e{constructor(e,t){this.dialogRef=e,this.result=t,this.getProgressColor=()=>this.scorePercent<30?"#cf0001":this.scorePercent<60&&this.scorePercent>=30?"orange":this.scorePercent>=60&&this.scorePercent<80||this.scorePercent>80?"#73ba5b":void 0,this.getProgressComment=()=>this.scorePercent<30?"You have to work hard !":this.scorePercent<=60&&this.scorePercent>=30?"Come on, Just a few miles ahead !":this.scorePercent>=60&&this.scorePercent<80?"Awesome, You are just close !":this.scorePercent>80?"Brilliant ! I'm speechless.":void 0}ngOnInit(){this.scorePercent=Math.floor(this.result.obtainedMarks/this.result.totalMarks*100);let e=10*Math.round(this.scorePercent/10);if(e>60){var t=document.getElementById("myCanvas");a(t,{resize:!0,useWorker:!0})({particleCount:200,spread:200})}document.getElementById("pr").setAttribute("data-percentage",e+"")}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275directiveInject"](r.h),s["\u0275\u0275directiveInject"](r.a))},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["learner-l-quiz-result"]],decls:18,vars:11,consts:[["id","myCanvas"],[1,"d-flex","justify-content-end"],["mat-icon-button","",2,"color","gray",3,"click"],["data-percentage","0","id","pr",1,"progress","mt-5"],[1,"progress-left"],[1,"progress-bar",3,"ngStyle"],[1,"progress-right"],[1,"progress-value"],[1,"progress-text"],[1,"d-flex","justify-content-center","mt-4"],[2,"font-size","23px"]],template:function(e,t){1&e&&(s["\u0275\u0275element"](0,"canvas",0),s["\u0275\u0275elementStart"](1,"div",1),s["\u0275\u0275elementStart"](2,"button",2),s["\u0275\u0275listener"]("click",(function(){return t.dialogRef.close()})),s["\u0275\u0275elementStart"](3,"mat-icon"),s["\u0275\u0275text"](4,"close"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](5,"div",3),s["\u0275\u0275elementStart"](6,"span",4),s["\u0275\u0275element"](7,"span",5),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](8,"span",6),s["\u0275\u0275element"](9,"span",5),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](10,"div",7),s["\u0275\u0275elementStart"](11,"div",8),s["\u0275\u0275text"](12),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](13,"div",9),s["\u0275\u0275text"](14),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](15,"div",9),s["\u0275\u0275elementStart"](16,"span",10),s["\u0275\u0275text"](17),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e&&(s["\u0275\u0275advance"](7),s["\u0275\u0275property"]("ngStyle",s["\u0275\u0275pureFunction1"](7,l,t.getProgressColor())),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("ngStyle",s["\u0275\u0275pureFunction1"](9,l,t.getProgressColor())),s["\u0275\u0275advance"](3),s["\u0275\u0275textInterpolate1"]("",t.scorePercent,"%"),s["\u0275\u0275advance"](2),s["\u0275\u0275textInterpolate3"](" Total Marks : ",t.result.totalMarks," Correct : ",t.result.correctAnswersCount," Incorrect : ",t.result.wrongAnswersCount,"\n"),s["\u0275\u0275advance"](3),s["\u0275\u0275textInterpolate"](t.getProgressComment()))},directives:[i.a,c.a,g.NgStyle],styles:['#myCanvas[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;height:100%;z-index:-1}.progress[_ngcontent-%COMP%]{width:150px;height:150px;line-height:150px;background:none;margin:0 auto;box-shadow:none;position:relative}.progress[_ngcontent-%COMP%]:after{content:"";width:100%;border-radius:50%;border:10px solid #eee;left:0}.progress[_ngcontent-%COMP%]:after, .progress[_ngcontent-%COMP%] > span[_ngcontent-%COMP%]{height:100%;position:absolute;top:0}.progress[_ngcontent-%COMP%] > span[_ngcontent-%COMP%]{width:50%;overflow:hidden;z-index:1}.progress[_ngcontent-%COMP%]   .progress-left[_ngcontent-%COMP%]{left:0}.progress[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]{width:100%;height:100%;background:none;position:absolute;top:0;border:10px solid #ffb43e}.progress[_ngcontent-%COMP%]   .progress-left[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]{left:100%;border-top-right-radius:75px;border-bottom-right-radius:75px;border-left:0;transform-origin:center left}.progress[_ngcontent-%COMP%]   .progress-right[_ngcontent-%COMP%]{right:0}.progress[_ngcontent-%COMP%]   .progress-right[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]{left:-100%;border-top-left-radius:75px;border-bottom-left-radius:75px;border-right:0;transform-origin:center right}.progress[_ngcontent-%COMP%]   .progress-value[_ngcontent-%COMP%]{display:flex;border-radius:50%;font-size:36px;text-align:center;line-height:20px;align-items:center;justify-content:center;height:100%;width:100%;font-weight:300}.progress[_ngcontent-%COMP%]   .progress-value[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{margin-top:10px}.progress[_ngcontent-%COMP%]   .progress-value[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:12px;text-transform:uppercase}.progress[_ngcontent-%COMP%]   .progress-text[_ngcontent-%COMP%]{font-weight:800}.progress[data-percentage="10"][_ngcontent-%COMP%]   .progress-right[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]{animation:loading-1 1.5s linear forwards}.progress[data-percentage="10"][_ngcontent-%COMP%]   .progress-left[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]{animation:0}.progress[data-percentage="20"][_ngcontent-%COMP%]   .progress-right[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]{animation:loading-2 1.5s linear forwards}.progress[data-percentage="20"][_ngcontent-%COMP%]   .progress-left[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]{animation:0}.progress[data-percentage="30"][_ngcontent-%COMP%]   .progress-right[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]{animation:loading-3 1.5s linear forwards}.progress[data-percentage="30"][_ngcontent-%COMP%]   .progress-left[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]{animation:0}.progress[data-percentage="40"][_ngcontent-%COMP%]   .progress-right[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]{animation:loading-4 1.5s linear forwards}.progress[data-percentage="40"][_ngcontent-%COMP%]   .progress-left[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]{animation:0}.progress[data-percentage="50"][_ngcontent-%COMP%]   .progress-right[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]{animation:loading-5 1.5s linear forwards}.progress[data-percentage="50"][_ngcontent-%COMP%]   .progress-left[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]{animation:0}.progress[data-percentage="60"][_ngcontent-%COMP%]   .progress-right[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]{animation:loading-5 1.5s linear forwards}.progress[data-percentage="60"][_ngcontent-%COMP%]   .progress-left[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]{animation:loading-1 1.5s linear 1.5s forwards}.progress[data-percentage="70"][_ngcontent-%COMP%]   .progress-right[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]{animation:loading-5 1.5s linear forwards}.progress[data-percentage="70"][_ngcontent-%COMP%]   .progress-left[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]{animation:loading-2 1.5s linear 1.5s forwards}.progress[data-percentage="80"][_ngcontent-%COMP%]   .progress-right[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]{animation:loading-5 1.5s linear forwards}.progress[data-percentage="80"][_ngcontent-%COMP%]   .progress-left[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]{animation:loading-3 1.5s linear 1.5s forwards}.progress[data-percentage="90"][_ngcontent-%COMP%]   .progress-right[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]{animation:loading-5 1.5s linear forwards}.progress[data-percentage="90"][_ngcontent-%COMP%]   .progress-left[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]{animation:loading-4 1.5s linear 1.5s forwards}.progress[data-percentage="100"][_ngcontent-%COMP%]   .progress-right[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]{animation:loading-5 1.5s linear forwards}.progress[data-percentage="100"][_ngcontent-%COMP%]   .progress-left[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%]{animation:loading-5 1.5s linear 1.5s forwards}@keyframes loading-1{0%{transform:rotate(0deg)}to{transform:rotate(36deg)}}@keyframes loading-2{0%{transform:rotate(0deg)}to{transform:rotate(72deg)}}@keyframes loading-3{0%{transform:rotate(0deg)}to{transform:rotate(108deg)}}@keyframes loading-4{0%{transform:rotate(0deg)}to{transform:rotate(144deg)}}@keyframes loading-5{0%{transform:rotate(0deg)}to{transform:rotate(180deg)}}.progress[_ngcontent-%COMP%]{margin-bottom:1em}']}),e})()}}]);