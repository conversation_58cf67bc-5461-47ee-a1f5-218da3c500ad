(window.webpackJsonp=window.webpackJsonp||[]).push([[935],{"3KYs":function(t,e,a){"use strict";a.r(e),a.d(e,"DynamicContentDialogComponent",(function(){return S}));var i=a("mrSG"),s=a("0IaG"),r=a("XNiG"),n=a("1G5W"),d=a("fXoL"),o=a("YVm3"),c=a("XNFG"),p=a("DnWP"),l=a("RThm"),h=a("ofXK"),u=a("Xa2L"),g=a("yx4D");function m(t,e){if(1&t&&(d["\u0275\u0275elementStart"](0,"div",10),d["\u0275\u0275element"](1,"div",11),d["\u0275\u0275pipe"](2,"svgSecurityBypass"),d["\u0275\u0275elementEnd"]()),2&t){const t=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("innerHTML",d["\u0275\u0275pipeBind1"](2,1,t.data.svg),d["\u0275\u0275sanitizeHtml"])}}function b(t,e){if(1&t&&(d["\u0275\u0275elementStart"](0,"div",12),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()),2&t){const t=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate"](t.data.subTitle)}}function I(t,e){if(1&t&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275text"](1),d["\u0275\u0275elementContainerEnd"]()),2&t){const t=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",t.data.yesBtnText," ")}}function _(t,e){1&t&&d["\u0275\u0275element"](0,"mat-spinner",13)}const f=function(t){return{"margin-bottom":t}},C=function(t){return{"pointer-events":t}};let S=(()=>{class t{constructor(t,e,a,i,s,n){this.data=t,this._dialogRef=e,this._jobService=a,this._toaster=i,this._candidateService=s,this._onboardingService=n,this._onDestroy=new r.b,this.isApiInProgress=!1}ngOnInit(){}onClose(){this._dialogRef.close(!1)}onClickYes(){var t,e;return Object(i.c)(this,void 0,void 0,(function*(){"jobStatus"==this.data.key?(yield this.updateJobStatus(this.data.params.jobIds,this.data.params.jobStatus,this.data.params.isBulkSelectActive,this.data.params.jobsExcluded,this.data.params.jobStatusId,this.data.params.isCampusJob,this.data.params.search_params,this.data.params.filter),this._dialogRef.close(!0)):"deleteJob"==this.data.key?(yield this.removeJob(this.data.params),this._dialogRef.close(!0)):"talentPool"==this.data.key?(yield this.moveCandidateToTalentPipeline(this.data.params),this._dialogRef.close(!0)):"blockCandidate"==this.data.key?(yield this.blacklistCandidates(this.data.params),this._dialogRef.close(!0)):"candidateJobStatus"==this.data.key?this.data.params&&(null===(t=this.data.params[0])||void 0===t?void 0:t.stageId)==(null===(e=this.data.params[0])||void 0===e?void 0:e.stage_id)?(this._toaster.showWarning("Warning \u26a0\ufe0f","Same stage movement is not allowed!",3e3),this._dialogRef.close(!1)):(yield this.updateCandidateJobStage(this.data.params),this._dialogRef.close(!0)):"candidateCurrentStageStatus"==this.data.key?(yield this.updateCandidateStageStatus(this.data.params),this._dialogRef.close(!0)):"document"==this.data.key?(yield this.sendDocumentUploadLink(this.data.params),this._dialogRef.close(!0)):"checklistPriority"==this.data.key?(yield this.updateCandidateChecklistPrioriyInBulk(this.data.params),this._dialogRef.close(!0)):"candidateOnboardingStatus"==this.data.key?("onboardingDetailsGroup"==this.data.module?yield this.updateOnboardingStatusForCandidatesInGroupInBulk(this.data.params):yield this.updateOnboardingStatusForCandidatesInBulk(this.data.params),this._dialogRef.close(!0)):"deleteOnboardingCandidate"==this.data.key?(yield this.deleteOnboardingCandidatesInBulk(this.data.params),this._dialogRef.close(!0)):"deleteOnboardingGroup"==this.data.key?(yield this.deleteOnboardingGroupInBulk(this.data.params),this._dialogRef.close(!0)):this._dialogRef.close(!0)}))}removeJob(t){return Object(i.c)(this,void 0,void 0,(function*(){return this.isApiInProgress=!0,new Promise((e,a)=>this._jobService.removeJob(t.data,t.isBulkSelectActive,t.jobsExcluded,t.search_params,t.filter,t.isCampusJob).pipe(Object(n.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this._toaster.showSuccess("Success","Delete Successful!",3e3):this._toaster.showError("Error",t.msg,3e3),this.isApiInProgress=!1,e(!0)},error:t=>{this._toaster.showError("Error",t.message?t.message:"Delete Failed!",3e3),this.isApiInProgress=!1,a()}}))}))}updateJobStatus(t,e,a,s,r,d,o,c){return Object(i.c)(this,void 0,void 0,(function*(){return this.isApiInProgress=!0,new Promise((i,p)=>this._jobService.updateJobStatus(t,e,a,s,r,d,o,c).pipe(Object(n.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this._toaster.showSuccess("Success","Update Successful!",3e3):this._toaster.showError("Error",t.msg,3e3),this.isApiInProgress=!1,i(!0)},error:t=>{this._toaster.showError("Error",t.message?t.message:"Update Failed!",3e3),this.isApiInProgress=!1,p()}}))}))}moveCandidateToTalentPipeline(t){return Object(i.c)(this,void 0,void 0,(function*(){return this.isApiInProgress=!0,new Promise((e,a)=>this._candidateService.moveCandidateToTalentPipeline(t.candidateIds,t.jobId,t.isBulkSelectActive,t.candidatesExcluded,t.stageId,t.filter,t.search_params).pipe(Object(n.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this._toaster.showSuccess("Success","Candidate(s) Moved to Talent Pipeline Successfully!",3e3):this._toaster.showError("Error",t.msg,3e3),this.isApiInProgress=!1,e(!0)},error:t=>{this._toaster.showError("Error",t.message?t.message:"Movement Failed!",3e3),this.isApiInProgress=!1,a()}}))}))}blacklistCandidates(t){return Object(i.c)(this,void 0,void 0,(function*(){return this.isApiInProgress=!0,new Promise((e,a)=>this._candidateService.blacklistCandidates(t.candidateIds,t.isBulkSelectActive,t.candidatesExcluded,t.stageId,t.jobId,t.filter,t.search_params,t.candidateStatusId).pipe(Object(n.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this._toaster.showSuccess("Success","Candidate(s) Blocked Successfully!",3e3):this._toaster.showError("Error",t.msg,3e3),this.isApiInProgress=!1,e(!0)},error:t=>{this._toaster.showError("Error",t.message?t.message:"Block Failed!",3e3),this.isApiInProgress=!1,a()}}))}))}updateCandidateJobStage(t){return Object(i.c)(this,void 0,void 0,(function*(){return this.isApiInProgress=!0,new Promise((e,a)=>this._candidateService.updateCandidateJobStage(t).pipe(Object(n.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this._toaster.showSuccess("Success","Candidate(s) Job Stage Changed Successfully!",3e3):this._toaster.showError("Error",t.msg,3e3),this.isApiInProgress=!1,e(!0)},error:t=>{this._toaster.showError("Error",t.message?t.message:"Update Failed!",3e3),this.isApiInProgress=!1,a()}}))}))}updateCandidateStageStatus(t){return Object(i.c)(this,void 0,void 0,(function*(){return this.isApiInProgress=!0,new Promise((e,a)=>this._candidateService.updateCandidateStageStatus(t).pipe(Object(n.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this._toaster.showSuccess("Success","Candidate(s) Job Stage Status Changed Successfully!",3e3):this._toaster.showError("Error",t.msg,3e3),this.isApiInProgress=!1,e(!0)},error:t=>{this._toaster.showError("Error",t.message?t.message:"Update Failed!",3e3),this.isApiInProgress=!1,a()}}))}))}sendDocumentUploadLink(t){return Object(i.c)(this,void 0,void 0,(function*(){return this.isApiInProgress=!0,new Promise((e,a)=>this._candidateService.sendDocumentUploadLink(t).pipe(Object(n.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this._toaster.showSuccess("Success","Upload Document Link Sent to selected Candidates Successfully!",3e3):this._toaster.showError("Error",t.msg,3e3),this.isApiInProgress=!1,e(!0)},error:t=>{this._toaster.showError("Error",t.message?t.message:"Document Link Failed To Send!",3e3),this.isApiInProgress=!1,a()}}))}))}updateCandidateChecklistPrioriyInBulk(t){return Object(i.c)(this,void 0,void 0,(function*(){return this.isApiInProgress=!0,new Promise((e,a)=>this._onboardingService.updateCandidateChecklistPrioriyInBulk(t).pipe(Object(n.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this._toaster.showSuccess("Success \u2705",t.msg,3e3):this._toaster.showError("Error",t.msg,3e3),this.isApiInProgress=!1,e(!0)},error:t=>{this._toaster.showError("Error",t.message?t.message:"Failed To Update Priority!",3e3),this.isApiInProgress=!1,a()}}))}))}updateOnboardingStatusForCandidatesInBulk(t){return Object(i.c)(this,void 0,void 0,(function*(){return this.isApiInProgress=!0,new Promise((e,a)=>this._onboardingService.updateOnboardingStatusForCandidatesInBulk(t).pipe(Object(n.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this._toaster.showSuccess("Success \u2705",t.msg,3e3):this._toaster.showError("Error",t.msg,3e3),this.isApiInProgress=!1,e(!0)},error:t=>{this._toaster.showError("Error",t.message?t.message:"Failed To Update Status!",3e3),this.isApiInProgress=!1,a()}}))}))}deleteOnboardingCandidatesInBulk(t){return Object(i.c)(this,void 0,void 0,(function*(){return this.isApiInProgress=!0,new Promise((e,a)=>this._onboardingService.deleteOnboardingCandidatesInBulk(t).pipe(Object(n.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this._toaster.showSuccess("Success \u2705",t.msg,3e3):this._toaster.showError("Error",t.msg,3e3),this.isApiInProgress=!1,e(!0)},error:t=>{this._toaster.showError("Error",t.message?t.message:"Failed To Update Status!",3e3),this.isApiInProgress=!1,a()}}))}))}updateOnboardingStatusForCandidatesInGroupInBulk(t){return Object(i.c)(this,void 0,void 0,(function*(){return this.isApiInProgress=!0,new Promise((e,a)=>this._onboardingService.updateOnboardingStatusForCandidatesInGroupInBulk(t).pipe(Object(n.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this._toaster.showSuccess("Success \u2705",t.msg,3e3):this._toaster.showError("Error",t.msg,3e3),this.isApiInProgress=!1,e(!0)},error:t=>{this._toaster.showError("Error",t.message?t.message:"Failed To Update Status!",3e3),this.isApiInProgress=!1,a()}}))}))}deleteOnboardingGroupInBulk(t){return Object(i.c)(this,void 0,void 0,(function*(){return this.isApiInProgress=!0,new Promise((e,a)=>this._onboardingService.deleteOnboardingGroupInBulk(t).pipe(Object(n.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this._toaster.showSuccess("Success \u2705",t.msg,3e3):this._toaster.showError("Error",t.msg,3e3),this.isApiInProgress=!1,e(!0)},error:t=>{this._toaster.showError("Error",t.message?t.message:"Failed To Update Status!",3e3),this.isApiInProgress=!1,a()}}))}))}}return t.\u0275fac=function(e){return new(e||t)(d["\u0275\u0275directiveInject"](s.a),d["\u0275\u0275directiveInject"](s.h),d["\u0275\u0275directiveInject"](o.a),d["\u0275\u0275directiveInject"](c.a),d["\u0275\u0275directiveInject"](p.a),d["\u0275\u0275directiveInject"](l.a))},t.\u0275cmp=d["\u0275\u0275defineComponent"]({type:t,selectors:[["app-dynamic-content-dialog"]],decls:14,vars:15,consts:[[1,"d-flex","flex-column","bg-container"],["class","d-flex justify-content-center fill-icon-color",4,"ngIf"],[1,"title-text",3,"ngStyle"],["class","sub-title-text",4,"ngIf"],[1,"content-text"],[1,"d-flex","justify-content-end",2,"gap","8px"],[1,"cancel-btn",3,"ngStyle","click"],[1,"yes-btn",3,"ngStyle","click"],[4,"ngIf","ngIfElse"],["loading",""],[1,"d-flex","justify-content-center","fill-icon-color"],[3,"innerHTML"],[1,"sub-title-text"],["diameter","20",1,"white-spinner"]],template:function(t,e){if(1&t&&(d["\u0275\u0275elementStart"](0,"div",0),d["\u0275\u0275template"](1,m,3,3,"div",1),d["\u0275\u0275elementStart"](2,"div",2),d["\u0275\u0275text"](3),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](4,b,2,1,"div",3),d["\u0275\u0275elementStart"](5,"div",4),d["\u0275\u0275text"](6),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](7,"div",5),d["\u0275\u0275elementStart"](8,"div",6),d["\u0275\u0275listener"]("click",(function(){return e.onClose()})),d["\u0275\u0275text"](9," Cancel "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](10,"div",7),d["\u0275\u0275listener"]("click",(function(){return e.onClickYes()})),d["\u0275\u0275template"](11,I,2,1,"ng-container",8),d["\u0275\u0275template"](12,_,1,0,"ng-template",null,9,d["\u0275\u0275templateRefExtractor"]),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&t){const t=d["\u0275\u0275reference"](13);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.data.isIconVisible),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngStyle",d["\u0275\u0275pureFunction1"](9,f,e.data.subTitle?"":"16px")),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",e.data.title," "),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.data.subTitle),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate"](e.data.content),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngStyle",d["\u0275\u0275pureFunction1"](11,C,e.isApiInProgress?"none":"")),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngStyle",d["\u0275\u0275pureFunction1"](13,C,e.isApiInProgress?"none":"")),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!e.isApiInProgress)("ngIfElse",t)}},directives:[h.NgIf,h.NgStyle,u.c],pipes:[g.a],styles:[".bg-container[_ngcontent-%COMP%]{padding:32px}.bg-container[_ngcontent-%COMP%]   .fill-icon-color[_ngcontent-%COMP%]{fill:var(--atsprimaryColor);margin-bottom:16px}.bg-container[_ngcontent-%COMP%]   .title-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:18px;font-weight:700;color:#111434;margin-bottom:4px;line-height:1.35;text-align:justify}.bg-container[_ngcontent-%COMP%]   .sub-title-text[_ngcontent-%COMP%]{font-size:12px;color:#8b95a5}.bg-container[_ngcontent-%COMP%]   .content-text[_ngcontent-%COMP%], .bg-container[_ngcontent-%COMP%]   .sub-title-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-weight:400;margin-bottom:16px}.bg-container[_ngcontent-%COMP%]   .content-text[_ngcontent-%COMP%]{font-size:14px;color:#6e7b8f;text-align:justify}.bg-container[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{border:1px solid #45546e;border-radius:8px;padding:11px 15px;color:#45546e}.bg-container[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%], .bg-container[_ngcontent-%COMP%]   .yes-btn[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:700;cursor:pointer}.bg-container[_ngcontent-%COMP%]   .yes-btn[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;background:linear-gradient(270deg,var(--atsprimaryColor),var(--atssecondaryColor) 105.29%);border-radius:8px;padding:0 16px;color:#fff}  .white-spinner circle{stroke:#fff!important}"]}),t})()},DnWP:function(t,e,a){"use strict";a.d(e,"a",(function(){return n}));var i=a("tk/3"),s=a("wd/R"),r=a("fXoL");let n=(()=>{class t{constructor(t){this._http=t}fetchCandidatesForJob(t){return this._http.post("api/ats/candidate/fetchCandidatesForJob",{jobDetails:t})}deleteCandidatefromTag(t){return this._http.post("api/ats/candidate/deleteCandidatefromTag",{tagDetails:t})}tagCandidates(t){return this._http.post("api/ats/candidate/tagCandidates",{tagDetails:t})}moveCandidateToTalentPipeline(t,e,a,i,s,r,n){return this._http.post("api/ats/candidate/moveCandidateToTalentPipeline",{candidateIds:t,jobId:e,isBulkSelectActive:a,candidatesExcluded:i,stageId:s,filter:r,search_params:n})}blacklistCandidates(t,e,a,i,s,r,n,d){return this._http.post("api/ats/candidate/blacklistCandidates",{candidateIds:t,isBulkSelectActive:e,candidatesExcluded:a,stageId:i,jobId:s,filter:r,search_params:n,candidateStatusId:d})}updateCandidateJobStage(t){return this._http.post("api/ats/candidate/updateCandidateJobStage",{details:t})}sendEmailForCandidates(t){return this._http.post("api/ats/utilService/sendEmailForCandidates",{details:t})}uploadBenchMarkDetails(t,e){return this._http.post("api/ats/candidate/uploadBenchMarkDetails",{jobId:t,data:e})}uploadAptitudeScoreDetails(t,e,a,i){return this._http.post("api/ats/candidate/uploadAptitudeScoreDetails",{jobId:t,data:e,imageLinkArray:a,reportLinkArray:i})}uploadNonScientificScoreDetails(t,e,a,i){return this._http.post("api/ats/candidate/uploadNonScientificScoreDetails",{jobId:t,data:e,imageLinkArray:a,reportLinkArray:i})}downloadBenchmarkReport(t,e,a=0){return this._http.post("api/ats/candidate/downloadBenchmarkReport",{jobId:t,candidateId:a,filter:e})}downloadAptitudeReport(t,e,a=0){return this._http.post("api/ats/candidate/downloadAptitudeReport",{jobId:t,candidateId:a,filter:e})}downloadNonScientificReport(t,e,a=0){return this._http.post("api/ats/candidate/downloadNonScientificReport",{jobId:t,candidateId:a,filter:e})}fetchAllcandidateTags(){return this._http.post("api/ats/candidate/fetchAllcandidateTags",{})}getCandidateOtherApplicationDetails(t,e){return this._http.post("api/ats/candidate/getCandidateOtherApplicationDetails",{jobId:t,candidateId:e})}updateCandidateStageStatus(t){return this._http.post("api/ats/candidate/updateCandidateStageStatus",{details:t})}getCandidateBasicDetails(t,e,a){return this._http.post("api/ats/candidate/getCandidateBasicDetails",{jobId:t,candidateId:e,currentUserAid:a})}getInterviewPanelist(t){return this._http.post("api/ats/candidate/getInterviewPanelist",{details:t})}getInterviewerScoreDetails(t){return this._http.post("api/ats/candidate/getInterviewerScoreDetails",{details:t})}getCandidateResumeDetails(t,e){return this._http.post("api/ats/candidate/getCandidateResumeDetails",{jobId:t,candidateId:e})}fetchCandidateCardViewScoreDetails(t,e,a,i,s,r){return this._http.post("api/ats/candidate/fetchCandidateCardViewScoreDetails",{jobId:t,stageId:e,sectionId:a,sortByMetricId:i,skip:s,limit:r})}fetchScorecardSectionDetails(t,e){return this._http.post("api/ats/candidate/fetchScorecardSectionDetails",{jobId:t,stageId:e})}fetchScorecardMetricDetails(t,e,a){return this._http.post("api/ats/candidate/fetchScorecardMetricDetails",{jobId:t,stageId:e,sectionId:a})}getCandidateCommentsDetails(t,e,a,i){return this._http.post("api/ats/candidate/getCandidateCommentsDetails",{jobId:t,candidateId:e,currentUserAid:a,searchText:i})}postCandidateComments(t,e){return this._http.post("api/ats/candidate/postCandidateComments",{candidateId:t,commentsDetails:e})}getCandidateInterviewDetails(t,e){return this._http.post("api/ats/candidate/getCandidateInterviewDetails",{jobId:t,candidateId:e})}scheduleInterview(t){return this._http.post("api/ats/interview/scheduleInterview",{details:t})}getCandidateDocumentDetails(t,e){return this._http.post("api/ats/candidate/fetchcandidateDocuments",{jobId:t,candidateId:e})}fetchCandidateDetailsForQueue(t,e,a){return this._http.post("api/ats/candidate/fetchCandidateDetailsForQueue",{jobId:t,candidateId:e,currentUserAid:a})}getCandidateStreamDetails(t,e){return this._http.post("api/ats/candidate/getCandidateStreamDetails",{jobId:t,candidateId:e})}fetchCandidateUploadTemplate(){return this._http.post("api/ats/bulkActions/fetchCandidateUploadTemplate",{})}simulateCandidateApply(t,e){return this._http.post("api/ats/bulkActions/simulateCandidateApply",{jobId:t,data:e})}performCandidateBulkApply(t,e,a,i,s,r){return this._http.post("api/ats/bulkActions/performCandidateBulkApply",{jobId:t,data:e,fileName:a,fileSize:i,fileUploadStatus:s,isFromTalentPipeline:r})}fetchCandidateUploadFileHistory(t){return this._http.post("api/ats/bulkActions/fetchCandidateUploadFileHistory",{job_id:t})}fetchCandidateUpdSimulationResult(t){return this._http.post("api/ats/bulkActions/fetchCandidateUpdSimulationResult",{id:t})}getAllCandidateDetails(t,e,a,i){return this._http.post("api/ats/candidate/getAllCandidateDetails",{skip:t.skip,limit:t.limit,sort:t.sort,filter:t.filter,condition_query:t.condition_query,search_params:t.search_params,aid:e,isTalentPipeLine:a,candidateStatusId:t.candidateStatusId,isCampusJobTalentPipeline:i})}getTotalCandidateCount(t,e,a){return this._http.post("api/ats/candidate/getTotalCandidateCount",{skip:t.skip,limit:t.limit,sort:t.sort,filter:t.filter,condition_query:t.condition_query,search_params:t.search_params,isTalentPipeLine:e,candidateStatusId:t.candidateStatusId,isCampusJobTalentPipeline:a})}performResumeSimulation(t,e){return this._http.post("api/ats/bulkActions/simulateBulkResumeUpload",{data:e,jobId:t})}performCandidateBulkResumeUpload(t,e,a,i){return this._http.post("api/ats/bulkActions/performCandidateBulkResumeApply",{data:t,jobId:e,isFromTalentPipeline:a,dataArrayForResumeUpload:i})}getCandidateLatestJobDetails(t){return this._http.post("api/ats/candidate/getCandidateLatestJobDetails",{candidateId:t})}getScheduledInterviewDetails(t){return this._http.post("api/ats/interview/getScheduledInterviewDetails",{interviewId:t})}rescheduleInterview(t){return this._http.post("api/ats/interview/rescheduleInterview",{details:t})}cancelScheduledInterview(t){return this._http.post("api/ats/interview/cancelScheduledInterview",{interviewId:t})}getCandidateCountForJobByJobId(t){return this._http.post("api/ats/candidate/getCandidateCountForJobByJobId",{jobDetails:t})}updateStarRating(t,e,a,i){return this._http.post("api/ats/candidate/updateStarRating",{jobId:t,candidateId:e,rating:a,currentUserAid:i})}getMeetingsofInterviewers(t){return this._http.post("api/ats/interview/getMeetingsofInterviewers",{details:t})}getScorecardOtherDetails(t,e,a,i){return this._http.post("api/ats/candidate/getScorecardOtherDetails",{jobId:t,candidateId:e,stageId:a,interviewer:i})}sendDocumentUploadLink(t){return this._http.post("api/ats/candidate/sendDocumentUploadLink",{details:t})}getCandidateCustomQuestionDetails(t,e,a){return this._http.post("api/ats/candidate/getCandidateCustomQuestionDetails",{jobId:t,candidateId:e,currentUserAid:a})}uploadCandidateDocuments(t,e,a,s){const r=new FormData;r.append("jobId",t),r.append("candidateId",e),r.append("document",JSON.stringify(a)),r.append("file",s);const n=new i.f;return this._http.post("/api/ats/candidate/uploadCandidateDocuments",r,{headers:n})}deleteCandidateDocuments(t,e,a){return this._http.post("/api/ats/candidate/deleteCandidateDocuments",{jobId:t,candidateId:e,documentId:a})}retrieveCandidateOfferTrackingDetails(t,e){return this._http.post("api/ats/candidate/retrieveCandidateOfferTrackingDetails",{jobId:t,candidateId:e,currentDate:s().format("YYYY-MM-DD")})}getAllCandidateIds(t,e,a){return this._http.post("api/ats/candidate/getAllCandidateIds",{skip:t.skip,limit:t.limit,sort:t.sort,filter:t.filter,condition_query:t.condition_query,search_params:t.search_params,isTalentPipeLine:e,candidateStatusId:t.candidateStatusId,isCampusJobTalentPipeline:a})}getCandidateIdsForJobByJobId(t){return this._http.post("api/ats/candidate/getCandidateIdsForJobByJobId",{jobDetails:t})}uploadCertifications(t){return this._http.post("/api/ats/candidate/uploadCertifications",t)}performUploadUpdation(t,e){return this._http.post("api/ats/candidate/performUploadUpdation",{fileUploadDetails:t,candidateId:e})}getCandidateCertificates(t){return this._http.post("api/ats/candidate/getCandidateCertificates",{candidateId:t})}deleteCandidateCertificate(t){return this._http.post("api/ats/candidate/deleteCandidateCertificate",{certificateDetails:t})}createCampusInterviewForcandidates(t){return this._http.post("api/ats/interview/createCampusInterviewForcandidates",{details:t})}storeCandidateEmails(t){return this._http.post("api/ats/interview/storeCandidateEmails",{details:t})}getInterviewEmailData(t,e){return this._http.post("api/ats/interview/getInterviewEmailData",{jobId:e,candidateId:t})}}return t.\u0275fac=function(e){return new(e||t)(r["\u0275\u0275inject"](i.c))},t.\u0275prov=r["\u0275\u0275defineInjectable"]({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()}}]);