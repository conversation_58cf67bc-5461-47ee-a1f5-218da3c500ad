(window.webpackJsonp=window.webpackJsonp||[]).push([[756],{"+XcZ":function(e,t,n){"use strict";n.r(t),n.d(t,"LocationComponent",(function(){return E}));var i=n("mrSG"),o=n("3Pt+"),l=n("0IaG"),r=n("xG9w"),a=n("ofXK"),c=n("fXoL"),s=n("jtHE"),d=n("XNiG"),p=n("NJ67"),m=n("1G5W"),h=n("kmnG"),u=n("d3UM"),f=n("FKr1"),g=n("WJ5W");const v=["singleSelect"];function b(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"mat-option",6),c["\u0275\u0275listener"]("click",(function(){c["\u0275\u0275restoreView"](e);const n=t.$implicit;return c["\u0275\u0275nextContext"]().emitChanges(n)})),c["\u0275\u0275text"](1),c["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;c["\u0275\u0275property"]("value",e.name),c["\u0275\u0275advance"](1),c["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let y=(()=>{class e extends p.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new o.j,this.fieldFilterCtrl=new o.j,this.list=[],this.required=!1,this.valueChange=new c.EventEmitter,this.disabled=!1,this.filteredList=new s.a,this.change=new c.EventEmitter,this._onDestroy=new d.b,this.emitChanges=e=>{this.change.emit(e)}}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(m.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(m.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){null!=this.list&&this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}}return e.\u0275fac=function(t){return new(t||e)(c["\u0275\u0275directiveInject"](c.Renderer2))},e.\u0275cmp=c["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search"]],viewQuery:function(e,t){if(1&e&&c["\u0275\u0275viewQuery"](v,!0),2&e){let e;c["\u0275\u0275queryRefresh"](e=c["\u0275\u0275loadQuery"]())&&(t.singleSelect=e.first)}},inputs:{list:"list",placeholder:"placeholder",required:"required",disabled:"disabled"},outputs:{valueChange:"valueChange",change:"change"},features:[c["\u0275\u0275ProvidersFeature"]([{provide:o.t,useExisting:Object(c.forwardRef)(()=>e),multi:!0}]),c["\u0275\u0275InheritDefinitionFeature"],c["\u0275\u0275NgOnChangesFeature"]],decls:11,vars:11,consts:[["appearance","outline"],[3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value"],[3,"value","click",4,"ngFor","ngForOf"],[3,"value","click"]],template:function(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"mat-form-field",0),c["\u0275\u0275elementStart"](1,"mat-label"),c["\u0275\u0275text"](2),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](3,"mat-select",1,2),c["\u0275\u0275elementStart"](5,"mat-option"),c["\u0275\u0275element"](6,"ngx-mat-select-search",3),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](7,"mat-option",4),c["\u0275\u0275text"](8,"None"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275template"](9,b,2,2,"mat-option",5),c["\u0275\u0275pipe"](10,"async"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e&&(c["\u0275\u0275advance"](2),c["\u0275\u0275textInterpolate"](t.placeholder),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),c["\u0275\u0275advance"](3),c["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("value",null),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("ngForOf",c["\u0275\u0275pipeBind1"](10,9,t.filteredList)))},directives:[h.c,h.g,u.c,o.v,o.k,o.F,f.p,g.a,a.NgForOf],pipes:[a.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}"]}),e})();var x=n("dNgK"),C=(n("qFsG"),n("NFeN")),w=(n("/1cH"),n("bTqV")),S=(n("1yaQ"),n("pgif"));let E=(()=>{class e{constructor(e,t,n,i,l){this.fb=e,this.oppService=t,this.dialogData=n,this.snackBar=i,this.dialogRef=l,this.opportunityForm=this.fb.group({location:["",o.H.required]})}ngOnInit(){return Object(i.c)(this,void 0,void 0,(function*(){this.activityId=this.dialogData.activityId,this.edit_location=this.dialogData.location,yield this.oppService.getLocationList().then(e=>Object(i.c)(this,void 0,void 0,(function*(){this.location=e.data})),e=>{console.log(e)}),yield this.editLocation()}))}editLocation(){this.opportunityForm.patchValue({location:this.edit_location})}updateLocation(){console.log(this.opportunityForm),this.mylocation=r.findWhere(this.location,{name:this.opportunityForm.value.location}),this.oppService.updateLocationforAct(this.mylocation.office_id,this.mylocation.location_id,this.activityId).subscribe(e=>{this.snackBar.open("Location updated successfully!","Dismiss",{duration:2e3}),this.dialogRef.close({office_id:this.mylocation.office_id,location_id:this.mylocation.location_id})},e=>{console.log(e),this.snackBar.open("Cannot update location for this activity")})}closeDialog(){this.dialogRef.close()}}return e.\u0275fac=function(t){return new(t||e)(c["\u0275\u0275directiveInject"](o.i),c["\u0275\u0275directiveInject"](S.a),c["\u0275\u0275directiveInject"](l.a),c["\u0275\u0275directiveInject"](x.a),c["\u0275\u0275directiveInject"](l.h))},e.\u0275cmp=c["\u0275\u0275defineComponent"]({type:e,selectors:[["app-location"]],decls:22,vars:2,consts:[[3,"formGroup"],[1,"container"],[1,"col-12"],[1,"row",2,"margin-left","10px"],[1,"col-11","pl-0","pt-2","updateLocation"],[1,"col-1","d-flex"],["mat-icon-button","",1,"ml-auto","close-button",3,"click"],[1,"close-Icon"],[1,"row"],[1,"col-12","pl-0","quotes"],[1,"col-9","pl-0"],["required","true","placeholder","Location","formControlName","location",1,"create-account-field",3,"list"],[1,"col-3","pl-0"],["mat-icon-button","","matTooltip","Update Location","type","submit",1,"iconbtn","ml-2","mt-0",3,"click"]],template:function(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"form",0),c["\u0275\u0275elementStart"](1,"div",1),c["\u0275\u0275elementStart"](2,"div",2),c["\u0275\u0275elementStart"](3,"div",3),c["\u0275\u0275element"](4,"div",4),c["\u0275\u0275elementStart"](5,"div",5),c["\u0275\u0275elementStart"](6,"button",6),c["\u0275\u0275listener"]("click",(function(){return t.closeDialog()})),c["\u0275\u0275elementStart"](7,"mat-icon",7),c["\u0275\u0275text"](8,"close"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](9,"div",3),c["\u0275\u0275elementStart"](10,"div",2),c["\u0275\u0275elementStart"](11,"div",8),c["\u0275\u0275elementStart"](12,"div",9),c["\u0275\u0275elementStart"](13,"h4"),c["\u0275\u0275text"](14,"Kindly choose location for reflecting in timesheet "),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](15,"div",8),c["\u0275\u0275elementStart"](16,"div",10),c["\u0275\u0275element"](17,"app-input-search",11),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](18,"div",12),c["\u0275\u0275elementStart"](19,"button",13),c["\u0275\u0275listener"]("click",(function(){return t.updateLocation()})),c["\u0275\u0275elementStart"](20,"mat-icon"),c["\u0275\u0275text"](21," done_all"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e&&(c["\u0275\u0275property"]("formGroup",t.opportunityForm),c["\u0275\u0275advance"](17),c["\u0275\u0275property"]("list",t.location))},directives:function(){return[o.J,o.w,o.n,w.a,C.a,y,o.F,o.v,o.l]},styles:[".iconbtn[_ngcontent-%COMP%]{background-color:#c92020;color:#fff;padding:0;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}.btn-not-active[_ngcontent-%COMP%]{margin-right:6px!important;margin-bottom:3px;color:rgba(0,0,0,.534);background-color:rgba(194,193,193,.24);font-weight:400;font-size:13px!important;min-width:60px;line-height:33px;padding:0 8px;border-radius:4px}.updateLocation[_ngcontent-%COMP%]{font-size:14px;color:#cf0001;font-weight:500}.create-account-field[_ngcontent-%COMP%]{font-size:14px!important;width:100%!important;color:#66615b}.close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.quotes[_ngcontent-%COMP%]{font-style:italic;font-weight:500;color:#66615b;font-size:14px}"]}),e})()},jvuC:function(e,t,n){"use strict";n.r(t),n.d(t,"LocationComponent",(function(){return E}));var i=n("mrSG"),o=n("3Pt+"),l=n("0IaG"),r=n("xG9w"),a=n("ofXK"),c=n("fXoL"),s=n("jtHE"),d=n("XNiG"),p=n("NJ67"),m=n("1G5W"),h=n("kmnG"),u=n("d3UM"),f=n("FKr1"),g=n("WJ5W");const v=["singleSelect"];function b(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"mat-option",6),c["\u0275\u0275listener"]("click",(function(){c["\u0275\u0275restoreView"](e);const n=t.$implicit;return c["\u0275\u0275nextContext"]().emitChanges(n)})),c["\u0275\u0275text"](1),c["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;c["\u0275\u0275property"]("value",e.name),c["\u0275\u0275advance"](1),c["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let y=(()=>{class e extends p.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new o.j,this.fieldFilterCtrl=new o.j,this.list=[],this.required=!1,this.valueChange=new c.EventEmitter,this.disabled=!1,this.filteredList=new s.a,this.change=new c.EventEmitter,this._onDestroy=new d.b,this.emitChanges=e=>{this.change.emit(e)}}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(m.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(m.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){null!=this.list&&this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}}return e.\u0275fac=function(t){return new(t||e)(c["\u0275\u0275directiveInject"](c.Renderer2))},e.\u0275cmp=c["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search"]],viewQuery:function(e,t){if(1&e&&c["\u0275\u0275viewQuery"](v,!0),2&e){let e;c["\u0275\u0275queryRefresh"](e=c["\u0275\u0275loadQuery"]())&&(t.singleSelect=e.first)}},inputs:{list:"list",placeholder:"placeholder",required:"required",disabled:"disabled"},outputs:{valueChange:"valueChange",change:"change"},features:[c["\u0275\u0275ProvidersFeature"]([{provide:o.t,useExisting:Object(c.forwardRef)(()=>e),multi:!0}]),c["\u0275\u0275InheritDefinitionFeature"],c["\u0275\u0275NgOnChangesFeature"]],decls:11,vars:11,consts:[["appearance","outline"],[3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value"],[3,"value","click",4,"ngFor","ngForOf"],[3,"value","click"]],template:function(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"mat-form-field",0),c["\u0275\u0275elementStart"](1,"mat-label"),c["\u0275\u0275text"](2),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](3,"mat-select",1,2),c["\u0275\u0275elementStart"](5,"mat-option"),c["\u0275\u0275element"](6,"ngx-mat-select-search",3),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](7,"mat-option",4),c["\u0275\u0275text"](8,"None"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275template"](9,b,2,2,"mat-option",5),c["\u0275\u0275pipe"](10,"async"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e&&(c["\u0275\u0275advance"](2),c["\u0275\u0275textInterpolate"](t.placeholder),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),c["\u0275\u0275advance"](3),c["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("value",null),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("ngForOf",c["\u0275\u0275pipeBind1"](10,9,t.filteredList)))},directives:[h.c,h.g,u.c,o.v,o.k,o.F,f.p,g.a,a.NgForOf],pipes:[a.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}"]}),e})();var x=n("dNgK"),C=(n("qFsG"),n("NFeN")),w=(n("/1cH"),n("bTqV")),S=(n("1yaQ"),n("pgif"));let E=(()=>{class e{constructor(e,t,n,i,l){this.fb=e,this.oppService=t,this.dialogData=n,this.snackBar=i,this.dialogRef=l,this.opportunityForm=this.fb.group({location:["",o.H.required]})}ngOnInit(){return Object(i.c)(this,void 0,void 0,(function*(){this.activityId=this.dialogData.activityId,this.edit_location=this.dialogData.location,yield this.oppService.getLocationList().then(e=>Object(i.c)(this,void 0,void 0,(function*(){this.location=e.data})),e=>{console.log(e)}),yield this.editLocation()}))}editLocation(){console.log(this.edit_location),this.opportunityForm.patchValue({location:this.edit_location})}updateLocation(){console.log(this.opportunityForm),this.mylocation=r.findWhere(this.location,{name:this.opportunityForm.value.location}),this.oppService.updateLocationforAct(this.mylocation.office_id,this.mylocation.location_id,this.activityId).subscribe(e=>{this.snackBar.open("Location updated successfully!","Dismiss",{duration:2e3}),this.dialogRef.close({office_id:this.mylocation.office_id,location_id:this.mylocation.location_id})},e=>{console.log(e),this.snackBar.open("Cannot update location for this activity")})}closeDialog(){this.dialogRef.close()}}return e.\u0275fac=function(t){return new(t||e)(c["\u0275\u0275directiveInject"](o.i),c["\u0275\u0275directiveInject"](S.a),c["\u0275\u0275directiveInject"](l.a),c["\u0275\u0275directiveInject"](x.a),c["\u0275\u0275directiveInject"](l.h))},e.\u0275cmp=c["\u0275\u0275defineComponent"]({type:e,selectors:[["app-location"]],decls:22,vars:2,consts:[[3,"formGroup"],[1,"container"],[1,"col-12"],[1,"row",2,"margin-left","10px"],[1,"col-11","pl-0","pt-2","updateLocation"],[1,"col-1","d-flex"],["mat-icon-button","",1,"ml-auto","close-button",3,"click"],[1,"close-Icon"],[1,"row"],[1,"col-12","pl-0","quotes"],[1,"col-9","pl-0"],["required","true","placeholder","Location","formControlName","location",1,"create-account-field",3,"list"],[1,"col-3","pl-0"],["mat-icon-button","","matTooltip","Update Location","type","submit",1,"iconbtn","ml-2","mt-0",3,"click"]],template:function(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"form",0),c["\u0275\u0275elementStart"](1,"div",1),c["\u0275\u0275elementStart"](2,"div",2),c["\u0275\u0275elementStart"](3,"div",3),c["\u0275\u0275element"](4,"div",4),c["\u0275\u0275elementStart"](5,"div",5),c["\u0275\u0275elementStart"](6,"button",6),c["\u0275\u0275listener"]("click",(function(){return t.closeDialog()})),c["\u0275\u0275elementStart"](7,"mat-icon",7),c["\u0275\u0275text"](8,"close"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](9,"div",3),c["\u0275\u0275elementStart"](10,"div",2),c["\u0275\u0275elementStart"](11,"div",8),c["\u0275\u0275elementStart"](12,"div",9),c["\u0275\u0275elementStart"](13,"h4"),c["\u0275\u0275text"](14,"Kindly choose location for reflecting in timesheet "),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](15,"div",8),c["\u0275\u0275elementStart"](16,"div",10),c["\u0275\u0275element"](17,"app-input-search",11),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](18,"div",12),c["\u0275\u0275elementStart"](19,"button",13),c["\u0275\u0275listener"]("click",(function(){return t.updateLocation()})),c["\u0275\u0275elementStart"](20,"mat-icon"),c["\u0275\u0275text"](21," done_all"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e&&(c["\u0275\u0275property"]("formGroup",t.opportunityForm),c["\u0275\u0275advance"](17),c["\u0275\u0275property"]("list",t.location))},directives:function(){return[o.J,o.w,o.n,w.a,C.a,y,o.F,o.v,o.l]},styles:[".iconbtn[_ngcontent-%COMP%]{background-color:#c92020;color:#fff;padding:0;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}.btn-not-active[_ngcontent-%COMP%]{margin-right:6px!important;margin-bottom:3px;color:rgba(0,0,0,.534);background-color:rgba(194,193,193,.24);font-weight:400;font-size:13px!important;min-width:60px;line-height:33px;padding:0 8px;border-radius:4px}.updateLocation[_ngcontent-%COMP%]{font-size:14px;color:#cf0001;font-weight:500}.create-account-field[_ngcontent-%COMP%]{font-size:14px!important;width:100%!important;color:#66615b}.close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.quotes[_ngcontent-%COMP%]{font-style:italic;font-weight:500;color:#66615b;font-size:14px}"]}),e})()}}]);