(window.webpackJsonp=window.webpackJsonp||[]).push([[789,861,981],{H8H3:function(e,t,n){"use strict";n.r(t),n.d(t,"MyLeaveModule",(function(){return ke}));var i=n("ofXK"),a=n("tyNb"),o=n("mrSG"),r=n("1G5W"),l=n("XNiG"),s=n("wd/R"),d=n("xG9w"),c=n("Idmh"),p=n("1yaQ"),h=n("FKr1"),g=n("fXoL"),m=n("0IaG"),f=n("xYuU"),u=n("XXEo"),v=n("BVzC"),x=n("1A3m"),y=n("GnQ3"),C=n("dYSZ"),b=n("Qu3c"),_=n("bTqV"),O=n("NFeN"),w=n("f0Cb"),S=n("Xa2L"),D=n("Wp6s"),I=n("bSwM"),P=n("dlKe"),E=n("me71"),M=n("lVl8");function k(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div",5),g["\u0275\u0275elementStart"](1,"button",14),g["\u0275\u0275listener"]("click",(function(){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"]().openCompensationOff()})),g["\u0275\u0275text"](2," Claim It "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}if(2&e){const e=g["\u0275\u0275nextContext"]();g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("disabled",e.disableBtn)}}function L(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div",15),g["\u0275\u0275elementStart"](1,"button",16),g["\u0275\u0275listener"]("click",(function(){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"]().openLeavePolicy()})),g["\u0275\u0275elementStart"](2,"mat-icon",17),g["\u0275\u0275text"](3," summarize "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](4,"span",18),g["\u0275\u0275text"](5,"Leave Policy"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}}function A(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div",19),g["\u0275\u0275elementStart"](1,"button",20),g["\u0275\u0275listener"]("click",(function(){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"]().openHowToApplyLeave()})),g["\u0275\u0275elementStart"](2,"mat-icon",17),g["\u0275\u0275text"](3," help "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](4,"span",18),g["\u0275\u0275text"](5,"How To Apply Leave"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}}function T(e,t){1&e&&(g["\u0275\u0275elementStart"](0,"div",21),g["\u0275\u0275elementStart"](1,"button",22),g["\u0275\u0275elementStart"](2,"mat-icon",17),g["\u0275\u0275text"](3," auto_graph "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](4,"span",18),g["\u0275\u0275text"](5,"Summary"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]())}function F(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div",23),g["\u0275\u0275elementStart"](1,"div",24),g["\u0275\u0275elementStart"](2,"button",25),g["\u0275\u0275listener"]("click",(function(){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"]().applyLeave()})),g["\u0275\u0275text"](3,"Apply Leave"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}if(2&e){const e=g["\u0275\u0275nextContext"]();g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("disabled",e.disableBtn)}}function j(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"mat-icon",34),g["\u0275\u0275listener"]("click",(function(){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](4).appendLastCards()})),g["\u0275\u0275text"](1,"arrow_back"),g["\u0275\u0275elementEnd"]()}}function U(e,t){if(1&e&&(g["\u0275\u0275elementStart"](0,"div",32),g["\u0275\u0275template"](1,j,2,0,"mat-icon",33),g["\u0275\u0275elementEnd"]()),2&e){const e=g["\u0275\u0275nextContext"](3);g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",e.showSummaryScrollIcon)}}function R(e,t){if(1&e&&(g["\u0275\u0275elementStart"](0,"span",43),g["\u0275\u0275text"](1),g["\u0275\u0275elementEnd"]()),2&e){const e=g["\u0275\u0275nextContext"](3).$implicit;g["\u0275\u0275advance"](1),g["\u0275\u0275textInterpolate"](e.dataType)}}function V(e,t){if(1&e&&(g["\u0275\u0275elementStart"](0,"span",44),g["\u0275\u0275text"](1),g["\u0275\u0275elementEnd"]()),2&e){const e=g["\u0275\u0275nextContext"](3).$implicit;g["\u0275\u0275advance"](1),g["\u0275\u0275textInterpolate"](e.dataType)}}const B=function(e){return{backgroundColor:e,position:"absolute",right:"30px"}};function H(e,t){if(1&e&&g["\u0275\u0275element"](0,"div",45),2&e){const e=g["\u0275\u0275nextContext"](3).$implicit;g["\u0275\u0275property"]("ngStyle",g["\u0275\u0275pureFunction1"](1,B,e.statusColor))}}function N(e,t){if(1&e&&(g["\u0275\u0275elementStart"](0,"div",37),g["\u0275\u0275elementStart"](1,"div",38),g["\u0275\u0275template"](2,R,2,1,"span",39),g["\u0275\u0275template"](3,V,2,1,"span",40),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](4,"div",38),g["\u0275\u0275elementStart"](5,"span",41),g["\u0275\u0275text"](6),g["\u0275\u0275elementEnd"](),g["\u0275\u0275template"](7,H,1,3,"div",42),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()),2&e){const e=g["\u0275\u0275nextContext"](2).$implicit;g["\u0275\u0275property"]("matTooltip",e.tootltipData),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("ngIf",e.statusColor),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",!e.statusColor),g["\u0275\u0275advance"](3),g["\u0275\u0275textInterpolate1"](" ",e.dataTypeValue," "),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",e.statusColor)}}const z=function(e){return{"border-left":e}};function Y(e,t){if(1&e&&(g["\u0275\u0275elementStart"](0,"div",35),g["\u0275\u0275template"](1,N,8,5,"div",36),g["\u0275\u0275elementEnd"]()),2&e){const e=g["\u0275\u0275nextContext"]().$implicit;g["\u0275\u0275property"]("ngStyle",g["\u0275\u0275pureFunction1"](2,z,"4px solid "+e.statusColor)),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",!e.isWithDetailedTooltip)}}function W(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"mat-icon",34),g["\u0275\u0275listener"]("click",(function(){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](4).appedFirstCards()})),g["\u0275\u0275text"](1,"arrow_forward"),g["\u0275\u0275elementEnd"]()}}function q(e,t){if(1&e&&(g["\u0275\u0275elementStart"](0,"div",46),g["\u0275\u0275template"](1,W,2,0,"mat-icon",33),g["\u0275\u0275elementEnd"]()),2&e){const e=g["\u0275\u0275nextContext"](3);g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",e.showSummaryScrollIcon)}}function G(e,t){if(1&e&&(g["\u0275\u0275elementStart"](0,"div",28),g["\u0275\u0275template"](1,U,2,1,"div",29),g["\u0275\u0275template"](2,Y,2,4,"div",30),g["\u0275\u0275template"](3,q,2,1,"div",31),g["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=t.index,i=g["\u0275\u0275nextContext"](2);g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",0==n&&i.showLeftSummaryCardIcon),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",e.isVisible),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",n==i.summaryCards.length-1&&i.showRightSummaryCardIcon)}}function Q(e,t){if(1&e&&(g["\u0275\u0275elementStart"](0,"div",26),g["\u0275\u0275template"](1,G,4,3,"div",27),g["\u0275\u0275elementEnd"]()),2&e){const e=g["\u0275\u0275nextContext"]();g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngForOf",e.summaryCards)}}function X(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"mat-icon",81),g["\u0275\u0275listener"]("click",(function(){g["\u0275\u0275restoreView"](e);const t=g["\u0275\u0275nextContext"](2).index;return g["\u0275\u0275nextContext"](3).viewdroplist(t)})),g["\u0275\u0275text"](1,"keyboard_arrow_down"),g["\u0275\u0275elementEnd"]()}}function $(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"mat-icon",81),g["\u0275\u0275listener"]("click",(function(){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](5).closedroplist()})),g["\u0275\u0275text"](1,"keyboard_arrow_up"),g["\u0275\u0275elementEnd"]()}}function K(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div"),g["\u0275\u0275elementStart"](1,"div",82),g["\u0275\u0275elementStart"](2,"p",83),g["\u0275\u0275text"](3),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](4,"mat-checkbox",84),g["\u0275\u0275listener"]("change",(function(n){g["\u0275\u0275restoreView"](e);const i=t.index,a=t.$implicit,o=g["\u0275\u0275nextContext"](2).index;return g["\u0275\u0275nextContext"](3).checkboxvalue(n.checked,o,i,a.checkboxName,a.checkboxId)})),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;g["\u0275\u0275advance"](3),g["\u0275\u0275textInterpolate"](e.checkboxName),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("checked",e.isCheckboxSelected)}}function J(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div",73),g["\u0275\u0275elementStart"](1,"div",74),g["\u0275\u0275elementStart"](2,"span",75),g["\u0275\u0275text"](3),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](4,"span",76),g["\u0275\u0275elementStart"](5,"span",77),g["\u0275\u0275text"](6),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](7,"mat-icon",78),g["\u0275\u0275listener"]("click",(function(){g["\u0275\u0275restoreView"](e);const t=g["\u0275\u0275nextContext"](),n=t.$implicit,i=t.index;return g["\u0275\u0275nextContext"](3).clearonefilter(n,i)})),g["\u0275\u0275text"](8,"clear"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275template"](9,X,2,0,"mat-icon",79),g["\u0275\u0275template"](10,$,2,0,"mat-icon",79),g["\u0275\u0275elementStart"](11,"mat-card",80),g["\u0275\u0275template"](12,K,5,2,"div",69),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}if(2&e){const e=g["\u0275\u0275nextContext"](),t=e.$implicit,n=e.index,i=g["\u0275\u0275nextContext"](3);g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("matTooltip",t.filterName),g["\u0275\u0275advance"](1),g["\u0275\u0275textInterpolate"](t.filterName),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("matTooltip",t.multiOptionSelectSearchValues[0]),g["\u0275\u0275advance"](1),g["\u0275\u0275textInterpolate"](t.multiOptionSelectSearchValues[0]),g["\u0275\u0275advance"](3),g["\u0275\u0275property"]("ngIf",!i.dropdownflag),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",i.dropdownflag),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngClass",i.dropdownflag&&n==i.selecteddropdown?"droplistvisible":"droplisthidden"),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngForOf",t.checkboxValues)}}function Z(e,t){if(1&e&&(g["\u0275\u0275elementStart"](0,"div"),g["\u0275\u0275template"](1,J,13,8,"div",72),g["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=t.index,i=g["\u0275\u0275nextContext"](3);g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",e.multiOptionSelectSearchValues[0]&&i.displayedFilter==n)}}function ee(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div",85),g["\u0275\u0275elementStart"](1,"mat-icon",86),g["\u0275\u0275listener"]("click",(function(){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](3).onClear()})),g["\u0275\u0275text"](2,"clear"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}}function te(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div",67),g["\u0275\u0275elementStart"](1,"mat-icon",68),g["\u0275\u0275listener"]("click",(function(){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](2).goToPreviousFilter()})),g["\u0275\u0275text"](2,"keyboard_arrow_left"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275template"](3,Z,2,1,"div",69),g["\u0275\u0275elementStart"](4,"mat-icon",70),g["\u0275\u0275listener"]("click",(function(){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](2).goToNextFilter()})),g["\u0275\u0275text"](5,"keyboard_arrow_right"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275template"](6,ee,3,0,"div",71),g["\u0275\u0275elementEnd"]()}if(2&e){const e=g["\u0275\u0275nextContext"](2);g["\u0275\u0275advance"](3),g["\u0275\u0275property"]("ngForOf",e._udrfService.udrfData.mainFilterArray),g["\u0275\u0275advance"](3),g["\u0275\u0275property"]("ngIf",e._udrfService.udrfData.mainFilterArray.length>0)}}function ne(e,t){if(1&e&&(g["\u0275\u0275elementStart"](0,"div",102),g["\u0275\u0275text"](1),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](2,"div",102),g["\u0275\u0275text"](3),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](4,"div",102),g["\u0275\u0275text"](5),g["\u0275\u0275elementEnd"]()),2&e){const e=g["\u0275\u0275nextContext"]().$implicit;g["\u0275\u0275advance"](1),g["\u0275\u0275textInterpolate1"](" ",e.name," "),g["\u0275\u0275advance"](2),g["\u0275\u0275textInterpolate1"](" ",e.role," "),g["\u0275\u0275advance"](2),g["\u0275\u0275textInterpolate1"](" Level : ",e.level,"")}}function ie(e,t){if(1&e&&(g["\u0275\u0275elementStart"](0,"div"),g["\u0275\u0275element"](1,"app-user-image",99),g["\u0275\u0275template"](2,ne,6,3,"ng-template",100,101,g["\u0275\u0275templateRefExtractor"]),g["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=g["\u0275\u0275reference"](3);g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("tooltip",n)("id",e.oid)}}function ae(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div",104),g["\u0275\u0275listener"]("click",(function(){g["\u0275\u0275restoreView"](e);const t=g["\u0275\u0275nextContext"](2).$implicit;return g["\u0275\u0275nextContext"](3).showDetailView(t)})),g["\u0275\u0275text"](1," Withdraw "),g["\u0275\u0275elementEnd"]()}}function oe(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div",104),g["\u0275\u0275listener"]("click",(function(){g["\u0275\u0275restoreView"](e);const t=g["\u0275\u0275nextContext"](2).$implicit;return g["\u0275\u0275nextContext"](3).showDetailView(t)})),g["\u0275\u0275text"](1," Withdraw "),g["\u0275\u0275elementEnd"]()}}function re(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div",104),g["\u0275\u0275listener"]("click",(function(){g["\u0275\u0275restoreView"](e);const t=g["\u0275\u0275nextContext"](2).$implicit;return g["\u0275\u0275nextContext"](3).showDetailView(t)})),g["\u0275\u0275text"](1," Withdraw "),g["\u0275\u0275elementEnd"]()}}function le(e,t){if(1&e&&(g["\u0275\u0275elementStart"](0,"div"),g["\u0275\u0275template"](1,ae,2,0,"div",103),g["\u0275\u0275template"](2,oe,2,0,"div",103),g["\u0275\u0275template"](3,re,2,0,"div",103),g["\u0275\u0275elementEnd"]()),2&e){const e=g["\u0275\u0275nextContext"]().$implicit;g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",4!=e.status_id&&3!=e.status_id&&2!=e.status_id&&1!=e.is_claim),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",4!=e.status_id&&3!=e.status_id&&1==e.status_id&&1==e.is_claim),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",4!=e.status_id&&3!=e.status_id&&2==e.status_id&&0==e.is_claim)}}const se=function(e){return{color:e,cursor:"pointer","Padding-left":"13px"}},de=function(e){return{background:e}};function ce(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div"),g["\u0275\u0275elementStart"](1,"div",2),g["\u0275\u0275elementStart"](2,"div",89),g["\u0275\u0275text"](3),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](4,"div",90),g["\u0275\u0275text"](5),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](6,"div",91),g["\u0275\u0275text"](7),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](8,"div",92),g["\u0275\u0275listener"]("click",(function(){g["\u0275\u0275restoreView"](e);const n=t.$implicit;return g["\u0275\u0275nextContext"](3).showDetailView(n)})),g["\u0275\u0275text"](9),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](10,"div",93),g["\u0275\u0275text"](11),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](12,"div",93),g["\u0275\u0275text"](13),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](14,"div",94),g["\u0275\u0275template"](15,ie,4,2,"div",69),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](16,"div",95),g["\u0275\u0275elementStart"](17,"button",96),g["\u0275\u0275text"](18),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275template"](19,le,4,3,"div",97),g["\u0275\u0275elementEnd"](),g["\u0275\u0275element"](20,"mat-divider",98),g["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;g["\u0275\u0275advance"](3),g["\u0275\u0275textInterpolate1"](" REQ ",e.id," "),g["\u0275\u0275advance"](2),g["\u0275\u0275textInterpolate1"](" ",e.leaveId," "),g["\u0275\u0275advance"](2),g["\u0275\u0275textInterpolate1"](" ",e.uiFormattedDate," "),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngStyle",g["\u0275\u0275pureFunction1"](11,se,e.leave_color_code)),g["\u0275\u0275advance"](1),g["\u0275\u0275textInterpolate1"](" ",e.leaveType," "),g["\u0275\u0275advance"](2),g["\u0275\u0275textInterpolate1"](" ",e.days," "),g["\u0275\u0275advance"](2),g["\u0275\u0275textInterpolate1"](" ",e.applied_on," "),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("ngForOf",e.approvers),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("ngStyle",g["\u0275\u0275pureFunction1"](13,de,e.status_color_code)),g["\u0275\u0275advance"](1),g["\u0275\u0275textInterpolate1"](" ",e.status," "),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",!e.disableWithdraw)}}function pe(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div",87),g["\u0275\u0275listener"]("scrolled",(function(){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](2).getPendingApprovalsOnScroll()})),g["\u0275\u0275template"](1,ce,21,15,"div",69),g["\u0275\u0275elementStart"](2,"div",88),g["\u0275\u0275element"](3,"mat-spinner",63),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}if(2&e){const e=g["\u0275\u0275nextContext"](2);g["\u0275\u0275property"]("infiniteScrollDistance",.1)("scrollWindow",!1),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngForOf",e.leaveData),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("hidden",!e.pendingScrollApiInProgress)}}function he(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div",106),g["\u0275\u0275elementStart"](1,"button",112),g["\u0275\u0275listener"]("click",(function(){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"](3).applyLeave()})),g["\u0275\u0275text"](2," Apply Your First Leave "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}}function ge(e,t){1&e&&(g["\u0275\u0275elementStart"](0,"div",113),g["\u0275\u0275text"](1," No Leave History Found "),g["\u0275\u0275elementEnd"]())}function me(e,t){if(1&e&&(g["\u0275\u0275elementStart"](0,"div",105),g["\u0275\u0275elementStart"](1,"div",106),g["\u0275\u0275element"](2,"img",107),g["\u0275\u0275elementEnd"](),g["\u0275\u0275element"](3,"div",108),g["\u0275\u0275element"](4,"div",109),g["\u0275\u0275template"](5,he,3,0,"div",110),g["\u0275\u0275template"](6,ge,2,0,"div",111),g["\u0275\u0275elementEnd"]()),2&e){const e=g["\u0275\u0275nextContext"](2);g["\u0275\u0275advance"](5),g["\u0275\u0275property"]("ngIf",0==e.leaveData.length&&0==e._udrfService.udrfData.mainFilterArray.length),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",0==e.leaveData.length&&e._udrfService.udrfData.mainFilterArray.length>0)}}function fe(e,t){if(1&e){const e=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div",47),g["\u0275\u0275elementStart"](1,"div",48),g["\u0275\u0275elementStart"](2,"div",49),g["\u0275\u0275elementStart"](3,"span",50),g["\u0275\u0275text"](4,"Leave History"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](5,"div",51),g["\u0275\u0275listener"]("click",(function(){return g["\u0275\u0275restoreView"](e),g["\u0275\u0275nextContext"]().openPendingRequestsUdrfModal()})),g["\u0275\u0275elementStart"](6,"mat-icon",52),g["\u0275\u0275text"](7,"filter_list"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](8,"span",53),g["\u0275\u0275text"](9,"Filter"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275template"](10,te,7,2,"div",54),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](11,"div",55),g["\u0275\u0275elementStart"](12,"div",2),g["\u0275\u0275elementStart"](13,"div",56),g["\u0275\u0275text"](14," REQUEST ID "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](15,"div",57),g["\u0275\u0275text"](16," REQUEST TYPE "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](17,"div",57),g["\u0275\u0275text"](18," INTERVAL "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](19,"div",58),g["\u0275\u0275text"](20," TYPE "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](21,"div",59),g["\u0275\u0275text"](22," ABSENCE DAYS "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](23,"div",56),g["\u0275\u0275text"](24," APPLIED ON "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](25,"div",60),g["\u0275\u0275text"](26," APPROVERS "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](27,"div",56),g["\u0275\u0275text"](28," STATUS "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](29,"div",56),g["\u0275\u0275text"](30," ACTION "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275element"](31,"mat-divider"),g["\u0275\u0275elementStart"](32,"div",61),g["\u0275\u0275elementStart"](33,"div",62),g["\u0275\u0275element"](34,"mat-spinner",63),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](35,"div",64),g["\u0275\u0275template"](36,pe,4,4,"div",65),g["\u0275\u0275template"](37,me,7,2,"div",66),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}if(2&e){const e=g["\u0275\u0275nextContext"]();g["\u0275\u0275advance"](10),g["\u0275\u0275property"]("ngIf",e._udrfService.udrfData.mainFilterArray.length>0),g["\u0275\u0275advance"](23),g["\u0275\u0275property"]("hidden",!e.initialLoading),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("hidden",e.initialLoading),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",e.leaveData.length>0),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",0==e.leaveData.length)}}const ue={parse:{dateInput:"MMM YYYY"},display:{dateInput:"MMM YYYY",monthYearLabel:"MMM YYYY",dateA11yLabel:"LL",monthYearA11yLabel:"MMMM YYYY"}},ve=[{path:"",component:(()=>{class e{constructor(e,t,n,i,a,o,r){this.matDialog=e,this._leaveAppService=t,this.authService=n,this.errorService=i,this.toastService=a,this._udrfService=o,this.toastmessage=r,this.currentUserOid="",this.compensationOff=!1,this.leavePolicy=!0,this.applyLeaveBtn=!0,this.howToApplyLeave=!1,this.showSummary=!1,this.leaveData=[],this.weekOffData=[],this.holidayList=[],this.leaveBalanceDetails=[],this.summaryCards=[],this.showRightSummaryCardIcon=!1,this.showLeftSummaryCardIcon=!1,this._onDestroy=new l.b,this.currentUserAdmin=!0,this.employeeDetails=[],this.showSummaryScrollIcon=!1,this.showSummaryCard=!1,this.showLeaveHistory=!1,this.orgLeaveYearType=1,this.approvedLeaveData=[],this.compOffLeaveData=[],this.s3BucketConfig={},this.allowedDivision=[],this.daysCompOfConsidered=0,this.flexiHolidayInLeaves=0,this.isCompOffSplitUpAllowed=0,this.isLOPAllowedAnyTime=0,this.pendingRequestsApplicationId=343,this.dropdownflag=!1,this.displayedFilter=0,this.pendingLimit=0,this.getAllLeaveDetails=[],this.disableBtn=!1,this.compOffNeedToAppliedAfterDoj=0,this.sessionData=[{id:1,name:"Full Day (FD)"},{id:2,name:"Forenoon (FN)"},{id:3,name:"Afternoon (AN)"}]}ngOnInit(){return Object(o.c)(this,void 0,void 0,(function*(){this.currentUser=this.authService.getProfile().profile,this.currentUserOid=this.currentUser.oid,yield this.configureUdrfForPendingApprovals(),this.getUIConfig(),this.getEmployeeDetails(),this.getEmployeeWeekOff(),this.getHolidayListOfMonth(),this.getEmployeeLeaveDetails()}))}applyLeave(){return Object(o.c)(this,void 0,void 0,(function*(){this.disableBtn=!0,this.getEmployeeLeaveDetails(),yield this.getApprovedLeaveDetails();let e={associateOid:this.currentUserOid,associateId:this.currentUser.aid,holidayList:this.holidayList,leaveDaysList:this.approvedLeaveData,employeeLeaveDetails:d.default.filter(this.leaveBalanceDetails,{is_req_leave:0}),weekOffDetails:this.weekOffData,employeeDetails:this.employeeDetails,orgLeaveYearType:this.orgLeaveYearType,s3BucketConfig:this.s3BucketConfig,flexiHolidayInLeaves:this.flexiHolidayInLeaves,isLOPAllowedAnyTime:this.isLOPAllowedAnyTime,pendingRequestsFilterConfig:this.pendingRequestsFilterConfig};const{ApplyLeaveComponent:t}=yield Promise.all([n.e(4),n.e(26),n.e(247)]).then(n.bind(null,"Lvio")),i=this.matDialog.open(t,{height:"100%",minWidth:"50%",position:{right:"0px"},data:{modalParams:e}});i.afterClosed().subscribe(e=>{"submit"==e.event&&this.initPendingRequests(),this.disableBtn=!1,this.getEmployeeLeaveDetails(),this.getApprovedLeaveDetails(),this.getComOffLeaveDetails()}),i.backdropClick().subscribe(()=>{this.disableBtn=!1,this.getEmployeeLeaveDetails()})}))}getUIConfig(){this._leaveAppService.uiConfig(this.currentUser.aid,this.currentUser.oid).pipe(Object(r.a)(this._onDestroy)).subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){e.data&&(this.uiConfigData=e.data[0],this.compensationOff=this.uiConfigData.compensation_off,this.applyLeaveBtn=this.uiConfigData.apply_leave,this.howToApplyLeave=this.uiConfigData.how_to_apply_leave,this.leavePolicy=this.uiConfigData.leave_policy,this.showSummary=this.uiConfigData.summary,this.restrictWeekOff=this.uiConfigData.apply_comp_off_config,this.showSummaryCard=this.uiConfigData.summary_card,this.showLeaveHistory=this.uiConfigData.leave_history,this.orgLeaveYearType=this.uiConfigData.org_leave_year_type,this.s3BucketConfig=JSON.parse(this.uiConfigData.s3_bucket_config),this.headerText=this.uiConfigData.headerText,this.allowedDivision=JSON.parse(this.uiConfigData.claim_allowed_division),this.daysCompOfConsidered=this.uiConfigData.days_comp_off_allowed?this.uiConfigData.days_comp_off_allowed:0,this.flexiHolidayInLeaves=this.uiConfigData.flexi_holiday_in_leaves?this.uiConfigData.flexi_holiday_in_leaves:0,this.isCompOffSplitUpAllowed=this.uiConfigData.is_comp_off_split_up_allowed?this.uiConfigData.is_comp_off_split_up_allowed:0,this.isLOPAllowedAnyTime=this.uiConfigData.is_lop_allowed_any_time?this.uiConfigData.is_lop_allowed_any_time:0,this.compOffTypeConfig=this.uiConfigData.comp_off_request_type,this.compOffNeedToAppliedAfterDoj=this.uiConfigData.comp_off_request_can_be_raised_after_doj)})),e=>{this.matDialog.open(c.a,{width:"40%",minHeight:"250px",data:{modalParams:e.error}})})}getLeaveDetails(){0==this.leaveData.length&&(this.initialLoading=!0),this.leaveData.length>0&&(this.pendingScrollApiInProgress=!0),this._leaveAppService.getLeaveDetails(this.currentUserOid,this.pendingRequestsFilterConfig,this.pendingLimit).pipe(Object(r.a)(this._onDestroy)).subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){if("S"==e.messType&&e.data&&e.data.length>0){this.leaveData=this.pendingScrollApiInProgress?this.leaveData.concat(e.data):e.data;for(let e of this.leaveData){let t=e.dates.sort((function(e,t){return new Date(e).valueOf()-new Date(t).valueOf()}));e.uiFormattedDate=s(t[0]).format("DD MMM YYYY")+" - "+s(t[t.length-1]).format("DD MMM YYYY")}this.pendingScrollApiInProgress=!1}else 0==this.leaveData.length&&(this.leaveData=[]),this.pendingScrollApiInProgress=!1;this.initialLoading=!1})),e=>{this.matDialog.open(c.a,{width:"40%",minHeight:"250px",data:{modalParams:e.error}})})}getApprovedLeaveDetails(){return Object(o.c)(this,void 0,void 0,(function*(){yield this.getAllPendingLeaveDetails(),this.approvedLeaveData=[];for(let e of this.getAllLeaveDetails)this.approvedLeaveData.push(e)}))}getComOffLeaveDetails(){return Object(o.c)(this,void 0,void 0,(function*(){this.compOffLeaveData=[],yield this.getAllPendingLeaveDetails();for(let e of this.getAllLeaveDetails)this.compOffLeaveData.push(e)}))}getEmployeeWeekOff(){this._leaveAppService.getEmployeeWeekOff(this.currentUserOid,this.currentUser.aid).pipe(Object(r.a)(this._onDestroy)).subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){"S"==e.messType&&e.data&&e.data.length>0?this.weekOffData=e.data:(this.weekOffData=[],this.toastmessage.showError(e.messText))})),e=>{this.matDialog.open(c.a,{width:"40%",minHeight:"250px",data:{modalParams:e.error}})})}getHolidayListOfMonth(){this._leaveAppService.getHolidayListOfMonth(s().startOf("month").format("YYYY-MM-DD hh:mm"),this.currentUserOid,this.currentUser.aid).pipe(Object(r.a)(this._onDestroy)).subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){this.holidayList="S"==e.messType&&e.data&&e.data.length>0?e.data:[]})),e=>{this.matDialog.open(c.a,{width:"40%",minHeight:"250px",data:{modalParams:e.error}})})}getEmployeeLeaveDetails(){this._leaveAppService.getEmployeeLeaveDetails(this.currentUser.aid,this.currentUserOid).pipe(Object(r.a)(this._onDestroy)).subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){"S"==e.messType&&e.data&&e.data.length>0?(this.leaveBalanceDetails=e.data,this.showSummaryScrollIcon=d.default.filter(this.leaveBalanceDetails,{is_need_to_show_in_summary:1}).length>6,this.bindToSummaryCards(this.leaveBalanceDetails)):(this.leaveBalanceDetails=[],this.toastmessage.showInfo(e.messText,5e3))})),e=>{this.matDialog.open(c.a,{width:"40%",minHeight:"250px",data:{modalParams:e.error}})})}showDetailView(e){return Object(o.c)(this,void 0,void 0,(function*(){let t={leaveData:e,currentUserOid:this.currentUserOid,s3BucketConfig:this.s3BucketConfig};const{MyLeaveDetailComponent:i}=yield Promise.all([n.e(4),n.e(26),n.e(304)]).then(n.bind(null,"PJXu"));this.matDialog.open(i,{height:"100%",width:"752px",position:{right:"0px"},data:{modalParams:t}}).afterClosed().subscribe(e=>{"submit"==e.event&&this.initPendingRequests(),this.getEmployeeLeaveDetails(),this.getApprovedLeaveDetails()})}))}bindToSummaryCards(e){this.summaryCards=[],this.showRightSummaryCardIcon=!0,this.showLeftSummaryCardIcon=!1;let t=d.default.filter(this.leaveBalanceDetails,{is_need_to_show_in_summary:1});for(let n=0;n<(t.length>6?6:t.length);n++)1==t[n].is_need_to_show_in_summary&&this.summaryCards.push({dataType:t[n].leaveName,dataTypeValue:t[n].leaveBalance+(0==t[n].AllocatedLeaveQuota?" Days":"/"+t[n].AllocatedLeaveQuota),tootltipData:0==t[n].AllocatedLeaveQuota?"Leave Balance":"Leave Balance / Leave Quota",dataTypeCode:t[n].leaveType,isVisible:!0,statusColor:t[n].colorCode})}appedFirstCards(){this.summaryCards=[];let e=d.default.filter(this.leaveBalanceDetails,{is_need_to_show_in_summary:1});for(let t=6;t<e.length;t++)e[t].is_need_to_show_in_summary&&this.summaryCards.push({dataType:e[t].leaveName,dataTypeValue:e[t].leaveBalance+(0==e[t].AllocatedLeaveQuota?" Days":"/"+e[t].AllocatedLeaveQuota),tootltipData:0==e[t].AllocatedLeaveQuota?"Leave Balance":"Leave Balance / Leave Quota",dataTypeCode:e[t].leaveType,isVisible:!0,statusColor:e[t].colorCode});this.showRightSummaryCardIcon=!1,this.showLeftSummaryCardIcon=!0}appendLastCards(){this.summaryCards=[];let e=d.default.filter(this.leaveBalanceDetails,{is_need_to_show_in_summary:1});for(let t=0;t<6;t++)e[t].is_need_to_show_in_summary&&this.summaryCards.push({dataType:e[t].leaveName,dataTypeValue:e[t].leaveBalance+(0==e[t].AllocatedLeaveQuota?" Days":"/"+e[t].AllocatedLeaveQuota),dataTypeCode:e[t].leaveType,tootltipData:0==e[t].AllocatedLeaveQuota?"Leave Balance":"Leave Balance / Leave Quota",isVisible:!0,statusColor:e[t].colorCode});this.showRightSummaryCardIcon=!0,this.showLeftSummaryCardIcon=!1}openHowToApplyLeave(){return Object(o.c)(this,void 0,void 0,(function*(){let e={displayText:this.uiConfigData.displayText,videoUrl:this.uiConfigData.videoUrl,headerText:this.uiConfigData.headerText};const{HowToApplyLeaveComponent:t}=yield n.e(283).then(n.bind(null,"PcBA"));this.matDialog.open(t,{height:"100%",width:"752px",position:{right:"0px"},data:{modalParams:e}})}))}openCompensationOff(){return Object(o.c)(this,void 0,void 0,(function*(){this.disableBtn=!0,yield this.getComOffLeaveDetails();let e={associateOid:this.currentUserOid,restrictWeekOff:+this.restrictWeekOff,holidayList:this.holidayList,leaveDaysList:this.compOffLeaveData,compOffDetails:d.default.filter(this.compOffLeaveData,{is_claim:1}),weekOffDetails:this.weekOffData,employeeDetails:this.employeeDetails,orgLeaveYearType:this.orgLeaveYearType,daysCompOfConsidered:this.daysCompOfConsidered,isCompOffSplitUpAllowed:this.isCompOffSplitUpAllowed,pendingRequestsFilterConfig:this.pendingRequestsFilterConfig,compOffTypeConfig:this.compOffTypeConfig,compOffNeedToAppliedAfterDoj:this.compOffNeedToAppliedAfterDoj};const{CompensationOffComponent:t}=yield n.e(261).then(n.bind(null,"BU4U")),i=this.matDialog.open(t,{height:"100%",width:"752px",position:{right:"0px"},data:{modalParams:e}});i.afterClosed().subscribe(e=>{"submit"==e.event&&this.initPendingRequests(),this.disableBtn=!1,this.getEmployeeLeaveDetails(),this.getEmployeeLeaveDetails()}),i.backdropClick().subscribe(()=>{this.disableBtn=!1,this.getEmployeeLeaveDetails()})}))}openLeavePolicy(){return Object(o.c)(this,void 0,void 0,(function*(){let e={associateOid:this.currentUserOid};const{LeavePolicyComponent:t}=yield n.e(300).then(n.bind(null,"hB+J"));this.matDialog.open(t,{height:"100%",width:"752px",position:{right:"0px"},data:{modalParams:e}})}))}getEmployeeDetails(){this._leaveAppService.getEmployeeDetails(this.currentUserOid,this.currentUser.aid).pipe(Object(r.a)(this._onDestroy)).subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){"S"==e.messType&&e.data&&(this.employeeDetails=e.data,this.currentUserAdmin=1==e.data[0].is_org_head)})),e=>{this.matDialog.open(c.a,{width:"40%",minHeight:"250px",data:{modalParams:e.error}})})}displayCompoffBasedOnE360Divison(){return Object(o.c)(this,void 0,void 0,(function*(){if(null!=this.allowedDivision&&this.allowedDivision.length>0)for(let e of this.allowedDivision)if(e.division==this.employeeDetails[0].division&&e.sub_division==this.employeeDetails[0].sub_division)return!0;return!1}))}openPendingRequestsUdrfModal(){return Object(o.c)(this,void 0,void 0,(function*(){this.dropdownflag=!1;const{UdrfModalComponent:e}=yield Promise.all([n.e(4),n.e(998)]).then(n.bind(null,"UIsE"));this.matDialog.open(e,{minWidth:"100%",height:"84%",position:{top:"0px",left:"77px"},disableClose:!0})}))}configureUdrfForPendingApprovals(){return Object(o.c)(this,void 0,void 0,(function*(){this._udrfService.udrfData.applicationId=this.pendingRequestsApplicationId,this._udrfService.udrfData.isItemDataLoading=!0,this._udrfService.udrfUiData.showItemDataCount=!1,this._udrfService.udrfUiData.showSearchBar=!1,this._udrfService.udrfUiData.showActionButtons=!0,this._udrfService.udrfUiData.showUdrfModalButton=!1,this._udrfService.udrfUiData.showColumnConfigButton=!1,this._udrfService.udrfUiData.showSettingsModalButton=!1,this._udrfService.udrfUiData.showNewReleasesButton=!1,this._udrfService.udrfUiData.showReportDownloadButton=!1,this._udrfService.udrfUiData.isReportDownloading=!1,this._udrfService.udrfUiData.itemHasOpenInNewTab=!1,this._udrfService.udrfUiData.horizontalScroll=!1,this._udrfService.udrfUiData.itemHasQuickCta=!1,this._udrfService.udrfUiData.collapseAll=!1,this._udrfService.udrfUiData.showCollapseButton=!1,this._udrfService.udrfUiData.countForOnlyThisReport=!1,this._udrfService.udrfUiData.toggleChecked=!1,this._udrfService.udrfUiData.countFlag=!1,this._udrfService.udrfUiData.isMultipleView=!1,this._udrfService.udrfUiData.itemHasDownloadButton=!1,this._udrfService.udrfUiData.emailPluginVisible=!1,this._udrfService.udrfUiData.itemHasDownloadButton=!1,this._udrfService.udrfUiData.itemHasAttachFileButton=!1,this._udrfService.udrfUiData.itemHasComments=!1,this._udrfService.udrfUiData.isMoreOptionsNeeded=!1,this._udrfService.udrfUiData.completeProfileBtn=!1,this._udrfService.udrfUiData.searchPlaceholder="",this._udrfService.getAppUdrfConfig(this.pendingRequestsApplicationId,yield this.initPendingRequests.bind(this))}))}initPendingRequests(){return Object(o.c)(this,void 0,void 0,(function*(){this.pendingRequestsFilterConfig={mainFilterArray:this._udrfService.udrfData.mainFilterArray,searchTableDetails:this._udrfService.udrfData.searchTableDetails,txTableDetails:this._udrfService.udrfData.txTableDetails},this.leaveData=[],this.pendingLimit=0,yield this.getLeaveDetails()}))}onClear(){return Object(o.c)(this,void 0,void 0,(function*(){this.dropdownflag=!1,this.displayedFilter=0,yield this._udrfService.udrfFunctions.clearSearchAndConfig(),yield this.initPendingRequests()}))}clearonefilter(e,t){return Object(o.c)(this,void 0,void 0,(function*(){this._udrfService.udrfFunctions.clearItemConfigApply(e,1),this._udrfService.udrfData.appliedFilterTypeArray.splice(t,1),this._udrfService.udrfData.mainFilterArray.splice(t,1),this.displayedFilter=0,this.dropdownflag=!1}))}viewdroplist(e){this.dropdownflag=!0,this.selecteddropdown=e}closedroplist(){this.dropdownflag=!1,this.selecteddropdown=-1}checkboxvalue(e,t,n,i,a){return Object(o.c)(this,void 0,void 0,(function*(){this._udrfService.udrfData.mainFilterArray[t].checkboxValues[n].isCheckboxSelected=e,e?(this._udrfService.udrfData.mainFilterArray[t].multiOptionSelectSearchValues.push(i),this._udrfService.udrfData.mainFilterArray[t].isIdBased&&this._udrfService.udrfData.mainFilterArray[t].multiOptionSelectSearchValuesWithId.push(a)):this._udrfService.udrfData.mainFilterArray[t].multiOptionSelectSearchValues.forEach((e,n)=>{e===i&&(this._udrfService.udrfData.mainFilterArray[t].multiOptionSelectSearchValues.splice(n,1),this._udrfService.udrfData.mainFilterArray[t].isIdBased&&this._udrfService.udrfData.mainFilterArray[t].multiOptionSelectSearchValuesWithId.splice(n,1))}),0==this._udrfService.udrfData.mainFilterArray[t].multiOptionSelectSearchValues.length?(this.dropdownflag=!1,this.displayedFilter=0,this.clearonefilter(this._udrfService.udrfData.mainFilterArray[t],t)):yield this.initPendingRequests()}))}goToPreviousFilter(){this.dropdownflag=!1,0==this.displayedFilter?this.displayedFilter=this._udrfService.udrfData.mainFilterArray.length-1:this.displayedFilter--}goToNextFilter(){this.dropdownflag=!1,this.displayedFilter==this._udrfService.udrfData.mainFilterArray.length-1?this.displayedFilter=0:this.displayedFilter++}getPendingApprovalsOnScroll(){return Object(o.c)(this,void 0,void 0,(function*(){this.pendingScrollApiInProgress||(this.pendingLimit+=15,yield this.getLeaveDetails())}))}getAllPendingLeaveDetails(){return Object(o.c)(this,void 0,void 0,(function*(){yield this._leaveAppService.getAllPendingLeaveDetails(this.currentUserOid).then(e=>Object(o.c)(this,void 0,void 0,(function*(){if("S"==e.messType&&e.data&&e.data.length>0){this.getAllLeaveDetails=e.data;for(let e of this.getAllLeaveDetails){let t=e.dates.sort((function(e,t){return new Date(e).valueOf()-new Date(t).valueOf()}));e.uiFormattedDate=s(t[0]).format("DD MMM YYYY")+" - "+s(t[t.length-1]).format("DD MMM YYYY")}}else this.getAllLeaveDetails=[]})),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Something Went Wrong Kindly Try After Some Time",e&&e.params?e.params:e&&e.error?e.error.params:{})})}))}openWorkFromHome(){var e;return Object(o.c)(this,void 0,void 0,(function*(){this.disableBtn=!0;let t={associateOid:this.currentUserOid,restrictWeekOff:+this.restrictWeekOff,holidayList:this.holidayList,leaveDaysList:[],wfhDetails:d.default.filter(this.compOffLeaveData,{is_claim:1}),weekOffDetails:this.weekOffData,employeeDetails:this.employeeDetails,orgLeaveYearType:this.orgLeaveYearType,daysCompOfConsidered:this.daysCompOfConsidered,iswfhSplitUpAllowed:this.isCompOffSplitUpAllowed,pendingRequestsFilterConfig:this.pendingRequestsFilterConfig,wfhTypeConfig:this.sessionData,wfhNeedToAppliedAfterDoj:this.compOffNeedToAppliedAfterDoj,leaveBalanceData:(null===(e=d.default.filter(this.leaveBalanceDetails,{leaveId:55}))||void 0===e?void 0:e.length)>0?d.default.filter(this.leaveBalanceDetails,{leaveId:55}):[]};const{ApplyWfhComponent:i}=yield n.e(248).then(n.bind(null,"OSeZ")),a=this.matDialog.open(i,{height:"100%",width:"752px",position:{right:"0px"},data:{modalParams:t}});a.afterClosed().subscribe(e=>{"submit"==e.event&&this.initPendingRequests(),this.disableBtn=!1,this.getEmployeeLeaveDetails()}),a.backdropClick().subscribe(()=>{this.disableBtn=!1,this.getEmployeeLeaveDetails()})}))}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}}return e.\u0275fac=function(t){return new(t||e)(g["\u0275\u0275directiveInject"](m.b),g["\u0275\u0275directiveInject"](f.a),g["\u0275\u0275directiveInject"](u.a),g["\u0275\u0275directiveInject"](v.a),g["\u0275\u0275directiveInject"](x.a),g["\u0275\u0275directiveInject"](y.a),g["\u0275\u0275directiveInject"](C.a))},e.\u0275cmp=g["\u0275\u0275defineComponent"]({type:e,selectors:[["app-leave-landing-page"]],features:[g["\u0275\u0275ProvidersFeature"]([{provide:h.c,useClass:p.c,deps:[h.f,p.a]},{provide:h.e,useValue:ue}])],decls:15,vars:9,consts:[[1,"container-fluild","pl-2","pr-2","leave-landing-styles"],[3,"ngClass"],[1,"row"],[1,"col-7","pl-0",2,"display","flex"],["style","display: flex; min-width: 100px; height: 40px; padding-left: 10px;",4,"ngIf"],[2,"display","flex","min-width","100px","height","40px","padding-left","10px"],["matTooltip","Apply Work From Home",1,"tag-btn",3,"disabled","click"],["style","min-width: 120px; height: 40px;",4,"ngIf"],["style","min-width: 170px; height: 40px;",4,"ngIf"],["style","min-width: 190px; height: 40px;",4,"ngIf"],[1,"col-3"],["class","col-2 pl-4",4,"ngIf"],["class","col-12 row pl-0 pr-2 pt-2",4,"ngIf"],["class","row mt-2 tempCheck","style","margin-left: 12px !important; margin-right: 8px !important;",4,"ngIf"],["matTooltip","Claim Compensation Off",1,"tag-btn",3,"disabled","click"],[2,"min-width","120px","height","40px"],["mat-icon-button","","matTooltip","Leave Policy",1,"view-button-inactive",3,"click"],[1,"iconButton"],[1,"valueGrey14ForIcon"],[2,"min-width","170px","height","40px"],["mat-icon-button","","matTooltip","How To Apply Leave",1,"view-button-inactive",3,"click"],[2,"min-width","190px","height","40px"],["mat-icon-button","","matTooltip","Summary",1,"view-button-inactive"],[1,"col-2","pl-4"],[2,"min-width","100px","padding-left","70px"],["mat-flat-button","",1,"leave-btn",3,"disabled","click"],[1,"col-12","row","pl-0","pr-2","pt-2"],["class","col-2 pb-0 pr-0 pl-2 pt-0",4,"ngFor","ngForOf"],[1,"col-2","pb-0","pr-0","pl-2","pt-0"],["style","position: absolute; right: 99%; top: 35%; cursor: pointer;",4,"ngIf"],["class","card data-type-card slide-in-top",3,"ngStyle",4,"ngIf"],["style","position: absolute; top: 35%; left: 100%; cursor: pointer;",4,"ngIf"],[2,"position","absolute","right","99%","top","35%","cursor","pointer"],["class","matIcon",3,"click",4,"ngIf"],[1,"matIcon",3,"click"],[1,"card","data-type-card","slide-in-top",3,"ngStyle"],["class","card-body p-1 cp","content-type","template","this.displayShadowEffect","true;",3,"matTooltip",4,"ngIf"],["content-type","template","this.displayShadowEffect","true;",1,"card-body","p-1","cp",3,"matTooltip"],[1,"row","d-flex","justify-content-left","pt-2","pl-2"],["class","pl-0 valueGrey14",4,"ngIf"],["class","valueGrey14",4,"ngIf"],[1,"headingBold"],["class","status-circular  justify-content-right",3,"ngStyle",4,"ngIf"],[1,"pl-0","valueGrey14"],[1,"valueGrey14"],[1,"status-circular","justify-content-right",3,"ngStyle"],[2,"position","absolute","top","35%","left","100%","cursor","pointer"],[1,"row","mt-2","tempCheck",2,"margin-left","12px !important","margin-right","8px !important"],[1,"col-12",2,"background","#fafafa","height","45px"],[2,"width","100px","float","left","display","flex","flex-direction","row","justify-content","center","align-items","center"],[1,"headerName"],[1,"filtericon",2,"width","100px","float","left","margin-top","10px","display","flex","flex-direction","row","justify-content","center","cursor","pointer","align-items","center",3,"click"],[2,"margin-top","2px","font-size","20px"],[2,"font-weight","700","margin-top","3px"],["style","margin-right: 10px; display: flex; flex-direction: row; margin-top: 3px;",4,"ngIf"],[1,"col-12","pl-0","pt-2",2,"height","35px","padding-top","12px"],[1,"col-1","subHeaderName"],[1,"col-2","subHeaderName"],[1,"col-2","pl-2","subHeaderName"],[1,"col-1","pr-0","subHeaderName"],[1,"col-1","pl-2","subHeaderName"],[1,"col-12","pl-0","pt-2",2,"height","280px","padding-top","12px"],[1,"approvals-spinner",2,"padding-top","100px",3,"hidden"],["diameter","40",1,"main-spinner"],[3,"hidden"],["infinite-scroll","","class","pl-2 itemData",3,"infiniteScrollDistance","scrollWindow","scrolled",4,"ngIf"],["class","col-12 itemData","style","padding-top: 40px;",4,"ngIf"],[2,"margin-right","10px","display","flex","flex-direction","row","margin-top","3px"],[2,"margin-right","10px","margin-top","6px","cursor","pointer",3,"click"],[4,"ngFor","ngForOf"],[2,"margin-left","10px","margin-top","6px","cursor","pointer",3,"click"],["style","margin-right: 10px; margin-top: 3px; display: flex; flex-direction: row",4,"ngIf"],["class","searchField","style","display: flex !important",4,"ngIf"],[1,"searchField",2,"display","flex !important"],[1,"searchboxes","tooltip",2,"display","contents"],[1,"searchtitle","titlemargin","filterfield",3,"matTooltip"],[1,"searchtitle","boxstyle"],[1,"filterval",3,"matTooltip"],[1,"clearonefiltericn",3,"click"],["class","dropdownfilter",3,"click",4,"ngIf"],[1,"tooltiptext","dropdownborder",3,"ngClass"],[1,"dropdownfilter",3,"click"],[2,"display","inline-flex"],[1,"dropdata"],[1,"example-margin",3,"checked","change"],[2,"margin-right","10px","margin-top","3px","display","flex","flex-direction","row"],[2,"padding-top","3px","cursor","pointer",3,"click"],["infinite-scroll","",1,"pl-2","itemData",3,"infiniteScrollDistance","scrollWindow","scrolled"],[1,"approvals-spinner",3,"hidden"],[1,"col-1","pl-2","itemName"],[1,"col-2","pl-2","itemName"],[1,"col-2","itemName",2,"padding-left","10px"],[1,"col-2","pl-2","itemName",3,"ngStyle","click"],[1,"col-1","pl-3","itemName"],[1,"col-1","itemName",2,"padding-left","13px"],[1,"col-1","pl-3","itemName","pb-2"],[1,"status-btn",3,"ngStyle"],[4,"ngIf"],[2,"padding-bottom","10px"],["content-type","template","max-width","300","placement","top","imgWidth","23px","imgHeight","23px",2,"margin","2px",3,"tooltip","id"],["placement","top"],["approverTooltip",""],[1,"row","tooltip-text"],["class","col-1 pl-4 itemName","style","text-decoration: underline; cursor: pointer;",3,"click",4,"ngIf"],[1,"col-1","pl-4","itemName",2,"text-decoration","underline","cursor","pointer",3,"click"],[1,"col-12","itemData",2,"padding-top","40px"],[2,"display","flex","justify-content","center"],["src","https://assets.kebs.app/images/no_milestones_v3.png","width","250","height","150"],[1,"noLeave"],[1,"subHead"],["style","display: flex; justify-content: center",4,"ngIf"],["style","display: flex; justify-content: center","class","headerName",4,"ngIf"],[1,"applyLeaveBtn1",3,"click"],[1,"headerName",2,"display","flex","justify-content","center"]],template:function(e,t){1&e&&(g["\u0275\u0275elementStart"](0,"div",0),g["\u0275\u0275elementStart"](1,"div",1),g["\u0275\u0275elementStart"](2,"div",2),g["\u0275\u0275elementStart"](3,"div",3),g["\u0275\u0275template"](4,k,3,1,"div",4),g["\u0275\u0275elementStart"](5,"div",5),g["\u0275\u0275elementStart"](6,"button",6),g["\u0275\u0275listener"]("click",(function(){return t.openWorkFromHome()})),g["\u0275\u0275text"](7," Apply WFH "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275template"](8,L,6,0,"div",7),g["\u0275\u0275template"](9,A,6,0,"div",8),g["\u0275\u0275template"](10,T,6,0,"div",9),g["\u0275\u0275elementEnd"](),g["\u0275\u0275element"](11,"div",10),g["\u0275\u0275template"](12,F,4,1,"div",11),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275template"](13,Q,2,1,"div",12),g["\u0275\u0275template"](14,fe,38,5,"div",13),g["\u0275\u0275elementEnd"]()),2&e&&(g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngClass",t.currentUserAdmin?"col-12 pl-0 pr-2 pt-5":"col-12 pl-0 pr-2"),g["\u0275\u0275advance"](3),g["\u0275\u0275property"]("ngIf",t.compensationOff),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("disabled",t.disableBtn),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("ngIf",t.leavePolicy),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",t.howToApplyLeave),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",t.showSummary),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("ngIf",t.applyLeaveBtn),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",t.showSummaryCard),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",t.showLeaveHistory))},directives:[i.NgClass,i.NgIf,b.a,_.a,O.a,i.NgForOf,i.NgStyle,w.a,S.c,D.a,I.a,P.a,E.a,M.a],styles:[".leave-landing-styles[_ngcontent-%COMP%]   .textValue[_ngcontent-%COMP%]{font-size:12px;color:#45546e}.leave-landing-styles[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]{margin-top:5px!important;line-height:8px;width:150px;height:50px;margin-right:10px!important;border-radius:10px}.leave-landing-styles[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#45546e;font-size:18px;height:25px}.leave-landing-styles[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]   .iconButtonWarn[_ngcontent-%COMP%], .leave-landing-styles[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]   .iconButtonWarnAnim[_ngcontent-%COMP%]{color:#cf0001;font-size:18px}.leave-landing-styles[_ngcontent-%COMP%]   .view-button-active[_ngcontent-%COMP%]{margin-top:5px!important;line-height:8px;width:93px;height:50px;border:2px solid #45546e;font-size:12px;border-radius:5px}.leave-landing-styles[_ngcontent-%COMP%]   .tag-btn[_ngcontent-%COMP%]{font-size:11px;font-weight:700;cursor:pointer;z-index:5;position:relative;padding:10px;margin:0;line-height:5px;transition:all .2s ease-in-out;border:1px solid #45546e;box-shadow:none;height:25px;border-radius:5px;margin-top:17px!important}.leave-landing-styles[_ngcontent-%COMP%]   .leave-btn[_ngcontent-%COMP%]{width:100px;height:37px;color:#fff;font-size:13px;background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%);border-radius:6px;margin-top:15px!important}.leave-landing-styles[_ngcontent-%COMP%]   .valueGrey14ForIcon[_ngcontent-%COMP%]{color:#45546e;font-size:12px;font-weight:400;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline;font-family:Roboto;padding:10px}.leave-landing-styles[_ngcontent-%COMP%]   .compOff[_ngcontent-%COMP%]{display:flex}.leave-landing-styles[_ngcontent-%COMP%]   .btn-small[_ngcontent-%COMP%]{width:15px;height:15px;font-weight:700;font-size:10px;border:2px solid #45546e;border-radius:5px;color:#45546e;padding:14px 28px;cursor:pointer}.leave-landing-styles[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{position:absolute;width:1260px;height:360px;margin:10px;margin-top:30px!important;background:#fff;box-shadow:0 2px 1px rgba(0,0,0,.12),0 4px 6px rgba(0,0,0,.12);border-radius:4px;overflow-y:auto}.leave-landing-styles[_ngcontent-%COMP%]   .headerName[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;font-weight:800;font-size:14px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;color:#45546e;padding-top:15px}.leave-landing-styles[_ngcontent-%COMP%]   .subHeaderName[_ngcontent-%COMP%]{color:#b9c0ca;font-size:11px;text-transform:uppercase}.leave-landing-styles[_ngcontent-%COMP%]   .itemName[_ngcontent-%COMP%], .leave-landing-styles[_ngcontent-%COMP%]   .subHeaderName[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;font-weight:400;line-height:16px;letter-spacing:.02em}.leave-landing-styles[_ngcontent-%COMP%]   .itemName[_ngcontent-%COMP%]{color:#45546e;font-size:12px;text-transform:capitalize;white-space:nowrap}.leave-landing-styles[_ngcontent-%COMP%]   .noLeave[_ngcontent-%COMP%]{color:#45546e;font-weight:700;font-size:14px;text-align:center;text-transform:capitalize;font-family:Roboto;font-style:normal;padding-top:10px}.leave-landing-styles[_ngcontent-%COMP%]   .applyLeaveBtn1[_ngcontent-%COMP%]{padding:12px 16px;border:none;position:absolute;width:171px;height:40px;background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%);border-radius:8px;color:#fff;text-align:center}.leave-landing-styles[_ngcontent-%COMP%]   .subHead[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;font-weight:400;font-size:14px;line-height:16px;text-align:center;color:#8b95a5;padding:5px}.leave-landing-styles[_ngcontent-%COMP%]   .data-type-card[_ngcontent-%COMP%]{min-height:15vh;transition:all .3s linear}.leave-landing-styles[_ngcontent-%COMP%]   .status-btn[_ngcontent-%COMP%]{color:#fff;border-radius:30px;width:65px;height:25px;border:none}.leave-landing-styles[_ngcontent-%COMP%]   .headingBold[_ngcontent-%COMP%]{color:#1a1a1a;font-weight:600;font-size:22px}.leave-landing-styles[_ngcontent-%COMP%]   .valueGrey14[_ngcontent-%COMP%]{color:#526179;font-size:12px;font-weight:400;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline;font-family:Roboto}.leave-landing-styles[_ngcontent-%COMP%]   .matIcon[_ngcontent-%COMP%]{font-size:15px;width:10px;height:10px;color:#45546e}.leave-landing-styles[_ngcontent-%COMP%]   .tempCheck[_ngcontent-%COMP%]{background:#fff;box-shadow:0 2px 1px rgba(0,0,0,.12),0 4px 6px rgba(0,0,0,.12);border-radius:4px}.leave-landing-styles[_ngcontent-%COMP%]   .itemData[_ngcontent-%COMP%]{height:280px;overflow-y:auto}.leave-landing-styles[_ngcontent-%COMP%]   .clearbtn[_ngcontent-%COMP%]{font-weight:600;font-size:12px;line-height:16px;height:34px;border-radius:4px;border:1px solid #ee4961;background-color:rgba(242,212,205,.6784313725490196);color:#ee4961;flex-direction:row}.leave-landing-styles[_ngcontent-%COMP%]   .searchField[_ngcontent-%COMP%]{display:inline-block;border:thin solid #dadce2;border-radius:4px;height:36px;margin-bottom:8px}.leave-landing-styles[_ngcontent-%COMP%]   .searchboxes[_ngcontent-%COMP%]{display:flex;align-items:center}.leave-landing-styles[_ngcontent-%COMP%]   .titlemargin[_ngcontent-%COMP%]{margin-left:0!important;margin-right:0!important}.leave-landing-styles[_ngcontent-%COMP%]   .searchtitle[_ngcontent-%COMP%]{font-weight:400;font-size:12px;line-height:16px;margin:auto 5px;padding:2px 10px}.leave-landing-styles[_ngcontent-%COMP%]   .clearonefiltericn[_ngcontent-%COMP%], .leave-landing-styles[_ngcontent-%COMP%]   .dropdownfilter[_ngcontent-%COMP%]{font-size:13px;display:inline-flex;justify-content:center;align-items:center;cursor:pointer}.leave-landing-styles[_ngcontent-%COMP%]   .dropdownfilter[_ngcontent-%COMP%]{margin-top:5px}.leave-landing-styles[_ngcontent-%COMP%]   .boxstyle[_ngcontent-%COMP%]{background-color:rgba(242,212,205,.6784313725490196);height:1.5rem;display:inline-flex;align-items:center;justify-content:center;margin-right:0;color:#526179}.leave-landing-styles[_ngcontent-%COMP%]   .filterfield[_ngcontent-%COMP%], .leave-landing-styles[_ngcontent-%COMP%]   .filterval[_ngcontent-%COMP%]{width:100px;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.leave-landing-styles[_ngcontent-%COMP%]   .searchFieldtxt[_ngcontent-%COMP%]{outline:none;border:none;font-weight:400;font-size:12px;line-height:16px}.leave-landing-styles[_ngcontent-%COMP%]   .positionFieldlable[_ngcontent-%COMP%]{margin-top:19px;margin-left:4px;color:#a8acb2;font-weight:400;font-size:12px;line-height:16px}.leave-landing-styles[_ngcontent-%COMP%]   .positionField[_ngcontent-%COMP%]{display:inline-block;margin-left:10px;padding-top:5px;width:147px;margin-right:20px}.leave-landing-styles[_ngcontent-%COMP%]   .positionFieldtxt[_ngcontent-%COMP%]{border-radius:4px;height:40px;width:200px;border-color:#b9c0ca}.leave-landing-styles[_ngcontent-%COMP%]   .tooltip[_ngcontent-%COMP%]{position:relative;display:inline-block}.leave-landing-styles[_ngcontent-%COMP%]   .droplistvisible[_ngcontent-%COMP%]{visibility:visible!important}.leave-landing-styles[_ngcontent-%COMP%]   .droplisthidden[_ngcontent-%COMP%]{visibility:hidden!important}.leave-landing-styles[_ngcontent-%COMP%]   .dropdata[_ngcontent-%COMP%]{font-size:12px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;color:#111434;width:175px;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.leave-landing-styles[_ngcontent-%COMP%]   .example-margin[_ngcontent-%COMP%]{display:flex;right:10px;position:absolute}.leave-landing-styles[_ngcontent-%COMP%]   .dropdownborder[_ngcontent-%COMP%]{z-index:1;width:230px;border:1px solid #d3d3d3;border-radius:4px;margin-top:35px;padding:10px!important}.leave-landing-styles[_ngcontent-%COMP%]   .tooltip[_ngcontent-%COMP%]   .tooltiptext[_ngcontent-%COMP%]{visibility:hidden;background-color:#fff;border-radius:6px;padding:7px 5px 5px;width:250px;height:200px;position:absolute;z-index:2;border-radius:4px;overflow-y:scroll;overflow-x:hidden}.leave-landing-styles[_ngcontent-%COMP%]   .tooltip[_ngcontent-%COMP%]:active   .tooltiptext[_ngcontent-%COMP%]{visibility:visible}.leave-landing-styles[_ngcontent-%COMP%]   .filtericon[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;font-weight:500;font-size:14px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;color:#45546e}.leave-landing-styles[_ngcontent-%COMP%]   .approvals-spinner[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;height:var(--dynamicHeight)}"]}),e})()}];let xe=(()=>{class e{}return e.\u0275mod=g["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=g["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[a.k.forChild(ve)],a.k]}),e})();var ye=n("kmnG"),Ce=n("STbY"),be=n("qFsG"),_e=n("3Pt+"),Oe=n("Xi0T"),we=n("iadO"),Se=n("wZkO"),De=n("jaxi"),Ie=n("7pIB"),Pe=n("pA3K"),Ee=n("w4ga"),Me=n("UXJo");let ke=(()=>{class e{}return e.\u0275mod=g["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=g["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,xe,ye.e,O.b,be.c,Ce.e,_e.p,_e.E,_.b,b.b,D.d,S.b,M.b,P.b,Oe.a,m.g,we.h,w.b,Ie.c,Se.g,Pe.a,Ee.b,De.c,I.b,Me.c]]}),e})()},Idmh:function(e,t,n){"use strict";n.d(t,"a",(function(){return u}));var i=n("mrSG"),a=n("XNiG"),o=n("0IaG"),r=n("1G5W"),l=n("fXoL"),s=n("xYuU"),d=n("XXEo"),c=n("NFeN"),p=n("ofXK"),h=n("3Pt+");function g(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",10),l["\u0275\u0275listener"]("click",(function(){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"]().opendetails()})),l["\u0275\u0275text"](1,"See Details"),l["\u0275\u0275elementEnd"]()}}function m(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div"),l["\u0275\u0275elementStart"](1,"div"),l["\u0275\u0275elementStart"](2,"textarea",11),l["\u0275\u0275listener"]("ngModelChange",(function(t){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"]().value=t})),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](3,"div",12),l["\u0275\u0275listener"]("click",(function(){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"]().hidedetails()})),l["\u0275\u0275text"](4,"Hide Details"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&e){const e=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngModel",e.value)}}function f(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"button",13),l["\u0275\u0275listener"]("click",(function(){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"]().openHelpdesk()})),l["\u0275\u0275text"](1,"Create Ticket"),l["\u0275\u0275elementEnd"]()}}let u=(()=>{class e{constructor(e,t,n,i,o){this.dialogRef=e,this._leaveAppService=t,this.authService=n,this.matDialog=i,this.errData=o,this.showdetails=!1,this.moreinfo=!0,this.hide=!1,this.createTicket=!1,this._onDestroy=new a.b}ngOnInit(){return Object(i.c)(this,void 0,void 0,(function*(){this.modalParams=this.errData.modalParams,this.modalParams.code=this.modalParams.code?this.modalParams.code:null,this.currentUser=this.authService.getProfile().profile,this.currentUserOid=this.currentUser.oid,null!=this.modalParams.code&&"LEAVE APP ERROR"!=this.modalParams.code&&"LEAVEAPP 01"!=this.modalParams.code&&(this.createTicket=!0,yield this.getUIConfig())}))}closePopUp(){this.dialogRef.close()}opendetails(){this.showdetails=!0,this.modalParams=this.errData.modalParams,this.value="Error Code: "+this.modalParams.code+"\n Prams:"+this.modalParams.params.error.stack}hidedetails(){this.showdetails=!1,this.hide=!0}getUIConfig(){return Object(i.c)(this,void 0,void 0,(function*(){this._leaveAppService.uiConfig(this.currentUser.aid,this.currentUser.oid).pipe(Object(r.a)(this._onDestroy)).subscribe(e=>Object(i.c)(this,void 0,void 0,(function*(){e.data&&(this.uiConfigData=e.data[0],this.url=this.uiConfigData.helpdesk_url?this.uiConfigData.helpdesk_url:null)})),e=>{})}))}openHelpdesk(){window.open(this.url,"_blank")}}return e.\u0275fac=function(t){return new(t||e)(l["\u0275\u0275directiveInject"](o.h),l["\u0275\u0275directiveInject"](s.a),l["\u0275\u0275directiveInject"](d.a),l["\u0275\u0275directiveInject"](o.b),l["\u0275\u0275directiveInject"](o.a))},e.\u0275cmp=l["\u0275\u0275defineComponent"]({type:e,selectors:[["app-error-popup"]],decls:14,vars:3,consts:[[1,"header"],[2,"padding-top","3px"],["mat-dialog-tile",""],[2,"padding-left","25px"],[2,"padding-left","25px","padding-bottom","10px"],["style","cursor: pointer; color: #257a97",3,"click",4,"ngIf"],[4,"ngIf"],[2,"padding-left","25px","padding-bottom","20px"],[1,"apply-cancel",3,"click"],["class","create-ticket","style","width: 100px; margin-left: 10px;",3,"click",4,"ngIf"],[2,"cursor","pointer","color","#257a97",3,"click"],["disabled","",2,"width","500px","padding-top","10px","padding-bottom","10px","min-height","140px","max-height","300px",3,"ngModel","ngModelChange"],[2,"cursor","pointer","color","red",3,"click"],[1,"create-ticket",2,"width","100px","margin-left","10px",3,"click"]],template:function(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"div",0),l["\u0275\u0275elementStart"](1,"mat-icon",1),l["\u0275\u0275text"](2,"report_problem"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](3,"h1",2),l["\u0275\u0275text"](4,"Report Problem"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](5,"p",3),l["\u0275\u0275text"](6,"An unexpected error occurred. Kindly try after sometime or create a ticket"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](7,"div",4),l["\u0275\u0275template"](8,g,2,0,"div",5),l["\u0275\u0275template"](9,m,5,1,"div",6),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](10,"div",7),l["\u0275\u0275elementStart"](11,"button",8),l["\u0275\u0275listener"]("click",(function(){return t.closePopUp()})),l["\u0275\u0275text"](12,"Close"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](13,f,2,0,"button",9),l["\u0275\u0275elementEnd"]()),2&e&&(l["\u0275\u0275advance"](8),l["\u0275\u0275property"]("ngIf",!t.showdetails),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",t.showdetails),l["\u0275\u0275advance"](4),l["\u0275\u0275property"]("ngIf",t.createTicket))},directives:[c.a,p.NgIf,h.e,h.v,h.y],styles:[".apply-cancel[_ngcontent-%COMP%]{margin-right:15px;width:80px;height:40px;border-radius:5px;color:#45546e;border:1px solid}.apply-cancel[_ngcontent-%COMP%], h1[_ngcontent-%COMP%]{font-weight:700;font-family:Roboto}h1[_ngcontent-%COMP%]{padding-left:10px}p[_ngcontent-%COMP%]{font-family:Roboto}.create-ticket[_ngcontent-%COMP%]{margin-right:15px;width:80px;height:40px;border-radius:5px;color:#fff;background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%);border:1px solid}.create-ticket[_ngcontent-%COMP%], .header[_ngcontent-%COMP%]{font-weight:700;font-family:Roboto}.header[_ngcontent-%COMP%]{display:flex;padding-left:25px;padding-top:50px}"]}),e})()},cHjg:function(e,t,n){"use strict";n.r(t),n.d(t,"LeaveAppModule",(function(){return P}));var i=n("ofXK"),a=n("wZkO"),o=n("NFeN"),r=n("STbY"),l=n("qFsG"),s=n("bTqV"),d=n("Qu3c"),c=n("0IaG"),p=n("3Pt+"),h=n("tyNb"),g=n("mrSG"),m=n("XNiG"),f=n("Idmh"),u=n("fXoL"),v=n("xYuU"),x=n("XXEo"),y=n("BVzC");function C(e,t){if(1&e&&(u["\u0275\u0275elementStart"](0,"a",4,5),u["\u0275\u0275text"](2),u["\u0275\u0275elementEnd"]()),2&e){const e=u["\u0275\u0275reference"](1),t=u["\u0275\u0275nextContext"]().$implicit;u["\u0275\u0275property"]("routerLink",t.path)("active",e.isActive),u["\u0275\u0275advance"](2),u["\u0275\u0275textInterpolate1"](" ",t.label," ")}}function b(e,t){if(1&e&&(u["\u0275\u0275elementStart"](0,"div"),u["\u0275\u0275template"](1,C,3,3,"a",3),u["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf",e.toDisplay)}}function _(e,t){if(1&e&&(u["\u0275\u0275elementStart"](0,"div"),u["\u0275\u0275elementStart"](1,"nav",1),u["\u0275\u0275template"](2,b,2,1,"div",2),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()),2&e){const e=u["\u0275\u0275nextContext"](2);u["\u0275\u0275advance"](2),u["\u0275\u0275property"]("ngForOf",e.leaveTabLinks)}}function O(e,t){if(1&e&&(u["\u0275\u0275elementStart"](0,"div"),u["\u0275\u0275template"](1,_,3,1,"div",0),u["\u0275\u0275elementEnd"]()),2&e){const e=u["\u0275\u0275nextContext"]();u["\u0275\u0275advance"](1),u["\u0275\u0275property"]("ngIf",e.currentUserIsAdmin||e.currentUserIsLeaveAmin)}}const w=[{path:"",component:(()=>{class e{constructor(e,t,n,i){this._leaveAppService=e,this.authService=t,this.errorService=n,this.matDialog=i,this.currentUserIsAdmin=!1,this.shouldTabBeVisible=!0,this._onDestroy=new m.b,this.currentUserIsLeaveAmin=!1,this.leaveTabLinks=[{label:"My Leaves",path:"leave",toDisplay:!0},{label:"My Approvals",path:"approvals",toDisplay:!0}],this._leaveAppService.shouldTabBeVisibleSub.subscribe(e=>{this.shouldTabBeVisible=1!=e})}ngOnInit(){this.currentUser=this.authService.getProfile().profile,this.currentUserOid=this.currentUser.oid,this.checkIfEmployeeIsApprover()}checkIfEmployeeIsApprover(){this._leaveAppService.checkIfEmployeeIsApprover(this.currentUserOid).subscribe(e=>Object(g.c)(this,void 0,void 0,(function*(){"S"==e.messType&&e.data?(this.currentUserIsAdmin=e.data,this.currentUserIsLeaveAmin=e.leaveAdmin,this._leaveAppService.currentUserIsAnAdmin=e.data):(this.currentUserIsAdmin=!1,this.currentUserIsLeaveAmin=!1,this._leaveAppService.currentUserIsAnAdmin=!1)})),e=>{this.matDialog.open(f.a,{width:"40%",minHeight:"250px",data:{modalParams:e.error}})})}}return e.\u0275fac=function(t){return new(t||e)(u["\u0275\u0275directiveInject"](v.a),u["\u0275\u0275directiveInject"](x.a),u["\u0275\u0275directiveInject"](y.a),u["\u0275\u0275directiveInject"](c.b))},e.\u0275cmp=u["\u0275\u0275defineComponent"]({type:e,selectors:[["app-leave-app-landing-page"]],decls:2,vars:1,consts:[[4,"ngIf"],["mat-tab-nav-bar",""],[4,"ngFor","ngForOf"],["mat-tab-link","","routerLinkActive","",3,"routerLink","active",4,"ngIf"],["mat-tab-link","","routerLinkActive","",3,"routerLink","active"],["rla","routerLinkActive"]],template:function(e,t){1&e&&(u["\u0275\u0275template"](0,O,2,1,"div",0),u["\u0275\u0275element"](1,"router-outlet")),2&e&&u["\u0275\u0275property"]("ngIf",t.shouldTabBeVisible)},directives:[i.NgIf,h.l,a.f,i.NgForOf,h.j,a.e,h.i],styles:["a[_ngcontent-%COMP%]:hover{color:unset!important;text-decoration:none!important}nav[_ngcontent-%COMP%]{position:fixed;width:100%;z-index:14;background:#f8f9fa}.project-tab-content[_ngcontent-%COMP%]{padding-top:48px!important}"]}),e})(),children:[{path:"",redirectTo:"leave",pathMatch:"full"},{path:"leave",loadChildren:()=>Promise.resolve().then(n.bind(null,"H8H3")).then(e=>e.MyLeaveModule),data:{breadcrumb:"My Leaves"}},{path:"approvals",loadChildren:()=>Promise.all([n.e(4),n.e(20),n.e(25),n.e(26),n.e(525)]).then(n.bind(null,"k1w1")).then(e=>e.MyApprovalModule),data:{breadcrumb:"Approvals"}},{path:"settings",loadChildren:()=>n.e(513).then(n.bind(null,"E1Cw")).then(e=>e.LeaveSettingsModule),data:{breadcrumb:"Settings"}}]}];let S=(()=>{class e{}return e.\u0275mod=u["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=u["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[h.k.forChild(w)],h.k]}),e})();var D=n("H8H3"),I=n("pA3K");let P=(()=>{class e{}return e.\u0275mod=u["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=u["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,S,a.g,D.MyLeaveModule,I.a,o.b,r.e,l.c,s.b,d.b,c.g,p.p]]}),e})()},dYSZ:function(e,t,n){"use strict";n.d(t,"a",(function(){return m}));var i=n("0IaG"),a=n("fXoL"),o=n("ofXK"),r=n("NFeN");function l(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"div",3),a["\u0275\u0275listener"]("click",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"]().closedismissMessage()})),a["\u0275\u0275text"](1,"Dismiss"),a["\u0275\u0275elementEnd"]()}if(2&e){const e=a["\u0275\u0275nextContext"]();a["\u0275\u0275classMapInterpolate1"]("text text-",e.data.icon,"")}}const s=function(e){return{"close-icon-style":e}};let d=(()=>{class e{constructor(e,t){this.data=e,this._dialogRef=t}ngOnInit(){}closeMessage(){"close"!=this.data.icon&&this._dialogRef.close()}closedismissMessage(){this._dialogRef.close()}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275directiveInject"](i.a),a["\u0275\u0275directiveInject"](i.h))},e.\u0275cmp=a["\u0275\u0275defineComponent"]({type:e,selectors:[["app-toast-message"]],decls:7,vars:13,consts:[[1,"dialog",3,"ngClass","click"],[3,"ngClass"],["style","font-weight: 600; margin-left: 10px;",3,"class","click",4,"ngIf"],[2,"font-weight","600","margin-left","10px",3,"click"]],template:function(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"div",0),a["\u0275\u0275listener"]("click",(function(){return t.closeMessage()})),a["\u0275\u0275elementStart"](1,"div",1),a["\u0275\u0275elementStart"](2,"mat-icon"),a["\u0275\u0275text"](3),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](4,"div"),a["\u0275\u0275text"](5),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](6,l,2,3,"div",2),a["\u0275\u0275elementEnd"]()),2&e&&(a["\u0275\u0275property"]("ngClass","dialog-"+t.data.icon),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngClass",a["\u0275\u0275pureFunction1"](11,s,"close"==t.data.icon)),a["\u0275\u0275advance"](1),a["\u0275\u0275classMapInterpolate1"]("icon-",t.data.icon,""),a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate"](t.data.icon),a["\u0275\u0275advance"](1),a["\u0275\u0275classMapInterpolate1"]("text text-",t.data.icon,""),a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate"](t.data.msg),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf","close"==t.data.icon))},directives:[o.NgClass,r.a,o.NgIf],styles:[".dialog[_ngcontent-%COMP%]{border-radius:4px;display:flex;align-items:center;justify-content:space-around;max-width:450px;min-width:200px;min-height:56px;max-height:-moz-fit-content;max-height:fit-content;gap:5px;border-width:2px;border-style:solid;padding:12px;cursor:pointer}.dialog-info[_ngcontent-%COMP%]{background:#e8f4ff;border-color:#1890ff}.dialog-check_circle[_ngcontent-%COMP%]{background:#eef9e8;border-color:#52c41a}.dialog-close[_ngcontent-%COMP%]{background:#ffebec;border-color:#ff3a46}.dialog-warning[_ngcontent-%COMP%]{background:#fff3e8;border-color:#fa8c16}.text[_ngcontent-%COMP%]{font-family:var(--fontFamily);font-size:12px;font-weight:400;text-align:center}.text-info[_ngcontent-%COMP%]{color:#1890ff}.text-check_circle[_ngcontent-%COMP%]{color:#52c41a}.text-close[_ngcontent-%COMP%]{color:#ff3a46}.text-warning[_ngcontent-%COMP%]{color:#fa8c16}.icon-info[_ngcontent-%COMP%]{font-size:24px;color:#1890ff;margin-top:7px}.icon-check_circle[_ngcontent-%COMP%]{font-size:24px;color:#52c41a;margin-top:7px}.icon-close[_ngcontent-%COMP%]{font-size:16px;color:#fff;margin-top:3px;margin-left:2px}.icon-warning[_ngcontent-%COMP%]{font-size:24px;color:#fa8c16;margin-top:7px}.close-icon-style[_ngcontent-%COMP%]{background-color:#ff3a46;border-radius:50%;width:20px;height:20px;margin-left:4px}"]}),e})();var c=n("tyNb"),p=n("XNiG"),h=n("pLZG"),g=n("1G5W");let m=(()=>{class e{constructor(e,t,n){this._dialog=e,this.rendererFactory=t,this.router=n,this.currentDialogRef=null,this.destroy$=new p.b,this.router.events.pipe(Object(h.a)(e=>e instanceof c.e),Object(g.a)(this.destroy$)).subscribe(e=>{this.closeCurrentDialog()}),this.renderer=this.rendererFactory.createRenderer(null,null),this.applyStyles()}ngOnInit(){}applyStyles(){const e=this.renderer.createElement("style");e.type="text/css",e.appendChild(this.renderer.createText(".mat-dialog-container { box-shadow: none !important; }")),this.renderer.appendChild(document.head,e)}closeCurrentDialog(){this.currentDialogRef&&(this.currentDialogRef.close(),this.currentDialogRef=null)}showInfo(e,t){this.closeCurrentDialog(),this.currentDialogRef=this._dialog.open(d,{data:{msg:e,icon:"info"},disableClose:!0,position:{bottom:"40px"},backdropClass:"none",hasBackdrop:!1}),setTimeout(()=>{this.currentDialogRef.close()},t)}showSuccess(e,t){this.closeCurrentDialog(),this.currentDialogRef=this._dialog.open(d,{data:{msg:e,icon:"check_circle"},disableClose:!0,position:{bottom:"40px"},backdropClass:"none",hasBackdrop:!1}),setTimeout(()=>{this.currentDialogRef.close()},t)}showError(e){this.closeCurrentDialog(),this.currentDialogRef=this._dialog.open(d,{data:{msg:e,icon:"close"},disableClose:!0,position:{bottom:"40px"}})}showWarning(e,t){this.closeCurrentDialog(),this.currentDialogRef=this._dialog.open(d,{data:{msg:e,icon:"warning"},disableClose:!0,position:{bottom:"40px"},backdropClass:"none",hasBackdrop:!1}),setTimeout(()=>{this.currentDialogRef.close()},t)}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275inject"](i.b),a["\u0275\u0275inject"](a.RendererFactory2),a["\u0275\u0275inject"](c.g))},e.\u0275prov=a["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},w76M:function(e,t,n){"use strict";n.d(t,"a",(function(){return d})),n.d(t,"b",(function(){return c}));var i=n("jhN1"),a=n("fXoL"),o=n("oHs6"),r=n("PVOt"),l=n("6t9p");const s=["*"];let d=(()=>{let e=class extends r.b{constructor(e,t,n,i,a,o,r,l){super(e,t,n,i,r,l),this._watcherHelper=i,this._idh=a,this._createEventEmitters([{subscribe:"contentReady",emit:"onContentReady"},{subscribe:"disposing",emit:"onDisposing"},{subscribe:"hidden",emit:"onHidden"},{subscribe:"hiding",emit:"onHiding"},{subscribe:"initialized",emit:"onInitialized"},{subscribe:"optionChanged",emit:"onOptionChanged"},{subscribe:"resize",emit:"onResize"},{subscribe:"resizeEnd",emit:"onResizeEnd"},{subscribe:"resizeStart",emit:"onResizeStart"},{subscribe:"showing",emit:"onShowing"},{subscribe:"shown",emit:"onShown"},{subscribe:"titleRendered",emit:"onTitleRendered"},{emit:"accessKeyChange"},{emit:"animationChange"},{emit:"closeOnOutsideClickChange"},{emit:"containerChange"},{emit:"contentTemplateChange"},{emit:"deferRenderingChange"},{emit:"disabledChange"},{emit:"dragEnabledChange"},{emit:"elementAttrChange"},{emit:"focusStateEnabledChange"},{emit:"fullScreenChange"},{emit:"heightChange"},{emit:"hintChange"},{emit:"hoverStateEnabledChange"},{emit:"maxHeightChange"},{emit:"maxWidthChange"},{emit:"minHeightChange"},{emit:"minWidthChange"},{emit:"positionChange"},{emit:"resizeEnabledChange"},{emit:"rtlEnabledChange"},{emit:"shadingChange"},{emit:"shadingColorChange"},{emit:"showCloseButtonChange"},{emit:"showTitleChange"},{emit:"tabIndexChange"},{emit:"titleChange"},{emit:"titleTemplateChange"},{emit:"toolbarItemsChange"},{emit:"visibleChange"},{emit:"widthChange"}]),this._idh.setHost(this),o.setHost(this)}get accessKey(){return this._getOption("accessKey")}set accessKey(e){this._setOption("accessKey",e)}get animation(){return this._getOption("animation")}set animation(e){this._setOption("animation",e)}get closeOnOutsideClick(){return this._getOption("closeOnOutsideClick")}set closeOnOutsideClick(e){this._setOption("closeOnOutsideClick",e)}get container(){return this._getOption("container")}set container(e){this._setOption("container",e)}get contentTemplate(){return this._getOption("contentTemplate")}set contentTemplate(e){this._setOption("contentTemplate",e)}get deferRendering(){return this._getOption("deferRendering")}set deferRendering(e){this._setOption("deferRendering",e)}get disabled(){return this._getOption("disabled")}set disabled(e){this._setOption("disabled",e)}get dragEnabled(){return this._getOption("dragEnabled")}set dragEnabled(e){this._setOption("dragEnabled",e)}get elementAttr(){return this._getOption("elementAttr")}set elementAttr(e){this._setOption("elementAttr",e)}get focusStateEnabled(){return this._getOption("focusStateEnabled")}set focusStateEnabled(e){this._setOption("focusStateEnabled",e)}get fullScreen(){return this._getOption("fullScreen")}set fullScreen(e){this._setOption("fullScreen",e)}get height(){return this._getOption("height")}set height(e){this._setOption("height",e)}get hint(){return this._getOption("hint")}set hint(e){this._setOption("hint",e)}get hoverStateEnabled(){return this._getOption("hoverStateEnabled")}set hoverStateEnabled(e){this._setOption("hoverStateEnabled",e)}get maxHeight(){return this._getOption("maxHeight")}set maxHeight(e){this._setOption("maxHeight",e)}get maxWidth(){return this._getOption("maxWidth")}set maxWidth(e){this._setOption("maxWidth",e)}get minHeight(){return this._getOption("minHeight")}set minHeight(e){this._setOption("minHeight",e)}get minWidth(){return this._getOption("minWidth")}set minWidth(e){this._setOption("minWidth",e)}get position(){return this._getOption("position")}set position(e){this._setOption("position",e)}get resizeEnabled(){return this._getOption("resizeEnabled")}set resizeEnabled(e){this._setOption("resizeEnabled",e)}get rtlEnabled(){return this._getOption("rtlEnabled")}set rtlEnabled(e){this._setOption("rtlEnabled",e)}get shading(){return this._getOption("shading")}set shading(e){this._setOption("shading",e)}get shadingColor(){return this._getOption("shadingColor")}set shadingColor(e){this._setOption("shadingColor",e)}get showCloseButton(){return this._getOption("showCloseButton")}set showCloseButton(e){this._setOption("showCloseButton",e)}get showTitle(){return this._getOption("showTitle")}set showTitle(e){this._setOption("showTitle",e)}get tabIndex(){return this._getOption("tabIndex")}set tabIndex(e){this._setOption("tabIndex",e)}get title(){return this._getOption("title")}set title(e){this._setOption("title",e)}get titleTemplate(){return this._getOption("titleTemplate")}set titleTemplate(e){this._setOption("titleTemplate",e)}get toolbarItems(){return this._getOption("toolbarItems")}set toolbarItems(e){this._setOption("toolbarItems",e)}get visible(){return this._getOption("visible")}set visible(e){this._setOption("visible",e)}get width(){return this._getOption("width")}set width(e){this._setOption("width",e)}get toolbarItemsChildren(){return this._getOption("toolbarItems")}set toolbarItemsChildren(e){this.setChildren("toolbarItems",e)}_createInstance(e,t){return new o.a(e,t)}ngOnDestroy(){this._destroyWidget()}ngOnChanges(e){super.ngOnChanges(e),this.setupChanges("toolbarItems",e)}setupChanges(e,t){e in this._optionsToUpdate||this._idh.setup(e,t)}ngDoCheck(){this._idh.doCheck("toolbarItems"),this._watcherHelper.checkWatchers(),super.ngDoCheck(),super.clearChangedOptions()}_setOption(e,t){let n=this._idh.setupSingle(e,t),i=null!==this._idh.getChanges(e,t);(n||i)&&super._setOption(e,t)}};return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275directiveInject"](a.ElementRef),a["\u0275\u0275directiveInject"](a.NgZone),a["\u0275\u0275directiveInject"](r.e),a["\u0275\u0275directiveInject"](r.j),a["\u0275\u0275directiveInject"](r.g),a["\u0275\u0275directiveInject"](r.i),a["\u0275\u0275directiveInject"](i.h),a["\u0275\u0275directiveInject"](a.PLATFORM_ID))},e.\u0275cmp=a["\u0275\u0275defineComponent"]({type:e,selectors:[["dx-popup"]],contentQueries:function(e,t,n){if(1&e&&a["\u0275\u0275contentQuery"](n,l.L,!1),2&e){let e;a["\u0275\u0275queryRefresh"](e=a["\u0275\u0275loadQuery"]())&&(t.toolbarItemsChildren=e)}},inputs:{accessKey:"accessKey",animation:"animation",closeOnOutsideClick:"closeOnOutsideClick",container:"container",contentTemplate:"contentTemplate",deferRendering:"deferRendering",disabled:"disabled",dragEnabled:"dragEnabled",elementAttr:"elementAttr",focusStateEnabled:"focusStateEnabled",fullScreen:"fullScreen",height:"height",hint:"hint",hoverStateEnabled:"hoverStateEnabled",maxHeight:"maxHeight",maxWidth:"maxWidth",minHeight:"minHeight",minWidth:"minWidth",position:"position",resizeEnabled:"resizeEnabled",rtlEnabled:"rtlEnabled",shading:"shading",shadingColor:"shadingColor",showCloseButton:"showCloseButton",showTitle:"showTitle",tabIndex:"tabIndex",title:"title",titleTemplate:"titleTemplate",toolbarItems:"toolbarItems",visible:"visible",width:"width"},outputs:{onContentReady:"onContentReady",onDisposing:"onDisposing",onHidden:"onHidden",onHiding:"onHiding",onInitialized:"onInitialized",onOptionChanged:"onOptionChanged",onResize:"onResize",onResizeEnd:"onResizeEnd",onResizeStart:"onResizeStart",onShowing:"onShowing",onShown:"onShown",onTitleRendered:"onTitleRendered",accessKeyChange:"accessKeyChange",animationChange:"animationChange",closeOnOutsideClickChange:"closeOnOutsideClickChange",containerChange:"containerChange",contentTemplateChange:"contentTemplateChange",deferRenderingChange:"deferRenderingChange",disabledChange:"disabledChange",dragEnabledChange:"dragEnabledChange",elementAttrChange:"elementAttrChange",focusStateEnabledChange:"focusStateEnabledChange",fullScreenChange:"fullScreenChange",heightChange:"heightChange",hintChange:"hintChange",hoverStateEnabledChange:"hoverStateEnabledChange",maxHeightChange:"maxHeightChange",maxWidthChange:"maxWidthChange",minHeightChange:"minHeightChange",minWidthChange:"minWidthChange",positionChange:"positionChange",resizeEnabledChange:"resizeEnabledChange",rtlEnabledChange:"rtlEnabledChange",shadingChange:"shadingChange",shadingColorChange:"shadingColorChange",showCloseButtonChange:"showCloseButtonChange",showTitleChange:"showTitleChange",tabIndexChange:"tabIndexChange",titleChange:"titleChange",titleTemplateChange:"titleTemplateChange",toolbarItemsChange:"toolbarItemsChange",visibleChange:"visibleChange",widthChange:"widthChange"},features:[a["\u0275\u0275ProvidersFeature"]([r.e,r.j,r.i,r.g]),a["\u0275\u0275InheritDefinitionFeature"],a["\u0275\u0275NgOnChangesFeature"]],ngContentSelectors:s,decls:1,vars:0,template:function(e,t){1&e&&(a["\u0275\u0275projectionDef"](),a["\u0275\u0275projection"](0))},encapsulation:2}),e})(),c=(()=>{let e=class{};return e.\u0275mod=a["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=a["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[l.bb,l.Gc,l.Vd,l.vd,l.hb,l.lb,l.sb,l.id,l.jd,l.M,r.c,r.f,i.b],l.bb,l.Gc,l.Vd,l.vd,l.hb,l.lb,l.sb,l.id,l.jd,l.M,r.f]}),e})()}}]);