(window.webpackJsonp=window.webpackJsonp||[]).push([[883],{dpz6:function(e,t,n){"use strict";n.r(t),n.d(t,"ApprovalRejectModalComponent",(function(){return c}));var l=n("0IaG"),o=n("XNiG"),a=n("fXoL"),i=n("kmnG"),r=n("qFsG"),s=n("3Pt+");let c=(()=>{class e{constructor(e,t,n){this.dialogRef=e,this.rejectModalData=t,this.dialog=n,this.reason="",this._onDestroy=new o.b}ngOnInit(){this.deatilViewData=this.rejectModalData.modalParams.data,console.log(this.deatilViewData)}closeModal(){this.dialogRef.close({event:"close"})}rejectLeaveRequest(){this.dialogRef.close({event:"Reject",reason:this.reason,data:this.deatilViewData})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275directiveInject"](l.h),a["\u0275\u0275directiveInject"](l.a),a["\u0275\u0275directiveInject"](l.b))},e.\u0275cmp=a["\u0275\u0275defineComponent"]({type:e,selectors:[["app-approval-reject-modal"]],decls:22,vars:2,consts:[[1,"container-fluild","pl-2","pr-2","reject-dialog-styles"],[1,"col-12","p-2"],[1,"row"],[1,"col-12","pb-2"],[1,"mainText"],[1,"subText","pt-2"],[1,"col-12"],[1,"msgText"],["appearance","outline","floatLabel","always",2,"width","450px"],["matInput","","rows","4","cols","50","placeholder","Type Your Message Here","required","",3,"ngModel","ngModelChange"],[1,"btn",2,"background","#DADCE2","color","#45546E",3,"click"],[1,"btn",2,"background","#FFFFFF","border","1px solid #FF3A46","color","#FF3A46","margin-left","15px",3,"click"]],template:function(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"div",0),a["\u0275\u0275elementStart"](1,"div",1),a["\u0275\u0275elementStart"](2,"div",2),a["\u0275\u0275elementStart"](3,"div",3),a["\u0275\u0275elementStart"](4,"span",4),a["\u0275\u0275text"](5),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](6,"div",5),a["\u0275\u0275text"](7," This will help them to resolve the issue "),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](8,"div",2),a["\u0275\u0275elementStart"](9,"div",6),a["\u0275\u0275elementStart"](10,"span",7),a["\u0275\u0275text"](11,"Message"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](12,"div",2),a["\u0275\u0275elementStart"](13,"div",6),a["\u0275\u0275elementStart"](14,"mat-form-field",8),a["\u0275\u0275elementStart"](15,"textarea",9),a["\u0275\u0275listener"]("ngModelChange",(function(e){return t.reason=e})),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](16,"div",2),a["\u0275\u0275elementStart"](17,"div",6),a["\u0275\u0275elementStart"](18,"button",10),a["\u0275\u0275listener"]("click",(function(){return t.closeModal()})),a["\u0275\u0275text"](19,"Cancel"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](20,"button",11),a["\u0275\u0275listener"]("click",(function(){return t.rejectLeaveRequest()})),a["\u0275\u0275text"](21,"Reject"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e&&(a["\u0275\u0275advance"](5),a["\u0275\u0275textInterpolate1"]("Let ",t.deatilViewData.employeeName," know the reason"),a["\u0275\u0275advance"](10),a["\u0275\u0275property"]("ngModel",t.reason))},directives:[i.c,r.b,s.e,s.F,s.v,s.y],styles:[".reject-dialog-styles[_ngcontent-%COMP%]   .mainText[_ngcontent-%COMP%]{font-family:DM Sans;font-style:normal;font-weight:700;font-size:18px;line-height:24px;letter-spacing:.02em;color:#111434}.reject-dialog-styles[_ngcontent-%COMP%]   .subText[_ngcontent-%COMP%]{font-family:DM Sans;font-style:normal;font-weight:400;font-size:12px;line-height:20px;color:#8b95a5}.reject-dialog-styles[_ngcontent-%COMP%]   .msgText[_ngcontent-%COMP%]{font-weight:700;font-size:15px;letter-spacing:.02em;color:#6e7b8f}.reject-dialog-styles[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%], .reject-dialog-styles[_ngcontent-%COMP%]   .msgText[_ngcontent-%COMP%]{font-family:DM Sans;font-style:normal;line-height:16px;text-transform:capitalize}.reject-dialog-styles[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{width:77px;height:40px;border-radius:8px;font-weight:500;font-size:14px;letter-spacing:-.02em}"]}),e})()}}]);