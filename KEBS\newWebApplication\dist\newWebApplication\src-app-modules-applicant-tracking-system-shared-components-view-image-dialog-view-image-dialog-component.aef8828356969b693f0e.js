(window.webpackJsonp=window.webpackJsonp||[]).push([[943],{Cd4J:function(e,t,n){"use strict";n.r(t),n.d(t,"ViewImageDialogComponent",(function(){return a}));var i=n("0IaG"),o=n("fXoL"),c=n("NFeN");let a=(()=>{class e{constructor(e,t){this.data=e,this._dialogRef=t}ngOnInit(){}onClose(){this._dialogRef.close()}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](i.a),o["\u0275\u0275directiveInject"](i.h))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-view-image-dialog"]],decls:9,vars:2,consts:[[1,"bg-container"],[1,"d-flex","align-items-center","justify-content-between"],[1,"title","p-0"],[1,"icon"],[1,"icon",3,"click"],[1,"img",3,"src"]],template:function(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"div",0),o["\u0275\u0275elementStart"](1,"div",1),o["\u0275\u0275elementStart"](2,"div",2),o["\u0275\u0275text"](3),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](4,"div",3),o["\u0275\u0275elementStart"](5,"mat-icon",4),o["\u0275\u0275listener"]("click",(function(){return t.onClose()})),o["\u0275\u0275text"](6," close "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](7,"div"),o["\u0275\u0275element"](8,"img",5),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e&&(o["\u0275\u0275advance"](3),o["\u0275\u0275textInterpolate"](t.data.title),o["\u0275\u0275advance"](5),o["\u0275\u0275property"]("src",t.data.img,o["\u0275\u0275sanitizeUrl"]))},directives:[c.a],styles:[".bg-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:20px;padding:16px;overflow:hidden}.bg-container[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:18px;font-weight:700;color:#111434}.bg-container[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{width:18px;height:18px;font-size:18px;color:#1b2140;cursor:pointer}.bg-container[_ngcontent-%COMP%]   .img[_ngcontent-%COMP%]{border-radius:4px}"]}),e})()}}]);