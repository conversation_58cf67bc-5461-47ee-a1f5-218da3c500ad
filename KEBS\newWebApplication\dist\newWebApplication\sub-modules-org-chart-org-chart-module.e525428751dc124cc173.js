(window.webpackJsonp=window.webpackJsonp||[]).push([[1012],{"7eMM":function(e,t,i){"use strict";i.r(t),i.d(t,"OKRListTooltipOptions",(function(){return Va})),i.d(t,"OKRListTooltipProperties",(function(){return Fa})),i.d(t,"OrgChartModule",(function(){return Ua}));var a=i("ofXK"),r=i("f0Cb"),n=i("Qu3c"),o=i("STbY"),s=i("tyNb"),d=i("mrSG"),l=i("33Jv"),c=i("3Pt+"),p=i("0IaG"),h=i("Kj3r"),u=i("/uUt"),m=i("quSY"),f=i("fXoL"),g=i("25DO"),y=i("+K9r"),v=i("1A3m"),b=i("bTqV"),S=i("NFeN"),C=i("jtHE"),D=i("XNiG"),O=i("1G5W"),_=i("NJ67"),T=i("kmnG"),x=i("d3UM"),w=i("FKr1"),A=i("WJ5W");const k=["allSelected"],E=["singleSelect"];function M(e,t){if(1&e&&(f["\u0275\u0275elementStart"](0,"mat-option",5),f["\u0275\u0275text"](1),f["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;f["\u0275\u0275property"]("value",e.id),f["\u0275\u0275advance"](1),f["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let I=(()=>{class e extends _.a{constructor(e,t){super(),this.renderer=e,this._okr=t,this.fieldCtrl=new c.j,this.fieldFilterCtrl=new c.j,this.list=[],this.required=!1,this.valueChange=new f.EventEmitter,this.disabled=!1,this.filteredList=new C.a,this._onDestroy=new D.b,this.filterToggle=!0}ngOnInit(){this.list&&this.list.length>1&&this.fieldCtrl.setValue(this.list[0]),this.fieldFilterCtrl.valueChanges.pipe(Object(O.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(O.a)(this._onDestroy)).subscribe(e=>{if(this.value=e,this.onChange(e),this.valueChange.emit(e),this.list){const e=this.list.map(e=>({key_name:e.key_name,isActive:this.fieldCtrl.value.includes(e.id)}));this._okr.updateChartConfig(e)}})}ngOnChanges(e){e.list&&this.list&&this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>{const i=t.name?t.name.toLowerCase():"",a=t.description?t.description.toLowerCase():"";return i.indexOf(e)>-1||a.indexOf(e)>-1}))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(Array.isArray(e)?e:[])}toggleAllSelection(){if(this.allSelected.selected){const e=this.list.map(e=>e.id);this.fieldCtrl.patchValue(e),console.log("Checked values:",e)}}}return e.\u0275fac=function(t){return new(t||e)(f["\u0275\u0275directiveInject"](f.Renderer2),f["\u0275\u0275directiveInject"](g.a))},e.\u0275cmp=f["\u0275\u0275defineComponent"]({type:e,selectors:[["app-chart-multi-select"]],viewQuery:function(e,t){if(1&e&&(f["\u0275\u0275viewQuery"](k,!0),f["\u0275\u0275viewQuery"](E,!0)),2&e){let e;f["\u0275\u0275queryRefresh"](e=f["\u0275\u0275loadQuery"]())&&(t.allSelected=e.first),f["\u0275\u0275queryRefresh"](e=f["\u0275\u0275loadQuery"]())&&(t.singleSelect=e.first)}},inputs:{list:"list",placeholder:"placeholder",required:"required",disabled:"disabled",filterToggle:"filterToggle"},outputs:{valueChange:"valueChange"},features:[f["\u0275\u0275ProvidersFeature"]([{provide:c.t,useExisting:Object(f.forwardRef)(()=>e),multi:!0}]),f["\u0275\u0275InheritDefinitionFeature"],f["\u0275\u0275NgOnChangesFeature"]],decls:9,vars:10,consts:[["appearance","outline",2,"width","100%"],["multiple","",3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value",4,"ngFor","ngForOf"],[3,"value"]],template:function(e,t){1&e&&(f["\u0275\u0275elementStart"](0,"mat-form-field",0),f["\u0275\u0275elementStart"](1,"mat-label"),f["\u0275\u0275text"](2),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](3,"mat-select",1,2),f["\u0275\u0275elementStart"](5,"mat-option"),f["\u0275\u0275element"](6,"ngx-mat-select-search",3),f["\u0275\u0275elementEnd"](),f["\u0275\u0275template"](7,M,2,2,"mat-option",4),f["\u0275\u0275pipe"](8,"async"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]()),2&e&&(f["\u0275\u0275advance"](2),f["\u0275\u0275textInterpolate"](t.placeholder),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),f["\u0275\u0275advance"](3),f["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngForOf",f["\u0275\u0275pipeBind1"](8,8,t.filteredList)))},directives:[T.c,T.g,x.c,c.v,c.k,c.F,w.p,A.a,a.NgForOf],pipes:[a.AsyncPipe],styles:[""]}),e})();var P=i("bSwM"),j=i("qFsG"),R=i("Xa2L"),N=i("QibW");function V(e,t){if(1&e){const e=f["\u0275\u0275getCurrentView"]();f["\u0275\u0275elementStart"](0,"mat-checkbox",36),f["\u0275\u0275listener"]("ngModelChange",(function(t){return f["\u0275\u0275restoreView"](e),f["\u0275\u0275nextContext"]().$implicit.isChecked=t}))("change",(function(t){f["\u0275\u0275restoreView"](e);const i=f["\u0275\u0275nextContext"]().$implicit;return f["\u0275\u0275nextContext"](3).logCheckedValue(i,t)})),f["\u0275\u0275text"](1),f["\u0275\u0275elementEnd"]()}if(2&e){const e=f["\u0275\u0275nextContext"]().$implicit,t=f["\u0275\u0275nextContext"](3);f["\u0275\u0275property"]("ngModel",e.isChecked)("checked",t.selectAllObjectives)("matTooltip",e.objective_name),f["\u0275\u0275advance"](1),f["\u0275\u0275textInterpolate1"](" ",e.objective_name||"-"," ")}}function F(e,t){if(1&e&&(f["\u0275\u0275elementStart"](0,"div",34),f["\u0275\u0275template"](1,V,2,4,"mat-checkbox",35),f["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",e.objective_name)}}function U(e,t){if(1&e&&(f["\u0275\u0275elementStart"](0,"div",32),f["\u0275\u0275template"](1,F,2,1,"div",33),f["\u0275\u0275elementEnd"]()),2&e){const e=f["\u0275\u0275nextContext"](2);f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngForOf",e.objectiveList)}}function K(e,t){1&e&&(f["\u0275\u0275elementStart"](0,"div",37),f["\u0275\u0275element"](1,"mat-spinner",38),f["\u0275\u0275elementEnd"]())}function L(e,t){1&e&&(f["\u0275\u0275elementStart"](0,"div",39),f["\u0275\u0275element"](1,"img",40),f["\u0275\u0275elementStart"](2,"div"),f["\u0275\u0275text"](3,"No Objectives Found"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]())}function H(e,t){if(1&e&&(f["\u0275\u0275elementStart"](0,"mat-radio-button",43),f["\u0275\u0275text"](1),f["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;f["\u0275\u0275property"]("value",e.id),f["\u0275\u0275advance"](1),f["\u0275\u0275textInterpolate1"](" ",e.name," ")}}function Y(e,t){if(1&e){const e=f["\u0275\u0275getCurrentView"]();f["\u0275\u0275elementStart"](0,"mat-radio-group",41),f["\u0275\u0275listener"]("ngModelChange",(function(t){return f["\u0275\u0275restoreView"](e),f["\u0275\u0275nextContext"](2).selectedOrgId=t}))("change",(function(){return f["\u0275\u0275restoreView"](e),f["\u0275\u0275nextContext"](2).orgSelectionChanged()})),f["\u0275\u0275template"](1,H,2,2,"mat-radio-button",42),f["\u0275\u0275elementEnd"]()}if(2&e){const e=f["\u0275\u0275nextContext"](2);f["\u0275\u0275property"]("ngModel",e.selectedOrgId),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngForOf",e.organizations)}}function z(e,t){1&e&&(f["\u0275\u0275elementStart"](0,"div",37),f["\u0275\u0275element"](1,"mat-spinner",38),f["\u0275\u0275elementEnd"]())}function B(e,t){1&e&&(f["\u0275\u0275elementStart"](0,"div",39),f["\u0275\u0275element"](1,"img",40),f["\u0275\u0275elementStart"](2,"div"),f["\u0275\u0275text"](3,"No Organization Found"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]())}function W(e,t){if(1&e){const e=f["\u0275\u0275getCurrentView"]();f["\u0275\u0275elementStart"](0,"div"),f["\u0275\u0275elementStart"](1,"div"),f["\u0275\u0275elementStart"](2,"form",6),f["\u0275\u0275element"](3,"app-chart-multi-select",7),f["\u0275\u0275element"](4,"app-chart-multi-select",8),f["\u0275\u0275element"](5,"app-chart-multi-select",9),f["\u0275\u0275elementStart"](6,"div",10),f["\u0275\u0275elementStart"](7,"mat-icon",11),f["\u0275\u0275text"](8,"info"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](9,"div",12),f["\u0275\u0275elementStart"](10,"button",13),f["\u0275\u0275listener"]("click",(function(){return f["\u0275\u0275restoreView"](e),f["\u0275\u0275nextContext"]().clearFilters()})),f["\u0275\u0275elementStart"](11,"mat-icon"),f["\u0275\u0275text"](12,"restart_alt"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](13,"div",14),f["\u0275\u0275elementStart"](14,"div",15),f["\u0275\u0275elementStart"](15,"div",16),f["\u0275\u0275elementStart"](16,"mat-checkbox",17),f["\u0275\u0275listener"]("ngModelChange",(function(t){return f["\u0275\u0275restoreView"](e),f["\u0275\u0275nextContext"]().selectAllObjectives=t}))("change",(function(t){return f["\u0275\u0275restoreView"](e),f["\u0275\u0275nextContext"]().selectAllObjs(t)})),f["\u0275\u0275text"](17,"Objectives"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](18,"mat-form-field",18),f["\u0275\u0275element"](19,"input",19),f["\u0275\u0275elementStart"](20,"mat-icon",20),f["\u0275\u0275text"](21,"search"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275element"](22,"hr"),f["\u0275\u0275template"](23,U,2,1,"div",21),f["\u0275\u0275template"](24,K,2,0,"div",22),f["\u0275\u0275template"](25,L,4,0,"div",23),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](26,"div",24),f["\u0275\u0275elementStart"](27,"div",25),f["\u0275\u0275elementStart"](28,"div",26),f["\u0275\u0275text"](29,"Target Organization"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](30,"mat-form-field",18),f["\u0275\u0275element"](31,"input",27),f["\u0275\u0275elementStart"](32,"mat-icon",20),f["\u0275\u0275text"](33,"search"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275element"](34,"hr"),f["\u0275\u0275template"](35,Y,2,2,"mat-radio-group",28),f["\u0275\u0275template"](36,z,2,0,"div",22),f["\u0275\u0275template"](37,B,4,0,"div",23),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](38,"div",29),f["\u0275\u0275elementStart"](39,"button",30),f["\u0275\u0275elementStart"](40,"span"),f["\u0275\u0275text"](41,"Cancel"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](42,"button",31),f["\u0275\u0275listener"]("click",(function(){return f["\u0275\u0275restoreView"](e),f["\u0275\u0275nextContext"]().saveOrgChanges()})),f["\u0275\u0275elementStart"](43,"span"),f["\u0275\u0275text"](44,"Save"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]()}if(2&e){const e=f["\u0275\u0275nextContext"]();f["\u0275\u0275advance"](2),f["\u0275\u0275property"]("formGroup",e.orgSelectGroup),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("list",e.organizationsDrop)("placeholder","Organization"),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("list",e.fyYears)("placeholder","Financial Year"),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("list",e.empData)("placeholder","Objective Owner"),f["\u0275\u0275advance"](11),f["\u0275\u0275property"]("ngModel",e.selectAllObjectives),f["\u0275\u0275advance"](3),f["\u0275\u0275property"]("formControl",e.searchObjTermControl),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("mattoolTip","Search"),f["\u0275\u0275advance"](3),f["\u0275\u0275property"]("ngIf",e.objectiveList.length>0&&!e.noObjectiveData&&!e.isObjLoading),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",e.isObjLoading),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",!e.isObjLoading&&(0==e.objectiveList.length||e.noObjectiveData)),f["\u0275\u0275advance"](6),f["\u0275\u0275property"]("formControl",e.searchTermControl),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("mattoolTip","Search"),f["\u0275\u0275advance"](3),f["\u0275\u0275property"]("ngIf",!e.isOrgLoading&&e.organizations.length>0),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",e.isOrgLoading),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",!e.isOrgLoading&&0==e.organizations.length),f["\u0275\u0275advance"](2),f["\u0275\u0275property"]("disabled",e.isSaving),f["\u0275\u0275advance"](3),f["\u0275\u0275property"]("disabled",e.isSaving)}}function J(e,t){1&e&&(f["\u0275\u0275elementStart"](0,"div"),f["\u0275\u0275element"](1,"mat-spinner",38),f["\u0275\u0275elementEnd"]())}let G=(()=>{class e{constructor(e,t,i,a,r,n){this._okr=e,this._okrService=t,this.fb=i,this._toaster=a,this.data=r,this.bulkDialogRef=n,this.organizations=[],this.originalOrganizationsList=[],this.organizationsDrop=[],this.objectiveList=[],this.originalObjectiveList=[],this.fyYears=[],this.empData=[],this.filteredOrganizations=[],this.selectAllObjectives=!1,this.isOrgLoading=!1,this.isObjLoading=!1,this.isSaving=!1,this.searchTerm="",this.searchTermControl=new c.j(""),this.searchObjTermControl=new c.j(""),this.subscriptions=new m.a,this.subs=new l.a,this.loaded=!1,this.noObjectiveData=!1,this.orgSelectGroup=this.fb.group({orgControl:[null],fyControl:[null],ownerControl:[null]})}ngOnInit(){this.loaded=!0,this.getAllOrgForOKR(),this.getQuaterFY(),this.getAllEmpFilter(),this.defaultObjectivefetchFilter(),this.loaded=!1,this.subs.add(this.searchTermControl.valueChanges.pipe(Object(h.a)(300),Object(u.a)()).subscribe(e=>{this.applySearchFilter("ORG")})),this.subs.add(this.searchObjTermControl.valueChanges.pipe(Object(h.a)(300),Object(u.a)()).subscribe(e=>{this.applySearchFilter("OBJ")})),this.subs.add(this.orgSelectGroup.valueChanges.subscribe(e=>{this.miniFiliter=e;let t=e.orgControl?String(e.orgControl):null,i=[];t&&(i=t.split(",")),this.miniFiliter=Object.assign(Object.assign({},e),{orgControl:i}),this.getObjectiveData()})),this.subs.add(this._okrService.selectedOrg$.subscribe(e=>{this.selectedOrgLanding=String(e.code)}))}ngOnDestroy(){this.subs.unsubscribe()}defaultObjectivefetchFilter(){this.miniFiliter={orgControl:[],fyControl:[],ownerControl:[]},this.getObjectiveData()}getAllOrgForOKR(){return Object(d.c)(this,void 0,void 0,(function*(){try{this.isOrgLoading=!0;const e=yield this._okr.getOkrForOrgData();this.organizations=e||[],this.organizationsDrop=e||[],this.originalOrganizationsList=e||[],this.applySearchFilter("ORG"),this.isOrgLoading=!1}catch(e){console.error("Error in getOkrForOrgData:",e),this.isOrgLoading=!1}}))}getObjectiveData(){return Object(d.c)(this,void 0,void 0,(function*(){try{this.isObjLoading=!0;let e=this.miniFiliter;const t=yield this._okr.getOkrOrgChartDetails(e).toPromise();this.objectiveList=t.data,this.originalObjectiveList=t.data,this.noObjectiveData=!this.objectiveList.some(e=>e.objective_name),this.applySearchFilter("OBJ"),this.isObjLoading=!1}catch(e){console.error("Error in getObjectiveData:",e),this.isObjLoading=!1}}))}applySearchFilter(e){if("ORG"===e){this.organizations=[...this.originalOrganizationsList];const e=this.searchTermControl.value.toLowerCase();if(!e)return void(this.organizations=[...this.originalOrganizationsList]);const t=this.organizations.filter(t=>{var i;return null===(i=null==t?void 0:t.name)||void 0===i?void 0:i.toLowerCase().includes(e)});0===t.length&&(this.organizations=[],this._toaster.showWarning("No such organization found in the list","")),this.organizations=t}if("OBJ"===e){this.noObjectiveData=!1;const e=this.searchObjTermControl.value.toLowerCase();if(!e)return void(this.objectiveList=[...this.originalObjectiveList]);const t=this.objectiveList.filter(t=>{var i;return null===(i=null==t?void 0:t.objective_name)||void 0===i?void 0:i.toLowerCase().includes(e)});0===t.length&&(this.noObjectiveData=!0,this._toaster.showWarning("No such objective found in the list!","")),this.objectiveList=t}}getQuaterFY(){return Object(d.c)(this,void 0,void 0,(function*(){try{let e=[],t=yield this._okrService.getQuarterDate();t?(e=t.map(e=>{const{type:t,startDate:i,endDate:a,fyr:r}=e;return{name:t,startDate:i,endDate:a,fyr:r,id:r}}),this.fyYears=e):console.error("Error in getQuaterFY: Response data is undefined or empty")}catch(e){console.error("Error in getQuaterFY:",e)}}))}getAllEmpFilter(){return Object(d.c)(this,void 0,void 0,(function*(){try{this.isOrgLoading=!0;const e=yield this._okr.getAllEmpFilter();this.empData=e||[],this.isOrgLoading=!1}catch(e){console.error("Error in getAllEmpFilter:",e),this.isOrgLoading=!1}}))}selectOKRFOrg(e){}orgSelectionChanged(){console.log("Selected Org ID:",this.selectedOrgId)}toggleSelectAll(){this.selectAllObjectives=!this.selectAllObjectives,this.objectiveList.forEach(e=>e.isChecked=this.selectAllObjectives)}logCheckedValue(e,t){e.isChecked=t.checked}clearFilters(){this.orgSelectGroup.reset(),this.getObjectiveData(),this._toaster.showSuccess("Filters Reset Successful","",2e3)}selectAllObjs(e){for(let t of this.objectiveList)t.isChecked=e.checked}saveOrgChanges(){if(this.isSaving)return;const e={selectedOrgId:this.selectedOrgId,selectedObjectives:this.objectiveList.filter(e=>e.isChecked).map(e=>e.objective_id)};e.selectedOrgId&&0!==e.selectedObjectives.length?(this.isSaving=!0,this._okr.updateObjectiveOrg(e).then(e=>{this._toaster.showSuccess("Selected Objectives saved successfully!","",2e3),this.ngOnDestroy(),this.bulkDialogRef.close(),this.isSaving=!1}).catch(e=>{console.error("Error updating data:",e),this._toaster.showError("Failed to Change Org of selected Objectives. Please try again.","",2e3),this.isSaving=!1})):this._toaster.showWarning("Organisation or alteast one objective should be checked","",2e3)}}return e.\u0275fac=function(t){return new(t||e)(f["\u0275\u0275directiveInject"](g.a),f["\u0275\u0275directiveInject"](y.a),f["\u0275\u0275directiveInject"](c.i),f["\u0275\u0275directiveInject"](v.a),f["\u0275\u0275directiveInject"](p.a),f["\u0275\u0275directiveInject"](p.h))},e.\u0275cmp=f["\u0275\u0275defineComponent"]({type:e,selectors:[["app-bulk-org-change"]],decls:10,vars:3,consts:[[1,"org-change-main"],[1,"top-row"],[1,"main-header"],["mat-icon-button","",3,"mat-dialog-close","matTooltip"],[1,"close-icon"],[4,"ngIf"],[1,"filter-wrapper",3,"formGroup"],["formControlName","orgControl",1,"filter-wrap","mr-3",3,"list","placeholder"],["formControlName","fyControl",1,"filter-wrap","mr-3",3,"list","placeholder"],["formControlName","ownerControl",1,"filter-wrap","mr-3",3,"list","placeholder"],[1,"info-btn"],["matTooltip","These filters are only applied for Objective List"],[1,"reset-btn"],["mat-icon-button","","matTooltip","Reset Filters",3,"click"],[1,"main-wrapper","row"],[1,"objective-list-wrapper","col"],[1,"obj-header","d-flex","align-items-center","justify-content-between"],[3,"ngModel","ngModelChange","change"],["appearance","outline",1,"search-field"],["matInput","","placeholder","Search Objective",3,"formControl"],["matSuffix","",3,"mattoolTip"],["class","check-main",4,"ngIf"],["class","loading",4,"ngIf"],["class","no-data",4,"ngIf"],[1,"organisation-list-wrapper","col"],[1,"org-lis","d-flex","align-items-center","justify-content-between"],[1,"org-header"],["matInput","","placeholder","Search Organization",3,"formControl"],["class","radio-shack",3,"ngModel","ngModelChange","change",4,"ngIf"],[1,"buttons-wrapper"],["mat-button","",1,"close-button",3,"mat-dialog-close","disabled"],["mat-button","",1,"save-button",3,"disabled","click"],[1,"check-main"],["class","check-lis",4,"ngFor","ngForOf"],[1,"check-lis"],[3,"ngModel","checked","matTooltip","ngModelChange","change",4,"ngIf"],[3,"ngModel","checked","matTooltip","ngModelChange","change"],[1,"loading"],["diameter","30"],[1,"no-data"],["src","https://assets.kebs.app/images/timeline.png","alt",""],[1,"radio-shack",3,"ngModel","ngModelChange","change"],[3,"value",4,"ngFor","ngForOf"],[3,"value"]],template:function(e,t){1&e&&(f["\u0275\u0275elementStart"](0,"div",0),f["\u0275\u0275elementStart"](1,"div",1),f["\u0275\u0275elementStart"](2,"div",2),f["\u0275\u0275text"](3,"OKR - Organisation Change Wizard"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](4,"button",3),f["\u0275\u0275elementStart"](5,"mat-icon",4),f["\u0275\u0275text"](6,"close"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275element"](7,"hr"),f["\u0275\u0275template"](8,W,45,20,"div",5),f["\u0275\u0275template"](9,J,2,0,"div",5),f["\u0275\u0275elementEnd"]()),2&e&&(f["\u0275\u0275advance"](4),f["\u0275\u0275property"]("matTooltip","Close"),f["\u0275\u0275advance"](4),f["\u0275\u0275property"]("ngIf",!t.loaded),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",t.loaded))},directives:[b.a,p.d,n.a,S.a,a.NgIf,c.J,c.w,c.n,I,c.v,c.l,P.a,c.y,T.c,j.b,c.e,c.k,T.i,a.NgForOf,R.c,N.b,N.a],styles:[".org-change-main[_ngcontent-%COMP%]{padding:1rem}.org-change-main[_ngcontent-%COMP%]   .top-row[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:0 1rem;margin-bottom:-1%}.org-change-main[_ngcontent-%COMP%]   .top-row[_ngcontent-%COMP%]   .main-header[_ngcontent-%COMP%]{color:var(--Black-90,#1b2140);font-family:Roboto;font-size:16px;font-style:normal;font-weight:700;line-height:16px;text-transform:capitalize}.org-change-main[_ngcontent-%COMP%]   .top-row[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.org-change-main[_ngcontent-%COMP%]   .filter-wrapper[_ngcontent-%COMP%]{display:flex;justify-content:space-between;padding:0 1rem}.org-change-main[_ngcontent-%COMP%]   .filter-wrapper[_ngcontent-%COMP%]   .filter-wrap[_ngcontent-%COMP%]{width:15rem}.org-change-main[_ngcontent-%COMP%]   .filter-wrapper[_ngcontent-%COMP%]   .info-btn[_ngcontent-%COMP%], .org-change-main[_ngcontent-%COMP%]   .filter-wrapper[_ngcontent-%COMP%]   .reset-btn[_ngcontent-%COMP%]{color:#7d838b;display:flex;justify-content:center}.org-change-main[_ngcontent-%COMP%]   .filter-wrapper[_ngcontent-%COMP%]   .info-btn[_ngcontent-%COMP%]{margin-top:.8rem;cursor:default}.org-change-main[_ngcontent-%COMP%]   .filter-wrapper[_ngcontent-%COMP%]   .info-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:18px;color:#7d838b;opacity:.8}.org-change-main[_ngcontent-%COMP%]   .filter-wrapper[_ngcontent-%COMP%]   .info-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]:hover{opacity:1}.org-change-main[_ngcontent-%COMP%]   .main-wrapper[_ngcontent-%COMP%]{height:70vh;overflow-y:auto;display:flex;justify-content:space-between;gap:2%;padding:0 1rem 1rem}.org-change-main[_ngcontent-%COMP%]   .main-wrapper[_ngcontent-%COMP%]   .objective-list-wrapper[_ngcontent-%COMP%]{height:-webkit-fill-available;border-radius:8px;border:1px solid var(--Blue-Grey-30,#e8e9ee);background:var(--Neutral-White,#fff);width:23rem}.org-change-main[_ngcontent-%COMP%]   .main-wrapper[_ngcontent-%COMP%]   .objective-list-wrapper[_ngcontent-%COMP%]   hr[_ngcontent-%COMP%]{color:#e8e9ee;margin-top:.5rem}.org-change-main[_ngcontent-%COMP%]   .main-wrapper[_ngcontent-%COMP%]   .objective-list-wrapper[_ngcontent-%COMP%]   .obj-header[_ngcontent-%COMP%]{color:var(--Blue-Grey-100,#45546e);font-family:Roboto;font-size:14px;font-style:normal;font-weight:700;line-height:24px;letter-spacing:.28px;text-transform:capitalize;margin-top:.75rem}.org-change-main[_ngcontent-%COMP%]   .main-wrapper[_ngcontent-%COMP%]   .objective-list-wrapper[_ngcontent-%COMP%]   .obj-header[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]{height:3rem;width:12rem}.org-change-main[_ngcontent-%COMP%]   .main-wrapper[_ngcontent-%COMP%]   .objective-list-wrapper[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;height:26rem;color:#45546e!important}.org-change-main[_ngcontent-%COMP%]   .main-wrapper[_ngcontent-%COMP%]   .objective-list-wrapper[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]   .spinner[_ngcontent-%COMP%]{color:#45546e!important}.org-change-main[_ngcontent-%COMP%]   .main-wrapper[_ngcontent-%COMP%]   .objective-list-wrapper[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;flex-direction:column;gap:5%;height:18rem;color:var(--Blue-Grey-100,#45546e);font-family:Roboto;font-size:13px;font-style:normal;font-weight:500;line-height:16px;letter-spacing:.24px;text-transform:capitalize;height:23rem}.org-change-main[_ngcontent-%COMP%]   .main-wrapper[_ngcontent-%COMP%]   .objective-list-wrapper[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:180px}.org-change-main[_ngcontent-%COMP%]   .main-wrapper[_ngcontent-%COMP%]   .objective-list-wrapper[_ngcontent-%COMP%]   .check-main[_ngcontent-%COMP%]{height:80%!important;overflow-y:auto}.org-change-main[_ngcontent-%COMP%]   .main-wrapper[_ngcontent-%COMP%]   .objective-list-wrapper[_ngcontent-%COMP%]   .check-main[_ngcontent-%COMP%]   .check-lis[_ngcontent-%COMP%]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;width:70%}.org-change-main[_ngcontent-%COMP%]   .main-wrapper[_ngcontent-%COMP%]   .organisation-list-wrapper[_ngcontent-%COMP%]{height:-webkit-fill-available;border-radius:8px;border:1px solid var(--Blue-Grey-30,#e8e9ee);background:var(--Neutral-White,#fff);width:23rem}.org-change-main[_ngcontent-%COMP%]   .main-wrapper[_ngcontent-%COMP%]   .organisation-list-wrapper[_ngcontent-%COMP%]   .org-lis[_ngcontent-%COMP%]{margin-top:.75rem}.org-change-main[_ngcontent-%COMP%]   .main-wrapper[_ngcontent-%COMP%]   .organisation-list-wrapper[_ngcontent-%COMP%]   hr[_ngcontent-%COMP%]{color:#e8e9ee;margin-top:.5rem}.org-change-main[_ngcontent-%COMP%]   .main-wrapper[_ngcontent-%COMP%]   .organisation-list-wrapper[_ngcontent-%COMP%]   .org-header[_ngcontent-%COMP%]{color:var(--Blue-Grey-100,#45546e);font-family:Roboto;font-size:14px;font-style:normal;font-weight:700;line-height:24px;letter-spacing:.28px;text-transform:capitalize}.org-change-main[_ngcontent-%COMP%]   .main-wrapper[_ngcontent-%COMP%]   .organisation-list-wrapper[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%]{height:3rem;width:12rem}.org-change-main[_ngcontent-%COMP%]   .main-wrapper[_ngcontent-%COMP%]   .organisation-list-wrapper[_ngcontent-%COMP%]   .radio-shack[_ngcontent-%COMP%]{display:flex;align-items:flex-start;justify-content:start;flex-direction:column;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;color:var(--Blue-Grey-80,#5f6c81);font-family:Roboto;font-size:14px;font-style:normal;font-weight:400;line-height:24px;letter-spacing:.28px;text-transform:capitalize;height:75%;overflow-y:auto}.org-change-main[_ngcontent-%COMP%]   .main-wrapper[_ngcontent-%COMP%]   .organisation-list-wrapper[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;height:26rem;color:#45546e!important}.org-change-main[_ngcontent-%COMP%]   .main-wrapper[_ngcontent-%COMP%]   .organisation-list-wrapper[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]   .spinner[_ngcontent-%COMP%]{color:#45546e!important}.org-change-main[_ngcontent-%COMP%]   .main-wrapper[_ngcontent-%COMP%]   .organisation-list-wrapper[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;flex-direction:column;gap:5%;height:18rem;color:var(--Blue-Grey-100,#45546e);font-family:Roboto;font-size:13px;font-style:normal;font-weight:500;line-height:16px;letter-spacing:.24px;text-transform:capitalize;height:23rem}.org-change-main[_ngcontent-%COMP%]   .main-wrapper[_ngcontent-%COMP%]   .organisation-list-wrapper[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:180px}.org-change-main[_ngcontent-%COMP%]   .buttons-wrapper[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;padding-top:inherit}.org-change-main[_ngcontent-%COMP%]   .buttons-wrapper[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%]{color:#fff;background-color:#ff5f5f}.org-change-main[_ngcontent-%COMP%]   .buttons-wrapper[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%], .org-change-main[_ngcontent-%COMP%]   .buttons-wrapper[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%]{font-size:13px;font-weight:700!important;display:flex;justify-content:center;align-items:center;margin-right:3%;width:1rem;height:35px}.org-change-main[_ngcontent-%COMP%]   .buttons-wrapper[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{color:#45546e;background-color:initial!important;border:1px solid #45546e;margin-left:auto}.org-change-main[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-flex{height:2.5rem;align-items:center}"]}),e})();var X=i("+rOU"),q=i("2Vo4"),Q="undefined"!=typeof window&&"undefined"!=typeof document&&"undefined"!=typeof navigator,$=function(){for(var e=["Edge","Trident","Firefox"],t=0;t<e.length;t+=1)if(Q&&navigator.userAgent.indexOf(e[t])>=0)return 1;return 0}(),Z=Q&&window.Promise?function(e){var t=!1;return function(){t||(t=!0,window.Promise.resolve().then((function(){t=!1,e()})))}}:function(e){var t=!1;return function(){t||(t=!0,setTimeout((function(){t=!1,e()}),$))}};function ee(e){return e&&"[object Function]"==={}.toString.call(e)}function te(e,t){if(1!==e.nodeType)return[];var i=e.ownerDocument.defaultView.getComputedStyle(e,null);return t?i[t]:i}function ie(e){return"HTML"===e.nodeName?e:e.parentNode||e.host}function ae(e){if(!e)return document.body;switch(e.nodeName){case"HTML":case"BODY":return e.ownerDocument.body;case"#document":return e.body}var t=te(e);return/(auto|scroll|overlay)/.test(t.overflow+t.overflowY+t.overflowX)?e:ae(ie(e))}function re(e){return e&&e.referenceNode?e.referenceNode:e}var ne=Q&&!(!window.MSInputMethodContext||!document.documentMode),oe=Q&&/MSIE 10/.test(navigator.userAgent);function se(e){return 11===e?ne:10===e?oe:ne||oe}function de(e){if(!e)return document.documentElement;for(var t=se(10)?document.body:null,i=e.offsetParent||null;i===t&&e.nextElementSibling;)i=(e=e.nextElementSibling).offsetParent;var a=i&&i.nodeName;return a&&"BODY"!==a&&"HTML"!==a?-1!==["TH","TD","TABLE"].indexOf(i.nodeName)&&"static"===te(i,"position")?de(i):i:e?e.ownerDocument.documentElement:document.documentElement}function le(e){return null!==e.parentNode?le(e.parentNode):e}function ce(e,t){if(!(e&&e.nodeType&&t&&t.nodeType))return document.documentElement;var i=e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_FOLLOWING,a=i?e:t,r=i?t:e,n=document.createRange();n.setStart(a,0),n.setEnd(r,0);var o,s,d=n.commonAncestorContainer;if(e!==d&&t!==d||a.contains(r))return"BODY"===(s=(o=d).nodeName)||"HTML"!==s&&de(o.firstElementChild)!==o?de(d):d;var l=le(e);return l.host?ce(l.host,t):ce(e,le(t).host)}function pe(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"top",i="top"===t?"scrollTop":"scrollLeft",a=e.nodeName;if("BODY"===a||"HTML"===a){var r=e.ownerDocument.documentElement,n=e.ownerDocument.scrollingElement||r;return n[i]}return e[i]}function he(e,t){var i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],a=pe(t,"top"),r=pe(t,"left"),n=i?-1:1;return e.top+=a*n,e.bottom+=a*n,e.left+=r*n,e.right+=r*n,e}function ue(e,t){var i="x"===t?"Left":"Top",a="Left"===i?"Right":"Bottom";return parseFloat(e["border"+i+"Width"])+parseFloat(e["border"+a+"Width"])}function me(e,t,i,a){return Math.max(t["offset"+e],t["scroll"+e],i["client"+e],i["offset"+e],i["scroll"+e],se(10)?parseInt(i["offset"+e])+parseInt(a["margin"+("Height"===e?"Top":"Left")])+parseInt(a["margin"+("Height"===e?"Bottom":"Right")]):0)}function fe(e){var t=e.body,i=e.documentElement,a=se(10)&&getComputedStyle(i);return{height:me("Height",t,i,a),width:me("Width",t,i,a)}}var ge=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},ye=function(){function e(e,t){for(var i=0;i<t.length;i++){var a=t[i];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,a.key,a)}}return function(t,i,a){return i&&e(t.prototype,i),a&&e(t,a),t}}(),ve=function(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e},be=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(e[a]=i[a])}return e};function Se(e){return be({},e,{right:e.left+e.width,bottom:e.top+e.height})}function Ce(e){var t={};try{if(se(10)){t=e.getBoundingClientRect();var i=pe(e,"top"),a=pe(e,"left");t.top+=i,t.left+=a,t.bottom+=i,t.right+=a}else t=e.getBoundingClientRect()}catch(l){}var r={left:t.left,top:t.top,width:t.right-t.left,height:t.bottom-t.top},n="HTML"===e.nodeName?fe(e.ownerDocument):{},o=e.offsetWidth-(n.width||e.clientWidth||r.width),s=e.offsetHeight-(n.height||e.clientHeight||r.height);if(o||s){var d=te(e);o-=ue(d,"x"),s-=ue(d,"y"),r.width-=o,r.height-=s}return Se(r)}function De(e,t){var i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],a=se(10),r="HTML"===t.nodeName,n=Ce(e),o=Ce(t),s=ae(e),d=te(t),l=parseFloat(d.borderTopWidth),c=parseFloat(d.borderLeftWidth);i&&r&&(o.top=Math.max(o.top,0),o.left=Math.max(o.left,0));var p=Se({top:n.top-o.top-l,left:n.left-o.left-c,width:n.width,height:n.height});if(p.marginTop=0,p.marginLeft=0,!a&&r){var h=parseFloat(d.marginTop),u=parseFloat(d.marginLeft);p.top-=l-h,p.bottom-=l-h,p.left-=c-u,p.right-=c-u,p.marginTop=h,p.marginLeft=u}return(a&&!i?t.contains(s):t===s&&"BODY"!==s.nodeName)&&(p=he(p,t)),p}function Oe(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=e.ownerDocument.documentElement,a=De(e,i),r=Math.max(i.clientWidth,window.innerWidth||0),n=Math.max(i.clientHeight,window.innerHeight||0),o=t?0:pe(i),s=t?0:pe(i,"left"),d={top:o-a.top+a.marginTop,left:s-a.left+a.marginLeft,width:r,height:n};return Se(d)}function _e(e){var t=e.nodeName;if("BODY"===t||"HTML"===t)return!1;if("fixed"===te(e,"position"))return!0;var i=ie(e);return!!i&&_e(i)}function Te(e){if(!e||!e.parentElement||se())return document.documentElement;for(var t=e.parentElement;t&&"none"===te(t,"transform");)t=t.parentElement;return t||document.documentElement}function xe(e,t,i,a){var r=arguments.length>4&&void 0!==arguments[4]&&arguments[4],n={top:0,left:0},o=r?Te(e):ce(e,re(t));if("viewport"===a)n=Oe(o,r);else{var s=void 0;"scrollParent"===a?"BODY"===(s=ae(ie(t))).nodeName&&(s=e.ownerDocument.documentElement):s="window"===a?e.ownerDocument.documentElement:a;var d=De(s,o,r);if("HTML"!==s.nodeName||_e(o))n=d;else{var l=fe(e.ownerDocument),c=l.height,p=l.width;n.top+=d.top-d.marginTop,n.bottom=c+d.top,n.left+=d.left-d.marginLeft,n.right=p+d.left}}var h="number"==typeof(i=i||0);return n.left+=h?i:i.left||0,n.top+=h?i:i.top||0,n.right-=h?i:i.right||0,n.bottom-=h?i:i.bottom||0,n}function we(e){return e.width*e.height}function Ae(e,t,i,a,r){var n=arguments.length>5&&void 0!==arguments[5]?arguments[5]:0;if(-1===e.indexOf("auto"))return e;var o=xe(i,a,n,r),s={top:{width:o.width,height:t.top-o.top},right:{width:o.right-t.right,height:o.height},bottom:{width:o.width,height:o.bottom-t.bottom},left:{width:t.left-o.left,height:o.height}},d=Object.keys(s).map((function(e){return be({key:e},s[e],{area:we(s[e])})})).sort((function(e,t){return t.area-e.area})),l=d.filter((function(e){return e.width>=i.clientWidth&&e.height>=i.clientHeight})),c=l.length>0?l[0].key:d[0].key,p=e.split("-")[1];return c+(p?"-"+p:"")}function ke(e,t,i){var a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,r=a?Te(t):ce(t,re(i));return De(i,r,a)}function Ee(e){var t=e.ownerDocument.defaultView.getComputedStyle(e),i=parseFloat(t.marginTop||0)+parseFloat(t.marginBottom||0),a=parseFloat(t.marginLeft||0)+parseFloat(t.marginRight||0);return{width:e.offsetWidth+a,height:e.offsetHeight+i}}function Me(e){var t={left:"right",right:"left",bottom:"top",top:"bottom"};return e.replace(/left|right|bottom|top/g,(function(e){return t[e]}))}function Ie(e,t,i){i=i.split("-")[0];var a=Ee(e),r={width:a.width,height:a.height},n=-1!==["right","left"].indexOf(i),o=n?"top":"left",s=n?"left":"top",d=n?"height":"width",l=n?"width":"height";return r[o]=t[o]+t[d]/2-a[d]/2,r[s]=i===s?t[s]-a[l]:t[Me(s)],r}function Pe(e,t){return Array.prototype.find?e.find(t):e.filter(t)[0]}function je(e,t,i){return(void 0===i?e:e.slice(0,function(e,t,i){if(Array.prototype.findIndex)return e.findIndex((function(e){return e.name===i}));var a=Pe(e,(function(e){return e.name===i}));return e.indexOf(a)}(e,0,i))).forEach((function(e){e.function&&console.warn("`modifier.function` is deprecated, use `modifier.fn`!");var i=e.function||e.fn;e.enabled&&ee(i)&&(t.offsets.popper=Se(t.offsets.popper),t.offsets.reference=Se(t.offsets.reference),t=i(t,e))})),t}function Re(){if(!this.state.isDestroyed){var e={instance:this,styles:{},arrowStyles:{},attributes:{},flipped:!1,offsets:{}};e.offsets.reference=ke(this.state,this.popper,this.reference,this.options.positionFixed),e.placement=Ae(this.options.placement,e.offsets.reference,this.popper,this.reference,this.options.modifiers.flip.boundariesElement,this.options.modifiers.flip.padding),e.originalPlacement=e.placement,e.positionFixed=this.options.positionFixed,e.offsets.popper=Ie(this.popper,e.offsets.reference,e.placement),e.offsets.popper.position=this.options.positionFixed?"fixed":"absolute",e=je(this.modifiers,e),this.state.isCreated?this.options.onUpdate(e):(this.state.isCreated=!0,this.options.onCreate(e))}}function Ne(e,t){return e.some((function(e){return e.enabled&&e.name===t}))}function Ve(e){for(var t=[!1,"ms","Webkit","Moz","O"],i=e.charAt(0).toUpperCase()+e.slice(1),a=0;a<t.length;a++){var r=t[a],n=r?""+r+i:e;if(void 0!==document.body.style[n])return n}return null}function Fe(){return this.state.isDestroyed=!0,Ne(this.modifiers,"applyStyle")&&(this.popper.removeAttribute("x-placement"),this.popper.style.position="",this.popper.style.top="",this.popper.style.left="",this.popper.style.right="",this.popper.style.bottom="",this.popper.style.willChange="",this.popper.style[Ve("transform")]=""),this.disableEventListeners(),this.options.removeOnDestroy&&this.popper.parentNode.removeChild(this.popper),this}function Ue(e){var t=e.ownerDocument;return t?t.defaultView:window}function Ke(){this.state.eventsEnabled||(this.state=function(e,t,i,a){i.updateBound=a,Ue(e).addEventListener("resize",i.updateBound,{passive:!0});var r=ae(e);return function e(t,i,a,r){var n="BODY"===t.nodeName,o=n?t.ownerDocument.defaultView:t;o.addEventListener(i,a,{passive:!0}),n||e(ae(o.parentNode),i,a,r),r.push(o)}(r,"scroll",i.updateBound,i.scrollParents),i.scrollElement=r,i.eventsEnabled=!0,i}(this.reference,0,this.state,this.scheduleUpdate))}function Le(){var e;this.state.eventsEnabled&&(cancelAnimationFrame(this.scheduleUpdate),this.state=(e=this.state,Ue(this.reference).removeEventListener("resize",e.updateBound),e.scrollParents.forEach((function(t){t.removeEventListener("scroll",e.updateBound)})),e.updateBound=null,e.scrollParents=[],e.scrollElement=null,e.eventsEnabled=!1,e))}function He(e){return""!==e&&!isNaN(parseFloat(e))&&isFinite(e)}function Ye(e,t){Object.keys(t).forEach((function(i){var a="";-1!==["width","height","top","right","bottom","left"].indexOf(i)&&He(t[i])&&(a="px"),e.style[i]=t[i]+a}))}var ze=Q&&/Firefox/i.test(navigator.userAgent);function Be(e,t,i){var a=Pe(e,(function(e){return e.name===t})),r=!!a&&e.some((function(e){return e.name===i&&e.enabled&&e.order<a.order}));if(!r){var n="`"+t+"`";console.warn("`"+i+"` modifier is required by "+n+" modifier in order to work, be sure to include it before "+n+"!")}return r}var We=["auto-start","auto","auto-end","top-start","top","top-end","right-start","right","right-end","bottom-end","bottom","bottom-start","left-end","left","left-start"],Je=We.slice(3);function Ge(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=Je.indexOf(e),a=Je.slice(i+1).concat(Je.slice(0,i));return t?a.reverse():a}var Xe={placement:"bottom",positionFixed:!1,eventsEnabled:!0,removeOnDestroy:!1,onCreate:function(){},onUpdate:function(){},modifiers:{shift:{order:100,enabled:!0,fn:function(e){var t=e.placement,i=t.split("-")[0],a=t.split("-")[1];if(a){var r=e.offsets,n=r.reference,o=r.popper,s=-1!==["bottom","top"].indexOf(i),d=s?"left":"top",l=s?"width":"height",c={start:ve({},d,n[d]),end:ve({},d,n[d]+n[l]-o[l])};e.offsets.popper=be({},o,c[a])}return e}},offset:{order:200,enabled:!0,fn:function(e,t){var i,a=t.offset,r=e.offsets,n=r.popper,o=r.reference,s=e.placement.split("-")[0];return i=He(+a)?[+a,0]:function(e,t,i,a){var r=[0,0],n=-1!==["right","left"].indexOf(a),o=e.split(/(\+|\-)/).map((function(e){return e.trim()})),s=o.indexOf(Pe(o,(function(e){return-1!==e.search(/,|\s/)})));o[s]&&-1===o[s].indexOf(",")&&console.warn("Offsets separated by white space(s) are deprecated, use a comma (,) instead.");var d=/\s*,\s*|\s+/,l=-1!==s?[o.slice(0,s).concat([o[s].split(d)[0]]),[o[s].split(d)[1]].concat(o.slice(s+1))]:[o];return(l=l.map((function(e,a){var r=(1===a?!n:n)?"height":"width",o=!1;return e.reduce((function(e,t){return""===e[e.length-1]&&-1!==["+","-"].indexOf(t)?(e[e.length-1]=t,o=!0,e):o?(e[e.length-1]+=t,o=!1,e):e.concat(t)}),[]).map((function(e){return function(e,t,i,a){var r=e.match(/((?:\-|\+)?\d*\.?\d*)(.*)/),n=+r[1],o=r[2];if(!n)return e;if(0===o.indexOf("%")){var s=void 0;switch(o){case"%p":s=i;break;case"%":case"%r":default:s=a}return Se(s)[t]/100*n}return"vh"===o||"vw"===o?("vh"===o?Math.max(document.documentElement.clientHeight,window.innerHeight||0):Math.max(document.documentElement.clientWidth,window.innerWidth||0))/100*n:n}(e,r,t,i)}))}))).forEach((function(e,t){e.forEach((function(i,a){He(i)&&(r[t]+=i*("-"===e[a-1]?-1:1))}))})),r}(a,n,o,s),"left"===s?(n.top+=i[0],n.left-=i[1]):"right"===s?(n.top+=i[0],n.left+=i[1]):"top"===s?(n.left+=i[0],n.top-=i[1]):"bottom"===s&&(n.left+=i[0],n.top+=i[1]),e.popper=n,e},offset:0},preventOverflow:{order:300,enabled:!0,fn:function(e,t){var i=t.boundariesElement||de(e.instance.popper);e.instance.reference===i&&(i=de(i));var a=Ve("transform"),r=e.instance.popper.style,n=r.top,o=r.left,s=r[a];r.top="",r.left="",r[a]="";var d=xe(e.instance.popper,e.instance.reference,t.padding,i,e.positionFixed);r.top=n,r.left=o,r[a]=s,t.boundaries=d;var l=e.offsets.popper,c={primary:function(e){var i=l[e];return l[e]<d[e]&&!t.escapeWithReference&&(i=Math.max(l[e],d[e])),ve({},e,i)},secondary:function(e){var i="right"===e?"left":"top",a=l[i];return l[e]>d[e]&&!t.escapeWithReference&&(a=Math.min(l[i],d[e]-("right"===e?l.width:l.height))),ve({},i,a)}};return t.priority.forEach((function(e){var t=-1!==["left","top"].indexOf(e)?"primary":"secondary";l=be({},l,c[t](e))})),e.offsets.popper=l,e},priority:["left","right","top","bottom"],padding:5,boundariesElement:"scrollParent"},keepTogether:{order:400,enabled:!0,fn:function(e){var t=e.offsets,i=t.popper,a=t.reference,r=e.placement.split("-")[0],n=Math.floor,o=-1!==["top","bottom"].indexOf(r),s=o?"right":"bottom",d=o?"left":"top",l=o?"width":"height";return i[s]<n(a[d])&&(e.offsets.popper[d]=n(a[d])-i[l]),i[d]>n(a[s])&&(e.offsets.popper[d]=n(a[s])),e}},arrow:{order:500,enabled:!0,fn:function(e,t){var i;if(!Be(e.instance.modifiers,"arrow","keepTogether"))return e;var a=t.element;if("string"==typeof a){if(!(a=e.instance.popper.querySelector(a)))return e}else if(!e.instance.popper.contains(a))return console.warn("WARNING: `arrow.element` must be child of its popper element!"),e;var r=e.placement.split("-")[0],n=e.offsets,o=n.popper,s=n.reference,d=-1!==["left","right"].indexOf(r),l=d?"height":"width",c=d?"Top":"Left",p=c.toLowerCase(),h=d?"left":"top",u=d?"bottom":"right",m=Ee(a)[l];s[u]-m<o[p]&&(e.offsets.popper[p]-=o[p]-(s[u]-m)),s[p]+m>o[u]&&(e.offsets.popper[p]+=s[p]+m-o[u]),e.offsets.popper=Se(e.offsets.popper);var f=s[p]+s[l]/2-m/2,g=te(e.instance.popper),y=parseFloat(g["margin"+c]),v=parseFloat(g["border"+c+"Width"]),b=f-e.offsets.popper[p]-y-v;return b=Math.max(Math.min(o[l]-m,b),0),e.arrowElement=a,e.offsets.arrow=(ve(i={},p,Math.round(b)),ve(i,h,""),i),e},element:"[x-arrow]"},flip:{order:600,enabled:!0,fn:function(e,t){if(Ne(e.instance.modifiers,"inner"))return e;if(e.flipped&&e.placement===e.originalPlacement)return e;var i=xe(e.instance.popper,e.instance.reference,t.padding,t.boundariesElement,e.positionFixed),a=e.placement.split("-")[0],r=Me(a),n=e.placement.split("-")[1]||"",o=[];switch(t.behavior){case"flip":o=[a,r];break;case"clockwise":o=Ge(a);break;case"counterclockwise":o=Ge(a,!0);break;default:o=t.behavior}return o.forEach((function(s,d){if(a!==s||o.length===d+1)return e;a=e.placement.split("-")[0],r=Me(a);var l=e.offsets.popper,c=e.offsets.reference,p=Math.floor,h="left"===a&&p(l.right)>p(c.left)||"right"===a&&p(l.left)<p(c.right)||"top"===a&&p(l.bottom)>p(c.top)||"bottom"===a&&p(l.top)<p(c.bottom),u=p(l.left)<p(i.left),m=p(l.right)>p(i.right),f=p(l.top)<p(i.top),g=p(l.bottom)>p(i.bottom),y="left"===a&&u||"right"===a&&m||"top"===a&&f||"bottom"===a&&g,v=-1!==["top","bottom"].indexOf(a),b=!!t.flipVariations&&(v&&"start"===n&&u||v&&"end"===n&&m||!v&&"start"===n&&f||!v&&"end"===n&&g)||!!t.flipVariationsByContent&&(v&&"start"===n&&m||v&&"end"===n&&u||!v&&"start"===n&&g||!v&&"end"===n&&f);(h||y||b)&&(e.flipped=!0,(h||y)&&(a=o[d+1]),b&&(n=function(e){return"end"===e?"start":"start"===e?"end":e}(n)),e.placement=a+(n?"-"+n:""),e.offsets.popper=be({},e.offsets.popper,Ie(e.instance.popper,e.offsets.reference,e.placement)),e=je(e.instance.modifiers,e,"flip"))})),e},behavior:"flip",padding:5,boundariesElement:"viewport",flipVariations:!1,flipVariationsByContent:!1},inner:{order:700,enabled:!1,fn:function(e){var t=e.placement,i=t.split("-")[0],a=e.offsets,r=a.popper,n=a.reference,o=-1!==["left","right"].indexOf(i),s=-1===["top","left"].indexOf(i);return r[o?"left":"top"]=n[i]-(s?r[o?"width":"height"]:0),e.placement=Me(t),e.offsets.popper=Se(r),e}},hide:{order:800,enabled:!0,fn:function(e){if(!Be(e.instance.modifiers,"hide","preventOverflow"))return e;var t=e.offsets.reference,i=Pe(e.instance.modifiers,(function(e){return"preventOverflow"===e.name})).boundaries;if(t.bottom<i.top||t.left>i.right||t.top>i.bottom||t.right<i.left){if(!0===e.hide)return e;e.hide=!0,e.attributes["x-out-of-boundaries"]=""}else{if(!1===e.hide)return e;e.hide=!1,e.attributes["x-out-of-boundaries"]=!1}return e}},computeStyle:{order:850,enabled:!0,fn:function(e,t){var i=t.x,a=t.y,r=e.offsets.popper,n=Pe(e.instance.modifiers,(function(e){return"applyStyle"===e.name})).gpuAcceleration;void 0!==n&&console.warn("WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!");var o,s,d=void 0!==n?n:t.gpuAcceleration,l=de(e.instance.popper),c=Ce(l),p={position:r.position},h=function(e,t){var i=e.offsets,a=i.popper,r=Math.round,n=Math.floor,o=function(e){return e},s=r(i.reference.width),d=r(a.width),l=-1!==["left","right"].indexOf(e.placement),c=-1!==e.placement.indexOf("-"),p=t?l||c||s%2==d%2?r:n:o,h=t?r:o;return{left:p(s%2==1&&d%2==1&&!c&&t?a.left-1:a.left),top:h(a.top),bottom:h(a.bottom),right:p(a.right)}}(e,window.devicePixelRatio<2||!ze),u="bottom"===i?"top":"bottom",m="right"===a?"left":"right",f=Ve("transform");if(s="bottom"===u?"HTML"===l.nodeName?-l.clientHeight+h.bottom:-c.height+h.bottom:h.top,o="right"===m?"HTML"===l.nodeName?-l.clientWidth+h.right:-c.width+h.right:h.left,d&&f)p[f]="translate3d("+o+"px, "+s+"px, 0)",p[u]=0,p[m]=0,p.willChange="transform";else{var g="right"===m?-1:1;p[u]=s*("bottom"===u?-1:1),p[m]=o*g,p.willChange=u+", "+m}return e.attributes=be({},{"x-placement":e.placement},e.attributes),e.styles=be({},p,e.styles),e.arrowStyles=be({},e.offsets.arrow,e.arrowStyles),e},gpuAcceleration:!0,x:"bottom",y:"right"},applyStyle:{order:900,enabled:!0,fn:function(e){var t,i;return Ye(e.instance.popper,e.styles),t=e.instance.popper,i=e.attributes,Object.keys(i).forEach((function(e){!1!==i[e]?t.setAttribute(e,i[e]):t.removeAttribute(e)})),e.arrowElement&&Object.keys(e.arrowStyles).length&&Ye(e.arrowElement,e.arrowStyles),e},onLoad:function(e,t,i,a,r){var n=ke(r,t,e,i.positionFixed),o=Ae(i.placement,n,t,e,i.modifiers.flip.boundariesElement,i.modifiers.flip.padding);return t.setAttribute("x-placement",o),Ye(t,{position:i.positionFixed?"fixed":"absolute"}),i},gpuAcceleration:void 0}}},qe=function(){function e(t,i){var a=this,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};ge(this,e),this.scheduleUpdate=function(){return requestAnimationFrame(a.update)},this.update=Z(this.update.bind(this)),this.options=be({},e.Defaults,r),this.state={isDestroyed:!1,isCreated:!1,scrollParents:[]},this.reference=t&&t.jquery?t[0]:t,this.popper=i&&i.jquery?i[0]:i,this.options.modifiers={},Object.keys(be({},e.Defaults.modifiers,r.modifiers)).forEach((function(t){a.options.modifiers[t]=be({},e.Defaults.modifiers[t]||{},r.modifiers?r.modifiers[t]:{})})),this.modifiers=Object.keys(this.options.modifiers).map((function(e){return be({name:e},a.options.modifiers[e])})).sort((function(e,t){return e.order-t.order})),this.modifiers.forEach((function(e){e.enabled&&ee(e.onLoad)&&e.onLoad(a.reference,a.popper,a.options,e,a.state)})),this.update();var n=this.options.eventsEnabled;n&&this.enableEventListeners(),this.state.eventsEnabled=n}return ye(e,[{key:"update",value:function(){return Re.call(this)}},{key:"destroy",value:function(){return Fe.call(this)}},{key:"enableEventListeners",value:function(){return Ke.call(this)}},{key:"disableEventListeners",value:function(){return Le.call(this)}}]),e}();qe.Utils=("undefined"!=typeof window?window:global).PopperUtils,qe.placements=We,qe.Defaults=Xe;var Qe=qe;function $e(){return($e=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var a in i)Object.prototype.hasOwnProperty.call(i,a)&&(e[a]=i[a])}return e}).apply(this,arguments)}var Ze="undefined"!=typeof window,et=Ze&&navigator.userAgent,tt=/MSIE |Trident\//.test(et),it=/UCBrowser\//.test(et),at=Ze&&/iPhone|iPad|iPod/.test(navigator.platform)&&!window.MSStream,rt={a11y:!0,allowHTML:!0,animateFill:!0,animation:"shift-away",appendTo:function(){return document.body},aria:"describedby",arrow:!1,arrowType:"sharp",boundary:"scrollParent",content:"",delay:[0,20],distance:10,duration:[325,275],flip:!0,flipBehavior:"flip",flipOnUpdate:!1,followCursor:!1,hideOnClick:!0,ignoreAttributes:!1,inertia:!1,interactive:!1,interactiveBorder:2,interactiveDebounce:0,lazy:!0,maxWidth:350,multiple:!1,offset:0,onHidden:function(){},onHide:function(){},onMount:function(){},onShow:function(){},onShown:function(){},placement:"top",popperOptions:{},role:"tooltip",showOnInit:!1,size:"regular",sticky:!1,target:"",theme:"dark",touch:!0,touchHold:!1,trigger:"mouseenter focus",updateDuration:0,wait:null,zIndex:9999},nt=["arrow","arrowType","boundary","distance","flip","flipBehavior","flipOnUpdate","offset","placement","popperOptions"],ot=".tippy-popper",st=Ze?Element.prototype:{},dt=st.matches||st.matchesSelector||st.webkitMatchesSelector||st.mozMatchesSelector||st.msMatchesSelector;function lt(e){return[].slice.call(e)}function ct(e,t){return(st.closest||function(e){for(var t=this;t;){if(dt.call(t,e))return t;t=t.parentElement}}).call(e,t)}function pt(e,t){for(;e;){if(t(e))return e;e=e.parentElement}}function ht(e,t){return{}.hasOwnProperty.call(e,t)}function ut(e,t,i){if(Array.isArray(e)){var a=e[t];return null==a?i:a}return e}function mt(e,t){var i;return function(){var a=this,r=arguments;clearTimeout(i),i=setTimeout((function(){return e.apply(a,r)}),t)}}function ft(e,t){return e&&e.modifiers&&e.modifiers[t]}function gt(e,t){return e.indexOf(t)>-1}function yt(e){return!(!e||!ht(e,"isVirtual"))||e instanceof Element}function vt(e,t){return"function"==typeof e?e.apply(null,t):e}function bt(e,t){e.filter((function(e){return"flip"===e.name}))[0].enabled=t}function St(){return document.createElement("div")}function Ct(e,t){e.innerHTML=t instanceof Element?t.innerHTML:t}function Dt(e,t){t.content instanceof Element?(Ct(e,""),e.appendChild(t.content)):e[t.allowHTML?"innerHTML":"textContent"]=t.content}function Ot(e){return{tooltip:e.querySelector(".tippy-tooltip"),backdrop:e.querySelector(".tippy-backdrop"),content:e.querySelector(".tippy-content"),arrow:e.querySelector(".tippy-arrow")||e.querySelector(".tippy-roundarrow")}}function _t(e){e.setAttribute("data-inertia","")}function Tt(e){var t=St();return"round"===e?(t.className="tippy-roundarrow",Ct(t,'<svg viewBox="0 0 18 7" xmlns="http://www.w3.org/2000/svg"><path d="M0 7s2.021-.015 5.253-4.218C6.584 1.051 7.797.007 9 0c1.203-.007 2.416 1.035 3.761 2.782C16.012 7.005 18 7 18 7H0z"/></svg>')):t.className="tippy-arrow",t}function xt(){var e=St();return e.className="tippy-backdrop",e.setAttribute("data-state","hidden"),e}function wt(e,t){e.setAttribute("tabindex","-1"),t.setAttribute("data-interactive","")}function At(e,t){e.forEach((function(e){e&&(e.style.transitionDuration="".concat(t,"ms"))}))}function kt(e,t,i){var a=it&&void 0!==document.body.style.webkitTransition?"webkitTransitionEnd":"transitionend";e[t+"EventListener"](a,i)}function Et(e){var t=e.getAttribute("x-placement");return t?t.split("-")[0]:""}function Mt(e,t){e.forEach((function(e){e&&e.setAttribute("data-state",t)}))}function It(e,t,i){i.split(" ").forEach((function(i){e.classList[t](i+"-theme")}))}function Pt(e,t,i){var a=Ot(e),r=a.tooltip,n=a.content,o=a.backdrop,s=a.arrow;e.style.zIndex=i.zIndex,r.setAttribute("data-size",i.size),r.setAttribute("data-animation",i.animation),r.style.maxWidth=i.maxWidth+("number"==typeof i.maxWidth?"px":""),i.role?e.setAttribute("role",i.role):e.removeAttribute("role"),t.content!==i.content&&Dt(n,i),!t.animateFill&&i.animateFill?(r.appendChild(xt()),r.setAttribute("data-animatefill","")):t.animateFill&&!i.animateFill&&(r.removeChild(o),r.removeAttribute("data-animatefill")),!t.arrow&&i.arrow?r.appendChild(Tt(i.arrowType)):t.arrow&&!i.arrow&&r.removeChild(s),t.arrow&&i.arrow&&t.arrowType!==i.arrowType&&r.replaceChild(Tt(i.arrowType),s),!t.interactive&&i.interactive?wt(e,r):t.interactive&&!i.interactive&&function(e,t){e.removeAttribute("tabindex"),t.removeAttribute("data-interactive")}(e,r),!t.inertia&&i.inertia?_t(r):t.inertia&&!i.inertia&&function(e){e.removeAttribute("data-inertia")}(r),t.theme!==i.theme&&(It(r,"remove",t.theme),It(r,"add",i.theme))}function jt(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.checkHideOnClick,i=e.exclude,a=e.duration;lt(document.querySelectorAll(ot)).forEach((function(e){var r=e._tippy;!r||t&&!0!==r.props.hideOnClick||i&&e===i.popper||r.hide(a)}))}var Rt={passive:!0},Nt=!1;function Vt(){Nt||(Nt=!0,at&&document.body.classList.add("tippy-iOS"),window.performance&&document.addEventListener("mousemove",Ut))}var Ft=0;function Ut(){var e=performance.now();e-Ft<20&&(Nt=!1,document.removeEventListener("mousemove",Ut),at||document.body.classList.remove("tippy-iOS")),Ft=e}function Kt(e){var t=e.target;if(!(t instanceof Element))return jt();var i=ct(t,ot);if(!(i&&i._tippy&&i._tippy.props.interactive)){var a=pt(t,(function(e){return e._tippy&&e._tippy.reference===e}));if(a){var r=a._tippy,n=gt(r.props.trigger,"click");if(Nt||n)return jt({exclude:r,checkHideOnClick:!0});if(!0!==r.props.hideOnClick||n)return;r.clearDelayTimeouts()}jt({checkHideOnClick:!0})}}function Lt(){var e=document.activeElement;e&&e.blur&&e._tippy&&e.blur()}var Ht=Object.keys(rt);function Yt(e,t){var i=$e({},t,{content:vt(t.content,[e])},t.ignoreAttributes?{}:function(e){return Ht.reduce((function(t,i){var a=(e.getAttribute("data-tippy-".concat(i))||"").trim();if(!a)return t;if("content"===i)t[i]=a;else try{t[i]=JSON.parse(a)}catch(r){t[i]=a}return t}),{})}(e));return(i.arrow||it)&&(i.animateFill=!1),i}function zt(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;Object.keys(e).forEach((function(e){if(!ht(t,e))throw new Error("[tippy]: `".concat(e,"` is not a valid option"))}))}var Bt=1,Wt=!1;function Jt(e,t){zt(t,rt),Wt||(document.addEventListener("click",Kt,!0),document.addEventListener("touchstart",Vt,Rt),window.addEventListener("blur",Lt),Wt=!0);var i,a=$e({},rt,t);"[object Object]"!=={}.toString.call(i=e)||i.addEventListener||function(e){var t={isVirtual:!0,attributes:e.attributes||{},setAttribute:function(t,i){e.attributes[t]=i},getAttribute:function(t){return e.attributes[t]},removeAttribute:function(t){delete e.attributes[t]},hasAttribute:function(t){return t in e.attributes},addEventListener:function(){},removeEventListener:function(){},classList:{classNames:{},add:function(t){e.classList.classNames[t]=!0},remove:function(t){delete e.classList.classNames[t]},contains:function(t){return t in e.classList.classNames}}};for(var i in t)e[i]=t[i]}(e);var r=function(e){if(yt(e))return[e];if(e instanceof NodeList)return lt(e);if(Array.isArray(e))return e;try{return lt(document.querySelectorAll(e))}catch(t){return[]}}(e).reduce((function(e,t){var i=t&&function e(t,i){var a=Yt(t,i);if(!a.multiple&&t._tippy)return null;var r={},n=null,o=0,s=0,d=!1,l=function(){},c=[],p=a.interactiveDebounce>0?mt(O,a.interactiveDebounce):O,h=null,u=Bt++,m=function(e,t){var i=St();i.className="tippy-popper",i.id="tippy-".concat(e),i.style.zIndex=t.zIndex,t.role&&i.setAttribute("role",t.role);var a=St();a.className="tippy-tooltip",a.style.maxWidth=t.maxWidth+("number"==typeof t.maxWidth?"px":""),a.setAttribute("data-size",t.size),a.setAttribute("data-animation",t.animation),a.setAttribute("data-state","hidden"),It(a,"add",t.theme);var r=St();return r.className="tippy-content",r.setAttribute("data-state","hidden"),t.interactive&&wt(i,a),t.arrow&&a.appendChild(Tt(t.arrowType)),t.animateFill&&(a.appendChild(xt()),a.setAttribute("data-animatefill","")),t.inertia&&_t(a),Dt(r,t),a.appendChild(r),i.appendChild(a),i}(u,a);m.addEventListener("mouseenter",(function(e){g.props.interactive&&g.state.isVisible&&"mouseenter"===r.type&&v(e)})),m.addEventListener("mouseleave",(function(){g.props.interactive&&"mouseenter"===r.type&&document.addEventListener("mousemove",p)}));var f,g={id:u,reference:t,popper:m,popperChildren:Ot(m),popperInstance:null,props:a,state:{isEnabled:!0,isVisible:!1,isDestroyed:!1,isMounted:!1,isShown:!1},clearDelayTimeouts:K,set:L,setContent:function(e){L({content:e})},show:H,hide:Y,enable:function(){g.state.isEnabled=!0},disable:function(){g.state.isEnabled=!1},destroy:z};return V(),a.lazy||(k(),g.popperInstance.disableEventListeners()),a.showOnInit&&v(),a.a11y&&!a.target&&(f=t)instanceof Element&&(!dt.call(f,"a[href],area[href],button,details,input,textarea,select,iframe,[tabindex]")||f.hasAttribute("disabled"))&&t.setAttribute("tabindex","0"),t._tippy=g,m._tippy=g,g;function y(e){var t=n=e,i=t.clientX,a=t.clientY;if(g.popperInstance){var r=Et(g.popper),o=g.props.arrow?4+("round"===g.props.arrowType?18:16):4,s=gt(["top","bottom"],r),d=gt(["left","right"],r),l=s?Math.max(o,i):i,c=d?Math.max(o,a):a;s&&l>o&&(l=Math.min(i,window.innerWidth-o)),d&&c>o&&(c=Math.min(a,window.innerHeight-o));var p=g.reference.getBoundingClientRect(),h=g.props.followCursor,u="horizontal"===h,m="vertical"===h;g.popperInstance.reference={getBoundingClientRect:function(){return{width:0,height:0,top:u?p.top:c,bottom:u?p.bottom:c,left:m?p.left:l,right:m?p.right:l}},clientWidth:0,clientHeight:0},g.popperInstance.scheduleUpdate(),"initial"===h&&g.state.isVisible&&S()}}function v(t){if(K(),!g.state.isVisible){if(g.props.target)return function(t){var a=ct(t.target,g.props.target);a&&!a._tippy&&(e(a,$e({},g.props,{content:vt(i.content,[a]),appendTo:i.appendTo,target:"",showOnInit:!0})),v(t))}(t);if(d=!0,g.props.wait)return g.props.wait(g,t);M()&&!g.state.isMounted&&document.addEventListener("mousemove",y);var a=ut(g.props.delay,0,rt.delay);a?o=setTimeout((function(){H()}),a):H()}}function b(){if(K(),!g.state.isVisible)return S();d=!1;var e=ut(g.props.delay,1,rt.delay);e?s=setTimeout((function(){g.state.isVisible&&Y()}),e):Y()}function S(){document.removeEventListener("mousemove",y)}function C(){document.body.removeEventListener("mouseleave",b),document.removeEventListener("mousemove",p)}function D(e){g.state.isEnabled&&!A(e)&&(g.state.isVisible||(r=e,e instanceof MouseEvent&&(n=e)),"click"===e.type&&!1!==g.props.hideOnClick&&g.state.isVisible?b():v(e))}function O(e){var t=pt(e.target,(function(e){return e._tippy}));ct(e.target,ot)===g.popper||t===g.reference||function(e,t,i,a){if(!e)return!0;var r=i.clientX,n=i.clientY,o=a.interactiveBorder,s=a.distance;return t.top-n>("top"===e?o+s:o)||n-t.bottom>("bottom"===e?o+s:o)||t.left-r>("left"===e?o+s:o)||r-t.right>("right"===e?o+s:o)}(Et(g.popper),g.popper.getBoundingClientRect(),e,g.props)&&(C(),b())}function _(e){if(!A(e))return g.props.interactive?(document.body.addEventListener("mouseleave",b),void document.addEventListener("mousemove",p)):void b()}function T(e){e.target===g.reference&&(g.props.interactive&&e.relatedTarget&&g.popper.contains(e.relatedTarget)||b())}function x(e){ct(e.target,g.props.target)&&v(e)}function w(e){ct(e.target,g.props.target)&&b()}function A(e){var t="ontouchstart"in window,i=gt(e.type,"touch"),a=g.props.touchHold;return t&&Nt&&a&&!i||Nt&&!a&&i}function k(){var e=g.props.popperOptions,t=g.popperChildren,i=t.tooltip,a=t.arrow;g.popperInstance=new Qe(g.reference,g.popper,$e({placement:g.props.placement},e,{modifiers:$e({},e?e.modifiers:{},{preventOverflow:$e({boundariesElement:g.props.boundary,padding:4},ft(e,"preventOverflow")),arrow:$e({element:a,enabled:!!a},ft(e,"arrow")),flip:$e({enabled:g.props.flip,padding:g.props.distance+4,behavior:g.props.flipBehavior},ft(e,"flip")),offset:$e({offset:g.props.offset},ft(e,"offset"))}),onUpdate:function(t){g.props.flip&&!g.props.flipOnUpdate&&(t.flipped&&(g.popperInstance.options.placement=t.placement),bt(g.popperInstance.modifiers,!1));var a=Et(g.popper),r=i.style;r.top="",r.bottom="",r.left="",r.right="",r[a]=-(g.props.distance-10)+"px";var n={top:4,bottom:4,left:4,right:4};n[a]=4+g.props.distance,g.popperInstance.modifiers.filter((function(e){return"preventOverflow"===e.name}))[0].padding=n,e&&e.onUpdate&&e.onUpdate(t)}}))}function E(e){var t=!(M()||"initial"===g.props.followCursor&&Nt);g.popperInstance?(M()||(g.popperInstance.scheduleUpdate(),t&&g.popperInstance.enableEventListeners()),bt(g.popperInstance.modifiers,g.props.flip)):(k(),t||g.popperInstance.disableEventListeners()),g.popperInstance.reference=g.reference;var i=g.popperChildren.arrow;if(M()){i&&(i.style.margin="0");var a=ut(g.props.delay,0,rt.delay);r.type&&y(a&&n?n:r)}else i&&(i.style.margin="");Nt&&n&&"initial"===g.props.followCursor&&(y(n),i&&(i.style.margin="0")),function(e,t){var i=e.options,a=i.onCreate,r=i.onUpdate;i.onCreate=i.onUpdate=function(e){t(),r(e),i.onCreate=a,i.onUpdate=r}}(g.popperInstance,e);var o=g.props.appendTo;(h="parent"===o?g.reference.parentNode:vt(o,[g.reference])).contains(g.popper)||(h.appendChild(g.popper),g.props.onMount(g),g.state.isMounted=!0)}function M(){return g.props.followCursor&&!Nt&&"focus"!==r.type}function I(){At([g.popper],tt?0:g.props.updateDuration),function e(){g.popperInstance&&g.popperInstance.scheduleUpdate(),g.state.isMounted?requestAnimationFrame(e):At([g.popper],0)}()}function P(e,t){R(e,(function(){!g.state.isVisible&&h&&h.contains(g.popper)&&t()}))}function j(e,t){R(e,t)}function R(e,t){function i(e){e.target===a&&(kt(a,"remove",i),t())}if(0===e)return t();var a=g.popperChildren.tooltip;kt(a,"remove",l),kt(a,"add",i),l=i}function N(e,t){var i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];g.reference.addEventListener(e,t,i),c.push({eventType:e,handler:t,options:i})}function V(){g.props.touchHold&&!g.props.target&&(N("touchstart",D,Rt),N("touchend",_,Rt)),g.props.trigger.trim().split(" ").forEach((function(e){if("manual"!==e)if(g.props.target)switch(e){case"mouseenter":N("mouseover",x),N("mouseout",w);break;case"focus":N("focusin",x),N("focusout",w);break;case"click":N(e,x)}else switch(N(e,D),e){case"mouseenter":N("mouseleave",_);break;case"focus":N(tt?"focusout":"blur",T)}}))}function F(){c.forEach((function(e){g.reference.removeEventListener(e.eventType,e.handler,e.options)})),c=[]}function U(){return[g.popperChildren.tooltip,g.popperChildren.backdrop,g.popperChildren.content]}function K(){clearTimeout(o),clearTimeout(s)}function L(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};zt(e,rt);var t=g.props,i=Yt(g.reference,$e({},g.props,e,{ignoreAttributes:!0}));i.ignoreAttributes=ht(e,"ignoreAttributes")?e.ignoreAttributes:t.ignoreAttributes,g.props=i,(ht(e,"trigger")||ht(e,"touchHold"))&&(F(),V()),ht(e,"interactiveDebounce")&&(C(),p=mt(O,e.interactiveDebounce)),Pt(g.popper,t,i),g.popperChildren=Ot(g.popper),g.popperInstance&&(g.popperInstance.update(),nt.some((function(t){return ht(e,t)}))&&(g.popperInstance.destroy(),k(),g.state.isVisible||g.popperInstance.disableEventListeners(),g.props.followCursor&&n&&y(n)))}function H(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:ut(g.props.duration,0,rt.duration[0]);if(!g.state.isDestroyed&&g.state.isEnabled&&(!Nt||g.props.touch))return ht(g.reference,"isVirtual")||document.documentElement.contains(g.reference)?void(g.reference.hasAttribute("disabled")||!1!==g.props.onShow(g)&&(g.popper.style.visibility="visible",g.state.isVisible=!0,g.props.interactive&&g.reference.classList.add("tippy-active"),At([g.popper,g.popperChildren.tooltip,g.popperChildren.backdrop],0),E((function(){g.state.isVisible&&(M()||g.popperInstance.update(),At([g.popper],a.updateDuration),At(U(),e),g.popperChildren.backdrop&&(g.popperChildren.content.style.transitionDelay=Math.round(e/12)+"ms"),g.props.sticky&&I(),Mt(U(),"visible"),j(e,(function(){g.popperChildren.tooltip.classList.add("tippy-notransition"),g.props.aria&&g.reference.setAttribute("aria-".concat(g.props.aria),g.popper.id),g.props.onShown(g),g.state.isShown=!0})))})))):z()}function Y(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:ut(g.props.duration,1,rt.duration[1]);!g.state.isDestroyed&&g.state.isEnabled&&!1!==g.props.onHide(g)&&(g.popperChildren.tooltip.classList.remove("tippy-notransition"),g.props.interactive&&g.reference.classList.remove("tippy-active"),g.popper.style.visibility="hidden",g.state.isVisible=!1,g.state.isShown=!1,At(U(),e),Mt(U(),"hidden"),P(e,(function(){d||S(),g.props.aria&&g.reference.removeAttribute("aria-".concat(g.props.aria)),g.popperInstance.disableEventListeners(),g.popperInstance.options.placement=g.props.placement,h.removeChild(g.popper),g.props.onHidden(g),g.state.isMounted=!1})))}function z(e){g.state.isDestroyed||(g.state.isMounted&&Y(0),F(),delete g.reference._tippy,g.props.target&&e&&lt(g.reference.querySelectorAll(g.props.target)).forEach((function(e){e._tippy&&e._tippy.destroy()})),g.popperInstance&&g.popperInstance.destroy(),g.state.isDestroyed=!0)}}(t,a);return i&&e.push(i),e}),[]);return yt(e)?r[0]:r}Jt.version="4.0.4",Jt.defaults=rt,Jt.setDefaults=function(e){Object.keys(e).forEach((function(t){rt[t]=e[t]}))},Jt.hideAll=jt,Jt.group=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=t.delay,a=void 0===i?e[0].props.delay:i,r=t.duration,n=void 0===r?0:r,o=!1;function s(e){o=e,p()}function d(t){t._originalProps.onShow(t),e.forEach((function(e){e.set({duration:n}),e.hide()})),s(!0)}function l(e){e._originalProps.onHide(e),s(!1)}function c(e){e._originalProps.onShown(e),e.set({duration:e._originalProps.duration})}function p(){e.forEach((function(e){e.set({onShow:d,onShown:c,onHide:l,delay:o?[0,Array.isArray(a)?a[1]:a]:a,duration:o?n:e._originalProps.duration})}))}e.forEach((function(e){e._originalProps={duration:e.props.duration,onHide:e.props.onHide,onShow:e.props.onShow,onShown:e.props.onShown}})),p()},Ze&&setTimeout((function(){lt(document.querySelectorAll("[data-tippy]")).forEach((function(e){var t=e.getAttribute("data-tippy");t&&Jt(e,{content:t})}))})),function(e){if(Ze){var t=document.createElement("style");t.type="text/css",t.textContent='.tippy-iOS{cursor:pointer!important}.tippy-notransition{transition:none}.tippy-popper{transition-timing-function:cubic-bezier(.165,.84,.44,1);max-width:calc(100% - 8px);pointer-events:none;outline:0}.tippy-popper[x-placement^=top] .tippy-backdrop{border-radius:40% 40% 0 0}.tippy-popper[x-placement^=top] .tippy-roundarrow{bottom:-7px;bottom:-6.5px;-webkit-transform-origin:50% 0;transform-origin:50% 0;margin:0 3px}.tippy-popper[x-placement^=top] .tippy-roundarrow svg{position:absolute;left:0;-webkit-transform:rotate(180deg);transform:rotate(180deg)}.tippy-popper[x-placement^=top] .tippy-arrow{border-top:8px solid #333;border-right:8px solid transparent;border-left:8px solid transparent;bottom:-7px;margin:0 3px;-webkit-transform-origin:50% 0;transform-origin:50% 0}.tippy-popper[x-placement^=top] .tippy-backdrop{-webkit-transform-origin:0 25%;transform-origin:0 25%}.tippy-popper[x-placement^=top] .tippy-backdrop[data-state=visible]{-webkit-transform:scale(1) translate(-50%,-55%);transform:scale(1) translate(-50%,-55%)}.tippy-popper[x-placement^=top] .tippy-backdrop[data-state=hidden]{-webkit-transform:scale(.2) translate(-50%,-45%);transform:scale(.2) translate(-50%,-45%);opacity:0}.tippy-popper[x-placement^=top] [data-animation=shift-toward][data-state=visible]{-webkit-transform:translateY(-10px);transform:translateY(-10px)}.tippy-popper[x-placement^=top] [data-animation=shift-toward][data-state=hidden]{opacity:0;-webkit-transform:translateY(-20px);transform:translateY(-20px)}.tippy-popper[x-placement^=top] [data-animation=perspective]{-webkit-transform-origin:bottom;transform-origin:bottom}.tippy-popper[x-placement^=top] [data-animation=perspective][data-state=visible]{-webkit-transform:perspective(700px) translateY(-10px) rotateX(0);transform:perspective(700px) translateY(-10px) rotateX(0)}.tippy-popper[x-placement^=top] [data-animation=perspective][data-state=hidden]{opacity:0;-webkit-transform:perspective(700px) translateY(0) rotateX(60deg);transform:perspective(700px) translateY(0) rotateX(60deg)}.tippy-popper[x-placement^=top] [data-animation=fade][data-state=visible]{-webkit-transform:translateY(-10px);transform:translateY(-10px)}.tippy-popper[x-placement^=top] [data-animation=fade][data-state=hidden]{opacity:0;-webkit-transform:translateY(-10px);transform:translateY(-10px)}.tippy-popper[x-placement^=top] [data-animation=shift-away][data-state=visible]{-webkit-transform:translateY(-10px);transform:translateY(-10px)}.tippy-popper[x-placement^=top] [data-animation=shift-away][data-state=hidden]{opacity:0;-webkit-transform:translateY(0);transform:translateY(0)}.tippy-popper[x-placement^=top] [data-animation=scale]{-webkit-transform-origin:bottom;transform-origin:bottom}.tippy-popper[x-placement^=top] [data-animation=scale][data-state=visible]{-webkit-transform:translateY(-10px) scale(1);transform:translateY(-10px) scale(1)}.tippy-popper[x-placement^=top] [data-animation=scale][data-state=hidden]{opacity:0;-webkit-transform:translateY(-10px) scale(.5);transform:translateY(-10px) scale(.5)}.tippy-popper[x-placement^=bottom] .tippy-backdrop{border-radius:0 0 30% 30%}.tippy-popper[x-placement^=bottom] .tippy-roundarrow{top:-7px;-webkit-transform-origin:50% 100%;transform-origin:50% 100%;margin:0 3px}.tippy-popper[x-placement^=bottom] .tippy-roundarrow svg{position:absolute;left:0;-webkit-transform:rotate(0);transform:rotate(0)}.tippy-popper[x-placement^=bottom] .tippy-arrow{border-bottom:8px solid #333;border-right:8px solid transparent;border-left:8px solid transparent;top:-7px;margin:0 3px;-webkit-transform-origin:50% 100%;transform-origin:50% 100%}.tippy-popper[x-placement^=bottom] .tippy-backdrop{-webkit-transform-origin:0 -50%;transform-origin:0 -50%}.tippy-popper[x-placement^=bottom] .tippy-backdrop[data-state=visible]{-webkit-transform:scale(1) translate(-50%,-45%);transform:scale(1) translate(-50%,-45%)}.tippy-popper[x-placement^=bottom] .tippy-backdrop[data-state=hidden]{-webkit-transform:scale(.2) translate(-50%);transform:scale(.2) translate(-50%);opacity:0}.tippy-popper[x-placement^=bottom] [data-animation=shift-toward][data-state=visible]{-webkit-transform:translateY(10px);transform:translateY(10px)}.tippy-popper[x-placement^=bottom] [data-animation=shift-toward][data-state=hidden]{opacity:0;-webkit-transform:translateY(20px);transform:translateY(20px)}.tippy-popper[x-placement^=bottom] [data-animation=perspective]{-webkit-transform-origin:top;transform-origin:top}.tippy-popper[x-placement^=bottom] [data-animation=perspective][data-state=visible]{-webkit-transform:perspective(700px) translateY(10px) rotateX(0);transform:perspective(700px) translateY(10px) rotateX(0)}.tippy-popper[x-placement^=bottom] [data-animation=perspective][data-state=hidden]{opacity:0;-webkit-transform:perspective(700px) translateY(0) rotateX(-60deg);transform:perspective(700px) translateY(0) rotateX(-60deg)}.tippy-popper[x-placement^=bottom] [data-animation=fade][data-state=visible]{-webkit-transform:translateY(10px);transform:translateY(10px)}.tippy-popper[x-placement^=bottom] [data-animation=fade][data-state=hidden]{opacity:0;-webkit-transform:translateY(10px);transform:translateY(10px)}.tippy-popper[x-placement^=bottom] [data-animation=shift-away][data-state=visible]{-webkit-transform:translateY(10px);transform:translateY(10px)}.tippy-popper[x-placement^=bottom] [data-animation=shift-away][data-state=hidden]{opacity:0;-webkit-transform:translateY(0);transform:translateY(0)}.tippy-popper[x-placement^=bottom] [data-animation=scale]{-webkit-transform-origin:top;transform-origin:top}.tippy-popper[x-placement^=bottom] [data-animation=scale][data-state=visible]{-webkit-transform:translateY(10px) scale(1);transform:translateY(10px) scale(1)}.tippy-popper[x-placement^=bottom] [data-animation=scale][data-state=hidden]{opacity:0;-webkit-transform:translateY(10px) scale(.5);transform:translateY(10px) scale(.5)}.tippy-popper[x-placement^=left] .tippy-backdrop{border-radius:50% 0 0 50%}.tippy-popper[x-placement^=left] .tippy-roundarrow{right:-12px;-webkit-transform-origin:33.33333333% 50%;transform-origin:33.33333333% 50%;margin:3px 0}.tippy-popper[x-placement^=left] .tippy-roundarrow svg{position:absolute;left:0;-webkit-transform:rotate(90deg);transform:rotate(90deg)}.tippy-popper[x-placement^=left] .tippy-arrow{border-left:8px solid #333;border-top:8px solid transparent;border-bottom:8px solid transparent;right:-7px;margin:3px 0;-webkit-transform-origin:0 50%;transform-origin:0 50%}.tippy-popper[x-placement^=left] .tippy-backdrop{-webkit-transform-origin:50% 0;transform-origin:50% 0}.tippy-popper[x-placement^=left] .tippy-backdrop[data-state=visible]{-webkit-transform:scale(1) translate(-50%,-50%);transform:scale(1) translate(-50%,-50%)}.tippy-popper[x-placement^=left] .tippy-backdrop[data-state=hidden]{-webkit-transform:scale(.2) translate(-75%,-50%);transform:scale(.2) translate(-75%,-50%);opacity:0}.tippy-popper[x-placement^=left] [data-animation=shift-toward][data-state=visible]{-webkit-transform:translateX(-10px);transform:translateX(-10px)}.tippy-popper[x-placement^=left] [data-animation=shift-toward][data-state=hidden]{opacity:0;-webkit-transform:translateX(-20px);transform:translateX(-20px)}.tippy-popper[x-placement^=left] [data-animation=perspective]{-webkit-transform-origin:right;transform-origin:right}.tippy-popper[x-placement^=left] [data-animation=perspective][data-state=visible]{-webkit-transform:perspective(700px) translateX(-10px) rotateY(0);transform:perspective(700px) translateX(-10px) rotateY(0)}.tippy-popper[x-placement^=left] [data-animation=perspective][data-state=hidden]{opacity:0;-webkit-transform:perspective(700px) translateX(0) rotateY(-60deg);transform:perspective(700px) translateX(0) rotateY(-60deg)}.tippy-popper[x-placement^=left] [data-animation=fade][data-state=visible]{-webkit-transform:translateX(-10px);transform:translateX(-10px)}.tippy-popper[x-placement^=left] [data-animation=fade][data-state=hidden]{opacity:0;-webkit-transform:translateX(-10px);transform:translateX(-10px)}.tippy-popper[x-placement^=left] [data-animation=shift-away][data-state=visible]{-webkit-transform:translateX(-10px);transform:translateX(-10px)}.tippy-popper[x-placement^=left] [data-animation=shift-away][data-state=hidden]{opacity:0;-webkit-transform:translateX(0);transform:translateX(0)}.tippy-popper[x-placement^=left] [data-animation=scale]{-webkit-transform-origin:right;transform-origin:right}.tippy-popper[x-placement^=left] [data-animation=scale][data-state=visible]{-webkit-transform:translateX(-10px) scale(1);transform:translateX(-10px) scale(1)}.tippy-popper[x-placement^=left] [data-animation=scale][data-state=hidden]{opacity:0;-webkit-transform:translateX(-10px) scale(.5);transform:translateX(-10px) scale(.5)}.tippy-popper[x-placement^=right] .tippy-backdrop{border-radius:0 50% 50% 0}.tippy-popper[x-placement^=right] .tippy-roundarrow{left:-12px;-webkit-transform-origin:66.66666666% 50%;transform-origin:66.66666666% 50%;margin:3px 0}.tippy-popper[x-placement^=right] .tippy-roundarrow svg{position:absolute;left:0;-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}.tippy-popper[x-placement^=right] .tippy-arrow{border-right:8px solid #333;border-top:8px solid transparent;border-bottom:8px solid transparent;left:-7px;margin:3px 0;-webkit-transform-origin:100% 50%;transform-origin:100% 50%}.tippy-popper[x-placement^=right] .tippy-backdrop{-webkit-transform-origin:-50% 0;transform-origin:-50% 0}.tippy-popper[x-placement^=right] .tippy-backdrop[data-state=visible]{-webkit-transform:scale(1) translate(-50%,-50%);transform:scale(1) translate(-50%,-50%)}.tippy-popper[x-placement^=right] .tippy-backdrop[data-state=hidden]{-webkit-transform:scale(.2) translate(-25%,-50%);transform:scale(.2) translate(-25%,-50%);opacity:0}.tippy-popper[x-placement^=right] [data-animation=shift-toward][data-state=visible]{-webkit-transform:translateX(10px);transform:translateX(10px)}.tippy-popper[x-placement^=right] [data-animation=shift-toward][data-state=hidden]{opacity:0;-webkit-transform:translateX(20px);transform:translateX(20px)}.tippy-popper[x-placement^=right] [data-animation=perspective]{-webkit-transform-origin:left;transform-origin:left}.tippy-popper[x-placement^=right] [data-animation=perspective][data-state=visible]{-webkit-transform:perspective(700px) translateX(10px) rotateY(0);transform:perspective(700px) translateX(10px) rotateY(0)}.tippy-popper[x-placement^=right] [data-animation=perspective][data-state=hidden]{opacity:0;-webkit-transform:perspective(700px) translateX(0) rotateY(60deg);transform:perspective(700px) translateX(0) rotateY(60deg)}.tippy-popper[x-placement^=right] [data-animation=fade][data-state=visible]{-webkit-transform:translateX(10px);transform:translateX(10px)}.tippy-popper[x-placement^=right] [data-animation=fade][data-state=hidden]{opacity:0;-webkit-transform:translateX(10px);transform:translateX(10px)}.tippy-popper[x-placement^=right] [data-animation=shift-away][data-state=visible]{-webkit-transform:translateX(10px);transform:translateX(10px)}.tippy-popper[x-placement^=right] [data-animation=shift-away][data-state=hidden]{opacity:0;-webkit-transform:translateX(0);transform:translateX(0)}.tippy-popper[x-placement^=right] [data-animation=scale]{-webkit-transform-origin:left;transform-origin:left}.tippy-popper[x-placement^=right] [data-animation=scale][data-state=visible]{-webkit-transform:translateX(10px) scale(1);transform:translateX(10px) scale(1)}.tippy-popper[x-placement^=right] [data-animation=scale][data-state=hidden]{opacity:0;-webkit-transform:translateX(10px) scale(.5);transform:translateX(10px) scale(.5)}.tippy-tooltip{position:relative;color:#fff;border-radius:.25rem;font-size:.875rem;padding:.3125rem .5625rem;line-height:1.4;text-align:center;will-change:transform;background-color:#333}.tippy-tooltip[data-size=small]{padding:.1875rem .375rem;font-size:.75rem}.tippy-tooltip[data-size=large]{padding:.375rem .75rem;font-size:1rem}.tippy-tooltip[data-animatefill]{overflow:hidden;background-color:transparent}.tippy-tooltip[data-interactive],.tippy-tooltip[data-interactive] path{pointer-events:auto}.tippy-tooltip[data-inertia][data-state=visible]{transition-timing-function:cubic-bezier(.54,1.5,.38,1.11)}.tippy-tooltip[data-inertia][data-state=hidden]{transition-timing-function:ease}.tippy-arrow,.tippy-roundarrow{position:absolute;width:0;height:0}.tippy-roundarrow{width:18px;height:7px;fill:#333;pointer-events:none}.tippy-backdrop{position:absolute;will-change:transform;background-color:#333;border-radius:50%;width:calc(110% + 2rem);left:50%;top:50%;z-index:-1;transition:all cubic-bezier(.46,.1,.52,.98);-webkit-backface-visibility:hidden;backface-visibility:hidden}.tippy-backdrop:after{content:"";float:left;padding-top:100%}.tippy-backdrop+.tippy-content{transition-property:opacity;will-change:opacity}.tippy-backdrop+.tippy-content[data-state=visible]{opacity:1}.tippy-backdrop+.tippy-content[data-state=hidden]{opacity:0}';var i=document.head,a=i.firstChild;a?i.insertBefore(t,a):i.appendChild(t)}}();var Gt=Jt;let Xt=(()=>{class e{constructor(){this._instances=new Map,this.groups=new Map}get instances(){return this._instances}getGroup(e){return this.groups.get(e)}addInstance(e){e&&e.id?this.instances.has(e.id)?console.error("TooltipInstance already exists in collection: cannot be added to collection"):(this.instances.set(e.id,e),e.group&&this.addGroupInstance(e)):console.error("TooltipInstance missing necessary 'id property: cannot be added to collection")}addGroupInstance(e){e.group?(this.groups.has(e.group)||this.groups.set(e.group,new Map),this.groups.get(e.group).set(e.id,e)):console.error("TooltipInstance missing necessary 'group property: cannot be added to group collection")}removeInstance(e,t){this.instances.has(e)?this.instances.delete(e):console.error("TooltipInstance does not exist in collection: cannot be removed from collection"),t&&this.removeGroupInstance(e,t)}removeGroupInstance(e,t){this.groups.has(t)?this.groups.get(t).has(e)?this.groups.get(t).delete(e):console.error("TooltipInstance does not exist in group collection: cannot be removed from group collection"):console.error("Group does not exist in group collection: cannot remove TooltipInstance")}hideAll(e,t){t&&this.groups.has(t)?this.groups.get(t).forEach(e=>e.hide()):Gt.hideAll(e)}showAll(e,t){(this.groups.get(t)||this.instances).forEach(t=>t.show(e))}disableAll(e){(this.groups.get(e)||this.instances).forEach(e=>e.disable())}enableAll(e){(this.groups.get(e)||this.instances).forEach(e=>{e.props.content&&e.enable()})}destroyAll(e){e&&this.groups.has(e)?(this.groups.get(e).forEach(e=>{this.instances.delete(e.id),e.destroy()}),this.groups.get(e).clear()):(this.instances.forEach(e=>e.destroy()),this.instances.clear())}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275prov=f["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac}),e})();const qt=new f.InjectionToken("TooltipOptions");let Qt=(()=>{class e{constructor(e,t,i){this.initOptions=e,this.el=t,this.tooltipService=i,this.options={},this.tooltipInstance=null,this.create(),this.updateOptions(this.options)}set tooltipOptions(e){this.updateOptions(e)}get tooltipOptions(){return this.options}set tooltipGroup(e){this.updateOptions({group:e})}get tooltipGroup(){return this.options.group}set tooltipContent(e){this.updateOptions({content:e})}get tooltipContent(){return this.options.content}set tooltipArrowType(e){this.options.arrow=!0,this.updateOptions({arrowType:e})}get tooltipArrowType(){return this.options.arrowType}set tooltipMaxWidth(e){this.updateOptions({maxWidth:e})}get tooltipMaxWidth(){return this.options.maxWidth}set tooltipPlacement(e){this.updateOptions({placement:e})}get tooltipPlacement(){return this.options.placement}set tooltipAnimation(e){this.updateOptions({animation:e})}get tooltipAnimation(){return this.options.animation}set tooltipTrigger(e){this.updateOptions({trigger:e})}get tooltipTrigger(){return this.options.trigger}set tooltipTouch(e){this.updateOptions({touch:e})}get tooltipTouch(){return this.options.touch}set tooltipTouchHold(e){this.updateOptions({touchHold:e})}get tooltipTouchHold(){return this.options.touchHold}set tooltipTheme(e){this.updateOptions({theme:e})}get tooltipTheme(){return this.options.theme}set tooltipAllowHtml(e){this.updateOptions({allowHTML:e})}get tooltipAllowHtml(){return this.options.allowHTML}ngOnDestroy(){this.destroy()}get state(){return this.tooltipInstance?this.tooltipInstance.state:{}}get id(){return this.tooltipInstance?this.tooltipInstance.id:void 0}get group(){return this.tooltipInstance?this.tooltipInstance.group:void 0}updateOptions(e){const t=this.group;this.options=this.cleanOptions(Object.assign({},this.initOptions,this.options,e));const{group:i}=this.options;if(delete this.options.group,this.state.isEnabled)this.options.content?this.tooltipInstance.set(this.options):this.disable();else if(this.options.content)return this.enable(),this.updateOptions(Object.assign({},this.options,{group:i}));this.options.group=i,this.tooltipInstance.group=i,t!==i&&(t&&this.tooltipService.removeGroupInstance(this.id,t),i&&this.tooltipService.addGroupInstance(this.tooltipInstance))}cleanOptions(e){for(const t in e)null==e[t]&&delete e[t];return e}create(){this.tooltipInstance=Gt(this.el.nativeElement),this.tooltipService.addInstance(this.tooltipInstance)}destroy(){this.tooltipInstance&&this.tooltipInstance.destroy(),this.tooltipService.removeInstance(this.id,this.group),this.tooltipInstance=null}enable(){this.tooltipInstance?this.tooltipInstance.enable():this.create()}disable(){this.tooltipInstance&&this.tooltipInstance.disable()}show(e){this.tooltipInstance&&this.tooltipInstance.show(e)}hide(e){this.tooltipInstance&&this.tooltipInstance.hide(e)}}return e.\u0275fac=function(t){return new(t||e)(f["\u0275\u0275directiveInject"](qt),f["\u0275\u0275directiveInject"](f.ElementRef),f["\u0275\u0275directiveInject"](Xt))},e.\u0275dir=f["\u0275\u0275defineDirective"]({type:e,selectors:[["","ngxTooltip",""]],inputs:{tooltipOptions:["ngxTooltip","tooltipOptions"],tooltipGroup:"tooltipGroup",tooltipContent:"tooltipContent",tooltipArrowType:"tooltipArrowType",tooltipMaxWidth:"tooltipMaxWidth",tooltipPlacement:"tooltipPlacement",tooltipAnimation:"tooltipAnimation",tooltipTrigger:"tooltipTrigger",tooltipTouch:"tooltipTouch",tooltipTouchHold:"tooltipTouchHold",tooltipTheme:"tooltipTheme",tooltipAllowHtml:"tooltipAllowHtml"}}),e})(),$t=(()=>{class e{static forRoot(t){return{ngModule:e,providers:[{provide:qt,useValue:t}]}}}return e.\u0275mod=f["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=f["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},providers:[Xt],imports:[[a.CommonModule]]}),e})();function Zt(e,t){if(1&e&&f["\u0275\u0275element"](0,"div",7),2&e){const e=f["\u0275\u0275nextContext"]();f["\u0275\u0275propertyInterpolate1"]("ngClass","ngx-org-connector-",e.direction,"")}}function ei(e,t){if(1&e&&(f["\u0275\u0275elementStart"](0,"div"),f["\u0275\u0275elementStart"](1,"div",8),f["\u0275\u0275text"](2),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](3,"div",9),f["\u0275\u0275text"](4),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]()),2&e){const e=f["\u0275\u0275nextContext"]();f["\u0275\u0275advance"](2),f["\u0275\u0275textInterpolate"](null==e.node?null:e.node.name),f["\u0275\u0275advance"](2),f["\u0275\u0275textInterpolate"](null==e.node?null:e.node.title)}}function ti(e,t){if(1&e&&(f["\u0275\u0275elementStart"](0,"div"),f["\u0275\u0275elementStart"](1,"div",10),f["\u0275\u0275text"](2),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](3,"div",11),f["\u0275\u0275text"](4),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]()),2&e){const e=f["\u0275\u0275nextContext"]();f["\u0275\u0275advance"](2),f["\u0275\u0275textInterpolate"](null==e.node?null:e.node.name),f["\u0275\u0275advance"](2),f["\u0275\u0275textInterpolate"](null==e.node?null:e.node.title)}}function ii(e,t){if(1&e&&f["\u0275\u0275element"](0,"div",7),2&e){const e=f["\u0275\u0275nextContext"]();f["\u0275\u0275propertyInterpolate1"]("ngClass","ngx-org-connector-",e.direction,"")}}const ai=function(e){return{"node-active":e}};function ri(e,t){if(1&e){const e=f["\u0275\u0275getCurrentView"]();f["\u0275\u0275elementStart"](0,"ngx-chart-node",2),f["\u0275\u0275listener"]("click",(function(){f["\u0275\u0275restoreView"](e);const t=f["\u0275\u0275nextContext"]();return t.nodeClick(t.nodeIndex)}))("itemClick",(function(t){return f["\u0275\u0275restoreView"](e),f["\u0275\u0275nextContext"]().itemClick.emit(t)})),f["\u0275\u0275elementEnd"]()}if(2&e){const e=f["\u0275\u0275nextContext"]();f["\u0275\u0275propertyInterpolate1"]("ngClass","ngx-org-",e.direction,""),f["\u0275\u0275property"]("node",e.node)("hasParent",e.hasParent)("nodeIndex",e.nodeIndex)("direction",e.direction)}}const ni=function(e,t){return[e,t]};function oi(e,t){if(1&e){const e=f["\u0275\u0275getCurrentView"]();f["\u0275\u0275elementContainerStart"](0),f["\u0275\u0275elementStart"](1,"div",5),f["\u0275\u0275elementStart"](2,"div",6),f["\u0275\u0275element"](3,"div",7),f["\u0275\u0275element"](4,"div",8),f["\u0275\u0275element"](5,"div",7),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](6,"ngx-chart-designer",9),f["\u0275\u0275listener"]("itemClick",(function(t){return f["\u0275\u0275restoreView"](e),f["\u0275\u0275nextContext"](2).itemClick.emit(t)})),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=t.$implicit,i=t.first,a=t.last,r=t.index,n=f["\u0275\u0275nextContext"](2);f["\u0275\u0275advance"](1),f["\u0275\u0275propertyInterpolate1"]("ngClass","ngx-org-org-container-",n.direction,""),f["\u0275\u0275advance"](1),f["\u0275\u0275propertyInterpolate1"]("ngClass","ngx-org-connector-container-",n.direction,""),f["\u0275\u0275advance"](1),f["\u0275\u0275styleProp"]("border-color",i?"transparent":""),f["\u0275\u0275advance"](2),f["\u0275\u0275styleProp"]("border-color",a?"transparent":""),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("node",e)("hasParent",!0)("direction",n.direction)("nodeIndex",f["\u0275\u0275pureFunction2"](10,ni,n.nodeIndex,r))}}function si(e,t){if(1&e&&(f["\u0275\u0275elementStart"](0,"div",3),f["\u0275\u0275template"](1,oi,7,13,"ng-container",4),f["\u0275\u0275elementEnd"]()),2&e){const e=f["\u0275\u0275nextContext"]();f["\u0275\u0275propertyInterpolate1"]("ngClass","ngx-org-reports-",e.direction,""),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngForOf",null==e.node?null:e.node.childs)}}const di=function(e){return[e]};function li(e,t){if(1&e){const e=f["\u0275\u0275getCurrentView"]();f["\u0275\u0275elementContainerStart"](0),f["\u0275\u0275elementStart"](1,"div"),f["\u0275\u0275elementStart"](2,"ngx-chart-designer",2),f["\u0275\u0275listener"]("itemClick",(function(t){return f["\u0275\u0275restoreView"](e),f["\u0275\u0275nextContext"](2).itemClick.emit(t)})),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=t.$implicit,i=t.index,a=f["\u0275\u0275nextContext"](2);f["\u0275\u0275advance"](1),f["\u0275\u0275classMapInterpolate1"]("ngx-org-self-",a.direction,""),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("node",e)("direction",a.direction)("nodeIndex",f["\u0275\u0275pureFunction1"](6,di,i))}}function ci(e,t){if(1&e&&(f["\u0275\u0275elementStart"](0,"div"),f["\u0275\u0275template"](1,li,3,8,"ng-container",1),f["\u0275\u0275elementEnd"]()),2&e){const e=f["\u0275\u0275nextContext"]();f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngForOf",e.nodes)}}let pi=(()=>{class e{constructor(){this.msg=new D.b,this.sendMsg=e=>{this.msg.next(e)},this.getMsg=()=>this.msg.asObservable(),this.displayFullOrgNameSubject=new q.a(!1)}get displayFullOrgName$(){return this.displayFullOrgNameSubject.asObservable()}setDisplayFullOrgName(e){this.displayFullOrgNameSubject.next(e)}getDisplayFullOrgName(){var e;return(null===(e=this.displayFullOrgNameSubject)||void 0===e?void 0:e.value)||!1}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275prov=Object(f["\u0275\u0275defineInjectable"])({factory:function(){return new e},token:e,providedIn:"root"}),e})(),hi=(()=>{class e{constructor(e){this._chart=e,this.hasParent=!1,this.direction="vertical",this.itemClick=new f.EventEmitter,this.tooltipOptions={placement:"bottom",arrow:!0,arrowType:"sharp",allowHTML:!0,maxWidth:500},this.displayFullOrgNameCheck=!1,this.getFirstTwoLetter=e=>{var t=e.match(/\b(\w)/g).join("");return t.length>1?t[0]+t[1]:t}}ngOnInit(){this.displayFullOrgNameCheck=this._chart.getDisplayFullOrgName(),this.storageSubscription=this._chart.displayFullOrgName$.subscribe(e=>{this.displayFullOrgNameCheck=e})}ngOnDestroy(){this.storageSubscription&&this.storageSubscription.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(f["\u0275\u0275directiveInject"](pi))},e.\u0275cmp=f["\u0275\u0275defineComponent"]({type:e,selectors:[["ngx-chart-node"]],inputs:{hasParent:"hasParent",direction:"direction",node:"node",nodeIndex:"nodeIndex"},outputs:{itemClick:"itemClick"},decls:14,vars:12,consts:[["class","ngx-org-border",3,"ngClass",4,"ngIf"],[1,"ngx-org-box","ngx-org-border","ngx-org-background",3,"ngxTooltip","tooltipContent","ngClass","click"],[1,"ngx-org-image","ngx-org-border"],[4,"ngIf"],["tooltipTemplate",""],[2,"text-align","left"],[2,"padding-top","5px","text-align","left"],[1,"ngx-org-border",3,"ngClass"],[1,"ngx-org-name","overflow"],[1,"ngx-org-title","overflow"],[1,"ngx-org-name","overflow-fulltext"],[1,"ngx-org-title","overflow-fulltext"]],template:function(e,t){if(1&e&&(f["\u0275\u0275template"](0,Zt,1,1,"div",0),f["\u0275\u0275elementStart"](1,"div",1),f["\u0275\u0275listener"]("click",(function(){return t.itemClick.emit(t.node)})),f["\u0275\u0275elementStart"](2,"div",2),f["\u0275\u0275elementStart"](3,"strong"),f["\u0275\u0275text"](4),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275template"](5,ei,5,2,"div",3),f["\u0275\u0275template"](6,ti,5,2,"div",3),f["\u0275\u0275elementEnd"](),f["\u0275\u0275template"](7,ii,1,1,"div",0),f["\u0275\u0275elementStart"](8,"div",null,4),f["\u0275\u0275elementStart"](10,"div",5),f["\u0275\u0275text"](11),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](12,"div",6),f["\u0275\u0275text"](13),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]()),2&e){const e=f["\u0275\u0275reference"](9);f["\u0275\u0275property"]("ngIf",t.hasParent),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngxTooltip",t.tooltipOptions)("tooltipContent",e)("ngClass",f["\u0275\u0275pureFunction1"](10,ai,null==t.node?null:t.node.isActive)),f["\u0275\u0275advance"](3),f["\u0275\u0275textInterpolate"](t.getFirstTwoLetter(null==t.node?null:t.node.name)),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",!t.displayFullOrgNameCheck),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",t.displayFullOrgNameCheck),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",null==t.node||null==t.node.childs?null:t.node.childs.length),f["\u0275\u0275advance"](4),f["\u0275\u0275textInterpolate1"]("Org : ",null==t.node?null:t.node.name,""),f["\u0275\u0275advance"](2),f["\u0275\u0275textInterpolate"](null==t.node?null:t.node.title)}},directives:[a.NgIf,Qt,a.NgClass],styles:["[_nghost-%COMP%]{display:flex;align-items:center}.ngx-org-box[_ngcontent-%COMP%]{border-radius:17px!important}.ngx-org-box[_ngcontent-%COMP%], .node-active[_ngcontent-%COMP%]{cursor:pointer;display:flex;align-items:center}.node-active[_ngcontent-%COMP%]{border-radius:17px!important;border:solid!important;border-width:initial!important;border-color:#cf0001!important;background-color:#fff3f3!important}.ngx-org-image[_ngcontent-%COMP%]{background-repeat:no-repeat;width:3em;height:3em;margin-right:.5em;background-color:#f1cbcb;border-radius:50%;padding:.25em;display:flex;align-items:center;justify-content:center}.ngx-org-name[_ngcontent-%COMP%]{font-family:Franklin Gothic Medium,Arial Narrow,Arial,sans-serif}.ngx-org-title[_ngcontent-%COMP%]{font-family:Cambria,Cochin,Georgia,Times,Times New Roman,serif}.ngx-org-horizontal[_ngcontent-%COMP%]{padding:1em 0}.ngx-org-vertical[_ngcontent-%COMP%]{padding:0 1em}.ngx-org-border[_ngcontent-%COMP%]{border:1px solid #a9a9a9}.ngx-org-box[_ngcontent-%COMP%]{padding:.5em 1em;border-radius:.2em;box-shadow:.05em .05em .2em .05em rgba(0,0,0,.16862745098039217)}.ngx-org-connector-horizontal[_ngcontent-%COMP%]{width:1em}.ngx-org-connector-vertical[_ngcontent-%COMP%]{height:1em}.overflow[_ngcontent-%COMP%]{width:91px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.overflow-fulltext[_ngcontent-%COMP%]{max-width:7rem;min-width:3rem;white-space:normal;overflow-wrap:break-word}"]}),e})(),ui=(()=>{class e{}return e.\u0275mod=f["\u0275\u0275defineNgModule"]({type:e,bootstrap:function(){return[hi]}}),e.\u0275inj=f["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[a.CommonModule,$t.forRoot({placement:"bottom",arrow:!0,arrowType:"sharp",allowHTML:!0})]]}),e})(),mi=(()=>{class e{constructor(e){this._org=e,this.hasParent=!1,this.direction="vertical",this.itemClick=new f.EventEmitter,this.getIndexes=new f.EventEmitter}get hostClass(){return"vertical"===this.direction?"column":""}flatten(e){const t=[...e],i=[];for(;t.length;){const e=t.pop();Array.isArray(e)?t.push(...e):i.push(e)}return i.reverse()}nodeClick(e){let t=this.flatten(e);this._org.sendMsg(t),console.log(t)}}return e.\u0275fac=function(t){return new(t||e)(f["\u0275\u0275directiveInject"](pi))},e.\u0275cmp=f["\u0275\u0275defineComponent"]({type:e,selectors:[["ngx-chart-designer"]],hostVars:2,hostBindings:function(e,t){2&e&&f["\u0275\u0275styleProp"]("flex-direction",t.hostClass)},inputs:{hasParent:"hasParent",direction:"direction",nodeIndex:"nodeIndex",node:"node"},outputs:{itemClick:"itemClick",getIndexes:"getIndexes"},decls:2,vars:2,consts:[[3,"ngClass","node","hasParent","nodeIndex","direction","click","itemClick",4,"ngIf"],["class","ngx-org-reports",3,"ngClass",4,"ngIf"],[3,"ngClass","node","hasParent","nodeIndex","direction","click","itemClick"],[1,"ngx-org-reports",3,"ngClass"],[4,"ngFor","ngForOf"],[1,"ngx-org-org-container",3,"ngClass"],[1,"ngx-org-connector-container",3,"ngClass"],[1,"ngx-org-connector","ngx-org-border"],[1,"ngx-org-border"],[3,"node","hasParent","direction","nodeIndex","itemClick"]],template:function(e,t){1&e&&(f["\u0275\u0275template"](0,ri,1,5,"ngx-chart-node",0),f["\u0275\u0275template"](1,si,2,2,"div",1)),2&e&&(f["\u0275\u0275property"]("ngIf",t.node),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",null==t.node||null==t.node.childs?null:t.node.childs.length))},directives:[a.NgIf,hi,a.NgClass,a.NgForOf,e],styles:["[_nghost-%COMP%]{display:flex;align-items:center;flex:1}.ngx-org-vertical[_ngcontent-%COMP%]{flex-direction:column}.ngx-org-org-container[_ngcontent-%COMP%]{display:flex}.ngx-org-org-container-vertical[_ngcontent-%COMP%]{flex-direction:column}.ngx-org-connector[_ngcontent-%COMP%]{flex:1}.ngx-org-connector-container[_ngcontent-%COMP%]{display:flex}.ngx-org-connector-container-horizontal[_ngcontent-%COMP%]{flex-direction:column}.ngx-org-reports[_ngcontent-%COMP%]{display:flex;flex:1}.ngx-org-reports-horizontal[_ngcontent-%COMP%]{flex-direction:column}.ngx-org-horizontal[_ngcontent-%COMP%]{padding:1em 0}.ngx-org-vertical[_ngcontent-%COMP%]{padding:0 1em}.ngx-org-border[_ngcontent-%COMP%]{border:1px solid #a9a9a9}.ngx-org-box[_ngcontent-%COMP%]{padding:.5em 1em;border-radius:.2em;box-shadow:.05em .05em .2em .05em rgba(0,0,0,.16862745098039217)}.ngx-org-connector-horizontal[_ngcontent-%COMP%]{width:1em}.ngx-org-connector-vertical[_ngcontent-%COMP%]{height:1em}"]}),e})(),fi=(()=>{class e{}return e.\u0275mod=f["\u0275\u0275defineNgModule"]({type:e,bootstrap:function(){return[mi]}}),e.\u0275inj=f["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[a.CommonModule,ui]]}),e})(),gi=(()=>{class e{constructor(){this.hasParent=!1,this.direction="vertical",this.itemClick=new f.EventEmitter}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=f["\u0275\u0275defineComponent"]({type:e,selectors:[["kebs-org-chart"]],inputs:{hasParent:"hasParent",direction:"direction",nodes:"nodes"},outputs:{itemClick:"itemClick"},decls:1,vars:1,consts:[[4,"ngIf"],[4,"ngFor","ngForOf"],[3,"node","direction","nodeIndex","itemClick"]],template:function(e,t){1&e&&f["\u0275\u0275template"](0,ci,2,1,"div",0),2&e&&f["\u0275\u0275property"]("ngIf",null==t.nodes?null:t.nodes.length)},directives:[a.NgIf,a.NgForOf,mi],styles:["body[_ngcontent-%COMP%], html[_ngcontent-%COMP%]{display:flex;flex:1}.ngx-org-name[_ngcontent-%COMP%]{font-family:Patua One,cursive}.ngx-org-title[_ngcontent-%COMP%]{font-family:Oswald,sans-serif}.ngx-org-border[_ngcontent-%COMP%]{border-color:#9e9e9e}.ngx-org-box[_ngcontent-%COMP%]{color:#000;width:10em}.ngx-org-self-vertical[_ngcontent-%COMP%]{margin-bottom:2%}.ngx-org-horizontal[_ngcontent-%COMP%]{padding:1em 0}.ngx-org-vertical[_ngcontent-%COMP%]{padding:0 1em}.ngx-org-border[_ngcontent-%COMP%]{border:1px solid #a9a9a9}.ngx-org-box[_ngcontent-%COMP%]{padding:.5em 1em;border-radius:.2em;box-shadow:.05em .05em .2em .05em rgba(0,0,0,.16862745098039217)}.ngx-org-connector-horizontal[_ngcontent-%COMP%]{width:1em}.ngx-org-connector-vertical[_ngcontent-%COMP%]{height:1em}"]}),e})(),yi=(()=>{class e{}return e.\u0275mod=f["\u0275\u0275defineNgModule"]({type:e,bootstrap:function(){return[gi]}}),e.\u0275inj=f["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[a.CommonModule,fi]]}),e})();var vi=i("BVzC"),bi=i("XXEo"),Si=i("LcQX"),Ci=i("flaP"),Di=i("rDax"),Oi=i("FKDz"),_i=i("TmG/"),Ti=i("1jcm"),xi=i("xG9w"),wi=i("wd/R"),Ai=i("R0Ic"),ki=i("m3gX");let Ei=(()=>{class e{constructor(e){this.data=e,this.KEYRES_ID="overall-okr"}ngOnInit(){console.log("Data received in MeetingNotesComponent:",this.data)}}return e.\u0275fac=function(t){return new(t||e)(f["\u0275\u0275directiveInject"](p.a))},e.\u0275cmp=f["\u0275\u0275defineComponent"]({type:e,selectors:[["app-meeting-notes"]],decls:8,vars:1,consts:[[1,"okr-meeting-notes","p-3"],[1,"topG","d-flex","justify-content-between"],[1,"header-notes"],["mat-icon-button","",3,"mat-dialog-close"],[1,"close-icon"],[3,"keyResId"]],template:function(e,t){1&e&&(f["\u0275\u0275elementStart"](0,"div",0),f["\u0275\u0275elementStart"](1,"div",1),f["\u0275\u0275elementStart"](2,"div",2),f["\u0275\u0275text"](3,"Meeting Notes"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](4,"button",3),f["\u0275\u0275elementStart"](5,"mat-icon",4),f["\u0275\u0275text"](6,"close"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275element"](7,"app-kr-overview-notes",5),f["\u0275\u0275elementEnd"]()),2&e&&(f["\u0275\u0275advance"](7),f["\u0275\u0275property"]("keyResId",t.KEYRES_ID))},directives:[b.a,p.d,S.a,ki.a],styles:[".okr-meeting-notes[_ngcontent-%COMP%]   .topG[_ngcontent-%COMP%]   .header-notes[_ngcontent-%COMP%]{color:#455468;font-size:16px;font-weight:600;font-family:DM Sans;text-transform:capitalize;font-style:normal;line-height:16px}.okr-meeting-notes[_ngcontent-%COMP%]   .topG[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#455468;font-size:19px}"]}),e})();var Mi=i("Iab2"),Ii=i("6K47"),Pi=i("JLuW"),ji=i("GnQ3"),Ri=i("to83"),Ni=i("HmYF"),Vi=i("xi/V"),Fi=i("Wk3H");let Ui=(()=>{class e{constructor(e,t,a,r,n,o,s,c,p,h,u,m){this._okrService=e,this._lazyService=t,this.udrfService=a,this.dialog=r,this.utilityService=n,this._appOkr=o,this._initStatus=s,this._ErrorService=c,this.excelService=p,this._loginService=h,this._roleService=u,this._toaster=m,this.showSummaryCards=!0,this.isFromCardClick=!1,this.permissionList=null,this.evaluatorResponse=new f.EventEmitter,this.subs=new l.a,this._onAppApiCalled=new D.b,this._onDestroy=new D.b,this.applicationId=188,this.OKRItemDataCurrentIndex=0,this.selectedCard=[],this.minNoOfVisibleSummaryCards=3,this.maxNoOfVisibleSummaryCards=6,this.isCardClicked=!1,this.cardClicked="",this.skip=0,this.limit=10,this.defaultDataRetrievalCount=15,this.check=!1,this.quickCTAInput={},this.commentsInput={},this.commentsContext={},this.mainApiDateRangeEnd=wi(),this.current_year_start=wi(),this.current_year_end=wi(),this.udrfBodyColumns=[{item:"name",header:"Description",isActive:!0,isVisible:"true",type:"textokr",isInlineEdit:!0,inlineEditVarient:["name","simple-text"],position:1,colSize:4,category:["Objective","KeyResult","Initiative"],sortOrder:"N",width:400},{item:"type",header:"type",isActive:!0,isVisible:"true",type:"text",position:2,colSize:1,category:["Objective","KeyResult","Initiative"],sortOrder:"N",width:140},{item:"owner",header:"Owner",isActive:!0,isVisible:"true",type:"profileImage",isInlineEdit:!0,inlineEditVarient:["Objective Owner","search-dropdown"],position:3,colSize:2,category:["Objective","KeyResult","Initiative"],sortOrder:"N",width:180},{item:"period",header:"Period",isActive:!0,isVisible:"true",type:"text",isInlineEdit:!0,inlineEditVarient:["PeriodChange","minimal-dropdown"],position:4,colSize:1,category:["Objective","KeyResult","Initiative"],sortOrder:"N",width:120},{item:"status_name",header:"Status",isActive:!0,isVisible:"true",type:"statusFunction",textClass:"text-center",isInlineEdit:!0,inlineEditVarient:["statusFunction","minimal-dropdown"],position:5,colSize:1,category:["Objective","KeyResult","Initiative"],sortOrder:"N",width:80},{item:"start",header:"Start",headerTextClass:"text-right",isActive:!0,isVisible:"false",type:"okr-tooltip",textClass:"text-right",position:6,colSize:1,category:["Objective","KeyResult","Initiative"],sortOrder:"N",width:75},{item:"end",header:"End",headerTextClass:"text-center",isActive:!0,isVisible:"false",type:"okr-tooltip",textClass:"text-right",position:7,colSize:1,category:["Objective","KeyResult","Initiative"],sortOrder:"N",width:75},{item:"upd_val",header:"Current Value",headerTextClass:"text-center",isActive:!0,isVisible:"false",type:"okr-tooltip",textClass:"text-right",position:11,colSize:1,category:["Objective","KeyResult","Initiative"],sortOrder:"N",width:120},{item:"progress_val",header:"Progress (%)",headerTextClass:"text-center",isActive:!0,isVisible:"true",type:"okr-tooltip",textClass:"text-center",position:8,colSize:1,category:["Objective","KeyResult","Initiative"],sortOrder:"N",width:100},{item:"act_prog_val",header:"Actual Progress (%)",headerTextClass:"text-center",isActive:!0,isVisible:"false",type:"okr-tooltip",textClass:"text-right",position:9,colSize:1,category:["Objective","KeyResult","Initiative"],sortOrder:"N",width:140},{item:"plnd_prog_val",header:"Planned Progress (%)",headerTextClass:"text-center",isActive:!0,isVisible:"false",type:"okr-tooltip",textClass:"text-right",position:10,colSize:1,category:["Objective","KeyResult","Initiative"],sortOrder:"N",width:140},{item:"weightage",header:"Weightage (%)",isActive:!0,isInlineEdit:!0,isVisible:"false",type:"okr-tooltip",textClass:"text-right",inlineEditVarient:["weightage","simple-text"],position:11,colSize:1,category:["Objective","KeyResult","Initiative"],sortOrder:"N",width:120},{item:"plnd_score",header:"Planned Score",headerTextClass:"text-center",isActive:!0,isVisible:"false",type:"okr-tooltip",textClass:"text-right",position:7,colSize:1,category:["Objective","KeyResult","Initiative"],sortOrder:"N",width:120},{item:"act_score",header:"Actual Score",headerTextClass:"text-center",isActive:!0,isVisible:"false",type:"okr-tooltip",textClass:"text-right",position:12,colSize:1,category:["Objective","KeyResult","Initiative"],sortOrder:"N",width:120},{item:"tot_score",header:"Total Score",headerTextClass:"text-center",isActive:!0,isVisible:"false",type:"okr-tooltip",textClass:"text-center",position:13,colSize:1,category:["Objective","KeyResult","Initiative"],sortOrder:"N",width:120},{item:"start_date",header:"Start date",isVisible:"true",isActive:!0,type:"date",colSize:1,textClass:"value13light text-center",position:14,category:["Objective","KeyResult","Initiative"],sortOrder:"N",width:120,isInlineEdit:!0,inlineEditVarient:["Start Date","date-picker"]},{item:"end_date",header:"End date",isVisible:"true",isActive:!0,type:"date",colSize:1,textClass:"value13light text-center",position:15,category:["Objective","KeyResult","Initiative"],sortOrder:"N",width:120,isInlineEdit:!0,inlineEditVarient:["End Date","date-picker"]},{item:"action",isActive:!0,header:"Actions",isVisible:"true",type:"action",position:16,colSize:2,sortOrder:"N",width:230,category:["Objective","KeyResult","Initiative"]}],this.dataTypeArray=[{dataType:"Objective",dataTypeValue:"0",isActive:!0,isVisible:!0,dataTypeCode:"O",cardType:"status",c_type:"Objective",apiCardType:"Objective",apiCardFilter:[]},{dataType:"KeyResult",dataTypeValue:"0",isActive:!1,isVisible:!1,dataTypeCode:"KR",c_type:"KeyResult",cardType:"status",apiCardType:"KeyResult",apiCardFilter:[{filterName:"Result Type",valueId:[{kr:1,init:0}]}]},{dataType:"Initiative",dataTypeValue:"0",isActive:!1,isVisible:!0,dataTypeCode:"INI",cardType:"status",c_type:"Initiative",apiCardType:"Initiative",apiCardFilter:[{filterName:"Result Type",valueId:[{kr:0,init:1}]}]},{dataType:"Completed(KeyResult)",dataTypeValue:"0",c_type:"KeyResult",isActive:!1,isVisible:!1,cardType:"status",statusColor:"#0077b8",dataTypeCode:"COKR",apiCardType:"KeyResult",apiCardFilter:[{filterName:"Result Type",valueId:[{kr:1,init:0}]},{filterName:"KRStatus",valueId:["60530e1a03b72607cd10e306"]}]},{dataType:"On track(KeyResult)",dataTypeValue:"0",c_type:"KeyResult",isActive:!1,isVisible:!1,cardType:"status",statusColor:"#7dd259",dataTypeCode:"OTKR",apiCardType:"KeyResult",apiCardFilter:[{filterName:"Result Type",valueId:[{kr:1,init:0}]},{filterName:"KRStatus",valueId:["60530db703b72607cd10e303"]}]},{dataType:"Not started(KeyResult)",dataTypeValue:"0",c_type:"KeyResult",isActive:!1,isVisible:!1,cardType:"status",statusColor:"#ACA9A6",dataTypeCode:"NSKR",apiCardType:"KeyResult",apiCardFilter:[{filterName:"Result Type",valueId:[{kr:1,init:0}]},{filterName:"KRStatus",valueId:["60530d8a03b72607cd10e302"]}]},{dataType:"At risk(KeyResult)",dataTypeValue:"0",c_type:"KeyResult",isActive:!1,isVisible:!1,cardType:"status",statusColor:"#fe0000",dataTypeCode:"ARKR",apiCardType:"KeyResult",apiCardFilter:[{filterName:"Result Type",valueId:[{kr:1,init:0}]},{filterName:"KRStatus",valueId:["60530df703b72607cd10e305"]}]},{dataType:"Delayed(KeyResult)",dataTypeValue:"0",c_type:"KeyResult",isActive:!1,isVisible:!1,cardType:"status",statusColor:"#febd00",dataTypeCode:"DKR",apiCardType:"KeyResult",apiCardFilter:[{filterName:"Result Type",valueId:[{kr:1,init:0}]},{filterName:"KRStatus",valueId:["60530e4603b72607cd10e307"]}]},{dataType:"On Hold(KeyResult)",dataTypeValue:"0",c_type:"KeyResult",isActive:!1,isVisible:!1,cardType:"status",statusColor:"#9370DB",dataTypeCode:"OHKR",apiCardType:"KeyResult",apiCardFilter:[{filterName:"Result Type",valueId:[{kr:1,init:0}]},{filterName:"KRStatus",valueId:["60a744a7cba307a5aff6a43c"]}]},{dataType:"On track(Initiative)",dataTypeValue:"0",c_type:"Initiative",isActive:!1,isVisible:!1,cardType:"status",statusColor:"#7dd259",dataTypeCode:"OTINI",apiCardType:"Initiative",apiCardFilter:[{filterName:"Result Type",valueId:[{kr:0,init:1}]},{filterName:"InitStatus",valueId:["60530db703b72607cd10e303"]}]},{dataType:"Completed(Initiative)",dataTypeValue:"0",c_type:"Initiative",isActive:!1,isVisible:!1,cardType:"status",statusColor:"#0077b8",dataTypeCode:"COINI",apiCardType:"Initiative",apiCardFilter:[{filterName:"Result Type",valueId:[{kr:0,init:1}]},{filterName:"InitStatus",valueId:["60530e1a03b72607cd10e306"]}]},{dataType:"Not started(Initiative)",dataTypeValue:"0",c_type:"Initiative",isActive:!1,isVisible:!1,cardType:"status",statusColor:"#ACA9A6",dataTypeCode:"NSINI",apiCardType:"Initiative",apiCardFilter:[{filterName:"Result Type",valueId:[{kr:0,init:1}]},{filterName:"InitStatus",valueId:["60530d8a03b72607cd10e302"]}]},{dataType:"At risk(Initiative)",dataTypeValue:"0",c_type:"Initiative",isActive:!1,isVisible:!1,cardType:"status",statusColor:"#fe0000",dataTypeCode:"ARINI",apiCardType:"Initiative",apiCardFilter:[{filterName:"Result Type",valueId:[{kr:0,init:1}]},{filterName:"InitStatus",valueId:["60530df703b72607cd10e305"]}]},{dataType:"Delayed(Initiative)",dataTypeValue:"0",isActive:!1,isVisible:!1,cardType:"status",statusColor:"#febd00",dataTypeCode:"DINI",apiCardType:"Initiative",c_type:"Initiative",apiCardFilter:[{filterName:"Result Type",valueId:[{kr:0,init:1}]},{filterName:"InitStatus",valueId:["60530e4603b72607cd10e307"]}]},{dataType:"On Hold(Initiative)",dataTypeValue:"0",c_type:"Initiative",isActive:!1,isVisible:!1,cardType:"status",statusColor:"#9370DB",dataTypeCode:"OHINI",apiCardType:"Initiative",apiCardFilter:[{filterName:"Result Type",valueId:[{kr:0,init:1}]},{filterName:"InitStatus",valueId:["60a744a7cba307a5aff6a43c"]}]},{dataType:"Planned Score",dataTypeValue:"0",isActive:!1,isVisible:!1,cardType:"status",dataTypeCode:"PS",c_type:"Objective",apiCardType:"Objective",apiCardFilter:[]},{dataType:"Actual Score",dataTypeValue:"0",isActive:!1,isVisible:!1,dataTypeCode:"AS",cardType:"status",c_type:"Objective",apiCardType:"Objective",apiCardFilter:[]},{dataType:"Total Score",dataTypeValue:"0",isActive:!1,isVisible:!1,dataTypeCode:"TS",cardType:"status",c_type:"Objective",apiCardType:"Objective",apiCardFilter:[]},{dataType:"IKR Planned Score",dataTypeValue:"0",isActive:!1,isVisible:!1,cardType:"status",dataTypeCode:"IKRPS",c_type:"Objective",apiCardType:"IKR",apiCardFilter:[]},{dataType:"IKR Actual Score",dataTypeValue:"0",isActive:!1,isVisible:!1,dataTypeCode:"IKRAS",cardType:"status",c_type:"Objective",apiCardType:"IKR",apiCardFilter:[]},{dataType:"IKR Total Score",dataTypeValue:"0",isActive:!1,isVisible:!1,dataTypeCode:"IKRTS",cardType:"status",c_type:"Objective",apiCardType:"IKR",apiCardFilter:[]},{dataType:"IKR",dataTypeValue:"0",isActive:!1,isVisible:!1,dataTypeCode:"IKR",cardType:"status",c_type:"Objective",apiCardType:"IKR",apiCardFilter:[]},{dataType:"KPI",dataTypeValue:"0",isActive:!1,isVisible:!1,dataTypeCode:"KPI",cardType:"status",c_type:"Objective",apiCardType:"KPI",apiCardFilter:[]},{dataType:"BAU",dataTypeValue:"0",isActive:!1,isVisible:!1,dataTypeCode:"BAU",cardType:"status",c_type:"Objective",apiCardType:"BAU",apiCardFilter:[]}],this.udrfItemStatusColor=[{color:"#ACA9A6",status:"Not started"},{color:"#ACA9A6",status:"Not Started"},{color:"#7dd259",status:"On track"},{color:"#fe0000",status:"At risk"},{color:"#0077b8",status:"Completed"},{color:"#febd00",status:"Delayed"},{status:"In progress",color:"orange"},{status:"Missed",color:"brown"},{status:"On Hold",color:"#9370DB"}],this.dateFilters=[{name:"Overdue",date:{startLimit:wi("2018-01-01").format(),endLimit:wi().format(),clicked:!1}},{name:"This Week",date:{startLimit:wi().startOf("week").format(),endLimit:wi().endOf("week").format(),clicked:!1}},{name:"This Month",date:{startLimit:wi().startOf("month").format(),endLimit:wi().endOf("month").format(),clicked:!1}},{name:"Next Month",date:{startLimit:wi().add(1,"month").startOf("month").format(),endLimit:wi().add(1,"month").endOf("month").format()},clicked:!1},{name:"Last Month",date:{startLimit:wi().subtract(1,"month").startOf("month").format(),endLimit:wi().subtract(1,"month").endOf("month").format()},clicked:!1},{name:"Upcoming 3 Months",date:{startLimit:wi().startOf("month").format(),endLimit:wi().add(2,"month").endOf("month").format()},clicked:!1},{name:"This Year",date:{startLimit:wi().startOf("year").format(),endLimit:wi().endOf("year").format()},clicked:!1}],this.defaultFilters=[],this.isAdmin=!1,this.periodDateData=[],this.getStatusMaster=()=>Object(d.c)(this,void 0,void 0,(function*(){this._appOkr.getStatusMaster("key_result").pipe(Object(O.a)(this._onDestroy)).subscribe(e=>{let t=e.data;this._appOkr.getTaskStatus().subscribe(e=>{this.udrfService.udrfUiData.inlineEditDropDownMasterDatas={statusMasterDataL1:t,statusMasterDataL2:e,periodDateData:this.periodDateData}},e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})},e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})})),this.deleteBtnClick=()=>{let e=this.udrfService.udrfUiData.openDeleteButtonData.data;console.log("df",e),"Objective"==e.type||"IKR"==e.type?(console.log(this._roleService.roles),this._appOkr.hasObjAccess("143015","Delete",[e.owner])?this.utilityService.openConfirmationSweetAlertWithCustom("Are you sure you want to delete ?","Once you confirm, this Objective will be deleted !").then(t=>Object(d.c)(this,void 0,void 0,(function*(){if(t){let t={objective_id:e._id};console.log(t),this._appOkr.deleteObj(t).pipe(Object(O.a)(this._onDestroy)).subscribe(e=>{this.utilityService.showMessage("Objective Deleted Successfully","Dismiss"),this.ngOnInit()},e=>{console.log("Error")})}}))):this.utilityService.showMessage("Sorry, you don't have delete access for this objective","Dismiss")):"KeyResult"==e.type||"KPI"==e.type?this._appOkr.hasObjAccess("143016","Delete",[e.owner])?this.utilityService.openConfirmationSweetAlertWithCustom("Are you sure you want to delete ?","Once you confirm, this "+e.type+" will be deleted !").then(t=>Object(d.c)(this,void 0,void 0,(function*(){if(t){let t={Kr_id:e._id};console.log(t),this._appOkr.deleteKr(t).pipe(Object(O.a)(this._onDestroy)).subscribe(t=>{this.utilityService.showMessage(e.type+" Deleted Successfully","Dismiss"),this.ngOnInit()},e=>{console.log("Error")})}}))):this.utilityService.showMessage("Sorry, you don't have delete access for this key result","Dismiss"):"Initiative"==e.type||"BAU"==e.type?this._appOkr.hasObjAccess("143023","Delete",[e.owner])?this.utilityService.openConfirmationSweetAlertWithCustom("Are you sure you want to delete ?","Once you confirm, this "+e.type+" will be deleted !").then(t=>Object(d.c)(this,void 0,void 0,(function*(){if(t){let t={init_id:e._id};console.log(t),this._appOkr.deleteInit(t).pipe(Object(O.a)(this._onDestroy)).subscribe(t=>{this.utilityService.showMessage(e.type+" Deleted Successfully","Dismiss"),this.ngOnInit()},e=>{console.log("Error")})}}))):this.utilityService.showMessage("Sorry, you don't have delete access for this initiative","Dismiss"):"MileStone"==e.type||"Milestone"==e.type?this._appOkr.hasObjAccess("143041","Delete",[e.owner])?this.utilityService.openConfirmationSweetAlertWithCustom("Are you sure you want to delete ?","Once you confirm, this Milestone will be deleted !").then(t=>Object(d.c)(this,void 0,void 0,(function*(){if(t){let t={task_id:e._id};console.log(t),this._appOkr.deleteTask(t).pipe(Object(O.a)(this._onDestroy)).subscribe(e=>{this.utilityService.showMessage("Milestone Deleted Successfully","Dismiss"),this.ngOnInit()},e=>{console.log("Error")})}}))):this.utilityService.showMessage("Sorry, you don't have delete access for this milestone","Dismiss"):this.utilityService.showMessage("Seleted item for delete cannot be recognized. Kindly reach out to KEBS team","Dismiss")},this.addBtnClick=()=>{let e=this.udrfService.udrfUiData.addButtonData;console.log(e),"Objective"==e.data.type||"IKR"==e.data.type||null==e.data.type?this._appOkr.hasObjAccess("143023","Create",[e.data.owner])?this.createInitiative({name:e.data.name,desc:"",_id:e.data.parent_id,start_date:e.data.start_date,end_date:e.data.end_date,owner_id:e.data.owner}):this.utilityService.showMessage("Sorry, you don't have access to create an initiative","Dismiss"):"Initiative"==e.data.type?this._appOkr.hasObjAccess("143041","Create",[e.data.owner])?(console.log("dataa",e),this.createQuickTask(e.data._id,e.data.mapped_under_name?e.data.mapped_under_name:"initiatives")):this.utilityService.showMessage("Sorry, you don't have access to create a milestone","Dismiss"):"MileStone"!=e.data.type&&"Milestone"!=e.data.type||this.utilityService.showMessage("Sorry, Creation of Sub-Tasks is not supported","Dismiss")},this.createQuickTask=(e,t)=>Object(d.c)(this,void 0,void 0,(function*(){let a="key_results"==t?"OKR-Key result":"OKR-Initiative",r=yield this._appOkr.getAppId({appName:a}).toPromise(),n=yield this._appOkr.getAppId({appName:"OKR"}).toPromise();const{QuickTaskComponent:o}=yield i.e(992).then(i.bind(null,"QCtt"));this.dialog.open(o,{width:"60%",height:"100%",position:{right:"0px"},autoFocus:!1,maxWidth:"90vw",data:{appId:n.data.id,subAppId:r.data.id,mapped_under_id:e,collection_name:t,db_name:"okr"}}).afterClosed().subscribe(e=>{this.getAllCardCount()})})),this.openPopUp=()=>{let e,t=this.udrfService.udrfUiData.statusFunctionData,i=this.udrfService.udrfUiData.statusFunctionData.type;if("Objective"==i||"IKR"==i);else if("KeyResult"==i||"Initiative"==i)this._okrService.getPopUpData(i,this.udrfService.udrfUiData.statusFunctionData._id).pipe(Object(O.a)(this._onDestroy)).subscribe(i=>{e=i.data.krDetail;let a=i.data.objectiveId?i.data.objectiveId:i.data.objective_id,r=i.data.isInitiative;this._initStatus.transform(e.status).then(i=>{e.status=i[0],this.check=!0,this.openValMetricPopUp(null==e?void 0:e.upd_val,e,a,r,!0,t)})},e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)});else if("MileStone"==i||"Milestone"==i){let e;this._appOkr.getKrOrInitDetailForTask({task_id:this.udrfService.udrfUiData.statusFunctionData._id,mapped_under_id:this.udrfService.udrfUiData.statusFunctionData.mapped_under_id}).pipe(Object(O.a)(this._onDestroy)).subscribe(i=>{e=i.data,this._initStatus.transform(e.krDetail.status).then(i=>{e.krDetail.status=i[0],this.check=!1,e.isInitiative?this.openValMetricPopUp(e.updatedValue,e.krDetail,0,e.isInitiative,!0,t):this.openValMetricPopUp(e.updatedValue,e.krDetail,e.objectiveId,e.isInitiative,e.showProgressBar,t)})},e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}},this.openValMetricPopUp=(e,t,a,r,n,o)=>Object(d.c)(this,void 0,void 0,(function*(){const{MetricUptValComponent:s}=yield Promise.all([i.e(184),i.e(908)]).then(i.bind(null,"VEKw"));this.dialog.open(s,{width:"50%",height:"100%",position:{right:"0px"},autoFocus:!1,maxWidth:"90vw",data:{updatedValue:e,krDetail:t,objectiveId:a,isInitiative:r,showProgressBar:n}}).afterClosed().subscribe(e=>{var t;if(""!=e)if(console.log(e),console.log(o),1==this.check){let t=this.getItemDataOnIndex(o,"");t.status=e.statusVal._id,t.status_name=e.statusVal.name,t.upd_val=e.updated_val,this.getAllCardCount()}else if(0==this.check){let i,a=this.getBodyData(this.udrfService.udrfBodyData,o,o._id,o.parent_id,o.mapped_under_id);a._id==o.mapped_under_id?i=a:(null===(t=a.children)||void 0===t?void 0:t.length)>0?(this.getInlineEditData(a.children,o.mapped_under_id),i=this.finalData):this.utilityService.showMessage("Not Valid","Dismiss"),i.status=e.statusVal._id,i[i.status_name?"status_name":i.item_status_name?"item_status_name":"status_name"]=e.statusVal.name,i.upd_val=e.updated_val,this.getAllCardCount()}})})),this.callInlineEditApi=()=>{let e=this.udrfService.udrfUiData.inlineEditData.hierarchyLevel;const t=e=>wi(e).format("YYYY-MM-DD");if(console.log(this.udrfService.udrfUiData.inlineEditData),console.log(this.inputData),console.log(this.okrCardClickedData),console.log(this.udrfService.udrfUiData),"l1"==e){let e=this.udrfService.udrfUiData.inlineEditData,i=this.udrfService.udrfUiData.inlineEditData.dataSelected.type,a=this.udrfService.udrfUiData.inlineEditData.inlineEditField;if("Objective"==i||"IKR"==i)if("name"==a)if(this._appOkr.hasObjAccess("143005","Update",[e.dataSelected.owner])){let t=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.name;this._appOkr.updateObjName({objective_id:e.dataSelected._id,okr_name:e.dataSelected.name,title:t}).pipe(Object(O.a)(this._onDestroy)).subscribe(i=>{this.getItemDataOnIndex(e.dataSelected,"").name=t,this.utilityService.showMessage("Objective Name Updated Successfully","Dismiss")},e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}else this.utilityService.showMessage("Sorry, you don't have access to update this objective name","Dismiss");else if("Objective Owner"==a)if(this._appOkr.hasObjAccess("143001","Update",[e.dataSelected.owner])){let t=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.oid;this._appOkr.updateObjOwner({objective_id:e.dataSelected._id,owner_id:t,okr_name:e.dataSelected.name}).pipe(Object(O.a)(this._onDestroy)).subscribe(i=>{this.getItemDataOnIndex(e.dataSelected,"").owner=t},e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}else this.utilityService.showMessage("Sorry, you don't have access to update this objective owner","Dismiss");else if("PeriodChange"==a)if(this._appOkr.hasObjAccess("143040","Update",[e.dataSelected.owner])){let t=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.type,i=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.startDate,a=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.endDate;this._appOkr.updateObjPeriod({objective_id:e.dataSelected._id,period_value:t,startDate:i,endDate:a}).pipe(Object(O.a)(this._onDestroy)).subscribe(r=>{var n;let o=this.getItemDataOnIndex(e.dataSelected,"");o&&(o.period=t,o.start_date=i,o.end_date=a,(null===(n=o.children)||void 0===n?void 0:n.length)>0?(o.children.forEach(e=>{"Initiative"==e.type&&(e.period=t,e.obj_start_date=i,e.obj_end_date=a,e.start_date=i,e.end_date=a)}),this.utilityService.showMessage("Objective and Initiative Period Updated Successfully","Dismiss")):this.utilityService.showMessage("Objective Period Updated Successfully","Dismiss"))},e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}else this.utilityService.showMessage("Sorry, you don't have access to update this objective period","Dismiss");else if("weightage"==a)if(this._appOkr.hasObjAccess("143004","Update",[e.dataSelected.owner])){let t=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.weightage;this._appOkr.updateObjWeightage({okr_name:e.dataSelected.name,objective_id:e.dataSelected._id,weightage:t}).pipe(Object(O.a)(this._onDestroy)).subscribe(i=>{this.getItemDataOnIndex(e.dataSelected,"").weightage=t},e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}else this.utilityService.showMessage("Sorry, you don't have access to update this objective weightage","Dismiss");else("Start Date"==a||"End Date"==a)&&this.utilityService.showMessage("can't update at Objective level","Dismiss",3e3);if("KeyResult"==i||"KPI"==i){let i=this.udrfService.udrfUiData.inlineEditData.inlineEditField,r=this.getBodyData(this.udrfService.udrfBodyData,e.dataSelected,e.dataSelected._id,e.dataSelected.parent_id,"");if("name"==i){let t=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.name;this._appOkr.updateKrName({key_result_id:e.dataSelected._id,okr_name:r.name,title:t}).pipe(Object(O.a)(this._onDestroy)).subscribe(i=>{let a=this.getItemDataOnIndex(e.dataSelected,"");a[a.name?"name":a.item_name?"item_name":"name"]=t,this.utilityService.showMessage("Key Result Name Updated Successfully","Dismiss")},e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}else if("Objective Owner"==i){let t=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.oid;this._appOkr.updateKrOwner({key_result_id:e.dataSelected._id,okr_name:r.name,owner_id:t}).pipe(Object(O.a)(this._onDestroy)).subscribe(i=>{let a=this.getItemDataOnIndex(e.dataSelected,"");a[a.owner?"owner":a.item_owner?"item_owner":"owner"]=t,this.utilityService.showMessage(" KR Owner Updated Successfully","Dismiss")},e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}else if("PeriodChange"==i){let i=e.dataSelected._id,a=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.type,r=t(this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.startDate),n=t(this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.endDate),o=t(e.dataSelected.obj_start_date),s=t(e.dataSelected.obj_end_date);wi(r).isSameOrAfter(o)&&wi(n).isSameOrBefore(s)?this._appOkr.updateKrPeriod({key_result_id:i,period_value:a,startDate:r,endDate:n}).pipe(Object(O.a)(this._onDestroy)).subscribe(t=>{let i=this.getItemDataOnIndex(e.dataSelected,"");i.period=a,i.start_date=r,i.end_date=n,this.utilityService.showMessage(" KR Period Updated Successfully","Dismiss")},e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)}):this.utilityService.showMessage("Start Date and End Date of Key Result should be within Objective Start Date and End Date !","dismiss")}else if("weightage"==i){let t=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.weightage;this._appOkr.updateKrWeightage({key_result_id:e.dataSelected._id,weightage:t}).pipe(Object(O.a)(this._onDestroy)).subscribe(i=>{let a=this.getItemDataOnIndex(e.dataSelected,e.dataSelected._id);a[a.weightage?"weightage":a.item_weightage?"item_weightage":"weightage"]=t,this.utilityService.showMessage("Key Result Weightage Updated Successfully","Dismiss")},e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}else if("tObjname"==a){console.log("hi",e.dataSelected);let t=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.tObjname;this._appOkr.updateObjName({objective_id:e.dataSelected.parent_id,okr_name:e.dataSelected.obj_name,title:t}).pipe(Object(O.a)(this._onDestroy)).subscribe(i=>{let a=this.getItemDataOnIndex(e.dataSelected,e.dataSelected._id);console.log("tname",a),a.obj_name=t,this.utilityService.showMessage("Objective Name Updated Successfully","Dismiss")},e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}else if("KR/Task Name"==a){let t=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse["KR/Task Name"];this._appOkr.updateKrName({key_result_id:e.dataSelected._id,okr_name:e.dataSelected.obj_name,title:t}).pipe(Object(O.a)(this._onDestroy)).subscribe(i=>{let a=this.getItemDataOnIndex(e.dataSelected,e.dataSelected._id);console.log("ttname",a),a.item_name=t,this.utilityService.showMessage("Key Result Name Updated Successfully","Dismiss")},e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}else if("Owner.T"==a){let t=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.oid;this._appOkr.updateKrOwner({key_result_id:e.dataSelected._id,okr_name:e.dataSelected.obj_name,owner_id:t}).pipe(Object(O.a)(this._onDestroy)).subscribe(i=>{this.getItemDataOnIndex(e.dataSelected,e.dataSelected._id).item_owner=t,this.utilityService.showMessage(" KR Owner Updated Successfully","Dismiss")},e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}else"Start Date"==a?(this.convertToLocalTime(this.udrfService.udrfUiData.inlineEditData.inlineEditResponse["Start Date"]),wi(this.udrfService.udrfUiData.inlineEditData.inlineEditResponse["Start Date"]).format("YYYY-MM-DD"),this.utilityService.showMessage("can't update at l1 level","Dismiss",3e3)):"End Date"==a&&(this.convertToLocalTime(this.udrfService.udrfUiData.inlineEditData.inlineEditResponse["End Date"]),wi(this.udrfService.udrfUiData.inlineEditData.inlineEditResponse["End Date"]).format("YYYY-MM-DD"),this.utilityService.showMessage("can't update at l1 level","Dismiss",3e3))}if("Initiative"==i||"BAU"==i){let i=this.udrfService.udrfUiData.inlineEditData.inlineEditField;console.log("checkk",e);let r=this.getBodyData(this.udrfService.udrfBodyData,e.dataSelected,e.dataSelected._id,e.dataSelected.parent_id,"");if("name"==i)if(this._appOkr.hasObjAccess("143021","Update",[e.dataSelected.owner])){let t=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.name;this._appOkr.updateInitName({initiative_id:e.dataSelected._id,okr_name:r.name,title:t}).pipe(Object(O.a)(this._onDestroy)).subscribe(i=>{this.getItemDataOnIndex(e.dataSelected,"").name=t,this.utilityService.showMessage("Initiative Name Updated Successfully","Dismiss")},e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}else this.utilityService.showMessage("Sorry, you don't have access to update this initiative name","Dismiss");else if("Objective Owner"==i)if(this._appOkr.hasObjAccess("143026","Update",[e.dataSelected.owner])){let t=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.oid;this._appOkr.updateInitOwner({initiative_id:e.dataSelected._id,okr_name:r.name,owner_id:t}).pipe(Object(O.a)(this._onDestroy)).subscribe(i=>{this.getItemDataOnIndex(e.dataSelected,"").owner=t,this.utilityService.showMessage(" Initiative Owner Updated Successfully","Dismiss")},e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}else this.utilityService.showMessage("Sorry, you don't have access to update this initiative owner","Dismiss");else if("PeriodChange"==i)if(this._appOkr.hasObjAccess("143028","Update",[e.dataSelected.owner])){let i=e.dataSelected._id,a=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.type,r=t(this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.startDate),n=t(this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.endDate),o=t(e.dataSelected.obj_start_date),s=t(e.dataSelected.obj_end_date);if(!wi(r,"YYYY-MM-DD").isBetween(wi(o,"YYYY-MM-DD"),wi(s,"YYYY-MM-DD"),"day","[]")||!wi(n,"YYYY-MM-DD").isBetween(wi(o,"YYYY-MM-DD"),wi(s,"YYYY-MM-DD"),"day","[]"))return void this.utilityService.showMessage("The start and end dates of the initiative should fall within the start and end dates of the objective or within the financial year/quarter selected for the objective.","Dismiss");this._appOkr.updateInitPeriod({initiative_id:i,period_value:a,startDate:r,endDate:n}).pipe(Object(O.a)(this._onDestroy)).subscribe(t=>{var i;let o=this.getItemDataOnIndex(e.dataSelected,"");o&&(o.period=a,o.start_date=r,o.end_date=n,(null===(i=o.children)||void 0===i?void 0:i.length)>0?(o.children.forEach(e=>{"KeyResult"==e.type&&(e.period=a,e.start_date=r,e.end_date=n)}),this.utilityService.showMessage("Initiative and Key Result Period Updated Successfully","Dismiss")):this.utilityService.showMessage("Objective Period Updated Successfully","Dismiss")),this.utilityService.showMessage(" Initiative Period Updated Successfully","Dismiss")},e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}else this.utilityService.showMessage("Sorry, you don't have access to update this initiative period","Dismiss");else if("weightage"==i)if(this._appOkr.hasObjAccess("143018","Update",[e.dataSelected.owner])){console.log("wei hi",e);let t=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.weightage;this._appOkr.updateInitWeightage({initiative_id:e.dataSelected._id,weightage:t}).pipe(Object(O.a)(this._onDestroy)).subscribe(i=>{let a=this.getItemDataOnIndex(e.dataSelected,e.dataSelected._id);a[a.weightage?"weightage":a.item_weightage?"item_weightage":"weightage"]=t,this.utilityService.showMessage("Initiative Weightage Updated Successfully","Dismiss")},e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}else this.utilityService.showMessage("Sorry, you don't have access to update this initiative weightage","Dismiss");else if("tObjname"==a){let t=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.tObjname;this._appOkr.updateObjName({objective_id:e.dataSelected.parent_id,okr_name:e.dataSelected.obj_name,title:t}).pipe(Object(O.a)(this._onDestroy)).subscribe(i=>{this.getItemDataOnIndex(e.dataSelected,e.dataSelected._id).obj_name=t,this.utilityService.showMessage("Objective Name Updated Successfully","Dismiss")},e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}else if("KR/Task Name"==a){let t=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse["KR/Task Name"];this._appOkr.updateInitName({initiative_id:e.dataSelected._id,okr_name:e.dataSelected.obj_name,title:t}).pipe(Object(O.a)(this._onDestroy)).subscribe(i=>{this.getItemDataOnIndex(e.dataSelected,e.dataSelected._id).item_name=t,this.utilityService.showMessage("Initiative Name Updated Successfully","Dismiss")},e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}else if("Owner.T"==a)if(this._appOkr.hasObjAccess("143040","Update",[e.dataSelected.owner])){let t=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.oid;this._appOkr.updateInitOwner({initiative_id:e.dataSelected._id,okr_name:e.dataSelected.obj_name,owner_id:t}).pipe(Object(O.a)(this._onDestroy)).subscribe(i=>{this.getItemDataOnIndex(e.dataSelected,e.dataSelected._id).item_owner=t,this.utilityService.showMessage(" Init Owner Updated Successfully","Dismiss")},e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}else this.utilityService.showMessage("Sorry, you don't have access to update this initiative weightage","Dismiss");else"Start Date"==a?(this.convertToLocalTime(this.udrfService.udrfUiData.inlineEditData.inlineEditResponse["Start Date"]),wi(this.udrfService.udrfUiData.inlineEditData.inlineEditResponse["Start Date"]).format("YYYY-MM-DD"),this.utilityService.showMessage("can't update at l1 level","Dismiss",3e3)):"End Date"==a&&(this.convertToLocalTime(this.udrfService.udrfUiData.inlineEditData.inlineEditResponse["End Date"]),wi(this.udrfService.udrfUiData.inlineEditData.inlineEditResponse["End Date"]).format("YYYY-MM-DD"),this.utilityService.showMessage("can't update at l1 level","Dismiss",3e3))}if("MileStone"==i||"Milestone"==i){let t=this.udrfService.udrfUiData.inlineEditData.inlineEditField;if("name"==t)if(this._appOkr.hasObjAccess("143042","Update",[e.dataSelected.owner])){let t=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.name?this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.name:this.udrfService.udrfUiData.inlineEditData.inlineEditResponse["KR/Task Name"];this._appOkr.updateTaskDetail({task_id:e.dataSelected._id,update_field:"title",update_value:t}).pipe(Object(O.a)(this._onDestroy)).subscribe(i=>{let a=this.getItemDataOnIndex(e.dataSelected,e.dataSelected.mapped_under_id);console.log(e.dataSelected),console.log(a),a[a.name?"name":a.item_name?"item_name":"name"]=t,this.utilityService.showMessage("Task Name Updated Successfully","Dismiss")},e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}else this.utilityService.showMessage("Sorry, you don't have access to update this milestone name","Dismiss");else if("Owner.T"==t||"Objective Owner"==t)if(this._appOkr.hasObjAccess("143043","Update",[e.dataSelected.owner])){let t=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.oid;this._appOkr.updateTaskDetail({task_id:e.dataSelected._id,update_field:"owner",update_value:t}).pipe(Object(O.a)(this._onDestroy)).subscribe(i=>{let a=this.getItemDataOnIndex(e.dataSelected,e.dataSelected.mapped_under_id);a[a.owner?"owner":a.item_owner?"item_owner":"owner"]=t,this.utilityService.showMessage("Task Owner Updated Successfully","Dismiss")},e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}else this.utilityService.showMessage("Sorry, you don't have access to update this milestone owner","Dismiss");else if("PeriodChange"==t)this.utilityService.showMessage("can't update milestone period","Dismiss",3e3);else if("KR/Task Name"==t){let t=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse["KR/Task Name"];this._appOkr.updateTaskDetail({task_id:e.dataSelected._id,update_field:"title",update_value:t}).pipe(Object(O.a)(this._onDestroy)).subscribe(i=>{this.getItemDataOnIndex(e.dataSelected,e.dataSelected.mapped_under_id).item_name=t,this.utilityService.showMessage("Task Name Updated Successfully","Dismiss")},e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}else if("statusFunction"==t)this._appOkr.hasObjAccess("143044","Update",[e.dataSelected.owner])?this._appOkr.updateTaskStatus({task_id:this.udrfService.udrfUiData.inlineEditData.dataSelected._id,status:this.udrfService.udrfUiData.inlineEditData.inlineEditResponse}).pipe(Object(O.a)(this._onDestroy)).subscribe(t=>{if(this.utilityService.showMessage(t.msg,"dismiss",2e3),"Please update the task during it's time period"!=t.msg){let t=this.getItemDataOnIndex(e.dataSelected,e.dataSelected.mapped_under_id),i=t.status_name?"status_name":t.item_status_name?"item_status_name":"status_name";t.status_id=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse._id,t[i]=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.name,this.getAllCardCount()}},e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)}):this.utilityService.showMessage("Sorry, you don't have access to update this milestone status","Dismiss");else if("Start Date"==t)if(this._appOkr.hasObjAccess("143045","Update",[e.dataSelected.owner])){let t=wi(this.udrfService.udrfUiData.inlineEditData.inlineEditResponse["Start Date"]).format("YYYY-MM-DD"),i=wi(e.dataSelected.due_date).format("YYYY-MM-DD");if(wi(i)>=wi(t)){let t=this.convertToLocalTime(this.udrfService.udrfUiData.inlineEditData.inlineEditResponse["Start Date"]),i=wi(this.udrfService.udrfUiData.inlineEditData.inlineEditResponse["Start Date"]).format("YYYY-MM-DD");this._appOkr.updateTaskDetail({task_id:this.udrfService.udrfUiData.inlineEditData.dataSelected._id,update_field:"start date",update_value:i}).pipe(Object(O.a)(this._onDestroy)).subscribe(i=>Object(d.c)(this,void 0,void 0,(function*(){this.utilityService.showMessage("Start Date Updated Successfully","Dismiss",3e3),this.getItemDataOnIndex(e.dataSelected,e.dataSelected.mapped_under_id).start_date=t})),e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}else this.utilityService.showMessage("Invalid start date","Dismiss",3e3)}else this.utilityService.showMessage("Sorry, you don't have access to update this milestone start date","Dismiss");else if("End Date"==t)if(this._appOkr.hasObjAccess("143045","Update",[e.dataSelected.owner])){let t=wi(e.dataSelected.start_date).format("YYYY-MM-DD"),i=wi(this.udrfService.udrfUiData.inlineEditData.inlineEditResponse["End Date"]).format("YYYY-MM-DD");if(wi(i)>=wi(t)){let t=this.convertToLocalTime(this.udrfService.udrfUiData.inlineEditData.inlineEditResponse["End Date"]),i=wi(this.udrfService.udrfUiData.inlineEditData.inlineEditResponse["End Date"]).format("YYYY-MM-DD");this._appOkr.updateTaskDetail({task_id:this.udrfService.udrfUiData.inlineEditData.dataSelected._id,update_field:"end date",update_value:i}).pipe(Object(O.a)(this._onDestroy)).subscribe(i=>Object(d.c)(this,void 0,void 0,(function*(){this.utilityService.showMessage("End Date Updated Successfully","Dismiss",3e3),this.getItemDataOnIndex(e.dataSelected,e.dataSelected.mapped_under_id).end_date=t})),e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}else this.utilityService.showMessage("Invalid end date","Dismiss",3e3)}else this.utilityService.showMessage("Sorry, you don't have access to update this initiative weightage","Dismiss");else if("weightage"==t){let t=this.udrfService.udrfUiData.inlineEditData.inlineEditResponse.weightage;this._appOkr.updateTaskWeightage({task_id:e.dataSelected._id,mapped_under_id:e.dataSelected.mapped_under_id,upd_val:Number(t)}).pipe(Object(O.a)(this._onDestroy)).subscribe(i=>{let a=this.getItemDataOnIndex(e.dataSelected,e.dataSelected.mapped_under_id);a[a.weightage?"weightage":a.item_weightage?"item_weightage":"weightage"]=t,this.utilityService.showMessage("Task Weightage Updated Successfully","Dismiss")},e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}}}},this.dataTypeCardSelected=()=>{this.udrfService.udrfData.isItemDataLoading=!0,this.udrfService.udrfData.noItemDataFound=!1;let e=this.udrfService.udrfUiData.summaryCardsItem;for(let t=0;t<this.dataTypeArray.length;t++)if(e.dataTypeCode==this.dataTypeArray[t].dataTypeCode)this.dataTypeArray[t].isActive=!0,e.isActive=!0;else{let e=xi.where(this.udrfService.udrfUiData.summaryCards,{dataTypeCode:this.dataTypeArray[t].dataTypeCode});e.length>0&&(e[0].isActive=!1),this.dataTypeArray[t].isActive=!1}this.isCardClicked=!0,this.cardClicked=e.dataType,this.okrCardClickedData=1==e.isActive?e:null,this.OKRItemDataCurrentIndex=0,this.udrfService.udrfBodyData=[],this.skip=0,this.limit=this.defaultDataRetrievalCount,this.isFromCardClick=!0,this.changeVisibleBodyCol(),this.getOKRList(!1)},this.convertToLocalTime=e=>{let t=new Date(e),i=6e4*t.getTimezoneOffset();return t.setTime(t.getTime()-i),t},this.okrCardClicked=()=>{},this.openAggregativePopup=(e,t)=>Object(d.c)(this,void 0,void 0,(function*(){const{AggregativeOkrComponent:a}=yield i.e(826).then(i.bind(null,"Fkny"));this.dialog.open(a,{width:"250px",autoFocus:!1,data:{id:e,name:t}}).afterClosed().subscribe(e=>{"sucess"==e&&this.ngOnInit()})})),this.getDefaultFilters=()=>new Promise((e,t)=>{this._appOkr.getDefaultFilters().subscribe(t=>{e(t.data)},e=>{t([])})}),this.getDurationType=()=>Object(d.c)(this,void 0,void 0,(function*(){this.subs.sink=this._appOkr.getDurationTypesMaster().subscribe(e=>{e.data&&(this.periodDateData=e.data)},e=>{console.error(e),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})})),this.current_year_start=wi().startOf("year"),this.current_year_end=wi().endOf("year")}ngOnInit(){return Object(d.c)(this,void 0,void 0,(function*(){this.permissionList=this._appOkr.getRoleMatrix(),yield this._appOkr.getSummaryCardCongfigsOKRList().pipe(Object(O.a)(this._onDestroy)).subscribe(e=>{this.categorisedDataTypeArray="S"==e.messType?e.data:[],this.udrfService.udrfUiData.categorisedSummaryCards=this.categorisedDataTypeArray,console.log("Summary Card Data Config : ",this.categorisedDataTypeArray),console.log("Summary Card Data Config 2 : ",JSON.stringify(this.categorisedDataTypeArray))}),this._okrService.$showSummaryCard.subscribe(e=>{this.udrfService.udrfUiData.summaryViewActivated=e}),this.OKRItemDataCurrentIndex=0,this.udrfService.udrfBodyData=[],this.udrfService.udrfData.applicationId=this.applicationId,this.udrfService.udrfUiData.showNewReleasesButton=!1,this.udrfService.udrfUiData.showItemDataCount=!1,this.udrfService.udrfUiData.showSearchBar=!0,this.udrfService.udrfUiData.showActionButtons=!0,this.udrfService.udrfUiData.showUdrfModalButton=!0,this.udrfService.udrfUiData.showSettingsModalButton=!0,this.udrfService.udrfUiData.isReportDownloading=!1,this.udrfService.udrfUiData.showReportDownloadButton=!1,this.udrfService.udrfUiData.showColumnConfigButton=!0,this.udrfService.udrfUiData.itemHasStatusFunctionBtn=!0,this.udrfService.udrfUiData.itemHasQuickCta=!1,this.udrfService.udrfUiData.itemHasComments=!0,this.udrfService.udrfUiData.collapseAll=!1,this.udrfService.udrfUiData.showCollapseButton=!0,this.udrfService.udrfUiData.horizontalScroll=!0,this.udrfService.udrfUiData.itemHasAddBtn=!0,this.udrfService.udrfUiData.itemHasDeleteBtn=!0,this.udrfService.udrfUiData.itemHasOpenBtn=!0,this.udrfService.udrfUiData.itemHasCollabarationButton=!0,this.udrfService.udrfUiData.itemHasIntegrationButton=!1,this.udrfService.udrfUiData.itemHasAggregationButton=!1,this.udrfService.udrfUiData.summaryViewActivated=this.showSummaryCards,this.udrfService.udrfUiData.variant=4,this.udrfService.udrfUiData.itemDataType="",this.udrfService.udrfUiData.totalItemDataCount=0,this.udrfService.udrfUiData.summaryCardsItem={},this.udrfService.udrfUiData.inlineEditData={},this.udrfService.udrfUiData.downloadItemDataReport=()=>{},this.udrfService.udrfUiData.udrfItemStatusColor=this.udrfItemStatusColor,this.udrfService.udrfUiData.summaryCards=this.dataTypeArray,this.udrfService.udrfUiData.udrfBodyColumns=this.udrfBodyColumns,this.udrfService.udrfUiData.udrfVisibleBodyColumns=this.udrfService.udrfUiData.udrfVisibleBodyColumns,this.udrfService.udrfUiData.udrfInvisibleBodyColumns=this.udrfService.udrfUiData.udrfInvisibleBodyColumns,this.udrfService.udrfUiData.minNoOfVisibleSummaryCards=this.minNoOfVisibleSummaryCards,this.udrfService.udrfUiData.maxNoOfVisibleSummaryCards=this.maxNoOfVisibleSummaryCards,this.udrfService.udrfUiData.selectedCard=this.selectedCard,this.udrfService.udrfUiData.quickCTAInput=this.quickCTAInput,this.udrfService.udrfUiData.commentsInput=this.commentsInput,this.udrfService.udrfUiData.commentsContext=this.commentsContext,this.udrfService.udrfUiData.resolveVisibleSummaryCards=this.resolveVisibleDataTypeArray.bind(this),this.udrfService.udrfUiData.summaryCardsSelected=this.dataTypeCardSelected.bind(this),this.udrfService.udrfUiData.openComments=this.openComments.bind(this),this.udrfService.udrfUiData.openQuickCta=this.openCTA.bind(this),this.udrfService.udrfUiData.itemDataScrollDown=this.onOKRItemDataScrollDown.bind(this),this.udrfService.udrfUiData.itemcardSelected=this.okrCardClicked.bind(this),this.udrfService.udrfUiData.statusFunction=this.openPopUp.bind(this),this.udrfService.udrfUiData.callInlineEditApi=this.callInlineEditApi.bind(this),this.udrfService.udrfUiData.addBtnClicked=this.addBtnClick.bind(this),this.udrfService.udrfUiData.openDeleteButton=this.deleteBtnClick.bind(this),this.udrfService.udrfUiData.openBtnClicked=this.openBtnClick.bind(this),this.udrfService.udrfUiData.okrCollabaration=this.openCollabrative.bind(this),this.udrfService.udrfUiData.okrAggregation=this.openAggregate.bind(this),this.udrfService.udrfUiData.downloadFile=()=>{},this.udrfService.udrfUiData.downloadItemDataReport=this.downloadReport.bind(this),this.udrfService.udrfUiData.getCollaborativeDetails=this.getCollaborativeDetails.bind(this),this.udrfService.udrfUiData.getChildAggregationDetails=this.getChildAggregationDetails.bind(this),this.udrfService.udrfUiData.getParentAggregationDetails=this.getParentAggregationDetails.bind(this),this.checkAdminAccess();let e=[],t=yield this._okrService.getQuarterDate();for(let o of t)e.push({checkboxId:o.id,checkboxName:o.type,checkboxStartValue:o.startDate,checkboxEndValue:o.endDate,isCheckboxDefaultSelected:o.is_default});this.filterYears=t,this.udrfService.udrfFunctions.constructCustomRangeData(10,"date",e);let i=[{checkboxId:"CDCRD",checkboxName:"Current Year",checkboxStartValue:this.current_year_start,checkboxEndValue:this.current_year_end,isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD2",checkboxName:"This Week",checkboxStartValue:this.utilityService.getFormattedDate(wi().startOf("week"),wi(wi().startOf("week")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(wi().endOf("week"),wi(wi().endOf("week")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD3",checkboxName:"This Month",checkboxStartValue:this.utilityService.getFormattedDate(wi().startOf("month"),wi(wi().startOf("month")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(wi().endOf("month"),wi(wi().endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD4",checkboxName:"Next Month",checkboxStartValue:this.utilityService.getFormattedDate(wi().add(1,"month").startOf("month"),wi(wi().add(1,"month").startOf("month")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(wi().add(1,"month").endOf("month"),wi(wi().add(1,"month").endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD5",checkboxName:"Upcoming 3 Months",checkboxStartValue:this.utilityService.getFormattedDate(wi().startOf("month"),wi(wi().startOf("month")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(wi().add(2,"month").endOf("month"),wi(wi().add(2,"month").endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD6",checkboxName:"OverDue",isCheckboxDefaultSelected:!1}],a=[{checkboxId:"CDCRD",checkboxName:"Current Year",checkboxStartValue:this.current_year_start,checkboxEndValue:this.current_year_end,isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD2",checkboxName:"This Week",checkboxStartValue:this.utilityService.getFormattedDate(wi().startOf("week"),wi(wi().startOf("week")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(wi().endOf("week"),wi(wi().endOf("week")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD3",checkboxName:"This Month",checkboxStartValue:this.utilityService.getFormattedDate(wi().startOf("month"),wi(wi().startOf("month")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(wi().endOf("month"),wi(wi().endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD4",checkboxName:"Next Month",checkboxStartValue:this.utilityService.getFormattedDate(wi().add(1,"month").startOf("month"),wi(wi().add(1,"month").startOf("month")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(wi().add(1,"month").endOf("month"),wi(wi().add(1,"month").endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD5",checkboxName:"Upcoming 3 Months",checkboxStartValue:this.utilityService.getFormattedDate(wi().startOf("month"),wi(wi().startOf("month")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(wi().add(2,"month").endOf("month"),wi(wi().add(2,"month").endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1}];this.udrfService.udrfFunctions.constructCustomRangeData(12,"date",i),this.udrfService.udrfFunctions.constructCustomRangeData(13,"date",a);let r=[],n=yield this._okrService.getAllProgValFilter();for(let o of n)r.push({checkboxName:o.range,checkboxStartValue:o.lower_limit,checkboxEndValue:o.upper_limit,isCheckboxDefaultSelected:!1});this.udrfService.udrfFunctions.constructCustomRangeData(11,"value",r),yield this.getDurationType(),this.udrfService.getAppUdrfConfig(this.applicationId,this.initReport.bind(this)),yield this.getStatusMaster(),this.udrfService.udrfUiData.isReportDownloading=!1,this._okrService.selectedOrg$.subscribe(e=>{console.log("Selected organization:",e),this.selectedOrgForDownload=String(e.code),this.selectedOrgNameForDownload=e.name,console.log(e),console.log(e.code),console.log(this.selectedOrgForDownload,"yahooo")})}))}downloadReport(){this.downloadOkrChartReport()}downloadOkrChartReportForAdmin(){this.udrfService.udrfUiData.isReportDownloading=!0,this._appOkr.getOkrOrgChartDetails(this.fyYear).subscribe(e=>{console.log(e),console.log("OKR Download Data");let t=e.data;if(!t||0===t.length)return void this._toaster.showWarning("No data available for download","Dismiss",1e3);const i=new Ii.Workbook,a=i.addWorksheet("OKR Report");a.columns=[{width:15},{width:30},{width:30},{width:35},{width:20},{width:20},{width:20},{width:20},{width:30},{width:30},{width:20},{width:20},{width:20}];const r=a.getRow(2);r.eachCell(e=>{e.alignment={horizontal:"center",vertical:"middle"}}),r.height=30,a.mergeCells(2,1,2,13);const n=r.getCell(1);n.value="OKR Report",n.font={name:"Segoe UI Light",size:22,bold:!0},n.alignment={horizontal:"left",vertical:"middle",wrapText:!0},this.applyStylesToCell(n,{border:!0,bgColor:"C0C0C0"}),this.applyStylesToCell(n,{border:!0,bgColor:"C0C0C0",font:{bold:!0,color:{argb:"000000"},size:12}}),a.addRow(["ORG Code","ORG Name","Objective Name","Objective Owner","Objective Owner Email","Start Date","End Date","Financial Year","Weightage","Created By","Created By Email ID","Actual Progress Value","Planned Progress Value","Total Score"]).eachCell({includeEmpty:!0},e=>{this.applyStylesToCell(e,{font:{bold:!0}})}),t.forEach(e=>{a.addRow([e.Org_id||"",e.Org_name||"",e.objective_name||"",e.owner_name||"",e.owner_email||"",e.objective_start_date||"",e.objective_end_date||"",e.fy_year||"",e.objective_weightage||"",e.created_by_name||"",e.created_by_email||"",e.actual_prog_val||"-",e.planned_prog_val||"-",e.total_score||"-"])}),i.xlsx.writeBuffer().then(e=>{const t=new Blob([e],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"});Mi(t,"okr_admin_report.xlsx")}),this.udrfService.udrfUiData.isReportDownloading=!1},e=>{console.error("Error fetching data:",e),this._toaster.showWarning("Couldn't get the excel report!","Dismiss",1e3),this.udrfService.udrfUiData.isReportDownloading=!1})}downloadOkrChartReport(){this.udrfService.udrfUiData.isReportDownloading=!0,this._okrService.getOKRList(this.downloadFilterConfig).subscribe(e=>{console.log(e),console.log("OKR Download Data");let t=e.data;if(!t||0===t.length)return this._toaster.showWarning("No data available for download","Dismiss",1e3),void(this.udrfService.udrfUiData.isReportDownloading=!1);const i=new Ii.Workbook,a=i.addWorksheet("OKR Report");a.columns=[{width:15},{width:30},{width:40},{width:25},{width:30},{width:30},{width:30},{width:20},{width:20},{width:20},{width:20},{width:40}];const r=a.getRow(2);r.eachCell(e=>{e.alignment={horizontal:"center",vertical:"middle"}}),r.height=30,a.mergeCells(2,1,2,15);const n=r.getCell(1);n.value="OKR Report",n.font={name:"Segoe UI Light",size:22,bold:!0},n.alignment={horizontal:"left",vertical:"middle",wrapText:!0},this.applyStylesToCell(n,{border:!0,bgColor:"C0C0C0"}),this.applyStylesToCell(n,{border:!0,bgColor:"C0C0C0",font:{bold:!0,color:{argb:"000000"},size:12}}),a.addRow(["ORG Code","ORG Name","Name","Type","Start Date","End Date","Financial Year","Weightage","Actual Progress Value","Planned Progress Value","Total Score","Parent Name"]).eachCell({includeEmpty:!0},e=>{this.applyStylesToCell(e,{font:{bold:!0}})});const o=(e,t,i="-")=>{t.forEach(t=>{const a=[this.selectedOrgForDownload||"",this.selectedOrgNameForDownload||"",t.name||"",t.type||"",this.formatDateForDownload(t.start_date)||"",this.formatDateForDownload(t.end_date)||"",t.period||"",t.weightage||"",t.act_prog_val||"-",t.plnd_prog_val||"-",t.tot_score||"-",i||"-"];e.addRow(a),t.children&&t.children.length>0&&o(e,t.children,t.name||"-")})};o(a,t),i.xlsx.writeBuffer().then(e=>{const t=new Blob([e],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"});Mi(t,"OKR_Report.xlsx"),this.udrfService.udrfUiData.isReportDownloading=!1})},e=>{console.error("Error fetching data:",e),this._toaster.showWarning("Couldn't get the excel report!","Dismiss",1e3),this.udrfService.udrfUiData.isReportDownloading=!1})}formatDateForDownload(e){return e?wi(e).format("DD-MMM-YYYY"):""}applyStylesToCell(e,t){if(t.border){const t={style:"thin",color:{argb:"BFBFBF"}};e.border={bottom:t,left:t,right:t,top:t}}t.bgColor&&(e.fill={type:"pattern",pattern:"solid",fgColor:{argb:t.bgColor}}),t.font&&(e.font=t.font)}checkAdminAccess(){let e={employee_oid:this._loginService.getProfile().profile.oid,configuration_name:"okr_roles",role_name:"Admin"};this.subs.sink=this._appOkr.checkAdmin(e).subscribe(e=>{this.isAdmin=!!e.data},e=>{this.udrfService.udrfUiData.showReportDownloadButton=!1,this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}createInitiative(e){return Object(d.c)(this,void 0,void 0,(function*(){const{InitiativeCreationComponent:t}=yield Promise.all([i.e(8),i.e(905)]).then(i.bind(null,"e+FK"));this.dialog.open(t,{width:"88%",height:"100%",position:{right:"0px"},autoFocus:!1,maxWidth:"90vw",data:{"creation-type":"obj",data:e,name:"Initiative"}}).afterClosed().subscribe(e=>{this.getAllCardCount()})}))}createBAU(e,t){return Object(d.c)(this,void 0,void 0,(function*(){const{InitiativeCreationComponent:a}=yield Promise.all([i.e(8),i.e(905)]).then(i.bind(null,"e+FK"));this.dialog.open(a,{width:"88%",height:"100%",position:{right:"0px"},autoFocus:!1,maxWidth:"90vw",data:{"creation-type":"obj",data:t,cardClickedData:e,name:"BAU"}}).afterClosed().subscribe(e=>{this.getAllCardCount()})}))}createKr(e){return Object(d.c)(this,void 0,void 0,(function*(){const{KrCreationComponent:t}=yield Promise.all([i.e(8),i.e(115),i.e(906)]).then(i.bind(null,"bIF9"));this.dialog.open(t,{width:"88%",height:"100%",position:{right:"0px"},autoFocus:!1,maxWidth:"90vw",data:{"creation-type":"withObj",data:e,name:"Key Result"}}).afterClosed().subscribe(e=>{this.getAllCardCount()})}))}createKPI(e,t){return Object(d.c)(this,void 0,void 0,(function*(){const{KrCreationComponent:a}=yield Promise.all([i.e(8),i.e(115),i.e(906)]).then(i.bind(null,"bIF9"));this.dialog.open(a,{width:"88%",height:"100%",position:{right:"0px"},autoFocus:!1,maxWidth:"90vw",data:{"creation-type":"withObj",data:t,cardClickedData:e,name:"KPI"}}).afterClosed().subscribe(e=>{this.getAllCardCount()})}))}createOKR(e,t){return Object(d.c)(this,void 0,void 0,(function*(){const{OkrCreationComponent:a}=yield Promise.all([i.e(8),i.e(117),i.e(909)]).then(i.bind(null,"cYXB"));this.dialog.open(a,{width:"88%",height:"100%",position:{right:"0px"},autoFocus:!1,maxWidth:"90vw",data:{data:e,json:t}}).afterClosed().subscribe(e=>{this.getAllCardCount()})}))}initReport(){return Object(d.c)(this,void 0,void 0,(function*(){this._onAppApiCalled.next(),this.OKRItemDataCurrentIndex=0,this.udrfService.udrfBodyData=[],this.isCardClicked=!1,this.cardClicked="",this.udrfService.udrfUiData.resolveColumnConfig(),this.defaultFilters=yield this.getDefaultFilters(),this.isFromCardClick=!1,this.skip=0,this.limit=this.defaultDataRetrievalCount,yield this.getOKRList(!1)}))}getOKRList(e){var t,i,a,r,n,o;return Object(d.c)(this,void 0,void 0,(function*(){let s,l,c,p,h=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),u=this.getDateLogic(h);s=u.durationStartDate,l=u.durationEndDate,c=u.endDateStartDate,p=u.endDateEndDate;let m=this.getFilters(h);m.push({filterName:"Org Code",valueId:[this.orgCode]});let f={skip:this.skip,limit:this.limit,filter:m,mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,cardData:null,startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,durationStartDate:s,durationEndDate:l,endDateStartDate:c,endDateEndDate:p};this.downloadFilterConfig={skip:0,limit:999,filter:m,mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,cardData:null,startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,durationStartDate:s,durationEndDate:l,endDateStartDate:c,endDateEndDate:p},f.cardData=this.okrCardClickedData,this.selectedCard=this.udrfService.udrfData.udrfSummaryCardCodes;for(let e=0;e<this.defaultFilters.length;e++)xi.contains(xi.pluck(xi.where(f.filter,{filterName:this.defaultFilters[e].filterName}),"filterName"),this.defaultFilters[e].filterName)||f.filter.push({filterName:this.defaultFilters[e].filterName,valueId:this.defaultFilters[e].valueId});console.log(this.okrCardClickedData),"KPI"==(null===(t=this.okrCardClickedData)||void 0===t?void 0:t.dataType)||"BAU"==(null===(i=this.okrCardClickedData)||void 0===i?void 0:i.dataType)?(this.udrfService.udrfUiData.itemHasCollabarationButton=!1,this.udrfService.udrfUiData.itemHasIntegrationButton=!1):"IKR"==(null===(a=this.okrCardClickedData)||void 0===a?void 0:a.dataType)||"IKR Actual Score"==(null===(r=this.okrCardClickedData)||void 0===r?void 0:r.dataType)||"IKR Total Score"==(null===(n=this.okrCardClickedData)||void 0===n?void 0:n.dataType)||"IKR Planned Score"==(null===(o=this.okrCardClickedData)||void 0===o?void 0:o.dataType)?(this.udrfService.udrfUiData.itemHasCollabarationButton=!1,this.udrfService.udrfUiData.itemHasIntegrationButton=!0):(this.udrfService.udrfUiData.itemHasCollabarationButton=!0,this.udrfService.udrfUiData.itemHasIntegrationButton=!1),console.log(this.fyYear),console.log("Filters",m);let g=xi.where(f.filter,{filterName:"Financial Year"});if(g&&g.length>0){let e=null==g[0].filterYear||this.fyYear==g[0].filterYear;if(this.fyYear=null==g[0].filterYear||this.fyYear==g[0].filterYear?this.fyYear:g[0].filterYear,console.log(this.fyYear),!e||!this.isFromOrgChart&&!this.isFromCardClick&&xi.contains(xi.pluck(g,"filterName"),"Financial Year")){this.isFromCardClick=!1;let e=g;this.evaluatorResponse.emit({filterValue:null==e[0].filterYear?this.fyYear:e[0].filterYear,isFromUdrfReport:!0,orgCode:this.orgCode,orgName:this.orgName})}}this.isFromOrgChart=!1,this._okrService.getOKRList(f).pipe(Object(O.a)(this._onDestroy)).pipe(Object(O.a)(this._onAppApiCalled)).subscribe(t=>Object(d.c)(this,void 0,void 0,(function*(){console.log(t,"Whole UDRF OKR Data"),"N"==t.err&&t.data&&t.data.length>0?this.isCardClicked?(xi.each(t.data,e=>{e.expanded=this.udrfService.udrfUiData.collapseAll}),this.udrfService.udrfBodyData.push(...t.data),this.formatOkrList(this.udrfService.udrfBodyData,!0)):(xi.each(t.data,e=>{e.expanded=this.udrfService.udrfUiData.collapseAll}),this.udrfService.udrfBodyData.push(...t.data),this.formatOkrList(this.udrfService.udrfBodyData,!0),e||this.getAllCardCount()):(e||this.getAllCardCount(),this.udrfService.udrfBodyData=this.udrfService.udrfBodyData.concat([]),this.udrfService.udrfData.noItemDataFound=!0),this.udrfService.udrfData.isItemDataLoading=!1})),e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}))}formatOkrList(e,t){var i;for(let a of e)a.loadChildTask=!1,a.showTask=t,a.parent=0!=t,(null===(i=a.children)||void 0===i?void 0:i.length)>0&&this.formatOkrList(a.children,!1)}getBodyData(e,t,i,a,r){if("MileStone"!=t.type&&""==r){for(let n of e)if(n.parent_id==a)return n}else for(let n of e)if(n.parent_id==a&&n.children.length>0)for(let e of n.children)if(e._id==r||e.mapped_under_id==r)return n}getInlineEditData(e,t){var i;for(let a of e){if(a._id==t){this.finalData=a;break}(null===(i=a.children)||void 0===i?void 0:i.length)>0&&this.getInlineEditData(a.children,t)}}getItemDataOnIndex(e,t){var i;let a,r=this.getBodyData(this.udrfService.udrfBodyData,e,e._id,e.parent_id,t);return r._id==e._id?a=r:(null===(i=r.children)||void 0===i?void 0:i.length)>0?(this.getInlineEditData(r.children,e._id),a=this.finalData):this.utilityService.showMessage("Not Valid","Dismiss"),a}getObjCount(){return Object(d.c)(this,void 0,void 0,(function*(){let e,t,i,a,r=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),n=this.getDateLogic(r);e=n.durationStartDate,t=n.durationEndDate,i=n.endDateStartDate,a=n.endDateEndDate;let o=this.getFilters(r);o.push({filterName:"Org Code",valueId:[this.orgCode]}),this._okrService.getOKRCard({skip:this.skip,limit:this.limit,filter:o,cardData:{dataType:"Objective",dataTypeValue:"0",isActive:!1,isVisible:!0,dataTypeCode:"O",c_type:"objective",apiCardType:"Objective",apiCardFilter:[]},startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,durationStartDate:e,durationEndDate:t,endDateStartDate:i,endDateEndDate:a}).pipe(Object(O.a)(this._onDestroy)).pipe(Object(O.a)(this._onAppApiCalled)).subscribe(e=>Object(d.c)(this,void 0,void 0,(function*(){if(this.udrfService.udrfData.udrfSummaryCardCodes.length>0&&this.resolveVisibleDataTypeArray(),"N"==e.err&&e.data&&e.data.length>0){for(let t=0;t<this.dataTypeArray.length;t++)"Objective"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].l1);this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}else{for(let e=0;e<this.dataTypeArray.length;e++)"Objective"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0");this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}})),e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}))}getKRCount(){return Object(d.c)(this,void 0,void 0,(function*(){let e,t,i,a,r=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),n=this.getDateLogic(r);e=n.durationStartDate,t=n.durationEndDate,i=n.endDateStartDate,a=n.endDateEndDate;let o=this.getFilters(r);o.push({filterName:"Org Code",valueId:[this.orgCode]}),this._okrService.getOKRCard({skip:this.skip,limit:this.limit,filter:o,cardData:{dataType:"KeyResult",dataTypeValue:"0",isActive:!1,isVisible:!0,dataTypeCode:"KR",c_type:"keyResult",apiCardType:"KeyResult",apiCardFilter:[{filterName:"Result Type",valueId:[{kr:1,init:0}]}]},startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,durationStartDate:e,durationEndDate:t,endDateStartDate:i,endDateEndDate:a}).pipe(Object(O.a)(this._onDestroy)).pipe(Object(O.a)(this._onAppApiCalled)).subscribe(e=>Object(d.c)(this,void 0,void 0,(function*(){if(this.udrfService.udrfData.udrfSummaryCardCodes.length>0&&this.resolveVisibleDataTypeArray(),"N"==e.err&&e.data&&e.data.length>0){for(let t=0;t<this.dataTypeArray.length;t++)"KeyResult"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].l2);this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}else{for(let e=0;e<this.dataTypeArray.length;e++)"KeyResult"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0");this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}})),e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}))}getInitiativeCount(){return Object(d.c)(this,void 0,void 0,(function*(){let e,t,i,a,r=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),n=this.getDateLogic(r);e=n.durationStartDate,t=n.durationEndDate,i=n.endDateStartDate,a=n.endDateEndDate;let o=this.getFilters(r);o.push({filterName:"Org Code",valueId:[this.orgCode]}),this._okrService.getOKRCard({skip:this.skip,limit:this.limit,filter:o,cardData:{dataType:"Initiative",dataTypeValue:"0",isActive:!1,isVisible:!0,dataTypeCode:"INI",c_type:"initiative",apiCardType:"Initiative",apiCardFilter:[{filterName:"Result Type",valueId:[{kr:0,init:1}]}]},startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,durationStartDate:e,durationEndDate:t,endDateStartDate:i,endDateEndDate:a}).pipe(Object(O.a)(this._onDestroy)).pipe(Object(O.a)(this._onAppApiCalled)).subscribe(e=>Object(d.c)(this,void 0,void 0,(function*(){if(this.udrfService.udrfData.udrfSummaryCardCodes.length>0&&this.resolveVisibleDataTypeArray(),"N"==e.err&&e.data&&e.data.length>0){for(let t=0;t<this.dataTypeArray.length;t++)"Initiative"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].l2);this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}else{for(let e=0;e<this.dataTypeArray.length;e++)"Initiative"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0");this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}})),e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}))}getTaskCount(){return Object(d.c)(this,void 0,void 0,(function*(){let e,t,i,a,r=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),n=this.getDateLogic(r);e=n.durationStartDate,t=n.durationEndDate,i=n.endDateStartDate,a=n.endDateEndDate;let o=this.getFilters(r);o.push({filterName:"Org Code",valueId:[this.orgCode]}),this._okrService.getOKRCard({skip:this.skip,limit:this.limit,filter:o,startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,cardData:{dataType:"Milestones",dataTypeValue:"0",isActive:!1,isVisible:!0,cardType:"status",statusColor:"#009432",dataTypeCode:"T",apiCardType:"Task",apiCardFilter:[]},mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,durationStartDate:e,durationEndDate:t,endDateStartDate:i,endDateEndDate:a}).pipe(Object(O.a)(this._onDestroy)).pipe(Object(O.a)(this._onAppApiCalled)).subscribe(e=>Object(d.c)(this,void 0,void 0,(function*(){if(this.udrfService.udrfData.udrfSummaryCardCodes.length>0&&this.resolveVisibleDataTypeArray(),"N"==e.err&&e.data&&e.data.length>0){for(let t=0;t<this.dataTypeArray.length;t++)"Milestones"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].l2);this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}else{for(let e=0;e<this.dataTypeArray.length;e++)"Milestones"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0");this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}})),e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}))}getNSKRCount(){return Object(d.c)(this,void 0,void 0,(function*(){let e,t,i,a,r=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),n=this.getDateLogic(r);e=n.durationStartDate,t=n.durationEndDate,i=n.endDateStartDate,a=n.endDateEndDate;let o=this.getFilters(r);o.push({filterName:"Org Code",valueId:[this.orgCode]}),this._okrService.getOKRCard({skip:this.skip,limit:this.limit,filter:o,startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,cardData:{dataType:"Not started(KeyResult)",dataTypeValue:"0",isActive:!1,isVisible:!0,cardType:"status",statusColor:"#ACA9A6",dataTypeCode:"NSKR",apiCardType:"KeyResult",apiCardFilter:[{filterName:"Result Type",valueId:[{kr:1,init:0}]},{filterName:"KRStatus",valueId:["60530d8a03b72607cd10e302"]}]},mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,durationStartDate:e,durationEndDate:t,endDateStartDate:i,endDateEndDate:a}).pipe(Object(O.a)(this._onDestroy)).pipe(Object(O.a)(this._onAppApiCalled)).subscribe(e=>Object(d.c)(this,void 0,void 0,(function*(){if(this.udrfService.udrfData.udrfSummaryCardCodes.length>0&&this.resolveVisibleDataTypeArray(),"N"==e.err&&e.data&&e.data.length>0){for(let t=0;t<this.dataTypeArray.length;t++)"Not started(KeyResult)"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].l2);this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}else{for(let e=0;e<this.dataTypeArray.length;e++)"Not started(KeyResult)"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0");this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}})),e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}))}getCOKRCount(){return Object(d.c)(this,void 0,void 0,(function*(){let e,t,i,a,r=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),n=this.getDateLogic(r);e=n.durationStartDate,t=n.durationEndDate,i=n.endDateStartDate,a=n.endDateEndDate;let o=this.getFilters(r);o.push({filterName:"Org Code",valueId:[this.orgCode]}),this._okrService.getOKRCard({skip:this.skip,limit:this.limit,filter:o,startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,cardData:{dataType:"Completed(KeyResult)",dataTypeValue:"0",isActive:!1,isVisible:!0,cardType:"status",statusColor:"#0077b8",dataTypeCode:"COKR",apiCardType:"KeyResult",apiCardFilter:[{filterName:"Result Type",valueId:[{kr:1,init:0}]},{filterName:"KRStatus",valueId:["60530e1a03b72607cd10e306"]}]},mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,durationStartDate:e,durationEndDate:t,endDateStartDate:i,endDateEndDate:a}).pipe(Object(O.a)(this._onDestroy)).pipe(Object(O.a)(this._onAppApiCalled)).subscribe(e=>Object(d.c)(this,void 0,void 0,(function*(){if(this.udrfService.udrfData.udrfSummaryCardCodes.length>0&&this.resolveVisibleDataTypeArray(),"N"==e.err&&e.data&&e.data.length>0){for(let t=0;t<this.dataTypeArray.length;t++)"Completed(KeyResult)"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].l2);this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}else{for(let e=0;e<this.dataTypeArray.length;e++)"Completed(KeyResult)"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0");this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}})),e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}))}getOTKRCount(){return Object(d.c)(this,void 0,void 0,(function*(){let e,t,i,a,r=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),n=this.getDateLogic(r);e=n.durationStartDate,t=n.durationEndDate,i=n.endDateStartDate,a=n.endDateEndDate;let o=this.getFilters(r);o.push({filterName:"Org Code",valueId:[this.orgCode]}),this._okrService.getOKRCard({skip:this.skip,limit:this.limit,filter:o,startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,cardData:{dataType:"On track(KeyResult)",dataTypeValue:"0",isActive:!1,isVisible:!1,cardType:"status",statusColor:"#7dd259",dataTypeCode:"OTKR",apiCardType:"KeyResult",apiCardFilter:[{filterName:"Result Type",valueId:[{kr:1,init:0}]},{filterName:"KRStatus",valueId:["60530db703b72607cd10e303"]}]},mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,durationStartDate:e,durationEndDate:t,endDateStartDate:i,endDateEndDate:a}).pipe(Object(O.a)(this._onDestroy)).pipe(Object(O.a)(this._onAppApiCalled)).subscribe(e=>Object(d.c)(this,void 0,void 0,(function*(){if(this.udrfService.udrfData.udrfSummaryCardCodes.length>0&&this.resolveVisibleDataTypeArray(),"N"==e.err&&e.data&&e.data.length>0){for(let t=0;t<this.dataTypeArray.length;t++)"On track(KeyResult)"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].l2);this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}else{for(let e=0;e<this.dataTypeArray.length;e++)"On track(KeyResult)"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0");this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}})),e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}))}getARKRCount(){return Object(d.c)(this,void 0,void 0,(function*(){let e,t,i,a,r=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),n=this.getDateLogic(r);e=n.durationStartDate,t=n.durationEndDate,i=n.endDateStartDate,a=n.endDateEndDate;let o=this.getFilters(r);o.push({filterName:"Org Code",valueId:[this.orgCode]}),this._okrService.getOKRCard({skip:this.skip,limit:this.limit,filter:o,startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,cardData:{dataType:"At risk(KeyResult)",dataTypeValue:"0",isActive:!1,isVisible:!1,cardType:"status",statusColor:"#fe0000",dataTypeCode:"ARKR",apiCardType:"KeyResult",apiCardFilter:[{filterName:"Result Type",valueId:[{kr:1,init:0}]},{filterName:"KRStatus",valueId:["60530df703b72607cd10e305"]}]},mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,durationStartDate:e,durationEndDate:t,endDateStartDate:i,endDateEndDate:a}).pipe(Object(O.a)(this._onDestroy)).pipe(Object(O.a)(this._onAppApiCalled)).subscribe(e=>Object(d.c)(this,void 0,void 0,(function*(){if(this.udrfService.udrfData.udrfSummaryCardCodes.length>0&&this.resolveVisibleDataTypeArray(),"N"==e.err&&e.data&&e.data.length>0){for(let t=0;t<this.dataTypeArray.length;t++)"At risk(KeyResult)"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].l2);this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}else{for(let e=0;e<this.dataTypeArray.length;e++)"At risk(KeyResult)"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0");this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}})),e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}))}getDKRCount(){return Object(d.c)(this,void 0,void 0,(function*(){let e,t,i,a,r=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),n=this.getDateLogic(r);e=n.durationStartDate,t=n.durationEndDate,i=n.endDateStartDate,a=n.endDateEndDate;let o=this.getFilters(r);o.push({filterName:"Org Code",valueId:[this.orgCode]}),this._okrService.getOKRCard({skip:this.skip,limit:this.limit,filter:o,startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,cardData:{dataType:"Delayed(KeyResult)",dataTypeValue:"0",isActive:!1,isVisible:!1,cardType:"status",statusColor:"#febd00",dataTypeCode:"DKR",apiCardType:"KeyResult",apiCardFilter:[{filterName:"Result Type",valueId:[{kr:1,init:0}]},{filterName:"KRStatus",valueId:["60530e4603b72607cd10e307"]}]},mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,durationStartDate:e,durationEndDate:t,endDateStartDate:i,endDateEndDate:a}).pipe(Object(O.a)(this._onDestroy)).pipe(Object(O.a)(this._onAppApiCalled)).subscribe(e=>Object(d.c)(this,void 0,void 0,(function*(){if(this.udrfService.udrfData.udrfSummaryCardCodes.length>0&&this.resolveVisibleDataTypeArray(),"N"==e.err&&e.data&&e.data.length>0){for(let t=0;t<this.dataTypeArray.length;t++)"Delayed(KeyResult)"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].l2);this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}else{for(let e=0;e<this.dataTypeArray.length;e++)"Delayed(KeyResult)"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0");this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}})),e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}))}getOHKRCount(){return Object(d.c)(this,void 0,void 0,(function*(){let e,t,i,a,r=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),n=this.getDateLogic(r);e=n.durationStartDate,t=n.durationEndDate,i=n.endDateStartDate,a=n.endDateEndDate;let o=this.getFilters(r);o.push({filterName:"Org Code",valueId:[this.orgCode]}),this._okrService.getOKRCard({skip:this.skip,limit:this.limit,filter:o,startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,cardData:{dataType:"On Hold(KeyResult)",dataTypeValue:"0",c_type:"KeyResult",isActive:!1,isVisible:!0,cardType:"status",statusColor:"#9370DB",dataTypeCode:"OHKR",apiCardType:"KeyResult",apiCardFilter:[{filterName:"Result Type",valueId:[{kr:1,init:0}]},{filterName:"KRStatus",valueId:["60a744a7cba307a5aff6a43c"]}]},mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,durationStartDate:e,durationEndDate:t,endDateStartDate:i,endDateEndDate:a}).pipe(Object(O.a)(this._onDestroy)).pipe(Object(O.a)(this._onAppApiCalled)).subscribe(e=>Object(d.c)(this,void 0,void 0,(function*(){if(this.udrfService.udrfData.udrfSummaryCardCodes.length>0&&this.resolveVisibleDataTypeArray(),"N"==e.err&&e.data&&e.data.length>0){for(let t=0;t<this.dataTypeArray.length;t++)"On Hold(KeyResult)"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].l2);this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}else{for(let e=0;e<this.dataTypeArray.length;e++)"On Hold(KeyResult)"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0");this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}})),e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}))}getOTINICount(){return Object(d.c)(this,void 0,void 0,(function*(){let e,t,i,a,r=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),n=this.getDateLogic(r);e=n.durationStartDate,t=n.durationEndDate,i=n.endDateStartDate,a=n.endDateEndDate;let o=this.getFilters(r);o.push({filterName:"Org Code",valueId:[this.orgCode]}),this._okrService.getOKRCard({skip:this.skip,limit:this.limit,filter:o,startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,cardData:{dataType:"On track(Initiative)",dataTypeValue:"0",isActive:!1,isVisible:!1,cardType:"status",statusColor:"#7dd259",dataTypeCode:"OTINI",apiCardType:"Initiative",apiCardFilter:[{filterName:"Result Type",valueId:[{kr:0,init:1}]},{filterName:"InitStatus",valueId:["60530db703b72607cd10e303"]}]},mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,durationStartDate:e,durationEndDate:t,endDateStartDate:i,endDateEndDate:a}).pipe(Object(O.a)(this._onDestroy)).pipe(Object(O.a)(this._onAppApiCalled)).subscribe(e=>Object(d.c)(this,void 0,void 0,(function*(){if(this.udrfService.udrfData.udrfSummaryCardCodes.length>0&&this.resolveVisibleDataTypeArray(),"N"==e.err&&e.data&&e.data.length>0){for(let t=0;t<this.dataTypeArray.length;t++)"On track(Initiative)"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].l2);this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}else{for(let e=0;e<this.dataTypeArray.length;e++)"On track(Initiative)"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0");this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}})),e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}))}getCOINICount(){return Object(d.c)(this,void 0,void 0,(function*(){let e,t,i,a,r=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),n=this.getDateLogic(r);e=n.durationStartDate,t=n.durationEndDate,i=n.endDateStartDate,a=n.endDateEndDate;let o=this.getFilters(r);o.push({filterName:"Org Code",valueId:[this.orgCode]}),this._okrService.getOKRCard({skip:this.skip,limit:this.limit,filter:o,startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,cardData:{dataType:"Completed(Initiative)",dataTypeValue:"0",isActive:!1,isVisible:!1,cardType:"status",statusColor:"#0077b8",dataTypeCode:"COINI",apiCardType:"Initiative",apiCardFilter:[{filterName:"Result Type",valueId:[{kr:0,init:1}]},{filterName:"InitStatus",valueId:["60530e1a03b72607cd10e306"]}]},mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,durationStartDate:e,durationEndDate:t,endDateStartDate:i,endDateEndDate:a}).pipe(Object(O.a)(this._onDestroy)).pipe(Object(O.a)(this._onAppApiCalled)).subscribe(e=>Object(d.c)(this,void 0,void 0,(function*(){if(this.udrfService.udrfData.udrfSummaryCardCodes.length>0&&this.resolveVisibleDataTypeArray(),"N"==e.err&&e.data&&e.data.length>0){for(let t=0;t<this.dataTypeArray.length;t++)"Completed(Initiative)"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].l2);this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}else{for(let e=0;e<this.dataTypeArray.length;e++)"Completed(Initiative)"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0");this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}})),e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}))}getNSINICount(){return Object(d.c)(this,void 0,void 0,(function*(){let e,t,i,a,r=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),n=this.getDateLogic(r);e=n.durationStartDate,t=n.durationEndDate,i=n.endDateStartDate,a=n.endDateEndDate;let o=this.getFilters(r);o.push({filterName:"Org Code",valueId:[this.orgCode]}),this._okrService.getOKRCard({skip:this.skip,limit:this.limit,filter:o,startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,cardData:{dataType:"Not started(Initiative)",dataTypeValue:"0",isActive:!1,isVisible:!1,cardType:"status",statusColor:"#ACA9A6",dataTypeCode:"NSINI",apiCardType:"Initiative",apiCardFilter:[{filterName:"Result Type",valueId:[{kr:0,init:1}]},{filterName:"InitStatus",valueId:["60530d8a03b72607cd10e302"]}]},mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,durationStartDate:e,durationEndDate:t,endDateStartDate:i,endDateEndDate:a}).pipe(Object(O.a)(this._onDestroy)).pipe(Object(O.a)(this._onAppApiCalled)).subscribe(e=>Object(d.c)(this,void 0,void 0,(function*(){if(this.udrfService.udrfData.udrfSummaryCardCodes.length>0&&this.resolveVisibleDataTypeArray(),"N"==e.err&&e.data&&e.data.length>0){for(let t=0;t<this.dataTypeArray.length;t++)"Not started(Initiative)"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].l2);this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}else{for(let e=0;e<this.dataTypeArray.length;e++)"Not started(Initiative)"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0");this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}})),e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}))}getARINICount(){return Object(d.c)(this,void 0,void 0,(function*(){let e,t,i,a,r=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),n=this.getDateLogic(r);e=n.durationStartDate,t=n.durationEndDate,i=n.endDateStartDate,a=n.endDateEndDate;let o=this.getFilters(r);o.push({filterName:"Org Code",valueId:[this.orgCode]}),this._okrService.getOKRCard({skip:this.skip,limit:this.limit,filter:o,startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,cardData:{dataType:"At risk(Initiative)",dataTypeValue:"0",isActive:!1,isVisible:!1,cardType:"status",statusColor:"#fe0000",dataTypeCode:"ARINI",apiCardType:"Initiative",apiCardFilter:[{filterName:"Result Type",valueId:[{kr:0,init:1}]},{filterName:"InitStatus",valueId:["60530df703b72607cd10e305"]}]},mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,durationStartDate:e,durationEndDate:t,endDateStartDate:i,endDateEndDate:a}).pipe(Object(O.a)(this._onDestroy)).pipe(Object(O.a)(this._onAppApiCalled)).subscribe(e=>Object(d.c)(this,void 0,void 0,(function*(){if(this.udrfService.udrfData.udrfSummaryCardCodes.length>0&&this.resolveVisibleDataTypeArray(),"N"==e.err&&e.data&&e.data.length>0){for(let t=0;t<this.dataTypeArray.length;t++)"At risk(Initiative)"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].l2);this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}else{for(let e=0;e<this.dataTypeArray.length;e++)"At risk(Initiative)"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0");this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}})),e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}))}getDINICount(){return Object(d.c)(this,void 0,void 0,(function*(){let e,t,i,a,r=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),n=this.getDateLogic(r);e=n.durationStartDate,t=n.durationEndDate,i=n.endDateStartDate,a=n.endDateEndDate;let o=this.getFilters(r);o.push({filterName:"Org Code",valueId:[this.orgCode]}),this._okrService.getOKRCard({skip:this.skip,limit:this.limit,filter:o,startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,cardData:{dataType:"Delayed(Initiative)",dataTypeValue:"0",isActive:!1,isVisible:!1,cardType:"status",statusColor:"#febd00",dataTypeCode:"DINI",apiCardType:"Initiative",apiCardFilter:[{filterName:"Result Type",valueId:[{kr:0,init:1}]},{filterName:"InitStatus",valueId:["60530e4603b72607cd10e307"]}]},mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,durationStartDate:e,durationEndDate:t,endDateStartDate:i,endDateEndDate:a}).pipe(Object(O.a)(this._onDestroy)).pipe(Object(O.a)(this._onAppApiCalled)).subscribe(e=>Object(d.c)(this,void 0,void 0,(function*(){if(this.udrfService.udrfData.udrfSummaryCardCodes.length>0&&this.resolveVisibleDataTypeArray(),"N"==e.err&&e.data&&e.data.length>0){for(let t=0;t<this.dataTypeArray.length;t++)"Delayed(Initiative)"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].l2);this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}else{for(let e=0;e<this.dataTypeArray.length;e++)"Delayed(Initiative)"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0");this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}})),e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}))}getOHINICount(){return Object(d.c)(this,void 0,void 0,(function*(){let e,t,i,a,r=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),n=this.getDateLogic(r);e=n.durationStartDate,t=n.durationEndDate,i=n.endDateStartDate,a=n.endDateEndDate;let o=this.getFilters(r);o.push({filterName:"Org Code",valueId:[this.orgCode]}),this._okrService.getOKRCard({skip:this.skip,limit:this.limit,filter:o,startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,cardData:{dataType:"On Hold(Initiative)",dataTypeValue:"0",c_type:"Initiative",isActive:!1,isVisible:!1,cardType:"status",statusColor:"#9370DB",dataTypeCode:"OHINI",apiCardType:"Initiative",apiCardFilter:[{filterName:"Result Type",valueId:[{kr:0,init:1}]},{filterName:"InitStatus",valueId:["60a744a7cba307a5aff6a43c"]}]},mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,durationStartDate:e,durationEndDate:t,endDateStartDate:i,endDateEndDate:a}).pipe(Object(O.a)(this._onDestroy)).pipe(Object(O.a)(this._onAppApiCalled)).subscribe(e=>Object(d.c)(this,void 0,void 0,(function*(){if(this.udrfService.udrfData.udrfSummaryCardCodes.length>0&&this.resolveVisibleDataTypeArray(),"N"==e.err&&e.data&&e.data.length>0){for(let t=0;t<this.dataTypeArray.length;t++)"On Hold(Initiative)"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].l2);this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}else{for(let e=0;e<this.dataTypeArray.length;e++)"On Hold(Initiative)"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0");this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}})),e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}))}getNSTCount(){return Object(d.c)(this,void 0,void 0,(function*(){let e,t,i,a,r=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),n=this.getDateLogic(r);e=n.durationStartDate,t=n.durationEndDate,i=n.endDateStartDate,a=n.endDateEndDate;let o=this.getFilters(r);o.push({filterName:"Org Code",valueId:[this.orgCode]}),this._okrService.getOKRCard({skip:this.skip,limit:this.limit,filter:o,startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,cardData:{dataType:"Not Started(Milestones)",dataTypeValue:"0",isActive:!1,isVisible:!1,cardType:"status",statusColor:"#ACA9A6",dataTypeCode:"OT",apiCardType:"Task",apiCardFilter:[{filterName:"TaskStatus",valueId:["6615adece05ca018a3b2819b"]}]},mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,durationStartDate:e,durationEndDate:t,endDateStartDate:i,endDateEndDate:a}).pipe(Object(O.a)(this._onDestroy)).pipe(Object(O.a)(this._onAppApiCalled)).subscribe(e=>Object(d.c)(this,void 0,void 0,(function*(){if(this.udrfService.udrfData.udrfSummaryCardCodes.length>0&&this.resolveVisibleDataTypeArray(),"N"==e.err&&e.data&&e.data.length>0){for(let t=0;t<this.dataTypeArray.length;t++)"Not Started(Milestones)"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].l2);this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}else{for(let e=0;e<this.dataTypeArray.length;e++)"Not Started(Milestones)"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0");this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}})),e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}))}getIPTCount(){return Object(d.c)(this,void 0,void 0,(function*(){let e,t,i,a,r=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),n=this.getDateLogic(r);e=n.durationStartDate,t=n.durationEndDate,i=n.endDateStartDate,a=n.endDateEndDate;let o=this.getFilters(r);o.push({filterName:"Org Code",valueId:[this.orgCode]}),this._okrService.getOKRCard({skip:this.skip,limit:this.limit,filter:o,startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,cardData:{dataType:"On Track(Milestones)",dataTypeValue:"0",isActive:!1,isVisible:!1,cardType:"status",statusColor:"#7dd259",dataTypeCode:"IPT",apiCardType:"Task",apiCardFilter:[{filterName:"TaskStatus",valueId:["608a4e3090e2cf3ac4ee3ea6"]}]},mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,durationStartDate:e,durationEndDate:t,endDateStartDate:i,endDateEndDate:a}).pipe(Object(O.a)(this._onDestroy)).pipe(Object(O.a)(this._onAppApiCalled)).subscribe(e=>Object(d.c)(this,void 0,void 0,(function*(){if(this.udrfService.udrfData.udrfSummaryCardCodes.length>0&&this.resolveVisibleDataTypeArray(),"N"==e.err&&e.data&&e.data.length>0){for(let t=0;t<this.dataTypeArray.length;t++)"On Track(Milestones)"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].l2);this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}else{for(let e=0;e<this.dataTypeArray.length;e++)"On Track(Milestones)"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0");this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}})),e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}))}getMTCount(){return Object(d.c)(this,void 0,void 0,(function*(){let e,t,i,a,r=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),n=this.getDateLogic(r);e=n.durationStartDate,t=n.durationEndDate,i=n.endDateStartDate,a=n.endDateEndDate;let o=this.getFilters(r);o.push({filterName:"Org Code",valueId:[this.orgCode]}),this._okrService.getOKRCard({skip:this.skip,limit:this.limit,filter:o,startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,cardData:{dataType:"At Risk(Milestones)",dataTypeValue:"0",isActive:!1,isVisible:!1,cardType:"status",statusColor:"#fe0000",dataTypeCode:"MT",apiCardType:"Task",apiCardFilter:[{filterName:"TaskStatus",valueId:["608a4e3090e2cf3ac4ee3ea7"]}]},mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,durationStartDate:e,durationEndDate:t,endDateStartDate:i,endDateEndDate:a}).pipe(Object(O.a)(this._onDestroy)).pipe(Object(O.a)(this._onAppApiCalled)).subscribe(e=>Object(d.c)(this,void 0,void 0,(function*(){if(this.udrfService.udrfData.udrfSummaryCardCodes.length>0&&this.resolveVisibleDataTypeArray(),"N"==e.err&&e.data&&e.data.length>0){for(let t=0;t<this.dataTypeArray.length;t++)"At Risk(Milestones)"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].l2);this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}else{for(let e=0;e<this.dataTypeArray.length;e++)"At Risk(Milestones)"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0");this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}})),e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}))}getPTCount(){return Object(d.c)(this,void 0,void 0,(function*(){let e,t,i,a,r=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),n=this.getDateLogic(r);e=n.durationStartDate,t=n.durationEndDate,i=n.endDateStartDate,a=n.endDateEndDate;let o=this.getFilters(r);o.push({filterName:"Org Code",valueId:[this.orgCode]}),this._okrService.getOKRCard({skip:this.skip,limit:this.limit,filter:o,startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,cardData:{dataType:"Delayed(Milestones)",dataTypeValue:"0",isActive:!1,isVisible:!1,cardType:"status",statusColor:"#febd00",dataTypeCode:"PT",apiCardType:"Task",apiCardFilter:[{filterName:"TaskStatus",valueId:["608a4e3090e2cf3ac4ee3ea9"]}]},mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,durationStartDate:e,durationEndDate:t,endDateStartDate:i,endDateEndDate:a}).pipe(Object(O.a)(this._onDestroy)).pipe(Object(O.a)(this._onAppApiCalled)).subscribe(e=>Object(d.c)(this,void 0,void 0,(function*(){if(this.udrfService.udrfData.udrfSummaryCardCodes.length>0&&this.resolveVisibleDataTypeArray(),"N"==e.err&&e.data&&e.data.length>0){for(let t=0;t<this.dataTypeArray.length;t++)"Delayed(Milestones)"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].l2);this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}else{for(let e=0;e<this.dataTypeArray.length;e++)"Delayed(Milestones)"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0");this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}})),e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}))}getCTCount(){return Object(d.c)(this,void 0,void 0,(function*(){let e,t,i,a,r=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),n=this.getDateLogic(r);e=n.durationStartDate,t=n.durationEndDate,i=n.endDateStartDate,a=n.endDateEndDate;let o=this.getFilters(r);o.push({filterName:"Org Code",valueId:[this.orgCode]}),this._okrService.getOKRCard({skip:this.skip,limit:this.limit,filter:o,startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,cardData:{dataType:"Completed(Milestones)",dataTypeValue:"0",isActive:!1,isVisible:!1,cardType:"status",statusColor:"#0077b8",dataTypeCode:"CT",apiCardType:"Task",apiCardFilter:[{filterName:"TaskStatus",valueId:["608a4e3090e2cf3ac4ee3ea8"]}]},mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,durationStartDate:e,durationEndDate:t,endDateStartDate:i,endDateEndDate:a}).pipe(Object(O.a)(this._onDestroy)).pipe(Object(O.a)(this._onAppApiCalled)).subscribe(e=>Object(d.c)(this,void 0,void 0,(function*(){if(this.udrfService.udrfData.udrfSummaryCardCodes.length>0&&this.resolveVisibleDataTypeArray(),"N"==e.err&&e.data&&e.data.length>0){for(let t=0;t<this.dataTypeArray.length;t++)"Completed(Milestones)"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].l2);this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}else{for(let e=0;e<this.dataTypeArray.length;e++)"Completed(Milestones)"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0");this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}})),e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}))}getOHTCount(){return Object(d.c)(this,void 0,void 0,(function*(){let e,t,i,a,r=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),n=this.getDateLogic(r);e=n.durationStartDate,t=n.durationEndDate,i=n.endDateStartDate,a=n.endDateEndDate;let o=this.getFilters(r);o.push({filterName:"Org Code",valueId:[this.orgCode]}),this._okrService.getOKRCard({skip:this.skip,limit:this.limit,filter:o,startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,cardData:{dataType:"On Hold(Milestones)",dataTypeValue:"0",isActive:!1,isVisible:!1,cardType:"status",statusColor:"#9370DB",dataTypeCode:"OHT",apiCardType:"Task",c_type:"Task",apiCardFilter:[{filterName:"TaskStatus",valueId:["609e569cc6bdc84b70596bb3"]}]},mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,durationStartDate:e,durationEndDate:t,endDateStartDate:i,endDateEndDate:a}).pipe(Object(O.a)(this._onDestroy)).pipe(Object(O.a)(this._onAppApiCalled)).subscribe(e=>Object(d.c)(this,void 0,void 0,(function*(){if(this.udrfService.udrfData.udrfSummaryCardCodes.length>0&&this.resolveVisibleDataTypeArray(),"N"==e.err&&e.data&&e.data.length>0){for(let t=0;t<this.dataTypeArray.length;t++)"On Hold(Milestones)"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].l2);this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}else{for(let e=0;e<this.dataTypeArray.length;e++)"On Hold(Milestones)"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0");this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}})),e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}))}getPSCount(){return Object(d.c)(this,void 0,void 0,(function*(){let e,t,i,a,r=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),n=this.getDateLogic(r);e=n.durationStartDate,t=n.durationEndDate,i=n.endDateStartDate,a=n.endDateEndDate;let o=this.getFilters(r);o.push({filterName:"Org Code",valueId:[this.orgCode]}),this._okrService.getOKRCard({skip:this.skip,limit:this.limit,filter:o,cardData:{dataType:"Planned Score",dataTypeValue:"0",isActive:!1,isVisible:!1,dataTypeCode:"PS",c_type:"Objective",apiCardType:"Objective",apiCardFilter:[]},startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,durationStartDate:e,durationEndDate:t,endDateStartDate:i,endDateEndDate:a}).pipe(Object(O.a)(this._onDestroy)).pipe(Object(O.a)(this._onAppApiCalled)).subscribe(e=>Object(d.c)(this,void 0,void 0,(function*(){if(this.udrfService.udrfData.udrfSummaryCardCodes.length>0&&this.resolveVisibleDataTypeArray(),"N"==e.err&&e.data&&e.data.length>0){for(let t=0;t<this.dataTypeArray.length;t++)"Planned Score"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].plnd_score);this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}else{for(let e=0;e<this.dataTypeArray.length;e++)"Planned Score"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0");this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}})),e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}))}getIKRPSCount(){return Object(d.c)(this,void 0,void 0,(function*(){let e,t,i,a,r=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),n=this.getDateLogic(r);e=n.durationStartDate,t=n.durationEndDate,i=n.endDateStartDate,a=n.endDateEndDate;let o=this.getFilters(r);o.push({filterName:"Org Code",valueId:[this.orgCode]}),this._okrService.getOKRCard({skip:this.skip,limit:this.limit,filter:o,cardData:{dataType:"IKR Planned Score",dataTypeValue:"0",isActive:!1,isVisible:!1,dataTypeCode:"IKRPS",c_type:"Objective",apiCardType:"Objective",apiCardFilter:[]},startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,durationStartDate:e,durationEndDate:t,endDateStartDate:i,endDateEndDate:a}).pipe(Object(O.a)(this._onDestroy)).pipe(Object(O.a)(this._onAppApiCalled)).subscribe(e=>Object(d.c)(this,void 0,void 0,(function*(){if(this.udrfService.udrfData.udrfSummaryCardCodes.length>0&&this.resolveVisibleDataTypeArray(),"N"==e.err&&e.data&&e.data.length>0){for(let t=0;t<this.dataTypeArray.length;t++)"Planned Score"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].ikr_plnd_score);this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}else{for(let e=0;e<this.dataTypeArray.length;e++)"Planned Score"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0");this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}})),e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}))}getASCount(){return Object(d.c)(this,void 0,void 0,(function*(){let e,t,i,a,r=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),n=this.getDateLogic(r);e=n.durationStartDate,t=n.durationEndDate,i=n.endDateStartDate,a=n.endDateEndDate;let o=this.getFilters(r);o.push({filterName:"Org Code",valueId:[this.orgCode]}),this._okrService.getOKRCard({skip:this.skip,limit:this.limit,filter:o,cardData:{dataType:"Actual Score",dataTypeValue:"0",isActive:!1,isVisible:!1,dataTypeCode:"AS",c_type:"Objective",apiCardType:"Objective",apiCardFilter:[]},startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,durationStartDate:e,durationEndDate:t,endDateStartDate:i,endDateEndDate:a}).pipe(Object(O.a)(this._onDestroy)).pipe(Object(O.a)(this._onAppApiCalled)).subscribe(e=>Object(d.c)(this,void 0,void 0,(function*(){if(this.udrfService.udrfData.udrfSummaryCardCodes.length>0&&this.resolveVisibleDataTypeArray(),"N"==e.err&&e.data&&e.data.length>0){for(let t=0;t<this.dataTypeArray.length;t++)"Actual Score"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].act_score);this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}else{for(let e=0;e<this.dataTypeArray.length;e++)"Actual Score"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0");this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}})),e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}))}getIKRASCount(){return Object(d.c)(this,void 0,void 0,(function*(){let e,t,i,a,r=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),n=this.getDateLogic(r);e=n.durationStartDate,t=n.durationEndDate,i=n.endDateStartDate,a=n.endDateEndDate;let o=this.getFilters(r);o.push({filterName:"Org Code",valueId:[this.orgCode]}),this._okrService.getOKRCard({skip:this.skip,limit:this.limit,filter:o,cardData:{dataType:"Actual Score",dataTypeValue:"0",isActive:!1,isVisible:!1,dataTypeCode:"IKRAS",c_type:"Objective",apiCardType:"Objective",apiCardFilter:[]},startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,durationStartDate:e,durationEndDate:t,endDateStartDate:i,endDateEndDate:a}).pipe(Object(O.a)(this._onDestroy)).pipe(Object(O.a)(this._onAppApiCalled)).subscribe(e=>Object(d.c)(this,void 0,void 0,(function*(){if(this.udrfService.udrfData.udrfSummaryCardCodes.length>0&&this.resolveVisibleDataTypeArray(),"N"==e.err&&e.data&&e.data.length>0){for(let t=0;t<this.dataTypeArray.length;t++)"Actual Score"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].ikr_act_score);this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}else{for(let e=0;e<this.dataTypeArray.length;e++)"Actual Score"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0");this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}})),e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}))}getTSCount(){return Object(d.c)(this,void 0,void 0,(function*(){let e,t,i,a,r=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),n=this.getDateLogic(r);e=n.durationStartDate,t=n.durationEndDate,i=n.endDateStartDate,a=n.endDateEndDate;let o=this.getFilters(r);o.push({filterName:"Org Code",valueId:[this.orgCode]}),this._okrService.getOKRCard({skip:this.skip,limit:this.limit,filter:o,cardData:{dataType:"Total Score",dataTypeValue:"0",isActive:!1,isVisible:!1,dataTypeCode:"PS",c_type:"Objective",apiCardType:"Objective",apiCardFilter:[]},startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,durationStartDate:e,durationEndDate:t,endDateStartDate:i,endDateEndDate:a}).pipe(Object(O.a)(this._onDestroy)).pipe(Object(O.a)(this._onAppApiCalled)).subscribe(e=>Object(d.c)(this,void 0,void 0,(function*(){if(this.udrfService.udrfData.udrfSummaryCardCodes.length>0&&this.resolveVisibleDataTypeArray(),"N"==e.err&&e.data&&e.data.length>0){for(let t=0;t<this.dataTypeArray.length;t++)"Total Score"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].tot_score);this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}else{for(let e=0;e<this.dataTypeArray.length;e++)"Total Score"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0");this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}})),e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}))}getIKRTSCount(){return Object(d.c)(this,void 0,void 0,(function*(){let e,t,i,a,r=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),n=this.getDateLogic(r);e=n.durationStartDate,t=n.durationEndDate,i=n.endDateStartDate,a=n.endDateEndDate;let o=this.getFilters(r);o.push({filterName:"Org Code",valueId:[this.orgCode]}),this._okrService.getOKRCard({skip:this.skip,limit:this.limit,filter:o,cardData:{dataType:"Total Score",dataTypeValue:"0",isActive:!1,isVisible:!1,dataTypeCode:"IKRPS",c_type:"Objective",apiCardType:"Objective",apiCardFilter:[]},startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,durationStartDate:e,durationEndDate:t,endDateStartDate:i,endDateEndDate:a}).pipe(Object(O.a)(this._onDestroy)).pipe(Object(O.a)(this._onAppApiCalled)).subscribe(e=>Object(d.c)(this,void 0,void 0,(function*(){if(this.udrfService.udrfData.udrfSummaryCardCodes.length>0&&this.resolveVisibleDataTypeArray(),"N"==e.err&&e.data&&e.data.length>0){for(let t=0;t<this.dataTypeArray.length;t++)"Total Score"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].ikr_tot_score);this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}else{for(let e=0;e<this.dataTypeArray.length;e++)"Total Score"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0");this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}})),e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}))}getIKRCount(){return Object(d.c)(this,void 0,void 0,(function*(){let e,t,i,a,r=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),n=this.getDateLogic(r);e=n.durationStartDate,t=n.durationEndDate,i=n.endDateStartDate,a=n.endDateEndDate;let o=this.getFilters(r);o.push({filterName:"Org Code",valueId:[this.orgCode]}),this._okrService.getOKRCard({skip:this.skip,limit:this.limit,filter:o,cardData:{dataType:"IKR",dataTypeValue:"0",isActive:!1,isVisible:!1,dataTypeCode:"IKR",c_type:"IKR",apiCardType:"IKR",apiCardFilter:[]},startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,durationStartDate:e,durationEndDate:t,endDateStartDate:i,endDateEndDate:a}).pipe(Object(O.a)(this._onDestroy)).pipe(Object(O.a)(this._onAppApiCalled)).subscribe(e=>Object(d.c)(this,void 0,void 0,(function*(){if(this.udrfService.udrfData.udrfSummaryCardCodes.length>0&&this.resolveVisibleDataTypeArray(),"N"==e.err&&e.data&&e.data.length>0){for(let t=0;t<this.dataTypeArray.length;t++)"IKR"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].l1);this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}else{for(let e=0;e<this.dataTypeArray.length;e++)"IKR"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0");this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}})),e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}))}getKPICount(){return Object(d.c)(this,void 0,void 0,(function*(){let e,t,i,a,r=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),n=this.getDateLogic(r);e=n.durationStartDate,t=n.durationEndDate,i=n.endDateStartDate,a=n.endDateEndDate;let o=this.getFilters(r);o.push({filterName:"Org Code",valueId:[this.orgCode]});let s={skip:this.skip,limit:this.limit,filter:o,cardData:{dataType:"KPI",dataTypeValue:"0",isActive:!1,isVisible:!1,dataTypeCode:"KPI",c_type:"IKR",apiCardType:"KPI",apiCardFilter:[]},startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,durationStartDate:e,durationEndDate:t,endDateStartDate:i,endDateEndDate:a};console.log(s),console.log("works"),this._okrService.getOKRCard(s).pipe(Object(O.a)(this._onDestroy)).pipe(Object(O.a)(this._onAppApiCalled)).subscribe(e=>Object(d.c)(this,void 0,void 0,(function*(){if(this.udrfService.udrfData.udrfSummaryCardCodes.length>0&&this.resolveVisibleDataTypeArray(),"N"==e.err&&e.data&&e.data.length>0){for(let t=0;t<this.dataTypeArray.length;t++)"KPI"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].l1);this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}else{for(let e=0;e<this.dataTypeArray.length;e++)"KPI"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0");this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}})),e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}))}getBAUCount(){return Object(d.c)(this,void 0,void 0,(function*(){let e,t,i,a,r=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),n=this.getDateLogic(r);e=n.durationStartDate,t=n.durationEndDate,i=n.endDateStartDate,a=n.endDateEndDate;let o=this.getFilters(r);o.push({filterName:"Org Code",valueId:[this.orgCode]});let s={skip:this.skip,limit:this.limit,filter:o,cardData:{dataType:"BAU",dataTypeValue:"0",isActive:!1,isVisible:!1,dataTypeCode:"BAU",c_type:"IKR",apiCardType:"BAU",apiCardFilter:[]},startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,durationStartDate:e,durationEndDate:t,endDateStartDate:i,endDateEndDate:a};console.log(s),console.log("works"),this._okrService.getOKRCard(s).pipe(Object(O.a)(this._onDestroy)).pipe(Object(O.a)(this._onAppApiCalled)).subscribe(e=>Object(d.c)(this,void 0,void 0,(function*(){if(this.udrfService.udrfData.udrfSummaryCardCodes.length>0&&this.resolveVisibleDataTypeArray(),"N"==e.err&&e.data&&e.data.length>0){for(let t=0;t<this.dataTypeArray.length;t++)"IKR"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].l1);this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}else{for(let e=0;e<this.dataTypeArray.length;e++)"IKR"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0");this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}})),e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}))}initOKRCard(){return Object(d.c)(this,void 0,void 0,(function*(){yield this.getObjCount(),yield this.getKRCount(),yield this.getInitiativeCount(),yield this.getTaskCount(),yield this.getNSKRCount(),yield this.getCOKRCount(),yield this.getOTKRCount(),yield this.getARKRCount(),yield this.getDKRCount(),yield this.getOHKRCount(),yield this.getOTINICount(),yield this.getCOINICount(),yield this.getNSINICount(),yield this.getARINICount(),yield this.getDINICount(),yield this.getOHINICount(),yield this.getNSTCount(),yield this.getIPTCount(),yield this.getMTCount(),yield this.getPTCount(),yield this.getCTCount(),yield this.getOHTCount(),yield this.getPSCount(),yield this.getASCount(),yield this.getTSCount(),yield this.getPSCount(),yield this.getASCount(),yield this.getTSCount(),yield this.getIKRCount(),yield this.getKPICount()}))}onOKRItemDataScrollDown(){return Object(d.c)(this,void 0,void 0,(function*(){this.udrfService.udrfData.noItemDataFound||this.udrfService.udrfData.isItemDataLoading||(this.skip+=this.defaultDataRetrievalCount,this.udrfService.udrfData.isItemDataLoading=!0,yield this.getOKRList(!0))}))}resolveVisibleDataTypeArray(){return Object(d.c)(this,void 0,void 0,(function*(){for(let e of this.udrfService.udrfUiData.summaryCards){let t;this.udrfService.udrfData.udrfSummaryCardCodes.length>0&&(t=xi.contains(this.udrfService.udrfData.udrfSummaryCardCodes,e.dataTypeCode),e.isVisible=t,t&&(this.udrfService.udrfUiData.summaryCardsItem=e))}}))}changeVisibleBodyCol(){let e=this.dataTypeArray.filter(e=>1==e.isActive);0==e.length&&(e[0]=this.dataTypeArray[0]),this.udrfBodyColumns.forEach(t=>{t.isVisible=t.category.includes(e[0].c_type)?"true":"false"}),console.log(this.udrfBodyColumns)}showErrorMessage(e){this.utilityService.showErrorMessage("Error:Unable to fetch Data","KEBS")}getColor(e){return 0==e?"#e2e2e2":1==e?"#ffa502":2==e?"#009432":void 0}encodeURIComponent(e){return encodeURIComponent(e).replace(/[!'()*]/g,(function(e){return"%"+e.charCodeAt(0).toString(16)}))}openComments(){return Object(d.c)(this,void 0,void 0,(function*(){let e,t=this.udrfService.udrfUiData.openCommentsData;if("Objective"==t.data.type||"IKR"==t.data.type)e={inputData:{application_id:143,application_name:"OKR",title:t.data._id?t.data.name:t.data.obj_name,unique_id_1:t.data._id,unique_id_2:""},context:{Objective:t.data.name,Year:t.data.period},commentBoxHeight:"100vh",commentBoxScrollHeight:"80%"};else{let i=t.data;"KeyResult"==i.type||"Initiative"==i.type?e={inputData:{application_id:143,application_name:"OKR",title:i.name?i.name:i.item_name?i.item_name:"",unique_id_1:i._id,unique_id_2:""},context:{"Type Name":i.name?i.name:i.item_name?i.item_name:"",Year:i.period?i.period:i.item_period?i.item_period:"",Type:i.type},commentBoxHeight:"100vh",commentBoxScrollHeight:"80%"}:"MileStone"!=i.type&&"Milestone"!=i.type||(e={inputData:{application_id:143,application_name:"OKR",title:i.name?i.name:i.item_name?i.item_name:"",unique_id_1:i._id,unique_id_2:""},context:{"Task Name":i.name?i.name:i.item_name?i.item_name:"","Task status":i.status_name?i.status_name:i.item_status_name?i.item_status_name:"",Type:i.type},commentBoxHeight:"100vh",commentBoxScrollHeight:"80%"})}const{ChatCommentContextModalComponent:a}=yield Promise.all([i.e(4),i.e(63),i.e(75),i.e(983)]).then(i.bind(null,"vg2w"));this.dialog.open(a,{height:"100%",width:"50%",position:{right:"0px"},data:{modalParams:e}})}))}openCTA(){this._lazyService.openQuickCTAModal(null,this.dialog)}getFilters(e){let t=[];for(let i of e)if(i.isIdBased)t.push({filterName:i.filterName,valueId:i.multiOptionSelectSearchValuesWithId});else if(xi.contains(i.multiOptionSelectSearchValues,"Initiative")&&xi.contains(i.multiOptionSelectSearchValues,"KeyResult"))t.push({filterName:i.filterName,valueId:[{kr:1,init:1}]});else if(6==i.filterId)t.push("Initiative"==i.multiOptionSelectSearchValues?{filterName:i.filterName,valueId:[{kr:0,init:1}]}:{filterName:i.filterName,valueId:[{kr:1,init:0}]});else if(7==i.filterId)"KeyResult/KPI"==i.multiOptionSelectSearchValues?t.push({filterName:i.filterName,valueId:[{kr:1,init:0}]}):"Initiative/BAU"==i.multiOptionSelectSearchValues?t.push({filterName:i.filterName,valueId:[{kr:0,init:1}]}):(xi.contains(i.multiOptionSelectSearchValues,"KeyResult/KPI")&&xi.contains(i.multiOptionSelectSearchValues,"Initiative/BAU"),t.push({filterName:i.filterName,valueId:[{kr:1,init:1}]}));else if(10==i.filterId){console.log("hii",i.checkboxValues);let e=xi.where(this.filterYears,{type:xi.where(i.checkboxValues,{isCheckboxSelected:!0})[0].checkboxName})[0].fyr;for(let a of i.checkboxValues)a.isCheckboxSelected&&t.push({filterName:i.filterName,valueId:[{lower_limit:a.checkboxStartValue,upper_limit:a.checkboxEndValue}],filterYear:e})}else t.push({filterName:i.filterName,valueId:i.multiOptionSelectSearchValues});return t}openBtnClick(){let e=this.udrfService.udrfUiData.openButtonData;"Objective"!=e.data.type&&"IKR"!=e.data.type||window.open("/main/okr/detail/"+e.data._id+"/Objective","_blank"),"KeyResult"==e.data.type||"KPI"==e.data.type?window.open("/main/okr/kr/"+e.data._id+"/KeyResult","_blank"):"Initiative"==e.data.type||"BAU"==e.data.type?window.open("/main/okr/initiative/"+e.data._id+"/Initiative","_blank"):"MileStone"!=e.data.type&&"Milestone"!=e.data.type||("key_results"==e.data.mapped_under_name||"KPI"==e.data.type?window.open("/main/okr/kr/"+e.data.mapped_under_id+"/KeyResult/tasks","_blank"):"initiatives"!=e.data.mapped_under_name&&"BAU"!=e.data.type||window.open("/main/okr/initiative/"+e.data.mapped_under_id+"/Initiative/tasks","_blank"))}getDateLogic(e){let t={durationStartDate:"",durationEndDate:"",endDateStartDate:"",endDateEndDate:""};console.log(e);for(let i of e)if(12==i.filterId){let e=xi.where(i.checkboxValues,{isCheckboxSelected:!0});e&&e.length>0&&(t.endDateStartDate=e[0].checkboxStartValue,t.endDateEndDate=e[0].checkboxEndValue)}else if(13==i.filterId){let e=xi.where(i.checkboxValues,{isCheckboxSelected:!0});t.durationStartDate=e[0].checkboxStartValue,t.durationEndDate=e[0].checkboxEndValue}return t}getAllCardCount(){return Object(d.c)(this,void 0,void 0,(function*(){let e,t,i,a,r=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),n=this.getDateLogic(r);e=n.durationStartDate,t=n.durationEndDate,i=n.endDateStartDate,a=n.endDateEndDate;let o=this.getFilters(r);o.push({filterName:"Org Code",valueId:[this.orgCode]});let s={skip:this.skip,limit:this.limit,filter:o,startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,durationStartDate:e,durationEndDate:t,endDateStartDate:i,endDateEndDate:a};for(let d=0;d<this.defaultFilters.length;d++)xi.contains(xi.pluck(xi.where(s.filter,{filterName:this.defaultFilters[d].filterName}),"filterName"),this.defaultFilters[d].filterName)||s.filter.push({filterName:this.defaultFilters[d].filterName,valueId:this.defaultFilters[d].valueId});this._okrService.getOKRCard(s).pipe(Object(O.a)(this._onDestroy)).pipe(Object(O.a)(this._onAppApiCalled)).subscribe(e=>Object(d.c)(this,void 0,void 0,(function*(){if(this.udrfService.udrfData.udrfSummaryCardCodes.length>0&&this.resolveVisibleDataTypeArray(),"N"==e.err&&e.data&&e.data.length>0){for(let t=0;t<this.dataTypeArray.length;t++)"Objective"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].objective),"KeyResult"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].kr),"Initiative"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].init),"Milestones"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].task),"IKR"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].ikr),"KPI"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].kpi),"BAU"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].bau),"Total Score"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].tot_score),"Actual Score"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].act_score),"Planned Score"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].plnd_score),"IKR Total Score"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].ikr_tot_score),"IKR Actual Score"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].ikr_act_score),"IKR Planned Score"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].ikr_plnd_score),"On Hold(Milestones)"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].task_on_hold),"Completed(Milestones)"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].task_completed),"Delayed(Milestones)"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].task_delayed),"At Risk(Milestones)"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].task_at_risk),"On Track(Milestones)"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].task_on_track),"Not Started(Milestones)"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].task_not_started),"On Hold(Initiative)"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].init_on_hold),"Delayed(Initiative)"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].init_delayed),"At risk(Initiative)"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].init_at_risk),"Not started(Initiative)"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].init_not_started),"Completed(Initiative)"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].init_completed),"On track(Initiative)"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].init_on_track),"On Hold(KeyResult)"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].kr_on_hold),"Delayed(KeyResult)"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].kr_delayed),"At risk(KeyResult)"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].kr_at_risk),"On track(KeyResult)"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].kr_on_track),"Completed(KeyResult)"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].kr_completed),"Not started(KeyResult)"==this.dataTypeArray[t].dataType&&(this.dataTypeArray[t].dataTypeValue=e.data[0].kr_not_started);this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}else{for(let e=0;e<this.dataTypeArray.length;e++)"Objective"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0"),"KeyResult"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0"),"Initiative"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0"),"Milestones"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0"),"IKR"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0"),"KPI"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0"),"BAU"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0"),"Total Score"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0"),"Actual Score"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0"),"Planned Score"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0"),"On Hold(Milestones)"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0"),"Completed(Milestones)"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0"),"Delayed(Milestones)"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0"),"At Risk(Milestones)"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0"),"On Track(Milestones)"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0"),"Not Started(Milestones)"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0"),"On Hold(Initiative)"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0"),"Delayed(Initiative)"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0"),"At risk(Initiative)"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0"),"Not started(Initiative)"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0"),"Completed(Initiative)"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0"),"On track(Initiative)"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0"),"On Hold(KeyResult)"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0"),"Delayed(KeyResult)"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0"),"At risk(KeyResult)"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0"),"On track(KeyResult)"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0"),"Completed(KeyResult)"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0"),"Not started(KeyResult)"==this.dataTypeArray[e].dataType&&(this.dataTypeArray[e].dataTypeValue="0");this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}})),e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}))}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete(),this.udrfService.resetUdrfData()}openCollabrative(){let e,t,i,a,r=[],n=this.udrfService.udrfUiData.collabarationData;if(e=n.data._id,t=n.data.type,i=n.data,a=n.data.children,"Objective"==t){let i=!1;for(let e=0;e<a.length&&0==i;e++)if(a[e].is_collaborative&&1==a[e].is_collaborative){i=!0;break}this.openCollabrativePopup(e,t,"")}else if("IKR"==t)this._okrService.getKpiAndBauForIntegration({objId:e}).subscribe(i=>{"N"==i.err&&(console.log(i),this.kpiList=i.kpiList,this.bauList=i.bauList,this.openCollabrativePopup(e,t,"",this.kpiList,this.bauList))},e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)});else if("KeyResult"==t){let a={obj_start_date:i.obj_start_date,obj_end_date:i.obj_end_date,id:e};console.log(a),this.subs.sink=this._appOkr.getKRCollabrative(a).subscribe(i=>{console.log("Kr Collab Successfully"),r=i.data,this.openCollabrativePopup(e,t,r)},e=>{console.log("Error"),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}else if("Initiative"==t){let a={obj_start_date:i.obj_start_date,obj_end_date:i.obj_end_date,id:e};console.log(a),this.subs.sink=this._appOkr.getInitCollabrative(a).subscribe(i=>{console.log("Kr Collab Successfully"),r=i.data,this.openCollabrativePopup(e,t,r)},e=>{console.log("Error"),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}else"MileStone"!=t&&"Milestone"!=t||(console.log({task_id:e}),this.openCollabrativePopup(e,"Milestone",r))}openCollabrativePopup(e,t,a,r,n){return Object(d.c)(this,void 0,void 0,(function*(){const{CollabrativeOkrComponent:o}=yield i.e(182).then(i.bind(null,"Ozlk"));this.dialog.open(o,{width:"300px",autoFocus:!1,data:{id:e,name:t,list:a,clickedCard:this.okrCardClickedData,bauList:n,kpiList:r}}).afterClosed().subscribe(e=>{"sucess"==e&&this.ngOnInit()})}))}openAggregate(){let e,t,i,a,r=this.udrfService.udrfUiData.AggregationData;e=r.data._id,t=r.data.type,i=r.data,a=r.data.children,i.is_child_aggregation&&1==i.is_child_aggregation?this.utilityService.showMessage("Already Aggregated","Dismiss"):this.openAggregativePopup(e,t)}getCollaborativeDetails(){return Object(d.c)(this,void 0,void 0,(function*(){let e=this.udrfService.udrfUiData.okrCollaborativeData.item,t=yield this._appOkr.getCollaborativeDetails({type:e.type,type_id:e._id});e.collaborativeObjectiveDetails=t.collaborativeObjectiveDetails,e.collaborativeKrDetails=t.collaborativeKrDetails,e.collaborativeInitDetails=t.collaborativeInitDetails,e.collaborativeTaskDetails=t.collaborativeTaskDetails}))}getChildAggregationDetails(){return Object(d.c)(this,void 0,void 0,(function*(){let e=this.udrfService.udrfUiData.okrChildAggregativeData.item,t=yield this._appOkr.getChildAggregationDetails({type:e.type,type_id:e._id});e.childAggregationDetails=t.childAggregationDetails}))}getParentAggregationDetails(){return Object(d.c)(this,void 0,void 0,(function*(){let e=this.udrfService.udrfUiData.okrParentAggregativeData.item,t=yield this._appOkr.getParentAggregationDetails({type:e.type,type_id:e._id});e.parentAggregationDetails=t.parentAggregationDetails}))}openMeetingNotes(){this.dialog.open(Ei,{width:"55%",height:"55vh",autoFocus:!1,maxWidth:"90vw",data:{db_name:"okr"}}).afterClosed().subscribe(e=>{this.getAllCardCount()})}openOrgChange(){this.dialog.open(G,{minWidth:"55%",minHeight:"100vh",autoFocus:!1,position:{top:"0",right:"0"}}).afterClosed().subscribe(e=>{})}}return e.\u0275fac=function(t){return new(t||e)(f["\u0275\u0275directiveInject"](y.a),f["\u0275\u0275directiveInject"](Pi.a),f["\u0275\u0275directiveInject"](ji.a),f["\u0275\u0275directiveInject"](p.b),f["\u0275\u0275directiveInject"](Si.a),f["\u0275\u0275directiveInject"](g.a),f["\u0275\u0275directiveInject"](Ri.a),f["\u0275\u0275directiveInject"](vi.a),f["\u0275\u0275directiveInject"](Ni.a),f["\u0275\u0275directiveInject"](bi.a),f["\u0275\u0275directiveInject"](Ci.a),f["\u0275\u0275directiveInject"](v.a))},e.\u0275cmp=f["\u0275\u0275defineComponent"]({type:e,selectors:[["okr-list"]],inputs:{orgCode:"orgCode",orgName:"orgName",isFromOrgChart:"isFromOrgChart",fyYear:"fyYear",showSummaryCards:"showSummaryCards"},outputs:{evaluatorResponse:"evaluatorResponse"},decls:13,vars:2,consts:[[1,"container-fluid","okr-report-styles","pl-0","pr-0"],[1,"col-12","pt-1","d-flex","justify-content-between","align-items-center"],[1,"org-name"],[1,"ml-3","pl-0"],["mat-button","",1,"ml-3","pl-0","header-btn",3,"matTooltip","click"],[1,"header-btn-icon"],[1,"pl-1","header-btn-txt"],[1,"col-12","px-1","py-1","okr-list-container"]],template:function(e,t){1&e&&(f["\u0275\u0275elementStart"](0,"div",0),f["\u0275\u0275element"](1,"udrf-header"),f["\u0275\u0275elementStart"](2,"div",1),f["\u0275\u0275elementStart"](3,"span",2),f["\u0275\u0275text"](4),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](5,"div",3),f["\u0275\u0275elementStart"](6,"button",4),f["\u0275\u0275listener"]("click",(function(){return t.openMeetingNotes()})),f["\u0275\u0275elementStart"](7,"mat-icon",5),f["\u0275\u0275text"](8,"forum"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](9,"span",6),f["\u0275\u0275text"](10,"Meeting Notes"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](11,"div",7),f["\u0275\u0275element"](12,"udrf-body"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]()),2&e&&(f["\u0275\u0275advance"](4),f["\u0275\u0275textInterpolate1"]("OKRs: ",t.orgName,""),f["\u0275\u0275advance"](2),f["\u0275\u0275property"]("matTooltip","Meeting Notes"))},directives:[Vi.a,b.a,n.a,S.a,Fi.a],styles:[".okr-report-styles[_ngcontent-%COMP%]   .status-circular[_ngcontent-%COMP%]{height:13px;width:13px;margin-top:4px;border-radius:50%;box-shadow:0 2px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.okr-report-styles[_ngcontent-%COMP%]   .okr-list-container[_ngcontent-%COMP%]{border:1px solid #e8e9ee;box-sizing:border-box;border-radius:8px;margin:1em 0 2em!important}.okr-report-styles[_ngcontent-%COMP%]   .org-name[_ngcontent-%COMP%]{color:#455468;font-size:14px;font-weight:500;font-family:DM Sans;text-transform:capitalize;font-style:normal;line-height:16px}.okr-report-styles[_ngcontent-%COMP%]   .is-submitted[_ngcontent-%COMP%]{background:#ff7200;color:#fff!important}.okr-report-styles[_ngcontent-%COMP%]   .is-draft[_ngcontent-%COMP%]{background:#c7c4c4;color:#fff!important}.okr-report-styles[_ngcontent-%COMP%]   .is-approved[_ngcontent-%COMP%]{background:#009432;color:#fff!important}.okr-report-styles[_ngcontent-%COMP%]   .is-wfh[_ngcontent-%COMP%]{background:#9980fa;color:#fff!important}.okr-report-styles[_ngcontent-%COMP%]   .is-rejected[_ngcontent-%COMP%]{background:#cf0001;color:#fff!important}.okr-report-styles[_ngcontent-%COMP%]   .is-cancelled[_ngcontent-%COMP%]{background:#9f2825;color:#fff!important}.okr-report-styles[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field{width:40vw;padding-top:25px}.okr-report-styles[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-infix{font-size:14px!important;padding:.5em 0!important;border-top:.54375em solid transparent!important}.okr-report-styles[_ngcontent-%COMP%]   .spinner-align[_ngcontent-%COMP%]{margin:auto;display:inline-block}.okr-report-styles[_ngcontent-%COMP%]   .infinite-scroll-fixed[_ngcontent-%COMP%]{height:425px;overflow-y:scroll}.okr-report-styles[_ngcontent-%COMP%]   .infinite-scroll-fixed-shortened[_ngcontent-%COMP%]{height:435px;overflow-y:scroll}.okr-report-styles[_ngcontent-%COMP%]   .ta-r[_ngcontent-%COMP%]{text-align:right!important}.okr-report-styles[_ngcontent-%COMP%]   .ta-l[_ngcontent-%COMP%]{text-align:left}.okr-report-styles[_ngcontent-%COMP%]   .is-normal-probability-text[_ngcontent-%COMP%]{color:grey!important}.okr-report-styles[_ngcontent-%COMP%]   .is-low-probability-text[_ngcontent-%COMP%]{color:#cf0001!important}.okr-report-styles[_ngcontent-%COMP%]   .is-moderate-probability-text[_ngcontent-%COMP%]{color:#ff7200!important}.okr-report-styles[_ngcontent-%COMP%]   .is-high-probability-text[_ngcontent-%COMP%]{color:#009432!important}.okr-report-styles[_ngcontent-%COMP%]   .is-normal-probability[_ngcontent-%COMP%]{border-left-color:grey!important}.okr-report-styles[_ngcontent-%COMP%]   .is-low-probability[_ngcontent-%COMP%]{border-left-color:#cf0001!important}.okr-report-styles[_ngcontent-%COMP%]   .is-moderate-probability[_ngcontent-%COMP%]{border-left-color:#ff7200!important}.okr-report-styles[_ngcontent-%COMP%]   .is-high-probability[_ngcontent-%COMP%]{border-left-color:#009432!important}.okr-report-styles[_ngcontent-%COMP%]   .value13Bold[_ngcontent-%COMP%]{font-size:13px!important}.okr-report-styles[_ngcontent-%COMP%]   .value13Bold[_ngcontent-%COMP%], .okr-report-styles[_ngcontent-%COMP%]   .value14Bold[_ngcontent-%COMP%]{color:#000;font-weight:500!important;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.okr-report-styles[_ngcontent-%COMP%]   .value14Bold[_ngcontent-%COMP%]{font-size:14px!important}.okr-report-styles[_ngcontent-%COMP%]   .listcard[_ngcontent-%COMP%]{transition:all .25s linear;height:auto}.okr-report-styles[_ngcontent-%COMP%]   .smallSubtleText[_ngcontent-%COMP%]{color:#66615b;font-size:11px}.okr-report-styles[_ngcontent-%COMP%]   .data-type-card[_ngcontent-%COMP%]{min-height:85px;transition:all .3s linear}.okr-report-styles[_ngcontent-%COMP%]   .data-type-card-is-active[_ngcontent-%COMP%]{border-color:#cf0001;background-color:#fff2f2}.okr-report-styles[_ngcontent-%COMP%]   .headingBold[_ngcontent-%COMP%]{color:#1a1a1a;font-weight:600;font-size:22px}.okr-report-styles[_ngcontent-%COMP%]   .valueGrey14[_ngcontent-%COMP%]{font-size:14px;font-weight:300}.okr-report-styles[_ngcontent-%COMP%]   .valueGrey12Normal[_ngcontent-%COMP%], .okr-report-styles[_ngcontent-%COMP%]   .valueGrey14[_ngcontent-%COMP%]{color:#4a4a4a;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.okr-report-styles[_ngcontent-%COMP%]   .valueGrey12Normal[_ngcontent-%COMP%]{font-size:12px;font-weight:400}.okr-report-styles[_ngcontent-%COMP%]   .cp[_ngcontent-%COMP%]{cursor:pointer!important}.okr-report-styles[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]{margin-top:5px!important;line-height:8px;width:26px;height:26px;margin-right:10px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12);animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}.okr-report-styles[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%], .okr-report-styles[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.okr-report-styles[_ngcontent-%COMP%]   .ta-c[_ngcontent-%COMP%]{text-align:center;padding-left:0!important;padding-right:0!important}.okr-report-styles[_ngcontent-%COMP%]   .divAlignItemCenter[_ngcontent-%COMP%]{display:flex;align-items:center}.okr-report-styles[_ngcontent-%COMP%]   .menu-icons[_ngcontent-%COMP%]{font-size:4px}.okr-report-styles[_ngcontent-%COMP%]   .example-container[_ngcontent-%COMP%]{width:400px;max-width:100%;margin:0 25px 25px 0;display:inline-block;vertical-align:top}.okr-report-styles[_ngcontent-%COMP%]   .example-list[_ngcontent-%COMP%]{border:1px solid #ccc;min-height:60px;background:#fff;border-radius:4px;overflow:hidden;display:block}.okr-report-styles[_ngcontent-%COMP%]     .example-box{border-bottom:1px solid #ccc;color:rgba(0,0,0,.87);display:flex;flex-direction:row;align-items:left;justify-content:space-between;box-sizing:border-box;cursor:move;background:#fff;font-size:4px}.okr-report-styles[_ngcontent-%COMP%]     .menu-list{cursor:move;font-size:8px}.okr-report-styles[_ngcontent-%COMP%]   .cdk-drag-preview[_ngcontent-%COMP%]{box-sizing:border-box;border-radius:4px;box-shadow:0 5px 5px -3px rgba(0,0,0,.2),0 8px 10px 1px rgba(0,0,0,.14),0 3px 14px 2px rgba(0,0,0,.12)}.okr-report-styles[_ngcontent-%COMP%]   .cdk-drag-placeholder[_ngcontent-%COMP%]{opacity:0}.okr-report-styles[_ngcontent-%COMP%]   .cdk-drag-animating[_ngcontent-%COMP%]{transition:transform .25s cubic-bezier(0,0,.2,1)}.okr-report-styles[_ngcontent-%COMP%]   .example-box[_ngcontent-%COMP%]:last-child{border:none}.okr-report-styles[_ngcontent-%COMP%]   .example-list.cdk-drop-list-dragging[_ngcontent-%COMP%]   .example-box[_ngcontent-%COMP%]:not(.cdk-drag-placeholder){transition:transform .25s cubic-bezier(0,0,.2,1)}.okr-report-styles[_ngcontent-%COMP%]   .header-btn[_ngcontent-%COMP%]   .header-btn-icon[_ngcontent-%COMP%]{color:#66615b;font-size:18px;line-height:22px}.okr-report-styles[_ngcontent-%COMP%]   .header-btn[_ngcontent-%COMP%]   .header-btn-txt[_ngcontent-%COMP%]{color:#6b7a99;font-size:13px;font-weight:400;font-family:DM Sans;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;line-height:3.8em}"],data:{animation:[Object(Ai.o)("slideInOut",[Object(Ai.l)("in",Object(Ai.m)({height:"*",overflow:"hidden"})),Object(Ai.l)("out",Object(Ai.m)({height:0,overflow:"hidden"})),Object(Ai.n)("* => in",[Object(Ai.m)({height:0}),Object(Ai.e)(250,Object(Ai.m)({height:"*"}))]),Object(Ai.n)("in=> *",[Object(Ai.m)({height:"*"}),Object(Ai.e)(250,Object(Ai.m)({height:0}))])]),Object(Ai.o)("smallCardAnimation",[Object(Ai.n)("* => *",[Object(Ai.i)(":leave",[Object(Ai.k)(100,[Object(Ai.e)("0.5s",Object(Ai.m)({opacity:0}))])],{optional:!0}),Object(Ai.i)(":enter",[Object(Ai.m)({opacity:0}),Object(Ai.k)(100,[Object(Ai.e)("0.5s",Object(Ai.m)({opacity:1}))])],{optional:!0})])])]}}),e})();const Ki=["triggerColumnCustomizationTemplateRef"];function Li(e,t){if(1&e){const e=f["\u0275\u0275getCurrentView"]();f["\u0275\u0275elementStart"](0,"div",6),f["\u0275\u0275elementStart"](1,"form",17),f["\u0275\u0275elementStart"](2,"app-input-search",18),f["\u0275\u0275listener"]("change",(function(t){return f["\u0275\u0275restoreView"](e),f["\u0275\u0275nextContext"]().selectOKRFOrg(t)})),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]()}if(2&e){const e=f["\u0275\u0275nextContext"]();f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("formGroup",e.orgSelectGroup),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("list",e.organizations)}}function Hi(e,t){if(1&e){const e=f["\u0275\u0275getCurrentView"]();f["\u0275\u0275elementStart"](0,"div",19),f["\u0275\u0275elementStart"](1,"div",20),f["\u0275\u0275elementStart"](2,"button",21),f["\u0275\u0275listener"]("click",(function(){return f["\u0275\u0275restoreView"](e),f["\u0275\u0275nextContext"]().openOrgChange()})),f["\u0275\u0275elementStart"](3,"mat-icon",22),f["\u0275\u0275text"](4,"difference"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275element"](5,"div",10),f["\u0275\u0275elementEnd"]()}2&e&&(f["\u0275\u0275advance"](2),f["\u0275\u0275property"]("matTooltip","OKR - Organization Change Wizard"))}function Yi(e,t){if(1&e){const e=f["\u0275\u0275getCurrentView"]();f["\u0275\u0275elementContainerStart"](0),f["\u0275\u0275elementStart"](1,"div",6),f["\u0275\u0275elementStart"](2,"button",7),f["\u0275\u0275listener"]("click",(function(){return f["\u0275\u0275restoreView"](e),f["\u0275\u0275nextContext"]().openObjPopUp()})),f["\u0275\u0275elementStart"](3,"mat-icon",8),f["\u0275\u0275text"](4,"add_circle "),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](5,"span",9),f["\u0275\u0275text"](6,"Create Objective"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275element"](7,"div",10),f["\u0275\u0275elementContainerEnd"]()}2&e&&(f["\u0275\u0275advance"](2),f["\u0275\u0275property"]("matTooltip","Create Objective"))}function zi(e,t){if(1&e){const e=f["\u0275\u0275getCurrentView"]();f["\u0275\u0275elementStart"](0,"div",5),f["\u0275\u0275elementStart"](1,"div",6),f["\u0275\u0275elementStart"](2,"button",7),f["\u0275\u0275listener"]("click",(function(){return f["\u0275\u0275restoreView"](e),f["\u0275\u0275nextContext"]().goToConfiguration()})),f["\u0275\u0275elementStart"](3,"mat-icon",8),f["\u0275\u0275text"](4,"settings"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](5,"span",9),f["\u0275\u0275text"](6,"Configurations"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275element"](7,"div",10),f["\u0275\u0275elementEnd"]()}2&e&&(f["\u0275\u0275advance"](2),f["\u0275\u0275property"]("matTooltip","Go To Home Page"))}function Bi(e,t){if(1&e){const e=f["\u0275\u0275getCurrentView"]();f["\u0275\u0275elementStart"](0,"div",33),f["\u0275\u0275elementStart"](1,"div",34),f["\u0275\u0275elementStart"](2,"div",35),f["\u0275\u0275element"](3,"img",36),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](4,"div",34),f["\u0275\u0275elementStart"](5,"div",37),f["\u0275\u0275elementStart"](6,"span"),f["\u0275\u0275text"](7,"Add Your Organiztions' Vision and Mission in this space."),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](8,"div",38),f["\u0275\u0275elementStart"](9,"div",39),f["\u0275\u0275elementStart"](10,"button",40),f["\u0275\u0275listener"]("click",(function(){return f["\u0275\u0275restoreView"](e),f["\u0275\u0275nextContext"](2).openAddVisionAndMissionDialog(!1)})),f["\u0275\u0275text"](11,"Add Content"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]()}if(2&e){const e=f["\u0275\u0275nextContext"](2);f["\u0275\u0275advance"](10),f["\u0275\u0275property"]("disabled",!e.isAdmin)}}function Wi(e,t){if(1&e){const e=f["\u0275\u0275getCurrentView"]();f["\u0275\u0275elementStart"](0,"a",54),f["\u0275\u0275listener"]("click",(function(){return f["\u0275\u0275restoreView"](e),f["\u0275\u0275nextContext"](4).readMore=!1})),f["\u0275\u0275text"](1,"Read More"),f["\u0275\u0275elementEnd"]()}}function Ji(e,t){if(1&e){const e=f["\u0275\u0275getCurrentView"]();f["\u0275\u0275elementStart"](0,"a",57),f["\u0275\u0275listener"]("click",(function(){return f["\u0275\u0275restoreView"](e),f["\u0275\u0275nextContext"](5).readMore=!0})),f["\u0275\u0275text"](1,"Show less"),f["\u0275\u0275elementEnd"]()}}function Gi(e,t){if(1&e&&(f["\u0275\u0275elementStart"](0,"div",15),f["\u0275\u0275elementStart"](1,"div",44),f["\u0275\u0275elementStart"](2,"span",55),f["\u0275\u0275text"](3),f["\u0275\u0275elementEnd"](),f["\u0275\u0275template"](4,Ji,2,0,"a",56),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]()),2&e){const e=f["\u0275\u0275nextContext"]().$implicit,t=f["\u0275\u0275nextContext"](3);f["\u0275\u0275advance"](3),f["\u0275\u0275textInterpolate"](e.desc),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",!t.readMore)}}function Xi(e,t){if(1&e&&(f["\u0275\u0275elementStart"](0,"div"),f["\u0275\u0275elementStart"](1,"div",15),f["\u0275\u0275elementStart"](2,"div",50),f["\u0275\u0275elementStart"](3,"span",51),f["\u0275\u0275text"](4),f["\u0275\u0275elementEnd"](),f["\u0275\u0275template"](5,Wi,2,0,"a",52),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275template"](6,Gi,5,2,"div",53),f["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,i=f["\u0275\u0275nextContext"](3);f["\u0275\u0275advance"](3),f["\u0275\u0275property"]("ngClass",i.readMore?"overflow":""),f["\u0275\u0275advance"](1),f["\u0275\u0275textInterpolate"](e.title),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",i.readMore),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",!i.readMore)}}function qi(e,t){if(1&e&&(f["\u0275\u0275elementStart"](0,"div",41),f["\u0275\u0275elementStart"](1,"div",42),f["\u0275\u0275elementStart"](2,"div",43),f["\u0275\u0275elementStart"](3,"div",44),f["\u0275\u0275elementStart"](4,"span",45),f["\u0275\u0275text"](5,"Our Mission & Vision"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](6,"div",46),f["\u0275\u0275element"](7,"div",47),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](8,"div",48),f["\u0275\u0275template"](9,Xi,7,4,"div",49),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]()),2&e){const e=f["\u0275\u0275nextContext"](2);f["\u0275\u0275advance"](9),f["\u0275\u0275property"]("ngForOf",e.visionMissionData)}}function Qi(e,t){1&e&&(f["\u0275\u0275elementStart"](0,"div",58),f["\u0275\u0275elementStart"](1,"div",59),f["\u0275\u0275elementStart"](2,"span",60),f["\u0275\u0275text"](3,"Loading..."),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]())}function $i(e,t){if(1&e){const e=f["\u0275\u0275getCurrentView"]();f["\u0275\u0275elementStart"](0,"div",23),f["\u0275\u0275elementStart"](1,"div",24),f["\u0275\u0275template"](2,Bi,12,1,"div",25),f["\u0275\u0275template"](3,qi,10,1,"div",26),f["\u0275\u0275elementStart"](4,"div",27),f["\u0275\u0275elementStart"](5,"button",28),f["\u0275\u0275elementStart"](6,"mat-icon",29),f["\u0275\u0275text"](7,"more_horiz"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](8,"mat-menu",null,30),f["\u0275\u0275elementStart"](10,"button",31),f["\u0275\u0275listener"]("click",(function(){return f["\u0275\u0275restoreView"](e),f["\u0275\u0275nextContext"]().openAddVisionAndMissionDialog(!0)})),f["\u0275\u0275elementStart"](11,"span"),f["\u0275\u0275text"](12,"Edit"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](13,"button",31),f["\u0275\u0275listener"]("click",(function(){return f["\u0275\u0275restoreView"](e),f["\u0275\u0275nextContext"]().deleteVisionAndMission()})),f["\u0275\u0275elementStart"](14,"span"),f["\u0275\u0275text"](15,"Delete"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275template"](16,Qi,4,0,"div",32),f["\u0275\u0275elementEnd"]()}if(2&e){const e=f["\u0275\u0275reference"](9),t=f["\u0275\u0275nextContext"]();f["\u0275\u0275property"]("ngClass",t.visionMissionData.length>0?"pt-2 pl-3 pr-3 pb-2":"px-3 py-3"),f["\u0275\u0275advance"](2),f["\u0275\u0275property"]("ngIf",0==t.visionMissionData.length&&0==t.visionLoading),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",t.visionMissionData.length>0&&0==t.visionLoading),f["\u0275\u0275advance"](2),f["\u0275\u0275property"]("matMenuTriggerFor",e),f["\u0275\u0275advance"](5),f["\u0275\u0275property"]("disabled",0==t.visionMissionData.length||!t.isAdmin),f["\u0275\u0275advance"](3),f["\u0275\u0275property"]("disabled",0==t.visionMissionData.length||!t.isAdmin),f["\u0275\u0275advance"](3),f["\u0275\u0275property"]("ngIf",0==t.visionMissionData.length&&1==t.visionLoading)}}function Zi(e,t){if(1&e){const e=f["\u0275\u0275getCurrentView"]();f["\u0275\u0275elementStart"](0,"div",69),f["\u0275\u0275elementStart"](1,"div",70),f["\u0275\u0275text"](2,"Organization Chart Settings"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](3,"div",71),f["\u0275\u0275elementStart"](4,"span",72),f["\u0275\u0275text"](5,"Display Org Full Name"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](6,"mat-slide-toggle",73),f["\u0275\u0275listener"]("change",(function(t){return f["\u0275\u0275restoreView"](e),f["\u0275\u0275nextContext"](2).displayFullOrgName(t)})),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]()}if(2&e){const e=f["\u0275\u0275nextContext"](2);f["\u0275\u0275advance"](6),f["\u0275\u0275property"]("checked",e.displayFullOrgNameCheck)}}function ea(e,t){if(1&e){const e=f["\u0275\u0275getCurrentView"]();f["\u0275\u0275elementStart"](0,"div",61),f["\u0275\u0275elementStart"](1,"div",62),f["\u0275\u0275elementStart"](2,"div",63),f["\u0275\u0275text"](3,"Organization Chart"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](4,"div",64,65),f["\u0275\u0275elementStart"](7,"button",66),f["\u0275\u0275listener"]("click",(function(){f["\u0275\u0275restoreView"](e);const t=f["\u0275\u0275reference"](6);return f["\u0275\u0275nextContext"]().openChartSettings(t)})),f["\u0275\u0275elementStart"](8,"mat-icon"),f["\u0275\u0275text"](9,"settings"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275template"](10,Zi,7,1,"ng-template",67,68,f["\u0275\u0275templateRefExtractor"]),f["\u0275\u0275elementEnd"]()}if(2&e){const e=f["\u0275\u0275reference"](5);f["\u0275\u0275advance"](7),f["\u0275\u0275property"]("matTooltip","Organization Chart Settings"),f["\u0275\u0275advance"](3),f["\u0275\u0275property"]("cdkConnectedOverlayOrigin",e)}}function ta(e,t){if(1&e){const e=f["\u0275\u0275getCurrentView"]();f["\u0275\u0275elementStart"](0,"button",82),f["\u0275\u0275listener"]("click",(function(){return f["\u0275\u0275restoreView"](e),f["\u0275\u0275nextContext"](2).moveLeft()})),f["\u0275\u0275elementStart"](1,"mat-icon"),f["\u0275\u0275text"](2,"keyboard_arrow_left"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]()}if(2&e){const e=f["\u0275\u0275nextContext"](2);f["\u0275\u0275property"]("disabled",e.lastActiveIndex<6)}}function ia(e,t){if(1&e){const e=f["\u0275\u0275getCurrentView"]();f["\u0275\u0275elementStart"](0,"button",82),f["\u0275\u0275listener"]("click",(function(){return f["\u0275\u0275restoreView"](e),f["\u0275\u0275nextContext"](2).moveRight()})),f["\u0275\u0275elementStart"](1,"mat-icon"),f["\u0275\u0275text"](2,"keyboard_arrow_right"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]()}if(2&e){const e=f["\u0275\u0275nextContext"](2);f["\u0275\u0275property"]("disabled",e.lastActiveIndex==e.childOrgList.length-1)}}function aa(e,t){if(1&e){const e=f["\u0275\u0275getCurrentView"]();f["\u0275\u0275elementStart"](0,"div",61),f["\u0275\u0275elementStart"](1,"div",74),f["\u0275\u0275template"](2,ta,3,1,"button",75),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](3,"div",76),f["\u0275\u0275elementStart"](4,"div",77),f["\u0275\u0275elementStart"](5,"button",78),f["\u0275\u0275listener"]("click",(function(){return f["\u0275\u0275restoreView"](e),f["\u0275\u0275nextContext"]().undoLastStep()})),f["\u0275\u0275elementStart"](6,"mat-icon"),f["\u0275\u0275text"](7,"undo"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](8,"button",79),f["\u0275\u0275listener"]("click",(function(){return f["\u0275\u0275restoreView"](e),f["\u0275\u0275nextContext"]().gotoPreviousOrg()})),f["\u0275\u0275elementStart"](9,"mat-icon"),f["\u0275\u0275text"](10,"keyboard_double_arrow_up"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](11,"kebs-org-chart",80),f["\u0275\u0275listener"]("itemClick",(function(t){return f["\u0275\u0275restoreView"](e),f["\u0275\u0275nextContext"]().test(t)})),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](12,"div",81),f["\u0275\u0275template"](13,ia,3,1,"button",75),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]()}if(2&e){const e=f["\u0275\u0275nextContext"]();f["\u0275\u0275advance"](2),f["\u0275\u0275property"]("ngIf",(null==e.nodes[0]?null:e.nodes[0].childs.length)>0),f["\u0275\u0275advance"](3),f["\u0275\u0275property"]("matTooltip","Go to Previous organization")("disabled",1==e.selectionHistory.length),f["\u0275\u0275advance"](3),f["\u0275\u0275property"]("matTooltip","Go to Parent organization")("disabled",(null==e.nodes[0]?null:e.nodes[0].org_code)==(null==e.userOrgObj?null:e.userOrgObj.org_code)&&!e.isAdmin),f["\u0275\u0275advance"](3),f["\u0275\u0275property"]("nodes",e.nodes),f["\u0275\u0275advance"](2),f["\u0275\u0275property"]("ngIf",(null==e.nodes[0]?null:e.nodes[0].childs.length)>0)}}function ra(e,t){if(1&e){const e=f["\u0275\u0275getCurrentView"]();f["\u0275\u0275elementStart"](0,"div",83),f["\u0275\u0275elementStart"](1,"okr-list",84),f["\u0275\u0275listener"]("evaluatorResponse",(function(t){return f["\u0275\u0275restoreView"](e),f["\u0275\u0275nextContext"]().getChanges(t)})),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]()}if(2&e){const e=f["\u0275\u0275nextContext"]();f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("orgCode",e.orgCode)("orgName",e.orgName)("isFromOrgChart",e.isFromOrgChart)("fyYear",e.fyYear)("showSummaryCards",e.okrListView)}}const na=function(){return{objId:"143015",entityOwner:"",type:"create"}};let oa=(()=>{class e{constructor(e,t,i,a,r,n,o,s,d,c,p){this._chart=e,this._okr=t,this._okrReport=i,this._ErrorService=a,this._loginService=r,this._dialog=n,this._util=o,this._rolesService=s,this.fb=d,this.overlay=c,this.viewContainerRef=p,this.nodes=[],this.fyYear="FY23",this.showHideList=[{name:"Vision & Mission",isVisible:!1},{name:"Organization Chart",isVisible:!0},{name:"Summary Card",isVisible:!0}],this.lastActiveIndex=0,this.isFromOrgChart=!0,this.readMore=!0,this.subs=new l.a,this.start=0,this.end=10,this.isAdmin=!1,this.orgsContainingOkr=[],this.childOrgList=[],this.isLoading=!1,this.visionLoading=!1,this.okrList=[],this.view=!1,this.orgChartView=!0,this.okrListView=!0,this.orgCode=20001,this.orgName="CEO Office",this.orgSearchUrl=window.location.origin+"/api/okr/objective/getAllOrg",this.selectionHistory=[],this.visionMissionData=[],this.disable=!1,this.userOrgObj=null,this.organizations=[],this.isOrgLoading=!1,this.displayFullOrgNameCheck=!1,this.getOkrOrgList=()=>{this._okr.getOrgsContainingOKR().subscribe(e=>{this.orgsContainingOkr=e,console.log(this.nodes),this.getInitialChildren(this.nodes[0].org_code,this.fyYear)},e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})},this.getInitialChildren=(e,t)=>{this._okr.getChildOrgs(e,t).subscribe(e=>{if(this.nodes[0].childs=[],this.childOrgList=e.filter(e=>e.org_code),this.lastActiveIndex=this.childOrgList.length>6?5:this.childOrgList.length-1,this.childOrgList.length>6)for(let t=0;t<6;t++)t<6&&(this.nodes[0].childs.push(this.childOrgList[t]),this.lastActiveIndex=t);else for(let t=0;t<this.childOrgList.length;t++)this.nodes[0].childs.push(this.childOrgList[t]),this.lastActiveIndex=t},e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})},this.getOkrList=(e,t,i)=>{this.isLoading=!0,this._okr.getOKrList(e,t,i).subscribe(e=>{this.okrList=e.data,this.isLoading=!1},e=>{this.isLoading=!1,this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})},this.getDefaultYear=()=>new Promise((e,t)=>{this._okr.getDefaultYear().subscribe(t=>{e(t.data)},e=>{t([])})}),this.orgSelectGroup=this.fb.group({searchControl:[null]})}getAllOrgForOKR(){return Object(d.c)(this,void 0,void 0,(function*(){try{this.isOrgLoading=!0;const e=yield this._okr.getOkrForOrgData();console.log(e),this.organizations=e||[],this.isOrgLoading=!1}catch(e){console.error("Error in getOkrForOrgData:",e),this.isOrgLoading=!1}}))}setParentNode(){var e,t,i,a,r,n;return Object(d.c)(this,void 0,void 0,(function*(){this.nodes=[{org_code:null===(e=this.userOrgObj)||void 0===e?void 0:e.org_code,name:null===(t=this.userOrgObj)||void 0===t?void 0:t.org_name,isActive:!0,title:yield this.getActualProg(null===(i=this.userOrgObj)||void 0===i?void 0:i.org_code,this.fyYear),childs:[]}],this.selectionHistory=[{org_code:null===(a=this.userOrgObj)||void 0===a?void 0:a.org_code,name:null===(r=this.userOrgObj)||void 0===r?void 0:r.org_name,isActive:!0,title:yield this.getActualProg(null===(n=this.userOrgObj)||void 0===n?void 0:n.org_code,this.fyYear)}]}))}moveRight(){this.nodes[0].childs=[];let e=this.childOrgList.slice(this.lastActiveIndex+1,this.lastActiveIndex+7);this.nodes[0].childs.push(...e),this.lastActiveIndex=this.lastActiveIndex+e.length}moveLeft(){let e,t=this.lastActiveIndex-this.nodes[0].childs.length;this.nodes[0].childs=[],e=t-6>0?this.childOrgList.slice(t-5,t+1):this.childOrgList.slice(0,6),this.nodes[0].childs.push(...e),this.lastActiveIndex=t}setNodeActive(e){console.log("Set node active"),console.log(e),1==e.length&&(this.nodes[e[0]].isActive=!0),2==e.length&&(this.nodes[e[0]].childs[e[1]].isActive=!0),3==e.length&&(this.nodes[e[0]].childs[e[1]].childs[e[2]].isActive=!0),4==e.length&&(this.nodes[e[0]].childs[e[1]].childs[e[2]].childs[e[3]].isActive=!0)}test(e){console.log("test"),console.log(e),this.selectionHistory[this.selectionHistory.length-1].org_code!=e.org_code&&this.selectionHistory.push(e),this.nodes[0].org_code=e.org_code,this.nodes[0].name=e.name,this.nodes[0].title=e.title,this.orgCode=e.org_code,this.orgName=e.name,this.getVisionMission(),this.end=10,this.getInitialChildren(e.org_code,this.fyYear),this.getOkrList(0,10,e.org_code)}selectOKRFOrg(e){console.log(this.orgSelectGroup.value.searchControl,"heeey");const t={code:e.id,name:e.name};this.getSelectedOrg(t),this._okrReport.setSelectedOrg(t)}ngOnInit(){var e,t,i,a,r,n,o,s,l,c,p,h;return Object(d.c)(this,void 0,void 0,(function*(){this.getAllOrgForOKR();let d=this._rolesService.userProfile;console.log("User profile - OKR"),console.log(d),this.userOrgObj=yield this._okr.getUserOrg(),console.log(this.userOrgObj),this.orgCode=null===(e=this.userOrgObj)||void 0===e?void 0:e.org_code,this.orgName=null===(t=this.userOrgObj)||void 0===t?void 0:t.org_name,d.orgCode=null===(i=this.userOrgObj)||void 0===i?void 0:i.org_code,d.orgName=null===(a=this.userOrgObj)||void 0===a?void 0:a.org_name,d.userLeveL=(null===(r=this.userOrgObj)||void 0===r?void 0:r.user_level)?null===(n=this.userOrgObj)||void 0===n?void 0:n.user_level:"user",this._okr.setUserProfile(d),this.setParentNode(),this.nodes=[{org_code:null===(o=this.userOrgObj)||void 0===o?void 0:o.org_code,name:null===(s=this.userOrgObj)||void 0===s?void 0:s.org_name,isActive:!0,title:yield this.getActualProg(null===(l=this.userOrgObj)||void 0===l?void 0:l.org_code,this.fyYear),childs:[]}],this.selectionHistory=[{org_code:null===(c=this.userOrgObj)||void 0===c?void 0:c.org_code,name:null===(p=this.userOrgObj)||void 0===p?void 0:p.org_name,isActive:!0,title:yield this.getActualProg(null===(h=this.userOrgObj)||void 0===h?void 0:h.org_code,this.fyYear)}];const u=localStorage.getItem("displayFullOrgName");u&&(this.displayFullOrgNameCheck=JSON.parse(u),this._chart.setDisplayFullOrgName(this.displayFullOrgNameCheck)),this._chart.getMsg().subscribe(e=>{console.log(e),this.setNodeActive(e)},e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)}),this.getOkrOrgList(),this.getOkrList(0,10,this.nodes[0].org_code);let m=new Date;this.fyYear=m.getMonth()<=2?m.getFullYear()+"":m.getFullYear()+1+"",this.getVisionMission(),this.checkAdminAccess()}))}getActualProg(e,t){return new Promise((i,a)=>{this._okr.getAcutualProg(e,t).subscribe(e=>{i(null!=e.data.act_prog_val?e.data.act_prog_val:null)},e=>{a(e),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})})}scrollDown(){this.start=0,this.end=this.end+10,this._okr.getOKrList(this.start,this.end,this.nodes[0].org_code).subscribe(e=>{this.okrList=e.data,this.isLoading=!1},e=>{console.error(e),this.isLoading=!1,this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}getSelectedOrg(e){return Object(d.c)(this,void 0,void 0,(function*(){this.getActualProg(e.code,this.fyYear).then(t=>{e&&(this.selectionHistory[this.selectionHistory.length-1].org_code!=e.code&&this.selectionHistory.push({org_code:e.code,name:e.name,title:t,isActive:!0}),this.nodes[0].org_code=e.code,this.nodes[0].name=e.name,this.nodes[0].title=t,this.orgCode=e.code,this.orgName=e.name,this.getVisionMission(),this.end=10,this.getInitialChildren(e.code,this.fyYear),this.getOkrList(0,10,e.code))}).catch(e=>{})}))}undoLastStep(){return Object(d.c)(this,void 0,void 0,(function*(){this.selectionHistory.pop(),this.nodes[0].org_code=this.selectionHistory[this.selectionHistory.length-1].org_code,this.nodes[0].name=this.selectionHistory[this.selectionHistory.length-1].name,this.nodes[0].title=this.selectionHistory[this.selectionHistory.length-1].title,this.orgCode=this.selectionHistory[this.selectionHistory.length-1].org_code,this.orgName=this.selectionHistory[this.selectionHistory.length-1].name,this.orgSelectGroup.patchValue({searchControl:this.orgCode}),this.getVisionMission(),console.log("finnn year",this.fyYear),this.getInitialChildren(this.nodes[0].org_code,this.fyYear),this.end=10,this.getOkrList(0,10,this.nodes[0].org_code)}))}gotoPreviousOrg(){return Object(d.c)(this,void 0,void 0,(function*(){let e=yield this._okr.getParentOrg(this.nodes[0].org_code);if(console.log(e),e){let t=yield this.getActualProg(e.code,this.fyYear);this.selectionHistory[this.selectionHistory.length-1].org_code!=e.code&&this.selectionHistory.push({org_code:e.code,name:e.name,title:t,isActive:!0}),this.nodes[0].org_code=e.code,this.nodes[0].name=e.name,this.nodes[0].title=t,this.orgCode=e.code,this.orgName=e.name,this.orgSelectGroup.patchValue({searchControl:this.orgCode}),this.getVisionMission(),console.log("finnn year",this.fyYear),this.getInitialChildren(this.nodes[0].org_code,this.fyYear),this.end=10,this.getOkrList(0,10,this.nodes[0].org_code)}else this._util.showMessage("Sorry, this is the maximum org level that can be accessed","Dismiss",4e3)}))}get token(){return this._loginService.getToken()}openAddVisionAndMissionDialog(e){return Object(d.c)(this,void 0,void 0,(function*(){const{VisionMissionDialogComponent:t}=yield i.e(330).then(i.bind(null,"Rqfv"));this._dialog.open(t,{width:"700px",height:"350px",autoFocus:!1,data:{isEdit:e,orgCode:this.orgCode,orgName:this.orgName,userOid:this._loginService.getProfile().profile.oid,userName:this._loginService.getProfile().profile.name,title:e?this.visionMissionData[0].title:"",description:e?this.visionMissionData[0].desc:""}}).afterClosed().subscribe(e=>{"success"==e.msg&&this.getVisionMission()})}))}getVisionMission(){let e={orgCode:String(this.orgCode),orgName:this.orgName};this.visionLoading=!0,this.visionMissionData=[],this.subs.sink=this._okr.getVisionMission(e).subscribe(e=>{this.visionLoading=!1,this.visionMissionData=e.data.length>0?e.data:[]},e=>{this.visionMissionData=[],this.visionLoading=!1,this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}deleteVisionAndMission(){this._util.openConfirmationSweetAlertWithCustom("Are you sure you want to Delete this Vision ?","Once you confirm, the Vision will be deleted !").then(e=>Object(d.c)(this,void 0,void 0,(function*(){if(e){this.visionLoading=!0,this.visionMissionData=[];let e={orgCode:String(this.orgCode),orgName:this.orgName};this.subs.sink=this._okr.deleteVisionMission(e).subscribe(e=>{this._util.showMessage("Vision and Mission Deleted Successfully","Dismiss"),this.getVisionMission(),this.visionLoading=!1},e=>{this.visionLoading=!1,this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}})))}viewShowAndHideDialog(){return Object(d.c)(this,void 0,void 0,(function*(){const{ShowHideDialogComponent:e}=yield i.e(318).then(i.bind(null,"58RH"));this._dialog.open(e,{height:"43%",minWidth:"30%",restoreFocus:!1,autoFocus:!1,position:{top:"0px"},data:{view:this.view,orgChartView:this.orgChartView,okrListView:this.okrListView,showHideList:this.showHideList}}).afterClosed().subscribe(e=>{console.log(e),"reset"==e.msg?(this.view=!0,this.orgChartView=!0,this.okrListView=!0,this.showHideList=e.showHideList):"apply"==e.msg&&(this.view=e.view,this.orgChartView=e.orgChartView,this.okrListView=e.okrListView,this.showHideList=e.showHideList,this._okrReport.setShowSummaryCards(e.okrListView))})}))}goToHomePage(){window.open("/main/okr/list/1","_blank")}goToConfiguration(){window.open("/main/okr/okr-org-chart/config","_blank")}checkAdminAccess(){let e={employee_oid:this._loginService.getProfile().profile.oid,configuration_name:"okr_roles",role_name:"Admin"};this.subs.sink=this._okr.checkAdmin(e).subscribe(e=>{this.isAdmin=!!e.data},e=>{this.isAdmin=!1,this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}getChanges(e){return Object(d.c)(this,void 0,void 0,(function*(){console.log("helllooo",e),e.isFromUdrfReport&&(this.fyYear=e.filterValue,this.nodes[0].org_code=Number(e.orgCode),this.nodes[0].name=e.orgName,this.nodes[0].title=yield this.getActualProg(Number(e.orgCode),e.filterValue),this.getInitialChildren(Number(e.orgCode),e.filterValue))}))}openObjPopUp(){return Object(d.c)(this,void 0,void 0,(function*(){const{OkrCreationComponent:e}=yield i.e(117).then(i.bind(null,"cYXB"));this._dialog.open(e,{width:"88%",height:"100%",position:{right:"0px"},autoFocus:!1,maxWidth:"90vw"})}))}openOrgChange(){this._dialog.open(G,{minWidth:"55%",minHeight:"100vh",autoFocus:!1,position:{top:"0",right:"0"}}).afterClosed().subscribe(e=>{})}openChartSettings(e){var t;const i=localStorage.getItem("displayFullOrgName");if(i&&(this.displayFullOrgNameCheck=JSON.parse(i),this._chart.setDisplayFullOrgName(this.displayFullOrgNameCheck)),!(null===(t=this.overlayRef)||void 0===t?void 0:t.hasAttached())){const t=this.overlay.position().flexibleConnectedTo(e).withFlexibleDimensions(!0).withPush(!0).withViewportMargin(25).withGrowAfterOpen(!0).withPositions([{originX:"center",originY:"bottom",overlayX:"center",overlayY:"top"}]),i=this.overlay.scrollStrategies.close();this.overlayRef=this.overlay.create({positionStrategy:t,scrollStrategy:i,hasBackdrop:!0,panelClass:["pop-up"]});const a=new X.h(this.triggerColumnCustomizationTemplateRef,this.viewContainerRef);this.overlayRef.attach(a),this.overlayRef.backdropClick().subscribe(()=>{this.closeOverlay()})}}displayFullOrgName(e){this.displayFullOrgNameCheck=e.checked,localStorage.setItem("displayFullOrgName",JSON.stringify(this.displayFullOrgNameCheck)),this._chart.setDisplayFullOrgName(this.displayFullOrgNameCheck)}closeOverlay(){var e;null===(e=this.overlayRef)||void 0===e||e.dispose()}}return e.\u0275fac=function(t){return new(t||e)(f["\u0275\u0275directiveInject"](pi),f["\u0275\u0275directiveInject"](g.a),f["\u0275\u0275directiveInject"](y.a),f["\u0275\u0275directiveInject"](vi.a),f["\u0275\u0275directiveInject"](bi.a),f["\u0275\u0275directiveInject"](p.b),f["\u0275\u0275directiveInject"](Si.a),f["\u0275\u0275directiveInject"](Ci.a),f["\u0275\u0275directiveInject"](c.i),f["\u0275\u0275directiveInject"](Di.e),f["\u0275\u0275directiveInject"](f.ViewContainerRef))},e.\u0275cmp=f["\u0275\u0275defineComponent"]({type:e,selectors:[["app-chart-home"]],viewQuery:function(e,t){if(1&e&&f["\u0275\u0275viewQuery"](Ki,!0),2&e){let e;f["\u0275\u0275queryRefresh"](e=f["\u0275\u0275loadQuery"]())&&(t.triggerColumnCustomizationTemplateRef=e.first)}},decls:29,vars:11,consts:[[1,"row","pt-2"],[1,"col-10","d-flex","p-0"],[1,"col-2","space","d-flex"],["class","col-11 p-0",4,"ngIf"],["class","col-1 pl-0 space d-flex",4,"ngIf"],[1,"col-2","pl-0","space","d-flex"],[1,"col-11","p-0"],["mat-button","",1,"ml-0","pl-0","header-btn",3,"matTooltip","click"],[1,"header-btn-icon"],[1,"pl-1","header-btn-txt"],[1,"col-1","icon-vl"],[4,"okrAuth"],["class","col-2 pl-0 space d-flex",4,"ngIf"],["class","row  vision-containter",3,"ngClass",4,"ngIf"],["class","row pt-3",4,"ngIf"],[1,"row"],["class","col-12 pl-2 pr-2",4,"ngIf"],[3,"formGroup"],["formControlName","searchControl","placeholder","Search Org",1,"create-account-field","title",3,"list","change"],[1,"col-1","pl-0","space","d-flex"],[1,"col-11","p-0",2,"display","flex","margin-top","-6%"],["mat-button","",1,"header-btn",3,"matTooltip","click"],[1,"header-btn-icon",2,"font-size","18px"],[1,"row","vision-containter",3,"ngClass"],[1,"col-12","p-0","d-flex"],["class","col-11 pr-0 slide-in-top","style","padding-left: 6.1em;",4,"ngIf"],["class","col-11 pl-0 pt-0 pr-0 d-flex","style","padding-left: 2em;",4,"ngIf"],[1,"col-1","p-0","d-flex","justify-content-end"],["mat-icon-button","","aria-label","Example icon-button with a menu",1,"menu-btn",3,"matMenuTriggerFor"],[1,"menu-btn-icon"],["menu","matMenu"],["mat-menu-item","",3,"disabled","click"],["class","col-12 d-flex justify-content-center",4,"ngIf"],[1,"col-11","pr-0","slide-in-top",2,"padding-left","6.1em"],[1,"row","add-vision"],[1,"col-3",2,"text-align","center"],["src"," https://assets.kebs.app/images/novision.png","height","190","width","220",1,"slide-in-top"],[1,"col-3","vision-text","slide-in-top"],[1,"row","pt-3","add-vision"],[1,"col-3","slide-from-down",2,"text-align","center"],["mat-raised-button","",1,"ml-2","content-btn",3,"disabled","click"],[1,"col-11","pl-0","pt-0","pr-0","d-flex",2,"padding-left","2em"],[1,"col-2","p-0","d-flex"],[1,"col-12","p-0","d-flex","justify-content-center",2,"text-align","left"],[1,"col-12","pl-0","pt-2","slide-from-down"],[1,"vision-heading"],[1,"col-1","pl-0","pr-0","d-flex","justify-content-center"],[2,"border","1px solid #E8E9EE","height","5em","margin-top","0.5em"],[1,"col-9","p-0"],[4,"ngFor","ngForOf"],[1,"col-11","pl-0","pt-2","slide-in-top"],[1,"vision-title",3,"ngClass"],["href","javascript:;","class","read-more",3,"click",4,"ngIf"],["class","row",4,"ngIf"],["href","javascript:;",1,"read-more",3,"click"],[1,"vision-description"],["href","javascript:;","class","pl-2 show-less",3,"click",4,"ngIf"],["href","javascript:;",1,"pl-2","show-less",3,"click"],[1,"col-12","d-flex","justify-content-center"],["role","status",1,"spinner-border","text-danger"],[1,"sr-only"],[1,"row","pt-3"],[1,"col-12","d-flex","justify-content-center","align-items-center"],[1,"org-heading"],["cdkOverlayOrigin","",1,"chart-setting-btn"],["triggerColumnCustomization","cdkOverlayOrigin","triggerColumnCustomizationField",""],["mat-icon-button","",3,"matTooltip","click"],["cdkConnectedOverlay","",3,"cdkConnectedOverlayOrigin"],["triggerColumnCustomizationTemplateRef",""],[1,"overlay-content-main"],[1,"overlay-header","pt-1"],[1,"overlay-content"],[1,"org-name-text"],[2,"margin","0",3,"checked","change"],[1,"col-1","pl-4","navi-col"],["mat-mini-fab","","class","navigation-btn",3,"disabled","click",4,"ngIf"],[1,"col-10","p-0"],[1,"p-0","btn-undo","d-flex","flex-row"],["mat-icon-button","",3,"matTooltip","disabled","click"],["mat-icon-button","",1,"ml-2",3,"matTooltip","disabled","click"],["direction","vertical",1,"mt-5",3,"nodes","itemClick"],[1,"col-1","navi-col"],["mat-mini-fab","",1,"navigation-btn",3,"disabled","click"],[1,"col-12","pl-2","pr-2"],[3,"orgCode","orgName","isFromOrgChart","fyYear","showSummaryCards","evaluatorResponse"]],template:function(e,t){1&e&&(f["\u0275\u0275elementStart"](0,"div",0),f["\u0275\u0275elementStart"](1,"div",1),f["\u0275\u0275elementStart"](2,"div",2),f["\u0275\u0275template"](3,Li,3,2,"div",3),f["\u0275\u0275elementEnd"](),f["\u0275\u0275template"](4,Hi,6,1,"div",4),f["\u0275\u0275elementStart"](5,"div",5),f["\u0275\u0275elementStart"](6,"div",6),f["\u0275\u0275elementStart"](7,"button",7),f["\u0275\u0275listener"]("click",(function(){return t.viewShowAndHideDialog()})),f["\u0275\u0275elementStart"](8,"mat-icon",8),f["\u0275\u0275text"](9,"visibility_off"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](10,"span",9),f["\u0275\u0275text"](11,"Show/Hide"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275element"](12,"div",10),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](13,"div",5),f["\u0275\u0275elementStart"](14,"div",6),f["\u0275\u0275elementStart"](15,"button",7),f["\u0275\u0275listener"]("click",(function(){return t.goToHomePage()})),f["\u0275\u0275elementStart"](16,"mat-icon",8),f["\u0275\u0275text"](17,"home"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](18,"span",9),f["\u0275\u0275text"](19,"Home Page"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275element"](20,"div",10),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](21,"div",5),f["\u0275\u0275template"](22,Yi,8,1,"ng-container",11),f["\u0275\u0275elementEnd"](),f["\u0275\u0275template"](23,zi,8,1,"div",12),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275template"](24,$i,17,7,"div",13),f["\u0275\u0275template"](25,ea,12,2,"div",14),f["\u0275\u0275template"](26,aa,14,7,"div",14),f["\u0275\u0275elementStart"](27,"div",15),f["\u0275\u0275template"](28,ra,2,5,"div",16),f["\u0275\u0275elementEnd"]()),2&e&&(f["\u0275\u0275advance"](3),f["\u0275\u0275property"]("ngIf",t.isAdmin),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",t.isAdmin),f["\u0275\u0275advance"](3),f["\u0275\u0275property"]("matTooltip","Show/Hide"),f["\u0275\u0275advance"](8),f["\u0275\u0275property"]("matTooltip","Go To Home Page"),f["\u0275\u0275advance"](7),f["\u0275\u0275property"]("okrAuth",f["\u0275\u0275pureFunction0"](10,na)),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",t.isAdmin&&t.disable),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",t.view),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",t.orgChartView),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",t.orgChartView),f["\u0275\u0275advance"](2),f["\u0275\u0275property"]("ngIf",""!=t.orgCode&&0==t.isLoading))},directives:[a.NgIf,b.a,n.a,S.a,Oi.a,c.J,c.w,c.n,_i.a,c.v,c.l,a.NgClass,o.f,o.g,o.d,a.NgForOf,Di.b,Di.a,Ti.a,gi,Ui],styles:["@import url(https://fonts.googleapis.com/css?family=DM+Sans);.navigation-btn[_ngcontent-%COMP%]{height:25px;width:25px;background-color:#cf0001;color:#fff}.navigation-btn[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%]{height:15px;width:25px;font-size:23px;line-height:0}.navi-col[_ngcontent-%COMP%]{margin-top:7rem;padding-left:4.2em}.btn-undo[_ngcontent-%COMP%]{position:absolute;left:22rem;top:9px}.vision-containter[_ngcontent-%COMP%]{border:1px solid #e8e9ee;box-sizing:border-box;border-radius:8px;margin:.5em 15px 4px!important}.vision-containter[_ngcontent-%COMP%]   .add-vision[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center}.vision-containter[_ngcontent-%COMP%]   .add-vision[_ngcontent-%COMP%]   .vision-text[_ngcontent-%COMP%]{font-family:DM Sans;color:#6e7b8f;font-weight:400;font-style:normal;text-align:center;letter-spacing:2%;line-height:16px;font-size:13px}.vision-containter[_ngcontent-%COMP%]   .add-vision[_ngcontent-%COMP%]   .not-btn[_ngcontent-%COMP%]{font-size:13px;color:#45546e;font-family:DM Sans;font-weight:700!important;font-style:normal}.vision-containter[_ngcontent-%COMP%]   .add-vision[_ngcontent-%COMP%]   .content-btn[_ngcontent-%COMP%]{font-size:13px;color:#fff;border-radius:8px;background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%)}.vision-containter[_ngcontent-%COMP%]   .add-vision[_ngcontent-%COMP%]   .content-btn[_ngcontent-%COMP%], .vision-containter[_ngcontent-%COMP%]   .vision-heading[_ngcontent-%COMP%]{font-family:DM Sans;font-weight:700!important;font-style:normal;text-transform:capitalize}.vision-containter[_ngcontent-%COMP%]   .vision-heading[_ngcontent-%COMP%]{color:#45546e;line-height:30px;font-size:30px}.vision-containter[_ngcontent-%COMP%]   .vision-title[_ngcontent-%COMP%]{font-family:DM Sans;color:#f27a6c;font-weight:500!important;font-style:normal;text-transform:capitalize;line-height:24px;font-size:16px;text-align:left}.vision-containter[_ngcontent-%COMP%]   .overflow[_ngcontent-%COMP%]{overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.vision-containter[_ngcontent-%COMP%]   .read-more[_ngcontent-%COMP%]{color:#000}.vision-containter[_ngcontent-%COMP%]   .show-less[_ngcontent-%COMP%]{color:#f27a6c}.vision-containter[_ngcontent-%COMP%]   .vision-description[_ngcontent-%COMP%]{font-family:DM Sans;color:#6e7b8f;font-weight:400!important;font-style:normal;text-transform:capitalize;line-height:24px;font-size:14px;text-align:left}.vision-containter[_ngcontent-%COMP%]   .menu-btn[_ngcontent-%COMP%]{border:1.5px solid #a0a1a5;border-radius:8px;box-sizing:border-box;width:25px;height:20px}.vision-containter[_ngcontent-%COMP%]   .menu-btn-icon[_ngcontent-%COMP%]{font-size:15px;line-height:2px;color:#e8e9ee}.vision-containter[_ngcontent-%COMP%]   .menu-btn[_ngcontent-%COMP%]:hover{border-color:grey}.vision-containter[_ngcontent-%COMP%]   .menu-btn-icon[_ngcontent-%COMP%]:hover{color:grey}.header-btn[_ngcontent-%COMP%]   .header-btn-icon[_ngcontent-%COMP%]{color:#66615b;font-size:21px;line-height:22px}.header-btn[_ngcontent-%COMP%]   .header-btn-txt[_ngcontent-%COMP%]{color:#6b7a99;font-size:13px;font-weight:400;font-family:DM Sans;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;line-height:3.8em}.space[_ngcontent-%COMP%]{padding-right:20px;padding-left:20px}.vl[_ngcontent-%COMP%]{border-right:1px solid #e8e9ee}.icon-vl[_ngcontent-%COMP%], .vl[_ngcontent-%COMP%]{height:40px;margin-top:.5em}.icon-vl[_ngcontent-%COMP%]{border-left:1px solid #e8e9ee}.org-heading[_ngcontent-%COMP%]{font-family:DM Sans;color:#45546e;font-weight:500!important;font-style:normal;text-transform:capitalize;line-height:16px;font-size:14px;text-align:center;position:absolute}.chart-setting-btn[_ngcontent-%COMP%]{margin-left:70%}.chart-setting-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#45546e;font-size:21px}.slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}.slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}.infinite-scroll-auto[_ngcontent-%COMP%]{height:70vh;overflow-y:scroll}.overlay-content-main[_ngcontent-%COMP%]{width:15rem;height:auto;background:#fff;border:1px solid #ccc;box-shadow:0 2px 10px rgba(0,0,0,.2);padding:10px;border-radius:5px}.overlay-content-main[_ngcontent-%COMP%]   .overlay-header[_ngcontent-%COMP%]{font-family:DM Sans;color:#45546e;font-weight:500!important;font-style:normal;text-transform:capitalize;line-height:16px;font-size:13px;text-align:center;display:flex;justify-content:center}.overlay-content-main[_ngcontent-%COMP%]   .overlay-content[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:4% 1% 0}.overlay-content-main[_ngcontent-%COMP%]   .overlay-content[_ngcontent-%COMP%]   .org-name-text[_ngcontent-%COMP%]{font-family:DM Sans;color:#45546e;font-weight:500!important;font-style:normal;text-transform:capitalize;line-height:16px;font-size:13px;text-align:center}.overlay-content-main[_ngcontent-%COMP%]   .overlay-content[_ngcontent-%COMP%]     .mat-slide-toggle .mat-slide-toggle-bar{width:25px}.overlay-content-main[_ngcontent-%COMP%]   .overlay-content[_ngcontent-%COMP%]     .mat-slide-toggle .mat-slide-toggle-thumb{background-color:#fff;width:10px;height:10px;margin-left:4px;margin-top:5px}.overlay-content-main[_ngcontent-%COMP%]   .overlay-content[_ngcontent-%COMP%]     .mat-slide-toggle.mat-checked .mat-slide-toggle-bar{background-color:#79ba44;width:25px}.overlay-content-main[_ngcontent-%COMP%]   .overlay-content[_ngcontent-%COMP%]     .mat-slide-toggle.mat-checked .mat-slide-toggle-thumb{background-color:#fff;width:10px;height:10px;margin-left:-4px;margin-top:5px}"]}),e})();var sa=i("jaxi");let da=(()=>{class e{constructor(e,t,i,a){this.dialogRef=e,this._ErrorService=t,this._util=i,this._okr=a}ngOnInit(){this._okr.getOpenNewTabConfig().subscribe(e=>{this.ackToggleVal=e.data[0].configuration_data},e=>{console.log(e),this._ErrorService.userErrorAlert(e.error.code,"Error while getting Default Display Module",e.error.errMessage)})}onAckToggle(e){let t=JSON.parse(e);this._okr.ChangeOpenNewTabConfig({configName:"enableOpenNewTab",configData:t}).subscribe(e=>{this._util.showMessage("New tab config Changed!","Dismiss")},e=>{console.log(e),this._ErrorService.userErrorAlert(e.error.code,"Error while changing Star rating config",e.error.errMessage)})}closeDialog(){this.dialogRef.close()}}return e.\u0275fac=function(t){return new(t||e)(f["\u0275\u0275directiveInject"](p.h),f["\u0275\u0275directiveInject"](vi.a),f["\u0275\u0275directiveInject"](Si.a),f["\u0275\u0275directiveInject"](g.a))},e.\u0275cmp=f["\u0275\u0275defineComponent"]({type:e,selectors:[["app-open-new-tab-config"]],decls:21,vars:3,consts:[[1,"container-fluid","acknowledgebtn-styles"],[1,"row","p-0"],[1,"col-12","d-flex","p-0","pt-2"],[1,"col-10","d-flex","p-0"],[1,"pl-2","pt-2"],[1,"title"],[1,"col-1","p-0"],["mat-icon-button","","matTooltip","Close",3,"click"],[1,"close-Icon"],[1,"row","rowStyle"],[1,"col-7","divClass"],[1,"header-btn","headerWidth"],[1,"col-5","toggleBtnStyle"],[3,"value","change"],[3,"value"]],template:function(e,t){1&e&&(f["\u0275\u0275elementStart"](0,"div",0),f["\u0275\u0275elementStart"](1,"div",1),f["\u0275\u0275elementStart"](2,"div",2),f["\u0275\u0275elementStart"](3,"div",3),f["\u0275\u0275elementStart"](4,"div",4),f["\u0275\u0275elementStart"](5,"span",5),f["\u0275\u0275text"](6,"Change Okr Open New Tab Config"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](7,"div",6),f["\u0275\u0275elementStart"](8,"button",7),f["\u0275\u0275listener"]("click",(function(){return t.closeDialog()})),f["\u0275\u0275elementStart"](9,"mat-icon",8),f["\u0275\u0275text"](10,"close"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](11,"div",9),f["\u0275\u0275elementStart"](12,"div",10),f["\u0275\u0275elementStart"](13,"h4",11),f["\u0275\u0275text"](14,"Open in New Tab"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](15,"div",12),f["\u0275\u0275elementStart"](16,"mat-button-toggle-group",13),f["\u0275\u0275listener"]("change",(function(e){return t.onAckToggle(e.value)})),f["\u0275\u0275elementStart"](17,"mat-button-toggle",14),f["\u0275\u0275text"](18,"False"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](19,"mat-button-toggle",14),f["\u0275\u0275text"](20,"True"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]()),2&e&&(f["\u0275\u0275advance"](16),f["\u0275\u0275property"]("value",t.ackToggleVal),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("value",!1),f["\u0275\u0275advance"](2),f["\u0275\u0275property"]("value",!0))},directives:[b.a,n.a,S.a,sa.b,sa.a],styles:[".customClass[_ngcontent-%COMP%]{padding-left:19px}  .mat-button-toggle-checked{background-color:#c92020!important;color:#fff!important}.toggleBtnStyle[_ngcontent-%COMP%]{margin-left:41px}.toggleBtnStyle1[_ngcontent-%COMP%]{margin-left:72px}.headerWidth[_ngcontent-%COMP%]{width:150px}.headerWidth1[_ngcontent-%COMP%]{width:184px}.list_employee_card[_ngcontent-%COMP%]{cursor:pointer}.example-viewport[_ngcontent-%COMP%]{height:70vh;width:100%}.spinner-align[_ngcontent-%COMP%]{margin:auto;display:inline-block}.bold-info[_ngcontent-%COMP%]{font-weight:500;font-size:large;padding-top:4rem}.mat-cell[_ngcontent-%COMP%]{font-family:Roboto,Segoe UI,Oxygen,Ubuntu,Cantarell,Open Sans,Helvetica Neue,sans-serif;font-size:14px;text-align:left!important;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.header-btn[_ngcontent-%COMP%]{color:#cf0001;display:block;width:130%;margin:5%;text-align:center}.card-tile[_ngcontent-%COMP%]{width:100%;display:flex;flex-direction:row;flex-wrap:wrap}.cardView_matCard[_ngcontent-%COMP%]{margin-top:1rem;margin-right:2rem;cursor:pointer}.col-2[_ngcontent-%COMP%]{margin-left:30px}"]}),e})();function la(e,t){1&e&&(f["\u0275\u0275elementStart"](0,"div"),f["\u0275\u0275text"](1,"Name is required. "),f["\u0275\u0275elementEnd"]())}function ca(e,t){if(1&e&&(f["\u0275\u0275elementStart"](0,"div"),f["\u0275\u0275template"](1,la,2,0,"div",10),f["\u0275\u0275elementEnd"]()),2&e){const e=f["\u0275\u0275nextContext"]();f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",e.descriptionControl.value&&e.descriptionControl.errors.required)}}function pa(e,t){1&e&&(f["\u0275\u0275elementStart"](0,"div"),f["\u0275\u0275text"](1,"Start date is required."),f["\u0275\u0275elementEnd"]())}function ha(e,t){1&e&&(f["\u0275\u0275elementStart"](0,"div"),f["\u0275\u0275text"](1,"Start and end date range cannot exceed 1 year."),f["\u0275\u0275elementEnd"]())}function ua(e,t){if(1&e&&(f["\u0275\u0275elementStart"](0,"div"),f["\u0275\u0275template"](1,pa,2,0,"div",10),f["\u0275\u0275template"](2,ha,2,0,"div",10),f["\u0275\u0275elementEnd"]()),2&e){const e=f["\u0275\u0275nextContext"]();f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",e.startDate.errors.required),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",e.startDate.errors.dateRange)}}function ma(e,t){1&e&&(f["\u0275\u0275elementStart"](0,"div"),f["\u0275\u0275text"](1,"End date is required."),f["\u0275\u0275elementEnd"]())}function fa(e,t){1&e&&(f["\u0275\u0275elementStart"](0,"div"),f["\u0275\u0275text"](1,"Start and end date range cannot exceed 1 year."),f["\u0275\u0275elementEnd"]())}function ga(e,t){if(1&e&&(f["\u0275\u0275elementStart"](0,"div"),f["\u0275\u0275template"](1,ma,2,0,"div",10),f["\u0275\u0275template"](2,fa,2,0,"div",10),f["\u0275\u0275elementEnd"]()),2&e){const e=f["\u0275\u0275nextContext"]();f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",e.endDate.errors.required),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",e.endDate.errors.dateRange)}}function ya(e,t){1&e&&(f["\u0275\u0275elementStart"](0,"div"),f["\u0275\u0275text"](1,"Name is required. "),f["\u0275\u0275elementEnd"]())}function va(e,t){if(1&e&&(f["\u0275\u0275elementStart"](0,"div"),f["\u0275\u0275template"](1,ya,2,0,"div",10),f["\u0275\u0275elementEnd"]()),2&e){const e=f["\u0275\u0275nextContext"]();f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",e.fyrControl.value&&e.fyrControl.errors.required)}}let ba=(()=>{class e{constructor(e,t,i,a){this.fb=e,this._okr=t,this.dialogRef=i,this._util=a}ngOnInit(){this.formGroup=this.fb.group({startDate:[new Date,c.H.required],endDate:[new Date,c.H.required],type:["",c.H.required],fyr:["",c.H.required]})}get startDate(){return this.formGroup.get("startDate")}get endDate(){return this.formGroup.get("endDate")}get descriptionControl(){return this.formGroup.get("type")}get fyrControl(){return this.formGroup.get("fyr")}get minDate(){return new Date(this.startDate.value.getFullYear(),0,1)}get maxDate(){return new Date(this.startDate.value.getFullYear()+1,0,0)}addFYConfig(){return Object(d.c)(this,void 0,void 0,(function*(){if(console.log(this.formGroup.value),this.formGroup.value){let e=yield this._okr.addFYConfig(this.formGroup.value);return this._util.showMessage("FY Config added successfully","Dismiss",3e3),this.dialogRef.close(),e}}))}getFYConfig(){return Object(d.c)(this,void 0,void 0,(function*(){let e=yield this._okr.getFYConfig();console.log(e)}))}}return e.\u0275fac=function(t){return new(t||e)(f["\u0275\u0275directiveInject"](c.i),f["\u0275\u0275directiveInject"](g.a),f["\u0275\u0275directiveInject"](p.h),f["\u0275\u0275directiveInject"](Si.a))},e.\u0275cmp=f["\u0275\u0275defineComponent"]({type:e,selectors:[["app-okr-add-fy"]],decls:41,vars:10,consts:[[1,"okr-add-fy-main","container-fluid","p-2"],[1,"row","d-flex","flex-row","px-3","py-1"],[1,"title"],["mat-icon-button","",1,"ml-auto",3,"click"],[1,"fy-config-form"],[3,"formGroup"],[1,"row","my-3"],[1,"col-4"],[1,"col-8"],["type","text","id","name","formControlName","type","placeholder","FY 2023"],[4,"ngIf"],["type","date","id","startDate","formControlName","startDate",3,"min","max"],["type","date","id","endDate","formControlName","endDate",3,"disabled","min","max"],["type","text","id","name","formControlName","fyr","placeholder","2023"],[1,"row","mt-4","mb-2"],["mat-flat-button","",1,"submit-btn",3,"click"]],template:function(e,t){1&e&&(f["\u0275\u0275elementStart"](0,"div",0),f["\u0275\u0275elementStart"](1,"div",1),f["\u0275\u0275elementStart"](2,"span",2),f["\u0275\u0275text"](3,"Add FY Configuration"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](4,"button",3),f["\u0275\u0275listener"]("click",(function(){return t.dialogRef.close()})),f["\u0275\u0275elementStart"](5,"mat-icon"),f["\u0275\u0275text"](6,"close"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](7,"div",4),f["\u0275\u0275elementStart"](8,"form",5),f["\u0275\u0275elementStart"](9,"div",6),f["\u0275\u0275elementStart"](10,"div",7),f["\u0275\u0275elementStart"](11,"span"),f["\u0275\u0275text"](12,"FY Name : "),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](13,"div",8),f["\u0275\u0275element"](14,"input",9),f["\u0275\u0275template"](15,ca,2,1,"div",10),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](16,"div",6),f["\u0275\u0275elementStart"](17,"div",7),f["\u0275\u0275elementStart"](18,"span"),f["\u0275\u0275text"](19,"Start Date : "),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](20,"div",8),f["\u0275\u0275element"](21,"input",11),f["\u0275\u0275template"](22,ua,3,2,"div",10),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](23,"div",6),f["\u0275\u0275elementStart"](24,"div",7),f["\u0275\u0275elementStart"](25,"span"),f["\u0275\u0275text"](26,"End Date : "),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](27,"div",8),f["\u0275\u0275element"](28,"input",12),f["\u0275\u0275template"](29,ga,3,2,"div",10),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](30,"div",6),f["\u0275\u0275elementStart"](31,"div",7),f["\u0275\u0275elementStart"](32,"span"),f["\u0275\u0275text"](33,"Financial Year : "),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](34,"div",8),f["\u0275\u0275element"](35,"input",13),f["\u0275\u0275template"](36,va,2,1,"div",10),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](37,"div",14),f["\u0275\u0275elementStart"](38,"div",7),f["\u0275\u0275elementStart"](39,"button",15),f["\u0275\u0275listener"]("click",(function(){return t.addFYConfig()})),f["\u0275\u0275text"](40,"Submit"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]()),2&e&&(f["\u0275\u0275advance"](8),f["\u0275\u0275property"]("formGroup",t.formGroup),f["\u0275\u0275advance"](7),f["\u0275\u0275property"]("ngIf",t.descriptionControl.value&&t.descriptionControl.invalid),f["\u0275\u0275advance"](6),f["\u0275\u0275propertyInterpolate"]("min",t.minDate),f["\u0275\u0275propertyInterpolate"]("max",t.maxDate),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",t.startDate.invalid),f["\u0275\u0275advance"](6),f["\u0275\u0275propertyInterpolate"]("min",t.minDate),f["\u0275\u0275propertyInterpolate"]("max",t.maxDate),f["\u0275\u0275property"]("disabled",!t.startDate.valid),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",t.endDate.invalid),f["\u0275\u0275advance"](7),f["\u0275\u0275property"]("ngIf",t.fyrControl.value&&t.fyrControl.invalid))},directives:[b.a,S.a,c.J,c.w,c.n,c.e,c.v,c.l,a.NgIf],styles:[".okr-add-fy-main[_ngcontent-%COMP%]   .fy-config-form[_ngcontent-%COMP%]{border:1px solid #ddd;border-radius:4px;padding:20px;margin:20px;background-color:#fafafa}.okr-add-fy-main[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:18px;font-weight:550}.okr-add-fy-main[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]{font-weight:700;display:block;margin-bottom:5px}.okr-add-fy-main[_ngcontent-%COMP%]   .input[_ngcontent-%COMP%]{width:100%;padding:5px;border:1px solid #ccc;border-radius:3px}.okr-add-fy-main[_ngcontent-%COMP%]   .input[_ngcontent-%COMP%]:invalid{border-color:red}.okr-add-fy-main[_ngcontent-%COMP%]   .error-message[_ngcontent-%COMP%]{color:red;font-size:12px}.okr-add-fy-main[_ngcontent-%COMP%]   .submit-btn[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff}"]}),e})();const Sa=[{path:"",component:oa},{path:"config",component:(()=>{class e{constructor(e){this.dialog=e}ngOnInit(){}changeRatingConfig(){this.dialog.open(da,{autoFocus:!1}).afterClosed().subscribe(e=>{console.log("The dialog was closed")})}addFYConfig(){this.dialog.open(ba,{autoFocus:!1,minWidth:"40%",minHeight:"40%"}).afterClosed().subscribe(e=>{console.log("The dialog was closed")})}}return e.\u0275fac=function(t){return new(t||e)(f["\u0275\u0275directiveInject"](p.b))},e.\u0275cmp=f["\u0275\u0275defineComponent"]({type:e,selectors:[["app-chart-configuration"]],decls:7,vars:0,consts:[[1,"row","pb-3","d-flex"],[1,"col-2","alignDown"],["mat-stroked-button","",1,"header-btn",3,"click"]],template:function(e,t){1&e&&(f["\u0275\u0275elementStart"](0,"div",0),f["\u0275\u0275elementStart"](1,"div",1),f["\u0275\u0275elementStart"](2,"button",2),f["\u0275\u0275listener"]("click",(function(){return t.changeRatingConfig()})),f["\u0275\u0275text"](3," Change Star rating config "),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](4,"div",1),f["\u0275\u0275elementStart"](5,"button",2),f["\u0275\u0275listener"]("click",(function(){return t.addFYConfig()})),f["\u0275\u0275text"](6," Add new FY Config "),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]())},directives:[b.a],styles:[".alignDown[_ngcontent-%COMP%]{margin-top:3%;margin-left:1%}"]}),e})()}];let Ca=(()=>{class e{}return e.\u0275mod=f["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=f["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[s.k.forChild(Sa)],s.k]}),e})();var Da=i("7EHt"),Oa=i("bv9b"),_a=i("Xi0T"),Ta=i("5RNC"),xa=i("3PA3"),wa=i("Zg/p"),Aa=i("FsDe"),ka=i("5+WD"),Ea=i("JqCM"),Ma=i("wZkO"),Ia=i("dlKe"),Pa=i("lVl8"),ja=i("mEBv"),Ra=i("ljNA"),Na=i("vVoB");i("9op9");const Va={width:200,"max-width":220},Fa={width:"auto",height:"auto",showDelay:500,hideDelay:300,trigger:"click"};let Ua=(()=>{class e{}return e.\u0275mod=f["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=f["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[a.CommonModule,Ca,yi,Oa.b,wa.a,Ta.b,Di.h,xa.a,p.g,b.b,Aa.b,S.b,o.e,r.b,n.b,_a.a,Da.b,j.c,T.e,x.d,c.E,ka.g,P.b,Ti.b,sa.c,Ea.b,Ma.g,Ia.b,ja.b.forRoot(Fa),Pa.b.forRoot(Va),Ra.KrDetailModule,Na.a,N.c,c.p,R.b,A.b]]}),e})()},HmYF:function(e,t,i){"use strict";i.d(t,"a",(function(){return l}));var a=i("mrSG"),r=i("Iab2"),n=i("EUZL"),o=i("wd/R"),s=i("xG9w"),d=i("fXoL");let l=(()=>{class e{constructor(){this.formatColumn=(e,t,i)=>{const a=n.utils.decode_range(e["!ref"]);for(let r=a.s.r+1;r<=a.e.r;++r){const a=n.utils.encode_cell({r:r,c:t});e[a]&&e[a].v&&(e[a].t="d",e[a].z=i)}}}exportAsExcelFile(e,t,i,a,r){console.log("Excel to JSON Service",e);const o=n.utils.json_to_sheet(e);if(r&&r.length){const e=n.utils.sheet_to_json(o,{header:1}).shift();for(const t of r){const i=e.indexOf(t.fieldKey);this.formatColumn(o,i,t.fieldFormat)}}null==i&&(i=[]),null==a&&(a="DD-MM-YYYY"),this.formatExcelDateData(o,i,a);const s=n.write({Sheets:{data:o},SheetNames:["data"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(s,t)}formatExcelDateData(e,t,i){for(let n of Object.keys(e))if(null!=e[n]&&null!=e[n].t&&null!=e[n].v&&o(e[n].v,i,!0).isValid()){let a=n.replace(/[0-9]/g,"")+"1";0==s.where(t,{value:e[a].v}).length&&null!=e[a]&&null!=e[a].t&&t.push({value:e[a].v,format:i})}let a=[],r=1;for(let n of t)for(let t of Object.keys(e)){let i=parseInt(t.replace(/[^0-9]/g,""));i>r&&(r=i),null!=e[t]&&null!=e[t].v&&e[t].v==n.value&&a.push({value:t.replace(/[0-9]/g,""),format:n.format})}for(let n of a)for(let t=2;t<=r;t++)null!=e[n.value+""+t]&&null!=e[n.value+""+t].t&&(e[n.value+""+t].t="d",null!=e[n.value+""+t].v&&"Invalid date"!=e[n.value+""+t].v?e[n.value+""+t].v=o(e[n.value+""+t].v,n.format).format("YYYY/MM/DD"):(console.log(e[n.value+""+t].t),e[n.value+""+t].v="",e[n.value+""+t].t="s"))}saveAsExcelFile(e,t){const i=new Blob([e],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8"});r.saveAs(i,t.concat(".xlsx"))}exportAsExcelFileWFH(e,t,i){const a=n.utils.json_to_sheet(e),r=n.utils.json_to_sheet(t),o=n.write({Sheets:{All_Approvals:a,Pending_Approvals:r},SheetNames:["All_Approvals","Pending_Approvals"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(o,i)}exportAsExcelFileForPayroll(e,t,i,a,r,o){const s=n.utils.json_to_sheet(e),d=n.utils.json_to_sheet(t),l=n.utils.json_to_sheet(i),c=n.utils.json_to_sheet(a),p=n.utils.json_to_sheet(r),h=n.write({Sheets:{Regular_Report:s,Intern_Report:d,Contract_Report:l,Perdiem_Report:c,RP_Report:p},SheetNames:["Regular_Report","Intern_Report","Contract_Report","Perdiem_Report","RP_Report"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(h,o)}exportAsCsvFileWithSheetName(e,t){return Object(a.c)(this,void 0,void 0,(function*(){let i=n.utils.book_new();for(let t of e){let e=n.utils.json_to_sheet(t.data);n.utils.book_append_sheet(i,e,t.sheetName)}let a=n.write(i,{bookType:"xlsx",type:"binary"});yield this.saveAsCsvFile(a,t)}))}saveAsCsvFile(e,t){return Object(a.c)(this,void 0,void 0,(function*(){const i=new Blob([yield this.s2ab(e)],{type:"application/octet-stream"});r.saveAs(i,t.concat(".csv"))}))}s2ab(e){return Object(a.c)(this,void 0,void 0,(function*(){for(var t=new ArrayBuffer(e.length),i=new Uint8Array(t),a=0;a<e.length;a++)i[a]=255&e.charCodeAt(a);return t}))}exportAsExcelFileWithCellMerge(e,t,i){const a=n.utils.json_to_sheet(e);a["!merges"]=i;const r=n.write({Sheets:{data:a},SheetNames:["data"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(r,t)}exportJsonToExcelWithMultipleSheets(e,t){return Object(a.c)(this,void 0,void 0,(function*(){let i=n.utils.book_new();for(let t of e){let e=n.utils.json_to_sheet(t.data);n.utils.book_append_sheet(i,e,t.sheetName)}let a=n.write(i,{bookType:"xlsx",type:"array"});this.saveAsExcelFile(a,t)}))}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275prov=d["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},"TmG/":function(e,t,i){"use strict";i.d(t,"a",(function(){return v}));var a=i("fXoL"),r=i("3Pt+"),n=i("jtHE"),o=i("XNiG"),s=i("NJ67"),d=i("1G5W"),l=i("kmnG"),c=i("ofXK"),p=i("d3UM"),h=i("FKr1"),u=i("WJ5W"),m=i("Qu3c");function f(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"mat-label"),a["\u0275\u0275text"](1),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"]();a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate"](e.placeholder)}}function g(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"mat-option",7),a["\u0275\u0275text"](1),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"]();a["\u0275\u0275property"]("value",null),a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate"](e.disableNone?"Select One":"None")}}function y(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"mat-option",8),a["\u0275\u0275listener"]("click",(function(){a["\u0275\u0275restoreView"](e);const i=t.$implicit;return a["\u0275\u0275nextContext"]().emitChanges(i)})),a["\u0275\u0275text"](1),a["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;a["\u0275\u0275property"]("value",e.id||e._id)("matTooltip",e.name),a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let v=(()=>{class e extends s.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new r.j,this.fieldFilterCtrl=new r.j,this.list=[],this.required=!1,this.disableNone=!1,this.valueChange=new a.EventEmitter,this.disabled=!1,this.hasNoneOption=!0,this.filteredList=new n.a,this.change=new a.EventEmitter,this.hideMatLabel=!1,this._onDestroy=new o.b,this.emitChanges=e=>{this.change.emit(e)}}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(d.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(d.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275directiveInject"](a.Renderer2))},e.\u0275cmp=a["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search"]],inputs:{list:"list",placeholder:"placeholder",required:"required",disableNone:"disableNone",disabled:"disabled",hasNoneOption:"hasNoneOption",hideMatLabel:"hideMatLabel"},outputs:{valueChange:"valueChange",change:"change"},features:[a["\u0275\u0275ProvidersFeature"]([{provide:r.t,useExisting:Object(a.forwardRef)(()=>e),multi:!0}]),a["\u0275\u0275InheritDefinitionFeature"],a["\u0275\u0275NgOnChangesFeature"]],decls:9,vars:11,consts:[["appearance","outline"],[4,"ngIf"],[3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value",4,"ngIf"],[3,"value","matTooltip","click",4,"ngFor","ngForOf"],[3,"value"],[3,"value","matTooltip","click"]],template:function(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"mat-form-field",0),a["\u0275\u0275template"](1,f,2,1,"mat-label",1),a["\u0275\u0275elementStart"](2,"mat-select",2,3),a["\u0275\u0275elementStart"](4,"mat-option"),a["\u0275\u0275element"](5,"ngx-mat-select-search",4),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](6,g,2,2,"mat-option",5),a["\u0275\u0275template"](7,y,2,3,"mat-option",6),a["\u0275\u0275pipe"](8,"async"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e&&(a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",!t.hideMatLabel),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",t.hasNoneOption),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngForOf",a["\u0275\u0275pipeBind1"](8,9,t.filteredList)))},directives:[l.c,c.NgIf,p.c,r.v,r.k,r.F,h.p,u.a,c.NgForOf,l.g,m.a],pipes:[c.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%}"]}),e})()}}]);