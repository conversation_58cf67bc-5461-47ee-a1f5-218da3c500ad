(window.webpackJsonp=window.webpackJsonp||[]).push([[830],{"2Pgj":function(e,t,n){"use strict";var o;n.d(t,"a",(function(){return a}));var i=new Uint8Array(16);function a(){if(!o&&!(o="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||"undefined"!=typeof msCrypto&&"function"==typeof msCrypto.getRandomValues&&msCrypto.getRandomValues.bind(msCrypto)))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return o(i)}},"7Cbv":function(e,t,n){"use strict";var o=n("2Pgj"),i=n("WM9j");t.a=function(e,t,n){var a=(e=e||{}).random||(e.rng||o.a)();if(a[6]=15&a[6]|64,a[8]=63&a[8]|128,t){n=n||0;for(var r=0;r<16;++r)t[n+r]=a[r];return t}return Object(i.a)(a)}},"7kmN":function(e,t,n){"use strict";n.r(t),n.d(t,"OnboardingTasksModule",(function(){return rt}));var o=n("ofXK"),i=n("tyNb"),a=n("mrSG"),r=n("XNiG"),l=n("1G5W"),c=n("7Cbv"),s=n("xG9w"),d=n("3Pt+"),C=n("+rOU"),g=n("5+WD"),p=n("yuIm"),m=n("fXoL"),f=n("XNFG"),u=n("rQiX"),h=n("oyK8"),M=n("rDax"),_=n("0IaG"),v=n("Jzeh"),O=n("NFeN"),x=n("Qu3c"),k=n("vzmP"),P=n("UVjm"),y=n("1jcm"),w=n("kmnG"),b=n("qFsG"),S=n("iadO"),V=n("bSwM"),F=n("Ot3k"),E=n("f0Cb"),H=n("pEYl"),T=n("/rGH");const L=["triggerTaskTypeChange"],I=["triggerActivateChange"],D=["triggerCustomSelectDropdown"],Z=["triggerFileUploadSelectDropdown"];function G(e,t){1&e&&(m["\u0275\u0275elementContainerStart"](0),m["\u0275\u0275element"](1,"app-access-denied"),m["\u0275\u0275elementContainerEnd"]())}function z(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div",25),m["\u0275\u0275listener"]("click",(function(){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"](2).switchToPreview()})),m["\u0275\u0275text"](1," Preview "),m["\u0275\u0275elementEnd"]()}}const j=function(e){return{"save-btn-disabled":e}};function U(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div",26),m["\u0275\u0275listener"]("click",(function(){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"](2).saveTask()})),m["\u0275\u0275text"](1),m["\u0275\u0275elementEnd"]()}if(2&e){const e=m["\u0275\u0275nextContext"](2);let t=null;m["\u0275\u0275property"]("ngClass",m["\u0275\u0275pureFunction1"](2,j,e.isSaveDisabled||!(null!=(t=e.taskFormGroup.get("fields"))&&null!=t.controls&&t.controls.length))),m["\u0275\u0275advance"](1),m["\u0275\u0275textInterpolate1"](" ","create"==e.currentView?"Create":"Update"," ")}}const A=function(e){return{"activate-btn-disabled":e}};function N(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div",27,28),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275reference"](2),n=m["\u0275\u0275nextContext"](2),o=m["\u0275\u0275reference"](7);return n.openOverlay(t,o)})),m["\u0275\u0275text"](3),m["\u0275\u0275pipe"](4,"masterData"),m["\u0275\u0275elementStart"](5,"mat-icon",29),m["\u0275\u0275text"](6,"keyboard_arrow_down"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}if(2&e){const e=m["\u0275\u0275nextContext"](2);let t=null,n=null;m["\u0275\u0275property"]("ngClass",m["\u0275\u0275pureFunction1"](6,A,e.isSaveDisabled||!(null!=(t=e.taskFormGroup.get("fields"))&&null!=t.controls&&t.controls.length)||e.taskFormGroup.invalid)),m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate1"](" ",m["\u0275\u0275pipeBind3"](4,2,null==(n=e.taskFormGroup.get("task_status"))?null:n.value,e.taskStatusMasterData,"display_name")," ")}}function R(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div",34),m["\u0275\u0275listener"]("click",(function(){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"](3).addNewTask()})),m["\u0275\u0275text"](1," + New Task "),m["\u0275\u0275elementEnd"]()}}const B=function(e,t){return[e,t,0,0,"C"]};function q(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",30),m["\u0275\u0275elementStart"](1,"div",31),m["\u0275\u0275elementStart"](2,"div",12),m["\u0275\u0275text"](3,"Tasks"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](4,"div",13),m["\u0275\u0275text"](5," Your existing tasks are listed below "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](6,"div",32),m["\u0275\u0275template"](7,R,2,0,"div",33),m["\u0275\u0275pipe"](8,"access"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"](2);m["\u0275\u0275advance"](4),m["\u0275\u0275property"]("matTooltip","Your existing tasks are listed below"),m["\u0275\u0275advance"](3),m["\u0275\u0275property"]("ngIf",m["\u0275\u0275pipeBindV"](8,2,m["\u0275\u0275pureFunction2"](8,B,e.access.moduleId.settings,e.access.subModuleId.onboardingTaskSettings))&&!e.isLoading)}}function Q(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"app-list-view",38),m["\u0275\u0275listener"]("onScroll",(function(){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"](3).onDataScroll()}))("onClick",(function(t){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"](3).onClickRowData(t)})),m["\u0275\u0275elementEnd"]()}if(2&e){const e=m["\u0275\u0275nextContext"](3);m["\u0275\u0275property"]("list",e.data)("fieldConfig",e.fieldConfig)("variant",2)("isCheckboxActive",!1)}}function Y(e,t){1&e&&(m["\u0275\u0275elementStart"](0,"div",39),m["\u0275\u0275element"](1,"img",40),m["\u0275\u0275elementStart"](2,"div",41),m["\u0275\u0275text"](3,"No Tasks Found"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]())}function W(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",39),m["\u0275\u0275elementStart"](1,"div",42),m["\u0275\u0275element"](2,"img",43),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](3,"div",44),m["\u0275\u0275elementStart"](4,"div",45),m["\u0275\u0275text"](5,"Loading..."),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"](3);m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("src",e.uiTextConfig["UI-LOADING-001"],m["\u0275\u0275sanitizeUrl"])}}function $(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",35),m["\u0275\u0275template"](1,Q,1,4,"app-list-view",36),m["\u0275\u0275template"](2,Y,4,0,"div",37),m["\u0275\u0275template"](3,W,6,1,"div",37),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"](2);m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",!e.isLoading&&e.data&&e.data.length>0),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",!(e.isLoading||e.data&&0!=e.data.length)),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",e.isLoading)}}function X(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div",64),m["\u0275\u0275elementStart"](1,"div",65),m["\u0275\u0275text"](2),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](3,"mat-icon",66),m["\u0275\u0275listener"]("click",(function(){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"](3).switchToTaskNameEditMode()})),m["\u0275\u0275text"](4," edit "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}if(2&e){const e=m["\u0275\u0275nextContext"](3);let t=null,n=null;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("matTooltip",null==(t=e.taskFormGroup.get("task_name"))?null:t.value),m["\u0275\u0275advance"](1),m["\u0275\u0275textInterpolate1"](" ",null==(n=e.taskFormGroup.get("task_name"))?null:n.value," ")}}const K=function(e){return{"border-bottom":e}};function J(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div",67),m["\u0275\u0275elementStart"](1,"input",68),m["\u0275\u0275listener"]("keydown.enter",(function(){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"](3).switchToTaskNameViewMode()})),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](2,"mat-icon",69),m["\u0275\u0275listener"]("click",(function(){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"](3).switchToTaskNameViewMode()})),m["\u0275\u0275text"](3," check_circle "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}if(2&e){const e=m["\u0275\u0275nextContext"](3);m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("id","#taskNameInput")("ngStyle",m["\u0275\u0275pureFunction1"](2,K,e.taskFormGroup.get("task_name").hasError("required")&&e.taskFormGroup.get("task_name").touched?"1px solid #f44336":""))}}function ee(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div",70),m["\u0275\u0275listener"]("click",(function(){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"](3).resetSubTasks()})),m["\u0275\u0275text"](1," Reset All "),m["\u0275\u0275elementEnd"]()}}function te(e,t){1&e&&(m["\u0275\u0275elementContainerStart"](0),m["\u0275\u0275namespaceSVG"](),m["\u0275\u0275elementStart"](1,"svg",94),m["\u0275\u0275element"](2,"path",95),m["\u0275\u0275element"](3,"path",96),m["\u0275\u0275element"](4,"path",97),m["\u0275\u0275element"](5,"path",98),m["\u0275\u0275element"](6,"path",99),m["\u0275\u0275element"](7,"path",100),m["\u0275\u0275element"](8,"path",101),m["\u0275\u0275element"](9,"path",102),m["\u0275\u0275element"](10,"path",103),m["\u0275\u0275element"](11,"path",104),m["\u0275\u0275elementStart"](12,"g",105),m["\u0275\u0275element"](13,"path",106),m["\u0275\u0275elementEnd"](),m["\u0275\u0275element"](14,"path",107),m["\u0275\u0275element"](15,"path",108),m["\u0275\u0275element"](16,"path",109),m["\u0275\u0275element"](17,"path",110),m["\u0275\u0275element"](18,"path",111),m["\u0275\u0275element"](19,"path",112),m["\u0275\u0275element"](20,"path",113),m["\u0275\u0275element"](21,"path",114),m["\u0275\u0275element"](22,"path",115),m["\u0275\u0275element"](23,"path",116),m["\u0275\u0275element"](24,"path",117),m["\u0275\u0275element"](25,"path",118),m["\u0275\u0275elementStart"](26,"defs"),m["\u0275\u0275elementStart"](27,"clipPath",119),m["\u0275\u0275element"](28,"rect",120),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementContainerEnd"]())}function ne(e,t){1&e&&(m["\u0275\u0275elementContainerStart"](0),m["\u0275\u0275elementStart"](1,"div",121),m["\u0275\u0275text"](2,"Enter a number"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementContainerEnd"]())}function oe(e,t){1&e&&(m["\u0275\u0275elementContainerStart"](0),m["\u0275\u0275elementStart"](1,"div",121),m["\u0275\u0275text"](2," Enter your response "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementContainerEnd"]())}function ie(e,t){1&e&&(m["\u0275\u0275elementContainerStart"](0),m["\u0275\u0275elementStart"](1,"div",121),m["\u0275\u0275text"](2,"Enter URL"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementContainerEnd"]())}function ae(e,t){if(1&e&&(m["\u0275\u0275elementContainerStart"](0),m["\u0275\u0275elementStart"](1,"mat-form-field",122),m["\u0275\u0275elementStart"](2,"div",123),m["\u0275\u0275element"](3,"input",124),m["\u0275\u0275elementStart"](4,"mat-datepicker-toggle",125),m["\u0275\u0275elementStart"](5,"mat-icon",126),m["\u0275\u0275text"](6,"calendar_today"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275element"](7,"mat-datepicker",null,127),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementContainerEnd"]()),2&e){const e=m["\u0275\u0275reference"](8);m["\u0275\u0275advance"](3),m["\u0275\u0275property"]("matDatepicker",e),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("for",e)}}function re(e,t){1&e&&(m["\u0275\u0275elementContainerStart"](0),m["\u0275\u0275element"](1,"div",136),m["\u0275\u0275elementContainerEnd"]())}function le(e,t){1&e&&(m["\u0275\u0275elementContainerStart"](0),m["\u0275\u0275element"](1,"div",137),m["\u0275\u0275elementContainerEnd"]())}function ce(e,t){if(1&e&&(m["\u0275\u0275elementContainerStart"](0),m["\u0275\u0275elementStart"](1,"div",138),m["\u0275\u0275text"](2),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementContainerEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]().index;m["\u0275\u0275advance"](2),m["\u0275\u0275textInterpolate1"](" ",e+1," ")}}function se(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div",133),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275nextContext"]().index,n=m["\u0275\u0275nextContext"](2).index;return m["\u0275\u0275nextContext"](3).deleteSingleCustomOption(n,t)})),m["\u0275\u0275elementStart"](1,"mat-icon",29),m["\u0275\u0275text"](2,"close"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}}function de(e,t){1&e&&(m["\u0275\u0275elementStart"](0,"div",139),m["\u0275\u0275elementStart"](1,"mat-icon",29),m["\u0275\u0275text"](2,"drag_indicator"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]())}function Ce(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementContainerStart"](0,71),m["\u0275\u0275elementStart"](1,"div",130),m["\u0275\u0275template"](2,re,2,0,"ng-container",0),m["\u0275\u0275template"](3,le,2,0,"ng-container",0),m["\u0275\u0275template"](4,ce,3,1,"ng-container",0),m["\u0275\u0275elementStart"](5,"div",131),m["\u0275\u0275element"](6,"input",132),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](7,"div",133),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const n=t.index,o=m["\u0275\u0275nextContext"](2).index;return m["\u0275\u0275nextContext"](3).addSingleCustomOption(o,n+1)})),m["\u0275\u0275elementStart"](8,"mat-icon",29),m["\u0275\u0275text"](9,"add"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](10,se,3,0,"div",134),m["\u0275\u0275template"](11,de,3,0,"div",135),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=t.index,n=m["\u0275\u0275nextContext"](2),o=n.$implicit,i=n.index,a=m["\u0275\u0275nextContext"](3);let r=null,l=null;m["\u0275\u0275property"]("formGroupName",e),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf","multiple-choice"==o.get("fieldType").value),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","checkbox"==o.get("fieldType").value),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","dropdown"==o.get("fieldType").value),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngStyle",m["\u0275\u0275pureFunction1"](7,K,a.taskFormGroup.get("fields").get(i.toString()).get("fieldValues").get(e.toString()).get("option").hasError("required")&&a.taskFormGroup.get("fields").get(i.toString()).get("fieldValues").get(e.toString()).get("option").touched?"1px solid #f44336":"")),m["\u0275\u0275advance"](4),m["\u0275\u0275property"]("ngIf",(null==(r=o.get("fieldValues"))||null==r.controls?null:r.controls.length)>1),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",(null==(l=o.get("fieldValues"))||null==l.controls?null:l.controls.length)>1)}}function ge(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementContainerStart"](0,128),m["\u0275\u0275elementStart"](1,"div",129),m["\u0275\u0275listener"]("cdkDropListDropped",(function(t){m["\u0275\u0275restoreView"](e);const n=m["\u0275\u0275nextContext"]().index;return m["\u0275\u0275nextContext"](3).dragDropOption(t,n)})),m["\u0275\u0275template"](2,Ce,12,9,"ng-container",60),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=m["\u0275\u0275nextContext"]().$implicit;let t=null;m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngForOf",null==(t=e.get("fieldValues"))?null:t.controls)}}function pe(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",150),m["\u0275\u0275element"](1,"mat-checkbox",151),m["\u0275\u0275elementStart"](2,"div",152),m["\u0275\u0275text"](3),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("formControlName",e.id),m["\u0275\u0275advance"](2),m["\u0275\u0275textInterpolate1"](" ",e.name," ")}}function me(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",148),m["\u0275\u0275template"](1,pe,4,2,"div",149),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"](2).$implicit,t=m["\u0275\u0275nextContext"](3);m["\u0275\u0275property"]("formGroup",e.get("fileTypes")),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",t.fileTypesInFileUpload)}}function fe(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementContainerStart"](0),m["\u0275\u0275elementStart"](1,"div",140),m["\u0275\u0275elementStart"](2,"div",141),m["\u0275\u0275elementStart"](3,"div",142),m["\u0275\u0275text"](4,"Allow only specific file types"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](5,"mat-slide-toggle",143),m["\u0275\u0275listener"]("change",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275nextContext"]().index;return m["\u0275\u0275nextContext"](3).onChangeFileAccess(t)})),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](6,me,2,2,"div",144),m["\u0275\u0275elementStart"](7,"div",145),m["\u0275\u0275elementStart"](8,"div",142),m["\u0275\u0275text"](9,"Maximum number of files"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](10,"div",146,147),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275reference"](12),n=m["\u0275\u0275nextContext"]().index,o=m["\u0275\u0275nextContext"](3),i=m["\u0275\u0275reference"](11);return o.openOverlay(t,i,o.fileCountInFileUpload,{index:n,field:"maxFileCount"})})),m["\u0275\u0275elementStart"](13,"div",142),m["\u0275\u0275text"](14),m["\u0275\u0275pipe"](15,"masterData"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](16,"mat-icon",29),m["\u0275\u0275text"](17," keyboard_arrow_down "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](18,"div",145),m["\u0275\u0275elementStart"](19,"div",142),m["\u0275\u0275text"](20,"Maximum file size"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](21,"div",146,147),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275reference"](12),n=m["\u0275\u0275nextContext"]().index,o=m["\u0275\u0275nextContext"](3),i=m["\u0275\u0275reference"](11);return o.openOverlay(t,i,o.fileSizeInFileUpload,{index:n,field:"maxFileSize"})})),m["\u0275\u0275elementStart"](24,"div",142),m["\u0275\u0275text"](25),m["\u0275\u0275pipe"](26,"masterData"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](27,"mat-icon",29),m["\u0275\u0275text"](28," keyboard_arrow_down "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=m["\u0275\u0275nextContext"]().$implicit,t=m["\u0275\u0275nextContext"](3);m["\u0275\u0275advance"](6),m["\u0275\u0275property"]("ngIf",e.get("allowSpecificFiles").value),m["\u0275\u0275advance"](8),m["\u0275\u0275textInterpolate1"](" ",m["\u0275\u0275pipeBind2"](15,3,e.get("maxFileCount").value,t.fileCountInFileUpload)," "),m["\u0275\u0275advance"](11),m["\u0275\u0275textInterpolate1"](" ",m["\u0275\u0275pipeBind2"](26,6,e.get("maxFileSize").value,t.fileSizeInFileUpload)," ")}}const ue=function(e){return{width:e}},he=function(){return["rich-text","number","text","url","date","file-upload"]};function Me(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementContainerStart"](0,71),m["\u0275\u0275elementStart"](1,"div",72),m["\u0275\u0275elementStart"](2,"div",62),m["\u0275\u0275text"](3),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](4,"div",11),m["\u0275\u0275elementStart"](5,"div",73),m["\u0275\u0275elementStart"](6,"div",74),m["\u0275\u0275element"](7,"input",75),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](8,"div",76),m["\u0275\u0275elementStart"](9,"app-single-select-chip",77),m["\u0275\u0275listener"]("onValueChange",(function(n){m["\u0275\u0275restoreView"](e);const o=t.index;return m["\u0275\u0275nextContext"](3).onQuestionTypeChange(n,o)})),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](10,"div",78),m["\u0275\u0275template"](11,te,29,0,"ng-container",0),m["\u0275\u0275template"](12,ne,3,0,"ng-container",0),m["\u0275\u0275template"](13,oe,3,0,"ng-container",0),m["\u0275\u0275template"](14,ie,3,0,"ng-container",0),m["\u0275\u0275template"](15,ae,9,2,"ng-container",0),m["\u0275\u0275template"](16,ge,3,1,"ng-container",79),m["\u0275\u0275template"](17,fe,29,9,"ng-container",0),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](18,"div",80),m["\u0275\u0275elementStart"](19,"mat-slide-toggle",81),m["\u0275\u0275elementStart"](20,"span",82),m["\u0275\u0275text"](21,"Mandatory"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](22,"div",83),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const n=t.index;return m["\u0275\u0275nextContext"](3).onDeleteCustomQuestion(n)})),m["\u0275\u0275namespaceSVG"](),m["\u0275\u0275elementStart"](23,"svg",84),m["\u0275\u0275elementStart"](24,"g",85),m["\u0275\u0275elementStart"](25,"mask",86),m["\u0275\u0275element"](26,"rect",87),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](27,"g",88),m["\u0275\u0275element"](28,"path",89),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275namespaceHTML"](),m["\u0275\u0275elementStart"](29,"div",83),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const n=t.index;return m["\u0275\u0275nextContext"](3).onCopyCustomQuestion(n)})),m["\u0275\u0275namespaceSVG"](),m["\u0275\u0275elementStart"](30,"svg",84),m["\u0275\u0275elementStart"](31,"g",90),m["\u0275\u0275elementStart"](32,"mask",91),m["\u0275\u0275element"](33,"rect",87),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](34,"g",92),m["\u0275\u0275element"](35,"path",93),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=t.$implicit,n=t.index,o=m["\u0275\u0275nextContext"](3);m["\u0275\u0275property"]("formGroupName",n),m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate"](n+1),m["\u0275\u0275advance"](4),m["\u0275\u0275property"]("ngStyle",m["\u0275\u0275pureFunction1"](14,K,o.taskFormGroup.get("fields").get(n.toString()).get("label").hasError("required")&&o.taskFormGroup.get("fields").get(n.toString()).get("label").touched?"1px solid #f44336":"")),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("masterData",o.subTasksMasterData)("selectedValue",e.get("fieldType").value)("displayClose",!1),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngStyle",m["\u0275\u0275pureFunction1"](16,ue,"file-upload"==e.get("fieldType").value?"95%":"")),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","rich-text"==e.get("fieldType").value),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","number"==e.get("fieldType").value),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","text"==e.get("fieldType").value),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","url"==e.get("fieldType").value),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","date"==e.get("fieldType").value),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",!m["\u0275\u0275pureFunction0"](18,he).includes(e.get("fieldType").value)),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","file-upload"==e.get("fieldType").value)}}function _e(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"form",46),m["\u0275\u0275elementStart"](1,"div",47),m["\u0275\u0275elementStart"](2,"div",48),m["\u0275\u0275template"](3,X,5,2,"div",49),m["\u0275\u0275template"](4,J,4,4,"div",50),m["\u0275\u0275elementStart"](5,"div",51),m["\u0275\u0275text"](6,"Enter the name of your task above"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](7,"div",52),m["\u0275\u0275template"](8,ee,2,0,"div",53),m["\u0275\u0275elementStart"](9,"div",54),m["\u0275\u0275elementStart"](10,"div",55),m["\u0275\u0275text"](11,"Type"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](12,"div",56,57),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275reference"](14),n=m["\u0275\u0275nextContext"](2),o=m["\u0275\u0275reference"](5);return n.openOverlay(t,o)})),m["\u0275\u0275text"](15),m["\u0275\u0275pipe"](16,"masterData"),m["\u0275\u0275elementStart"](17,"mat-icon",29),m["\u0275\u0275text"](18,"keyboard_arrow_down"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](19,"div",58),m["\u0275\u0275elementContainerStart"](20,59),m["\u0275\u0275template"](21,Me,36,19,"ng-container",60),m["\u0275\u0275elementStart"](22,"div",61),m["\u0275\u0275listener"]("click",(function(){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"](2).addCustomQuestion()})),m["\u0275\u0275elementStart"](23,"div",62),m["\u0275\u0275text"](24),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](25,"div",63),m["\u0275\u0275text"](26,"+ Create Task"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementContainerEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}if(2&e){const e=m["\u0275\u0275nextContext"](2);let t=null,n=null,o=null,i=null;m["\u0275\u0275property"]("formGroup",e.taskFormGroup),m["\u0275\u0275advance"](3),m["\u0275\u0275property"]("ngIf",!e.isTaskNameEditInProgress),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",e.isTaskNameEditInProgress),m["\u0275\u0275advance"](4),m["\u0275\u0275property"]("ngIf",("create"==e.currentView||"edit"==e.currentView)&&(null==(t=e.taskFormGroup.get("fields"))||null==t.controls?null:t.controls.length)>0),m["\u0275\u0275advance"](7),m["\u0275\u0275textInterpolate1"](" ",m["\u0275\u0275pipeBind2"](16,7,null==(n=e.taskFormGroup.get("task_type"))?null:n.value,e.taskTypeMasterData)," "),m["\u0275\u0275advance"](6),m["\u0275\u0275property"]("ngForOf",null==(o=e.taskFormGroup.get("fields"))?null:o.controls),m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate1"](" ",(null==(i=e.taskFormGroup.get("fields"))||null==i.controls?null:i.controls.length)+1," ")}}function ve(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",153),m["\u0275\u0275elementStart"](1,"div",154),m["\u0275\u0275elementStart"](2,"div",65),m["\u0275\u0275text"](3),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](4,"div",155),m["\u0275\u0275elementStart"](5,"div",54),m["\u0275\u0275elementStart"](6,"div",55),m["\u0275\u0275text"](7,"Type"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](8,"div",156),m["\u0275\u0275text"](9),m["\u0275\u0275pipe"](10,"masterData"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"](2);let t=null,n=null,o=null;m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("matTooltip",null==(t=e.taskFormGroup.get("task_name"))?null:t.value),m["\u0275\u0275advance"](1),m["\u0275\u0275textInterpolate1"](" ",null==(n=e.taskFormGroup.get("task_name"))?null:n.value," "),m["\u0275\u0275advance"](6),m["\u0275\u0275textInterpolate1"](" ",m["\u0275\u0275pipeBind2"](10,3,null==(o=e.taskFormGroup.get("task_type"))?null:o.value,e.taskTypeMasterData)," ")}}function Oe(e,t){}function xe(e,t){if(1&e&&m["\u0275\u0275template"](0,Oe,0,0,"ng-template",160),2&e){m["\u0275\u0275nextContext"](4);const e=m["\u0275\u0275reference"](13);m["\u0275\u0275property"]("ngTemplateOutlet",e)}}function ke(e,t){1&e&&(m["\u0275\u0275elementContainerStart"](0),m["\u0275\u0275namespaceSVG"](),m["\u0275\u0275elementStart"](1,"svg",94),m["\u0275\u0275element"](2,"path",95),m["\u0275\u0275element"](3,"path",96),m["\u0275\u0275element"](4,"path",97),m["\u0275\u0275element"](5,"path",98),m["\u0275\u0275element"](6,"path",99),m["\u0275\u0275element"](7,"path",100),m["\u0275\u0275element"](8,"path",101),m["\u0275\u0275element"](9,"path",102),m["\u0275\u0275element"](10,"path",103),m["\u0275\u0275element"](11,"path",104),m["\u0275\u0275elementStart"](12,"g",105),m["\u0275\u0275element"](13,"path",106),m["\u0275\u0275elementEnd"](),m["\u0275\u0275element"](14,"path",107),m["\u0275\u0275element"](15,"path",108),m["\u0275\u0275element"](16,"path",109),m["\u0275\u0275element"](17,"path",110),m["\u0275\u0275element"](18,"path",111),m["\u0275\u0275element"](19,"path",112),m["\u0275\u0275element"](20,"path",113),m["\u0275\u0275element"](21,"path",114),m["\u0275\u0275element"](22,"path",115),m["\u0275\u0275element"](23,"path",116),m["\u0275\u0275element"](24,"path",117),m["\u0275\u0275element"](25,"path",118),m["\u0275\u0275elementStart"](26,"defs"),m["\u0275\u0275elementStart"](27,"clipPath",119),m["\u0275\u0275element"](28,"rect",120),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementContainerEnd"]())}function Pe(e,t){1&e&&(m["\u0275\u0275elementContainerStart"](0),m["\u0275\u0275elementStart"](1,"div",121),m["\u0275\u0275text"](2,"Enter a number"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementContainerEnd"]())}function ye(e,t){1&e&&(m["\u0275\u0275elementContainerStart"](0),m["\u0275\u0275elementStart"](1,"div",121),m["\u0275\u0275text"](2,"Enter your response"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementContainerEnd"]())}function we(e,t){1&e&&(m["\u0275\u0275elementContainerStart"](0),m["\u0275\u0275elementStart"](1,"div",121),m["\u0275\u0275text"](2,"Enter URL"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementContainerEnd"]())}function be(e,t){if(1&e&&(m["\u0275\u0275elementContainerStart"](0),m["\u0275\u0275elementStart"](1,"mat-form-field",122),m["\u0275\u0275elementStart"](2,"div",123),m["\u0275\u0275element"](3,"input",124),m["\u0275\u0275elementStart"](4,"mat-datepicker-toggle",125),m["\u0275\u0275elementStart"](5,"mat-icon",126),m["\u0275\u0275text"](6,"calendar_today"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275element"](7,"mat-datepicker",null,127),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementContainerEnd"]()),2&e){const e=m["\u0275\u0275reference"](8);m["\u0275\u0275advance"](3),m["\u0275\u0275property"]("matDatepicker",e),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("for",e)}}function Se(e,t){1&e&&(m["\u0275\u0275elementContainerStart"](0),m["\u0275\u0275element"](1,"div",136),m["\u0275\u0275elementContainerEnd"]())}function Ve(e,t){1&e&&(m["\u0275\u0275elementContainerStart"](0),m["\u0275\u0275element"](1,"div",137),m["\u0275\u0275elementContainerEnd"]())}function Fe(e,t){if(1&e&&(m["\u0275\u0275elementContainerStart"](0,71),m["\u0275\u0275elementStart"](1,"div",162),m["\u0275\u0275template"](2,Se,2,0,"ng-container",0),m["\u0275\u0275template"](3,Ve,2,0,"ng-container",0),m["\u0275\u0275elementStart"](4,"div",163),m["\u0275\u0275text"](5),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit,n=t.index,o=m["\u0275\u0275nextContext"](2).$implicit;m["\u0275\u0275property"]("formGroupName",n),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf","multiple-choice"==o.get("fieldType").value),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","checkbox"==o.get("fieldType").value),m["\u0275\u0275advance"](2),m["\u0275\u0275textInterpolate1"](" ",e.get("option").value," ")}}function Ee(e,t){if(1&e&&(m["\u0275\u0275elementContainerStart"](0,128),m["\u0275\u0275elementStart"](1,"div",161),m["\u0275\u0275template"](2,Fe,6,4,"ng-container",60),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementContainerEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]().$implicit;let t=null;m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngForOf",null==(t=e.get("fieldValues"))?null:t.controls)}}function He(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementContainerStart"](0),m["\u0275\u0275elementStart"](1,"div",164,165),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const t=m["\u0275\u0275reference"](3),n=m["\u0275\u0275nextContext"]().$implicit,o=m["\u0275\u0275nextContext"](3),i=m["\u0275\u0275reference"](9);return o.openOverlay(t,i,n.get("fieldValues"))})),m["\u0275\u0275text"](4," Select "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementContainerEnd"]()}}function Te(e,t){if(1&e&&(m["\u0275\u0275elementContainerStart"](0),m["\u0275\u0275elementStart"](1,"div",140),m["\u0275\u0275elementStart"](2,"div"),m["\u0275\u0275namespaceSVG"](),m["\u0275\u0275elementStart"](3,"svg",166),m["\u0275\u0275element"](4,"ellipse",167),m["\u0275\u0275element"](5,"ellipse",168),m["\u0275\u0275element"](6,"ellipse",169),m["\u0275\u0275element"](7,"ellipse",170),m["\u0275\u0275element"](8,"ellipse",171),m["\u0275\u0275element"](9,"path",172),m["\u0275\u0275element"](10,"path",173),m["\u0275\u0275element"](11,"path",174),m["\u0275\u0275element"](12,"path",175),m["\u0275\u0275element"](13,"path",176),m["\u0275\u0275element"](14,"path",177),m["\u0275\u0275element"](15,"path",178),m["\u0275\u0275element"](16,"path",176),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275namespaceHTML"](),m["\u0275\u0275elementStart"](17,"div",179),m["\u0275\u0275elementStart"](18,"span",180),m["\u0275\u0275text"](19,"Drag And Drop File Here Or"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](20,"span",181),m["\u0275\u0275text"](21," Choose File "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](22,"div",182),m["\u0275\u0275elementStart"](23,"div",183),m["\u0275\u0275text"](24),m["\u0275\u0275pipe"](25,"masterData"),m["\u0275\u0275pipe"](26,"masterData"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](27,"div",183),m["\u0275\u0275text"](28),m["\u0275\u0275pipe"](29,"masterData"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementContainerEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]().$implicit,t=m["\u0275\u0275nextContext"](3);let n=null;m["\u0275\u0275advance"](24),m["\u0275\u0275textInterpolate3"](" Supported Formats: ",t.displaySupportedFormats(null==(n=e.get("allowSpecificFiles"))?null:n.value,e.get("fileTypes"))," (Upto ",m["\u0275\u0275pipeBind2"](25,4,e.get("maxFileCount").value,t.fileCountInFileUpload)," File",m["\u0275\u0275pipeBind2"](26,7,e.get("maxFileCount").value,t.fileCountInFileUpload)>1?"s":"",") "),m["\u0275\u0275advance"](4),m["\u0275\u0275textInterpolate1"](" Maximum Size: ",m["\u0275\u0275pipeBind2"](29,10,e.get("maxFileSize").value,t.fileSizeInFileUpload)," ")}}const Le=function(){return["multiple-choice","checkbox"]};function Ie(e,t){if(1&e&&(m["\u0275\u0275elementContainerStart"](0,71),m["\u0275\u0275elementStart"](1,"div",72),m["\u0275\u0275elementStart"](2,"div",62),m["\u0275\u0275text"](3),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](4,"div",11),m["\u0275\u0275elementStart"](5,"div",158),m["\u0275\u0275text"](6),m["\u0275\u0275template"](7,xe,1,1,void 0,0),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](8,"div",159),m["\u0275\u0275template"](9,ke,29,0,"ng-container",0),m["\u0275\u0275template"](10,Pe,3,0,"ng-container",0),m["\u0275\u0275template"](11,ye,3,0,"ng-container",0),m["\u0275\u0275template"](12,we,3,0,"ng-container",0),m["\u0275\u0275template"](13,be,9,2,"ng-container",0),m["\u0275\u0275template"](14,Ee,3,1,"ng-container",79),m["\u0275\u0275template"](15,He,5,0,"ng-container",0),m["\u0275\u0275template"](16,Te,30,13,"ng-container",0),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit,n=t.index;let o=null,i=null;m["\u0275\u0275property"]("formGroupName",n),m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate"](n+1),m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate1"](" ",null==(o=e.get("label"))?null:o.value," "),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",null==(i=e.get("isMandatory"))?null:i.value),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf","rich-text"==e.get("fieldType").value),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","number"==e.get("fieldType").value),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","text"==e.get("fieldType").value),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","url"==e.get("fieldType").value),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","date"==e.get("fieldType").value),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",m["\u0275\u0275pureFunction0"](12,Le).includes(e.get("fieldType").value)),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","dropdown"==e.get("fieldType").value),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","file-upload"==e.get("fieldType").value)}}function De(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",157),m["\u0275\u0275elementContainerStart"](1,59),m["\u0275\u0275template"](2,Ie,17,13,"ng-container",60),m["\u0275\u0275elementContainerEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"](2);let t=null;m["\u0275\u0275property"]("formGroup",e.taskFormGroup),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngForOf",null==(t=e.taskFormGroup.get("fields"))?null:t.controls)}}function Ze(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",184),m["\u0275\u0275elementStart"](1,"div",42),m["\u0275\u0275element"](2,"img",43),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](3,"div",44),m["\u0275\u0275elementStart"](4,"div",45),m["\u0275\u0275text"](5,"Loading..."),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"](2);m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("src",e.uiTextConfig["UI-LOADING-001"],m["\u0275\u0275sanitizeUrl"])}}function Ge(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementContainerStart"](0),m["\u0275\u0275elementStart"](1,"div",7),m["\u0275\u0275elementStart"](2,"div",8),m["\u0275\u0275elementStart"](3,"div",9),m["\u0275\u0275elementStart"](4,"mat-icon",10),m["\u0275\u0275listener"]("click",(function(){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"]().goBackToPreviousRoute()})),m["\u0275\u0275text"](5," chevron_left "),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](6,"div",11),m["\u0275\u0275elementStart"](7,"div",12),m["\u0275\u0275text"](8),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](9,"div",13),m["\u0275\u0275text"](10),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](11,"div",14),m["\u0275\u0275template"](12,z,2,0,"div",15),m["\u0275\u0275template"](13,U,2,4,"div",16),m["\u0275\u0275template"](14,N,7,8,"div",17),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](15,"div",18),m["\u0275\u0275template"](16,q,9,11,"div",19),m["\u0275\u0275template"](17,$,4,3,"div",20),m["\u0275\u0275template"](18,_e,27,10,"form",21),m["\u0275\u0275template"](19,ve,11,6,"div",22),m["\u0275\u0275template"](20,De,3,2,"div",23),m["\u0275\u0275template"](21,Ze,6,1,"div",24),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=m["\u0275\u0275nextContext"]();m["\u0275\u0275advance"](8),m["\u0275\u0275textInterpolate1"](" ","view"==e.currentView?"Task Creation":"create"==e.currentView?"New Task":"edit"==e.currentView?"Edit Task":"Task Preview"," "),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("matTooltip","view"==e.currentView||"create"==e.currentView?"Create Tasks to assign to the candidates":"edit"==e.currentView?"Edit Tasks to assign to the candidates":"View your created task"),m["\u0275\u0275advance"](1),m["\u0275\u0275textInterpolate1"](" ","view"==e.currentView||"create"==e.currentView?"Create Tasks to assign to the candidates":"edit"==e.currentView?"Edit Tasks to assign to the candidates":"View your created task"," "),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",("create"==e.currentView||"edit"==e.currentView)&&!e.isLoading),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",("create"==e.currentView||"edit"==e.currentView)&&!e.isLoading),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","edit"==e.currentView&&!e.isLoading),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf","view"==e.currentView),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","view"==e.currentView),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",("create"==e.currentView||"edit"==e.currentView)&&!e.isLoading),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","preview"==e.currentView&&!e.isLoading),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf","preview"==e.currentView&&!e.isLoading),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",e.isLoading&&"view"!=e.currentView)}}function ze(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"app-menu-list-overlay",185),m["\u0275\u0275listener"]("onConfirmation",(function(t){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"]().onTaskTypeChange(t)})),m["\u0275\u0275elementEnd"]()}if(2&e){const e=m["\u0275\u0275nextContext"]();let t=null;m["\u0275\u0275property"]("list",e.taskTypeMasterData)("currentSelectedId",null==(t=e.taskFormGroup.get("task_type"))?null:t.value)}}function je(e,t){1&e&&m["\u0275\u0275element"](0,"mat-divider",191)}const Ue=function(e){return{background:e}};function Ae(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementContainerStart"](0),m["\u0275\u0275elementStart"](1,"div",188),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const n=t.$implicit;return m["\u0275\u0275nextContext"](2).onTaskStatusChange(n.id)})),m["\u0275\u0275element"](2,"div",189),m["\u0275\u0275elementStart"](3,"div",142),m["\u0275\u0275text"](4),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](5,je,1,0,"mat-divider",190),m["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=t.$implicit,n=t.index,o=m["\u0275\u0275nextContext"](2);let i=null;m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngStyle",m["\u0275\u0275pureFunction1"](4,Ue,e.id==(null==(i=o.taskFormGroup.get("task_status"))?null:i.value)?e.bg_color:"#fff")),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngStyle",m["\u0275\u0275pureFunction1"](6,Ue,e.color)),m["\u0275\u0275advance"](2),m["\u0275\u0275textInterpolate"](e.display_name),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",o.taskStatusMasterData.length-1!=n)}}function Ne(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",186),m["\u0275\u0275template"](1,Ae,6,8,"ng-container",187),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]();m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",e.taskStatusMasterData)}}function Re(e,t){1&e&&m["\u0275\u0275element"](0,"mat-divider",191)}function Be(e,t){if(1&e&&(m["\u0275\u0275elementContainerStart"](0),m["\u0275\u0275elementStart"](1,"div",193),m["\u0275\u0275text"](2),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](3,Re,1,0,"mat-divider",190),m["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit,n=t.index,o=m["\u0275\u0275nextContext"](2);m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("matTooltip",e||"-"),m["\u0275\u0275advance"](1),m["\u0275\u0275textInterpolate"](e||"-"),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",o.customDropdownFieldValues.length-1!=n)}}function qe(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",192),m["\u0275\u0275template"](1,Be,4,3,"ng-container",187),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]();m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",e.customDropdownFieldValues)}}function Qe(e,t){1&e&&m["\u0275\u0275element"](0,"mat-divider",191)}function Ye(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementContainerStart"](0),m["\u0275\u0275elementStart"](1,"div",195),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const n=t.$implicit;return m["\u0275\u0275nextContext"](2).setFileUploadValidationValue(n.id)})),m["\u0275\u0275text"](2),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](3,Qe,1,0,"mat-divider",190),m["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=t.$implicit,n=t.index,o=m["\u0275\u0275nextContext"](2);m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("matTooltip",e.name||"-"),m["\u0275\u0275advance"](1),m["\u0275\u0275textInterpolate1"](" ",e.name||"-"," "),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",o.customDropdownFieldValues.length-1!=n)}}function We(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",194),m["\u0275\u0275template"](1,Ye,4,3,"ng-container",187),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]();m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",e.customDropdownFieldValues)}}function $e(e,t){1&e&&(m["\u0275\u0275elementStart"](0,"span",196),m["\u0275\u0275text"](1,"*"),m["\u0275\u0275elementEnd"]())}const Xe=function(e,t){return[e,t,0,0,"V"]},Ke=[{path:"",component:(()=>{class e{constructor(e,t,n,o,i,a,l){this._toaster=e,this._masterDataService=t,this._onboardingSettingService=n,this._fb=o,this._overlay=i,this._viewContainerRef=a,this._dialog=l,this._onDestroy=new r.b,this.access=p,this.skip=0,this.limit=15,this.data=[],this.fieldConfig=[],this.uiTextConfig={},this.isLoading=!0,this.isSaveDisabled=!1,this.isTaskNameEditInProgress=!1,this.currentView="view",this.previousView="view",this.editTaskData={},this.taskTypeMasterData=[],this.subTasksMasterData=[],this.taskStatusMasterData=[],this.fileTypesInFileUpload=[],this.fileSizeInFileUpload=[],this.fileCountInFileUpload=[],this.customDropdownFieldValues=[],this.currentSelectedFieldConfig={},this.taskFormGroup=this._fb.group({})}ngOnInit(){return Object(a.c)(this,void 0,void 0,(function*(){this.calculateDynamicContentHeight(),yield this.getAtsMasterUiConfig("onboardingTaskSettings"),this.retrieveOnboardingTaskTypeMaster(),this.retrieveOnboardingTaskStatusMaster(),this.retrieveOnboardingSubTasksMaster(),yield this.retrieveAllOnboardingTasks(!0)}))}onResize(){this.calculateDynamicContentHeight()}calculateDynamicContentHeight(){document.documentElement.style.setProperty("--dynamicHeight",window.innerHeight-57+"px"),document.documentElement.style.setProperty("--dynamicSubHeight",window.innerHeight-57-162+"px"),document.documentElement.style.setProperty("--dynamicAccessDeniedHeight",window.innerHeight-57+"px")}goBackToPreviousRoute(){"view"==this.currentView?history.back():"create"==this.currentView||"edit"==this.currentView?(this.previousView=this.currentView,this.currentView="view",this.retrieveAllOnboardingTasks(!0)):"preview"==this.currentView&&(this.currentView=this.previousView)}onClickRowData(e){return Object(a.c)(this,void 0,void 0,(function*(){e.functionName&&""!=e.functionName&&(yield this[e.functionName](e.data))}))}switchToPreview(e){return Object(a.c)(this,void 0,void 0,(function*(){if("create"==this.currentView||"edit"==this.currentView){if(this.taskFormGroup.invalid)return this._toaster.showWarning("Warning \u26a0\ufe0f","Kindly fill all mandatory fields to view the preview!",7e3);let e=this.taskFormGroup.get("fields").getRawValue();if(!e||0==e.length)return this._toaster.showWarning("Warning \u26a0\ufe0f","Atleast one task is mandatory to view the preview!",7e3);this.previousView=this.currentView,this.currentView="preview"}else"view"==this.currentView&&(this.isLoading=!0,this.currentView="preview",this.previousView="view",yield this.retrieveSingleOnboardingTaskData({_id:null==e?void 0:e._id}),yield this.patchForm(),this.isLoading=!1)}))}onEditTask(e){return Object(a.c)(this,void 0,void 0,(function*(){return p.checkAccessForGeneralRole(p.moduleId.settings,p.subModuleId.onboardingTaskSettings,0,0,"E")?(null==e?void 0:e.isEditAllowed)?(this.isLoading=!0,this.isSaveDisabled=!0,this.previousView=this.currentView,this.currentView="edit",yield this.retrieveSingleOnboardingTaskData({_id:null==e?void 0:e._id}),yield this.patchForm(),this.isLoading=!1,void(this.isSaveDisabled=!1)):this._toaster.showInfo("Task Already Assigned!","Oops! This task is already assigned to a checklist and cannot be edited.",7e3):this._toaster.showInfo("Access Restricted!","Oops! You're not authorized to view this content. Contact your administrator for access.",7e3)}))}addNewTask(){return Object(a.c)(this,void 0,void 0,(function*(){yield this.createForm(),this.previousView=this.currentView,this.currentView="create",this.switchToTaskNameEditMode()}))}saveTask(){var e,t,n;if(this.markFormGroupTouched(this.taskFormGroup),this.taskFormGroup.invalid)return this._toaster.showWarning("Warning \u26a0\ufe0f","Kindly Fill All Mandatory Fields!",7e3);let o=[],i=this.taskFormGroup.get("fields").getRawValue();if(!i||0==i.length)return this._toaster.showWarning("Warning \u26a0\ufe0f","Atleast one task is mandatory!",7e3);for(let a=0;a<i.length;a++){let t={};if(t.key=Object(c.a)(),t.label=i[a].label,t.placeholder="number"==i[a].fieldType?"Enter a number":"text"==i[a].fieldType?"Enter you response":"dropdown"==i[a].fieldType?"Select a value":"url"==i[a].fieldType?"Enter URL":"date"==i[a].fieldType?"DD MMM YYYY":"",t.fieldType=i[a].fieldType,t.fieldValues=s.pluck(i[a].fieldValues,"option"),t.isMandatory=i[a].isMandatory,"file-upload"==t.fieldType){t.allowSpecificFiles=i[a].allowSpecificFiles,t.maxFileCount=i[a].maxFileCount,t.maxFileSize=i[a].maxFileSize;let n=(null===(e=this.subTasksMasterData.find(e=>"file-upload"==e.id))||void 0===e?void 0:e.allowed_files)||[];n.forEach(e=>{e.isChecked=i[a].fileTypes[e.id]||!1}),t.fileTypes=n}o.push(t)}if("create"==this.currentView){let e={data:{task_name:this.taskFormGroup.get("task_name").value,task_status:this.taskFormGroup.get("task_status").value,task_type:this.taskFormGroup.get("task_type").value,fields:o}};this.createAndUpdateOnboardingTask(e)}else if("edit"==this.currentView){let e={data:{_id:null===(t=this.editTaskData)||void 0===t?void 0:t._id,id:null===(n=this.editTaskData)||void 0===n?void 0:n.id,task_name:this.taskFormGroup.get("task_name").value,task_status:this.taskFormGroup.get("task_status").value,task_type:this.taskFormGroup.get("task_type").value,fields:o}};this.createAndUpdateOnboardingTask(e)}}displaySuccessMessage(){return Object(a.c)(this,void 0,void 0,(function*(){const{SuccessMsgComponent:e}=yield n.e(940).then(n.bind(null,"oA7u"));this._dialog.open(e,{data:{icon:'<svg width="35" height="34" viewBox="0 0 35 34" fill="none"><path d="M15.1673 24.6654L26.9173 12.9154L24.584 10.582L15.1673 19.9987L10.4173 15.2487L8.08398 17.582L15.1673 24.6654ZM17.5007 33.6654C15.1951 33.6654 13.0284 33.2279 11.0007 32.3529C8.97287 31.4779 7.20898 30.2904 5.70898 28.7904C4.20898 27.2904 3.02148 25.5265 2.14648 23.4987C1.27148 21.4709 0.833984 19.3043 0.833984 16.9987C0.833984 14.6931 1.27148 12.5265 2.14648 10.4987C3.02148 8.47092 4.20898 6.70703 5.70898 5.20703C7.20898 3.70703 8.97287 2.51953 11.0007 1.64453C13.0284 0.769531 15.1951 0.332031 17.5007 0.332031C19.8062 0.332031 21.9729 0.769531 24.0007 1.64453C26.0284 2.51953 27.7923 3.70703 29.2923 5.20703C30.7923 6.70703 31.9798 8.47092 32.8548 10.4987C33.7298 12.5265 34.1673 14.6931 34.1673 16.9987C34.1673 19.3043 33.7298 21.4709 32.8548 23.4987C31.9798 25.5265 30.7923 27.2904 29.2923 28.7904C27.7923 30.2904 26.0284 31.4779 24.0007 32.3529C21.9729 33.2279 19.8062 33.6654 17.5007 33.6654Z" fill="#79BA44"/></svg>',text:"Your task has been",boldText:"Activated Successfully."},disableClose:!0})}))}resetSubTasks(){this.taskFormGroup.get("fields").clear()}onTaskTypeChange(e){this.taskFormGroup.get("task_type").setValue(e.id),this.closeOverlay()}onTaskStatusChange(e){var t,o;return Object(a.c)(this,void 0,void 0,(function*(){if("create"==this.currentView)this.taskFormGroup.get("task_status").setValue(e),this.closeOverlay();else if("edit"==this.currentView){if((null===(t=this.taskFormGroup.get("task_status"))||void 0===t?void 0:t.value)==e)return void this.closeOverlay();if(2==e)this.taskFormGroup.get("task_status").setValue(e),this.updateOnboardingTaskStatus({_id:null===(o=this.editTaskData)||void 0===o?void 0:o._id,task_status:e}),this.closeOverlay();else if(3==e){this.closeOverlay(),this.taskFormGroup.get("task_status").setValue(e);const{DynamicContentDialogComponent:t}=yield Promise.all([n.e(0),n.e(935)]).then(n.bind(null,"3KYs"));this._dialog.open(t,{width:"450px",data:{title:"Are You Sure You Want To Deactivate This Task ?",content:'Once you click on "Deactivate," this task will be disabled, and team members will no longer be able to make updates.',yesBtnText:"Deactivate",isIconVisible:!0,svg:'<svg width="40" height="40" viewBox="0 0 40 40" fill="none">\n                  <g clip-path="url(#clip0_25217_158403)">\n                  <path d="M6.66927 13.332H33.3359V34.9987C33.3359 35.4407 33.1603 35.8647 32.8478 36.1772C32.5352 36.4898 32.1113 36.6654 31.6693 36.6654H8.33594C7.89391 36.6654 7.46999 36.4898 7.15743 36.1772C6.84487 35.8647 6.66927 35.4407 6.66927 34.9987V13.332ZM11.6693 8.33203V4.9987C11.6693 4.55667 11.8449 4.13275 12.1574 3.82019C12.47 3.50763 12.8939 3.33203 13.3359 3.33203H26.6693C27.1113 3.33203 27.5352 3.50763 27.8478 3.82019C28.1603 4.13275 28.3359 4.55667 28.3359 4.9987V8.33203H36.6693V11.6654H3.33594V8.33203H11.6693ZM15.0026 6.66536V8.33203H25.0026V6.66536H15.0026ZM15.0026 19.9987V29.9987H18.3359V19.9987H15.0026ZM21.6693 19.9987V29.9987H25.0026V19.9987H21.6693Z" fill="#1F2347"/>\n                  </g>\n                  <defs>\n                  <clipPath id="clip0_25217_158403">\n                  <rect width="40" height="40" fill="white"/>\n                  </clipPath>\n                  </defs>\n                  </svg>\n                 '},disableClose:!0}).afterClosed().subscribe(t=>Object(a.c)(this,void 0,void 0,(function*(){var n;t?this.updateOnboardingTaskStatus({_id:null===(n=this.editTaskData)||void 0===n?void 0:n._id,task_status:e}):this.taskFormGroup.get("task_status").setValue(2)})))}}}))}createForm(){return Object(a.c)(this,void 0,void 0,(function*(){this.taskFormGroup=this._fb.group({}),this.taskFormGroup.addControl("task_name",new d.j(null,[d.H.required])),this.taskFormGroup.addControl("task_status",new d.j(2,[d.H.required])),this.taskFormGroup.addControl("task_type",new d.j(this.taskTypeMasterData&&this.taskTypeMasterData.length>0?this.taskTypeMasterData[0].id:null,[d.H.required])),this.taskFormGroup.addControl("fields",this._fb.array([]))}))}patchForm(){var e,t,n,o;return Object(a.c)(this,void 0,void 0,(function*(){this.taskFormGroup=this._fb.group({}),this.taskFormGroup.addControl("task_name",new d.j(null===(e=this.editTaskData)||void 0===e?void 0:e.task_name,[d.H.required])),this.taskFormGroup.addControl("task_status",new d.j(null===(t=this.editTaskData)||void 0===t?void 0:t.task_status,[d.H.required])),this.taskFormGroup.addControl("task_type",new d.j(null===(n=this.editTaskData)||void 0===n?void 0:n.task_type,[d.H.required])),this.taskFormGroup.addControl("fields",this._fb.array([]));let i=this.taskFormGroup.get("fields"),a=(null===(o=this.editTaskData)||void 0===o?void 0:o.fields)||[];for(let e=0;e<a.length;e++){const t=this._fb.group({});let n=a[e].fileTypes||[];n&&n.length>0?n.forEach(e=>{t.addControl(e.id,this._fb.control(e.isChecked))}):this.fileTypesInFileUpload.forEach(e=>{t.addControl(e.id,this._fb.control(!1))});const o=this._fb.array([]);(a[e].fieldValues||[]).forEach(e=>{const t=this._fb.group({option:[e,[d.H.required,d.H.pattern(/\S/)]]});o.push(t)});const r=this._fb.group({fieldType:[a[e].fieldType,d.H.required],label:[a[e].label,[d.H.required,d.H.pattern(/\S/)]],isMandatory:[a[e].isMandatory],fieldValues:o,allowSpecificFiles:[a[e].allowSpecificFiles||!1],maxFileCount:[a[e].maxFileCount?a[e].maxFileCount:this.fileCountInFileUpload&&this.fileCountInFileUpload.length>0?this.fileCountInFileUpload[0].id:null,d.H.required],maxFileSize:[a[e].maxFileSize?a[e].maxFileSize:this.fileSizeInFileUpload&&this.fileSizeInFileUpload.length>0?this.fileSizeInFileUpload[0].id:null,d.H.required],fileTypes:t});i.push(r)}}))}openOverlay(e,t,n,o){var i;if(!(null===(i=this.overlayRef)||void 0===i?void 0:i.hasAttached())){n instanceof d.g?(n=n.getRawValue(),this.customDropdownFieldValues=s.pluck(n,"option")):this.customDropdownFieldValues=n,this.currentSelectedFieldConfig=o;const i=this._overlay.position().flexibleConnectedTo(e).withFlexibleDimensions(!0).withPush(!0).withViewportMargin(10).withGrowAfterOpen(!0).withPositions([{originX:"center",originY:"bottom",overlayX:"center",overlayY:"top"}]);i.withDefaultOffsetY(5);const a=this._overlay.scrollStrategies.close();this.overlayRef=this._overlay.create({positionStrategy:i,scrollStrategy:a,hasBackdrop:!0,backdropClass:"",panelClass:["pop-up"]});const r=new C.h(t,this._viewContainerRef);this.overlayRef.attach(r),this.overlayRef.backdropClick().subscribe(()=>{this.closeOverlay()})}}closeOverlay(){var e;null===(e=this.overlayRef)||void 0===e||e.dispose()}setFileUploadValidationValue(e){var t,n,o,i;null===(i=null===(o=null===(n=null===(t=this.taskFormGroup)||void 0===t?void 0:t.get("fields"))||void 0===n?void 0:n.get(this.currentSelectedFieldConfig.index.toString()))||void 0===o?void 0:o.get(this.currentSelectedFieldConfig.field))||void 0===i||i.setValue(e),this.closeOverlay()}switchToTaskNameEditMode(){this.isTaskNameEditInProgress=!0,setTimeout(e=>{const t=document.getElementById("#taskNameInput");t.focus(),t.select()},50)}switchToTaskNameViewMode(){var e,t,n;if(this.taskFormGroup.get("task_name").invalid||!this.taskFormGroup.get("task_name").value||0==(null===(n=null===(t=null===(e=this.taskFormGroup.get("task_name"))||void 0===e?void 0:e.value)||void 0===t?void 0:t.trim())||void 0===n?void 0:n.length))return this._toaster.showWarning("Warning \u26a0\ufe0f","Invalid Task Name!",7e3);this.isTaskNameEditInProgress=!1}onQuestionTypeChange(e,t){var n,o,i,a,r,l,c,s,C,g,p,m,f,u,h,M,_,v,O,x,k,P,y,w,b;if(null===(a=null===(i=null===(o=null===(n=this.taskFormGroup)||void 0===n?void 0:n.get("fields"))||void 0===o?void 0:o.get(t.toString()))||void 0===i?void 0:i.get("fieldType"))||void 0===a||a.setValue(e.val),["rich-text","number","text","url","date","file-upload"].includes(e.val))(null===(c=null===(l=null===(r=this.taskFormGroup)||void 0===r?void 0:r.get("fields"))||void 0===l?void 0:l.get(t.toString()))||void 0===c?void 0:c.get("fieldValues")).clear();else{const e=null===(g=null===(C=null===(s=this.taskFormGroup)||void 0===s?void 0:s.get("fields"))||void 0===C?void 0:C.get(t.toString()))||void 0===g?void 0:g.get("fieldValues");if(0==e.length){const t=this._fb.group({option:[null,[d.H.required,d.H.pattern(/\S/)]]});e.push(t)}}const S=this._fb.group({});this.fileTypesInFileUpload.forEach(e=>{S.addControl(e.id,this._fb.control(!1))}),null===(u=null===(f=null===(m=null===(p=this.taskFormGroup)||void 0===p?void 0:p.get("fields"))||void 0===m?void 0:m.get(t.toString()))||void 0===f?void 0:f.get("allowSpecificFiles"))||void 0===u||u.setValue(!1),null===(v=null===(_=null===(M=null===(h=this.taskFormGroup)||void 0===h?void 0:h.get("fields"))||void 0===M?void 0:M.get(t.toString()))||void 0===_?void 0:_.get("maxFileCount"))||void 0===v||v.setValue(this.fileCountInFileUpload&&this.fileCountInFileUpload.length>0?this.fileCountInFileUpload[0].id:null),null===(P=null===(k=null===(x=null===(O=this.taskFormGroup)||void 0===O?void 0:O.get("fields"))||void 0===x?void 0:x.get(t.toString()))||void 0===k?void 0:k.get("maxFileSize"))||void 0===P||P.setValue(this.fileSizeInFileUpload&&this.fileSizeInFileUpload.length>0?this.fileSizeInFileUpload[0].id:null),null===(b=null===(w=null===(y=this.taskFormGroup)||void 0===y?void 0:y.get("fields"))||void 0===w?void 0:w.get(t.toString()))||void 0===b||b.setControl("fileTypes",S)}onDeleteCustomQuestion(e){var t;(null===(t=this.taskFormGroup)||void 0===t?void 0:t.get("fields")).removeAt(e)}onCopyCustomQuestion(e){var t;const n=null===(t=this.taskFormGroup)||void 0===t?void 0:t.get("fields"),o=n.at(e),i=this.copyCustomQuestionForm(o);n.insert(e+1,i)}copyCustomQuestionForm(e){let t;if(e instanceof d.m){const n={};Object.keys(e.controls).forEach(t=>{n[t]=this.copyCustomQuestionForm(null==e?void 0:e.get(t))}),t=new d.m(n)}else if(e instanceof d.g){const n=e.controls.map(e=>this.copyCustomQuestionForm(e));t=new d.g(n)}else e instanceof d.j&&(t=new d.j(e.value),this.copyValidators(e,t));return t}copyValidators(e,t){const n=e.validator,o=e.asyncValidator;n&&t.setValidators(n||null),o&&t.setAsyncValidators(o||null)}addCustomQuestion(){var e;const t=this._fb.group({});this.fileTypesInFileUpload.forEach(e=>{t.addControl(e.id,this._fb.control(!1))});const n=null===(e=this.taskFormGroup)||void 0===e?void 0:e.get("fields"),o=this._fb.group({fieldType:[this.subTasksMasterData[0].id,d.H.required],label:[null,[d.H.required,d.H.pattern(/\S/)]],isMandatory:[!1],fieldValues:this._fb.array([]),allowSpecificFiles:[!1],maxFileCount:[this.fileCountInFileUpload&&this.fileCountInFileUpload.length>0?this.fileCountInFileUpload[0].id:null,d.H.required],maxFileSize:[this.fileSizeInFileUpload&&this.fileSizeInFileUpload.length>0?this.fileSizeInFileUpload[0].id:null,d.H.required],fileTypes:t});n.push(o)}addSingleCustomOption(e,t){var n,o,i;const a=null===(i=null===(o=null===(n=this.taskFormGroup)||void 0===n?void 0:n.get("fields"))||void 0===o?void 0:o.get(e.toString()))||void 0===i?void 0:i.get("fieldValues"),r=this._fb.group({option:[null,[d.H.required,d.H.pattern(/\S/)]]});a.insert(t,r)}deleteSingleCustomOption(e,t){var n,o,i;(null===(i=null===(o=null===(n=this.taskFormGroup)||void 0===n?void 0:n.get("fields"))||void 0===o?void 0:o.get(e.toString()))||void 0===i?void 0:i.get("fieldValues")).removeAt(t)}onChangeFileAccess(e){var t,n,o;const i=null===(o=null===(n=null===(t=this.taskFormGroup)||void 0===t?void 0:t.get("fields"))||void 0===n?void 0:n.get(e.toString()))||void 0===o?void 0:o.get("fileTypes");Object.keys(i.controls).forEach(e=>{i.get(e).setValue(!1)})}displaySupportedFormats(e,t){let n=t.getRawValue(),o=this.fileTypesInFileUpload;return o.forEach(e=>{e.isChecked=n[e.id]}),o=o.filter(e=>e.isChecked),e&&o&&o.length>0?o.map(e=>e.supportedFormats.join(", ")).join(", "):"Any"}onDataScroll(){this.skip+=this.limit,this.retrieveAllOnboardingTasks(!1)}markFormGroupTouched(e){Object.values(e.controls).forEach(e=>{e instanceof d.m?this.markFormGroupTouched(e):(e.markAsTouched(),e.markAllAsTouched(),e.markAsDirty())})}dragDropOption(e,t){var n;Object(g.h)((null===(n=this.taskFormGroup)||void 0===n?void 0:n.get("fields").get(t.toString()).get("fieldValues")).controls,e.previousIndex,e.currentIndex)}getAtsMasterUiConfig(e){return Object(a.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._masterDataService.getAtsMasterUiConfig(e).pipe(Object(l.a)(this._onDestroy)).subscribe({next:n=>{0==n.err?"onboardingTaskSettings"==e&&(this.fieldConfig=n.data.fieldConfig,this.uiTextConfig=n.data.uiTextConfig):this._toaster.showError("Error",n.msg,7e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Master Data Retrieval Failed!",7e3),n()}}))}))}retrieveAllOnboardingTasks(e){return Object(a.c)(this,void 0,void 0,(function*(){e&&(this.skip=0,this.isLoading=!0,this.data=[]);let t={skip:this.skip,limit:this.limit};return new Promise((e,n)=>this._onboardingSettingService.retrieveAllOnboardingTasks(t).pipe(Object(l.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.data=[...this.data,...t.data]:this._toaster.showError("Error",t.msg,7e3),this.isLoading=!1,e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Onboarding Tasks Data Retrieval Failed!",7e3),this.isLoading=!1,n()}}))}))}retrieveOnboardingTaskTypeMaster(){return Object(a.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._onboardingSettingService.retrieveOnboardingTaskTypeMaster({}).pipe(Object(l.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.taskTypeMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Onboarding Task Type Data Retrieval Failed!",7e3),t()}}))}))}retrieveOnboardingTaskStatusMaster(){return Object(a.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._onboardingSettingService.retrieveOnboardingTaskStatusMaster({}).pipe(Object(l.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.taskStatusMasterData=t.data.filter(e=>1!=e.id):this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Onboarding Task Status Data Retrieval Failed!",7e3),t()}}))}))}retrieveOnboardingSubTasksMaster(){return Object(a.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._onboardingSettingService.retrieveOnboardingSubTasksMaster({}).pipe(Object(l.a)(this._onDestroy)).subscribe({next:t=>{if(0==t.err){this.subTasksMasterData=t.data;let e=this.subTasksMasterData.find(e=>"file-upload"==e.id);this.fileTypesInFileUpload=null==e?void 0:e.allowed_files,this.fileCountInFileUpload=null==e?void 0:e.no_of_files,this.fileSizeInFileUpload=null==e?void 0:e.max_file_size}else this._toaster.showError("Error",t.msg,7e3);e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Onboarding Sub Task Data Retrieval Failed!",7e3),t()}}))}))}createAndUpdateOnboardingTask(e){return Object(a.c)(this,void 0,void 0,(function*(){return this.isSaveDisabled=!0,new Promise((t,n)=>this._onboardingSettingService.createAndUpdateOnboardingTask(e).pipe(Object(l.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?(this._toaster.showSuccess("Success \u2705",e.msg,7e3),"create"==this.currentView&&this.goBackToPreviousRoute()):this._toaster.showError("Error",e.msg,7e3),this.isSaveDisabled=!1,t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Task API Failed!",7e3),this.isSaveDisabled=!1,n()}}))}))}retrieveSingleOnboardingTaskData(e){return Object(a.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._onboardingSettingService.retrieveSingleOnboardingTaskData(e).pipe(Object(l.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?this.editTaskData=e.data:this._toaster.showError("Error",e.msg,7e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Task Retrieval Failed!",7e3),n()}}))}))}updateOnboardingTaskStatus(e){return Object(a.c)(this,void 0,void 0,(function*(){return this.isSaveDisabled=!0,this.isLoading=!0,new Promise((t,n)=>this._onboardingSettingService.updateOnboardingTaskStatus(e).pipe(Object(l.a)(this._onDestroy)).subscribe({next:n=>{0==n.err?(2==(null==e?void 0:e.task_status)?(this.displaySuccessMessage(),this.taskFormGroup.get("task_status").setValue(2)):3==(null==e?void 0:e.task_status)&&(this._toaster.showSuccess("Success \u2705",n.msg,7e3),this.taskFormGroup.get("task_status").setValue(3)),this.goBackToPreviousRoute()):this._toaster.showError("Error",n.msg,7e3),this.isSaveDisabled=!1,t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Task Status Updation Failed!",7e3),this.isSaveDisabled=!1,n()}}))}))}}return e.\u0275fac=function(t){return new(t||e)(m["\u0275\u0275directiveInject"](f.a),m["\u0275\u0275directiveInject"](u.a),m["\u0275\u0275directiveInject"](h.a),m["\u0275\u0275directiveInject"](d.i),m["\u0275\u0275directiveInject"](M.e),m["\u0275\u0275directiveInject"](m.ViewContainerRef),m["\u0275\u0275directiveInject"](_.b))},e.\u0275cmp=m["\u0275\u0275defineComponent"]({type:e,selectors:[["app-landing-page"]],viewQuery:function(e,t){if(1&e&&(m["\u0275\u0275viewQuery"](L,!0),m["\u0275\u0275viewQuery"](I,!0),m["\u0275\u0275viewQuery"](D,!0),m["\u0275\u0275viewQuery"](Z,!0)),2&e){let e;m["\u0275\u0275queryRefresh"](e=m["\u0275\u0275loadQuery"]())&&(t.triggerTaskTypeChange=e.first),m["\u0275\u0275queryRefresh"](e=m["\u0275\u0275loadQuery"]())&&(t.triggerActivateChange=e.first),m["\u0275\u0275queryRefresh"](e=m["\u0275\u0275loadQuery"]())&&(t.triggerCustomSelectDropdown=e.first),m["\u0275\u0275queryRefresh"](e=m["\u0275\u0275loadQuery"]())&&(t.triggerFileUploadSelectDropdown=e.first)}},hostBindings:function(e,t){1&e&&m["\u0275\u0275listener"]("resize",(function(){return t.onResize()}),!1,m["\u0275\u0275resolveWindow"])},decls:14,vars:24,consts:[[4,"ngIf"],["cdkConnectedOverlay","",3,"cdkConnectedOverlayOrigin"],["triggerTaskTypeChange",""],["triggerActivateChange",""],["triggerCustomSelectDropdown",""],["triggerFileUploadSelectDropdown",""],["mandatoryTemplate",""],[1,"bg-container"],[1,"header"],[1,"header-1"],[1,"back-icon",3,"click"],[1,"content"],[1,"content-title"],[1,"content-sub-title",3,"matTooltip"],[1,"header-2"],["class","preview",3,"click",4,"ngIf"],["class","save-btn",3,"ngClass","click",4,"ngIf"],["class","activate-btn","cdkOverlayOrigin","",3,"ngClass","click",4,"ngIf"],[1,"task-container"],["class","task-header",4,"ngIf"],["class","task-list",4,"ngIf"],["class","form",3,"formGroup",4,"ngIf"],["class","preview-header",4,"ngIf"],["class","preview-content",3,"formGroup",4,"ngIf"],["class","loading-state-ui",4,"ngIf"],[1,"preview",3,"click"],[1,"save-btn",3,"ngClass","click"],["cdkOverlayOrigin","",1,"activate-btn",3,"ngClass","click"],["triggerActivate","cdkOverlayOrigin","triggerActivateField",""],[1,"icon"],[1,"task-header"],[1,"task-header-1"],[1,"task-header-2"],["class","add-task",3,"click",4,"ngIf"],[1,"add-task",3,"click"],[1,"task-list"],[3,"list","fieldConfig","variant","isCheckboxActive","onScroll","onClick",4,"ngIf"],["class","task-list-empty",4,"ngIf"],[3,"list","fieldConfig","variant","isCheckboxActive","onScroll","onClick"],[1,"task-list-empty"],["src","https://assets.kebs.app/ATS-Empty-State.png"],[1,"no-data-title"],[2,"max-width","60px","max-height","60px"],[2,"max-width","100%","max-height","100%",3,"src"],[1,"loading-wrapper"],[1,"loading"],[1,"form",3,"formGroup"],[1,"create-task-header"],[1,"create-task-header-1"],["class","view-task",4,"ngIf"],["class","edit-task",4,"ngIf"],[1,"task-name-desc"],[1,"create-task-header-2"],["class","reset",3,"click",4,"ngIf"],[1,"task-type"],[1,"type-text"],["cdkOverlayOrigin","",1,"type",3,"click"],["triggerTaskType","cdkOverlayOrigin","triggerTaskTypeField",""],[1,"create-task-content"],["formArrayName","fields"],[3,"formGroupName",4,"ngFor","ngForOf"],[1,"single-field",3,"click"],[1,"index"],[1,"add-content"],[1,"view-task"],[1,"task-name",3,"matTooltip"],[1,"edit-icon",3,"click"],[1,"edit-task"],["type","text","maxlength","75","formControlName","task_name","placeholder","Enter Task Name",3,"id","ngStyle","keydown.enter"],[1,"tick-icon",3,"click"],[1,"reset",3,"click"],[3,"formGroupName"],[1,"single-field"],[1,"d-flex","justify-content-between",2,"margin-bottom","12px"],[1,"input-field"],["type","text","formControlName","label","placeholder","Untitled Question","maxlength","255",3,"ngStyle"],[2,"width","20%"],[3,"masterData","selectedValue","displayClose","onValueChange"],[1,"d-flex","flex-column",3,"ngStyle"],["formArrayName","fieldValues",4,"ngIf"],[1,"custom-icons"],["formControlName","isMandatory"],[1,"mand-text"],[1,"svg",3,"click"],["width","24","height","24","viewBox","0 0 24 24","fill","none"],["opacity","0.5"],["id","mask0_10914_66850","maskUnits","userSpaceOnUse","x","0","y","0","width","24","height","24",2,"mask-type","alpha"],["width","24","height","24","fill","#D9D9D9"],["mask","url(#mask0_10914_66850)"],["d","M7.3077 20.4998C6.81058 20.4998 6.38502 20.3228 6.03102 19.9688C5.67701 19.6148 5.5 19.1892 5.5 18.6921V5.99981H5.25C5.0375 5.99981 4.85938 5.9279 4.71563 5.78408C4.57188 5.64028 4.5 5.46208 4.5 5.24948C4.5 5.0369 4.57188 4.85882 4.71563 4.71523C4.85938 4.57163 5.0375 4.49983 5.25 4.49983H8.99997C8.99997 4.25497 9.08619 4.04632 9.25863 3.87388C9.43106 3.70145 9.63971 3.61523 9.88457 3.61523H14.1154C14.3602 3.61523 14.5689 3.70145 14.7413 3.87388C14.9138 4.04632 15 4.25497 15 4.49983H18.75C18.9625 4.49983 19.1406 4.57174 19.2843 4.71556C19.4281 4.85938 19.5 5.03758 19.5 5.25016C19.5 5.46276 19.4281 5.64085 19.2843 5.78443C19.1406 5.92802 18.9625 5.99981 18.75 5.99981H18.5V18.6921C18.5 19.1892 18.3229 19.6148 17.9689 19.9688C17.6149 20.3228 17.1894 20.4998 16.6922 20.4998H7.3077ZM17 5.99981H6.99997V18.6921C6.99997 18.7818 7.02883 18.8556 7.08652 18.9133C7.14422 18.971 7.21795 18.9998 7.3077 18.9998H16.6922C16.782 18.9998 16.8557 18.971 16.9134 18.9133C16.9711 18.8556 17 18.7818 17 18.6921V5.99981ZM10.1542 16.9998C10.3668 16.9998 10.5448 16.9279 10.6884 16.7842C10.832 16.6404 10.9038 16.4623 10.9038 16.2498V8.74979C10.9038 8.5373 10.8319 8.35918 10.6881 8.21543C10.5443 8.07168 10.3661 7.99981 10.1535 7.99981C9.9409 7.99981 9.76281 8.07168 9.61922 8.21543C9.47564 8.35918 9.40385 8.5373 9.40385 8.74979V16.2498C9.40385 16.4623 9.47576 16.6404 9.61958 16.7842C9.76337 16.9279 9.94158 16.9998 10.1542 16.9998ZM13.8464 16.9998C14.059 16.9998 14.2371 16.9279 14.3807 16.7842C14.5243 16.6404 14.5961 16.4623 14.5961 16.2498V8.74979C14.5961 8.5373 14.5242 8.35918 14.3804 8.21543C14.2366 8.07168 14.0584 7.99981 13.8458 7.99981C13.6332 7.99981 13.4551 8.07168 13.3115 8.21543C13.1679 8.35918 13.0961 8.5373 13.0961 8.74979V16.2498C13.0961 16.4623 13.168 16.6404 13.3118 16.7842C13.4557 16.9279 13.6339 16.9998 13.8464 16.9998Z","fill","#1C1B1F"],["opacity","0.3"],["id","mask0_10914_66847","maskUnits","userSpaceOnUse","x","0","y","0","width","24","height","24",2,"mask-type","alpha"],["mask","url(#mask0_10914_66847)"],["d","M9.05765 17.5C8.55252 17.5 8.12496 17.325 7.77498 16.975C7.42498 16.625 7.24998 16.1974 7.24998 15.6923V4.3077C7.24998 3.80257 7.42498 3.375 7.77498 3.025C8.12496 2.675 8.55252 2.5 9.05765 2.5H17.4422C17.9473 2.5 18.3749 2.675 18.7249 3.025C19.0749 3.375 19.2499 3.80257 19.2499 4.3077V15.6923C19.2499 16.1974 19.0749 16.625 18.7249 16.975C18.3749 17.325 17.9473 17.5 17.4422 17.5H9.05765ZM9.05765 16H17.4422C17.5191 16 17.5897 15.9679 17.6538 15.9038C17.7179 15.8397 17.7499 15.7692 17.7499 15.6923V4.3077C17.7499 4.23077 17.7179 4.16024 17.6538 4.09613C17.5897 4.03203 17.5191 3.99998 17.4422 3.99998H9.05765C8.98072 3.99998 8.9102 4.03203 8.8461 4.09613C8.78198 4.16024 8.74993 4.23077 8.74993 4.3077V15.6923C8.74993 15.7692 8.78198 15.8397 8.8461 15.9038C8.9102 15.9679 8.98072 16 9.05765 16ZM5.55768 20.9999C5.05256 20.9999 4.625 20.8249 4.275 20.4749C3.925 20.1249 3.75 19.6973 3.75 19.1922V7.05768C3.75 6.84486 3.82179 6.66666 3.96538 6.52308C4.10898 6.37949 4.28718 6.3077 4.49998 6.3077C4.71279 6.3077 4.891 6.37949 5.0346 6.52308C5.17818 6.66666 5.24998 6.84486 5.24998 7.05768V19.1922C5.24998 19.2692 5.28202 19.3397 5.34613 19.4038C5.41024 19.4679 5.48076 19.5 5.55768 19.5H14.6922C14.905 19.5 15.0832 19.5717 15.2268 19.7153C15.3704 19.8589 15.4422 20.0371 15.4422 20.2499C15.4422 20.4627 15.3704 20.6409 15.2268 20.7845C15.0832 20.9281 14.905 20.9999 14.6922 20.9999H5.55768Z","fill","#1C1B1F"],["width","70%","height","108","viewBox","0 0 733 108","fill","none"],["d","M0.315 4C0.315 1.96483 1.96483 0.315 4 0.315H729C731.035 0.315 732.685 1.96483 732.685 4V29C732.685 31.0352 731.035 32.685 729 32.685H4.00001C1.96484 32.685 0.315 31.0352 0.315 29V4Z","fill","#F6F6F7"],["d","M0.315 4C0.315 1.96483 1.96483 0.315 4 0.315H729C731.035 0.315 732.685 1.96483 732.685 4V29C732.685 31.0352 731.035 32.685 729 32.685H4.00001C1.96484 32.685 0.315 31.0352 0.315 29V4Z","stroke","#D2D2D2","stroke-width","0.63"],["d","M11.482 19.291V12.3493H13.7232C14.2653 12.3493 14.7082 12.4386 15.052 12.6171C15.3958 12.7956 15.6503 13.0402 15.8156 13.3509C15.9809 13.6617 16.0635 14.0054 16.0635 14.3823C16.0635 14.8186 15.9445 15.212 15.7065 15.5623C15.4751 15.9127 15.1115 16.1607 14.6157 16.3061L16.1329 19.291H15.1512L13.743 16.4251H13.6835H12.315V19.291H11.482ZM12.315 15.7706H13.6637C14.1992 15.7706 14.5892 15.6417 14.8338 15.3838C15.0784 15.126 15.2008 14.7955 15.2008 14.3922C15.2008 13.9823 15.0784 13.6583 14.8338 13.4203C14.5958 13.1757 14.2025 13.0534 13.6538 13.0534H12.315V15.7706ZM19.2517 19.41C18.7889 19.41 18.3724 19.3042 18.0022 19.0927C17.632 18.8811 17.3378 18.5836 17.1196 18.2002C16.9081 17.8101 16.8023 17.354 16.8023 16.8317C16.8023 16.3094 16.9114 15.8565 17.1295 15.4731C17.3477 15.083 17.6419 14.7822 18.0121 14.5707C18.389 14.3591 18.8088 14.2533 19.2715 14.2533C19.7343 14.2533 20.1508 14.3591 20.521 14.5707C20.8913 14.7822 21.1822 15.083 21.3937 15.4731C21.6119 15.8565 21.721 16.3094 21.721 16.8317C21.721 17.354 21.6119 17.8101 21.3937 18.2002C21.1755 18.5836 20.878 18.8811 20.5012 19.0927C20.131 19.3042 19.7145 19.41 19.2517 19.41ZM19.2517 18.696C19.536 18.696 19.8004 18.6266 20.045 18.4878C20.2897 18.3489 20.488 18.1407 20.64 17.863C20.7921 17.5853 20.8681 17.2416 20.8681 16.8317C20.8681 16.4218 20.7921 16.078 20.64 15.8003C20.4946 15.5227 20.2996 15.3144 20.055 15.1756C19.8104 15.0368 19.5492 14.9673 19.2715 14.9673C18.9873 14.9673 18.7228 15.0368 18.4782 15.1756C18.2336 15.3144 18.0353 15.5227 17.8832 15.8003C17.7312 16.078 17.6551 16.4218 17.6551 16.8317C17.6551 17.2416 17.7312 17.5853 17.8832 17.863C18.0353 18.1407 18.2303 18.3489 18.4683 18.4878C18.7129 18.6266 18.974 18.696 19.2517 18.696ZM25.5071 19.41C25.1038 19.41 24.7435 19.3274 24.4262 19.1621C24.1155 18.9968 23.8775 18.7654 23.7122 18.4679L23.6329 19.291H22.8792V12.151H23.7122V15.1855C23.8709 14.9475 24.0956 14.7327 24.3865 14.5409C24.684 14.3492 25.0609 14.2533 25.517 14.2533C26.0063 14.2533 26.4327 14.3657 26.7963 14.5905C27.1599 14.8153 27.4409 15.1227 27.6392 15.5128C27.8441 15.9028 27.9466 16.3458 27.9466 16.8416C27.9466 17.3374 27.8441 17.7804 27.6392 18.1704C27.4409 18.5539 27.1566 18.858 26.7864 19.0828C26.4228 19.3009 25.9963 19.41 25.5071 19.41ZM25.4179 18.6861C25.7484 18.6861 26.0393 18.6101 26.2905 18.458C26.5418 18.2993 26.7401 18.0812 26.8855 17.8035C27.031 17.5258 27.1037 17.2019 27.1037 16.8317C27.1037 16.4615 27.031 16.1375 26.8855 15.8598C26.7401 15.5822 26.5418 15.3673 26.2905 15.2153C26.0393 15.0566 25.7484 14.9773 25.4179 14.9773C25.0873 14.9773 24.7964 15.0566 24.5452 15.2153C24.294 15.3673 24.0956 15.5822 23.9502 15.8598C23.8048 16.1375 23.732 16.4615 23.732 16.8317C23.732 17.2019 23.8048 17.5258 23.9502 17.8035C24.0956 18.0812 24.294 18.2993 24.5452 18.458C24.7964 18.6101 25.0873 18.6861 25.4179 18.6861ZM31.3473 19.41C30.8846 19.41 30.4681 19.3042 30.0978 19.0927C29.7276 18.8811 29.4334 18.5836 29.2152 18.2002C29.0037 17.8101 28.8979 17.354 28.8979 16.8317C28.8979 16.3094 29.007 15.8565 29.2252 15.4731C29.4433 15.083 29.7375 14.7822 30.1077 14.5707C30.4846 14.3591 30.9044 14.2533 31.3672 14.2533C31.8299 14.2533 32.2464 14.3591 32.6167 14.5707C32.9869 14.7822 33.2778 15.083 33.4893 15.4731C33.7075 15.8565 33.8166 16.3094 33.8166 16.8317C33.8166 17.354 33.7075 17.8101 33.4893 18.2002C33.2712 18.5836 32.9737 18.8811 32.5968 19.0927C32.2266 19.3042 31.8101 19.41 31.3473 19.41ZM31.3473 18.696C31.6316 18.696 31.8961 18.6266 32.1407 18.4878C32.3853 18.3489 32.5836 18.1407 32.7357 17.863C32.8877 17.5853 32.9637 17.2416 32.9637 16.8317C32.9637 16.4218 32.8877 16.078 32.7357 15.8003C32.5902 15.5227 32.3952 15.3144 32.1506 15.1756C31.906 15.0368 31.6448 14.9673 31.3672 14.9673C31.0829 14.9673 30.8184 15.0368 30.5738 15.1756C30.3292 15.3144 30.1309 15.5227 29.9788 15.8003C29.8268 16.078 29.7507 16.4218 29.7507 16.8317C29.7507 17.2416 29.8268 17.5853 29.9788 17.863C30.1309 18.1407 30.3259 18.3489 30.5639 18.4878C30.8085 18.6266 31.0697 18.696 31.3473 18.696ZM36.7248 19.291C36.2752 19.291 35.9216 19.1819 35.6637 18.9638C35.4059 18.7456 35.277 18.3522 35.277 17.7837V15.0764H34.4241V14.3723H35.277L35.3861 13.1923H36.11V14.3723H37.5578V15.0764H36.11V17.7837C36.11 18.0944 36.1728 18.306 36.2984 18.4183C36.424 18.5241 36.6455 18.577 36.9628 18.577H37.4785V19.291H36.7248ZM40.7217 19.41C40.2589 19.41 39.8424 19.3042 39.4722 19.0927C39.102 18.8811 38.8078 18.5836 38.5896 18.2002C38.378 17.8101 38.2723 17.354 38.2723 16.8317C38.2723 16.3094 38.3813 15.8565 38.5995 15.4731C38.8177 15.083 39.1119 14.7822 39.4821 14.5707C39.8589 14.3591 40.2787 14.2533 40.7415 14.2533C41.2043 14.2533 41.6208 14.3591 41.991 14.5707C42.3612 14.7822 42.6521 15.083 42.8637 15.4731C43.0818 15.8565 43.1909 16.3094 43.1909 16.8317C43.1909 17.354 43.0818 17.8101 42.8637 18.2002C42.6455 18.5836 42.348 18.8811 41.9712 19.0927C41.601 19.3042 41.1845 19.41 40.7217 19.41ZM40.7217 18.696C41.006 18.696 41.2704 18.6266 41.515 18.4878C41.7596 18.3489 41.958 18.1407 42.11 17.863C42.2621 17.5853 42.3381 17.2416 42.3381 16.8317C42.3381 16.4218 42.2621 16.078 42.11 15.8003C41.9646 15.5227 41.7695 15.3144 41.5249 15.1756C41.2803 15.0368 41.0192 14.9673 40.7415 14.9673C40.4572 14.9673 40.1928 15.0368 39.9482 15.1756C39.7036 15.3144 39.5052 15.5227 39.3532 15.8003C39.2011 16.078 39.1251 16.4218 39.1251 16.8317C39.1251 17.2416 39.2011 17.5853 39.3532 17.863C39.5052 18.1407 39.7003 18.3489 39.9383 18.4878C40.1829 18.6266 40.444 18.696 40.7217 18.696Z","fill","#515965"],["d","M67.3998 17.0473L64.9561 19.491L62.5123 17.0473L61.7686 17.791L64.9561 20.9785L68.1436 17.791L67.3998 17.0473ZM67.3998 14.2848L64.9561 11.841L62.5123 14.2848L61.7686 13.541L64.9561 10.3535L68.1436 13.541L67.3998 14.2848Z","fill","#515965"],["d","M90.1942 19.291V12.3493H91.0272L94.7261 17.9126V12.3493H95.5591V19.291H94.7261L91.0272 13.7278V19.291H90.1942ZM99.2228 19.41C98.76 19.41 98.3435 19.3042 97.9733 19.0927C97.6031 18.8811 97.3089 18.5836 97.0907 18.2002C96.8792 17.8101 96.7734 17.354 96.7734 16.8317C96.7734 16.3094 96.8825 15.8565 97.1007 15.4731C97.3188 15.083 97.613 14.7822 97.9832 14.5707C98.3601 14.3591 98.7799 14.2533 99.2427 14.2533C99.7054 14.2533 100.122 14.3591 100.492 14.5707C100.862 14.7822 101.153 15.083 101.365 15.4731C101.583 15.8565 101.692 16.3094 101.692 16.8317C101.692 17.354 101.583 17.8101 101.365 18.2002C101.147 18.5836 100.849 18.8811 100.472 19.0927C100.102 19.3042 99.6856 19.41 99.2228 19.41ZM99.2228 18.696C99.5071 18.696 99.7715 18.6266 100.016 18.4878C100.261 18.3489 100.459 18.1407 100.611 17.863C100.763 17.5853 100.839 17.2416 100.839 16.8317C100.839 16.4218 100.763 16.078 100.611 15.8003C100.466 15.5227 100.271 15.3144 100.026 15.1756C99.7815 15.0368 99.5203 14.9673 99.2427 14.9673C98.9584 14.9673 98.6939 15.0368 98.4493 15.1756C98.2047 15.3144 98.0064 15.5227 97.8543 15.8003C97.7023 16.078 97.6262 16.4218 97.6262 16.8317C97.6262 17.2416 97.7023 17.5853 97.8543 17.863C98.0064 18.1407 98.2014 18.3489 98.4394 18.4878C98.684 18.6266 98.9452 18.696 99.2228 18.696ZM102.85 19.291V14.3723H103.604L103.673 15.3144C103.825 14.9905 104.057 14.7327 104.368 14.5409C104.678 14.3492 105.062 14.2533 105.518 14.2533V15.126H105.29C104.999 15.126 104.731 15.1789 104.487 15.2847C104.242 15.3838 104.047 15.5557 103.901 15.8003C103.756 16.045 103.683 16.3821 103.683 16.8118V19.291H102.85ZM106.453 19.291V14.3723H107.207L107.266 15.0863C107.425 14.8219 107.636 14.617 107.901 14.4715C108.165 14.3261 108.463 14.2533 108.793 14.2533C109.183 14.2533 109.517 14.3327 109.795 14.4913C110.079 14.65 110.297 14.8913 110.449 15.2153C110.621 14.9178 110.856 14.6831 111.153 14.5112C111.457 14.3393 111.785 14.2533 112.135 14.2533C112.723 14.2533 113.193 14.4318 113.543 14.7888C113.894 15.1392 114.069 15.6813 114.069 16.4152V19.291H113.246V16.5044C113.246 15.9954 113.143 15.6119 112.938 15.3541C112.733 15.0963 112.439 14.9673 112.056 14.9673C111.659 14.9673 111.329 15.1227 111.064 15.4334C110.806 15.7375 110.677 16.1739 110.677 16.7424V19.291H109.844V16.5044C109.844 15.9954 109.742 15.6119 109.537 15.3541C109.332 15.0963 109.038 14.9673 108.654 14.9673C108.264 14.9673 107.937 15.1227 107.673 15.4334C107.415 15.7375 107.286 16.1739 107.286 16.7424V19.291H106.453ZM116.96 19.41C116.55 19.41 116.21 19.3406 115.938 19.2018C115.667 19.0629 115.466 18.8778 115.334 18.6464C115.201 18.415 115.135 18.1638 115.135 17.8928C115.135 17.3903 115.327 17.0036 115.71 16.7325C116.094 16.4615 116.616 16.3259 117.277 16.3259H118.606V16.2664C118.606 15.8367 118.494 15.5128 118.269 15.2946C118.044 15.0698 117.743 14.9574 117.366 14.9574C117.043 14.9574 116.762 15.0401 116.524 15.2053C116.292 15.364 116.147 15.5987 116.087 15.9094H115.234C115.267 15.5524 115.386 15.2516 115.591 15.007C115.803 14.7624 116.064 14.5773 116.375 14.4517C116.686 14.3195 117.016 14.2533 117.366 14.2533C118.054 14.2533 118.57 14.4385 118.913 14.8087C119.264 15.1723 119.439 15.6582 119.439 16.2664V19.291H118.695L118.646 18.4084C118.507 18.6861 118.302 18.9241 118.031 19.1224C117.766 19.3142 117.409 19.41 116.96 19.41ZM117.089 18.7059C117.406 18.7059 117.677 18.6233 117.902 18.458C118.133 18.2927 118.309 18.0779 118.428 17.8134C118.547 17.549 118.606 17.2713 118.606 16.9804V16.9705H117.347C116.857 16.9705 116.51 17.0565 116.305 17.2283C116.107 17.3936 116.008 17.6019 116.008 17.8531C116.008 18.1109 116.1 18.3192 116.286 18.4778C116.477 18.6299 116.745 18.7059 117.089 18.7059ZM120.689 19.291V12.151H121.522V19.291H120.689Z","fill","#515965"],["d","M146.026 17.0473L143.582 19.491L141.138 17.0473L140.395 17.791L143.582 20.9785L146.77 17.791L146.026 17.0473ZM146.026 14.2848L143.582 11.841L141.138 14.2848L140.395 13.541L143.582 10.3535L146.77 13.541L146.026 14.2848Z","fill","#515965"],["d","M171.808 16.3938C172.068 16.2354 172.288 16.0184 172.449 15.7601C172.61 15.5019 172.709 15.2095 172.738 14.9062C172.743 14.6321 172.693 14.3596 172.593 14.1044C172.493 13.8492 172.343 13.6163 172.153 13.419C171.962 13.2216 171.735 13.0637 171.483 12.9543C171.232 12.8449 170.961 12.7861 170.687 12.7812H167.239V20.2188H170.958C171.219 20.216 171.477 20.1618 171.717 20.0594C171.957 19.957 172.174 19.8083 172.357 19.6218C172.539 19.4353 172.683 19.2147 172.781 18.9726C172.878 18.7305 172.926 18.4715 172.924 18.2106V18.1469C172.924 17.7788 172.819 17.4183 172.621 17.1077C172.424 16.7972 172.142 16.5495 171.808 16.3938ZM168.302 13.8438H170.533C170.759 13.8367 170.983 13.8992 171.172 14.0226C171.362 14.146 171.51 14.3246 171.596 14.5344C171.682 14.8148 171.654 15.118 171.518 15.3778C171.381 15.6376 171.148 15.8329 170.868 15.9209C170.759 15.9528 170.646 15.9689 170.533 15.9688H168.302V13.8438ZM170.746 19.1562H168.302V17.0312H170.746C170.972 17.0242 171.195 17.0867 171.385 17.2101C171.575 17.3335 171.722 17.5121 171.808 17.7219C171.895 18.0023 171.867 18.3055 171.73 18.5653C171.594 18.8251 171.36 19.0204 171.08 19.1084C170.971 19.1403 170.859 19.1564 170.746 19.1562Z","fill","#515965"],["d","M189.787 14.9062H190.85L189.681 20.2188H188.619L189.787 14.9062ZM190.68 12.7812C190.575 12.7812 190.472 12.8124 190.385 12.8708C190.297 12.9292 190.229 13.0121 190.189 13.1092C190.149 13.2063 190.138 13.3131 190.159 13.4161C190.179 13.5192 190.23 13.6139 190.304 13.6882C190.379 13.7624 190.473 13.813 190.576 13.8335C190.679 13.854 190.786 13.8435 190.883 13.8033C190.98 13.7631 191.063 13.695 191.122 13.6076C191.18 13.5203 191.211 13.4176 191.211 13.3125C191.211 13.1716 191.155 13.0365 191.056 12.9368C190.956 12.8372 190.821 12.7812 190.68 12.7812Z","fill","#515965"],["d","M213.467 20.75V21.8125H206.03V20.75H213.467ZM211.874 17.1455C211.856 17.4961 211.752 17.8369 211.571 18.1375C211.389 18.438 211.136 18.689 210.834 18.8679C210.532 19.0468 210.19 19.1481 209.84 19.1627C209.489 19.1774 209.14 19.1049 208.824 18.9517C208.46 18.7942 208.151 18.5316 207.937 18.1975C207.723 17.8634 207.614 17.473 207.624 17.0764V12.7839H206.561V17.1455C206.579 17.6456 206.714 18.1345 206.957 18.5725C207.199 19.0105 207.54 19.3854 207.954 19.6668C208.368 19.9482 208.842 20.1282 209.339 20.1923C209.835 20.2563 210.339 20.2026 210.811 20.0355C211.438 19.8263 211.983 19.4225 212.365 18.8827C212.747 18.3429 212.947 17.6952 212.936 17.0339V12.7839H211.874V17.1455ZM211.874 12.7812H212.936H211.874ZM207.624 12.7812H206.561H207.624Z","fill","#515965"],["d","M225.332 16.6445H238.082V17.707H234.989C235.157 18.0592 235.242 18.4452 235.237 18.8353C235.247 19.2054 235.167 19.5724 235.005 19.9053C234.843 20.2382 234.603 20.5271 234.306 20.7476C233.589 21.2517 232.725 21.5021 231.85 21.4591C231.211 21.4634 230.578 21.3333 229.992 21.0773C229.457 20.8554 228.993 20.4907 228.651 20.0232C228.336 19.5768 228.17 19.0426 228.176 18.4964V18.4161H228.38V18.4153H228.622V18.4161H229.596V18.4964C229.585 18.7453 229.634 18.9932 229.739 19.2189C229.845 19.4447 230.003 19.6417 230.201 19.7931C230.681 20.1347 231.262 20.3043 231.85 20.2743C232.369 20.3082 232.886 20.1724 233.321 19.8874C233.482 19.768 233.612 19.6109 233.699 19.4298C233.785 19.2487 233.826 19.0491 233.818 18.8486C233.829 18.6548 233.793 18.4612 233.713 18.2842C233.634 18.1073 233.512 17.9523 233.359 17.8325C233.297 17.7872 233.232 17.7453 233.165 17.707H225.332V16.6445ZM234.784 13.0015C234.485 12.5375 234.063 12.1662 233.565 11.9293C233.009 11.6647 232.4 11.5323 231.785 11.5424C230.927 11.5056 230.084 11.772 229.402 12.2948C229.11 12.5206 228.874 12.8114 228.713 13.1441C228.553 13.4768 228.472 13.8424 228.477 14.2118C228.474 14.5619 228.552 14.908 228.704 15.2232H230.544C230.485 15.1852 230.412 15.1483 230.365 15.1094C230.221 15.0031 230.104 14.8643 230.023 14.7042C229.943 14.5442 229.901 14.3674 229.902 14.1883C229.892 13.9839 229.93 13.7799 230.015 13.5936C230.1 13.4073 230.229 13.2443 230.39 13.1182C230.798 12.8352 231.289 12.6974 231.785 12.7269C232.324 12.6926 232.856 12.8616 233.277 13.2005C233.453 13.3662 233.59 13.568 233.68 13.7922C233.77 14.0163 233.811 14.2574 233.798 14.4986V14.579H235.217V14.4986C235.219 13.9684 235.069 13.4486 234.784 13.0015Z","fill","#515965"],["clip-path","url(#clip0_10914_44976)"],["d","M260.324 17.2937C260.324 17.2937 258.411 19.3656 258.411 20.6406C258.408 20.8952 258.454 21.148 258.549 21.3846C258.643 21.6212 258.783 21.8369 258.96 22.0195C259.319 22.3881 259.809 22.5991 260.324 22.6062C260.838 22.6132 261.334 22.4157 261.703 22.057C261.885 21.8794 262.031 21.6676 262.131 21.4337C262.232 21.1998 262.286 20.9483 262.289 20.6937V20.6406C262.289 19.3656 260.324 17.2937 260.324 17.2937ZM259.543 16.5728L259.898 16.1882L258.677 12.7812H257.614L254.958 20.2188H256.021L256.78 18.0938H258.344C258.704 17.5577 259.104 17.0493 259.541 16.5728H259.543ZM258.146 14.2688L259.134 17.0312H257.16L258.146 14.2688Z","fill","#515965"],["d","M277.351 16.6288L281.467 13.7652L282.093 14.4537L278.856 18.2827L277.351 16.6288ZM276.113 16.8472L278.522 19.4946C278.572 19.544 278.632 19.5822 278.698 19.6068C278.764 19.6314 278.834 19.6418 278.904 19.6372C278.974 19.6326 279.042 19.6133 279.104 19.5804C279.166 19.5474 279.221 19.5017 279.264 19.4463L283.222 14.7644C283.301 14.6777 283.346 14.5653 283.348 14.4482C283.35 14.3311 283.309 14.2171 283.233 14.1277L281.898 12.6609C281.817 12.5771 281.707 12.526 281.59 12.517C281.473 12.5081 281.357 12.542 281.264 12.6124L276.231 16.1137C276.171 16.1514 276.121 16.2012 276.082 16.2598C276.044 16.3185 276.018 16.3847 276.007 16.454C275.996 16.5233 275.999 16.5942 276.018 16.662C276.036 16.7298 276.068 16.7929 276.113 16.8472ZM273.566 19.9687L276.551 20.4852L277.336 19.7701L275.727 18.0021L273.566 19.9687Z","fill","#515965"],["d","M296.785 18.625H297.847V18.8906H297.316V19.4219H297.847V19.6875H296.785V20.2188H298.378V18.0938H296.785V18.625ZM297.316 14.9062H297.847V12.7812H296.785V13.3125H297.316V14.9062ZM296.785 15.9688H297.741L296.785 17.0844V17.5625H298.378V17.0312H297.422L298.378 15.9156V15.4375H296.785V15.9688ZM299.441 13.3125V14.375H306.878V13.3125H299.441ZM299.441 19.6875H306.878V18.625H299.441V19.6875ZM299.441 17.0312H306.878V15.9688H299.441V17.0312Z","fill","#515965"],["d","M317.415 15.7031C316.99 15.7031 316.618 16.075 316.618 16.5C316.618 16.925 316.99 17.2969 317.415 17.2969C317.84 17.2969 318.212 16.925 318.212 16.5C318.212 16.075 317.84 15.7031 317.415 15.7031ZM317.415 13.0469C316.99 13.0469 316.618 13.4187 316.618 13.8438C316.618 14.2687 316.99 14.6406 317.415 14.6406C317.84 14.6406 318.212 14.2687 318.212 13.8438C318.212 13.4187 317.84 13.0469 317.415 13.0469ZM317.415 18.3594C316.99 18.3594 316.618 18.7312 316.618 19.1562C316.618 19.5813 316.99 19.9531 317.415 19.9531C317.84 19.9531 318.212 19.5813 318.212 19.1562C318.212 18.7312 317.84 18.3594 317.415 18.3594ZM319.274 13.3125V14.375H326.712V13.3125H319.274ZM319.274 19.6875H326.712V18.625H319.274V19.6875ZM319.274 17.0312H326.712V15.9688H319.274V17.0312Z","fill","#515965"],["d","M336.717 16.5L338.311 18.0938V14.9062L336.717 16.5ZM336.717 20.2188H346.279V19.1562H336.717V20.2188ZM336.717 13.8438H346.279V12.7812H336.717V13.8438ZM339.904 15.9688H346.279V14.9062H339.904V15.9688ZM339.904 18.0938H346.279V17.0312H339.904V18.0938Z","fill","#515965"],["d","M356.55 14.9062V18.0938L358.144 16.5L356.55 14.9062ZM356.55 20.2188H366.113V19.1562H356.55V20.2188ZM356.55 13.8438H366.113V12.7812H356.55V13.8438ZM359.738 15.9688H366.113V14.9062H359.738V15.9688ZM359.738 18.0938H366.113V17.0312H359.738V18.0938Z","fill","#515965"],["d","M383.857 16.5L385.769 18.4125L385.025 19.1562L383.113 17.2437L381.2 19.1562L380.457 18.4125L382.369 16.5L380.457 14.5875L381.2 13.8438L383.113 15.7563L385.025 13.8438L385.769 14.5875L383.857 16.5ZM388.06 20.5768L388.627 19.9677C388.732 19.8607 388.824 19.7513 388.913 19.6443C388.997 19.5412 389.074 19.4316 389.142 19.3165C389.206 19.2102 389.258 19.0974 389.298 18.98C389.335 18.8659 389.354 18.7464 389.354 18.6261C389.356 18.4766 389.328 18.3283 389.273 18.1894C389.22 18.0588 389.139 17.9422 389.034 17.8485C388.922 17.751 388.792 17.6775 388.651 17.6326C388.312 17.5316 387.95 17.5399 387.616 17.6563C387.463 17.7139 387.325 17.8036 387.21 17.9192C387.099 18.03 387.013 18.1632 386.958 18.31C386.904 18.4515 386.876 18.6013 386.875 18.7525L386.873 18.8784H387.691L387.694 18.7578C387.695 18.6905 387.704 18.6237 387.723 18.5588C387.74 18.502 387.769 18.4489 387.806 18.4025C387.844 18.3598 387.891 18.3263 387.943 18.3047C388.063 18.2574 388.195 18.2546 388.316 18.2969C388.36 18.3161 388.399 18.3445 388.43 18.3802C388.463 18.4183 388.488 18.4622 388.504 18.5095C388.522 18.567 388.532 18.6271 388.533 18.6876C388.532 18.7352 388.526 18.7826 388.516 18.829C388.5 18.8844 388.478 18.9377 388.45 18.9878C388.409 19.0602 388.362 19.1292 388.31 19.1941C388.238 19.2869 388.162 19.3759 388.082 19.4609L386.936 20.711V21.2812H389.488V20.5768H388.06Z","fill","#515965"],["d","M403.69 16.5L405.603 18.4125L404.859 19.1562L402.946 17.2437L401.034 19.1562L400.29 18.4125L402.203 16.5L400.29 14.5875L401.034 13.8437L402.946 15.7562L404.859 13.8437L405.603 14.5875L403.69 16.5ZM408.425 14.733L408.992 14.124C409.096 14.017 409.189 13.9075 409.277 13.8005C409.362 13.6974 409.439 13.5878 409.507 13.4728C409.57 13.3664 409.623 13.2536 409.662 13.1363C409.7 13.0221 409.719 12.9026 409.719 12.7823C409.72 12.6329 409.693 12.4845 409.638 12.3456C409.585 12.2151 409.503 12.0984 409.398 12.0048C409.287 11.9073 409.157 11.8338 409.015 11.7888C408.676 11.6879 408.314 11.6962 407.98 11.8126C407.828 11.8702 407.689 11.9598 407.575 12.0755C407.464 12.1863 407.378 12.3195 407.322 12.4662C407.269 12.6078 407.241 12.7575 407.239 12.9087L407.238 13.0346H408.055L408.059 12.914C408.06 12.8468 408.069 12.7799 408.087 12.715C408.105 12.6582 408.133 12.6051 408.171 12.5587C408.208 12.516 408.255 12.4825 408.308 12.4609C408.427 12.4136 408.56 12.4108 408.681 12.4531C408.725 12.4723 408.764 12.5007 408.795 12.5364C408.827 12.5745 408.852 12.6184 408.868 12.6657C408.887 12.7232 408.897 12.7833 408.897 12.8438C408.897 12.8914 408.891 12.9388 408.88 12.9852C408.865 13.0406 408.843 13.0939 408.815 13.144C408.773 13.2164 408.727 13.2854 408.675 13.3503C408.603 13.4431 408.527 13.5321 408.447 13.6171L407.301 14.8671V15.4375H409.853V14.733H408.425Z","fill","#515965"],["d","M427.384 19.1562H425.259C424.554 19.1562 423.879 18.8764 423.381 18.3783C422.882 17.8801 422.603 17.2045 422.603 16.5C422.603 15.7955 422.882 15.1199 423.381 14.6217C423.879 14.1236 424.554 13.8438 425.259 13.8438H427.384V14.9062H425.259C424.836 14.9062 424.431 15.0742 424.132 15.373C423.833 15.6719 423.665 16.0773 423.665 16.5C423.665 16.9227 423.833 17.3281 424.132 17.627C424.431 17.9258 424.836 18.0938 425.259 18.0938H427.384V19.1562ZM430.571 13.8438H428.446V14.9062H430.571C430.994 14.9062 431.399 15.0742 431.698 15.373C431.997 15.6719 432.165 16.0773 432.165 16.5C432.165 16.9227 431.997 17.3281 431.698 17.627C431.399 17.9258 430.994 18.0938 430.571 18.0938H428.446V19.1562H430.571C431.276 19.1562 431.951 18.8764 432.45 18.3783C432.948 17.8801 433.228 17.2045 433.228 16.5C433.228 15.7955 432.948 15.1199 432.45 14.6217C431.951 14.1236 431.276 13.8438 430.571 13.8438ZM430.04 15.9688H425.79V17.0312H430.04V15.9688Z","fill","#515965"],["d","M451.999 13.3125H448.28L447.217 12.25H443.499C442.914 12.25 442.436 12.7281 442.436 13.3125V19.6875C442.436 20.2719 442.914 20.75 443.499 20.75H451.999C452.583 20.75 453.061 20.2719 453.061 19.6875V14.375C453.061 13.7906 452.583 13.3125 451.999 13.3125ZM451.999 19.6875H443.499V13.3125H446.792L447.855 14.375H451.999V19.6875ZM450.936 18.625L448.917 15.9688H448.811L447.27 17.9875L446.155 16.5531L444.561 18.625H450.936ZM446.686 15.1719C446.686 14.7469 446.314 14.375 445.889 14.375C445.464 14.375 445.092 14.7469 445.092 15.1719C445.092 15.5969 445.464 15.9688 445.889 15.9688C446.314 15.9688 446.686 15.5969 446.686 15.1719Z","fill","#515965"],["d","M0.315 37C0.315 34.9648 1.96483 33.315 4 33.315H729C731.035 33.315 732.685 34.9648 732.685 37V104C732.685 106.035 731.035 107.685 729 107.685H4.00001C1.96484 107.685 0.315 106.035 0.315 104V37Z","fill","white"],["d","M0.315 37C0.315 34.9648 1.96483 33.315 4 33.315H729C731.035 33.315 732.685 34.9648 732.685 37V104C732.685 106.035 731.035 107.685 729 107.685H4.00001C1.96484 107.685 0.315 106.035 0.315 104V37Z","stroke","#D2D2D2","stroke-width","0.63"],["d","M12.8106 49.6664C12.205 49.6664 11.6817 49.5341 11.2407 49.2695C10.8055 48.999 10.4674 48.6256 10.2264 48.1493C9.98528 47.6672 9.86474 47.1086 9.86474 46.4735C9.86474 45.8444 9.98528 45.2917 10.2264 44.8154C10.4674 44.3332 10.8055 43.9569 11.2407 43.6864C11.6817 43.4159 12.205 43.2807 12.8106 43.2807C13.5162 43.2807 14.0895 43.4512 14.5305 43.7923C14.9774 44.1274 15.2626 44.5978 15.3861 45.2035H14.5658C14.4717 44.8213 14.2777 44.5155 13.9837 44.2862C13.6956 44.051 13.3045 43.9334 12.8106 43.9334C12.3696 43.9334 11.9845 44.0363 11.6552 44.2421C11.3259 44.442 11.0701 44.7331 10.8879 45.1153C10.7115 45.4916 10.6233 45.9443 10.6233 46.4735C10.6233 47.0027 10.7115 47.4584 10.8879 47.8406C11.0701 48.217 11.3259 48.508 11.6552 48.7138C11.9845 48.9137 12.3696 49.0137 12.8106 49.0137C13.3045 49.0137 13.6956 48.902 13.9837 48.6785C14.2777 48.4492 14.4717 48.1464 14.5658 47.7701H15.3861C15.2626 48.364 14.9774 48.8285 14.5305 49.1636C14.0895 49.4988 13.5162 49.6664 12.8106 49.6664ZM18.4001 49.6664C17.9885 49.6664 17.618 49.5723 17.2888 49.3841C16.9595 49.196 16.6978 48.9314 16.5038 48.5903C16.3156 48.2434 16.2215 47.8377 16.2215 47.3732C16.2215 46.9087 16.3186 46.5059 16.5126 46.1648C16.7066 45.8179 16.9683 45.5504 17.2976 45.3622C17.6327 45.1741 18.0061 45.08 18.4177 45.08C18.8293 45.08 19.1998 45.1741 19.529 45.3622C19.8583 45.5504 20.117 45.8179 20.3052 46.1648C20.4992 46.5059 20.5963 46.9087 20.5963 47.3732C20.5963 47.8377 20.4992 48.2434 20.3052 48.5903C20.1112 48.9314 19.8466 49.196 19.5114 49.3841C19.1821 49.5723 18.8117 49.6664 18.4001 49.6664ZM18.4001 49.0313C18.6529 49.0313 18.8881 48.9696 19.1057 48.8461C19.3232 48.7226 19.4996 48.5374 19.6349 48.2905C19.7701 48.0435 19.8377 47.7377 19.8377 47.3732C19.8377 47.0086 19.7701 46.7029 19.6349 46.4559C19.5055 46.2089 19.3321 46.0237 19.1145 45.9002C18.8969 45.7768 18.6647 45.715 18.4177 45.715C18.1649 45.715 17.9297 45.7768 17.7121 45.9002C17.4946 46.0237 17.3182 46.2089 17.1829 46.4559C17.0477 46.7029 16.9801 47.0086 16.9801 47.3732C16.9801 47.7377 17.0477 48.0435 17.1829 48.2905C17.3182 48.5374 17.4916 48.7226 17.7033 48.8461C17.9209 48.9696 18.1531 49.0313 18.4001 49.0313ZM21.6264 49.5605V45.1858H22.2967L22.3497 45.8209C22.4908 45.5857 22.6789 45.4034 22.9141 45.274C23.1493 45.1447 23.4139 45.08 23.7079 45.08C24.0549 45.08 24.3518 45.1505 24.5988 45.2917C24.8516 45.4328 25.0456 45.6474 25.1809 45.9355C25.3338 45.6709 25.5425 45.4622 25.8071 45.3093C26.0776 45.1564 26.3686 45.08 26.6803 45.08C27.2036 45.08 27.6211 45.2387 27.9327 45.5563C28.2444 45.8679 28.4002 46.3501 28.4002 47.0027V49.5605H27.6681V47.0821C27.6681 46.6294 27.577 46.2883 27.3947 46.059C27.2124 45.8297 26.9508 45.715 26.6097 45.715C26.2569 45.715 25.9629 45.8532 25.7277 46.1296C25.4984 46.4 25.3837 46.7881 25.3837 47.2938V49.5605H24.6429V47.0821C24.6429 46.6294 24.5517 46.2883 24.3694 46.059C24.1872 45.8297 23.9255 45.715 23.5845 45.715C23.2375 45.715 22.9465 45.8532 22.7113 46.1296C22.482 46.4 22.3673 46.7881 22.3673 47.2938V49.5605H21.6264ZM29.5162 51.5009V45.1858H30.1865L30.2571 45.9091C30.3982 45.6974 30.5981 45.5063 30.8568 45.3358C31.1214 45.1652 31.4566 45.08 31.8623 45.08C32.2974 45.08 32.6767 45.1799 33.0001 45.3799C33.3235 45.5798 33.5734 45.8532 33.7498 46.2001C33.9321 46.547 34.0232 46.941 34.0232 47.382C34.0232 47.823 33.9321 48.217 33.7498 48.5639C33.5734 48.9049 33.3205 49.1754 32.9913 49.3753C32.6679 49.5694 32.2886 49.6664 31.8535 49.6664C31.4948 49.6664 31.1743 49.5929 30.8921 49.4459C30.6157 49.2989 30.4041 49.0931 30.2571 48.8285V51.5009H29.5162ZM31.7741 49.0225C32.0681 49.0225 32.3268 48.9549 32.5503 48.8197C32.7737 48.6785 32.9501 48.4845 33.0795 48.2375C33.2088 47.9906 33.2735 47.7025 33.2735 47.3732C33.2735 47.0439 33.2088 46.7558 33.0795 46.5088C32.9501 46.2619 32.7737 46.0708 32.5503 45.9355C32.3268 45.7944 32.0681 45.7238 31.7741 45.7238C31.4801 45.7238 31.2214 45.7944 30.9979 45.9355C30.7745 46.0708 30.5981 46.2619 30.4687 46.5088C30.3394 46.7558 30.2747 47.0439 30.2747 47.3732C30.2747 47.7025 30.3394 47.9906 30.4687 48.2375C30.5981 48.4845 30.7745 48.6785 30.9979 48.8197C31.2214 48.9549 31.4801 49.0225 31.7741 49.0225ZM37.0478 49.6664C36.6362 49.6664 36.2658 49.5723 35.9365 49.3841C35.6072 49.196 35.3456 48.9314 35.1515 48.5903C34.9634 48.2434 34.8693 47.8377 34.8693 47.3732C34.8693 46.9087 34.9663 46.5059 35.1604 46.1648C35.3544 45.8179 35.6161 45.5504 35.9453 45.3622C36.2805 45.1741 36.6539 45.08 37.0655 45.08C37.4771 45.08 37.8475 45.1741 38.1768 45.3622C38.5061 45.5504 38.7648 45.8179 38.953 46.1648C39.147 46.5059 39.244 46.9087 39.244 47.3732C39.244 47.8377 39.147 48.2434 38.953 48.5903C38.7589 48.9314 38.4943 49.196 38.1592 49.3841C37.8299 49.5723 37.4594 49.6664 37.0478 49.6664ZM37.0478 49.0313C37.3007 49.0313 37.5359 48.9696 37.7534 48.8461C37.971 48.7226 38.1474 48.5374 38.2826 48.2905C38.4179 48.0435 38.4855 47.7377 38.4855 47.3732C38.4855 47.0086 38.4179 46.7029 38.2826 46.4559C38.1533 46.2089 37.9798 46.0237 37.7623 45.9002C37.5447 45.7768 37.3124 45.715 37.0655 45.715C36.8126 45.715 36.5774 45.7768 36.3599 45.9002C36.1423 46.0237 35.9659 46.2089 35.8307 46.4559C35.6954 46.7029 35.6278 47.0086 35.6278 47.3732C35.6278 47.7377 35.6954 48.0435 35.8307 48.2905C35.9659 48.5374 36.1394 48.7226 36.3511 48.8461C36.5686 48.9696 36.8009 49.0313 37.0478 49.0313ZM41.95 49.6664C41.4266 49.6664 40.9915 49.5341 40.6446 49.2695C40.2977 49.0049 40.0948 48.6462 40.036 48.1934H40.7945C40.8416 48.4228 40.9621 48.6227 41.1562 48.7932C41.3561 48.9578 41.6236 49.0402 41.9588 49.0402C42.2704 49.0402 42.4997 48.9755 42.6467 48.8461C42.7937 48.7109 42.8672 48.5521 42.8672 48.3698C42.8672 48.1052 42.7702 47.9288 42.5762 47.8406C42.388 47.7524 42.1205 47.6731 41.7736 47.6025C41.5384 47.5555 41.3032 47.4878 41.068 47.3996C40.8328 47.3114 40.6358 47.188 40.477 47.0292C40.3183 46.8646 40.2389 46.6499 40.2389 46.3853C40.2389 46.0031 40.38 45.6915 40.6622 45.4504C40.9504 45.2035 41.3384 45.08 41.8265 45.08C42.291 45.08 42.6703 45.1976 42.9643 45.4328C43.2641 45.6621 43.4376 45.9914 43.4846 46.4206H42.7526C42.7232 46.1972 42.6262 46.0237 42.4615 45.9002C42.3028 45.7709 42.0881 45.7062 41.8177 45.7062C41.5531 45.7062 41.3473 45.7621 41.2003 45.8738C41.0591 45.9855 40.9886 46.1325 40.9886 46.3148C40.9886 46.4912 41.0797 46.6294 41.262 46.7293C41.4502 46.8293 41.703 46.9145 42.0205 46.9851C42.291 47.0439 42.5468 47.1174 42.7879 47.2056C43.0348 47.2879 43.2347 47.4143 43.3876 47.5849C43.5464 47.7495 43.6258 47.9906 43.6258 48.3081C43.6316 48.7021 43.4817 49.0284 43.1759 49.2871C42.8761 49.54 42.4674 49.6664 41.95 49.6664ZM46.651 49.6664C46.2335 49.6664 45.8631 49.5723 45.5397 49.3841C45.2163 49.1901 44.9605 48.9226 44.7724 48.5815C44.5901 48.2405 44.4989 47.8377 44.4989 47.3732C44.4989 46.9145 44.5901 46.5147 44.7724 46.1737C44.9546 45.8267 45.2075 45.5592 45.5309 45.371C45.8602 45.177 46.2394 45.08 46.6687 45.08C47.092 45.08 47.4566 45.177 47.7623 45.371C48.074 45.5592 48.3121 45.8091 48.4768 46.1207C48.6414 46.4324 48.7237 46.7675 48.7237 47.1262C48.7237 47.1909 48.7208 47.2556 48.7149 47.3203C48.7149 47.3849 48.7149 47.4584 48.7149 47.5408H45.231C45.2486 47.8759 45.3251 48.1552 45.4603 48.3787C45.6014 48.5962 45.7749 48.7609 45.9807 48.8726C46.1924 48.9843 46.4158 49.0402 46.651 49.0402C46.9568 49.0402 47.2126 48.9696 47.4184 48.8285C47.6242 48.6874 47.7741 48.4963 47.8682 48.2552H48.6002C48.4826 48.6609 48.2563 48.999 47.9211 49.2695C47.5918 49.5341 47.1685 49.6664 46.651 49.6664ZM46.651 45.7062C46.2982 45.7062 45.9836 45.815 45.7073 46.0325C45.4368 46.2442 45.281 46.5559 45.2398 46.9675H47.9917C47.974 46.5735 47.8388 46.2648 47.5859 46.0414C47.3331 45.8179 47.0215 45.7062 46.651 45.7062ZM53.4952 49.6664C53.1306 49.6664 52.8278 49.6046 52.5867 49.4812C52.3457 49.3577 52.1663 49.193 52.0487 48.9872C51.9311 48.7814 51.8723 48.558 51.8723 48.3169C51.8723 47.87 52.0428 47.5261 52.3839 47.285C52.7249 47.0439 53.1894 46.9234 53.7774 46.9234H54.9593V46.8704C54.9593 46.4882 54.8594 46.2001 54.6594 46.0061C54.4595 45.8062 54.192 45.7062 53.8568 45.7062C53.5687 45.7062 53.3188 45.7797 53.1071 45.9267C52.9013 46.0678 52.772 46.2766 52.719 46.5529H51.9605C51.9899 46.2354 52.0958 45.9679 52.278 45.7503C52.4662 45.5327 52.6985 45.3681 52.9748 45.2564C53.2512 45.1388 53.5452 45.08 53.8568 45.08C54.4683 45.08 54.927 45.2446 55.2327 45.5739C55.5444 45.8973 55.7002 46.3295 55.7002 46.8704V49.5605H55.0387L54.9946 48.7756C54.8711 49.0225 54.6888 49.2342 54.4478 49.4106C54.2126 49.5811 53.895 49.6664 53.4952 49.6664ZM53.6099 49.0402C53.8921 49.0402 54.1332 48.9667 54.3331 48.8197C54.5389 48.6727 54.6947 48.4816 54.8006 48.2464C54.9064 48.0112 54.9593 47.7642 54.9593 47.5055V47.4967H53.8392C53.4041 47.4967 53.0954 47.5731 52.9131 47.726C52.7367 47.873 52.6485 48.0582 52.6485 48.2816C52.6485 48.511 52.7308 48.6962 52.8954 48.8373C53.066 48.9725 53.3041 49.0402 53.6099 49.0402ZM56.8117 49.5605V45.1858H57.482L57.5261 45.9708C57.6672 45.6944 57.8701 45.4769 58.1347 45.3181C58.3993 45.1594 58.6991 45.08 59.0343 45.08C59.5517 45.08 59.9633 45.2387 60.2691 45.5563C60.5807 45.8679 60.7366 46.3501 60.7366 47.0027V49.5605H59.9957V47.0821C59.9957 46.1707 59.6194 45.715 58.8667 45.715C58.4904 45.715 58.1758 45.8532 57.923 46.1296C57.676 46.4 57.5525 46.7881 57.5525 47.2938V49.5605H56.8117ZM66.1601 49.6664C65.7426 49.6664 65.3722 49.5723 65.0488 49.3841C64.7254 49.1901 64.4696 48.9226 64.2814 48.5815C64.0992 48.2405 64.008 47.8377 64.008 47.3732C64.008 46.9145 64.0992 46.5147 64.2814 46.1737C64.4637 45.8267 64.7166 45.5592 65.04 45.371C65.3692 45.177 65.7485 45.08 66.1777 45.08C66.6011 45.08 66.9657 45.177 67.2714 45.371C67.5831 45.5592 67.8212 45.8091 67.9858 46.1207C68.1505 46.4324 68.2328 46.7675 68.2328 47.1262C68.2328 47.1909 68.2299 47.2556 68.224 47.3203C68.224 47.3849 68.224 47.4584 68.224 47.5408H64.7401C64.7577 47.8759 64.8342 48.1552 64.9694 48.3787C65.1105 48.5962 65.284 48.7609 65.4898 48.8726C65.7015 48.9843 65.9249 49.0402 66.1601 49.0402C66.4659 49.0402 66.7216 48.9696 66.9274 48.8285C67.1332 48.6874 67.2832 48.4963 67.3773 48.2552H68.1093C67.9917 48.6609 67.7653 48.999 67.4302 49.2695C67.1009 49.5341 66.6775 49.6664 66.1601 49.6664ZM66.1601 45.7062C65.8073 45.7062 65.4927 45.815 65.2164 46.0325C64.9459 46.2442 64.7901 46.5559 64.7489 46.9675H67.5007C67.4831 46.5735 67.3479 46.2648 67.095 46.0414C66.8422 45.8179 66.5305 45.7062 66.1601 45.7062ZM69.2148 51.5009V45.1858H69.8851L69.9557 45.9091C70.0968 45.6974 70.2967 45.5063 70.5554 45.3358C70.82 45.1652 71.1552 45.08 71.5609 45.08C71.996 45.08 72.3753 45.1799 72.6987 45.3799C73.0221 45.5798 73.272 45.8532 73.4484 46.2001C73.6307 46.547 73.7218 46.941 73.7218 47.382C73.7218 47.823 73.6307 48.217 73.4484 48.5639C73.272 48.9049 73.0191 49.1754 72.6899 49.3753C72.3665 49.5694 71.9872 49.6664 71.5521 49.6664C71.1934 49.6664 70.8729 49.5929 70.5907 49.4459C70.3143 49.2989 70.1027 49.0931 69.9557 48.8285V51.5009H69.2148ZM71.4727 49.0225C71.7667 49.0225 72.0254 48.9549 72.2489 48.8197C72.4723 48.6785 72.6487 48.4845 72.7781 48.2375C72.9074 47.9906 72.9721 47.7025 72.9721 47.3732C72.9721 47.0439 72.9074 46.7558 72.7781 46.5088C72.6487 46.2619 72.4723 46.0708 72.2489 45.9355C72.0254 45.7944 71.7667 45.7238 71.4727 45.7238C71.1787 45.7238 70.92 45.7944 70.6965 45.9355C70.4731 46.0708 70.2967 46.2619 70.1673 46.5088C70.038 46.7558 69.9733 47.0439 69.9733 47.3732C69.9733 47.7025 70.038 47.9906 70.1673 48.2375C70.2967 48.4845 70.4731 48.6785 70.6965 48.8197C70.92 48.9549 71.1787 49.0225 71.4727 49.0225ZM75.2029 44.2156C75.0559 44.2156 74.9325 44.1686 74.8325 44.0745C74.7384 43.9745 74.6914 43.8511 74.6914 43.7041C74.6914 43.5629 74.7384 43.4453 74.8325 43.3513C74.9325 43.2572 75.0559 43.2101 75.2029 43.2101C75.3441 43.2101 75.4646 43.2572 75.5646 43.3513C75.6645 43.4453 75.7145 43.5629 75.7145 43.7041C75.7145 43.8511 75.6645 43.9745 75.5646 44.0745C75.4646 44.1686 75.3441 44.2156 75.2029 44.2156ZM74.8325 49.5605V45.1858H75.5734V49.5605H74.8325ZM78.8743 49.6664C78.4569 49.6664 78.0805 49.5723 77.7454 49.3841C77.4161 49.1901 77.1544 48.9226 76.9604 48.5815C76.7722 48.2346 76.6782 47.8318 76.6782 47.3732C76.6782 46.9145 76.7722 46.5147 76.9604 46.1737C77.1544 45.8267 77.4161 45.5592 77.7454 45.371C78.0805 45.177 78.4569 45.08 78.8743 45.08C79.3918 45.08 79.8269 45.2152 80.1797 45.4857C80.5384 45.7562 80.7648 46.1178 80.8588 46.5706H80.1003C80.0415 46.3001 79.8975 46.0913 79.6681 45.9443C79.4388 45.7915 79.1713 45.715 78.8655 45.715C78.6186 45.715 78.3863 45.7768 78.1687 45.9002C77.9512 46.0237 77.7748 46.2089 77.6395 46.4559C77.5043 46.7029 77.4367 47.0086 77.4367 47.3732C77.4367 47.7377 77.5043 48.0435 77.6395 48.2905C77.7748 48.5374 77.9512 48.7256 78.1687 48.8549C78.3863 48.9784 78.6186 49.0402 78.8655 49.0402C79.1713 49.0402 79.4388 48.9667 79.6681 48.8197C79.8975 48.6668 80.0415 48.4522 80.1003 48.1758H80.8588C80.7706 48.6168 80.5472 48.9755 80.1885 49.2518C79.8298 49.5282 79.3918 49.6664 78.8743 49.6664ZM82.1493 49.6046C82.0023 49.6046 81.8788 49.5576 81.7789 49.4635C81.6848 49.3636 81.6378 49.243 81.6378 49.1019C81.6378 48.9608 81.6848 48.8432 81.7789 48.7491C81.8788 48.6491 82.0023 48.5992 82.1493 48.5992C82.2904 48.5992 82.408 48.6491 82.5021 48.7491C82.6021 48.8432 82.6521 48.9608 82.6521 49.1019C82.6521 49.243 82.6021 49.3636 82.5021 49.4635C82.408 49.5576 82.2904 49.6046 82.1493 49.6046ZM83.872 49.6046C83.725 49.6046 83.6015 49.5576 83.5015 49.4635C83.4075 49.3636 83.3604 49.243 83.3604 49.1019C83.3604 48.9608 83.4075 48.8432 83.5015 48.7491C83.6015 48.6491 83.725 48.5992 83.872 48.5992C84.0131 48.5992 84.1307 48.6491 84.2248 48.7491C84.3247 48.8432 84.3747 48.9608 84.3747 49.1019C84.3747 49.243 84.3247 49.3636 84.2248 49.4635C84.1307 49.5576 84.0131 49.6046 83.872 49.6046Z","fill","#676767"],["id","clip0_10914_44976"],["width","12.75","height","12.75","fill","white","transform","translate(252.249 10.125)"],[1,"custom-question-field"],["appearance","outline",1,"form-field-class-date"],[1,"date-picker"],["matInput","","placeholder","DD MMM YYYY","disabled","",3,"matDatepicker"],[3,"for"],["matDatepickerToggleIcon","",1,"calendar-icon"],["dp",""],["formArrayName","fieldValues"],["cdkDropList","",1,"d-flex","flex-column",2,"gap","20px","margin-bottom","16px","width","95%",3,"cdkDropListDropped"],["cdkDrag","",1,"options-row"],[1,"option-input"],["type","text","formControlName","option","placeholder","Add Option","maxlength","75",3,"ngStyle"],[1,"svg","icon",3,"click"],["class","svg icon",3,"click",4,"ngIf"],["class","svg icon","cdkDragHandle","",4,"ngIf"],[1,"radio"],[1,"check"],[1,"number"],["cdkDragHandle","",1,"svg","icon"],[1,"file-upload"],[1,"file-type-toggle"],[1,"text"],["formControlName","allowSpecificFiles",3,"change"],["class","file-types",3,"formGroup",4,"ngIf"],[1,"file-validation"],["cdkOverlayOrigin","",1,"dropdown-field",3,"click"],["triggerFileUploadDropdown","cdkOverlayOrigin","triggerFileUploadDropdownField",""],[1,"file-types",3,"formGroup"],["class","check-box",4,"ngFor","ngForOf"],[1,"check-box"],[1,"file-type",3,"formControlName"],[1,"single-file-type-text"],[1,"preview-header"],[1,"preview-header-1"],[1,"preview-header-2"],[1,"type"],[1,"preview-content",3,"formGroup"],[1,"question-text"],[1,"d-flex","flex-column"],[3,"ngTemplateOutlet"],[1,"d-flex","flex-column",2,"gap","20px","margin-bottom","16px"],[1,"options-row"],[1,"option-label"],["cdkOverlayOrigin","",1,"custom-question-field",2,"cursor","pointer",3,"click"],["triggerCustomDropdown","cdkOverlayOrigin","triggerCustomDropdownField",""],["width","53","height","41","viewBox","0 0 53 41","fill","none"],["cx","8.30133","cy","7.66658","rx","1.29547","ry","1.29549","fill","#EFEFEF"],["cx","41.8147","cy","2.54323","rx","2.41429","ry","2.41432","fill","#EFEFEF"],["cx","8.71254","cy","34.9294","rx","2.29652","ry","2.29655","fill","#EFEFEF"],["cx","49.82","cy","32.7028","rx","2.29652","ry","2.29655","fill","#EFEFEF"],["cx","45.3454","cy","35.2985","rx","1.11882","ry","1.11883","fill","#EFEFEF"],["d","M30.9732 1.84022C30.9049 1.8257 30.9049 1.72825 30.9732 1.71373L32.0055 1.4942C32.0304 1.48891 32.0499 1.46952 32.0553 1.44466L32.2775 0.419585C32.2923 0.351644 32.3892 0.351643 32.4039 0.419585L32.6262 1.44466C32.6316 1.46952 32.6511 1.48891 32.6759 1.4942L33.7083 1.71373C33.7766 1.72825 33.7766 1.8257 33.7083 1.84022L32.6759 2.05975C32.6511 2.06504 32.6316 2.08443 32.6262 2.1093L32.4039 3.13437C32.3892 3.20231 32.2923 3.20231 32.2775 3.13437L32.0553 2.1093C32.0499 2.08443 32.0304 2.06504 32.0055 2.05975L30.9732 1.84022Z","fill","#1B2140"],["d","M2.91329 24.7123C2.93964 24.5878 3.11737 24.5878 3.14372 24.7123L3.53741 26.5722C3.547 26.6176 3.58228 26.653 3.62756 26.6629L5.46825 27.0639C5.59186 27.0908 5.59186 27.2671 5.46825 27.294L3.62756 27.695C3.58228 27.7049 3.547 27.7404 3.53741 27.7857L3.14372 29.6456C3.11737 29.7702 2.93964 29.7702 2.91329 29.6456L2.5196 27.7857C2.51001 27.7404 2.47473 27.7049 2.42946 27.695L0.588759 27.294C0.465156 27.2671 0.465156 27.0908 0.588759 27.0639L2.42946 26.6629C2.47473 26.653 2.51001 26.6176 2.5196 26.5722L2.91329 24.7123Z","fill","#1B2140"],["d","M46.1515 8.22402C46.1778 8.09951 46.3556 8.09951 46.3819 8.22402L46.6116 9.30906C46.6212 9.35439 46.6565 9.38988 46.7017 9.39974L47.7786 9.63435C47.9022 9.66127 47.9022 9.83756 47.7786 9.86449L46.7017 10.0991C46.6565 10.109 46.6212 10.1444 46.6116 10.1898L46.3819 11.2748C46.3556 11.3993 46.1778 11.3993 46.1515 11.2748L45.9218 10.1898C45.9122 10.1444 45.8769 10.109 45.8317 10.0991L44.7548 9.86449C44.6312 9.83756 44.6312 9.66127 44.7548 9.63435L45.8317 9.39974C45.8769 9.38988 45.9122 9.35439 45.9218 9.30906L46.1515 8.22402Z","fill","#1B2140"],["d","M50.8976 25.6055L51.2582 27.3093L52.9377 27.6752L51.2582 28.0411L50.8976 29.7449L50.5369 28.0411L48.8574 27.6752L50.5369 27.3093L50.8976 25.6055Z","fill","white"],["d","M33.3916 29.3354L27.9936 23.9375L22.5957 29.3354","stroke","black","stroke-opacity","0.4","stroke-width","0.660971","stroke-linecap","round","stroke-linejoin","round"],["d","M27.9922 23.9375V36.0828","stroke","black","stroke-opacity","0.4","stroke-width","0.660971","stroke-linecap","round","stroke-linejoin","round"],["d","M39.3123 32.5582C40.6285 31.8406 41.6683 30.7052 42.2675 29.3311C42.8668 27.9569 42.9913 26.4224 42.6215 24.9696C42.2518 23.5169 41.4087 22.2286 40.2255 21.3082C39.0422 20.3877 37.5862 19.8875 36.0871 19.8865H34.3867C33.9783 18.3066 33.2169 16.8399 32.16 15.5965C31.1031 14.3532 29.778 13.3656 28.2845 12.7081C26.791 12.0506 25.1678 11.7402 23.537 11.8003C21.9063 11.8604 20.3104 12.2894 18.8693 13.055C17.4282 13.8207 16.1794 14.903 15.2168 16.2208C14.2542 17.5385 13.6029 19.0573 13.3118 20.663C13.0207 22.2687 13.0974 23.9195 13.5362 25.4913C13.9749 27.0631 14.7642 28.515 15.8448 29.7378","stroke","black","stroke-opacity","0.4","stroke-width","0.660971","stroke-linecap","round","stroke-linejoin","round"],[1,"d-flex","align-items-center",2,"gap","4px"],[1,"text-1"],[1,"text-2"],[1,"d-flex","align-items-center","justify-content-between"],[1,"file-upload-restriction"],[1,"loading-state-ui"],[3,"list","currentSelectedId","onConfirmation"],[1,"activation-overlay-container"],[4,"ngFor","ngForOf"],[1,"single-item",3,"ngStyle","click"],[1,"circle",3,"ngStyle"],["class","divider",4,"ngIf"],[1,"divider"],[1,"dropdown-overlay"],[1,"text",3,"matTooltip"],[1,"file-upload-dropdown-overlay"],[1,"text",3,"matTooltip","click"],[2,"color","#cf0001"]],template:function(e,t){1&e&&(m["\u0275\u0275template"](0,G,2,0,"ng-container",0),m["\u0275\u0275pipe"](1,"access"),m["\u0275\u0275template"](2,Ge,22,12,"ng-container",0),m["\u0275\u0275pipe"](3,"access"),m["\u0275\u0275template"](4,ze,1,2,"ng-template",1,2,m["\u0275\u0275templateRefExtractor"]),m["\u0275\u0275template"](6,Ne,2,1,"ng-template",1,3,m["\u0275\u0275templateRefExtractor"]),m["\u0275\u0275template"](8,qe,2,1,"ng-template",1,4,m["\u0275\u0275templateRefExtractor"]),m["\u0275\u0275template"](10,We,2,1,"ng-template",1,5,m["\u0275\u0275templateRefExtractor"]),m["\u0275\u0275template"](12,$e,2,0,"ng-template",null,6,m["\u0275\u0275templateRefExtractor"])),2&e&&(m["\u0275\u0275property"]("ngIf",!m["\u0275\u0275pipeBindV"](1,6,m["\u0275\u0275pureFunction2"](18,Xe,t.access.moduleId.settings,t.access.subModuleId.onboardingTaskSettings))),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",m["\u0275\u0275pipeBindV"](3,12,m["\u0275\u0275pureFunction2"](21,Xe,t.access.moduleId.settings,t.access.subModuleId.onboardingTaskSettings))),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("cdkConnectedOverlayOrigin",t.triggerTaskType),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("cdkConnectedOverlayOrigin",t.triggerActivate),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("cdkConnectedOverlayOrigin",t.triggerCustomDropdown),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("cdkConnectedOverlayOrigin",t.triggerFileUploadDropdown))},directives:[o.NgIf,M.a,v.a,O.a,x.a,o.NgClass,M.b,k.a,d.J,d.w,d.n,d.h,o.NgForOf,d.e,d.q,d.v,d.l,o.NgStyle,d.o,P.a,y.a,w.c,b.b,S.g,S.i,S.j,S.f,g.e,g.a,g.b,V.a,o.NgTemplateOutlet,F.a,E.a],pipes:[H.a,T.a],styles:['.bg-container[_ngcontent-%COMP%]{background-color:#f1f3f8;height:var(--dynamicHeight)}.header[_ngcontent-%COMP%]{justify-content:space-between;background-color:#fff;padding:6px 16px;max-height:52px}.header[_ngcontent-%COMP%], .header[_ngcontent-%COMP%]   .header-1[_ngcontent-%COMP%]{display:flex;align-items:center}.header[_ngcontent-%COMP%]   .header-1[_ngcontent-%COMP%]{gap:16px;padding-left:24px;width:50%}.header[_ngcontent-%COMP%]   .header-1[_ngcontent-%COMP%]   .back-icon[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;font-size:18px;width:18px;height:18px;color:#526179;border-radius:4px;border:1px solid #526179;cursor:pointer}.header[_ngcontent-%COMP%]   .header-1[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:100%}.header[_ngcontent-%COMP%]   .header-1[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .content-title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:16px;font-weight:700;color:#111434}.header[_ngcontent-%COMP%]   .header-1[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .content-sub-title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:11px;font-weight:400;color:#8b95a5;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.header[_ngcontent-%COMP%]   .header-2[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px}.header[_ngcontent-%COMP%]   .header-2[_ngcontent-%COMP%]   .preview[_ngcontent-%COMP%]{text-decoration:underline}.header[_ngcontent-%COMP%]   .header-2[_ngcontent-%COMP%]   .preview[_ngcontent-%COMP%], .header[_ngcontent-%COMP%]   .header-2[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:700;color:#45546e;cursor:pointer}.header[_ngcontent-%COMP%]   .header-2[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%]{border:1px solid #45546e;border-radius:8px;padding:7px 11px}.header[_ngcontent-%COMP%]   .header-2[_ngcontent-%COMP%]   .save-btn-disabled[_ngcontent-%COMP%]{background:#b9c0ca;color:#fff;pointer-events:none;padding:8px 12px;border:none}.header[_ngcontent-%COMP%]   .header-2[_ngcontent-%COMP%]   .activate-btn[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px;font-family:var(--atsfontFamily);font-size:14px;font-weight:700;color:#fff;cursor:pointer;padding:8px 12px 8px 16px;background-color:var(--atsprimaryColor);border-radius:8px}.header[_ngcontent-%COMP%]   .header-2[_ngcontent-%COMP%]   .activate-btn[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{font-size:16px;width:16px;height:16px;color:#fff}.header[_ngcontent-%COMP%]   .header-2[_ngcontent-%COMP%]   .activate-btn-disabled[_ngcontent-%COMP%]{background:#b9c0ca;color:#fff;pointer-events:none;padding:8px 12px 8px 16px}.task-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;background-color:#fff;margin:16px;padding:16px}.task-container[_ngcontent-%COMP%]   .task-header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;max-height:52px}.task-container[_ngcontent-%COMP%]   .task-header[_ngcontent-%COMP%]   .task-header-1[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:50%}.task-container[_ngcontent-%COMP%]   .task-header[_ngcontent-%COMP%]   .task-header-1[_ngcontent-%COMP%]   .content-title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:16px;font-weight:500;color:#111434}.task-container[_ngcontent-%COMP%]   .task-header[_ngcontent-%COMP%]   .task-header-1[_ngcontent-%COMP%]   .content-sub-title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:11px;font-weight:400;color:#b9c0ca;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.task-container[_ngcontent-%COMP%]   .task-header[_ngcontent-%COMP%]   .task-header-2[_ngcontent-%COMP%]   .add-task[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:700;color:#fff;background-color:var(--atsprimaryColor);padding:8px 12px;border-radius:8px;cursor:pointer}.task-container[_ngcontent-%COMP%]   .task-list[_ngcontent-%COMP%]{padding:8px 0 0}.task-container[_ngcontent-%COMP%]   .task-list-empty[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:8px 0 0;height:var(--dynamicSubHeight)}.task-container[_ngcontent-%COMP%]   .task-list-empty[_ngcontent-%COMP%]   .no-data-title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:500;color:#1b2140}.task-container[_ngcontent-%COMP%]   .task-list-empty[_ngcontent-%COMP%]   .loading-wrapper[_ngcontent-%COMP%]{display:flex;vertical-align:center;justify-content:center;align-items:center}.task-container[_ngcontent-%COMP%]   .task-list-empty[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]{color:rgba(0,0,0,.3);font-size:16px}.task-container[_ngcontent-%COMP%]   .task-list-empty[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]:before{content:"Loading...";position:absolute;overflow:hidden;max-width:7em;white-space:nowrap;background:linear-gradient(270deg,var(--atsprimaryColor),var(--atssecondaryColor) 105.29%);background-clip:text;-webkit-background-clip:text;-webkit-text-fill-color:transparent;animation:loading 6s linear infinite}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]{display:flex;flex-direction:column}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;height:46px}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-header[_ngcontent-%COMP%]   .create-task-header-1[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:50%}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-header[_ngcontent-%COMP%]   .create-task-header-1[_ngcontent-%COMP%]   .view-task[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;min-width:30%;max-width:90%}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-header[_ngcontent-%COMP%]   .create-task-header-1[_ngcontent-%COMP%]   .view-task[_ngcontent-%COMP%]   .task-name[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:16px;font-weight:500;color:#111434;max-width:90%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;padding-left:8px}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-header[_ngcontent-%COMP%]   .create-task-header-1[_ngcontent-%COMP%]   .view-task[_ngcontent-%COMP%]   .edit-icon[_ngcontent-%COMP%]{font-size:16px;width:16px;height:16px;color:#b9c0ca;margin-left:8px;cursor:pointer}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-header[_ngcontent-%COMP%]   .create-task-header-1[_ngcontent-%COMP%]   .edit-task[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;min-width:30%;max-width:90%}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-header[_ngcontent-%COMP%]   .create-task-header-1[_ngcontent-%COMP%]   .edit-task[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{height:100%;width:70%;padding:4px 8px;font-family:var(--atsfontFamily);font-size:14px;font-weight:400;color:#45546e;outline:none;border:none;border-bottom:1px solid #111434;background-color:#f7f9fb}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-header[_ngcontent-%COMP%]   .create-task-header-1[_ngcontent-%COMP%]   .edit-task[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus, .task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-header[_ngcontent-%COMP%]   .create-task-header-1[_ngcontent-%COMP%]   .edit-task[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:hover{border-bottom:1px solid #f44336}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-header[_ngcontent-%COMP%]   .create-task-header-1[_ngcontent-%COMP%]   .edit-task[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{font-family:var(--atsfontFamily);font-size:14px;font-weight:400;color:#6e7b8f}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-header[_ngcontent-%COMP%]   .create-task-header-1[_ngcontent-%COMP%]   .edit-task[_ngcontent-%COMP%]   .tick-icon[_ngcontent-%COMP%]{font-size:16px;width:16px;height:16px;color:#111434;cursor:pointer}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-header[_ngcontent-%COMP%]   .create-task-header-1[_ngcontent-%COMP%]   .task-name-desc[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:11px;font-weight:400;color:#b9c0ca;padding-left:8px}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-header[_ngcontent-%COMP%]   .create-task-header-2[_ngcontent-%COMP%]{display:flex;align-items:end;gap:16px}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-header[_ngcontent-%COMP%]   .create-task-header-2[_ngcontent-%COMP%]   .reset[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:700;color:#45546e;text-decoration:underline;cursor:pointer;white-space:nowrap}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-header[_ngcontent-%COMP%]   .create-task-header-2[_ngcontent-%COMP%]   .task-type[_ngcontent-%COMP%]{display:flex;flex-direction:column}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-header[_ngcontent-%COMP%]   .create-task-header-2[_ngcontent-%COMP%]   .task-type[_ngcontent-%COMP%]   .type-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:11px;font-weight:400;color:#8b95a5}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-header[_ngcontent-%COMP%]   .create-task-header-2[_ngcontent-%COMP%]   .task-type[_ngcontent-%COMP%]   .type[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;background-color:#f6f6f6;padding:2px 6px;border-radius:8px;font-family:var(--atsfontFamily);font-size:12px;font-weight:500;color:#45546e;cursor:pointer;white-space:nowrap}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-header[_ngcontent-%COMP%]   .create-task-header-2[_ngcontent-%COMP%]   .task-type[_ngcontent-%COMP%]   .type[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{font-size:16px;width:16px;height:16px;color:#45546e}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:16px;margin-top:16px;padding:16px 32px;height:calc(var(--dynamicSubHeight) - 8px);background-color:#fafafa;border:1px solid #f3f4f6;overflow-y:auto}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]{display:flex;align-items:start;gap:8px}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .index[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;border-radius:4px;border:1px solid #dadce2;height:40px;min-width:32px;font-family:var(--atsfontFamily);font-size:12px;font-weight:500;color:#5f6c81}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{width:calc(100% - 40px);min-height:150px;border:1px solid #dadce2;border-radius:8px;padding:20px 30px}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]     .mat-slide-toggle-thumb{width:12px!important;height:12px!important;transform:translate(50%,50%)}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]     .mat-slide-toggle-bar{background-color:#dadce2;border-radius:15px!important;height:16px!important;width:32px!important}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]     .mat-slide-toggle-thumb-container{top:-4px!important;left:-5px!important}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]     .mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-bar{background-color:var(--atssecondaryColor)}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]     .mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-thumb{background-color:#fff}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]     .mat-checkbox-checked.mat-accent .mat-checkbox-background, .task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]     .mat-checkbox-indeterminate .mat-checkbox-background{background-color:var(--atssecondaryColor)!important}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]     .mat-checkbox-frame{border-color:#dadce2!important}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]     .mat-checkbox-inner-container{width:14px!important;height:14px!important}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .custom-question-field[_ngcontent-%COMP%]{display:flex;align-items:center;height:35px;width:50%;border-radius:4px;border:1px solid #d2d2d2;padding:4px 8px;font-family:var(--atsfontFamily);font-size:12px;font-weight:400;color:#111434}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .form-field-class-date[_ngcontent-%COMP%]{width:50%;font-size:12px;font-family:var(--atsfontFamily)}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .form-field-class-date[_ngcontent-%COMP%]     .mat-input-element{height:17px!important}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .form-field-class-date[_ngcontent-%COMP%]   .date-picker[_ngcontent-%COMP%]{display:flex;align-items:center;height:17px}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .form-field-class-date[_ngcontent-%COMP%]   .calendar-icon[_ngcontent-%COMP%]{font-size:16px}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .form-field-class-date[_ngcontent-%COMP%]     .mat-input-element, .task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .form-field-class-date[_ngcontent-%COMP%]     .mat-input-element:disabled{color:#45546e!important}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .form-field-class-date[_ngcontent-%COMP%]     .mat-form-field{width:50%!important;font-size:12px!important;font-family:var(--atsfontFamily)!important}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .form-field-class-date[_ngcontent-%COMP%]     .mat-form-field-wrapper, .task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .form-field-class-date[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-wrapper{padding-bottom:0!important}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .form-field-class-date[_ngcontent-%COMP%]     .mat-form-field-outline{color:#d2d2d2!important}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]{width:60%}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{height:100%;width:95%;font-family:var(--atsfontFamily);font-size:14px;font-weight:400;color:#45546e;outline:none;border:none;border-bottom:1px solid #111434;background-color:#f7f9fb}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus, .task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:hover{border-bottom:1px solid #f44336}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{font-family:var(--atsfontFamily);font-size:14px;font-weight:400;color:#6e7b8f}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .mand-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:12px;font-weight:400;color:#5f6c81}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .svg[_ngcontent-%COMP%]{cursor:pointer}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .custom-icons[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:end;width:100%;gap:10px}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .file-upload[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px;width:60%}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .file-upload[_ngcontent-%COMP%]   .file-type-toggle[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .file-upload[_ngcontent-%COMP%]   .file-type-toggle[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:400;color:#6e7b8f}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .file-upload[_ngcontent-%COMP%]   .file-types[_ngcontent-%COMP%]{display:grid;grid-template-columns:48% 48%;gap:8px;width:100%}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .file-upload[_ngcontent-%COMP%]   .file-types[_ngcontent-%COMP%]   .check-box[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .file-upload[_ngcontent-%COMP%]   .file-types[_ngcontent-%COMP%]   .check-box[_ngcontent-%COMP%]   .file-type[_ngcontent-%COMP%]{width:16px;height:16px}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .file-upload[_ngcontent-%COMP%]   .file-types[_ngcontent-%COMP%]   .check-box[_ngcontent-%COMP%]   .single-file-type-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:400;color:#6e7b8f;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;width:50%}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .file-upload[_ngcontent-%COMP%]   .file-validation[_ngcontent-%COMP%]{display:flex;align-items:center;gap:20px}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .file-upload[_ngcontent-%COMP%]   .file-validation[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:400;color:#6e7b8f;width:170px}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .file-upload[_ngcontent-%COMP%]   .file-validation[_ngcontent-%COMP%]   .dropdown-field[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;width:70px;border:1px solid #b9c0ca;border-radius:6px;padding:4px 8px;cursor:pointer}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .file-upload[_ngcontent-%COMP%]   .file-validation[_ngcontent-%COMP%]   .dropdown-field[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:12px;font-weight:400;color:#45546e}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .file-upload[_ngcontent-%COMP%]   .file-validation[_ngcontent-%COMP%]   .dropdown-field[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{font-size:14px;width:14px;height:14px;color:#d9d9d9}.task-container[_ngcontent-%COMP%]   .form[_ngcontent-%COMP%]   .create-task-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .add-content[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;height:40px;border-radius:8px;border:1.5px dashed #6e7b8f;cursor:pointer;font-family:var(--atsfontFamily);font-size:14px;font-weight:400;color:#6e7b8f;width:100%}.task-container[_ngcontent-%COMP%]   .preview-header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between}.task-container[_ngcontent-%COMP%]   .preview-header[_ngcontent-%COMP%]   .preview-header-1[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:50%}.task-container[_ngcontent-%COMP%]   .preview-header[_ngcontent-%COMP%]   .preview-header-1[_ngcontent-%COMP%]   .task-name[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:16px;font-weight:500;color:#111434;max-width:90%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.task-container[_ngcontent-%COMP%]   .preview-header[_ngcontent-%COMP%]   .preview-header-2[_ngcontent-%COMP%]{display:flex;align-items:end;gap:16px}.task-container[_ngcontent-%COMP%]   .preview-header[_ngcontent-%COMP%]   .preview-header-2[_ngcontent-%COMP%]   .task-type[_ngcontent-%COMP%]{display:flex;flex-direction:column}.task-container[_ngcontent-%COMP%]   .preview-header[_ngcontent-%COMP%]   .preview-header-2[_ngcontent-%COMP%]   .task-type[_ngcontent-%COMP%]   .type-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:11px;font-weight:400;color:#8b95a5}.task-container[_ngcontent-%COMP%]   .preview-header[_ngcontent-%COMP%]   .preview-header-2[_ngcontent-%COMP%]   .task-type[_ngcontent-%COMP%]   .type[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;background-color:#f6f6f6;padding:2px 6px;border-radius:8px;font-family:var(--atsfontFamily);font-size:12px;font-weight:500;color:#45546e;white-space:nowrap}.task-container[_ngcontent-%COMP%]   .preview-header[_ngcontent-%COMP%]   .preview-header-2[_ngcontent-%COMP%]   .task-type[_ngcontent-%COMP%]   .type[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{font-size:16px;width:16px;height:16px;color:#45546e}.task-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:16px;margin-top:16px;padding:16px 32px;height:calc(var(--dynamicSubHeight) - 8px);background-color:#fafafa;border:1px solid #f3f4f6;overflow-y:auto}.task-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]{display:flex;align-items:start;gap:8px}.task-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .index[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;border-radius:4px;border:1px solid #dadce2;height:40px;min-width:32px;font-family:var(--atsfontFamily);font-size:12px;font-weight:500;color:#5f6c81}.task-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:16px;width:calc(100% - 40px);min-height:125px;border:1px solid #dadce2;border-radius:8px;padding:20px 30px}.task-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .question-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:400;color:#45546e;word-wrap:break-word}.task-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .custom-question-field[_ngcontent-%COMP%]{display:flex;align-items:center;height:35px;width:50%;border-radius:4px;border:1px solid #d2d2d2;padding:4px 8px;font-family:var(--atsfontFamily);font-size:12px;font-weight:400;color:#111434}.task-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .form-field-class-date[_ngcontent-%COMP%]{width:50%;font-size:12px;font-family:var(--atsfontFamily)}.task-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .form-field-class-date[_ngcontent-%COMP%]     .mat-input-element{height:17px!important}.task-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .form-field-class-date[_ngcontent-%COMP%]   .date-picker[_ngcontent-%COMP%]{display:flex;align-items:center;height:17px}.task-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .form-field-class-date[_ngcontent-%COMP%]   .calendar-icon[_ngcontent-%COMP%]{font-size:16px}.task-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .form-field-class-date[_ngcontent-%COMP%]     .mat-input-element, .task-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .form-field-class-date[_ngcontent-%COMP%]     .mat-input-element:disabled{color:#45546e!important}.task-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .form-field-class-date[_ngcontent-%COMP%]     .mat-form-field{width:50%!important;font-size:12px!important;font-family:var(--atsfontFamily)!important}.task-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .form-field-class-date[_ngcontent-%COMP%]     .mat-form-field-wrapper, .task-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .form-field-class-date[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-wrapper{padding-bottom:0!important}.task-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .form-field-class-date[_ngcontent-%COMP%]     .mat-form-field-outline{color:#d2d2d2!important}.task-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .options-row[_ngcontent-%COMP%]{display:flex;align-items:center;width:100%;gap:10px}.task-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .options-row[_ngcontent-%COMP%]   .radio[_ngcontent-%COMP%]{min-width:16px;height:16px;border:1px solid #6e7b8f;border-radius:50%}.task-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .options-row[_ngcontent-%COMP%]   .check[_ngcontent-%COMP%]{min-width:16px;height:16px;border:1px solid #dadce2;border-radius:2px}.task-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .options-row[_ngcontent-%COMP%]   .number[_ngcontent-%COMP%]{min-width:16px;height:16px}.task-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .options-row[_ngcontent-%COMP%]   .number[_ngcontent-%COMP%], .task-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .options-row[_ngcontent-%COMP%]   .option-label[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:400;color:#6e7b8f}.task-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .file-upload[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;gap:6px;border-radius:12px;padding:20px 0;border:1.5px dashed #7d838b}.task-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .file-upload[_ngcontent-%COMP%]   .text-1[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:10px;font-weight:400;color:#7d838b}.task-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .file-upload[_ngcontent-%COMP%]   .text-2[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:10px;font-weight:500;color:#111434;text-decoration:underline}.task-container[_ngcontent-%COMP%]   .preview-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .file-upload-restriction[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:12px;font-weight:400;color:#8b95a5}.task-container[_ngcontent-%COMP%]   .loading-state-ui[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;flex-direction:column;background-color:#fff;height:calc(var(--dynamicHeight) - 112px)}.task-container[_ngcontent-%COMP%]   .loading-state-ui[_ngcontent-%COMP%]   .no-data-title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:500;color:#1b2140}.task-container[_ngcontent-%COMP%]   .loading-state-ui[_ngcontent-%COMP%]   .loading-wrapper[_ngcontent-%COMP%]{display:flex;vertical-align:center;justify-content:center;align-items:center}.task-container[_ngcontent-%COMP%]   .loading-state-ui[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]{color:rgba(0,0,0,.3);font-size:16px}.task-container[_ngcontent-%COMP%]   .loading-state-ui[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]:before{content:"Loading...";position:absolute;overflow:hidden;max-width:7em;white-space:nowrap;background:linear-gradient(270deg,var(--atsprimaryColor),var(--atssecondaryColor) 105.29%);background-clip:text;-webkit-background-clip:text;-webkit-text-fill-color:transparent;animation:loading 6s linear infinite}@keyframes loading{0%{max-width:0}}.activation-overlay-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:4px;border:1.5px solid #b9c0ca;border-radius:8px;padding:8px;background-color:#fff}.activation-overlay-container[_ngcontent-%COMP%]   .single-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;border-radius:4px;padding:4px 8px;cursor:pointer}.activation-overlay-container[_ngcontent-%COMP%]   .single-item[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{min-width:8px;min-height:8px;border-radius:50%}.activation-overlay-container[_ngcontent-%COMP%]   .single-item[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:400;color:#5f6c81}.activation-overlay-container[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]{color:#eaeaea}.dropdown-overlay[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:80vh;gap:4px;border:1px solid #d2d2d2;border-radius:4px;background-color:#fff;padding:8px}.dropdown-overlay[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:12px;font-weight:400;color:#45546e}.dropdown-overlay[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]{color:#d2d2d2}.file-upload-dropdown-overlay[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:70px;gap:4px;border:1px solid #d2d2d2;border-radius:4px;background-color:#fff;padding:8px}.file-upload-dropdown-overlay[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:12px;font-weight:400;color:#45546e;cursor:pointer}.file-upload-dropdown-overlay[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]{color:#d2d2d2}.options-row[_ngcontent-%COMP%]{display:flex;align-items:center;width:60%;gap:10px}.options-row[_ngcontent-%COMP%]   .radio[_ngcontent-%COMP%]{min-width:16px;height:16px;border:1px solid #6e7b8f;border-radius:50%}.options-row[_ngcontent-%COMP%]   .check[_ngcontent-%COMP%]{min-width:16px;height:16px;border:1px solid #dadce2;border-radius:2px}.options-row[_ngcontent-%COMP%]   .number[_ngcontent-%COMP%]{min-width:16px;height:16px;font-family:var(--atsfontFamily);font-size:14px;font-weight:400;color:#6e7b8f}.options-row[_ngcontent-%COMP%]   .option-input[_ngcontent-%COMP%]{width:100%}.options-row[_ngcontent-%COMP%]   .option-input[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{height:100%;width:100%;font-family:var(--atsfontFamily);font-size:12px;font-weight:400;color:#6e7b8f;outline:none;border:none;border-bottom:1px solid #6e7b8f}.options-row[_ngcontent-%COMP%]   .option-input[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus, .options-row[_ngcontent-%COMP%]   .option-input[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:hover{border-bottom:1px solid #f44336}.options-row[_ngcontent-%COMP%]   .option-input[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{font-family:var(--atsfontFamily);font-size:12px;font-weight:400;color:#6e7b8f}.options-row[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{width:18px;height:18px;font-size:18px;color:#6e7b8f}']}),e})()}];let Je=(()=>{class e{}return e.\u0275mod=m["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=m["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.k.forChild(Ke)],i.k]}),e})();var et=n("1+mW"),tt=n("Xi0T"),nt=n("lVl8"),ot=n("FKr1"),it=n("QibW"),at=n("d3UM");let rt=(()=>{class e{}return e.\u0275mod=m["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=m["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[o.CommonModule,Je,et.ApplicantTrackingSystemModule,tt.a,M.h,nt.b,b.c,d.p,d.E,O.b,y.b,S.h,ot.n,V.b,it.c,at.d,g.g,E.b,x.b]]}),e})()},BuRe:function(e,t,n){"use strict";var o=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;t.a=function(e){return"string"==typeof e&&o.test(e)}},Jzeh:function(e,t,n){"use strict";n.d(t,"a",(function(){return c}));var o=n("yuIm"),i=n("fXoL"),a=n("ofXK");function r(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",4),i["\u0275\u0275text"](1," Oops! You're not authorized to view this content. Contact your administrator for access. "),i["\u0275\u0275elementEnd"]())}function l(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",4),i["\u0275\u0275text"](1," KEBS System is unavailable, kindly try after sometime! "),i["\u0275\u0275elementEnd"]())}let c=(()=>{class e{constructor(){this.isRDSPeak=!1}ngOnInit(){this.isRDSPeak=null==o?void 0:o.is_rds_peak}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-access-denied"]],decls:5,vars:2,consts:[[1,"bg-container"],[1,"contents"],["src","https://assets.kebs.app/ATS-noAccess.png",1,"image-styles"],["class","message",4,"ngIf"],[1,"message"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275elementStart"](1,"div",1),i["\u0275\u0275element"](2,"img",2),i["\u0275\u0275template"](3,r,2,0,"div",3),i["\u0275\u0275template"](4,l,2,0,"div",3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngIf",!t.isRDSPeak),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.isRDSPeak))},directives:[a.NgIf],styles:[".bg-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:10px;align-items:center;justify-content:center;background-color:#fff;height:var(--dynamicAccessDeniedHeight)}.bg-container[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:500;color:#111434;text-align:center}.bg-container[_ngcontent-%COMP%]   .image-styles[_ngcontent-%COMP%]{height:200px;width:200px}.bg-container[_ngcontent-%COMP%]   .contents[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center}"]}),e})()},WM9j:function(e,t,n){"use strict";for(var o=n("BuRe"),i=[],a=0;a<256;++a)i.push((a+256).toString(16).substr(1));t.a=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=(i[e[t+0]]+i[e[t+1]]+i[e[t+2]]+i[e[t+3]]+"-"+i[e[t+4]]+i[e[t+5]]+"-"+i[e[t+6]]+i[e[t+7]]+"-"+i[e[t+8]]+i[e[t+9]]+"-"+i[e[t+10]]+i[e[t+11]]+i[e[t+12]]+i[e[t+13]]+i[e[t+14]]+i[e[t+15]]).toLowerCase();if(!Object(o.a)(n))throw TypeError("Stringified UUID is invalid");return n}},pEYl:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var o=n("yuIm"),i=n("fXoL");let a=(()=>{class e{transform(e=0,t=0,n=0,i=0,a=""){if(!a||""==a)return!1;let r={module_id:e,sub_module_id:t,section_id:n,sub_section_id:i};"V"==a&&(r.view_permission=1),"C"==a&&(r.create_permission=1),"E"==a&&(r.edit_permission=1),"DE"==a&&(r.delete_permission=1),"DO"==a&&(r.download_permission=1),"U"==a&&(r.upload_permission=1),"B"==a&&(r.bulk_operation=1);const l=Object.keys(r);return o.roleAccessList.find(e=>l.every(t=>e[t]===r[t]))}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=i["\u0275\u0275definePipe"]({name:"access",type:e,pure:!0}),e})()}}]);