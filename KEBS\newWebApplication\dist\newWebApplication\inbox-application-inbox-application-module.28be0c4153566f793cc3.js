(window.webpackJsonp=window.webpackJsonp||[]).push([[725],{EfcB:function(n,e,t){"use strict";t.r(e),t.d(e,"InboxApplicationModule",(function(){return b}));var o=t("ofXK"),a=t("tyNb"),i=t("fXoL"),r=t("wZkO");function l(n,e){if(1&n&&(i["\u0275\u0275elementStart"](0,"a",4,5),i["\u0275\u0275text"](2),i["\u0275\u0275elementEnd"]()),2&n){const n=e.$implicit,t=i["\u0275\u0275reference"](1);i["\u0275\u0275property"]("routerLink",n.path)("active",t.isActive),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"](" ",n.label," ")}}const c=[{path:"",component:(()=>{class n{constructor(){this.inboxTabLinks=[{label:"Approvals",path:"approvals"},{label:"Tasks",path:"tasks"},{label:"Announcements",path:"announcements"}]}ngOnInit(){}}return n.\u0275fac=function(e){return new(e||n)},n.\u0275cmp=i["\u0275\u0275defineComponent"]({type:n,selectors:[["app-inbox-application-landing-page"]],decls:5,vars:1,consts:[[1,"inbox-styles"],["mat-tab-nav-bar",""],["mat-tab-link","","routerLinkActive","",3,"routerLink","active",4,"ngFor","ngForOf"],[1,"col-12","p-0","inbox-tab-content"],["mat-tab-link","","routerLinkActive","",3,"routerLink","active"],["rla","routerLinkActive"]],template:function(n,e){1&n&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275elementStart"](1,"nav",1),i["\u0275\u0275template"](2,l,3,3,"a",2),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](3,"div",3),i["\u0275\u0275element"](4,"router-outlet"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&n&&(i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngForOf",e.inboxTabLinks))},directives:[r.f,o.NgForOf,a.l,a.j,r.e,a.i],styles:[".inbox-styles[_ngcontent-%COMP%]{background-color:#f0f2f7;margin:0;padding:0;height:91vh}.inbox-styles[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{color:unset!important;text-decoration:none!important}.inbox-styles[_ngcontent-%COMP%]   nav[_ngcontent-%COMP%]{position:fixed;width:100%;z-index:14}.inbox-styles[_ngcontent-%COMP%]   .inbox-tab-content[_ngcontent-%COMP%]{padding-top:48px!important}"]}),n})(),children:[{path:"",redirectTo:"approvals",pathMatch:"full"},{path:"approvals",loadChildren:()=>Promise.all([t.e(7),t.e(0),t.e(376)]).then(t.bind(null,"swbB")).then(n=>n.ApprovalsModule)},{path:"tasks",loadChildren:()=>Promise.all([t.e(7),t.e(658)]).then(t.bind(null,"jvuX")).then(n=>n.TasksModule)},{path:"announcements",loadChildren:()=>t.e(371).then(t.bind(null,"20If")).then(n=>n.AnnouncementsModule)}]}];let s=(()=>{class n{}return n.\u0275mod=i["\u0275\u0275defineNgModule"]({type:n}),n.\u0275inj=i["\u0275\u0275defineInjector"]({factory:function(e){return new(e||n)},imports:[[a.k.forChild(c)],a.k]}),n})();var p=t("NFeN"),d=t("bTqV"),u=t("f0Cb");let b=(()=>{class n{}return n.\u0275mod=i["\u0275\u0275defineNgModule"]({type:n}),n.\u0275inj=i["\u0275\u0275defineInjector"]({factory:function(e){return new(e||n)},imports:[[o.CommonModule,s,r.g,u.b,d.b,p.b]]}),n})()}}]);