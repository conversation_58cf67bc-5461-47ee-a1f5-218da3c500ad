(window.webpackJsonp=window.webpackJsonp||[]).push([[1011,634,765,821,822,858,861,981,983,987,990,991],{H44p:function(e,t,i){"use strict";i.d(t,"a",(function(){return f}));var n=i("xG9w"),r=i("fXoL"),o=i("flaP"),s=i("ofXK"),a=i("Qu3c"),l=i("NFeN");function c(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",9),r["\u0275\u0275elementStart"](1,"div",10),r["\u0275\u0275elementStart"](2,"div"),r["\u0275\u0275text"](3),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](4,"div"),r["\u0275\u0275elementStart"](5,"p",11),r["\u0275\u0275text"](6),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](7,"p",12),r["\u0275\u0275text"](8),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),r["\u0275\u0275advance"](3),r["\u0275\u0275textInterpolate"](null==e.currency[e.index]?null:e.currency[e.index].currency_code),r["\u0275\u0275advance"](3),r["\u0275\u0275textInterpolate"](e.label),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate1"](" ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function d(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",13),r["\u0275\u0275elementStart"](1,"span"),r["\u0275\u0275text"](2),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate1"](" ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function h(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",14),r["\u0275\u0275elementStart"](1,"span",15),r["\u0275\u0275text"](2),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function u(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",16),r["\u0275\u0275elementStart"](1,"span",15),r["\u0275\u0275text"](2),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function p(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",17),r["\u0275\u0275elementStart"](1,"span",18),r["\u0275\u0275text"](2),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function g(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"mat-icon",19),r["\u0275\u0275text"](1,"loop"),r["\u0275\u0275elementEnd"]())}function m(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"div",1),r["\u0275\u0275listener"]("click",(function(){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"]().change()})),r["\u0275\u0275template"](1,c,9,4,"div",2),r["\u0275\u0275template"](2,d,3,2,"div",3),r["\u0275\u0275template"](3,h,3,3,"div",4),r["\u0275\u0275template"](4,u,3,3,"div",5),r["\u0275\u0275template"](5,p,3,3,"div",6),r["\u0275\u0275elementStart"](6,"div",7),r["\u0275\u0275template"](7,g,2,0,"mat-icon",8),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&e){const e=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","big"==e.type),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","small"==e.type),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","medium"==e.type),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","large"==e.type),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","overview"==e.type),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngIf",e.toDisplay)}}let f=(()=>{class e{constructor(e){this.roleService=e,this.showActualAmount=!1,this.currency=[]}set currencyList(e){if(this.currency=e,e){const t=n.findIndex(e,e=>e.currency_code==this.currency_code);-1!=t&&(this.index=t,this.currency[this.index].value=Math.trunc(this.currency[this.index].value))}}ngOnInit(){null==this.toDisplay&&(this.toDisplay=!0),this.tenant_info=this.roleService.currency_info,this.tenantName=this.tenant_info.tenant_name,this.currency_code=this.defaultCurrencyCode?this.defaultCurrencyCode:this.tenant_info.default_currency?this.tenant_info.default_currency:"INR",this.setCurrency(),this.isConvertValue=null==this.tenant_info.is_to_convert_currency_value||this.tenant_info.is_to_convert_currency_value}setCurrency(){if(this.currency){const e=n.findIndex(this.currency,e=>e.currency_code==this.currency_code);-1!=e&&(this.index=e,this.currency[this.index].value=Math.trunc(this.currency[this.index].value))}}change(){void 0!==event.stopPropagation?event.stopPropagation():void 0!==event.cancelBubble&&(event.cancelBubble=!0),this.currency.length>1&&(this.index=++this.index%this.currency.length),this.currency[this.index].value=Math.trunc(this.currency[this.index].value)}convert(e,t){return e?(this.showActualAmount?e=new Intl.NumberFormat("INR"==t?"en-IN":"en-US",{}).format(e)+" "+t:1==this.isConvertValue?e="INR"==t?(e/1e7).toFixed(2)+" Cr":(e/1e6).toFixed(2)+" M":0!=this.isConvertValue||n.contains(["big","small"],this.type)?0==this.isConvertValue&&n.contains(["big","small"],this.type)&&(e="INR"==t?"big"!=this.type?"INR "+new Intl.NumberFormat("en-IN",{}).format(e):new Intl.NumberFormat("en-IN",{}).format(e):"big"!=this.type?t+" "+new Intl.NumberFormat("en-US",{}).format(e):new Intl.NumberFormat("en-US",{}).format(e)):e="INR"==t?(e/1e7).toFixed(2)+" Cr":(e/1e6).toFixed(2)+" M",e):"-"}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](o.a))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["app-currency"]],inputs:{type:"type",label:"label",showActualAmount:"showActualAmount",acl:"acl",toDisplay:"toDisplay",defaultCurrencyCode:"defaultCurrencyCode",currencyList:"currencyList"},decls:1,vars:1,consts:[["class","d-flex currency-wrapper",3,"click",4,"ngIf"],[1,"d-flex","currency-wrapper",3,"click"],["class","d-flex",3,"matTooltip",4,"ngIf"],["class","data-label",3,"matTooltip",4,"ngIf"],["class","data-medium d-flex",3,"matTooltip",4,"ngIf"],["class","data-large d-flex",3,"matTooltip",4,"ngIf"],["class","justify-content-center",3,"matTooltip",4,"ngIf"],[1,"d-flex","align-items-center"],["class","change-icon pl-2","matTooltip","Next currency",4,"ngIf"],[1,"d-flex",3,"matTooltip"],[1,"pr-3","my-auto","currency-code","justify-content-center"],[1,"m-0","header"],[1,"m-0","data-label","pt-1"],[1,"data-label",3,"matTooltip"],[1,"data-medium","d-flex",3,"matTooltip"],[1,"justify-content-center"],[1,"data-large","d-flex",3,"matTooltip"],[1,"justify-content-center",3,"matTooltip"],[1,"data-overview"],["matTooltip","Next currency",1,"change-icon","pl-2"]],template:function(e,t){1&e&&r["\u0275\u0275template"](0,m,8,6,"div",0),2&e&&r["\u0275\u0275property"]("ngIf",t.currency)},directives:[s.NgIf,a.a,l.a],styles:[".header[_ngcontent-%COMP%]{color:#5f5f5f;font-size:11px!important}.data-label[_ngcontent-%COMP%]{font-size:13px!important}.change-icon[_ngcontent-%COMP%]{font-size:18px;width:auto;height:auto;visibility:hidden;cursor:default}.currency-code[_ngcontent-%COMP%]{padding-right:8px;font-size:14px!important}.currency-wrapper[_ngcontent-%COMP%]{white-space:nowrap!important;height:100%}.currency-wrapper[_ngcontent-%COMP%]:hover   .change-icon[_ngcontent-%COMP%]{visibility:visible}.data-medium[_ngcontent-%COMP%]{color:#252422!important;font-size:15px!important}.data-large[_ngcontent-%COMP%], .data-medium[_ngcontent-%COMP%]{text-align:center!important;font-weight:440!important;padding-right:4px!important}.data-large[_ngcontent-%COMP%]{font-size:large!important}.data-overview[_ngcontent-%COMP%]{color:#252422!important;font-size:15px!important;padding-left:24px!important;font-weight:500!important;padding-right:4px!important;text-align:center!important;margin:auto!important}"]}),e})()},NJ67:function(e,t,i){"use strict";i.d(t,"a",(function(){return n}));class n{constructor(){this.disabled=!1}onChange(e){}onTouched(e){}writeValue(e){this.value=e}registerOnChange(e){this.onChange=e}registerOnTouched(e){this.onTouched=e}setDisabledState(e){this.disabled=e}}},hJL4:function(e,t,i){"use strict";i.d(t,"a",(function(){return h}));var n=i("mrSG"),r=i("XNiG"),o=i("xG9w"),s=i("fXoL"),a=i("tk/3"),l=i("LcQX"),c=i("XXEo"),d=i("flaP");let h=(()=>{class e{constructor(e,t,i,n){this.http=e,this.UtilityService=t,this.loginService=i,this.roleService=n,this.msg=new r.b,this.taskStatusColor=[],this.approveRequest=(e,t)=>this.http.post("/api/isa/request/approveResourceRequest",{requestDetails:e,approverOid:t}),this.rejectRequest=(e,t)=>this.http.post("/api/isa/request/rejectResourceRequest",{requestDetails:e,approverOid:t}),this.getTaskStatusColor()}getAllRequestsOfUser(e,t,i,n,r,o,s){let a=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getAllRequestsOfUser",{oid:e,statusId:t,statusCode:i,objectIds:n,skip:r,limit:o,filterConfig:s,orgIds:a})}getAllRoleAccess(){return o.where(this.roleService.roles,{application_id:139})}getRequestsBasedOnStatus(e,t,i,n,r,o,s){let a=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getRequestsBasedOnStatus",{oid:e,statusId:t,statusCode:i,objectIds:n,skip:r,limit:o,filterConfig:s,orgIds:a})}getRequestsForAwaitingApproval(e,t,i,n){return this.http.post("/api/isa/request/getRequestsForAwaitingApproval",{oid:e,skip:t,limit:i,filterConfig:n})}getObjectIdsBasedOnOid(e){return this.http.post("/api/isa/request/getObjectIdsBasedOnOid",{oid:e})}getStatusCountBasedOnOid(e,t,i,n){let r=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getStatusCountBasedOnOid",{oid:e,objectIds:t,dataTypeArray:i,filterConfig:n,orgIds:r})}getHeaderCount(e){return this.http.post("/api/isa/request/getHeaderCount",{oid:e})}getRequestBasedOnStatus(e,t){return this.http.post("/api/isa/request/getRequestBasedOnStatus",{oid:e,status:t})}getDataTypeArray(){return this.http.post("/api/isa/request/getISAStatusMasterDataUDRF",{})}sendMsg(e){this.msg.next(e)}getMsg(){return this.msg.asObservable()}getTaskStatusColor(){return new Promise((e,t)=>{this.http.post("/api/isa/request/getTaskStatusNameColor",{}).subscribe(t=>(this.taskStatusColor=t,e(t)),e=>{})})}getAllRequestsForRmg(e){return this.http.post("/api/isa/request/getAllRequestsForRmg",{filterConfig:e})}getTotalForRmg(e){return new Promise((t,i)=>{this.http.post("/api/isa/request/getTotalForRmg",{filterConfig:e}).subscribe(e=>t(e),e=>{})})}getDefaultISAStatusToDisplay(){return this.http.post("/api/isa/request/getISADefaultStatusToDisplay",{})}getCTCbreakUpForSkillSet(e){return new Promise((t,i)=>{this.http.post("/api/isa/request/getCTCbreakUpForSkillSet",e).subscribe(e=>t(e.data),e=>{i(e)})})}getCurrentUserRole(){return this.roleService&&this.roleService.roles&&this.roleService.roles.length>0?this.roleService.roles[0].role_id:null}getTemplateForUser(e){return this.http.post("/api/isa/request/getVisibleFormFieldsForUser",e)}saveAndApproveRequest(e){return this.http.post("/api/isa/request/updBudgetInfoByRequestId",e)}getVisibilityMatrix(e){return this.http.post("/api/isa/request/getAuthorzedFieldsByRoleId",e)}getISAAttachmentFromS3(e){return this.http.post("/api/isa/attachment/getISAAttachmentFromS3",{key:e})}getSkillType(){return this.http.post("/api/isa/request/getSkillType",{})}getSkillLevel(){return this.http.post("/api/isa/request/getSkillLevel",{})}getSkillExperience(){return this.http.post("/api/isa/request/getSkillExperience",{})}getSkillName(e){return this.http.post("/api/isa/request/getSkillName",{skill_type_id:e})}getIsaActivityReportDownload(){return this.http.post("/api/isa/request/getIsaActivityReportDownload",{})}getISASLAreport(e){return this.http.post("/api/isa/request/getISASLAreport",{filterConfig:e})}getISACustomReport(e){return this.http.post("/api/isa/request/getISACustomReport",{filterConfig:e})}getPositionType(){return this.http.post("/api/employee360/masterData/getPositionType",{})}calculateNetCostBasedOnPosition(e,t,i,r,s,a,l){return Object(n.c)(this,void 0,void 0,(function*(){let n;n=a&&a.length>1&&(yield this.getManpowerCostByOId(a,i,s,2))||(yield this.getManpowerCostBasedOnPosition(e,t,i,s,l));let c=yield this.getNonManpowerCost(t,i,r,s,2),d=yield this.getAllocatedCost(),h=0;h=(n?n.cost:0)+c.length>0?o.reduce(o.pluck(c,"cost"),(e,t)=>e+t,0):0;let u=d.length>0?o.reduce(o.pluck(d,"percentage"),(e,t)=>e+t,0):0;return{cost:h,currency:n&&n.currency_code?n.currency_code:"",manpowerCost:n,nonManpowerCost:c,allocatedCost:d,allocatedCostValue:h*(u/100)}}))}getManpowerCostBasedOnPosition(e,t,i,n,r){return new Promise((o,s)=>{this.http.post("/api/isa/configuration/getManpowerCostBasedOnPosition",{skillRoleId:e,nationalityId:t,locationGroupId:i,unit:n,position:r}).subscribe(e=>o(e),e=>(console.log(e),s(e)))})}getNonManpowerCost(e,t,i,n,r){return new Promise((o,s)=>{this.http.post("/api/project/getNonManpowerCost",{nationalityId:e,locationGroupId:t,locationId:i,unit:n,currency_id:r}).subscribe(e=>o(e),e=>(console.log(e),s(e)))})}getAllocatedCost(){return new Promise((e,t)=>{this.http.post("/api/project/getAllocatedCost","").subscribe(t=>e(t),e=>(console.log(e),t(e)))})}getManpowerCostByOId(e,t,i,n){return new Promise((r,o)=>{this.http.post("/api/project/getEmployeeSalary",{oid:e,location_group_id:t,unit_id:i,currency_id:n}).subscribe(e=>r(e),e=>(console.log(e),o(e)))})}getSkillNameMaster(){return this.http.post("/api/isa/configuration/getSkillName",{})}getVendorOid(e,t){return this.http.post("/api/isa/request/getVendorOid",{vendorNames:e,docID:t})}getVendorList(e,t){return this.http.post("/api/isa/request/getVendorList",{documentID:e,limitval:t})}removeVendor(e,t){return this.http.post("/api/isa/request/removeVendors",{vendorOid:e,documentID:t})}checkVendorOrNot(){return this.http.post("/api/isa/request/checkVendorOrNot",{})}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275inject"](a.c),s["\u0275\u0275inject"](l.a),s["\u0275\u0275inject"](c.a),s["\u0275\u0275inject"](d.a))},e.\u0275prov=s["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},ucYs:function(e,t,i){"use strict";i.d(t,"a",(function(){return l}));var n=i("mrSG"),r=i("xG9w"),o=i("fXoL"),s=i("tk/3"),a=i("BVzC");let l=(()=>{class e{constructor(e,t){this.http=e,this.errorService=t,this.workflowStatusList=[],this.getWorkflowStatusList()}getWorkflowStatusList(){this.http.post("/api/wfPrimary/getWorkflowStatusList","").subscribe(e=>{this.workflowStatusList=e.data},e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server While Getting Workflow Status List",e&&e.params?e.params:e&&e.error?e.error.params:{})})}getWorkflowProperties(e){return new Promise((t,i)=>{this.http.post("/api/wfPrimary/getWorkflowProperties",{applicationId:e}).subscribe(e=>t(e),e=>i(e))})}getWorkflowPropertiesByWorkflowId(e){return new Promise((t,i)=>{this.http.post("/api/wfPrimary/getWorkflowPropertiesByWorkflowId",{workflowId:e}).subscribe(e=>t(e),e=>i(e))})}getApproversHierarchy(e){return new Promise((t,i)=>{this.http.post("/api/wfPrimary/getApproversHierarchy",e).subscribe(e=>t(e),e=>i(e))})}createWorkflowItems(e){return new Promise((t,i)=>{this.http.post("/api/wfPrimary/createWorkflowItems",e).subscribe(e=>t(e),e=>i(e))})}getWorkflowDetails(e){return new Promise((t,i)=>{this.http.post("/api/wfPrimary/getWorkflowDetails",{workflowId:e}).subscribe(e=>t(e),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while getting workflow details",e&&e.params?e.params:e&&e.error?e.error.params:{})})})}formatApproversHierarchy(e,t){return Object(n.c)(this,void 0,void 0,(function*(){0==e.length&&t.cc0&&t.cc0.appr0&&t.cc0.appr0.length>0&&e.push([{approvalType:"I",purposeOfInsertingIntoArray:"Just to ensure that 'I' type data is used, if it exists"}]);for(let i=0;i<e.length;i++){let n=[],o=r.keys(t["cc"+i]);for(let r=0;r<o.length;r++)for(let s=0;s<t["cc"+i][o[r]].length;s++){let a={name:t["cc"+i][o[r]][s].DELEGATE_NAME,oid:t["cc"+i][o[r]][s].DELEGATE_OID,level:r+1,designation:t["cc"+i][o[r]][s].DELEGATE_DESIGNATION_NAME,is_delegated:t["cc"+i][o[r]][s].IS_DELEGATED,role:t["cc"+i][o[r]][s].DELEGATE_ROLE_NAME};if(1==t["cc"+i][o[r]][s].IS_DELEGATED&&(a.delegated_by={name:t["cc"+i][o[r]][s].APPROVER_NAME,oid:t["cc"+i][o[r]][s].APPROVER_OID,level:r+1,designation:t["cc"+i][o[r]][s].APPROVER_DESIGNATION_NAME}),n.push(a),i==e.length-1&&r==o.length-1&&s==t["cc"+i][o[r]].length-1)return n}}}))}storeComments(e,t,i){return new Promise((n,r)=>{this.http.post("/api/wfPrimary/updateComments",{workflowHeaderId:e,newComments:t,commentor:i}).subscribe(e=>n(e),e=>(this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server Error while Updating Workflow Comments",e&&e.params?e.params:e&&e.error?e.error.params:{}),r(e)))})}updateWorkflowItems(e){return new Promise((t,i)=>{this.http.post("/api/wfPrimary/updateWorkflowItems",e).subscribe(e=>t(e),e=>(console.log(e),i(e)))})}formatApproversHierarchyForOpportunityApprovalActivity(e){return Object(n.c)(this,void 0,void 0,(function*(){for(let t=0;t<1;t++){let i=[],n=r.keys(e["cc"+t]);for(let r=0;r<n.length;r++)for(let o=0;o<e["cc"+t][n[r]].length;o++){let s={name:e["cc"+t][n[r]][o].DELEGATE_NAME,oid:e["cc"+t][n[r]][o].DELEGATE_OID,level:e["cc"+t][n[r]][o].APPROVAL_ORDER,designation:e["cc"+t][n[r]][o].DELEGATE_DESIGNATION_NAME,is_delegated:e["cc"+t][n[r]][o].IS_DELEGATED};if(1==e["cc"+t][n[r]][o].IS_DELEGATED&&(s.delegated_by={name:e["cc"+t][n[r]][o].APPROVER_NAME,oid:e["cc"+t][n[r]][o].APPROVER_OID,level:e["cc"+t][n[r]][o].APPROVAL_ORDER,designation:e["cc"+t][n[r]][o].APPROVER_DESIGNATION_NAME}),i.push(s),r==n.length-1&&o==e["cc"+t][n[r]].length-1)return i}}}))}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275inject"](s.c),o["\u0275\u0275inject"](a.a))},e.\u0275prov=o["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},w76M:function(e,t,i){"use strict";i.d(t,"a",(function(){return c})),i.d(t,"b",(function(){return d}));var n=i("jhN1"),r=i("fXoL"),o=i("oHs6"),s=i("PVOt"),a=i("6t9p");const l=["*"];let c=(()=>{let e=class extends s.b{constructor(e,t,i,n,r,o,s,a){super(e,t,i,n,s,a),this._watcherHelper=n,this._idh=r,this._createEventEmitters([{subscribe:"contentReady",emit:"onContentReady"},{subscribe:"disposing",emit:"onDisposing"},{subscribe:"hidden",emit:"onHidden"},{subscribe:"hiding",emit:"onHiding"},{subscribe:"initialized",emit:"onInitialized"},{subscribe:"optionChanged",emit:"onOptionChanged"},{subscribe:"resize",emit:"onResize"},{subscribe:"resizeEnd",emit:"onResizeEnd"},{subscribe:"resizeStart",emit:"onResizeStart"},{subscribe:"showing",emit:"onShowing"},{subscribe:"shown",emit:"onShown"},{subscribe:"titleRendered",emit:"onTitleRendered"},{emit:"accessKeyChange"},{emit:"animationChange"},{emit:"closeOnOutsideClickChange"},{emit:"containerChange"},{emit:"contentTemplateChange"},{emit:"deferRenderingChange"},{emit:"disabledChange"},{emit:"dragEnabledChange"},{emit:"elementAttrChange"},{emit:"focusStateEnabledChange"},{emit:"fullScreenChange"},{emit:"heightChange"},{emit:"hintChange"},{emit:"hoverStateEnabledChange"},{emit:"maxHeightChange"},{emit:"maxWidthChange"},{emit:"minHeightChange"},{emit:"minWidthChange"},{emit:"positionChange"},{emit:"resizeEnabledChange"},{emit:"rtlEnabledChange"},{emit:"shadingChange"},{emit:"shadingColorChange"},{emit:"showCloseButtonChange"},{emit:"showTitleChange"},{emit:"tabIndexChange"},{emit:"titleChange"},{emit:"titleTemplateChange"},{emit:"toolbarItemsChange"},{emit:"visibleChange"},{emit:"widthChange"}]),this._idh.setHost(this),o.setHost(this)}get accessKey(){return this._getOption("accessKey")}set accessKey(e){this._setOption("accessKey",e)}get animation(){return this._getOption("animation")}set animation(e){this._setOption("animation",e)}get closeOnOutsideClick(){return this._getOption("closeOnOutsideClick")}set closeOnOutsideClick(e){this._setOption("closeOnOutsideClick",e)}get container(){return this._getOption("container")}set container(e){this._setOption("container",e)}get contentTemplate(){return this._getOption("contentTemplate")}set contentTemplate(e){this._setOption("contentTemplate",e)}get deferRendering(){return this._getOption("deferRendering")}set deferRendering(e){this._setOption("deferRendering",e)}get disabled(){return this._getOption("disabled")}set disabled(e){this._setOption("disabled",e)}get dragEnabled(){return this._getOption("dragEnabled")}set dragEnabled(e){this._setOption("dragEnabled",e)}get elementAttr(){return this._getOption("elementAttr")}set elementAttr(e){this._setOption("elementAttr",e)}get focusStateEnabled(){return this._getOption("focusStateEnabled")}set focusStateEnabled(e){this._setOption("focusStateEnabled",e)}get fullScreen(){return this._getOption("fullScreen")}set fullScreen(e){this._setOption("fullScreen",e)}get height(){return this._getOption("height")}set height(e){this._setOption("height",e)}get hint(){return this._getOption("hint")}set hint(e){this._setOption("hint",e)}get hoverStateEnabled(){return this._getOption("hoverStateEnabled")}set hoverStateEnabled(e){this._setOption("hoverStateEnabled",e)}get maxHeight(){return this._getOption("maxHeight")}set maxHeight(e){this._setOption("maxHeight",e)}get maxWidth(){return this._getOption("maxWidth")}set maxWidth(e){this._setOption("maxWidth",e)}get minHeight(){return this._getOption("minHeight")}set minHeight(e){this._setOption("minHeight",e)}get minWidth(){return this._getOption("minWidth")}set minWidth(e){this._setOption("minWidth",e)}get position(){return this._getOption("position")}set position(e){this._setOption("position",e)}get resizeEnabled(){return this._getOption("resizeEnabled")}set resizeEnabled(e){this._setOption("resizeEnabled",e)}get rtlEnabled(){return this._getOption("rtlEnabled")}set rtlEnabled(e){this._setOption("rtlEnabled",e)}get shading(){return this._getOption("shading")}set shading(e){this._setOption("shading",e)}get shadingColor(){return this._getOption("shadingColor")}set shadingColor(e){this._setOption("shadingColor",e)}get showCloseButton(){return this._getOption("showCloseButton")}set showCloseButton(e){this._setOption("showCloseButton",e)}get showTitle(){return this._getOption("showTitle")}set showTitle(e){this._setOption("showTitle",e)}get tabIndex(){return this._getOption("tabIndex")}set tabIndex(e){this._setOption("tabIndex",e)}get title(){return this._getOption("title")}set title(e){this._setOption("title",e)}get titleTemplate(){return this._getOption("titleTemplate")}set titleTemplate(e){this._setOption("titleTemplate",e)}get toolbarItems(){return this._getOption("toolbarItems")}set toolbarItems(e){this._setOption("toolbarItems",e)}get visible(){return this._getOption("visible")}set visible(e){this._setOption("visible",e)}get width(){return this._getOption("width")}set width(e){this._setOption("width",e)}get toolbarItemsChildren(){return this._getOption("toolbarItems")}set toolbarItemsChildren(e){this.setChildren("toolbarItems",e)}_createInstance(e,t){return new o.a(e,t)}ngOnDestroy(){this._destroyWidget()}ngOnChanges(e){super.ngOnChanges(e),this.setupChanges("toolbarItems",e)}setupChanges(e,t){e in this._optionsToUpdate||this._idh.setup(e,t)}ngDoCheck(){this._idh.doCheck("toolbarItems"),this._watcherHelper.checkWatchers(),super.ngDoCheck(),super.clearChangedOptions()}_setOption(e,t){let i=this._idh.setupSingle(e,t),n=null!==this._idh.getChanges(e,t);(i||n)&&super._setOption(e,t)}};return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](r.ElementRef),r["\u0275\u0275directiveInject"](r.NgZone),r["\u0275\u0275directiveInject"](s.e),r["\u0275\u0275directiveInject"](s.j),r["\u0275\u0275directiveInject"](s.g),r["\u0275\u0275directiveInject"](s.i),r["\u0275\u0275directiveInject"](n.h),r["\u0275\u0275directiveInject"](r.PLATFORM_ID))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["dx-popup"]],contentQueries:function(e,t,i){if(1&e&&r["\u0275\u0275contentQuery"](i,a.L,!1),2&e){let e;r["\u0275\u0275queryRefresh"](e=r["\u0275\u0275loadQuery"]())&&(t.toolbarItemsChildren=e)}},inputs:{accessKey:"accessKey",animation:"animation",closeOnOutsideClick:"closeOnOutsideClick",container:"container",contentTemplate:"contentTemplate",deferRendering:"deferRendering",disabled:"disabled",dragEnabled:"dragEnabled",elementAttr:"elementAttr",focusStateEnabled:"focusStateEnabled",fullScreen:"fullScreen",height:"height",hint:"hint",hoverStateEnabled:"hoverStateEnabled",maxHeight:"maxHeight",maxWidth:"maxWidth",minHeight:"minHeight",minWidth:"minWidth",position:"position",resizeEnabled:"resizeEnabled",rtlEnabled:"rtlEnabled",shading:"shading",shadingColor:"shadingColor",showCloseButton:"showCloseButton",showTitle:"showTitle",tabIndex:"tabIndex",title:"title",titleTemplate:"titleTemplate",toolbarItems:"toolbarItems",visible:"visible",width:"width"},outputs:{onContentReady:"onContentReady",onDisposing:"onDisposing",onHidden:"onHidden",onHiding:"onHiding",onInitialized:"onInitialized",onOptionChanged:"onOptionChanged",onResize:"onResize",onResizeEnd:"onResizeEnd",onResizeStart:"onResizeStart",onShowing:"onShowing",onShown:"onShown",onTitleRendered:"onTitleRendered",accessKeyChange:"accessKeyChange",animationChange:"animationChange",closeOnOutsideClickChange:"closeOnOutsideClickChange",containerChange:"containerChange",contentTemplateChange:"contentTemplateChange",deferRenderingChange:"deferRenderingChange",disabledChange:"disabledChange",dragEnabledChange:"dragEnabledChange",elementAttrChange:"elementAttrChange",focusStateEnabledChange:"focusStateEnabledChange",fullScreenChange:"fullScreenChange",heightChange:"heightChange",hintChange:"hintChange",hoverStateEnabledChange:"hoverStateEnabledChange",maxHeightChange:"maxHeightChange",maxWidthChange:"maxWidthChange",minHeightChange:"minHeightChange",minWidthChange:"minWidthChange",positionChange:"positionChange",resizeEnabledChange:"resizeEnabledChange",rtlEnabledChange:"rtlEnabledChange",shadingChange:"shadingChange",shadingColorChange:"shadingColorChange",showCloseButtonChange:"showCloseButtonChange",showTitleChange:"showTitleChange",tabIndexChange:"tabIndexChange",titleChange:"titleChange",titleTemplateChange:"titleTemplateChange",toolbarItemsChange:"toolbarItemsChange",visibleChange:"visibleChange",widthChange:"widthChange"},features:[r["\u0275\u0275ProvidersFeature"]([s.e,s.j,s.i,s.g]),r["\u0275\u0275InheritDefinitionFeature"],r["\u0275\u0275NgOnChangesFeature"]],ngContentSelectors:l,decls:1,vars:0,template:function(e,t){1&e&&(r["\u0275\u0275projectionDef"](),r["\u0275\u0275projection"](0))},encapsulation:2}),e})(),d=(()=>{let e=class{};return e.\u0275mod=r["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=r["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[a.bb,a.Gc,a.Vd,a.vd,a.hb,a.lb,a.sb,a.id,a.jd,a.M,s.c,s.f,n.b],a.bb,a.Gc,a.Vd,a.vd,a.hb,a.lb,a.sb,a.id,a.jd,a.M,s.f]}),e})()},yGaM:function(e,t,i){"use strict";i.r(t),i.d(t,"OkrListingModule",(function(){return j}));var n=i("ofXK"),r=i("tyNb"),o=i("mrSG"),s=i("33Jv"),a=i("XNiG"),l=i("xG9w"),c=i("wd/R"),d=i("fXoL"),h=i("0IaG"),u=i("GnQ3"),p=i("25DO"),g=i("XXEo"),m=i("LcQX"),f=i("BVzC"),b=i("+K9r"),C=i("xi/V"),v=i("Wk3H");const y=[{path:"",component:(()=>{class e{constructor(e,t,n,r,l,d,h,u,p){this.$dialog=e,this._udrfService=t,this._OkrService=n,this._loginService=r,this.route=l,this.utilityService=d,this.udrfService=h,this._ErrorService=u,this._okrService=p,this.ikr=[],this.skip=0,this.limit=15,this.sharedCount=3,this.privateCount=0,this.allorgCount=0,this.current_year_start=c(),this.current_year_end=c(),this.udrfBodyColumns=[{item:"",header:"",sales_unit_name:"",isVisible:"true",isActive:!0,type:"goalName",colSize:"3",textClass:"",position:1,hasColumnClick:!0,onColumnClick:this.openObjective.bind(this)},{item:"",header:"",customer_name:"",isVisible:"true",isActive:!0,type:"quater",colSize:"3",textClass:"",position:2,hasColumnClick:!1,onColumnClick:""},{item:"",header:"",opportunity_name:"",isVisible:"true",isActive:!0,type:"owner",colSize:"2",textClass:"",position:3,hasColumnClick:!1,onColumnClick:""},{item:"",header:"",sales_unit_name:"",isVisible:"true",isActive:!0,type:"progress",colSize:"4",textClass:"",position:4,hasColumnClick:!1,onColumnClick:""}],this.btnToggleData=[{name:"Shared OKRs",type:"shared",count:this.sharedCount},{name:"Across ORGs",type:"org",count:this.allorgCount}],this.applicationId=143,this._onDestroy=new a.b,this._onAppApiCalled=new a.b,this.subs=new s.a,this.createQuickTask=()=>Object(o.c)(this,void 0,void 0,(function*(){const{QuickTaskComponent:e}=yield i.e(992).then(i.bind(null,"QCtt"));this.$dialog.open(e,{width:"53%",height:"100%",position:{right:"0px"},autoFocus:!1,maxWidth:"90vw"})})),this.openMilstoneMetricPopUp=()=>Object(o.c)(this,void 0,void 0,(function*(){const{MetricUptMileComponent:e}=yield i.e(0).then(i.bind(null,"eEz1"));this.$dialog.open(e,{width:"46%",height:"100%",position:{right:"0px"},autoFocus:!1,maxWidth:"90vw",data:{}})})),this.openHistoryPopup=()=>Object(o.c)(this,void 0,void 0,(function*(){const{HistoryPopupComponent:e}=yield Promise.all([i.e(76),i.e(0)]).then(i.bind(null,"kXFY"));this.$dialog.open(e,{width:"55%",height:"100%",position:{right:"0px"},autoFocus:!1,maxWidth:"90vw",data:{hideSecondRow:!0,isKeyResult:!1,isInitiative:!1}})}))}ngOnInit(){return Object(o.c)(this,void 0,void 0,(function*(){this.selectedOKRType="org",this._udrfService.udrfUiData.selectedButtonToggle="org",this._udrfService.udrfData.isItemDataLoading=!0,this.oid=this._loginService.getProfile().profile.oid,this.route.parent.params.subscribe(e=>{console.log(e),e.oid&&(this.isAdmin=!0,this.adminInptOid=e.oid),console.log(this.isAdmin)});let e=[],t=yield this._OkrService.getQuarterDate();for(let o of t)e.push({checkboxId:o.id,checkboxName:o.type,checkboxStartValue:o.startDate,checkboxEndValue:o.endDate,isCheckboxDefaultSelected:!1});this.udrfService.udrfFunctions.constructCustomRangeData(10,"date",e);let i=[{checkboxId:"CDCRD",checkboxName:"Current Year",checkboxStartValue:this.current_year_start,checkboxEndValue:this.current_year_end,isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD2",checkboxName:"This Week",checkboxStartValue:this.utilityService.getFormattedDate(c().startOf("week"),c(c().startOf("week")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(c().endOf("week"),c(c().endOf("week")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD3",checkboxName:"This Month",checkboxStartValue:this.utilityService.getFormattedDate(c().startOf("month"),c(c().startOf("month")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(c().endOf("month"),c(c().endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD4",checkboxName:"Next Month",checkboxStartValue:this.utilityService.getFormattedDate(c().add(1,"month").startOf("month"),c(c().add(1,"month").startOf("month")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(c().add(1,"month").endOf("month"),c(c().add(1,"month").endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD5",checkboxName:"Upcoming 3 Months",checkboxStartValue:this.utilityService.getFormattedDate(c().startOf("month"),c(c().startOf("month")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(c().add(2,"month").endOf("month"),c(c().add(2,"month").endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1}];this.udrfService.udrfFunctions.constructCustomRangeData(12,"date",i);let n=[],r=yield this._OkrService.getAllProgValFilter();for(let o of r)n.push({checkboxName:o.range,checkboxStartValue:o.lower_limit,checkboxEndValue:o.upper_limit,isCheckboxDefaultSelected:!1});this.udrfService.udrfFunctions.constructCustomRangeData(11,"date",n),this._udrfService.getAppUdrfConfig(this.applicationId,this.setUDRFData.bind(this))}))}OrgChartRoute(){this.chartRoute()}chartRoute(){window.open("/main/okr/okr-org-chart","_blank")}openObjPopUp(){return Object(o.c)(this,void 0,void 0,(function*(){const{OkrCreationComponent:e}=yield i.e(117).then(i.bind(null,"cYXB"));this.$dialog.open(e,{width:"88%",height:"100%",position:{right:"0px"},autoFocus:!1,maxWidth:"90vw"})}))}onChangeOKRType(e){this.selectedOKRType=e}setUDRFData(){return Object(o.c)(this,void 0,void 0,(function*(){console.log("first",this.btnToggleData),this._udrfService.udrfUiData.showButtonToggle=!0,this._udrfService.udrfUiData.buttonToggleItems=this.btnToggleData,this._udrfService.udrfUiData.itemDataType="Objective, Key Result, Initiative",this._udrfService.udrfUiData.totalItemDataCount=0,this._udrfService.udrfUiData.showSearchBar=!0,this._udrfService.udrfUiData.showActionButtons=!0,this._udrfService.udrfUiData.showUdrfModalButton=!0,this._udrfService.udrfUiData.showSettingsModalButton=!1,this._udrfService.udrfUiData.isReportDownloading=!1,this._udrfService.udrfUiData.showColumnConfigButton=!1,this._udrfService.udrfUiData.showOrgChartButton=!0,this._udrfService.udrfUiData.showHistoryButton=!0,this._udrfService.udrfUiData.showInfoButton=!1,this._udrfService.udrfUiData.showReportButton=!1,this._udrfService.udrfUiData.OrgChartData=this.OrgChartRoute.bind(this),this._udrfService.udrfUiData.HistoryData=this.showHistory.bind(this),this._udrfService.udrfUiData.InfoData=this.showInfo.bind(this),this._udrfService.udrfUiData.openReport=this.openReport.bind(this),this._udrfService.udrfUiData.toggleSelectedButton=this.toggleOkrType.bind(this),this._udrfService.udrfUiData.summaryCardsItem=this.okrData,this._udrfService.udrfUiData.udrfBodyColumns=this.udrfBodyColumns,this._udrfService.udrfUiData.udrfVisibleBodyColumns=this._udrfService.udrfUiData.udrfVisibleBodyColumns,this._udrfService.udrfUiData.udrfInvisibleBodyColumns=this._udrfService.udrfUiData.udrfInvisibleBodyColumns,this._udrfService.udrfUiData.minNoOfVisibleSummaryCards=10,this._udrfService.udrfUiData.maxNoOfVisibleSummaryCards=10,this._udrfService.udrfUiData.variant=3,this._udrfService.udrfUiData.isOkr=!0,this._udrfService.udrfUiData.ghostButtonUI=!1,this.skip=0,this.limit=15,this.toggleOkrType()}))}getORGOKR(){let e=this.getFilters(),t=this.oid;this.isAdmin&&(t=this.adminInptOid),this.subs.sink=this._OkrService.getORGOKR({oid:t,filters:e,mainSearchParameter:this.udrfService.udrfData.mainSearchParameter}).subscribe(e=>{this.okrData=e,this._udrfService.udrfData.isItemDataLoading=!1,this._udrfService.udrfBodyData=e.data,this.allorgCount=e.data.length,this.btnToggleData[1].count=this.allorgCount,console.log(e)},e=>{this._udrfService.udrfData.isItemDataLoading=!1,console.log(e),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}getPrivateOKR(){let e=this.getFilters(),t=this.oid;this.isAdmin&&(t=this.adminInptOid),console.log(this.udrfService.udrfData.mainSearchParameter),this.subs.sink=this._OkrService.getPrivateOKR({oid:t,filters:e,mainSearchParameter:this.udrfService.udrfData.mainSearchParameter}).subscribe(e=>{this.ikr=[],this._udrfService.udrfData.isItemDataLoading=!1,(e.BAU.length>0||e.KPI.length>0)&&this.ikr.push(e),console.log(this.ikr),this._udrfService.udrfBodyData=this.ikr,console.log(this._udrfService.udrfBodyData),this.privateCount=e.length,this.btnToggleData[0].count=this.privateCount},e=>{this._udrfService.udrfData.isItemDataLoading=!1,console.log(e),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}getSharedOKR(){let e=this.getFilters(),t=this.oid;this.isAdmin&&(t=this.adminInptOid),this.subs.sink=this._OkrService.getSharedOKR({oid:t,filters:e,mainSearchParameter:this.udrfService.udrfData.mainSearchParameter}).subscribe(e=>{this._udrfService.udrfData.isItemDataLoading=!1,this._udrfService.udrfBodyData=e.data,this.sharedCount=e.data.length,this.btnToggleData[0].count=this.sharedCount},e=>{this._udrfService.udrfData.isItemDataLoading=!1,console.log(e),this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}openObjective(){window.open("/main/okr/detail/"+this._udrfService.udrfUiData.onColumnClickItem.objective_details._id+"/objective","_blank")}toggleOkrType(){this._udrfService.udrfData.isItemDataLoading=!0;let e=this._udrfService.udrfUiData.selectedButtonToggle;console.log("Toggled Buton : ",e),this._udrfService.udrfBodyData=[],"org"==e?this.getORGOKR():"private"==e?this.getPrivateOKR():"shared"==e&&this.getSharedOKR()}initReport(){}getFilters(){console.log(this.udrfService.udrfData.mainFilterArray);let e=[];for(let t of this.udrfService.udrfData.mainFilterArray)if(t.isIdBased)e.push({filterName:t.filterName,valueId:t.multiOptionSelectSearchValuesWithId});else if(l.contains(t.multiOptionSelectSearchValues,"Initiative")&&l.contains(t.multiOptionSelectSearchValues,"KeyResult"))e.push({filterName:t.filterName,valueId:[{kr:1,init:1}]});else if(6==t.filterId)e.push("Initiative"==t.multiOptionSelectSearchValues?{filterName:t.filterName,valueId:[{kr:0,init:1}]}:{filterName:t.filterName,valueId:[{kr:1,init:0}]});else if(8==t.filterId)for(let i of t.checkboxValues)i.isCheckboxSelected&&e.push({filterName:t.filterName,valueId:[{lower_limit:i.checkboxStartValue,upper_limit:i.checkboxEndValue}]});else e.push({filterName:t.filterName,valueId:t.multiOptionSelectSearchValues});return console.log(e),e}showHistory(){console.log("history"),this.openHistoryPopup()}showInfo(){console.log("info")}openReport(){console.log("Report"),window.open("/main/reports/okrReport","_blank")}ngOnDestroy(){this.subs.unsubscribe(),this._udrfService.resetUdrfData()}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](h.b),d["\u0275\u0275directiveInject"](u.a),d["\u0275\u0275directiveInject"](p.a),d["\u0275\u0275directiveInject"](g.a),d["\u0275\u0275directiveInject"](r.a),d["\u0275\u0275directiveInject"](m.a),d["\u0275\u0275directiveInject"](u.a),d["\u0275\u0275directiveInject"](f.a),d["\u0275\u0275directiveInject"](b.a))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["okr-obj-list"]],decls:3,vars:0,consts:[[1,"position-relative"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",0),d["\u0275\u0275element"](1,"udrf-header"),d["\u0275\u0275element"](2,"udrf-body"),d["\u0275\u0275elementEnd"]())},directives:[C.a,v.a],styles:[".abs-btn[_ngcontent-%COMP%]{position:absolute;top:9px;right:2rem;z-index:10;margin-top:5px!important;line-height:8px;width:26px;height:26px;margin-right:10px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12);animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}.abs-btn[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}"]}),e})()}];let S=(()=>{class e{}return e.\u0275mod=d["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=d["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[r.k.forChild(y)],r.k]}),e})();var _=i("3PA3"),O=i("Xi0T"),x=i("jaxi"),I=i("kmnG"),w=i("NFeN"),k=i("qFsG"),D=i("bTqV"),E=i("7EHt"),R=i("bv9b"),A=i("5RNC"),T=i("STbY"),P=i("f0Cb"),N=i("7pIB");let j=(()=>{class e{}return e.\u0275mod=d["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=d["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[n.CommonModule,S,h.g,N.c,x.c,w.b,I.e,D.b,k.c,E.b,_.a,R.b,T.e,A.b,P.b,O.a]]}),e})()}}]);