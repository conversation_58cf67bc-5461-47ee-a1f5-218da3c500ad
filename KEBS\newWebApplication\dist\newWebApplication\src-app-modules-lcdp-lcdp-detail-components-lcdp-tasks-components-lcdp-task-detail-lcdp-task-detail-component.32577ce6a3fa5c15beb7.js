(window.webpackJsonp=window.webpackJsonp||[]).push([[959],{cCgr:function(t,e,n){"use strict";n.r(e),n.d(e,"LcdpTaskDetailComponent",(function(){return $}));var i=n("mrSG"),a=n("0IaG"),l=n("7pIB"),o=n("1G5W"),d=n("XNiG"),r=n("AytR"),m=n("ofXK"),s=n("kmnG"),c=n("qFsG"),p=n("bTqV"),u=n("NFeN"),h=n("Qu3c"),g=(n("Xa2L"),n("Xi0T"),n("mD+J"),n("jaxi")),x=n("fXoL"),v=n("wd/R"),f=n("XXEo"),S=n("me71");const _=["scrollFrame"];function y(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"div",17),x["\u0275\u0275elementStart"](1,"div",18),x["\u0275\u0275elementStart"](2,"div",19),x["\u0275\u0275elementStart"](3,"span",20),x["\u0275\u0275text"](4),x["\u0275\u0275pipe"](5,"date"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275text"](6),x["\u0275\u0275elementEnd"](),x["\u0275\u0275element"](7,"app-user-image",21),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"]().$implicit;x["\u0275\u0275advance"](4),x["\u0275\u0275textInterpolate"](x["\u0275\u0275pipeBind2"](5,3,t.time,"medium")),x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate1"](" ",t.comment," "),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("id",t.commentor_oid)}}function E(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"div",22),x["\u0275\u0275element"](1,"app-user-image",23),x["\u0275\u0275elementStart"](2,"div",24),x["\u0275\u0275elementStart"](3,"div",19),x["\u0275\u0275elementStart"](4,"span",25),x["\u0275\u0275text"](5),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](6,"span",26),x["\u0275\u0275text"](7),x["\u0275\u0275pipe"](8,"date"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](9,"div",27),x["\u0275\u0275text"](10),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"]().$implicit;x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("id",t.commentor_oid),x["\u0275\u0275advance"](4),x["\u0275\u0275textInterpolate1"](" ",t.commentor_name," "),x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate"](x["\u0275\u0275pipeBind2"](8,4,t.time,"medium")),x["\u0275\u0275advance"](3),x["\u0275\u0275textInterpolate1"](" ",t.comment," ")}}function k(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"div",14),x["\u0275\u0275template"](1,y,8,6,"div",15),x["\u0275\u0275template"](2,E,11,7,"div",16),x["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit,n=x["\u0275\u0275nextContext"]();x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",t.commentor_oid==n.currentUser.oid),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",t.commentor_oid!=n.currentUser.oid)}}const b=function(t){return{height:t}},C=function(t){return{"min-height":t}};let I=(()=>{class t{constructor(t){this.loginService=t,this.comments=[],this.sendComments=new x.EventEmitter}ngOnInit(){this.currentUser=this.loginService.getProfile().profile,setTimeout(()=>{this.scrollToBottom()},500)}ngAfterViewInit(){this.scrollContainer=this.scrollFrame.nativeElement}scrollToBottom(){this.scrollContainer.scroll({top:this.scrollContainer.scrollHeight,left:0,behavior:"smooth"})}enterComment(t,e){this.comments.push({sequence_number:e,time:v(),commentor_oid:this.currentUser.oid,commentor_name:this.currentUser.name,comment:document.getElementById("comment").value});let n={sequence_number:e,time:v(),commentor_oid:this.currentUser.oid,commentor_name:this.currentUser.name,comment:document.getElementById("comment").value};this.sendComments.emit(n),this.scrollToBottom(),document.getElementById("comment").value=""}onKeydown(t){"Enter"===t.key&&this.enterComment(void 0,this.comments.length+1)}getCommentBoxHeight(){return this.commentBoxHeight?this.commentBoxHeight:"70vh"}getCommentScrollHeight(){return this.commentBoxScrollHeight?this.commentBoxScrollHeight:"93%"}}return t.\u0275fac=function(e){return new(e||t)(x["\u0275\u0275directiveInject"](f.a))},t.\u0275cmp=x["\u0275\u0275defineComponent"]({type:t,selectors:[["app-lcdp-task-comment"]],viewQuery:function(t,e){if(1&t&&x["\u0275\u0275viewQuery"](_,!0),2&t){let t;x["\u0275\u0275queryRefresh"](t=x["\u0275\u0275loadQuery"]())&&(e.scrollFrame=t.first)}},inputs:{comments:"comments",commentBoxHeight:"commentBoxHeight",commentBoxScrollHeight:"commentBoxScrollHeight"},outputs:{sendComments:"sendComments"},decls:15,vars:8,consts:[[1,"container-fluid","d-flex","flex-column","p-0",3,"ngStyle"],["id","scrollDiv",1,"overflow-scroll",3,"ngStyle"],["scrollFrame",""],["class","row mt-3","style","min-width: 90%",4,"ngFor","ngForOf"],[1,"row","mx-0","mt-auto","h-40","pt-2",2,"border-top","1px solid #c1bfbf"],[1,"col-1",2,"padding-top","9px"],["imgWidth","30px","imgHeight","30px",1,"my-auto","pr-1",3,"id"],[1,"col-10","pl-3","pr-2"],[1,"comment-box"],["matInput","","placeholder","comments","id","comment",3,"keydown"],["comment",""],[1,"col-1"],["mat-icon-button","",1,"my-auto","send-button",3,"click"],[2,"color","white","font-size","22px"],[1,"row","mt-3",2,"min-width","90%"],["class","col-12 pl-1 d-flex",4,"ngIf"],["class","col-12 pl-2  d-flex",4,"ngIf"],[1,"col-12","pl-1","d-flex"],[1,"chat-outgoing"],[1,"row",2,"height","21px !important"],[1,"comment-date","ml-auto"],["imgWidth","28px","imgHeight","28px",1,"my-auto","pr-1","ml-1",3,"id"],[1,"col-12","pl-2","d-flex"],["imgWidth","28px","imgHeight","28px",1,"my-auto","pr-1",3,"id"],[1,"chat-incoming"],[1,"comment-user-name"],[1,"comment-date"],[1,"row"]],template:function(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"div",0),x["\u0275\u0275elementStart"](1,"div",1,2),x["\u0275\u0275template"](3,k,3,2,"div",3),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](4,"div",4),x["\u0275\u0275elementStart"](5,"div",5),x["\u0275\u0275element"](6,"app-user-image",6),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](7,"div",7),x["\u0275\u0275elementStart"](8,"mat-form-field",8),x["\u0275\u0275elementStart"](9,"input",9,10),x["\u0275\u0275listener"]("keydown",(function(t){return e.onKeydown(t)})),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](11,"div",11),x["\u0275\u0275elementStart"](12,"button",12),x["\u0275\u0275listener"]("click",(function(t){return e.enterComment(t.target.value,e.comments.length+1)})),x["\u0275\u0275elementStart"](13,"mat-icon",13),x["\u0275\u0275text"](14,"send"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()),2&t&&(x["\u0275\u0275property"]("ngStyle",x["\u0275\u0275pureFunction1"](4,b,e.getCommentBoxHeight())),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngStyle",x["\u0275\u0275pureFunction1"](6,C,e.getCommentScrollHeight())),x["\u0275\u0275advance"](2),x["\u0275\u0275property"]("ngForOf",e.comments),x["\u0275\u0275advance"](3),x["\u0275\u0275property"]("id",e.currentUser.oid))},directives:[m.NgStyle,m.NgForOf,S.a,s.c,c.b,p.a,u.a,m.NgIf],pipes:[m.DatePipe],styles:[".comment-row[_ngcontent-%COMP%]{height:70vh}.footer-bar[_ngcontent-%COMP%]{position:absolute;bottom:0!important}.comment-box[_ngcontent-%COMP%]{width:100%;font-size:14px}.comment-box[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{height:30px;padding-bottom:9px}.comment-box[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding-bottom:0!important}.comment-date[_ngcontent-%COMP%]{font-size:11px;color:#959696}.comment-user-name[_ngcontent-%COMP%]{padding-right:10px;font-size:11px;font-weight:400;color:#6b6969}.comment-user-name-left[_ngcontent-%COMP%]{padding-left:20px;font-size:11px;font-weight:400;color:#252424}.chat-incoming[_ngcontent-%COMP%]{padding:5px 50px 5px 22px;text-align:left}.chat-outgoing[_ngcontent-%COMP%]{margin-left:auto!important;padding:5px 22px}.chat-incoming[_ngcontent-%COMP%], .chat-outgoing[_ngcontent-%COMP%]{background:#ececec;display:inline-block;font-size:14px;color:#454546;border-radius:7px;max-width:90%;text-align:right;position:relative}.chat-incoming[_ngcontent-%COMP%]{margin-right:auto!important;padding:5px 15px}.smallCardIcon[_ngcontent-%COMP%]{font-size:18px!important;color:#66615b!important}.send-button[_ngcontent-%COMP%]{width:35px;height:35px;line-height:34px;background-color:#cf0001;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}"]}),t})();n("3beV");var w=n("z52X"),M=n("LcQX"),O=n("nAV5"),P=n("JLuW"),F=n("mS9j"),T=n("yArD"),B=n("2PFs"),D=n("Hg/M");const z=["uploadElement"];function A(t,e){if(1&t&&x["\u0275\u0275element"](0,"app-user-profile",17),2&t){const t=x["\u0275\u0275nextContext"]();x["\u0275\u0275property"]("oid",null==t.taskItem?null:t.taskItem.assigned_to)}}function H(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"span"),x["\u0275\u0275text"](1,"Not Assigned"),x["\u0275\u0275elementEnd"]())}function j(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"div",37),x["\u0275\u0275text"](1," - "),x["\u0275\u0275elementEnd"]())}function N(t,e){if(1&t){const t=x["\u0275\u0275getCurrentView"]();x["\u0275\u0275elementContainerStart"](0),x["\u0275\u0275elementStart"](1,"div",42),x["\u0275\u0275elementStart"](2,"div",43),x["\u0275\u0275elementStart"](3,"button",44),x["\u0275\u0275element"](4,"i",45),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](5,"div",46),x["\u0275\u0275text"](6),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](7,"div",47),x["\u0275\u0275text"](8),x["\u0275\u0275pipe"](9,"date"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](10,"div",47),x["\u0275\u0275text"](11),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](12,"div",47),x["\u0275\u0275text"](13),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](14,"div",48),x["\u0275\u0275elementStart"](15,"button",49),x["\u0275\u0275listener"]("click",(function(){x["\u0275\u0275restoreView"](t);const n=e.index;return x["\u0275\u0275nextContext"](2).downloadFile(n)})),x["\u0275\u0275elementStart"](16,"mat-icon",50),x["\u0275\u0275text"](17,"file_download"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](18,"button",51),x["\u0275\u0275listener"]("click",(function(){x["\u0275\u0275restoreView"](t);const n=e.index;return x["\u0275\u0275nextContext"](2).deleteFile(n)})),x["\u0275\u0275elementStart"](19,"mat-icon",50),x["\u0275\u0275text"](20,"delete"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementContainerEnd"]()}if(2&t){const t=e.$implicit,n=x["\u0275\u0275nextContext"](2);x["\u0275\u0275advance"](4),x["\u0275\u0275classMap"](n.fileType[null==t?null:t.type]||"ms-Icon ms-Icon--FileTemplate"),x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate1"](" ",t.file_name?t.file_name:"-"," "),x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate1"](" ",x["\u0275\u0275pipeBind2"](9,6,null==t?null:t.created_at,"dd-MMM-yy")," "),x["\u0275\u0275advance"](3),x["\u0275\u0275textInterpolate1"](" ",t.created_by_name?t.created_by_name:"-"," "),x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate1"](" ",null==t?null:t.displaySize," ")}}function U(t,e){if(1&t&&(x["\u0275\u0275elementContainerStart"](0),x["\u0275\u0275elementStart"](1,"div",38),x["\u0275\u0275elementStart"](2,"div",26),x["\u0275\u0275text"](3," Type "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](4,"div",39),x["\u0275\u0275text"](5," File name "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](6,"div",40),x["\u0275\u0275text"](7," Created on "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](8,"div",40),x["\u0275\u0275text"](9," Created by "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](10,"div",40),x["\u0275\u0275text"](11," Size "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275template"](12,N,21,9,"ng-container",41),x["\u0275\u0275elementContainerEnd"]()),2&t){const t=x["\u0275\u0275nextContext"]();x["\u0275\u0275advance"](12),x["\u0275\u0275property"]("ngForOf",null==t.taskItem?null:t.taskItem.attachments)}}function R(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"div",52),x["\u0275\u0275elementStart"](1,"div",53),x["\u0275\u0275elementStart"](2,"span",54),x["\u0275\u0275text"](3,"No Attachment found ! "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](4,"div",55),x["\u0275\u0275element"](5,"img",56),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]())}const V=function(t){return{"btn-toggle-selected":t}};function K(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"mat-button-toggle",57),x["\u0275\u0275text"](1),x["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit,n=x["\u0275\u0275nextContext"]();x["\u0275\u0275property"]("value",t.tab_id)("ngClass",x["\u0275\u0275pureFunction1"](3,V,n.selectedMetaToggle==t.tab_id)),x["\u0275\u0275advance"](1),x["\u0275\u0275textInterpolate1"](" ",t.tab_label,"")}}function L(t,e){if(1&t){const t=x["\u0275\u0275getCurrentView"]();x["\u0275\u0275elementStart"](0,"app-lcdp-task-comment",58),x["\u0275\u0275listener"]("sendComments",(function(e){return x["\u0275\u0275restoreView"](t),x["\u0275\u0275nextContext"]().getComments(e)})),x["\u0275\u0275elementEnd"]()}if(2&t){const t=x["\u0275\u0275nextContext"]();x["\u0275\u0275property"]("comments",null==t.taskItem?null:t.taskItem.comments)("commentBoxHeight","64vh")}}function W(t,e){if(1&t&&(x["\u0275\u0275elementContainerStart"](0),x["\u0275\u0275elementStart"](1,"div",7),x["\u0275\u0275elementStart"](2,"div",39),x["\u0275\u0275text"](3," Manual updated at: "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](4,"div",59),x["\u0275\u0275text"](5),x["\u0275\u0275pipe"](6,"date"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](7,"div",7),x["\u0275\u0275elementStart"](8,"div",39),x["\u0275\u0275text"](9," Manual updated by: "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](10,"div",59),x["\u0275\u0275text"](11),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementContainerEnd"]()),2&t){const t=x["\u0275\u0275nextContext"](2);x["\u0275\u0275advance"](5),x["\u0275\u0275textInterpolate1"](" ",x["\u0275\u0275pipeBind2"](6,2,null==t.taskItem?null:t.taskItem.manual_updated_at,"dd-MMM-yy hh:mm a")," "),x["\u0275\u0275advance"](6),x["\u0275\u0275textInterpolate1"](" ",null==t.taskItem?null:t.taskItem.manual_updated_by_name," ")}}function X(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"div",71),x["\u0275\u0275element"](1,"span",72),x["\u0275\u0275elementEnd"]())}function Q(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"div",63),x["\u0275\u0275elementStart"](1,"div",64),x["\u0275\u0275elementStart"](2,"div",65),x["\u0275\u0275element"](3,"span",66),x["\u0275\u0275elementStart"](4,"mat-icon",67),x["\u0275\u0275text"](5," keyboard_arrow_right "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](6,"div",68),x["\u0275\u0275elementStart"](7,"span",69),x["\u0275\u0275text"](8),x["\u0275\u0275pipe"](9,"date"),x["\u0275\u0275elementStart"](10,"small"),x["\u0275\u0275text"](11),x["\u0275\u0275pipe"](12,"date"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275text"](13),x["\u0275\u0275pipe"](14,"date"),x["\u0275\u0275elementStart"](15,"small"),x["\u0275\u0275text"](16),x["\u0275\u0275pipe"](17,"date"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275template"](18,X,2,0,"div",70),x["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit,n=e.index,i=x["\u0275\u0275nextContext"](3);x["\u0275\u0275advance"](8),x["\u0275\u0275textInterpolate1"](" ",t.start_time?x["\u0275\u0275pipeBind2"](9,5,t.start_time,"dd-MMM-yy"):"-"," "),x["\u0275\u0275advance"](3),x["\u0275\u0275textInterpolate"](t.start_time?x["\u0275\u0275pipeBind2"](12,8,t.start_time,"hh:mm a"):"-"),x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate1"](" to ",t.end_time?x["\u0275\u0275pipeBind2"](14,11,t.end_time,"dd-MMM-yy"):"-"," "),x["\u0275\u0275advance"](3),x["\u0275\u0275textInterpolate"](t.end_time?x["\u0275\u0275pipeBind2"](17,14,t.end_time,"hh:mm a"):"-"),x["\u0275\u0275advance"](2),x["\u0275\u0275property"]("ngIf",n<(null==i.taskItem?null:i.taskItem.task_completion_time_intervals.length)-1)}}function G(t,e){if(1&t&&(x["\u0275\u0275elementContainerStart"](0),x["\u0275\u0275elementStart"](1,"div",7),x["\u0275\u0275elementStart"](2,"div",60),x["\u0275\u0275text"](3," Day Intervals "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](4,"div",61),x["\u0275\u0275elementStart"](5,"div",20),x["\u0275\u0275template"](6,Q,19,17,"div",62),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementContainerEnd"]()),2&t){const t=x["\u0275\u0275nextContext"](2);x["\u0275\u0275advance"](6),x["\u0275\u0275property"]("ngForOf",null==t.taskItem?null:t.taskItem.task_completion_time_intervals)}}function q(t,e){if(1&t&&(x["\u0275\u0275elementContainerStart"](0),x["\u0275\u0275elementStart"](1,"div",7),x["\u0275\u0275elementStart"](2,"div",39),x["\u0275\u0275text"](3," Duration: "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](4,"div",59),x["\u0275\u0275text"](5),x["\u0275\u0275pipe"](6,"durationFormat"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275template"](7,W,12,5,"ng-container",14),x["\u0275\u0275template"](8,G,7,1,"ng-container",14),x["\u0275\u0275elementContainerEnd"]()),2&t){const t=x["\u0275\u0275nextContext"]();x["\u0275\u0275advance"](5),x["\u0275\u0275textInterpolate1"](" ",x["\u0275\u0275pipeBind1"](6,3,null==t.taskItem?null:t.taskItem.duration)," "),x["\u0275\u0275advance"](2),x["\u0275\u0275property"]("ngIf",null==t.taskItem?null:t.taskItem.is_manual_update),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",!(null!=t.taskItem&&t.taskItem.is_manual_update)&&(null==t.taskItem?null:t.taskItem.task_completion_time_intervals)&&(null==t.taskItem?null:t.taskItem.task_completion_time_intervals.length))}}const J=function(t){return{background:t}};let $=(()=>{class t{constructor(t,e,n,a,m,s,c){this.dialogRef=t,this.inData=e,this._lcdpService=n,this._login=a,this._util=m,this.inlineEditPopupService=s,this.sharedLazyLoadedComponentsService=c,this._onDestroy=new d.b,this.currentUser=this._login.getProfile().profile,this.allowedMimeType=["*/*"],this.maxFileSize=10485760,this.isFromTicket=!0,this.uploader=new l.d({url:"/api/appBuilder/lcdp/attachments/uploadAttachment",authToken:"Bearer "+this._login.getToken(),autoUpload:!0,disableMultipart:!1,maxFileSize:this.maxFileSize,headers:[{name:"user",value:this.currentUser.name}]}),this.fileType=r.a.fileuploadedSupportedFileTypes,this.isUploading=!1,this.dataArray=[],this.inlineEditField="",this.editType="",this.metaTabs=[{tab_id:1,tab_label:"Task Timer"},{tab_id:2,tab_label:"Comments"}],this.formatFileSize=()=>{if(this.taskItem.attachments)for(let t of this.taskItem.attachments)t.displaySize=this.bytesToSize(t.size)},this.bytesToSize=t=>{if(0===t)return"0 Bytes";const e=Math.floor(Math.log(t)/Math.log(1024));return parseFloat((t/Math.pow(1024,e)).toFixed(2))+" "+["Bytes","KB","MB","GB","TB","PB","EB","ZB","YB"][e]},this.fileUploadChanges=()=>{this.uploader.onProgressItem=t=>{this.isUploading=!0},this.uploader.onCompleteItem=(t,e,n,i)=>{if(200==n&&e&&e.length>0){this.isUploading=!1;let t=JSON.parse(e);this.updateAttachment(t.data),this.uploadElementRef.nativeElement.value=""}else this._util.showToastMessage("Unable to upload")}},this.downloadFile=t=>{let e=this.taskItem.attachments[t];e&&e.cdn_link&&this._lcdpService.downloadAttachment(this._lcdpService.lcdpDetails.lcdpApplicationId,null,e.cdn_link).pipe(Object(o.a)(this._onDestroy)).subscribe(t=>Object(i.c)(this,void 0,void 0,(function*(){"S"==t.messType&&t.data?window.open(t.data):this._util.showToastMessage(t.messText)})),t=>{this._lcdpService.showErrorMessage(t)})},this.updateAttachment=t=>{this.taskItem.attachments.push(t),this.updateTaskObject("attachments",this.taskItem.attachments),this.formatFileSize()},this.updateTaskObject=(t,e)=>{this._lcdpService.editTask(this._lcdpService.lcdpDetails.lcdpApplicationId,this._lcdpService.lcdpDetails.recordId,{_id:this.taskItem._id,update_field:t,update_value:e}).pipe(Object(o.a)(this._onDestroy)).subscribe(t=>Object(i.c)(this,void 0,void 0,(function*(){this._util.showToastMessage("S"==t.messType?"Task updated !":t.messText)})),t=>{this._lcdpService.showErrorMessage(t)})},this.getComments=t=>{this.updateTaskObject("comments",this.taskItem.comments)},this.closeTaskDetailModal=()=>{this.dialogRef.close({event:"Close"})},this.fileUploadChanges()}ngOnInit(){this.inData&&this.inData.modalParams&&(this.taskItem=this.inData.modalParams.taskItem),this.uploader.setOptions({headers:[{name:"lcdp-application-id",value:this._lcdpService.lcdpDetails.lcdpApplicationId},{name:"record-id",value:this._lcdpService.lcdpDetails.recordId}]}),this.formatFileSize(),this.selectedMetaToggle=this.metaTabs[0].tab_id}activateInlineEdit(t,e,n,i){this.dataArray=n,this.inlineEditActiveRow=i,(!this.inlineEditPopupService.inlineEditCallbackSubscription||this.inlineEditPopupService.inlineEditCallbackSubscription&&this.inlineEditPopupService.inlineEditCallbackSubscription.closed)&&(this.inlineEditPopupService.inlineEditCallbackSubscription=this.inlineEditPopupService.inlineEditCallback.subscribe(t=>{!t||0===Object.keys(t).length&&t.constructor===Object||this.inlineEditResponseFunction(t)})),this.inlineEditField=t,this.editType=e,"minimal-dropdown"==e?"Status"==this.inlineEditField&&(this.dropdownConfig={apiDataUpdateKeyName:"id",apiDataSelectedKeyName:"location_name",statusColorMapping:[],maxWidth:"250px"}):"search-dropdown"==e&&("Location"==this.inlineEditField?this.dropdownConfig={inlineEditField:t,dropdownSelectedValue:this.inlineEditActiveRow.location,apiServiceVariable:this.sharedLazyLoadedComponentsService,apiFunctionName:"searchLocation",apiDataUpdateKeyName:"value",apiDataSelectedKeyName:"name",hasImageView:!1,apiDataImageKeyName:null,maxWidth:"250px"}:"date-picker"==this.editType?this.dropdownConfig={inlineEditField:this.inlineEditField,dropdownSelectedValue:this.inlineEditActiveRow.end_date,apiDataSelectedKeyName:this.inlineEditField,maxWidth:"250px"}:"simple-text"==this.editType&&(this.dropdownConfig={inlineEditField:this.inlineEditField,dropdownSelectedValue:this.inlineEditActiveRow.planned_hours,apiDataSelectedKeyName:this.inlineEditField,inputType:"number",maxWidth:"250px"})),this.dataArray=n,this.inlineEditPopupService.setInlineEditActiveDataSubject({editType:this.editType,dataArray:this.dataArray,dropdownConfig:this.dropdownConfig})}inlineEditResponseFunction(t){if("Location"==this.inlineEditField&&this.inlineEditActiveRow.location!=t[this.dropdownConfig.apiDataSelectedKeyName]){let e=JSON.parse(t[this.dropdownConfig.apiDataUpdateKeyName]);this.updateTaskObject("location",{location_id:e.location_id,office_id:e.office_id}),this.inlineEditActiveRow.location_id=e.location_id,this.inlineEditActiveRow.office_id=e.office_id,this.inlineEditActiveRow.location=t[this.dropdownConfig.apiDataSelectedKeyName]}}deleteFile(t){this._lcdpService.deleteFileInTask(this._lcdpService.lcdpDetails.lcdpApplicationId,this._lcdpService.lcdpDetails.recordId,{_id:this.taskItem._id,key:this.taskItem.attachments[t].key}).pipe(Object(o.a)(this._onDestroy)).subscribe(e=>{"S"==e.messType?this.taskItem.attachments.splice(t,1):this._util.showToastMessage(e.messText)},t=>{this._lcdpService.showErrorMessage(t)})}selectMetaToggle(t){this.selectedMetaToggle=t.value}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}}return t.\u0275fac=function(e){return new(e||t)(x["\u0275\u0275directiveInject"](a.h),x["\u0275\u0275directiveInject"](a.a),x["\u0275\u0275directiveInject"](w.a),x["\u0275\u0275directiveInject"](f.a),x["\u0275\u0275directiveInject"](M.a),x["\u0275\u0275directiveInject"](O.a),x["\u0275\u0275directiveInject"](P.a))},t.\u0275cmp=x["\u0275\u0275defineComponent"]({type:t,selectors:[["app-lcdp-task-detail"]],viewQuery:function(t,e){if(1&t&&x["\u0275\u0275viewQuery"](z,!0),2&t){let t;x["\u0275\u0275queryRefresh"](t=x["\u0275\u0275loadQuery"]())&&(e.uploadElementRef=t.first)}},decls:80,vars:30,consts:[[1,"container-fluid","pt-2","pb-2","task-detail-styles"],[2,"background-color","#F9F9F9"],[1,"row"],[1,"col-11","pt-2","headingBold"],[1,"col-1","mt-0","d-flex"],["mat-icon-button","",1,"ml-auto","close-button","mt-1",3,"click"],["matTooltip","Close",1,"close-Icon"],[1,"row","pt-2","pb-2"],[1,"col-1","headerFont"],[1,"col-3","normalFont"],[1,"status-dot",3,"ngStyle"],[1,"pl-2"],["matTooltip","Assigned to",1,"col-1","headerFont"],["type","small","imgHeight","28px","imgWidth","28px",3,"oid",4,"ngIf"],[4,"ngIf"],[1,"col-1","pl-0","pr-0","headerFont"],["matTooltip","Created by",1,"col-1","headerFont"],["type","small","imgHeight","28px","imgWidth","28px",3,"oid"],["inlineEdit","",1,"col-3","normalFont",2,"cursor","pointer",3,"click"],["matTooltip","Created on",1,"col-1","headerFont"],[1,"row","pt-2"],[1,"col-8","pl-0"],[1,"col-12","headerFont"],[1,"col-12","pl-4",3,"innerHtml"],["class","pl-4",4,"ngIf"],[1,"col-11","headerFont"],[1,"col-1"],["mat-icon-button","","matTooltip","Add files",1,"view-button-inactive",3,"click"],[1,"iconButton"],["hidden","","type","file","ng2FileSelect","",3,"uploader","accept"],["uploadElement","","moreFileInput",""],["style","text-align: center;padding-top: 3rem;",4,"ngIf"],[1,"col-4","pl-1","pr-1",2,"border-left","solid 1px #cacaca"],[1,"row","p-3","d-flex","justify-content-center"],[2,"border-radius","1rem",3,"value","change"],["class","toggle-btn",3,"value","ngClass",4,"ngFor","ngForOf"],[3,"comments","commentBoxHeight","sendComments",4,"ngIf"],[1,"pl-4"],[1,"row","pt-2","header",2,"border-bottom","solid 1px #cacaca"],[1,"col-4"],[1,"col-2"],[4,"ngFor","ngForOf"],[1,"row",2,"border-bottom","solid 1px #cacaca"],[1,"col-1","pl-2"],["mat-icon-button","",1,"ic-size"],["aria-hidden","true"],[1,"col-4","fileFont","d-flex","my-auto"],[1,"col-2","fileFont","d-flex","my-auto"],[1,"col-1","d-flex","my-auto"],["mat-icon-button","","matTooltip","Download",1,"icon-tray-button","mr-2",3,"click"],[1,"smallCardIcon"],["mat-icon-button","","matTooltip","Delete",1,"icon-tray-button","mr-2",3,"click"],[2,"text-align","center","padding-top","3rem"],[1,"d-flex","pb-2","justify-content-center","align-items-center","slide-in-top"],[2,"font-size","16px","font-weight","normal"],[1,"d-flex","justify-content-center","align-items-center","slide-from-down","pt-2","pb-2"],["src","https://assets.kebs.app/images/noAccounts.png","height","200","width","200",1,"mt-2","mb-2"],[1,"toggle-btn",3,"value","ngClass"],[3,"comments","commentBoxHeight","sendComments"],[1,"col-6"],[1,"col-12","headingBold"],[2,"height","45vh","overflow-y","scroll"],["class","col-12",4,"ngFor","ngForOf"],[1,"col-12"],[1,"row","p-0",2,"height","18px"],[1,"col-3","d-flex"],[1,"status-dot"],[2,"font-size","15px","color","#4d4d4b","vertical-align","sub","cursor","pointer"],[1,"col-9","p-0"],[1,"pl-2","normalFont","pt-1"],["class","row line-height",4,"ngIf"],[1,"row","line-height"],[2,"border-left","1px solid #c7c4c4"]],template:function(t,e){if(1&t){const t=x["\u0275\u0275getCurrentView"]();x["\u0275\u0275elementStart"](0,"div",0),x["\u0275\u0275elementStart"](1,"div",1),x["\u0275\u0275elementStart"](2,"div",2),x["\u0275\u0275elementStart"](3,"div",3),x["\u0275\u0275text"](4),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](5,"div",4),x["\u0275\u0275elementStart"](6,"button",5),x["\u0275\u0275listener"]("click",(function(){return e.closeTaskDetailModal()})),x["\u0275\u0275elementStart"](7,"mat-icon",6),x["\u0275\u0275text"](8,"close"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](9,"div",7),x["\u0275\u0275elementStart"](10,"div",8),x["\u0275\u0275text"](11," Status "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](12,"div",9),x["\u0275\u0275element"](13,"span",10),x["\u0275\u0275elementStart"](14,"span",11),x["\u0275\u0275text"](15),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](16,"div",12),x["\u0275\u0275text"](17," Assigned to "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](18,"div",9),x["\u0275\u0275template"](19,A,1,1,"app-user-profile",13),x["\u0275\u0275template"](20,H,2,0,"span",14),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](21,"div",15),x["\u0275\u0275text"](22," Planned hours "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](23,"div",9),x["\u0275\u0275text"](24),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](25,"div",7),x["\u0275\u0275elementStart"](26,"div",8),x["\u0275\u0275text"](27," Due on "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](28,"div",9),x["\u0275\u0275text"](29),x["\u0275\u0275pipe"](30,"date"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](31,"div",16),x["\u0275\u0275text"](32," Created by "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](33,"div",9),x["\u0275\u0275element"](34,"app-user-profile",17),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](35,"div",15),x["\u0275\u0275text"](36," Actual hours "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](37,"div",9),x["\u0275\u0275text"](38," - "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](39,"div",7),x["\u0275\u0275elementStart"](40,"div",8),x["\u0275\u0275text"](41," Location "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](42,"div",18),x["\u0275\u0275listener"]("click",(function(){return e.activateInlineEdit("Location","search-dropdown",[],e.taskItem)})),x["\u0275\u0275text"](43),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](44,"div",19),x["\u0275\u0275text"](45," Created on "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](46,"div",9),x["\u0275\u0275text"](47),x["\u0275\u0275pipe"](48,"date"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](49,"div",15),x["\u0275\u0275text"](50," Billable hours "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](51,"div",9),x["\u0275\u0275text"](52," - "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](53,"div",20),x["\u0275\u0275elementStart"](54,"div",21),x["\u0275\u0275elementStart"](55,"div",7),x["\u0275\u0275elementStart"](56,"div",22),x["\u0275\u0275text"](57," Task Description "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](58,"div",20),x["\u0275\u0275element"](59,"div",23),x["\u0275\u0275pipe"](60,"safehtml"),x["\u0275\u0275template"](61,j,2,0,"div",24),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](62,"div",7),x["\u0275\u0275elementStart"](63,"div",25),x["\u0275\u0275text"](64," Attachment "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](65,"div",26),x["\u0275\u0275elementStart"](66,"button",27),x["\u0275\u0275listener"]("click",(function(){return x["\u0275\u0275restoreView"](t),x["\u0275\u0275reference"](71).click()})),x["\u0275\u0275elementStart"](67,"mat-icon",28),x["\u0275\u0275text"](68,"cloud_upload"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275element"](69,"input",29,30),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275template"](72,U,13,1,"ng-container",14),x["\u0275\u0275template"](73,R,6,0,"div",31),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](74,"div",32),x["\u0275\u0275elementStart"](75,"div",33),x["\u0275\u0275elementStart"](76,"mat-button-toggle-group",34),x["\u0275\u0275listener"]("change",(function(t){return e.selectMetaToggle(t)})),x["\u0275\u0275template"](77,K,2,5,"mat-button-toggle",35),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275template"](78,L,1,2,"app-lcdp-task-comment",36),x["\u0275\u0275template"](79,q,9,5,"ng-container",14),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()}2&t&&(x["\u0275\u0275advance"](4),x["\u0275\u0275textInterpolate1"](" ",null==e.taskItem?null:e.taskItem.task_name," "),x["\u0275\u0275advance"](9),x["\u0275\u0275property"]("ngStyle",x["\u0275\u0275pureFunction1"](28,J,null==e.taskItem?null:e.taskItem.status_bg_color)),x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate"](null==e.taskItem?null:e.taskItem.task_status_name),x["\u0275\u0275advance"](4),x["\u0275\u0275property"]("ngIf",null==e.taskItem?null:e.taskItem.assigned_to),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",!(null!=e.taskItem&&e.taskItem.assigned_to)),x["\u0275\u0275advance"](4),x["\u0275\u0275textInterpolate1"](" ",null!=e.taskItem&&e.taskItem.planned_hours?e.taskItem.planned_hours:"00"," Hours "),x["\u0275\u0275advance"](5),x["\u0275\u0275textInterpolate1"](" ",x["\u0275\u0275pipeBind2"](30,20,null==e.taskItem?null:e.taskItem.end_date,"dd-MMM-yy")," "),x["\u0275\u0275advance"](5),x["\u0275\u0275property"]("oid",null==e.taskItem?null:e.taskItem.created_by),x["\u0275\u0275advance"](9),x["\u0275\u0275textInterpolate1"](" ",e.taskItem.location?e.taskItem.location:"-"," "),x["\u0275\u0275advance"](4),x["\u0275\u0275textInterpolate1"](" ",x["\u0275\u0275pipeBind2"](48,23,null==e.taskItem?null:e.taskItem.created_at,"dd-MMM-yy")," "),x["\u0275\u0275advance"](12),x["\u0275\u0275property"]("innerHtml",x["\u0275\u0275pipeBind1"](60,26,null==e.taskItem?null:e.taskItem.task_description),x["\u0275\u0275sanitizeHtml"]),x["\u0275\u0275advance"](2),x["\u0275\u0275property"]("ngIf",!(null!=e.taskItem&&e.taskItem.task_description)),x["\u0275\u0275advance"](8),x["\u0275\u0275property"]("uploader",e.uploader)("accept",e.allowedMimeType),x["\u0275\u0275advance"](3),x["\u0275\u0275property"]("ngIf",e.taskItem.attachments&&e.taskItem.attachments.length>0),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",!(null!=e.taskItem&&e.taskItem.attachments)||0==(null==e.taskItem?null:e.taskItem.attachments.length)),x["\u0275\u0275advance"](3),x["\u0275\u0275property"]("value",e.selectedMetaToggle),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngForOf",e.metaTabs),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",2==e.selectedMetaToggle),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",1==e.selectedMetaToggle))},directives:function(){return[p.a,u.a,h.a,m.NgStyle,m.NgIf,F.a,T.a,l.b,g.b,m.NgForOf,g.a,m.NgClass,I]},pipes:function(){return[m.DatePipe,B.a,D.a]},styles:[".task-detail-styles[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.task-detail-styles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}.task-detail-styles[_ngcontent-%COMP%]   .headingBold[_ngcontent-%COMP%]{color:#cf0001;font-weight:500;font-size:14px}.task-detail-styles[_ngcontent-%COMP%]   .headerFont[_ngcontent-%COMP%]{color:#868383;font-size:14px!important;font-weight:400;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.task-detail-styles[_ngcontent-%COMP%]   .normalFont[_ngcontent-%COMP%]{font-weight:500}.task-detail-styles[_ngcontent-%COMP%]   .fileFont[_ngcontent-%COMP%], .task-detail-styles[_ngcontent-%COMP%]   .normalFont[_ngcontent-%COMP%]{color:#181818;font-size:14px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.task-detail-styles[_ngcontent-%COMP%]   .fileFont[_ngcontent-%COMP%]{font-weight:400}.task-detail-styles[_ngcontent-%COMP%]   .status-dot[_ngcontent-%COMP%]{height:13px;width:13px;border-radius:50%;display:inline-block;vertical-align:middle;background:#5f5f5f}.task-detail-styles[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]{line-height:8px;width:26px;height:26px;position:absolute;margin-right:5px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12);animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}.task-detail-styles[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.task-detail-styles[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{color:#5f5f5f;font-size:11px!important}.task-detail-styles[_ngcontent-%COMP%]   .ic-size[_ngcontent-%COMP%]{font-size:20px!important;line-height:1!important}.task-detail-styles[_ngcontent-%COMP%]   .excel[_ngcontent-%COMP%]{color:green!important}.task-detail-styles[_ngcontent-%COMP%]   .word[_ngcontent-%COMP%]{color:#2b579a!important}.task-detail-styles[_ngcontent-%COMP%]   .ppt[_ngcontent-%COMP%]{color:#d24726!important}.task-detail-styles[_ngcontent-%COMP%]   .txt[_ngcontent-%COMP%]{color:#2b579a!important}.task-detail-styles[_ngcontent-%COMP%]   .pdf[_ngcontent-%COMP%]{color:#bf040d!important}.task-detail-styles[_ngcontent-%COMP%]   .mail[_ngcontent-%COMP%]{color:#282829!important}.task-detail-styles[_ngcontent-%COMP%]   .icon-tray-button[_ngcontent-%COMP%]{width:27px!important;height:27px!important;line-height:25px!important}.task-detail-styles[_ngcontent-%COMP%]   .smallCardIcon[_ngcontent-%COMP%]{font-size:18px!important;color:#868683}.task-detail-styles[_ngcontent-%COMP%]   .toggle-btn[_ngcontent-%COMP%]{min-width:7rem}.task-detail-styles[_ngcontent-%COMP%]   .btn-toggle-selected[_ngcontent-%COMP%]{background-color:#c92020!important;color:#fff}.task-detail-styles[_ngcontent-%COMP%]   .line-height[_ngcontent-%COMP%]{height:15px;padding-left:20px}.task-detail-styles[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}.task-detail-styles[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}"]}),t})()}}]);