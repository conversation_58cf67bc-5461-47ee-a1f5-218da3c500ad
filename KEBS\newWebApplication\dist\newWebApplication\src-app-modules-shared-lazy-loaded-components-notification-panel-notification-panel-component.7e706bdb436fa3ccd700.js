(window.webpackJsonp=window.webpackJsonp||[]).push([[989,987,990,991],{"2tir":function(t,e,n){"use strict";n.r(e),n.d(e,"NotificationPanelComponent",(function(){return it}));var i=n("fXoL"),o=n("bSwM"),r=n("STbY"),a=n("NFeN"),l=n("bTqV"),s=n("Qu3c"),c=n("ofXK"),p=n("mrSG"),d=n("xG9w"),m=n("wd/R"),f=n("tk/3"),u=n("XXEo"),h=n("LcQX"),g=n("ucYs"),v=n("BVzC");let w=(()=>{class t{constructor(t,e,n,i,o){this.http=t,this.authHelper=e,this.utilityService=n,this.workflowService=i,this.errorService=o,this.currentUser={},this.ICON_MAPPINGS={2:{icon:"gavel",style:{"background-color":"#e74c3c",color:"#ffffff"}},37:{icon:"timer",style:{"background-color":"#3c6382",color:"#ffffff"}},36:{icon:"work",style:{"background-color":"#C44569",color:"#ffffff"}},83:{icon:"work",style:{"background-color":"#C44569",color:"#ffffff"}},65:{icon:"work",style:{"background-color":"#C44569",color:"#ffffff"}}},this.currentUser=this.authHelper.getProfile().profile}getStatusDescription(t){return d.findWhere(this.workflowService.workflowStatusList,{code:t}).status}borderStatusColor(t){return"S"==t?"#b7b7b7":"A"==t?"#009432":"R"==t?"#af0505":"RL"==t?"#ff7200":"white"}initWorkflowDetails(t){return Object(p.c)(this,void 0,void 0,(function*(){let e=yield this.workflowService.getWorkflowDetails(t);e=d.sortBy(e.data,t=>t.appr_level);let n={};return n.workflowHeaderId=t,n.workflowDetails=e,n.submissionDetails=JSON.parse(e[0].submission),n.overallStatus=yield this.calculateOverallStatus(e),n}))}calculateOverallStatus(t){return Object(p.c)(this,void 0,void 0,(function*(){let e=d.pluck(t,"status");return this.isApproved(e)?{statusCode:"A",status:"Approved"}:this.isRejected(e)?{statusCode:"R",status:"Rejected"}:this.isRecalled(e)?{statusCode:"RL",status:"Recalled"}:{statusCode:"S",status:"Submitted"}}))}isApproved(t){for(let e=0;e<t.length;e++){if("A"!=t[e]&&"E"!=t[e])return!1;if(e==t.length-1)return!0}}isRejected(t){return-1!=d.findIndex(t,t=>"R"==t)}isRecalled(t){return-1!=d.findIndex(t,t=>"RL"==t)}storeComments(t,e){return Object(p.c)(this,void 0,void 0,(function*(){yield this.projectStoreComments(t,[e]),yield this.workflowService.storeComments(t,[e],this.currentUser.oid)}))}projectStoreComments(t,e){return new Promise((n,i)=>{this.http.post("/api/project/storeCommentsProjectVariance",{workflowHeaderId:t,newComments:e}).subscribe(t=>n(t),t=>(console.log(t),i(t)))})}getWorkflowProperties(t){return Object(p.c)(this,void 0,void 0,(function*(){let e=yield this.workflowService.getWorkflowPropertiesByWorkflowId(t);if("S"==e.messType)return e.data[0]}))}getOpportunityDetails(t){return new Promise((e,n)=>{this.http.post("/api/notification/getOpportunityActivityDetails",{workflowHeaderId:t}).subscribe(t=>e(t),t=>(console.log(t),this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Error while retrieving Opportunity Notifications",t&&t.params?t.params:t&&t.error?t.error.params:{}),n(t)))})}updateWorkflowStatus(t,e){return Object(p.c)(this,void 0,void 0,(function*(){yield this.updateTaskChangeStatus(t,e.statusCode)}))}updateTaskChangeStatus(t,e){return new Promise((n,i)=>{this.http.post("/api/project/updateTaskChangeStatus",{workflowHeaderId:t,status:e}).subscribe(t=>n(t),t=>(console.log(t),i(t)))})}updateWFDetailsInActivity(t,e,n){return new Promise((i,o)=>{this.http.post("/api/activity/updateWFDetailsInActivity",{activityId:t,workflowHeaderId:n,workflowRecord:e}).subscribe(t=>i(t),t=>(console.log(t),o(t)))})}isApprover(t){return d.contains(d.pluck(t,"actual_appr_oid"),this.currentUser.oid)}approveChange(t){return Object(p.c)(this,void 0,void 0,(function*(){let e,n=(yield this.initWorkflowDetails(t)).workflowDetails,i=yield this.getWorkflowProperties(n[0].workflow_id);if(this.isApprover(n)&&!d.findWhere(n,{actual_appr_oid:this.currentUser.oid}).is_workflow_item_complete){let o={};36==i.application_id?(e=yield this.getOpportunityDetails(t),console.log(e),o={userOId:this.currentUser.oid,workflowHeaderId:t,aggregationFlag:i.aggregation_allowed,status:"A",comments:"Activity approved by "+this.currentUser.name+" on "+m().format("DD-MM-YY hh:mm a")}):o={userOId:this.currentUser.oid,workflowHeaderId:t,aggregationFlag:i.aggregation_allowed,status:"A",comments:[]};let r=yield this.workflowService.updateWorkflowItems(o),a=d.findIndex(n,t=>t.actual_appr_oid==this.currentUser.oid);a>=0&&(n[a].status="A");let l=yield this.calculateOverallStatus(n),s={event:"statusChange",data:{workflowHeaderId:t,status:l}};if(2==i.application_id&&this.updateWorkflowStatus(t,l),"S"==r.messType){if(36==i.application_id){let n=JSON.parse(e[0].workflow_log);console.log(n);let i={comment:"Activity approved by "+this.currentUser.name+" on "+m().format("DD-MM-YY hh:mm a"),approveParams:JSON.stringify(o),approvers:[]};n.push(i),this.updateWFDetailsInActivity(e[0].activity_id,n,t),"A"==l.status&&this.completeActivity(e[0].activity_id,1).then(t=>{},t=>{console.log(t),this.utilityService.showMessage("Failed to update in Activities. Contact KEBS Team","Dismiss")})}return n[d.findIndex(n,t=>t.actual_appr_oid==this.currentUser.oid)].is_workflow_item_complete=1,this.utilityService.showMessage("You have successfully approved of this Change","Dismiss"),{workflowHeaderId:t,workflowDetails:n,overallStatus:l,submitData:s}}this.utilityService.showMessage("Sorry, cannot approve the change. Please try again","Dismiss")}else this.utilityService.showMessage("Sorry, you are not an approver for this change approval or you have already approved this","Dismiss")}))}completeActivity(t,e){return new Promise((n,i)=>{this.http.post("/api/activity/completeActivity",{activity_id:t,is_completed:e}).subscribe(t=>n(t),t=>i(t))})}rejectChange(t){return Object(p.c)(this,void 0,void 0,(function*(){let e,n=(yield this.initWorkflowDetails(t)).workflowDetails,i=yield this.getWorkflowProperties(n[0].workflow_id);if(this.isApprover(n)&&!d.findWhere(n,{actual_appr_oid:this.currentUser.oid}).is_workflow_item_complete){let o={};36==i.application_id?(e=yield this.getOpportunityDetails(t),console.log(e),o={userOId:this.currentUser.oid,workflowHeaderId:t,aggregationFlag:i.aggregation_allowed,status:"R",comments:"Activity rejected by "+this.currentUser.name+" on "+m().format("DD-MM-YY hh:mm a")}):o={userOId:this.currentUser.oid,workflowHeaderId:t,aggregationFlag:i.aggregation_allowed,status:"R",comments:this.newComments};let r=yield this.workflowService.updateWorkflowItems(o),a=d.findIndex(n,t=>t.actual_appr_oid==this.currentUser.oid);a>=0&&(n[a].status="R");let l=yield this.calculateOverallStatus(n),s={event:"statusChange",data:{workflowHeaderId:t,status:l}};if(2==i.application_id&&this.updateWorkflowStatus(t,l),"S"==r.messType){if(n[d.findIndex(n,t=>t.actual_appr_oid==this.currentUser.oid)].is_workflow_item_complete=1,36==i.application_id){let t=JSON.parse(e[0].workflow_log);console.log(t);let n={comment:"Activity rejected by "+this.currentUser.name+" on "+m().format("DD-MM-YY hh:mm a"),approveParams:JSON.stringify(o),approvers:[]};t.push(n),this.updateWFDetailsInActivity(e[0].activity_id,t,null),this.openActivity(e[0].activity_id).then(t=>{console.log(t)},t=>{console.log(t),this.utilityService.showMessage("Failed to update in Activities. Kindly contact KEBS Team","Dismiss")})}return this.utilityService.showMessage("You have successfully rejected this Change","Dismiss"),{workflowHeaderId:t,workflowDetails:n,overallStatus:l,submitData:s}}this.utilityService.showMessage("Sorry, cannot reject the change. Please try again","Dismiss")}else this.utilityService.showMessage("Sorry, you are not an approver for this change approval","Dismiss")}))}openActivity(t){return new Promise((e,n)=>{this.http.post("/api/activity/openActivity",{activity_id:t}).subscribe(t=>e(t),t=>n(t))})}getWorkflowNotifications(){return Object(p.c)(this,void 0,void 0,(function*(){return new Promise((t,e)=>{this.http.post("/api/notification/getWorkflowNotifications",{userOid:this.currentUser.oid}).subscribe(e=>t(e),t=>(console.log(t),this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Error while retrieving Notifications",t&&t.params?t.params:t&&t.error?t.error.params:{}),e(t)))})}))}notificationsFormatter(t){return Object(p.c)(this,void 0,void 0,(function*(){let e=d.groupBy(t,t=>t.change_status),n=[],i=d.keys(e);return console.log(i),t=d.filter(t,t=>"created"!=t.change_status||0==t.is_workflow_complete&&1==t.is_curr_appr),n=d.sortBy(t,t=>t.changed_on?m(t.changed_on).toDate():m(t.created_on).toDate()),n.reverse()}))}getErrorDetails(t){return new Promise((e,n)=>{this.http.post("/api/error/getErrorDetails",{errorCode:t}).subscribe(t=>e(t),t=>n(t))})}}return t.\u0275fac=function(e){return new(e||t)(i["\u0275\u0275inject"](f.c),i["\u0275\u0275inject"](u.a),i["\u0275\u0275inject"](h.a),i["\u0275\u0275inject"](g.a),i["\u0275\u0275inject"](v.a))},t.\u0275prov=i["\u0275\u0275defineInjectable"]({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();var S=n("f0Cb"),x=n("3Pt+");function y(t,e){if(1&t){const t=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",22),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](t),i["\u0275\u0275nextContext"](2).openApprovalDialogSheet()})),i["\u0275\u0275elementStart"](1,"mat-icon",9),i["\u0275\u0275text"](2,"arrow_forward"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}}function b(t,e){if(1&t){const t=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",23),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](t),i["\u0275\u0275nextContext"](2).closeApprovalDialogSheet()})),i["\u0275\u0275elementStart"](1,"mat-icon",9),i["\u0275\u0275text"](2,"arrow_back"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}}const C=function(t){return{"background-color":t}};function E(t,e){if(1&t){const t=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",1),i["\u0275\u0275elementStart"](1,"div",2),i["\u0275\u0275elementStart"](2,"div",3),i["\u0275\u0275elementStart"](3,"mat-icon",4),i["\u0275\u0275text"](4),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"div",5),i["\u0275\u0275elementStart"](6,"span",6),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](t),i["\u0275\u0275nextContext"]().openApprovalDialogSheet()})),i["\u0275\u0275text"](7),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](8,"div",7),i["\u0275\u0275elementStart"](9,"button",8),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](t),i["\u0275\u0275nextContext"]().openInNewTab()})),i["\u0275\u0275elementStart"](10,"mat-icon",9),i["\u0275\u0275text"](11,"open_in_new"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](12,y,3,0,"button",10),i["\u0275\u0275template"](13,b,3,0,"button",11),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](14,"div",12),i["\u0275\u0275elementStart"](15,"div",13),i["\u0275\u0275elementStart"](16,"mat-checkbox",14),i["\u0275\u0275listener"]("change",(function(e){return i["\u0275\u0275restoreView"](t),i["\u0275\u0275nextContext"]().handleSelection(e)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](17,"div",15),i["\u0275\u0275elementStart"](18,"button",16),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](t),i["\u0275\u0275nextContext"]().approveChange()})),i["\u0275\u0275elementStart"](19,"mat-icon",17),i["\u0275\u0275text"](20,"done"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](21,"button",18),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](t),i["\u0275\u0275nextContext"]().rejectChange()})),i["\u0275\u0275elementStart"](22,"mat-icon",19),i["\u0275\u0275text"](23,"highlight_off"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](24,"div",20),i["\u0275\u0275elementStart"](25,"div",15),i["\u0275\u0275elementStart"](26,"span",21),i["\u0275\u0275text"](27),i["\u0275\u0275pipe"](28,"date"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&t){const t=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngStyle",t.ICON_MAPPINGS[t.inputNotif.application_id].style),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" ",t.ICON_MAPPINGS[t.inputNotif.application_id].icon,""),i["\u0275\u0275advance"](3),i["\u0275\u0275textInterpolate1"](" ",t.inputNotif.notif_text," "),i["\u0275\u0275advance"](5),i["\u0275\u0275property"]("ngIf",!t.isApprovalDialogOpen),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.isApprovalDialogOpen),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("formControl",t.selectControl),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("disabled",!t.isapprovalButtonsEnabled)("ngStyle",i["\u0275\u0275pureFunction1"](14,C,t.isapprovalButtonsEnabled?"":"#55555550")),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("disabled",!t.isapprovalButtonsEnabled)("ngStyle",i["\u0275\u0275pureFunction1"](16,C,t.isapprovalButtonsEnabled?"":"#55555550")),i["\u0275\u0275advance"](6),i["\u0275\u0275textInterpolate"](i["\u0275\u0275pipeBind2"](28,11,t.inputNotif.changed_on,"MMMM dd hh:mm aa"))}}function _(t,e){1&t&&(i["\u0275\u0275elementStart"](0,"mat-icon",28),i["\u0275\u0275text"](1,"done"),i["\u0275\u0275elementEnd"]())}function k(t,e){1&t&&(i["\u0275\u0275elementStart"](0,"mat-icon",29),i["\u0275\u0275text"](1,"highlight_off"),i["\u0275\u0275elementEnd"]())}const O=function(t){return{color:t}};function A(t,e){if(1&t){const t=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",1),i["\u0275\u0275elementStart"](1,"div",2),i["\u0275\u0275elementStart"](2,"div",3),i["\u0275\u0275elementStart"](3,"mat-icon",4),i["\u0275\u0275text"](4),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"div",5),i["\u0275\u0275elementStart"](6,"span",6),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](t),i["\u0275\u0275nextContext"]().openApprovalDialogSheet()})),i["\u0275\u0275text"](7),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](8,"div",7),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](9,"div",12),i["\u0275\u0275element"](10,"div",13),i["\u0275\u0275elementStart"](11,"div",24),i["\u0275\u0275template"](12,_,2,0,"mat-icon",25),i["\u0275\u0275template"](13,k,2,0,"mat-icon",26),i["\u0275\u0275elementStart"](14,"div",27),i["\u0275\u0275text"](15),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](16,"div",15),i["\u0275\u0275elementStart"](17,"span",21),i["\u0275\u0275text"](18),i["\u0275\u0275pipe"](19,"date"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&t){const t=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngStyle",t.ICON_MAPPINGS[t.inputNotif.application_id].style),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" ",t.ICON_MAPPINGS[t.inputNotif.application_id].icon,""),i["\u0275\u0275advance"](3),i["\u0275\u0275textInterpolate1"](" ",t.inputNotif.notif_text," "),i["\u0275\u0275advance"](5),i["\u0275\u0275property"]("ngIf","A"==t.inputNotif.status),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf","R"==t.inputNotif.status),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngStyle",i["\u0275\u0275pureFunction1"](11,O,t.notificationService.borderStatusColor(t.inputNotif.status))),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" ",t.notificationService.getStatusDescription(t.inputNotif.status)," ! "),i["\u0275\u0275advance"](3),i["\u0275\u0275textInterpolate"](i["\u0275\u0275pipeBind2"](19,8,t.inputNotif.changed_on,"MMMM dd hh:mm aa"))}}let I=(()=>{class t{constructor(t,e){this.notificationService=t,this.utilityService=e,this.inputNotif={},this.openApprovalSheet=new i.EventEmitter,this.closeApprovalSheet=new i.EventEmitter,this.refreshList=new i.EventEmitter,this.sendSelectionChange=new i.EventEmitter,this.submissionDetails={},this.isapprovalButtonsEnabled=!0,this.isApprovalDialogOpen=!1,this.selectControl=new x.j}ngOnInit(){this.submissionDetails=JSON.parse(this.inputNotif.submission),this.ICON_MAPPINGS=this.notificationService.ICON_MAPPINGS}toggleApprovalDialogSheet(){this.isApprovalDialogOpen=!this.isApprovalDialogOpen,this.openApprovalSheet.emit(this.inputNotif)}openApprovalDialogSheet(){this.openApprovalSheet.emit(this.inputNotif),this.isApprovalDialogOpen=!0}closeApprovalDialogSheet(){this.isApprovalDialogOpen=!1,this.closeApprovalSheet.emit(this.inputNotif)}approveChange(){return Object(p.c)(this,void 0,void 0,(function*(){this.disableApprovalButtons(),yield this.notificationService.approveChange(this.inputNotif.workflow_header_id),this.refreshList.emit()}))}rejectChange(){return Object(p.c)(this,void 0,void 0,(function*(){this.disableApprovalButtons(),yield this.notificationService.rejectChange(this.inputNotif.workflow_header_id),this.refreshList.emit()}))}disableApprovalButtons(){this.isapprovalButtonsEnabled=!1}handleSelection(t){let e={workflowHeaderId:this.inputNotif.workflow_header_id,isSelected:t.checked};this.isapprovalButtonsEnabled=!t.checked,this.sendSelectionChange.emit(e)}selectAllTriggered(){"created"==this.inputNotif.change_status&&(this.selectControl.setValue(!0),this.handleSelection({checked:!0}))}deSelectAllTriggered(){"created"==this.inputNotif.change_status&&(this.selectControl.setValue(!1),this.handleSelection({checked:!1}))}openInNewTab(){return Object(p.c)(this,void 0,void 0,(function*(){this.inputNotif.notif_link?window.open(this.inputNotif.notif_link+""):this.utilityService.showMessage("Sorry, cannot open notification. No Notification link attached.","Dismiss")}))}}return t.\u0275fac=function(e){return new(e||t)(i["\u0275\u0275directiveInject"](w),i["\u0275\u0275directiveInject"](h.a))},t.\u0275cmp=i["\u0275\u0275defineComponent"]({type:t,selectors:[["app-notification-card"]],inputs:{inputNotif:["inputData","inputNotif"],isSelectAllTriggered:"isSelectAllTriggered"},outputs:{openApprovalSheet:"openApprovalSheet",closeApprovalSheet:"closeApprovalSheet",refreshList:"refreshList",sendSelectionChange:"sendSelectionChange"},decls:2,vars:2,consts:[["class","container-fluid card-main p-0 mt-1 mb-1 w-100",4,"ngIf"],[1,"container-fluid","card-main","p-0","mt-1","mb-1","w-100"],[1,"row","mb-2"],[1,"col-12","col-lg-1","my-auto","pr-0","pl-2"],[1,"main-icon",3,"ngStyle"],[1,"col-12","col-lg-8"],[1,"description",3,"click"],[1,"col-12","col-lg-3","d-flex","flex-row"],["mat-icon-button","","matTooltip","Open in New Tab",1,"ml-auto",3,"click"],[1,"smallCardIcon"],["mat-icon-button","","matTooltip","More Info","class","ml-auto",3,"click",4,"ngIf"],["mat-icon-button","","matTooltip","Hide More Info","class","ml-auto",3,"click",4,"ngIf"],[1,"row"],[1,"col-12","col-lg-1","pr-0","d-flex"],["matTooltip","Select",1,"mt-1",3,"formControl","change"],[1,"col-12","col-lg-4","d-flex","flex-row"],["mat-icon-button","","matTooltip","Approve",1,"approve-btn","mr-3",3,"disabled","ngStyle","click"],[2,"color","white !important","font-size","18px !important"],["mat-icon-button","","matTooltip","Reject",1,"reject-btn",3,"disabled","ngStyle","click"],[2,"color","white !important","font-size","21px !important","margin-bottom","1px"],[1,"col-12","col-lg-3"],[1,"ml-auto","date-text","my-auto"],["mat-icon-button","","matTooltip","More Info",1,"ml-auto",3,"click"],["mat-icon-button","","matTooltip","Hide More Info",1,"ml-auto",3,"click"],[1,"col-12","col-lg-7","d-flex","flex-row"],["style","color: #009432 !important; font-size: 13px !important",4,"ngIf"],["style","color: #af0505 !important; font-size: 13px !important",4,"ngIf"],[1,"status-description",3,"ngStyle"],[2,"color","#009432 !important","font-size","13px !important"],[2,"color","#af0505 !important","font-size","13px !important"]],template:function(t,e){1&t&&(i["\u0275\u0275template"](0,E,29,18,"div",0),i["\u0275\u0275template"](1,A,20,13,"div",0)),2&t&&(i["\u0275\u0275property"]("ngIf","created"==e.inputNotif.change_status),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf","updated"==e.inputNotif.change_status))},directives:[c.NgIf,a.a,c.NgStyle,l.a,s.a,o.a],pipes:[c.DatePipe],styles:[".card-main[_ngcontent-%COMP%]{width:100%}.card-main[_ngcontent-%COMP%]   .main-icon[_ngcontent-%COMP%]{width:27px;height:27px;font-size:17px;border-radius:50%;line-height:26px;text-align:center;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 3px 0 rgba(0,0,0,.14),0 2px 14px 0 rgba(0,0,0,.12)}.card-main[_ngcontent-%COMP%]   .status-description[_ngcontent-%COMP%]{font-size:12px}.card-main[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%]{font-size:13px!important;color:#cf0001;font-weight:500}.card-main[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%]:hover{cursor:pointer}.card-main[_ngcontent-%COMP%]   .tooltip-description[_ngcontent-%COMP%]{font-size:12px}.card-main[_ngcontent-%COMP%]   .date-text[_ngcontent-%COMP%]{font-size:12px!important;color:#3e3e3e}.card-main[_ngcontent-%COMP%]   .smallCardIcon[_ngcontent-%COMP%]{font-size:18px!important;color:#66615b}.card-main[_ngcontent-%COMP%]   .approve-btn[_ngcontent-%COMP%]{background-color:#64d464;width:27px;height:27px;line-height:25px}.card-main[_ngcontent-%COMP%]   .approve-btn[_ngcontent-%COMP%], .card-main[_ngcontent-%COMP%]   .reject-btn[_ngcontent-%COMP%]{font-size:18px;color:#fff;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 3px 0 rgba(0,0,0,.14),0 2px 14px 0 rgba(0,0,0,.12)}.card-main[_ngcontent-%COMP%]   .reject-btn[_ngcontent-%COMP%]{background-color:#fc3939;width:28px;height:28px;line-height:27px}"]}),t})();const P=function(t){return{color:t}};function M(t,e){if(1&t&&(i["\u0275\u0275elementStart"](0,"div",48),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](2,"div",48),i["\u0275\u0275text"](3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"div",48),i["\u0275\u0275text"](5),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](6,"div",49),i["\u0275\u0275elementStart"](7,"div",50),i["\u0275\u0275text"](8),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](9,"mat-icon",51),i["\u0275\u0275text"](10,"fiber_manual_record"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&t){const t=i["\u0275\u0275nextContext"]().$implicit,e=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](t.employee_name),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate"](t.appr_desgn),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"]("Level ",t.appr_level,""),i["\u0275\u0275advance"](3),i["\u0275\u0275textInterpolate"](e.getStatusDescription(t.status)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngStyle",i["\u0275\u0275pureFunction1"](5,P,e.borderStatusColor(t.status)))}}function D(t,e){if(1&t&&(i["\u0275\u0275elementStart"](0,"div",44),i["\u0275\u0275element"](1,"app-user-image",45),i["\u0275\u0275template"](2,M,11,7,"ng-template",46,47,i["\u0275\u0275templateRefExtractor"]),i["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit,n=i["\u0275\u0275reference"](3),o=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("tooltip",n)("id",t.actual_appr_oid)("borderColor",o.borderStatusColor(t.status))}}function N(t,e){if(1&t&&(i["\u0275\u0275elementStart"](0,"span",33),i["\u0275\u0275text"](1),i["\u0275\u0275pipe"](2,"date"),i["\u0275\u0275elementEnd"]()),2&t){const t=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" ",i["\u0275\u0275pipeBind2"](2,1,t.getActionOnDate(),"dd-MM-yyyy")," ")}}function j(t,e){1&t&&(i["\u0275\u0275elementStart"](0,"span",33),i["\u0275\u0275text"](1," --/-- "),i["\u0275\u0275elementEnd"]())}function T(t,e){if(1&t&&(i["\u0275\u0275elementStart"](0,"span",33),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&t){const t=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" ",t.submissionDetails.projectName," ")}}function R(t,e){1&t&&(i["\u0275\u0275elementStart"](0,"span",33),i["\u0275\u0275text"](1," --/-- "),i["\u0275\u0275elementEnd"]())}const W=function(t){return{"background-color":t}};function L(t,e){if(1&t){const t=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",52),i["\u0275\u0275elementStart"](1,"button",53),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](t),i["\u0275\u0275nextContext"](2).approveChange()})),i["\u0275\u0275elementStart"](2,"mat-icon",54),i["\u0275\u0275text"](3,"done"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"button",55),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](t),i["\u0275\u0275nextContext"](2).rejectChange()})),i["\u0275\u0275elementStart"](5,"mat-icon",56),i["\u0275\u0275text"](6," highlight_off"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&t){const t=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("disabled",!t.canApprove()&&t.isapprovalButtonsEnabled)("ngStyle",i["\u0275\u0275pureFunction1"](4,W,t.canApprove()&&t.isapprovalButtonsEnabled?"":"#55555550")),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("disabled",!t.canApprove()&&t.isapprovalButtonsEnabled)("ngStyle",i["\u0275\u0275pureFunction1"](6,W,t.canApprove()&&t.isapprovalButtonsEnabled?"":"#55555550"))}}function H(t,e){1&t&&i["\u0275\u0275element"](0,"div",57)}function V(t,e){1&t&&i["\u0275\u0275element"](0,"div",57)}function B(t,e){if(1&t){const t=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",1),i["\u0275\u0275elementStart"](1,"div",2),i["\u0275\u0275elementStart"](2,"div",3),i["\u0275\u0275elementStart"](3,"span",4),i["\u0275\u0275text"](4),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"div",5),i["\u0275\u0275elementStart"](6,"div",6),i["\u0275\u0275elementStart"](7,"span",7),i["\u0275\u0275text"](8,"Approvers "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](9,"div",8),i["\u0275\u0275template"](10,D,4,3,"div",9),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](11,"div",10),i["\u0275\u0275elementStart"](12,"div",11),i["\u0275\u0275elementStart"](13,"div",12),i["\u0275\u0275elementStart"](14,"div",13),i["\u0275\u0275elementStart"](15,"span",14),i["\u0275\u0275text"](16,"Approval"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](17,"div",15),i["\u0275\u0275elementStart"](18,"span",16),i["\u0275\u0275element"](19,"div",17),i["\u0275\u0275elementStart"](20,"div",18),i["\u0275\u0275text"](21),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](22,"div",19),i["\u0275\u0275elementStart"](23,"div",12),i["\u0275\u0275elementStart"](24,"div",20),i["\u0275\u0275elementStart"](25,"span",21),i["\u0275\u0275text"](26,"Created By"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](27,"div",22),i["\u0275\u0275elementStart"](28,"div",23),i["\u0275\u0275elementStart"](29,"div",24),i["\u0275\u0275elementStart"](30,"div",25),i["\u0275\u0275text"](31),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](32,"div",26),i["\u0275\u0275element"](33,"app-user-image",27),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](34,"div",10),i["\u0275\u0275elementStart"](35,"div",28),i["\u0275\u0275elementStart"](36,"div",29),i["\u0275\u0275elementStart"](37,"div",13),i["\u0275\u0275elementStart"](38,"span",21),i["\u0275\u0275text"](39," Approved/Rejected on "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](40,"div",15),i["\u0275\u0275template"](41,N,3,4,"span",30),i["\u0275\u0275template"](42,j,2,0,"span",30),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](43,"div",31),i["\u0275\u0275elementStart"](44,"span",32),i["\u0275\u0275text"](45," Created On "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](46,"span",33),i["\u0275\u0275text"](47),i["\u0275\u0275pipe"](48,"date"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](49,"div",34),i["\u0275\u0275elementStart"](50,"div",28),i["\u0275\u0275elementStart"](51,"div",29),i["\u0275\u0275elementStart"](52,"div",13),i["\u0275\u0275elementStart"](53,"span",35),i["\u0275\u0275text"](54," Project "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](55,"div",15),i["\u0275\u0275template"](56,T,2,1,"span",30),i["\u0275\u0275template"](57,R,2,0,"span",30),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](58,"div",31),i["\u0275\u0275elementStart"](59,"span",32),i["\u0275\u0275text"](60," Milestone "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](61,"span",33),i["\u0275\u0275text"](62),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](63,"div",36),i["\u0275\u0275template"](64,L,7,8,"div",37),i["\u0275\u0275elementStart"](65,"div",38),i["\u0275\u0275elementStart"](66,"div",8),i["\u0275\u0275elementStart"](67,"button",39),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](t),i["\u0275\u0275nextContext"]().toggleComments()})),i["\u0275\u0275elementStart"](68,"mat-icon",40),i["\u0275\u0275text"](69,"forum "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](70,"button",41),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](t),i["\u0275\u0275nextContext"]().toggleHistory()})),i["\u0275\u0275elementStart"](71,"mat-icon",40),i["\u0275\u0275text"](72,"history "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](73,"div",42),i["\u0275\u0275template"](74,H,1,0,"div",43),i["\u0275\u0275template"](75,V,1,0,"div",43),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&t){const t=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("matTooltip",t.submissionDetails.changeDescription),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"](" ",t.submissionDetails.changeDescription,""),i["\u0275\u0275advance"](6),i["\u0275\u0275property"]("ngForOf",t.data.workflowDetails),i["\u0275\u0275advance"](9),i["\u0275\u0275property"]("ngStyle",i["\u0275\u0275pureFunction1"](25,W,t.borderStatusColor(t.data.overallStatus.statusCode))),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate"](t.getStatusDescription(t.data.overallStatus.statusCode)),i["\u0275\u0275advance"](10),i["\u0275\u0275textInterpolate1"](" ",t.submissionDetails.changedByName," "),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("id",t.submissionDetails.changedBy)("imgWidth","30px")("imgHeight","30px"),i["\u0275\u0275advance"](8),i["\u0275\u0275property"]("ngIf",t.isApprover()),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",!t.getActionOnDate()||!t.isApprover()),i["\u0275\u0275advance"](5),i["\u0275\u0275textInterpolate1"](" ",i["\u0275\u0275pipeBind2"](48,22,t.submissionDetails.changedOn,"dd-MM-yyyy")," "),i["\u0275\u0275advance"](9),i["\u0275\u0275property"]("ngIf",t.isApprover()),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",!t.submissionDetails.projectName||!t.isApprover()),i["\u0275\u0275advance"](5),i["\u0275\u0275textInterpolate1"](" ",t.submissionDetails.milestoneName," "),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf",t.data&&t.data.workflowDetails&&t.data.workflowDetails.length>0),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngStyle",i["\u0275\u0275pureFunction1"](27,W,t.isShowComments?"#c92020":"")),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngStyle",i["\u0275\u0275pureFunction1"](29,P,t.isShowComments?"white":"#66615b")),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngStyle",i["\u0275\u0275pureFunction1"](31,W,t.isShowHistory?"#c92020":"")),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngStyle",i["\u0275\u0275pureFunction1"](33,P,t.isShowHistory?"white":"#66615b")),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngIf",t.isShowComments),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.isShowHistory)}}let F=(()=>{class t{constructor(t,e,n){this.workflowService=t,this.utilityService=e,this.notificationService=n,this.refreshList=new i.EventEmitter,this.isapprovalButtonsEnabled=!0,this.newComments=[],this.workflowProperties={},this.submitData=[],this.isShowComments=!0,this.isShowHistory=!1}ngOnInit(){}ngOnChanges(){return Object(p.c)(this,void 0,void 0,(function*(){this.initWorkflowDetails()}))}initWorkflowDetails(){return Object(p.c)(this,void 0,void 0,(function*(){let t=yield this.notificationService.initWorkflowDetails(this.data.workflowHeaderId);this.data.workflowDetails=t.workflowDetails,this.submissionDetails=t.submissionDetails,this.data.overallStatus=yield this.notificationService.calculateOverallStatus(t.workflowDetails)}))}getStatusDescription(t){if(t)return d.findWhere(this.workflowService.workflowStatusList,{code:t}).status}borderStatusColor(t){return"S"==t?"#b7b7b7":"A"==t?"#009432":"R"==t?"#af0505":"RL"==t?"#ff7200":"white"}saveComments(t){this.newComments.push(t),this.notificationService.storeComments(this.data.workflowHeaderId,t)}toggleComments(){this.isShowHistory=!1,this.isShowComments=!this.isShowComments}toggleHistory(){this.isShowComments=!1,this.isShowHistory=!this.isShowHistory}approveChange(){return Object(p.c)(this,void 0,void 0,(function*(){this.disableApprovalButtons(),yield this.notificationService.approveChange(this.data.workflowHeaderId),this.refreshList.emit()}))}disableApprovalButtons(){this.isapprovalButtonsEnabled=!1}isApprover(){return d.contains(d.pluck(this.data.workflowDetails,"actual_appr_oid"),this.notificationService.currentUser.oid)}getActionOnDate(){return d.findWhere(this.data.workflowDetails,{actual_appr_oid:this.notificationService.currentUser.oid}).action_on}canApprove(){return this.isApprover()&&!d.findWhere(this.data.workflowDetails,{actual_appr_oid:this.notificationService.currentUser.oid}).is_workflow_item_complete&&d.findWhere(this.data.workflowDetails,{actual_appr_oid:this.notificationService.currentUser.oid}).is_curr_appr}rejectChange(){return Object(p.c)(this,void 0,void 0,(function*(){this.disableApprovalButtons(),yield this.notificationService.rejectChange(this.data.workflowHeaderId),this.refreshList.emit()}))}}return t.\u0275fac=function(e){return new(e||t)(i["\u0275\u0275directiveInject"](g.a),i["\u0275\u0275directiveInject"](h.a),i["\u0275\u0275directiveInject"](w))},t.\u0275cmp=i["\u0275\u0275defineComponent"]({type:t,selectors:[["app-notification-approval"]],inputs:{data:["inputData","data"]},outputs:{refreshList:"refreshList"},features:[i["\u0275\u0275NgOnChangesFeature"]],decls:1,vars:1,consts:[["class","container-fluid notif-approval-main pl-0 pr-0  pt-1 ",4,"ngIf"],[1,"container-fluid","notif-approval-main","pl-0","pr-0","pt-1"],[1,"row","mt-2"],[1,"col-12","col-lg-6","d-flex","pl-2","pr-1","mb-0",3,"matTooltip"],[1,"my-auto","card-title"],[1,"col-12","col-lg-6","pr-0","d-flex","flex-row"],[1,"card-input-title","pt-2","d-flex","mr-3"],[1,"my-auto"],[1,"d-flex","flex-row"],["class","p-0 my-auto",4,"ngFor","ngForOf"],[1,"row","mt-3"],[1,"col-12","col-lg-6","pl-2","pr-0","cold-flex","flex-row"],[1,"row"],[1,"col-5","pl-0","d-flex"],[1,"my-auto","card-input-title","mr-3"],[1,"col-7","pl-0","d-flex"],[1,"pl-1","d-flex","my-auto","flex-row"],[1,"status-circular","mr-2",3,"ngStyle"],[1,"pb-1","description-text"],[1,"col-12","col-lg-6","pr-0"],[1,"col-3","pl-0","pr-0","d-flex"],[1,"card-input-title","my-auto"],[1,"col-8"],[1,"row","d-flex","flex-row"],[1,"pr-0","my-auto"],[1,"mr-2","pt-1","description-text"],[1,"ml-2"],[3,"id","imgWidth","imgHeight"],[1,"col-12","col-lg-6","d-flex","flex-row","pl-2"],[1,"row","w-100"],["class","description-text my-auto",4,"ngIf"],[1,"col-12","col-lg-6","d-flex","flex-row"],[1,"mr-3","card-input-title","my-auto"],[1,"description-text","my-auto"],[1,"row","mt-4"],[1,"card-input-title","mr-3","my-auto"],[1,"row","mt-3","pb-1","border-bottom-style"],["class","col-12 col-lg-3 pl-2 d-flex flex-row",4,"ngIf"],[1,"col-12","col-lg-8","pl-1","pr-1"],["mat-icon-button","","matTooltip","View Comments",1,"m-1","ml-auto","icon-buttons",3,"ngStyle","click"],[2,"font-size","15px !important",3,"ngStyle"],["mat-icon-button","","matTooltip","View Change History",1,"m-1","icon-buttons",3,"ngStyle","click"],[1,"row","my-1"],["class","col-12 col-lg-12 pl-0 pr-1",4,"ngIf"],[1,"p-0","my-auto"],["content-type","template","placement","top","imgHeight","35px","imgWidth","35px","borderStyle","solid","borderWidth","2px",2,"margin","2px",3,"tooltip","id","borderColor"],["placement","top"],["approverTooltip",""],[1,"row","tooltip-text"],[1,"d-flex","flex-row","mx-auto"],[1,"pb-1","row","tooltip-text"],[1,"tooltip-status-indicator","p-0","mt-0","mb-0","ml-2",3,"ngStyle"],[1,"col-12","col-lg-3","pl-2","d-flex","flex-row"],["mat-icon-button","","matTooltip","Approve",1,"approve-btn","mr-3",3,"disabled","ngStyle","click"],[1,"mb-2",2,"color","white !important","font-size","20px !important"],["mat-icon-button","","matTooltip","Reject",1,"reject-btn",3,"disabled","ngStyle","click"],[2,"color","white !important","font-size","21px !important","margin-bottom","12px !important"],[1,"col-12","col-lg-12","pl-0","pr-1"]],template:function(t,e){1&t&&i["\u0275\u0275template"](0,B,76,35,"div",0),2&t&&i["\u0275\u0275property"]("ngIf",e.data.workflowDetails&&e.data.workflowDetails.length>0)},directives:[c.NgIf,s.a,c.NgForOf,c.NgStyle,l.a,a.a],pipes:[c.DatePipe],styles:['.notif-approval-main[_ngcontent-%COMP%]   .card-status-indicator[_ngcontent-%COMP%]{font-size:18px;margin-top:9px}.notif-approval-main[_ngcontent-%COMP%]   .tooltip-text[_ngcontent-%COMP%]{font-size:12px;font-family:Roboto,Helvetica Neue,"sans-serif"!important}.notif-approval-main[_ngcontent-%COMP%]   .tooltip-status-indicator[_ngcontent-%COMP%]{font-size:12px;margin-top:9px}.notif-approval-main[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#af0505;text-overflow:ellipsis;overflow:hidden;white-space:nowrap}.notif-approval-main[_ngcontent-%COMP%]   .card-input-title[_ngcontent-%COMP%]{color:#7b7b7a;font-size:12px}.notif-approval-main[_ngcontent-%COMP%]   .approve-btn[_ngcontent-%COMP%]{background-color:#64d464;line-height:37px}.notif-approval-main[_ngcontent-%COMP%]   .approve-btn[_ngcontent-%COMP%], .notif-approval-main[_ngcontent-%COMP%]   .reject-btn[_ngcontent-%COMP%]{font-size:18px;color:#fff;width:30px;height:30px;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 3px 0 rgba(0,0,0,.14),0 2px 14px 0 rgba(0,0,0,.12)}.notif-approval-main[_ngcontent-%COMP%]   .reject-btn[_ngcontent-%COMP%]{background-color:#fc3939;line-height:40px}.notif-approval-main[_ngcontent-%COMP%]   .reject-btn[_ngcontent-%COMP%]   [_ngcontent-%COMP%]:disabled{background-color:#555}.notif-approval-main[_ngcontent-%COMP%]   .description-text[_ngcontent-%COMP%]{font-size:13px}.notif-approval-main[_ngcontent-%COMP%]   .status-circular[_ngcontent-%COMP%]{height:14px;width:14px;margin-top:2px;border-radius:50%;box-shadow:0 2px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.notif-approval-main[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:33px!important;height:33px!important;line-height:33px!important}.notif-approval-main[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%]{font-size:21px!important;color:#66615b!important}.notif-approval-main[_ngcontent-%COMP%]   .icon-buttons[_ngcontent-%COMP%]{font-size:18px;width:30px;height:30px;line-height:30px}.notif-approval-main[_ngcontent-%COMP%]   .border-bottom-style[_ngcontent-%COMP%]{border-bottom:1px solid #d8d8d8}']}),t})();const z=["notifCard"];function G(t,e){if(1&t){const t=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div"),i["\u0275\u0275elementStart"](1,"button",25),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](t),i["\u0275\u0275nextContext"]().approveChangeMultiple()})),i["\u0275\u0275elementStart"](2,"mat-icon",26),i["\u0275\u0275text"](3,"done"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"button",27),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](t),i["\u0275\u0275nextContext"]().rejectChangeMultiple()})),i["\u0275\u0275elementStart"](5,"mat-icon",28),i["\u0275\u0275text"](6,"highlight_off"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&t){const t=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("disabled",t.isapprovalButtonsEnabled),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("disabled",t.isapprovalButtonsEnabled)}}function U(t,e){1&t&&i["\u0275\u0275element"](0,"mat-divider",29)}function Y(t,e){1&t&&i["\u0275\u0275element"](0,"mat-progress-bar",30)}function J(t,e){if(1&t){const t=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div"),i["\u0275\u0275elementStart"](1,"app-notification-card",33,34),i["\u0275\u0275listener"]("sendSelectionChange",(function(e){return i["\u0275\u0275restoreView"](t),i["\u0275\u0275nextContext"](2).handleCardSelection(e)}))("openApprovalSheet",(function(e){return i["\u0275\u0275restoreView"](t),i["\u0275\u0275nextContext"](2).openApprovalSheet(e)}))("closeApprovalSheet",(function(){return i["\u0275\u0275restoreView"](t),i["\u0275\u0275nextContext"](2).closeApprovalSheet()}))("refreshList",(function(){return i["\u0275\u0275restoreView"](t),i["\u0275\u0275nextContext"](2).refreshData()})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](3,"mat-divider",35),i["\u0275\u0275elementEnd"]()}if(2&t){const t=e.$implicit,n=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("isSelectAllTriggered",n.isSelectAll)("inputData",t)}}function X(t,e){if(1&t&&(i["\u0275\u0275elementStart"](0,"div",31),i["\u0275\u0275template"](1,J,4,2,"div",32),i["\u0275\u0275elementEnd"]()),2&t){const t=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngForOf",t.notifications)}}function Q(t,e){1&t&&(i["\u0275\u0275elementStart"](0,"div"),i["\u0275\u0275elementStart"](1,"h4",39),i["\u0275\u0275text"](2," Getting your notifications ... "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]())}function $(t,e){1&t&&(i["\u0275\u0275elementStart"](0,"div"),i["\u0275\u0275elementStart"](1,"h4",39),i["\u0275\u0275text"](2," No Notifications available ... "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]())}function K(t,e){if(1&t&&(i["\u0275\u0275elementStart"](0,"div",36),i["\u0275\u0275template"](1,Q,3,0,"div",11),i["\u0275\u0275template"](2,$,3,0,"div",11),i["\u0275\u0275elementStart"](3,"div",37),i["\u0275\u0275element"](4,"img",38),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&t){const t=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.isReloading),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",!t.isReloading)}}const q=function(t){return{width:t}};function Z(t,e){if(1&t){const t=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",40),i["\u0275\u0275elementStart"](1,"app-notification-approval",41),i["\u0275\u0275listener"]("refreshList",(function(){return i["\u0275\u0275restoreView"](t),i["\u0275\u0275nextContext"]().refreshData()})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&t){const t=i["\u0275\u0275nextContext"]();i["\u0275\u0275property"]("ngStyle",i["\u0275\u0275pureFunction1"](2,q,t.isApprovalClicked?"43rem":"auto")),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("inputData",t.clicked_wh_id)}}const tt=function(t,e){return{"notif-card-width100":t,"notif-card-width41":e}};let et=(()=>{class t{constructor(t,e){this.notificationService=t,this.utilityService=e,this.closeEvent=new i.EventEmitter,this.notifications=[],this.selectedTasks=[],this.isReloading=!1,this.isSelectAll=!1,this.isApprovalClicked=!1}ngOnInit(){return Object(p.c)(this,void 0,void 0,(function*(){this.isReloading=!0,this.notifications=yield this.initData(),this.isReloading=!1}))}ngOnChanges(){return Object(p.c)(this,void 0,void 0,(function*(){console.log("inside initdata")}))}initData(){return Object(p.c)(this,void 0,void 0,(function*(){let t=yield this.notificationService.getWorkflowNotifications(),e=[];if(t&&"S"==t.messType)e=yield this.notificationService.notificationsFormatter(t.data);else if(t&&"E"==t.messType){let e=yield this.notificationService.getErrorDetails(t.errorCode);this.utilityService.showMessage(e?`ERR CODE : ${t.errorCode}. Description : ${e.user_message}`:"Sorry, we are unable to retreive your notifications.","Dismiss")}return e}))}refreshData(){return Object(p.c)(this,void 0,void 0,(function*(){this.selectedTasks=[];let t=[];this.isReloading=!0,this.isApprovalClicked=!1,this.closeAllApprovalDialogSheets(),t=yield this.initData(),this.notifications=t,this.isReloading=!1}))}selectAll(t){this.isSelectAll=t.checked,this.notifCards.forEach(t.checked?t=>{t.selectAllTriggered()}:t=>{t.deSelectAllTriggered()})}close(){this.closeEvent.emit()}openApprovalSheet(t){this.isApprovalClicked&&this.closeAllApprovalDialogSheets(),this.utilityService.showMessage("The notification detail page is under re-construction. Sorry for the inconvinence","Dismiss"),console.log(t),this.clicked_wh_id={workflowHeaderId:t.workflow_header_id}}closeAllApprovalDialogSheets(){this.notifCards.forEach(t=>{t.isApprovalDialogOpen=!1})}closeApprovalSheet(){this.isApprovalClicked=!1}handleCardSelection(t){t.isSelected?this.selectedTasks.push(t.workflowHeaderId):this.selectedTasks=d.reject(this.selectedTasks,e=>e==t.workflowHeaderId)}disableApprovalButtons(){this.isapprovalButtonsEnabled=!1}approveChangeMultiple(){return Object(p.c)(this,void 0,void 0,(function*(){this.disableApprovalButtons(),this.selectedTasks.forEach((t,e)=>Object(p.c)(this,void 0,void 0,(function*(){yield this.notificationService.approveChange(t),e==this.selectedTasks.length-1&&this.refreshData()})))}))}rejectChangeMultiple(){return Object(p.c)(this,void 0,void 0,(function*(){this.disableApprovalButtons(),this.selectedTasks.forEach((t,e)=>Object(p.c)(this,void 0,void 0,(function*(){yield this.notificationService.rejectChange(t),e==this.selectedTasks.length-1&&this.refreshData()})))}))}showApprovals(){return Object(p.c)(this,void 0,void 0,(function*(){this.notifications=d.filter(this.notifications,t=>"created"==t.change_status)}))}}return t.\u0275fac=function(e){return new(e||t)(i["\u0275\u0275directiveInject"](w),i["\u0275\u0275directiveInject"](h.a))},t.\u0275cmp=i["\u0275\u0275defineComponent"]({type:t,selectors:[["app-notification-list"]],viewQuery:function(t,e){if(1&t&&i["\u0275\u0275viewQuery"](z,!0),2&t){let t;i["\u0275\u0275queryRefresh"](t=i["\u0275\u0275loadQuery"]())&&(e.notifCards=t)}},outputs:{closeEvent:"closeEvent"},features:[i["\u0275\u0275NgOnChangesFeature"]],decls:36,vars:12,consts:[[1,"container-fluid","p-0","notification-list-styles"],[1,"row","pt-2"],[1,"col-12","col-lg-8","d-flex","flex-row"],[1,"my-auto","d-flex","mr-4"],["matTooltip","Select",1,"mt-1",3,"change"],[1,"title-text","mr-2","my-auto"],["mat-icon-button","","matTooltip","More Options",1,"iconTray",3,"matMenuTriggerFor"],[1,"icon-style"],["moreOptions","matMenu"],["mat-menu-item","",1,"drop-btn",3,"click"],["mat-menu-item","",1,"drop-btn"],[4,"ngIf"],[1,"col-12","col-lg-4","d-flex","flex-row"],["mat-icon-button","","matTooltip","Refresh",1,"iconTray","ml-auto","mr-2",3,"click"],["mat-icon-button","",1,"icon-style"],["mat-icon-button","","matTooltip","Settings",1,"iconTray","mr-2"],["mat-icon-button","","matTooltip","Close",1,"iconTray",3,"click"],["class","my-1",4,"ngIf"],["mode","indeterminate","class","my-1",4,"ngIf"],[1,"row","d-flex","flex-row"],[1,"h-100","mx-auto",3,"ngClass"],["class","w-100 p-0",4,"ngIf"],["class","mt-3",4,"ngIf"],[3,"vertical"],["class","h-100",3,"ngStyle",4,"ngIf"],["mat-icon-button","","matTooltip","Approve",1,"approve-btn","mr-3",3,"disabled","click"],[2,"color","white !important","font-size","18px !important"],["mat-icon-button","","matTooltip","Reject",1,"reject-btn",3,"disabled","click"],[2,"color","white !important","font-size","21px !important","margin-bottom","1px"],[1,"my-1"],["mode","indeterminate",1,"my-1"],[1,"w-100","p-0"],[4,"ngFor","ngForOf"],[3,"isSelectAllTriggered","inputData","sendSelectionChange","openApprovalSheet","closeApprovalSheet","refreshList"],["notifCard",""],[1,"my-2"],[1,"mt-3"],[1,"mt-4","w-100","h-100","d-flex","flex-row"],["src","https://assets.kebs.app/images/no_notifications.png","height","250","width","250",1,"mx-auto","slide-from-down"],[1,"d-flex","justify-content-center","align-items-center","mt-5","slide-in-top"],[1,"h-100",3,"ngStyle"],[3,"inputData","refreshList"]],template:function(t,e){if(1&t&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275elementStart"](1,"div",1),i["\u0275\u0275elementStart"](2,"div",2),i["\u0275\u0275elementStart"](3,"div",3),i["\u0275\u0275elementStart"](4,"mat-checkbox",4),i["\u0275\u0275listener"]("change",(function(t){return e.selectAll(t)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"div",5),i["\u0275\u0275text"](6," Notifications "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](7,"div"),i["\u0275\u0275elementStart"](8,"button",6),i["\u0275\u0275elementStart"](9,"mat-icon",7),i["\u0275\u0275text"](10,"more_vert"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](11,"mat-menu",null,8),i["\u0275\u0275elementStart"](13,"button",9),i["\u0275\u0275listener"]("click",(function(){return e.showApprovals()})),i["\u0275\u0275text"](14," Show Approvals "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](15,"button",10),i["\u0275\u0275text"](16," Show Unread "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](17,G,7,2,"div",11),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](18,"div",12),i["\u0275\u0275elementStart"](19,"button",13),i["\u0275\u0275listener"]("click",(function(){return e.refreshData()})),i["\u0275\u0275elementStart"](20,"mat-icon",14),i["\u0275\u0275text"](21,"refresh"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](22,"button",15),i["\u0275\u0275elementStart"](23,"mat-icon",7),i["\u0275\u0275text"](24,"settings"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](25,"button",16),i["\u0275\u0275listener"]("click",(function(){return e.close()})),i["\u0275\u0275elementStart"](26,"mat-icon",7),i["\u0275\u0275text"](27,"close"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](28,U,1,0,"mat-divider",17),i["\u0275\u0275template"](29,Y,1,0,"mat-progress-bar",18),i["\u0275\u0275elementStart"](30,"div",19),i["\u0275\u0275elementStart"](31,"div",20),i["\u0275\u0275template"](32,X,2,1,"div",21),i["\u0275\u0275template"](33,K,5,2,"div",22),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](34,"mat-divider",23),i["\u0275\u0275template"](35,Z,2,4,"div",24),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&t){const t=i["\u0275\u0275reference"](12);i["\u0275\u0275advance"](8),i["\u0275\u0275property"]("matMenuTriggerFor",t),i["\u0275\u0275advance"](9),i["\u0275\u0275property"]("ngIf",e.selectedTasks&&e.selectedTasks.length>0),i["\u0275\u0275advance"](11),i["\u0275\u0275property"]("ngIf",!e.isReloading),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",e.isReloading),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction2"](9,tt,!e.isApprovalClicked,e.isApprovalClicked)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",e.notifications&&e.notifications.length>0),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",!(e.notifications&&e.notifications.length>0)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("vertical",!0),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",e.isApprovalClicked)}},directives:[o.a,s.a,l.a,r.f,a.a,r.g,r.d,c.NgIf,c.NgClass,S.a,c.NgForOf,I,c.NgStyle,F],styles:[".notification-list-styles[_ngcontent-%COMP%]   .drop-btn[_ngcontent-%COMP%]{font-size:13px!important;height:38px!important;line-height:2!important;padding:0 13px}.notification-list-styles[_ngcontent-%COMP%]   .approve-btn[_ngcontent-%COMP%]{background-color:#64d464;width:27px;height:27px;line-height:25px}.notification-list-styles[_ngcontent-%COMP%]   .approve-btn[_ngcontent-%COMP%], .notification-list-styles[_ngcontent-%COMP%]   .reject-btn[_ngcontent-%COMP%]{font-size:18px;color:#fff;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 3px 0 rgba(0,0,0,.14),0 2px 14px 0 rgba(0,0,0,.12)}.notification-list-styles[_ngcontent-%COMP%]   .reject-btn[_ngcontent-%COMP%]{background-color:#fc3939;width:28px;height:28px;line-height:27px}.notification-list-styles[_ngcontent-%COMP%]   .notif-card-width100[_ngcontent-%COMP%]{max-width:38rem!important;width:100%}.notification-list-styles[_ngcontent-%COMP%]   .notif-card-width41[_ngcontent-%COMP%]{width:35rem}.notification-list-styles[_ngcontent-%COMP%]   .iconTray[_ngcontent-%COMP%]{width:35px!important;height:35px!important;line-height:35px!important}.notification-list-styles[_ngcontent-%COMP%]   .iconTray[_ngcontent-%COMP%]   .icon-style[_ngcontent-%COMP%]{font-size:19px;color:#66615b}.notification-list-styles[_ngcontent-%COMP%]   .title-text[_ngcontent-%COMP%]{font-size:14px!important}.notification-list-styles[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#3c3c3c;font-weight:400;font-size:16px}.notification-list-styles[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}.notification-list-styles[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}"]}),t})();var nt=n("0IaG");let it=(()=>{class t{constructor(t){this.dialogRef=t,this.closeNotifications=new i.EventEmitter,this.isApprovalClicked=!1}ngOnInit(){}close(){this.dialogRef.close()}openApprovalDialog(t){this.isApprovalClicked=!this.isApprovalClicked,console.log(t),this.clicked_wh_id={workflowHeaderId:t.workflow_header_id}}}return t.\u0275fac=function(e){return new(e||t)(i["\u0275\u0275directiveInject"](nt.h))},t.\u0275cmp=i["\u0275\u0275defineComponent"]({type:t,selectors:[["ng-component"]],outputs:{closeNotifications:"closeNotifications"},decls:3,vars:0,consts:[[1,"container-fluid","notification-main","pl-1","pr-1","h-100"],[1,"p-0","w-100","h-100"],[3,"closeEvent"]],template:function(t,e){1&t&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275elementStart"](1,"div",1),i["\u0275\u0275elementStart"](2,"app-notification-list",2),i["\u0275\u0275listener"]("closeEvent",(function(){return e.close()})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]())},directives:function(){return[et]},styles:[".notification-main[_ngcontent-%COMP%]{display:inline-flex!important}.notification-main[_ngcontent-%COMP%]   .notification-list-width[_ngcontent-%COMP%]{min-width:32rem;width:auto!important}.notification-width-clicked[_ngcontent-%COMP%]{width:78rem}.notification-width[_ngcontent-%COMP%]{width:32rem}"]}),t})()},ucYs:function(t,e,n){"use strict";n.d(e,"a",(function(){return s}));var i=n("mrSG"),o=n("xG9w"),r=n("fXoL"),a=n("tk/3"),l=n("BVzC");let s=(()=>{class t{constructor(t,e){this.http=t,this.errorService=e,this.workflowStatusList=[],this.getWorkflowStatusList()}getWorkflowStatusList(){this.http.post("/api/wfPrimary/getWorkflowStatusList","").subscribe(t=>{this.workflowStatusList=t.data},t=>{this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Internal Server While Getting Workflow Status List",t&&t.params?t.params:t&&t.error?t.error.params:{})})}getWorkflowProperties(t){return new Promise((e,n)=>{this.http.post("/api/wfPrimary/getWorkflowProperties",{applicationId:t}).subscribe(t=>e(t),t=>n(t))})}getWorkflowPropertiesByWorkflowId(t){return new Promise((e,n)=>{this.http.post("/api/wfPrimary/getWorkflowPropertiesByWorkflowId",{workflowId:t}).subscribe(t=>e(t),t=>n(t))})}getApproversHierarchy(t){return new Promise((e,n)=>{this.http.post("/api/wfPrimary/getApproversHierarchy",t).subscribe(t=>e(t),t=>n(t))})}createWorkflowItems(t){return new Promise((e,n)=>{this.http.post("/api/wfPrimary/createWorkflowItems",t).subscribe(t=>e(t),t=>n(t))})}getWorkflowDetails(t){return new Promise((e,n)=>{this.http.post("/api/wfPrimary/getWorkflowDetails",{workflowId:t}).subscribe(t=>e(t),t=>{this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Error while getting workflow details",t&&t.params?t.params:t&&t.error?t.error.params:{})})})}formatApproversHierarchy(t,e){return Object(i.c)(this,void 0,void 0,(function*(){0==t.length&&e.cc0&&e.cc0.appr0&&e.cc0.appr0.length>0&&t.push([{approvalType:"I",purposeOfInsertingIntoArray:"Just to ensure that 'I' type data is used, if it exists"}]);for(let n=0;n<t.length;n++){let i=[],r=o.keys(e["cc"+n]);for(let o=0;o<r.length;o++)for(let a=0;a<e["cc"+n][r[o]].length;a++){let l={name:e["cc"+n][r[o]][a].DELEGATE_NAME,oid:e["cc"+n][r[o]][a].DELEGATE_OID,level:o+1,designation:e["cc"+n][r[o]][a].DELEGATE_DESIGNATION_NAME,is_delegated:e["cc"+n][r[o]][a].IS_DELEGATED,role:e["cc"+n][r[o]][a].DELEGATE_ROLE_NAME};if(1==e["cc"+n][r[o]][a].IS_DELEGATED&&(l.delegated_by={name:e["cc"+n][r[o]][a].APPROVER_NAME,oid:e["cc"+n][r[o]][a].APPROVER_OID,level:o+1,designation:e["cc"+n][r[o]][a].APPROVER_DESIGNATION_NAME}),i.push(l),n==t.length-1&&o==r.length-1&&a==e["cc"+n][r[o]].length-1)return i}}}))}storeComments(t,e,n){return new Promise((i,o)=>{this.http.post("/api/wfPrimary/updateComments",{workflowHeaderId:t,newComments:e,commentor:n}).subscribe(t=>i(t),t=>(this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Internal Server Error while Updating Workflow Comments",t&&t.params?t.params:t&&t.error?t.error.params:{}),o(t)))})}updateWorkflowItems(t){return new Promise((e,n)=>{this.http.post("/api/wfPrimary/updateWorkflowItems",t).subscribe(t=>e(t),t=>(console.log(t),n(t)))})}formatApproversHierarchyForOpportunityApprovalActivity(t){return Object(i.c)(this,void 0,void 0,(function*(){for(let e=0;e<1;e++){let n=[],i=o.keys(t["cc"+e]);for(let o=0;o<i.length;o++)for(let r=0;r<t["cc"+e][i[o]].length;r++){let a={name:t["cc"+e][i[o]][r].DELEGATE_NAME,oid:t["cc"+e][i[o]][r].DELEGATE_OID,level:t["cc"+e][i[o]][r].APPROVAL_ORDER,designation:t["cc"+e][i[o]][r].DELEGATE_DESIGNATION_NAME,is_delegated:t["cc"+e][i[o]][r].IS_DELEGATED};if(1==t["cc"+e][i[o]][r].IS_DELEGATED&&(a.delegated_by={name:t["cc"+e][i[o]][r].APPROVER_NAME,oid:t["cc"+e][i[o]][r].APPROVER_OID,level:t["cc"+e][i[o]][r].APPROVAL_ORDER,designation:t["cc"+e][i[o]][r].APPROVER_DESIGNATION_NAME}),n.push(a),o==i.length-1&&r==t["cc"+e][i[o]].length-1)return n}}}))}}return t.\u0275fac=function(e){return new(e||t)(r["\u0275\u0275inject"](a.c),r["\u0275\u0275inject"](l.a))},t.\u0275prov=r["\u0275\u0275defineInjectable"]({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()}}]);