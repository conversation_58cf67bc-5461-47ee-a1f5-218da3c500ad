(window.webpackJsonp=window.webpackJsonp||[]).push([[760],{"+l3v":function(t,e,n){"use strict";n.r(e),n.d(e,"PaymentActivityComponent",(function(){return h}));var i=n("0IaG"),a=n("3Pt+"),l=n("wd/R"),o=n("ofXK"),r=n("kmnG"),d=n("qFsG"),m=n("NFeN"),c=(n("Qu3c"),n("bTqV")),s=n("Wp6s"),v=n("fXoL"),p=n("A6Kz"),f=n("dNgK");function u(t,e){if(1&t&&(v["\u0275\u0275elementStart"](0,"div",34),v["\u0275\u0275elementStart"](1,"div",35),v["\u0275\u0275text"](2),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](3,"div",36),v["\u0275\u0275elementStart"](4,"mat-form-field",37),v["\u0275\u0275element"](5,"input",38),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit;v["\u0275\u0275property"]("formGroupName",e.index),v["\u0275\u0275advance"](2),v["\u0275\u0275textInterpolate1"]("",t.value.activityName," ")}}function y(t,e){if(1&t&&(v["\u0275\u0275elementStart"](0,"div",26),v["\u0275\u0275elementStart"](1,"div",23),v["\u0275\u0275elementStart"](2,"div",27),v["\u0275\u0275elementStart"](3,"div",28),v["\u0275\u0275text"](4,"Activity name"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](5,"div",29),v["\u0275\u0275text"](6,"Days"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](7,"div",30),v["\u0275\u0275template"](8,u,6,2,"div",31),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](9,"div",32),v["\u0275\u0275element"](10,"img",33),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"]()),2&t){const t=v["\u0275\u0275nextContext"]();v["\u0275\u0275advance"](8),v["\u0275\u0275property"]("ngForOf",null==t.ActivityArray?null:t.ActivityArray.controls)}}let h=(()=>{class t{constructor(t,e,n,i,a){this.invoiceService=t,this.data=e,this.dialogRef=n,this.formBuilder=i,this._snackBar=a,this.initialTotalDays=0,this.getDDMMYYDate=t=>null!=t&&""!=t?l(t).format("DD-MM-YY"):""}ngOnInit(){this.oldExpectedOn=this.data.plannedOn,this.activitiesForm=this.formBuilder.group({activities:this.formBuilder.array([])}),this.invoiceService.getInvoiceListActivityList(this.data.milestoneId).subscribe(t=>{this.addActivitiesToFormArray(t.activities),t.activities.forEach((t,e)=>{this.initialTotalDays=this.initialTotalDays+t.no_of_days})},t=>{console.error(t)})}closeDialog(){this.dialogRef.close()}get ActivityArray(){return this.activitiesForm.get("activities")}addActivitiesToFormArray(t){this.activities=this.activitiesForm.get("activities"),t.forEach((t,e)=>{this.activities.push(this.addActivity(t.activity_name,t.no_of_days,t.id))})}createActivity(){return this.formBuilder.group({activityName:"",noOfDays:""})}getTotalDays(){let t=0;this.activitiesForm.value.activities.forEach((e,n)=>{let i=parseInt(e.noOfDays);"number"==typeof i&&(t+=i),this.activitiesForm.value.activities.length-1==n&&(this.totalD=t)}),this.newExpectedDate=l(this.oldExpectedOn).add(t-this.initialTotalDays,"days")}updateActivity(t){this.invoiceService.updateInvoiceActivityList(this.data.milestoneId,t.value.activities,this.oldExpectedOn,this.newExpectedDate).subscribe(t=>{console.log(t),"success"==t.message&&this._snackBar.open("Update Successful","X",{duration:2e3})},t=>{console.error(t)})}addActivity(t,e,n){return this.formBuilder.group({id:n,activityName:t,noOfDays:e})}}return t.\u0275fac=function(e){return new(e||t)(v["\u0275\u0275directiveInject"](p.a),v["\u0275\u0275directiveInject"](i.a),v["\u0275\u0275directiveInject"](i.h),v["\u0275\u0275directiveInject"](a.i),v["\u0275\u0275directiveInject"](f.a))},t.\u0275cmp=v["\u0275\u0275defineComponent"]({type:t,selectors:[["app-payment-activity"]],decls:49,vars:9,consts:[[1,"row","fullBody"],[1,"col-12"],[1,"row","border-bottom","solid"],[1,"col-11","pt-2","pb-2","text-center","headingBold"],[1,"col-1","pl-5"],["mat-icon-button","","color","primary",1,"icon-tray-button",3,"click"],[1,"smallCardIcon"],[1,"row","pt-4",2,"background-color","#f7f7f7"],[1,"col-5","pt-2"],[1,"row"],[1,"col-5","key"],[1,"col-7","value",2,"color","#cf0001 !important"],[1,"row","pt-2"],[1,"col-7","value"],[1,"col-4","pt-2"],[1,"col-7","key"],[1,"col-5","value"],[1,"col-3","pb-3"],[1,"row","text-center"],[1,"col-12","headingBold"],[1,"col-12","value",2,"font-size","12px"],[3,"formGroup","ngSubmit"],["class","row pt-3",4,"ngIf"],[1,"col-8"],[1,"col-4"],["type","submit","color","primary","mat-mini-fab","",3,"disabled"],[1,"row","pt-3"],[1,"row","pb-2"],[1,"col-7","tableHeader"],[1,"col-5","pl-4","tableHeader"],["formArrayName","activities"],["class","row pt-2 border-bottom solid",3,"formGroupName",4,"ngFor","ngForOf"],[1,"col-4",2,"margin-top","6rem"],["src","https://assets.kebs.app/images/change_date_bg.png","height","190","width","270","alt","",1,"rounded","mx-auto","d-block"],[1,"row","pt-2","border-bottom","solid",3,"formGroupName"],[1,"col-7","pt-2","Activity"],[1,"col-5"],["appearance","outline"],["type","number","matInput","","formControlName","noOfDays"]],template:function(t,e){1&t&&(v["\u0275\u0275elementStart"](0,"div",0),v["\u0275\u0275elementStart"](1,"div",1),v["\u0275\u0275elementStart"](2,"div",2),v["\u0275\u0275elementStart"](3,"div",3),v["\u0275\u0275text"](4," Change planned collection date "),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](5,"div",4),v["\u0275\u0275elementStart"](6,"span"),v["\u0275\u0275elementStart"](7,"button",5),v["\u0275\u0275listener"]("click",(function(){return e.closeDialog()})),v["\u0275\u0275elementStart"](8,"mat-icon",6),v["\u0275\u0275text"](9,"close"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](10,"div",7),v["\u0275\u0275elementStart"](11,"div",8),v["\u0275\u0275elementStart"](12,"div",9),v["\u0275\u0275elementStart"](13,"div",10),v["\u0275\u0275text"](14,"In Milestone"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](15,"div",11),v["\u0275\u0275text"](16),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](17,"div",12),v["\u0275\u0275elementStart"](18,"div",10),v["\u0275\u0275text"](19,"In Item"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](20,"div",13),v["\u0275\u0275text"](21),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](22,"div",14),v["\u0275\u0275elementStart"](23,"div",9),v["\u0275\u0275elementStart"](24,"div",15),v["\u0275\u0275text"](25,"Planned On (Old)"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](26,"div",16),v["\u0275\u0275text"](27),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](28,"div",12),v["\u0275\u0275elementStart"](29,"div",15),v["\u0275\u0275text"](30,"Planned On (New)"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](31,"div",16),v["\u0275\u0275text"](32),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](33,"div",17),v["\u0275\u0275elementStart"](34,"mat-card"),v["\u0275\u0275elementStart"](35,"div",18),v["\u0275\u0275elementStart"](36,"div",19),v["\u0275\u0275text"](37),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](38,"div",18),v["\u0275\u0275elementStart"](39,"div",20),v["\u0275\u0275text"](40," Total Days "),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](41,"form",21),v["\u0275\u0275listener"]("ngSubmit",(function(){return e.updateActivity(e.activitiesForm)})),v["\u0275\u0275template"](42,y,11,1,"div",22),v["\u0275\u0275elementStart"](43,"div",9),v["\u0275\u0275element"](44,"div",23),v["\u0275\u0275elementStart"](45,"div",24),v["\u0275\u0275elementStart"](46,"button",25),v["\u0275\u0275elementStart"](47,"mat-icon"),v["\u0275\u0275text"](48,"done_all"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"]()),2&t&&(v["\u0275\u0275advance"](16),v["\u0275\u0275textInterpolate"](e.data.milestoneName),v["\u0275\u0275advance"](5),v["\u0275\u0275textInterpolate"](e.data.itemName),v["\u0275\u0275advance"](6),v["\u0275\u0275textInterpolate1"](" ",e.getDDMMYYDate(e.oldExpectedOn)," "),v["\u0275\u0275advance"](5),v["\u0275\u0275textInterpolate"](e.getDDMMYYDate(e.newExpectedDate)),v["\u0275\u0275advance"](5),v["\u0275\u0275textInterpolate2"](" ",e.totalD?e.totalD:"","",e.getTotalDays()," "),v["\u0275\u0275advance"](4),v["\u0275\u0275property"]("formGroup",e.activitiesForm),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("ngIf",e.activitiesForm.value.activities.length>0),v["\u0275\u0275advance"](4),v["\u0275\u0275property"]("disabled",e.activitiesForm.invalid))},directives:[c.a,m.a,s.a,a.J,a.w,a.n,o.NgIf,a.h,o.NgForOf,a.o,r.c,a.A,d.b,a.e,a.v,a.l],styles:[".fullBody[_ngcontent-%COMP%]   .headingBold[_ngcontent-%COMP%]{color:#cf0001;font-weight:500}.fullBody[_ngcontent-%COMP%]   .key[_ngcontent-%COMP%]{font-size:12px;color:#817e7e}.fullBody[_ngcontent-%COMP%]   .tableHeader[_ngcontent-%COMP%]{color:#817e7e;font-size:14px}.fullBody[_ngcontent-%COMP%]   .Activity[_ngcontent-%COMP%]{font-size:14px}.fullBody[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{color:#181818;font-size:14px;font-weight:400;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.fullBody[_ngcontent-%COMP%]   .mat-card[_ngcontent-%COMP%]{height:5rem;width:12rem}.fullBody[_ngcontent-%COMP%]     .mat-form-field-flex>.mat-form-field-infix{padding:.4em 0!important;width:53px!important}.fullBody[_ngcontent-%COMP%]     .mat-form-field-label-wrapper{top:-1.5em}.fullBody[_ngcontent-%COMP%]     .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label{transform:translateY(-1.1em) scale(.75);width:133.33333%}.fullBody[_ngcontent-%COMP%]   .smallCardIcon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.fullBody[_ngcontent-%COMP%]   .icon-tray-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}"]}),t})()},polZ:function(t,e,n){"use strict";n.r(e),n.d(e,"PaymentActivityComponent",(function(){return h}));var i=n("0IaG"),a=n("3Pt+"),l=n("wd/R"),o=n("ofXK"),r=n("kmnG"),d=n("qFsG"),m=n("NFeN"),c=(n("Qu3c"),n("bTqV")),s=n("Wp6s"),v=n("fXoL"),p=n("T4Li"),f=n("dNgK");function u(t,e){if(1&t&&(v["\u0275\u0275elementStart"](0,"div",34),v["\u0275\u0275elementStart"](1,"div",35),v["\u0275\u0275text"](2),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](3,"div",36),v["\u0275\u0275elementStart"](4,"mat-form-field",37),v["\u0275\u0275element"](5,"input",38),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit;v["\u0275\u0275property"]("formGroupName",e.index),v["\u0275\u0275advance"](2),v["\u0275\u0275textInterpolate1"]("",t.value.activityName," ")}}function y(t,e){if(1&t&&(v["\u0275\u0275elementStart"](0,"div",26),v["\u0275\u0275elementStart"](1,"div",23),v["\u0275\u0275elementStart"](2,"div",27),v["\u0275\u0275elementStart"](3,"div",28),v["\u0275\u0275text"](4,"Activity name"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](5,"div",29),v["\u0275\u0275text"](6,"Days"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](7,"div",30),v["\u0275\u0275template"](8,u,6,2,"div",31),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](9,"div",32),v["\u0275\u0275element"](10,"img",33),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"]()),2&t){const t=v["\u0275\u0275nextContext"]();v["\u0275\u0275advance"](8),v["\u0275\u0275property"]("ngForOf",null==t.ActivityArray?null:t.ActivityArray.controls)}}let h=(()=>{class t{constructor(t,e,n,i,a){this.invoiceService=t,this.data=e,this.dialogRef=n,this.formBuilder=i,this._snackBar=a,this.initialTotalDays=0,this.getDDMMYYDate=t=>null!=t&&""!=t?l(t).format("DD-MM-YY"):""}ngOnInit(){this.oldExpectedOn=this.data.plannedOn,this.activitiesForm=this.formBuilder.group({activities:this.formBuilder.array([])}),this.invoiceService.getInvoiceListActivityList(this.data.milestoneId).subscribe(t=>{this.addActivitiesToFormArray(t.activities),t.activities.forEach((t,e)=>{this.initialTotalDays=this.initialTotalDays+t.no_of_days})},t=>{console.error(t)})}closeDialog(){this.dialogRef.close()}get ActivityArray(){return this.activitiesForm.get("activities")}addActivitiesToFormArray(t){this.activities=this.activitiesForm.get("activities"),t.forEach((t,e)=>{this.activities.push(this.addActivity(t.activity_name,t.no_of_days,t.id))})}createActivity(){return this.formBuilder.group({activityName:"",noOfDays:""})}getTotalDays(){let t=0;this.activitiesForm.value.activities.forEach((e,n)=>{let i=parseInt(e.noOfDays);"number"==typeof i&&(t+=i),this.activitiesForm.value.activities.length-1==n&&(this.totalD=t)}),this.newExpectedDate=l(this.oldExpectedOn).add(t-this.initialTotalDays,"days")}updateActivity(t){this.invoiceService.updateInvoiceActivityList(this.data.milestoneId,t.value.activities,this.oldExpectedOn,this.newExpectedDate).subscribe(t=>{console.log(t),"success"==t.message&&this._snackBar.open("Update Successful","X",{duration:2e3})},t=>{console.error(t)})}addActivity(t,e,n){return this.formBuilder.group({id:n,activityName:t,noOfDays:e})}}return t.\u0275fac=function(e){return new(e||t)(v["\u0275\u0275directiveInject"](p.a),v["\u0275\u0275directiveInject"](i.a),v["\u0275\u0275directiveInject"](i.h),v["\u0275\u0275directiveInject"](a.i),v["\u0275\u0275directiveInject"](f.a))},t.\u0275cmp=v["\u0275\u0275defineComponent"]({type:t,selectors:[["app-payment-activity"]],decls:49,vars:9,consts:[[1,"row","fullBody"],[1,"col-12"],[1,"row","border-bottom","solid"],[1,"col-11","pt-2","pb-2","text-center","headingBold"],[1,"col-1","pl-5"],["mat-icon-button","","color","primary",1,"icon-tray-button",3,"click"],[1,"smallCardIcon"],[1,"row","pt-4",2,"background-color","#f7f7f7"],[1,"col-5","pt-2"],[1,"row"],[1,"col-5","key"],[1,"col-7","value",2,"color","#cf0001 !important"],[1,"row","pt-2"],[1,"col-7","value"],[1,"col-4","pt-2"],[1,"col-7","key"],[1,"col-5","value"],[1,"col-3","pb-3"],[1,"row","text-center"],[1,"col-12","headingBold"],[1,"col-12","value",2,"font-size","12px"],[3,"formGroup","ngSubmit"],["class","row pt-3",4,"ngIf"],[1,"col-8"],[1,"col-4"],["type","submit","color","primary","mat-mini-fab","",3,"disabled"],[1,"row","pt-3"],[1,"row","pb-2"],[1,"col-7","tableHeader"],[1,"col-5","pl-4","tableHeader"],["formArrayName","activities"],["class","row pt-2 border-bottom solid",3,"formGroupName",4,"ngFor","ngForOf"],[1,"col-4",2,"margin-top","6rem"],["src","https://assets.kebs.app/images/change_date_bg.png","height","190","width","270","alt","",1,"rounded","mx-auto","d-block"],[1,"row","pt-2","border-bottom","solid",3,"formGroupName"],[1,"col-7","pt-2","Activity"],[1,"col-5"],["appearance","outline"],["type","number","matInput","","formControlName","noOfDays"]],template:function(t,e){1&t&&(v["\u0275\u0275elementStart"](0,"div",0),v["\u0275\u0275elementStart"](1,"div",1),v["\u0275\u0275elementStart"](2,"div",2),v["\u0275\u0275elementStart"](3,"div",3),v["\u0275\u0275text"](4," Change planned collection date "),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](5,"div",4),v["\u0275\u0275elementStart"](6,"span"),v["\u0275\u0275elementStart"](7,"button",5),v["\u0275\u0275listener"]("click",(function(){return e.closeDialog()})),v["\u0275\u0275elementStart"](8,"mat-icon",6),v["\u0275\u0275text"](9,"close"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](10,"div",7),v["\u0275\u0275elementStart"](11,"div",8),v["\u0275\u0275elementStart"](12,"div",9),v["\u0275\u0275elementStart"](13,"div",10),v["\u0275\u0275text"](14,"In Milestone"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](15,"div",11),v["\u0275\u0275text"](16),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](17,"div",12),v["\u0275\u0275elementStart"](18,"div",10),v["\u0275\u0275text"](19,"In Item"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](20,"div",13),v["\u0275\u0275text"](21),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](22,"div",14),v["\u0275\u0275elementStart"](23,"div",9),v["\u0275\u0275elementStart"](24,"div",15),v["\u0275\u0275text"](25,"Planned On (Old)"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](26,"div",16),v["\u0275\u0275text"](27),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](28,"div",12),v["\u0275\u0275elementStart"](29,"div",15),v["\u0275\u0275text"](30,"Planned On (New)"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](31,"div",16),v["\u0275\u0275text"](32),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](33,"div",17),v["\u0275\u0275elementStart"](34,"mat-card"),v["\u0275\u0275elementStart"](35,"div",18),v["\u0275\u0275elementStart"](36,"div",19),v["\u0275\u0275text"](37),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](38,"div",18),v["\u0275\u0275elementStart"](39,"div",20),v["\u0275\u0275text"](40," Total Days "),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](41,"form",21),v["\u0275\u0275listener"]("ngSubmit",(function(){return e.updateActivity(e.activitiesForm)})),v["\u0275\u0275template"](42,y,11,1,"div",22),v["\u0275\u0275elementStart"](43,"div",9),v["\u0275\u0275element"](44,"div",23),v["\u0275\u0275elementStart"](45,"div",24),v["\u0275\u0275elementStart"](46,"button",25),v["\u0275\u0275elementStart"](47,"mat-icon"),v["\u0275\u0275text"](48,"done_all"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"]()),2&t&&(v["\u0275\u0275advance"](16),v["\u0275\u0275textInterpolate"](e.data.milestoneName),v["\u0275\u0275advance"](5),v["\u0275\u0275textInterpolate"](e.data.itemName),v["\u0275\u0275advance"](6),v["\u0275\u0275textInterpolate1"](" ",e.getDDMMYYDate(e.oldExpectedOn)," "),v["\u0275\u0275advance"](5),v["\u0275\u0275textInterpolate"](e.getDDMMYYDate(e.newExpectedDate)),v["\u0275\u0275advance"](5),v["\u0275\u0275textInterpolate2"](" ",e.totalD?e.totalD:"","",e.getTotalDays()," "),v["\u0275\u0275advance"](4),v["\u0275\u0275property"]("formGroup",e.activitiesForm),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("ngIf",e.activitiesForm.value.activities.length>0),v["\u0275\u0275advance"](4),v["\u0275\u0275property"]("disabled",e.activitiesForm.invalid))},directives:[c.a,m.a,s.a,a.J,a.w,a.n,o.NgIf,a.h,o.NgForOf,a.o,r.c,a.A,d.b,a.e,a.v,a.l],styles:[".fullBody[_ngcontent-%COMP%]   .headingBold[_ngcontent-%COMP%]{color:#cf0001;font-weight:500}.fullBody[_ngcontent-%COMP%]   .key[_ngcontent-%COMP%]{font-size:12px;color:#817e7e}.fullBody[_ngcontent-%COMP%]   .tableHeader[_ngcontent-%COMP%]{color:#817e7e;font-size:14px}.fullBody[_ngcontent-%COMP%]   .Activity[_ngcontent-%COMP%]{font-size:14px}.fullBody[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{color:#181818;font-size:14px;font-weight:400;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.fullBody[_ngcontent-%COMP%]   .mat-card[_ngcontent-%COMP%]{height:5rem;width:12rem}.fullBody[_ngcontent-%COMP%]     .mat-form-field-flex>.mat-form-field-infix{padding:.4em 0!important;width:53px!important}.fullBody[_ngcontent-%COMP%]     .mat-form-field-label-wrapper{top:-1.5em}.fullBody[_ngcontent-%COMP%]     .mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label{transform:translateY(-1.1em) scale(.75);width:133.33333%}.fullBody[_ngcontent-%COMP%]   .smallCardIcon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.fullBody[_ngcontent-%COMP%]   .icon-tray-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}"]}),t})()}}]);