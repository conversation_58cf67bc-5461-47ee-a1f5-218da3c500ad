import {
  Compo<PERSON>,
  ElementRef,
  HostListener,
  OnInit,
  TemplateRef,
  ViewChild,
  ViewContainerRef,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';

import { Subject, Subscription } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import * as _ from 'underscore';

import { Overlay, OverlayRef } from '@angular/cdk/overlay';
import { TemplatePortal } from '@angular/cdk/portal';
import { MatDialog } from '@angular/material/dialog';

import { MasterService } from './../../../services/master/master.service';
import { UtilitiesService } from './../../../services/utilities/utilities.service';
import { InboxService } from './../../../services/inbox/inbox.service';
import { ToasterService } from 'src/app/modules/applicant-tracking-system/shared-components/ats-custom-toast/toaster.service';
import { RolesService } from 'src/app/services/acl/roles.service';
import { FilterServiceService } from 'src/app/modules/project-management/shared-lazy-loaded/components/filters-common-dialog/services/filter-service.service';
import { LoginService } from 'src/app/services/login/login.service';
import { SubSink } from 'subsink';
import { EmployeeDirectoryService } from 'src/app/modules/employee-directory/services/employee-directory.service';

@Component({
  selector: 'app-landing-page',
  templateUrl: './landing-page.component.html',
  styleUrls: ['./landing-page.component.scss'],
})
export class LandingPageComponent implements OnInit {
  protected _onDestroy = new Subject<void>();

  overlayRef: OverlayRef;
  @ViewChild('triggerColumnCustomizationTemplateRef', { static: false })
  private triggerColumnCustomizationTemplateRef!: TemplateRef<HTMLElement>;
  triggerColumnCustomization: ElementRef;

  loadingGif: string = '';
  currentSelectedApplicationId: number = null;
  currentSelectedApprovalType: string = null;
  currentSelectedApplicationIndex: number = null;
  homePageUiConfig: any = {};
  profile: any = {};
  applications = [];
  subs = new SubSink();
  isLoading: boolean = true;
  isListViewLoading: boolean = false;
  isPendingRequestSelected: boolean = true;
  isSearchBarVisible: boolean = false;

  isRejectApprovedDataApiInProgress: boolean = false;
  isApproveApiInProgress: boolean = false;
  isRejectApiInProgress: boolean = false;

  skip: number = 0;
  limit: number = 15;

  filterSubscription$: Subscription;
  filterData: any = {};
  filterQuery: string = '';
  appliedFilter: any = [];

  searchParams: string = '';
  currentFilterSubApplicationId: string = '';
  currentColumnConfig = [];
  currentData = [];

  pendingCheckAll: boolean = false;
  countSelected: number = 0;
  selectedDetails: any = [];
  unselectedDetails: any = [];
  associateId:any;
  approvalPendings: any = [];

  constructor(
    private _toaster: ToasterService,
    private _masterService: MasterService,
    private _utilitiesService: UtilitiesService,
    private _inboxService: InboxService,
    private _rolesService: RolesService,
    private _filterService: FilterServiceService,
    private _loginService: LoginService,
    private _router: Router,
    private _activatedRoute: ActivatedRoute,
    private _overlay: Overlay,
    private _viewContainerRef: ViewContainerRef,
    private _dialog: MatDialog,
    private _edService: EmployeeDirectoryService,
  ) {}

  async ngOnInit() {
    this.profile = this._loginService.getProfile().profile;
    this.associateId=this.profile.aid
    let kebsHomePage = 'KEBS Homepage';
    if (!this._rolesService.checkApplicationForRoles(kebsHomePage)) {
      this._router.navigateByUrl('/main/dashboard');
    } else {
      this.calculateDynamicContentHeight();
      this.getUiConfiguration('homePage');

      this._utilitiesService.loadingGif$.subscribe((gif) => {
        this.loadingGif = gif;
      });

      await this.getInboxApplications();
      await this.addInitialWidthFieldInAllApplications();

      this._activatedRoute.queryParams.subscribe(async (params) => {
        this.currentSelectedApplicationId = params['applicationId']
          ? params['applicationId']
          : null;
        this.currentSelectedApprovalType = params['approvalType']
          ? params['approvalType']
          : null;
        if (!this.currentSelectedApplicationId) {
          if (this.applications.length > 0) {
            this._router.navigate([], {
              relativeTo: this._activatedRoute,
              queryParams: {
                applicationId: this.applications[0]['application_id'],
                approvalType: this.applications[0]['approval_type'] || null,
              },
              queryParamsHandling: 'merge',
            });
            this.currentSelectedApplicationId =
              this.applications[0]['application_id'];
            this.currentSelectedApprovalType =
              this.applications[0]['approval_type'];
            this.updateIsSelectedApplication(0);
          } else {
            this._router.navigateByUrl('/main');
          }
        } else {
          let index = this.applications.findIndex(
            (obj) =>
              obj?.application_id == this.currentSelectedApplicationId &&
              obj?.approval_type == this.currentSelectedApprovalType
          );
          if (index == -1) {
            this.updateIsSelectedApplication(0);
          } else {
            this.updateIsSelectedApplication(index);
          }
        }
      });
    }

    if (this.currentSelectedApplicationId) {
      await this.fetchAllPendingApprovalsCountData();
    }
   
  }

  @HostListener('window:resize')
  onResize() {
    this.calculateDynamicContentHeight();
  }

  /**
   * @description Calculates dynamic height based on screen size
   */
  calculateDynamicContentHeight() {
    // Dynamic content calculation
    let dynamicWindowWidth = window.innerWidth - 128;

    // Convert initial widths to numbers and calculate the total initial width
    let totalInitialWidth = this.currentColumnConfig.reduce(
      (sum, item) => sum + parseInt(item.initial_width),
      0
    );

    if (totalInitialWidth < dynamicWindowWidth) {
      // Calculate the scaling factor
      const scaleFactor = dynamicWindowWidth / totalInitialWidth;

      // Update each item's width, ensuring it doesn't go below its initial width
      this.currentColumnConfig = this.currentColumnConfig.map((item) => {
        const originalWidth = parseInt(item.initial_width);
        const newWidth = Math.round(originalWidth * scaleFactor);
        return { ...item, width: `${newWidth}px` };
      });
    } else {
      // If zooming back in, reset widths to their initial values
      this.currentColumnConfig = this.currentColumnConfig.map((item) => ({
        ...item,
        width: item.initial_width,
      }));
    }

    document.documentElement.style.setProperty(
      '--homePageDynamicHeight',
      window.innerHeight - 55 + 'px'
    );
    document.documentElement.style.setProperty(
      '--homePageInboxDynamicHeight',
      window.innerHeight - 91 + 'px'
    );

    if (!this.filterQuery || this.filterQuery == '') {
      document.documentElement.style.setProperty(
        '--homePageInboxListDynamicHeight',
        window.innerHeight - 91 - 128 + 'px'
      );
    } else {
      document.documentElement.style.setProperty(
        '--homePageInboxListDynamicHeight',
        window.innerHeight - 91 - 180 + 'px'
      );
    }
  }

  /**
   * @description Initialize local storage
   */
  async initializeLocalStorageData() {
    let requestType = this.isPendingRequestSelected
      ? '-pending-request-'
      : '-previous-request-';

    // Column customization data
    let existingData = JSON.parse(
      localStorage.getItem(
        this.currentSelectedApplicationId +
          (this.currentSelectedApprovalType || '') +
          requestType +
          '-inbox-list-view-customization-' +
          this.profile.aid
      )
    );
    if (existingData && existingData.length > 0) {
      let incomingData = _.pluck(this.currentColumnConfig, 'id');
      let existingLocalData = _.pluck(existingData, 'id');
      let allPresent = incomingData.every((item) =>
        existingLocalData.includes(item)
      );
      if (allPresent) {
        this.currentColumnConfig = existingData;
      } else {
        localStorage.removeItem(
          this.currentSelectedApplicationId +
            (this.currentSelectedApprovalType || '') +
            requestType +
            '-inbox-list-view-customization-' +
            this.profile.aid
        );
      }
    }

    this.calculateDynamicContentHeight();
  }

  /**
   * @description Add initial width field in configurations
   */
  async addInitialWidthFieldInAllApplications() {
    if (this.applications && this.applications.length > 0) {
      for (let i = 0; i < this.applications.length; i++) {
        if (this.applications[i]['pending_request_list_view_configurations']) {
          for (
            let j = 0;
            j <
            this.applications[i]['pending_request_list_view_configurations']
              .length;
            j++
          ) {
            this.applications[i]['pending_request_list_view_configurations'][j][
              'initial_width'
            ] =
              this.applications[i]['pending_request_list_view_configurations'][
                j
              ]['width'];
          }
        }
        if (this.applications[i]['previous_request_list_view_configurations']) {
          for (
            let j = 0;
            j <
            this.applications[i]['previous_request_list_view_configurations']
              .length;
            j++
          ) {
            this.applications[i]['previous_request_list_view_configurations'][
              j
            ]['initial_width'] =
              this.applications[i]['previous_request_list_view_configurations'][
                j
              ]['width'];
          }
        }
      }
    }
  }

  /**
   * To open Column Customization Overlay
   * @param {id} triggerField
   */
  openCustomizationOverlay(triggerField) {
    if (!this.overlayRef?.hasAttached()) {
      const positionStrategyBuilder = this._overlay.position();

      const positionStrategy = positionStrategyBuilder
        .flexibleConnectedTo(triggerField)
        .withFlexibleDimensions(true)
        .withPush(true)
        .withViewportMargin(25)
        .withGrowAfterOpen(true)
        .withPositions([
          {
            originX: 'center',
            originY: 'bottom',
            overlayX: 'center',
            overlayY: 'top',
          },
        ]);
      positionStrategy.withDefaultOffsetY(20);
      const scrollStrategy = this._overlay.scrollStrategies.close();

      this.overlayRef = this._overlay.create({
        positionStrategy,
        scrollStrategy,
        hasBackdrop: true,
        panelClass: ['pop-up'],
      });

      const templatePortal = new TemplatePortal(
        this.triggerColumnCustomizationTemplateRef,
        this._viewContainerRef
      );

      this.overlayRef.attach(templatePortal);

      this.overlayRef.backdropClick().subscribe(() => {
        this.closeOverlay();
      });
    }
  }

  /**
   * To close the Overlay
   */
  closeOverlay() {
    this.overlayRef?.dispose();
  }

  /**
   * @description On Apply Column Customization
   * @param {array} data
   */
  async onApplyColumnCustomization(data) {
    let requestType = this.isPendingRequestSelected
      ? '-pending-request-'
      : '-previous-request-';

    this.currentColumnConfig = [...data];
    localStorage.setItem(
      this.currentSelectedApplicationId +
        (this.currentSelectedApprovalType || '') +
        requestType +
        '-inbox-list-view-customization-' +
        this.profile.aid,
      JSON.stringify(this.currentColumnConfig)
    );
    this.closeOverlay();

    this.calculateDynamicContentHeight();
  }

  /**
   * @description On stop propagation
   */
  stopParentPropagation(event: MouseEvent) {
    if (event) {
      event.stopPropagation();
    }
  }

  /**
   * @description On Single Toggle Changes
   */
  onIndividualCheckboxSelected() {
    if (this.currentData && this.currentData.length > 0) {
      if (this.pendingCheckAll) {
        let unselectedCount = this.currentData.filter(
          (obj) => obj.isChecked == false
        );
        this.countSelected =
          this.applications[this.currentSelectedApplicationIndex]['count'] -
          unselectedCount.length;
      } else {
        this.countSelected = this.currentData.filter(
          (obj) => obj.isChecked == true
        ).length;
      }

      let val1 = this.currentData.filter((obj) => obj.isChecked == true);
      this.selectedDetails = val1;
      let val2 = this.currentData.filter((obj) => obj.isChecked == false);
      this.unselectedDetails = val2;

      if (!this.countSelected || this.countSelected == 0) {
        this.pendingCheckAll = false;
      }
    }
  }

  /**
   * @description On change select all toggle
   */
  onChangePendingCheckAll() {
    this.currentData.forEach((obj) => (obj.isChecked = this.pendingCheckAll));
    this.onIndividualCheckboxSelected();
  }

  /**
   * @description Open home screen dashboard
   */
  openHomeScreen() {
    this._router.navigateByUrl('/main/home/<USER>');
  }

  /**
   * @description Open home screen inbox
   */
  openApprovalsScreen() {
    this._router.navigateByUrl('/main/home/<USER>');
  }

  /**
   * @description Update isSelected flag
   * @param {number} index
   */
  async updateIsSelectedApplication(index) {
    this.applications.forEach((obj) => {
      obj['isSelected'] = false;
    });

    this.applications[index]['isSelected'] = true;
    this.currentSelectedApplicationIndex = index;
    this.currentSelectedApplicationId =
      this.applications[index]['application_id'];
    this.currentSelectedApprovalType =
      this.applications[index]['approval_type'];

    this._router.navigate([], {
      relativeTo: this._activatedRoute,
      queryParams: {
        applicationId: this.applications[index]['application_id'],
        approvalType: this.applications[index]['approval_type'] || null,
      },
      queryParamsHandling: 'merge',
    });

    this.currentColumnConfig = this.isPendingRequestSelected
      ? this.applications[index]['pending_request_list_view_configurations'] ||
        []
      : this.applications[index]['previous_request_list_view_configurations'] ||
        [];
    this.currentFilterSubApplicationId = this.isPendingRequestSelected
      ? this.applications[index]['pending_request_filter_sub_application_id'] ||
        ''
      : this.applications[index][
          'previous_request_filter_sub_application_id'
        ] || '';

    this.initializeLocalStorageData();

    this.filterSubscription$ = this._filterService
      .getFilterConfig(
        this.currentSelectedApplicationId,
        this.currentFilterSubApplicationId
      )
      .subscribe((filterList) => {
        this.applyFilter();
      });

    this.calculateDynamicContentHeight();
  }

  /**
   * @description Switch between applications
   * @param {number} index
   */
  async switchApplication(index) {
    if (this.currentSelectedApplicationIndex == index) {
      return;
    }

    this.isPendingRequestSelected = true;
    this.pendingCheckAll = false;
    this.countSelected = 0;
    this.currentData = [];
    this.skip = 0;
    this.searchParams = '';
    this.isSearchBarVisible = false;

    await this.updateIsSelectedApplication(index);

    this._router.navigate([], {
      relativeTo: this._activatedRoute,
      queryParams: {
        applicationId: this.applications[index]['application_id'],
        approvalType: this.applications[index]['approval_type'] || null,
      },
      queryParamsHandling: 'merge',
    });
  }

  /**
   * @description Switch to pending requests
   */
  async switchToPendingRequest() {
    if (this.isPendingRequestSelected) {
      return;
    }
    this.isPendingRequestSelected = true;
    this.isSearchBarVisible = false;
    this.pendingCheckAll = false;
    this.countSelected = 0;
    this.currentData = [];
    this.skip = 0;
    this.searchParams = '';

    await this.updateIsSelectedApplication(
      this.currentSelectedApplicationIndex
    );
  }

  /**
   * @description Switch to previous requests
   */
  async switchToPreviousRequest() {
    if (!this.isPendingRequestSelected) {
      return;
    }
    this.isPendingRequestSelected = false;
    this.isSearchBarVisible = false;
    this.pendingCheckAll = false;
    this.countSelected = 0;
    this.currentData = [];
    this.skip = 0;
    this.searchParams = '';

    await this.updateIsSelectedApplication(
      this.currentSelectedApplicationIndex
    );
  }

  /**
   * @description On click search icon
   */
  onClickSearchIcon() {
    this.isSearchBarVisible = true;
    setTimeout(() => {
      const inputElement = document.getElementById(
        'inputSearchField'
      ) as HTMLInputElement;
      inputElement.focus();
    }, 100);
  }

  /**
   * @description Open Hyperlink text in New Tab
   * @param link
   */
  openHyperLink(link) {
    if (link && link != '' && typeof link == 'string') {
      window.open(link);
    } else {
      this._toaster.showWarning(
        'Warning ⚠️',
        'Invalid URL or No URL Found!',
        7000
      );
    }
  }

  /**
   * @description Click action to redirect
   * @param {object} action
   * @param {object} data
   * @param {string} url
   */
  columnClickAction(action, data, url) {
    if (action) {
      if (action?.action_type == 'hyperlink') {
        this.openHyperLink(url);
      } else if (action?.action_type == 'function') {
        this[action?.function](action, data);
      }
    }
  }

  /**
   * @description Click action to redirect
   * @param {object} action
   * @param {object} data
   */
  async openProjectsDetailsPage(action, data) {
    console.log(data)
    if (data?.type == 'hyperlink') {
      this.openHyperLink(data?.url); 
    } else if (data?.type == 'dialog') {
      const { ApprovalsDetailViewComponent } = await import('../../../../project-management/shared-lazy-loaded/components/approvals-detail-view/approvals-detail-view.component');
      const dialogRef = this._dialog.open(ApprovalsDetailViewComponent, {
        height: '100%',
        minWidth: '50%',
        position: { right: '0px' },
        data: { formConfig: null, details: data },
      });
      dialogRef.afterClosed().subscribe(async (result:any)=>{
        if (result) {
          this.getRequestDataDynamically(true);
        }
      })
    }
  }

  /**
   * @description On click action icon
   * @param {object} data
   * @param {string} type
   * @param {string} apiURL
   */
  onClickActionsIcon(
    data,
    type,
    apiURL,
    fileName,
    listIndex,
    fieldIndex,
    iconIndex
  ) {
    if (type == 'api') {
      if (!(data && apiURL != '' && !apiURL)) {
        this._toaster.showWarning(
          'Warning ⚠️',
          'API Configurations Missing!',
          7000
        );
        return;
      }
      return new Promise((resolve, reject) =>
        this._inboxService
          .onClickActionsIcon(data, apiURL)
          .pipe(takeUntil(this._onDestroy))
          .subscribe({
            next: (res) => {
              if (res['err'] == false) {
                this._toaster.showSuccess('Success ✅', res['msg'], 7000);
              } else {
                this._toaster.showError('Error', res['msg'], 7000);
              }
              resolve(true);
            },
            error: (err) => {
              this._toaster.showError(
                'Error',
                err.msg ? err.msg : 'API Failed!',
                7000
              );
              reject();
            },
          })
      );
    } else if (type == 'hyperlink') {
      let link = data[apiURL];
      this.openHyperLink(link);
    } else if (type == 'attachment_download') {
      if (!apiURL) {
        this._toaster.showWarning(
          'Warning ⚠️',
          'API Configurations Missing!',
          7000
        );
        return;
      }
      this.currentData[listIndex][
        `${this.currentColumnConfig[fieldIndex].id}_${iconIndex}_loader`
      ] = true;
      fileName = eval(fileName);
      this._inboxService
        .onClickDownloadAttachments(data, apiURL)
        .pipe(takeUntil(this._onDestroy))
        .subscribe(
          (blob) => {
            const url = window.URL.createObjectURL(blob);
            const downloadLink = document.createElement('a');
            downloadLink.href = url;
            downloadLink.download = fileName;
            document.body.appendChild(downloadLink);
            downloadLink.click();
            document.body.removeChild(downloadLink);
            window.URL.revokeObjectURL(url);
            this.currentData[listIndex][
              `${this.currentColumnConfig[fieldIndex].id}_${iconIndex}_loader`
            ] = false;
          },
          (error) => {
            this.currentData[listIndex][
              `${this.currentColumnConfig[fieldIndex].id}_${iconIndex}_loader`
            ] = false;
            this._toaster.showWarning(
              'Warning ⚠️',
              'No Attachments Found!',
              7000
            );
          }
        );
    }
  }

  /**
   * @description On approve single item
   * @param {number} index
   */
  async onSingleDataApprove(index) {
    if (this.applications[this.currentSelectedApplicationIndex]['people_allocation_approve_flag']) {
      const openModalComponent = this._dialog.open(InboxAcceptDialogComponent, {
      width: '800px',
      disableClose: true,
      data: {
        data:
          this.applications[this.currentSelectedApplicationIndex][
            'rejection_comments'
          ] || [],
      },
    });

    openModalComponent.afterClosed().subscribe((result) => {
      if (result) {
        let payload = {
          aid: this.profile.aid,
          oid: this.profile.oid,
          selectedDetails: [this.currentData[index]],
          status: 'R',
          comments: result,
          isRejectApi: true,
        };

        this.isRejectApiInProgress = true;

        return new Promise((resolve, reject) => {
          this._inboxService
            .getDataDynamically(apiUrl, payload)
            .pipe(takeUntil(this._onDestroy))
            .subscribe({
              next: (res: any) => {
                if (responseType && responseType == 'messType') {
                  if (res['messType'] == 'S') {
                    if (this.applications[this.currentSelectedApplicationIndex]['refresh_page']) {
                      this.updateIsSelectedApplication(this.currentSelectedApplicationIndex);
                    } else {
                      this.currentData.splice(index, 1);
                      this.applications[this.currentSelectedApplicationIndex][
                        'count'
                      ] -= 1;
                    }
                    this._toaster.showSuccess(
                      'Success ✅',
                      res['messText'],
                      7000
                    );
                  } else {
                    this._toaster.showError('Error', res['messText'], 7000);
                  }
                } else if (responseType && responseType == 'err') {
                  if (res['err'] == false) {
                    if (this.applications[this.currentSelectedApplicationIndex]['refresh_page']) {
                      this.updateIsSelectedApplication(this.currentSelectedApplicationIndex);
                    } else {
                      this.currentData.splice(index, 1);
                      this.applications[this.currentSelectedApplicationIndex][
                        'count'
                      ] -= 1;
                    } 
                    this._toaster.showSuccess('Success ✅', res['msg'], 7000);
                  } else {
                    this._toaster.showError('Error', res['msg'], 7000);
                  }
                }
                this.isRejectApiInProgress = false;
                resolve(true);
              },
              error: (err) => {
                this._toaster.showError('Error', 'Error in Reject API', 7000);
                this.isRejectApiInProgress = false;
                reject();
              },
            });
        });
      }
    });
      return;
    }

    let apiUrl =
      this.applications[this.currentSelectedApplicationIndex][
        'api_configurations'
      ]['approve_api'];

    let responseType =
      this.applications[this.currentSelectedApplicationIndex][
        'api_response_type'
      ];

    if (!apiUrl || !responseType) {
      return this._toaster.showError(
        'Error',
        'API Configurations Not Found!',
        7000
      );
    }

    let payload = {
      aid: this.profile.aid,
      oid: this.profile.oid,
      selectedDetails: [this.currentData[index]],
      status: 'A',
      isApproveApi: true,
    };

    this.isApproveApiInProgress = true;

    return new Promise((resolve, reject) => {
      this._inboxService
        .getDataDynamically(apiUrl, payload)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res: any) => {
            if (responseType && responseType == 'messType') {
              if (res['messType'] == 'S') {
                this.currentData.splice(index, 1);
                this.applications[this.currentSelectedApplicationIndex][
                  'count'
                ] -= 1;
                this._toaster.showSuccess('Success ✅', res['messText'], 7000);
              } else {
                this._toaster.showError('Error', res['messText'], 7000);
              }
            } else if (responseType && responseType == 'err') {
              if (res['err'] == false) {
                this.currentData.splice(index, 1);
                this.applications[this.currentSelectedApplicationIndex][
                  'count'
                ] -= 1;
                this._toaster.showSuccess('Success ✅', res['msg'], 7000);
              } else {
                this._toaster.showError('Error', res['msg'], 7000);
              }
            }
            this.isApproveApiInProgress = false;
            resolve(true);
          },
          error: (err) => {
            this._toaster.showError('Error', 'Error in Approve API', 7000);
            this.isApproveApiInProgress = false;
            reject();
          },
        });
    });
  }

  /**
   * @description On reject single item
   * @param {number} index
   */
  async onSingleDataReject(index) {
    let apiUrl =
      this.applications[this.currentSelectedApplicationIndex][
        'api_configurations'
      ]['reject_api'];

    let responseType =
      this.applications[this.currentSelectedApplicationIndex][
        'api_response_type'
      ];

    if (!apiUrl || !responseType) {
      return this._toaster.showError(
        'Error',
        'API Configurations Not Found!',
        7000
      );
    }

    const { InboxRejectDialogComponent } = await import(
      'src/app/modules/kebs-home/inbox/components/inbox-reject-dialog/inbox-reject-dialog.component'
    );
    const openModalComponent = this._dialog.open(InboxRejectDialogComponent, {
      width: '800px',
      disableClose: true,
      data: {
        data:
          this.applications[this.currentSelectedApplicationIndex][
            'rejection_comments'
          ] || [],
      },
    });

    openModalComponent.afterClosed().subscribe((result) => {
      if (result) {
        let payload = {
          aid: this.profile.aid,
          oid: this.profile.oid,
          selectedDetails: [this.currentData[index]],
          status: 'R',
          comments: result,
          isRejectApi: true,
        };

        this.isRejectApiInProgress = true;

        return new Promise((resolve, reject) => {
          this._inboxService
            .getDataDynamically(apiUrl, payload)
            .pipe(takeUntil(this._onDestroy))
            .subscribe({
              next: (res: any) => {
                if (responseType && responseType == 'messType') {
                  if (res['messType'] == 'S') {
                    this.currentData.splice(index, 1);
                    this.applications[this.currentSelectedApplicationIndex][
                      'count'
                    ] -= 1;
                    this._toaster.showSuccess(
                      'Success ✅',
                      res['messText'],
                      7000
                    );
                  } else {
                    this._toaster.showError('Error', res['messText'], 7000);
                  }
                } else if (responseType && responseType == 'err') {
                  if (res['err'] == false) {
                    this.currentData.splice(index, 1);
                    this.applications[this.currentSelectedApplicationIndex][
                      'count'
                    ] -= 1;
                    this._toaster.showSuccess('Success ✅', res['msg'], 7000);
                  } else {
                    this._toaster.showError('Error', res['msg'], 7000);
                  }
                }
                this.isRejectApiInProgress = false;
                resolve(true);
              },
              error: (err) => {
                this._toaster.showError('Error', 'Error in Reject API', 7000);
                this.isRejectApiInProgress = false;
                reject();
              },
            });
        });
      }
    });
  }

  /**
   * @description On Bulk Approve
   */
  async onBulkApprove() {
    let apiUrl =
      this.applications[this.currentSelectedApplicationIndex][
        'api_configurations'
      ]['bulk_approve_api'];

    if (!apiUrl) {
      return this._toaster.showError(
        'Error',
        'API Configurations Not Found!',
        7000
      );
    }

    let val1 = this.currentData.filter((obj) => obj.isChecked == true);
    this.selectedDetails = val1;
    let val2 = this.currentData.filter((obj) => obj.isChecked == false);
    this.unselectedDetails = val2;

    this._toaster.showSuccess(
      'Success ✅',
      'Selected Request(s) has been sent for Approval Process. All the Requests will be Approved in sometime!',
      7000
    );

    let payload = {
      aid: this.profile.aid,
      oid: this.profile.oid,
      selectedDetails: this.selectedDetails,
      unselectedDetails: this.unselectedDetails,
      bulkApprove: this.pendingCheckAll,
      searchParams: this.searchParams,
      filter: this.appliedFilter,
      filterQuery: this.filterQuery,
      status: 'A',
      isApproveApi: true,
    };

    this.applications[this.currentSelectedApplicationIndex]['count'] =
      this.applications[this.currentSelectedApplicationIndex]['count'] -
      this.countSelected;
    this.countSelected = 0;

    this.currentData = this.currentData.filter(
      (obj) => obj.isChecked === false
    );
    this.pendingCheckAll = false;

    return new Promise((resolve, reject) => {
      this._inboxService
        .getDataDynamically(apiUrl, payload)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res: any) => {
            resolve(true);
          },
          error: (err) => {
            this._toaster.showError('Error', 'Error in Bulk Approve API', 7000);
            reject();
          },
        });
    });
  }

  /**
   * @description On reject approved item
   * @param {number} index
   */
  async onRejectApprovedItem(index) {
    let apiUrl =
      this.applications[this.currentSelectedApplicationIndex][
        'api_configurations'
      ]['reject_approved_item_api'];

    let responseType =
      this.applications[this.currentSelectedApplicationIndex][
        'api_response_type'
      ];

    if (!apiUrl || !responseType) {
      return this._toaster.showError(
        'Error',
        'API Configurations Not Found!',
        7000
      );
    }

    const { InboxRejectDialogComponent } = await import(
      'src/app/modules/kebs-home/inbox/components/inbox-reject-dialog/inbox-reject-dialog.component'
    );
    const openModalComponent = this._dialog.open(InboxRejectDialogComponent, {
      width: '800px',
      disableClose: true,
      data: {
        data:
          this.applications[this.currentSelectedApplicationIndex][
            'rejection_comments'
          ] || [],
      },
    });

    openModalComponent.afterClosed().subscribe((result) => {
      if (result) {
        let payload = {
          aid: this.profile.aid,
          oid: this.profile.oid,
          comments: [result],
          status: 'R',
          approvedItemDetails: [this.currentData[index]],
          statusId: 3,
        };

        this.isRejectApprovedDataApiInProgress = true;

        return new Promise((resolve, reject) => {
          this._inboxService
            .getDataDynamically(apiUrl, payload)
            .pipe(takeUntil(this._onDestroy))
            .subscribe({
              next: (res: any) => {
                if (responseType && responseType == 'messType') {
                  if (res['messType'] == 'S') {
                    this.getRequestDataDynamically(true);
                    this._toaster.showSuccess(
                      'Success ✅',
                      res['messText'],
                      7000
                    );
                  } else {
                    this._toaster.showError('Error', res['messText'], 7000);
                  }
                } else if (responseType && responseType == 'err') {
                  if (res['err'] == false) {
                    this.getRequestDataDynamically(true);
                    this._toaster.showSuccess('Success ✅', res['msg'], 7000);
                  } else {
                    this._toaster.showError('Error', res['msg'], 7000);
                  }
                }
                this.isRejectApprovedDataApiInProgress = false;
                resolve(true);
              },
              error: (err) => {
                this._toaster.showError('Error', 'Error in Reject API', 7000);
                this.isRejectApprovedDataApiInProgress = false;
                reject();
              },
            });
        });
      }
    });
  }

  /**
   * @description On clear search data
   */
  onClearSearch() {
    this.searchParams = '';
    this.isSearchBarVisible = false;

    this.getRequestDataDynamically(true);
  }

  /**
   * @description On enter search data
   */
  onEnterSearch() {
    this.getRequestDataDynamically(true);
  }

  /**
   * @description To open filter dialog
   */
  async openFilterDialog() {
    this._filterService.openFilterLandingPage(
      this.currentSelectedApplicationId,
      this.currentFilterSubApplicationId,
      this.filterData
    );
  }

  /**
   * @description On click sort
   */
  async onClickSort(sortOrder, fieldConfigIndex) {
    let requestType = this.isPendingRequestSelected
      ? '-pending-request-'
      : '-previous-request-';

    this.currentColumnConfig.forEach((field) => {
      field.sort_order = 0;
    });

    this.currentColumnConfig[fieldConfigIndex]['sort_order'] = sortOrder;

    localStorage.setItem(
      this.currentSelectedApplicationId +
        (this.currentSelectedApprovalType || '') +
        requestType +
        '-inbox-list-view-customization-' +
        this.profile.aid,
      JSON.stringify(this.currentColumnConfig)
    );

    this.getRequestDataDynamically(true);
  }

  /**
   * @description Apply filter and generate query
   */
  async applyFilter() {
    if (this.isListViewLoading) {
      return;
    }

    this.isListViewLoading = true;
    this.filterQuery = '';
    this.appliedFilter = [];

    let filter = await this.getUserFilterConfig();
    if (filter && filter !== '' && filter != null) {
      let filterVal =
        filter && filter['filterConfig'] && filter['filterConfig']['filterData']
          ? filter['filterConfig']['filterData']
          : [];
      let query = this._filterService.generateConditionBasedQuery(filterVal);
      this.appliedFilter = filterVal;
      this.filterQuery = query ? query : '';
    }

    await this.getRequestDataDynamically(true);
  }

  /**
   * @description On scroll data
   */
  async onDataScroll() {
    let isLazyLoaded = this.isPendingRequestSelected
      ? this.applications[this.currentSelectedApplicationIndex][
          'is_api_lazy_loaded'
        ] || false
      : this.applications[this.currentSelectedApplicationIndex][
          'is_api_lazy_loaded'
        ] || false;

    if (!isLazyLoaded) {
      return;
    }

    this.skip += 15;
    await this.getRequestDataDynamically(false);
  }

  /**
   * @description Fetch all pending approvals count
   */
  async fetchAllPendingApprovalsCountData(): Promise<void> {
    let payload = {
      aid: this.profile.aid,
      oid: this.profile.oid,
      isFromInbox: true,
    };

    return new Promise((resolve, reject) => {
      // Array to hold promises for each API call
      const apiRequests = this.applications.map((api, index) => {
        return new Promise((resolveRequest, rejectRequest) => {
          this._inboxService
            .getDataDynamically(
              api?.api_configurations?.pending_request_count_api,
              payload
            )
            .pipe(takeUntil(this._onDestroy))
            .subscribe({
              next: (res: any) => {
                if (!api?.api_response_type) {
                  this.applications[index]['count'] = res || 0;
                } else if (
                  api?.api_response_type &&
                  api?.api_response_type == 'messType'
                ) {
                  if (res['messType'] == 'S') {
                    this.applications[index]['count'] = res['data'] || 0;
                  }
                } else if (
                  api?.api_response_type &&
                  api?.api_response_type == 'err'
                ) {
                  if (res['err'] == false) {
                    this.applications[index]['count'] = res['data'] || 0;
                  }
                }
                resolveRequest(res);
              },
              error: (err) => {
                rejectRequest(err); // Reject the promise if there's an error
              },
            });
        });
      });

      // Use Promise.all to wait for all API requests to resolve
      Promise.all(apiRequests)
        .then((results) => {
          this.isLoading = false;
          resolve();
        })
        .catch((error) => {
          this.isLoading = false;
          this.isListViewLoading = false;
          reject();
        });
    });
  }

  /**
   * @description Gets UI configurations
   * @param {string} key
   */
  async getUiConfiguration(key) {
    return new Promise((resolve, reject) =>
      this._masterService
        .getUiConfiguration(key)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['err'] == false) {
              if (key == 'homePage') {
                this.homePageUiConfig = res['data'];
              }
            } else {
              this._toaster.showError('Error', res['msg'], 7000);
            }
            resolve(true);
          },
          error: (err) => {
            this._toaster.showError(
              'Error',
              err.message ? err.message : 'UI Configuration Retrieval Failed!',
              7000
            );
            reject();
          },
        })
    );
  }

  /**
   * @description Gets Application configurations
   */
  async getInboxApplications() {
    return new Promise((resolve, reject) =>
      this._inboxService
        .getInboxApplications()
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['err'] == false) {
              this.applications = res['data'];
            } else {
              this._toaster.showError('Error', res['msg'], 7000);
            }
            resolve(true);
          },
          error: (err) => {
            this._toaster.showError(
              'Error',
              err.message
                ? err.message
                : 'Application Configuration Retrieval Failed!',
              7000
            );
            this.isLoading = false;
            this.isListViewLoading = false;
            reject();
          },
        })
    );
  }

  /**
   * @description Fetching the User Filter Config
   */
  getUserFilterConfig() {
    if (
      !this.currentFilterSubApplicationId ||
      this.currentFilterSubApplicationId == ''
    ) {
      return;
    }

    return new Promise((resolve, reject) => {
      this._filterService
        .getFilterUserConfig(
          this.currentSelectedApplicationId,
          this.currentFilterSubApplicationId
        )
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res) => {
            if (res['messType'] == 'S') {
              resolve(res['data']);
            } else {
              resolve([]);
            }
          },
          error: (err) => {
            this._toaster.showError(
              'Error',
              'Error in Fetching User Filter Config',
              7000
            );
            resolve([]);
          },
        });
    });
  }

  async getRequestDataDynamically(resetData) {
    if (resetData) {
      this.pendingCheckAll = false;
      this.countSelected = 0;
      this.currentData = [];
      this.skip = 0;
      this.isListViewLoading = true;

      if (this.isPendingRequestSelected) {
        await this.getRequestCountDynamically();
      }
    }

    let apiUrl = this.isPendingRequestSelected
      ? this.applications[this.currentSelectedApplicationIndex][
          'api_configurations'
        ]['pending_request_data_api'] || null
      : this.applications[this.currentSelectedApplicationIndex][
          'api_configurations'
        ]['previous_request_data_api'] || null;

    let sort = this.currentColumnConfig.filter(
      (obj) => obj?.is_sort_active && obj?.sort_order
    );

    let responseType =
      this.applications[this.currentSelectedApplicationIndex][
        'api_response_type'
      ];

    let payload = {
      aid: this.profile.aid,
      oid: this.profile.oid,
      searchParams: this.searchParams,
      filter: this.appliedFilter,
      filterQuery: this.filterQuery,
      sort: sort,
      skip: this.skip,
      limit: this.limit,
    };

    return new Promise((resolve, reject) => {
      this._inboxService
        .getDataDynamically(apiUrl, payload)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res: any) => {
            if (resetData) {
              this.currentData = [];
            }
            if (!responseType) {
              const updatedRes = res.map((item: any) => ({
                ...item,
                isChecked: this.isPendingRequestSelected
                  ? this.pendingCheckAll
                  : false,
              }));
              this.currentData = [...this.currentData, ...updatedRes];
            } else if (responseType && responseType == 'messType') {
              if (res['messType'] == 'S') {
                const updatedRes = res['data'].map((item: any) => ({
                  ...item,
                  isChecked: this.isPendingRequestSelected
                    ? this.pendingCheckAll
                    : false,
                }));
                this.currentData = [...this.currentData, ...updatedRes];
              }
            } else if (responseType && responseType == 'err') {
              if (res['err'] == false) {
                const updatedRes = res['data'].map((item: any) => ({
                  ...item,
                  isChecked: this.isPendingRequestSelected
                    ? this.pendingCheckAll
                    : false,
                }));
                this.currentData = [...this.currentData, ...updatedRes];
              }
            }
            this.isListViewLoading = false;
            resolve(true);
          },
          error: (err) => {
            this._toaster.showError('Error', 'Error in Request Data', 7000);
            this.isListViewLoading = false;
            reject();
          },
        });
    });
  }

  async getRequestCountDynamically() {
    let apiUrl = this.isPendingRequestSelected
      ? this.applications[this.currentSelectedApplicationIndex][
          'api_configurations'
        ]['pending_request_count_api'] || null
      : this.applications[this.currentSelectedApplicationIndex][
          'api_configurations'
        ]['pending_request_count_api'] || null;
    let responseType =
      this.applications[this.currentSelectedApplicationIndex][
        'api_response_type'
      ];

    let payload = {
      aid: this.profile.aid,
      oid: this.profile.oid,
      searchParams: this.searchParams,
      filter: this.appliedFilter,
      filterQuery: this.filterQuery,
    };

    return new Promise((resolve, reject) => {
      this._inboxService
        .getDataDynamically(apiUrl, payload)
        .pipe(takeUntil(this._onDestroy))
        .subscribe({
          next: (res: any) => {
            if (!responseType) {
              this.applications[this.currentSelectedApplicationIndex]['count'] =
                res || 0;
            } else if (responseType && responseType == 'messType') {
              if (res['messType'] == 'S') {
                this.applications[this.currentSelectedApplicationIndex][
                  'count'
                ] = res['data'] || 0;
              }
            } else if (responseType && responseType == 'err') {
              if (res['err'] == false) {
                this.applications[this.currentSelectedApplicationIndex][
                  'count'
                ] = res['data'] || 0;
              }
            }
            resolve(true);
          },
          error: (err) => {
            this._toaster.showError(
              'Error',
              'Error in Fetching Inbox Data',
              7000
            );
            this.isListViewLoading = false;
            reject();
          },
        });
    });
  }
  getApprovalPendingDetailsDetails(data){
    //  this.loaderObject.isFormSubmitLoading = true;
      // let req = {
      //   "associate_id": this.associateId,
      // }
      return new Promise((resolve, reject) => {
        this.subs.sink = this._edService
          .getPendingApprovalsForEmployeeAID(data,'skill_details')
          .subscribe(
            (res: any) => {
              if(!res['err']){
              //this.loaderObject.isFormSubmitLoading = false;
              resolve(res.approval_list);
              }
            },
            (err) => {
              //this.loaderObject.isFormSubmitLoading = false;
              this._toaster.showError(
                'Error',
                'Failed to Update Skill Validation!',
                2000
              );
              console.log(err);
              reject(err);
            }
          );
      });
    }
    async openApprovalPendingDialog(action,data){
      console.log(data)
      let modalParams
      if(this.isPendingRequestSelected){
      this.approvalPendings = await this.getApprovalPendingDetailsDetails(data.submitted_by_aid);
      console.log(data.submitted_by_aid)
      
     modalParams = {
        associateId: parseInt(data.submitted_by_aid),
        approvalPendings : this.approvalPendings[0]?.submission_item?.approval_fields,
        pendingMetaData : this.approvalPendings[0]
      };}
      else{
        modalParams = {
          associateId: parseInt(data.submitted_by_aid),
          approvalPendings : data?.submission_item?.approval_fields,
          pendingMetaData : data,
          showlength:false

        }

      }
      const { ApprovalPendingDetailScreenComponent } = await import(
        'src/app/modules/employee-directory/shared-components/approval-pending-detail-screen/approval-pending-detail-screen.component'
      );
     const approvalPendingDialogCompnent = this._dialog.open(ApprovalPendingDetailScreenComponent, {
        width: '700px',
        panelClass: 'e360-approval-pending-dialog-box',
        autoFocus: false,
        // minHeight: 'calc(100vh - 90px)',
       // height : '300px',
        data: { modalParams: modalParams },
      });
  
      approvalPendingDialogCompnent.afterClosed().subscribe(
        (res) => {
          // if (res == 'success') {
          //   this.isProfileValidated = true;
          // }
        },
        (err) => {
          this._toaster.showError('Error', 'Failed to retrieve employee !', 2000);
        }
      );
    }
}
