(window.webpackJsonp=window.webpackJsonp||[]).push([[931],{U1l8:function(e,t,n){"use strict";n.r(t),n.d(t,"CreateUserComponent",(function(){return O}));var r=n("mrSG"),i=n("3Pt+"),o=n("0IaG"),a=n("XNiG"),s=n("1G5W"),l=n("fXoL"),d=n("rQiX"),c=n("XNFG"),m=n("XQl4"),p=n("tyNb"),g=n("ofXK"),f=n("NFeN"),h=n("kmnG"),u=n("qFsG"),v=n("TmG/"),x=n("su5B");function C(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div"),l["\u0275\u0275elementStart"](1,"mat-form-field",18),l["\u0275\u0275element"](2,"input",26),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"]().$implicit;l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("placeholder",e.placeholder)("formControlName",e.key)}}function w(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div"),l["\u0275\u0275element"](1,"app-input-search",27),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"]().$implicit,t=l["\u0275\u0275nextContext"](2);l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("list",t.roleList)("disableNone",!0)("placeholder",e.placeholder)("formControlName",e.key)("hideMatLabel",!0)}}const y=function(e){return{fieldKey:e}};function b(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div"),l["\u0275\u0275elementStart"](1,"app-multi-select-chip",28),l["\u0275\u0275listener"]("onValueChange",(function(t){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"](3).onMultiSelectChipChanges(t)})),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&e){const e=l["\u0275\u0275nextContext"]().$implicit,t=l["\u0275\u0275nextContext"](2);l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("type",2)("placeholder",e.placeholder)("masterData",t.roleList)("selectedValues",t.userCreateForm.get(e.key).value)("data",l["\u0275\u0275pureFunction1"](5,y,e.key))}}function _(e,t){if(1&e&&(l["\u0275\u0275elementContainerStart"](0),l["\u0275\u0275elementStart"](1,"div"),l["\u0275\u0275elementStart"](2,"div",24),l["\u0275\u0275elementStart"](3,"div",16),l["\u0275\u0275text"](4),l["\u0275\u0275elementStart"](5,"span"),l["\u0275\u0275elementStart"](6,"sup",25),l["\u0275\u0275text"](7,"*"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](8,C,3,2,"div",0),l["\u0275\u0275template"](9,w,2,5,"div",0),l["\u0275\u0275template"](10,b,2,7,"div",0),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;l["\u0275\u0275advance"](1),l["\u0275\u0275classMapInterpolate1"]("col-",e.col," over-all-filed "),l["\u0275\u0275advance"](3),l["\u0275\u0275textInterpolate1"](" ",e.label," "),l["\u0275\u0275advance"](4),l["\u0275\u0275property"]("ngIf","text"===e.fieldType||"email"===e.fieldType||"phonenumber"===e.fieldType),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf","single-select"===e.fieldType),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf","multi-select"===e.fieldType)}}function E(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div"),l["\u0275\u0275elementStart"](1,"div",1),l["\u0275\u0275elementStart"](2,"div",2),l["\u0275\u0275elementStart"](3,"div",3),l["\u0275\u0275text"](4,"New User"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](5,"div",4),l["\u0275\u0275element"](6,"img",5),l["\u0275\u0275elementStart"](7,"div",6),l["\u0275\u0275elementStart"](8,"mat-icon",7),l["\u0275\u0275listener"]("click",(function(){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"]().closeOverlay()})),l["\u0275\u0275text"](9," close "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](10,"div",8),l["\u0275\u0275elementStart"](11,"div"),l["\u0275\u0275elementStart"](12,"div",9),l["\u0275\u0275text"](13,"Basic Information"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](14,"div",10),l["\u0275\u0275elementStart"](15,"form",11),l["\u0275\u0275elementStart"](16,"div",12),l["\u0275\u0275template"](17,_,11,7,"ng-container",13),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](18,"div"),l["\u0275\u0275elementStart"](19,"div",14),l["\u0275\u0275text"](20,"Password management"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](21,"div",15),l["\u0275\u0275elementStart"](22,"div",16),l["\u0275\u0275text"](23,"Password"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](24,"div",17),l["\u0275\u0275elementStart"](25,"mat-form-field",18),l["\u0275\u0275element"](26,"input",19),l["\u0275\u0275elementStart"](27,"mat-icon",20),l["\u0275\u0275listener"]("click",(function(){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"]().togglePasswordVisibility()})),l["\u0275\u0275text"](28),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](29,"div",21),l["\u0275\u0275elementStart"](30,"div",22),l["\u0275\u0275listener"]("click",(function(){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"]().onClickCancel()})),l["\u0275\u0275text"](31," Cancel "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](32,"div",23),l["\u0275\u0275listener"]("click",(function(){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"]().onClickSave()})),l["\u0275\u0275text"](33," Create User "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&e){const e=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](15),l["\u0275\u0275property"]("formGroup",e.userCreateForm),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngForOf",e.formFields),l["\u0275\u0275advance"](9),l["\u0275\u0275property"]("type",e.showPassword?"text":"password")("readonly",!0),l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate1"](" ",e.showPassword?"visibility_off":"visibility"," ")}}function S(e,t){if(1&e&&(l["\u0275\u0275elementContainerStart"](0),l["\u0275\u0275elementStart"](1,"div",29),l["\u0275\u0275elementStart"](2,"div",30),l["\u0275\u0275element"](3,"img",31),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](4,"div",32),l["\u0275\u0275elementStart"](5,"div",33),l["\u0275\u0275text"](6,"Loading..."),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementContainerEnd"]()),2&e){const e=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("src",e.uiTextConfig["UI-LOADING-001"],l["\u0275\u0275sanitizeUrl"])}}let O=(()=>{class e{constructor(e,t,n,r,i,o,s){this.dialogRef=e,this._atsMasterService=t,this._toaster=n,this._fb=r,this._settingService=i,this._activatedRoute=o,this.data=s,this._onDestroy=new a.b,this.formFields=[],this.isLoading=!0,this.uiTextConfig={},this.showPassword=!1,this.spinnerText="Loading..",this.roleList=[]}ngOnInit(){return Object(r.c)(this,void 0,void 0,(function*(){yield this.getAtsMasterUiConfig("usersettingconfig"),yield this.getAllRole(),yield this.getAtsFormsConfig("userSettingCreateForm"),this.userCreateForm=this._fb.group({}),this.createForm(),yield this.generatePassword(),this.isLoading=!1}))}onMultiSelectChipChanges(e){var t,n;null===(n=null===(t=this.userCreateForm)||void 0===t?void 0:t.get(e.data.fieldKey))||void 0===n||n.setValue(e.val)}createForm(){this.userCreateForm.addControl("password",this._fb.control(this.password)),this.formFields.forEach(e=>{this.userCreateForm.addControl(e.key,this._fb.control(null,[e.isMandatory?i.H.required:null,"user_name"===e.key?i.H.pattern(/^[a-zA-Z ]+$/):null,"phonenumber"===e.fieldType?i.H.pattern(/^\d{10}$/):null,"email"==e.fieldType?i.H.pattern(/^[a-zA-Z0-9._%+-]+@(gmail|yahoo|outlook)\.(com|in)$/):null].filter(e=>null!==e)))})}getAtsMasterUiConfig(e){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._atsMasterService.getAtsMasterUiConfig(e).pipe(Object(s.a)(this._onDestroy)).subscribe({next:n=>{0==n.err?"usersettingconfig"==e&&(this.uiTextConfig=n.data.uiTextConfig):this._toaster.showError("Error",n.msg,7e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Master Data Retrieval Failed!",7e3),n()}}))}))}getAllRole(){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getAllRole().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.roleList=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Master Data Retrieval Failed!",7e3),t()}}))}))}onClickSave(){return Object(r.c)(this,void 0,void 0,(function*(){if(this.userCreateForm.valid){this.isLoading=!0;const e=this.userCreateForm.value;yield this.createUser(e,this.hashedPassword),this.dialogRef.close(!0),this.isLoading=!1}else Object.keys(this.userCreateForm.controls).forEach(e=>{const t=(e.charAt(0).toUpperCase()+e.slice(1)).replace(/_/g," "),n=this.userCreateForm.get(e);n.errors&&(n.errors.required?this._toaster.showError("Warning",t+" is Mandatory",7e3):n.errors.pattern&&this._toaster.showError("Warning","Invalid "+t,7e3))})}))}onClickCancel(){this.dialogRef.close(!1)}createUser(e,t){return Object(r.c)(this,void 0,void 0,(function*(){let n=window.location.host;return new Promise((r,i)=>this._settingService.createUser(e,t,n).pipe(Object(s.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?(this._toaster.showSuccess("Success",e.msg,7e3),r(!0)):(this._toaster.showError("Error",e.msg,7e3),r(!1))},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS User Master Data Retrieval Failed!",7e3),i()}}))}))}closeOverlay(){this.dialogRef.close(!1)}togglePasswordVisibility(){this.showPassword=!this.showPassword}getAtsFormsConfig(e){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._atsMasterService.getAtsFormsConfig(e).pipe(Object(s.a)(this._onDestroy)).subscribe({next:n=>{0==n.err?"userSettingCreateForm"==e&&(this.formFields=n.data.form[0].formFields):this._toaster.showError("Error",n.msg,7e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Forms Configuration Retrieval Failed!",7e3),n()}}))}))}generatePassword(){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._settingService.generatePassword().pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?t.raw&&(this.userCreateForm.get("password").setValue(t.raw),this.password=t.raw,this.hashedPassword=t.hashed):this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Forms Configuration Retrieval Failed!",7e3),t()}}))}))}}return e.\u0275fac=function(t){return new(t||e)(l["\u0275\u0275directiveInject"](o.h),l["\u0275\u0275directiveInject"](d.a),l["\u0275\u0275directiveInject"](c.a),l["\u0275\u0275directiveInject"](i.i),l["\u0275\u0275directiveInject"](m.a),l["\u0275\u0275directiveInject"](p.a),l["\u0275\u0275directiveInject"](o.a))},e.\u0275cmp=l["\u0275\u0275defineComponent"]({type:e,selectors:[["app-create-user"]],decls:2,vars:2,consts:[[4,"ngIf"],[1,"header"],[1,"align-items-column"],[1,"title-text"],[1,"hearder-column-two"],["src","https://assets.kebs.app/ats-kebs-header-logo.png",1,"bg-image"],[1,"close-btn"],[1,"material-symbols-outlined","close-icon",3,"click"],[1,"d-flex","flex-column","container-box"],[1,"title-user-information"],[1,"userdetail-container"],[3,"formGroup"],[1,"row"],[4,"ngFor","ngForOf"],[1,"title-user-password"],[1,"d-flex","flex-column","password-overall"],[1,"form-label"],[1,"col-6","password-input-wrapper"],["appearance","outline","color","warn",1,"mat-form-field"],["matInput","","formControlName","password",3,"type","readonly"],[1,"visibility",3,"click"],[1,"buttons"],["type","button",1,"cancel-button",3,"click"],["type","submit",1,"save-button",3,"click"],[1,"d-flex","flex-column"],[1,"required-field"],["matInput","",3,"placeholder","formControlName"],[1,"userDetailDropDown",3,"list","disableNone","placeholder","formControlName","hideMatLabel"],[3,"type","placeholder","masterData","selectedValues","data","onValueChange"],[1,"loading-img"],[2,"max-width","60px","max-height","60px"],[2,"max-width","100%","max-height","100%",3,"src"],[1,"loading-wrapper"],[1,"loading"]],template:function(e,t){1&e&&(l["\u0275\u0275template"](0,E,34,5,"div",0),l["\u0275\u0275template"](1,S,7,1,"ng-container",0)),2&e&&(l["\u0275\u0275property"]("ngIf",!t.isLoading),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",t.isLoading))},directives:[g.NgIf,f.a,i.J,i.w,i.n,g.NgForOf,h.c,u.b,i.e,i.v,i.l,v.a,x.a],styles:['.title-user-information[_ngcontent-%COMP%]{padding-left:20px}.title-user-information[_ngcontent-%COMP%], .title-user-password[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:700;line-height:18.23px;color:var(--atsprimaryColor);padding-top:15px}.userdetail-container[_ngcontent-%COMP%]{padding:8px 20px 20px}.password-input-wrapper[_ngcontent-%COMP%]{position:relative;padding:0}.mat-icon.visibility[_ngcontent-%COMP%]{cursor:pointer;position:absolute;right:10px;top:50%;transform:translateY(-50%);color:rgba(0,0,0,.54)}.mat-icon.visibility[_ngcontent-%COMP%]:hover{color:rgba(0,0,0,.87)}.password-overall[_ngcontent-%COMP%]{padding:15px}.container-box[_ngcontent-%COMP%]{position:relative;height:487px}.password-management[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:700;line-height:18.23px;color:#79ba44;padding-left:14px;padding-top:15px}.buttons[_ngcontent-%COMP%]{display:flex;gap:20px;bottom:13px;position:absolute;right:20px;padding:20px}.cancel-button[_ngcontent-%COMP%]{text-align:left;color:#45546e;width:64px;border-radius:5px;border:1px solid #45546e}.cancel-button[_ngcontent-%COMP%], .save-button[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:700;line-height:16px;letter-spacing:-.02em;height:40px;padding:10px;justify-content:center;align-items:center;display:flex;cursor:pointer}.save-button[_ngcontent-%COMP%]{width:100px;border-radius:5px;color:#fff;background:linear-gradient(270deg,var(--atsprimaryColor),var(--atssecondaryColor) 105.29%)}.header[_ngcontent-%COMP%]{display:flex;flex-direction:row;align-items:center;justify-content:space-between;height:52px;background-color:#f4f4f6;padding-left:24px;border-bottom:1px solid #e8e9ee}.align-items-column[_ngcontent-%COMP%]{display:flex;flex-direction:column}.title-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:20px;font-weight:700;color:#111434;line-height:20px}.loading-img[_ngcontent-%COMP%]{height:100%}.required-field[_ngcontent-%COMP%]{color:red;font-size:15px;top:0}input[_ngcontent-%COMP%]::placeholder{font-family:var(--atsfontFamily);font-size:12px;font-weight:200;line-height:16px;letter-spacing:.02em;color:#45546e}.mat-form-field[_ngcontent-%COMP%]{width:100%}.form-label[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:11px;font-weight:400;line-height:16px;letter-spacing:.02em;color:#5f6c81}.close-icon[_ngcontent-%COMP%]{cursor:pointer}.bg-image[_ngcontent-%COMP%]{position:relative}.close-btn[_ngcontent-%COMP%]{padding-right:15px;cursor:pointer}.hearder-column-two[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:end}.loading-img[_ngcontent-%COMP%]{height:var(--dynamicHeight);flex-direction:column;background-color:#fff}.loading-img[_ngcontent-%COMP%], .loading-img[_ngcontent-%COMP%]   .loading-wrapper[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center}.loading-img[_ngcontent-%COMP%]   .loading-wrapper[_ngcontent-%COMP%]{vertical-align:center}.loading-img[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]{color:rgba(0,0,0,.3);font-size:16px}.loading-img[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]:before{content:"Loading...";position:absolute;overflow:hidden;max-width:7em;white-space:nowrap;background:linear-gradient(270deg,var(--atsprimaryColor),var(--atssecondaryColor) 105.29%);background-clip:text;-webkit-background-clip:text;-webkit-text-fill-color:transparent;animation:loading 6s linear infinite}@keyframes loading{0%{max-width:0}}']}),e})()}}]);