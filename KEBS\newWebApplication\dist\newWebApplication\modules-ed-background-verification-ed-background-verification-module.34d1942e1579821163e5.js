(window.webpackJsonp=window.webpackJsonp||[]).push([[807],{a1Ju:function(e,t,n){"use strict";n.r(t),n.d(t,"EdBackgroundVerificationModule",(function(){return C}));var o=n("ofXK"),i=n("tyNb"),r=n("fXoL"),c=n("33Jv"),s=n("jAlA"),a=n("1A3m");const l=["contentContainer"],d=[{path:"",component:(()=>{class e{constructor(e,t,n,o,i){this._router=e,this._edService=t,this._route=n,this._toaster=o,this._compiler=i,this.subs=new c.a}ngOnInit(){this.getEmployeeId(),this.loadBackgroundVerificationDetailContainer()}getEmployeeId(){this.subs.sink=this._edService.getEmployeeId().subscribe(e=>{console.log(e),this.associateId=e})}loadBackgroundVerificationDetailContainer(){this.contentContainerRef&&this.contentContainerRef.clear(),Promise.all([n.e(20),n.e(25),n.e(389)]).then(n.bind(null,"3Hwu")).then(e=>{const t=this._compiler.compileModuleSync(e.BackgroundVerificationDetailsModule).create(this.contentContainerRef.injector).componentFactoryResolver.resolveComponentFactory(e.BackgroundVerificationDetailsComponent);this.contentContainerRef.createComponent(t).instance.associateId=this.associateId})}ngOnDestroy(){this.subs.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](i.g),r["\u0275\u0275directiveInject"](s.a),r["\u0275\u0275directiveInject"](i.a),r["\u0275\u0275directiveInject"](a.a),r["\u0275\u0275directiveInject"](r.Compiler))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["app-ed-background-verification-landing-page"]],viewQuery:function(e,t){if(1&e&&r["\u0275\u0275viewQuery"](l,!0,r.ViewContainerRef),2&e){let e;r["\u0275\u0275queryRefresh"](e=r["\u0275\u0275loadQuery"]())&&(t.contentContainerRef=e.first)}},decls:5,vars:0,consts:[[1,"container-fluid","ed-bgv-detail-item-styles"],[1,"row"],[1,"col-12","p-0"],["contentContainer",""]],template:function(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div",0),r["\u0275\u0275elementStart"](1,"div",1),r["\u0275\u0275elementStart"](2,"div",2),r["\u0275\u0275elementContainer"](3,null,3),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]())},styles:[".ed-bgv-detail-item-styles[_ngcontent-%COMP%]   .overflow[_ngcontent-%COMP%]{height:28rem;overflow:scroll;overflow-x:hidden}.ed-bgv-detail-item-styles[_ngcontent-%COMP%]   .normalFont[_ngcontent-%COMP%]{color:#181818;font-size:14px;font-weight:600;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}"]}),e})()}];let u=(()=>{class e{}return e.\u0275mod=r["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=r["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.k.forChild(d)],i.k]}),e})();var f=n("bTqV"),m=n("NFeN"),p=n("Qu3c"),h=n("jaxi"),g=n("STbY"),v=n("MutI"),w=n("0IaG");let C=(()=>{class e{}return e.\u0275mod=r["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=r["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[o.CommonModule,u,m.b,f.b,p.b,h.c,g.e,v.d,w.g]]}),e})()}}]);