(window.webpackJsonp=window.webpackJsonp||[]).push([[946],{"1d+P":function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var o=n("Iab2"),i=n("fXoL");let a=(()=>{class e{constructor(){this.saveAsFile=(e,t,n)=>{var i=this.base64ToBlob(e,n);o.saveAs(i,t)},this.base64ToBlob=(e,t,n=512)=>{e=e.replace(/\s/g,"");let o=atob(e),i=[];for(let l=0;l<o.length;l+=n){let e=o.slice(l,l+n),t=new Array(e.length);for(var a=0;a<e.length;a++)t[a]=e.charCodeAt(a);let s=new Uint8Array(t);i.push(s)}return new Blob(i,{type:t})}}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275prov=i["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},vOvr:function(e,t,n){"use strict";n.r(t),n.d(t,"TaskDetailViewComponent",(function(){return G}));var o=n("mrSG"),i=n("0IaG"),a=n("7pIB"),l=n("1G5W"),s=n("XNiG"),r=n("xG9w"),m=n("AytR"),c=n("ofXK"),d=n("kmnG"),p=n("qFsG"),h=n("bTqV"),u=n("NFeN"),g=n("Qu3c"),f=(n("Xa2L"),n("z3KA")),x=(n("STbY"),n("Xi0T"),n("fXoL")),v=n("wd/R"),_=n("XXEo"),y=n("BVzC"),S=n("JLuW"),C=n("me71");const b=["scrollFrame"];function k(e,t){if(1&e&&(x["\u0275\u0275elementStart"](0,"div",17),x["\u0275\u0275elementStart"](1,"div",18),x["\u0275\u0275elementStart"](2,"div",19),x["\u0275\u0275elementStart"](3,"span",20),x["\u0275\u0275text"](4),x["\u0275\u0275pipe"](5,"date"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275text"](6),x["\u0275\u0275elementEnd"](),x["\u0275\u0275element"](7,"app-user-image",21),x["\u0275\u0275elementEnd"]()),2&e){const e=x["\u0275\u0275nextContext"]().$implicit;x["\u0275\u0275advance"](4),x["\u0275\u0275textInterpolate"](x["\u0275\u0275pipeBind2"](5,3,e.time,"medium")),x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate1"](" ",e.comment," "),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("id",e.commentor_oid)}}function I(e,t){if(1&e&&(x["\u0275\u0275elementStart"](0,"div",22),x["\u0275\u0275element"](1,"app-user-image",23),x["\u0275\u0275elementStart"](2,"div",24),x["\u0275\u0275elementStart"](3,"div",19),x["\u0275\u0275elementStart"](4,"span",25),x["\u0275\u0275text"](5),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](6,"span",26),x["\u0275\u0275text"](7),x["\u0275\u0275pipe"](8,"date"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](9,"div",27),x["\u0275\u0275text"](10),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()),2&e){const e=x["\u0275\u0275nextContext"]().$implicit;x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("id",e.commentor_oid),x["\u0275\u0275advance"](4),x["\u0275\u0275textInterpolate1"](" ",e.commentor_name," "),x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate"](x["\u0275\u0275pipeBind2"](8,4,e.time,"medium")),x["\u0275\u0275advance"](3),x["\u0275\u0275textInterpolate1"](" ",e.comment," ")}}function M(e,t){if(1&e&&(x["\u0275\u0275elementStart"](0,"div",14),x["\u0275\u0275template"](1,k,8,6,"div",15),x["\u0275\u0275template"](2,I,11,7,"div",16),x["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=x["\u0275\u0275nextContext"]();x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",e.commentor_oid==n.currentUser.oid),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",e.commentor_oid!=n.currentUser.oid)}}const E=function(e){return{height:e}},w=function(e){return{"min-height":e}};let O=(()=>{class e{constructor(e,t,n){this.loginService=e,this._ErrorService=t,this.sharedLazyLoadedComponentsService=n,this.comments=[],this.sendComments=new x.EventEmitter,this.sendMentions=new x.EventEmitter,this.selectedMentions=[]}ngOnInit(){return Object(o.c)(this,void 0,void 0,(function*(){this.currentUser=this.loginService.getProfile().profile,this.userSuggestions=yield this.sharedLazyLoadedComponentsService.getEmployeesForSearch(),this.mentionConfig={mentions:[{items:this.userSuggestions,triggerChar:"@",maxItems:10,labelKey:"displayName",dropUp:!0}]},setTimeout(()=>{this.scrollToBottom()},500)}))}ngAfterViewInit(){this.scrollContainer=this.scrollFrame.nativeElement}scrollToBottom(){this.scrollContainer.scroll({top:this.scrollContainer.scrollHeight,left:0,behavior:"smooth"})}enterComment(e,t){this.comments.push({sequence_number:t,time:v(),commentor_oid:this.currentUser.oid,commentor_name:this.currentUser.name,comment:document.getElementById("comment").value});let n={sequence_number:t,time:v(),commentor_oid:this.currentUser.oid,commentor_name:this.currentUser.name,comment:document.getElementById("comment").value};this.sendComments.emit(n),this.selectedMentions.length>0&&(this.sendMentions.emit(this.selectedMentions),this.selectedMentions=[]),this.scrollToBottom(),document.getElementById("comment").value=""}onKeydown(e){"Enter"===e.key&&this.enterComment(void 0,this.comments.length+1)}getCommentBoxHeight(){return this.commentBoxHeight?this.commentBoxHeight:"70vh"}getCommentScrollHeight(){return this.commentBoxScrollHeight?this.commentBoxScrollHeight:"93%"}selectMention(e){this.selectedMentions.push(e)}}return e.\u0275fac=function(t){return new(t||e)(x["\u0275\u0275directiveInject"](_.a),x["\u0275\u0275directiveInject"](y.a),x["\u0275\u0275directiveInject"](S.a))},e.\u0275cmp=x["\u0275\u0275defineComponent"]({type:e,selectors:[["task-detail-comments"]],viewQuery:function(e,t){if(1&e&&x["\u0275\u0275viewQuery"](b,!0),2&e){let e;x["\u0275\u0275queryRefresh"](e=x["\u0275\u0275loadQuery"]())&&(t.scrollFrame=e.first)}},inputs:{comments:"comments",commentBoxHeight:"commentBoxHeight",commentBoxScrollHeight:"commentBoxScrollHeight"},outputs:{sendComments:"sendComments",sendMentions:"sendMentions"},decls:15,vars:9,consts:[[1,"container-fluid","d-flex","flex-column","p-0",3,"ngStyle"],["id","scrollDiv",1,"overflow-scroll",3,"ngStyle"],["scrollFrame",""],["class","row mt-3","style","min-width: 90%",4,"ngFor","ngForOf"],[1,"row","mx-0","mt-auto","h-40","pt-2",2,"border-top","1px solid #c1bfbf"],[1,"col-1",2,"padding-top","9px"],["imgWidth","30px","imgHeight","30px",1,"my-auto","pr-1",3,"id"],[1,"col-10","pl-3","pr-2"],[1,"comment-box"],["type","text","matInput","","placeholder","Enter comment here ...","id","comment",3,"mentionConfig","keydown","itemSelected"],["comment",""],[1,"col-1"],["mat-icon-button","",1,"my-auto","send-button",3,"click"],[2,"color","white","font-size","22px"],[1,"row","mt-3",2,"min-width","90%"],["class","col-12 pl-1 d-flex",4,"ngIf"],["class","col-12 pl-2  d-flex",4,"ngIf"],[1,"col-12","pl-1","d-flex"],[1,"chat-outgoing"],[1,"row",2,"height","21px !important"],[1,"comment-date","ml-auto"],["imgWidth","28px","imgHeight","28px",1,"my-auto","pr-1","ml-1",3,"id"],[1,"col-12","pl-2","d-flex"],["imgWidth","28px","imgHeight","28px",1,"my-auto","pr-1",3,"id"],[1,"chat-incoming"],[1,"comment-user-name"],[1,"comment-date"],[1,"row"]],template:function(e,t){1&e&&(x["\u0275\u0275elementStart"](0,"div",0),x["\u0275\u0275elementStart"](1,"div",1,2),x["\u0275\u0275template"](3,M,3,2,"div",3),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](4,"div",4),x["\u0275\u0275elementStart"](5,"div",5),x["\u0275\u0275element"](6,"app-user-image",6),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](7,"div",7),x["\u0275\u0275elementStart"](8,"mat-form-field",8),x["\u0275\u0275elementStart"](9,"input",9,10),x["\u0275\u0275listener"]("keydown",(function(e){return t.onKeydown(e)}))("itemSelected",(function(e){return t.selectMention(e)})),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](11,"div",11),x["\u0275\u0275elementStart"](12,"button",12),x["\u0275\u0275listener"]("click",(function(e){return t.enterComment(e.target.value,t.comments.length+1)})),x["\u0275\u0275elementStart"](13,"mat-icon",13),x["\u0275\u0275text"](14,"send"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()),2&e&&(x["\u0275\u0275property"]("ngStyle",x["\u0275\u0275pureFunction1"](5,E,t.getCommentBoxHeight())),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngStyle",x["\u0275\u0275pureFunction1"](7,w,t.getCommentScrollHeight())),x["\u0275\u0275advance"](2),x["\u0275\u0275property"]("ngForOf",t.comments),x["\u0275\u0275advance"](3),x["\u0275\u0275property"]("id",t.currentUser.oid),x["\u0275\u0275advance"](3),x["\u0275\u0275property"]("mentionConfig",t.mentionConfig))},directives:[c.NgStyle,c.NgForOf,C.a,d.c,p.b,f.a,h.a,u.a,c.NgIf],pipes:[c.DatePipe],styles:[".comment-row[_ngcontent-%COMP%]{height:70vh}.footer-bar[_ngcontent-%COMP%]{position:absolute;bottom:0!important}.comment-box[_ngcontent-%COMP%]{width:100%;font-size:14px}.comment-box[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{height:30px;padding-bottom:9px}.comment-box[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding-bottom:0!important}.comment-date[_ngcontent-%COMP%]{font-size:11px;color:#959696}.comment-user-name[_ngcontent-%COMP%]{padding-right:10px;font-size:11px;font-weight:400;color:#6b6969}.comment-user-name-left[_ngcontent-%COMP%]{padding-left:20px;font-size:11px;font-weight:400;color:#252424}.chat-incoming[_ngcontent-%COMP%]{padding:5px 50px 5px 22px;text-align:left}.chat-outgoing[_ngcontent-%COMP%]{margin-left:auto!important;padding:5px 22px}.chat-incoming[_ngcontent-%COMP%], .chat-outgoing[_ngcontent-%COMP%]{background:#ececec;display:inline-block;font-size:14px;color:#454546;border-radius:7px;max-width:90%;text-align:right;position:relative}.chat-incoming[_ngcontent-%COMP%]{margin-right:auto!important;padding:5px 15px}.smallCardIcon[_ngcontent-%COMP%]{font-size:18px!important;color:#66615b!important}.send-button[_ngcontent-%COMP%]{width:35px;height:35px;line-height:34px;background-color:#cf0001;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}"]}),e})();var P=n("1d+P"),F=n("R/Xf"),B=n("nAV5"),T=n("mS9j");const z=function(e){return{background:e}};function D(e,t){if(1&e&&(x["\u0275\u0275elementContainerStart"](0),x["\u0275\u0275element"](1,"span",29),x["\u0275\u0275pipe"](2,"getCtaStatusColor"),x["\u0275\u0275elementStart"](3,"span",30),x["\u0275\u0275text"](4),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementContainerEnd"]()),2&e){const e=x["\u0275\u0275nextContext"]();x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngStyle",x["\u0275\u0275pureFunction1"](4,z,x["\u0275\u0275pipeBind1"](2,2,null==e.taskItem?null:e.taskItem.task_status))),x["\u0275\u0275advance"](3),x["\u0275\u0275textInterpolate"](null==e.taskItem?null:e.taskItem.task_status)}}function A(e,t){if(1&e&&(x["\u0275\u0275element"](0,"span",29),x["\u0275\u0275elementStart"](1,"span",30),x["\u0275\u0275text"](2),x["\u0275\u0275elementEnd"]()),2&e){const e=x["\u0275\u0275nextContext"]();x["\u0275\u0275property"]("ngStyle",x["\u0275\u0275pureFunction1"](2,z,null==e.taskItem?null:e.taskItem.status_color)),x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate"](null==e.taskItem?null:e.taskItem.status_name)}}function j(e,t){if(1&e&&(x["\u0275\u0275elementContainerStart"](0),x["\u0275\u0275element"](1,"app-user-profile",31),x["\u0275\u0275pipe"](2,"activeObject"),x["\u0275\u0275elementContainerEnd"]()),2&e){const e=x["\u0275\u0275nextContext"]();x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("oid",x["\u0275\u0275pipeBind2"](2,1,null==e.taskItem?null:e.taskItem.assigned_to,"employee_oid"))}}function N(e,t){if(1&e&&x["\u0275\u0275element"](0,"app-user-profile",31),2&e){const e=x["\u0275\u0275nextContext"](2);x["\u0275\u0275property"]("oid",null==e.taskItem?null:e.taskItem.owner_id)}}function H(e,t){1&e&&(x["\u0275\u0275elementStart"](0,"span"),x["\u0275\u0275text"](1,"Not Assigned"),x["\u0275\u0275elementEnd"]())}function U(e,t){if(1&e&&(x["\u0275\u0275template"](0,N,1,1,"app-user-profile",32),x["\u0275\u0275template"](1,H,2,0,"span",25)),2&e){const e=x["\u0275\u0275nextContext"]();x["\u0275\u0275property"]("ngIf",null==e.taskItem?null:e.taskItem.owner_id),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",!(null!=e.taskItem&&e.taskItem.owner_id))}}function L(e,t){1&e&&(x["\u0275\u0275elementStart"](0,"div",33),x["\u0275\u0275text"](1," - "),x["\u0275\u0275elementEnd"]())}function q(e,t){if(1&e){const e=x["\u0275\u0275getCurrentView"]();x["\u0275\u0275elementContainerStart"](0),x["\u0275\u0275elementStart"](1,"div",38),x["\u0275\u0275elementStart"](2,"div",39),x["\u0275\u0275elementStart"](3,"button",40),x["\u0275\u0275element"](4,"i",41),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](5,"div",42),x["\u0275\u0275text"](6),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](7,"div",43),x["\u0275\u0275text"](8),x["\u0275\u0275pipe"](9,"date"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](10,"div",43),x["\u0275\u0275text"](11),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](12,"div",43),x["\u0275\u0275text"](13),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](14,"div",44),x["\u0275\u0275elementStart"](15,"button",45),x["\u0275\u0275listener"]("click",(function(){x["\u0275\u0275restoreView"](e);const n=t.index;return x["\u0275\u0275nextContext"](2).downloadFile(n)})),x["\u0275\u0275elementStart"](16,"mat-icon",46),x["\u0275\u0275text"](17,"file_download"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=t.$implicit,n=x["\u0275\u0275nextContext"](2);x["\u0275\u0275advance"](4),x["\u0275\u0275classMap"](n.fileType[null==e?null:e.type]||"ms-Icon ms-Icon--FileTemplate"),x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate1"](" ",e.fileName?e.fileName:e.file_name," "),x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate1"](" ",x["\u0275\u0275pipeBind2"](9,6,null==e?null:e.created_on,"dd-MMM-yy")," "),x["\u0275\u0275advance"](3),x["\u0275\u0275textInterpolate1"](" ",null==e?null:e.created_by," "),x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate1"](" ",null==e?null:e.displaySize," ")}}function V(e,t){if(1&e&&(x["\u0275\u0275elementContainerStart"](0),x["\u0275\u0275elementStart"](1,"div",34),x["\u0275\u0275elementStart"](2,"div",20),x["\u0275\u0275text"](3," Type "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](4,"div",35),x["\u0275\u0275text"](5," File name "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](6,"div",36),x["\u0275\u0275text"](7," Created on "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](8,"div",36),x["\u0275\u0275text"](9," Created by "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](10,"div",36),x["\u0275\u0275text"](11," Size "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275template"](12,q,18,9,"ng-container",37),x["\u0275\u0275elementContainerEnd"]()),2&e){const e=x["\u0275\u0275nextContext"]();x["\u0275\u0275advance"](12),x["\u0275\u0275property"]("ngForOf",null==e.taskItem?null:e.taskItem.attachments)}}function X(e,t){1&e&&(x["\u0275\u0275elementStart"](0,"div",47),x["\u0275\u0275elementStart"](1,"div",48),x["\u0275\u0275elementStart"](2,"span",49),x["\u0275\u0275text"](3,"No Attachment found ! "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](4,"div",50),x["\u0275\u0275element"](5,"img",51),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]())}let G=(()=>{class e{constructor(e,t,n,i,r,c,d,p){this._auth=e,this.dialogRef=t,this.inData=n,this._fileSaver=i,this._ErrorService=r,this._cmsService=c,this.inlineEditPopupService=d,this.sharedLazyLoadedComponentsService=p,this.currentUser=this._cmsService.currentUser,this._onDestroy=new s.b,this.allowedMimeType=["*/*"],this.comment=[],this.maxFileSize=10485760,this.selectedMentions=[],this.uploader=new a.d({url:"/api/appraisal/configuration/uploadAppraisalAttachment",authToken:"Bearer "+this._auth.getToken(),autoUpload:!0,disableMultipart:!1,maxFileSize:this.maxFileSize,headers:[{name:"user",value:this.currentUser.name}]}),this.fileType=m.a.fileuploadedSupportedFileTypes,this.isUploading=!1,this.dataArray=[],this.TABLE_NAME="",this.COMMENT_ID="",this.isFromTicket=!1,this.inlineEditField="",this.editType="",this.formatFileSize=()=>{if(this.taskItem.attachments)for(let e of this.taskItem.attachments)e.displaySize=this.bytesToSize(e.size)},this.bytesToSize=e=>{if(0===e)return"0 Bytes";const t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB","TB","PB","EB","ZB","YB"][t]},this.fileUploadChanges=()=>{this.uploader.onProgressItem=e=>{this.isUploading=!0},this.uploader.onCompleteItem=(e,t,n,o)=>{if(200==n&&t&&t.length>0){this.isUploading=!1;let e=JSON.parse(t);this.updateAttachment(e.files_json)}else console.log("Unable to upload")}},this.updateAttachment=e=>{this.updateTaskObject("attachments",e),console.log(e),this.taskItem.attachments.push(e),this.formatFileSize()},this.updateTaskObject=(e,t)=>{"attachments"==e&&this._cmsService.addTaskAttachment({compliance_id:this.inData.modalParams.compliance_id,compliance_name:this.inData.modalParams.compliance_name,task_id:this.inData.modalParams.task_id,task_name:this.inData.modalParams.task_name,sub_task_id:this.inData.modalParams.sub_task_id?this.inData.modalParams.sub_task_id:null,sub_task_name:this.inData.modalParams.sub_task_name?this.inData.modalParams.sub_task_name:null,attachment:t}).pipe(Object(l.a)(this._onDestroy)).subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){"N"==e.err?console.log("Task updated !"):console.log("Task update failed!")})),e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})},this.getComments=e=>{this.writeComments(e)},this.closeTaskDetailModal=()=>{this.dialogRef.close({event:"Close"})},this.fileUploadChanges()}ngOnInit(){return Object(o.c)(this,void 0,void 0,(function*(){this.inData&&this.inData.modalParams&&(this.taskItem=this.inData.modalParams.taskItem),console.log(this.taskItem),this.initCommentData(),this.formatFileSize()}))}initCommentData(){return Object(o.c)(this,void 0,void 0,(function*(){let e={application_id:218,unique_id_1:this.taskItem._id,unique_id_2:"",application_name:"Compliance Management",title:this.taskItem.name},t=yield this.sharedLazyLoadedComponentsService.getComments(e);return"S"==t.messType?(console.log(t),console.log(this.comment),this.COMMENT_ID=t.result.id,this.comment="string"==typeof t.result.comments?JSON.parse(t.result.comments):t.result.comments,this.TABLE_NAME=t.result.table_name,void console.log(this.comment)):(console.log(t),void console.log(this.comment))}))}writeComments(e){return Object(o.c)(this,void 0,void 0,(function*(){let t=this._auth.getProfile().profile;console.log("user"),console.log(t);let n={context:{},sequence_number:e.sequence_number,time:new Date,commentor_oid:t.oid,commentor_name:t.name,comment:e.comment},o={application_id:218,unique_id_1:this.taskItem._id,unique_id_2:"",table_name:"t_compliance_management_comments",comment:n};if(console.log(n),yield this.sharedLazyLoadedComponentsService.writeComment(o),this.selectedMentions.length>0){let e=yield this.mentionSavePreFormatter(n);for(let t=0;t<e.length;t++)yield this.sharedLazyLoadedComponentsService.notifyIndividual(e[t]),t==e.length-1&&(this.selectedMentions=[])}}))}downloadFile(e){let t=this.taskItem.attachments[e];this._cmsService.getComplianceAttachmentFromS3(t.key).pipe(Object(l.a)(this._onDestroy)).subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){"S"==e.messType?"image/png"==t.type||"image/jpg"==t.type||"image/jpeg"==t.type||"application/pdf"==t.type?(window.open("").document.write(`<iframe width=100% height=100% src=data:${t.type};base64,${e.data.fileData}></iframe>`),this._fileSaver.saveAsFile(e.data.fileData,t.fileName,t.type)):this._fileSaver.saveAsFile(e.data.fileData,t.fileName,t.type):this._cmsService.showMessage(e.messText)})),e=>{this._ErrorService.userErrorAlert(e.error.errorCode,"Error downloading file",e.error.errMessage)})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}mentionSavePreFormatter(e){return Object(o.c)(this,void 0,void 0,(function*(){let t={application_id:218,unique_id_1:this.taskItem._id,unique_id_2:"",table_name:"t_compliance_management_comments"};return this.selectedMentions=r.reject(this.selectedMentions,n=>(n.message=this.currentUser.name+" has commented "+e.comment+" in "+this.taskItem.name+" under Compliance Management",n.context=e,n.link="",n.insert_meta_data=t,-1==e.comment.search("@"+n.displayName))),this.selectedMentions}))}getMentions(e){this.selectedMentions=e}}return e.\u0275fac=function(t){return new(t||e)(x["\u0275\u0275directiveInject"](_.a),x["\u0275\u0275directiveInject"](i.h),x["\u0275\u0275directiveInject"](i.a),x["\u0275\u0275directiveInject"](P.a),x["\u0275\u0275directiveInject"](y.a),x["\u0275\u0275directiveInject"](F.a),x["\u0275\u0275directiveInject"](B.a),x["\u0275\u0275directiveInject"](S.a))},e.\u0275cmp=x["\u0275\u0275defineComponent"]({type:e,selectors:[["task-detail-view"]],decls:52,vars:17,consts:[[1,"container-fluid","pt-2","pb-2","task-detail-styles"],[2,"background-color","#F9F9F9"],[1,"row"],[1,"col-11","pt-2","headingBold"],[1,"col-1","mt-0","d-flex"],["mat-icon-button","",1,"ml-auto","close-button","mt-1",3,"click"],["matTooltip","Close",1,"close-Icon"],[1,"row","pt-2","pb-2"],[1,"col-1","headerFont"],[1,"col-3","normalFont"],[4,"ngIf","ngIfElse"],["showOther",""],["matTooltip","Assigned to",1,"col-1","headerFont"],["showOtherAt",""],[1,"row","pt-2"],[1,"col-8","pl-0"],[1,"col-12","headerFont"],[1,"col-12","pl-4"],["class","pl-4",4,"ngIf"],[1,"col-11","headerFont"],[1,"col-1"],["mat-icon-button","","matTooltip","Add files",1,"view-button-inactive",3,"click"],[1,"iconButton"],["hidden","","type","file","ng2FileSelect","",3,"uploader","accept"],["moreFileInput",""],[4,"ngIf"],["style","text-align: center;padding-top: 3rem;",4,"ngIf"],[1,"col-4","pl-1","pr-1",2,"border-left","solid 1px #cacaca"],[3,"comments","commentBoxHeight","sendComments","sendMentions"],[1,"status-dot",3,"ngStyle"],[1,"pl-2"],["type","small","imgHeight","28px","imgWidth","28px",3,"oid"],["type","small","imgHeight","28px","imgWidth","28px",3,"oid",4,"ngIf"],[1,"pl-4"],[1,"row","pt-2","header",2,"border-bottom","solid 1px #cacaca"],[1,"col-4"],[1,"col-2"],[4,"ngFor","ngForOf"],[1,"row",2,"border-bottom","solid 1px #cacaca"],[1,"col-1","pl-2"],["mat-icon-button","",1,"ic-size"],["aria-hidden","true"],[1,"col-4","fileFont","d-flex","my-auto"],[1,"col-2","fileFont","d-flex","my-auto"],[1,"col-1","d-flex","my-auto"],["mat-icon-button","","matTooltip","Download",1,"icon-tray-button","mr-2",3,"click"],[1,"smallCardIcon"],[2,"text-align","center","padding-top","3rem"],[1,"d-flex","pb-2","justify-content-center","align-items-center","slide-in-top"],[2,"font-size","16px","font-weight","normal"],[1,"d-flex","justify-content-center","align-items-center","slide-from-down","pt-2","pb-2"],["src","https://assets.kebs.app/images/noAccounts.png","height","200","width","200",1,"mt-2","mb-2"]],template:function(e,t){if(1&e){const e=x["\u0275\u0275getCurrentView"]();x["\u0275\u0275elementStart"](0,"div",0),x["\u0275\u0275elementStart"](1,"div",1),x["\u0275\u0275elementStart"](2,"div",2),x["\u0275\u0275elementStart"](3,"div",3),x["\u0275\u0275text"](4),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](5,"div",4),x["\u0275\u0275elementStart"](6,"button",5),x["\u0275\u0275listener"]("click",(function(){return t.closeTaskDetailModal()})),x["\u0275\u0275elementStart"](7,"mat-icon",6),x["\u0275\u0275text"](8,"close"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](9,"div",7),x["\u0275\u0275elementStart"](10,"div",8),x["\u0275\u0275text"](11," Status "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](12,"div",9),x["\u0275\u0275template"](13,D,5,6,"ng-container",10),x["\u0275\u0275template"](14,A,3,4,"ng-template",null,11,x["\u0275\u0275templateRefExtractor"]),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](16,"div",12),x["\u0275\u0275text"](17," Assigned to "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](18,"div",9),x["\u0275\u0275template"](19,j,3,4,"ng-container",10),x["\u0275\u0275template"](20,U,2,2,"ng-template",null,13,x["\u0275\u0275templateRefExtractor"]),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](22,"div",8),x["\u0275\u0275text"](23," Deadline "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](24,"div",9),x["\u0275\u0275text"](25),x["\u0275\u0275pipe"](26,"date"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](27,"div",14),x["\u0275\u0275elementStart"](28,"div",15),x["\u0275\u0275elementStart"](29,"div",7),x["\u0275\u0275elementStart"](30,"div",16),x["\u0275\u0275text"](31," Task Description "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](32,"div",14),x["\u0275\u0275elementStart"](33,"div",17),x["\u0275\u0275text"](34),x["\u0275\u0275elementEnd"](),x["\u0275\u0275template"](35,L,2,0,"div",18),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](36,"div",7),x["\u0275\u0275elementStart"](37,"div",19),x["\u0275\u0275text"](38," Attachment "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](39,"div",20),x["\u0275\u0275elementStart"](40,"button",21),x["\u0275\u0275listener"]("click",(function(){return x["\u0275\u0275restoreView"](e),x["\u0275\u0275reference"](44).click()})),x["\u0275\u0275elementStart"](41,"mat-icon",22),x["\u0275\u0275text"](42,"cloud_upload"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275element"](43,"input",23,24),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275template"](45,V,13,1,"ng-container",25),x["\u0275\u0275template"](46,X,6,0,"div",26),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](47,"div",27),x["\u0275\u0275elementStart"](48,"div",14),x["\u0275\u0275elementStart"](49,"div",19),x["\u0275\u0275text"](50," Comments "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](51,"task-detail-comments",28),x["\u0275\u0275listener"]("sendComments",(function(e){return t.getComments(e)}))("sendMentions",(function(e){return t.getMentions(e)})),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()}if(2&e){const e=x["\u0275\u0275reference"](15),n=x["\u0275\u0275reference"](21);x["\u0275\u0275advance"](4),x["\u0275\u0275textInterpolate1"](" ",null==t.taskItem?null:t.taskItem.name," "),x["\u0275\u0275advance"](9),x["\u0275\u0275property"]("ngIf",t.isFromTicket)("ngIfElse",e),x["\u0275\u0275advance"](6),x["\u0275\u0275property"]("ngIf",t.isFromTicket)("ngIfElse",n),x["\u0275\u0275advance"](6),x["\u0275\u0275textInterpolate1"](" ",x["\u0275\u0275pipeBind2"](26,14,null==t.taskItem?null:t.taskItem.deadline,"dd-MMM-yy")," "),x["\u0275\u0275advance"](9),x["\u0275\u0275textInterpolate1"](" ",null==t.taskItem?null:t.taskItem.desc," "),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",!(null!=t.taskItem&&t.taskItem.desc)),x["\u0275\u0275advance"](8),x["\u0275\u0275property"]("uploader",t.uploader)("accept",t.allowedMimeType),x["\u0275\u0275advance"](2),x["\u0275\u0275property"]("ngIf",t.taskItem.attachments&&t.taskItem.attachments.length>0),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",!(null!=t.taskItem&&t.taskItem.attachments)||0==(null==t.taskItem?null:t.taskItem.attachments.length)),x["\u0275\u0275advance"](5),x["\u0275\u0275property"]("comments",t.comment)("commentBoxHeight","64vh")}},directives:function(){return[h.a,u.a,g.a,c.NgIf,a.b,O,c.NgStyle,T.a,c.NgForOf]},pipes:function(){return[c.DatePipe]},styles:[".task-detail-styles[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.task-detail-styles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}.task-detail-styles[_ngcontent-%COMP%]   .headingBold[_ngcontent-%COMP%]{color:#cf0001;font-weight:500;font-size:14px}.task-detail-styles[_ngcontent-%COMP%]   .headerFont[_ngcontent-%COMP%]{color:#868383;font-size:14px!important;font-weight:400;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.task-detail-styles[_ngcontent-%COMP%]   .normalFont[_ngcontent-%COMP%]{font-weight:500}.task-detail-styles[_ngcontent-%COMP%]   .fileFont[_ngcontent-%COMP%], .task-detail-styles[_ngcontent-%COMP%]   .normalFont[_ngcontent-%COMP%]{color:#181818;font-size:14px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.task-detail-styles[_ngcontent-%COMP%]   .fileFont[_ngcontent-%COMP%]{font-weight:400}.task-detail-styles[_ngcontent-%COMP%]   .status-dot[_ngcontent-%COMP%]{height:13px;width:13px;border-radius:50%;display:inline-block;vertical-align:middle}.task-detail-styles[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]{line-height:8px;width:26px;height:26px;position:absolute;margin-right:5px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12);animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}.task-detail-styles[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.task-detail-styles[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{color:#5f5f5f;font-size:11px!important}.task-detail-styles[_ngcontent-%COMP%]   .ic-size[_ngcontent-%COMP%]{font-size:20px!important;line-height:1!important}.task-detail-styles[_ngcontent-%COMP%]   .excel[_ngcontent-%COMP%]{color:green!important}.task-detail-styles[_ngcontent-%COMP%]   .word[_ngcontent-%COMP%]{color:#2b579a!important}.task-detail-styles[_ngcontent-%COMP%]   .ppt[_ngcontent-%COMP%]{color:#d24726!important}.task-detail-styles[_ngcontent-%COMP%]   .txt[_ngcontent-%COMP%]{color:#2b579a!important}.task-detail-styles[_ngcontent-%COMP%]   .pdf[_ngcontent-%COMP%]{color:#bf040d!important}.task-detail-styles[_ngcontent-%COMP%]   .mail[_ngcontent-%COMP%]{color:#282829!important}.task-detail-styles[_ngcontent-%COMP%]   .icon-tray-button[_ngcontent-%COMP%]{width:27px!important;height:27px!important;line-height:25px!important}.task-detail-styles[_ngcontent-%COMP%]   .smallCardIcon[_ngcontent-%COMP%]{font-size:18px!important;color:#868683}.task-detail-styles[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}.task-detail-styles[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}"]}),e})()}}]);