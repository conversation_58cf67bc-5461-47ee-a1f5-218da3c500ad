(window.webpackJsonp=window.webpackJsonp||[]).push([[970],{Ekz2:function(e,t,n){"use strict";n.r(t),n.d(t,"TagsDialogComponent",(function(){return d}));var o=n("0IaG"),i=n("fXoL"),r=n("NFeN"),a=n("ofXK");let c=(()=>{class e{transform(e,...t){return null}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=i["\u0275\u0275definePipe"]({name:"isDarkColor",type:e,pure:!0}),e})();const l=function(e){return{background:e}},p=function(e){return{color:e}};function g(e,t){if(1&e&&(i["\u0275\u0275elementContainerStart"](0),i["\u0275\u0275elementStart"](1,"div",7),i["\u0275\u0275elementStart"](2,"div",8),i["\u0275\u0275pipe"](3,"isDarkColor"),i["\u0275\u0275text"](4),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngStyle",i["\u0275\u0275pureFunction1"](5,l,e.color)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngStyle",i["\u0275\u0275pureFunction1"](7,p,i["\u0275\u0275pipeBind1"](3,3,e.color)?"#fff":"#45546e")),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let d=(()=>{class e{constructor(e,t){this.data=e,this._dialogRef=t}ngOnInit(){}onClose(){this._dialogRef.close(!1)}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](o.a),i["\u0275\u0275directiveInject"](o.h))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-tags-dialog"]],decls:9,vars:1,consts:[[1,"bg-container"],[1,"d-flex","align-items-center","justify-content-between",2,"margin-bottom","16px"],[1,"title","p-0"],[1,"icon"],[1,"icon",3,"click"],[1,"d-flex","flex-wrap",2,"gap","10px","overflow-y","auto","max-height","300px"],[4,"ngFor","ngForOf"],[1,"d-flex","align-items-center","tag",3,"ngStyle"],[1,"tag-text",3,"ngStyle"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275elementStart"](1,"div",1),i["\u0275\u0275elementStart"](2,"div",2),i["\u0275\u0275text"](3,"Tags"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"div",3),i["\u0275\u0275elementStart"](5,"mat-icon",4),i["\u0275\u0275listener"]("click",(function(){return t.onClose()})),i["\u0275\u0275text"](6," close "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](7,"div",5),i["\u0275\u0275template"](8,g,5,9,"ng-container",6),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](8),i["\u0275\u0275property"]("ngForOf",t.data.tags))},directives:[r.a,a.NgForOf,a.NgStyle],pipes:[c],styles:[".bg-container[_ngcontent-%COMP%]{padding:16px}.bg-container[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:var(--dashboardFontFamily);font-size:18px;font-weight:700;color:#111434}.bg-container[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{width:18px;height:18px;font-size:18px;color:#1b2140;cursor:pointer}.bg-container[_ngcontent-%COMP%]   .tag[_ngcontent-%COMP%]{padding:8px 16px;border-radius:8px}.bg-container[_ngcontent-%COMP%]   .tag[_ngcontent-%COMP%]   .tag-text[_ngcontent-%COMP%]{font-family:var(--dashboardFontFamily);font-size:14px;font-weight:400;color:#45546e}"]}),e})()}}]);