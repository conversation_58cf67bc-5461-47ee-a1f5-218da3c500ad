(window.webpackJsonp=window.webpackJsonp||[]).push([[648],{T4Li:function(e,t,n){"use strict";n.d(t,"a",(function(){return s}));var a=n("XNiG"),i=n("z6cu"),r=n("xG9w"),o=n("fXoL"),l=n("tk/3"),d=n("flaP");let s=(()=>{class e{constructor(e,t){this.http=e,this.roleService=t,this.messageSharingSubject=new a.b,this.getTranslatedPdfDetail=(e,t,n)=>this.http.post("/api/invoice/otherLanguagePdfDetails",{billingId:e,languageId:t,billingIdFlag:n}),this.getAvailableBankAddress=e=>this.http.post("/api/invoice/stepperAddressChange",{legalEntityId:e,id:3}),this.saveEditedInvoice=(e,t,n)=>this.http.post("/api/invoice/editInvoicePdf",{billingId:e,pdfDetails:t,languageId:n}),this.getAvailableBanks=e=>this.http.post("/api/invoice/bankCrudOperations",{legalEntityId:e,crudId:2}),this.getAvailableFromAddress=e=>this.http.post("/api/invoice/stepperAddressChange",{legalEntityId:e,id:1}),this.getAvailableToAddress=e=>this.http.post("/api/invoice/stepperAddressChange",{customerId:e,id:2}),this.getTranslatedAddressForEnAddress=(e,t,n)=>this.http.post("/api/invoice/getFromAddressDetails",{legalEntityId:e,fromAddressId:t,languageId:n}),this.getTranslatedToAddressForEnAddress=(e,t,n)=>this.http.post("/api/invoice/getToAddressDetails",{customerId:e,toAddressId:t,languageId:n}),this.getTranslatedAddressForAbank=(e,t,n)=>this.http.post("/api/invoice/getBankLanguageDetails",{legalEntityId:e,bankId:t,languageId:n}),this.sendMsg=e=>{this.messageSharingSubject.next(e)},this.getMsg=()=>this.messageSharingSubject.asObservable(),this.generateEngPdf=e=>{this.http.post("/api/invoice/generateEnglishPDF",{pdf_details:e},{responseType:"blob"}).subscribe(e=>{var t=new Blob([e],{type:"application/pdf"}),n=URL.createObjectURL(t);window.open(n)},e=>{Object(i.a)(e)})},this.generateArabicPdf=e=>{this.http.post("/api/invoice/generateArabicPDF",{pdf_details:e},{responseType:"blob"}).subscribe(e=>{var t=new Blob([e],{type:"application/pdf"}),n=URL.createObjectURL(t);window.open(n)},e=>{Object(i.a)(e)})},this.getTenantDateFormats=()=>this.http.post("/api/invoice/v2/getTenantDateFormats",{}),this.syncInvoiceWithSharepoint=(e,t)=>this.http.post("/api/invoice/syncInvoiceWithSharepoint",{billingId:e,token:t})}viewInvoice(e){return this.http.post("api/invoice/viewInvoice",{billingId:e})}roleCheck(){return this.http.post("api/invoice/roleCheck",{})}getInvoiceListActivityList(e){return this.http.post("/api/invoice/getActivitiesInPopUp",{milestoneId:e})}updateInvoiceActivityList(e,t,n,a){return this.http.post("/api/invoice/updateActivities",{milestoneId:e,activities:t,oldPlannedOn:n,newPlannedOn:a})}getInvoicePdfConfig(e,t,n,a,i){return this.http.post("/api/invoice/getInvoicePdfConfig",{fromCompanyCode:e,itemId:t,projectId:n,customerId:a,legalEntityId:i})}getInvoiceTenantCheckDetail(e,t){return this.http.post("/api/invoice/getInvoiceTenantApplicationCheck",{tenantName:e,checkType:t})}getPaymentTerms(){return new Promise((e,t)=>{this.http.post("/api/master/getPaymentTerms",{}).subscribe(t=>e(t),t=>(console.log(t),e([])))})}invoiceUndoAccess(){return r.where(this.roleService.roles,{application_id:10,object_id:320}).length>0}getconsultantDetail(e){return this.http.post("/api/invoice/getFteDetails",{otherMilestoneDataToPdf:e})}getFteDetailForViewInvoice(e){return this.http.post("/api/invoice/getFteDetailForViewInvoice",{otherMilestoneDataToPdf:e})}getCurrencyDetails(){return new Promise((e,t)=>{this.http.post("/api/invoice/getCurrencyDetails",{}).subscribe(t=>(console.log(t),e(t.data)))})}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275inject"](l.c),o["\u0275\u0275inject"](d.a))},e.\u0275prov=o["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},b1E3:function(e,t,n){"use strict";n.r(t),n.d(t,"SalesReportVersion2Module",(function(){return T}));var a=n("ofXK"),i=n("tyNb"),r=n("mrSG"),o=n("3Pt+"),l=n("wd/R"),d=n("FKr1"),s=n("fXoL"),c=n("tk/3"),p=n("BVzC");let m=(()=>{class e{constructor(e,t){this.http=e,this.errorService=t}getSalesReport(e,t){return this.http.post("api/invoice/v2/salesReportApi",{start_date:e,end_date:t})}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275inject"](c.c),s["\u0275\u0275inject"](p.a))},e.\u0275prov=s["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var h=n("dNgK"),g=n("T4Li"),v=n("kmnG"),u=n("qFsG"),b=n("iadO"),w=n("bTqV"),f=n("Qu3c"),x=n("NFeN"),D=n("ZzPI"),S=n("6t9p");let y=(()=>{class e{constructor(){}ngOnInit(){this.currentDate=l().format("DD-MM-YY")}ngOnChanges(e){console.log(this.data)}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-devxtreme"]],viewQuery:function(e,t){if(1&e&&s["\u0275\u0275viewQuery"](D.a,!0),2&e){let e;s["\u0275\u0275queryRefresh"](e=s["\u0275\u0275loadQuery"]())&&(t.dataGrid=e.first)}},inputs:{data:"data"},features:[s["\u0275\u0275NgOnChangesFeature"]],decls:78,vars:59,consts:[["id","gridContainer",1,"dev-style",3,"height","allowColumnResizing","dataSource","showBorders","hoverStateEnabled"],[3,"enabled","fileName"],["mode","select",3,"enabled"],[3,"enabled"],["placeholder","Search...",3,"visible","width"],["mode","single"],[3,"visible"],["dataField","billing_id","caption","Billing Id",3,"allowReordering","width","visible"],["dataField","invoice_raised_month","caption","Invoice raised month",3,"allowReordering","width"],["dataField","item_name","caption","Project Name",3,"width","allowReordering"],["dataField","pl_name","caption","P & L",3,"allowReordering","width"],["dataField","company_code","caption","Legal Entity",3,"allowReordering","width","visible"],["dataField","project_customer","caption","Project Customer",3,"allowReordering","width"],["dataField","invoice_description","caption","Invoice Description",3,"allowReordering","width"],["dataField","service_type_name","caption","Project Type",3,"width","allowReordering","visible"],["dataField","profit_center","caption","Profit Center",3,"allowReordering","width","visible"],["dataField","invoice_raised_on","caption","Invoice raised on",3,"allowReordering","width"],["dataField","poRef","caption","PO Ref",3,"allowReordering","width","visible"],["dataField","poDate","caption","PO Date",3,"allowReordering","width","visible"],["dataField","po_number","caption","PO Number",3,"allowReordering","width"],["dataField","invoice_no","caption","Invoice No",3,"allowReordering","width"],["dataField","currency","caption","Invoice Currency",2,"background-color","#cf0001 !important",3,"allowReordering","width"],["dataField","subtotal","caption","SubTotal",3,"allowReordering","width"],["dataField","total_tax","caption","Tax Amount",3,"allowReordering","width"],["dataField","invoice_value","caption","Invoice Value W/O TDS/TCS",3,"allowReordering","width"],["dataField","tds_value","caption","TDS/TCS Value",3,"allowReordering","width"],["dataField","total_invoice_value","caption","Total Invoice Value",3,"allowReordering","width"]],template:function(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"dx-data-grid",0),s["\u0275\u0275text"](1,"\n  "),s["\u0275\u0275element"](2,"dxo-export",1),s["\u0275\u0275text"](3,"\n  "),s["\u0275\u0275elementStart"](4,"dxo-column-chooser",2),s["\u0275\u0275text"](5,"\n    "),s["\u0275\u0275text"](6,"\n  "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275text"](7,"\n  "),s["\u0275\u0275element"](8,"dxo-column-fixing",3),s["\u0275\u0275text"](9,"\n  "),s["\u0275\u0275element"](10,"dxo-search-panel",4),s["\u0275\u0275text"](11,"\n  "),s["\u0275\u0275element"](12,"dxo-selection",5),s["\u0275\u0275text"](13,"\n  "),s["\u0275\u0275element"](14,"dxo-header-filter",6),s["\u0275\u0275text"](15,"\n  "),s["\u0275\u0275element"](16,"dxo-filter-row",6),s["\u0275\u0275text"](17,"\n\n  "),s["\u0275\u0275elementStart"](18,"dxi-column",7),s["\u0275\u0275text"](19,"\n  "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275text"](20,"\n\n  "),s["\u0275\u0275elementStart"](21,"dxi-column",8),s["\u0275\u0275text"](22,"\n  "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275text"](23,"\n\n  "),s["\u0275\u0275elementStart"](24,"dxi-column",9),s["\u0275\u0275text"](25,"\n  "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275text"](26,"\n\n  "),s["\u0275\u0275elementStart"](27,"dxi-column",10),s["\u0275\u0275text"](28,"\n  "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275text"](29,"\n\n\n  "),s["\u0275\u0275elementStart"](30,"dxi-column",11),s["\u0275\u0275text"](31,"\n  "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275text"](32,"\n\n  "),s["\u0275\u0275elementStart"](33,"dxi-column",12),s["\u0275\u0275text"](34,"\n  "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275text"](35,"\n\n\n  "),s["\u0275\u0275elementStart"](36,"dxi-column",13),s["\u0275\u0275text"](37,"\n  "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275text"](38,"\n\n  "),s["\u0275\u0275elementStart"](39,"dxi-column",14),s["\u0275\u0275text"](40,"\n  "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275text"](41,"\n\n  "),s["\u0275\u0275elementStart"](42,"dxi-column",15),s["\u0275\u0275text"](43,"\n  "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275text"](44,"\n\n\n  "),s["\u0275\u0275elementStart"](45,"dxi-column",16),s["\u0275\u0275text"](46,"\n  "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275text"](47,"\n\n  "),s["\u0275\u0275elementStart"](48,"dxi-column",17),s["\u0275\u0275text"](49,"\n  "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275text"](50,"\n\n  "),s["\u0275\u0275elementStart"](51,"dxi-column",18),s["\u0275\u0275text"](52,"\n  "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275text"](53,"\n\n  "),s["\u0275\u0275elementStart"](54,"dxi-column",19),s["\u0275\u0275text"](55,"\n  "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275text"](56,"\n\n  "),s["\u0275\u0275elementStart"](57,"dxi-column",20),s["\u0275\u0275text"](58,"\n  "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275text"](59,"\n\n\n  "),s["\u0275\u0275elementStart"](60,"dxi-column",21),s["\u0275\u0275text"](61,"\n  "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275text"](62,"\n\n  "),s["\u0275\u0275elementStart"](63,"dxi-column",22),s["\u0275\u0275text"](64,"\n  "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275text"](65,"\n  \n  "),s["\u0275\u0275elementStart"](66,"dxi-column",23),s["\u0275\u0275text"](67,"\n  "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275text"](68,"\n\n  "),s["\u0275\u0275elementStart"](69,"dxi-column",24),s["\u0275\u0275text"](70,"\n  "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275text"](71,"\n\n  "),s["\u0275\u0275elementStart"](72,"dxi-column",25),s["\u0275\u0275text"](73,"\n  "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275text"](74,"\n\n  "),s["\u0275\u0275elementStart"](75,"dxi-column",26),s["\u0275\u0275text"](76,"\n  "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275text"](77,"\n\n"),s["\u0275\u0275elementEnd"]()),2&e&&(s["\u0275\u0275property"]("height",840)("allowColumnResizing",!0)("dataSource",t.data)("showBorders",!0)("hoverStateEnabled",!0),s["\u0275\u0275advance"](2),s["\u0275\u0275propertyInterpolate1"]("fileName","sales report - ",t.currentDate,""),s["\u0275\u0275property"]("enabled",!0),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("enabled",!0),s["\u0275\u0275advance"](4),s["\u0275\u0275property"]("enabled",!0),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("visible",!0)("width",240),s["\u0275\u0275advance"](4),s["\u0275\u0275property"]("visible",!0),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("visible",!0),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("allowReordering",!0)("width",135)("visible",!1),s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("allowReordering",!0)("width",135),s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("width",135)("allowReordering",!0),s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("allowReordering",!0)("width",135),s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("allowReordering",!0)("width",135)("visible",!0),s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("allowReordering",!0)("width",135),s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("allowReordering",!0)("width",135),s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("width",135)("allowReordering",!0)("visible",!1),s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("allowReordering",!0)("width",135)("visible",!1),s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("allowReordering",!0)("width",135),s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("allowReordering",!0)("width",135)("visible",!1),s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("allowReordering",!0)("width",135)("visible",!1),s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("allowReordering",!0)("width",135),s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("allowReordering",!0)("width",150),s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("allowReordering",!0)("width",135),s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("allowReordering",!0)("width",135),s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("allowReordering",!0)("width",135),s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("allowReordering",!0)("width",135),s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("allowReordering",!0)("width",135),s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("allowReordering",!0)("width",135))},directives:[D.a,S.Sb,S.tb,S.vb,S.Md,S.Od,S.Cc,S.dc,S.g],styles:[""]}),e})();const I=[{path:"",component:(()=>{class e{constructor(e,t,n,a,i,r){this._salesReportService=e,this.snackBar=t,this.fb=n,this.invoiceCommonService=a,this.matDateFormats=i,this.dateAdapter=r,this.InvoiceList=[],this.salesReport=[],this.bucketInfo=[],this.dateForm=this.fb.group({startDate:["",o.H.required],endDate:["",o.H.required]})}ngOnInit(){return Object(r.c)(this,void 0,void 0,(function*(){yield this.setCurrentFyData(),this.getSalesChartData(),this.subscribeOnValueChanges()}))}getTenantDateFormats(){return new Promise((e,t)=>{this.invoiceCommonService.getTenantDateFormats().subscribe(t=>{e(t.data)},t=>{console.log(t),e(t)})})}subscribeOnValueChanges(){this.dateForm.get("startDate").valueChanges.subscribe(e=>{if(e){this.startDate=l(e).format("YYYY-MM-DD"),this.minEndDate=l(this.startDate);const t=this.dateForm.get("endDate").value;t&&l(this.startDate).isAfter(l(t))&&(this.endDate="",this.dateForm.get("endDate").setValue(""))}}),this.dateForm.get("endDate").valueChanges.subscribe(e=>{e&&(this.endDate=e,this.endDate=l(this.endDate).endOf("month").format("YYYY-MM-DD"))})}salesReportDateUpdate(){localStorage.setItem("salesReportStartDate",this.dateForm.controls.startDate.value),localStorage.setItem("salesReportEndDate",this.dateForm.controls.endDate.value),this.getSalesChartData()}setCurrentFyData(){var e,t;return Object(r.c)(this,void 0,void 0,(function*(){this.dateFormats=yield this.getTenantDateFormats(),this.format=(null===(e=this.dateFormats)||void 0===e?void 0:e.invoice_general_date_format)?null===(t=this.dateFormats)||void 0===t?void 0:t.invoice_general_date_format:"DD-MM-YY",Object.assign(this.matDateFormats,{parse:{dateInput:this.format},display:{dateInput:this.format,monthYearLabel:this.format,dateA11yLabel:this.format,monthYearA11yLabel:this.format}}),this.minStartDate=l("2021-04-01"),this.minEndDate=l(this.minStartDate).endOf("month");let n=localStorage.getItem("salesReportStartDate"),a=localStorage.getItem("salesReportEndDate");null!=n&&null!=n&&null!=a&&null!=a?(this.startDate=l(n).format("YYYY-MM-DD"),this.endDate=l(a).format("YYYY-MM-DD")):(this.startDate=l(l().subtract(2,"month")).startOf("month").format("YYYY-MM-DD"),this.endDate=l().endOf("month").format("YYYY-MM-DD")),this.minEndDate=l(this.startDate).endOf("month").format("YYYY-MM-DD"),this.dateForm.patchValue({startDate:this.startDate,endDate:this.endDate})}))}changeDate(e){return l(e).format("DD-MM-YY")}getColor(e){return 0==e?"#e2e2e2":1==e?"#009432":void 0}getSalesChartData(){this._salesReportService.getSalesReport(this.startDate,this.endDate).subscribe(e=>{console.log(e),this.salesChartData=e},e=>{console.error(e)})}ngOnDestroy(){}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275directiveInject"](m),s["\u0275\u0275directiveInject"](h.a),s["\u0275\u0275directiveInject"](o.i),s["\u0275\u0275directiveInject"](g.a),s["\u0275\u0275directiveInject"](d.e),s["\u0275\u0275directiveInject"](d.c))},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-sales-report-landing"]],decls:32,vars:10,consts:[[3,"formGroup"],[1,"container-fluid","pl-0","pr-0"],[1,"row"],[1,"col-12","order-lg-1","order-md-2","col-lg-12","mainSection","pl-1","pr-2"],[1,"row","mt-3"],[1,"row","w-100","pt-2","pb-2"],[1,"col-2"],["appearance","outline",2,"width","80%"],["matInput","","formControlName","startDate",3,"matDatepicker","min","placeholder"],["matSuffix","",3,"for"],["sd",""],["matInput","","formControlName","endDate",3,"matDatepicker","min","placeholder"],["ed",""],[1,"col-1"],["mat-icon-button","","matTooltip","Submit",1,"iconbtn","mr-2",2,"font-weight","normal","margin-left","1%","margin-bottom","1%",3,"click"],[1,"col"],[1,"row","pt-2"],[1,"col-12","h-100","pl-0","pr-0"],[1,"col-12","pl-0","pr-0"],[3,"data"]],template:function(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"form",0),s["\u0275\u0275elementStart"](1,"div",1),s["\u0275\u0275elementStart"](2,"div",2),s["\u0275\u0275elementStart"](3,"div",3),s["\u0275\u0275element"](4,"div",4),s["\u0275\u0275elementStart"](5,"div",5),s["\u0275\u0275elementStart"](6,"div",6),s["\u0275\u0275elementStart"](7,"mat-form-field",7),s["\u0275\u0275elementStart"](8,"mat-label"),s["\u0275\u0275text"](9,"Start Period"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275element"](10,"input",8),s["\u0275\u0275element"](11,"mat-datepicker-toggle",9),s["\u0275\u0275element"](12,"mat-datepicker",null,10),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](14,"div",6),s["\u0275\u0275elementStart"](15,"mat-form-field",7),s["\u0275\u0275elementStart"](16,"mat-label"),s["\u0275\u0275text"](17,"End Period"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275element"](18,"input",11),s["\u0275\u0275element"](19,"mat-datepicker-toggle",9),s["\u0275\u0275element"](20,"mat-datepicker",null,12),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](22,"div",13),s["\u0275\u0275elementStart"](23,"button",14),s["\u0275\u0275listener"]("click",(function(){return t.salesReportDateUpdate()})),s["\u0275\u0275elementStart"](24,"mat-icon"),s["\u0275\u0275text"](25,"done_all"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275element"](26,"div",15),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](27,"div",16),s["\u0275\u0275elementStart"](28,"div",17),s["\u0275\u0275elementStart"](29,"div",2),s["\u0275\u0275elementStart"](30,"div",18),s["\u0275\u0275element"](31,"app-devxtreme",19),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e){const e=s["\u0275\u0275reference"](13),n=s["\u0275\u0275reference"](21);s["\u0275\u0275property"]("formGroup",t.dateForm),s["\u0275\u0275advance"](10),s["\u0275\u0275property"]("matDatepicker",e)("min",t.minStartDate)("placeholder",t.format),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("for",e),s["\u0275\u0275advance"](7),s["\u0275\u0275property"]("matDatepicker",n)("min",t.minEndDate)("placeholder",t.format),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("for",n),s["\u0275\u0275advance"](12),s["\u0275\u0275property"]("data",t.salesChartData)}},directives:[o.J,o.w,o.n,v.c,v.g,u.b,o.e,b.g,o.v,o.l,b.i,v.i,b.f,w.a,f.a,x.a,y],styles:[".iconbtn[_ngcontent-%COMP%]{background-color:#c92020;color:#fff;padding:0;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.trend-button-inactive[_ngcontent-%COMP%]{margin-top:3px!important;line-height:8px;width:26px;height:26px;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.trend-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}"]}),e})()}];let E=(()=>{class e{}return e.\u0275mod=s["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=s["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.k.forChild(I)],i.k]}),e})();var R=n("ClZT"),F=n("WYlB"),C=n("XPKZ"),_=n("STbY"),Y=n("8hBH"),P=n("TU8p");let T=(()=>{class e{}return e.\u0275mod=s["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=s["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[a.CommonModule,E,D.b,v.e,u.c,o.p,o.E,P.b,f.b,b.h,x.b,w.b,_.e,Y.c,R.b,F.b,C.b]]}),e})()}}]);