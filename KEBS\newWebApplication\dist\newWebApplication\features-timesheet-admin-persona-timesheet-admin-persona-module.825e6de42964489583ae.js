(window.webpackJsonp=window.webpackJsonp||[]).push([[671,765,821,822,983,987,990,991],{Hnhj:function(e,t,n){"use strict";n.r(t),n.d(t,"TimesheetAdminPersonaModule",(function(){return $}));var r=n("ofXK"),o=n("3Pt+"),a=n("+0xr"),i=n("bTqV"),l=n("NFeN"),s=n("Qu3c"),m=n("STbY"),d=n("kmnG"),p=n("qFsG"),c=n("d3UM"),u=n("iadO"),h=n("FKr1"),f=n("jaxi"),E=n("wZkO"),S=n("/1cH"),g=n("Xi0T"),C=n("tyNb"),v=n("mrSG"),I=n("xG9w"),y=n("wd/R"),b=n("fXoL"),x=n("JLuW"),F=n("tk/3"),L=n("BVzC");let w=(()=>{class e{constructor(e,t){this.http=e,this.errorService=t}fetchCostCenters(){return new Promise(e=>{this.http.post("/api/tsPrimary/getCostCentreList",{}).subscribe(t=>Object(v.c)(this,void 0,void 0,(function*(){let n=[];"S"==t.messType&&t.data.length>0&&(n=t.data),e(n)})),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while searching costcentre data",e&&e.params?e.params:e&&e.error?e.error.params:{})})})}updateDetailsForTSAdminPersona(e){return new Promise(t=>{this.http.post("/api/tsPrimary/updateDetailsForTSAdminPersona",{data:e}).subscribe(e=>Object(v.c)(this,void 0,void 0,(function*(){t(e)})),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while updating data",e&&e.params?e.params:e&&e.error?e.error.params:{})})})}}return e.\u0275fac=function(t){return new(t||e)(b["\u0275\u0275inject"](F.c),b["\u0275\u0275inject"](L.a))},e.\u0275prov=b["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var k=n("dNgK"),O=n("me71"),P=n("qFYv"),U=n("TmG/");function _(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"mat-option",35),b["\u0275\u0275text"](1),b["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;b["\u0275\u0275propertyInterpolate"]("matTooltip",e.name),b["\u0275\u0275property"]("value",e),b["\u0275\u0275advance"](1),b["\u0275\u0275textInterpolate1"](" ",e.name," ")}}function T(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"mat-option",35),b["\u0275\u0275text"](1),b["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;b["\u0275\u0275propertyInterpolate1"]("matTooltip","",e.name," "),b["\u0275\u0275property"]("value",e),b["\u0275\u0275advance"](1),b["\u0275\u0275textInterpolate1"](" ",e.name," ")}}const A=function(){return["name","role"]};function N(e,t){if(1&e){const e=b["\u0275\u0275getCurrentView"]();b["\u0275\u0275elementStart"](0,"div",12),b["\u0275\u0275elementStart"](1,"div",1),b["\u0275\u0275elementStart"](2,"div",13),b["\u0275\u0275elementStart"](3,"span",14),b["\u0275\u0275text"](4," Cost Center Updatation "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](5,"form",15),b["\u0275\u0275elementStart"](6,"div",16),b["\u0275\u0275elementStart"](7,"div",17),b["\u0275\u0275element"](8,"app-user-image",18),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](9,"div",19),b["\u0275\u0275elementStart"](10,"app-input-search-huge-input",20),b["\u0275\u0275listener"]("click",(function(){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().updateOid()})),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](11,"div",19),b["\u0275\u0275elementStart"](12,"mat-form-field",21),b["\u0275\u0275elementStart"](13,"mat-label"),b["\u0275\u0275text"](14,"Month Sub"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](15,"mat-select",22),b["\u0275\u0275template"](16,_,2,3,"mat-option",23),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](17,"div",19),b["\u0275\u0275elementStart"](18,"mat-form-field",21),b["\u0275\u0275elementStart"](19,"mat-label"),b["\u0275\u0275text"](20,"Location"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](21,"mat-select",24),b["\u0275\u0275template"](22,T,2,3,"mat-option",23),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](23,"div",16),b["\u0275\u0275elementStart"](24,"div",25),b["\u0275\u0275element"](25,"app-input-search",26),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](26,"div",25),b["\u0275\u0275element"](27,"app-input-search",27),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](28,"div",28),b["\u0275\u0275elementStart"](29,"mat-form-field",21),b["\u0275\u0275elementStart"](30,"mat-label"),b["\u0275\u0275text"](31,"Date Range"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](32,"mat-date-range-input",29),b["\u0275\u0275listener"]("click",(function(){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275reference"](37).open()})),b["\u0275\u0275element"](33,"input",30),b["\u0275\u0275element"](34,"input",31),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](35,"mat-datepicker-toggle",32),b["\u0275\u0275element"](36,"mat-date-range-picker",null,33),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](38,"div",16),b["\u0275\u0275elementStart"](39,"div",34),b["\u0275\u0275elementStart"](40,"button",3),b["\u0275\u0275listener"]("click",(function(){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().updateCC()})),b["\u0275\u0275text"](41,"Update"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()}if(2&e){const e=b["\u0275\u0275reference"](37),t=b["\u0275\u0275nextContext"]();b["\u0275\u0275advance"](5),b["\u0275\u0275property"]("formGroup",t.formCC),b["\u0275\u0275advance"](3),b["\u0275\u0275property"]("id",t.selectedUserOid),b["\u0275\u0275advance"](2),b["\u0275\u0275property"]("label","Employee Name")("optionLabel",b["\u0275\u0275pureFunction0"](11,A))("apiUri","/api/master/searchConsultantsForTSAdminPersona"),b["\u0275\u0275advance"](6),b["\u0275\u0275property"]("ngForOf",t.monthList),b["\u0275\u0275advance"](6),b["\u0275\u0275property"]("ngForOf",t.filteredLocationList),b["\u0275\u0275advance"](3),b["\u0275\u0275property"]("list",t.costCentreItems),b["\u0275\u0275advance"](2),b["\u0275\u0275property"]("list",t.costCentreItems),b["\u0275\u0275advance"](5),b["\u0275\u0275property"]("rangePicker",e),b["\u0275\u0275advance"](3),b["\u0275\u0275property"]("for",e)}}function R(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"mat-option",35),b["\u0275\u0275text"](1),b["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;b["\u0275\u0275propertyInterpolate"]("matTooltip",e.name),b["\u0275\u0275property"]("value",e),b["\u0275\u0275advance"](1),b["\u0275\u0275textInterpolate1"](" ",e.name," ")}}const M=function(){return["name"]};function V(e,t){if(1&e){const e=b["\u0275\u0275getCurrentView"]();b["\u0275\u0275elementStart"](0,"div",36),b["\u0275\u0275elementStart"](1,"div",1),b["\u0275\u0275elementStart"](2,"div",13),b["\u0275\u0275elementStart"](3,"span",14),b["\u0275\u0275text"](4," MIS Hours Updatation "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](5,"form",15),b["\u0275\u0275elementStart"](6,"div",16),b["\u0275\u0275elementStart"](7,"div",17),b["\u0275\u0275element"](8,"app-user-image",18),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](9,"div",19),b["\u0275\u0275element"](10,"app-input-search-huge-input",37),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](11,"div",19),b["\u0275\u0275elementStart"](12,"mat-form-field",21),b["\u0275\u0275elementStart"](13,"mat-label"),b["\u0275\u0275text"](14,"Month Sub"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](15,"mat-select",38),b["\u0275\u0275template"](16,R,2,3,"mat-option",23),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](17,"div",19),b["\u0275\u0275elementStart"](18,"mat-form-field",39),b["\u0275\u0275elementStart"](19,"mat-label"),b["\u0275\u0275text"](20,"MIS Hours"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](21,"input",40),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](22,"div",16),b["\u0275\u0275elementStart"](23,"div",25),b["\u0275\u0275elementStart"](24,"mat-form-field",39),b["\u0275\u0275elementStart"](25,"mat-label"),b["\u0275\u0275text"](26,"Year"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](27,"input",41),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](28,"div",16),b["\u0275\u0275elementStart"](29,"div",34),b["\u0275\u0275elementStart"](30,"button",3),b["\u0275\u0275listener"]("click",(function(){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().updateMISHours()})),b["\u0275\u0275text"](31,"Update"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()}if(2&e){const e=b["\u0275\u0275nextContext"]();b["\u0275\u0275advance"](5),b["\u0275\u0275property"]("formGroup",e.formMIS),b["\u0275\u0275advance"](5),b["\u0275\u0275property"]("label","Employee Name")("optionLabel",b["\u0275\u0275pureFunction0"](5,M))("apiUri","/api/master/searchConsultantsForTSAdminPersona"),b["\u0275\u0275advance"](6),b["\u0275\u0275property"]("ngForOf",e.monthList)}}function D(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"mat-option",35),b["\u0275\u0275text"](1),b["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;b["\u0275\u0275propertyInterpolate"]("matTooltip",e.name),b["\u0275\u0275property"]("value",e),b["\u0275\u0275advance"](1),b["\u0275\u0275textInterpolate1"](" ",e.name," ")}}function q(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"mat-option",35),b["\u0275\u0275text"](1),b["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;b["\u0275\u0275propertyInterpolate"]("matTooltip",e.name),b["\u0275\u0275property"]("value",e),b["\u0275\u0275advance"](1),b["\u0275\u0275textInterpolate1"](" ",e.name," ")}}function B(e,t){if(1&e){const e=b["\u0275\u0275getCurrentView"]();b["\u0275\u0275elementStart"](0,"div",42),b["\u0275\u0275elementStart"](1,"div",1),b["\u0275\u0275elementStart"](2,"div",13),b["\u0275\u0275elementStart"](3,"span",14),b["\u0275\u0275text"](4," Exit Employee Details Insertion "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](5,"form",15),b["\u0275\u0275elementStart"](6,"div",16),b["\u0275\u0275elementStart"](7,"div",19),b["\u0275\u0275elementStart"](8,"mat-form-field",39),b["\u0275\u0275elementStart"](9,"mat-label"),b["\u0275\u0275text"](10,"Associate OID"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](11,"input",43),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](12,"div",19),b["\u0275\u0275elementStart"](13,"mat-form-field",39),b["\u0275\u0275elementStart"](14,"mat-label"),b["\u0275\u0275text"](15,"Day Type"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](16,"input",44),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](17,"div",19),b["\u0275\u0275elementStart"](18,"mat-form-field",39),b["\u0275\u0275elementStart"](19,"mat-label"),b["\u0275\u0275text"](20,"Cost Center Type"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](21,"input",45),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](22,"div",19),b["\u0275\u0275elementStart"](23,"mat-form-field",39),b["\u0275\u0275elementStart"](24,"mat-label"),b["\u0275\u0275text"](25,"Date"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](26,"input",46),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](27,"div",16),b["\u0275\u0275elementStart"](28,"div",19),b["\u0275\u0275elementStart"](29,"mat-form-field",39),b["\u0275\u0275elementStart"](30,"mat-label"),b["\u0275\u0275text"](31,"Cost Center Code"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](32,"input",47),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](33,"div",19),b["\u0275\u0275elementStart"](34,"mat-form-field",39),b["\u0275\u0275elementStart"](35,"mat-label"),b["\u0275\u0275text"](36,"Cost Center Description"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](37,"input",48),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](38,"div",19),b["\u0275\u0275elementStart"](39,"mat-form-field",39),b["\u0275\u0275elementStart"](40,"mat-label"),b["\u0275\u0275text"](41,"Location ID"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](42,"input",49),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](43,"div",19),b["\u0275\u0275elementStart"](44,"mat-form-field",39),b["\u0275\u0275elementStart"](45,"mat-label"),b["\u0275\u0275text"](46,"Location"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](47,"input",50),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](48,"div",16),b["\u0275\u0275elementStart"](49,"div",19),b["\u0275\u0275elementStart"](50,"mat-form-field",39),b["\u0275\u0275elementStart"](51,"mat-label"),b["\u0275\u0275text"](52,"Hours"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](53,"input",51),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](54,"div",19),b["\u0275\u0275elementStart"](55,"mat-form-field",39),b["\u0275\u0275elementStart"](56,"mat-label"),b["\u0275\u0275text"](57,"MIS Hours"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](58,"input",52),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](59,"div",19),b["\u0275\u0275elementStart"](60,"mat-form-field",39),b["\u0275\u0275elementStart"](61,"mat-label"),b["\u0275\u0275text"](62,"Status Code"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](63,"input",53),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](64,"div",16),b["\u0275\u0275elementStart"](65,"div",19),b["\u0275\u0275elementStart"](66,"mat-form-field",21),b["\u0275\u0275elementStart"](67,"mat-label"),b["\u0275\u0275text"](68,"Month Sub"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](69,"mat-select",54),b["\u0275\u0275template"](70,D,2,3,"mat-option",23),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](71,"div",19),b["\u0275\u0275elementStart"](72,"mat-form-field",21),b["\u0275\u0275elementStart"](73,"mat-label"),b["\u0275\u0275text"](74,"Payroll Month"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](75,"mat-select",55),b["\u0275\u0275template"](76,q,2,3,"mat-option",23),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](77,"div",19),b["\u0275\u0275elementStart"](78,"mat-form-field",39),b["\u0275\u0275elementStart"](79,"mat-label"),b["\u0275\u0275text"](80,"Payroll Year"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](81,"input",56),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](82,"div",16),b["\u0275\u0275elementStart"](83,"div",34),b["\u0275\u0275elementStart"](84,"button",3),b["\u0275\u0275listener"]("click",(function(){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().insertExitEmployeeDetails()})),b["\u0275\u0275text"](85,"Insert"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()}if(2&e){const e=b["\u0275\u0275nextContext"]();b["\u0275\u0275advance"](5),b["\u0275\u0275property"]("formGroup",e.formEE),b["\u0275\u0275advance"](65),b["\u0275\u0275property"]("ngForOf",e.monthList),b["\u0275\u0275advance"](6),b["\u0275\u0275property"]("ngForOf",e.monthList)}}function j(e,t){if(1&e){const e=b["\u0275\u0275getCurrentView"]();b["\u0275\u0275elementStart"](0,"div",57),b["\u0275\u0275elementStart"](1,"div",1),b["\u0275\u0275elementStart"](2,"div",13),b["\u0275\u0275elementStart"](3,"span",14),b["\u0275\u0275text"](4," Leave Balance Updation "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](5,"form",15),b["\u0275\u0275elementStart"](6,"div",16),b["\u0275\u0275elementStart"](7,"div",19),b["\u0275\u0275elementStart"](8,"mat-form-field",39),b["\u0275\u0275elementStart"](9,"mat-label"),b["\u0275\u0275text"](10,"Associate ID"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](11,"input",58),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](12,"div",19),b["\u0275\u0275elementStart"](13,"mat-form-field",39),b["\u0275\u0275elementStart"](14,"mat-label"),b["\u0275\u0275text"](15,"Leave ID"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](16,"input",59),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](17,"div",19),b["\u0275\u0275elementStart"](18,"mat-form-field",39),b["\u0275\u0275elementStart"](19,"mat-label"),b["\u0275\u0275text"](20,"Balance"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](21,"input",60),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](22,"div",16),b["\u0275\u0275elementStart"](23,"div",34),b["\u0275\u0275elementStart"](24,"button",3),b["\u0275\u0275listener"]("click",(function(){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().leaveBalanceUpdationFunc()})),b["\u0275\u0275text"](25,"Update"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()}if(2&e){const e=b["\u0275\u0275nextContext"]();b["\u0275\u0275advance"](5),b["\u0275\u0275property"]("formGroup",e.formLBU)}}function G(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"mat-option",35),b["\u0275\u0275text"](1),b["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;b["\u0275\u0275propertyInterpolate1"]("matTooltip","",e.name," "),b["\u0275\u0275property"]("value",e),b["\u0275\u0275advance"](1),b["\u0275\u0275textInterpolate1"](" ",e.name," ")}}function H(e,t){if(1&e){const e=b["\u0275\u0275getCurrentView"]();b["\u0275\u0275elementStart"](0,"div",61),b["\u0275\u0275elementStart"](1,"div",1),b["\u0275\u0275elementStart"](2,"div",13),b["\u0275\u0275elementStart"](3,"span",14),b["\u0275\u0275text"](4," Location & CC Updation "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](5,"form",15),b["\u0275\u0275elementStart"](6,"div",16),b["\u0275\u0275elementStart"](7,"div",17),b["\u0275\u0275element"](8,"app-user-image",18),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](9,"div",19),b["\u0275\u0275elementStart"](10,"app-input-search-huge-input",62),b["\u0275\u0275listener"]("click",(function(){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().updateOidLCCU()})),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](11,"div",25),b["\u0275\u0275element"](12,"app-input-search",63),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](13,"div",19),b["\u0275\u0275elementStart"](14,"mat-form-field",21),b["\u0275\u0275elementStart"](15,"mat-label"),b["\u0275\u0275text"](16,"Location"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](17,"mat-select",64),b["\u0275\u0275template"](18,G,2,3,"mat-option",23),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](19,"div",16),b["\u0275\u0275elementStart"](20,"div",28),b["\u0275\u0275elementStart"](21,"mat-form-field",21),b["\u0275\u0275elementStart"](22,"mat-label"),b["\u0275\u0275text"](23,"Date Range"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](24,"mat-date-range-input",29),b["\u0275\u0275listener"]("click",(function(){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275reference"](29).open()})),b["\u0275\u0275element"](25,"input",65),b["\u0275\u0275element"](26,"input",66),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](27,"mat-datepicker-toggle",32),b["\u0275\u0275element"](28,"mat-date-range-picker",null,33),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](30,"div",16),b["\u0275\u0275elementStart"](31,"div",34),b["\u0275\u0275elementStart"](32,"button",3),b["\u0275\u0275listener"]("click",(function(){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().updateLocationCostCenter()})),b["\u0275\u0275text"](33,"Update"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()}if(2&e){const e=b["\u0275\u0275reference"](29),t=b["\u0275\u0275nextContext"]();b["\u0275\u0275advance"](5),b["\u0275\u0275property"]("formGroup",t.formLCCU),b["\u0275\u0275advance"](3),b["\u0275\u0275property"]("id",t.selectedUserOidLCCU),b["\u0275\u0275advance"](2),b["\u0275\u0275property"]("label","Employee Name")("optionLabel",b["\u0275\u0275pureFunction0"](9,A))("apiUri","/api/master/searchConsultantsForTSAdminPersona"),b["\u0275\u0275advance"](2),b["\u0275\u0275property"]("list",t.costCentreItems),b["\u0275\u0275advance"](6),b["\u0275\u0275property"]("ngForOf",t.filteredLocationList),b["\u0275\u0275advance"](6),b["\u0275\u0275property"]("rangePicker",e),b["\u0275\u0275advance"](3),b["\u0275\u0275property"]("for",e)}}function Y(e,t){if(1&e){const e=b["\u0275\u0275getCurrentView"]();b["\u0275\u0275elementStart"](0,"div",67),b["\u0275\u0275elementStart"](1,"div",1),b["\u0275\u0275elementStart"](2,"div",13),b["\u0275\u0275elementStart"](3,"span",14),b["\u0275\u0275text"](4," SRV36 Fixes "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](5,"form",15),b["\u0275\u0275elementStart"](6,"div",16),b["\u0275\u0275elementStart"](7,"div",19),b["\u0275\u0275elementStart"](8,"mat-form-field",39),b["\u0275\u0275elementStart"](9,"mat-label"),b["\u0275\u0275text"](10,"Submission ID's"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](11,"input",68),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](12,"div",16),b["\u0275\u0275elementStart"](13,"div",34),b["\u0275\u0275elementStart"](14,"button",3),b["\u0275\u0275listener"]("click",(function(){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().updateSRV36Fixes()})),b["\u0275\u0275text"](15,"Fix"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()}if(2&e){const e=b["\u0275\u0275nextContext"]();b["\u0275\u0275advance"](5),b["\u0275\u0275property"]("formGroup",e.formSRVF)}}function W(e,t){if(1&e){const e=b["\u0275\u0275getCurrentView"]();b["\u0275\u0275elementStart"](0,"div",69),b["\u0275\u0275elementStart"](1,"div",1),b["\u0275\u0275elementStart"](2,"div",13),b["\u0275\u0275elementStart"](3,"span",14),b["\u0275\u0275text"](4," Withraw Leave "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](5,"form",15),b["\u0275\u0275elementStart"](6,"div",16),b["\u0275\u0275elementStart"](7,"div",19),b["\u0275\u0275elementStart"](8,"mat-form-field",39),b["\u0275\u0275elementStart"](9,"mat-label"),b["\u0275\u0275text"](10,"Leave Req ID's"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](11,"input",70),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](12,"div",16),b["\u0275\u0275elementStart"](13,"div",34),b["\u0275\u0275elementStart"](14,"button",3),b["\u0275\u0275listener"]("click",(function(){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().updateLeaveId()})),b["\u0275\u0275text"](15,"Fix"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()}if(2&e){const e=b["\u0275\u0275nextContext"]();b["\u0275\u0275advance"](5),b["\u0275\u0275property"]("formGroup",e.formLeave)}}function J(e,t){if(1&e){const e=b["\u0275\u0275getCurrentView"]();b["\u0275\u0275elementStart"](0,"div",71),b["\u0275\u0275elementStart"](1,"div",1),b["\u0275\u0275elementStart"](2,"div",13),b["\u0275\u0275elementStart"](3,"span",14),b["\u0275\u0275text"](4," Activate Leave "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](5,"form",15),b["\u0275\u0275elementStart"](6,"div",16),b["\u0275\u0275elementStart"](7,"div",19),b["\u0275\u0275elementStart"](8,"mat-form-field",39),b["\u0275\u0275elementStart"](9,"mat-label"),b["\u0275\u0275text"](10,"Timesheet Id"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](11,"input",72),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](12,"div",16),b["\u0275\u0275elementStart"](13,"div",34),b["\u0275\u0275elementStart"](14,"button",3),b["\u0275\u0275listener"]("click",(function(){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().updateTimesheetLeaveEntry()})),b["\u0275\u0275text"](15,"Fix"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()}if(2&e){const e=b["\u0275\u0275nextContext"]();b["\u0275\u0275advance"](5),b["\u0275\u0275property"]("formGroup",e.formTsLeave)}}const z=[{path:"",component:(()=>{class e{constructor(e,t,n,r,a){this.sharedLazyLoadedComponentsService=e,this.timesheetAdminPersonaService=t,this.errorService=n,this.fb=r,this._snackBar=a,this.costCenterUpdatation=!1,this.misHoursUpdatation=!1,this.exitEmployeeInsertion=!1,this.leaveBalanceUpdation=!1,this.locationCostCenterUpdation=!1,this.SRV36Fixes=!1,this.costCentreItems=[],this.filteredLocationList=[],this.monthList=[],this.selectedUserOid="",this.monthStartInterval=y().hour(15).minute(0).second(0).millisecond(0),this.dateRangePickerStart=new o.j(y()),this.dateRangePickerEnd=new o.j(y()),this.dateRangePickerStartLCCU=new o.j(y()),this.dateRangePickerEndLCCU=new o.j(y()),this.leave_withdraw=!1,this.leave_activate=!1,this.formCC=this.fb.group({initLoad:[1],employeeNameTSForm:[""],selectedMonthTSForm:[""],selectedLocationTSForm:[""],selectedOldCCTSForm:[""],selectedNewCCTSForm:[""],dateRangePickerStart:[""],dateRangePickerEnd:[""]}),this.formMIS=this.fb.group({initLoad:[1],employeeNameMISForm:[""],selectedMonthMISForm:[""],misHoursUpdatedMISForm:[""],yearMISForm:[""]}),this.formEE=this.fb.group({initLoad:[1],associateOIdEEForm:[""],dayTypeEEForm:[""],objTypeEEForm:[""],objValueEEForm:[""],objDescriptionEEForm:[""],dateEEForm:[""],locationIdEEForm:[""],locationEEForm:[""],hoursEEForm:[""],misHoursEEForm:[""],statusCodeEEForm:[""],monthSubEEForm:[""],payrollMonthEEForm:[""],payrollYearEEForm:[""]}),this.formLBU=this.fb.group({initLoad:[1],associateIdLBUForm:[""],leavIdLBUForm:[""],balanceLBUForm:[""]}),this.selectedUserOidLCCU="",this.formLCCU=this.fb.group({initLoad:[1],employeeNameLCCUForm:[""],selectedCCLCCUForm:[""],selectedLocationLCCUForm:[""],dateRangePickerStartLCCU:[""],dateRangePickerEndLCCU:[""]}),this.formSRVF=this.fb.group({initLoad:[1],submissionIdSRVFForm:[""]}),this.formLeave=this.fb.group({initLoad:[1],leaveId:[""]}),this.formTsLeave=this.fb.group({initLoad:[1],tsLeaveId:[""]})}ngOnInit(){this.changeFilteredCostCentreList(),this.searchLocation("").then(e=>{this.filteredLocationList=e}),this.monthList=[{name:"January",month_num:"01"},{name:"Feburary",month_num:"02"},{name:"March",month_num:"03"},{name:"April",month_num:"04"},{name:"May",month_num:"05"},{name:"June",month_num:"06"},{name:"July",month_num:"07"},{name:"August",month_num:"08"},{name:"September",month_num:"09"},{name:"October",month_num:"10"},{name:"November",month_num:"11"},{name:"December",month_num:"12"}],this.dateRangePickerStart.setValue(this.monthStartInterval),this.dateRangePickerEnd.setValue(this.monthStartInterval),this.dateRangePickerStartLCCU.setValue(this.monthStartInterval),this.dateRangePickerEndLCCU.setValue(this.monthStartInterval)}updateOid(){this.selectedUserOid=JSON.parse(this.formCC.get("employeeNameTSForm").value).oid}updateOidLCCU(){this.selectedUserOidLCCU=JSON.parse(this.formLCCU.get("employeeNameLCCUForm").value).oid}selectView(e){this.clearForm(),1==e?(this.costCenterUpdatation=!0,this.misHoursUpdatation=!1,this.exitEmployeeInsertion=!1,this.leaveBalanceUpdation=!1,this.locationCostCenterUpdation=!1,this.SRV36Fixes=!1):2==e?(this.misHoursUpdatation=!0,this.costCenterUpdatation=!1,this.exitEmployeeInsertion=!1,this.leaveBalanceUpdation=!1,this.locationCostCenterUpdation=!1,this.SRV36Fixes=!1):3==e?(this.exitEmployeeInsertion=!0,this.costCenterUpdatation=!1,this.misHoursUpdatation=!1,this.leaveBalanceUpdation=!1,this.locationCostCenterUpdation=!1,this.SRV36Fixes=!1):4==e?(this.leaveBalanceUpdation=!0,this.exitEmployeeInsertion=!1,this.costCenterUpdatation=!1,this.misHoursUpdatation=!1,this.locationCostCenterUpdation=!1,this.SRV36Fixes=!1):5==e?(this.locationCostCenterUpdation=!0,this.exitEmployeeInsertion=!1,this.costCenterUpdatation=!1,this.misHoursUpdatation=!1,this.leaveBalanceUpdation=!1,this.SRV36Fixes=!1):6==e?(this.SRV36Fixes=!0,this.exitEmployeeInsertion=!1,this.costCenterUpdatation=!1,this.misHoursUpdatation=!1,this.leaveBalanceUpdation=!1,this.locationCostCenterUpdation=!1):7==e?(this.leave_withdraw=!0,this.SRV36Fixes=!1,this.exitEmployeeInsertion=!1,this.costCenterUpdatation=!1,this.misHoursUpdatation=!1,this.leaveBalanceUpdation=!1,this.locationCostCenterUpdation=!1):8==e&&(this.leave_activate=!0,this.leave_withdraw=!1,this.SRV36Fixes=!1,this.exitEmployeeInsertion=!1,this.costCenterUpdatation=!1,this.misHoursUpdatation=!1,this.leaveBalanceUpdation=!1,this.locationCostCenterUpdation=!1)}clearForm(){this.formCC.reset(),this.formMIS.reset(),this.formEE.reset(),this.formLBU.reset(),this.formLCCU.reset(),this.formSRVF.reset(),this.formLeave.reset(),this.formTsLeave.reset()}searchLocation(e){return new Promise(t=>{this.sharedLazyLoadedComponentsService.searchLocation(e).subscribe(e=>Object(v.c)(this,void 0,void 0,(function*(){let n=[];"S"==e.messType&&e.data.length>0&&(n=e.data),t(n)})),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while searching location !",e&&e.params?e.params:e&&e.error?e.error.params:{})})})}changeFilteredCostCentreList(){this.timesheetAdminPersonaService.fetchCostCenters().then(e=>{this.costCentreItems=e})}updateCC(){let e=parseInt(this.formCC.get("selectedOldCCTSForm").value),t=parseInt(this.formCC.get("selectedNewCCTSForm").value),n=I.default.find(this.costCentreItems,(function(t){if(e==parseInt(t.id))return t})),r=n.name.split(" ~ ");n={costCenterType:n.cost_center_type,costCenterValue:r[0],costCenterDescription:r[1]};let o=I.default.find(this.costCentreItems,(function(e){if(t==parseInt(e.id))return e}));r=o.name.split(" ~ "),o={costCenterType:o.cost_center_type,costCenterValue:r[0],costCenterDescription:r[1]};let a={toBeUpdated:1,dataToBeUpdated:{empDetails:{empAssociateOid:JSON.parse(this.formCC.get("employeeNameTSForm").value).oid,empName:JSON.parse(this.formCC.get("employeeNameTSForm").value).name},monthSelected:this.formCC.get("selectedMonthTSForm").value.month_num,oldCostCenter:n,newCostCenter:o,locationSelected:this.formCC.get("selectedLocationTSForm").value.name,dateRange:{dateRangeStart:y(this.formCC.get("dateRangePickerStart").value).format("YYYY-MM-DD"),dateRangeEnd:y(this.formCC.get("dateRangePickerEnd").value).format("YYYY-MM-DD")}}};this.timesheetAdminPersonaService.updateDetailsForTSAdminPersona(a).then(e=>{this._snackBar.open(e.messText,"close",{duration:2e3})})}updateMISHours(){let e={toBeUpdated:2,dataToBeUpdated:{empDetails:{empAssociateOid:JSON.parse(this.formMIS.get("employeeNameMISForm").value).oid,empName:JSON.parse(this.formMIS.get("employeeNameMISForm").value).name},monthSelected:this.formMIS.get("selectedMonthMISForm").value.month_num,yearSelected:this.formMIS.get("yearMISForm").value,misHoursValue:this.formMIS.get("misHoursUpdatedMISForm").value}};this.timesheetAdminPersonaService.updateDetailsForTSAdminPersona(e).then(e=>{this._snackBar.open(e.messText,"close",{duration:2e3})})}insertExitEmployeeDetails(){let e={toBeUpdated:3,dataToBeUpdated:{associateOId:this.formEE.get("associateOIdEEForm").value,dayType:this.formEE.get("dayTypeEEForm").value,objType:this.formEE.get("objTypeEEForm").value,objValue:this.formEE.get("objValueEEForm").value,objDescription:this.formEE.get("objDescriptionEEForm").value,date:this.formEE.get("dateEEForm").value,locationId:this.formEE.get("locationIdEEForm").value,location:this.formEE.get("locationEEForm").value,hours:this.formEE.get("hoursEEForm").value,misHours:this.formEE.get("misHoursEEForm").value,statusCode:this.formEE.get("statusCodeEEForm").value,monthSub:this.formEE.get("monthSubEEForm").value.month_num,payrollMonth:this.formEE.get("payrollMonthEEForm").value.month_num,payrollYear:this.formEE.get("payrollYearEEForm").value}};this.timesheetAdminPersonaService.updateDetailsForTSAdminPersona(e).then(e=>{this._snackBar.open(e.messText,"close",{duration:2e3})})}leaveBalanceUpdationFunc(){let e={toBeUpdated:4,dataToBeUpdated:{associateId:this.formLBU.get("associateIdLBUForm").value,leavId:this.formLBU.get("leavIdLBUForm").value,balance:this.formLBU.get("balanceLBUForm").value}};this.timesheetAdminPersonaService.updateDetailsForTSAdminPersona(e).then(e=>{this._snackBar.open(e.messText,"close",{duration:2e3})})}updateLocationCostCenter(){let e=parseInt(this.formLCCU.get("selectedCCLCCUForm").value);console.log(e);let t=I.default.find(this.costCentreItems,(function(t){if(e==parseInt(t.id))return t}));console.log(t);let n=t.name.split(" ~ ");console.log(n),t={costCenterType:t.cost_center_type,costCenterValue:n[0],costCenterDescription:n[1]},console.log(t);let r={toBeUpdated:5,dataToBeUpdated:{empDetails:{empAssociateOid:JSON.parse(this.formLCCU.get("employeeNameLCCUForm").value).oid,empName:JSON.parse(this.formLCCU.get("employeeNameLCCUForm").value).name},costCenter:t,location:this.formLCCU.get("selectedLocationLCCUForm").value,dateRange:{dateRangeStart:y(this.formLCCU.get("dateRangePickerStartLCCU").value).format("YYYY-MM-DD"),dateRangeEnd:y(this.formLCCU.get("dateRangePickerEndLCCU").value).format("YYYY-MM-DD")}}};this.timesheetAdminPersonaService.updateDetailsForTSAdminPersona(r).then(e=>{this._snackBar.open(e.messText,"close",{duration:2e3})})}updateSRV36Fixes(){let e={toBeUpdated:6,dataToBeUpdated:{submissionIds:this.formSRVF.get("submissionIdSRVFForm").value}};this.timesheetAdminPersonaService.updateDetailsForTSAdminPersona(e).then(e=>{this._snackBar.open(e.messText,"close",{duration:2e3})})}updateLeaveId(){let e={toBeUpdated:7,dataToBeUpdated:{submissionIds:this.formLeave.get("leaveId").value}};this.timesheetAdminPersonaService.updateDetailsForTSAdminPersona(e).then(e=>{this._snackBar.open(e.messText,"close",{duration:2e3})})}updateTimesheetLeaveEntry(){let e={toBeUpdated:8,dataToBeUpdated:{submissionIds:this.formTsLeave.get("tsLeaveId").value}};this.timesheetAdminPersonaService.updateDetailsForTSAdminPersona(e).then(e=>{this._snackBar.open(e.messText,"close",{duration:2e3})})}}return e.\u0275fac=function(t){return new(t||e)(b["\u0275\u0275directiveInject"](x.a),b["\u0275\u0275directiveInject"](w),b["\u0275\u0275directiveInject"](L.a),b["\u0275\u0275directiveInject"](o.i),b["\u0275\u0275directiveInject"](k.a))},e.\u0275cmp=b["\u0275\u0275defineComponent"]({type:e,selectors:[["app-timesheet-admin-persona-landing-page"]],decls:36,vars:8,consts:[[1,"p-0","container-fluid","ts-admin-persona"],[1,"row"],[1,"col-2"],["mat-flat-button","",1,"leave-btn",3,"click"],["class","cost_center_updatation",4,"ngIf"],["class","mis_hours_updatation",4,"ngIf"],["class","exit_employee_insertion",4,"ngIf"],["class","leave_balance_updation",4,"ngIf"],["class","location_cost_center_updation",4,"ngIf"],["class","srv36_fixes",4,"ngIf"],["class","leave_is_active",4,"ngIf"],["class","activate_leave",4,"ngIf"],[1,"cost_center_updatation"],[1,"col-12","center","pt-2"],[2,"font-size","medium","font-weight","500","color","#cf0001"],[3,"formGroup"],[1,"row","pt-2"],[1,"pl-4","pt-1"],[3,"id"],[1,"col-3"],["formControlName","employeeNameTSForm",2,"width","100%",3,"label","optionLabel","apiUri","click"],["appearance","outline",2,"width","100%"],["formControlName","selectedMonthTSForm","required",""],[3,"value","matTooltip",4,"ngFor","ngForOf"],["formControlName","selectedLocationTSForm","required",""],[1,"col-3","pl-4"],["required","true","placeholder","Old Cost Center","formControlName","selectedOldCCTSForm",2,"width","100%",3,"list"],["required","true","placeholder","New Cost Center","formControlName","selectedNewCCTSForm",2,"width","100%",3,"list"],["matTooltip","Change Date Range",1,"col-4"],[3,"rangePicker","click"],["matStartDate","","formControlName","dateRangePickerStart","placeholder","Start Date","readonly",""],["matEndDate","","formControlName","dateRangePickerEnd","placeholder","End Date","readonly",""],["matIconSuffix","",3,"for"],["dateRangePicker",""],[1,"col","d-flex","justify-content-center"],[3,"value","matTooltip"],[1,"mis_hours_updatation"],["formControlName","employeeNameMISForm",2,"width","100%",3,"label","optionLabel","apiUri"],["formControlName","selectedMonthMISForm","required",""],["appearance","outline"],["matInput","","type","text","required","","formControlName","misHoursUpdatedMISForm"],["matInput","","type","text","required","","formControlName","yearMISForm"],[1,"exit_employee_insertion"],["matInput","","type","text","required","","formControlName","associateOIdEEForm"],["matInput","","type","text","required","","formControlName","dayTypeEEForm"],["matInput","","type","text","required","","formControlName","objTypeEEForm"],["matInput","","type","text","required","","formControlName","dateEEForm"],["matInput","","type","text","required","","formControlName","objValueEEForm"],["matInput","","type","text","required","","formControlName","objDescriptionEEForm"],["matInput","","type","text","required","","formControlName","locationIdEEForm"],["matInput","","type","text","required","","formControlName","locationEEForm"],["matInput","","type","text","required","","formControlName","hoursEEForm"],["matInput","","type","text","required","","formControlName","misHoursEEForm"],["matInput","","type","text","required","","formControlName","statusCodeEEForm"],["formControlName","monthSubEEForm","required",""],["formControlName","payrollMonthEEForm","required",""],["matInput","","type","text","required","","formControlName","payrollYearEEForm"],[1,"leave_balance_updation"],["matInput","","type","text","required","","formControlName","associateIdLBUForm"],["matInput","","type","text","required","","formControlName","leavIdLBUForm"],["matInput","","type","text","required","","formControlName","balanceLBUForm"],[1,"location_cost_center_updation"],["formControlName","employeeNameLCCUForm",2,"width","100%",3,"label","optionLabel","apiUri","click"],["required","true","placeholder","Cost Center","formControlName","selectedCCLCCUForm",2,"width","100%",3,"list"],["formControlName","selectedLocationLCCUForm","required",""],["matStartDate","","formControlName","dateRangePickerStartLCCU","placeholder","Start Date","readonly",""],["matEndDate","","formControlName","dateRangePickerEndLCCU","placeholder","End Date","readonly",""],[1,"srv36_fixes"],["matInput","","type","text","required","","formControlName","submissionIdSRVFForm"],[1,"leave_is_active"],["matInput","","type","text","required","","formControlName","leaveId"],[1,"activate_leave"],["matInput","","type","text","required","","formControlName","tsLeaveId"]],template:function(e,t){1&e&&(b["\u0275\u0275elementStart"](0,"div",0),b["\u0275\u0275elementStart"](1,"div",1),b["\u0275\u0275elementStart"](2,"div",2),b["\u0275\u0275elementStart"](3,"button",3),b["\u0275\u0275listener"]("click",(function(){return t.selectView(1)})),b["\u0275\u0275text"](4,"Cost Center Updation"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](5,"div",2),b["\u0275\u0275elementStart"](6,"button",3),b["\u0275\u0275listener"]("click",(function(){return t.selectView(2)})),b["\u0275\u0275text"](7,"MIS Hours Updation"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](8,"div",2),b["\u0275\u0275elementStart"](9,"button",3),b["\u0275\u0275listener"]("click",(function(){return t.selectView(3)})),b["\u0275\u0275text"](10,"Exit Employee"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](11,"div",2),b["\u0275\u0275elementStart"](12,"button",3),b["\u0275\u0275listener"]("click",(function(){return t.selectView(4)})),b["\u0275\u0275text"](13,"Leave Balance Updation"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](14,"div",2),b["\u0275\u0275elementStart"](15,"button",3),b["\u0275\u0275listener"]("click",(function(){return t.selectView(5)})),b["\u0275\u0275text"](16,"Location & Cost Center Updation"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](17,"div",1),b["\u0275\u0275elementStart"](18,"div",2),b["\u0275\u0275elementStart"](19,"button",3),b["\u0275\u0275listener"]("click",(function(){return t.selectView(6)})),b["\u0275\u0275text"](20,"SRV36 Fixes"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](21,"div",2),b["\u0275\u0275elementStart"](22,"button",3),b["\u0275\u0275listener"]("click",(function(){return t.selectView(7)})),b["\u0275\u0275text"](23,"Leave Withdraw"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](24,"div",2),b["\u0275\u0275elementStart"](25,"button",3),b["\u0275\u0275listener"]("click",(function(){return t.selectView(8)})),b["\u0275\u0275text"](26,"Activate Leave"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](27,"br"),b["\u0275\u0275template"](28,N,42,12,"div",4),b["\u0275\u0275template"](29,V,32,6,"div",5),b["\u0275\u0275template"](30,B,86,3,"div",6),b["\u0275\u0275template"](31,j,26,1,"div",7),b["\u0275\u0275template"](32,H,34,10,"div",8),b["\u0275\u0275template"](33,Y,16,1,"div",9),b["\u0275\u0275template"](34,W,16,1,"div",10),b["\u0275\u0275template"](35,J,16,1,"div",11),b["\u0275\u0275elementEnd"]()),2&e&&(b["\u0275\u0275advance"](28),b["\u0275\u0275property"]("ngIf",t.costCenterUpdatation),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf",t.misHoursUpdatation),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf",t.exitEmployeeInsertion),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf",t.leaveBalanceUpdation),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf",t.locationCostCenterUpdation),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf",t.SRV36Fixes),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf",t.leave_withdraw),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf",t.leave_activate))},directives:[i.a,r.NgIf,o.J,o.w,o.n,O.a,P.a,o.v,o.l,d.c,d.g,c.c,o.F,r.NgForOf,U.a,s.a,u.d,u.l,o.e,u.k,u.i,u.e,h.p,p.b],styles:[".ts-admin-persona[_ngcontent-%COMP%]   table[_ngcontent-%COMP%]{width:99%}.ts-admin-persona[_ngcontent-%COMP%]   .iconsSize[_ngcontent-%COMP%]{background:transparent;font-size:21px!important;color:#545352!important}.ts-admin-persona[_ngcontent-%COMP%]   .headingBold[_ngcontent-%COMP%]{color:#cf0001;font-weight:500;font-size:14px}.ts-admin-persona[_ngcontent-%COMP%]   .mat-field-name[_ngcontent-%COMP%]{font-size:15px}.ts-admin-persona[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff;font-weight:400;font-size:13px!important;min-width:60px;line-height:33px;padding:0 8px;border-radius:4px;margin-right:6px!important;margin-bottom:3px}.ts-admin-persona[_ngcontent-%COMP%]   .mat-header-row[_ngcontent-%COMP%], .ts-admin-persona[_ngcontent-%COMP%]   .mat-row[_ngcontent-%COMP%]{min-height:30px}.ts-admin-persona[_ngcontent-%COMP%]   .center[_ngcontent-%COMP%]{text-align:center}.ts-admin-persona[_ngcontent-%COMP%]   .leave-btn[_ngcontent-%COMP%]{height:37px;color:#fff;font-size:13px;background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%);border-radius:6px;margin-top:15px!important}.ts-admin-persona[_ngcontent-%COMP%]   .header-card-title[_ngcontent-%COMP%]{font-size:14px;color:#66615b;font-weight:400;vertical-align:baseline-middle}.ts-admin-persona[_ngcontent-%COMP%]   .field-title[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#6e7b8f}"]}),e})()}];let X=(()=>{class e{}return e.\u0275mod=b["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=b["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[C.k.forChild(z)],C.k]}),e})(),$=(()=>{class e{}return e.\u0275mod=b["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=b["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},providers:[h.n,u.h],imports:[[r.CommonModule,X,o.p,o.E,a.m,i.b,l.b,s.b,p.c,d.e,c.d,m.e,u.h,h.n,f.c,E.g,S.c,g.a]]}),e})()},hJL4:function(e,t,n){"use strict";n.d(t,"a",(function(){return p}));var r=n("mrSG"),o=n("XNiG"),a=n("xG9w"),i=n("fXoL"),l=n("tk/3"),s=n("LcQX"),m=n("XXEo"),d=n("flaP");let p=(()=>{class e{constructor(e,t,n,r){this.http=e,this.UtilityService=t,this.loginService=n,this.roleService=r,this.msg=new o.b,this.taskStatusColor=[],this.approveRequest=(e,t)=>this.http.post("/api/isa/request/approveResourceRequest",{requestDetails:e,approverOid:t}),this.rejectRequest=(e,t)=>this.http.post("/api/isa/request/rejectResourceRequest",{requestDetails:e,approverOid:t}),this.getTaskStatusColor()}getAllRequestsOfUser(e,t,n,r,o,a,i){let l=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getAllRequestsOfUser",{oid:e,statusId:t,statusCode:n,objectIds:r,skip:o,limit:a,filterConfig:i,orgIds:l})}getAllRoleAccess(){return a.where(this.roleService.roles,{application_id:139})}getRequestsBasedOnStatus(e,t,n,r,o,a,i){let l=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getRequestsBasedOnStatus",{oid:e,statusId:t,statusCode:n,objectIds:r,skip:o,limit:a,filterConfig:i,orgIds:l})}getRequestsForAwaitingApproval(e,t,n,r){return this.http.post("/api/isa/request/getRequestsForAwaitingApproval",{oid:e,skip:t,limit:n,filterConfig:r})}getObjectIdsBasedOnOid(e){return this.http.post("/api/isa/request/getObjectIdsBasedOnOid",{oid:e})}getStatusCountBasedOnOid(e,t,n,r){let o=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getStatusCountBasedOnOid",{oid:e,objectIds:t,dataTypeArray:n,filterConfig:r,orgIds:o})}getHeaderCount(e){return this.http.post("/api/isa/request/getHeaderCount",{oid:e})}getRequestBasedOnStatus(e,t){return this.http.post("/api/isa/request/getRequestBasedOnStatus",{oid:e,status:t})}getDataTypeArray(){return this.http.post("/api/isa/request/getISAStatusMasterDataUDRF",{})}sendMsg(e){this.msg.next(e)}getMsg(){return this.msg.asObservable()}getTaskStatusColor(){return new Promise((e,t)=>{this.http.post("/api/isa/request/getTaskStatusNameColor",{}).subscribe(t=>(this.taskStatusColor=t,e(t)),e=>{})})}getAllRequestsForRmg(e){return this.http.post("/api/isa/request/getAllRequestsForRmg",{filterConfig:e})}getTotalForRmg(e){return new Promise((t,n)=>{this.http.post("/api/isa/request/getTotalForRmg",{filterConfig:e}).subscribe(e=>t(e),e=>{})})}getDefaultISAStatusToDisplay(){return this.http.post("/api/isa/request/getISADefaultStatusToDisplay",{})}getCTCbreakUpForSkillSet(e){return new Promise((t,n)=>{this.http.post("/api/isa/request/getCTCbreakUpForSkillSet",e).subscribe(e=>t(e.data),e=>{n(e)})})}getCurrentUserRole(){return this.roleService&&this.roleService.roles&&this.roleService.roles.length>0?this.roleService.roles[0].role_id:null}getTemplateForUser(e){return this.http.post("/api/isa/request/getVisibleFormFieldsForUser",e)}saveAndApproveRequest(e){return this.http.post("/api/isa/request/updBudgetInfoByRequestId",e)}getVisibilityMatrix(e){return this.http.post("/api/isa/request/getAuthorzedFieldsByRoleId",e)}getISAAttachmentFromS3(e){return this.http.post("/api/isa/attachment/getISAAttachmentFromS3",{key:e})}getSkillType(){return this.http.post("/api/isa/request/getSkillType",{})}getSkillLevel(){return this.http.post("/api/isa/request/getSkillLevel",{})}getSkillExperience(){return this.http.post("/api/isa/request/getSkillExperience",{})}getSkillName(e){return this.http.post("/api/isa/request/getSkillName",{skill_type_id:e})}getIsaActivityReportDownload(){return this.http.post("/api/isa/request/getIsaActivityReportDownload",{})}getISASLAreport(e){return this.http.post("/api/isa/request/getISASLAreport",{filterConfig:e})}getISACustomReport(e){return this.http.post("/api/isa/request/getISACustomReport",{filterConfig:e})}getPositionType(){return this.http.post("/api/employee360/masterData/getPositionType",{})}calculateNetCostBasedOnPosition(e,t,n,o,i,l,s){return Object(r.c)(this,void 0,void 0,(function*(){let r;r=l&&l.length>1&&(yield this.getManpowerCostByOId(l,n,i,2))||(yield this.getManpowerCostBasedOnPosition(e,t,n,i,s));let m=yield this.getNonManpowerCost(t,n,o,i,2),d=yield this.getAllocatedCost(),p=0;p=(r?r.cost:0)+m.length>0?a.reduce(a.pluck(m,"cost"),(e,t)=>e+t,0):0;let c=d.length>0?a.reduce(a.pluck(d,"percentage"),(e,t)=>e+t,0):0;return{cost:p,currency:r&&r.currency_code?r.currency_code:"",manpowerCost:r,nonManpowerCost:m,allocatedCost:d,allocatedCostValue:p*(c/100)}}))}getManpowerCostBasedOnPosition(e,t,n,r,o){return new Promise((a,i)=>{this.http.post("/api/isa/configuration/getManpowerCostBasedOnPosition",{skillRoleId:e,nationalityId:t,locationGroupId:n,unit:r,position:o}).subscribe(e=>a(e),e=>(console.log(e),i(e)))})}getNonManpowerCost(e,t,n,r,o){return new Promise((a,i)=>{this.http.post("/api/project/getNonManpowerCost",{nationalityId:e,locationGroupId:t,locationId:n,unit:r,currency_id:o}).subscribe(e=>a(e),e=>(console.log(e),i(e)))})}getAllocatedCost(){return new Promise((e,t)=>{this.http.post("/api/project/getAllocatedCost","").subscribe(t=>e(t),e=>(console.log(e),t(e)))})}getManpowerCostByOId(e,t,n,r){return new Promise((o,a)=>{this.http.post("/api/project/getEmployeeSalary",{oid:e,location_group_id:t,unit_id:n,currency_id:r}).subscribe(e=>o(e),e=>(console.log(e),a(e)))})}getSkillNameMaster(){return this.http.post("/api/isa/configuration/getSkillName",{})}getVendorOid(e,t){return this.http.post("/api/isa/request/getVendorOid",{vendorNames:e,docID:t})}getVendorList(e,t){return this.http.post("/api/isa/request/getVendorList",{documentID:e,limitval:t})}removeVendor(e,t){return this.http.post("/api/isa/request/removeVendors",{vendorOid:e,documentID:t})}checkVendorOrNot(){return this.http.post("/api/isa/request/checkVendorOrNot",{})}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275inject"](l.c),i["\u0275\u0275inject"](s.a),i["\u0275\u0275inject"](m.a),i["\u0275\u0275inject"](d.a))},e.\u0275prov=i["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},qFYv:function(e,t,n){"use strict";n.d(t,"a",(function(){return b}));var r=n("fXoL"),o=n("tk/3"),a=n("XNiG"),i=n("3Pt+"),l=n("NJ67"),s=n("1G5W"),m=n("Kj3r"),d=n("XXEo"),p=n("kmnG"),c=n("ofXK"),u=n("qFsG"),h=n("/1cH"),f=n("bTqV"),E=n("NFeN"),S=n("Qu3c"),g=n("FKr1");function C(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"button",7),r["\u0275\u0275listener"]("click",(function(){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"]().checkAndClearInput(!0)})),r["\u0275\u0275elementStart"](1,"mat-icon",8),r["\u0275\u0275text"](2," close "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}}function v(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"mat-option",9),r["\u0275\u0275elementStart"](1,"div",10),r["\u0275\u0275element"](2,"div"),r["\u0275\u0275element"](3,"div"),r["\u0275\u0275element"](4,"div"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]())}function I(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"mat-option",12),r["\u0275\u0275listener"]("onSelectionChange",(function(){r["\u0275\u0275restoreView"](e);const n=t.$implicit;return r["\u0275\u0275nextContext"](2).resultClicked(n)})),r["\u0275\u0275elementStart"](1,"span"),r["\u0275\u0275elementStart"](2,"small",13),r["\u0275\u0275text"](3),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=r["\u0275\u0275nextContext"](2);r["\u0275\u0275advance"](2),r["\u0275\u0275classMap"](n.ngClasses),r["\u0275\u0275propertyInterpolate3"]("matTooltip","",e[n.optionLabel[0]],"",e[n.optionLabel[1]]?" - ":"","",e[n.optionLabel[1]]?e[n.optionLabel[1]]:"",""),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate3"]("",e[n.optionLabel[0]],"",e[n.optionLabel[1]]?" - ":"","",e[n.optionLabel[1]]?e[n.optionLabel[1]]:"","")}}function y(e,t){if(1&e&&(r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275template"](1,I,4,9,"mat-option",11),r["\u0275\u0275elementContainerEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngForOf",e.searchResult)}}let b=(()=>{class e extends l.a{constructor(e,t){super(),this.http=e,this._login=t,this.label="",this.placeholder="",this.isRequired=!1,this.isReadOnly=!1,this.emitVal=new r.EventEmitter,this.optClicked=!1,this.searchTextControl=new i.j,this.searchResult=[],this.isLoading=!1,this.ifPlaceholderExists=!1,this._onDestroy=new a.b,this.resultClicked=e=>{setTimeout(()=>{this.optClicked=!0,this.outputParam?(this.onChange(e[this.outputParam]),this.emitVal.emit(e[this.outputParam])):(this.onChange(JSON.stringify(e)),this.emitVal.emit(JSON.stringify(e))),setTimeout(()=>{document.getElementById("searchTextInput").blur()},100),this.searchTextControl.patchValue(e[this.optionLabel[0]]+(this.optionLabel.length>1?" - "+e[this.optionLabel[1]]:""))},100)}}ngOnInit(){""!=this.placeholder&&""==this.label&&(this.label=this.placeholder,this.ifPlaceholderExists=!0),this.searchTxtControlSubscription=this.searchTextControl.valueChanges.pipe(Object(s.a)(this._onDestroy),Object(m.a)(700)).subscribe(e=>{e&&e.length>0&&this.callApiFn(e)})}callInitApi(){null==this.searchTextControl.value&&0==this.searchResult.length&&this.callApiFn("")}callApiFn(e){this.isLoading=!0;let t={searchParameter:e,options:this.bodyParams},n={headers:new o.f({Authorization:"Bearer "+this._login.getToken()})};this.http.post(this.apiUri,t,n).pipe(Object(s.a)(this._onDestroy)).subscribe(e=>{this.isLoading=!1,this.searchResult=e},e=>{console.error(e)})}checkAndClearInput(e){this.optClicked&&!e||0!=this.isReadOnly||(this.searchTextControl.reset(),this.onChange(null),this.emitVal.emit(null)),e&&setTimeout(()=>{document.getElementById("searchTextInput").blur(),setTimeout(()=>{document.getElementById("searchTextInput").focus()},100)},100)}writeValue(e){if(e){let t=JSON.parse(e);this.searchTextControl.patchValue(t[this.optionLabel[0]]+(this.optionLabel.length>1?" - "+t[this.optionLabel[1]]:""))}else this.searchTextControl.patchValue("")}resetSuggestion(){this.searchTextControl.patchValue("")}ngOnDestroy(){this.searchTxtControlSubscription&&this.searchTxtControlSubscription.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](o.c),r["\u0275\u0275directiveInject"](d.a))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search-huge-input"]],inputs:{apiUri:"apiUri",bodyParams:"bodyParams",outputParam:"outputParam",optionLabel:"optionLabel",optionValue:"optionValue",label:"label",placeholder:"placeholder",ngClasses:"ngClasses",isRequired:"isRequired",isReadOnly:"isReadOnly"},outputs:{emitVal:"emitVal"},features:[r["\u0275\u0275ProvidersFeature"]([{provide:i.t,useExisting:Object(r.forwardRef)(()=>e),multi:!0}]),r["\u0275\u0275InheritDefinitionFeature"]],decls:9,vars:11,consts:[["appearance","outline",3,"ngClass"],["id","searchTextInput","matInput","","autocomplete","off",3,"placeholder","matAutocomplete","formControl","required","readonly","focusout","focusin","keyup"],["mat-button","","matSuffix","","mat-icon-button","","style","height: 30px; width: 30px; line-height: 1",3,"click",4,"ngIf"],[3,"displayWith"],["auto","matAutocomplete"],["class","is-loading",4,"ngIf"],[4,"ngIf"],["mat-button","","matSuffix","","mat-icon-button","",2,"height","30px","width","30px","line-height","1",3,"click"],["matTooltip","Clear",2,"font-size","20px !important","color","#66615b !important"],[1,"is-loading"],[1,"lds-facebook"],[3,"onSelectionChange",4,"ngFor","ngForOf"],[3,"onSelectionChange"],[2,"padding-left","12px",3,"matTooltip"]],template:function(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"mat-form-field",0),r["\u0275\u0275elementStart"](1,"mat-label"),r["\u0275\u0275text"](2),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](3,"input",1),r["\u0275\u0275listener"]("focusout",(function(){return t.checkAndClearInput(!1)}))("focusin",(function(){return t.callInitApi()}))("keyup",(function(){return t.optClicked=!1})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](4,C,3,0,"button",2),r["\u0275\u0275elementStart"](5,"mat-autocomplete",3,4),r["\u0275\u0275template"](7,v,5,0,"mat-option",5),r["\u0275\u0275template"](8,y,2,1,"ng-container",6),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275reference"](6);r["\u0275\u0275property"]("ngClass",t.ifPlaceholderExists?"label-no-float":""),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate"](t.label),r["\u0275\u0275advance"](1),r["\u0275\u0275propertyInterpolate"]("placeholder",t.label),r["\u0275\u0275property"]("matAutocomplete",e)("formControl",t.searchTextControl)("required",t.isRequired)("readonly",t.isReadOnly),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",null!=t.searchTextControl.value),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("displayWith",t.displayFn),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngIf",t.isLoading),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",0==t.isLoading)}},directives:[p.c,c.NgClass,p.g,u.b,h.d,i.e,i.v,i.k,i.F,c.NgIf,h.b,f.a,p.i,E.a,S.a,g.p,c.NgForOf],styles:[".label-no-float.mat-form-field-appearance-outline.mat-form-field-should-float .mat-form-field-label{display:none}  .label-no-float.mat-form-field-appearance-outline.mat-form-field-should-float .mat-form-field-outline-gap{border-top-color:initial}.mat-form-field[_ngcontent-%COMP%]{width:100%}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding-bottom:8px!important}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-underline{bottom:0}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-infix{padding:.65em 0!important;border-top:.54375em solid transparent!important;font-size:13px}.mat-form-field[_ngcontent-%COMP%]     .mat-select-arrow{margin:0 4px!important}.lds-facebook[_ngcontent-%COMP%]{display:inline-block;position:sticky;width:80px;height:24px;margin-left:110px}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{display:inline-block;position:absolute;left:4px;width:5px;background:#cf0001;animation:lds-facebook 1.2s cubic-bezier(0,.5,.5,1) infinite}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:first-child{left:8px;animation-delay:-.24s}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:nth-child(2){left:16px;animation-delay:-.12s}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:nth-child(3){left:24px;animation-delay:0}@keyframes lds-facebook{0%{top:8px;height:32px}50%,to{top:12px;height:16px}}"]}),e})()},ucYs:function(e,t,n){"use strict";n.d(t,"a",(function(){return s}));var r=n("mrSG"),o=n("xG9w"),a=n("fXoL"),i=n("tk/3"),l=n("BVzC");let s=(()=>{class e{constructor(e,t){this.http=e,this.errorService=t,this.workflowStatusList=[],this.getWorkflowStatusList()}getWorkflowStatusList(){this.http.post("/api/wfPrimary/getWorkflowStatusList","").subscribe(e=>{this.workflowStatusList=e.data},e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server While Getting Workflow Status List",e&&e.params?e.params:e&&e.error?e.error.params:{})})}getWorkflowProperties(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/getWorkflowProperties",{applicationId:e}).subscribe(e=>t(e),e=>n(e))})}getWorkflowPropertiesByWorkflowId(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/getWorkflowPropertiesByWorkflowId",{workflowId:e}).subscribe(e=>t(e),e=>n(e))})}getApproversHierarchy(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/getApproversHierarchy",e).subscribe(e=>t(e),e=>n(e))})}createWorkflowItems(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/createWorkflowItems",e).subscribe(e=>t(e),e=>n(e))})}getWorkflowDetails(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/getWorkflowDetails",{workflowId:e}).subscribe(e=>t(e),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while getting workflow details",e&&e.params?e.params:e&&e.error?e.error.params:{})})})}formatApproversHierarchy(e,t){return Object(r.c)(this,void 0,void 0,(function*(){0==e.length&&t.cc0&&t.cc0.appr0&&t.cc0.appr0.length>0&&e.push([{approvalType:"I",purposeOfInsertingIntoArray:"Just to ensure that 'I' type data is used, if it exists"}]);for(let n=0;n<e.length;n++){let r=[],a=o.keys(t["cc"+n]);for(let o=0;o<a.length;o++)for(let i=0;i<t["cc"+n][a[o]].length;i++){let l={name:t["cc"+n][a[o]][i].DELEGATE_NAME,oid:t["cc"+n][a[o]][i].DELEGATE_OID,level:o+1,designation:t["cc"+n][a[o]][i].DELEGATE_DESIGNATION_NAME,is_delegated:t["cc"+n][a[o]][i].IS_DELEGATED,role:t["cc"+n][a[o]][i].DELEGATE_ROLE_NAME};if(1==t["cc"+n][a[o]][i].IS_DELEGATED&&(l.delegated_by={name:t["cc"+n][a[o]][i].APPROVER_NAME,oid:t["cc"+n][a[o]][i].APPROVER_OID,level:o+1,designation:t["cc"+n][a[o]][i].APPROVER_DESIGNATION_NAME}),r.push(l),n==e.length-1&&o==a.length-1&&i==t["cc"+n][a[o]].length-1)return r}}}))}storeComments(e,t,n){return new Promise((r,o)=>{this.http.post("/api/wfPrimary/updateComments",{workflowHeaderId:e,newComments:t,commentor:n}).subscribe(e=>r(e),e=>(this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server Error while Updating Workflow Comments",e&&e.params?e.params:e&&e.error?e.error.params:{}),o(e)))})}updateWorkflowItems(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/updateWorkflowItems",e).subscribe(e=>t(e),e=>(console.log(e),n(e)))})}formatApproversHierarchyForOpportunityApprovalActivity(e){return Object(r.c)(this,void 0,void 0,(function*(){for(let t=0;t<1;t++){let n=[],r=o.keys(e["cc"+t]);for(let o=0;o<r.length;o++)for(let a=0;a<e["cc"+t][r[o]].length;a++){let i={name:e["cc"+t][r[o]][a].DELEGATE_NAME,oid:e["cc"+t][r[o]][a].DELEGATE_OID,level:e["cc"+t][r[o]][a].APPROVAL_ORDER,designation:e["cc"+t][r[o]][a].DELEGATE_DESIGNATION_NAME,is_delegated:e["cc"+t][r[o]][a].IS_DELEGATED};if(1==e["cc"+t][r[o]][a].IS_DELEGATED&&(i.delegated_by={name:e["cc"+t][r[o]][a].APPROVER_NAME,oid:e["cc"+t][r[o]][a].APPROVER_OID,level:e["cc"+t][r[o]][a].APPROVAL_ORDER,designation:e["cc"+t][r[o]][a].APPROVER_DESIGNATION_NAME}),n.push(i),o==r.length-1&&a==e["cc"+t][r[o]].length-1)return n}}}))}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275inject"](i.c),a["\u0275\u0275inject"](l.a))},e.\u0275prov=a["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()}}]);