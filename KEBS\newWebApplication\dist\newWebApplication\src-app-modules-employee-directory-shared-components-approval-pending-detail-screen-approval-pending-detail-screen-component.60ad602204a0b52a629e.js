(window.webpackJsonp=window.webpackJsonp||[]).push([[954],{slBa:function(t,e,n){"use strict";n.r(e),n.d(e,"ApprovalPendingDetailScreenComponent",(function(){return A})),n.d(e,"ApprovalPendingDetailScreenModule",(function(){return V}));var o=n("33Jv"),a=n("0IaG"),i=n("ofXK"),l=n("Xi0T"),r=n("NFeN"),d=n("bTqV"),c=n("Xa2L"),s=n("Qu3c"),p=n("STbY"),m=n("kmnG"),g=n("qFsG"),v=n("3Pt+"),f=n("QibW"),u=n("d3UM"),h=n("7EHt"),x=n("fXoL"),_=n("1A3m"),C=n("jAlA");function y(t,e){1&t&&(x["\u0275\u0275elementContainerStart"](0),x["\u0275\u0275elementStart"](1,"div",2),x["\u0275\u0275element"](2,"mat-spinner",3),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementContainerEnd"]())}function b(t,e){if(1&t){const t=x["\u0275\u0275getCurrentView"]();x["\u0275\u0275elementStart"](0,"div",8),x["\u0275\u0275elementStart"](1,"div",9),x["\u0275\u0275elementStart"](2,"div",10),x["\u0275\u0275elementStart"](3,"div",11),x["\u0275\u0275elementStart"](4,"div",12),x["\u0275\u0275elementStart"](5,"span"),x["\u0275\u0275text"](6),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](7,"div",13),x["\u0275\u0275listener"]("click",(function(){return x["\u0275\u0275restoreView"](t),x["\u0275\u0275nextContext"](2).cancel()})),x["\u0275\u0275elementStart"](8,"mat-icon",14),x["\u0275\u0275text"](9," close "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()}if(2&t){const t=x["\u0275\u0275nextContext"](2);x["\u0275\u0275advance"](6),x["\u0275\u0275textInterpolate1"](" ",t.approvalPendings.length," Approval Pending")}}function M(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"span"),x["\u0275\u0275text"](1),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"](2).$implicit;x["\u0275\u0275advance"](1),x["\u0275\u0275textInterpolate2"]("","-"==t.current_value.phoneCode?"":"+ "+t.current_value.phoneCode," ",t.current_value.contactNumber?t.current_value.contactNumber:"-","")}}function O(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"span",27),x["\u0275\u0275text"](1),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"](2).$implicit;x["\u0275\u0275advance"](1),x["\u0275\u0275textInterpolate"](t.current_value?t.current_value:"-")}}function P(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"span",28),x["\u0275\u0275text"](1),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"](2).$implicit;x["\u0275\u0275advance"](1),x["\u0275\u0275textInterpolate2"]("","-"==t.prev_value.phoneCode?"":"+ "+t.prev_value.phoneCode," ",t.prev_value.contactNumber?t.prev_value.contactNumber:"-","")}}function w(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"span",28),x["\u0275\u0275text"](1),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"](2).$implicit;x["\u0275\u0275advance"](1),x["\u0275\u0275textInterpolate"](t.prev_value?t.prev_value:"-")}}function S(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"div",22),x["\u0275\u0275elementStart"](1,"div",23),x["\u0275\u0275template"](2,M,2,2,"span",1),x["\u0275\u0275template"](3,O,2,1,"span",24),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](4,"div",25),x["\u0275\u0275template"](5,P,2,2,"span",26),x["\u0275\u0275template"](6,w,2,1,"span",26),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"]().$implicit;x["\u0275\u0275advance"](2),x["\u0275\u0275property"]("ngIf","contactNumberDetails"==t.field_key),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf","contactNumberDetails"!=t.field_key),x["\u0275\u0275advance"](2),x["\u0275\u0275property"]("ngIf","contactNumberDetails"==t.field_key),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf","contactNumberDetails"!=t.field_key)}}function E(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"th"),x["\u0275\u0275text"](1),x["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit;x["\u0275\u0275advance"](1),x["\u0275\u0275textInterpolate"](t)}}function I(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"span",36),x["\u0275\u0275text"](1),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"]().$implicit,e=x["\u0275\u0275nextContext"]().$implicit;x["\u0275\u0275propertyInterpolate2"]("matTooltip","","-"!=e[t].phoneCode||e[t].contactNumber?"+ "+e[t].phoneCode:""," ",e[t].contactNumber?e[t].contactNumber:"-",""),x["\u0275\u0275advance"](1),x["\u0275\u0275textInterpolate2"]("","-"!=e[t].phoneCode||e[t].contactNumber?"+ "+e[t].phoneCode:""," ",e[t].contactNumber?e[t].contactNumber:"-","")}}function D(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"span",36),x["\u0275\u0275text"](1),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"]().$implicit,e=x["\u0275\u0275nextContext"]().$implicit;x["\u0275\u0275propertyInterpolate"]("matTooltip",e[t]?e[t]:"-"),x["\u0275\u0275advance"](1),x["\u0275\u0275textInterpolate"](e[t]?e[t]:"-")}}function N(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"td"),x["\u0275\u0275template"](1,I,2,4,"span",35),x["\u0275\u0275template"](2,D,2,2,"span",35),x["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit;x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf","contact Number"==t),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf","contact Number"!=t)}}function k(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"tr"),x["\u0275\u0275template"](1,N,3,2,"td",34),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"](4);x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngForOf",t.prev_value_key)}}function z(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"th"),x["\u0275\u0275text"](1),x["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit;x["\u0275\u0275advance"](1),x["\u0275\u0275textInterpolate"](t)}}function $(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"span",36),x["\u0275\u0275text"](1),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"]().$implicit,e=x["\u0275\u0275nextContext"]().$implicit;x["\u0275\u0275propertyInterpolate2"]("matTooltip","","-"!=e[t].phoneCode||e[t].contactNumber?"+ "+e[t].phoneCode:""," ",e[t].contactNumber?e[t].contactNumber:"-",""),x["\u0275\u0275advance"](1),x["\u0275\u0275textInterpolate2"](" ","-"!=e[t].phoneCode||e[t].contactNumber?"+ "+e[t].phoneCode:""," ",e[t].contactNumber?e[t].contactNumber:"-","")}}function j(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"span",36),x["\u0275\u0275text"](1),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"]().$implicit,e=x["\u0275\u0275nextContext"]().$implicit;x["\u0275\u0275propertyInterpolate"]("matTooltip",e[t]?e[t]:"-"),x["\u0275\u0275advance"](1),x["\u0275\u0275textInterpolate"](e[t]?e[t]:"-")}}function F(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"td"),x["\u0275\u0275template"](1,$,2,4,"span",35),x["\u0275\u0275template"](2,j,2,2,"span",35),x["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit;x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf","contact Number"==t),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf","contact Number"!=t)}}function L(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"tr"),x["\u0275\u0275template"](1,F,3,2,"td",34),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"](4);x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngForOf",t.curr_value_key)}}function T(t,e){if(1&t&&(x["\u0275\u0275elementStart"](0,"div",29),x["\u0275\u0275elementStart"](1,"div",30),x["\u0275\u0275elementStart"](2,"div",31),x["\u0275\u0275elementStart"](3,"span",32),x["\u0275\u0275text"](4,"Previous"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](5,"table",33),x["\u0275\u0275elementStart"](6,"thead"),x["\u0275\u0275elementStart"](7,"tr"),x["\u0275\u0275template"](8,E,2,1,"th",34),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](9,"tbody"),x["\u0275\u0275template"](10,k,2,1,"tr",34),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](11,"div",30),x["\u0275\u0275elementStart"](12,"div",31),x["\u0275\u0275elementStart"](13,"span",32),x["\u0275\u0275text"](14,"Current"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](15,"table",33),x["\u0275\u0275elementStart"](16,"thead"),x["\u0275\u0275elementStart"](17,"tr"),x["\u0275\u0275template"](18,z,2,1,"th",34),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](19,"tbody"),x["\u0275\u0275template"](20,L,2,1,"tr",34),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()),2&t){const t=x["\u0275\u0275nextContext"](3);x["\u0275\u0275advance"](8),x["\u0275\u0275property"]("ngForOf",t.prev_value_key),x["\u0275\u0275advance"](2),x["\u0275\u0275property"]("ngForOf",t.pendingMetaData.submission_item.approval_fields[0].prev_value),x["\u0275\u0275advance"](8),x["\u0275\u0275property"]("ngForOf",t.curr_value_key),x["\u0275\u0275advance"](2),x["\u0275\u0275property"]("ngForOf",t.pendingMetaData.submission_item.approval_fields[0].current_value)}}function Y(t,e){if(1&t){const t=x["\u0275\u0275getCurrentView"]();x["\u0275\u0275elementStart"](0,"div",15),x["\u0275\u0275elementStart"](1,"mat-accordion"),x["\u0275\u0275elementStart"](2,"mat-expansion-panel",16),x["\u0275\u0275listener"]("opened",(function(){return x["\u0275\u0275restoreView"](t),x["\u0275\u0275nextContext"](2).panelOpenState=!0}))("closed",(function(){return x["\u0275\u0275restoreView"](t),x["\u0275\u0275nextContext"](2).panelOpenState=!1})),x["\u0275\u0275elementStart"](3,"mat-expansion-panel-header"),x["\u0275\u0275elementStart"](4,"div",17),x["\u0275\u0275elementStart"](5,"div",18),x["\u0275\u0275text"](6),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](7,"div",19),x["\u0275\u0275text"](8),x["\u0275\u0275pipe"](9,"date"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275template"](10,S,7,4,"div",20),x["\u0275\u0275template"](11,T,21,4,"div",21),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()}if(2&t){const t=e.$implicit,n=x["\u0275\u0275nextContext"](2);x["\u0275\u0275advance"](6),x["\u0275\u0275textInterpolate1"](" Updated ",n.pendingMetaData.submission_item.is_multi_val?n.pendingMetaData.tab_name:t.field_name," "),x["\u0275\u0275advance"](2),x["\u0275\u0275textInterpolate1"](" ",x["\u0275\u0275pipeBind2"](9,4,n.pendingMetaData.submitted_on,"d MMM YYYY")," "),x["\u0275\u0275advance"](2),x["\u0275\u0275property"]("ngIf",!n.pendingMetaData.submission_item.is_multi_val),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",n.pendingMetaData.submission_item.is_multi_val)}}function R(t,e){if(1&t&&(x["\u0275\u0275elementContainerStart"](0),x["\u0275\u0275elementStart"](1,"div",4),x["\u0275\u0275elementStart"](2,"div",5),x["\u0275\u0275template"](3,b,10,1,"div",6),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](4,"div",4),x["\u0275\u0275template"](5,Y,12,7,"div",7),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementContainerEnd"]()),2&t){const t=x["\u0275\u0275nextContext"]();x["\u0275\u0275advance"](3),x["\u0275\u0275property"]("ngIf",t.showLength),x["\u0275\u0275advance"](2),x["\u0275\u0275property"]("ngForOf",t.approvalPendings)}}let A=(()=>{class t{constructor(t,e,n,i){this.injector=t,this.fb=e,this._toaster=n,this._edService=i,this.dialogRef=null,this.panelOpenState=!1,this.subs=new o.a,this.loaderObject={isComponentLoading:!1,isFormSubmitLoading:!1},this.showLength=!0,this.prev_value_key=[],this.curr_value_key=[],this.dialogRef=this.injector.get(a.h,null),this.dialogData=this.injector.get(a.a,null)}ngOnInit(){var t,e,n,o,a,i,l,r,d,c,s,p,m,g,v,f,u,h,x,_,C;this.associateId=(null===(e=null===(t=this.dialogData)||void 0===t?void 0:t.modalParams)||void 0===e?void 0:e.associateId)?null===(o=null===(n=this.dialogData)||void 0===n?void 0:n.modalParams)||void 0===o?void 0:o.associateId:"",this.approvalPendings=(null===(i=null===(a=this.dialogData)||void 0===a?void 0:a.modalParams)||void 0===i?void 0:i.approvalPendings)?null===(r=null===(l=this.dialogData)||void 0===l?void 0:l.modalParams)||void 0===r?void 0:r.approvalPendings:[],this.pendingMetaData=(null===(c=null===(d=this.dialogData)||void 0===d?void 0:d.modalParams)||void 0===c?void 0:c.pendingMetaData)?null===(p=null===(s=this.dialogData)||void 0===s?void 0:s.modalParams)||void 0===p?void 0:p.pendingMetaData:{},this.showLength=null===(v=null===(g=null===(m=this.dialogData)||void 0===m?void 0:m.modalParams)||void 0===g?void 0:g.showlength)||void 0===v||v,console.log(this.showLength),this.pendingMetaData.submission_item.is_multi_val&&(this.prev_value_key=Object.keys(null!==(h=null===(u=null===(f=this.pendingMetaData.submission_item.approval_fields[0])||void 0===f?void 0:f.prev_value)||void 0===u?void 0:u[0])&&void 0!==h?h:{}),this.curr_value_key=Object.keys(null!==(C=null===(_=null===(x=this.pendingMetaData.submission_item.approval_fields[0])||void 0===x?void 0:x.current_value)||void 0===_?void 0:_[0])&&void 0!==C?C:{}))}cancel(){this.closeDialog("close")}closeDialog(t){this.dialogRef.close(t)}ngOnDestroy(){this.subs.unsubscribe()}}return t.\u0275fac=function(e){return new(e||t)(x["\u0275\u0275directiveInject"](x.Injector),x["\u0275\u0275directiveInject"](v.i),x["\u0275\u0275directiveInject"](_.a),x["\u0275\u0275directiveInject"](C.a))},t.\u0275cmp=x["\u0275\u0275defineComponent"]({type:t,selectors:[["app-approval-pending-detail-screen"]],decls:3,vars:2,consts:[[1,"container-fluid","p-0","pr-1","mt-0","validation-dialog-styles"],[4,"ngIf"],[1,"d-flex","justify-content-center","mt-3"],["matTooltip","Please wait...","diameter","30"],[1,"row","pt-3","slide-from-down"],[1,"col-12","px-2","py-2"],["class","row",4,"ngIf"],["class","col-12 px-3 py-2",4,"ngFor","ngForOf"],[1,"row"],[1,"col-10","p-0","pl-3"],[1,"row","pt-2"],[1,"col-12","p-0"],[1,"row","heading"],[1,"col-2","d-flex","flex-row-reverse",2,"cursor","pointer",3,"click"],[1,"close-icon"],[1,"col-12","px-3","py-2"],[3,"opened","closed"],[1,"col-12","p-0","pt-1"],[1,"row","section-heading"],[1,"row","date-item"],["class","col-12",4,"ngIf"],["class","row pt-3 pb-2","style","overflow-x:auto;overflow-y:auto",4,"ngIf"],[1,"col-12"],[1,"row",2,"font-weight","500"],["class","current-value",4,"ngIf"],[1,"row",2,"text-decoration","line-through","color","grey"],["class","prev-value",4,"ngIf"],[1,"current-value"],[1,"prev-value"],[1,"row","pt-3","pb-2",2,"overflow-x","auto","overflow-y","auto"],[1,"col-12","pb-2"],[1,"row","pb-2"],[1,"status-header"],[1,"table","table-hover"],[4,"ngFor","ngForOf"],["class","value-header",3,"matTooltip",4,"ngIf"],[1,"value-header",3,"matTooltip"]],template:function(t,e){1&t&&(x["\u0275\u0275elementStart"](0,"div",0),x["\u0275\u0275template"](1,y,3,0,"ng-container",1),x["\u0275\u0275template"](2,R,6,2,"ng-container",1),x["\u0275\u0275elementEnd"]()),2&t&&(x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",e.loaderObject.isComponentLoading),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",!e.loaderObject.isComponentLoading))},directives:[i.NgIf,c.c,s.a,i.NgForOf,r.a,h.a,h.c,h.g],pipes:[i.DatePipe],styles:[".validation-dialog-styles[_ngcontent-%COMP%]{overflow-x:auto;height:70vh;scrollbar-width:none}.validation-dialog-styles[_ngcontent-%COMP%]::-webkit-scrollbar{display:none}.validation-dialog-styles[_ngcontent-%COMP%]   .title-header[_ngcontent-%COMP%]{font-weight:700;font-size:14px;line-height:16px;color:#45546e}.validation-dialog-styles[_ngcontent-%COMP%]   .status-header[_ngcontent-%COMP%]{font-size:14px;color:#8b95a5}.validation-dialog-styles[_ngcontent-%COMP%]   .value-header[_ngcontent-%COMP%]{font-size:14px;color:#57acff;font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:96%}.validation-dialog-styles[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{font-weight:400;font-size:14px;line-height:24px;color:#8b95a5}.validation-dialog-styles[_ngcontent-%COMP%]   .radio-content[_ngcontent-%COMP%]{font-weight:400;font-size:14px;line-height:24px;color:#5f6c81}.validation-dialog-styles[_ngcontent-%COMP%]   .mat-radio-outer-circle[_ngcontent-%COMP%], .validation-dialog-styles[_ngcontent-%COMP%]     .mat-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle{border-color:#f27a6c}.validation-dialog-styles[_ngcontent-%COMP%]     .mat-radio-button.mat-accent .mat-radio-inner-circle{background-color:#f27a6c}.validation-dialog-styles[_ngcontent-%COMP%]   .img-section[_ngcontent-%COMP%]{display:flex}.validation-dialog-styles[_ngcontent-%COMP%]   .circular[_ngcontent-%COMP%]{width:65px;height:65px;border-radius:50%;border:1px solid #000}.validation-dialog-styles[_ngcontent-%COMP%]   .emp-heading[_ngcontent-%COMP%]{display:flex;align-items:center}.validation-dialog-styles[_ngcontent-%COMP%]   .circular[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:65px;height:65px;border-radius:50%;transition:transform .5s ease;transform:scale(.8)}.validation-dialog-styles[_ngcontent-%COMP%]   .emp-name-heading[_ngcontent-%COMP%]{font-size:16px;font-weight:700;color:#45546e;line-height:24px}.validation-dialog-styles[_ngcontent-%COMP%]   .card1-title-heading[_ngcontent-%COMP%], .validation-dialog-styles[_ngcontent-%COMP%]   .emp-name-heading[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;text-transform:capitalize;max-width:96%}.validation-dialog-styles[_ngcontent-%COMP%]   .card1-title-heading[_ngcontent-%COMP%]{font-size:14px;font-weight:400;color:#5f6c81}.validation-dialog-styles[_ngcontent-%COMP%]   .create-employee-btn[_ngcontent-%COMP%]{background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%)!important;color:#fff;height:42px}.validation-dialog-styles[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{color:#45546e;height:42px}.validation-dialog-styles[_ngcontent-%COMP%]   .mat-card[_ngcontent-%COMP%]{width:100%;border:1px solid #fff;background-color:#fff;border-radius:5px}.validation-dialog-styles[_ngcontent-%COMP%]   .item-value[_ngcontent-%COMP%], .validation-dialog-styles[_ngcontent-%COMP%]   .title-heading[_ngcontent-%COMP%]{font-size:14px;font-weight:400;color:#8b95a5;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;text-transform:capitalize;max-width:96%}.validation-dialog-styles[_ngcontent-%COMP%]   .item-value[_ngcontent-%COMP%]{line-height:16px}.validation-dialog-styles[_ngcontent-%COMP%]   .required-star[_ngcontent-%COMP%]{color:#cf0001}.validation-dialog-styles[_ngcontent-%COMP%]   .field-title[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#6e7b8f}.validation-dialog-styles[_ngcontent-%COMP%]     .mat-form-field-outline{color:#b9c0ca!important}.validation-dialog-styles[_ngcontent-%COMP%]   .status[_ngcontent-%COMP%]{color:#fff;line-height:24px;padding:2}.validation-dialog-styles[_ngcontent-%COMP%]   .status[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%]{font-size:12px;font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.validation-dialog-styles[_ngcontent-%COMP%]   .section-heading[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;font-weight:600;font-size:14px;color:#f27a6c;text-transform:capitalize}.validation-dialog-styles[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;font-weight:500;font-size:16px;display:flex;align-items:center;text-transform:capitalize;color:#45546e}.validation-dialog-styles[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%]{color:#45546e;font-size:18px;line-height:32px}.validation-dialog-styles[_ngcontent-%COMP%]   .date-item[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;font-weight:400;font-size:12px;color:#45546e}.validation-dialog-styles[_ngcontent-%COMP%]   .current-value[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;font-weight:400;font-size:14px;color:#111434}.validation-dialog-styles[_ngcontent-%COMP%]   .prev-value[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;font-weight:400;font-size:14px;-webkit-text-decoration-line:line-through;text-decoration-line:line-through;text-transform:capitalize;color:#b9c0ca}.validation-dialog-styles[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}.validation-dialog-styles[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}"]}),t})(),V=(()=>{class t{}return t.\u0275mod=x["\u0275\u0275defineNgModule"]({type:t}),t.\u0275inj=x["\u0275\u0275defineInjector"]({factory:function(e){return new(e||t)},imports:[[i.CommonModule,l.a,r.b,d.b,c.b,s.b,p.e,m.e,g.c,v.E,v.p,f.c,u.d,h.b]]}),t})()}}]);