import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, Subject } from 'rxjs';

export interface MilestoneCardData {
  id: number;
  milestone_name: string;
  quote_id: number;
  total_amount: number;
  status: string;
  created_date: Date;
  expense_ids: number[];
}

@Injectable({
  providedIn: 'root'
})
export class PmExpenseIntegrationService {
  private milestoneCreatedSubject = new Subject<MilestoneCardData>();
  private expenseStatusUpdatedSubject = new Subject<{expenseIds: number[], newStatus: number}>();

  constructor(private http: HttpClient) { }

  /**
   * Observable for milestone creation events
   */
  get milestoneCreated$(): Observable<MilestoneCardData> {
    return this.milestoneCreatedSubject.asObservable();
  }

  /**
   * Observable for expense status update events
   */
  get expenseStatusUpdated$(): Observable<{expenseIds: number[], newStatus: number}> {
    return this.expenseStatusUpdatedSubject.asObservable();
  }

  /**
   * Notify that a milestone has been created
   */
  notifyMilestoneCreated(milestoneData: MilestoneCardData): void {
    this.milestoneCreatedSubject.next(milestoneData);
  }

  /**
   * Notify that expense status has been updated
   */
  notifyExpenseStatusUpdated(expenseIds: number[], newStatus: number): void {
    this.expenseStatusUpdatedSubject.next({ expenseIds, newStatus });
  }

  /**
   * Create milestone card in reimbursement tab
   */
  createReimbursementMilestone(milestoneData: MilestoneCardData): Observable<any> {
    return this.http.post('/api/pm/finance/reimbursement/createMilestone', {
      milestone_id: milestoneData.id,
      milestone_name: milestoneData.milestone_name,
      total_amount: milestoneData.total_amount,
      status: 'YTB', // Default YTB status
      created_date: milestoneData.created_date,
      expense_ids: milestoneData.expense_ids
    });
  }

  /**
   * Create milestone card in bills tab
   */
  createBillsMilestone(milestoneData: MilestoneCardData): Observable<any> {
    return this.http.post('/api/pm/finance/bills/createMilestone', {
      milestone_id: milestoneData.id,
      milestone_name: milestoneData.milestone_name,
      total_amount: milestoneData.total_amount,
      status: 'YTB', // Default YTB status
      created_date: milestoneData.created_date,
      expense_ids: milestoneData.expense_ids
    });
  }

  /**
   * Create milestone card in invoice YTB tab
   */
  createInvoiceYTBMilestone(milestoneData: MilestoneCardData): Observable<any> {
    return this.http.post('/api/pm/finance/invoice/ytb/createMilestone', {
      milestone_id: milestoneData.id,
      milestone_name: milestoneData.milestone_name,
      total_amount: milestoneData.total_amount,
      status: 'YTB', // Default YTB status
      created_date: milestoneData.created_date,
      expense_ids: milestoneData.expense_ids
    });
  }

  /**
   * Update expense status in t_expense_header table
   * Changes expense_status from 1 to 2
   */
  updateExpenseHeaderStatus(expenseIds: number[]): Observable<any> {
    return this.http.post('/api/pm/expense/updateExpenseHeaderStatus', {
      expense_ids: expenseIds,
      old_status: 1,
      new_status: 2
    });
  }

  /**
   * Get milestone data by ID
   */
  getMilestoneById(milestoneId: number): Observable<MilestoneCardData> {
    return this.http.get<MilestoneCardData>(`/api/pm/expense/milestone/${milestoneId}`);
  }

  /**
   * Refresh related tabs after milestone creation
   */
  refreshRelatedTabs(): void {
    // This method can be used to trigger refresh events for other tabs
    // Implementation depends on how the tabs are structured in the parent component
    console.log('Refreshing related tabs after milestone creation');
  }
}
