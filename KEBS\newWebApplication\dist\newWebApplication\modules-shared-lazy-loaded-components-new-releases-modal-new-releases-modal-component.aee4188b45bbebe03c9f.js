(window.webpackJsonp=window.webpackJsonp||[]).push([[821,765,822,983,987,990,991],{hJL4:function(t,e,r){"use strict";r.d(e,"a",(function(){return u}));var s=r("mrSG"),o=r("XNiG"),i=r("xG9w"),a=r("fXoL"),n=r("tk/3"),p=r("LcQX"),l=r("XXEo"),c=r("flaP");let u=(()=>{class t{constructor(t,e,r,s){this.http=t,this.UtilityService=e,this.loginService=r,this.roleService=s,this.msg=new o.b,this.taskStatusColor=[],this.approveRequest=(t,e)=>this.http.post("/api/isa/request/approveResourceRequest",{requestDetails:t,approverOid:e}),this.rejectRequest=(t,e)=>this.http.post("/api/isa/request/rejectResourceRequest",{requestDetails:t,approverOid:e}),this.getTaskStatusColor()}getAllRequestsOfUser(t,e,r,s,o,i,a){let n=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getAllRequestsOfUser",{oid:t,statusId:e,statusCode:r,objectIds:s,skip:o,limit:i,filterConfig:a,orgIds:n})}getAllRoleAccess(){return i.where(this.roleService.roles,{application_id:139})}getRequestsBasedOnStatus(t,e,r,s,o,i,a){let n=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getRequestsBasedOnStatus",{oid:t,statusId:e,statusCode:r,objectIds:s,skip:o,limit:i,filterConfig:a,orgIds:n})}getRequestsForAwaitingApproval(t,e,r,s){return this.http.post("/api/isa/request/getRequestsForAwaitingApproval",{oid:t,skip:e,limit:r,filterConfig:s})}getObjectIdsBasedOnOid(t){return this.http.post("/api/isa/request/getObjectIdsBasedOnOid",{oid:t})}getStatusCountBasedOnOid(t,e,r,s){let o=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getStatusCountBasedOnOid",{oid:t,objectIds:e,dataTypeArray:r,filterConfig:s,orgIds:o})}getHeaderCount(t){return this.http.post("/api/isa/request/getHeaderCount",{oid:t})}getRequestBasedOnStatus(t,e){return this.http.post("/api/isa/request/getRequestBasedOnStatus",{oid:t,status:e})}getDataTypeArray(){return this.http.post("/api/isa/request/getISAStatusMasterDataUDRF",{})}sendMsg(t){this.msg.next(t)}getMsg(){return this.msg.asObservable()}getTaskStatusColor(){return new Promise((t,e)=>{this.http.post("/api/isa/request/getTaskStatusNameColor",{}).subscribe(e=>(this.taskStatusColor=e,t(e)),t=>{})})}getAllRequestsForRmg(t){return this.http.post("/api/isa/request/getAllRequestsForRmg",{filterConfig:t})}getTotalForRmg(t){return new Promise((e,r)=>{this.http.post("/api/isa/request/getTotalForRmg",{filterConfig:t}).subscribe(t=>e(t),t=>{})})}getDefaultISAStatusToDisplay(){return this.http.post("/api/isa/request/getISADefaultStatusToDisplay",{})}getCTCbreakUpForSkillSet(t){return new Promise((e,r)=>{this.http.post("/api/isa/request/getCTCbreakUpForSkillSet",t).subscribe(t=>e(t.data),t=>{r(t)})})}getCurrentUserRole(){return this.roleService&&this.roleService.roles&&this.roleService.roles.length>0?this.roleService.roles[0].role_id:null}getTemplateForUser(t){return this.http.post("/api/isa/request/getVisibleFormFieldsForUser",t)}saveAndApproveRequest(t){return this.http.post("/api/isa/request/updBudgetInfoByRequestId",t)}getVisibilityMatrix(t){return this.http.post("/api/isa/request/getAuthorzedFieldsByRoleId",t)}getISAAttachmentFromS3(t){return this.http.post("/api/isa/attachment/getISAAttachmentFromS3",{key:t})}getSkillType(){return this.http.post("/api/isa/request/getSkillType",{})}getSkillLevel(){return this.http.post("/api/isa/request/getSkillLevel",{})}getSkillExperience(){return this.http.post("/api/isa/request/getSkillExperience",{})}getSkillName(t){return this.http.post("/api/isa/request/getSkillName",{skill_type_id:t})}getIsaActivityReportDownload(){return this.http.post("/api/isa/request/getIsaActivityReportDownload",{})}getISASLAreport(t){return this.http.post("/api/isa/request/getISASLAreport",{filterConfig:t})}getISACustomReport(t){return this.http.post("/api/isa/request/getISACustomReport",{filterConfig:t})}getPositionType(){return this.http.post("/api/employee360/masterData/getPositionType",{})}calculateNetCostBasedOnPosition(t,e,r,o,a,n,p){return Object(s.c)(this,void 0,void 0,(function*(){let s;s=n&&n.length>1&&(yield this.getManpowerCostByOId(n,r,a,2))||(yield this.getManpowerCostBasedOnPosition(t,e,r,a,p));let l=yield this.getNonManpowerCost(e,r,o,a,2),c=yield this.getAllocatedCost(),u=0;u=(s?s.cost:0)+l.length>0?i.reduce(i.pluck(l,"cost"),(t,e)=>t+e,0):0;let h=c.length>0?i.reduce(i.pluck(c,"percentage"),(t,e)=>t+e,0):0;return{cost:u,currency:s&&s.currency_code?s.currency_code:"",manpowerCost:s,nonManpowerCost:l,allocatedCost:c,allocatedCostValue:u*(h/100)}}))}getManpowerCostBasedOnPosition(t,e,r,s,o){return new Promise((i,a)=>{this.http.post("/api/isa/configuration/getManpowerCostBasedOnPosition",{skillRoleId:t,nationalityId:e,locationGroupId:r,unit:s,position:o}).subscribe(t=>i(t),t=>(console.log(t),a(t)))})}getNonManpowerCost(t,e,r,s,o){return new Promise((i,a)=>{this.http.post("/api/project/getNonManpowerCost",{nationalityId:t,locationGroupId:e,locationId:r,unit:s,currency_id:o}).subscribe(t=>i(t),t=>(console.log(t),a(t)))})}getAllocatedCost(){return new Promise((t,e)=>{this.http.post("/api/project/getAllocatedCost","").subscribe(e=>t(e),t=>(console.log(t),e(t)))})}getManpowerCostByOId(t,e,r,s){return new Promise((o,i)=>{this.http.post("/api/project/getEmployeeSalary",{oid:t,location_group_id:e,unit_id:r,currency_id:s}).subscribe(t=>o(t),t=>(console.log(t),i(t)))})}getSkillNameMaster(){return this.http.post("/api/isa/configuration/getSkillName",{})}getVendorOid(t,e){return this.http.post("/api/isa/request/getVendorOid",{vendorNames:t,docID:e})}getVendorList(t,e){return this.http.post("/api/isa/request/getVendorList",{documentID:t,limitval:e})}removeVendor(t,e){return this.http.post("/api/isa/request/removeVendors",{vendorOid:t,documentID:e})}checkVendorOrNot(){return this.http.post("/api/isa/request/checkVendorOrNot",{})}}return t.\u0275fac=function(e){return new(e||t)(a["\u0275\u0275inject"](n.c),a["\u0275\u0275inject"](p.a),a["\u0275\u0275inject"](l.a),a["\u0275\u0275inject"](c.a))},t.\u0275prov=a["\u0275\u0275defineInjectable"]({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()},ucYs:function(t,e,r){"use strict";r.d(e,"a",(function(){return p}));var s=r("mrSG"),o=r("xG9w"),i=r("fXoL"),a=r("tk/3"),n=r("BVzC");let p=(()=>{class t{constructor(t,e){this.http=t,this.errorService=e,this.workflowStatusList=[],this.getWorkflowStatusList()}getWorkflowStatusList(){this.http.post("/api/wfPrimary/getWorkflowStatusList","").subscribe(t=>{this.workflowStatusList=t.data},t=>{this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Internal Server While Getting Workflow Status List",t&&t.params?t.params:t&&t.error?t.error.params:{})})}getWorkflowProperties(t){return new Promise((e,r)=>{this.http.post("/api/wfPrimary/getWorkflowProperties",{applicationId:t}).subscribe(t=>e(t),t=>r(t))})}getWorkflowPropertiesByWorkflowId(t){return new Promise((e,r)=>{this.http.post("/api/wfPrimary/getWorkflowPropertiesByWorkflowId",{workflowId:t}).subscribe(t=>e(t),t=>r(t))})}getApproversHierarchy(t){return new Promise((e,r)=>{this.http.post("/api/wfPrimary/getApproversHierarchy",t).subscribe(t=>e(t),t=>r(t))})}createWorkflowItems(t){return new Promise((e,r)=>{this.http.post("/api/wfPrimary/createWorkflowItems",t).subscribe(t=>e(t),t=>r(t))})}getWorkflowDetails(t){return new Promise((e,r)=>{this.http.post("/api/wfPrimary/getWorkflowDetails",{workflowId:t}).subscribe(t=>e(t),t=>{this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Error while getting workflow details",t&&t.params?t.params:t&&t.error?t.error.params:{})})})}formatApproversHierarchy(t,e){return Object(s.c)(this,void 0,void 0,(function*(){0==t.length&&e.cc0&&e.cc0.appr0&&e.cc0.appr0.length>0&&t.push([{approvalType:"I",purposeOfInsertingIntoArray:"Just to ensure that 'I' type data is used, if it exists"}]);for(let r=0;r<t.length;r++){let s=[],i=o.keys(e["cc"+r]);for(let o=0;o<i.length;o++)for(let a=0;a<e["cc"+r][i[o]].length;a++){let n={name:e["cc"+r][i[o]][a].DELEGATE_NAME,oid:e["cc"+r][i[o]][a].DELEGATE_OID,level:o+1,designation:e["cc"+r][i[o]][a].DELEGATE_DESIGNATION_NAME,is_delegated:e["cc"+r][i[o]][a].IS_DELEGATED,role:e["cc"+r][i[o]][a].DELEGATE_ROLE_NAME};if(1==e["cc"+r][i[o]][a].IS_DELEGATED&&(n.delegated_by={name:e["cc"+r][i[o]][a].APPROVER_NAME,oid:e["cc"+r][i[o]][a].APPROVER_OID,level:o+1,designation:e["cc"+r][i[o]][a].APPROVER_DESIGNATION_NAME}),s.push(n),r==t.length-1&&o==i.length-1&&a==e["cc"+r][i[o]].length-1)return s}}}))}storeComments(t,e,r){return new Promise((s,o)=>{this.http.post("/api/wfPrimary/updateComments",{workflowHeaderId:t,newComments:e,commentor:r}).subscribe(t=>s(t),t=>(this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Internal Server Error while Updating Workflow Comments",t&&t.params?t.params:t&&t.error?t.error.params:{}),o(t)))})}updateWorkflowItems(t){return new Promise((e,r)=>{this.http.post("/api/wfPrimary/updateWorkflowItems",t).subscribe(t=>e(t),t=>(console.log(t),r(t)))})}formatApproversHierarchyForOpportunityApprovalActivity(t){return Object(s.c)(this,void 0,void 0,(function*(){for(let e=0;e<1;e++){let r=[],s=o.keys(t["cc"+e]);for(let o=0;o<s.length;o++)for(let i=0;i<t["cc"+e][s[o]].length;i++){let a={name:t["cc"+e][s[o]][i].DELEGATE_NAME,oid:t["cc"+e][s[o]][i].DELEGATE_OID,level:t["cc"+e][s[o]][i].APPROVAL_ORDER,designation:t["cc"+e][s[o]][i].DELEGATE_DESIGNATION_NAME,is_delegated:t["cc"+e][s[o]][i].IS_DELEGATED};if(1==t["cc"+e][s[o]][i].IS_DELEGATED&&(a.delegated_by={name:t["cc"+e][s[o]][i].APPROVER_NAME,oid:t["cc"+e][s[o]][i].APPROVER_OID,level:t["cc"+e][s[o]][i].APPROVAL_ORDER,designation:t["cc"+e][s[o]][i].APPROVER_DESIGNATION_NAME}),r.push(a),o==s.length-1&&i==t["cc"+e][s[o]].length-1)return r}}}))}}return t.\u0275fac=function(e){return new(e||t)(i["\u0275\u0275inject"](a.c),i["\u0275\u0275inject"](n.a))},t.\u0275prov=i["\u0275\u0275defineInjectable"]({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()}}]);