(window.webpackJsonp=window.webpackJsonp||[]).push([[786,535,631,634,858],{"/5jE":function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var i=n("mrSG"),a=n("XNiG"),o=n("2Vo4"),l=n("fXoL");let r=(()=>{class e{constructor(){this.reloadSubject=new a.b,this.searchSubject=new a.b,this.leadsReportGovernanceTypeSubject=new o.a([])}sendNotification(e){console.log(e),this.reloadSubject.next(e)}getNotification(){return this.reloadSubject.asObservable()}sendSearchContent(e){console.log(e),this.searchSubject.next(e)}getSearchContent(){return this.searchSubject.asObservable()}sendLeadsGovTypes(e){console.log(e),this.leadsReportGovernanceTypeSubject.next(e)}getLeadsGovTypes(){return this.leadsReportGovernanceTypeSubject.asObservable()}getLeadsGovTypesValue(){return Object(i.c)(this,void 0,void 0,(function*(){return this.leadsReportGovernanceTypeSubject.getValue()}))}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275prov=l["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},"4sa2":function(e,t,n){"use strict";n.r(t),n.d(t,"LeadsDetailModule",(function(){return aa}));var i=n("ofXK"),a=n("tyNb"),o=n("fXoL"),l=n("wZkO");function r(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"a",3,4),o["\u0275\u0275text"](2),o["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=o["\u0275\u0275reference"](1);o["\u0275\u0275property"]("routerLink",e.path)("active",n.isActive),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate1"](" ",e.label," ")}}let s=(()=>{class e{constructor(e,t){this._route=e,this._router=t}ngOnInit(){this._route.params.subscribe(e=>{this.leadId=e.LeadId}),this.tabLinks=[{label:"Overview",path:"overview"},{label:"Activities",path:this.leadId+"/activities"},{label:"Attachments",path:"attachments"}]}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](a.a),o["\u0275\u0275directiveInject"](a.g))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-leads-tab"]],decls:4,vars:1,consts:[["mat-tab-nav-bar",""],["mat-tab-link","","routerLinkActive","",3,"routerLink","active",4,"ngFor","ngForOf"],[1,"project-tab-content"],["mat-tab-link","","routerLinkActive","",3,"routerLink","active"],["rla","routerLinkActive"]],template:function(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"nav",0),o["\u0275\u0275template"](1,r,3,3,"a",1),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](2,"div",2),o["\u0275\u0275element"](3,"router-outlet"),o["\u0275\u0275elementEnd"]()),2&e&&(o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngForOf",t.tabLinks))},directives:[l.f,i.NgForOf,a.l,a.j,l.e,a.i],styles:["a[_ngcontent-%COMP%]:hover{color:unset!important;text-decoration:none!important}nav[_ngcontent-%COMP%]{position:fixed;width:100%;z-index:14;background:#f8f9fa}.project-tab-content[_ngcontent-%COMP%]{padding-top:48px!important}"]}),e})();var d=n("mrSG"),c=n("xG9w"),p=n("yi1p"),m=n("tT+h"),u=n("+yIk"),h=n("dNgK"),v=n("nAV5"),g=n("JLuW"),f=n("LcQX"),y=n("0IaG"),x=n("vFIH"),S=n("XXEo"),b=n("F97M"),_=n("bTqV"),C=n("Qu3c"),w=n("STbY"),E=n("NFeN"),I=n("L515"),k=n("fAip"),O=n("flaP");function D(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",8),o["\u0275\u0275elementStart"](1,"div",9),o["\u0275\u0275elementStart"](2,"div"),o["\u0275\u0275text"](3),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](4,"div"),o["\u0275\u0275elementStart"](5,"p",10),o["\u0275\u0275text"](6),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](7,"p",11),o["\u0275\u0275text"](8),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275property"]("matTooltip",(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value)),o["\u0275\u0275advance"](3),o["\u0275\u0275textInterpolate"](null==e.currency[e.index]?null:e.currency[e.index].currency_code),o["\u0275\u0275advance"](3),o["\u0275\u0275textInterpolate"](e.label),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate1"](" ",e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code)," ")}}function M(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",12),o["\u0275\u0275elementStart"](1,"span"),o["\u0275\u0275text"](2),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275property"]("matTooltip",(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value)),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate1"](" ",e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code)," ")}}function P(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",13),o["\u0275\u0275elementStart"](1,"span",14),o["\u0275\u0275text"](2),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275property"]("matTooltip",(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value)),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code)," ")}}function T(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",15),o["\u0275\u0275elementStart"](1,"span",16),o["\u0275\u0275text"](2),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275property"]("matTooltip",(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value)),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code)," ")}}function A(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div",1),o["\u0275\u0275listener"]("click",(function(){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"]().change()})),o["\u0275\u0275template"](1,D,9,4,"div",2),o["\u0275\u0275template"](2,M,3,2,"div",3),o["\u0275\u0275template"](3,P,3,3,"div",4),o["\u0275\u0275template"](4,T,3,3,"div",5),o["\u0275\u0275elementStart"](5,"div",6),o["\u0275\u0275elementStart"](6,"mat-icon",7),o["\u0275\u0275text"](7,"loop"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf","big"==e.type),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf","small"==e.type),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf","medium"==e.type),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf","overview"==e.type)}}let F=(()=>{class e{constructor(e){this.roleService=e,this.showActualAmount=!1,this.currency=[]}set currencyList(e){if(this.currency=e,e){const t=c.findIndex(e,e=>e.currency_code==this.currency_code);-1!=t&&(this.index=t,this.currency[this.index].value=Math.trunc(this.currency[this.index].value))}}ngOnInit(){this.tenant_info=this.roleService.currency_info,this.tenantName=this.tenant_info.tenant_name,this.currency_code=this.tenant_info.default_currency?this.tenant_info.default_currency:"INR",this.setCurrency(),this.isConvertValue=null==this.tenant_info.is_to_convert_currency_value||this.tenant_info.is_to_convert_currency_value}setCurrency(){if(this.currency){const e=c.findIndex(this.currency,e=>e.currency_code==this.currency_code);-1!=e&&(this.index=e,this.currency[this.index].value=Math.trunc(this.currency[this.index].value))}}change(){void 0!==event.stopPropagation?event.stopPropagation():void 0!==event.cancelBubble&&(event.cancelBubble=!0),this.currency.length>1&&(this.index=++this.index%this.currency.length),this.currency[this.index].value=Math.trunc(this.currency[this.index].value)}convert(e,t){return e?(this.showActualAmount?e=e+" "+t:1==this.isConvertValue?e="INR"==t?(e/1e7).toFixed(2)+" Cr":(e/1e6).toFixed(2)+" M":0!=this.isConvertValue||c.contains(["big","small"],this.type)?0==this.isConvertValue&&c.contains(["big","small"],this.type)&&(e="INR"==t?"INR "+new Intl.NumberFormat("en-IN",{}).format(e):"USD "+new Intl.NumberFormat("en-US",{}).format(e)):e="INR"==t?(e/1e7).toFixed(2)+" Cr":(e/1e6).toFixed(2)+" M",e):"-"}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](O.a))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-currency"]],inputs:{type:"type",label:"label",showActualAmount:"showActualAmount",acl:"acl",currencyList:"currencyList"},decls:1,vars:1,consts:[["class","d-flex currency-wrapper",3,"click",4,"ngIf"],[1,"d-flex","currency-wrapper",3,"click"],["class","d-flex",3,"matTooltip",4,"ngIf"],["class","data-label",3,"matTooltip",4,"ngIf"],["class","data-medium d-flex",3,"matTooltip",4,"ngIf"],["class","justify-content-center",3,"matTooltip",4,"ngIf"],[1,"d-flex","align-items-center"],["matTooltip","Next currency",1,"change-icon","pl-2"],[1,"d-flex",3,"matTooltip"],[1,"pr-3","my-auto","currency-code","justify-content-center"],[1,"m-0","header"],[1,"m-0","data-label","pt-1"],[1,"data-label",3,"matTooltip"],[1,"data-medium","d-flex",3,"matTooltip"],[1,"justify-content-center"],[1,"justify-content-center",3,"matTooltip"],[1,"data-overview"]],template:function(e,t){1&e&&o["\u0275\u0275template"](0,A,8,4,"div",0),2&e&&o["\u0275\u0275property"]("ngIf",t.currency)},directives:[i.NgIf,E.a,C.a],styles:[".header[_ngcontent-%COMP%]{color:#5f5f5f;font-size:11px!important}.data-label[_ngcontent-%COMP%]{font-size:13px!important}.change-icon[_ngcontent-%COMP%]{font-size:18px;width:auto;height:auto;visibility:hidden;cursor:default}.currency-code[_ngcontent-%COMP%]{padding-right:8px;font-size:14px!important}.currency-wrapper[_ngcontent-%COMP%]{white-space:nowrap!important;height:100%}.currency-wrapper[_ngcontent-%COMP%]:hover   .change-icon[_ngcontent-%COMP%]{visibility:visible}.data-medium[_ngcontent-%COMP%]{font-weight:440!important}.data-medium[_ngcontent-%COMP%], .data-overview[_ngcontent-%COMP%]{color:#252422!important;font-size:15px!important;text-align:center!important;padding-right:4px!important}.data-overview[_ngcontent-%COMP%]{padding-left:24px!important;font-weight:500!important;margin:auto!important}"]}),e})();var N=n("WGBV"),L=n("1nMH");let j=(()=>{class e{constructor(e){this._userProfile=e,this.imgSrc="assets/images/User.png",this.imageList={},this.imgWidth="37px",this.imgHeight="37px",this.borderWidth="0px",this.borderStyle="solid",this.borderColor="#000000"}ngOnInit(){return Object(d.c)(this,void 0,void 0,(function*(){}))}ngOnChanges(){return Object(d.c)(this,void 0,void 0,(function*(){try{if(this.id)if(this.imageList[this.id])this.imgSrc=this.imageList[this.id];else{let e=yield this._userProfile.getUserProfileImage(this.id);null==e?this.imgSrc="assets/images/User.png":(this.imgSrc=e,this.imageList[this.id]=this.imgSrc)}else this.imgSrc="assets/images/User.png"}catch(e){this.imgSrc="assets/images/User.png",this.imageList[this.id]=this.imgSrc}}))}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](L.a))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-user-image"]],inputs:{id:"id",imgWidth:"imgWidth",imgHeight:"imgHeight",borderWidth:"borderWidth",borderStyle:"borderStyle",borderColor:"borderColor"},features:[o["\u0275\u0275NgOnChangesFeature"]],decls:1,vars:11,consts:[[3,"src"]],template:function(e,t){1&e&&o["\u0275\u0275element"](0,"img",0),2&e&&(o["\u0275\u0275styleProp"]("height",t.imgHeight)("width",t.imgWidth)("border-width",t.borderWidth)("border-style",t.borderStyle)("border-color",t.borderColor),o["\u0275\u0275property"]("src",t.imgSrc,o["\u0275\u0275sanitizeUrl"]))},styles:["img[_ngcontent-%COMP%]{border-radius:50%!important}"]}),e})();function B(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div",4),o["\u0275\u0275elementStart"](1,"div",5),o["\u0275\u0275elementStart"](2,"div",6),o["\u0275\u0275elementStart"](3,"button",7),o["\u0275\u0275elementStart"](4,"mat-icon",8),o["\u0275\u0275text"](5,"more_vert"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](6,"mat-menu",null,9),o["\u0275\u0275elementStart"](8,"button",10),o["\u0275\u0275listener"]("click",(function(){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"]().removeMember()})),o["\u0275\u0275elementStart"](9,"span"),o["\u0275\u0275text"](10,"Remove"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](11,"div",11),o["\u0275\u0275element"](12,"app-user-image",12),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](13,"div",13),o["\u0275\u0275elementStart"](14,"span",14),o["\u0275\u0275text"](15),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](16,"div",15),o["\u0275\u0275text"](17),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](18,"div",16),o["\u0275\u0275elementStart"](19,"mat-icon",17),o["\u0275\u0275text"](20," mail_outline "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275text"](21),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](22,"div",18),o["\u0275\u0275elementStart"](23,"mat-icon",17),o["\u0275\u0275text"](24," phone "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275text"](25," - "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275reference"](7),t=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("matMenuTriggerFor",e),o["\u0275\u0275advance"](9),o["\u0275\u0275property"]("id",t.oid),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("matTooltip",t.profile.displayName),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate"](t.profile.displayName),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate1"](" ",t.profile.jobTitle," "),o["\u0275\u0275advance"](4),o["\u0275\u0275textInterpolate1"](" ",t.profile.mail," ")}}function V(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",19),o["\u0275\u0275elementStart"](1,"div",20),o["\u0275\u0275element"](2,"app-user-image",21),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](3,"div",22),o["\u0275\u0275elementStart"](4,"p",23),o["\u0275\u0275text"](5),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](6,"p",24),o["\u0275\u0275text"](7),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("id",e.oid),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("matTooltip",e.profile.displayName),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate"](e.role),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate1"](" ",e.profile.displayName," ")}}function U(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div"),o["\u0275\u0275elementStart"](1,"div",25),o["\u0275\u0275element"](2,"app-user-image",26),o["\u0275\u0275elementStart"](3,"p",27),o["\u0275\u0275text"](4),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("id",e.oid),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate1"](" ",e.profile.displayName," ")}}function z(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div"),o["\u0275\u0275elementStart"](1,"div",25),o["\u0275\u0275element"](2,"app-user-image",26),o["\u0275\u0275elementStart"](3,"p",28),o["\u0275\u0275text"](4),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("id",e.oid),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate1"](" ",null==e.profile?null:e.profile.displayName," ")}}function $(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div"),o["\u0275\u0275elementStart"](1,"div",25),o["\u0275\u0275elementStart"](2,"div",20),o["\u0275\u0275element"](3,"app-user-image",29),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](4,"div",30),o["\u0275\u0275elementStart"](5,"p",23),o["\u0275\u0275text"](6),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](7,"p",24),o["\u0275\u0275text"](8),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("id",e.oid),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("matTooltip",null==e.profile?null:e.profile.displayName),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate"](e.role),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate1"](" ",null==e.profile?null:e.profile.displayName," ")}}function q(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",31),o["\u0275\u0275elementStart"](1,"p",32),o["\u0275\u0275text"](2),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275property"]("id",e.oid),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate1"](" ",null==e.profile?null:e.profile.displayName," ")}}function R(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div"),o["\u0275\u0275element"](1,"app-user-image",33),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("id",e.oid)}}function H(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div"),o["\u0275\u0275text"](1),o["\u0275\u0275elementStart"](2,"span",34),o["\u0275\u0275element"](3,"app-user-image",26),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" ",null==e.profile?null:e.profile.displayName," "),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("id",e.oid)}}function K(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div"),o["\u0275\u0275elementStart"](1,"div",35),o["\u0275\u0275element"](2,"app-user-image",36),o["\u0275\u0275elementStart"](3,"p",37),o["\u0275\u0275text"](4),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("id",e.oid),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate1"](" ",null==e.profile?null:e.profile.displayName," ")}}let Y=(()=>{class e{constructor(e,t,n){this.graphApi=e,this.accountService=t,this.snackBar=n,this.profile={},this.refreshList=new o.EventEmitter}ngOnInit(){}ngOnChanges(){this.getUserProfile()}getUserProfile(){return Object(d.c)(this,void 0,void 0,(function*(){if(this.oid)try{if(console.log(this.oid),null!=this.oid&&"-"!=this.oid){let e=yield this.getUserProfileFromDB(this.oid);this.profile=e}}catch(e){}else this.profile={}}))}getUserProfileFromDB(e){return Object(d.c)(this,void 0,void 0,(function*(){let t=yield this.accountService.getUserProfileFromDB(e);return t?t[0]:""}))}removeMember(){try{this.accountService.removeMember(this.stakeholder_id).then(e=>{this.refreshList.emit(),this.snackBar.open("Member Removed.","Dismiss",{duration:2e3})},e=>{console.log(e)})}catch(e){console.log(e)}}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](b.a),o["\u0275\u0275directiveInject"](N.a),o["\u0275\u0275directiveInject"](h.a))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-user-profile"]],inputs:{type:"type",oid:"oid",role:"role",stakeholder_id:"stakeholder_id",project_id:"project_id",item_id:"item_id"},outputs:{refreshList:"refreshList"},features:[o["\u0275\u0275NgOnChangesFeature"]],decls:9,vars:9,consts:[["class","card card-design",4,"ngIf"],["class","d-flex flex-row pb-2 pb-lg-0 px-0",4,"ngIf"],[4,"ngIf"],[3,"id",4,"ngIf"],[1,"card","card-design"],[1,"card-body",2,"padding","6px !important"],[1,"row","justify-content-end",2,"margin","-7px !important"],["mat-icon-button","",3,"matMenuTriggerFor"],[2,"color","#847E7E"],["menu","matMenu"],["mat-menu-item","",3,"click"],[1,"row","justify-content-center"],["imgWidth","55px","imgHeight","55px",3,"id"],[1,"row","justify-content-center","pt-1",3,"matTooltip"],[1,"username"],[1,"row","justify-content-center",2,"color","#847E7E","font-size","12px !important"],[1,"row","justify-content-center","pt-1","flex-nowrap","user-mail"],[2,"font-size","17px !important"],[1,"row","justify-content-center","pb-1",2,"color","#1E2733","font-size","12px !important"],[1,"d-flex","flex-row","pb-2","pb-lg-0","px-0"],[2,"flex","1"],["imgWidth","32px","imgHeight","32px",1,"my-auto","pr-1",3,"id"],[2,"flex","3","padding-left","8px !important","width","110px !important",3,"matTooltip"],[1,"m-0","header","text-overflow"],[1,"m-0","data-label","text-overflow"],[1,"d-flex","flex-row","pb-2","pb-lg-0","px-0","flex-nowrap"],["imgWidth","24px","imgHeight","24px",1,"my-auto","pr-1",3,"id"],[1,"text-overflow","m-0","data-label","pl-1"],[1,"text-overflow-expense","m-0","data-label","pl-1"],["imgWidth","45px","imgHeight","45px",1,"my-auto","pr-1",3,"id"],[2,"flex","6","padding-left","8px !important","width","250px !important",3,"matTooltip"],[3,"id"],[1,"text-overflow-name","m-0","data-label","pl-0"],["imgWidth","28px","imgHeight","28px",1,"my-auto","pr-1",3,"id"],[2,"padding-left","10px"],[1,"d-flex","flex-row","pb-2","pl-1","pb-lg-0","px-0","flex-nowrap"],["imgWidth","18px","imgHeight","18px",1,"my-auto","pr-1",3,"id"],[1,"text-overflow","m-0","data-label","filter-user-name","pl-1"]],template:function(e,t){1&e&&(o["\u0275\u0275template"](0,B,26,6,"div",0),o["\u0275\u0275template"](1,V,8,4,"div",1),o["\u0275\u0275template"](2,U,5,2,"div",2),o["\u0275\u0275template"](3,z,5,2,"div",2),o["\u0275\u0275template"](4,$,9,4,"div",2),o["\u0275\u0275template"](5,q,3,2,"div",3),o["\u0275\u0275template"](6,R,2,1,"div",2),o["\u0275\u0275template"](7,H,4,2,"div",2),o["\u0275\u0275template"](8,K,5,2,"div",2)),2&e&&(o["\u0275\u0275property"]("ngIf","big"==t.type),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf","medium"==t.type),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf","small"==t.type),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf","expense-card-data"==t.type),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf","user-badge"==t.type),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf","name"==t.type),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf","user_img"==t.type),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf","mini"==t.type),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf","filter-user-img"==t.type))},directives:[i.NgIf,_.a,w.f,E.a,w.g,w.d,j,C.a],styles:[".card-design[_ngcontent-%COMP%]{max-width:200x!important;min-width:190px!important;min-height:195px!important;max-height:270px!important;padding:0!important}.data-label[_ngcontent-%COMP%]{font-size:14px!important}.header[_ngcontent-%COMP%]{color:#5f5f5f;font-size:11px!important}.user-mail[_ngcontent-%COMP%]{color:#1e2733;font-size:12px!important;width:100%}.text-overflow[_ngcontent-%COMP%], .user-mail[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.text-overflow[_ngcontent-%COMP%]{width:81%}.text-overflow-expense[_ngcontent-%COMP%]{padding-top:5px;width:90%;font-size:13px!important}.text-overflow-expense[_ngcontent-%COMP%], .username[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.username[_ngcontent-%COMP%]{color:#1e2733;font-size:14px!important}.text-overflow-name[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.filter-user-name[_ngcontent-%COMP%]{padding-top:2px!important}"]}),e})();var W=n("vFgw"),G=n("wd/R");let X=(()=>{class e{transform(e){return"Invalid date"!=G(e).utc().format("DD MMM YYYY")?G(e).utc().format("DD MMM YYYY"):"-"}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=o["\u0275\u0275definePipe"]({name:"DDMMYYYY",type:e,pure:!0}),e})();const J=["noteComponent"];function Q(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",55),o["\u0275\u0275elementStart"](1,"div",56),o["\u0275\u0275text"](2),o["\u0275\u0275pipe"](3,"tenantLead"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](4,"div",57),o["\u0275\u0275element"](5,"app-currency",58),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate1"](" ",o["\u0275\u0275pipeBind3"](3,2,"leadValueUSD",e.formFieldData,"label")," "),o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("currencyList",e.leadDetails?e.leadDetails.lead_value:"")}}function Z(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div",55),o["\u0275\u0275elementStart"](1,"div",56),o["\u0275\u0275text"](2),o["\u0275\u0275pipe"](3,"tenantLead"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](4,"div",59),o["\u0275\u0275listener"]("click",(function(){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"](2).navigateToAccounts()})),o["\u0275\u0275text"](5),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate1"](" ",o["\u0275\u0275pipeBind3"](3,2,"inAccount",e.formFieldData,"label")," "),o["\u0275\u0275advance"](3),o["\u0275\u0275textInterpolate1"](" ",e.leadDetails.account_name?e.leadDetails.account_name:"-"," ")}}function ee(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div",55),o["\u0275\u0275elementStart"](1,"div",56),o["\u0275\u0275text"](2),o["\u0275\u0275pipe"](3,"tenantLead"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](4,"div",60),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](e);const t=o["\u0275\u0275nextContext"](2);return t.activateInlineEdit("Lead source","minimal-dropdown",t.leadSourceMaster,"")})),o["\u0275\u0275text"](5),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate1"](" ",o["\u0275\u0275pipeBind3"](3,2,"leadSource",e.formFieldData,"label")," "),o["\u0275\u0275advance"](3),o["\u0275\u0275textInterpolate1"](" ",e.leadDetails.lead_source_name?e.leadDetails.lead_source_name:"-"," ")}}function te(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div",55),o["\u0275\u0275elementStart"](1,"div",56),o["\u0275\u0275text"](2),o["\u0275\u0275pipe"](3,"tenantLead"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](4,"div",60),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](e);const t=o["\u0275\u0275nextContext"](2);return t.activateInlineEdit("Lead score","minimal-dropdown",t.leadScoreMaster,"")})),o["\u0275\u0275text"](5),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate1"](" ",o["\u0275\u0275pipeBind3"](3,2,"leadScore",e.formFieldData,"label")," "),o["\u0275\u0275advance"](3),o["\u0275\u0275textInterpolate1"](" ",e.leadDetails.leads_score?e.leadDetails.leads_score:"-"," ")}}function ne(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div",55),o["\u0275\u0275elementStart"](1,"div",56),o["\u0275\u0275text"](2),o["\u0275\u0275pipe"](3,"tenantLead"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](4,"div",60),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](e);const t=o["\u0275\u0275nextContext"](2);return t.activateInlineEdit("Lead channel","minimal-dropdown",t.leadsChannelList,"")})),o["\u0275\u0275text"](5),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate1"](" ",o["\u0275\u0275pipeBind3"](3,2,"leadsChannel",e.formFieldData,"label")," "),o["\u0275\u0275advance"](3),o["\u0275\u0275textInterpolate1"](" ",e.leadDetails.channel_name?e.leadDetails.channel_name:"-"," ")}}function ie(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",55),o["\u0275\u0275elementStart"](1,"div",56),o["\u0275\u0275text"](2),o["\u0275\u0275pipe"](3,"tenantLead"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](4,"div",61),o["\u0275\u0275text"](5),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate1"](" ",o["\u0275\u0275pipeBind3"](3,2,"leadChannelDes",e.formFieldData,"label")," "),o["\u0275\u0275advance"](3),o["\u0275\u0275textInterpolate1"](" ",e.leadDetails.channel_name?e.leadDetails.channel_description:"-"," ")}}const ae=function(){return{transform:"scale(0.8)"}};function oe(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"button",62),o["\u0275\u0275elementStart"](1,"mat-icon"),o["\u0275\u0275text"](2,"email"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e&&o["\u0275\u0275property"]("ngStyle",o["\u0275\u0275pureFunction0"](1,ae))}function le(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",50),o["\u0275\u0275elementStart"](1,"div",51),o["\u0275\u0275elementStart"](2,"span"),o["\u0275\u0275elementStart"](3,"mat-icon",52),o["\u0275\u0275text"](4," assessment "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](5,"div",53),o["\u0275\u0275text"](6),o["\u0275\u0275pipe"](7,"tenantLead"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](8,"div",63),o["\u0275\u0275text"](9),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](6),o["\u0275\u0275textInterpolate1"](" ",o["\u0275\u0275pipeBind3"](7,3,"requirement",e.formFieldData,"label")," "),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("matTooltip",e.leadDetails.requirement_name?e.leadDetails.requirement_name:"-"),o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" ",e.leadDetails.requirement_name?e.leadDetails.requirement_name:"-"," ")}}function re(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",50),o["\u0275\u0275elementStart"](1,"div",51),o["\u0275\u0275elementStart"](2,"span"),o["\u0275\u0275elementStart"](3,"mat-icon",52),o["\u0275\u0275text"](4," assessment "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](5,"div",53),o["\u0275\u0275text"](6),o["\u0275\u0275pipe"](7,"tenantLead"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](8,"div",54),o["\u0275\u0275text"](9," - "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](6),o["\u0275\u0275textInterpolate1"](" ",o["\u0275\u0275pipeBind3"](7,1,"secondaryRequirements",e.formFieldData,"label")," ")}}function se(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",50),o["\u0275\u0275elementStart"](1,"div",51),o["\u0275\u0275elementStart"](2,"span"),o["\u0275\u0275elementStart"](3,"mat-icon",52),o["\u0275\u0275text"](4," assessment "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](5,"div",53),o["\u0275\u0275text"](6),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](7,"div",63),o["\u0275\u0275text"](8),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=t.index;o["\u0275\u0275advance"](6),o["\u0275\u0275textInterpolate1"](" Secondary Req ",n+1," "),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("matTooltip",e),o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" ",e," ")}}function de(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",50),o["\u0275\u0275elementStart"](1,"div",51),o["\u0275\u0275elementStart"](2,"span"),o["\u0275\u0275elementStart"](3,"mat-icon",52),o["\u0275\u0275text"](4," domain "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](5,"div",53),o["\u0275\u0275text"](6),o["\u0275\u0275pipe"](7,"tenantLead"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](8,"div",54),o["\u0275\u0275text"](9),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](6),o["\u0275\u0275textInterpolate1"](" ",o["\u0275\u0275pipeBind3"](7,2,"salesUnit",e.formFieldData,"label")," "),o["\u0275\u0275advance"](3),o["\u0275\u0275textInterpolate1"](" ",e.leadDetails.sales_unit_name?e.leadDetails.sales_unit_name:"-"," ")}}function ce(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div",50),o["\u0275\u0275elementStart"](1,"div",51),o["\u0275\u0275elementStart"](2,"span"),o["\u0275\u0275elementStart"](3,"mat-icon",52),o["\u0275\u0275text"](4," event "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](5,"div",53),o["\u0275\u0275text"](6),o["\u0275\u0275pipe"](7,"tenantLead"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](8,"div",64),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](e);const t=o["\u0275\u0275nextContext"](2);return t.activateInlineEdit("Closure date","date-picker",[],t.leadDetails)})),o["\u0275\u0275text"](9),o["\u0275\u0275pipe"](10,"DDMMYYYY"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](6),o["\u0275\u0275textInterpolate1"](" ",o["\u0275\u0275pipeBind3"](7,2,"leadsClosureDate",e.formFieldData,"label")," "),o["\u0275\u0275advance"](3),o["\u0275\u0275textInterpolate1"](" ",null!=e.leadDetails&&e.leadDetails.leads_closure_date?o["\u0275\u0275pipeBind1"](10,6,null==e.leadDetails?null:e.leadDetails.leads_closure_date):"-"," ")}}function pe(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div",50),o["\u0275\u0275elementStart"](1,"div",51),o["\u0275\u0275elementStart"](2,"span"),o["\u0275\u0275elementStart"](3,"mat-icon",52),o["\u0275\u0275text"](4," person "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](5,"div",53),o["\u0275\u0275text"](6),o["\u0275\u0275pipe"](7,"tenantLead"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](8,"div",64),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](e);const t=o["\u0275\u0275nextContext"](2);return t.activateInlineEdit("Name","simple-text",[],t.leadDetails)})),o["\u0275\u0275text"](9),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](6),o["\u0275\u0275textInterpolate1"](" ",o["\u0275\u0275pipeBind3"](7,3,"contactFirstName",e.formFieldData,"label")," "),o["\u0275\u0275advance"](3),o["\u0275\u0275textInterpolate2"](" ",e.leadDetails.first_name?e.leadDetails.first_name:"-"," ",e.leadDetails.last_name?e.leadDetails.last_name:" "," ")}}function me(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div",50),o["\u0275\u0275elementStart"](1,"div",51),o["\u0275\u0275elementStart"](2,"span"),o["\u0275\u0275elementStart"](3,"mat-icon",52),o["\u0275\u0275text"](4," business "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](5,"div",53),o["\u0275\u0275text"](6),o["\u0275\u0275pipe"](7,"tenantLead"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](8,"div",64),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](e);const t=o["\u0275\u0275nextContext"](2);return t.activateInlineEdit("Job title","simple-text",[],t.leadDetails)})),o["\u0275\u0275text"](9),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](6),o["\u0275\u0275textInterpolate1"](" ",o["\u0275\u0275pipeBind3"](7,2,"jobTitle",e.formFieldData,"label")," "),o["\u0275\u0275advance"](3),o["\u0275\u0275textInterpolate1"](" ",e.leadDetails.job_title?e.leadDetails.job_title:"-"," ")}}function ue(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div",50),o["\u0275\u0275elementStart"](1,"div",51),o["\u0275\u0275elementStart"](2,"span"),o["\u0275\u0275elementStart"](3,"mat-icon",52),o["\u0275\u0275text"](4," mail "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](5,"div",53),o["\u0275\u0275text"](6),o["\u0275\u0275pipe"](7,"tenantLead"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](8,"div",64),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](e);const t=o["\u0275\u0275nextContext"](2);return t.activateInlineEdit("Email","simple-text",[],t.leadDetails)})),o["\u0275\u0275text"](9),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](6),o["\u0275\u0275textInterpolate1"](" ",o["\u0275\u0275pipeBind3"](7,2,"contactEmail",e.formFieldData,"label")," "),o["\u0275\u0275advance"](3),o["\u0275\u0275textInterpolate1"](" ",e.leadDetails.email?e.leadDetails.email:"-"," ")}}function he(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",50),o["\u0275\u0275elementStart"](1,"div",51),o["\u0275\u0275elementStart"](2,"span"),o["\u0275\u0275elementStart"](3,"mat-icon",52),o["\u0275\u0275text"](4," phone "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](5,"div",53),o["\u0275\u0275text"](6),o["\u0275\u0275pipe"](7,"tenantLead"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](8,"div",54),o["\u0275\u0275text"](9),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](6),o["\u0275\u0275textInterpolate1"](" ",o["\u0275\u0275pipeBind3"](7,2,"contactTelephone",e.formFieldData,"label")," "),o["\u0275\u0275advance"](3),o["\u0275\u0275textInterpolate1"](" ",e.leadDetails.telephone?e.leadDetails.telephone:"-"," ")}}function ve(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div",50),o["\u0275\u0275elementStart"](1,"div",51),o["\u0275\u0275elementStart"](2,"span"),o["\u0275\u0275elementStart"](3,"mat-icon",52),o["\u0275\u0275text"](4," phone_iphone "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](5,"div",53),o["\u0275\u0275text"](6),o["\u0275\u0275pipe"](7,"tenantLead"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](8,"div",64),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](e);const t=o["\u0275\u0275nextContext"](2);return t.activateInlineEdit("Phone","simple-text",[],t.leadDetails)})),o["\u0275\u0275text"](9),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](6),o["\u0275\u0275textInterpolate1"](" ",o["\u0275\u0275pipeBind3"](7,2,"phone",e.formFieldData,"label")," "),o["\u0275\u0275advance"](3),o["\u0275\u0275textInterpolate1"](" ",e.leadDetails.mobile_number?e.leadDetails.mobile_number:"-"," ")}}function ge(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div",50),o["\u0275\u0275elementStart"](1,"div",51),o["\u0275\u0275elementStart"](2,"span"),o["\u0275\u0275elementStart"](3,"mat-icon",52),o["\u0275\u0275text"](4," account_circle "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](5,"div",53),o["\u0275\u0275text"](6),o["\u0275\u0275pipe"](7,"tenantLead"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](8,"div",64),o["\u0275\u0275listener"]("click",(function(){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"](2).activateInlineEdit("Lead owner","search-dropdown",[],"")})),o["\u0275\u0275element"](9,"app-user-profile",65),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](6),o["\u0275\u0275textInterpolate1"](" ",o["\u0275\u0275pipeBind3"](7,2,"leadOwner",e.formFieldData,"label")," "),o["\u0275\u0275advance"](3),o["\u0275\u0275propertyInterpolate"]("oid",e.leadDetails?e.leadDetails.lead_owner_oid:"-")}}function fe(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div",50),o["\u0275\u0275elementStart"](1,"div",51),o["\u0275\u0275elementStart"](2,"span"),o["\u0275\u0275elementStart"](3,"mat-icon",52),o["\u0275\u0275text"](4," account_circle "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](5,"div",53),o["\u0275\u0275text"](6),o["\u0275\u0275pipe"](7,"tenantLead"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](8,"div",64),o["\u0275\u0275listener"]("click",(function(){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"](2).activateInlineEdit("Sales owner","search-dropdown",[],"")})),o["\u0275\u0275element"](9,"app-user-profile",65),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](6),o["\u0275\u0275textInterpolate1"](" ",o["\u0275\u0275pipeBind3"](7,2,"salesOwner",e.formFieldData,"label")," "),o["\u0275\u0275advance"](3),o["\u0275\u0275propertyInterpolate"]("oid",e.leadDetails?e.leadDetails.sales_owner_oid:"-")}}function ye(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div",50),o["\u0275\u0275elementStart"](1,"div",51),o["\u0275\u0275elementStart"](2,"span"),o["\u0275\u0275elementStart"](3,"mat-icon",52),o["\u0275\u0275text"](4," account_circle "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](5,"div",53),o["\u0275\u0275text"](6),o["\u0275\u0275pipe"](7,"tenantLead"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](8,"div",64),o["\u0275\u0275listener"]("click",(function(){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"](2).activateInlineEdit("Marketing owner","search-dropdown",[],"")})),o["\u0275\u0275element"](9,"app-user-profile",65),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](6),o["\u0275\u0275textInterpolate1"](" ",o["\u0275\u0275pipeBind3"](7,2,"marketingOwner",e.formFieldData,"label")," "),o["\u0275\u0275advance"](3),o["\u0275\u0275propertyInterpolate"]("oid",e.leadDetails.marketing_owner_oid?e.leadDetails.marketing_owner_oid:"-")}}const xe=function(e){return[e]};function Se(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementContainerStart"](0),o["\u0275\u0275elementStart"](1,"div",22),o["\u0275\u0275elementStart"](2,"div",23),o["\u0275\u0275elementStart"](3,"div",24),o["\u0275\u0275elementStart"](4,"div",15),o["\u0275\u0275elementStart"](5,"div",25),o["\u0275\u0275elementStart"](6,"div",26),o["\u0275\u0275elementStart"](7,"div",9),o["\u0275\u0275elementStart"](8,"div",27),o["\u0275\u0275elementStart"](9,"div",28),o["\u0275\u0275text"](10," - "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](11,"div",29),o["\u0275\u0275text"](12," Contacts "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](13,"div",27),o["\u0275\u0275elementStart"](14,"div",28),o["\u0275\u0275text"](15),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](16,"div",29),o["\u0275\u0275text"](17," Meetings "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](18,"div",27),o["\u0275\u0275elementStart"](19,"div",28),o["\u0275\u0275text"](20),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](21,"div",29),o["\u0275\u0275text"](22," Call log "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](23,"div",30),o["\u0275\u0275elementStart"](24,"div",28),o["\u0275\u0275text"](25),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](26,"div",29),o["\u0275\u0275text"](27," Mail "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](28,"div",31),o["\u0275\u0275elementStart"](29,"div",11),o["\u0275\u0275elementStart"](30,"div",32),o["\u0275\u0275elementStart"](31,"div",26),o["\u0275\u0275elementStart"](32,"div",9),o["\u0275\u0275elementStart"](33,"div",33),o["\u0275\u0275elementStart"](34,"div",34),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](e);const t=o["\u0275\u0275nextContext"]();return t.activateInlineEdit("Lead name","simple-text",[],t.leadDetails)})),o["\u0275\u0275text"](35),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](36,Q,6,6,"div",35),o["\u0275\u0275pipe"](37,"tenantLead"),o["\u0275\u0275template"](38,Z,6,6,"div",35),o["\u0275\u0275pipe"](39,"tenantLead"),o["\u0275\u0275template"](40,ee,6,6,"div",35),o["\u0275\u0275pipe"](41,"tenantLead"),o["\u0275\u0275template"](42,te,6,6,"div",35),o["\u0275\u0275pipe"](43,"tenantLead"),o["\u0275\u0275template"](44,ne,6,6,"div",35),o["\u0275\u0275pipe"](45,"tenantLead"),o["\u0275\u0275template"](46,ie,6,6,"div",35),o["\u0275\u0275pipe"](47,"tenantLead"),o["\u0275\u0275elementStart"](48,"div",36),o["\u0275\u0275template"](49,oe,3,2,"button",37),o["\u0275\u0275elementStart"](50,"button",38),o["\u0275\u0275listener"]("click",(function(){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"]().createNewTask()})),o["\u0275\u0275elementStart"](51,"mat-icon"),o["\u0275\u0275text"](52,"assignment"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](53,"button",39),o["\u0275\u0275listener"]("click",(function(){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"]().openMeetingModal()})),o["\u0275\u0275elementStart"](54,"mat-icon"),o["\u0275\u0275text"](55,"event_available"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](56,"button",40),o["\u0275\u0275listener"]("click",(function(){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"]().addNotes()})),o["\u0275\u0275elementStart"](57,"mat-icon"),o["\u0275\u0275text"](58,"note_add"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](59,"button",41),o["\u0275\u0275elementStart"](60,"mat-icon"),o["\u0275\u0275text"](61,"add_circle"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](62,"mat-menu",null,42),o["\u0275\u0275elementStart"](64,"button",43),o["\u0275\u0275listener"]("click",(function(){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"]().createCallLog()})),o["\u0275\u0275text"](65,"Log a call"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](66,"button",43),o["\u0275\u0275listener"]("click",(function(){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"]().createMail()})),o["\u0275\u0275text"](67,"Log an email"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](68,"button",43),o["\u0275\u0275listener"]("click",(function(){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"]().createMeeting()})),o["\u0275\u0275text"](69,"Log a meeting"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](70,"div",44),o["\u0275\u0275elementStart"](71,"div",45),o["\u0275\u0275text"](72," More info "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](73,"div",9),o["\u0275\u0275elementStart"](74,"div",15),o["\u0275\u0275template"](75,le,10,7,"div",46),o["\u0275\u0275pipe"](76,"tenantLead"),o["\u0275\u0275template"](77,re,10,5,"div",46),o["\u0275\u0275pipe"](78,"tenantLead"),o["\u0275\u0275template"](79,se,9,3,"div",47),o["\u0275\u0275template"](80,de,10,6,"div",46),o["\u0275\u0275pipe"](81,"tenantLead"),o["\u0275\u0275template"](82,ce,11,8,"div",46),o["\u0275\u0275pipe"](83,"tenantLead"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275element"](84,"div",48),o["\u0275\u0275elementStart"](85,"div",44),o["\u0275\u0275elementStart"](86,"div",45),o["\u0275\u0275text"](87," Contact "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](88,"div",9),o["\u0275\u0275elementStart"](89,"div",15),o["\u0275\u0275template"](90,pe,10,7,"div",49),o["\u0275\u0275pipe"](91,"tenantLead"),o["\u0275\u0275template"](92,me,10,6,"div",46),o["\u0275\u0275pipe"](93,"tenantLead"),o["\u0275\u0275template"](94,ue,10,6,"div",49),o["\u0275\u0275pipe"](95,"tenantLead"),o["\u0275\u0275template"](96,he,10,6,"div",46),o["\u0275\u0275pipe"](97,"tenantLead"),o["\u0275\u0275template"](98,ve,10,6,"div",46),o["\u0275\u0275pipe"](99,"tenantLead"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275element"](100,"div",48),o["\u0275\u0275elementStart"](101,"div",44),o["\u0275\u0275elementStart"](102,"div",45),o["\u0275\u0275text"](103),o["\u0275\u0275pipe"](104,"tenantLead"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](105,"div",9),o["\u0275\u0275elementStart"](106,"div",15),o["\u0275\u0275template"](107,ge,10,6,"div",46),o["\u0275\u0275pipe"](108,"tenantLead"),o["\u0275\u0275template"](109,fe,10,6,"div",46),o["\u0275\u0275pipe"](110,"tenantLead"),o["\u0275\u0275template"](111,ye,10,6,"div",46),o["\u0275\u0275pipe"](112,"tenantLead"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275element"](113,"div",48),o["\u0275\u0275elementStart"](114,"div",44),o["\u0275\u0275elementStart"](115,"div",45),o["\u0275\u0275text"](116," Others "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](117,"div",9),o["\u0275\u0275elementStart"](118,"div",15),o["\u0275\u0275elementStart"](119,"div",50),o["\u0275\u0275elementStart"](120,"div",51),o["\u0275\u0275elementStart"](121,"span",52),o["\u0275\u0275text"](122," # "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](123,"div",53),o["\u0275\u0275text"](124," Lead Id "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](125,"div",54),o["\u0275\u0275text"](126),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](127,"div",50),o["\u0275\u0275elementStart"](128,"div",51),o["\u0275\u0275elementStart"](129,"span"),o["\u0275\u0275elementStart"](130,"mat-icon",52),o["\u0275\u0275text"](131," event "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](132,"div",53),o["\u0275\u0275text"](133," Lead created on "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](134,"div",54),o["\u0275\u0275text"](135),o["\u0275\u0275pipe"](136,"DDMMYYYY"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=o["\u0275\u0275reference"](63),t=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngClass",o["\u0275\u0275pureFunction1"](112,xe,"salesStatusBorder_"+t.leadDetails.status_id)),o["\u0275\u0275advance"](14),o["\u0275\u0275textInterpolate1"](" ",t.leadDetails.meetings_count," "),o["\u0275\u0275advance"](5),o["\u0275\u0275textInterpolate1"](" ",t.leadDetails.call_log_count," "),o["\u0275\u0275advance"](5),o["\u0275\u0275textInterpolate1"](" ",t.leadDetails.mail_count," "),o["\u0275\u0275advance"](9),o["\u0275\u0275property"]("matTooltip",t.getLength(t.leadDetails.lead_name)>30?t.leadDetails.lead_name:""),o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" ",t.leadDetails.lead_name?t.leadDetails.lead_name:"-"," "),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",o["\u0275\u0275pipeBind3"](37,34,"leadValueUSD",t.formFieldData,"isActive")),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngIf",o["\u0275\u0275pipeBind3"](39,38,"inAccount",t.formFieldData,"isActive")),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngIf",o["\u0275\u0275pipeBind3"](41,42,"leadSource",t.formFieldData,"isActive")),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngIf",o["\u0275\u0275pipeBind3"](43,46,"leadScore",t.formFieldData,"isActive")),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngIf",o["\u0275\u0275pipeBind3"](45,50,"leadsChannel",t.formFieldData,"isActive")),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngIf",o["\u0275\u0275pipeBind3"](47,54,t.leadDetails.channel_name&&"leadChannelDes",t.formFieldData,"isActive")),o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("ngIf",t.mailAccessForLeads),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngStyle",o["\u0275\u0275pureFunction0"](114,ae)),o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("ngStyle",o["\u0275\u0275pureFunction0"](115,ae)),o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("ngStyle",o["\u0275\u0275pureFunction0"](116,ae)),o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("matMenuTriggerFor",e)("ngStyle",o["\u0275\u0275pureFunction0"](117,ae)),o["\u0275\u0275advance"](16),o["\u0275\u0275property"]("ngIf",o["\u0275\u0275pipeBind3"](76,58,"requirement",t.formFieldData,"isActive")),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngIf",o["\u0275\u0275pipeBind3"](78,62,0==t.secondary_industries_names.length&&"secondaryRequirements",t.formFieldData,"isActive")),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngForOf",t.secondary_industries_names),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",o["\u0275\u0275pipeBind3"](81,66,"salesUnit",t.formFieldData,"isActive")),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngIf",o["\u0275\u0275pipeBind3"](83,70,"leadsClosureDate",t.formFieldData,"isActive")),o["\u0275\u0275advance"](8),o["\u0275\u0275property"]("ngif",o["\u0275\u0275pipeBind3"](91,74,"contactFirstName",t.formFieldData,"isActive")),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngIf",o["\u0275\u0275pipeBind3"](93,78,"jobTitle",t.formFieldData,"isActive")),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngif",o["\u0275\u0275pipeBind3"](95,82,"contactEmail",t.formFieldData,"isActive")),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngIf",o["\u0275\u0275pipeBind3"](97,86,"contactTelephone",t.formFieldData,"isActive")),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngIf",o["\u0275\u0275pipeBind3"](99,90,"phone",t.formFieldData,"isActive")),o["\u0275\u0275advance"](5),o["\u0275\u0275textInterpolate1"](" ",o["\u0275\u0275pipeBind3"](104,94,"Owner",t.formFieldData,"label")," "),o["\u0275\u0275advance"](4),o["\u0275\u0275property"]("ngIf",o["\u0275\u0275pipeBind3"](108,98,"leadOwner",t.formFieldData,"isActive")),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngIf",o["\u0275\u0275pipeBind3"](110,102,"salesOwner",t.formFieldData,"isActive")),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngIf",o["\u0275\u0275pipeBind3"](112,106,"marketingOwner",t.formFieldData,"isActive")),o["\u0275\u0275advance"](15),o["\u0275\u0275textInterpolate1"](" LE",t.leadDetails.lead_id?t.leadDetails.lead_id:"-"," "),o["\u0275\u0275advance"](9),o["\u0275\u0275textInterpolate1"](" ",t.leadDetails.created_on?o["\u0275\u0275pipeBind1"](136,110,t.leadDetails.created_on):"-"," ")}}function be(e,t){if(1&e&&(o["\u0275\u0275elementContainerStart"](0),o["\u0275\u0275elementStart"](1,"div",9),o["\u0275\u0275elementStart"](2,"div",66),o["\u0275\u0275element"](3,"div",67),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](4,"div",68),o["\u0275\u0275text"](5),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementContainerEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](5),o["\u0275\u0275textInterpolate1"](" ",e.leadDetails.requirement_name?e.leadDetails.requirement_name:"-"," ")}}function _e(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",9),o["\u0275\u0275elementStart"](1,"div",66),o["\u0275\u0275element"](2,"div",73),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](3,"div",68),o["\u0275\u0275text"](4),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;o["\u0275\u0275advance"](4),o["\u0275\u0275textInterpolate1"](" ",e," ")}}function Ce(e,t){if(1&e&&(o["\u0275\u0275elementContainerStart"](0),o["\u0275\u0275template"](1,_e,5,1,"div",72),o["\u0275\u0275elementContainerEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngForOf",e.module_list)}}function we(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",69),o["\u0275\u0275elementStart"](1,"div",70),o["\u0275\u0275elementStart"](2,"span",71),o["\u0275\u0275text"](3," Modules "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](4,"div",15),o["\u0275\u0275elementStart"](5,"div",16),o["\u0275\u0275elementStart"](6,"div",17),o["\u0275\u0275template"](7,Ce,2,1,"ng-container",12),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](7),o["\u0275\u0275property"]("ngIf",e.leadDetails)}}function Ee(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"button",5),o["\u0275\u0275elementStart"](1,"mat-icon",6),o["\u0275\u0275text"](2,"more_vert"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){o["\u0275\u0275nextContext"]();const e=o["\u0275\u0275reference"](6);o["\u0275\u0275property"]("matMenuTriggerFor",e)}}function Ie(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",9),o["\u0275\u0275elementStart"](1,"div",66),o["\u0275\u0275element"](2,"div",80),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](3,"div",81),o["\u0275\u0275text"](4),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](5,"div",82),o["\u0275\u0275element"](6,"app-currency",58),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](7,"div",83),o["\u0275\u0275text"](8),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](9,"div",83),o["\u0275\u0275text"](10),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("matTooltip",e.name),o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" ",e.name," "),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("currencyList",e.currency_value),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("matTooltip",e.FTE_Count),o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" ",e.FTE_Count," "),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("matTooltip",e.shiftName),o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" ",e.shiftName," ")}}function ke(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div",74),o["\u0275\u0275elementStart"](1,"div",75),o["\u0275\u0275elementStart"](2,"span",76),o["\u0275\u0275text"](3," Line of Business "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](4,Ee,3,1,"button",77),o["\u0275\u0275elementStart"](5,"mat-menu",null,7),o["\u0275\u0275elementStart"](7,"button",8),o["\u0275\u0275listener"]("click",(function(){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"]().viewLineofBusiness()})),o["\u0275\u0275elementStart"](8,"mat-icon",6),o["\u0275\u0275text"](9,"business_center"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](10,"span"),o["\u0275\u0275text"](11,"View heirarchy"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](12,"div",15),o["\u0275\u0275elementStart"](13,"div",16),o["\u0275\u0275elementStart"](14,"div",17),o["\u0275\u0275elementStart"](15,"div",9),o["\u0275\u0275element"](16,"div",66),o["\u0275\u0275elementStart"](17,"div",78),o["\u0275\u0275text"](18," Product Name "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](19,"div",79),o["\u0275\u0275text"](20," Value "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](21,"div",79),o["\u0275\u0275text"](22," FTE Count "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](23,"div",79),o["\u0275\u0275text"](24," Shift "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](25,Ie,11,7,"div",72),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](4),o["\u0275\u0275property"]("ngIf",e.viewScopeAccess),o["\u0275\u0275advance"](21),o["\u0275\u0275property"]("ngForOf",e.productCategory)}}let Oe=(()=>{class e{constructor(e,t,i,a,o,l,r,s,p,u){this.route=e,this.leadsService=t,this._snackBar=i,this.inlineEditPopupService=a,this.sharedLazyLoadedComponentsService=o,this.UtilityService=l,this.dialog=r,this._mailUtilityService=s,this._loginService=p,this._graphService=u,this.showCreateNote=0,this.notes_bg_color=["#fbbc04","#e6c9a8","#ccff90","#F8EFBA","#e2e2e2","#f4ffa4","#fffa65","#ffb8b8"],this.currentNoteColor="white",this.currentNoteTitle="",this.noteMode="create",this.valueType="html",this.inlineEditField="",this.editType="",this.secondary_industries_names=[],this.productCategory=[],this.module_list=[],this.column_config=[],this.view_heirarchy_details={},this.line_of_business_access=this.leadsService.displayLineOfBusinessAndModulesInCreateLeads(),this.viewScopeAccess=!1,this.shift_list=[],this.contactsListForMailModal=[],this.mailAccessForLeads=!1,this.currentUser=this._loginService.getProfile().profile,this.initMailModal=()=>{this._mailUtilityService.mUtilityData.applicationId=35,this._mailUtilityService.mUtilityData.recipientMailIdArr=c.pluck(this.contactsListForMailModal,"email"),this._mailUtilityService.mUtilityData.currentUserMailId=this.currentUser.email,this._mailUtilityService.mailUiData.isSyncWithKebsBtn=!0},this.saveNote=()=>{""!=this.currentNote&&""!=this.currentNoteTitle?this.leadsService.createNotes(this.leadsId,this.currentNote,this.currentNoteTitle,this.currentNoteColor).subscribe(e=>Object(d.c)(this,void 0,void 0,(function*(){console.log(e),this.notes=yield this.getNotes(),this._snackBar.open(e,"Dismiss",{duration:2e3}),console.log(this.notes),this.showCreateNotefunction()})),e=>{console.error(e)}):this._snackBar.open("Fields cannot be empty","close",{duration:2e3})},this.setNoteColor=e=>{console.log(e),this.currentNoteColor=e},this.updateNote=()=>{console.log("update"),""!=this.currentNote&&""!=this.currentNoteTitle?this.leadsService.editNotes(this.currentEditingNoteId,this.leadsId,this.currentNote,this.currentNoteTitle,this.currentNoteColor).subscribe(e=>Object(d.c)(this,void 0,void 0,(function*(){console.log(e),this.showCreateNote=!this.showCreateNote,this._snackBar.open(e,"Dismiss",{duration:2e3}),this.notes=yield this.getNotes(),this.currentEditingNoteId="*",console.log(this.notes)})),e=>{this._snackBar.open(e,"Dismiss",{duration:2e3}),console.error(e)}):this._snackBar.open("Fields cannot be empty","Dismiss",{duration:2e3})},this.editNote=(e,t,n,i)=>{console.log(e),this.currentEditingNoteId=e,this.showCreateNote=!this.showCreateNote,this.currentNoteColor=t,this.currentNoteTitle=i,this.currentNote=n,this.noteMode="edit"},this.getNotes=()=>new Promise((e,t)=>{this.leadsService.retrieveNotes(this.leadsId).subscribe(t=>{e(t)},e=>{t(e)})}),this.convertToLocalTime=e=>{let t=new Date(e),n=6e4*t.getTimezoneOffset();return t.setTime(t.getTime()-n),t},this.addTask=()=>Object(d.c)(this,void 0,void 0,(function*(){this.dialog.open(m.a,{height:"80%",width:"80%",data:{tabbed_content_type:8,customer_id:this.leadDetails.in_account,mode:"Create",application_reference_id:this.leadsId,application_id:33}}).afterClosed().subscribe(e=>Object(d.c)(this,void 0,void 0,(function*(){this.notes=yield this.getNotes(),console.log("note res",e),"update required"==e&&this.ngOnInit()})))})),this.openMailModal=()=>Object(d.c)(this,void 0,void 0,(function*(){this.initMailModal(),this._mailUtilityService.mUtilityData.o365Token={token:yield this._graphService.getO365Token()},this._mailUtilityService.mUtilityData.getMailFromKebsApiData={url:"/api/general/getMailListBasedOnApplication",jwtToken:this._loginService.getJwtToken(),paramsArr:[{applicationId:34,objectIdArr:c.pluck(this.contactsListForMailModal,"id")},{applicationId:35,objectIdArr:[parseInt(this.leadsId)]}]},this._mailUtilityService.mUtilityData.saveMailInKebsApiData={url:"/api/general/saveMailItemWithObjectId",jwtToken:this._loginService.getJwtToken(),paramsArr:[{applicationId:34,objectIdArr:c.pluck(this.contactsListForMailModal,"id")},{applicationId:35,objectIdArr:[parseInt(this.leadsId)]}]};const{ViewMailComponent:e}=yield Promise.resolve().then(n.bind(null,"vFIH"));this.dialog.open(e,{width:"96%",height:"97%",maxWidth:"100vw",maxHeight:"100vw",data:{},disableClose:!0}).afterClosed().subscribe(e=>{this._mailUtilityService.resetMailData()})}))}ngOnInit(){return Object(d.c)(this,void 0,void 0,(function*(){this.module_list=[],this.secondary_industries_names=[],this.route.parent.params.subscribe(e=>Object(d.c)(this,void 0,void 0,(function*(){this.leadsId=e.LeadId,this.elementId=parseInt(this.leadsId),this.notes=yield this.getNotes(),console.log(this.notes),console.log(this.leadsId)}))),this.leadsService.getFormFieldCollection().subscribe(e=>{this.formFieldData=e},e=>{console.log(e)}),yield this.leadsService.getLeadsCardDetails(this.leadsId).then(e=>{this.leadDetails=e,this.tempLeadDetails=this.leadDetails,this.attendee=e.lead_owner_oid,this.productCategory=null!=this.leadDetails.product_category_value?JSON.parse(this.leadDetails.product_category_value):[],console.log(this.leadDetails),console.log("attende",this.attendee)}),this.viewScopeAccess=this.leadsService.viewLineofBusinessAccess(),yield this.leadsService.getLeadSource().then(e=>{this.leadSourceMaster=e},e=>{console.log(e)}),yield this.leadsService.getLeadScore().then(e=>{this.leadScoreMaster=e},e=>{console.log(e)}),yield this.leadsService.getProductCode().then(e=>{console.log("requirementlist",e),this.requirementList=e},e=>{console.log(e)}),this.leadsService.leadsChannelMaster().subscribe(e=>{this.leadsChannelList=e},e=>{console.log(e)}),yield this.leadsService.getProductViewHierarchyLabel(35,"Leads").then(e=>{"S"==e.messType&&(this.column_config=e.result,this.view_heirarchy_details=e.result_details)},e=>{console.log(e)}),yield this.leadsService.getModule().then(e=>{this.module_category_list=e},e=>{console.log(e)}),yield this.leadsService.getShiftList().then(e=>{this.shift_list=e},e=>{console.log(e)}),yield this.productCategoryList(),yield this.moduleList(),yield this.getSecondaryRequirements(),this.initMailModal()}))}deleteNote(e){this.leadsService.deleteNote(e.note_id,this.leadsId).subscribe(e=>Object(d.c)(this,void 0,void 0,(function*(){console.log(e),this._snackBar.open("Note deleted!","close",{duration:2e3}),this.notes=yield this.getNotes()})),e=>{console.error(e)})}getLength(e){return null!=e?e.length:0}notesValueChange(e){this.currentNote=e}getCurrentNoteTitle(e){this.currentNoteTitle=e}showCreateNotefunction(){this.currentNoteColor="white",this.currentNoteTitle="",this.currentNote="",this.showCreateNote=!this.showCreateNote,this.noteMode="create"}activateInlineEdit(e,t,n,i){this.dataArray=n,this.inlineEditActiveRow=i,(!this.inlineEditPopupService.inlineEditCallbackSubscription||this.inlineEditPopupService.inlineEditCallbackSubscription&&this.inlineEditPopupService.inlineEditCallbackSubscription.closed)&&(this.inlineEditPopupService.inlineEditCallbackSubscription=this.inlineEditPopupService.inlineEditCallback.subscribe(e=>{!e||0===Object.keys(e).length&&e.constructor===Object||this.inlineEditResponseFunction(e)})),this.inlineEditField=e,this.editType=t,"minimal-dropdown"==t?("Lead source"==this.inlineEditField||"Lead score"==this.inlineEditField||"Lead channel"==this.inlineEditField)&&(this.dropdownConfig={apiDataUpdateKeyName:"id",apiDataSelectedKeyName:"name"}):"search-dropdown"==t?this.dropdownConfig={inlineEditField:e,dropdownSelectedValue:null,apiServiceVariable:this.sharedLazyLoadedComponentsService,apiFunctionName:"searchConsultants",apiDataUpdateKeyName:"oid",apiDataSelectedKeyName:"name",hasImageView:!0,apiDataImageKeyName:"oid"}:"date-picker"==this.editType?"Closure date"==this.inlineEditField&&(this.dropdownConfig={inlineEditField:this.inlineEditField,dropdownSelectedValue:this.inlineEditActiveRow.leads_closure_date,apiDataSelectedKeyName:this.inlineEditField}):"simple-text"==this.editType&&("Lead name"==this.inlineEditField?this.dropdownConfig={inlineEditField:this.inlineEditField,dropdownSelectedValue:this.inlineEditActiveRow.lead_name,apiDataSelectedKeyName:this.inlineEditField,inputType:"text"}:"Name"==this.inlineEditField?this.dropdownConfig={inlineEditField:this.inlineEditField,dropdownSelectedValue:this.inlineEditActiveRow.first_name,apiDataSelectedKeyName:this.inlineEditField,inputType:"text"}:"Email"==this.inlineEditField?this.dropdownConfig={inlineEditField:this.inlineEditField,dropdownSelectedValue:this.inlineEditActiveRow.email,apiDataSelectedKeyName:this.inlineEditField,inputType:"text"}:"Telephone"==this.inlineEditField?this.dropdownConfig={inlineEditField:this.inlineEditField,dropdownSelectedValue:this.inlineEditActiveRow.telephone,apiDataSelectedKeyName:this.inlineEditField,inputType:"number"}:"Phone"==this.inlineEditField?this.dropdownConfig={inlineEditField:this.inlineEditField,dropdownSelectedValue:this.inlineEditActiveRow.mobile_number,apiDataSelectedKeyName:this.inlineEditField,inputType:"number"}:"Job title"==this.inlineEditField&&(this.dropdownConfig={inlineEditField:this.inlineEditField,dropdownSelectedValue:this.inlineEditActiveRow.job_title,apiDataSelectedKeyName:this.inlineEditField,inputType:"text"})),this.inlineEditPopupService.setInlineEditActiveDataSubject({editType:this.editType,dataArray:this.dataArray,dropdownConfig:this.dropdownConfig})}inlineEditResponseFunction(e){if("Lead source"==this.inlineEditField)e[this.dropdownConfig.apiDataSelectedKeyName]&&this.leadDetails.lead_source_id!=e[this.dropdownConfig.apiDataUpdateKeyName]&&this.leadsService.updateLeadSource(this.leadDetails.lead_id,e[this.dropdownConfig.apiDataUpdateKeyName]).subscribe(t=>Object(d.c)(this,void 0,void 0,(function*(){this.tempLeadDetails.lead_source_id=e[this.dropdownConfig.apiDataUpdateKeyName],this.tempLeadDetails.lead_source_name=e[this.dropdownConfig.apiDataSelectedKeyName],this.UtilityService.showMessage("Lead Source Updated Successfully","Dismiss",3e3)})),e=>{this.UtilityService.showMessage(e,"dismiss",3e3)});else if("Lead score"==this.inlineEditField)e[this.dropdownConfig.apiDataSelectedKeyName]&&this.leadDetails.lead_source_id!=e[this.dropdownConfig.apiDataUpdateKeyName]&&this.leadsService.updateLeadScore(this.leadDetails.lead_id,e[this.dropdownConfig.apiDataUpdateKeyName]).subscribe(t=>Object(d.c)(this,void 0,void 0,(function*(){this.tempLeadDetails.lead_score_id=e[this.dropdownConfig.apiDataUpdateKeyName],this.tempLeadDetails.leads_score=e[this.dropdownConfig.apiDataSelectedKeyName],this.UtilityService.showMessage("Lead Score Updated Successfully","Dismiss",3e3)})),e=>{this.UtilityService.showMessage(e,"dismiss",3e3)});else if("Lead channel"==this.inlineEditField)e[this.dropdownConfig.apiDataSelectedKeyName]&&this.leadDetails.lead_source_id!=e[this.dropdownConfig.apiDataUpdateKeyName]&&this.leadsService.updateLeadChannel(this.leadDetails.lead_id,e[this.dropdownConfig.apiDataUpdateKeyName]).subscribe(t=>Object(d.c)(this,void 0,void 0,(function*(){"F"!=t?(this.tempLeadDetails.channel_id=e[this.dropdownConfig.apiDataUpdateKeyName],this.tempLeadDetails.channel_name=e[this.dropdownConfig.apiDataSelectedKeyName],this.UtilityService.showMessage("Lead channel Updated Successfully","Dismiss",3e3)):this.UtilityService.showMessage("Error in updating Lead channel","dismiss",3e3)})),e=>{this.UtilityService.showMessage("Error in updating Lead channel","dismiss",3e3)});else if("Closure date"==this.inlineEditField){let t=this.convertToLocalTime(e[this.dropdownConfig.apiDataSelectedKeyName]);e[this.dropdownConfig.apiDataSelectedKeyName]&&this.leadDetails.leads_closure_date!=t&&this.leadsService.updateLeadsClosureDate(this.leadDetails.lead_id,t).subscribe(t=>Object(d.c)(this,void 0,void 0,(function*(){this.tempLeadDetails.leads_closure_date=e[this.dropdownConfig.apiDataSelectedKeyName],this.UtilityService.showMessage("Closure Date Updated Successfully","Dismiss",3e3)})),e=>{this.UtilityService.showMessage(e,"dismiss",3e3)})}else"Lead name"==this.inlineEditField?this.inlineEditActiveRow.lead_name!=e[this.dropdownConfig.apiDataSelectedKeyName]&&this.leadsService.updateLeadName(this.leadDetails.lead_id,e[this.dropdownConfig.apiDataSelectedKeyName]).subscribe(t=>Object(d.c)(this,void 0,void 0,(function*(){this.tempLeadDetails.lead_name=e[this.dropdownConfig.apiDataSelectedKeyName],this.UtilityService.showMessage("Lead Name Updated Successfully","Dismiss",3e3)})),e=>{this.UtilityService.showMessage(e,"dismiss",3e3)}):"Name"==this.inlineEditField?this.inlineEditActiveRow.first_name!=e[this.dropdownConfig.apiDataSelectedKeyName]&&this.leadsService.updateLeadContactDetail(this.tempLeadDetails.contact_id,"first name",e[this.dropdownConfig.apiDataSelectedKeyName]).subscribe(t=>Object(d.c)(this,void 0,void 0,(function*(){this.tempLeadDetails.first_name=e[this.dropdownConfig.apiDataSelectedKeyName],this.UtilityService.showMessage("Name Updated Successfully","Dismiss",3e3)})),e=>{this.UtilityService.showMessage(e,"dismiss",3e3)}):"Email"==this.inlineEditField?this.inlineEditActiveRow.first_name!=e[this.dropdownConfig.apiDataSelectedKeyName]&&this.leadsService.updateLeadContactDetail(this.tempLeadDetails.contact_id,"email",e[this.dropdownConfig.apiDataSelectedKeyName]).subscribe(t=>Object(d.c)(this,void 0,void 0,(function*(){this.tempLeadDetails.email=e[this.dropdownConfig.apiDataSelectedKeyName],this.UtilityService.showMessage("Email Updated Successfully","Dismiss",3e3)})),e=>{this.UtilityService.showMessage(e,"dismiss",3e3)}):"Phone"==this.inlineEditField?this.inlineEditActiveRow.first_name!=e[this.dropdownConfig.apiDataSelectedKeyName]&&this.leadsService.updateLeadContactDetail(this.tempLeadDetails.contact_id,"mobile number",e[this.dropdownConfig.apiDataSelectedKeyName]).subscribe(t=>Object(d.c)(this,void 0,void 0,(function*(){this.tempLeadDetails.mobile_number=e[this.dropdownConfig.apiDataSelectedKeyName],this.UtilityService.showMessage("Phone Updated Successfully","Dismiss",3e3)})),e=>{this.UtilityService.showMessage(e,"dismiss",3e3)}):"Lead owner"==this.inlineEditField?e[this.dropdownConfig.apiDataSelectedKeyName]&&this.tempLeadDetails.lead_owner_oid!=e[this.dropdownConfig.apiDataUpdateKeyName]&&this.leadsService.updateLeadOwner(this.leadDetails.lead_id,e[this.dropdownConfig.apiDataUpdateKeyName]).subscribe(t=>Object(d.c)(this,void 0,void 0,(function*(){this.tempLeadDetails.lead_owner_oid=e[this.dropdownConfig.apiDataUpdateKeyName],this.UtilityService.showMessage("Lead Owner Updated Successfully","Dismiss",3e3)})),e=>{this.UtilityService.showMessage(e,"dismiss",3e3)}):"Sales owner"==this.inlineEditField?e[this.dropdownConfig.apiDataSelectedKeyName]&&this.tempLeadDetails.sales_owner_oid!=e[this.dropdownConfig.apiDataUpdateKeyName]&&this.leadsService.updateLeadSalesOwner(this.leadDetails.lead_id,e[this.dropdownConfig.apiDataUpdateKeyName]).subscribe(t=>Object(d.c)(this,void 0,void 0,(function*(){this.tempLeadDetails.sales_owner_oid=e[this.dropdownConfig.apiDataUpdateKeyName],this.UtilityService.showMessage("Sales Owner Updated Successfully","Dismiss",3e3)})),e=>{this.UtilityService.showMessage(e,"dismiss",3e3)}):"Marketing owner"==this.inlineEditField?e[this.dropdownConfig.apiDataSelectedKeyName]&&this.tempLeadDetails.sales_owner_oid!=e[this.dropdownConfig.apiDataUpdateKeyName]&&this.leadsService.updateLeadMarketingOwner(this.leadDetails.lead_id,e[this.dropdownConfig.apiDataUpdateKeyName]).subscribe(t=>Object(d.c)(this,void 0,void 0,(function*(){"F"!=t?(this.tempLeadDetails.marketing_owner_oid=e[this.dropdownConfig.apiDataUpdateKeyName],this.UtilityService.showMessage("Marketing Owner Updated Successfully","Dismiss",3e3)):this.UtilityService.showMessage("Cannot update Marketing Owner","dismiss",3e3)})),e=>{this.UtilityService.showMessage(e,"dismiss",3e3)}):"Job title"==this.inlineEditField&&this.inlineEditActiveRow.job_title!=e[this.dropdownConfig.apiDataSelectedKeyName]&&this.leadsService.updateLeadContactDetail(this.tempLeadDetails.contact_id,"jobTitle",e[this.dropdownConfig.apiDataSelectedKeyName]).subscribe(t=>Object(d.c)(this,void 0,void 0,(function*(){this.tempLeadDetails.job_title=e[this.dropdownConfig.apiDataSelectedKeyName],this.UtilityService.showMessage("Job title Updated Successfully","Dismiss",3e3)})),e=>{this.UtilityService.showMessage(e,"dismiss",3e3)})}getSecondaryRequirements(){if(null!=this.leadDetails&&null!=this.leadDetails&&null!=this.leadDetails.secondary_requirements)for(let e of JSON.parse(this.leadDetails.secondary_requirements)){let t=c.findWhere(this.requirementList,{id:e});this.secondary_industries_names.push(t.name)}}moduleList(){if(null!=this.leadDetails&&null!=this.leadDetails&&null!=this.leadDetails.modules)for(let e of JSON.parse(this.leadDetails.modules)){let t=c.findWhere(this.module_category_list,{id:e});this.module_list.push(t.name)}}productCategoryList(){if(null!=this.leadDetails&&null!=this.leadDetails){let e=null!=this.leadDetails.product_category_value?JSON.parse(this.leadDetails.product_category_value):[];if(null!=this.shift_list)for(let t of e){let e=c.findWhere(this.shift_list,{id:t.shift});t.shiftName=null!=e?e.name:""}this.productCategory=e}}viewLineofBusiness(){return Object(d.c)(this,void 0,void 0,(function*(){let e=[];yield this.leadsService.getProductHeirarchy().then(t=>{e=t},e=>{console.log(e)});const{HeirarchyViewComponent:t}=yield n.e(986).then(n.bind(null,"zS0E"));this.dialog.open(t,{height:"100%",width:"85%",position:{right:"0px"},data:{heading:"Line of Business - "+this.leadDetails.lead_name+" ("+this.leadsId+")",heirarchy_data:e,column_config:this.column_config,application_id:this.view_heirarchy_details.application_id,application_object:this.view_heirarchy_details.application_object,patchData:""!=this.leadDetails.product_category_value&&null!=this.leadDetails.product_category_value?JSON.parse(this.leadDetails.product_category_value):[],returnAsArray:!1,access:"edit"}}).afterClosed().subscribe(e=>Object(d.c)(this,void 0,void 0,(function*(){null!=e&&(console.log(e),"S"==e.messType&&this.leadsService.updateProductCategory(e.data,this.leadsId).then(e=>{"S"==e.messType&&(this.ngOnInit(),this._snackBar.open("Updated Successfully!","Dismiss",{duration:3e3}))},e=>{this._snackBar.open("Unable to update Product Category!","Dismiss",{duration:3e3})}))})))}))}editLead(){return Object(d.c)(this,void 0,void 0,(function*(){const{CreateLeadComponent:e}=yield Promise.all([n.e(8),n.e(45),n.e(54),n.e(62),n.e(80),n.e(79),n.e(135),n.e(336)]).then(n.bind(null,"9oms"));this.dialog.open(e,{height:"100%",width:"80%",position:{right:"0px"},data:{checked:this.checked,leadDetail:this.leadDetails,mode:"Edit"},disableClose:!0}).afterClosed().subscribe(e=>{"update required"==e&&(this.UtilityService.showMessage("Lead updated successfully!","Dismiss",3e3),this.ngOnInit())})}))}navigateToAccounts(){let e=window.location.origin+"/main/accounts/"+this.leadDetails.in_account+"/"+this.UtilityService.encodeURIComponent(this.leadDetails.account_name)+"/overview";window.open(e)}openMeetingModal(){console.log("attendee",[this.attendee]),this.dialog.open(p.MeetingInviteComponent,{width:"6000px",maxHeight:"90vh",autoFocus:!1,data:{modalParams:{application_id:34,primary_unique_id:this.leadsId,secondary_unique_id:null,attendees:[this.attendee],cost_centre_object:[],is_from_header_creation:!1,meeting_meta_data:{lead_id:this.leadsId}}}})}createCallLog(){return Object(d.c)(this,void 0,void 0,(function*(){this.dialog.open(m.a,{height:"80%",width:"80%",data:{tabbed_content_type:2,customer_id:this.leadDetails.in_account,mode:"Create",application_reference_id:this.leadsId,application_id:35}}).afterClosed().subscribe(e=>{console.log(e)})}))}createMeeting(){return Object(d.c)(this,void 0,void 0,(function*(){this.dialog.open(m.a,{height:"80%",width:"80%",data:{tabbed_content_type:1,customer_id:this.leadDetails.in_account,mode:"Create",application_reference_id:this.leadsId,application_id:35}}).afterClosed().subscribe(e=>{console.log(e)})}))}createMail(){return Object(d.c)(this,void 0,void 0,(function*(){this.dialog.open(m.a,{height:"80%",width:"80%",data:{tabbed_content_type:3,customer_id:this.leadDetails.in_account,mode:"Create",application_reference_id:this.leadsId,application_id:35}}).afterClosed().subscribe(e=>{console.log(e)})}))}createNewTask(){return Object(d.c)(this,void 0,void 0,(function*(){this.dialog.open(m.a,{height:"80%",width:"80%",data:{tabbed_content_type:4,customer_id:this.leadDetails.in_account,mode:"Create",application_reference_id:this.leadsId,application_id:35}}).afterClosed().subscribe(e=>{console.log(e)})}))}addNotes(){this.dialog.open(m.a,{height:"80%",width:"80%",data:{tabbed_content_type:8,customer_id:this.leadDetails.in_account,mode:"Create",application_reference_id:this.leadsId,application_id:35}}).afterClosed().subscribe(e=>{this.CrmNotesComponent.ngOnInit()})}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](a.a),o["\u0275\u0275directiveInject"](u.a),o["\u0275\u0275directiveInject"](h.a),o["\u0275\u0275directiveInject"](v.a),o["\u0275\u0275directiveInject"](g.a),o["\u0275\u0275directiveInject"](f.a),o["\u0275\u0275directiveInject"](y.b),o["\u0275\u0275directiveInject"](x.MailUtilityService),o["\u0275\u0275directiveInject"](S.a),o["\u0275\u0275directiveInject"](b.a))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-leads-overview"]],viewQuery:function(e,t){if(1&e&&o["\u0275\u0275viewQuery"](J,!0),2&e){let e;o["\u0275\u0275queryRefresh"](e=o["\u0275\u0275loadQuery"]())&&(t.CrmNotesComponent=e.first)}},decls:39,vars:6,consts:[[1,"container-fluid","leads-overview-styles","pt-2"],[1,"row","mt-2","pl-2","pb-2"],[1,"col-5","pl-2"],[1,"tileName"],[1,"col-7","pl-3","d-flex"],["mat-icon-button","","matTooltip","View more",1,"more-button","ml-auto",3,"matMenuTriggerFor"],[1,"smallCardIcon"],["options","matMenu"],["mat-menu-item","",1,"drop-btn",3,"click"],[1,"row"],[1,"col-5","pr-0"],[1,"col-12","pl-0"],[4,"ngIf"],[1,"col-7","pl-2"],[1,"row","pt-2","pb-2"],[1,"col-12"],[1,"card","slide-in-top"],[1,"card-body","p-3"],["class","row pb-2",4,"ngIf"],["class","row pb-2","style","padding-top: 20px;",4,"ngIf"],["appId","35",3,"elementId"],["noteComponent",""],[1,"card",2,"min-height","510px !important",3,"ngClass"],[1,"card-body","pt-3","pl-2","pr-2","pb-2"],[1,"row","pt-2","pb-1"],[1,"card","d-flex","slide-in-top"],[1,"card-body","p-2"],[1,"col-3",2,"border-right","1px solid #d6d6d6"],[1,"title","mx-auto","pt-2"],[1,"sub-heading","pb-2","pt-1"],[1,"col-3"],[1,"row","pl-3","pt-3","slide-from-down"],[1,"card","leads-overview-card-bg","slide-in-top"],[1,"col-12","pl-4"],["interActiveInlineEdit","",1,"row","leads-name",2,"cursor","pointer",3,"matTooltip","click"],["class","row pl-0 pt-1",4,"ngIf"],[1,"pl-0","pt-2","d-flex","flex-row-reverse","justify-content-start"],["mat-mini-fab","","style","background-color: #cf0001;color: #FAFAFA; margin-right: 60px;","matTooltip","Open mail",3,"ngStyle",4,"ngIf"],["mat-mini-fab","","mat-button","","matTooltip","Create Task","matRipple","",2,"background-color","#cf0001","color","#FAFAFA",3,"ngStyle","click"],["mat-mini-fab","","matTooltip","Schedule meeting",2,"background-color","#cf0001","color","#FAFAFA",3,"ngStyle","click"],["mat-mini-fab","","matTooltip","Add Note",2,"background-color","#cf0001","color","#FAFAFA",3,"ngStyle","click"],["mat-mini-fab","","mat-button","","matTooltip","Add Logs","matRipple","",2,"background-color","#cf0001","color","#FAFAFA",3,"matMenuTriggerFor","ngStyle"],["menu","matMenu"],["mat-menu-item","",3,"click"],[1,"row","pt-3"],[1,"col-12","leads-sub-headings"],["class","row pl-3 pt-2 slide-from-down",4,"ngIf"],["class","row pl-3 pt-2 slide-from-down",4,"ngFor","ngForOf"],[1,"row","pt-2",2,"border-bottom","#d6d6d6 1px solid"],["class","row pl-3 pt-2 slide-from-down",4,"ngif"],[1,"row","pl-3","pt-2","slide-from-down"],[1,"col-1"],[1,"leads-info-icons"],[1,"col-6","leads-info-title"],[1,"col-5","leads-info-details"],[1,"row","pl-0","pt-1"],[1,"col-6","leads-info-title","pl-0"],[1,"col-6","pl-0","leads-info-title"],["type","small",1,"flex-1",3,"currencyList"],[1,"col-6","pl-0","leads-info-in-account",2,"cursor","pointer",3,"click"],["interActiveInlineEdit","",1,"col-6","pl-0","leads-info-details",2,"cursor","pointer",3,"click"],[1,"col-6","pl-0","leads-info-details",2,"cursor","pointer"],["mat-mini-fab","","matTooltip","Open mail",2,"background-color","#cf0001","color","#FAFAFA","margin-right","60px",3,"ngStyle"],[1,"col-5","leads-info-details",3,"matTooltip"],["interActiveInlineEdit","",1,"col-5","leads-info-details",2,"cursor","pointer",3,"click"],["type","name",3,"oid"],[1,"col-1","pr-0"],[1,"status-circular-2"],[1,"col-11","pl-0","lead-info-details"],[1,"row","pb-2"],[1,"col-12","pl-3","pr-3"],[1,"tileName","pr-2"],["class","row",4,"ngFor","ngForOf"],[1,"status-circular"],[1,"row","pb-2",2,"padding-top","20px"],[1,"col-12","d-flex"],[1,"tileName","pl-1"],["mat-icon-button","","matTooltip","View more","class","more-button ml-auto",3,"matMenuTriggerFor",4,"ngIf"],[1,"col-3","pl-2","lead-info-title"],[1,"col-2","pl-2","lead-info-title"],[1,"status-circular-1"],[1,"col-3","pl-2","lead-info-details",2,"font-weight","500","color","#cf0001",3,"matTooltip"],[1,"col-2","pl-2","lead-info-details"],[1,"col-2","pl-2","lead-info-details",3,"matTooltip"]],template:function(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",0),o["\u0275\u0275elementStart"](1,"div",1),o["\u0275\u0275elementStart"](2,"div",2),o["\u0275\u0275elementStart"](3,"span",3),o["\u0275\u0275text"](4," Leads Info "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](5,"div",4),o["\u0275\u0275elementStart"](6,"span",3),o["\u0275\u0275text"](7," Requirement "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](8,"button",5),o["\u0275\u0275elementStart"](9,"mat-icon",6),o["\u0275\u0275text"](10,"more_vert"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](11,"mat-menu",null,7),o["\u0275\u0275elementStart"](13,"button",8),o["\u0275\u0275listener"]("click",(function(){return t.editLead()})),o["\u0275\u0275elementStart"](14,"mat-icon",6),o["\u0275\u0275text"](15,"edit"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](16,"span"),o["\u0275\u0275text"](17,"Edit Lead"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](18,"button",8),o["\u0275\u0275listener"]("click",(function(){return t.viewLineofBusiness()})),o["\u0275\u0275elementStart"](19,"mat-icon",6),o["\u0275\u0275text"](20,"business_center"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](21,"span"),o["\u0275\u0275text"](22,"View heirarchy"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](23,"div",9),o["\u0275\u0275elementStart"](24,"div",10),o["\u0275\u0275elementStart"](25,"div",9),o["\u0275\u0275elementStart"](26,"div",11),o["\u0275\u0275template"](27,Se,137,118,"ng-container",12),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](28,"div",13),o["\u0275\u0275elementStart"](29,"div",14),o["\u0275\u0275elementStart"](30,"div",15),o["\u0275\u0275elementStart"](31,"div",16),o["\u0275\u0275elementStart"](32,"div",17),o["\u0275\u0275template"](33,be,6,1,"ng-container",12),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](34,we,8,1,"div",18),o["\u0275\u0275template"](35,ke,26,2,"div",19),o["\u0275\u0275elementStart"](36,"div"),o["\u0275\u0275element"](37,"crm-notes",20,21),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275reference"](12);o["\u0275\u0275advance"](8),o["\u0275\u0275property"]("matMenuTriggerFor",e),o["\u0275\u0275advance"](19),o["\u0275\u0275property"]("ngIf",t.leadDetails),o["\u0275\u0275advance"](6),o["\u0275\u0275property"]("ngIf",t.leadDetails),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",0!=t.module_list.length),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",0!=t.productCategory.length&&t.line_of_business_access),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("elementId",t.elementId)}},directives:[_.a,C.a,w.f,E.a,w.g,w.d,i.NgIf,I.a,i.NgClass,k.a,i.NgStyle,i.NgForOf,F,Y],pipes:[W.a,X],styles:[".leads-overview-styles[_ngcontent-%COMP%]   .leads-info-icons[_ngcontent-%COMP%]{color:#5a5957!important;font-size:21px!important}.leads-overview-styles[_ngcontent-%COMP%]   .leads-overview-card-bg[_ngcontent-%COMP%]{background-color:rgba(251,192,192,.*****************)!important;box-shadow:0 3px 6px hsla(0,0%,41.2%,.19),0 1px 4px hsla(0,0%,41.2%,.19)}.leads-overview-styles[_ngcontent-%COMP%]   .leads-name[_ngcontent-%COMP%]{color:#cf0001;font-size:16px;font-weight:500;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.leads-overview-styles[_ngcontent-%COMP%]   .leads-info-title[_ngcontent-%COMP%]{color:#7b7b7a;font-size:14px}.leads-overview-styles[_ngcontent-%COMP%]   .leads-sub-headings[_ngcontent-%COMP%]{color:#cf0001;font-size:14px;font-weight:500}.leads-overview-styles[_ngcontent-%COMP%]   .leads-info-details[_ngcontent-%COMP%]{color:#1a1a1a;font-weight:400;font-size:14px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.leads-overview-styles[_ngcontent-%COMP%]   .leads-info-in-account[_ngcontent-%COMP%]{color:#cf0001;font-weight:700;font-size:14px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.leads-overview-styles[_ngcontent-%COMP%]   .tileName[_ngcontent-%COMP%]{color:#9a9a9a;font-size:14px!important}.leads-overview-styles[_ngcontent-%COMP%]   .leads-overview-card-bg[_ngcontent-%COMP%]{background-color:rgba(255,109,109,.*****************)!important;box-shadow:0 2px 4px hsla(0,0%,41.2%,.19),0 1px 4px hsla(0,0%,41.2%,.19)}.leads-overview-styles[_ngcontent-%COMP%]   .more-button[_ngcontent-%COMP%]{width:27px!important;height:27px!important;line-height:25px!important}.leads-overview-styles[_ngcontent-%COMP%]   .smallCardIcon[_ngcontent-%COMP%]{font-size:18px!important;color:#868683!important}.leads-overview-styles[_ngcontent-%COMP%]   .drop-btn[_ngcontent-%COMP%]{font-size:13px!important;height:38px!important;line-height:2!important;padding:0 13px!important}.leads-overview-styles[_ngcontent-%COMP%]   .status-circular[_ngcontent-%COMP%]{background-color:#ee8686}.leads-overview-styles[_ngcontent-%COMP%]   .status-circular[_ngcontent-%COMP%], .leads-overview-styles[_ngcontent-%COMP%]   .status-circular-1[_ngcontent-%COMP%]{height:13px;width:13px;margin-top:4px;border-radius:50%;box-shadow:0 2px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.leads-overview-styles[_ngcontent-%COMP%]   .status-circular-1[_ngcontent-%COMP%]{background-color:#9de6f0}.leads-overview-styles[_ngcontent-%COMP%]   .status-circular-2[_ngcontent-%COMP%]{height:13px;width:13px;margin-top:4px;border-radius:50%;background-color:#f32fc2;box-shadow:0 2px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.leads-overview-styles[_ngcontent-%COMP%]   .view-more[_ngcontent-%COMP%]{line-height:1;height:30px;width:100px;color:#5a5957}.leads-overview-styles[_ngcontent-%COMP%]   .lead-name[_ngcontent-%COMP%]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.leads-overview-styles[_ngcontent-%COMP%]   .lead-name[_ngcontent-%COMP%], .leads-overview-styles[_ngcontent-%COMP%]   .lead-sub-headings[_ngcontent-%COMP%]{color:#cf0001;font-size:14px;font-weight:500}.leads-overview-styles[_ngcontent-%COMP%]   .lead-info-title[_ngcontent-%COMP%]{color:#7b7b7a}.leads-overview-styles[_ngcontent-%COMP%]   .lead-info-details[_ngcontent-%COMP%], .leads-overview-styles[_ngcontent-%COMP%]   .notes-data[_ngcontent-%COMP%]{color:#1a1a1a;font-weight:400;font-size:14px}.leads-overview-styles[_ngcontent-%COMP%]   .lead-info-details[_ngcontent-%COMP%]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.leads-overview-styles[_ngcontent-%COMP%]   .lead-info-title[_ngcontent-%COMP%]{color:#1a1a1a;font-size:14px;font-weight:500}.leads-overview-styles[_ngcontent-%COMP%]   .lead-info-icons[_ngcontent-%COMP%]{color:#5a5957!important;font-size:18px!important}.leads-overview-styles[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{color:#252422!important;font-size:20px!important;text-align:center!important;font-weight:500!important}.leads-overview-styles[_ngcontent-%COMP%]   .sub-heading[_ngcontent-%COMP%]{color:#7b7b7a!important;font-size:14px!important;text-align:center!important;font-weight:400!important}.leads-overview-styles[_ngcontent-%COMP%]   .smallSubtleText[_ngcontent-%COMP%]{font-size:11px;color:#66615b;color:#9a9a9a!important}.leads-overview-styles[_ngcontent-%COMP%]   .dev-extreme-styles[_ngcontent-%COMP%]     .dx-htmleditor-toolbar-wrapper .dx-toolbar-items-container{height:48px!important}.leads-overview-styles[_ngcontent-%COMP%]   .dev-extreme-styles[_ngcontent-%COMP%]     .dx-htmleditor-toolbar-wrapper .dx-button-content .dx-icon{font-size:16px!important;color:#66615b!important}.leads-overview-styles[_ngcontent-%COMP%]   .colors[_ngcontent-%COMP%]{cursor:pointer;height:16px;width:16px;margin-top:4px;border-radius:50%}.leads-overview-styles[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}.leads-overview-styles[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}"]}),e})();var De=n("Kj3r"),Me=n("3Pt+"),Pe=n("DlXH"),Te=n("R0Ic"),Ae=n("RJSY"),Fe=n("/5jE"),Ne=n("bmw1"),Le=n("+FIJ"),je=n("kmnG"),Be=n("qFsG"),Ve=n("Xa2L"),Ue=n("pgif"),ze=n("Wp6s"),$e=n("vxfF"),qe=n("PSD3"),Re=n.n(qe),He=n("TC2u"),Ke=n("zcNR"),Ye=n("yArD"),We=n("/QRN");let Ge=(()=>{class e{constructor(){}ngOnInit(){}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-shimmer"]],decls:2,vars:5,consts:[[3,"speed","width","height","primaryColor","secondaryColor"],["ngx-rect","","width","1000","height","70","y","50","x","0","rx","5","ry","5"]],template:function(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"ngx-content-loading",0),o["\u0275\u0275namespaceSVG"](),o["\u0275\u0275element"](1,"g",1),o["\u0275\u0275elementEnd"]()),2&e&&o["\u0275\u0275property"]("speed","1500ms")("width",1e3)("height",100)("primaryColor","#ebebeb")("secondaryColor","#f7f7f7")},directives:[We.b,We.c],styles:[""]}),e})();function Xe(e,t){1&e&&o["\u0275\u0275element"](0,"app-shimmer")}const Je=function(e){return{"background-color":e}};function Qe(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"span",44),o["\u0275\u0275elementStart"](1,"button",45),o["\u0275\u0275elementStart"](2,"mat-icon"),o["\u0275\u0275text"](3),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("matTooltip",e.activityDetail.sales_activity_type)("ngStyle",o["\u0275\u0275pureFunction1"](3,Je,e.activityDetail?e.activityDetail.activity_type_bg:"black")),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate"](e.activityDetail.activity_mat_icon)}}function Ze(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"button",48),o["\u0275\u0275elementStart"](1,"mat-icon"),o["\u0275\u0275text"](2),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275property"]("matTooltip",e.activityDetail.activity_sub_type_name),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate"](e.activityDetail.sub_activity_mat_icon)}}function et(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"span",46),o["\u0275\u0275template"](1,Ze,3,2,"button",47),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",e.activityDetail.sub_activity_mat_icon)}}function tt(e,t){1&e&&o["\u0275\u0275element"](0,"app-shimmer")}function nt(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"span"),o["\u0275\u0275text"](1),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate"](e.activityDetail.title?e.activityDetail.title:"-")}}function it(e,t){1&e&&o["\u0275\u0275element"](0,"app-shimmer")}function at(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"span"),o["\u0275\u0275text"](1),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate"](e.activityDetail.activity_sub_type_name?e.activityDetail.activity_sub_type_name:"-")}}function ot(e,t){1&e&&o["\u0275\u0275element"](0,"app-shimmer")}function lt(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"span"),o["\u0275\u0275text"](1),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate"](e.activityDetail.governance_name?e.activityDetail.governance_name:"-")}}function rt(e,t){1&e&&o["\u0275\u0275element"](0,"app-shimmer")}function st(e,t){if(1&e&&o["\u0275\u0275element"](0,"app-user-profile",50),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275propertyInterpolate"]("oid",e.activityDetail.assigned_to)}}function dt(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"span"),o["\u0275\u0275template"](1,st,1,1,"app-user-profile",49),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",null!=e.activityDetail.assigned_to&&0!=e.activityDetail.assigned_to.length)}}function ct(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"app-shimmer",51),o["\u0275\u0275listener"]("click",(function(){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"]().navigateToLeadActivityDetailPage()})),o["\u0275\u0275elementEnd"]()}}function pt(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"span"),o["\u0275\u0275text"](1),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate"]("Invalid date"!=e.activityDetail.task_due_date?e.activityDetail.task_due_date:"-")}}function mt(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"app-shimmer",51),o["\u0275\u0275listener"]("click",(function(){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"]().navigateToLeadActivityDetailPage()})),o["\u0275\u0275elementEnd"]()}}function ut(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"span",52,53),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](e);const t=o["\u0275\u0275reference"](1);return o["\u0275\u0275nextContext"]().actualhoursTs(t)})),o["\u0275\u0275text"](2),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate1"]("",e.activityDetail.actual_hours?e.activityDetail.actual_hours:"0"," Hrs")}}function ht(e,t){1&e&&o["\u0275\u0275element"](0,"app-shimmer")}function vt(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"span",54),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](e);const t=o["\u0275\u0275nextContext"]();return t.activateInlineEdit("planned hours","simple-text",[],t.activityDetail.planned_hours)})),o["\u0275\u0275text"](1),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"]("",e.activityDetail.planned_hours?e.activityDetail.planned_hours:"0"," Hrs")}}const gt=function(e){return{color:e}};function ft(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"button",55),o["\u0275\u0275listener"]("click",(function(){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"]().completeActivity()})),o["\u0275\u0275elementStart"](1,"mat-icon"),o["\u0275\u0275text"](2,"check_circle"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275property"]("ngStyle",o["\u0275\u0275pureFunction1"](1,gt,3==e.activityDetail.activity_status_id?"#009432":1==e.activityDetail.activity_status_id?"#9A9A9A":"#ffa502"))}}const yt=function(e){return{"border-left-color":e}};let xt=(()=>{class e{constructor(e,t,i,a,l,r,s,p,m,u,h,v){this.leadService=e,this._route=t,this.router=i,this.toMainService=a,this._snackBar=l,this.dialog=r,this.inlineEditPopupService=s,this.tsInlineEntryPopupService=p,this.sharedLazyLoadedComponentsService=m,this.tsSubmissionPrimaryService=u,this.oppService=h,this.viewContainerRef=v,this.deactivateEvent=new o.EventEmitter,this.timesheetIds=[],this.getDetail=()=>new Promise((e,t)=>{this.leadService.activityDetails(this.activityNo).then(t=>{e(t[0])},e=>{console.error(e),t(e)})}),this.navigateToLeadActivityDetailPage=()=>{this.toMainService.sendMsg("giveMeDetailedView-"+this.scrollIndex)},this.getColor=()=>{},this.editActivity=(e,t)=>Object(d.c)(this,void 0,void 0,(function*(){let i;if(console.log(e),"Task"==e){i="editTask";const{NewTaskFormComponent:e}=yield Promise.all([n.e(54),n.e(0),n.e(759)]).then(n.bind(null,"0auA"));this.leadService.taskFormDetails(this.activityId).then(n=>{this.dialog.open(e,{height:"100%",width:"50%",position:{right:"0px"},data:{mode:"Edit",activityId:t,data:n[0]}}).afterClosed().subscribe(e=>{console.log(e),"update required"==e&&(this.activityDetail=this.getDetail())})})}else if("Meeting"==e){i="editMeeting";const{MeetingFormComponent:e}=yield Promise.all([n.e(54),n.e(0),n.e(758)]).then(n.bind(null,"eQOY"));this.leadService.meetingFormDetails(this.activityId).then(n=>{this.dialog.open(e,{height:"100%",width:"70%",position:{right:"0px"},data:{mode:"Edit",activityId:t,data:n[0]}}).afterClosed().subscribe(e=>{console.log(e),"update required"==e&&(this.activityDetail=this.getDetail())})})}else if("Call Log"==e){i="editCall";const{CallLogFormComponent:e}=yield Promise.all([n.e(54),n.e(0),n.e(744)]).then(n.bind(null,"U10Z"));this.leadService.callLogFormDetails(this.activityId).then(n=>{this.dialog.open(e,{height:"100%",width:"50%",position:{right:"0px"},data:{mode:"Edit",activityId:t,data:n[0]}}).afterClosed().subscribe(e=>{console.log(e),"update required"==e&&(this.activityDetail=this.getDetail())})})}else if("Mail"==e){i="editMail";const{MailFormComponent:e}=yield Promise.all([n.e(54),n.e(0),n.e(757)]).then(n.bind(null,"97Zw"));this.leadService.mailFormDetails(this.activityId).then(n=>{this.dialog.open(e,{height:"100%",width:"50%",position:{right:"0px"},data:{mode:"Edit",activityId:t,data:n[0]}}).afterClosed().subscribe(e=>{console.log(e),"update required"==e&&(this.activityDetail=this.getDetail())})})}this.toMainService.sendMsg(i+"-"+t)})),this.getSubmissionTsdetails=()=>new Promise((e,t)=>{let n,i=[];this.timesheetIds=c.pluck(JSON.parse(this.activityDetail.timesheet_desc),"timesheet_id"),0!=this.timesheetIds.length?this.tsSubmissionPrimaryService.getTicketSubmissionsOfMonth(this.activityDetail.assigned_to,this.timesheetIds).subscribe(t=>{n={entries:"S"==t.messType?t.data:[],actual_hours_total:0,activity_id:this.activityDetail.activity_id};for(let e of n.entries)"nonBillable"==e.billing_type&&(n.actual_hours_total+=e.hours?e.hours:0);this.activityDetail.actual_hours=n.actual_hours_total,i.push(n),e(i)},t=>{console.error(t),i.push({}),e(i)}):(n={entries:[],actual_hours_total:0,activity_id:this.activityDetail.activity_id},i.push(n),this.activityDetail.actual_hours=0,e(i))}),this.convertToLocalTime=e=>{let t=new Date(e),n=6e4*t.getTimezoneOffset();return t.setTime(t.getTime()-n),t}}ngOnInit(){return Object(d.c)(this,void 0,void 0,(function*(){this._route.parent.params.subscribe(e=>{this.leadId=e.leadId}),this.activityDetail=yield this.getDetail(),console.log(this.activityDetail),this.activityId=this.activityDetail.activity_id,console.log(this.costCode),"F"!=this.costCode&&(this.taskitem=yield this.getSubmissionTsdetails()),this.tsInlineEntryPopupService.getWorkflowProperties({applicationId:37}).then(e=>{this.tsInlineEntryPopupService.wfProperties=e,this.tsInlineEntryPopupService.getTimesheetProperties().then(e=>{this.tsInlineEntryPopupService.tsProperties=e})})}))}changeStatus(){0!=this.activityDetail.is_completed?this.openActivity():this.startActivity()}openActivity(){this.leadService.openActivity(this.activityId).then(e=>Object(d.c)(this,void 0,void 0,(function*(){console.log(e),this.activityDetail.activity_status_id=1,this._snackBar.open("Activity moved to Open!","Dismiss",{duration:2e3}),this.activityDetail=yield this.getDetail()})),e=>{console.error(e),this._snackBar.open("Failed Moving to Open Status!","Dismiss",{duration:2e3})})}completeActivity(){this.leadService.completeActivity(this.activityId,1).then(e=>Object(d.c)(this,void 0,void 0,(function*(){console.log(e),this.activityDetail.activity_status_id=3,this.activityDetail.is_completed=1,this._snackBar.open("Activity Completed!","Dismiss",{duration:2e3}),this.activityDetail=yield this.getDetail()})),e=>{console.error(e),this._snackBar.open("Failed to Complete Activity!","Dismiss",{duration:2e3})})}startActivity(){this.leadService.startActivity(this.activityId).then(e=>Object(d.c)(this,void 0,void 0,(function*(){console.log(e),this.activityDetail.activity_status_id=2,this._snackBar.open("Activity Started Successfully!","Dismiss",{duration:2e3}),this.activityDetail=yield this.getDetail()})),e=>{console.error(e),this._snackBar.open("Failed to Start Activity!","Dismiss",{duration:2e3})})}deleteActivity(){this.confirmSweetAlert("Do you want to delete this activity ?").then(e=>{e.value&&this.leadService.deleteActivity(this.activityId).then(e=>{this.toMainService.sendMsg("reload")},e=>{console.error(e)})})}confirmSweetAlert(e){return Re.a.fire({title:e,icon:"warning",showCancelButton:!0,confirmButtonText:"Yes, delete it!",cancelButtonText:"No, keep it"})}activateInlineEdit(e,t,n,i){return Object(d.c)(this,void 0,void 0,(function*(){console.log(),console.log(e,"-",t,"-",n,"-",i),this.dataArray=n,this.inlineEditActiveRow=i,(!this.inlineEditPopupService.inlineEditCallbackSubscription||this.inlineEditPopupService.inlineEditCallbackSubscription&&this.inlineEditPopupService.inlineEditCallbackSubscription.closed)&&(this.inlineEditPopupService.inlineEditCallbackSubscription=this.inlineEditPopupService.inlineEditCallback.subscribe(e=>{console.log("Res",e,e&&!(0===Object.keys(e).length&&e.constructor===Object)),!e||0===Object.keys(e).length&&e.constructor===Object||this.inlineEditResponseFunction(e)})),this.inlineEditField=e,this.editType=t,console.log("simple-text"==t),"simple-text"==t?(console.log("Hello"),"planned hours"==e&&(console.log(e),this.dropdownConfig={inlineEditField:this.inlineEditField,dropdownSelectedValue:this.inlineEditActiveRow,apiDataSelectedKeyName:this.inlineEditField,inputType:"number"})):"search-dropdown"==t?this.dropdownConfig={inlineEditField:e,dropdownSelectedValue:null,apiServiceVariable:this.sharedLazyLoadedComponentsService,apiFunctionName:"searchConsultants",apiDataUpdateKeyName:"oid",apiDataSelectedKeyName:"name",hasImageView:!0,apiDataImageKeyName:"oid"}:"date-picker"==this.editType&&"task due date"==this.inlineEditField&&(this.dropdownConfig={inlineEditField:this.inlineEditField,dropdownSelectedValue:this.inlineEditActiveRow,apiDataSelectedKeyName:this.inlineEditField}),console.log(this.editType,this.dataArray,this.dropdownConfig),this.inlineEditPopupService.setInlineEditActiveDataSubject({editType:this.editType,dataArray:this.dataArray,dropdownConfig:this.dropdownConfig})}))}inlineEditResponseFunction(e){return Object(d.c)(this,void 0,void 0,(function*(){if("planned hours"==this.inlineEditField)this.activityDetail.planned_hours!=e[this.inlineEditField]&&(console.log(e),this.leadService.updatePlannedHours(this.activityId,e[this.inlineEditField]).then(t=>Object(d.c)(this,void 0,void 0,(function*(){this.activityDetail.channel_id=e[this.inlineEditField],this._snackBar.open("Planned Hours changed successfully!","Dismiss",{duration:2e3})})),e=>{console.error(e)}));else if("Assigned To"==this.inlineEditField)e[this.dropdownConfig.apiDataSelectedKeyName]&&this.activityDetail.assigned_to!=e[this.dropdownConfig.apiDataUpdateKeyName]&&this.oppService.updateActivityAssignedTo(this.activityDetail.activity_id,e[this.dropdownConfig.apiDataUpdateKeyName]).subscribe(t=>Object(d.c)(this,void 0,void 0,(function*(){this.activityDetail.assigned_to=e[this.dropdownConfig.apiDataUpdateKeyName],this._snackBar.open("Owner Updated Successfully","Dismiss",{duration:2e3})})),e=>{this._snackBar.open(e,"dismiss",{duration:2e3})});else if("task due date"==this.inlineEditField){let t=this.convertToLocalTime(e[this.dropdownConfig.apiDataSelectedKeyName]);e[this.dropdownConfig.apiDataSelectedKeyName]&&this.activityDetail.task_due_date!=t&&this.leadService.updateTaskDuedate(this.activityDetail.lead_id,t).subscribe(t=>Object(d.c)(this,void 0,void 0,(function*(){"F"!=t?(this.activityDetail.task_due_date=e[this.dropdownConfig.apiDataSelectedKeyName],this._snackBar.open("Due date Updated Successfully","Dismiss",{duration:3e3})):this._snackBar.open("Cannot update Due date of this activity","dismiss",{duration:3e3})})),e=>{this._snackBar.open("Cannot update Due date of this activity","dismiss",{duration:3e3})})}}))}actualhoursTs(e){return null==this.activityDetail.assigned_to||0==this.activityDetail.assigned_to.length?(this._snackBar.open("There is no assigned person for this activity","Dismiss",{duration:2e3}),void this.tsInlineEntryPopupService.setInlineEditActiveDataSubject({canOpenPopup:!1})):void this.activateInlineEditTs(e)}activateInlineEditTs(e){let t=c.where(this.taskitem,{activity_id:this.activityDetail.activity_id})[0];if(console.log(this.activityDetail.assigned_to.length),0!=this.activityDetail.assigned_to.length&&"F"!=this.costCode){console.log("HELLO"),(!this.tsInlineEntryPopupService.inlineEditCallbackSubscription||this.tsInlineEntryPopupService.inlineEditCallbackSubscription&&this.tsInlineEntryPopupService.inlineEditCallbackSubscription.closed)&&(this.tsInlineEntryPopupService.inlineEditCallbackSubscription=this.tsInlineEntryPopupService.inlineEditCallback.subscribe(e=>{if(console.log(e),e&&(0!==Object.keys(e).length||e.constructor!==Object))if("S"==e.method)"W"==e.type&&(this.timesheetIds.push(e.data),null!=t&&t.entries.push(e.entryItem)),this.activityDetail.actual_hours+=e.entryItem.hours;else{if("W"==e.type){let n=[];for(let t of this.timesheetIds)t!=e.submissionId&&n.push(t);this.timesheetIds=n,n=[];for(let i of t.entries)i.submissionId&&i.submissionId!=e.submissionId||i.submission_id&&i.submission_id!=e.submissionId?n.push(i):this.activityDetail.actual_hours-=i.hours;t.entries=n}for(let n of e.billingOnly){let e=c.where(t.entries,{submission_id:n.submissionId});0==c.where(t.entries,{submissionId:n.submissionId}).length&&0==e.length&&t.entries.push(n)}t.actual_hours_total=0;for(let e of t.entries)"nonBillable"!=e.billingType&&(t.actual_hours_total+=e.hours)}}));let n,i=[],a=0;n=null!=t?t.entries:[];let o=[];for(let e of n)o.push({_id:e._id,submissionId:e.submission_id?e.submission_id:e.submissionId,date:e.date,hours:e.hours,location:e.location?e.location:"",dayType:e.day_type?e.day_type:e.dayType,billingType:e.billing_type?e.billing_type:e.billingType}),a+=e.hours;i=i.concat(o);let l=!0;G().date()==this.tsInlineEntryPopupService.tsProperties.monthly_subm_cut_off_date?l=!(G().hour()>this.tsInlineEntryPopupService.tsProperties.monthly_subm_cut_off_hour||G().hour()==this.tsInlineEntryPopupService.tsProperties.monthly_subm_cut_off_hour&&G().minute()>this.tsInlineEntryPopupService.tsProperties.monthly_subm_cut_off_minute):G().date()>this.tsInlineEntryPopupService.tsProperties.monthly_subm_cut_off_date&&!this.tsInlineEntryPopupService.tsProperties.is_payroll_frozen&&(l=!1);let r=c.where(this.tsInlineEntryPopupService.wfProperties,{sub_application_id:"R"})[0],s=G(this.activityDetail.planned_start_date).format("DD-MMM-YY");console.log("Location",this.location),this.tsInlineEntryPopupService.setInlineEditActiveDataSubject({inlineEditTs:e,viewContainerRef:this.viewContainerRef,costCentreItem:{costCentre:this.costCode,costCentreDescription:this.activityDetail.lead_name,costCentreType:"PS",location:c.filter(this.location,e=>1==e.office_id&&1==e.location_id)[0].name,approvers:[]},currentUserOid:this.activityDetail.assigned_to,wfProperties:this.tsInlineEntryPopupService.wfProperties,activeWfProperties:r,week:Math.ceil(G(this.activityDetail.planned_start_date).date()/7),inlineApplicationDetails:{applicationId:35,objectDetails:[{objectName:this.activityDetail.lead_name,heading:"Lead Name :"},{objectName:this.activityDetail.title,heading:"Activity Name    :"}],start_date:this.activityDetail.planned_start_date,end_date:this.activityDetail.task_due_date,tsTaskId:this.activityDetail.activity_id,taskItem:{_id:this.activityDetail.activity_id,task_name:this.activityDetail.title,approved_planned_hours:this.activityDetail.planned_hours-this.activityDetail.actual_hours},ticketId:this.activityDetail.application_reference_id,taskId:this.activityDetail.activity_id},hasMultipleTasks:!1,totalHours:a,entries:i,objectDetailsVisible:!0,isFromTs:!l,canEnterTs:l,isSubmissionDisabled:!1,canOpenPopup:!0,canDateBeChanged:!0,formattedMonthYearDate:G(this.activityDetail.planned_start_date).hour(15).format("YYYY-MM-DD"),monthYearDate:G(this.activityDetail.planned_start_date),defaultFormattedMonthYearDate:G(this.activityDetail.planned_start_date).hour(15).format("YYYY-MM-DD"),defaultMonthYearDate:G(this.activityDetail.planned_start_date),defaultDate:s,defaultBackupDate:s,defaultHours:8,defaultBackupHours:8,defaultDayType:"R",defaultBackupDayType:"R",defaultBillingType:"nonBillable",defaultBackupBillingType:"nonBillable"})}else 0==this.activityDetail.assigned_to.length?(this._snackBar.open("There is no assigned person for this activity","Dismiss",{duration:2e3}),this.tsInlineEntryPopupService.setInlineEditActiveDataSubject({canOpenPopup:!1})):(this._snackBar.open("Cannot update actual hours! Please contact KEBS Team","Dismiss",{duration:2e3}),this.tsInlineEntryPopupService.setInlineEditActiveDataSubject({canOpenPopup:!1}))}addLocation(e){return Object(d.c)(this,void 0,void 0,(function*(){const{LocationComponent:t}=yield n.e(756).then(n.bind(null,"+XcZ"));this.dialog.open(t,{width:"35%",data:{activityId:e,location:this.activity_location}}).afterClosed().subscribe(e=>{console.log(e),null!=e&&null!=e&&(this.activity_location=c.findWhere(this.location,{office_id:e.office_id,location_id:e.location_id}).name)})}))}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](u.a),o["\u0275\u0275directiveInject"](a.a),o["\u0275\u0275directiveInject"](a.g),o["\u0275\u0275directiveInject"](Ae.a),o["\u0275\u0275directiveInject"](h.a),o["\u0275\u0275directiveInject"](y.b),o["\u0275\u0275directiveInject"](v.a),o["\u0275\u0275directiveInject"](He.a),o["\u0275\u0275directiveInject"](g.a),o["\u0275\u0275directiveInject"](Ke.a),o["\u0275\u0275directiveInject"](Ue.a),o["\u0275\u0275directiveInject"](o.ViewContainerRef))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-activity-card-general"]],inputs:{activityNo:"activityNo",scrollIndex:"scrollIndex",activityIds:"activityIds",costCode:"costCode",location:"location"},outputs:{deactivateEvent:"deactivateEvent"},decls:78,vars:23,consts:[[1,"row","list-card-styles"],[1,"col-12","pl-0","pr-0"],[1,"card","listcard",2,"border-left-width","3px !important","padding-top","3px",3,"ngStyle"],[1,"card-body",2,"padding","2px !important"],[1,"row","card-details","p-0"],[1,"col-1"],[4,"ngIf"],["class","primary",4,"ngIf"],["class","secondary","style","padding-left: 3px !important;",4,"ngIf"],[1,"col-2","pt-1","name",3,"matTooltip","click"],[1,"col-1","pt-1","parent",3,"click"],[1,"col-2","pt-1","parent",3,"click"],["inlineEdit","",1,"col-2","pt-1","parent","pl-0",2,"cursor","pointer",3,"click"],["inlineEdit","",1,"col-1","pt-1","parent","pl-0",3,"click"],[3,"click",4,"ngIf"],[1,"col-1","pt-1","parent"],["style","cursor: pointer;",3,"click",4,"ngIf"],["style","cursor: pointer;","inlineEdit","",3,"click",4,"ngIf"],[1,"col-1","d-flex","ml-0"],["mat-icon-button","","class","tick-active",3,"ngStyle","click",4,"ngIf"],["mat-icon-button","","matTooltip","View more",1,"icon-tray-button","ml-auto",3,"matMenuTriggerFor"],[1,"smallCardIcon"],[1,"card-panel"],["options","matMenu"],[1,"card"],[1,"card-body","pl-3","pr-3","pt-2","pb-2"],[1,"row","pb-1"],[2,"font-size","14px !important","font-weight","normal !important","color","#b8b8b8 !important"],[1,"row","pt-2","pb-2"],["matTooltip","Edit","matRipple","",1,"col-4","pb-1","col-styles-lead",2,"cursor","pointer",3,"click"],[1,"row"],[2,"height","26px","width","26px"],[1,"pt-1",2,"color","#1a1a1a","font-size","19px !important"],["matRipple","","matTooltip","Open in new tab",1,"col-4","col-styles-lead",2,"cursor","pointer"],[2,"height","24px","width","24px"],[1,"pt-1",2,"color","#1289A7","font-size","19px !important"],["matRipple","","matTooltip","Delete",1,"col-4","col-styles-lead",2,"cursor","pointer",3,"click"],[1,"pt-1",2,"color","#cf0001","font-size","21px !important"],["matTooltip","Open","matRipple","",1,"col-4","pb-1","col-styles-lead",2,"cursor","pointer"],[1,"pt-1",2,"color","#9A9A9A","font-size","21px !important",3,"click"],["matRipple","","matTooltip","Inprocess",1,"col-4","col-styles-lead",2,"cursor","pointer"],[1,"pt-1",2,"color","#ffa502","font-size","21px !important",3,"click"],["matTooltip","Complete","matRipple","",1,"col-4","pb-1","col-styles-lead",2,"cursor","pointer"],[1,"pt-1",2,"color","#009432","font-size","21px !important",3,"click"],[1,"primary"],["mat-icon-button","",1,"activity-button",3,"matTooltip","ngStyle"],[1,"secondary",2,"padding-left","3px !important"],["mat-icon-button","","class","activity-status-button",3,"matTooltip",4,"ngIf"],["mat-icon-button","",1,"activity-status-button",3,"matTooltip"],["type","small",3,"oid",4,"ngIf"],["type","small",3,"oid"],[3,"click"],[2,"cursor","pointer",3,"click"],["inlineEditTs",""],["inlineEdit","",2,"cursor","pointer",3,"click"],["mat-icon-button","",1,"tick-active",3,"ngStyle","click"]],template:function(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",0),o["\u0275\u0275elementStart"](1,"div",1),o["\u0275\u0275elementStart"](2,"div",2),o["\u0275\u0275elementStart"](3,"div",3),o["\u0275\u0275elementStart"](4,"div",4),o["\u0275\u0275elementStart"](5,"div",5),o["\u0275\u0275template"](6,Xe,1,0,"app-shimmer",6),o["\u0275\u0275template"](7,Qe,4,5,"span",7),o["\u0275\u0275template"](8,et,2,1,"span",8),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](9,"div",9),o["\u0275\u0275listener"]("click",(function(){return t.navigateToLeadActivityDetailPage()})),o["\u0275\u0275template"](10,tt,1,0,"app-shimmer",6),o["\u0275\u0275template"](11,nt,2,1,"span",6),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](12,"div",10),o["\u0275\u0275listener"]("click",(function(){return t.navigateToLeadActivityDetailPage()})),o["\u0275\u0275template"](13,it,1,0,"app-shimmer",6),o["\u0275\u0275template"](14,at,2,1,"span",6),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](15,"div",11),o["\u0275\u0275listener"]("click",(function(){return t.navigateToLeadActivityDetailPage()})),o["\u0275\u0275template"](16,ot,1,0,"app-shimmer",6),o["\u0275\u0275template"](17,lt,2,1,"span",6),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](18,"div",12),o["\u0275\u0275listener"]("click",(function(){return t.activateInlineEdit("Assigned To","search-dropdown",[],"")})),o["\u0275\u0275template"](19,rt,1,0,"app-shimmer",6),o["\u0275\u0275template"](20,dt,2,1,"span",6),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](21,"div",13),o["\u0275\u0275listener"]("click",(function(){return t.activateInlineEdit("task due date","date-picker",[],t.activityDetail.task_due_date)})),o["\u0275\u0275template"](22,ct,1,0,"app-shimmer",14),o["\u0275\u0275template"](23,pt,2,1,"span",6),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](24,"div",15),o["\u0275\u0275template"](25,mt,1,0,"app-shimmer",14),o["\u0275\u0275template"](26,ut,3,1,"span",16),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](27,"div",15),o["\u0275\u0275template"](28,ht,1,0,"app-shimmer",6),o["\u0275\u0275template"](29,vt,2,1,"span",17),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](30,"div",18),o["\u0275\u0275elementStart"](31,"span"),o["\u0275\u0275template"](32,ft,3,3,"button",19),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](33,"button",20),o["\u0275\u0275elementStart"](34,"mat-icon",21),o["\u0275\u0275text"](35,"more_vert"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](36,"mat-menu",22,23),o["\u0275\u0275elementStart"](38,"div",24),o["\u0275\u0275elementStart"](39,"div",25),o["\u0275\u0275elementStart"](40,"div",26),o["\u0275\u0275elementStart"](41,"span",27),o["\u0275\u0275text"](42,"Actions"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](43,"div",28),o["\u0275\u0275elementStart"](44,"div",29),o["\u0275\u0275listener"]("click",(function(){return t.editActivity(t.activityDetail.sales_activity_type,t.activityDetail.activity_id)})),o["\u0275\u0275elementStart"](45,"div",30),o["\u0275\u0275elementStart"](46,"div",31),o["\u0275\u0275elementStart"](47,"mat-icon",32),o["\u0275\u0275text"](48," edit "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](49,"div",33),o["\u0275\u0275elementStart"](50,"div",30),o["\u0275\u0275elementStart"](51,"div",34),o["\u0275\u0275elementStart"](52,"mat-icon",35),o["\u0275\u0275text"](53," launch "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](54,"div",36),o["\u0275\u0275listener"]("click",(function(){return t.deleteActivity()})),o["\u0275\u0275elementStart"](55,"div",30),o["\u0275\u0275elementStart"](56,"div",34),o["\u0275\u0275elementStart"](57,"mat-icon",37),o["\u0275\u0275text"](58," delete "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](59,"div",26),o["\u0275\u0275elementStart"](60,"span",27),o["\u0275\u0275text"](61," Change status"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](62,"div",28),o["\u0275\u0275elementStart"](63,"div",38),o["\u0275\u0275elementStart"](64,"div",30),o["\u0275\u0275elementStart"](65,"div",31),o["\u0275\u0275elementStart"](66,"mat-icon",39),o["\u0275\u0275listener"]("click",(function(){return t.openActivity()})),o["\u0275\u0275text"](67," check_circle "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](68,"div",40),o["\u0275\u0275elementStart"](69,"div",30),o["\u0275\u0275elementStart"](70,"div",34),o["\u0275\u0275elementStart"](71,"mat-icon",41),o["\u0275\u0275listener"]("click",(function(){return t.startActivity()})),o["\u0275\u0275text"](72," timelapse "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](73,"div",42),o["\u0275\u0275elementStart"](74,"div",30),o["\u0275\u0275elementStart"](75,"div",31),o["\u0275\u0275elementStart"](76,"mat-icon",43),o["\u0275\u0275listener"]("click",(function(){return t.completeActivity()})),o["\u0275\u0275text"](77," check_circle "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275reference"](37);o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngStyle",o["\u0275\u0275pureFunction1"](21,yt,t.activityDetail?t.activityDetail.activity_type_bg:"white")),o["\u0275\u0275advance"](4),o["\u0275\u0275property"]("ngIf",null==t.activityDetail||null==t.activityDetail),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",t.activityDetail),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",t.activityDetail),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("matTooltip",t.activityDetail?t.activityDetail.title:""),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",null==t.activityDetail||null==t.activityDetail),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",t.activityDetail),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngIf",null==t.activityDetail||null==t.activityDetail),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",t.activityDetail),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngIf",null==t.activityDetail||null==t.activityDetail),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",t.activityDetail),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngIf",null==t.activityDetail),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",t.activityDetail),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngIf",null==t.activityDetail||null==t.activityDetail),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",t.activityDetail),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngIf",null==t.activityDetail||null==t.activityDetail),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",t.activityDetail),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngIf",null==t.activityDetail||null==t.activityDetail),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",t.activityDetail),o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("ngIf",t.activityDetail),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("matMenuTriggerFor",e)}},directives:[i.NgStyle,i.NgIf,C.a,Ye.a,_.a,w.f,E.a,w.g,Ge,Y],styles:[".list-card-styles[_ngcontent-%COMP%]   .listcard[_ngcontent-%COMP%]{transition:all .25s linear;height:39px;max-height:39px!important}.list-card-styles[_ngcontent-%COMP%]   .tick-active[_ngcontent-%COMP%]{line-height:24px!important;width:28px!important;height:28px!important}.list-card-styles[_ngcontent-%COMP%]   .parent[_ngcontent-%COMP%]{font-size:14px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.list-card-styles[_ngcontent-%COMP%]   .activity-button[_ngcontent-%COMP%]{color:#fff;line-height:24px!important;width:29px!important;height:29px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.list-card-styles[_ngcontent-%COMP%]   .activity-button[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%]{font-size:18px}.list-card-styles[_ngcontent-%COMP%]   .activity-status-button[_ngcontent-%COMP%]{color:#fff;line-height:22px!important;width:23px!important;height:23px!important;background-color:#190281}.list-card-styles[_ngcontent-%COMP%]   .activity-status-button[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%]{font-size:17px}.list-card-styles[_ngcontent-%COMP%]   .name[_ngcontent-%COMP%]{font-size:14px;color:#cf0001;font-family:Roboto;font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.list-card-styles[_ngcontent-%COMP%]   .listcard[_ngcontent-%COMP%]:hover{box-shadow:0 4px 8px 0 rgba(0,0,0,.2),0 6px 20px 0 rgba(0,0,0,.19);cursor:pointer}.list-card-styles[_ngcontent-%COMP%]   .listcard[_ngcontent-%COMP%]:hover   .icon-tray-button[_ngcontent-%COMP%]{visibility:visible!important;animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}.list-card-styles[_ngcontent-%COMP%]   .icon-tray-button[_ngcontent-%COMP%]{width:27px!important;height:27px!important;line-height:25px!important;visibility:hidden}.list-card-styles[_ngcontent-%COMP%]   .smallCardIcon[_ngcontent-%COMP%]{font-size:18px!important;color:#868683!important}.list-card-styles[_ngcontent-%COMP%]     .card-panel .mat-menu-content .col-styles-lead{cursor:pointer!important;border-radius:4px!important}.list-card-styles[_ngcontent-%COMP%]     .card-panel .mat-menu-content .col-styles-lead:hover{background-color:rgba(255,235,235,.6705882352941176)!important}@keyframes slide-top{0%{transform:translateY(10px)}to{transform:translateY(0)}}"]}),e})();function St(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"div"),o["\u0275\u0275elementStart"](1,"span",3),o["\u0275\u0275element"](2,"mat-spinner",4),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e&&(o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("diameter",30))}function bt(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div"),o["\u0275\u0275element"](1,"app-activity-card-general",17),o["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=t.index,i=o["\u0275\u0275nextContext"](4);o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("activityNo",e.activity_id)("costCode",i.costCode)("location",i.location)("scrollIndex",n)}}function _t(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"cdk-virtual-scroll-viewport",15),o["\u0275\u0275template"](1,bt,2,4,"div",16),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](3);o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("cdkVirtualForOf",e.activityIds)("cdkVirtualForTemplateCacheSize",0)}}function Ct(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div"),o["\u0275\u0275elementStart"](1,"mat-card",5),o["\u0275\u0275elementStart"](2,"div",6),o["\u0275\u0275elementStart"](3,"div",7),o["\u0275\u0275text"](4," Type "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](5,"div",8),o["\u0275\u0275text"](6," Name "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](7,"div",7),o["\u0275\u0275text"](8," Subtype "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](9,"div",9),o["\u0275\u0275text"](10," Governance Type "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](11,"div",10),o["\u0275\u0275text"](12," Assigned To "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](13,"div",11),o["\u0275\u0275text"](14," Due Date "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](15,"div",11),o["\u0275\u0275text"](16," Actual Hours "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](17,"div",12),o["\u0275\u0275text"](18," Planned Hours "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275element"](19,"div",13),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](20,_t,2,2,"cdk-virtual-scroll-viewport",14),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](20),o["\u0275\u0275property"]("ngIf",e.activityIds)}}function wt(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"div"),o["\u0275\u0275elementStart"](1,"div"),o["\u0275\u0275elementStart"](2,"h1",18),o["\u0275\u0275text"](3," No Activities found ! "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](4,"div",19),o["\u0275\u0275element"](5,"img",20),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]())}function Et(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div"),o["\u0275\u0275template"](1,Ct,21,1,"div",2),o["\u0275\u0275template"](2,wt,6,0,"div",2),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",0!=(null==e.activityIds?null:e.activityIds.length)),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",0==(null==e.activityIds?null:e.activityIds.length))}}let It=(()=>{class e{constructor(e,t,n,i,a){this.leadService=e,this._route=t,this.reloadService=n,this.leadsReloadService=i,this.oppService=a,this.loading=!0,this.getActivityId=()=>new Promise((e,t)=>{this.leadsReloadService.getSearchContent().subscribe(t=>Object(d.c)(this,void 0,void 0,(function*(){this.activityIds=t,e(t)})),e=>{console.error(e),t(e)})})}ngOnInit(){return Object(d.c)(this,void 0,void 0,(function*(){this.loading=!0,yield this.oppService.getLocationList().then(e=>Object(d.c)(this,void 0,void 0,(function*(){this.location=e.data})),e=>{console.log(e)}),"leads"==window.location.pathname.split("/")[2]?this.applicationId=35:"opportunities"==window.location.pathname.split("/")[2]&&(this.applicationId=36),this._route.parent.params.subscribe(e=>{this.leadId=e.leadId}),this.getCostCode(this.leadId),this.activityIds=yield this.getActivityId(),this.loading=!1,this.reloadService.getMsg().subscribe(e=>Object(d.c)(this,void 0,void 0,(function*(){"reload leads"==e&&(this.activityIds=yield this.getActivityId())})))}))}getCostCode(e){null!=e&&this.leadService.getCostCode(e).then(e=>Object(d.c)(this,void 0,void 0,(function*(){console.log(e),this.costCode=e})),e=>{console.log(e)})}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](u.a),o["\u0275\u0275directiveInject"](a.a),o["\u0275\u0275directiveInject"](Ae.a),o["\u0275\u0275directiveInject"](Fe.a),o["\u0275\u0275directiveInject"](Ue.a))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-activity-general-view"]],decls:4,vars:2,consts:[[1,"row","general"],[1,"col-12"],[4,"ngIf"],[1,"pt-6","d-flex","justify-content-center","align-items-center","mt-5","slide-in-top"],[3,"diameter"],[1,"pt-0","pl-3","pr-3"],[1,"row","pb-2","pt-2"],[1,"col-1","column-name"],[1,"col-2","pl-4","column-name"],[1,"col-2","column-name","pr-0","ml-0",2,"margin-right","0px"],[1,"col-2","column-name"],[1,"col-1","column-name","pl-0"],[1,"col-1","column-name","pl-0","pr-0"],[1,"col-1"],["itemSize","50","minBufferPx","300","maxBufferPx","500","class","example-viewport",4,"ngIf"],["itemSize","50","minBufferPx","300","maxBufferPx","500",1,"example-viewport"],[4,"cdkVirtualFor","cdkVirtualForOf","cdkVirtualForTemplateCacheSize"],[3,"activityNo","costCode","location","scrollIndex"],[1,"d-flex","justify-content-center","align-items-center","mt-5","slide-in-top"],[1,"d-flex","justify-content-center","align-items-center","slide-from-down"],["src","assets/images/nomilestone.png","height","300","width","350",1,"mt-4"]],template:function(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"div",0),o["\u0275\u0275elementStart"](1,"div",1),o["\u0275\u0275template"](2,St,3,1,"div",2),o["\u0275\u0275template"](3,Et,3,2,"div",2),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e&&(o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngIf",t.loading),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",!t.loading))},directives:[i.NgIf,Ve.c,ze.a,$e.e,$e.a,$e.d,xt],styles:[".general[_ngcontent-%COMP%]   .column-name[_ngcontent-%COMP%]{font-size:11px;color:#66615b}.general[_ngcontent-%COMP%]   .example-viewport[_ngcontent-%COMP%]{min-height:608px}"]}),e})(),kt=(()=>{class e{constructor(){this.focusOut=new o.EventEmitter,this.currency="$",this.editMode=!1}ngOnInit(){}onFocusOut(){this.focusOut.emit(this.data)}saveClicked(){this.focusOut.emit(this.data)}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-inline-edit2"]],inputs:{data:"data"},outputs:{focusOut:"focusOut"},decls:2,vars:1,consts:[["appAutofocus","","type","text",2,"width","100%",3,"ngModel","focusout","keydown.enter","ngModelChange"]],template:function(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"div"),o["\u0275\u0275elementStart"](1,"input",0),o["\u0275\u0275listener"]("focusout",(function(){return t.onFocusOut()}))("keydown.enter",(function(e){return e.target.blur()}))("ngModelChange",(function(e){return t.data=e}))("keydown.enter",(function(e){return e.target.blur()}))("focusout",(function(){return t.editMode=!1})),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e&&(o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngModel",t.data))},directives:[Me.e,Me.v,Me.y],styles:[""]}),e})();var Ot=n("4/q7"),Dt=n("6t9p"),Mt=n("jhN1");let Pt=(()=>{class e{constructor(e){this.sanitizer=e}transform(e){return this.sanitizer.bypassSecurityTrustHtml(e)}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](Mt.c))},e.\u0275pipe=o["\u0275\u0275definePipe"]({name:"safeHtml",type:e,pure:!0}),e})();function Tt(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"span"),o["\u0275\u0275elementStart"](1,"button",5),o["\u0275\u0275listener"]("click",(function(){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"](2).showCreateNotefunction()})),o["\u0275\u0275elementStart"](2,"mat-icon",6),o["\u0275\u0275text"](3,"add"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}}function At(e,t){if(1&e&&(o["\u0275\u0275elementContainerStart"](0),o["\u0275\u0275template"](1,Tt,4,0,"span",3),o["\u0275\u0275elementContainerEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",0!=e.notes.length)}}const Ft=function(e){return{backgroundColor:e}};function Nt(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div",39),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](e);const n=t.$implicit;return o["\u0275\u0275nextContext"](2).setNoteColor(n)})),o["\u0275\u0275elementEnd"]()}2&e&&o["\u0275\u0275property"]("ngStyle",o["\u0275\u0275pureFunction1"](1,Ft,t.$implicit))}function Lt(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"button",40),o["\u0275\u0275listener"]("click",(function(){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"](2).saveNote()})),o["\u0275\u0275elementStart"](1,"mat-icon",41),o["\u0275\u0275text"](2," done_all"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}}function jt(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"button",42),o["\u0275\u0275listener"]("click",(function(){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"](2).updateNote()})),o["\u0275\u0275elementStart"](1,"mat-icon",41),o["\u0275\u0275text"](2," done_all"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}}function Bt(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div",7),o["\u0275\u0275elementStart"](1,"div",8),o["\u0275\u0275elementStart"](2,"div",9),o["\u0275\u0275elementStart"](3,"div",10),o["\u0275\u0275elementStart"](4,"div",11),o["\u0275\u0275elementStart"](5,"div",12),o["\u0275\u0275elementStart"](6,"span",13),o["\u0275\u0275text"](7," Title : "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](8,"div",14),o["\u0275\u0275elementStart"](9,"app-inline-edit2",15),o["\u0275\u0275listener"]("focusOut",(function(t){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"]().getCurrentNoteTitle(t)})),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](10,"div",16),o["\u0275\u0275template"](11,Nt,1,3,"div",17),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](12,"div",18),o["\u0275\u0275template"](13,Lt,3,0,"button",19),o["\u0275\u0275template"](14,jt,3,0,"button",20),o["\u0275\u0275elementStart"](15,"button",21),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](e);const t=o["\u0275\u0275nextContext"]();return t.showCreateNotefunction(),t.showEmptyState()})),o["\u0275\u0275elementStart"](16,"mat-icon",6),o["\u0275\u0275text"](17,"close"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](18,"div",22),o["\u0275\u0275elementStart"](19,"div",23),o["\u0275\u0275elementStart"](20,"dx-html-editor",24),o["\u0275\u0275listener"]("valueType",(function(){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"]().valueType}))("valueChange",(function(t){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"]().notesValueChange(t)})),o["\u0275\u0275elementStart"](21,"dxo-toolbar"),o["\u0275\u0275element"](22,"dxi-item",25),o["\u0275\u0275element"](23,"dxi-item",26),o["\u0275\u0275element"](24,"dxi-item",27),o["\u0275\u0275element"](25,"dxi-item",28),o["\u0275\u0275element"](26,"dxi-item",29),o["\u0275\u0275element"](27,"dxi-item",30),o["\u0275\u0275element"](28,"dxi-item",27),o["\u0275\u0275element"](29,"dxi-item",31),o["\u0275\u0275element"](30,"dxi-item",32),o["\u0275\u0275element"](31,"dxi-item",33),o["\u0275\u0275element"](32,"dxi-item",27),o["\u0275\u0275element"](33,"dxi-item",34),o["\u0275\u0275element"](34,"dxi-item",35),o["\u0275\u0275element"](35,"dxi-item",27),o["\u0275\u0275element"](36,"dxi-item",36),o["\u0275\u0275element"](37,"dxi-item",37),o["\u0275\u0275elementEnd"](),o["\u0275\u0275element"](38,"span",38),o["\u0275\u0275pipe"](39,"safeHtml"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("ngStyle",o["\u0275\u0275pureFunction1"](8,Ft,e.currentNoteColor)),o["\u0275\u0275advance"](6),o["\u0275\u0275property"]("data",e.currentNoteTitle),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngForOf",e.notes_bg_color),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngIf","create"==e.noteMode),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf","edit"==e.noteMode),o["\u0275\u0275advance"](24),o["\u0275\u0275property"]("innerHTML",o["\u0275\u0275pipeBind1"](39,6,e.currentNote),o["\u0275\u0275sanitizeHtml"])}}function Vt(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div",47),o["\u0275\u0275elementStart"](1,"div",48),o["\u0275\u0275elementStart"](2,"div",49),o["\u0275\u0275elementStart"](3,"div",11),o["\u0275\u0275elementStart"](4,"div",50),o["\u0275\u0275elementStart"](5,"span",51),o["\u0275\u0275text"](6," Title : "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](7,"span",52),o["\u0275\u0275text"](8),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](9,"div",53),o["\u0275\u0275elementStart"](10,"button",54),o["\u0275\u0275elementStart"](11,"mat-icon",6),o["\u0275\u0275text"](12,"more_vert"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](13,"mat-menu",null,55),o["\u0275\u0275elementStart"](15,"button",56),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](e);const t=o["\u0275\u0275nextContext"]().$implicit;return o["\u0275\u0275nextContext"](3).editNote(t.note_id,t.color_code,t.note,t.title)})),o["\u0275\u0275text"](16," Edit "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](17,"button",56),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](e);const t=o["\u0275\u0275nextContext"]().$implicit;return o["\u0275\u0275nextContext"](3).deleteNote(t)})),o["\u0275\u0275text"](18," Delete "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](19,"div",57),o["\u0275\u0275elementStart"](20,"div",58),o["\u0275\u0275element"](21,"span",38),o["\u0275\u0275pipe"](22,"safeHtml"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](23,"div",57),o["\u0275\u0275elementStart"](24,"div",59),o["\u0275\u0275elementStart"](25,"span",60),o["\u0275\u0275text"](26),o["\u0275\u0275pipe"](27,"DDMMYYYY"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275reference"](14),t=o["\u0275\u0275nextContext"]().$implicit;o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngStyle",o["\u0275\u0275pureFunction1"](10,Ft,t.color_code)),o["\u0275\u0275advance"](7),o["\u0275\u0275textInterpolate1"](" ",t.title," "),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("matMenuTriggerFor",e),o["\u0275\u0275advance"](11),o["\u0275\u0275property"]("innerHTML",o["\u0275\u0275pipeBind1"](22,6,t.note),o["\u0275\u0275sanitizeHtml"]),o["\u0275\u0275advance"](5),o["\u0275\u0275textInterpolate2"](" ",t.created_by," ",o["\u0275\u0275pipeBind1"](27,8,t.created_date)," ")}}function Ut(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",7),o["\u0275\u0275template"](1,Vt,28,12,"div",46),o["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=o["\u0275\u0275nextContext"](3);o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",e.note_id!=n.currentEditingNoteId)}}function zt(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",44),o["\u0275\u0275template"](1,Ut,2,1,"div",45),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngForOf",e.notes)}}function $t(e,t){if(1&e&&(o["\u0275\u0275elementContainerStart"](0),o["\u0275\u0275template"](1,zt,2,1,"div",43),o["\u0275\u0275elementContainerEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",0!=e.notes.length)}}function qt(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div",62),o["\u0275\u0275elementStart"](1,"div",63),o["\u0275\u0275elementStart"](2,"div"),o["\u0275\u0275elementStart"](3,"p",64),o["\u0275\u0275text"](4," No notes found! Let's create one "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](5,"div",65),o["\u0275\u0275elementStart"](6,"button",66),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](e);const t=o["\u0275\u0275nextContext"](2);return t.showCreateNotefunction(),t.showEmptyState()})),o["\u0275\u0275text"](7," Create notes "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](8,"div",65),o["\u0275\u0275element"](9,"img",67),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}}function Rt(e,t){if(1&e&&(o["\u0275\u0275elementContainerStart"](0),o["\u0275\u0275template"](1,qt,10,0,"div",61),o["\u0275\u0275elementContainerEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",0==e.notes.length&&0==e.emptynotes)}}let Ht=(()=>{class e{constructor(e,t){this.leadService=e,this._snackBar=t,this.valueType="html",this.emptynotes=!1,this.currentNoteColor="white",this.currentNoteTitle="",this.noteMode="create",this.showCreateNote=0,this.notes_bg_color=["#fbbc04","#e6c9a8","#ccff90","#F8EFBA","#e2e2e2","#f4ffa4","#fffa65","#ffb8b8"],this.setNoteColor=e=>{console.log(e),this.currentNoteColor=e},this.editNote=(e,t,n,i)=>{console.log(e),this.currentEditingNoteId=e,this.showCreateNote=!this.showCreateNote,this.currentNoteColor=t,this.currentNoteTitle=i,this.currentNote=n,this.noteMode="edit"}}ngOnInit(){}ngOnChanges(){this.activityId&&(this.notes=[],this.leadService.getActivityNotes(this.activityId).subscribe(e=>{console.log(e),this.notes=e},e=>{console.error(e)}))}notesValueChange(e){this.currentNote=e}getCurrentNoteTitle(e){this.currentNoteTitle=e}showCreateNotefunction(){this.currentNoteColor="white",this.currentNoteTitle="",this.currentNote="",this.showCreateNote=!this.showCreateNote,this.noteMode="create"}saveNote(){console.log(this.activityId),console.log(this.activityId,this.currentNoteColor,this.currentNoteTitle,this.currentNote),this.leadService.createActivityNote(this.activityId,this.currentNoteColor,this.currentNoteTitle,this.currentNote).subscribe(e=>{console.log(e),this.notes=e,this.showCreateNote=!this.showCreateNote,this._snackBar.open("Note Created succesfully","close",{duration:2e3})},e=>{console.error(e)})}updateNote(){this.leadService.editActivityNote(this.activityId,this.currentEditingNoteId,this.currentNote,this.currentNoteTitle).subscribe(e=>{console.log(e),this.notes=e,this.showCreateNote=!this.showCreateNote,this._snackBar.open("Note updated successfully!","close",{duration:2e3}),this.currentEditingNoteId="*"},e=>{console.error(e)})}deleteNote(e){this.leadService.deleteActivityNote(this.activityId,e.note_id).subscribe(e=>{this.notes=e,this._snackBar.open("Note deleted!","close",{duration:2e3})},e=>{console.error(e)})}showEmptyState(){this.emptynotes=!this.emptynotes}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](u.a),o["\u0275\u0275directiveInject"](h.a))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-activity-user-note"]],inputs:{activityId:"activityId"},features:[o["\u0275\u0275NgOnChangesFeature"]],decls:8,vars:4,consts:[[1,"row","pt-2","pb-1"],[1,"col-12","pl-3","pr-3"],[1,"tileName","pr-2"],[4,"ngIf"],["class","row pb-2",4,"ngIf"],["mat-icon-button","","matTooltip","Add note",1,"more-button","ml-auto",3,"click"],[1,"smallCardIcon"],[1,"row","pb-2"],[1,"col-12","pl-3"],[1,"card","slide-in-top"],[1,"card-body","pt-2","pb-2","pl-3","pr-3",3,"ngStyle"],[1,"row"],[1,"col-1","pl-0"],[1,"account-info-details"],[1,"col-4","pl-1","pr-0"],[3,"data","focusOut"],[1,"col-5","d-flex"],["matRipple","","class","colors mr-3 mt-2",3,"ngStyle","click",4,"ngFor","ngForOf"],[1,"col-2","d-flex"],["mat-icon-button","","class","iconbtn ml-auto mr-4","matTooltip","Create note",3,"click",4,"ngIf"],["mat-icon-button","","class","iconbtn ml-auto mr-4","matTooltip","Update note",3,"click",4,"ngIf"],["mat-icon-button","","matTooltip","Close",1,"close",3,"click"],[1,"row","pt-2","pb-2"],[1,"col-12","pl-0","pr-0"],[1,"dev-extreme-styles",3,"valueType","valueChange"],["name","undo"],["name","redo"],["name","separator"],["name","bold"],["name","italic"],["name","underline"],["name","alignLeft"],["name","alignCenter"],["name","alignJustify"],["name","orderedList"],["name","bulletList"],["name","color"],["name","background"],[3,"innerHTML"],["matRipple","",1,"colors","mr-3","mt-2",3,"ngStyle","click"],["mat-icon-button","","matTooltip","Create note",1,"iconbtn","ml-auto","mr-4",3,"click"],[2,"font-size","17px !important"],["mat-icon-button","","matTooltip","Update note",1,"iconbtn","ml-auto","mr-4",3,"click"],["class","notes",4,"ngIf"],[1,"notes"],["class","row pb-2",4,"ngFor","ngForOf"],["class","col-12 pl-3 slide-from-down ",4,"ngIf"],[1,"col-12","pl-3","slide-from-down"],[1,"card",3,"ngStyle"],[1,"card-body","pt-2","pb-2","pl-3","pr-3"],[1,"col-8","pl-0"],[1,"lead-info-details"],[1,"lead-sub-headings","pl-2"],[1,"col-4","d-flex"],["mat-icon-button","","matTooltip","View more",1,"more-button","ml-auto",3,"matMenuTriggerFor"],["note_options","matMenu"],["mat-menu-item","",1,"drop-btn",3,"click"],[1,"row","pt-2"],[1,"col-12","notes-data","pl-0"],[1,"col-12","d-flex"],[1,"ml-auto","sub-heading"],["class","row slide-from-down","style","min-height: 250px !important;",4,"ngIf"],[1,"row","slide-from-down",2,"min-height","250px !important"],[1,"col-12"],[1,"empty-styles","d-flex","justify-content-center","align-items-center","mt-1"],[1,"d-flex","justify-content-center","align-items-center"],["mat-raised-button","",1,"complete",3,"click"],["src","https://assets.kebs.app/images/nomilestone.png","height","190","width","190",1,"mt-4"]],template:function(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"div",0),o["\u0275\u0275elementStart"](1,"div",1),o["\u0275\u0275elementStart"](2,"span",2),o["\u0275\u0275text"](3," Notes "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](4,At,2,1,"ng-container",3),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](5,Bt,40,10,"div",4),o["\u0275\u0275template"](6,$t,2,1,"ng-container",3),o["\u0275\u0275template"](7,Rt,2,1,"ng-container",3)),2&e&&(o["\u0275\u0275advance"](4),o["\u0275\u0275property"]("ngIf",t.notes),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",1==t.showCreateNote),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",t.notes),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",t.notes))},directives:[i.NgIf,_.a,C.a,E.a,i.NgStyle,kt,i.NgForOf,Ot.a,Dt.Ge,Dt.o,w.f,w.g,w.d],pipes:[Pt,X],styles:['.dev-extreme-styles[_ngcontent-%COMP%]     .dx-htmleditor-toolbar-wrapper .dx-toolbar-items-container{height:48px!important}.dev-extreme-styles[_ngcontent-%COMP%]     .dx-htmleditor-toolbar-wrapper .dx-button-content .dx-icon{font-size:16px!important;color:#66615b!important}.slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}.lead-info-details[_ngcontent-%COMP%]{color:#1a1a1a;font-weight:400;font-size:14px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.lead-sub-headings[_ngcontent-%COMP%]{color:#cf0001;font-size:14px;font-weight:500}.speech-bubble[_ngcontent-%COMP%]{position:relative;background:#f4f3ef;border-radius:.8em;height:6rem;width:35rem;padding:1rem}.drop-btn[_ngcontent-%COMP%]{font-size:13px!important;height:38px!important;line-height:2!important;padding:0 13px!important}.notes-data[_ngcontent-%COMP%]{color:#1a1a1a;font-weight:400;font-size:14px}.notes[_ngcontent-%COMP%]{max-height:18rem;overflow:scroll}.sub-heading[_ngcontent-%COMP%]{color:#7b7b7a!important;font-size:14px!important;text-align:center!important;font-weight:400!important}.smallCardIcon[_ngcontent-%COMP%]{font-size:18px!important;color:#868683!important}.tileName[_ngcontent-%COMP%]{color:#9a9a9a;font-size:14px!important}.more-button[_ngcontent-%COMP%]{width:27px!important;height:27px!important;line-height:25px!important}.slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}.speech-bubble[_ngcontent-%COMP%]:after{content:"";position:absolute;right:0;top:50%;width:0;height:0;border:30px solid transparent;border-left-color:#f4f3ef;border-right:0;border-top:0;margin-top:-15px;margin-right:-30px}.colors[_ngcontent-%COMP%]{cursor:pointer;height:16px;width:16px;margin-top:4px;border-radius:50%}.complete[_ngcontent-%COMP%]{color:#fff;font-weight:400;font-size:12px!important;background-color:#cf0001;min-width:62px;line-height:26px;padding:0 10px}.empty-styles[_ngcontent-%COMP%]{color:#3c3c3c;font-weight:500;font-size:14px}']}),e})();function Kt(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",18),o["\u0275\u0275elementStart"](1,"div",19),o["\u0275\u0275elementStart"](2,"div",20),o["\u0275\u0275elementStart"](3,"span",21),o["\u0275\u0275text"](4),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275text"](5),o["\u0275\u0275elementEnd"](),o["\u0275\u0275element"](6,"app-user-image",22),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]().$implicit,t=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](4),o["\u0275\u0275textInterpolate"](t.getDateFormat(e.time)),o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" ",e.comment," "),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("id",e.commentor_oid)}}function Yt(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",23),o["\u0275\u0275element"](1,"app-user-image",24),o["\u0275\u0275elementStart"](2,"div",25),o["\u0275\u0275elementStart"](3,"div",20),o["\u0275\u0275elementStart"](4,"span",26),o["\u0275\u0275text"](5),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](6,"span",27),o["\u0275\u0275text"](7),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](8,"div",28),o["\u0275\u0275text"](9),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]().$implicit,t=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("id",e.commentor_oid),o["\u0275\u0275advance"](4),o["\u0275\u0275textInterpolate1"](" ",e.commentor_name," "),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate"](t.getDateFormat(e.time)),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate1"](" ",e.comment," ")}}function Wt(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",15),o["\u0275\u0275template"](1,Kt,7,3,"div",16),o["\u0275\u0275template"](2,Yt,10,4,"div",17),o["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",e.commentor_oid==n.currentUser.oid),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",e.commentor_oid!=n.currentUser.oid)}}function Gt(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div"),o["\u0275\u0275template"](1,Wt,3,2,"div",14),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngForOf",e.comments)}}function Xt(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"div",15),o["\u0275\u0275elementStart"](1,"div",29),o["\u0275\u0275element"](2,"img",30),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]())}const Jt=function(e){return{height:e}},Qt=function(e){return{"min-height":e}};let Zt=(()=>{class e{constructor(e){this.loginService=e,this.comments=[],this.sendComments=new o.EventEmitter}ngOnInit(){console.log("Hello",this.comments),this.currentUser=this.loginService.getProfile().profile}getDateFormat(e){return G(e).format("LLL")}enterComment(e,t){let n=this.loginService.getProfile().profile;console.log(n),this.comments.push({sequence_number:t,time:G(new Date).format("LLL"),commentor_oid:n.oid,commentor_name:n.name,comment:document.getElementById("comment").value});let i={sequence_number:t,time:new Date,commentor_oid:n.oid,commentor_name:n.name,comment:document.getElementById("comment").value};this.sendComments.emit(i),document.getElementById("comment").value=""}onKeydown(e){"Enter"===e.key&&this.enterComment(void 0,this.comments.length+1)}getCommentBoxHeight(){return this.commentBoxHeight?this.commentBoxHeight:"70vh"}getCommentScrollHeight(){return this.commentBoxScrollHeight?this.commentBoxScrollHeight:"93%"}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](S.a))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-chat-comment"]],inputs:{comments:"comments",commentBoxHeight:"commentBoxHeight",commentBoxScrollHeight:"commentBoxScrollHeight"},outputs:{sendComments:"sendComments"},decls:15,vars:9,consts:[[1,"container-fluid","d-flex","flex-column","p-0",3,"ngStyle"],["id","scrollDiv",1,"overflow-scroll",3,"ngStyle"],[4,"ngIf"],["class","row mt-3","style","min-width: 90%",4,"ngIf"],[1,"row","mx-0","mt-auto","h-40","pt-2",2,"border-top","1px solid #c1bfbf"],[1,"col-1",2,"padding-top","9px"],["imgWidth","30px","imgHeight","30px",1,"my-auto","pr-1",3,"id"],[1,"col-10","pl-3","pr-2"],[1,"comment-box"],["matInput","","placeholder","comments","id","comment",3,"keydown"],["comment",""],[1,"col-1"],["mat-icon-button","",1,"my-auto","send-button",3,"click"],[2,"color","white","font-size","22px"],["class","row mt-3","style","min-width: 90%",4,"ngFor","ngForOf"],[1,"row","mt-3",2,"min-width","90%"],["class","col-12 pl-1 d-flex",4,"ngIf"],["class","col-12 pl-2  d-flex",4,"ngIf"],[1,"col-12","pl-1","d-flex"],[1,"chat-outgoing"],[1,"row",2,"height","21px !important"],[1,"comment-date","ml-auto"],["imgWidth","28px","imgHeight","28px",1,"my-auto","pr-1","ml-1",3,"id"],[1,"col-12","pl-2","d-flex"],["imgWidth","28px","imgHeight","28px",1,"my-auto","pr-1",3,"id"],[1,"chat-incoming"],[1,"comment-user-name"],[1,"comment-date"],[1,"row"],[2,"margin","auto"],["src","https://assets.kebs.app/images/nocommends.png","height","150","width","190"]],template:function(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"div",0),o["\u0275\u0275elementStart"](1,"div",1),o["\u0275\u0275template"](2,Gt,2,1,"div",2),o["\u0275\u0275template"](3,Xt,3,0,"div",3),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](4,"div",4),o["\u0275\u0275elementStart"](5,"div",5),o["\u0275\u0275element"](6,"app-user-image",6),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](7,"div",7),o["\u0275\u0275elementStart"](8,"mat-form-field",8),o["\u0275\u0275elementStart"](9,"input",9,10),o["\u0275\u0275listener"]("keydown",(function(e){return t.onKeydown(e)})),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](11,"div",11),o["\u0275\u0275elementStart"](12,"button",12),o["\u0275\u0275listener"]("click",(function(e){return t.enterComment(e.target.value,t.comments.length+1)})),o["\u0275\u0275elementStart"](13,"mat-icon",13),o["\u0275\u0275text"](14,"send"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e&&(o["\u0275\u0275property"]("ngStyle",o["\u0275\u0275pureFunction1"](5,Jt,t.getCommentBoxHeight())),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngStyle",o["\u0275\u0275pureFunction1"](7,Qt,t.getCommentScrollHeight())),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",0!=t.comments.length),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",0==t.comments.length),o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("id",t.currentUser.oid))},directives:[i.NgStyle,i.NgIf,j,je.c,Be.b,_.a,E.a,i.NgForOf],styles:[".comment-row[_ngcontent-%COMP%]{height:70vh}.footer-bar[_ngcontent-%COMP%]{position:absolute;bottom:0!important}.comment-box[_ngcontent-%COMP%]{width:100%;font-size:14px}.comment-box[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{height:30px;padding-bottom:9px}.comment-box[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding-bottom:0!important}.comment-date[_ngcontent-%COMP%]{font-size:11px;color:#959696}.comment-user-name[_ngcontent-%COMP%]{padding-right:10px;font-size:11px;font-weight:400;color:#6b6969}.comment-user-name-left[_ngcontent-%COMP%]{padding-left:20px;font-size:11px;font-weight:400;color:#252424}.chat-incoming[_ngcontent-%COMP%]{padding:5px 50px 5px 22px;text-align:left}.chat-outgoing[_ngcontent-%COMP%]{margin-left:auto!important;padding:5px 22px}.chat-incoming[_ngcontent-%COMP%], .chat-outgoing[_ngcontent-%COMP%]{background:#ececec;display:inline-block;font-size:14px;color:#454546;border-radius:7px;max-width:90%;text-align:right;position:relative}.chat-incoming[_ngcontent-%COMP%]{margin-right:auto!important;padding:5px 15px}.smallCardIcon[_ngcontent-%COMP%]{font-size:18px!important;color:#66615b!important}.send-button[_ngcontent-%COMP%]{width:35px;height:35px;line-height:34px;background-color:#cf0001;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}"]}),e})();function en(e,t){1&e&&o["\u0275\u0275element"](0,"app-shimmer")}const tn=function(e){return{"background-color":e}};function nn(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"span",11),o["\u0275\u0275elementStart"](1,"button",12),o["\u0275\u0275elementStart"](2,"mat-icon"),o["\u0275\u0275text"](3),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngStyle",o["\u0275\u0275pureFunction1"](2,tn,e.activityDetail?e.activityDetail.activity_type_bg:"black")),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate"](e.activityDetail.activity_mat_icon)}}function an(e,t){1&e&&o["\u0275\u0275element"](0,"app-shimmer")}function on(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"span"),o["\u0275\u0275text"](1),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate"](e.activityDetail.title?e.activityDetail.title:"")}}function ln(e,t){1&e&&o["\u0275\u0275element"](0,"app-shimmer")}function rn(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"span"),o["\u0275\u0275text"](1),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate"](e.activityDetail.created_on?e.activityDetail.created_on:"")}}function sn(e,t){1&e&&o["\u0275\u0275element"](0,"app-shimmer")}function dn(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"span"),o["\u0275\u0275text"](1),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" ",e.activityDetail.short_desc?e.activityDetail.short_desc:"","")}}const cn=function(e,t){return{"border-left-color":e,"background-color":t}};let pn=(()=>{class e{constructor(e,t,n){this.leadService=e,this._route=t,this.router=n}ngOnInit(){console.log(this.scrollIndex),this.leadService.activityDetails(this.activityNo).then(e=>{console.log(e),this.activityDetail=e[0],this.activityId=this.activityDetail.activity_id},e=>{console.error(e)})}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](u.a),o["\u0275\u0275directiveInject"](a.a),o["\u0275\u0275directiveInject"](a.g))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-mini-card"]],inputs:{activityNo:"activityNo",scrollIndex:"scrollIndex"},decls:17,vars:12,consts:[[1,"card","listcard",3,"ngStyle"],[1,"row"],[1,"col-2","pt-3","pl-2","pr-2"],[4,"ngIf"],["class","primary",4,"ngIf"],[1,"col-10","pl-0","pr-1"],[1,"row","p-0","pt-3"],[1,"col-8","pl-0","pr-1","name"],[1,"col-4","p-0","normal"],[1,"row","p-0","pt-2"],[1,"col-12","p-0","normal"],[1,"primary"],["mat-icon-button","",1,"activity-button",3,"ngStyle"]],template:function(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"div",0),o["\u0275\u0275elementStart"](1,"div",1),o["\u0275\u0275elementStart"](2,"div",2),o["\u0275\u0275template"](3,en,1,0,"app-shimmer",3),o["\u0275\u0275template"](4,nn,4,4,"span",4),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](5,"div",5),o["\u0275\u0275elementStart"](6,"div",6),o["\u0275\u0275elementStart"](7,"div",7),o["\u0275\u0275template"](8,an,1,0,"app-shimmer",3),o["\u0275\u0275template"](9,on,2,1,"span",3),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](10,"div",8),o["\u0275\u0275template"](11,ln,1,0,"app-shimmer",3),o["\u0275\u0275template"](12,rn,2,1,"span",3),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](13,"div",9),o["\u0275\u0275elementStart"](14,"div",10),o["\u0275\u0275template"](15,sn,1,0,"app-shimmer",3),o["\u0275\u0275template"](16,dn,2,1,"span",3),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e&&(o["\u0275\u0275property"]("ngStyle",o["\u0275\u0275pureFunction2"](9,cn,t.activityDetail?t.activityDetail.activity_type_bg:"white",1==t.scrollIndex?"#e9ecef":"")),o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("ngIf",null==t.activityDetail),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",t.activityDetail),o["\u0275\u0275advance"](4),o["\u0275\u0275property"]("ngIf",null==t.activityDetail),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",t.activityDetail),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngIf",null==t.activityDetail),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",t.activityDetail),o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("ngIf",null==t.activityDetail),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",t.activityDetail))},directives:[i.NgStyle,i.NgIf,Ge,_.a,E.a],styles:[".listcard[_ngcontent-%COMP%]{min-height:5rem;padding-top:3px;border-left-width:3px!important;transition:all .25s linear;animation:slide-in-left .5s cubic-bezier(.25,.46,.45,.94) both}.listcard[_ngcontent-%COMP%]:hover{box-shadow:0 4px 8px 0 rgba(0,0,0,.2),0 6px 20px 0 rgba(0,0,0,.19);cursor:pointer}.name[_ngcontent-%COMP%]{color:#cf0001;font-family:Roboto;font-weight:500}.name[_ngcontent-%COMP%], .normal[_ngcontent-%COMP%]{font-size:14px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.activity-button[_ngcontent-%COMP%]{color:#fff;line-height:24px!important;width:28px!important;height:28px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.activity-button[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%]{font-size:18px}@keyframes slide-in-left{0%{transform:translateX(-30px);opacity:0}to{transform:translateX(0);opacity:1}}"]}),e})();var mn=n("bv9b");let un=(()=>{class e{constructor(){}ngOnInit(){this.pplInvolved=JSON.parse(this.details.people_involved),console.log(this.pplInvolved)}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-call-details"]],inputs:{details:"details"},decls:62,vars:10,consts:[[1,"row","pt-3"],[1,"col-12","p-0","open"],[1,"row","p-0"],[1,"col-1","pt-3","pl-3"],["mat-icon-button","",1,"activity-button"],[1,"col-11","p-0","pt-2","pb-2"],[1,"row"],[1,"col-7","name","pl-0"],[1,"col-5"],[1,"col-4","pl-0","pr-0","key"],[1,"col-8","value"],[1,"col-7","value","pl-0"],[1,"col-7","p-0"],[1,"col-4","pt-1","pr-0","pl-0","key"],[1,"col-8","value","pl-0"],[3,"type","oid"],[1,"col-4","pt-1","pl-0","pr-0","key"],[1,"col-8","pl-0"],[1,"col-12","value","pl-0"],[1,"row","pt-3","pb-2"],[1,"col-7"]],template:function(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"div",0),o["\u0275\u0275elementStart"](1,"div",1),o["\u0275\u0275elementStart"](2,"div",2),o["\u0275\u0275elementStart"](3,"div",3),o["\u0275\u0275elementStart"](4,"button",4),o["\u0275\u0275elementStart"](5,"mat-icon"),o["\u0275\u0275text"](6,"call"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](7,"div",5),o["\u0275\u0275elementStart"](8,"div",6),o["\u0275\u0275elementStart"](9,"div",7),o["\u0275\u0275text"](10),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](11,"div",8),o["\u0275\u0275elementStart"](12,"div",2),o["\u0275\u0275elementStart"](13,"div",9),o["\u0275\u0275text"](14,"Type"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](15,"div",10),o["\u0275\u0275text"](16,"Call"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](17,"div",0),o["\u0275\u0275elementStart"](18,"div",11),o["\u0275\u0275text"](19),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](20,"div",8),o["\u0275\u0275elementStart"](21,"div",2),o["\u0275\u0275elementStart"](22,"div",9),o["\u0275\u0275text"](23,"Subtype"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](24,"div",10),o["\u0275\u0275text"](25),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](26,"div",0),o["\u0275\u0275elementStart"](27,"div",12),o["\u0275\u0275elementStart"](28,"div",2),o["\u0275\u0275elementStart"](29,"div",13),o["\u0275\u0275text"](30,"Person contacted"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](31,"div",14),o["\u0275\u0275element"](32,"app-user-profile",15),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](33,"div",8),o["\u0275\u0275elementStart"](34,"div",2),o["\u0275\u0275elementStart"](35,"div",16),o["\u0275\u0275text"](36,"Response type"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](37,"div",10),o["\u0275\u0275text"](38),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](39,"div",0),o["\u0275\u0275elementStart"](40,"div",12),o["\u0275\u0275elementStart"](41,"div",2),o["\u0275\u0275elementStart"](42,"div",16),o["\u0275\u0275text"](43,"Contacted by"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](44,"div",17),o["\u0275\u0275elementStart"](45,"div",2),o["\u0275\u0275elementStart"](46,"div",18),o["\u0275\u0275element"](47,"app-user-profile",15),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](48,"div",8),o["\u0275\u0275elementStart"](49,"div",2),o["\u0275\u0275elementStart"](50,"div",16),o["\u0275\u0275text"](51,"Status"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](52,"div",10),o["\u0275\u0275text"](53),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](54,"div",19),o["\u0275\u0275element"](55,"div",20),o["\u0275\u0275elementStart"](56,"div",8),o["\u0275\u0275elementStart"](57,"div",6),o["\u0275\u0275elementStart"](58,"div",16),o["\u0275\u0275text"](59,"Completed on"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](60,"div",10),o["\u0275\u0275text"](61),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e&&(o["\u0275\u0275advance"](10),o["\u0275\u0275textInterpolate"](t.details?t.details.title:""),o["\u0275\u0275advance"](9),o["\u0275\u0275textInterpolate"](t.details?t.details.created_on:""),o["\u0275\u0275advance"](6),o["\u0275\u0275textInterpolate"](t.details?t.details.activity_sub_type_name:""),o["\u0275\u0275advance"](7),o["\u0275\u0275property"]("type","mini")("oid",t.pplInvolved),o["\u0275\u0275advance"](6),o["\u0275\u0275textInterpolate"](t.details?t.details.response_type_name:""),o["\u0275\u0275advance"](9),o["\u0275\u0275property"]("type","mini")("oid",t.details?t.details.organizer_oid:""),o["\u0275\u0275advance"](6),o["\u0275\u0275textInterpolate"](1==t.details.is_completed?"Completed":"Open"),o["\u0275\u0275advance"](8),o["\u0275\u0275textInterpolate"](t.details.end_date?t.details.end_date:""))},directives:[_.a,E.a,Y],styles:[".open[_ngcontent-%COMP%]{background-color:hsla(0,0%,92.5%,.30196078431372547)!important}.inprogress[_ngcontent-%COMP%], .open[_ngcontent-%COMP%]{box-shadow:0 3px 6px hsla(0,0%,41.2%,.19),0 1px 4px hsla(0,0%,41.2%,.19)}.inprogress[_ngcontent-%COMP%]{background-color:rgba(255,233,76,.*****************)!important}.completed[_ngcontent-%COMP%]{background-color:rgba(233,255,240,.5803921568627451)!important;box-shadow:0 3px 6px hsla(0,0%,41.2%,.19),0 1px 4px hsla(0,0%,41.2%,.19)}.name[_ngcontent-%COMP%]{font-size:14px;color:#cf0001;font-family:Roboto;font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;font-size:14px!important}.activity-button[_ngcontent-%COMP%]{color:#fff;line-height:24px!important;background-color:#227093;width:30px!important;height:30px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.activity-button[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%]{font-size:18px}.key[_ngcontent-%COMP%]{font-size:13px;color:#7b7b7a}.value[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;font-size:14px;font-weight:400}"]}),e})();function hn(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",23),o["\u0275\u0275elementStart"](1,"div",21),o["\u0275\u0275element"](2,"app-user-profile",22),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("type","mini")("oid",e)}}let vn=(()=>{class e{constructor(){}ngOnInit(){console.log(this.details),this.pplInvolved=JSON.parse(this.details.people_involved)}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-meeting-details"]],inputs:{details:"details"},decls:73,vars:10,consts:[[1,"row","pt-3","meeting-styles"],[1,"col-12","p-0","open"],[1,"row","p-0"],[1,"col-1","pt-3","pl-3"],["mat-icon-button","",1,"activity-button"],[1,"col-11","p-0","pt-2","pb-2"],[1,"row"],[1,"col-7","name","pl-0"],[1,"col-5"],[1,"col-4","pl-0","pr-0","key"],[1,"col-8","value"],[1,"row","pt-3"],[1,"col-7","value","pl-0"],[1,"col-3","pt-1","pl-0","pr-0","key"],[1,"col-8","pl-0"],[1,"col-12","value","pt-1","pl-0"],[1,"col-7","p-0"],[1,"col-3","pt-1","pr-0","pl-0","key"],[1,"col-8","value","pl-0"],["class","row pl-0 pr-0 pt-0 pb-2",4,"ngFor","ngForOf"],[1,"col-4","pt-1","pl-0","pr-0","key"],[1,"col-12","value","pl-0"],[3,"type","oid"],[1,"row","pl-0","pr-0","pt-0","pb-2"],[1,"col-8","pt-1","value"],[1,"row","pt-3","pb-2"],[1,"col-7","pl-0"],[1,"col-8","pt-1","pl-0","value"]],template:function(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"div",0),o["\u0275\u0275elementStart"](1,"div",1),o["\u0275\u0275elementStart"](2,"div",2),o["\u0275\u0275elementStart"](3,"div",3),o["\u0275\u0275elementStart"](4,"button",4),o["\u0275\u0275elementStart"](5,"mat-icon"),o["\u0275\u0275text"](6,"date_range"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](7,"div",5),o["\u0275\u0275elementStart"](8,"div",6),o["\u0275\u0275elementStart"](9,"div",7),o["\u0275\u0275text"](10),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](11,"div",8),o["\u0275\u0275elementStart"](12,"div",2),o["\u0275\u0275elementStart"](13,"div",9),o["\u0275\u0275text"](14,"Type"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](15,"div",10),o["\u0275\u0275text"](16,"Meeting"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](17,"div",11),o["\u0275\u0275elementStart"](18,"div",12),o["\u0275\u0275elementStart"](19,"div",2),o["\u0275\u0275elementStart"](20,"div",13),o["\u0275\u0275text"](21,"Description"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](22,"div",14),o["\u0275\u0275elementStart"](23,"div",2),o["\u0275\u0275elementStart"](24,"div",15),o["\u0275\u0275text"](25),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](26,"div",8),o["\u0275\u0275elementStart"](27,"div",2),o["\u0275\u0275elementStart"](28,"div",9),o["\u0275\u0275text"](29,"Subtype"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](30,"div",10),o["\u0275\u0275text"](31),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](32,"div",11),o["\u0275\u0275elementStart"](33,"div",16),o["\u0275\u0275elementStart"](34,"div",2),o["\u0275\u0275elementStart"](35,"div",17),o["\u0275\u0275text"](36,"People"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](37,"div",18),o["\u0275\u0275template"](38,hn,3,2,"div",19),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](39,"div",8),o["\u0275\u0275elementStart"](40,"div",2),o["\u0275\u0275elementStart"](41,"div",20),o["\u0275\u0275text"](42,"Status"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](43,"div",10),o["\u0275\u0275text"](44),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](45,"div",11),o["\u0275\u0275elementStart"](46,"div",16),o["\u0275\u0275elementStart"](47,"div",2),o["\u0275\u0275elementStart"](48,"div",13),o["\u0275\u0275text"](49,"Organizer"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](50,"div",14),o["\u0275\u0275elementStart"](51,"div",2),o["\u0275\u0275elementStart"](52,"div",21),o["\u0275\u0275element"](53,"app-user-profile",22),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](54,"div",8),o["\u0275\u0275elementStart"](55,"div",23),o["\u0275\u0275elementStart"](56,"div",20),o["\u0275\u0275text"](57,"Created On"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](58,"div",24),o["\u0275\u0275text"](59),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](60,"div",25),o["\u0275\u0275elementStart"](61,"div",26),o["\u0275\u0275elementStart"](62,"div",2),o["\u0275\u0275elementStart"](63,"div",13),o["\u0275\u0275text"](64,"Due On"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](65,"div",27),o["\u0275\u0275text"](66),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](67,"div",8),o["\u0275\u0275elementStart"](68,"div",6),o["\u0275\u0275elementStart"](69,"div",20),o["\u0275\u0275text"](70,"Completed on"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](71,"div",24),o["\u0275\u0275text"](72),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e&&(o["\u0275\u0275advance"](10),o["\u0275\u0275textInterpolate"](t.details?t.details.title:""),o["\u0275\u0275advance"](15),o["\u0275\u0275textInterpolate1"](" ",t.details?t.details.description:""," "),o["\u0275\u0275advance"](6),o["\u0275\u0275textInterpolate"](t.details?t.details.activity_sub_type_name:""),o["\u0275\u0275advance"](7),o["\u0275\u0275property"]("ngForOf",t.pplInvolved),o["\u0275\u0275advance"](6),o["\u0275\u0275textInterpolate"](1==t.details.is_completed?"Completed":"Open"),o["\u0275\u0275advance"](9),o["\u0275\u0275property"]("type","mini")("oid",t.details?t.details.organizer_oid:""),o["\u0275\u0275advance"](6),o["\u0275\u0275textInterpolate1"](" ",t.details?t.details.created_on:""," "),o["\u0275\u0275advance"](7),o["\u0275\u0275textInterpolate1"](" ",t.details?t.details.task_due_date:""," "),o["\u0275\u0275advance"](6),o["\u0275\u0275textInterpolate1"](" ",t.details?t.details.end_date:"",""))},directives:[_.a,E.a,i.NgForOf,Y],styles:[".meeting-styles[_ngcontent-%COMP%]   .open[_ngcontent-%COMP%]{background-color:hsla(0,0%,92.5%,.30196078431372547)!important}.meeting-styles[_ngcontent-%COMP%]   .inprogress[_ngcontent-%COMP%], .meeting-styles[_ngcontent-%COMP%]   .open[_ngcontent-%COMP%]{box-shadow:0 3px 6px hsla(0,0%,41.2%,.19),0 1px 4px hsla(0,0%,41.2%,.19)}.meeting-styles[_ngcontent-%COMP%]   .inprogress[_ngcontent-%COMP%]{background-color:rgba(255,233,76,.*****************)!important}.meeting-styles[_ngcontent-%COMP%]   .completed[_ngcontent-%COMP%]{background-color:rgba(233,255,240,.5803921568627451)!important;box-shadow:0 3px 6px hsla(0,0%,41.2%,.19),0 1px 4px hsla(0,0%,41.2%,.19)}.meeting-styles[_ngcontent-%COMP%]   .name[_ngcontent-%COMP%]{font-size:14px;color:#cf0001;font-family:Roboto;font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;font-size:14px!important}.meeting-styles[_ngcontent-%COMP%]   .activity-button[_ngcontent-%COMP%]{color:#fff;line-height:24px!important;background-color:#b71540;width:30px!important;height:30px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.meeting-styles[_ngcontent-%COMP%]   .activity-button[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%]{font-size:18px}.meeting-styles[_ngcontent-%COMP%]   .key[_ngcontent-%COMP%]{font-size:13px;color:#7b7b7a}.meeting-styles[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;font-size:14px;font-weight:400}"]}),e})(),gn=(()=>{class e{constructor(){}ngOnInit(){console.log(this.details)}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-mail-detail"]],inputs:{details:"details"},decls:26,vars:4,consts:[[1,"row","pt-3","mail-styles"],[1,"col-12","p-0","open"],[1,"row","p-0"],[1,"col-1","pt-3","pl-3"],["mat-icon-button","",1,"activity-button"],[1,"col-11","p-0","pt-2","pb-2"],[1,"row"],[1,"col-7","name","pl-0"],[1,"col-5"],[1,"col-4","pl-0","pr-0","key"],[1,"col-8","value"],[1,"row","pt-3"],[1,"col-7","value","pl-0"],[3,"type","oid"]],template:function(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"div",0),o["\u0275\u0275elementStart"](1,"div",1),o["\u0275\u0275elementStart"](2,"div",2),o["\u0275\u0275elementStart"](3,"div",3),o["\u0275\u0275elementStart"](4,"button",4),o["\u0275\u0275elementStart"](5,"mat-icon"),o["\u0275\u0275text"](6,"mail"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](7,"div",5),o["\u0275\u0275elementStart"](8,"div",6),o["\u0275\u0275elementStart"](9,"div",7),o["\u0275\u0275text"](10),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](11,"div",8),o["\u0275\u0275elementStart"](12,"div",2),o["\u0275\u0275elementStart"](13,"div",9),o["\u0275\u0275text"](14,"Type"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](15,"div",10),o["\u0275\u0275text"](16,"Mail"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](17,"div",11),o["\u0275\u0275elementStart"](18,"div",12),o["\u0275\u0275text"](19),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](20,"div",8),o["\u0275\u0275elementStart"](21,"div",2),o["\u0275\u0275elementStart"](22,"div",9),o["\u0275\u0275text"](23,"Created by"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](24,"div",10),o["\u0275\u0275element"](25,"app-user-profile",13),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e&&(o["\u0275\u0275advance"](10),o["\u0275\u0275textInterpolate"](t.details?t.details.title:""),o["\u0275\u0275advance"](9),o["\u0275\u0275textInterpolate"](t.details?t.details.created_on:""),o["\u0275\u0275advance"](6),o["\u0275\u0275property"]("type","mini")("oid",t.details?t.details.created_by:""))},directives:[_.a,E.a,Y],styles:[".mail-styles[_ngcontent-%COMP%]   .open[_ngcontent-%COMP%]{background-color:hsla(0,0%,92.5%,.30196078431372547)!important}.mail-styles[_ngcontent-%COMP%]   .inprogress[_ngcontent-%COMP%], .mail-styles[_ngcontent-%COMP%]   .open[_ngcontent-%COMP%]{box-shadow:0 3px 6px hsla(0,0%,41.2%,.19),0 1px 4px hsla(0,0%,41.2%,.19)}.mail-styles[_ngcontent-%COMP%]   .inprogress[_ngcontent-%COMP%]{background-color:rgba(255,233,76,.*****************)!important}.mail-styles[_ngcontent-%COMP%]   .completed[_ngcontent-%COMP%]{background-color:rgba(233,255,240,.5803921568627451)!important;box-shadow:0 3px 6px hsla(0,0%,41.2%,.19),0 1px 4px hsla(0,0%,41.2%,.19)}.mail-styles[_ngcontent-%COMP%]   .name[_ngcontent-%COMP%]{font-size:14px;color:#cf0001;font-family:Roboto;font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;font-size:14px!important}.mail-styles[_ngcontent-%COMP%]   .activity-button[_ngcontent-%COMP%]{color:#fff;line-height:24px!important;background-color:#006266;width:30px!important;height:30px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.mail-styles[_ngcontent-%COMP%]   .activity-button[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%]{font-size:18px}.mail-styles[_ngcontent-%COMP%]   .key[_ngcontent-%COMP%]{font-size:13px;color:#7b7b7a}.mail-styles[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;font-size:14px;font-weight:400}"]}),e})();var fn=n("jtHE"),yn=n("XNiG"),xn=n("NJ67"),Sn=n("1G5W"),bn=n("d3UM"),_n=n("FKr1");const Cn=["singleSelect"];function wn(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"mat-option",6),o["\u0275\u0275text"](1),o["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;o["\u0275\u0275property"]("value",e.id),o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" ",e.name," ")}}function En(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div"),o["\u0275\u0275elementStart"](1,"mat-form-field",2),o["\u0275\u0275elementStart"](2,"mat-label"),o["\u0275\u0275text"](3),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](4,"mat-select",3,4),o["\u0275\u0275listener"]("openedChange",(function(t){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"]().onOpenedChange(t)}))("blur",(function(t){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"]().onBlur(t)}))("keyup",(function(t){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"]().looseFocus(t)}))("selectionChange",(function(t){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"]().onSelectionChange(t)})),o["\u0275\u0275elementStart"](6,"mat-option"),o["\u0275\u0275element"](7,"ngx-mat-select-search",5),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](8,"mat-option",6),o["\u0275\u0275text"](9,"None"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](10,wn,2,2,"mat-option",7),o["\u0275\u0275pipe"](11,"async"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](3),o["\u0275\u0275textInterpolate"](e.placeholder),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("formControl",e.fieldCtrl)("placeholder",e.placeholder)("required",e.required)("disabled",e.disabled),o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("formControl",e.fieldFilterCtrl)("placeholderLabel",e.placeholder),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("value",null),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngForOf",o["\u0275\u0275pipeBind1"](11,9,e.filteredList))}}function In(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div",8),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](e);const t=o["\u0275\u0275nextContext"]();return t.edit(t.value)}))("focus",(function(){o["\u0275\u0275restoreView"](e);const t=o["\u0275\u0275nextContext"]();return t.edit(t.value)})),o["\u0275\u0275text"](1),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" ",e.label," ")}}let kn=(()=>{class e extends xn.a{constructor(){super(),this.fieldCtrl=new Me.j,this.fieldFilterCtrl=new Me.j,this.list=[],this.required=!1,this.valueChange=new o.EventEmitter,this.disabled=!1,this.filteredList=new fn.a,this.label="",this._onDestroy=new yn.b,this.isSelectOpened=!1,this.onChange=()=>{},this.onTouch=()=>{},this.val=""}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(Sn.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(Sn.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){this.editing=!1,this.filteredList.next(this.list.slice()),this.label=this.initLabel()}ngAfterViewInit(){this.label=this.initLabel()}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e),this.onChange(e),this.onTouch(e),this.fieldCtrl.valueChanges.subscribe(e=>{this.label=this.initLabel()})}onBlur(e){this.isSelectOpened||(this.editing=!1)}looseFocus(e){13==e.keyCode&&document.getElementById("inlineEditControl")&&document.getElementById("inlineEditControl").blur()}onOpenedChange(e){this.isSelectOpened=e}edit(e){this.disabled||(this.preValue=e,this.editing=!0,setTimeout(e=>{document.getElementById("inlineEditControl")&&document.getElementById("inlineEditControl").focus()}))}registerOnChange(e){this.onChange=e}registerOnTouched(e){this.onTouch=e}onSelectionChange(e){this.fieldCtrl.setValue(e.value),this.label=this.initLabel()}initLabel(){if(this.fieldCtrl.value&&this.list&&this.list.length>0){let e=c.findWhere(this.list,{id:this.fieldCtrl.value});return e?e.name:""}return this.placeholder}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-inline-input-search"]],viewQuery:function(e,t){if(1&e&&o["\u0275\u0275viewQuery"](Cn,!0),2&e){let e;o["\u0275\u0275queryRefresh"](e=o["\u0275\u0275loadQuery"]())&&(t.singleSelect=e.first)}},inputs:{list:"list",placeholder:"placeholder",required:"required",disabled:"disabled"},outputs:{valueChange:"valueChange"},features:[o["\u0275\u0275ProvidersFeature"]([{provide:Me.t,useExisting:Object(o.forwardRef)(()=>e),multi:!0}]),o["\u0275\u0275InheritDefinitionFeature"],o["\u0275\u0275NgOnChangesFeature"]],decls:2,vars:2,consts:[[4,"ngIf"],["class","value-field",3,"click","focus",4,"ngIf"],["appearance","outline"],["id","inlineEditControl",3,"formControl","placeholder","required","disabled","openedChange","blur","keyup","selectionChange"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value"],[3,"value",4,"ngFor","ngForOf"],[1,"value-field",3,"click","focus"]],template:function(e,t){1&e&&(o["\u0275\u0275template"](0,En,12,11,"div",0),o["\u0275\u0275template"](1,In,2,1,"div",1)),2&e&&(o["\u0275\u0275property"]("ngIf",t.editing),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",!t.editing))},directives:[i.NgIf,je.c,je.g,bn.c,Me.v,Me.k,Me.F,_n.p,i.NgForOf],pipes:[i.AsyncPipe],styles:["input[_ngcontent-%COMP%]{font-size:14px!important;padding:3px!important}.value-field[_ngcontent-%COMP%]{padding:4px!important}.value-field[_ngcontent-%COMP%]:hover{outline:1px solid #a5a4a2!important;height:21px}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding:0!important;margin:0!important}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper   .mat-form-field-flex{padding:0!important}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper   .mat-form-field-infix{padding:6px!important;margin:auto!important;border-top-width:0!important}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper   .mat-form-field-outline{top:0!important;line-height:1.7!important}"]}),e})();n("DlyV"),n("4PPT"),n("sx9y");var On=n("/1cH");function Dn(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"mat-option",9),o["\u0275\u0275elementStart"](1,"div",10),o["\u0275\u0275element"](2,"div"),o["\u0275\u0275element"](3,"div"),o["\u0275\u0275element"](4,"div"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]())}function Mn(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"mat-option",12),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](e);const n=t.$implicit;return o["\u0275\u0275nextContext"](3).anyOptionClicked(n.id)})),o["\u0275\u0275elementStart"](1,"span"),o["\u0275\u0275elementStart"](2,"small",13),o["\u0275\u0275text"](3),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;o["\u0275\u0275advance"](3),o["\u0275\u0275textInterpolate2"]("",e.displayName," | ",e.officeLocation,"")}}function Pn(e,t){if(1&e&&(o["\u0275\u0275elementContainerStart"](0),o["\u0275\u0275template"](1,Mn,4,2,"mat-option",11),o["\u0275\u0275elementContainerEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngForOf",e.searchResult)}}function Tn(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div",2),o["\u0275\u0275listener"]("focusout",(function(){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"]().focusOut()})),o["\u0275\u0275elementStart"](1,"mat-form-field",3),o["\u0275\u0275elementStart"](2,"mat-label"),o["\u0275\u0275text"](3),o["\u0275\u0275elementEnd"](),o["\u0275\u0275element"](4,"input",4),o["\u0275\u0275elementStart"](5,"mat-autocomplete",5,6),o["\u0275\u0275template"](7,Dn,5,0,"mat-option",7),o["\u0275\u0275template"](8,Pn,2,1,"ng-container",8),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275reference"](6),t=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](3),o["\u0275\u0275textInterpolate"](t.label),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("matAutocomplete",e)("formControl",t.searchTextControl),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("displayWith",t.displayFn),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngIf",t.isLoading),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",0==t.isLoading)}}function An(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div",14),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](e);const t=o["\u0275\u0275nextContext"]();return t.edit(t.value)}))("focus",(function(){o["\u0275\u0275restoreView"](e);const t=o["\u0275\u0275nextContext"]();return t.edit(t.value)})),o["\u0275\u0275element"](1,"app-user-profile",15),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("type","mini")("oid",e.searchTextControl.value)}}let Fn=(()=>{class e{constructor(e){this.searchService=e,this.searchTextControl=new Me.j,this.editing=!1,this.isLoading=!1,this.onChange=()=>{},this.onTouch=()=>{},this.val=""}ngOnInit(){this.searchResultSubscription=this.searchTextControl.valueChanges.debounceTime(1e3).subscribe(e=>{e&&e.length>0&&(this.isLoading=!0,this.searchService.getUserSuggestions(e).then(e=>{console.log(e),this.searchResult=e,this.isLoading=!1}))})}focusOut(){console.log("focussing out"),""==this.searchTextControl.value&&(this.editing=!1,this.searchTextControl.patchValue(this.temp))}anyOptionClicked(e){console.log("any option clicked"),this.editing=!1,this.temp=e,this.searchTextControl.patchValue(e),this.onChange(e),this.onTouch(e),console.log(this.searchTextControl.value)}edit(){this.editing=!0,this.searchTextControl.patchValue(""),setTimeout(e=>{document.getElementById("textbox")&&document.getElementById("textbox").focus()})}resetSuggestion(){this.searchTextControl.patchValue("")}writeValue(e){this.temp=e,this.searchTextControl.setValue(e),this.onChange(e),this.onTouch(e)}registerOnChange(e){this.onChange=e}registerOnTouched(e){this.onTouch=e}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](b.a))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-inline-search-user"]],features:[o["\u0275\u0275ProvidersFeature"]([{provide:Me.t,multi:!0,useExisting:Object(o.forwardRef)(()=>e)}])],decls:3,vars:2,consts:[[3,"focusout",4,"ngIf"],["class","value-field",3,"click","focus",4,"ngIf"],[3,"focusout"],["appearance","outline"],["id","textbox","type","text","matInput","","placeholder","Search","aria-label","State",3,"matAutocomplete","formControl"],[3,"displayWith"],["auto","matAutocomplete"],["class","is-loading",4,"ngIf"],[4,"ngIf"],[1,"is-loading"],[1,"lds-facebook"],[3,"click",4,"ngFor","ngForOf"],[3,"click"],[2,"padding-left","12px"],[1,"value-field",3,"click","focus"],[3,"type","oid"]],template:function(e,t){1&e&&(o["\u0275\u0275template"](0,Tn,9,6,"div",0),o["\u0275\u0275element"](1,"div"),o["\u0275\u0275template"](2,An,2,2,"div",1)),2&e&&(o["\u0275\u0275property"]("ngIf",t.editing),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngIf",!t.editing))},directives:[i.NgIf,je.c,je.g,Be.b,On.d,Me.e,Me.v,Me.k,On.b,_n.p,i.NgForOf,Y],styles:["input[_ngcontent-%COMP%]{font-size:14px!important;padding:3px!important}.custom-ts-search[_ngcontent-%COMP%]{width:100%;text-align:center!important;font-size:14px;color:#1a1a1a}.custom-ts-search[_ngcontent-%COMP%]   .mat-form-field-wrapper[_ngcontent-%COMP%]   .mat-form-field-flex[_ngcontent-%COMP%]{padding:5px!important}.value-field[_ngcontent-%COMP%]{padding:4px!important}.value-field[_ngcontent-%COMP%]:hover{outline:1px solid #a5a4a2!important}.mat-form-field[_ngcontent-%COMP%]{width:100%}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding:4px!important}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-underline{bottom:0}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-infix{padding:.65em 0!important;border-top:.54375em solid transparent!important;font-size:13px}.mat-form-field[_ngcontent-%COMP%]     .mat-select-arrow{margin:0 4px!important}.lds-facebook[_ngcontent-%COMP%]{display:inline-block;position:sticky;width:80px;height:24px;margin-left:110px}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{display:inline-block;position:absolute;left:4px;width:5px;background:#cf0001;animation:lds-facebook 1.2s cubic-bezier(0,.5,.5,1) infinite}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:first-child{left:8px;animation-delay:-.24s}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:nth-child(2){left:16px;animation-delay:-.12s}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:nth-child(3){left:24px;animation-delay:0}@keyframes lds-facebook{0%{top:8px;height:32px}50%,to{top:12px;height:16px}}"]}),e})();var Nn=n("iadO");function Ln(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div"),o["\u0275\u0275elementStart"](1,"mat-form-field",5),o["\u0275\u0275elementStart"](2,"input",6),o["\u0275\u0275listener"]("blur",(function(t){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"](2).onBlur(t)}))("keyup",(function(t){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"](2).looseFocus(t)})),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("type",e.inputType)("placeholder",e.label)("required",e.required)("formControl",e.fieldCtrl)("disabled",e.disabled)}}function jn(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div"),o["\u0275\u0275elementStart"](1,"mat-form-field",5),o["\u0275\u0275elementStart"](2,"textarea",7),o["\u0275\u0275listener"]("blur",(function(t){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"](2).onBlur(t)})),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("type",e.inputType)("required",e.required)("formControl",e.fieldCtrl)("disabled",e.disabled)}}function Bn(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div"),o["\u0275\u0275elementStart"](1,"mat-form-field",5),o["\u0275\u0275elementStart"](2,"input",8),o["\u0275\u0275listener"]("dateChange",(function(t){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"](2).onBlur(t)}))("keyup",(function(t){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"](2).looseFocus(t)})),o["\u0275\u0275elementEnd"](),o["\u0275\u0275element"](3,"mat-datepicker-toggle",9),o["\u0275\u0275element"](4,"mat-datepicker",null,10),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275reference"](5),t=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("matDatepicker",e)("placeholder",t.label)("required",t.required)("formControl",t.fieldCtrl)("disabled",t.disabled)("min",t.min)("max",t.max),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("for",e)}}function Vn(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div"),o["\u0275\u0275elementStart"](1,"mat-form-field",11),o["\u0275\u0275elementStart"](2,"input",12),o["\u0275\u0275listener"]("dateChange",(function(t){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"](2).onBlur(t)}))("keyup",(function(t){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"](2).looseFocus(t)})),o["\u0275\u0275elementEnd"](),o["\u0275\u0275element"](3,"mat-datepicker-toggle",9),o["\u0275\u0275element"](4,"mat-datepicker",null,10),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275reference"](5),t=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("matDatepicker",e)("placeholder",t.label)("required",t.required)("formControl",t.fieldCtrl)("disabled",t.disabled)("min",t.min)("max",t.max),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("for",e)}}function Un(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div"),o["\u0275\u0275elementStart"](1,"app-search-user",13),o["\u0275\u0275listener"]("blur",(function(t){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"](2).onBlur(t)}))("keyup",(function(t){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"](2).looseFocus(t)}))("selectedUser",(function(t){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"](2).setOwnerName(t)})),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("required",e.required)("formControl",e.fieldCtrl)("disabled",e.disabled)("isAutocomplete",!0)}}function zn(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div"),o["\u0275\u0275elementStart"](1,"div",3),o["\u0275\u0275template"](2,Ln,3,5,"div",4),o["\u0275\u0275template"](3,jn,3,4,"div",4),o["\u0275\u0275template"](4,Bn,6,8,"div",4),o["\u0275\u0275template"](5,Vn,6,8,"div",4),o["\u0275\u0275template"](6,Un,2,4,"div",4),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngSwitch",e.type),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngSwitchCase","input"),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngSwitchCase","textarea"),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngSwitchCase","date"),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngSwitchCase","textDate"),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngSwitchCase","user")}}function $n(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div",14),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](e);const t=o["\u0275\u0275nextContext"]();return t.edit(t.value)}))("focus",(function(){o["\u0275\u0275restoreView"](e);const t=o["\u0275\u0275nextContext"]();return t.edit(t.value)})),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275property"]("innerHTML",e.fieldCtrl.value?e.fieldCtrl.value+" "+e.endFormatter:e.label,o["\u0275\u0275sanitizeHtml"])}}function qn(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div",15),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](e);const t=o["\u0275\u0275nextContext"]();return t.edit(t.value)}))("focus",(function(){o["\u0275\u0275restoreView"](e);const t=o["\u0275\u0275nextContext"]();return t.edit(t.value)})),o["\u0275\u0275text"](1),o["\u0275\u0275pipe"](2,"amDateFormat"),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" ",o["\u0275\u0275pipeBind2"](2,1,e.fieldCtrl.value,"DD-MM-YY")||e.label," ")}}function Rn(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div",15),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](e);const t=o["\u0275\u0275nextContext"]();return t.edit(t.value)}))("focus",(function(){o["\u0275\u0275restoreView"](e);const t=o["\u0275\u0275nextContext"]();return t.edit(t.value)})),o["\u0275\u0275element"](1,"app-user-profile",16),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("oid",e.fieldCtrl.value)}}let Hn=(()=>{class e extends xn.a{constructor(e,t){super(),this.graphApi=e,this.label="",this.type="input",this.inputType="text",this.required=!1,this.endFormatter="",this.fieldCtrl=new Me.j,this.disabled=!1,this._onDestroy=new yn.b,this.onChange=Function.prototype,this.onTouched=Function.prototype,this.preValue="",this.editing=!1,this.setOwnerName1=new o.EventEmitter}ngOnInit(){this.fieldCtrl.valueChanges.pipe(Object(Sn.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e)})}ngOnChanges(){this.fieldCtrl.valueChanges.pipe(Object(Sn.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e)})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}setDisabledState(e){e?(this.disabled=!0,this.fieldCtrl.disable()):(this.disabled=!1,this.fieldCtrl.enable())}writeValue(e){this.fieldCtrl.setValue(e)}onBlur(e){this.editing=!1}looseFocus(e){13==e.keyCode&&document.getElementById("inlineEditControl")&&document.getElementById("inlineEditControl").blur()}edit(e){this.disabled||(this.preValue=e,this.editing=!0,setTimeout(e=>{document.getElementById("inlineEditControl")&&document.getElementById("inlineEditControl").focus()}))}setOwnerName(e){this.setOwnerName1.emit(e.displayName)}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](b.a),o["\u0275\u0275directiveInject"](o.ElementRef))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["inline-form-field"]],inputs:{label:"label",type:"type",inputType:"inputType",required:"required",endFormatter:"endFormatter",disabled:"disabled",min:"min",max:"max"},outputs:{setOwnerName1:"setOwnerName1"},features:[o["\u0275\u0275ProvidersFeature"]([{provide:Me.t,useExisting:Object(o.forwardRef)(()=>e),multi:!0}]),o["\u0275\u0275InheritDefinitionFeature"],o["\u0275\u0275NgOnChangesFeature"]],decls:4,vars:4,consts:[[4,"ngIf"],["class","value-field",3,"innerHTML","click","focus",4,"ngIf"],["class","value-field",3,"click","focus",4,"ngIf"],[3,"ngSwitch"],[4,"ngSwitchCase"],["appearance","outline",1,"w-100"],["matInput","","id","inlineEditControl",3,"type","placeholder","required","formControl","disabled","blur","keyup"],["matInput","","id","inlineEditControl",2,"height","130px !important",3,"type","required","formControl","disabled","blur"],["matInput","","id","inlineEditControl",3,"matDatepicker","placeholder","required","formControl","disabled","min","max","dateChange","keyup"],["matSuffix","",3,"for"],["picker",""],["appearance","fill",1,"w-100"],["matInput","","id","inlineEditControl",1,"w-80","textDateClass",3,"matDatepicker","placeholder","required","formControl","disabled","min","max","dateChange","keyup"],[3,"required","formControl","disabled","isAutocomplete","blur","keyup","selectedUser"],[1,"value-field",3,"innerHTML","click","focus"],[1,"value-field",3,"click","focus"],["type","small",3,"oid"]],template:function(e,t){1&e&&(o["\u0275\u0275template"](0,zn,7,6,"div",0),o["\u0275\u0275template"](1,$n,1,1,"div",1),o["\u0275\u0275template"](2,qn,3,4,"div",2),o["\u0275\u0275template"](3,Rn,2,1,"div",2)),2&e&&(o["\u0275\u0275property"]("ngIf",t.editing),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",!t.editing&&"date"!=t.type&&"user"!=t.type),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",!t.editing&&"date"==t.type),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",!t.editing&&"user"==t.type))},directives:[i.NgIf,i.NgSwitch,i.NgSwitchCase,je.c,Be.b,Me.e,Me.F,Me.v,Me.k,Nn.g,Nn.i,je.i,Nn.f,Y],styles:["input[_ngcontent-%COMP%]{font-size:14px!important;padding:3px!important}.value-field[_ngcontent-%COMP%]{padding:4px!important}.value-field[_ngcontent-%COMP%]:hover{outline:1px solid #a5a4a2!important}.textDateClass[_ngcontent-%COMP%]{background:transparent!important}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding:0!important;margin:0!important}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper   .mat-form-field-flex{padding:0!important}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper   .mat-form-field-infix{padding:6px!important;margin:auto!important;border-top-width:0!important}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper   .mat-form-field-suffix{top:0}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper   .mat-form-field-outline{top:0!important;line-height:1.7!important}"]}),e})(),Kn=(()=>{class e{transform(e,t){return G(e).format("LLL")}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=o["\u0275\u0275definePipe"]({name:"multiDate",type:e,pure:!0}),e})(),Yn=(()=>{class e{transform(e){return G(e).format("DD MMM YYYY")}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=o["\u0275\u0275definePipe"]({name:"DDMMYY",type:e,pure:!0}),e})();function Wn(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"span"),o["\u0275\u0275element"](1,"inline-form-field",35),o["\u0275\u0275elementEnd"]())}function Gn(e,t){1&e&&o["\u0275\u0275element"](0,"span")}function Xn(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"span"),o["\u0275\u0275element"](1,"inline-form-field",36),o["\u0275\u0275elementEnd"]())}function Jn(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"span"),o["\u0275\u0275text"](1),o["\u0275\u0275pipe"](2,"multiDate"),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate"](e.data.created_on?o["\u0275\u0275pipeBind1"](2,1,e.data.created_on):"")}}function Qn(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"span"),o["\u0275\u0275text"](1),o["\u0275\u0275pipe"](2,"DDMMYY"),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate"](e.data.end_date?o["\u0275\u0275pipeBind1"](2,1,e.data.end_date):"")}}function Zn(e,t){if(1&e&&o["\u0275\u0275element"](0,"app-user-profile",37),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275property"]("type","mini")("oid",e.data.created_by)}}const ei=function(e,t,n,i,a,o){return{open_bg:e,inprogress_bg:t,completed_bg:n,triggered_bg:i,rejected_bg:a,concurred_bg:o}};function ti(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementContainerStart"](0),o["\u0275\u0275elementStart"](1,"div",2),o["\u0275\u0275elementStart"](2,"div",3),o["\u0275\u0275elementStart"](3,"div",4),o["\u0275\u0275elementStart"](4,"div",5),o["\u0275\u0275elementStart"](5,"button",6),o["\u0275\u0275elementStart"](6,"mat-icon"),o["\u0275\u0275text"](7,"assignment"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](8,"button",7),o["\u0275\u0275listener"]("click",(function(){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"]().saveDetail()})),o["\u0275\u0275elementStart"](9,"mat-icon",8),o["\u0275\u0275text"](10,"done_all"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](11,"div",9),o["\u0275\u0275elementStart"](12,"div",10),o["\u0275\u0275elementStart"](13,"div",11),o["\u0275\u0275template"](14,Wn,2,0,"span",1),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](15,"div",12),o["\u0275\u0275elementStart"](16,"div",4),o["\u0275\u0275elementStart"](17,"div",13),o["\u0275\u0275text"](18,"Type"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](19,"div",14),o["\u0275\u0275text"](20,"Task"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](21,"div",15),o["\u0275\u0275elementStart"](22,"div",16),o["\u0275\u0275elementStart"](23,"div",4),o["\u0275\u0275element"](24,"div",17),o["\u0275\u0275elementStart"](25,"div",18),o["\u0275\u0275template"](26,Gn,1,0,"span",1),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](27,"div",19),o["\u0275\u0275elementStart"](28,"div",4),o["\u0275\u0275elementStart"](29,"div",20),o["\u0275\u0275text"](30,"Status"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](31,"div",21),o["\u0275\u0275element"](32,"app-inline-input-search",22),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](33,"div",15),o["\u0275\u0275elementStart"](34,"div",23),o["\u0275\u0275elementStart"](35,"div",4),o["\u0275\u0275elementStart"](36,"div",24),o["\u0275\u0275text"](37,"Owner"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](38,"div",18),o["\u0275\u0275element"](39,"app-inline-search-user",25),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](40,"div",19),o["\u0275\u0275elementStart"](41,"div",4),o["\u0275\u0275elementStart"](42,"div",20),o["\u0275\u0275text"](43,"Due On"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](44,"div",26),o["\u0275\u0275template"](45,Xn,2,0,"span",1),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](46,"div",27),o["\u0275\u0275elementStart"](47,"div",16),o["\u0275\u0275elementStart"](48,"div",4),o["\u0275\u0275elementStart"](49,"div",17),o["\u0275\u0275text"](50,"Created on"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](51,"div",18),o["\u0275\u0275template"](52,Jn,3,3,"span",1),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](53,"div",19),o["\u0275\u0275elementStart"](54,"div",28),o["\u0275\u0275elementStart"](55,"div",29),o["\u0275\u0275text"](56,"Completed On"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](57,"div",30),o["\u0275\u0275template"](58,Qn,3,3,"span",1),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](59,"div",27),o["\u0275\u0275elementStart"](60,"div",23),o["\u0275\u0275elementStart"](61,"div",4),o["\u0275\u0275elementStart"](62,"div",31),o["\u0275\u0275text"](63,"Created By"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](64,"div",32),o["\u0275\u0275elementStart"](65,"div",4),o["\u0275\u0275elementStart"](66,"div",33),o["\u0275\u0275template"](67,Zn,1,2,"app-user-profile",34),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275element"](68,"div",19),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngClass",o["\u0275\u0275pureFunction6"](8,ei,1==e.data.activity_status_id,2==e.data.activity_status_id,3==e.data.activity_status_id,4==e.data.activity_status_id,5==e.data.activity_status_id,6==e.data.activity_status_id)),o["\u0275\u0275advance"](12),o["\u0275\u0275property"]("ngIf",null!=e.data),o["\u0275\u0275advance"](12),o["\u0275\u0275property"]("ngIf",null!=e.data),o["\u0275\u0275advance"](6),o["\u0275\u0275property"]("list",e.Activitystatus),o["\u0275\u0275advance"](13),o["\u0275\u0275property"]("ngIf",null!=e.data),o["\u0275\u0275advance"](7),o["\u0275\u0275property"]("ngIf",null!=e.data),o["\u0275\u0275advance"](6),o["\u0275\u0275property"]("ngIf",null!=e.data),o["\u0275\u0275advance"](9),o["\u0275\u0275property"]("ngIf",null!=e.data)}}let ni=(()=>{class e{constructor(e,t,n,i){this.fb=e,this.leadService=t,this.snackBar=n,this.loginService=i,this.Activitystatus=[{id:1,name:"open"},{id:2,name:"in-progress"},{id:3,name:"Completed"}],this.activityTaskDetailForm=this.fb.group({title:[""],dueOn:[""],owner:[""],newDueOn:[""],predecessorActivityId:[""],applicationReferenceId:"",activityStatus:[""]})}saveDetail(){3==this.activityTaskDetailForm.value.activityStatus?this.leadService.completeActivity(this.data.id,1).then(e=>Object(d.c)(this,void 0,void 0,(function*(){console.log(e),this.data.activity_status_id=3,this.data.end_date=new Date})),e=>{console.error(e),this.snackBar.open("Failed to Complete Activity!","Dismiss",{duration:2e3})}):1==this.activityTaskDetailForm.value.activityStatus?this.leadService.openActivity(this.data.id).then(e=>Object(d.c)(this,void 0,void 0,(function*(){console.log(e),this.data.activity_status_id=1})),e=>{console.error(e),this.snackBar.open("Failed Moving to Open Status!","Dismiss",{duration:2e3})}):2==this.activityTaskDetailForm.value.activityStatus&&(this.activityTaskDetailForm.patchValue({owner:this.loginService.getProfile().profile.oid}),this.leadService.startActivity(this.data.id).then(e=>Object(d.c)(this,void 0,void 0,(function*(){this.data.activity_status_id=2,console.log(e)})),e=>{console.error(e),this.snackBar.open("Failed to Start Activity!","Dismiss",{duration:2e3})})),this.leadService.updateTaskDetailInline(this.data.id,this.activityTaskDetailForm.value).subscribe(e=>{console.log(e),this.snackBar.open("Updated successfully!","Dismiss",{duration:2e3})},e=>{this.snackBar.open("Oops! something went wrong..","Dismiss",{duration:2e3}),console.error(e)})}ngOnInit(){}ngOnChanges(){console.log(this.data),this.data&&this.activityTaskDetailForm.patchValue({title:this.data.title,dueOn:this.data.task_due_date,newDueOn:this.data.task_due_date,predecessorActivityId:this.data.predecessor_id,applicationReferenceId:this.data.opportunity_id,owner:this.data.assigned_to,activityStatus:this.data.activity_status_id})}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](Me.i),o["\u0275\u0275directiveInject"](u.a),o["\u0275\u0275directiveInject"](h.a),o["\u0275\u0275directiveInject"](S.a))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-activity-task-detail"]],inputs:{data:"data"},features:[o["\u0275\u0275NgOnChangesFeature"]],decls:2,vars:2,consts:[[3,"formGroup"],[4,"ngIf"],[1,"row","pt-3","meeting-styles"],[1,"col-12","p-0",3,"ngClass"],[1,"row","p-0"],[1,"col-1","pt-3","pl-3"],["mat-icon-button","",1,"activity-button"],["mat-icon-button","","matTooltip","Save",1,"save-button","mt-3","ml-1",3,"click"],[1,"iconButton"],[1,"col-11","p-0","pt-2","pb-2"],[1,"row"],[1,"col-7","name","pt-2","pb-2","pl-1"],[1,"col-5","pt-3","pb-2"],[1,"col-4","pl-0","pr-0","key"],[1,"col-8","value"],[1,"row","pt-1"],[1,"col-7","value","p-0"],[1,"col-2","pt-1","pb-1","pr-0","pl-0","key"],[1,"col-8","value","pt-1","pb-1","pl-1"],[1,"col-5"],[1,"col-4","pt-2","pl-0","pr-0","key"],[1,"col-8","pt-1","pb-1","value"],["formControlName","activityStatus",3,"list"],[1,"col-7","p-0"],[1,"col-2","pt-2","pr-0","pl-0","key"],["formControlName","owner"],[1,"col-8","pb-1","pt-1","value"],[1,"row","pt-2"],[1,"row","pl-0","pr-0","pt-0","pb-2"],[1,"col-4","pt-1","pl-0","pr-0","key"],[1,"col-8","value","pt-1"],[1,"col-2","pt-1","pl-0","pr-0","key"],[1,"col-8","pl-0"],[1,"col-12","value","pl-2","pt-1"],[3,"type","oid",4,"ngIf"],["label","title","formControlName","title"],["label","due On","type","date","formControlName","dueOn"],[3,"type","oid"]],template:function(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"form",0),o["\u0275\u0275template"](1,ti,69,15,"ng-container",1),o["\u0275\u0275elementEnd"]()),2&e&&(o["\u0275\u0275property"]("formGroup",t.activityTaskDetailForm),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",t.data))},directives:[Me.J,Me.w,Me.n,i.NgIf,i.NgClass,_.a,E.a,C.a,kn,Me.v,Me.l,Fn,Hn,Y],pipes:[Kn,Yn],styles:[".meeting-styles[_ngcontent-%COMP%]   .open_bg[_ngcontent-%COMP%]{background-color:hsla(0,0%,92.5%,.30196078431372547)!important}.meeting-styles[_ngcontent-%COMP%]   .inprogress_bg[_ngcontent-%COMP%], .meeting-styles[_ngcontent-%COMP%]   .open_bg[_ngcontent-%COMP%]{box-shadow:0 3px 6px hsla(0,0%,41.2%,.19),0 1px 4px hsla(0,0%,41.2%,.19)}.meeting-styles[_ngcontent-%COMP%]   .inprogress_bg[_ngcontent-%COMP%]{background-color:rgba(255,233,76,.*****************)!important}.meeting-styles[_ngcontent-%COMP%]   .completed_bg[_ngcontent-%COMP%]{background-color:rgba(233,255,240,.5803921568627451)!important}.meeting-styles[_ngcontent-%COMP%]   .completed_bg[_ngcontent-%COMP%], .meeting-styles[_ngcontent-%COMP%]   .triggered_bg[_ngcontent-%COMP%]{box-shadow:0 3px 6px hsla(0,0%,41.2%,.19),0 1px 4px hsla(0,0%,41.2%,.19)}.meeting-styles[_ngcontent-%COMP%]   .triggered_bg[_ngcontent-%COMP%]{background-color:rgba(104,157,255,.23137254901960785)!important}.meeting-styles[_ngcontent-%COMP%]   .rejected_bg[_ngcontent-%COMP%]{background-color:#f88570!important;box-shadow:0 3px 6px hsla(0,0%,41.2%,.19)}.meeting-styles[_ngcontent-%COMP%]   .concurred_bg[_ngcontent-%COMP%]{background-color:#ff1aff!important;box-shadow:0 3px 6px hsla(0,0%,41.2%,.19)}.meeting-styles[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%]{background-color:#cf0001!important;line-height:8px;width:28px;height:28px;box-shadow:0 3px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.meeting-styles[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#fff;font-size:18px}.meeting-styles[_ngcontent-%COMP%]   .name[_ngcontent-%COMP%]{font-size:14px;color:#cf0001;font-family:Roboto;font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;font-size:14px!important}.meeting-styles[_ngcontent-%COMP%]   .activity-button[_ngcontent-%COMP%]{color:#fff;line-height:24px!important;background-color:#30336b;width:30px!important;height:30px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.meeting-styles[_ngcontent-%COMP%]   .activity-button[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%]{font-size:18px}.meeting-styles[_ngcontent-%COMP%]   .key[_ngcontent-%COMP%]{font-size:13px;color:#7b7b7a}.meeting-styles[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;font-size:14px;font-weight:400}"]}),e})();function ii(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"span"),o["\u0275\u0275text"](1),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" ",e.data.title?e.data.title:"-"," ")}}function ai(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"span",23),o["\u0275\u0275text"](1),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate"](null!=e.data&&e.data.description?null==e.data?null:e.data.description:"")}}function oi(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"span"),o["\u0275\u0275text"](1),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" ",e.data.task_due_data?e.data.task_due_date:""," ")}}function li(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"span"),o["\u0275\u0275text"](1),o["\u0275\u0275pipe"](2,"DDMMYYYY"),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate"](e.data.created_on?o["\u0275\u0275pipeBind1"](2,1,e.data.created_on):"")}}function ri(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"span"),o["\u0275\u0275text"](1),o["\u0275\u0275pipe"](2,"DDMMYYYY"),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate"](e.data.end_date?o["\u0275\u0275pipeBind1"](2,1,e.data.end_date):"")}}const si=function(e){return{color:e}};function di(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div",26),o["\u0275\u0275elementStart"](1,"div",27),o["\u0275\u0275elementStart"](2,"button",28),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](e);const n=t.$implicit,i=t.index;return o["\u0275\u0275nextContext"](3).completeCheckList(n.sub_activity_id,i)})),o["\u0275\u0275elementStart"](3,"mat-icon",29),o["\u0275\u0275text"](4," check_circle"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](5,"div",30),o["\u0275\u0275elementStart"](6,"span",31),o["\u0275\u0275text"](7),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngStyle",o["\u0275\u0275pureFunction1"](2,si,1==e.status?"#009432":"#9A9A9A")),o["\u0275\u0275advance"](5),o["\u0275\u0275textInterpolate"](e.sub_activity_name?e.sub_activity_name:"")}}function ci(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",24),o["\u0275\u0275template"](1,di,8,4,"div",25),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngForOf",e.sub_activities)}}const pi=function(e,t,n){return{open_bg:e,inprogress_bg:t,completed_bg:n}};function mi(e,t){if(1&e&&(o["\u0275\u0275elementContainerStart"](0),o["\u0275\u0275elementStart"](1,"div",1),o["\u0275\u0275elementStart"](2,"div",2),o["\u0275\u0275elementStart"](3,"div",3),o["\u0275\u0275elementStart"](4,"div",4),o["\u0275\u0275elementStart"](5,"button",5),o["\u0275\u0275elementStart"](6,"mat-icon"),o["\u0275\u0275text"](7,"playlist_add_check"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](8,"div",6),o["\u0275\u0275elementStart"](9,"div",7),o["\u0275\u0275elementStart"](10,"div",8),o["\u0275\u0275template"](11,ii,2,1,"span",0),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](12,"div",9),o["\u0275\u0275elementStart"](13,"div",3),o["\u0275\u0275elementStart"](14,"div",10),o["\u0275\u0275text"](15,"Type"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](16,"div",11),o["\u0275\u0275text"](17,"Checklist"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](18,"div",12),o["\u0275\u0275elementStart"](19,"div",13),o["\u0275\u0275elementStart"](20,"div",3),o["\u0275\u0275elementStart"](21,"div",14),o["\u0275\u0275text"](22,"Description"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](23,"div",15),o["\u0275\u0275template"](24,ai,2,1,"span",16),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](25,"div",17),o["\u0275\u0275elementStart"](26,"div",3),o["\u0275\u0275elementStart"](27,"div",18),o["\u0275\u0275text"](28,"Due on"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](29,"div",19),o["\u0275\u0275template"](30,oi,2,1,"span",0),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](31,"div",12),o["\u0275\u0275elementStart"](32,"div",13),o["\u0275\u0275elementStart"](33,"div",3),o["\u0275\u0275elementStart"](34,"div",14),o["\u0275\u0275text"](35,"Created on"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](36,"div",15),o["\u0275\u0275template"](37,li,3,3,"span",0),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](38,"div",17),o["\u0275\u0275elementStart"](39,"div",3),o["\u0275\u0275elementStart"](40,"div",18),o["\u0275\u0275text"](41,"Completed on"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](42,"div",19),o["\u0275\u0275template"](43,ri,3,3,"span",0),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](44,"div",20),o["\u0275\u0275elementStart"](45,"div",21),o["\u0275\u0275template"](46,ci,2,1,"div",22),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementContainerEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngClass",o["\u0275\u0275pureFunction3"](7,pi,1==e.data.activity_status_id,2==e.data.activity_status_id,3==e.data.activity_status_id)),o["\u0275\u0275advance"](9),o["\u0275\u0275property"]("ngIf",null!=e.data),o["\u0275\u0275advance"](13),o["\u0275\u0275property"]("ngIf",null!=e.data),o["\u0275\u0275advance"](6),o["\u0275\u0275property"]("ngIf",null!=e.data),o["\u0275\u0275advance"](7),o["\u0275\u0275property"]("ngIf",null!=e.data),o["\u0275\u0275advance"](6),o["\u0275\u0275property"]("ngIf",null!=e.data),o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("ngIf",null!=e.data)}}let ui=(()=>{class e{constructor(e,t,n){this.snackBar=e,this.leadService=t,this.fb=n,this.completeCheckList=(e,t)=>{this.leadService.completeChecklist(e).subscribe(e=>{this.sub_activities[t].status=1,this.snackBar.open("Completed!","Dismiss",{duration:2e3})},e=>{console.error(e),this.snackBar.open("Completion failed!","Dismiss",{duration:2e3})})}}ngOnInit(){console.log("devi",this.data),this.leadService.getSubActivities(this.data.id).subscribe(e=>{console.log(e),this.sub_activities=e},e=>{console.log(e)})}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](h.a),o["\u0275\u0275directiveInject"](u.a),o["\u0275\u0275directiveInject"](Me.i))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-check-list-details"]],inputs:{data:"data"},decls:1,vars:1,consts:[[4,"ngIf"],[1,"row","pt-3","mail-styles"],[1,"col-12","p-0",3,"ngClass"],[1,"row","p-0"],[1,"col-1","pt-3","pl-3"],["mat-icon-button","",1,"activity-button"],[1,"col-11","p-0","pt-2","pb-2"],[1,"row"],[1,"col-7","pt-2","pb-2","pl-1","name","pl-0"],[1,"col-5","pt-3"],[1,"col-4","pl-0","pr-0","key"],[1,"col-8","value"],[1,"row","pt-1"],[1,"col-7","value","p-0","pl-2"],[1,"col-2","pt-2","pr-0","pl-0","key"],[1,"col-8","value","pt-2","pb-1","pl-1"],["matTooltip","Testing activity Description blah",4,"ngIf"],[1,"col-5"],[1,"col-4","pt-2","pb-2","pl-0","pr-0","key"],[1,"col-8","pb-1","pt-1","value"],[1,"row","pt-3","check-list-style"],[1,"col-12"],["class","card check-list-card",4,"ngIf"],["matTooltip","Testing activity Description blah"],[1,"card","check-list-card"],["class","row p-2 border-bottom solid",4,"ngFor","ngForOf"],[1,"row","p-2","border-bottom","solid"],[1,"col-2"],["mat-icon-button","",1,"checkIcon","my-auto",3,"ngStyle","click"],[2,"cursor","pointer"],[1,"col-10","value","pl-0","d-flex"],[1,"my-auto"]],template:function(e,t){1&e&&o["\u0275\u0275template"](0,mi,47,11,"ng-container",0),2&e&&o["\u0275\u0275property"]("ngIf",t.data)},directives:[i.NgIf,i.NgClass,_.a,E.a,C.a,i.NgForOf,i.NgStyle],pipes:[X],styles:[".mail-styles[_ngcontent-%COMP%]   .open_bg[_ngcontent-%COMP%]{background-color:hsla(0,0%,92.5%,.30196078431372547)!important}.mail-styles[_ngcontent-%COMP%]   .inprogress_bg[_ngcontent-%COMP%], .mail-styles[_ngcontent-%COMP%]   .open_bg[_ngcontent-%COMP%]{box-shadow:0 3px 6px hsla(0,0%,41.2%,.19),0 1px 4px hsla(0,0%,41.2%,.19)}.mail-styles[_ngcontent-%COMP%]   .inprogress_bg[_ngcontent-%COMP%]{background-color:rgba(255,233,76,.*****************)!important}.mail-styles[_ngcontent-%COMP%]   .completed_bg[_ngcontent-%COMP%]{background-color:rgba(233,255,240,.5803921568627451)!important;box-shadow:0 3px 6px hsla(0,0%,41.2%,.19),0 1px 4px hsla(0,0%,41.2%,.19)}.mail-styles[_ngcontent-%COMP%]   .name[_ngcontent-%COMP%]{font-size:14px;color:#cf0001;font-family:Roboto;font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;font-size:14px!important}.mail-styles[_ngcontent-%COMP%]   .activity-button[_ngcontent-%COMP%]{color:#fff;line-height:24px!important;background-color:#006266;width:30px!important;height:30px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.mail-styles[_ngcontent-%COMP%]   .activity-button[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%]{font-size:18px}.mail-styles[_ngcontent-%COMP%]   .key[_ngcontent-%COMP%]{font-size:13px;color:#7b7b7a}.mail-styles[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%]{background-color:#cf0001!important;line-height:8px;width:28px;height:28px;box-shadow:0 3px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.mail-styles[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#fff;font-size:18px}.mail-styles[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;font-size:14px;color:#1a1a1a;font-weight:400}.check-list-style[_ngcontent-%COMP%]   .check-list-card[_ngcontent-%COMP%]{transition:all .25s linear;animation:slide-in-left .5s cubic-bezier(.25,.46,.45,.94) both}.check-list-style[_ngcontent-%COMP%]   .checkIcon[_ngcontent-%COMP%]{color:#c9cccf}.check-list-style[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;font-size:14px!important;color:#1a1a1a!important;font-weight:400}"]}),e})();function hi(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div"),o["\u0275\u0275elementStart"](1,"app-mini-card",22),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](e);const n=t.$implicit,i=t.index;return o["\u0275\u0275nextContext"](2).clickActivityMiniCard(n,i)})),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=t.index,i=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("activityNo",e.activity_id)("scrollIndex",n==i.scrollToIndex)}}function vi(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"cdk-virtual-scroll-viewport",20),o["\u0275\u0275template"](1,hi,2,2,"div",21),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("cdkVirtualForOf",e.activityIds)("cdkVirtualForTemplateCacheSize",0)}}function gi(e,t){1&e&&o["\u0275\u0275element"](0,"mat-progress-bar",23)}function fi(e,t){if(1&e&&o["\u0275\u0275element"](0,"app-call-details",24),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275property"]("details",e.currentlySelectedActivityDetail)}}function yi(e,t){if(1&e&&o["\u0275\u0275element"](0,"app-meeting-details",24),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275property"]("details",e.currentlySelectedActivityDetail)}}function xi(e,t){if(1&e&&o["\u0275\u0275element"](0,"app-mail-detail",24),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275property"]("details",e.currentlySelectedActivityDetail)}}function Si(e,t){if(1&e&&o["\u0275\u0275element"](0,"app-activity-task-detail",25),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275property"]("data",e.currentlySelectedActivityDetail)}}function bi(e,t){if(1&e&&o["\u0275\u0275element"](0,"app-check-list-details",25),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275property"]("data",e.currentlySelectedActivityDetail)}}let _i=(()=>{class e{constructor(e,t){this.leadService=e,this._route=t,this.isDetailLoading=!1,this.comment=[],this.clickActivityMiniCard=(e,t)=>Object(d.c)(this,void 0,void 0,(function*(){this.activityId=e.activity_id,this.scrollToIndex=t,console.log(e),this.isDetailLoading=!0,this.leadService.getFullDetailOfActivity(e.activity_id).subscribe(e=>Object(d.c)(this,void 0,void 0,(function*(){console.log(e),this.currentlySelectedActivityId=e[0].sales_activity_id,this.currentlySelectedActivityDetail=e[0],this.isDetailLoading=!1,this.comment=yield this.fetchCommentsFromService(),null==this.comment&&(this.comment=[])})),e=>{console.error(e)})})),this.getSelectedActivityDetailOnPageLoad=e=>(this.isDetailLoading=!0,new Promise((t,n)=>{this.leadService.getFullDetailOfActivity(e).subscribe(e=>{this.isDetailLoading=!1,t(e[0])},e=>{console.error(e),n(e)})})),this.getActivityId=()=>new Promise((e,t)=>{this.leadService.activityList(this.applicationId,this.leadId).then(t=>{console.log(t),e(t)},e=>{console.error(e),t(e)})}),this.fetchCommentsFromService=()=>new Promise((e,t)=>{this.leadService.getActivityComments(this.activityId).subscribe(t=>{e(JSON.parse(t.comments))},e=>{console.error(e),t(e)})})}ngOnInit(){return Object(d.c)(this,void 0,void 0,(function*(){console.log(this.scrollToIndex),"leads"==window.location.pathname.split("/")[2]?this.applicationId=35:"opportunities"==window.location.pathname.split("/")[2]&&(this.applicationId=36),this._route.parent.params.subscribe(e=>{this.leadId=e.leadId}),this.activityIds=yield this.getActivityId(),this.activityId=this.activityIds[this.scrollToIndex].activity_id,console.log(this.activityId),this.currentlySelectedActivityDetail=yield this.getSelectedActivityDetailOnPageLoad(this.activityIds[this.scrollToIndex].activity_id),this.currentlySelectedActivityId=this.currentlySelectedActivityDetail.sales_activity_id,console.log(this.currentlySelectedActivityDetail),this.comment=yield this.fetchCommentsFromService(),null==this.comment&&(this.comment=[])}))}ngAfterViewInit(){setTimeout(()=>{this.viewPort.scrollToIndex(this.scrollToIndex-1,"smooth")},2e3)}getComments(e){console.log(e),this.leadService.insertComment(e,this.activityId).subscribe(e=>{console.log(e)},e=>{console.error(e)})}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](u.a),o["\u0275\u0275directiveInject"](a.a))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-activity-details"]],viewQuery:function(e,t){if(1&e&&o["\u0275\u0275viewQuery"]($e.e,!0),2&e){let e;o["\u0275\u0275queryRefresh"](e=o["\u0275\u0275loadQuery"]())&&(t.viewPort=e.first)}},inputs:{scrollToIndex:"scrollToIndex"},decls:31,vars:9,consts:[[1,"row"],[1,"col-12"],[1,"col-12","pl-1","pr-1"],[1,"col-3","pr-0","pl-2"],["itemSize","50","minBufferPx","300","maxBufferPx","500","class","example-viewport",4,"ngIf"],[1,"col-9","border","solid","bg-white","slide-in-right"],[1,"col-12","p-0"],["mode","indeterminate",4,"ngIf"],[3,"details",4,"ngIf"],[3,"data",4,"ngIf"],[3,"activityId"],[1,"row","pt-2"],[1,"tab-height"],["label","Attachments"],[1,"row","slide-from-down",2,"min-height","230px !important"],[1,"empty-styles","d-flex","justify-content-center","align-items-center","mt-4"],[1,"d-flex","justify-content-center","align-items-center"],["src","https://assets.kebs.app/images/noAccounts.png","height","190","width","190",1,"mt-2"],["label","Comments"],[3,"comments","sendComments"],["itemSize","50","minBufferPx","300","maxBufferPx","500",1,"example-viewport"],[4,"cdkVirtualFor","cdkVirtualForOf","cdkVirtualForTemplateCacheSize"],[3,"activityNo","scrollIndex","click"],["mode","indeterminate"],[3,"details"],[3,"data"]],template:function(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"div",0),o["\u0275\u0275elementStart"](1,"div",1),o["\u0275\u0275elementStart"](2,"div",0),o["\u0275\u0275elementStart"](3,"div",2),o["\u0275\u0275elementStart"](4,"div",0),o["\u0275\u0275elementStart"](5,"div",3),o["\u0275\u0275template"](6,vi,2,2,"cdk-virtual-scroll-viewport",4),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](7,"div",5),o["\u0275\u0275elementStart"](8,"div",0),o["\u0275\u0275elementStart"](9,"div",6),o["\u0275\u0275template"](10,gi,1,0,"mat-progress-bar",7),o["\u0275\u0275template"](11,fi,1,1,"app-call-details",8),o["\u0275\u0275template"](12,yi,1,1,"app-meeting-details",8),o["\u0275\u0275template"](13,xi,1,1,"app-mail-detail",8),o["\u0275\u0275template"](14,Si,1,1,"app-activity-task-detail",9),o["\u0275\u0275template"](15,bi,1,1,"app-check-list-details",9),o["\u0275\u0275element"](16,"app-activity-user-note",10),o["\u0275\u0275elementStart"](17,"div",11),o["\u0275\u0275elementStart"](18,"div",6),o["\u0275\u0275elementStart"](19,"mat-tab-group",12),o["\u0275\u0275elementStart"](20,"mat-tab",13),o["\u0275\u0275elementContainerStart"](21),o["\u0275\u0275elementStart"](22,"div",14),o["\u0275\u0275elementStart"](23,"div",1),o["\u0275\u0275elementStart"](24,"div"),o["\u0275\u0275elementStart"](25,"p",15),o["\u0275\u0275text"](26," No attachments found ! "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](27,"div",16),o["\u0275\u0275element"](28,"img",17),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementContainerEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](29,"mat-tab",18),o["\u0275\u0275elementStart"](30,"app-chat-comment",19),o["\u0275\u0275listener"]("sendComments",(function(e){return t.getComments(e)})),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e&&(o["\u0275\u0275advance"](6),o["\u0275\u0275property"]("ngIf",t.activityIds),o["\u0275\u0275advance"](4),o["\u0275\u0275property"]("ngIf",1==t.isDetailLoading),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",2==t.currentlySelectedActivityId),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",1==t.currentlySelectedActivityId),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",3==t.currentlySelectedActivityId),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",4==t.currentlySelectedActivityId),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",7==t.currentlySelectedActivityId),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("activityId",t.activityId),o["\u0275\u0275advance"](14),o["\u0275\u0275property"]("comments",t.comment))},directives:[i.NgIf,Ht,l.c,l.a,Zt,$e.e,$e.a,$e.d,pn,mn.a,un,vn,gn,ni,ui],styles:[".example-viewport[_ngcontent-%COMP%]{min-height:558px}.empty-styles[_ngcontent-%COMP%]{color:#3c3c3c;font-weight:500;font-size:14px}.slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}.slide-in-right[_ngcontent-%COMP%]{animation:slide-in-right .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-right{0%{transform:translateX(30px);opacity:0}to{transform:translateX(0);opacity:1}}"]}),e})();var Ci=n("WU5D");function wi(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"button",41),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](e);const t=o["\u0275\u0275nextContext"](2);return t.view="general",t.refreshList()})),o["\u0275\u0275elementStart"](1,"mat-icon",42),o["\u0275\u0275text"](2," chevron_left "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}}function Ei(e,t){1&e&&o["\u0275\u0275element"](0,"mat-spinner",43)}function Ii(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"button",44),o["\u0275\u0275listener"]("click",(function(){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"](2).clearSearch()})),o["\u0275\u0275elementStart"](1,"mat-icon",45),o["\u0275\u0275text"](2,"close "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}}function ki(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div"),o["\u0275\u0275elementStart"](1,"p"),o["\u0275\u0275text"](2,"Hello"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275property"]("@slideInOut",e.showfilter?"in":"out")}}function Oi(e,t){1&e&&o["\u0275\u0275element"](0,"app-activity-general-view")}function Di(e,t){if(1&e&&o["\u0275\u0275element"](0,"app-activity-details",46),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275property"]("scrollToIndex",e.scrollToIndex)}}function Mi(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div"),o["\u0275\u0275elementStart"](1,"div",1),o["\u0275\u0275elementStart"](2,"div",2),o["\u0275\u0275elementStart"](3,"div",3),o["\u0275\u0275elementStart"](4,"div",4),o["\u0275\u0275elementStart"](5,"span"),o["\u0275\u0275template"](6,wi,3,0,"button",5),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](7,"span",6),o["\u0275\u0275text"](8,"Total Activities :"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](9,"span",7),o["\u0275\u0275text"](10),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](11,"div",8),o["\u0275\u0275elementStart"](12,"mat-form-field",9),o["\u0275\u0275elementStart"](13,"span",10),o["\u0275\u0275elementStart"](14,"mat-icon",11),o["\u0275\u0275text"](15,"search"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275element"](16,"input",12),o["\u0275\u0275elementStart"](17,"mat-icon",13),o["\u0275\u0275template"](18,Ei,1,0,"mat-spinner",14),o["\u0275\u0275template"](19,Ii,3,0,"button",15),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](20,"div",16),o["\u0275\u0275elementStart"](21,"span",17),o["\u0275\u0275elementStart"](22,"button",18),o["\u0275\u0275elementStart"](23,"mat-icon",19),o["\u0275\u0275text"](24,"add"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](25,"mat-menu",20,21),o["\u0275\u0275elementStart"](27,"div",22),o["\u0275\u0275elementStart"](28,"div",23),o["\u0275\u0275elementStart"](29,"div",24),o["\u0275\u0275elementStart"](30,"span",25),o["\u0275\u0275text"](31,"Lead activities"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](32,"div",26),o["\u0275\u0275elementStart"](33,"div",27),o["\u0275\u0275listener"]("click",(function(){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"]().createMeeting()})),o["\u0275\u0275elementStart"](34,"div",28),o["\u0275\u0275elementStart"](35,"div",29),o["\u0275\u0275elementStart"](36,"mat-icon",30),o["\u0275\u0275text"](37," date_range "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](38,"div",28),o["\u0275\u0275elementStart"](39,"div",31),o["\u0275\u0275text"](40," New Meeting "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](41,"div",32),o["\u0275\u0275listener"]("click",(function(){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"]().createCallLog()})),o["\u0275\u0275elementStart"](42,"div",33),o["\u0275\u0275elementStart"](43,"div",29),o["\u0275\u0275elementStart"](44,"mat-icon",34),o["\u0275\u0275text"](45," call "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](46,"div",28),o["\u0275\u0275elementStart"](47,"div",31),o["\u0275\u0275text"](48," New Call log "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](49,"div",26),o["\u0275\u0275elementStart"](50,"div",27),o["\u0275\u0275listener"]("click",(function(){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"]().createMail()})),o["\u0275\u0275elementStart"](51,"div",28),o["\u0275\u0275elementStart"](52,"div",29),o["\u0275\u0275elementStart"](53,"mat-icon",35),o["\u0275\u0275text"](54," mail "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](55,"div",28),o["\u0275\u0275elementStart"](56,"div",31),o["\u0275\u0275text"](57," New Mail "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](58,"div",32),o["\u0275\u0275listener"]("click",(function(){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"]().createNewTask()})),o["\u0275\u0275elementStart"](59,"div",33),o["\u0275\u0275elementStart"](60,"div",29),o["\u0275\u0275elementStart"](61,"mat-icon",36),o["\u0275\u0275text"](62," assignment "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](63,"div",28),o["\u0275\u0275elementStart"](64,"div",31),o["\u0275\u0275text"](65," New Task "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](66,"div",26),o["\u0275\u0275elementStart"](67,"div",27),o["\u0275\u0275listener"]("click",(function(){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"]().adaptTemplate()})),o["\u0275\u0275elementStart"](68,"div",28),o["\u0275\u0275elementStart"](69,"div",29),o["\u0275\u0275elementStart"](70,"mat-icon",37),o["\u0275\u0275text"](71," assignment "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](72,"div",28),o["\u0275\u0275elementStart"](73,"div",31),o["\u0275\u0275text"](74," Adapt from Template "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](75,ki,3,1,"div",0),o["\u0275\u0275elementContainerStart"](76,38),o["\u0275\u0275template"](77,Oi,1,0,"app-activity-general-view",39),o["\u0275\u0275elementContainerEnd"](),o["\u0275\u0275elementContainerStart"](78,38),o["\u0275\u0275template"](79,Di,1,1,"app-activity-details",40),o["\u0275\u0275elementContainerEnd"](),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275reference"](26),t=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](6),o["\u0275\u0275property"]("ngIf","Detailed"==t.view),o["\u0275\u0275advance"](4),o["\u0275\u0275textInterpolate1"](" ",t.leadActivityIdList?t.leadActivityIdList.length:0," "),o["\u0275\u0275advance"](6),o["\u0275\u0275property"]("formControl",t.searchTextControl),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngIf",1==t.isloading),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",0==t.isloading&&1==t.isClearVisible),o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("matMenuTriggerFor",e),o["\u0275\u0275advance"](53),o["\u0275\u0275property"]("ngIf",1==t.showfilter),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngSwitch",t.view),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngSwitchCase","general"),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngSwitch",t.view),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngSwitchCase","Detailed")}}function Pi(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div"),o["\u0275\u0275element"](1,"app-crm-udrf-activity-landing-page",47),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("application_name",e.application_id)("application_reference_id",e.application_reference_id)}}let Ti=(()=>{class e{constructor(e,t,n,i,a,o,l){this.activityToMainService=e,this.leadsService=t,this.snackBar=n,this.reloadService=i,this.dialog=a,this.uploadService=o,this.createLeadsService=l,this.showfilter=!1,this.view="general",this.searchTextControl=new Me.j,this.isClearVisible=!1,this.isloading=!1,this.newDisplayUI=""==this.leadsService.getLabelForOpportunity(34,13)?"Leads New Activity UI":this.leadsService.getLabelForOpportunity(34,13),this.createActivity=e=>{this.activityToMainService.sendMsg(e)},this.adaptTemplate=()=>Object(d.c)(this,void 0,void 0,(function*(){let e=yield this.leadsService.getLeadsCardDetails(this.leadId);e=e&&e.length>0?e[0]:e||"",this.uploadService.setAppId(35),this.dialog.open(Pe.a,{minHeight:"450px",minWidth:"500px",maxHeight:"95%",maxWidth:"95%"}).afterClosed().subscribe(t=>Object(d.c)(this,void 0,void 0,(function*(){t=JSON.parse(JSON.stringify(t));let n=c.filter(t.templateItems,e=>"checked"==e.selected||"indeterminate"==e.selected);console.log(n);let i={owner:{I:e.lead_owner_oid,M:e.marketing_owner_oid},startDate:e.created_on,endDate:e.leads_closure_date},a=yield this.createLeadsService.dataFormatter(n,i,[t.templateId]);this.createLeadsService.setTemplateItems(a);let o=yield this.createLeadsService.dataConverter();yield this.createLeadsService.insertActivitiesTemplate(o,this.leadId).toPromise(),this.refreshList()})))}))}ngOnInit(){return Object(d.c)(this,void 0,void 0,(function*(){this.filterMaster=yield this.getFilterMaster(),this.application_id=35,this.leadId=window.location.pathname.split("/")[3],this.application_reference_id=this.leadId,"Leads New Activity UI"!=this.newDisplayUI&&(this.activityToMainService.getMsg().subscribe(e=>{"giveMeDetailedView"===e.split("-")[0]?(this.view="Detailed",this.scrollToIndex=e.split("-")[1]):"reload"==e&&this.refreshList()}),this.leadsService.activityList(this.application_id,this.application_reference_id).then(e=>{this.leadActivityIdList=e,this.reloadService.sendSearchContent(this.leadActivityIdList)},e=>{console.log(e),this.snackBar.open("Unable to fetch Activity List!","Dismiss",{duration:2e3})}),this.searchResultSubscription=yield this.searchTextControl.valueChanges.pipe(Object(De.a)(1e3)).subscribe(e=>Object(d.c)(this,void 0,void 0,(function*(){e.length>0?(console.log("length > 0"),console.log(e),console.log(this.leadId),this.isloading=!0,this.leadsService.searchLeadActivity(e,this.leadId).then(e=>{this.leadActivityIdList=e,console.log("HI"),this.reloadService.sendSearchContent(this.leadActivityIdList),console.log(this.leadActivityIdList),this.isloading=!1,this.isClearVisible=!0})):(console.log("length = 0"),this.isClearVisible=!1,this.leadActivityIdList=this.leadsService.activityList(this.application_id,this.application_reference_id).then(e=>{this.leadActivityIdList=e,this.reloadService.sendSearchContent(this.leadActivityIdList)},e=>{console.log(e),this.snackBar.open("Unable to fetch Activity List!","Dismiss",{duration:2e3})}))})))),yield this.leadsService.getLeadsCardDetails(this.leadId).then(e=>{this.leadDetails=e}),this.leadsService.reloadSubject.subscribe(e=>{this.refreshList()})}))}refreshList(){this.isClearVisible=!1,this.leadActivityIdList=this.leadsService.activityList(this.application_id,this.application_reference_id).then(e=>{this.leadActivityIdList=e,this.reloadService.sendSearchContent(this.leadActivityIdList)},e=>{console.log(e),this.snackBar.open("Unable to fetch Activity List!","Dismiss",{duration:2e3})})}openFilter(){this.showfilter=!this.showfilter}getFilterData(e){}getFilterMaster(){return new Promise((e,t)=>{this.leadsService.getActivityFilterMasterDate().subscribe(t=>{console.log(t),e(t)},e=>{console.error(e),t(e)})})}createCallLog(){return Object(d.c)(this,void 0,void 0,(function*(){this.dialog.open(m.a,{height:"80%",width:"80%",data:{tabbed_content_type:2,customer_id:this.leadDetails.in_account,mode:"Create",application_reference_id:this.leadId,application_id:35}}).afterClosed().subscribe(e=>{this.refreshList()})}))}createMeeting(){return Object(d.c)(this,void 0,void 0,(function*(){this.dialog.open(m.a,{height:"80%",width:"80%",data:{tabbed_content_type:1,customer_id:this.leadDetails.in_account,mode:"Create",application_reference_id:this.leadId,application_id:35}}).afterClosed().subscribe(e=>{this.refreshList()})}))}createMail(){return Object(d.c)(this,void 0,void 0,(function*(){this.dialog.open(m.a,{height:"80%",width:"80%",data:{tabbed_content_type:3,customer_id:this.leadDetails.in_account,mode:"Create",application_reference_id:this.leadId,application_id:35}}).afterClosed().subscribe(e=>{this.refreshList()})}))}createNewTask(){return Object(d.c)(this,void 0,void 0,(function*(){this.dialog.open(m.a,{height:"80%",width:"80%",data:{tabbed_content_type:4,customer_id:this.leadDetails.in_account,mode:"Create",application_reference_id:this.leadId,application_id:35}}).afterClosed().subscribe(e=>{this.refreshList()})}))}clearSearch(){return Object(d.c)(this,void 0,void 0,(function*(){this.searchTextControl.patchValue(""),this.leadActivityIdList=this.leadsService.activityList(this.application_id,this.application_reference_id).then(e=>{this.leadActivityIdList=e,console.log(e)},e=>{console.log(e),this.snackBar.open("Unable to fetch Activity List!","Dismiss",{duration:2e3})}),this.isloading=!1,this.isClearVisible=!1}))}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](Ae.a),o["\u0275\u0275directiveInject"](u.a),o["\u0275\u0275directiveInject"](h.a),o["\u0275\u0275directiveInject"](Fe.a),o["\u0275\u0275directiveInject"](y.b),o["\u0275\u0275directiveInject"](Ne.a),o["\u0275\u0275directiveInject"](Le.a))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-leads-activities"]],decls:2,vars:2,consts:[[4,"ngIf"],[1,"row","lead-activitities"],[1,"col-12","pl-0","pr-0"],[1,"row"],[1,"col-4","d-flex","pt-3","pl-2"],["class"," icon-tray-button","mat-icon-button","",3,"click",4,"ngIf"],[1,"mediumSubtleText","pl-3"],[1,"heading","pl-2"],[1,"d-flex","col","col-5","search-bar"],["appearance","outline",1,"ml-auto"],["matPrefix",""],[2,"font-size","21px !important","color","#66615b !important"],["matInput","","type","search","name","search","placeholder","Search Activity",3,"formControl"],["matSuffix",""],["diameter","18","class","mt-2",4,"ngIf"],["mat-button","","matSuffix","","mat-icon-button","","aria-label","Clear","style","height: 30px; width: 30px; line-height: 1",3,"click",4,"ngIf"],[1,"col-3","d-flex","pt-2"],[1,"pr-2"],["mat-icon-button","","matTooltip","Add Activity",1,"trend-button-inactive",3,"matMenuTriggerFor"],[1,"iconButton"],[1,"new-items-panel"],["newitems","matMenu"],[1,"card"],[1,"card-body","p-0"],[1,"row","pb-1","pl-2","pr-2","pt-1","pb-1",2,"min-width","200px","background-color","#e6e6e6"],[2,"font-size","12px !important","font-weight","normal !important","color","#1a1a1a !important"],[1,"row","pt-2","pb-2"],["matRipple","",1,"col-6","pl-0","pr-0","col-styles",3,"click"],[1,"row","d-flex"],[1,"mx-auto",2,"min-height","26px","min-width","26px"],[1,"pt-1","mx-auto",2,"color","#B71540","font-size","21px !important"],[1,"mx-auto",2,"color","#1a1a1a","font-size","10px","font-weight","normal"],["matRipple","",1,"col-6","pl-0","pr-0","col-styles",2,"cursor","pointer",3,"click"],[1,"row","d"],[1,"pt-1",2,"color","#227093","font-size","21px !important"],[1,"pt-1","mx-auto",2,"color","#006266","font-size","21px !important"],[1,"pt-1",2,"color","#30336B","font-size","21px !important"],[1,"pt-1","mx-auto",2,"color","#30336B","font-size","21px !important"],[3,"ngSwitch"],[4,"ngSwitchCase"],[3,"scrollToIndex",4,"ngSwitchCase"],["mat-icon-button","",1,"icon-tray-button",3,"click"],[1,"smallCardIcon"],["diameter","18",1,"mt-2"],["mat-button","","matSuffix","","mat-icon-button","","aria-label","Clear",2,"height","30px","width","30px","line-height","1",3,"click"],["matTooltip","Clear search",2,"font-size","18px !important","color","#66615b !important"],[3,"scrollToIndex"],[3,"application_name","application_reference_id"]],template:function(e,t){1&e&&(o["\u0275\u0275template"](0,Mi,80,11,"div",0),o["\u0275\u0275template"](1,Pi,2,2,"div",0)),2&e&&(o["\u0275\u0275property"]("ngIf","Leads New Activity UI"!=t.newDisplayUI),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf","Leads New Activity UI"==t.newDisplayUI&&t.application_id&&t.application_reference_id))},directives:[i.NgIf,je.c,je.h,E.a,Be.b,Me.e,Me.v,Me.k,je.i,_.a,C.a,w.f,w.g,i.NgSwitch,i.NgSwitchCase,Ve.c,It,_i,Ci.a],styles:[".lead-activitities[_ngcontent-%COMP%]   .mediumSubtleText[_ngcontent-%COMP%]{font-size:14px;color:#66615b}.lead-activitities[_ngcontent-%COMP%]   .smallCardIcon[_ngcontent-%COMP%]{font-size:26px!important;color:#868683!important;margin-bottom:5px!important}.lead-activitities[_ngcontent-%COMP%]   .icon-tray-button[_ngcontent-%COMP%]{width:27px!important;height:27px!important;line-height:25px!important;visibility:visible}.lead-activitities[_ngcontent-%COMP%]   .smallSubtleText[_ngcontent-%COMP%]{font-size:11px;color:#66615b}.lead-activitities[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field{width:40vw;padding-top:5px}.lead-activitities[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-infix{font-size:14px!important;padding:.5em 0!important;border-top:.54375em solid transparent!important}.lead-activitities[_ngcontent-%COMP%]   .trend-button-inactive[_ngcontent-%COMP%]{margin-top:5px!important;line-height:8px;width:26px;height:26px;margin-right:5px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.lead-activitities[_ngcontent-%COMP%]   .trend-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.lead-activitities[_ngcontent-%COMP%]   .trend-button-inactive[_ngcontent-%COMP%]   .canManView[_ngcontent-%COMP%]{color:#66615b;font-size:21px}.lead-activitities[_ngcontent-%COMP%]   .trend-button-active[_ngcontent-%COMP%]{background-color:#cf0001!important;margin-top:5px!important;line-height:8px;width:26px;height:26px;margin-right:5px!important;box-shadow:0 3px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.lead-activitities[_ngcontent-%COMP%]   .trend-button-active[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#fff;font-size:18px}.lead-activitities[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.lead-activitities[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]{color:#1a1a1a;font-size:14px;font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.lead-activitities[_ngcontent-%COMP%]     .new-items-panel .mat-menu-content .col-styles{cursor:pointer!important;border-radius:4px!important}.lead-activitities[_ngcontent-%COMP%]     .new-items-panel .mat-menu-content .col-styles:hover{background-color:rgba(255,235,235,.6705882352941176)!important}.lead-activitities[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#3c3c3c;font-weight:400;font-size:22px}.lead-activitities[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}.lead-activitities[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}"],data:{animation:[Object(Te.o)("slideInOut",[Object(Te.l)("in",Object(Te.m)({height:"*",overflow:"hidden"})),Object(Te.l)("out",Object(Te.m)({height:0,overflow:"hidden"})),Object(Te.n)("* => in",[Object(Te.m)({height:0}),Object(Te.e)(250,Object(Te.m)({height:"*"}))]),Object(Te.n)("in=> *",[Object(Te.m)({height:"*"}),Object(Te.e)(250,Object(Te.m)({height:0}))])]),Object(Te.o)("smallCardAnimation",[Object(Te.n)("* => *",[Object(Te.i)(":leave",[Object(Te.k)(100,[Object(Te.e)("0.5s",Object(Te.m)({opacity:0}))])],{optional:!0}),Object(Te.i)(":enter",[Object(Te.m)({opacity:0}),Object(Te.k)(100,[Object(Te.e)("0.5s",Object(Te.m)({opacity:1}))])],{optional:!0})])])]}}),e})();var Ai=n("B0y8"),Fi=n("wO+i"),Ni=n("m5YA");function Li(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"div"),o["\u0275\u0275element"](1,"mat-progress-bar",6),o["\u0275\u0275elementEnd"]())}function ji(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div",7),o["\u0275\u0275elementStart"](1,"div",8),o["\u0275\u0275elementStart"](2,"div"),o["\u0275\u0275elementStart"](3,"p",9),o["\u0275\u0275text"](4," Let's create an attachment! "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](5,"div",10),o["\u0275\u0275elementStart"](6,"button",11),o["\u0275\u0275listener"]("click",(function(){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"]().openPopUp()})),o["\u0275\u0275text"](7," Add attachments "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](8,"div",10),o["\u0275\u0275element"](9,"img",12),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}}function Bi(e,t){1&e&&o["\u0275\u0275element"](0,"img",27)}function Vi(e,t){1&e&&o["\u0275\u0275element"](0,"img",28)}function Ui(e,t){1&e&&o["\u0275\u0275element"](0,"img",29)}function zi(e,t){1&e&&o["\u0275\u0275element"](0,"img",30)}function $i(e,t){1&e&&o["\u0275\u0275element"](0,"img",31)}function qi(e,t){1&e&&o["\u0275\u0275element"](0,"img",32)}function Ri(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div",15),o["\u0275\u0275elementStart"](1,"button",16),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](e);const n=t.$implicit,i=o["\u0275\u0275nextContext"](2);return i.deleteFile(n),i.openSnackBar()})),o["\u0275\u0275elementStart"](2,"mat-icon",17),o["\u0275\u0275text"](3,"delete"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](4,"div",18),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](e);const n=t.$implicit;return o["\u0275\u0275nextContext"](2).viewFile(n)})),o["\u0275\u0275template"](5,Bi,1,0,"img",19),o["\u0275\u0275template"](6,Vi,1,0,"img",20),o["\u0275\u0275template"](7,Ui,1,0,"img",21),o["\u0275\u0275template"](8,zi,1,0,"img",22),o["\u0275\u0275template"](9,$i,1,0,"img",23),o["\u0275\u0275template"](10,qi,1,0,"img",24),o["\u0275\u0275elementStart"](11,"div",25),o["\u0275\u0275elementStart"](12,"span",26),o["\u0275\u0275text"](13),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](14,"span"),o["\u0275\u0275text"](15),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;o["\u0275\u0275advance"](5),o["\u0275\u0275property"]("ngIf","xlsx"===e.file_format),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf","txt"===e.file_format),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf","pdf"===e.file_format),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf","doc"===e.file_format),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf","png"===e.file_format),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf","png"!=e.file_format&&"txt"!=e.file_format&&"pdf"!=e.file_format&&"doc"!=e.file_format&&"xlsx"!=e.file_format),o["\u0275\u0275advance"](3),o["\u0275\u0275textInterpolate"](e.file_name),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate1"]("",e.size/1e3," MB")}}function Hi(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div"),o["\u0275\u0275elementStart"](1,"div",13),o["\u0275\u0275template"](2,Ri,16,8,"div",14),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngForOf",e.filesUploaded)}}const Ki=[{path:"",component:s,children:[{path:"",redirectTo:"overview",pathMatch:"full"},{path:"overview",component:Oe},{path:":leadId/activities",children:[{path:"",component:Ti},{path:"activityDetails/:ActivityId",component:_i}]},{path:"attachments",component:(()=>{class e{constructor(e,t,n,i,a,o){this.UtilityService=e,this.snackBar=t,this.route=n,this.leadsService=i,this.dialog=a,this._sharedService=o,this.attachmentCount=0,this.contextId="35",this.fileArray=[],this.urls={uploadUrl:"/api/leads/uploadLeadsAttachment",downloadUrl:"api/leads/downloadLeadsAttachment"},this.filesUploaded=[],this.isLoading=!0,this.deleteFile=e=>{this._sharedService.deleteObj(e,"t_app_attachments_meta").subscribe(e=>{this.filesUploaded.splice(0),this.retrieveUploadedObjects()},e=>{console.error(e)})},this.viewFile=e=>{this._sharedService.getDownloadUrl(e.cdn_link).subscribe(t=>{this.dialog.open(Ni.a,{width:"100%",height:"90%",data:{selectedFileUrl:t.data,fileFormat:e.file_format}})})}}ngOnInit(){return Object(d.c)(this,void 0,void 0,(function*(){yield this.route.parent.params.subscribe(e=>Object(d.c)(this,void 0,void 0,(function*(){console.log(e),this.leadsId=e.LeadId,this.contextId=this.contextId+this.leadsId,console.log("contextId",this.contextId)}))),yield this.leadsService.getCRMAttachmentsConfigs().then(e=>{console.log(e);let t=e.data.aws.attachment_plugin_config;console.log(t),this.crmAttachmentConfig=t}),yield this.retrieveUploadedObjects()}))}openSnackBar(){this.snackBar.open("File deleted Successfully","close",{duration:1e3})}retrieveUploadedObjects(){return Object(d.c)(this,void 0,void 0,(function*(){this._sharedService.retrieveUploadedObjects(this.crmAttachmentConfig.destination_bucket,this.contextId).pipe(Object(Fi.a)("data")).subscribe(e=>{this.attachmentCount=e.length,this.filesUploaded.push(...e),this.isLoading=!1},e=>{console.error(e),this.isLoading=!1})}))}openPopUp(){console.log(this.crmAttachmentConfig),this.dialog.open(Ai.a,{width:"100%",height:"90%",data:{data:{destinationBucket:this.crmAttachmentConfig.destination_bucket,routingKey:this.crmAttachmentConfig.routing_key,contextId:this.contextId,allowEdit:!0,myFilesDefaultFolder:[]}},disableClose:!0}).afterClosed().subscribe(e=>{this.attachmentCount=e.fileCount,this.filesUploaded.splice(0),this.retrieveUploadedObjects()})}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](f.a),o["\u0275\u0275directiveInject"](h.a),o["\u0275\u0275directiveInject"](a.a),o["\u0275\u0275directiveInject"](u.a),o["\u0275\u0275directiveInject"](y.b),o["\u0275\u0275directiveInject"](g.a))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-leads-attachment"]],decls:10,vars:4,consts:[[1,"container-fluid","row","pt-2","pb-2"],[1,"tileName","pr-2"],["mat-icon-button","","matTooltip","Add attachments",1,"more-button","ml-auto",3,"click"],[1,"smallCardIcon"],[4,"ngIf"],["class","row slide-from-down no-attachemnts","style","min-height: 250px !important;",4,"ngIf"],["mode","indeterminate"],[1,"row","slide-from-down","no-attachemnts",2,"min-height","250px !important"],[1,"col-12"],[1,"empty-styles","d-flex","justify-content-center","align-items-center","mt-1"],[1,"d-flex","justify-content-center","align-items-center"],["mat-raised-button","",1,"complete",3,"click"],["src","https://assets.kebs.app/images/noAccounts.png","height","190","width","190",1,"mt-4"],[1,"d-flex","flex-wrap","p-2","card-data"],["class","card",4,"ngFor","ngForOf"],[1,"card"],["mat-icon-button","",2,"position","absolute","top","-5px","right","-5px","background-color","#cf0000be","height","30px","width","30px",3,"click"],[2,"color","white","font-size","18px","vertical-align","text-bottom"],[3,"click"],["class","d-block card-img-top","src","https://assets.kebs.app/lms/svgs/excel.svg","alt","Not found",4,"ngIf"],["class","d-block card-img-top","src","https://assets.kebs.app/lms/svgs/txt.svg","alt","Not found",4,"ngIf"],["class","d-block card-img-top","src","https://assets.kebs.app/lms/svgs/pdf.svg","alt","Not found",4,"ngIf"],["class","d-block card-img-top","src","https://assets.kebs.app/lms/svgs/word.svg","alt","Not found",4,"ngIf"],["class","d-block card-img-top","src","https://assets.kebs.app/lms/svgs/png.svg","alt","Not found",4,"ngIf"],["class","d-block card-img-top","src","https://assets.kebs.app/lms/svgs/file.svg","alt","Not found",4,"ngIf"],[1,"card-body"],[1,"card-title","d-block","p-0","m-0"],["src","https://assets.kebs.app/lms/svgs/excel.svg","alt","Not found",1,"d-block","card-img-top"],["src","https://assets.kebs.app/lms/svgs/txt.svg","alt","Not found",1,"d-block","card-img-top"],["src","https://assets.kebs.app/lms/svgs/pdf.svg","alt","Not found",1,"d-block","card-img-top"],["src","https://assets.kebs.app/lms/svgs/word.svg","alt","Not found",1,"d-block","card-img-top"],["src","https://assets.kebs.app/lms/svgs/png.svg","alt","Not found",1,"d-block","card-img-top"],["src","https://assets.kebs.app/lms/svgs/file.svg","alt","Not found",1,"d-block","card-img-top"]],template:function(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"div",0),o["\u0275\u0275elementStart"](1,"span",1),o["\u0275\u0275text"](2),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](3,"span"),o["\u0275\u0275elementStart"](4,"button",2),o["\u0275\u0275listener"]("click",(function(){return t.openPopUp()})),o["\u0275\u0275elementStart"](5,"mat-icon",3),o["\u0275\u0275text"](6,"add"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](7,Li,2,0,"div",4),o["\u0275\u0275template"](8,ji,10,0,"div",5),o["\u0275\u0275template"](9,Hi,3,1,"div",4)),2&e&&(o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate1"](" Attachments : ",t.attachmentCount," "),o["\u0275\u0275advance"](5),o["\u0275\u0275property"]("ngIf",t.isLoading),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",0==t.attachmentCount&&!t.isLoading),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",t.attachmentCount>0))},directives:[_.a,C.a,E.a,i.NgIf,mn.a,i.NgForOf],styles:[".slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}.no-attachemnts[_ngcontent-%COMP%]   .empty-styles[_ngcontent-%COMP%]{color:#3c3c3c;font-weight:500;font-size:14px}.no-attachemnts[_ngcontent-%COMP%]   .complete[_ngcontent-%COMP%]{color:#fff;font-weight:400;font-size:12px!important;background-color:#cf0001;min-width:62px;line-height:26px;padding:0 10px}.container-fluid[_ngcontent-%COMP%]   .more-button[_ngcontent-%COMP%]{width:27px!important;height:27px!important;line-height:25px!important}.container-fluid[_ngcontent-%COMP%]   .tileName[_ngcontent-%COMP%]{color:#9a9a9a;font-size:14px!important}.container-fluid[_ngcontent-%COMP%]   .smallCardIcon[_ngcontent-%COMP%]{font-size:18px!important;color:#868683!important}.list[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;flex-direction:row}.list[_ngcontent-%COMP%]   .flex-grid[_ngcontent-%COMP%]{flex:1 1 30%}@media (max-width:800px){.list[_ngcontent-%COMP%]   .flex-grid[_ngcontent-%COMP%]{flex:1 1 40%}}.card-data[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]{box-shadow:0 1px 3px rgba(0,0,0,.12),0 1px 2px rgba(0,0,0,.24);width:156px;margin:2px}.card-data[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]{text-align:center;padding:0}.card-data[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .card-img-top[_ngcontent-%COMP%]{width:30%;padding:10px 0;margin:0 auto}.card-data[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%]{font-size:14px;font-weight:300;color:#66615b;transition:all .1s}.card-data[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:12px;font-weight:300;color:#66615b;transition:all .1s}.card-data[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{display:none}.card-data[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]:hover{cursor:pointer}.card-data[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]:hover   .card-title[_ngcontent-%COMP%], .card-data[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]:hover   span[_ngcontent-%COMP%]{color:#cf0001}.card-data[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]:hover   button[_ngcontent-%COMP%]{display:block}"]}),e})()}]}];let Yi=(()=>{class e{}return e.\u0275mod=o["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=o["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[a.k.forChild(Ki)],a.k]}),e})();var Wi=n("lVl8"),Gi=n("dlKe"),Xi=n("XhcP"),Ji=n("bSwM"),Qi=n("KHjE"),Zi=n("3beV"),ea=n("1yaQ"),ta=n("Uu0G"),na=n("GN7+"),ia=n("By2N");let aa=(()=>{class e{}return e.\u0275mod=o["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=o["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,Yi,je.e,E.b,w.e,Wi.b,Gi.b,Be.c,Me.p,Me.E,_.b,C.b,ze.d,Ve.b,$e.g,l.g,y.g,bn.d,Xi.g,Ji.b,Qi.a,_n.n,ea.b,Nn.h,On.c,Zi.a,Ot.b,ta.a,na.a,ia.a,mn.b]]}),e})()},By2N:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var i=n("ofXK"),a=n("fXoL");let o=(()=>{class e{}return e.\u0275mod=a["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=a["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule]]}),e})()},NJ67:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));class i{constructor(){this.disabled=!1}onChange(e){}onTouched(e){}writeValue(e){this.value=e}registerOnChange(e){this.onChange=e}registerOnTouched(e){this.onTouched=e}setDisabledState(e){this.disabled=e}}},R3G1:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var i=n("xG9w"),a=n("fXoL");let o=(()=>{class e{transform(e,t,n){let a=i.findWhere(t,{field_name:e,type:n});return!!a&&!!a.is_active}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=a["\u0275\u0275definePipe"]({name:"checkActive",type:e,pure:!0}),e})()},"TmG/":function(e,t,n){"use strict";n.d(t,"a",(function(){return y}));var i=n("fXoL"),a=n("3Pt+"),o=n("jtHE"),l=n("XNiG"),r=n("NJ67"),s=n("1G5W"),d=n("kmnG"),c=n("ofXK"),p=n("d3UM"),m=n("FKr1"),u=n("WJ5W"),h=n("Qu3c");function v(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-label"),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](e.placeholder)}}function g(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-option",7),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275property"]("value",null),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](e.disableNone?"Select One":"None")}}function f(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"mat-option",8),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const n=t.$implicit;return i["\u0275\u0275nextContext"]().emitChanges(n)})),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;i["\u0275\u0275property"]("value",e.id||e._id)("matTooltip",e.name),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let y=(()=>{class e extends r.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new a.j,this.fieldFilterCtrl=new a.j,this.list=[],this.required=!1,this.disableNone=!1,this.valueChange=new i.EventEmitter,this.disabled=!1,this.hasNoneOption=!0,this.filteredList=new o.a,this.change=new i.EventEmitter,this.hideMatLabel=!1,this._onDestroy=new l.b,this.emitChanges=e=>{this.change.emit(e)}}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(s.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(s.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](i.Renderer2))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search"]],inputs:{list:"list",placeholder:"placeholder",required:"required",disableNone:"disableNone",disabled:"disabled",hasNoneOption:"hasNoneOption",hideMatLabel:"hideMatLabel"},outputs:{valueChange:"valueChange",change:"change"},features:[i["\u0275\u0275ProvidersFeature"]([{provide:a.t,useExisting:Object(i.forwardRef)(()=>e),multi:!0}]),i["\u0275\u0275InheritDefinitionFeature"],i["\u0275\u0275NgOnChangesFeature"]],decls:9,vars:11,consts:[["appearance","outline"],[4,"ngIf"],[3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value",4,"ngIf"],[3,"value","matTooltip","click",4,"ngFor","ngForOf"],[3,"value"],[3,"value","matTooltip","click"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"mat-form-field",0),i["\u0275\u0275template"](1,v,2,1,"mat-label",1),i["\u0275\u0275elementStart"](2,"mat-select",2,3),i["\u0275\u0275elementStart"](4,"mat-option"),i["\u0275\u0275element"](5,"ngx-mat-select-search",4),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](6,g,2,2,"mat-option",5),i["\u0275\u0275template"](7,f,2,3,"mat-option",6),i["\u0275\u0275pipe"](8,"async"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",!t.hideMatLabel),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.hasNoneOption),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngForOf",i["\u0275\u0275pipeBind1"](8,9,t.filteredList)))},directives:[d.c,c.NgIf,p.c,a.v,a.k,a.F,m.p,u.a,c.NgForOf,d.g,h.a],pipes:[c.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%}"]}),e})()},os0P:function(e,t,n){"use strict";n.d(t,"a",(function(){return b}));var i=n("mrSG"),a=n("fXoL"),o=n("3Pt+"),l=n("jtHE"),r=n("XNiG"),s=n("NJ67"),d=n("1G5W"),c=n("xG9w"),p=n("t44d"),m=n("kmnG"),u=n("ofXK"),h=n("d3UM"),v=n("FKr1"),g=n("WJ5W"),f=n("Qu3c");const y=["singleSelect"];function x(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"mat-option",6),a["\u0275\u0275text"](1,"Select one"),a["\u0275\u0275elementEnd"]()),2&e&&a["\u0275\u0275property"]("value",null)}function S(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"mat-option",7),a["\u0275\u0275listener"]("click",(function(){a["\u0275\u0275restoreView"](e);const n=t.$implicit;return a["\u0275\u0275nextContext"]().emitChanges(n)})),a["\u0275\u0275text"](1),a["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;a["\u0275\u0275propertyInterpolate"]("matTooltip",e.name),a["\u0275\u0275property"]("value",e.id),a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let b=(()=>{class e extends s.a{constructor(e,t){super(),this.renderer=e,this.pmMasterService=t,this.fieldCtrl=new o.j,this.fieldFilterCtrl=new o.j,this.list=[],this.required=!1,this.valueChange=new a.EventEmitter,this.disabled=!1,this.disableColor="#E8E9EE",this.disableField=!1,this.showSelect=!0,this.uniqueList=[],this.filteredList=new l.a,this.change=new a.EventEmitter,this._onDestroy=new r.b,this.formConfig=[],this.emitChanges=e=>{this.change.emit(e)}}ngOnInit(){return Object(i.c)(this,void 0,void 0,(function*(){yield this.pmMasterService.getPMFormCustomizeConfigV().then(e=>{e&&(this.formConfig=e)});const e=c.where(this.formConfig,{type:"project-theme",field_name:"styles",is_active:!0});this.fontStyle=e.length>0&&e[0].data.font_style?e[0].data.font_style:"Roboto",document.documentElement.style.setProperty("--inputSearchFont",this.fontStyle),this.fieldFilterCtrl.valueChanges.pipe(Object(d.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(d.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)}),this.updateFilteredList()}))}ngOnChanges(){this.updateFilteredList()}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value,t=this.list.slice();e&&(e=e.toLowerCase(),t=t.filter(t=>t.name.toLowerCase().indexOf(e)>-1)),this.uniqueList.length>0&&(t=t.filter(e=>this.uniqueList.includes(e))),this.filteredList.next(t)}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}isDisabled(){return this.disableField?{"background-color":null!=this.disableColor?this.disableColor:"#E8E9EE","pointer-events":"none",color:"#6E7B8F"}:{}}writeValue(e){this.fieldCtrl.setValue(e)}updateFilteredList(){this.uniqueList.length>0?this.filterBanks():this.filteredList.next(this.list.slice())}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275directiveInject"](a.Renderer2),a["\u0275\u0275directiveInject"](p.a))},e.\u0275cmp=a["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search-name"]],viewQuery:function(e,t){if(1&e&&a["\u0275\u0275viewQuery"](y,!0),2&e){let e;a["\u0275\u0275queryRefresh"](e=a["\u0275\u0275loadQuery"]())&&(t.singleSelect=e.first)}},inputs:{list:"list",placeholder:"placeholder",required:"required",disabled:"disabled",disableColor:"disableColor",disableField:"disableField",showSelect:"showSelect",uniqueList:"uniqueList"},outputs:{valueChange:"valueChange",change:"change"},features:[a["\u0275\u0275ProvidersFeature"]([{provide:o.t,useExisting:Object(a.forwardRef)(()=>e),multi:!0}]),a["\u0275\u0275InheritDefinitionFeature"],a["\u0275\u0275NgOnChangesFeature"]],decls:8,vars:11,consts:[["appearance","outline",1,"app-input-search",3,"ngStyle"],["nameOnly","",3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value",4,"ngIf"],[3,"value","matTooltip","click",4,"ngFor","ngForOf"],[3,"value"],[3,"value","matTooltip","click"]],template:function(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"mat-form-field",0),a["\u0275\u0275elementStart"](1,"mat-select",1,2),a["\u0275\u0275elementStart"](3,"mat-option"),a["\u0275\u0275element"](4,"ngx-mat-select-search",3),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](5,x,2,1,"mat-option",4),a["\u0275\u0275template"](6,S,2,3,"mat-option",5),a["\u0275\u0275pipe"](7,"async"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e&&(a["\u0275\u0275property"]("ngStyle",t.isDisabled()),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",t.showSelect),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngForOf",a["\u0275\u0275pipeBind1"](7,9,t.filteredList)))},directives:[m.c,u.NgStyle,h.c,o.v,o.k,o.F,v.p,g.a,u.NgIf,u.NgForOf,f.a],pipes:[u.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}.mat-form-field[_ngcontent-%COMP%]:disabled{background-color:#0ff}.app-input-search[_ngcontent-%COMP%]     .mat-select-value{color:var(--blue-grey-80,#5f6c81);font-family:var(--inputSearchFont)!important;font-size:13px;font-style:normal;font-weight:400;line-height:16px}"]}),e})()},v2fc:function(e,t,n){"use strict";n.d(t,"a",(function(){return m}));var i=n("xG9w"),a=n("wd/R"),o=n("2Vo4"),l=n("XNiG"),r=n("fXoL"),s=n("tk/3"),d=n("LcQX"),c=n("flaP"),p=n("XXEo");let m=(()=>{class e{constructor(e,t,n,i){this.$http=e,this._util=t,this._roles=n,this._auth=i,this.currentUser=this._auth.getProfile().profile,this.token=this._auth.getJwtToken(),this.isaBudgettedAttachmentSubject=new o.a({}),this.getisaBudgettedAttachmentObservable=this.isaBudgettedAttachmentSubject.asObservable(),this.activitySubject=new l.b,this.getActivityObservable=this.activitySubject.asObservable(),this.priority_status_colors=[{statusName:"Low",statusColor:"#BADC58"},{statusName:"Medium",statusColor:"#91AECB"},{statusName:"High",statusColor:"#FFA502"},{statusName:"Very High",statusColor:"#cf0001"}],this.getVendorDetailsData=()=>this.$http.post("/api/isa/request/getVendorForList",{})}showMessage(e){this._util.showToastMessage(e)}showErrorMessage(e){this._util.showErrorMessage(e,"KEBS")}getToken(){return this._auth.getToken()}getCurrentUserRole(){return this._roles&&this._roles.roles&&this._roles.roles.length>0?this._roles.roles[0].role_id:null}setActivityObservable(e){this.activitySubject.next(e)}getStatusObjectEntries(){let e=i.where(this._roles.roles,{application_id:139,object_id:68});return e.length>0?JSON.parse(e[0].object_entries):null}resolveActivityTemplate(e,t,n,o,l,r){let s=i.where(e,{activity_name:("Status"==t?"status":"Priority"==t?"priority":"RMG Owner"==t?"rmgOwner":"TAG Owner"==t?"tagOwner":null)||t});return s.length>0?{activity_type_id:s[0]._id,activity_description:this.getActivityDescription(s[0],n,o,l,r),activity_by:this.currentUser.oid,activity_created_date:a()}:{}}getActivityDescription(e,t,n,i,a){let o="";return o=e.activity_template[t],n&&(o=o.replace("from_value",n)),i&&(o=o.replace("to_value",i)),a&&(o=o.replace("object_name",a)),o}getRequestById(e){return this.$http.post("/api/isa/request/getRequestById",{requestId:e})}getActivityTypes(){return this.$http.post("/api/isa/request/getActivityTypeMasterData",{})}getStatusMasterData(){return this.$http.post("/api/isa/request/getISAStatusMasterData",{statusValues:this.getStatusObjectEntries()})}updateRequestStatus(e,t,n,i){return this.$http.post("/api/isa/request/statusChange",{requestId:e,statusRefId:t,currentStatusId:n,activityTemplate:i})}updateKeyValueInResourceRequest(e,t,n){return this.$http.post("/api/isa/request/updateKeyValueInResourceRequest",{requestId:e,activityTemplate:t,modifyKeyValue:n})}changeTagOrRmgOwner(e,t,n,i){return this.$http.post("/api/isa/request/changeTagOrRmgOwner",{requestId:e,activityTemplate:t,type:n,changedOid:i})}getActivity(e){return this.$http.post("/api/isa/request/activityRetreivalBasedOnRequestIdOrActivityId",{activityId:e})}insertISAActivity(e,t){return this.$http.post("/api/isa/request/insertISAActivity",{requestId:e,activityTemplate:t})}editISAActivity(e,t){return this.$http.post("/api/isa/request/editISAActivity",{activity_id:e,activity_details:t})}getISAAttachmentById(e){return this.$http.post("/api/isa/attachment/getISAAttachmentById",{request_id:e})}getISAAttachmentFromS3(e){return this.$http.post("/api/isa/attachment/getISAAttachmentFromS3",{key:e})}deleteISAAttachment(e,t,n){return this.$http.post("/api/isa/attachment/deleteISAAttachment",{requestId:e,file:t,activityTemplate:n})}updateISAAttachment(e,t,n,i){return this.$http.post("/api/isa/attachment/updateISAAttachment",{requestId:e,file:n,attachmentId:t,activityTemplate:i})}getApproverStatuses(e,t){return this.$http.post("/api/isa/request/getApproverStatusForRequest",{workflowHeaderId:e,approvers:t})}createTask(e,t,n){return this.$http.post("/api/isa/tasks/createTasks",{request_id:e,tasks:t,activityTemplate:n})}getRequestTasks(e){return this.$http.post("/api/isa/tasks/getTaskById",{request_id:e})}addTask(e,t,n,i){return this.$http.post("/api/isa/tasks/addTasks",{task_id:e,request_id:t,tasks:n,activityTemplate:i})}updateTaskObject(e,t,n,i,a){return this.$http.post("/api/isa/tasks/updateTaskObject",{task_id:e,sub_task_id:t,key:n,object:i,activityTemplate:a})}getTaskTemplate(){return this.$http.post("/api/isa/tasks/getTaskTemplates",{})}createTaskFromTemplate(e,t,n,i,a,o,l,r){return this.$http.post("/api/isa/tasks/assignTaskFromTemplate",{task_template_id:e,request_id:t,task_id:n,rmg_owner:i,tag_owner:a,activityTemplate:o,taskTypesList:l,requestSkillId:r})}updateTaskDataFromAttachment(e){return this.$http.post("/api/isa/tasks/updateTaskNameAndOwner",e)}updateExtTaskExtAtchId(e){return this.$http.post("/api/isa/tasks/updateExtTaskExtAtchId",e)}getTaskStatusList(){return this.$http.post("/api/isa/tasks/getTaskStatus",{})}getTaskTypeList(){return this.$http.post("/api/isa/tasks/getTaskTypeMasterData",{})}updateTypeListInReq(e){return this.$http.post("/api/isa/request/insertTaskTypeOwnerDetail",e)}updateTypeAssigned(e){return this.$http.post("/api/isa/request/updTaskTypeOwnerDetail",e)}deleteTask(e,t,n,i,a){return this.$http.post("/api/isa/tasks/changeTaskFlag",{request_id:e,task_id:t,sub_task_id:n,is_active:i,activityTemplate:a})}updateTaskAssigned(e,t,n,i,a){return this.$http.post("/api/isa/tasks/updateAssignedToTask",{request_id:e,task_id:t,sub_task_id:n,assigned_to:i,activityTemplate:a})}updateTaskData(e,t,n,i,a,o){return this.$http.post("/api/isa/tasks/updateTaskData",{request_id:e,task_id:t,sub_task_id:n,key:i,value:a,activityTemplate:o})}getRequestCTAs(e){return this.$http.post("/api/isa/configuration/getRequestCTAs",{requestId:e})}getISAWfConfig(){return this.$http.post("/api/isa/configuration/getISAWfConfig",{})}getTodoDetails(e){return this.$http.post("/api/isa/todo/getTodo",{todoId:e})}createTodo(e,t,n){return this.$http.post("/api/isa/todo/createToDo",{request_id:e,to_do_list:t,activityTemplate:n})}insertTodo(e,t,n,i){return this.$http.post("/api/isa/todo/insertToDo",{request_id:e,to_do_id:t,to_do_list:n,activityTemplate:i})}editTodo(e,t,n,i){return this.$http.post("/api/isa/todo/editToDo",{request_id:e,to_do_id:t,to_do_details:n,activityTemplate:i})}updateResourceInRequest(e,t,n,i,a){return this.$http.post("/api/isa/request/updateResourceInRequest",{requestId:e,activityTemplate:t,wfActivityTemplate:n,statusRefId:i,resourceOid:a})}triggerWfOnSubmission(e,t,n,i,a,o,l,r){return this.$http.post("/api/isa/request/triggerWfOnSubmission",{requestId:e,activityTemplate:t,wfActivityTemplate:n,statusRefId:i,task_id:a,sub_task_id:o,task_status:l,wfConfig:r})}addIsaTaskActualHours(e,t,n,i,a){return this.$http.post("/api/isa/tasks/addIsaTaskActualHours",{task_id:e,task_item:t,actual_hours:n,timesheet_id:i,request_id:a})}deleteIsaTaskActualHours(e,t,n,i,a){return this.$http.post("/api/isa/tasks/deleteIsaTaskActualHours",{task_id:e,task_item:t,actual_hours:n,request_id:i,timesheet_id:a})}getSLAByRequestId(e){return this.$http.post("/api/isa/request/getSLAByRequestId",{requestId:e})}getRequestBasedOnSearch(e){return this.$http.post("/api/isa/request/getRequestBasedOnSearch",{searchParameter:e})}moveTaskToRequest(e,t,n,i,a,o){return this.$http.post("/api/isa/tasks/moveTaskToRequest",{fromRequestId:e,fromTaskId:t,mainTaskId:n,toRequestId:i,toTaskId:a,activityTemplate:o})}removeResourceFromRequest(e,t,n,i,a){return this.$http.post("/api/isa/request/removeResourceFromRequest",{requestId:e,statusId:t,statusRefId:n,resActivityTemplate:i,statusActivityTemplate:a})}checkIfUserHasAccessToRequest(e){return this.$http.post("/api/isa/request/checkIfUserHasAccessToRequest",{requestId:e,oid:this.currentUser.oid})}updateEmployeeStatus(e){return this.$http.post("/api/obPrimary/updateEmployeeStatus",e)}checkIfEmailHasRequestId(e){return this.$http.post("/api/obPrimary/checkIfEmailHasRequestId",{apiParams:{emailId:e}})}createISAAttachment(e){return this.$http.post("/api/isa/attachment/createIsaAttachment",e)}getSourceList(){return this.$http.post("/api/isa/attachment/getAllSource",{})}saveEmailtoTask(e){return this.$http.post("/api/isa/tasks/saveEmailtoTask",e)}setISABudgetedAttachmentActivityObservable(e){this.isaBudgettedAttachmentSubject.next(e)}getTemplateForUser(e){return this.$http.post("/api/isa/request/getVisibleFormFieldsForUser",e)}budgetApprovalCheck(e){return this.$http.post("/api/isa/request/ctcRangeCheck",e)}checkElligibleToSubmit(e){return new Promise((t,n)=>{this.$http.post("/api/isa/request/budgetApprovalCheck",e).subscribe(e=>t(e),e=>n(e))})}offerApprovalCheck(e){return this.$http.post("/api/isa/request/offerApprovalCheck",e)}triggerWfOnBudgetApproval(e,t,n,i,a){return this.$http.post("/api/isa/request/triggerWfOnBudgetApproval",{requestId:e,activityTemplate:t,wfActivityTemplate:n,statusRefId:i,wfConfig:a})}getMasterDataByMasterDataName(e){return this.$http.post("/api/isa/request/getMasterDataByMasterDataName",e)}getVisibilityMatrix(e){return this.$http.post("/api/isa/request/getAuthorzedFieldsByRoleId",e)}getAllRoleAccess(){return i.where(this._roles.roles,{application_id:139})}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275inject"](s.c),r["\u0275\u0275inject"](d.a),r["\u0275\u0275inject"](c.a),r["\u0275\u0275inject"](p.a))},e.\u0275prov=r["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},vFgw:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var i=n("fXoL");let a=(()=>{class e{constructor(){this.isMandant=e=>{const t=this.formFieldObject.find(t=>t.field_name==e);return!!t&&t.is_mandant},this.isActive=e=>{const t=this.formFieldObject.find(t=>t.field_name==e);return!!t&&t.is_active},this.label=e=>{const t=this.formFieldObject.find(t=>t.field_name==e);return console.log(e,t),!!t&&t.label},this.isDisabled=e=>{const t=this.formFieldObject.find(t=>t.field_name==e);return console.log(e,t,!!t&&t.disabled),!!t&&t.disabled}}transform(e,t,n){return this.formFieldObject=t,"isActive"==n?this.isActive(e):"isMandant"==n?this.isMandant(e):"label"==n?this.label(e):"isDisabled"==n?this.isDisabled(e):void 0}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=i["\u0275\u0275definePipe"]({name:"tenantLead",type:e,pure:!0}),e})()},yu80:function(e,t,n){"use strict";n.d(t,"a",(function(){return m}));var i=n("mrSG"),a=n("xG9w"),o=n("XNiG"),l=n("2Vo4"),r=n("fXoL"),s=n("tk/3"),d=n("flaP"),c=n("XXEo"),p=n("LcQX");let m=(()=>{class e{constructor(e,t,n,i){this.$http=e,this._roles=t,this._auth=n,this._util=i,this.applicationName="AMS",this.currentUser=this._auth.getProfile().profile,this.activitySubject=new o.b,this.getActivityObservable=this.activitySubject.asObservable(),this.actualHoursSubject=new l.a({}),this.getActualHoursObservable=this.actualHoursSubject.asObservable(),this.taskRefreshSubject=new o.b,this.getTaskRefreshObservable=this.taskRefreshSubject.asObservable(),this.task_status_colors=[{statusName:"Open",statusColor:"#928F8D"},{statusName:"In Progress",statusColor:"#ff7200"},{statusName:"Completed",statusColor:"#4caf50"}],this.supportTeams=[],this.statusColorArray=[],this.priorityArray=[],this.getAmsAccessForUser(),this.getCurrentUserRole(),this.getSupportTeamData()}setActivityObservable(e){this.activitySubject.next(e)}setTaskRefreshObservable(e){this.taskRefreshSubject.next(e)}setActualHoursObservable(e){this.actualHoursSubject.next(e)}showMessage(e){this._util.showToastMessage(e)}showErrorMessage(e){this._util.showErrorMessage(e,"KEBS")}getToken(){return this._auth.getToken()}getCurrentUserRole(){return this._roles&&this._roles.roles&&this._roles.roles.length>0?this._roles.roles[0].role_id:null}getAmsAccessForUser(){let e=a.findWhere(this._roles.roles,{application_id:95,object_id:13});return!e||"Update"!=e.operation&&"*"!=e.operation}getStatusObjectEntries(){let e=a.where(this._roles.roles,{application_id:95,object_id:66});return e.length>0?JSON.parse(e[0].object_entries):null}getTicketCreationObjectEntries(){return Object(i.c)(this,void 0,void 0,(function*(){return a.where(this._roles.roles,{application_id:95,object_id:565})}))}getCreateAccess(){let e=a.where(this._roles.roles,{application_id:95,object_id:67});return e.length>0&&"*"==e[0].object_value}checkIfTicketIsEditable(e,t){if(this.supportTeams.length>0)for(let n of this.supportTeams){n.team_coordinator="string"==typeof n.team_coordinator?JSON.parse(n.team_coordinator):n.team_coordinator;for(let e of n.team_coordinator)if(e==this.currentUser.oid)return!0;n.team_head="string"==typeof n.team_head?JSON.parse(n.team_head):n.team_head;for(let e of n.team_head)if(e==this.currentUser.oid)return!0}return!a.contains(e.ticket_non_editable,t.status[0]._id)}getSupportTeamData(){return new Promise((e,t)=>{this.getSupportTeams().subscribe(n=>{"S"==n.messType&&n.data.length>0?(this.supportTeams=n.data,e(!0)):t("Not data found !")},e=>{t(e)})})}checkIfUserIsCustomer(){return!(!this._roles.roles||!this._roles.roles[0]||35!=this._roles.roles[0].role_id)}getCurrentCustomerDetails(){return this.$http.post("/api/ams/configuration/getCurrentCustomerDetails",{})}getStatusList(){return this.$http.post("/api/ams/configuration/getStatusList",{statusValues:this.getStatusObjectEntries()})}getTypeList(){return this.$http.post("/api/ams/configuration/getTypeList",{})}getAmsPostingDate(){return this.$http.post("/api/ams/configuration/getAmsPostingDate",{})}getAMSNewReleases(){return this.$http.post("/api/ams/configuration/getAMSNewReleases",{})}saveTicketToCollection(e){return this.$http.post("/api/ams/ticket/createTicket",{ticketDetails:e})}createTask(e,t){return this.$http.post("/api/ams/tasks/createTasks",{ticket_id:e,tasks:t})}createTaskFromTemplate(e,t,n,i,a){return this.$http.post("/api/ams/tasks/assignTaskFromTemplate",{task_template_id:e,ticket_id:t,estimated_closure_date:n,created_on:i,assigned_to:a})}addTask(e,t,n){return this.$http.post("/api/ams/tasks/addTasks",{task_id:e,ticket_id:t,tasks:n})}updateTaskObject(e,t,n,i){return this.$http.post("/api/ams/tasks/updateTaskObject",{task_id:e,sub_task_id:t,key:n,object:i})}editTaskStatus(e,t,n,i){return this.$http.post("/api/ams/tasks/editTaskStatus",{task_id:e,task_item:t,status:n,ticket_id:i})}editTaskDueDate(e,t,n,i){return this.$http.post("/api/ams/tasks/editTaskDueDate",{task_id:e,task_item:t,due_on:n,ticket_id:i})}editTaskPlannedHours(e,t,n,i){return this.$http.post("/api/ams/tasks/editTaskPlannedHours",{task_id:e,task_item:t,planned_hours:n,ticket_id:i})}editTaskName(e,t,n,i){return this.$http.post("/api/ams/tasks/editTaskName",{task_id:e,task_item:t,task_name:n,ticket_id:i})}editTaskAssigned(e,t,n,i){return this.$http.post("/api/ams/tasks/editTaskAssigned",{task_id:e,task_item:t,assigned_to:n,ticket_id:i})}addTaskActualHours(e,t,n,i,a){return this.$http.post("/api/ams/tasks/addTaskActualHours",{task_id:e,task_item:t,actual_hours:n,timesheet_id:i,ticket_id:a})}editTaskActualHours(e,t,n,i){return this.$http.post("/api/ams/tasks/editTaskActualHours",{task_id:e,task_item:t,actual_hours:n,ticket_id:i})}deleteTaskActualHours(e,t,n,i,a){return this.$http.post("/api/ams/tasks/deleteTaskActualHours",{task_id:e,task_item:t,actual_hours:n,ticket_id:i,timesheet_id:a})}getTicketMasterData(){return this.$http.post("/api/ams/configuration/getTicketMasterData",{})}getTicketProperties(){return this.$http.post("/api/ams/configuration/getTicketProperties",{})}getTicketById(e){return this.$http.post("/api/ams/ticket/getTicketById",{ticketId:e})}getTicketBasedOnSearch(e,t){return this.$http.post("/api/ams/ticket/getTicketBasedOnSearch",{searchParameter:e,ticketList:t})}getTicketMetaData(e){return this.$http.post("/api/ams/ticket/getTicketMetaData",{ticketId:e})}getTicketAttachment(e){return this.$http.post("/api/ams/ticket/getTicketAttachment",{ticketId:e})}getFileDataForDownload(e){return this.$http.post("/api/ams/configuration/getAMSAttachment",{key:e})}deleteTicketAttachment(e,t){return this.$http.post("/api/ams/configuration/deleteAMSAttachment",{ticketId:e,file:t})}updateTicketAttachment(e,t){return this.$http.post("/api/ams/configuration/updateAMSAttachment",{ticketId:e,file:t})}getTicketsForEmployee(e){return this.$http.post("/api/ams/ticket/getAllTicketsForEmployee",{employee_oid:e,org_codes:this._roles.getUserRoleOrgCodes("AMS"),role_id:this._roles.roles[0].role_id,is_read_only:this.getAmsAccessForUser()})}getConsultantBasedOnProjectModule(e,t){return this.$http.post("/api/ams/configuration/getConsultantBasedOnProjectModule",{project_item_id:e,module_id:t})}getActivity(e){return this.$http.post("/api/ams/activities/getActivity",{activityId:e})}addNoteActivity(e,t){return this.$http.post("/api/ams/activities/addNoteActivity",{activity_id:e,activities:t})}editActivity(e,t){return this.$http.post("/api/ams/activities/editActivity",{activity_id:e,activity_details:t})}getTodoDetails(e){return this.$http.post("/api/ams/todo/getTodo",{todoId:e})}getTicketCTAs(e){return this.$http.post("/api/ams/configuration/getTicketCTAs",{ticketId:e})}getTicketTasks(e){return this.$http.post("/api/ams/tasks/getTaskById",{task_id:e})}getTaskTemplate(){return this.$http.post("/api/ams/tasks/getTaskTemplates",{})}getConfigBasedOnProject(e){return this.$http.post("/api/ams/configuration/getConfigBasedOnProject",{itemId:e})}getModuleBasedOnSubmodule(e){return this.$http.post("/api/ams/configuration/getModuleBasedOnSubmodule",{subModuleId:e})}createTodo(e,t){return this.$http.post("/api/ams/todo/createToDo",{ticket_id:e,to_do_list:t})}insertTodo(e,t,n){return this.$http.post("/api/ams/todo/insertToDo",{ticket_id:e,to_do_id:t,to_do_list:n})}editTodo(e,t,n,i){return this.$http.post("/api/ams/todo/editToDo",{ticket_id:e,to_do_id:t,to_do_details:n,change_type:i})}getTrDetails(e){return this.$http.post("/api/ams/tr/getTr",{trId:e})}createTr(e,t){return this.$http.post("/api/ams/tr/createTr",{ticket_id:e,tr_list:t})}insertTr(e,t,n){return this.$http.post("/api/ams/tr/insertTr",{ticket_id:e,tr_id:t,tr_list:n})}editTr(e,t,n){return this.$http.post("/api/ams/tr/editTr",{ticket_id:e,tr_id:t,tr_details:n})}editTicketPriority(e,t,n){return this.$http.post("/api/ams/ticket/editTicketPriority",{ticket_id:e,prev_priority:t,priority:n})}editTicketStatus(e,t,n,i,a,o){return this.$http.post("/api/ams/ticket/editTicketStatus",{ticket_id:e,status_id:i,prev_status:t,status_name:a,status_ref_id:n,wf_plugin_data:o})}editTicketType(e,t,n,i){return this.$http.post("/api/ams/ticket/editTicketType",{ticket_id:e,type_id:n,prev_type:t,type_name:i})}editTicketConsultants(e,t,n,i,a,o){return this.$http.post("/api/ams/ticket/editTicketConsultants",{ticket_id:e,employee_oid:n,employee_name:i,level_name:a,emp_ref_id:o,prev_oid:t})}getFlaggedTickets(e){return this.$http.post("/api/ams/configuration/getFlaggedTickets",{associateOId:e})}updateFlaggedTickets(e,t){return this.$http.post("/api/ams/configuration/updateFlaggedTickets",{associateOId:e,flaggedTickets:t})}getTicketsFilter(e,t,n,i){return this.$http.post("/api/ams/reports/getTicketsFilter",{status:e,filters:t,ticketList:n,searchParameter:i})}getStatusCount(e){return this.$http.post("/api/ams/reports/getStatusCount",{ticketList:e})}downloadTickets(e){return this.$http.post("/api/ams/reports/downloadTickets",{ticketList:e})}getSLAByTicketId(e){return this.$http.post("/api/ams/ticket/getSLAByTicketId",{ticketId:e})}getLocationList(){return this.$http.post("/api/tsPrimary/getLocationList",{})}updateTaskLocation(e,t,n,i,a,o){return this.$http.post("/api/ams/tasks/updateTaskLocation",{task_id:e,taskItem:t,old_loc:n,new_loc:i,value:a,ticket_id:o})}getSupportTeams(){return this.$http.post("/api/ams/configuration/getSupportTeams",{})}getReportedByList(e){return this.$http.post("/api/ams/configuration/getReportedByList",{project_item_id:e})}editTicketDetails(e,t,n,i){return this.$http.post("/api/ams/ticket/editTicketDetails",{ticket_id:e,key:t,value:n,prev_value:i})}getTicketsFilterSR(e,t){return this.$http.post("/api/ams/reports/getTicketsFilter",{filterConfig:e,ticketList:t})}updateNewWfPluginId(e,t,n){return this.$http.post("/api/ams/ticket/updateNewWfPluginId",{wf_plugin_data:t,ticket_id:e,status_object:n})}getActiveLabel(){return this.$http.post("/api/ams/ticket/getActiveLabelData",{})}getActivePriority(){return new Promise((e,t)=>{0==this.priorityArray.length?this.$http.post("/api/ams/ticket/getActivePriorityData",{}).subscribe(t=>{this.priorityArray=t,e(this.priorityArray)}):e(this.priorityArray)})}getActivePriorityFromDB(){return Object(i.c)(this,void 0,void 0,(function*(){return this.$http.post("/api/ams/ticket/getActivePriorityData",{}).subscribe(e=>(this.priorityArray=e,this.priorityArray))}))}getStatusColor(){return new Promise((e,t)=>{0==this.statusColorArray.length?this.$http.post("/api/ams/ticket/getStatusColorData",{}).subscribe(t=>{this.statusColorArray=t,e(this.statusColorArray)}):e(this.statusColorArray)})}getStatusColorFromDB(){return Object(i.c)(this,void 0,void 0,(function*(){this.$http.post("/api/ams/ticket/getStatusColorData",{}).subscribe(e=>(this.statusColorArray=e,this.statusColorArray))}))}checkAdminRole(){return this.$http.post("/api/ams/reports/checkForAdmin",{object_id:530,application_id:95})}getSupportTeamsForAdmin(){return this.$http.post("/api/ams/reports/getSupportTeamsForAdmin",{})}updateSupportTeamForAdmin(e){return this.$http.post("/api/ams/reports/updateSupportTeamForAdmin",{formData:e})}deleteSupportTeamForAdmin(e){return this.$http.post("/api/ams/reports/deleteSupportTeamForAdmin",{team_id:e})}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275inject"](s.c),r["\u0275\u0275inject"](d.a),r["\u0275\u0275inject"](c.a),r["\u0275\u0275inject"](p.a))},e.\u0275prov=r["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()}}]);