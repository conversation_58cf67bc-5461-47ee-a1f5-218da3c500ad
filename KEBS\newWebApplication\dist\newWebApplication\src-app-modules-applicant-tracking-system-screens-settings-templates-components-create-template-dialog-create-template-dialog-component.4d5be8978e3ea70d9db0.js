(window.webpackJsonp=window.webpackJsonp||[]).push([[930],{FYQT:function(e,t,n){"use strict";n.r(t),n.d(t,"CreateTemplateDialogComponent",(function(){return d}));var o=n("fXoL"),i=n("0IaG"),a=n("NFeN"),r=n("ofXK");const l=function(e,t){return{"background-color":e,"border-color":t}};function c(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementContainerStart"](0),o["\u0275\u0275elementStart"](1,"div",6),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](e);const n=t.index;return o["\u0275\u0275nextContext"]().openCreationMethod(n)})),o["\u0275\u0275element"](2,"img",7),o["\u0275\u0275elementStart"](3,"div",8),o["\u0275\u0275text"](4),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=t.$implicit;o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngStyle",o["\u0275\u0275pureFunction2"](3,l,e.backgroundColor,e.color)),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("src",e.imgUrl,o["\u0275\u0275sanitizeUrl"]),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate"](e.label)}}let d=(()=>{class e{constructor(e){this._dialogRef=e,this.newTemplate=[{id:1,label:"Blank Template",color:"#52C41A",backgroundColor:"#EEF9E8",navigation:"blankTemplate",imgUrl:"https://assets.kebs.app/candidate-new-candidate.png"},{id:2,label:"Duplicate An Existing Template",color:"#722ED1",backgroundColor:"#F1EAFA",navigation:"copyTemplate",imgUrl:"https://assets.kebs.app/duplicate-template.png"}]}ngOnInit(){}onClose(){this._dialogRef.close(!1)}openCreationMethod(e){this._dialogRef.close(this.newTemplate[e].navigation)}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](i.h))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-create-template-dialog"]],decls:9,vars:1,consts:[[1,"bg-container"],[1,"d-flex","align-items-center","justify-content-between","header"],[1,"title","p-0"],[1,"close",3,"click"],[1,"d-flex","align-items-center","justify-content-center","flex-wrap","main"],[4,"ngFor","ngForOf"],[1,"create-tile",3,"ngStyle","click"],["width","32","height","32",2,"margin-bottom","20px",3,"src"],[1,"label-text"]],template:function(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"div",0),o["\u0275\u0275elementStart"](1,"div",1),o["\u0275\u0275elementStart"](2,"div",2),o["\u0275\u0275text"](3,"Create New Template"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](4,"div"),o["\u0275\u0275elementStart"](5,"mat-icon",3),o["\u0275\u0275listener"]("click",(function(){return t.onClose()})),o["\u0275\u0275text"](6,"close"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](7,"div",4),o["\u0275\u0275template"](8,c,5,6,"ng-container",5),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e&&(o["\u0275\u0275advance"](8),o["\u0275\u0275property"]("ngForOf",t.newTemplate))},directives:[a.a,r.NgForOf,r.NgStyle],styles:[".bg-container[_ngcontent-%COMP%]{padding:24px;overflow:hidden}.bg-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{margin-bottom:20px}.bg-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:18px;font-weight:700;color:#111434}.bg-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .close[_ngcontent-%COMP%]{font-size:18px;width:18px;height:18px;color:#1c1b1f;cursor:pointer}.bg-container[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]{gap:30px}.bg-container[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]   .create-tile[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;flex-direction:column;border-radius:8px;border-width:1px;border-style:solid;cursor:pointer;height:120px;width:200px}.bg-container[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]   .create-tile[_ngcontent-%COMP%]   .label-text[_ngcontent-%COMP%]{color:#111434;font-family:var(--atsfontFamily);font-size:11px;font-weight:600}"]}),e})()}}]);