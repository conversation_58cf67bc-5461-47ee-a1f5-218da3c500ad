(window.webpackJsonp=window.webpackJsonp||[]).push([[827],{AWeY:function(e,t,n){"use strict";n.r(t),n.d(t,"AlignTaskComponent",(function(){return x}));var i=n("mrSG"),l=n("3Pt+"),a=n("0IaG"),o=n("33Jv"),r=n("fXoL"),c=n("25DO"),d=n("LcQX"),s=n("XXEo"),p=n("bTqV"),m=n("NFeN"),u=n("FsDe"),v=n("kmnG"),h=n("d3UM"),g=n("ofXK"),f=n("FKr1");function S(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"mat-option",20),r["\u0275\u0275text"](1),r["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;r["\u0275\u0275property"]("value",e.value),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"](" ",e.display_value," ")}}function y(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"mat-icon",21),r["\u0275\u0275text"](1,"done_all"),r["\u0275\u0275elementEnd"]())}function _(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div",22),r["\u0275\u0275elementStart"](1,"span",23),r["\u0275\u0275text"](2,"Loading..."),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]())}const b=function(){return{label:"Employee Name",api_obj_key:"displayName"}},k=function(){return{label:"Associate Id",api_obj_key:"associate_id"}},I=function(e,t){return[e,t]},E=function(){return{label:"Kr/Init",api_obj_key:"name"}},w=function(e){return[e]};let x=(()=>{class e{constructor(e,t,n,i,a,r,c){this._formBuilder=e,this._okr=t,this._util=n,this._matDialogRef=i,this.data=a,this._dialog=r,this._loginService=c,this.employeeSearchUrl=window.location.origin+"/api/okr/objective/searchEmployee",this.KRInitSearchUrl=window.location.origin+"/api/okr/task/searchKRINTAll",this.subs=new o.a,this.isUpdate=!1,this.okrTypeList=[{value:"KPI",display_value:"KPI"},{value:"BAU",display_value:"BAU"},{value:"key_result",display_value:"Key Result"},{value:"initiative",display_value:"Initiative"}],this.okrType=new l.j,console.log(this.data)}ngOnInit(){return Object(i.c)(this,void 0,void 0,(function*(){this.okrTypeList=yield this._okr.getOKRTypes()}))}getSelectedOwner(e){console.log(e),this.ownerId=e.id}getSelectedKrInit(e){console.log(e),this.KRInitId=e._id}onSave(){this.isUpdate=!0,this._okr.duplicateTask({mapped_under_id:this.KRInitId,updated_mapped_under_id:this.data.mapped_under_id,owner_id:this.ownerId,collection_name:this.data.collection_name,sub_app_id:this.data.subAppId}).subscribe(e=>{console.log("successs"),this.closeDialog("sucess")})}closeDialog(e){this._matDialogRef.close(e)}get token(){return this._loginService.getToken()}okrTypeChange(e){this.type={type:e}}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](l.i),r["\u0275\u0275directiveInject"](c.a),r["\u0275\u0275directiveInject"](d.a),r["\u0275\u0275directiveInject"](a.h),r["\u0275\u0275directiveInject"](a.a),r["\u0275\u0275directiveInject"](a.b),r["\u0275\u0275directiveInject"](s.a))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["app-align-task"]],decls:36,vars:20,consts:[[1,"row","px-3","py-2"],[1,"col-12","p-0","m-0"],[1,"row","mt-3","p-0"],[1,"col-12","m-0","p-0"],[1,"row"],[1,"col-10","pl-0","pr-0"],[1,"pl-2",2,"font-size","16px","font-weight","500","color","brown"],[1,"col-2","p-0"],["mat-icon-button","",1,"close-btn",3,"click"],[2,"font-size","20px","line-height","5px"],[1,"row","mt-1","p-0"],[1,"col-12"],["placeholder","Search Owner",3,"token","optionLabel","API_URL","selectedValues"],["appearance","outline",1,"w-100"],[3,"formControl","selectionChange"],[3,"value",4,"ngFor","ngForOf"],["placeholder","Search KR/Init",3,"token","optionLabel","API_URL","extraBodyParams","selectedValues"],["mat-mini-fab","",1,"done-all-btn",3,"disabled","click"],["style","color: white;font-size: 21px;",4,"ngIf"],["class","spinner-border text-danger","role","status",4,"ngIf"],[3,"value"],[2,"color","white","font-size","21px"],["role","status",1,"spinner-border","text-danger"],[1,"sr-only"]],template:function(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div",0),r["\u0275\u0275elementStart"](1,"div",1),r["\u0275\u0275elementStart"](2,"div",2),r["\u0275\u0275elementStart"](3,"div",3),r["\u0275\u0275elementStart"](4,"div",4),r["\u0275\u0275elementStart"](5,"div",5),r["\u0275\u0275elementStart"](6,"span",6),r["\u0275\u0275text"](7,"Task Duplication"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](8,"div",7),r["\u0275\u0275elementStart"](9,"button",8),r["\u0275\u0275listener"]("click",(function(){return t.closeDialog("")})),r["\u0275\u0275elementStart"](10,"mat-icon",9),r["\u0275\u0275text"](11,"close"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](12,"div",10),r["\u0275\u0275elementStart"](13,"div",11),r["\u0275\u0275elementStart"](14,"span"),r["\u0275\u0275text"](15,"Owner:"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](16,"div",11),r["\u0275\u0275elementStart"](17,"kebs-select-search-d1",12),r["\u0275\u0275listener"]("selectedValues",(function(e){return t.getSelectedOwner(e)})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](18,"div",11),r["\u0275\u0275elementStart"](19,"span"),r["\u0275\u0275text"](20,"Select Type:"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](21,"div",11),r["\u0275\u0275elementStart"](22,"mat-form-field",13),r["\u0275\u0275elementStart"](23,"mat-select",14),r["\u0275\u0275listener"]("selectionChange",(function(e){return t.okrTypeChange(e.value)})),r["\u0275\u0275template"](24,S,2,2,"mat-option",15),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](25,"div",11),r["\u0275\u0275elementStart"](26,"span"),r["\u0275\u0275text"](27,"Search KR/Init:"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](28,"div",11),r["\u0275\u0275elementStart"](29,"kebs-select-search-d1",16),r["\u0275\u0275listener"]("selectedValues",(function(e){return t.getSelectedKrInit(e)})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](30,"div",10),r["\u0275\u0275elementStart"](31,"div",11),r["\u0275\u0275elementStart"](32,"button",17),r["\u0275\u0275listener"]("click",(function(){return t.onSave()})),r["\u0275\u0275template"](33,y,2,0,"mat-icon",18),r["\u0275\u0275template"](34,_,3,0,"div",19),r["\u0275\u0275text"](35," Done "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e&&(r["\u0275\u0275advance"](17),r["\u0275\u0275property"]("token",t.token)("optionLabel",r["\u0275\u0275pureFunction2"](14,I,r["\u0275\u0275pureFunction0"](12,b),r["\u0275\u0275pureFunction0"](13,k)))("API_URL",t.employeeSearchUrl),r["\u0275\u0275advance"](6),r["\u0275\u0275property"]("formControl",t.okrType),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngForOf",t.okrTypeList),r["\u0275\u0275advance"](5),r["\u0275\u0275property"]("token",t.token)("optionLabel",r["\u0275\u0275pureFunction1"](18,w,r["\u0275\u0275pureFunction0"](17,E)))("API_URL",t.KRInitSearchUrl)("extraBodyParams",t.type),r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("disabled",t.isUpdate),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",!t.isUpdate),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",t.isUpdate))},directives:[p.a,m.a,u.a,v.c,h.c,l.v,l.k,g.NgForOf,g.NgIf,f.p],styles:[".close-btn[_ngcontent-%COMP%]{color:grey}.done-all-btn[_ngcontent-%COMP%]{width:11.8rem;border-radius:4px;background-color:#cf0001;color:#fff;box-shadow:0 1px 3px 0 rgba(0,0,0,.51),0 0 14px 0 rgba(0,0,0,.11);margin-bottom:8px}"]}),e})()}}]);