(window.webpackJsonp=window.webpackJsonp||[]).push([[980],{KJjA:function(e,t,n){"use strict";n.r(t),n.d(t,"DisplayCurrencyComponent",(function(){return F}));var r=n("mrSG"),i=n("wd/R"),o=n("0IaG"),a=n("xG9w"),c=n("fXoL"),l=n("tk/3");let s=(()=>{class e{constructor(e){this.http=e}getCurrencyInfo(){return new Promise((e,t)=>{this.http.get("/api/master/getCurrencyInfo").subscribe(t=>{e(t)},e=>{t(e)})})}}return e.\u0275fac=function(t){return new(t||e)(c["\u0275\u0275inject"](l.c))},e.\u0275prov=c["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var d=n("HmYF"),m=n("LcQX"),p=n("ofXK"),y=n("bTqV"),u=n("NFeN"),h=n("wZkO"),f=n("Qu3c"),g=n("Xa2L"),_=n("tyNb"),v=n("+0xr");function C(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"button",6),c["\u0275\u0275elementStart"](1,"mat-icon",14),c["\u0275\u0275listener"]("click",(function(){return c["\u0275\u0275restoreView"](e),c["\u0275\u0275nextContext"]().exportCurrency()})),c["\u0275\u0275text"](2,"cloud_download"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()}}function x(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"div",15),c["\u0275\u0275element"](1,"mat-spinner",16),c["\u0275\u0275elementEnd"]())}function M(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"div"),c["\u0275\u0275elementStart"](1,"a",17,18),c["\u0275\u0275listener"]("click",(function(){c["\u0275\u0275restoreView"](e);const n=t.$implicit;return c["\u0275\u0275nextContext"]().filterCurrency(n)})),c["\u0275\u0275text"](3),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=c["\u0275\u0275nextContext"]();c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("active",n.selected_conversion_currency==e),c["\u0275\u0275advance"](2),c["\u0275\u0275textInterpolate1"](" ",e," ")}}function w(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"div"),c["\u0275\u0275elementStart"](1,"a",17,18),c["\u0275\u0275listener"]("click",(function(){c["\u0275\u0275restoreView"](e);const n=t.$implicit;return c["\u0275\u0275nextContext"]().filterYears(n)})),c["\u0275\u0275text"](3),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=c["\u0275\u0275nextContext"]();c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("active",n.selected_year==e),c["\u0275\u0275advance"](2),c["\u0275\u0275textInterpolate1"](" ",e," ")}}function O(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"mat-header-cell"),c["\u0275\u0275text"](1," Month "),c["\u0275\u0275elementEnd"]())}function b(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"mat-cell"),c["\u0275\u0275text"](1),c["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;c["\u0275\u0275advance"](1),c["\u0275\u0275textInterpolate1"](" ",e.month_name," ")}}function P(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"mat-header-cell"),c["\u0275\u0275text"](1," Rates "),c["\u0275\u0275elementEnd"]())}function S(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"mat-cell"),c["\u0275\u0275text"](1),c["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;c["\u0275\u0275advance"](1),c["\u0275\u0275textInterpolate1"](" ",e.rate," ")}}function E(e,t){1&e&&c["\u0275\u0275element"](0,"mat-header-row")}function I(e,t){1&e&&c["\u0275\u0275element"](0,"mat-row")}function k(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div",19),c["\u0275\u0275elementStart"](1,"div",20),c["\u0275\u0275elementStart"](2,"mat-table",21,22),c["\u0275\u0275elementContainerStart"](4,23),c["\u0275\u0275template"](5,O,2,0,"mat-header-cell",24),c["\u0275\u0275template"](6,b,2,1,"mat-cell",25),c["\u0275\u0275elementContainerEnd"](),c["\u0275\u0275elementContainerStart"](7,26),c["\u0275\u0275template"](8,P,2,0,"mat-header-cell",24),c["\u0275\u0275template"](9,S,2,1,"mat-cell",25),c["\u0275\u0275elementContainerEnd"](),c["\u0275\u0275template"](10,E,1,0,"mat-header-row",27),c["\u0275\u0275template"](11,I,1,0,"mat-row",28),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("dataSource",e.filtered_conversion_rates),c["\u0275\u0275advance"](8),c["\u0275\u0275property"]("matHeaderRowDef",e.displayedColumns),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("matRowDefColumns",e.displayedColumns)}}function D(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"div",29),c["\u0275\u0275elementStart"](1,"div"),c["\u0275\u0275elementStart"](2,"p"),c["\u0275\u0275text"](3,"Oops ! No data found !"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]())}function j(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"div",30),c["\u0275\u0275element"](1,"mat-spinner",31),c["\u0275\u0275elementEnd"]())}let F=(()=>{class e{constructor(e,t,n,r,o){this.misService=e,this.dialogRef=t,this.inData=n,this.exceltoJson=r,this.utilityService=o,this.displayedColumns=["Month","Rates"],this.displayMonth=i().format("MMM YY"),this.currentPointerDate=i().format(),this.inr_rate=0,this.usd_rate=0,this.display_inr_rate=0,this.display_usd_rate=0,this.inital_dollor=1,this.intial_rupee=1,this.dataFound=!1,this.loading=!1,this.conversion_currency=[],this.conversion_rates=[],this.filtered_conversion_rates=[],this.years_present=[],this.downloading=!1}ngOnInit(){this.currentPointerDate=i().format(),this.month=i().month()+1,this.year=i().year(),this.getCurrencyInfo()}getCurrencyInfo(){return Object(r.c)(this,void 0,void 0,(function*(){this.dataFound=!1,this.loading=!0,this.inr_rate=0,this.usd_rate=0,yield this.misService.getCurrencyInfo().then(e=>{"S"==e.messType&&(e.data.length>0&&(this.dataFound=!0,this.conversion_rates=e.data,this.conversion_currency=a.uniq(a.pluck(this.conversion_rates,"currency")),this.selected_conversion_currency=this.conversion_currency[0],this.filterCurrency(this.selected_conversion_currency)),this.loading=!1)},e=>{console.log(e),this.loading=!1})}))}filterYears(e){this.selected_year=e,this.filtered_conversion_rates=a.where(this.conversion_rates,{currency:this.selected_conversion_currency,year:e}),this.loading=!1,0==this.filtered_conversion_rates.length&&(this.dataFound=!1),this.formatData(this.filtered_conversion_rates)}filterCurrency(e){console.log(e),this.loading=!0,this.dataFound=!0,this.selected_conversion_currency=e,this.filtered_conversion_rates=a.where(this.conversion_rates,{currency:e}),this.years_present=a.uniq(a.pluck(this.filtered_conversion_rates,"year")),this.selected_year=a.contains(this.years_present,i().year())?i().year():this.years_present[0],this.filterYears(this.selected_year)}closeClicked(){this.dialogRef.close()}formatData(e){for(let n of e){n.position=0,n.month_name=i(n.month,"MM").format("MMMM");try{n.rate=n.rate.toFixed(4)}catch(t){n.rate=n.rate}}}exportCurrency(){if(this.downloading=!0,this.conversion_rates.length>0){for(let e of this.conversion_rates)e.month_name=i(e.month,"MM").format("MMMM");console.log(this.conversion_rates),this.exceltoJson.exportAsExcelFile(this.conversion_rates,"conversion_rates"),this.downloading=!1}else this.utilityService.showMessage("Currency Data Not found!","Dismiss",3e3),this.downloading=!1}}return e.\u0275fac=function(t){return new(t||e)(c["\u0275\u0275directiveInject"](s),c["\u0275\u0275directiveInject"](o.h),c["\u0275\u0275directiveInject"](o.a),c["\u0275\u0275directiveInject"](d.a),c["\u0275\u0275directiveInject"](m.a))},e.\u0275cmp=c["\u0275\u0275defineComponent"]({type:e,selectors:[["app-display-currency"]],decls:22,vars:7,consts:[[1,"display-currency-styles"],[1,"row","border-bottom","solid"],[1,"col-10","d-flex","pt-2","heading"],[1,"col-1","d-flex"],["mat-icon-button","","class","ml-auto close-button",4,"ngIf"],["class","col-2 pb-1","matTooltip","downloading...",4,"ngIf"],["mat-icon-button","",1,"ml-auto","close-button"],[1,"close-Icon",3,"click"],[1,"col-12"],["mat-tab-nav-bar","","animationDuration","200ms","mat-align-tabs","center"],[4,"ngFor","ngForOf"],["class","row justify-content-center",4,"ngIf"],["class","row justify-content-center intialHeading","style","vertical-align:middle;",4,"ngIf"],["class","row justify-content-center","style","vertical-align:middle;",4,"ngIf"],[3,"click"],["matTooltip","downloading...",1,"col-2","pb-1"],["diameter","18",1,"mt-2"],["mat-tab-link","","routerLinkActive","",3,"active","click"],["rla","routerLinkActive"],[1,"row","justify-content-center"],[1,"example-container","mat-elevation-z8","currency-table"],[3,"dataSource"],["table",""],["matColumnDef","Month"],[4,"matHeaderCellDef"],[4,"matCellDef"],["matColumnDef","Rates"],[4,"matHeaderRowDef"],[4,"matRowDef","matRowDefColumns"],[1,"row","justify-content-center","intialHeading",2,"vertical-align","middle"],[1,"row","justify-content-center",2,"vertical-align","middle"],["matTooltip","Loading currency ...","diameter","18"]],template:function(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"div",0),c["\u0275\u0275elementStart"](1,"div",1),c["\u0275\u0275elementStart"](2,"div",2),c["\u0275\u0275text"](3,"Currency"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](4,"div",3),c["\u0275\u0275template"](5,C,3,0,"button",4),c["\u0275\u0275template"](6,x,2,0,"div",5),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](7,"div",3),c["\u0275\u0275elementStart"](8,"button",6),c["\u0275\u0275elementStart"](9,"mat-icon",7),c["\u0275\u0275listener"]("click",(function(){return t.closeClicked()})),c["\u0275\u0275text"](10,"close"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](11,"div",1),c["\u0275\u0275elementStart"](12,"div",8),c["\u0275\u0275elementStart"](13,"nav",9),c["\u0275\u0275template"](14,M,4,2,"div",10),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](15,"div",1),c["\u0275\u0275elementStart"](16,"div",8),c["\u0275\u0275elementStart"](17,"nav",9),c["\u0275\u0275template"](18,w,4,2,"div",10),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275template"](19,k,12,3,"div",11),c["\u0275\u0275template"](20,D,4,0,"div",12),c["\u0275\u0275template"](21,j,2,0,"div",13),c["\u0275\u0275elementEnd"]()),2&e&&(c["\u0275\u0275advance"](5),c["\u0275\u0275property"]("ngIf",!t.downloading),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",t.downloading),c["\u0275\u0275advance"](8),c["\u0275\u0275property"]("ngForOf",t.conversion_currency),c["\u0275\u0275advance"](4),c["\u0275\u0275property"]("ngForOf",t.years_present),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",t.dataFound),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",!t.dataFound&&!t.loading),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",t.loading))},directives:[p.NgIf,y.a,u.a,h.f,p.NgForOf,f.a,g.c,h.e,_.i,v.k,v.c,v.e,v.b,v.g,v.j,v.d,v.a,v.f,v.i],styles:[".display-currency-styles[_ngcontent-%COMP%]{overflow:hidden;font-size:14px}.display-currency-styles[_ngcontent-%COMP%]   .smallCardIcon[_ngcontent-%COMP%]{font-size:21px!important;color:#868683!important}.display-currency-styles[_ngcontent-%COMP%]   .icon-tray-button[_ngcontent-%COMP%]{width:27px!important;height:27px!important;line-height:25px!important;visibility:hidden;visibility:visible!important;animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}.display-currency-styles[_ngcontent-%COMP%]   .txt-select[_ngcontent-%COMP%]:hover{cursor:pointer}.display-currency-styles[_ngcontent-%COMP%]   .expand-btn[_ngcontent-%COMP%]{border-radius:0;line-height:22px}.display-currency-styles[_ngcontent-%COMP%]   .col-1x[_ngcontent-%COMP%]{flex:0 0 6%}.display-currency-styles[_ngcontent-%COMP%]   .col-2x[_ngcontent-%COMP%]{flex:0 0 12%}.display-currency-styles[_ngcontent-%COMP%]   .del-btn[_ngcontent-%COMP%]   .del-icon[_ngcontent-%COMP%]{font-size:18px}.display-currency-styles[_ngcontent-%COMP%]   .item-list[_ngcontent-%COMP%]{line-height:0}.display-currency-styles[_ngcontent-%COMP%]     .cdk-overlay-container{z-index:100}.display-currency-styles[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}.display-currency-styles[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]{color:#c92020;font-weight:500}.display-currency-styles[_ngcontent-%COMP%]   .iconbtn[_ngcontent-%COMP%]{background-color:#c92020;color:#fff;padding:0;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.display-currency-styles[_ngcontent-%COMP%]   .intialHeading[_ngcontent-%COMP%]{color:#767676!important;padding-top:6px;padding-bottom:4px;font-size:21px}.display-currency-styles[_ngcontent-%COMP%]   .intialContent[_ngcontent-%COMP%]{color:#0e0e0e!important;padding-top:10px;font-size:30px}.display-currency-styles[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{color:unset!important;text-decoration:none!important}.display-currency-styles[_ngcontent-%COMP%]   table[_ngcontent-%COMP%], .display-currency-styles[_ngcontent-%COMP%]   td[_ngcontent-%COMP%], .display-currency-styles[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{border:1px solid #000;border-collapse:collapse}.display-currency-styles[_ngcontent-%COMP%]   .example-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;max-height:400px;min-width:300px}.display-currency-styles[_ngcontent-%COMP%]   .mat-table[_ngcontent-%COMP%]{overflow:auto;max-height:400px}.display-currency-styles[_ngcontent-%COMP%]   .currency-table[_ngcontent-%COMP%]{width:60%;overflow:auto}.display-currency-styles[_ngcontent-%COMP%]   .mat-column-Rates[_ngcontent-%COMP%]{font-style:italic}"]}),e})()}}]);