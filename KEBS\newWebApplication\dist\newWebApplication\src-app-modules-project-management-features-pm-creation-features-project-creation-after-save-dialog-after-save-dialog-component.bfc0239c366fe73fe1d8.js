(window.webpackJsonp=window.webpackJsonp||[]).push([[971],{"+Pfq":function(e,t,i){"use strict";i.r(t),i.d(t,"AfterSaveDialogComponent",(function(){return v}));var o=i("mrSG"),s=i("0IaG"),n=i("xG9w"),r=i("fXoL"),a=i("LcQX"),c=i("tyNb"),l=i("8EZa"),p=i("sRDL"),d=i("t44d"),h=i("ofXK"),u=i("NFeN"),m=i("R898"),g=i("R3G1");function f(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"mat-icon",44),r["\u0275\u0275listener"]("click",(function(){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"](2).sendMailNotification()})),r["\u0275\u0275text"](1,"forward_to_inbox"),r["\u0275\u0275elementEnd"]()}}function x(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"mat-icon",45),r["\u0275\u0275text"](1,"mark_email_read"),r["\u0275\u0275elementEnd"]())}function j(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",41),r["\u0275\u0275template"](1,f,2,0,"mat-icon",42),r["\u0275\u0275template"](2,x,2,0,"mat-icon",43),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",!e.mail_sent&&e.mailEnable),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",e.mail_sent&&e.mailEnable)}}let v=(()=>{class e{constructor(e,t,i,o,s,n,r,a){this.dialogRef=e,this.dialogData=t,this.dialog=i,this.utilityService=o,this.router=s,this.authService=n,this.pmlandingService=r,this.pmMasterService=a,this.mail_sent=!1,this.mailEnable=!1}ngOnInit(){return Object(o.c)(this,void 0,void 0,(function*(){console.log(this.dialogData.data),this.data=this.dialogData.data.data,this.mode=this.dialogData.mode,this.projectCode=this.dialogData.data.data?this.dialogData.data.data.project_code:"",this.tittle="Edit"==this.mode?"project edited successfully":"project created successfully",this.authService.projectListAccess=[],yield this.pmMasterService.getPMFormCustomizeConfigV().then(e=>{e&&(this.formConfig=e)}),this.projectCreationVersion=n.where(this.formConfig,{type:"landing-header",field_name:"version",is_active:!0});const e=n.where(this.formConfig,{type:"project-theme",field_name:"styles",is_active:!0});this.button=e.length>0&&e[0].data.button_color?e[0].data.button_color:"#90ee90",this.fontStyle=e.length>0&&e[0].data.font_style?e[0].data.font_style:"Roboto",document.documentElement.style.setProperty("--afterSaveFont",this.fontStyle);const t=n.where(this.formConfig,{type:"project-creation",field_name:"mail_notification",is_active:!0});this.mailEnable=t.length>0&&t[0].mailEnable}))}back(){"Edit"==this.mode?this.pmlandingService.projectHeaderConfig.refreshProjectCard():this.projectCreationVersion&&this.projectCreationVersion.length>0?this.router.navigateByUrl("/main/project-management/landing/home"):this.pmlandingService.projectHeaderConfig.refreshProjectList("reset"),this.dialogRef.close({messType:"S",mode:this.mode})}goToProject(){if(this.projectCreationVersion&&this.projectCreationVersion.length>0){const e=this.getProjectUrlLink(this.dialogData.data.portfolio_id,this.dialogData.data.portfolio_name,this.dialogData.data.project_id,this.dialogData.data.project_name);this.router.navigateByUrl(e),this.dialogRef.close({messType:"S",mode:this.mode})}else{let e=this.utilityService.encodeURIComponent(this.dialogData.data.portfolio_name),t=this.utilityService.encodeURIComponent(this.data.project_name);console.log("/main/project-management/"+this.data.portfolio+"/"+e+"/"+this.dialogData.data.project_id+"/"+t),this.router.navigateByUrl("/main/project-management/"+this.data.portfolio+"/"+e+"/"+this.dialogData.data.project_id+"/"+t),this.dialogRef.close({messType:"S",mode:this.mode})}}sendMailNotification(){this.mail_sent=!0}urlEncodeURIComponent(e){return encodeURIComponent(e).replace(/[!'()*]/g,(function(e){return"%"+e.charCodeAt(0).toString(16)}))}getProjectUrlLink(e,t,i,o){return`/main/project-management/${e}/${this.urlEncodeURIComponent(t)}/${i}/${this.urlEncodeURIComponent(o)}`}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](s.h),r["\u0275\u0275directiveInject"](s.a),r["\u0275\u0275directiveInject"](s.b),r["\u0275\u0275directiveInject"](a.a),r["\u0275\u0275directiveInject"](c.g),r["\u0275\u0275directiveInject"](l.a),r["\u0275\u0275directiveInject"](p.a),r["\u0275\u0275directiveInject"](d.a))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["app-after-save-dialog"]],decls:54,vars:27,consts:[[2,"width","437px","height","300px","position","relative","background","white","border-radius","16px"],[2,"left","115px","top","160px","position","absolute","flex-direction","column","justify-content","flex-start","align-items","center","gap","7px","display","inline-flex"],[1,"font-family",2,"color","#111434","font-size","16px","font-weight","600","text-transform","capitalize","line-height","24px","word-wrap","break-word"],[2,"left","142px","top","181px","position","absolute","flex-direction","column","justify-content","flex-start","align-items","center","gap","7px","display","inline-flex"],[1,"font-family",2,"color","#3e4acb","font-size","12px","font-weight","400","text-transform","capitalize","line-height","24px","white-space","nowrap","text-overflow","ellipsis","width","170px","overflow","hidden"],[2,"left","178px","top","205px","position","absolute","flex-direction","column","justify-content","center","align-items","center","gap","8px","display","inline-flex"],[2,"cursor","pointer","padding-left","8px","padding-right","8px","padding-top","4px","padding-bottom","4px","border-radius","4px","justify-content","flex-start","align-items","center","gap","8px","display","inline-flex",3,"click"],[1,"font-family",2,"color","white","font-size","12px","font-weight","700","text-transform","capitalize","line-height","16px","word-wrap","break-word"],[1,"font-family",2,"color","#526179","font-size","12px","font-weight","500","text-decoration","underline","text-transform","capitalize","line-height","16px","word-wrap","break-word"],["class","mailIcon",4,"ngIf"],[2,"width","134px","height","105px","left","152px","top","41px","position","absolute"],[2,"width","15.20px","height","20.63px","left","68.06px","top","84.25px","position","absolute","background","#F2F2F2"],[2,"width","2.18px","height","20.74px","left","75.61px","top","83.94px","position","absolute","background","white"],[2,"width","6.77px","height","1.01px","left","72.37px","top","87.53px","position","absolute","background","white"],[2,"width","10.90px","height","3.49px","left","70.84px","top","89.63px","position","absolute","background","white"],[2,"width","13.19px","height","5.69px","left","69.76px","top","91.33px","position","absolute","background","white"],[2,"width","36.66px","height","36.49px","left","0px","top","0.56px","position","absolute","background","#E4E4E4"],[2,"width","36.66px","height","36.49px","left","48.67px","top","0.56px","position","absolute","background","#E4E4E4"],[2,"width","30.55px","height","30.41px","left","52.05px","top","3.60px","position","absolute"],[2,"width","30.55px","height","30.41px","left","0px","top","0px","position","absolute","background","white"],[2,"width","13.08px","height","11.17px","left","9.95px","top","9.53px","position","absolute","background","white"],[2,"width","36.66px","height","36.49px","left","97.34px","top","0.56px","position","absolute","background","#E4E4E4"],[2,"width","30.55px","height","30.41px","left","100.05px","top","3.60px","position","absolute"],[2,"width","30.55px","height","30.41px","left","3.05px","top","3.60px","position","absolute"],[2,"width","46.52px","height","102.45px","left","80.44px","top","2.27px","position","absolute"],[2,"width","12.06px","height","12.84px","left","26.45px","top","0px","position","absolute","background","#2F2E41"],[2,"width","3.38px","height","8.79px","left","39.60px","top","91.45px","position","absolute","background","#FFB6B6"],[2,"width","7.38px","height","2.95px","left","36.18px","top","99.50px","position","absolute","background","#2F2E41"],[2,"width","3.38px","height","8.79px","left","18.49px","top","91.45px","position","absolute","background","#FFB6B6"],[2,"width","7.38px","height","2.95px","left","15.07px","top","99.50px","position","absolute","background","#2F2E41"],[2,"width","14.20px","height","9.67px","left","23.14px","top","29.59px","position","absolute","background","#FFB6B6"],[2,"width","25.12px","height","61.66px","left","18.47px","top","36.76px","position","absolute","background","#2F2E41"],[2,"width","11.41px","height","24.47px","left","35.11px","top","21.74px","position","absolute","background","#FFB6B6"],[2,"width","15.86px","height","20.90px","left","22.72px","top","13.93px","position","absolute","background","#E4E4E4"],[2,"width","23.58px","height","11.94px","left","0px","top","16.01px","position","absolute","background","#FFB6B6"],[2,"width","10.61px","height","10.86px","left","98.09px","top","17.97px","position","absolute","background","#E4E4E4"],[2,"width","9.39px","height","11.05px","left","113.14px","top","18.93px","position","absolute","background","#E4E4E4"],[2,"width","4.16px","height","5.57px","left","114.09px","top","3.02px","position","absolute","transform","rotate(-66.78deg)","transform-origin","0 0","background","#2F2E41"],[2,"width","11.38px","height","11.41px","left","102.88px","top","10.67px","position","absolute","transform","rotate(-61.23deg)","transform-origin","0 0","background","#FFB6B6"],[2,"width","13.92px","height","13.12px","left","105.31px","top","1.90px","position","absolute","background","#2F2E41"],[2,"width","71.08px","height","0.37px","left","15.07px","top","104.63px","position","absolute","background","#CACACA"],[1,"mailIcon"],["style","font-size: 20px;color: #5F6C81;",3,"click",4,"ngIf"],["style","font-size: 20px;color: green;",4,"ngIf"],[2,"font-size","20px","color","#5F6C81",3,"click"],[2,"font-size","20px","color","green"]],template:function(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div",0),r["\u0275\u0275elementStart"](1,"div",1),r["\u0275\u0275elementStart"](2,"div",2),r["\u0275\u0275text"](3),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](4,"div",3),r["\u0275\u0275elementStart"](5,"div",4),r["\u0275\u0275text"](6),r["\u0275\u0275pipe"](7,"checkLabel"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](8,"div",5),r["\u0275\u0275elementStart"](9,"div",6),r["\u0275\u0275listener"]("click",(function(){return t.goToProject()})),r["\u0275\u0275elementStart"](10,"div",7),r["\u0275\u0275text"](11),r["\u0275\u0275pipe"](12,"checkLabel"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](13,"div",6),r["\u0275\u0275listener"]("click",(function(){return t.back()})),r["\u0275\u0275elementStart"](14,"div",8),r["\u0275\u0275text"](15),r["\u0275\u0275pipe"](16,"checkLabel"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](17,j,3,2,"div",9),r["\u0275\u0275pipe"](18,"checkActive"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](19,"div",10),r["\u0275\u0275element"](20,"div",11),r["\u0275\u0275element"](21,"div",12),r["\u0275\u0275element"](22,"div",13),r["\u0275\u0275element"](23,"div",14),r["\u0275\u0275element"](24,"div",15),r["\u0275\u0275element"](25,"div",16),r["\u0275\u0275element"](26,"div",17),r["\u0275\u0275elementStart"](27,"div",18),r["\u0275\u0275element"](28,"div",19),r["\u0275\u0275element"](29,"div",20),r["\u0275\u0275elementEnd"](),r["\u0275\u0275element"](30,"div",21),r["\u0275\u0275elementStart"](31,"div",22),r["\u0275\u0275element"](32,"div",19),r["\u0275\u0275element"](33,"div",20),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](34,"div",23),r["\u0275\u0275element"](35,"div",19),r["\u0275\u0275element"](36,"div",20),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](37,"div",24),r["\u0275\u0275element"](38,"div",25),r["\u0275\u0275element"](39,"div",26),r["\u0275\u0275element"](40,"div",27),r["\u0275\u0275element"](41,"div",28),r["\u0275\u0275element"](42,"div",29),r["\u0275\u0275element"](43,"div",30),r["\u0275\u0275element"](44,"div",31),r["\u0275\u0275element"](45,"div",32),r["\u0275\u0275element"](46,"div",33),r["\u0275\u0275element"](47,"div",34),r["\u0275\u0275elementEnd"](),r["\u0275\u0275element"](48,"div",35),r["\u0275\u0275element"](49,"div",36),r["\u0275\u0275element"](50,"div",37),r["\u0275\u0275element"](51,"div",38),r["\u0275\u0275element"](52,"div",39),r["\u0275\u0275element"](53,"div",40),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e&&(r["\u0275\u0275advance"](3),r["\u0275\u0275textInterpolate"](t.tittle),r["\u0275\u0275advance"](3),r["\u0275\u0275textInterpolate2"]("",r["\u0275\u0275pipeBind4"](7,8,"project_code_save_dialog",t.formConfig,"Project-creation","Project Code :"),"",t.projectCode,""),r["\u0275\u0275advance"](3),r["\u0275\u0275styleProp"]("background",t.button),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate"](r["\u0275\u0275pipeBind4"](12,13,"goTo_project",t.formConfig,"Project-creation","Go to Project")),r["\u0275\u0275advance"](4),r["\u0275\u0275textInterpolate"](r["\u0275\u0275pipeBind4"](16,18,"home_screen",t.formConfig,"Project-creation","Home Screen")),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngIf","Edit"!=t.mode&&r["\u0275\u0275pipeBind3"](18,23,"send_mail",t.formConfig,"save-dialog")))},directives:[h.NgIf,u.a],pipes:[m.a,g.a],styles:[".font-family[_ngcontent-%COMP%]{font-family:var(--editProjectFont)!important}"]}),e})()},"8EZa":function(e,t,i){"use strict";i.d(t,"a",(function(){return p}));var o=i("mrSG"),s=i("xG9w"),n=i("fXoL"),r=i("tk/3"),a=i("flaP"),c=i("1A3m"),l=i("XXEo");let p=(()=>{class e{constructor(e,t,i,o){this.http=e,this.rolesService=t,this.toaster=i,this.loginService=o,this.projectRoleAccessList=[],this.roleList=[],this.mainAccess=[],this.projectEmployeeRole=[],this.projectListAccess=[],this.status_list=[],this.checkProjectApplicationAccess(),this.getProjectOverallAccess(),this.getUserRoleAccessProjectList(),this.getStatusList()}getProjectRoleAccess(){return new Promise((e,t)=>{if(this.projectRoleAccessList&&this.projectRoleAccessList.length>0)return e(this.projectRoleAccessList);this.http.post("/api/pm/auth/getProjectRoleAccessList",{}).subscribe(t=>(this.projectRoleAccessList=t,e(t)),e=>t(e))})}checkProjectApplicationAccess(){return new Promise((e,t)=>Object(o.c)(this,void 0,void 0,(function*(){if(this.rolesService.roles&&this.rolesService.roles.length>0){let t=s.where(this.rolesService.roles,{application_id:915});this.projectEmployeeRole=t;let i=s.where(this.projectEmployeeRole,{object_id:6});return e(i.length>0)}{const t=this.loginService.getProfile().profile;yield this.getAccessList(t.oid,"project").subscribe(t=>{let i=s.where(t,{application_id:915});this.projectEmployeeRole=i;let o=s.where(this.projectEmployeeRole,{object_id:6});return e(o.length>0)})}})))}checkProjectRoleAccess(e){let t=s.where(this.projectEmployeeRole,{application_id:915,object_id:6});if(t.length>0){if("*"==t[0].object_value)return{messType:"S",message:"Admin access enabled!",data:this.getAccessTopPriority([1],e)};if("True"==t[0].object_value){let i="string"==typeof t[0].object_entries?JSON.parse(t[0].object_entries):t[0].object_entries;return"null"==i||"*"==i||null==i||"null"==i?{messType:"S",message:"Team Member Access enabled",data:this.getAccessTopPriority([9],e)}:{messType:"S",message:"Project Role Access enabled",data:this.getAccessTopPriority(i,e)}}return{messType:"E",message:"Not authorized!",data:this.getAccessTopPriority([],e)}}return{messType:"E",message:"Not authorized!",data:this.getAccessTopPriority([],e)}}getAccessTopPriority(e,t){return s.filter(t,t=>{for(let i of e)if(t.id==i)return t})}getProjectRoleObjectAccess(){return Object(o.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>Object(o.c)(this,void 0,void 0,(function*(){this.projectRoleAccess&&"S"==this.projectRoleAccess.messType?e(this.projectRoleAccess):yield this.getProjectRoleAccess().then(t=>{let i=this.checkProjectRoleAccess(this.projectRoleAccessList);if("S"==i.messType){let t=i.data;0==t.length?(this.projectRoleAccess={messType:"E",message:"No access to Projects",access:!1,data:[]},e(this.projectRoleAccess)):(this.projectRoleAccess={messType:"S",message:"Access to Project",access:!0,data:t},e(this.projectRoleAccess))}else this.projectRoleAccess={messType:"E",message:"No access to Projects",access:!1,data:[]},e(this.projectRoleAccess)},e=>{this.toaster.showError("This action is not allowed!","Dismiss",3e3),t(!1)})})))}))}getProjectOverallAccess(){return new Promise((e,t)=>Object(o.c)(this,void 0,void 0,(function*(){if(this.roleList&&this.roleList.length>0)return e(this.roleList);yield this.getProjectRoleObjectAccess().then(i=>{if("S"!=i.messType)return e([]);if(!(i.data.length>0))return e([]);{let o=s.pluck(i.data,"id");if(!(o.length>0))return e([]);this.http.post("/api/pm/auth/getProjectOverallAccess",{mainAccess:o}).subscribe(t=>(console.log(this.roleList),this.roleList=t,e(t)),e=>t(e))}})})))}getProjectObjectAccess(e){return s.where(this.roleList,{object_id:e}).length>0}getEmployeeRoleObjectAccess(e){return s.where(this.projectEmployeeRole,{object_id:e}).length>0}getUserRoleAccessProjectList(){return new Promise((e,t)=>Object(o.c)(this,void 0,void 0,(function*(){this.projectListAccess&&this.projectListAccess.length>0?e(this.projectListAccess):yield this.getProjectRoleObjectAccess().then(i=>"S"!=i.messType?e([]):i.data.length>0?void this.http.post("/api/pm/auth/getUserRoleAccessProjectList",{mainAccess:i.data}).subscribe(t=>(this.projectListAccess=t,e(t)),e=>t(e)):e([]))})))}getAccessList(e,t){return this.http.post("/api/pm/auth/getAccessFor",{oid:e,type:t})}getReadWriteAccess(e,t){return new Promise((i,n)=>Object(o.c)(this,void 0,void 0,(function*(){yield this.getUserRoleAccessProjectList().then(n=>Object(o.c)(this,void 0,void 0,(function*(){yield this.getStatusList().then(o=>{let r=s.where(n,{item_id:t,project_id:e,object_access:"Both"});if(r.length>0){let e=s.where(o,{id:r[0].item_status_id,object_access:"Both"});i(e.length>0)}else i(!1)})})),e=>{i(!0)})})))}getAdminAccess(){return new Promise((e,t)=>Object(o.c)(this,void 0,void 0,(function*(){this.http.post("/api/pm/auth/getAdminAccessList",{}).subscribe(t=>e(t),e=>t(e))})))}getStatusList(){return new Promise((e,t)=>{if(this.status_list&&this.status_list.length>0)return e(this.status_list);this.http.post("/api/pm/masterData/getStatusList",{}).subscribe(t=>(this.status_list=t,e(this.status_list)),e=>t(e))})}getProjectWiseObjectAccess(e,t,i){return Object(o.c)(this,void 0,void 0,(function*(){let o=s.pluck(s.where(this.projectListAccess,{project_id:e,item_id:t}),"role_access_id"),n=yield this.getSuperiorRole(o);return s.where(this.roleList,{role_id:n,object_id:i}).length>0}))}getSuperiorRole(e){let t=s.filter(this.projectRoleAccessList,t=>{if(s.contains(e,t.id))return t});return s.sortBy(t,"sequence_list")[0].id}updateProjectStatus(e,t,i){for(let o of this.projectListAccess)o.project_id==e&&o.item_id==t&&(o.item_status_id=i)}}return e.\u0275fac=function(t){return new(t||e)(n["\u0275\u0275inject"](r.c),n["\u0275\u0275inject"](a.a),n["\u0275\u0275inject"](c.a),n["\u0275\u0275inject"](l.a))},e.\u0275prov=n["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},R898:function(e,t,i){"use strict";i.d(t,"a",(function(){return n}));var o=i("xG9w"),s=i("fXoL");let n=(()=>{class e{transform(e,t,i,s){let n=o.findWhere(t,{field_name:e,type:i});return n&&n.label?n.label:s}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=s["\u0275\u0275definePipe"]({name:"checkLabel",type:e,pure:!0}),e})()},sRDL:function(e,t,i){"use strict";i.d(t,"a",(function(){return a}));var o=i("XNiG"),s=i("fXoL"),n=i("tk/3"),r=i("wfSS");let a=(()=>{class e{constructor(e,t){this.http=e,this.shepherdService=t,this.dataSubject=new o.b,this.filetrConfig$=this.dataSubject.asObservable(),this.projectHeaderConfig={displayActiveStatus:!1,displayCompletedStatus:!1,displayLateStatus:!1,displayHoldStatus:!1,displaySearch:!0,displayViewOption:!0,displayCustomize:!0,displayGroupBy:!0,displayProjectToggle:!0,displayFilter:!0,displayCreateButton:!0,displayInfoButton:!0,displayMoreOptions:!0,activeStatusCall:()=>{},completedStatusCall:()=>{},lateStatusCall:()=>{},holdStatusCall:()=>{},searchCall:()=>{},searchData:"",viewOptionCall:()=>{},viewList:[],viewOptionData:"",customizeCall:()=>{},customizeData:[],groupByCall:()=>{},groupByData:"",projectToggleCall:()=>{},filterData:[],filterCall:()=>{},createButtonCall:()=>{},createData:[],infoButtonCall:()=>{},infoButtonData:[],moreOptionCall:()=>{},moreButtonData:[],searchPlaceholder:"",openWalkthrough:()=>{},transformView:()=>{},groupByView:()=>{},displaySelectedStatus:{},displaySelectedStatusCount:0,displayStatusList:[],changeStatusDisplay:()=>{},viewOptionToolTip:"",columnList:[],transformViewData:"",openCreateCall:()=>{},updateUserConfig:()=>{},toggleViewList:[],userFilterList:[],projectCreationAccess:!1,portfolioCreationAccess:!1,invoiceCreationAccess:!1,projectTemplateCreationAccess:!1,planningBoardTemplateAccess:!1,projectGroupByIcon:!1,projectViewIcon:!1,projectSearchDisplay:!1,projectStatusDisplay:!1,groupByViewList:[],updateStatusConfig:()=>{},searchConfig:()=>{},selectedSearch:{},switchView:()=>{},viewOptionMatIcon:"",groupByMatIcon:"",groupByTooltip:"",toggleViewData:"",toggleView:()=>{},transformColumnConfig:()=>{},minColumnLength:0,maxColumnLength:0,refreshProjectList:e=>{},portfolioCount:0,expandPortfolio:!1,callExpandCollapseAll:()=>{},saveUserConfiguration:()=>{},refreshProjectCard:()=>{},openAdminSettings:()=>{}}}updateFilterConfig(e){this.dataSubject.next(e)}getTotalProjectList(e,t,i){return new Promise((o,s)=>{this.http.post("/api/pm/planning/getTotalProjectList",{mainAccess:e,filterConfig:t,date:i}).subscribe(e=>{o(e)},e=>{s(e)})})}saveUserConfiguration(e,t,i){return new Promise((o,s)=>{this.http.post("/api/pm/planning/saveUserConfiguration",{config:e,screen:t,currentDate:i}).subscribe(e=>{o(e)},e=>{s(e)})})}retrieveUserConfig(e){return new Promise((t,i)=>{this.http.post("/api/pm/planning/retrieveUserConfig",{screen:e}).subscribe(e=>{t(e)},e=>{i(e)})})}getProjectCardData(e,t,i){return new Promise((o,s)=>{this.http.post("/api/pm/planning/getProjectCardData",{itemId:e,projectId:t,date:i}).subscribe(e=>{o(e)},e=>{s(e)})})}getTotalPortfolioList(e,t,i){return new Promise((o,s)=>{this.http.post("/api/pm/planning/getTotalPortfolioList",{mainAccess:e,filterConfig:t,date:i}).subscribe(e=>{o(e)},e=>{s(e)})})}moveToExecution(e,t,i){return new Promise((o,s)=>{this.http.post("/api/pm/planning/moveProjectToExcuetion",{projectId:e,itemId:t,date:i}).subscribe(e=>{o(e)},e=>{s(e)})})}moveToCompleted(e,t,i){return new Promise((o,s)=>{this.http.post("/api/pm/planning/moveToCompleted",{projectId:e,itemId:t,date:i}).subscribe(e=>{o(e)},e=>{s(e)})})}moveToHold(e,t,i){return new Promise((o,s)=>{this.http.post("/api/pm/planning/moveToHold",{projectId:e,itemId:t,date:i}).subscribe(e=>{o(e)},e=>{s(e)})})}moveToClosed(e,t,i){return new Promise((o,s)=>{this.http.post("/api/pm/planning/moveToClosed",{projectId:e,itemId:t,date:i}).subscribe(e=>{o(e)},e=>{s(e)})})}startTour(e,t){this.shepherdService.defaultStepOptions=e,this.shepherdService.modal=!0,this.shepherdService.confirmCancel=!1,this.shepherdService.addSteps(t),this.shepherdService.start()}completeTour(){this.shepherdService&&this.shepherdService.isActive&&this.shepherdService.complete()}checkIfTourIsActive(){return this.shepherdService.isActive}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275inject"](n.c),s["\u0275\u0275inject"](r.a))},e.\u0275prov=s["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()}}]);