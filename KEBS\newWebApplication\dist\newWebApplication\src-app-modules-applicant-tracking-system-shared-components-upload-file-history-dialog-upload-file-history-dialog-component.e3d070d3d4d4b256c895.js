(window.webpackJsonp=window.webpackJsonp||[]).push([[942],{DnWP:function(t,e,a){"use strict";a.d(e,"a",(function(){return o}));var n=a("tk/3"),i=a("wd/R"),d=a("fXoL");let o=(()=>{class t{constructor(t){this._http=t}fetchCandidatesForJob(t){return this._http.post("api/ats/candidate/fetchCandidatesForJob",{jobDetails:t})}deleteCandidatefromTag(t){return this._http.post("api/ats/candidate/deleteCandidatefromTag",{tagDetails:t})}tagCandidates(t){return this._http.post("api/ats/candidate/tagCandidates",{tagDetails:t})}moveCandidateToTalentPipeline(t,e,a,n,i,d,o){return this._http.post("api/ats/candidate/moveCandidateToTalentPipeline",{candidateIds:t,jobId:e,isBulkSelectActive:a,candidatesExcluded:n,stageId:i,filter:d,search_params:o})}blacklistCandidates(t,e,a,n,i,d,o,r){return this._http.post("api/ats/candidate/blacklistCandidates",{candidateIds:t,isBulkSelectActive:e,candidatesExcluded:a,stageId:n,jobId:i,filter:d,search_params:o,candidateStatusId:r})}updateCandidateJobStage(t){return this._http.post("api/ats/candidate/updateCandidateJobStage",{details:t})}sendEmailForCandidates(t){return this._http.post("api/ats/utilService/sendEmailForCandidates",{details:t})}uploadBenchMarkDetails(t,e){return this._http.post("api/ats/candidate/uploadBenchMarkDetails",{jobId:t,data:e})}uploadAptitudeScoreDetails(t,e,a,n){return this._http.post("api/ats/candidate/uploadAptitudeScoreDetails",{jobId:t,data:e,imageLinkArray:a,reportLinkArray:n})}uploadNonScientificScoreDetails(t,e,a,n){return this._http.post("api/ats/candidate/uploadNonScientificScoreDetails",{jobId:t,data:e,imageLinkArray:a,reportLinkArray:n})}downloadBenchmarkReport(t,e,a=0){return this._http.post("api/ats/candidate/downloadBenchmarkReport",{jobId:t,candidateId:a,filter:e})}downloadAptitudeReport(t,e,a=0){return this._http.post("api/ats/candidate/downloadAptitudeReport",{jobId:t,candidateId:a,filter:e})}downloadNonScientificReport(t,e,a=0){return this._http.post("api/ats/candidate/downloadNonScientificReport",{jobId:t,candidateId:a,filter:e})}fetchAllcandidateTags(){return this._http.post("api/ats/candidate/fetchAllcandidateTags",{})}getCandidateOtherApplicationDetails(t,e){return this._http.post("api/ats/candidate/getCandidateOtherApplicationDetails",{jobId:t,candidateId:e})}updateCandidateStageStatus(t){return this._http.post("api/ats/candidate/updateCandidateStageStatus",{details:t})}getCandidateBasicDetails(t,e,a){return this._http.post("api/ats/candidate/getCandidateBasicDetails",{jobId:t,candidateId:e,currentUserAid:a})}getInterviewPanelist(t){return this._http.post("api/ats/candidate/getInterviewPanelist",{details:t})}getInterviewerScoreDetails(t){return this._http.post("api/ats/candidate/getInterviewerScoreDetails",{details:t})}getCandidateResumeDetails(t,e){return this._http.post("api/ats/candidate/getCandidateResumeDetails",{jobId:t,candidateId:e})}fetchCandidateCardViewScoreDetails(t,e,a,n,i,d){return this._http.post("api/ats/candidate/fetchCandidateCardViewScoreDetails",{jobId:t,stageId:e,sectionId:a,sortByMetricId:n,skip:i,limit:d})}fetchScorecardSectionDetails(t,e){return this._http.post("api/ats/candidate/fetchScorecardSectionDetails",{jobId:t,stageId:e})}fetchScorecardMetricDetails(t,e,a){return this._http.post("api/ats/candidate/fetchScorecardMetricDetails",{jobId:t,stageId:e,sectionId:a})}getCandidateCommentsDetails(t,e,a,n){return this._http.post("api/ats/candidate/getCandidateCommentsDetails",{jobId:t,candidateId:e,currentUserAid:a,searchText:n})}postCandidateComments(t,e){return this._http.post("api/ats/candidate/postCandidateComments",{candidateId:t,commentsDetails:e})}getCandidateInterviewDetails(t,e){return this._http.post("api/ats/candidate/getCandidateInterviewDetails",{jobId:t,candidateId:e})}scheduleInterview(t){return this._http.post("api/ats/interview/scheduleInterview",{details:t})}getCandidateDocumentDetails(t,e){return this._http.post("api/ats/candidate/fetchcandidateDocuments",{jobId:t,candidateId:e})}fetchCandidateDetailsForQueue(t,e,a){return this._http.post("api/ats/candidate/fetchCandidateDetailsForQueue",{jobId:t,candidateId:e,currentUserAid:a})}getCandidateStreamDetails(t,e){return this._http.post("api/ats/candidate/getCandidateStreamDetails",{jobId:t,candidateId:e})}fetchCandidateUploadTemplate(){return this._http.post("api/ats/bulkActions/fetchCandidateUploadTemplate",{})}simulateCandidateApply(t,e){return this._http.post("api/ats/bulkActions/simulateCandidateApply",{jobId:t,data:e})}performCandidateBulkApply(t,e,a,n,i,d){return this._http.post("api/ats/bulkActions/performCandidateBulkApply",{jobId:t,data:e,fileName:a,fileSize:n,fileUploadStatus:i,isFromTalentPipeline:d})}fetchCandidateUploadFileHistory(t){return this._http.post("api/ats/bulkActions/fetchCandidateUploadFileHistory",{job_id:t})}fetchCandidateUpdSimulationResult(t){return this._http.post("api/ats/bulkActions/fetchCandidateUpdSimulationResult",{id:t})}getAllCandidateDetails(t,e,a,n){return this._http.post("api/ats/candidate/getAllCandidateDetails",{skip:t.skip,limit:t.limit,sort:t.sort,filter:t.filter,condition_query:t.condition_query,search_params:t.search_params,aid:e,isTalentPipeLine:a,candidateStatusId:t.candidateStatusId,isCampusJobTalentPipeline:n})}getTotalCandidateCount(t,e,a){return this._http.post("api/ats/candidate/getTotalCandidateCount",{skip:t.skip,limit:t.limit,sort:t.sort,filter:t.filter,condition_query:t.condition_query,search_params:t.search_params,isTalentPipeLine:e,candidateStatusId:t.candidateStatusId,isCampusJobTalentPipeline:a})}performResumeSimulation(t,e){return this._http.post("api/ats/bulkActions/simulateBulkResumeUpload",{data:e,jobId:t})}performCandidateBulkResumeUpload(t,e,a,n){return this._http.post("api/ats/bulkActions/performCandidateBulkResumeApply",{data:t,jobId:e,isFromTalentPipeline:a,dataArrayForResumeUpload:n})}getCandidateLatestJobDetails(t){return this._http.post("api/ats/candidate/getCandidateLatestJobDetails",{candidateId:t})}getScheduledInterviewDetails(t){return this._http.post("api/ats/interview/getScheduledInterviewDetails",{interviewId:t})}rescheduleInterview(t){return this._http.post("api/ats/interview/rescheduleInterview",{details:t})}cancelScheduledInterview(t){return this._http.post("api/ats/interview/cancelScheduledInterview",{interviewId:t})}getCandidateCountForJobByJobId(t){return this._http.post("api/ats/candidate/getCandidateCountForJobByJobId",{jobDetails:t})}updateStarRating(t,e,a,n){return this._http.post("api/ats/candidate/updateStarRating",{jobId:t,candidateId:e,rating:a,currentUserAid:n})}getMeetingsofInterviewers(t){return this._http.post("api/ats/interview/getMeetingsofInterviewers",{details:t})}getScorecardOtherDetails(t,e,a,n){return this._http.post("api/ats/candidate/getScorecardOtherDetails",{jobId:t,candidateId:e,stageId:a,interviewer:n})}sendDocumentUploadLink(t){return this._http.post("api/ats/candidate/sendDocumentUploadLink",{details:t})}getCandidateCustomQuestionDetails(t,e,a){return this._http.post("api/ats/candidate/getCandidateCustomQuestionDetails",{jobId:t,candidateId:e,currentUserAid:a})}uploadCandidateDocuments(t,e,a,i){const d=new FormData;d.append("jobId",t),d.append("candidateId",e),d.append("document",JSON.stringify(a)),d.append("file",i);const o=new n.f;return this._http.post("/api/ats/candidate/uploadCandidateDocuments",d,{headers:o})}deleteCandidateDocuments(t,e,a){return this._http.post("/api/ats/candidate/deleteCandidateDocuments",{jobId:t,candidateId:e,documentId:a})}retrieveCandidateOfferTrackingDetails(t,e){return this._http.post("api/ats/candidate/retrieveCandidateOfferTrackingDetails",{jobId:t,candidateId:e,currentDate:i().format("YYYY-MM-DD")})}getAllCandidateIds(t,e,a){return this._http.post("api/ats/candidate/getAllCandidateIds",{skip:t.skip,limit:t.limit,sort:t.sort,filter:t.filter,condition_query:t.condition_query,search_params:t.search_params,isTalentPipeLine:e,candidateStatusId:t.candidateStatusId,isCampusJobTalentPipeline:a})}getCandidateIdsForJobByJobId(t){return this._http.post("api/ats/candidate/getCandidateIdsForJobByJobId",{jobDetails:t})}uploadCertifications(t){return this._http.post("/api/ats/candidate/uploadCertifications",t)}performUploadUpdation(t,e){return this._http.post("api/ats/candidate/performUploadUpdation",{fileUploadDetails:t,candidateId:e})}getCandidateCertificates(t){return this._http.post("api/ats/candidate/getCandidateCertificates",{candidateId:t})}deleteCandidateCertificate(t){return this._http.post("api/ats/candidate/deleteCandidateCertificate",{certificateDetails:t})}createCampusInterviewForcandidates(t){return this._http.post("api/ats/interview/createCampusInterviewForcandidates",{details:t})}storeCandidateEmails(t){return this._http.post("api/ats/interview/storeCandidateEmails",{details:t})}getInterviewEmailData(t,e){return this._http.post("api/ats/interview/getInterviewEmailData",{jobId:e,candidateId:t})}}return t.\u0275fac=function(e){return new(e||t)(d["\u0275\u0275inject"](n.c))},t.\u0275prov=d["\u0275\u0275defineInjectable"]({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()},uGzg:function(t,e,a){"use strict";a.r(e),a.d(e,"UploadFileHistoryDialogComponent",(function(){return j}));var n=a("mrSG"),i=a("0IaG"),d=a("1G5W"),o=a("XNiG"),r=a("fXoL"),s=a("DnWP"),l=a("YVm3"),c=a("XNFG"),p=a("c7zN"),h=a("NFeN"),m=a("ofXK"),C=a("Xa2L"),u=a("Qu3c"),f=a("f0Cb"),g=a("wC0v");function _(t,e){1&t&&(r["\u0275\u0275elementStart"](0,"div",19),r["\u0275\u0275element"](1,"mat-spinner",20),r["\u0275\u0275elementEnd"]())}function I(t,e){1&t&&(r["\u0275\u0275elementStart"](0,"div",27),r["\u0275\u0275namespaceSVG"](),r["\u0275\u0275elementStart"](1,"svg",28),r["\u0275\u0275elementStart"](2,"mask",29),r["\u0275\u0275element"](3,"rect",30),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](4,"g",31),r["\u0275\u0275element"](5,"path",32),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]())}function v(t,e){if(1&t&&(r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275template"](1,I,6,0,"div",25),r["\u0275\u0275elementStart"](2,"span",26),r["\u0275\u0275text"](3),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementContainerEnd"]()),2&t){const t=e.$implicit,a=e.index;r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",0==a),r["\u0275\u0275advance"](1),r["\u0275\u0275classMap"](t.col),r["\u0275\u0275property"]("matTooltip",t.label),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate"](t.label)}}function D(t,e){1&t&&(r["\u0275\u0275elementStart"](0,"div",27),r["\u0275\u0275namespaceSVG"](),r["\u0275\u0275elementStart"](1,"svg",28),r["\u0275\u0275element"](2,"path",36),r["\u0275\u0275element"](3,"path",37),r["\u0275\u0275element"](4,"path",38),r["\u0275\u0275element"](5,"path",39),r["\u0275\u0275element"](6,"path",40),r["\u0275\u0275element"](7,"path",41),r["\u0275\u0275element"](8,"path",42),r["\u0275\u0275element"](9,"path",43),r["\u0275\u0275element"](10,"path",44),r["\u0275\u0275element"](11,"path",45),r["\u0275\u0275element"](12,"path",46),r["\u0275\u0275elementStart"](13,"defs"),r["\u0275\u0275elementStart"](14,"linearGradient",47),r["\u0275\u0275element"](15,"stop",48),r["\u0275\u0275element"](16,"stop",49),r["\u0275\u0275element"](17,"stop",50),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]())}function y(t,e){if(1&t&&(r["\u0275\u0275elementStart"](0,"span",51),r["\u0275\u0275text"](1),r["\u0275\u0275elementEnd"]()),2&t){const t=r["\u0275\u0275nextContext"]().$implicit,e=r["\u0275\u0275nextContext"]().$implicit;r["\u0275\u0275classMap"](t.col),r["\u0275\u0275property"]("matTooltip",e[t.fieldKey]),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"](" ",e[t.fieldKey]," ")}}function b(t,e){if(1&t&&(r["\u0275\u0275elementStart"](0,"span",51),r["\u0275\u0275pipe"](1,"dateFormat"),r["\u0275\u0275text"](2),r["\u0275\u0275pipe"](3,"dateFormat"),r["\u0275\u0275elementEnd"]()),2&t){const t=r["\u0275\u0275nextContext"]().$implicit,e=r["\u0275\u0275nextContext"]().$implicit;r["\u0275\u0275classMap"](t.col),r["\u0275\u0275property"]("matTooltip",r["\u0275\u0275pipeBind2"](1,4,e[t.fieldKey],"DD MMM YYYY HH:mm A")),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate1"](" ",r["\u0275\u0275pipeBind2"](3,7,e[t.fieldKey],"DD MMM YYYY HH:mm A")," ")}}function w(t,e){if(1&t){const t=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"mat-icon",15),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](t);const e=r["\u0275\u0275nextContext"](3).index;return r["\u0275\u0275nextContext"](3).downloadData(e)})),r["\u0275\u0275text"](1," download "),r["\u0275\u0275elementEnd"]()}}function S(t,e){1&t&&r["\u0275\u0275element"](0,"mat-spinner",55)}function L(t,e){if(1&t&&(r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275elementStart"](1,"span",52),r["\u0275\u0275template"](2,w,2,0,"mat-icon",53),r["\u0275\u0275template"](3,S,1,0,"mat-spinner",54),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementContainerEnd"]()),2&t){const t=r["\u0275\u0275nextContext"](2).$implicit;r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngIf",!(null!=t&&t.loading)),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",null==t?null:t.loading)}}function x(t,e){if(1&t&&(r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275template"](1,D,18,0,"div",25),r["\u0275\u0275template"](2,y,2,4,"span",35),r["\u0275\u0275template"](3,b,4,10,"span",35),r["\u0275\u0275template"](4,L,4,2,"ng-container",22),r["\u0275\u0275elementContainerEnd"]()),2&t){const t=e.$implicit,a=e.index,n=r["\u0275\u0275nextContext"](4);r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",0==a),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","text"==t.type),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","date"==t.type),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",a==n.tableColumns.length-1)}}function M(t,e){1&t&&r["\u0275\u0275element"](0,"mat-divider",56)}function E(t,e){if(1&t&&(r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275elementStart"](1,"div",33),r["\u0275\u0275template"](2,x,5,4,"ng-container",24),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](3,M,1,0,"mat-divider",34),r["\u0275\u0275elementContainerEnd"]()),2&t){const t=e.index,a=r["\u0275\u0275nextContext"](3);r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngForOf",a.tableColumns),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",t!=a.historyData.length-1)}}function k(t,e){if(1&t&&(r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275elementStart"](1,"div",23),r["\u0275\u0275template"](2,v,4,5,"ng-container",24),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](3,E,4,2,"ng-container",24),r["\u0275\u0275elementContainerEnd"]()),2&t){const t=r["\u0275\u0275nextContext"](2);r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngForOf",t.tableColumns),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngForOf",t.historyData)}}function O(t,e){1&t&&(r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275elementStart"](1,"div",57),r["\u0275\u0275element"](2,"img",58),r["\u0275\u0275elementStart"](3,"div",59),r["\u0275\u0275text"](4,"No File History Found"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementContainerEnd"]())}function P(t,e){if(1&t&&(r["\u0275\u0275elementStart"](0,"div",21),r["\u0275\u0275template"](1,k,4,2,"ng-container",22),r["\u0275\u0275template"](2,O,5,0,"ng-container",22),r["\u0275\u0275elementEnd"]()),2&t){const t=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",t.historyData&&t.historyData.length>0),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",!t.historyData||0==t.historyData.length)}}let j=(()=>{class t{constructor(t,e,a,n,i,d){this.data=t,this._dialogRef=e,this._candidateService=a,this._jobService=n,this._toaster=i,this._utilitiesService=d,this._onDestroy=new o.b,this.tableColumns=[{label:"NAME",col:"col-4",fieldKey:"file_name",type:"text"},{label:"UPLOADED DATE",col:"col-3",fieldKey:"modified_date_and_time",type:"date"},{label:"STATUS",col:"col-2",fieldKey:"file_upload_status",type:"text"},{label:"FILE SIZE",col:"col-2",fieldKey:"file_size",type:"text"}],this.historyData=[],this.isLoading=!0}ngOnInit(){"candidate"==this.data.module?this.fetchCandidateUploadFileHistory():"job"==this.data.module&&this.fetchJobUploadFileHistory()}onClose(){this._dialogRef.close()}downloadData(t){this.historyData[t].loading=!0,"candidate"==this.data.module?this.fetchCandidateUpdSimulationResult(this.historyData[t]._id,t):"job"==this.data.module&&this.fetchJobSimulationResult(this.historyData[t]._id,t)}fetchCandidateUploadFileHistory(){return Object(n.c)(this,void 0,void 0,(function*(){return new Promise((t,e)=>this._candidateService.fetchCandidateUploadFileHistory(this.data.jobId).pipe(Object(d.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?this.historyData=e.data:this._toaster.showError("Error",e.msg,7e3),this.isLoading=!1,t(!0)},error:t=>{this._toaster.showError("Error",t.message?t.message:"Retrieval Failed!",7e3),this.isLoading=!1,e()}}))}))}fetchJobUploadFileHistory(){return Object(n.c)(this,void 0,void 0,(function*(){return new Promise((t,e)=>this._jobService.fetchJobUploadFileHistory().pipe(Object(d.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?this.historyData=e.data:this._toaster.showError("Error",e.msg,7e3),this.isLoading=!1,t(!0)},error:t=>{this._toaster.showError("Error",t.message?t.message:"Retrieval Failed!",7e3),this.isLoading=!1,e()}}))}))}fetchCandidateUpdSimulationResult(t,e){return Object(n.c)(this,void 0,void 0,(function*(){return new Promise((a,n)=>this._candidateService.fetchCandidateUpdSimulationResult(t).pipe(Object(d.a)(this._onDestroy)).subscribe({next:t=>{0==t.err&&t.data.length>0?this._utilitiesService.exportDataAsExcelFile(t.data[0].simulation_result,this.data.jobId?this.data.jobId+" File History":"File History","Data"):this._toaster.showError("Error",t.msg,7e3),this.historyData[e].loading=!1,a(!0)},error:t=>{this._toaster.showError("Error",t.message?t.message:"Simulation Data Retrieval Failed!",7e3),this.historyData[e].loading=!1,n()}}))}))}fetchJobSimulationResult(t,e){return Object(n.c)(this,void 0,void 0,(function*(){return new Promise((a,n)=>this._jobService.fetchJobSimulationResult(t).pipe(Object(d.a)(this._onDestroy)).subscribe({next:t=>{0==t.err&&t.data.length>0?this._utilitiesService.exportDataAsExcelFile(t.data[0].simulation_result,"Job File History","Data"):this._toaster.showError("Error",t.msg,7e3),this.historyData[e].loading=!1,a(!0)},error:t=>{this._toaster.showError("Error",t.message?t.message:"Simulation Data Retrieval Failed!",7e3),this.historyData[e].loading=!1,n()}}))}))}}return t.\u0275fac=function(e){return new(e||t)(r["\u0275\u0275directiveInject"](i.a),r["\u0275\u0275directiveInject"](i.h),r["\u0275\u0275directiveInject"](s.a),r["\u0275\u0275directiveInject"](l.a),r["\u0275\u0275directiveInject"](c.a),r["\u0275\u0275directiveInject"](p.a))},t.\u0275cmp=r["\u0275\u0275defineComponent"]({type:t,selectors:[["app-upload-file-history-dialog"]],decls:22,vars:2,consts:[[1,"main-container"],[1,"d-flex","align-items-center","justify-content-between","header"],[1,"title","p-0"],[1,"d-flex","align-items-center"],["width","269","height","56","viewBox","0 0 269 56","fill","none","xmlns","http://www.w3.org/2000/svg"],["d","M133.603 -22.5168V4.76469C133.603 8.35397 132.087 11.811 129.39 14.4067C125.91 17.7302 120.857 19.1903 115.947 18.2782L115.946 18.2781L93.9778 14.2877L103.74 5.17581L116.468 7.51274L116.468 7.51278C119.364 8.04124 122.088 5.97651 122.088 3.19398V-11.8208L133.603 -22.5168Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M179.019 14.2879L157.079 18.2781L157.078 18.2782C152.174 19.1903 147.127 17.73 143.651 14.4063L143.649 14.4043C140.909 11.856 139.394 8.40031 139.394 4.81089V-22.5164L150.895 -11.8208V2.96299C150.895 5.88334 153.764 8.04193 156.758 7.46638C156.758 7.46632 156.758 7.46626 156.759 7.46621L169.221 5.17578L179.019 14.2879Z","fill","#EAEDF3","stroke","#DADCE2","stroke-width","0.421296"],["d","M133.603 37.6429V64.9403L122.088 54.256V38.8889C122.088 36.2929 119.563 34.3707 116.817 34.8512L116.816 34.8515L103.74 37.2322L93.9781 28.1306L115.946 24.1447L115.947 24.1445C120.857 23.2335 125.91 24.6921 129.391 28.012C132.087 30.6048 133.603 34.0579 133.603 37.6429Z","fill","#EAEDF3","stroke","#DADCE2","stroke-width","0.421296"],["d","M139.394 64.9399V37.6584C139.394 34.0692 140.91 30.6122 143.607 28.0166C147.087 24.693 152.141 23.2328 157.05 24.1448L157.051 24.145L179.018 28.1352L169.258 37.2012L155.933 34.7718C155.933 34.7718 155.933 34.7718 155.933 34.7718C153.335 34.2893 150.91 36.1234 150.91 38.6285V54.2438L139.394 64.9399Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M40.0758 14.7878V42.0694C40.0758 45.6587 38.5596 49.1157 35.8631 51.7114C32.3828 55.0348 27.3294 56.495 22.4198 55.5829L22.419 55.5828L0.45043 51.5924L10.2125 42.4805L22.9402 44.8174L22.9404 44.8175C25.8368 45.3459 28.5604 43.2812 28.5604 40.4987V25.4839L40.0758 14.7878Z","fill","#F3F5FA","stroke","#DADCE2","stroke-width","0.421296"],["d","M85.4914 51.5926L63.5512 55.5828L63.5504 55.5829C58.6468 56.495 53.5996 55.0347 50.1236 51.711L50.1214 51.709C47.3813 49.1606 45.8669 45.705 45.8669 42.1156V14.7883L57.3676 25.4838V40.2677C57.3676 43.188 60.2364 45.3466 63.2304 44.7711C63.2307 44.771 63.231 44.771 63.2313 44.7709L75.6933 42.4805L85.4914 51.5926Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M40.0758 14.4832V41.7647C40.0758 45.354 38.5596 48.811 35.8631 51.4067C32.3828 54.7302 27.3294 56.1903 22.4198 55.2782L22.419 55.2781L0.45043 51.2877L10.2125 42.1758L22.9402 44.5127L22.9404 44.5128C25.8368 45.0412 28.5604 42.9765 28.5604 40.194V25.1792L40.0758 14.4832Z","fill","#EAEDF3","fill-opacity","0.5","stroke","#DADCE2","stroke-width","0.421296"],["d","M85.4914 51.2879L63.5512 55.2781L63.5504 55.2782C58.6468 56.1903 53.5996 54.73 50.1236 51.4063L50.1214 51.4043C47.3813 48.856 45.8669 45.4003 45.8669 41.8109V14.4836L57.3676 25.1792V39.963C57.3676 42.8833 60.2364 45.0419 63.2304 44.4664C63.2307 44.4663 63.231 44.4663 63.2313 44.4662L75.6933 42.1758L85.4914 51.2879Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M227.13 14.7878V42.0694C227.13 45.6587 225.614 49.1157 222.918 51.7114C219.437 55.0348 214.384 56.495 209.475 55.5829L209.474 55.5828L187.505 51.5924L197.267 42.4805L209.995 44.8174L209.995 44.8175C212.892 45.3459 215.615 43.2812 215.615 40.4987V25.4839L227.13 14.7878Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M272.546 51.5926L250.606 55.5828L250.605 55.5829C245.702 56.495 240.654 55.0347 237.178 51.711L237.176 51.709C234.436 49.1606 232.922 45.705 232.922 42.1156V14.7883L244.422 25.4838V40.2677C244.422 43.188 247.291 45.3466 250.285 44.7711C250.285 44.771 250.286 44.771 250.286 44.7709L262.748 42.4805L272.546 51.5926Z","stroke","#DADCE2","stroke-width","0.421296"],[1,"icon",3,"click"],[1,"icon"],["class","loading-screen",4,"ngIf"],["class","table",4,"ngIf"],[1,"loading-screen"],["diameter","40",1,"green-spinner"],[1,"table"],[4,"ngIf"],[1,"header-row"],[4,"ngFor","ngForOf"],["class","svg",4,"ngIf"],[1,"header-text","pl-0",3,"matTooltip"],[1,"svg"],["width","24","height","24","viewBox","0 0 24 24","fill","none"],["id","mask0_10694_161713","maskUnits","userSpaceOnUse","x","0","y","0","width","24","height","24",2,"mask-type","alpha"],["width","24","height","24","fill","#D9D9D9"],["mask","url(#mask0_10694_161713)"],["d","M6.3077 21.5C5.80257 21.5 5.375 21.325 5.025 20.975C4.675 20.625 4.5 20.1974 4.5 19.6923V4.3077C4.5 3.80257 4.675 3.375 5.025 3.025C5.375 2.675 5.80257 2.5 6.3077 2.5H13.5019C13.7429 2.5 13.9747 2.5468 14.1971 2.6404C14.4195 2.73398 14.6128 2.86283 14.7769 3.02693L18.973 7.22303C19.1371 7.38714 19.266 7.58042 19.3595 7.80285C19.4531 8.02528 19.5 8.25701 19.5 8.49803V19.6923C19.5 20.1974 19.325 20.625 18.975 20.975C18.625 21.325 18.1974 21.5 17.6922 21.5H6.3077ZM13.5 7.5961V3.99998H6.3077C6.23077 3.99998 6.16024 4.03203 6.09612 4.09613C6.03202 4.16024 5.99997 4.23077 5.99997 4.3077V19.6923C5.99997 19.7692 6.03202 19.8397 6.09612 19.9038C6.16024 19.9679 6.23077 20 6.3077 20H17.6922C17.7692 20 17.8397 19.9679 17.9038 19.9038C17.9679 19.8397 18 19.7692 18 19.6923V8.49995H14.4039C14.1462 8.49995 13.9311 8.41373 13.7587 8.24128C13.5862 8.06884 13.5 7.85378 13.5 7.5961Z","fill","#1C1B1F"],[1,"content-row"],["class","divider",4,"ngIf"],["class","content-text pl-0",3,"class","matTooltip",4,"ngIf"],["d","M14.6865 11.5123L6.38477 10.0498V20.8566C6.38486 20.974 6.40809 21.0902 6.4531 21.1986C6.49812 21.307 6.56406 21.4055 6.64714 21.4885C6.73023 21.5714 6.82884 21.6372 6.93734 21.682C7.04584 21.7269 7.16211 21.7499 7.27952 21.7498H21.6045C21.722 21.7501 21.8385 21.7272 21.9472 21.6825C22.0558 21.6377 22.1546 21.572 22.2379 21.489C22.3211 21.4061 22.3872 21.3075 22.4323 21.199C22.4774 21.0904 22.5007 20.9741 22.5008 20.8566V16.8748L14.6865 11.5123Z","fill","#185C37"],["d","M14.6865 2.25H7.27952C7.16211 2.2499 7.04584 2.27293 6.93734 2.31776C6.82884 2.3626 6.73023 2.42837 6.64714 2.51132C6.56406 2.59426 6.49812 2.69276 6.4531 2.80119C6.40809 2.90962 6.38486 3.02585 6.38477 3.14325V7.125L14.6865 12L19.0823 13.4625L22.5008 12V7.125L14.6865 2.25Z","fill","#21A366"],["d","M6.38477 7.125H14.6865V12H6.38477V7.125Z","fill","#107C41"],["opacity","0.1","d","M12.3263 6.15039H6.38477V18.3379H12.3263C12.563 18.3367 12.7898 18.2423 12.9574 18.0751C13.125 17.9079 13.22 17.6814 13.2218 17.4446V7.04364C13.22 6.80689 13.125 6.58038 12.9574 6.41318C12.7898 6.24598 12.563 6.15157 12.3263 6.15039Z","fill","black"],["opacity","0.2","d","M11.838 6.6377H6.38477V18.8252H11.838C12.0748 18.824 12.3015 18.7296 12.4691 18.5624C12.6368 18.3952 12.7317 18.1687 12.7335 17.9319V7.53095C12.7317 7.2942 12.6368 7.06769 12.4691 6.90049C12.3015 6.73329 12.0748 6.63887 11.838 6.6377Z","fill","black"],["opacity","0.2","d","M11.838 6.6377H6.38477V17.8502H11.838C12.0748 17.849 12.3015 17.7546 12.4691 17.5874C12.6368 17.4202 12.7317 17.1937 12.7335 16.9569V7.53095C12.7317 7.2942 12.6368 7.06769 12.4691 6.90049C12.3015 6.73329 12.0748 6.63887 11.838 6.6377Z","fill","black"],["opacity","0.2","d","M11.3498 6.6377H6.38477V17.8502H11.3498C11.5865 17.849 11.8133 17.7546 11.9809 17.5874C12.1485 17.4202 12.2435 17.1937 12.2453 16.9569V7.53095C12.2435 7.2942 12.1485 7.06769 11.9809 6.90049C11.8133 6.73329 11.5865 6.63887 11.3498 6.6377Z","fill","black"],["d","M2.3955 6.6377H11.349C11.5862 6.6375 11.8137 6.73147 11.9816 6.89897C12.1496 7.06646 12.2441 7.29377 12.2445 7.53095V16.4694C12.2441 16.7066 12.1496 16.9339 11.9816 17.1014C11.8137 17.2689 11.5862 17.3629 11.349 17.3627H2.3955C2.27804 17.3629 2.16168 17.3399 2.05309 17.2951C1.9445 17.2504 1.84581 17.1846 1.76264 17.1016C1.67948 17.0187 1.61348 16.9202 1.56841 16.8117C1.52334 16.7032 1.5001 16.5869 1.5 16.4694V7.53095C1.5001 7.41348 1.52334 7.29719 1.56841 7.18871C1.61348 7.08023 1.67948 6.9817 1.76264 6.89875C1.84581 6.81579 1.9445 6.75004 2.05309 6.70525C2.16168 6.66045 2.27804 6.6375 2.3955 6.6377Z","fill","url(#paint0_linear_10721_162322)"],["d","M4.27539 14.9052L6.15864 11.9922L4.43364 9.0957H5.81889L6.76014 10.9505C6.84714 11.126 6.91014 11.2565 6.93864 11.3435H6.95139C7.01289 11.2025 7.07814 11.0667 7.14639 10.934L8.15289 9.0987H9.42789L7.65864 11.9787L9.47289 14.9075H8.11614L7.02864 12.8742C6.97815 12.7868 6.93524 12.6952 6.90039 12.6005H6.88239C6.85068 12.6928 6.8084 12.7811 6.75639 12.8637L5.63664 14.9052H4.27539Z","fill","white"],["d","M21.6043 2.25H14.6855V7.125H22.4998V3.14325C22.4997 3.02579 22.4765 2.90949 22.4314 2.80102C22.3863 2.69254 22.3203 2.59401 22.2372 2.51105C22.154 2.4281 22.0553 2.36234 21.9467 2.31755C21.8381 2.27276 21.7218 2.2498 21.6043 2.25Z","fill","#33C481"],["d","M14.6855 12H22.4998V16.875H14.6855V12Z","fill","#107C41"],["id","paint0_linear_10721_162322","x1","3.3705","y1","5.9357","x2","10.374","y2","18.0647","gradientUnits","userSpaceOnUse"],["stop-color","#18884F"],["offset","0.5","stop-color","#117E43"],["offset","1","stop-color","#0B6631"],[1,"content-text","pl-0",3,"matTooltip"],[1,"d-flex","align-items-center","col-1","pl-0"],["class","icon",3,"click",4,"ngIf"],["class","green-spinner","diameter","16",4,"ngIf"],["diameter","16",1,"green-spinner"],[1,"divider"],[1,"history-empty-state"],["src","https://assets.kebs.app/ATS-Empty-State.png"],[1,"no-history-title"]],template:function(t,e){1&t&&(r["\u0275\u0275elementStart"](0,"div",0),r["\u0275\u0275elementStart"](1,"div",1),r["\u0275\u0275elementStart"](2,"div",2),r["\u0275\u0275text"](3,"File Upload History"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](4,"div",3),r["\u0275\u0275elementStart"](5,"div"),r["\u0275\u0275namespaceSVG"](),r["\u0275\u0275elementStart"](6,"svg",4),r["\u0275\u0275element"](7,"path",5),r["\u0275\u0275element"](8,"path",6),r["\u0275\u0275element"](9,"path",7),r["\u0275\u0275element"](10,"path",8),r["\u0275\u0275element"](11,"path",9),r["\u0275\u0275element"](12,"path",10),r["\u0275\u0275element"](13,"path",11),r["\u0275\u0275element"](14,"path",12),r["\u0275\u0275element"](15,"path",13),r["\u0275\u0275element"](16,"path",14),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275namespaceHTML"](),r["\u0275\u0275elementStart"](17,"div",15),r["\u0275\u0275listener"]("click",(function(){return e.onClose()})),r["\u0275\u0275elementStart"](18,"mat-icon",16),r["\u0275\u0275text"](19,"close"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](20,_,2,0,"div",17),r["\u0275\u0275template"](21,P,3,2,"div",18),r["\u0275\u0275elementEnd"]()),2&t&&(r["\u0275\u0275advance"](20),r["\u0275\u0275property"]("ngIf",e.isLoading),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",!e.isLoading))},directives:[h.a,m.NgIf,C.c,m.NgForOf,u.a,f.a],pipes:[g.a],styles:[".main-container[_ngcontent-%COMP%]{overflow:hidden}.main-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{width:80%;height:56px;padding:8px 24px;background-color:#f4f4f6;position:absolute;z-index:1}.main-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:18px;font-weight:700;color:#111434}.main-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{font-size:18px;width:18px;height:18px;color:#1c1b1f;cursor:pointer}.main-container[_ngcontent-%COMP%]   .loading-screen[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;margin-top:25%}.main-container[_ngcontent-%COMP%]   .loading-screen[_ngcontent-%COMP%]     .green-spinner circle{stroke:var(--atsprimaryColor)!important}.main-container[_ngcontent-%COMP%]   .history-empty-state[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;overflow:hidden;padding:0 24px;height:var(--dynamicActivityLogSubHeight)}.main-container[_ngcontent-%COMP%]   .history-empty-state[_ngcontent-%COMP%]   .no-history-title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:500;color:#1b2140;margin-bottom:4px}.main-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]{padding:16px 50px;width:100%;margin-top:56px}.main-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   .svg[_ngcontent-%COMP%]{margin-right:8px}.main-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   .header-row[_ngcontent-%COMP%]{display:flex;align-items:center;padding:8px 0 16px}.main-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   .header-row[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]{font-size:14px;font-weight:500;font-family:var(--atsfontFamily);color:#111434;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.main-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   .content-row[_ngcontent-%COMP%]{display:flex;align-items:center;padding:16px 0}.main-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   .content-row[_ngcontent-%COMP%]   .content-text[_ngcontent-%COMP%]{font-size:16px;font-weight:500;font-family:var(--atsfontFamily);color:#111434;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.main-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   .content-row[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]{color:#e8e9ee}.main-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   .content-row[_ngcontent-%COMP%]     .green-spinner circle{stroke:var(--atsprimaryColor)!important}.main-container[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   .content-row[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{font-size:22px;width:22px;height:22px;cursor:pointer;color:#111434}"]}),t})()},wC0v:function(t,e,a){"use strict";a.d(e,"a",(function(){return d}));var n=a("wd/R"),i=a("fXoL");let d=(()=>{class t{transform(t,e,a=!0){if(!t||!n(t,n.ISO_8601,!0).isValid())return"-";if(n(t,"YYYY-MM-DD",!0).isValid())return n(t,"YYYY-MM-DD").format(e);const i=n(t);return a?i.local().format(e):i.format(e)}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275pipe=i["\u0275\u0275definePipe"]({name:"dateFormat",type:t,pure:!0}),t})()}}]);