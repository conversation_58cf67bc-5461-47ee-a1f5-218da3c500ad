(window.webpackJsonp=window.webpackJsonp||[]).push([[656],{pTSO:function(e,t,i){"use strict";i.r(t),i.d(t,"TallyGlReportModule",(function(){return q}));var n=i("ofXK"),o=i("ClZT"),a=i("WYlB"),r=i("XPKZ"),l=i("NFeN"),s=i("bTqV"),d=i("STbY"),c=i("8hBH"),m=i("kmnG"),h=i("qFsG"),p=i("3Pt+"),u=i("TU8p"),w=i("Qu3c"),g=i("tyNb"),f=i("mrSG"),v=i("fXoL"),b=i("ySCK"),F=i("qD4H"),S=i("xG9w"),D=i("PSD3"),y=i.n(D),E=i("wd/R"),x=i("1yaQ"),C=i("FKr1"),R=i("JIr8"),V=i("EY2u"),k=i("tk/3"),Y=i("BVzC");let _=(()=>{class e{constructor(e,t){this.http=e,this.errorService=t,this.application_id=256,this.application_object=6}getTallyGlReport(e,t){return new Promise((i,n)=>{this.http.post("/api/project/getTallyGlReportData",{startDate:e,endDate:t,application_id:this.application_id,application_object:this.application_object}).subscribe(e=>i(e),e=>n(e))})}saveReportState(e,t,i,n){return this.http.post("api/userExperience/saveReportState",{state:e,name:t,field_conf:i,application_id:n}).pipe(Object(R.a)(e=>(console.log(e),this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while retrieving Column info list",e&&e.params?e.params:e&&e.error?e.error.params:{}),V.a)))}updateReportState(e,t,i,n){return this.http.post("api/userExperience/updateReportState",{application_id:n,state:e,customization_id:t,field_conf:i}).pipe(Object(R.a)(e=>(console.log(e),this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while retrieving Column info list",e&&e.params?e.params:e&&e.error?e.error.params:{}),V.a)))}getReportUserViews(e){return this.http.post("api/userExperience/getReportUserViews",{application_id:e}).pipe(Object(R.a)(e=>(console.log(e),this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while retrieving Column info list",e&&e.params?e.params:e&&e.error?e.error.params:{}),V.a)))}deleteVariant(e,t){return this.http.post("api/userExperience/deleteVariant",{application_id:e,customization_id:t}).pipe(Object(R.a)(e=>(console.log(e),this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while retrieving Column info list",e&&e.params?e.params:e&&e.error?e.error.params:{}),V.a)))}getPostingPeriodDetails(){try{return new Promise((e,t)=>{this.http.post("api/project/getPostingPeriodDetails",{application_id:this.application_id,application_object:this.application_object}).subscribe(t=>e(t),e=>t(e))})}catch(e){return Promise.reject(e)}}reportAuthorizationValidation(){return new Promise((e,t)=>{this.http.post("/api/invoice/checkRoleAccessForUser",{application_id:this.application_id,application_object:this.application_object}).subscribe(t=>e(t),e=>t(e))})}}return e.\u0275fac=function(t){return new(t||e)(v["\u0275\u0275inject"](k.c),v["\u0275\u0275inject"](Y.a))},e.\u0275prov=v["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var I=i("dNgK"),M=i("0IaG"),G=i("iadO"),T=i("6t9p");const z=["cardScroll"];function B(e,t){1&e&&(v["\u0275\u0275elementStart"](0,"mat-icon"),v["\u0275\u0275text"](1," done_all"),v["\u0275\u0275elementEnd"]())}function N(e,t){1&e&&(v["\u0275\u0275elementStart"](0,"span",50),v["\u0275\u0275elementStart"](1,"span",51),v["\u0275\u0275text"](2,"Loading..."),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"]())}function j(e,t){if(1&e){const e=v["\u0275\u0275getCurrentView"]();v["\u0275\u0275elementStart"](0,"button",52),v["\u0275\u0275listener"]("click",(function(){return v["\u0275\u0275restoreView"](e),v["\u0275\u0275nextContext"]().scrollLeft()})),v["\u0275\u0275elementStart"](1,"mat-icon",53),v["\u0275\u0275text"](2,"chevron_left"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"]()}}function P(e,t){if(1&e){const e=v["\u0275\u0275getCurrentView"]();v["\u0275\u0275elementStart"](0,"mat-icon",56),v["\u0275\u0275listener"]("click",(function(){v["\u0275\u0275restoreView"](e);const t=v["\u0275\u0275nextContext"]().index;return v["\u0275\u0275nextContext"]().deleteVariant(t)})),v["\u0275\u0275text"](1," delete"),v["\u0275\u0275elementEnd"]()}}function A(e,t){if(1&e){const e=v["\u0275\u0275getCurrentView"]();v["\u0275\u0275elementStart"](0,"button",54),v["\u0275\u0275listener"]("click",(function(){v["\u0275\u0275restoreView"](e);const i=t.index;return v["\u0275\u0275nextContext"]().changeView(i)}))("mouseenter",(function(){return t.$implicit.visibleDeleteView=!0}))("mouseleave",(function(){return t.$implicit.visibleDeleteView=!1})),v["\u0275\u0275text"](1),v["\u0275\u0275template"](2,P,2,0,"mat-icon",55),v["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,i=t.index,n=v["\u0275\u0275nextContext"]();v["\u0275\u0275property"]("ngClass",i==n.currentView?"btn-active my-2 version-button":"btn-not-active my-2  version-button"),v["\u0275\u0275advance"](1),v["\u0275\u0275textInterpolate1"](" ",null==e?null:e.config_name," "),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("ngIf",e.visibleDeleteView)}}function L(e,t){if(1&e){const e=v["\u0275\u0275getCurrentView"]();v["\u0275\u0275elementStart"](0,"button",57),v["\u0275\u0275listener"]("click",(function(){return v["\u0275\u0275restoreView"](e),v["\u0275\u0275nextContext"]().scrollRight()})),v["\u0275\u0275elementStart"](1,"mat-icon",53),v["\u0275\u0275text"](2,"chevron_right"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"]()}}function O(e,t){1&e&&(v["\u0275\u0275elementStart"](0,"mat-error"),v["\u0275\u0275text"](1," Name is required "),v["\u0275\u0275elementEnd"]())}const H=[{path:"",component:(()=>{class e{constructor(e,t,i,n,o){this._tallyGlReportService=e,this.snackBar=t,this.dialog=i,this.fb=n,this._router=o,this.storageKey="dx-widget-gallery-pivotgrid-storing-tally-gl-report",this.applicationId=69,this.showDataFields=!0,this.showRowFields=!0,this.showColumnFields=!0,this.showFilterFields=!0,this.displayView=!0,this.isAuthorized=!0,this.roleAccessFilters="",this.versionName=new p.j("",[p.H.required]),this.onFetchRecord=!1,this.fetchTallyGLReport=()=>Object(f.c)(this,void 0,void 0,(function*(){if(!this.startDateFromStorage||!this.endDateFromStorage){let e=yield this._tallyGlReportService.getPostingPeriodDetails();if("S"==e.messType){this.postingPeriod=e.data.posting_period[0].date;let t=E(this.postingPeriod).startOf("month").format("YYYY-MM-DD");this.dateRange.controls.startDate.patchValue(E(t).format("YYYY-MM-DD")),this.dateRange.controls.endDate.patchValue(E(t).add(1,"months").endOf("month").format("YYYY-MM-DD")),this.minEndDate=new Date(this.dateRange.controls.startDate.value),this.maxEndDate=new Date(E(this.dateRange.controls.startDate.value).add(12,"months").format("YYYY-MM-DD"))}}this.filters="IncomeStatementReport",this.dataSource=new F.a({fields:[{caption:"Legal Entity",width:120,dataField:"entity_name",area:"row",expanded:!0},{caption:"GL Level 1",width:120,dataField:"GL Level 1",area:"row"},{caption:"GL Level 2",width:120,dataField:"GL Level 2",area:"row"},{caption:"GL Level 3",width:120,dataField:"GL Level 3",area:"row"},{caption:"GL code",width:120,dataField:"gl_code",area:"filter"},{caption:"GL name",width:120,dataField:"gl_description",area:"row"},{caption:"Cost Center",width:120,dataField:"cost_centre",area:"filter"},{caption:"Vertical",width:120,dataField:"pl_name",area:"filter"},{caption:"FY",width:120,dataField:"fy",area:"filter"},{caption:"Year",dataField:"period_year",area:"column",expanded:!0},{caption:"Month",dataField:"period_month",dataType:"date",area:"column",groupInterval:"month",expanded:!0},{caption:"Type",width:120,dataField:"type",area:"column"},{caption:"Value",width:120,dataField:"valueFormatted",dataType:"number",summaryType:"sum",format:{formatter:e=>e.toFixed(2),parser:e=>Number(e)},area:"filter"},{caption:"Full Value",width:120,dataField:"value",dataType:"number",summaryType:"sum",format:{type:"fixedPoint",precision:2},area:"data"},{caption:"Currency",width:120,dataField:"entity_currency_code",dataType:"string",area:"column",expanded:!0}],store:new b.a({load:(function(){return this.onFetchRecord=!0,this._tallyGlReportService.getTallyGlReport(this.dateRange.controls.startDate.value,this.dateRange.controls.endDate.value).then(e=>(console.log("tally Gl report data"),console.log(e),this.onFetchRecord=!1,e))}).bind(this)})})})),this.stateUpdate=()=>Object(f.c)(this,void 0,void 0,(function*(){let e=yield localStorage.getItem(this.storageKey);"string"==typeof e&&(e=JSON.parse(e));let t=[{showDataFields:this.showDataFields,showRowFields:this.showRowFields,showColumnFields:this.showColumnFields,showFilterFields:this.showFilterFields}];console.log(e),e&&this.views&&this.views.allViews.length>0?this._tallyGlReportService.updateReportState(e,this.views.allViews[this.currentView].customization_id,t,this.applicationId).subscribe(e=>{console.log(e),this.enableDisplayView(),this.snackBar.open("Report Version - "+this.views.allViews[this.currentView].config_name+" was updated Successfully!","Dismiss"),this.getConfigData()},e=>{this.snackBar.open("Unable to Update the Current Report Version! Try Again","Dismiss"),console.log(e)}):this.snackBar.open("No Report Versions Found.Kindly Use SaveAs!","Dismiss")})),this.startDateFromStorage=localStorage.getItem("tallyGlReportStartDate"),this.endDateFromStorage=localStorage.getItem("tallyGlReportEndDate");let a=this.startDateFromStorage?E(this.startDateFromStorage).format("YYYY-MM-DD"):E().subtract(12,"months").format("YYYY-MM-DD"),r=this.endDateFromStorage?E(this.endDateFromStorage).format("YYYY-MM-DD"):E().endOf("month").format("YYYY-MM-DD");this.dateRange=this.fb.group({startDate:[a,p.H.required],endDate:[r,p.H.required]}),this.startDate=this.dateRange.controls.startDate.value,this.endDate=this.dateRange.controls.endDate.value,this.minEndDate=new Date(this.dateRange.controls.startDate.value),this.maxEndDate=new Date(E(this.dateRange.controls.startDate.value).add(12,"months").format("YYYY-MM-DD"))}ngOnInit(){return Object(f.c)(this,void 0,void 0,(function*(){if(yield this.reportAuthorizationValidation(),1!=this.isAuthorized)return this._router.navigateByUrl("/main/reports"),this.snackBar.open(this.errorMessage,"Dismiss",{duration:2e3});this.fetchTallyGLReport(),this.getConfigData()}))}onStartDateChange(){let e=new Date(this.dateRange.controls.startDate.value),t=new Date(this.dateRange.controls.endDate.value),i=t.getMonth()-e.getMonth()+12*(t.getFullYear()-e.getFullYear());i>12||i<0?(this.dateRange.controls.endDate.patchValue(E(e).add(12,"months").format("YYYY-MM-DD")),this.minEndDate=e,this.maxEndDate=new Date(this.dateRange.controls.endDate.value)):(this.minEndDate=e,this.maxEndDate=E(e).add(12,"months").format("YYYY-MM-DD"),(t<e||E(t).format("YYYY-MM-DD")>this.maxEndDate)&&this.dateRange.controls.endDate.patchValue(E(e).add(12,"months").format("YYYY-MM-DD")))}onFySelection(){localStorage.setItem("tallyGlReportStartDate",this.dateRange.controls.startDate.value),localStorage.setItem("tallyGlReportEndDate",this.dateRange.controls.endDate.value),this.onFetchRecord=!0,this.refresh()}onInitialized(e){this.pivotGrid1=e.component}refresh(){this.pivotGrid1.getDataSource().reload()}reset(){this.pivotGrid1.getDataSource().state({})}getConfigData(){return Object(f.c)(this,void 0,void 0,(function*(){yield this._tallyGlReportService.getReportUserViews(this.applicationId).subscribe(e=>{this.views=e;for(let t=0;t<this.views.allViews.length;t++)console.log(t),this.views.allViews[t].visibleDeleteView=!1;this.views.activeView.length>0&&this.views&&(this.enableDisplayView(),this.currentView=0,localStorage.setItem(this.storageKey,this.views.activeView[0].saved_config),this.pivotGrid1.getDataSource().state(JSON.parse(this.views.activeView[0].saved_config)),this.setFieldChoosers(this.views.activeView[0].field_config.length>0?JSON.parse(this.views.activeView[0].field_config)[0]:{showDataFields:!0,showRowFields:!0,showColumnFields:!0,showFilterFields:!0})),console.log(this.views)},e=>{this.enableDisplayView(),this.snackBar.open("Error Retrieving Report Views! Try Refreshing","Dismiss"),console.log("Error retrieving views")})}))}saveState(){let e=localStorage.getItem(this.storageKey);"string"==typeof e&&(e=JSON.parse(e));let t=[{showDataFields:this.showDataFields,showRowFields:this.showRowFields,showColumnFields:this.showColumnFields,showFilterFields:this.showFilterFields}];console.log(this.applicationId),this._tallyGlReportService.saveReportState(e,this.versionName.value,t,this.applicationId).subscribe(e=>{console.log(e),this.enableDisplayView(),this.snackBar.open("Report Version - "+this.versionName.value+" was created Successfully!","Dismiss"),this.getConfigData()},e=>{this.snackBar.open("Unable to create the Report Version! Try Again","Dismiss"),console.log(e)})}changeView(e){this.currentView=e,console.log(e),localStorage.setItem(this.storageKey,this.views.allViews[e].saved_config);let t=localStorage.getItem(this.storageKey);console.log("Here"),console.log(t),this.pivotGrid1.getDataSource().state(JSON.parse(this.views.allViews[e].saved_config)),this.setFieldChoosers(JSON.parse(this.views.allViews[e].field_config)[0]),this.enableDisplayView()}deleteVariant(e){let t=this.views.allViews[e].config_name;this.confirmSweetAlert("Do you want to Delete "+t+" Variant?").then(i=>{i.value&&(console.log(i.value),this._tallyGlReportService.deleteVariant(this.applicationId,this.views.allViews[e].customization_id).subscribe(e=>{this.getConfigData(),this.snackBar.open("Variant "+t+" was Deleted Succesfully","Dismiss")},e=>{this.snackBar.open("Failed to delete Variant "+t+".","Dismiss")}))})}showDataFieldsFn(){this.showDataFields=!this.showDataFields,this.snackBar.open(1==this.showDataFields?"Displaying Data Fields!":"Data Fields Hidden!","Dismiss",{duration:1e3})}showRowFieldsFn(){this.showRowFields=!this.showRowFields,this.snackBar.open(1==this.showRowFields?"Displaying Row Fields!":"Row Fields Hidden!","Dismiss",{duration:1e3})}showColumnFieldsFn(){this.showColumnFields=!this.showColumnFields,this.snackBar.open(1==this.showColumnFields?"Displaying Column Fields!":"Column Fields Hidden!","Dismiss",{duration:1e3})}showFilterFieldsFn(){this.showFilterFields=!this.showFilterFields,this.snackBar.open(1==this.showFilterFields?"Displaying Filter Fields!":"Filter Fields Hidden!","Dismiss",{duration:1e3})}toggleEditView(){this.displayView=!this.displayView}enableDisplayView(){this.displayView=!0}scrollRight(){this.cardScroll.nativeElement.scrollTo({left:this.cardScroll.nativeElement.scrollLeft+1060,behavior:"smooth"})}scrollLeft(){this.cardScroll.nativeElement.scrollTo({left:this.cardScroll.nativeElement.scrollLeft-1060,behavior:"smooth"})}confirmSweetAlert(e){return y.a.fire({customClass:{title:"title-class",confirmButton:"confirm-button-class",cancelButton:"confirm-button-class"},title:e,type:"warning",showConfirmButton:!0,showCancelButton:!0})}setFieldChoosers(e){console.log(e),this.showDataFields=e.showDataFields,this.showRowFields=e.showRowFields,this.showColumnFields=e.showColumnFields,this.showFilterFields=e.showFilterFields}resetFieldChoosers(){this.showDataFields=!1,this.showRowFields=!1,this.showColumnFields=!1,this.showFilterFields=!1}onCellPrepared(e){if("data"==e.area&&e.cell.value)if(S.contains(e.cell.columnPath,"INR")){let t="\u20b9 "+new Intl.NumberFormat("en-IN",{}).format(e.cell.value);e.cellElement.setAttribute("title",t),e.cellElement.innerHTML=t}else{let t=new Intl.NumberFormat("en-US",{}).format(e.cell.value);S.contains(e.cell.columnPath,"USD")&&(t="$ "+t),e.cellElement.setAttribute("title",t),e.cellElement.innerHTML=t}}getInfo(){return Object(f.c)(this,void 0,void 0,(function*(){const{DisplayCurrencyComponent:e}=yield Promise.all([i.e(20),i.e(980)]).then(i.bind(null,"KJjA"));this.dialog.open(e,{height:"80%",width:"40%"}).afterClosed().subscribe(e=>Object(f.c)(this,void 0,void 0,(function*(){})))}))}reportAuthorizationValidation(){var e;return Object(f.c)(this,void 0,void 0,(function*(){try{this.roleAccessFilters=yield this._tallyGlReportService.reportAuthorizationValidation(),"E"==this.roleAccessFilters.messType&&(this.isAuthorized=!1)}catch(t){this.isAuthorized=!1,this.errorMessage=(null===(e=null==t?void 0:t.error)||void 0===e?void 0:e.errorMessage)||"Error in Retrieving Income statment Report",this.snackBar.open(this.errorMessage,"Dismiss",{duration:2e3})}}))}}return e.\u0275fac=function(t){return new(t||e)(v["\u0275\u0275directiveInject"](_),v["\u0275\u0275directiveInject"](I.a),v["\u0275\u0275directiveInject"](M.b),v["\u0275\u0275directiveInject"](p.i),v["\u0275\u0275directiveInject"](g.g))},e.\u0275cmp=v["\u0275\u0275defineComponent"]({type:e,selectors:[["app-tally-gl-report-landing-page"]],viewQuery:function(e,t){if(1&e&&(v["\u0275\u0275viewQuery"](o.a,!0),v["\u0275\u0275viewQuery"](z,!0,v.ElementRef)),2&e){let e;v["\u0275\u0275queryRefresh"](e=v["\u0275\u0275loadQuery"]())&&(t.pivotGrid=e.first),v["\u0275\u0275queryRefresh"](e=v["\u0275\u0275loadQuery"]())&&(t.cardScroll=e.first)}},features:[v["\u0275\u0275ProvidersFeature"]([{provide:C.c,useClass:x.c,deps:[C.f,x.a]},{provide:C.e,useValue:{parse:{dateInput:"DD-MM-YYYY"},display:{dateInput:"DD-MM-YYYY",monthYearLabel:"MMM YYYY"},useUtc:!0}}])],decls:78,vars:48,consts:[[1,"row","w-100","pb-2","pt-2",3,"formGroup"],[1,"col-2"],["appearance","outline",2,"width","100%"],["matInput","","formControlName","startDate",3,"matDatepicker","dateChange"],["matSuffix","",3,"for"],["sd",""],["matInput","","formControlName","endDate",3,"matDatepicker","min","max"],["ed",""],["mat-icon-button","","type","submit",1,"iconbtnSubmit",3,"disabled","click"],[4,"ngIf"],["class","spinner-border","role","status",4,"ngIf"],[1,"col-3","pl-0","pr-0","d-flex"],[1,"d-flex","overflow-hidden",2,"max-width","100%","min-width","70%"],["cardScroll",""],["mat-icon-button","","class","iconsSize btn-fab-left",3,"click",4,"ngIf"],["mat-raised-button","","style","font-weight: normal; margin-left: 1%;",3,"ngClass","click","mouseenter","mouseleave",4,"ngFor","ngForOf"],["mat-icon-button","","class","iconsSize btn-fab-right pt-1",3,"click",4,"ngIf"],[1,"col-3","pr-0","pl-0","d-flex","align-items-baseline"],["mat-icon-button","","matTooltip","Edit View",1,"iconsSize","ml-auto","mt-1",3,"matMenuTriggerFor"],["matBadge","!","matBadgeSize","small","matBadgeColor","warn",1,"iconsSize",3,"matBadgeHidden"],[1,"card-panel"],["options","matMenu"],[1,"card"],[1,"card-body","pl-3","pr-3","pt-2","pb-2"],[1,"row"],[1,"col-12"],["mat-icon-button","","matTooltip","Hide Data Fields",1,"iconsSize","ml-1",3,"click"],["mat-icon-button","","matTooltip","Hide Row Fields",1,"iconsSize","ml-1",3,"click"],["mat-icon-button","","matTooltip","Hide Column Fields",1,"iconsSize","ml-1",3,"click"],["mat-icon-button","","matTooltip","Hide Filter Fields",1,"iconsSize","ml-1",3,"click"],["mat-icon-button","","matTooltip","Save",1,"iconsSize","ml-1","mt-1",3,"disabled","click"],[1,"iconsSize"],["data-step","9","data-intro","Click to Save as a new version","data-position","right","mat-icon-button","","matTooltip","Save As",1,"iconsSize","ml-1","mt-1",3,"satPopoverAnchor","click"],["horizontalAlign","after","verticalAlign","below"],["saveAs",""],[2,"box-shadow","0 3px 1px -2px rgba(0, 0, 0, 0.2),\n              0 2px 2px 0 rgba(0, 0, 0, 0.14),\n              0 1px 5px 0 rgba(0, 0, 0, 0.12) !important","padding","16px !important","background-color","white !important","max-width","300px !important","line-height","33px !important"],["matInput","","placeholder","Save as - Name","required","",3,"formControl"],["mat-raised-button","","color","warn",3,"disabled","click"],["icon","clear",1,"iconsSize","ml-1"],["icon","refresh",1,"ml-1","iconsSize",3,"onClick"],["icon","columnchooser",1,"iconsSize","ml-1",3,"onClick"],["icon","exportxlsx",1,"iconsSize","ml-1",3,"onClick"],["mat-icon-button",""],[3,"click"],["id","costing",3,"allowSortingBySummary","allowSorting","allowFiltering","allowExpandAll","showBorders","dataSource","wordWrapEnabled","height","showColumnTotals","showColumnGrandTotals","showRowTotals","onCellPrepared","onInitialized"],["fileName","Income Statement",3,"enabled"],[3,"enabled","allowSearch"],[3,"showDataFields","showRowFields","showColumnFields","showFilterFields","allowFieldDragging","visible"],[3,"allowSearch","width","height"],["type","localStorage","storageKey","dx-widget-gallery-pivotgrid-storing-tally-gl-report",3,"enabled"],["role","status",1,"spinner-border"],[1,"sr-only"],["mat-icon-button","",1,"iconsSize","btn-fab-left",3,"click"],[2,"color","#1e2733 !important","font-size","21px !important"],["mat-raised-button","",2,"font-weight","normal","margin-left","1%",3,"ngClass","click","mouseenter","mouseleave"],["class","resource-costing-icon ml-1",3,"click",4,"ngIf"],[1,"resource-costing-icon","ml-1",3,"click"],["mat-icon-button","",1,"iconsSize","btn-fab-right","pt-1",3,"click"]],template:function(e,t){if(1&e){const e=v["\u0275\u0275getCurrentView"]();v["\u0275\u0275elementStart"](0,"div",0),v["\u0275\u0275elementStart"](1,"div",1),v["\u0275\u0275elementStart"](2,"mat-form-field",2),v["\u0275\u0275elementStart"](3,"mat-label"),v["\u0275\u0275text"](4,"Start Period"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](5,"input",3),v["\u0275\u0275listener"]("dateChange",(function(){return t.onStartDateChange()})),v["\u0275\u0275elementEnd"](),v["\u0275\u0275element"](6,"mat-datepicker-toggle",4),v["\u0275\u0275element"](7,"mat-datepicker",null,5),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](9,"div",1),v["\u0275\u0275elementStart"](10,"mat-form-field",2),v["\u0275\u0275elementStart"](11,"mat-label"),v["\u0275\u0275text"](12,"End Period"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275element"](13,"input",6),v["\u0275\u0275element"](14,"mat-datepicker-toggle",4),v["\u0275\u0275element"](15,"mat-datepicker",null,7),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](17,"div",1),v["\u0275\u0275elementStart"](18,"button",8),v["\u0275\u0275listener"]("click",(function(){return t.onFySelection()})),v["\u0275\u0275template"](19,B,2,0,"mat-icon",9),v["\u0275\u0275template"](20,N,3,0,"span",10),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](21,"div",11),v["\u0275\u0275elementStart"](22,"div",12,13),v["\u0275\u0275template"](24,j,3,0,"button",14),v["\u0275\u0275template"](25,A,3,3,"button",15),v["\u0275\u0275elementEnd"](),v["\u0275\u0275template"](26,L,3,0,"button",16),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](27,"div",17),v["\u0275\u0275elementStart"](28,"button",18),v["\u0275\u0275elementStart"](29,"mat-icon",19),v["\u0275\u0275text"](30,"create"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](31,"mat-menu",20,21),v["\u0275\u0275elementStart"](33,"div",22),v["\u0275\u0275elementStart"](34,"div",23),v["\u0275\u0275elementStart"](35,"div",24),v["\u0275\u0275elementStart"](36,"div",25),v["\u0275\u0275elementStart"](37,"button",26),v["\u0275\u0275listener"]("click",(function(){return t.showDataFieldsFn()})),v["\u0275\u0275elementStart"](38,"mat-icon",19),v["\u0275\u0275text"](39,"menu_open"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](40,"button",27),v["\u0275\u0275listener"]("click",(function(){return t.showRowFieldsFn()})),v["\u0275\u0275elementStart"](41,"mat-icon",19),v["\u0275\u0275text"](42,"menu_open"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](43,"button",28),v["\u0275\u0275listener"]("click",(function(){return t.showColumnFieldsFn()})),v["\u0275\u0275elementStart"](44,"mat-icon",19),v["\u0275\u0275text"](45,"view_column"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](46,"button",29),v["\u0275\u0275listener"]("click",(function(){return t.showFilterFieldsFn()})),v["\u0275\u0275elementStart"](47,"mat-icon",19),v["\u0275\u0275text"](48,"filter_list"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](49,"button",30),v["\u0275\u0275listener"]("click",(function(){return t.stateUpdate()})),v["\u0275\u0275elementStart"](50,"mat-icon",31),v["\u0275\u0275text"](51,"save"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](52,"button",32),v["\u0275\u0275listener"]("click",(function(){return v["\u0275\u0275restoreView"](e),v["\u0275\u0275reference"](56).toggle()})),v["\u0275\u0275elementStart"](53,"mat-icon",31),v["\u0275\u0275text"](54,"move_to_inbox"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](55,"sat-popover",33,34),v["\u0275\u0275elementStart"](57,"div",35),v["\u0275\u0275elementStart"](58,"div",24),v["\u0275\u0275elementStart"](59,"mat-form-field"),v["\u0275\u0275element"](60,"input",36),v["\u0275\u0275template"](61,O,2,0,"mat-error",9),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](62,"div",24),v["\u0275\u0275elementStart"](63,"button",37),v["\u0275\u0275listener"]("click",(function(){v["\u0275\u0275restoreView"](e);const i=v["\u0275\u0275reference"](56);return t.saveState(),i.toggle()})),v["\u0275\u0275text"](64," Save "),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275element"](65,"dx-button",38),v["\u0275\u0275elementStart"](66,"dx-button",39),v["\u0275\u0275listener"]("onClick",(function(){return t.refresh()})),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](67,"dx-button",40),v["\u0275\u0275listener"]("onClick",(function(){return t.pivotGrid1.getFieldChooserPopup().show()})),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](68,"dx-button",41),v["\u0275\u0275listener"]("onClick",(function(){return t.pivotGrid1.exportToExcel()})),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](69,"button",42),v["\u0275\u0275elementStart"](70,"mat-icon",43),v["\u0275\u0275listener"]("click",(function(){return t.getInfo()})),v["\u0275\u0275text"](71,"currency_exchange"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](72,"dx-pivot-grid",44),v["\u0275\u0275listener"]("onCellPrepared",(function(e){return t.onCellPrepared(e)}))("onInitialized",(function(e){return t.onInitialized(e)})),v["\u0275\u0275element"](73,"dxo-export",45),v["\u0275\u0275element"](74,"dxo-field-chooser",46),v["\u0275\u0275element"](75,"dxo-field-panel",47),v["\u0275\u0275element"](76,"dxo-header-filter",48),v["\u0275\u0275element"](77,"dxo-state-storing",49),v["\u0275\u0275elementEnd"]()}if(2&e){const e=v["\u0275\u0275reference"](8),i=v["\u0275\u0275reference"](16),n=v["\u0275\u0275reference"](32),o=v["\u0275\u0275reference"](56);v["\u0275\u0275property"]("formGroup",t.dateRange),v["\u0275\u0275advance"](5),v["\u0275\u0275property"]("matDatepicker",e),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("for",e),v["\u0275\u0275advance"](7),v["\u0275\u0275property"]("matDatepicker",i)("min",t.minEndDate)("max",t.maxEndDate),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("for",i),v["\u0275\u0275advance"](4),v["\u0275\u0275property"]("disabled",1==t.onFetchRecord),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("ngIf",0==t.onFetchRecord),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("ngIf",1==t.onFetchRecord),v["\u0275\u0275advance"](4),v["\u0275\u0275property"]("ngIf",(null==t.views||null==t.views.activeView?null:t.views.activeView.length)>0),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("ngForOf",null==t.views?null:t.views.allViews),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("ngIf",(null==t.views||null==t.views.activeView?null:t.views.activeView.length)>0),v["\u0275\u0275advance"](2),v["\u0275\u0275property"]("matMenuTriggerFor",n),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("matBadgeHidden",t.displayView),v["\u0275\u0275advance"](9),v["\u0275\u0275property"]("matBadgeHidden",t.showDataFields),v["\u0275\u0275advance"](3),v["\u0275\u0275property"]("matBadgeHidden",t.showRowFields),v["\u0275\u0275advance"](3),v["\u0275\u0275property"]("matBadgeHidden",t.showColumnFields),v["\u0275\u0275advance"](3),v["\u0275\u0275property"]("matBadgeHidden",t.showFilterFields),v["\u0275\u0275advance"](2),v["\u0275\u0275property"]("disabled",0==(null==t.views||null==t.views.allViews?null:t.views.allViews.length)),v["\u0275\u0275advance"](3),v["\u0275\u0275property"]("satPopoverAnchor",o),v["\u0275\u0275advance"](8),v["\u0275\u0275property"]("formControl",t.versionName),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("ngIf",t.versionName.hasError("required")),v["\u0275\u0275advance"](2),v["\u0275\u0275property"]("disabled",!t.versionName.value),v["\u0275\u0275advance"](9),v["\u0275\u0275property"]("allowSortingBySummary",!0)("allowSorting",!0)("allowFiltering",!0)("allowExpandAll",!0)("showBorders",!0)("dataSource",t.dataSource)("wordWrapEnabled",!1)("height",580)("showColumnTotals",!0)("showColumnGrandTotals",!1)("showRowTotals",!0),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("enabled",!1),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("enabled",!1)("allowSearch",!0),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("showDataFields",t.showDataFields)("showRowFields",t.showRowFields)("showColumnFields",t.showColumnFields)("showFilterFields",t.showFilterFields)("allowFieldDragging",!0)("visible",!0),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("allowSearch",!0)("width",300)("height",400),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("enabled",!0)}},directives:[p.w,p.n,m.c,m.g,h.b,p.e,G.g,p.v,p.l,G.i,m.i,G.f,s.a,n.NgIf,n.NgForOf,w.a,d.f,l.a,u.a,d.g,c.b,c.a,p.F,p.k,a.a,o.a,T.Sb,T.Ub,T.Wb,T.Cc,T.le,n.NgClass,m.b],styles:[".iconbtn[_ngcontent-%COMP%], .iconbtnSubmit[_ngcontent-%COMP%]{background-color:#c92020;color:#fff;padding:0;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.iconbtnSubmit[_ngcontent-%COMP%]{cursor:pointer;border-radius:20px;border:none!important}.submit-btn[_ngcontent-%COMP%]{background:linear-gradient(44deg,#e86565 10%,#cf0001 105.29%);border-radius:8px;color:#fff}"]}),e})()}];let U=(()=>{class e{}return e.\u0275mod=v["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=v["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[g.k.forChild(H)],g.k]}),e})();var K=i("d3UM");let q=(()=>{class e{}return e.\u0275mod=v["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=v["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[n.CommonModule,U,o.b,u.b,a.b,w.b,c.c,m.e,h.c,p.p,p.E,r.b,l.b,d.e,s.b,G.h,C.n,K.d]]}),e})()}}]);