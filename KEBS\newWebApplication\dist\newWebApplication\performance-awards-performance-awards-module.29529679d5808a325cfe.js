(window.webpackJsonp=window.webpackJsonp||[]).push([[846],{"5BXY":function(t,e,a){"use strict";a.r(e),a.d(e,"PerformanceAwardsModule",(function(){return M}));var i=a("ofXK"),r=a("tyNb"),n=a("fXoL"),o=a("rWiy"),s=a("XXEo"),p=a("wZkO");function l(t,e){if(1&t&&(n["\u0275\u0275elementStart"](0,"a",3,4),n["\u0275\u0275text"](2),n["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit,a=n["\u0275\u0275reference"](1);n["\u0275\u0275property"]("active",a.isActive)("routerLink",t.path),n["\u0275\u0275advance"](2),n["\u0275\u0275textInterpolate1"](" ",t.label," ")}}function d(t,e){if(1&t){const t=n["\u0275\u0275getCurrentView"]();n["\u0275\u0275elementStart"](0,"a",5),n["\u0275\u0275listener"]("click",(function(){return n["\u0275\u0275restoreView"](t),n["\u0275\u0275nextContext"]().navigate()})),n["\u0275\u0275text"](1," Configuration "),n["\u0275\u0275elementEnd"]()}}let c=(()=>{class t{constructor(t,e,a,i){this.route=t,this._Router=e,this._url=a,this._LoginService=i,this.navLinks=[{path:"dashboard/1",label:"Dashboard"},{path:"nominations/1",label:"My Rewards"},{path:"my-nominations/1",label:"My Nominations"},{path:"approvals/1",label:"Approvals"},{path:"my-team/1",label:"My Team"}]}ngOnInit(){this.profile=this._LoginService.getProfile().profile,this.tabLinks=[{label:"My Awards",path:"awards/1"},{label:"Nominate",path:"nominate/1"},{label:"Approve",path:"approve/1"}],this._url.haveAccess(this.profile.oid).subscribe(t=>{this.isAdmin=t.data})}navigate(){this._Router.navigateByUrl("/main/pms/awards/config")}}return t.\u0275fac=function(e){return new(e||t)(n["\u0275\u0275directiveInject"](r.a),n["\u0275\u0275directiveInject"](r.g),n["\u0275\u0275directiveInject"](o.a),n["\u0275\u0275directiveInject"](s.a))},t.\u0275cmp=n["\u0275\u0275defineComponent"]({type:t,selectors:[["app-performance-awards"]],decls:4,vars:2,consts:[["mat-tab-nav-bar",""],["mat-tab-link","","routerLinkActive","",3,"active","routerLink",4,"ngFor","ngForOf"],["mat-tab-link","",3,"click",4,"ngIf"],["mat-tab-link","","routerLinkActive","",3,"active","routerLink"],["rla","routerLinkActive"],["mat-tab-link","",3,"click"]],template:function(t,e){1&t&&(n["\u0275\u0275elementStart"](0,"nav",0),n["\u0275\u0275template"](1,l,3,3,"a",1),n["\u0275\u0275template"](2,d,2,0,"a",2),n["\u0275\u0275elementEnd"](),n["\u0275\u0275element"](3,"router-outlet")),2&t&&(n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngForOf",e.navLinks),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf",e.isAdmin))},directives:[p.f,i.NgForOf,i.NgIf,r.l,r.j,p.e,r.i],styles:[""]}),t})();var h=a("mrSG"),u=a("LcQX");const m=[{path:"",component:c,children:[{path:"",redirectTo:"dashboard/1"},{path:"config",canActivate:[(()=>{class t{constructor(t,e,a,i){this._auth=t,this._url=e,this._util=a,this.router=i,this.checkAccess=()=>new Promise((t,e)=>{this._url.haveAccess(this.profile.oid).subscribe(e=>{console.log(e),t(e.data)},t=>{e(t)})}),this.profile=this._auth.getProfile().profile}canActivate(){return Object(h.c)(this,void 0,void 0,(function*(){let t=yield this.checkAccess();return 0==t&&(this._util.showMessage("You Dont Have Access! Contact KEBS Support.","dismiss",2e3),this.router.navigateByUrl("/main/pms/awards")),t}))}}return t.\u0275fac=function(e){return new(e||t)(n["\u0275\u0275inject"](s.a),n["\u0275\u0275inject"](o.a),n["\u0275\u0275inject"](u.a),n["\u0275\u0275inject"](r.g))},t.\u0275prov=n["\u0275\u0275defineInjectable"]({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()],loadChildren:()=>Promise.all([a.e(2),a.e(3),a.e(5),a.e(6),a.e(8),a.e(19),a.e(21),a.e(83),a.e(406)]).then(a.bind(null,"22yK")).then(t=>t.ConfigurationModule)},{path:"nominations/:flag",loadChildren:()=>Promise.all([a.e(1),a.e(153)]).then(a.bind(null,"ufuu")).then(t=>t.NominationModule),data:{breadcrumb:"Nominations"}},{path:"approvals/:flag",loadChildren:()=>a.e(385).then(a.bind(null,"0U6B")).then(t=>t.AwardApprovalsModule),data:{breadcrumb:"Approvals"}},{path:"my-nominations/:flag",loadChildren:()=>Promise.all([a.e(1),a.e(153),a.e(529)]).then(a.bind(null,"W7aW")).then(t=>t.MyNominationsModule),data:{breadcrumb:"My Nominations"}},{path:"my-team/:flag",loadChildren:()=>Promise.all([a.e(1),a.e(2),a.e(3),a.e(4),a.e(5),a.e(6),a.e(7),a.e(9),a.e(10),a.e(11),a.e(12),a.e(13),a.e(14),a.e(15),a.e(16),a.e(17),a.e(18),a.e(21),a.e(0),a.e(530)]).then(a.bind(null,"hQLs")).then(t=>t.MyTeamModule),data:{breadcrumb:"My Team"}},{path:"dashboard/:flag",loadChildren:()=>a.e(420).then(a.bind(null,"gz1C")).then(t=>t.DashboardModule),data:{breadcrumb:"Rewards Dashboard"}}]}];let f=(()=>{class t{}return t.\u0275mod=n["\u0275\u0275defineNgModule"]({type:t}),t.\u0275inj=n["\u0275\u0275defineInjector"]({factory:function(e){return new(e||t)},imports:[[r.k.forChild(m)],r.k]}),t})();var g=a("Xi0T"),v=a("lVl8"),w=a("NFeN"),b=a("bTqV"),A=a("Wp6s"),y=a("Qu3c"),k=a("7EHt"),_=a("3Pt+"),C=a("3beV");let M=(()=>{class t{}return t.\u0275mod=n["\u0275\u0275defineNgModule"]({type:t}),t.\u0275inj=n["\u0275\u0275defineInjector"]({factory:function(e){return new(e||t)},imports:[[i.CommonModule,f,p.g,A.d,g.a,w.b,v.b,b.b,y.b,k.b,C.a,_.p,_.E]]}),t})()},rWiy:function(t,e,a){"use strict";a.d(e,"a",(function(){return s}));var i=a("fXoL"),r=a("tk/3"),n=a("XXEo"),o=a("LcQX");let s=(()=>{class t{constructor(t,e,a){this.http=t,this._login=e,this._util=a,this.fetchResult=(t,e)=>this.http.post(t,e),this.getFileDataFromS3=t=>this.http.post("/api/appraisal/configuration/getAppraisalAttachment",{keyName:t})}getNominatedShowStatus(t){return this.http.post("/api/awards/configurations/getNominatedVisibleStatus",t)}createAward(t){return this.http.post("/api/awards/award/createAward",t)}getAwardsToNominate(t){return this.http.post("/api/awards/nominator/getAwardsToNominate",t)}getTargetAudienceForAward(t){return this.http.post("/api/awards/target_audience/getTargetAudienceForAward",t)}createNominationsMultiple(t,e){return this.http.post("/api/awards/nominations/createNominationsMultiple",{multiple_documents:t,is_without_nth_layer:e})}getCurrentUserOID(t){return console.log(t),new Promise(e=>{e("1"==t?this._login.getProfile().profile.oid:t)})}getPendingApprovals(t){return this.http.post("/api/awards/approver/getPendingApprovals",{employee_oid:t})}checkapprovalquota(t){return this.http.post("/api/awards/approver/checkapprovalquota",{quotaResponse:t})}approveAward(t){return this.http.post("/api/awards/approver/approveAward",{quotaResponse:t})}approveIndividualAward(t){return this.http.post("/api/awards/approver/approveIndividualAward",{quotaResponse:t})}haveAccess(t){return this.http.post("/api/appraisal/configuration/isAppraisalAdmin",{configuration_name:"appraisal_roles",role_name:"Admin",employee_oid:t})}getAwardsCard(t){return this.http.post("/api/awards/award/getAllAwardCountForReport",{filterConfig:t})}getAwardsList(t,e){return this.http.post("/api/awards/nominations/getMyTeamAwards",{filterConfig:t,employee_oid_arr:e})}showMessage(t){this._util.showToastMessage(t)}editPointsOrCash(t){return this.http.post("/api/awards/nominations/updPointAndCashAwardedById",t)}getCAADetails(t){return this.http.post("/api/awards/nominations/getCAADashboardDetailByEmpOidAndYear",t)}deleteNomination(t){return this.http.post("/api/awards/nominations/deleteNomination",t)}checkIfOrgHead(t){return this.http.post("/api/awards/award/checkIfOrgHead",{oid:t})}getQuotaAvailable(t){return this.http.post("/api/awards/award/getQuotaAvailable",{oid:t})}getApprovalHistory(t){return this.http.post("/api/awards/approver/getApprovalHistory",{employee_oid:t})}checkQuotaCount(t){return new Promise((e,a)=>{this.http.post("/api/awards/approver/checkQuotaTesting",{quotaResponse:t}).subscribe(t=>{e(t)},t=>{a(t)})})}}return t.\u0275fac=function(e){return new(e||t)(i["\u0275\u0275inject"](r.c),i["\u0275\u0275inject"](n.a),i["\u0275\u0275inject"](o.a))},t.\u0275prov=i["\u0275\u0275defineInjectable"]({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()}}]);