(window.webpackJsonp=window.webpackJsonp||[]).push([[782],{"3N6u":function(e,t,n){"use strict";n.r(t),n.d(t,"LcdpDetailModule",(function(){return de}));var i=n("ofXK"),o=n("bTqV"),a=n("NFeN"),l=n("Qu3c"),r=n("jaxi"),s=n("Xa2L"),c=n("0IaG"),d=n("qFsG"),p=n("kmnG"),u=n("3Pt+"),m=n("tyNb"),h=n("mrSG"),f=n("1G5W"),g=n("XNiG"),v=n("xG9w"),b=n("+rOU"),_=n("fXoL"),C=n("JLuW"),S=n("z52X"),x=n("LcQX"),y=n("rDax"),w=n("XXEo"),I=n("lVl8");let E=(()=>{class e{transform(e){return"Awaiting Approval"==e||"Submitted"==e?"#FF7200":"Approved"==e?"#009432":"Rejected"==e||"Cancelled"==e?"#cf0001":"#FF7200"}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=_["\u0275\u0275definePipe"]({name:"wfStatus",type:e,pure:!0}),e})();const M=["hoursTemplateRef"];function O(e,t){1&e&&(_["\u0275\u0275elementStart"](0,"div",3),_["\u0275\u0275element"](1,"div",4),_["\u0275\u0275elementStart"](2,"div",5),_["\u0275\u0275element"](3,"mat-spinner",6),_["\u0275\u0275elementEnd"](),_["\u0275\u0275element"](4,"div",4),_["\u0275\u0275elementEnd"]())}const T=function(e){return{"btn-toggle-selected":e}};function L(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"mat-button-toggle",24),_["\u0275\u0275text"](1),_["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=_["\u0275\u0275nextContext"](2);_["\u0275\u0275property"]("disabled",n.isComponentLoading)("value",e.tab_id)("ngClass",_["\u0275\u0275pureFunction1"](4,T,n.selectedToggle==e.tab_id)),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",e.tab_label,"")}}function D(e,t){if(1&e){const e=_["\u0275\u0275getCurrentView"]();_["\u0275\u0275elementStart"](0,"button",25),_["\u0275\u0275listener"]("click",(function(){return _["\u0275\u0275restoreView"](e),_["\u0275\u0275nextContext"](2).openComments()})),_["\u0275\u0275elementStart"](1,"mat-icon",16),_["\u0275\u0275text"](2,"forum"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()}}function k(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"mat-button-toggle",37),_["\u0275\u0275text"](1),_["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=_["\u0275\u0275nextContext"](3);_["\u0275\u0275property"]("value",e.tab_id)("ngClass",_["\u0275\u0275pureFunction1"](3,T,n.selectedMetaToggle==e.tab_id)),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",e.tab_label,"")}}function P(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275text"](1),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2).$implicit;_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",(null==e?null:e.sla_value_in_days)||0," days ")}}function F(e,t){if(1&e&&_["\u0275\u0275text"](0),2&e){const e=_["\u0275\u0275nextContext"](2).$implicit;_["\u0275\u0275textInterpolate2"](" ",null==e||null==e.sla_hours?null:e.sla_hours.hours," Hr ",null==e||null==e.sla_hours?null:e.sla_hours.minutes," min ")}}function j(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",49),_["\u0275\u0275elementStart"](1,"div",29),_["\u0275\u0275text"](2,"SLA Value"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](3,"div",50),_["\u0275\u0275template"](4,P,2,1,"ng-container",51),_["\u0275\u0275template"](5,F,1,2,"ng-template",null,52,_["\u0275\u0275templateRefExtractor"]),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275reference"](6),t=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275advance"](4),_["\u0275\u0275property"]("ngIf",(null==t?null:t.sla_default_view)&&"days"==(null==t?null:t.sla_default_view))("ngIfElse",e)}}function A(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",50),_["\u0275\u0275text"](1),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",(null==e?null:e.sla_consumed_in_days)||0," days ")}}const V=function(e){return{cursor:e}},R=function(e){return{"sla-breached-color":e}};function H(e,t){if(1&e){const e=_["\u0275\u0275getCurrentView"]();_["\u0275\u0275elementStart"](0,"div",53,54),_["\u0275\u0275listener"]("click",(function(){_["\u0275\u0275restoreView"](e);const t=_["\u0275\u0275reference"](2),n=_["\u0275\u0275nextContext"]().$implicit;return _["\u0275\u0275nextContext"](4).openHoursOverlay(t,n)})),_["\u0275\u0275text"](3),_["\u0275\u0275elementEnd"]()}if(2&e){const e=_["\u0275\u0275nextContext"]().$implicit,t=_["\u0275\u0275nextContext"](4);_["\u0275\u0275property"]("disabled",!t.isHoursEditAllowed)("ngStyle",_["\u0275\u0275pureFunction1"](5,V,t.isHoursEditAllowed?"pointer":"text"))("ngClass",_["\u0275\u0275pureFunction1"](7,R,null==e?null:e.is_sla_breached)),_["\u0275\u0275advance"](3),_["\u0275\u0275textInterpolate2"](" ",null==e||null==e.total_consumed_hours?null:e.total_consumed_hours.hours," Hr ",null==e||null==e.total_consumed_hours?null:e.total_consumed_hours.minutes," min ")}}function N(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",40),_["\u0275\u0275elementStart"](1,"div",55),_["\u0275\u0275text"](2," SLA Due on: "),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](3,"div",56),_["\u0275\u0275text"](4),_["\u0275\u0275pipe"](5,"date"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275advance"](4),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind2"](5,1,null==e?null:e.sla_due_on,"dd-MMM-yy hh:mm a")," ")}}function W(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",40),_["\u0275\u0275elementStart"](1,"div",28),_["\u0275\u0275elementStart"](2,"div",29),_["\u0275\u0275text"](3,"Auto Calc Value"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](4,"div",58),_["\u0275\u0275text"](5),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](6,"div",28),_["\u0275\u0275elementStart"](7,"div",29),_["\u0275\u0275text"](8,"Deviation (in min)"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](9,"div",58),_["\u0275\u0275text"](10),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2).$implicit;_["\u0275\u0275advance"](5),_["\u0275\u0275textInterpolate2"](" ",null==e||null==e.auto_consumed_hours?null:e.auto_consumed_hours.hours," Hr ",null==e||null==e.auto_consumed_hours?null:e.auto_consumed_hours.minutes," min "),_["\u0275\u0275advance"](5),_["\u0275\u0275textInterpolate1"](" ",(null==e?null:e.manual_update_deviation)||0," min ")}}function $(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275template"](1,W,11,3,"div",46),_["\u0275\u0275elementStart"](2,"div",40),_["\u0275\u0275elementStart"](3,"div",28),_["\u0275\u0275elementStart"](4,"div",29),_["\u0275\u0275text"](5,"Updated by"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](6,"div",57),_["\u0275\u0275text"](7),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](8,"div",28),_["\u0275\u0275elementStart"](9,"div",29),_["\u0275\u0275text"](10,"Updated at"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](11,"div",57),_["\u0275\u0275pipe"](12,"date"),_["\u0275\u0275text"](13),_["\u0275\u0275pipe"](14,"date"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",null==e?null:e.auto_consumed_hours),_["\u0275\u0275advance"](5),_["\u0275\u0275property"]("matTooltip",null==e?null:e.updated_by),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null==e?null:e.updated_by," "),_["\u0275\u0275advance"](4),_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBind2"](12,5,null==e?null:e.updated_at,"dd-MMM-yy hh:mm a")),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind2"](14,8,null==e?null:e.updated_at,"dd-MMM-yy hh:mm a")," ")}}function B(e,t){if(1&e){const e=_["\u0275\u0275getCurrentView"]();_["\u0275\u0275elementStart"](0,"div",59),_["\u0275\u0275elementStart"](1,"form",60),_["\u0275\u0275text"](2," Change consumed SLA hours "),_["\u0275\u0275elementStart"](3,"div",21),_["\u0275\u0275elementStart"](4,"div",61),_["\u0275\u0275elementStart"](5,"mat-form-field",62),_["\u0275\u0275elementStart"](6,"mat-label"),_["\u0275\u0275text"](7,"Hours"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275element"](8,"input",63),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](9,"div",61),_["\u0275\u0275elementStart"](10,"mat-form-field",62),_["\u0275\u0275elementStart"](11,"mat-label"),_["\u0275\u0275text"](12,"Minutes"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275element"](13,"input",64),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](14,"button",65),_["\u0275\u0275listener"]("click",(function(){return _["\u0275\u0275restoreView"](e),_["\u0275\u0275nextContext"](5).changeHours()})),_["\u0275\u0275elementStart"](15,"mat-icon",66),_["\u0275\u0275text"](16,"done_all"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](17,"span",67),_["\u0275\u0275text"](18,"Save"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()}if(2&e){const e=_["\u0275\u0275nextContext"](5);_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("formGroup",e.hoursFormGroup)}}const q=function(e){return{"sla-breached-bg":e}};function z(e,t){if(1&e){const e=_["\u0275\u0275getCurrentView"]();_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275elementStart"](1,"div",22),_["\u0275\u0275elementStart"](2,"div",21),_["\u0275\u0275elementStart"](3,"div",39),_["\u0275\u0275text"](4),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](5,"div",40),_["\u0275\u0275template"](6,j,7,2,"div",41),_["\u0275\u0275elementStart"](7,"div",42),_["\u0275\u0275elementStart"](8,"mat-icon",43),_["\u0275\u0275listener"]("click",(function(){_["\u0275\u0275restoreView"](e);const n=t.$implicit;return _["\u0275\u0275nextContext"](4).changeView(n)})),_["\u0275\u0275text"](9,"loop"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](10,"div",22),_["\u0275\u0275elementStart"](11,"div",29),_["\u0275\u0275text"](12,"Consumed Value"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275template"](13,A,2,1,"div",44),_["\u0275\u0275template"](14,H,4,9,"ng-template",null,45,_["\u0275\u0275templateRefExtractor"]),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275template"](16,N,6,4,"div",46),_["\u0275\u0275template"](17,$,15,11,"ng-container",2),_["\u0275\u0275template"](18,B,19,1,"ng-template",47,48,_["\u0275\u0275templateRefExtractor"]),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=t.$implicit,n=_["\u0275\u0275reference"](15),i=_["\u0275\u0275nextContext"](4);_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngClass",_["\u0275\u0275pureFunction1"](9,q,null==e?null:e.is_sla_breached)),_["\u0275\u0275advance"](3),_["\u0275\u0275textInterpolate1"](" ",null==e?null:e.sla_name," "),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",!(null!=e&&e.is_for_aging)),_["\u0275\u0275advance"](4),_["\u0275\u0275property"]("ngClass",e.is_for_aging?"col-10":"col-6"),_["\u0275\u0275advance"](3),_["\u0275\u0275property"]("ngIf",(null==e?null:e.sla_default_view)&&"days"==(null==e?null:e.sla_default_view))("ngIfElse",n),_["\u0275\u0275advance"](3),_["\u0275\u0275property"]("ngIf",!(null!=e&&e.is_sla_breached)&&(null==e?null:e.sla_due_on)),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",null==e?null:e.is_manual_update),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("cdkConnectedOverlayOrigin",i.trigger)}}function X(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275template"](1,z,20,11,"ng-container",38),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](3);_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngForOf",e.slaList)}}function G(e,t){if(1&e){const e=_["\u0275\u0275getCurrentView"]();_["\u0275\u0275elementStart"](0,"div",69),_["\u0275\u0275element"](1,"div",70),_["\u0275\u0275elementStart"](2,"div",71),_["\u0275\u0275elementStart"](3,"button",72),_["\u0275\u0275listener"]("click",(function(){return _["\u0275\u0275restoreView"](e),_["\u0275\u0275nextContext"](4).toggleWorkflowHistory()})),_["\u0275\u0275elementStart"](4,"mat-icon",16),_["\u0275\u0275text"](5,"history"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()}if(2&e){const e=_["\u0275\u0275nextContext"](4);_["\u0275\u0275advance"](3),_["\u0275\u0275property"]("ngClass",e.showWfHistory?"icon-button-active":"icon-button-inactive")}}const K=function(e){return{color:e}};function U(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",84),_["\u0275\u0275text"](1),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](2,"div",85),_["\u0275\u0275text"](3),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](4,"div",85),_["\u0275\u0275text"](5," Status : "),_["\u0275\u0275elementStart"](6,"mat-icon",86),_["\u0275\u0275pipe"](7,"wfStatus"),_["\u0275\u0275text"](8," fiber_manual_record "),_["\u0275\u0275elementEnd"](),_["\u0275\u0275text"](9),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"]("",e.approver_name," "),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" Level : ",e.appr_level,""),_["\u0275\u0275advance"](3),_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](6,K,_["\u0275\u0275pipeBind1"](7,4,e.status))),_["\u0275\u0275advance"](3),_["\u0275\u0275textInterpolate1"](" ",e.status," ")}}const Q=function(e){return{"border-color":e}};function J(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"span",80),_["\u0275\u0275element"](1,"img",81),_["\u0275\u0275pipe"](2,"wfStatus"),_["\u0275\u0275template"](3,U,10,8,"ng-template",82,83,_["\u0275\u0275templateRefExtractor"]),_["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=_["\u0275\u0275reference"](4);_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("src",e.approver_profile_url?e.approver_profile_url:"https://assets.kebs.app/images/User.png",_["\u0275\u0275sanitizeUrl"])("ngStyle",_["\u0275\u0275pureFunction1"](5,Q,_["\u0275\u0275pipeBind1"](2,3,e.status)))("tooltip",n)}}const Y=function(e){return{background:e}};function Z(e,t){if(1&e){const e=_["\u0275\u0275getCurrentView"]();_["\u0275\u0275elementStart"](0,"div",74),_["\u0275\u0275listener"]("click",(function(){_["\u0275\u0275restoreView"](e);const t=_["\u0275\u0275nextContext"]().$implicit;return _["\u0275\u0275nextContext"](4).openWfApprovals(t.wf_plugin_id)})),_["\u0275\u0275elementStart"](1,"div",21),_["\u0275\u0275elementStart"](2,"div",39),_["\u0275\u0275text"](3),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](4,"div",40),_["\u0275\u0275elementStart"](5,"div",75),_["\u0275\u0275text"](6," Status "),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](7,"div",76),_["\u0275\u0275element"](8,"span",77),_["\u0275\u0275pipe"](9,"wfStatus"),_["\u0275\u0275elementStart"](10,"span",78),_["\u0275\u0275text"](11),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](12,"div",40),_["\u0275\u0275elementStart"](13,"div",75),_["\u0275\u0275text"](14," Approvers "),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](15,"div",76),_["\u0275\u0275template"](16,J,5,7,"span",79),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()}if(2&e){const e=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275advance"](3),_["\u0275\u0275textInterpolate1"](" ",null==e?null:e.wf_name," "),_["\u0275\u0275advance"](5),_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](6,Y,_["\u0275\u0275pipeBind1"](9,4,null==e?null:e.wf_status))),_["\u0275\u0275advance"](3),_["\u0275\u0275textInterpolate"](null==e?null:e.wf_status),_["\u0275\u0275advance"](5),_["\u0275\u0275property"]("ngForOf",null==e?null:e.wf_details)}}function ee(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275template"](1,Z,17,8,"div",73),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",null==e?null:e.showWf)}}function te(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275template"](1,G,6,1,"div",68),_["\u0275\u0275template"](2,ee,2,1,"ng-container",38),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](3);_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",e.wfList.length>1),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngForOf",e.wfList)}}function ne(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",87),_["\u0275\u0275elementStart"](1,"div"),_["\u0275\u0275element"](2,"img",88),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](3,"div",89),_["\u0275\u0275text"](4),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](3);_["\u0275\u0275advance"](4),_["\u0275\u0275textInterpolate"](1==e.selectedMetaToggle?"No SLA Found !":"No Workflow Found !")}}function ie(e,t){if(1&e){const e=_["\u0275\u0275getCurrentView"]();_["\u0275\u0275elementStart"](0,"div",26),_["\u0275\u0275elementStart"](1,"div",27),_["\u0275\u0275elementStart"](2,"div",28),_["\u0275\u0275elementStart"](3,"div",29),_["\u0275\u0275text"](4,"Created on"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](5,"div",30),_["\u0275\u0275pipe"](6,"date"),_["\u0275\u0275text"](7),_["\u0275\u0275pipe"](8,"date"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](9,"div",28),_["\u0275\u0275elementStart"](10,"div",29),_["\u0275\u0275text"](11,"Created by"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](12,"div",30),_["\u0275\u0275text"](13),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](14,"div",31),_["\u0275\u0275elementStart"](15,"div",32),_["\u0275\u0275elementStart"](16,"mat-button-toggle-group",33),_["\u0275\u0275listener"]("change",(function(t){return _["\u0275\u0275restoreView"](e),_["\u0275\u0275nextContext"](2).selectMetaToggle(t)})),_["\u0275\u0275template"](17,k,2,5,"mat-button-toggle",34),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](18,"div",35),_["\u0275\u0275template"](19,X,2,1,"ng-container",2),_["\u0275\u0275template"](20,te,3,2,"ng-container",2),_["\u0275\u0275template"](21,ne,5,1,"div",36),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()}if(2&e){const e=_["\u0275\u0275nextContext"](2);_["\u0275\u0275advance"](5),_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBind2"](6,9,null==e.recordItem?null:e.recordItem.created_at,"dd-MMM-yy hh:mm a")),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind2"](8,12,null==e.recordItem?null:e.recordItem.created_at,"dd-MMM-yy")," "),_["\u0275\u0275advance"](5),_["\u0275\u0275property"]("matTooltip",null==e.recordItem?null:e.recordItem.created_by_name),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null==e.recordItem?null:e.recordItem.created_by_name," "),_["\u0275\u0275advance"](3),_["\u0275\u0275property"]("value",e.selectedMetaToggle),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngForOf",e.metaTabs),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",1==e.selectedMetaToggle),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",2==e.selectedMetaToggle),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",1==e.selectedMetaToggle&&0==e.slaList.length||2==e.selectedMetaToggle&&0==e.wfList.length)}}function oe(e,t){if(1&e){const e=_["\u0275\u0275getCurrentView"]();_["\u0275\u0275elementStart"](0,"div",7),_["\u0275\u0275elementStart"](1,"div",8),_["\u0275\u0275elementStart"](2,"div",9),_["\u0275\u0275elementStart"](3,"mat-icon",10),_["\u0275\u0275listener"]("click",(function(){return _["\u0275\u0275restoreView"](e),_["\u0275\u0275nextContext"]().goToLandingPage()})),_["\u0275\u0275text"](4,"arrow_back"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](5,"div",11),_["\u0275\u0275elementStart"](6,"mat-button-toggle-group",12),_["\u0275\u0275listener"]("change",(function(t){return _["\u0275\u0275restoreView"](e),_["\u0275\u0275nextContext"]().selectToggle(t)})),_["\u0275\u0275template"](7,L,2,6,"mat-button-toggle",13),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](8,"div",14),_["\u0275\u0275elementStart"](9,"button",15),_["\u0275\u0275listener"]("click",(function(){return _["\u0275\u0275restoreView"](e),_["\u0275\u0275nextContext"]().openWfApprovals(null)})),_["\u0275\u0275elementStart"](10,"mat-icon",16),_["\u0275\u0275text"](11,"fact_check"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](12,"button",17),_["\u0275\u0275listener"]("click",(function(){return _["\u0275\u0275restoreView"](e),_["\u0275\u0275nextContext"]().openMeetingInviteModal()})),_["\u0275\u0275elementStart"](13,"mat-icon",16),_["\u0275\u0275text"](14,"event"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275template"](15,D,3,0,"button",18),_["\u0275\u0275elementStart"](16,"button",19),_["\u0275\u0275listener"]("click",(function(){return _["\u0275\u0275restoreView"](e),_["\u0275\u0275nextContext"]().openRecordStatusHistory()})),_["\u0275\u0275elementStart"](17,"mat-icon",16),_["\u0275\u0275text"](18,"history"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](19,"button",20),_["\u0275\u0275listener"]("click",(function(){_["\u0275\u0275restoreView"](e);const t=_["\u0275\u0275nextContext"]();return t.metaItemCardVisible=!t.metaItemCardVisible})),_["\u0275\u0275elementStart"](20,"mat-icon",16),_["\u0275\u0275text"](21),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](22,"div",21),_["\u0275\u0275elementStart"](23,"div",22),_["\u0275\u0275element"](24,"router-outlet"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275template"](25,ie,22,15,"div",23),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()}if(2&e){const e=_["\u0275\u0275nextContext"]();_["\u0275\u0275advance"](6),_["\u0275\u0275property"]("disabled",e.isComponentLoading)("value",e.selectedToggle),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngForOf",e.recordTabs),_["\u0275\u0275advance"](8),_["\u0275\u0275property"]("ngIf",e.isCommentsVisible),_["\u0275\u0275advance"](4),_["\u0275\u0275property"]("matTooltip",e.metaItemCardVisible?"Hide card":"Show card"),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate"](e.metaItemCardVisible?"visibility_off":"visibility"),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngClass",e.metaItemCardVisible?"col-9":"col-12"),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",e.metaItemCardVisible)}}function ae(e,t){1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275elementStart"](1,"div",90),_["\u0275\u0275elementStart"](2,"div"),_["\u0275\u0275element"](3,"img",91),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](4,"div",92),_["\u0275\u0275text"](5,"No Content found !"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementContainerEnd"]())}const le=["tabContentContainer"],re=[{path:"",component:(()=>{class e{constructor(e,t,n,i,o,a,l,r,s,c){this.route=e,this.$router=t,this.sharedLazyLoadedComponentsService=n,this._lcdpService=i,this._util=o,this.dialog=a,this.viewContainerRef=l,this.overlay=r,this.fb=s,this._login=c,this._onDestroy=new g.b,this.recordTabs=[],this.metaTabs=[{tab_id:1,tab_label:"SLA"},{tab_id:2,tab_label:"Workflow"}],this.slaList=[],this.wfList=[],this.metaItemCardVisible=!0,this.isDataLoading=!0,this.isComponentLoading=!0,this.isMetaDataLoading=!1,this.isHoursEditAllowed=!1,this.showWfHistory=!1,this.isCommentsVisible=!1,this.quickCTAInput={applicationId:null,objectId:0,objectType:"P",originatorData:null},this.hoursFormGroup=this.fb.group({hours:[null],minutes:[null]}),this.requestList=[],this.applicationName="",this.currentUser=this._login.getProfile().profile,this.initParamsData=e=>{this._lcdpService.lcdpDetails.lcdpApplicationId=e.lcdpApplicationId,this._lcdpService.lcdpDetails.applicationId=e.appId,this._lcdpService.lcdpDetails.recordId=e.recordId,e.label&&(this.applicationName=e.label),this.getAppRecordData(e)}}ngOnInit(){this.parentRouteSubscription=this.route.parent.params.subscribe(e=>{if(e.lcdpApplicationId&&e.recordId)this.initParamsData(e);else{const e=this.mergeRouteParams(this.$router);e.lcdpApplicationId&&e.recordId?this.initParamsData(e):this._util.showToastMessage("Application ID/Record ID not Found, Kindly contact KEBS Team !")}}),this._lcdpService.getTabLoadingObservable.pipe(Object(f.a)(this._onDestroy)).subscribe(e=>{this.isComponentLoading=e||!1}),this.checkIfHoursIsEditable()}mergeRouteParams(e){let t={},n=e.routerState.snapshot.root;do{t=Object.assign(Object.assign({},t),n.params),n=n.firstChild}while(n);return t}getAppRecordData(e){this.isDataLoading=!0,this._lcdpService.getAppRecordData(e.lcdpApplicationId,e.recordId).pipe(Object(f.a)(this._onDestroy)).subscribe(e=>Object(h.c)(this,void 0,void 0,(function*(){if(this.isDataLoading=!1,"S"==e.messType&&e.data){if(e.data.detail_tabs&&e.data.detail_tabs.length>0){this.recordTabs=e.data.detail_tabs;let t=0;this.route.firstChild?this.childRouteSubscription=this.route.firstChild.params.subscribe(e=>{if(e.tabId&&e.tabKey)for(let n=0;n<this.recordTabs.length;n++)this.recordTabs[n].tab_id==parseInt(e.tabId)&&(t=n);this.selectedToggle=this.recordTabs[t].tab_id,this.routeToTabContent(this.recordTabs[t])}):(this.selectedToggle=this.recordTabs[t].tab_id,this.routeToTabContent(this.recordTabs[t])),this.getSLADetails(),this.selectedMetaToggle=this.metaTabs[0].tab_id}else this._util.showToastMessage("Detail Tabs data not Found !, Kindly contact KEBS team");e.data.record_data?(this._lcdpService.lcdpDetails.recordData=e.data.record_data,this.recordItem=e.data.record_data):this._util.showToastMessage("Item record data not Found !, Kindly contact KEBS team")}else this._util.showToastMessage("App builder Form Config not Found !")})),e=>{this.isDataLoading=!1,this._lcdpService.showErrorMessage(e)})}selectToggle(e){this.selectedToggle=e.value;for(let t of this.recordTabs)t.tab_id==this.selectedToggle&&this.routeToTabContent(t)}selectMetaToggle(e){this.selectedMetaToggle=e.value}goToLandingPage(){const e=this.$router.url.split("/");let t="";for(let n of e)if(""!=n){if("details"==n)break;t+="/"+n}this.$router.navigateByUrl(t)}routeToTabContent(e){this.childRouteSubscription&&this.childRouteSubscription.unsubscribe(),this.$router.navigate([`${e.tab_id}/${e.tab_key}`],{relativeTo:this.route})}openRecordStatusHistory(){return Object(h.c)(this,void 0,void 0,(function*(){const{LcdpStatusHistoryComponent:e}=yield n.e(890).then(n.bind(null,"diiJ"));this.dialog.open(e,{width:"40%",height:"100%",position:{right:"0px"},autoFocus:!1,maxWidth:"90vw",data:{applicationId:this._lcdpService.lcdpDetails.lcdpApplicationId,recordId:this._lcdpService.lcdpDetails.recordId}})}))}openComments(){return Object(h.c)(this,void 0,void 0,(function*(){let e={application_id:this._lcdpService.lcdpDetails.applicationId,unique_id_1:this._lcdpService.lcdpDetails.recordId,unique_id_2:"",application_name:this.applicationName,title:this.applicationName},t=this._lcdpService.lcdpDetails.recordData.fields_data,i=null;if(t)for(let n=0;n<t.length;n++)if(2==t[n].field_data.fieldId){i=t[n].field_display_value;break}let o={inputData:e,context:{ticket_id:i},commentBoxHeight:"100vh",commentBoxScrollHeight:"80%",url:window.location.href,unique_id:i?"  - Ticket : ("+i+")":""};const{ChatCommentContextModalComponent:a}=yield Promise.all([n.e(4),n.e(63),n.e(75),n.e(983)]).then(n.bind(null,"vg2w"));this.dialog.open(a,{height:"100%",width:"75%",position:{right:"0px"},data:{modalParams:o}})}))}openCTA(){this.quickCTAInput.applicationId=this._lcdpService.lcdpDetails.applicationId,this.quickCTAInput.objectId=this._lcdpService.lcdpDetails.recordId,this.sharedLazyLoadedComponentsService.openQuickCTAModal(this.quickCTAInput,this.dialog)}openMeetingInviteModal(){return Object(h.c)(this,void 0,void 0,(function*(){let e={},t=this._lcdpService.lcdpDetails.recordData.fields_data;for(let n=0;n<t.length&&n<6;n++)e[t[n].field_data.fieldName]=t[n].field_display_value;let i={application_id:this._lcdpService.lcdpDetails.applicationId,primary_unique_id:this._lcdpService.lcdpDetails.recordId,secondary_unique_id:null,attendees:[],cost_centre_object:{},is_from_header_creation:!1,meeting_meta_data:e};const{MeetingInviteComponent:o}=yield Promise.all([n.e(4),n.e(31),n.e(66),n.e(0),n.e(987)]).then(n.bind(null,"yi1p"));this.dialog.open(o,{height:"100%",width:"75%",position:{right:"0px"},data:{modalParams:i}})}))}openWfApprovals(e){return Object(h.c)(this,void 0,void 0,(function*(){if(e||this._lcdpService.lcdpDetails.recordData.wf_plugin_id){const{WfApproverDetailsComponent:t}=yield n.e(1001).then(n.bind(null,"4SbF"));this.dialog.open(t,{height:"auto",maxHeight:"100vh",minHeight:"25vw",width:"60%",maxWidth:"86%",data:{modalParams:{wfPluginId:e||this._lcdpService.lcdpDetails.recordData.wf_plugin_id,recordId:this._lcdpService.lcdpDetails.recordData._id,lcdpApplicationId:this._lcdpService.lcdpDetails.lcdpApplicationId}}}).afterClosed().subscribe(e=>{e&&"submit"==e.event&&1==e.data.is_workflow_complete&&(this._util.showMessage("Workflow Completed Successfully","Dismiss",3e3),e.data.is_new_wf_created&&e.data.created_wf_response&&this._lcdpService.updateLCDPWorkflowData(this._lcdpService.lcdpDetails.lcdpApplicationId,e.data.created_wf_response,this._lcdpService.lcdpDetails.recordData._id).subscribe(e=>Object(h.c)(this,void 0,void 0,(function*(){"E"==e.messType&&this._util.showMessage(e.messText,"Dismiss",3e3)})),e=>{this._util.showMessage(e,"dismiss",3e3)}))})}else this._util.showToastMessage("No workflow found !")}))}getSLADetails(){this.isMetaDataLoading=!0,this._lcdpService.getSLADetails(this._lcdpService.lcdpDetails.lcdpApplicationId,this._lcdpService.lcdpDetails.recordId).pipe(Object(f.a)(this._onDestroy)).subscribe(e=>{this.isMetaDataLoading=!1,"S"==e.messType&&e.data&&(this.slaList=e.data,this.slaList.sort((e,t)=>{if(e.hasOwnProperty("sequence_no")&&t.hasOwnProperty("sequence_no"))return e.sequence_no-t.sequence_no})),this.getWfDetails()},e=>{this.isMetaDataLoading=!1,this._lcdpService.showErrorMessage(e)})}getWfDetails(){this.isMetaDataLoading=!0,this._lcdpService.getWfDetails(this._lcdpService.lcdpDetails.recordId).pipe(Object(f.a)(this._onDestroy)).subscribe(e=>{if(this.isMetaDataLoading=!1,"S"==e.messType&&e.data){this.wfList=e.data;for(let e=0;e<this.wfList.length;e++)this.wfList[e].showWf=0===e}},e=>{this.isMetaDataLoading=!1,this._lcdpService.showErrorMessage(e)})}checkIfHoursIsEditable(){const e=this._lcdpService.getLCDPAppRoles(this._lcdpService.lcdpDetails.applicationId);if(e.length>0){const t=v.findWhere(e,{object_id:122});if(t&&"*"==t.object_value)this.isHoursEditAllowed=!0;else if(t&&"User"==t.object_value){const e=t.object_id_values?JSON.parse(t.object_id_values):[];e.length>0&&"ALL"==e[0]&&(this.isHoursEditAllowed=!0)}const n=v.findWhere(e,{object_id:552});if(n&&"*"==n.object_value)this.isCommentsVisible=!0;else if(n&&"User"==n.object_value){const e=n.object_id_values?JSON.parse(n.object_id_values):[];e.length>0&&"ALL"==e[0]&&(this.isCommentsVisible=!0)}}else this.isHoursEditAllowed=!1,this.isCommentsVisible=!1}openHoursOverlay(e,t){var n;if(this.isHoursEditAllowed&&!(null===(n=this.overlayRef)||void 0===n?void 0:n.hasAttached())&&!t.is_for_aging){this.currentSlaItem=t,this.hoursFormGroup.patchValue({hours:this.currentSlaItem.consumed_hours.hours,minutes:this.currentSlaItem.consumed_hours.minutes});const n=this.overlay.position().flexibleConnectedTo(e).withFlexibleDimensions(!0).withPush(!0).withViewportMargin(25).withGrowAfterOpen(!0).withPositions([{originX:"end",originY:"bottom",overlayX:"start",overlayY:"center"}]),i=this.overlay.scrollStrategies.close();this.overlayRef=this.overlay.create({positionStrategy:n,scrollStrategy:i,hasBackdrop:!0,panelClass:["panel-class","pop-up"]});const o=new b.h(this.templateRef,this.viewContainerRef);this.overlayRef.attach(o),this.overlayRef.backdropClick().subscribe(()=>{this.closeOverlay()})}}closeOverlay(){var e;null===(e=this.overlayRef)||void 0===e||e.dispose(),this.hoursFormGroup.reset({hours:null,minutes:null},{emitEvent:!1})}changeHours(){let e={hours:this.hoursFormGroup.get("hours").value,minutes:this.hoursFormGroup.get("minutes").value},t=this.recalculateOverdue(e),n=this.currentSlaItem.auto_consumed_hours&&"object"==typeof this.currentSlaItem.auto_consumed_hours?this.currentSlaItem.auto_consumed_hours:this.currentSlaItem.consumed_hours,i=this.calculateDeviation(e);this._lcdpService.updateSLAConsumedHours(this._lcdpService.lcdpDetails.lcdpApplicationId,this._lcdpService.lcdpDetails.recordId,this.currentSlaItem.sla_id,e,t,n,i).pipe(Object(f.a)(this._onDestroy)).subscribe(o=>{if("S"==o.messType){if(this.currentSlaItem.is_manual_update=!0,this.currentSlaItem.auto_consumed_hours=n,this.currentSlaItem.consumed_hours=e,this.currentSlaItem.total_consumed_hours=e,this.currentSlaItem.task_consumed_hours){const e=60*this.currentSlaItem.consumed_hours.hours+this.currentSlaItem.consumed_hours.minutes+(60*this.currentSlaItem.task_consumed_hours.hours+this.currentSlaItem.task_consumed_hours.minutes);this.currentSlaItem.total_consumed_hours={hours:Math.floor(e/60),minutes:e%60}}this.currentSlaItem.manual_update_deviation=i,this.currentSlaItem.is_sla_breached=t,this.currentSlaItem.updated_by=this.currentUser.name,this.currentSlaItem.updated_at=new Date}this._util.showMessage(o.messText,"Dismiss"),this.closeOverlay()},e=>{this._lcdpService.showErrorMessage(e)})}recalculateOverdue(e){return!!this.currentSlaItem&&60*e.hours+e.minutes>60*this.currentSlaItem.sla_hours.hours+this.currentSlaItem.sla_hours.minutes}calculateDeviation(e){if(this.currentSlaItem){let t=this.currentSlaItem.auto_consumed_hours&&"object"==typeof this.currentSlaItem.auto_consumed_hours?this.currentSlaItem.auto_consumed_hours:this.currentSlaItem.consumed_hours;return 60*t.hours+t.minutes-(60*e.hours+e.minutes)}return null}changeView(e){e.sla_default_view=e.sla_default_view&&"hours"==e.sla_default_view?"days":"hours"}toggleWorkflowHistory(){this.showWfHistory=!this.showWfHistory;for(let e=0;e<this.wfList.length;e++)this.wfList[e].showWf=this.showWfHistory||0===e}ngOnDestroy(){var e;this._onDestroy.next(),this._onDestroy.complete(),this.routeSubscription&&this.routeSubscription.unsubscribe(),this.parentRouteSubscription&&this.parentRouteSubscription.unsubscribe(),this.childRouteSubscription&&this.childRouteSubscription.unsubscribe(),this._lcdpService.resetLcdpDetails(),null===(e=this.overlayRef)||void 0===e||e.dispose()}}return e.\u0275fac=function(t){return new(t||e)(_["\u0275\u0275directiveInject"](m.a),_["\u0275\u0275directiveInject"](m.g),_["\u0275\u0275directiveInject"](C.a),_["\u0275\u0275directiveInject"](S.a),_["\u0275\u0275directiveInject"](x.a),_["\u0275\u0275directiveInject"](c.b),_["\u0275\u0275directiveInject"](_.ViewContainerRef),_["\u0275\u0275directiveInject"](y.e),_["\u0275\u0275directiveInject"](u.i),_["\u0275\u0275directiveInject"](w.a))},e.\u0275cmp=_["\u0275\u0275defineComponent"]({type:e,selectors:[["app-lcdp-detail-landing-page"]],viewQuery:function(e,t){if(1&e&&_["\u0275\u0275viewQuery"](M,!0),2&e){let e;_["\u0275\u0275queryRefresh"](e=_["\u0275\u0275loadQuery"]())&&(t.templateRef=e.first)}},decls:3,vars:3,consts:[["class","container d-flex h-100 flex-column",4,"ngIf"],["class","container-fluid lcdp-detail-styles",4,"ngIf"],[4,"ngIf"],[1,"container","d-flex","h-100","flex-column"],[1,"flex-grow-1"],[1,"row","pt-4","justify-content-center"],["diameter","30"],[1,"container-fluid","lcdp-detail-styles"],[1,"row","pt-2","slide-from-top"],[1,"col-1x","pl-0","pr-0"],["matTooltip","Back",1,"pt-1",2,"font-size","20px","cursor","pointer",3,"click"],[1,"col-8","pl-0","pr-0"],[3,"disabled","value","change"],["class","toggle-btn",3,"disabled","value","ngClass",4,"ngFor","ngForOf"],[1,"col-3x","d-flex","justify-content-end"],["mat-icon-button","","matTooltip","Open Approvals",1,"icon-button-inactive",3,"click"],[1,"iconButton"],["mat-icon-button","","matTooltip","Open Meeting Invite",1,"icon-button-inactive",3,"click"],["mat-icon-button","","class","icon-button-inactive","matTooltip","Open Comments",3,"click",4,"ngIf"],["mat-icon-button","","matTooltip","Status History",1,"icon-button-inactive",3,"click"],["mat-icon-button","",1,"icon-button-inactive",3,"matTooltip","click"],[1,"row","pt-2"],[3,"ngClass"],["class","col-3 right-in",4,"ngIf"],[1,"toggle-btn",3,"disabled","value","ngClass"],["mat-icon-button","","matTooltip","Open Comments",1,"icon-button-inactive",3,"click"],[1,"col-3","right-in"],[1,"row","pt-3"],[1,"col-6"],[1,"header"],[1,"pt-1","normalFont",3,"matTooltip"],[1,"row","p-3","d-flex","justify-content-center"],[1,"col-12"],[2,"border-radius","1rem",3,"value","change"],["class","toggle-btn",3,"value","ngClass",4,"ngFor","ngForOf"],[2,"height","55vh","overflow","scroll","overflow-x","hidden"],["class"," d-flex flex-column justify-content-center align-items-center","style","color: #cf0001; padding-top: 5rem;",4,"ngIf"],[1,"toggle-btn",3,"value","ngClass"],[4,"ngFor","ngForOf"],[1,"col-12","normalFont","d-flex","justify-content-center"],[1,"row","pt-2","pb-2"],["class","col-5",4,"ngIf"],[1,"col-1","p-0"],["matTooltip","Change View",1,"change-icon","pt-3",3,"click"],["class","pt-1 normalFont",4,"ngIf","ngIfElse"],["consumedHoursView",""],["class","row pt-2 pb-2",4,"ngIf"],["cdkConnectedOverlay","",3,"cdkConnectedOverlayOrigin"],["hoursTemplateRef",""],[1,"col-5"],[1,"pt-1","normalFont"],[4,"ngIf","ngIfElse"],["slaHoursView",""],["cdkOverlayOrigin","",1,"pt-1","normalFont",3,"disabled","ngStyle","ngClass","click"],["trigger","cdkOverlayOrigin","hoursContainer",""],[1,"col-4","header","pr-0"],[1,"col-8","normalFont"],[1,"pt-1","normalFont",2,"font-size","12px","font-weight","500",3,"matTooltip"],[1,"pt-1","normalFont",2,"font-size","12px","font-weight","500"],[1,"pop-up"],[3,"formGroup"],[1,"col-6","p-1"],["appearance","outline",1,"custom-field-class"],["type","number","min","0","formControlName","hours","matInput",""],["type","number","min","0","formControlName","minutes","matInput",""],["type","button","mat-raised-button","","matTooltip","Save",1,"done-btn",3,"click"],["aria-label","Done",1,"col-1"],[1,"col-2","ml-2"],["class","row",4,"ngIf"],[1,"row"],[1,"col-10"],[1,"col-2"],["mat-icon-button","","matTooltip","Workflow History",1,"icon-button-inactive",3,"ngClass","click"],["style","border-bottom: solid 1px #cacaca; cursor: pointer;",3,"click",4,"ngIf"],[2,"border-bottom","solid 1px #cacaca","cursor","pointer",3,"click"],[1,"col-3","header"],[1,"col-9"],[1,"status-dot",3,"ngStyle"],[1,"pl-2"],["class","p-0",4,"ngFor","ngForOf"],[1,"p-0"],["height","30px","width","30px","placement","top","content-type","template",1,"mr-1",2,"border","2px solid black","border-radius","50%",3,"src","ngStyle","tooltip"],["placement","top"],["approverTooltip",""],[1,"row","tooltip-text",2,"text-align","center"],[1,"row","tooltip-text"],[1,"tooltip-status-indicator","p-0","m-0",3,"ngStyle"],[1,"d-flex","flex-column","justify-content-center","align-items-center",2,"color","#cf0001","padding-top","5rem"],["src","https://assets.kebs.app/lms/svgs/empty-animate.svg","alt","","height","200","width","200"],[1,"mt-3"],[1,"d-flex","flex-column","justify-content-center","align-items-center",2,"color","#cf0001","padding-top","7rem"],["src","https://assets.kebs.app/lms/svgs/empty-animate.svg","alt","","height","250","width","250"],[1,"mt-3",2,"font-size","18px"]],template:function(e,t){1&e&&(_["\u0275\u0275template"](0,O,5,0,"div",0),_["\u0275\u0275template"](1,oe,26,8,"div",1),_["\u0275\u0275template"](2,ae,6,0,"ng-container",2)),2&e&&(_["\u0275\u0275property"]("ngIf",t.isDataLoading),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",t.recordTabs.length>0),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",!t.isDataLoading&&0==t.recordTabs.length))},directives:[i.NgIf,s.c,a.a,l.a,r.b,i.NgForOf,o.a,i.NgClass,m.l,r.a,y.a,y.b,i.NgStyle,u.J,u.w,u.n,p.c,p.g,u.A,u.e,d.b,u.v,u.l,I.a],pipes:[i.DatePipe,E],styles:[".lcdp-detail-styles[_ngcontent-%COMP%]   .toggle-btn[_ngcontent-%COMP%]{min-width:7rem}.lcdp-detail-styles[_ngcontent-%COMP%]   .btn-toggle-selected[_ngcontent-%COMP%]{background-color:#c92020!important;color:#fff}.lcdp-detail-styles[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{color:#5f5f5f;font-size:11px!important}.lcdp-detail-styles[_ngcontent-%COMP%]   .overflow[_ngcontent-%COMP%]{height:28rem;overflow:scroll;overflow-x:hidden}.lcdp-detail-styles[_ngcontent-%COMP%]   .normalFont[_ngcontent-%COMP%]{color:#181818;font-size:14px;font-weight:600;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.lcdp-detail-styles[_ngcontent-%COMP%]   .sla-breached-bg[_ngcontent-%COMP%]{background-color:#fff2f2}.lcdp-detail-styles[_ngcontent-%COMP%]   .sla-breached-color[_ngcontent-%COMP%]{color:#c92020}.lcdp-detail-styles[_ngcontent-%COMP%]   .desc[_ngcontent-%COMP%]{max-height:8rem;overflow:scroll;overflow-x:hidden;overflow-y:auto}.lcdp-detail-styles[_ngcontent-%COMP%]   .icon-button-inactive[_ngcontent-%COMP%]{background-color:#f1f1f1;line-height:8px;width:26px;height:26px;margin-right:15px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.lcdp-detail-styles[_ngcontent-%COMP%]   .icon-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.lcdp-detail-styles[_ngcontent-%COMP%]   .icon-button-active[_ngcontent-%COMP%]{background-color:#f1f1f1;line-height:8px;width:26px;height:26px;margin-right:15px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.lcdp-detail-styles[_ngcontent-%COMP%]   .icon-button-active[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#c92020;font-size:18px}.lcdp-detail-styles[_ngcontent-%COMP%]   .right-in[_ngcontent-%COMP%]{height:80vh;background:#fff;box-shadow:-1px 0 2px 1px #eee;transition:.3s ease-in;border-radius:5px;animation:slide-from-right .6s cubic-bezier(.25,.46,.45,.94)}.lcdp-detail-styles[_ngcontent-%COMP%]   .left-in[_ngcontent-%COMP%]{transition:.3s ease-in;animation:slide-from-left .6s cubic-bezier(.25,.46,.45,.94)}.lcdp-detail-styles[_ngcontent-%COMP%]   .status-dot[_ngcontent-%COMP%]{height:13px;width:13px;border-radius:50%;display:inline-block;vertical-align:middle}.lcdp-detail-styles[_ngcontent-%COMP%]   .tooltip-status-indicator[_ngcontent-%COMP%]{font-size:10px;margin-top:9px}.lcdp-detail-styles[_ngcontent-%COMP%]   .change-icon[_ngcontent-%COMP%]{font-size:18px;cursor:pointer}.lcdp-detail-styles[_ngcontent-%COMP%]   .col-1x[_ngcontent-%COMP%]{flex:0 0 3.333333%;max-width:3.333333%}.lcdp-detail-styles[_ngcontent-%COMP%]   .col-3x[_ngcontent-%COMP%]{flex:0 0 30%;max-width:30%}.lcdp-detail-styles[_ngcontent-%COMP%]   .slide-from-top[_ngcontent-%COMP%]{animation:slide-from-top .5s cubic-bezier(.25,.46,.45,.94) both;transition:.3s ease-in}.lcdp-detail-styles[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-top{0%{transform:translateY(10px)}to{transform:translateY(0)}}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}@keyframes slide-from-right{0%{transform:translateX(30px);opacity:0}65%{transform:translateX(-10px)}to{transform:translateX(0);opacity:1}}@keyframes slide-from-left{0%{transform:translateX(-30px);opacity:0}65%{transform:translateX(10px)}to{transform:translateX(0);opacity:1}}.pop-up[_ngcontent-%COMP%]{width:265px;border:1px solid #ccc;border-radius:5px;background:#fff;padding:10px;margin:0;box-shadow:0 4px 8px 0 rgba(0,0,0,.2),0 6px 20px 0 rgba(0,0,0,.19)}.done-btn[_ngcontent-%COMP%]{background-color:#66615b;color:#fff;padding:0!important}.custom-field-class[_ngcontent-%COMP%], .done-btn[_ngcontent-%COMP%]{width:100%}"]}),e})(),children:[{path:":tabId/:tabKey",component:(()=>{class e{constructor(e,t,n,i,o){this.route=e,this.$router=t,this.compiler=n,this._util=i,this._lcdpService=o}ngOnInit(){this.route.params.subscribe(e=>{e.tabId&&e.tabKey?this.resolveComponent(e.tabId):this._util.showToastMessage("Tab ID not Found! Kindly contact KEBS team")})}resolveComponent(e){switch(this.tabContentContainer&&this.tabContentContainer.clear(),this._lcdpService.setTabLoadingObservable(!0),parseInt(e)){case 1:this.loadLcdpOverviewContainer();break;case 2:this.loadLcdpActivityContainer();break;case 3:this.loadLcdpTasksContainer();break;case 4:this.loadLcdpAttachmentContainer();break;case 5:this.loadLcdpNotesContainer();break;case 6:this.loadLcdpCTAContainer();break;case 7:break;case 8:this.loadLcdpEffortDataContainer()}}loadLcdpOverviewContainer(){n.e(295).then(n.bind(null,"0Hje")).then(e=>{const t=this.compiler.compileModuleSync(e.LcdpOverviewModule).create(this.tabContentContainer.injector).componentFactoryResolver.resolveComponentFactory(e.LcdpOverviewComponent);this.tabContentContainer.createComponent(t),this._lcdpService.setTabLoadingObservable(!1)})}loadLcdpActivityContainer(){Promise.all([n.e(0),n.e(289)]).then(n.bind(null,"v28T")).then(e=>{const t=this.compiler.compileModuleSync(e.LcdpActivitiesModule).create(this.tabContentContainer.injector).componentFactoryResolver.resolveComponentFactory(e.LcdpActivitiesComponent);this.tabContentContainer.createComponent(t),this._lcdpService.setTabLoadingObservable(!1)})}loadLcdpNotesContainer(){Promise.all([n.e(0),n.e(293)]).then(n.bind(null,"T+TV")).then(e=>{const t=this.compiler.compileModuleSync(e.LcdpNotesModule).create(this.tabContentContainer.injector).componentFactoryResolver.resolveComponentFactory(e.LcdpNotesComponent);this.tabContentContainer.createComponent(t),this._lcdpService.setTabLoadingObservable(!1)})}loadLcdpTasksContainer(){n.e(129).then(n.bind(null,"onu9")).then(e=>{const t=this.compiler.compileModuleSync(e.LcdpTasksModule).create(this.tabContentContainer.injector).componentFactoryResolver.resolveComponentFactory(e.LcdpTasksComponent);this.tabContentContainer.createComponent(t),this._lcdpService.setTabLoadingObservable(!1)})}loadLcdpAttachmentContainer(){n.e(290).then(n.bind(null,"iK9o")).then(e=>{const t=this.compiler.compileModuleSync(e.LcdpAttachmentModule).create(this.tabContentContainer.injector).componentFactoryResolver.resolveComponentFactory(e.LcdpAttachmentComponent);this.tabContentContainer.createComponent(t),this._lcdpService.setTabLoadingObservable(!1)})}loadLcdpCTAContainer(){n.e(291).then(n.bind(null,"dN2p")).then(e=>{const t=this.compiler.compileModuleSync(e.LcdpCtaModule).create(this.tabContentContainer.injector).componentFactoryResolver.resolveComponentFactory(e.LcdpCtaComponent);this.tabContentContainer.createComponent(t),this._lcdpService.setTabLoadingObservable(!1)})}loadLcdpEffortDataContainer(){n.e(292).then(n.bind(null,"PXLu")).then(e=>{const t=this.compiler.compileModuleSync(e.LcdpEffortModule).create(this.tabContentContainer.injector).componentFactoryResolver.resolveComponentFactory(e.LcdpEffortComponent);this.tabContentContainer.createComponent(t),this._lcdpService.setTabLoadingObservable(!1)})}}return e.\u0275fac=function(t){return new(t||e)(_["\u0275\u0275directiveInject"](m.a),_["\u0275\u0275directiveInject"](m.g),_["\u0275\u0275directiveInject"](_.Compiler),_["\u0275\u0275directiveInject"](x.a),_["\u0275\u0275directiveInject"](S.a))},e.\u0275cmp=_["\u0275\u0275defineComponent"]({type:e,selectors:[["app-lcdp-detail-tab"]],viewQuery:function(e,t){if(1&e&&_["\u0275\u0275viewQuery"](le,!0,_.ViewContainerRef),2&e){let e;_["\u0275\u0275queryRefresh"](e=_["\u0275\u0275loadQuery"]())&&(t.tabContentContainer=e.first)}},decls:2,vars:0,consts:[["tabContentContainer",""]],template:function(e,t){1&e&&_["\u0275\u0275elementContainer"](0,null,0)},styles:[""]}),e})()}]}];let se=(()=>{class e{}return e.\u0275mod=_["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=_["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[m.k.forChild(re)],m.k]}),e})();var ce=n("mD+J");let de=(()=>{class e{}return e.\u0275mod=_["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=_["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,se,o.b,a.b,l.b,r.c,s.b,c.g,I.b,ce.a,y.h,d.c,p.e,u.E]]}),e})()}}]);