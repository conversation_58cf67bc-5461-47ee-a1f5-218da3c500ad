(window.webpackJsonp=window.webpackJsonp||[]).push([[734],{gvOY:function(e,t,i){"use strict";i.d(t,"a",(function(){return w})),i.d(t,"b",(function(){return I})),i.d(t,"c",(function(){return D}));var s=i("fXoL"),a=i("XNiG"),n=i("mrSG"),o=i("tk/3"),r=i("3Pt+"),l=i("1G5W"),c=i("Kj3r"),d=i("ofXK"),h=i("bTqV"),u=i("NFeN"),p=i("kmnG"),f=i("qFsG"),m=i("d3UM"),g=i("/1cH"),_=i("WJ5W"),b=i("FKr1");function v(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"mat-option",6),s["\u0275\u0275elementStart"](1,"div",7),s["\u0275\u0275element"](2,"div"),s["\u0275\u0275element"](3,"div"),s["\u0275\u0275element"](4,"div"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]())}function y(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"span"),s["\u0275\u0275text"](1),s["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,i=s["\u0275\u0275nextContext"]().$implicit;s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate1"]("",i[e],"\xa0")}}function C(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"mat-option",8),s["\u0275\u0275template"](1,y,2,1,"span",9),s["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,i=s["\u0275\u0275nextContext"]();s["\u0275\u0275property"]("value",e),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngForOf",i.optionLabel)}}let D=(()=>{class e{constructor(){this.msg=new a.b,this.removeOption=e=>{this.msg.next(e)},this.getRemoveIndex=()=>this.msg.asObservable()}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275prov=Object(s["\u0275\u0275defineInjectable"])({factory:function(){return new e},token:e,providedIn:"root"}),e})(),w=(()=>{class e{constructor(e,t){this._http=e,this._mulL=t,this.selectedValues=new s.EventEmitter,this.list=[],this.searchCtrl=new r.j,this.selectedValCtrl=new r.j,this._onDestroy=new a.b,this.isLoading=!1,this.fetchData=()=>{const e={headers:new o.f({"Content-Type":"application/json",Authorization:"Bearer "+this.token})};return this.isLoading=!0,new Promise((t,i)=>{this._http.post(this.API_URL?this.API_URL:"",{searchText:this.searchCtrl.value},e).subscribe(e=>{t(e),this.isLoading=!1},e=>{i(e),this.isLoading=!1,console.error(e)})})}}ngOnInit(){this.searchCtrl.valueChanges.pipe(Object(l.a)(this._onDestroy),Object(c.a)(700)).subscribe(e=>Object(n.c)(this,void 0,void 0,(function*(){null!=e&&""!=e&&(this.list=yield this.fetchData())}))),this.selectedValCtrl.valueChanges.subscribe(e=>{this.selectedValues.emit(e)}),this._mulL.getRemoveIndex().subscribe(e=>{this.removeSelectedOption(e)})}removeSelectedOption(e){let t=[];t=this.selectedValCtrl.value,t.splice(e,1),this.selectedValCtrl.patchValue([...t]),this.selectedValues.emit(this.selectedValCtrl.value)}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275directiveInject"](o.c),s["\u0275\u0275directiveInject"](D))},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["kebs-mul-sel-search"]],inputs:{label:"label",placeholder:"placeholder",appearance:"appearance",optionLabel:"optionLabel",API_URL:"API_URL",bodyParams:"bodyParams",token:"token"},outputs:{selectedValues:"selectedValues"},decls:10,vars:8,consts:[["appearance","outline",1,"full-width"],["multiple","",3,"placeholder","formControl"],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel","hideClearSearchButton"],["class","is-loading",4,"ngIf"],[3,"click"],[3,"value",4,"ngFor","ngForOf"],[1,"is-loading"],[1,"lds-facebook"],[3,"value"],[4,"ngFor","ngForOf"]],template:function(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"mat-form-field",0),s["\u0275\u0275elementStart"](1,"mat-label"),s["\u0275\u0275text"](2),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](3,"mat-select",1),s["\u0275\u0275elementStart"](4,"mat-option"),s["\u0275\u0275element"](5,"ngx-mat-select-search",2),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](6,v,5,0,"mat-option",3),s["\u0275\u0275elementStart"](7,"mat-option",4),s["\u0275\u0275listener"]("click",(function(){return t.selectedValCtrl.reset()})),s["\u0275\u0275text"](8,"None"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](9,C,2,2,"mat-option",5),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e&&(s["\u0275\u0275advance"](2),s["\u0275\u0275textInterpolate"](t.label),s["\u0275\u0275advance"](1),s["\u0275\u0275propertyInterpolate"]("placeholder",t.placeholder),s["\u0275\u0275property"]("formControl",t.selectedValCtrl),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("formControl",t.searchCtrl)("placeholderLabel",t.placeholder?t.placeholder:"")("hideClearSearchButton",!1),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",t.isLoading),s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("ngForOf",t.list))},directives:[p.c,p.g,m.c,r.v,r.k,b.p,_.a,d.NgIf,d.NgForOf],styles:[".full-width[_ngcontent-%COMP%]{width:100%}.lds-facebook[_ngcontent-%COMP%]{display:inline-block;position:sticky;width:80px;height:24px;margin-left:110px}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{display:inline-block;position:absolute;left:4px;width:5px;background:#cf0001;-webkit-animation:lds-facebook 1.2s cubic-bezier(0,.5,.5,1) infinite;animation:lds-facebook 1.2s cubic-bezier(0,.5,.5,1) infinite}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:first-child{left:8px;-webkit-animation-delay:-.24s;animation-delay:-.24s}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:nth-child(2){left:16px;-webkit-animation-delay:-.12s;animation-delay:-.12s}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:nth-child(3){left:24px;-webkit-animation-delay:0;animation-delay:0}@-webkit-keyframes lds-facebook{0%{top:8px;height:32px}50%,to{top:12px;height:16px}}@keyframes lds-facebook{0%{top:8px;height:32px}50%,to{top:12px;height:16px}}"]}),e})(),I=(()=>{class e{}return e.\u0275mod=s["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=s["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[d.CommonModule,h.b,u.b,p.e,f.c,m.d,g.c,o.d,r.E,_.b]]}),e})()},xGdZ:function(e,t,i){"use strict";i.r(t),i.d(t,"IsaDetailsModule",(function(){return A}));var s=i("ofXK"),a=i("Xa2L"),n=i("tyNb"),o=i("mrSG"),r=i("fXoL"),l=i("xG9w"),c=i("1G5W"),d=i("XNiG"),h=i("v2fc"),u=i("BVzC"),p=i("JLuW"),f=i("hJL4"),m=i("LcQX");const g=["isaDetailHeaderContainer"],_=["isaDetailItemContainer"];function b(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div",3),r["\u0275\u0275element"](1,"div",4),r["\u0275\u0275elementStart"](2,"div",5),r["\u0275\u0275element"](3,"mat-spinner",6),r["\u0275\u0275elementEnd"](),r["\u0275\u0275element"](4,"div",4),r["\u0275\u0275elementEnd"]())}const v=[{path:"",component:(()=>{class e{constructor(e,t,i,s,a,n,o){this._isaService=e,this.compiler=t,this.route=i,this._ErrorService=s,this.sharedLazyLoadedComponentsService=a,this.isaService=n,this.utilityService=o,this._onDestroy=new d.b,this.isDetailLoading=!1,this.activityTypes=[],this.templateType="Standard Cost",this.budgetApprovalTaskStatus="",this.budgetOfferTaskStatus="",this.offerApprovalCheck="",this.showApproveButton=!1,this.showStepper=!1,this.checkForOfferApprovalCheck=!1,this.showSkillAndLanguagesInfo=!1,this.displayStartEndDateInDtPg=!1,this.positionBasedApproval=!1,this.showDesignation=!1,this.showEntity=!1,this.showDivision=!1,this.showSubDivision=!1,this.showYearsofExp=!1,this.subTaskIgnoreTaskType=[],this.showActPlndBillableHrs=!1,this.isCurrentUserVendor=!1,this.slaList=[],this.show_budgeted_field=!1,this.getDefaultCurrencyConfigs()}ngOnInit(){return Object(o.c)(this,void 0,void 0,(function*(){this.user_role=this._isaService.getCurrentUserRole(),this.getTemplateTypeBasedOnRole(),this.getVisibilityMatrix(),this.getBudgetApprovalTaskStausId(),this.getBudgetOfferTaskStausId(),this.getBudgetOfferStausId(),yield this.checkVendorOrNot(),this.route.parent.params.subscribe(e=>{e.requestId&&this.getRequestDetails(e.requestId)})}))}getRequestDetails(e){this.isDetailLoading=!0,e&&this._isaService.getRequestById(e).pipe(Object(c.a)(this._onDestroy)).subscribe(e=>{"S"==e.messType&&e.data.length>0?(this.requestItem=e.data[0],this.getActivityTypes()):(this._isaService.showMessage("No Data Found !"),this.isDetailLoading=!1)},e=>{this.isDetailLoading=!1,this._ErrorService.userErrorAlert(e.error.errorCode,"Error getting request details",e.error.errMessage)})}getActivityTypes(){this._isaService.getActivityTypes().pipe(Object(c.a)(this._onDestroy)).subscribe(e=>{this.isDetailLoading=!1,"S"==e.messType&&e.data.length>0?(this.activityTypes=e.data,this.isaDetailHeaderContainerRef&&this.isaDetailHeaderContainerRef.clear(),this.isaDetailItemContainerRef&&this.isaDetailItemContainerRef.clear(),this.getRequestSLAs()):(this._isaService.showMessage(e.messText),this.isDetailLoading=!1)},e=>{this.isDetailLoading=!1,this._ErrorService.userErrorAlert(e.error.errorCode,"Error getting activity types",e.error.errMessage)})}getRequestSLAs(){this._isaService.getSLAByRequestId(this.requestItem._id).pipe(Object(c.a)(this._onDestroy)).subscribe(e=>{this.isDetailLoading=!1,this.slaList="N"==e.error&&e.data&&e.data.length>0?e.data[0].slas_mapped:[],this.loadIsaDetailHeader()},e=>{this.isDetailLoading=!1,this._ErrorService.userErrorAlert(e.error.errorCode,"Error getting SLA details",e.error.errMessage)})}loadIsaDetailHeader(){this.isaDetailHeaderContainerRef&&this.isaDetailHeaderContainerRef.clear(),i.e(287).then(i.bind(null,"Bh57")).then(e=>{const t=this.compiler.compileModuleSync(e.IsaDetailHeaderModule).create(this.isaDetailHeaderContainerRef.injector).componentFactoryResolver.resolveComponentFactory(e.IsaDetailHeaderComponent),i=this.isaDetailHeaderContainerRef.createComponent(t);i.instance.requestItem=this.requestItem,i.instance.activityTypes=this.activityTypes,i.instance.slaList=this.slaList,i.instance.default_currecncy_from=this.default_currecncy_from,i.instance.default_currency_to_display=this.default_currency_to_display,i.instance.show_budgeted_field=this.show_budgeted_field,i.instance.templateType=this.templateType,i.instance.budgetOfferTaskStatus=this.budgetOfferTaskStatus,i.instance.offerApprovalCheck=this.offerApprovalCheck,i.instance.checkForOfferApprovalCheck=this.checkForOfferApprovalCheck,i.instance.positionBasedApproval=this.positionBasedApproval,i.instance.isCurrentUserVendor=this.isCurrentUserVendor,i.instance.teams_format_section1=this.teams_format_section1,i.instance.teams_format_section2=this.teams_format_section2,i.instance.meeting_subject=this.meeting_subject,this.loadIsaDetailItem()})}loadIsaDetailItem(){this.isaDetailItemContainerRef&&this.isaDetailItemContainerRef.clear(),i.e(288).then(i.bind(null,"4RP9")).then(e=>{const t=this.compiler.compileModuleSync(e.IsaDetailItemModule).create(this.isaDetailHeaderContainerRef.injector).componentFactoryResolver.resolveComponentFactory(e.IsaDetailItemComponent),i=this.isaDetailHeaderContainerRef.createComponent(t);i.instance.requestItem=this.requestItem,i.instance.activityTypes=this.activityTypes,i.instance.slaList=this.slaList,i.instance.default_currecncy_from=this.default_currecncy_from,i.instance.default_currency_to_display=this.default_currency_to_display,i.instance.templateType=this.templateType,i.instance.budgetApprovalTaskStatus=this.budgetApprovalTaskStatus,i.instance.showApproveButton=this.showApproveButton,i.instance.showStepper=this.showStepper,i.instance.budgetFieldVisibleMatrix=this.budgetFieldVisibleMatrix,i.instance.showSkillAndLanguagesInfo=this.showSkillAndLanguagesInfo,i.instance.positionBasedApproval=this.positionBasedApproval,i.instance.showDesignation=this.showDesignation,i.instance.showEntity=this.showEntity,i.instance.showDivision=this.showDivision,i.instance.showSubDivision=this.showSubDivision,i.instance.showYearsofExp=this.showYearsofExp,i.instance.displayStartEndDateInDtPg=this.displayStartEndDateInDtPg,i.instance.showActPlndBillableHrs=this.showActPlndBillableHrs,i.instance.subTaskIgnoreTaskType=this.subTaskIgnoreTaskType,i.instance.totalResourcesLabel=this.totalResourcesLabel,i.instance.maxVendorCount=this.maxVendorCount,i.instance.teams_format_section1=this.teams_format_section1,i.instance.teams_format_section2=this.teams_format_section2,i.instance.meeting_subject=this.meeting_subject,i.instance.file_formID=this.file_formID})}checkVendorOrNot(){return Object(o.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>{this.isaService.checkVendorOrNot().subscribe(t=>{"N"==t.err?1==t.data?(this.isCurrentUserVendor=!0,e(this.isCurrentUserVendor)):(this.isCurrentUserVendor=!1,e(this.isCurrentUserVendor)):(console.log(t),this.utilityService.showMessage("Error while checking vendor or not","Dismiss",3e3),this.isCurrentUserVendor=!1,e(this.isCurrentUserVendor))},e=>{console.log(e),t(e)})})}))}ngOnDestroy(){this.isaDetailHeaderContainerRef&&this.isaDetailHeaderContainerRef.clear(),this.isaDetailItemContainerRef&&this.isaDetailItemContainerRef.clear(),this._onDestroy.next(),this._onDestroy.complete()}getDefaultCurrencyConfigs(){this.sharedLazyLoadedComponentsService.getISAConfigs().subscribe(e=>{e&&e.data.length>0?(this.default_currecncy_from=e.data[0].default_from_currency,this.default_currency_to_display=e.data[0].default_currency_to_be_displayed,this.show_budgeted_field=e.data[0].show_budgeted_field,this.showApproveButton=e.data[0].show_approve_button_in_detail_page,this.showStepper=e.data[0].show_stepper,this.checkForOfferApprovalCheck=e.data[0].offer_approval_check_in_task,this.showSkillAndLanguagesInfo=e.data[0].show_skill_and_language,this.displayStartEndDateInDtPg=e.data[0].displayStartEndDateInDtPg,this.positionBasedApproval=e.data[0].positionBasedApproval,this.showDesignation=!!e.data[0].showDesignation&&e.data[0].showDesignation,this.showEntity=!!e.data[0].showEntities&&e.data[0].showEntities,this.showDivision=!!e.data[0].showDivision&&e.data[0].showDivision,this.showSubDivision=!!e.data[0].showSubDivision&&e.data[0].showSubDivision,this.showYearsofExp=!!e.data[0].showYearsofExp&&e.data[0].showYearsofExp,this.totalResourcesLabel=e.data[0].totalResourcesLabel?e.data[0].totalResourcesLabel:"Total Resources",this.maxVendorCount=e.data[0].maxVendorCount?e.data[0].maxVendorCount:5,this.teams_format_section1=e.data[0].teams_format_section1?e.data[0].teams_format_section1:null,this.teams_format_section2=e.data[0].teams_format_section2?e.data[0].teams_format_section2:null,this.meeting_subject=e.data[0].meeting_subject?e.data[0].meeting_subject:null,this.file_formID=e.data[0].file_formID?e.data[0].file_formID:null,this.showActPlndBillableHrs=!!e.data[0].showActPlndBillableHrs&&e.data[0].showActPlndBillableHrs,this.subTaskIgnoreTaskType=Array.isArray(e.data[0].subTaskIgnoreTaskType)?e.data[0].subTaskIgnoreTaskType:[]):(this.default_currecncy_from="USD",this.default_currency_to_display="USD")},e=>{this.default_currecncy_from="USD",this.default_currency_to_display="USD",console.error(e)})}getTemplateTypeBasedOnRole(){this._isaService.getTemplateForUser({master_data_name:"RMG_fields",role_id:this.user_role}).pipe(Object(c.a)(this._onDestroy)).subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){this.templateType="N"==e.err&&e.data&&e.data.length>0?e.data[0].field_name:"Standard Cost"})),e=>{this.templateType="Standard Cost"})}getBudgetApprovalTaskStausId(){this._isaService.getMasterDataByMasterDataName({master_data_name:"budget_approval_check_status"}).pipe(Object(c.a)(this._onDestroy)).subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){"N"==e.err&&e.data&&e.data.length>0&&(this.budgetApprovalTaskStatus=e.data[0].status_id)})),e=>{console.log(e)})}getBudgetOfferTaskStausId(){this._isaService.getMasterDataByMasterDataName({master_data_name:"budget_offer_check_status"}).pipe(Object(c.a)(this._onDestroy)).subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){"N"==e.err&&e.data&&e.data.length>0&&(this.budgetOfferTaskStatus=e.data[0].status_id)})),e=>{console.log(e)})}getBudgetOfferStausId(){this._isaService.getMasterDataByMasterDataName({master_data_name:"offer_approval_check_status"}).pipe(Object(c.a)(this._onDestroy)).subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){"N"==e.err&&e.data&&e.data.length>0&&(this.offerApprovalCheck=e.data[0].status_id)})),e=>{console.log(e)})}getVisibilityMatrix(){this._isaService.getVisibilityMatrix({role_id:this.user_role}).pipe(Object(c.a)(this._onDestroy)).subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){"N"==e.err&&e.data&&e.data.length>0?(this.visibilityMatrix=e.data,this.budgetFieldVisibleMatrix=l.where(this.visibilityMatrix,{fieldName:"budget_information"})[0],console.log(this.budgetFieldVisibleMatrix)):this.visibilityMatrix=[]})),e=>{console.log(e)})}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](h.a),r["\u0275\u0275directiveInject"](r.Compiler),r["\u0275\u0275directiveInject"](n.a),r["\u0275\u0275directiveInject"](u.a),r["\u0275\u0275directiveInject"](p.a),r["\u0275\u0275directiveInject"](f.a),r["\u0275\u0275directiveInject"](m.a))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["app-isa-details-landing-page"]],viewQuery:function(e,t){if(1&e&&(r["\u0275\u0275viewQuery"](g,!0,r.ViewContainerRef),r["\u0275\u0275viewQuery"](_,!0,r.ViewContainerRef)),2&e){let e;r["\u0275\u0275queryRefresh"](e=r["\u0275\u0275loadQuery"]())&&(t.isaDetailHeaderContainerRef=e.first),r["\u0275\u0275queryRefresh"](e=r["\u0275\u0275loadQuery"]())&&(t.isaDetailItemContainerRef=e.first)}},decls:5,vars:1,consts:[["class","container d-flex h-100 flex-column",4,"ngIf"],["isaDetailHeaderContainer",""],["isaDetailItemContainer",""],[1,"container","d-flex","h-100","flex-column"],[1,"flex-grow-1"],[1,"row","pt-4","justify-content-center"],["diameter","30"]],template:function(e,t){1&e&&(r["\u0275\u0275template"](0,b,5,0,"div",0),r["\u0275\u0275elementContainer"](1,null,1),r["\u0275\u0275elementContainer"](3,null,2)),2&e&&r["\u0275\u0275property"]("ngIf",t.isDetailLoading)},directives:[s.NgIf,a.c],styles:[""]}),e})()}];let y=(()=>{class e{}return e.\u0275mod=r["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=r["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[n.k.forChild(v)],n.k]}),e})();var C=i("vxfF"),D=i("dlKe"),w=i("d3UM"),I=i("NFeN"),S=i("3Pt+"),k=i("rDax"),x=i("WJ5W"),O=i("gvOY"),T=i("STbY"),L=i("ZzPI");let A=(()=>{class e{}return e.\u0275mod=r["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=r["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[s.CommonModule,y,a.b,C.g,D.b,w.d,I.b,S.E,k.h,x.b,O.b,T.e,L.b]]}),e})()}}]);