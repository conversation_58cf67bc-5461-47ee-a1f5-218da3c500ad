(window.webpackJsonp=window.webpackJsonp||[]).push([[809],{"WD+x":function(e,t,n){"use strict";n.r(t),n.d(t,"EdHrDocumentsModule",(function(){return M}));var o=n("ofXK"),i=n("tyNb"),r=n("mrSG"),s=n("fXoL"),d=n("33Jv"),c=n("jAlA"),l=n("1A3m"),a=n("0IaG"),m=n("Wp6s"),u=n("bTqV"),h=n("STbY"),p=n("NFeN");const g=["menuTrigger"],f=["contentContainer"],b=[{path:"",component:(()=>{class e{constructor(e,t,n,o,i,r){this._router=e,this._edService=t,this._route=n,this._toaster=o,this._compiler=i,this.dialog=r,this.subs=new d.a,this.isEditAccess=!1}ngOnInit(){this.getEmployeeId(),this.$isEmpRetired=this._edService.getEmployeeRetiredStatus(),this.isEditAccess=this._edService.checkViewAndEditAccess(205),this.loadHrDocDetailContainer()}getEmployeeId(){this.subs.sink=this._edService.getEmployeeId().subscribe(e=>{this.associateId=parseInt(e)})}loadHrDocDetailContainer(){this.contentContainerRef&&this.contentContainerRef.clear(),Promise.all([n.e(4),n.e(19),n.e(26),n.e(191)]).then(n.bind(null,"UO1O")).then(e=>{const t=this._compiler.compileModuleSync(e.HrDocumentsModule).create(this.contentContainerRef.injector).componentFactoryResolver.resolveComponentFactory(e.HrDocumentsComponent);let n=this.contentContainerRef.createComponent(t).instance;n.associateId=this.associateId,n.attachmentPluginConfig.allowEdit=!1})}editHrDocDetails(){return Object(r.c)(this,void 0,void 0,(function*(){this.trigger.closeMenu();let e={associateId:parseInt(this.associateId),isFromModal:!0,isEditAccess:this.isEditAccess};const{HrDocumentsComponent:t}=yield Promise.all([n.e(4),n.e(19),n.e(26),n.e(191)]).then(n.bind(null,"UO1O"));this.dialog.open(t,{height:"75%",width:"70vw",panelClass:"e360-hr-doc-modalbox",data:{modalParams:e}}).afterClosed().subscribe(e=>{"updated"==e&&this.ngOnInit()})}))}openHrDocHistory(){return Object(r.c)(this,void 0,void 0,(function*(){this.trigger.closeMenu();let e={associateId:parseInt(this.associateId),tabKey:"hr_documents"};const{EditHistoryModalComponent:t}=yield Promise.all([n.e(0),n.e(955)]).then(n.bind(null,"Qcrq"));this.dialog.open(t,{height:"100%",width:"40%",position:{right:"0px"},data:{modalParams:e}})}))}ngOnDestroy(){this.subs.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275directiveInject"](i.g),s["\u0275\u0275directiveInject"](c.a),s["\u0275\u0275directiveInject"](i.a),s["\u0275\u0275directiveInject"](l.a),s["\u0275\u0275directiveInject"](s.Compiler),s["\u0275\u0275directiveInject"](a.b))},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-hr-documents-landing-page"]],viewQuery:function(e,t){if(1&e&&(s["\u0275\u0275viewQuery"](g,!0),s["\u0275\u0275viewQuery"](f,!0,s.ViewContainerRef)),2&e){let e;s["\u0275\u0275queryRefresh"](e=s["\u0275\u0275loadQuery"]())&&(t.trigger=e.first),s["\u0275\u0275queryRefresh"](e=s["\u0275\u0275loadQuery"]())&&(t.contentContainerRef=e.first)}},decls:23,vars:4,consts:[[1,"container-fluid","ed-hr-document-styles","mt-3"],[1,"p-4","mat-card","slide-from-down",2,"min-height","25vh"],[1,"d-flex","flex-row-reverse"],["mat-button","",1,"edit-button",3,"matMenuTriggerFor"],["menuTrigger","matMenuTrigger"],[1,"edit-icon"],[1,"pl-0","edit-text"],[1,"pl-0","drop-down-icon"],["editMenu","matMenu"],["mat-menu-item","",3,"disabled","click"],["mat-menu-item","",3,"click"],[1,"row","mt-2"],[1,"col-12","p-0"],["contentContainer",""]],template:function(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"div",0),s["\u0275\u0275elementStart"](1,"mat-card",1),s["\u0275\u0275elementStart"](2,"div",2),s["\u0275\u0275elementStart"](3,"div"),s["\u0275\u0275elementStart"](4,"button",3,4),s["\u0275\u0275elementStart"](6,"mat-icon",5),s["\u0275\u0275text"](7," edit "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](8,"span",6),s["\u0275\u0275text"](9," Edit "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](10,"mat-icon",7),s["\u0275\u0275text"](11," keyboard_arrow_down "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](12,"mat-menu",null,8),s["\u0275\u0275elementStart"](14,"button",9),s["\u0275\u0275listener"]("click",(function(){return t.editHrDocDetails()})),s["\u0275\u0275pipe"](15,"async"),s["\u0275\u0275text"](16,"Edit"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](17,"button",10),s["\u0275\u0275listener"]("click",(function(){return t.openHrDocHistory()})),s["\u0275\u0275text"](18," Edit History "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](19,"div",11),s["\u0275\u0275elementStart"](20,"div",12),s["\u0275\u0275elementContainer"](21,null,13),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e){const e=s["\u0275\u0275reference"](13);s["\u0275\u0275advance"](4),s["\u0275\u0275property"]("matMenuTriggerFor",e),s["\u0275\u0275advance"](10),s["\u0275\u0275propertyInterpolate"]("disabled",s["\u0275\u0275pipeBind1"](15,2,t.$isEmpRetired))}},directives:[m.a,u.a,h.f,p.a,h.g,h.d],pipes:[o.AsyncPipe],styles:[".ed-hr-document-styles[_ngcontent-%COMP%]{overflow-x:auto;height:72vh;scrollbar-width:none}.ed-hr-document-styles[_ngcontent-%COMP%]::-webkit-scrollbar{display:none}.ed-hr-document-styles[_ngcontent-%COMP%]   .overflow[_ngcontent-%COMP%]{height:28rem;overflow:scroll;overflow-x:hidden}.ed-hr-document-styles[_ngcontent-%COMP%]   .normalFont[_ngcontent-%COMP%]{color:#181818;font-size:14px;font-weight:600;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ed-hr-document-styles[_ngcontent-%COMP%]   .mat-card[_ngcontent-%COMP%]{width:100%;border:1px solid #fff;background-color:#fff;border-radius:5px}.ed-hr-document-styles[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%]{border:1px solid #dadce2;border-radius:4px;line-height:24px;padding:2px;float:right;display:flex;flex-direction:row;align-items:center}.ed-hr-document-styles[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%]   .edit-icon[_ngcontent-%COMP%]{font-size:14px;line-height:22px}.ed-hr-document-styles[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%]   .edit-text[_ngcontent-%COMP%]{color:#45546e;font-size:14px;font-weight:400}.ed-hr-document-styles[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%]   .drop-down-icon[_ngcontent-%COMP%]{font-size:21px;line-height:24px}  .e360-hr-doc-modalbox mat-dialog-container{padding:24px!important;overflow:auto}"]}),e})()}];let y=(()=>{class e{}return e.\u0275mod=s["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=s["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.k.forChild(b)],i.k]}),e})();var v=n("Qu3c"),C=n("jaxi"),w=n("MutI");let M=(()=>{class e{}return e.\u0275mod=s["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=s["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[o.CommonModule,y,p.b,u.b,v.b,C.c,h.e,w.d,a.g,m.d]]}),e})()}}]);