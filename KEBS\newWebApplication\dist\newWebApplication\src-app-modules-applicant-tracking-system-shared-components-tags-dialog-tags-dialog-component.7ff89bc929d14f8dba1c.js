(window.webpackJsonp=window.webpackJsonp||[]).push([[941],{DnWP:function(t,e,a){"use strict";a.d(e,"a",(function(){return o}));var i=a("tk/3"),n=a("wd/R"),r=a("fXoL");let o=(()=>{class t{constructor(t){this._http=t}fetchCandidatesForJob(t){return this._http.post("api/ats/candidate/fetchCandidatesForJob",{jobDetails:t})}deleteCandidatefromTag(t){return this._http.post("api/ats/candidate/deleteCandidatefromTag",{tagDetails:t})}tagCandidates(t){return this._http.post("api/ats/candidate/tagCandidates",{tagDetails:t})}moveCandidateToTalentPipeline(t,e,a,i,n,r,o){return this._http.post("api/ats/candidate/moveCandidateToTalentPipeline",{candidateIds:t,jobId:e,isBulkSelectActive:a,candidatesExcluded:i,stageId:n,filter:r,search_params:o})}blacklistCandidates(t,e,a,i,n,r,o,s){return this._http.post("api/ats/candidate/blacklistCandidates",{candidateIds:t,isBulkSelectActive:e,candidatesExcluded:a,stageId:i,jobId:n,filter:r,search_params:o,candidateStatusId:s})}updateCandidateJobStage(t){return this._http.post("api/ats/candidate/updateCandidateJobStage",{details:t})}sendEmailForCandidates(t){return this._http.post("api/ats/utilService/sendEmailForCandidates",{details:t})}uploadBenchMarkDetails(t,e){return this._http.post("api/ats/candidate/uploadBenchMarkDetails",{jobId:t,data:e})}uploadAptitudeScoreDetails(t,e,a,i){return this._http.post("api/ats/candidate/uploadAptitudeScoreDetails",{jobId:t,data:e,imageLinkArray:a,reportLinkArray:i})}uploadNonScientificScoreDetails(t,e,a,i){return this._http.post("api/ats/candidate/uploadNonScientificScoreDetails",{jobId:t,data:e,imageLinkArray:a,reportLinkArray:i})}downloadBenchmarkReport(t,e,a=0){return this._http.post("api/ats/candidate/downloadBenchmarkReport",{jobId:t,candidateId:a,filter:e})}downloadAptitudeReport(t,e,a=0){return this._http.post("api/ats/candidate/downloadAptitudeReport",{jobId:t,candidateId:a,filter:e})}downloadNonScientificReport(t,e,a=0){return this._http.post("api/ats/candidate/downloadNonScientificReport",{jobId:t,candidateId:a,filter:e})}fetchAllcandidateTags(){return this._http.post("api/ats/candidate/fetchAllcandidateTags",{})}getCandidateOtherApplicationDetails(t,e){return this._http.post("api/ats/candidate/getCandidateOtherApplicationDetails",{jobId:t,candidateId:e})}updateCandidateStageStatus(t){return this._http.post("api/ats/candidate/updateCandidateStageStatus",{details:t})}getCandidateBasicDetails(t,e,a){return this._http.post("api/ats/candidate/getCandidateBasicDetails",{jobId:t,candidateId:e,currentUserAid:a})}getInterviewPanelist(t){return this._http.post("api/ats/candidate/getInterviewPanelist",{details:t})}getInterviewerScoreDetails(t){return this._http.post("api/ats/candidate/getInterviewerScoreDetails",{details:t})}getCandidateResumeDetails(t,e){return this._http.post("api/ats/candidate/getCandidateResumeDetails",{jobId:t,candidateId:e})}fetchCandidateCardViewScoreDetails(t,e,a,i,n,r){return this._http.post("api/ats/candidate/fetchCandidateCardViewScoreDetails",{jobId:t,stageId:e,sectionId:a,sortByMetricId:i,skip:n,limit:r})}fetchScorecardSectionDetails(t,e){return this._http.post("api/ats/candidate/fetchScorecardSectionDetails",{jobId:t,stageId:e})}fetchScorecardMetricDetails(t,e,a){return this._http.post("api/ats/candidate/fetchScorecardMetricDetails",{jobId:t,stageId:e,sectionId:a})}getCandidateCommentsDetails(t,e,a,i){return this._http.post("api/ats/candidate/getCandidateCommentsDetails",{jobId:t,candidateId:e,currentUserAid:a,searchText:i})}postCandidateComments(t,e){return this._http.post("api/ats/candidate/postCandidateComments",{candidateId:t,commentsDetails:e})}getCandidateInterviewDetails(t,e){return this._http.post("api/ats/candidate/getCandidateInterviewDetails",{jobId:t,candidateId:e})}scheduleInterview(t){return this._http.post("api/ats/interview/scheduleInterview",{details:t})}getCandidateDocumentDetails(t,e){return this._http.post("api/ats/candidate/fetchcandidateDocuments",{jobId:t,candidateId:e})}fetchCandidateDetailsForQueue(t,e,a){return this._http.post("api/ats/candidate/fetchCandidateDetailsForQueue",{jobId:t,candidateId:e,currentUserAid:a})}getCandidateStreamDetails(t,e){return this._http.post("api/ats/candidate/getCandidateStreamDetails",{jobId:t,candidateId:e})}fetchCandidateUploadTemplate(){return this._http.post("api/ats/bulkActions/fetchCandidateUploadTemplate",{})}simulateCandidateApply(t,e){return this._http.post("api/ats/bulkActions/simulateCandidateApply",{jobId:t,data:e})}performCandidateBulkApply(t,e,a,i,n,r){return this._http.post("api/ats/bulkActions/performCandidateBulkApply",{jobId:t,data:e,fileName:a,fileSize:i,fileUploadStatus:n,isFromTalentPipeline:r})}fetchCandidateUploadFileHistory(t){return this._http.post("api/ats/bulkActions/fetchCandidateUploadFileHistory",{job_id:t})}fetchCandidateUpdSimulationResult(t){return this._http.post("api/ats/bulkActions/fetchCandidateUpdSimulationResult",{id:t})}getAllCandidateDetails(t,e,a,i){return this._http.post("api/ats/candidate/getAllCandidateDetails",{skip:t.skip,limit:t.limit,sort:t.sort,filter:t.filter,condition_query:t.condition_query,search_params:t.search_params,aid:e,isTalentPipeLine:a,candidateStatusId:t.candidateStatusId,isCampusJobTalentPipeline:i})}getTotalCandidateCount(t,e,a){return this._http.post("api/ats/candidate/getTotalCandidateCount",{skip:t.skip,limit:t.limit,sort:t.sort,filter:t.filter,condition_query:t.condition_query,search_params:t.search_params,isTalentPipeLine:e,candidateStatusId:t.candidateStatusId,isCampusJobTalentPipeline:a})}performResumeSimulation(t,e){return this._http.post("api/ats/bulkActions/simulateBulkResumeUpload",{data:e,jobId:t})}performCandidateBulkResumeUpload(t,e,a,i){return this._http.post("api/ats/bulkActions/performCandidateBulkResumeApply",{data:t,jobId:e,isFromTalentPipeline:a,dataArrayForResumeUpload:i})}getCandidateLatestJobDetails(t){return this._http.post("api/ats/candidate/getCandidateLatestJobDetails",{candidateId:t})}getScheduledInterviewDetails(t){return this._http.post("api/ats/interview/getScheduledInterviewDetails",{interviewId:t})}rescheduleInterview(t){return this._http.post("api/ats/interview/rescheduleInterview",{details:t})}cancelScheduledInterview(t){return this._http.post("api/ats/interview/cancelScheduledInterview",{interviewId:t})}getCandidateCountForJobByJobId(t){return this._http.post("api/ats/candidate/getCandidateCountForJobByJobId",{jobDetails:t})}updateStarRating(t,e,a,i){return this._http.post("api/ats/candidate/updateStarRating",{jobId:t,candidateId:e,rating:a,currentUserAid:i})}getMeetingsofInterviewers(t){return this._http.post("api/ats/interview/getMeetingsofInterviewers",{details:t})}getScorecardOtherDetails(t,e,a,i){return this._http.post("api/ats/candidate/getScorecardOtherDetails",{jobId:t,candidateId:e,stageId:a,interviewer:i})}sendDocumentUploadLink(t){return this._http.post("api/ats/candidate/sendDocumentUploadLink",{details:t})}getCandidateCustomQuestionDetails(t,e,a){return this._http.post("api/ats/candidate/getCandidateCustomQuestionDetails",{jobId:t,candidateId:e,currentUserAid:a})}uploadCandidateDocuments(t,e,a,n){const r=new FormData;r.append("jobId",t),r.append("candidateId",e),r.append("document",JSON.stringify(a)),r.append("file",n);const o=new i.f;return this._http.post("/api/ats/candidate/uploadCandidateDocuments",r,{headers:o})}deleteCandidateDocuments(t,e,a){return this._http.post("/api/ats/candidate/deleteCandidateDocuments",{jobId:t,candidateId:e,documentId:a})}retrieveCandidateOfferTrackingDetails(t,e){return this._http.post("api/ats/candidate/retrieveCandidateOfferTrackingDetails",{jobId:t,candidateId:e,currentDate:n().format("YYYY-MM-DD")})}getAllCandidateIds(t,e,a){return this._http.post("api/ats/candidate/getAllCandidateIds",{skip:t.skip,limit:t.limit,sort:t.sort,filter:t.filter,condition_query:t.condition_query,search_params:t.search_params,isTalentPipeLine:e,candidateStatusId:t.candidateStatusId,isCampusJobTalentPipeline:a})}getCandidateIdsForJobByJobId(t){return this._http.post("api/ats/candidate/getCandidateIdsForJobByJobId",{jobDetails:t})}uploadCertifications(t){return this._http.post("/api/ats/candidate/uploadCertifications",t)}performUploadUpdation(t,e){return this._http.post("api/ats/candidate/performUploadUpdation",{fileUploadDetails:t,candidateId:e})}getCandidateCertificates(t){return this._http.post("api/ats/candidate/getCandidateCertificates",{candidateId:t})}deleteCandidateCertificate(t){return this._http.post("api/ats/candidate/deleteCandidateCertificate",{certificateDetails:t})}createCampusInterviewForcandidates(t){return this._http.post("api/ats/interview/createCampusInterviewForcandidates",{details:t})}storeCandidateEmails(t){return this._http.post("api/ats/interview/storeCandidateEmails",{details:t})}getInterviewEmailData(t,e){return this._http.post("api/ats/interview/getInterviewEmailData",{jobId:e,candidateId:t})}}return t.\u0275fac=function(e){return new(e||t)(r["\u0275\u0275inject"](i.c))},t.\u0275prov=r["\u0275\u0275defineInjectable"]({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()},YVm3:function(t,e,a){"use strict";a.d(e,"a",(function(){return r}));var i=a("fXoL"),n=a("tk/3");let r=(()=>{class t{constructor(t){this._http=t,this.linkedinCode=""}generateJobID(){return this._http.post("api/ats/job/generateJobID",{})}createJob(t,e,a){return this._http.post("api/ats/job/createJob",{jobDetails:t,isPublish:e,saveAsDraft:a})}getAllJobDetails(t,e){return this._http.post("api/ats/job/getAllJobDetails",{filterConfig:t,isCampusJob:e})}deleteJobfromTag(t){return this._http.post("api/ats/candidate/deleteJobfromTag",{tagDetails:t})}tagJobs(t){return this._http.post("api/ats/candidate/tagJobs",{tagDetails:t})}updateJobDueDate(t,e,a,i,n,r,o,s,d){return this._http.post("api/ats/job/updateJobDueDate",{jobId:t,currentValidateDate:e,createdBy:a,isBulkSelectActive:i,jobsExcluded:n,jobStatusId:r,isCampusJob:o,search_params:s,filter:d})}updateJobHiringManager(t,e,a,i,n,r,o,s,d){return this._http.post("api/ats/job/updateJobHiringManager",{jobId:t,hiringManager:e,createdBy:a,isBulkSelectActive:i,jobsExcluded:n,jobStatusId:r,isCampusJob:o,search_params:s,filter:d})}updateStageTransisitonRule(t){return this._http.post("api/ats/job/updateStageTransisitonRule",{details:t})}getStageTransistionByJobId(t){return this._http.post("api/ats/job/getStageTransistionByJobId",{jobId:t})}getStageTransistionById(t,e){return this._http.post("api/ats/job/getStageTransistionById",{jobId:t,stageId:e})}getJobDetailsForEdit(t,e){return this._http.post("api/ats/job/getJobDetailsForEdit",{jobId:t,isFromCopyOrTemplate:e})}getAllJobTemplate(t){return this._http.post("api/ats/job/getAllJobTemplate",{searchParams:t})}getJobTemplatebyJobId(t){return this._http.post("api/ats/job/getJobTemplatebyJobId",{jobId:t})}updateFavList(t){return this._http.post("api/ats/job/updateFavList",{details:t})}getDraftJobDetails(t){return this._http.post("api/ats/job/getDraftJobDetails",{filterConfig:t})}getJobUploadTemplate(){return this._http.post("api/ats/job/getJobUploadTemplate",{})}simulateJobUpload(t){return this._http.post("api/ats/job/simulateJobUpload",{data:t})}createJobByUpload(t,e,a,i){return this._http.post("api/ats/job/createJobByUpload",{data:t,fileName:e,fileSize:a,fileUploadStatus:i})}removeJob(t,e,a,i,n,r){return this._http.post("api/ats/job/removeJob",{details:t,isBulkSelectActive:e,jobsExcluded:a,search_params:i,filter:n,isCampusJob:r})}updateJobStatus(t,e,a,i,n,r,o,s){return this._http.post("api/ats/job/updateJobStatus",{jobIds:t,jobStatus:e,isBulkSelectActive:a,jobsExcluded:i,jobStatusId:n,isCampusJob:r,search_params:o,filter:s})}getJobDetailsByJobId(t){return this._http.post("api/ats/job/getJobDetailsByJobId",{jobId:t})}getDraftJobCount(t){return this._http.post("api/ats/job/getDraftJobCount",{filterConfig:t})}getJobCountForLanding(t,e){return this._http.post("api/ats/job/getJobCountForLanding",{filterConfig:t,isCampusJob:e})}getCollegeAssociateWithJob(t,e){return this._http.post("api/ats/job/getCollegeAssociateWithJob",{jobId:t,filterConfig:e})}updateJobCollegeStatus(t){return this._http.post("api/ats/job/updateJobCollegeStatus",{details:t})}addCollegeToFav(t){return this._http.post("api/ats/job/addCollegeToFav",{details:t})}sendEmailToCollege(t){return this._http.post("api/ats/utilService/sendEmailToCollege",{details:t})}sendOfferToCandidates(t){return this._http.post("api/ats/job/sendOfferToCandidates",{details:t})}fetchAllJobTags(){return this._http.post("api/ats/candidate/fetchAllJobTags",{})}updateAllUnpublishedCollege(t,e){return this._http.post("api/ats/job/updateAllUnpublishedCollege",{jobId:t,otherColleges:e})}getEmailLogsForJobId(t,e,a,i){return this._http.post("api/ats/utilservice/getEmailLogsForJobId",{jobId:t,searchParams:e,forCollege:a,forCandidate:i})}retriggerEmail(t){return this._http.post("api/ats/utilservice/retriggerEmail",{email_log_id:t})}fetchJobUploadFileHistory(){return this._http.post("api/ats/job/fetchJobUploadFileHistory",{})}fetchJobSimulationResult(t){return this._http.post("api/ats/job/fetchJobSimulationResult",{document_id:t})}getAiEvaluationJobList(t,e){return this._http.post("api/ats/aimodel/getAiEvaluationJobList",{aiEvaluationfilterConfig:t,jobId:e})}getAiEValuationCount(t,e){return this._http.post("api/ats/aimodel/getAiEvaluationJobCount",{jobId:e,aiEvaluationfilterConfig:t})}getJobHiringTeamDetails(t){return this._http.post("api/ats/job/getJobHiringTeamDetails",{jobId:t})}updateJobHiringTeamDetails(t){return this._http.post("api/ats/job/updateJobHiringTeamDetails",{jobDetails:t})}determineJobType(t){return this._http.post("api/ats/job/determineJobType",{jobId:t})}generateOfferId(t,e){return this._http.post("api/ats/job/generateOfferId",{jobId:t,candidateId:e})}getJobDetailsForAIFilter(t){return this._http.post("api/ats/job/getJobDetailsForAIFilter",{jobId:t})}applyDynamicAIFilter(t){return this._http.post("api/ats/aimodel/performDynamicFiter",{payload:t})}}return t.\u0275fac=function(e){return new(e||t)(i["\u0275\u0275inject"](n.c))},t.\u0275prov=i["\u0275\u0275defineInjectable"]({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()},gaFh:function(t,e,a){"use strict";a.r(e),a.d(e,"TagsDialogComponent",(function(){return G}));var i=a("mrSG"),n=a("0IaG"),r=a("XNiG"),o=a("1G5W"),s=a("+rOU"),d=a("xG9w"),l=a("fXoL"),c=a("XNFG"),p=a("YVm3"),h=a("DnWP"),g=a("rDax"),u=a("RThm"),m=a("NFeN"),f=a("ofXK"),b=a("Xa2L"),C=a("3Pt+"),v=a("Jg0x"),_=a("cZdB");const I=["inputField"],y=["width"],S=["overlayTemplateRef"],w=function(t,e){return{"pointer-events":t,color:e}};function x(t,e){if(1&t){const t=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"mat-icon",4),l["\u0275\u0275listener"]("click",(function(){l["\u0275\u0275restoreView"](t);const e=l["\u0275\u0275nextContext"]().index;return l["\u0275\u0275nextContext"](2).onRemoveTag(e)})),l["\u0275\u0275pipe"](1,"isDarkColor"),l["\u0275\u0275text"](2," close "),l["\u0275\u0275elementEnd"]()}if(2&t){const t=l["\u0275\u0275nextContext"]().$implicit,e=l["\u0275\u0275nextContext"](2);l["\u0275\u0275property"]("ngStyle",l["\u0275\u0275pureFunction2"](3,w,e.isApiInProgress?"none":"",l["\u0275\u0275pipeBind1"](1,1,t.color)?"#fff":"#45546e"))}}const O=function(t){return{background:t}},T=function(t){return{color:t}};function D(t,e){if(1&t&&(l["\u0275\u0275elementContainerStart"](0),l["\u0275\u0275elementStart"](1,"div",11),l["\u0275\u0275elementStart"](2,"div",12),l["\u0275\u0275pipe"](3,"isDarkColor"),l["\u0275\u0275text"](4),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](5,x,3,6,"mat-icon",13),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementContainerEnd"]()),2&t){const t=e.$implicit,a=l["\u0275\u0275nextContext"](2);l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngStyle",l["\u0275\u0275pureFunction1"](6,O,t.color)),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngStyle",l["\u0275\u0275pureFunction1"](8,T,l["\u0275\u0275pipeBind1"](3,4,t.color)?"#fff":"#45546e")),l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate1"](" ",t.name," "),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",null==a.data?null:a.data.removeAccess)}}function j(t,e){if(1&t&&(l["\u0275\u0275elementContainerStart"](0),l["\u0275\u0275elementStart"](1,"div",9),l["\u0275\u0275template"](2,D,6,10,"ng-container",10),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementContainerEnd"]()),2&t){const t=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngForOf",t.data.tags)}}const P=function(t){return{"background-color":t}};function k(t,e){if(1&t&&l["\u0275\u0275element"](0,"div",26),2&t){const t=l["\u0275\u0275nextContext"](2);l["\u0275\u0275property"]("ngStyle",l["\u0275\u0275pureFunction1"](1,P,t.selectedTagColor&&""!=t.selectedTagColor?t.selectedTagColor:"#fff"))}}function E(t,e){1&t&&(l["\u0275\u0275elementContainerStart"](0),l["\u0275\u0275text"](1," Add Tag "),l["\u0275\u0275elementContainerEnd"]())}function M(t,e){1&t&&l["\u0275\u0275element"](0,"mat-spinner",27)}const A=function(t){return{visibility:t}},J=function(t){return{"pointer-events":t}};function F(t,e){if(1&t){const t=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementContainerStart"](0),l["\u0275\u0275elementStart"](1,"span",14),l["\u0275\u0275text"](2,"Tag"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](3,"div",15,16),l["\u0275\u0275listener"]("click",(function(){l["\u0275\u0275restoreView"](t);const e=l["\u0275\u0275reference"](5);return l["\u0275\u0275nextContext"]().openOverlay(e)})),l["\u0275\u0275template"](6,k,1,3,"div",17),l["\u0275\u0275elementStart"](7,"div",18),l["\u0275\u0275text"](8),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](9,"div",19),l["\u0275\u0275elementStart"](10,"span",14),l["\u0275\u0275text"](11,"Color"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](12,"input",20),l["\u0275\u0275listener"]("input",(function(e){return l["\u0275\u0275restoreView"](t),l["\u0275\u0275nextContext"]().onColorInput(e)})),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](13,"div",21),l["\u0275\u0275elementStart"](14,"div",22),l["\u0275\u0275listener"]("click",(function(){return l["\u0275\u0275restoreView"](t),l["\u0275\u0275nextContext"]().onClose()})),l["\u0275\u0275text"](15," Cancel "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](16,"div",23),l["\u0275\u0275listener"]("click",(function(){return l["\u0275\u0275restoreView"](t),l["\u0275\u0275nextContext"]().onAddTag()})),l["\u0275\u0275template"](17,E,2,0,"ng-container",24),l["\u0275\u0275template"](18,M,1,0,"ng-template",null,25,l["\u0275\u0275templateRefExtractor"]),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementContainerEnd"]()}if(2&t){const t=l["\u0275\u0275reference"](19),e=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](6),l["\u0275\u0275property"]("ngIf",e.selectedTagColor&&""!=e.selectedTagColor),l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate1"](" ",""==e.searchTagControl&&""==e.selectedTagName?"Search":""!=e.searchTagControl?e.searchTagControl:e.selectedTagName," "),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngStyle",l["\u0275\u0275pureFunction1"](8,A,""!=e.searchTagControl?"":"hidden")),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("value",e.colorValue),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngStyle",l["\u0275\u0275pureFunction1"](10,J,e.isApiInProgress?"none":"")),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngStyle",l["\u0275\u0275pureFunction1"](12,J,e.isApiInProgress?"none":"")),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",!e.isApiInProgress)("ngIfElse",t)}}function B(t,e){1&t&&(l["\u0275\u0275elementStart"](0,"div",37),l["\u0275\u0275namespaceSVG"](),l["\u0275\u0275elementStart"](1,"svg",38),l["\u0275\u0275elementStart"](2,"mask",39),l["\u0275\u0275element"](3,"rect",40),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](4,"g",41),l["\u0275\u0275element"](5,"path",42),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]())}function R(t,e){if(1&t){const t=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementContainerStart"](0),l["\u0275\u0275elementStart"](1,"div",34),l["\u0275\u0275listener"]("click",(function(){l["\u0275\u0275restoreView"](t);const a=e.$implicit;return l["\u0275\u0275nextContext"](3).onChangeInValue(a.id)})),l["\u0275\u0275elementStart"](2,"div",35),l["\u0275\u0275element"](3,"div",26),l["\u0275\u0275elementStart"](4,"div"),l["\u0275\u0275text"](5),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](6,B,6,0,"div",36),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementContainerEnd"]()}if(2&t){const t=e.$implicit,a=l["\u0275\u0275nextContext"](3);l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("ngStyle",l["\u0275\u0275pureFunction1"](5,P,t.color)),l["\u0275\u0275advance"](1),l["\u0275\u0275classMap"](t.id==a.selectedTagId?"checked-list":"list"),l["\u0275\u0275advance"](1),l["\u0275\u0275textInterpolate1"](" ",t.name," "),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",t.id==a.selectedTagId)}}function U(t,e){if(1&t&&(l["\u0275\u0275elementContainerStart"](0),l["\u0275\u0275elementStart"](1,"div",33),l["\u0275\u0275template"](2,R,7,7,"ng-container",10),l["\u0275\u0275pipe"](3,"filter"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementContainerEnd"]()),2&t){const t=l["\u0275\u0275nextContext"](2);l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngForOf",l["\u0275\u0275pipeBind2"](3,1,t.currentMasterData,t.searchTagControl))}}function V(t,e){1&t&&(l["\u0275\u0275elementContainerStart"](0),l["\u0275\u0275elementStart"](1,"span",43),l["\u0275\u0275text"](2,"No Results Found!"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementContainerEnd"]())}const L=function(t){return{width:t}};function N(t,e){if(1&t){const t=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",28),l["\u0275\u0275elementStart"](1,"div",29),l["\u0275\u0275elementStart"](2,"input",30,31),l["\u0275\u0275listener"]("ngModelChange",(function(e){return l["\u0275\u0275restoreView"](t),l["\u0275\u0275nextContext"]().searchTagControl=e}))("keydown.enter",(function(){return l["\u0275\u0275restoreView"](t),l["\u0275\u0275nextContext"]().onEnterKeyPressed()})),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275element"](4,"div",32),l["\u0275\u0275template"](5,U,4,4,"ng-container",6),l["\u0275\u0275pipe"](6,"filter"),l["\u0275\u0275template"](7,V,3,0,"ng-container",6),l["\u0275\u0275pipe"](8,"filter"),l["\u0275\u0275elementEnd"]()}if(2&t){const t=l["\u0275\u0275nextContext"]();l["\u0275\u0275property"]("ngStyle",l["\u0275\u0275pureFunction1"](10,L,t.overlayWidth)),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngModel",t.searchTagControl),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("ngIf",l["\u0275\u0275pipeBind2"](6,4,t.currentMasterData,t.searchTagControl).length>0),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf",0==l["\u0275\u0275pipeBind2"](8,7,t.currentMasterData,t.searchTagControl).length)}}let G=(()=>{class t{constructor(t,e,a,i,n,o,s,d){this.data=t,this._dialogRef=e,this._toaster=a,this._jobService=i,this._candidateService=n,this._overlay=o,this._viewContainerRef=s,this._onboardingService=d,this._onDestroy=new r.b,this.currentMasterData=[],this.searchTagControl="",this.selectedTagId=null,this.selectedTagName="",this.selectedTagColor="",this.colorValue="#F3EAFD",this.isApiInProgress=!1}ngOnInit(){this.currentMasterData=this.data.tagsMasterData&&this.data.tagsMasterData.length>0?[...this.data.tagsMasterData]:[]}onClose(){this._dialogRef.close("V"==this.data.mode&&this.data.tags)}onColorInput(t){this.colorValue=t.target.value}onChangeInValue(t){var e,a;this.selectedTagId=t,this.searchTagControl="",this.selectedTagName=(null===(e=this.currentMasterData.find(e=>e.id==t))||void 0===e?void 0:e.name)||"",this.selectedTagColor=(null===(a=this.currentMasterData.find(e=>e.id==t))||void 0===a?void 0:a.color)||"",this.closeOverlay()}onEnterKeyPressed(){this.selectedTagId=null,this.selectedTagName="",this.selectedTagColor="",this.closeOverlay()}onRemoveTag(t){return Object(i.c)(this,void 0,void 0,(function*(){this.isApiInProgress=!0;let e=[];if("jobOverview"==this.data.module){let a={jobId:this.data.id,tagId:this.data.tags[t].id};e=this.data.tags.filter(t=>t.id!=a.tagId),yield this.deleteJobfromTag(a)}else if("jobDetailsCandidate"==this.data.module||"allCandidateOverview"==this.data.module){let a={candidateId:this.data.id,tagId:this.data.tags[t].id};e=this.data.tags.filter(t=>t.id!=a.tagId),yield this.deleteCandidatefromTag(a)}else if("onboardingDetailsCandidate"==this.data.module){let a={candidateId:this.data.id,tagId:this.data.tags[t].id};e=this.data.tags.filter(t=>t.id!=a.tagId),yield this.removeTagForOnboardingCandidate(a)}else if("onboardingDetailsGroup"==this.data.module){let a={groupId:this.data.id,tagId:this.data.tags[t].id};e=this.data.tags.filter(t=>t.id!=a.tagId),yield this.removeTagForOnboardingGroup(a)}this.data.tags=e,this.data.subTitle=e.length+" tag(s)",this.isApiInProgress=!1}))}onAddTag(){return Object(i.c)(this,void 0,void 0,(function*(){if(""!=this.searchTagControl||null!=this.selectedTagId)if(null!=this.colorValue&&""!=this.colorValue){if(this.isApiInProgress=!0,"jobOverview"==this.data.module){let t={tagId:this.selectedTagId,tagName:this.searchTagControl,tagColor:this.colorValue,jobIds:this.data.bulkIds,isBulkSelectActive:this.data.isBulkSelectActive,jobsExcluded:d.pluck(this.data.unselectedData,"jobId"),jobStatusId:this.data.jobStatusId,isCampusJob:this.data.isCampusJob,search_params:this.data.search_params,filter:this.data.filter};(yield this.tagJobs(t))&&this._dialogRef.close(!0),this.isApiInProgress=!1}else if("jobDetailsCandidate"==this.data.module||"allCandidateOverview"==this.data.module||"aiEvaluation"==this.data.module){let t={tagId:this.selectedTagId,tagName:this.searchTagControl,tagColor:this.colorValue,candidateIds:this.data.bulkIds,isBulkSelectActive:this.data.isBulkSelectActive,stageId:this.data.stageId,candidatesExcluded:d.pluck(this.data.unselectedData,"candidate_id"),jobId:this.data.jobId,isTalentPipeLine:this.data.isTalentPipeLine,filter:this.data.filter,search_params:this.data.search_params,candidateStatusId:this.data.candidateStatusId};(yield this.tagCandidates(t))&&this._dialogRef.close(!0),this.isApiInProgress=!1}else if("onboardingDetailsCandidate"==this.data.module){let t={selectedDetails:this.data.bulkIds,unselectedDetails:this.data.unselectedData,isBulkSelectActive:this.data.isBulkSelectActive,searchParams:this.data.search_params,onboardingStatus:this.data.onboardingStatus,groupId:this.data.groupId,updatedValue:{tagId:this.selectedTagId,tagName:this.searchTagControl,tagColor:this.colorValue}};(yield this.addTagsToOnboardingCandidatesInBulk(t))&&this._dialogRef.close(!0),this.isApiInProgress=!1}else if("onboardingDetailsGroup"==this.data.module){let t={selectedDetails:this.data.bulkIds,unselectedDetails:this.data.unselectedData,isBulkSelectActive:this.data.isBulkSelectActive,searchParams:this.data.search_params,updatedValue:{tagId:this.selectedTagId,tagName:this.searchTagControl,tagColor:this.colorValue}};(yield this.addTagsToOnboardingGroupInBulk(t))&&this._dialogRef.close(!0),this.isApiInProgress=!1}}else this._toaster.showWarning("Warning \u26a0\ufe0f","Kindly select a color!",7e3);else this._toaster.showWarning("Warning \u26a0\ufe0f","Kindly type a Tag Name or select a Tag!",7e3)}))}openOverlay(t){var e;if(!(null===(e=this.overlayRef)||void 0===e?void 0:e.hasAttached())){const e=this._overlay.position().flexibleConnectedTo(t).withFlexibleDimensions(!0).withPush(!0).withViewportMargin(0).withGrowAfterOpen(!0).withPositions([{originX:"center",originY:"bottom",overlayX:"center",overlayY:"top"}]),a=this._overlay.scrollStrategies.close();e.withDefaultOffsetY(-40),this.overlayRef=this._overlay.create({positionStrategy:e,scrollStrategy:a,hasBackdrop:!0,backdropClass:"",panelClass:["pop-up"]});const i=new s.h(this.overlayTemplateRef,this._viewContainerRef);this.overlayRef.attach(i),setTimeout(()=>{this.inputField&&this.inputField.nativeElement&&this.inputField.nativeElement.focus()},200),this.overlayRef.backdropClick().subscribe(()=>{this.selectedTagId=null,this.selectedTagName="",this.selectedTagColor="",this.closeOverlay()})}}closeOverlay(){var t;null===(t=this.overlayRef)||void 0===t||t.dispose()}deleteJobfromTag(t){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,a)=>this._jobService.deleteJobfromTag(t).pipe(Object(o.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?(this._toaster.showSuccess("Success","Tag Removed Successfully!",7e3),e(!0)):(this._toaster.showError("Error",t.msg,7e3),e(!1))},error:t=>{this._toaster.showError("Error",t.message?t.message:"Delete Tag API Failed!",7e3),this.isApiInProgress=!1,a(!1)}}))}))}tagJobs(t){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,a)=>this._jobService.tagJobs(t).pipe(Object(o.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?(this._toaster.showSuccess("Success","Tag Added Successfully!",7e3),e(!0)):(this._toaster.showError("Error",t.msg,7e3),e(!1))},error:t=>{this._toaster.showError("Error",t.message?t.message:"Delete Tag API Failed!",7e3),this.isApiInProgress=!1,a(!1)}}))}))}deleteCandidatefromTag(t){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,a)=>this._candidateService.deleteCandidatefromTag(t).pipe(Object(o.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?(this._toaster.showSuccess("Success","Tag Removed Successfully!",7e3),e(!0)):(this._toaster.showError("Error",t.msg,7e3),e(!1))},error:t=>{this._toaster.showError("Error",t.message?t.message:"Delete Tag API Failed!",7e3),this.isApiInProgress=!1,a(!1)}}))}))}tagCandidates(t){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,a)=>this._candidateService.tagCandidates(t).pipe(Object(o.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?(this._toaster.showSuccess("Success","Tag Added Successfully!",7e3),e(!0)):(this._toaster.showError("Error",t.msg,7e3),e(!1))},error:t=>{this._toaster.showError("Error",t.message?t.message:"Delete Tag API Failed!",7e3),this.isApiInProgress=!1,a(!1)}}))}))}removeTagForOnboardingCandidate(t){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,a)=>this._onboardingService.removeTagForOnboardingCandidate(t).pipe(Object(o.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?(this._toaster.showSuccess("Success",t.msg,7e3),e(!0)):(this._toaster.showError("Error",t.msg,7e3),e(!1))},error:t=>{this._toaster.showError("Error",t.message?t.message:"Delete Tag API Failed!",7e3),this.isApiInProgress=!1,a(!1)}}))}))}addTagsToOnboardingCandidatesInBulk(t){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,a)=>this._onboardingService.addTagsToOnboardingCandidatesInBulk(t).pipe(Object(o.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?(this._toaster.showSuccess("Success",t.msg,7e3),e(!0)):(this._toaster.showError("Error",t.msg,7e3),e(!1))},error:t=>{this._toaster.showError("Error",t.message?t.message:"Delete Tag API Failed!",7e3),this.isApiInProgress=!1,a(!1)}}))}))}removeTagForOnboardingGroup(t){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,a)=>this._onboardingService.removeTagForOnboardingGroup(t).pipe(Object(o.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?(this._toaster.showSuccess("Success",t.msg,7e3),e(!0)):(this._toaster.showError("Error",t.msg,7e3),e(!1))},error:t=>{this._toaster.showError("Error",t.message?t.message:"Delete Tag API Failed!",7e3),this.isApiInProgress=!1,a(!1)}}))}))}addTagsToOnboardingGroupInBulk(t){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,a)=>this._onboardingService.addTagsToOnboardingGroupInBulk(t).pipe(Object(o.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?(this._toaster.showSuccess("Success",t.msg,7e3),e(!0)):(this._toaster.showError("Error",t.msg,7e3),e(!1))},error:t=>{this._toaster.showError("Error",t.message?t.message:"Delete Tag API Failed!",7e3),this.isApiInProgress=!1,a(!1)}}))}))}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}}return t.\u0275fac=function(e){return new(e||t)(l["\u0275\u0275directiveInject"](n.a),l["\u0275\u0275directiveInject"](n.h),l["\u0275\u0275directiveInject"](c.a),l["\u0275\u0275directiveInject"](p.a),l["\u0275\u0275directiveInject"](h.a),l["\u0275\u0275directiveInject"](g.e),l["\u0275\u0275directiveInject"](l.ViewContainerRef),l["\u0275\u0275directiveInject"](u.a))},t.\u0275cmp=l["\u0275\u0275defineComponent"]({type:t,selectors:[["app-tags-dialog"]],viewQuery:function(t,e){if(1&t&&(l["\u0275\u0275viewQuery"](I,!0),l["\u0275\u0275viewQuery"](y,!0),l["\u0275\u0275viewQuery"](S,!0)),2&t){let t;l["\u0275\u0275queryRefresh"](t=l["\u0275\u0275loadQuery"]())&&(e.inputField=t.first),l["\u0275\u0275queryRefresh"](t=l["\u0275\u0275loadQuery"]())&&(e.width=t.first),l["\u0275\u0275queryRefresh"](t=l["\u0275\u0275loadQuery"]())&&(e.overlayTemplateRef=t.first)}},decls:13,vars:8,consts:[[1,"bg-container"],[1,"d-flex","align-items-center","justify-content-between",2,"margin-bottom","4px"],[1,"title","p-0"],[1,"icon"],[1,"icon",3,"ngStyle","click"],[1,"sub-title"],[4,"ngIf"],["cdkConnectedOverlay","",3,"cdkConnectedOverlayOrigin"],["overlayTemplateRef",""],[1,"d-flex","flex-wrap",2,"gap","10px","overflow-y","auto","max-height","300px"],[4,"ngFor","ngForOf"],[1,"d-flex","align-items-center","tag",3,"ngStyle"],[1,"tag-text",3,"ngStyle"],["class","icon",3,"ngStyle","click",4,"ngIf"],[1,"tag-label"],["cdkOverlayOrigin","",1,"tag-search",3,"click"],["triggerOverlay","cdkOverlayOrigin","triggerOverlayField",""],["class","circle",3,"ngStyle",4,"ngIf"],[1,"tag-search-name"],[1,"d-flex","flex-wrap","align-items-center","tag-input",2,"margin-top","12px","gap","12px",3,"ngStyle"],["type","color",3,"value","input"],[1,"d-flex","justify-content-end",2,"margin-top","12px"],[1,"cancel-btn",3,"ngStyle","click"],[1,"add-btn",3,"ngStyle","click"],[4,"ngIf","ngIfElse"],["loading",""],[1,"circle",3,"ngStyle"],["diameter","20",1,"white-spinner"],[1,"overlay-container",3,"ngStyle"],[1,"search-bar"],["type","text","maxlength","55","placeholder","Search...",3,"ngModel","ngModelChange","keydown.enter"],["inputField",""],[1,"divider"],[1,"d-flex","flex-column","list-view"],[1,"d-flex","align-items-center","justify-content-between",2,"cursor","pointer",3,"click"],[1,"d-flex","align-items-center",2,"gap","8px"],["class","svg",4,"ngIf"],[1,"svg"],["width","18","height","18","viewBox","0 0 18 18"],["id","mask0_9363_260624","maskUnits","userSpaceOnUse","x","0","y","0","width","18","height","18",2,"mask-type","alpha"],["width","18","height","18"],["mask","url(#mask0_9363_260624)"],["d","M7.49961 10.1996L11.9246 5.77461C12.0621 5.63711 12.2371 5.56836 12.4496 5.56836C12.6621 5.56836 12.8371 5.63711 12.9746 5.77461C13.1121 5.91211 13.1809 6.08711 13.1809 6.29961C13.1809 6.51211 13.1121 6.68711 12.9746 6.82461L8.02461 11.7746C7.87461 11.9246 7.69961 11.9996 7.49961 11.9996C7.29961 11.9996 7.12461 11.9246 6.97461 11.7746L5.02461 9.82461C4.88711 9.68711 4.81836 9.51211 4.81836 9.29961C4.81836 9.08711 4.88711 8.91211 5.02461 8.77461C5.16211 8.63711 5.33711 8.56836 5.54961 8.56836C5.76211 8.56836 5.93711 8.63711 6.07461 8.77461L7.49961 10.1996Z"],[1,"no-result-text"]],template:function(t,e){1&t&&(l["\u0275\u0275elementStart"](0,"div",0),l["\u0275\u0275elementStart"](1,"div",1),l["\u0275\u0275elementStart"](2,"div",2),l["\u0275\u0275text"](3),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](4,"div",3),l["\u0275\u0275elementStart"](5,"mat-icon",4),l["\u0275\u0275listener"]("click",(function(){return e.onClose()})),l["\u0275\u0275text"](6,"close"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](7,"div",5),l["\u0275\u0275text"](8),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](9,j,3,1,"ng-container",6),l["\u0275\u0275template"](10,F,20,14,"ng-container",6),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](11,N,9,12,"ng-template",7,8,l["\u0275\u0275templateRefExtractor"])),2&t&&(l["\u0275\u0275advance"](3),l["\u0275\u0275textInterpolate"](e.data.title),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngStyle",l["\u0275\u0275pureFunction1"](6,J,e.isApiInProgress?"none":"")),l["\u0275\u0275advance"](3),l["\u0275\u0275textInterpolate"](e.data.subTitle),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf","V"==e.data.mode),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf","C"==e.data.mode),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("cdkConnectedOverlayOrigin",e.triggerOverlay))},directives:[m.a,f.NgStyle,f.NgIf,g.a,f.NgForOf,g.b,b.c,C.e,C.q,C.v,C.y],pipes:[v.a,_.a],styles:[".bg-container[_ngcontent-%COMP%]{padding:16px}.bg-container[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:18px;font-weight:700;color:#111434}.bg-container[_ngcontent-%COMP%]   .sub-title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:12px;font-weight:400;color:#8b95a5;margin-bottom:12px}.bg-container[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{width:18px;height:18px;font-size:18px;color:#1b2140;cursor:pointer}.bg-container[_ngcontent-%COMP%]   .tag[_ngcontent-%COMP%]{padding:8px 16px;border-radius:8px}.bg-container[_ngcontent-%COMP%]   .tag[_ngcontent-%COMP%]   .tag-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:400;color:#45546e}.bg-container[_ngcontent-%COMP%]   .tag[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{width:12px;height:12px;font-size:12px;padding-left:8px;cursor:pointer;color:#45546e}.bg-container[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{color:#45546e;padding:11px 15px;border:1px solid #45546e;border-radius:8px;margin-right:8px}.bg-container[_ngcontent-%COMP%]   .add-btn[_ngcontent-%COMP%], .bg-container[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:700;cursor:pointer}.bg-container[_ngcontent-%COMP%]   .add-btn[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;width:80px;color:#fff;border-radius:8px;background:linear-gradient(270deg,var(--atsprimaryColor),var(--atssecondaryColor) 105.29%)}.bg-container[_ngcontent-%COMP%]   .tag-label[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:500;color:#6e7b8f}.bg-container[_ngcontent-%COMP%]   .tag-input[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{outline:none;width:60px;background:#fff}.bg-container[_ngcontent-%COMP%]   .tag-input[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .bg-container[_ngcontent-%COMP%]   .tag-search[_ngcontent-%COMP%]{border:1px solid #d2d2d2;border-radius:4px;cursor:pointer;height:40px}.bg-container[_ngcontent-%COMP%]   .tag-search[_ngcontent-%COMP%]{display:flex;flex-direction:row;align-items:center;width:100%;padding:4px 8px;gap:8px}.bg-container[_ngcontent-%COMP%]   .tag-search[_ngcontent-%COMP%]   .tag-search-name[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:500;color:#111434;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.bg-container[_ngcontent-%COMP%]   .tag-search[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{width:12px;height:12px;border-radius:50%}.bg-container[_ngcontent-%COMP%]   .color-circle[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:50%}.overlay-container[_ngcontent-%COMP%]{width:420px;min-height:75px;max-height:210px;background-color:#fff;border-radius:8px;border:1px solid #dadce2;padding:8px}.overlay-container[_ngcontent-%COMP%]   .checkbox[_ngcontent-%COMP%]{width:16px;height:16px;margin:0 8px 0 0}.overlay-container[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]{height:1px;width:100%;background:#e8e9ee;margin-bottom:8px;margin-top:8px}.overlay-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]{width:100%}.overlay-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{height:100%;width:95%;outline:none;border:none}.overlay-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .overlay-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{font-family:var(--atsfontFamily);font-size:11px;font-weight:400;color:#6e7b8f}.overlay-container[_ngcontent-%COMP%]   .list-view[_ngcontent-%COMP%]{overflow-y:auto;height:110px;gap:8px}.overlay-container[_ngcontent-%COMP%]   .list-view[_ngcontent-%COMP%]::-webkit-scrollbar{width:1px!important}.overlay-container[_ngcontent-%COMP%]   .list-view[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{min-height:40px!important}.overlay-container[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{width:12px;height:12px;border-radius:50%}.overlay-container[_ngcontent-%COMP%]   .checked-list[_ngcontent-%COMP%]{color:var(--atsprimaryColor)}.overlay-container[_ngcontent-%COMP%]   .checked-list[_ngcontent-%COMP%], .overlay-container[_ngcontent-%COMP%]   .list[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:400;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.overlay-container[_ngcontent-%COMP%]   .list[_ngcontent-%COMP%]{color:#5f6c81}.overlay-container[_ngcontent-%COMP%]   .svg[_ngcontent-%COMP%]{fill:var(--atsprimaryColor)}.overlay-container[_ngcontent-%COMP%]   .no-result-text[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;font-family:var(--atsfontFamily);font-size:14px;font-weight:400;color:#6e7b8f}  .white-spinner circle{stroke:#fff!important}"]}),t})()}}]);