(window.webpackJsonp=window.webpackJsonp||[]).push([[717],{"3HsR":function(e,t,n){"use strict";n.r(t),n.d(t,"FieldsLibraryModule",(function(){return lt}));var o=n("ofXK"),i=n("fXoL"),a=n("mrSG"),r=n("0IaG"),l=n("3Pt+"),c=n("bTqV"),d=n("NFeN"),m=n("kmnG"),s=n("qFsG"),p=n("QibW");function g(e,t){if(1&e&&i["\u0275\u0275element"](0,"mat-radio-button",21),2&e){const e=t.$implicit;i["\u0275\u0275styleProp"]("background",e),i["\u0275\u0275property"]("value",e)("disableRipple",!0)}}function u(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-radio-group",19),i["\u0275\u0275template"](1,g,1,4,"mat-radio-button",20),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngForOf",e.colorPalette)}}function h(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",23),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"](3).approverAddedWarning=!0})),i["\u0275\u0275elementStart"](1,"mat-icon"),i["\u0275\u0275text"](2,"person_add"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275text"](3," Add approver"),i["\u0275\u0275elementEnd"]()}}function f(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",22),i["\u0275\u0275elementStart"](1,"button",23),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"](2).addOption()})),i["\u0275\u0275elementStart"](2,"mat-icon"),i["\u0275\u0275text"](3,"add"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275text"](4," Add option"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](5,h,4,0,"button",24),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](5),i["\u0275\u0275property"]("ngIf",!e.approverAdded)}}function C(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",25),i["\u0275\u0275elementStart"](1,"span",26),i["\u0275\u0275text"](2),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](3,"button",27),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"](2).clearApprover()})),i["\u0275\u0275elementStart"](4,"mat-icon"),i["\u0275\u0275text"](5,"close"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](6,"div",28),i["\u0275\u0275elementStart"](7,"mat-icon",29),i["\u0275\u0275text"](8,"info"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](9,"span"),i["\u0275\u0275text"](10," Needs approver before moving to the next Option "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](11,"hr",30),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"](" ",e.approverName," ")}}function b(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div"),i["\u0275\u0275elementContainerStart"](1,9),i["\u0275\u0275elementStart"](2,"div",10),i["\u0275\u0275elementStart"](3,"div",11),i["\u0275\u0275elementStart"](4,"mat-form-field",12),i["\u0275\u0275element"](5,"input",13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](6,"button",14),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const n=t.index,o=i["\u0275\u0275nextContext"]();return o.toggleDisplay(o,n)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](7,u,2,1,"mat-radio-group",15),i["\u0275\u0275elementStart"](8,"button",16),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const n=t.index;return i["\u0275\u0275nextContext"]().removeOption(n)})),i["\u0275\u0275elementStart"](9,"mat-icon"),i["\u0275\u0275text"](10,"close"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](11,f,6,1,"div",17),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementContainerEnd"](),i["\u0275\u0275template"](12,C,12,1,"div",18),i["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=t.index,o=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("formGroupName",n),i["\u0275\u0275advance"](5),i["\u0275\u0275styleProp"]("background",e.value.color),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",o.selectedIndex==n),i["\u0275\u0275advance"](4),i["\u0275\u0275property"]("ngIf",n==o.dialogForm.get("options").controls.length-1),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",o.approverAdded&&0==n)}}function x(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",36),i["\u0275\u0275elementStart"](1,"mat-form-field",12),i["\u0275\u0275elementStart"](2,"input",37),i["\u0275\u0275listener"]("ngModelChange",(function(t){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"](2).approverName=t})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](3,"button",38),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"](2).addApprover()})),i["\u0275\u0275elementStart"](4,"mat-icon"),i["\u0275\u0275text"](5,"done"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](6,"button",38),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"](2).individualFlag=!1})),i["\u0275\u0275elementStart"](7,"mat-icon"),i["\u0275\u0275text"](8,"close"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngModel",e.approverName)}}function w(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",31),i["\u0275\u0275elementStart"](1,"div",32),i["\u0275\u0275elementStart"](2,"mat-icon",29),i["\u0275\u0275text"](3,"info"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275text"](4," Needs approver before moving to the next Option"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"button",33),i["\u0275\u0275text"](6,"From Role"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](7,"button",33),i["\u0275\u0275text"](8,"From Org"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](9,"button",34),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const t=i["\u0275\u0275nextContext"]();return t.individualFlag=!t.individualFlag})),i["\u0275\u0275text"](10,"Individual"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](11,x,9,1,"div",35),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](11),i["\u0275\u0275property"]("ngIf",e.individualFlag)}}let _=(()=>{class e{constructor(e,t){this.data=e,this.matDialogRef=t,this.formResponse=new i.EventEmitter,this.colorPalette=["red","blue","black","green","violet","pink","orange","salmon","darkblue","yellow"],this.individualFlag=!1,this.dialogForm=new l.m({approver:new l.j(""),options:new l.g([new l.m({name:new l.j(""),color:new l.j("red")})])}),this.approverAdded=!1,this.approverAddedWarning=!1}ngOnInit(){this.setExistingForm(this.data),console.log("at mat dialog",this.data)}setExistingForm(e){this.dialogForm.get("options").removeAt(0),0!=e.options.length?(e.approver&&(this.approverAdded=!0,this.approverName=e.approver,this.approverAddedWarning=!1),e.options.forEach(e=>{this.dialogForm.get("options").push(new l.m({name:new l.j(e.name),color:new l.j(e.color)}))})):this.dialogForm.get("options").push(new l.j(""))}addApprover(){this.approverName&&(this.dialogForm.controls.approver.setValue(this.approverName),this.approverAdded=!0,this.approverAddedWarning=!1)}clearApprover(){this.approverAdded=!1,this.approverAddedWarning=!0,this.approverName="",this.individualFlag=!1}addOption(){this.approverAdded?this.dialogForm.get("options").push(new l.m({name:new l.j(""),color:new l.j("red")})):this.approverAddedWarning=!0}removeOption(e){0!=e&&this.dialogForm.get("options").removeAt(e)}toggleDisplay(e,t){this.selectedIndex=t!=this.selectedIndex?t:null}onSave(){console.log("on exit",this.dialogForm.value),this.formResponse.emit(this.dialogForm.value),this.matDialogRef.close()}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](r.a),i["\u0275\u0275directiveInject"](r.h))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-dropdown-approver-dialog"]],outputs:{formResponse:"formResponse"},decls:15,vars:3,consts:[[1,"dialog-wrapper"],[1,"dialog-header"],["mat-icon-button","","mat-dialog-close",""],[1,"dialog-contents"],[3,"formGroup"],["formArrayName","options"],[4,"ngFor","ngForOf"],["class","add-approver",4,"ngIf"],["mat-icon-button","",1,"button-inactive","red",2,"float","right",3,"click"],[3,"formGroupName"],[1,"dialog-card","mb-3"],[1,"left-half"],["appearance","outline"],["matInput","","formControlName","name"],["mat-icon-button","","type","button",1,"ml-5",2,"width","20px","height","20px","margin","0px 3px",3,"click"],["formControlName","color",4,"ngIf"],["type","button","mat-icon-button","",1,"ml-5",3,"click"],["class","right-half",4,"ngIf"],["class","mt-3 mb-3",4,"ngIf"],["formControlName","color"],[3,"value","disableRipple","background",4,"ngFor","ngForOf"],[3,"value","disableRipple"],[1,"right-half"],["mat-flat-button","","type","button",3,"click"],["mat-flat-button","","type","button",3,"click",4,"ngIf"],[1,"mt-3","mb-3"],[2,"width","250px","display","inline-block"],["type","button","mat-icon-button","",1,"ml-3",3,"click"],[1,"approver-warning","mat-h3",2,"font-size","14px"],["color","warn"],[2,"text-align","left","width","50%","margin","0px"],[1,"add-approver"],[1,"approver-warning","mat-h3"],["mat-raised-button","",1,"mr-3"],["mat-raised-button","",1,"mr-3",3,"click"],["class","mt-2",4,"ngIf"],[1,"mt-2"],["matInput","",3,"ngModel","ngModelChange"],["mat-icon-button","",3,"click"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275elementStart"](1,"div",1),i["\u0275\u0275elementStart"](2,"span"),i["\u0275\u0275text"](3," Create own options in Dropdown - with approvers "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"button",2),i["\u0275\u0275elementStart"](5,"mat-icon"),i["\u0275\u0275text"](6,"close"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](7,"div",3),i["\u0275\u0275elementStart"](8,"form",4),i["\u0275\u0275elementStart"](9,"div",5),i["\u0275\u0275template"](10,b,13,6,"div",6),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](11,w,12,1,"div",7),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](12,"button",8),i["\u0275\u0275listener"]("click",(function(){return t.onSave()})),i["\u0275\u0275elementStart"](13,"mat-icon"),i["\u0275\u0275text"](14,"done_all"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](8),i["\u0275\u0275property"]("formGroup",t.dialogForm),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngForOf",t.dialogForm.get("options").controls),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.approverAddedWarning))},directives:[c.a,r.d,d.a,l.J,l.w,l.n,l.h,o.NgForOf,o.NgIf,l.o,m.c,s.b,l.e,l.v,l.l,p.b,p.a,l.y],styles:['.dialog-wrapper[_ngcontent-%COMP%]{min-width:950px;min-height:500px;width:80vw;padding:30px 50px 50px}.dialog-wrapper[_ngcontent-%COMP%]   .dialog-header[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-weight:500;font-size:16px}.dialog-wrapper[_ngcontent-%COMP%]   .dialog-header[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{float:right;margin-top:-7px}.dialog-wrapper[_ngcontent-%COMP%]   .dialog-contents[_ngcontent-%COMP%]{margin-top:30px;height:330px;overflow:scroll;-ms-overflow-style:none;scrollbar-width:none}.dialog-wrapper[_ngcontent-%COMP%]   .dialog-contents[_ngcontent-%COMP%]::-webkit-scrollbar{display:none}.dialog-wrapper[_ngcontent-%COMP%]   .dialog-contents[_ngcontent-%COMP%]   .dialog-card[_ngcontent-%COMP%]{display:flex;align-items:center}.dialog-wrapper[_ngcontent-%COMP%]   .dialog-contents[_ngcontent-%COMP%]   .dialog-card[_ngcontent-%COMP%]   .left-half[_ngcontent-%COMP%]{position:relative;min-width:50%;display:flex;align-items:center;border-right:1px solid #ccc}.dialog-wrapper[_ngcontent-%COMP%]   .dialog-contents[_ngcontent-%COMP%]   .dialog-card[_ngcontent-%COMP%]   .left-half[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{min-width:250px}.dialog-wrapper[_ngcontent-%COMP%]   .dialog-contents[_ngcontent-%COMP%]   .dialog-card[_ngcontent-%COMP%]   .left-half[_ngcontent-%COMP%]   mat-radio-group[_ngcontent-%COMP%]{z-index:1;background:#fff;padding:3px;width:158px;position:absolute;left:230px;top:50px;box-shadow:0 0 2px 1px #ccc;display:flex;flex-wrap:wrap}.dialog-wrapper[_ngcontent-%COMP%]   .dialog-contents[_ngcontent-%COMP%]   .dialog-card[_ngcontent-%COMP%]   .left-half[_ngcontent-%COMP%]   mat-radio-group[_ngcontent-%COMP%]:before{content:"";display:block;width:0;height:0;position:absolute;border-bottom:8px solid #000;border-right:8px solid transparent;border-left:8px solid transparent;left:70px;top:-10px}.dialog-wrapper[_ngcontent-%COMP%]   .dialog-contents[_ngcontent-%COMP%]   .dialog-card[_ngcontent-%COMP%]   .left-half[_ngcontent-%COMP%]   mat-radio-group[_ngcontent-%COMP%]   mat-radio-button[_ngcontent-%COMP%]{border-radius:50%;width:20px;height:20px;margin:5px}.dialog-wrapper[_ngcontent-%COMP%]   .dialog-contents[_ngcontent-%COMP%]   .dialog-card[_ngcontent-%COMP%]   .left-half[_ngcontent-%COMP%]   mat-radio-group[_ngcontent-%COMP%]   mat-radio-button[_ngcontent-%COMP%]     .mat-radio-container .mat-radio-inner-circle, .dialog-wrapper[_ngcontent-%COMP%]   .dialog-contents[_ngcontent-%COMP%]   .dialog-card[_ngcontent-%COMP%]   .left-half[_ngcontent-%COMP%]   mat-radio-group[_ngcontent-%COMP%]   mat-radio-button[_ngcontent-%COMP%]     .mat-radio-container .mat-radio-outer-circle{border:none;width:0}.dialog-wrapper[_ngcontent-%COMP%]   .dialog-contents[_ngcontent-%COMP%]   .dialog-card[_ngcontent-%COMP%]   .left-half[_ngcontent-%COMP%]   mat-radio-group[_ngcontent-%COMP%]   mat-radio-button[_ngcontent-%COMP%]     .mat-radio-container{pointer-events:none}.dialog-wrapper[_ngcontent-%COMP%]   .dialog-contents[_ngcontent-%COMP%]   .dialog-card[_ngcontent-%COMP%]   .left-half[_ngcontent-%COMP%]   mat-radio-group[_ngcontent-%COMP%]   mat-radio-button[_ngcontent-%COMP%]:hover{background:#fff}.dialog-wrapper[_ngcontent-%COMP%]   .dialog-contents[_ngcontent-%COMP%]   .dialog-card[_ngcontent-%COMP%]   .right-half[_ngcontent-%COMP%]{padding-left:40px}.dialog-wrapper[_ngcontent-%COMP%]   .dialog-contents[_ngcontent-%COMP%]   .dialog-card[_ngcontent-%COMP%]   .right-half[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{margin-right:20px}.dialog-wrapper[_ngcontent-%COMP%]   .dialog-contents[_ngcontent-%COMP%]   .approver-warning[_ngcontent-%COMP%]{font-weight:500;font-size:15px}.dialog-wrapper[_ngcontent-%COMP%]   .button-inactive[_ngcontent-%COMP%]{line-height:8px;width:33px;height:33px;margin-right:12px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.dialog-wrapper[_ngcontent-%COMP%]   .button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:16px}.dialog-wrapper[_ngcontent-%COMP%]   .red[_ngcontent-%COMP%]{background:#d41111;color:#fff;font-size:16px}']}),e})();var F=n("quSY"),v=n("l9wN"),O=n("w2pn"),M=n("bSwM"),y=n("qjZ2");function S(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div"),i["\u0275\u0275elementStart"](1,"app-input-search",39),i["\u0275\u0275listener"]("change",(function(){i["\u0275\u0275restoreView"](e);const t=i["\u0275\u0275nextContext"]();return t.getTableFieldsMasterData(t.getValueFn("masterData"))})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("list",e.masterDataList)}}function P(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div"),i["\u0275\u0275elementStart"](1,"div",40),i["\u0275\u0275elementStart"](2,"mat-icon"),i["\u0275\u0275text"](3,"error"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"div"),i["\u0275\u0275text"](5," Choose key-value fields "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](6,"app-input-search",41),i["\u0275\u0275element"](7,"app-input-search",42),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](6),i["\u0275\u0275property"]("list",e.masterTableFieldsList),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("list",e.masterTableFieldsList)}}function E(e,t){if(1&e&&i["\u0275\u0275element"](0,"mat-radio-button",57),2&e){const e=t.$implicit;i["\u0275\u0275styleProp"]("background",e),i["\u0275\u0275property"]("value",e)("disableRipple",!0)}}function k(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-radio-group",55),i["\u0275\u0275template"](1,E,1,4,"mat-radio-button",56),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](3);i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngForOf",e.colorPalette)}}function j(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",58),i["\u0275\u0275elementStart"](1,"span"),i["\u0275\u0275text"](2,"needs "),i["\u0275\u0275elementStart"](3,"span",59),i["\u0275\u0275text"](4,"approval"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275text"](5),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"](3);i["\u0275\u0275advance"](5),i["\u0275\u0275textInterpolate1"](" from ",e.dropdownForm.get("approver").value,"")}}function N(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div"),i["\u0275\u0275elementContainerStart"](1,46),i["\u0275\u0275elementStart"](2,"mat-form-field",5),i["\u0275\u0275element"](3,"input",47),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"button",48),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const n=t.index,o=i["\u0275\u0275nextContext"](2);return o.toggleDisplay(o,n)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](5,k,2,1,"mat-radio-group",49),i["\u0275\u0275elementStart"](6,"button",50),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const n=t.index;return i["\u0275\u0275nextContext"](2).removeOption(n)})),i["\u0275\u0275elementStart"](7,"mat-icon",51),i["\u0275\u0275text"](8,"close"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](9,"div",52),i["\u0275\u0275elementStart"](10,"mat-form-field",5),i["\u0275\u0275elementStart"](11,"mat-label"),i["\u0275\u0275text"](12,"Weightage"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](13,"input",53),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementContainerEnd"](),i["\u0275\u0275template"](14,j,6,1,"div",54),i["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=t.index,o=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("formGroupName",n),i["\u0275\u0275advance"](3),i["\u0275\u0275styleProp"]("background",e.value.color),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",o.selectedIndex==n),i["\u0275\u0275advance"](9),i["\u0275\u0275property"]("ngIf",0==n&&o.approverAdded)}}function I(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",43),i["\u0275\u0275template"](1,N,15,5,"div",44),i["\u0275\u0275elementStart"](2,"button",45),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"]().addOption()})),i["\u0275\u0275elementStart"](3,"mat-icon"),i["\u0275\u0275text"](4,"add"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275text"](5," Add option "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngForOf",e.dropdownForm.get("options").controls)}}function A(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",60),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"]().openApiConfigModal()})),i["\u0275\u0275text"](1," Api Config "),i["\u0275\u0275elementEnd"]()}}function V(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"mat-checkbox",61),i["\u0275\u0275text"](1,"Status Field"),i["\u0275\u0275elementEnd"]())}function D(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"mat-checkbox",62),i["\u0275\u0275text"](1,"Add Sub form from Master data"),i["\u0275\u0275elementEnd"]())}function T(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",60),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"]().openSubformConfigModal()})),i["\u0275\u0275text"](1," Sub form Config "),i["\u0275\u0275elementEnd"]()}}function q(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"mat-checkbox",63),i["\u0275\u0275text"](1,"Attach feedback form for Master data"),i["\u0275\u0275elementEnd"]())}function z(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",60),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"]().openFeedbackformConfigModal()})),i["\u0275\u0275text"](1," Feedback form Config "),i["\u0275\u0275elementEnd"]()}}function L(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",60),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"]().openFieldControlConfigModal()})),i["\u0275\u0275text"](1," Field Control Config "),i["\u0275\u0275elementEnd"]()}}let U=(()=>{class e{constructor(e,t,n){this._CustomFieldService=e,this._FieldsLibraryService=t,this.dialog=n,this.closeComponent=new i.EventEmitter,this.masterDataList=[],this.masterTableFieldsList=[],this.masterTableKeyFieldsList=[],this.masterTableValueFieldsList=[],this.newOptions=[],this.option=new l.g([]),this.colorPalette=["red","blue","black","green","violet","pink","orange","salmon","darkblue","yellow"],this.approverAdded=!1,this.msg=!1,this.isFeedbackForm=!1,this.valueChangeSubscription=new F.a}ngOnInit(){this.formFieldData=this._FieldsLibraryService.getFormFieldData(),this.dropdownForm=new l.m({fieldName:new l.j("",l.H.required),fieldType:new l.j(this.field.label),fieldId:new l.j(this.field.id),fieldIcon:new l.j(this.field.icon),rows:new l.j(this.field.default_rows),cols:new l.j(this.field.default_columns),chooseFromMasterData:new l.j(!0),masterData:new l.j("",l.H.required),masterDataKeyField:new l.j("",l.H.required),masterDataValueField:new l.j("",l.H.required),chooseOwnOptions:new l.j(!1),options:new l.g([new l.m({name:new l.j(""),color:new l.j("red"),weightage:new l.j("")})]),allowMultiSelect:new l.j(!1),allowSubForm:new l.j(!1),mandatoryField:new l.j(!1),hasSummaryCard:new l.j(!1),isStatusField:new l.j(!1),isWorkflowElement:new l.j(!1),udrfColumnData:new l.j(this.field.udrf_column_data),enableActivity:new l.j(!1),enableNotification:new l.j(!1),enableInlineEdit:new l.j(!1),editableOnCreation:new l.j(!0),editableOnUpdation:new l.j(!0),isTaskBasedStatus:new l.j(!1),uniqueField:new l.j(!1),allowFeedbackForm:new l.j(!1),valueFromApi:new l.j(!1),authorizeFieldValue:new l.j(!1),allowFieldControl:new l.j(!1),isOrgDropdown:new l.j(!1),isCCDropdown:new l.j(!1),isProjectDropdown:new l.j(!1)}),this.valueChangeSubscription.add(this.dropdownForm.get("enableNotification").valueChanges.subscribe(e=>{e&&this.dropdownForm.get("enableActivity").patchValue(!0)})),this.valueChangeSubscription.add(this.dropdownForm.get("enableActivity").valueChanges.subscribe(e=>{e||this.dropdownForm.get("enableNotification").patchValue(!1)})),Object.keys(this.formFieldData).length>0&&this.patchvalues(this.formFieldData),this.dropdownForm.get("chooseFromMasterData").value&&this.getMasterData(),this.dropdownForm.get("masterData").value.length>0&&this.getTableFieldsMasterData(this.dropdownForm.get("masterData").value),this.dropdownForm.get("chooseFromMasterData").value||(this.removeRequiredValidator("masterData"),this.removeRequiredValidator("masterDataKeyField"),this.removeRequiredValidator("masterDataValueField")),this._CustomFieldService.isFeedbackForm&&(this.isFeedbackForm=this._CustomFieldService.isFeedbackForm)}patchvalues(e){this.dropdownForm.addControl("x",new l.j("")),this.dropdownForm.addControl("y",new l.j("")),e._id&&this.dropdownForm.addControl("_id",new l.j(e._id)),e.mysql_column_name&&this.dropdownForm.addControl("mysql_column_name",new l.j(e.mysql_column_name)),this.newOptions=e.chooseOwnOptions,this.dropdownForm.patchValue({fieldName:e.fieldName,fieldType:e.fieldType,fieldId:e.fieldId,fieldIcon:e.fieldIcon,rows:e.rows,cols:e.cols,x:e.x,y:e.y,chooseFromMasterData:e.chooseFromMasterData,masterData:e.masterData,masterDataKeyField:e.masterDataKeyField,masterDataValueField:e.masterDataValueField,chooseOwnOptions:e.chooseOwnOptions,options:e.options,allowMultiSelect:e.allowMultiSelect,approver:e.approver,mandatoryField:e.mandatoryField,hasSummaryCard:e.hasSummaryCard,isStatusField:e.isStatusField,isWorkflowElement:!!e.isWorkflowElement&&e.isWorkflowElement,allowSubForm:e.allowSubForm,udrfColumnData:e.udrfColumnData,enableActivity:!!e.enableActivity&&e.enableActivity,enableNotification:!!e.enableNotification&&e.enableNotification,enableInlineEdit:!!e.enableInlineEdit&&e.enableInlineEdit,editableOnCreation:null==e.editableOnCreation||e.editableOnCreation,editableOnUpdation:null==e.editableOnUpdation||e.editableOnUpdation,uniqueField:e.uniqueField,isTaskBasedStatus:e.isTaskBasedStatus||!1,allowFeedbackForm:e.allowFeedbackForm,valueFromApi:e.valueFromApi,authorizeFieldValue:e.authorizeFieldValue,allowFieldControl:e.allowFieldControl,isOrgDropdown:e.isOrgDropdown||!1,isCCDropdown:e.isCCDropdown||!1,isProjectDropdown:e.isProjectDropdown||!1}),e.masterDataFormMapping&&(this.dropdownForm.addControl("masterDataFormMapping",new l.j(null)),this.dropdownForm.get("masterDataFormMapping").patchValue(e.masterDataFormMapping)),e.feedbackFormMapping&&(this.dropdownForm.addControl("feedbackFormMapping",new l.j(null)),this.dropdownForm.get("feedbackFormMapping").patchValue(e.feedbackFormMapping)),e.valueFromApi&&(this.dropdownForm.addControl("apiConfig",new l.j(null)),this.dropdownForm.get("apiConfig").patchValue(e.apiConfig)),e.allowFieldControl&&(this.dropdownForm.addControl("fieldControlMapping",new l.j(null)),this.dropdownForm.get("fieldControlMapping").patchValue(e.fieldControlMapping)),this.newOptions&&(this.dropdownForm.get("options").removeAt(0),e.options.forEach(e=>{this.dropdownForm.get("options").push(new l.m({name:new l.j(e.name),color:new l.j(e.color),weightage:new l.j(null==e?void 0:e.weightage)}))})),this.options(e)}options(e){console.log(e.options)}openDialog(){console.log(this.dropdownForm.controls.options.value);const e=this.dialog.open(_,{data:{options:this.dropdownForm.controls.options.value,approver:this.dropdownForm.controls.approver.value}}),t=e.componentInstance.formResponse.subscribe(e=>{console.log("At drpdwn",e),this.removeAllOptions(),this.setExistingForm(e),e.approver&&(this.approverAdded=!0)});e.afterClosed().subscribe(e=>{t.unsubscribe()})}setExistingForm(e){this.dropdownForm.controls.approver.setValue(e.approver),e.options.forEach(e=>{this.dropdownForm.get("options").push(new l.m({name:new l.j(e.name),color:new l.j(e.color),weightage:new l.j(null==e?void 0:e.weightage)}))})}addOption(){this.dropdownForm.get("options").push(new l.m({name:new l.j(""),color:new l.j("red"),weightage:new l.j("")}))}removeAllOptions(){let e=this.dropdownForm.get("options").length-1;for(;e-- >=0;)this.removeOption(e)}removeOption(e){this.dropdownForm.get("options").removeAt(e)}toggleDisplay(e,t){this.selectedIndex=t!=this.selectedIndex?t:null}onSubmit(){if(this.dropdownForm.invalid)return"Kindly fill all mandatory fields";this.dropdownForm.get("chooseOwnOptions").value||this.removeAllOptions(),this.dropdownForm.get("chooseFromMasterData").value||(this.dropdownForm.controls.masterData.setValue(""),this.dropdownForm.controls.masterDataKeyField.setValue(""),this.dropdownForm.controls.masterDataValueField.setValue("")),this._CustomFieldService.addField(this.dropdownForm.value),this.closeComponent.emit(!0)}close(){this.msg=!1,this._CustomFieldService.editCheck(this.msg),this.closeComponent.emit(!0)}getMasterData(){this._FieldsLibraryService.getDropDownMasterData().subscribe(e=>{this.masterDataList=e})}getTableFieldsMasterData(e){this._FieldsLibraryService.getMasterTableFieldsData(e).subscribe(e=>{this.masterTableFieldsList=e})}addRequiredValidator(e){this.dropdownForm.controls[e].setValidators([l.H.required]),this.dropdownForm.controls[e].updateValueAndValidity()}removeRequiredValidator(e){this.dropdownForm.controls[e].clearValidators(),this.dropdownForm.controls[e].updateValueAndValidity()}setValueFn(e,t){this.dropdownForm.controls[e].setValue(t)}getValueFn(e){return this.dropdownForm.get(e).value}toggleSelectOptions(e,t){switch(e){case"chooseFromMasterData":t?this.getMasterData():this.setValueFn("chooseFromMasterData",!0),this.setValueFn("chooseOwnOptions",!1),this.addRequiredValidator("masterData"),this.addRequiredValidator("masterDataKeyField"),this.addRequiredValidator("masterDataValueField");break;case"chooseOwnOptions":t||this.setValueFn("chooseOwnOptions",!0),this.setValueFn("chooseFromMasterData",!1),this.removeRequiredValidator("masterData"),this.removeRequiredValidator("masterDataKeyField"),this.removeRequiredValidator("masterDataValueField"),0==this.dropdownForm.get("options").length&&this.addOption()}}openSubformConfigModal(){return Object(a.c)(this,void 0,void 0,(function*(){let e={dropdownData:this.dropdownForm.value};const{ValueFormMappingComponent:t}=yield n.e(326).then(n.bind(null,"3yJQ"));this.dialog.open(t,{height:"70%",width:"70%",data:{modalParams:e}}).afterClosed().subscribe(e=>{e&&"submit"==e.event&&(this.dropdownForm.addControl("masterDataFormMapping",new l.j(null)),this.dropdownForm.get("masterDataFormMapping").patchValue(e.data))})}))}openFeedbackformConfigModal(){return Object(a.c)(this,void 0,void 0,(function*(){let e={dropdownData:this.dropdownForm.value};const{FeedbackFormMappingComponent:t}=yield n.e(282).then(n.bind(null,"jOh4"));this.dialog.open(t,{height:"70%",width:"70%",data:{modalParams:e}}).afterClosed().subscribe(e=>{e&&"submit"==e.event&&(this.dropdownForm.addControl("feedbackFormMapping",new l.j(null)),this.dropdownForm.get("feedbackFormMapping").patchValue(e.data))})}))}toggleValueFromApi(e){e.checked&&!this.dropdownForm.contains("apiConfig")?this.dropdownForm.addControl("apiConfig",new l.j(null,l.H.required)):e.checked||this.dropdownForm.removeControl("apiConfig")}openApiConfigModal(){return Object(a.c)(this,void 0,void 0,(function*(){this._FieldsLibraryService.openApiConfigModal(this.dropdownForm)}))}toggleAllowFieldControl(e){e.checked&&!this.dropdownForm.contains("fieldControlMapping")?this.dropdownForm.addControl("fieldControlMapping",new l.j(null,l.H.required)):e.checked||this.dropdownForm.removeControl("fieldControlMapping")}openFieldControlConfigModal(){return Object(a.c)(this,void 0,void 0,(function*(){this._FieldsLibraryService.openFieldControlConfigModal(this.dropdownForm)}))}ngOnDestroy(){this.valueChangeSubscription&&this.valueChangeSubscription.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](v.a),i["\u0275\u0275directiveInject"](O.a),i["\u0275\u0275directiveInject"](r.b))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-dropdown"]],inputs:{field:"field"},outputs:{closeComponent:"closeComponent"},decls:78,vars:11,consts:[[1,"card-wrapper"],[1,"card-heading"],["mat-icon-button","",3,"click"],[1,"card-contents"],[3,"formGroup","ngSubmit"],["appearance","outline"],["matInput","","formControlName","fieldName"],["matInput","","formControlName","fieldType"],["formControlName","chooseFromMasterData",3,"change"],[4,"ngIf"],["formControlName","chooseOwnOptions",3,"change"],["class","new-options","formArrayName","options",4,"ngIf"],["formControlName","valueFromApi",3,"change"],[1,"d-flex","justify-content-center"],["type","button","mat-raised-button","","class","ml-2 btn-class",3,"click",4,"ngIf"],["formControlName","allowMultiSelect"],["formControlName","mandatoryField"],["formControlName","enableActivity"],["formControlName","enableNotification"],["formControlName","enableInlineEdit"],["formControlName","hasSummaryCard"],["formControlName","isWorkflowElement"],["formControlName","isTaskBasedStatus"],["formControlName","isStatusField",4,"ngIf"],["formControlName","editableOnCreation"],["formControlName","editableOnUpdation"],["formControlName","isOrgDropdown"],["formControlName","isCCDropdown"],["formControlName","isProjectDropdown"],["formControlName","uniqueField"],["formControlName","authorizeFieldValue"],["formControlName","allowSubForm",4,"ngIf"],[1,"d-flex","justify-content-center","m-2"],["formControlName","allowFeedbackForm",4,"ngIf"],["formControlName","allowFieldControl",3,"change"],["mat-icon-button","","type","submit",1,"button-inactive","red"],[2,"font-size","16px"],["mat-icon-button","","type","button",1,"button-inactive"],[1,"iconButton",3,"click"],["placeholder","Table","formControlName","masterData",3,"list","change"],[1,"mt-2","mb-2",2,"display","flex","align-items","center"],["placeholder","Key field","formControlName","masterDataKeyField",3,"list"],["placeholder","Value field","formControlName","masterDataValueField",3,"list"],["formArrayName","options",1,"new-options"],[4,"ngFor","ngForOf"],["type","button",1,"button-add",3,"click"],[3,"formGroupName"],["matInput","","formControlName","name"],["mat-icon-button","","type","button",2,"width","16px","height","16px","margin","0px 5px 0px","vertical-align","middle",3,"click"],["formControlName","color",4,"ngIf"],["type","button","mat-icon-button","",3,"click"],[2,"font-size","18px"],[1,"row"],["matInput","","type","number","formControlName","weightage"],["style","font-size:13px;","class","mt-2 mb-2",4,"ngIf"],["formControlName","color"],[3,"value","disableRipple","background",4,"ngFor","ngForOf"],[3,"value","disableRipple"],[1,"mt-2","mb-2",2,"font-size","13px"],[2,"color","rgb(161, 10, 10)"],["type","button","mat-raised-button","",1,"ml-2","btn-class",3,"click"],["formControlName","isStatusField"],["formControlName","allowSubForm"],["formControlName","allowFeedbackForm"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275elementStart"](1,"div",1),i["\u0275\u0275elementStart"](2,"button",2),i["\u0275\u0275listener"]("click",(function(){return t.close()})),i["\u0275\u0275elementStart"](3,"mat-icon"),i["\u0275\u0275text"](4,"keyboard_backspace"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"span"),i["\u0275\u0275text"](6,"Dropdown"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](7,"div",3),i["\u0275\u0275elementStart"](8,"form",4),i["\u0275\u0275listener"]("ngSubmit",(function(){return t.onSubmit()})),i["\u0275\u0275elementStart"](9,"mat-form-field",5),i["\u0275\u0275elementStart"](10,"mat-label"),i["\u0275\u0275text"](11,"Field name"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](12,"input",6),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](13,"mat-form-field",5),i["\u0275\u0275elementStart"](14,"mat-label"),i["\u0275\u0275text"](15,"Field type"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](16,"input",7),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](17,"section"),i["\u0275\u0275elementStart"](18,"mat-checkbox",8),i["\u0275\u0275listener"]("change",(function(){return t.toggleSelectOptions("chooseFromMasterData",t.getValueFn("chooseFromMasterData"))})),i["\u0275\u0275text"](19,"Choose from master data"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](20,S,2,1,"div",9),i["\u0275\u0275template"](21,P,8,2,"div",9),i["\u0275\u0275elementStart"](22,"mat-checkbox",10),i["\u0275\u0275listener"]("change",(function(){return t.toggleSelectOptions("chooseOwnOptions",t.getValueFn("chooseOwnOptions"))})),i["\u0275\u0275text"](23,"Create new options"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](24,I,6,1,"div",11),i["\u0275\u0275elementStart"](25,"mat-checkbox",12),i["\u0275\u0275listener"]("change",(function(e){return t.toggleValueFromApi(e)})),i["\u0275\u0275text"](26,"Get Value from external api"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](27,"div",13),i["\u0275\u0275template"](28,A,2,0,"button",14),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](29,"mat-checkbox",15),i["\u0275\u0275text"](30,"Allow multi select"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](31,"mat-checkbox",16),i["\u0275\u0275text"](32,"Mandatory field "),i["\u0275\u0275elementStart"](33,"sup"),i["\u0275\u0275text"](34,"*"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](35,"mat-checkbox",17),i["\u0275\u0275text"](36,"Enable Activity for field "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](37,"mat-checkbox",18),i["\u0275\u0275text"](38,"Enable Notification for field "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](39,"mat-checkbox",19),i["\u0275\u0275text"](40,"Enable Inline Edit for field "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](41,"mat-checkbox",20),i["\u0275\u0275text"](42,"Has Summary card"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](43,"mat-checkbox",21),i["\u0275\u0275text"](44,"Is Workflow Element"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](45,"mat-checkbox",22),i["\u0275\u0275text"](46,"Based on task Status"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](47,V,2,0,"mat-checkbox",23),i["\u0275\u0275elementStart"](48,"mat-checkbox",24),i["\u0275\u0275text"](49,"Edit allowed on Creation"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](50,"mat-checkbox",25),i["\u0275\u0275text"](51,"Edit allowed on Updation"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](52,"mat-checkbox",26),i["\u0275\u0275text"](53,"Organization Dropdown"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](54,"mat-checkbox",27),i["\u0275\u0275text"](55,"Cost center Dropdown"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](56,"mat-checkbox",28),i["\u0275\u0275text"](57,"Project Dropdown"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](58,"mat-checkbox",29),i["\u0275\u0275text"](59,"Unique Field"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](60,"mat-checkbox",30),i["\u0275\u0275text"](61,"Use Field Value Authorization"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](62,D,2,0,"mat-checkbox",31),i["\u0275\u0275elementStart"](63,"div",32),i["\u0275\u0275template"](64,T,2,0,"button",14),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](65,q,2,0,"mat-checkbox",33),i["\u0275\u0275elementStart"](66,"div",13),i["\u0275\u0275template"](67,z,2,0,"button",14),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](68,"mat-checkbox",34),i["\u0275\u0275listener"]("change",(function(e){return t.toggleAllowFieldControl(e)})),i["\u0275\u0275text"](69,"Enable Field Control (based on condition)"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](70,"div",13),i["\u0275\u0275template"](71,L,2,0,"button",14),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](72,"button",35),i["\u0275\u0275elementStart"](73,"mat-icon",36),i["\u0275\u0275text"](74,"done_all"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](75,"button",37),i["\u0275\u0275elementStart"](76,"mat-icon",38),i["\u0275\u0275listener"]("click",(function(){return t.close()})),i["\u0275\u0275text"](77,"close"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](8),i["\u0275\u0275property"]("formGroup",t.dropdownForm),i["\u0275\u0275advance"](12),i["\u0275\u0275property"]("ngIf",t.getValueFn("chooseFromMasterData")),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.getValueFn("chooseFromMasterData")&&t.getValueFn("masterData").length>0),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngIf",t.getValueFn("chooseOwnOptions")),i["\u0275\u0275advance"](4),i["\u0275\u0275property"]("ngIf",t.dropdownForm.get("valueFromApi").value),i["\u0275\u0275advance"](19),i["\u0275\u0275property"]("ngIf",t.dropdownForm.get("chooseFromMasterData").value&&t.dropdownForm.get("masterData").value&&t.dropdownForm.get("masterDataKeyField").value&&t.dropdownForm.get("masterDataValueField").value),i["\u0275\u0275advance"](15),i["\u0275\u0275property"]("ngIf",t.dropdownForm.get("chooseFromMasterData").value&&t.dropdownForm.get("masterData").value&&t.dropdownForm.get("masterDataKeyField").value&&t.dropdownForm.get("masterDataValueField").value),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf",t.dropdownForm.get("allowSubForm").value),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.dropdownForm.get("chooseFromMasterData").value&&t.dropdownForm.get("masterData").value&&t.dropdownForm.get("masterDataKeyField").value&&t.dropdownForm.get("masterDataValueField").value&&!t.isFeedbackForm),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf",t.dropdownForm.get("allowFeedbackForm").value),i["\u0275\u0275advance"](4),i["\u0275\u0275property"]("ngIf",t.dropdownForm.get("allowFieldControl").value))},directives:[c.a,d.a,l.J,l.w,l.n,m.c,m.g,s.b,l.e,l.v,l.l,M.a,o.NgIf,y.a,l.h,o.NgForOf,l.o,l.A,p.b,p.a],styles:['.card-wrapper[_ngcontent-%COMP%]{box-shadow:0 0 3px 0 #ccc;border-radius:3px;margin:5px 0 20px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]{padding-top:5px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-top:-4px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-weight:500;font-size:17px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]{min-width:220px;margin:10px 18px;padding-bottom:20px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{width:100%;font-size:12px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .button-inactive[_ngcontent-%COMP%]{line-height:8px;width:26px;height:26px;margin-right:12px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:16px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .red[_ngcontent-%COMP%]{background:#d41111;color:#fff;font-size:16px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]{margin:10px 0}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]{display:block}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]     .mat-checkbox-inner-container{margin-right:14px;height:15px;width:15px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]     .mat-checkbox-label{font-size:13px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]   sup[_ngcontent-%COMP%]{color:red;font-size:1em;top:-.1em}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .new-options[_ngcontent-%COMP%]{position:relative}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .new-options[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{font-size:11px;width:70%}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .new-options[_ngcontent-%COMP%]   .button-add[_ngcontent-%COMP%]{background:none;border:none;width:99px;align-items:middle;font-size:12px;padding:0}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .new-options[_ngcontent-%COMP%]   .button-add[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:12px;margin-top:-2px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .new-options[_ngcontent-%COMP%]   mat-radio-group[_ngcontent-%COMP%]{z-index:1;background:#fff;padding:3px;width:158px;position:absolute;right:-16px;box-shadow:0 0 2px 1px #ccc;display:flex;flex-wrap:wrap}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .new-options[_ngcontent-%COMP%]   mat-radio-group[_ngcontent-%COMP%]:before{content:"";display:block;width:0;height:0;position:absolute;border-bottom:8px solid #000;border-right:8px solid transparent;border-left:8px solid transparent;left:80px;top:-10px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .new-options[_ngcontent-%COMP%]   mat-radio-group[_ngcontent-%COMP%]   mat-radio-button[_ngcontent-%COMP%]{border-radius:50%;width:20px;height:20px;margin:5px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .new-options[_ngcontent-%COMP%]   mat-radio-group[_ngcontent-%COMP%]   mat-radio-button[_ngcontent-%COMP%]     .mat-radio-container .mat-radio-inner-circle, .card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .new-options[_ngcontent-%COMP%]   mat-radio-group[_ngcontent-%COMP%]   mat-radio-button[_ngcontent-%COMP%]     .mat-radio-container .mat-radio-outer-circle{border:none;width:0}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .new-options[_ngcontent-%COMP%]   mat-radio-group[_ngcontent-%COMP%]   mat-radio-button[_ngcontent-%COMP%]     .mat-radio-container{pointer-events:none}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   section[_ngcontent-%COMP%]   .new-options[_ngcontent-%COMP%]   mat-radio-group[_ngcontent-%COMP%]   mat-radio-button[_ngcontent-%COMP%]:hover{background:#fff}.card-wrapper[_ngcontent-%COMP%]   .btn-class[_ngcontent-%COMP%]{margin-right:6px!important;margin-bottom:3px;color:rgba(0,0,0,.534);background-color:rgba(194,193,193,.24);font-weight:400;font-size:13px!important;min-width:60px;line-height:33px;padding:0 8px;border-radius:4px}']}),e})();function G(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",23),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"]().maxcount=!0})),i["\u0275\u0275elementStart"](1,"mat-icon",20),i["\u0275\u0275text"](2,"edit"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}}function R(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",23),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"]().maxcount=!1})),i["\u0275\u0275elementStart"](1,"mat-icon",20),i["\u0275\u0275text"](2,"close"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}}function H(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div"),i["\u0275\u0275elementStart"](1,"mat-form-field",5),i["\u0275\u0275elementStart"](2,"mat-label"),i["\u0275\u0275text"](3,"Max Characters"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](4,"input",24),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]())}let B=(()=>{class e{constructor(e,t){this._FieldsLibraryService=e,this._CustomFieldService=t,this.closeComponent=new i.EventEmitter,this.maxcount=!1,this.msg=!1,this.valueChangeSubscription=new F.a}ngOnInit(){this.formFieldData=this._FieldsLibraryService.getFormFieldData(),this.longTextForm=new l.m({fieldName:new l.j("",l.H.required),fieldType:new l.j(this.field.label),fieldId:new l.j(this.field.id),fieldIcon:new l.j(this.field.icon),rows:new l.j(this.field.default_rows),cols:new l.j(this.field.default_columns),maxCount:new l.j("200"),mandatoryField:new l.j(""),udrfColumnData:new l.j(this.field.udrf_column_data),enableActivity:new l.j(!1),enableNotification:new l.j(!1),editableOnCreation:new l.j(!0),editableOnUpdation:new l.j(!0),uniqueField:new l.j(!1)}),this.valueChangeSubscription.add(this.longTextForm.get("enableNotification").valueChanges.subscribe(e=>{e&&this.longTextForm.get("enableActivity").patchValue(!0)})),this.valueChangeSubscription.add(this.longTextForm.get("enableActivity").valueChanges.subscribe(e=>{e||this.longTextForm.get("enableNotification").patchValue(!1)})),Object.keys(this.formFieldData).length>0&&this.patchvalues(this.formFieldData)}patchvalues(e){this.longTextForm.addControl("x",new l.j("")),this.longTextForm.addControl("y",new l.j("")),e._id&&this.longTextForm.addControl("_id",new l.j(e._id)),e.mysql_column_name&&this.longTextForm.addControl("mysql_column_name",new l.j(e.mysql_column_name)),this.longTextForm.patchValue({fieldName:e.fieldName,fieldType:e.fieldType,fieldId:e.fieldId,fieldIcon:e.fieldIcon,rows:e.rows,cols:e.cols,x:e.x,y:e.y,maxCount:e.maxCount,mandatoryField:e.mandatoryField,udrfColumnData:e.udrfColumnData,enableActivity:!!e.enableActivity&&e.enableActivity,enableNotification:!!e.enableNotification&&e.enableNotification,editableOnCreation:null==e.editableOnCreation||e.editableOnCreation,editableOnUpdation:null==e.editableOnUpdation||e.editableOnUpdation,uniqueField:e.uniqueField})}onSubmit(){this.longTextForm.invalid||(console.log(this.longTextForm.value),this._CustomFieldService.addField(this.longTextForm.value),this.closeComponent.emit(!0))}close(){this.msg=!1,this._CustomFieldService.editCheck(this.msg),this.closeComponent.emit(!0)}ngOnDestroy(){this.valueChangeSubscription&&this.valueChangeSubscription.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](O.a),i["\u0275\u0275directiveInject"](v.a))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-long-text"]],inputs:{field:"field"},outputs:{closeComponent:"closeComponent"},decls:46,vars:4,consts:[[1,"card-wrapper"],[1,"card-heading"],["mat-icon-button","",3,"click"],[1,"card-contents"],[3,"formGroup","ngSubmit"],["appearance","outline"],["matInput","","formControlName","fieldName"],["matInput","","formControlName","fieldType","readonly",""],[1,"extra_fields","mb-2"],[1,"mt-2","mb-2",2,"display","flex","align-items","center"],[2,"color","#cf0001","font-size","18px"],["mat-icon-button","","class","mb-1",3,"click",4,"ngIf"],[4,"ngIf"],["formControlName","mandatoryField"],["formControlName","enableActivity"],["formControlName","enableNotification"],["formControlName","editableOnCreation"],["formControlName","editableOnUpdation"],["formControlName","uniqueField"],["mat-icon-button","","type","submit",1,"button-inactive","red"],[2,"font-size","16px"],["mat-icon-button","",1,"button-inactive"],[1,"iconButton",3,"click"],["mat-icon-button","",1,"mb-1",3,"click"],["matInput","","formControlName","maxCount"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275elementStart"](1,"div",1),i["\u0275\u0275elementStart"](2,"button",2),i["\u0275\u0275listener"]("click",(function(){return t.close()})),i["\u0275\u0275elementStart"](3,"mat-icon"),i["\u0275\u0275text"](4,"keyboard_backspace"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"span"),i["\u0275\u0275text"](6,"Long Text"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](7,"div",3),i["\u0275\u0275elementStart"](8,"form",4),i["\u0275\u0275listener"]("ngSubmit",(function(){return t.onSubmit()})),i["\u0275\u0275elementStart"](9,"mat-form-field",5),i["\u0275\u0275elementStart"](10,"mat-label"),i["\u0275\u0275text"](11,"Field name"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](12,"input",6),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](13,"mat-form-field",5),i["\u0275\u0275elementStart"](14,"mat-label"),i["\u0275\u0275text"](15,"Field type"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](16,"input",7),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](17,"div",8),i["\u0275\u0275elementStart"](18,"div",9),i["\u0275\u0275elementStart"](19,"mat-icon",10),i["\u0275\u0275text"](20,"error"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](21,"div"),i["\u0275\u0275text"](22," Max count allowed - 200 "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](23,G,3,0,"button",11),i["\u0275\u0275template"](24,R,3,0,"button",11),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](25,H,5,0,"div",12),i["\u0275\u0275elementStart"](26,"mat-checkbox",13),i["\u0275\u0275text"](27,"Mandatory Field"),i["\u0275\u0275elementStart"](28,"sup"),i["\u0275\u0275text"](29,"*"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](30,"mat-checkbox",14),i["\u0275\u0275text"](31,"Enable Activity for field "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](32,"mat-checkbox",15),i["\u0275\u0275text"](33,"Enable Notification for field "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](34,"mat-checkbox",16),i["\u0275\u0275text"](35,"Edit allowed on Creation"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](36,"mat-checkbox",17),i["\u0275\u0275text"](37,"Edit allowed on Updation"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](38,"mat-checkbox",18),i["\u0275\u0275text"](39,"Unique Field"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](40,"button",19),i["\u0275\u0275elementStart"](41,"mat-icon",20),i["\u0275\u0275text"](42,"done_all"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](43,"button",21),i["\u0275\u0275elementStart"](44,"mat-icon",22),i["\u0275\u0275listener"]("click",(function(){return t.close()})),i["\u0275\u0275text"](45,"close"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](8),i["\u0275\u0275property"]("formGroup",t.longTextForm),i["\u0275\u0275advance"](15),i["\u0275\u0275property"]("ngIf",!t.maxcount),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.maxcount),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.maxcount))},directives:[c.a,d.a,l.J,l.w,l.n,m.c,m.g,s.b,l.e,l.v,l.l,o.NgIf,M.a],styles:[".card-wrapper[_ngcontent-%COMP%]{box-shadow:0 0 3px 0 #ccc;border-radius:3px;margin:5px 0 20px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]{padding-top:5px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-top:-4px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-weight:500;font-size:17px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]{margin:10px 18px;padding-bottom:20px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{width:100%;font-size:12px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .button-inactive[_ngcontent-%COMP%]{line-height:8px;width:26px;height:26px;margin-right:12px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:16px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .red[_ngcontent-%COMP%]{background:#d41111;color:#fff;font-size:16px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .extra_fields[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]{display:block}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .extra_fields[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]     .mat-checkbox-inner-container{margin-right:14px;height:15px;width:15px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .extra_fields[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]     .mat-checkbox-label{font-size:13px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .extra_fields[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]   sup[_ngcontent-%COMP%]{color:red;font-size:1em;line-height:0;position:relative;vertical-align:initial;top:-.1em}"]}),e})();function W(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",27),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"]().maxcount=!0})),i["\u0275\u0275elementStart"](1,"mat-icon",24),i["\u0275\u0275text"](2,"edit"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}}function Y(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",27),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"]().maxcount=!1})),i["\u0275\u0275elementStart"](1,"mat-icon",24),i["\u0275\u0275text"](2,"close"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}}function J(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div"),i["\u0275\u0275elementStart"](1,"mat-form-field",5),i["\u0275\u0275elementStart"](2,"mat-label"),i["\u0275\u0275text"](3,"Max Characters"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](4,"input",28),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]())}function $(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",29),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"]().openApiConfigModal()})),i["\u0275\u0275text"](1," Api Config "),i["\u0275\u0275elementEnd"]()}}function X(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",29),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"]().openFieldControlConfigModal()})),i["\u0275\u0275text"](1," Field Control Config "),i["\u0275\u0275elementEnd"]()}}let K=(()=>{class e{constructor(e,t){this._FieldsLibraryService=e,this._CustomFieldService=t,this.closeComponent=new i.EventEmitter,this.msg=!1,this.maxcount=!1,this.valueChangeSubscription=new F.a}ngOnInit(){this.formFieldData=this._FieldsLibraryService.getFormFieldData(),this.shortTextForm=new l.m({fieldName:new l.j("",l.H.required),fieldType:new l.j(this.field.label),fieldId:new l.j(this.field.id),fieldIcon:new l.j(this.field.icon),rows:new l.j(this.field.default_rows),cols:new l.j(this.field.default_columns),maxCount:new l.j("50"),mandatoryField:new l.j(""),udrfColumnData:new l.j(this.field.udrf_column_data),enableActivity:new l.j(!1),enableNotification:new l.j(!1),editableOnCreation:new l.j(!0),editableOnUpdation:new l.j(!0),uniqueField:new l.j(!1),valueFromApi:new l.j(!1),allowFieldControl:new l.j(!1)}),this.valueChangeSubscription.add(this.shortTextForm.get("enableNotification").valueChanges.subscribe(e=>{e&&this.shortTextForm.get("enableActivity").patchValue(!0)})),this.valueChangeSubscription.add(this.shortTextForm.get("enableActivity").valueChanges.subscribe(e=>{e||this.shortTextForm.get("enableNotification").patchValue(!1)})),Object.keys(this.formFieldData).length>0&&this.patchvalues(this.formFieldData)}patchvalues(e){this.shortTextForm.addControl("x",new l.j("")),this.shortTextForm.addControl("y",new l.j("")),e._id&&this.shortTextForm.addControl("_id",new l.j(e._id)),e.mysql_column_name&&this.shortTextForm.addControl("mysql_column_name",new l.j(e.mysql_column_name)),this.shortTextForm.patchValue({fieldName:e.fieldName,fieldType:e.fieldType,fieldId:e.fieldId,fieldIcon:e.fieldIcon,rows:e.rows,cols:e.cols,x:e.x,y:e.y,maxCount:e.maxCount,mandatoryField:e.mandatoryField,udrfColumnData:e.udrfColumnData,enableActivity:!!e.enableActivity&&e.enableActivity,enableNotification:!!e.enableNotification&&e.enableNotification,editableOnCreation:null==e.editableOnCreation||e.editableOnCreation,editableOnUpdation:null==e.editableOnUpdation||e.editableOnUpdation,uniqueField:e.uniqueField,valueFromApi:e.valueFromApi,allowFieldControl:e.allowFieldControl}),e.valueFromApi&&(this.shortTextForm.addControl("apiConfig",new l.j(null)),this.shortTextForm.get("apiConfig").patchValue(e.apiConfig)),e.allowFieldControl&&(this.shortTextForm.addControl("fieldControlMapping",new l.j(null)),this.shortTextForm.get("fieldControlMapping").patchValue(e.fieldControlMapping))}onSubmit(){this.shortTextForm.invalid||(console.log(this.shortTextForm.value),this._CustomFieldService.addField(this.shortTextForm.value),this.closeComponent.emit(!0))}close(){this.msg=!1,this._CustomFieldService.editCheck(this.msg),this.closeComponent.emit(!0)}toggleValueFromApi(e){e.checked&&!this.shortTextForm.contains("apiConfig")?this.shortTextForm.addControl("apiConfig",new l.j(null,l.H.required)):e.checked||this.shortTextForm.removeControl("apiConfig")}openApiConfigModal(){return Object(a.c)(this,void 0,void 0,(function*(){this._FieldsLibraryService.openApiConfigModal(this.shortTextForm)}))}toggleAllowFieldControl(e){e.checked&&!this.shortTextForm.contains("fieldControlMapping")?this.shortTextForm.addControl("fieldControlMapping",new l.j(null,l.H.required)):e.checked||this.shortTextForm.removeControl("fieldControlMapping")}openFieldControlConfigModal(){return Object(a.c)(this,void 0,void 0,(function*(){this._FieldsLibraryService.openFieldControlConfigModal(this.shortTextForm)}))}ngOnDestroy(){this.valueChangeSubscription&&this.valueChangeSubscription.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](O.a),i["\u0275\u0275directiveInject"](v.a))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-short-text"]],inputs:{field:"field"},outputs:{closeComponent:"closeComponent"},decls:54,vars:6,consts:[[1,"card-wrapper"],[1,"card-heading"],["mat-icon-button","",3,"click"],[1,"card-contents"],[3,"formGroup","ngSubmit"],["appearance","outline"],["matInput","","formControlName","fieldName"],["matInput","","formControlName","fieldType","readonly",""],[1,"extra_fields","mb-2"],[1,"mt-2","mb-2",2,"display","flex","align-items","center"],[2,"color","#cf0001","font-size","18px"],["mat-icon-button","","class","mb-1",3,"click",4,"ngIf"],[4,"ngIf"],["formControlName","mandatoryField"],["formControlName","enableActivity"],["formControlName","enableNotification"],["formControlName","editableOnCreation"],["formControlName","editableOnUpdation"],["formControlName","uniqueField"],["formControlName","valueFromApi",3,"change"],[1,"d-flex","justify-content-center"],["type","button","mat-raised-button","","class","ml-2 btn-class",3,"click",4,"ngIf"],["formControlName","allowFieldControl",3,"change"],["mat-icon-button","","type","submit",1,"button-inactive","red"],[2,"font-size","16px"],["mat-icon-button","",1,"button-inactive"],[1,"iconButton",3,"click"],["mat-icon-button","",1,"mb-1",3,"click"],["matInput","","formControlName","maxCount"],["type","button","mat-raised-button","",1,"ml-2","btn-class",3,"click"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275elementStart"](1,"div",1),i["\u0275\u0275elementStart"](2,"button",2),i["\u0275\u0275listener"]("click",(function(){return t.close()})),i["\u0275\u0275elementStart"](3,"mat-icon"),i["\u0275\u0275text"](4,"keyboard_backspace"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"span"),i["\u0275\u0275text"](6,"Short Text"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](7,"div",3),i["\u0275\u0275elementStart"](8,"form",4),i["\u0275\u0275listener"]("ngSubmit",(function(){return t.onSubmit()})),i["\u0275\u0275elementStart"](9,"mat-form-field",5),i["\u0275\u0275elementStart"](10,"mat-label"),i["\u0275\u0275text"](11,"Field name"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](12,"input",6),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](13,"mat-form-field",5),i["\u0275\u0275elementStart"](14,"mat-label"),i["\u0275\u0275text"](15,"Field type"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](16,"input",7),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](17,"div",8),i["\u0275\u0275elementStart"](18,"div",9),i["\u0275\u0275elementStart"](19,"mat-icon",10),i["\u0275\u0275text"](20,"error"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](21,"div"),i["\u0275\u0275text"](22," Max count allowed - 50 "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](23,W,3,0,"button",11),i["\u0275\u0275template"](24,Y,3,0,"button",11),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](25,J,5,0,"div",12),i["\u0275\u0275elementStart"](26,"mat-checkbox",13),i["\u0275\u0275text"](27,"Mandatory Field "),i["\u0275\u0275elementStart"](28,"sup"),i["\u0275\u0275text"](29,"*"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](30,"mat-checkbox",14),i["\u0275\u0275text"](31,"Enable Activity for field "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](32,"mat-checkbox",15),i["\u0275\u0275text"](33,"Enable Notification for field "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](34,"mat-checkbox",16),i["\u0275\u0275text"](35,"Edit allowed on Creation"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](36,"mat-checkbox",17),i["\u0275\u0275text"](37,"Edit allowed on Updation"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](38,"mat-checkbox",18),i["\u0275\u0275text"](39,"Unique Field"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](40,"mat-checkbox",19),i["\u0275\u0275listener"]("change",(function(e){return t.toggleValueFromApi(e)})),i["\u0275\u0275text"](41,"Get Value from external api"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](42,"div",20),i["\u0275\u0275template"](43,$,2,0,"button",21),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](44,"mat-checkbox",22),i["\u0275\u0275listener"]("change",(function(e){return t.toggleAllowFieldControl(e)})),i["\u0275\u0275text"](45,"Enable Field Control (based on condition)"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](46,"div",20),i["\u0275\u0275template"](47,X,2,0,"button",21),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](48,"button",23),i["\u0275\u0275elementStart"](49,"mat-icon",24),i["\u0275\u0275text"](50,"done_all"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](51,"button",25),i["\u0275\u0275elementStart"](52,"mat-icon",26),i["\u0275\u0275listener"]("click",(function(){return t.close()})),i["\u0275\u0275text"](53,"close"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](8),i["\u0275\u0275property"]("formGroup",t.shortTextForm),i["\u0275\u0275advance"](15),i["\u0275\u0275property"]("ngIf",!t.maxcount),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.maxcount),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.maxcount),i["\u0275\u0275advance"](18),i["\u0275\u0275property"]("ngIf",t.shortTextForm.get("valueFromApi").value),i["\u0275\u0275advance"](4),i["\u0275\u0275property"]("ngIf",t.shortTextForm.get("allowFieldControl").value))},directives:[c.a,d.a,l.J,l.w,l.n,m.c,m.g,s.b,l.e,l.v,l.l,o.NgIf,M.a],styles:[".card-wrapper[_ngcontent-%COMP%]{box-shadow:0 0 3px 0 #ccc;border-radius:3px;margin:5px 0 20px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]{padding-top:5px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-top:-4px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-weight:500;font-size:17px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]{margin:10px 18px;padding-bottom:20px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{width:100%;font-size:12px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .button-inactive[_ngcontent-%COMP%]{line-height:8px;width:26px;height:26px;margin-right:12px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:16px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .red[_ngcontent-%COMP%]{background:#d41111;color:#fff;font-size:16px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .extra_fields[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]{display:block}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .extra_fields[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]     .mat-checkbox-inner-container{margin-right:14px;height:15px;width:15px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .extra_fields[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]     .mat-checkbox-label{font-size:13px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .extra_fields[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]   sup[_ngcontent-%COMP%]{color:red;font-size:1em;line-height:0;position:relative;vertical-align:initial;top:-.1em}"]}),e})(),Q=(()=>{class e{constructor(e,t){this._FieldsLibraryService=e,this._CustomFieldService=t,this.closeComponent=new i.EventEmitter,this.valueChangeSubscription=new F.a,this.msg=!1}ngOnInit(){this.formFieldData=this._FieldsLibraryService.getFormFieldData(),this.phoneForm=new l.m({fieldName:new l.j("",l.H.required),fieldType:new l.j(this.field.label),fieldId:new l.j(this.field.id),fieldIcon:new l.j(this.field.icon),rows:new l.j(this.field.default_rows),cols:new l.j(this.field.default_columns),addPhoneExtension:new l.j(""),mandatoryField:new l.j(""),udrfColumnData:new l.j(this.field.udrf_column_data),enableActivity:new l.j(!1),enableNotification:new l.j(!1),editableOnCreation:new l.j(!0),editableOnUpdation:new l.j(!0)}),this.valueChangeSubscription.add(this.phoneForm.get("enableNotification").valueChanges.subscribe(e=>{e&&this.phoneForm.get("enableActivity").patchValue(!0)})),this.valueChangeSubscription.add(this.phoneForm.get("enableActivity").valueChanges.subscribe(e=>{e||this.phoneForm.get("enableNotification").patchValue(!1)})),Object.keys(this.formFieldData).length>0&&(console.log(this.formFieldData),this.patchvalues(this.formFieldData))}patchvalues(e){this.phoneForm.addControl("x",new l.j("")),this.phoneForm.addControl("y",new l.j("")),e._id&&this.phoneForm.addControl("_id",new l.j(e._id)),e.mysql_column_name&&this.phoneForm.addControl("mysql_column_name",new l.j(e.mysql_column_name)),this.phoneForm.patchValue({fieldName:e.fieldName,fieldType:e.fieldType,fieldId:e.fieldId,fieldIcon:e.fieldIcon,rows:e.rows,cols:e.cols,x:e.x,y:e.y,addPhoneExtension:e.addPhoneExtension,mandatoryField:e.mandatoryField,udrfColumnData:e.udrfColumnData,enableActivity:!!e.enableActivity&&e.enableActivity,enableNotification:!!e.enableNotification&&e.enableNotification,editableOnCreation:null==e.editableOnCreation||e.editableOnCreation,editableOnUpdation:null==e.editableOnUpdation||e.editableOnUpdation})}onSubmit(){this.phoneForm.invalid||(console.log(this.phoneForm.value),this._CustomFieldService.addField(this.phoneForm.value),this.closeComponent.emit(!0))}close(){this.msg=!1,this._CustomFieldService.editCheck(this.msg),this.closeComponent.emit(!0)}ngOnDestroy(){this.valueChangeSubscription&&this.valueChangeSubscription.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](O.a),i["\u0275\u0275directiveInject"](v.a))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-phone"]],inputs:{field:"field"},outputs:{closeComponent:"closeComponent"},decls:43,vars:1,consts:[[1,"card-wrapper"],[1,"card-heading"],["mat-icon-button","",3,"click"],[1,"card-contents"],[3,"formGroup","ngSubmit"],["appearance","outline"],["matInput","","formControlName","fieldName"],["matInput","","formControlName","fieldType","readonly",""],[1,"extra_fields","mb-3"],[1,"d-flex","align-items-center","my-3"],[1,"mr-1",2,"color","#cf0000","font-size","20px"],["formControlName","addPhoneExtension"],["formControlName","mandatoryField"],["formControlName","enableActivity"],["formControlName","enableNotification"],["formControlName","editableOnCreation"],["formControlName","editableOnUpdation"],["mat-icon-button","","type","submit",1,"button-inactive","red"],[2,"font-size","16px"],["mat-icon-button","",1,"button-inactive"],[1,"iconButton",3,"click"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275elementStart"](1,"div",1),i["\u0275\u0275elementStart"](2,"button",2),i["\u0275\u0275listener"]("click",(function(){return t.close()})),i["\u0275\u0275elementStart"](3,"mat-icon"),i["\u0275\u0275text"](4,"keyboard_backspace"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"span"),i["\u0275\u0275text"](6,"Phone"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](7,"div",3),i["\u0275\u0275elementStart"](8,"form",4),i["\u0275\u0275listener"]("ngSubmit",(function(){return t.onSubmit()})),i["\u0275\u0275elementStart"](9,"mat-form-field",5),i["\u0275\u0275elementStart"](10,"mat-label"),i["\u0275\u0275text"](11,"Field name"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](12,"input",6),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](13,"mat-form-field",5),i["\u0275\u0275elementStart"](14,"mat-label"),i["\u0275\u0275text"](15,"Field type"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](16,"input",7),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](17,"div",8),i["\u0275\u0275elementStart"](18,"div",9),i["\u0275\u0275elementStart"](19,"mat-icon",10),i["\u0275\u0275text"](20,"info"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](21,"div"),i["\u0275\u0275text"](22,"Max count allowed - 15"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](23,"mat-checkbox",11),i["\u0275\u0275text"](24,"Add Phone extension number"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](25,"mat-checkbox",12),i["\u0275\u0275text"](26,"Mandatory Field "),i["\u0275\u0275elementStart"](27,"sup"),i["\u0275\u0275text"](28,"*"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](29,"mat-checkbox",13),i["\u0275\u0275text"](30,"Enable Activity for field "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](31,"mat-checkbox",14),i["\u0275\u0275text"](32,"Enable Notification for field "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](33,"mat-checkbox",15),i["\u0275\u0275text"](34,"Edit allowed on Creation"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](35,"mat-checkbox",16),i["\u0275\u0275text"](36,"Edit allowed on Updation"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](37,"button",17),i["\u0275\u0275elementStart"](38,"mat-icon",18),i["\u0275\u0275text"](39,"done_all"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](40,"button",19),i["\u0275\u0275elementStart"](41,"mat-icon",20),i["\u0275\u0275listener"]("click",(function(){return t.close()})),i["\u0275\u0275text"](42,"close"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](8),i["\u0275\u0275property"]("formGroup",t.phoneForm))},directives:[c.a,d.a,l.J,l.w,l.n,m.c,m.g,s.b,l.e,l.v,l.l,M.a],styles:[".card-wrapper[_ngcontent-%COMP%]{box-shadow:0 0 3px 0 #ccc;border-radius:3px;margin:5px 0 20px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]{padding-top:5px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-top:-4px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-weight:500;font-size:17px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]{min-width:220px;margin:10px 18px;padding-bottom:20px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{width:100%;font-size:12px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .button-inactive[_ngcontent-%COMP%]{line-height:8px;width:26px;height:26px;margin-right:12px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:16px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .red[_ngcontent-%COMP%]{background:#d41111;color:#fff;font-size:16px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .extra-fields[_ngcontent-%COMP%]{margin:10px 0}mat-checkbox[_ngcontent-%COMP%]{display:block}mat-checkbox[_ngcontent-%COMP%]     .mat-checkbox-inner-container{margin-right:14px;height:15px;width:15px}mat-checkbox[_ngcontent-%COMP%]     .mat-checkbox-label{font-size:13px}mat-checkbox[_ngcontent-%COMP%]   sup[_ngcontent-%COMP%]{color:red;font-size:1em;line-height:0;position:relative;vertical-align:initial;top:-.1em}"]}),e})();function Z(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",23),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"]().openApiConfigModal()})),i["\u0275\u0275text"](1," Api Config "),i["\u0275\u0275elementEnd"]()}}function ee(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",23),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"]().openFieldControlConfigModal()})),i["\u0275\u0275text"](1," Field Control Config "),i["\u0275\u0275elementEnd"]()}}let te=(()=>{class e{constructor(e,t){this._FieldsLibraryService=e,this._CustomFieldService=t,this.closeComponent=new i.EventEmitter,this.msg=!1,this.valueChangeSubscription=new F.a}ngOnInit(){this.formFieldData=this._FieldsLibraryService.getFormFieldData(),this.emailForm=new l.m({fieldName:new l.j("",l.H.required),fieldType:new l.j(this.field.label),fieldId:new l.j(this.field.id),fieldIcon:new l.j(this.field.icon),rows:new l.j(this.field.default_rows),cols:new l.j(this.field.default_columns),mandatoryField:new l.j(""),udrfColumnData:new l.j(this.field.udrf_column_data),enableActivity:new l.j(!1),enableNotification:new l.j(!1),editableOnCreation:new l.j(!0),editableOnUpdation:new l.j(!0),uniqueField:new l.j(!1),valueFromApi:new l.j(!1),allowFieldControl:new l.j(!1)}),this.valueChangeSubscription.add(this.emailForm.get("enableNotification").valueChanges.subscribe(e=>{e&&this.emailForm.get("enableActivity").patchValue(!0)})),this.valueChangeSubscription.add(this.emailForm.get("enableActivity").valueChanges.subscribe(e=>{e||this.emailForm.get("enableNotification").patchValue(!1)})),Object.keys(this.formFieldData).length>0&&this.patchvalues(this.formFieldData)}patchvalues(e){this.emailForm.addControl("x",new l.j("")),this.emailForm.addControl("y",new l.j("")),e._id&&this.emailForm.addControl("_id",new l.j(e._id)),e.mysql_column_name&&this.emailForm.addControl("mysql_column_name",new l.j(e.mysql_column_name)),this.emailForm.patchValue({fieldName:e.fieldName,fieldType:e.fieldType,fieldId:e.fieldId,fieldIcon:e.fieldIcon,rows:e.rows,cols:e.cols,x:e.x,y:e.y,mandatoryField:e.mandatoryField,udrfColumnData:e.udrfColumnData,enableActivity:!!e.enableActivity&&e.enableActivity,enableNotification:!!e.enableNotification&&e.enableNotification,editableOnCreation:null==e.editableOnCreation||e.editableOnCreation,editableOnUpdation:null==e.editableOnUpdation||e.editableOnUpdation,uniqueField:e.uniqueField,valueFromApi:e.valueFromApi,allowFieldControl:e.allowFieldControl}),e.valueFromApi&&(this.emailForm.addControl("apiConfig",new l.j(null)),this.emailForm.get("apiConfig").patchValue(e.apiConfig)),e.allowFieldControl&&(this.emailForm.addControl("fieldControlMapping",new l.j(null)),this.emailForm.get("fieldControlMapping").patchValue(e.fieldControlMapping))}onSubmit(){this.emailForm.invalid||(console.log(this.emailForm.value),this._CustomFieldService.addField(this.emailForm.value),this.closeComponent.emit(!0))}close(){this.msg=!1,this._CustomFieldService.editCheck(this.msg),this.closeComponent.emit(!0)}toggleValueFromApi(e){e.checked&&!this.emailForm.contains("apiConfig")?this.emailForm.addControl("apiConfig",new l.j(null,l.H.required)):e.checked||this.emailForm.removeControl("apiConfig")}openApiConfigModal(){return Object(a.c)(this,void 0,void 0,(function*(){this._FieldsLibraryService.openApiConfigModal(this.emailForm)}))}toggleAllowFieldControl(e){e.checked&&!this.emailForm.contains("fieldControlMapping")?this.emailForm.addControl("fieldControlMapping",new l.j(null,l.H.required)):e.checked||this.emailForm.removeControl("fieldControlMapping")}openFieldControlConfigModal(){return Object(a.c)(this,void 0,void 0,(function*(){this._FieldsLibraryService.openFieldControlConfigModal(this.emailForm)}))}ngOnDestroy(){this.valueChangeSubscription&&this.valueChangeSubscription.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](O.a),i["\u0275\u0275directiveInject"](v.a))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-email"]],inputs:{field:"field"},outputs:{closeComponent:"closeComponent"},decls:46,vars:3,consts:[[1,"card-wrapper"],[1,"card-heading"],["mat-icon-button","",3,"click"],[1,"card-contents"],[3,"formGroup","ngSubmit"],["appearance","outline"],["matInput","","formControlName","fieldName"],["matInput","","formControlName","fieldType","readonly",""],[1,"mandatoryFieldCheck"],["formControlName","mandatoryField"],["formControlName","enableActivity"],["formControlName","enableNotification"],["formControlName","editableOnCreation"],["formControlName","editableOnUpdation"],["formControlName","uniqueField"],["formControlName","valueFromApi",3,"change"],[1,"d-flex","justify-content-center"],["type","button","mat-raised-button","","class","ml-2 btn-class",3,"click",4,"ngIf"],["formControlName","allowFieldControl",3,"change"],["mat-icon-button","","type","submit",1,"button-inactive","red"],[2,"font-size","16px"],["mat-icon-button","","type","button",1,"button-inactive"],[1,"iconButton",3,"click"],["type","button","mat-raised-button","",1,"ml-2","btn-class",3,"click"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275elementStart"](1,"div",1),i["\u0275\u0275elementStart"](2,"button",2),i["\u0275\u0275listener"]("click",(function(){return t.close()})),i["\u0275\u0275elementStart"](3,"mat-icon"),i["\u0275\u0275text"](4,"keyboard_backspace"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"span"),i["\u0275\u0275text"](6,"Email"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](7,"div",3),i["\u0275\u0275elementStart"](8,"form",4),i["\u0275\u0275listener"]("ngSubmit",(function(){return t.onSubmit()})),i["\u0275\u0275elementStart"](9,"mat-form-field",5),i["\u0275\u0275elementStart"](10,"mat-label"),i["\u0275\u0275text"](11,"Field name"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](12,"input",6),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](13,"mat-form-field",5),i["\u0275\u0275elementStart"](14,"mat-label"),i["\u0275\u0275text"](15,"Field type"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](16,"input",7),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](17,"div",8),i["\u0275\u0275elementStart"](18,"mat-checkbox",9),i["\u0275\u0275text"](19,"Mandatory field "),i["\u0275\u0275elementStart"](20,"sup"),i["\u0275\u0275text"](21,"*"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](22,"mat-checkbox",10),i["\u0275\u0275text"](23,"Enable Activity for field "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](24,"mat-checkbox",11),i["\u0275\u0275text"](25,"Enable Notification for field "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](26,"mat-checkbox",12),i["\u0275\u0275text"](27,"Edit allowed on Creation"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](28,"mat-checkbox",13),i["\u0275\u0275text"](29,"Edit allowed on Updation"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](30,"mat-checkbox",14),i["\u0275\u0275text"](31,"Unique Field"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](32,"mat-checkbox",15),i["\u0275\u0275listener"]("change",(function(e){return t.toggleValueFromApi(e)})),i["\u0275\u0275text"](33,"Get Value from external api"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](34,"div",16),i["\u0275\u0275template"](35,Z,2,0,"button",17),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](36,"mat-checkbox",18),i["\u0275\u0275listener"]("change",(function(e){return t.toggleAllowFieldControl(e)})),i["\u0275\u0275text"](37,"Enable Field Control (based on condition)"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](38,"div",16),i["\u0275\u0275template"](39,ee,2,0,"button",17),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](40,"button",19),i["\u0275\u0275elementStart"](41,"mat-icon",20),i["\u0275\u0275text"](42,"done_all"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](43,"button",21),i["\u0275\u0275elementStart"](44,"mat-icon",22),i["\u0275\u0275listener"]("click",(function(){return t.close()})),i["\u0275\u0275text"](45,"close"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](8),i["\u0275\u0275property"]("formGroup",t.emailForm),i["\u0275\u0275advance"](27),i["\u0275\u0275property"]("ngIf",t.emailForm.get("valueFromApi").value),i["\u0275\u0275advance"](4),i["\u0275\u0275property"]("ngIf",t.emailForm.get("allowFieldControl").value))},directives:[c.a,d.a,l.J,l.w,l.n,m.c,m.g,s.b,l.e,l.v,l.l,M.a,o.NgIf],styles:[".card-wrapper[_ngcontent-%COMP%]{box-shadow:0 0 3px 0 #ccc;border-radius:3px;margin:5px 0 20px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]{padding-top:5px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-top:-4px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-weight:500;font-size:17px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]{min-width:220px;margin:10px 18px;padding-bottom:20px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{width:100%;font-size:12px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .button-inactive[_ngcontent-%COMP%]{line-height:8px;width:26px;height:26px;margin-right:12px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:16px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .red[_ngcontent-%COMP%]{background:#d41111;color:#fff;font-size:16px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .mandatoryFieldCheck[_ngcontent-%COMP%]{margin:10px 0}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .mandatoryFieldCheck[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]{display:block}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .mandatoryFieldCheck[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]     .mat-checkbox-inner-container{margin-right:14px;height:15px;width:15px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .mandatoryFieldCheck[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]     .mat-checkbox-label{font-size:13px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .mandatoryFieldCheck[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]   sup[_ngcontent-%COMP%]{color:red;font-size:1em;line-height:0;position:relative;vertical-align:initial;top:-.1em}"]}),e})();var ne=n("wd/R"),oe=n("zB/H"),ie=n("XNiG"),ae=n("1G5W"),re=n("d3UM"),le=n("FKr1");function ce(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-option",31),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;i["\u0275\u0275property"]("value",e.value),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](e.label)}}function de(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",32),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"]().openApiConfigModal()})),i["\u0275\u0275text"](1," Api Config "),i["\u0275\u0275elementEnd"]()}}function me(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",32),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"]().openFieldControlConfigModal()})),i["\u0275\u0275text"](1," Field Control Config "),i["\u0275\u0275elementEnd"]()}}let se=(()=>{class e{constructor(e,t){this._FieldsLibraryService=e,this._CustomFieldService=t,this.closeComponent=new i.EventEmitter,this.ngUnSubscribe=new ie.b,this.msg=!1,this.formatList=[{label:`DD-MM-YY (Eg: ${ne().format("DD-MM-YY")})`,value:"DD-MM-YY"},{label:`DD-MM-YYYY (Eg: ${ne().format("DD-MM-YYYY")})`,value:"DD-MM-YYYY"},{label:`MM-DD-YYYY (Eg: ${ne().format("MM-DD-YYYY")})`,value:"MM-DD-YYYY"},{label:`Do MMM YY (Eg: ${ne().format("Do MMM YY")})`,value:"Do MMM YY"},{label:`DD-MMM-YY (Eg: ${ne().format("DD-MMM-YY")})`,value:"DD-MMM-YY"},{label:`MMMM Do YYYY (Eg: ${ne().format("MMMM Do YYYY")})`,value:"MMMM Do YYYY"}],this.valueChangeSubscription=new oe.Subscription}ngOnInit(){if(this.formFieldData=this._FieldsLibraryService.getFormFieldData(),this.dateForm=new l.m({fieldName:new l.j("",l.H.required),fieldType:new l.j(this.field.label),dateFormat:new l.j(this.formatList[0].value),fieldId:new l.j(this.field.id),fieldIcon:new l.j(this.field.icon),rows:new l.j(this.field.default_rows),cols:new l.j(this.field.default_columns),useDateRange:new l.j(!1),allowTimeSelection:new l.j(!1),mandatoryField:new l.j(!1),udrfColumnData:new l.j(this.field.udrf_column_data),enableActivity:new l.j(!1),enableNotification:new l.j(!1),allowPastDate:new l.j(!0),allowFutureDate:new l.j(!0),editableOnCreation:new l.j(!0),editableOnUpdation:new l.j(!0),valueFromApi:new l.j(!1),allowFieldControl:new l.j(!1)}),this.resolveSubscriptions(),Object.keys(this.formFieldData).length>0&&this.patchvalues(this.formFieldData),this.field&&this.field.hasOwnProperty("date_formats")){if(this.field.date_formats instanceof Array&&this.field.date_formats.length){this.formatList=[];for(const e of this.field.date_formats)this.formatList.push({label:`${e} (Eg: ${ne().format(e)})`,value:e})}}else this.field.fieldId&&this._CustomFieldService.getMasterData().pipe(Object(ae.a)(this.ngUnSubscribe)).subscribe(e=>{if(e.length){let t=e.find(e=>5==e.id);if(t&&t.hasOwnProperty("date_formats")&&t.date_formats instanceof Array&&t.date_formats.length){this.formatList=[];for(const e of t.date_formats)this.formatList.push({label:`${e} (Eg: ${ne().format(e)})`,value:e})}}},e=>{console.log("error fetching fields master data")})}resolveSubscriptions(){this.rangeSubscription=this.dateForm.get("useDateRange").valueChanges.subscribe(e=>{e&&this.dateForm.get("allowTimeSelection").patchValue(!1)}),this.timeSubscription=this.dateForm.get("allowTimeSelection").valueChanges.subscribe(e=>{e&&this.dateForm.get("useDateRange").patchValue(!1)}),this.valueChangeSubscription.add(this.dateForm.get("enableNotification").valueChanges.subscribe(e=>{e&&this.dateForm.get("enableActivity").patchValue(!0)})),this.valueChangeSubscription.add(this.dateForm.get("enableActivity").valueChanges.subscribe(e=>{e||this.dateForm.get("enableNotification").patchValue(!1)}))}patchvalues(e){this.dateForm.addControl("x",new l.j("")),this.dateForm.addControl("y",new l.j("")),e._id&&this.dateForm.addControl("_id",new l.j(e._id)),e.mysql_column_name&&this.dateForm.addControl("mysql_column_name",new l.j(e.mysql_column_name)),this.dateForm.patchValue({fieldName:e.fieldName,fieldType:e.fieldType,dateFormat:e.dateFormat?e.dateFormat:this.formatList[0].value,fieldId:e.fieldId,fieldIcon:e.fieldIcon,rows:e.rows,cols:e.cols,x:e.x,y:e.y,useDateRange:e.useDateRange,allowTimeSelection:e.allowTimeSelection,mandatoryField:e.mandatoryField,udrfColumnData:e.udrfColumnData,enableActivity:!!e.enableActivity&&e.enableActivity,enableNotification:!!e.enableNotification&&e.enableNotification,allowPastDate:null==e.allowPastDate||e.allowPastDate,allowFutureDate:null==e.allowFutureDate||e.allowFutureDate,editableOnCreation:null==e.editableOnCreation||e.editableOnCreation,editableOnUpdation:null==e.editableOnUpdation||e.editableOnUpdation,valueFromApi:e.valueFromApi,allowFieldControl:e.allowFieldControl}),e.valueFromApi&&(this.dateForm.addControl("apiConfig",new l.j(null)),this.dateForm.get("apiConfig").patchValue(e.apiConfig)),e.allowFieldControl&&(this.dateForm.addControl("fieldControlMapping",new l.j(null)),this.dateForm.get("fieldControlMapping").patchValue(e.fieldControlMapping))}onSubmit(){this.dateForm.invalid||(console.log(this.dateForm.value),this._CustomFieldService.addField(this.dateForm.value),this.closeComponent.emit(!0))}close(){this.msg=!1,this._CustomFieldService.editCheck(this.msg),this.closeComponent.emit(!0)}toggleValueFromApi(e){e.checked&&!this.dateForm.contains("apiConfig")?this.dateForm.addControl("apiConfig",new l.j(null,l.H.required)):e.checked||this.dateForm.removeControl("apiConfig")}openApiConfigModal(){return Object(a.c)(this,void 0,void 0,(function*(){this._FieldsLibraryService.openApiConfigModal(this.dateForm)}))}toggleAllowFieldControl(e){e.checked&&!this.dateForm.contains("fieldControlMapping")?this.dateForm.addControl("fieldControlMapping",new l.j(null,l.H.required)):e.checked||this.dateForm.removeControl("fieldControlMapping")}openFieldControlConfigModal(){return Object(a.c)(this,void 0,void 0,(function*(){this._FieldsLibraryService.openFieldControlConfigModal(this.dateForm)}))}ngOnDestroy(){this.rangeSubscription&&this.rangeSubscription.unsubscribe(),this.timeSubscription&&this.timeSubscription.unsubscribe(),this.valueChangeSubscription&&this.valueChangeSubscription.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](O.a),i["\u0275\u0275directiveInject"](v.a))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-date"]],inputs:{field:"field"},outputs:{closeComponent:"closeComponent"},decls:62,vars:4,consts:[[1,"card-wrapper"],[1,"card-heading"],["mat-icon-button","",3,"click"],[1,"card-contents"],[3,"formGroup","ngSubmit"],["appearance","outline"],["matInput","","formControlName","fieldName"],["matInput","","formControlName","fieldType","readonly",""],["formControlName","dateFormat"],[3,"value",4,"ngFor","ngForOf"],[1,"extra_fields"],["formControlName","useDateRange"],["formControlName","allowTimeSelection"],[1,"mt-2","mb-2",2,"display","flex","align-items","center"],[2,"color","#cf0001","font-size","18px"],[1,"ml-1",2,"font-size","13px"],["formControlName","mandatoryField"],["formControlName","enableActivity"],["formControlName","enableNotification"],["formControlName","allowPastDate"],["formControlName","allowFutureDate"],["formControlName","editableOnCreation"],["formControlName","editableOnUpdation"],["formControlName","valueFromApi",3,"change"],[1,"d-flex","justify-content-center"],["type","button","mat-raised-button","","class","ml-2 btn-class",3,"click",4,"ngIf"],["formControlName","allowFieldControl",3,"change"],["mat-icon-button","","type","submit",1,"button-inactive","red"],[2,"font-size","16px"],["mat-icon-button","",1,"button-inactive"],[1,"iconButton",3,"click"],[3,"value"],["type","button","mat-raised-button","",1,"ml-2","btn-class",3,"click"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275elementStart"](1,"div",1),i["\u0275\u0275elementStart"](2,"button",2),i["\u0275\u0275listener"]("click",(function(){return t.close()})),i["\u0275\u0275elementStart"](3,"mat-icon"),i["\u0275\u0275text"](4,"keyboard_backspace"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"span"),i["\u0275\u0275text"](6,"Date"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](7,"div",3),i["\u0275\u0275elementStart"](8,"form",4),i["\u0275\u0275listener"]("ngSubmit",(function(){return t.onSubmit()})),i["\u0275\u0275elementStart"](9,"mat-form-field",5),i["\u0275\u0275elementStart"](10,"mat-label"),i["\u0275\u0275text"](11,"Field name"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](12,"input",6),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](13,"mat-form-field",5),i["\u0275\u0275elementStart"](14,"mat-label"),i["\u0275\u0275text"](15,"Field type"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](16,"input",7),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](17,"mat-form-field",5),i["\u0275\u0275elementStart"](18,"mat-label"),i["\u0275\u0275text"](19,"Date Format"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](20,"mat-select",8),i["\u0275\u0275template"](21,ce,2,2,"mat-option",9),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](22,"div",10),i["\u0275\u0275elementStart"](23,"mat-checkbox",11),i["\u0275\u0275text"](24,"Allow users to select date range "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](25,"mat-checkbox",12),i["\u0275\u0275text"](26,"Allow users to select time "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](27,"div",13),i["\u0275\u0275elementStart"](28,"mat-icon",14),i["\u0275\u0275text"](29,"error"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](30,"div",15),i["\u0275\u0275text"](31," Start date - End date "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](32,"mat-checkbox",16),i["\u0275\u0275text"](33,"Mandatory Field "),i["\u0275\u0275elementStart"](34,"sup"),i["\u0275\u0275text"](35,"*"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](36,"mat-checkbox",17),i["\u0275\u0275text"](37,"Enable Activity for field "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](38,"mat-checkbox",18),i["\u0275\u0275text"](39,"Enable Notification for field "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](40,"mat-checkbox",19),i["\u0275\u0275text"](41,"Allow Past Date Selection "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](42,"mat-checkbox",20),i["\u0275\u0275text"](43,"Allow Future Date Selection "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](44,"mat-checkbox",21),i["\u0275\u0275text"](45,"Edit allowed on Creation"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](46,"mat-checkbox",22),i["\u0275\u0275text"](47,"Edit allowed on Updation"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](48,"mat-checkbox",23),i["\u0275\u0275listener"]("change",(function(e){return t.toggleValueFromApi(e)})),i["\u0275\u0275text"](49,"Get Value from external api"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](50,"div",24),i["\u0275\u0275template"](51,de,2,0,"button",25),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](52,"mat-checkbox",26),i["\u0275\u0275listener"]("change",(function(e){return t.toggleAllowFieldControl(e)})),i["\u0275\u0275text"](53,"Enable Field Control (based on condition)"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](54,"div",24),i["\u0275\u0275template"](55,me,2,0,"button",25),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](56,"button",27),i["\u0275\u0275elementStart"](57,"mat-icon",28),i["\u0275\u0275text"](58,"done_all"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](59,"button",29),i["\u0275\u0275elementStart"](60,"mat-icon",30),i["\u0275\u0275listener"]("click",(function(){return t.close()})),i["\u0275\u0275text"](61,"close"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](8),i["\u0275\u0275property"]("formGroup",t.dateForm),i["\u0275\u0275advance"](13),i["\u0275\u0275property"]("ngForOf",t.formatList),i["\u0275\u0275advance"](30),i["\u0275\u0275property"]("ngIf",t.dateForm.get("valueFromApi").value),i["\u0275\u0275advance"](4),i["\u0275\u0275property"]("ngIf",t.dateForm.get("allowFieldControl").value))},directives:[c.a,d.a,l.J,l.w,l.n,m.c,m.g,s.b,l.e,l.v,l.l,re.c,o.NgForOf,M.a,o.NgIf,le.p],styles:[".card-wrapper[_ngcontent-%COMP%]{box-shadow:0 0 3px 0 #ccc;border-radius:3px;margin:5px 0 20px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]{padding-top:5px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-top:-4px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-weight:500;font-size:17px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]{min-width:220px;margin:10px 18px;padding-bottom:20px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{width:100%;font-size:12px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .button-inactive[_ngcontent-%COMP%]{line-height:8px;width:26px;height:26px;margin-right:12px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:16px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .red[_ngcontent-%COMP%]{background:#d41111;color:#fff;font-size:16px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .extra_fields[_ngcontent-%COMP%]{margin:10px 0}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .extra_fields[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]{display:block}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .extra_fields[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]     .mat-checkbox-inner-container{margin-right:14px;height:15px;width:15px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .extra_fields[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]     .mat-checkbox-label{font-size:13px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .extra_fields[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]   sup[_ngcontent-%COMP%]{color:red;font-size:1em;line-height:0;position:relative;vertical-align:initial;top:-.1em}"]}),e})();var pe=n("5CHU");function ge(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div"),i["\u0275\u0275elementStart"](1,"app-multi-select-search2",30),i["\u0275\u0275listener"]("valueChange",(function(){i["\u0275\u0275restoreView"](e);const t=i["\u0275\u0275nextContext"]();return t.getValueFn("selectPeople")&&t.getEmployeesByOrg(t.getValueFn("orgId"))})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("list",e.orgList)}}function ue(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div"),i["\u0275\u0275element"](1,"app-multi-select-search2",31),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("list",e.empList)}}function he(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",32),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"]().openApiConfigModal()})),i["\u0275\u0275text"](1," Api Config "),i["\u0275\u0275elementEnd"]()}}function fe(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",32),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"]().openFieldControlConfigModal()})),i["\u0275\u0275text"](1," Field Control Config "),i["\u0275\u0275elementEnd"]()}}let Ce=(()=>{class e{constructor(e,t){this._FieldsLibraryService=e,this._CustomFieldService=t,this.closeComponent=new i.EventEmitter,this.orgList=[],this.empList=[],this.msg=!1,this.valueChangeSubscription=new F.a}ngOnInit(){this.formFieldData=this._FieldsLibraryService.getFormFieldData(),this.assigntoForm=new l.m({fieldName:new l.j("",l.H.required),fieldType:new l.j(this.field.label),fieldId:new l.j(this.field.id),fieldIcon:new l.j(this.field.icon),rows:new l.j(this.field.default_rows),cols:new l.j(this.field.default_columns),showEveryone:new l.j(!0),specificOrg:new l.j(!1),orgId:new l.j(""),selectPeople:new l.j(!1),people:new l.j(""),allowMultiSelect:new l.j(!1),mandatoryField:new l.j(!1),udrfColumnData:new l.j(this.field.udrf_column_data),enableActivity:new l.j(!1),enableNotification:new l.j(!1),enableInlineEdit:new l.j(!1),editableOnCreation:new l.j(!0),editableOnUpdation:new l.j(!0),uniqueField:new l.j(!1),showAssociateId:new l.j(!1),valueFromApi:new l.j(!1),allowFieldControl:new l.j(!1)}),this.valueChangeSubscription.add(this.assigntoForm.get("enableNotification").valueChanges.subscribe(e=>{e&&this.assigntoForm.get("enableActivity").patchValue(!0)})),this.valueChangeSubscription.add(this.assigntoForm.get("enableActivity").valueChanges.subscribe(e=>{e||this.assigntoForm.get("enableNotification").patchValue(!1)})),Object.keys(this.formFieldData).length>0&&this.patchvalues(this.formFieldData),this.getValueFn("specificOrg")&&this.getOrgData(),this.assigntoForm.get("selectPeople").value&&(this.assigntoForm.get("specificOrg").value&&this.assigntoForm.get("orgId").value.length>0?this.getEmployeesByOrg(this.assigntoForm.get("orgId").value):this.getAllEmployees())}patchvalues(e){this.assigntoForm.addControl("x",new l.j("")),this.assigntoForm.addControl("y",new l.j("")),e._id&&this.assigntoForm.addControl("_id",new l.j(e._id)),e.mysql_column_name&&this.assigntoForm.addControl("mysql_column_name",new l.j(e.mysql_column_name));let t=[];e.orgId&&(Array.isArray(e.orgId)?t=e.orgId:"string"==typeof e.orgId&&(t=[e.orgId])),this.assigntoForm.patchValue({fieldName:e.fieldName,fieldType:e.fieldType,fieldId:e.fieldId,fieldIcon:e.fieldIcon,rows:e.rows,cols:e.cols,x:e.x,y:e.y,showEveryone:e.showEveryone,specificOrg:e.specificOrg,orgId:t,selectPeople:e.selectPeople,people:e.people,allowMultiSelect:e.allowMultiSelect,mandatoryField:e.mandatoryField,udrfColumnData:e.udrfColumnData,enableActivity:!!e.enableActivity&&e.enableActivity,enableNotification:!!e.enableNotification&&e.enableNotification,enableInlineEdit:!!e.enableInlineEdit&&e.enableInlineEdit,editableOnCreation:null==e.editableOnCreation||e.editableOnCreation,editableOnUpdation:null==e.editableOnUpdation||e.editableOnUpdation,uniqueField:e.uniqueField,showAssociateId:e.showAssociateId,valueFromApi:e.valueFromApi,allowFieldControl:e.allowFieldControl}),e.valueFromApi&&(this.assigntoForm.addControl("apiConfig",new l.j(null)),this.assigntoForm.get("apiConfig").patchValue(e.apiConfig)),e.allowFieldControl&&(this.assigntoForm.addControl("fieldControlMapping",new l.j(null)),this.assigntoForm.get("fieldControlMapping").patchValue(e.fieldControlMapping))}onSubmit(){this.assigntoForm.invalid||(this.getValueFn("showEveryone")&&this.setValueFn("orgId",""),this.getValueFn("selectPeople")||this.setValueFn("people",""),this._CustomFieldService.addField(this.assigntoForm.value),this.closeComponent.emit(!0))}close(){this.msg=!1,this._CustomFieldService.editCheck(this.msg),this.closeComponent.emit(!0)}getOrgData(){this._FieldsLibraryService.getOrgDesgData().subscribe(e=>{this.orgList=e})}getEmployeesByOrg(e){""==e?this.getAllEmployees():this._FieldsLibraryService.getEmployeesByOrg(e).subscribe(e=>{this.empList=e})}getAllEmployees(){this._FieldsLibraryService.getAllEmployees().subscribe(e=>{this.empList=e})}addRequiredValidator(e){this.assigntoForm.controls[e].setValidators([l.H.required]),this.assigntoForm.controls[e].updateValueAndValidity()}removeRequiredValidator(e){this.assigntoForm.controls[e].clearValidators(),this.assigntoForm.controls[e].updateValueAndValidity()}setValueFn(e,t){this.assigntoForm.controls[e].setValue(t)}getValueFn(e){return this.assigntoForm.get(e).value}toggleAssignScope(e,t){switch(e){case"showEveryone":t?(this.getAllEmployees(),this.setValueFn("people","")):this.setValueFn("showEveryone",!0),this.removeRequiredValidator("orgId"),this.setValueFn("specificOrg",!1);break;case"specificOrg":t?(this.getOrgData(),this.setValueFn("people",""),this.addRequiredValidator("orgId")):this.setValueFn("specificOrg",!0),this.setValueFn("showEveryone",!1);break;case"selectPeople":t?(this.addRequiredValidator("people"),this.getValueFn("specificOrg")&&this.getValueFn("orgId").length>0?this.getEmployeesByOrg(this.getValueFn("orgId")):this.getAllEmployees()):this.removeRequiredValidator("people")}}toggleValueFromApi(e){e.checked&&!this.assigntoForm.contains("apiConfig")?this.assigntoForm.addControl("apiConfig",new l.j(null,l.H.required)):e.checked||this.assigntoForm.removeControl("apiConfig")}openApiConfigModal(){return Object(a.c)(this,void 0,void 0,(function*(){this._FieldsLibraryService.openApiConfigModal(this.assigntoForm)}))}toggleAllowFieldControl(e){e.checked&&!this.assigntoForm.contains("fieldControlMapping")?this.assigntoForm.addControl("fieldControlMapping",new l.j(null,l.H.required)):e.checked||this.assigntoForm.removeControl("fieldControlMapping")}openFieldControlConfigModal(){return Object(a.c)(this,void 0,void 0,(function*(){this._FieldsLibraryService.openFieldControlConfigModal(this.assigntoForm)}))}ngOnDestroy(){this.valueChangeSubscription&&this.valueChangeSubscription.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](O.a),i["\u0275\u0275directiveInject"](v.a))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-assign-to"]],inputs:{field:"field"},outputs:{closeComponent:"closeComponent"},decls:60,vars:5,consts:[[1,"card-wrapper"],[1,"card-heading"],["mat-icon-button","",3,"click"],[1,"card-contents"],[3,"formGroup","ngSubmit"],["appearance","outline"],["matInput","","formControlName","fieldName"],["matInput","","formControlName","fieldType","readonly",""],[1,"extra_fields"],["formControlName","showEveryone",3,"change"],["formControlName","specificOrg",3,"change"],[4,"ngIf"],["formControlName","selectPeople",3,"change"],["formControlName","allowMultiSelect"],["formControlName","mandatoryField"],["formControlName","enableActivity"],["formControlName","enableNotification"],["formControlName","enableInlineEdit"],["formControlName","editableOnCreation"],["formControlName","editableOnUpdation"],["formControlName","showAssociateId"],["formControlName","uniqueField"],["formControlName","valueFromApi",3,"change"],[1,"d-flex","justify-content-center"],["type","button","mat-raised-button","","class","ml-2 btn-class",3,"click",4,"ngIf"],["formControlName","allowFieldControl",3,"change"],["mat-icon-button","","type","submit",1,"button-inactive","red"],[2,"font-size","16px"],["mat-icon-button","","type","button",1,"button-inactive"],[1,"iconButton",3,"click"],["formControlName","orgId","placeholder","Organization",3,"list","valueChange"],["formControlName","people","placeholder","Employees",3,"list"],["type","button","mat-raised-button","",1,"ml-2","btn-class",3,"click"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275elementStart"](1,"div",1),i["\u0275\u0275elementStart"](2,"button",2),i["\u0275\u0275listener"]("click",(function(){return t.close()})),i["\u0275\u0275elementStart"](3,"mat-icon"),i["\u0275\u0275text"](4,"keyboard_backspace"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"span"),i["\u0275\u0275text"](6,"Assign to"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](7,"div",3),i["\u0275\u0275elementStart"](8,"form",4),i["\u0275\u0275listener"]("ngSubmit",(function(){return t.onSubmit()})),i["\u0275\u0275elementStart"](9,"mat-form-field",5),i["\u0275\u0275elementStart"](10,"mat-label"),i["\u0275\u0275text"](11,"Field name"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](12,"input",6),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](13,"mat-form-field",5),i["\u0275\u0275elementStart"](14,"mat-label"),i["\u0275\u0275text"](15,"Field type"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](16,"input",7),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](17,"div",8),i["\u0275\u0275elementStart"](18,"mat-checkbox",9),i["\u0275\u0275listener"]("change",(function(){return t.toggleAssignScope("showEveryone",t.getValueFn("showEveryone"))})),i["\u0275\u0275text"](19,"Select everyone in organization"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](20,"mat-checkbox",10),i["\u0275\u0275listener"]("change",(function(){return t.toggleAssignScope("specificOrg",t.getValueFn("specificOrg"))})),i["\u0275\u0275text"](21,"Select specific Organization(s) "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](22,ge,2,1,"div",11),i["\u0275\u0275elementStart"](23,"mat-checkbox",12),i["\u0275\u0275listener"]("change",(function(){return t.toggleAssignScope("selectPeople",t.getValueFn("selectPeople"))})),i["\u0275\u0275text"](24,"Select People across Organization(s)"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](25,ue,2,1,"div",11),i["\u0275\u0275elementStart"](26,"mat-checkbox",13),i["\u0275\u0275text"](27,"Allow multi-select"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](28,"mat-checkbox",14),i["\u0275\u0275text"](29,"Mandatory field "),i["\u0275\u0275elementStart"](30,"sup"),i["\u0275\u0275text"](31,"*"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](32,"mat-checkbox",15),i["\u0275\u0275text"](33,"Enable Activity for field "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](34,"mat-checkbox",16),i["\u0275\u0275text"](35,"Enable Notification for field "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](36,"mat-checkbox",17),i["\u0275\u0275text"](37,"Enable Inline Edit for field "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](38,"mat-checkbox",18),i["\u0275\u0275text"](39,"Edit allowed on Creation"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](40,"mat-checkbox",19),i["\u0275\u0275text"](41,"Edit allowed on Updation"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](42,"mat-checkbox",20),i["\u0275\u0275text"](43,"Show Associate Id on dropdown"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](44,"mat-checkbox",21),i["\u0275\u0275text"](45,"Unique Field"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](46,"mat-checkbox",22),i["\u0275\u0275listener"]("change",(function(e){return t.toggleValueFromApi(e)})),i["\u0275\u0275text"](47,"Get Value from external api"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](48,"div",23),i["\u0275\u0275template"](49,he,2,0,"button",24),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](50,"mat-checkbox",25),i["\u0275\u0275listener"]("change",(function(e){return t.toggleAllowFieldControl(e)})),i["\u0275\u0275text"](51,"Enable Field Control (based on condition)"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](52,"div",23),i["\u0275\u0275template"](53,fe,2,0,"button",24),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](54,"button",26),i["\u0275\u0275elementStart"](55,"mat-icon",27),i["\u0275\u0275text"](56,"done_all"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](57,"button",28),i["\u0275\u0275elementStart"](58,"mat-icon",29),i["\u0275\u0275listener"]("click",(function(){return t.close()})),i["\u0275\u0275text"](59,"close"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](8),i["\u0275\u0275property"]("formGroup",t.assigntoForm),i["\u0275\u0275advance"](14),i["\u0275\u0275property"]("ngIf",t.assigntoForm.get("specificOrg").value),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngIf",t.assigntoForm.get("selectPeople").value),i["\u0275\u0275advance"](24),i["\u0275\u0275property"]("ngIf",t.assigntoForm.get("valueFromApi").value),i["\u0275\u0275advance"](4),i["\u0275\u0275property"]("ngIf",t.assigntoForm.get("allowFieldControl").value))},directives:[c.a,d.a,l.J,l.w,l.n,m.c,m.g,s.b,l.e,l.v,l.l,M.a,o.NgIf,pe.a],styles:[".card-wrapper[_ngcontent-%COMP%]{box-shadow:0 0 3px 0 #ccc;border-radius:3px;margin:5px 0 20px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]{padding-top:5px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-top:-4px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-weight:500;font-size:17px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]{min-width:220px;margin:10px 18px;padding-bottom:20px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{width:100%;font-size:12px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .button-inactive[_ngcontent-%COMP%]{line-height:8px;width:26px;height:26px;margin-right:12px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:16px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .red[_ngcontent-%COMP%]{background:#d41111;color:#fff;font-size:16px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .extra_fields[_ngcontent-%COMP%]{margin:10px 0}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .extra_fields[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]{display:block}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .extra_fields[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]     .mat-checkbox-inner-container{margin-right:14px;height:15px;width:15px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .extra_fields[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]     .mat-checkbox-label{font-size:13px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .extra_fields[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]   sup[_ngcontent-%COMP%]{color:red;font-size:1em;line-height:0;position:relative;vertical-align:initial;top:-.1em}"]}),e})();var be=n("dNgK");function xe(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",8),i["\u0275\u0275elementStart"](1,"mat-form-field",5),i["\u0275\u0275elementStart"](2,"mat-label"),i["\u0275\u0275text"](3,"Weightage"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](4,"input",31),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]())}function we(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",8),i["\u0275\u0275elementStart"](1,"mat-form-field",5),i["\u0275\u0275elementStart"](2,"mat-label"),i["\u0275\u0275text"](3,"Weightage (%)"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](4,"input",32),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]())}function _e(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",33),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"]().openApiConfigModal()})),i["\u0275\u0275text"](1," Api Config "),i["\u0275\u0275elementEnd"]()}}function Fe(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",33),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"]().openFieldControlConfigModal()})),i["\u0275\u0275text"](1," Field Control Config "),i["\u0275\u0275elementEnd"]()}}let ve=(()=>{class e{constructor(e,t,n){this._FieldsLibraryService=e,this._CustomFieldService=t,this.snackBar=n,this.closeComponent=new i.EventEmitter,this.msg=!1,this.valueChangeSubscription=new F.a}ngOnInit(){this.formFieldData=this._FieldsLibraryService.getFormFieldData(),this.numberForm=new l.m({fieldName:new l.j("",l.H.required),fieldType:new l.j(this.field.label),fieldId:new l.j(this.field.id),fieldIcon:new l.j(this.field.icon),rows:new l.j(this.field.default_rows),cols:new l.j(this.field.default_columns),setWeightage:new l.j(!1),setWeightagePercentage:new l.j(!1),weightagePercentage:new l.j(""),weightage:new l.j(""),minValue:new l.j(null),maxValue:new l.j(null),mandatoryField:new l.j(""),udrfColumnData:new l.j(this.field.udrf_column_data),enableActivity:new l.j(!1),enableNotification:new l.j(!1),editableOnCreation:new l.j(!0),editableOnUpdation:new l.j(!0),valueFromApi:new l.j(!1),allowFieldControl:new l.j(!1)}),this.valueChangeSubscription.add(this.numberForm.get("enableNotification").valueChanges.subscribe(e=>{e&&this.numberForm.get("enableActivity").patchValue(!0)})),this.valueChangeSubscription.add(this.numberForm.get("enableActivity").valueChanges.subscribe(e=>{e||this.numberForm.get("enableNotification").patchValue(!1)})),Object.keys(this.formFieldData).length>0&&this.patchvalues(this.formFieldData)}patchvalues(e){this.numberForm.addControl("x",new l.j("")),this.numberForm.addControl("y",new l.j("")),e._id&&this.numberForm.addControl("_id",new l.j(e._id)),e.mysql_column_name&&this.numberForm.addControl("mysql_column_name",new l.j(e.mysql_column_name)),this.numberForm.patchValue({fieldName:e.fieldName,fieldType:e.fieldType,fieldId:e.fieldId,fieldIcon:e.fieldIcon,rows:e.rows,cols:e.cols,x:e.x,y:e.y,setWeightage:e.setWeightage,setWeightagePercentage:e.setWeightagePercentage,weightagePercentage:e.weightagePercentage,weightage:e.weightage,minValue:e.minValue,maxValue:e.maxValue,mandatoryField:e.mandatoryField,udrfColumnData:e.udrfColumnData,enableActivity:!!e.enableActivity&&e.enableActivity,enableNotification:!!e.enableNotification&&e.enableNotification,editableOnCreation:null==e.editableOnCreation||e.editableOnCreation,editableOnUpdation:null==e.editableOnUpdation||e.editableOnUpdation,valueFromApi:e.valueFromApi,allowFieldControl:e.allowFieldControl}),e.valueFromApi&&(this.numberForm.addControl("apiConfig",new l.j(null)),this.numberForm.get("apiConfig").patchValue(e.apiConfig)),e.allowFieldControl&&(this.numberForm.addControl("fieldControlMapping",new l.j(null)),this.numberForm.get("fieldControlMapping").patchValue(e.fieldControlMapping))}onSubmit(){if(!this.numberForm.invalid)if("number"==typeof this.numberForm.controls.minValue.value&&"number"==typeof this.numberForm.controls.maxValue.value&&this.numberForm.controls.minValue.value>this.numberForm.controls.maxValue.value)this.snackBar.open("Min Value should be less than Max value","Dismiss",{duration:2e3});else{if(this.numberForm.controls.setWeightagePercentage.value){if(this.numberForm.controls.weightagePercentage.value<=0)return void this.snackBar.open("Set Weightage Percentage > 0","Dismiss",{duration:2e3})}else this.numberForm.controls.weightagePercentage.setValue("");if(this.numberForm.controls.setWeightage.value){if(this.numberForm.controls.weightage.value<=0)return void this.snackBar.open("Set Weightage > 0","Dismiss",{duration:2e3})}else this.numberForm.controls.weightage.setValue("");console.log(this.numberForm.value),this._CustomFieldService.addField(this.numberForm.value),this.closeComponent.emit(!0)}}close(){this.msg=!1,this._CustomFieldService.editCheck(this.msg),this.closeComponent.emit(!0)}toggleValueFromApi(e){e.checked&&!this.numberForm.contains("apiConfig")?this.numberForm.addControl("apiConfig",new l.j(null,l.H.required)):e.checked||this.numberForm.removeControl("apiConfig")}openApiConfigModal(){return Object(a.c)(this,void 0,void 0,(function*(){this._FieldsLibraryService.openApiConfigModal(this.numberForm)}))}toggleAllowFieldControl(e){e.checked&&!this.numberForm.contains("fieldControlMapping")?this.numberForm.addControl("fieldControlMapping",new l.j(null,l.H.required)):e.checked||this.numberForm.removeControl("fieldControlMapping")}openFieldControlConfigModal(){return Object(a.c)(this,void 0,void 0,(function*(){this._FieldsLibraryService.openFieldControlConfigModal(this.numberForm)}))}ngOnDestroy(){this.valueChangeSubscription&&this.valueChangeSubscription.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](O.a),i["\u0275\u0275directiveInject"](v.a),i["\u0275\u0275directiveInject"](be.a))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-number"]],inputs:{field:"field"},outputs:{closeComponent:"closeComponent"},decls:63,vars:6,consts:[[1,"card-wrapper"],[1,"card-heading"],["mat-icon-button","",3,"click"],[1,"card-contents"],[3,"formGroup","ngSubmit"],["appearance","outline"],["matInput","","formControlName","fieldName"],["matInput","","formControlName","fieldType","readonly","","value","Number"],[1,"row"],[1,"col-6","p-0","pr-1"],["matInput","","formControlName","minValue","type","number"],[1,"col-6","p-0","pl-1"],["matInput","","formControlName","maxValue","type","number",3,"min"],[1,"row","mandatoryFieldCheck"],["formControlName","setWeightage"],["class","row",4,"ngIf"],["formControlName","setWeightagePercentage"],[1,"mandatoryFieldCheck"],["formControlName","mandatoryField"],["formControlName","enableActivity"],["formControlName","enableNotification"],["formControlName","editableOnCreation"],["formControlName","editableOnUpdation"],["formControlName","valueFromApi",3,"change"],[1,"d-flex","justify-content-center"],["type","button","mat-raised-button","","class","ml-2 btn-class",3,"click",4,"ngIf"],["formControlName","allowFieldControl",3,"change"],["mat-icon-button","","type","submit",1,"button-inactive","red"],[2,"font-size","16px"],["mat-icon-button","","type","button",1,"button-inactive"],[1,"iconButton",3,"click"],["matInput","","formControlName","weightage","type","number"],["matInput","","formControlName","weightagePercentage","type","number"],["type","button","mat-raised-button","",1,"ml-2","btn-class",3,"click"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275elementStart"](1,"div",1),i["\u0275\u0275elementStart"](2,"button",2),i["\u0275\u0275listener"]("click",(function(){return t.close()})),i["\u0275\u0275elementStart"](3,"mat-icon"),i["\u0275\u0275text"](4,"keyboard_backspace"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"span"),i["\u0275\u0275text"](6,"Number"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](7,"div",3),i["\u0275\u0275elementStart"](8,"form",4),i["\u0275\u0275listener"]("ngSubmit",(function(){return t.onSubmit()})),i["\u0275\u0275elementStart"](9,"mat-form-field",5),i["\u0275\u0275elementStart"](10,"mat-label"),i["\u0275\u0275text"](11,"Field name"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](12,"input",6),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](13,"mat-form-field",5),i["\u0275\u0275elementStart"](14,"mat-label"),i["\u0275\u0275text"](15,"Field type"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](16,"input",7),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](17,"div",8),i["\u0275\u0275elementStart"](18,"div",9),i["\u0275\u0275elementStart"](19,"mat-form-field",5),i["\u0275\u0275elementStart"](20,"mat-label"),i["\u0275\u0275text"](21,"Min Value"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](22,"input",10),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](23,"div",11),i["\u0275\u0275elementStart"](24,"mat-form-field",5),i["\u0275\u0275elementStart"](25,"mat-label"),i["\u0275\u0275text"](26,"Max Value"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](27,"input",12),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](28,"div",13),i["\u0275\u0275elementStart"](29,"mat-checkbox",14),i["\u0275\u0275text"](30," Set Weightage "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](31,xe,5,0,"div",15),i["\u0275\u0275elementStart"](32,"div",13),i["\u0275\u0275elementStart"](33,"mat-checkbox",16),i["\u0275\u0275text"](34," Set Weightage (%)"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](35,we,5,0,"div",15),i["\u0275\u0275elementStart"](36,"div",17),i["\u0275\u0275elementStart"](37,"mat-checkbox",18),i["\u0275\u0275text"](38,"Mandatory field "),i["\u0275\u0275elementStart"](39,"sup"),i["\u0275\u0275text"](40,"*"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](41,"mat-checkbox",19),i["\u0275\u0275text"](42,"Enable Activity for field "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](43,"mat-checkbox",20),i["\u0275\u0275text"](44,"Enable Notification for field "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](45,"mat-checkbox",21),i["\u0275\u0275text"](46,"Edit allowed on Creation"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](47,"mat-checkbox",22),i["\u0275\u0275text"](48,"Edit allowed on Updation"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](49,"mat-checkbox",23),i["\u0275\u0275listener"]("change",(function(e){return t.toggleValueFromApi(e)})),i["\u0275\u0275text"](50,"Get Value from external api"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](51,"div",24),i["\u0275\u0275template"](52,_e,2,0,"button",25),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](53,"mat-checkbox",26),i["\u0275\u0275listener"]("change",(function(e){return t.toggleAllowFieldControl(e)})),i["\u0275\u0275text"](54,"Enable Field Control (based on condition)"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](55,"div",24),i["\u0275\u0275template"](56,Fe,2,0,"button",25),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](57,"button",27),i["\u0275\u0275elementStart"](58,"mat-icon",28),i["\u0275\u0275text"](59,"done_all"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](60,"button",29),i["\u0275\u0275elementStart"](61,"mat-icon",30),i["\u0275\u0275listener"]("click",(function(){return t.close()})),i["\u0275\u0275text"](62,"close"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](8),i["\u0275\u0275property"]("formGroup",t.numberForm),i["\u0275\u0275advance"](19),i["\u0275\u0275property"]("min",t.numberForm.get("minValue").value),i["\u0275\u0275advance"](4),i["\u0275\u0275property"]("ngIf",t.numberForm.get("setWeightage").value),i["\u0275\u0275advance"](4),i["\u0275\u0275property"]("ngIf",t.numberForm.get("setWeightagePercentage").value),i["\u0275\u0275advance"](17),i["\u0275\u0275property"]("ngIf",t.numberForm.get("valueFromApi").value),i["\u0275\u0275advance"](4),i["\u0275\u0275property"]("ngIf",t.numberForm.get("allowFieldControl").value))},directives:[c.a,d.a,l.J,l.w,l.n,m.c,m.g,s.b,l.e,l.v,l.l,l.A,M.a,o.NgIf],styles:[".card-wrapper[_ngcontent-%COMP%]{box-shadow:0 0 3px 0 #ccc;border-radius:3px;margin:5px 0 20px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]{padding-top:5px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-top:-4px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-weight:500;font-size:17px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]{min-width:220px;margin:10px 18px;padding-bottom:20px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{width:100%;font-size:12px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .button-inactive[_ngcontent-%COMP%]{line-height:8px;width:26px;height:26px;margin-right:12px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:16px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .red[_ngcontent-%COMP%]{background:#d41111;color:#fff;font-size:16px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .mandatoryFieldCheck[_ngcontent-%COMP%]{margin:10px 0}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .mandatoryFieldCheck[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]{display:block}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .mandatoryFieldCheck[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]     .mat-checkbox-inner-container{margin-right:14px;height:15px;width:15px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .mandatoryFieldCheck[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]     .mat-checkbox-label{font-size:13px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .mandatoryFieldCheck[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]   sup[_ngcontent-%COMP%]{color:red;font-size:1em;line-height:0;position:relative;vertical-align:initial;top:-.1em}"]}),e})();function Oe(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",22),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"]().openApiConfigModal()})),i["\u0275\u0275text"](1," Api Config "),i["\u0275\u0275elementEnd"]()}}let Me=(()=>{class e{constructor(e,t){this._FieldsLibraryService=e,this._CustomFieldService=t,this.closeComponent=new i.EventEmitter,this.msg=!1,this.valueChangeSubscription=new F.a}ngOnInit(){this.formFieldData=this._FieldsLibraryService.getFormFieldData(),this.websiteForm=new l.m({fieldName:new l.j("",l.H.required),fieldType:new l.j(this.field.label),fieldId:new l.j(this.field.id),fieldIcon:new l.j(this.field.icon),rows:new l.j(this.field.default_rows),cols:new l.j(this.field.default_columns),mandatoryField:new l.j(""),udrfColumnData:new l.j(this.field.udrf_column_data),enableActivity:new l.j(!1),enableNotification:new l.j(!1),editableOnCreation:new l.j(!0),editableOnUpdation:new l.j(!0),uniqueField:new l.j(!1),valueFromApi:new l.j(!1)}),this.valueChangeSubscription.add(this.websiteForm.get("enableNotification").valueChanges.subscribe(e=>{e&&this.websiteForm.get("enableActivity").patchValue(!0)})),this.valueChangeSubscription.add(this.websiteForm.get("enableActivity").valueChanges.subscribe(e=>{e||this.websiteForm.get("enableNotification").patchValue(!1)})),Object.keys(this.formFieldData).length>0&&this.patchvalues(this.formFieldData)}patchvalues(e){this.websiteForm.addControl("x",new l.j("")),this.websiteForm.addControl("y",new l.j("")),e._id&&this.websiteForm.addControl("_id",new l.j(e._id)),e.mysql_column_name&&this.websiteForm.addControl("mysql_column_name",new l.j(e.mysql_column_name)),this.websiteForm.patchValue({fieldName:e.fieldName,fieldType:e.fieldType,fieldId:e.fieldId,fieldIcon:e.fieldIcon,rows:e.rows,cols:e.cols,x:e.x,y:e.y,mandatoryField:e.mandatoryField,udrfColumnData:e.udrfColumnData,enableActivity:!!e.enableActivity&&e.enableActivity,enableNotification:!!e.enableNotification&&e.enableNotification,editableOnCreation:null==e.editableOnCreation||e.editableOnCreation,editableOnUpdation:null==e.editableOnUpdation||e.editableOnUpdation,uniqueField:e.uniqueField,valueFromApi:e.valueFromApi}),e.valueFromApi&&(this.websiteForm.addControl("apiConfig",new l.j(null)),this.websiteForm.get("apiConfig").patchValue(e.apiConfig))}onSubmit(){this.websiteForm.invalid||(console.log(this.websiteForm.value),this._CustomFieldService.addField(this.websiteForm.value),this.closeComponent.emit(!0))}close(){this.msg=!1,this._CustomFieldService.editCheck(this.msg),this.closeComponent.emit(!0)}toggleValueFromApi(e){e.checked&&!this.websiteForm.contains("apiConfig")?this.websiteForm.addControl("apiConfig",new l.j(null,l.H.required)):e.checked||this.websiteForm.removeControl("apiConfig")}openApiConfigModal(){return Object(a.c)(this,void 0,void 0,(function*(){this._FieldsLibraryService.openApiConfigModal(this.websiteForm)}))}ngOnDestroy(){this.valueChangeSubscription&&this.valueChangeSubscription.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](O.a),i["\u0275\u0275directiveInject"](v.a))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-website"]],inputs:{field:"field"},outputs:{closeComponent:"closeComponent"},decls:42,vars:2,consts:[[1,"card-wrapper"],[1,"card-heading"],["mat-icon-button","",3,"click"],[1,"card-contents"],[3,"formGroup","ngSubmit"],["appearance","outline"],["matInput","","formControlName","fieldName"],["matInput","","formControlName","fieldType","readonly","","value","Website"],[1,"mandatoryFieldCheck"],["formControlName","mandatoryField"],["formControlName","enableActivity"],["formControlName","enableNotification"],["formControlName","editableOnCreation"],["formControlName","editableOnUpdation"],["formControlName","uniqueField"],["formControlName","valueFromApi",3,"change"],[1,"d-flex","justify-content-center"],["type","button","mat-raised-button","","class","ml-2 btn-class",3,"click",4,"ngIf"],["mat-icon-button","","type","submit",1,"button-inactive","red"],[2,"font-size","16px"],["mat-icon-button","","type","button",1,"button-inactive"],[1,"iconButton",3,"click"],["type","button","mat-raised-button","",1,"ml-2","btn-class",3,"click"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275elementStart"](1,"div",1),i["\u0275\u0275elementStart"](2,"button",2),i["\u0275\u0275listener"]("click",(function(){return t.close()})),i["\u0275\u0275elementStart"](3,"mat-icon"),i["\u0275\u0275text"](4,"keyboard_backspace"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"span"),i["\u0275\u0275text"](6,"Website"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](7,"div",3),i["\u0275\u0275elementStart"](8,"form",4),i["\u0275\u0275listener"]("ngSubmit",(function(){return t.onSubmit()})),i["\u0275\u0275elementStart"](9,"mat-form-field",5),i["\u0275\u0275elementStart"](10,"mat-label"),i["\u0275\u0275text"](11,"Field name"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](12,"input",6),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](13,"mat-form-field",5),i["\u0275\u0275elementStart"](14,"mat-label"),i["\u0275\u0275text"](15,"Field type"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](16,"input",7),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](17,"div",8),i["\u0275\u0275elementStart"](18,"mat-checkbox",9),i["\u0275\u0275text"](19,"Mandatory field "),i["\u0275\u0275elementStart"](20,"sup"),i["\u0275\u0275text"](21,"*"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](22,"mat-checkbox",10),i["\u0275\u0275text"](23,"Enable Activity for field "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](24,"mat-checkbox",11),i["\u0275\u0275text"](25,"Enable Notification for field "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](26,"mat-checkbox",12),i["\u0275\u0275text"](27,"Edit allowed on Creation"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](28,"mat-checkbox",13),i["\u0275\u0275text"](29,"Edit allowed on Updation"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](30,"mat-checkbox",14),i["\u0275\u0275text"](31,"Unique Field"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](32,"mat-checkbox",15),i["\u0275\u0275listener"]("change",(function(e){return t.toggleValueFromApi(e)})),i["\u0275\u0275text"](33,"Get Value from external api"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](34,"div",16),i["\u0275\u0275template"](35,Oe,2,0,"button",17),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](36,"button",18),i["\u0275\u0275elementStart"](37,"mat-icon",19),i["\u0275\u0275text"](38,"done_all"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](39,"button",20),i["\u0275\u0275elementStart"](40,"mat-icon",21),i["\u0275\u0275listener"]("click",(function(){return t.close()})),i["\u0275\u0275text"](41,"close"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](8),i["\u0275\u0275property"]("formGroup",t.websiteForm),i["\u0275\u0275advance"](27),i["\u0275\u0275property"]("ngIf",t.websiteForm.get("valueFromApi").value))},directives:[c.a,d.a,l.J,l.w,l.n,m.c,m.g,s.b,l.e,l.v,l.l,M.a,o.NgIf],styles:[".card-wrapper[_ngcontent-%COMP%]{box-shadow:0 0 3px 0 #ccc;border-radius:3px;margin:5px 0 20px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]{padding-top:5px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-top:-4px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-weight:500;font-size:17px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]{min-width:220px;margin:10px 18px;padding-bottom:20px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{width:100%;font-size:12px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .button-inactive[_ngcontent-%COMP%]{line-height:8px;width:26px;height:26px;margin-right:12px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:16px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .red[_ngcontent-%COMP%]{background:#d41111;color:#fff;font-size:16px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .mandatoryFieldCheck[_ngcontent-%COMP%]{margin:10px 0}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .mandatoryFieldCheck[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]{display:block}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .mandatoryFieldCheck[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]     .mat-checkbox-inner-container{margin-right:14px;height:15px;width:15px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .mandatoryFieldCheck[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]     .mat-checkbox-label{font-size:13px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .mandatoryFieldCheck[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]   sup[_ngcontent-%COMP%]{color:red;font-size:1em;line-height:0;position:relative;vertical-align:initial;top:-.1em}"]}),e})();function ye(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",31),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"]().openApiConfigModal()})),i["\u0275\u0275text"](1," Api Config "),i["\u0275\u0275elementEnd"]()}}function Se(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",31),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"]().openFieldControlConfigModal()})),i["\u0275\u0275text"](1," Field Control Config "),i["\u0275\u0275elementEnd"]()}}let Pe=(()=>{class e{constructor(e,t){this._FieldsLibraryService=e,this._CustomFieldService=t,this.closeComponent=new i.EventEmitter,this.msg=!1,this.valueChangeSubscription=new F.a}ngOnInit(){this.formFieldData=this._FieldsLibraryService.getFormFieldData(),this.priorityForm=new l.m({fieldName:new l.j("",l.H.required),fieldType:new l.j(this.field.label),fieldId:new l.j(this.field.id),fieldIcon:new l.j(this.field.icon),rows:new l.j(this.field.default_rows),cols:new l.j(this.field.default_columns),mandatoryField:new l.j(""),udrfColumnData:new l.j(this.field.udrf_column_data),enableActivity:new l.j(!1),enableNotification:new l.j(!1),enableInlineEdit:new l.j(!1),editableOnCreation:new l.j(!0),editableOnUpdation:new l.j(!0),valueFromApi:new l.j(!1),allowFieldControl:new l.j(!1)}),this.valueChangeSubscription.add(this.priorityForm.get("enableNotification").valueChanges.subscribe(e=>{e&&this.priorityForm.get("enableActivity").patchValue(!0)})),this.valueChangeSubscription.add(this.priorityForm.get("enableActivity").valueChanges.subscribe(e=>{e||this.priorityForm.get("enableNotification").patchValue(!1)})),Object.keys(this.formFieldData).length>0&&this.patchvalues(this.formFieldData)}patchvalues(e){this.priorityForm.addControl("x",new l.j("")),this.priorityForm.addControl("y",new l.j("")),e._id&&this.priorityForm.addControl("_id",new l.j(e._id)),e.mysql_column_name&&this.priorityForm.addControl("mysql_column_name",new l.j(e.mysql_column_name)),this.priorityForm.patchValue({fieldName:e.fieldName,fieldType:e.fieldType,fieldId:e.fieldId,fieldIcon:e.fieldIcon,rows:e.rows,cols:e.cols,x:e.x,y:e.y,mandatoryField:e.mandatoryField,udrfColumnData:e.udrfColumnData,enableActivity:!!e.enableActivity&&e.enableActivity,enableNotification:!!e.enableNotification&&e.enableNotification,enableInlineEdit:!!e.enableInlineEdit&&e.enableInlineEdit,editableOnCreation:null==e.editableOnCreation||e.editableOnCreation,editableOnUpdation:null==e.editableOnUpdation||e.editableOnUpdation,valueFromApi:e.valueFromApi,allowFieldControl:e.allowFieldControl}),e.valueFromApi&&(this.priorityForm.addControl("apiConfig",new l.j(null)),this.priorityForm.get("apiConfig").patchValue(e.apiConfig)),e.allowFieldControl&&(this.priorityForm.addControl("fieldControlMapping",new l.j(null)),this.priorityForm.get("fieldControlMapping").patchValue(e.fieldControlMapping))}onSubmit(){this.priorityForm.invalid||(console.log(this.priorityForm.value),this._CustomFieldService.addField(this.priorityForm.value),this.closeComponent.emit(!0))}close(){this.msg=!1,this._CustomFieldService.editCheck(this.msg),this.closeComponent.emit(!0)}toggleValueFromApi(e){e.checked&&!this.priorityForm.contains("apiConfig")?this.priorityForm.addControl("apiConfig",new l.j(null,l.H.required)):e.checked||this.priorityForm.removeControl("apiConfig")}openApiConfigModal(){return Object(a.c)(this,void 0,void 0,(function*(){this._FieldsLibraryService.openApiConfigModal(this.priorityForm)}))}toggleAllowFieldControl(e){e.checked&&!this.priorityForm.contains("fieldControlMapping")?this.priorityForm.addControl("fieldControlMapping",new l.j(null,l.H.required)):e.checked||this.priorityForm.removeControl("fieldControlMapping")}openFieldControlConfigModal(){return Object(a.c)(this,void 0,void 0,(function*(){this._FieldsLibraryService.openFieldControlConfigModal(this.priorityForm)}))}ngOnDestroy(){this.valueChangeSubscription&&this.valueChangeSubscription.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](O.a),i["\u0275\u0275directiveInject"](v.a))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-priority"]],inputs:{field:"field"},outputs:{closeComponent:"closeComponent"},decls:71,vars:3,consts:[[1,"card-wrapper"],[1,"card-heading"],["mat-icon-button","",3,"click"],[1,"card-contents"],[3,"formGroup","ngSubmit"],["appearance","outline"],["matInput","","formControlName","fieldName"],["matInput","","formControlName","fieldType","readonly","","value","Priority"],[1,"d-flex","align-items-center","my-3"],[1,"mr-1",2,"color","#cf0000","font-size","20px"],[1,"d-flex","align-items-center","mt-2"],[1,"mr-1",2,"color","#cf0000","font-size","18px"],[2,"font-size","13px"],[1,"mr-1",2,"color","green","font-size","18px"],[1,"mr-1",2,"color","yellow","font-size","18px"],[1,"mr-1",2,"color","grey","font-size","18px"],[1,"mandatoryFieldCheck","mt-3"],["formControlName","mandatoryField"],["formControlName","enableActivity"],["formControlName","enableNotification"],["formControlName","enableInlineEdit"],["formControlName","editableOnCreation"],["formControlName","editableOnUpdation"],["formControlName","valueFromApi",3,"change"],[1,"d-flex","justify-content-center"],["type","button","mat-raised-button","","class","ml-2 btn-class",3,"click",4,"ngIf"],["formControlName","allowFieldControl",3,"change"],["mat-icon-button","","type","submit",1,"button-inactive","red"],[2,"font-size","16px"],["mat-icon-button","","type","button",1,"button-inactive"],[1,"iconButton",3,"click"],["type","button","mat-raised-button","",1,"ml-2","btn-class",3,"click"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275elementStart"](1,"div",1),i["\u0275\u0275elementStart"](2,"button",2),i["\u0275\u0275listener"]("click",(function(){return t.close()})),i["\u0275\u0275elementStart"](3,"mat-icon"),i["\u0275\u0275text"](4,"keyboard_backspace"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"span"),i["\u0275\u0275text"](6,"Priority"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](7,"div",3),i["\u0275\u0275elementStart"](8,"form",4),i["\u0275\u0275listener"]("ngSubmit",(function(){return t.onSubmit()})),i["\u0275\u0275elementStart"](9,"mat-form-field",5),i["\u0275\u0275elementStart"](10,"mat-label"),i["\u0275\u0275text"](11,"Field name"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](12,"input",6),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](13,"mat-form-field",5),i["\u0275\u0275elementStart"](14,"mat-label"),i["\u0275\u0275text"](15,"Field type"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](16,"input",7),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](17,"div",8),i["\u0275\u0275elementStart"](18,"mat-icon",9),i["\u0275\u0275text"](19,"info"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](20,"div"),i["\u0275\u0275text"](21,"Priority states"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](22,"div",10),i["\u0275\u0275elementStart"](23,"mat-icon",11),i["\u0275\u0275text"](24,"flag"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](25,"div",12),i["\u0275\u0275text"](26,"Very High"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](27,"div",10),i["\u0275\u0275elementStart"](28,"mat-icon",13),i["\u0275\u0275text"](29,"flag"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](30,"div",12),i["\u0275\u0275text"](31,"High"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](32,"div",10),i["\u0275\u0275elementStart"](33,"mat-icon",14),i["\u0275\u0275text"](34,"flag"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](35,"div",12),i["\u0275\u0275text"](36,"Medium"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](37,"div",10),i["\u0275\u0275elementStart"](38,"mat-icon",15),i["\u0275\u0275text"](39,"flag"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](40,"div",12),i["\u0275\u0275text"](41,"Low"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](42,"div",16),i["\u0275\u0275elementStart"](43,"mat-checkbox",17),i["\u0275\u0275text"](44,"Mandatory field "),i["\u0275\u0275elementStart"](45,"sup"),i["\u0275\u0275text"](46,"*"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](47,"mat-checkbox",18),i["\u0275\u0275text"](48,"Enable Activity for field "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](49,"mat-checkbox",19),i["\u0275\u0275text"](50,"Enable Notification for field "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](51,"mat-checkbox",20),i["\u0275\u0275text"](52,"Enable Inline Edit for field "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](53,"mat-checkbox",21),i["\u0275\u0275text"](54,"Edit allowed on Creation"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](55,"mat-checkbox",22),i["\u0275\u0275text"](56,"Edit allowed on Updation"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](57,"mat-checkbox",23),i["\u0275\u0275listener"]("change",(function(e){return t.toggleValueFromApi(e)})),i["\u0275\u0275text"](58,"Get Value from external api"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](59,"div",24),i["\u0275\u0275template"](60,ye,2,0,"button",25),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](61,"mat-checkbox",26),i["\u0275\u0275listener"]("change",(function(e){return t.toggleAllowFieldControl(e)})),i["\u0275\u0275text"](62,"Enable Field Control (based on condition)"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](63,"div",24),i["\u0275\u0275template"](64,Se,2,0,"button",25),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](65,"button",27),i["\u0275\u0275elementStart"](66,"mat-icon",28),i["\u0275\u0275text"](67,"done_all"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](68,"button",29),i["\u0275\u0275elementStart"](69,"mat-icon",30),i["\u0275\u0275listener"]("click",(function(){return t.close()})),i["\u0275\u0275text"](70,"close"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](8),i["\u0275\u0275property"]("formGroup",t.priorityForm),i["\u0275\u0275advance"](52),i["\u0275\u0275property"]("ngIf",t.priorityForm.get("valueFromApi").value),i["\u0275\u0275advance"](4),i["\u0275\u0275property"]("ngIf",t.priorityForm.get("allowFieldControl").value))},directives:[c.a,d.a,l.J,l.w,l.n,m.c,m.g,s.b,l.e,l.v,l.l,M.a,o.NgIf],styles:[".card-wrapper[_ngcontent-%COMP%]{box-shadow:0 0 3px 0 #ccc;border-radius:3px;margin:5px 0 20px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]{padding-top:5px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-top:-4px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-weight:500;font-size:17px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]{min-width:220px;margin:10px 18px;padding-bottom:20px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{width:100%;font-size:12px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .button-inactive[_ngcontent-%COMP%]{line-height:8px;width:26px;height:26px;margin-right:12px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:16px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .red[_ngcontent-%COMP%]{background:#d41111;color:#fff;font-size:16px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .mandatoryFieldCheck[_ngcontent-%COMP%]{margin:10px 0}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .mandatoryFieldCheck[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]{display:block}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .mandatoryFieldCheck[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]     .mat-checkbox-inner-container{margin-right:14px;height:15px;width:15px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .mandatoryFieldCheck[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]     .mat-checkbox-label{font-size:13px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .mandatoryFieldCheck[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]   sup[_ngcontent-%COMP%]{color:red;font-size:1em;line-height:0;position:relative;vertical-align:initial;top:-.1em}"]}),e})();function Ee(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",20),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"]().openApiConfigModal()})),i["\u0275\u0275text"](1," Api Config "),i["\u0275\u0275elementEnd"]()}}function ke(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",20),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"]().openFieldControlConfigModal()})),i["\u0275\u0275text"](1," Field Control Config "),i["\u0275\u0275elementEnd"]()}}let je=(()=>{class e{constructor(e,t){this._FieldsLibraryService=e,this._CustomFieldService=t,this.closeComponent=new i.EventEmitter,this.msg=!1}ngOnInit(){this.formFieldData=this._FieldsLibraryService.getFormFieldData(),this.checkboxForm=new l.m({fieldName:new l.j("",l.H.required),fieldType:new l.j(this.field.label),fieldId:new l.j(this.field.id),fieldIcon:new l.j(this.field.icon),rows:new l.j(this.field.default_rows),cols:new l.j(this.field.default_columns),mandatoryField:new l.j(""),udrfColumnData:new l.j(this.field.udrf_column_data),editableOnCreation:new l.j(!0),editableOnUpdation:new l.j(!0),valueFromApi:new l.j(!1),allowFieldControl:new l.j(!1)}),Object.keys(this.formFieldData).length>0&&this.patchvalues(this.formFieldData)}patchvalues(e){this.checkboxForm.addControl("x",new l.j("")),this.checkboxForm.addControl("y",new l.j("")),e._id&&this.checkboxForm.addControl("_id",new l.j(e._id)),e.mysql_column_name&&this.checkboxForm.addControl("mysql_column_name",new l.j(e.mysql_column_name)),this.checkboxForm.patchValue({fieldName:e.fieldName,fieldType:e.fieldType,fieldId:e.fieldId,fieldIcon:e.fieldIcon,rows:e.rows,cols:e.cols,x:e.x,y:e.y,mandatoryField:e.mandatoryField,udrfColumnData:e.udrfColumnData,editableOnCreation:null==e.editableOnCreation||e.editableOnCreation,editableOnUpdation:null==e.editableOnUpdation||e.editableOnUpdation,valueFromApi:e.valueFromApi,allowFieldControl:e.allowFieldControl}),e.valueFromApi&&(this.checkboxForm.addControl("apiConfig",new l.j(null)),this.checkboxForm.get("apiConfig").patchValue(e.apiConfig)),e.allowFieldControl&&(this.checkboxForm.addControl("fieldControlMapping",new l.j(null)),this.checkboxForm.get("fieldControlMapping").patchValue(e.fieldControlMapping))}onSubmit(){this.checkboxForm.invalid||(console.log(this.checkboxForm.value),this._CustomFieldService.addField(this.checkboxForm.value),this.closeComponent.emit(!0))}close(){this.msg=!1,this._CustomFieldService.editCheck(this.msg),this.closeComponent.emit(!0)}toggleValueFromApi(e){e.checked&&!this.checkboxForm.contains("apiConfig")?this.checkboxForm.addControl("apiConfig",new l.j(null,l.H.required)):e.checked||this.checkboxForm.removeControl("apiConfig")}openApiConfigModal(){return Object(a.c)(this,void 0,void 0,(function*(){this._FieldsLibraryService.openApiConfigModal(this.checkboxForm)}))}toggleAllowFieldControl(e){e.checked&&!this.checkboxForm.contains("fieldControlMapping")?this.checkboxForm.addControl("fieldControlMapping",new l.j(null,l.H.required)):e.checked||this.checkboxForm.removeControl("fieldControlMapping")}openFieldControlConfigModal(){return Object(a.c)(this,void 0,void 0,(function*(){this._FieldsLibraryService.openFieldControlConfigModal(this.checkboxForm)}))}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](O.a),i["\u0275\u0275directiveInject"](v.a))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-checkbox"]],inputs:{field:"field"},outputs:{closeComponent:"closeComponent"},decls:40,vars:3,consts:[[1,"card-wrapper"],[1,"card-heading"],["mat-icon-button","",3,"click"],[1,"card-contents"],[3,"formGroup","ngSubmit"],["appearance","outline"],["matInput","","formControlName","fieldName"],["matInput","","formControlName","fieldType","readonly","","value","Number"],[1,"mandatoryFieldCheck"],["formControlName","mandatoryField"],["formControlName","editableOnCreation"],["formControlName","editableOnUpdation"],["formControlName","valueFromApi",3,"change"],[1,"d-flex","justify-content-center"],["type","button","mat-raised-button","","class","ml-2 btn-class",3,"click",4,"ngIf"],["formControlName","allowFieldControl",3,"change"],["mat-icon-button","","type","submit",1,"button-inactive","red"],[2,"font-size","16px"],["mat-icon-button","","type","button",1,"button-inactive"],[1,"iconButton",3,"click"],["type","button","mat-raised-button","",1,"ml-2","btn-class",3,"click"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275elementStart"](1,"div",1),i["\u0275\u0275elementStart"](2,"button",2),i["\u0275\u0275listener"]("click",(function(){return t.close()})),i["\u0275\u0275elementStart"](3,"mat-icon"),i["\u0275\u0275text"](4,"keyboard_backspace"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"span"),i["\u0275\u0275text"](6,"Checkbox"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](7,"div",3),i["\u0275\u0275elementStart"](8,"form",4),i["\u0275\u0275listener"]("ngSubmit",(function(){return t.onSubmit()})),i["\u0275\u0275elementStart"](9,"mat-form-field",5),i["\u0275\u0275elementStart"](10,"mat-label"),i["\u0275\u0275text"](11,"Field name"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](12,"input",6),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](13,"mat-form-field",5),i["\u0275\u0275elementStart"](14,"mat-label"),i["\u0275\u0275text"](15,"Field type"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](16,"input",7),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](17,"div",8),i["\u0275\u0275elementStart"](18,"mat-checkbox",9),i["\u0275\u0275text"](19,"Mandatory field "),i["\u0275\u0275elementStart"](20,"sup"),i["\u0275\u0275text"](21,"*"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](22,"mat-checkbox",10),i["\u0275\u0275text"](23,"Edit allowed on Creation"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](24,"mat-checkbox",11),i["\u0275\u0275text"](25,"Edit allowed on Updation"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](26,"mat-checkbox",12),i["\u0275\u0275listener"]("change",(function(e){return t.toggleValueFromApi(e)})),i["\u0275\u0275text"](27,"Get Value from external api"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](28,"div",13),i["\u0275\u0275template"](29,Ee,2,0,"button",14),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](30,"mat-checkbox",15),i["\u0275\u0275listener"]("change",(function(e){return t.toggleAllowFieldControl(e)})),i["\u0275\u0275text"](31,"Enable Field Control (based on condition)"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](32,"div",13),i["\u0275\u0275template"](33,ke,2,0,"button",14),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](34,"button",16),i["\u0275\u0275elementStart"](35,"mat-icon",17),i["\u0275\u0275text"](36,"done_all"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](37,"button",18),i["\u0275\u0275elementStart"](38,"mat-icon",19),i["\u0275\u0275listener"]("click",(function(){return t.close()})),i["\u0275\u0275text"](39,"close"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](8),i["\u0275\u0275property"]("formGroup",t.checkboxForm),i["\u0275\u0275advance"](21),i["\u0275\u0275property"]("ngIf",t.checkboxForm.get("valueFromApi").value),i["\u0275\u0275advance"](4),i["\u0275\u0275property"]("ngIf",t.checkboxForm.get("allowFieldControl").value))},directives:[c.a,d.a,l.J,l.w,l.n,m.c,m.g,s.b,l.e,l.v,l.l,M.a,o.NgIf],styles:[".card-wrapper[_ngcontent-%COMP%]{box-shadow:0 0 3px 0 #ccc;border-radius:3px;margin:5px 0 20px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]{padding-top:5px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-top:-4px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-weight:500;font-size:17px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]{min-width:220px;margin:10px 18px;padding-bottom:20px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{width:100%;font-size:12px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .button-inactive[_ngcontent-%COMP%]{line-height:8px;width:26px;height:26px;margin-right:12px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:16px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .red[_ngcontent-%COMP%]{background:#d41111;color:#fff;font-size:16px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .mandatoryFieldCheck[_ngcontent-%COMP%]{margin:10px 0}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .mandatoryFieldCheck[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]{display:block}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .mandatoryFieldCheck[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]     .mat-checkbox-inner-container{margin-right:14px;height:15px;width:15px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .mandatoryFieldCheck[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]     .mat-checkbox-label{font-size:13px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .mandatoryFieldCheck[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]   sup[_ngcontent-%COMP%]{color:red;font-size:1em;line-height:0;position:relative;vertical-align:initial;top:-.1em}"]}),e})(),Ne=(()=>{class e{constructor(e,t){this._FieldsLibraryService=e,this._CustomFieldService=t,this.closeComponent=new i.EventEmitter,this.msg=!1}ngOnInit(){this.formFieldData=this._FieldsLibraryService.getFormFieldData(),this.filesForm=new l.m({fieldName:new l.j("",l.H.required),fieldType:new l.j(this.field.label),fieldId:new l.j(this.field.id),fieldIcon:new l.j(this.field.icon),rows:new l.j(this.field.default_rows),cols:new l.j(this.field.default_columns),fileTypes:new l.m({PDF:new l.j(!1),DocX:new l.j(!1),Xslx:new l.j(!1),PPTx:new l.j(!1),JPEG:new l.j(!1),PNG:new l.j(!1)}),createTemplate:new l.j(!1),mandatoryField:new l.j(""),udrfColumnData:new l.j(this.field.udrf_column_data)}),Object.keys(this.formFieldData).length>0&&this.patchvalues(this.formFieldData)}patchvalues(e){this.filesForm.addControl("x",new l.j("")),this.filesForm.addControl("y",new l.j("")),e._id&&this.filesForm.addControl("_id",new l.j(e._id)),e.mysql_column_name&&this.filesForm.addControl("mysql_column_name",new l.j(e.mysql_column_name)),this.filesForm.patchValue({fieldName:e.fieldName,fieldType:e.fieldType,fieldId:e.fieldId,fieldIcon:e.fieldIcon,rows:e.rows,cols:e.cols,x:e.x,y:e.y,createTemplate:e.createTemplate,mandatoryField:e.mandatoryField,udrfColumnData:e.udrfColumnData}),this.filesForm.patchValue({fileTypes:{PDF:e.fileTypes.PDF,DocX:e.fileTypes.DocX,Xslx:e.fileTypes.Xslx,PPTx:e.fileTypes.PPTx,JPEG:e.fileTypes.JPEG,PNG:e.fileTypes.PNG}})}onSubmit(){this.filesForm.invalid||(console.log(this.filesForm.value),this._CustomFieldService.addField(this.filesForm.value),this.closeComponent.emit(!0))}close(){this.msg=!1,this._CustomFieldService.editCheck(this.msg),this.closeComponent.emit(!0)}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](O.a),i["\u0275\u0275directiveInject"](v.a))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-files"]],inputs:{field:"field"},outputs:{closeComponent:"closeComponent"},decls:46,vars:1,consts:[[1,"card-wrapper"],[1,"card-heading"],["mat-icon-button","",3,"click"],[1,"card-contents"],[3,"formGroup","ngSubmit"],["appearance","outline"],["matInput","","formControlName","fieldName"],["matInput","","formControlName","fieldType","readonly","","value","Number"],[1,"d-flex","align-items-center","my-3"],[1,"mr-1",2,"color","#cf0000","font-size","20px"],["formGroupName","fileTypes"],["formControlName","PDF"],["formControlName","DocX"],["formControlName","Xslx"],["formControlName","PPTx"],["formControlName","JPEG"],["formControlName","PNG"],[1,"mandatoryFieldCheck"],["formControlName","mandatoryField"],["mat-icon-button","","type","submit",1,"button-inactive","red"],[2,"font-size","16px"],["mat-icon-button","","type","button",1,"button-inactive"],[1,"iconButton",3,"click"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275elementStart"](1,"div",1),i["\u0275\u0275elementStart"](2,"button",2),i["\u0275\u0275listener"]("click",(function(){return t.close()})),i["\u0275\u0275elementStart"](3,"mat-icon"),i["\u0275\u0275text"](4,"keyboard_backspace"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"span"),i["\u0275\u0275text"](6,"Files"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](7,"div",3),i["\u0275\u0275elementStart"](8,"form",4),i["\u0275\u0275listener"]("ngSubmit",(function(){return t.onSubmit()})),i["\u0275\u0275elementStart"](9,"mat-form-field",5),i["\u0275\u0275elementStart"](10,"mat-label"),i["\u0275\u0275text"](11,"Field name"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](12,"input",6),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](13,"mat-form-field",5),i["\u0275\u0275elementStart"](14,"mat-label"),i["\u0275\u0275text"](15,"Field type"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](16,"input",7),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](17,"div",8),i["\u0275\u0275elementStart"](18,"mat-icon",9),i["\u0275\u0275text"](19,"info"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](20,"div"),i["\u0275\u0275text"](21,"Allowed field types"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementContainerStart"](22,10),i["\u0275\u0275elementStart"](23,"mat-checkbox",11),i["\u0275\u0275text"](24,"PDF"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](25,"mat-checkbox",12),i["\u0275\u0275text"](26,"DocX(Word)"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](27,"mat-checkbox",13),i["\u0275\u0275text"](28,"Xslx(Excel)"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](29,"mat-checkbox",14),i["\u0275\u0275text"](30,"PPTx(Powerpoint)"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](31,"mat-checkbox",15),i["\u0275\u0275text"](32,"JPEG(Image)"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](33,"mat-checkbox",16),i["\u0275\u0275text"](34,"PNG(Image)"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementContainerEnd"](),i["\u0275\u0275elementStart"](35,"div",17),i["\u0275\u0275elementStart"](36,"mat-checkbox",18),i["\u0275\u0275text"](37,"Mandatory field "),i["\u0275\u0275elementStart"](38,"sup"),i["\u0275\u0275text"](39,"*"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](40,"button",19),i["\u0275\u0275elementStart"](41,"mat-icon",20),i["\u0275\u0275text"](42,"done_all"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](43,"button",21),i["\u0275\u0275elementStart"](44,"mat-icon",22),i["\u0275\u0275listener"]("click",(function(){return t.close()})),i["\u0275\u0275text"](45,"close"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](8),i["\u0275\u0275property"]("formGroup",t.filesForm))},directives:[c.a,d.a,l.J,l.w,l.n,m.c,m.g,s.b,l.e,l.v,l.l,l.o,M.a],styles:[".card-wrapper[_ngcontent-%COMP%]{box-shadow:0 0 3px 0 #ccc;border-radius:3px;margin:5px 0 20px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]{padding-top:5px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-top:-4px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-weight:500;font-size:17px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]{min-width:220px;margin:10px 18px;padding-bottom:20px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{width:100%;font-size:12px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .button-inactive[_ngcontent-%COMP%]{line-height:8px;width:26px;height:26px;margin-right:12px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:16px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .red[_ngcontent-%COMP%]{background:#d41111;color:#fff;font-size:16px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .mandatoryFieldCheck[_ngcontent-%COMP%]{margin:10px 0}mat-checkbox[_ngcontent-%COMP%]{display:block}mat-checkbox[_ngcontent-%COMP%]     .mat-checkbox-inner-container{margin-right:14px;height:15px;width:15px}mat-checkbox[_ngcontent-%COMP%]     .mat-checkbox-label{font-size:13px}mat-checkbox[_ngcontent-%COMP%]   sup[_ngcontent-%COMP%]{color:red;font-size:1em;line-height:0;position:relative;vertical-align:initial;top:-.1em}"]}),e})();var Ie=n("xG9w"),Ae=n("O/Cq");function Ve(e,t){if(1&e&&i["\u0275\u0275element"](0,"app-multi-select-search-customfield",21),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275property"]("placeholder","Select Fields")("list",e.fieldsList)}}let De=(()=>{class e{constructor(e,t){this._FieldsLibraryService=e,this._CustomFieldService=t,this.closeComponent=new i.EventEmitter,this.msg=!1,this.fieldsList=[]}ngOnInit(){this.formFieldData=this._FieldsLibraryService.getFormFieldData(),this.autogenForm=new l.m({fieldName:new l.j("",l.H.required),fieldType:new l.j(this.field.label),fieldId:new l.j(this.field.id),fieldIcon:new l.j(this.field.icon),rows:new l.j(this.field.default_rows),cols:new l.j(this.field.default_columns),prefix:new l.j(""),currentValue:new l.j(0),startWithOne:new l.j(!1),mandatoryField:new l.j(!1),udrfColumnData:new l.j(this.field.udrf_column_data),applyFilter:new l.j(!1),filterFields:new l.j(null)}),Object.keys(this.formFieldData).length>0&&this.patchvalues(this.formFieldData),this.getFieldsList()}patchvalues(e){this.autogenForm.addControl("x",new l.j("")),this.autogenForm.addControl("y",new l.j("")),e._id&&this.autogenForm.addControl("_id",new l.j(e._id)),e.mysql_column_name&&this.autogenForm.addControl("mysql_column_name",new l.j(e.mysql_column_name)),this.autogenForm.patchValue({fieldName:e.fieldName,fieldType:e.fieldType,fieldId:e.fieldId,fieldIcon:e.fieldIcon,rows:e.rows,cols:e.cols,x:e.x,y:e.y,prefix:e.prefix,currentValue:e.currentValue,startWithOne:e.startWithOne,mandatoryField:e.mandatoryField,udrfColumnData:e.udrfColumnData,applyFilter:e.applyFilter||!1,filterFields:e.filterFields||null})}onSubmit(){this.autogenForm.invalid||(this.autogenForm.get("startWithOne").value||""!=this.autogenForm.get("prefix").value||this.autogenForm.controls.startWithOne.setValue(!0),this._CustomFieldService.addField(this.autogenForm.value),this.closeComponent.emit(!0))}close(){this.msg=!1,this._CustomFieldService.editCheck(this.msg),this.closeComponent.emit(!0)}getFieldsList(){let e=this._CustomFieldService.fieldsList;e.length?this.fieldsList=this.filterFieldList(e):this._CustomFieldService.getFieldsByApp().subscribe(e=>{e&&e.data&&e.data.length&&(this.fieldsList=this.filterFieldList(e.data),this._CustomFieldService.setFieldsList(e.data))})}filterFieldList(e){const t=[1,6,9,15,16];return Ie.filter(e,e=>Ie.contains(t,parseInt(e.fieldData.fieldId)))}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](O.a),i["\u0275\u0275directiveInject"](v.a))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-autogen"]],inputs:{field:"field"},outputs:{closeComponent:"closeComponent"},decls:40,vars:3,consts:[[1,"card-wrapper"],[1,"card-heading"],["mat-icon-button","",3,"click"],[1,"card-contents"],[3,"formGroup","ngSubmit"],["appearance","outline"],["matInput","","formControlName","fieldName"],["matInput","","formControlName","fieldType","readonly","","value","Autogen"],["appearance","outline",2,"font-size","11px"],["matInput","","formControlName","prefix","maxlength","2","placeholder","Prefix of auto-generating number",3,"readonly"],[1,"d-flex","my-3"],[1,"mr-2",2,"color","#cf0000","font-size","20px"],[1,"row",2,"font-size","13px"],[1,"row"],["formControlName","startWithOne"],["formControlName","applyFilter"],["formControlName","filterFields",3,"placeholder","list",4,"ngIf"],["mat-icon-button","","type","submit",1,"button-inactive","red"],[2,"font-size","16px"],["mat-icon-button","","type","button",1,"button-inactive"],[1,"iconButton",3,"click"],["formControlName","filterFields",3,"placeholder","list"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275elementStart"](1,"div",1),i["\u0275\u0275elementStart"](2,"button",2),i["\u0275\u0275listener"]("click",(function(){return t.close()})),i["\u0275\u0275elementStart"](3,"mat-icon"),i["\u0275\u0275text"](4,"keyboard_backspace"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"span"),i["\u0275\u0275text"](6,"Autogen"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](7,"div",3),i["\u0275\u0275elementStart"](8,"form",4),i["\u0275\u0275listener"]("ngSubmit",(function(){return t.onSubmit()})),i["\u0275\u0275elementStart"](9,"mat-form-field",5),i["\u0275\u0275elementStart"](10,"mat-label"),i["\u0275\u0275text"](11,"Field name"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](12,"input",6),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](13,"mat-form-field",5),i["\u0275\u0275elementStart"](14,"mat-label"),i["\u0275\u0275text"](15,"Field type"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](16,"input",7),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](17,"mat-form-field",8),i["\u0275\u0275element"](18,"input",9),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](19,"div",10),i["\u0275\u0275elementStart"](20,"mat-icon",11),i["\u0275\u0275text"](21,"info"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](22,"div",12),i["\u0275\u0275elementStart"](23,"div",13),i["\u0275\u0275text"](24,"Maximum length 8"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](25,"div",13),i["\u0275\u0275text"](26,"First two are prefix "),i["\u0275\u0275elementStart"](27,"strong"),i["\u0275\u0275text"](28,"Eg - LE000001 "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](29,"mat-checkbox",14),i["\u0275\u0275text"](30,"Start with 1 Instead of XX0000001"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](31,"mat-checkbox",15),i["\u0275\u0275text"](32,"Apply Filter"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](33,Ve,1,2,"app-multi-select-search-customfield",16),i["\u0275\u0275elementStart"](34,"button",17),i["\u0275\u0275elementStart"](35,"mat-icon",18),i["\u0275\u0275text"](36,"done_all"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](37,"button",19),i["\u0275\u0275elementStart"](38,"mat-icon",20),i["\u0275\u0275listener"]("click",(function(){return t.close()})),i["\u0275\u0275text"](39,"close"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](8),i["\u0275\u0275property"]("formGroup",t.autogenForm),i["\u0275\u0275advance"](10),i["\u0275\u0275property"]("readonly",t.autogenForm.get("startWithOne").value),i["\u0275\u0275advance"](15),i["\u0275\u0275property"]("ngIf",t.autogenForm.get("applyFilter").value))},directives:[c.a,d.a,l.J,l.w,l.n,m.c,m.g,s.b,l.e,l.v,l.l,l.q,M.a,o.NgIf,Ae.a],styles:[".card-wrapper[_ngcontent-%COMP%]{box-shadow:0 0 3px 0 #ccc;border-radius:3px;margin:5px 0 20px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]{padding-top:5px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-top:-4px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-weight:500;font-size:17px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]{min-width:220px;margin:10px 18px;padding-bottom:20px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{width:100%;font-size:12px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .button-inactive[_ngcontent-%COMP%]{line-height:8px;width:26px;height:26px;margin-right:12px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:16px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .red[_ngcontent-%COMP%]{background:#d41111;color:#fff;font-size:16px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .mandatoryFieldCheck[_ngcontent-%COMP%]{margin:10px 0}mat-checkbox[_ngcontent-%COMP%]{display:block}mat-checkbox[_ngcontent-%COMP%]     .mat-checkbox-inner-container{margin-right:14px;height:15px;width:15px}mat-checkbox[_ngcontent-%COMP%]     .mat-checkbox-label{font-size:13px}mat-checkbox[_ngcontent-%COMP%]   sup[_ngcontent-%COMP%]{color:red;font-size:1em;line-height:0;position:relative;vertical-align:initial;top:-.1em}"]}),e})();function Te(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",24),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"]().openApiConfigModal()})),i["\u0275\u0275text"](1," Api Config "),i["\u0275\u0275elementEnd"]()}}function qe(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",24),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"]().openFieldControlConfigModal()})),i["\u0275\u0275text"](1," Field Control Config "),i["\u0275\u0275elementEnd"]()}}let ze=(()=>{class e{constructor(e,t){this._FieldsLibraryService=e,this._CustomFieldService=t,this.closeComponent=new i.EventEmitter,this.msg=!1,this.currencyList=[],this.valueChangeSubscription=new F.a}ngOnInit(){this.formFieldData=this._FieldsLibraryService.getFormFieldData(),this.currencyForm=new l.m({fieldName:new l.j("",l.H.required),fieldType:new l.j(this.field.label),fieldId:new l.j(this.field.id),fieldIcon:new l.j(this.field.icon),rows:new l.j(this.field.default_rows),cols:new l.j(this.field.default_columns),currencyCode:new l.j("INR"),roundOffValues:new l.j(!1),mandatoryField:new l.j(!1),udrfColumnData:new l.j(this.field.udrf_column_data),enableActivity:new l.j(!1),enableNotification:new l.j(!1),editableOnCreation:new l.j(!0),editableOnUpdation:new l.j(!0),valueFromApi:new l.j(!1),allowFieldControl:new l.j(!1)}),this.valueChangeSubscription.add(this.currencyForm.get("enableNotification").valueChanges.subscribe(e=>{e&&this.currencyForm.get("enableActivity").patchValue(!0)})),this.valueChangeSubscription.add(this.currencyForm.get("enableActivity").valueChanges.subscribe(e=>{e||this.currencyForm.get("enableNotification").patchValue(!1)})),Object.keys(this.formFieldData).length>0&&this.patchvalues(this.formFieldData),this.getCurrencyData()}patchvalues(e){this.currencyForm.addControl("x",new l.j("")),this.currencyForm.addControl("y",new l.j("")),e._id&&this.currencyForm.addControl("_id",new l.j(e._id)),e.mysql_column_name&&this.currencyForm.addControl("mysql_column_name",new l.j(e.mysql_column_name)),this.currencyForm.patchValue({fieldName:e.fieldName,fieldType:e.fieldType,fieldId:e.fieldId,fieldIcon:e.fieldIcon,rows:e.rows,cols:e.cols,x:e.x,y:e.y,currencyCode:e.currencyCode,roundOffValues:e.roundOffValues,mandatoryField:e.mandatoryField,udrfColumnData:e.udrfColumnData,enableActivity:!!e.enableActivity&&e.enableActivity,enableNotification:!!e.enableNotification&&e.enableNotification,editableOnCreation:null==e.editableOnCreation||e.editableOnCreation,editableOnUpdation:null==e.editableOnUpdation||e.editableOnUpdation,valueFromApi:e.valueFromApi,allowFieldControl:e.allowFieldControl}),e.valueFromApi&&(this.currencyForm.addControl("apiConfig",new l.j(null)),this.currencyForm.get("apiConfig").patchValue(e.apiConfig)),e.allowFieldControl&&(this.currencyForm.addControl("fieldControlMapping",new l.j(null)),this.currencyForm.get("fieldControlMapping").patchValue(e.fieldControlMapping))}onSubmit(){this.currencyForm.invalid||(console.log(this.currencyForm.value),this._CustomFieldService.addField(this.currencyForm.value),this.closeComponent.emit(!0))}close(){this.msg=!1,this._CustomFieldService.editCheck(this.msg),this.closeComponent.emit(!0)}getCurrencyData(){this._FieldsLibraryService.getCurrencyData().subscribe(e=>{this.currencyList=e})}toggleValueFromApi(e){e.checked&&!this.currencyForm.contains("apiConfig")?this.currencyForm.addControl("apiConfig",new l.j(null,l.H.required)):e.checked||this.currencyForm.removeControl("apiConfig")}openApiConfigModal(){return Object(a.c)(this,void 0,void 0,(function*(){this._FieldsLibraryService.openApiConfigModal(this.currencyForm)}))}toggleAllowFieldControl(e){e.checked&&!this.currencyForm.contains("fieldControlMapping")?this.currencyForm.addControl("fieldControlMapping",new l.j(null,l.H.required)):e.checked||this.currencyForm.removeControl("fieldControlMapping")}openFieldControlConfigModal(){return Object(a.c)(this,void 0,void 0,(function*(){this._FieldsLibraryService.openFieldControlConfigModal(this.currencyForm)}))}ngOnDestroy(){this.valueChangeSubscription&&this.valueChangeSubscription.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](O.a),i["\u0275\u0275directiveInject"](v.a))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-currency"]],inputs:{field:"field"},outputs:{closeComponent:"closeComponent"},decls:47,vars:4,consts:[[1,"card-wrapper"],[1,"card-heading"],["mat-icon-button","",3,"click"],[1,"card-contents"],[3,"formGroup","ngSubmit"],["appearance","outline"],["matInput","","formControlName","fieldName"],["matInput","","formControlName","fieldType","readonly","","value","Number"],[1,"extra_fields"],["required","true","placeholder","Currency","formControlName","currencyCode",3,"list"],["formControlName","roundOffValues"],["formControlName","mandatoryField"],["formControlName","enableActivity"],["formControlName","enableNotification"],["formControlName","editableOnCreation"],["formControlName","editableOnUpdation"],["formControlName","valueFromApi",3,"change"],[1,"d-flex","justify-content-center"],["type","button","mat-raised-button","","class","ml-2 btn-class",3,"click",4,"ngIf"],["formControlName","allowFieldControl",3,"change"],["mat-icon-button","","type","submit",1,"button-inactive","red"],[2,"font-size","16px"],["mat-icon-button","","type","button",1,"button-inactive"],[1,"iconButton",3,"click"],["type","button","mat-raised-button","",1,"ml-2","btn-class",3,"click"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275elementStart"](1,"div",1),i["\u0275\u0275elementStart"](2,"button",2),i["\u0275\u0275listener"]("click",(function(){return t.close()})),i["\u0275\u0275elementStart"](3,"mat-icon"),i["\u0275\u0275text"](4,"keyboard_backspace"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"span"),i["\u0275\u0275text"](6,"Currency"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](7,"div",3),i["\u0275\u0275elementStart"](8,"form",4),i["\u0275\u0275listener"]("ngSubmit",(function(){return t.onSubmit()})),i["\u0275\u0275elementStart"](9,"mat-form-field",5),i["\u0275\u0275elementStart"](10,"mat-label"),i["\u0275\u0275text"](11,"Field name"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](12,"input",6),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](13,"mat-form-field",5),i["\u0275\u0275elementStart"](14,"mat-label"),i["\u0275\u0275text"](15,"Field type"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](16,"input",7),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](17,"div",8),i["\u0275\u0275element"](18,"app-input-search",9),i["\u0275\u0275elementStart"](19,"mat-checkbox",10),i["\u0275\u0275text"](20,"Round off values to Crs & Mns "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](21,"mat-checkbox",11),i["\u0275\u0275text"](22,"Mandatory Field "),i["\u0275\u0275elementStart"](23,"sup"),i["\u0275\u0275text"](24,"*"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](25,"mat-checkbox",12),i["\u0275\u0275text"](26,"Enable Activity for field "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](27,"mat-checkbox",13),i["\u0275\u0275text"](28,"Enable Notification for field "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](29,"mat-checkbox",14),i["\u0275\u0275text"](30,"Edit allowed on Creation"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](31,"mat-checkbox",15),i["\u0275\u0275text"](32,"Edit allowed on Updation"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](33,"mat-checkbox",16),i["\u0275\u0275listener"]("change",(function(e){return t.toggleValueFromApi(e)})),i["\u0275\u0275text"](34,"Get Value from external api"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](35,"div",17),i["\u0275\u0275template"](36,Te,2,0,"button",18),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](37,"mat-checkbox",19),i["\u0275\u0275listener"]("change",(function(e){return t.toggleAllowFieldControl(e)})),i["\u0275\u0275text"](38,"Enable Field Control (based on condition)"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](39,"div",17),i["\u0275\u0275template"](40,qe,2,0,"button",18),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](41,"button",20),i["\u0275\u0275elementStart"](42,"mat-icon",21),i["\u0275\u0275text"](43,"done_all"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](44,"button",22),i["\u0275\u0275elementStart"](45,"mat-icon",23),i["\u0275\u0275listener"]("click",(function(){return t.close()})),i["\u0275\u0275text"](46,"close"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](8),i["\u0275\u0275property"]("formGroup",t.currencyForm),i["\u0275\u0275advance"](10),i["\u0275\u0275property"]("list",t.currencyList),i["\u0275\u0275advance"](18),i["\u0275\u0275property"]("ngIf",t.currencyForm.get("valueFromApi").value),i["\u0275\u0275advance"](4),i["\u0275\u0275property"]("ngIf",t.currencyForm.get("allowFieldControl").value))},directives:[c.a,d.a,l.J,l.w,l.n,m.c,m.g,s.b,l.e,l.v,l.l,y.a,l.F,M.a,o.NgIf],styles:[".card-wrapper[_ngcontent-%COMP%]{box-shadow:0 0 3px 0 #ccc;border-radius:3px;margin:5px 0 20px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]{padding-top:5px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-top:-4px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-weight:500;font-size:17px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]{min-width:220px;margin:10px 18px;padding-bottom:20px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{width:100%;font-size:12px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .button-inactive[_ngcontent-%COMP%]{line-height:8px;width:26px;height:26px;margin-right:12px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:16px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .red[_ngcontent-%COMP%]{background:#d41111;color:#fff;font-size:16px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .mandatoryFieldCheck[_ngcontent-%COMP%]{margin:10px 0}mat-checkbox[_ngcontent-%COMP%]{display:block}mat-checkbox[_ngcontent-%COMP%]     .mat-checkbox-inner-container{margin-right:14px;height:15px;width:15px}mat-checkbox[_ngcontent-%COMP%]     .mat-checkbox-label{font-size:13px}mat-checkbox[_ngcontent-%COMP%]   sup[_ngcontent-%COMP%]{color:red;font-size:1em;line-height:0;position:relative;vertical-align:initial;top:-.1em}"]}),e})();function Le(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div"),i["\u0275\u0275element"](1,"app-multi-select-search2",19),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("list",e.tagsList)}}let Ue=(()=>{class e{constructor(e,t,n){this._FieldsLibraryService=e,this.snackBar=t,this._CustomFieldService=n,this.closeComponent=new i.EventEmitter,this.destroy$=new ie.b,this.tagsList=[],this.msg=!1,this.maxcount=!1}ngOnInit(){this.formFieldData=this._FieldsLibraryService.getFormFieldData(),this.tagsForm=new l.m({fieldName:new l.j("",l.H.required),fieldType:new l.j(this.field.label),fieldId:new l.j(this.field.id),fieldIcon:new l.j(this.field.icon),rows:new l.j(this.field.default_rows),cols:new l.j(this.field.default_columns),allowUserTags:new l.j(!0),restrictTags:new l.j(!1),selectedTags:new l.j(""),allowOwnAndAdmin:new l.j(!1),mandatoryField:new l.j(!1),udrfColumnData:new l.j(this.field.udrf_column_data),uniqueField:new l.j(!1)}),Object.keys(this.formFieldData).length>0&&this.patchvalues(this.formFieldData),this.getValueFn("restrictTags")&&this.getAdminTagsData()}patchvalues(e){this.tagsForm.addControl("x",new l.j("")),this.tagsForm.addControl("y",new l.j("")),e._id&&this.tagsForm.addControl("_id",new l.j(e._id)),e.mysql_column_name&&this.tagsForm.addControl("mysql_column_name",new l.j(e.mysql_column_name)),this.tagsForm.patchValue({fieldName:e.fieldName,fieldType:e.fieldType,fieldId:e.fieldId,fieldIcon:e.fieldIcon,rows:e.rows,cols:e.cols,x:e.x,y:e.y,allowUserTags:e.allowUserTags,restrictTags:e.restrictTags,selectedTags:e.selectedTags,allowOwnAndAdmin:e.allowOwnAndAdmin,mandatoryField:e.mandatoryField,udrfColumnData:e.udrfColumnData,uniqueField:e.uniqueField})}onSubmit(){this.tagsForm.invalid||(this.getValueFn("restrictTags")||this.setValueFn("selectedTags",""),console.log(this.tagsForm.value),this._CustomFieldService.addField(this.tagsForm.value),this.closeComponent.emit(!0))}close(){this.msg=!1,this._CustomFieldService.editCheck(this.msg),this.closeComponent.emit(!0)}getAdminTagsData(){this._FieldsLibraryService.getAdminTagsData("object").pipe(Object(ae.a)(this.destroy$)).subscribe(e=>{this.tagsList=e,0==this.tagsList.length&&(this.snackBar.open("Cannot restrict as there are no tags available","Dismiss",{duration:2e3}),this.setValueFn("allowUserTags",!0),this.setValueFn("restrictTags",!1),this.setValueFn("allowOwnAndAdmin",!1),this.removeRequiredValidator("selectedTags"))})}addRequiredValidator(e){this.tagsForm.controls[e].setValidators([l.H.required]),this.tagsForm.controls[e].updateValueAndValidity()}removeRequiredValidator(e){this.tagsForm.controls[e].clearValidators(),this.tagsForm.controls[e].updateValueAndValidity()}setValueFn(e,t){this.tagsForm.controls[e].setValue(t)}getValueFn(e){return this.tagsForm.get(e).value}toggleTagsScope(e){switch(e){case"allowUserTags":this.setValueFn("allowUserTags",!0),this.setValueFn("restrictTags",!1),this.setValueFn("allowOwnAndAdmin",!1),this.removeRequiredValidator("selectedTags");break;case"restrictTags":this.setValueFn("allowUserTags",!1),this.setValueFn("allowOwnAndAdmin",!1),this.addRequiredValidator("selectedTags"),this.setValueFn("restrictTags",!0),this.getAdminTagsData();break;case"allowOwnAndAdmin":this.setValueFn("allowOwnAndAdmin",!0),this.setValueFn("restrictTags",!1),this.setValueFn("allowUserTags",!1),this.removeRequiredValidator("selectedTags")}}ngOnDestroy(){this.destroy$.next(!0),this.destroy$.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](O.a),i["\u0275\u0275directiveInject"](be.a),i["\u0275\u0275directiveInject"](v.a))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-tags"]],inputs:{field:"field"},outputs:{closeComponent:"closeComponent"},decls:37,vars:2,consts:[[1,"card-wrapper"],[1,"card-heading"],["mat-icon-button","",3,"click"],[1,"card-contents"],[3,"formGroup","ngSubmit"],["appearance","outline"],["matInput","","formControlName","fieldName"],["matInput","","formControlName","fieldType","readonly",""],[1,"extra_fields","mb-2"],["formControlName","allowUserTags",3,"change"],["formControlName","restrictTags",3,"change"],[4,"ngIf"],["formControlName","allowOwnAndAdmin",2,"word-wrap","normal",3,"change"],["formControlName","mandatoryField"],["formControlName","uniqueField"],["mat-icon-button","","type","submit",1,"button-inactive","red"],[2,"font-size","16px"],["mat-icon-button","",1,"button-inactive"],[1,"iconButton",3,"click"],["formControlName","selectedTags","placeholder","Select tags",3,"list"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275elementStart"](1,"div",1),i["\u0275\u0275elementStart"](2,"button",2),i["\u0275\u0275listener"]("click",(function(){return t.close()})),i["\u0275\u0275elementStart"](3,"mat-icon"),i["\u0275\u0275text"](4,"keyboard_backspace"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"span"),i["\u0275\u0275text"](6,"Tags"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](7,"div",3),i["\u0275\u0275elementStart"](8,"form",4),i["\u0275\u0275listener"]("ngSubmit",(function(){return t.onSubmit()})),i["\u0275\u0275elementStart"](9,"mat-form-field",5),i["\u0275\u0275elementStart"](10,"mat-label"),i["\u0275\u0275text"](11,"Field name"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](12,"input",6),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](13,"mat-form-field",5),i["\u0275\u0275elementStart"](14,"mat-label"),i["\u0275\u0275text"](15,"Field type"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](16,"input",7),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](17,"div",8),i["\u0275\u0275elementStart"](18,"mat-checkbox",9),i["\u0275\u0275listener"]("change",(function(){return t.toggleTagsScope("allowUserTags")})),i["\u0275\u0275text"](19,"Allow users to create tags"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](20,"mat-checkbox",10),i["\u0275\u0275listener"]("change",(function(){return t.toggleTagsScope("restrictTags")})),i["\u0275\u0275text"](21,"Restrict to following tags"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](22,Le,2,1,"div",11),i["\u0275\u0275elementStart"](23,"mat-checkbox",12),i["\u0275\u0275listener"]("change",(function(){return t.toggleTagsScope("allowOwnAndAdmin")})),i["\u0275\u0275text"](24,"Allow own creation & Selection "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](25,"mat-checkbox",13),i["\u0275\u0275text"](26,"Mandatory Field "),i["\u0275\u0275elementStart"](27,"sup"),i["\u0275\u0275text"](28,"*"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](29,"mat-checkbox",14),i["\u0275\u0275text"](30,"Unique Field"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](31,"button",15),i["\u0275\u0275elementStart"](32,"mat-icon",16),i["\u0275\u0275text"](33,"done_all"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](34,"button",17),i["\u0275\u0275elementStart"](35,"mat-icon",18),i["\u0275\u0275listener"]("click",(function(){return t.close()})),i["\u0275\u0275text"](36,"close"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](8),i["\u0275\u0275property"]("formGroup",t.tagsForm),i["\u0275\u0275advance"](14),i["\u0275\u0275property"]("ngIf",t.tagsForm.get("restrictTags").value))},directives:[c.a,d.a,l.J,l.w,l.n,m.c,m.g,s.b,l.e,l.v,l.l,M.a,o.NgIf,pe.a],styles:[".card-wrapper[_ngcontent-%COMP%]{box-shadow:0 0 3px 0 #ccc;border-radius:3px;margin:5px 0 20px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]{padding-top:5px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-top:-4px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-weight:500;font-size:17px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]{margin:10px 18px;padding-bottom:20px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{width:100%;font-size:12px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .button-inactive[_ngcontent-%COMP%]{line-height:8px;width:26px;height:26px;margin-right:12px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:16px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .red[_ngcontent-%COMP%]{background:#d41111;color:#fff;font-size:16px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .extra_fields[_ngcontent-%COMP%]{word-wrap:normal}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .extra_fields[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]{display:block}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .extra_fields[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]     .mat-checkbox-inner-container{margin-right:14px;height:15px;width:15px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .extra_fields[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]     .mat-checkbox-label{font-size:13px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .extra_fields[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]   sup[_ngcontent-%COMP%]{color:red;font-size:1em;line-height:0;position:relative;vertical-align:initial;top:-.1em}"]}),e})();function Ge(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-option",26),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;i["\u0275\u0275property"]("value",e),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](e)}}function Re(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-option",26),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;i["\u0275\u0275property"]("value",e),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](e)}}function He(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div"),i["\u0275\u0275elementStart"](1,"mat-form-field",5),i["\u0275\u0275elementStart"](2,"mat-label"),i["\u0275\u0275text"](3,"Symbol"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"mat-select",23),i["\u0275\u0275template"](5,Ge,2,2,"mat-option",24),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](6,"mat-form-field",5),i["\u0275\u0275elementStart"](7,"mat-label"),i["\u0275\u0275text"](8,"Scale"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](9,"mat-select",25),i["\u0275\u0275template"](10,Re,2,2,"mat-option",24),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](5),i["\u0275\u0275property"]("ngForOf",e.symbols),i["\u0275\u0275advance"](5),i["\u0275\u0275property"]("ngForOf",e.scales)}}function Be(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",27),i["\u0275\u0275elementStart"](1,"mat-form-field",5),i["\u0275\u0275elementStart"](2,"mat-label"),i["\u0275\u0275text"](3,"Weightage"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](4,"input",28),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]())}function We(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",27),i["\u0275\u0275elementStart"](1,"mat-form-field",5),i["\u0275\u0275elementStart"](2,"mat-label"),i["\u0275\u0275text"](3,"Weightage (%)"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](4,"input",29),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]())}let Ye=(()=>{class e{constructor(e,t,n){this._FieldsLibraryService=e,this.snackBar=t,this._CustomFieldService=n,this.closeComponent=new i.EventEmitter,this.msg=!1,this.symbols=["star","heart","smiley"],this.scales=[5,4,3],this.valueChangeSubscription=new F.a}ngOnInit(){this.formFieldData=this._FieldsLibraryService.getFormFieldData(),this.ratingForm=new l.m({fieldName:new l.j("",l.H.required),fieldType:new l.j(this.field.label),fieldId:new l.j(this.field.id),fieldIcon:new l.j(this.field.icon),rows:new l.j(this.field.default_rows),cols:new l.j(this.field.default_columns),default:new l.j(!0),custom:new l.j(!1),symbol:new l.j(""),scale:new l.j(""),setWeightage:new l.j(!1),setWeightagePercentage:new l.j(!1),weightagePercentage:new l.j(""),weightage:new l.j(""),numberLimitValue:new l.j(""),mandatoryField:new l.j(""),udrfColumnData:new l.j(this.field.udrf_column_data),enableActivity:new l.j(!1),enableNotification:new l.j(!1)}),this.valueChangeSubscription.add(this.ratingForm.get("enableNotification").valueChanges.subscribe(e=>{e&&this.ratingForm.get("enableActivity").patchValue(!0)})),this.valueChangeSubscription.add(this.ratingForm.get("enableActivity").valueChanges.subscribe(e=>{e||this.ratingForm.get("enableNotification").patchValue(!1)})),Object.keys(this.formFieldData).length>0&&this.patchvalues(this.formFieldData)}patchvalues(e){this.ratingForm.addControl("x",new l.j("")),this.ratingForm.addControl("y",new l.j("")),e._id&&this.ratingForm.addControl("_id",new l.j(e._id)),e.mysql_column_name&&this.ratingForm.addControl("mysql_column_name",new l.j(e.mysql_column_name)),this.ratingForm.patchValue({fieldName:e.fieldName,fieldType:e.fieldType,fieldId:e.fieldId,fieldIcon:e.fieldIcon,rows:e.rows,cols:e.cols,x:e.x,y:e.y,default:e.default,custom:e.custom,symbol:e.symbol,scale:e.scale,setWeightage:e.setWeightage,setWeightagePercentage:e.setWeightagePercentage,weightagePercentage:e.weightagePercentage,weightage:e.weightage,mandatoryField:e.mandatoryField,udrfColumnData:e.udrfColumnData,enableActivity:!!e.enableActivity&&e.enableActivity,enableNotification:!!e.enableNotification&&e.enableNotification})}onSubmit(){if(!this.ratingForm.invalid){if(this.ratingForm.controls.setWeightagePercentage.value){if(this.ratingForm.controls.weightagePercentage.value<=0)return void this.snackBar.open("Set Weightage Percentage > 0","Dismiss",{duration:2e3})}else this.ratingForm.controls.weightagePercentage.setValue("");if(this.ratingForm.controls.setWeightage.value){if(this.ratingForm.controls.weightage.value<=0)return void this.snackBar.open("Set Weightage > 0","Dismiss",{duration:2e3})}else this.ratingForm.controls.weightage.setValue("");this._CustomFieldService.addField(this.ratingForm.value),this.closeComponent.emit(!0)}}close(){this.msg=!1,this._CustomFieldService.editCheck(this.msg),this.closeComponent.emit(!0)}addRequiredValidator(e){this.ratingForm.controls[e].setValidators([l.H.required]),this.ratingForm.controls[e].updateValueAndValidity()}removeRequiredValidator(e){this.ratingForm.controls[e].clearValidators(),this.ratingForm.controls[e].updateValueAndValidity()}setValueFn(e,t){this.ratingForm.controls[e].setValue(t)}toggleRatingScope(e){switch(e){case"default":this.setValueFn("default",!0),this.setValueFn("custom",!1),this.removeRequiredValidator("symbol"),this.removeRequiredValidator("scale"),this.ratingForm.controls.symbol.setValue("star"),this.ratingForm.controls.scale.setValue(5);break;case"custom":this.setValueFn("default",!1),this.addRequiredValidator("symbol"),this.addRequiredValidator("scale"),this.setValueFn("custom",!0),this.ratingForm.controls.symbol.setValue(""),this.ratingForm.controls.scale.setValue("")}}ngOnDestroy(){this.valueChangeSubscription&&this.valueChangeSubscription.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](O.a),i["\u0275\u0275directiveInject"](be.a),i["\u0275\u0275directiveInject"](v.a))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-rating"]],inputs:{field:"field"},outputs:{closeComponent:"closeComponent"},decls:45,vars:4,consts:[[1,"card-wrapper"],[1,"card-heading"],["mat-icon-button","",3,"click"],[1,"card-contents"],[3,"formGroup","ngSubmit"],["appearance","outline"],["matInput","","formControlName","fieldName"],["matInput","","formControlName","fieldType","readonly","","value","Rating"],[1,"extra_fields"],["formControlName","default",3,"change"],["formControlName","custom",3,"change"],[4,"ngIf"],[1,"row","mandatoryFieldCheck"],["formControlName","setWeightage"],["class","row",4,"ngIf"],["formControlName","setWeightagePercentage"],["formControlName","mandatoryField"],["formControlName","enableActivity"],["formControlName","enableNotification"],["mat-icon-button","","type","submit",1,"button-inactive","red"],[2,"font-size","16px"],["mat-icon-button","","type","button",1,"button-inactive"],[1,"iconButton",3,"click"],["formControlName","symbol"],[3,"value",4,"ngFor","ngForOf"],["formControlName","scale"],[3,"value"],[1,"row"],["matInput","","formControlName","weightage","type","number"],["matInput","","formControlName","weightagePercentage","type","number"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275elementStart"](1,"div",1),i["\u0275\u0275elementStart"](2,"button",2),i["\u0275\u0275listener"]("click",(function(){return t.close()})),i["\u0275\u0275elementStart"](3,"mat-icon"),i["\u0275\u0275text"](4,"keyboard_backspace"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"span"),i["\u0275\u0275text"](6,"Rating"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](7,"div",3),i["\u0275\u0275elementStart"](8,"form",4),i["\u0275\u0275listener"]("ngSubmit",(function(){return t.onSubmit()})),i["\u0275\u0275elementStart"](9,"mat-form-field",5),i["\u0275\u0275elementStart"](10,"mat-label"),i["\u0275\u0275text"](11,"Field name"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](12,"input",6),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](13,"mat-form-field",5),i["\u0275\u0275elementStart"](14,"mat-label"),i["\u0275\u0275text"](15,"Field type"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](16,"input",7),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](17,"div",8),i["\u0275\u0275elementStart"](18,"mat-checkbox",9),i["\u0275\u0275listener"]("change",(function(){return t.toggleRatingScope("default")})),i["\u0275\u0275text"](19,"Default (5 Star Rating)"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](20,"mat-checkbox",10),i["\u0275\u0275listener"]("change",(function(){return t.toggleRatingScope("custom")})),i["\u0275\u0275text"](21,"Custom"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](22,He,11,2,"div",11),i["\u0275\u0275elementStart"](23,"div",12),i["\u0275\u0275elementStart"](24,"mat-checkbox",13),i["\u0275\u0275text"](25," Set Weightage "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](26,Be,5,0,"div",14),i["\u0275\u0275elementStart"](27,"div",12),i["\u0275\u0275elementStart"](28,"mat-checkbox",15),i["\u0275\u0275text"](29," Set Weightage (%)"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](30,We,5,0,"div",14),i["\u0275\u0275elementStart"](31,"mat-checkbox",16),i["\u0275\u0275text"](32,"Mandatory field "),i["\u0275\u0275elementStart"](33,"sup"),i["\u0275\u0275text"](34,"*"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](35,"mat-checkbox",17),i["\u0275\u0275text"](36,"Enable Activity for field "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](37,"mat-checkbox",18),i["\u0275\u0275text"](38,"Enable Notification for field "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](39,"button",19),i["\u0275\u0275elementStart"](40,"mat-icon",20),i["\u0275\u0275text"](41,"done_all"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](42,"button",21),i["\u0275\u0275elementStart"](43,"mat-icon",22),i["\u0275\u0275listener"]("click",(function(){return t.close()})),i["\u0275\u0275text"](44,"close"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](8),i["\u0275\u0275property"]("formGroup",t.ratingForm),i["\u0275\u0275advance"](14),i["\u0275\u0275property"]("ngIf",t.ratingForm.get("custom").value),i["\u0275\u0275advance"](4),i["\u0275\u0275property"]("ngIf",t.ratingForm.get("setWeightage").value),i["\u0275\u0275advance"](4),i["\u0275\u0275property"]("ngIf",t.ratingForm.get("setWeightagePercentage").value))},directives:[c.a,d.a,l.J,l.w,l.n,m.c,m.g,s.b,l.e,l.v,l.l,M.a,o.NgIf,re.c,o.NgForOf,le.p,l.A],styles:[".card-wrapper[_ngcontent-%COMP%]{box-shadow:0 0 3px 0 #ccc;border-radius:3px;margin:5px 0 20px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]{padding-top:5px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-top:-4px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-weight:500;font-size:17px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]{min-width:220px;margin:10px 18px;padding-bottom:20px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{width:100%;font-size:12px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .button-inactive[_ngcontent-%COMP%]{line-height:8px;width:26px;height:26px;margin-right:12px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:16px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .red[_ngcontent-%COMP%]{background:#d41111;color:#fff;font-size:16px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .extra_fields[_ngcontent-%COMP%]{margin:10px 0}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .extra_fields[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]{display:block}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .extra_fields[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]     .mat-checkbox-inner-container{margin-right:14px;height:15px;width:15px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .extra_fields[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]     .mat-checkbox-label{font-size:13px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .extra_fields[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]   sup[_ngcontent-%COMP%]{color:red;font-size:1em;line-height:0;position:relative;vertical-align:initial;top:-.1em}"]}),e})(),Je=(()=>{class e{constructor(e,t){this._FieldsLibraryService=e,this._CustomFieldService=t,this.closeComponent=new i.EventEmitter,this.msg=!1,this.valueChangeSubscription=new F.a}ngOnInit(){this.formFieldData=this._FieldsLibraryService.getFormFieldData(),this.progressForm=new l.m({fieldName:new l.j("",l.H.required),fieldType:new l.j(this.field.label),fieldId:new l.j(this.field.id),fieldIcon:new l.j(this.field.icon),rows:new l.j(this.field.default_rows),cols:new l.j(this.field.default_columns),manual:new l.j(!0),automatic:new l.j(!1),mandatoryField:new l.j(""),udrfColumnData:new l.j(this.field.udrf_column_data),enableActivity:new l.j(!1),enableNotification:new l.j(!1)}),this.valueChangeSubscription.add(this.progressForm.get("enableNotification").valueChanges.subscribe(e=>{e&&this.progressForm.get("enableActivity").patchValue(!0)})),this.valueChangeSubscription.add(this.progressForm.get("enableActivity").valueChanges.subscribe(e=>{e||this.progressForm.get("enableNotification").patchValue(!1)})),Object.keys(this.formFieldData).length>0&&this.patchvalues(this.formFieldData)}patchvalues(e){this.progressForm.addControl("x",new l.j("")),this.progressForm.addControl("y",new l.j("")),e._id&&this.progressForm.addControl("_id",new l.j(e._id)),e.mysql_column_name&&this.progressForm.addControl("mysql_column_name",new l.j(e.mysql_column_name)),this.progressForm.patchValue({fieldName:e.fieldName,fieldType:e.fieldType,fieldId:e.fieldId,fieldIcon:e.fieldIcon,rows:e.rows,cols:e.cols,x:e.x,y:e.y,manual:e.manual,automatic:e.automatic,mandatoryField:e.mandatoryField,udrfColumnData:e.udrfColumnData,enableActivity:!!e.enableActivity&&e.enableActivity,enableNotification:!!e.enableNotification&&e.enableNotification})}onSubmit(){this.progressForm.invalid||(console.log(this.progressForm.value),this._CustomFieldService.addField(this.progressForm.value),this.closeComponent.emit(!0))}close(){this.msg=!1,this._CustomFieldService.editCheck(this.msg),this.closeComponent.emit(!0)}addRequiredValidator(e){this.progressForm.controls[e].setValidators([l.H.required]),this.progressForm.controls[e].updateValueAndValidity()}removeRequiredValidator(e){this.progressForm.controls[e].clearValidators(),this.progressForm.controls[e].updateValueAndValidity()}setValueFn(e,t){this.progressForm.controls[e].setValue(t)}toggleProgressScope(e){switch(e){case"manual":this.setValueFn("manual",!0),this.setValueFn("automatic",!1);break;case"automatic":this.setValueFn("manual",!1),this.setValueFn("automatic",!0)}}ngOnDestroy(){this.valueChangeSubscription&&this.valueChangeSubscription.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](O.a),i["\u0275\u0275directiveInject"](v.a))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-progress"]],inputs:{field:"field"},outputs:{closeComponent:"closeComponent"},decls:41,vars:1,consts:[[1,"card-wrapper"],[1,"card-heading"],["mat-icon-button","",3,"click"],[1,"card-contents"],[3,"formGroup","ngSubmit"],["appearance","outline"],["matInput","","formControlName","fieldName"],["matInput","","formControlName","fieldType","readonly","","value","Progress"],[1,"extra_fields"],[1,"mt-2","mb-2",2,"display","flex","align-items","center"],[2,"color","#cf0001","font-size","18px"],[1,"ml-1",2,"font-size","13px"],["formControlName","manual",3,"change"],["formControlName","automatic",3,"change"],["formControlName","mandatoryField"],["formControlName","enableActivity"],["formControlName","enableNotification"],["mat-icon-button","","type","submit",1,"button-inactive","red"],[2,"font-size","16px"],["mat-icon-button","","type","button",1,"button-inactive"],[1,"iconButton",3,"click"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275elementStart"](1,"div",1),i["\u0275\u0275elementStart"](2,"button",2),i["\u0275\u0275listener"]("click",(function(){return t.close()})),i["\u0275\u0275elementStart"](3,"mat-icon"),i["\u0275\u0275text"](4,"keyboard_backspace"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"span"),i["\u0275\u0275text"](6,"Progress"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](7,"div",3),i["\u0275\u0275elementStart"](8,"form",4),i["\u0275\u0275listener"]("ngSubmit",(function(){return t.onSubmit()})),i["\u0275\u0275elementStart"](9,"mat-form-field",5),i["\u0275\u0275elementStart"](10,"mat-label"),i["\u0275\u0275text"](11,"Field name"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](12,"input",6),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](13,"mat-form-field",5),i["\u0275\u0275elementStart"](14,"mat-label"),i["\u0275\u0275text"](15,"Field type"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](16,"input",7),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](17,"div",8),i["\u0275\u0275elementStart"](18,"div",9),i["\u0275\u0275elementStart"](19,"mat-icon",10),i["\u0275\u0275text"](20,"error"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](21,"div",11),i["\u0275\u0275text"](22," Progress is calculated in terms of Percentage (Max:100%)"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](23,"mat-checkbox",12),i["\u0275\u0275listener"]("change",(function(){return t.toggleProgressScope("manual")})),i["\u0275\u0275text"](24,"Manual Calculation "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](25,"mat-checkbox",13),i["\u0275\u0275listener"]("change",(function(){return t.toggleProgressScope("automatic")})),i["\u0275\u0275text"](26,"Automatic Calculation"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](27,"mat-checkbox",14),i["\u0275\u0275text"](28,"Mandatory field "),i["\u0275\u0275elementStart"](29,"sup"),i["\u0275\u0275text"](30,"*"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](31,"mat-checkbox",15),i["\u0275\u0275text"](32,"Enable Activity for field "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](33,"mat-checkbox",16),i["\u0275\u0275text"](34,"Enable Notification for field "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](35,"button",17),i["\u0275\u0275elementStart"](36,"mat-icon",18),i["\u0275\u0275text"](37,"done_all"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](38,"button",19),i["\u0275\u0275elementStart"](39,"mat-icon",20),i["\u0275\u0275listener"]("click",(function(){return t.close()})),i["\u0275\u0275text"](40,"close"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](8),i["\u0275\u0275property"]("formGroup",t.progressForm))},directives:[c.a,d.a,l.J,l.w,l.n,m.c,m.g,s.b,l.e,l.v,l.l,M.a],styles:[".card-wrapper[_ngcontent-%COMP%]{box-shadow:0 0 3px 0 #ccc;border-radius:3px;margin:5px 0 20px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]{padding-top:5px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-top:-4px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-weight:500;font-size:17px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]{min-width:220px;margin:10px 18px;padding-bottom:20px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{width:100%;font-size:12px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .button-inactive[_ngcontent-%COMP%]{line-height:8px;width:26px;height:26px;margin-right:12px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:16px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .red[_ngcontent-%COMP%]{background:#d41111;color:#fff;font-size:16px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .extra_fields[_ngcontent-%COMP%]{margin:10px 0}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .extra_fields[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]{display:block}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .extra_fields[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]     .mat-checkbox-inner-container{margin-right:14px;height:15px;width:15px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .extra_fields[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]     .mat-checkbox-label{font-size:13px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .extra_fields[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]   sup[_ngcontent-%COMP%]{color:red;font-size:1em;line-height:0;position:relative;vertical-align:initial;top:-.1em}"]}),e})(),$e=(()=>{class e{constructor(e,t){this._FieldsLibraryService=e,this._CustomFieldService=t,this.closeComponent=new i.EventEmitter,this.maxcount=!1,this.msg=!1,this.valueChangeSubscription=new F.a}ngOnInit(){this.formFieldData=this._FieldsLibraryService.getFormFieldData(),this.richTextForm=new l.m({fieldName:new l.j("",l.H.required),fieldType:new l.j(this.field.label),fieldId:new l.j(this.field.id),fieldIcon:new l.j(this.field.icon),rows:new l.j(this.field.default_rows),cols:new l.j(this.field.default_columns),mandatoryField:new l.j(""),udrfColumnData:new l.j(this.field.udrf_column_data),enableActivity:new l.j(!1),enableNotification:new l.j(!1),editableOnCreation:new l.j(!0),editableOnUpdation:new l.j(!0)}),this.valueChangeSubscription.add(this.richTextForm.get("enableNotification").valueChanges.subscribe(e=>{e&&this.richTextForm.get("enableActivity").patchValue(!0)})),this.valueChangeSubscription.add(this.richTextForm.get("enableActivity").valueChanges.subscribe(e=>{e||this.richTextForm.get("enableNotification").patchValue(!1)})),Object.keys(this.formFieldData).length>0&&this.patchvalues(this.formFieldData)}patchvalues(e){this.richTextForm.addControl("x",new l.j("")),this.richTextForm.addControl("y",new l.j("")),e._id&&this.richTextForm.addControl("_id",new l.j(e._id)),e.mysql_column_name&&this.richTextForm.addControl("mysql_column_name",new l.j(e.mysql_column_name)),this.richTextForm.patchValue({fieldName:e.fieldName,fieldType:e.fieldType,fieldId:e.fieldId,fieldIcon:e.fieldIcon,rows:e.rows,cols:e.cols,x:e.x,y:e.y,mandatoryField:e.mandatoryField,udrfColumnData:e.udrfColumnData,enableActivity:!!e.enableActivity&&e.enableActivity,enableNotification:!!e.enableNotification&&e.enableNotification,editableOnCreation:null==e.editableOnCreation||e.editableOnCreation,editableOnUpdation:null==e.editableOnUpdation||e.editableOnUpdation})}onSubmit(){this.richTextForm.invalid||(this._CustomFieldService.addField(this.richTextForm.value),this.closeComponent.emit(!0))}close(){this.msg=!1,this._CustomFieldService.editCheck(this.msg),this.closeComponent.emit(!0)}ngOnDestroy(){this.valueChangeSubscription&&this.valueChangeSubscription.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](O.a),i["\u0275\u0275directiveInject"](v.a))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-rich-text"]],inputs:{field:"field"},outputs:{closeComponent:"closeComponent"},decls:36,vars:1,consts:[[1,"card-wrapper"],[1,"card-heading"],["mat-icon-button","",3,"click"],[1,"card-contents"],[3,"formGroup","ngSubmit"],["appearance","outline"],["matInput","","formControlName","fieldName"],["matInput","","formControlName","fieldType","readonly",""],[1,"extra_fields","mb-2"],["formControlName","mandatoryField"],["formControlName","enableActivity"],["formControlName","enableNotification"],["formControlName","editableOnCreation"],["formControlName","editableOnUpdation"],["mat-icon-button","","type","submit",1,"button-inactive","red"],[2,"font-size","16px"],["mat-icon-button","",1,"button-inactive"],[1,"iconButton",3,"click"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275elementStart"](1,"div",1),i["\u0275\u0275elementStart"](2,"button",2),i["\u0275\u0275listener"]("click",(function(){return t.close()})),i["\u0275\u0275elementStart"](3,"mat-icon"),i["\u0275\u0275text"](4,"keyboard_backspace"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"span"),i["\u0275\u0275text"](6,"Rich Text"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](7,"div",3),i["\u0275\u0275elementStart"](8,"form",4),i["\u0275\u0275listener"]("ngSubmit",(function(){return t.onSubmit()})),i["\u0275\u0275elementStart"](9,"mat-form-field",5),i["\u0275\u0275elementStart"](10,"mat-label"),i["\u0275\u0275text"](11,"Field name"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](12,"input",6),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](13,"mat-form-field",5),i["\u0275\u0275elementStart"](14,"mat-label"),i["\u0275\u0275text"](15,"Field type"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](16,"input",7),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](17,"div",8),i["\u0275\u0275elementStart"](18,"mat-checkbox",9),i["\u0275\u0275text"](19,"Mandatory Field"),i["\u0275\u0275elementStart"](20,"sup"),i["\u0275\u0275text"](21,"*"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](22,"mat-checkbox",10),i["\u0275\u0275text"](23,"Enable Activity for field "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](24,"mat-checkbox",11),i["\u0275\u0275text"](25,"Enable Notification for field "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](26,"mat-checkbox",12),i["\u0275\u0275text"](27,"Edit allowed on Creation"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](28,"mat-checkbox",13),i["\u0275\u0275text"](29,"Edit allowed on Updation"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](30,"button",14),i["\u0275\u0275elementStart"](31,"mat-icon",15),i["\u0275\u0275text"](32,"done_all"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](33,"button",16),i["\u0275\u0275elementStart"](34,"mat-icon",17),i["\u0275\u0275listener"]("click",(function(){return t.close()})),i["\u0275\u0275text"](35,"close"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](8),i["\u0275\u0275property"]("formGroup",t.richTextForm))},directives:[c.a,d.a,l.J,l.w,l.n,m.c,m.g,s.b,l.e,l.v,l.l,M.a],styles:[".card-wrapper[_ngcontent-%COMP%]{box-shadow:0 0 3px 0 #ccc;border-radius:3px;margin:5px 0 20px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]{padding-top:5px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-top:-4px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-weight:500;font-size:17px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]{margin:10px 18px;padding-bottom:20px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{width:100%;font-size:12px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .button-inactive[_ngcontent-%COMP%]{line-height:8px;width:26px;height:26px;margin-right:12px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:16px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .red[_ngcontent-%COMP%]{background:#d41111;color:#fff;font-size:16px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .extra_fields[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]{display:block}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .extra_fields[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]     .mat-checkbox-inner-container{margin-right:14px;height:15px;width:15px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .extra_fields[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]     .mat-checkbox-label{font-size:13px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .extra_fields[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]   sup[_ngcontent-%COMP%]{color:red;font-size:1em;line-height:0;position:relative;vertical-align:initial;top:-.1em}"]}),e})();var Xe=n("fbp6"),Ke=n("/1cH");function Qe(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-option",16),i["\u0275\u0275elementStart"](1,"mat-icon",17),i["\u0275\u0275text"](2),i["\u0275\u0275elementEnd"](),i["\u0275\u0275text"](3),i["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;i["\u0275\u0275property"]("value",e.name),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate"](e.name),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" - \xa0\xa0",e.name," ")}}let Ze=(()=>{class e{constructor(e,t,n){this._FieldsLibraryService=e,this._CustomFieldService=t,this._appBuilderService=n,this.closeComponent=new i.EventEmitter,this.maxcount=!1,this.msg=!1,this.matIconsList=[],this.initialIconList=[]}ngOnInit(){this.formFieldData=this._FieldsLibraryService.getFormFieldData(),this.groupHeadingForm=new l.m({fieldName:new l.j("",l.H.required),fieldType:new l.j(this.field.label),fieldId:new l.j(this.field.id),fieldIcon:new l.j("",l.H.required),rows:new l.j(this.field.default_rows),cols:new l.j(this.field.default_columns),mandatoryField:new l.j(""),udrfColumnData:new l.j(this.field.udrf_column_data)}),this.resolveSubscriptions(),this.getIcons(),Object.keys(this.formFieldData).length>0&&this.patchvalues(this.formFieldData)}patchvalues(e){this.groupHeadingForm.addControl("x",new l.j("")),this.groupHeadingForm.addControl("y",new l.j("")),e._id&&this.groupHeadingForm.addControl("_id",new l.j(e._id)),e.mysql_column_name&&this.groupHeadingForm.addControl("mysql_column_name",new l.j(e.mysql_column_name)),this.groupHeadingForm.patchValue({fieldName:e.fieldName,fieldType:e.fieldType,fieldId:e.fieldId,fieldIcon:e.fieldIcon,rows:e.rows,cols:e.cols,x:e.x,y:e.y,mandatoryField:e.mandatoryField,udrfColumnData:e.udrfColumnData})}getIcons(){this.iconSubscription=this._appBuilderService.getCreationIcons().subscribe(e=>{e&&e.length>0&&(this.matIconsList=e,this.initialIconList=this.matIconsList)},e=>{console.log(e)})}resolveSubscriptions(){this.iconFormSubscription=this.groupHeadingForm.get("fieldIcon").valueChanges.subscribe(e=>{this.reorderIconList(e)})}reorderIconList(e){var t=new RegExp(e,"i");this.matIconsList=Ie.filter(this.initialIconList,e=>e.name&&t.test(e.name))}onSubmit(){this.groupHeadingForm.invalid||(this._CustomFieldService.addField(this.groupHeadingForm.value),this.closeComponent.emit(!0))}close(){this.msg=!1,this._CustomFieldService.editCheck(this.msg),this.closeComponent.emit(!0)}ngOnDestroy(){this.iconSubscription.unsubscribe(),this.iconFormSubscription.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](O.a),i["\u0275\u0275directiveInject"](v.a),i["\u0275\u0275directiveInject"](Xe.a))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-group-heading"]],inputs:{field:"field"},outputs:{closeComponent:"closeComponent"},decls:30,vars:3,consts:[[1,"card-wrapper"],[1,"card-heading"],["mat-icon-button","",3,"click"],[1,"card-contents"],[3,"formGroup","ngSubmit"],["appearance","outline"],["matInput","","formControlName","fieldName"],["matInput","","formControlName","fieldType","readonly",""],["appearance","outline",2,"width","100% !important"],["type","text","formControlName","fieldIcon","matInput","",3,"matAutocomplete"],["iconAuto","matAutocomplete"],["class","custom-ts-select-option",3,"value",4,"ngFor","ngForOf"],["mat-icon-button","","type","submit",1,"button-inactive","red"],[2,"font-size","16px"],["mat-icon-button","",1,"button-inactive"],[1,"iconButton",3,"click"],[1,"custom-ts-select-option",3,"value"],[2,"color","#66615b"]],template:function(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275elementStart"](1,"div",1),i["\u0275\u0275elementStart"](2,"button",2),i["\u0275\u0275listener"]("click",(function(){return t.close()})),i["\u0275\u0275elementStart"](3,"mat-icon"),i["\u0275\u0275text"](4,"keyboard_backspace"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"span"),i["\u0275\u0275text"](6,"Heading"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](7,"div",3),i["\u0275\u0275elementStart"](8,"form",4),i["\u0275\u0275listener"]("ngSubmit",(function(){return t.onSubmit()})),i["\u0275\u0275elementStart"](9,"mat-form-field",5),i["\u0275\u0275elementStart"](10,"mat-label"),i["\u0275\u0275text"](11,"Heading title"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](12,"input",6),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](13,"mat-form-field",5),i["\u0275\u0275elementStart"](14,"mat-label"),i["\u0275\u0275text"](15,"Field type"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](16,"input",7),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](17,"mat-form-field",8),i["\u0275\u0275elementStart"](18,"mat-label"),i["\u0275\u0275text"](19,"Heading icon"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](20,"input",9),i["\u0275\u0275elementStart"](21,"mat-autocomplete",null,10),i["\u0275\u0275template"](23,Qe,4,3,"mat-option",11),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](24,"button",12),i["\u0275\u0275elementStart"](25,"mat-icon",13),i["\u0275\u0275text"](26,"done_all"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](27,"button",14),i["\u0275\u0275elementStart"](28,"mat-icon",15),i["\u0275\u0275listener"]("click",(function(){return t.close()})),i["\u0275\u0275text"](29,"close"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275reference"](22);i["\u0275\u0275advance"](8),i["\u0275\u0275property"]("formGroup",t.groupHeadingForm),i["\u0275\u0275advance"](12),i["\u0275\u0275property"]("matAutocomplete",e),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngForOf",t.matIconsList)}},directives:[c.a,d.a,l.J,l.w,l.n,m.c,m.g,s.b,l.e,l.v,l.l,Ke.d,Ke.b,o.NgForOf,le.p],styles:[".card-wrapper[_ngcontent-%COMP%]{box-shadow:0 0 3px 0 #ccc;border-radius:3px;margin:5px 0 20px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]{padding-top:5px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{margin-top:-4px}.card-wrapper[_ngcontent-%COMP%]   .card-heading[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-weight:500;font-size:17px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]{margin:10px 18px;padding-bottom:20px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{width:100%;font-size:12px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .button-inactive[_ngcontent-%COMP%]{line-height:8px;width:26px;height:26px;margin-right:12px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:16px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .red[_ngcontent-%COMP%]{background:#d41111;color:#fff;font-size:16px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .extra_fields[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]{display:block}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .extra_fields[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]     .mat-checkbox-inner-container{margin-right:14px;height:15px;width:15px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .extra_fields[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]     .mat-checkbox-label{font-size:13px}.card-wrapper[_ngcontent-%COMP%]   .card-contents[_ngcontent-%COMP%]   .extra_fields[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]   sup[_ngcontent-%COMP%]{color:red;font-size:1em;line-height:0;position:relative;vertical-align:initial;top:-.1em}.card-wrapper[_ngcontent-%COMP%]   .custom-ts-select[_ngcontent-%COMP%]{border:1px solid grey;padding:5px 0!important}.card-wrapper[_ngcontent-%COMP%]   .custom-ts-select[_ngcontent-%COMP%], .card-wrapper[_ngcontent-%COMP%]   .custom-ts-select-option[_ngcontent-%COMP%]{text-align:center;font-size:13px;color:#1a1a1a}.card-wrapper[_ngcontent-%COMP%]   .custom-ts-search-input[_ngcontent-%COMP%]{padding:12px 5px 8px;cursor:pointer}.card-wrapper[_ngcontent-%COMP%]   .custom-ts-search[_ngcontent-%COMP%]{width:100%;text-align:center!important;font-size:14px;color:#1a1a1a}.card-wrapper[_ngcontent-%COMP%]   .custom-ts-search[_ngcontent-%COMP%]   .mat-form-field-wrapper[_ngcontent-%COMP%]   .mat-form-field-flex[_ngcontent-%COMP%]{padding:5px!important}"]}),e})();const et=["field"];let tt=(()=>{class e{constructor(e,t){this.resolver=e,this._FieldsLibraryService=t,this.close=new i.EventEmitter,this.fieldsLibrary={6:U,1:Ce,2:De,3:je,4:ze,15:K,16:B,10:Q,7:te,5:se,8:Ne,9:ve,11:Je,12:Pe,14:Ue,17:Me,13:Ye,19:Ze,20:$e}}ngOnInit(){this.value=0==this.edit?this.message.id:this.message.fieldId;const e=this.resolver.resolveComponentFactory(this.fieldsLibrary[this.value]),t=this.fieldRef.createComponent(e);this.datafield=this.fieldData,this._FieldsLibraryService.setFormFieldData(this.datafield),t.instance.field=this.message,t.instance.closeComponent.subscribe(e=>{this.onClose()})}onClose(){this.close.emit(!0)}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](i.ComponentFactoryResolver),i["\u0275\u0275directiveInject"](O.a))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-fields-library"]],viewQuery:function(e,t){if(1&e&&i["\u0275\u0275staticViewQuery"](et,!0,i.ViewContainerRef),2&e){let e;i["\u0275\u0275queryRefresh"](e=i["\u0275\u0275loadQuery"]())&&(t.fieldRef=e.first)}},inputs:{message:"message",fieldData:"fieldData",edit:"edit"},outputs:{close:"close"},decls:2,vars:0,consts:[["field",""]],template:function(e,t){1&e&&i["\u0275\u0275element"](0,"div",null,0)},styles:[""]}),e})();var nt=n("tk/3"),ot=n("MutI"),it=n("J9mX"),at=n("A5z7"),rt=n("ptd3");let lt=(()=>{class e{static getFieldComponent(){return tt}}return e.\u0275mod=i["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=i["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[o.CommonModule,d.b,M.b,m.e,s.c,c.b,p.c,l.p,re.d,r.g,l.E,nt.d,ot.d,at.e,it.a,Ke.c,rt.CustomFieldHomeModule]]}),e})()},"5CHU":function(e,t,n){"use strict";n.d(t,"a",(function(){return C}));var o=n("fXoL"),i=n("3Pt+"),a=n("jtHE"),r=n("XNiG"),l=n("NJ67"),c=n("1G5W"),d=n("kmnG"),m=n("d3UM"),s=n("FKr1"),p=n("WJ5W"),g=n("ofXK");const u=["allSelected"],h=["singleSelect"];function f(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"mat-option",5),o["\u0275\u0275text"](1),o["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;o["\u0275\u0275property"]("value",e.id),o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let C=(()=>{class e extends l.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new i.j,this.fieldFilterCtrl=new i.j,this.list=[],this.required=!1,this.valueChange=new o.EventEmitter,this.disabled=!1,this.filteredList=new a.a,this._onDestroy=new r.b}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(c.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(c.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}toggleAllSelection(){this.allSelected.selected&&this.fieldCtrl.patchValue([])}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](o.Renderer2))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-multi-select-search2"]],viewQuery:function(e,t){if(1&e&&(o["\u0275\u0275viewQuery"](u,!0),o["\u0275\u0275viewQuery"](h,!0)),2&e){let e;o["\u0275\u0275queryRefresh"](e=o["\u0275\u0275loadQuery"]())&&(t.allSelected=e.first),o["\u0275\u0275queryRefresh"](e=o["\u0275\u0275loadQuery"]())&&(t.singleSelect=e.first)}},inputs:{list:"list",placeholder:"placeholder",required:"required",disabled:"disabled"},outputs:{valueChange:"valueChange"},features:[o["\u0275\u0275ProvidersFeature"]([{provide:i.t,useExisting:Object(o.forwardRef)(()=>e),multi:!0}]),o["\u0275\u0275InheritDefinitionFeature"],o["\u0275\u0275NgOnChangesFeature"]],decls:9,vars:10,consts:[["appearance","outline",2,"width","100%"],["multiple","",3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value",4,"ngFor","ngForOf"],[3,"value"]],template:function(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"mat-form-field",0),o["\u0275\u0275elementStart"](1,"mat-label"),o["\u0275\u0275text"](2),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](3,"mat-select",1,2),o["\u0275\u0275elementStart"](5,"mat-option"),o["\u0275\u0275element"](6,"ngx-mat-select-search",3),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](7,f,2,2,"mat-option",4),o["\u0275\u0275pipe"](8,"async"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e&&(o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate"](t.placeholder),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngForOf",o["\u0275\u0275pipeBind1"](8,8,t.filteredList)))},directives:[d.c,d.g,m.c,i.v,i.k,i.F,s.p,p.a,g.NgForOf],pipes:[g.AsyncPipe],styles:[""]}),e})()},GMZp:function(e,t,n){"use strict";t.isObject=function(e){return null!==e&&"object"==typeof e}},LBXl:function(e,t,n){"use strict";t.UnsubscriptionError=function(){function e(e){return Error.call(this),this.message=e?e.length+" errors occurred during unsubscription:\n"+e.map((function(e,t){return t+1+") "+e.toString()})).join("\n  "):"",this.name="UnsubscriptionError",this.errors=e,this}return e.prototype=Object.create(Error.prototype),e}()},mbIT:function(e,t,n){"use strict";t.isArray=function(){return Array.isArray||function(e){return e&&"number"==typeof e.length}}()},pshJ:function(e,t,n){"use strict";t.isFunction=function(e){return"function"==typeof e}},qjZ2:function(e,t,n){"use strict";n.d(t,"a",(function(){return f}));var o=n("fXoL"),i=n("3Pt+"),a=n("jtHE"),r=n("XNiG"),l=n("NJ67"),c=n("1G5W"),d=n("kmnG"),m=n("d3UM"),s=n("FKr1"),p=n("WJ5W"),g=n("ofXK");const u=["singleSelect"];function h(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"mat-option",6),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](e);const n=t.$implicit;return o["\u0275\u0275nextContext"]().emitChanges(n)})),o["\u0275\u0275text"](1),o["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;o["\u0275\u0275property"]("value",e.id),o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let f=(()=>{class e extends l.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new i.j,this.fieldFilterCtrl=new i.j,this.list=[],this.required=!1,this.valueChange=new o.EventEmitter,this.disabled=!1,this.filteredList=new a.a,this.change=new o.EventEmitter,this._onDestroy=new r.b,this.emitChanges=e=>{this.change.emit(e)}}ngOnInit(){this.list=this.list.filter(e=>null!==e&&""!=e),this.fieldFilterCtrl.valueChanges.pipe(Object(c.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(c.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){null!=this.list&&this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;console.log("this is filtered value : ",this.fieldFilterCtrl.value),e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](o.Renderer2))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search"]],viewQuery:function(e,t){if(1&e&&o["\u0275\u0275viewQuery"](u,!0),2&e){let e;o["\u0275\u0275queryRefresh"](e=o["\u0275\u0275loadQuery"]())&&(t.singleSelect=e.first)}},inputs:{list:"list",placeholder:"placeholder",required:"required",disabled:"disabled"},outputs:{valueChange:"valueChange",change:"change"},features:[o["\u0275\u0275ProvidersFeature"]([{provide:i.t,useExisting:Object(o.forwardRef)(()=>e),multi:!0}]),o["\u0275\u0275InheritDefinitionFeature"],o["\u0275\u0275NgOnChangesFeature"]],decls:11,vars:11,consts:[["appearance","outline"],[3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value"],[3,"value","click",4,"ngFor","ngForOf"],[3,"value","click"]],template:function(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"mat-form-field",0),o["\u0275\u0275elementStart"](1,"mat-label"),o["\u0275\u0275text"](2),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](3,"mat-select",1,2),o["\u0275\u0275elementStart"](5,"mat-option"),o["\u0275\u0275element"](6,"ngx-mat-select-search",3),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](7,"mat-option",4),o["\u0275\u0275text"](8,"Select One"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](9,h,2,2,"mat-option",5),o["\u0275\u0275pipe"](10,"async"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e&&(o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate"](t.placeholder),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("value",null),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngForOf",o["\u0275\u0275pipeBind1"](10,9,t.filteredList)))},directives:[d.c,d.g,m.c,i.v,i.k,i.F,s.p,p.a,g.NgForOf],pipes:[g.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}"]}),e})()},"zB/H":function(e,t,n){"use strict";var o=n("mbIT"),i=n("GMZp"),a=n("pshJ"),r=n("LBXl");function l(e){return e.reduce((function(e,t){return e.concat(t instanceof r.UnsubscriptionError?t.errors:t)}),[])}t.Subscription=function(){function e(e){this.closed=!1,this._parentOrParents=null,this._subscriptions=null,e&&(this._ctorUnsubscribe=!0,this._unsubscribe=e)}var t;return e.prototype.unsubscribe=function(){var t;if(!this.closed){var n=this,c=n._parentOrParents,d=n._ctorUnsubscribe,m=n._unsubscribe,s=n._subscriptions;if(this.closed=!0,this._parentOrParents=null,this._subscriptions=null,c instanceof e)c.remove(this);else if(null!==c)for(var p=0;p<c.length;++p)c[p].remove(this);if(a.isFunction(m)){d&&(this._unsubscribe=void 0);try{m.call(this)}catch(h){t=h instanceof r.UnsubscriptionError?l(h.errors):[h]}}if(o.isArray(s)){p=-1;for(var g=s.length;++p<g;){var u=s[p];if(i.isObject(u))try{u.unsubscribe()}catch(h){t=t||[],h instanceof r.UnsubscriptionError?t=t.concat(l(h.errors)):t.push(h)}}}if(t)throw new r.UnsubscriptionError(t)}},e.prototype.add=function(t){var n=t;if(!t)return e.EMPTY;switch(typeof t){case"function":n=new e(t);case"object":if(n===this||n.closed||"function"!=typeof n.unsubscribe)return n;if(this.closed)return n.unsubscribe(),n;if(!(n instanceof e)){var o=n;(n=new e)._subscriptions=[o]}break;default:throw new Error("unrecognized teardown "+t+" added to Subscription.")}var i=n._parentOrParents;if(null===i)n._parentOrParents=this;else if(i instanceof e){if(i===this)return n;n._parentOrParents=[i,this]}else{if(-1!==i.indexOf(this))return n;i.push(this)}var a=this._subscriptions;return null===a?this._subscriptions=[n]:a.push(n),n},e.prototype.remove=function(e){var t=this._subscriptions;if(t){var n=t.indexOf(e);-1!==n&&t.splice(n,1)}},e.EMPTY=((t=new e).closed=!0,t),e}()}}]);