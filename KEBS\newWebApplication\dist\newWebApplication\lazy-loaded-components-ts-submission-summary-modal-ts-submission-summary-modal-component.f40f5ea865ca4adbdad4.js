(window.webpackJsonp=window.webpackJsonp||[]).push([[773],{hHH2:function(e,t,n){"use strict";n.r(t),n.d(t,"TsSubmissionSummaryModalComponent",(function(){return S}));var a=n("mrSG"),s=n("xG9w"),i=n("XNiG"),r=n("1G5W"),o=n("0IaG"),l=n("ofXK"),m=n("bTqV"),c=(n("3Pt+"),n("NFeN")),d=(n("kmnG"),n("fXoL")),p=n("LcQX"),u=n("ug40"),h=n("BVzC");function g(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"span",7),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" of ",e.associateName," ")}}const y=function(e){return{cursor:e}};function f(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",18),d["\u0275\u0275elementStart"](1,"div",19),d["\u0275\u0275elementStart"](2,"div",20),d["\u0275\u0275listener"]("click",(function(){d["\u0275\u0275restoreView"](e);const n=t.index,a=t.$implicit;return d["\u0275\u0275nextContext"]().cardClicked(n,a.isClickable)})),d["\u0275\u0275elementStart"](3,"div",21),d["\u0275\u0275elementStart"](4,"span",22),d["\u0275\u0275text"](5),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](6,"div",23),d["\u0275\u0275elementStart"](7,"span",24),d["\u0275\u0275text"](8),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngStyle",d["\u0275\u0275pureFunction1"](4,y,e.isClickable?"pointer":"default"))("ngClass",e.isClicked?"summary-card-clicked":""),d["\u0275\u0275advance"](4),d["\u0275\u0275textInterpolate1"](" ",e.summaryValue," "),d["\u0275\u0275advance"](3),d["\u0275\u0275textInterpolate1"](" ",e.summaryLabel," ")}}function x(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",25),d["\u0275\u0275elementStart"](1,"button",26),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().hoursTrend()})),d["\u0275\u0275text"](2," By Hours"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](3,"button",26),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().showLeaveFHTrendChart()})),d["\u0275\u0275text"](4," By Leaves"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngClass","hoursTrend"==e.activeChart?"btn-active":"btn-not-active"),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngClass","LeaveFHTrend"==e.activeChart?"btn-active":"btn-not-active")}}function v(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",30),d["\u0275\u0275elementStart"](1,"div",31),d["\u0275\u0275element"](2,"div",32),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function b(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",30),d["\u0275\u0275elementStart"](1,"div",31),d["\u0275\u0275element"](2,"div",33),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function C(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",27),d["\u0275\u0275elementStart"](1,"div",28),d["\u0275\u0275template"](2,v,3,0,"div",29),d["\u0275\u0275template"](3,b,3,0,"div",29),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf","hoursTrend"==e.activeChart),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","LeaveFHTrend"==e.activeChart)}}function w(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"span",41),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]().$implicit;d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" - ",e.date_description,"")}}function O(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",38),d["\u0275\u0275elementStart"](1,"span",39),d["\u0275\u0275text"](2),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](3,w,2,1,"span",40),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate"](e.dates),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.date_description)}}function P(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementStart"](1,"div",34),d["\u0275\u0275elementStart"](2,"span"),d["\u0275\u0275element"](3,"div",35),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](4,"span",36),d["\u0275\u0275text"](5),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](6,"div",34),d["\u0275\u0275template"](7,O,4,2,"div",37),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](5),d["\u0275\u0275textInterpolate1"]("",e.quotaTaken," On :"),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngForOf",e.dates)}}let S=(()=>{class e{constructor(e,t,n,a,s){this.dialogRef=e,this.utilityService=t,this.inData=n,this.tsPrimaryService=a,this.errorService=s,this.activeChart="hoursTrend",this._onDestroy=new i.b,this.summaryOfTheYear=[],this.summaryArray=[],this.dates=[],this.associateName="",this.currentYear=0,this.associateOId="",this.quotaTaken="",this.datesViewClicked=!1,this.leaveData=[]}ngOnInit(){this.initDetails()}initDetails(){this.modalParams=this.inData.modalParams,this.summaryOfTheYear=this.modalParams.summaryOfTheYear,this.summaryOfTheYear=s.default.sortBy(this.summaryOfTheYear,"day"),this.leaveData=this.modalParams.leaveData,this.associateName=this.modalParams.associateName?this.modalParams.associateName:"",this.currentYear=this.modalParams.isFybased?"FY "+(parseInt(this.modalParams.currentYear)+1):this.modalParams.currentYear,this.associateOId=this.modalParams.currentAssociateOId,this.datesViewClicked=!1,this.summaryArray=this.modalParams.summaryArray,this.hoursTrend()}closeModal(){this.dialogRef.close({event:"Close"})}hoursTrend(){this.activeChart="hoursTrend",setTimeout(()=>{Promise.all([Promise.all([n.e(36),n.e(38),n.e(42)]).then(n.bind(null,"cclQ")),Promise.all([n.e(36),n.e(38),n.e(46)]).then(n.bind(null,"xJfa")),Promise.all([n.e(36),n.e(209)]).then(n.bind(null,"WlQZ")),n.e(1002).then(n.bind(null,"VrR4"))]).then(e=>Object(a.c)(this,void 0,void 0,(function*(){this.am4core=e[0],this.am4charts=e[1];const t=e[3].am4themes_custom;this.am4core.useTheme(e[2].default),this.am4core.useTheme(t);let n=this.am4core.create("chartdiv",this.am4charts.XYChart);n.data=this.summaryOfTheYear;let a=n.xAxes.push(new this.am4charts.CategoryAxis);a.dataFields.category="month",a.renderer.grid.template.location=0,a.renderer.minGridDistance=30,n.yAxes.push(new this.am4charts.ValueAxis);let s=n.series.push(new this.am4charts.ColumnSeries);s.dataFields.valueY="value",s.dataFields.categoryX="month",s.name="Hours",s.columns.template.tooltipText="{categoryX}: [bold]{valueY}[/]",s.columns.template.fillOpacity=.8;let i=s.columns.template;i.strokeWidth=2,i.strokeOpacity=1}))).catch(e=>{console.error("Timesheet Submission Summary Amcharts Module Loading Error: ",e)})},100)}showLeaveFHTrendChart(){this.activeChart="LeaveFHTrend",setTimeout(()=>{let e=this.am4core.create("leaveFH",this.am4charts.XYChart);e.data=this.leaveData;let t=e.xAxes.push(new this.am4charts.CategoryAxis);t.dataFields.category="name",t.title.text="Leave Types",t.renderer.grid.template.location=0,t.renderer.minGridDistance=20;let n=e.yAxes.push(new this.am4charts.ValueAxis);n.min=0,n.title.text="Leave Balance",this.createSeries(e,"leave_balance","Leaves"),e.legend=new this.am4charts.Legend},100)}createSeries(e,t,n){let a=e.series.push(new this.am4charts.ColumnSeries);a.dataFields.valueY=t,a.dataFields.categoryX="name",a.name=n,a.columns.template.tooltipText="{name}: [bold]{valueY}[/]",a.columns.template.width=this.am4core.percent(95)}cardClicked(e,t){if(t){let t=[];for(let n=0;n<this.summaryArray.length;n++)this.summaryArray[n].isClicked=!(!this.summaryArray[n].isClickable||n!=e||this.summaryArray[n].isClicked),t.push(this.summaryArray[n].isClicked);if(this.datesViewClicked=s.default.contains(t,!0),this.datesViewClicked){let t=s.default.where(this.summaryArray,{isClicked:!0});this.quotaTaken=t[0].summaryLabel,this.tsPrimaryService.getDatesForDayType(this.associateOId,t[0].dayType).pipe(Object(r.a)(this._onDestroy)).subscribe(t=>{"S"==t.messType&&t.data.length>0?this.dates=t.data:(this.utilityService.showMessage(t.messText,"Dismiss"),this.dates=[],this.summaryArray[e].isClicked=!1,this.datesViewClicked=!1,this.hoursTrend())},e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while retrieving the Timesheet Properties",e&&e.params?e.params:e&&e.error?e.error.params:{})})}else this.hoursTrend()}}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](o.h),d["\u0275\u0275directiveInject"](p.a),d["\u0275\u0275directiveInject"](o.a),d["\u0275\u0275directiveInject"](u.a),d["\u0275\u0275directiveInject"](h.a))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["ts-submission-summary-modal"]],decls:22,vars:6,consts:[[1,"container-fluid","submission-summary-styles"],[1,"row",2,"border-bottom","solid 1px #cacaca"],[1,"col-11","pt-2","pb-2","d-flex"],["mat-icon-button","",1,"bubble","mt-1"],[1,"iconButton"],[1,"headingBold","my-auto","ml-3"],["class","value ml-2 my-auto",4,"ngIf"],[1,"value","ml-2","my-auto"],[1,"col-1","d-flex"],["mat-icon-button","",1,"ml-auto","close-button","mt-1",3,"click"],[1,"close-Icon"],[1,"row","pt-2","pb-2"],[1,"col-12","pl-0"],[1,"row"],["class","col-2 pb-2 pr-0",4,"ngFor","ngForOf"],["class","row pl-3 pb-2",4,"ngIf"],["class","row pt-2",4,"ngIf"],[4,"ngIf"],[1,"col-2","pb-2","pr-0"],[1,"card","slide-in-right",3,"ngStyle","ngClass"],[1,"card-body","p-3",3,"click"],[1,"row","d-flex"],[1,"mx-auto","headingBold"],[1,"row","d-flex","pt-1"],[1,"mx-auto","value",2,"font-weight","500 !important"],[1,"row","pl-3","pb-2"],["mat-raised-button","",1,"slide-in-right",3,"ngClass","click"],[1,"row","pt-2"],[1,"col-12"],["class","card",4,"ngIf"],[1,"card"],[1,"card-body","p-2"],["id","chartdiv",2,"min-height","380px !important"],["id","leaveFH",2,"min-height","380px !important"],[1,"row","p-2"],[1,"status-circular"],[1,"ml-2","headingBold"],["class","col-12 pb-1",4,"ngFor","ngForOf"],[1,"col-12","pb-1"],[1,"normalText"],["class","headingBold pl-2",4,"ngIf"],[1,"headingBold","pl-2"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",0),d["\u0275\u0275elementStart"](1,"div",1),d["\u0275\u0275elementStart"](2,"div",2),d["\u0275\u0275elementStart"](3,"div",3),d["\u0275\u0275elementStart"](4,"mat-icon",4),d["\u0275\u0275text"](5,"show_chart"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](6,"span",5),d["\u0275\u0275text"](7," Summary "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](8,g,2,1,"span",6),d["\u0275\u0275elementStart"](9,"span",7),d["\u0275\u0275text"](10),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](11,"div",8),d["\u0275\u0275elementStart"](12,"button",9),d["\u0275\u0275listener"]("click",(function(){return t.closeModal()})),d["\u0275\u0275elementStart"](13,"mat-icon",10),d["\u0275\u0275text"](14,"close"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](15,"div",11),d["\u0275\u0275elementStart"](16,"div",12),d["\u0275\u0275elementStart"](17,"div",13),d["\u0275\u0275template"](18,f,9,6,"div",14),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](19,x,5,2,"div",15),d["\u0275\u0275template"](20,C,4,2,"div",16),d["\u0275\u0275template"](21,P,8,2,"div",17),d["\u0275\u0275elementEnd"]()),2&e&&(d["\u0275\u0275advance"](8),d["\u0275\u0275property"]("ngIf",""!=t.associateName),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"](" for ",t.currentYear," "),d["\u0275\u0275advance"](8),d["\u0275\u0275property"]("ngForOf",t.summaryArray),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!t.datesViewClicked),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!t.datesViewClicked),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.datesViewClicked))},directives:[c.a,l.NgIf,m.a,l.NgForOf,l.NgStyle,l.NgClass],styles:[".submission-summary-styles[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]{display:flex;line-height:26px;border-radius:50%;cursor:context-menu!important;width:26px;height:26px;background-color:#fbfbfb;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.submission-summary-styles[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#cf0001;font-size:18px;margin-top:3px!important;margin-left:4px!important}.submission-summary-styles[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.submission-summary-styles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}.submission-summary-styles[_ngcontent-%COMP%]   .headingBold[_ngcontent-%COMP%]{color:#cf0001;font-weight:500;font-size:14px}.submission-summary-styles[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{color:#181818;font-size:14px;font-weight:400;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.submission-summary-styles[_ngcontent-%COMP%]   .terminologies-button-inactive[_ngcontent-%COMP%]{line-height:32px;padding:0 14px;background-color:#fff;color:#181818;font-weight:400;font-size:14px}.submission-summary-styles[_ngcontent-%COMP%]   .terminologies-button-active[_ngcontent-%COMP%]{line-height:32px;padding:0 14px;background-color:#cf0001;color:#fff;font-weight:400;font-size:14px}.submission-summary-styles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff}.submission-summary-styles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%], .submission-summary-styles[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]{font-weight:400;font-size:13px!important;min-width:60px;line-height:33px;padding:0 8px;border-radius:4px;margin-right:17px!important;margin-bottom:3px}.submission-summary-styles[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]{color:rgba(0,0,0,.534);background-color:rgba(194,193,193,.24)}.submission-summary-styles[_ngcontent-%COMP%]   .terminologies-panel[_ngcontent-%COMP%]   .mat-expansion-panel-header-description[_ngcontent-%COMP%], .submission-summary-styles[_ngcontent-%COMP%]   .terminologies-panel[_ngcontent-%COMP%]   .mat-expansion-panel-header-title[_ngcontent-%COMP%]{flex-basis:0}.submission-summary-styles[_ngcontent-%COMP%]   .terminologies-panel[_ngcontent-%COMP%]   .mat-expansion-panel-header-description[_ngcontent-%COMP%]{justify-content:space-between;align-items:center;color:#ababab;font-weight:400;font-size:14px}.submission-summary-styles[_ngcontent-%COMP%]   .terminologies-panel[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%] + .mat-form-field[_ngcontent-%COMP%]{margin-left:8px}.submission-summary-styles[_ngcontent-%COMP%]   .slide-in-right[_ngcontent-%COMP%]{animation:slide-in-right .3s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-right{0%{transform:translateX(30px);opacity:0}to{transform:translateX(0);opacity:1}}.submission-summary-styles[_ngcontent-%COMP%]   .status-circular[_ngcontent-%COMP%]{height:13px;width:13px;margin-top:4px;border-radius:50%;background-color:#cf0001;box-shadow:0 2px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.submission-summary-styles[_ngcontent-%COMP%]   .summary-card-clicked[_ngcontent-%COMP%]{border-color:#cf0001;background-color:#fff2f2}.submission-summary-styles[_ngcontent-%COMP%]   .normalText[_ngcontent-%COMP%]{font-size:14px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}"]}),e})()}}]);