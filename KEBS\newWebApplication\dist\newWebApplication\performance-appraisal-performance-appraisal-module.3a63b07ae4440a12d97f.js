(window.webpackJsonp=window.webpackJsonp||[]).push([[845],{"6RYe":function(e,t,n){"use strict";n.d(t,"a",(function(){return u}));var i=n("mrSG"),o=n("fXoL"),r=n("XXEo"),c=n("Pi1M"),s=n("LcQX"),a=n("tyNb");let u=(()=>{class e{constructor(e,t,n,i){this._auth=e,this._url=t,this._util=n,this.router=i,this.checkAccess=()=>new Promise((e,t)=>{this._url.haveAccess(this.profile.oid).subscribe(t=>{console.log(t),e(t.data)},e=>{t(e)})}),this.profile=this._auth.getProfile().profile}canActivate(){return Object(i.c)(this,void 0,void 0,(function*(){let e=yield this.checkAccess();return 0==e&&(this._util.showMessage("You Dont Have Access! Contact KEBS Support.","dismiss",2e3),this.router.navigateByUrl("/main/appraisal")),e}))}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275inject"](r.a),o["\u0275\u0275inject"](c.a),o["\u0275\u0275inject"](s.a),o["\u0275\u0275inject"](a.g))},e.\u0275prov=o["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},NVmD:function(e,t,n){"use strict";n.r(t),n.d(t,"PerformanceAppraisalModule",(function(){return u}));var i=n("ofXK"),o=n("tyNb"),r=n("6RYe"),c=n("fXoL");const s=[{path:"",redirectTo:"home",pathMatch:"full"},{path:"home",loadChildren:()=>Promise.all([n.e(1),n.e(805)]).then(n.bind(null,"00fp")).then(e=>e.AppraisalHomeModule)},{path:"configure",canActivate:[r.a],loadChildren:()=>n.e(804).then(n.bind(null,"Wurt")).then(e=>e.AppraisalConfigurationModule)}];let a=(()=>{class e{}return e.\u0275mod=c["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=c["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[o.k.forChild(s)],o.k]}),e})(),u=(()=>{class e{}return e.\u0275mod=c["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=c["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,a]]}),e})()}}]);