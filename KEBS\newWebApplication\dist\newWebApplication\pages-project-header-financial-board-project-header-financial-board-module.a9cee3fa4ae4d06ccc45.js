(window.webpackJsonp=window.webpackJsonp||[]).push([[841],{"4WVu":function(e,t,n){"use strict";n.r(t),n.d(t,"ProjectHeaderFinancialBoardModule",(function(){return d}));var o=n("ofXK"),r=n("tyNb"),i=n("mrSG"),c=n("fXoL");const a=["lazyRequestCardView"];function s(e,t){}const l=[{path:"",component:(()=>{class e{constructor(e,t){this.cfr=e,this.compiler=t}lazyLoadRequestCardView(){return Object(i.c)(this,void 0,void 0,(function*(){Promise.all([n.e(4),n.e(23),n.e(26),n.e(30),n.e(36),n.e(38),n.e(42),n.e(45),n.e(46),n.e(55),n.e(58),n.e(62),n.e(85),n.e(103),n.e(0)]).then(n.bind(null,"gTCa")).then(e=>{const t=this.compiler.compileModuleSync(e.ProjectItemTaskModule).create(this.lazyRequestCardView.injector).componentFactoryResolver.resolveComponentFactory(e.ProjectItemTaskComponent);this.lazyRequestCardView.createComponent(t)})}))}ngOnInit(){this.lazyLoadRequestCardView()}}return e.\u0275fac=function(t){return new(t||e)(c["\u0275\u0275directiveInject"](c.ComponentFactoryResolver),c["\u0275\u0275directiveInject"](c.Compiler))},e.\u0275cmp=c["\u0275\u0275defineComponent"]({type:e,selectors:[["app-project-header-financial-board"]],viewQuery:function(e,t){if(1&e&&c["\u0275\u0275viewQuery"](a,!0,c.ViewContainerRef),2&e){let e;c["\u0275\u0275queryRefresh"](e=c["\u0275\u0275loadQuery"]())&&(t.lazyRequestCardView=e.first)}},decls:2,vars:0,consts:[["lazyRequestCardView",""]],template:function(e,t){1&e&&c["\u0275\u0275template"](0,s,0,0,"ng-template",null,0,c["\u0275\u0275templateRefExtractor"])},styles:[""]}),e})()}];let u=(()=>{class e{}return e.\u0275mod=c["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=c["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[r.k.forChild(l)],r.k]}),e})(),d=(()=>{class e{}return e.\u0275mod=c["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=c["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[o.CommonModule,u]]}),e})()}}]);