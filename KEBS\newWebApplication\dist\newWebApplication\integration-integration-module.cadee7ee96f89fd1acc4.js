(window.webpackJsonp=window.webpackJsonp||[]).push([[727,535,631,634,858],{NJ67:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));class i{constructor(){this.disabled=!1}onChange(e){}onTouched(e){}writeValue(e){this.value=e}registerOnChange(e){this.onChange=e}registerOnTouched(e){this.onTouched=e}setDisabledState(e){this.disabled=e}}},"TmG/":function(e,t,n){"use strict";n.d(t,"a",(function(){return _}));var i=n("fXoL"),a=n("3Pt+"),o=n("jtHE"),r=n("XNiG"),l=n("NJ67"),s=n("1G5W"),c=n("kmnG"),d=n("ofXK"),p=n("d3UM"),g=n("FKr1"),m=n("WJ5W"),h=n("Qu3c");function u(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-label"),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](e.placeholder)}}function f(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-option",7),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275property"]("value",null),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](e.disableNone?"Select One":"None")}}function x(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"mat-option",8),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const n=t.$implicit;return i["\u0275\u0275nextContext"]().emitChanges(n)})),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;i["\u0275\u0275property"]("value",e.id||e._id)("matTooltip",e.name),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let _=(()=>{class e extends l.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new a.j,this.fieldFilterCtrl=new a.j,this.list=[],this.required=!1,this.disableNone=!1,this.valueChange=new i.EventEmitter,this.disabled=!1,this.hasNoneOption=!0,this.filteredList=new o.a,this.change=new i.EventEmitter,this.hideMatLabel=!1,this._onDestroy=new r.b,this.emitChanges=e=>{this.change.emit(e)}}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(s.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(s.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](i.Renderer2))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search"]],inputs:{list:"list",placeholder:"placeholder",required:"required",disableNone:"disableNone",disabled:"disabled",hasNoneOption:"hasNoneOption",hideMatLabel:"hideMatLabel"},outputs:{valueChange:"valueChange",change:"change"},features:[i["\u0275\u0275ProvidersFeature"]([{provide:a.t,useExisting:Object(i.forwardRef)(()=>e),multi:!0}]),i["\u0275\u0275InheritDefinitionFeature"],i["\u0275\u0275NgOnChangesFeature"]],decls:9,vars:11,consts:[["appearance","outline"],[4,"ngIf"],[3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value",4,"ngIf"],[3,"value","matTooltip","click",4,"ngFor","ngForOf"],[3,"value"],[3,"value","matTooltip","click"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"mat-form-field",0),i["\u0275\u0275template"](1,u,2,1,"mat-label",1),i["\u0275\u0275elementStart"](2,"mat-select",2,3),i["\u0275\u0275elementStart"](4,"mat-option"),i["\u0275\u0275element"](5,"ngx-mat-select-search",4),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](6,f,2,2,"mat-option",5),i["\u0275\u0275template"](7,x,2,3,"mat-option",6),i["\u0275\u0275pipe"](8,"async"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",!t.hideMatLabel),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.hasNoneOption),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngForOf",i["\u0275\u0275pipeBind1"](8,9,t.filteredList)))},directives:[c.c,d.NgIf,p.c,a.v,a.k,a.F,g.p,m.a,d.NgForOf,c.g,h.a],pipes:[d.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%}"]}),e})()},gMzk:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var i=n("jhN1"),a=n("fXoL"),o=(n("5xO4"),n("3Pt+"),n("PVOt"));let r=(()=>{let e=class{};return e.\u0275mod=a["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=a["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[o.c,o.f,i.b],o.f]}),e})()},mOWZ:function(e,t,n){"use strict";n.r(t),n.d(t,"IntegrationModule",(function(){return Pe}));var i=n("ofXK"),a=n("tyNb"),o=n("mrSG"),r=n("33Jv"),l=n("fXoL"),s=n("2Vo4"),c=n("tk/3");let d=(()=>{class e{constructor(e){this._http=e,this.externalAppData$=new s.a(null),this.selectedApp$=this.externalAppData$.asObservable()}checkUserRole(){return this._http.post("/api/kebsintegration/master/checkUserRole",{})}getKEBSAdminExternalApps(){return this._http.post("/api/kebsintegration/master/getKEBSAdminExternalApps",{})}getTenantAdminExternalApps(){return this._http.post("/api/kebsintegration/master/getTenantAdminExternalApps",{})}setApplicationData(e){this.externalAppData$.next(e)}getApplicationData(){return this.selectedApp$}getTenantList(){return this._http.post("/api/kebsintegration/master/getTenantList",{})}updateExternalAppAccess(e,t){return this._http.post("/api/kebsintegration/monitor/updateTenantExternalAppAccess",{external_application_id:e,t_dddb_n:t})}getKEBSAdminInternalAppsForExternalApps(e){return this._http.post("/api/kebsintegration/master/getKEBSAdminInternalAppsForExternalApps",{external_application_id:e})}getTenantAdminInternalAppsForExternalApps(e){return this._http.post("/api/kebsintegration/master/getTenantAdminInternalAppsForExternalApps",{external_application_id:e})}getChannelList(e,t){return this._http.post("/api/kebsintegration/master/getChannelList",{external_application_id:e,internal_application_id:t})}getSubscriptionList(e,t){return this._http.post("/api/kebsintegration/master/getSubcribedChannel",{external_application_id:e,internal_application_id:t})}updateSubscibeChannel(e,t){return this._http.post("/api/kebsintegration/monitor/subscibeChannel",{id:e,is_subscribed:t})}getEDErrorLogs(){return this._http.post("/api/employee360/ecintegration/getEmployeeDetailsLogsFromStaging",{})}getTimesheetErrorLogs(){return this._http.post("/api/kebsintegration/timesheet/getTimesheetIntegrationErrorLogData",{})}getProjectErrorLogs(){return this._http.post("/api/kebsintegration/monitor/getProjectOutboundErrorLogs",{})}}return e.\u0275fac=function(t){return new(t||e)(l["\u0275\u0275inject"](c.c))},e.\u0275prov=l["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var p=n("1A3m"),g=n("Xa2L"),m=n("Qu3c");function h(e,t){1&e&&(l["\u0275\u0275elementContainerStart"](0),l["\u0275\u0275elementStart"](1,"div",12),l["\u0275\u0275element"](2,"mat-spinner",13),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementContainerEnd"]())}function u(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",16),l["\u0275\u0275listener"]("click",(function(){l["\u0275\u0275restoreView"](e);const n=t.$implicit;return l["\u0275\u0275nextContext"](2).cardClick(n)})),l["\u0275\u0275elementStart"](1,"div",17),l["\u0275\u0275element"](2,"img",18),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](3,"h4",19),l["\u0275\u0275text"](4),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](5,"span",20),l["\u0275\u0275text"](6),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](7,"div",21),l["\u0275\u0275namespaceSVG"](),l["\u0275\u0275elementStart"](8,"svg",22),l["\u0275\u0275element"](9,"path",23),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("src",e.image_url?e.image_url:"https://assets.kebs.app/Keka_logo.jpg",l["\u0275\u0275sanitizeUrl"]),l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate"](e.name?e.name:"-"),l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate"](e.description?e.description:"-")}}function f(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div",14),l["\u0275\u0275template"](1,u,10,3,"div",15),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngForOf",e.cardList)}}function x(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"div",24),l["\u0275\u0275elementStart"](1,"div",25),l["\u0275\u0275element"](2,"img",26),l["\u0275\u0275elementStart"](3,"span",27),l["\u0275\u0275text"](4,"No Integrations Available !"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]())}let _=(()=>{class e{constructor(e,t,n){this._router=e,this._integrationService=t,this._toaster=n,this.isComponentLoading=!1,this.isAdminRole=!1,this.cardList=[],this.subs=new r.a,this.noDataImg=!1}ngOnInit(){return Object(o.c)(this,void 0,void 0,(function*(){this.calculateDynamicStyle(),this._integrationService.setApplicationData(null),this.isComponentLoading=!0,yield this.getCardData(),this.isComponentLoading=!1}))}cardClick(e){let t=Object.assign(Object.assign({},e),{isAdminRole:this.isAdminRole});this._integrationService.setApplicationData(t),this._router.navigateByUrl(this.isAdminRole?"/main/integration/integration/authentication":"/main/integration/integration/landing-page")}getCardData(){return Object(o.c)(this,void 0,void 0,(function*(){let e=yield this.checkUserRole();e&&e.isAdmin?(this.isAdminRole=!0,this.cardList=yield this.getKEBSAdminExternalApps(),0==this.cardList.length&&(this.noDataImg=!0)):e&&e.isTenantAccess&&!e.isAdmin?(this.cardList=yield this.getTenantAdminExternalApps(),0==this.cardList.length&&(this.noDataImg=!0)):this._toaster.showError("Authorization","Access Denied !",3e3)}))}checkUserRole(){return new Promise((e,t)=>{this.subs.sink=this._integrationService.checkUserRole().subscribe(t=>{e("S"==t.messType?t:null)},t=>{console.log(t),e(null)})})}getKEBSAdminExternalApps(){return new Promise((e,t)=>{this.subs.sink=this._integrationService.getKEBSAdminExternalApps().subscribe(t=>{e("S"==t.messType?t.data:[])},t=>{console.log(t),e([])})})}getTenantAdminExternalApps(){return new Promise((e,t)=>{this.subs.sink=this._integrationService.getTenantAdminExternalApps().subscribe(t=>{e("S"==t.messType?t.data:[])},t=>{console.log(t),e([])})})}onResize(){this.calculateDynamicStyle()}calculateDynamicStyle(){let e=window.innerHeight-55+"px",t=window.innerHeight-123+"px";document.documentElement.style.setProperty("--dynamicPageHeight",e),document.documentElement.style.setProperty("--dynamicContentHeight",t)}ngOnDestroy(){this.subs.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(l["\u0275\u0275directiveInject"](a.g),l["\u0275\u0275directiveInject"](d),l["\u0275\u0275directiveInject"](p.a))},e.\u0275cmp=l["\u0275\u0275defineComponent"]({type:e,selectors:[["app-integration-landing-page"]],hostBindings:function(e,t){1&e&&l["\u0275\u0275listener"]("resize",(function(){return t.onResize()}),!1,l["\u0275\u0275resolveWindow"])},decls:13,vars:3,consts:[[1,"integration"],[1,"row","d-flex","align-items-center","header"],["width","20","height","20","viewBox","0 0 20 20","fill","none","xmlns","http://www.w3.org/2000/svg"],["id","mask0_263_630","maskUnits","userSpaceOnUse","x","0","y","0","width","20","height","20",2,"mask-type","alpha"],["width","20","height","20","fill","#D9D9D9"],["mask","url(#mask0_263_630)"],["d","M4.99964 18.9584C4.36333 18.9584 3.82216 18.7356 3.37611 18.2902C2.93007 17.8448 2.70705 17.3039 2.70705 16.6676C2.70705 16.0313 2.92987 15.4901 3.37551 15.0441C3.82116 14.5981 4.3623 14.375 4.99893 14.375C5.17719 14.375 5.35233 14.3972 5.52434 14.4415C5.69634 14.4859 5.85873 14.5454 6.01151 14.6202L7.6638 12.5641C7.31765 12.187 7.07886 11.765 6.94745 11.2981C6.81605 10.8312 6.78934 10.3617 6.86732 9.88946L4.46664 9.09456C4.25723 9.4311 3.9819 9.70087 3.64064 9.90385C3.29937 10.1068 2.91873 10.2083 2.4987 10.2083C1.86213 10.2083 1.32105 9.98562 0.875448 9.54021C0.429851 9.09479 0.207045 8.55393 0.207031 7.91762C0.207031 7.28132 0.42974 6.74015 0.875156 6.2941C1.32057 5.84806 1.86143 5.62504 2.49774 5.62504C3.13405 5.62504 3.67522 5.84784 4.12126 6.29344C4.5673 6.73903 4.79033 7.28012 4.79034 7.91669C4.79034 7.96584 4.78901 8.00697 4.78634 8.04008C4.78368 8.07321 4.78234 8.10633 4.78234 8.13946L7.17501 8.97119C7.40471 8.54598 7.72014 8.18114 8.12132 7.87667C8.52249 7.57218 8.98268 7.37667 9.50191 7.29012V4.73237C8.96559 4.60631 8.53236 4.33521 8.20224 3.91908C7.87211 3.50296 7.70705 3.02883 7.70705 2.49671C7.70705 1.86324 7.92976 1.32348 8.37518 0.877438C8.82059 0.431396 9.36146 0.208374 9.99776 0.208374C10.6341 0.208374 11.1752 0.431173 11.6213 0.87677C12.0673 1.32237 12.2903 1.86345 12.2903 2.50002C12.2903 3.02994 12.1231 3.50296 11.7887 3.91908C11.4544 4.33521 11.0233 4.60631 10.4955 4.73237V7.29012C11.0147 7.37667 11.4736 7.57218 11.8721 7.87667C12.2706 8.18114 12.5873 8.54598 12.8224 8.97119L15.2151 8.13946C15.2097 8.10099 15.2071 8.06653 15.2071 8.03608V7.91669C15.2071 7.28012 15.4298 6.73903 15.8752 6.29344C16.3206 5.84784 16.8615 5.62504 17.4978 5.62504C18.1341 5.62504 18.6752 5.84775 19.1213 6.29317C19.5673 6.73858 19.7903 7.27944 19.7903 7.91575C19.7903 8.55206 19.5675 9.09323 19.1219 9.53927C18.6764 9.98531 18.1353 10.2083 17.4987 10.2083C17.0804 10.2083 16.6981 10.1068 16.3516 9.90385C16.0052 9.70087 15.7316 9.4311 15.5308 9.09456L13.1301 9.88946C13.2081 10.3617 13.1814 10.8291 13.0499 11.2917C12.9185 11.7543 12.6797 12.1758 12.3336 12.5561L13.9859 14.5994C14.1387 14.53 14.3011 14.4752 14.4731 14.4351C14.6451 14.3951 14.8202 14.375 14.9985 14.375C15.6351 14.375 16.1762 14.5977 16.6219 15.0432C17.0675 15.4886 17.2903 16.0294 17.2903 16.6658C17.2903 17.3021 17.0676 17.8432 16.6222 18.2893C16.1768 18.7353 15.6359 18.9583 14.9996 18.9583C14.3633 18.9583 13.8222 18.7355 13.3761 18.2899C12.9301 17.8443 12.7071 17.3033 12.7071 16.6667C12.7071 16.4039 12.7495 16.151 12.8344 15.9079C12.9194 15.6648 13.0462 15.4418 13.2151 15.2388L11.5628 13.1666C11.0895 13.4487 10.5673 13.5897 9.9963 13.5897C9.42526 13.5897 8.90043 13.4487 8.4218 13.1666L6.78234 15.2388C6.9458 15.4418 7.07134 15.6648 7.15895 15.9079C7.24655 16.151 7.29034 16.4039 7.29034 16.6667C7.29034 17.3033 7.06764 17.8443 6.62222 18.2899C6.1768 18.7355 5.63594 18.9583 4.99964 18.9584ZM2.4987 8.95835C2.78823 8.95835 3.03423 8.85712 3.2367 8.65467C3.43916 8.45219 3.54039 8.20619 3.54039 7.91667C3.54039 7.62714 3.43916 7.38114 3.2367 7.17867C3.03423 6.97622 2.78823 6.875 2.4987 6.875C2.20917 6.875 1.96317 6.97623 1.7607 7.17869C1.55825 7.38116 1.45703 7.62716 1.45703 7.91669C1.45703 8.20622 1.55826 8.45222 1.76072 8.65469C1.96318 8.85715 2.20917 8.95835 2.4987 8.95835ZM4.9987 17.7084C5.28823 17.7084 5.53423 17.6071 5.7367 17.4047C5.93916 17.2022 6.04039 16.9562 6.04039 16.6667C6.04039 16.3771 5.93916 16.1311 5.7367 15.9287C5.53423 15.7262 5.28823 15.625 4.9987 15.625C4.70917 15.625 4.46317 15.7262 4.2607 15.9287C4.05824 16.1312 3.95701 16.3772 3.95701 16.6667C3.95701 16.9562 4.05824 17.2022 4.2607 17.4047C4.46317 17.6071 4.70917 17.7084 4.9987 17.7084ZM9.9987 3.54169C10.2882 3.54169 10.5342 3.44046 10.7367 3.238C10.9392 3.03553 11.0404 2.78953 11.0404 2.5C11.0404 2.21047 10.9392 1.96447 10.7367 1.762C10.5342 1.55956 10.2882 1.45833 9.9987 1.45833C9.70917 1.45833 9.46317 1.55956 9.2607 1.76202C9.05824 1.96449 8.95701 2.21049 8.95701 2.50002C8.95701 2.78955 9.05824 3.03555 9.2607 3.23802C9.46317 3.44048 9.70917 3.54169 9.9987 3.54169ZM9.9987 12.3397C10.5339 12.3397 10.9883 12.153 11.3617 11.7796C11.7351 11.4062 11.9218 10.9519 11.9218 10.4167C11.9218 9.88142 11.7351 9.42709 11.3617 9.05369C10.9883 8.68031 10.5339 8.49362 9.9987 8.49362C9.46345 8.49362 9.00912 8.68032 8.63572 9.05371C8.26233 9.42711 8.07564 9.88144 8.07564 10.4167C8.07564 10.9519 8.26233 11.4063 8.63572 11.7797C9.00912 12.1531 9.46345 12.3397 9.9987 12.3397ZM14.9987 17.7084C15.2882 17.7084 15.5342 17.6071 15.7367 17.4047C15.9392 17.2022 16.0404 16.9562 16.0404 16.6667C16.0404 16.3771 15.9392 16.1311 15.7367 15.9287C15.5342 15.7262 15.2882 15.625 14.9987 15.625C14.7092 15.625 14.4632 15.7262 14.2607 15.9287C14.0582 16.1312 13.957 16.3772 13.957 16.6667C13.957 16.9562 14.0582 17.2022 14.2607 17.4047C14.4632 17.6071 14.7092 17.7084 14.9987 17.7084ZM17.4987 8.95835C17.7882 8.95835 18.0342 8.85712 18.2367 8.65467C18.4392 8.45219 18.5404 8.20619 18.5404 7.91667C18.5404 7.62714 18.4392 7.38114 18.2367 7.17867C18.0342 6.97622 17.7882 6.875 17.4987 6.875C17.2092 6.875 16.9632 6.97623 16.7607 7.17869C16.5582 7.38116 16.457 7.62716 16.457 7.91669C16.457 8.20622 16.5582 8.45222 16.7607 8.65469C16.9632 8.85715 17.2092 8.95835 17.4987 8.95835Z","fill","white"],[1,"header-text"],[1,"main-content"],[4,"ngIf"],["class","row d-flex card-section",4,"ngIf"],["style","margin-top: 8%;",4,"ngIf"],[1,"d-flex","justify-content-center","mt-3"],["matTooltip","Please wait...","diameter","30"],[1,"row","d-flex","card-section"],["class","card",3,"click",4,"ngFor","ngForOf"],[1,"card",3,"click"],[1,"img-outline"],[2,"border-radius","2px",3,"src"],[1,"heading"],[1,"content"],[1,"row","card-text-footer"],["width","268","height","21","viewBox","0 0 268 21","fill","none","xmlns","http://www.w3.org/2000/svg"],["d","M250.293 10.8104H242.513C242.367 10.8104 242.245 10.7614 242.147 10.6632C242.049 10.565 242 10.4431 242 10.2976C242 10.152 242.049 10.0302 242.147 9.93199C242.245 9.8338 242.367 9.7847 242.513 9.7847H250.293L246.758 6.2497C246.656 6.148 246.606 6.02898 246.607 5.89266C246.609 5.75632 246.662 5.63511 246.769 5.52904C246.875 5.42997 246.995 5.37868 247.129 5.37517C247.263 5.37166 247.383 5.42295 247.489 5.52904L251.825 9.86492C251.889 9.92892 251.934 9.99643 251.961 10.0674C251.987 10.1385 252 10.2152 252 10.2976C252 10.38 251.987 10.4567 251.961 10.5277C251.934 10.5987 251.889 10.6662 251.825 10.7302L247.489 15.0661C247.395 15.1608 247.277 15.2092 247.137 15.2114C246.998 15.2136 246.875 15.1652 246.769 15.0661C246.662 14.96 246.609 14.8382 246.609 14.7005C246.609 14.5629 246.662 14.441 246.769 14.3349L250.293 10.8104Z","fill","#1C1B1F"],[2,"margin-top","8%"],[1,"justify-content-center","align-items-center",2,"display","grid"],["src","https://assets.kebs.app/images/no_data_found.png",2,"height","200px","width","250px"],[1,"no-img-txt"]],template:function(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"div",0),l["\u0275\u0275elementStart"](1,"div",1),l["\u0275\u0275namespaceSVG"](),l["\u0275\u0275elementStart"](2,"svg",2),l["\u0275\u0275elementStart"](3,"mask",3),l["\u0275\u0275element"](4,"rect",4),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](5,"g",5),l["\u0275\u0275element"](6,"path",6),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275namespaceHTML"](),l["\u0275\u0275elementStart"](7,"h4",7),l["\u0275\u0275text"](8,"Integrations"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](9,"div",8),l["\u0275\u0275template"](10,h,3,0,"ng-container",9),l["\u0275\u0275template"](11,f,2,1,"div",10),l["\u0275\u0275template"](12,x,5,0,"div",11),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&e&&(l["\u0275\u0275advance"](10),l["\u0275\u0275property"]("ngIf",t.isComponentLoading),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",!t.isComponentLoading&&!t.noDataImg),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",!t.isComponentLoading&&t.noDataImg))},directives:[i.NgIf,g.c,m.a,i.NgForOf],styles:[".integration[_ngcontent-%COMP%]{background-color:rgba(232,177,177,.058823529411764705);height:var(--dynamicPageHeight)}.integration[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{padding:10px 8px 24px 24px;background:rgba(238,73,97,.09019607843137255);border-bottom:1px solid #ee4961;background:linear-gradient(270deg,rgba(238,73,97,.4392156862745098),rgba(238,73,97,.6784313725490196) 105.29%)}.integration[_ngcontent-%COMP%]   .header-icon[_ngcontent-%COMP%]{font-size:14px;margin-right:5px;color:#111434;border:none;background:none}.integration[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]{font-family:Roboto;font-size:16px;font-weight:700;line-height:24px;letter-spacing:.02em;text-align:left;color:#ee4961;padding:0 0 0 8px;margin:0;color:#fff}.integration[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]{color:#26303e}.integration[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]{padding:40px 4px 0 24px;height:var(--dynamicContentHeight);overflow-y:scroll}.integration[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]{border:1px solid #e3e3e3;border-radius:8px;background-color:#fff;padding:7px 10px 6px;margin-bottom:20px;margin-right:52px;width:300px;height:169.29px;gap:12px;cursor:pointer}.integration[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .img-outline[_ngcontent-%COMP%]{padding:2px;border-radius:8.75px;border:.55px solid #e8e9ee;width:-moz-fit-content;width:fit-content}.integration[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]{color:#111434;font-size:16px;font-weight:700}.integration[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%], .integration[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]{font-family:Roboto;line-height:16px;letter-spacing:.02em;text-align:left;margin:0}.integration[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{color:#8b95a5;font-size:12px;font-weight:400}.integration[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .card-text-footer[_ngcontent-%COMP%]{display:flex;justify-content:end;margin-top:-7px}.integration[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]:hover{transform:translateY(-5px);box-shadow:0 4px 8px rgba(243,93,93,.4);margin-top:6px}.integration[_ngcontent-%COMP%]   .no-img-txt[_ngcontent-%COMP%]{font-size:14px;font-family:Roboto;color:#ee4961;margin-top:9px;margin-left:40px}"]}),e})();var C=n("3Pt+"),b=n("NFeN"),v=n("TmG/"),y=n("bTqV"),P=n("UXJo"),O=n("kmnG"),E=n("qFsG");let M=(()=>{class e{constructor(e,t){this.clipboard=e,this._router=t,this.tokenFormControl=new C.j("",[C.H.required])}ngOnInit(){}copyToClipboard(){this.clipboard.copy(this.tokenFormControl.value),console.log("Token Copy was Called!",this.tokenFormControl.value)}navigateToMainPage(){this._router.navigateByUrl("/main/integration/integration/landing-page")}}return e.\u0275fac=function(t){return new(t||e)(l["\u0275\u0275directiveInject"](P.b),l["\u0275\u0275directiveInject"](a.g))},e.\u0275cmp=l["\u0275\u0275defineComponent"]({type:e,selectors:[["app-keka-integration"]],decls:23,vars:1,consts:[[1,"authentication-keka"],[1,"section"],[1,"text-header"],[1,"row","d-flex","m-0","p-0"],[1,"input-form"],[1,"input-field"],["type","text","matInput","","placeholder","2o5cfe9-2a87-456a-9ef1-4125a8077430e"],["mat-raised-button","","type","button",1,"copy-btn",3,"cdkCopyToClipboard"],[1,"btn-text"],[1,"icon-copy"],["mat-raised-button","",1,"next-btn",3,"click"],[1,"btn-nxt-text"],[1,"row","d-block","mt-2"],[1,"sub-content"]],template:function(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"div",0),l["\u0275\u0275elementStart"](1,"div",1),l["\u0275\u0275elementStart"](2,"h4",2),l["\u0275\u0275text"](3,"Use this API token to connect"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](4,"div",3),l["\u0275\u0275elementStart"](5,"form",4),l["\u0275\u0275elementStart"](6,"mat-form-field",5),l["\u0275\u0275elementStart"](7,"mat-label"),l["\u0275\u0275text"](8,"API Token"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275element"](9,"input",6),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](10,"button",7),l["\u0275\u0275elementStart"](11,"span",8),l["\u0275\u0275text"](12,"Copy "),l["\u0275\u0275elementStart"](13,"mat-icon",9),l["\u0275\u0275text"](14,"file_copy"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](15,"button",10),l["\u0275\u0275listener"]("click",(function(){return t.navigateToMainPage()})),l["\u0275\u0275elementStart"](16,"span",11),l["\u0275\u0275text"](17,"Go to Keka "),l["\u0275\u0275elementStart"](18,"mat-icon"),l["\u0275\u0275text"](19,"keyboard_arrow_right"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](20,"div",12),l["\u0275\u0275elementStart"](21,"span",13),l["\u0275\u0275text"](22,"This is a unique identifier used to authenticate you to keka. Keep your token private to avoid sharing sensitive information."),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&e&&(l["\u0275\u0275advance"](10),l["\u0275\u0275property"]("cdkCopyToClipboard",t.tokenFormControl.value))},directives:[C.J,C.w,C.x,O.c,O.g,E.b,y.a,P.a,b.a],styles:[".authentication-keka[_ngcontent-%COMP%]{align-items:center;display:flex;justify-content:center;height:50vh}.authentication-keka[_ngcontent-%COMP%]   .text-header[_ngcontent-%COMP%]{font-family:Roboto;font-size:14px;font-weight:700;line-height:16px;letter-spacing:.02em;text-align:left}.authentication-keka[_ngcontent-%COMP%]   .input-form[_ngcontent-%COMP%]{width:80%;display:flex;margin-right:18px;justify-content:start;align-items:flex-start}.authentication-keka[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]{width:83%;margin-right:6px;background:rgba(193,198,203,.34901960784313724);height:46px;border-bottom:1px solid;padding-left:7px}.authentication-keka[_ngcontent-%COMP%]   .copy-btn[_ngcontent-%COMP%]{color:#fff;background-color:#ee4961;height:44px;display:flex;justify-content:center;align-items:center}.authentication-keka[_ngcontent-%COMP%]   .next-btn[_ngcontent-%COMP%]{border:1px solid grey;background:#fff;height:44px}.authentication-keka[_ngcontent-%COMP%]   .sub-content[_ngcontent-%COMP%]{display:block;width:68.5%;font-size:14px;font-family:roboto}.authentication-keka[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]{display:grid;align-items:center;justify-content:center}.authentication-keka[_ngcontent-%COMP%]     .mat-form-field-appearance-legacy .mat-form-field-underline{height:0!important}.authentication-keka[_ngcontent-%COMP%]     .mat-form-field-appearance-legacy .mat-form-field-ripple{top:0;height:0!important;overflow:hidden}.authentication-keka[_ngcontent-%COMP%]   .icon-copy[_ngcontent-%COMP%]{font-size:18px;margin-top:9px}.authentication-keka[_ngcontent-%COMP%]   .btn-text[_ngcontent-%COMP%]{gap:8px}.authentication-keka[_ngcontent-%COMP%]   .btn-nxt-text[_ngcontent-%COMP%], .authentication-keka[_ngcontent-%COMP%]   .btn-text[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;font-size:14px}"]}),e})();function w(e,t){1&e&&(l["\u0275\u0275elementContainerStart"](0),l["\u0275\u0275elementStart"](1,"div",12),l["\u0275\u0275element"](2,"mat-spinner",13),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementContainerEnd"]())}function S(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"span",23),l["\u0275\u0275text"](1,"Provide Access"),l["\u0275\u0275elementEnd"]())}function k(e,t){1&e&&l["\u0275\u0275element"](0,"mat-spinner",24)}const A=function(e,t){return{"copy-btn-disabled":e,"copy-btn":t}};function I(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",14),l["\u0275\u0275elementStart"](1,"h4",15),l["\u0275\u0275text"](2,"Tenants"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](3,"app-input-search",16),l["\u0275\u0275listener"]("change",(function(){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"](2).handleAccess()})),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](4,"div",17),l["\u0275\u0275elementStart"](5,"button",18),l["\u0275\u0275listener"]("click",(function(){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"](2).updateExternalAppAccess()})),l["\u0275\u0275template"](6,S,2,0,"span",19),l["\u0275\u0275template"](7,k,1,0,"mat-spinner",20),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](8,"button",21),l["\u0275\u0275listener"]("click",(function(){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"](2).navigateToMainPage()})),l["\u0275\u0275elementStart"](9,"span",22),l["\u0275\u0275text"](10),l["\u0275\u0275elementStart"](11,"mat-icon"),l["\u0275\u0275text"](12,"keyboard_arrow_right"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&e){const e=l["\u0275\u0275nextContext"](2);l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("required",!0)("list",e.tenantList)("disableNone",!0)("hideMatLabel",!0),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("disabled",e.accessDisabled)("ngClass",l["\u0275\u0275pureFunction2"](9,A,e.accessDisabled,!e.accessDisabled)),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",!e.accessLoader),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",e.accessLoader),l["\u0275\u0275advance"](3),l["\u0275\u0275textInterpolate1"]("Go to ",null!=e.externalAppData&&e.externalAppData.name?e.externalAppData.name:""," ")}}function D(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div"),l["\u0275\u0275template"](1,w,3,0,"ng-container",9),l["\u0275\u0275template"](2,I,13,12,"div",11),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",e.isComponentLoading),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",!e.isComponentLoading)}}function L(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"div",25),l["\u0275\u0275element"](1,"app-keka-integration"),l["\u0275\u0275elementEnd"]())}let T=(()=>{class e{constructor(e,t,n){this._router=e,this._integrationService=t,this._toaster=n,this.isAdminRole=!1,this.tenant=new C.j("",C.H.required),this.subs=new r.a,this.isComponentLoading=!1,this.accessDisabled=!0,this.authorizeFormGroup=new C.m({tenant:new C.j(null),tokenControl:new C.j(null)}),this.accessLoader=!1}ngOnInit(){return Object(o.c)(this,void 0,void 0,(function*(){this.calculateDynamicStyle(),this.isComponentLoading=!0,this.subs.sink=yield this._integrationService.getApplicationData().subscribe(e=>{this.externalAppData=e}),null==this.externalAppData&&this._router.navigateByUrl("/main/integration"),this.isAdminRole=!!this.externalAppData&&!!this.externalAppData.isAdminRole&&this.externalAppData.isAdminRole,this.tenantList=yield this.getTenantList(),this.isComponentLoading=!1}))}backButton(){this._router.navigateByUrl("/main/integration")}getTenantList(){return new Promise((e,t)=>{this.subs.sink=this._integrationService.getTenantList().subscribe(t=>{e(t.length>0?t:[])},t=>{this._toaster.showError("Error","Failed to fetch Tenant List !",3e3),console.log(t),e([])})})}navigateToMainPage(){this._router.navigateByUrl("/main/integration/integration/landing-page")}updateExternalAppAccess(){return Object(o.c)(this,void 0,void 0,(function*(){this.accessLoader=!0;let e=this.authorizeFormGroup.get("tenant").value;yield this.updateTenantExternalAppAccess(e),this.accessLoader=!1}))}updateTenantExternalAppAccess(e){return new Promise((t,n)=>{this.subs.sink=this._integrationService.updateExternalAppAccess(this.externalAppData.id,e).subscribe(e=>{"S"==e.messType?(this._toaster.showSuccess("Success","Provided Access !",3e3),t(e)):(t(null),this._toaster.showError("Failed","Failed in Providing Access !",3e3))},e=>{console.log(e),this._toaster.showError("Error","Error while Providing Access !",3e3),t(null)})})}handleAccess(){this.accessDisabled=!this.authorizeFormGroup.get("tenant").value}onResize(){this.calculateDynamicStyle()}calculateDynamicStyle(){let e=window.innerHeight-55+"px";document.documentElement.style.setProperty("--dynamicAuthPageHeight",e)}ngOnDestroy(){this.subs.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(l["\u0275\u0275directiveInject"](a.g),l["\u0275\u0275directiveInject"](d),l["\u0275\u0275directiveInject"](p.a))},e.\u0275cmp=l["\u0275\u0275defineComponent"]({type:e,selectors:[["app-authentication-page"]],hostBindings:function(e,t){1&e&&l["\u0275\u0275listener"]("resize",(function(){return t.onResize()}),!1,l["\u0275\u0275resolveWindow"])},decls:14,vars:6,consts:[[1,"authentication"],[1,"d-flex","header"],[1,"header-icon",3,"click"],[1,"img-outline"],[2,"border-radius","2px","height","36px","width","36px",3,"src"],[1,"d-block"],[1,"side-header"],[2,"font-size","12px","color","#000000cf","margin","0px"],[3,"formGroup"],[4,"ngIf"],["class","section",4,"ngIf"],["class","admin-content",4,"ngIf"],[1,"d-flex","justify-content-center","mt-3"],["matTooltip","Please wait...","diameter","30"],[1,"admin-content"],[1,"admin-header"],["placeholder","Select Tenant","formControlName","tenant",1,"inputSearch",3,"required","list","disableNone","hideMatLabel","change"],[1,"row","d-flex","mt-4"],["mat-raised-button","","type","button",1,"copy-btn",3,"disabled","ngClass","click"],["class","btn-text",4,"ngIf"],["matTooltip","Please wait...","diameter","20",4,"ngIf"],["mat-raised-button","",1,"next-btn",3,"click"],[1,"btn-nxt-text"],[1,"btn-text"],["matTooltip","Please wait...","diameter","20"],[1,"section"]],template:function(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"div",0),l["\u0275\u0275elementStart"](1,"div",1),l["\u0275\u0275elementStart"](2,"mat-icon",2),l["\u0275\u0275listener"]("click",(function(){return t.backButton()})),l["\u0275\u0275text"](3,"keyboard_arrow_left"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](4,"div",3),l["\u0275\u0275element"](5,"img",4),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](6,"span",5),l["\u0275\u0275elementStart"](7,"h4",6),l["\u0275\u0275text"](8),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](9,"p",7),l["\u0275\u0275text"](10),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](11,"form",8),l["\u0275\u0275template"](12,D,3,2,"div",9),l["\u0275\u0275template"](13,L,2,0,"div",10),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&e&&(l["\u0275\u0275advance"](5),l["\u0275\u0275property"]("src",null!=t.externalAppData&&t.externalAppData.image_url?t.externalAppData.image_url:"https://assets.kebs.app/Keka_logo.jpg",l["\u0275\u0275sanitizeUrl"]),l["\u0275\u0275advance"](3),l["\u0275\u0275textInterpolate"](null!=t.externalAppData&&t.externalAppData.name?t.externalAppData.name:"-"),l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate1"]("Connect Kebs to the apps you use with ",null!=t.externalAppData&&t.externalAppData.name?t.externalAppData.name:""," to automate your workflows."),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("formGroup",t.authorizeFormGroup),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",t.isAdminRole),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",!t.isAdminRole))},directives:[b.a,C.J,C.w,C.n,i.NgIf,g.c,m.a,v.a,C.v,C.l,C.F,y.a,i.NgClass,M],styles:[".authentication[_ngcontent-%COMP%]{height:var(--dynamicAuthPageHeight);background-color:rgba(232,177,177,.058823529411764705)}.authentication[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{border-bottom:1px solid #ee4961;gap:6px;padding:9px 3px 7px 9px;background:rgba(238,73,97,.09019607843137255);background:linear-gradient(270deg,rgba(238,73,97,.2901960784313726),rgba(238,73,97,.43137254901960786) 105.29%);align-items:center}.authentication[_ngcontent-%COMP%]   .side-header[_ngcontent-%COMP%]{font-size:18px;font-weight:700;font-family:Roboto;margin:0}.authentication[_ngcontent-%COMP%]   .header-icon[_ngcontent-%COMP%]{border-radius:7px;width:20px;height:24px;font-size:29px;display:flex;margin-top:2px;margin-left:-8px;margin-right:9px;color:#940d0d;cursor:pointer}.authentication[_ngcontent-%COMP%]   .inputSearch[_ngcontent-%COMP%]{border-radius:4px;height:40px;width:200px;border-color:rgba(232,177,177,.058823529411764705);display:flex}.authentication[_ngcontent-%COMP%]   .copy-btn[_ngcontent-%COMP%]{color:#fff;background-color:#ee4961;height:44px;display:flex;justify-content:center;align-items:center;margin-right:32px}.authentication[_ngcontent-%COMP%]   .next-btn[_ngcontent-%COMP%]{border:1px solid grey;background:#fff;height:44px}.authentication[_ngcontent-%COMP%]   .btn-text[_ngcontent-%COMP%]{gap:8px}.authentication[_ngcontent-%COMP%]   .btn-nxt-text[_ngcontent-%COMP%], .authentication[_ngcontent-%COMP%]   .btn-text[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;font-size:14px}.authentication[_ngcontent-%COMP%]   .admin-content[_ngcontent-%COMP%]{margin:41px 0 0 30px}.authentication[_ngcontent-%COMP%]   .admin-header[_ngcontent-%COMP%]{margin:0 0 5px;font-family:Roboto;font-size:16px;font-weight:600;color:#ee4961}.authentication[_ngcontent-%COMP%]   .copy-btn-disabled[_ngcontent-%COMP%]{color:#fff;background-color:#d9d9d9;height:44px;display:flex;justify-content:center;align-items:center;margin-right:32px;cursor:not-allowed}"]}),e})();var R=n("xG9w");function j(e,t){1&e&&(l["\u0275\u0275elementContainerStart"](0),l["\u0275\u0275elementStart"](1,"div",9),l["\u0275\u0275element"](2,"mat-spinner",10),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementContainerEnd"]())}function F(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",12),l["\u0275\u0275listener"]("click",(function(){l["\u0275\u0275restoreView"](e);const n=t.$implicit;return l["\u0275\u0275nextContext"](2).featureSelected(n.id)})),l["\u0275\u0275elementStart"](1,"div",13),l["\u0275\u0275elementStart"](2,"mat-icon",14),l["\u0275\u0275text"](3),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](4,"div",15),l["\u0275\u0275text"](5),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=l["\u0275\u0275nextContext"](2);l["\u0275\u0275property"]("ngClass",e.id==n.selectedId?"sr-nav-item":"r-nav-item")("routerLink",null!=e&&e.id?e.id:""),l["\u0275\u0275advance"](3),l["\u0275\u0275textInterpolate1"](" ",null!=e&&e.application_icon?e.application_icon:""," "),l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate1"](" ",null!=e&&e.name?e.name:""," ")}}function z(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div"),l["\u0275\u0275template"](1,F,6,4,"div",11),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngForOf",e.navList)}}let N=(()=>{class e{constructor(e,t,n){this._router=e,this._integrationService=t,this._toaster=n,this.isComponentLoading=!1,this.navList=[],this.isAdminRole=!1,this.subs=new r.a}ngOnInit(){return Object(o.c)(this,void 0,void 0,(function*(){this.calculateDynamicStyle(),this.isComponentLoading=!0,this.subs.sink=yield this._integrationService.selectedApp$.subscribe(e=>{console.log("Card Data",e),this.externalAppData=e,this.isAdminRole=!!this.externalAppData&&!!this.externalAppData.isAdminRole&&this.externalAppData.isAdminRole}),null==this.externalAppData&&this._router.navigateByUrl("/main/integration"),yield this.getInternalAppsList();let e=parseInt(this._router.url.split("/").pop());const t=R.find(this.navList,{id:e});this.selectedId=t&&t.id?t.id:1,this.isComponentLoading=!1}))}featureSelected(e){this.selectedId=e}backButton(){this._router.navigateByUrl(this.isAdminRole?"/main/integration/integration/authentication":"/main/integration")}getInternalAppsList(){return Object(o.c)(this,void 0,void 0,(function*(){this.navList=this.isAdminRole?yield this.getKebsInternalApps():yield this.getTenantInternalApps()}))}getKebsInternalApps(){var e,t;let n=(null===(e=this.externalAppData)||void 0===e?void 0:e.id)?null===(t=this.externalAppData)||void 0===t?void 0:t.id:null;return new Promise((e,t)=>{this.subs.sink=this._integrationService.getKEBSAdminInternalAppsForExternalApps(n).subscribe(t=>{"S"==t.messType?e(t.data):(this._toaster.showError("Error","Failed to fetch Application Data !",3e3),e([]))},t=>{console.log(t),this._toaster.showError("Error","Failed to fetch Application Data !",3e3),e([])})})}getTenantInternalApps(){var e,t;let n=(null===(e=this.externalAppData)||void 0===e?void 0:e.id)?null===(t=this.externalAppData)||void 0===t?void 0:t.id:null;return new Promise((e,t)=>{this.subs.sink=this._integrationService.getTenantAdminInternalAppsForExternalApps(n).subscribe(t=>{"S"==t.messType?e(t.data):(this._toaster.showError("Error","Failed to fetch Application Data !",3e3),e([]))},t=>{console.log(t),this._toaster.showError("Error","Failed to fetch Application Data !",3e3),e([])})})}onResize(){this.calculateDynamicStyle()}calculateDynamicStyle(){let e=window.innerHeight-55+"px";document.documentElement.style.setProperty("--dynamicNavHeight",e)}ngOnDestroy(){this.subs.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(l["\u0275\u0275directiveInject"](a.g),l["\u0275\u0275directiveInject"](d),l["\u0275\u0275directiveInject"](p.a))},e.\u0275cmp=l["\u0275\u0275defineComponent"]({type:e,selectors:[["app-feature-landing-page"]],hostBindings:function(e,t){1&e&&l["\u0275\u0275listener"]("resize",(function(){return t.onResize()}),!1,l["\u0275\u0275resolveWindow"])},decls:13,vars:4,consts:[[1,"row","d-flex","feature-landing-page"],[1,"col-2","side-content"],[1,"row","d-flex","border-bottom","pb-1","mb-2",2,"gap","6px","border-bottom","1px solid #ffffff4d !important","align-items","center"],[1,"header-icon",3,"click"],[1,"img-outline"],[2,"border-radius","2px","height","36px","width","36px",3,"src"],[1,"side-header"],[4,"ngIf"],[1,"col-10","p-0","feature-content"],[1,"d-flex","justify-content-center","mt-3"],["matTooltip","Please wait...","diameter","30"],["class","py-3 px-3 slide-from-down row",3,"ngClass","routerLink","click",4,"ngFor","ngForOf"],[1,"py-3","px-3","slide-from-down","row",3,"ngClass","routerLink","click"],[1,"mt-1","pl-0","col-2","icon"],[2,"font-size","20px !important"],[1,"col-10","px-2","my-auto"]],template:function(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"div",0),l["\u0275\u0275elementStart"](1,"div",1),l["\u0275\u0275elementStart"](2,"div",2),l["\u0275\u0275elementStart"](3,"mat-icon",3),l["\u0275\u0275listener"]("click",(function(){return t.backButton()})),l["\u0275\u0275text"](4,"keyboard_arrow_left"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](5,"div",4),l["\u0275\u0275element"](6,"img",5),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](7,"h4",6),l["\u0275\u0275text"](8),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](9,j,3,0,"ng-container",7),l["\u0275\u0275template"](10,z,2,1,"div",7),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](11,"div",8),l["\u0275\u0275element"](12,"router-outlet"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&e&&(l["\u0275\u0275advance"](6),l["\u0275\u0275property"]("src",null!=t.externalAppData&&t.externalAppData.image_url?t.externalAppData.image_url:"https://assets.kebs.app/Keka_logo.jpg",l["\u0275\u0275sanitizeUrl"]),l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate"](null!=t.externalAppData&&t.externalAppData.name?t.externalAppData.name:"-"),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",t.isComponentLoading),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",!t.isComponentLoading))},directives:[b.a,i.NgIf,a.l,g.c,m.a,i.NgForOf,i.NgClass,a.h],styles:[".feature-landing-page[_ngcontent-%COMP%]{height:100%}.feature-landing-page[_ngcontent-%COMP%]   .side-content[_ngcontent-%COMP%]{width:15vw;padding:15px 0 10px 10px;background-color:rgba(204,156,156,.1607843137254902);overflow-y:auto;height:var(--dynamicNavHeight);background:linear-gradient(270deg,rgba(238,73,97,.2901960784313726),rgba(238,73,97,.43137254901960786) 105.29%)}.feature-landing-page[_ngcontent-%COMP%]   .side-header[_ngcontent-%COMP%]{font-size:18px;font-weight:700;font-family:Roboto;margin:5px 0 0 6px}.feature-landing-page[_ngcontent-%COMP%]   .r-nav-item[_ngcontent-%COMP%]{cursor:pointer}.feature-landing-page[_ngcontent-%COMP%]   .sr-nav-item[_ngcontent-%COMP%]{background:rgba(193,17,17,.21176470588235294);border-left:5px solid rgba(116,10,25,.38823529411764707);cursor:pointer;font-weight:500;color:#fff}.feature-landing-page[_ngcontent-%COMP%]   .feature-content[_ngcontent-%COMP%]{width:76.7vw}.feature-landing-page[_ngcontent-%COMP%]   .header-icon[_ngcontent-%COMP%]{border-radius:7px;width:20px;height:24px;font-size:29px;display:flex;margin-top:2px;margin-left:-8px;margin-right:9px;color:#940d0d;cursor:pointer}"]}),e})();var K=n("0IaG");let B=(()=>{class e{constructor(e,t){this.dialogRef=e,this.inData=t,this.selectedId=1}ngOnInit(){console.log("Pop Up Component data:",this.inData.modalParams)}closeDialog(e){this.dialogRef.close(e)}changeType(e){this.selectedId=e}}return e.\u0275fac=function(t){return new(t||e)(l["\u0275\u0275directiveInject"](K.h),l["\u0275\u0275directiveInject"](K.a))},e.\u0275cmp=l["\u0275\u0275defineComponent"]({type:e,selectors:[["app-detail-pop-up"]],decls:51,vars:2,consts:[[1,"detail-styles"],[1,"row","header"],[1,"col-10","p-0","m-0"],[1,"header-main"],[1,"col-2","d-flex","p-0","m-0","justify-content-end"],[1,"close-icon",3,"click"],[1,"content"],[1,"row","main-content"],[1,"input-field"],[1,"bolder-txt"],[1,"url-txt"],[1,"row","d-flex","sub-content"],[1,"circle"],[1,"row","d-flex","border-bottom"],[3,"ngClass","click"],[1,"text-class"],[1,"row","d-block"],[1,"row","d-flex","mt-2","mb-2"],[1,"col-8","p-0","m-0"],[1,"col-4","d-flex","justify-content-end","p-0","m-0"],[1,"copy-btn"],["readonly","",1,"text-class-area"]],template:function(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"div",0),l["\u0275\u0275elementStart"](1,"div",1),l["\u0275\u0275elementStart"](2,"div",2),l["\u0275\u0275elementStart"](3,"span",3),l["\u0275\u0275text"](4,"Call Details"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](5,"div",4),l["\u0275\u0275elementStart"](6,"mat-icon",5),l["\u0275\u0275listener"]("click",(function(){return t.closeDialog(null)})),l["\u0275\u0275text"](7,"clear"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](8,"div",6),l["\u0275\u0275elementStart"](9,"div",7),l["\u0275\u0275elementStart"](10,"div",8),l["\u0275\u0275elementStart"](11,"span",9),l["\u0275\u0275text"](12,"GET"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](13,"span",10),l["\u0275\u0275text"](14,"https//engagements/v1/engagement/project/team-members"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](15,"span",9),l["\u0275\u0275text"](16,"copy"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](17,"div",11),l["\u0275\u0275elementStart"](18,"span",9),l["\u0275\u0275text"](19,"Result :"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275element"](20,"div",12),l["\u0275\u0275elementStart"](21,"span"),l["\u0275\u0275text"](22,"HTTP 500"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](23,"div",13),l["\u0275\u0275elementStart"](24,"div",14),l["\u0275\u0275listener"]("click",(function(){return t.changeType(1)})),l["\u0275\u0275elementStart"](25,"span",15),l["\u0275\u0275text"](26,"Requests"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](27,"div",14),l["\u0275\u0275listener"]("click",(function(){return t.changeType(2)})),l["\u0275\u0275elementStart"](28,"span",15),l["\u0275\u0275text"](29,"Responses"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](30,"div",16),l["\u0275\u0275elementStart"](31,"div",17),l["\u0275\u0275elementStart"](32,"div",18),l["\u0275\u0275elementStart"](33,"span",9),l["\u0275\u0275text"](34,"HEADER"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](35,"div",19),l["\u0275\u0275elementStart"](36,"button",20),l["\u0275\u0275text"](37,"Copy"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](38,"div",16),l["\u0275\u0275elementStart"](39,"textarea",21),l["\u0275\u0275text"](40,'{\n                    "candidateDetails":[{\n                        "job_id":1234,\n                        "candidate_main_details" : [{\n                            "email_id":"<EMAIL>",\n                            "phone_number":"*********",\n                            "overall_experience_years":2,\n                            "recent_company_name":"Wilbur Co"\n                        }],\n                        "personal_details": [{\n                            "first_name":"Widen",\n                            "middle_name":"S",\n                            "last_name":"Clin",\n                            "preffered_name":"Clin",\n                            "date_of_birth":"08-01-2001",\n                            "gender":1,\n                            "father_name":"Kar",\n                            "mother_name":"Eli"\n                        }],\n                        "contact_details": [{\n                            "address_line_1":"Main Road",\n                            "address_line_2":"Cross street",\n                            "address_line_3":"",\n                            "city": 1,\n                            "district": "TamilNadu",\n                            "state": 1,\n                            "pincode": 600023,\n                            "country": 2,\n                            "mobile_number": "90992920329",\n                            "landline_number": "10223933029",\n                            "country_code": 1,\n                            "current_city":2,\n                            "current_country":2\n                        }],\n                        "experience_details": [{\n                            "position":"Developer",\n                            "years_of_exp":3,\n                            "company_name":"ASD",\n                            "company_doj":"04-04-2020",\n                            "company_doe":"04-10-2023",\n                            "reason_for_exit":"test",\n                            "work_details_description":"Description"\n                        },\n                        {\n                            "position":"Consultant",\n                            "years_of_exp":1,\n                            "company_name":"BDF",\n                            "company_doj":"04-04-2020",\n                            "company_doe":"04-10-2023",\n                            "reason_for_exit":"test",\n                            "work_details_description":"Description"\n                        }\n                    ],\n                        "education_details" : [{\n                            "education_level":1,\n                            "university":"ANS",\n                            "cgpa":8.0,\n                            "percentage":"80%",\n                            "expected_completion_date":"02-04-2024",\n                            "completed_on_date":"02-04-2024",\n                            "stream":2,\n                            "is_campus_job":1\n                        },\n                        {\n                            "education_level":2,\n                            "university":"BDE",\n                            "cgpa":8.0,\n                            "percentage":"80%",\n                            "expected_completion_date":"02-04-2024",\n                            "completed_on_date":"02-04-2024",\n                            "stream":1,\n                            "is_campus_job":1\n                        }\n                    ],\n                        "diversity_details": [{\n                            "gender": 1,\n                            "race_or_ethinicity": 1,\n                            "religion":2,\n                            "community":1,\n                            "veteran_status":1,\n                            "military_services":1,\n                            "marital_status":1,\n                            "age_bracket":1,\n                            "disability":1\n                        }],\n                        "refferal_details": [{\n                            "referral_employee_name":"Kare",\n                            "referral_employee_email":"<EMAIL>"\n                        }],\n                        "additional_details": [{\n                            "custom_qn_related_to_job":"Response value1",\n                            "question_id":1\n                        },\n                        {\n                            "custom_qn_related_to_job":"Response value2",\n                            "question_id":2\n                        },\n                        {\n                            "custom_qn_related_to_job":"Response value3",\n                            "question_id":3\n                        }\n                    ],\n                        "social_link_details": [{\n                            "linkedIn_link":"www.linkedin.com",\n                            "github_link":"www.github.com",\n                            "stackoverflow_link":"www.stackoverflow.com",\n                            "medium_link":"www.medium.com"\n                        }],\n                        "govt_details": [{\n                            "aadhaar_number": "e32432rew",\n                            "pan_number": "1ed324d",\n                            "passport_number": "rf3434f3f",\n                            "ss_id": "efrwfrw24443"\n                        }],\n                        "skill_details": [{\n                            "skill_name":"Angular",\n                            "profiency_level":4\n                        },\n                        {\n                            "skill_name":"SQL",\n                            "profiency_level":3\n                        },\n                        {\n                            "skill_name":"NodeJS",\n                            "profiency_level":2\n                        }\n                    ],\n                        "salary_expectation_details": [{\n                            "current_ctc": 100000,\n                            "expected_ctc": 120000,\n                            "current_fixed_ctc": 100000,\n                            "current_variable_ctc": 20000\n                        }],\n                        "marksheet_details": [{\n                            "education_details_id":1,\n                            "s3_document_link":"www.s2link.com"\n                        },\n                        {\n                            "education_details_id":2,\n                            "s3_document_link":"www.s2link.com"\n                        },\n                        {\n                            "education_details_id":3,\n                            "s3_document_link":"www.s2link.com"\n                        }\n                    ],\n                        "language_details": [{\n                            "language_id":1,\n                            "can_read":1,\n                            "can_write":1,\n                            "can_speak":1\n                        },\n                        {\n                            "language_id":2,\n                            "can_read":1,\n                            "can_write":1,\n                            "can_speak":1\n                        },\n                        {\n                            "language_id":3,\n                            "can_read":1,\n                            "can_write":1,\n                            "can_speak":1\n                        }\n                    ],\n                        "acknowledgement_details": [{\n                            "acknowledged_privacy_policy": 1,\n                            "acknowledged_terms_and_condt": 1\n                        }],\n                        "resume_details":[{\n                            "file":"uyjdd"\n                        }]\n                    }]\n                    // "db":"stelliumdev"\n                }'),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](41,"div",17),l["\u0275\u0275elementStart"](42,"div",18),l["\u0275\u0275elementStart"](43,"span",9),l["\u0275\u0275text"](44,"BODY"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](45,"div",19),l["\u0275\u0275elementStart"](46,"button",20),l["\u0275\u0275text"](47,"Copy"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](48,"div",16),l["\u0275\u0275elementStart"](49,"textarea",21),l["\u0275\u0275text"](50,'{"err": false,"msg": "candidate added successfully !"}'),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&e&&(l["\u0275\u0275advance"](24),l["\u0275\u0275property"]("ngClass",1==t.selectedId?"toggle-class-selected":"toggle-class"),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("ngClass",2==t.selectedId?"toggle-class-selected":"toggle-class"))},directives:[b.a,i.NgClass],styles:[".detail-styles[_ngcontent-%COMP%]{font-family:Roboto}.detail-styles[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{padding:5px 3px 0 15px;background-color:rgba(193,17,17,.1215686275);height:76px;align-items:center;background:linear-gradient(270deg,rgba(238,73,97,.6901960784313725),rgba(238,73,97,.7686274509803922) 105.29%)}.detail-styles[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%]{font-size:16px;background:none;border:none;font-weight:600;color:#fff;cursor:pointer}.detail-styles[_ngcontent-%COMP%]   .main-content[_ngcontent-%COMP%]{height:50px;overflow:auto;margin-bottom:12px}.detail-styles[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]{display:flex;gap:20px;padding:8px;align-items:center;border-radius:2px;border:1px solid rgba(238,73,97,.8)}.detail-styles[_ngcontent-%COMP%]   .url-txt[_ngcontent-%COMP%]{width:401px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.detail-styles[_ngcontent-%COMP%]   .toggle-class[_ngcontent-%COMP%]{padding:8px;cursor:pointer;width:140px;height:50px;display:flex;justify-content:center;align-items:center;border:1px solid hsla(0,0%,50.2%,.3215686274509804);background:hsla(0,0%,88.6%,.5215686274509804)}.detail-styles[_ngcontent-%COMP%]   .toggle-class[_ngcontent-%COMP%]   .text-class[_ngcontent-%COMP%]{font-size:15px;font-weight:500;line-height:24px;letter-spacing:.02em;text-align:left;font-family:Roboto;color:rgba(69,66,66,.6313725490196078)}.detail-styles[_ngcontent-%COMP%]   .toggle-class-selected[_ngcontent-%COMP%]{padding:8px;cursor:pointer;width:140px;height:50px;display:flex;justify-content:center;align-items:center;border:1px solid hsla(0,0%,50.2%,.3215686274509804);border-bottom:none;background:#fff}.detail-styles[_ngcontent-%COMP%]   .toggle-class-selected[_ngcontent-%COMP%]   .text-class[_ngcontent-%COMP%]{font-size:15px;font-weight:500;line-height:24px;letter-spacing:.02em;text-align:left;font-family:Roboto;color:#ee4961}.detail-styles[_ngcontent-%COMP%]   .copy-btn[_ngcontent-%COMP%]{background:#f0f8ff;border:none;border-radius:2px;padding:5px 10px 5px 12px;font-size:13px;font-weight:500;color:#0390f4}.detail-styles[_ngcontent-%COMP%]   .text-class-area[_ngcontent-%COMP%]{overflow-y:scroll;height:154px;width:530px;display:flex;justify-content:space-between;background:rgba(240,248,255,.6392156862745098);background:rgba(238,73,97,.0392156862745098);border:1px solid rgba(238,73,97,.3215686274509804);border-radius:3px;color:rgba(0,0,0,.8)}.detail-styles[_ngcontent-%COMP%]   .bolder-txt[_ngcontent-%COMP%]{font-size:13px;font-weight:500;font-family:Roboto}.detail-styles[_ngcontent-%COMP%]   .header-main[_ngcontent-%COMP%]{font-size:17px;font-family:Roboto;font-weight:500;color:#ee4961;color:#fff}.detail-styles[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{width:10px;border-radius:50%;background-color:rgba(0,128,0,.49019607843137253);height:10px}.detail-styles[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{padding:30px;height:85vh;overflow:auto}.detail-styles[_ngcontent-%COMP%]   .sub-content[_ngcontent-%COMP%]{gap:9px;align-items:baseline;margin-bottom:12px}.detail-styles[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]:focus-visible{outline-offset:0;border:1px solid rgba(238,73,97,.3215686274509804)}"]}),e})();var H=n("bSwM"),V=n("d3UM"),G=n("iadO"),U=n("FKr1"),q=n("ZzPI"),$=n("6t9p");function W(e,t){1&e&&(l["\u0275\u0275elementContainerStart"](0),l["\u0275\u0275elementStart"](1,"div",11),l["\u0275\u0275element"](2,"mat-spinner",12),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementContainerEnd"]())}function J(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"span"),l["\u0275\u0275text"](1),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"]().$implicit,t=l["\u0275\u0275nextContext"](4);l["\u0275\u0275advance"](1),l["\u0275\u0275textInterpolate"](t.getSubscribe(e.id)?"Unsubscribe":"Subscribe")}}function Z(e,t){1&e&&l["\u0275\u0275element"](0,"mat-spinner",23)}const X=function(e){return{"background-color":e}};function Q(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"tr"),l["\u0275\u0275elementStart"](1,"td",18),l["\u0275\u0275element"](2,"mat-checkbox"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](3,"td",19),l["\u0275\u0275elementStart"](4,"span",20),l["\u0275\u0275text"](5),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](6,"td",19),l["\u0275\u0275elementStart"](7,"span",20),l["\u0275\u0275text"](8),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](9,"td",18),l["\u0275\u0275elementStart"](10,"button",21),l["\u0275\u0275listener"]("click",(function(){l["\u0275\u0275restoreView"](e);const n=t.$implicit,i=l["\u0275\u0275nextContext"](4);return i.changeSubscription(n.id,i.getSubscribe(n.id))})),l["\u0275\u0275template"](11,J,2,1,"span",9),l["\u0275\u0275template"](12,Z,1,0,"mat-spinner",22),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=l["\u0275\u0275nextContext"](4);l["\u0275\u0275advance"](5),l["\u0275\u0275textInterpolate"](e.name),l["\u0275\u0275advance"](3),l["\u0275\u0275textInterpolate"](e.description),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngStyle",l["\u0275\u0275pureFunction1"](5,X,(n.getSubscribe(e.id),"#ee4961"))),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",!n.subscribeLoading),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",n.subscribeLoading)}}function Y(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"tbody"),l["\u0275\u0275template"](1,Q,13,7,"tr",17),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"](3);l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngForOf",e.eventList)}}function ee(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"tbody"),l["\u0275\u0275elementStart"](1,"tr"),l["\u0275\u0275elementStart"](2,"td",24),l["\u0275\u0275text"](3,"No Channels Available"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]())}function te(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div",13),l["\u0275\u0275elementStart"](1,"table",14),l["\u0275\u0275elementStart"](2,"thead"),l["\u0275\u0275elementStart"](3,"tr"),l["\u0275\u0275elementStart"](4,"th",15),l["\u0275\u0275element"](5,"mat-checkbox"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](6,"th",16),l["\u0275\u0275text"](7,"Name"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](8,"th",16),l["\u0275\u0275text"](9,"Description"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275element"](10,"th",15),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](11,Y,2,1,"tbody",9),l["\u0275\u0275template"](12,ee,4,0,"tbody",9),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"](2);l["\u0275\u0275advance"](11),l["\u0275\u0275property"]("ngIf",!e.eventNoData),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",e.eventNoData)}}function ne(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div",7),l["\u0275\u0275elementStart"](1,"h4",8),l["\u0275\u0275text"](2,"Channel Subscriptions"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](3,W,3,0,"ng-container",9),l["\u0275\u0275template"](4,te,13,2,"div",10),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("ngIf",e.isComponentLoading),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",!e.isComponentLoading)}}function ie(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"mat-option",41),l["\u0275\u0275text"](1),l["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;l["\u0275\u0275property"]("value",e.id),l["\u0275\u0275advance"](1),l["\u0275\u0275textInterpolate1"](" ",e.name," ")}}function ae(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"mat-option",41),l["\u0275\u0275text"](1),l["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;l["\u0275\u0275property"]("value",e.id),l["\u0275\u0275advance"](1),l["\u0275\u0275textInterpolate1"](" ",e.value," ")}}function oe(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"tr",42),l["\u0275\u0275listener"]("click",(function(){l["\u0275\u0275restoreView"](e);const n=t.$implicit;return l["\u0275\u0275nextContext"](2).openDetailDialog(n)})),l["\u0275\u0275elementStart"](1,"td",18),l["\u0275\u0275element"](2,"div",43),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](3,"td",18),l["\u0275\u0275elementStart"](4,"span",20),l["\u0275\u0275text"](5),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](6,"td",18),l["\u0275\u0275elementStart"](7,"span",20),l["\u0275\u0275text"](8),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](9,"td",19),l["\u0275\u0275elementStart"](10,"span",20),l["\u0275\u0275text"](11),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](12,"td",19),l["\u0275\u0275elementStart"](13,"span",20),l["\u0275\u0275text"](14),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;l["\u0275\u0275advance"](5),l["\u0275\u0275textInterpolate"](e.http),l["\u0275\u0275advance"](3),l["\u0275\u0275textInterpolate"](e.method),l["\u0275\u0275advance"](3),l["\u0275\u0275textInterpolate"](e.endPoint),l["\u0275\u0275advance"](3),l["\u0275\u0275textInterpolate"](e.timestamp)}}function re(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div",25),l["\u0275\u0275elementStart"](1,"div",26),l["\u0275\u0275elementStart"](2,"span",27),l["\u0275\u0275text"](3,"Filter by:"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](4,"div",28),l["\u0275\u0275elementStart"](5,"span",29),l["\u0275\u0275text"](6,"Method"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](7,"mat-form-field",30),l["\u0275\u0275elementStart"](8,"mat-select",31),l["\u0275\u0275template"](9,ie,2,2,"mat-option",32),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](10,"div",28),l["\u0275\u0275elementStart"](11,"span",29),l["\u0275\u0275text"](12,"Response"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](13,"mat-form-field",30),l["\u0275\u0275elementStart"](14,"mat-select",33),l["\u0275\u0275template"](15,ae,2,2,"mat-option",32),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](16,"div",34),l["\u0275\u0275elementStart"](17,"div"),l["\u0275\u0275elementStart"](18,"span",29),l["\u0275\u0275text"](19,"Start Date"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](20,"mat-form-field",35),l["\u0275\u0275element"](21,"input",36),l["\u0275\u0275element"](22,"mat-datepicker-toggle",37),l["\u0275\u0275element"](23,"mat-datepicker",null,38),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](25,"div"),l["\u0275\u0275elementStart"](26,"span",29),l["\u0275\u0275text"](27,"End Date"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](28,"mat-form-field",35),l["\u0275\u0275element"](29,"input",36),l["\u0275\u0275element"](30,"mat-datepicker-toggle",37),l["\u0275\u0275element"](31,"mat-datepicker",null,39),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](33,"div",13),l["\u0275\u0275elementStart"](34,"table",14),l["\u0275\u0275elementStart"](35,"thead"),l["\u0275\u0275elementStart"](36,"tr"),l["\u0275\u0275element"](37,"th",15),l["\u0275\u0275elementStart"](38,"th",15),l["\u0275\u0275text"](39,"HTTP"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](40,"th",15),l["\u0275\u0275text"](41,"Method"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](42,"th",16),l["\u0275\u0275text"](43,"End Point"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](44,"th",16),l["\u0275\u0275text"](45,"Timestamp"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](46,"tbody"),l["\u0275\u0275template"](47,oe,15,4,"tr",40),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275reference"](24),t=l["\u0275\u0275reference"](32),n=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](9),l["\u0275\u0275property"]("ngForOf",n.methodFilter),l["\u0275\u0275advance"](6),l["\u0275\u0275property"]("ngForOf",n.responseList),l["\u0275\u0275advance"](6),l["\u0275\u0275property"]("matDatepicker",e),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("for",e),l["\u0275\u0275advance"](7),l["\u0275\u0275property"]("matDatepicker",t),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("for",t),l["\u0275\u0275advance"](17),l["\u0275\u0275property"]("ngForOf",n.monitorDataList)}}function le(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"div"),l["\u0275\u0275elementStart"](1,"div",11),l["\u0275\u0275element"](2,"mat-spinner",12),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]())}function se(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div"),l["\u0275\u0275elementStart"](1,"dx-data-grid",45),l["\u0275\u0275element"](2,"dxo-scrolling",46),l["\u0275\u0275element"](3,"dxo-paging",47),l["\u0275\u0275element"](4,"dxo-pager",48),l["\u0275\u0275element"](5,"dxo-column-chooser",49),l["\u0275\u0275element"](6,"dxo-header-filter",50),l["\u0275\u0275element"](7,"dxi-column",51),l["\u0275\u0275element"](8,"dxi-column",52),l["\u0275\u0275element"](9,"dxi-column",53),l["\u0275\u0275element"](10,"dxi-column",54),l["\u0275\u0275element"](11,"dxo-column-fixing",55),l["\u0275\u0275element"](12,"dxo-search-panel",50),l["\u0275\u0275element"](13,"dxo-selection",56),l["\u0275\u0275element"](14,"dxo-export",55),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"](2);l["\u0275\u0275advance"](1),l["\u0275\u0275styleMap"]("height:"+e.errorTableHeight+"px"),l["\u0275\u0275property"]("customizeColumns",e.customizeColumns)("dataSource",e.errorLog)("allowColumnResizing",!0)("allowColumnReordering",!0)("showBorders",!0),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("pageSize",10)("pageIndex",0),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("visible",!0)("showInfo",!0)("showNavigationButtons",!0),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("enabled",!0),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("visible",!0),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("minWidth",150)("allowEditing",!1),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("allowEditing",!1),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("allowEditing",!1),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("allowEditing",!1),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("enabled",!0),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("visible",!0),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("enabled",!0)}}function ce(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div",44),l["\u0275\u0275template"](1,le,3,0,"div",9),l["\u0275\u0275template"](2,se,15,22,"div",9),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",e.isComponentLoading),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",!e.isComponentLoading)}}let de=(()=>{class e{constructor(e,t,n,i){this.dialog=e,this._router=t,this._integrationService=n,this._toaster=i,this.monitorDataList=[{id:1,http:200,method:"GET",endPoint:"/app/main/accounts/recent/modified",timestamp:"March 20, 2024 9.00 AM IST"},{id:2,http:200,method:"POST",endPoint:"/app/main/contacts/recent/modified",timestamp:"March 20, 2024 9.00 AM IST"}],this.methodFilter=[{id:1,name:"Get",checked:!1},{id:2,name:"Post",checked:!1},{id:3,name:"Put",checked:!1},{id:4,name:"Patch",checked:!1},{id:5,name:"Delete",checked:!1}],this.responseList=[{id:1,value:200},{id:2,value:404},{id:3,value:500},{id:4,value:504}],this.isComponentLoading=!1,this.range=new C.m({start:new C.j(null),end:new C.j(null)}),this.subs=new r.a,this.isAdminRole=!1,this.eventList=[],this.subscribedData=[],this.subscribeLoading=!1,this.eventNoData=!1,this.errorLog=[{date:"2024-03-20",time:"20:06:20",error_message:"Timesheet Retrieval Failed for KEKA Employee Id: E201",error_code:"ERR-TL-001"},{date:"2024-03-20",time:"20:06:20",error_message:"Timesheet Retrieval Failed for KEKA Employee Id: E201",error_code:"ERR-TL-001"},{date:"2024-03-20",time:"20:06:20",error_message:"Timesheet Retrieval Failed for KEKA Employee Id: E201",error_code:"ERR-TL-001"},{date:"2024-03-20",time:"20:06:20",error_message:"Timesheet Retrieval Failed for KEKA Employee Id: E201",error_code:"ERR-TL-001"},{date:"2024-03-20",time:"20:06:20",error_message:"Timesheet Retrieval Failed for KEKA Employee Id: E201",error_code:"ERR-TL-001"},{date:"2024-03-20",time:"20:06:20",error_message:"Timesheet Retrieval Failed for KEKA Employee Id: E201",error_code:"ERR-TL-001"},{date:"2024-03-20",time:"20:06:20",error_message:"Timesheet Retrieval Failed for KEKA Employee Id: E201",error_code:"ERR-TL-001"},{date:"2024-03-20",time:"20:06:20",error_message:"Timesheet Retrieval Failed for KEKA Employee Id: E201",error_code:"ERR-TL-001"},{date:"2024-03-20",time:"20:06:20",error_message:"Timesheet Retrieval Failed for KEKA Employee Id: E201",error_code:"ERR-TL-001"},{date:"2024-03-20",time:"20:06:20",error_message:"Timesheet Retrieval Failed for KEKA Employee Id: E201",error_code:"ERR-TL-001"}]}ngOnInit(){return Object(o.c)(this,void 0,void 0,(function*(){this.calculateDynamicStyle(),this.isComponentLoading=!0,this.subs.sink=yield this._integrationService.selectedApp$.subscribe(e=>{console.log("Card Data",e),this.externalAppData=e,this.isAdminRole=!!this.externalAppData&&!!this.externalAppData.isAdminRole&&this.externalAppData.isAdminRole}),this.selectedId=1,1==this.selectedId&&(this.subscribedData=yield this.getSubscriptionList()),yield this.getData(),this.isComponentLoading=!1}))}changeType(e){return Object(o.c)(this,void 0,void 0,(function*(){this.selectedId=e,1==this.selectedId&&(this.subscribedData=yield this.getSubscriptionList()),yield this.getData()}))}openDetailDialog(e){this.dialog.open(B,{width:"574px",height:"744px",position:{top:"0px",right:"0%"},data:{modalParams:e}}).afterClosed().subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){console.log("Dialog Closed",e)})))}getChannelList(){var e,t;let n=(null===(e=this.externalAppData)||void 0===e?void 0:e.id)?null===(t=this.externalAppData)||void 0===t?void 0:t.id:null,i=parseInt(this._router.url.split("/").pop());return new Promise((e,t)=>{this.subs.sink=this._integrationService.getChannelList(n,i).subscribe(t=>{"S"==t.messType?e(t.data):(this._toaster.showError("Error","Failed to fetch Channel List !",3e3),e([]))},t=>{console.log(t),this._toaster.showError("Error","Failed to fetch Channel List !",3e3),e([])})})}getData(){return Object(o.c)(this,void 0,void 0,(function*(){if(this.isComponentLoading=!0,1==this.selectedId)this.eventList=yield this.getChannelList(),0==this.eventList.length&&(this.eventNoData=!0);else if(3==this.selectedId){let e=parseInt(this._router.url.split("/").pop());1==e?(console.log("Timesheet called!"),this.errorLog=yield this.getTimesheetErrorLog()):2==e?(console.log("ED Called ! "),this.errorLog=yield this.getEDErrorLog()):3==e&&(console.log("Project Called ! "),this.errorLog=yield this.getProjectErrorLog())}this.isComponentLoading=!1}))}getSubscriptionList(){var e,t;let n=(null===(e=this.externalAppData)||void 0===e?void 0:e.id)?null===(t=this.externalAppData)||void 0===t?void 0:t.id:null,i=parseInt(this._router.url.split("/").pop());return new Promise((e,t)=>{this.subs.sink=this._integrationService.getSubscriptionList(n,i).subscribe(t=>{"S"==t.messType?e(t.data):(this._toaster.showError("Error","Failed to fetch Subscripted Data !",3e3),e([]))},t=>{console.log(t),this._toaster.showError("Error","Failed to fetch Subscripted Data !",3e3),e([])})})}getSubscribe(e){const t=R.find(this.subscribedData,{id:e});return!!t&&!!t.is_subscribed&&t.is_subscribed}changeSubscription(e,t){return Object(o.c)(this,void 0,void 0,(function*(){this.subscribeLoading=!0;let n=t?0:1;yield this.subscribeEventItem(e,n),this.subscribedData=yield this.getSubscriptionList(),this.subscribeLoading=!1}))}subscribeEventItem(e,t){return new Promise((n,i)=>{this.subs.sink=this._integrationService.updateSubscibeChannel(e,t).subscribe(e=>{"S"==e.messType?(this._toaster.showSuccess("Success","Provided Access !",3e3),n(e)):(n(null),this._toaster.showError("Failed","Failed to Subscribe !",3e3))},e=>{console.log(e),this._toaster.showError("Error","Error while Subscribing !",3e3),n(null)})})}customizeColumns(e){e[0].width=70}onResize(){this.calculateDynamicStyle()}calculateDynamicStyle(){let e=window.innerHeight-190+"px",t=window.innerHeight-251+"px";this.errorTableHeight=window.innerHeight-152,document.documentElement.style.setProperty("--dynamicTableHeight",e),document.documentElement.style.setProperty("--dynamicMonitorTableHeight",t)}getEDErrorLog(){return new Promise((e,t)=>{this.subs.sink=this._integrationService.getEDErrorLogs().subscribe(t=>{"S"==t.messType?e(t.data):(this._toaster.showError("Error","Failed to fetch Employee Directory Error Log Data !",3e3),e([]))},t=>{console.log(t),this._toaster.showError("Error","Failed to fetch  Employee Directory Error Log Data !",3e3),e([])})})}getTimesheetErrorLog(){return new Promise((e,t)=>{this.subs.sink=this._integrationService.getTimesheetErrorLogs().subscribe(t=>{"S"==t.messType?e(t.data):(this._toaster.showError("Error","Failed to fetch Timesheet Error Log Data !",3e3),e([]))},t=>{console.log(t),this._toaster.showError("Error","Failed to fetch Timesheet Error Logd Data !",3e3),e([])})})}getProjectErrorLog(){return new Promise((e,t)=>{this.subs.sink=this._integrationService.getProjectErrorLogs().subscribe(t=>{"S"==t.messType?e(t.data):(this._toaster.showError("Error","Failed to fetch Project Error Log Data !",3e3),e([]))},t=>{console.log(t),this._toaster.showError("Error","Failed to fetch Project Error Logd Data !",3e3),e([])})})}ngOnDestroy(){this.subs.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(l["\u0275\u0275directiveInject"](K.b),l["\u0275\u0275directiveInject"](a.g),l["\u0275\u0275directiveInject"](d),l["\u0275\u0275directiveInject"](p.a))},e.\u0275cmp=l["\u0275\u0275defineComponent"]({type:e,selectors:[["app-app-landing-page"]],hostBindings:function(e,t){1&e&&l["\u0275\u0275listener"]("resize",(function(){return t.onResize()}),!1,l["\u0275\u0275resolveWindow"])},decls:11,vars:5,consts:[[1,"app-page"],[1,"row","d-flex",2,"background-color","#e2e2e23d"],[3,"ngClass","click"],[1,"text-class"],["class","events-class",4,"ngIf"],["class","monitoring-class",4,"ngIf"],["class","error-log-class",4,"ngIf"],[1,"events-class"],[1,"event-header"],[4,"ngIf"],["class","table-class",4,"ngIf"],[1,"d-flex","justify-content-center","mt-3"],["matTooltip","Please wait...","diameter","30"],[1,"table-class"],[1,"table","table-striped","table-bordered"],[1,"col-1","header-table-txt"],[1,"col-4","header-table-txt"],[4,"ngFor","ngForOf"],[1,"col-1"],[1,"col-4"],[1,"body-txt-value"],[1,"btn","btn-primary","btn-sm","action-button",3,"ngStyle","click"],["matTooltip","Please wait...","diameter","20",4,"ngIf"],["matTooltip","Please wait...","diameter","20"],["colspan","4",1,"text-center"],[1,"monitoring-class"],[1,"row","d-flex","align-items-center","filter-row"],[1,"filter-txt"],[1,"d-block"],[1,"filter-header"],["appearance","outline",1,"form-field-class",2,"width","80%"],["placeholder","Method","multiple",""],[3,"value",4,"ngFor","ngForOf"],["placeholder","Response","multiple",""],[1,"date-group"],["appearance","outline",1,"date-field-class",2,"width","80%"],["matInput","",3,"matDatepicker"],["matIconSuffix","",3,"for"],["picker",""],["picker1",""],[3,"click",4,"ngFor","ngForOf"],[3,"value"],[3,"click"],[1,"circle"],[1,"error-log-class"],["id","gridContainer",3,"customizeColumns","dataSource","allowColumnResizing","allowColumnReordering","showBorders"],["rowRenderingMode","virtual"],[3,"pageSize","pageIndex"],[3,"visible","showInfo","showNavigationButtons"],["mode","select",3,"enabled"],[3,"visible"],["dataField","date","dataType","date","format","dd-MM-yyyy","caption","Date",3,"minWidth","allowEditing"],["dataField","time","caption","Time",3,"allowEditing"],["dataField","error_message","caption","Error Message",3,"allowEditing"],["dataField","error_code","caption","Error Code",3,"allowEditing"],[3,"enabled"],["mode","single"]],template:function(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"div",0),l["\u0275\u0275elementStart"](1,"div",1),l["\u0275\u0275elementStart"](2,"div",2),l["\u0275\u0275listener"]("click",(function(){return t.changeType(1)})),l["\u0275\u0275elementStart"](3,"span",3),l["\u0275\u0275text"](4,"Events"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](5,"div",2),l["\u0275\u0275listener"]("click",(function(){return t.changeType(3)})),l["\u0275\u0275elementStart"](6,"span",3),l["\u0275\u0275text"](7,"Error Logs"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](8,ne,5,2,"div",4),l["\u0275\u0275template"](9,re,48,7,"div",5),l["\u0275\u0275template"](10,ce,3,2,"div",6),l["\u0275\u0275elementEnd"]()),2&e&&(l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngClass",1==t.selectedId?"toggle-class-selected":"toggle-class"),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("ngClass",3==t.selectedId?"toggle-class-selected":"toggle-class"),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("ngIf",1==t.selectedId),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",2==t.selectedId),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",3==t.selectedId))},directives:[i.NgClass,i.NgIf,g.c,m.a,H.a,i.NgForOf,i.NgStyle,O.c,V.c,E.b,G.g,G.i,G.f,U.p,q.a,$.Jd,$.od,$.md,$.tb,$.Cc,$.g,$.vb,$.Md,$.Od,$.Sb],styles:[".app-page[_ngcontent-%COMP%]{padding:6px}.app-page[_ngcontent-%COMP%]   .toggle-class[_ngcontent-%COMP%]{padding:8px;cursor:pointer;width:196px;height:58px;display:flex;justify-content:center;align-items:center;border:1px solid hsla(0,0%,50.2%,.3215686274509804);background:hsla(0,0%,88.6%,.5215686274509804)}.app-page[_ngcontent-%COMP%]   .toggle-class[_ngcontent-%COMP%]   .text-class[_ngcontent-%COMP%]{font-size:15px;font-weight:500;line-height:24px;letter-spacing:.02em;text-align:left;font-family:Roboto;color:rgba(69,66,66,.6313725490196078)}.app-page[_ngcontent-%COMP%]   .toggle-class-selected[_ngcontent-%COMP%]{padding:8px;cursor:pointer;width:196px;height:58px;display:flex;justify-content:center;align-items:center;border:1px solid hsla(0,0%,50.2%,.3215686274509804);border-bottom:none;background:#fff}.app-page[_ngcontent-%COMP%]   .toggle-class-selected[_ngcontent-%COMP%]   .text-class[_ngcontent-%COMP%]{font-size:15px;font-weight:500;line-height:24px;letter-spacing:.02em;text-align:left;font-family:Roboto;color:#ee4961}.app-page[_ngcontent-%COMP%]   .events-class[_ngcontent-%COMP%]{height:100%;padding:13px 10px 10px}.app-page[_ngcontent-%COMP%]   .events-class[_ngcontent-%COMP%]   .table-class[_ngcontent-%COMP%]{height:100%}.app-page[_ngcontent-%COMP%]   .events-class[_ngcontent-%COMP%]   table[_ngcontent-%COMP%]{border-collapse:collapse;width:100%}.app-page[_ngcontent-%COMP%]   .events-class[_ngcontent-%COMP%]   td[_ngcontent-%COMP%], .app-page[_ngcontent-%COMP%]   .events-class[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{text-align:center;padding:10.7px}.app-page[_ngcontent-%COMP%]   .events-class[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{position:sticky;top:-1px;z-index:1;background:#fff}.app-page[_ngcontent-%COMP%]   .events-class[_ngcontent-%COMP%]   .table-bordered[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]{cursor:pointer;visibility:hidden;border:none;width:82px;font-size:12px;height:30px;display:flex;justify-content:center;align-items:center}.app-page[_ngcontent-%COMP%]   .events-class[_ngcontent-%COMP%]   .table-bordered[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover   .action-button[_ngcontent-%COMP%]{cursor:pointer;visibility:visible;border:none;width:82px;font-size:12px;height:30px;display:flex;justify-content:center;align-items:center}.app-page[_ngcontent-%COMP%]   .events-class[_ngcontent-%COMP%]   .table-bordered[_ngcontent-%COMP%]   td[_ngcontent-%COMP%], .app-page[_ngcontent-%COMP%]   .events-class[_ngcontent-%COMP%]   .table-bordered[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{border:none!important}.app-page[_ngcontent-%COMP%]   .events-class[_ngcontent-%COMP%]   .header-table-txt[_ngcontent-%COMP%]{font-family:Roboto;font-size:14px;line-height:16px;text-align:center;font-weight:600}.app-page[_ngcontent-%COMP%]   .events-class[_ngcontent-%COMP%]   .body-txt-value[_ngcontent-%COMP%]{font-size:13px;font-weight:400;font-family:Roboto;display:block;text-overflow:ellipsis;overflow:hidden;white-space:nowrap;width:98%}.app-page[_ngcontent-%COMP%]   .events-class[_ngcontent-%COMP%]   .table-class[_ngcontent-%COMP%]{height:var(--dynamicTableHeight);overflow:auto}.app-page[_ngcontent-%COMP%]   .events-class[_ngcontent-%COMP%]   .table-striped[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:nth-of-type(odd){background-color:rgba(233,225,226,.4392156862745098);cursor:pointer}.app-page[_ngcontent-%COMP%]   .events-class[_ngcontent-%COMP%]   .table-striped[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]{cursor:pointer}.app-page[_ngcontent-%COMP%]   .events-class[_ngcontent-%COMP%]   .event-header[_ngcontent-%COMP%]{font-size:17px;font-weight:600;line-height:24px;text-align:left;text-transform:capitalize}.app-page[_ngcontent-%COMP%]   .monitoring-class[_ngcontent-%COMP%]{padding:13px 10px 10px}.app-page[_ngcontent-%COMP%]   .monitoring-class[_ngcontent-%COMP%]   .filter-txt[_ngcontent-%COMP%]{font-size:14px;font-weight:400;margin-right:11px;margin-top:13px}.app-page[_ngcontent-%COMP%]   .monitoring-class[_ngcontent-%COMP%]   .table-class[_ngcontent-%COMP%]{height:100%}.app-page[_ngcontent-%COMP%]   .monitoring-class[_ngcontent-%COMP%]   table[_ngcontent-%COMP%]{border-collapse:collapse;width:100%}.app-page[_ngcontent-%COMP%]   .monitoring-class[_ngcontent-%COMP%]   td[_ngcontent-%COMP%], .app-page[_ngcontent-%COMP%]   .monitoring-class[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{text-align:center;padding:10.7px}.app-page[_ngcontent-%COMP%]   .monitoring-class[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{position:sticky;top:-1px;z-index:1;background:#fff}.app-page[_ngcontent-%COMP%]   .monitoring-class[_ngcontent-%COMP%]   .table-bordered[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   .action-button[_ngcontent-%COMP%]{cursor:pointer;visibility:hidden;background:#ee4961;border:none;width:82px;font-size:12px;height:30px;display:flex;justify-content:center;align-items:center}.app-page[_ngcontent-%COMP%]   .monitoring-class[_ngcontent-%COMP%]   .table-bordered[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover   .action-button[_ngcontent-%COMP%]{cursor:pointer;visibility:visible;background:#ee4961;border:none;width:82px;font-size:12px;height:30px;display:flex;justify-content:center;align-items:center}.app-page[_ngcontent-%COMP%]   .monitoring-class[_ngcontent-%COMP%]   .table-bordered[_ngcontent-%COMP%]   td[_ngcontent-%COMP%], .app-page[_ngcontent-%COMP%]   .monitoring-class[_ngcontent-%COMP%]   .table-bordered[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{border:none!important}.app-page[_ngcontent-%COMP%]   .monitoring-class[_ngcontent-%COMP%]   .header-table-txt[_ngcontent-%COMP%]{font-family:Roboto;font-size:14px;line-height:16px;text-align:center;font-weight:600}.app-page[_ngcontent-%COMP%]   .monitoring-class[_ngcontent-%COMP%]   .body-txt-value[_ngcontent-%COMP%]{font-size:13px;font-weight:400;font-family:Roboto;display:block;text-overflow:ellipsis;overflow:hidden;white-space:nowrap;width:98%}.app-page[_ngcontent-%COMP%]   .monitoring-class[_ngcontent-%COMP%]   .table-class[_ngcontent-%COMP%]{height:var(--dynamicMonitorTableHeight);overflow:auto}.app-page[_ngcontent-%COMP%]   .monitoring-class[_ngcontent-%COMP%]   .table-striped[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:nth-of-type(odd){background-color:rgba(233,225,226,.4392156862745098);cursor:pointer}.app-page[_ngcontent-%COMP%]   .monitoring-class[_ngcontent-%COMP%]   .table-striped[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]{cursor:pointer}.app-page[_ngcontent-%COMP%]   .monitoring-class[_ngcontent-%COMP%]   .event-header[_ngcontent-%COMP%]{font-size:17px;font-weight:600;line-height:24px;text-align:left;text-transform:capitalize}.app-page[_ngcontent-%COMP%]   .monitoring-class[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{width:10px;border-radius:50%;background-color:rgba(0,128,0,.49019607843137253);height:10px;margin-left:20px}.app-page[_ngcontent-%COMP%]   .monitoring-class[_ngcontent-%COMP%]   .method-filter[_ngcontent-%COMP%]   .dropdown-icon[_ngcontent-%COMP%]{font-size:24px}.app-page[_ngcontent-%COMP%]   .monitoring-class[_ngcontent-%COMP%]   .calendar-icon[_ngcontent-%COMP%]{font-size:18px;margin-top:5px}.app-page[_ngcontent-%COMP%]   .monitoring-class[_ngcontent-%COMP%]   .date-class[_ngcontent-%COMP%]{align-items:center;gap:14px;border:1px solid rgba(222,86,86,.21176470588235294);width:-moz-fit-content;width:fit-content;margin-bottom:7px;border-radius:2px;background:rgba(238,158,158,.07058823529411765);display:flex}.app-page[_ngcontent-%COMP%]   .filter-header[_ngcontent-%COMP%]{display:block;font-size:12px;color:#ee4961;font-weight:500}.app-page[_ngcontent-%COMP%]   .date-group[_ngcontent-%COMP%]{display:flex}.app-page[_ngcontent-%COMP%]   .filter-row[_ngcontent-%COMP%]{margin-bottom:18px;margin-top:9px;padding-left:24px}.app-page[_ngcontent-%COMP%]   .error-log-class[_ngcontent-%COMP%]{display:block;overflow-y:scroll;margin:0 2px 2px 0}.app-page[_ngcontent-%COMP%]   .error-log-class[_ngcontent-%COMP%]     .options{margin-top:20px;background-color:hsla(0,0%,74.9%,.15)}.app-page[_ngcontent-%COMP%]   .error-log-class[_ngcontent-%COMP%]     .caption{font-size:18px;font-weight:500}.app-page[_ngcontent-%COMP%]   .error-log-class[_ngcontent-%COMP%]     .option{margin-top:10px}.app-page[_ngcontent-%COMP%]   .error-log-class[_ngcontent-%COMP%]     .dx-datagrid-headers .dx-datagrid-table .dx-row>td, .app-page[_ngcontent-%COMP%]   .error-log-class[_ngcontent-%COMP%]     .dx-datagrid .dx-row-lines>td{text-align:center!important}  .date-group .mat-form-field .mat-form-field-infix{display:flex!important;align-items:center!important;padding:0!important;height:42.6px!important;margin:1px!important}"]}),e})();const pe=[{path:"",component:_,redirectTo:"integration",pathMatch:"full"},{path:"integration",component:_},{path:"integration/authentication",component:T},{path:"integration/landing-page",component:N,children:[{path:"",redirectTo:"1",pathMatch:"full"},{path:"1",component:de},{path:"2",component:de},{path:"3",component:de}]}];let ge=(()=>{class e{}return e.\u0275mod=l["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=l["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[a.k.forChild(pe)],a.k]}),e})();var me=n("Wp6s"),he=n("jaxi"),ue=n("XhcP"),fe=n("wZkO"),xe=n("STbY"),_e=n("ihCf"),Ce=n("Xi0T"),be=n("79uE"),ve=n("gMzk"),ye=n("Gkpw");let Pe=(()=>{class e{}return e.\u0275mod=l["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=l["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,ge,me.d,b.b,m.b,he.c,y.b,H.b,E.c,P.c,ue.g,fe.g,xe.e,V.d,O.e,G.h,C.p,C.E,_e.c,g.b,Ce.a,be.TimesheetV2ConfigurationModule,q.b,ve.a,ye.a]]}),e})()}}]);