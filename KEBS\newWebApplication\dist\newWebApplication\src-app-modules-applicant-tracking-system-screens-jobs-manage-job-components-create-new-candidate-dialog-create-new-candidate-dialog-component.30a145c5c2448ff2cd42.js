(window.webpackJsonp=window.webpackJsonp||[]).push([[911],{"HNK/":function(e,t,n){"use strict";n.r(t),n.d(t,"CreateNewCandidateDialogComponent",(function(){return p}));var o=n("0IaG"),a=n("yuIm"),i=n("fXoL"),r=n("XNFG"),s=n("tyNb"),c=n("NFeN"),d=n("ofXK");const l=function(e,t){return{"background-color":e,"border-color":t}};function u(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementContainerStart"](0),i["\u0275\u0275elementStart"](1,"div",6),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const n=t.index;return i["\u0275\u0275nextContext"]().openCreationMethod(n)})),i["\u0275\u0275element"](2,"img",7),i["\u0275\u0275elementStart"](3,"div",8),i["\u0275\u0275text"](4),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=t.$implicit;i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngStyle",i["\u0275\u0275pureFunction2"](3,l,e.backgroundColor,e.color)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("src",e.imgUrl,i["\u0275\u0275sanitizeUrl"]),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate"](e.label)}}let p=(()=>{class e{constructor(e,t,n,o){this.data=e,this._dialogRef=t,this._toaster=n,this._router=o,this.newCandidates=[{id:1,label:"New Candidate",color:"#52C41A",backgroundColor:"#EEF9E8",navigation:"newCandidate",imgUrl:"https://assets.kebs.app/candidate-new-candidate.png"},{id:2,label:"Bulk Candidate Upload",color:"#1890FF",backgroundColor:"#E8F4FF",navigation:"bulkCandidateUpload",imgUrl:"https://assets.kebs.app/candidate-candidate-upload.png"},{id:3,label:"Bulk Resume Upload",color:"#FA8C16",backgroundColor:"#FFF3E8",navigation:"bulkResumeUpload",imgUrl:"https://assets.kebs.app/candidate-candidate-resume-upload.png"}],this.currentRoute=""}ngOnInit(){this.currentRoute=this._router.url}onClose(){this._dialogRef.close(!1)}openCreationMethod(e){var t;if("bulkCandidateUpload"==this.newCandidates[e].navigation||"bulkResumeUpload"==this.newCandidates[e].navigation)if(null===(t=this.data)||void 0===t?void 0:t.isFromAllCandidates){if(this.data.isFromTalentPipeline){let e="/main/ats/candidates/campus-job-talent-pipeline-list"==this.currentRoute;if("/main/ats/candidates/talent-pipeline-list"==this.currentRoute){if(!a.checkAccessForGeneralRole(a.moduleId.candidates,a.subModuleId.talentPipeline,0,0,"U"))return this._toaster.showInfo("Access Restricted!","Oops! You're not authorized to view this content. Contact your administrator for access.",7e3)}else if(e&&!a.checkAccessForGeneralRole(a.moduleId.campusJobs,a.subModuleId.campusTalentPipeline,0,0,"U"))return this._toaster.showInfo("Access Restricted!","Oops! You're not authorized to view this content. Contact your administrator for access.",7e3)}else if("/main/ats/candidates/candidate-list"==this.currentRoute&&!a.checkAccessForGeneralRole(a.moduleId.candidates,a.subModuleId.manageCandidate,0,0,"U"))return this._toaster.showInfo("Access Restricted!","Oops! You're not authorized to view this content. Contact your administrator for access.",7e3)}else if(this.currentRoute=="/main/ats/jobs/campus-job-list/"+this.data.jobId?!a.checkAccessForJobRole(a.moduleId.campusJobs,a.subModuleId.manageCampusJob,a.sectionId.manageCampusJobDetailViewCandidate,0,"U"):!a.checkAccessForJobRole(a.moduleId.jobs,a.subModuleId.manageJob,a.sectionId.manageJobDetailViewCandidate,0,"U"))return this._toaster.showInfo("Access Restricted!","Oops! You're not authorized to view this content. Contact your administrator for access.",7e3);this._dialogRef.close(this.newCandidates[e].navigation)}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](o.a),i["\u0275\u0275directiveInject"](o.h),i["\u0275\u0275directiveInject"](r.a),i["\u0275\u0275directiveInject"](s.g))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-create-new-candidate-dialog"]],decls:9,vars:1,consts:[[1,"bg-container"],[1,"d-flex","align-items-center","justify-content-between","header"],[1,"title","p-0"],[1,"close",3,"click"],[1,"d-flex","align-items-center","justify-content-center","flex-wrap","main"],[4,"ngFor","ngForOf"],[1,"create-tile",3,"ngStyle","click"],["width","32","height","32",2,"margin-bottom","20px",3,"src"],[1,"label-text"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275elementStart"](1,"div",1),i["\u0275\u0275elementStart"](2,"div",2),i["\u0275\u0275text"](3,"Create New Candidate"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"div"),i["\u0275\u0275elementStart"](5,"mat-icon",3),i["\u0275\u0275listener"]("click",(function(){return t.onClose()})),i["\u0275\u0275text"](6,"close"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](7,"div",4),i["\u0275\u0275template"](8,u,5,6,"ng-container",5),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](8),i["\u0275\u0275property"]("ngForOf",t.newCandidates))},directives:[c.a,d.NgForOf,d.NgStyle],styles:[".bg-container[_ngcontent-%COMP%]{padding:24px;overflow:hidden}.bg-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{margin-bottom:20px}.bg-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:18px;font-weight:700;color:#111434}.bg-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .close[_ngcontent-%COMP%]{font-size:18px;width:18px;height:18px;color:#1c1b1f;cursor:pointer}.bg-container[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]{gap:30px}.bg-container[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]   .create-tile[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;flex-direction:column;border-radius:8px;border-width:1px;border-style:solid;cursor:pointer;height:120px;width:200px}.bg-container[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]   .create-tile[_ngcontent-%COMP%]   .label-text[_ngcontent-%COMP%]{color:#111434;font-family:var(--atsfontFamily);font-size:11px;font-weight:600}"]}),e})()}}]);