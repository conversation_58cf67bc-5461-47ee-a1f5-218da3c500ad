(window.webpackJsonp=window.webpackJsonp||[]).push([[922],{Vth9:function(e,t,n){"use strict";n.r(t),n.d(t,"CreateEditGroupComponent",(function(){return ne}));var o=n("mrSG"),i=n("XNiG"),a=n("1G5W"),r=n("3Pt+"),s=n("0IaG"),l=n("fXoL"),d=n("XNFG"),c=n("rQiX"),p=n("RThm"),g=n("ofXK"),m=n("NFeN"),u=n("kmnG"),f=n("qFsG"),C=n("UVjm"),h=n("su5B"),v=n("Qu3c"),x=n("me71"),M=n("pPzn");function _(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div",26),l["\u0275\u0275text"](1),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](1),l["\u0275\u0275textInterpolate1"](" ","C"==e.data.mode?"Group Creation":"Edit Group"," ")}}function O(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",26),l["\u0275\u0275elementStart"](1,"mat-icon",27),l["\u0275\u0275listener"]("click",(function(){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"]().closeCandidateView()})),l["\u0275\u0275text"](2," keyboard_arrow_left "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275text"](3),l["\u0275\u0275elementEnd"]()}if(2&e){const e=l["\u0275\u0275nextContext"]();let t=null;l["\u0275\u0275advance"](3),l["\u0275\u0275textInterpolate2"](" ",null==e.groupForms||null==(t=e.groupForms.get("associatedCandidates"))||null==t.value?null:t.value.length," ",(null==e.groupForms||null==(t=e.groupForms.get("associatedCandidates"))||null==t.value?null:t.value.length)>1?"Candidates":"Candidate"," ")}}const w=function(e){return{"pointer-events":e}};function F(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",28),l["\u0275\u0275elementStart"](1,"mat-icon",29),l["\u0275\u0275listener"]("click",(function(){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"]().onClose()})),l["\u0275\u0275text"](2,"close"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&e){const e=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngStyle",l["\u0275\u0275pureFunction1"](1,w,e.isLoading?"none":""))}}function y(e,t){}function E(e,t){if(1&e&&l["\u0275\u0275template"](0,y,0,0,"ng-template",37),2&e){l["\u0275\u0275nextContext"](4);const e=l["\u0275\u0275reference"](27);l["\u0275\u0275property"]("ngTemplateOutlet",e)}}function P(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div",33),l["\u0275\u0275elementStart"](1,"div",34),l["\u0275\u0275text"](2),l["\u0275\u0275pipe"](3,"columnCustomization"),l["\u0275\u0275template"](4,E,1,1,void 0,31),l["\u0275\u0275pipe"](5,"columnCustomization"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](6,"mat-form-field",35),l["\u0275\u0275element"](7,"input",36),l["\u0275\u0275pipe"](8,"columnCustomization"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"](3);l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate1"](" ",l["\u0275\u0275pipeBind3"](3,3,e.groupFormFields,"groupName","label")," "),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf",l["\u0275\u0275pipeBind3"](5,7,e.groupFormFields,"groupName","isMandatory")),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("placeholder",l["\u0275\u0275pipeBind3"](8,11,e.groupFormFields,"groupName","placeholder"))}}function b(e,t){}function L(e,t){if(1&e&&l["\u0275\u0275template"](0,b,0,0,"ng-template",37),2&e){l["\u0275\u0275nextContext"](4);const e=l["\u0275\u0275reference"](27);l["\u0275\u0275property"]("ngTemplateOutlet",e)}}function D(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div",33),l["\u0275\u0275elementStart"](1,"div",34),l["\u0275\u0275text"](2),l["\u0275\u0275pipe"](3,"columnCustomization"),l["\u0275\u0275template"](4,L,1,1,void 0,31),l["\u0275\u0275pipe"](5,"columnCustomization"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](6,"mat-form-field",35),l["\u0275\u0275element"](7,"input",38),l["\u0275\u0275pipe"](8,"columnCustomization"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"](3);l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate1"](" ",l["\u0275\u0275pipeBind3"](3,3,e.groupFormFields,"groupDescription","label")," "),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf",l["\u0275\u0275pipeBind3"](5,7,e.groupFormFields,"groupDescription","isMandatory")),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("placeholder",l["\u0275\u0275pipeBind3"](8,11,e.groupFormFields,"groupDescription","placeholder"))}}function S(e,t){}function V(e,t){if(1&e&&l["\u0275\u0275template"](0,S,0,0,"ng-template",37),2&e){l["\u0275\u0275nextContext"](4);const e=l["\u0275\u0275reference"](27);l["\u0275\u0275property"]("ngTemplateOutlet",e)}}const I=function(){return{field:"groupSpoc"}};function k(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",33),l["\u0275\u0275elementStart"](1,"div",34),l["\u0275\u0275text"](2),l["\u0275\u0275pipe"](3,"columnCustomization"),l["\u0275\u0275template"](4,V,1,1,void 0,31),l["\u0275\u0275pipe"](5,"columnCustomization"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](6,"app-single-select-chip",39),l["\u0275\u0275listener"]("onValueChange",(function(t){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"](3).onCustomSelectValueChange(t)})),l["\u0275\u0275pipe"](7,"columnCustomization"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&e){const e=l["\u0275\u0275nextContext"](3);l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate1"](" ",l["\u0275\u0275pipeBind3"](3,7,e.groupFormFields,"groupSpoc","label")," "),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf",l["\u0275\u0275pipeBind3"](5,11,e.groupFormFields,"groupSpoc","isMandatory")),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("masterData",e.employeeMasterData)("placeholder",l["\u0275\u0275pipeBind3"](7,15,e.groupFormFields,"groupSpoc","placeholder"))("selectedValue",e.groupForms.get("groupSpoc").value)("displayClose",!1)("data",l["\u0275\u0275pureFunction0"](19,I))}}function A(e,t){}function z(e,t){if(1&e&&l["\u0275\u0275template"](0,A,0,0,"ng-template",37),2&e){l["\u0275\u0275nextContext"](4);const e=l["\u0275\u0275reference"](27);l["\u0275\u0275property"]("ngTemplateOutlet",e)}}const T=function(){return{field:"groupTags"}};function G(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",33),l["\u0275\u0275elementStart"](1,"div",34),l["\u0275\u0275text"](2),l["\u0275\u0275pipe"](3,"columnCustomization"),l["\u0275\u0275template"](4,z,1,1,void 0,31),l["\u0275\u0275pipe"](5,"columnCustomization"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](6,"app-multi-select-chip",40),l["\u0275\u0275listener"]("onValueChange",(function(t){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"](3).onCustomSelectValueChange(t)})),l["\u0275\u0275pipe"](7,"columnCustomization"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&e){const e=l["\u0275\u0275nextContext"](3);l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate1"](" ",l["\u0275\u0275pipeBind3"](3,8,e.groupFormFields,"groupTags","label")," "),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf",l["\u0275\u0275pipeBind3"](5,12,e.groupFormFields,"groupTags","isMandatory")),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("type",2)("placeholder",l["\u0275\u0275pipeBind3"](7,16,e.groupFormFields,"groupTags","placeholder"))("masterData",e.tagsMasterData)("selectedValues",e.groupForms.get("groupTags").value)("displayClose",!1)("data",l["\u0275\u0275pureFunction0"](20,T))}}function j(e,t){}function B(e,t){if(1&e&&l["\u0275\u0275template"](0,j,0,0,"ng-template",37),2&e){l["\u0275\u0275nextContext"](4);const e=l["\u0275\u0275reference"](27);l["\u0275\u0275property"]("ngTemplateOutlet",e)}}const N=function(){return{field:"associatedJobPosition"}};function Z(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"app-single-select-chip",39),l["\u0275\u0275listener"]("onValueChange",(function(t){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"](4).onCustomSelectValueChange(t)})),l["\u0275\u0275pipe"](1,"columnCustomization"),l["\u0275\u0275elementEnd"]()}if(2&e){const e=l["\u0275\u0275nextContext"](4);l["\u0275\u0275property"]("masterData",e.designationMasterData)("placeholder",l["\u0275\u0275pipeBind3"](1,5,e.groupFormFields,"associatedJobPosition","placeholder"))("selectedValue",e.groupForms.get("associatedJobPosition").value)("displayClose",!1)("data",l["\u0275\u0275pureFunction0"](9,N))}}function R(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"mat-form-field",35),l["\u0275\u0275element"](1,"input",43),l["\u0275\u0275pipe"](2,"columnCustomization"),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"](4);l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("placeholder",l["\u0275\u0275pipeBind3"](2,1,e.groupFormFields,"associatedJobPosition","placeholder"))}}function J(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div",33),l["\u0275\u0275elementStart"](1,"div",34),l["\u0275\u0275text"](2),l["\u0275\u0275pipe"](3,"columnCustomization"),l["\u0275\u0275template"](4,B,1,1,void 0,31),l["\u0275\u0275pipe"](5,"columnCustomization"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](6,Z,2,10,"app-single-select-chip",41),l["\u0275\u0275pipe"](7,"columnCustomization"),l["\u0275\u0275template"](8,R,3,5,"mat-form-field",42),l["\u0275\u0275pipe"](9,"columnCustomization"),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"](3);l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate1"](" ",l["\u0275\u0275pipeBind3"](3,4,e.groupFormFields,"associatedJobPosition","label")," "),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf",l["\u0275\u0275pipeBind3"](5,8,e.groupFormFields,"associatedJobPosition","isMandatory")),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf","single-select"==l["\u0275\u0275pipeBind3"](7,12,e.groupFormFields,"associatedJobPosition","fieldType")),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf","text"==l["\u0275\u0275pipeBind3"](9,16,e.groupFormFields,"associatedJobPosition","fieldType"))}}function H(e,t){}function X(e,t){if(1&e&&l["\u0275\u0275template"](0,H,0,0,"ng-template",37),2&e){l["\u0275\u0275nextContext"](4);const e=l["\u0275\u0275reference"](27);l["\u0275\u0275property"]("ngTemplateOutlet",e)}}const U=function(){return{field:"associatedCandidates"}};function W(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",33),l["\u0275\u0275elementStart"](1,"div",34),l["\u0275\u0275text"](2),l["\u0275\u0275pipe"](3,"columnCustomization"),l["\u0275\u0275template"](4,X,1,1,void 0,31),l["\u0275\u0275pipe"](5,"columnCustomization"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](6,"app-multi-select-chip",44),l["\u0275\u0275listener"]("onValueChange",(function(t){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"](3).onCustomSelectValueChange(t)}))("openDetailedView",(function(){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"](3).openCandidateView()})),l["\u0275\u0275pipe"](7,"columnCustomization"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&e){const e=l["\u0275\u0275nextContext"](3);l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate1"](" ",l["\u0275\u0275pipeBind3"](3,8,e.groupFormFields,"associatedCandidates","label")," "),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf",l["\u0275\u0275pipeBind3"](5,12,e.groupFormFields,"associatedCandidates","isMandatory")),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("type",2)("placeholder",l["\u0275\u0275pipeBind3"](7,16,e.groupFormFields,"associatedCandidates","placeholder"))("masterData",e.candidatesMasterData)("selectedValues",e.groupForms.get("associatedCandidates").value)("displayClose",!1)("data",l["\u0275\u0275pureFunction0"](20,U))}}function q(e,t){if(1&e&&(l["\u0275\u0275elementContainerStart"](0),l["\u0275\u0275template"](1,P,9,15,"div",32),l["\u0275\u0275pipe"](2,"columnCustomization"),l["\u0275\u0275template"](3,D,9,15,"div",32),l["\u0275\u0275pipe"](4,"columnCustomization"),l["\u0275\u0275template"](5,k,8,20,"div",32),l["\u0275\u0275pipe"](6,"columnCustomization"),l["\u0275\u0275template"](7,G,8,21,"div",32),l["\u0275\u0275pipe"](8,"columnCustomization"),l["\u0275\u0275template"](9,J,10,20,"div",32),l["\u0275\u0275pipe"](10,"columnCustomization"),l["\u0275\u0275template"](11,W,8,21,"div",32),l["\u0275\u0275pipe"](12,"columnCustomization"),l["\u0275\u0275elementContainerEnd"]()),2&e){const e=l["\u0275\u0275nextContext"](2);l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",l["\u0275\u0275pipeBind3"](2,6,e.groupFormFields,"groupName","isActive")),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf",l["\u0275\u0275pipeBind3"](4,10,e.groupFormFields,"groupDescription","isActive")),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf",l["\u0275\u0275pipeBind3"](6,14,e.groupFormFields,"groupSpoc","isActive")),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf",l["\u0275\u0275pipeBind3"](8,18,e.groupFormFields,"groupTags","isActive")),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf",l["\u0275\u0275pipeBind3"](10,22,e.groupFormFields,"associatedJobPosition","isActive")),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf",l["\u0275\u0275pipeBind3"](12,26,e.groupFormFields,"associatedCandidates","isActive"))}}function K(e,t){if(1&e&&(l["\u0275\u0275elementContainerStart"](0),l["\u0275\u0275elementStart"](1,"div",45),l["\u0275\u0275elementStart"](2,"div",46),l["\u0275\u0275element"](3,"img",47),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](4,"div",48),l["\u0275\u0275elementStart"](5,"div",49),l["\u0275\u0275text"](6,"Loading..."),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementContainerEnd"]()),2&e){const e=l["\u0275\u0275nextContext"](2);l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("src",e.uiTextConfig["UI-LOADING-001"],l["\u0275\u0275sanitizeUrl"])}}function Q(e,t){if(1&e&&(l["\u0275\u0275namespaceSVG"](),l["\u0275\u0275namespaceHTML"](),l["\u0275\u0275elementStart"](0,"div",30),l["\u0275\u0275template"](1,q,13,30,"ng-container",31),l["\u0275\u0275template"](2,K,7,1,"ng-container",31),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"]();l["\u0275\u0275property"]("formGroup",e.groupForms),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",!e.isLoading),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",e.isLoading)}}function $(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div",57),l["\u0275\u0275elementStart"](1,"div",58),l["\u0275\u0275elementStart"](2,"span",59),l["\u0275\u0275text"](3),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](4,"div",60),l["\u0275\u0275element"](5,"app-user-image",61),l["\u0275\u0275elementStart"](6,"span",59),l["\u0275\u0275text"](7),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("matTooltip",e.id),l["\u0275\u0275advance"](1),l["\u0275\u0275textInterpolate1"](" #",e.id," "),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("oid",e.oid),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("matTooltip",e.name),l["\u0275\u0275advance"](1),l["\u0275\u0275textInterpolate1"](" ",e.name," ")}}function Y(e,t){if(1&e&&(l["\u0275\u0275namespaceSVG"](),l["\u0275\u0275namespaceHTML"](),l["\u0275\u0275elementStart"](0,"div",50),l["\u0275\u0275elementStart"](1,"div",51),l["\u0275\u0275elementStart"](2,"div",52),l["\u0275\u0275elementStart"](3,"span",53),l["\u0275\u0275text"](4," CANDIDATE ID "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](5,"div",54),l["\u0275\u0275elementStart"](6,"span",53),l["\u0275\u0275text"](7," CANDIDATE NAME "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](8,"div",55),l["\u0275\u0275template"](9,$,8,5,"div",56),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("matTooltip","CANDIDATE ID"),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("matTooltip","CANDIDATE NAME"),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("ngForOf",e.currentSelectedCandidatesList)}}function ee(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275namespaceSVG"](),l["\u0275\u0275namespaceHTML"](),l["\u0275\u0275elementStart"](0,"div",62),l["\u0275\u0275elementStart"](1,"div",63),l["\u0275\u0275listener"]("click",(function(){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"]().onClose()})),l["\u0275\u0275text"](2," Cancel "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](3,"div",64),l["\u0275\u0275listener"]("click",(function(){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"]().onCreateOrEditGroup()})),l["\u0275\u0275text"](4),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&e){const e=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngStyle",l["\u0275\u0275pureFunction1"](3,w,e.isLoading?"none":"")),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngStyle",l["\u0275\u0275pureFunction1"](5,w,e.isLoading?"none":"")),l["\u0275\u0275advance"](1),l["\u0275\u0275textInterpolate1"](" ","C"==e.data.mode?"Create":"Update"," ")}}function te(e,t){1&e&&(l["\u0275\u0275namespaceSVG"](),l["\u0275\u0275namespaceHTML"](),l["\u0275\u0275elementStart"](0,"span",65),l["\u0275\u0275text"](1,"*"),l["\u0275\u0275elementEnd"]())}let ne=(()=>{class e{constructor(e,t,n,o,a,r){this.data=e,this._dialogRef=t,this._toaster=n,this._atsMasterService=o,this._onboardingService=a,this._fb=r,this._onDestroy=new i.b,this.groupFormFields=[],this.groupEditDetails={},this.currentSelectedCandidatesList=[],this.isLoading=!0,this.isCandidateView=!1,this.employeeMasterData=[],this.tagsMasterData=[],this.designationMasterData=[],this.candidatesMasterData=[],this.groupForms=this._fb.group({})}ngOnInit(){return Object(o.c)(this,void 0,void 0,(function*(){yield Promise.all([this.getAtsFormsConfig("onboardingGroupCreationForm"),this.getDesignation(),this.getAssociateDetailsInSystem(),this.getOnboardingGroupTagsMaster(),this.getAllOnboardingCandidates()]).then(e=>Object(o.c)(this,void 0,void 0,(function*(){"E"==this.data.mode&&(yield this.getGroupDetailsForEdit(this.data.groupId)),yield this.createGroupForm(),this.isLoading=!1}))).catch(e=>{this.isLoading=!1,this.onClose()})}))}onClose(){this._dialogRef.close(!1)}openCandidateView(){let e=this.groupForms.get("associatedCandidates").value||[];this.currentSelectedCandidatesList=this.candidatesMasterData.filter(t=>e.includes(t.id)),this.isCandidateView=!0}closeCandidateView(){this.isCandidateView=!1,this.currentSelectedCandidatesList=[]}createGroupForm(){return Object(o.c)(this,void 0,void 0,(function*(){for(let e=0;e<this.groupFormFields.length;e++){const t=this.groupFormFields[e].key;this.groupForms.addControl(t,this._fb.control("E"==this.data.mode?this.groupEditDetails[t]:null,[this.groupFormFields[e].isMandatory?r.H.required:null].filter(e=>null!=e)))}}))}onCustomSelectValueChange(e){this.groupForms.get(e.data.field).setValue(e.val)}onCreateOrEditGroup(){return Object(o.c)(this,void 0,void 0,(function*(){if(this.groupForms.invalid)return this._toaster.showWarning("Warning \u26a0\ufe0f","Kindly Fill All Mandatory Fields",7e3);let e=this.groupForms.getRawValue();e.groupId=this.data.groupId,console.log(e),this.isLoading=!0,"C"==this.data.mode?yield this.createOnboardingCandidateGroup(e):"E"==this.data.mode&&(yield this.updateOnboardingCandidateGroup(e)),this.isLoading=!1}))}getAtsFormsConfig(e){return Object(o.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._atsMasterService.getAtsFormsConfig(e).pipe(Object(a.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?(this.groupFormFields=e.data.form[0].formFields,t(!0)):(this._toaster.showError("Error",e.msg,7e3),t(!1))},error:e=>{this._toaster.showError("Error",e.message?e.message:"Forms Configuration Retrieval Failed!",7e3),n()}}))}))}getDesignation(){return Object(o.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getDesignation().pipe(Object(a.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.designationMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Master Data Retrieval Failed!",7e3),t()}}))}))}getAssociateDetailsInSystem(){return Object(o.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getAssociateDetailsInSystem().pipe(Object(a.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.employeeMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Team Master Data Retrieval Failed!",7e3),t()}}))}))}getOnboardingGroupTagsMaster(){return Object(o.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._onboardingService.getOnboardingGroupTagsMaster({}).pipe(Object(a.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.tagsMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Tags Master Data Retrieval Failed!",7e3),t()}}))}))}getAllOnboardingCandidates(){return Object(o.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._onboardingService.getAllOnboardingCandidates({}).pipe(Object(a.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.candidatesMasterData=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Master Data Retrieval Failed!",7e3),t()}}))}))}getGroupDetailsForEdit(e){return Object(o.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._onboardingService.getGroupDetailsForEdit({groupId:e}).pipe(Object(a.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?this.groupEditDetails=e.data:this._toaster.showError("Error",e.msg,7e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Master Data Retrieval Failed!",7e3),n()}}))}))}createOnboardingCandidateGroup(e){return Object(o.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._onboardingService.createOnboardingCandidateGroup(e).pipe(Object(a.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?(this._toaster.showSuccess("Success \u2705",e.msg,7e3),this._dialogRef.close(!0)):this._toaster.showError("Error",e.msg,7e3),t(!0)},error:e=>{this.isLoading=!1,this._toaster.showError("Error",e.message?e.message:"Failed To Create Group!",7e3),n()}}))}))}updateOnboardingCandidateGroup(e){return Object(o.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._onboardingService.updateOnboardingCandidateGroup(e).pipe(Object(a.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?(this._toaster.showSuccess("Success \u2705",e.msg,7e3),this._dialogRef.close(!0)):this._toaster.showError("Error",e.msg,7e3),t(!0)},error:e=>{this.isLoading=!1,this._toaster.showError("Error",e.message?e.message:"Failed To Update Group!",7e3),n()}}))}))}}return e.\u0275fac=function(t){return new(t||e)(l["\u0275\u0275directiveInject"](s.a),l["\u0275\u0275directiveInject"](s.h),l["\u0275\u0275directiveInject"](d.a),l["\u0275\u0275directiveInject"](c.a),l["\u0275\u0275directiveInject"](p.a),l["\u0275\u0275directiveInject"](r.i))},e.\u0275cmp=l["\u0275\u0275defineComponent"]({type:e,selectors:[["app-create-edit-group"]],decls:28,vars:6,consts:[[1,"dialog"],[1,"header"],["class","header-1",4,"ngIf"],["class","header-2",4,"ngIf"],[1,"img"],["width","261","height","91","viewBox","0 0 261 91","fill","none",2,"max-height","56px"],["d","M133.604 -34.5168V-7.23531C133.604 -3.64603 132.088 -0.188957 129.391 2.40674C125.911 5.73015 120.858 7.19028 115.948 6.27824L115.947 6.27809L93.9786 2.28769L103.741 -6.82419L116.468 -4.48726L116.469 -4.48722C119.365 -3.95876 122.089 -6.02349 122.089 -8.80602V-23.8208L133.604 -34.5168Z","fill","#EAEDF3","fill-opacity","0.5","stroke","#DADCE2","stroke-width","0.421296"],["d","M179.021 2.28793L157.081 6.2781L157.08 6.27825C152.177 7.19031 147.129 5.73004 143.653 2.40629L143.651 2.4043C140.911 -0.144047 139.397 -3.59969 139.397 -7.18911V-34.5164L150.898 -23.8208V-9.03701C150.898 -6.11666 153.766 -3.95807 156.76 -4.53362C156.761 -4.53368 156.761 -4.53374 156.761 -4.53379L169.223 -6.82422L179.021 2.28793Z","fill","#F7F8FA","stroke","#DADCE2","stroke-width","0.421296"],["d","M133.604 25.6434V52.9408L122.089 42.2565V26.8894C122.089 24.2934 119.563 22.3712 116.818 22.8517L116.817 22.852L103.741 25.2326L93.979 16.1311L115.947 12.1452L115.948 12.145C120.858 11.234 125.911 12.6926 129.392 16.0125C132.088 18.6053 133.604 22.0584 133.604 25.6434Z","fill","#F7F8FA","stroke","#DADCE2","stroke-width","0.421296"],["d","M139.397 52.9404V25.6589C139.397 22.0697 140.913 18.6127 143.609 16.0171C147.09 12.6935 152.143 11.2333 157.053 12.1453L157.054 12.1455L179.021 16.1357L169.26 25.2017L155.936 22.7723C155.936 22.7723 155.936 22.7723 155.936 22.7723C153.337 22.2898 150.912 24.1239 150.912 26.629V42.2443L139.397 52.9404Z","fill","#EAEDF3","fill-opacity","0.5","stroke","#DADCE2","stroke-width","0.421296"],["d","M40.0758 2.78785V30.0694C40.0758 33.6587 38.5596 37.1157 35.8631 39.7114C32.3828 43.0348 27.3294 44.495 22.4198 43.5829L22.419 43.5828L0.45043 39.5924L10.2125 30.4805L22.9402 32.8174L22.9404 32.8175C25.8368 33.3459 28.5604 31.2812 28.5604 28.4987V13.4839L40.0758 2.78785Z","fill","#F3F5FA","stroke","#DADCE2","stroke-width","0.421296"],["d","M85.4931 39.5926L63.553 43.5828L63.5521 43.5829C58.6486 44.495 53.6013 43.0347 50.1253 39.711L50.1231 39.709C47.383 37.1606 45.8686 33.705 45.8686 30.1156V2.78825L57.3693 13.4838V28.2677C57.3693 31.188 60.2381 33.3466 63.2321 32.7711C63.2324 32.771 63.2327 32.771 63.233 32.7709L75.6951 30.4805L85.4931 39.5926Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M40.0758 62.9471V90.2445L28.5604 79.5602V64.1931C28.5604 61.5971 26.0352 59.6749 23.29 60.1554L23.2886 60.1557L10.2125 62.5364L0.450787 53.4348L22.419 49.4489L22.4198 49.4487C27.3296 48.5377 32.3831 49.9963 35.8634 53.3162C38.5597 55.909 40.0758 59.3621 40.0758 62.9471Z","fill","#F7F8FA","stroke","#DADCE2","stroke-width","0.421296"],["d","M45.8686 90.2441V62.9626C45.8686 59.3734 47.3848 55.9164 50.0811 53.3208C53.5614 49.9972 58.6149 48.537 63.5246 49.449L63.5254 49.4492L85.4927 53.4394L75.7322 62.5054L62.4079 60.076C62.4077 60.076 62.4076 60.076 62.4075 60.076C59.809 59.5935 57.384 61.4276 57.384 63.9327V79.548L45.8686 90.2441Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M40.0758 2.48316V29.7647C40.0758 33.354 38.5596 36.811 35.8631 39.4067C32.3828 42.7302 27.3294 44.1903 22.4198 43.2782L22.419 43.2781L0.45043 39.2877L10.2125 30.1758L22.9402 32.5127L22.9404 32.5128C25.8368 33.0412 28.5604 30.9765 28.5604 28.194V13.1792L40.0758 2.48316Z","fill","#EAEDF3","fill-opacity","0.5","stroke","#DADCE2","stroke-width","0.421296"],["d","M85.4931 39.2879L63.553 43.2781L63.5521 43.2782C58.6486 44.1903 53.6013 42.73 50.1253 39.4063L50.1231 39.4043C47.383 36.856 45.8686 33.4003 45.8686 29.8109V2.48356L57.3693 13.1792V27.963C57.3693 30.8833 60.2381 33.0419 63.2321 32.4664C63.2324 32.4663 63.2327 32.4663 63.233 32.4662L75.6951 30.1758L85.4931 39.2879Z","fill","#F7F8FA","stroke","#DADCE2","stroke-width","0.421296"],["d","M40.0758 62.6424V89.9398L28.5604 79.2555V63.8884C28.5604 61.2924 26.0352 59.3702 23.29 59.8507L23.2886 59.851L10.2125 62.2317L0.450787 53.1301L22.419 49.1442L22.4198 49.1441C27.3296 48.233 32.3831 49.6916 35.8634 53.0115C38.5597 55.6043 40.0758 59.0574 40.0758 62.6424Z","fill","#F7F8FA","stroke","#DADCE2","stroke-width","0.421296"],["d","M45.8686 89.9394V62.6579C45.8686 59.0687 47.3848 55.6118 50.0811 53.0161C53.5614 49.6925 58.6149 48.2323 63.5246 49.1444L63.5254 49.1445L85.4927 53.1347L75.7322 62.2007L62.4079 59.7713C62.4077 59.7713 62.4076 59.7713 62.4075 59.7713C59.809 59.2888 57.384 61.1229 57.384 63.628V79.2434L45.8686 89.9394Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M227.131 2.78785V30.0694C227.131 33.6587 225.615 37.1157 222.919 39.7114C219.438 43.0348 214.385 44.495 209.475 43.5829L209.475 43.5828L187.506 39.5924L197.268 30.4805L209.996 32.8174L209.996 32.8175C212.892 33.3459 215.616 31.2812 215.616 28.4987V13.4839L227.131 2.78785Z","fill","#EAEDF3","fill-opacity","0.5","stroke","#DADCE2","stroke-width","0.421296"],["d","M272.549 39.5926L250.608 43.5828L250.608 43.5829C245.704 44.495 240.657 43.0347 237.181 39.711L237.179 39.709C234.438 37.1606 232.924 33.705 232.924 30.1156V2.78825L244.425 13.4838V28.2677C244.425 31.188 247.294 33.3466 250.288 32.7711C250.288 32.771 250.288 32.771 250.288 32.7709L262.75 30.4805L272.549 39.5926Z","fill","#F7F8FA","stroke","#DADCE2","stroke-width","0.421296"],["d","M227.131 62.9471V90.2445L215.616 79.5602V64.1931C215.616 61.5971 213.091 59.6749 210.345 60.1554L210.344 60.1557L197.268 62.5364L187.506 53.4348L209.474 49.4489L209.475 49.4487C214.385 48.5377 219.439 49.9963 222.919 53.3162C225.615 55.909 227.131 59.3621 227.131 62.9471Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M232.924 90.2441V62.9626C232.924 59.3734 234.44 55.9164 237.136 53.3208C240.617 49.9972 245.67 48.537 250.58 49.449L250.581 49.4492L272.548 53.4394L262.788 62.5054L249.463 60.076C249.463 60.076 249.463 60.076 249.463 60.076C246.864 59.5935 244.439 61.4276 244.439 63.9327V79.548L232.924 90.2441Z","fill","#EAEDF3","fill-opacity","0.5","stroke","#DADCE2","stroke-width","0.421296"],["class","form-content",3,"formGroup",4,"ngIf"],["class","list-view",4,"ngIf"],["class","buttons",4,"ngIf"],["mandatoryTemplate",""],[1,"header-1"],[1,"back-icon",3,"click"],[1,"header-2"],[1,"close-icon",3,"ngStyle","click"],[1,"form-content",3,"formGroup"],[4,"ngIf"],["class","single-field",4,"ngIf"],[1,"single-field"],[1,"form-label"],["appearance","outline",1,"form-field-class"],["type","text","maxlength","50","matInput","","formControlName","groupName",3,"placeholder"],[3,"ngTemplateOutlet"],["type","text","maxlength","75","matInput","","formControlName","groupDescription",3,"placeholder"],[3,"masterData","placeholder","selectedValue","displayClose","data","onValueChange"],[3,"type","placeholder","masterData","selectedValues","displayClose","data","onValueChange"],[3,"masterData","placeholder","selectedValue","displayClose","data","onValueChange",4,"ngIf"],["class","form-field-class","appearance","outline",4,"ngIf"],["type","text","maxlength","200","matInput","","formControlName","associatedJobPosition",3,"placeholder"],[3,"type","placeholder","masterData","selectedValues","displayClose","data","onValueChange","openDetailedView"],[1,"loading-section-container"],[2,"max-width","60px","max-height","60px"],[2,"max-width","100%","max-height","100%",3,"src"],[1,"loading-wrapper"],[1,"loading"],[1,"list-view"],[1,"d-flex","align-items-center","header-row"],[1,"d-flex","align-items-center","pr-0","header-row","col-3"],[1,"list-title",3,"matTooltip"],[1,"d-flex","align-items-center","pr-0","header-row","col-9"],[1,"content"],["class","d-flex align-items-center content-row-main",4,"ngFor","ngForOf"],[1,"d-flex","align-items-center","content-row-main"],[1,"d-flex","align-items-center","pr-0","content-row","col-3"],[1,"sub-content",3,"matTooltip"],[1,"d-flex","align-items-center","pr-0","content-row","col-9"],["imgWidth","24px","imgHeight","24px",2,"padding-right","8px",3,"oid"],[1,"buttons"],[1,"cancel-btn",3,"ngStyle","click"],[1,"create-btn",3,"ngStyle","click"],[2,"color","#cf0001"]],template:function(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"div",0),l["\u0275\u0275elementStart"](1,"div",1),l["\u0275\u0275template"](2,_,2,1,"div",2),l["\u0275\u0275template"](3,O,4,2,"div",2),l["\u0275\u0275template"](4,F,3,3,"div",3),l["\u0275\u0275elementStart"](5,"div",4),l["\u0275\u0275namespaceSVG"](),l["\u0275\u0275elementStart"](6,"svg",5),l["\u0275\u0275element"](7,"path",6),l["\u0275\u0275element"](8,"path",7),l["\u0275\u0275element"](9,"path",8),l["\u0275\u0275element"](10,"path",9),l["\u0275\u0275element"](11,"path",10),l["\u0275\u0275element"](12,"path",11),l["\u0275\u0275element"](13,"path",12),l["\u0275\u0275element"](14,"path",13),l["\u0275\u0275element"](15,"path",14),l["\u0275\u0275element"](16,"path",15),l["\u0275\u0275element"](17,"path",16),l["\u0275\u0275element"](18,"path",17),l["\u0275\u0275element"](19,"path",18),l["\u0275\u0275element"](20,"path",19),l["\u0275\u0275element"](21,"path",20),l["\u0275\u0275element"](22,"path",21),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](23,Q,3,3,"div",22),l["\u0275\u0275template"](24,Y,10,3,"div",23),l["\u0275\u0275template"](25,ee,5,7,"div",24),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](26,te,2,0,"ng-template",null,25,l["\u0275\u0275templateRefExtractor"])),2&e&&(l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf",!t.isCandidateView),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",t.isCandidateView),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",!t.isCandidateView),l["\u0275\u0275advance"](19),l["\u0275\u0275property"]("ngIf",!t.isCandidateView),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",t.isCandidateView),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",!t.isCandidateView))},directives:[g.NgIf,m.a,g.NgStyle,r.w,r.n,u.c,f.b,r.e,r.q,r.v,r.l,g.NgTemplateOutlet,C.a,h.a,v.a,g.NgForOf,x.a],pipes:[M.a],styles:['.dialog[_ngcontent-%COMP%]{overflow:hidden}.dialog[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;height:56px;background-color:#f4f4f6;padding:0 16px;position:relative}.dialog[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .header-1[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-family:var(--atsfontFamily);font-size:20px;font-weight:700;color:#111434}.dialog[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .header-1[_ngcontent-%COMP%]   .back-icon[_ngcontent-%COMP%]{font-size:18px;width:18px;height:18px;color:#6e7b8f;border:1px solid #6e7b8f;border-radius:4px;cursor:pointer}.dialog[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .header-2[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%]{font-size:18px;width:18px;height:18px;color:#1c1b1f;cursor:pointer;position:relative;z-index:2}.dialog[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .img[_ngcontent-%COMP%]{position:absolute;right:-44px;height:56px}.dialog[_ngcontent-%COMP%]   .form-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:6px;height:calc(100vh - 104px);overflow-y:auto;padding:16px}.dialog[_ngcontent-%COMP%]   .form-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:2px;width:100%}.dialog[_ngcontent-%COMP%]   .form-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:12px;font-weight:400;color:#5f6c81}.dialog[_ngcontent-%COMP%]   .form-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]{font-size:11px;font-family:var(--atsfontFamily)}.dialog[_ngcontent-%COMP%]   .form-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-input-element{height:17px!important}.dialog[_ngcontent-%COMP%]   .form-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-input-element, .dialog[_ngcontent-%COMP%]   .form-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-input-element:disabled{color:#45546e!important}.dialog[_ngcontent-%COMP%]   .form-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-form-field{width:90%!important;font-size:12px!important;font-family:var(--atsfontFamily)!important}.dialog[_ngcontent-%COMP%]   .form-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-form-field-wrapper, .dialog[_ngcontent-%COMP%]   .form-content[_ngcontent-%COMP%]   .single-field[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-wrapper{padding-bottom:0!important}.dialog[_ngcontent-%COMP%]   .loading-section-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;gap:6px;height:calc(100vh - 104px)}.dialog[_ngcontent-%COMP%]   .loading-section-container[_ngcontent-%COMP%]   .loading-wrapper[_ngcontent-%COMP%]{display:flex;vertical-align:center;justify-content:center;align-items:center}.dialog[_ngcontent-%COMP%]   .loading-section-container[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]{color:rgba(0,0,0,.3);font-size:16px}.dialog[_ngcontent-%COMP%]   .loading-section-container[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]:before{content:"Loading...";position:absolute;overflow:hidden;max-width:7em;white-space:nowrap;background:linear-gradient(270deg,var(--atsprimaryColor),var(--atssecondaryColor) 105.29%);background-clip:text;-webkit-background-clip:text;-webkit-text-fill-color:transparent;animation:loading 6s linear infinite}@keyframes loading{0%{max-width:0}}.dialog[_ngcontent-%COMP%]   .list-view[_ngcontent-%COMP%]{display:flex;flex-direction:column;overflow:auto;padding:16px}.dialog[_ngcontent-%COMP%]   .list-view[_ngcontent-%COMP%]   .header-row[_ngcontent-%COMP%]{min-height:32px;background:#f2f3f6}.dialog[_ngcontent-%COMP%]   .list-view[_ngcontent-%COMP%]   .header-row[_ngcontent-%COMP%]   .list-title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:12px;font-weight:500;color:#5f6c81;padding-right:8px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.dialog[_ngcontent-%COMP%]   .list-view[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{display:flex;flex-direction:column;overflow-y:auto;height:calc(100vh - 136px)}.dialog[_ngcontent-%COMP%]   .list-view[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .content-row-main[_ngcontent-%COMP%]{height:48px;background-color:#fff}.dialog[_ngcontent-%COMP%]   .list-view[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .content-row-main[_ngcontent-%COMP%]   .content-row[_ngcontent-%COMP%]{height:48px;background-color:#fff;border-bottom:1px solid #b9c0ca}.dialog[_ngcontent-%COMP%]   .list-view[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .content-row-main[_ngcontent-%COMP%]   .content-row[_ngcontent-%COMP%]   .sub-content[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:400;color:#45546e;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.dialog[_ngcontent-%COMP%]   .buttons[_ngcontent-%COMP%]{width:100%;display:flex;justify-content:end;gap:8px;padding:0 8px 8px}.dialog[_ngcontent-%COMP%]   .buttons[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{border:1px solid #45546e;border-radius:8px;padding:7px 11px;color:#45546e}.dialog[_ngcontent-%COMP%]   .buttons[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%], .dialog[_ngcontent-%COMP%]   .buttons[_ngcontent-%COMP%]   .create-btn[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:700;cursor:pointer}.dialog[_ngcontent-%COMP%]   .buttons[_ngcontent-%COMP%]   .create-btn[_ngcontent-%COMP%]{border-radius:8px;padding:8px 12px;background-color:var(--atsprimaryColor);color:#fff}']}),e})()},pPzn:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));var o=n("fXoL");let i=(()=>{class e{transform(e,t,n){var o;return!!(e&&t&&n)&&(null===(o=e.find(e=>e.key==t))||void 0===o?void 0:o[n])}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=o["\u0275\u0275definePipe"]({name:"columnCustomization",type:e,pure:!0}),e})()}}]);