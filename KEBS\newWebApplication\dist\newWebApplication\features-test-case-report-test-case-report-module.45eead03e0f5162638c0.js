(window.webpackJsonp=window.webpackJsonp||[]).push([[660,634,765,821,822,858,983,987,990,991],{H44p:function(e,t,r){"use strict";r.d(t,"a",(function(){return g}));var i=r("xG9w"),n=r("fXoL"),a=r("flaP"),o=r("ofXK"),s=r("Qu3c"),c=r("NFeN");function d(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"div",9),n["\u0275\u0275elementStart"](1,"div",10),n["\u0275\u0275elementStart"](2,"div"),n["\u0275\u0275text"](3),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](4,"div"),n["\u0275\u0275elementStart"](5,"p",11),n["\u0275\u0275text"](6),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](7,"p",12),n["\u0275\u0275text"](8),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e){const e=n["\u0275\u0275nextContext"](2);n["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),n["\u0275\u0275advance"](3),n["\u0275\u0275textInterpolate"](null==e.currency[e.index]?null:e.currency[e.index].currency_code),n["\u0275\u0275advance"](3),n["\u0275\u0275textInterpolate"](e.label),n["\u0275\u0275advance"](2),n["\u0275\u0275textInterpolate1"](" ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function l(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"div",13),n["\u0275\u0275elementStart"](1,"span"),n["\u0275\u0275text"](2),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e){const e=n["\u0275\u0275nextContext"](2);n["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),n["\u0275\u0275advance"](2),n["\u0275\u0275textInterpolate1"](" ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function u(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"div",14),n["\u0275\u0275elementStart"](1,"span",15),n["\u0275\u0275text"](2),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e){const e=n["\u0275\u0275nextContext"](2);n["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),n["\u0275\u0275advance"](2),n["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function p(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"div",16),n["\u0275\u0275elementStart"](1,"span",15),n["\u0275\u0275text"](2),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e){const e=n["\u0275\u0275nextContext"](2);n["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),n["\u0275\u0275advance"](2),n["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function h(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"div",17),n["\u0275\u0275elementStart"](1,"span",18),n["\u0275\u0275text"](2),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e){const e=n["\u0275\u0275nextContext"](2);n["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),n["\u0275\u0275advance"](2),n["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function f(e,t){1&e&&(n["\u0275\u0275elementStart"](0,"mat-icon",19),n["\u0275\u0275text"](1,"loop"),n["\u0275\u0275elementEnd"]())}function m(e,t){if(1&e){const e=n["\u0275\u0275getCurrentView"]();n["\u0275\u0275elementStart"](0,"div",1),n["\u0275\u0275listener"]("click",(function(){return n["\u0275\u0275restoreView"](e),n["\u0275\u0275nextContext"]().change()})),n["\u0275\u0275template"](1,d,9,4,"div",2),n["\u0275\u0275template"](2,l,3,2,"div",3),n["\u0275\u0275template"](3,u,3,3,"div",4),n["\u0275\u0275template"](4,p,3,3,"div",5),n["\u0275\u0275template"](5,h,3,3,"div",6),n["\u0275\u0275elementStart"](6,"div",7),n["\u0275\u0275template"](7,f,2,0,"mat-icon",8),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()}if(2&e){const e=n["\u0275\u0275nextContext"]();n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf","big"==e.type),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf","small"==e.type),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf","medium"==e.type),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf","large"==e.type),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf","overview"==e.type),n["\u0275\u0275advance"](2),n["\u0275\u0275property"]("ngIf",e.toDisplay)}}let g=(()=>{class e{constructor(e){this.roleService=e,this.showActualAmount=!1,this.currency=[]}set currencyList(e){if(this.currency=e,e){const t=i.findIndex(e,e=>e.currency_code==this.currency_code);-1!=t&&(this.index=t,this.currency[this.index].value=Math.trunc(this.currency[this.index].value))}}ngOnInit(){null==this.toDisplay&&(this.toDisplay=!0),this.tenant_info=this.roleService.currency_info,this.tenantName=this.tenant_info.tenant_name,this.currency_code=this.defaultCurrencyCode?this.defaultCurrencyCode:this.tenant_info.default_currency?this.tenant_info.default_currency:"INR",this.setCurrency(),this.isConvertValue=null==this.tenant_info.is_to_convert_currency_value||this.tenant_info.is_to_convert_currency_value}setCurrency(){if(this.currency){const e=i.findIndex(this.currency,e=>e.currency_code==this.currency_code);-1!=e&&(this.index=e,this.currency[this.index].value=Math.trunc(this.currency[this.index].value))}}change(){void 0!==event.stopPropagation?event.stopPropagation():void 0!==event.cancelBubble&&(event.cancelBubble=!0),this.currency.length>1&&(this.index=++this.index%this.currency.length),this.currency[this.index].value=Math.trunc(this.currency[this.index].value)}convert(e,t){return e?(this.showActualAmount?e=new Intl.NumberFormat("INR"==t?"en-IN":"en-US",{}).format(e)+" "+t:1==this.isConvertValue?e="INR"==t?(e/1e7).toFixed(2)+" Cr":(e/1e6).toFixed(2)+" M":0!=this.isConvertValue||i.contains(["big","small"],this.type)?0==this.isConvertValue&&i.contains(["big","small"],this.type)&&(e="INR"==t?"big"!=this.type?"INR "+new Intl.NumberFormat("en-IN",{}).format(e):new Intl.NumberFormat("en-IN",{}).format(e):"big"!=this.type?t+" "+new Intl.NumberFormat("en-US",{}).format(e):new Intl.NumberFormat("en-US",{}).format(e)):e="INR"==t?(e/1e7).toFixed(2)+" Cr":(e/1e6).toFixed(2)+" M",e):"-"}}return e.\u0275fac=function(t){return new(t||e)(n["\u0275\u0275directiveInject"](a.a))},e.\u0275cmp=n["\u0275\u0275defineComponent"]({type:e,selectors:[["app-currency"]],inputs:{type:"type",label:"label",showActualAmount:"showActualAmount",acl:"acl",toDisplay:"toDisplay",defaultCurrencyCode:"defaultCurrencyCode",currencyList:"currencyList"},decls:1,vars:1,consts:[["class","d-flex currency-wrapper",3,"click",4,"ngIf"],[1,"d-flex","currency-wrapper",3,"click"],["class","d-flex",3,"matTooltip",4,"ngIf"],["class","data-label",3,"matTooltip",4,"ngIf"],["class","data-medium d-flex",3,"matTooltip",4,"ngIf"],["class","data-large d-flex",3,"matTooltip",4,"ngIf"],["class","justify-content-center",3,"matTooltip",4,"ngIf"],[1,"d-flex","align-items-center"],["class","change-icon pl-2","matTooltip","Next currency",4,"ngIf"],[1,"d-flex",3,"matTooltip"],[1,"pr-3","my-auto","currency-code","justify-content-center"],[1,"m-0","header"],[1,"m-0","data-label","pt-1"],[1,"data-label",3,"matTooltip"],[1,"data-medium","d-flex",3,"matTooltip"],[1,"justify-content-center"],[1,"data-large","d-flex",3,"matTooltip"],[1,"justify-content-center",3,"matTooltip"],[1,"data-overview"],["matTooltip","Next currency",1,"change-icon","pl-2"]],template:function(e,t){1&e&&n["\u0275\u0275template"](0,m,8,6,"div",0),2&e&&n["\u0275\u0275property"]("ngIf",t.currency)},directives:[o.NgIf,s.a,c.a],styles:[".header[_ngcontent-%COMP%]{color:#5f5f5f;font-size:11px!important}.data-label[_ngcontent-%COMP%]{font-size:13px!important}.change-icon[_ngcontent-%COMP%]{font-size:18px;width:auto;height:auto;visibility:hidden;cursor:default}.currency-code[_ngcontent-%COMP%]{padding-right:8px;font-size:14px!important}.currency-wrapper[_ngcontent-%COMP%]{white-space:nowrap!important;height:100%}.currency-wrapper[_ngcontent-%COMP%]:hover   .change-icon[_ngcontent-%COMP%]{visibility:visible}.data-medium[_ngcontent-%COMP%]{color:#252422!important;font-size:15px!important}.data-large[_ngcontent-%COMP%], .data-medium[_ngcontent-%COMP%]{text-align:center!important;font-weight:440!important;padding-right:4px!important}.data-large[_ngcontent-%COMP%]{font-size:large!important}.data-overview[_ngcontent-%COMP%]{color:#252422!important;font-size:15px!important;padding-left:24px!important;font-weight:500!important;padding-right:4px!important;text-align:center!important;margin:auto!important}"]}),e})()},NJ67:function(e,t,r){"use strict";r.d(t,"a",(function(){return i}));class i{constructor(){this.disabled=!1}onChange(e){}onTouched(e){}writeValue(e){this.value=e}registerOnChange(e){this.onChange=e}registerOnTouched(e){this.onTouched=e}setDisabledState(e){this.disabled=e}}},"cER/":function(e,t,r){"use strict";r.r(t),r.d(t,"TestCaseReportModule",(function(){return A}));var i=r("ofXK"),n=r("tyNb"),a=r("mrSG"),o=r("XNiG"),s=r("1G5W"),c=r("xG9w"),d=r("fXoL"),l=r("Vym5"),u=r("GnQ3");function p(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",2),d["\u0275\u0275element"](1,"div",8),d["\u0275\u0275elementEnd"]())}function h(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",2),d["\u0275\u0275elementStart"](1,"div",3),d["\u0275\u0275template"](2,p,2,0,"div",4),d["\u0275\u0275elementStart"](3,"span",5),d["\u0275\u0275element"](4,"div",6),d["\u0275\u0275elementStart"](5,"span",7),d["\u0275\u0275text"](6),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",0!=(null==e?null:e.level)||""!=(null==e?null:e.level)),d["\u0275\u0275advance"](4),d["\u0275\u0275textInterpolate"](null==e?null:e.text)}}let f=(()=>{class e{constructor(e,t){this.projectService=e,this.udrfService=t,this.data={project_id:635,version_id:4.225,id:1576556767204},this.getHierarchyData=()=>Object(a.c)(this,void 0,void 0,(function*(){let e=yield this.projectService.getAllParents(this.udrfService.udrfUiData.showHierarchyData.data.project_id,this.udrfService.udrfUiData.showHierarchyData.data.gantt_id);this.hierarchyArray=c.sortBy(e,"level")}))}ngOnInit(){this.getHierarchyData()}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](l.a),d["\u0275\u0275directiveInject"](u.a))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-hierarchy-component"]],decls:2,vars:1,consts:[[1,"main-ctnr","h-100","w-100","py-3","px-2"],["class","row",4,"ngFor","ngForOf"],[1,"row"],[1,"col-12"],["class","row",4,"ngIf"],[1,"ml-1","d-flex"],[1,"status-circular-in-thread","mr-3"],[1,"txt-overflow"],[1,"col-12","ml-2","left-border-line"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",0),d["\u0275\u0275template"](1,h,7,2,"div",1),d["\u0275\u0275elementEnd"]()),2&e&&(d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngForOf",t.hierarchyArray))},directives:[i.NgForOf,i.NgIf],styles:[".main-ctnr[_ngcontent-%COMP%]{font-size:14px}.main-ctnr[_ngcontent-%COMP%]   .txt-bold[_ngcontent-%COMP%]{font-weight:500}.main-ctnr[_ngcontent-%COMP%]   .txt-highlighted[_ngcontent-%COMP%]{color:#c92020}.main-ctnr[_ngcontent-%COMP%]   .txt-overflow[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.main-ctnr[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%]{width:30px!important;height:30px!important;line-height:30px!important}.main-ctnr[_ngcontent-%COMP%]   .icon-button[_ngcontent-%COMP%]   .icon-size[_ngcontent-%COMP%]{font-size:21px!important}.main-ctnr[_ngcontent-%COMP%]   .left-border-line[_ngcontent-%COMP%]{height:14px;border-left:1px solid #c7c4c4}.main-ctnr[_ngcontent-%COMP%]   .status-circular-in-thread[_ngcontent-%COMP%]{height:11px;width:11px;min-width:11px;margin-top:3px;z-index:1;border-radius:50%;background-color:#c7c4c4;box-shadow:0 2px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}"]}),e})();var m=r("wd/R"),g=r("JLuW"),v=r("dNgK"),y=r("0IaG"),C=r("LcQX"),S=r("mEBv"),x=r("BVzC"),D=r("tk/3");let b=(()=>{class e{constructor(e){this.http=e,this.getTestCaseList=e=>this.http.post("/api/project/reports/getTestCaseListUDRF",{filterConfig:e}),this.getTestCaseCards=e=>this.http.post("/api/project/reports/getTestCaseCardsUDRF",{filterConfig:e})}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275inject"](D.c))},e.\u0275prov=d["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var O=r("HmYF"),_=r("xi/V"),w=r("Wk3H");const I=[{path:"",component:(()=>{class e{constructor(e,t,r,i,n,a,s,c,d){this.sharedLazyLoadedComponentsService=e,this.snackBar=t,this.udrfService=r,this.dialog=i,this.utilityService=n,this.tooltip=a,this.errorService=s,this.testCaseService=c,this._excelService=d,this.applicationId=259,this.PGItemDataCurrentIndex=0,this.isLoading=!1,this.cardIterator=[],this.selectedCard=[],this._onDestroy=new o.b,this.actionBtns=[],this.statusMasterData=[],this.locationMasterData=[],this._onAppApiCalled=new o.b,this.dataTypeArray=[{dataType:"Draft",dataTypeValue:"0",isActive:!1,dataTypeCode:"D",isVisible:!1,cardType:"status",statusColor:"#e2e2e2"},{dataType:"Execution",dataTypeValue:"0",isActive:!1,dataTypeCode:"EX",isVisible:!0,cardType:"status",statusColor:"#ffa502"},{dataType:"Completed",dataTypeValue:"0",isActive:!1,dataTypeCode:"C",isVisible:!0,cardType:"status",statusColor:"#009432"},{dataType:"Cancelled",dataTypeValue:"0",isActive:!1,dataTypeCode:"CL",isVisible:!0,cardType:"status",statusColor:"#c7c4c4"},{dataType:"Open",dataTypeValue:"0",isActive:!1,dataTypeCode:"O",isVisible:!0,cardType:"status",statusColor:"#e2e2e2"}],this.categorisedDataTypeArray=[{categoryType:"Status Cards",categoryCardCodes:["D","EX","C","CL","O"],categoryCards:[]}],this.user_duration_start=m(),this.mainApiDateRangeEnd=m(),this.current_year_start=m(),this.current_year_end=m(),this.minNoOfVisibleSummaryCards=3,this.maxNoOfVisibleSummaryCards=6,this.isCardClicked=!1,this.cardClicked="",this.udrfBodyColumns=[{item:"description",header:"Description",isActive:!0,isVisible:"true",type:"text",position:1,colSize:2,textClass:"",width:300,sortOrder:"I",filterId:20},{item:"status",header:"Status",isActive:!0,isVisible:"true",type:"status",position:4,colSize:1,textClass:"value13Bold",isInlineEdit:!0,inlineEditVarient:["Status","minimal-dropdown"],width:150,sortOrder:"I",filterId:21},{item:"assigned_to_name",header:"Owner",isActive:!0,isVisible:"true",type:"profile",position:3,colSize:2,textClass:"value13Bold",width:200,sortOrder:"N",filterId:22},{item:"planned_start_date",header:"Planned Start Date",isActive:!0,isVisible:"true",type:"date",position:5,colSize:2,textClass:"pl-3 value13Bold",width:200,sortOrder:"I",filterId:23},{item:"planned_end_date",header:"Planned End Date",isActive:!0,isVisible:"true",type:"date",position:6,colSize:2,textClass:"pl-3 value13Bold",width:200,sortOrder:"I",filterId:24},{item:"item_name",header:"Item Name",isActive:!0,isVisible:"false",type:"text",position:7,colSize:2,textClass:"value13Bold",width:200,sortOrder:"N",filterId:25},{item:"pl_description",header:"Region",isActive:!0,isVisible:"false",type:"text",position:9,colSize:2,textClass:"value13Bold",width:140,sortOrder:"N",filterId:26},{item:"service_type_name",header:"Service Type",isActive:!0,isVisible:"false",type:"text",position:10,colSize:2,textClass:"value13Bold",width:200,sortOrder:"N",filterId:27},{item:"profit_center",header:"Cost Center",isActive:!0,isVisible:"false",type:"text",position:11,colSize:2,textClass:"value13Bold",width:200,sortOrder:"N",filterId:28},{item:"action",header:"Actions",isActive:!0,isVisible:"false",type:"action",position:12,colSize:2,width:240,sortOrder:"N"},{item:"planned_hours",header:"Planned hours",isActive:!0,isVisible:"false",type:"text",position:17,colSize:1,width:100,sortOrder:"N",filterId:29,textClass:"text-center"},{item:"location_name",header:"Activity location",isActive:!0,isVisible:"false",type:"text",position:13,colSize:1,textClass:"value13Bold",isInlineEdit:!0,inlineEditVarient:["location","search-dropdown"],width:180,sortOrder:"N",filterId:31},{item:"area_path",header:"Area Path",isActive:!0,isVisible:"false",type:"text",position:14,colSize:1,textClass:"value13Bold",width:250,sortOrder:"N",filterId:33},{item:"iteration",header:"Iteration",isActive:!0,isVisible:"false",type:"text",position:15,colSize:2,textClass:"value13Bold",width:300,sortOrder:"N",filterId:34},{item:"effort",header:"Effort",isActive:!0,isVisible:"false",type:"text",position:16,colSize:1,textClass:"value13Bold",width:150,sortOrder:"N",filterId:35}],this.udrfItemStatusColor=[{status:"Draft",color:"#e2e2e2"},{status:"SHS",color:"#ffa502"},{status:"Delivery Mandate",color:"#009432"},{status:"Execution",color:"#ffa502"},{status:"Completed",color:"#009432"},{status:"Cancelled",color:"#c7c4c4"},{status:"Open",color:"#e2e2e2"},{status:"YTB",color:"#648588"},{status:"Billed",color:"#ff7200"},{status:"Partial Payment",color:"#00e64d"},{status:"Payment Recieved ",color:"#009432"}],this.dataTypeCardSelected=()=>{this.udrfService.udrfData.isItemDataLoading=!0,this.udrfService.udrfData.noItemDataFound=!1;let e=this.udrfService.udrfUiData.summaryCardsItem;for(let t=0;t<this.dataTypeArray.length;t++)this.dataTypeArray[t].dataType!=e.dataType&&(this.dataTypeArray[t].isActive=!1);e.isActive=!e.isActive,this.isCardClicked=!0,this.cardClicked=e.dataType,this.PGItemDataCurrentIndex=0,this.udrfService.udrfBodyData=[],this.getTestCaseList()},this.convertToLocalTime=e=>{let t=new Date(e),r=6e4*t.getTimezoneOffset();return t.setTime(t.getTime()-r),t},this.current_year_start=m().startOf("year"),this.current_year_end=m().endOf("year")}ngOnInit(){return Object(a.c)(this,void 0,void 0,(function*(){this.PGItemDataCurrentIndex=0,this.udrfService.udrfBodyData=[];let e=new Date,t=(m([m().year(),e.getMonth()-1,25]),m([m().year(),e.getMonth(),24]),[{checkboxId:"CDCRD",checkboxName:"Current Year",checkboxStartValue:this.current_year_start,checkboxEndValue:this.current_year_end,isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD2",checkboxName:"This Week",checkboxStartValue:this.utilityService.getFormattedDate(m().startOf("week"),m(m().startOf("week")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(m().endOf("week"),m(m().endOf("week")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD3",checkboxName:"This Month",checkboxStartValue:this.utilityService.getFormattedDate(m().startOf("month"),m(m().startOf("month")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(m().endOf("month"),m(m().endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD4",checkboxName:"Next Month",checkboxStartValue:this.utilityService.getFormattedDate(m().add(1,"month").startOf("month"),m(m().add(1,"month").startOf("month")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(m().add(1,"month").endOf("month"),m(m().add(1,"month").endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD5",checkboxName:"Upcoming 3 Months",checkboxStartValue:this.utilityService.getFormattedDate(m().startOf("month"),m(m().startOf("month")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(m().add(2,"month").endOf("month"),m(m().add(2,"month").endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD6",checkboxName:"Overdue",checkboxStartValue:this.utilityService.getFormattedDate(m("2016-01-01"),m("2016-01-01").date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(m(),m().date,15,0,0,0),isCheckboxDefaultSelected:!1}]);this.udrfService.udrfFunctions.constructCustomRangeData(7,"date",t);let r=[{checkboxId:"CDCRD",checkboxName:"Current Year",checkboxStartValue:this.current_year_start,checkboxEndValue:this.current_year_end,isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD2",checkboxName:"This Week",checkboxStartValue:this.utilityService.getFormattedDate(m().startOf("week"),m(m().startOf("week")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(m().endOf("week"),m(m().endOf("week")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD3",checkboxName:"This Month",checkboxStartValue:this.utilityService.getFormattedDate(m().startOf("month"),m(m().startOf("month")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(m().endOf("month"),m(m().endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD4",checkboxName:"Next Month",checkboxStartValue:this.utilityService.getFormattedDate(m().add(1,"month").startOf("month"),m(m().add(1,"month").startOf("month")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(m().add(1,"month").endOf("month"),m(m().add(1,"month").endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD5",checkboxName:"Upcoming 3 Months",checkboxStartValue:this.utilityService.getFormattedDate(m().startOf("month"),m(m().startOf("month")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(m().add(2,"month").endOf("month"),m(m().add(2,"month").endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD6",checkboxName:"Overdue",checkboxStartValue:this.utilityService.getFormattedDate(m("2016-01-01"),m("2016-01-01").date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(m(),m().date,15,0,0,0),isCheckboxDefaultSelected:!1}];this.udrfService.udrfFunctions.constructCustomRangeData(8,"date",r);let i=[{checkboxId:"CDCRD",checkboxName:"Current Year",checkboxStartValue:this.current_year_start,checkboxEndValue:this.current_year_end,isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD2",checkboxName:"This Week",checkboxStartValue:this.utilityService.getFormattedDate(m().startOf("week"),m(m().startOf("week")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(m().endOf("week"),m(m().endOf("week")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD3",checkboxName:"This Month",checkboxStartValue:this.utilityService.getFormattedDate(m().startOf("month"),m(m().startOf("month")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(m().endOf("month"),m(m().endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD4",checkboxName:"Next Month",checkboxStartValue:this.utilityService.getFormattedDate(m().add(1,"month").startOf("month"),m(m().add(1,"month").startOf("month")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(m().add(1,"month").endOf("month"),m(m().add(1,"month").endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD5",checkboxName:"Upcoming 3 Months",checkboxStartValue:this.utilityService.getFormattedDate(m().startOf("month"),m(m().startOf("month")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(m().add(2,"month").endOf("month"),m(m().add(2,"month").endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD10",checkboxName:"Timesheet Duration",checkboxStartValue:m().startOf("month").format("YYYY-MM-DD"),checkboxEndValue:m().endOf("month").format("YYYY-MM-DD"),isCheckboxDefaultSelected:!0}];this.udrfService.udrfFunctions.constructCustomRangeData(12,"date",i),this.udrfService.udrfData.applicationId=this.applicationId,this.udrfService.udrfUiData.showNewReleasesButton=!0,this.udrfService.udrfUiData.showItemDataCount=!0,this.udrfService.udrfUiData.itemDataType="",this.udrfService.udrfUiData.totalItemDataCount=0,this.udrfService.udrfUiData.showSearchBar=!0,this.udrfService.udrfUiData.showActionButtons=!0,this.udrfService.udrfUiData.showUdrfModalButton=!0,this.udrfService.udrfUiData.showSettingsModalButton=!0,this.udrfService.udrfUiData.isReportDownloading=!1,this.udrfService.udrfUiData.showReportDownloadButton=!0,this.udrfService.udrfUiData.showColumnConfigButton=!0,this.udrfService.udrfUiData.udrfItemStatusColor=this.udrfItemStatusColor,this.udrfService.udrfUiData.resolveVisibleSummaryCards=this.resolveVisibleDataTypeArray.bind(this),this.udrfService.udrfUiData.summaryCardsSelected=this.dataTypeCardSelected.bind(this),this.udrfService.udrfUiData.itemcardSelected=this.projectCardClicked.bind(this),this.udrfService.udrfUiData.summaryCardsItem={},this.udrfService.udrfUiData.showHierarchy=this.showHierarchy.bind(this),this.udrfService.udrfUiData.showHierarchyData={},this.udrfService.udrfUiData.inlineEditData={},this.udrfService.udrfUiData.downloadItemDataReport=()=>{},this.udrfService.udrfUiData.openComments=this.openComments.bind(this),this.udrfService.udrfUiData.openQuickCta=this.openCTA.bind(this),this.udrfService.udrfUiData.itemDataScrollDown=this.onPGItemDataScrollDown.bind(this),this.udrfService.udrfUiData.summaryCards=this.dataTypeArray,this.udrfService.udrfUiData.udrfBodyColumns=this.udrfBodyColumns,this.udrfService.udrfUiData.udrfVisibleBodyColumns=this.udrfService.udrfUiData.udrfVisibleBodyColumns,this.udrfService.udrfUiData.udrfInvisibleBodyColumns=this.udrfService.udrfUiData.udrfInvisibleBodyColumns,this.udrfService.udrfUiData.categorisedSummaryCards=this.categorisedDataTypeArray,this.udrfService.udrfUiData.minNoOfVisibleSummaryCards=this.minNoOfVisibleSummaryCards,this.udrfService.udrfUiData.maxNoOfVisibleSummaryCards=this.maxNoOfVisibleSummaryCards,this.udrfService.udrfUiData.selectedCard=this.selectedCard,this.udrfService.udrfUiData.variant=1,this.udrfService.udrfUiData.itemHasQuickCta=!0,this.udrfService.udrfUiData.itemHasComments=!0,this.udrfService.udrfUiData.itemHasHierarchyView=!0,this.udrfService.udrfUiData.collapseAll=!0,this.udrfService.udrfUiData.showCollapseButton=!0,this.udrfService.udrfUiData.horizontalScroll=!0,this.udrfService.udrfUiData.itemHasOpenBtn=!0,this.udrfService.udrfUiData.openBtnClicked=this.openBtnClick.bind(this),this.udrfService.udrfUiData.downloadItemDataReport=this.downloadReport.bind(this),this.udrfService.getAppUdrfConfig(this.applicationId,this.initReport.bind(this)),this.udrfService.udrfUiData.callInlineEditApi=()=>{},this.udrfService.udrfUiData.inlineEditDropDownMasterDatas={statusMasterData:this.statusMasterData,locationMasterData:this.locationMasterData},this.udrfService.getNotifyReleasesUDRF()}))}initReport(){return Object(a.c)(this,void 0,void 0,(function*(){this._onAppApiCalled.next(),this.PGItemDataCurrentIndex=0,this.udrfService.udrfBodyData=[];for(let e=0;e<this.dataTypeArray.length;e++)this.dataTypeArray[e].isActive=!1;this.isCardClicked=!1,this.cardClicked="",this.udrfService.udrfUiData.resolveColumnConfig(),this.getTestCaseList()}))}getTestCaseList(){return Object(a.c)(this,void 0,void 0,(function*(){let e=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),t=c.where(this.dataTypeArray,{isActive:!0}),r=[],i=!1;if(t.length>0&&(console.log("mainFilterArrayItem"),console.log(e),r=c.where(t,{cardType:"status"}),r.length>0))if(e.length>0){for(let r of e)"Activity Status"==r.filterName&&(r.multiOptionSelectSearchValues=[t[0].dataType],i=!0);if(0==i){let r=JSON.parse(JSON.stringify(c.where(this.udrfService.udrfData.filterTypeArray,{filterName:"Activity Status"})));e.push(r[0]);for(let n of e)"Activity Status"==n.filterName&&(n.multiOptionSelectSearchValues=[t[0].dataType],i=!0)}}else e=JSON.parse(JSON.stringify(c.where(this.udrfService.udrfData.filterTypeArray,{filterName:"Activity Status"}))),e[0].multiOptionSelectSearchValues=[t[0].dataType];let n={startIndex:this.PGItemDataCurrentIndex,startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,mainFilterArray:e,txTableDetails:this.udrfService.udrfData.txTableDetails,mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,searchTableDetails:this.udrfService.udrfData.searchTableDetails};this.selectedCard=this.udrfService.udrfData.udrfSummaryCardCodes,this.requestDataSubscription&&this.requestDataSubscription.unsubscribe(),this.requestDataSubscription=this.testCaseService.getTestCaseList(n).pipe(Object(s.a)(this._onDestroy)).pipe(Object(s.a)(this._onAppApiCalled)).subscribe(e=>Object(a.c)(this,void 0,void 0,(function*(){"S"==e.messType&&e.messData&&e.messData.length>0?this.isCardClicked?this.udrfService.udrfBodyData=this.udrfService.udrfBodyData.concat(e.messData):(this.udrfService.udrfBodyData=this.udrfService.udrfBodyData.concat(e.messData),this.initPGCard()):(this.udrfService.udrfBodyData=this.udrfService.udrfBodyData.concat([]),this.udrfService.udrfData.noItemDataFound=!0),this.udrfService.udrfData.isItemDataLoading=!1})),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while retrieving WFH Approvals",e&&e.params?e.params:e&&e.error?e.error.params:{})})}))}initPGCard(){return Object(a.c)(this,void 0,void 0,(function*(){console.log("initcard"),this.testCaseService.getTestCaseCards({startIndex:this.PGItemDataCurrentIndex,startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,mainFilterArray:this.udrfService.udrfData.mainFilterArray,txTableDetails:this.udrfService.udrfData.txTableDetails,mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,searchTableDetails:this.udrfService.udrfData.searchTableDetails}).pipe(Object(s.a)(this._onDestroy)).pipe(Object(s.a)(this._onAppApiCalled)).subscribe(e=>Object(a.c)(this,void 0,void 0,(function*(){if(this.udrfService.udrfData.udrfSummaryCardCodes.length>0&&(console.log(this.udrfService.udrfData.udrfSummaryCardCodes),this.resolveVisibleDataTypeArray()),"S"==e.messType&&e.messData&&e.messData.length>0){for(let e=0;e<this.dataTypeArray.length;e++)this.dataTypeArray[e].isActive=!1,this.dataTypeArray[e].dataTypeValue="0";this.dataTypeArray=this.dataTypeArray.map((t,r)=>{const i=e.messData.find(e=>t.dataType==e.dataType);return Object.assign(Object.assign({},t),i)}),this.udrfService.udrfUiData.summaryCards=this.dataTypeArray,this.udrfService.udrfUiData.totalItemDataCount=e.total}else{for(let e=0;e<this.dataTypeArray.length;e++)this.dataTypeArray[e].isActive=!1,this.dataTypeArray[e].dataTypeValue="0";this.udrfService.udrfUiData.summaryCards=this.dataTypeArray}})),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while retrieving WFH Approvals",e&&e.params?e.params:e&&e.error?e.error.params:{})})}))}onPGItemDataScrollDown(){return Object(a.c)(this,void 0,void 0,(function*(){this.udrfService.udrfData.noItemDataFound||this.udrfService.udrfData.isItemDataLoading||(this.PGItemDataCurrentIndex+=this.udrfService.udrfData.defaultRecordsPerFetch,this.udrfService.udrfData.isItemDataLoading=!0,yield this.getTestCaseList())}))}resolveVisibleDataTypeArray(){return Object(a.c)(this,void 0,void 0,(function*(){for(let e of this.udrfService.udrfUiData.summaryCards){let t;this.udrfService.udrfData.udrfSummaryCardCodes.length>0&&(t=c.contains(this.udrfService.udrfData.udrfSummaryCardCodes,e.dataTypeCode),e.isVisible=t,t&&(this.udrfService.udrfUiData.summaryCardsItem=e))}}))}showErrorMessage(e){this.utilityService.showErrorMessage("Error:Unable to fetch Data","KEBS")}projectCardClicked(){}encodeURIComponent(e){return encodeURIComponent(e).replace(/[!'()*]/g,(function(e){return"%"+e.charCodeAt(0).toString(16)}))}showHierarchy(){let e=this.udrfService.udrfUiData.showHierarchyData.event;e.padding="0px",e.width="auto",e.height="auto",e.maxWidth="28rem",e.maxHeight="450px",e.showDelay=500,e.hideDelay=300,e.trigger="click",e.theme="dark",e.autoPlacement=!0,e.component=f,this.tooltip.load(e)}openComments(){return Object(a.c)(this,void 0,void 0,(function*(){let e=this.udrfService.udrfUiData.openCommentsData.data,t=this.udrfService.udrfUiData.openCommentsData.l2index,i=window.location.origin+"/main/project/"+e.project_id+"/"+this.encodeURIComponent(e.project_name);this.inputData={application_id:151,unique_id_1:t||0==t?e.l2[t].id:e.id,unique_id_2:"",application_name:"Project governance"},this.inputData.title=t||0==t?e.l2[t].description:e.description,this.inputData.link=i;let n={inputData:this.inputData,context:{"Project Name":e.project_name,"Activity Name":t||0==t?e.l2[t].description:"","Activity Status":t||0==t?e.l2[t].status:e.status,Owner:t||0==t?e.l2[t].assigned_to_name:""},commentBoxHeight:"100vh",commentBoxScrollHeight:"80%"};const{ChatCommentContextModalComponent:a}=yield Promise.all([r.e(4),r.e(63),r.e(75),r.e(983)]).then(r.bind(null,"vg2w"));this.dialog.open(a,{height:"100%",width:"65%",position:{right:"0px"},data:{modalParams:n}})}))}openCTA(){let e=this.udrfService.udrfUiData.openQuickCtaData.data,t=this.udrfService.udrfUiData.openCommentsData.l2index;this.sharedLazyLoadedComponentsService.openQuickCTAModal({applicationId:151,objectId:t||0==t?e.l2[t].item_id:e.l2[0].item_id,objectType:"P",originatorData:null},this.dialog)}openBtnClick(){var e;console.log(this.udrfService.udrfUiData.openButtonData);let t=this.udrfService.udrfUiData.openButtonData;if(t)if(t.l2Index||0===t.l2Index){let r=null===(e=t.data)||void 0===e?void 0:e.l2[t.l2Index];r&&r.link?window.open(r.link):this.utilityService.showMessage("No external link found for this work item","Dismiss")}else{let e=window.location.origin+`/main/project/${encodeURIComponent(t.data.id)}/${encodeURIComponent(t.data.project_name)}/overview`;window.open(e)}else this.utilityService.showMessage("Sorry, the current selected work item cannot be identified","Dismiss")}downloadReport(){let e=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),t=c.where(this.dataTypeArray,{isActive:!0}),r=[],i=!1;if(t.length>0&&(console.log("mainFilterArrayItem"),console.log(e),r=c.where(t,{cardType:"status"}),r.length>0))if(e.length>0){for(let r of e)"Activity Status"==r.filterName&&(r.multiOptionSelectSearchValues=[t[0].dataType],i=!0);if(0==i){let r=JSON.parse(JSON.stringify(c.where(this.udrfService.udrfData.filterTypeArray,{filterName:"Activity Status"})));e.push(r[0]);for(let n of e)"Activity Status"==n.filterName&&(n.multiOptionSelectSearchValues=[t[0].dataType],i=!0)}}else e=JSON.parse(JSON.stringify(c.where(this.udrfService.udrfData.filterTypeArray,{filterName:"Activity Status"}))),e[0].multiOptionSelectSearchValues=[t[0].dataType];let n={startIndex:"D",startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,mainFilterArray:e,txTableDetails:this.udrfService.udrfData.txTableDetails,mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,searchTableDetails:this.udrfService.udrfData.searchTableDetails};this.selectedCard=this.udrfService.udrfData.udrfSummaryCardCodes,this.requestDataSubscription&&this.requestDataSubscription.unsubscribe(),this.requestDataSubscription=this.testCaseService.getTestCaseList(n).pipe(Object(s.a)(this._onDestroy)).pipe(Object(s.a)(this._onAppApiCalled)).subscribe(e=>Object(a.c)(this,void 0,void 0,(function*(){this.udrfService.udrfUiData.isReportDownloading=!1,"S"==e.messType&&e.messData&&e.messData.length>0?this._excelService.exportAsExcelFile(e.messData,"LMS Report - "+m().format("DD-MMM-YYYY")):this.utilityService.showMessage(e.messText,"Dismiss",3e3)})),e=>{this.udrfService.udrfUiData.isReportDownloading=!1,this.utilityService.showMessage("Something went Wrong!","Dismiss",3e3)})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete(),this.udrfService.resetUdrfData()}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](g.a),d["\u0275\u0275directiveInject"](v.a),d["\u0275\u0275directiveInject"](u.a),d["\u0275\u0275directiveInject"](y.b),d["\u0275\u0275directiveInject"](C.a),d["\u0275\u0275directiveInject"](S.a),d["\u0275\u0275directiveInject"](x.a),d["\u0275\u0275directiveInject"](b),d["\u0275\u0275directiveInject"](O.a))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-test-case-report-landing-page"]],decls:3,vars:0,consts:[[1,"container-fluid","project-governance-component","pl-0","pr-0"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",0),d["\u0275\u0275element"](1,"udrf-header"),d["\u0275\u0275element"](2,"udrf-body"),d["\u0275\u0275elementEnd"]())},directives:[_.a,w.a],styles:[".project-governance-component[_ngcontent-%COMP%]   .status-circular[_ngcontent-%COMP%]{height:13px;width:13px;margin-top:4px;border-radius:50%;box-shadow:0 2px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.project-governance-component[_ngcontent-%COMP%]   .is-submitted[_ngcontent-%COMP%]{background:#ff7200;color:#fff!important}.project-governance-component[_ngcontent-%COMP%]   .is-draft[_ngcontent-%COMP%]{background:#c7c4c4;color:#fff!important}.project-governance-component[_ngcontent-%COMP%]   .is-approved[_ngcontent-%COMP%]{background:#009432;color:#fff!important}.project-governance-component[_ngcontent-%COMP%]   .is-wfh[_ngcontent-%COMP%]{background:#9980fa;color:#fff!important}.project-governance-component[_ngcontent-%COMP%]   .is-rejected[_ngcontent-%COMP%]{background:#cf0001;color:#fff!important}.project-governance-component[_ngcontent-%COMP%]   .is-cancelled[_ngcontent-%COMP%]{background:#9f2825;color:#fff!important}.project-governance-component[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field{width:40vw;padding-top:25px}.project-governance-component[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-infix{font-size:14px!important;padding:.5em 0!important;border-top:.54375em solid transparent!important}.project-governance-component[_ngcontent-%COMP%]   .spinner-align[_ngcontent-%COMP%]{margin:auto;display:inline-block}.project-governance-component[_ngcontent-%COMP%]   .infinite-scroll-fixed[_ngcontent-%COMP%]{height:425px;overflow-y:scroll}.project-governance-component[_ngcontent-%COMP%]   .infinite-scroll-fixed-shortened[_ngcontent-%COMP%]{height:435px;overflow-y:scroll}.project-governance-component[_ngcontent-%COMP%]   .ta-r[_ngcontent-%COMP%]{text-align:right!important}.project-governance-component[_ngcontent-%COMP%]   .ta-l[_ngcontent-%COMP%]{text-align:left}.project-governance-component[_ngcontent-%COMP%]   .is-normal-probability-text[_ngcontent-%COMP%]{color:grey!important}.project-governance-component[_ngcontent-%COMP%]   .is-low-probability-text[_ngcontent-%COMP%]{color:#cf0001!important}.project-governance-component[_ngcontent-%COMP%]   .is-moderate-probability-text[_ngcontent-%COMP%]{color:#ff7200!important}.project-governance-component[_ngcontent-%COMP%]   .is-high-probability-text[_ngcontent-%COMP%]{color:#009432!important}.project-governance-component[_ngcontent-%COMP%]   .is-normal-probability[_ngcontent-%COMP%]{border-left-color:grey!important}.project-governance-component[_ngcontent-%COMP%]   .is-low-probability[_ngcontent-%COMP%]{border-left-color:#cf0001!important}.project-governance-component[_ngcontent-%COMP%]   .is-moderate-probability[_ngcontent-%COMP%]{border-left-color:#ff7200!important}.project-governance-component[_ngcontent-%COMP%]   .is-high-probability[_ngcontent-%COMP%]{border-left-color:#009432!important}.project-governance-component[_ngcontent-%COMP%]   .value13Bold[_ngcontent-%COMP%]{font-size:13px!important}.project-governance-component[_ngcontent-%COMP%]   .value13Bold[_ngcontent-%COMP%], .project-governance-component[_ngcontent-%COMP%]   .value14Bold[_ngcontent-%COMP%]{color:#000;font-weight:500!important;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.project-governance-component[_ngcontent-%COMP%]   .value14Bold[_ngcontent-%COMP%]{font-size:14px!important}.project-governance-component[_ngcontent-%COMP%]   .listcard[_ngcontent-%COMP%]{transition:all .25s linear;height:auto}.project-governance-component[_ngcontent-%COMP%]   .smallSubtleText[_ngcontent-%COMP%]{color:#66615b;font-size:11px}.project-governance-component[_ngcontent-%COMP%]   .data-type-card[_ngcontent-%COMP%]{min-height:85px;transition:all .3s linear}.project-governance-component[_ngcontent-%COMP%]   .data-type-card-is-active[_ngcontent-%COMP%]{border-color:#cf0001;background-color:#fff2f2}.project-governance-component[_ngcontent-%COMP%]   .headingBold[_ngcontent-%COMP%]{color:#1a1a1a;font-weight:600;font-size:22px}.project-governance-component[_ngcontent-%COMP%]   .valueGrey14[_ngcontent-%COMP%]{color:#4a4a4a;font-size:14px;font-weight:300;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.project-governance-component[_ngcontent-%COMP%]   .valueGrey12Normal[_ngcontent-%COMP%]{color:#4a4a4a;font-size:12px;font-weight:400;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.project-governance-component[_ngcontent-%COMP%]   .cp[_ngcontent-%COMP%]{cursor:pointer!important}.project-governance-component[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]{margin-top:5px!important;line-height:8px;width:26px;height:26px;margin-right:10px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12);animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}.project-governance-component[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%], .project-governance-component[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.project-governance-component[_ngcontent-%COMP%]   .ta-c[_ngcontent-%COMP%]{text-align:center;padding-left:0!important;padding-right:0!important}.project-governance-component[_ngcontent-%COMP%]   .divAlignItemCenter[_ngcontent-%COMP%]{display:flex;align-items:center}.project-governance-component[_ngcontent-%COMP%]   .menu-icons[_ngcontent-%COMP%]{font-size:4px}.project-governance-component[_ngcontent-%COMP%]   .example-container[_ngcontent-%COMP%]{width:400px;max-width:100%;margin:0 25px 25px 0;display:inline-block;vertical-align:top}.project-governance-component[_ngcontent-%COMP%]   .example-list[_ngcontent-%COMP%]{border:1px solid #ccc;min-height:60px;background:#fff;border-radius:4px;overflow:hidden;display:block}.project-governance-component[_ngcontent-%COMP%]     .example-box{border-bottom:1px solid #ccc;color:rgba(0,0,0,.87);display:flex;flex-direction:row;align-items:left;justify-content:space-between;box-sizing:border-box;cursor:move;background:#fff;font-size:4px}.project-governance-component[_ngcontent-%COMP%]     .menu-list{cursor:move;font-size:8px}.project-governance-component[_ngcontent-%COMP%]   .cdk-drag-preview[_ngcontent-%COMP%]{box-sizing:border-box;border-radius:4px;box-shadow:0 5px 5px -3px rgba(0,0,0,.2),0 8px 10px 1px rgba(0,0,0,.14),0 3px 14px 2px rgba(0,0,0,.12)}.project-governance-component[_ngcontent-%COMP%]   .cdk-drag-placeholder[_ngcontent-%COMP%]{opacity:0}.project-governance-component[_ngcontent-%COMP%]   .cdk-drag-animating[_ngcontent-%COMP%]{transition:transform .25s cubic-bezier(0,0,.2,1)}.project-governance-component[_ngcontent-%COMP%]   .example-box[_ngcontent-%COMP%]:last-child{border:none}.project-governance-component[_ngcontent-%COMP%]   .example-list.cdk-drop-list-dragging[_ngcontent-%COMP%]   .example-box[_ngcontent-%COMP%]:not(.cdk-drag-placeholder){transition:transform .25s cubic-bezier(0,0,.2,1)}"]}),e})()}];let k=(()=>{class e{}return e.\u0275mod=d["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=d["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[n.k.forChild(I)],n.k]}),e})();var P=r("Xi0T");let A=(()=>{class e{}return e.\u0275mod=d["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=d["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,k,P.a]]}),e})()},hJL4:function(e,t,r){"use strict";r.d(t,"a",(function(){return u}));var i=r("mrSG"),n=r("XNiG"),a=r("xG9w"),o=r("fXoL"),s=r("tk/3"),c=r("LcQX"),d=r("XXEo"),l=r("flaP");let u=(()=>{class e{constructor(e,t,r,i){this.http=e,this.UtilityService=t,this.loginService=r,this.roleService=i,this.msg=new n.b,this.taskStatusColor=[],this.approveRequest=(e,t)=>this.http.post("/api/isa/request/approveResourceRequest",{requestDetails:e,approverOid:t}),this.rejectRequest=(e,t)=>this.http.post("/api/isa/request/rejectResourceRequest",{requestDetails:e,approverOid:t}),this.getTaskStatusColor()}getAllRequestsOfUser(e,t,r,i,n,a,o){let s=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getAllRequestsOfUser",{oid:e,statusId:t,statusCode:r,objectIds:i,skip:n,limit:a,filterConfig:o,orgIds:s})}getAllRoleAccess(){return a.where(this.roleService.roles,{application_id:139})}getRequestsBasedOnStatus(e,t,r,i,n,a,o){let s=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getRequestsBasedOnStatus",{oid:e,statusId:t,statusCode:r,objectIds:i,skip:n,limit:a,filterConfig:o,orgIds:s})}getRequestsForAwaitingApproval(e,t,r,i){return this.http.post("/api/isa/request/getRequestsForAwaitingApproval",{oid:e,skip:t,limit:r,filterConfig:i})}getObjectIdsBasedOnOid(e){return this.http.post("/api/isa/request/getObjectIdsBasedOnOid",{oid:e})}getStatusCountBasedOnOid(e,t,r,i){let n=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getStatusCountBasedOnOid",{oid:e,objectIds:t,dataTypeArray:r,filterConfig:i,orgIds:n})}getHeaderCount(e){return this.http.post("/api/isa/request/getHeaderCount",{oid:e})}getRequestBasedOnStatus(e,t){return this.http.post("/api/isa/request/getRequestBasedOnStatus",{oid:e,status:t})}getDataTypeArray(){return this.http.post("/api/isa/request/getISAStatusMasterDataUDRF",{})}sendMsg(e){this.msg.next(e)}getMsg(){return this.msg.asObservable()}getTaskStatusColor(){return new Promise((e,t)=>{this.http.post("/api/isa/request/getTaskStatusNameColor",{}).subscribe(t=>(this.taskStatusColor=t,e(t)),e=>{})})}getAllRequestsForRmg(e){return this.http.post("/api/isa/request/getAllRequestsForRmg",{filterConfig:e})}getTotalForRmg(e){return new Promise((t,r)=>{this.http.post("/api/isa/request/getTotalForRmg",{filterConfig:e}).subscribe(e=>t(e),e=>{})})}getDefaultISAStatusToDisplay(){return this.http.post("/api/isa/request/getISADefaultStatusToDisplay",{})}getCTCbreakUpForSkillSet(e){return new Promise((t,r)=>{this.http.post("/api/isa/request/getCTCbreakUpForSkillSet",e).subscribe(e=>t(e.data),e=>{r(e)})})}getCurrentUserRole(){return this.roleService&&this.roleService.roles&&this.roleService.roles.length>0?this.roleService.roles[0].role_id:null}getTemplateForUser(e){return this.http.post("/api/isa/request/getVisibleFormFieldsForUser",e)}saveAndApproveRequest(e){return this.http.post("/api/isa/request/updBudgetInfoByRequestId",e)}getVisibilityMatrix(e){return this.http.post("/api/isa/request/getAuthorzedFieldsByRoleId",e)}getISAAttachmentFromS3(e){return this.http.post("/api/isa/attachment/getISAAttachmentFromS3",{key:e})}getSkillType(){return this.http.post("/api/isa/request/getSkillType",{})}getSkillLevel(){return this.http.post("/api/isa/request/getSkillLevel",{})}getSkillExperience(){return this.http.post("/api/isa/request/getSkillExperience",{})}getSkillName(e){return this.http.post("/api/isa/request/getSkillName",{skill_type_id:e})}getIsaActivityReportDownload(){return this.http.post("/api/isa/request/getIsaActivityReportDownload",{})}getISASLAreport(e){return this.http.post("/api/isa/request/getISASLAreport",{filterConfig:e})}getISACustomReport(e){return this.http.post("/api/isa/request/getISACustomReport",{filterConfig:e})}getPositionType(){return this.http.post("/api/employee360/masterData/getPositionType",{})}calculateNetCostBasedOnPosition(e,t,r,n,o,s,c){return Object(i.c)(this,void 0,void 0,(function*(){let i;i=s&&s.length>1&&(yield this.getManpowerCostByOId(s,r,o,2))||(yield this.getManpowerCostBasedOnPosition(e,t,r,o,c));let d=yield this.getNonManpowerCost(t,r,n,o,2),l=yield this.getAllocatedCost(),u=0;u=(i?i.cost:0)+d.length>0?a.reduce(a.pluck(d,"cost"),(e,t)=>e+t,0):0;let p=l.length>0?a.reduce(a.pluck(l,"percentage"),(e,t)=>e+t,0):0;return{cost:u,currency:i&&i.currency_code?i.currency_code:"",manpowerCost:i,nonManpowerCost:d,allocatedCost:l,allocatedCostValue:u*(p/100)}}))}getManpowerCostBasedOnPosition(e,t,r,i,n){return new Promise((a,o)=>{this.http.post("/api/isa/configuration/getManpowerCostBasedOnPosition",{skillRoleId:e,nationalityId:t,locationGroupId:r,unit:i,position:n}).subscribe(e=>a(e),e=>(console.log(e),o(e)))})}getNonManpowerCost(e,t,r,i,n){return new Promise((a,o)=>{this.http.post("/api/project/getNonManpowerCost",{nationalityId:e,locationGroupId:t,locationId:r,unit:i,currency_id:n}).subscribe(e=>a(e),e=>(console.log(e),o(e)))})}getAllocatedCost(){return new Promise((e,t)=>{this.http.post("/api/project/getAllocatedCost","").subscribe(t=>e(t),e=>(console.log(e),t(e)))})}getManpowerCostByOId(e,t,r,i){return new Promise((n,a)=>{this.http.post("/api/project/getEmployeeSalary",{oid:e,location_group_id:t,unit_id:r,currency_id:i}).subscribe(e=>n(e),e=>(console.log(e),a(e)))})}getSkillNameMaster(){return this.http.post("/api/isa/configuration/getSkillName",{})}getVendorOid(e,t){return this.http.post("/api/isa/request/getVendorOid",{vendorNames:e,docID:t})}getVendorList(e,t){return this.http.post("/api/isa/request/getVendorList",{documentID:e,limitval:t})}removeVendor(e,t){return this.http.post("/api/isa/request/removeVendors",{vendorOid:e,documentID:t})}checkVendorOrNot(){return this.http.post("/api/isa/request/checkVendorOrNot",{})}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275inject"](s.c),o["\u0275\u0275inject"](c.a),o["\u0275\u0275inject"](d.a),o["\u0275\u0275inject"](l.a))},e.\u0275prov=o["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},ucYs:function(e,t,r){"use strict";r.d(t,"a",(function(){return c}));var i=r("mrSG"),n=r("xG9w"),a=r("fXoL"),o=r("tk/3"),s=r("BVzC");let c=(()=>{class e{constructor(e,t){this.http=e,this.errorService=t,this.workflowStatusList=[],this.getWorkflowStatusList()}getWorkflowStatusList(){this.http.post("/api/wfPrimary/getWorkflowStatusList","").subscribe(e=>{this.workflowStatusList=e.data},e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server While Getting Workflow Status List",e&&e.params?e.params:e&&e.error?e.error.params:{})})}getWorkflowProperties(e){return new Promise((t,r)=>{this.http.post("/api/wfPrimary/getWorkflowProperties",{applicationId:e}).subscribe(e=>t(e),e=>r(e))})}getWorkflowPropertiesByWorkflowId(e){return new Promise((t,r)=>{this.http.post("/api/wfPrimary/getWorkflowPropertiesByWorkflowId",{workflowId:e}).subscribe(e=>t(e),e=>r(e))})}getApproversHierarchy(e){return new Promise((t,r)=>{this.http.post("/api/wfPrimary/getApproversHierarchy",e).subscribe(e=>t(e),e=>r(e))})}createWorkflowItems(e){return new Promise((t,r)=>{this.http.post("/api/wfPrimary/createWorkflowItems",e).subscribe(e=>t(e),e=>r(e))})}getWorkflowDetails(e){return new Promise((t,r)=>{this.http.post("/api/wfPrimary/getWorkflowDetails",{workflowId:e}).subscribe(e=>t(e),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while getting workflow details",e&&e.params?e.params:e&&e.error?e.error.params:{})})})}formatApproversHierarchy(e,t){return Object(i.c)(this,void 0,void 0,(function*(){0==e.length&&t.cc0&&t.cc0.appr0&&t.cc0.appr0.length>0&&e.push([{approvalType:"I",purposeOfInsertingIntoArray:"Just to ensure that 'I' type data is used, if it exists"}]);for(let r=0;r<e.length;r++){let i=[],a=n.keys(t["cc"+r]);for(let n=0;n<a.length;n++)for(let o=0;o<t["cc"+r][a[n]].length;o++){let s={name:t["cc"+r][a[n]][o].DELEGATE_NAME,oid:t["cc"+r][a[n]][o].DELEGATE_OID,level:n+1,designation:t["cc"+r][a[n]][o].DELEGATE_DESIGNATION_NAME,is_delegated:t["cc"+r][a[n]][o].IS_DELEGATED,role:t["cc"+r][a[n]][o].DELEGATE_ROLE_NAME};if(1==t["cc"+r][a[n]][o].IS_DELEGATED&&(s.delegated_by={name:t["cc"+r][a[n]][o].APPROVER_NAME,oid:t["cc"+r][a[n]][o].APPROVER_OID,level:n+1,designation:t["cc"+r][a[n]][o].APPROVER_DESIGNATION_NAME}),i.push(s),r==e.length-1&&n==a.length-1&&o==t["cc"+r][a[n]].length-1)return i}}}))}storeComments(e,t,r){return new Promise((i,n)=>{this.http.post("/api/wfPrimary/updateComments",{workflowHeaderId:e,newComments:t,commentor:r}).subscribe(e=>i(e),e=>(this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server Error while Updating Workflow Comments",e&&e.params?e.params:e&&e.error?e.error.params:{}),n(e)))})}updateWorkflowItems(e){return new Promise((t,r)=>{this.http.post("/api/wfPrimary/updateWorkflowItems",e).subscribe(e=>t(e),e=>(console.log(e),r(e)))})}formatApproversHierarchyForOpportunityApprovalActivity(e){return Object(i.c)(this,void 0,void 0,(function*(){for(let t=0;t<1;t++){let r=[],i=n.keys(e["cc"+t]);for(let n=0;n<i.length;n++)for(let a=0;a<e["cc"+t][i[n]].length;a++){let o={name:e["cc"+t][i[n]][a].DELEGATE_NAME,oid:e["cc"+t][i[n]][a].DELEGATE_OID,level:e["cc"+t][i[n]][a].APPROVAL_ORDER,designation:e["cc"+t][i[n]][a].DELEGATE_DESIGNATION_NAME,is_delegated:e["cc"+t][i[n]][a].IS_DELEGATED};if(1==e["cc"+t][i[n]][a].IS_DELEGATED&&(o.delegated_by={name:e["cc"+t][i[n]][a].APPROVER_NAME,oid:e["cc"+t][i[n]][a].APPROVER_OID,level:e["cc"+t][i[n]][a].APPROVAL_ORDER,designation:e["cc"+t][i[n]][a].APPROVER_DESIGNATION_NAME}),r.push(o),n==i.length-1&&a==e["cc"+t][i[n]].length-1)return r}}}))}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275inject"](o.c),a["\u0275\u0275inject"](s.a))},e.\u0275prov=a["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()}}]);