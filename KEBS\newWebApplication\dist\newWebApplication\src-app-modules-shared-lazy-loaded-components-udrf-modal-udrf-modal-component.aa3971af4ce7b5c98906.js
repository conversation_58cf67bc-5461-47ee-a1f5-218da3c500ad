(window.webpackJsonp=window.webpackJsonp||[]).push([[998,765,821,822,983,987,990,991],{UIsE:function(e,t,n){"use strict";n.r(t),n.d(t,"MONTH_YEAR_DATE_FORMAT",(function(){return Fe})),n.d(t,"UdrfModalComponent",(function(){return Me}));var i=n("mrSG"),r=n("fXoL"),o=n("5+WD"),l=n("wd/R"),a=n("1yaQ"),s=n("FKr1"),c=n("ofXK"),d=n("kmnG"),p=n("bTqV"),u=n("NFeN"),m=n("qFsG"),f=n("jaxi"),g=n("Qu3c"),h=n("bSwM"),v=n("iadO"),y=(n("mEBv"),n("3Pt+")),C=n("dlKe"),S=n("Xa2L"),x=(n("3beV"),n("1jcm")),b=n("0IaG"),E=n("LcQX"),w=n("nAV5"),F=n("JLuW"),M=n("GnQ3"),A=n("Yef5"),k=n("7a+r"),O=n("4lFs");let I=(()=>{class e{constructor(e,t,n){this.elementRef=e,this.overlay=t,this.overlayEventService=n,this.hostDisplay="none"}get componentRef(){return this.overlay.componentRefs[0]}get isTooltipDestroyed(){return this.componentRef&&this.componentRef.hostView.destroyed}close(){this.componentRef&&!this.isTooltipDestroyed&&this.overlayEventService.emitChangeEvent({type:"Hide"})}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](r.ElementRef),r["\u0275\u0275directiveInject"](k.a),r["\u0275\u0275directiveInject"](O.a))},e.\u0275dir=r["\u0275\u0275defineDirective"]({type:e,selectors:[["tooltip"]],hostVars:2,hostBindings:function(e,t){2&e&&r["\u0275\u0275styleProp"]("display",t.hostDisplay)},exportAs:["tooltip"]}),e})();var D=n("yArD");const P=["carouselView"],_=["createMyFilterPopUp"],T=["editMyFilterPopUp"],V=function(e){return{"btn-toggle-selected":e}};function R(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"mat-button-toggle",44),r["\u0275\u0275text"](1,"Sort"),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]();r["\u0275\u0275property"]("ngClass",r["\u0275\u0275pureFunction1"](1,V,"sort"==e.filtersSortToggle))}}function B(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",45),r["\u0275\u0275elementStart"](1,"span",46),r["\u0275\u0275text"](2,"Active My Filter :"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](3,"span",47),r["\u0275\u0275text"](4),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](3),r["\u0275\u0275propertyInterpolate2"]("matTooltip","",e.udrfService.udrfData.activeMyFilterName," - ",e.udrfService.udrfData.activeMyFilterDescription,""),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate"](e.udrfService.udrfData.activeMyFilterName)}}function N(e,t){1&e&&r["\u0275\u0275element"](0,"div",48)}function U(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"button",49),r["\u0275\u0275element"](1,"mat-spinner",50),r["\u0275\u0275elementEnd"]())}function L(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"mat-slide-toggle",51),r["\u0275\u0275listener"]("ngModelChange",(function(t){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"]().udrfService.udrfUiData.toggleChecked=t}))("change",(function(){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"]().callAllFilters()})),r["\u0275\u0275elementEnd"]()}if(2&e){const e=r["\u0275\u0275nextContext"]();r["\u0275\u0275property"]("ngModel",e.udrfService.udrfUiData.toggleChecked)}}function j(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"button",71),r["\u0275\u0275elementStart"](1,"mat-icon",72),r["\u0275\u0275text"](2,"offline_pin"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]())}function z(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"div",55),r["\u0275\u0275elementStart"](1,"div",56),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const n=t.$implicit;return r["\u0275\u0275nextContext"](2).makeMyFiltersArrayItemActive(n)})),r["\u0275\u0275template"](2,j,3,0,"button",57),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](3,"div",58),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const n=t.$implicit;return r["\u0275\u0275nextContext"](2).makeMyFiltersArrayItemActive(n)})),r["\u0275\u0275elementStart"](4,"span",59),r["\u0275\u0275text"](5),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](6,"div",60),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const n=t.$implicit;return r["\u0275\u0275nextContext"](2).makeMyFiltersArrayItemActive(n)})),r["\u0275\u0275elementStart"](7,"span",59),r["\u0275\u0275text"](8),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](9,"div",61),r["\u0275\u0275elementStart"](10,"button",62),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const n=t.$implicit;return r["\u0275\u0275nextContext"](2).activateInlineEdit("Consultant",n,"search-dropdown",[])})),r["\u0275\u0275elementStart"](11,"mat-icon",21),r["\u0275\u0275text"](12,"share"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](13,"button",63),r["\u0275\u0275elementStart"](14,"mat-icon",21),r["\u0275\u0275text"](15,"edit"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](16,"button",64),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const n=t.$implicit,i=t.index;return r["\u0275\u0275nextContext"](2).deleteMyFiltersArrayItem(n,i)})),r["\u0275\u0275elementStart"](17,"mat-icon",21),r["\u0275\u0275text"](18,"delete"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](19,"button",65),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const n=t.$implicit;return r["\u0275\u0275nextContext"](2).makeMyFiltersArrayItemActive(n)})),r["\u0275\u0275elementStart"](20,"mat-icon"),r["\u0275\u0275text"](21,"done_all"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](22,"tooltip",null,66),r["\u0275\u0275elementStart"](24,"div",67),r["\u0275\u0275text"](25,"Edit My Filter"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](26,"div",68),r["\u0275\u0275elementStart"](27,"span"),r["\u0275\u0275text"](28,"Name \xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0:"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](29,"mat-form-field",69),r["\u0275\u0275elementStart"](30,"input",28),r["\u0275\u0275listener"]("ngModelChange",(function(e){return t.$implicit.myFilterEditName=e})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](31,"div",68),r["\u0275\u0275elementStart"](32,"span"),r["\u0275\u0275text"](33,"Description :"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](34,"mat-form-field",69),r["\u0275\u0275elementStart"](35,"input",28),r["\u0275\u0275listener"]("ngModelChange",(function(e){return t.$implicit.myFilterEditDescription=e})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](36,"div",70),r["\u0275\u0275elementStart"](37,"button",30),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const n=t.$implicit;return r["\u0275\u0275nextContext"](2).editMyFiltersArrayItem(n)})),r["\u0275\u0275text"](38,"Save"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](39,"button",31),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const n=t.$implicit;return r["\u0275\u0275nextContext"](2).closeEditMyFilterPopUp(n)})),r["\u0275\u0275text"](40,"Close"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=r["\u0275\u0275reference"](23);r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngIf",e.isMyFilterActive),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("matTooltip",e.myFilterName),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"]("",e.myFilterName," "),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("matTooltip",e.myFilterDescription),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate"](e.myFilterDescription),r["\u0275\u0275advance"](5),r["\u0275\u0275property"]("tooltipTriggerFor",n)("trigger","click"),r["\u0275\u0275advance"](17),r["\u0275\u0275property"]("ngModel",e.myFilterEditName),r["\u0275\u0275advance"](5),r["\u0275\u0275property"]("ngModel",e.myFilterEditDescription)}}function q(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div",73),r["\u0275\u0275elementStart"](1,"div"),r["\u0275\u0275elementStart"](2,"h4",74),r["\u0275\u0275text"](3," Oops ! My Filters Not Found ! "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](4,"div",75),r["\u0275\u0275element"](5,"img",76),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]())}function $(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",52),r["\u0275\u0275template"](1,z,41,9,"div",53),r["\u0275\u0275template"](2,q,6,0,"div",54),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngForOf",e.udrfService.udrfData.myFiltersArray),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",0==e.udrfService.udrfData.myFiltersArray.length)}}function W(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"mat-checkbox",94),r["\u0275\u0275listener"]("change",(function(t){r["\u0275\u0275restoreView"](e);const n=r["\u0275\u0275nextContext"]().$implicit;return r["\u0275\u0275nextContext"](2).udrfService.udrfFunctions.selectAllFilterItemCheckbox(t.checked,n)})),r["\u0275\u0275elementEnd"]()}if(2&e){const e=r["\u0275\u0275nextContext"]().$implicit,t=r["\u0275\u0275nextContext"](2);r["\u0275\u0275property"]("ngClass",e.hasHierarchyWithinCheckbox?"pr-0":"pr-1")("checked",t.udrfService.udrfFunctions.areAllFilterItemCheckboxesSelected(e))("disabled",e.selectAllLoading)}}function G(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"button",95),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const t=r["\u0275\u0275nextContext"]().$implicit;return r["\u0275\u0275nextContext"](2).udrfService.udrfFunctions.expandAllCheckboxValues(t)})),r["\u0275\u0275elementStart"](1,"mat-icon",96),r["\u0275\u0275text"](2,"keyboard_arrow_right"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&e){const e=r["\u0275\u0275nextContext"]().$implicit;r["\u0275\u0275property"]("disabled",e.selectAllLoading)}}function Y(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"button",95),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const t=r["\u0275\u0275nextContext"]().$implicit;return r["\u0275\u0275nextContext"](2).udrfService.udrfFunctions.collapseAllCheckboxValues(t)})),r["\u0275\u0275elementStart"](1,"mat-icon",97),r["\u0275\u0275text"](2,"keyboard_arrow_down"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&e){const e=r["\u0275\u0275nextContext"]().$implicit;r["\u0275\u0275property"]("disabled",e.selectAllLoading)}}function H(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",98),r["\u0275\u0275text"](1),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]().$implicit;r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"](" ",e.filterName," ")}}function K(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"button",102),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const t=r["\u0275\u0275nextContext"](2).$implicit;return r["\u0275\u0275nextContext"](2).udrfService.udrfFunctions.clearFilter(t)})),r["\u0275\u0275elementStart"](1,"mat-icon"),r["\u0275\u0275text"](2,"close"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}}function Q(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"mat-form-field",99),r["\u0275\u0275elementStart"](1,"input",100),r["\u0275\u0275listener"]("input",(function(t){r["\u0275\u0275restoreView"](e);const n=r["\u0275\u0275nextContext"]().$implicit;return r["\u0275\u0275nextContext"](2).udrfService.udrfFunctions.searchFilter(n,t.target.value)}))("ngModelChange",(function(t){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"]().$implicit.searchParameter=t})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](2,K,3,0,"button",101),r["\u0275\u0275elementEnd"]()}if(2&e){const e=r["\u0275\u0275nextContext"]().$implicit;r["\u0275\u0275property"]("ngClass",e.hasHierarchyWithinCheckbox?"col-7":"col-8"),r["\u0275\u0275advance"](1),r["\u0275\u0275propertyInterpolate1"]("placeholder","Search ",e.filterName,""),r["\u0275\u0275property"]("ngModel",e.searchParameter),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",""!=e.searchParameter)}}function X(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"mat-icon",103),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const t=r["\u0275\u0275nextContext"]().$implicit;return r["\u0275\u0275nextContext"](2).udrfService.udrfFunctions.toggleSearchBar(t)})),r["\u0275\u0275text"](1,"search"),r["\u0275\u0275elementEnd"]()}if(2&e){const e=r["\u0275\u0275nextContext"]().$implicit;r["\u0275\u0275property"]("matTooltip","Search "+e.filterName)}}function J(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"mat-icon",104),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const t=r["\u0275\u0275nextContext"]().$implicit;return r["\u0275\u0275nextContext"](2).udrfService.udrfFunctions.toggleSearchBar(t)})),r["\u0275\u0275text"](1,"cancel"),r["\u0275\u0275elementEnd"]()}}function Z(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"button",109),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const t=r["\u0275\u0275nextContext"]().$implicit,n=r["\u0275\u0275nextContext"]().$implicit;return r["\u0275\u0275nextContext"](2).udrfService.udrfFunctions.expandCheckboxValues(n,t,!0)})),r["\u0275\u0275elementStart"](1,"mat-icon",110),r["\u0275\u0275text"](2,"keyboard_arrow_right"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&e){const e=r["\u0275\u0275nextContext"]().$implicit;r["\u0275\u0275property"]("ngClass",e.isCheckboxSelected?"value13Red":"value13")}}function ee(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"button",109),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const t=r["\u0275\u0275nextContext"]().$implicit,n=r["\u0275\u0275nextContext"]().$implicit;return r["\u0275\u0275nextContext"](2).udrfService.udrfFunctions.expandCheckboxValues(n,t,!0)})),r["\u0275\u0275elementStart"](1,"mat-icon",111),r["\u0275\u0275text"](2,"keyboard_arrow_down"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&e){const e=r["\u0275\u0275nextContext"]().$implicit;r["\u0275\u0275property"]("ngClass",e.isCheckboxSelected?"value13Red":"value13")}}function te(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"span",112),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const t=r["\u0275\u0275nextContext"]().$implicit,n=r["\u0275\u0275nextContext"]().$implicit;return r["\u0275\u0275nextContext"](2).udrfService.udrfFunctions.selectFilterItemCheckbox(n,t)})),r["\u0275\u0275text"](1),r["\u0275\u0275elementEnd"]()}if(2&e){const e=r["\u0275\u0275nextContext"]().$implicit;r["\u0275\u0275property"]("ngClass",(e.isCheckboxSelected?"value13Red ":"value13 ")+(e.hasCheckboxValues?"ml-1":"ml-2"))("matTooltip",e.checkboxName),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate"]("("+e.checkboxCount+") "+e.checkboxName)}}function ne(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"span",112),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const t=r["\u0275\u0275nextContext"]().$implicit,n=r["\u0275\u0275nextContext"]().$implicit;return r["\u0275\u0275nextContext"](2).udrfService.udrfFunctions.selectFilterItemCheckbox(n,t)})),r["\u0275\u0275text"](1),r["\u0275\u0275elementEnd"]()}if(2&e){const e=r["\u0275\u0275nextContext"]().$implicit;r["\u0275\u0275property"]("ngClass",(e.isCheckboxSelected?"value13Red ":"value13 ")+(e.hasCheckboxValues?"ml-1":"ml-2"))("matTooltip",e.checkboxName),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate"](e.checkboxName)}}function ie(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"div",105),r["\u0275\u0275elementStart"](1,"mat-checkbox",106),r["\u0275\u0275listener"]("change",(function(){r["\u0275\u0275restoreView"](e);const n=t.$implicit,i=r["\u0275\u0275nextContext"]().$implicit;return r["\u0275\u0275nextContext"](2).udrfService.udrfFunctions.selectFilterItemCheckbox(i,n)})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](2,Z,3,1,"button",107),r["\u0275\u0275template"](3,ee,3,1,"button",107),r["\u0275\u0275template"](4,te,2,3,"span",108),r["\u0275\u0275template"](5,ne,2,3,"span",108),r["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=r["\u0275\u0275nextContext"]().$implicit,i=r["\u0275\u0275nextContext"](2);r["\u0275\u0275property"]("hidden",n.selectAllLoading),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("checked",e.isCheckboxSelected)("ngClass","ml-"+e.checkboxLevel)("disabled",i.udrfService.udrfFunctions.isCheckboxForFilterWithCustomButtonDisabled(e)),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",n.hasHierarchyWithinCheckbox&&e.hasCheckboxValues&&!e.isCheckboxExpanded),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",n.hasHierarchyWithinCheckbox&&e.hasCheckboxValues&&e.isCheckboxExpanded),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",i.udrfService.udrfUiData.countForOnlyThisReport&&i.udrfService.udrfUiData.toggleChecked),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",!i.udrfService.udrfUiData.countForOnlyThisReport&&!i.udrfService.udrfUiData.toggleChecked||i.udrfService.udrfUiData.countForOnlyThisReport&&!i.udrfService.udrfUiData.toggleChecked)}}function re(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div",113),r["\u0275\u0275elementStart"](1,"div",114),r["\u0275\u0275element"](2,"mat-spinner",115),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]())}function oe(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"div",119),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const t=r["\u0275\u0275nextContext"](2).$implicit;return r["\u0275\u0275nextContext"](2).udrfService.udrfFunctions.clickFilterCustomButton(t,!0)})),r["\u0275\u0275text"](1,"Custom "),r["\u0275\u0275elementEnd"]()}}function le(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"div",120),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const t=r["\u0275\u0275nextContext"](2).$implicit;return r["\u0275\u0275nextContext"](2).udrfService.udrfFunctions.clickFilterCustomButton(t,!1)})),r["\u0275\u0275text"](1,"Custom"),r["\u0275\u0275elementEnd"]()}}function ae(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",116),r["\u0275\u0275template"](1,oe,2,0,"div",117),r["\u0275\u0275template"](2,le,2,0,"div",118),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]().$implicit;r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",!(e.isCustomButtonActivated||null!=e&&e.IsToDisableCustomRangeButton&&0!=(null==e?null:e.IsToDisableCustomRangeButton))),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",e.isCustomButtonActivated&&(!(null!=e&&e.IsToDisableCustomRangeButton)||0==(null==e?null:e.IsToDisableCustomRangeButton)))}}function se(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"div",82),r["\u0275\u0275elementStart"](1,"div",83),r["\u0275\u0275template"](2,W,1,3,"mat-checkbox",84),r["\u0275\u0275template"](3,G,3,1,"button",85),r["\u0275\u0275template"](4,Y,3,1,"button",85),r["\u0275\u0275template"](5,H,2,1,"div",86),r["\u0275\u0275template"](6,Q,3,4,"mat-form-field",87),r["\u0275\u0275template"](7,X,2,1,"mat-icon",88),r["\u0275\u0275template"](8,J,2,0,"mat-icon",89),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](9,"div",90),r["\u0275\u0275listener"]("scrolled",(function(){r["\u0275\u0275restoreView"](e);const n=t.$implicit;return r["\u0275\u0275nextContext"](2).udrfService.udrfFunctions.onFilterScrollDown(n,!1)})),r["\u0275\u0275template"](10,ie,6,8,"div",91),r["\u0275\u0275template"](11,re,3,0,"div",92),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](12,ae,3,2,"div",93),r["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=r["\u0275\u0275nextContext"](2);r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngClass",e.isSearchBoxVisible?"":"mb-2"),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",!e.hasCustomRangeButton&&!e.onlyOneLevelSelectable),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",e.hasHierarchyWithinCheckbox&&!e.areAllHierarchyCbsExpanded),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",e.hasHierarchyWithinCheckbox&&e.areAllHierarchyCbsExpanded),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",!e.isSearchBoxVisible),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",e.isSearchBoxVisible&&!e.selectAllLoading),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",e.isSearchAllowed&&!e.isSearchBoxVisible&&!e.selectAllLoading),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",e.isSearchAllowed&&e.isSearchBoxVisible&&!e.selectAllLoading),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("infiniteScrollDistance",2)("scrollWindow",!1),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngForOf",n.udrfService.udrfFunctions.retrieveNecessaryCheckboxes(e)),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",e.isLazyLoadingHappening||e.selectAllLoading),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",e.hasCustomRangeButton)}}function ce(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",77),r["\u0275\u0275elementStart"](1,"div",78),r["\u0275\u0275elementStart"](2,"div",79,80),r["\u0275\u0275template"](4,se,13,13,"div",81),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](4),r["\u0275\u0275property"]("ngForOf",e.udrfService.udrfData.availableFilterTypeArray)}}function de(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"mat-icon",129),r["\u0275\u0275text"](1,"maximize "),r["\u0275\u0275elementEnd"]())}function pe(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"mat-icon",130),r["\u0275\u0275text"](1,"arrow_upward"),r["\u0275\u0275elementEnd"]())}function ue(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"mat-icon",131),r["\u0275\u0275text"](1,"arrow_downward"),r["\u0275\u0275elementEnd"]())}function me(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"div",123),r["\u0275\u0275elementStart"](1,"button",124),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const n=t.$implicit;return r["\u0275\u0275nextContext"](2).udrfService.udrfFunctions.sortBy(n)})),r["\u0275\u0275template"](2,de,2,0,"mat-icon",125),r["\u0275\u0275template"](3,pe,2,0,"mat-icon",126),r["\u0275\u0275template"](4,ue,2,0,"mat-icon",127),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](5,"span",128),r["\u0275\u0275text"](6),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("disabled",e.isSortDisabled),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","I"==e.currentSortOrder),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","A"==e.currentSortOrder),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","D"==e.currentSortOrder),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate"](e.filterName)}}function fe(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",121),r["\u0275\u0275template"](1,me,7,5,"div",122),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngForOf",e.udrfService.udrfData.sortTabFilterArray)}}function ge(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div",132),r["\u0275\u0275elementStart"](1,"div",133),r["\u0275\u0275elementStart"](2,"span",134),r["\u0275\u0275text"](3,"Applied Filters"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](4,"span",135),r["\u0275\u0275text"](5,"(You may drag and rearrange these filters as appropriate)"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]())}function he(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"div",138),r["\u0275\u0275elementStart"](1,"mat-checkbox",139),r["\u0275\u0275listener"]("change",(function(){r["\u0275\u0275restoreView"](e);const n=t.$implicit;return r["\u0275\u0275nextContext"](2).udrfService.udrfFunctions.selectAvailableFilterItemCheckbox(n)}))("cdkDragEntered",(function(t){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"](2).dragAndDropAvailableFilters(t)})),r["\u0275\u0275elementStart"](2,"span",140),r["\u0275\u0275text"](3),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=t.index;r["\u0275\u0275property"]("cdkDropListData",n),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngClass",e.isFilterAvailable?"value13Red":"value13")("checked",e.isFilterAvailable)("cdkDragData",n),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate"](e.filterName)}}function ve(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",136),r["\u0275\u0275template"](1,he,4,5,"div",137),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngForOf",e.udrfService.udrfData.filterTypeArray)}}function ye(e,t){1&e&&r["\u0275\u0275element"](0,"div",141)}function Ce(e,t){1&e&&r["\u0275\u0275element"](0,"div",142)}function Se(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"div",143),r["\u0275\u0275elementStart"](1,"div",144),r["\u0275\u0275text"](2,"Custom Duration"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](3,"mat-form-field",145),r["\u0275\u0275elementStart"](4,"mat-label"),r["\u0275\u0275text"](5,"Start Date"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](6,"input",146),r["\u0275\u0275listener"]("ngModelChange",(function(t){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"]().udrfService.udrfData.overallCustomButtonValueStart=t}))("ngModelChange",(function(){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"]().udrfService.udrfFunctions.changeOverallCustomRangeValue(!0)})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275element"](7,"mat-datepicker-toggle",147),r["\u0275\u0275element"](8,"mat-datepicker",null,148),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](10,"mat-form-field",149),r["\u0275\u0275elementStart"](11,"mat-label"),r["\u0275\u0275text"](12,"End Date"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](13,"input",150),r["\u0275\u0275listener"]("ngModelChange",(function(t){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"]().udrfService.udrfData.overallCustomButtonValueEnd=t}))("ngModelChange",(function(){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"]().udrfService.udrfFunctions.changeOverallCustomRangeValue(!1)})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275element"](14,"mat-datepicker-toggle",147),r["\u0275\u0275element"](15,"mat-datepicker",null,151),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&e){const e=r["\u0275\u0275reference"](9),t=r["\u0275\u0275reference"](16),n=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](6),r["\u0275\u0275property"]("matDatepicker",e)("max",n.getEndDate())("ngModel",n.udrfService.udrfData.overallCustomButtonValueStart),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("for",e),r["\u0275\u0275advance"](6),r["\u0275\u0275property"]("matDatepicker",t)("min",n.getStartDate())("ngModel",n.udrfService.udrfData.overallCustomButtonValueEnd),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("for",t)}}function xe(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"div",143),r["\u0275\u0275elementStart"](1,"div",144),r["\u0275\u0275text"](2,"Custom Range"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](3,"mat-form-field",152),r["\u0275\u0275elementStart"](4,"input",153),r["\u0275\u0275listener"]("ngModelChange",(function(t){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"]().udrfService.udrfData.overallCustomButtonValueStart=t}))("ngModelChange",(function(){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"]().udrfService.udrfFunctions.changeOverallCustomRangeValue(!0)})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](5,"mat-form-field",152),r["\u0275\u0275elementStart"](6,"input",154),r["\u0275\u0275listener"]("ngModelChange",(function(t){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"]().udrfService.udrfData.overallCustomButtonValueEnd=t}))("ngModelChange",(function(){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"]().udrfService.udrfFunctions.changeOverallCustomRangeValue(!1)})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&e){const e=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](4),r["\u0275\u0275property"]("ngModel",e.udrfService.udrfData.overallCustomButtonValueStart),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngModel",e.udrfService.udrfData.overallCustomButtonValueEnd)}}const be=function(e){return{iconButton:e}},Ee=function(e,t){return{iconButton:e,searchIsActive:t}},we=function(e){return{searchIsActive:e}},Fe={parse:{dateInput:"MMMM YYYY"},display:{dateInput:"DD - MM - YYYY",monthYearLabel:"MMMM YYYY",dateA11yLabel:"LL",monthYearA11yLabel:"MMMM YYYY"}};let Me=(()=>{class e{constructor(e,t,n,i,r){this.dialogRef=e,this.utilityService=t,this.inlineEditPopupService=n,this.sharedLazyLoadedComponentsService=i,this.udrfService=r,this.carouselViewScrollWidth=240,this.createMyFilterPopUpInput={name:"",description:""},this.filtersSortToggle="filters",this.inlineEditField="",this.editType="",this.dataArray=[],this.hasClearConfigBeenClicked=!1,this.hasClearConfigBeenClicked=!1}dragAndDropAvailableFilters(e){return Object(i.c)(this,void 0,void 0,(function*(){Object(o.h)(this.udrfService.udrfData.filterTypeArray,e.item.data,e.container.data);let t=1;for(let e of this.udrfService.udrfData.filterTypeArray)e.filterOrder=t++;yield this.udrfService.udrfFunctions.determineAppliedFilters(!1),this.udrfService.udrfFunctions.determineAreFiltersApplied()}))}ngOnInit(){this.hasClearConfigBeenClicked=!1,this.udrfService.udrfData.isCustomizeFiltersActive=!1,this.udrfService.udrfFunctions.getCheckBoxList(),this.udrfService.udrfFunctions.determineAvailableFilters()}previousFilters(){this.carouselView.nativeElement.scrollTo({left:this.carouselView.nativeElement.scrollLeft-this.carouselViewScrollWidth,behavior:"smooth"})}nextFilters(){this.carouselView.nativeElement.scrollTo({left:this.carouselView.nativeElement.scrollLeft+this.carouselViewScrollWidth,behavior:"smooth"})}applyConfig(){this.udrfService.udrfFunctions.applyConfig(!0),this.closeFiltersAndSort(),this.utilityService.showToastMessage("The filters have been applied!")}getEndDate(){return this.udrfService.udrfData.overallCustomButtonValueEnd}getStartDate(){return this.udrfService.udrfData.overallCustomButtonValueStart}clearConfig(){this.udrfService.udrfFunctions.clearConfig(),this.hasClearConfigBeenClicked=!0,this.utilityService.showToastMessage("The filters have been cleared!")}createMyFilter(){return Object(i.c)(this,void 0,void 0,(function*(){let e=this.createMyFilterPopUpInput.name,t=this.createMyFilterPopUpInput.description;if(""!=e){for(let e of this.udrfService.udrfData.myFiltersArray)e.isMyFilterActive=!1;let n={isMyFilterActive:!0,myFilterName:e,myFilterDescription:t,myFilterEditName:e,myFilterEditDescription:t,myFilterArray:this.udrfService.udrfData.filterTypeArray,udrfSummaryCardCodes:this.udrfService.udrfData.udrfSummaryCardCodes};this.udrfService.udrfData.myFiltersArray.push(n),yield this.udrfService.udrfFunctions.setMyActiveFilter(n,!1),yield this.udrfService.udrfFunctions.updateUdrfUserConfigFunction(this.udrfService.udrfData,!0),this.utilityService.showToastMessage("Your filter "+e+" has been created!"),this.createMyFilterPopUpInput.name="",this.createMyFilterPopUpInput.description="",this.closeCreateMyFilterPopUp()}else this.utilityService.showToastMessage("Kindly enter at least a name for your filter!")}))}editMyFiltersArrayItem(e){return Object(i.c)(this,void 0,void 0,(function*(){e.myFilterName=e.myFilterEditName,e.myFilterDescription=e.myFilterEditDescription;let t={isMyFilterActive:!0,myFilterName:e.myFilterEditName,myFilterDescription:e.myFilterEditDescription,myFilterArray:e.myFilterArray,udrfSummaryCardCodes:e.udrfSummaryCardCodes};e.isMyFilterActive&&(yield this.udrfService.udrfFunctions.setMyActiveFilter(t,!1)),yield this.udrfService.udrfFunctions.updateUdrfUserConfigFunction(this.udrfService.udrfData,!0),this.utilityService.showToastMessage("Your filter "+e.myFilterEditName+" has been edited!"),this.editMyFilterPopUp.close()}))}deleteMyFiltersArrayItem(e,t){this.utilityService.openConfirmationSweetAlertWithCustom("Are you sure you want to delete?","Once you confirm, your saved filter will be deleted!").then(n=>Object(i.c)(this,void 0,void 0,(function*(){if(n){let n={isMyFilterActive:!1,myFilterName:"",myFilterDescription:"",myFilterArray:this.udrfService.udrfData.filterTypeArray,udrfSummaryCardCodes:this.udrfService.udrfData.udrfSummaryCardCodes};e.isMyFilterActive&&(yield this.udrfService.udrfFunctions.setMyActiveFilter(n,!1));let i=[];for(let e=0;e<this.udrfService.udrfData.myFiltersArray.length;e++)e!=t&&i.push(this.udrfService.udrfData.myFiltersArray[e]);this.udrfService.udrfData.myFiltersArray=i,yield this.udrfService.udrfFunctions.updateUdrfUserConfigFunction(this.udrfService.udrfData,!0),this.utilityService.showToastMessage("Your filter has been deleted!")}})))}activateInlineEdit(e,t,n,i){(!this.inlineEditPopupService.inlineEditCallbackSubscription||this.inlineEditPopupService.inlineEditCallbackSubscription&&this.inlineEditPopupService.inlineEditCallbackSubscription.closed)&&(this.inlineEditPopupService.inlineEditCallbackSubscription=this.inlineEditPopupService.inlineEditCallback.subscribe(e=>{!e||0===Object.keys(e).length&&e.constructor===Object||this.inlineEditResponseFunction(e)})),this.inlineEditField=e,this.inlineEditActiveRow=t,this.editType=n,"search-dropdown"==this.editType&&(this.dropdownConfig={inlineEditField:this.inlineEditField,dropdownSelectedValue:"",apiServiceVariable:this.sharedLazyLoadedComponentsService,apiFunctionName:"searchConsultants",apiDataUpdateKeyName:"oid",apiDataSelectedKeyName:"name",apiDataAdditionalKeyName:"email",hasImageView:!0,apiDataImageKeyName:"oid",maxWidth:"250px"}),this.dataArray=i,this.inlineEditPopupService.setInlineEditActiveDataSubject({editType:this.editType,dataArray:this.dataArray,dropdownConfig:this.dropdownConfig})}inlineEditResponseFunction(e){e[this.dropdownConfig.apiDataUpdateKeyName]!=this.udrfService.udrfData.currentUserOId?this.utilityService.openConfirmationSweetAlertWithCustom("Are you sure you want to share?","Once you confirm, this configuration will be appended to their My Filters!").then(t=>Object(i.c)(this,void 0,void 0,(function*(){if(t){let t={currentUserOId:e[this.dropdownConfig.apiDataUpdateKeyName],applicationId:this.udrfService.udrfData.applicationId};this.udrfService.getUdrfUserConfig(t).subscribe(n=>Object(i.c)(this,void 0,void 0,(function*(){let r=0,o={};"S"==n.messType&&n.data.length>0&&(r=n.data[0].id?n.data[0].id:0,o=n.data[0].applied_config?JSON.parse(n.data[0].applied_config):{});let a=JSON.parse(JSON.stringify(this.inlineEditActiveRow));a.myFilterDescription+=(""==a.myFilterDescription?"":" - ")+this.udrfService.udrfData.currentUserFullName+"'s Filter",a.myFilterEditDescription=a.myFilterDescription,a.isMyFilterActive=!1,a.myFilterArray=this.udrfService.udrfFunctions.reconfigureFilterTypeArray(a.myFilterArray),o.areToolbarFiltersApplied||0==o.areToolbarFiltersApplied?(o.myFiltersArray||(o.myFiltersArray=[]),o.myFiltersArray.push(a)):o={activeMyFilterDescription:"",activeMyFilterName:"",areToolbarFiltersApplied:!1,isMyFiltersActive:!1,myFiltersArray:[a]},t.userConfigId=r,t.reportName=this.udrfService.udrfData.reportName,t.appliedConfig=o;let s=this.udrfService.udrfData.currentUserFullName+" has shared their "+this.udrfService.udrfData.reportName+" filter configuration with you!";t.myFilterNotifyUserArray=[],t.myFilterNotifyUserArray.push([5,0,e[this.dropdownConfig.apiDataAdditionalKeyName],e[this.dropdownConfig.apiDataUpdateKeyName],s,"updated",null,l().format("YYYY-MM-DD HH:mm:ss")]),this.udrfService.updateUdrfUserConfig(t).subscribe(e=>Object(i.c)(this,void 0,void 0,(function*(){this.utilityService.showToastMessage("Your filter has been shared!")})),e=>{this.utilityService.showErrorMessage(e,"KEBS")})})),e=>{this.utilityService.showErrorMessage(e,"KEBS")})}}))):this.utilityService.showToastMessage("My Filters cannot be shared with self!")}saveMyFilter(){this.utilityService.showToastMessage("Your filter has been saved!"),this.udrfService.udrfFunctions.saveMyFilter()}resetFilters(){return Object(i.c)(this,void 0,void 0,(function*(){yield this.udrfService.udrfFunctions.resetUserUdrfConfig(this.udrfService.udrfData)}))}makeMyFiltersArrayItemActive(e){this.udrfService.udrfFunctions.makeMyFiltersArrayItemActive(e,!0),this.closeFiltersAndSort()}customizeFilters(){this.udrfService.udrfData.isCustomizeFiltersActive=!this.udrfService.udrfData.isCustomizeFiltersActive,this.filtersSortToggle=this.udrfService.udrfData.isCustomizeFiltersActive?"":"filters"}switchFiltersSortToggle(e){this.udrfService.udrfData.isCustomizeFiltersActive=!1,this.filtersSortToggle=e.value}closeEditMyFilterPopUp(e){e.myFilterEditName=e.myFilterName,e.myFilterEditDescription=e.myFilterDescription,e.isMyFilterActive&&this.udrfService.udrfFunctions.setMyActiveFilter(e,!1),this.editMyFilterPopUp.close()}showErrorMessage(e){this.utilityService.showErrorMessage(e,"KEBS")}closeCreateMyFilterPopUp(){this.createMyFilterPopUp.close()}closeFiltersAndSort(){this.hasClearConfigBeenClicked&&this.udrfService.udrfFunctions.applyConfig(!0),this.dialogRef.close()}callAllFilters(){this.udrfService.udrfUiData.toggleChecked&&this.udrfService.udrfUiData.getCountData()}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](b.h),r["\u0275\u0275directiveInject"](E.a),r["\u0275\u0275directiveInject"](w.a),r["\u0275\u0275directiveInject"](F.a),r["\u0275\u0275directiveInject"](M.a))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["udrf-modal"]],viewQuery:function(e,t){if(1&e&&(r["\u0275\u0275viewQuery"](P,!0,r.ElementRef),r["\u0275\u0275viewQuery"](_,!0),r["\u0275\u0275viewQuery"](T,!0)),2&e){let e;r["\u0275\u0275queryRefresh"](e=r["\u0275\u0275loadQuery"]())&&(t.carouselView=e.first),r["\u0275\u0275queryRefresh"](e=r["\u0275\u0275loadQuery"]())&&(t.createMyFilterPopUp=e.first),r["\u0275\u0275queryRefresh"](e=r["\u0275\u0275loadQuery"]())&&(t.editMyFilterPopUp=e.first)}},features:[r["\u0275\u0275ProvidersFeature"]([{provide:s.c,useClass:a.c,deps:[s.f,a.a]},{provide:s.e,useValue:Fe}])],decls:72,vars:48,consts:[[1,"container-fluid","udrf-modal-styles","d-flex","flex-column","slide-top","p-0"],[1,"col-11-95","row","pt-3","pb-2"],[1,"col-4","pl-4","mt-1"],["name","fontStyle","aria-label","Font Style",3,"value","change"],["group","matButtonToggleGroup"],["value","filters",2,"padding","0px 7px !important",3,"ngClass"],["value","sort","style","padding: 0px 13px !important",3,"ngClass",4,"ngIf"],["value","my_filters",2,"padding","0px 4px !important",3,"ngClass"],["class","col-4  d-flex",4,"ngIf"],["class","col-4 pl-0 pr-0",4,"ngIf"],[1,"col-4","ta-c","pl-0","pr-0"],["mat-flat-button","",4,"ngIf"],["matTooltip","Show count of records per filter selection","color","primary",3,"ngModel","ngModelChange","change",4,"ngIf"],["mat-icon-button","","matTooltip","Previous Filters",3,"disabled","click"],[3,"ngClass"],["mat-icon-button","","matTooltip","Next Filters",3,"disabled","click"],["mat-icon-button","","matTooltip","Save My Filter",3,"disabled","click"],["mat-icon-button","","matTooltip","Create My Filter",3,"disabled","tooltipTriggerFor","trigger"],["mat-icon-button","","matTooltip","Customize Filters",3,"click"],[1,"iconButton",3,"ngClass"],["mat-icon-button","","matTooltip","Reset Filters",3,"click"],[1,"iconButton"],["mat-icon-button","","matTooltip","Close",3,"click"],["createMyFilterPopUp","tooltip"],[1,"popover-title","pt-2"],[1,"popover-content","ml-0","mr-0"],[1,"col-3","pl-0","pr-0"],[1,"col-8","pl-0","pr-0","ml-2","example-form-field"],["matInput","","type","text","autocomplete","off",3,"ngModel","ngModelChange"],[1,"popover-actions","pb-2"],[1,"popover-actions-accept-btn",3,"click"],[1,"popover-actions-dismiss-btn",3,"click"],["class","col-11-95 pl-3 scrollColumnAlign360",4,"ngIf"],["class","col-11-95 row p-0 pt-1 d-flex",4,"ngIf"],["class","col-11-95 autoColumnAlign370 pl-3",4,"ngIf"],["class","col-11-95 row pl-4 pb-2",4,"ngIf"],["class","col-11-95 pl-4 autoColumnAlign350","cdkDropListGroup","",4,"ngIf"],["class","col-10",4,"ngIf"],["class","col-4",4,"ngIf"],["class","col-6 divAlignItemCenter",4,"ngIf"],[1,"col-2"],["matTooltip","Apply Filters","mat-mini-fab","",1,"mr-4",3,"disabled","ngClass","click"],[2,"font-size","22px !important"],["matTooltip","Clear Filters","mat-mini-fab","",3,"ngClass","click"],["value","sort",2,"padding","0px 13px !important",3,"ngClass"],[1,"col-4","d-flex"],[1,"title","p-0","value14RedBold","my-auto"],[1,"title","p-0","ml-2","value14","my-auto",3,"matTooltip"],[1,"col-4","pl-0","pr-0"],["mat-flat-button",""],["diameter","16","matTooltip","Loading ..."],["matTooltip","Show count of records per filter selection","color","primary",3,"ngModel","ngModelChange","change"],[1,"col-11-95","pl-3","scrollColumnAlign360"],["class","row listcard cp","style","border-bottom: solid 1px #cacaca",4,"ngFor","ngForOf"],["style","text-align: center;","class","pt-4",4,"ngIf"],[1,"row","listcard","cp",2,"border-bottom","solid 1px #cacaca"],[1,"col-1","pl-0","pr-0","searchRow",3,"click"],["mat-icon-button","","matTooltip","Active My Filter",4,"ngIf"],[1,"col-4","pl-0","pr-0","d-flex",3,"click"],[1,"value13","my-auto",3,"matTooltip"],[1,"col-5","d-flex","pl-0","pr-0",3,"click"],[1,"col-2","pl-0","pr-0","d-flex","h-40"],["inlineEdit","","mat-icon-button","","matTooltip","Share",1,"hide-on-hover","slide-top",3,"click"],["mat-icon-button","","matTooltip","Edit",1,"hide-on-hover","slide-top",3,"tooltipTriggerFor","trigger"],["mat-icon-button","","matTooltip","Delete",1,"hide-on-hover","mr-1","slide-top",3,"click"],["mat-mini-fab","","matTooltip","Make Active and Apply",1,"mini-tick-enabled-small","hide-on-hover","slide-top",3,"click"],["editMyFilterPopUp","tooltip"],[1,"popover-title"],[1,"popover-content"],[1,"ml-4","example-form-field"],[1,"popover-actions"],["mat-icon-button","","matTooltip","Active My Filter"],[2,"color","#009432 !important","font-size","21px !important"],[1,"pt-4",2,"text-align","center"],[1,"d-flex","justify-content-center","align-items-center","mt-0","mb-0","slide-in-top"],[1,"d-flex","justify-content-center","align-items-center","slide-from-down"],["src","assets/images/nomilestone.png","height","220","width","250",1,"mt-2"],[1,"col-11-95","row","p-0","pt-1","d-flex"],[1,"col-12","pl-0","pr-0","d-flex","carousel-row"],[1,"d-flex","carousel-div"],["carouselView",""],["class","carousel-item-div pr-2",4,"ngFor","ngForOf"],[1,"carousel-item-div","pr-2"],[1,"row","col-12","ml-1","pl-0",3,"ngClass"],["class","pl-1 pt-04","matTooltip","Select All",3,"ngClass","checked","disabled","change",4,"ngIf"],["mat-icon-button","","class","mt-1 arrow-in-sidnav value13",3,"disabled","click",4,"ngIf"],["class","title col-5x p-1 m-0 value13Bold",4,"ngIf"],["class","pl-0 pr-0 example-form-field",3,"ngClass",4,"ngIf"],["class","col-2 searchRow p-1 searchButton","style","font-size: 18px !important;",3,"matTooltip","click",4,"ngIf"],["class","col-2 searchRow p-1 searchButton searchIsActive","style","font-size: 18px !important;","matTooltip","Close Search",3,"click",4,"ngIf"],["infinite-scroll","",1,"scrollColumnAlign320",3,"infiniteScrollDistance","scrollWindow","scrolled"],["class","row col-12 pl-1 pr-1 wrapText",3,"hidden",4,"ngFor","ngForOf"],["class","row container d-flex flex-column",4,"ngIf"],["class","row col-12 p-1 cp",4,"ngIf"],["matTooltip","Select All",1,"pl-1","pt-04",3,"ngClass","checked","disabled","change"],["mat-icon-button","",1,"mt-1","arrow-in-sidnav","value13",3,"disabled","click"],["matTooltip","Expand All",1,"expand-icons"],["matTooltip","Collapse All",1,"expand-icons"],[1,"title","col-5x","p-1","m-0","value13Bold"],[1,"pl-0","pr-0","example-form-field",3,"ngClass"],["matInput","","type","text","autocomplete","off",1,"value13",3,"placeholder","ngModel","input","ngModelChange"],["mat-button","","matSuffix","","mat-icon-button","","matTooltip","Clear Search",3,"click",4,"ngIf"],["mat-button","","matSuffix","","mat-icon-button","","matTooltip","Clear Search",3,"click"],[1,"col-2","searchRow","p-1","searchButton",2,"font-size","18px !important",3,"matTooltip","click"],["matTooltip","Close Search",1,"col-2","searchRow","p-1","searchButton","searchIsActive",2,"font-size","18px !important",3,"click"],[1,"row","col-12","pl-1","pr-1","wrapText",3,"hidden"],[3,"checked","ngClass","disabled","change"],["mat-icon-button","","class","arrow-in-sidnav",3,"ngClass","click",4,"ngIf"],[3,"ngClass","matTooltip","click",4,"ngIf"],["mat-icon-button","",1,"arrow-in-sidnav",3,"ngClass","click"],["matTooltip","Expand",1,"expand-icons"],["matTooltip","Collapse",1,"expand-icons"],[3,"ngClass","matTooltip","click"],[1,"row","container","d-flex","flex-column"],[1,"row","justify-content-center"],["diameter","25","matTooltip","Loading Items ..."],[1,"row","col-12","p-1","cp"],["class","col-12 p-0 value11Grey",3,"click",4,"ngIf"],["class","col-12 p-0 value11Red",3,"click",4,"ngIf"],[1,"col-12","p-0","value11Grey",3,"click"],[1,"col-12","p-0","value11Red",3,"click"],[1,"col-11-95","autoColumnAlign370","pl-3"],["class","row divAlignItemCenter",4,"ngFor","ngForOf"],[1,"row","divAlignItemCenter"],["mat-icon-button","",3,"disabled","click"],["style","padding-top: 5px; font-size: 17px;","matTooltip","Set Ascending Order",4,"ngIf"],["style","font-size: 17px;","matTooltip","Set Descending Order",4,"ngIf"],["style","font-size: 17px;","matTooltip","Set Default Order",4,"ngIf"],[1,"value13"],["matTooltip","Set Ascending Order",2,"padding-top","5px","font-size","17px"],["matTooltip","Set Descending Order",2,"font-size","17px"],["matTooltip","Set Default Order",2,"font-size","17px"],[1,"col-11-95","row","pl-4","pb-2"],[1,"col-12","p-1"],[1,"title","value14Bold"],[1,"title","value14","ml-2"],["cdkDropListGroup","",1,"col-11-95","pl-4","autoColumnAlign350"],["class","row","cdkDropList","",3,"cdkDropListData",4,"ngFor","ngForOf"],["cdkDropList","",1,"row",3,"cdkDropListData"],["cdkDrag","",1,"row","p-0","pl-1","col-12",3,"ngClass","checked","cdkDragData","change","cdkDragEntered"],[1,"ml-2"],[1,"col-10"],[1,"col-4"],[1,"col-6","divAlignItemCenter"],[1,"col-4","p-0","value14RedBold"],["appearance","outline",1,"col-4","date","slide-in-right"],["matInput","","readonly","",3,"matDatepicker","max","ngModel","ngModelChange"],["matSuffix","",3,"for"],["startDatePicker",""],["appearance","outline",1,"col-4","pl-0","pr-0date","slide-in-right"],["matInput","","readonly","",3,"matDatepicker","min","ngModel","ngModelChange"],["endDatePicker",""],[1,"col-4","example-form-field","slide-in-right"],["matInput","","type","text","autocomplete","off","placeholder","Start Value",3,"ngModel","ngModelChange"],["matInput","","type","text","autocomplete","off","placeholder","End Value",3,"ngModel","ngModelChange"]],template:function(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",0),r["\u0275\u0275elementStart"](1,"div",1),r["\u0275\u0275elementStart"](2,"div",2),r["\u0275\u0275elementStart"](3,"mat-button-toggle-group",3,4),r["\u0275\u0275listener"]("change",(function(e){return t.switchFiltersSortToggle(e)})),r["\u0275\u0275elementStart"](5,"mat-button-toggle",5),r["\u0275\u0275text"](6,"Filters"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](7,R,2,3,"mat-button-toggle",6),r["\u0275\u0275elementStart"](8,"mat-button-toggle",7),r["\u0275\u0275text"](9,"My Filters "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](10,B,5,3,"div",8),r["\u0275\u0275template"](11,N,1,0,"div",9),r["\u0275\u0275elementStart"](12,"div",10),r["\u0275\u0275template"](13,U,2,0,"button",11),r["\u0275\u0275template"](14,L,1,1,"mat-slide-toggle",12),r["\u0275\u0275elementStart"](15,"button",13),r["\u0275\u0275listener"]("click",(function(){return t.previousFilters()})),r["\u0275\u0275elementStart"](16,"mat-icon",14),r["\u0275\u0275text"](17," keyboard_arrow_left"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](18,"button",15),r["\u0275\u0275listener"]("click",(function(){return t.nextFilters()})),r["\u0275\u0275elementStart"](19,"mat-icon",14),r["\u0275\u0275text"](20," keyboard_arrow_right"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](21,"button",16),r["\u0275\u0275listener"]("click",(function(){return t.saveMyFilter()})),r["\u0275\u0275elementStart"](22,"mat-icon",14),r["\u0275\u0275text"](23," save"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](24,"button",17),r["\u0275\u0275elementStart"](25,"mat-icon",14),r["\u0275\u0275text"](26," move_to_inbox"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](27,"button",18),r["\u0275\u0275listener"]("click",(function(){return t.customizeFilters()})),r["\u0275\u0275elementStart"](28,"mat-icon",19),r["\u0275\u0275text"](29," settings "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](30,"button",20),r["\u0275\u0275listener"]("click",(function(){return t.resetFilters()})),r["\u0275\u0275elementStart"](31,"mat-icon",21),r["\u0275\u0275text"](32," undo"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](33,"button",22),r["\u0275\u0275listener"]("click",(function(){return t.closeFiltersAndSort()})),r["\u0275\u0275elementStart"](34,"mat-icon",21),r["\u0275\u0275text"](35,"close"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](36,"tooltip",null,23),r["\u0275\u0275elementStart"](38,"div",24),r["\u0275\u0275text"](39,"Create My Filter"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](40,"div",25),r["\u0275\u0275elementStart"](41,"span",26),r["\u0275\u0275text"](42,"Name \xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0\xa0:"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](43,"mat-form-field",27),r["\u0275\u0275elementStart"](44,"input",28),r["\u0275\u0275listener"]("ngModelChange",(function(e){return t.createMyFilterPopUpInput.name=e})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](45,"div",25),r["\u0275\u0275elementStart"](46,"span",26),r["\u0275\u0275text"](47,"Description :"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](48,"mat-form-field",27),r["\u0275\u0275elementStart"](49,"input",28),r["\u0275\u0275listener"]("ngModelChange",(function(e){return t.createMyFilterPopUpInput.description=e})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](50,"div",29),r["\u0275\u0275elementStart"](51,"button",30),r["\u0275\u0275listener"]("click",(function(){return t.createMyFilter()})),r["\u0275\u0275text"](52,"Save"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](53,"button",31),r["\u0275\u0275listener"]("click",(function(){return t.closeCreateMyFilterPopUp()})),r["\u0275\u0275text"](54,"Close"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](55,$,3,2,"div",32),r["\u0275\u0275template"](56,ce,5,1,"div",33),r["\u0275\u0275template"](57,fe,2,1,"div",34),r["\u0275\u0275template"](58,ge,6,0,"div",35),r["\u0275\u0275template"](59,ve,2,1,"div",36),r["\u0275\u0275elementStart"](60,"div",1),r["\u0275\u0275template"](61,ye,1,0,"div",37),r["\u0275\u0275template"](62,Ce,1,0,"div",38),r["\u0275\u0275template"](63,Se,17,8,"div",39),r["\u0275\u0275template"](64,xe,7,2,"div",39),r["\u0275\u0275elementStart"](65,"div",40),r["\u0275\u0275elementStart"](66,"button",41),r["\u0275\u0275listener"]("click",(function(){return t.applyConfig()})),r["\u0275\u0275elementStart"](67,"mat-icon",42),r["\u0275\u0275text"](68,"done_all"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](69,"button",43),r["\u0275\u0275listener"]("click",(function(){return t.clearConfig()})),r["\u0275\u0275elementStart"](70,"mat-icon",42),r["\u0275\u0275text"](71,"clear"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275reference"](37);r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("value",t.filtersSortToggle),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngClass",r["\u0275\u0275pureFunction1"](33,V,"filters"==t.filtersSortToggle)),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngIf",t.udrfService.udrfUiData.isModalSortVisible),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngClass",r["\u0275\u0275pureFunction1"](35,V,"my_filters"==t.filtersSortToggle)),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngIf",t.udrfService.udrfData.isMyFiltersActive),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",!t.udrfService.udrfData.isMyFiltersActive),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngIf",t.udrfService.udrfUiData.countFlag&&t.udrfService.udrfUiData.countForOnlyThisReport&&t.udrfService.udrfUiData.toggleChecked),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",t.udrfService.udrfUiData.countForOnlyThisReport),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("disabled","sort"==t.filtersSortToggle||"my_filters"==t.filtersSortToggle||t.udrfService.udrfData.isCustomizeFiltersActive),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngClass",r["\u0275\u0275pureFunction1"](37,be,!("sort"==t.filtersSortToggle||"my_filters"==t.filtersSortToggle||t.udrfService.udrfData.isCustomizeFiltersActive))),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("disabled","sort"==t.filtersSortToggle||"my_filters"==t.filtersSortToggle||t.udrfService.udrfData.isCustomizeFiltersActive),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngClass",r["\u0275\u0275pureFunction1"](39,be,!("sort"==t.filtersSortToggle||"my_filters"==t.filtersSortToggle||t.udrfService.udrfData.isCustomizeFiltersActive))),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("disabled",!t.udrfService.udrfData.isMyFiltersActive||"my_filters"==t.filtersSortToggle),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngClass",r["\u0275\u0275pureFunction1"](41,be,!(!t.udrfService.udrfData.isMyFiltersActive||"my_filters"==t.filtersSortToggle))),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("disabled","my_filters"==t.filtersSortToggle)("tooltipTriggerFor",e)("trigger","click"),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngClass",r["\u0275\u0275pureFunction2"](43,Ee,!("my_filters"==t.filtersSortToggle),t.udrfService.udrfData.isMyFiltersActive)),r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("ngClass",r["\u0275\u0275pureFunction1"](46,we,t.udrfService.udrfData.isCustomizeFiltersActive)),r["\u0275\u0275advance"](16),r["\u0275\u0275property"]("ngModel",t.createMyFilterPopUpInput.name),r["\u0275\u0275advance"](5),r["\u0275\u0275property"]("ngModel",t.createMyFilterPopUpInput.description),r["\u0275\u0275advance"](6),r["\u0275\u0275property"]("ngIf","my_filters"==t.filtersSortToggle),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","filters"==t.filtersSortToggle&&!t.udrfService.udrfData.isCustomizeFiltersActive),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","sort"==t.filtersSortToggle&&!t.udrfService.udrfData.isCustomizeFiltersActive),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",t.udrfService.udrfData.isCustomizeFiltersActive),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",t.udrfService.udrfData.isCustomizeFiltersActive),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngIf","filters"!=t.filtersSortToggle||!t.udrfService.udrfData.isOverallCustomButtonActivated),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","filters"==t.filtersSortToggle&&t.udrfService.udrfData.isOverallCustomButtonActivated),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","filters"==t.filtersSortToggle&&t.udrfService.udrfData.isOverallCustomButtonActivated&&"date"==t.udrfService.udrfData.overallCustomButtonDataType),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","filters"==t.filtersSortToggle&&t.udrfService.udrfData.isOverallCustomButtonActivated&&"value"==t.udrfService.udrfData.overallCustomButtonDataType),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("disabled",!t.udrfService.udrfData.areFiltersApplied)("ngClass",t.udrfService.udrfData.areFiltersApplied?"mini-tick-enabled":"mini-tick-disabled"),r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("ngClass",t.udrfService.udrfData.areFiltersApplied?"mini-tick-enabled":"mini-tick-disabled")}},directives:[f.b,f.a,c.NgClass,c.NgIf,p.a,g.a,u.a,A.a,I,d.c,m.b,y.e,y.v,y.y,S.c,x.a,c.NgForOf,D.a,C.a,h.a,d.i,o.f,o.e,o.a,d.g,v.g,v.i,v.f],styles:["popover-container[_ngcontent-%COMP%]{line-height:145%}.popover-title[_ngcontent-%COMP%]{font-weight:700;margin:5px 5px 15px;font-size:14px;text-align:center}.popover-content[_ngcontent-%COMP%]{font-size:14px;margin-bottom:10px;margin-left:5px;margin-right:5px}.popover-actions[_ngcontent-%COMP%]{text-align:right;margin:5px}.popover-actions-accept-btn[_ngcontent-%COMP%]{border:1px solid transparent;border-radius:6px;background:#c92020;cursor:pointer;color:#fff;padding:6px 15px;font-size:14px;margin-right:3%}.popover-actions-dismiss-btn[_ngcontent-%COMP%]{border:1px solid #c92020;border-radius:6px;background:none;cursor:pointer;color:#c92020;padding:6px 15px;font-size:14px}.udrf-modal-styles[_ngcontent-%COMP%]   .ml-6[_ngcontent-%COMP%]{margin-left:2rem!important}.udrf-modal-styles[_ngcontent-%COMP%]   .ml-8[_ngcontent-%COMP%]{margin-left:2.5rem!important}.udrf-modal-styles[_ngcontent-%COMP%]   .ml-10[_ngcontent-%COMP%]{margin-left:3rem!important}.udrf-modal-styles[_ngcontent-%COMP%]   .arrow-in-sidnav[_ngcontent-%COMP%]{height:20px;width:20px;line-height:10px}.udrf-modal-styles[_ngcontent-%COMP%]   .arrow-in-sidnav[_ngcontent-%COMP%]   .expand-icons[_ngcontent-%COMP%]{font-size:15px}.udrf-modal-styles[_ngcontent-%COMP%]     .mat-checkbox-inner-container{margin-right:0!important}.udrf-modal-styles[_ngcontent-%COMP%]     .swal2-container.swal2-center{z-index:10000}.udrf-modal-styles[_ngcontent-%COMP%]     .cdk-overlay-container{z-index:100}.udrf-modal-styles[_ngcontent-%COMP%]   .carousel-row[_ngcontent-%COMP%]{transition:all .2s}.udrf-modal-styles[_ngcontent-%COMP%]   .carousel-div[_ngcontent-%COMP%]{margin-left:2%;margin-right:2%;padding-bottom:.5%;overflow-x:scroll!important}.udrf-modal-styles[_ngcontent-%COMP%]   .carousel-item-div[_ngcontent-%COMP%]{min-width:225px}.udrf-modal-styles[_ngcontent-%COMP%]   .btn-toggle-selected[_ngcontent-%COMP%]{font-size:12px!important;background-color:#c92020!important;color:#fff}.udrf-modal-styles[_ngcontent-%COMP%]   .ta-c[_ngcontent-%COMP%]{text-align:center;padding-left:0!important;padding-right:0!important}.udrf-modal-styles[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:14px;color:#66615b;font-weight:400}.udrf-modal-styles[_ngcontent-%COMP%]   .value13[_ngcontent-%COMP%]{font-weight:400!important}.udrf-modal-styles[_ngcontent-%COMP%]   .value13[_ngcontent-%COMP%], .udrf-modal-styles[_ngcontent-%COMP%]   .value13Bold[_ngcontent-%COMP%]{color:#000!important;font-size:13px!important;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.udrf-modal-styles[_ngcontent-%COMP%]   .value13Bold[_ngcontent-%COMP%]{font-weight:700!important}.udrf-modal-styles[_ngcontent-%COMP%]   .wrapText[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block}.udrf-modal-styles[_ngcontent-%COMP%]   .value13Red[_ngcontent-%COMP%]{color:#c92020!important;font-size:13px!important;font-weight:500!important}.udrf-modal-styles[_ngcontent-%COMP%]   .value13Red[_ngcontent-%COMP%], .udrf-modal-styles[_ngcontent-%COMP%]   .value14[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.udrf-modal-styles[_ngcontent-%COMP%]   .value14[_ngcontent-%COMP%]{color:#000!important;font-size:14px!important;font-weight:400!important}.udrf-modal-styles[_ngcontent-%COMP%]   .value14Bold[_ngcontent-%COMP%]{color:#000!important;font-size:14px!important;font-weight:700!important}.udrf-modal-styles[_ngcontent-%COMP%]   .value11Grey[_ngcontent-%COMP%], .udrf-modal-styles[_ngcontent-%COMP%]   .value14Bold[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.udrf-modal-styles[_ngcontent-%COMP%]   .value11Grey[_ngcontent-%COMP%]{color:#4a4a4a;font-size:11px;font-weight:400}.udrf-modal-styles[_ngcontent-%COMP%]   .value11Red[_ngcontent-%COMP%]{color:#c92020;font-size:11px;font-weight:400;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.udrf-modal-styles[_ngcontent-%COMP%]   .value14RedBold[_ngcontent-%COMP%]{color:#c92020;font-size:14px;font-weight:700;white-space:nowrap;display:inline}.udrf-modal-styles[_ngcontent-%COMP%]   .cp[_ngcontent-%COMP%]{cursor:pointer}.udrf-modal-styles[_ngcontent-%COMP%]   .divAlignItemCenter[_ngcontent-%COMP%], .udrf-modal-styles[_ngcontent-%COMP%]   .searchRow[_ngcontent-%COMP%]{display:flex;align-items:center}.udrf-modal-styles[_ngcontent-%COMP%]   .searchRow[_ngcontent-%COMP%]{justify-content:center}.udrf-modal-styles[_ngcontent-%COMP%]   .searchButton[_ngcontent-%COMP%]{cursor:pointer;font-size:18px}.udrf-modal-styles[_ngcontent-%COMP%]   .searchIsActive[_ngcontent-%COMP%]{color:#c92020!important}.udrf-modal-styles[_ngcontent-%COMP%]   .mini-tick-enabled-small[_ngcontent-%COMP%]{height:32px!important;width:32px!important;line-height:32px;background-color:#cf0001!important;color:#fff!important;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.udrf-modal-styles[_ngcontent-%COMP%]     .mat-button-wrapper{padding:0!important}.udrf-modal-styles[_ngcontent-%COMP%]   .example-form-field[_ngcontent-%COMP%]{bottom:10px}.udrf-modal-styles[_ngcontent-%COMP%]     .mat-form-field-appearance-legacy .mat-form-field-wrapper{margin-left:5px!important;margin-bottom:0!important;padding-bottom:6px!important}.udrf-modal-styles[_ngcontent-%COMP%]   .mini-tick-enabled[_ngcontent-%COMP%]{background-color:#cf0001!important}.udrf-modal-styles[_ngcontent-%COMP%]   .mini-tick-disabled[_ngcontent-%COMP%], .udrf-modal-styles[_ngcontent-%COMP%]   .mini-tick-enabled[_ngcontent-%COMP%]{height:40px!important;width:40px!important;line-height:42px!important;color:#fff!important;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.udrf-modal-styles[_ngcontent-%COMP%]   .listcard[_ngcontent-%COMP%]:hover   .hide-on-hover[_ngcontent-%COMP%]{display:block!important}.udrf-modal-styles[_ngcontent-%COMP%]   .hide-on-hover[_ngcontent-%COMP%]{display:none}.udrf-modal-styles[_ngcontent-%COMP%]   .hide-on-hover[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{font-size:21px!important}.udrf-modal-styles[_ngcontent-%COMP%]   .h-40[_ngcontent-%COMP%]{height:40px!important}.udrf-modal-styles[_ngcontent-%COMP%]   .autoColumnAlign370[_ngcontent-%COMP%]{display:flex;flex-direction:column;flex-wrap:wrap;max-height:370px}.udrf-modal-styles[_ngcontent-%COMP%]   .autoColumnAlign350[_ngcontent-%COMP%]{display:flex;flex-direction:column;flex-wrap:wrap;max-height:350px}.udrf-modal-styles[_ngcontent-%COMP%]   .pt-04[_ngcontent-%COMP%]{padding-top:.4rem!important}.udrf-modal-styles[_ngcontent-%COMP%]   .scrollColumnAlign360[_ngcontent-%COMP%]{max-height:360px;overflow-y:scroll}.udrf-modal-styles[_ngcontent-%COMP%]   .scrollColumnAlign320[_ngcontent-%COMP%]{height:280px;overflow-y:scroll}.udrf-modal-styles[_ngcontent-%COMP%]   .col-11-95[_ngcontent-%COMP%]{flex:0 0 94%;max-width:94%}.udrf-modal-styles[_ngcontent-%COMP%]   .cdk-drag-preview[_ngcontent-%COMP%]{box-sizing:border-box;border-radius:4px;box-shadow:0 5px 5px -3px rgba(0,0,0,.2),0 8px 10px 1px rgba(0,0,0,.14),0 3px 14px 2px rgba(0,0,0,.12)}.udrf-modal-styles[_ngcontent-%COMP%]   .cdk-drag-placeholder[_ngcontent-%COMP%]{opacity:0}.udrf-modal-styles[_ngcontent-%COMP%]   .cdk-drag-animating[_ngcontent-%COMP%], .udrf-modal-styles[_ngcontent-%COMP%]   .dragAndDrop.cdk-drop-list-dragging[_ngcontent-%COMP%]   .example-box[_ngcontent-%COMP%]:not(.cdk-drag-placeholder){transition:transform .25s cubic-bezier(0,0,.2,1)}.udrf-modal-styles[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{font-size:21px!important;color:#66615b}.udrf-modal-styles[_ngcontent-%COMP%]   .slide-top[_ngcontent-%COMP%]{animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-top{0%{transform:translateY(10px)}to{transform:translateY(0)}}"]}),e})()},hJL4:function(e,t,n){"use strict";n.d(t,"a",(function(){return p}));var i=n("mrSG"),r=n("XNiG"),o=n("xG9w"),l=n("fXoL"),a=n("tk/3"),s=n("LcQX"),c=n("XXEo"),d=n("flaP");let p=(()=>{class e{constructor(e,t,n,i){this.http=e,this.UtilityService=t,this.loginService=n,this.roleService=i,this.msg=new r.b,this.taskStatusColor=[],this.approveRequest=(e,t)=>this.http.post("/api/isa/request/approveResourceRequest",{requestDetails:e,approverOid:t}),this.rejectRequest=(e,t)=>this.http.post("/api/isa/request/rejectResourceRequest",{requestDetails:e,approverOid:t}),this.getTaskStatusColor()}getAllRequestsOfUser(e,t,n,i,r,o,l){let a=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getAllRequestsOfUser",{oid:e,statusId:t,statusCode:n,objectIds:i,skip:r,limit:o,filterConfig:l,orgIds:a})}getAllRoleAccess(){return o.where(this.roleService.roles,{application_id:139})}getRequestsBasedOnStatus(e,t,n,i,r,o,l){let a=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getRequestsBasedOnStatus",{oid:e,statusId:t,statusCode:n,objectIds:i,skip:r,limit:o,filterConfig:l,orgIds:a})}getRequestsForAwaitingApproval(e,t,n,i){return this.http.post("/api/isa/request/getRequestsForAwaitingApproval",{oid:e,skip:t,limit:n,filterConfig:i})}getObjectIdsBasedOnOid(e){return this.http.post("/api/isa/request/getObjectIdsBasedOnOid",{oid:e})}getStatusCountBasedOnOid(e,t,n,i){let r=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getStatusCountBasedOnOid",{oid:e,objectIds:t,dataTypeArray:n,filterConfig:i,orgIds:r})}getHeaderCount(e){return this.http.post("/api/isa/request/getHeaderCount",{oid:e})}getRequestBasedOnStatus(e,t){return this.http.post("/api/isa/request/getRequestBasedOnStatus",{oid:e,status:t})}getDataTypeArray(){return this.http.post("/api/isa/request/getISAStatusMasterDataUDRF",{})}sendMsg(e){this.msg.next(e)}getMsg(){return this.msg.asObservable()}getTaskStatusColor(){return new Promise((e,t)=>{this.http.post("/api/isa/request/getTaskStatusNameColor",{}).subscribe(t=>(this.taskStatusColor=t,e(t)),e=>{})})}getAllRequestsForRmg(e){return this.http.post("/api/isa/request/getAllRequestsForRmg",{filterConfig:e})}getTotalForRmg(e){return new Promise((t,n)=>{this.http.post("/api/isa/request/getTotalForRmg",{filterConfig:e}).subscribe(e=>t(e),e=>{})})}getDefaultISAStatusToDisplay(){return this.http.post("/api/isa/request/getISADefaultStatusToDisplay",{})}getCTCbreakUpForSkillSet(e){return new Promise((t,n)=>{this.http.post("/api/isa/request/getCTCbreakUpForSkillSet",e).subscribe(e=>t(e.data),e=>{n(e)})})}getCurrentUserRole(){return this.roleService&&this.roleService.roles&&this.roleService.roles.length>0?this.roleService.roles[0].role_id:null}getTemplateForUser(e){return this.http.post("/api/isa/request/getVisibleFormFieldsForUser",e)}saveAndApproveRequest(e){return this.http.post("/api/isa/request/updBudgetInfoByRequestId",e)}getVisibilityMatrix(e){return this.http.post("/api/isa/request/getAuthorzedFieldsByRoleId",e)}getISAAttachmentFromS3(e){return this.http.post("/api/isa/attachment/getISAAttachmentFromS3",{key:e})}getSkillType(){return this.http.post("/api/isa/request/getSkillType",{})}getSkillLevel(){return this.http.post("/api/isa/request/getSkillLevel",{})}getSkillExperience(){return this.http.post("/api/isa/request/getSkillExperience",{})}getSkillName(e){return this.http.post("/api/isa/request/getSkillName",{skill_type_id:e})}getIsaActivityReportDownload(){return this.http.post("/api/isa/request/getIsaActivityReportDownload",{})}getISASLAreport(e){return this.http.post("/api/isa/request/getISASLAreport",{filterConfig:e})}getISACustomReport(e){return this.http.post("/api/isa/request/getISACustomReport",{filterConfig:e})}getPositionType(){return this.http.post("/api/employee360/masterData/getPositionType",{})}calculateNetCostBasedOnPosition(e,t,n,r,l,a,s){return Object(i.c)(this,void 0,void 0,(function*(){let i;i=a&&a.length>1&&(yield this.getManpowerCostByOId(a,n,l,2))||(yield this.getManpowerCostBasedOnPosition(e,t,n,l,s));let c=yield this.getNonManpowerCost(t,n,r,l,2),d=yield this.getAllocatedCost(),p=0;p=(i?i.cost:0)+c.length>0?o.reduce(o.pluck(c,"cost"),(e,t)=>e+t,0):0;let u=d.length>0?o.reduce(o.pluck(d,"percentage"),(e,t)=>e+t,0):0;return{cost:p,currency:i&&i.currency_code?i.currency_code:"",manpowerCost:i,nonManpowerCost:c,allocatedCost:d,allocatedCostValue:p*(u/100)}}))}getManpowerCostBasedOnPosition(e,t,n,i,r){return new Promise((o,l)=>{this.http.post("/api/isa/configuration/getManpowerCostBasedOnPosition",{skillRoleId:e,nationalityId:t,locationGroupId:n,unit:i,position:r}).subscribe(e=>o(e),e=>(console.log(e),l(e)))})}getNonManpowerCost(e,t,n,i,r){return new Promise((o,l)=>{this.http.post("/api/project/getNonManpowerCost",{nationalityId:e,locationGroupId:t,locationId:n,unit:i,currency_id:r}).subscribe(e=>o(e),e=>(console.log(e),l(e)))})}getAllocatedCost(){return new Promise((e,t)=>{this.http.post("/api/project/getAllocatedCost","").subscribe(t=>e(t),e=>(console.log(e),t(e)))})}getManpowerCostByOId(e,t,n,i){return new Promise((r,o)=>{this.http.post("/api/project/getEmployeeSalary",{oid:e,location_group_id:t,unit_id:n,currency_id:i}).subscribe(e=>r(e),e=>(console.log(e),o(e)))})}getSkillNameMaster(){return this.http.post("/api/isa/configuration/getSkillName",{})}getVendorOid(e,t){return this.http.post("/api/isa/request/getVendorOid",{vendorNames:e,docID:t})}getVendorList(e,t){return this.http.post("/api/isa/request/getVendorList",{documentID:e,limitval:t})}removeVendor(e,t){return this.http.post("/api/isa/request/removeVendors",{vendorOid:e,documentID:t})}checkVendorOrNot(){return this.http.post("/api/isa/request/checkVendorOrNot",{})}}return e.\u0275fac=function(t){return new(t||e)(l["\u0275\u0275inject"](a.c),l["\u0275\u0275inject"](s.a),l["\u0275\u0275inject"](c.a),l["\u0275\u0275inject"](d.a))},e.\u0275prov=l["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},nAV5:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var i=n("2Vo4"),r=n("fXoL");let o=(()=>{class e{constructor(){this.inlineEditActiveDataSubject=new i.a({}),this.inlineEditActiveData=this.inlineEditActiveDataSubject.asObservable(),this.inlineEditPopupSubject=new i.a({}),this.inlineEditPopup=this.inlineEditPopupSubject.asObservable(),this.inlineEditCallbackSubject=new i.a({}),this.inlineEditCallback=this.inlineEditCallbackSubject.asObservable()}setInlineEditActiveDataSubject(e){this.inlineEditActiveDataSubject.next(e)}setInlineEditPopupSubject(e){this.inlineEditPopupSubject.next(e)}setInlineEditCallbackSubject(e){this.inlineEditCallbackSubject.next(e)}setOverlayRef(e){this.overlayRef=e}getOverlayRef(){return this.overlayRef}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275prov=r["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},ucYs:function(e,t,n){"use strict";n.d(t,"a",(function(){return s}));var i=n("mrSG"),r=n("xG9w"),o=n("fXoL"),l=n("tk/3"),a=n("BVzC");let s=(()=>{class e{constructor(e,t){this.http=e,this.errorService=t,this.workflowStatusList=[],this.getWorkflowStatusList()}getWorkflowStatusList(){this.http.post("/api/wfPrimary/getWorkflowStatusList","").subscribe(e=>{this.workflowStatusList=e.data},e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server While Getting Workflow Status List",e&&e.params?e.params:e&&e.error?e.error.params:{})})}getWorkflowProperties(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/getWorkflowProperties",{applicationId:e}).subscribe(e=>t(e),e=>n(e))})}getWorkflowPropertiesByWorkflowId(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/getWorkflowPropertiesByWorkflowId",{workflowId:e}).subscribe(e=>t(e),e=>n(e))})}getApproversHierarchy(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/getApproversHierarchy",e).subscribe(e=>t(e),e=>n(e))})}createWorkflowItems(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/createWorkflowItems",e).subscribe(e=>t(e),e=>n(e))})}getWorkflowDetails(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/getWorkflowDetails",{workflowId:e}).subscribe(e=>t(e),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while getting workflow details",e&&e.params?e.params:e&&e.error?e.error.params:{})})})}formatApproversHierarchy(e,t){return Object(i.c)(this,void 0,void 0,(function*(){0==e.length&&t.cc0&&t.cc0.appr0&&t.cc0.appr0.length>0&&e.push([{approvalType:"I",purposeOfInsertingIntoArray:"Just to ensure that 'I' type data is used, if it exists"}]);for(let n=0;n<e.length;n++){let i=[],o=r.keys(t["cc"+n]);for(let r=0;r<o.length;r++)for(let l=0;l<t["cc"+n][o[r]].length;l++){let a={name:t["cc"+n][o[r]][l].DELEGATE_NAME,oid:t["cc"+n][o[r]][l].DELEGATE_OID,level:r+1,designation:t["cc"+n][o[r]][l].DELEGATE_DESIGNATION_NAME,is_delegated:t["cc"+n][o[r]][l].IS_DELEGATED,role:t["cc"+n][o[r]][l].DELEGATE_ROLE_NAME};if(1==t["cc"+n][o[r]][l].IS_DELEGATED&&(a.delegated_by={name:t["cc"+n][o[r]][l].APPROVER_NAME,oid:t["cc"+n][o[r]][l].APPROVER_OID,level:r+1,designation:t["cc"+n][o[r]][l].APPROVER_DESIGNATION_NAME}),i.push(a),n==e.length-1&&r==o.length-1&&l==t["cc"+n][o[r]].length-1)return i}}}))}storeComments(e,t,n){return new Promise((i,r)=>{this.http.post("/api/wfPrimary/updateComments",{workflowHeaderId:e,newComments:t,commentor:n}).subscribe(e=>i(e),e=>(this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server Error while Updating Workflow Comments",e&&e.params?e.params:e&&e.error?e.error.params:{}),r(e)))})}updateWorkflowItems(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/updateWorkflowItems",e).subscribe(e=>t(e),e=>(console.log(e),n(e)))})}formatApproversHierarchyForOpportunityApprovalActivity(e){return Object(i.c)(this,void 0,void 0,(function*(){for(let t=0;t<1;t++){let n=[],i=r.keys(e["cc"+t]);for(let r=0;r<i.length;r++)for(let o=0;o<e["cc"+t][i[r]].length;o++){let l={name:e["cc"+t][i[r]][o].DELEGATE_NAME,oid:e["cc"+t][i[r]][o].DELEGATE_OID,level:e["cc"+t][i[r]][o].APPROVAL_ORDER,designation:e["cc"+t][i[r]][o].DELEGATE_DESIGNATION_NAME,is_delegated:e["cc"+t][i[r]][o].IS_DELEGATED};if(1==e["cc"+t][i[r]][o].IS_DELEGATED&&(l.delegated_by={name:e["cc"+t][i[r]][o].APPROVER_NAME,oid:e["cc"+t][i[r]][o].APPROVER_OID,level:e["cc"+t][i[r]][o].APPROVAL_ORDER,designation:e["cc"+t][i[r]][o].APPROVER_DESIGNATION_NAME}),n.push(l),r==i.length-1&&o==e["cc"+t][i[r]].length-1)return n}}}))}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275inject"](l.c),o["\u0275\u0275inject"](a.a))},e.\u0275prov=o["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},yArD:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var i=n("mrSG"),r=n("fXoL"),o=n("mEBv"),l=n("nAV5");let a=(()=>{class e{constructor(e,t){this.tooltipPopup=e,this.inlineEditPopupService=t,this.loadInlineEditTooltip=e=>Object(i.c)(this,void 0,void 0,(function*(){const{InlineEditPopupComponent:t}=yield n.e(195).then(n.bind(null,"PgVx"));this.inlineEditActiveDataSubscription=this.inlineEditPopupService.inlineEditActiveData.subscribe(t=>{t&&(this.inlineEditActiveData=t,e.maxWidth=t.dropdownConfig.maxWidth)}),e.showDelay=500,e.hideDelay=300,e.trigger="click",e.autoPlacement=!0,e.component=t,this.tooltipPopup.load(e),this.inlineEditActiveData.tooltipPopup=this.tooltipPopup,this.inlineEditPopupService.setInlineEditPopupSubject(this.inlineEditActiveData)}))}onEdit(e){this.loadInlineEditTooltip({event:e})}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](o.a),r["\u0275\u0275directiveInject"](l.a))},e.\u0275dir=r["\u0275\u0275defineDirective"]({type:e,selectors:[["","inlineEdit",""]],hostBindings:function(e,t){1&e&&r["\u0275\u0275listener"]("click",(function(e){return t.onEdit(e)}))}}),e})()}}]);