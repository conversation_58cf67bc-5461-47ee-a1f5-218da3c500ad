(window.webpackJsonp=window.webpackJsonp||[]).push([[997,765,821,822,983,987,990,991],{hJL4:function(t,e,i){"use strict";i.d(e,"a",(function(){return u}));var n=i("mrSG"),a=i("XNiG"),s=i("xG9w"),o=i("fXoL"),l=i("tk/3"),r=i("LcQX"),p=i("XXEo"),d=i("flaP");let u=(()=>{class t{constructor(t,e,i,n){this.http=t,this.UtilityService=e,this.loginService=i,this.roleService=n,this.msg=new a.b,this.taskStatusColor=[],this.approveRequest=(t,e)=>this.http.post("/api/isa/request/approveResourceRequest",{requestDetails:t,approverOid:e}),this.rejectRequest=(t,e)=>this.http.post("/api/isa/request/rejectResourceRequest",{requestDetails:t,approverOid:e}),this.getTaskStatusColor()}getAllRequestsOfUser(t,e,i,n,a,s,o){let l=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getAllRequestsOfUser",{oid:t,statusId:e,statusCode:i,objectIds:n,skip:a,limit:s,filterConfig:o,orgIds:l})}getAllRoleAccess(){return s.where(this.roleService.roles,{application_id:139})}getRequestsBasedOnStatus(t,e,i,n,a,s,o){let l=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getRequestsBasedOnStatus",{oid:t,statusId:e,statusCode:i,objectIds:n,skip:a,limit:s,filterConfig:o,orgIds:l})}getRequestsForAwaitingApproval(t,e,i,n){return this.http.post("/api/isa/request/getRequestsForAwaitingApproval",{oid:t,skip:e,limit:i,filterConfig:n})}getObjectIdsBasedOnOid(t){return this.http.post("/api/isa/request/getObjectIdsBasedOnOid",{oid:t})}getStatusCountBasedOnOid(t,e,i,n){let a=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getStatusCountBasedOnOid",{oid:t,objectIds:e,dataTypeArray:i,filterConfig:n,orgIds:a})}getHeaderCount(t){return this.http.post("/api/isa/request/getHeaderCount",{oid:t})}getRequestBasedOnStatus(t,e){return this.http.post("/api/isa/request/getRequestBasedOnStatus",{oid:t,status:e})}getDataTypeArray(){return this.http.post("/api/isa/request/getISAStatusMasterDataUDRF",{})}sendMsg(t){this.msg.next(t)}getMsg(){return this.msg.asObservable()}getTaskStatusColor(){return new Promise((t,e)=>{this.http.post("/api/isa/request/getTaskStatusNameColor",{}).subscribe(e=>(this.taskStatusColor=e,t(e)),t=>{})})}getAllRequestsForRmg(t){return this.http.post("/api/isa/request/getAllRequestsForRmg",{filterConfig:t})}getTotalForRmg(t){return new Promise((e,i)=>{this.http.post("/api/isa/request/getTotalForRmg",{filterConfig:t}).subscribe(t=>e(t),t=>{})})}getDefaultISAStatusToDisplay(){return this.http.post("/api/isa/request/getISADefaultStatusToDisplay",{})}getCTCbreakUpForSkillSet(t){return new Promise((e,i)=>{this.http.post("/api/isa/request/getCTCbreakUpForSkillSet",t).subscribe(t=>e(t.data),t=>{i(t)})})}getCurrentUserRole(){return this.roleService&&this.roleService.roles&&this.roleService.roles.length>0?this.roleService.roles[0].role_id:null}getTemplateForUser(t){return this.http.post("/api/isa/request/getVisibleFormFieldsForUser",t)}saveAndApproveRequest(t){return this.http.post("/api/isa/request/updBudgetInfoByRequestId",t)}getVisibilityMatrix(t){return this.http.post("/api/isa/request/getAuthorzedFieldsByRoleId",t)}getISAAttachmentFromS3(t){return this.http.post("/api/isa/attachment/getISAAttachmentFromS3",{key:t})}getSkillType(){return this.http.post("/api/isa/request/getSkillType",{})}getSkillLevel(){return this.http.post("/api/isa/request/getSkillLevel",{})}getSkillExperience(){return this.http.post("/api/isa/request/getSkillExperience",{})}getSkillName(t){return this.http.post("/api/isa/request/getSkillName",{skill_type_id:t})}getIsaActivityReportDownload(){return this.http.post("/api/isa/request/getIsaActivityReportDownload",{})}getISASLAreport(t){return this.http.post("/api/isa/request/getISASLAreport",{filterConfig:t})}getISACustomReport(t){return this.http.post("/api/isa/request/getISACustomReport",{filterConfig:t})}getPositionType(){return this.http.post("/api/employee360/masterData/getPositionType",{})}calculateNetCostBasedOnPosition(t,e,i,a,o,l,r){return Object(n.c)(this,void 0,void 0,(function*(){let n;n=l&&l.length>1&&(yield this.getManpowerCostByOId(l,i,o,2))||(yield this.getManpowerCostBasedOnPosition(t,e,i,o,r));let p=yield this.getNonManpowerCost(e,i,a,o,2),d=yield this.getAllocatedCost(),u=0;u=(n?n.cost:0)+p.length>0?s.reduce(s.pluck(p,"cost"),(t,e)=>t+e,0):0;let c=d.length>0?s.reduce(s.pluck(d,"percentage"),(t,e)=>t+e,0):0;return{cost:u,currency:n&&n.currency_code?n.currency_code:"",manpowerCost:n,nonManpowerCost:p,allocatedCost:d,allocatedCostValue:u*(c/100)}}))}getManpowerCostBasedOnPosition(t,e,i,n,a){return new Promise((s,o)=>{this.http.post("/api/isa/configuration/getManpowerCostBasedOnPosition",{skillRoleId:t,nationalityId:e,locationGroupId:i,unit:n,position:a}).subscribe(t=>s(t),t=>(console.log(t),o(t)))})}getNonManpowerCost(t,e,i,n,a){return new Promise((s,o)=>{this.http.post("/api/project/getNonManpowerCost",{nationalityId:t,locationGroupId:e,locationId:i,unit:n,currency_id:a}).subscribe(t=>s(t),t=>(console.log(t),o(t)))})}getAllocatedCost(){return new Promise((t,e)=>{this.http.post("/api/project/getAllocatedCost","").subscribe(e=>t(e),t=>(console.log(t),e(t)))})}getManpowerCostByOId(t,e,i,n){return new Promise((a,s)=>{this.http.post("/api/project/getEmployeeSalary",{oid:t,location_group_id:e,unit_id:i,currency_id:n}).subscribe(t=>a(t),t=>(console.log(t),s(t)))})}getSkillNameMaster(){return this.http.post("/api/isa/configuration/getSkillName",{})}getVendorOid(t,e){return this.http.post("/api/isa/request/getVendorOid",{vendorNames:t,docID:e})}getVendorList(t,e){return this.http.post("/api/isa/request/getVendorList",{documentID:t,limitval:e})}removeVendor(t,e){return this.http.post("/api/isa/request/removeVendors",{vendorOid:t,documentID:e})}checkVendorOrNot(){return this.http.post("/api/isa/request/checkVendorOrNot",{})}}return t.\u0275fac=function(e){return new(e||t)(o["\u0275\u0275inject"](l.c),o["\u0275\u0275inject"](r.a),o["\u0275\u0275inject"](p.a),o["\u0275\u0275inject"](d.a))},t.\u0275prov=o["\u0275\u0275defineInjectable"]({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()},ucYs:function(t,e,i){"use strict";i.d(e,"a",(function(){return r}));var n=i("mrSG"),a=i("xG9w"),s=i("fXoL"),o=i("tk/3"),l=i("BVzC");let r=(()=>{class t{constructor(t,e){this.http=t,this.errorService=e,this.workflowStatusList=[],this.getWorkflowStatusList()}getWorkflowStatusList(){this.http.post("/api/wfPrimary/getWorkflowStatusList","").subscribe(t=>{this.workflowStatusList=t.data},t=>{this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Internal Server While Getting Workflow Status List",t&&t.params?t.params:t&&t.error?t.error.params:{})})}getWorkflowProperties(t){return new Promise((e,i)=>{this.http.post("/api/wfPrimary/getWorkflowProperties",{applicationId:t}).subscribe(t=>e(t),t=>i(t))})}getWorkflowPropertiesByWorkflowId(t){return new Promise((e,i)=>{this.http.post("/api/wfPrimary/getWorkflowPropertiesByWorkflowId",{workflowId:t}).subscribe(t=>e(t),t=>i(t))})}getApproversHierarchy(t){return new Promise((e,i)=>{this.http.post("/api/wfPrimary/getApproversHierarchy",t).subscribe(t=>e(t),t=>i(t))})}createWorkflowItems(t){return new Promise((e,i)=>{this.http.post("/api/wfPrimary/createWorkflowItems",t).subscribe(t=>e(t),t=>i(t))})}getWorkflowDetails(t){return new Promise((e,i)=>{this.http.post("/api/wfPrimary/getWorkflowDetails",{workflowId:t}).subscribe(t=>e(t),t=>{this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Error while getting workflow details",t&&t.params?t.params:t&&t.error?t.error.params:{})})})}formatApproversHierarchy(t,e){return Object(n.c)(this,void 0,void 0,(function*(){0==t.length&&e.cc0&&e.cc0.appr0&&e.cc0.appr0.length>0&&t.push([{approvalType:"I",purposeOfInsertingIntoArray:"Just to ensure that 'I' type data is used, if it exists"}]);for(let i=0;i<t.length;i++){let n=[],s=a.keys(e["cc"+i]);for(let a=0;a<s.length;a++)for(let o=0;o<e["cc"+i][s[a]].length;o++){let l={name:e["cc"+i][s[a]][o].DELEGATE_NAME,oid:e["cc"+i][s[a]][o].DELEGATE_OID,level:a+1,designation:e["cc"+i][s[a]][o].DELEGATE_DESIGNATION_NAME,is_delegated:e["cc"+i][s[a]][o].IS_DELEGATED,role:e["cc"+i][s[a]][o].DELEGATE_ROLE_NAME};if(1==e["cc"+i][s[a]][o].IS_DELEGATED&&(l.delegated_by={name:e["cc"+i][s[a]][o].APPROVER_NAME,oid:e["cc"+i][s[a]][o].APPROVER_OID,level:a+1,designation:e["cc"+i][s[a]][o].APPROVER_DESIGNATION_NAME}),n.push(l),i==t.length-1&&a==s.length-1&&o==e["cc"+i][s[a]].length-1)return n}}}))}storeComments(t,e,i){return new Promise((n,a)=>{this.http.post("/api/wfPrimary/updateComments",{workflowHeaderId:t,newComments:e,commentor:i}).subscribe(t=>n(t),t=>(this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Internal Server Error while Updating Workflow Comments",t&&t.params?t.params:t&&t.error?t.error.params:{}),a(t)))})}updateWorkflowItems(t){return new Promise((e,i)=>{this.http.post("/api/wfPrimary/updateWorkflowItems",t).subscribe(t=>e(t),t=>(console.log(t),i(t)))})}formatApproversHierarchyForOpportunityApprovalActivity(t){return Object(n.c)(this,void 0,void 0,(function*(){for(let e=0;e<1;e++){let i=[],n=a.keys(t["cc"+e]);for(let a=0;a<n.length;a++)for(let s=0;s<t["cc"+e][n[a]].length;s++){let o={name:t["cc"+e][n[a]][s].DELEGATE_NAME,oid:t["cc"+e][n[a]][s].DELEGATE_OID,level:t["cc"+e][n[a]][s].APPROVAL_ORDER,designation:t["cc"+e][n[a]][s].DELEGATE_DESIGNATION_NAME,is_delegated:t["cc"+e][n[a]][s].IS_DELEGATED};if(1==t["cc"+e][n[a]][s].IS_DELEGATED&&(o.delegated_by={name:t["cc"+e][n[a]][s].APPROVER_NAME,oid:t["cc"+e][n[a]][s].APPROVER_OID,level:t["cc"+e][n[a]][s].APPROVAL_ORDER,designation:t["cc"+e][n[a]][s].APPROVER_DESIGNATION_NAME}),i.push(o),a==n.length-1&&s==t["cc"+e][n[a]].length-1)return i}}}))}}return t.\u0275fac=function(e){return new(e||t)(s["\u0275\u0275inject"](o.c),s["\u0275\u0275inject"](l.a))},t.\u0275prov=s["\u0275\u0275defineInjectable"]({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()},zsqF:function(t,e,i){"use strict";i.r(e),i.d(e,"MONTH_YEAR_DATE_FORMAT",(function(){return Z})),i.d(e,"TsInlineEntryPopupComponent",(function(){return tt})),i.d(e,"TsInlineEntryPopupModule",(function(){return et}));var n=i("mrSG"),a=i("XNiG"),s=i("wd/R"),o=i("xG9w"),l=i("1G5W"),r=i("3Pt+"),p=i("1yaQ"),d=i("FKr1"),u=i("ofXK"),c=i("kmnG"),h=i("Qu3c"),m=i("qFsG"),g=i("bTqV"),f=i("NFeN"),I=i("/1cH"),D=i("Xi0T"),y=i("iadO"),E=i("d3UM"),P=i("fXoL"),b=i("JLuW"),v=i("TC2u"),S=i("LcQX"),x=i("zcNR"),C=i("BVzC"),w=i("ug40");function T(t,e){if(1&t){const t=P["\u0275\u0275getCurrentView"]();P["\u0275\u0275elementStart"](0,"button",17),P["\u0275\u0275listener"]("click",(function(){return P["\u0275\u0275restoreView"](t),P["\u0275\u0275nextContext"]().viewAllEntries()})),P["\u0275\u0275elementStart"](1,"mat-icon",18),P["\u0275\u0275text"](2," history "),P["\u0275\u0275elementEnd"](),P["\u0275\u0275elementEnd"]()}if(2&t){const t=P["\u0275\u0275nextContext"]();P["\u0275\u0275advance"](1),P["\u0275\u0275property"]("ngClass",t.areEntriesVisible?"is-active":"")}}function _(t,e){1&t&&P["\u0275\u0275element"](0,"div",19)}function k(t,e){1&t&&(P["\u0275\u0275elementStart"](0,"div",4),P["\u0275\u0275text"](1," Cost Centre "),P["\u0275\u0275elementEnd"]())}function O(t,e){if(1&t&&(P["\u0275\u0275elementStart"](0,"div",4),P["\u0275\u0275text"](1),P["\u0275\u0275elementEnd"]()),2&t){const t=P["\u0275\u0275nextContext"]();P["\u0275\u0275advance"](1),P["\u0275\u0275textInterpolate1"](" ",t.cc_name," ")}}function M(t,e){if(1&t&&(P["\u0275\u0275elementStart"](0,"div",22),P["\u0275\u0275elementStart"](1,"div",23),P["\u0275\u0275elementStart"](2,"span",24),P["\u0275\u0275text"](3),P["\u0275\u0275elementEnd"](),P["\u0275\u0275text"](4),P["\u0275\u0275elementEnd"](),P["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit;P["\u0275\u0275advance"](1),P["\u0275\u0275property"]("ngClass",t.textcolor?t.textcolor:"normalFont"),P["\u0275\u0275advance"](2),P["\u0275\u0275textInterpolate"](t.heading?t.heading:""),P["\u0275\u0275advance"](1),P["\u0275\u0275textInterpolate1"](" ",t.objectName," ")}}function A(t,e){if(1&t&&(P["\u0275\u0275elementStart"](0,"div",20),P["\u0275\u0275template"](1,M,5,3,"div",21),P["\u0275\u0275elementEnd"]()),2&t){const t=P["\u0275\u0275nextContext"]();P["\u0275\u0275advance"](1),P["\u0275\u0275property"]("ngForOf",t.tsInlineEditPopupData.inlineApplicationDetails.objectDetails)}}function B(t,e){if(1&t){const t=P["\u0275\u0275getCurrentView"]();P["\u0275\u0275elementStart"](0,"mat-icon",31),P["\u0275\u0275listener"]("click",(function(){return P["\u0275\u0275restoreView"](t),P["\u0275\u0275nextContext"](2).goToPreviousTask()})),P["\u0275\u0275text"](1,"keyboard_arrow_left "),P["\u0275\u0275elementEnd"]()}}function Y(t,e){1&t&&P["\u0275\u0275element"](0,"div",32)}function R(t,e){1&t&&(P["\u0275\u0275elementStart"](0,"div",42),P["\u0275\u0275text"](1," Billable "),P["\u0275\u0275elementEnd"]())}function L(t,e){if(1&t&&(P["\u0275\u0275elementStart"](0,"div",51),P["\u0275\u0275text"](1),P["\u0275\u0275elementEnd"]()),2&t){const t=P["\u0275\u0275nextContext"]().$implicit;P["\u0275\u0275advance"](1),P["\u0275\u0275textInterpolate1"](" ","nonBillable"==t.billingType?"No":"Yes"," ")}}function N(t,e){if(1&t){const t=P["\u0275\u0275getCurrentView"]();P["\u0275\u0275elementStart"](0,"mat-icon",52),P["\u0275\u0275listener"]("click",(function(){P["\u0275\u0275restoreView"](t);const e=P["\u0275\u0275nextContext"]().index;return P["\u0275\u0275nextContext"](3).deleteEntry(e)})),P["\u0275\u0275text"](1,"delete "),P["\u0275\u0275elementEnd"]()}}function V(t,e){1&t&&P["\u0275\u0275element"](0,"div",40)}const F=function(t){return{width:t}};function q(t,e){if(1&t&&(P["\u0275\u0275elementStart"](0,"div",34),P["\u0275\u0275elementStart"](1,"div",2),P["\u0275\u0275elementStart"](2,"div",43),P["\u0275\u0275text"](3),P["\u0275\u0275pipe"](4,"date"),P["\u0275\u0275elementEnd"](),P["\u0275\u0275elementStart"](5,"div",44),P["\u0275\u0275text"](6),P["\u0275\u0275elementEnd"](),P["\u0275\u0275elementStart"](7,"div",45),P["\u0275\u0275text"](8),P["\u0275\u0275elementEnd"](),P["\u0275\u0275elementStart"](9,"div",46),P["\u0275\u0275text"](10),P["\u0275\u0275elementEnd"](),P["\u0275\u0275template"](11,L,2,1,"div",47),P["\u0275\u0275elementStart"](12,"div",33),P["\u0275\u0275elementStart"](13,"mat-icon",48),P["\u0275\u0275text"](14),P["\u0275\u0275elementEnd"](),P["\u0275\u0275template"](15,N,2,0,"mat-icon",49),P["\u0275\u0275elementEnd"](),P["\u0275\u0275template"](16,V,1,0,"div",50),P["\u0275\u0275elementEnd"](),P["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit,i=e.index,n=P["\u0275\u0275nextContext"](3);P["\u0275\u0275advance"](3),P["\u0275\u0275textInterpolate1"](" ",P["\u0275\u0275pipeBind2"](4,13,t.date,"dd-MMM-YY")," "),P["\u0275\u0275advance"](3),P["\u0275\u0275textInterpolate1"](" ",t.hours," "),P["\u0275\u0275advance"](1),P["\u0275\u0275property"]("matTooltip",n.getDayType(t.dayType)),P["\u0275\u0275advance"](1),P["\u0275\u0275textInterpolate1"](" ",n.getDayType(t.dayType)," "),P["\u0275\u0275advance"](1),P["\u0275\u0275property"]("matTooltip",t.location)("ngStyle",P["\u0275\u0275pureFunction1"](16,F,n.tsInlineEditPopupData.hasMultipleTasks?n.isNonBillableOnly?"120px":"70px":n.isNonBillableOnly?"138px":"88px")),P["\u0275\u0275advance"](1),P["\u0275\u0275textInterpolate1"](" ",t.location," "),P["\u0275\u0275advance"](1),P["\u0275\u0275property"]("ngIf",!n.isNonBillableOnly),P["\u0275\u0275advance"](2),P["\u0275\u0275property"]("matTooltip","nonBillable"==t.billingType?"Non Billable":"billable"==t.billingType?"Billable":"Billing Only")("ngClass",t.billingType),P["\u0275\u0275advance"](1),P["\u0275\u0275textInterpolate1"](" ","billable"==t.billingType||"billingOnly"==t.billingType?"attach_money":"money_off",""),P["\u0275\u0275advance"](1),P["\u0275\u0275property"]("ngIf","billingOnly"==t.billingType||n.tsInlineEditPopupData.canEnterTs),P["\u0275\u0275advance"](1),P["\u0275\u0275property"]("ngIf",i!=n.tsInlineEditPopupData.entries.length-1)}}function H(t,e){if(1&t&&(P["\u0275\u0275elementStart"](0,"div",33),P["\u0275\u0275elementStart"](1,"div",34),P["\u0275\u0275elementStart"](2,"div",35),P["\u0275\u0275text"](3," Date "),P["\u0275\u0275elementEnd"](),P["\u0275\u0275elementStart"](4,"div",36),P["\u0275\u0275text"](5," Hours "),P["\u0275\u0275elementEnd"](),P["\u0275\u0275elementStart"](6,"div",37),P["\u0275\u0275text"](7," Hour Type "),P["\u0275\u0275elementEnd"](),P["\u0275\u0275elementStart"](8,"div",38),P["\u0275\u0275text"](9," Location "),P["\u0275\u0275elementEnd"](),P["\u0275\u0275template"](10,R,2,0,"div",39),P["\u0275\u0275elementEnd"](),P["\u0275\u0275element"](11,"div",40),P["\u0275\u0275template"](12,q,17,18,"div",41),P["\u0275\u0275elementEnd"]()),2&t){const t=P["\u0275\u0275nextContext"](2);P["\u0275\u0275advance"](8),P["\u0275\u0275property"]("ngStyle",P["\u0275\u0275pureFunction1"](3,F,t.tsInlineEditPopupData.hasMultipleTasks?t.isNonBillableOnly?"120px":"70px":t.isNonBillableOnly?"138px":"88px")),P["\u0275\u0275advance"](2),P["\u0275\u0275property"]("ngIf",!t.isNonBillableOnly),P["\u0275\u0275advance"](2),P["\u0275\u0275property"]("ngForOf",t.tsInlineEditPopupData.entries)}}function j(t,e){if(1&t){const t=P["\u0275\u0275getCurrentView"]();P["\u0275\u0275elementStart"](0,"mat-icon",53),P["\u0275\u0275listener"]("click",(function(){return P["\u0275\u0275restoreView"](t),P["\u0275\u0275nextContext"](2).goToNextTask()})),P["\u0275\u0275text"](1," keyboard_arrow_right "),P["\u0275\u0275elementEnd"]()}}function W(t,e){if(1&t&&(P["\u0275\u0275elementStart"](0,"div",25),P["\u0275\u0275elementStart"](1,"div",2),P["\u0275\u0275template"](2,B,2,0,"mat-icon",26),P["\u0275\u0275elementStart"](3,"div",27),P["\u0275\u0275template"](4,Y,1,0,"div",28),P["\u0275\u0275template"](5,H,13,5,"div",29),P["\u0275\u0275elementEnd"](),P["\u0275\u0275template"](6,j,2,0,"mat-icon",30),P["\u0275\u0275elementEnd"](),P["\u0275\u0275elementEnd"]()),2&t){const t=P["\u0275\u0275nextContext"]();P["\u0275\u0275property"]("ngClass",t.tsInlineEditPopupData.hasMultipleTasks?"ml-1 mr-1":"ml-3 mr-3"),P["\u0275\u0275advance"](2),P["\u0275\u0275property"]("ngIf",t.tsInlineEditPopupData.hasMultipleTasks),P["\u0275\u0275advance"](1),P["\u0275\u0275property"]("ngClass",t.tsInlineEditPopupData.hasMultipleTasks?"col-10-5 pl-0":"pl-1"),P["\u0275\u0275advance"](1),P["\u0275\u0275property"]("ngIf",t.tsInlineEditPopupData.entries.length>0),P["\u0275\u0275advance"](1),P["\u0275\u0275property"]("ngIf",t.tsInlineEditPopupData.entries.length>0),P["\u0275\u0275advance"](1),P["\u0275\u0275property"]("ngIf",t.tsInlineEditPopupData.hasMultipleTasks)}}function G(t,e){1&t&&(P["\u0275\u0275elementStart"](0,"div",54),P["\u0275\u0275elementStart"](1,"div",55),P["\u0275\u0275text"](2," Add Hours "),P["\u0275\u0275elementEnd"](),P["\u0275\u0275elementEnd"]())}function U(t,e){if(1&t){const t=P["\u0275\u0275getCurrentView"]();P["\u0275\u0275elementStart"](0,"mat-icon",67),P["\u0275\u0275listener"]("click",(function(){P["\u0275\u0275restoreView"](t),P["\u0275\u0275nextContext"]();const e=P["\u0275\u0275reference"](7);return P["\u0275\u0275nextContext"]().tsInlineEditPopupData.canDateBeChanged?e.open():""})),P["\u0275\u0275text"](1," calendar_today "),P["\u0275\u0275elementEnd"]()}if(2&t){const t=P["\u0275\u0275nextContext"](2);P["\u0275\u0275property"]("disabled",t.tsInlineEditPopupData.isSubmissionDisabled)}}function z(t,e){if(1&t&&(P["\u0275\u0275elementStart"](0,"mat-option",68),P["\u0275\u0275text"](1),P["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit;P["\u0275\u0275propertyInterpolate1"]("matTooltip","",t.name," "),P["\u0275\u0275property"]("value",t),P["\u0275\u0275advance"](1),P["\u0275\u0275textInterpolate1"](" ",t.name," ")}}function X(t,e){if(1&t){const t=P["\u0275\u0275getCurrentView"]();P["\u0275\u0275elementStart"](0,"div",56),P["\u0275\u0275elementStart"](1,"div",57),P["\u0275\u0275elementStart"](2,"mat-date-range-input",58),P["\u0275\u0275listener"]("click",(function(){P["\u0275\u0275restoreView"](t);const e=P["\u0275\u0275reference"](7);return P["\u0275\u0275nextContext"]().tsInlineEditPopupData.canDateBeChanged?e.open():""})),P["\u0275\u0275element"](3,"input",59),P["\u0275\u0275element"](4,"input",60),P["\u0275\u0275elementEnd"](),P["\u0275\u0275template"](5,U,2,1,"mat-icon",61),P["\u0275\u0275element"](6,"mat-date-range-picker",null,62),P["\u0275\u0275elementEnd"](),P["\u0275\u0275elementStart"](8,"div",63),P["\u0275\u0275elementStart"](9,"input",64),P["\u0275\u0275listener"]("ngModelOptions",(function(){return{debounce:1e3}}))("ngModelChange",(function(e){return P["\u0275\u0275restoreView"](t),P["\u0275\u0275nextContext"]().tsInlineEditPopupData.costCentreItem.location=e}))("ngModelChange",(function(){P["\u0275\u0275restoreView"](t);const e=P["\u0275\u0275nextContext"]();return e.changeFilteredLocationList(e.tsInlineEditPopupData.costCentreItem.location)})),P["\u0275\u0275elementEnd"](),P["\u0275\u0275elementStart"](10,"mat-autocomplete",null,65),P["\u0275\u0275template"](12,z,2,3,"mat-option",66),P["\u0275\u0275elementEnd"](),P["\u0275\u0275elementEnd"](),P["\u0275\u0275elementEnd"]()}if(2&t){const t=P["\u0275\u0275reference"](7),e=P["\u0275\u0275reference"](11),i=P["\u0275\u0275nextContext"]();P["\u0275\u0275advance"](2),P["\u0275\u0275property"]("disabled",i.tsInlineEditPopupData.isSubmissionDisabled)("ngStyle",P["\u0275\u0275pureFunction1"](13,F,i.tsInlineEditPopupData.canDateBeChanged?"175px":"200px"))("min",i.tsInlineEditPopupData.inlineApplicationDetails.start_date)("max",i.tsInlineEditPopupData.inlineApplicationDetails.end_date)("rangePicker",t),P["\u0275\u0275advance"](1),P["\u0275\u0275property"]("formControl",i.dateRangePickerStart),P["\u0275\u0275advance"](1),P["\u0275\u0275property"]("formControl",i.dateRangePickerEnd),P["\u0275\u0275advance"](1),P["\u0275\u0275property"]("ngIf",i.tsInlineEditPopupData.canDateBeChanged),P["\u0275\u0275advance"](4),P["\u0275\u0275property"]("matTooltip",i.tsInlineEditPopupData.costCentreItem.location)("ngModel",i.tsInlineEditPopupData.costCentreItem.location)("disabled",i.tsInlineEditPopupData.isSubmissionDisabled)("matAutocomplete",e),P["\u0275\u0275advance"](3),P["\u0275\u0275property"]("ngForOf",i.filteredLocationList)}}function J(t,e){if(1&t){const t=P["\u0275\u0275getCurrentView"]();P["\u0275\u0275elementStart"](0,"div",78),P["\u0275\u0275elementStart"](1,"input",79),P["\u0275\u0275listener"]("ngModelChange",(function(e){return P["\u0275\u0275restoreView"](t),P["\u0275\u0275nextContext"](2).changeDefaultHours(e)})),P["\u0275\u0275elementEnd"](),P["\u0275\u0275element"](2,"input",80),P["\u0275\u0275elementStart"](3,"mat-icon",81),P["\u0275\u0275text"](4,"timer"),P["\u0275\u0275elementEnd"](),P["\u0275\u0275elementEnd"]()}if(2&t){const t=P["\u0275\u0275nextContext"](2);P["\u0275\u0275advance"](1),P["\u0275\u0275property"]("ngModel",t.tsInlineEditPopupData.defaultHours)("disabled",t.tsInlineEditPopupData.isSubmissionDisabled||t.isWeekOffDisabled),P["\u0275\u0275advance"](1),P["\u0275\u0275property"]("ngModel","Hrs")}}function Q(t,e){if(1&t){const t=P["\u0275\u0275getCurrentView"]();P["\u0275\u0275elementStart"](0,"div",78),P["\u0275\u0275elementStart"](1,"input",82),P["\u0275\u0275listener"]("ngModelChange",(function(e){return P["\u0275\u0275restoreView"](t),P["\u0275\u0275nextContext"](2).changeDefaultHours(e)})),P["\u0275\u0275elementEnd"](),P["\u0275\u0275element"](2,"input",80),P["\u0275\u0275elementStart"](3,"mat-icon",81),P["\u0275\u0275text"](4,"timer"),P["\u0275\u0275elementEnd"](),P["\u0275\u0275elementEnd"]()}if(2&t){const t=P["\u0275\u0275nextContext"](2);P["\u0275\u0275advance"](1),P["\u0275\u0275property"]("ngModel",t.tsInlineEditPopupData.defaultHours)("disabled",t.tsInlineEditPopupData.isSubmissionDisabled||t.isWeekOffDisabled),P["\u0275\u0275advance"](1),P["\u0275\u0275property"]("ngModel","Hrs")}}function K(t,e){if(1&t&&(P["\u0275\u0275elementStart"](0,"mat-option",83),P["\u0275\u0275text"](1),P["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit;P["\u0275\u0275property"]("ngValue",t.value)("value",t.value),P["\u0275\u0275advance"](1),P["\u0275\u0275textInterpolate1"](" ",t.name,"")}}function $(t,e){if(1&t){const t=P["\u0275\u0275getCurrentView"]();P["\u0275\u0275elementStart"](0,"div",69),P["\u0275\u0275template"](1,J,5,3,"div",70),P["\u0275\u0275template"](2,Q,5,3,"div",70),P["\u0275\u0275elementStart"](3,"mat-select",71),P["\u0275\u0275listener"]("ngModelChange",(function(e){return P["\u0275\u0275restoreView"](t),P["\u0275\u0275nextContext"]().tsInlineEditPopupData.defaultDayType=e}))("ngModelChange",(function(){return P["\u0275\u0275restoreView"](t),P["\u0275\u0275nextContext"]().onDayTypeChange()})),P["\u0275\u0275template"](4,K,2,3,"mat-option",72),P["\u0275\u0275elementEnd"](),P["\u0275\u0275elementStart"](5,"div",73),P["\u0275\u0275elementStart"](6,"mat-icon",74),P["\u0275\u0275listener"]("click",(function(){return P["\u0275\u0275restoreView"](t),P["\u0275\u0275nextContext"]().changeBillableState()})),P["\u0275\u0275text"](7),P["\u0275\u0275elementEnd"](),P["\u0275\u0275element"](8,"input",75),P["\u0275\u0275elementEnd"](),P["\u0275\u0275elementStart"](9,"button",76),P["\u0275\u0275listener"]("click",(function(){return P["\u0275\u0275restoreView"](t),P["\u0275\u0275nextContext"]().submitEntry()})),P["\u0275\u0275elementStart"](10,"mat-icon",77),P["\u0275\u0275text"](11,"done_all"),P["\u0275\u0275elementEnd"](),P["\u0275\u0275elementEnd"](),P["\u0275\u0275elementEnd"]()}if(2&t){const t=P["\u0275\u0275nextContext"]();P["\u0275\u0275advance"](1),P["\u0275\u0275property"]("ngIf",!t.is_decimal),P["\u0275\u0275advance"](1),P["\u0275\u0275property"]("ngIf",t.is_decimal),P["\u0275\u0275advance"](1),P["\u0275\u0275property"]("ngModel",t.tsInlineEditPopupData.defaultDayType)("disabled",t.tsInlineEditPopupData.isSubmissionDisabled),P["\u0275\u0275advance"](1),P["\u0275\u0275property"]("ngForOf",t.dayTypeList),P["\u0275\u0275advance"](2),P["\u0275\u0275property"]("ngClass",t.tsInlineEditPopupData.defaultBillingType)("matTooltip",t.isNonBillableOnly?"":"nonBillable"==t.tsInlineEditPopupData.defaultBillingType?"Mark as Billable":"billable"==t.tsInlineEditPopupData.defaultBillingType?"Mark as Billing Only":t.tsInlineEditPopupData.canEnterTs?"Mark as Not Billable":t.tsInlineEditPopupData.canEnterTs?"":"Monthly Timesheet Submission Blocked - Only Billing Only Data Can Be Entered"),P["\u0275\u0275advance"](1),P["\u0275\u0275textInterpolate1"]("","billable"==t.tsInlineEditPopupData.defaultBillingType||"billingOnly"==t.tsInlineEditPopupData.defaultBillingType?"attach_money":"money_off"," "),P["\u0275\u0275advance"](1),P["\u0275\u0275property"]("ngModel","billable"==t.tsInlineEditPopupData.defaultBillingType?"Billable":"nonBillable"==t.tsInlineEditPopupData.defaultBillingType?"Non Billable":"Billing Only"),P["\u0275\u0275advance"](1),P["\u0275\u0275property"]("disabled",t.tsInlineEditPopupData.isSubmissionDisabled)}}const Z={parse:{dateInput:"DD-MMM-YY"},display:{dateInput:"DD-MMM-YY",monthYearLabel:"MMMM YYYY",dateA11yLabel:"LL",monthYearA11yLabel:"DD-MMM-YY"}};let tt=(()=>{class t{constructor(t,e,i,n,o,l){this.sharedLazyLoadedComponentsService=t,this.tsInlineEntryPopupService=e,this.utilityService=i,this.tsSubmissionPrimaryService=n,this.errorService=o,this.tsPrimaryService=l,this._onDestroy=new a.b,this.costCentreList=[],this.firstTime=!0,this.isWeekOffDisabled=!1,this.currentDayType="Regular",this.dayTypeList=[],this.dateRangePickerStart=new r.j(s()),this.dateRangePickerEnd=new r.j(s()),this.formattedDateRange=s().hour(15).format("YYYY-MM-DD"),this.formattedDateRangeEnd=s().hour(15).format("YYYY-MM-DD"),this.defaultDate=s().format("DD-MMM-YYYY"),this.defaultDeleteDate=s().format("DD-MMM-YYYY"),this.week=Math.ceil((s().date()-1-s().day())/7)+1,this.monthEndDateVal=31,this.monthStartInterval=s().hour(15).minute(0).second(0).millisecond(0),this.areEntriesVisible=!1,this.filteredLocationList=[],this.locationList=[],this.isNonBillableOnly=!1}ngOnInit(){this.tsInlineEntryPopupSubscription=this.tsInlineEntryPopupService.inlineEditActiveData.subscribe(t=>{t&&(this.tsInlineEditPopupData=t,this.tsInlineEditPopupData.defaultWfProperties=this.tsInlineEditPopupData.activeWfProperties,this.monthEndDateVal="END"==this.tsInlineEntryPopupService.tsProperties.month_end_date?"END":parseInt(this.tsInlineEntryPopupService.tsProperties.month_end_date),this.is_decimal=this.tsInlineEntryPopupService.tsProperties.is_decimal,this.cc_name_config=this.tsInlineEntryPopupService.tsProperties.ts_ui_change_and_download_icon,this.cc_name=this.tsInlineEntryPopupService.tsProperties.ts_ui_text,this.monthStartInterval="END"==this.monthEndDateVal?s(this.monthStartInterval).date(1):s().date()>this.monthEndDateVal?s(this.monthStartInterval).date(this.monthEndDateVal).add(1,"days"):s(this.monthStartInterval).date(this.monthEndDateVal).subtract(1,"months").add(1,"days"),this.tsInlineEditPopupData.canDateBeChanged||s(this.tsInlineEditPopupData.defaultDate).hour(15).minute(0).second(0).millisecond(0).isAfter(this.monthStartInterval)?(this.dateRangePickerStart.setValue(s(this.tsInlineEditPopupData.defaultDate)),this.dateRangePickerEnd.setValue(s(this.tsInlineEditPopupData.defaultDate))):(this.dateRangePickerStart.setValue(this.monthStartInterval),this.dateRangePickerEnd.setValue(this.monthStartInterval)),this.formattedDateRange=s(this.dateRangePickerStart.value).hour(15).format("YYYY-MM-DD"),this.formattedDateRangeEnd=s(this.dateRangePickerEnd.value).hour(15).format("YYYY-MM-DD"),(36==this.tsInlineEditPopupData.inlineApplicationDetails.applicationId||151==this.tsInlineEditPopupData.inlineApplicationDetails.applicationId&&this.tsInlineEditPopupData.inlineApplicationDetails.overallAccess||35==this.tsInlineEditPopupData.inlineApplicationDetails.applicationId||37==this.tsInlineEditPopupData.inlineApplicationDetails.applicationId)&&(this.isNonBillableOnly=!0,this.tsInlineEditPopupData.defaultBillingType="nonBillable"),this.areEntriesVisible=!1)}),this.tsInlineEntryOverlaySubscription=this.tsInlineEntryPopupService.inlineEditPopup.subscribe(t=>{this.overlayRef=t}),this.getDayList(),this.getLocationList()}submitEntry(){let t=this.checkItisValidDate();this.tsSubmissionPrimaryService.resubmissionData&&(t=!0),s(this.dateRangePickerStart.value).isValid()?""==this.tsInlineEditPopupData.costCentreItem.location?(this.utilityService.showToastMessage("Enter valid location !"),this.tsInlineEditPopupData.isSubmissionDisabled=!1):"WO"!=this.tsInlineEditPopupData.defaultDayType&&this.tsInlineEditPopupData.defaultHours<=0?(this.utilityService.showToastMessage("Enter valid hours !"),this.tsInlineEditPopupData.isSubmissionDisabled=!1):this.is_decimal||Number.isInteger(this.tsInlineEditPopupData.defaultHours)?t?s(this.dateRangePickerStart.value).format("YYYY-MM-DD")<s(this.tsInlineEntryPopupService.tsProperties.doj).format("YYYY-MM-DD")?(this.utilityService.showToastMessage("You cannot Submit Hours Before Your Date Of Joining !"),this.tsInlineEditPopupData.isSubmissionDisabled=!1):(this.defaultDate=s(this.dateRangePickerStart.value).format("DD-MMM-YYYY"),this.firstTime=!0,this.costCentreList=[],this.submitEntryContinuation()):(this.utilityService.showToastMessage("You cannot Submit hours for the Previous Week As Submission Deadline is Over! Kindly contact the HR Team for assistance."),this.tsInlineEditPopupData.isSubmissionDisabled=!1):(this.utilityService.showToastMessage("Decimal Hours Is Not Allowed !"),this.tsInlineEditPopupData.isSubmissionDisabled=!1):(this.utilityService.showToastMessage("Enter at least valid start date in range !"),this.tsInlineEditPopupData.isSubmissionDisabled=!1),this.closePopup()}submitEntryContinuation(){(this.firstTime&&!this.dateRangePickerEnd.value||s(this.defaultDate).isSameOrBefore(this.dateRangePickerEnd.value))&&(this.tsInlineEditPopupData.isSubmissionDisabled=!0,this.week=Math.ceil((s(this.defaultDate).date()-1-s(this.defaultDate).day())/7)+1,this.formattedDateRange=s(this.defaultDate).hour(15).format("YYYY-MM-DD"),this.formattedDateRangeEnd=this.shouldTakeCurrentMonth(s(this.defaultDate).date())?s(this.defaultDate).hour(15).format("YYYY-MM-DD"):s(this.defaultDate).add(1,"month").hour(15).format("YYYY-MM-DD"),this.submitEntryIteration())}submitEntryIteration(){let t=0;for(let e of this.tsInlineEditPopupData.entries)"nonBillable"!=e.billingType&&(t+=e.hours);this.tsInlineEntryPopupService.appVerificationData={tsInlineEditPopupData:this.tsInlineEditPopupData,actualHours:t},this.tsInlineEntryPopupService.isAppSubmitConditionsSatisfied().then(t=>{if(t){let t={associateOId:this.tsInlineEditPopupData.currentUserOid,monthYearDate:this.formattedDateRange,costCentre:this.tsInlineEditPopupData.costCentreItem.costCentre,costCentreName:this.tsInlineEditPopupData.costCentreItem.costCentreDescription,location:this.tsInlineEditPopupData.costCentreItem.location,hoursValue:this.tsInlineEditPopupData.defaultHours,dayType:this.tsInlineEditPopupData.defaultDayType,isFromDelete:!1,submissionId:0};"billingOnly"==this.tsInlineEditPopupData.defaultBillingType||this.tsInlineEditPopupData.canOverideData?this.verificationDetails(t):this.tsInlineEntryPopupService.verifyTsDetailsForInline(t).then(e=>{e.allowedToProceed?this.verificationDetails(t):(this.utilityService.showToastMessage(e.reason+" on "+this.defaultDate+" !"),this.tsInlineEditPopupData.isSubmissionDisabled=!1)})}})}verificationDetails(t){return Object(n.c)(this,void 0,void 0,(function*(){this.currentUserGroupDayQuota=yield this.tsPrimaryService.getAssocGroupDayQuotaForInlineEdit(this.tsInlineEditPopupData.currentUserOid);let e=o.where(this.currentUserGroupDayQuota,{day_type:"r",interval_code:"d"});this.daily_hour_quota=e[0]?parseFloat(e[0].quota):0;let i=o.where(this.currentUserGroupDayQuota,{day_type:"rm",interval_code:"d"});this.max_hour_quota=i[0]?parseFloat(i[0].quota):0,this.tsInlineEntryPopupService.retrieveTsHoursDataForInline(t).then(t=>{let e=t[0].daily_r_hours?t[0].daily_r_hours:0,i=t[0].monthly_ot_hours?t[0].monthly_ot_hours:0;if("WO"!=this.tsInlineEditPopupData.defaultDayType&&this.tsInlineEditPopupData.defaultHours+(t[0].daily_hours?t[0].daily_hours:0)>24&&!this.tsInlineEditPopupData.canOverideData)this.utilityService.showToastMessage("Entries booked in a day cannot exceed 24 hours on "+this.defaultDate+" !"),this.tsInlineEditPopupData.isSubmissionDisabled=!1;else if("billingOnly"!=this.tsInlineEditPopupData.defaultBillingType&&"R"==this.tsInlineEditPopupData.defaultDayType&&this.tsInlineEditPopupData.defaultHours+e>this.max_hour_quota&&!this.tsInlineEditPopupData.canOverideData)this.utilityService.showToastMessage("Regular (R) type cannot be booked for more than "+this.max_hour_quota+" hours in a day on "+this.defaultDate+" !"),this.tsInlineEditPopupData.isSubmissionDisabled=!1;else if("billingOnly"!=this.tsInlineEditPopupData.defaultBillingType&&"OT"==this.tsInlineEditPopupData.defaultDayType&&e<8&&!this.tsInlineEditPopupData.canOverideData)this.utilityService.showToastMessage("Overtime (OT) type cannot be booked before 8 regular hours for the day is booked on "+this.defaultDate+" !"),this.tsInlineEditPopupData.isSubmissionDisabled=!1;else{let t={cost_centre:this.tsInlineEditPopupData.costCentreItem.costCentre,cost_centre_description:this.tsInlineEditPopupData.costCentreItem.costCentreDescription,cost_centre_type:this.tsInlineEditPopupData.costCentreItem.costCentreType};if("billable"==this.tsInlineEditPopupData.defaultBillingType||"nonBillable"==this.tsInlineEditPopupData.defaultBillingType)this.tsInlineEditPopupData.activeWfProperties=o.where(this.tsInlineEditPopupData.wfProperties,{sub_application_id:"R"})[0],this.firstTime?this.tsSubmissionPrimaryService.searchCostCentresFn(this.tsInlineEditPopupData.costCentreItem.costCentre,this.tsInlineEditPopupData.currentUserOid).then(e=>{this.costCentreList=e,this.completeGetApprovers(i,t)}):this.completeGetApprovers(i,t);else{let t={actual_hours:this.tsInlineEditPopupData.defaultHours,actual_date:s(this.defaultDate).format("YYYY-MM-DD"),is_billable:!0,billing_type:this.tsInlineEditPopupData.defaultBillingType,day_type:this.tsInlineEditPopupData.defaultDayType};this.tsInlineEntryPopupService.createBillableData={tsInlineEditPopupData:this.tsInlineEditPopupData,currentActualHours:t,timesheetId:0},this.tsInlineEntryPopupService.createBillableFunction().then(e=>{this.reflectInUi(t,e[0].actual_hours[e[0].actual_hours.length-1],"WO")})}}})}))}completeGetApprovers(t,e){this.resolveActiveWfPropertiesOfCostCentre(t),this.tsInlineEntryPopupService.getApproversOfCostCentre(this.tsInlineEditPopupData.activeWfProperties,this.tsInlineEditPopupData.currentUserOid,e).then(t=>{this.tsInlineEditPopupData.costCentreItem.approvers=t,this.completeTsSubmission()})}resolveActiveWfPropertiesOfCostCentre(t){let e=t;"OT"==this.tsInlineEditPopupData.defaultDayType&&(e+=this.tsInlineEditPopupData.defaultHours);let i="R";e>0&&(i="OT");let n=[];n=o.where(this.tsInlineEditPopupData.wfProperties,this.costCentreList.length>0&&this.costCentreList[0].obj_org_code?{sub_application_id:i,org_code:this.costCentreList[0].obj_org_code}:this.costCentreList.length>0&&this.costCentreList[0].p_and_l_id?{sub_application_id:i,org_code:this.costCentreList[0].p_and_l_id}:{sub_application_id:i,org_code:null});for(let a of n)e>=a.min_value&&e<=a.max_value&&(this.tsInlineEditPopupData.activeWfProperties=a)}completeTsSubmission(){console.log(this.tsInlineEditPopupData.canOverideData);let t={associateOId:this.tsInlineEditPopupData.currentUserOid,formattedMonthYearDate:this.formattedDateRange,formattedMonthYearDateEnd:this.formattedDateRangeEnd,monthYearDate:this.formattedDateRange,workflowId:this.tsInlineEditPopupData.defaultWfProperties.workflow_id,isAggregationAllowed:this.tsInlineEditPopupData.defaultWfProperties.aggregation_allowed,statusCode:"D",originalInitiatorOId:this.tsInlineEditPopupData.currentUserOid,submissionDetails:[{objectType:this.tsInlineEditPopupData.costCentreItem.costCentreType,objectDescription:this.tsInlineEditPopupData.costCentreItem.costCentreDescription,objectValue:this.tsInlineEditPopupData.costCentreItem.costCentre,location:this.tsInlineEditPopupData.costCentreItem.location,locationId:o.filter(this.locationList,{name:this.tsInlineEditPopupData.costCentreItem.location})[0].locationID,week:this.week,billingType:this.tsInlineEditPopupData.canOverideData?"NA":this.tsInlineEditPopupData.defaultBillingType,inlineApplicationDetails:this.tsInlineEditPopupData.canOverideData?null:JSON.parse(JSON.stringify(this.tsInlineEditPopupData.inlineApplicationDetails)),submissionId:"",submissionStatus:"D",dayType:this.tsInlineEditPopupData.defaultDayType,dateValue:s(this.defaultDate).format("YYYY-MM-DD"),hours:this.tsInlineEditPopupData.defaultHours,approvers:this.tsInlineEditPopupData.costCentreItem.approvers,comments:[]}],costCentreItems:JSON.stringify([{costCentre:this.tsInlineEditPopupData.costCentreItem.costCentre,location:this.tsInlineEditPopupData.costCentreItem.location}]),shouldDeleteNotWork:!0,updateAllApproversOfPair:!0,canOverrideData:this.tsInlineEditPopupData.canOverideData};this.tsSubmissionPrimaryService.insertTsSubm(t,[],!1).subscribe(t=>Object(n.c)(this,void 0,void 0,(function*(){if(t.submissionResponse&&t.submissionResponse.length>0){let e=t.submissionResponse[t.submissionResponse.length-1],i=null,n=e.submission_id;i={timesheet_id:n,actual_hours:this.tsInlineEditPopupData.defaultHours,actual_date:s(this.defaultDate).format("YYYY-MM-DD"),is_billable:"billable"==this.tsInlineEditPopupData.defaultBillingType,billing_type:this.tsInlineEditPopupData.defaultBillingType,day_type:this.tsInlineEditPopupData.defaultDayType},this.tsInlineEntryPopupService.createBillableData={tsInlineEditPopupData:this.tsInlineEditPopupData,currentActualHours:i,timesheetId:n},this.tsInlineEntryPopupService.createBillableFunction().then(t=>{this.reflectInUi(e,t[0].actual_hours[t[0].actual_hours.length-1],"W")})}})))}reflectInUi(t,e,i){let n=!1;if(this.tsInlineEditPopupData.entries.length>0)for(let a of this.tsInlineEditPopupData.entries)console.log(a),a.submissionId==t.submission_id&&a.billingType==t.billing_type&&a.dayType==t.day_type&&s(a.date).format("YYYY-M-D")==t.date&&(a.hours=t.hours,n=!0);n||(console.log("Creating New Entry"),this.entryItem={_id:e._id?e._id:0,submissionId:t?t.submission_id:0,date:this.defaultDate,hours:this.tsInlineEditPopupData.defaultHours,dayType:this.tsInlineEditPopupData.defaultDayType,location:this.tsInlineEditPopupData.costCentreItem.location,billingType:this.tsInlineEditPopupData.defaultBillingType},t._id=e._id?e._id:0,this.tsInlineEditPopupData.entries.push(this.entryItem)),this.tsInlineEditPopupData.totalHours+=this.tsInlineEditPopupData.defaultHours,this.tsInlineEditPopupData.isSubmissionDisabled=!1,this.utilityService.showToastMessage("Timesheet entry saved successfully on "+this.defaultDate+" !"),this.tsInlineEntryPopupService.setInlineEditCallbackSubject({method:"S",type:i,data:"W"==i?t.submission_id:t,tsData:t,entryItem:this.entryItem}),this.defaultDate=s(this.defaultDate).add(1,"day").format("DD-MMM-YY"),this.firstTime=!1,this.submitEntryContinuation()}deleteEntry(t){if(!this.tsInlineEditPopupData.isSubmissionDisabled){this.defaultDeleteDate=s(this.tsInlineEditPopupData.entries[t].date).format("DD-MMM-YYYY"),this.tsInlineEditPopupData.isSubmissionDisabled=!0;let e=this.tsInlineEditPopupData.entries[t].submissionId;"billable"==this.tsInlineEditPopupData.entries[t].billingType||"nonBillable"==this.tsInlineEditPopupData.entries[t].billingType?this.tsInlineEntryPopupService.verifyTsDetailsForInline({associateOId:this.tsInlineEditPopupData.currentUserOid,monthYearDate:this.formattedDateRange,costCentre:this.tsInlineEditPopupData.costCentreItem.costCentre,location:this.tsInlineEditPopupData.costCentreItem.location,hoursValue:this.tsInlineEditPopupData.defaultHours,isFromDelete:!0,submissionId:e}).then(i=>{i.allowedToProceed?this.tsSubmissionPrimaryService.markTsItemInactive(e).subscribe(i=>Object(n.c)(this,void 0,void 0,(function*(){let i=null;this.tsInlineEditPopupData.entries[t].timesheet_id=e,i=this.tsInlineEditPopupData.entries[t],this.tsInlineEntryPopupService.deleteBillableData={tsInlineEditPopupData:this.tsInlineEditPopupData,dataIndex:t,timesheetId:e,actualHours:i},this.tsInlineEntryPopupService.deleteBillableFunction(),this.reflectDeletionInUi(t,e,0,"W")}))):(this.utilityService.showToastMessage(i.reason+" on "+this.defaultDeleteDate+" !"),this.tsInlineEditPopupData.isSubmissionDisabled=!1)}):(this.tsInlineEntryPopupService.deleteBillableData={tsInlineEditPopupData:this.tsInlineEditPopupData,dataIndex:t,timesheetId:0,actualHours:this.tsInlineEditPopupData.entries[t]},this.tsInlineEntryPopupService.deleteBillableFunction(),this.reflectDeletionInUi(t,e,this.tsInlineEditPopupData.entries[t]._id,"WO"))}}reflectDeletionInUi(t,e,i,n){this.tsInlineEditPopupData.totalHours-=this.tsInlineEditPopupData.entries[t].hours;let a=[];for(let s=t+1;s<this.tsInlineEditPopupData.entries.length;s++)"nonBillable"==this.tsInlineEditPopupData.entries[s].billingType&&a.push(this.tsInlineEditPopupData.entries[s]);this.tsInlineEditPopupData.entries.splice(t,1),this.utilityService.showToastMessage("Timesheet entry deleted successfully on "+this.defaultDeleteDate+" !"),this.tsInlineEditPopupData.isSubmissionDisabled=!1,this.tsInlineEntryPopupService.setInlineEditCallbackSubject({method:"D",type:n,submissionId:e,mongoId:i,billingOnly:a})}changeDefaultHours(t){this.tsInlineEditPopupData.defaultHours=t}shouldTakeCurrentMonth(t){let e="END"==this.monthEndDateVal;return!e&&parseInt(t)<=this.monthEndDateVal&&(e=!0),e}getDayType(t){return o.where(this.dayTypeList,{value:t})[0].name}onDayTypeChange(){"WO"==this.tsInlineEditPopupData.defaultDayType?(this.tsInlineEditPopupData.defaultHours=0,this.isWeekOffDisabled=!0):("WO"==this.currentDayType&&(this.tsInlineEditPopupData.defaultHours=this.tsInlineEditPopupData.defaultBackupHours),this.isWeekOffDisabled=!1),this.currentDayType=this.tsInlineEditPopupData.defaultDayType}closePopup(){var t;null===(t=this.overlayRef)||void 0===t||t.dispose()}viewAllEntries(){this.areEntriesVisible=!this.areEntriesVisible}changeFilteredLocationList(t){this.tsInlineEditPopupData.costCentreItem.location="",t.name?this.tsInlineEditPopupData.costCentreItem.location=t.name:this.searchLocation(t).then(t=>{this.filteredLocationList=t})}searchLocation(t){return new Promise(e=>{this.sharedLazyLoadedComponentsService.searchLocation(t).pipe(Object(l.a)(this._onDestroy)).subscribe(t=>Object(n.c)(this,void 0,void 0,(function*(){let i=[];"S"==t.messType&&t.data.length>0&&(i=t.data),e(i)})),t=>{this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Error while searching location !",t&&t.params?t.params:t&&t.error?t.error.params:{})})})}handleKeyboardEvent(t){"Enter"==t.key&&this.submitEntry()}goToPreviousTask(){0==this.tsInlineEditPopupData.currentTaskItemIndex?this.tsInlineEditPopupData.currentTaskItemIndex=this.tsInlineEditPopupData.taskItems.length-1:this.tsInlineEditPopupData.currentTaskItemIndex-=1,this.tsInlineEditPopupData.inlineApplicationDetails=this.tsInlineEditPopupData.taskItems[this.tsInlineEditPopupData.currentTaskItemIndex].inlineApplicationDetails,this.tsInlineEditPopupData.entries=this.tsInlineEditPopupData.taskItems[this.tsInlineEditPopupData.currentTaskItemIndex].entries,this.tsInlineEditPopupData.totalHours=this.tsInlineEditPopupData.taskItems[this.tsInlineEditPopupData.currentTaskItemIndex].totalHours}goToNextTask(){this.tsInlineEditPopupData.currentTaskItemIndex=this.tsInlineEditPopupData.currentTaskItemIndex==this.tsInlineEditPopupData.taskItems.length-1?0:this.tsInlineEditPopupData.currentTaskItemIndex+1,this.tsInlineEditPopupData.inlineApplicationDetails=this.tsInlineEditPopupData.taskItems[this.tsInlineEditPopupData.currentTaskItemIndex].inlineApplicationDetails,this.tsInlineEditPopupData.entries=this.tsInlineEditPopupData.taskItems[this.tsInlineEditPopupData.currentTaskItemIndex].entries,this.tsInlineEditPopupData.totalHours=this.tsInlineEditPopupData.taskItems[this.tsInlineEditPopupData.currentTaskItemIndex].totalHours}changeBillableState(){console.log(this.tsInlineEditPopupData.isFromTs),this.tsInlineEditPopupData.isSubmissionDisabled||this.isNonBillableOnly||(this.tsInlineEditPopupData.isFromTs?this.tsInlineEditPopupData.defaultBillingType="nonBillable"==this.tsInlineEditPopupData.defaultBillingType?"billable":"nonBillable":this.tsInlineEditPopupData.canEnterTs&&(this.tsInlineEditPopupData.defaultBillingType="nonBillable"!=this.tsInlineEditPopupData.defaultBillingType||this.tsInlineEditPopupData.allowOnlyNonBillable?"billable"==this.tsInlineEditPopupData.defaultBillingType?"billingOnly":"nonBillable":"billable"))}checkItisValidDate(){if(1==this.tsInlineEntryPopupService.tsProperties.timesheet_type_flag){let t,e=this.dateRangePickerStart.value,i=s().format("YYYY-MM-DD"),n=this.tsInlineEntryPopupService.tsProperties.weekly_subm_cut_off_date,a=s(e).day();0==a?t=7:1==a?t=6:2==a?t=5:3==a?t=4:4==a?t=3:5==a?t=2:6==a&&(t=1);let o=s(e).add(t+n,"days").format("YYYY-MM-DD"),l=s(i).diff(s(o),"days");return console.log(l),i==o?!(s().hour()>=this.tsInlineEntryPopupService.tsProperties.weekly_subm_cut_off_hour&&s().minutes()>=this.tsInlineEntryPopupService.tsProperties.weekly_subm_cut_off_minute):!(i>o)}return!0}getDayList(){this.tsSubmissionPrimaryService.getDataForInlineSummary().subscribe(t=>Object(n.c)(this,void 0,void 0,(function*(){if(t.data&&t.data.length>0){console.log(t.data);let e=o.where(t.data,{is_for_inline_edit:1});for(let t of e)this.dayTypeList.push({name:t.name,value:t.code})}})))}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete(),this.tsInlineEntryPopupSubscription&&this.tsInlineEntryPopupSubscription.unsubscribe(),this.tsInlineEntryPopupService.setInlineEditPopupSubject({}),this.tsInlineEntryPopupService.inlineEditCallbackSubscription&&this.tsInlineEntryPopupService.inlineEditCallbackSubscription.unsubscribe(),this.tsInlineEntryPopupService.setInlineEditCallbackSubject({})}getLocationList(){this.tsSubmissionPrimaryService.getLocationList().pipe(Object(l.a)(this._onDestroy)).subscribe(t=>Object(n.c)(this,void 0,void 0,(function*(){"S"==t.messType&&t.data.length>0?this.locationList=t.data:this.utilityService.showToastMessage(t.messText)})),t=>{this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Error while retrieving location list",t&&t.params?t.params:t&&t.error?t.error.params:{})})}}return t.\u0275fac=function(e){return new(e||t)(P["\u0275\u0275directiveInject"](b.a),P["\u0275\u0275directiveInject"](v.a),P["\u0275\u0275directiveInject"](S.a),P["\u0275\u0275directiveInject"](x.a),P["\u0275\u0275directiveInject"](C.a),P["\u0275\u0275directiveInject"](w.a))},t.\u0275cmp=P["\u0275\u0275defineComponent"]({type:t,selectors:[["ts-inline-entry-popup"]],hostBindings:function(t,e){1&t&&P["\u0275\u0275listener"]("keypress",(function(t){return e.handleKeyboardEvent(t)}),!1,P["\u0275\u0275resolveDocument"])},features:[P["\u0275\u0275ProvidersFeature"]([{provide:d.c,useClass:p.c,deps:[d.f,p.a]},{provide:d.e,useValue:Z}])],decls:23,vars:11,consts:[[1,"pop-up","ts-inline-entry-popup-styles","d-flex","flex-column"],[2,"width","450px","max-height","210px","overflow-y","scroll"],[1,"row"],[1,"row","pt-2","pl-3","pr-3","normalFont",2,"height","fit-content"],[1,"pl-0","pr-3",2,"font-weight","500","height","fit-content","color","#5f5f5f"],[1,"pl-0","pr-0",2,"font-weight","600","height","fit-content"],["class","mt-1 ml-2","mat-icon-button","","style","width: 26px; height: 26px; line-height: 26px;","matTooltip","View All Entries",3,"click",4,"ngIf"],["class","ml-2","style","width: 24px;",4,"ngIf"],[1,"row","pt-2","pl-3","pr-1","normalFont",2,"height","fit-content"],["class","pl-0 pr-3","style","font-weight: 500; height: fit-content; color: #5f5f5f;",4,"ngIf"],["mat-icon-button","","matTooltip","Close",1,"mt-1","ml-2",2,"width","26px","height","26px","line-height","26px",3,"click"],[2,"color","grey","font-size","18px !important"],["class","pt-1 pl-3 pr-3 pb-1",4,"ngIf"],["class","mt-0 mb-2","style","background: #ECECEC;",3,"ngClass",4,"ngIf"],["class","row mb-1 mr-2 mb-2",4,"ngIf"],["class","row pt-0 pb-1",4,"ngIf"],["class","row pt-2 pb-2",4,"ngIf"],["mat-icon-button","","matTooltip","View All Entries",1,"mt-1","ml-2",2,"width","26px","height","26px","line-height","26px",3,"click"],[2,"color","grey","font-size","18px !important",3,"ngClass"],[1,"ml-2",2,"width","24px"],[1,"pt-1","pl-3","pr-3","pb-1"],["class","normalFont",4,"ngFor","ngForOf"],[1,"normalFont"],[1,"pl-0","pr-0","Wwrap",3,"ngClass"],[1,"colorheading"],[1,"mt-0","mb-2",2,"background","#ECECEC",3,"ngClass"],["class","pl-0 pr-0 my-auto iconButton","matTooltip","Previous Task","style","cursor: pointer;",3,"click",4,"ngIf"],[1,"row","pr-0",3,"ngClass"],["class","pt-1","style","border-bottom: solid 2px #cacaca;",4,"ngIf"],["class","pl-0 pr-0",4,"ngIf"],["class","pl-0 pr-0 my-auto iconButton","matTooltip","Next Task","style","cursor: pointer; text-align: right;",3,"click",4,"ngIf"],["matTooltip","Previous Task",1,"pl-0","pr-0","my-auto","iconButton",2,"cursor","pointer",3,"click"],[1,"pt-1",2,"border-bottom","solid 2px #cacaca"],[1,"pl-0","pr-0"],[1,"pl-0","pr-0","row","pt-1","pb-1","normalFont"],[1,"pl-0","mr-1",2,"text-align","left","color","#5f5f5f","width","65px"],[1,"pl-0","mr-1",2,"text-align","center","color","#5f5f5f","width","40px"],[1,"pl-0","mr-1",2,"text-align","left","color","#5f5f5f","width","88px"],[1,"pl-0","mr-1",2,"text-align","left","color","#5f5f5f",3,"ngStyle"],["class","pl-0 mr-1","style","text-align: left; color: #5f5f5f; width: 50px;",4,"ngIf"],[2,"border-bottom","solid 2px #cacaca"],["class","pl-0 pr-0 row pt-1 pb-1 normalFont",4,"ngFor","ngForOf"],[1,"pl-0","mr-1",2,"text-align","left","color","#5f5f5f","width","50px"],[1,"pl-0","mr-1",2,"text-align","left","width","65px"],[1,"pl-0","mr-1",2,"text-align","center","width","40px"],[1,"pl-0","mr-1",2,"text-align","left","width","88px","overflow","hidden","text-overflow","ellipsis","display","inline",3,"matTooltip"],[1,"pl-0","mr-1",2,"text-align","left","overflow","hidden","text-overflow","ellipsis","display","inline",3,"matTooltip","ngStyle"],["class","pl-0 mr-1","style","text-align: left; width: 50px;",4,"ngIf"],[1,"iconButton",2,"font-size","20px",3,"matTooltip","ngClass"],["class","iconButton ml-1","matTooltip","Delete Entry","style","font-size: 20px; cursor: pointer;",3,"click",4,"ngIf"],["style","border-bottom: solid 2px #cacaca;",4,"ngIf"],[1,"pl-0","mr-1",2,"text-align","left","width","50px"],["matTooltip","Delete Entry",1,"iconButton","ml-1",2,"font-size","20px","cursor","pointer",3,"click"],["matTooltip","Next Task",1,"pl-0","pr-0","my-auto","iconButton",2,"cursor","pointer","text-align","right",3,"click"],[1,"row","mb-1","mr-2","mb-2"],[1,"pl-3","pr-0","header"],[1,"row","pt-0","pb-1"],["matTooltip","Change Date Range",1,"ml-3","mr-3","pt-1","pb-1","d-flex",2,"outline","1px solid grey"],[1,"pl-2",3,"disabled","ngStyle","min","max","rangePicker","click"],["matStartDate","","placeholder","Start Date","readonly","",2,"cursor","pointer",3,"formControl"],["matEndDate","","placeholder","End Date","readonly","",2,"cursor","pointer",3,"formControl"],["style","cursor: pointer;","class","pt-1 my-auto iconButton",3,"disabled","click",4,"ngIf"],["dateRangePicker",""],[1,"ml-0","mr-0","pt-1","pb-1","d-flex",2,"width","200px","outline","1px solid grey"],["matInput","","type","text","placeholder","Search Location","autocapitalize","none",1,"pl-1","pr-1",2,"text-align","center",3,"matTooltip","ngModel","disabled","matAutocomplete","ngModelOptions","ngModelChange"],["auto","matAutocomplete"],[3,"value","matTooltip",4,"ngFor","ngForOf"],[1,"pt-1","my-auto","iconButton",2,"cursor","pointer",3,"disabled","click"],[3,"value","matTooltip"],[1,"row","pt-2","pb-2"],["class","ml-3 pt-1 pb-1 d-flex","style","outline: 1px solid grey",4,"ngIf"],["matTooltip","Change Day Type",1,"ml-3","pt-1","pl-2","listItemName",3,"ngModel","disabled","ngModelChange"],[3,"ngValue","value",4,"ngFor","ngForOf"],[1,"ml-3","d-flex","my-auto"],[1,"mt-1","iconButton",2,"font-size","20px","cursor","pointer",3,"ngClass","matTooltip","click"],["matInput","","matInput","","placeholder","Billing Type","readonly","",1,"pl-0",2,"font-weight","500","width","80px","color","#5f5f5f",3,"ngModel"],["mat-icon-button","","matTooltip","Submit Entry",1,"approve-btn","ml-4",3,"disabled","click"],[2,"color","white !important","font-size","18px !important"],[1,"ml-3","pt-1","pb-1","d-flex",2,"outline","1px solid grey"],["matInput","","type","number","autocomplete","off","onKeyPress","if(this.value.length==4) return false",2,"text-align","center","width","55px",3,"ngModel","disabled","ngModelChange"],["matInput","","matInput","","readonly","",1,"mr-1",2,"width","30px","text-align","center",3,"ngModel"],[1,"pt-1","my-auto","iconButton"],["matInput","","type","number","step","0.1","autocomplete","off","onKeyPress","if(this.value.length==4) return false",2,"text-align","center","width","55px",3,"ngModel","disabled","ngModelChange"],[3,"ngValue","value"]],template:function(t,e){1&t&&(P["\u0275\u0275elementStart"](0,"div",0),P["\u0275\u0275elementStart"](1,"div",1),P["\u0275\u0275elementStart"](2,"div",2),P["\u0275\u0275elementStart"](3,"div",3),P["\u0275\u0275elementStart"](4,"div",4),P["\u0275\u0275text"](5," Total Hours "),P["\u0275\u0275elementEnd"](),P["\u0275\u0275elementStart"](6,"div",5),P["\u0275\u0275text"](7),P["\u0275\u0275elementEnd"](),P["\u0275\u0275elementEnd"](),P["\u0275\u0275template"](8,T,3,1,"button",6),P["\u0275\u0275template"](9,_,1,0,"div",7),P["\u0275\u0275elementStart"](10,"div",8),P["\u0275\u0275template"](11,k,2,0,"div",9),P["\u0275\u0275template"](12,O,2,1,"div",9),P["\u0275\u0275elementStart"](13,"div",5),P["\u0275\u0275text"](14),P["\u0275\u0275elementEnd"](),P["\u0275\u0275elementEnd"](),P["\u0275\u0275elementStart"](15,"button",10),P["\u0275\u0275listener"]("click",(function(){return e.closePopup()})),P["\u0275\u0275elementStart"](16,"mat-icon",11),P["\u0275\u0275text"](17," cancel "),P["\u0275\u0275elementEnd"](),P["\u0275\u0275elementEnd"](),P["\u0275\u0275elementEnd"](),P["\u0275\u0275template"](18,A,2,1,"div",12),P["\u0275\u0275template"](19,W,7,6,"div",13),P["\u0275\u0275template"](20,G,3,0,"div",14),P["\u0275\u0275template"](21,X,13,15,"div",15),P["\u0275\u0275template"](22,$,12,10,"div",16),P["\u0275\u0275elementEnd"](),P["\u0275\u0275elementEnd"]()),2&t&&(P["\u0275\u0275advance"](7),P["\u0275\u0275textInterpolate1"](" ",e.tsInlineEditPopupData.totalHours," Hrs "),P["\u0275\u0275advance"](1),P["\u0275\u0275property"]("ngIf",e.tsInlineEditPopupData.entries.length>0),P["\u0275\u0275advance"](1),P["\u0275\u0275property"]("ngIf",e.tsInlineEditPopupData.entries.length<=0),P["\u0275\u0275advance"](2),P["\u0275\u0275property"]("ngIf",0==e.cc_name_config),P["\u0275\u0275advance"](1),P["\u0275\u0275property"]("ngIf",1==e.cc_name_config),P["\u0275\u0275advance"](2),P["\u0275\u0275textInterpolate1"](" ",e.tsInlineEditPopupData.costCentreItem.costCentre," "),P["\u0275\u0275advance"](4),P["\u0275\u0275property"]("ngIf",e.tsInlineEditPopupData.objectDetailsVisible),P["\u0275\u0275advance"](1),P["\u0275\u0275property"]("ngIf",e.areEntriesVisible),P["\u0275\u0275advance"](1),P["\u0275\u0275property"]("ngIf",!(e.tsInlineEditPopupData.isFromTs&&!e.tsInlineEditPopupData.canEnterTs)),P["\u0275\u0275advance"](1),P["\u0275\u0275property"]("ngIf",!(e.tsInlineEditPopupData.isFromTs&&!e.tsInlineEditPopupData.canEnterTs)),P["\u0275\u0275advance"](1),P["\u0275\u0275property"]("ngIf",!(e.tsInlineEditPopupData.isFromTs&&!e.tsInlineEditPopupData.canEnterTs)))},directives:[u.NgIf,g.a,h.a,f.a,u.NgClass,u.NgForOf,u.NgStyle,y.d,y.l,r.e,r.v,r.k,y.k,y.e,m.b,I.d,r.y,I.b,d.p,E.c,r.A],pipes:[u.DatePipe],styles:[".pop-up[_ngcontent-%COMP%]{width:auto;border:1px solid #ccc;border-radius:5px;background:#fff;margin:0;box-shadow:0 4px 8px 0 rgba(0,0,0,.2),0 6px 20px 0 rgba(0,0,0,.19)}  .cdk-overlay-dark-backdrop{background:transparent!important}.ts-inline-entry-popup-styles[_ngcontent-%COMP%]     .mat-datepicker-toggle .mat-icon-button{width:20px;height:20px;flex-shrink:0;line-height:20px}.ts-inline-entry-popup-styles[_ngcontent-%COMP%]   .colorRed[_ngcontent-%COMP%]{color:#cf0001!important}.ts-inline-entry-popup-styles[_ngcontent-%COMP%]   .Wwrap[_ngcontent-%COMP%]{width:100%;word-break:break-all;white-space:normal}.ts-inline-entry-popup-styles[_ngcontent-%COMP%]   .normalFont[_ngcontent-%COMP%]{color:#181818;font-size:14px;font-weight:400;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ts-inline-entry-popup-styles[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{color:#5f5f5f;font-size:14px!important}.ts-inline-entry-popup-styles[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:14px}.ts-inline-entry-popup-styles[_ngcontent-%COMP%]   .spinner-align[_ngcontent-%COMP%]{margin:auto;display:inline-block}.ts-inline-entry-popup-styles[_ngcontent-%COMP%]   .billable[_ngcontent-%COMP%]{color:#cf0001}.ts-inline-entry-popup-styles[_ngcontent-%COMP%]   .billingOnly[_ngcontent-%COMP%]{color:#1a1a1a}.ts-inline-entry-popup-styles[_ngcontent-%COMP%]   .col-10-5[_ngcontent-%COMP%]{flex:0 0 88.75%;max-width:88.75%}.ts-inline-entry-popup-styles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff;font-weight:400;font-size:12px!important;line-height:31px;padding:0 4px;width:95px}.ts-inline-entry-popup-styles[_ngcontent-%COMP%]   .is-active[_ngcontent-%COMP%]{color:#cf0001!important}.ts-inline-entry-popup-styles[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]{color:#cf0001;background-color:#fff;font-weight:400;font-size:12px!important;line-height:31px;padding:0 4px;border:1px solid #cf0001!important;width:95px}.ts-inline-entry-popup-styles[_ngcontent-%COMP%]   .approve-btn[_ngcontent-%COMP%]{background-color:#cf0001;font-size:18px;color:#fff;width:27px;height:27px;line-height:25px;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 3px 0 rgba(0,0,0,.14),0 2px 14px 0 rgba(0,0,0,.12)}.ts-inline-entry-popup-styles[_ngcontent-%COMP%]   .custom-ts-select-option[_ngcontent-%COMP%]{font-size:14px;text-align:center;color:#1a1a1a}.ts-inline-entry-popup-styles[_ngcontent-%COMP%]   .colorAccount[_ngcontent-%COMP%]{color:#cf0001!important}.ts-inline-entry-popup-styles[_ngcontent-%COMP%]   .colorAccount[_ngcontent-%COMP%], .ts-inline-entry-popup-styles[_ngcontent-%COMP%]   .colorheading[_ngcontent-%COMP%]{font-weight:700!important;font-size:14px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ts-inline-entry-popup-styles[_ngcontent-%COMP%]   .colorheading[_ngcontent-%COMP%]{color:#181818!important}.ts-inline-entry-popup-styles[_ngcontent-%COMP%]   .listItemName[_ngcontent-%COMP%]{width:120px;box-shadow:0 0 0 1pt #dbdbdb;border-radius:2pt}.ts-inline-entry-popup-styles[_ngcontent-%COMP%]   .listItemName[_ngcontent-%COMP%]:hover{width:120px;box-shadow:0 0 0 1pt #cf0001;border-radius:2pt}  .mat-input-element{font:inherit;background:transparent;color:currentColor;border:none;outline:none;padding:0;margin:0;width:100%;max-width:100%;vertical-align:bottom}"]}),t})(),et=(()=>{class t{}return t.\u0275mod=P["\u0275\u0275defineNgModule"]({type:t}),t.\u0275inj=P["\u0275\u0275defineInjector"]({factory:function(e){return new(e||t)},providers:[],imports:[[u.CommonModule,c.e,g.b,r.p,r.E,h.b,m.c,f.b,y.h,d.n,I.c,D.a,E.d]]}),t})()}}]);