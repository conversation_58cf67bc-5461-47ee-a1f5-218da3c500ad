(window.webpackJsonp=window.webpackJsonp||[]).push([[688,634,765,821,822,858,983,987,990,991],{H44p:function(e,t,r){"use strict";r.d(t,"a",(function(){return y}));var i=r("xG9w"),s=r("fXoL"),a=r("flaP"),o=r("ofXK"),n=r("Qu3c"),l=r("NFeN");function d(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"div",9),s["\u0275\u0275elementStart"](1,"div",10),s["\u0275\u0275elementStart"](2,"div"),s["\u0275\u0275text"](3),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](4,"div"),s["\u0275\u0275elementStart"](5,"p",11),s["\u0275\u0275text"](6),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](7,"p",12),s["\u0275\u0275text"](8),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e){const e=s["\u0275\u0275nextContext"](2);s["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),s["\u0275\u0275advance"](3),s["\u0275\u0275textInterpolate"](null==e.currency[e.index]?null:e.currency[e.index].currency_code),s["\u0275\u0275advance"](3),s["\u0275\u0275textInterpolate"](e.label),s["\u0275\u0275advance"](2),s["\u0275\u0275textInterpolate1"](" ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function c(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"div",13),s["\u0275\u0275elementStart"](1,"span"),s["\u0275\u0275text"](2),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e){const e=s["\u0275\u0275nextContext"](2);s["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),s["\u0275\u0275advance"](2),s["\u0275\u0275textInterpolate1"](" ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function u(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"div",14),s["\u0275\u0275elementStart"](1,"span",15),s["\u0275\u0275text"](2),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e){const e=s["\u0275\u0275nextContext"](2);s["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),s["\u0275\u0275advance"](2),s["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function p(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"div",16),s["\u0275\u0275elementStart"](1,"span",15),s["\u0275\u0275text"](2),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e){const e=s["\u0275\u0275nextContext"](2);s["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),s["\u0275\u0275advance"](2),s["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function h(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"div",17),s["\u0275\u0275elementStart"](1,"span",18),s["\u0275\u0275text"](2),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e){const e=s["\u0275\u0275nextContext"](2);s["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),s["\u0275\u0275advance"](2),s["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function m(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"mat-icon",19),s["\u0275\u0275text"](1,"loop"),s["\u0275\u0275elementEnd"]())}function f(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"div",1),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"]().change()})),s["\u0275\u0275template"](1,d,9,4,"div",2),s["\u0275\u0275template"](2,c,3,2,"div",3),s["\u0275\u0275template"](3,u,3,3,"div",4),s["\u0275\u0275template"](4,p,3,3,"div",5),s["\u0275\u0275template"](5,h,3,3,"div",6),s["\u0275\u0275elementStart"](6,"div",7),s["\u0275\u0275template"](7,m,2,0,"mat-icon",8),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275nextContext"]();s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf","big"==e.type),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf","small"==e.type),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf","medium"==e.type),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf","large"==e.type),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf","overview"==e.type),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("ngIf",e.toDisplay)}}let y=(()=>{class e{constructor(e){this.roleService=e,this.showActualAmount=!1,this.currency=[]}set currencyList(e){if(this.currency=e,e){const t=i.findIndex(e,e=>e.currency_code==this.currency_code);-1!=t&&(this.index=t,this.currency[this.index].value=Math.trunc(this.currency[this.index].value))}}ngOnInit(){null==this.toDisplay&&(this.toDisplay=!0),this.tenant_info=this.roleService.currency_info,this.tenantName=this.tenant_info.tenant_name,this.currency_code=this.defaultCurrencyCode?this.defaultCurrencyCode:this.tenant_info.default_currency?this.tenant_info.default_currency:"INR",this.setCurrency(),this.isConvertValue=null==this.tenant_info.is_to_convert_currency_value||this.tenant_info.is_to_convert_currency_value}setCurrency(){if(this.currency){const e=i.findIndex(this.currency,e=>e.currency_code==this.currency_code);-1!=e&&(this.index=e,this.currency[this.index].value=Math.trunc(this.currency[this.index].value))}}change(){void 0!==event.stopPropagation?event.stopPropagation():void 0!==event.cancelBubble&&(event.cancelBubble=!0),this.currency.length>1&&(this.index=++this.index%this.currency.length),this.currency[this.index].value=Math.trunc(this.currency[this.index].value)}convert(e,t){return e?(this.showActualAmount?e=new Intl.NumberFormat("INR"==t?"en-IN":"en-US",{}).format(e)+" "+t:1==this.isConvertValue?e="INR"==t?(e/1e7).toFixed(2)+" Cr":(e/1e6).toFixed(2)+" M":0!=this.isConvertValue||i.contains(["big","small"],this.type)?0==this.isConvertValue&&i.contains(["big","small"],this.type)&&(e="INR"==t?"big"!=this.type?"INR "+new Intl.NumberFormat("en-IN",{}).format(e):new Intl.NumberFormat("en-IN",{}).format(e):"big"!=this.type?t+" "+new Intl.NumberFormat("en-US",{}).format(e):new Intl.NumberFormat("en-US",{}).format(e)):e="INR"==t?(e/1e7).toFixed(2)+" Cr":(e/1e6).toFixed(2)+" M",e):"-"}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275directiveInject"](a.a))},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-currency"]],inputs:{type:"type",label:"label",showActualAmount:"showActualAmount",acl:"acl",toDisplay:"toDisplay",defaultCurrencyCode:"defaultCurrencyCode",currencyList:"currencyList"},decls:1,vars:1,consts:[["class","d-flex currency-wrapper",3,"click",4,"ngIf"],[1,"d-flex","currency-wrapper",3,"click"],["class","d-flex",3,"matTooltip",4,"ngIf"],["class","data-label",3,"matTooltip",4,"ngIf"],["class","data-medium d-flex",3,"matTooltip",4,"ngIf"],["class","data-large d-flex",3,"matTooltip",4,"ngIf"],["class","justify-content-center",3,"matTooltip",4,"ngIf"],[1,"d-flex","align-items-center"],["class","change-icon pl-2","matTooltip","Next currency",4,"ngIf"],[1,"d-flex",3,"matTooltip"],[1,"pr-3","my-auto","currency-code","justify-content-center"],[1,"m-0","header"],[1,"m-0","data-label","pt-1"],[1,"data-label",3,"matTooltip"],[1,"data-medium","d-flex",3,"matTooltip"],[1,"justify-content-center"],[1,"data-large","d-flex",3,"matTooltip"],[1,"justify-content-center",3,"matTooltip"],[1,"data-overview"],["matTooltip","Next currency",1,"change-icon","pl-2"]],template:function(e,t){1&e&&s["\u0275\u0275template"](0,f,8,6,"div",0),2&e&&s["\u0275\u0275property"]("ngIf",t.currency)},directives:[o.NgIf,n.a,l.a],styles:[".header[_ngcontent-%COMP%]{color:#5f5f5f;font-size:11px!important}.data-label[_ngcontent-%COMP%]{font-size:13px!important}.change-icon[_ngcontent-%COMP%]{font-size:18px;width:auto;height:auto;visibility:hidden;cursor:default}.currency-code[_ngcontent-%COMP%]{padding-right:8px;font-size:14px!important}.currency-wrapper[_ngcontent-%COMP%]{white-space:nowrap!important;height:100%}.currency-wrapper[_ngcontent-%COMP%]:hover   .change-icon[_ngcontent-%COMP%]{visibility:visible}.data-medium[_ngcontent-%COMP%]{color:#252422!important;font-size:15px!important}.data-large[_ngcontent-%COMP%], .data-medium[_ngcontent-%COMP%]{text-align:center!important;font-weight:440!important;padding-right:4px!important}.data-large[_ngcontent-%COMP%]{font-size:large!important}.data-overview[_ngcontent-%COMP%]{color:#252422!important;font-size:15px!important;padding-left:24px!important;font-weight:500!important;padding-right:4px!important;text-align:center!important;margin:auto!important}"]}),e})()},NJ67:function(e,t,r){"use strict";r.d(t,"a",(function(){return i}));class i{constructor(){this.disabled=!1}onChange(e){}onTouched(e){}writeValue(e){this.value=e}registerOnChange(e){this.onChange=e}registerOnTouched(e){this.onTouched=e}setDisabledState(e){this.disabled=e}}},bPut:function(e,t,r){"use strict";r.r(t),r.d(t,"TimesheetV2ReportModule",(function(){return R}));var i=r("ofXK"),s=r("tyNb"),a=r("mrSG"),o=r("1yaQ"),n=r("FKr1"),l=r("wd/R"),d=r("xG9w"),c=r("XNiG"),u=r("1G5W"),p=r("fXoL"),h=r("tk/3");let m=(()=>{class e{constructor(e){this._http=e}getReportsItemData(e){return this._http.post("api/timesheetv2/Reports/getReportsItemData",{filterConfig:e})}}return e.\u0275fac=function(t){return new(t||e)(p["\u0275\u0275inject"](h.c))},e.\u0275prov=p["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var f=r("GnQ3"),y=r("0IaG"),v=r("1A3m"),g=r("XXEo"),S=r("BVzC"),_=r("xi/V"),D=r("Wk3H");const x={parse:{dateInput:"MMMM YYYY"},display:{dateInput:"MMMM YYYY",monthYearLabel:"MMM YYYY",dateA11yLabel:"LL",monthYearA11yLabel:"MMMM YYYY"}},C=[{path:"",component:(()=>{class e{constructor(e,t,r,i,s,a){this._timesheetReportService=e,this._udrfService=t,this.matDialog=r,this._toasterService=i,this._loginService=s,this._errorService=a,this.applicationId=567,this.minNoOfVisibleSummaryCards=1,this.maxNoOfVisibleSummaryCards=6,this.categorisedDataTypeArray=[{categoryType:"Status Cards",categoryCardCodes:["D","R","S","A","US"],categoryCards:[]}],this.udrfItemStatusColor=[{status:"Draft",color:"#1890FF"},{status:"Approved",color:"#52C41A"},{status:"Rejected",color:"#FF3A46"},{status:"Submitted",color:"#FA8C16"},{status:"Unsubmitted",color:"#5F6C81"}],this.udrfBodyColumns=[{item:"employee_id",header:"Employee Id",status_description:"",isVisible:"true",type:"text1",position:1,isActive:!0,colSize:"1",textClass:"value13Bold",headerTextClass:"text-center",filterId:2,sortOrder:"N",width:100},{item:"employee_name",header:"Employee Name",status_description:"",isVisible:"true",type:"text1",position:2,isActive:!0,colSize:"1",textClass:"value13Bold",headerTextClass:"text-center",filterId:2,sortOrder:"N",width:100},{item:"doj",header:"Employee Joining Data",status_description:"",isVisible:"true",type:"text1",position:3,isActive:!0,colSize:"1",textClass:"value13Bold",headerTextClass:"text-center",filterId:2,sortOrder:"N",width:100},{item:"employment_type",header:"Employee Type",status_description:"",isVisible:"true",type:"text1",position:4,isActive:!0,colSize:"1",textClass:"value13Bold",headerTextClass:"text-center",filterId:2,sortOrder:"N",width:100},{item:"employment_status",header:"Employee Status",status_description:"",isVisible:"true",type:"text1",position:5,isActive:!0,colSize:"1",textClass:"value13Bold",headerTextClass:"text-center",filterId:2,sortOrder:"N",width:100},{item:"entity",header:"Employee Entity",status_description:"",isVisible:"true",type:"text1",position:6,isActive:!0,colSize:"1",textClass:"value13Bold",headerTextClass:"text-center",filterId:2,sortOrder:"N",width:100},{item:"division",header:"Employee Division",status_description:"",isVisible:"true",type:"text1",position:7,isActive:!0,colSize:"1",textClass:"value13Bold",headerTextClass:"text-center",filterId:2,sortOrder:"N",width:100},{item:"sub_division",header:"Employee Sub Division",status_description:"",isVisible:"true",type:"text1",position:8,isActive:!0,colSize:"1",textClass:"value13Bold",headerTextClass:"text-center",filterId:2,sortOrder:"N",width:100},{item:"holiday_calendar",header:"Employee Holiday Calendar",status_description:"",isVisible:"true",type:"text1",position:9,isActive:!0,colSize:"1",textClass:"value13Bold",headerTextClass:"text-center",filterId:2,sortOrder:"N",width:100},{item:"work_schedule",header:"Employee Work Schedule",status_description:"",isVisible:"true",type:"text1",position:10,isActive:!0,colSize:"1",textClass:"value13Bold",headerTextClass:"text-center",filterId:2,sortOrder:"N",width:100},{item:"total_hours",header:"Total Hours Booked",status_description:"",isVisible:"true",type:"text1",position:11,isActive:!0,colSize:"1",textClass:"value13Bold",headerTextClass:"text-center",filterId:2,sortOrder:"N",width:100},{item:"billable_hours",header:"Total Billable Hours",status_description:"",isVisible:"true",type:"text1",position:12,isActive:!0,colSize:"1",textClass:"value13Bold",headerTextClass:"text-center",filterId:2,sortOrder:"N",width:100},{item:"nonbillable_hours",header:"Total NonBillable Hours",status_description:"",isVisible:"true",type:"text1",position:13,isActive:!0,colSize:"1",textClass:"value13Bold",headerTextClass:"text-center",filterId:2,sortOrder:"N",width:100},{item:"overtime_hours",header:"Total Overtime Hours",status_description:"",isVisible:"true",type:"text1",position:14,isActive:!0,colSize:"1",textClass:"value13Bold",headerTextClass:"text-center",filterId:2,sortOrder:"N",width:100},{item:"utilization_percentage",header:"Utilization Percentage",status_description:"",isVisible:"true",type:"text1",position:15,isActive:!0,colSize:"1",textClass:"value13Bold",headerTextClass:"text-center",filterId:2,sortOrder:"N",width:100},{item:"standard_hours",header:"Standard Hours",status_description:"",isVisible:"true",type:"text1",position:16,isActive:!0,colSize:"1",textClass:"value13Bold",headerTextClass:"text-center",filterId:2,sortOrder:"N",width:100},{item:"leave_count",header:"Leave Count",status_description:"",isVisible:"true",type:"text1",position:17,isActive:!0,colSize:"1",textClass:"value13Bold",headerTextClass:"text-center",filterId:2,sortOrder:"N",width:100},{item:"month",header:"Timesheet Month",status_description:"",isVisible:"true",type:"text1",position:18,isActive:!0,colSize:"1",textClass:"value13Bold",headerTextClass:"text-center",filterId:2,sortOrder:"N",width:100},{type:"timeBtn",isVisible:"true",position:19,isActive:!0,colSize:"2",textClass:"value13Bold",headerTextClass:"text-center",filterId:2,sortOrder:"N",width:100}],this.defaultLimit=0,this.statusArray=[{dataType:"Unsubmitted",dataTypeValue:"0",dataTypeCode:"US",clicked:!1,isVisible:!0,isActive:!1,cardType:"status",statusColor:"#5F6C81",category:"Status Cards"},{dataType:"Draft",dataTypeValue:"0",dataTypeCode:"D",clicked:!1,isVisible:!0,isActive:!1,cardType:"status",statusColor:"#1890FF",category:"Status Cards"},{dataType:"Rejected",dataTypeValue:"0",dataTypeCode:"R",clicked:!1,isVisible:!0,isActive:!1,cardType:"status",statusColor:"#FF3A46",category:"Status Cards"},{dataType:"Submitted",dataTypeValue:"0",dataTypeCode:"S",clicked:!1,isVisible:!0,isActive:!1,cardType:"status",statusColor:"#FA8C16",category:"Status Cards"},{dataType:"Approved",dataTypeValue:"0",dataTypeCode:"A",clicked:!1,isVisible:!0,isActive:!1,cardType:"status",statusColor:"#52C41A",category:"Status Cards"}],this.selectedCard=[],this.timesheetReportData=[],this.isCardClicked=!1,this._onDestroy=new c.b,this.durationRanges=[]}ngOnInit(){this.currentUser=this._loginService.getProfile().profile,this._udrfService.udrfBodyData=[],this._udrfService.udrfUiData.expansionPanelInnerItemData=[],this._udrfService.udrfData.applicationId=this.applicationId,this._udrfService.udrfUiData.showNewReleasesButton=!0,this._udrfService.udrfUiData.showItemDataCount=!0,this._udrfService.udrfUiData.itemDataType="",this._udrfService.udrfUiData.totalItemDataCount=0,this._udrfService.udrfUiData.showSearchBar=!0,this._udrfService.udrfUiData.showActionButtons=!0,this._udrfService.udrfUiData.showUdrfModalButton=!0,this._udrfService.udrfUiData.showColumnConfigButton=!0,this._udrfService.udrfUiData.udrfItemStatusColor=this.udrfItemStatusColor,this._udrfService.udrfUiData.udrfBodyColumns=this.udrfBodyColumns,this._udrfService.udrfUiData.resolveVisibleSummaryCards=this.resolveVisibleSummaryCards.bind(this),this._udrfService.udrfUiData.summaryCardsSelected=this.statusCardsSelected.bind(this),this._udrfService.udrfUiData.summaryCardsItem={},this._udrfService.udrfUiData.showSettingsModalButton=!0,this._udrfService.udrfUiData.itemDataScrollDown=this.onTimesheetReportScroll.bind(this),this._udrfService.udrfUiData.inlineEditData={},this._udrfService.udrfUiData.inlineEditDropDownMasterDatas={},this._udrfService.udrfUiData.summaryCards=this.statusArray,this._udrfService.udrfUiData.udrfVisibleBodyColumns=this._udrfService.udrfUiData.udrfVisibleBodyColumns,this._udrfService.udrfUiData.udrfInvisibleBodyColumns=this._udrfService.udrfUiData.udrfInvisibleBodyColumns,this._udrfService.udrfUiData.categorisedSummaryCards=this.categorisedDataTypeArray,this._udrfService.udrfUiData.minNoOfVisibleSummaryCards=this.minNoOfVisibleSummaryCards,this._udrfService.udrfUiData.maxNoOfVisibleSummaryCards=this.maxNoOfVisibleSummaryCards,this._udrfService.udrfUiData.selectedCard=this.selectedCard,this._udrfService.udrfUiData.variant=0,this._udrfService.udrfUiData.itemHasQuickCta=!1,this._udrfService.udrfUiData.itemHasComments=!1,this._udrfService.udrfUiData.quickCTAInput={},this._udrfService.udrfUiData.commentsInput={},this._udrfService.udrfUiData.commentsContext={},this._udrfService.udrfUiData.variant2View=!0,this._udrfService.udrfUiData.isHeaderSort=!0,this._udrfService.udrfUiData.horizontalScroll=!0,this._udrfService.udrfUiData.isMultipleView=!0,this._udrfService.udrfUiData.showReportDownloadButton=!0,this._udrfService.udrfUiData.timesheetDetailId=-1,this._udrfService.udrfUiData.openDetailReportView=this.openDetailView.bind(this),this._udrfService.udrfUiData.summaryViewActivated=!1,this._udrfService.udrfUiData.downloadItemDataReport=this.downloadTimesheetReport.bind(this),this._udrfService.getAppUdrfConfig(this.applicationId,this.initReport.bind(this)),this._udrfService.udrfUiData.isMoreOptionsNeeded=!0,this.durationRanges=[{checkboxId:"CDCRD3",checkboxName:"This Month",checkboxStartValue:l().startOf("month").format("YYYY-MM-DD"),checkboxEndValue:l().endOf("month").format("YYYY-MM-DD"),isCheckboxDefaultSelected:!0},{checkboxId:"CDCRD1",checkboxName:"Previous Month",checkboxStartValue:l().subtract(1,"month").startOf("month").format("YYYY-MM-DD"),checkboxEndValue:l().subtract(1,"month").endOf("month").format("YYYY-MM-DD"),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD4",checkboxName:"Upcoming 3 Month",checkboxStartValue:l().startOf("month").format("YYYY-MM-DD"),checkboxEndValue:l().add(3,"month").endOf("month").format("YYYY-MM-DD"),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD5",checkboxName:"Previous 3 Month",checkboxStartValue:l().subtract(3,"month").startOf("month").format("YYYY-MM-DD"),checkboxEndValue:l().endOf("month").format("YYYY-MM-DD"),isCheckboxDefaultSelected:!1}],this._udrfService.udrfFunctions.constructCustomRangeData(14,"date",this.durationRanges)}initReport(){return Object(a.c)(this,void 0,void 0,(function*(){console.log("Subash Calls"),this._udrfService.udrfBodyData=[],this._udrfService.udrfUiData.expansionPanelInnerItemData=[],this.isCardClicked=!1,this.cardClicked="",this._udrfService.udrfUiData.resolveColumnConfig(),this.defaultLimit=0,this.timesheetReportData=[],yield this.loadTimesheetReportData()}))}downloadTimesheetReport(){}resolveVisibleSummaryCards(){}onTimesheetReportScroll(){this.defaultLimit+=15}statusCardsSelected(){this._udrfService.udrfData.isItemDataLoading=!0,this._udrfService.udrfData.noItemDataFound=!1;let e=this._udrfService.udrfUiData.summaryCardsItem;for(let t=0;t<this.statusArray.length;t++)if(e.dataTypeCode==this.statusArray[t].dataTypeCode)1==this.statusArray[t].isActive?(this._udrfService.udrfBodyData=[],this.statusArray[t].isActive=!1,e.isActive=!1):(this._udrfService.udrfBodyData=[],this.statusArray[t].isActive=!0,e.isActive=!0);else{let e=d.where(this._udrfService.udrfUiData.summaryCards,{dataTypeCode:this.statusArray[t].dataTypeCode});e.length>0&&(e[0].isActive=!1),this.statusArray[t].isActive=!1,this._udrfService.udrfUiData.cardClicked=!1}this.cardClicked=e.dataType,this.defaultLimit=0,this._udrfService.udrfBodyData=[],this.loadTimesheetReportData()}loadTimesheetReportData(){return Object(a.c)(this,void 0,void 0,(function*(){let e=JSON.parse(JSON.stringify(this._udrfService.udrfData.mainFilterArray)),t={startIndex:this.defaultLimit,startDate:l(this._udrfService.udrfData.mainApiDateRangeStart).format("YYYY-MM-DD"),endDate:l(this._udrfService.udrfData.mainApiDateRangeEnd).format("YYYY-MM-DD"),mainFilterArray:e,txTableDetails:this._udrfService.udrfData.txTableDetails,mainSearchParameter:this._udrfService.udrfData.mainSearchParameter,searchTableDetails:this._udrfService.udrfData.searchTableDetails,associateAid:this.currentUser.aid,associateOid:this.currentUser.oid};this._timesheetReportService.getReportsItemData(t).pipe(Object(u.a)(this._onDestroy)).subscribe(e=>{e.data&&e.data.length>0&&"S"==e.messType?(this._udrfService.udrfBodyData=this._udrfService.udrfBodyData.concat(e.data),this._udrfService.udrfData.isItemDataLoading=!1):(this._udrfService.udrfBodyData=this._udrfService.udrfBodyData.concat([]),this._udrfService.udrfData.noItemDataFound=!0,this._toasterService.showInfo("Timesheet Report Message",e.messText,3e3)),this._udrfService.udrfData.isItemDataLoading=!1},e=>{this._errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Kindly Try After SomeTime",e&&e.params?e.params:e&&e.error?e.error.params:{})})}))}openDetailView(){return Object(a.c)(this,void 0,void 0,(function*(){console.log(this._udrfService.udrfUiData.timesheetDetailId);let e=d.filter(this._udrfService.udrfBodyData,{employee_id:this._udrfService.udrfUiData.timesheetDetailId});e=e.length>0?e[0]:[],console.log(d.filter(this._udrfService.udrfBodyData,{employee_id:this._udrfService.udrfUiData.timesheetDetailId}));let t={leaveDetails:d.filter(this._udrfService.udrfBodyData,{employee_id:this._udrfService.udrfUiData.timesheetDetailId}).length>0?d.filter(this._udrfService.udrfBodyData,{employee_id:this._udrfService.udrfUiData.timesheetDetailId})[0].leave_details:[],detailData:d.filter(this._udrfService.udrfBodyData,{employee_id:this._udrfService.udrfUiData.timesheetDetailId}).length>0?d.filter(this._udrfService.udrfBodyData,{employee_id:this._udrfService.udrfUiData.timesheetDetailId})[0].cost_center_details:[],totalHours:e.total_hours+"h "+(parseInt(e.total_minutes)>9?e.total_minutes+"m":"0"+e.total_minutes+"m"),billableHours:e.billable_hours+"h "+(parseInt(e.billable_minutes)>9?e.billable_minutes+"m":"0"+e.billable_minutes+"m"),nonBillableHours:e.nonbillable_hours+"h "+(parseInt(e.nonbillable_minutes)>9?e.nonbillable_minutes+"m":"0"+e.nonbillable_minutes+"m"),overTimeHours:e.overtime_hours+"h "+(parseInt(e.overtime_minutes)>9?e.overtime_minutes+"m":"0"+e.overtime_minutes+"m"),designation:e.designation?e.designation:"-",oid:e.oid?e.oid:null,employeeName:e.employee_name?e.employee_name:"-"};const{TimesheetReportDetailViewComponent:i}=yield r.e(896).then(r.bind(null,"R5y9"));this.matDialog.open(i,{height:"100%",minWidth:"40%",position:{right:"0px"},data:{modalParams:t}})}))}}return e.\u0275fac=function(t){return new(t||e)(p["\u0275\u0275directiveInject"](m),p["\u0275\u0275directiveInject"](f.a),p["\u0275\u0275directiveInject"](y.b),p["\u0275\u0275directiveInject"](v.a),p["\u0275\u0275directiveInject"](g.a),p["\u0275\u0275directiveInject"](S.a))},e.\u0275cmp=p["\u0275\u0275defineComponent"]({type:e,selectors:[["app-timesheet-v2-report-landing-page"]],features:[p["\u0275\u0275ProvidersFeature"]([{provide:n.c,useClass:o.c,deps:[n.f,o.a]},{provide:n.e,useValue:x}])],decls:3,vars:0,consts:[[1,"container-fluid","timesheet-v2-report-styles","pl-0","pr-0"]],template:function(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"div",0),p["\u0275\u0275element"](1,"udrf-header"),p["\u0275\u0275element"](2,"udrf-body"),p["\u0275\u0275elementEnd"]())},directives:[_.a,D.a],styles:[""]}),e})()}];let b=(()=>{class e{}return e.\u0275mod=p["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=p["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[s.k.forChild(C)],s.k]}),e})();var I=r("Xi0T"),w=r("bTqV"),A=r("Qu3c"),T=r("NFeN"),O=r("kmnG"),k=r("iadO"),E=r("f0Cb");let R=(()=>{class e{}return e.\u0275mod=p["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=p["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,b,I.a,w.b,A.b,O.e,k.h,T.b,E.b]]}),e})()},hJL4:function(e,t,r){"use strict";r.d(t,"a",(function(){return u}));var i=r("mrSG"),s=r("XNiG"),a=r("xG9w"),o=r("fXoL"),n=r("tk/3"),l=r("LcQX"),d=r("XXEo"),c=r("flaP");let u=(()=>{class e{constructor(e,t,r,i){this.http=e,this.UtilityService=t,this.loginService=r,this.roleService=i,this.msg=new s.b,this.taskStatusColor=[],this.approveRequest=(e,t)=>this.http.post("/api/isa/request/approveResourceRequest",{requestDetails:e,approverOid:t}),this.rejectRequest=(e,t)=>this.http.post("/api/isa/request/rejectResourceRequest",{requestDetails:e,approverOid:t}),this.getTaskStatusColor()}getAllRequestsOfUser(e,t,r,i,s,a,o){let n=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getAllRequestsOfUser",{oid:e,statusId:t,statusCode:r,objectIds:i,skip:s,limit:a,filterConfig:o,orgIds:n})}getAllRoleAccess(){return a.where(this.roleService.roles,{application_id:139})}getRequestsBasedOnStatus(e,t,r,i,s,a,o){let n=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getRequestsBasedOnStatus",{oid:e,statusId:t,statusCode:r,objectIds:i,skip:s,limit:a,filterConfig:o,orgIds:n})}getRequestsForAwaitingApproval(e,t,r,i){return this.http.post("/api/isa/request/getRequestsForAwaitingApproval",{oid:e,skip:t,limit:r,filterConfig:i})}getObjectIdsBasedOnOid(e){return this.http.post("/api/isa/request/getObjectIdsBasedOnOid",{oid:e})}getStatusCountBasedOnOid(e,t,r,i){let s=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getStatusCountBasedOnOid",{oid:e,objectIds:t,dataTypeArray:r,filterConfig:i,orgIds:s})}getHeaderCount(e){return this.http.post("/api/isa/request/getHeaderCount",{oid:e})}getRequestBasedOnStatus(e,t){return this.http.post("/api/isa/request/getRequestBasedOnStatus",{oid:e,status:t})}getDataTypeArray(){return this.http.post("/api/isa/request/getISAStatusMasterDataUDRF",{})}sendMsg(e){this.msg.next(e)}getMsg(){return this.msg.asObservable()}getTaskStatusColor(){return new Promise((e,t)=>{this.http.post("/api/isa/request/getTaskStatusNameColor",{}).subscribe(t=>(this.taskStatusColor=t,e(t)),e=>{})})}getAllRequestsForRmg(e){return this.http.post("/api/isa/request/getAllRequestsForRmg",{filterConfig:e})}getTotalForRmg(e){return new Promise((t,r)=>{this.http.post("/api/isa/request/getTotalForRmg",{filterConfig:e}).subscribe(e=>t(e),e=>{})})}getDefaultISAStatusToDisplay(){return this.http.post("/api/isa/request/getISADefaultStatusToDisplay",{})}getCTCbreakUpForSkillSet(e){return new Promise((t,r)=>{this.http.post("/api/isa/request/getCTCbreakUpForSkillSet",e).subscribe(e=>t(e.data),e=>{r(e)})})}getCurrentUserRole(){return this.roleService&&this.roleService.roles&&this.roleService.roles.length>0?this.roleService.roles[0].role_id:null}getTemplateForUser(e){return this.http.post("/api/isa/request/getVisibleFormFieldsForUser",e)}saveAndApproveRequest(e){return this.http.post("/api/isa/request/updBudgetInfoByRequestId",e)}getVisibilityMatrix(e){return this.http.post("/api/isa/request/getAuthorzedFieldsByRoleId",e)}getISAAttachmentFromS3(e){return this.http.post("/api/isa/attachment/getISAAttachmentFromS3",{key:e})}getSkillType(){return this.http.post("/api/isa/request/getSkillType",{})}getSkillLevel(){return this.http.post("/api/isa/request/getSkillLevel",{})}getSkillExperience(){return this.http.post("/api/isa/request/getSkillExperience",{})}getSkillName(e){return this.http.post("/api/isa/request/getSkillName",{skill_type_id:e})}getIsaActivityReportDownload(){return this.http.post("/api/isa/request/getIsaActivityReportDownload",{})}getISASLAreport(e){return this.http.post("/api/isa/request/getISASLAreport",{filterConfig:e})}getISACustomReport(e){return this.http.post("/api/isa/request/getISACustomReport",{filterConfig:e})}getPositionType(){return this.http.post("/api/employee360/masterData/getPositionType",{})}calculateNetCostBasedOnPosition(e,t,r,s,o,n,l){return Object(i.c)(this,void 0,void 0,(function*(){let i;i=n&&n.length>1&&(yield this.getManpowerCostByOId(n,r,o,2))||(yield this.getManpowerCostBasedOnPosition(e,t,r,o,l));let d=yield this.getNonManpowerCost(t,r,s,o,2),c=yield this.getAllocatedCost(),u=0;u=(i?i.cost:0)+d.length>0?a.reduce(a.pluck(d,"cost"),(e,t)=>e+t,0):0;let p=c.length>0?a.reduce(a.pluck(c,"percentage"),(e,t)=>e+t,0):0;return{cost:u,currency:i&&i.currency_code?i.currency_code:"",manpowerCost:i,nonManpowerCost:d,allocatedCost:c,allocatedCostValue:u*(p/100)}}))}getManpowerCostBasedOnPosition(e,t,r,i,s){return new Promise((a,o)=>{this.http.post("/api/isa/configuration/getManpowerCostBasedOnPosition",{skillRoleId:e,nationalityId:t,locationGroupId:r,unit:i,position:s}).subscribe(e=>a(e),e=>(console.log(e),o(e)))})}getNonManpowerCost(e,t,r,i,s){return new Promise((a,o)=>{this.http.post("/api/project/getNonManpowerCost",{nationalityId:e,locationGroupId:t,locationId:r,unit:i,currency_id:s}).subscribe(e=>a(e),e=>(console.log(e),o(e)))})}getAllocatedCost(){return new Promise((e,t)=>{this.http.post("/api/project/getAllocatedCost","").subscribe(t=>e(t),e=>(console.log(e),t(e)))})}getManpowerCostByOId(e,t,r,i){return new Promise((s,a)=>{this.http.post("/api/project/getEmployeeSalary",{oid:e,location_group_id:t,unit_id:r,currency_id:i}).subscribe(e=>s(e),e=>(console.log(e),a(e)))})}getSkillNameMaster(){return this.http.post("/api/isa/configuration/getSkillName",{})}getVendorOid(e,t){return this.http.post("/api/isa/request/getVendorOid",{vendorNames:e,docID:t})}getVendorList(e,t){return this.http.post("/api/isa/request/getVendorList",{documentID:e,limitval:t})}removeVendor(e,t){return this.http.post("/api/isa/request/removeVendors",{vendorOid:e,documentID:t})}checkVendorOrNot(){return this.http.post("/api/isa/request/checkVendorOrNot",{})}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275inject"](n.c),o["\u0275\u0275inject"](l.a),o["\u0275\u0275inject"](d.a),o["\u0275\u0275inject"](c.a))},e.\u0275prov=o["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},ucYs:function(e,t,r){"use strict";r.d(t,"a",(function(){return l}));var i=r("mrSG"),s=r("xG9w"),a=r("fXoL"),o=r("tk/3"),n=r("BVzC");let l=(()=>{class e{constructor(e,t){this.http=e,this.errorService=t,this.workflowStatusList=[],this.getWorkflowStatusList()}getWorkflowStatusList(){this.http.post("/api/wfPrimary/getWorkflowStatusList","").subscribe(e=>{this.workflowStatusList=e.data},e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server While Getting Workflow Status List",e&&e.params?e.params:e&&e.error?e.error.params:{})})}getWorkflowProperties(e){return new Promise((t,r)=>{this.http.post("/api/wfPrimary/getWorkflowProperties",{applicationId:e}).subscribe(e=>t(e),e=>r(e))})}getWorkflowPropertiesByWorkflowId(e){return new Promise((t,r)=>{this.http.post("/api/wfPrimary/getWorkflowPropertiesByWorkflowId",{workflowId:e}).subscribe(e=>t(e),e=>r(e))})}getApproversHierarchy(e){return new Promise((t,r)=>{this.http.post("/api/wfPrimary/getApproversHierarchy",e).subscribe(e=>t(e),e=>r(e))})}createWorkflowItems(e){return new Promise((t,r)=>{this.http.post("/api/wfPrimary/createWorkflowItems",e).subscribe(e=>t(e),e=>r(e))})}getWorkflowDetails(e){return new Promise((t,r)=>{this.http.post("/api/wfPrimary/getWorkflowDetails",{workflowId:e}).subscribe(e=>t(e),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while getting workflow details",e&&e.params?e.params:e&&e.error?e.error.params:{})})})}formatApproversHierarchy(e,t){return Object(i.c)(this,void 0,void 0,(function*(){0==e.length&&t.cc0&&t.cc0.appr0&&t.cc0.appr0.length>0&&e.push([{approvalType:"I",purposeOfInsertingIntoArray:"Just to ensure that 'I' type data is used, if it exists"}]);for(let r=0;r<e.length;r++){let i=[],a=s.keys(t["cc"+r]);for(let s=0;s<a.length;s++)for(let o=0;o<t["cc"+r][a[s]].length;o++){let n={name:t["cc"+r][a[s]][o].DELEGATE_NAME,oid:t["cc"+r][a[s]][o].DELEGATE_OID,level:s+1,designation:t["cc"+r][a[s]][o].DELEGATE_DESIGNATION_NAME,is_delegated:t["cc"+r][a[s]][o].IS_DELEGATED,role:t["cc"+r][a[s]][o].DELEGATE_ROLE_NAME};if(1==t["cc"+r][a[s]][o].IS_DELEGATED&&(n.delegated_by={name:t["cc"+r][a[s]][o].APPROVER_NAME,oid:t["cc"+r][a[s]][o].APPROVER_OID,level:s+1,designation:t["cc"+r][a[s]][o].APPROVER_DESIGNATION_NAME}),i.push(n),r==e.length-1&&s==a.length-1&&o==t["cc"+r][a[s]].length-1)return i}}}))}storeComments(e,t,r){return new Promise((i,s)=>{this.http.post("/api/wfPrimary/updateComments",{workflowHeaderId:e,newComments:t,commentor:r}).subscribe(e=>i(e),e=>(this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server Error while Updating Workflow Comments",e&&e.params?e.params:e&&e.error?e.error.params:{}),s(e)))})}updateWorkflowItems(e){return new Promise((t,r)=>{this.http.post("/api/wfPrimary/updateWorkflowItems",e).subscribe(e=>t(e),e=>(console.log(e),r(e)))})}formatApproversHierarchyForOpportunityApprovalActivity(e){return Object(i.c)(this,void 0,void 0,(function*(){for(let t=0;t<1;t++){let r=[],i=s.keys(e["cc"+t]);for(let s=0;s<i.length;s++)for(let a=0;a<e["cc"+t][i[s]].length;a++){let o={name:e["cc"+t][i[s]][a].DELEGATE_NAME,oid:e["cc"+t][i[s]][a].DELEGATE_OID,level:e["cc"+t][i[s]][a].APPROVAL_ORDER,designation:e["cc"+t][i[s]][a].DELEGATE_DESIGNATION_NAME,is_delegated:e["cc"+t][i[s]][a].IS_DELEGATED};if(1==e["cc"+t][i[s]][a].IS_DELEGATED&&(o.delegated_by={name:e["cc"+t][i[s]][a].APPROVER_NAME,oid:e["cc"+t][i[s]][a].APPROVER_OID,level:e["cc"+t][i[s]][a].APPROVAL_ORDER,designation:e["cc"+t][i[s]][a].APPROVER_DESIGNATION_NAME}),r.push(o),s==i.length-1&&a==e["cc"+t][i[s]].length-1)return r}}}))}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275inject"](o.c),a["\u0275\u0275inject"](n.a))},e.\u0275prov=a["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()}}]);