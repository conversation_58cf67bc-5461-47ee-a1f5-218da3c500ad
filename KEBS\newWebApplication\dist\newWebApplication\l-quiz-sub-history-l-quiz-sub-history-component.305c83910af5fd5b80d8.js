(window.webpackJsonp=window.webpackJsonp||[]).push([[737],{m5a7:function(e,t,n){"use strict";n.r(t),n.d(t,"LQuizSubHistoryComponent",(function(){return b}));var i=n("mrSG"),r=n("0IaG"),s=n("33Jv"),o=n("fXoL"),a=n("bpb9"),l=n("ofXK"),c=n("bTqV"),d=n("NFeN"),m=n("+PWi");let u=(()=>{class e{transform(e,...t){return Math.floor(e/60)+" Min "+e%60+" Sec"}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=o["\u0275\u0275definePipe"]({name:"secondsToMinsec",type:e,pure:!0}),e})();function p(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"tr"),o["\u0275\u0275elementStart"](1,"th",4),o["\u0275\u0275text"](2),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](3,"td"),o["\u0275\u0275text"](4),o["\u0275\u0275pipe"](5,"dateAgo"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](6,"td"),o["\u0275\u0275text"](7),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](8,"td"),o["\u0275\u0275text"](9),o["\u0275\u0275pipe"](10,"secondsToMinsec"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](11,"td"),o["\u0275\u0275text"](12),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](13,"td"),o["\u0275\u0275elementStart"](14,"button",5),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](e);const n=t.$implicit;return o["\u0275\u0275nextContext"]().getAnswers(n)})),o["\u0275\u0275elementStart"](15,"mat-icon"),o["\u0275\u0275text"](16,"fact_check"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=t.index;o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate"](n+1),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate"](o["\u0275\u0275pipeBind1"](5,6,e.submittedAt)),o["\u0275\u0275advance"](3),o["\u0275\u0275textInterpolate2"]("",e.obtainedMarks,"/",e.totalMarks,""),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate"](o["\u0275\u0275pipeBind1"](10,8,e.completionTime)),o["\u0275\u0275advance"](3),o["\u0275\u0275textInterpolate"](e.wrongAnswersCount)}}function h(e,t){1&e&&(o["\u0275\u0275elementContainerStart"](0),o["\u0275\u0275elementStart"](1,"div",6),o["\u0275\u0275element"](2,"img",7),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](3,"div",8),o["\u0275\u0275text"](4," No Previous Submissions ! "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementContainerEnd"]())}let b=(()=>{class e{constructor(e,t,n,i){this.dialogRef=e,this.data=t,this._learner=n,this._dialog=i,this.subs=new s.a,this.reviewSubmission=e=>new Promise((t,n)=>{this.subs.sink=this._learner.getQuizAnswers(e).subscribe(e=>{t(e.data)},e=>{console.error(e),this._learner.showSnack("Failed to fetch Answers!"),n(e)})})}ngOnInit(){}ngOnDestroy(){this.subs.unsubscribe()}getAnswers(e){return Object(i.c)(this,void 0,void 0,(function*(){let t=yield this.reviewSubmission(e.submissionId);const{QReviewSubmissionComponent:i}=yield n.e(857).then(n.bind(null,"1fQ3"));this._dialog.open(i,{maxWidth:"none",height:"100%",width:"100%",disableClose:!0,panelClass:"full-screen-modal",data:t})}))}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](r.h),o["\u0275\u0275directiveInject"](r.a),o["\u0275\u0275directiveInject"](a.a),o["\u0275\u0275directiveInject"](r.b))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["learner-l-quiz-sub-history"]],decls:18,vars:2,consts:[[1,"table","table-hover"],["scope","col"],[4,"ngFor","ngForOf"],[4,"ngIf"],["scope","row"],["mat-icon-button","",1,"result-btn",3,"click"],[1,"d-flex","justify-content-center","align-items-center","slide-from-down"],["src","https://assets.kebs.app/lms/svgs/empty-animate.svg",2,"width","17rem","height","17rem"],[1,"d-flex","justify-content-center","align-items-center","slide-from-down",2,"font-size","18px"]],template:function(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"table",0),o["\u0275\u0275elementStart"](1,"thead"),o["\u0275\u0275elementStart"](2,"tr"),o["\u0275\u0275elementStart"](3,"th",1),o["\u0275\u0275text"](4,"#"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](5,"th",1),o["\u0275\u0275text"](6,"Quiz Taken"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](7,"th",1),o["\u0275\u0275text"](8,"Score"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](9,"th",1),o["\u0275\u0275text"](10,"Time Taken"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](11,"th",1),o["\u0275\u0275text"](12,"Incorrect"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](13,"th",1),o["\u0275\u0275text"](14,"Answers"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](15,"tbody"),o["\u0275\u0275template"](16,p,17,10,"tr",2),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](17,h,5,0,"ng-container",3)),2&e&&(o["\u0275\u0275advance"](16),o["\u0275\u0275property"]("ngForOf",t.data.submissions),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",0==t.data.submissions.length))},directives:[l.NgForOf,l.NgIf,c.a,d.a],pipes:[m.a,u],styles:["table[_ngcontent-%COMP%]{width:100%}.result-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:grey}.full-screen-modal[_ngcontent-%COMP%]   .mat-dialog-container[_ngcontent-%COMP%]{max-width:none}.table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{padding:.75rem;vertical-align:unset;border-top:1px solid #dee2e6}"]}),e})()}}]);