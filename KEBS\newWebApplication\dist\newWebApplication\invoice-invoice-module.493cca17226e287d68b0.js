(window.webpackJsonp=window.webpackJsonp||[]).push([[732],{"N/3u":function(e,t,n){"use strict";n.r(t),n.d(t,"InvoiceModule",(function(){return s}));var i=n("ofXK"),o=n("wZkO"),a=n("tyNb"),r=n("fXoL");function l(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"a",3,4),r["\u0275\u0275text"](2),r["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=r["\u0275\u0275reference"](1);r["\u0275\u0275property"]("routerLink",e.path)("active",n.isActive),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate1"](" ",e.label," ")}}const c=[{path:"",component:(()=>{class e{constructor(){this.tabLinks=[{label:"Invoices",path:"invoicelist"},{label:"Upcoming payments",path:"upcomings"},{label:"Settings",path:"setting"}]}ngOnInit(){}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["app-invoice"]],decls:4,vars:1,consts:[["mat-tab-nav-bar",""],["mat-tab-link","","routerLinkActive","",3,"routerLink","active",4,"ngFor","ngForOf"],[1,"project-tab-content"],["mat-tab-link","","routerLinkActive","",3,"routerLink","active"],["rla","routerLinkActive"]],template:function(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"nav",0),r["\u0275\u0275template"](1,l,3,3,"a",1),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](2,"div",2),r["\u0275\u0275element"](3,"router-outlet"),r["\u0275\u0275elementEnd"]()),2&e&&(r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngForOf",t.tabLinks))},directives:[o.f,i.NgForOf,a.l,a.j,o.e,a.i],styles:["a[_ngcontent-%COMP%]:hover{color:unset!important;text-decoration:none!important}nav[_ngcontent-%COMP%]{position:fixed;width:100%;z-index:14;background:#f8f9fa}.project-tab-content[_ngcontent-%COMP%]{padding-top:48px!important}"]}),e})(),children:[{path:"",redirectTo:"invoicelist/0",pathMatch:"full"},{path:"invoicelist",redirectTo:"invoicelist/0",pathMatch:"full"},{path:"invoicelist/:type",loadChildren:()=>Promise.all([n.e(1),n.e(2),n.e(3),n.e(4),n.e(5),n.e(6),n.e(9),n.e(10),n.e(11),n.e(12),n.e(21),n.e(60),n.e(82),n.e(107),n.e(0),n.e(495)]).then(n.bind(null,"LUBw")).then(e=>e.InvoiceHomeModule)},{path:"setting",loadChildren:()=>Promise.all([n.e(61),n.e(496)]).then(n.bind(null,"+Mz7")).then(e=>e.InvoiceSettingModule),data:{breadcrumb:"Settings"}},{path:"dunning",loadChildren:()=>Promise.all([n.e(1),n.e(10),n.e(37),n.e(494)]).then(n.bind(null,"eF/u")).then(e=>e.InvoiceDunningModule),data:{breadcrumb:"Dunning"}},{path:"upcomings",loadChildren:()=>Promise.all([n.e(4),n.e(60),n.e(82),n.e(107),n.e(0),n.e(498)]).then(n.bind(null,"TBnj")).then(e=>e.InvoiceUpcomingPaymentsModule),data:{breadcrumb:"Upcoming Payments"}}]}];let d=(()=>{class e{}return e.\u0275mod=r["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=r["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[a.k.forChild(c)],a.k]}),e})();var p=n("FKr1"),u=n("1yaQ");let s=(()=>{class e{}return e.\u0275mod=r["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=r["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,p.n,d,u.b,o.g]]}),e})()}}]);