(window.webpackJsonp=window.webpackJsonp||[]).push([[882],{k6no:function(t,a,e){"use strict";e.r(a),e.d(a,"DetailedTaskDialogComponent",(function(){return v}));var n=e("mrSG"),i=e("0IaG"),l=e("33Jv"),o=e("dHLR"),r=e("fXoL"),d=e("2Clw"),s=e("ek25"),u=e("ofXK"),c=e("kmnG"),m=e("NFeN"),p=e("qFsG"),g=e("bTqV"),h=e("Qu3c");function D(t,a){if(1&t){const t=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"button",20),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](t);const a=r["\u0275\u0275nextContext"](),e=r["\u0275\u0275reference"](17);return a.stopSearching(),e.value=""})),r["\u0275\u0275elementStart"](1,"mat-icon",21),r["\u0275\u0275text"](2," close "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}}function f(t,a){if(1&t&&(r["\u0275\u0275elementStart"](0,"div",22),r["\u0275\u0275elementStart"](1,"strong"),r["\u0275\u0275text"](2,"Start Date : "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275text"](3),r["\u0275\u0275elementEnd"]()),2&t){const t=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](3),r["\u0275\u0275textInterpolate1"]("",null!=t.inputData&&null!=t.inputData.dataParams&&null!=t.inputData.dataParams.rowValue&&t.inputData.dataParams.rowValue.start_date?null==t.inputData||null==t.inputData.dataParams||null==t.inputData.dataParams.rowValue?null:t.inputData.dataParams.rowValue.start_date:"-"," ")}}function P(t,a){if(1&t&&(r["\u0275\u0275elementStart"](0,"div",23),r["\u0275\u0275elementStart"](1,"strong"),r["\u0275\u0275text"](2,"End Date : "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275text"](3),r["\u0275\u0275elementEnd"]()),2&t){const t=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](3),r["\u0275\u0275textInterpolate1"]("",null!=t.inputData&&null!=t.inputData.dataParams&&null!=t.inputData.dataParams.rowValue&&t.inputData.dataParams.rowValue.end_date?null==t.inputData||null==t.inputData.dataParams||null==t.inputData.dataParams.rowValue?null:t.inputData.dataParams.rowValue.end_date:"-"," ")}}function y(t,a){if(1&t&&(r["\u0275\u0275elementStart"](0,"div",23),r["\u0275\u0275elementStart"](1,"strong"),r["\u0275\u0275text"](2,"Order Value : "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275text"](3),r["\u0275\u0275elementEnd"]()),2&t){const t=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](3),r["\u0275\u0275textInterpolate1"]("",null!=t.inputData&&null!=t.inputData.dataParams&&null!=t.inputData.dataParams.rowValue&&t.inputData.dataParams.rowValue.order_value?null==t.inputData||null==t.inputData.dataParams||null==t.inputData.dataParams.rowValue?null:t.inputData.dataParams.rowValue.order_value:"-"," ")}}function w(t,a){if(1&t&&(r["\u0275\u0275elementStart"](0,"div",23),r["\u0275\u0275elementStart"](1,"strong"),r["\u0275\u0275text"](2,"Billed Value : "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275text"](3),r["\u0275\u0275elementEnd"]()),2&t){const t=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](3),r["\u0275\u0275textInterpolate1"]("",null!=t.inputData&&null!=t.inputData.dataParams&&null!=t.inputData.dataParams.rowValue&&t.inputData.dataParams.rowValue.billed_value?null==t.inputData||null==t.inputData.dataParams||null==t.inputData.dataParams.rowValue?null:t.inputData.dataParams.rowValue.billed_value:"-"," ")}}function b(t,a){if(1&t&&(r["\u0275\u0275elementStart"](0,"div",23),r["\u0275\u0275elementStart"](1,"strong"),r["\u0275\u0275text"](2,"Collected Value : "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275text"](3),r["\u0275\u0275elementEnd"]()),2&t){const t=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](3),r["\u0275\u0275textInterpolate1"]("",null!=t.inputData&&null!=t.inputData.dataParams&&null!=t.inputData.dataParams.rowValue&&t.inputData.dataParams.rowValue.collected_value?null==t.inputData||null==t.inputData.dataParams||null==t.inputData.dataParams.rowValue?null:t.inputData.dataParams.rowValue.collected_value:"-"," ")}}let v=(()=>{class t{constructor(t,a,e,n){this.dialogRef=t,this.inputData=a,this._dvService=e,this._pmoDashboardService=n,this.applicationRoleAccessList=[],this.searchValue="",this.gcolList=[{keyName:"l2_activity_name",colName:"DESCRIPTION",type:"type1",width:230},{keyName:"planned_end_date",colName:"Date (PL)",type:"date-format1",width:120},{keyName:"actual_end_date",colName:"Date (ACTUAL)",type:"date-format1",width:120},{keyName:"deviation",colName:"Deviation",type:"simple-text",width:120},{keyName:"assigned_to_name",colName:"Owner",type:"type1",width:140},{keyName:"sub_division_name",colName:"Practice Name",type:"type1",width:120},{keyName:"external_reference_id",colName:"Ext-Ref-ID",type:"type1",width:120}],this.milcolList=[{keyName:"l2_activity_name",colName:"DESCRIPTION",type:"type1",width:230},{keyName:"planned_end_date",colName:"Date (PL)",type:"date-format1",width:120},{keyName:"actual_end_date",colName:"Date (ACTUAL)",type:"date-format1",width:120},{keyName:"deviation",colName:"Deviation",type:"simple-text",width:120},{keyName:"assigned_to_name",colName:"Owner",type:"type1",width:140},{keyName:"sub_division_name",colName:"Practice Name",type:"type1",width:120},{keyName:"value",colName:"Value (INR)",type:"type1",width:100}],this.soColList=[{keyName:"l2_activity_name",colName:"DESCRIPTION",type:"type1",width:230},{keyName:"planned_end_date",colName:"Date (PL)",type:"date-format1",width:120},{keyName:"actual_end_date",colName:"Date (ACTUAL)",type:"date-format1",width:120},{keyName:"deviation",colName:"Deviation",type:"simple-text",width:120},{keyName:"externalStakeholder",colName:"Ext Stakeholders",type:"stake",width:160},{keyName:"internalStakeholder",colName:"Int Stakeholders",type:"stake",width:160},{keyName:"sub_division_name",colName:"Practice Name",type:"type1",width:120}],this.defaultCount=15,this.summarydialog=[],this.subs=new l.a}setBodyLayoutConfig(){return Object(n.c)(this,void 0,void 0,(function*(){this.blInstance.layoutConfig.layoutHeight="65vh"}))}ngAfterViewInit(){return Object(n.c)(this,void 0,void 0,(function*(){yield this.setBodyLayoutConfig(),console.log(this.inputData),this.filterConfig=this.inputData.filterConfig,this.typeconfig=this.inputData.type,yield this.getInitCompletedTasksData()}))}getInitCompletedTasksData(){return Object(n.c)(this,void 0,void 0,(function*(){this.blInstance.layoutConfig.isMainDataLoading=!0,this.filterConfig.start_index=0,this.filterConfig.limit=15;let t=yield this.getMainListData(this.inputData.dataParams,this.searchValue);this.formatChildList(t,!0,0),this.blInstance.mainData.dataList=t,this.blInstance.mainData.colList=this.colList,this.blInstance.layoutConfig.isSearchVisible=!0,this.blInstance.layoutConfig.isMainDataLoading=!1}))}formatChildList(t,a,e){var n;for(let i of t)i.loadChild=!1,i.showChild=a,i.rowLevel=e,(null===(n=i.children)||void 0===n?void 0:n.length)>0&&this.formatChildList(i.children,!1,e+1)}ngOnInit(){return Object(n.c)(this,void 0,void 0,(function*(){console.log("input data From Detailed Task Dialog",this.inputData),this.colList="Sign Off"==this.inputData.dataParams.col.tooltip?this.soColList:"Milestone"==this.inputData.dataParams.col.tooltip?this.milcolList:this.gcolList,this.applicationRoleAccessList=yield this._pmoDashboardService.getAllRoleAccessForExtRefId(),console.log("RoleAccess",this.applicationRoleAccessList[0].role_id),1!=this.applicationRoleAccessList[0].role_id&&this.colList.pop()}))}getMainListData(t,a){let e=this.inputData.dataParams.col.status;return this.apiParams={project_ids:this.getProjectIds([t.rowValue],[]),gantt_type_id:t.col.gantt_type_id,status:e,type:this.typeconfig,description:t.rowValue.description},this.blInstance.inputParams.apiParams=this.apiParams,this.blInstance.inputParams.filterConfig=this.filterConfig,new Promise((t,e)=>{this.subs.sink=this._dvService.getGanttTypeDetailedView(this.apiParams,this.filterConfig,a).subscribe(a=>{"S"==a.messType?t(a.data):e(a)},t=>{console.log(t),e(t)})})}getProjectIds(t,a){var e;for(let n of t)n.project_id&&a.push(n.project_id),(null===(e=n.children)||void 0===e?void 0:e.length)>0&&this.getProjectIds(n.children,a);return a}closeDialog(){this.dialogRef.close()}onDataScrolled(t){return Object(n.c)(this,void 0,void 0,(function*(){this.blInstance.layoutConfig.lazyLoadDataLoading=!0,this.filterConfig.start_index=this.filterConfig.limit,this.filterConfig.limit+=this.defaultCount,console.log(this.filterConfig);let t=yield this.getMainListData(this.inputData.dataParams,this.searchValue);this.formatChildList(t,!0,0),this.blInstance.mainData.dataList=this.blInstance.mainData.dataList.concat(t),this.blInstance.layoutConfig.lazyLoadDataLoading=!1}))}searchData(t){this.blInstance.layoutConfig.isMainDataLoading=!0,this.searchValue=t,this._dvService.getGanttTypeDetailedView(this.apiParams,this.filterConfig,t).subscribe(t=>{"S"==t.messType&&(this.formatChildList(t.data,!0,0),this.blInstance.mainData.dataList=t.data,console.log("res.data",t.data),this.blInstance.layoutConfig.isMainDataLoading=!1)},t=>{console.log(t)})}stopSearching(){this.searchValue="",this.getInitCompletedTasksData()}ngOnDestroy(){this.subs.unsubscribe()}}return t.\u0275fac=function(a){return new(a||t)(r["\u0275\u0275directiveInject"](i.h),r["\u0275\u0275directiveInject"](i.a),r["\u0275\u0275directiveInject"](d.a),r["\u0275\u0275directiveInject"](s.a))},t.\u0275cmp=r["\u0275\u0275defineComponent"]({type:t,selectors:[["app-detailed-task-dialog"]],viewQuery:function(t,a){if(1&t&&r["\u0275\u0275viewQuery"](o.a,!0),2&t){let t;r["\u0275\u0275queryRefresh"](t=r["\u0275\u0275loadQuery"]())&&(a.blInstance=t.first)}},decls:29,vars:10,consts:[[1,"container-fluid","detailed-task-dialog"],[1,"d-flex","flex-row","py-2","justify-content-between",2,"border-bottom","1px solid #e8e9ee"],[1,"title","col-10","my-auto"],[1,"row","main-title","mb-2"],[1,"sub-title"],[1,"row","my-auto","badge-style"],[1,"mr-3","my-auto","px-1",3,"ngClass"],[1,"my-auto","pr-2"],["appearance","outline",1,"ml-auto","search-bar","ta-c"],["matPrefix",""],[2,"font-size","21px !important","color","#66615b !important"],["matInput","","placeholder","Search",2,"color","#444444 !important",3,"keyup.enter"],["yourInput",""],["matSuffix",""],["mat-button","","matSuffix","","mat-icon-button","","style","height: 30px; width: 30px; line-height: 1",3,"click",4,"ngIf"],[1,"row","mt-2"],["class","my-auto pr-2 col p-0",4,"ngIf"],["class","my-auto pr-2 col",4,"ngIf"],[1,"close-btn","my-auto",3,"click"],[3,"verticalScrollEvent"],["mat-button","","matSuffix","","mat-icon-button","",2,"height","30px","width","30px","line-height","1",3,"click"],["matTooltip","Clear Search",2,"font-size","18px !important","color","#66615b !important"],[1,"my-auto","pr-2","col","p-0"],[1,"my-auto","pr-2","col"]],template:function(t,a){if(1&t){const t=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"div",0),r["\u0275\u0275elementStart"](1,"div",1),r["\u0275\u0275elementStart"](2,"div",2),r["\u0275\u0275elementStart"](3,"div",3),r["\u0275\u0275text"](4),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](5,"div",4),r["\u0275\u0275elementStart"](6,"div",5),r["\u0275\u0275elementStart"](7,"div",6),r["\u0275\u0275text"](8),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](9,"div",7),r["\u0275\u0275text"](10),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](11,"div",7),r["\u0275\u0275elementStart"](12,"mat-form-field",8),r["\u0275\u0275elementStart"](13,"span",9),r["\u0275\u0275elementStart"](14,"mat-icon",10),r["\u0275\u0275text"](15,"search"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](16,"input",11,12),r["\u0275\u0275listener"]("keyup.enter",(function(){r["\u0275\u0275restoreView"](t);const e=r["\u0275\u0275reference"](17);return a.searchData(e.value)})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](18,"mat-icon",13),r["\u0275\u0275template"](19,D,3,0,"button",14),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](20,"div",15),r["\u0275\u0275template"](21,f,4,1,"div",16),r["\u0275\u0275template"](22,P,4,1,"div",17),r["\u0275\u0275template"](23,y,4,1,"div",17),r["\u0275\u0275template"](24,w,4,1,"div",17),r["\u0275\u0275template"](25,b,4,1,"div",17),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](26,"div",18),r["\u0275\u0275listener"]("click",(function(){return a.closeDialog()})),r["\u0275\u0275text"](27,"Close"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](28,"pmo-body-layout",19),r["\u0275\u0275listener"]("verticalScrollEvent",(function(t){return a.onDataScrolled(t)})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&t){const t=r["\u0275\u0275reference"](17);r["\u0275\u0275advance"](4),r["\u0275\u0275textInterpolate"](null!=a.inputData&&null!=a.inputData.dataParams&&null!=a.inputData.dataParams.col&&a.inputData.dataParams.col.colName?null==a.inputData||null==a.inputData.dataParams||null==a.inputData.dataParams.col?null:a.inputData.dataParams.col.colName:"-"),r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("ngClass","SBU"==(null==a.inputData||null==a.inputData.dataParams||null==a.inputData.dataParams.rowValue?null:a.inputData.dataParams.rowValue.badge)?"badge-box gray2":"P"==(null==a.inputData||null==a.inputData.dataParams||null==a.inputData.dataParams.rowValue?null:a.inputData.dataParams.rowValue.badge)?"badge-box gray4":"BU"==(null==a.inputData||null==a.inputData.dataParams||null==a.inputData.dataParams.rowValue?null:a.inputData.dataParams.rowValue.badge)?"badge-box gray1":"V"==(null==a.inputData||null==a.inputData.dataParams||null==a.inputData.dataParams.rowValue?null:a.inputData.dataParams.rowValue.badge)?"badge-box gray3":"badge-box gray4"),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"]("",null!=a.inputData&&null!=a.inputData.dataParams&&null!=a.inputData.dataParams.rowValue&&a.inputData.dataParams.rowValue.badge?null==a.inputData||null==a.inputData.dataParams||null==a.inputData.dataParams.rowValue?null:a.inputData.dataParams.rowValue.badge:"-"," "),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate1"](" ",null!=a.inputData&&null!=a.inputData.dataParams&&null!=a.inputData.dataParams.rowValue&&a.inputData.dataParams.rowValue.description?null==a.inputData||null==a.inputData.dataParams||null==a.inputData.dataParams.rowValue?null:a.inputData.dataParams.rowValue.description:"-"," "),r["\u0275\u0275advance"](9),r["\u0275\u0275property"]("ngIf",t.value&&""!=t.value),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngIf","P"==(null==a.inputData||null==a.inputData.dataParams||null==a.inputData.dataParams.rowValue?null:a.inputData.dataParams.rowValue.badge)),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","P"==(null==a.inputData||null==a.inputData.dataParams||null==a.inputData.dataParams.rowValue?null:a.inputData.dataParams.rowValue.badge)),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","P"==(null==a.inputData||null==a.inputData.dataParams||null==a.inputData.dataParams.rowValue?null:a.inputData.dataParams.rowValue.badge)),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","P"==(null==a.inputData||null==a.inputData.dataParams||null==a.inputData.dataParams.rowValue?null:a.inputData.dataParams.rowValue.badge)),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","P"==(null==a.inputData||null==a.inputData.dataParams||null==a.inputData.dataParams.rowValue?null:a.inputData.dataParams.rowValue.badge))}},directives:[u.NgClass,c.c,c.h,m.a,p.b,c.i,u.NgIf,o.a,g.a,h.a],styles:[".detailed-task-dialog[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]   .main-title[_ngcontent-%COMP%]{color:#26303e;font-weight:500;font-size:20px}.detailed-task-dialog[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]   .sub-title[_ngcontent-%COMP%]{color:#45546e;font-size:12px;font-weight:400}.detailed-task-dialog[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]{cursor:pointer;font-size:12px;color:#45546e;font-weight:500;margin-right:2%}.detailed-task-dialog[_ngcontent-%COMP%]   .badge-box[_ngcontent-%COMP%]{height:21px;width:-moz-fit-content;width:fit-content;font-size:12px;padding:2px;border-radius:4px;color:#fff;text-align:center;min-width:7%}.detailed-task-dialog[_ngcontent-%COMP%]   .orange[_ngcontent-%COMP%]{background-color:#fa8c16}.detailed-task-dialog[_ngcontent-%COMP%]   .blue[_ngcontent-%COMP%]{background-color:#1890ff}.detailed-task-dialog[_ngcontent-%COMP%]   .gray[_ngcontent-%COMP%]{background-color:grey}.detailed-task-dialog[_ngcontent-%COMP%]   .green[_ngcontent-%COMP%]{background-color:#52c41a}.detailed-task-dialog[_ngcontent-%COMP%]   .pink[_ngcontent-%COMP%]{background-color:#ff3a46}.detailed-task-dialog[_ngcontent-%COMP%]   .gray1[_ngcontent-%COMP%]{background-color:#444}.detailed-task-dialog[_ngcontent-%COMP%]   .gray2[_ngcontent-%COMP%]{background-color:#555}.detailed-task-dialog[_ngcontent-%COMP%]   .gray3[_ngcontent-%COMP%]{background-color:#777}.detailed-task-dialog[_ngcontent-%COMP%]   .gray4[_ngcontent-%COMP%]{background-color:#999}.detailed-task-dialog[_ngcontent-%COMP%]   .badge-style[_ngcontent-%COMP%]{display:flex;width:100%}.detailed-task-dialog[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]{padding-left:1rem;width:100%!important}.detailed-task-dialog[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field{width:20vw;padding-top:5px}.detailed-task-dialog[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-infix{font-size:14px!important;padding:.5em 0!important;border-top:.54375em solid transparent!important}"]}),t})()}}]);