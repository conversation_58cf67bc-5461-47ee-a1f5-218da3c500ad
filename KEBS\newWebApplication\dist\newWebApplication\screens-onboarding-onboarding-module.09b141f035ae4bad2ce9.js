(window.webpackJsonp=window.webpackJsonp||[]).push([[873],{vMvN:function(e,n,o){"use strict";o.r(n),o.d(n,"OnboardingModule",(function(){return l}));var t=o("ofXK"),r=o("tyNb"),i=o("fXoL");const d=[{path:"",loadChildren:()=>Promise.all([o.e(19),o.e(39),o.e(43),o.e(70),o.e(68),o.e(0),o.e(795)]).then(o.bind(null,"IqKi")).then(e=>e.MainOnboardingModule)}];let u=(()=>{class e{}return e.\u0275mod=i["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=i["\u0275\u0275defineInjector"]({factory:function(n){return new(n||e)},imports:[[r.k.forChild(d)],r.k]}),e})(),l=(()=>{class e{}return e.\u0275mod=i["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=i["\u0275\u0275defineInjector"]({factory:function(n){return new(n||e)},imports:[[t.CommonModule,u]]}),e})()}}]);