(window.webpackJsonp=window.webpackJsonp||[]).push([[733],{A6Kz:function(t,e,i){"use strict";i.d(e,"a",(function(){return c}));var n=i("XNiG"),s=i("z6cu"),a=i("xG9w"),o=i("fXoL"),r=i("tk/3"),p=i("flaP");let c=(()=>{class t{constructor(t,e){this.http=t,this.roleService=e,this.messageSharingSubject=new n.b,this.getTranslatedPdfDetail=(t,e,i)=>this.http.post("/api/invoice/otherLanguagePdfDetails",{billingId:t,languageId:e,billingIdFlag:i}),this.getAvailableBankAddress=t=>this.http.post("/api/invoice/stepperAddressChange",{legalEntityId:t,id:3}),this.saveEditedInvoice=(t,e,i)=>this.http.post("/api/invoice/editInvoicePdf",{billingId:t,pdfDetails:e,languageId:i}),this.getAvailableBanks=t=>this.http.post("/api/invoice/bankCrudOperations",{legalEntityId:t,crudId:2}),this.getAvailableFromAddress=t=>this.http.post("/api/invoice/stepperAddressChange",{legalEntityId:t,id:1}),this.getAvailableToAddress=t=>this.http.post("/api/invoice/stepperAddressChange",{customerId:t,id:2}),this.getTranslatedAddressForEnAddress=(t,e,i)=>this.http.post("/api/invoice/getFromAddressDetails",{legalEntityId:t,fromAddressId:e,languageId:i}),this.getTranslatedToAddressForEnAddress=(t,e,i)=>this.http.post("/api/invoice/getToAddressDetails",{customerId:t,toAddressId:e,languageId:i}),this.getTranslatedAddressForAbank=(t,e,i)=>this.http.post("/api/invoice/getBankLanguageDetails",{legalEntityId:t,bankId:e,languageId:i}),this.sendMsg=t=>{this.messageSharingSubject.next(t)},this.getMsg=()=>this.messageSharingSubject.asObservable(),this.generateEngPdf=t=>{this.http.post("/api/invoice/generateEnglishPDF",{pdf_details:t},{responseType:"blob"}).subscribe(t=>{var e=new Blob([t],{type:"application/pdf"}),i=URL.createObjectURL(e);window.open(i)},t=>{Object(s.a)(t)})},this.generateArabicPdf=t=>{this.http.post("/api/invoice/generateArabicPDF",{pdf_details:t},{responseType:"blob"}).subscribe(t=>{var e=new Blob([t],{type:"application/pdf"}),i=URL.createObjectURL(e);window.open(i)},t=>{Object(s.a)(t)})},this.getTenantDateFormats=()=>this.http.post("/api/invoice/v2/getTenantDateFormats",{}),this.syncInvoiceWithSharepoint=(t,e)=>this.http.post("/api/invoice/syncInvoiceWithSharepoint",{billingId:t,token:e})}static invoiceAuthGaurd(t,e,i,n){throw new Error("Method not implemented.")}viewInvoice(t){return this.http.post("api/invoice/viewInvoice",{billingId:t})}roleCheck(){return this.http.post("api/invoice/roleCheck",{})}getInvoiceListActivityList(t){return this.http.post("/api/invoice/getActivitiesInPopUp",{milestoneId:t})}updateInvoiceActivityList(t,e,i,n){return this.http.post("/api/invoice/updateActivities",{milestoneId:t,activities:e,oldPlannedOn:i,newPlannedOn:n})}getInvoicePdfConfig(t,e,i,n,s){return this.http.post("/api/invoice/getInvoicePdfConfig",{fromCompanyCode:t,itemId:e,projectId:i,customerId:n,legalEntityId:s})}getInvoiceTenantCheckDetail(t,e){return this.http.post("/api/invoice/getInvoiceTenantApplicationCheck",{tenantName:t,checkType:e})}getPaymentTerms(){return new Promise((t,e)=>{this.http.post("/api/master/getPaymentTerms",{}).subscribe(e=>t(e),e=>(console.log(e),t([])))})}invoiceUndoAccess(){return a.where(this.roleService.roles,{application_id:907,object_id:320}).length>0}getconsultantDetail(t){return this.http.post("/api/invoice/getFteDetails",{otherMilestoneDataToPdf:t})}getFteDetailForViewInvoice(t){return this.http.post("/api/invoice/getFteDetailForViewInvoice",{otherMilestoneDataToPdf:t})}getLegalEntityQRConfig(t){return this.http.post("/api/invoice/v2/getLegalEntityQRConfig",{legalEntityId:t})}getCurrencyDetails(){return new Promise((t,e)=>{this.http.post("/api/invoice/getCurrencyDetails",{}).subscribe(e=>(console.log(e),t(e.data)))})}invoiceAuthGaurd(t,e,i,n=null){return this.http.post("/api/invoice/checkRoleAccessForUser",{oid:t,aid:e,application_id:i,application_object:n})}}return t.\u0275fac=function(e){return new(e||t)(o["\u0275\u0275inject"](r.c),o["\u0275\u0275inject"](p.a))},t.\u0275prov=o["\u0275\u0275defineInjectable"]({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()}}]);