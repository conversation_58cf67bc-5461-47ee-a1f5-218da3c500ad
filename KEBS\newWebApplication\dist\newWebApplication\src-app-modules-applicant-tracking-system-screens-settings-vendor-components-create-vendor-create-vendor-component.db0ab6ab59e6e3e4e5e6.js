(window.webpackJsonp=window.webpackJsonp||[]).push([[933],{TrPQ:function(e,t,n){"use strict";n.r(t),n.d(t,"CreateVendorComponent",(function(){return w}));var o=n("mrSG"),r=n("3Pt+"),i=n("0IaG"),a=n("XNiG"),l=n("1G5W"),c=n("fXoL"),d=n("XNFG"),s=n("XQl4"),m=n("rQiX"),g=n("ofXK"),f=n("kmnG"),p=n("qFsG"),v=n("TmG/");function h(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"sup",16),c["\u0275\u0275text"](1,"*"),c["\u0275\u0275elementEnd"]())}function u(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div",14),c["\u0275\u0275text"](1),c["\u0275\u0275elementStart"](2,"span"),c["\u0275\u0275template"](3,h,2,0,"sup",15),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"](2).$implicit;c["\u0275\u0275advance"](1),c["\u0275\u0275textInterpolate1"](" ",e.label," "),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("ngIf",e.isMandatory)}}function C(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div"),c["\u0275\u0275elementStart"](1,"mat-form-field",17),c["\u0275\u0275element"](2,"input",18),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"](2).$implicit;c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("placeholder",e.placeholder)("formControlName",e.key)}}const x=function(){return[]};function y(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div"),c["\u0275\u0275element"](1,"app-input-search",19),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"](2).$implicit,t=c["\u0275\u0275nextContext"](2);c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("list","vendor_type"===e.key?t.vendorTypeList:c["\u0275\u0275pureFunction0"](5,x))("disableNone",!0)("placeholder",e.placeholder)("formControlName",e.key)("hideMatLabel",!0)}}function _(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div",20),c["\u0275\u0275elementStart"](1,"div"),c["\u0275\u0275text"](2),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"](2).$implicit;c["\u0275\u0275advance"](2),c["\u0275\u0275textInterpolate1"](" ",e.description," ")}}function O(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div"),c["\u0275\u0275elementStart"](1,"div",11),c["\u0275\u0275template"](2,u,4,2,"div",12),c["\u0275\u0275template"](3,C,3,2,"div",1),c["\u0275\u0275template"](4,y,2,6,"div",1),c["\u0275\u0275template"](5,_,3,1,"div",13),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]().$implicit;c["\u0275\u0275classMapInterpolate1"]("col-",e.col," over-all-field "),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("ngIf","countryCode"!=e.key),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","text"===e.fieldType||"email"===e.fieldType||"url"===e.fieldType),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","single-select"==e.fieldType),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",""!==e.description)}}function M(e,t){if(1&e&&(c["\u0275\u0275elementContainerStart"](0),c["\u0275\u0275template"](1,O,6,7,"div",10),c["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","countryCode"!=e.key)}}function b(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"div",2),c["\u0275\u0275elementStart"](1,"div",3),c["\u0275\u0275text"](2,"Create Vendor"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](3,"form",4),c["\u0275\u0275elementStart"](4,"div",5),c["\u0275\u0275template"](5,M,2,1,"ng-container",6),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](6,"div",7),c["\u0275\u0275elementStart"](7,"div",8),c["\u0275\u0275listener"]("click",(function(){return c["\u0275\u0275restoreView"](e),c["\u0275\u0275nextContext"]().onClickCancel()})),c["\u0275\u0275text"](8," Cancel "),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](9,"div",9),c["\u0275\u0275listener"]("click",(function(){return c["\u0275\u0275restoreView"](e),c["\u0275\u0275nextContext"]().onClickCreate()})),c["\u0275\u0275text"](10," Create "),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()}if(2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275advance"](3),c["\u0275\u0275property"]("formGroup",e.createVendorForm),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("ngForOf",e.formFields)}}function P(e,t){if(1&e&&(c["\u0275\u0275elementContainerStart"](0),c["\u0275\u0275elementStart"](1,"div",21),c["\u0275\u0275elementStart"](2,"div",22),c["\u0275\u0275element"](3,"img",23),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](4,"div",24),c["\u0275\u0275elementStart"](5,"div",25),c["\u0275\u0275text"](6,"Loading..."),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementContainerEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275advance"](3),c["\u0275\u0275property"]("src",e.uiTextConfig["UI-LOADING-001"],c["\u0275\u0275sanitizeUrl"])}}let w=(()=>{class e{constructor(e,t,n,o,r,i){this.dialogRef=e,this._toaster=t,this._fb=n,this._settingsService=o,this._atsMasterService=r,this.data=i,this._onDestroy=new a.b,this.formFields=[],this.uiTextConfig={},this.vendorTypeList=[],this.isLoading=!1,this.spinnerText="Loading..."}ngOnInit(){return Object(o.c)(this,void 0,void 0,(function*(){this.isLoading=!0,this.vendorTypeList=this.data,this.createVendorForm=this._fb.group({}),yield this.getAtsFormsConfig("createVendor"),yield this.createForm(),this.isLoading=!1}))}onClickCancel(){this.dialogRef.close(!1)}onClickCreate(){return Object(o.c)(this,void 0,void 0,(function*(){if(this.createVendorForm.valid){this.isLoading=!0;const e=this.createVendorForm.value;yield this.createVendor(e),this.isLoading=!1,this.dialogRef.close(!0)}else{const e=[];Object.keys(this.createVendorForm.controls).forEach(t=>{const n=this.createVendorForm.get(t);if(n.errors){const o=this.findLabelByKey(t,this.formFields);n.errors.required?e.push(o+" is Mandatory"):n.errors.pattern&&e.push("Invalid "+o)}}),e.reverse(),e.forEach(e=>{this._toaster.showError("Warning",e,7e3)})}}))}findLabelByKey(e,t){const n=t.find(t=>t.key===e);return n?n.label:void 0}createForm(){this.formFields.forEach(e=>{this.createVendorForm.addControl(e.key,this._fb.control("",[e.isMandatory?r.H.required:null,"email"==e.fieldType?r.H.pattern(/^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/):null].filter(e=>null!==e)))})}getAtsFormsConfig(e){return Object(o.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._atsMasterService.getAtsFormsConfig(e).pipe(Object(l.a)(this._onDestroy)).subscribe({next:n=>{0==n.err?"createVendor"==e&&(this.formFields=n.data.form[0].formFields):this._toaster.showError("Error",n.msg,7e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Forms Configuration Retrieval Failed!",7e3),n()}}))}))}createVendor(e){return Object(o.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._settingsService.createVendor(e).pipe(Object(l.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?this._toaster.showSuccess("Success",e.msg,7e3):this._toaster.showError("Error",e.msg,7e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Vendor Creation Failed!",7e3),n()}}))}))}}return e.\u0275fac=function(t){return new(t||e)(c["\u0275\u0275directiveInject"](i.h),c["\u0275\u0275directiveInject"](d.a),c["\u0275\u0275directiveInject"](r.i),c["\u0275\u0275directiveInject"](s.a),c["\u0275\u0275directiveInject"](m.a),c["\u0275\u0275directiveInject"](i.a))},e.\u0275cmp=c["\u0275\u0275defineComponent"]({type:e,selectors:[["app-create-vendor"]],decls:2,vars:2,consts:[["class","container-mat-dialog-create-vendor",4,"ngIf"],[4,"ngIf"],[1,"container-mat-dialog-create-vendor"],[1,"create-vendor-title"],[3,"formGroup"],[1,"row"],[4,"ngFor","ngForOf"],[1,"buttons"],["type","button",1,"cancel-button",3,"click"],["type","submit",1,"save-button",3,"click"],[3,"class",4,"ngIf"],[1,"d-flex","flex-column","label-input-value"],["class","form-label",4,"ngIf"],["class","description",4,"ngIf"],[1,"form-label"],["class","required-field",4,"ngIf"],[1,"required-field"],["appearance","outline","color","warn",1,"mat-form-field"],["matInput","",3,"placeholder","formControlName"],[1,"companyDetailDropDown",3,"list","disableNone","placeholder","formControlName","hideMatLabel"],[1,"description"],[1,"loading-img"],[2,"max-width","60px","max-height","60px"],[2,"max-width","100%","max-height","100%",3,"src"],[1,"loading-wrapper"],[1,"loading"]],template:function(e,t){1&e&&(c["\u0275\u0275template"](0,b,11,2,"div",0),c["\u0275\u0275template"](1,P,7,1,"ng-container",1)),2&e&&(c["\u0275\u0275property"]("ngIf",!t.isLoading),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",t.isLoading))},directives:[g.NgIf,r.J,r.w,r.n,g.NgForOf,f.c,p.b,r.e,r.v,r.l,v.a],styles:['.container-mat-dialog-create-vendor[_ngcontent-%COMP%]{padding:30px 30px 20px;overflow:hidden}.container-mat-dialog-create-vendor[_ngcontent-%COMP%]   .create-vendor-title[_ngcontent-%COMP%]{color:#111434;font-family:var(--atsfontFamily);font-size:18px;font-weight:700;line-height:24px;letter-spacing:.02em;text-align:left}.container-mat-dialog-create-vendor[_ngcontent-%COMP%]   .over-all-field[_ngcontent-%COMP%]{padding:10px 10px 10px 0}.container-mat-dialog-create-vendor[_ngcontent-%COMP%]   .over-all-field[_ngcontent-%COMP%]   .label-input-value[_ngcontent-%COMP%]{gap:4px!important}.container-mat-dialog-create-vendor[_ngcontent-%COMP%]   .over-all-field[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:500;line-height:24px;letter-spacing:.02em;color:#6e7b8f}.container-mat-dialog-create-vendor[_ngcontent-%COMP%]   .over-all-field[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%]   .required-field[_ngcontent-%COMP%]{color:red;font-size:15px;top:0}.container-mat-dialog-create-vendor[_ngcontent-%COMP%]   .over-all-field[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]{width:100%;font-family:var(--atsfontFamily);font-size:14px;font-weight:400;line-height:24px;letter-spacing:.02em;color:#5f6c81}.container-mat-dialog-create-vendor[_ngcontent-%COMP%]   .over-all-field[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-flex, .container-mat-dialog-create-vendor[_ngcontent-%COMP%]   .over-all-field[_ngcontent-%COMP%]     .mat-form-field-type-mat-select:not(.mat-form-field-disabled) .mat-form-field-flex{height:40px!important;display:flex!important;align-items:center!important}.container-mat-dialog-create-vendor[_ngcontent-%COMP%]   .over-all-field[_ngcontent-%COMP%]     .mat-select-value-text{width:100%!important;font-family:var(--atsfontFamily)!important;font-size:12px!important;font-weight:400!important;line-height:16px!important;letter-spacing:.02em!important;color:#45546e!important}.container-mat-dialog-create-vendor[_ngcontent-%COMP%]   .over-all-field[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-outline{height:40px!important}.container-mat-dialog-create-vendor[_ngcontent-%COMP%]   .over-all-field[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-wrapper{margin:0!important}.container-mat-dialog-create-vendor[_ngcontent-%COMP%]   .over-all-field[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:12px;font-weight:400;line-height:20px;color:#b9c0ca}.container-mat-dialog-create-vendor[_ngcontent-%COMP%]   .buttons[_ngcontent-%COMP%]{display:flex;gap:20px;margin-top:18px;padding-right:10px;justify-content:end;width:100%}.container-mat-dialog-create-vendor[_ngcontent-%COMP%]   .buttons[_ngcontent-%COMP%]   .cancel-button[_ngcontent-%COMP%]{text-align:left;color:#45546e;width:78px;border-radius:10px;border:1px solid #45546e}.container-mat-dialog-create-vendor[_ngcontent-%COMP%]   .buttons[_ngcontent-%COMP%]   .cancel-button[_ngcontent-%COMP%], .container-mat-dialog-create-vendor[_ngcontent-%COMP%]   .buttons[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:700;line-height:16px;letter-spacing:-.02em;height:40px;justify-content:center;align-items:center;display:flex;cursor:pointer}.container-mat-dialog-create-vendor[_ngcontent-%COMP%]   .buttons[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%]{width:77px;border-radius:10px;color:#fff;background:linear-gradient(270deg,var(--atsprimaryColor),var(--atssecondaryColor) 105.29%)}.loading-img[_ngcontent-%COMP%]{height:100%;flex-direction:column;background-color:#fff}.loading-img[_ngcontent-%COMP%], .loading-img[_ngcontent-%COMP%]   .loading-wrapper[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center}.loading-img[_ngcontent-%COMP%]   .loading-wrapper[_ngcontent-%COMP%]{vertical-align:center}.loading-img[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]{color:rgba(0,0,0,.3);font-size:16px}.loading-img[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]:before{content:"Loading...";position:absolute;overflow:hidden;max-width:7em;white-space:nowrap;background:linear-gradient(270deg,var(--atsprimaryColor),var(--atssecondaryColor) 105.29%);background-clip:text;-webkit-background-clip:text;-webkit-text-fill-color:transparent;animation:loading 6s linear infinite}@keyframes loading{0%{max-width:0}}']}),e})()}}]);