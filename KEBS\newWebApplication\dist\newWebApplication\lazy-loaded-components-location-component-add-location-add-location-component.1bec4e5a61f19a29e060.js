(window.webpackJsonp=window.webpackJsonp||[]).push([[755],{zmBJ:function(t,e,n){"use strict";n.r(e),n.d(e,"AddLocationComponent",(function(){return p}));var o=n("0IaG"),i=n("fXoL"),l=n("SC65"),a=n("1A3m"),c=n("NFeN"),s=n("Qu3c"),r=n("kmnG"),d=n("qFsG"),m=n("3Pt+");let p=(()=>{class t{constructor(t,e,n,o,i){this.dialogRef=t,this.locationData=e,this.dialog=n,this._timesheeetConfigService=o,this._toastService=i,this.headerText="Add Location"}ngOnInit(){this.location=this.locationData.modalParams.location?this.locationData.modalParams.location:null,this.locationId=this.locationData.modalParams.id?this.locationData.modalParams.id:null,console.log(this.location),this.headerText=null==this.location?"Add Location":"Edit Location"}closeForm(){this.dialogRef.close({event:"close"})}saveLocationData(){null!=this.location?this._timesheeetConfigService.addOrEditLocation(this.location,this.locationId).subscribe(t=>{"S"==t.messType?(this._toastService.showSuccess("Timesheet Configuration Message",t.messText,1e3),this.dialogRef.close()):(this._toastService.showInfo("Timesheet Configuration Message",t.messText,1e3),this.dialogRef.close())}):this._toastService.showInfo("Timesheet Configuration Message","Kindly Fill The Location",1e3)}}return t.\u0275fac=function(e){return new(e||t)(i["\u0275\u0275directiveInject"](o.h),i["\u0275\u0275directiveInject"](o.a),i["\u0275\u0275directiveInject"](o.b),i["\u0275\u0275directiveInject"](l.a),i["\u0275\u0275directiveInject"](a.a))},t.\u0275cmp=i["\u0275\u0275defineComponent"]({type:t,selectors:[["app-add-location"]],decls:25,vars:2,consts:[[1,"p-0","container-fluid","ts-add-location-styles"],[1,"row","p-0"],[1,"col-12","p-2"],[1,"row","border-bottom","solid"],[1,"col-10","pt-2","pb-2","pl-0","d-flex"],[1,"titleName","my-auto","ml-2",2,"color","#26303E"],[1,"col-1","d-flex","pt-2","pb-2"],["matTooltip","Close",2,"font-size","12px","cursor","pointer",3,"click"],[1,"col-12","pt-0","pl-2","pr-2","pb-2"],[1,"col-12","pl-2","sub-title"],[1,"col-12","pl-2"],["appearance","outline",2,"width","100%"],["matInput","","type","text",3,"ngModel","ngModelChange"],[1,"col-12","row","pt-2"],[1,"col-2","pl-0"],["mat-raised","",1,"active-btn",3,"click"],[1,"col-2"],[1,"inactive-btn",3,"click"]],template:function(t,e){1&t&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275elementStart"](1,"div",1),i["\u0275\u0275elementStart"](2,"div",2),i["\u0275\u0275elementStart"](3,"div",3),i["\u0275\u0275elementStart"](4,"div",4),i["\u0275\u0275elementStart"](5,"span",5),i["\u0275\u0275text"](6),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](7,"div",6),i["\u0275\u0275elementStart"](8,"mat-icon",7),i["\u0275\u0275listener"]("click",(function(){return e.closeForm()})),i["\u0275\u0275text"](9,"close"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](10,"div",1),i["\u0275\u0275elementStart"](11,"div",8),i["\u0275\u0275elementStart"](12,"div",3),i["\u0275\u0275elementStart"](13,"div",9),i["\u0275\u0275text"](14," Location "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](15,"div",10),i["\u0275\u0275elementStart"](16,"mat-form-field",11),i["\u0275\u0275elementStart"](17,"input",12),i["\u0275\u0275listener"]("ngModelChange",(function(t){return e.location=t})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](18,"div",13),i["\u0275\u0275elementStart"](19,"div",14),i["\u0275\u0275elementStart"](20,"button",15),i["\u0275\u0275listener"]("click",(function(){return e.saveLocationData()})),i["\u0275\u0275text"](21,"Save"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](22,"div",16),i["\u0275\u0275elementStart"](23,"button",17),i["\u0275\u0275listener"]("click",(function(){return e.closeForm()})),i["\u0275\u0275text"](24,"Cancel"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&t&&(i["\u0275\u0275advance"](6),i["\u0275\u0275textInterpolate"](e.headerText),i["\u0275\u0275advance"](11),i["\u0275\u0275property"]("ngModel",e.location))},directives:[c.a,s.a,r.c,d.b,m.e,m.v,m.y],styles:[".ts-add-location-styles[_ngcontent-%COMP%]   .titleName[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;font-weight:500;font-size:11px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;color:#111434}.ts-add-location-styles[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]{margin-top:5px!important;line-height:8px;width:50px;height:25px;margin-right:10px!important;border:none;background:#fff;color:#45546e;font-size:14px;font-weight:700}.ts-add-location-styles[_ngcontent-%COMP%]   .sub-title[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;font-weight:500;font-size:10px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;color:#6e7b8f}.ts-add-location-styles[_ngcontent-%COMP%]   .active-btn[_ngcontent-%COMP%]{background:#ee4961;border-radius:4px;color:#fff;border:1px solid #fff}.ts-add-location-styles[_ngcontent-%COMP%]   .inactive-btn[_ngcontent-%COMP%]{border:1px solid #45546e;border-radius:4px;color:#45546e;background-color:#fff;font-weight:500}"]}),t})()}}]);