(window.webpackJsonp=window.webpackJsonp||[]).push([[735,634,765,783,821,822,858,861,977,981,983,987,990,991],{"24aK":function(e,t,i){"use strict";i.r(t),i.d(t,"InternalStakeHolderModule",(function(){return W}));var s=i("ofXK"),a=i("wZkO"),r=i("NFeN"),n=i("bTqV"),o=i("Qu3c"),l=i("7EHt"),d=i("dlKe"),c=i("Xa2L"),u=i("kmnG"),h=i("qFsG"),p=i("3Pt+"),g=i("3beV"),m=i("Xi0T"),f=i("tyNb"),v=i("mrSG"),y=i("XNiG"),C=i("1G5W"),S=i("xG9w"),b=i("wd/R"),_=i("33Jv"),O=i("ZzPI"),D=i("jhN1"),A=i("fXoL"),I=i("v2fc"),w=i("6t9p");function x(e,t){if(1&e&&A["\u0275\u0275element"](0,"dxi-column",9),2&e){const e=t.$implicit;A["\u0275\u0275property"]("dataField",e.report_column)("allowReordering",!0)("caption",e.display_name)}}let k=(()=>{class e{constructor(e){this.IsaDetailService=e,this.tdsDetailColumn=[],this.reportColumns=[]}ngOnInit(){return Object(v.c)(this,void 0,void 0,(function*(){this.data=yield this.getTdsDetailsData(),console.log("data",this.data),this.tdsDetailColumn=[{report_column:"employee_name",display_name:"Vendor Name"},{report_column:"employee_email",display_name:"Vendor Email"}],console.log(this.tdsDetailColumn)}))}getTdsDetailsData(){return Object(v.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>{this.IsaDetailService.getVendorDetailsData().subscribe(t=>{e(t.data)},e=>{t(e)})})}))}}return e.\u0275fac=function(t){return new(t||e)(A["\u0275\u0275directiveInject"](I.a))},e.\u0275cmp=A["\u0275\u0275defineComponent"]({type:e,selectors:[["app-vendor-list"]],viewQuery:function(e,t){if(1&e&&A["\u0275\u0275viewQuery"](O.a,!0),2&e){let e;A["\u0275\u0275queryRefresh"](e=A["\u0275\u0275loadQuery"]())&&(t.dataGrid=e.first)}},decls:12,vars:13,consts:[[1,"container-fluid"],["id","gridContainer",1,"dev-style",3,"height","allowColumnResizing","dataSource","showBorders","hoverStateEnabled","columnAutoWidth"],["placeholder","Search...",3,"visible"],["mode","select",3,"enabled"],[3,"enabled"],[3,"allowFixing"],["mode","single"],[3,"visible"],[3,"dataField","allowReordering","caption",4,"ngFor","ngForOf"],[3,"dataField","allowReordering","caption"]],template:function(e,t){1&e&&(A["\u0275\u0275elementStart"](0,"div",0),A["\u0275\u0275elementStart"](1,"div"),A["\u0275\u0275elementStart"](2,"dx-data-grid",1),A["\u0275\u0275element"](3,"dxo-search-panel",2),A["\u0275\u0275element"](4,"dxo-column-chooser",3),A["\u0275\u0275element"](5,"dxo-column-fixing",4),A["\u0275\u0275element"](6,"dxi-column",5),A["\u0275\u0275element"](7,"dxo-selection",6),A["\u0275\u0275element"](8,"dxo-header-filter",7),A["\u0275\u0275element"](9,"dxo-filter-row",7),A["\u0275\u0275text"](10," --\x3e "),A["\u0275\u0275template"](11,x,1,3,"dxi-column",8),A["\u0275\u0275elementEnd"](),A["\u0275\u0275elementEnd"](),A["\u0275\u0275elementEnd"]()),2&e&&(A["\u0275\u0275advance"](2),A["\u0275\u0275property"]("height",840)("allowColumnResizing",!0)("dataSource",t.data)("showBorders",!0)("hoverStateEnabled",!0)("columnAutoWidth",!0),A["\u0275\u0275advance"](1),A["\u0275\u0275property"]("visible",!0),A["\u0275\u0275advance"](1),A["\u0275\u0275property"]("enabled",!0),A["\u0275\u0275advance"](1),A["\u0275\u0275property"]("enabled",!0),A["\u0275\u0275advance"](1),A["\u0275\u0275property"]("allowFixing",!1),A["\u0275\u0275advance"](2),A["\u0275\u0275property"]("visible",!0),A["\u0275\u0275advance"](1),A["\u0275\u0275property"]("visible",!0),A["\u0275\u0275advance"](2),A["\u0275\u0275property"]("ngForOf",t.tdsDetailColumn))},directives:[O.a,w.Md,w.tb,w.vb,w.g,w.Od,w.Cc,w.dc,s.NgForOf],styles:[""]}),e})();var T=i("XXEo"),R=i("GnQ3"),E=i("JLuW"),P=i("hJL4"),M=i("F97M"),q=i("LcQX"),N=i("flaP"),j=i("0IaG"),U=i("HmYF"),B=i("xi/V"),V=i("Wk3H");let F=(()=>{class e{constructor(e,t,i,s,a,r,n,o,l,d){this.loginService=e,this.udrfService=t,this.sharedLazyLoadedComponentsService=i,this.isaService=s,this.graphService=a,this.utilityService=r,this.roleService=n,this.dialog=o,this.excelService=l,this.$router=d,this.subs=new _.a,this.applicationId=139,this.IsaItemDataCurrentIndex=0,this.noDataFlag=0,this.headerData=[],this.requestData=[],this.skillList=[],this.status="S",this.itemCount=3,this.sectionCount=10,this.sections=[],this.items=[],this._onDestroy=new y.b,this._onAppApiCalled=new y.b,this.dataTypeArray=[],this.objectIdsBasedOnOid=[],this.skip=0,this.limit=15,this.defaultDataRetrievalCount=15,this.categorisedDataTypeArray=[],this.udrfBodyColumns=[{item:"request_number",header:"Request No.",isVisible:"true",type:"text",position:1,isActive:!0,colSize:"1",textClass:"value13Bold",sortOrder:"N",width:100},{item:"workflowHeaderId",header:"Workflow Id",isVisible:"true",type:"text",position:2,isActive:!0,colSize:"1",textClass:"value13Bold",sortOrder:"N",width:100},{item:"status_name",header:"Status",isVisible:"true",type:"status",position:3,isActive:!0,colSize:"2",textClass:"value13Bold",sortOrder:"N",width:200},{item:"orgName",header:"Organization",isVisible:"true",type:"text",position:4,isActive:!0,colSize:"2",textClass:"value13Bold",sortOrder:"N",width:240},{item:"resourceName",resourceName:"",header:"Resource Name",isVisible:"false",type:"text",position:5,isActive:!0,colSize:"1",textClass:"colorRed value13Bold",sortOrder:"N",width:200},{item:"totalCost",totalCost:"",header:"Total Cost",isVisible:"true",type:"RMGText",position:6,isActive:!0,colSize:"2",textClass:"value13Bold",headerTextClass:"",sortOrder:"N",width:220},{item:"originatorName",originatorName:"",header:"Originator",isVisible:"true",type:"text",position:7,isActive:!0,colSize:"2",textClass:"value13Bold",headerTextClass:"",sortOrder:"N",width:240},{item:"startDate",header:"Start Date",isVisible:"true",type:"date",position:8,isActive:!0,colSize:"1",textClass:"value13Bold",headerTextClass:"",sortOrder:"N",width:120},{item:"endDate",header:"End Date",isVisible:"true",type:"date",position:9,isActive:!0,colSize:"1",textClass:"value13Bold",headerTextClass:"",sortOrder:"N",width:120},{item:"locationName",header:"Location",isVisible:"false",type:"text",position:10,isActive:!0,colSize:"2",textClass:"value13Bold",sortOrder:"N",width:200},{item:"nationalityName",header:"Nationality",isVisible:"true",type:"text",position:11,isActive:!0,colSize:"1",textClass:"value13Bold",headerTextClass:"",sortOrder:"N",width:120},{item:"totalResource",header:"Total Resources",isVisible:"true",type:"text",position:12,isActive:!0,colSize:"1",textClass:"value13Bold",headerTextClass:"",sortOrder:"N",width:120},{item:"budget",header:"Standard Cost",isVisible:"true",type:"RMGText",position:13,isActive:!0,colSize:"1",textClass:"value13Bold",headerTextClass:"",sortOrder:"N",width:120},{item:"createdOn",header:"Created On",isVisible:"false",type:"date",position:14,isActive:!0,colSize:"1",textClass:"value14Bold",headerTextClass:"",sortOrder:"N",width:120},{item:"action",isActive:!0,header:"Actions",isVisible:"true",type:"action",position:15,colSize:2,sortOrder:"N",width:240},{item:"skillSetName",header:"Skillset",isVisible:"true",type:"text",position:16,isActive:!0,colSize:"2",textClass:"value13Bold",headerTextClass:"",sortOrder:"N",width:240},{item:"approvers",header:"Approver",isVisible:"false",type:"RMGprofileImgList",position:17,isActive:!0,colSize:"2",textClass:"value13Bold",headerTextClass:"",sortOrder:"N",width:240},{item:"objectName",header:"Project/Opportunity",isVisible:"true",type:"RMGText",position:18,isActive:!0,colSize:"2",textClass:"value13Bold",headerTextClass:"",sortOrder:"N",width:240},{item:"currencyConversionsBudget",header:"Standard Cost Converted",isVisible:"false",type:"RMGcurrency",position:19,isActive:!0,colSize:"2",textClass:"value13Bold",headerTextClass:"",sortOrder:"N",width:220},{item:"currencyConversionsTotal",header:"Total Cost Converted",isVisible:"false",type:"RMGcurrency",position:20,isActive:!0,colSize:"2",textClass:"value13Bold",headerTextClass:"",sortOrder:"N",width:220},{item:"resourceFinanceType",header:"Budgeted or Non-Budgeted",isVisible:"true",type:"RMGText",position:21,isActive:!0,colSize:"2",textClass:"value13Bold",headerTextClass:"",sortOrder:"N",width:220},{item:"currencyConvertedExpectedMinCTC",header:"Minimum CTC",isVisible:"false",type:"RMGcurrency",position:22,isActive:!0,colSize:"2",textClass:"value13Bold",headerTextClass:"",sortOrder:"N",width:220},{item:"currencyConvertedExpectedMaxCTC",header:"Maximum CTC",isVisible:"false",type:"RMGcurrency",position:23,isActive:!0,colSize:"2",textClass:"value13Bold",headerTextClass:"",sortOrder:"N",width:220}],this.udrfItemStatusColor=[],this.user=this.loginService.getProfile().profile,this.isCardClicked=!1,this.currentUser={},this.quickCTAInput={applicationId:139,objectId:0,objectType:"P",originatorData:null},this.commentsInput={application_id:139,unique_id_1:"",unique_id_2:"",application_name:"Internal Stakeholder Application",title:"",link:window.location.origin+"/main/isa/"},this.commentsContext={"Opportunity Name":"","Account Name":"","Sales SPOC":""},this.current_year_start=b(),this.current_year_end=b(),this.applicationRoleAccessList=[],this.isCurrentUserVendor=!1,this.getDataTypeArray=()=>new Promise((e,t)=>{this.isaService.getDataTypeArray().subscribe(i=>{"S"==i.messType?(e(i.data),this.dataTypeArray=i.data,this.categorisedDataTypeArray=[{categoryType:"Status Cards",categoryCardCodes:S.pluck(this.dataTypeArray,"dataTypeCode"),categoryCards:[]}],this.udrfService.udrfUiData.categorisedSummaryCards=this.categorisedDataTypeArray,this.setUdrfItemStatusColor()):"E"==i.messType&&t(i)},e=>{console.log(e),t(e)})}),this.onCreationReload=()=>{this.isaService.getMsg().subscribe(e=>{"close_ISA"==e&&(this.udrfService.udrfBodyData=[],this.udrfService.udrfData.isItemDataLoading=!0,this.isCardClicked=!1,this.IsaItemDataCurrentIndex=0,this.skip=0,this.udrfService.udrfUiData.resolveColumnConfig(),this.initISACard(),this.assignDataOnStatusCardClick())})},this.getObjectIdsBasedOnOid=()=>{this.isaService.getObjectIdsBasedOnOid(this.user.oid).pipe(Object(C.a)(this._onDestroy)).pipe(Object(C.a)(this._onAppApiCalled)).subscribe(e=>Object(v.c)(this,void 0,void 0,(function*(){this.objectIdsBasedOnOid=e.data})),e=>{console.log(e)})},this.assignDataOnStatusCardClick=()=>Object(v.c)(this,void 0,void 0,(function*(){let e=[];"AA"==this.status?(e=yield this.getRequestsForAwaitingApproval(),this.udrfService.udrfUiData.itemHasEdit=!1,this.udrfService.udrfUiData.itemHasApproval=!0):"ALR"==this.status?(e=yield this.getAllRequestsofUser(),this.udrfService.udrfUiData.itemHasEdit=!0,this.udrfService.udrfUiData.itemHasApproval=!1):(e=yield this.getRequestsBasedOnStatus(),this.udrfService.udrfUiData.itemHasEdit=!0,this.udrfService.udrfUiData.itemHasApproval=!1),0==this.isCardClicked&&""!=this.udrfService.udrfData.mainSearchParameter&&"AA"!=this.status&&"ALR"!=this.status&&S.each(this.dataTypeArray,e=>{e.isActive=!1}),0==e.length?(this.udrfService.udrfBodyData=this.udrfService.udrfBodyData.concat([]),this.udrfService.udrfData.isItemDataLoading=!1,this.udrfService.udrfData.noItemDataFound=!0):(this.udrfService.udrfBodyData=this.udrfService.udrfBodyData.concat(e),this.udrfService.udrfData.isItemDataLoading=!1)})),this.getAllRequestsofUser=()=>Object(v.c)(this,void 0,void 0,(function*(){let e=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),t=this.getFilters(e),i={start_date:this.udrfService.udrfData.mainApiDateRangeStart,end_date:this.udrfService.udrfData.mainApiDateRangeEnd,mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,isCardClicked:this.isCardClicked,skip:this.skip,limit:this.limit,filter:t};return new Promise((e,t)=>{let s=this.user.oid,a=S.findWhere(this.dataTypeArray,{dataTypeCode:this.status}),r=a?a.statusId:null,n=this.status,o=this.objectIdsBasedOnOid;this.requestDataSubscription&&this.requestDataSubscription.unsubscribe(),this.requestDataSubscription=this.isaService.getAllRequestsOfUser(s,r,n,o,this.skip,this.limit,i).pipe(Object(C.a)(this._onDestroy)).pipe(Object(C.a)(this._onAppApiCalled)).subscribe(i=>{"S"==i.messType&&i.data?(this.setStatusCountOnClick(i.totalCount),e(i.data)):t(i)})})})),this.getRequestsBasedOnStatus=()=>Object(v.c)(this,void 0,void 0,(function*(){let e=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),t=this.getFilters(e),i={start_date:this.udrfService.udrfData.mainApiDateRangeStart,end_date:this.udrfService.udrfData.mainApiDateRangeEnd,mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,isCardClicked:this.isCardClicked,skip:this.skip,limit:this.limit,filter:t};return new Promise((e,t)=>{let s=this.user.oid,a=S.findWhere(this.dataTypeArray,{dataTypeCode:this.status}),r=a?a.statusId:null,n=this.status,o=this.objectIdsBasedOnOid;this.requestDataSubscription&&this.requestDataSubscription.unsubscribe(),this.requestDataSubscription=this.isaService.getRequestsBasedOnStatus(s,r,n,o,this.skip,this.limit,i).pipe(Object(C.a)(this._onDestroy)).pipe(Object(C.a)(this._onAppApiCalled)).subscribe(i=>{"S"==i.messType&&i.data?(e(i.data),this.setStatusCountOnClick(i.totalCount)):t(i)})})})),this.getRequestsForAwaitingApproval=()=>Object(v.c)(this,void 0,void 0,(function*(){let e=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),t=this.getFilters(e),i={start_date:this.udrfService.udrfData.mainApiDateRangeStart,end_date:this.udrfService.udrfData.mainApiDateRangeEnd,mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,isCardClicked:this.isCardClicked,skip:this.skip,limit:this.limit,filter:t};return new Promise((e,t)=>{let s=this.user.oid;this.requestDataSubscription&&this.requestDataSubscription.unsubscribe(),this.requestDataSubscription=this.isaService.getRequestsForAwaitingApproval(s,this.skip,this.limit,i).pipe(Object(C.a)(this._onDestroy)).pipe(Object(C.a)(this._onAppApiCalled)).subscribe(i=>{"S"==i.messType&&i.data?(this.setStatusCountOnClick(i.totalCount),e(i.data)):t(i)})})})),this.setStatusCountOnClick=e=>{S.map(this.dataTypeArray,t=>{t.dataTypeCode==this.status&&(t.dataTypeValue=e)})},this.getStatusCountBasedOnOid=()=>Object(v.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>{let i=this.user.oid,s=this.objectIdsBasedOnOid,a=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),r=this.getFilters(a);this.isaService.getStatusCountBasedOnOid(i,s,this.dataTypeArray,{start_date:this.udrfService.udrfData.mainApiDateRangeStart,end_date:this.udrfService.udrfData.mainApiDateRangeEnd,mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,skip:this.skip,limit:this.limit,isCardClicked:this.isCardClicked,filter:r}).pipe(Object(C.a)(this._onDestroy)).pipe(Object(C.a)(this._onAppApiCalled)).subscribe(i=>{if("S"==i.messType&&i.data&&i.data.length>0){this.udrfService.udrfUiData.totalItemDataCount=0;for(let e=0;e<i.data.length;e++)"ALR"!=i.data[e].dataTypeCode&&"AA"!=i.data[e].dataTypeCode&&(this.udrfService.udrfUiData.totalItemDataCount=this.udrfService.udrfUiData.totalItemDataCount+Number(i.data[e].dataTypeValue));e(i.data)}else this.udrfService.udrfUiData.totalItemDataCount=0,t(i)})})})),this.openEdit=()=>{console.log("Edit");let e=this.udrfService.udrfUiData.openEditData.data;if(console.log("Card Data",e),null==e?void 0:e.isFromVendor)this.utilityService.showMessage("Access denied","Dismiss",3e3);else if(console.log("role - "+e.role),"current_approver"!=e.role){let t={applicationId:null,objectType:"L",objectId:e,type:"Edit",originatorData:null,statusArray:S.pluck(this.dataTypeArray,"dataType")};console.log("Quick isa input value",t),this.sharedLazyLoadedComponentsService.openQuickISAModal(t,this.dialog)}else this.udrfService.udrfUiData.itemHasEdit=!1},this.openRequestDetailPageInNewTab=()=>{let e=`${window.location.origin}/main/isa/isaDetails/${this.udrfService.udrfUiData.openinNewTabData.data._id}`;window.open(e)},this.onApproval=()=>Object(v.c)(this,void 0,void 0,(function*(){let e=this.udrfService.udrfUiData.openApprovalData.data;console.log(e),"A"==e.clickStatus?yield this.approveRequest(e):"R"==e.clickStatus&&(yield this.rejectRequest(e))})),this.onApprovalReload=()=>{this.status="AA",this.initISA()},this.approveRequest=e=>new Promise((t,i)=>{this.udrfService.udrfUiData.inApproval=!0,this.isaService.approveRequest(e,this.user.oid).subscribe(i=>{console.log("approveRequest"),console.log(i),t(i),this.onApprovalReload(),this.udrfService.udrfUiData.inApproval=!1,this.utilityService.showMessage(`Request - ${e.requestNo} Approved successfully !`,"Dismiss",3e3)},t=>{console.log(t),i(t),this.udrfService.udrfUiData.inApproval=!1,this.utilityService.showMessage(`Request - ${e.requestNo} failed to be approved !`,"Dismiss",3e3)})}),this.rejectRequest=e=>new Promise((t,i)=>{this.udrfService.udrfUiData.inApproval=!0,this.isaService.rejectRequest(e,this.user.oid).subscribe(i=>{console.log("rejectRequest"),console.log(i),t(i),this.onApprovalReload(),this.udrfService.udrfUiData.inApproval=!1,this.utilityService.showMessage(`Request - ${e.requestNo} Rejected successfully !`,"Dismiss",3e3)},t=>{console.log(t),i(t),this.udrfService.udrfUiData.inApproval=!1,this.utilityService.showMessage(`Request - ${e.requestNo} Rejection Failed !`,"Dismiss",3e3)})}),this.checkVendorOrNot=()=>{this.isaService.checkVendorOrNot().subscribe(e=>{"N"==e.err?1!=e.data?this.udrfService.udrfUiData.showCreateNewComponentButton=!0:this.isCurrentUserVendor=!0:"Y"==e.messType&&(console.log(e),this.utilityService.showMessage("Error while checking vendor or not","Dismiss",3e3))},e=>{console.log(e),this.utilityService.showMessage("Error while checking vendor or not","Dismiss",3e3)})},this.currentUser=this.loginService.getProfile().profile,this.subs.add(this.requestDataSubscription)}ngOnInit(){var e,t;return Object(v.c)(this,void 0,void 0,(function*(){if(yield this.getDefaultConfigs(),this.totalResourcesLabel&&this.totalResourcesLabel.length>0)for(let e=0;e<this.udrfBodyColumns.length;e++)"totalResource"==this.udrfBodyColumns[e].item&&"true"==this.udrfBodyColumns[e].isVisible&&(this.udrfBodyColumns[e].header=this.totalResourcesLabel);else console.log("Total resource value name not found");this.roleBasedReportActivity();let i=[{checkboxId:"CDCRD",checkboxName:"Current Year",checkboxStartValue:b().startOf("year"),checkboxEndValue:b().endOf("year"),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD2",checkboxName:"This Week",checkboxStartValue:this.utilityService.getFormattedDate(b().startOf("week"),b(b().startOf("week")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(b().endOf("week"),b(b().endOf("week")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD3",checkboxName:"This Month",checkboxStartValue:this.utilityService.getFormattedDate(b().startOf("month"),b(b().startOf("month")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(b().endOf("month"),b(b().endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD4",checkboxName:"Next Month",checkboxStartValue:this.utilityService.getFormattedDate(b().add(1,"month").startOf("month"),b(b().add(1,"month").startOf("month")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(b().add(1,"month").endOf("month"),b(b().add(1,"month").endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD5",checkboxName:"Upcoming 3 Months",checkboxStartValue:this.utilityService.getFormattedDate(b().startOf("month"),b(b().startOf("month")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(b().add(2,"month").endOf("month"),b(b().add(2,"month").endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD6",checkboxName:"All",checkboxStartValue:b("1920-01-01"),checkboxEndValue:b("2100-12-12"),isCheckboxDefaultSelected:!0}];this.udrfService.udrfFunctions.constructCustomRangeData(8,"date",i),this.onCreationReload(),this.getDefaultISAStatusToDisplay(),this.initUdrf(),this.udrfService.getNotifyReleasesUDRF(),this.checkVendorOrNot(),this.applicationRoleAccessList=yield this.isaService.getAllRoleAccess();for(let s=0;s<this.applicationRoleAccessList.length;s++)520==(null===(e=this.applicationRoleAccessList[s])||void 0===e?void 0:e.object_id)?this.udrfService.udrfUiData.showCreateVendorComponentButton=!0:519==(null===(t=this.applicationRoleAccessList[s])||void 0===t?void 0:t.object_id)&&(this.udrfService.udrfUiData.showVendorList=!0)}))}setUdrfItemStatusColor(){let e=[];this.dataTypeArray.forEach(t=>{e.push({status:t.dataType,color:t.statusColor})}),e.push(...this.isaService.taskStatusColor),this.udrfService.udrfUiData.udrfItemStatusColor=e}initUdrf(){this.udrfService.udrfBodyData=[],this.udrfService.udrfUiData.itemHasQuickCta=!0,this.udrfService.udrfUiData.itemHasComments=!0,this.udrfService.udrfUiData.itemHasEdit=!0,this.udrfService.udrfUiData.itemHasOpenInNewTab=!0,this.udrfService.udrfUiData.showNewReleasesButton=!0,this.udrfService.udrfUiData.showCreateNewComponentButton=!1,this.udrfService.udrfUiData.showCreateVendorComponentButton=!1,this.getDataTypeArray(),this.getObjectIdsBasedOnOid(),this.IsaItemDataCurrentIndex=0,this.udrfService.udrfUiData.showItemDataCount=!0,this.udrfService.udrfUiData.itemDataType="",this.udrfService.udrfUiData.totalItemDataCount=0,this.udrfService.udrfUiData.showSearchBar=!0,this.udrfService.udrfUiData.showActionButtons=!0,this.udrfService.udrfUiData.showUdrfModalButton=!0,this.udrfService.udrfUiData.showSettingsModalButton=!0,this.udrfService.udrfUiData.isReportDownloading=!1,this.udrfService.udrfUiData.showReportDownloadButton=!1,this.udrfService.udrfUiData.showColumnConfigButton=!0,this.udrfService.udrfUiData.udrfItemStatusColor=this.udrfItemStatusColor,this.udrfService.udrfUiData.resolveVisibleSummaryCards=this.resolveVisibleDataTypeArray.bind(this),this.udrfService.udrfUiData.summaryCardsSelected=this.dataTypeCardSelected.bind(this),this.udrfService.udrfUiData.itemcardSelected=this.openModal.bind(this),this.udrfService.udrfUiData.summaryCardsItem={},this.udrfService.udrfUiData.downloadItemDataReport=()=>{},this.udrfService.udrfUiData.itemDataScrollDown=this.itemDataScrollDown.bind(this),this.udrfService.udrfUiData.updateItemCard=()=>{},this.udrfService.udrfUiData.attachFile=()=>{},this.udrfService.udrfUiData.deleteFile=()=>{},this.udrfService.udrfUiData.downloadFile=()=>{},this.udrfService.udrfUiData.closeCTA=()=>{},this.udrfService.udrfUiData.showActualCurrencyForCurrencyComponent=!0,this.udrfService.udrfUiData.openEdit=this.openEdit.bind(this),this.udrfService.udrfUiData.openComments=this.openComments.bind(this),this.udrfService.udrfUiData.openQuickCta=this.openCTA.bind(this),this.udrfService.udrfUiData.onApproval=this.onApproval.bind(this),this.udrfService.udrfUiData.openInNewTab=this.openRequestDetailPageInNewTab.bind(this),this.udrfService.udrfUiData.udrfBodyColumns=this.udrfBodyColumns,this.udrfService.udrfUiData.udrfVisibleBodyColumns=this.udrfService.udrfUiData.udrfVisibleBodyColumns,this.udrfService.udrfUiData.udrfInvisibleBodyColumns=this.udrfService.udrfUiData.udrfInvisibleBodyColumns,this.udrfService.udrfUiData.summaryCards=this.dataTypeArray,this.udrfService.udrfUiData.minNoOfVisibleSummaryCards=3,this.udrfService.udrfUiData.maxNoOfVisibleSummaryCards=6,this.udrfService.udrfUiData.selectedCard=[],this.udrfService.udrfUiData.variant=1,this.udrfService.udrfUiData.horizontalScroll=!0,this.udrfService.udrfUiData.quickCTAInput=this.quickCTAInput,this.udrfService.udrfUiData.commentsInput=this.commentsInput,this.udrfService.udrfUiData.commentsContext=this.commentsContext,this.udrfService.udrfUiData.showCollapseButton=!0,this.udrfService.udrfUiData.collapseAll=!0,this.udrfService.udrfUiData.createNewComponent=this.openQuickISA.bind(this),console.log("Before new vendor bind"),this.udrfService.udrfUiData.isMoreOptionsNeeded=!0,this.udrfService.udrfUiData.createNewVendorComponent=this.openQuickVendor.bind(this),this.udrfService.udrfUiData.downloadItemDataReport=this.downloadISASLAreport.bind(this),this.udrfService.getAppUdrfConfig(this.applicationId,this.initISA.bind(this)),this.udrfService.udrfData.applicationId=this.applicationId,this.udrfService.udrfUiData.showVendorList=!1,this.udrfService.udrfUiData.openVendorList=this.showVendorList.bind(this)}initISA(){this._onAppApiCalled.next(),this.isCardClicked=!1,this.IsaItemDataCurrentIndex=0,this.skip=0,this.udrfService.udrfBodyData=[],this.udrfService.udrfUiData.resolveColumnConfig(),this.initISACard(),this.initISAList()}getDefaultConfigs(){return new Promise((e,t)=>{this.sharedLazyLoadedComponentsService.getISAConfigs().subscribe(t=>{t&&t.data.length>0?(this.totalResourcesLabel=t.data[0].totalResourcesLabel?t.data[0].totalResourcesLabel:"Total Resources",this.positionBasedApproval=!!t.data[0].positionBasedApproval&&t.data[0].positionBasedApproval,e(t.data)):this.totalResourcesLabel=""},e=>{console.log(e),t(e)})})}downloadIsaActivityReport(){this.udrfService.udrfUiData.isReportDownloading=!0,this.isaService.getIsaActivityReportDownload().subscribe(e=>Object(v.c)(this,void 0,void 0,(function*(){"N"==e.err&&e.data.length>0?(this.udrfService.udrfUiData.isReportDownloading=!1,this.excelService.exportAsExcelFile(e.data,"ISA Activity Report"),this.utilityService.showMessage(e.messText,"Dismiss",3e3)):(this.udrfService.udrfUiData.isReportDownloading=!1,this.utilityService.showMessage(e.messText,"Dismiss",3e3))})),e=>{console.log(e),this.udrfService.udrfUiData.isReportDownloading=!1,this.utilityService.showMessage("Please Contact KEBS team!","Dismiss",3e3)})}downloadISASLAreport(){let e=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),t=this.getFilters(e),i={start_date:this.udrfService.udrfData.mainApiDateRangeStart,end_date:this.udrfService.udrfData.mainApiDateRangeEnd,mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,skip:this.skip,limit:this.limit,isCardClicked:this.isCardClicked,filter:t};this.positionBasedApproval?(this.udrfService.udrfUiData.isReportDownloading=!0,this.isaService.getISACustomReport(i).subscribe(e=>Object(v.c)(this,void 0,void 0,(function*(){"N"==e.err&&e.data.length>0?(this.udrfService.udrfUiData.isReportDownloading=!1,this.excelService.exportAsExcelFile(e.data,"KEBS ISA Report"),this.utilityService.showMessage(e.messText,"Dismiss",3e3)):(this.udrfService.udrfUiData.isReportDownloading=!1,this.utilityService.showMessage(e.messText,"Dismiss",3e3))})))):(this.udrfService.udrfUiData.isReportDownloading=!0,this.isaService.getISASLAreport(i).subscribe(e=>Object(v.c)(this,void 0,void 0,(function*(){"N"==e.err&&e.data.length>0?(this.udrfService.udrfUiData.isReportDownloading=!1,this.excelService.exportAsExcelFile(e.data,"ISA SLA Aging Report"),this.utilityService.showMessage(e.messText,"Dismiss",3e3)):(this.udrfService.udrfUiData.isReportDownloading=!1,this.utilityService.showMessage(e.messText,"Dismiss",3e3))})),e=>{console.log(e),this.udrfService.udrfUiData.isReportDownloading=!1,this.utilityService.showMessage("Please Contact KEBS team!","Dismiss",3e3)}))}initISAList(){return Object(v.c)(this,void 0,void 0,(function*(){this.noDataFlag=0,this.udrfService.udrfBodyData=[],this.skip=0,this.assignDataOnStatusCardClick()}))}getFilters(e){let t=[];for(let i of e)t.push(i.isIdBased?{filterName:i.filterName,valueId:i.multiOptionSelectSearchValuesWithId}:{filterName:i.filterName,valueId:i.multiOptionSelectSearchValues});return t}itemDataScrollDown(){return Object(v.c)(this,void 0,void 0,(function*(){this.udrfService.udrfData.noItemDataFound||this.udrfService.udrfData.isItemDataLoading||(this.skip+=this.limit,this.udrfService.udrfData.isItemDataLoading=!0,this.assignDataOnStatusCardClick())}))}roleBasedReportActivity(){return Object(v.c)(this,void 0,void 0,(function*(){let e=yield this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");this.udrfService.udrfUiData.showReportDownloadButton="ALL"==e}))}initISACard(){return Object(v.c)(this,void 0,void 0,(function*(){for(let e=0;e<this.dataTypeArray.length;e++)this.dataTypeArray[e].dataTypeValue="0";this.dataTypeArray=yield this.getStatusCountBasedOnOid(),this.udrfService.udrfUiData.summaryCards=this.dataTypeArray,this.udrfService.udrfData.udrfSummaryCardCodes.length>0&&this.resolveVisibleDataTypeArray()}))}dataTypeCardSelected(){this.udrfService.udrfData.isItemDataLoading=!0,this.udrfService.udrfData.noItemDataFound=!1;let e=this.udrfService.udrfUiData.summaryCardsItem;for(let t=0;t<this.dataTypeArray.length;t++)if(e.dataTypeCode==this.dataTypeArray[t].dataTypeCode)this.dataTypeArray[t].isActive=!0,e.isActive=!0;else{let e=S.where(this.udrfService.udrfUiData.summaryCards,{dataTypeCode:this.dataTypeArray[t].dataTypeCode});e.length>0&&(e[0].isActive=!1),this.dataTypeArray[t].isActive=!1}this.status=e.dataTypeCode,this.isCardClicked=!0,this.udrfService.udrfBodyData=[],this.IsaItemDataCurrentIndex=0,this.initISAList()}openModal(){return Object(v.c)(this,void 0,void 0,(function*(){if("AA"==this.status){let e=this.udrfService.udrfUiData.itemCardSelecteditem;console.log("requestDetail"),console.log(e);let t=null;if(t=S.findWhere(e.l2,{workflowHeaderId:e.workflowHeaderId}),t&&t.form_id){const{CustomFormModalComponent:e}=yield Promise.all([i.e(1),i.e(7),i.e(8),i.e(9),i.e(19),i.e(44),i.e(48),i.e(52),i.e(984)]).then(i.bind(null,"mGD6"));this.dialog.open(e,{height:"100%",width:"75%",position:{right:"0px"},data:{formId:t.form_id,isEdit:!1,entryForId:t.task_id}})}else this.sharedLazyLoadedComponentsService.openQuickISAModal({applicationId:null,objectType:"L",objectId:this.udrfService.udrfUiData.itemCardSelecteditem,type:"Approval",originatorData:null},this.dialog)}else this.$router.navigateByUrl("/main/isa/isaDetails/"+this.udrfService.udrfUiData.itemCardSelecteditem._id)}))}resolveVisibleDataTypeArray(){return Object(v.c)(this,void 0,void 0,(function*(){for(let e of this.udrfService.udrfUiData.summaryCards){let t;this.udrfService.udrfData.udrfSummaryCardCodes.length>0&&(t=S.contains(this.udrfService.udrfData.udrfSummaryCardCodes,e.dataTypeCode),e.isVisible=t,t&&(this.udrfService.udrfUiData.summaryCardsItem=e))}}))}openCTA(){let e=this.udrfService.udrfUiData.openQuickCtaData.data;this.sharedLazyLoadedComponentsService.openQuickCTAModal({applicationId:139,objectId:e.objectId,objectType:e.objectType,originatorData:null},this.dialog)}openComments(){return Object(v.c)(this,void 0,void 0,(function*(){let e=this.udrfService.udrfUiData.openCommentsData.data,t={inputData:{application_id:139,unique_id_1:e._id,unique_id_2:"",application_name:"Internal Stakeholder Application",title:`Request #${e.requestNo} - ${e.skillSetName}`},context:{"Request number":e.requestNo,"Skill Set":e.skillSetName,["P"==e.objectType?"Project":"Opportunityname"]:e.objectName,"Resource name":e.resourceName?e.resourceName:"Not Assigned"},commentBoxHeight:"100vh",commentBoxScrollHeight:"80%"};this.isCurrentUserVendor?(t.showOwnComments=!0,t.showOthersComments=!1):(t.showOwnComments=!0,t.showOthersComments=!0),console.log("Modal params value",t);const{ChatCommentContextModalComponent:s}=yield Promise.all([i.e(4),i.e(63),i.e(75),i.e(983)]).then(i.bind(null,"vg2w"));this.dialog.open(s,{height:"100%",width:"75%",position:{right:"0px"},data:{modalParams:t}})}))}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete(),this.udrfService.resetUdrfData(),this.subs.unsubscribe()}getDefaultISAStatusToDisplay(){this.isaService.getDefaultISAStatusToDisplay().pipe(Object(C.a)(this._onDestroy)).subscribe(e=>{this.status="S"==e.messType&&e.data?e.data.status:"S"},e=>{this.status="S",this.utilityService.showMessage("Error in Default Retriving Status","Dismiss",3e3)})}openQuickISA(){return Object(v.c)(this,void 0,void 0,(function*(){const{QuickIsaModalComponent:e}=yield Promise.all([i.e(4),i.e(19),i.e(113),i.e(186),i.e(0),i.e(991)]).then(i.bind(null,"yIhA"));this.dialog.open(e,{height:"100%",width:"75%",position:{right:"0px"},data:{modalParams:null}}).afterClosed().subscribe(e=>{"Submit"==e.event&&this.isaService.sendMsg("close_ISA")})}))}openQuickVendor(){return Object(v.c)(this,void 0,void 0,(function*(){console.log("Inside quick vendor component");const{QuickVendorModalComponent:e}=yield Promise.all([i.e(19),i.e(993)]).then(i.bind(null,"tx/t"));this.dialog.open(e,{height:"100%",width:"30%",position:{right:"0px"},data:{modalParams:null}}).afterClosed().subscribe(e=>{"Submit"==e.event&&this.isaService.sendMsg("close_ISA")})}))}showVendorList(){return Object(v.c)(this,void 0,void 0,(function*(){this.dialog.open(k,{height:"100%",width:"60%",position:{right:"10px"},data:{mode:"Create"}})}))}}return e.\u0275fac=function(t){return new(t||e)(A["\u0275\u0275directiveInject"](T.a),A["\u0275\u0275directiveInject"](R.a),A["\u0275\u0275directiveInject"](E.a),A["\u0275\u0275directiveInject"](P.a),A["\u0275\u0275directiveInject"](M.a),A["\u0275\u0275directiveInject"](q.a),A["\u0275\u0275directiveInject"](N.a),A["\u0275\u0275directiveInject"](j.b),A["\u0275\u0275directiveInject"](U.a),A["\u0275\u0275directiveInject"](f.g))},e.\u0275cmp=A["\u0275\u0275defineComponent"]({type:e,selectors:[["app-isa-landing-page"]],decls:3,vars:0,consts:[[1,"container-fluid","isa-landing-page-styles","pl-0","pr-0"]],template:function(e,t){1&e&&(A["\u0275\u0275elementStart"](0,"div",0),A["\u0275\u0275element"](1,"udrf-header"),A["\u0275\u0275element"](2,"udrf-body"),A["\u0275\u0275elementEnd"]())},directives:[B.a,V.a],styles:[".isa-landing-page-styles[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field{width:40vw;padding-top:5px}.isa-landing-page-styles[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-infix{font-size:14px!important;padding:.5em 0!important;border-top:.54375em solid transparent!important}.isa-landing-page-styles[_ngcontent-%COMP%]   .spinner-align[_ngcontent-%COMP%]{margin:auto;display:inline-block}.isa-landing-page-styles[_ngcontent-%COMP%]   .infinite-scroll-fixed[_ngcontent-%COMP%]{height:395px;overflow-y:scroll}.isa-landing-page-styles[_ngcontent-%COMP%]   .infinite-scroll-fixed-shortened[_ngcontent-%COMP%]{height:345px;overflow-y:scroll}.isa-landing-page-styles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff;font-weight:400;font-size:13px!important;min-width:60px;line-height:30px;padding:0 25px;border-radius:0!important}.isa-landing-page-styles[_ngcontent-%COMP%]   .ta-r[_ngcontent-%COMP%]{text-align:right!important}.isa-landing-page-styles[_ngcontent-%COMP%]   .ta-l[_ngcontent-%COMP%]{text-align:left}.isa-landing-page-styles[_ngcontent-%COMP%]   .is-normal-probability-text[_ngcontent-%COMP%]{color:grey!important}.isa-landing-page-styles[_ngcontent-%COMP%]   .is-low-probability-text[_ngcontent-%COMP%]{color:#cf0001!important}.isa-landing-page-styles[_ngcontent-%COMP%]   .is-moderate-probability-text[_ngcontent-%COMP%]{color:#ff7200!important}.isa-landing-page-styles[_ngcontent-%COMP%]   .is-high-probability-text[_ngcontent-%COMP%]{color:#009432!important}.isa-landing-page-styles[_ngcontent-%COMP%]   .is-normal-probability[_ngcontent-%COMP%]{border-left-color:grey!important}.isa-landing-page-styles[_ngcontent-%COMP%]   .is-low-probability[_ngcontent-%COMP%]{border-left-color:#cf0001!important}.isa-landing-page-styles[_ngcontent-%COMP%]   .is-moderate-probability[_ngcontent-%COMP%]{border-left-color:#ff7200!important}.isa-landing-page-styles[_ngcontent-%COMP%]   .is-high-probability[_ngcontent-%COMP%]{border-left-color:#009432!important}.isa-landing-page-styles[_ngcontent-%COMP%]   .value13Bold[_ngcontent-%COMP%]{color:#000;font-weight:500!important}.isa-landing-page-styles[_ngcontent-%COMP%]   .value13Bold[_ngcontent-%COMP%], .isa-landing-page-styles[_ngcontent-%COMP%]   .value13light[_ngcontent-%COMP%]{font-size:13px!important;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.isa-landing-page-styles[_ngcontent-%COMP%]   .value13light[_ngcontent-%COMP%]{color:#4e4c4c;font-weight:400!important}.isa-landing-page-styles[_ngcontent-%COMP%]   .value14Bold[_ngcontent-%COMP%]{color:#000;font-size:14px!important;font-weight:500!important;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.isa-landing-page-styles[_ngcontent-%COMP%]   .listcard[_ngcontent-%COMP%]{transition:all .25s linear;height:auto}.isa-landing-page-styles[_ngcontent-%COMP%]   .smallSubtleText[_ngcontent-%COMP%]{color:#66615b;font-size:11px}.isa-landing-page-styles[_ngcontent-%COMP%]   .data-type-card[_ngcontent-%COMP%]{min-height:85px;transition:all .3s linear}.isa-landing-page-styles[_ngcontent-%COMP%]   .data-type-card-is-active[_ngcontent-%COMP%]{border-color:#cf0001;background-color:#fff2f2}.isa-landing-page-styles[_ngcontent-%COMP%]   .headingBold[_ngcontent-%COMP%]{color:#1a1a1a;font-weight:600;font-size:20px}.isa-landing-page-styles[_ngcontent-%COMP%]   .valueGrey14[_ngcontent-%COMP%]{font-size:14px}.isa-landing-page-styles[_ngcontent-%COMP%]   .valueGrey12Normal[_ngcontent-%COMP%], .isa-landing-page-styles[_ngcontent-%COMP%]   .valueGrey14[_ngcontent-%COMP%]{color:#4a4a4a;font-weight:400;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.isa-landing-page-styles[_ngcontent-%COMP%]   .valueGrey12Normal[_ngcontent-%COMP%]{font-size:12px}.isa-landing-page-styles[_ngcontent-%COMP%]   .cp[_ngcontent-%COMP%]{cursor:pointer!important}.isa-landing-page-styles[_ngcontent-%COMP%]   .col-2-2[_ngcontent-%COMP%]{flex:0 0 11%;max-width:11%}.isa-landing-page-styles[_ngcontent-%COMP%]   .col-2-8[_ngcontent-%COMP%]{flex:0 0 21.666667%;max-width:21.666667%}.isa-landing-page-styles[_ngcontent-%COMP%]   .alignCenter[_ngcontent-%COMP%]{margin:0 auto;display:block}.isa-landing-page-styles[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]{margin-top:5px!important;line-height:8px;width:26px;height:26px;margin-right:10px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12);animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}.isa-landing-page-styles[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%], .isa-landing-page-styles[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.isa-landing-page-styles[_ngcontent-%COMP%]   .ta-c[_ngcontent-%COMP%]{text-align:center;padding-left:0!important;padding-right:0!important}.isa-landing-page-styles[_ngcontent-%COMP%]   .divAlignItemCenter[_ngcontent-%COMP%]{display:flex;align-items:center}.isa-landing-page-styles[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#3c3c3c;font-weight:400;font-size:18px}.isa-landing-page-styles[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}.isa-landing-page-styles[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}.cp[_ngcontent-%COMP%]{cursor:pointer!important}.value14Bold[_ngcontent-%COMP%]{color:#000;font-size:14px!important;font-weight:500!important;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}"]}),e})();var L=i("BVzC");const z=[{path:"",component:F},{path:"isaDetails/:requestId",canActivate:[(()=>{class e{constructor(e,t,i){this._isaService=e,this._util=t,this._error=i}canActivate(e){var t;return Object(v.c)(this,void 0,void 0,(function*(){let i=null===(t=e.params)||void 0===t?void 0:t.requestId;return new Promise((e,t)=>{this._isaService.checkIfUserHasAccessToRequest(i).subscribe(t=>{"S"==t.messType&&t.data&&t.data.hasAccess?e(!0):(this._util.showToastMessage(t.messText),e(!1))},e=>{this._error.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage),t(e)})})}))}}return e.\u0275fac=function(t){return new(t||e)(A["\u0275\u0275inject"](I.a),A["\u0275\u0275inject"](q.a),A["\u0275\u0275inject"](L.a))},e.\u0275prov=A["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()],loadChildren:()=>i.e(734).then(i.bind(null,"xGdZ")).then(e=>e.IsaDetailsModule)}];let H=(()=>{class e{}return e.\u0275mod=A["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=A["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[f.k.forChild(z)],f.k]}),e})();var $=i("rDax");let W=(()=>{class e{}return e.\u0275mod=A["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=A["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[s.CommonModule,r.b,n.b,o.b,H,a.g,m.a,l.b,d.b,c.b,u.e,h.c,p.p,p.E,g.a,$.h,D.b,O.b]]}),e})()},H44p:function(e,t,i){"use strict";i.d(t,"a",(function(){return f}));var s=i("xG9w"),a=i("fXoL"),r=i("flaP"),n=i("ofXK"),o=i("Qu3c"),l=i("NFeN");function d(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",9),a["\u0275\u0275elementStart"](1,"div",10),a["\u0275\u0275elementStart"](2,"div"),a["\u0275\u0275text"](3),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](4,"div"),a["\u0275\u0275elementStart"](5,"p",11),a["\u0275\u0275text"](6),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](7,"p",12),a["\u0275\u0275text"](8),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"](2);a["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),a["\u0275\u0275advance"](3),a["\u0275\u0275textInterpolate"](null==e.currency[e.index]?null:e.currency[e.index].currency_code),a["\u0275\u0275advance"](3),a["\u0275\u0275textInterpolate"](e.label),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate1"](" ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function c(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",13),a["\u0275\u0275elementStart"](1,"span"),a["\u0275\u0275text"](2),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"](2);a["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate1"](" ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function u(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",14),a["\u0275\u0275elementStart"](1,"span",15),a["\u0275\u0275text"](2),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"](2);a["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function h(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",16),a["\u0275\u0275elementStart"](1,"span",15),a["\u0275\u0275text"](2),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"](2);a["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function p(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div",17),a["\u0275\u0275elementStart"](1,"span",18),a["\u0275\u0275text"](2),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"](2);a["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function g(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"mat-icon",19),a["\u0275\u0275text"](1,"loop"),a["\u0275\u0275elementEnd"]())}function m(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"div",1),a["\u0275\u0275listener"]("click",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"]().change()})),a["\u0275\u0275template"](1,d,9,4,"div",2),a["\u0275\u0275template"](2,c,3,2,"div",3),a["\u0275\u0275template"](3,u,3,3,"div",4),a["\u0275\u0275template"](4,h,3,3,"div",5),a["\u0275\u0275template"](5,p,3,3,"div",6),a["\u0275\u0275elementStart"](6,"div",7),a["\u0275\u0275template"](7,g,2,0,"mat-icon",8),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()}if(2&e){const e=a["\u0275\u0275nextContext"]();a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf","big"==e.type),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf","small"==e.type),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf","medium"==e.type),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf","large"==e.type),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf","overview"==e.type),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("ngIf",e.toDisplay)}}let f=(()=>{class e{constructor(e){this.roleService=e,this.showActualAmount=!1,this.currency=[]}set currencyList(e){if(this.currency=e,e){const t=s.findIndex(e,e=>e.currency_code==this.currency_code);-1!=t&&(this.index=t,this.currency[this.index].value=Math.trunc(this.currency[this.index].value))}}ngOnInit(){null==this.toDisplay&&(this.toDisplay=!0),this.tenant_info=this.roleService.currency_info,this.tenantName=this.tenant_info.tenant_name,this.currency_code=this.defaultCurrencyCode?this.defaultCurrencyCode:this.tenant_info.default_currency?this.tenant_info.default_currency:"INR",this.setCurrency(),this.isConvertValue=null==this.tenant_info.is_to_convert_currency_value||this.tenant_info.is_to_convert_currency_value}setCurrency(){if(this.currency){const e=s.findIndex(this.currency,e=>e.currency_code==this.currency_code);-1!=e&&(this.index=e,this.currency[this.index].value=Math.trunc(this.currency[this.index].value))}}change(){void 0!==event.stopPropagation?event.stopPropagation():void 0!==event.cancelBubble&&(event.cancelBubble=!0),this.currency.length>1&&(this.index=++this.index%this.currency.length),this.currency[this.index].value=Math.trunc(this.currency[this.index].value)}convert(e,t){return e?(this.showActualAmount?e=new Intl.NumberFormat("INR"==t?"en-IN":"en-US",{}).format(e)+" "+t:1==this.isConvertValue?e="INR"==t?(e/1e7).toFixed(2)+" Cr":(e/1e6).toFixed(2)+" M":0!=this.isConvertValue||s.contains(["big","small"],this.type)?0==this.isConvertValue&&s.contains(["big","small"],this.type)&&(e="INR"==t?"big"!=this.type?"INR "+new Intl.NumberFormat("en-IN",{}).format(e):new Intl.NumberFormat("en-IN",{}).format(e):"big"!=this.type?t+" "+new Intl.NumberFormat("en-US",{}).format(e):new Intl.NumberFormat("en-US",{}).format(e)):e="INR"==t?(e/1e7).toFixed(2)+" Cr":(e/1e6).toFixed(2)+" M",e):"-"}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275directiveInject"](r.a))},e.\u0275cmp=a["\u0275\u0275defineComponent"]({type:e,selectors:[["app-currency"]],inputs:{type:"type",label:"label",showActualAmount:"showActualAmount",acl:"acl",toDisplay:"toDisplay",defaultCurrencyCode:"defaultCurrencyCode",currencyList:"currencyList"},decls:1,vars:1,consts:[["class","d-flex currency-wrapper",3,"click",4,"ngIf"],[1,"d-flex","currency-wrapper",3,"click"],["class","d-flex",3,"matTooltip",4,"ngIf"],["class","data-label",3,"matTooltip",4,"ngIf"],["class","data-medium d-flex",3,"matTooltip",4,"ngIf"],["class","data-large d-flex",3,"matTooltip",4,"ngIf"],["class","justify-content-center",3,"matTooltip",4,"ngIf"],[1,"d-flex","align-items-center"],["class","change-icon pl-2","matTooltip","Next currency",4,"ngIf"],[1,"d-flex",3,"matTooltip"],[1,"pr-3","my-auto","currency-code","justify-content-center"],[1,"m-0","header"],[1,"m-0","data-label","pt-1"],[1,"data-label",3,"matTooltip"],[1,"data-medium","d-flex",3,"matTooltip"],[1,"justify-content-center"],[1,"data-large","d-flex",3,"matTooltip"],[1,"justify-content-center",3,"matTooltip"],[1,"data-overview"],["matTooltip","Next currency",1,"change-icon","pl-2"]],template:function(e,t){1&e&&a["\u0275\u0275template"](0,m,8,6,"div",0),2&e&&a["\u0275\u0275property"]("ngIf",t.currency)},directives:[n.NgIf,o.a,l.a],styles:[".header[_ngcontent-%COMP%]{color:#5f5f5f;font-size:11px!important}.data-label[_ngcontent-%COMP%]{font-size:13px!important}.change-icon[_ngcontent-%COMP%]{font-size:18px;width:auto;height:auto;visibility:hidden;cursor:default}.currency-code[_ngcontent-%COMP%]{padding-right:8px;font-size:14px!important}.currency-wrapper[_ngcontent-%COMP%]{white-space:nowrap!important;height:100%}.currency-wrapper[_ngcontent-%COMP%]:hover   .change-icon[_ngcontent-%COMP%]{visibility:visible}.data-medium[_ngcontent-%COMP%]{color:#252422!important;font-size:15px!important}.data-large[_ngcontent-%COMP%], .data-medium[_ngcontent-%COMP%]{text-align:center!important;font-weight:440!important;padding-right:4px!important}.data-large[_ngcontent-%COMP%]{font-size:large!important}.data-overview[_ngcontent-%COMP%]{color:#252422!important;font-size:15px!important;padding-left:24px!important;font-weight:500!important;padding-right:4px!important;text-align:center!important;margin:auto!important}"]}),e})()},HmYF:function(e,t,i){"use strict";i.d(t,"a",(function(){return d}));var s=i("mrSG"),a=i("Iab2"),r=i("EUZL"),n=i("wd/R"),o=i("xG9w"),l=i("fXoL");let d=(()=>{class e{constructor(){this.formatColumn=(e,t,i)=>{const s=r.utils.decode_range(e["!ref"]);for(let a=s.s.r+1;a<=s.e.r;++a){const s=r.utils.encode_cell({r:a,c:t});e[s]&&e[s].v&&(e[s].t="d",e[s].z=i)}}}exportAsExcelFile(e,t,i,s,a){console.log("Excel to JSON Service",e);const n=r.utils.json_to_sheet(e);if(a&&a.length){const e=r.utils.sheet_to_json(n,{header:1}).shift();for(const t of a){const i=e.indexOf(t.fieldKey);this.formatColumn(n,i,t.fieldFormat)}}null==i&&(i=[]),null==s&&(s="DD-MM-YYYY"),this.formatExcelDateData(n,i,s);const o=r.write({Sheets:{data:n},SheetNames:["data"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(o,t)}formatExcelDateData(e,t,i){for(let r of Object.keys(e))if(null!=e[r]&&null!=e[r].t&&null!=e[r].v&&n(e[r].v,i,!0).isValid()){let s=r.replace(/[0-9]/g,"")+"1";0==o.where(t,{value:e[s].v}).length&&null!=e[s]&&null!=e[s].t&&t.push({value:e[s].v,format:i})}let s=[],a=1;for(let r of t)for(let t of Object.keys(e)){let i=parseInt(t.replace(/[^0-9]/g,""));i>a&&(a=i),null!=e[t]&&null!=e[t].v&&e[t].v==r.value&&s.push({value:t.replace(/[0-9]/g,""),format:r.format})}for(let r of s)for(let t=2;t<=a;t++)null!=e[r.value+""+t]&&null!=e[r.value+""+t].t&&(e[r.value+""+t].t="d",null!=e[r.value+""+t].v&&"Invalid date"!=e[r.value+""+t].v?e[r.value+""+t].v=n(e[r.value+""+t].v,r.format).format("YYYY/MM/DD"):(console.log(e[r.value+""+t].t),e[r.value+""+t].v="",e[r.value+""+t].t="s"))}saveAsExcelFile(e,t){const i=new Blob([e],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8"});a.saveAs(i,t.concat(".xlsx"))}exportAsExcelFileWFH(e,t,i){const s=r.utils.json_to_sheet(e),a=r.utils.json_to_sheet(t),n=r.write({Sheets:{All_Approvals:s,Pending_Approvals:a},SheetNames:["All_Approvals","Pending_Approvals"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(n,i)}exportAsExcelFileForPayroll(e,t,i,s,a,n){const o=r.utils.json_to_sheet(e),l=r.utils.json_to_sheet(t),d=r.utils.json_to_sheet(i),c=r.utils.json_to_sheet(s),u=r.utils.json_to_sheet(a),h=r.write({Sheets:{Regular_Report:o,Intern_Report:l,Contract_Report:d,Perdiem_Report:c,RP_Report:u},SheetNames:["Regular_Report","Intern_Report","Contract_Report","Perdiem_Report","RP_Report"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(h,n)}exportAsCsvFileWithSheetName(e,t){return Object(s.c)(this,void 0,void 0,(function*(){let i=r.utils.book_new();for(let t of e){let e=r.utils.json_to_sheet(t.data);r.utils.book_append_sheet(i,e,t.sheetName)}let s=r.write(i,{bookType:"xlsx",type:"binary"});yield this.saveAsCsvFile(s,t)}))}saveAsCsvFile(e,t){return Object(s.c)(this,void 0,void 0,(function*(){const i=new Blob([yield this.s2ab(e)],{type:"application/octet-stream"});a.saveAs(i,t.concat(".csv"))}))}s2ab(e){return Object(s.c)(this,void 0,void 0,(function*(){for(var t=new ArrayBuffer(e.length),i=new Uint8Array(t),s=0;s<e.length;s++)i[s]=255&e.charCodeAt(s);return t}))}exportAsExcelFileWithCellMerge(e,t,i){const s=r.utils.json_to_sheet(e);s["!merges"]=i;const a=r.write({Sheets:{data:s},SheetNames:["data"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(a,t)}exportJsonToExcelWithMultipleSheets(e,t){return Object(s.c)(this,void 0,void 0,(function*(){let i=r.utils.book_new();for(let t of e){let e=r.utils.json_to_sheet(t.data);r.utils.book_append_sheet(i,e,t.sheetName)}let s=r.write(i,{bookType:"xlsx",type:"array"});this.saveAsExcelFile(s,t)}))}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275prov=l["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},NJ67:function(e,t,i){"use strict";i.d(t,"a",(function(){return s}));class s{constructor(){this.disabled=!1}onChange(e){}onTouched(e){}writeValue(e){this.value=e}registerOnChange(e){this.onChange=e}registerOnTouched(e){this.onTouched=e}setDisabledState(e){this.disabled=e}}},hJL4:function(e,t,i){"use strict";i.d(t,"a",(function(){return u}));var s=i("mrSG"),a=i("XNiG"),r=i("xG9w"),n=i("fXoL"),o=i("tk/3"),l=i("LcQX"),d=i("XXEo"),c=i("flaP");let u=(()=>{class e{constructor(e,t,i,s){this.http=e,this.UtilityService=t,this.loginService=i,this.roleService=s,this.msg=new a.b,this.taskStatusColor=[],this.approveRequest=(e,t)=>this.http.post("/api/isa/request/approveResourceRequest",{requestDetails:e,approverOid:t}),this.rejectRequest=(e,t)=>this.http.post("/api/isa/request/rejectResourceRequest",{requestDetails:e,approverOid:t}),this.getTaskStatusColor()}getAllRequestsOfUser(e,t,i,s,a,r,n){let o=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getAllRequestsOfUser",{oid:e,statusId:t,statusCode:i,objectIds:s,skip:a,limit:r,filterConfig:n,orgIds:o})}getAllRoleAccess(){return r.where(this.roleService.roles,{application_id:139})}getRequestsBasedOnStatus(e,t,i,s,a,r,n){let o=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getRequestsBasedOnStatus",{oid:e,statusId:t,statusCode:i,objectIds:s,skip:a,limit:r,filterConfig:n,orgIds:o})}getRequestsForAwaitingApproval(e,t,i,s){return this.http.post("/api/isa/request/getRequestsForAwaitingApproval",{oid:e,skip:t,limit:i,filterConfig:s})}getObjectIdsBasedOnOid(e){return this.http.post("/api/isa/request/getObjectIdsBasedOnOid",{oid:e})}getStatusCountBasedOnOid(e,t,i,s){let a=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getStatusCountBasedOnOid",{oid:e,objectIds:t,dataTypeArray:i,filterConfig:s,orgIds:a})}getHeaderCount(e){return this.http.post("/api/isa/request/getHeaderCount",{oid:e})}getRequestBasedOnStatus(e,t){return this.http.post("/api/isa/request/getRequestBasedOnStatus",{oid:e,status:t})}getDataTypeArray(){return this.http.post("/api/isa/request/getISAStatusMasterDataUDRF",{})}sendMsg(e){this.msg.next(e)}getMsg(){return this.msg.asObservable()}getTaskStatusColor(){return new Promise((e,t)=>{this.http.post("/api/isa/request/getTaskStatusNameColor",{}).subscribe(t=>(this.taskStatusColor=t,e(t)),e=>{})})}getAllRequestsForRmg(e){return this.http.post("/api/isa/request/getAllRequestsForRmg",{filterConfig:e})}getTotalForRmg(e){return new Promise((t,i)=>{this.http.post("/api/isa/request/getTotalForRmg",{filterConfig:e}).subscribe(e=>t(e),e=>{})})}getDefaultISAStatusToDisplay(){return this.http.post("/api/isa/request/getISADefaultStatusToDisplay",{})}getCTCbreakUpForSkillSet(e){return new Promise((t,i)=>{this.http.post("/api/isa/request/getCTCbreakUpForSkillSet",e).subscribe(e=>t(e.data),e=>{i(e)})})}getCurrentUserRole(){return this.roleService&&this.roleService.roles&&this.roleService.roles.length>0?this.roleService.roles[0].role_id:null}getTemplateForUser(e){return this.http.post("/api/isa/request/getVisibleFormFieldsForUser",e)}saveAndApproveRequest(e){return this.http.post("/api/isa/request/updBudgetInfoByRequestId",e)}getVisibilityMatrix(e){return this.http.post("/api/isa/request/getAuthorzedFieldsByRoleId",e)}getISAAttachmentFromS3(e){return this.http.post("/api/isa/attachment/getISAAttachmentFromS3",{key:e})}getSkillType(){return this.http.post("/api/isa/request/getSkillType",{})}getSkillLevel(){return this.http.post("/api/isa/request/getSkillLevel",{})}getSkillExperience(){return this.http.post("/api/isa/request/getSkillExperience",{})}getSkillName(e){return this.http.post("/api/isa/request/getSkillName",{skill_type_id:e})}getIsaActivityReportDownload(){return this.http.post("/api/isa/request/getIsaActivityReportDownload",{})}getISASLAreport(e){return this.http.post("/api/isa/request/getISASLAreport",{filterConfig:e})}getISACustomReport(e){return this.http.post("/api/isa/request/getISACustomReport",{filterConfig:e})}getPositionType(){return this.http.post("/api/employee360/masterData/getPositionType",{})}calculateNetCostBasedOnPosition(e,t,i,a,n,o,l){return Object(s.c)(this,void 0,void 0,(function*(){let s;s=o&&o.length>1&&(yield this.getManpowerCostByOId(o,i,n,2))||(yield this.getManpowerCostBasedOnPosition(e,t,i,n,l));let d=yield this.getNonManpowerCost(t,i,a,n,2),c=yield this.getAllocatedCost(),u=0;u=(s?s.cost:0)+d.length>0?r.reduce(r.pluck(d,"cost"),(e,t)=>e+t,0):0;let h=c.length>0?r.reduce(r.pluck(c,"percentage"),(e,t)=>e+t,0):0;return{cost:u,currency:s&&s.currency_code?s.currency_code:"",manpowerCost:s,nonManpowerCost:d,allocatedCost:c,allocatedCostValue:u*(h/100)}}))}getManpowerCostBasedOnPosition(e,t,i,s,a){return new Promise((r,n)=>{this.http.post("/api/isa/configuration/getManpowerCostBasedOnPosition",{skillRoleId:e,nationalityId:t,locationGroupId:i,unit:s,position:a}).subscribe(e=>r(e),e=>(console.log(e),n(e)))})}getNonManpowerCost(e,t,i,s,a){return new Promise((r,n)=>{this.http.post("/api/project/getNonManpowerCost",{nationalityId:e,locationGroupId:t,locationId:i,unit:s,currency_id:a}).subscribe(e=>r(e),e=>(console.log(e),n(e)))})}getAllocatedCost(){return new Promise((e,t)=>{this.http.post("/api/project/getAllocatedCost","").subscribe(t=>e(t),e=>(console.log(e),t(e)))})}getManpowerCostByOId(e,t,i,s){return new Promise((a,r)=>{this.http.post("/api/project/getEmployeeSalary",{oid:e,location_group_id:t,unit_id:i,currency_id:s}).subscribe(e=>a(e),e=>(console.log(e),r(e)))})}getSkillNameMaster(){return this.http.post("/api/isa/configuration/getSkillName",{})}getVendorOid(e,t){return this.http.post("/api/isa/request/getVendorOid",{vendorNames:e,docID:t})}getVendorList(e,t){return this.http.post("/api/isa/request/getVendorList",{documentID:e,limitval:t})}removeVendor(e,t){return this.http.post("/api/isa/request/removeVendors",{vendorOid:e,documentID:t})}checkVendorOrNot(){return this.http.post("/api/isa/request/checkVendorOrNot",{})}}return e.\u0275fac=function(t){return new(t||e)(n["\u0275\u0275inject"](o.c),n["\u0275\u0275inject"](l.a),n["\u0275\u0275inject"](d.a),n["\u0275\u0275inject"](c.a))},e.\u0275prov=n["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},ucYs:function(e,t,i){"use strict";i.d(t,"a",(function(){return l}));var s=i("mrSG"),a=i("xG9w"),r=i("fXoL"),n=i("tk/3"),o=i("BVzC");let l=(()=>{class e{constructor(e,t){this.http=e,this.errorService=t,this.workflowStatusList=[],this.getWorkflowStatusList()}getWorkflowStatusList(){this.http.post("/api/wfPrimary/getWorkflowStatusList","").subscribe(e=>{this.workflowStatusList=e.data},e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server While Getting Workflow Status List",e&&e.params?e.params:e&&e.error?e.error.params:{})})}getWorkflowProperties(e){return new Promise((t,i)=>{this.http.post("/api/wfPrimary/getWorkflowProperties",{applicationId:e}).subscribe(e=>t(e),e=>i(e))})}getWorkflowPropertiesByWorkflowId(e){return new Promise((t,i)=>{this.http.post("/api/wfPrimary/getWorkflowPropertiesByWorkflowId",{workflowId:e}).subscribe(e=>t(e),e=>i(e))})}getApproversHierarchy(e){return new Promise((t,i)=>{this.http.post("/api/wfPrimary/getApproversHierarchy",e).subscribe(e=>t(e),e=>i(e))})}createWorkflowItems(e){return new Promise((t,i)=>{this.http.post("/api/wfPrimary/createWorkflowItems",e).subscribe(e=>t(e),e=>i(e))})}getWorkflowDetails(e){return new Promise((t,i)=>{this.http.post("/api/wfPrimary/getWorkflowDetails",{workflowId:e}).subscribe(e=>t(e),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while getting workflow details",e&&e.params?e.params:e&&e.error?e.error.params:{})})})}formatApproversHierarchy(e,t){return Object(s.c)(this,void 0,void 0,(function*(){0==e.length&&t.cc0&&t.cc0.appr0&&t.cc0.appr0.length>0&&e.push([{approvalType:"I",purposeOfInsertingIntoArray:"Just to ensure that 'I' type data is used, if it exists"}]);for(let i=0;i<e.length;i++){let s=[],r=a.keys(t["cc"+i]);for(let a=0;a<r.length;a++)for(let n=0;n<t["cc"+i][r[a]].length;n++){let o={name:t["cc"+i][r[a]][n].DELEGATE_NAME,oid:t["cc"+i][r[a]][n].DELEGATE_OID,level:a+1,designation:t["cc"+i][r[a]][n].DELEGATE_DESIGNATION_NAME,is_delegated:t["cc"+i][r[a]][n].IS_DELEGATED,role:t["cc"+i][r[a]][n].DELEGATE_ROLE_NAME};if(1==t["cc"+i][r[a]][n].IS_DELEGATED&&(o.delegated_by={name:t["cc"+i][r[a]][n].APPROVER_NAME,oid:t["cc"+i][r[a]][n].APPROVER_OID,level:a+1,designation:t["cc"+i][r[a]][n].APPROVER_DESIGNATION_NAME}),s.push(o),i==e.length-1&&a==r.length-1&&n==t["cc"+i][r[a]].length-1)return s}}}))}storeComments(e,t,i){return new Promise((s,a)=>{this.http.post("/api/wfPrimary/updateComments",{workflowHeaderId:e,newComments:t,commentor:i}).subscribe(e=>s(e),e=>(this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server Error while Updating Workflow Comments",e&&e.params?e.params:e&&e.error?e.error.params:{}),a(e)))})}updateWorkflowItems(e){return new Promise((t,i)=>{this.http.post("/api/wfPrimary/updateWorkflowItems",e).subscribe(e=>t(e),e=>(console.log(e),i(e)))})}formatApproversHierarchyForOpportunityApprovalActivity(e){return Object(s.c)(this,void 0,void 0,(function*(){for(let t=0;t<1;t++){let i=[],s=a.keys(e["cc"+t]);for(let a=0;a<s.length;a++)for(let r=0;r<e["cc"+t][s[a]].length;r++){let n={name:e["cc"+t][s[a]][r].DELEGATE_NAME,oid:e["cc"+t][s[a]][r].DELEGATE_OID,level:e["cc"+t][s[a]][r].APPROVAL_ORDER,designation:e["cc"+t][s[a]][r].DELEGATE_DESIGNATION_NAME,is_delegated:e["cc"+t][s[a]][r].IS_DELEGATED};if(1==e["cc"+t][s[a]][r].IS_DELEGATED&&(n.delegated_by={name:e["cc"+t][s[a]][r].APPROVER_NAME,oid:e["cc"+t][s[a]][r].APPROVER_OID,level:e["cc"+t][s[a]][r].APPROVAL_ORDER,designation:e["cc"+t][s[a]][r].APPROVER_DESIGNATION_NAME}),i.push(n),a==s.length-1&&r==e["cc"+t][s[a]].length-1)return i}}}))}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275inject"](n.c),r["\u0275\u0275inject"](o.a))},e.\u0275prov=r["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},v2fc:function(e,t,i){"use strict";i.d(t,"a",(function(){return h}));var s=i("xG9w"),a=i("wd/R"),r=i("2Vo4"),n=i("XNiG"),o=i("fXoL"),l=i("tk/3"),d=i("LcQX"),c=i("flaP"),u=i("XXEo");let h=(()=>{class e{constructor(e,t,i,s){this.$http=e,this._util=t,this._roles=i,this._auth=s,this.currentUser=this._auth.getProfile().profile,this.token=this._auth.getJwtToken(),this.isaBudgettedAttachmentSubject=new r.a({}),this.getisaBudgettedAttachmentObservable=this.isaBudgettedAttachmentSubject.asObservable(),this.activitySubject=new n.b,this.getActivityObservable=this.activitySubject.asObservable(),this.priority_status_colors=[{statusName:"Low",statusColor:"#BADC58"},{statusName:"Medium",statusColor:"#91AECB"},{statusName:"High",statusColor:"#FFA502"},{statusName:"Very High",statusColor:"#cf0001"}],this.getVendorDetailsData=()=>this.$http.post("/api/isa/request/getVendorForList",{})}showMessage(e){this._util.showToastMessage(e)}showErrorMessage(e){this._util.showErrorMessage(e,"KEBS")}getToken(){return this._auth.getToken()}getCurrentUserRole(){return this._roles&&this._roles.roles&&this._roles.roles.length>0?this._roles.roles[0].role_id:null}setActivityObservable(e){this.activitySubject.next(e)}getStatusObjectEntries(){let e=s.where(this._roles.roles,{application_id:139,object_id:68});return e.length>0?JSON.parse(e[0].object_entries):null}resolveActivityTemplate(e,t,i,r,n,o){let l=s.where(e,{activity_name:("Status"==t?"status":"Priority"==t?"priority":"RMG Owner"==t?"rmgOwner":"TAG Owner"==t?"tagOwner":null)||t});return l.length>0?{activity_type_id:l[0]._id,activity_description:this.getActivityDescription(l[0],i,r,n,o),activity_by:this.currentUser.oid,activity_created_date:a()}:{}}getActivityDescription(e,t,i,s,a){let r="";return r=e.activity_template[t],i&&(r=r.replace("from_value",i)),s&&(r=r.replace("to_value",s)),a&&(r=r.replace("object_name",a)),r}getRequestById(e){return this.$http.post("/api/isa/request/getRequestById",{requestId:e})}getActivityTypes(){return this.$http.post("/api/isa/request/getActivityTypeMasterData",{})}getStatusMasterData(){return this.$http.post("/api/isa/request/getISAStatusMasterData",{statusValues:this.getStatusObjectEntries()})}updateRequestStatus(e,t,i,s){return this.$http.post("/api/isa/request/statusChange",{requestId:e,statusRefId:t,currentStatusId:i,activityTemplate:s})}updateKeyValueInResourceRequest(e,t,i){return this.$http.post("/api/isa/request/updateKeyValueInResourceRequest",{requestId:e,activityTemplate:t,modifyKeyValue:i})}changeTagOrRmgOwner(e,t,i,s){return this.$http.post("/api/isa/request/changeTagOrRmgOwner",{requestId:e,activityTemplate:t,type:i,changedOid:s})}getActivity(e){return this.$http.post("/api/isa/request/activityRetreivalBasedOnRequestIdOrActivityId",{activityId:e})}insertISAActivity(e,t){return this.$http.post("/api/isa/request/insertISAActivity",{requestId:e,activityTemplate:t})}editISAActivity(e,t){return this.$http.post("/api/isa/request/editISAActivity",{activity_id:e,activity_details:t})}getISAAttachmentById(e){return this.$http.post("/api/isa/attachment/getISAAttachmentById",{request_id:e})}getISAAttachmentFromS3(e){return this.$http.post("/api/isa/attachment/getISAAttachmentFromS3",{key:e})}deleteISAAttachment(e,t,i){return this.$http.post("/api/isa/attachment/deleteISAAttachment",{requestId:e,file:t,activityTemplate:i})}updateISAAttachment(e,t,i,s){return this.$http.post("/api/isa/attachment/updateISAAttachment",{requestId:e,file:i,attachmentId:t,activityTemplate:s})}getApproverStatuses(e,t){return this.$http.post("/api/isa/request/getApproverStatusForRequest",{workflowHeaderId:e,approvers:t})}createTask(e,t,i){return this.$http.post("/api/isa/tasks/createTasks",{request_id:e,tasks:t,activityTemplate:i})}getRequestTasks(e){return this.$http.post("/api/isa/tasks/getTaskById",{request_id:e})}addTask(e,t,i,s){return this.$http.post("/api/isa/tasks/addTasks",{task_id:e,request_id:t,tasks:i,activityTemplate:s})}updateTaskObject(e,t,i,s,a){return this.$http.post("/api/isa/tasks/updateTaskObject",{task_id:e,sub_task_id:t,key:i,object:s,activityTemplate:a})}getTaskTemplate(){return this.$http.post("/api/isa/tasks/getTaskTemplates",{})}createTaskFromTemplate(e,t,i,s,a,r,n,o){return this.$http.post("/api/isa/tasks/assignTaskFromTemplate",{task_template_id:e,request_id:t,task_id:i,rmg_owner:s,tag_owner:a,activityTemplate:r,taskTypesList:n,requestSkillId:o})}updateTaskDataFromAttachment(e){return this.$http.post("/api/isa/tasks/updateTaskNameAndOwner",e)}updateExtTaskExtAtchId(e){return this.$http.post("/api/isa/tasks/updateExtTaskExtAtchId",e)}getTaskStatusList(){return this.$http.post("/api/isa/tasks/getTaskStatus",{})}getTaskTypeList(){return this.$http.post("/api/isa/tasks/getTaskTypeMasterData",{})}updateTypeListInReq(e){return this.$http.post("/api/isa/request/insertTaskTypeOwnerDetail",e)}updateTypeAssigned(e){return this.$http.post("/api/isa/request/updTaskTypeOwnerDetail",e)}deleteTask(e,t,i,s,a){return this.$http.post("/api/isa/tasks/changeTaskFlag",{request_id:e,task_id:t,sub_task_id:i,is_active:s,activityTemplate:a})}updateTaskAssigned(e,t,i,s,a){return this.$http.post("/api/isa/tasks/updateAssignedToTask",{request_id:e,task_id:t,sub_task_id:i,assigned_to:s,activityTemplate:a})}updateTaskData(e,t,i,s,a,r){return this.$http.post("/api/isa/tasks/updateTaskData",{request_id:e,task_id:t,sub_task_id:i,key:s,value:a,activityTemplate:r})}getRequestCTAs(e){return this.$http.post("/api/isa/configuration/getRequestCTAs",{requestId:e})}getISAWfConfig(){return this.$http.post("/api/isa/configuration/getISAWfConfig",{})}getTodoDetails(e){return this.$http.post("/api/isa/todo/getTodo",{todoId:e})}createTodo(e,t,i){return this.$http.post("/api/isa/todo/createToDo",{request_id:e,to_do_list:t,activityTemplate:i})}insertTodo(e,t,i,s){return this.$http.post("/api/isa/todo/insertToDo",{request_id:e,to_do_id:t,to_do_list:i,activityTemplate:s})}editTodo(e,t,i,s){return this.$http.post("/api/isa/todo/editToDo",{request_id:e,to_do_id:t,to_do_details:i,activityTemplate:s})}updateResourceInRequest(e,t,i,s,a){return this.$http.post("/api/isa/request/updateResourceInRequest",{requestId:e,activityTemplate:t,wfActivityTemplate:i,statusRefId:s,resourceOid:a})}triggerWfOnSubmission(e,t,i,s,a,r,n,o){return this.$http.post("/api/isa/request/triggerWfOnSubmission",{requestId:e,activityTemplate:t,wfActivityTemplate:i,statusRefId:s,task_id:a,sub_task_id:r,task_status:n,wfConfig:o})}addIsaTaskActualHours(e,t,i,s,a){return this.$http.post("/api/isa/tasks/addIsaTaskActualHours",{task_id:e,task_item:t,actual_hours:i,timesheet_id:s,request_id:a})}deleteIsaTaskActualHours(e,t,i,s,a){return this.$http.post("/api/isa/tasks/deleteIsaTaskActualHours",{task_id:e,task_item:t,actual_hours:i,request_id:s,timesheet_id:a})}getSLAByRequestId(e){return this.$http.post("/api/isa/request/getSLAByRequestId",{requestId:e})}getRequestBasedOnSearch(e){return this.$http.post("/api/isa/request/getRequestBasedOnSearch",{searchParameter:e})}moveTaskToRequest(e,t,i,s,a,r){return this.$http.post("/api/isa/tasks/moveTaskToRequest",{fromRequestId:e,fromTaskId:t,mainTaskId:i,toRequestId:s,toTaskId:a,activityTemplate:r})}removeResourceFromRequest(e,t,i,s,a){return this.$http.post("/api/isa/request/removeResourceFromRequest",{requestId:e,statusId:t,statusRefId:i,resActivityTemplate:s,statusActivityTemplate:a})}checkIfUserHasAccessToRequest(e){return this.$http.post("/api/isa/request/checkIfUserHasAccessToRequest",{requestId:e,oid:this.currentUser.oid})}updateEmployeeStatus(e){return this.$http.post("/api/obPrimary/updateEmployeeStatus",e)}checkIfEmailHasRequestId(e){return this.$http.post("/api/obPrimary/checkIfEmailHasRequestId",{apiParams:{emailId:e}})}createISAAttachment(e){return this.$http.post("/api/isa/attachment/createIsaAttachment",e)}getSourceList(){return this.$http.post("/api/isa/attachment/getAllSource",{})}saveEmailtoTask(e){return this.$http.post("/api/isa/tasks/saveEmailtoTask",e)}setISABudgetedAttachmentActivityObservable(e){this.isaBudgettedAttachmentSubject.next(e)}getTemplateForUser(e){return this.$http.post("/api/isa/request/getVisibleFormFieldsForUser",e)}budgetApprovalCheck(e){return this.$http.post("/api/isa/request/ctcRangeCheck",e)}checkElligibleToSubmit(e){return new Promise((t,i)=>{this.$http.post("/api/isa/request/budgetApprovalCheck",e).subscribe(e=>t(e),e=>i(e))})}offerApprovalCheck(e){return this.$http.post("/api/isa/request/offerApprovalCheck",e)}triggerWfOnBudgetApproval(e,t,i,s,a){return this.$http.post("/api/isa/request/triggerWfOnBudgetApproval",{requestId:e,activityTemplate:t,wfActivityTemplate:i,statusRefId:s,wfConfig:a})}getMasterDataByMasterDataName(e){return this.$http.post("/api/isa/request/getMasterDataByMasterDataName",e)}getVisibilityMatrix(e){return this.$http.post("/api/isa/request/getAuthorzedFieldsByRoleId",e)}getAllRoleAccess(){return s.where(this._roles.roles,{application_id:139})}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275inject"](l.c),o["\u0275\u0275inject"](d.a),o["\u0275\u0275inject"](c.a),o["\u0275\u0275inject"](u.a))},e.\u0275prov=o["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},w76M:function(e,t,i){"use strict";i.d(t,"a",(function(){return d})),i.d(t,"b",(function(){return c}));var s=i("jhN1"),a=i("fXoL"),r=i("oHs6"),n=i("PVOt"),o=i("6t9p");const l=["*"];let d=(()=>{let e=class extends n.b{constructor(e,t,i,s,a,r,n,o){super(e,t,i,s,n,o),this._watcherHelper=s,this._idh=a,this._createEventEmitters([{subscribe:"contentReady",emit:"onContentReady"},{subscribe:"disposing",emit:"onDisposing"},{subscribe:"hidden",emit:"onHidden"},{subscribe:"hiding",emit:"onHiding"},{subscribe:"initialized",emit:"onInitialized"},{subscribe:"optionChanged",emit:"onOptionChanged"},{subscribe:"resize",emit:"onResize"},{subscribe:"resizeEnd",emit:"onResizeEnd"},{subscribe:"resizeStart",emit:"onResizeStart"},{subscribe:"showing",emit:"onShowing"},{subscribe:"shown",emit:"onShown"},{subscribe:"titleRendered",emit:"onTitleRendered"},{emit:"accessKeyChange"},{emit:"animationChange"},{emit:"closeOnOutsideClickChange"},{emit:"containerChange"},{emit:"contentTemplateChange"},{emit:"deferRenderingChange"},{emit:"disabledChange"},{emit:"dragEnabledChange"},{emit:"elementAttrChange"},{emit:"focusStateEnabledChange"},{emit:"fullScreenChange"},{emit:"heightChange"},{emit:"hintChange"},{emit:"hoverStateEnabledChange"},{emit:"maxHeightChange"},{emit:"maxWidthChange"},{emit:"minHeightChange"},{emit:"minWidthChange"},{emit:"positionChange"},{emit:"resizeEnabledChange"},{emit:"rtlEnabledChange"},{emit:"shadingChange"},{emit:"shadingColorChange"},{emit:"showCloseButtonChange"},{emit:"showTitleChange"},{emit:"tabIndexChange"},{emit:"titleChange"},{emit:"titleTemplateChange"},{emit:"toolbarItemsChange"},{emit:"visibleChange"},{emit:"widthChange"}]),this._idh.setHost(this),r.setHost(this)}get accessKey(){return this._getOption("accessKey")}set accessKey(e){this._setOption("accessKey",e)}get animation(){return this._getOption("animation")}set animation(e){this._setOption("animation",e)}get closeOnOutsideClick(){return this._getOption("closeOnOutsideClick")}set closeOnOutsideClick(e){this._setOption("closeOnOutsideClick",e)}get container(){return this._getOption("container")}set container(e){this._setOption("container",e)}get contentTemplate(){return this._getOption("contentTemplate")}set contentTemplate(e){this._setOption("contentTemplate",e)}get deferRendering(){return this._getOption("deferRendering")}set deferRendering(e){this._setOption("deferRendering",e)}get disabled(){return this._getOption("disabled")}set disabled(e){this._setOption("disabled",e)}get dragEnabled(){return this._getOption("dragEnabled")}set dragEnabled(e){this._setOption("dragEnabled",e)}get elementAttr(){return this._getOption("elementAttr")}set elementAttr(e){this._setOption("elementAttr",e)}get focusStateEnabled(){return this._getOption("focusStateEnabled")}set focusStateEnabled(e){this._setOption("focusStateEnabled",e)}get fullScreen(){return this._getOption("fullScreen")}set fullScreen(e){this._setOption("fullScreen",e)}get height(){return this._getOption("height")}set height(e){this._setOption("height",e)}get hint(){return this._getOption("hint")}set hint(e){this._setOption("hint",e)}get hoverStateEnabled(){return this._getOption("hoverStateEnabled")}set hoverStateEnabled(e){this._setOption("hoverStateEnabled",e)}get maxHeight(){return this._getOption("maxHeight")}set maxHeight(e){this._setOption("maxHeight",e)}get maxWidth(){return this._getOption("maxWidth")}set maxWidth(e){this._setOption("maxWidth",e)}get minHeight(){return this._getOption("minHeight")}set minHeight(e){this._setOption("minHeight",e)}get minWidth(){return this._getOption("minWidth")}set minWidth(e){this._setOption("minWidth",e)}get position(){return this._getOption("position")}set position(e){this._setOption("position",e)}get resizeEnabled(){return this._getOption("resizeEnabled")}set resizeEnabled(e){this._setOption("resizeEnabled",e)}get rtlEnabled(){return this._getOption("rtlEnabled")}set rtlEnabled(e){this._setOption("rtlEnabled",e)}get shading(){return this._getOption("shading")}set shading(e){this._setOption("shading",e)}get shadingColor(){return this._getOption("shadingColor")}set shadingColor(e){this._setOption("shadingColor",e)}get showCloseButton(){return this._getOption("showCloseButton")}set showCloseButton(e){this._setOption("showCloseButton",e)}get showTitle(){return this._getOption("showTitle")}set showTitle(e){this._setOption("showTitle",e)}get tabIndex(){return this._getOption("tabIndex")}set tabIndex(e){this._setOption("tabIndex",e)}get title(){return this._getOption("title")}set title(e){this._setOption("title",e)}get titleTemplate(){return this._getOption("titleTemplate")}set titleTemplate(e){this._setOption("titleTemplate",e)}get toolbarItems(){return this._getOption("toolbarItems")}set toolbarItems(e){this._setOption("toolbarItems",e)}get visible(){return this._getOption("visible")}set visible(e){this._setOption("visible",e)}get width(){return this._getOption("width")}set width(e){this._setOption("width",e)}get toolbarItemsChildren(){return this._getOption("toolbarItems")}set toolbarItemsChildren(e){this.setChildren("toolbarItems",e)}_createInstance(e,t){return new r.a(e,t)}ngOnDestroy(){this._destroyWidget()}ngOnChanges(e){super.ngOnChanges(e),this.setupChanges("toolbarItems",e)}setupChanges(e,t){e in this._optionsToUpdate||this._idh.setup(e,t)}ngDoCheck(){this._idh.doCheck("toolbarItems"),this._watcherHelper.checkWatchers(),super.ngDoCheck(),super.clearChangedOptions()}_setOption(e,t){let i=this._idh.setupSingle(e,t),s=null!==this._idh.getChanges(e,t);(i||s)&&super._setOption(e,t)}};return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275directiveInject"](a.ElementRef),a["\u0275\u0275directiveInject"](a.NgZone),a["\u0275\u0275directiveInject"](n.e),a["\u0275\u0275directiveInject"](n.j),a["\u0275\u0275directiveInject"](n.g),a["\u0275\u0275directiveInject"](n.i),a["\u0275\u0275directiveInject"](s.h),a["\u0275\u0275directiveInject"](a.PLATFORM_ID))},e.\u0275cmp=a["\u0275\u0275defineComponent"]({type:e,selectors:[["dx-popup"]],contentQueries:function(e,t,i){if(1&e&&a["\u0275\u0275contentQuery"](i,o.L,!1),2&e){let e;a["\u0275\u0275queryRefresh"](e=a["\u0275\u0275loadQuery"]())&&(t.toolbarItemsChildren=e)}},inputs:{accessKey:"accessKey",animation:"animation",closeOnOutsideClick:"closeOnOutsideClick",container:"container",contentTemplate:"contentTemplate",deferRendering:"deferRendering",disabled:"disabled",dragEnabled:"dragEnabled",elementAttr:"elementAttr",focusStateEnabled:"focusStateEnabled",fullScreen:"fullScreen",height:"height",hint:"hint",hoverStateEnabled:"hoverStateEnabled",maxHeight:"maxHeight",maxWidth:"maxWidth",minHeight:"minHeight",minWidth:"minWidth",position:"position",resizeEnabled:"resizeEnabled",rtlEnabled:"rtlEnabled",shading:"shading",shadingColor:"shadingColor",showCloseButton:"showCloseButton",showTitle:"showTitle",tabIndex:"tabIndex",title:"title",titleTemplate:"titleTemplate",toolbarItems:"toolbarItems",visible:"visible",width:"width"},outputs:{onContentReady:"onContentReady",onDisposing:"onDisposing",onHidden:"onHidden",onHiding:"onHiding",onInitialized:"onInitialized",onOptionChanged:"onOptionChanged",onResize:"onResize",onResizeEnd:"onResizeEnd",onResizeStart:"onResizeStart",onShowing:"onShowing",onShown:"onShown",onTitleRendered:"onTitleRendered",accessKeyChange:"accessKeyChange",animationChange:"animationChange",closeOnOutsideClickChange:"closeOnOutsideClickChange",containerChange:"containerChange",contentTemplateChange:"contentTemplateChange",deferRenderingChange:"deferRenderingChange",disabledChange:"disabledChange",dragEnabledChange:"dragEnabledChange",elementAttrChange:"elementAttrChange",focusStateEnabledChange:"focusStateEnabledChange",fullScreenChange:"fullScreenChange",heightChange:"heightChange",hintChange:"hintChange",hoverStateEnabledChange:"hoverStateEnabledChange",maxHeightChange:"maxHeightChange",maxWidthChange:"maxWidthChange",minHeightChange:"minHeightChange",minWidthChange:"minWidthChange",positionChange:"positionChange",resizeEnabledChange:"resizeEnabledChange",rtlEnabledChange:"rtlEnabledChange",shadingChange:"shadingChange",shadingColorChange:"shadingColorChange",showCloseButtonChange:"showCloseButtonChange",showTitleChange:"showTitleChange",tabIndexChange:"tabIndexChange",titleChange:"titleChange",titleTemplateChange:"titleTemplateChange",toolbarItemsChange:"toolbarItemsChange",visibleChange:"visibleChange",widthChange:"widthChange"},features:[a["\u0275\u0275ProvidersFeature"]([n.e,n.j,n.i,n.g]),a["\u0275\u0275InheritDefinitionFeature"],a["\u0275\u0275NgOnChangesFeature"]],ngContentSelectors:l,decls:1,vars:0,template:function(e,t){1&e&&(a["\u0275\u0275projectionDef"](),a["\u0275\u0275projection"](0))},encapsulation:2}),e})(),c=(()=>{let e=class{};return e.\u0275mod=a["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=a["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[o.bb,o.Gc,o.Vd,o.vd,o.hb,o.lb,o.sb,o.id,o.jd,o.M,n.c,n.f,s.b],o.bb,o.Gc,o.Vd,o.vd,o.hb,o.lb,o.sb,o.id,o.jd,o.M,n.f]}),e})()}}]);