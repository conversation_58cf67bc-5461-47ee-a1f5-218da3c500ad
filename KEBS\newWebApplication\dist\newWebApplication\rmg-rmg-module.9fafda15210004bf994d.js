(window.webpackJsonp=window.webpackJsonp||[]).push([[868,861,981],{WPV7:function(e,t,i){"use strict";i.r(t),i.d(t,"RMGLandingModule",(function(){return ee}));var n=i("ofXK"),r=i("wZkO"),s=i("tyNb"),a=i("mrSG"),o=i("XNiG"),d=i("1G5W"),c=i("wd/R"),l=i("fXoL"),h=i("XXEo"),g=i("GnQ3"),m=i("JLuW"),u=i("hJL4"),f=i("tk/3"),p=i("LcQX");let v=(()=>{class e{constructor(e,t,i){this.http=e,this.UtilityService=t,this.loginService=i,this.msg=new o.b}getAssociateDetails(e){return this.http.post("/api/resource/getAssociateDetails",{inputDetails:e})}getAllRequestsForRmg(e){return this.http.post("/api/resource/getAllRequestsForRmg",{inputDetails:e})}}return e.\u0275fac=function(t){return new(t||e)(l["\u0275\u0275inject"](f.c),l["\u0275\u0275inject"](p.a),l["\u0275\u0275inject"](h.a))},e.\u0275prov=l["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var C=i("F97M"),b=i("0IaG"),S=i("xi/V"),O=i("Wk3H");class D{constructor(){this.NextButton="Next",this.PrevButton="Prev",this.TodayButton="Today",this.GotoButton="Go to",this.SectionTitle="Section"}}class y{}class x{constructor(){this.itemMetas=new Array}}class w{constructor(){this.cssTop=0,this.cssLeft=0,this.cssWidth=0}}class P{constructor(){this.headerDetails=new Array}}class _{}var M=i("quSY");let I=(()=>{class e{constructor(){this.item=new o.b,this.itemAdd=new o.b,this.itemId=new o.b,this.sectionAdd=new o.b,this.section=new o.b,this.sectionId=new o.b,this.refreshView=new o.b}itemPush(e){this.itemAdd.next(e)}itemPop(){this.item.next()}itemRemove(e){this.itemId.next(e)}sectionPush(e){this.sectionAdd.next(e)}sectionPop(){this.section.next()}sectionRemove(e){this.sectionId.next(e)}refresh(){this.refreshView.next()}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275prov=l["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var k=i("vxfF"),T=i("dlKe"),B=i("5+WD"),U=i("Qu3c");const F=["periods",""];function E(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"td",15),l["\u0275\u0275text"](1),l["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;l["\u0275\u0275property"]("colSpan",e.colspan)("title",e.tooltip?e.tooltip:""),l["\u0275\u0275advance"](1),l["\u0275\u0275textInterpolate1"](" ",e.name," ")}}function R(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"tr",13),l["\u0275\u0275template"](1,E,2,3,"td",14),l["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,i=l["\u0275\u0275nextContext"](2);l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngForOf",e.headerDetails)("ngForTrackBy",i.trackByFn)}}function A(e,t){1&e&&l["\u0275\u0275element"](0,"td")}function N(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"tr",16),l["\u0275\u0275template"](1,A,1,0,"td",17),l["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,i=l["\u0275\u0275nextContext"](2);l["\u0275\u0275property"]("ngClass",1==e.expanded&&"l1"==e.hierarchy?"table-item-height":1==e.expanded&&"l2"==e.hierarchy?"table-item-height-l2":"table-item-no-height"),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngForOf",i.header[i.header.length-1].headerDetails)("ngForTrackBy",i.trackByFn)}}function H(e,t){if(1&e&&l["\u0275\u0275element"](0,"div",27),2&e){const e=l["\u0275\u0275nextContext"]().$implicit,t=l["\u0275\u0275nextContext"](4);l["\u0275\u0275styleProp"]("height",t.minRowHeight-6+"px")("left",e.cssLeft+"%")("width","calc("+e.cssWidth+"% - 6px)")}}function z(e,t){1&e&&l["\u0275\u0275element"](0,"div",28)}function V(e,t){1&e&&l["\u0275\u0275element"](0,"div",29)}function j(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",22),l["\u0275\u0275listener"]("click",(function(){l["\u0275\u0275restoreView"](e);const i=t.$implicit,n=l["\u0275\u0275nextContext"](4);return!!n.events.ItemClicked&&n.events.ItemClicked(i.item)}))("contextmenu",(function(i){l["\u0275\u0275restoreView"](e);const n=t.$implicit,r=l["\u0275\u0275nextContext"](4);return!!r.events.ItemContextMenu&&r.events.ItemContextMenu(n.item,i)})),l["\u0275\u0275template"](1,H,1,6,"div",23),l["\u0275\u0275template"](2,z,1,0,"div",24),l["\u0275\u0275elementStart"](3,"div",25),l["\u0275\u0275text"](4),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](5,V,1,0,"div",26),l["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,i=l["\u0275\u0275nextContext"](4);l["\u0275\u0275styleProp"]("height",i.minRowHeight+"px")("top",e.cssTop+"px")("left",e.cssLeft+"%")("width",e.cssWidth+"%"),l["\u0275\u0275property"]("cdkDragData",e.item)("cdkDragDisabled",!i.allowDragging)("ngClass","l2"==e.item.classes?"time-sch-item-l2":"time-sch-item"),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf",e.isStart),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngClass","l2"==e.item.classes?"time-sch-item-content-l2":"time-sch-item-content")("title",e.item.tooltip?e.item.tooltip:"")("matTooltip",e.item.name),l["\u0275\u0275advance"](1),l["\u0275\u0275textInterpolate1"](" ",e.item.name," "),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",e.isEnd)}}function L(e,t){if(1&e&&l["\u0275\u0275element"](0,"div",30),2&e){const e=l["\u0275\u0275nextContext"](4);l["\u0275\u0275styleProp"]("visibility",e.currentTimeVisibility)("left",e.currentTimeIndicatorPosition),l["\u0275\u0275property"]("title",e.currentTimeTitle)}}function W(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div"),l["\u0275\u0275template"](1,j,6,17,"div",20),l["\u0275\u0275element"](2,"div"),l["\u0275\u0275template"](3,L,1,5,"div",21),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"]().$implicit,t=l["\u0275\u0275nextContext"](2);l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngForOf",e.itemMetas),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf",t.showCurrentTime)}}function Y(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",18),l["\u0275\u0275listener"]("cdkDropListDropped",(function(t){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"](2).drop(t)})),l["\u0275\u0275template"](1,W,4,2,"div",19),l["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;l["\u0275\u0275styleProp"]("height",1==e.expanded?e.minRowHeight+"px":""),l["\u0275\u0275property"]("ngClass",1==e.expanded&&"l1"==e.hierarchy?"time-sch-section-container":1==e.expanded&&"l2"==e.hierarchy?"time-sch-section-container-l2":"")("cdkDropListData",e.section),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",e.expanded)}}function G(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",1),l["\u0275\u0275elementStart"](1,"div",2),l["\u0275\u0275elementStart"](2,"div",3),l["\u0275\u0275elementStart"](3,"table",4),l["\u0275\u0275template"](4,R,2,2,"tr",5),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](5,"div",6),l["\u0275\u0275elementStart"](6,"div",7),l["\u0275\u0275listener"]("scrolled",(function(){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"]().itemDataScrollDown()})),l["\u0275\u0275elementStart"](7,"div",8),l["\u0275\u0275elementStart"](8,"table",9),l["\u0275\u0275template"](9,N,2,3,"tr",10),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](10,"div",11),l["\u0275\u0275template"](11,Y,2,5,"div",12),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&e){const e=l["\u0275\u0275nextContext"]();l["\u0275\u0275property"]("ngClass",0!=e.udrfService.udrfUiData.summaryCards.length||e.udrfService.udrfData.areToolbarFiltersApplied?0==e.udrfService.udrfUiData.summaryCards.length&&e.udrfService.udrfData.areToolbarFiltersApplied?"timeline-large-height-shortened":"timeline-height":"timeline-large-height"),l["\u0275\u0275advance"](4),l["\u0275\u0275property"]("ngForOf",e.header)("ngForTrackBy",e.trackByFn),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngClass",e.udrfService.udrfData.areToolbarFiltersApplied&&0==e.udrfService.udrfUiData.summaryCards.length?"infinite-scroll-large-fixed-shortened":0==e.udrfService.udrfUiData.summaryCards.length?"infinite-scroll-large-fixed":e.udrfService.udrfData.areToolbarFiltersApplied?"infinite-scroll-fixed-shortened":"infinite-scroll-fixed")("infiniteScrollDistance",2)("scrollWindow",!1),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("ngForOf",e.udrfService.udrfUiData.timelineSectionItems)("ngForTrackBy",e.trackByFn),l["\u0275\u0275advance"](1),l["\u0275\u0275styleProp"]("left",e.SectionLeftMeasure),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngForOf",e.udrfService.udrfUiData.timelineSectionItems)("ngForTrackBy",e.trackByFn)}}const q=c;let K=(()=>{class e{constructor(e,t,i,n,r){this.changeDetector=e,this.service=t,this.udrfService=i,this.scrollDispatcher=n,this.el=r,this.currentTimeFormat="DD-MMM-YYYY HH:mm",this.showCurrentTime=!0,this.showGoto=!0,this.showToday=!0,this.allowDragging=!1,this.locale="",this.showBusinessDayOnly=!1,this.headerFormat="Do MMM YYYY",this.minRowHeight=40,this.maxHeight=null,this.text=new D,this.sections=[],this.items=[],this.events=new y,this.start=q().subtract(1,"year").startOf("year"),this.end=q().endOf("day"),this.showGotoModal=!1,this.currentTimeVisibility="visible",this.ShowCurrentTimeHandle=null,this.SectionLeftMeasure="0",this.currentPeriodMinuteDiff=0,this.subscription=new M.a,this.showCurrentTimeIndicator=()=>{this.ShowCurrentTimeHandle&&clearTimeout(this.ShowCurrentTimeHandle);const e=q();e>=this.start&&e<=this.end?(this.currentTimeVisibility="visible",this.currentTimeIndicatorPosition=Math.abs(this.start.diff(e,"minutes"))/this.currentPeriodMinuteDiff*100+"%",this.currentTimeTitle=e.format(this.currentTimeFormat)):this.currentTimeVisibility="hidden",this.ShowCurrentTimeHandle=setTimeout(this.showCurrentTimeIndicator,3e4)},q.locale(this.locale),this.scrollDispatcher.scrolled().subscribe(e=>{const t=e.measureScrollOffset("top");console.log("scrolldispatcher"),Array.from(this.scrollDispatcher.scrollContainers.keys()).filter(t=>t&&t!==e).forEach(e=>{e.measureScrollOffset("top")!==t&&e.scrollTo({top:t})})})}ngOnInit(){if(console.log("Timeline"),this.udrfService.udrfUiData.timelineSetSectionsInSectionItems=this.setSectionsInSectionItems.bind(this),console.log("TimelineSSI"),this.udrfService.udrfUiData.changeTimelinePeriod=this.changeTimelinePeriod.bind(this),this.udrfService.udrfUiData.timelineNavigationNext=this.nextPeriod.bind(this),this.udrfService.udrfUiData.timelineNavigationPrevious=this.previousPeriod.bind(this),this.start=""!=this.udrfService.udrfUiData.timelineStartdate?q(this.udrfService.udrfUiData.timelineStartdate).startOf("month"):q().subtract(1,"year").startOf("year"),this.setSectionsInSectionItems(),null!=this.start){for(let e of this.periods)e.isActive&&(this.selectedPeriod=e);this.changePeriod(this.selectedPeriod,!1)}this.itemPush(),this.itemPop(),this.itemRemove(),this.sectionPush(),this.sectionPop(),this.sectionRemove()}itemDataScrollDown(){console.log("scrolled")}refreshView(){this.setSectionsInSectionItems(),this.changePeriod(this.currentPeriod,!1)}trackByFn(e,t){return e}setSectionsInSectionItems(){console.log(this.udrfService.udrfUiData.timelineCurrentPeriod),this.start="year"==this.udrfService.udrfUiData.timelineCurrentPeriod?""!=this.udrfService.udrfUiData.timelineStartdate?q(this.udrfService.udrfUiData.timelineStartdate).startOf("year"):q().startOf("year"):"month"==this.udrfService.udrfUiData.timelineCurrentPeriod?""!=this.udrfService.udrfUiData.timelineStartdate?q(this.udrfService.udrfUiData.timelineStartdate).startOf("month"):q().startOf("year"):null!=this.udrfService.udrfUiData.timelineStartdate?q(this.udrfService.udrfUiData.timelineStartdate).startOf("month"):q().startOf("year"),console.log(this.start),console.log(this.udrfService.udrfUiData.timelineStartdate),this.udrfService.udrfUiData.timelineSectionItems=new Array,this.udrfService.udrfUiData.timelineSections.forEach(e=>{const t=new x;t.section=e,t.hierarchy=e.hierarchy,t.expanded=e.expanded,t.minRowHeight=this.minRowHeight,this.udrfService.udrfUiData.timelineSectionItems.push(t)}),console.log("section"),console.log(this.udrfService.udrfUiData.timelineSections),1==this.udrfService.udrfUiData.changeTimelinePeriodData.isActive&&(this.selectedPeriod=this.udrfService.udrfUiData.changeTimelinePeriodData),this.changePeriod(this.selectedPeriod,!1)}setItemsInSectionItems(){const e=new Array;this.udrfService.udrfUiData.timelineSectionItems.forEach(t=>{t.itemMetas=new Array,t.minRowHeight=this.minRowHeight,this.udrfService.udrfUiData.timelineItems.filter(i=>{let n=new w;i.sectionID===t.section.id&&(n.item=i,n.item.start<=this.end&&n.item.end>=this.start&&(n=this.itemMetaCal(n),t.itemMetas.push(n),e.push(n)))})}),e.reduce((e,t)=>{const i=this.udrfService.udrfUiData.timelineSectionItems.findIndex(e=>e.section.id===t.item.sectionID);return e[i]||(e[i]=[]),e[i].push(t),e},{})}itemMetaCal(e){const t=q.max(e.item.start,this.start),i=q.min(e.item.end,this.end);let n=Math.abs(t.diff(i,"minutes")),r=t.diff(this.start,"minutes");return this.showBusinessDayOnly&&(n-=this.getNumberOfWeekendDays(q(t),q(i))*this.currentPeriod.timeFramePeriod,r-=this.getNumberOfWeekendDays(q(this.start),q(t))*this.currentPeriod.timeFramePeriod),e.cssLeft=r/this.currentPeriodMinuteDiff*100,e.cssWidth=n/this.currentPeriodMinuteDiff*100,e.item.start>=this.start&&(e.isStart=!0),e.item.end<=this.end&&(e.isEnd=!0),e}calCssTop(e){for(const t of Object.keys(e))for(let i=0;i<e[t].length;i++){let n;const r=e[t][i];for(let s=0;s<i;s++){const i=e[t][s],a=i.cssTop+this.minRowHeight;n=r.cssTop+this.minRowHeight,(i.item.start<=r.item.start&&r.item.start<=i.item.end||i.item.start<=r.item.end&&r.item.end<=i.item.end||i.item.start>=r.item.start&&r.item.end>=i.item.end)&&(i.cssTop<=r.cssTop&&r.cssTop<=a||i.cssTop<=n&&n<=a)&&(r.cssTop=a+1)}n=r.cssTop+this.minRowHeight+1,this.sectionItems[Number(t)]&&n>this.sectionItems[Number(t)].minRowHeight&&(this.sectionItems[Number(t)].minRowHeight=n)}}changeTimelinePeriod(e=!0){this.start="year"==this.udrfService.udrfUiData.timelineCurrentPeriod?""!=this.udrfService.udrfUiData.timelineStartdate?q(this.udrfService.udrfUiData.timelineStartdate).startOf("year"):q().subtract(1,"year").startOf("year"):""!=this.udrfService.udrfUiData.timelineStartdate?q(this.udrfService.udrfUiData.timelineStartdate).startOf("month"):q().subtract(1,"year").startOf("year"),this.currentPeriod=this.udrfService.udrfUiData.changeTimelinePeriodData,console.log("period change"),console.log(this.currentPeriod),this.end=q(this.start).add(this.currentPeriod.timeFrameOverall,"minutes").endOf("day"),this.currentPeriodMinuteDiff=Math.abs(this.start.diff(this.end,"minutes")),e&&this.events.PeriodChange&&this.events.PeriodChange(this.start,this.end),this.showBusinessDayOnly&&(this.currentPeriodMinuteDiff-=this.getNumberOfWeekendDays(q(this.start),q(this.end))*this.currentPeriod.timeFramePeriod),this.header=new Array,this.currentPeriod.timeFrameHeaders.forEach((e,t)=>{this.header.push(this.getDatesBetweenTwoDates(e,t))}),this.setItemsInSectionItems(),this.showCurrentTimeIndicator()}changePeriod(e,t=!0){this.currentPeriod=this.selectedPeriod,this.start="year"==this.udrfService.udrfUiData.timelineCurrentPeriod?""!=this.udrfService.udrfUiData.timelineStartdate?q(this.udrfService.udrfUiData.timelineStartdate).startOf("year"):q().subtract(1,"year").startOf("year"):""!=this.udrfService.udrfUiData.timelineStartdate?q(this.udrfService.udrfUiData.timelineStartdate).startOf("month"):q().subtract(1,"year").startOf("year"),this.end=q(this.start).add(this.currentPeriod.timeFrameOverall,"minutes").endOf("day"),this.currentPeriodMinuteDiff=Math.abs(this.start.diff(this.end,"minutes")),t&&this.events.PeriodChange&&this.events.PeriodChange(this.start,this.end),this.showBusinessDayOnly&&(this.currentPeriodMinuteDiff-=this.getNumberOfWeekendDays(q(this.start),q(this.end))*this.currentPeriod.timeFramePeriod),this.header=new Array,this.currentPeriod.timeFrameHeaders.forEach((e,t)=>{this.header.push(this.getDatesBetweenTwoDates(e,t))}),this.setItemsInSectionItems(),this.showCurrentTimeIndicator()}changePeriodopenTimeline(){this.currentPeriod=this.selectedPeriod,this.end=q(this.start).add(this.currentPeriod.timeFrameOverall,"minutes").endOf("day"),this.currentPeriodMinuteDiff=Math.abs(this.start.diff(this.end,"minutes")),this.showBusinessDayOnly&&(this.currentPeriodMinuteDiff-=this.getNumberOfWeekendDays(q(this.start),q(this.end))*this.currentPeriod.timeFramePeriod),this.header=new Array,this.currentPeriod.timeFrameHeaders.forEach((e,t)=>{this.header.push(this.getDatesBetweenTwoDates(e,t))}),this.setItemsInSectionItems(),this.showCurrentTimeIndicator()}gotoToday(){this.start=q().startOf("day"),this.changePeriod(this.currentPeriod)}nextPeriod(){this.start.add(this.currentPeriod.timeFrameOverall,"minutes"),this.changeTimelinePeriod(this.currentPeriod)}previousPeriod(){this.start.subtract(this.currentPeriod.timeFrameOverall,"minutes"),this.changeTimelinePeriod(this.currentPeriod)}gotoDate(e){this.showGotoModal=!1,this.start=q(e).startOf("day"),this.changePeriod(this.currentPeriod)}getDatesBetweenTwoDates(e,t){const i=q(this.start),n=new P;let r,s=0;for(;i.isBefore(this.end)||i.isSame(this.end);){if(!this.showBusinessDayOnly||0!==i.day()&&6!==i.day()){const a=new _;a.name=i.locale(this.locale).format(e),r&&r!==a.name?s=1:(s++,n.headerDetails.pop()),r=a.name,a.colspan=s,a.tooltip=this.currentPeriod.timeFrameHeadersTooltip&&this.currentPeriod.timeFrameHeadersTooltip[t]?i.locale(this.locale).format(this.currentPeriod.timeFrameHeadersTooltip[t]):"",n.headerDetails.push(a)}i.add(this.currentPeriod.timeFramePeriod,"minutes")}return n}getNumberOfWeekendDays(e,t){let i=0;for(;e.isBefore(t)||e.isSame(t);)0!==e.day()&&6!==e.day()||i++,e.add(this.currentPeriod.timeFramePeriod,"minutes");return i}drop(e){e.item.data.sectionID=e.container.data.id,this.refreshView(),this.events.ItemDropped(e.item.data)}itemPush(){this.subscription.add(this.service.itemAdd.asObservable().subscribe(e=>{this.items.push(e),this.refreshView()}))}itemPop(){this.subscription.add(this.service.item.asObservable().subscribe(()=>{this.items.pop(),this.refreshView()}))}itemRemove(){this.subscription.add(this.service.itemId.asObservable().subscribe(e=>{this.items.splice(this.items.findIndex(t=>t.id===e),1),this.refreshView()}))}sectionPush(){this.subscription.add(this.service.sectionAdd.asObservable().subscribe(e=>{this.sections.push(e),this.refreshView()}))}sectionPop(){this.subscription.add(this.service.section.asObservable().subscribe(()=>{this.sections.pop(),this.refreshView()}))}sectionRemove(){this.subscription.add(this.service.sectionId.asObservable().subscribe(e=>{this.sections.splice(this.sections.findIndex(t=>t.id===e),1),this.refreshView()}))}refresh(){this.subscription.add(this.service.refreshView.asObservable().subscribe(()=>{this.refreshView()}))}reoninit(){this.ngOnInit()}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(l["\u0275\u0275directiveInject"](l.ChangeDetectorRef),l["\u0275\u0275directiveInject"](I),l["\u0275\u0275directiveInject"](g.a),l["\u0275\u0275directiveInject"](k.f),l["\u0275\u0275directiveInject"](l.ElementRef))},e.\u0275cmp=l["\u0275\u0275defineComponent"]({type:e,selectors:[["ngx-ts","periods",""]],inputs:{currentTimeFormat:"currentTimeFormat",showCurrentTime:"showCurrentTime",showGoto:"showGoto",showToday:"showToday",allowDragging:"allowDragging",locale:"locale",showBusinessDayOnly:"showBusinessDayOnly",headerFormat:"headerFormat",minRowHeight:"minRowHeight",maxHeight:"maxHeight",text:"text",periods:"periods",events:"events",start:"start"},attrs:F,decls:1,vars:1,consts:[["class","time-sch-wrapper",3,"ngClass",4,"ngIf"],[1,"time-sch-wrapper",3,"ngClass"],[1,"time-sch-table-wrapper","sticky-top"],[1,"sch-header"],[1,"time-sch-table-header"],["class","time-sch-table-header-tr",4,"ngFor","ngForOf","ngForTrackBy"],[1,"time-sch-content-wrap"],["infinite-scroll","","cdkScrollable","",1,"col-12",3,"ngClass","infiniteScrollDistance","scrollWindow","scrolled"],[1,"table-body"],[1,"time-sch-table"],[3,"ngClass",4,"ngFor","ngForOf","ngForTrackBy"],["cdkDropListGroup","",1,"time-sch-section-wrapper"],["cdkDropList","","cdkDropListSortingDisabled","",3,"ngClass","cdkDropListData","height","cdkDropListDropped",4,"ngFor","ngForOf","ngForTrackBy"],[1,"time-sch-table-header-tr"],["class","text-center-header",3,"colSpan","title",4,"ngFor","ngForOf","ngForTrackBy"],[1,"text-center-header",3,"colSpan","title"],[3,"ngClass"],[4,"ngFor","ngForOf","ngForTrackBy"],["cdkDropList","","cdkDropListSortingDisabled","",3,"ngClass","cdkDropListData","cdkDropListDropped"],[4,"ngIf"],["class","time-sch-item","cdkDrag","","cdkDragLockAxis","y","cdkDragBoundary",".time-sch-section-wrapper",3,"cdkDragData","cdkDragDisabled","ngClass","height","top","left","width","click","contextmenu",4,"ngFor","ngForOf"],["class","time-sch-current-time",3,"title","visibility","left",4,"ngIf"],["cdkDrag","","cdkDragLockAxis","y","cdkDragBoundary",".time-sch-section-wrapper",1,"time-sch-item",3,"cdkDragData","cdkDragDisabled","ngClass","click","contextmenu"],["class","item-drag-placeholder",3,"height","left","width",4,"cdkDragPlaceholder"],["class","time-sch-item-start",4,"ngIf"],[1,"time-sch-item-content",3,"ngClass","title","matTooltip"],["class","time-sch-item-end",4,"ngIf"],[1,"item-drag-placeholder"],[1,"time-sch-item-start"],[1,"time-sch-item-end"],[1,"time-sch-current-time",3,"title"]],template:function(e,t){1&e&&l["\u0275\u0275template"](0,G,12,12,"div",0),2&e&&l["\u0275\u0275property"]("ngIf",t.udrfService.udrfBodyData.length>0)},directives:[n.NgIf,n.NgClass,n.NgForOf,T.a,k.b,B.f,B.e,B.a,B.c,U.a],styles:[".d-inline-block[_ngcontent-%COMP%]{display:inline-block!important}.text-center-header[_ngcontent-%COMP%]{overflow-x:hidden;table-layout:fixed;white-space:nowrap;text-overflow:ellipsis!important;text-align:center!important;font-size:9px;font-weight:500}.m-0[_ngcontent-%COMP%]{margin:0!important}.mb-1[_ngcontent-%COMP%]{margin-bottom:1rem!important}.btn[_ngcontent-%COMP%]{border:1px solid #e1e1e1;font-weight:600;text-decoration:none;color:#222;height:30px;padding:.5em 1em;cursor:pointer;margin:.2rem;border-radius:4px}.infinite-scroll-fixed-shortened[_ngcontent-%COMP%]{min-height:73vh;max-height:73vh;overflow-y:scroll;padding:0}.infinite-scroll-fixed[_ngcontent-%COMP%]{min-height:66vh;max-height:66vh;overflow-y:scroll;padding:0}.infinite-scroll-large-fixed[_ngcontent-%COMP%]{min-height:78vh;max-height:78vh;overflow-y:scroll;padding:0}.infinite-scroll-large-fixed-shortened[_ngcontent-%COMP%]{min-height:70vh;max-height:71vh;overflow-y:scroll;padding:0}.table-item-height[_ngcontent-%COMP%]{height:5.2vh!important}.table-item-height-l2[_ngcontent-%COMP%]{height:4vh!important}.table-item-no-height[_ngcontent-%COMP%]{display:none}.sch-header[_ngcontent-%COMP%]{height:6.1vh!important;background-color:#fff;background-clip:initial;background-color:#f5f5f5}.goto-modal[_ngcontent-%COMP%]{position:absolute;top:100%;left:0;height:auto;width:auto;border-radius:4px;background-color:#ddd;padding:5px;text-align:left;z-index:1}.time-sch-wrapper[_ngcontent-%COMP%]{overflow-x:scroll;overflow-y:hidden;height:67vh}.time-sch-header-wrapper[_ngcontent-%COMP%], .time-sch-table-wrapper[_ngcontent-%COMP%], .time-sch-wrapper[_ngcontent-%COMP%]{position:relative}.time-sch-table-wrapper[_ngcontent-%COMP%]{width:max-content}.timeline-height[_ngcontent-%COMP%]{height:68vh!important;overflow-x:scroll;width:100%}.timeline-large-height[_ngcontent-%COMP%]{height:84vh!important;overflow-x:scroll;width:100%}.timeline-large-height-shortened[_ngcontent-%COMP%]{height:77vh!important;overflow-x:scroll;width:100%}.time-sch-header-wrapper[_ngcontent-%COMP%]{padding:.5em;margin-bottom:.5em}.table[_ngcontent-%COMP%]{display:block}.table[_ngcontent-%COMP%], body[_ngcontent-%COMP%]{margin:0!important;padding:0!important}.time-sch-table[_ngcontent-%COMP%]{margin:0!important;display:inline-table;width:100%}.time-sch-table[_ngcontent-%COMP%], .time-sch-table-header[_ngcontent-%COMP%]{border-collapse:collapse;border-spacing:0;background-color:#f5f5f5;padding:0!important}.time-sch-table-header[_ngcontent-%COMP%]{width:max-content;table-layout:fixed;margin:0 8px 0 0!important;display:block}.time-sch-table-header-tr[_ngcontent-%COMP%]{height:3vh;white-space:nowrap;line-height:.4vh}.table-body[_ngcontent-%COMP%]{height:66vh;top:36vh}.time-sch-period-container[_ngcontent-%COMP%]{float:left;position:relative}.time-sch-time-container[_ngcontent-%COMP%]{float:right;position:relative}.time-sch-wrapper[_ngcontent-%COMP%]   .time-sch-section[_ngcontent-%COMP%]{width:200px}.time-sch-wrapper[_ngcontent-%COMP%]   td[_ngcontent-%COMP%], .time-sch-wrapper[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{width:2vw;border-color:#e1e1e1 #c1c1c1;border-style:solid;border-width:1px}.time-sch-content-wrap[_ngcontent-%COMP%]{position:relative}.time-sch-section-wrapper[_ngcontent-%COMP%]{position:absolute;top:7px;right:0;bottom:0;z-index:1;padding-left:1px;padding-right:1px}.time-sch-section-container[_ngcontent-%COMP%]{position:relative;overflow:hidden;height:5.2vh!important}.time-sch-section-container-l2[_ngcontent-%COMP%]{position:relative;overflow:hidden;height:4vh!important}.time-sch-item[_ngcontent-%COMP%]{height:3.4vh!important;background-color:#0c82cd;border-radius:2px;border:1px solid #c1c1c1;border-radius:25px!important}.time-sch-item[_ngcontent-%COMP%], .time-sch-item-l2[_ngcontent-%COMP%]{position:absolute;min-height:1em;clear:both;color:#fff;cursor:pointer;transition:background-color .1s ease}.time-sch-item-l2[_ngcontent-%COMP%]{height:2.4vh!important;background-color:#3b9ff7;border-radius:2.4px;border:1px solid #c1c1c1;border-radius:25px!important}.time-sch-item-content[_ngcontent-%COMP%], .time-sch-item-content-l2[_ngcontent-%COMP%]{overflow:hidden;white-space:nowrap;position:relative;z-index:1;padding:0}.time-sch-item-content-l2[_ngcontent-%COMP%]{font-size:14px;color:#e0dcdc}.time-sch-item-end[_ngcontent-%COMP%], .time-sch-item-start[_ngcontent-%COMP%]{position:absolute;top:1px;bottom:px}.time-sch-item-start[_ngcontent-%COMP%]{left:1px}.time-sch-item-end[_ngcontent-%COMP%]{right:1px}.time-sch-current-time[_ngcontent-%COMP%]{position:absolute;top:0;bottom:0;z-index:2;background:transparent;border-left:1px dotted #000}.item-drag-placeholder[_ngcontent-%COMP%]{position:absolute;background:#ccc;border:3px dotted #999;transition:transform .25s cubic-bezier(0,0,.2,1)}"]}),e})();function $(e,t){if(1&e&&l["\u0275\u0275element"](0,"ngx-ts",4),2&e){const e=l["\u0275\u0275nextContext"]();l["\u0275\u0275property"]("periods",e.periods)("showBusinessDayOnly",!1)("allowDragging",!1)("start",e.udrfService.udrfUiData.timelineStartdate)}}const J=[{path:"",component:(()=>{class e{constructor(e,t,i,n,r,s,l,h,g){this.loginService=e,this.udrfService=t,this.sharedLazyLoadedComponentsService=i,this.isaService=n,this.rmgService=r,this.graphService=s,this.utilityService=l,this.dialog=h,this.$router=g,this.applicationId=159,this.currentUser={},this.requestData=[],this.skip=0,this.limit=20,this._onDestroy=new o.b,this._onAppApiCalled=new o.b,this.user=this.loginService.getProfile().profile,this.udrfBodyColumns=[{item:"resourceName",resourceName:"",header:"Resource Name/Project/Opportunity",isVisible:"true",type:"text",position:1,isActive:!0,colSize:"2",textClass:"colorRed value13Bold",sortOrder:"N",width:250},{item:"status_name",header:"Status",isVisible:"true",type:"status",position:2,isActive:!0,colSize:"1",textClass:"value13Bold",sortOrder:"N",width:200},{item:"startDate",header:"Start Date",isVisible:"true",type:"date",position:3,isActive:!0,colSize:"1",textClass:"value13Bold",headerTextClass:"",sortOrder:"N",width:150},{item:"endDate",header:"End Date",isVisible:"true",type:"date",position:4,isActive:!0,colSize:"1",textClass:"value13Bold",headerTextClass:"",sortOrder:"N",width:150},{item:"skillSetName",header:"Skillset",isVisible:"true",type:"text",position:5,isActive:!0,colSize:"2",textClass:"value13Bold",headerTextClass:"",sortOrder:"N",width:250},{item:"orgName",header:"Organization",isVisible:"true",type:"text",position:6,isActive:!0,colSize:"2",sortOrder:"N",width:250},{item:"request_number",header:"Request No.",isVisible:"false",type:"text",position:7,isActive:!0,colSize:"1",textClass:"value13Bold",sortOrder:"N",width:200},{item:"originatorName",originatorName:"",header:"Originator",isVisible:"false",type:"text",position:8,isActive:!0,colSize:"1",textClass:"value13Bold",headerTextClass:"",sortOrder:"N",width:200},{item:"locationName",header:"Location",isVisible:"false",type:"text",position:9,isActive:!0,colSize:"1",textClass:"value13Bold",sortOrder:"N",width:200},{item:"nationalityName",header:"Nationality",isVisible:"false",type:"text",position:10,isActive:!0,colSize:"1",textClass:"value13Bold",headerTextClass:"",sortOrder:"N",width:200},{item:"budget",header:"Budget",isVisible:"false",type:"text",position:11,isActive:!0,colSize:"1",textClass:"value13Bold",headerTextClass:"",sortOrder:"N",width:200},{item:"createdOn",header:"Created On",isVisible:"false",type:"date",position:12,isActive:!0,colSize:"1",textClass:"value14Bold",headerTextClass:"",sortOrder:"N",width:200},{item:"action",isActive:!0,header:"Actions",isVisible:"false",type:"action",position:13,colSize:1,width:100,sortOrder:"N"},{item:"approvers",header:"Approver",isVisible:"false",type:"profileImgList",position:14,isActive:!0,colSize:"2",textClass:"value13Bold",headerTextClass:"",sortOrder:"N",width:240}],this.itemCount=0,this.sectionCount=10,this.sections=[],this.items=[],this.udrfItemStatusColor=[{status:"Active",color:"#009432"},{status:"Inactive",color:"#cf0001"},{status:"Closed",color:"#009432"}],this.getRequestData=()=>Object(a.c)(this,void 0,void 0,(function*(){let e=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),t=this.getFilters(e),i={start_date:this.udrfService.udrfData.mainApiDateRangeStart,end_date:this.udrfService.udrfData.mainApiDateRangeEnd,mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,skip:this.skip,limit:this.limit,filter:t};return new Promise((e,t)=>{this.isaService.getAllRequestsForRmg(i).pipe(Object(d.a)(this._onDestroy)).pipe(Object(d.a)(this._onAppApiCalled)).subscribe(e=>{if(console.log(e.data),"S"==e.messType&&e.data&&e.data.length>0){this.udrfService.udrfData.isItemDataLoading=!1,this.udrfService.udrfBodyData=this.udrfService.udrfBodyData.concat(e.data),console.log(this.udrfService.udrfBodyData),console.log(this.udrfService.udrfBodyData.length),console.log(this.itemCount);for(let e=this.itemCount;e<this.udrfService.udrfBodyData.length;e++){console.log(e),this.udrfService.udrfUiData.timelineSections.push({id:this.udrfService.udrfBodyData[e]._id,name:this.udrfService.udrfBodyData[e].resourceName,expanded:!0,hierarchy:"l1"});for(let t=0;t<this.udrfService.udrfBodyData[e].l2.length;t++)this.udrfService.udrfUiData.timelineSections.push({index:e,id:this.udrfService.udrfBodyData[e].l2[t]._id,name:this.udrfService.udrfBodyData[e].resourceName,expanded:!!this.udrfService.udrfBodyData[e].expanded,hierarchy:"l2"});this.udrfService.udrfUiData.timelineItems.push({id:this.udrfService.udrfBodyData[e]._id,name:this.udrfService.udrfBodyData[e].resourceName,start:c(this.udrfService.udrfBodyData[e].startDate),end:c(this.udrfService.udrfBodyData[e].endDate),sectionID:this.udrfService.udrfBodyData[e]._id,classes:""});for(let t=0;t<this.udrfService.udrfBodyData[e].l2.length;t++)this.udrfService.udrfUiData.timelineItems.push({id:this.udrfService.udrfBodyData[e].l2[t]._id,name:this.udrfService.udrfBodyData[e].resourceName,start:c(this.udrfService.udrfBodyData[e].l2[t].startDate),end:c(this.udrfService.udrfBodyData[e].l2[t].endDate),sectionID:this.udrfService.udrfBodyData[e].l2[t]._id,classes:"l2"})}this.itemCount=this.udrfService.udrfBodyData.length,console.log(this.udrfService.udrfUiData.timelineSections),console.log(this.udrfService.udrfUiData.timelineItems),this.udrfService.udrfUiData.timelineSetSectionsInSectionItems()}else 0==e.data.length&&0==this.udrfService.udrfBodyData.length?(this.udrfService.udrfBodyData=this.udrfService.udrfBodyData.concat([]),this.udrfService.udrfUiData.timelineSections=[],this.udrfService.udrfUiData.timelineItems=[],this.udrfService.udrfData.isItemDataLoading=!1,this.udrfService.udrfData.noItemDataFound=!0,this.udrfService.udrfUiData.totalItemDataCount=0,t(e)):0==e.data.length?(this.udrfService.udrfBodyData=this.udrfService.udrfBodyData.concat([]),this.udrfService.udrfData.isItemDataLoading=!1,t(e)):(this.udrfService.udrfBodyData=this.udrfService.udrfBodyData.concat([]),this.udrfService.udrfUiData.timelineSections=[],this.udrfService.udrfUiData.timelineItems=[],this.udrfService.udrfData.isItemDataLoading=!1,this.udrfService.udrfData.noItemDataFound=!0,this.udrfService.udrfUiData.totalItemDataCount=0,t(e))})})})),this.currentUser=this.loginService.getProfile().profile}ngOnInit(){return Object(a.c)(this,void 0,void 0,(function*(){this.udrfService.udrfBodyData=[],this.periods=[{name:"day",description:"Day",timeFrameHeaders:["MMM YYYY","DD"],timeFrameHeadersTooltip:["MMM YYYY","DD ddd"],classes:"",timeFrameOverall:1576800,timeFramePeriod:1440,isActive:!0},{name:"month",timeFramePeriod:10080,description:"Month",timeFrameOverall:2628e3,timeFrameHeaders:["MMM YYYY","WW"],classes:"",isActive:!1},{name:"year",timeFrameHeaders:["YYYY","MMM"],description:"Year",classes:"",timeFrameOverall:5256e3,timeFramePeriod:1440,isActive:!1}];let e=[{checkboxId:"CDCRD",checkboxName:"Current Year",checkboxStartValue:c().startOf("year"),checkboxEndValue:c().endOf("year"),isCheckboxDefaultSelected:!0},{checkboxId:"CDCRD2",checkboxName:"This Week",checkboxStartValue:this.utilityService.getFormattedDate(c().startOf("week"),c(c().startOf("week")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(c().endOf("week"),c(c().endOf("week")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD3",checkboxName:"This Month",checkboxStartValue:this.utilityService.getFormattedDate(c().startOf("month"),c(c().startOf("month")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(c().endOf("month"),c(c().endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD4",checkboxName:"Next Month",checkboxStartValue:this.utilityService.getFormattedDate(c().add(1,"month").startOf("month"),c(c().add(1,"month").startOf("month")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(c().add(1,"month").endOf("month"),c(c().add(1,"month").endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD5",checkboxName:"Upcoming 3 Months",checkboxStartValue:this.utilityService.getFormattedDate(c().startOf("month"),c(c().startOf("month")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(c().add(2,"month").endOf("month"),c(c().add(2,"month").endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1}];this.udrfService.udrfFunctions.constructCustomRangeData(18,"date",e),this.initUdrf()}))}initUdrf(){this.udrfService.udrfUiData.showItemDataCount=!0,this.udrfService.udrfUiData.itemDataType="",this.udrfService.udrfUiData.totalItemDataCount=0,this.udrfService.udrfUiData.showSearchBar=!0,this.udrfService.udrfUiData.showActionButtons=!0,this.udrfService.udrfUiData.showUdrfModalButton=!0,this.udrfService.udrfUiData.showSettingsModalButton=!1,this.udrfService.udrfUiData.isReportDownloading=!1,this.udrfService.udrfUiData.showReportDownloadButton=!1,this.udrfService.udrfUiData.showColumnConfigButton=!0,this.udrfService.udrfUiData.horizontalScroll=!0,this.udrfService.udrfUiData.itemHasOpenBtn=!0,this.udrfService.udrfUiData.udrfItemStatusColor=this.udrfItemStatusColor,this.udrfService.udrfUiData.downloadItemDataReport=()=>{},this.udrfService.udrfUiData.itemDataScrollDown=this.onItemDataScrollDown.bind(this),this.udrfService.udrfUiData.itemcardSelected=this.openAssociateDetailsModal.bind(this),this.udrfService.udrfUiData.updateItemCard=()=>{},this.udrfService.udrfUiData.attachFile=()=>{},this.udrfService.udrfUiData.deleteFile=()=>{},this.udrfService.udrfUiData.downloadFile=()=>{},this.udrfService.udrfUiData.closeCTA=()=>{},this.udrfService.udrfUiData.udrfBodyColumns=this.udrfBodyColumns,this.udrfService.udrfUiData.udrfVisibleBodyColumns=this.udrfService.udrfUiData.udrfVisibleBodyColumns,this.udrfService.udrfUiData.udrfInvisibleBodyColumns=this.udrfService.udrfUiData.udrfInvisibleBodyColumns,this.udrfService.udrfUiData.selectedCard=[],this.udrfService.udrfUiData.variant=1,this.udrfService.udrfUiData.itemHasQuickCta=!1,this.udrfService.udrfUiData.itemHasComments=!1,this.udrfService.udrfUiData.showCollapseButton=!0,this.udrfService.udrfUiData.collapseAll=!1,this.udrfService.udrfUiData.summaryCards=[],this.udrfService.udrfUiData.isTimelineView=!1,this.udrfService.udrfUiData.hasTimelineViewButton=!0,this.udrfService.udrfUiData.timelinePeriod=this.periods,this.udrfService.udrfUiData.changeTimelinePeriodData=this.periods[0],this.udrfService.getAppUdrfConfig(this.applicationId,this.initResource.bind(this))}initResource(){this.skip=0,this.limit=20,this.itemCount=0,this._onAppApiCalled.next(),this.udrfService.udrfBodyData=[],this.udrfService.udrfUiData.timelineSections=[],this.udrfService.udrfUiData.timelineItems=[],this.udrfService.udrfUiData.resolveColumnConfig(),this.getTotalCount()}initResourceList(){return Object(a.c)(this,void 0,void 0,(function*(){this.udrfService.udrfBodyData=[],this.udrfService.udrfUiData.timelineSections=[],this.udrfService.udrfUiData.timelineItems=[],this.getRequestData()}))}getTotalCount(){return Object(a.c)(this,void 0,void 0,(function*(){let e=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),t=this.getFilters(e),i={start_date:this.udrfService.udrfData.mainApiDateRangeStart,end_date:this.udrfService.udrfData.mainApiDateRangeEnd,mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,skip:this.skip,limit:this.limit,filter:t},n=yield this.isaService.getTotalForRmg(i);console.log(n.total),console.log(n.minStartdate),"S"==n.messType&&n.total?(this.udrfService.udrfUiData.totalItemDataCount=n.total,this.udrfService.udrfUiData.timelineStartdate=n.minStartdate):(this.udrfService.udrfUiData.totalItemDataCount=0,this.udrfService.udrfUiData.timelineStartdate=""),this.initResourceList()}))}getFilters(e){let t=[];for(let i of e)t.push(i.isIdBased?{filterName:i.filterName,valueId:i.multiOptionSelectSearchValuesWithId}:{filterName:i.filterName,valueId:i.multiOptionSelectSearchValues});return t}openAssociateDetailsModal(){return Object(a.c)(this,void 0,void 0,(function*(){this.sharedLazyLoadedComponentsService.openAssociateDetailsModal({applicationId:159,objectType:"L",objectId:this.udrfService.udrfUiData.itemCardSelecteditem,originatorData:null},this.dialog)}))}onItemDataScrollDown(){return Object(a.c)(this,void 0,void 0,(function*(){if(!this.udrfService.udrfData.noItemDataFound&&!this.udrfService.udrfData.isItemDataLoading){this.skip+=this.limit,this.udrfService.udrfData.isItemDataLoading=!0;let e=[];e=yield this.getRequestData(),0==e.length?(this.udrfService.udrfBodyData=this.udrfService.udrfBodyData.concat([]),this.udrfService.udrfData.noItemDataFound=!0,this.udrfService.udrfData.isItemDataLoading=!1):(this.udrfService.udrfBodyData=this.udrfService.udrfBodyData.concat(e),this.udrfService.udrfData.isItemDataLoading=!1)}}))}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete(),this.udrfService.resetUdrfData()}}return e.\u0275fac=function(t){return new(t||e)(l["\u0275\u0275directiveInject"](h.a),l["\u0275\u0275directiveInject"](g.a),l["\u0275\u0275directiveInject"](m.a),l["\u0275\u0275directiveInject"](u.a),l["\u0275\u0275directiveInject"](v),l["\u0275\u0275directiveInject"](C.a),l["\u0275\u0275directiveInject"](p.a),l["\u0275\u0275directiveInject"](b.b),l["\u0275\u0275directiveInject"](s.g))},e.\u0275cmp=l["\u0275\u0275defineComponent"]({type:e,selectors:[["rmg-landing-page"]],decls:5,vars:3,consts:[[1,"container-fluid","rmg-landing-page-styles","pl-0","pr-0"],[1,"row",3,"ngClass"],["cdkScrollable","",3,"ngClass"],["class","timeline","cdkScrollable","",3,"periods","showBusinessDayOnly","allowDragging","start",4,"ngIf"],["cdkScrollable","",1,"timeline",3,"periods","showBusinessDayOnly","allowDragging","start"]],template:function(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"div",0),l["\u0275\u0275element"](1,"udrf-header"),l["\u0275\u0275elementStart"](2,"div",1),l["\u0275\u0275element"](3,"udrf-body",2),l["\u0275\u0275template"](4,$,1,4,"ngx-ts",3),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&e&&(l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngClass",t.udrfService.udrfData.areToolbarFiltersApplied?"infinite-scroll-fixed-shortened":"infinite-scroll-fixed"),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngClass",t.udrfService.udrfUiData.isTimelineView?"halfscreen":"fullscreen"),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",t.udrfService.udrfUiData.isTimelineView))},directives:[S.a,n.NgClass,O.a,n.NgIf,K],styles:[".rmg-landing-page-styles[_ngcontent-%COMP%]   .halfscreen[_ngcontent-%COMP%]{position:absolute;width:52.3%}.rmg-landing-page-styles[_ngcontent-%COMP%]   .fullscreen[_ngcontent-%COMP%]{width:100%}.rmg-landing-page-styles[_ngcontent-%COMP%]   .timeline[_ngcontent-%COMP%]{position:absolute;left:51%;width:49%;background:#fff}.rmg-landing-page-styles[_ngcontent-%COMP%]   .status-circular[_ngcontent-%COMP%]{height:13px;width:13px;margin-top:4px;border-radius:50%;box-shadow:0 2px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.rmg-landing-page-styles[_ngcontent-%COMP%]   .is-submitted[_ngcontent-%COMP%]{background:#ff7200;color:#fff!important}.rmg-landing-page-styles[_ngcontent-%COMP%]   .is-draft[_ngcontent-%COMP%]{background:#c7c4c4;color:#fff!important}.rmg-landing-page-styles[_ngcontent-%COMP%]   .is-approved[_ngcontent-%COMP%]{background:#009432;color:#fff!important}.rmg-landing-page-styles[_ngcontent-%COMP%]   .is-wfh[_ngcontent-%COMP%]{background:#9980fa;color:#fff!important}.rmg-landing-page-styles[_ngcontent-%COMP%]   .is-rejected[_ngcontent-%COMP%]{background:#cf0001;color:#fff!important}.rmg-landing-page-styles[_ngcontent-%COMP%]   .is-cancelled[_ngcontent-%COMP%]{background:#9f2825;color:#fff!important}.rmg-landing-page-styles[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field{width:40vw;padding-top:25px}.rmg-landing-page-styles[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-infix{font-size:14px!important;padding:.5em 0!important;border-top:.54375em solid transparent!important}.rmg-landing-page-styles[_ngcontent-%COMP%]   .spinner-align[_ngcontent-%COMP%]{margin:auto;display:inline-block}.rmg-landing-page-styles[_ngcontent-%COMP%]   .infinite-scroll-fixed[_ngcontent-%COMP%]{min-height:83vh;max-height:85vh;overflow-y:scroll}.rmg-landing-page-styles[_ngcontent-%COMP%]   .infinite-scroll-fixed-shortened[_ngcontent-%COMP%]{min-height:77vh;max-height:78vh;overflow-y:scroll}.rmg-landing-page-styles[_ngcontent-%COMP%]   .ta-r[_ngcontent-%COMP%]{text-align:right!important}.rmg-landing-page-styles[_ngcontent-%COMP%]   .ta-l[_ngcontent-%COMP%]{text-align:left}.rmg-landing-page-styles[_ngcontent-%COMP%]   .is-normal-probability-text[_ngcontent-%COMP%]{color:grey!important}.rmg-landing-page-styles[_ngcontent-%COMP%]   .is-low-probability-text[_ngcontent-%COMP%]{color:#cf0001!important}.rmg-landing-page-styles[_ngcontent-%COMP%]   .is-moderate-probability-text[_ngcontent-%COMP%]{color:#ff7200!important}.rmg-landing-page-styles[_ngcontent-%COMP%]   .is-high-probability-text[_ngcontent-%COMP%]{color:#009432!important}.rmg-landing-page-styles[_ngcontent-%COMP%]   .is-normal-probability[_ngcontent-%COMP%]{border-left-color:grey!important}.rmg-landing-page-styles[_ngcontent-%COMP%]   .is-low-probability[_ngcontent-%COMP%]{border-left-color:#cf0001!important}.rmg-landing-page-styles[_ngcontent-%COMP%]   .is-moderate-probability[_ngcontent-%COMP%]{border-left-color:#ff7200!important}.rmg-landing-page-styles[_ngcontent-%COMP%]   .is-high-probability[_ngcontent-%COMP%]{border-left-color:#009432!important}.rmg-landing-page-styles[_ngcontent-%COMP%]   .value13Bold[_ngcontent-%COMP%]{font-size:13px!important}.rmg-landing-page-styles[_ngcontent-%COMP%]   .value13Bold[_ngcontent-%COMP%], .rmg-landing-page-styles[_ngcontent-%COMP%]   .value14Bold[_ngcontent-%COMP%]{color:#000;font-weight:500!important;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.rmg-landing-page-styles[_ngcontent-%COMP%]   .value14Bold[_ngcontent-%COMP%]{font-size:14px!important}.rmg-landing-page-styles[_ngcontent-%COMP%]   .listcard[_ngcontent-%COMP%]{transition:all .25s linear;height:auto}.rmg-landing-page-styles[_ngcontent-%COMP%]   .smallSubtleText[_ngcontent-%COMP%]{color:#66615b;font-size:11px}.rmg-landing-page-styles[_ngcontent-%COMP%]   .data-type-card[_ngcontent-%COMP%]{min-height:85px;transition:all .3s linear}.rmg-landing-page-styles[_ngcontent-%COMP%]   .data-type-card-is-active[_ngcontent-%COMP%]{border-color:#cf0001;background-color:#fff2f2}.rmg-landing-page-styles[_ngcontent-%COMP%]   .headingBold[_ngcontent-%COMP%]{color:#1a1a1a;font-weight:600;font-size:22px}.rmg-landing-page-styles[_ngcontent-%COMP%]   .valueGrey14[_ngcontent-%COMP%]{font-size:14px;font-weight:300}.rmg-landing-page-styles[_ngcontent-%COMP%]   .valueGrey12Normal[_ngcontent-%COMP%], .rmg-landing-page-styles[_ngcontent-%COMP%]   .valueGrey14[_ngcontent-%COMP%]{color:#4a4a4a;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.rmg-landing-page-styles[_ngcontent-%COMP%]   .valueGrey12Normal[_ngcontent-%COMP%]{font-size:12px;font-weight:400}.rmg-landing-page-styles[_ngcontent-%COMP%]   .cp[_ngcontent-%COMP%]{cursor:pointer!important}.rmg-landing-page-styles[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]{margin-top:5px!important;line-height:8px;width:26px;height:26px;margin-right:10px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12);animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}.rmg-landing-page-styles[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%], .rmg-landing-page-styles[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.rmg-landing-page-styles[_ngcontent-%COMP%]   .ta-c[_ngcontent-%COMP%]{text-align:center;padding-left:0!important;padding-right:0!important}.rmg-landing-page-styles[_ngcontent-%COMP%]   .divAlignItemCenter[_ngcontent-%COMP%]{display:flex;align-items:center}.rmg-landing-page-styles[_ngcontent-%COMP%]   .menu-icons[_ngcontent-%COMP%]{font-size:4px}.rmg-landing-page-styles[_ngcontent-%COMP%]   .example-container[_ngcontent-%COMP%]{width:400px;max-width:100%;margin:0 25px 25px 0;display:inline-block;vertical-align:top}.rmg-landing-page-styles[_ngcontent-%COMP%]   .example-list[_ngcontent-%COMP%]{border:1px solid #ccc;min-height:60px;background:#fff;border-radius:4px;overflow:hidden;display:block}.rmg-landing-page-styles[_ngcontent-%COMP%]     .example-box{border-bottom:1px solid #ccc;color:rgba(0,0,0,.87);display:flex;flex-direction:row;align-items:left;justify-content:space-between;box-sizing:border-box;cursor:move;background:#fff;font-size:4px}.rmg-landing-page-styles[_ngcontent-%COMP%]     .menu-list{cursor:move;font-size:8px}.rmg-landing-page-styles[_ngcontent-%COMP%]   .cdk-drag-preview[_ngcontent-%COMP%]{box-sizing:border-box;border-radius:4px;box-shadow:0 5px 5px -3px rgba(0,0,0,.2),0 8px 10px 1px rgba(0,0,0,.14),0 3px 14px 2px rgba(0,0,0,.12)}.rmg-landing-page-styles[_ngcontent-%COMP%]   .cdk-drag-placeholder[_ngcontent-%COMP%]{opacity:0}.rmg-landing-page-styles[_ngcontent-%COMP%]   .cdk-drag-animating[_ngcontent-%COMP%]{transition:transform .25s cubic-bezier(0,0,.2,1)}.rmg-landing-page-styles[_ngcontent-%COMP%]   .example-box[_ngcontent-%COMP%]:last-child{border:none}.rmg-landing-page-styles[_ngcontent-%COMP%]   .example-list.cdk-drop-list-dragging[_ngcontent-%COMP%]   .example-box[_ngcontent-%COMP%]:not(.cdk-drag-placeholder){transition:transform .25s cubic-bezier(0,0,.2,1)}"]}),e})()}];let X=(()=>{class e{}return e.\u0275mod=l["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=l["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[s.k.forChild(J)],s.k]}),e})();var Q=i("Xi0T"),Z=i("se3I");let ee=(()=>{class e{}return e.\u0275mod=l["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=l["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[n.CommonModule,X,r.g,Q.a,Z.a]]}),e})()},w76M:function(e,t,i){"use strict";i.d(t,"a",(function(){return c})),i.d(t,"b",(function(){return l}));var n=i("jhN1"),r=i("fXoL"),s=i("oHs6"),a=i("PVOt"),o=i("6t9p");const d=["*"];let c=(()=>{let e=class extends a.b{constructor(e,t,i,n,r,s,a,o){super(e,t,i,n,a,o),this._watcherHelper=n,this._idh=r,this._createEventEmitters([{subscribe:"contentReady",emit:"onContentReady"},{subscribe:"disposing",emit:"onDisposing"},{subscribe:"hidden",emit:"onHidden"},{subscribe:"hiding",emit:"onHiding"},{subscribe:"initialized",emit:"onInitialized"},{subscribe:"optionChanged",emit:"onOptionChanged"},{subscribe:"resize",emit:"onResize"},{subscribe:"resizeEnd",emit:"onResizeEnd"},{subscribe:"resizeStart",emit:"onResizeStart"},{subscribe:"showing",emit:"onShowing"},{subscribe:"shown",emit:"onShown"},{subscribe:"titleRendered",emit:"onTitleRendered"},{emit:"accessKeyChange"},{emit:"animationChange"},{emit:"closeOnOutsideClickChange"},{emit:"containerChange"},{emit:"contentTemplateChange"},{emit:"deferRenderingChange"},{emit:"disabledChange"},{emit:"dragEnabledChange"},{emit:"elementAttrChange"},{emit:"focusStateEnabledChange"},{emit:"fullScreenChange"},{emit:"heightChange"},{emit:"hintChange"},{emit:"hoverStateEnabledChange"},{emit:"maxHeightChange"},{emit:"maxWidthChange"},{emit:"minHeightChange"},{emit:"minWidthChange"},{emit:"positionChange"},{emit:"resizeEnabledChange"},{emit:"rtlEnabledChange"},{emit:"shadingChange"},{emit:"shadingColorChange"},{emit:"showCloseButtonChange"},{emit:"showTitleChange"},{emit:"tabIndexChange"},{emit:"titleChange"},{emit:"titleTemplateChange"},{emit:"toolbarItemsChange"},{emit:"visibleChange"},{emit:"widthChange"}]),this._idh.setHost(this),s.setHost(this)}get accessKey(){return this._getOption("accessKey")}set accessKey(e){this._setOption("accessKey",e)}get animation(){return this._getOption("animation")}set animation(e){this._setOption("animation",e)}get closeOnOutsideClick(){return this._getOption("closeOnOutsideClick")}set closeOnOutsideClick(e){this._setOption("closeOnOutsideClick",e)}get container(){return this._getOption("container")}set container(e){this._setOption("container",e)}get contentTemplate(){return this._getOption("contentTemplate")}set contentTemplate(e){this._setOption("contentTemplate",e)}get deferRendering(){return this._getOption("deferRendering")}set deferRendering(e){this._setOption("deferRendering",e)}get disabled(){return this._getOption("disabled")}set disabled(e){this._setOption("disabled",e)}get dragEnabled(){return this._getOption("dragEnabled")}set dragEnabled(e){this._setOption("dragEnabled",e)}get elementAttr(){return this._getOption("elementAttr")}set elementAttr(e){this._setOption("elementAttr",e)}get focusStateEnabled(){return this._getOption("focusStateEnabled")}set focusStateEnabled(e){this._setOption("focusStateEnabled",e)}get fullScreen(){return this._getOption("fullScreen")}set fullScreen(e){this._setOption("fullScreen",e)}get height(){return this._getOption("height")}set height(e){this._setOption("height",e)}get hint(){return this._getOption("hint")}set hint(e){this._setOption("hint",e)}get hoverStateEnabled(){return this._getOption("hoverStateEnabled")}set hoverStateEnabled(e){this._setOption("hoverStateEnabled",e)}get maxHeight(){return this._getOption("maxHeight")}set maxHeight(e){this._setOption("maxHeight",e)}get maxWidth(){return this._getOption("maxWidth")}set maxWidth(e){this._setOption("maxWidth",e)}get minHeight(){return this._getOption("minHeight")}set minHeight(e){this._setOption("minHeight",e)}get minWidth(){return this._getOption("minWidth")}set minWidth(e){this._setOption("minWidth",e)}get position(){return this._getOption("position")}set position(e){this._setOption("position",e)}get resizeEnabled(){return this._getOption("resizeEnabled")}set resizeEnabled(e){this._setOption("resizeEnabled",e)}get rtlEnabled(){return this._getOption("rtlEnabled")}set rtlEnabled(e){this._setOption("rtlEnabled",e)}get shading(){return this._getOption("shading")}set shading(e){this._setOption("shading",e)}get shadingColor(){return this._getOption("shadingColor")}set shadingColor(e){this._setOption("shadingColor",e)}get showCloseButton(){return this._getOption("showCloseButton")}set showCloseButton(e){this._setOption("showCloseButton",e)}get showTitle(){return this._getOption("showTitle")}set showTitle(e){this._setOption("showTitle",e)}get tabIndex(){return this._getOption("tabIndex")}set tabIndex(e){this._setOption("tabIndex",e)}get title(){return this._getOption("title")}set title(e){this._setOption("title",e)}get titleTemplate(){return this._getOption("titleTemplate")}set titleTemplate(e){this._setOption("titleTemplate",e)}get toolbarItems(){return this._getOption("toolbarItems")}set toolbarItems(e){this._setOption("toolbarItems",e)}get visible(){return this._getOption("visible")}set visible(e){this._setOption("visible",e)}get width(){return this._getOption("width")}set width(e){this._setOption("width",e)}get toolbarItemsChildren(){return this._getOption("toolbarItems")}set toolbarItemsChildren(e){this.setChildren("toolbarItems",e)}_createInstance(e,t){return new s.a(e,t)}ngOnDestroy(){this._destroyWidget()}ngOnChanges(e){super.ngOnChanges(e),this.setupChanges("toolbarItems",e)}setupChanges(e,t){e in this._optionsToUpdate||this._idh.setup(e,t)}ngDoCheck(){this._idh.doCheck("toolbarItems"),this._watcherHelper.checkWatchers(),super.ngDoCheck(),super.clearChangedOptions()}_setOption(e,t){let i=this._idh.setupSingle(e,t),n=null!==this._idh.getChanges(e,t);(i||n)&&super._setOption(e,t)}};return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](r.ElementRef),r["\u0275\u0275directiveInject"](r.NgZone),r["\u0275\u0275directiveInject"](a.e),r["\u0275\u0275directiveInject"](a.j),r["\u0275\u0275directiveInject"](a.g),r["\u0275\u0275directiveInject"](a.i),r["\u0275\u0275directiveInject"](n.h),r["\u0275\u0275directiveInject"](r.PLATFORM_ID))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["dx-popup"]],contentQueries:function(e,t,i){if(1&e&&r["\u0275\u0275contentQuery"](i,o.L,!1),2&e){let e;r["\u0275\u0275queryRefresh"](e=r["\u0275\u0275loadQuery"]())&&(t.toolbarItemsChildren=e)}},inputs:{accessKey:"accessKey",animation:"animation",closeOnOutsideClick:"closeOnOutsideClick",container:"container",contentTemplate:"contentTemplate",deferRendering:"deferRendering",disabled:"disabled",dragEnabled:"dragEnabled",elementAttr:"elementAttr",focusStateEnabled:"focusStateEnabled",fullScreen:"fullScreen",height:"height",hint:"hint",hoverStateEnabled:"hoverStateEnabled",maxHeight:"maxHeight",maxWidth:"maxWidth",minHeight:"minHeight",minWidth:"minWidth",position:"position",resizeEnabled:"resizeEnabled",rtlEnabled:"rtlEnabled",shading:"shading",shadingColor:"shadingColor",showCloseButton:"showCloseButton",showTitle:"showTitle",tabIndex:"tabIndex",title:"title",titleTemplate:"titleTemplate",toolbarItems:"toolbarItems",visible:"visible",width:"width"},outputs:{onContentReady:"onContentReady",onDisposing:"onDisposing",onHidden:"onHidden",onHiding:"onHiding",onInitialized:"onInitialized",onOptionChanged:"onOptionChanged",onResize:"onResize",onResizeEnd:"onResizeEnd",onResizeStart:"onResizeStart",onShowing:"onShowing",onShown:"onShown",onTitleRendered:"onTitleRendered",accessKeyChange:"accessKeyChange",animationChange:"animationChange",closeOnOutsideClickChange:"closeOnOutsideClickChange",containerChange:"containerChange",contentTemplateChange:"contentTemplateChange",deferRenderingChange:"deferRenderingChange",disabledChange:"disabledChange",dragEnabledChange:"dragEnabledChange",elementAttrChange:"elementAttrChange",focusStateEnabledChange:"focusStateEnabledChange",fullScreenChange:"fullScreenChange",heightChange:"heightChange",hintChange:"hintChange",hoverStateEnabledChange:"hoverStateEnabledChange",maxHeightChange:"maxHeightChange",maxWidthChange:"maxWidthChange",minHeightChange:"minHeightChange",minWidthChange:"minWidthChange",positionChange:"positionChange",resizeEnabledChange:"resizeEnabledChange",rtlEnabledChange:"rtlEnabledChange",shadingChange:"shadingChange",shadingColorChange:"shadingColorChange",showCloseButtonChange:"showCloseButtonChange",showTitleChange:"showTitleChange",tabIndexChange:"tabIndexChange",titleChange:"titleChange",titleTemplateChange:"titleTemplateChange",toolbarItemsChange:"toolbarItemsChange",visibleChange:"visibleChange",widthChange:"widthChange"},features:[r["\u0275\u0275ProvidersFeature"]([a.e,a.j,a.i,a.g]),r["\u0275\u0275InheritDefinitionFeature"],r["\u0275\u0275NgOnChangesFeature"]],ngContentSelectors:d,decls:1,vars:0,template:function(e,t){1&e&&(r["\u0275\u0275projectionDef"](),r["\u0275\u0275projection"](0))},encapsulation:2}),e})(),l=(()=>{let e=class{};return e.\u0275mod=r["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=r["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[o.bb,o.Gc,o.Vd,o.vd,o.hb,o.lb,o.sb,o.id,o.jd,o.M,a.c,a.f,n.b],o.bb,o.Gc,o.Vd,o.vd,o.hb,o.lb,o.sb,o.id,o.jd,o.M,a.f]}),e})()}}]);