(window.webpackJsonp=window.webpackJsonp||[]).push([[870,535,631,634,858],{"4phY":function(e,t,n){"use strict";n.r(t),n.d(t,"SalesDashboardModule",(function(){return rs}));var i=n("ofXK"),a=n("tyNb"),o=n("mrSG"),r=n("xG9w"),l=n("wd/R"),s=n.n(l),c=n("3Pt+"),d=n("fXoL"),p=n("0IaG"),m=n("jhN1");let g=(()=>{class e{transform(e,t,n,i){let a="string"==typeof e?parseFloat(e):e,o="INR"==t?"en-IN":"en-US",r="INR"==t?"Cr":"M";return i&&(a="INR"==t?(a/1e7).toFixed(2):(a/1e6).toFixed(2)),new Intl.NumberFormat(o,{currency:t,minimumFractionDigits:2,maximumFractionDigits:2}).format(a)+""+(i?" "+r:"")+(n?" "+t:"")}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=d["\u0275\u0275definePipe"]({name:"commaSeparation",type:e,pure:!0}),e})();var u=n("XNiG"),f=n("2Vo4"),h=n("tk/3"),_=n("flaP");let v=(()=>{class e{constructor(e,t){this._http=e,this.roleService=t,this.myTeam=void 0,this.widgetConfig=void 0,this.clearFilterSubject=new u.b,this.clearFilter$=this.clearFilterSubject.asObservable(),this.getProductHeirarchy=()=>new Promise((e,t)=>{this._http.post("/api/salesMaster/getProductCategoryHierarchy",{}).subscribe(t=>e(t),e=>t(e))}),this._probability_summary_subject=new f.a({}),this.probability_summary_observable=this._probability_summary_subject,this._pipeline_summary_subject=new f.a({}),this.pipeline_summary_observable=this._pipeline_summary_subject,this._opp_status_summary_subject=new f.a({}),this.opp_status_summary_observable=this._opp_status_summary_subject,this._tasks_summary_subject=new f.a({}),this.tasks_summary_observable=this._tasks_summary_subject,this._obv_summary_subject=new f.a({}),this.obv_summary_observable=this._obv_summary_subject,this._pipeline_owner_subject=new f.a({}),this.pipeline_owner_observable=this._pipeline_owner_subject,this._calls_summary_subject=new f.a({}),this.calls_summary_observable=this._calls_summary_subject,this._mails_summary_subject=new f.a({}),this.mails_summary_observable=this._mails_summary_subject,this._qrcg_summary_subject=new f.a({}),this.qrcg_summary_observable=this._qrcg_summary_subject,this._fte_summary_subject=new f.a({}),this.fte_summary_observable=this._fte_summary_subject,this._finance_data_summary_subject=new f.a({}),this.finance_data_summary_observable=this._finance_data_summary_subject,this._billing_collection_summary_subject=new f.a({}),this.billing_collection_summary_observable=this._billing_collection_summary_subject}clearFilters(){this.clearFilterSubject.next()}getProductHierarchyForDashboard(e){return this._http.post("/api/opportunity/getProductHierarchyForDashboard",{filter_config:e,org_codes:this.roleService.getUserRoleOrgCodes("Opportunities")})}updateFilterConfig(e,t){return this._http.post("/api/opportunity/updateFilterConfig",{status_config:e,filter_config:t})}getOpportunityDiscrepencies(e,t){return this._http.post("/api/opportunity/getOpportunityDiscrepencies",{startDate:e,endDate:t,org_codes:this.roleService.getUserRoleOrgCodes("Opportunities")})}updateProductCategory(e,t){return new Promise((n,i)=>{this._http.post("/api/opportunity/updateProductCategoryInOpp",{product_category:e,opportunity_id:t}).subscribe(e=>n(e),e=>i(e))})}getProductViewHierarchyLabel(e,t){return new Promise((n,i)=>{this._http.post("/api/salesMaster/getProductViewHeirarchy",{application_id:e,label_name:t}).subscribe(e=>n(e),e=>i(e))})}checkForAdmin(){return this.roleService.roles.filter(e=>334==e.application_id&&29338==e.object_id).length>0}getDashboardConfig(){return this._http.post("/api/activity/getDashboardConfig",{})}getConfigFor(e){let t=this.widgetConfig.findIndex(t=>t.widget_id===e);return t>=0?this.widgetConfig[t]:{}}getMyTeam(){this._http.post("/api/employee360/organizationDetails/getTeamDetails",{}).subscribe(e=>{if(!0!==e.error){this.myTeam=e.data;let t=[];for(;0!=e.data.length;)e.data.push(...e.data[0].team),t.push(e.data.shift().id);this.myTeam_id=t}})}getMyTeamActivities(e){return this._http.post("/api/activity/getMyTeamActivity",{filter_config:e})}getMyTeamCalls(e){return this._http.post("/api/activity/getMyTeamCallLogs",{filter_config:e})}getMyTeamMails(e){return this._http.post("/api/activity/getMyTeamMails",{filter_config:e})}getOBVTrendReport(e,t,n,i){return this._obv_summary_subject.next({}),this._http.post("/api/opportunity/getOBVTrendReport",{orgCodes:e,filter_config:t,filterStartDate:n,filterEndDate:i})}getPipelineGrowthBySalesOwner(e,t,n){return this._pipeline_owner_subject.next({}),this._http.post("/api/opportunity/getPipelineGrowthBySalesOwner",{filter_config:e,duration:t,limit:n})}getPipelineGrowthByStatus(e,t,n){return this._pipeline_owner_subject.next({}),this._http.post("/api/opportunity/getPipelineGrowthByStatus",{filter_config:e,duration:t,status_config:n})}getPipelineGrowthByStatusSummary(e){return this._http.post("/api/opportunity/getPipelineGrowthByStatusSummary",{opportunity_ids:e})}getFTETrendReport(e,t,n,i){return this._fte_summary_subject.next({}),this._http.post("/api/opportunity/getFTETrendReport",{orgCodes:e,filter_config:t,filterStartDate:n,filterEndDate:i})}getBillingCollectionForDashboard(e,t,n){return this._billing_collection_summary_subject.next({}),this._http.post("/api/opportunity/getBillingCollectionForDashboard",{orgCodes:this.roleService.getUserRoleOrgCodes("Opportunities"),filter_config:e,filterStartDate:t,filterEndDate:n})}getRevenueForDashboard(e){return this._http.post("/api/opportunity/getRevenueForDashboard",{filter_config:e})}getRecentActivities(e,t=6,n){return this._http.post("/api/activity/getRecentActivities",{sales_activity_type:e,limit:t,org_codes:this.roleService.getUserRoleOrgCodes("Opportunities"),filter_config:n})}getPieChartPaletteForActivity(e){return this._http.post("/api/activity/getPieChartPaletteForActivity",{sales_activity_type:e})}getTotalActivities(e){return this._http.post("/api/activity/getTotalActivitiesForDashboard",{sales_activity_type:e})}getStatusForOpportunity(e,t){return this._opp_status_summary_subject.next({}),this._http.post("/api/opportunity/getStatusForDashboard",{orgCodes:e,filterConfig:t})}updateWidgetStatusConfig(e,t){return this._http.post("/api/opportunity/updateWidgetStatusConfig",{status_config:e,widget_id:t})}getSalesStatusMaster(){return this._http.post("/api/salesMaster/getSalesStatusMaster",{})}getSalesOwner(){return this._http.post("/api/opportunity/getReportFilterOwnerInOpportunity",{owner:"opportunity_owner_oid"})}getSalesProbability(){return this._http.post("/api/salesMaster/getProbabilityMaster",{applicationId:36})}getShiftList(){return this._http.post("/api/salesMaster/getShiftList",{})}get probability_summary_data(){return this._probability_summary_subject.getValue()}set probability_summary_data(e){this._probability_summary_subject.next(e)}getProbabilityDataSummary(e,t=[]){this.probability_summary_data[e]||this._http.post("/api/opportunity/getProbabilityDataSummary",{probability:e,org_codes:this.roleService.getUserRoleOrgCodes("Opportunities"),filter_config:t}).subscribe(t=>{let n=this.probability_summary_data;n[e]=t,this._probability_summary_subject.next(n)})}get pipeline_summary_data(){return this._pipeline_summary_subject.getValue()}set pipeline_summary_data(e){this._pipeline_summary_subject.next(e)}getPipelineDataSummary(e,t=[]){this.pipeline_summary_data[e]||this._http.post("/api/opportunity/getPipelineDataSummary",{pipeline:e,org_codes:this.roleService.getUserRoleOrgCodes("Opportunities"),filter_config:t}).subscribe(t=>{let n=this.pipeline_summary_data;n[e]=t,this._pipeline_summary_subject.next(n)})}get opp_status_summary_data(){return this._opp_status_summary_subject.getValue()}getOppStatusDataSummary(e,t){this.opp_status_summary_data[e]||this._http.post("/api/opportunity/getOppStatusDataSummary",{status:e,org_codes:this.roleService.getUserRoleOrgCodes("Opportunities"),filter_config:t}).subscribe(t=>{let n=this.opp_status_summary_data;n[e]=t,this._opp_status_summary_subject.next(n)})}get tasks_summary_data(){return this._tasks_summary_subject.getValue()}getTasksDataSummary(e){this.tasks_summary_data[e]||this._http.post("/api/activity/getTasksDataSummary",{assigned_to:e,created_by:this.myTeam_id.includes(e)?[]:this.myTeam_id}).subscribe(t=>{let n=this.tasks_summary_data;n[e]=t,this._tasks_summary_subject.next(n)})}get obv_summary_data(){return this._obv_summary_subject.getValue()}getOBVDataSummary(e,t,n,i=!1,a,o){this._http.post("/api/opportunity/getOBVDataSummary",{filter_config:n,status:t,org_codes:this.roleService.getUserRoleOrgCodes("Opportunities"),date:e,isLast:i,startDate:a,endDate:o}).subscribe(t=>{let n=this.obv_summary_data;n[e]=t,this._obv_summary_subject.next(n)})}get pipeline_owner_summay_data(){return this._pipeline_owner_subject.getValue()}getPipelineGrowthSummary(e,t){this._http.post("/api/opportunity/getPipelineGrowthBySalesOwnerSummary",{opportunity_ids:e}).subscribe(e=>{let n=this.pipeline_owner_summay_data;n[t]=e.messData,this._pipeline_owner_subject.next(n)})}get calls_summary_data(){return this._calls_summary_subject.getValue()}getCallsDataSummary(e){this.calls_summary_data[e]||this._http.post("/api/activity/getCallsDataSummary",{organizer_oid:e,created_by:this.myTeam_id.includes(e)?[]:this.myTeam_id}).subscribe(t=>{let n=this.calls_summary_data;n[e]=t,this._calls_summary_subject.next(n)})}get mails_summary_data(){return this._mails_summary_subject.getValue()}getMailsDataSummary(e){this.mails_summary_data[e]||this._http.post("/api/activity/getMailsDataSummary",{organizer_oid:e,created_by:this.myTeam_id.includes(e)?[]:this.myTeam_id}).subscribe(t=>{let n=this.mails_summary_data;n[e]=t,this._mails_summary_subject.next(n)})}getQuoteDiscrepencies(){return this._http.post("/api/qb/quote/getQuoteDiscrepencies",{orgCode:this.roleService.getUserRoleOrgCodes("Opportunities")})}getQuoteRevCostGmTrend(e,t,n,i,a,o,r,l,s,c){return this._qrcg_summary_subject.next({}),this._http.post("/api/qb/quote/getQuoteRevCostGmTrend",{orgCode:this.roleService.getUserRoleOrgCodes("Opportunities"),filterConfig:e,filterStartDate:t,filterEndDate:n,status:i,serviceLine:a,entity:o,division:r,subDivision:l,positionStartDate:s,positionEndDate:c})}get qrcg_summary_data(){return this._qrcg_summary_subject.getValue()}getQuoteRevCostGmTrendDataSummary(e,t,n,i,a,o,r){this._http.post("/api/qb/quote/getQuoteRevCostGmTrendDataSummary",{orgCode:this.roleService.getUserRoleOrgCodes("Opportunities"),filterConfig:t,month:e,status:n,serviceLine:i,entity:a,division:o,subDivision:r}).subscribe(t=>{let n=this.qrcg_summary_data;n[e]=t.data,this._qrcg_summary_subject.next(n)})}get fte_summary_data(){return this._fte_summary_subject.getValue()}getFTEDataSummary(e,t,n,i=!1){this._http.post("/api/opportunity/getFTEDataSummary",{filter_config:n,status:t,org_codes:this.roleService.getUserRoleOrgCodes("Opportunities"),date:e,isLast:i}).subscribe(t=>{let n=this.fte_summary_data;n[e]=t,this._fte_summary_subject.next(n)})}getFinanceDataForDashboard(e,t,n,i,a,o){return this._finance_data_summary_subject.next({}),this._http.post("/api/qb/quote/getFinanceDataForDashboard",{widgetId:e,orgCode:t,filterConfig:n,filterStartDate:i,filterEndDate:a,statusFilter:o})}get finance_data_summary_data(){return this._finance_data_summary_subject.getValue()}getFinanceDataForDashboardSummary(e,t,n,i,a,o,r){this._http.post("/api/qb/quote/getFinanceDataForDashboardSummary",{widgetId:e,selectedId:t,orgCode:this.roleService.getUserRoleOrgCodes("Opportunities"),filterConfig:n,filterStartDate:i,filterEndDate:a,statusFilter:r}).subscribe(e=>{let t=this.finance_data_summary_data;t[o]=e.data,this._finance_data_summary_subject.next(t)})}getEntity(){return this._http.post("/api/qb/master/getEntity",{})}getDivision(){return this._http.post("/api/qb/master/getDivision",{})}getSubDivision(){return this._http.post("/api/qb/master/getSubDivision",{})}getServices(){return this._http.post("/api/qb/serviceConfig/getServices",{activeService:!0})}get billing_collection_data(){return this._billing_collection_summary_subject.getValue()}getBillingCollectionForDashboardSummary(e,t,n,i){this._http.post("/api/opportunity/getBillingCollectionForDashboardSummary",{filterStartDate:e,filterEndDate:t,isBilling:n}).subscribe(e=>{let t=this.billing_collection_data;t[i]=e,this._billing_collection_summary_subject.next(t)})}getUpcomingContractClosureData(e){return this._http.post("/api/project/getItemEndingAlertForBot",{month:e})}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275inject"](h.c),d["\u0275\u0275inject"](_.a))},e.\u0275prov=d["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var y=n("Qu3c"),C=n("NFeN");let x=(()=>{class e{constructor(e,t){this.container=e,this.factory=t}ngAfterViewInit(){console.log(this.cellComponent),console.log(this.container);const e=d.Injector.create({providers:[{provide:"ROW_DATA",useValue:this.cellComponent.data}].concat(this.cellComponent.col.providers?this.cellComponent.col.providers:[])}),t=this.factory.resolveComponentFactory(this.cellComponent.col.component);this.container.createComponent(t,0,e)}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](d.ViewContainerRef),d["\u0275\u0275directiveInject"](d.ComponentFactoryResolver))},e.\u0275dir=d["\u0275\u0275defineDirective"]({type:e,selectors:[["","cellComponent",""]],inputs:{cellComponent:"cellComponent"}}),e})();const b=function(e){return{"padding-left":e}};function w(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"th",0),d["\u0275\u0275elementStart"](1,"p"),d["\u0275\u0275text"](2),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275property"]("ngStyle",d["\u0275\u0275pureFunction1"](2,b,e.gutter_space)),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate"](e.column_name)}}const S=function(e,t){return{rowData:e,colConfig:t}},O=function(e,t){return{color:e,"font-size":t}};function E(e,t){if(1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275elementStart"](1,"p",8),d["\u0275\u0275text"](2),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementContainerEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]().$implicit,t=d["\u0275\u0275nextContext"]().$implicit,n=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("matTooltip",n.formatCell(d["\u0275\u0275pureFunction2"](4,S,t,e)))("ngClass",e.text_overflow?"overflow":"")("ngStyle",d["\u0275\u0275pureFunction2"](7,O,null!=e.property_field_binding&&e.property_field_binding.text_color_field?t[e.property_field_binding.text_color_field]:e.text_color?e.text_color:"#45546E",null!=e.property_field_binding&&e.property_field_binding.font_size_field?t[e.property_field_binding.font_size_field]:e.font_size?e.font_size:"14px")),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",n.formatCell(d["\u0275\u0275pureFunction2"](10,S,t,e))," ")}}function D(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"mat-icon",10),d["\u0275\u0275listener"]("click",(function(n){d["\u0275\u0275restoreView"](e);const i=t.$implicit,a=d["\u0275\u0275nextContext"](3).$implicit;return d["\u0275\u0275nextContext"](2).onActionClick({rowData:a,actionData:i},n)})),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=d["\u0275\u0275nextContext"](2).$implicit,i=d["\u0275\u0275nextContext"]().$implicit;d["\u0275\u0275property"]("matTooltip",e.action_name)("ngStyle",d["\u0275\u0275pureFunction2"](3,O,null!=n.property_field_binding&&n.property_field_binding.text_color_field?i[n.property_field_binding.text_color_field]:n.text_color?n.text_color:"#45546E",null!=n.property_field_binding&&n.property_field_binding.font_size_field?i[n.property_field_binding.font_size_field]:n.font_size?n.font_size:"14px")),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",e.action_icon," ")}}function M(e,t){if(1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275template"](1,D,2,6,"mat-icon",9),d["\u0275\u0275elementContainerEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]().$implicit;d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngForOf",e.actions)}}function P(e,t){1&e&&d["\u0275\u0275elementContainer"](0)}const I=function(e){return{$implicit:e}};function F(e,t){if(1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275template"](1,P,1,0,"ng-container",11),d["\u0275\u0275elementContainerEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]().$implicit,t=d["\u0275\u0275nextContext"]().$implicit,n=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngTemplateOutlet",e.template_ref)("ngTemplateOutletContext",n.getContextForTemplate(d["\u0275\u0275pureFunction1"](2,I,t),e.context))}}function T(e,t){}const k=function(e,t){return{col:e,data:t}};function L(e,t){if(1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275template"](1,T,0,0,"ng-template",12),d["\u0275\u0275elementContainerEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]().$implicit,t=d["\u0275\u0275nextContext"]().$implicit;d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("cellComponent",d["\u0275\u0275pureFunction2"](1,k,e,t))}}function Y(e,t){if(1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275elementStart"](1,"p",8),d["\u0275\u0275text"](2),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementContainerEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]().$implicit,t=d["\u0275\u0275nextContext"]().$implicit,n=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("matTooltip",n.formatDate(d["\u0275\u0275pureFunction2"](4,S,t,e)))("ngClass",e.text_overflow?"overflow":"")("ngStyle",d["\u0275\u0275pureFunction2"](7,O,null!=e.property_field_binding&&e.property_field_binding.text_color_field?t[e.property_field_binding.text_color_field]:e.text_color?e.text_color:"#45546E",null!=e.property_field_binding&&e.property_field_binding.font_size_field?t[e.property_field_binding.font_size_field]:e.font_size?e.font_size:"14px")),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",n.formatDate(d["\u0275\u0275pureFunction2"](10,S,t,e))," ")}}const A=function(e,t){return{"padding-left":e,"max-width":"214px","border-bottom":t}};function R(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"td",7),d["\u0275\u0275listener"]("click",(function(n){d["\u0275\u0275restoreView"](e);const i=t.$implicit,a=d["\u0275\u0275nextContext"]().$implicit;return d["\u0275\u0275nextContext"](2).onCellClick({rowData:a,colConfig:i},n)})),d["\u0275\u0275template"](1,E,3,13,"ng-container",2),d["\u0275\u0275template"](2,M,2,1,"ng-container",2),d["\u0275\u0275template"](3,F,2,4,"ng-container",2),d["\u0275\u0275template"](4,L,2,4,"ng-container",2),d["\u0275\u0275template"](5,Y,3,13,"ng-container",2),d["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=d["\u0275\u0275nextContext"](3);d["\u0275\u0275property"]("ngStyle",d["\u0275\u0275pureFunction2"](6,A,e.gutter_space,n.tableConfig.cell_border_bottom)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","text"==e.column_type),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","actions"==e.column_type),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","template"==e.column_type),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","component"==e.column_type),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","date"==e.column_type)}}function N(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"tr",5),d["\u0275\u0275listener"]("click",(function(){d["\u0275\u0275restoreView"](e);const n=t.$implicit;return d["\u0275\u0275nextContext"](2).onRowClick(n)})),d["\u0275\u0275template"](1,R,6,9,"td",6),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngForOf",e.tableColumns)}}function V(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"tbody"),d["\u0275\u0275template"](1,N,2,1,"tr",4),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngForOf",e.tableContent)}}function z(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",13),d["\u0275\u0275text"](1," No Data "),d["\u0275\u0275elementEnd"]())}const j=function(e,t,n){return{"table-layout":e,width:t,color:n}};let B=(()=>{class e{constructor(){}ngOnInit(){this.tableColumns=this.tableConfig.columnConfig,console.log(this.tableContent)}getContextForTemplate(e,t){return Object.assign(e,t)}onCellClick(e,t){console.log(e);try{e.colConfig.onCellClick({cell:e.rowData[e.colConfig.data_field],row:e.rowData}),t.stopPropagation()}catch(n){return}}onRowClick(e){try{console.log(this.tableConfig),this.tableConfig.onRowClick(e)}catch(t){return}}onActionClick(e,t){try{e.actionData.action_click(e.rowData),t.stopPropagation()}catch(n){return}}formatCell(e){try{return e.colConfig.formatcell({cellData:e.rowData[e.colConfig.data_field],rowData:e.rowData})||"-"}catch(t){return e.rowData[e.colConfig.data_field]?e.rowData[e.colConfig.data_field]:"-"}}formatDate(e){try{let t=l(e.rowData[e.colConfig.data_field]).utc().format("DD-MMM-YYYY");return t&&"Invalid date"!=t?t:"-"}catch(t){return"-"}}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["data-table"]],inputs:{tableConfig:"tableConfig",tableContent:"tableContent"},decls:6,vars:8,consts:[[3,"ngStyle"],[3,"ngStyle",4,"ngFor","ngForOf"],[4,"ngIf"],["class","no-data",4,"ngIf"],["style","cursor: pointer;",3,"click",4,"ngFor","ngForOf"],[2,"cursor","pointer",3,"click"],[3,"ngStyle","click",4,"ngFor","ngForOf"],[3,"ngStyle","click"],[1,"p-0",2,"margin-top","5px","margin-bottom","5px",3,"matTooltip","ngClass","ngStyle"],["class","p-0 row ","style","padding-top:6px!important",3,"matTooltip","ngStyle","click",4,"ngFor","ngForOf"],[1,"p-0","row",2,"padding-top","6px!important",3,"matTooltip","ngStyle","click"],[4,"ngTemplateOutlet","ngTemplateOutletContext"],[3,"cellComponent"],[1,"no-data"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"table",0),d["\u0275\u0275elementStart"](1,"thead"),d["\u0275\u0275elementStart"](2,"tr"),d["\u0275\u0275template"](3,w,3,4,"th",1),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](4,V,2,1,"tbody",2),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](5,z,2,0,"div",3)),2&e&&(d["\u0275\u0275property"]("ngStyle",d["\u0275\u0275pureFunction3"](4,j,t.tableConfig.layout,t.tableConfig.width?t.tableConfig.width:"100%",t.tableConfig.color?t.tableConfig.color:"#45546E")),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngForOf",t.tableColumns),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.tableContent.length>0),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.tableContent.length<=0))},directives:[i.NgStyle,i.NgForOf,i.NgIf,y.a,i.NgClass,C.a,i.NgTemplateOutlet,x],styles:["th[_ngcontent-%COMP%]{font-weight:400;font-size:11px;line-height:16px;letter-spacing:.02em;text-transform:uppercase;color:#b9c0ca}table[_ngcontent-%COMP%]{overflow:hidden;color:#45546e}.overflow[_ngcontent-%COMP%]{text-overflow:ellipsis;overflow:hidden;white-space:nowrap}.gutter[_ngcontent-%COMP%]{padding:0 70px 0 0xp}tr[_ngcontent-%COMP%]{animation:fade-in-bottom .6s cubic-bezier(.39,.575,.565,1) both}@keyframes fade-in-bottom{0%{transform:translateY(50px);opacity:0}to{transform:translateY(0);opacity:1}}.no-data[_ngcontent-%COMP%]{display:flex;height:80%;font-size:small;vertical-align:center;justify-content:center;align-items:center;width:100%}"]}),e})();var $=n("ZzPI"),U=n("6t9p"),Q=n("PVOt");function q(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",11),d["\u0275\u0275elementStart"](1,"div",12),d["\u0275\u0275text"](2,"Loading..."),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function W(e,t){if(1&e&&d["\u0275\u0275element"](0,"data-table",13),2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275property"]("tableConfig",e.tableConfig)("tableContent",e.tableContent)}}function G(e,t){if(1&e&&d["\u0275\u0275element"](0,"dxi-column",21),2&e){const e=t.$implicit;d["\u0275\u0275property"]("dataField",e.data_field)("caption",e.column_name)("dataType","date"==e.column_type?"date":"currency"==e.column_type?"number":"")("format","date"==e.column_type?"dd-MMM yyyy":"")}}const H=function(e,t){return{rowData:e,colConfig:t}},J=function(e,t){return{color:e,"font-size":t}};function K(e,t){if(1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275elementStart"](1,"p",23),d["\u0275\u0275text"](2),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementContainerEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]().$implicit,t=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("matTooltip",t.formatCell(d["\u0275\u0275pureFunction2"](4,H,e.data,e.data.colConfig[e.column.caption])))("ngClass",e.data.colConfig[e.column.caption].text_overflow?"overflow":"")("ngStyle",d["\u0275\u0275pureFunction2"](7,J,null!=e.data.colConfig[e.column.caption].property_field_binding&&e.data.colConfig[e.column.caption].property_field_binding.text_color_field?e.data[e.data.colConfig[e.column.caption].property_field_binding.text_color_field]:e.data.colConfig[e.column.caption].text_color?e.data.colConfig[e.column.caption].text_color:"#45546E",null!=e.data.colConfig[e.column.caption].property_field_binding&&e.data.colConfig[e.column.caption].property_field_binding.font_size_field?e.data[e.data.colConfig[e.column.caption].property_field_binding.font_size_field]:e.data.colConfig[e.column.caption].font_size?t.colConfig.font_size:"14px")),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",t.formatCell(d["\u0275\u0275pureFunction2"](10,H,e.data,e.data.colConfig[e.column.caption]))," ")}}function X(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"mat-icon",25),d["\u0275\u0275listener"]("click",(function(n){d["\u0275\u0275restoreView"](e);const i=t.$implicit,a=d["\u0275\u0275nextContext"](2).$implicit;return d["\u0275\u0275nextContext"](2).onActionClick({rowData:a.data,actionData:i},n)})),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=d["\u0275\u0275nextContext"](2).$implicit,i=d["\u0275\u0275nextContext"](2);d["\u0275\u0275property"]("matTooltip",e.action_name)("ngStyle",d["\u0275\u0275pureFunction2"](3,J,null!=n.data.colConfig[n.column.caption].property_field_binding&&n.data.colConfig[n.column.caption].property_field_binding.text_color_field?n.data[n.data.colConfig[n.column.caption].property_field_binding.text_color_field]:n.data.colConfig[n.column.caption].text_color?n.data.colConfig[n.column.caption].text_color:"#45546E",null!=n.data.colConfig[n.column.caption].property_field_binding&&n.data.colConfig[n.column.caption].property_field_binding.font_size_field?n.data[n.data.colConfig[n.column.caption].property_field_binding.font_size_field]:n.data.colConfig[n.column.caption].font_size?i.colConfig.font_size:"14px")),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",e.action_icon," ")}}function Z(e,t){if(1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275template"](1,X,2,6,"mat-icon",24),d["\u0275\u0275elementContainerEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]().$implicit;d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngForOf",e.data.colConfig[e.column.caption].actions)}}function ee(e,t){1&e&&d["\u0275\u0275elementContainer"](0)}const te=function(e){return{$implicit:e}};function ne(e,t){if(1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275template"](1,ee,1,0,"ng-container",26),d["\u0275\u0275elementContainerEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]().$implicit;d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngTemplateOutlet",e.data.colConfig[e.column.caption].template_ref)("ngTemplateOutletContext",d["\u0275\u0275pureFunction1"](2,te,e.data))}}function ie(e,t){}const ae=function(e,t){return{col:e,data:t}};function oe(e,t){if(1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275template"](1,ie,0,0,"ng-template",27),d["\u0275\u0275elementContainerEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]().$implicit;d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("cellComponent",d["\u0275\u0275pureFunction2"](1,ae,e.data.colConfig[e.column.caption],e))}}function re(e,t){if(1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275elementStart"](1,"p",23),d["\u0275\u0275text"](2),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementContainerEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]().$implicit,t=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("matTooltip",t.formatDate(d["\u0275\u0275pureFunction2"](4,H,e.data,e.data.colConfig[e.column.caption])))("ngClass",e.data.colConfig[e.column.caption].text_overflow?"overflow":"")("ngStyle",d["\u0275\u0275pureFunction2"](7,J,null!=e.data.colConfig[e.column.caption].property_field_binding&&e.data.colConfig[e.column.caption].property_field_binding.text_color_field?e.data[e.data.colConfig[e.column.caption].property_field_binding.text_color_field]:e.data.colConfig[e.column.caption].text_color?e.data.colConfig[e.column.caption].text_color:"#45546E",null!=e.data.colConfig[e.column.caption].property_field_binding&&e.data.colConfig[e.column.caption].property_field_binding.font_size_field?e.data[e.data.colConfig[e.column.caption].property_field_binding.font_size_field]:e.data.colConfig[e.column.caption].font_size?t.colConfig.font_size:"14px")),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",t.formatDate(d["\u0275\u0275pureFunction2"](10,H,e.data,e.data.colConfig[e.column.caption]))," ")}}const le=function(e,t){return{rowData:e,colConfig:t,displayCurrency:!1,isRoundOff:!1}},se=function(e,t){return{rowData:e,colConfig:t,displayCurrency:!1,isRoundOff:!0}};function ce(e,t){if(1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275elementStart"](1,"p",23),d["\u0275\u0275text"](2),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementContainerEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]().$implicit,t=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("matTooltip",t.formatCurrency(d["\u0275\u0275pureFunction2"](4,le,e.data,e.data.colConfig[e.column.caption])))("ngClass",e.data.colConfig[e.column.caption].text_overflow?"overflow":"")("ngStyle",d["\u0275\u0275pureFunction2"](7,J,null!=e.data.colConfig[e.column.caption].property_field_binding&&e.data.colConfig[e.column.caption].property_field_binding.text_color_field?e.data[e.data.colConfig[e.column.caption].property_field_binding.text_color_field]:e.data.colConfig[e.column.caption].text_color?e.data.colConfig[e.column.caption].text_color:"#45546E",null!=e.data.colConfig[e.column.caption].property_field_binding&&e.data.colConfig[e.column.caption].property_field_binding.font_size_field?e.data[e.data.colConfig[e.column.caption].property_field_binding.font_size_field]:e.data.colConfig[e.column.caption].font_size?t.colConfig.font_size:"14px")),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",t.formatCurrency(d["\u0275\u0275pureFunction2"](10,se,e.data,e.data.colConfig[e.column.caption]))," ")}}function de(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275template"](1,K,3,13,"ng-container",22),d["\u0275\u0275template"](2,Z,2,1,"ng-container",22),d["\u0275\u0275template"](3,ne,2,4,"ng-container",22),d["\u0275\u0275template"](4,oe,2,4,"ng-container",22),d["\u0275\u0275template"](5,re,3,13,"ng-container",22),d["\u0275\u0275template"](6,ce,3,13,"ng-container",22),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","text"==e.data.colConfig[e.column.caption].column_type),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","actions"==e.data.colConfig[e.column.caption].column_type),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","template"==e.data.colConfig[e.column.caption].column_type),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","component"==e.data.colConfig[e.column.caption].column_type),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","date"==e.data.colConfig[e.column.caption].column_type),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","currency"==e.data.colConfig[e.column.caption].column_type)}}function pe(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"dx-data-grid",14),d["\u0275\u0275listener"]("onExporting",(function(t){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().onExporting(t)})),d["\u0275\u0275element"](1,"dxo-sorting",15),d["\u0275\u0275element"](2,"dxo-header-filter",16),d["\u0275\u0275element"](3,"dxo-scrolling",17),d["\u0275\u0275element"](4,"dxo-export",18),d["\u0275\u0275template"](5,G,1,4,"dxi-column",19),d["\u0275\u0275template"](6,de,7,6,"div",20),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275property"]("dataSource",e.tableContent)("allowColumnReordering",!0)("allowColumnResizing",!0)("columnAutoWidth",!0)("showBorders",!0),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("visible",!0),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("enabled",!0),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngForOf",e.tableConfig.columnConfig),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("dxTemplateOf","cellTemplate")}}const me=function(e){return{"background-color":e}};let ge=(()=>{class e{constructor(e,t,n){this.summaryData=e,this.salesDashboardService=t,this.commaSep=n,this.title="",this.subtitle="",this.subtitle1="",this.headerBg="#FFF3E8",this.isLoading=!0,this.useDxTable=!1}ngOnInit(){this.useDxTable=!!this.summaryData.useDxTable,this.tableConfig={onRowClick:e=>{try{console.log("RE"),this.summaryData.onRowClick(e)}catch(t){return void console.log(t)}},columnConfig:this.summaryData.columnConfig,cell_border_bottom:this.summaryData.noBorder?"none":"solid 1px #E8E9EE",border_collapse:"collapse",layout:"fixed"},this.isLoading=!this.tableContent,this.salesDashboardService[this.summaryData.dataObservername].subscribe(e=>{this.tableContent=e[this.summaryData.summaryFor],this.tableContent&&this.tableContent.map(e=>e.colConfig=r.indexBy(this.summaryData.columnConfig,"column_name")),this.isLoading=!this.tableContent}),this.title=this.summaryData.title,this.subtitle=this.summaryData.subtitle,this.subtitle1=this.summaryData.subtitle1,this.headerBg=this.summaryData.headerBG}onActionClick(e,t){try{e.actionData.action_click(e.rowData),t.stopPropagation()}catch(n){return}}formatCell(e){try{return e.colConfig.formatcell({cellData:e.rowData[e.colConfig.data_field],rowData:e.rowData})||"-"}catch(t){return e.rowData[e.colConfig.data_field]?e.rowData[e.colConfig.data_field]:"-"}}formatDate(e){try{let t=l(e.rowData[e.colConfig.data_field]).utc().format("DD-MMM-YYYY");return t&&"Invalid date"!=t?t:"-"}catch(t){return"-"}}formatCurrency(e){try{return this.commaSep.transform(e.rowData[e.colConfig.data_field],e.colConfig.currency_value,e.displayCurrency,e.isRoundOff)||"-"}catch(t){return"-"}}onExporting(e){e.fileName=this.title}ngOnDestroy(){}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](p.a),d["\u0275\u0275directiveInject"](v),d["\u0275\u0275directiveInject"](g))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-summary-table"]],features:[d["\u0275\u0275ProvidersFeature"]([{provide:m.h},g])],decls:16,vars:13,consts:[[1,"card-container"],[1,"card-header","row","justify-content-between","align-items-center",3,"ngStyle"],[1,"title"],[2,"display","flex"],[1,"subtitle"],[1,"subtitle","ml-4"],["matDialogClose","",2,"font-size","small","font-weight","500"],[1,"card-body"],["class","loading-wrapper",4,"ngIf"],[3,"tableConfig","tableContent",4,"ngIf"],["class","data-grid","style","height:100% !important","columnResizingMode","widget",3,"dataSource","allowColumnReordering","allowColumnResizing","columnAutoWidth","showBorders","onExporting",4,"ngIf"],[1,"loading-wrapper"],[1,"loading"],[3,"tableConfig","tableContent"],["columnResizingMode","widget",1,"data-grid",2,"height","100% !important",3,"dataSource","allowColumnReordering","allowColumnResizing","columnAutoWidth","showBorders","onExporting"],["mode","single"],[3,"visible"],["mode","infinite"],[3,"enabled"],["alignment","left","cellTemplate","cellTemplate",3,"dataField","caption","dataType","format",4,"ngFor","ngForOf"],[4,"dxTemplate","dxTemplateOf"],["alignment","left","cellTemplate","cellTemplate",3,"dataField","caption","dataType","format"],[4,"ngIf"],[1,"p-0",2,"margin-top","5px","margin-bottom","5px",3,"matTooltip","ngClass","ngStyle"],["class","p-0 row ","style","padding-top:6px!important",3,"matTooltip","ngStyle","click",4,"ngFor","ngForOf"],[1,"p-0","row",2,"padding-top","6px!important",3,"matTooltip","ngStyle","click"],[4,"ngTemplateOutlet","ngTemplateOutletContext"],[3,"cellComponent"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",0),d["\u0275\u0275elementStart"](1,"div",1),d["\u0275\u0275elementStart"](2,"div"),d["\u0275\u0275elementStart"](3,"p",2),d["\u0275\u0275text"](4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](5,"div",3),d["\u0275\u0275elementStart"](6,"p",4),d["\u0275\u0275text"](7),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](8,"p",5),d["\u0275\u0275text"](9),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](10,"span",6),d["\u0275\u0275text"](11,"Close"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](12,"div",7),d["\u0275\u0275template"](13,q,3,0,"div",8),d["\u0275\u0275template"](14,W,1,2,"data-table",9),d["\u0275\u0275template"](15,pe,7,9,"dx-data-grid",10),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e&&(d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngStyle",d["\u0275\u0275pureFunction1"](11,me,t.headerBg||"#FFF3E8")),d["\u0275\u0275advance"](3),d["\u0275\u0275textInterpolate"](t.title),d["\u0275\u0275advance"](3),d["\u0275\u0275textInterpolate"](t.subtitle),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate"](t.subtitle1),d["\u0275\u0275advance"](3),d["\u0275\u0275styleProp"]("height",t.useDxTable?"80%":"0px")("overflow",t.useDxTable?"none":"scroll"),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.isLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!t.isLoading&&!t.useDxTable),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!t.isLoading&&t.useDxTable))},directives:[i.NgStyle,p.d,i.NgIf,B,$.a,U.ae,U.Cc,U.Jd,U.Sb,i.NgForOf,Q.d,U.g,y.a,i.NgClass,C.a,i.NgTemplateOutlet,x],styles:[".card-container[_ngcontent-%COMP%]{display:flex;flex-flow:column;height:100%;overflow-y:hidden}.card-container[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]{justify-self:flex-start;flex:0 1 auto;color:#45546e}.card-container[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:14px;font-weight:500;padding:0!important;margin-bottom:5px}.card-container[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .subtitle[_ngcontent-%COMP%]{font-size:12px;margin-bottom:0}.card-container[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]{flex:1 1 auto}[_nghost-%COMP%]     .dx-freespace-row{height:0!important}.data-grid[_ngcontent-%COMP%]     .dx-toolbar .dx-toolbar-items-container{height:40px!important}"]}),e})();var ue=n("LcQX"),fe=n("dg+m"),he=n("dNgK"),_e=n("1A3m"),ve=n("Wp6s"),ye=n("VI6+"),Ce=n("H44p"),xe=n("bTqV"),be=n("jtHE"),we=n("NJ67"),Se=n("1G5W"),Oe=n("kmnG"),Ee=n("d3UM"),De=n("FKr1"),Me=n("WJ5W");const Pe=["allSelected"],Ie=["singleSelect"];function Fe(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"mat-label"),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate"](e.placeholder)}}function Te(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"mat-option",8),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=d["\u0275\u0275nextContext"]();d["\u0275\u0275property"]("value",e[n.idKey]),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",e[n.valueKey]," ")}}let ke=(()=>{class e extends we.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new c.j,this.fieldFilterCtrl=new c.j,this.showLabel=!1,this.list=[],this.required=!1,this.idKey="id",this.valueKey="name",this.valueChange=new d.EventEmitter,this.disabled=!1,this.filteredList=new be.a,this._onDestroy=new u.b}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(Se.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(Se.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){null!=this.list&&this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>{var n,i;return(null===(i=null===(n=t[this.valueKey])||void 0===n?void 0:n.toLowerCase())||void 0===i?void 0:i.indexOf(e))>-1}))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){console.log(e),this.fieldCtrl.setValue(e)}toggleAllSelection(){this.allSelected.selected&&this.fieldCtrl.patchValue([])}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](d.Renderer2))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-multi-select-search2"]],viewQuery:function(e,t){if(1&e&&(d["\u0275\u0275viewQuery"](Pe,!0),d["\u0275\u0275viewQuery"](Ie,!0)),2&e){let e;d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.allSelected=e.first),d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.singleSelect=e.first)}},inputs:{showLabel:"showLabel",list:"list",placeholder:"placeholder",required:"required",idKey:"idKey",valueKey:"valueKey",disabled:"disabled"},outputs:{valueChange:"valueChange"},features:[d["\u0275\u0275ProvidersFeature"]([{provide:c.t,useExisting:Object(d.forwardRef)(()=>e),multi:!0}]),d["\u0275\u0275InheritDefinitionFeature"],d["\u0275\u0275NgOnChangesFeature"]],decls:11,vars:11,consts:[["appearance","outline",2,"width","100%"],[4,"ngIf"],["multiple","",3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value","click"],["allSelected",""],[3,"value",4,"ngFor","ngForOf"],[3,"value"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"mat-form-field",0),d["\u0275\u0275template"](1,Fe,2,1,"mat-label",1),d["\u0275\u0275elementStart"](2,"mat-select",2,3),d["\u0275\u0275elementStart"](4,"mat-option"),d["\u0275\u0275element"](5,"ngx-mat-select-search",4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](6,"mat-option",5,6),d["\u0275\u0275listener"]("click",(function(){return t.toggleAllSelection()})),d["\u0275\u0275text"](8,"None"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](9,Te,2,2,"mat-option",7),d["\u0275\u0275pipe"](10,"async"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e&&(d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.showLabel),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("value",0),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngForOf",d["\u0275\u0275pipeBind1"](10,9,t.filteredList)))},directives:[Oe.c,i.NgIf,Ee.c,c.v,c.k,c.F,De.p,Me.a,i.NgForOf,Oe.g],pipes:[i.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}"]}),e})();var Le=n("0EQZ"),Ye=n("FvrZ"),Ae=n("8yBR"),Re=n("STbY"),Ne=n("qFsG"),Ve=n("TU8p"),ze=n("bSwM");function je(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"mat-tree-node",16),d["\u0275\u0275element"](1,"button",17),d["\u0275\u0275elementStart"](2,"mat-checkbox",18),d["\u0275\u0275listener"]("change",(function(){d["\u0275\u0275restoreView"](e);const n=t.$implicit;return d["\u0275\u0275nextContext"](2).todoLeafItemSelectionToggle(n)})),d["\u0275\u0275text"](3),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=d["\u0275\u0275nextContext"](2);d["\u0275\u0275styleProp"]("display",n.showSelection&&!n.checklistSelection.isSelected(e)||n.filterLeafNode(e)?"none":"flex"),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("checked",n.checklistSelection.isSelected(e)),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate"](e.item[n.config.name])}}function Be(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"mat-tree-node",19),d["\u0275\u0275elementStart"](1,"button",20),d["\u0275\u0275elementStart"](2,"mat-icon",21),d["\u0275\u0275text"](3),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](4,"mat-checkbox",22),d["\u0275\u0275listener"]("change",(function(){d["\u0275\u0275restoreView"](e);const n=t.$implicit;return d["\u0275\u0275nextContext"](2).todoItemSelectionToggle(n)})),d["\u0275\u0275text"](5),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=d["\u0275\u0275nextContext"](2);d["\u0275\u0275styleProp"]("display",n.filterParentNode(e)||n.showSelection&&!n.descendantsAllSelected(e)&&!n.descendantsPartiallySelected(e)?"none":"flex"),d["\u0275\u0275advance"](1),d["\u0275\u0275attribute"]("aria-label","Toggle "+e.item[n.config.name]),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"](" ",n.treeControl.isExpanded(e)?"expand_more":"chevron_right"," "),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("checked",n.descendantsAllSelected(e))("indeterminate",n.descendantsPartiallySelected(e)),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate"](e.item[n.config.name])}}const $e="linear-gradient(270deg, #ef4a61 0%, #f27a6c 105.29%)",Ue=function(){return{background:$e,color:"white"}},Qe=function(){return{}},qe=function(){return{background:$e,"-webkit-background-clip":"text","-webkit-text-fill-color":"transparent"}};function We(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",5),d["\u0275\u0275listener"]("click",(function(e){return e.stopPropagation()})),d["\u0275\u0275elementStart"](1,"span",6),d["\u0275\u0275elementStart"](2,"span",7),d["\u0275\u0275elementStart"](3,"mat-icon",8),d["\u0275\u0275text"](4,"search"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](5,"input",9),d["\u0275\u0275listener"]("input",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().treeControl.expandAll()}))("ngModelChange",(function(t){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().searchString=t})),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](6,"mat-icon",10),d["\u0275\u0275listener"]("click",(function(){d["\u0275\u0275restoreView"](e);const t=d["\u0275\u0275nextContext"]();return t.showSelection=!t.showSelection,t.showSelection?t.treeControl.expandAll():""})),d["\u0275\u0275text"](7,"visibility"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](8,"mat-icon",11),d["\u0275\u0275listener"]("click",(function(){d["\u0275\u0275restoreView"](e);const t=d["\u0275\u0275nextContext"]();return t.isAllSelected?t.deselectAllNodes():t.selectAllNodes()})),d["\u0275\u0275text"](9),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](10,"div",12),d["\u0275\u0275elementStart"](11,"mat-tree",13),d["\u0275\u0275template"](12,je,4,4,"mat-tree-node",14),d["\u0275\u0275template"](13,Be,6,7,"mat-tree-node",15),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](5),d["\u0275\u0275property"]("ngModel",e.searchString),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("matBadge",e.checklistSelection.selected.length)("ngStyle",e.showSelection?d["\u0275\u0275pureFunction0"](9,Ue):d["\u0275\u0275pureFunction0"](10,Qe)),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("matTooltip",e.isAllSelected?"Deselect all":"Select all")("ngStyle",e.isAllSelected?d["\u0275\u0275pureFunction0"](11,qe):d["\u0275\u0275pureFunction0"](12,Qe)),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate"](e.isAllSelected?"check_box":"select_all"),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("dataSource",e.dataSource)("treeControl",e.treeControl),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("matTreeNodeDefWhen",e.hasChild)}}let Ge=(()=>{class e{constructor(){this.config={id:"id",children:"children",name:"name"},this.placeholder="",this.checklistData=[],this.appliedSelection=[],this.showSelection=!1,this.displayName="",this.flatNodeMap=new Map,this.searchString="",this.nestedNodeMap=new Map,this.selectedParent=null,this.newItemName="",this.checklistSelection=new Le.c(!0),this.getLevel=e=>e.level,this.isExpandable=e=>e.expandable,this.getChildren=e=>e[this.config.children],this.hasChild=(e,t)=>t.expandable,this.hasNoContent=(e,t)=>""===t.item,this.transformer=(e,t)=>{var n;const i=this.nestedNodeMap.get(e),a=i&&i.item===e.item?i:{};return a.item=e,a.level=t,a.expandable=!!(null===(n=e[this.config.children])||void 0===n?void 0:n.length),this.flatNodeMap.set(a,e),this.nestedNodeMap.set(e,a),a},this.treeFlattener=new Ae.d(this.transformer,this.getLevel,this.isExpandable,this.getChildren),this.treeControl=new Ye.j(this.getLevel,this.isExpandable),this.dataSource=new Ae.c(this.treeControl,this.treeFlattener)}get isAllSelected(){return this.treeControl.dataNodes.every(e=>this.checklistSelection.isSelected(e))}selectAllNodes(){this.checklistSelection.select(...this.treeControl.dataNodes)}deselectAllNodes(){this.checklistSelection.deselect(...this.treeControl.dataNodes)}ngOnInit(){this.dataSource.data=this.checklistData?[...this.checklistData]:[],this.checklistSelection.changed.subscribe(()=>{this.changed&&this.changed(this.checklistSelection.selected.map(e=>e.item[this.config.id]).filter(e=>!!e)),this.displayName=this.checklistSelection.selected.map(e=>e.item[this.config.name]).filter(e=>!!e).toString()})}ngOnChanges(e){}filterLeafNode(e){var t,n;return!!this.searchString&&-1===(null===(t=e.item[this.config.name])||void 0===t?void 0:t.toLowerCase().indexOf(null===(n=this.searchString)||void 0===n?void 0:n.toLowerCase()))}filterParentNode(e){var t,n;return!(!this.searchString||-1!==(null===(t=e.item[this.config.name])||void 0===t?void 0:t.toLowerCase().indexOf(null===(n=this.searchString)||void 0===n?void 0:n.toLowerCase()))||this.treeControl.getDescendants(e).some(e=>{var t,n;return-1!==(null===(t=e.item[this.config.name])||void 0===t?void 0:t.toLowerCase().indexOf(null===(n=this.searchString)||void 0===n?void 0:n.toLowerCase()))}))}descendantsAllSelected(e){const t=this.treeControl.getDescendants(e);return t.length>0&&this.checklistSelection.isSelected(e)?(this.checklistSelection.select(...t),!0):t.length>0&&t.every(e=>this.checklistSelection.isSelected(e))}descendantsPartiallySelected(e){return this.treeControl.getDescendants(e).some(e=>this.checklistSelection.isSelected(e))&&!this.descendantsAllSelected(e)}todoItemSelectionToggle(e){this.checklistSelection.toggle(e);const t=this.treeControl.getDescendants(e);this.checklistSelection.isSelected(e)?this.checklistSelection.select(...t):this.checklistSelection.deselect(...t),t.forEach(e=>this.checklistSelection.isSelected(e)),this.checkAllParentsSelection(e)}todoLeafItemSelectionToggle(e){this.checklistSelection.toggle(e),this.checkAllParentsSelection(e)}checkAllParentsSelection(e){let t=this.getParentNode(e);for(;t;)this.checkRootNodeSelection(t),t=this.getParentNode(t)}checkRootNodeSelection(e){const t=this.checklistSelection.isSelected(e),n=this.treeControl.getDescendants(e),i=n.length>0&&n.every(e=>this.checklistSelection.isSelected(e));t&&!i?this.checklistSelection.deselect(e):!t&&i&&this.checklistSelection.select(e)}getParentNode(e){const t=this.getLevel(e);if(t<1)return null;for(let n=this.treeControl.dataNodes.indexOf(e)-1;n>=0;n--){const e=this.treeControl.dataNodes[n];if(this.getLevel(e)<t)return e}return null}setDisabledState(e){}writeValue(e){e=Array.isArray(e)?e:[],this.checklistSelection.clear();let t=[];for(let n of e)t.push(...this.treeControl.dataNodes.filter(e=>e.item[this.config.id]===n&&!!e.item[this.config.id]));0!=t.length&&(this.checklistSelection.select(...t),console.log(this.checklistSelection))}registerOnChange(e){this.changed=e}registerOnTouched(e){this.touched=e}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-tree-select-search"]],inputs:{config:"config",placeholder:"placeholder",checklistData:"checklistData"},features:[d["\u0275\u0275ProvidersFeature"]([{provide:c.t,useExisting:Object(d.forwardRef)(()=>e),multi:!0}]),d["\u0275\u0275NgOnChangesFeature"]],decls:5,vars:4,consts:[["appearance","outline",2,"width","100%",3,"matMenuTriggerFor","click"],["matInput","","type","text","readonly","",2,"color","black","text-overflow","ellipsis","cursor","pointer",3,"placeholder","value"],[3,"overlapTrigger"],["treeSelect",""],[3,"matMenuContent"],[2,"min-width","112px","max-width","280px","overflow","auto","overflow-y","hidden","-webkit-overflow-scrolling","touch","padding-top","0","padding-bottom","0","max-height","256px","min-width","100%","border-radius","4px",3,"click"],[2,"display","flex","justify-content","center","align-items","center","padding-top","5px","position","sticky","top","0"],[2,"display","inline-flex","padding","5px"],[2,"font-size","medium","padding-top","3px","padding-right","16px"],["placeholder","search","matInput","",2,"border","solid 1px #434659","border-radius","3px","padding","3px 10px",3,"ngModel","input","ngModelChange"],["matTooltip","Show Selection","matBadgeSize","small",2,"border-radius","50%","padding","4px","font-size","medium","cursor","pointer","margin-right","10px",3,"matBadge","ngStyle","click"],[2,"border-radius","50%","cursor","pointer",3,"matTooltip","ngStyle","click"],[2,"max-height","215px","overflow-y","auto"],[3,"dataSource","treeControl"],["matTreeNodeToggle","","matTreeNodePadding","","matTreeNodePaddingIndent","20",3,"display",4,"matTreeNodeDef"],["matTreeNodePadding","","matTreeNodePaddingIndent","20",3,"display",4,"matTreeNodeDef","matTreeNodeDefWhen"],["matTreeNodeToggle","","matTreeNodePadding","","matTreeNodePaddingIndent","20"],["mat-icon-button","","disabled","",1,"button__arrow"],[1,"tree-selecct-checkbox",3,"checked","change"],["matTreeNodePadding","","matTreeNodePaddingIndent","20"],["mat-icon-button","","matTreeNodeToggle","",1,"button__arrow"],[1,"mat-icon-rtl-mirror"],[1,"tree-selecct-checkbox",3,"checked","indeterminate","change"]],template:function(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"mat-form-field",0),d["\u0275\u0275listener"]("click",(function(e){return e.preventDefault()})),d["\u0275\u0275element"](1,"input",1),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](2,"mat-menu",2,3),d["\u0275\u0275template"](4,We,14,13,"ng-template",4),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275reference"](3);d["\u0275\u0275property"]("matMenuTriggerFor",e),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("placeholder",t.placeholder)("value",t.displayName),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("overlapTrigger",!0)}},directives:[Oe.c,Re.f,Ne.b,Re.g,Re.c,C.a,c.e,c.v,c.y,y.a,Ve.a,i.NgStyle,Ae.b,Ae.h,Ae.g,Ae.k,Ae.j,xe.a,ze.a],styles:[".mat-badge-content[_ngcontent-%COMP%]{margin-right:0!important;font-size:8px!important}mat-tree-node[_ngcontent-%COMP%]{min-height:unset!important}  .tree-selecct-checkbox.mat-accent:not(.mat-checkbox-disabled).mat-checkbox-checked .mat-checkbox-background,   .tree-selecct-checkbox.mat-accent:not(.mat-checkbox-disabled).mat-checkbox-indeterminate .mat-checkbox-background{background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%)!important}  .tree-selecct-checkbox .mat-ripple-element{background:#ff4081!important}  .tree-selecct-checkbox.mat-checkbox-disabled.mat-checkbox-checked .mat-checkbox-background,   .tree-selecct-checkbox.mat-checkbox-disabled.mat-checkbox-indeterminate .mat-checkbox-background{background-color:#b0b0b0!important}.dp-class[_ngcontent-%COMP%]{background:0 0;border:1px solid #d3d3d3;border-radius:4px;font-size:12px;font-weight:500;width:100%;height:40px;margin-top:4px;cursor:pointer;text-align:center}"]}),e})();const He=["config"],Je=["currency"];function Ke(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",5),d["\u0275\u0275elementStart"](1,"div",6),d["\u0275\u0275text"](2,"Loading..."),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function Xe(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",34),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](2).openConfig()})),d["\u0275\u0275elementStart"](1,"mat-icon"),d["\u0275\u0275text"](2,"settings"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}}function Ze(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",5),d["\u0275\u0275elementStart"](1,"div",6),d["\u0275\u0275text"](2,"Loading..."),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function et(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",35),d["\u0275\u0275text"](1,"No Data"),d["\u0275\u0275elementEnd"]())}function tt(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementStart"](1,"div",7),d["\u0275\u0275elementStart"](2,"div",8),d["\u0275\u0275elementStart"](3,"p"),d["\u0275\u0275text"](4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](5,"div",9),d["\u0275\u0275elementStart"](6,"mat-icon",10),d["\u0275\u0275text"](7,"info"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](8,"div",11),d["\u0275\u0275template"](9,Xe,3,0,"div",12),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](10,"div"),d["\u0275\u0275template"](11,Ze,3,0,"div",1),d["\u0275\u0275elementStart"](12,"dx-chart",13),d["\u0275\u0275listener"]("onLegendClick",(function(t){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().legendClickHandler(t)}))("onPointClick",(function(t){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().openSummary(t)})),d["\u0275\u0275elementStart"](13,"dxi-series",14),d["\u0275\u0275element"](14,"dxo-aggregation",15,16),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](16,"dxi-series",17),d["\u0275\u0275element"](17,"dxo-aggregation",15,16),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](19,"dxi-series",18),d["\u0275\u0275element"](20,"dxo-aggregation",15,16),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](22,"dxi-series",19),d["\u0275\u0275element"](23,"dxo-aggregation",15,16),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](25,"dxi-series",20),d["\u0275\u0275element"](26,"dxo-aggregation",15,16),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](28,"dxo-common-series-settings",21,22),d["\u0275\u0275elementStart"](30,"dxi-value-axis",23),d["\u0275\u0275element"](31,"dxo-grid",24),d["\u0275\u0275element"](32,"dxo-title",25),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](33,"dxo-argument-axis",26,27),d["\u0275\u0275element"](35,"dxo-label",28),d["\u0275\u0275element"](36,"dxo-tick-interval",29),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](37,"dxo-legend",30),d["\u0275\u0275element"](38,"dxo-tooltip",31),d["\u0275\u0275element"](39,"dxo-export",32),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](40,et,2,0,"div",33),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](4),d["\u0275\u0275textInterpolate"](e.widgetConfig.widget_name),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("matTooltip",e.widgetConfig.widget_info),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngIf",e.checkForAdmin()),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",e.isDataLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275styleProp"]("display",e.isDataLoading&&e.data.length>0?"none":"block"),d["\u0275\u0275property"]("dataSource",e.data)("customizePoint",e.customizePoint),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("visible",e.isPlannedVisible)("minBarSize",10),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("enabled",!0),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("visible",e.isActualVisible)("minBarSize",10),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("enabled",!0),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("visible",e.isLastYearVisible)("minBarSize",10),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("enabled",!0),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("visible",e.isPlannedCumulativeVisible),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("enabled",!0),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("visible",e.isActualCumulativeVisible),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("enabled",!0),d["\u0275\u0275advance"](4),d["\u0275\u0275property"]("allowDecimals",!1),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("visible",!0),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("text",e.currency_label),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("valueMarginsEnabled",!1),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("months",1),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("enabled",!0)("customizeTooltip",e.customizeTooltip),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("enabled",!0)("fileName",null==e.widgetConfig?null:e.widgetConfig.widget_name),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.data.length<=0)}}function nt(e,t){1&e&&d["\u0275\u0275element"](0,"app-currency",36),2&e&&d["\u0275\u0275property"]("currencyList",t.$implicit.opportunity_value)("showActualAmount",!1)}function it(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",48),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate"](null==e.widgetConfig.local_filters||null==e.widgetConfig.local_filters.actual?null:e.widgetConfig.local_filters.actual.label)}}function at(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",49),d["\u0275\u0275element"](1,"app-multi-select-search2",50),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("list",e.salesStatus)}}function ot(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",48),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"]("",null==e.widgetConfig.local_filters||null==e.widgetConfig.local_filters.planned?null:e.widgetConfig.local_filters.planned.label," ")}}function rt(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",49),d["\u0275\u0275element"](1,"app-multi-select-search2",51),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("list",e.salesStatus)}}function lt(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",52),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"]("",null==e.widgetConfig.local_filters||null==e.widgetConfig.local_filters.salesOwner?null:e.widgetConfig.local_filters.salesOwner.label," ")}}function st(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",49),d["\u0275\u0275element"](1,"app-multi-select-search2",53),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("list",e.salesOwnerList)}}function ct(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",48),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",null==e.widgetConfig.local_filters||null==e.widgetConfig.local_filters.productCategory?null:e.widgetConfig.local_filters.productCategory.label,"")}}const dt=function(){return{id:"id",name:"name",children:"subValues"}};function pt(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",49),d["\u0275\u0275element"](1,"app-tree-select-search",54),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("config",d["\u0275\u0275pureFunction0"](2,dt))("checklistData",e.productCategoryList)}}function mt(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",48),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",null==e.widgetConfig.local_filters||null==e.widgetConfig.local_filters.probability?null:e.widgetConfig.local_filters.probability.label,"")}}function gt(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",49),d["\u0275\u0275element"](1,"app-multi-select-search2",55),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("list",e.probabilityList)}}function ut(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",37),d["\u0275\u0275elementStart"](1,"form",38),d["\u0275\u0275listener"]("ngSubmit",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().updateStatusConfig()})),d["\u0275\u0275elementStart"](2,"div",39),d["\u0275\u0275elementStart"](3,"div",40),d["\u0275\u0275elementStart"](4,"mat-icon",41),d["\u0275\u0275text"](5,"info"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](6,it,2,1,"div",42),d["\u0275\u0275template"](7,at,2,1,"div",43),d["\u0275\u0275template"](8,ot,2,1,"div",42),d["\u0275\u0275template"](9,rt,2,1,"div",43),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](10,lt,2,1,"div",44),d["\u0275\u0275template"](11,st,2,1,"div",43),d["\u0275\u0275template"](12,ct,2,1,"div",42),d["\u0275\u0275template"](13,pt,2,3,"div",43),d["\u0275\u0275template"](14,mt,2,1,"div",42),d["\u0275\u0275template"](15,gt,2,1,"div",43),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](16,"div",45),d["\u0275\u0275elementStart"](17,"button",46),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().cancelClicked()})),d["\u0275\u0275text"](18," Cancel "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](19,"button",47),d["\u0275\u0275text"](20," Submit "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("formGroup",e.configForm),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("matTooltip",null==e.widgetConfig?null:e.widgetConfig.info_label),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",null==e.widgetConfig.local_filters||null==e.widgetConfig.local_filters.actual?null:e.widgetConfig.local_filters.actual.is_active),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",null==e.widgetConfig.local_filters||null==e.widgetConfig.local_filters.actual?null:e.widgetConfig.local_filters.actual.is_active),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",null==e.widgetConfig.local_filters||null==e.widgetConfig.local_filters.planned?null:e.widgetConfig.local_filters.planned.is_active),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",null==e.widgetConfig.local_filters||null==e.widgetConfig.local_filters.planned?null:e.widgetConfig.local_filters.planned.is_active),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",null==e.widgetConfig.local_filters||null==e.widgetConfig.local_filters.salesOwner?null:e.widgetConfig.local_filters.salesOwner.is_active),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",null==e.widgetConfig.local_filters||null==e.widgetConfig.local_filters.salesOwner?null:e.widgetConfig.local_filters.salesOwner.is_active),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",null==e.widgetConfig.local_filters||null==e.widgetConfig.local_filters.productCategory?null:e.widgetConfig.local_filters.productCategory.is_active),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",null==e.widgetConfig.local_filters||null==e.widgetConfig.local_filters.productCategory?null:e.widgetConfig.local_filters.productCategory.is_active),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",null==e.widgetConfig.local_filters||null==e.widgetConfig.local_filters.probability?null:e.widgetConfig.local_filters.probability.is_active),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",null==e.widgetConfig.local_filters||null==e.widgetConfig.local_filters.probability?null:e.widgetConfig.local_filters.probability.is_active)}}function ft(e,t){1&e&&d["\u0275\u0275element"](0,"app-currency",36),2&e&&d["\u0275\u0275property"]("currencyList",t.$implicit.opportunity_value)("showActualAmount",!1)}let ht=(()=>{class e{constructor(e,t,n,i,a,o,r,l){this.salesDashBoardService=e,this.rolesService=t,this.fb=n,this.utilityService=i,this.dialog=a,this.snackBar=o,this.commaSep=r,this._toaster=l,this.filterClear=!1,this.isPlannedVisible=!0,this.isActualVisible=!0,this.isLastYearVisible=!0,this.isPlannedCumulativeVisible=!1,this.isActualCumulativeVisible=!1,this.displayFteCount=!1,this.isLoading=!0,this.isError=!1,this.currency_label="USD",this.isDataLoading=!0,this.appliedFilterCount=0,this.customizePercText=e=>e.valueText+"%",this.configForm=this.fb.group({actual:["",c.H.required],planned:["",c.H.required],salesOwner:[""],productCategory:[""],probability:[""]}),this.customizePoint=e=>{if("Planned"==e.seriesName){if(!e.aggregationInfo.data[0].plannedTotalOpp||e.aggregationInfo.data[0].plannedTotalOpp<=0)return{visible:!1}}else if("Actual"==e.seriesName){if(!e.aggregationInfo.data[0].actualTotalOpp||e.aggregationInfo.data[0].actualTotalOpp<=0)return{visible:!1}}else if("Last Year Closure"==e.seriesName){if(!e.aggregationInfo.data[0].lastActualTotalOpp||e.aggregationInfo.data[0].lastActualTotalOpp<=0)return{visible:!1}}else if("Cumulative Planned"==e.seriesName){if(!e.aggregationInfo.data[0].cumulativePlannedTotalOpp||e.aggregationInfo.data[0].cumulativePlannedTotalOpp<=0)return{visible:!1}}else if(!e.aggregationInfo.data[0].cumulativeActualTotalOpp||e.aggregationInfo.data[0].cumulativeActualTotalOpp<=0)return{visible:!1}},this.customizeTooltip=e=>{let t=this.displayFteCount?"<br/>FTE Count: "+("Planned"==e.seriesName?e.point.aggregationInfo.data[0].planned_fte_count:"Actual"==e.seriesName?e.point.aggregationInfo.data[0].actual_fte_count:"Last Year Closure"==e.seriesName?e.point.aggregationInfo.data[0].last_actual_fte_count:"Cumulative Planned"==e.seriesName?e.point.aggregationInfo.data[0].cumulative_planned_fte_count:e.point.aggregationInfo.data[0].cumulative_actual_fte_count):"";return{text:`${e.seriesName}<br/>Count: ${"Planned"==e.seriesName?e.point.aggregationInfo.data[0].plannedTotalOpp:"Actual"==e.seriesName?e.point.aggregationInfo.data[0].actualTotalOpp:"Last Year Closure"==e.seriesName?e.point.aggregationInfo.data[0].lastActualTotalOpp:"Cumulative Planned"==e.seriesName?e.point.aggregationInfo.data[0].cumulativePlannedTotalOpp:e.point.aggregationInfo.data[0].cumulativeActualTotalOpp}<br/>Value: ${this.commaSep.transform(e.value,this.currency_label,!0,!0)}${t}`}}}getReport(e){var t,n;console.log(e,"Filter Config"),this.isDataLoading=!0,this.current_filter=e;let i=this.rolesService.getUserRoleOrgCodes("Opportunities"),a=s()(null===(t=e.filter(e=>1==e.filter_id)[0])||void 0===t?void 0:t.applied_filter.startDate).format("YYYY-MM-DD"),o=s()(null===(n=e.filter(e=>1==e.filter_id)[0])||void 0===n?void 0:n.applied_filter.endDate).format("YYYY-MM-DD"),r=e.filter(e=>1==e.filter_id);e=e.filter(e=>1!=e.filter_id),r[0].applied_filter.startDate=s()(a).format("YYYY-MM-DD"),r[0].applied_filter.endDate=s()(o).format("YYYY-MM-DD"),e.push(...r),this.salesDashBoardService.getOBVTrendReport(i,e,a,o).subscribe(e=>{this.displayFteCount=this.widgetConfig.component_config.displayFteCount,this.data=e.messData,this.isLoading=!1,this.isDataLoading=!1,this.isError=!1},e=>{this.isLoading=!1,this.isDataLoading=!1,this.isError=!0}),this.appliedFilterCount=0;for(let l of e)this.appliedFilterCount+="date"==l.filter_type?l.applied_filter.startDate?1:0:l.applied_filter.length>0?1:0}checkForAdmin(){return this.salesDashBoardService.checkForAdmin()}legendClickHandler(e){const t=e.target,n=t.isVisible();"Planned"==t.name?this.isPlannedVisible=!n:"Actual"==t.name?this.isActualVisible=!n:"Last Year Closure"==t.name?this.isLastYearVisible=!n:"Cumulative Planned"==t.name?this.isPlannedCumulativeVisible=!n:"Cumulative Actual"==t.name&&(this.isActualCumulativeVisible=!n)}viewHierarchyProduct(e){return Object(o.c)(this,void 0,void 0,(function*(){let t=[],i=[],a={};yield this.salesDashBoardService.getProductHeirarchy().then(e=>{t=e},e=>{console.log(e)}),yield this.salesDashBoardService.getProductViewHierarchyLabel(36,"Opportunity").then(e=>{"S"==e.messType&&(i=e.result,a=e.result_details)},e=>{console.log(e)});const{HeirarchyViewComponent:r}=yield n.e(986).then(n.bind(null,"zS0E"));this.dialog.open(r,{height:"100%",width:"85%",position:{right:"0px"},data:{heading:"Line of Business - "+e.opportunity_name+" ("+e.opportunity_id+")",heirarchy_data:t,column_config:i,application_object:a.application_object,application_id:a.application_id,patchData:""!=e.product_category_value&&null!=e.product_category_value?"string"==typeof e.product_category_value?JSON.parse(e.product_category_value):e.product_category_value:[],returnAsArray:!1}}).afterClosed().subscribe(t=>Object(o.c)(this,void 0,void 0,(function*(){null!=t&&(console.log(t),"S"==t.messType&&this.salesDashBoardService.updateProductCategory(t.data,e.opportunity_id).then(e=>{"S"==e.messType&&(this.utilityService.showMessage("Updated Successfully!","Dismiss",3e3),this.getReport(this.current_filter))},e=>{this.utilityService.showMessage("Unable to update Product Category!","Dismiss",3e3)}))})))}))}openSummary(e){var t,n,i,a,o,r;let l,c,d=e.target.series.getValueFields()[0],p=s()(e.target.aggregationInfo.data[0].month_formatted).format("YYYY-MM-DD"),m=s()(p).startOf("month").format("YYYY-MM-DD"),g=s()(p).endOf("month").format("YYYY-MM-DD"),u=!1,f="Planned"==e.target.series.name?e.target.aggregationInfo.data[0].plannedTotalOpp:"Actual"==e.target.series.name?e.target.aggregationInfo.data[0].actualTotalOpp:"Last Year Closure"==e.target.series.name?e.target.aggregationInfo.data[0].lastActualTotalOpp:"Cumulative Planned"==e.target.series.name?e.target.aggregationInfo.data[0].cumulativePlannedTotalOpp:e.target.aggregationInfo.data[0].cumulativeActualTotalOpp;if("last_actual_value_usd"==d)p=s()(p,"YYYY-MM-DD").subtract(1,"year").format("YYYY-MM-DD"),m=s()(p).startOf("month").format("YYYY-MM-DD"),g=s()(p).endOf("month").format("YYYY-MM-DD"),l=this.configForm.value.actual,c="Actual",u=!0;else if("opportunity_actual_usd"==d)l=this.configForm.value.actual,c="Actual";else if("opportunity_planned_value_usd"==d)l=this.configForm.value.planned,c="Planned";else if("opportunity_cummulative_actual_usd"==d)l=this.configForm.value.actual,c="Cumulative Actual",m=s()(null===(t=this.current_filter.filter(e=>1==e.filter_id)[0])||void 0===t?void 0:t.applied_filter.startDate).format("YYYY-MM-DD"),g=s()(p).format("YYYY-MM")==s()(null===(n=this.current_filter.filter(e=>1==e.filter_id)[0])||void 0===n?void 0:n.applied_filter.endDate).format("YYYY-MM")?s()(null===(i=this.current_filter.filter(e=>1==e.filter_id)[0])||void 0===i?void 0:i.applied_filter.endDate).format("YYYY-MM-DD"):s()(p).endOf("month").format("YYYY-MM-DD");else{if("opportunity_cummulative_value_usd"!=d)return;l=this.configForm.value.planned,c="Cumulative Planned",m=s()(null===(a=this.current_filter.filter(e=>1==e.filter_id)[0])||void 0===a?void 0:a.applied_filter.startDate).format("YYYY-MM-DD"),g=s()(p).format("YYYY-MM")==s()(null===(o=this.current_filter.filter(e=>1==e.filter_id)[0])||void 0===o?void 0:o.applied_filter.endDate).format("YYYY-MM")?s()(null===(r=this.current_filter.filter(e=>1==e.filter_id)[0])||void 0===r?void 0:r.applied_filter.endDate).format("YYYY-MM-DD"):s()(p).endOf("month").format("YYYY-MM-DD")}this.filterClear&&(this.current_filter.find(e=>4==e.filter_id).applied_filter=[],this.current_filter.find(e=>6==e.filter_id).applied_filter=[],this.current_filter.find(e=>7==e.filter_id).applied_filter=[]);let h={columnConfig:this.summaryColumnConfig,useDxTable:this.widgetConfig.component_config.useDxTable,dataObservername:"obv_summary_observable",title:`${c} Opportunities - ${s()(p,"YYYY-MM-DD").format("MMMM YYYY")}`,subtitle:"Total opportunities: "+f,subtitle1:"Total value: $ "+this.commaSep.transform(e.target.aggregationInfo.data[0][d],this.currency_label,!0,!0),summaryFor:p};this.salesDashBoardService.getOBVDataSummary(p,l,this.current_filter,u,m,g),this.dialog.open(ge,{height:"80%",width:"80%",data:h,animation:{entryAnimation:{keyframes:[{transform:"translateY(-100%)"},{transform:"translateY(0)"}],keyframeAnimationOptions:{duration:250,easing:"ease-in-out"}},exitAnimation:{keyframes:[{transform:"translateY(0)"},{transform:"translateY(-100%)"}],keyframeAnimationOptions:{duration:250,easing:"ease-in-out"}}}})}ngOnInit(){this.widgetConfig=this.salesDashBoardService.getConfigFor(this.widget_id),this.salesDashBoardService.clearFilter$.subscribe(()=>{this.configForm.patchValue({salesOwner:"",productCategory:"",probability:""}),this.filterClear=!0}),this.salesDashBoardService.getSalesStatusMaster().subscribe(e=>{this.salesStatus=e,console.log(this.salesStatus,"filter 1")},e=>{this.salesStatus=[],console.error(e)}),this.salesDashBoardService.getSalesOwner().subscribe(e=>{this.salesOwnerList=e,console.log(this.salesOwnerList,"filter Owner")},e=>{this.salesOwnerList=[],console.error(e)}),this.salesDashBoardService.getProductHeirarchy().then(e=>{this.productCategoryList=e,console.log(this.productCategoryList,"filter Product Category")}).catch(e=>{this.productCategoryList=[],console.error(e)}),this.salesDashBoardService.getSalesProbability().subscribe(e=>{this.probabilityList=e,console.log(this.probabilityList,"filter Probability")},e=>{this.probabilityList=[],console.error(e)}),this.configForm.patchValue({actual:this.widgetConfig.status_config.actual,planned:this.widgetConfig.status_config.planned}),console.log("Patched values:",this.configForm.value),console.log("Patched values:",this.current_filter.find(e=>4===e.filter_id).applied_filter)}openConfig(){this.configDialogRef=this.dialog.open(this.configFormTemplate,{minHeight:"60vh",minWidth:"35%"})}cancelClicked(){this.configDialogRef.close()}updateStatusConfig(){this.configForm.valid?this.salesDashBoardService.updateWidgetStatusConfig(this.configForm.value,this.widget_id).subscribe(e=>{"E"==e.mesType?(this.snackBar.open("ERROR:Edit failed. Try Again","",{duration:3e3}),this.configDialogRef.close()):(this.current_filter.find(e=>4===e.filter_id).applied_filter=this.configForm.get("salesOwner").value,this.current_filter.find(e=>6===e.filter_id).applied_filter=this.configForm.get("productCategory").value,this.current_filter.find(e=>7===e.filter_id).applied_filter=this.configForm.get("probability").value,this.getReport(this.current_filter),this.snackBar.open("Successfully edited data!","Dismiss",{duration:3e3}),this.configDialogRef.close())},e=>{this.snackBar.open("ERROR:Edit failed. Try Again","",{duration:3e3}),this.configDialogRef.close()}):this._toaster.showWarning("Kindly select all mandatory fields!!","Planned & Actual Vales are Mandatory Fields",1e3)}openInNew(e){window.open(`${window.location.origin}/main/opportunities/${e.opportunity_id}/${this.utilityService.encodeURIComponent(e.opportunity_name)}`)}get summaryColumnConfig(){var e,t,n,i;let a=this.displayFteCount?[{column_name:"FTE",column_type:"text",data_field:"fte_count",text_overflow:!0,gutter_space:"5%"}]:[],o=this.displayFteCount?[{action_click:this.viewHierarchyProduct.bind(this),action_icon:"business_center",action_name:"View Hierarchy"}]:[];return[{column_name:"OPPORTUNITY NAME",column_type:"text",data_field:"opportunity_name",text_overflow:!0},{column_name:"ACCOUNT",column_type:"text",data_field:"customer_name",text_overflow:!0,gutter_space:"5%"},...(null===(e=this.widgetConfig.summaryColumnConfig)||void 0===e?void 0:e.salesOrganisation)?[{column_name:(null===(t=this.widgetConfig.summaryColumnConfig)||void 0===t?void 0:t.label)||"SALES ORGANIZATION",column_type:"text",data_field:"sales_unit_name",text_overflow:!0,gutter_space:"5%"}]:[],...(null===(n=this.widgetConfig.summaryColumnConfig)||void 0===n?void 0:n.salesRegion)?[{column_name:(null===(i=this.widgetConfig.summaryColumnConfig)||void 0===i?void 0:i.label)||"SALES REGION",column_type:"text",data_field:"sales_region_name",text_overflow:!0,gutter_space:"5%"}]:[],{column_name:"SALES OWNER",column_type:"text",data_field:"sales_owner",text_overflow:!0,gutter_space:"5%"},...a,{column_name:"VALUE",column_type:"template",template_ref:this.currency,gutter_space:"5%",data_field:"opportunity_val_usd"},{column_name:this.widgetConfig.component_config.estOppCloseDate,column_type:"date",data_field:"processing_end_date",gutter_space:"5%"},{column_name:"ACTUAL CLOSURE DATE",column_type:"date",data_field:"actual_closure_date",gutter_space:"5%"},{column_name:"STATUS",column_type:"text",data_field:"status_name",gutter_space:"5%"},{column_name:"",column_type:"actions",actions:[{action_name:"Open in new tab",action_icon:"open_in_new",action_click:this.openInNew.bind(this)},...o]}]}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](v),d["\u0275\u0275directiveInject"](_.a),d["\u0275\u0275directiveInject"](c.i),d["\u0275\u0275directiveInject"](ue.a),d["\u0275\u0275directiveInject"](fe.a),d["\u0275\u0275directiveInject"](he.a),d["\u0275\u0275directiveInject"](g),d["\u0275\u0275directiveInject"](_e.a))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-obv-trend-widget"]],viewQuery:function(e,t){if(1&e&&(d["\u0275\u0275viewQuery"](He,!0,d.TemplateRef),d["\u0275\u0275viewQuery"](Je,!0)),2&e){let e;d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.configFormTemplate=e.first),d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.currency=e.first)}},inputs:{widget_id:"widget_id"},features:[d["\u0275\u0275ProvidersFeature"]([g])],decls:9,vars:2,consts:[[1,"total-activities-card"],["class","loading-wrapper",4,"ngIf"],[4,"ngIf"],["currency",""],["config",""],[1,"loading-wrapper"],[1,"loading"],[1,"row","p-0","justify-content-between"],[1,"title"],[1,"iconbtn",2,"margin-left","5px"],[2,"font-size","larger",3,"matTooltip"],[1,"d-flex","justify-content-center","align-items-center"],["class","iconbtn pr-3","style","cursor:pointer;",3,"click",4,"ngIf"],[3,"dataSource","customizePoint","onLegendClick","onPointClick"],["valueField","opportunity_planned_value_usd","type","bar","name","Planned",3,"visible","minBarSize"],["method","avg",3,"enabled"],["pointsAggregationSettings",""],["valueField","opportunity_actual_usd","type","bar","name","Actual",3,"visible","minBarSize"],["valueField","last_actual_value_usd","type","bar","name","Last Year Closure",3,"visible","minBarSize"],["valueField","opportunity_cummulative_value_usd","name","Cumulative Planned",3,"visible"],["valueField","opportunity_cummulative_actual_usd","name","Cumulative Actual",3,"visible"],["argumentField","month_formatted"],["seriesSettings",""],["name","total","position","left",3,"allowDecimals"],[3,"visible"],[3,"text"],["argumentType","datetime","aggregatedPointsPosition","crossTicks","aggregationInterval","month",3,"valueMarginsEnabled"],["argumentAxisSettings",""],["format","monthAndYear"],[3,"months"],["verticalAlignment","top","horizontalAlignment","center"],["zIndex","1",3,"enabled","customizeTooltip"],[3,"enabled","fileName"],["class","no-data",4,"ngIf"],[1,"iconbtn","pr-3",2,"cursor","pointer",3,"click"],[1,"no-data"],["type","small",3,"currencyList","showActualAmount"],[1,"p-4",2,"height","100%","width","100%"],[3,"formGroup","ngSubmit"],[1,"filter-wrapper"],[1,"static-wrapper"],[3,"matTooltip"],["class","label-filter",4,"ngIf"],["class","col-12",4,"ngIf"],["class","label-filter mt-2",4,"ngIf"],[1,"row"],["mat-button","","type","button",2,"font-size","13px","color","#45546e","font-weight","bold !important","background-color","transparent !important","border","1px solid #45546e","display","flex","justify-content","center","align-items","center","margin-right","3%","width","1rem","height","35px","margin-left","auto",3,"click"],["mat-button","","type","submit",1,"btn-color",2,"font-size","13px","color","#ffff","font-weight","bold !important","display","flex","justify-content","center","align-items","center","margin-right","3%","width","1rem","height","35px"],[1,"label-filter"],[1,"col-12"],["required","true","placeholder","Sales status","formControlName","actual",3,"list"],["required","true","placeholder","Sales status","formControlName","planned",1,"create-account-field-inputsearch",3,"list"],[1,"label-filter","mt-2"],["placeholder","Sales Owner","formControlName","salesOwner",1,"create-account-field-inputsearch",3,"list"],["formControlName","productCategory","placeholder","Product Category",1,"create-account-field-inputsearch",3,"config","checklistData"],["placeholder","Probability","formControlName","probability",1,"create-account-field-inputsearch",3,"list"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"mat-card",0),d["\u0275\u0275template"](1,Ke,3,0,"div",1),d["\u0275\u0275template"](2,tt,41,31,"div",2),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](3,nt,1,2,"ng-template",null,3,d["\u0275\u0275templateRefExtractor"]),d["\u0275\u0275template"](5,ut,21,12,"ng-template",null,4,d["\u0275\u0275templateRefExtractor"]),d["\u0275\u0275template"](7,ft,1,2,"ng-template",null,3,d["\u0275\u0275templateRefExtractor"])),2&e&&(d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.isLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!t.isLoading))},directives:[ve.a,i.NgIf,C.a,y.a,ye.a,U.E,U.X,U.Bb,U.R,U.uc,U.Ee,U.eb,U.Oc,U.Be,U.Qc,U.Ie,U.Sb,Ce.a,c.J,c.w,c.n,xe.a,ke,c.F,c.v,c.l,Ge],styles:[".total-activities-card[_ngcontent-%COMP%]{margin:7px;height:100%;min-height:300px;padding:14px!important;color:#45546e;box-shadow:0 6px 18px rgba(0,0,0,.1)}.total-activities-card[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-weight:500;display:flex;font-size:14px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;padding:0!important;color:#45546e}  #pie{height:80%;width:100%}#overlay[_ngcontent-%COMP%]{position:absolute;display:block;width:100%;height:100%;top:0;left:0;right:0;bottom:0;background-color:rgba(0,0,0,.1);z-index:3;cursor:pointer}.no-data[_ngcontent-%COMP%]{display:flex;height:80%;font-size:small;vertical-align:center;justify-content:center;align-items:center;width:100%}.iconbtn[_ngcontent-%COMP%]{color:#7b7b7b!important;padding:0;height:-moz-fit-content;height:fit-content}.btn-color[_ngcontent-%COMP%]{background-color:var(--primaryColor)}.filter-wrapper[_ngcontent-%COMP%]{height:25rem;overflow-y:auto;margin:0 auto .5rem;width:30rem}.filter-wrapper[_ngcontent-%COMP%]   .label-filter[_ngcontent-%COMP%]{margin-left:3%;font-size:14px;color:#45546e;font-weight:500;font-family:Roboto}.filter-wrapper[_ngcontent-%COMP%]   .static-wrapper[_ngcontent-%COMP%]{border:1px solid #e0e0e0;border-radius:5px;padding:.35rem;background-color:#eceff3}.filter-wrapper[_ngcontent-%COMP%]   .static-wrapper[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{display:flex;margin-left:auto!important;font-size:19px;color:#45546e;cursor:pointer;margin-bottom:-5%}"]}),e})();var _t=n("pzj6");const vt=["config"],yt=["currency"],Ct=["filterMenu"];function xt(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",6),d["\u0275\u0275elementStart"](1,"div",7),d["\u0275\u0275text"](2,"Loading..."),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function bt(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"p",23),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate"](e.totalOppCount)}}function wt(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"span",23),d["\u0275\u0275text"](1," 0 USD "),d["\u0275\u0275elementEnd"]())}function St(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275element"](1,"app-currency",24),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("currencyList",e.total_opp_value)("showActualAmount",!1)}}function Ot(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"p"),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]().$implicit;d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"]("FTE Count: ",e.point.data.fte,"")}}function Et(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementStart"](1,"div"),d["\u0275\u0275elementStart"](2,"p"),d["\u0275\u0275text"](3),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](4,"p"),d["\u0275\u0275text"](5),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](6,"p"),d["\u0275\u0275text"](7),d["\u0275\u0275pipe"](8,"commaSeparation"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](9,Ot,2,1,"p",2),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](3),d["\u0275\u0275textInterpolate"](e.argumentText),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"]("Count: ",e.point.data.value,""),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"]("Value: ",d["\u0275\u0275pipeBind4"](8,4,e.point.data.opp_val,"USD",!0,!0),""),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",null==n.widgetConfig.component_config?null:n.widgetConfig.component_config.displayFteCount)}}const Dt=function(e){return{show:e,enabled:!0}},Mt=function(){return["SVG"]};function Pt(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementStart"](1,"div"),d["\u0275\u0275elementStart"](2,"div",8),d["\u0275\u0275elementStart"](3,"div",9),d["\u0275\u0275elementStart"](4,"p"),d["\u0275\u0275text"](5),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](6,"div",10),d["\u0275\u0275elementStart"](7,"mat-icon",11),d["\u0275\u0275text"](8,"info"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](9,bt,2,1,"p",12),d["\u0275\u0275template"](10,wt,2,0,"span",12),d["\u0275\u0275template"](11,St,2,2,"div",2),d["\u0275\u0275elementStart"](12,"span",13),d["\u0275\u0275listener"]("click",(function(){d["\u0275\u0275restoreView"](e);const t=d["\u0275\u0275nextContext"]();return t.showProdVal=!t.showProdVal})),d["\u0275\u0275elementStart"](13,"p"),d["\u0275\u0275text"](14),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](15,"mat-icon",14),d["\u0275\u0275text"](16,"sync_alt"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](17,"div"),d["\u0275\u0275elementStart"](18,"dx-pie-chart",15),d["\u0275\u0275listener"]("onPointClick",(function(t){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().openSummary(t)})),d["\u0275\u0275element"](19,"dxo-legend",16),d["\u0275\u0275elementStart"](20,"dxi-series",17),d["\u0275\u0275elementStart"](21,"dxo-label",18),d["\u0275\u0275element"](22,"dxo-connector",19),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](23,"dxo-tooltip",20),d["\u0275\u0275element"](24,"dxo-export",21),d["\u0275\u0275template"](25,Et,10,9,"div",22),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](5),d["\u0275\u0275textInterpolate"](e.widgetConfig.widget_name),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("matTooltip",e.widgetConfig.widget_info),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",e.showProdVal),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",0==e.total_opp_value.length&&!e.showProdVal),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.total_opp_value.length>0&&!e.showProdVal),d["\u0275\u0275advance"](3),d["\u0275\u0275textInterpolate1"](" ",e.showProdVal?"Total Opportunities":"Total Value",""),d["\u0275\u0275advance"](4),d["\u0275\u0275property"]("dataSource",e.data)("animation",!0)("palette","material")("loadingIndicator",d["\u0275\u0275pureFunction1"](22,Dt,e.isLoading)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("visible",!1),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("valueField",e.showProdVal?"value":"opp_val"),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("visible",!0)("customizeText",e.customizeLabel),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("visible",!0)("width",.5),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("enabled",!0),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("enabled",!0)("formats",d["\u0275\u0275pureFunction0"](24,Mt))("printingEnabled",!1)("fileName",null==e.widgetConfig?null:e.widgetConfig.widget_name),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("dxTemplateOf","content")}}function It(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",25),d["\u0275\u0275elementStart"](1,"div",26),d["\u0275\u0275text"](2,"No Data"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function Ft(e,t){1&e&&d["\u0275\u0275element"](0,"app-currency",27),2&e&&d["\u0275\u0275property"]("currencyList",t.$implicit.opportunity_value)("showActualAmount",!1)}function Tt(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",28),d["\u0275\u0275elementStart"](1,"form",29),d["\u0275\u0275listener"]("ngSubmit",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().updateStatusConfig()})),d["\u0275\u0275elementStart"](2,"div"),d["\u0275\u0275text"](3,"Choose statuses"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](4,"div",30),d["\u0275\u0275element"](5,"app-multi-select-search2",31),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](6,"div",32),d["\u0275\u0275elementStart"](7,"button",33),d["\u0275\u0275text"](8," Submit "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("formGroup",e.configForm),d["\u0275\u0275advance"](4),d["\u0275\u0275property"]("list",e.salesStatus)}}let kt=(()=>{class e{constructor(e,t,n,i,a,o,r){this.salesDashBoardService=e,this.rolesService=t,this.dialog=n,this.fb=i,this.utilityService=a,this.snackBar=o,this.commaSep=r,this.type="count",this.isLoading=!0,this.isError=!1,this.appliedFilterCount=0,this.showProdVal=!1,this.showOrg=!1,this.customizeLabel=this.customizeLbl.bind(this),this.configForm=this.fb.group({visible:["",c.H.required]})}ngOnInit(){var e;this.widgetConfig=this.salesDashBoardService.getConfigFor(this.widget_id),this.type=this.widgetConfig.component_config.type,this.salesDashBoardService.getSalesStatusMaster().subscribe(e=>{this.salesStatus=e},e=>{this.salesStatus=[],console.error(e)}),this.configForm.patchValue({visible:null===(e=this.widgetConfig.status_config)||void 0===e?void 0:e.visible})}get totalOppCount(){return r.pluck(this.data,"value").reduce((e,t)=>e+t,0)}get totalOppValue(){return r.pluck(this.data,"opp_val_mil").reduce((e,t)=>e+t,0).toFixed(2)}customizeLbl(e){var t;let n=(null===(t=this.widgetConfig.component_config)||void 0===t?void 0:t.displayFteCount)?"\nFTE Count: "+e.point.data.fte:"";const i=this.showProdVal?"":" | Value: "+this.commaSep.transform(e.point.data.opp_val,"USD",!0,!0);return`${e.argumentText}${i}${this.showProdVal?" | Count: "+e.point.data.value:""}${n}`}getReport(e){this.current_filter=e;let t=e.filter(e=>1==e.filter_id);e=e.filter(e=>1!=e.filter_id),t[0].applied_filter.startDate=l(t[0].applied_filter.startDate).format("YYYY-MM-DD"),t[0].applied_filter.endDate=l(t[0].applied_filter.endDate).format("YYYY-MM-DD"),e.push(...t);let n=this.rolesService.getUserRoleOrgCodes("Opportunities");this.salesDashBoardService.getStatusForOpportunity(n,e).subscribe(e=>{this.data=e.status_data,this.total_opp_value=e.total_opp_value,console.log(this.total_opp_value),this.product_value=e.product_category_value,console.log(this.product_value),this.palette=r.pluck(this.data,"status_color"),this.isLoading=!1,this.isError=!1},e=>{this.isError=!0,this.isLoading=!1,this.data=[]}),this.appliedFilterCount=0;for(let i of e)this.appliedFilterCount+="date"==i.filter_type?i.applied_filter.startDate?1:0:i.applied_filter.length>0?1:0}openSummary(e){let t=e.target.argument,n={columnConfig:this.summaryColumnConfig,dataObservername:"opp_status_summary_observable",title:`Opportunities with ${t.toLowerCase()} as status`,subtitle:"Total "+e.target.data.value,useDxTable:this.widgetConfig.component_config.useDxTable,summaryFor:t};this.salesDashBoardService.getOppStatusDataSummary(t,this.current_filter),this.dialog.open(ge,{height:"80%",width:"80%",data:n,animation:{entryAnimation:{keyframes:[{transform:"translateY(-100%)"},{transform:"translateY(0)"}],keyframeAnimationOptions:{duration:250,easing:"ease-in-out"}},exitAnimation:{keyframes:[{transform:"translateY(0)"},{transform:"translateY(-100%)"}],keyframeAnimationOptions:{duration:250,easing:"ease-in-out"}}}})}customizeTooltip(e){return{text:`${e.argument}: ${e.value}`}}openInNew(e){window.open(`${window.location.origin}/main/opportunities/${e.opportunity_id}/${this.utilityService.encodeURIComponent(e.opportunity_name)}`)}openConfig(){this.configDialogRef=this.dialog.open(this.configFormTemplate,{height:"40%",width:"45%"})}updateStatusConfig(){this.configForm.valid&&this.salesDashBoardService.updateWidgetStatusConfig(this.configForm.value,this.widget_id).subscribe(e=>{"E"==e.mesType?(this.snackBar.open("ERROR:Edit failed. Try Again","",{duration:3e3}),this.configDialogRef.close()):(this.getReport(this.current_filter),this.snackBar.open("Successfully edited data!","Dismiss",{duration:3e3}),this.configDialogRef.close())},e=>{this.snackBar.open("ERROR:Edit failed. Try Again","",{duration:3e3}),this.configDialogRef.close()})}viewHierarchyProduct(e){return Object(o.c)(this,void 0,void 0,(function*(){let t=[],i=[],a={};yield this.salesDashBoardService.getProductHeirarchy().then(e=>{t=e},e=>{console.log(e)}),yield this.salesDashBoardService.getProductViewHierarchyLabel(36,"Opportunity").then(e=>{"S"==e.messType&&(i=e.result,a=e.result_details)},e=>{console.log(e)});const{HeirarchyViewComponent:r}=yield n.e(986).then(n.bind(null,"zS0E"));this.dialog.open(r,{height:"100%",width:"85%",position:{right:"0px"},data:{heading:"Line of Business - "+e.opportunity_name+" ("+e.opportunity_id+")",heirarchy_data:t,column_config:i,application_object:a.application_object,application_id:a.application_id,patchData:""!=e.product_category_value&&null!=e.product_category_value?"string"==typeof e.product_category_value?JSON.parse(e.product_category_value):e.product_category_value:[],returnAsArray:!1}}).afterClosed().subscribe(t=>Object(o.c)(this,void 0,void 0,(function*(){null!=t&&(console.log(t),"S"==t.messType&&this.salesDashBoardService.updateProductCategory(t.data,e.opportunity_id).then(e=>{"S"==e.messType&&(this.utilityService.showMessage("Updated Successfully!","Dismiss",3e3),this.getReport(this.current_filter))},e=>{this.utilityService.showMessage("Unable to update Product Category!","Dismiss",3e3)}))})))}))}get summaryColumnConfig(){var e,t,n,i,a,o;let r=(null===(e=this.widgetConfig.component_config)||void 0===e?void 0:e.displayFteCount)?[{column_name:"FTE",column_type:"text",data_field:"fte_count",text_overflow:!0,gutter_space:"5%"}]:[],l=(null===(t=this.widgetConfig.summaryColumnConfig)||void 0===t?void 0:t.salesOrganisation)?[{column_name:(null===(n=this.widgetConfig.summaryColumnConfig)||void 0===n?void 0:n.label)||"SALES ORGANIZATION",column_type:"text",data_field:"sales_unit_name",text_overflow:!0,gutter_space:"5%"}]:[],s=(null===(i=this.widgetConfig.summaryColumnConfig)||void 0===i?void 0:i.salesRegion)?[{column_name:(null===(a=this.widgetConfig.summaryColumnConfig)||void 0===a?void 0:a.label)||"SALES REGION",column_type:"text",data_field:"sales_region_name",text_overflow:!0,gutter_space:"5%"}]:[],c=(null===(o=this.widgetConfig.component_config)||void 0===o?void 0:o.displayFteCount)?[{action_click:this.viewHierarchyProduct.bind(this),action_icon:"business_center",action_name:"View Hierarchy"}]:[];return[{column_name:"OPPORTUNITY NAME",column_type:"text",data_field:"opportunity_name",text_overflow:!0},{column_name:"ACCOUNT",column_type:"text",data_field:"customer_name",text_overflow:!0,gutter_space:"5%"},...l,...s,{column_name:"SALES OWNER",column_type:"text",data_field:"sales_owner",text_overflow:!0,gutter_space:"5%"},...r,{column_name:"VALUE",column_type:"template",template_ref:this.currency,gutter_space:"5%",data_field:"opportunity_val_usd"},{column_name:this.widgetConfig.component_config.estOppCloseDate,column_type:"date",data_field:"processing_end_date",gutter_space:"5%"},{column_name:"STATUS",column_type:"text",data_field:"status_name",gutter_space:"5%"},{column_name:"",column_type:"actions",actions:[{action_name:"Open in new tab",action_icon:"open_in_new",action_click:this.openInNew.bind(this)},...c]}]}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](v),d["\u0275\u0275directiveInject"](_.a),d["\u0275\u0275directiveInject"](fe.a),d["\u0275\u0275directiveInject"](c.i),d["\u0275\u0275directiveInject"](ue.a),d["\u0275\u0275directiveInject"](he.a),d["\u0275\u0275directiveInject"](g))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-opportunity-status-chart"]],viewQuery:function(e,t){if(1&e&&(d["\u0275\u0275viewQuery"](vt,!0,d.TemplateRef),d["\u0275\u0275viewQuery"](yt,!0),d["\u0275\u0275viewQuery"](Ct,!0)),2&e){let e;d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.configFormTemplate=e.first),d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.currency=e.first),d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.filterMenu=e.first)}},inputs:{widget_id:"widget_id"},features:[d["\u0275\u0275ProvidersFeature"]([g])],decls:8,vars:3,consts:[[1,"total-activities-card"],["class","loading-wrapper",4,"ngIf"],[4,"ngIf"],["id","overlay",4,"ngIf"],["currency",""],["config",""],[1,"loading-wrapper"],[1,"loading"],[1,"row","p-0","justify-content-between"],[1,"title"],[1,"iconbtn",2,"margin-left","5px"],[2,"font-size","larger",3,"matTooltip"],["class","currency",4,"ngIf"],[2,"font-weight","400","display","flex",3,"click"],[1,"hover-icon"],["id","pie","resolveLabelOverlapping","shift","type","pie",3,"dataSource","animation","palette","loadingIndicator","onPointClick"],[3,"visible"],["argumentField","name",3,"valueField"],["position","columns",3,"visible","customizeText"],[3,"visible","width"],["contentTemplate","content","zIndex","1",3,"enabled"],[3,"enabled","formats","printingEnabled","fileName"],[4,"dxTemplate","dxTemplateOf"],[1,"currency"],["type","large",1,"currency",3,"currencyList","showActualAmount"],["id","overlay"],[1,"no-data"],["type","small",3,"currencyList","showActualAmount"],[1,"p-4",2,"height","100%","width","100%"],[3,"formGroup","ngSubmit"],[1,"col-8"],["placeholder","Sales status","formControlName","visible",3,"list"],[1,"col-12","row","justify-content-end"],["mat-button","","type","submit",1,"btn-color",2,"font-size","13px","color","#ffffff","font-weight","bold !important"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"mat-card",0),d["\u0275\u0275template"](1,xt,3,0,"div",1),d["\u0275\u0275template"](2,Pt,26,25,"div",2),d["\u0275\u0275template"](3,It,3,0,"div",3),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](4,Ft,1,2,"ng-template",null,4,d["\u0275\u0275templateRefExtractor"]),d["\u0275\u0275template"](6,Tt,9,2,"ng-template",null,5,d["\u0275\u0275templateRefExtractor"])),2&e&&(d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.isLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!t.isLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",(null==t.data?null:t.data.length)<=0&&!t.isLoading))},directives:[ve.a,i.NgIf,C.a,y.a,_t.a,U.Qc,U.E,U.Oc,U.Db,U.Ie,U.Sb,Q.d,Ce.a,c.J,c.w,c.n,ke,c.v,c.l,xe.a],pipes:[g],styles:[".total-activities-card[_ngcontent-%COMP%]{margin:7px;height:100%;min-height:300px;padding:14px!important;color:#45546e;box-shadow:0 6px 18px rgba(0,0,0,.1)}.total-activities-card[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-weight:500;font-size:14px;display:flex;justify-content:space-between;line-height:16px;letter-spacing:.02em;text-transform:capitalize;padding:0!important;color:#45546e;height:-moz-fit-content;height:fit-content}.total-activities-card[_ngcontent-%COMP%]   .currency[_ngcontent-%COMP%]{font-weight:700;font-size:22px;color:#fa8c16;height:-moz-fit-content;height:fit-content}.hover-icon[_ngcontent-%COMP%]{font-size:medium;margin-left:3px}.hover-icon-wrapper[_ngcontent-%COMP%]:hover   .hover-icon[_ngcontent-%COMP%]{display:inline-block!important}[_nghost-%COMP%]     #pie{height:80%;width:100%}[_nghost-%COMP%]     .data-label{font-size:23px!important}#overlay[_ngcontent-%COMP%]{position:absolute;display:flex;justify-content:center;align-items:center;width:100%;height:100%;top:0;left:0;right:0;bottom:0;z-index:3}.iconbtn[_ngcontent-%COMP%]{color:#7b7b7b!important;padding:0;height:-moz-fit-content;height:fit-content}.btn-color[_ngcontent-%COMP%]{background-color:var(--primaryColor)}"]}),e})();var Lt=n("jes5"),Yt=n("XPKZ");const At=["currency"],Rt=["filterMenu"];function Nt(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",8),d["\u0275\u0275elementStart"](1,"div",9),d["\u0275\u0275text"](2,"Loading..."),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function Vt(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"p",18),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate"](e.totalOppCount)}}function zt(e,t){if(1&e&&d["\u0275\u0275element"](0,"app-currency",19),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275property"]("currencyList",e.total_opp_currency)("showActualAmount",!1)}}function jt(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",10),d["\u0275\u0275elementStart"](1,"div",11),d["\u0275\u0275elementStart"](2,"p"),d["\u0275\u0275text"](3),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](4,"div",12),d["\u0275\u0275elementStart"](5,"mat-icon",13),d["\u0275\u0275text"](6,"info"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](7,"div"),d["\u0275\u0275template"](8,Vt,2,1,"p",14),d["\u0275\u0275template"](9,zt,1,2,"app-currency",15),d["\u0275\u0275elementStart"](10,"span",16),d["\u0275\u0275listener"]("click",(function(){d["\u0275\u0275restoreView"](e);const t=d["\u0275\u0275nextContext"]();return t.showProdVal=!t.showProdVal})),d["\u0275\u0275elementStart"](11,"p"),d["\u0275\u0275text"](12),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](13,"mat-icon",17),d["\u0275\u0275text"](14,"sync_alt"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](3),d["\u0275\u0275textInterpolate"](e.widgetConfig.widget_name),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("matTooltip",e.widgetConfig.widget_info),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngIf",e.showProdVal),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!e.showProdVal),d["\u0275\u0275advance"](3),d["\u0275\u0275textInterpolate1"](" ",e.showProdVal?"Total Opportunities":"Total Value","")}}const Bt=function(e,t){return{width:e,"background-color":t}};function $t(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",22),d["\u0275\u0275listener"]("click",(function(){d["\u0275\u0275restoreView"](e);const n=t.$implicit,i=d["\u0275\u0275nextContext"](2);return i.pipelineClick(n[i.pipeline_argument])})),d["\u0275\u0275elementStart"](1,"div",23),d["\u0275\u0275elementStart"](2,"span",24),d["\u0275\u0275text"](3),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](4,"div",25),d["\u0275\u0275elementStart"](5,"div",26),d["\u0275\u0275listener"]("mouseenter",(function(){d["\u0275\u0275restoreView"](e);const n=t.index;return d["\u0275\u0275nextContext"](2).setIndexTooltip(n)}))("mouseleave",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](2).isTooltipVisible=!1})),d["\u0275\u0275text"](6),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](7,"div",27),d["\u0275\u0275text"](8),d["\u0275\u0275pipe"](9,"commaSeparation"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=t.index,i=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](3),d["\u0275\u0275textInterpolate1"](" #",e?e.opportunity_count:"-"," "),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("id","tooltip"+n)("ngStyle",d["\u0275\u0275pureFunction2"](10,Bt,"calc( 1% *"+100*(i.showProdVal?null==e?null:e.opportunity_count:null==e?null:e.opportunity_val)/(i.showProdVal?i.totalOppCount:i.totalOppValue),(null==i.palette?null:i.palette.length)-1>=n?i.palette[n]:"")),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",e[i.pipeline_argument]," "),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"](" ",d["\u0275\u0275pipeBind4"](9,5,e.opportunity_val,"USD",!0,!0)," ")}}function Ut(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",20),d["\u0275\u0275template"](1,$t,10,13,"div",21),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngForOf",e.pipeline_data)}}function Qt(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",28),d["\u0275\u0275elementStart"](1,"div",29),d["\u0275\u0275text"](2,"No Data"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function qt(e,t){1&e&&d["\u0275\u0275element"](0,"app-currency",30),2&e&&d["\u0275\u0275property"]("currencyList",t.$implicit.opportunity_value)("showActualAmount",!1)}function Wt(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementStart"](1,"p"),d["\u0275\u0275text"](2),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](3,"p"),d["\u0275\u0275text"](4),d["\u0275\u0275pipe"](5,"commaSeparation"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](6,"p"),d["\u0275\u0275text"](7),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"]("Count: ",e.totalOpportunities,""),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"]("Value: ",d["\u0275\u0275pipeBind4"](5,3,e.totalVal,"USD",!0,!0),""),d["\u0275\u0275advance"](3),d["\u0275\u0275textInterpolate1"]("FTE Count: ",e.totalFteCount,"")}}let Gt=(()=>{class e{constructor(e,t,n,i,a){this.opportunityService=e,this.roleService=t,this.salesDashBoardService=n,this.utilityService=i,this.dialog=a,this.isLoading=!0,this.isError=!1,this.pipeline_argument="pipeline_name",this.showProdVal=!1,this.tooltipIndex=-1,this.isTooltipVisible=!1,this.palette=["#526179","#5F6C81","#6E7B8F","#8B95A5","#a9b0bc"],this.chart_type="pipeline",this.appliedFilterCount=0}ngOnInit(){return Object(o.c)(this,void 0,void 0,(function*(){this.widgetConfig=this.salesDashBoardService.getConfigFor(this.widget_id)}))}getReport(e){return Object(o.c)(this,void 0,void 0,(function*(){this.isLoading=!0,this.current_filter=e;let t=e.filter(e=>1==e.filter_id);e=e.filter(e=>1!=e.filter_id),t[0].applied_filter.startDate=l(t[0].applied_filter.startDate).format("YYYY-MM-DD"),t[0].applied_filter.endDate=l(t[0].applied_filter.endDate).format("YYYY-MM-DD"),e.push(...t),"pipeline"==this.chart_type?(this.pipeline_argument="pipeline_name",yield this.LoadPipeline("P0",e)):(this.pipeline_argument="probability_name",yield this.LoadPipeline("Low",e)),this.appliedFilterCount=0;for(let n of e)this.appliedFilterCount+="date"==n.filter_type?n.applied_filter.startDate?1:0:n.applied_filter.length>0?1:0}))}LoadPipeline(e,t){return Object(o.c)(this,void 0,void 0,(function*(){if("pipeline"==this.chart_type){let n={Pipeline:[e]};this.salesDashBoardService.pipeline_summary_data={},yield this.opportunityService.getPipelineView(n,this.roleService.getUserRoleOrgCodes("Opportunities"),t).then(e=>{this.pipeline_data=e.pipeline_data,this.data=e,console.log(e),this.total_opp_currency=e.total_opp_value,this.isLoading=!1},e=>{console.log(e),this.isError=!0,this.isLoading=!1})}else{let n={Probability:[e]};this.salesDashBoardService.probability_summary_data={},yield this.opportunityService.getProbabilityView(n,this.roleService.getUserRoleOrgCodes("Opportunities"),t).then(e=>{this.pipeline_data=e.probability_data,this.data=e,this.total_opp_currency=e.total_opp_value,this.isLoading=!1},e=>{console.log(e),this.isError=!0,this.isLoading=!1})}}))}pipelineClick(e){let t={columnConfig:this.summaryColumnConfig,dataObservername:this.chart_type+"_summary_observable",useDxTable:this.widgetConfig.component_config.useDxTable,title:`Opportunities with ${e.toLowerCase()} ${this.chart_type}`,subtitle:"Total "+r.default.where(this.pipeline_data,{[this.pipeline_argument]:e})[0].opportunity_count,summaryFor:e};"probability"===this.chart_type?this.salesDashBoardService.getProbabilityDataSummary(e,this.current_filter):this.salesDashBoardService.getPipelineDataSummary(e,this.current_filter),this.dialog.open(ge,{height:"80%",width:"80%",data:t})}get totalOppValue(){return r.default.pluck(this.pipeline_data,"opportunity_val").reduce((e,t)=>e+t,0).toFixed(2)}get totalOppCount(){return r.default.pluck(this.pipeline_data,"opportunity_count").reduce((e,t)=>e+t,0)}openInNew(e){window.open(`${window.location.origin}/main/opportunities/${e.opportunity_id}/${this.utilityService.encodeURIComponent(e.opportunity_name)}`)}setIndexTooltip(e){var t,n,i;this.tooltipIndex=e,this.totalOpportunities=null===(t=this.pipeline_data[e])||void 0===t?void 0:t.opportunity_count,this.totalFteCount=null===(n=this.pipeline_data[e])||void 0===n?void 0:n.fte_count,this.totalVal=null===(i=this.pipeline_data[e])||void 0===i?void 0:i.opportunity_val,this.isTooltipVisible=!0}viewHierarchyProduct(e){return Object(o.c)(this,void 0,void 0,(function*(){let t=[],i=[],a={};yield this.salesDashBoardService.getProductHeirarchy().then(e=>{t=e},e=>{console.log(e)}),yield this.salesDashBoardService.getProductViewHierarchyLabel(36,"Opportunity").then(e=>{"S"==e.messType&&(i=e.result,a=e.result_details)},e=>{console.log(e)});const{HeirarchyViewComponent:r}=yield n.e(986).then(n.bind(null,"zS0E"));this.dialog.open(r,{height:"100%",width:"85%",position:{right:"0px"},data:{heading:"Line of Business - "+e.opportunity_name+" ("+e.opportunity_id+")",heirarchy_data:t,column_config:i,application_object:a.application_object,application_id:a.application_id,patchData:""!=e.product_category_value&&null!=e.product_category_value?"string"==typeof e.product_category_value?JSON.parse(e.product_category_value):e.product_category_value:[],returnAsArray:!1}}).afterClosed().subscribe(t=>Object(o.c)(this,void 0,void 0,(function*(){null!=t&&(console.log(t),"S"==t.messType&&this.salesDashBoardService.updateProductCategory(t.data,e.opportunity_id).then(e=>{"S"==e.messType&&(this.utilityService.showMessage("Updated Successfully!","Dismiss",3e3),this.getReport(this.current_filter))},e=>{this.utilityService.showMessage("Unable to update Product Category!","Dismiss",3e3)}))})))}))}get summaryColumnConfig(){var e,t,n,i;let a=(null===(e=this.widgetConfig.summaryColumnConfig)||void 0===e?void 0:e.salesOrganisation)?[{column_name:(null===(t=this.widgetConfig.summaryColumnConfig)||void 0===t?void 0:t.label)||"SALES ORGANIZATION",column_type:"text",data_field:"sales_unit_name",text_overflow:!0,gutter_space:"5%"}]:[],o=(null===(n=this.widgetConfig.summaryColumnConfig)||void 0===n?void 0:n.salesRegion)?[{column_name:(null===(i=this.widgetConfig.summaryColumnConfig)||void 0===i?void 0:i.label)||"SALES REGION",column_type:"text",data_field:"sales_region_name",text_overflow:!0,gutter_space:"5%"}]:[];return[{column_name:"OPPORTUNITY NAME",column_type:"text",data_field:"opportunity_name",text_overflow:!0},{column_name:"ACCOUNT",column_type:"text",data_field:"customer_name",text_overflow:!0,gutter_space:"10px"},{column_name:"Sales Stage",column_type:"text",data_field:"sales_stage",text_overflow:!0,gutter_space:"5px"},{column_name:"FTE",column_type:"text",data_field:"fte_count",text_overflow:!0,gutter_space:"5%"},{column_name:"VALUE",column_type:"template",template_ref:this.currency,data_field:"opportunity_val_usd"},...a,...o,{column_name:"SALES OWNER",column_type:"text",data_field:"sales_owner",text_overflow:!0},{column_name:"CURRENT EXPECTED CLOSURE DATE",column_type:"date",data_field:"processing_end_date",gutter_space:"5%"},{column_name:this.chart_type.toUpperCase(),column_type:"text",data_field:"pipeline"==this.chart_type?"pipeline_name":"probability_name",gutter_space:"5%"},{column_name:"",column_type:"actions",actions:[{action_name:"Open in new tab",action_icon:"open_in_new",action_click:this.openInNew.bind(this)},{action_click:this.viewHierarchyProduct.bind(this),action_icon:"business_center",action_name:"View Hierarchy"}]}]}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](Lt.a),d["\u0275\u0275directiveInject"](_.a),d["\u0275\u0275directiveInject"](v),d["\u0275\u0275directiveInject"](ue.a),d["\u0275\u0275directiveInject"](fe.a))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-pipeline-card"]],viewQuery:function(e,t){if(1&e&&(d["\u0275\u0275viewQuery"](At,!0),d["\u0275\u0275viewQuery"](Rt,!0)),2&e){let e;d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.currency=e.first),d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.filterMenu=e.first)}},inputs:{palette:"palette",chart_type:"chart_type",widget_id:"widget_id"},decls:9,vars:7,consts:[[1,"pipeline-card-styles","slide-in-top"],["class","loading-wrapper",4,"ngIf"],["class","p-0 justify-content-between",4,"ngIf"],["class","pr-3 pl-3 pipeline-wrapper",4,"ngIf"],["id","overlay",4,"ngIf"],["currency",""],["showEvent","dxhoverstart","hideEvent","dxhoverend",3,"visible","target"],[4,"dxTemplate","dxTemplateOf"],[1,"loading-wrapper"],[1,"loading"],[1,"p-0","justify-content-between"],[1,"title"],[1,"iconbtn",2,"margin-left","5px"],[2,"font-size","larger",3,"matTooltip"],["class","currency",4,"ngIf"],["class","currency","type","large",3,"currencyList","showActualAmount",4,"ngIf"],[2,"font-weight","400","display","flex",3,"click"],[1,"hover-icon"],[1,"currency"],["type","large",1,"currency",3,"currencyList","showActualAmount"],[1,"pr-3","pl-3","pipeline-wrapper"],["class","pipeline-row",3,"click",4,"ngFor","ngForOf"],[1,"pipeline-row",3,"click"],[1,"end"],[1,"pipeline-details"],[1,"header"],[1,"p5-block","scale-up-hor-center",2,"flex","0 1 auto",3,"id","ngStyle","mouseenter","mouseleave"],[1,"pipeline-details","start"],["id","overlay"],[1,"no-data"],["type","small",3,"currencyList","showActualAmount"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"mat-card",0),d["\u0275\u0275template"](1,Nt,3,0,"div",1),d["\u0275\u0275template"](2,jt,15,5,"div",2),d["\u0275\u0275template"](3,Ut,2,1,"div",3),d["\u0275\u0275template"](4,Qt,3,0,"div",4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](5,qt,1,2,"ng-template",null,5,d["\u0275\u0275templateRefExtractor"]),d["\u0275\u0275elementStart"](7,"dx-tooltip",6),d["\u0275\u0275template"](8,Wt,8,8,"div",7),d["\u0275\u0275elementEnd"]()),2&e&&(d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.isLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!t.isLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!t.isLoading&&!t.isError),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",(null==t.pipeline_data?null:t.pipeline_data.length)<=0&&!t.isLoading),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("visible",t.isTooltipVisible)("target","#tooltip"+t.tooltipIndex),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("dxTemplateOf","content"))},directives:[ve.a,i.NgIf,Yt.a,Q.d,C.a,y.a,Ce.a,i.NgForOf,i.NgStyle],pipes:[g],styles:['.pipeline-card-styles[_ngcontent-%COMP%]{margin:7px;min-height:300px;height:100%;padding:14px!important;color:#45546e;box-shadow:0 6px 18px rgba(0,0,0,.1)}.pipeline-card-styles[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-weight:500;display:flex;font-size:14px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;padding:0!important;color:#45546e}.pipeline-card-styles[_ngcontent-%COMP%]   .currency[_ngcontent-%COMP%]{font-weight:700;font-size:22px;color:#fa8c16}.pipeline-card-styles[_ngcontent-%COMP%]   .pipeline-row[_ngcontent-%COMP%]{position:relative;display:grid;justify-items:center;grid-template-columns:10% 1fr 15%;column-gap:15px;align-items:center}.pipeline-card-styles[_ngcontent-%COMP%]   .pipeline-details[_ngcontent-%COMP%]{font-size:14px;color:#1a1a1a;overflow:hidden;font-weight:500}.pipeline-card-styles[_ngcontent-%COMP%]   .start[_ngcontent-%COMP%]{justify-self:start}.pipeline-card-styles[_ngcontent-%COMP%]   .end[_ngcontent-%COMP%]{justify-self:end}.pipeline-card-styles[_ngcontent-%COMP%]   .pipeline-wrapper[_ngcontent-%COMP%]{padding-block:5%}.pipeline-card-styles[_ngcontent-%COMP%]   .p5-block[_ngcontent-%COMP%]{color:#fff;overflow:visible;min-width:60px;max-width:90%!important;z-index:5;background-color:#b4bbc5;padding:4px;font-size:14px;margin-bottom:1px;cursor:pointer;text-align:center;box-shadow:0 1px 1px 0 rgba(0,0,0,.14),0 1px 8px 0 rgba(0,0,0,.12)}.pipeline-card-styles[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{display:flex;flex-wrap:nowrap;padding-block:4px;overflow:visible;width:100%;justify-content:flex-start;align-items:center}.pipeline-card-styles[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]:after, .pipeline-card-styles[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]:before{content:"";flex:1 1 auto;border-bottom:1px solid #45546e}.pipeline-card-styles[_ngcontent-%COMP%]   .scale-up-hor-center[_ngcontent-%COMP%]{animation:scale-up-hor-center .4s cubic-bezier(.39,.575,.565,1) both}@keyframes scale-up-hor-center{0%{transform:scaleX(.4)}to{transform:scaleX(1)}}.pipeline-card-styles[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}.pipeline-card-styles[_ngcontent-%COMP%]   .p5-loader[_ngcontent-%COMP%]{animation:loading 1.1s ease-in-out infinite;background-image:linear-gradient(90deg,#8a929e,#6e7b8f 0,#8a929e 20%,#6e7b8f 40%,#6e7b8f 100%,#8a929e)}#overlay[_ngcontent-%COMP%]{position:absolute;display:flex;justify-content:center;align-items:center;width:100%;height:100%;top:0;left:0;right:0;bottom:0;z-index:3}.iconbtn[_ngcontent-%COMP%]{color:#7b7b7b!important;padding:0;height:-moz-fit-content;height:fit-content}']}),e})();var Ht=n("IzEk"),Jt=n("Kj3r"),Kt=n("pgif");const Xt=["cardBody"];function Zt(e,t){1&e&&d["\u0275\u0275elementContainer"](0)}function en(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",7),d["\u0275\u0275elementStart"](1,"div",8),d["\u0275\u0275text"](2,"Loading..."),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function tn(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",9),d["\u0275\u0275text"](1,"Something went wrong :("),d["\u0275\u0275elementEnd"]())}function nn(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"mat-icon",15),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](3).viewInFull()})),d["\u0275\u0275text"](1,"fullscreen"),d["\u0275\u0275elementEnd"]()}}function an(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"mat-icon",15),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](3).dialogRef.close()})),d["\u0275\u0275text"](1,"fullscreen_exit"),d["\u0275\u0275elementEnd"]()}}function on(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",10),d["\u0275\u0275elementStart"](1,"div",11),d["\u0275\u0275elementStart"](2,"p"),d["\u0275\u0275text"](3),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](4,"div",12),d["\u0275\u0275elementStart"](5,"mat-icon",13),d["\u0275\u0275text"](6,"info"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](7,"div"),d["\u0275\u0275template"](8,nn,2,0,"mat-icon",14),d["\u0275\u0275template"](9,an,2,0,"mat-icon",14),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](3),d["\u0275\u0275textInterpolate"](e.widgetConfig.widget_name),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("matTooltip",e.widgetConfig.widget_info),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngIf",!e.isFullView),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isFullView)}}function rn(e,t){1&e&&d["\u0275\u0275element"](0,"dxi-column",34)}function ln(e,t){1&e&&d["\u0275\u0275element"](0,"dxi-column",35)}function sn(e,t){1&e&&d["\u0275\u0275element"](0,"dxi-column",36)}function cn(e,t){1&e&&d["\u0275\u0275element"](0,"dxi-column",37)}function dn(e,t){1&e&&d["\u0275\u0275element"](0,"dxi-column",38)}function pn(e,t){1&e&&d["\u0275\u0275element"](0,"dxi-column",39)}function mn(e,t){1&e&&d["\u0275\u0275element"](0,"dxi-column",40)}function gn(e,t){1&e&&d["\u0275\u0275element"](0,"dxi-column",41)}function un(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementStart"](1,"p",42),d["\u0275\u0275listener"]("click",(function(n){d["\u0275\u0275restoreView"](e);const i=t.$implicit;return d["\u0275\u0275nextContext"](3).openInNew(i.data,n)})),d["\u0275\u0275text"](2,"View"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}}function fn(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"mat-icon",44),d["\u0275\u0275listener"]("click",(function(t){d["\u0275\u0275restoreView"](e);const n=d["\u0275\u0275nextContext"]().$implicit;return d["\u0275\u0275nextContext"](3).openAtRiskWizard(n.data,t)})),d["\u0275\u0275text"](1," flag "),d["\u0275\u0275elementEnd"]()}2&e&&d["\u0275\u0275property"]("matTooltip","Flag Change")}function hn(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275template"](1,fn,2,1,"mat-icon",43),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",7===e.data.issue_id)}}function _n(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",16),d["\u0275\u0275elementStart"](1,"dx-data-grid",17),d["\u0275\u0275listener"]("onExporting",(function(t){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](2).onExporting(t)})),d["\u0275\u0275template"](2,rn,1,0,"dxi-column",18),d["\u0275\u0275template"](3,ln,1,0,"dxi-column",19),d["\u0275\u0275template"](4,sn,1,0,"dxi-column",20),d["\u0275\u0275template"](5,cn,1,0,"dxi-column",21),d["\u0275\u0275template"](6,dn,1,0,"dxi-column",22),d["\u0275\u0275template"](7,pn,1,0,"dxi-column",23),d["\u0275\u0275template"](8,mn,1,0,"dxi-column",24),d["\u0275\u0275element"](9,"dxi-column",25),d["\u0275\u0275template"](10,gn,1,0,"dxi-column",26),d["\u0275\u0275template"](11,un,3,0,"div",27),d["\u0275\u0275template"](12,hn,2,1,"div",27),d["\u0275\u0275element"](13,"dxi-column",28),d["\u0275\u0275element"](14,"dxo-search-panel",29),d["\u0275\u0275element"](15,"dxo-paging",30),d["\u0275\u0275element"](16,"dxo-group-panel",29),d["\u0275\u0275element"](17,"dxo-grouping",31,32),d["\u0275\u0275element"](19,"dxo-export",33),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("dataSource",e.opp_discrepencies)("allowColumnReordering",!0)("showBorders",!0),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",null==e.widgetConfig||null==e.widgetConfig.widget_field_config?null:e.widgetConfig.widget_field_config.showOpportunityID),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",null==e.widgetConfig||null==e.widgetConfig.widget_field_config?null:e.widgetConfig.widget_field_config.showOpportunityCode),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",null==e.widgetConfig||null==e.widgetConfig.widget_field_config?null:e.widgetConfig.widget_field_config.showOpportunityName),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",null==e.widgetConfig||null==e.widgetConfig.widget_field_config?null:e.widgetConfig.widget_field_config.showSalesOwner),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",null==e.widgetConfig||null==e.widgetConfig.widget_field_config?null:e.widgetConfig.widget_field_config.showAccountName),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",null==e.widgetConfig||null==e.widgetConfig.widget_field_config?null:e.widgetConfig.widget_field_config.showClosureDate),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",null==e.widgetConfig||null==e.widgetConfig.widget_field_config?null:e.widgetConfig.widget_field_config.showOpportunityValue),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",(null==e.widgetConfig||null==e.widgetConfig.widget_field_config?null:e.widgetConfig.widget_field_config.showAtRiskWizard)&&e.showFlag&&e.flagChangeWizardAccess),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("dxTemplateOf","btnTemplate"),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("dxTemplateOf","riskFactors"),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("groupIndex",0),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("visible",!0),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("pageSize",6),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("visible",!1),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("autoExpandAll",!1),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("enabled",!0)}}function vn(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"mat-card",2),d["\u0275\u0275template"](1,en,3,0,"div",3),d["\u0275\u0275template"](2,tn,2,0,"div",4),d["\u0275\u0275template"](3,on,10,4,"div",5),d["\u0275\u0275template"](4,_n,20,19,"div",6),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275styleMap"](e.isFullView?"max-height:none":"max-height:400px"),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isError),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!e.isLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!e.isLoading&&!e.isError)}}let yn=(()=>{class e{constructor(e,t,n,i,a){this._salesDashboardService=e,this.utilityService=t,this.dialog=n,this.opportunityService=i,this._toaster=a,this.isLoading=!0,this.isError=!1,this.isFullView=!1,this.showFlag=!1,this.atRiskFieldLabel="",this.flagChangeWizardAccess=!1}ngOnInit(){return Object(o.c)(this,void 0,void 0,(function*(){this.widgetConfig=this._salesDashboardService.getConfigFor(this.widget_id),this.opportunityFormField(),this.flagChangeWizardAccess=yield this.opportunityService.checkFlagChangeAccess()}))}getReport(e){var t,n,i,a;return Object(o.c)(this,void 0,void 0,(function*(){this.isLoading=!0,this.isError=!1,this.current_filter=e;let o=null===(n=null===(t=e.filter(e=>1==e.filter_id)[0])||void 0===t?void 0:t.applied_filter)||void 0===n?void 0:n.startDate,r=null===(a=null===(i=e.filter(e=>1==e.filter_id)[0])||void 0===i?void 0:i.applied_filter)||void 0===a?void 0:a.endDate;this._salesDashboardService.getOpportunityDiscrepencies(o,r).subscribe(e=>{"S"==e.mesType?(this.opp_discrepencies=e.data,this.isLoading=!1,this.isError=!1):(this.isLoading=!1,this.isError=!0)},e=>{this.isError=!0,this.isLoading=!1,this.opp_discrepencies=[]})}))}openInNew(e,t){t.stopPropagation(),window.open(`${window.location.origin}/main/opportunities/${e.opportunity_id}/${this.utilityService.encodeURIComponent(e.opportunity_name)}`)}openAtRiskWizard(e,t){return Object(o.c)(this,void 0,void 0,(function*(){t.stopPropagation();let i=e.opportunity_id,a=e.opportunity_name;if(console.log(i,a),i){const{OpportunityFlagChangeWizardComponent:e}=yield n.e(965).then(n.bind(null,"aKSU"));this.dialog.open(e,{data:{opportunity_id:Number(i),opportunity_name:a,at_risk:1,project_id:"",label:this.atRiskFieldLabel||"Flag Change",reDirect:!0},disableClose:!1}).afterClosed().subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){"S"==e.messType?(this._toaster.showSuccess("Risk Status Updated successfully","",2e3),yield this.getReport(this.current_filter)):"E"==e.messType&&this._toaster.showError("Error Changing the Flag!",(null==e?void 0:e.error)||"",2e3)})))}}))}opportunityFormField(){return Object(o.c)(this,void 0,void 0,(function*(){this.opportunityService.getFormFieldCollection().subscribe(e=>{"S"==e.messType&&(this.formFieldData=e.result);const t=this.formFieldData.find(e=>"atRisk"===e.field_name&&e.is_active);t&&(this.showFlag=!0,this.atRiskFieldLabel=t.label)},e=>{console.log(e)})}))}viewInFull(){this.dialogRef=this.dialog.open(this.cardBody,{height:"80%",width:"80%"}),this.dialogRef.afterOpened().subscribe(e=>this.isFullView=!0),this.dialogRef.afterClosed().subscribe(e=>this.isFullView=!1)}toggle(e){this.opp_discrepencies.map(t=>{t.opportunity_id==e.opportunity_id&&(t.open=!t.open)})}onExporting(e){e.fileName=this.widgetConfig.widget_name}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](v),d["\u0275\u0275directiveInject"](ue.a),d["\u0275\u0275directiveInject"](fe.a),d["\u0275\u0275directiveInject"](Kt.a),d["\u0275\u0275directiveInject"](_e.a))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-exception-widget"]],viewQuery:function(e,t){if(1&e&&d["\u0275\u0275viewQuery"](Xt,!0),2&e){let e;d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.cardBody=e.first)}},inputs:{widget_id:"widget_id"},decls:3,vars:1,consts:[[4,"ngTemplateOutlet"],["cardBody",""],[1,"recent-activities-card"],["class","loading-wrapper",4,"ngIf"],["class","error",4,"ngIf"],["class","row p-0 justify-content-between",4,"ngIf"],["style","height:95%;overflow:auto",4,"ngIf"],[1,"loading-wrapper"],[1,"loading"],[1,"error"],[1,"row","p-0","justify-content-between"],[1,"title"],[1,"iconbtn",2,"margin-left","5px"],[2,"font-size","larger",3,"matTooltip"],["style","cursor:pointer;",3,"click",4,"ngIf"],[2,"cursor","pointer",3,"click"],[2,"height","95%","overflow","auto"],["keyExpr","issue_id",2,"height","100%",3,"dataSource","allowColumnReordering","showBorders","onExporting"],["dataField","opportunity_id","alignment","left","caption","Opportunity ID",4,"ngIf"],["dataField","opportunity_code","alignment","left","caption","Opportunity Code",4,"ngIf"],["dataField","opportunity_name","caption","Opportunity Name",4,"ngIf"],["dataField","sales_owner","alignment","left","caption","Sales Owner",4,"ngIf"],["dataField","account_name","alignment","left","caption","Account Name",4,"ngIf"],["dataField","processing_end_date","alignment","left","caption","Est. Closure Date",4,"ngIf"],["dataField","value_in_millions_USD","alignment","left","caption","Opportunity Value",4,"ngIf"],["caption","","cellTemplate","btnTemplate","alignment","right"],["caption","","cellTemplate","riskFactors","alignment","right",4,"ngIf"],[4,"dxTemplate","dxTemplateOf"],["dataField","reason",3,"groupIndex"],[3,"visible"],[3,"pageSize"],[3,"autoExpandAll"],["expand",""],[3,"enabled"],["dataField","opportunity_id","alignment","left","caption","Opportunity ID"],["dataField","opportunity_code","alignment","left","caption","Opportunity Code"],["dataField","opportunity_name","caption","Opportunity Name"],["dataField","sales_owner","alignment","left","caption","Sales Owner"],["dataField","account_name","alignment","left","caption","Account Name"],["dataField","processing_end_date","alignment","left","caption","Est. Closure Date"],["dataField","value_in_millions_USD","alignment","left","caption","Opportunity Value"],["caption","","cellTemplate","riskFactors","alignment","right"],[2,"color","rgb(77, 138, 240)","cursor","pointer",3,"click"],["style","color:rgb(77, 138, 240);cursor:pointer;",3,"matTooltip","click",4,"ngIf"],[2,"color","rgb(77, 138, 240)","cursor","pointer",3,"matTooltip","click"]],template:function(e,t){if(1&e&&(d["\u0275\u0275template"](0,Zt,1,0,"ng-container",0),d["\u0275\u0275template"](1,vn,5,6,"ng-template",null,1,d["\u0275\u0275templateRefExtractor"])),2&e){const e=d["\u0275\u0275reference"](2);d["\u0275\u0275property"]("ngTemplateOutlet",e)}},directives:[i.NgTemplateOutlet,ve.a,i.NgIf,C.a,y.a,$.a,U.g,Q.d,U.Md,U.od,U.xc,U.zc,U.Sb],styles:[".recent-activities-card[_ngcontent-%COMP%]{min-height:300px;margin:7px;height:100%;padding:14px!important;box-shadow:0 6px 18px rgba(0,0,0,.1)}.recent-activities-card[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-weight:500;font-size:14px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;display:flex;padding:0!important;color:#45546e}  .recent-activities-card .mat-expansion-panel-header-title{color:#45546e;max-width:95px}  .recent-activities-card .mat-expansion-panel-header-description{flex-direction:row;justify-content:space-between;color:#45546e}  .recent-activities-card .dx-toolbar-items-container{height:45px!important}.iconbtn[_ngcontent-%COMP%]{color:#7b7b7b!important;padding:0;height:-moz-fit-content;height:fit-content}.opp-name[_ngcontent-%COMP%]{font-weight:500}.error[_ngcontent-%COMP%]{display:flex;height:100%;font-size:small;color:#cf2020;vertical-align:center;justify-content:center;align-items:center;width:100%}"]}),e})();function Cn(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",5),d["\u0275\u0275elementStart"](1,"div",6),d["\u0275\u0275text"](2,"Loading..."),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function xn(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",19),d["\u0275\u0275elementStart"](1,"p",20),d["\u0275\u0275text"](2,"FTE: "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275text"](3),d["\u0275\u0275elementStart"](4,"span",21),d["\u0275\u0275elementStart"](5,"p",20),d["\u0275\u0275text"](6,"Value:"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](7,"app-currency",22),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]().$implicit,t=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](3),d["\u0275\u0275textInterpolate1"]("",t.product_data[e.item.id].fte_count," "),d["\u0275\u0275advance"](4),d["\u0275\u0275property"]("currencyList",t.product_data[e.item.id].currency_value)("showActualAmount",!1)}}function bn(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"mat-tree-node",15),d["\u0275\u0275element"](1,"button",16),d["\u0275\u0275elementStart"](2,"div",17),d["\u0275\u0275elementStart"](3,"div"),d["\u0275\u0275text"](4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](5,xn,8,3,"div",18),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=d["\u0275\u0275nextContext"](2);d["\u0275\u0275styleProp"]("display",n.filterLeafNode(e)?"none":"flex"),d["\u0275\u0275advance"](4),d["\u0275\u0275textInterpolate1"](" ",e.item.name," "),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",n.product_data&&n.product_data[e.item.id])}}function wn(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",19),d["\u0275\u0275elementStart"](1,"p",20),d["\u0275\u0275text"](2,"FTE: "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275text"](3),d["\u0275\u0275elementStart"](4,"span",21),d["\u0275\u0275elementStart"](5,"p",20),d["\u0275\u0275text"](6,"Value:"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](7,"app-currency",22),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]().$implicit,t=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](3),d["\u0275\u0275textInterpolate1"]("",t.product_data[e.item.id].fte_count," "),d["\u0275\u0275advance"](4),d["\u0275\u0275property"]("currencyList",t.product_data[e.item.id].currency_value)("showActualAmount",!1)}}function Sn(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"mat-tree-node",23),d["\u0275\u0275elementStart"](1,"button",24),d["\u0275\u0275elementStart"](2,"mat-icon",25),d["\u0275\u0275text"](3),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](4,"div",17),d["\u0275\u0275elementStart"](5,"div"),d["\u0275\u0275text"](6),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](7,wn,8,3,"div",18),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=d["\u0275\u0275nextContext"](2);d["\u0275\u0275styleProp"]("display",n.filterParentNode(e)?"none":"flex"),d["\u0275\u0275advance"](1),d["\u0275\u0275attribute"]("aria-label","Toggle "+e.item.name),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"](" ",n.treeControl.isExpanded(e)?"expand_more":"chevron_right"," "),d["\u0275\u0275advance"](3),d["\u0275\u0275textInterpolate1"](" ",e.item.name," "),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",n.product_data&&n.product_data[e.item.id])}}function On(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementStart"](1,"div"),d["\u0275\u0275elementStart"](2,"div",7),d["\u0275\u0275elementStart"](3,"div",8),d["\u0275\u0275elementStart"](4,"p"),d["\u0275\u0275text"](5),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](6,"div",9),d["\u0275\u0275elementStart"](7,"mat-icon",10),d["\u0275\u0275text"](8,"info"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](9,"div",11),d["\u0275\u0275elementStart"](10,"mat-tree",12),d["\u0275\u0275template"](11,bn,6,4,"mat-tree-node",13),d["\u0275\u0275template"](12,Sn,8,6,"mat-tree-node",14),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](5),d["\u0275\u0275textInterpolate"](e.widgetConfig.widget_name),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("matTooltip",e.widgetConfig.widget_info),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("dataSource",e.dataSource)("treeControl",e.treeControl),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("matTreeNodeDefWhen",e.hasChild)}}function En(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",26),d["\u0275\u0275elementStart"](1,"div",27),d["\u0275\u0275text"](2,"No Data"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function Dn(e,t){1&e&&d["\u0275\u0275element"](0,"app-currency",28),2&e&&d["\u0275\u0275property"]("currencyList",t.$implicit.opportunity_value)("showActualAmount",!1)}let Mn=(()=>{class e{constructor(e){this.salesDashboardService=e,this.flatNodeMap=new Map,this.searchString="",this.nestedNodeMap=new Map,this.selectedParent=null,this.newItemName="",this.getLevel=e=>e.level,this.isExpandable=e=>e.expandable,this.getChildren=e=>e.subValues,this.hasChild=(e,t)=>t.expandable,this.hasNoContent=(e,t)=>""===t.item,this.transformer=(e,t)=>{var n;const i=this.nestedNodeMap.get(e),a=i&&i.item===e.item?i:{};return a.item=e,a.level=t,a.expandable=!!(null===(n=e.subValues)||void 0===n?void 0:n.length),this.flatNodeMap.set(a,e),this.nestedNodeMap.set(e,a),a},this.isLoading=!0,this.isError=!1,this.treeFlattener=new Ae.d(this.transformer,this.getLevel,this.isExpandable,this.getChildren),this.treeControl=new Ye.j(this.getLevel,this.isExpandable),this.dataSource=new Ae.c(this.treeControl,this.treeFlattener)}get isExpanded(){return this.treeControl.dataNodes.every(e=>this.treeControl.isExpanded)}filterLeafNode(e){var t;return 0!=(null===(t=this.pc_filter)||void 0===t?void 0:t.length)&&!this.pc_filter.includes("string"==typeof e.item.id?parseInt(e.item.id):e.item.id)}filterParentNode(e){var t;return 0!=(null===(t=this.pc_filter)||void 0===t?void 0:t.length)&&!this.pc_filter.includes("string"==typeof e.item.id?parseInt(e.item.id):e.item.id)&&!this.treeControl.getDescendants(e).some(e=>this.pc_filter.includes("string"==typeof e.item.id?parseInt(e.item.id):e.item.id))}ngOnInit(){return Object(o.c)(this,void 0,void 0,(function*(){this.widgetConfig=this.salesDashboardService.getConfigFor(this.widget_id),this.dataSource.data=yield this.salesDashboardService.getProductHeirarchy()}))}getReport(e){var t;this.isLoading=!0,this.current_filter=e,this.pc_filter=null===(t=e.filter(e=>6==e.filter_id)[0])||void 0===t?void 0:t.applied_filter,this.salesDashboardService.getProductHierarchyForDashboard(e).subscribe(e=>{"S"==e.mesType&&(this.product_data=e.mesData),this.isLoading=!1})}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](v))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-product-category-widget"]],inputs:{widget_id:"widget_id"},decls:6,vars:3,consts:[[1,"total-activities-card"],["class","loading-wrapper",4,"ngIf"],[4,"ngIf"],["id","overlay",4,"ngIf"],["currency",""],[1,"loading-wrapper"],[1,"loading"],[1,"row","p-0","justify-content-between","mb-4"],[1,"title"],[1,"iconbtn",2,"margin-left","5px"],[2,"font-size","larger",3,"matTooltip"],[2,"max-height","400px","overflow","auto"],[3,"dataSource","treeControl"],["matTreeNodeToggle","","matTreeNodePadding","","matTreeNodePaddingIndent","20",3,"display",4,"matTreeNodeDef"],["matTreeNodePadding","","matTreeNodePaddingIndent","20",3,"display",4,"matTreeNodeDef","matTreeNodeDefWhen"],["matTreeNodeToggle","","matTreeNodePadding","","matTreeNodePaddingIndent","20"],["mat-icon-button","","disabled","",1,"button__arrow"],[2,"width","100%","display","flex","flex-direction","row","justify-content","space-between","color","#45546e"],["style","display:flex;",4,"ngIf"],[2,"display","flex"],[1,"info-name"],[2,"display","flex","margin-left","14px"],["type","small",2,"color","#45546e",3,"currencyList","showActualAmount"],["matTreeNodePadding","","matTreeNodePaddingIndent","20"],["mat-icon-button","","matTreeNodeToggle","",1,"button__arrow"],[1,"mat-icon-rtl-mirror"],["id","overlay"],[1,"no-data"],["type","small",3,"currencyList","showActualAmount"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"mat-card",0),d["\u0275\u0275template"](1,Cn,3,0,"div",1),d["\u0275\u0275template"](2,On,13,5,"div",2),d["\u0275\u0275template"](3,En,3,0,"div",3),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](4,Dn,1,2,"ng-template",null,4,d["\u0275\u0275templateRefExtractor"])),2&e&&(d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.isLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!t.isLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",(null==t.dataSource||null==t.dataSource.data?null:t.dataSource.data.length)<=0&&!t.isLoading))},directives:[ve.a,i.NgIf,C.a,y.a,Ae.b,Ae.h,Ae.g,Ae.k,Ae.j,xe.a,Ce.a],styles:[".total-activities-card[_ngcontent-%COMP%]{margin:7px;height:100%;min-height:300px;padding:14px!important;color:#45546e;box-shadow:0 6px 18px rgba(0,0,0,.1)}.total-activities-card[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-weight:500;font-size:14px;display:flex;justify-content:space-between;line-height:16px;letter-spacing:.02em;text-transform:capitalize;padding:0!important;color:#45546e;height:-moz-fit-content;height:fit-content}.total-activities-card[_ngcontent-%COMP%]   .currency[_ngcontent-%COMP%]{font-weight:700;font-size:22px;color:#fa8c16;height:-moz-fit-content;height:fit-content}.hover-icon[_ngcontent-%COMP%]{font-size:medium;margin-left:3px}.hover-icon-wrapper[_ngcontent-%COMP%]:hover   .hover-icon[_ngcontent-%COMP%]{display:inline-block!important}[_nghost-%COMP%]     #pie{height:80%;width:100%}#overlay[_ngcontent-%COMP%]{position:absolute;display:flex;justify-content:center;align-items:center;width:100%;height:100%;top:0;left:0;right:0;bottom:0;z-index:3}.iconbtn[_ngcontent-%COMP%]{color:#7b7b7b!important;padding:0;height:-moz-fit-content;height:fit-content}.info-name[_ngcontent-%COMP%]{font-weight:500;margin-right:5px;margin-bottom:0!important}"]}),e})();function Pn(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",4),d["\u0275\u0275elementStart"](1,"div",5),d["\u0275\u0275text"](2,"Loading..."),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function In(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",4),d["\u0275\u0275elementStart"](1,"div",5),d["\u0275\u0275text"](2,"Loading..."),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function Fn(e,t){1&e&&d["\u0275\u0275element"](0,"dxo-common-series-settings",30)}function Tn(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementStart"](1,"p"),d["\u0275\u0275text"](2),d["\u0275\u0275pipe"](3,"commaSeparation"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=d["\u0275\u0275nextContext"](3);d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate2"]("",e.seriesName,": ",d["\u0275\u0275pipeBind4"](3,2,e.originalValue,n.currency_label,!0,!0)," ")}}function kn(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275template"](1,Tn,4,7,"div",31),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngForOf",e.points)}}function Ln(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",32),d["\u0275\u0275text"](1,"No Data"),d["\u0275\u0275elementEnd"]())}const Yn=function(e){return{active:e}},An=function(){return["#c1dff0","#3587A4"]},Rn=function(){return{color:"#000000"}};function Nn(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementStart"](1,"div",6),d["\u0275\u0275elementStart"](2,"div",7),d["\u0275\u0275elementStart"](3,"p"),d["\u0275\u0275text"](4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](5,"div",8),d["\u0275\u0275elementStart"](6,"mat-icon",9),d["\u0275\u0275text"](7,"info"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](8,"div",10),d["\u0275\u0275listener"]("click",(function(){d["\u0275\u0275restoreView"](e);const t=d["\u0275\u0275nextContext"]();return t.isBilling=!t.isBilling})),d["\u0275\u0275elementStart"](9,"p",11),d["\u0275\u0275text"](10,"Billing"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](11,"p",12),d["\u0275\u0275text"](12," Collection "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](13,"div"),d["\u0275\u0275template"](14,In,3,0,"div",1),d["\u0275\u0275elementStart"](15,"dx-chart",13),d["\u0275\u0275listener"]("onLegendClick",(function(t){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().legendClickHandler(t)}))("onPointClick",(function(t){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().openSummary(t)})),d["\u0275\u0275elementStart"](16,"dxi-series",14),d["\u0275\u0275element"](17,"dxo-label",15),d["\u0275\u0275element"](18,"dxo-aggregation",16,17),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](20,"dxi-series",14),d["\u0275\u0275element"](21,"dxo-label",18),d["\u0275\u0275element"](22,"dxo-aggregation",16,17),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](24,"dxo-common-series-settings",19,20),d["\u0275\u0275template"](26,Fn,1,0,"dxo-common-series-settings",21),d["\u0275\u0275elementStart"](27,"dxi-value-axis",22),d["\u0275\u0275element"](28,"dxo-grid",23),d["\u0275\u0275element"](29,"dxo-title",24),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](30,"dxo-legend",25),d["\u0275\u0275element"](31,"dxo-tooltip",26),d["\u0275\u0275element"](32,"dxo-export",27),d["\u0275\u0275template"](33,kn,2,1,"div",28),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](34,Ln,2,0,"div",29),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](4),d["\u0275\u0275textInterpolate"](e.widgetConfig.widget_name),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("matTooltip",e.widgetConfig.widget_info),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngClass",d["\u0275\u0275pureFunction1"](33,Yn,e.isBilling)),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngClass",d["\u0275\u0275pureFunction1"](35,Yn,!e.isBilling)),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngIf",e.isDataLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275styleProp"]("display",e.isDataLoading&&(e.isBilling?e.billingData.length>0:e.collectionData.length>0)?"none":"block"),d["\u0275\u0275property"]("dataSource",e.isBilling?e.billingData:e.collectionData)("palette",d["\u0275\u0275pureFunction0"](37,An))("customizePoint",e.customizePoint),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("valueField",e.isBilling?"planned_billing":"planned_collection")("minBarSize",10)("name",e.isBilling?"Planned billing":"Planned collection"),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("visible",!0)("font",d["\u0275\u0275pureFunction0"](38,Rn))("customizeText",e.customizeLabelText),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("enabled",!0),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("valueField",e.isBilling?"actual_billing":"actual_collection")("minBarSize",10)("name",e.isBilling?"Actual billing":"Actual collection"),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("visible",!0)("customizeText",e.customizeLabelText),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("enabled",!0),d["\u0275\u0275advance"](4),d["\u0275\u0275property"]("ngIf",!0),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("allowDecimals",!1),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("visible",!0),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("text",e.currency_label),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("enabled",!0)("shared",!0),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("enabled",!0)("fileName",null==e.widgetConfig?null:e.widgetConfig.widget_name),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("dxTemplateOf","content"),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isBilling?e.billingData.length<=0:e.collectionData.length<=0)}}function Vn(e,t){1&e&&d["\u0275\u0275element"](0,"app-currency",33),2&e&&d["\u0275\u0275property"]("currencyList",t.$implicit.opportunity_value)("showActualAmount",!1)}function zn(e,t){1&e&&d["\u0275\u0275element"](0,"app-currency",33),2&e&&d["\u0275\u0275property"]("currencyList",t.$implicit.opportunity_value)("showActualAmount",!1)}let jn=(()=>{class e{constructor(e,t,n,i){this.salesDashBoardService=e,this.rolesService=t,this.dialog=n,this.commaSep=i,this.isLoading=!0,this.isError=!1,this.billingData=[],this.collectionData=[],this.isDataLoading=!0,this.isBilling=!0,this.defaultCurrency="USD",this.currency_label=this.rolesService.currency_info.default_currency,this.currency_unit="INR"==this.currency_label?"Cr":"M",this.customizePercText=e=>e.valueText+"%",this.customizeLabelText=this.customizeLabelTextFunc.bind(this),this.customizePoint=e=>{if(e.value<=0)return{visible:!1}}}getReport(e){var t,n;this.isDataLoading=!0,this.current_filter=e;let i=l(null===(t=e.filter(e=>1==e.filter_id)[0])||void 0===t?void 0:t.applied_filter.startDate).format("YYYY-MM-DD"),a=l(null===(n=e.filter(e=>1==e.filter_id)[0])||void 0===n?void 0:n.applied_filter.endDate).format("YYYY-MM-DD");this.salesDashBoardService.getBillingCollectionForDashboard(e,i,a).subscribe(e=>{let t=this.formatBillingData(e.messData.billingTrend),n=this.formatCollectionData(e.messData.collectionTrend);this.billingData=t,this.collectionData=n,this.defaultCurrency=(null==e?void 0:e.defaultCurrency)?e.defaultCurrency:"USD",this.isLoading=!1,this.isDataLoading=!1,this.isError=!1},e=>{this.isLoading=!1,this.isDataLoading=!1,this.isError=!0})}legendClickHandler(e){const t=e.target;t.isVisible()?t.hide():t.show()}customizeLabelTextFunc(e){return""+this.commaSep.transform(e.valueText,this.currency_label,!1,!0)}formatBillingData(e){return e.forEach(e=>{function t(e){return parseFloat(e.replace(/,/g,""))||0}if("string"==typeof e.actual_billing){const n=t(e.actual_billing.trim());e.actual_billing=Number(parseFloat(n.toFixed(2)))}else"number"!=typeof e.actual_billing&&(e.actual_billing=0);if("string"==typeof e.planned_billing){const n=t(e.planned_billing.trim());e.planned_billing=Number(parseFloat(n.toFixed(2)))}else"number"!=typeof e.planned_billing&&(e.planned_billing=0)}),e}formatCollectionData(e){return e.forEach(e=>{function t(e){return parseFloat(e.replace(/,/g,""))||0}if("string"==typeof e.actual_collection){const n=t(e.actual_collection.trim());e.actual_collection=Number(parseFloat(n.toFixed(2)))}else"number"!=typeof e.actual_collection&&(e.actual_collection=0);if("string"==typeof e.planned_collection){const n=t(e.planned_collection.trim());e.planned_collection=Number(parseFloat(n.toFixed(2)))}else"number"!=typeof e.planned_collection&&(e.planned_collection=0)}),e}openSummary(e){let t=l().year(e.target.data.year).month(e.target.data.month-1).startOf("month").format("YYYY-MM-DD"),n=l().year(e.target.data.year).month(e.target.data.month-1).endOf("month").format("YYYY-MM-DD"),i=this.isBilling?"B"+e.target.data.month+e.target.data.year:"C"+e.target.data.month+e.target.data.year,a={columnConfig:this.summaryColumnConfig,useDxTable:this.widgetConfig.component_config.useDxTable,dataObservername:"billing_collection_summary_observable",title:`${this.isBilling?"Billing":"Collection"} - ${l(t,"YYYY-MM-DD").format("MMMM YYYY")}`,summaryFor:i};this.salesDashBoardService.getBillingCollectionForDashboardSummary(t,n,this.isBilling,i),this.dialog.open(ge,{height:"80%",width:"80%",data:a,animation:{entryAnimation:{keyframes:[{transform:"translateY(-100%)"},{transform:"translateY(0)"}],keyframeAnimationOptions:{duration:250,easing:"ease-in-out"}},exitAnimation:{keyframes:[{transform:"translateY(0)"},{transform:"translateY(-100%)"}],keyframeAnimationOptions:{duration:250,easing:"ease-in-out"}}}})}ngOnInit(){this.widgetConfig=this.salesDashBoardService.getConfigFor(this.widget_id)}get summaryColumnConfig(){return[{column_name:"SUB DIVISION",column_type:"text",data_field:"subDivisionName",text_overflow:!0},{column_name:"ACCOUNT NAME",column_type:"text",data_field:"accountName",text_overflow:!0,gutter_space:"5%"},{column_name:"PROJECT ITEM NAME",column_type:"text",data_field:"itemName",text_overflow:!0,gutter_space:"5%"},{column_name:"MILESTONE NAME",column_type:"text",data_field:"milestoneName",text_overflow:!0,gutter_space:"5%"},{column_name:"MILESTONE VALUE IN "+this.defaultCurrency,column_type:"currency",currency_value:this.defaultCurrency,data_field:"value",gutter_space:"5%"},{column_name:"MILESTONE STATUS",column_type:"text",gutter_space:"5%",data_field:"milestoneStatus"},{column_name:"ACTUAL / PLANNED",column_type:"text",gutter_space:"5%",data_field:"type"}]}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](v),d["\u0275\u0275directiveInject"](_.a),d["\u0275\u0275directiveInject"](fe.a),d["\u0275\u0275directiveInject"](g))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-collector-widget"]],inputs:{widget_id:"widget_id"},features:[d["\u0275\u0275ProvidersFeature"]([g])],decls:7,vars:2,consts:[[1,"total-activities-card"],["class","loading-wrapper",4,"ngIf"],[4,"ngIf"],["currency",""],[1,"loading-wrapper"],[1,"loading"],[1,"row","p-0","justify-content-between"],[1,"title"],[1,"iconbtn",2,"margin-left","5px"],[2,"font-size","larger",3,"matTooltip"],[1,"title",2,"cursor","pointer",3,"click"],[1,"widget-action",3,"ngClass"],[1,"widget-action",2,"margin-left","5px",3,"ngClass"],[3,"dataSource","palette","customizePoint","onLegendClick","onPointClick"],["type","bar",3,"valueField","minBarSize","name"],["alignment","right",3,"visible","font","customizeText"],["method","avg",3,"enabled"],["pointsAggregationSettings",""],["alignment","left",3,"visible","customizeText"],["argumentField","month_formatted"],["seriesSettings",""],["barOverlapGroup","month_formatted",4,"ngIf"],["name","total","position","left",3,"allowDecimals"],[3,"visible"],[3,"text"],["verticalAlignment","top","horizontalAlignment","center"],["contentTemplate","content","zIndex","1",3,"enabled","shared"],[3,"enabled","fileName"],[4,"dxTemplate","dxTemplateOf"],["class","no-data",4,"ngIf"],["barOverlapGroup","month_formatted"],[4,"ngFor","ngForOf"],[1,"no-data"],["type","small",3,"currencyList","showActualAmount"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"mat-card",0),d["\u0275\u0275template"](1,Pn,3,0,"div",1),d["\u0275\u0275template"](2,Nn,35,39,"div",2),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](3,Vn,1,2,"ng-template",null,3,d["\u0275\u0275templateRefExtractor"]),d["\u0275\u0275template"](5,zn,1,2,"ng-template",null,3,d["\u0275\u0275templateRefExtractor"])),2&e&&(d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.isLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!t.isLoading))},directives:[ve.a,i.NgIf,C.a,y.a,i.NgClass,ye.a,U.E,U.Oc,U.X,U.Bb,U.R,U.uc,U.Ee,U.Qc,U.Ie,U.Sb,Q.d,i.NgForOf,Ce.a],pipes:[g],styles:[".total-activities-card[_ngcontent-%COMP%]{margin:7px;height:100%;min-height:300px;padding:14px!important;color:#45546e;box-shadow:0 6px 18px rgba(0,0,0,.1)}.total-activities-card[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-weight:500;display:flex;font-size:14px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;padding:0!important;color:#45546e}  #pie{height:80%;width:100%}#overlay[_ngcontent-%COMP%]{position:absolute;display:block;width:100%;height:100%;top:0;left:0;right:0;bottom:0;background-color:rgba(0,0,0,.1);z-index:3;cursor:pointer}.no-data[_ngcontent-%COMP%]{display:flex;height:80%;font-size:small;vertical-align:center;justify-content:center;align-items:center;width:100%}.iconbtn[_ngcontent-%COMP%]{color:#7b7b7b!important;padding:0;height:-moz-fit-content;height:fit-content}.widget-action[_ngcontent-%COMP%]{color:rgba(69,84,110,.7058823529411765)}.active[_ngcontent-%COMP%]{color:#ef4a61!important}"]}),e})();function Bn(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",4),d["\u0275\u0275elementStart"](1,"div",5),d["\u0275\u0275text"](2,"Loading..."),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function $n(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",4),d["\u0275\u0275elementStart"](1,"div",5),d["\u0275\u0275text"](2,"Loading..."),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function Un(e,t){1&e&&d["\u0275\u0275element"](0,"dxo-common-series-settings",28)}function Qn(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementStart"](1,"div"),d["\u0275\u0275elementStart"](2,"p"),d["\u0275\u0275text"](3),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](4,"p"),d["\u0275\u0275text"](5),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275advance"](3),d["\u0275\u0275textInterpolate"](e.point.series.name),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"]("Value: ",(e.originalValue/1e6).toFixed(2)," Mn USD ")}}function qn(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",29),d["\u0275\u0275text"](1,"No Data"),d["\u0275\u0275elementEnd"]())}function Wn(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementStart"](1,"div",6),d["\u0275\u0275elementStart"](2,"div",7),d["\u0275\u0275elementStart"](3,"p"),d["\u0275\u0275text"](4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](5,"div",8),d["\u0275\u0275elementStart"](6,"mat-icon",9),d["\u0275\u0275text"](7,"info"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](8,"div"),d["\u0275\u0275template"](9,$n,3,0,"div",1),d["\u0275\u0275elementStart"](10,"dx-chart",10),d["\u0275\u0275listener"]("onLegendClick",(function(t){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().legendClickHandler(t)}))("onPointClick",(function(t){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().openSummary(t)})),d["\u0275\u0275elementStart"](11,"dxi-series",11),d["\u0275\u0275element"](12,"dxo-aggregation",12,13),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](14,"dxi-series",14),d["\u0275\u0275element"](15,"dxo-aggregation",12,13),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](17,"dxo-common-series-settings",15,16),d["\u0275\u0275template"](19,Un,1,0,"dxo-common-series-settings",17),d["\u0275\u0275elementStart"](20,"dxi-value-axis",18),d["\u0275\u0275element"](21,"dxo-grid",19),d["\u0275\u0275element"](22,"dxo-title",20),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](23,"dxo-argument-axis",21,22),d["\u0275\u0275element"](25,"dxo-legend",23),d["\u0275\u0275element"](26,"dxo-tooltip",24),d["\u0275\u0275element"](27,"dxo-export",25),d["\u0275\u0275template"](28,Qn,6,2,"div",26),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](29,qn,2,0,"div",27),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](4),d["\u0275\u0275textInterpolate"](e.widgetConfig.widget_name),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("matTooltip",e.widgetConfig.widget_info),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngIf",e.isDataLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275styleProp"]("display",e.isDataLoading&&e.revDataUSD?"none":"block"),d["\u0275\u0275property"]("dataSource",e.revDataUSD),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("minBarSize",20),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("enabled",!0),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("minBarSize",10),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("enabled",!0),d["\u0275\u0275advance"](4),d["\u0275\u0275property"]("ngIf",!0),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("allowDecimals",!1),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("visible",!0),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("valueMarginsEnabled",!1),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("enabled",!0),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("enabled",!0)("fileName",null==e.widgetConfig?null:e.widgetConfig.widget_name),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("dxTemplateOf","content"),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.revDataUSD.length<=0)}}function Gn(e,t){1&e&&d["\u0275\u0275element"](0,"app-currency",30),2&e&&d["\u0275\u0275property"]("currencyList",t.$implicit.opportunity_value)("showActualAmount",!1)}function Hn(e,t){1&e&&d["\u0275\u0275element"](0,"app-currency",30),2&e&&d["\u0275\u0275property"]("currencyList",t.$implicit.opportunity_value)("showActualAmount",!1)}let Jn=(()=>{class e{constructor(e,t,n,i,a,o){this.salesDashBoardService=e,this.rolesService=t,this.fb=n,this.utilityService=i,this.dialog=a,this.snackBar=o,this.isLoading=!0,this.isError=!1,this.isDataLoading=!0,this.customizePercText=e=>e.valueText+"%"}getReport(e){this.isDataLoading=!0,this.current_filter=e,this.salesDashBoardService.getRevenueForDashboard(e).subscribe(e=>{this.revDataUSD=e.messData.usd,this.revDataINR=e.messData.inr,this.isLoading=!1,this.isDataLoading=!1,this.isError=!1},e=>{this.isLoading=!1,this.isDataLoading=!1,this.isError=!0})}legendClickHandler(e){const t=e.target;t.isVisible()?t.hide():t.show()}ngOnInit(){this.widgetConfig=this.salesDashBoardService.getConfigFor(this.widget_id)}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](v),d["\u0275\u0275directiveInject"](_.a),d["\u0275\u0275directiveInject"](c.i),d["\u0275\u0275directiveInject"](ue.a),d["\u0275\u0275directiveInject"](fe.a),d["\u0275\u0275directiveInject"](he.a))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-revenue-widget"]],inputs:{widget_id:"widget_id"},decls:7,vars:2,consts:[[1,"total-activities-card"],["class","loading-wrapper",4,"ngIf"],[4,"ngIf"],["currency",""],[1,"loading-wrapper"],[1,"loading"],[1,"row","p-0","justify-content-between"],[1,"title"],[1,"iconbtn",2,"margin-left","5px"],[2,"font-size","larger",3,"matTooltip"],[3,"dataSource","onLegendClick","onPointClick"],["valueField","planned","type","bar","name","Planned",3,"minBarSize"],["method","avg",3,"enabled"],["pointsAggregationSettings",""],["valueField","actual","type","bar","name","Actual",3,"minBarSize"],["argumentField","period"],["seriesSettings",""],["barOverlapGroup","period",4,"ngIf"],["name","total","position","left",3,"allowDecimals"],[3,"visible"],["text","USD"],["argumentType","datetime","aggregatedPointsPosition","crossTicks","aggregationInterval","month",3,"valueMarginsEnabled"],["argumentAxisSettings",""],["verticalAlignment","top","horizontalAlignment","center"],["contentTemplate","content","zIndex","1",3,"enabled"],[3,"enabled","fileName"],[4,"dxTemplate","dxTemplateOf"],["class","no-data",4,"ngIf"],["barOverlapGroup","period"],[1,"no-data"],["type","small",3,"currencyList","showActualAmount"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"mat-card",0),d["\u0275\u0275template"](1,Bn,3,0,"div",1),d["\u0275\u0275template"](2,Wn,30,19,"div",2),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](3,Gn,1,2,"ng-template",null,3,d["\u0275\u0275templateRefExtractor"]),d["\u0275\u0275template"](5,Hn,1,2,"ng-template",null,3,d["\u0275\u0275templateRefExtractor"])),2&e&&(d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.isLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!t.isLoading))},directives:[ve.a,i.NgIf,C.a,y.a,ye.a,U.E,U.X,U.Bb,U.R,U.uc,U.Ee,U.eb,U.Qc,U.Ie,U.Sb,Q.d,Ce.a],styles:[".total-activities-card[_ngcontent-%COMP%]{margin:7px;height:100%;min-height:300px;padding:14px!important;color:#45546e;box-shadow:0 6px 18px rgba(0,0,0,.1)}.total-activities-card[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-weight:500;display:flex;font-size:14px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;padding:0!important;color:#45546e}  #pie{height:80%;width:100%}#overlay[_ngcontent-%COMP%]{position:absolute;display:block;width:100%;height:100%;top:0;left:0;right:0;bottom:0;background-color:rgba(0,0,0,.1);z-index:3;cursor:pointer}.no-data[_ngcontent-%COMP%]{display:flex;height:80%;font-size:small;vertical-align:center;justify-content:center;align-items:center;width:100%}.iconbtn[_ngcontent-%COMP%]{color:#7b7b7b!important;padding:0;height:-moz-fit-content;height:fit-content}.widget-action[_ngcontent-%COMP%]{color:rgba(69,84,110,.7058823529411765)}.active[_ngcontent-%COMP%]{color:#ef4a61!important}"]}),e})();const Kn=["cardBody"];function Xn(e,t){1&e&&d["\u0275\u0275elementContainer"](0)}function Zn(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",7),d["\u0275\u0275elementStart"](1,"div",8),d["\u0275\u0275text"](2,"Loading..."),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function ei(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",9),d["\u0275\u0275text"](1,"Something went wrong :("),d["\u0275\u0275elementEnd"]())}function ti(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"mat-icon",15),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](3).viewInFull()})),d["\u0275\u0275text"](1,"fullscreen"),d["\u0275\u0275elementEnd"]()}}function ni(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"mat-icon",15),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](3).dialogRef.close()})),d["\u0275\u0275text"](1,"fullscreen_exit"),d["\u0275\u0275elementEnd"]()}}function ii(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",10),d["\u0275\u0275elementStart"](1,"div",11),d["\u0275\u0275elementStart"](2,"p"),d["\u0275\u0275text"](3),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](4,"div",12),d["\u0275\u0275elementStart"](5,"mat-icon",13),d["\u0275\u0275text"](6,"info"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](7,"div"),d["\u0275\u0275template"](8,ti,2,0,"mat-icon",14),d["\u0275\u0275template"](9,ni,2,0,"mat-icon",14),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](3),d["\u0275\u0275textInterpolate"](e.widgetConfig.widget_name),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("matTooltip",e.widgetConfig.widget_info),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngIf",!e.isFullView),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isFullView)}}function ai(e,t){1&e&&d["\u0275\u0275element"](0,"dxi-column",29)}function oi(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementStart"](1,"p",30),d["\u0275\u0275listener"]("click",(function(n){d["\u0275\u0275restoreView"](e);const i=t.$implicit;return d["\u0275\u0275nextContext"](3).openInNew(i.data,n)})),d["\u0275\u0275text"](2," View "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}}function ri(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",16),d["\u0275\u0275elementStart"](1,"dx-data-grid",17),d["\u0275\u0275listener"]("onExporting",(function(t){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](2).onExporting(t)})),d["\u0275\u0275element"](2,"dxi-column",18),d["\u0275\u0275template"](3,ai,1,0,"dxi-column",19),d["\u0275\u0275element"](4,"dxi-column",20),d["\u0275\u0275element"](5,"dxi-column",21),d["\u0275\u0275template"](6,oi,3,0,"div",22),d["\u0275\u0275element"](7,"dxi-column",23),d["\u0275\u0275element"](8,"dxo-search-panel",24),d["\u0275\u0275element"](9,"dxo-paging",25),d["\u0275\u0275element"](10,"dxo-group-panel",24),d["\u0275\u0275element"](11,"dxo-grouping",26,27),d["\u0275\u0275element"](13,"dxo-export",28),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("dataSource",e.opp_discrepencies)("allowColumnReordering",!0)("showBorders",!0),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",null==e.widgetConfig||null==e.widgetConfig.component_config?null:e.widgetConfig.component_config.showOpportunityCode),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("dxTemplateOf","btnTemplate"),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("groupIndex",0),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("visible",!0),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("pageSize",6),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("visible",!1),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("autoExpandAll",!1),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("enabled",!0)}}function li(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"mat-card",2),d["\u0275\u0275template"](1,Zn,3,0,"div",3),d["\u0275\u0275template"](2,ei,2,0,"div",4),d["\u0275\u0275template"](3,ii,10,4,"div",5),d["\u0275\u0275template"](4,ri,14,11,"div",6),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275styleMap"](e.isFullView?"max-height:none":"max-height:400px"),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isError),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!e.isLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!e.isLoading&&!e.isError)}}let si=(()=>{class e{constructor(e,t,n){this._salesDashboardService=e,this.utilityService=t,this.dialog=n,this.isLoading=!0,this.isError=!1,this.isFullView=!1}ngOnInit(){this.widgetConfig=this._salesDashboardService.getConfigFor(this.widget_id)}getReport(e){this.isLoading=!0,this.isError=!1,this.current_filter=e,this._salesDashboardService.getQuoteDiscrepencies().subscribe(e=>{"S"==e.messType?(this.opp_discrepencies=e.data,this.isLoading=!1,this.isError=!1):(this.isLoading=!1,this.isError=!0)},e=>{this.isError=!0,this.isLoading=!1,this.opp_discrepencies=[]})}openInNew(e,t){t.stopPropagation(),window.open(`${window.location.origin}/main/opportunities/${e.opportunity_id}/${this.utilityService.encodeURIComponent(e.opportunity_name)}`)}viewInFull(){this.dialogRef=this.dialog.open(this.cardBody,{height:"80%",width:"80%"}),this.dialogRef.afterOpened().subscribe(e=>this.isFullView=!0),this.dialogRef.afterClosed().subscribe(e=>this.isFullView=!1)}toggle(e){this.opp_discrepencies.map(t=>{t.opportunity_id==e.opportunity_id&&(t.open=!t.open)})}onExporting(e){e.fileName=this.widgetConfig.widget_name}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](v),d["\u0275\u0275directiveInject"](ue.a),d["\u0275\u0275directiveInject"](fe.a))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-quote-discrepencies-widget"]],viewQuery:function(e,t){if(1&e&&d["\u0275\u0275viewQuery"](Kn,!0),2&e){let e;d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.cardBody=e.first)}},inputs:{widget_id:"widget_id"},decls:3,vars:1,consts:[[4,"ngTemplateOutlet"],["cardBody",""],[1,"recent-activities-card"],["class","loading-wrapper",4,"ngIf"],["class","error",4,"ngIf"],["class","row p-0 justify-content-between",4,"ngIf"],["style","height: 95%; overflow: auto",4,"ngIf"],[1,"loading-wrapper"],[1,"loading"],[1,"error"],[1,"row","p-0","justify-content-between"],[1,"title"],[1,"iconbtn",2,"margin-left","5px"],[2,"font-size","larger",3,"matTooltip"],["style","cursor: pointer",3,"click",4,"ngIf"],[2,"cursor","pointer",3,"click"],[2,"height","95%","overflow","auto"],["keyExpr","issue_id",2,"height","100%",3,"dataSource","allowColumnReordering","showBorders","onExporting"],["dataField","opportunity_id","alignment","left"],["dataField","opportunity_code","alignment","left",4,"ngIf"],["dataField","opportunity_name"],["caption","","cellTemplate","btnTemplate","alignment","right"],[4,"dxTemplate","dxTemplateOf"],["dataField","reason",3,"groupIndex"],[3,"visible"],[3,"pageSize"],[3,"autoExpandAll"],["expand",""],[3,"enabled"],["dataField","opportunity_code","alignment","left"],[2,"color","rgb(77, 138, 240)","cursor","pointer",3,"click"]],template:function(e,t){if(1&e&&(d["\u0275\u0275template"](0,Xn,1,0,"ng-container",0),d["\u0275\u0275template"](1,li,5,6,"ng-template",null,1,d["\u0275\u0275templateRefExtractor"])),2&e){const e=d["\u0275\u0275reference"](2);d["\u0275\u0275property"]("ngTemplateOutlet",e)}},directives:[i.NgTemplateOutlet,ve.a,i.NgIf,C.a,y.a,$.a,U.g,Q.d,U.Md,U.od,U.xc,U.zc,U.Sb],styles:[".recent-activities-card[_ngcontent-%COMP%]{min-height:300px;margin:7px;height:100%;padding:14px!important;box-shadow:0 6px 18px rgba(0,0,0,.1)}.recent-activities-card[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-weight:500;font-size:14px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;display:flex;padding:0!important;color:#45546e}  .recent-activities-card .mat-expansion-panel-header-title{color:#45546e;max-width:95px}  .recent-activities-card .mat-expansion-panel-header-description{flex-direction:row;justify-content:space-between;color:#45546e}  .recent-activities-card .dx-toolbar-items-container{height:45px!important}.iconbtn[_ngcontent-%COMP%]{color:#7b7b7b!important;padding:0;height:-moz-fit-content;height:fit-content}.opp-name[_ngcontent-%COMP%]{font-weight:500}.error[_ngcontent-%COMP%]{display:flex;height:100%;font-size:small;color:#cf2020;vertical-align:center;justify-content:center;align-items:center;width:100%}"]}),e})();var ci=n("YhS8");let di=(()=>{class e{transform(e,t,n){return e.find(e=>e.filter_id==t)[n]}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=d["\u0275\u0275definePipe"]({name:"filter",type:e,pure:!0}),e})();const pi=["currency"],mi=["config"];function gi(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",5),d["\u0275\u0275elementStart"](1,"div",6),d["\u0275\u0275text"](2,"Loading..."),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function ui(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",34),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](2).openConfig()})),d["\u0275\u0275elementStart"](1,"mat-icon"),d["\u0275\u0275text"](2,"settings"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}}function fi(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",5),d["\u0275\u0275elementStart"](1,"div",6),d["\u0275\u0275text"](2,"Loading..."),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function hi(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",35),d["\u0275\u0275text"](1,"No Data"),d["\u0275\u0275elementEnd"]())}function _i(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementStart"](1,"div",7),d["\u0275\u0275elementStart"](2,"div",8),d["\u0275\u0275elementStart"](3,"p"),d["\u0275\u0275text"](4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](5,"div",9),d["\u0275\u0275elementStart"](6,"mat-icon",10),d["\u0275\u0275text"](7,"info"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](8,ui,3,0,"div",11),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](9,"div"),d["\u0275\u0275template"](10,fi,3,0,"div",1),d["\u0275\u0275elementStart"](11,"dx-chart",12),d["\u0275\u0275listener"]("onLegendClick",(function(t){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().legendClickHandler(t)}))("onPointClick",(function(t){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().openSummary(t)})),d["\u0275\u0275elementStart"](12,"dxi-series",13),d["\u0275\u0275element"](13,"dxo-aggregation",14,15),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](15,"dxi-series",16),d["\u0275\u0275element"](16,"dxo-aggregation",14,15),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](18,"dxi-series",17),d["\u0275\u0275element"](19,"dxo-aggregation",14,15),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](21,"dxi-series",18),d["\u0275\u0275element"](22,"dxo-aggregation",14,15),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](24,"dxi-series",19),d["\u0275\u0275element"](25,"dxo-aggregation",14,15),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](27,"dxi-series",20),d["\u0275\u0275element"](28,"dxo-aggregation",14,15),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](30,"dxo-common-series-settings",21,22),d["\u0275\u0275elementStart"](32,"dxo-argument-axis",23,24),d["\u0275\u0275element"](34,"dxo-label",25),d["\u0275\u0275element"](35,"dxo-tick-interval",26),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](36,"dxi-value-axis",27),d["\u0275\u0275element"](37,"dxo-grid",28),d["\u0275\u0275element"](38,"dxo-title",29),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](39,"dxo-legend",30),d["\u0275\u0275element"](40,"dxo-tooltip",31),d["\u0275\u0275element"](41,"dxo-export",32),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](42,hi,2,0,"div",33),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](4),d["\u0275\u0275textInterpolate"](e.widgetConfig.widget_name),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("matTooltip",e.widgetConfig.widget_info),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",e.checkForAdmin()),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",e.isDataLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275styleProp"]("display",e.isDataLoading&&e.data.length>0?"none":"block"),d["\u0275\u0275property"]("dataSource",e.data)("customizePoint",e.customizePoint),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("visible",e.isRevenueVisible)("minBarSize",10),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("enabled",!0),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("visible",e.isCostVisible)("minBarSize",10),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("enabled",!0),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("visible",e.isGmVisible)("minBarSize",10),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("enabled",!0),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("visible",e.isCumulativeRevenueVisible),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("enabled",!0),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("visible",e.isCumulativeCostVisible),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("enabled",!0),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("visible",e.isCumulativeGmVisible),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("enabled",!0),d["\u0275\u0275advance"](4),d["\u0275\u0275property"]("valueMarginsEnabled",!1),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("months",1),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("allowDecimals",!1),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("visible",!0),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("text",e.y_axis_label),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("enabled",!0)("customizeTooltip",e.customizeTooltip),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("enabled",!0)("fileName",null==e.widgetConfig?null:e.widgetConfig.widget_name),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.data.length<=0)}}function vi(e,t){1&e&&d["\u0275\u0275element"](0,"app-currency",36),2&e&&d["\u0275\u0275property"]("currencyList",t.$implicit.opportunity_value)("showActualAmount",!1)}function yi(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275text"](1),d["\u0275\u0275pipe"](2,"filter"),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",d["\u0275\u0275pipeBind3"](2,1,e.filters,6,"filter_name")," ")}}const Ci=function(){return{applyLabel:"Apply",displayFormat:"DD MMM YYYY",customRangeLabel:"Custom Range",format:"YYYY-MM-DD",clearLabel:"Clear"}};function xi(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"input",44),d["\u0275\u0275listener"]("ngModelChange",(function(t){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](2).duration=t}))("ngModelChange",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](2).changeInDateRange()}))("startDateChanged",(function(t){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](2).startDateClicked(t)})),d["\u0275\u0275pipe"](1,"filter"),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275property"]("ngModel",e.duration)("showCustomRangeLabel",!0)("locale",d["\u0275\u0275pureFunction0"](12,Ci))("placeholder",d["\u0275\u0275pipeBind3"](1,8,e.filters,6,"filter_name"))("alwaysShowCalendars",!0)("ranges",e.dateRangePickerRanges)("linkedCalendars",!0)("maxDate",e.maxDate)}}function bi(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275text"](1),d["\u0275\u0275pipe"](2,"filter"),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" Choose ",d["\u0275\u0275pipeBind3"](2,1,e.filters,1,"filter_name")," ")}}function wi(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",45),d["\u0275\u0275element"](1,"app-multi-select-search2",46),d["\u0275\u0275pipe"](2,"filter"),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("placeholder",d["\u0275\u0275pipeBind3"](2,2,e.filters,1,"filter_name"))("list",e.salesStatusList)}}function Si(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275text"](1),d["\u0275\u0275pipe"](2,"filter"),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" Choose ",d["\u0275\u0275pipeBind3"](2,1,e.filters,2,"filter_name")," ")}}function Oi(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",45),d["\u0275\u0275element"](1,"app-multi-select-search2",47),d["\u0275\u0275pipe"](2,"filter"),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("placeholder",d["\u0275\u0275pipeBind3"](2,2,e.filters,2,"filter_name"))("list",e.serviceLineList)}}function Ei(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275text"](1),d["\u0275\u0275pipe"](2,"filter"),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" Choose ",d["\u0275\u0275pipeBind3"](2,1,e.filters,3,"filter_name")," ")}}function Di(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",45),d["\u0275\u0275element"](1,"app-multi-select-search2",48),d["\u0275\u0275pipe"](2,"filter"),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("placeholder",d["\u0275\u0275pipeBind3"](2,2,e.filters,3,"filter_name"))("list",e.entityList)}}function Mi(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275text"](1),d["\u0275\u0275pipe"](2,"filter"),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" Choose ",d["\u0275\u0275pipeBind3"](2,1,e.filters,4,"filter_name")," ")}}function Pi(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",45),d["\u0275\u0275element"](1,"app-multi-select-search2",49),d["\u0275\u0275pipe"](2,"filter"),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("placeholder",d["\u0275\u0275pipeBind3"](2,2,e.filters,4,"filter_name"))("list",e.divisionList)}}function Ii(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275text"](1),d["\u0275\u0275pipe"](2,"filter"),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" Choose ",d["\u0275\u0275pipeBind3"](2,1,e.filters,5,"filter_name")," ")}}function Fi(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",45),d["\u0275\u0275element"](1,"app-multi-select-search2",50),d["\u0275\u0275pipe"](2,"filter"),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("placeholder",d["\u0275\u0275pipeBind3"](2,2,e.filters,5,"filter_name"))("list",e.subDivisionList)}}function Ti(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",37),d["\u0275\u0275elementStart"](1,"mat-icon",38),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().closeConfig()})),d["\u0275\u0275text"](2,"close"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](3,yi,3,5,"div",2),d["\u0275\u0275pipe"](4,"filter"),d["\u0275\u0275template"](5,xi,2,13,"input",39),d["\u0275\u0275pipe"](6,"filter"),d["\u0275\u0275elementStart"](7,"form",40),d["\u0275\u0275listener"]("ngSubmit",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().updateStatusConfig()})),d["\u0275\u0275template"](8,bi,3,5,"div",2),d["\u0275\u0275pipe"](9,"filter"),d["\u0275\u0275template"](10,wi,3,6,"div",41),d["\u0275\u0275pipe"](11,"filter"),d["\u0275\u0275template"](12,Si,3,5,"div",2),d["\u0275\u0275pipe"](13,"filter"),d["\u0275\u0275template"](14,Oi,3,6,"div",41),d["\u0275\u0275pipe"](15,"filter"),d["\u0275\u0275template"](16,Ei,3,5,"div",2),d["\u0275\u0275pipe"](17,"filter"),d["\u0275\u0275template"](18,Di,3,6,"div",41),d["\u0275\u0275pipe"](19,"filter"),d["\u0275\u0275template"](20,Mi,3,5,"div",2),d["\u0275\u0275pipe"](21,"filter"),d["\u0275\u0275template"](22,Pi,3,6,"div",41),d["\u0275\u0275pipe"](23,"filter"),d["\u0275\u0275template"](24,Ii,3,5,"div",2),d["\u0275\u0275pipe"](25,"filter"),d["\u0275\u0275template"](26,Fi,3,6,"div",41),d["\u0275\u0275pipe"](27,"filter"),d["\u0275\u0275elementStart"](28,"div",42),d["\u0275\u0275elementStart"](29,"button",43),d["\u0275\u0275text"](30," Submit "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](4,13,e.filters,6,"is_active")),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](6,17,e.filters,6,"is_active")),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("formGroup",e.configForm),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](9,21,e.filters,1,"is_active")),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](11,25,e.filters,1,"is_active")),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](13,29,e.filters,2,"is_active")),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](15,33,e.filters,2,"is_active")),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](17,37,e.filters,3,"is_active")),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](19,41,e.filters,3,"is_active")),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](21,45,e.filters,4,"is_active")),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](23,49,e.filters,4,"is_active")),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](25,53,e.filters,5,"is_active")),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBind3"](27,57,e.filters,5,"is_active"))}}function ki(e,t){1&e&&d["\u0275\u0275element"](0,"app-currency",36),2&e&&d["\u0275\u0275property"]("currencyList",t.$implicit.opportunity_value)("showActualAmount",!1)}let Li=(()=>{class e{constructor(e,t,n,i,a,o){this.salesDashBoardService=e,this.utilityService=t,this.dialog=n,this.fb=i,this.snackBar=a,this.commaSep=o,this.isRevenueVisible=!0,this.isCostVisible=!0,this.isGmVisible=!0,this.isCumulativeRevenueVisible=!1,this.isCumulativeCostVisible=!1,this.isCumulativeGmVisible=!1,this.isLoading=!0,this.isError=!1,this.y_axis_label="Value",this.isDataLoading=!0,this.appliedFilterCount=0,this.currencyCode="USD",this.filters=[],this.summaryTableMonth="",this.configForm=this.fb.group({status:[],serviceLine:[],entity:[],division:[],subDivision:[]}),this.salesStatusList=[],this.serviceLineList=[],this.entityList=[],this.divisionList=[],this.subDivisionList=[],this.tempStatusValue=[],this.tempServiceLineValue=[],this.tempEntityValue=[],this.tempDivisionValue=[],this.tempSubDivisionValue=[],this.maxDate=null,this.dateRangePickerRanges={"This Month":[s()().startOf("month"),s()().endOf("month")],"Last Month":[s()().subtract(1,"month").startOf("month"),s()().subtract(1,"month").endOf("month")],"Next Month":[s()().add(1,"month").startOf("month"),s()().add(1,"month").endOf("month")],"Upcoming 3 Months":[s()().startOf("month"),s()().add(2,"month").endOf("month")],"This Year":[s()().startOf("year"),s()().endOf("year")],"Previous Year":[s()().subtract(1,"year").startOf("year"),s()().subtract(1,"year").endOf("year")],"Current FY":[s()().subtract(s()().month()<3?1:0,"year").month(3).date(1).startOf("day"),s()().add(s()().month()>=3?1:0,"year").month(2).date(31).endOf("day")],"Previous FY":[s()().subtract(s()().month()<3?2:1,"years").month(3).date(1).startOf("day"),s()().subtract(s()().month()<3?1:0,"years").month(2).date(31).endOf("day")]},this.customizePoint=e=>{if(!e.aggregationInfo.data[0].count||e.aggregationInfo.data[0].count<=0)return{visible:!1}},this.customizeTooltip=e=>({text:`${e.seriesName}<br/>Count: ${["Revenue","Cost","Gross Margin"].includes(e.seriesName)?e.point.aggregationInfo.data[0].count:e.point.aggregationInfo.data[0].cumulative_count}<br/>Value: ${this.commaSep.transform(e.value,this.currencyCode,!0,!0)}`})}getReport(e){var t,n;this.isDataLoading=!0,this.current_filter=e;let i=s()(null===(t=e.filter(e=>1==e.filter_id)[0])||void 0===t?void 0:t.applied_filter.startDate).format("YYYY-MM-DD"),a=s()(null===(n=e.filter(e=>1==e.filter_id)[0])||void 0===n?void 0:n.applied_filter.endDate).format("YYYY-MM-DD"),o=e.filter(e=>1==e.filter_id);e=e.filter(e=>1!=e.filter_id),o[0].applied_filter.startDate=s()(i).format("YYYY-MM-DD"),o[0].applied_filter.endDate=s()(a).format("YYYY-MM-DD"),e.push(...o);let r=s()(i).format("YYYY-MM-DD"),l=s()(a).format("YYYY-MM-DD");this.duration={start:r,end:l},this.durationStart=s()(this.duration.start).format("YYYY-MM-DD"),this.durationEnd=s()(this.duration.end).format("YYYY-MM-DD"),this.salesDashBoardService.getQuoteRevCostGmTrend(e,i,a,this.configForm.value.status,this.configForm.value.serviceLine,this.configForm.value.entity,this.configForm.value.division,this.configForm.value.subDivision,this.durationStart,this.durationEnd).subscribe(e=>{this.data=e.data,this.currencyCode=e.currency_code,this.isLoading=!1,this.isDataLoading=!1,this.isError=!1},e=>{this.isLoading=!1,this.isDataLoading=!1,this.isError=!0}),this.appliedFilterCount=0;for(let s of e)this.appliedFilterCount+="date"==s.filter_type?s.applied_filter.startDate?1:0:s.applied_filter.length>0?1:0}getReportOnUpdate(e){var t,n;this.isDataLoading=!0,this.current_filter=e;let i=s()(null===(t=e.filter(e=>1==e.filter_id)[0])||void 0===t?void 0:t.applied_filter.startDate).format("YYYY-MM-DD"),a=s()(null===(n=e.filter(e=>1==e.filter_id)[0])||void 0===n?void 0:n.applied_filter.endDate).format("YYYY-MM-DD"),o=e.filter(e=>1==e.filter_id);e=e.filter(e=>1!=e.filter_id),o[0].applied_filter.startDate=s()(i).format("YYYY-MM-DD"),o[0].applied_filter.endDate=s()(a).format("YYYY-MM-DD"),e.push(...o),this.salesDashBoardService.getQuoteRevCostGmTrend(e,i,a,this.configForm.value.status,this.configForm.value.serviceLine,this.configForm.value.entity,this.configForm.value.division,this.configForm.value.subDivision,this.durationStart,this.durationEnd).subscribe(e=>{this.data=e.data,this.currencyCode=e.currency_code,this.isLoading=!1,this.isDataLoading=!1,this.isError=!1},e=>{this.isLoading=!1,this.isDataLoading=!1,this.isError=!0}),this.appliedFilterCount=0;for(let r of e)this.appliedFilterCount+="date"==r.filter_type?r.applied_filter.startDate?1:0:r.applied_filter.length>0?1:0}checkForAdmin(){return this.salesDashBoardService.checkForAdmin()}legendClickHandler(e){const t=e.target,n=t.isVisible();"Revenue"==t.name?this.isRevenueVisible=!n:"Cost"==t.name?this.isCostVisible=!n:"Gross Margin"==t.name?this.isGmVisible=!n:"Cumulative Revenue"==t.name?this.isCumulativeRevenueVisible=!n:"Cumulative Cost"==t.name?this.isCumulativeCostVisible=!n:"Cumulative GM"==t.name&&(this.isCumulativeGmVisible=!n)}startDateClicked(e){this.maxDate=s()(e.startDate._d).add(3,"year").subtract(1,"day")}viewHierarchyProduct(e){return Object(o.c)(this,void 0,void 0,(function*(){let t=[],i=[],a={};yield this.salesDashBoardService.getProductHeirarchy().then(e=>{t=e},e=>{console.log(e)}),yield this.salesDashBoardService.getProductViewHierarchyLabel(36,"Opportunity").then(e=>{"S"==e.messType&&(i=e.result,a=e.result_details)},e=>{console.log(e)});const{HeirarchyViewComponent:r}=yield n.e(986).then(n.bind(null,"zS0E"));this.dialog.open(r,{height:"100%",width:"85%",position:{right:"0px"},data:{heading:"Line of Business - "+e.opportunity_name+" ("+e.opportunity_id+")",heirarchy_data:t,column_config:i,application_object:a.application_object,application_id:a.application_id,patchData:""!=e.product_category_value&&null!=e.product_category_value?"string"==typeof e.product_category_value?JSON.parse(e.product_category_value):e.product_category_value:[],returnAsArray:!1}}).afterClosed().subscribe(t=>Object(o.c)(this,void 0,void 0,(function*(){null!=t&&"S"==t.messType&&this.salesDashBoardService.updateProductCategory(t.data,e.opportunity_id).then(e=>{"S"==e.messType&&(this.utilityService.showMessage("Updated Successfully!","Dismiss",3e3),this.getReport(this.current_filter))},e=>{this.utilityService.showMessage("Unable to update Product Category!","Dismiss",3e3)})})))}))}openSummary(e){let t=e.target.series.getValueFields()[0],n=s()(e.target.aggregationInfo.data[0].month).format("YYYY-MM-DD");this.summaryTableMonth=s()(n).format("MMMM YYYY").toUpperCase();let i,a,o=this.configForm.value.status,r=this.configForm.value.serviceLine,l=this.configForm.value.entity,c=this.configForm.value.division,d=this.configForm.value.subDivision,p=e.target.aggregationInfo.data,m=p[0].count;if("revenue"==t)i="Revenue",a=p[0].revenue;else if("cost"==t)i="Cost",a=p[0].cost;else{if("gm"!=t)return;i="Gross Margin",a=p[0].gm}let g={columnConfig:this.summaryColumnConfig,useDxTable:this.widgetConfig.component_config.useDxTable,dataObservername:"qrcg_summary_observable",title:`${i} - ${s()(n,"YYYY-MM-DD").format("MMMM YYYY")}`,subtitle:"Total Opportunities: "+m,subtitle1:`Total ${i}: ${this.commaSep.transform(a,this.currencyCode,!0,!0)}`,summaryFor:n};this.salesDashBoardService.getQuoteRevCostGmTrendDataSummary(n,this.current_filter,o,r,l,c,d),this.dialog.open(ge,{height:"80%",width:"80%",data:g,animation:{entryAnimation:{keyframes:[{transform:"translateY(-100%)"},{transform:"translateY(0)"}],keyframeAnimationOptions:{duration:250,easing:"ease-in-out"}},exitAnimation:{keyframes:[{transform:"translateY(0)"},{transform:"translateY(-100%)"}],keyframeAnimationOptions:{duration:250,easing:"ease-in-out"}}}})}ngOnInit(){this.duration=this.dateRangePickerRanges["Current FY"],this.tempDuration=this.dateRangePickerRanges["Current FY"],this.widgetConfig=this.salesDashBoardService.getConfigFor(this.widget_id),this.filters=this.widgetConfig.filters,this.salesDashBoardService.getSalesStatusMaster().subscribe(e=>{this.salesStatusList=e},e=>{this.salesStatusList=[],console.error(e)}),this.salesDashBoardService.getServices().subscribe(e=>{this.serviceLineList=e.data&&e.data.length>0?e.data:[]},e=>{this.serviceLineList=[],console.error(e)}),this.salesDashBoardService.getEntity().subscribe(e=>{this.entityList=e.data&&e.data.length>0?e.data:[]},e=>{this.entityList=[],console.error(e)}),this.salesDashBoardService.getDivision().subscribe(e=>{this.divisionList=e.data&&e.data.length>0?e.data:[]},e=>{this.divisionList=[],console.error(e)}),this.salesDashBoardService.getSubDivision().subscribe(e=>{this.subDivisionList=e.data&&e.data.length>0?e.data:[]},e=>{this.subDivisionList=[],console.error(e)})}openConfig(){this.tempStatusValue=this.configForm.value.status,this.tempServiceLineValue=this.configForm.value.serviceLine,this.tempEntityValue=this.configForm.value.entity,this.tempDivisionValue=this.configForm.value.division,this.tempSubDivisionValue=this.configForm.value.subDivision,this.tempDuration=this.duration,this.tempCurrentFilter=this.current_filter,this.configDialogRef=this.dialog.open(this.configFormTemplate,{height:"85%",width:"45%",disableClose:!0})}updateStatusConfig(){this.getReportOnUpdate(this.current_filter),this.snackBar.open("Successfully edited data!","Dismiss",{duration:3e3}),this.configDialogRef.close()}closeConfig(){this.configForm.patchValue({status:this.tempStatusValue,serviceLine:this.tempServiceLineValue,entity:this.tempEntityValue,division:this.tempDivisionValue,subDivision:this.tempSubDivisionValue}),this.duration=this.tempDuration,this.current_filter=this.tempCurrentFilter,this.configDialogRef.close()}changeInDateRange(){this.durationStart=s()(this.duration.start).format("YYYY-MM-DD"),this.durationEnd=s()(this.duration.end).format("YYYY-MM-DD")}openInNew(e){window.open(`${window.location.origin}/main/opportunities/${e.opportunity_id}/${this.utilityService.encodeURIComponent(e.opportunity_name)}`)}get summaryColumnConfig(){var e,t,n,i;return[{column_name:"OPPORTUNITY NAME",column_type:"text",data_field:"opportunity_name",text_overflow:!0},{column_name:"ACCOUNT",column_type:"text",data_field:"customer_name",text_overflow:!0,gutter_space:"5%"},...(null===(e=this.widgetConfig.summaryColumnConfig)||void 0===e?void 0:e.salesOrganisation)?[{column_name:(null===(t=this.widgetConfig.summaryColumnConfig)||void 0===t?void 0:t.label)||"SALES ORGANIZATION",column_type:"text",data_field:"sales_unit_name",text_overflow:!0,gutter_space:"5%"}]:[],...(null===(n=this.widgetConfig.summaryColumnConfig)||void 0===n?void 0:n.salesRegion)?[{column_name:(null===(i=this.widgetConfig.summaryColumnConfig)||void 0===i?void 0:i.label)||"SALES REGION",column_type:"text",data_field:"sales_region_name",text_overflow:!0,gutter_space:"5%"}]:[],{column_name:"SALES OWNER",column_type:"text",data_field:"sales_owner",text_overflow:!0,gutter_space:"5%"},{column_name:"OPPORTUNITY VALUE",column_type:"template",template_ref:this.currency,gutter_space:"5%",data_field:"opportunity_val_usd"},{column_name:"REVENUE PROJECTION FOR "+this.summaryTableMonth+" IN "+this.currencyCode,column_type:"currency",currency_value:this.currencyCode,data_field:"revenue",gutter_space:"5%"},{column_name:"COST PROJECTION FOR "+this.summaryTableMonth+" IN "+this.currencyCode,column_type:"currency",currency_value:this.currencyCode,data_field:"cost",gutter_space:"5%"},{column_name:"GROSS MARGIN PROJECTION FOR "+this.summaryTableMonth+" IN "+this.currencyCode,column_type:"currency",currency_value:this.currencyCode,data_field:"gm",gutter_space:"5%"},{column_name:"ESTIMATED OPPORTUNITY CLOSURE DATE",column_type:"date",data_field:"processing_end_date",gutter_space:"5%"},{column_name:"STATUS",column_type:"text",data_field:"status_name",gutter_space:"5%"},{column_name:"",column_type:"actions",actions:[{action_name:"Open in new tab",action_icon:"open_in_new",action_click:this.openInNew.bind(this)}]}]}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](v),d["\u0275\u0275directiveInject"](ue.a),d["\u0275\u0275directiveInject"](fe.a),d["\u0275\u0275directiveInject"](c.i),d["\u0275\u0275directiveInject"](he.a),d["\u0275\u0275directiveInject"](g))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-rev-cost-gm-trend"]],viewQuery:function(e,t){if(1&e&&(d["\u0275\u0275viewQuery"](pi,!0),d["\u0275\u0275viewQuery"](mi,!0,d.TemplateRef)),2&e){let e;d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.currency=e.first),d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.configFormTemplate=e.first)}},inputs:{widget_id:"widget_id"},features:[d["\u0275\u0275ProvidersFeature"]([g])],decls:9,vars:2,consts:[[1,"total-activities-card"],["class","loading-wrapper",4,"ngIf"],[4,"ngIf"],["currency",""],["config",""],[1,"loading-wrapper"],[1,"loading"],[1,"row","p-0","justify-content-between"],[1,"title"],[1,"iconbtn",2,"margin-left","5px"],[2,"font-size","larger",3,"matTooltip"],["class","iconbtn pr-3","style","cursor: pointer",3,"click",4,"ngIf"],[3,"dataSource","customizePoint","onLegendClick","onPointClick"],["valueField","revenue","type","bar","name","Revenue",3,"visible","minBarSize"],["method","avg",3,"enabled"],["pointsAggregationSettings",""],["valueField","cost","type","bar","name","Cost",3,"visible","minBarSize"],["valueField","gm","type","bar","name","Gross Margin",3,"visible","minBarSize"],["valueField","cumulative_revenue","name","Cumulative Revenue",3,"visible"],["valueField","cumulative_cost","name","Cumulative Cost",3,"visible"],["valueField","cumulative_gm","name","Cumulative GM",3,"visible"],["argumentField","month_formatted"],["seriesSettings",""],["argumentType","datetime","aggregatedPointsPosition","crossTicks","aggregationInterval","month",3,"valueMarginsEnabled"],["argumentAxisSettings",""],["format","monthAndYear"],[3,"months"],["name","total","position","left",3,"allowDecimals"],[3,"visible"],[3,"text"],["verticalAlignment","top","horizontalAlignment","center"],["zIndex","1",3,"enabled","customizeTooltip"],[3,"enabled","fileName"],["class","no-data",4,"ngIf"],[1,"iconbtn","pr-3",2,"cursor","pointer",3,"click"],[1,"no-data"],["type","small",3,"currencyList","showActualAmount"],[1,"p-4",2,"height","100%","width","100%"],[1,"close-icon",3,"click"],["startKey","start","endKey","end","type","text","class","dp-class col-8","ngxDaterangepickerMd","",3,"ngModel","showCustomRangeLabel","locale","placeholder","alwaysShowCalendars","ranges","linkedCalendars","maxDate","ngModelChange","startDateChanged",4,"ngIf"],[3,"formGroup","ngSubmit"],["class","col-8 p-0",4,"ngIf"],[1,"col-12","row","justify-content-end"],["mat-button","","type","submit",1,"btn-color",2,"font-size","13px","color","#ffffff","font-weight","bold !important"],["startKey","start","endKey","end","type","text","ngxDaterangepickerMd","",1,"dp-class","col-8",3,"ngModel","showCustomRangeLabel","locale","placeholder","alwaysShowCalendars","ranges","linkedCalendars","maxDate","ngModelChange","startDateChanged"],[1,"col-8","p-0"],["required","true","formControlName","status",3,"placeholder","list"],["required","true","formControlName","serviceLine",3,"placeholder","list"],["required","true","formControlName","entity",3,"placeholder","list"],["required","true","formControlName","division",3,"placeholder","list"],["required","true","formControlName","subDivision",3,"placeholder","list"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"mat-card",0),d["\u0275\u0275template"](1,gi,3,0,"div",1),d["\u0275\u0275template"](2,_i,43,33,"div",2),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](3,vi,1,2,"ng-template",null,3,d["\u0275\u0275templateRefExtractor"]),d["\u0275\u0275template"](5,Ti,31,61,"ng-template",null,4,d["\u0275\u0275templateRefExtractor"]),d["\u0275\u0275template"](7,ki,1,2,"ng-template",null,3,d["\u0275\u0275templateRefExtractor"])),2&e&&(d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.isLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!t.isLoading))},directives:[ve.a,i.NgIf,C.a,y.a,ye.a,U.E,U.X,U.Bb,U.eb,U.Oc,U.Be,U.R,U.uc,U.Ee,U.Qc,U.Ie,U.Sb,Ce.a,c.J,c.w,c.n,xe.a,ci.b,c.e,c.v,c.y,ke,c.F,c.l],pipes:[di],styles:[".total-activities-card[_ngcontent-%COMP%]{margin:7px;height:100%;min-height:300px;padding:14px!important;color:#45546e;box-shadow:0 6px 18px rgba(0,0,0,.1)}.total-activities-card[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-weight:500;display:flex;font-size:14px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;padding:0!important;color:#45546e}  #pie{height:80%;width:100%}#overlay[_ngcontent-%COMP%]{position:absolute;display:block;width:100%;height:100%;top:0;left:0;right:0;bottom:0;background-color:rgba(0,0,0,.1);z-index:3;cursor:pointer}.no-data[_ngcontent-%COMP%]{display:flex;height:80%;font-size:small;vertical-align:center;justify-content:center;align-items:center;width:100%}.iconbtn[_ngcontent-%COMP%]{color:#7b7b7b!important;padding:0;height:-moz-fit-content;height:fit-content;z-index:5}.close-icon[_ngcontent-%COMP%]{display:flex;justify-content:end;font-size:20px}.close-icon[_ngcontent-%COMP%], .dp-class[_ngcontent-%COMP%]{width:100%;cursor:pointer}.dp-class[_ngcontent-%COMP%]{background:0 0;border:1px solid #d3d3d3;border-radius:4px;font-size:12px;font-weight:500;height:40px;margin-top:4px;text-align:center}.btn-color[_ngcontent-%COMP%]{background-color:var(--primaryColor)}"]}),e})();const Yi=["config"],Ai=["currency"];function Ri(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",5),d["\u0275\u0275elementStart"](1,"div",6),d["\u0275\u0275text"](2,"Loading..."),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function Ni(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",33),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](2).openConfig()})),d["\u0275\u0275elementStart"](1,"mat-icon"),d["\u0275\u0275text"](2,"settings"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}}function Vi(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",5),d["\u0275\u0275elementStart"](1,"div",6),d["\u0275\u0275text"](2,"Loading..."),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function zi(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",34),d["\u0275\u0275text"](1,"No Data"),d["\u0275\u0275elementEnd"]())}function ji(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementStart"](1,"div",7),d["\u0275\u0275elementStart"](2,"div",8),d["\u0275\u0275elementStart"](3,"p"),d["\u0275\u0275text"](4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](5,"div",9),d["\u0275\u0275elementStart"](6,"mat-icon",10),d["\u0275\u0275text"](7,"info"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](8,Ni,3,0,"div",11),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](9,"div"),d["\u0275\u0275template"](10,Vi,3,0,"div",1),d["\u0275\u0275elementStart"](11,"dx-chart",12),d["\u0275\u0275listener"]("onLegendClick",(function(t){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().legendClickHandler(t)}))("onPointClick",(function(t){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().openSummary(t)})),d["\u0275\u0275elementStart"](12,"dxi-series",13),d["\u0275\u0275element"](13,"dxo-aggregation",14,15),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](15,"dxi-series",16),d["\u0275\u0275element"](16,"dxo-aggregation",14,15),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](18,"dxi-series",17),d["\u0275\u0275element"](19,"dxo-aggregation",14,15),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](21,"dxi-series",18),d["\u0275\u0275element"](22,"dxo-aggregation",14,15),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](24,"dxi-series",19),d["\u0275\u0275element"](25,"dxo-aggregation",14,15),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](27,"dxo-common-series-settings",20,21),d["\u0275\u0275elementStart"](29,"dxi-value-axis",22),d["\u0275\u0275element"](30,"dxo-grid",23),d["\u0275\u0275element"](31,"dxo-title",24),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](32,"dxo-argument-axis",25,26),d["\u0275\u0275element"](34,"dxo-label",27),d["\u0275\u0275element"](35,"dxo-tick-interval",28),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](36,"dxo-legend",29),d["\u0275\u0275element"](37,"dxo-tooltip",30),d["\u0275\u0275element"](38,"dxo-export",31),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](39,zi,2,0,"div",32),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](4),d["\u0275\u0275textInterpolate"](e.widgetConfig.widget_name),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("matTooltip",e.widgetConfig.widget_info),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",e.checkForAdmin()),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",e.isDataLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275styleProp"]("display",e.isDataLoading&&e.data.length>0?"none":"block"),d["\u0275\u0275property"]("dataSource",e.data)("customizePoint",e.customizePoint),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("visible",e.isPlannedVisible)("minBarSize",10),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("enabled",!0),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("visible",e.isActualVisible)("minBarSize",10),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("enabled",!0),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("visible",e.isLastYearVisible)("minBarSize",10),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("enabled",!0),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("visible",e.isPlannedCumulativeVisible),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("enabled",!0),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("visible",e.isActualCumulativeVisible),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("enabled",!0),d["\u0275\u0275advance"](4),d["\u0275\u0275property"]("allowDecimals",!1),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("visible",!0),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("text",e.fte_label),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("valueMarginsEnabled",!1),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("months",1),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("enabled",!0)("customizeTooltip",e.customizeTooltip),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("enabled",!0)("fileName",null==e.widgetConfig?null:e.widgetConfig.widget_name),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.data.length<=0)}}function Bi(e,t){1&e&&d["\u0275\u0275element"](0,"app-currency",35),2&e&&d["\u0275\u0275property"]("currencyList",t.$implicit.opportunity_value)("showActualAmount",!1)}function $i(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",48),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",null==e.widgetConfig.local_filters||null==e.widgetConfig.local_filters.actual?null:e.widgetConfig.local_filters.actual.label," ")}}function Ui(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",49),d["\u0275\u0275element"](1,"app-multi-select-search2",50),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("list",e.salesStatus)}}function Qi(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",48),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",null==e.widgetConfig.local_filters||null==e.widgetConfig.local_filters.planned?null:e.widgetConfig.local_filters.planned.label," ")}}function qi(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",49),d["\u0275\u0275element"](1,"app-multi-select-search2",51),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("list",e.salesStatus)}}function Wi(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",52),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",null==e.widgetConfig.local_filters||null==e.widgetConfig.local_filters.salesOwner?null:e.widgetConfig.local_filters.salesOwner.label," ")}}function Gi(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",49),d["\u0275\u0275element"](1,"app-multi-select-search2",53),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("list",e.salesOwnerList)}}function Hi(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",48),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",null==e.widgetConfig.local_filters||null==e.widgetConfig.local_filters.productCategory?null:e.widgetConfig.local_filters.productCategory.label," ")}}const Ji=function(){return{id:"id",name:"name",children:"subValues"}};function Ki(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",49),d["\u0275\u0275element"](1,"app-tree-select-search",54),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("config",d["\u0275\u0275pureFunction0"](2,Ji))("checklistData",e.productCategoryList)}}function Xi(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",48),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",null==e.widgetConfig.local_filters||null==e.widgetConfig.local_filters.probability?null:e.widgetConfig.local_filters.probability.label," ")}}function Zi(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",49),d["\u0275\u0275element"](1,"app-multi-select-search2",55),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("list",e.probabilityList)}}function ea(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",48),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",null==e.widgetConfig.local_filters||null==e.widgetConfig.local_filters.shift?null:e.widgetConfig.local_filters.shift.label," ")}}function ta(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",49),d["\u0275\u0275element"](1,"app-multi-select-search2",56),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("list",e.shiftList)}}function na(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",36),d["\u0275\u0275elementStart"](1,"form",37),d["\u0275\u0275listener"]("ngSubmit",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().updateStatusConfig()})),d["\u0275\u0275elementStart"](2,"div",38),d["\u0275\u0275elementStart"](3,"div",39),d["\u0275\u0275elementStart"](4,"mat-icon",40),d["\u0275\u0275text"](5,"info"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](6,$i,2,1,"div",41),d["\u0275\u0275template"](7,Ui,2,1,"div",42),d["\u0275\u0275template"](8,Qi,2,1,"div",41),d["\u0275\u0275template"](9,qi,2,1,"div",42),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](10,"div",43),d["\u0275\u0275template"](11,Wi,2,1,"div",44),d["\u0275\u0275template"](12,Gi,2,1,"div",42),d["\u0275\u0275template"](13,Hi,2,1,"div",41),d["\u0275\u0275template"](14,Ki,2,3,"div",42),d["\u0275\u0275template"](15,Xi,2,1,"div",41),d["\u0275\u0275template"](16,Zi,2,1,"div",42),d["\u0275\u0275template"](17,ea,2,1,"div",41),d["\u0275\u0275template"](18,ta,2,1,"div",42),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](19,"div",45),d["\u0275\u0275elementStart"](20,"button",46),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().cancelClicked()})),d["\u0275\u0275text"](21," Cancel "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](22,"button",47),d["\u0275\u0275text"](23," Submit "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("formGroup",e.configForm),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("matTooltip",null==e.widgetConfig?null:e.widgetConfig.info_label),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",null==e.widgetConfig.local_filters||null==e.widgetConfig.local_filters.actual?null:e.widgetConfig.local_filters.actual.is_active),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",null==e.widgetConfig.local_filters||null==e.widgetConfig.local_filters.actual?null:e.widgetConfig.local_filters.actual.is_active),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",null==e.widgetConfig.local_filters||null==e.widgetConfig.local_filters.planned?null:e.widgetConfig.local_filters.planned.is_active),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",null==e.widgetConfig.local_filters||null==e.widgetConfig.local_filters.planned?null:e.widgetConfig.local_filters.planned.is_active),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",null==e.widgetConfig.local_filters||null==e.widgetConfig.local_filters.salesOwner?null:e.widgetConfig.local_filters.salesOwner.is_active),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",null==e.widgetConfig.local_filters||null==e.widgetConfig.local_filters.salesOwner?null:e.widgetConfig.local_filters.salesOwner.is_active),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",null==e.widgetConfig.local_filters||null==e.widgetConfig.local_filters.productCategory?null:e.widgetConfig.local_filters.productCategory.is_active),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",null==e.widgetConfig.local_filters||null==e.widgetConfig.local_filters.productCategory?null:e.widgetConfig.local_filters.productCategory.is_active),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",null==e.widgetConfig.local_filters||null==e.widgetConfig.local_filters.probability?null:e.widgetConfig.local_filters.probability.is_active),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",null==e.widgetConfig.local_filters||null==e.widgetConfig.local_filters.probability?null:e.widgetConfig.local_filters.probability.is_active),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",null==e.widgetConfig.local_filters||null==e.widgetConfig.local_filters.shift?null:e.widgetConfig.local_filters.shift.is_active),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",null==e.widgetConfig.local_filters||null==e.widgetConfig.local_filters.shift?null:e.widgetConfig.local_filters.shift.is_active)}}function ia(e,t){1&e&&d["\u0275\u0275element"](0,"app-currency",35),2&e&&d["\u0275\u0275property"]("currencyList",t.$implicit.opportunity_value)("showActualAmount",!1)}let aa=(()=>{class e{constructor(e,t,n,i,a,o,r,l){this.salesDashBoardService=e,this.rolesService=t,this.fb=n,this.utilityService=i,this.dialog=a,this.snackBar=o,this.commaSep=r,this._toaster=l,this.isPlannedVisible=!0,this.isActualVisible=!0,this.isLastYearVisible=!0,this.isPlannedCumulativeVisible=!1,this.isActualCumulativeVisible=!1,this.isLoading=!0,this.isError=!1,this.fte_label="FTE",this.isDataLoading=!0,this.appliedFilterCount=0,this.currency_label="USD",this.customizePoint=e=>{if("Planned"==e.seriesName){if(!e.aggregationInfo.data[0].plannedTotalOpp||e.aggregationInfo.data[0].plannedTotalOpp<=0)return{visible:!1}}else if("Actual"==e.seriesName){if(!e.aggregationInfo.data[0].actualTotalOpp||e.aggregationInfo.data[0].actualTotalOpp<=0)return{visible:!1}}else if("Last Year Closure"==e.seriesName){if(!e.aggregationInfo.data[0].lastActualTotalOpp||e.aggregationInfo.data[0].lastActualTotalOpp<=0)return{visible:!1}}else if("Cumulative Planned"==e.seriesName){if(!e.aggregationInfo.data[0].cumulativePlannedTotalOpp||e.aggregationInfo.data[0].cumulativePlannedTotalOpp<=0)return{visible:!1}}else if(!e.aggregationInfo.data[0].cumulativeActualTotalOpp||e.aggregationInfo.data[0].cumulativeActualTotalOpp<=0)return{visible:!1}},this.customizeTooltip=e=>{let t=e.point.aggregationInfo.data[0];return{text:`${e.seriesName}<br/>Count: ${"Planned"==e.seriesName?t.plannedTotalOpp:"Actual"==e.seriesName?t.actualTotalOpp:"Last Year Closure"==e.seriesName?t.lastActualTotalOpp:"Cumulative Planned"==e.seriesName?t.cumulativePlannedTotalOpp:t.cumulativeActualTotalOpp}<br/>Value: ${this.commaSep.transform("Planned"==e.seriesName?t.planned_opportunity_val:"Actual"==e.seriesName?t.actual_opportunity_val:"Last Year Closure"==e.seriesName?t.last_actual_opportunity_val:"Cumulative Planned"==e.seriesName?t.cumulative_planned_opportunity_val:t.cumulative_actual_opportunity_val,this.currency_label,!0,!0)}<br/>FTE Count: ${e.value}`}},this.customizePercText=e=>e.valueText+"%",this.configForm=this.fb.group({actual:["",c.H.required],planned:["",c.H.required],salesOwner:[""],productCategory:[""],probability:[""],shift:[""]})}getReport(e){var t,n,i,a,o;this.isDataLoading=!0,this.current_filter=e;let r=JSON.parse(JSON.stringify(e)),l=this.rolesService.getUserRoleOrgCodes("Opportunities"),c=s()(null===(t=r.filter(e=>1==e.filter_id)[0])||void 0===t?void 0:t.applied_filter.startDate).format("YYYY-MM-DD"),d=s()(null===(n=r.filter(e=>1==e.filter_id)[0])||void 0===n?void 0:n.applied_filter.endDate).format("YYYY-MM-DD"),p=r.filter(e=>1==e.filter_id);r=r.filter(e=>1!=e.filter_id),p[0].applied_filter.startDate=s()(c).format("YYYY-MM-DD"),p[0].applied_filter.endDate=s()(d).format("YYYY-MM-DD"),r.push(...p),0==r.find(e=>6===e.filter_id).applied_filter.length?(this.configForm.patchValue({productCategory:null===(o=null===(a=null===(i=this.widgetConfig)||void 0===i?void 0:i.local_filters)||void 0===a?void 0:a.productCategory)||void 0===o?void 0:o.default_filters}),r.find(e=>6===e.filter_id).applied_filter=this.configForm.value.productCategory):this.configForm.patchValue({productCategory:r.find(e=>6===e.filter_id).applied_filter}),this.salesDashBoardService.getFTETrendReport(l,r,c,d).subscribe(e=>{this.data=e.messData,this.isLoading=!1,this.isDataLoading=!1,this.isError=!1},e=>{this.isLoading=!1,this.isDataLoading=!1,this.isError=!0}),this.appliedFilterCount=0;for(let s of r)this.appliedFilterCount+="date"==s.filter_type?s.applied_filter.startDate?1:0:s.applied_filter.length>0?1:0}checkForAdmin(){return this.salesDashBoardService.checkForAdmin()}legendClickHandler(e){const t=e.target,n=t.isVisible();"Planned"==t.name?this.isPlannedVisible=!n:"Actual"==t.name?this.isActualVisible=!n:"Last Year Closure"==t.name?this.isLastYearVisible=!n:"Cumulative Planned"==t.name?this.isPlannedCumulativeVisible=!n:"Cumulative Actual"==t.name&&(this.isActualCumulativeVisible=!n)}viewHierarchyProduct(e){return Object(o.c)(this,void 0,void 0,(function*(){let t=[],i=[],a={};yield this.salesDashBoardService.getProductHeirarchy().then(e=>{t=e},e=>{console.log(e)}),yield this.salesDashBoardService.getProductViewHierarchyLabel(36,"Opportunity").then(e=>{"S"==e.messType&&(i=e.result,a=e.result_details)},e=>{console.log(e)});const{HeirarchyViewComponent:r}=yield n.e(986).then(n.bind(null,"zS0E"));this.dialog.open(r,{height:"100%",width:"85%",position:{right:"0px"},data:{heading:"Line of Business - "+e.opportunity_name+" ("+e.opportunity_id+")",heirarchy_data:t,column_config:i,application_object:a.application_object,application_id:a.application_id,patchData:""!=e.product_category_value&&null!=e.product_category_value?"string"==typeof e.product_category_value?JSON.parse(e.product_category_value):e.product_category_value:[],returnAsArray:!1}}).afterClosed().subscribe(t=>Object(o.c)(this,void 0,void 0,(function*(){null!=t&&(console.log(t),"S"==t.messType&&this.salesDashBoardService.updateProductCategory(t.data,e.opportunity_id).then(e=>{"S"==e.messType&&(this.utilityService.showMessage("Updated Successfully!","Dismiss",3e3),this.getReport(this.current_filter))},e=>{this.utilityService.showMessage("Unable to update Product Category!","Dismiss",3e3)}))})))}))}openSummary(e){let t,n,i,a=e.target.series.getValueFields()[0],o=s()(e.target.aggregationInfo.data[0].month_formatted).format("YYYY-MM-DD"),r=!1,l="Planned"==e.target.series.name?e.target.aggregationInfo.data[0].plannedTotalOpp:"Actual"==e.target.series.name?e.target.aggregationInfo.data[0].actualTotalOpp:"Last Year Closure"==e.target.series.name?e.target.aggregationInfo.data[0].lastActualTotalOpp:"Cumulative Planned"==e.target.series.name?e.target.aggregationInfo.data[0].cumulativePlannedTotalOpp:e.target.aggregationInfo.data[0].cumulativeActualTotalOpp;if("last_actual_value_fte"==a)o=s()(o,"YYYY-MM-DD").subtract(1,"year").format("YYYY-MM-DD"),t=this.configForm.value.actual,n="Actual",r=!0,i=e.target.aggregationInfo.data[0].last_actual_value_fte;else if("opportunity_actual_fte"==a)t=this.configForm.value.actual,n="Actual",i=e.target.aggregationInfo.data[0].opportunity_actual_fte;else{if("opportunity_planned_value_fte"!=a)return;t=this.configForm.value.planned,n="Planned",i=e.target.aggregationInfo.data[0].opportunity_planned_value_fte}let c={columnConfig:this.summaryColumnConfig,useDxTable:this.widgetConfig.component_config.useDxTable,dataObservername:"fte_summary_observable",title:`${n} Opportunities - ${s()(o,"YYYY-MM-DD").format("MMMM YYYY")}`,subtitle:"Total opportunities: "+l,subtitle1:"Total FTE: "+i,summaryFor:o};this.salesDashBoardService.getFTEDataSummary(o,t,this.current_filter,r),this.dialog.open(ge,{height:"80%",width:"80%",data:c,animation:{entryAnimation:{keyframes:[{transform:"translateY(-100%)"},{transform:"translateY(0)"}],keyframeAnimationOptions:{duration:250,easing:"ease-in-out"}},exitAnimation:{keyframes:[{transform:"translateY(0)"},{transform:"translateY(-100%)"}],keyframeAnimationOptions:{duration:250,easing:"ease-in-out"}}}})}ngOnInit(){this.widgetConfig=this.salesDashBoardService.getConfigFor(this.widget_id),this.salesDashBoardService.clearFilter$.subscribe(()=>{this.configForm.patchValue({salesOwner:"",probability:"",shift:""})}),this.salesDashBoardService.getSalesStatusMaster().subscribe(e=>{this.salesStatus=e},e=>{this.salesStatus=[],console.error(e)}),this.salesDashBoardService.getSalesOwner().subscribe(e=>{this.salesOwnerList=e},e=>{this.salesOwnerList=[],console.error(e)}),this.salesDashBoardService.getProductHeirarchy().then(e=>{this.productCategoryList=e}).catch(e=>{this.productCategoryList=[],console.error(e)}),this.salesDashBoardService.getSalesProbability().subscribe(e=>{this.probabilityList=e},e=>{this.probabilityList=[],console.error(e)}),this.salesDashBoardService.getShiftList().subscribe(e=>{this.shiftList=e},e=>{this.shiftList=[],console.error(e)}),this.configForm.patchValue({actual:this.widgetConfig.status_config.actual,planned:this.widgetConfig.status_config.planned})}openConfig(){this.configDialogRef=this.dialog.open(this.configFormTemplate,{minHeight:"70vh",minWidth:"35rem"})}updateStatusConfig(){this.configForm.valid?this.salesDashBoardService.updateWidgetStatusConfig(this.configForm.value,this.widget_id).subscribe(e=>{"E"==e.mesType?(this.snackBar.open("ERROR:Edit failed. Try Again","",{duration:3e3}),this.configDialogRef.close()):(this.current_filter.find(e=>4===e.filter_id).applied_filter=this.configForm.get("salesOwner").value,this.current_filter.find(e=>6===e.filter_id).applied_filter=this.configForm.get("productCategory").value,this.current_filter.find(e=>7===e.filter_id).applied_filter=this.configForm.get("probability").value,this.current_filter.find(e=>8===e.filter_id).applied_filter=this.configForm.get("shift").value,this.getReport(this.current_filter),this.snackBar.open("Successfully edited data!","Dismiss",{duration:3e3}),this.configDialogRef.close())},e=>{this.snackBar.open("ERROR:Edit failed. Try Again","",{duration:3e3}),this.configDialogRef.close()}):this._toaster.showWarning("Kindly select all mandatory fields!!","",1e3)}openInNew(e){window.open(`${window.location.origin}/main/opportunities/${e.opportunity_id}/${this.utilityService.encodeURIComponent(e.opportunity_name)}`)}cancelClicked(){this.configDialogRef.close()}get summaryColumnConfig(){var e,t,n,i;return[{column_name:"OPPORTUNITY NAME",column_type:"text",data_field:"opportunity_name",text_overflow:!0},{column_name:"ACCOUNT",column_type:"text",data_field:"customer_name",text_overflow:!0,gutter_space:"5%"},...(null===(e=this.widgetConfig.summaryColumnConfig)||void 0===e?void 0:e.salesOrganisation)?[{column_name:(null===(t=this.widgetConfig.summaryColumnConfig)||void 0===t?void 0:t.label)||"SALES ORGANIZATION",column_type:"text",data_field:"sales_unit_name",text_overflow:!0,gutter_space:"5%"}]:[],...(null===(n=this.widgetConfig.summaryColumnConfig)||void 0===n?void 0:n.salesRegion)?[{column_name:(null===(i=this.widgetConfig.summaryColumnConfig)||void 0===i?void 0:i.label)||"SALES REGION",column_type:"text",data_field:"sales_region_name",text_overflow:!0,gutter_space:"5%"}]:[],{column_name:"SALES OWNER",column_type:"text",data_field:"sales_owner",text_overflow:!0,gutter_space:"5%"},{column_name:"FTE",column_type:"text",data_field:"fte_count",text_overflow:!0,gutter_space:"5%"},{column_name:"VALUE",column_type:"template",template_ref:this.currency,gutter_space:"5%",data_field:"opportunity_val_usd"},{column_name:"CURRENT EXPECTED CLOSURE DATE",column_type:"date",data_field:"processing_end_date",gutter_space:"5%"},{column_name:"ACTUAL CLOSURE DATE",column_type:"date",data_field:"actual_closure_date",gutter_space:"5%"},{column_name:"STATUS",column_type:"text",data_field:"status_name",gutter_space:"5%"},{column_name:"",column_type:"actions",actions:[{action_name:"Open in new tab",action_icon:"open_in_new",action_click:this.openInNew.bind(this)},{action_click:this.viewHierarchyProduct.bind(this),action_icon:"business_center",action_name:"View Hierarchy"}]}]}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](v),d["\u0275\u0275directiveInject"](_.a),d["\u0275\u0275directiveInject"](c.i),d["\u0275\u0275directiveInject"](ue.a),d["\u0275\u0275directiveInject"](fe.a),d["\u0275\u0275directiveInject"](he.a),d["\u0275\u0275directiveInject"](g),d["\u0275\u0275directiveInject"](_e.a))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-fte-trend-widget"]],viewQuery:function(e,t){if(1&e&&(d["\u0275\u0275viewQuery"](Yi,!0,d.TemplateRef),d["\u0275\u0275viewQuery"](Ai,!0)),2&e){let e;d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.configFormTemplate=e.first),d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.currency=e.first)}},inputs:{widget_id:"widget_id"},features:[d["\u0275\u0275ProvidersFeature"]([g])],decls:9,vars:2,consts:[[1,"total-activities-card"],["class","loading-wrapper",4,"ngIf"],[4,"ngIf"],["currency",""],["config",""],[1,"loading-wrapper"],[1,"loading"],[1,"row","p-0","justify-content-between"],[1,"title"],[1,"iconbtn",2,"margin-left","5px"],[2,"font-size","larger",3,"matTooltip"],["class","iconbtn pr-3","style","cursor: pointer",3,"click",4,"ngIf"],[3,"dataSource","customizePoint","onLegendClick","onPointClick"],["valueField","opportunity_planned_value_fte","type","bar","name","Planned",3,"visible","minBarSize"],["method","avg",3,"enabled"],["pointsAggregationSettings",""],["valueField","opportunity_actual_fte","type","bar","name","Actual",3,"visible","minBarSize"],["valueField","last_actual_value_fte","type","bar","name","Last Year Closure",3,"visible","minBarSize"],["valueField","opportunity_cummulative_value_fte","name","Cumulative Planned",3,"visible"],["valueField","opportunity_cummulative_actual_fte","name","Cumulative Actual",3,"visible"],["argumentField","month_formatted"],["seriesSettings",""],["name","total","position","left",3,"allowDecimals"],[3,"visible"],[3,"text"],["argumentType","datetime","aggregatedPointsPosition","crossTicks","aggregationInterval","month",3,"valueMarginsEnabled"],["argumentAxisSettings",""],["format","monthAndYear"],[3,"months"],["verticalAlignment","top","horizontalAlignment","center"],["zIndex","1",3,"enabled","customizeTooltip"],[3,"enabled","fileName"],["class","no-data",4,"ngIf"],[1,"iconbtn","pr-3",2,"cursor","pointer",3,"click"],[1,"no-data"],["type","small",3,"currencyList","showActualAmount"],[1,"p-4",2,"height","100%","width","100%"],[3,"formGroup","ngSubmit"],[1,"filter-wrapper"],[1,"static-wrapper"],[3,"matTooltip"],["class","label-filter",4,"ngIf"],["class","col-12",4,"ngIf"],[1,"dyno-wrapper"],["class","label-filter mt-2",4,"ngIf"],[1,"row","pt-3"],["mat-button","","type","button",2,"font-size","13px","color","#45546e","font-weight","bold !important","background-color","transparent !important","border","1px solid #45546e","display","flex","justify-content","center","align-items","center","margin-right","3%","width","1rem","height","35px","margin-left","auto",3,"click"],["mat-button","","type","submit",1,"btn-color",2,"font-size","13px","color","#ffff","font-weight","bold !important","display","flex","justify-content","center","align-items","center","margin-right","3%","width","1rem","height","35px"],[1,"label-filter"],[1,"col-12"],["required","true","placeholder","Sales status","formControlName","actual",3,"list"],["required","true","placeholder","Sales status","formControlName","planned",1,"create-account-field-inputsearch",3,"list"],[1,"label-filter","mt-2"],["placeholder","Sales Owner","formControlName","salesOwner",1,"create-account-field-inputsearch",3,"list"],["formControlName","productCategory","placeholder","Product Category",1,"create-account-field-inputsearch",3,"config","checklistData"],["placeholder","Probability","formControlName","probability",1,"create-account-field-inputsearch",3,"list"],["placeholder","Shift","formControlName","shift",1,"create-account-field-inputsearch",3,"list"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"mat-card",0),d["\u0275\u0275template"](1,Ri,3,0,"div",1),d["\u0275\u0275template"](2,ji,40,31,"div",2),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](3,Bi,1,2,"ng-template",null,3,d["\u0275\u0275templateRefExtractor"]),d["\u0275\u0275template"](5,na,24,14,"ng-template",null,4,d["\u0275\u0275templateRefExtractor"]),d["\u0275\u0275template"](7,ia,1,2,"ng-template",null,3,d["\u0275\u0275templateRefExtractor"])),2&e&&(d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.isLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!t.isLoading))},directives:[ve.a,i.NgIf,C.a,y.a,ye.a,U.E,U.X,U.Bb,U.R,U.uc,U.Ee,U.eb,U.Oc,U.Be,U.Qc,U.Ie,U.Sb,Ce.a,c.J,c.w,c.n,xe.a,ke,c.F,c.v,c.l,Ge],styles:[".total-activities-card[_ngcontent-%COMP%]{margin:7px;height:100%;min-height:300px;padding:14px!important;color:#45546e;box-shadow:0 6px 18px rgba(0,0,0,.1)}.total-activities-card[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-weight:500;display:flex;font-size:14px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;padding:0!important;color:#45546e}  #pie{height:80%;width:100%}#overlay[_ngcontent-%COMP%]{position:absolute;display:block;width:100%;height:100%;top:0;left:0;right:0;bottom:0;background-color:rgba(0,0,0,.1);z-index:3;cursor:pointer}.no-data[_ngcontent-%COMP%]{display:flex;height:80%;font-size:small;vertical-align:center;justify-content:center;align-items:center;width:100%}.iconbtn[_ngcontent-%COMP%]{color:#7b7b7b!important;padding:0;height:-moz-fit-content;height:fit-content}.btn-color[_ngcontent-%COMP%]{background-color:var(--primaryColor)}.filter-wrapper[_ngcontent-%COMP%]{height:auto;margin:0 auto .5rem;width:30rem}.filter-wrapper[_ngcontent-%COMP%]   .label-filter[_ngcontent-%COMP%]{margin-left:3%;font-size:14px;color:#45546e;font-weight:500;font-family:Roboto}.filter-wrapper[_ngcontent-%COMP%]   .static-wrapper[_ngcontent-%COMP%]{border:1px solid #e0e0e0;border-radius:5px;padding:.35rem;background-color:#eceff3}.filter-wrapper[_ngcontent-%COMP%]   .static-wrapper[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{display:flex;margin-left:auto!important;font-size:19px;color:#45546e;cursor:pointer;margin-bottom:-5%}.filter-wrapper[_ngcontent-%COMP%]   .dyno-wrapper[_ngcontent-%COMP%]{overflow-y:auto!important;height:35vh;margin-top:1%}"]}),e})();const oa=["currency"],ra=["config"];function la(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",6),d["\u0275\u0275elementStart"](1,"div",7),d["\u0275\u0275text"](2,"Loading..."),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function sa(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",24),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](2).openConfig()})),d["\u0275\u0275elementStart"](1,"mat-icon"),d["\u0275\u0275text"](2,"settings"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}}function ca(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementStart"](1,"div"),d["\u0275\u0275elementStart"](2,"p"),d["\u0275\u0275text"](3),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](4,"p"),d["\u0275\u0275text"](5),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](6,"p"),d["\u0275\u0275text"](7),d["\u0275\u0275pipe"](8,"commaSeparation"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](3),d["\u0275\u0275textInterpolate"](e.argumentText),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"]("Count: ",e.point.data.count,""),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate2"]("",n.widgetConfig.component_config.finance_text,": ",d["\u0275\u0275pipeBind3"](8,4,e.point.data.val,n.currencyCode,!0),"")}}const da=function(e){return{show:e,enabled:!0}},pa=function(){return["SVG"]};function ma(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementStart"](1,"div"),d["\u0275\u0275elementStart"](2,"div",8),d["\u0275\u0275elementStart"](3,"div",9),d["\u0275\u0275elementStart"](4,"p"),d["\u0275\u0275text"](5),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](6,"div",10),d["\u0275\u0275elementStart"](7,"mat-icon",11),d["\u0275\u0275text"](8,"info"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](9,sa,3,0,"div",12),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](10,"span",13),d["\u0275\u0275text"](11),d["\u0275\u0275pipe"](12,"commaSeparation"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](13,"span",14),d["\u0275\u0275elementStart"](14,"p"),d["\u0275\u0275text"](15),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](16,"div"),d["\u0275\u0275elementStart"](17,"dx-pie-chart",15),d["\u0275\u0275listener"]("onPointClick",(function(t){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().openSummary(t)})),d["\u0275\u0275element"](18,"dxo-legend",16),d["\u0275\u0275elementStart"](19,"dxi-series",17),d["\u0275\u0275elementStart"](20,"dxo-label",18),d["\u0275\u0275element"](21,"dxo-font",19),d["\u0275\u0275element"](22,"dxo-connector",20),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](23,"dxo-tooltip",21),d["\u0275\u0275element"](24,"dxo-export",22),d["\u0275\u0275template"](25,ca,9,8,"div",23),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](5),d["\u0275\u0275textInterpolate"](e.widgetConfig.widget_name),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("matTooltip",e.widgetConfig.widget_info),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",e.checkForAdmin()),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate"](d["\u0275\u0275pipeBind4"](12,20,e.total_value,e.currencyCode,!0,!0)),d["\u0275\u0275advance"](4),d["\u0275\u0275textInterpolate1"]("Total ",e.widgetConfig.component_config.finance_text,""),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("dataSource",e.data)("animation",!0)("loadingIndicator",d["\u0275\u0275pureFunction1"](25,da,e.isLoading)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("visible",!1),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("valueField",e.val),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("visible",!0)("customizeText",e.customizeLabel),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("visible",!0)("width",.5),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("enabled",!0),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("enabled",!0)("formats",d["\u0275\u0275pureFunction0"](27,pa))("printingEnabled",!1)("fileName",null==e.widgetConfig?null:e.widgetConfig.widget_name),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("dxTemplateOf","content")}}function ga(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",25),d["\u0275\u0275elementStart"](1,"div",26),d["\u0275\u0275text"](2,"No Data"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function ua(e,t){1&e&&d["\u0275\u0275element"](0,"app-currency",27),2&e&&d["\u0275\u0275property"]("currencyList",t.$implicit.opportunity_value)("showActualAmount",!1)}function fa(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",28),d["\u0275\u0275elementStart"](1,"form",29),d["\u0275\u0275listener"]("ngSubmit",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().updateStatusConfig()})),d["\u0275\u0275elementStart"](2,"mat-icon",30),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().closeConfig()})),d["\u0275\u0275text"](3,"close"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](4,"div"),d["\u0275\u0275text"](5,"Choose Status"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](6,"div",31),d["\u0275\u0275element"](7,"app-multi-select-search2",32),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](8,"div",33),d["\u0275\u0275elementStart"](9,"button",34),d["\u0275\u0275text"](10," Submit "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("formGroup",e.configForm),d["\u0275\u0275advance"](6),d["\u0275\u0275property"]("list",e.salesStatus)}}let ha=(()=>{class e{constructor(e,t,n,i,a,o,r){this.salesDashBoardService=e,this.rolesService=t,this.dialog=n,this.fb=i,this.utilityService=a,this.snackBar=o,this.commaSep=r,this.isLoading=!0,this.isError=!1,this.tempStatusValue="",this.currencyCode="USD",this.configForm=this.fb.group({visible:[""]}),this.customizeLabel=this.customizeLbl.bind(this)}ngOnInit(){this.widgetConfig=this.salesDashBoardService.getConfigFor(this.widget_id),this.salesDashBoardService.getSalesStatusMaster().subscribe(e=>{this.salesStatus=e},e=>{this.salesStatus=[],console.error(e)})}customizeLbl(e){const t="Count: "+e.point.data.count,n=`${this.widgetConfig.component_config.finance_text||"Value"}: ${this.commaSep.transform(e.point.data.val,this.currencyCode,!0,!0)}`;return this.data.length>28?`${e.argumentText} | ${t}`:`${e.argumentText} | ${t} | ${n}`}openConfig(){this.tempStatusValue=this.configForm.value.visible,this.configDialogRef=this.dialog.open(this.configFormTemplate,{height:"40%",width:"45%",disableClose:!0})}checkForAdmin(){return this.salesDashBoardService.checkForAdmin()}updateStatusConfig(){this.configForm.valid&&(this.getReport(this.current_filter),this.snackBar.open("Successfully edited data!","Dismiss",{duration:3e3}),this.configDialogRef.close())}closeConfig(){this.configForm.patchValue({visible:this.tempStatusValue}),this.configDialogRef.close()}getReport(e){var t,n;this.current_filter=e;let i=this.configForm.value.visible&&this.configForm.value.visible.length>0?this.configForm.value.visible:[],a=this.rolesService.getUserRoleOrgCodes("Opportunities"),o=l(null===(t=e.filter(e=>1==e.filter_id)[0])||void 0===t?void 0:t.applied_filter.startDate).format("YYYY-MM-DD"),r=l(null===(n=e.filter(e=>1==e.filter_id)[0])||void 0===n?void 0:n.applied_filter.endDate).format("YYYY-MM-DD"),s=e.filter(e=>1==e.filter_id);e=e.filter(e=>1!=e.filter_id),s[0].applied_filter.startDate=l(o).format("YYYY-MM-DD"),s[0].applied_filter.endDate=l(r).format("YYYY-MM-DD"),e.push(...s),this.salesDashBoardService.getFinanceDataForDashboard(this.widget_id,a,e,o,r,i).subscribe(e=>{this.data=e.data,this.total_value=e.total_value,this.currencyCode=e.currency_code,this.isLoading=!1,this.isError=!1},e=>{this.isError=!0,this.isLoading=!1,this.data=[]})}openSummary(e){var t,n;let i=this.configForm.value.visible&&this.configForm.value.visible.length>0?this.configForm.value.visible:[],a=e.target.argument,o=this.widgetConfig.group_by,r=this.widgetConfig.component_config.finance_text||"",s=l(null===(t=this.current_filter.filter(e=>1==e.filter_id)[0])||void 0===t?void 0:t.applied_filter.startDate).format("YYYY-MM-DD"),c=l(null===(n=this.current_filter.filter(e=>1==e.filter_id)[0])||void 0===n?void 0:n.applied_filter.endDate).format("YYYY-MM-DD"),d={columnConfig:this.summaryColumnConfig,dataObservername:"finance_data_summary_observable",title:`Opportunities with ${a} as ${o} in Active Quote`,subtitle:`Total ${r} ${this.commaSep.transform(e.target.data.val,this.currencyCode,!0,!0)}`,useDxTable:this.widgetConfig.component_config.useDxTable,summaryFor:a};this.salesDashBoardService.getFinanceDataForDashboardSummary(this.widget_id,e.target.data.id,this.current_filter,s,c,e.target.data.name,i),this.dialog.open(ge,{height:"80%",width:"80%",data:d,animation:{entryAnimation:{keyframes:[{transform:"translateY(-100%)"},{transform:"translateY(0)"}],keyframeAnimationOptions:{duration:250,easing:"ease-in-out"}},exitAnimation:{keyframes:[{transform:"translateY(0)"},{transform:"translateY(-100%)"}],keyframeAnimationOptions:{duration:250,easing:"ease-in-out"}}}})}viewHierarchyProduct(e){return Object(o.c)(this,void 0,void 0,(function*(){let t=[],i=[],a={};yield this.salesDashBoardService.getProductHeirarchy().then(e=>{t=e},e=>{console.log(e)}),yield this.salesDashBoardService.getProductViewHierarchyLabel(36,"Opportunity").then(e=>{"S"==e.messType&&(i=e.result,a=e.result_details)},e=>{console.log(e)});const{HeirarchyViewComponent:r}=yield n.e(986).then(n.bind(null,"zS0E"));this.dialog.open(r,{height:"100%",width:"85%",position:{right:"0px"},data:{heading:"Line of Business - "+e.opportunity_name+" ("+e.opportunity_id+")",heirarchy_data:t,column_config:i,application_object:a.application_object,application_id:a.application_id,patchData:""!=e.product_category_value&&null!=e.product_category_value?"string"==typeof e.product_category_value?JSON.parse(e.product_category_value):e.product_category_value:[],returnAsArray:!1}}).afterClosed().subscribe(t=>Object(o.c)(this,void 0,void 0,(function*(){null!=t&&"S"==t.messType&&this.salesDashBoardService.updateProductCategory(t.data,e.opportunity_id).then(e=>{"S"==e.messType&&(this.utilityService.showMessage("Updated Successfully!","Dismiss",3e3),this.getReport(this.current_filter))},e=>{this.utilityService.showMessage("Unable to update Product Category!","Dismiss",3e3)})})))}))}openInNew(e){window.open(`${window.location.origin}/main/opportunities/${e.opportunity_id}/${this.utilityService.encodeURIComponent(e.opportunity_name)}`)}get summaryColumnConfig(){var e,t,n,i;return[{column_name:"OPPORTUNITY NAME",column_type:"text",data_field:"opportunity_name",text_overflow:!0},{column_name:"ACCOUNT",column_type:"text",data_field:"customer_name",text_overflow:!0,gutter_space:"5%"},...(null===(e=this.widgetConfig.summaryColumnConfig)||void 0===e?void 0:e.salesOrganisation)?[{column_name:(null===(t=this.widgetConfig.summaryColumnConfig)||void 0===t?void 0:t.label)||"SALES ORGANIZATION",column_type:"text",data_field:"sales_unit_name",text_overflow:!0,gutter_space:"5%"}]:[],...(null===(n=this.widgetConfig.summaryColumnConfig)||void 0===n?void 0:n.salesRegion)?[{column_name:(null===(i=this.widgetConfig.summaryColumnConfig)||void 0===i?void 0:i.label)||"SALES REGION",column_type:"text",data_field:"sales_region_name",text_overflow:!0,gutter_space:"5%"}]:[],{column_name:"SALES OWNER",column_type:"text",data_field:"sales_owner",text_overflow:!0,gutter_space:"5%"},{column_name:"OPPORTUNITY VALUE",column_type:"template",template_ref:this.currency,gutter_space:"5%",data_field:"opportunity_val_usd"},{column_name:"CURRENT EXPECTED CLOSURE DATE",column_type:"date",data_field:"processing_end_date",gutter_space:"5%"},{column_name:"STATUS",column_type:"text",data_field:"status_name",gutter_space:"5%"},{column_name:"",column_type:"actions",actions:[{action_name:"Open in new tab",action_icon:"open_in_new",action_click:this.openInNew.bind(this)}]}]}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](v),d["\u0275\u0275directiveInject"](_.a),d["\u0275\u0275directiveInject"](fe.a),d["\u0275\u0275directiveInject"](c.i),d["\u0275\u0275directiveInject"](ue.a),d["\u0275\u0275directiveInject"](he.a),d["\u0275\u0275directiveInject"](g))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-financial-pie-chart"]],viewQuery:function(e,t){if(1&e&&(d["\u0275\u0275viewQuery"](oa,!0),d["\u0275\u0275viewQuery"](ra,!0,d.TemplateRef)),2&e){let e;d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.currency=e.first),d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.configFormTemplate=e.first)}},inputs:{widget_id:"widget_id"},features:[d["\u0275\u0275ProvidersFeature"]([g])],decls:8,vars:3,consts:[[1,"total-activities-card"],["class","loading-wrapper",4,"ngIf"],[4,"ngIf"],["id","overlay",4,"ngIf"],["currency",""],["config",""],[1,"loading-wrapper"],[1,"loading"],[1,"row","p-0","justify-content-between"],[1,"title"],[1,"iconbtn",2,"margin-left","5px"],[2,"font-size","larger",3,"matTooltip"],["class","iconbtn pr-3","style","cursor: pointer",3,"click",4,"ngIf"],[1,"currency"],[2,"font-weight","400","display","flex"],["id","pieFD","resolveLabelOverlapping","shift","type","pie",3,"dataSource","animation","loadingIndicator","onPointClick"],[3,"visible"],["argumentField","name",3,"valueField"],["position","columns","columnCount","2",3,"visible","customizeText"],["size","10"],[3,"visible","width"],["contentTemplate","content","zIndex","1",3,"enabled"],[3,"enabled","formats","printingEnabled","fileName"],[4,"dxTemplate","dxTemplateOf"],[1,"iconbtn","pr-3",2,"cursor","pointer",3,"click"],["id","overlay"],[1,"no-data"],["type","small",3,"currencyList","showActualAmount"],[1,"p-4",2,"height","100%","width","100%"],[3,"formGroup","ngSubmit"],[1,"close-icon",3,"click"],[1,"col-8","p-0"],["placeholder","Sales Status","formControlName","visible",3,"list"],[1,"col-12","row","justify-content-end"],["mat-button","","type","submit",1,"btn-color",2,"font-size","13px","color","#ffffff","font-weight","bold !important"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"mat-card",0),d["\u0275\u0275template"](1,la,3,0,"div",1),d["\u0275\u0275template"](2,ma,26,28,"div",2),d["\u0275\u0275template"](3,ga,3,0,"div",3),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](4,ua,1,2,"ng-template",null,4,d["\u0275\u0275templateRefExtractor"]),d["\u0275\u0275template"](6,fa,11,2,"ng-template",null,5,d["\u0275\u0275templateRefExtractor"])),2&e&&(d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.isLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!t.isLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",(null==t.data?null:t.data.length)<=0&&!t.isLoading))},directives:[ve.a,i.NgIf,C.a,y.a,_t.a,U.Qc,U.E,U.Oc,U.fc,U.Db,U.Ie,U.Sb,Q.d,Ce.a,c.J,c.w,c.n,ke,c.v,c.l,xe.a],pipes:[g],styles:[".total-activities-card[_ngcontent-%COMP%]{margin:7px;height:100%;min-height:35rem!important;padding:14px!important;color:#45546e;box-shadow:0 6px 18px rgba(0,0,0,.1)}.total-activities-card[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-weight:500;font-size:14px;display:flex;justify-content:space-between;line-height:16px;letter-spacing:.02em;text-transform:capitalize;padding:0!important;color:#45546e;height:-moz-fit-content;height:fit-content}.total-activities-card[_ngcontent-%COMP%]   .currency[_ngcontent-%COMP%]{font-weight:700;font-size:22px;color:#fa8c16;height:-moz-fit-content;height:fit-content}.hover-icon[_ngcontent-%COMP%]{font-size:medium;margin-left:3px}.hover-icon-wrapper[_ngcontent-%COMP%]:hover   .hover-icon[_ngcontent-%COMP%]{display:inline-block!important}[_nghost-%COMP%]     #pieFD{height:80%;width:100%}[_nghost-%COMP%]     .data-label{font-size:23px!important}#overlay[_ngcontent-%COMP%]{position:absolute;display:flex;justify-content:center;align-items:center;width:100%;height:100%;top:0;left:0;right:0;bottom:0;z-index:3}.iconbtn[_ngcontent-%COMP%]{color:#7b7b7b!important;padding:0;height:-moz-fit-content;height:fit-content;z-index:5}.close-icon[_ngcontent-%COMP%]{width:100%;display:flex;justify-content:end;font-size:20px;cursor:pointer}.btn-color[_ngcontent-%COMP%]{background-color:var(--primaryColor)}"]}),e})();function _a(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",3),d["\u0275\u0275elementStart"](1,"div",4),d["\u0275\u0275text"](2,"Loading..."),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function va(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"mat-option",24),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]().$implicit;d["\u0275\u0275property"]("value",e.value),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",e.name," ")}}function ya(e,t){if(1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275template"](1,va,2,2,"mat-option",23),d["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",""!==e.name&&null!=e.name)}}function Ca(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",3),d["\u0275\u0275elementStart"](1,"div",4),d["\u0275\u0275text"](2,"Loading..."),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function xa(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",25),d["\u0275\u0275text"](1,"No Data"),d["\u0275\u0275elementEnd"]())}const ba=function(){return["#976DD1","#FFBD3D"]},wa=function(){return{visible:!0}},Sa=function(){return{weight:600}},Oa=function(){return{type:"fixedPoint",precision:"0"}};function Ea(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementStart"](1,"div",5),d["\u0275\u0275elementStart"](2,"div",6),d["\u0275\u0275elementStart"](3,"p"),d["\u0275\u0275text"](4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](5,"div",7),d["\u0275\u0275elementStart"](6,"mat-icon",8),d["\u0275\u0275text"](7,"info"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](8,"div",9),d["\u0275\u0275elementStart"](9,"mat-form-field",10),d["\u0275\u0275elementStart"](10,"mat-label"),d["\u0275\u0275text"](11,"Duration"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](12,"mat-select",11),d["\u0275\u0275listener"]("selectionChange",(function(t){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().handleDurationChange(t.value)})),d["\u0275\u0275template"](13,ya,2,1,"ng-container",12),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](14,"div",13),d["\u0275\u0275template"](15,Ca,3,0,"div",1),d["\u0275\u0275elementStart"](16,"dx-chart",14),d["\u0275\u0275listener"]("onPointClick",(function(t){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().openSummary(t)})),d["\u0275\u0275element"](17,"dxi-series",15),d["\u0275\u0275element"](18,"dxi-series",15),d["\u0275\u0275elementStart"](19,"dxo-common-series-settings",16),d["\u0275\u0275element"](20,"dxo-label",17),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](21,"dxo-argument-axis"),d["\u0275\u0275element"](22,"dxo-tick",18),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](23,"dxi-value-axis",18),d["\u0275\u0275element"](24,"dxo-grid",18),d["\u0275\u0275element"](25,"dxo-label",18),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](26,"dxo-argument-axis",18),d["\u0275\u0275element"](27,"dxo-legend",19),d["\u0275\u0275element"](28,"dxo-tooltip",20),d["\u0275\u0275element"](29,"dxo-export",21),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](30,xa,2,0,"div",22),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](4),d["\u0275\u0275textInterpolate"](e.widgetConfig.widget_name),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("matTooltip",e.widgetConfig.widget_info),d["\u0275\u0275advance"](6),d["\u0275\u0275property"]("value",e.selectedDuration),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngForOf",e.durationData),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",e.isDataLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275styleProp"]("display",e.isDataLoading&&e.data.length>0?"none":"block"),d["\u0275\u0275property"]("dataSource",e.data)("palette",d["\u0275\u0275pureFunction0"](30,ba)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("valueField","totalQualifiedOpportunities")("name","Qualified Opportunities")("label",d["\u0275\u0275pureFunction0"](31,wa)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("valueField","totalUnqualifiedOpportunities")("name","Unqualified Opportunities")("label",d["\u0275\u0275pureFunction0"](32,wa)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("barPadding",0)("barGroupPadding",0),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("visible",!0)("font",d["\u0275\u0275pureFunction0"](33,Sa))("format",d["\u0275\u0275pureFunction0"](34,Oa)),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("visible",!1),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("visible",!1),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("visible",!1),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("visible",!0),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("visible",!1),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("enabled",!0)("customizeTooltip",e.customizeTooltip),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("enabled",!0)("fileName",null==e.widgetConfig?null:e.widgetConfig.widget_name),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.data.length<=0)}}let Da=(()=>{class e{constructor(e,t,n){this.salesDashBoardService=e,this.utilityService=t,this.dialog=n,this.widgetConfig=[],this.isLoading=!0,this.isDataLoading=!1,this.isError=!1,this.currency_label="USD",this.currentOpportunityIds=[],this.data=[],this.durationData=[],this.palette=[],this.widget_status_config={}}ngOnInit(){this.widgetConfig=this.salesDashBoardService.getConfigFor(this.widget_id),this.widget_status_config=this.widgetConfig.widget_status_config,this.durationData=this.widgetConfig.durationData,this.selectedDuration=this.widgetConfig.defaultValues.selectedDuration||"year"}handleDurationChange(e){console.log("Selected Duration:",e),this.selectedDuration=e,this.getReport(this.current_filter)}getReport(e){var t,n;this.isDataLoading=!0,this.current_filter=e;let i=s()(null===(t=e.filter(e=>1==e.filter_id)[0])||void 0===t?void 0:t.applied_filter.startDate).format("YYYY-MM-DD"),a=s()(null===(n=e.filter(e=>1==e.filter_id)[0])||void 0===n?void 0:n.applied_filter.endDate).format("YYYY-MM-DD"),o=e.filter(e=>1==e.filter_id);e=e.filter(e=>1!=e.filter_id),o[0].applied_filter.startDate=s()(i).format("YYYY-MM-DD"),o[0].applied_filter.endDate=s()(a).format("YYYY-MM-DD"),delete o[0].applied_filter.startDate,delete o[0].applied_filter.endDate,e.push(...o),this.salesDashBoardService.getPipelineGrowthByStatus(e,this.selectedDuration,this.widget_status_config).subscribe(e=>{this.data=e.messData.chart_data,this.isLoading=!1,this.isDataLoading=!1,this.isError=!1},e=>{this.isLoading=!1,this.isDataLoading=!1,this.isError=!0})}openSummary(e){let t=[],n=e.target.argument,i=e.target.series.getValueFields()[0],a={columnConfig:this.summaryColumnConfig,useDxTable:this.widgetConfig.component_config.useDxTable,dataObservername:"pipeline_owner_observable",title:"Pipeline Growth By Status",subtitle:"Total opportunities: "+("totalQualifiedOpportunities"==i?e.target.data.totalQualifiedOpportunities:e.target.data.totalUnqualifiedOpportunities),subtitle1:`${n} ${"totalQualifiedOpportunities"==i?" Qualified Opportunities":" Unqualified Opportunities"}`,summaryFor:t};this.salesDashBoardService.getPipelineGrowthSummary("totalQualifiedOpportunities"==i?e.target.data.qualifiedOpportunityIds:e.target.data.unqualifiedOpportunityIds,t),this.dialog.open(ge,{height:"80%",width:"80%",data:a,animation:{entryAnimation:{keyframes:[{transform:"translateY(-100%)"},{transform:"translateY(0)"}],keyframeAnimationOptions:{duration:250,easing:"ease-in-out"}},exitAnimation:{keyframes:[{transform:"translateY(0)"},{transform:"translateY(-100%)"}],keyframeAnimationOptions:{duration:250,easing:"ease-in-out"}}}})}customizeTooltip(e){const t=e.point.data;let n='<table style="border-collapse: collapse; width: 200px;">';return t.qualifiedOpportunities&&"Qualified Opportunities"===e.seriesName&&(n+=`\n        <tr>\n          <th colspan="2" style="border: 1px solid #ddd; padding: 6px; text-align: center;">Total Qualified - ${e.originalValue}</th>\n        </tr>\n      `,t.qualifiedOpportunities.forEach(e=>{n+=`\n          <tr>\n            <td style="border: 1px solid #ddd; padding: 4px;">${e.status_name}</td>\n            <td style="border: 1px solid #ddd; padding: 4px; text-align: center;">${e.count}</td>\n          </tr>\n        `})),t.unqualifiedOpportunities&&"Unqualified Opportunities"===e.seriesName&&(n+=`\n        <tr>\n          <th colspan="2" style="border: 1px solid #ddd; padding: 6px; text-align: center;">Total Unqualified - ${e.originalValue}</th>\n        </tr>\n      `,t.unqualifiedOpportunities.forEach(e=>{n+=`\n          <tr>\n            <td style="border: 1px solid #ddd; padding: 4px;">${e.status_name}</td>\n            <td style="border: 1px solid #ddd; padding: 4px; text-align: center;">${e.count}</td>\n          </tr>\n        `})),n+="</table>",{html:n}}get summaryColumnConfig(){return[{column_name:"OPPORTUNITY NAME",column_type:"text",data_field:"opportunity_name",text_overflow:!0},{column_name:"ACCOUNT NAME",column_type:"text",data_field:"customer_name",text_overflow:!0,gutter_space:"5%"},{column_name:"SALES OWNER",column_type:"text",data_field:"sales_owner",text_overflow:!0,gutter_space:"5%"},{column_name:this.widgetConfig.component_config.estOppCloseDate,column_type:"date",data_field:"processing_end_date",gutter_space:"5%"},{column_name:"ACTUAL CLOSURE DATE",column_type:"date",data_field:"actual_closure_date",gutter_space:"5%"},{column_name:"OPPORTUNITY VALUE",column_type:"text",gutter_space:"5%",data_field:"opportunity_val_usd"},{column_name:"STATUS",column_type:"text",data_field:"status_name",gutter_space:"5%"},{column_name:"",column_type:"actions",actions:[{action_name:"Open in new tab",action_icon:"open_in_new",action_click:this.openInNew.bind(this)}]}]}openInNew(e){window.open(`${window.location.origin}/main/opportunities/${e.opportunity_id}/${this.utilityService.encodeURIComponent(e.opportunity_name)}`)}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](v),d["\u0275\u0275directiveInject"](ue.a),d["\u0275\u0275directiveInject"](fe.a))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-pipeline-status-widget"]],inputs:{widget_id:"widget_id"},decls:3,vars:2,consts:[[1,"total-activities-card"],["class","loading-wrapper",4,"ngIf"],[4,"ngIf"],[1,"loading-wrapper"],[1,"loading"],[1,"row","p-0","justify-content-between","align-items-center"],[1,"title"],[1,"iconbtn",2,"margin-left","5px"],[2,"font-size","larger",3,"matTooltip"],[1,"right-elements"],["appearance","outline",1,"drop-down-filter"],["placeholder","Select Duration",2,"padding","0% 0% 5%",3,"value","selectionChange"],[4,"ngFor","ngForOf"],[1,"scrollable-chart-container"],[3,"dataSource","palette","onPointClick"],[3,"valueField","name","label"],["argumentField","duration","type","bar","hoverMode","onlyPoint","selectionMode","none",3,"barPadding","barGroupPadding"],["backgroundColor","none",3,"visible","font","format"],[3,"visible"],["verticalAlignment","top","horizontalAlignment","left","orientation","horizontal","itemTextPosition","right"],["zIndex","1",3,"enabled","customizeTooltip"],[3,"enabled","fileName"],["class","no-data",4,"ngIf"],[3,"value",4,"ngIf"],[3,"value"],[1,"no-data"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"mat-card",0),d["\u0275\u0275template"](1,_a,3,0,"div",1),d["\u0275\u0275template"](2,Ea,31,35,"div",2),d["\u0275\u0275elementEnd"]()),2&e&&(d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.isLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!t.isLoading))},directives:[ve.a,i.NgIf,C.a,y.a,Oe.c,Oe.g,Ee.c,i.NgForOf,ye.a,U.E,U.Bb,U.Oc,U.eb,U.Ae,U.R,U.uc,U.Qc,U.Ie,U.Sb,De.p],styles:[".total-activities-card[_ngcontent-%COMP%]{margin:7px;height:100%;min-height:300px;padding:14px!important;color:#45546e;box-shadow:0 6px 18px rgba(0,0,0,.1)}.total-activities-card[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-weight:500;display:flex;font-size:14px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;padding:0!important;color:#45546e}.total-activities-card[_ngcontent-%COMP%]   .right-elements[_ngcontent-%COMP%]{display:flex;justify-content:end;align-items:center;width:28rem;gap:5%}.total-activities-card[_ngcontent-%COMP%]   .right-elements[_ngcontent-%COMP%]   .toggle-btn[_ngcontent-%COMP%]{min-width:4rem;font-family:Plus Jakarta Sans!important}.total-activities-card[_ngcontent-%COMP%]   .right-elements[_ngcontent-%COMP%]   .btn-toggle-selected[_ngcontent-%COMP%]{font-size:12px!important;background-color:var(--primaryColor)!important;color:#fff}.total-activities-card[_ngcontent-%COMP%]   .right-elements[_ngcontent-%COMP%]   .icon-wrapper[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{cursor:pointer}.total-activities-card[_ngcontent-%COMP%]   .right-elements[_ngcontent-%COMP%]   .drop-down-filter[_ngcontent-%COMP%]{width:9rem;padding:0 1%;height:2rem;color:var(--Blue-Grey-100,#45546e);font-family:Roboto;font-size:13px;font-style:normal;font-weight:500;border-radius:4px;font-family:Plus Jakarta Sans!important}.total-activities-card[_ngcontent-%COMP%]   .right-elements[_ngcontent-%COMP%]   .drop-down-filter[_ngcontent-%COMP%]     .mat-form-field-flex{height:2.5rem;align-items:center}.total-activities-card[_ngcontent-%COMP%]   .right-elements[_ngcontent-%COMP%]   .drop-down-filter[_ngcontent-%COMP%]     .mat-form-field-infix{padding:12px 0!important}.total-activities-card[_ngcontent-%COMP%]   .right-elements[_ngcontent-%COMP%]   .drop-down-filter[_ngcontent-%COMP%]     .mat-form-field-outline{height:2rem!important;top:0!important}#overlay[_ngcontent-%COMP%]{position:absolute;display:block;width:100%;height:100%;top:0;left:0;right:0;bottom:0;background-color:rgba(0,0,0,.1);z-index:3;cursor:pointer}.no-data[_ngcontent-%COMP%]{display:flex;height:80%;font-size:small;vertical-align:center;justify-content:center;align-items:center;width:100%}.iconbtn[_ngcontent-%COMP%]{color:#7b7b7b!important;padding:0;height:-moz-fit-content;height:fit-content}.btn-color[_ngcontent-%COMP%]{background-color:var(--primaryColor)}.filter-wrapper[_ngcontent-%COMP%]{height:25rem;overflow-y:auto;margin:0 auto .5rem;width:30rem}.filter-wrapper[_ngcontent-%COMP%]   .label-filter[_ngcontent-%COMP%]{margin-left:3%;font-size:14px;color:#45546e;font-weight:500;font-family:Roboto}.filter-wrapper[_ngcontent-%COMP%]   .static-wrapper[_ngcontent-%COMP%]{border:1px solid #e0e0e0;border-radius:5px;padding:.35rem;background-color:#eceff3}.filter-wrapper[_ngcontent-%COMP%]   .static-wrapper[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{display:flex;margin-left:auto!important;font-size:19px;color:#45546e;cursor:pointer;margin-bottom:-5%}.scrollable-chart-container[_ngcontent-%COMP%]{width:100%;min-height:250px;overflow-x:auto;overflow-y:hidden}"]}),e})();var Ma=n("Iab2"),Pa=n("6K47"),Ia=n("jaxi");const Fa=["currency"];function Ta(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",3),d["\u0275\u0275elementStart"](1,"div",4),d["\u0275\u0275text"](2,"Loading..."),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}const ka=function(e){return{"btn-toggle-selected":e}};function La(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"mat-button-toggle",28),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]().$implicit,t=d["\u0275\u0275nextContext"](2);d["\u0275\u0275property"]("value",e.value)("ngClass",d["\u0275\u0275pureFunction1"](3,ka,t.selectedToggle===e.value)),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",e.name," ")}}function Ya(e,t){if(1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275template"](1,La,2,5,"mat-button-toggle",27),d["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.is_active)}}function Aa(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"mat-option",30),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]().$implicit;d["\u0275\u0275property"]("value",e.value),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",e.name," ")}}function Ra(e,t){if(1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275template"](1,Aa,2,2,"mat-option",29),d["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",""!==e.name&&null!=e.name)}}function Na(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"mat-icon",31),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](2).download()})),d["\u0275\u0275text"](1,"download"),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275classProp"]("disabled",e.downloading)}}function Va(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"mat-icon",32),d["\u0275\u0275text"](1,"download"),d["\u0275\u0275elementEnd"]())}function za(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",3),d["\u0275\u0275elementStart"](1,"div",4),d["\u0275\u0275text"](2,"Loading..."),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function ja(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",33),d["\u0275\u0275text"](1,"No Data"),d["\u0275\u0275elementEnd"]())}const Ba=function(){return["#5F6C81","#A0D911"]},$a=function(){return{visible:!0}},Ua=function(){return{weight:600,color:"#343a40"}},Qa=function(){return{type:"fixedPoint",precision:"0"}};function qa(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementStart"](1,"div",5),d["\u0275\u0275elementStart"](2,"div",6),d["\u0275\u0275elementStart"](3,"p"),d["\u0275\u0275text"](4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](5,"div",7),d["\u0275\u0275elementStart"](6,"mat-icon",8),d["\u0275\u0275text"](7,"info"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](8,"div",9),d["\u0275\u0275elementStart"](9,"mat-button-toggle-group",10),d["\u0275\u0275listener"]("change",(function(t){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().handleToggleChange(t.value)})),d["\u0275\u0275template"](10,Ya,2,1,"ng-container",11),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](11,"mat-form-field",12),d["\u0275\u0275elementStart"](12,"mat-label"),d["\u0275\u0275text"](13,"Duration"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](14,"mat-select",13),d["\u0275\u0275listener"]("selectionChange",(function(t){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().handleDurationChange(t.value)})),d["\u0275\u0275template"](15,Ra,2,1,"ng-container",11),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](16,"div",14),d["\u0275\u0275template"](17,Na,2,2,"mat-icon",15),d["\u0275\u0275template"](18,Va,2,0,"mat-icon",16),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](19,"div",17),d["\u0275\u0275template"](20,za,3,0,"div",1),d["\u0275\u0275elementStart"](21,"dx-chart",18),d["\u0275\u0275listener"]("onPointClick",(function(t){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().openSummary(t)})),d["\u0275\u0275element"](22,"dxi-series",19),d["\u0275\u0275element"](23,"dxi-series",19),d["\u0275\u0275elementStart"](24,"dxo-common-series-settings",20),d["\u0275\u0275element"](25,"dxo-label",21),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](26,"dxo-argument-axis"),d["\u0275\u0275element"](27,"dxo-tick",22),d["\u0275\u0275element"](28,"dxo-visual-range"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](29,"dxi-value-axis",22),d["\u0275\u0275element"](30,"dxo-grid",22),d["\u0275\u0275element"](31,"dxo-label",22),d["\u0275\u0275element"](32,"dxo-visual-range"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](33,"dxo-argument-axis",22),d["\u0275\u0275element"](34,"dxo-legend",23),d["\u0275\u0275element"](35,"dxo-tooltip",24),d["\u0275\u0275element"](36,"dxo-export",25),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](37,ja,2,0,"div",26),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](4),d["\u0275\u0275textInterpolate"](e.widgetConfig.widget_name),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("matTooltip",e.widgetConfig.widget_info),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("value",e.selectedToggle),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngForOf",e.toggleOptions),d["\u0275\u0275advance"](4),d["\u0275\u0275property"]("value",e.selectedDuration),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngForOf",e.durationData),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",!e.downloading),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.downloading),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",e.isDataLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275styleProp"]("display",e.isDataLoading&&e.data.length>0?"none":"block"),d["\u0275\u0275property"]("dataSource",e.data)("rotated",!0)("palette",d["\u0275\u0275pureFunction0"](36,Ba)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("valueField","value"===e.selectedToggle?"totalPreviousOppValue":"totalPreviousOppCount")("name","value"===e.selectedToggle?"Previous "+e.selectedDuration+" Value":"Previous "+e.selectedDuration+" Count")("label",d["\u0275\u0275pureFunction0"](37,$a)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("valueField","value"===e.selectedToggle?"totalCurrentOppValue":"totalCurrentOppCount")("name","value"===e.selectedToggle?"Current "+e.selectedDuration+" Value":"Current "+e.selectedDuration+" Count")("label",d["\u0275\u0275pureFunction0"](38,$a)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("barPadding",0)("barGroupPadding",0),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("visible",!0)("backgroundColor","none")("font",d["\u0275\u0275pureFunction0"](39,Ua))("format",d["\u0275\u0275pureFunction0"](40,Qa)),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("visible",!1),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("visible",!1),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("visible",!1),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("visible",!0),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("visible",!1),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("enabled",!0)("customizeTooltip",e.customizeTooltip),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("enabled",!0)("fileName",null==e.widgetConfig?null:e.widgetConfig.widget_name),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.data.length<=0)}}let Wa=(()=>{class e{constructor(e,t,n,i){this.salesDashBoardService=e,this.utilityService=t,this.dialog=n,this._toaster=i,this.isLoading=!0,this.isDataLoading=!1,this.isError=!1,this.current_filter=[],this.currency_label="USD",this.data=[],this.palette=["#5F6C81","#A0D911"],this.durationData=[],this.toggleOptions=[],this.downloading=!1,this.customizeTooltip=e=>({text:`RATE VALUE (%) - "${"value"===this.selectedToggle?e.point.data.percentageValue:e.point.data.percentageCount}%"`}),this.customizeTooltip=this.customizeTooltip.bind(this)}ngOnInit(){this.widgetConfig=this.salesDashBoardService.getConfigFor(this.widget_id),this.palette=r.pluck(this.widgetConfig,"palette"),this.durationData=this.widgetConfig.durationData,this.selectedDuration=this.widgetConfig.defaultValues.selectedDuration||"year",this.selectedToggle=this.widgetConfig.defaultValues.selectedToggle||"count",this.toggleOptions=this.widgetConfig.toggleOptions}getMaxValue(){let e=0;return this.data.forEach(t=>{const n=Math.max(t.totalPreviousOppValue||0,t.totalCurrentOppValue||0);n>e&&(e=n)}),e}customizeLabel(e){const t=this.data.find(t=>t.ownerName===e.argument);return t?t.percentageValue+"%":""}getReport(e){var t,n;this.isDataLoading=!0,s()().year(),console.log(e,"Filter Config"),this.current_filter=e;let i=s()(null===(t=e.filter(e=>1==e.filter_id)[0])||void 0===t?void 0:t.applied_filter.startDate).format("YYYY-MM-DD"),a=s()(null===(n=e.filter(e=>1==e.filter_id)[0])||void 0===n?void 0:n.applied_filter.endDate).format("YYYY-MM-DD"),o=e.filter(e=>1==e.filter_id);e=e.filter(e=>1!=e.filter_id),o[0].applied_filter.startDate=s()(i).format("YYYY-MM-DD"),o[0].applied_filter.endDate=s()(a).format("YYYY-MM-DD"),delete o[0].applied_filter.startDate,delete o[0].applied_filter.endDate,e.push(...o),this.salesDashBoardService.getPipelineGrowthBySalesOwner(e,this.selectedDuration,5).subscribe(e=>{this.data=e.messData,this.isLoading=!1,this.isDataLoading=!1,this.isError=!1},e=>{this.isLoading=!1,this.isDataLoading=!1,this.isError=!0})}openSummary(e){var t;let n,i,a=e.target.series.getValueFields()[0],o=[];"totalCurrentOppCount"===a||"totalCurrentOppValue"===a?(o=e.target.data.currentOpportunityIds,n=e.target.data.totalCurrentOppCount):"totalPreviousOppCount"!==a&&"totalPreviousOppValue"!==a||(o=e.target.data.previousOpportunityIds,n=e.target.data.totalPreviousOppCount);let r={columnConfig:this.summaryColumnConfig,useDxTable:null===(t=this.widgetConfig.component_config)||void 0===t?void 0:t.useDxTable,dataObservername:"pipeline_owner_observable",title:"Pipeline Growth By Sales Owner",subtitle:"Total opportunities: "+n,subtitle1:"Sales Owner: "+e.target.argument,summaryFor:i};this.salesDashBoardService.getPipelineGrowthSummary(o,i),this.dialog.open(ge,{height:"80%",width:"80%",data:r,animation:{entryAnimation:{keyframes:[{transform:"translateY(-100%)"},{transform:"translateY(0)"}],keyframeAnimationOptions:{duration:250,easing:"ease-in-out"}},exitAnimation:{keyframes:[{transform:"translateY(0)"},{transform:"translateY(-100%)"}],keyframeAnimationOptions:{duration:250,easing:"ease-in-out"}}}})}handleDurationChange(e){this.selectedDuration=e,this.getReport(this.current_filter)}handleToggleChange(e){this.selectedToggle=e,this.getReport(this.current_filter)}get summaryColumnConfig(){return[{column_name:"OPPORTUNITY NAME",column_type:"text",data_field:"opportunity_name",text_overflow:!0},{column_name:"ACCOUNT NAME",column_type:"text",data_field:"customer_name",text_overflow:!0,gutter_space:"5%"},{column_name:"SALES OWNER",column_type:"text",data_field:"sales_owner",text_overflow:!0,gutter_space:"5%"},{column_name:this.widgetConfig.component_config.estOppCloseDate,column_type:"date",data_field:"processing_end_date",gutter_space:"5%"},{column_name:"ACTUAL CLOSURE DATE",column_type:"date",data_field:"actual_closure_date",gutter_space:"5%"},{column_name:"OPPORTUNITY VALUE",column_type:"text",gutter_space:"5%",data_field:"opportunity_val_usd"},{column_name:"STATUS",column_type:"text",data_field:"status_name",gutter_space:"5%"},{column_name:"",column_type:"actions",actions:[{action_name:"Open in new tab",action_icon:"open_in_new",action_click:this.openInNew.bind(this)}]}]}openInNew(e){window.open(`${window.location.origin}/main/opportunities/${e.opportunity_id}/${this.utilityService.encodeURIComponent(e.opportunity_name)}`)}applyStylesToCell(e,t){if(t.border){const t={style:"thin",color:{argb:"BFBFBF"}};e.border={bottom:t,left:t,right:t,top:t}}t.bgColor&&(e.fill={type:"pattern",pattern:"solid",fgColor:{argb:t.bgColor}}),t.font&&(e.font=t.font)}download(){this.downloading=!0,this.getPipelineGrowthBySalesOwnerPromise(this.current_filter,this.selectedDuration,99999).then(e=>{var t;if(!e||0===e.length)return this._toaster.showWarning("No data available for download","",1e3),void(this.downloading=!1);const n=new Pa.Workbook,i=n.addWorksheet(null===(t=this.widgetConfig)||void 0===t?void 0:t.widget_name);i.columns=[{width:30},{width:40},{width:40},{width:40},{width:40}];const a=i.getRow(2);a.height=30,i.mergeCells(2,1,2,5);const o=a.getCell(1);o.value="Pipeline Growth By Sales Owner",o.font={name:"Segoe UI Light",size:19,bold:!0},o.alignment={horizontal:"left",vertical:"middle",wrapText:!0},this.applyStylesToCell(o,{border:!0,bgColor:"C0C0C0"});const r=e=>e.charAt(0).toUpperCase()+e.slice(1),l=["Sales Owner",`Total Current ${r(this.selectedDuration)} Opportunity Value`,`Total Previous ${r(this.selectedDuration)} Opportunity Value`,`Total Current ${r(this.selectedDuration)} Opportunity Count`,`Total Previous ${r(this.selectedDuration)} Opportunity Count`];return i.addRow(l).eachCell({includeEmpty:!0},e=>{this.applyStylesToCell(e,{font:{bold:!0}})}),e.forEach(e=>{i.addRow([e.ownerName||"-",e.totalCurrentOppValue||0,e.totalPreviousOppValue||0,e.totalCurrentOppCount||0,e.totalPreviousOppCount||0])}),n.xlsx.writeBuffer()}).then(e=>{const t=new Blob([e],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"});Ma(t,"sales_owners_report.xlsx"),this.downloading=!1}).catch(e=>{console.error("Error writing Excel buffer:",e),this._toaster.showError("Couldn't generate the excel report!","Dismiss",1e3),this.downloading=!1})}getPipelineGrowthBySalesOwnerPromise(e,t,n){return new Promise((i,a)=>{this.salesDashBoardService.getPipelineGrowthBySalesOwner(e,t,n).subscribe(e=>i(e.messData),e=>a(e))})}formatDateForDownload(e){return e?s.a.utc(e).format("DD MMM YYYY"):""}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](v),d["\u0275\u0275directiveInject"](ue.a),d["\u0275\u0275directiveInject"](fe.a),d["\u0275\u0275directiveInject"](_e.a))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-pipleine-growth-widget"]],viewQuery:function(e,t){if(1&e&&d["\u0275\u0275viewQuery"](Fa,!0),2&e){let e;d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.currency=e.first)}},inputs:{widget_id:"widget_id"},decls:3,vars:2,consts:[[1,"total-activities-card"],["class","loading-wrapper",4,"ngIf"],[4,"ngIf"],[1,"loading-wrapper"],[1,"loading"],[1,"row","p-0","justify-content-between"],[1,"title"],[1,"iconbtn",2,"margin-left","5px"],[2,"font-size","larger",3,"matTooltip"],[1,"right-elements"],[3,"value","change"],[4,"ngFor","ngForOf"],["appearance","outline",1,"drop-down-filter"],["placeholder","Select Duration",2,"padding","0% 0% 5%",3,"value","selectionChange"],[1,"d-flex","align-item-center","icon-wrapper",2,"cursor","pointer"],["matTooltip","Excel Export",3,"disabled","click",4,"ngIf"],["matTooltip","Downloading...",4,"ngIf"],[1,"scrollable-chart-container"],[3,"dataSource","rotated","palette","onPointClick"],[3,"valueField","name","label"],["argumentField","ownerName","type","bar","hoverMode","onlyPoint","selectionMode","none",3,"barPadding","barGroupPadding"],[3,"visible","backgroundColor","font","format"],[3,"visible"],["verticalAlignment","top","horizontalAlignment","left","orientation","horizontal","itemTextPosition","left"],["zIndex","1",3,"enabled","customizeTooltip"],[3,"enabled","fileName"],["class","no-data",4,"ngIf"],["class","toggle-btn",3,"value","ngClass",4,"ngIf"],[1,"toggle-btn",3,"value","ngClass"],[3,"value",4,"ngIf"],[3,"value"],["matTooltip","Excel Export",3,"click"],["matTooltip","Downloading..."],[1,"no-data"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"mat-card",0),d["\u0275\u0275template"](1,Ta,3,0,"div",1),d["\u0275\u0275template"](2,qa,38,41,"div",2),d["\u0275\u0275elementEnd"]()),2&e&&(d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.isLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!t.isLoading))},directives:[ve.a,i.NgIf,C.a,y.a,Ia.b,i.NgForOf,Oe.c,Oe.g,Ee.c,ye.a,U.E,U.Bb,U.Oc,U.eb,U.Ae,U.R,U.uc,U.Qc,U.Ie,U.Sb,Ia.a,i.NgClass,De.p],styles:[".total-activities-card[_ngcontent-%COMP%]{margin:7px;height:100%;min-height:300px;padding:14px!important;color:#45546e;box-shadow:0 6px 18px rgba(0,0,0,.1)}.total-activities-card[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-weight:500;display:flex;font-size:14px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;padding:0!important;color:#45546e}.total-activities-card[_ngcontent-%COMP%]   .right-elements[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;width:28rem;padding:0 1.25rem}.total-activities-card[_ngcontent-%COMP%]   .right-elements[_ngcontent-%COMP%]   .toggle-btn[_ngcontent-%COMP%]{min-width:4rem;font-family:Plus Jakarta Sans!important}.total-activities-card[_ngcontent-%COMP%]   .right-elements[_ngcontent-%COMP%]   .btn-toggle-selected[_ngcontent-%COMP%]{font-size:12px!important;background-color:var(--primaryColor)!important;color:#fff}.total-activities-card[_ngcontent-%COMP%]   .right-elements[_ngcontent-%COMP%]   .icon-wrapper[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{cursor:pointer}.total-activities-card[_ngcontent-%COMP%]   .right-elements[_ngcontent-%COMP%]   .drop-down-filter[_ngcontent-%COMP%]{width:9rem;padding:0 1%;height:2rem;color:var(--Blue-Grey-100,#45546e);font-family:Roboto;font-size:13px;font-style:normal;font-weight:500;border-radius:4px;font-family:Plus Jakarta Sans!important}.total-activities-card[_ngcontent-%COMP%]   .right-elements[_ngcontent-%COMP%]   .drop-down-filter[_ngcontent-%COMP%]     .mat-form-field-flex{height:2.5rem;align-items:center}.total-activities-card[_ngcontent-%COMP%]   .right-elements[_ngcontent-%COMP%]   .drop-down-filter[_ngcontent-%COMP%]     .mat-form-field-infix{padding:12px 0!important}.total-activities-card[_ngcontent-%COMP%]   .right-elements[_ngcontent-%COMP%]   .drop-down-filter[_ngcontent-%COMP%]     .mat-form-field-outline{height:2rem!important;top:0!important}#overlay[_ngcontent-%COMP%]{position:absolute;display:block;width:100%;height:100%;top:0;left:0;right:0;bottom:0;background-color:rgba(0,0,0,.1);z-index:3;cursor:pointer}.no-data[_ngcontent-%COMP%]{display:flex;height:80%;font-size:small;vertical-align:center;justify-content:center;align-items:center;width:100%}.iconbtn[_ngcontent-%COMP%]{color:#7b7b7b!important;padding:0;height:-moz-fit-content;height:fit-content}.btn-color[_ngcontent-%COMP%]{background-color:var(--primaryColor)}.filter-wrapper[_ngcontent-%COMP%]{height:25rem;overflow-y:auto;margin:0 auto .5rem;width:30rem}.filter-wrapper[_ngcontent-%COMP%]   .label-filter[_ngcontent-%COMP%]{margin-left:3%;font-size:14px;color:#45546e;font-weight:500;font-family:Roboto}.filter-wrapper[_ngcontent-%COMP%]   .static-wrapper[_ngcontent-%COMP%]{border:1px solid #e0e0e0;border-radius:5px;padding:.35rem;background-color:#eceff3}.filter-wrapper[_ngcontent-%COMP%]   .static-wrapper[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{display:flex;margin-left:auto!important;font-size:19px;color:#45546e;cursor:pointer;margin-bottom:-5%}.scrollable-chart-container[_ngcontent-%COMP%]{width:100%;min-height:250px;overflow-x:auto;overflow-y:hidden}"]}),e})();var Ga=n("+yIk"),Ha=n("GnQ3"),Ja=n("xgIS"),Ka=n("7EHt"),Xa=n("iadO");function Za(e,t){1&e&&d["\u0275\u0275elementContainer"](0)}function eo(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"span",19),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",e.selectedYear," ")}}function to(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"span",20),d["\u0275\u0275elementStart"](1,"input",21),d["\u0275\u0275listener"]("onYearSubmit",(function(){d["\u0275\u0275restoreView"](e);const t=d["\u0275\u0275nextContext"]();return t.selectedYear=t.yearModel}))("ngModelChange",(function(t){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().yearModel=t})),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngModel",e.yearModel)}}const no=function(e){return{"active-option":e}};function io(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"span",22),d["\u0275\u0275listener"]("click",(function(){d["\u0275\u0275restoreView"](e);const n=t.$implicit;return d["\u0275\u0275nextContext"]().quarterClicked(n)})),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=d["\u0275\u0275nextContext"]();d["\u0275\u0275property"]("ngClass",d["\u0275\u0275pureFunction1"](2,no,e==n.getSelectedQuarter())),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"]("Q",e,"")}}const ao=function(e,t,n){return{"month-start":e,"month-range":t,"month-end":n}};function oo(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",23),d["\u0275\u0275listener"]("click",(function(){d["\u0275\u0275restoreView"](e);const n=t.$implicit;return d["\u0275\u0275nextContext"]().onMonthClick(n)})),d["\u0275\u0275text"](1),d["\u0275\u0275pipe"](2,"month"),d["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=d["\u0275\u0275nextContext"]();d["\u0275\u0275property"]("ngClass",d["\u0275\u0275pureFunction3"](4,ao,n.isMonthStart(e),n.isMonthInRange(e),n.isMonthEnd(e))),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",d["\u0275\u0275pipeBind1"](2,2,e)," ")}}const ro=function(){return[1,2,3,4]};let lo=(()=>{class e{constructor(e){this.host=e}ngAfterViewInit(){this.host.nativeElement.focus()}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](d.ElementRef))},e.\u0275dir=d["\u0275\u0275defineDirective"]({type:e,selectors:[["","autoFocus",""]]}),e})(),so=(()=>{class e{constructor(e){this.ngControl=e,this.onYearSubmit=new d.EventEmitter,this.el=e}onInput(e){var t;null===(t=this.el.control)||void 0===t||t.patchValue(e.replace(/[^0-9]/g,""))}onEnter(e){var t,n,i,a;let o=parseInt(s()().format("YYYY")),r=null===(t=this.el.control)||void 0===t?void 0:t.value,l="number"==typeof r?r:parseInt(r);l>o+5&&(null===(n=this.el.control)||void 0===n||n.patchValue(o+5)),l<o-10&&(null===(i=this.el.control)||void 0===i||i.patchValue(o-10)),r||null===(a=this.el.control)||void 0===a||a.patchValue(o),this.onYearSubmit.next()}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](c.u))},e.\u0275dir=d["\u0275\u0275defineDirective"]({type:e,selectors:[["","validYear",""]],hostBindings:function(e,t){1&e&&d["\u0275\u0275listener"]("input",(function(e){return t.onInput(e.target.value)}))("keyup.enter",(function(e){return t.onEnter(e.target.value)}))},outputs:{onYearSubmit:"onYearSubmit"}}),e})(),co=(()=>{class e{constructor(e){this.tpl=e}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](d.TemplateRef))},e.\u0275dir=d["\u0275\u0275defineDirective"]({type:e,selectors:[["","view",""]]}),e})(),po=(()=>{class e{constructor(e){this.tpl=e}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](d.TemplateRef))},e.\u0275dir=d["\u0275\u0275defineDirective"]({type:e,selectors:[["","edit",""]]}),e})(),mo=(()=>{class e{transform(e){return e?s()(e,"M").format("MMM"):" "}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=d["\u0275\u0275definePipe"]({name:"month",type:e,pure:!0}),e})(),go=(()=>{class e{transform(e){return e?new Date(e):" "}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=d["\u0275\u0275definePipe"]({name:"jsdate",type:e,pure:!0}),e})(),uo=(()=>{class e{constructor(e){this.host=e,this.update=new d.EventEmitter,this.mode="view",this.editMode=new u.b,this.editMode$=this.editMode.asObservable()}get currentView(){return"view"===this.mode?this.viewModeTpl.tpl:this.editModeTpl.tpl}ngOnInit(){this.viewModeHandler()}onEnter(){this.update.next(),this.mode="view"}viewModeHandler(){Object(Ja.a)(this.element,"click").subscribe(()=>{this.mode="edit",this.editMode.next(!0)})}get element(){return this.host.nativeElement}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](d.ElementRef))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["edit-in-place"]],contentQueries:function(e,t,n){if(1&e&&(d["\u0275\u0275contentQuery"](n,co,!0),d["\u0275\u0275contentQuery"](n,po,!0)),2&e){let e;d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.viewModeTpl=e.first),d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.editModeTpl=e.first)}},hostBindings:function(e,t){1&e&&d["\u0275\u0275listener"]("keyup.enter",(function(){return t.onEnter()}))},outputs:{update:"update"},decls:1,vars:1,consts:[[4,"ngTemplateOutlet"]],template:function(e,t){1&e&&d["\u0275\u0275template"](0,Za,1,0,"ng-container",0),2&e&&d["\u0275\u0275property"]("ngTemplateOutlet",t.currentView)},directives:[i.NgTemplateOutlet],encapsulation:2}),e})(),fo=(()=>{class e{constructor(){this.config={format:"MM-DD-YYYY"},this.dateRangeChange=new d.EventEmitter,this.currentYear=parseInt(s()().format("YYYY")),this.minDate=new Date("01-01-"+(this.currentYear-10)),this.maxDate=new Date("12-31-"+(this.currentYear+5)),this.months=Array.from({length:12},(e,t)=>t+1)}get isAllNotNull(){return!!this.selectedEndMonth&&!!this.selectedStartMonth&&!!this.selectedStartMonth}get startDate(){return this._startDate}set startDate(e){this._startDate=s()(e).format(this.config.format),this.resolveDate("update")}get endDate(){return this._endDate}set endDate(e){this._endDate=s()(e).format(this.config.format),this.resolveDate("update")}resolveDate(e="set"){if("set"===e){if(!this.isAllNotNull)return;this._startDate=s()(`${this._selectedStartMonth}-01-${this._selectedYear}`).format(this.config.format),this.endDate=s()(`${this._selectedEndMonth}-${s()(this._selectedEndMonth,"M").endOf("month").format("D")}-${this._selectedYear}`).format(this.config.format)}else this._selectedYear=s()(this._startDate).format("YYYY"),this._selectedStartMonth=parseInt(s()(this._startDate).format("M")),this._selectedEndMonth=parseInt(s()(this._endDate).format("M"));this.dateRangeChange.emit([{startDate:this.startDate,endDate:this.endDate}])}get selectedYear(){return this._selectedYear}set selectedYear(e){console.trace(),this._selectedYear=e,this.yearModel=this._selectedYear,console.log(typeof this._selectedYear,this._selectedYear),this.resolveDate("set")}get minEndDate(){return this.startDate?new Date(this.startDate):new Date(this.minDate)}increaseYear(){let e=this.selectedYear;console.log(this.selectedYear),"string"==typeof this.selectedYear&&(e=parseInt(this.selectedYear)),this.selectedYear=Math.min(e+1,this.currentYear+5)}decreaseYear(){this.selectedYear=Math.max(this.selectedYear-1,this.currentYear-10)}getSelectedQuarter(){return this.startDate==s()(1,"M").startOf("M").format(this.config.format)&&this.endDate==s()(3,"M").endOf("M").format(this.config.format)?1:this.startDate==s()(4,"M").startOf("M").format(this.config.format)&&this.endDate==s()(6,"M").endOf("M").format(this.config.format)?2:this.startDate==s()(7,"M").startOf("M").format(this.config.format)&&this.endDate==s()(9,"M").endOf("M").format(this.config.format)?3:this.startDate==s()(10,"M").startOf("M").format(this.config.format)&&this.endDate==s()(12,"M").endOf("M").format(this.config.format)?4:void 0}quarterClicked(e){if(e==this.selectedQuarter)return this.selectedQuarter=0,this.selectedStartMonth=1,void(this.selectedEndMonth=12);switch(this.selectedQuarter=e,e){case 1:this.selectedStartMonth=1,this.selectedEndMonth=3;break;case 2:this.selectedStartMonth=4,this.selectedEndMonth=6;break;case 3:this.selectedStartMonth=7,this.selectedEndMonth=9;break;case 4:this.selectedStartMonth=10,this.selectedEndMonth=12}}get selectedStartMonth(){return this._selectedStartMonth}set selectedStartMonth(e){this._selectedStartMonth=e,this.resolveDate("set")}get selectedEndMonth(){return this._selectedEndMonth}set selectedEndMonth(e){this._selectedEndMonth=e,this.resolveDate("set")}isMonthStart(e){return this.selectedStartMonth===e}isMonthEnd(e){return this.selectedEndMonth===e}isMonthInRange(e){return this.selectedStartMonth<=e&&e<=this.selectedEndMonth}onMonthClick(e){e==this.selectedStartMonth?(this.selectedStartMonth=void 0,this.selectedEndMonth=void 0):this.selectedStartMonth?this.selectedEndMonth?e<=(this.selectedEndMonth+this.selectedStartMonth)/2>>0?this.selectedStartMonth=e:this.selectedEndMonth=e:(this.selectedEndMonth=Math.max(this.selectedStartMonth,e),this.selectedStartMonth=Math.min(this.selectedStartMonth,e)):this.selectedStartMonth=e}ngOnInit(){var e,t;this._startDate=(null===(e=this.dateRange[0])||void 0===e?void 0:e.startDate)||s()().startOf("year").format(this.config.format),this._endDate=(null===(t=this.dateRange[0])||void 0===t?void 0:t.endDate)||s()().endOf("year").format(this.config.format),this.resolveDate("update"),this.yearModel=this.selectedYear}getSelection(){return{startDate:this.startDate,endDate:this.endDate}}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["fm-date-range"]],inputs:{config:"config",dateRange:"dateRange"},outputs:{dateRangeChange:"dateRangeChange"},features:[d["\u0275\u0275ProvidersFeature"]([{provide:c.t,useExisting:Object(d.forwardRef)(()=>e),multi:!0}])],decls:50,vars:23,consts:[[1,"container-fluid","p-1",3,"click"],[1,"date-option"],[1,"option-name"],[1,"option-value-wrapper"],["mat-icon-button","",3,"click"],["view",""],["edit",""],["class","option-value pointer",3,"ngClass","click",4,"ngFor","ngForOf"],["displayMode","flat"],["hideToggle",""],[1,"container-fluid","p-0","month-range-row"],["class","month-range-col",3,"ngClass","click",4,"ngFor","ngForOf"],[1,"date-preview"],[1,"hidden-fields"],["matInput","",3,"ngModel","min","max","matDatepicker","ngModelChange"],["startDatePicker",""],["mat-button","",1,"date",3,"click"],[1,"inline-block"],["endDatePicker",""],[1,"option-value","active-option"],[1,"option-value"],["matInput","","validYear","","autoFocus","",2,"width","34px",3,"ngModel","onYearSubmit","ngModelChange"],[1,"option-value","pointer",3,"ngClass","click"],[1,"month-range-col",3,"ngClass","click"]],template:function(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",0),d["\u0275\u0275listener"]("click",(function(e){return e.stopPropagation()})),d["\u0275\u0275elementStart"](1,"div",1),d["\u0275\u0275elementStart"](2,"span",2),d["\u0275\u0275text"](3," Year\xa0: "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](4,"div",3),d["\u0275\u0275elementStart"](5,"button",4),d["\u0275\u0275listener"]("click",(function(){return t.decreaseYear()})),d["\u0275\u0275elementStart"](6,"mat-icon"),d["\u0275\u0275text"](7,"keyboard_arrow_left"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](8,"edit-in-place"),d["\u0275\u0275template"](9,eo,2,1,"ng-template",5),d["\u0275\u0275template"](10,to,2,1,"ng-template",6),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](11,"button",4),d["\u0275\u0275listener"]("click",(function(){return t.increaseYear()})),d["\u0275\u0275elementStart"](12,"mat-icon"),d["\u0275\u0275text"](13,"keyboard_arrow_right"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](14,"div",1),d["\u0275\u0275elementStart"](15,"span",2),d["\u0275\u0275text"](16," Quarter\xa0: "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](17,"div",3),d["\u0275\u0275template"](18,io,2,4,"span",7),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](19,"div",1),d["\u0275\u0275elementStart"](20,"mat-accordion",8),d["\u0275\u0275elementStart"](21,"mat-expansion-panel",9),d["\u0275\u0275elementStart"](22,"mat-expansion-panel-header"),d["\u0275\u0275elementStart"](23,"mat-panel-title"),d["\u0275\u0275text"](24," Month\xa0: "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](25,"mat-panel-description"),d["\u0275\u0275text"](26),d["\u0275\u0275pipe"](27,"month"),d["\u0275\u0275pipe"](28,"month"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](29,"div",10),d["\u0275\u0275template"](30,oo,3,8,"div",11),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](31,"div",12),d["\u0275\u0275elementStart"](32,"div",13),d["\u0275\u0275elementStart"](33,"mat-form-field"),d["\u0275\u0275elementStart"](34,"input",14),d["\u0275\u0275listener"]("ngModelChange",(function(e){return t.startDate=e})),d["\u0275\u0275pipe"](35,"jsdate"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](36,"mat-datepicker",null,15),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](38,"button",16),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275reference"](37).open()})),d["\u0275\u0275text"](39),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](40,"p",17),d["\u0275\u0275text"](41,"-"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](42,"div",13),d["\u0275\u0275elementStart"](43,"mat-form-field"),d["\u0275\u0275elementStart"](44,"input",14),d["\u0275\u0275listener"]("ngModelChange",(function(e){return t.endDate=e})),d["\u0275\u0275pipe"](45,"jsdate"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](46,"mat-datepicker",null,18),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](48,"button",16),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275reference"](47).open()})),d["\u0275\u0275text"](49),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275reference"](37),n=d["\u0275\u0275reference"](47);d["\u0275\u0275advance"](18),d["\u0275\u0275property"]("ngForOf",d["\u0275\u0275pureFunction0"](22,ro)),d["\u0275\u0275advance"](8),d["\u0275\u0275textInterpolate2"](" ",d["\u0275\u0275pipeBind1"](27,14,t.selectedStartMonth)," - ",d["\u0275\u0275pipeBind1"](28,16,t.selectedEndMonth)," "),d["\u0275\u0275advance"](4),d["\u0275\u0275property"]("ngForOf",t.months),d["\u0275\u0275advance"](4),d["\u0275\u0275property"]("ngModel",d["\u0275\u0275pipeBind1"](35,18,t.startDate))("min",t.minDate)("max",t.maxDate)("matDatepicker",e),d["\u0275\u0275advance"](5),d["\u0275\u0275textInterpolate1"](" ",t.startDate," "),d["\u0275\u0275advance"](5),d["\u0275\u0275property"]("ngModel",d["\u0275\u0275pipeBind1"](45,20,t.endDate))("min",t.minEndDate)("max",t.maxDate)("matDatepicker",n),d["\u0275\u0275advance"](5),d["\u0275\u0275textInterpolate1"](" ",t.endDate," ")}},directives:[xe.a,C.a,uo,co,po,i.NgForOf,Ka.a,Ka.c,Ka.g,Ka.h,Ka.f,Oe.c,Ne.b,c.e,Xa.g,c.v,c.y,Xa.f,so,lo,i.NgClass],pipes:[mo,go],styles:['.date-option[_ngcontent-%COMP%], .date-preview[_ngcontent-%COMP%]{display:flex;flex-direction:row;flex-wrap:nowrap;align-items:center;position:relative;margin-block:5px}.date-preview[_ngcontent-%COMP%]{justify-content:space-around}.date-preview[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%]{display:inline-block}.hidden-fields[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{visibility:hidden;width:0!important;height:0!important}.small-icon[_ngcontent-%COMP%]{font-size:small;line-height:26px;margin-left:2px}.on-hover-trigger[_ngcontent-%COMP%]{cursor:pointer;display:flex!important;flex-wrap:nowrap;justify-content:center;align-items:center}.on-hover[_ngcontent-%COMP%]{display:none}.on-hover-trigger[_ngcontent-%COMP%]:hover   .on-hover[_ngcontent-%COMP%]{display:inline-block}.disable-date-option[_ngcontent-%COMP%]   [_ngcontent-%COMP%]:after{content:"";position:absolute;cursor:auto;top:0;right:0;bottom:0;left:0;z-index:9999;border-radius:4px;background-color:rgba(83,83,83,.05)}.option-value-wrapper[_ngcontent-%COMP%]{display:flex;justify-content:center;width:100%;align-items:center}.option-name[_ngcontent-%COMP%]{display:inline-block;color:#45546e}.option-value[_ngcontent-%COMP%]{display:inline-block;padding:5px 12px;border-radius:10px}.inline-block[_ngcontent-%COMP%], .pointer[_ngcontent-%COMP%]{cursor:pointer}.inline-block[_ngcontent-%COMP%]{display:inline-block}.active-option[_ngcontent-%COMP%]{background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%);color:#fff}  .mat-expansion-panel{box-shadow:none!important}  .mat-expansion-panel-header{height:30px;padding:0!important}  .mat-expansion-panel-header-title{color:#45546e}  .mat-expansion-panel-header.mat-expanded{height:30px}  .mat-expansion-panel-body{padding:0!important}.month-range-row[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap}.month-range-col[_ngcontent-%COMP%]{cursor:pointer;flex:0 0 20%;padding:5px}.month-selected[_ngcontent-%COMP%]{border-radius:10px;background-color:#ef4a61}.month-start[_ngcontent-%COMP%]{border-bottom-left-radius:10px;border-top-left-radius:10px}.month-end[_ngcontent-%COMP%]{border-bottom-right-radius:10px;border-top-right-radius:10px}.month-range[_ngcontent-%COMP%]{background:#ef4a61;color:#fff}']}),e})();function ho(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"mat-tree-node",10),d["\u0275\u0275element"](1,"button",11),d["\u0275\u0275elementStart"](2,"mat-checkbox",12),d["\u0275\u0275listener"]("change",(function(){d["\u0275\u0275restoreView"](e);const n=t.$implicit;return d["\u0275\u0275nextContext"]().todoLeafItemSelectionToggle(n)})),d["\u0275\u0275text"](3),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=d["\u0275\u0275nextContext"]();d["\u0275\u0275styleProp"]("display",n.showSelection&&!n.checklistSelection.isSelected(e)||n.filterLeafNode(e)?"none":"flex"),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("checked",n.checklistSelection.isSelected(e)),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate"](e.item[n.config.name])}}function _o(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"mat-tree-node",13),d["\u0275\u0275elementStart"](1,"button",14),d["\u0275\u0275elementStart"](2,"mat-icon",15),d["\u0275\u0275text"](3),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](4,"mat-checkbox",16),d["\u0275\u0275listener"]("change",(function(){d["\u0275\u0275restoreView"](e);const n=t.$implicit;return d["\u0275\u0275nextContext"]().todoItemSelectionToggle(n)})),d["\u0275\u0275text"](5),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=d["\u0275\u0275nextContext"]();d["\u0275\u0275styleProp"]("display",n.filterParentNode(e)||n.showSelection&&!n.descendantsAllSelected(e)&&!n.descendantsPartiallySelected(e)?"none":"flex"),d["\u0275\u0275advance"](1),d["\u0275\u0275attribute"]("aria-label","Toggle "+e.item[n.config.name]),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"](" ",n.treeControl.isExpanded(e)?"expand_more":"chevron_right"," "),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("checked",n.descendantsAllSelected(e))("indeterminate",n.descendantsPartiallySelected(e)),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate"](e.item[n.config.name])}}const vo="linear-gradient(270deg, #ef4a61 0%, #f27a6c 105.29%)",yo=function(){return{background:vo,color:"white"}},Co=function(){return{}},xo=function(){return{background:vo,"-webkit-background-clip":"text","-webkit-text-fill-color":"transparent"}};let bo=(()=>{class e{constructor(){this.config={id:"id",children:"children",name:"name"},this.checklistData=[],this.appliedSelection=[],this.checklistSelectionChange=new d.EventEmitter,this.showSelection=!1,this.flatNodeMap=new Map,this.searchString="",this.nestedNodeMap=new Map,this.selectedParent=null,this.newItemName="",this.checklistSelection=new Le.c(!0),this.getLevel=e=>e.level,this.isExpandable=e=>e.expandable,this.getChildren=e=>e[this.config.children],this.hasChild=(e,t)=>t.expandable,this.hasNoContent=(e,t)=>""===t.item,this.transformer=(e,t)=>{var n;const i=this.nestedNodeMap.get(e),a=i&&i.item===e.item?i:{};return a.item=e,a.level=t,a.expandable=!!(null===(n=e[this.config.children])||void 0===n?void 0:n.length),this.flatNodeMap.set(a,e),this.nestedNodeMap.set(e,a),a},this.treeFlattener=new Ae.d(this.transformer,this.getLevel,this.isExpandable,this.getChildren),this.treeControl=new Ye.j(this.getLevel,this.isExpandable),this.dataSource=new Ae.c(this.treeControl,this.treeFlattener)}get isAllSelected(){return this.treeControl.dataNodes.every(e=>this.checklistSelection.isSelected(e))}selectAllNodes(){this.checklistSelection.select(...this.treeControl.dataNodes)}deselectAllNodes(){this.checklistSelection.deselect(...this.treeControl.dataNodes)}ngOnInit(){this.dataSource.data=this.checklistData?[...this.checklistData]:[];let e=[];for(let t of this.appliedSelection)e.push(...this.treeControl.dataNodes.filter(e=>e.item[this.config.id]===t&&!!e.item[this.config.id]));this.checklistSelection.select(...e),this.checklistSelection.changed.subscribe(e=>{this.checklistSelectionChange.emit(this.checklistSelection.selected.map(e=>e.item[this.config.id]).filter(e=>!!e))})}filterLeafNode(e){var t,n;return!!this.searchString&&-1===(null===(t=e.item[this.config.name])||void 0===t?void 0:t.toLowerCase().indexOf(null===(n=this.searchString)||void 0===n?void 0:n.toLowerCase()))}filterParentNode(e){var t,n;return!(!this.searchString||-1!==(null===(t=e.item[this.config.name])||void 0===t?void 0:t.toLowerCase().indexOf(null===(n=this.searchString)||void 0===n?void 0:n.toLowerCase()))||this.treeControl.getDescendants(e).some(e=>{var t,n;return-1!==(null===(t=e.item[this.config.name])||void 0===t?void 0:t.toLowerCase().indexOf(null===(n=this.searchString)||void 0===n?void 0:n.toLowerCase()))}))}descendantsAllSelected(e){const t=this.treeControl.getDescendants(e);return t.length>0&&this.checklistSelection.isSelected(e)?(this.checklistSelection.select(...t),!0):t.length>0&&t.every(e=>this.checklistSelection.isSelected(e))}descendantsPartiallySelected(e){return this.treeControl.getDescendants(e).some(e=>this.checklistSelection.isSelected(e))&&!this.descendantsAllSelected(e)}todoItemSelectionToggle(e){this.checklistSelection.toggle(e);const t=this.treeControl.getDescendants(e);this.checklistSelection.isSelected(e)?this.checklistSelection.select(...t):this.checklistSelection.deselect(...t),t.forEach(e=>this.checklistSelection.isSelected(e)),this.checkAllParentsSelection(e)}todoLeafItemSelectionToggle(e){this.checklistSelection.toggle(e),this.checkAllParentsSelection(e)}checkAllParentsSelection(e){let t=this.getParentNode(e);for(;t;)this.checkRootNodeSelection(t),t=this.getParentNode(t)}checkRootNodeSelection(e){const t=this.checklistSelection.isSelected(e),n=this.treeControl.getDescendants(e),i=n.length>0&&n.every(e=>this.checklistSelection.isSelected(e));t&&!i?this.checklistSelection.deselect(e):!t&&i&&this.checklistSelection.select(e)}getParentNode(e){const t=this.getLevel(e);if(t<1)return null;for(let n=this.treeControl.dataNodes.indexOf(e)-1;n>=0;n--){const e=this.treeControl.dataNodes[n];if(this.getLevel(e)<t)return e}return null}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["fm-check-box-tree"]],inputs:{config:"config",checklistData:"checklistData",appliedSelection:"appliedSelection"},outputs:{checklistSelectionChange:"checklistSelectionChange"},decls:13,vars:13,consts:[[2,"display","flex","justify-content","center","align-items","center","padding-top","5px","position","sticky","top","0"],[2,"display","inline-flex","padding","5px"],[2,"font-size","medium","padding-top","3px","padding-right","16px"],["placeholder","search","matInput","",2,"border","solid 1px #434659","border-radius","3px","padding","3px 10px",3,"ngModel","input","ngModelChange"],["matTooltip","Show Selection","matBadgeSize","small",2,"border-radius","50%","padding","4px","font-size","medium","cursor","pointer","margin-right","10px",3,"matBadge","ngStyle","click"],[2,"border-radius","50%","cursor","pointer",3,"matTooltip","ngStyle","click"],[2,"height","150px","overflow-y","auto"],[3,"dataSource","treeControl"],["matTreeNodeToggle","","matTreeNodePadding","","matTreeNodePaddingIndent","20",3,"display",4,"matTreeNodeDef"],["matTreeNodePadding","","matTreeNodePaddingIndent","20",3,"display",4,"matTreeNodeDef","matTreeNodeDefWhen"],["matTreeNodeToggle","","matTreeNodePadding","","matTreeNodePaddingIndent","20"],["mat-icon-button","","disabled","",1,"button__arrow"],[1,"checklist-leaf-node",3,"checked","change"],["matTreeNodePadding","","matTreeNodePaddingIndent","20"],["mat-icon-button","","matTreeNodeToggle","",1,"button__arrow"],[1,"mat-icon-rtl-mirror"],[3,"checked","indeterminate","change"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"span",0),d["\u0275\u0275elementStart"](1,"span",1),d["\u0275\u0275elementStart"](2,"mat-icon",2),d["\u0275\u0275text"](3,"search"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](4,"input",3),d["\u0275\u0275listener"]("input",(function(){return t.treeControl.expandAll()}))("ngModelChange",(function(e){return t.searchString=e})),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](5,"mat-icon",4),d["\u0275\u0275listener"]("click",(function(){return t.showSelection=!t.showSelection,t.showSelection?t.treeControl.expandAll():""})),d["\u0275\u0275text"](6,"visibility"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](7,"mat-icon",5),d["\u0275\u0275listener"]("click",(function(){return t.isAllSelected?t.deselectAllNodes():t.selectAllNodes()})),d["\u0275\u0275text"](8),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](9,"div",6),d["\u0275\u0275elementStart"](10,"mat-tree",7),d["\u0275\u0275template"](11,ho,4,4,"mat-tree-node",8),d["\u0275\u0275template"](12,_o,6,7,"mat-tree-node",9),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e&&(d["\u0275\u0275advance"](4),d["\u0275\u0275property"]("ngModel",t.searchString),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("matBadge",t.checklistSelection.selected.length)("ngStyle",t.showSelection?d["\u0275\u0275pureFunction0"](9,yo):d["\u0275\u0275pureFunction0"](10,Co)),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("matTooltip",t.isAllSelected?"Deselect all":"Select all")("ngStyle",t.isAllSelected?d["\u0275\u0275pureFunction0"](11,xo):d["\u0275\u0275pureFunction0"](12,Co)),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate"](t.isAllSelected?"check_box":"select_all"),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("dataSource",t.dataSource)("treeControl",t.treeControl),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("matTreeNodeDefWhen",t.hasChild))},directives:[C.a,Ne.b,c.e,c.v,c.y,y.a,Ve.a,i.NgStyle,Ae.b,Ae.h,Ae.g,Ae.k,Ae.j,xe.a,ze.a],styles:[".mat-badge-content[_ngcontent-%COMP%]{margin-right:0!important;font-size:8px!important}mat-tree-node[_ngcontent-%COMP%]{min-height:unset!important}  .mat-checkbox-checked.mat-accent .mat-checkbox-background,   .mat-checkbox-indeterminate.mat-accent .mat-checkbox-background{background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%)!important}  .mat-ripple-element{background:#ff4081!important}"]}),e})();const wo=["fmDateType"],So=["fmChecklistType"],Oo=["activeItemContainer"];function Eo(e,t){1&e&&d["\u0275\u0275elementContainer"](0)}const Do=function(e){return{$implicit:e}};function Mo(e,t){if(1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275template"](1,Eo,1,0,"ng-container",18),d["\u0275\u0275elementContainerEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]().$implicit;d["\u0275\u0275nextContext"](2);const t=d["\u0275\u0275reference"](7);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngTemplateOutlet",t)("ngTemplateOutletContext",d["\u0275\u0275pureFunction1"](2,Do,e))}}function Po(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275template"](1,Mo,2,4,"ng-container",17),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",(null==e.master_data?null:e.master_data.length)>0||"date"==e.filter_type)}}function Io(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",8),d["\u0275\u0275listener"]("click",(function(e){return e.stopPropagation()})),d["\u0275\u0275elementStart"](1,"div",9),d["\u0275\u0275template"](2,Po,2,1,"div",10),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](3,"div",11),d["\u0275\u0275elementStart"](4,"div",12),d["\u0275\u0275elementContainer"](5,null,13),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](7,"div",14),d["\u0275\u0275elementStart"](8,"button",15),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().getResult()})),d["\u0275\u0275text"](9," Apply"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](10,"button",16),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().trigger.closeMenu()})),d["\u0275\u0275text"](11," Cancel "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngForOf",e.current_filter)}}const Fo=function(e,t){return{active:e,"has-active-filter":t}};function To(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"span",19),d["\u0275\u0275listener"]("click",(function(n){d["\u0275\u0275restoreView"](e);const i=t.$implicit,a=d["\u0275\u0275nextContext"]();return n.stopPropagation(),a.switchActiveItem(i)})),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=d["\u0275\u0275nextContext"]();d["\u0275\u0275property"]("ngClass",d["\u0275\u0275pureFunction2"](2,Fo,n.isItemActive(e.filter_id),(null==e.applied_filter?null:e.applied_filter.length)>0)),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",e.filter_name," ")}}function ko(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"fm-date-range",20),d["\u0275\u0275listener"]("dateRangeChange",(function(n){d["\u0275\u0275restoreView"](e);const i=t.$implicit;return d["\u0275\u0275nextContext"]().updateFilter({applied_filter:n,filter_id:i.filter_id})})),d["\u0275\u0275elementEnd"]()}2&e&&d["\u0275\u0275property"]("dateRange",t.$implicit.applied_filter)}const Lo=function(e,t,n){return{id:e,children:t,name:n}};function Yo(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"fm-check-box-tree",21),d["\u0275\u0275listener"]("checklistSelectionChange",(function(n){d["\u0275\u0275restoreView"](e);const i=t.$implicit;return d["\u0275\u0275nextContext"]().updateFilter({applied_filter:n,filter_id:i.filter_id})})),d["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;d["\u0275\u0275property"]("checklistData",e.master_data)("appliedSelection",e.applied_filter)("config",d["\u0275\u0275pureFunction3"](3,Lo,e.master_data_key,e.master_data_child_key,e.master_data_name_key))}}let Ao=(()=>{class e{constructor(){this.filterApplied=new d.EventEmitter,this.isLoading=!1,this.current_filter=[]}updateFilter(e){let t=this.current_filter.findIndex(t=>t.filter_id===e.filter_id);this.current_filter[t].applied_filter=e.applied_filter,this.activeDrawerItem=this.current_filter[t]}ngOnInit(){this.drawerItems=this.filterConfig,this.current_filter=this.drawerItems.map(e=>Object.assign({},e))}ngAfterViewInit(){let e=this.drawerItems.findIndex(e=>{var t;return(null===(t=e.master_data)||void 0===t?void 0:t.length)>0||"date"==e.filter_type});this.switchActiveItem(this.drawerItems[e])}isItemActive(e){return e===this.activeDrawerItem.filter_id}switchActiveItem(e){this.activeDrawerItem=Object.assign({},e),console.log(this.activeDrawerItem),this.activeItemContainer?this.viewActiveItem():this.activeItemContainers.changes.subscribe(e=>{this.activeItemContainer=this.activeItemContainers.first,this.viewActiveItem()})}viewActiveItem(){var e,t;null===(e=this.activeItemContainer)||void 0===e||e.clear(),null===(t=this.activeItemContainer)||void 0===t||t.createEmbeddedView("date"===this.activeDrawerItem.filter_type?this.fmDate:this.fmChecklist,{$implicit:this.activeDrawerItem})}getResult(){this.drawerItems=this.current_filter.map(e=>Object.assign({},e)),this.trigger.closeMenu(),this.filterApplied.emit(this.current_filter)}menuClosed(){this.current_filter=this.drawerItems.map(e=>Object.assign({},e));let e=this.current_filter.findIndex(e=>e.filter_id===this.activeDrawerItem.filter_id);console.log(this.current_filter),this.activeDrawerItem=this.current_filter[e]}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["dashboard-filter-menu"]],viewQuery:function(e,t){if(1&e&&(d["\u0275\u0275viewQuery"](Re.f,!0),d["\u0275\u0275viewQuery"](wo,!0,d.TemplateRef),d["\u0275\u0275viewQuery"](So,!0,d.TemplateRef),d["\u0275\u0275viewQuery"](Oo,!0,d.ViewContainerRef)),2&e){let e;d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.trigger=e.first),d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.fmDate=e.first),d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.fmChecklist=e.first),d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.activeItemContainers=e)}},inputs:{filterConfig:"filterConfig"},outputs:{filterApplied:"filterApplied"},decls:12,vars:2,consts:[["mat-button","",1,"iconbtn",3,"disabled","matMenuTriggerFor","click"],[1,"icon-style"],["yPosition","below",3,"closed"],["filterMenu","matMenu"],[3,"matMenuContent"],["drawerItem",""],["fmDateType",""],["fmChecklistType",""],[1,"filter-menu","container-fluid","row","p-0",3,"click"],[1,"p-0","col-4","drawer"],[4,"ngFor","ngForOf"],[1,"p-0","pb-2","col-8","d-flex","flex-column","justify-content-between"],[2,"height","200px","overflow-y","auto"],["activeItemContainer",""],[1,"d-flex","justify-content-end"],["mat-button","",1,"option-value","active-option",3,"click"],["mat-button","",1,"option-value",3,"click"],[4,"ngIf"],[4,"ngTemplateOutlet","ngTemplateOutletContext"],[1,"drawer-item",3,"ngClass","click"],[3,"dateRange","dateRangeChange"],[3,"checklistData","appliedSelection","config","checklistSelectionChange"]],template:function(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"button",0),d["\u0275\u0275listener"]("click",(function(e){return e.stopPropagation()})),d["\u0275\u0275elementStart"](1,"mat-icon",1),d["\u0275\u0275text"](2,"filter_list"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](3,"mat-menu",2,3),d["\u0275\u0275listener"]("closed",(function(){return t.menuClosed()})),d["\u0275\u0275template"](5,Io,12,1,"ng-template",4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](6,To,2,5,"ng-template",null,5,d["\u0275\u0275templateRefExtractor"]),d["\u0275\u0275template"](8,ko,1,1,"ng-template",null,6,d["\u0275\u0275templateRefExtractor"]),d["\u0275\u0275template"](10,Yo,1,7,"ng-template",null,7,d["\u0275\u0275templateRefExtractor"])),2&e){const e=d["\u0275\u0275reference"](4);d["\u0275\u0275property"]("disabled",t.isLoading)("matMenuTriggerFor",e)}},directives:function(){return[xe.a,Re.f,C.a,Re.g,Re.c,i.NgForOf,i.NgIf,i.NgTemplateOutlet,i.NgClass,fo,bo]},styles:[".iconbtn[_ngcontent-%COMP%]{color:#000!important;padding:0}  .mat-menu-panel{max-width:none!important;border-radius:12px}.filter-menu[_ngcontent-%COMP%]{height:260px;width:450px;padding:10px}.drawer[_ngcontent-%COMP%]{border-radius:12px;height:100%;overflow-y:auto}.drawer[_ngcontent-%COMP%]   .drawer-item[_ngcontent-%COMP%]{cursor:pointer;display:inline-block;width:100%;text-align:center;height:32px;padding:4px 24px;color:#45546e}.drawer[_ngcontent-%COMP%]   .has-active-filter[_ngcontent-%COMP%]{border-left:3px solid #ef4a61}.drawer[_ngcontent-%COMP%]   .active[_ngcontent-%COMP%]{background-color:rgba(232,233,238,.72);color:#cf0001}.option-value[_ngcontent-%COMP%]{display:inline-block;padding:5px 12px;border-radius:10px}.active-option[_ngcontent-%COMP%]{background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%);color:#fff}"]}),e})(),Ro=(()=>{class e{}return e.\u0275mod=d["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=d["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,Re.e,C.b,xe.b,Ka.b,Ne.c,c.p,ze.b,Xa.h,Ae.e,y.b,Ve.b]]}),e})();const No=["people_involved"],Vo=["mail_detail"];function zo(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",7),d["\u0275\u0275elementStart"](1,"div",8),d["\u0275\u0275text"](2,"Loading..."),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function jo(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",9),d["\u0275\u0275text"](1,"Something went wrong :("),d["\u0275\u0275elementEnd"]())}function Bo(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"dashboard-filter-menu",15),d["\u0275\u0275listener"]("filterApplied",(function(t){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](2).getReport(t)})),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275property"]("filterConfig",e.widgetConfig.filter_config)}}function $o(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",10),d["\u0275\u0275elementStart"](1,"div",11),d["\u0275\u0275elementStart"](2,"p"),d["\u0275\u0275text"](3),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](4,"div",12),d["\u0275\u0275elementStart"](5,"mat-icon",13),d["\u0275\u0275text"](6,"info"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](7,"div"),d["\u0275\u0275template"](8,Bo,1,1,"dashboard-filter-menu",14),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](3),d["\u0275\u0275textInterpolate"](e.widgetConfig.widget_name),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("matTooltip",e.widgetConfig.widget_info),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngIf",(null==e.widgetConfig||null==e.widgetConfig.filter_config?null:e.widgetConfig.filter_config.length)>0)}}function Uo(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",16),d["\u0275\u0275element"](1,"data-table",17),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("tableConfig",e.tableConfig)("tableContent",e.recent_sales_activities)}}function Qo(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"span",31),d["\u0275\u0275listener"]("click",(function(e){return e.stopPropagation()})),d["\u0275\u0275elementStart"](1,"p",32),d["\u0275\u0275text"](2,"To: \xa0"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](3,"p",33),d["\u0275\u0275text"](4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]().$implicit,t=d["\u0275\u0275reference"](14);d["\u0275\u0275property"]("matMenuTriggerFor",t),d["\u0275\u0275advance"](4),d["\u0275\u0275textInterpolate2"](" ",e.people_involved[0]," ",e.people_involved.length-1>0?"+ "+e.people_involved.length:""," ")}}function qo(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",34),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",e," ")}}function Wo(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",18),d["\u0275\u0275elementStart"](1,"div",19),d["\u0275\u0275elementStart"](2,"p",20),d["\u0275\u0275elementStart"](3,"b"),d["\u0275\u0275text"](4,"Sub: \xa0"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275text"](5),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](6,"span",21),d["\u0275\u0275elementStart"](7,"span",22),d["\u0275\u0275elementStart"](8,"p",23),d["\u0275\u0275text"](9,"From: \xa0"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](10,"p",24),d["\u0275\u0275text"](11),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](12,Qo,5,3,"span",25),d["\u0275\u0275elementStart"](13,"mat-menu",26,27),d["\u0275\u0275template"](15,qo,2,1,"div",28),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](16,"div",29),d["\u0275\u0275elementStart"](17,"p"),d["\u0275\u0275text"](18),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](19,"p",30),d["\u0275\u0275text"](20),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275advance"](5),d["\u0275\u0275textInterpolate"](e.title),d["\u0275\u0275advance"](6),d["\u0275\u0275textInterpolate"](e.mail_from),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",(null==e.people_involved?null:e.people_involved.length)>0),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngForOf",e.people_involved),d["\u0275\u0275advance"](3),d["\u0275\u0275textInterpolate"](e.planned_start_date_formatted),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"](" ",e.planned_start_time_formatted," ")}}function Go(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"span",36),d["\u0275\u0275listener"]("click",(function(e){return e.stopPropagation()})),d["\u0275\u0275elementStart"](1,"mat-icon",37),d["\u0275\u0275text"](2,"group "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](3,"p",20),d["\u0275\u0275text"](4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]().$implicit,t=d["\u0275\u0275reference"](2);d["\u0275\u0275property"]("matMenuTriggerFor",t),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("matBadge",null==e.people_involved_formatted?null:e.people_involved_formatted.length),d["\u0275\u0275advance"](3),d["\u0275\u0275textInterpolate1"](" ",e.people_involved_formatted[0]," ")}}function Ho(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",34),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",e," ")}}function Jo(e,t){if(1&e&&(d["\u0275\u0275template"](0,Go,5,3,"span",35),d["\u0275\u0275elementStart"](1,"mat-menu",26,27),d["\u0275\u0275template"](3,Ho,2,1,"div",28),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275property"]("ngIf",(null==e.people_involved_formatted?null:e.people_involved_formatted.length)>0),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngForOf",e.people_involved_formatted)}}let Ko=(()=>{class e{constructor(e,t,n){this.dashboardService=e,this.utilityService=t,this.router=n,this.isLoading=!0,this.isError=!1}get tableConfigBasedOnActivity(){return 2==this.sales_activity_type?{layout:"fixed",onRowClick:this.onRowClick.bind(this),columnConfig:[{column_name:"CALL DATE",column_type:"text",data_field:"planned_start_date_formatted",text_color:"#45546E"},{column_name:"CONTACT TYPE",column_type:"text",data_field:"contact_type",property_field_binding:{text_color_field:"contact_type_bg"}},{column_name:"CONTACT FROM",column_type:"text",data_field:"organizer_name",text_color:"#45546E"},{column_name:"CONTACT TO",column_type:"template",template_ref:this.people_involved},{column_name:"created in",column_type:"text",data_field:"application_name",text_color:"#45546E",text_overflow:!0}]}:3==this.sales_activity_type?{onRowClick:this.onRowClick.bind(this),columnConfig:[{column_name:"",column_type:"template",template_ref:this.mail_detail}]}:4==this.sales_activity_type?{layout:"auto",onRowClick:this.onRowClick.bind(this),columnConfig:[{column_name:"task name",column_type:"text",data_field:"title",text_color:"#45546E",text_overflow:!0},{column_name:"created in",column_type:"text",data_field:"application_name",text_color:"#45546E",text_overflow:!0,gutter_space:"5%"},{column_name:"due date",column_type:"text",data_field:"task_due_date",formatcell:e=>s()(e.cellData).format("DD-MMM-YYYY"),gutter_space:"5%"},{column_name:"status",column_type:"text",data_field:"activity_status_name",property_field_binding:{text_color_field:"status_color"}}]}:void 0}getReport(e){this.dashboardService.getRecentActivities(this.sales_activity_type,this.widgetConfig.component_config.limit,e).subscribe(e=>{this.recent_sales_activities=e.recent_sales_activities,this.recent_sales_activities.forEach(e=>{e.planned_start_date_formatted=null!=e.planned_start_date&&"0000-00-00 00:00:00"!=e.planned_start_date?s()(e.planned_start_date).format("DD-MMM-YYYY"):"-",e.planned_start_time_formatted=null!=e.planned_start_date&&"0000-00-00 00:00:00"!=e.planned_start_date?s()(e.planned_start_date).format("h:mm a"):"-"}),this.activity_type_name=e.activity_type_name,this.isLoading=!1,this.isError=!1},e=>{console.log(e),this.isLoading=!1,this.isError=!0,this.utilityService.showErrorMessage("Couldn't get recent activites","KEBS")})}ngOnInit(){this.widgetConfig=this.dashboardService.getConfigFor(this.widget_id),this.sales_activity_type=this.widgetConfig.component_config.sales_activity_type,console.log(this.sales_activity_type),this.getReport(this.widgetConfig.filter_config)}ngAfterViewInit(){console.log("viewinit"),console.log(this.sales_activity_type),this.sales_activity_type=this.widgetConfig.component_config.sales_activity_type,this.tableConfig=this.tableConfigBasedOnActivity}onRowClick(e){33==e.application_id?window.open(`${window.location.origin}/main/accounts/${e.application_reference_id}/${this.utilityService.encodeURIComponent(e.customer_name)}`):34==e.application_id?window.open(`${window.location.origin}/main/contacts/${e.application_reference_id}`):35==e.application_id?window.open(`${window.location.origin}/main/leads/${e.application_reference_id}/${this.encodeURIComponent(e.lead_name)}`):36==e.application_id&&window.open(`${window.location.origin}/main/opportunities/${e.application_reference_id}/${this.utilityService.encodeURIComponent(e.opportunity_name)}`)}encodeURIComponent(e){return encodeURIComponent(e).replace(/[!'()*]/g,(function(e){return"%"+e.charCodeAt(0).toString(16)}))}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](v),d["\u0275\u0275directiveInject"](ue.a),d["\u0275\u0275directiveInject"](a.g))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-recent-sales-activities"]],viewQuery:function(e,t){if(1&e&&(d["\u0275\u0275viewQuery"](No,!0),d["\u0275\u0275viewQuery"](Vo,!0)),2&e){let e;d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.people_involved=e.first),d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.mail_detail=e.first)}},inputs:{widget_id:"widget_id"},decls:9,vars:4,consts:[[1,"recent-activities-card"],["class","loading-wrapper",4,"ngIf"],["class","error",4,"ngIf"],["class","row p-0 justify-content-between",4,"ngIf"],["class","card-body",4,"ngIf"],["mail_detail",""],["people_involved",""],[1,"loading-wrapper"],[1,"loading"],[1,"error"],[1,"row","p-0","justify-content-between"],[1,"title"],[1,"iconbtn",2,"margin-left","5px"],[2,"font-size","larger",3,"matTooltip"],[3,"filterConfig","filterApplied",4,"ngIf"],[3,"filterConfig","filterApplied"],[1,"card-body"],[3,"tableConfig","tableContent"],[1,"row","p-0","border-"],[1,"col-8","p-0"],[1,"col-10","p-0","overflow"],[1,"row","p-0","det"],[1,"row","col-6","p-0","nowrap"],[2,"color","#3dbf1c"],[1,"overflow"],["class","row col-6 p-0 pl-2 nowrap overflow","style","flex-wrap: nowrap !important",3,"matMenuTriggerFor","click",4,"ngIf"],["yPosition","above","xPosition","after",1,"history-menu"],["peopleMenu","matMenu"],["style","padding: 5px","class","user-det",4,"ngFor","ngForOf"],[1,"col-4",2,"text-align","end"],[2,"font-weight","400","font-size","11px"],[1,"row","col-6","p-0","pl-2","nowrap","overflow",2,"flex-wrap","nowrap !important",3,"matMenuTriggerFor","click"],[2,"color","#4d8af0"],[1,"p-0","overflow"],[1,"user-det",2,"padding","5px"],["class","row p-0 text","style","flex-wrap: nowrap !important",3,"matMenuTriggerFor","click",4,"ngIf"],[1,"row","p-0","text",2,"flex-wrap","nowrap !important",3,"matMenuTriggerFor","click"],["matBadgeSize","small",1,"icon-badge",3,"matBadge"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"mat-card",0),d["\u0275\u0275template"](1,zo,3,0,"div",1),d["\u0275\u0275template"](2,jo,2,0,"div",2),d["\u0275\u0275template"](3,$o,9,3,"div",3),d["\u0275\u0275template"](4,Uo,2,2,"div",4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](5,Wo,21,6,"ng-template",null,5,d["\u0275\u0275templateRefExtractor"]),d["\u0275\u0275template"](7,Jo,4,2,"ng-template",null,6,d["\u0275\u0275templateRefExtractor"])),2&e&&(d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.isLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.isError),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!t.isLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!t.isLoading&&!t.isError))},directives:[ve.a,i.NgIf,C.a,y.a,Ao,B,Re.g,i.NgForOf,Re.f,Ve.a],styles:[".recent-activities-card[_ngcontent-%COMP%]{min-height:300px;margin:7px;height:100%;padding:14px!important;box-shadow:0 6px 18px rgba(0,0,0,.1)}.recent-activities-card[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-weight:500;display:flex;font-size:14px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;padding:0!important;color:#45546e}.card-body[_ngcontent-%COMP%]{overflow-y:auto;max-height:350px;padding:0!important}.error[_ngcontent-%COMP%]{display:flex;height:100%;font-size:small;color:#cf2020;vertical-align:center;justify-content:center;align-items:center;width:100%}.iconbtn[_ngcontent-%COMP%]{color:#7b7b7b!important;padding:0;height:-moz-fit-content;height:fit-content}",".nowrap[_ngcontent-%COMP%] {\n      flex-wrap: nowrap !important;\n    }\n\n    .det[_ngcontent-%COMP%] {\n      font-size: 12px;\n    }\n\n    .border-[_ngcontent-%COMP%] {\n      border-bottom: 1px solid #e8e9ee;\n      height: 55px;\n      margin-bottom: 10px;\n    }\n\n    .overflow[_ngcontent-%COMP%] {\n      text-overflow: ellipsis;\n      overflow: hidden;\n      white-space: nowrap;\n    }",".overflow[_ngcontent-%COMP%] {\n      text-overflow: ellipsis;\n      overflow: hidden;\n      white-space: nowrap;\n    }\n\n    .text[_ngcontent-%COMP%] {\n      align-items: center;\n    }\n\n    .text[_ngcontent-%COMP%]:hover {\n      transition: ease-in-out;\n      cursor: default;\n      color: #4d8af0;\n    }\n\n    .icon-badge[_ngcontent-%COMP%] {\n      font-size: large;\n      margin-right: 10px;\n    }\n\n    .history-menu[_ngcontent-%COMP%] {\n      max-height: 220px;\n      overflow: scroll;\n    }\n\n    .user-det[_ngcontent-%COMP%] {\n      color: #45546e;\n      font-size: 12px;\n      white-space: nowrap;\n      text-overflow: ellipsis;\n    }\n\n    .mat-menu-panel[_ngcontent-%COMP%] {\n      max-width: none !important;\n    }"]}),e})();const Xo=["mail_detail"];function Zo(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",6),d["\u0275\u0275elementStart"](1,"div",7),d["\u0275\u0275text"](2,"Loading..."),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function er(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",8),d["\u0275\u0275text"](1,"Something went wrong :("),d["\u0275\u0275elementEnd"]())}function tr(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"dashboard-filter-menu",14),d["\u0275\u0275listener"]("filterApplied",(function(t){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](2).getReport(t)})),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275property"]("filterConfig",e.widgetConfig.filter_config)}}function nr(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",9),d["\u0275\u0275elementStart"](1,"div",10),d["\u0275\u0275elementStart"](2,"p"),d["\u0275\u0275text"](3),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](4,"div",11),d["\u0275\u0275elementStart"](5,"mat-icon",12),d["\u0275\u0275text"](6,"info"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](7,"div"),d["\u0275\u0275template"](8,tr,1,1,"dashboard-filter-menu",13),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](3),d["\u0275\u0275textInterpolate"](e.widgetConfig.widget_name),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("matTooltip",e.widgetConfig.widget_info),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngIf",(null==e.widgetConfig||null==e.widgetConfig.filter_config?null:e.widgetConfig.filter_config.length)>0)}}function ir(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",15),d["\u0275\u0275element"](1,"data-table",16),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("tableConfig",e.tableConfig)("tableContent",e.data)}}const ar=function(e){return{color:e}};function or(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementStart"](1,"span",22),d["\u0275\u0275elementStart"](2,"p"),d["\u0275\u0275text"](3),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](4,"p",23),d["\u0275\u0275text"](5),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](2),d["\u0275\u0275styleMap"](d["\u0275\u0275pureFunction1"](4,ar,n.getStatusColor(e.status_color))),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",e.activity_status_name,": \xa0 "),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate"](e.count)}}function rr(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",17),d["\u0275\u0275elementStart"](1,"div",18),d["\u0275\u0275elementStart"](2,"p",19),d["\u0275\u0275elementStart"](3,"b"),d["\u0275\u0275text"](4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](5,"span",20),d["\u0275\u0275template"](6,or,6,6,"div",21),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275advance"](4),d["\u0275\u0275textInterpolate"](e[0].employee_name),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngForOf",e)}}let lr=(()=>{class e{constructor(e,t,n,i){this.dashboardService=e,this.dialog=t,this.router=n,this.utilityService=i,this.isLoading=!0,this.isError=!1}get tableConfigBasedOnActivity(){return{onRowClick:this.onRowClick.bind(this),columnConfig:[{column_name:"",column_type:"template",template_ref:this.mail_detail}]}}getStatusColor(e){return JSON.parse(e)}getReport(e){this.dashboardService.getMyTeamActivities(e).subscribe(e=>{this.data=e,this.isLoading=!1},e=>{console.log(e),this.isLoading=!1,this.isError=!0,this.utilityService.showErrorMessage("Couldn't get team activites","KEBS")})}ngOnInit(){this.widgetConfig=this.dashboardService.getConfigFor(this.widget_id),this.getReport(this.widgetConfig.filter_config)}ngAfterViewInit(){this.tableConfig=this.tableConfigBasedOnActivity}onRowClick(e){let t={useDxTable:this.widgetConfig.component_config.useDxTable,columnConfig:this.summaryColumnConfig,dataObservername:"tasks_summary_observable",title:"Tasks of "+e[0].employee_name,subtitle:"Total "+e.reduce((function(e,t){return e+t.count}),0),summaryFor:e[0].member_oid};this.dashboardService.getTasksDataSummary(e[0].member_oid),this.dialog.open(ge,{height:"80%",width:"80%",data:t})}get summaryColumnConfig(){return[{column_name:"Task name",column_type:"text",data_field:"task_name",text_overflow:!0},{column_name:"Assigned by",column_type:"text",data_field:"created_by_name",text_overflow:!0,gutter_space:"2%"},{column_name:"Status",column_type:"text",data_field:"activity_status_name",property_field_binding:{text_color_field:"status_color"},gutter_space:"5%"},{column_name:"Start date",column_type:"text",data_field:"planned_start_date",formatcell:e=>s()(e.cellData).format("DD-MMM-YYYY"),gutter_space:"5%"},{column_name:"Due date",column_type:"text",data_field:"task_due_date",formatcell:e=>s()(e.cellData).format("DD-MMM-YYYY")},{column_name:"created in",column_type:"text",data_field:"application_name",text_color:"#45546E",text_overflow:!0,gutter_space:"5%"},{column_name:"",column_type:"actions",actions:[{action_name:"Open in new tab",action_icon:"open_in_new",action_click:this.openInNewTab.bind(this)}]}]}encodeURIComponent(e){return encodeURIComponent(e).replace(/[!'()*]/g,(function(e){return"%"+e.charCodeAt(0).toString(16)}))}openInNewTab(e){33==e.application_id?window.open(`${window.location.origin}/main/accounts/${e.application_reference_id}/${this.utilityService.encodeURIComponent(e.customer_name)}`):34==e.application_id?window.open(`${window.location.origin}/main/contacts/${e.application_reference_id}`):35==e.application_id?window.open(`${window.location.origin}/main/leads/${e.application_reference_id}/${this.encodeURIComponent(e.lead_name)}`):36==e.application_id&&window.open(`${window.location.origin}/main/opportunities/${e.application_reference_id}/${this.utilityService.encodeURIComponent(e.opportunity_name)}`)}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](v),d["\u0275\u0275directiveInject"](fe.a),d["\u0275\u0275directiveInject"](a.g),d["\u0275\u0275directiveInject"](ue.a))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-my-activities-widget"]],viewQuery:function(e,t){if(1&e&&d["\u0275\u0275viewQuery"](Xo,!0),2&e){let e;d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.mail_detail=e.first)}},inputs:{widget_id:"widget_id"},decls:7,vars:4,consts:[[1,"my-team-activities-card"],["class","loading-wrapper",4,"ngIf"],["class","error",4,"ngIf"],["class","row p-0 justify-content-between",4,"ngIf"],["class","card-body",4,"ngIf"],["mail_detail",""],[1,"loading-wrapper"],[1,"loading"],[1,"error"],[1,"row","p-0","justify-content-between"],[1,"title"],[1,"iconbtn",2,"margin-left","5px"],[2,"font-size","larger",3,"matTooltip"],[3,"filterConfig","filterApplied",4,"ngIf"],[3,"filterConfig","filterApplied"],[1,"card-body"],[3,"tableConfig","tableContent"],[1,"row","box","p-0","border-"],[1,"col-8","p-0"],[1,"col","p-0","overflow"],[1,"row","p-0","justify-content-between","det"],[4,"ngFor","ngForOf"],[1,"row","col","p-0","nowrap"],[1,"overflow"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"mat-card",0),d["\u0275\u0275template"](1,Zo,3,0,"div",1),d["\u0275\u0275template"](2,er,2,0,"div",2),d["\u0275\u0275template"](3,nr,9,3,"div",3),d["\u0275\u0275template"](4,ir,2,2,"div",4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](5,rr,7,2,"ng-template",null,5,d["\u0275\u0275templateRefExtractor"])),2&e&&(d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.isLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.isError),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!t.isLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!t.isLoading&&!t.isError))},directives:[ve.a,i.NgIf,C.a,y.a,Ao,B,i.NgForOf],styles:[".my-team-activities-card[_ngcontent-%COMP%]{min-height:300px;margin:7px;height:100%;padding:14px!important;box-shadow:0 6px 18px rgba(0,0,0,.1)}.my-team-activities-card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]{overflow-y:auto;max-height:350px;padding:0!important}.my-team-activities-card[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-weight:500;font-size:14px;display:flex;line-height:16px;letter-spacing:.02em;text-transform:capitalize;padding:0!important;color:#45546e}.error[_ngcontent-%COMP%]{display:flex;height:100%;font-size:small;color:#cf2020;vertical-align:center;justify-content:center;align-items:center;width:100%}.iconbtn[_ngcontent-%COMP%]{color:#7b7b7b!important;padding:0;height:-moz-fit-content;height:fit-content}",".nowrap[_ngcontent-%COMP%] {\n      flex-wrap: nowrap !important;\n    }\n\n    .det[_ngcontent-%COMP%] {\n      font-size: 12px;\n    }\n\n    .border-[_ngcontent-%COMP%] {\n      border-bottom: 1px solid #e8e9ee;\n      height: 55px;\n      margin-bottom: 10px;\n    }\n    .overflow[_ngcontent-%COMP%] {\n      text-overflow: ellipsis;\n      overflow: hidden;\n      white-space: nowrap;\n    }\n    .box[_ngcontent-%COMP%] {\n      position: relative;\n      border-radius: 5px;\n      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n      padding: 5px;\n      cursor: pointer;\n      border-radius: 5px;\n      -webkit-transition: all 0.6s cubic-bezier(0.165, 0.84, 0.44, 1);\n      transition: all 0.6s cubic-bezier(0.165, 0.84, 0.44, 1);\n    }\n    .box[_ngcontent-%COMP%]:hover {\n      color: #4d8af0;\n      font-size: 16px;\n    }"]}),e})();const sr=["mail_detail"];function cr(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",6),d["\u0275\u0275elementStart"](1,"div",7),d["\u0275\u0275text"](2,"Loading..."),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function dr(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",8),d["\u0275\u0275text"](1,"Something went wrong :("),d["\u0275\u0275elementEnd"]())}function pr(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"dashboard-filter-menu",14),d["\u0275\u0275listener"]("filterApplied",(function(t){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](2).getReport(t)})),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275property"]("filterConfig",e.widgetConfig.filter_config)}}function mr(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",9),d["\u0275\u0275elementStart"](1,"div",10),d["\u0275\u0275elementStart"](2,"p"),d["\u0275\u0275text"](3),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](4,"div",11),d["\u0275\u0275elementStart"](5,"mat-icon",12),d["\u0275\u0275text"](6,"info"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](7,"div"),d["\u0275\u0275template"](8,pr,1,1,"dashboard-filter-menu",13),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](3),d["\u0275\u0275textInterpolate"](e.widgetConfig.widget_name),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("matTooltip",e.widgetConfig.widget_info),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngIf",(null==e.widgetConfig||null==e.widgetConfig.filter_config?null:e.widgetConfig.filter_config.length)>0)}}function gr(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",15),d["\u0275\u0275element"](1,"data-table",16),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("tableConfig",e.tableConfig)("tableContent",e.data)}}const ur=function(e){return{color:e}};function fr(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementStart"](1,"span",22),d["\u0275\u0275elementStart"](2,"p"),d["\u0275\u0275text"](3),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](4,"p",23),d["\u0275\u0275text"](5),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275advance"](2),d["\u0275\u0275styleMap"](d["\u0275\u0275pureFunction1"](4,ur,e.icon_bg)),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",e.activity_sub_type_name,": \xa0 "),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate"](e.count)}}function hr(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",17),d["\u0275\u0275elementStart"](1,"div",18),d["\u0275\u0275elementStart"](2,"p",19),d["\u0275\u0275elementStart"](3,"b"),d["\u0275\u0275text"](4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](5,"span",20),d["\u0275\u0275template"](6,fr,6,6,"div",21),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275advance"](4),d["\u0275\u0275textInterpolate"](e[0].employee_name),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngForOf",e)}}let _r=(()=>{class e{constructor(e,t,n,i){this.dashboardService=e,this.dialog=t,this.router=n,this.utilityService=i,this.isLoading=!0,this.isError=!1}get tableConfigBasedOnActivity(){return{onRowClick:this.onRowClick.bind(this),columnConfig:[{column_name:"",column_type:"template",template_ref:this.mail_detail}]}}getStatusColor(e){return JSON.parse(e)}getReport(e){this.dashboardService.getMyTeamCalls(e).subscribe(e=>{this.data=e,this.isLoading=!1},e=>{console.log(e),this.isLoading=!1,this.isError=!0,this.utilityService.showErrorMessage("Couldn't get team activites","KEBS")})}ngOnInit(){this.widgetConfig=this.dashboardService.getConfigFor(this.widget_id),this.getReport(this.widgetConfig.filter_config)}ngAfterViewInit(){this.tableConfig=this.tableConfigBasedOnActivity}onRowClick(e){console.log(e);let t={columnConfig:this.summaryColumnConfig,useDxTable:this.widgetConfig.component_config.useDxTable,dataObservername:"calls_summary_observable",title:"Call logs of "+e[0].employee_name,subtitle:"Total "+e.reduce((function(e,t){return e+t.count}),0),summaryFor:e[0].member_oid};this.dashboardService.getCallsDataSummary(e[0].member_oid),this.dialog.open(ge,{height:"80%",width:"80%",data:t})}get summaryColumnConfig(){return[{column_name:"Call name",column_type:"text",data_field:"call_name",text_overflow:!0},{column_name:"Placed by",column_type:"text",data_field:"employee_name",text_overflow:!0,gutter_space:"2%"},{column_name:"Status",column_type:"text",data_field:"activity_sub_type_name",property_field_binding:{text_color_field:"status_color"},gutter_space:"5%"},{column_name:"Placed on",column_type:"text",data_field:"start_date",formatcell:e=>e.cellData?s()(e.cellData).format("DD-MMM-YYYY"):"-",gutter_space:"5%"},{column_name:"Duration (mins)",column_type:"text",data_field:"duration",formatcell:e=>e.cellData<1?"Less than a minute":e.cellData},{column_name:"created in",column_type:"text",data_field:"application_name",text_color:"#45546E",text_overflow:!0,gutter_space:"5%"},{column_name:"",column_type:"actions",actions:[{action_name:"Open in new tab",action_icon:"open_in_new",action_click:this.openInNewTab.bind(this)}]}]}encodeURIComponent(e){return encodeURIComponent(e).replace(/[!'()*]/g,(function(e){return"%"+e.charCodeAt(0).toString(16)}))}openInNewTab(e){33==e.application_id?window.open(`${window.location.origin}/main/accounts/${e.application_reference_id}/${this.utilityService.encodeURIComponent(e.customer_name)}`):34==e.application_id?window.open(`${window.location.origin}/main/contacts/${e.application_reference_id}`):35==e.application_id?window.open(`${window.location.origin}/main/leads/${e.application_reference_id}/${this.encodeURIComponent(e.lead_name)}`):36==e.application_id&&window.open(`${window.location.origin}/main/opportunities/${e.application_reference_id}/${this.utilityService.encodeURIComponent(e.opportunity_name)}`)}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](v),d["\u0275\u0275directiveInject"](fe.a),d["\u0275\u0275directiveInject"](a.g),d["\u0275\u0275directiveInject"](ue.a))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-my-team-calls-widget"]],viewQuery:function(e,t){if(1&e&&d["\u0275\u0275viewQuery"](sr,!0),2&e){let e;d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.mail_detail=e.first)}},inputs:{widget_id:"widget_id"},decls:7,vars:4,consts:[[1,"my-team-activities-card"],["class","loading-wrapper",4,"ngIf"],["class","error",4,"ngIf"],["class","row p-0 justify-content-between",4,"ngIf"],["class","card-body",4,"ngIf"],["mail_detail",""],[1,"loading-wrapper"],[1,"loading"],[1,"error"],[1,"row","p-0","justify-content-between"],[1,"title"],[1,"iconbtn",2,"margin-left","5px"],[2,"font-size","larger",3,"matTooltip"],[3,"filterConfig","filterApplied",4,"ngIf"],[3,"filterConfig","filterApplied"],[1,"card-body"],[3,"tableConfig","tableContent"],[1,"row","box","p-0","border-"],[1,"col-8","p-0"],[1,"col","p-0","overflow"],[1,"row","p-0","justify-content-between","det"],[4,"ngFor","ngForOf"],[1,"row","col","p-0","nowrap"],[1,"overflow"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"mat-card",0),d["\u0275\u0275template"](1,cr,3,0,"div",1),d["\u0275\u0275template"](2,dr,2,0,"div",2),d["\u0275\u0275template"](3,mr,9,3,"div",3),d["\u0275\u0275template"](4,gr,2,2,"div",4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](5,hr,7,2,"ng-template",null,5,d["\u0275\u0275templateRefExtractor"])),2&e&&(d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.isLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.isError),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!t.isLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!t.isLoading&&!t.isError))},directives:[ve.a,i.NgIf,C.a,y.a,Ao,B,i.NgForOf],styles:[".my-team-activities-card[_ngcontent-%COMP%]{min-height:300px;margin:7px;height:100%;padding:14px!important;box-shadow:0 6px 18px rgba(0,0,0,.1)}.my-team-activities-card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]{overflow-y:auto;max-height:350px;padding:0!important}.my-team-activities-card[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-weight:500;font-size:14px;line-height:16px;display:flex;letter-spacing:.02em;text-transform:capitalize;padding:0!important;color:#45546e}.error[_ngcontent-%COMP%]{display:flex;height:100%;font-size:small;color:#cf2020;vertical-align:center;justify-content:center;align-items:center;width:100%}.iconbtn[_ngcontent-%COMP%]{color:#7b7b7b!important;padding:0;height:-moz-fit-content;height:fit-content}",".nowrap[_ngcontent-%COMP%] {\n      flex-wrap: nowrap !important;\n    }\n\n    .det[_ngcontent-%COMP%] {\n      font-size: 12px;\n    }\n\n    .border-[_ngcontent-%COMP%] {\n      border-bottom: 1px solid #e8e9ee;\n      height: 55px;\n      margin-bottom: 10px;\n    }\n    .overflow[_ngcontent-%COMP%] {\n      text-overflow: ellipsis;\n      overflow: hidden;\n      white-space: nowrap;\n    }\n    .box[_ngcontent-%COMP%] {\n      position: relative;\n      border-radius: 5px;\n      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n      padding: 5px;\n      cursor: pointer;\n      border-radius: 5px;\n      -webkit-transition: all 0.6s cubic-bezier(0.165, 0.84, 0.44, 1);\n      transition: all 0.6s cubic-bezier(0.165, 0.84, 0.44, 1);\n    }\n    .box[_ngcontent-%COMP%]:hover {\n      color: #4d8af0;\n      font-size: 16px;\n    }"]}),e})();const vr=["mail_detail"],yr=["mail_detail_summary"];function Cr(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",7),d["\u0275\u0275elementStart"](1,"div",8),d["\u0275\u0275text"](2,"Loading..."),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function xr(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",9),d["\u0275\u0275text"](1,"Something went wrong :("),d["\u0275\u0275elementEnd"]())}function br(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"dashboard-filter-menu",15),d["\u0275\u0275listener"]("filterApplied",(function(t){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](2).getReport(t)})),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275property"]("filterConfig",e.widgetConfig.filter_config)}}function wr(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",10),d["\u0275\u0275elementStart"](1,"div",11),d["\u0275\u0275elementStart"](2,"p"),d["\u0275\u0275text"](3),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](4,"div",12),d["\u0275\u0275elementStart"](5,"mat-icon",13),d["\u0275\u0275text"](6,"info"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](7,"div"),d["\u0275\u0275template"](8,br,1,1,"dashboard-filter-menu",14),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](3),d["\u0275\u0275textInterpolate"](e.widgetConfig.widget_name),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("matTooltip",e.widgetConfig.widget_info),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngIf",(null==e.widgetConfig||null==e.widgetConfig.filter_config?null:e.widgetConfig.filter_config.length)>0)}}function Sr(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",16),d["\u0275\u0275element"](1,"data-table",17),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("tableConfig",e.tableConfig)("tableContent",e.data)}}const Or=function(e){return{color:e}};function Er(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementStart"](1,"span",23),d["\u0275\u0275elementStart"](2,"p"),d["\u0275\u0275text"](3),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](4,"p",24),d["\u0275\u0275text"](5),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](2),d["\u0275\u0275styleMap"](d["\u0275\u0275pureFunction1"](4,Or,n.getColor(e.mail_type))),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",e.mail_type,": \xa0 "),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate"](e.count)}}function Dr(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",18),d["\u0275\u0275elementStart"](1,"div",19),d["\u0275\u0275elementStart"](2,"p",20),d["\u0275\u0275elementStart"](3,"b"),d["\u0275\u0275text"](4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](5,"span",21),d["\u0275\u0275template"](6,Er,6,6,"div",22),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275advance"](4),d["\u0275\u0275textInterpolate"](e[0].employee_name),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngForOf",e)}}function Mr(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"span",36),d["\u0275\u0275listener"]("click",(function(e){return e.stopPropagation()})),d["\u0275\u0275elementStart"](1,"p",37),d["\u0275\u0275text"](2,"To: \xa0"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](3,"p",38),d["\u0275\u0275text"](4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]().$implicit,t=d["\u0275\u0275reference"](14);d["\u0275\u0275property"]("matMenuTriggerFor",t),d["\u0275\u0275advance"](4),d["\u0275\u0275textInterpolate2"](" ",e.people_involved[0]," ",e.people_involved.length-1>0?"+ "+e.people_involved.length:""," ")}}function Pr(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",39),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",e," ")}}function Ir(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",25),d["\u0275\u0275elementStart"](1,"div",19),d["\u0275\u0275elementStart"](2,"p",26),d["\u0275\u0275elementStart"](3,"b"),d["\u0275\u0275text"](4,"Sub: \xa0"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275text"](5),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](6,"span",27),d["\u0275\u0275elementStart"](7,"span",28),d["\u0275\u0275elementStart"](8,"p",29),d["\u0275\u0275text"](9,"From: \xa0"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](10,"p",24),d["\u0275\u0275text"](11),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](12,Mr,5,3,"span",30),d["\u0275\u0275elementStart"](13,"mat-menu",31,32),d["\u0275\u0275template"](15,Pr,2,1,"div",33),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](16,"div",34),d["\u0275\u0275elementStart"](17,"p"),d["\u0275\u0275text"](18),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](19,"p",35),d["\u0275\u0275text"](20),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275advance"](5),d["\u0275\u0275textInterpolate"](e.title),d["\u0275\u0275advance"](6),d["\u0275\u0275textInterpolate"](e.mail_from),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",(null==e.people_involved?null:e.people_involved.length)>0),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngForOf",e.people_involved),d["\u0275\u0275advance"](3),d["\u0275\u0275textInterpolate"](e.planned_start_date_formatted),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"](" ",e.planned_start_time_formatted," ")}}let Fr=(()=>{class e{constructor(e,t,n,i){this.dashboardService=e,this.dialog=t,this.router=n,this.utilityService=i,this.isLoading=!0,this.isError=!1}get tableConfigBasedOnActivity(){return{onRowClick:this.onRowClick.bind(this),columnConfig:[{column_name:"",column_type:"template",template_ref:this.mail_detail}]}}getStatusColor(e){return JSON.parse(e)}getReport(e){this.dashboardService.getMyTeamMails(e).subscribe(e=>{this.data=e,this.isLoading=!1},e=>{console.log(e),this.isLoading=!1,this.isError=!0,this.utilityService.showErrorMessage("Couldn't get team activites","KEBS")})}ngOnInit(){this.widgetConfig=this.dashboardService.getConfigFor(this.widget_id),this.getReport(this.widgetConfig.filter_config)}ngAfterViewInit(){this.tableConfig=this.tableConfigBasedOnActivity}getColor(e){return"Sent"==e?"#10AC84":"#575FCF"}onRowClick(e){let t={onRowClick:this.openInNewTab.bind(this),useDxTable:this.widgetConfig.component_config.useDxTable,columnConfig:this.summaryColumnConfig,dataObservername:"mails_summary_observable",title:"Mails of "+e[0].employee_name,subtitle:"Total "+e.reduce((function(e,t){return e+t.count}),0),summaryFor:e[0].member_oid,noBorder:!0};this.dashboardService.getMailsDataSummary(e[0].member_oid),this.dialog.open(ge,{height:"80%",width:"80%",data:t})}get summaryColumnConfig(){return[{column_name:"",column_type:"template",template_ref:this.mail_detail_summary}]}encodeURIComponent(e){return encodeURIComponent(e).replace(/[!'()*]/g,(function(e){return"%"+e.charCodeAt(0).toString(16)}))}openInNewTab(e){33==e.application_id?window.open(`${window.location.origin}/main/accounts/${e.application_reference_id}/${this.utilityService.encodeURIComponent(e.customer_name)}`):34==e.application_id?window.open(`${window.location.origin}/main/contacts/${e.application_reference_id}`):35==e.application_id?window.open(`${window.location.origin}/main/leads/${e.application_reference_id}/${this.encodeURIComponent(e.lead_name)}`):36==e.application_id&&window.open(`${window.location.origin}/main/opportunities/${e.application_reference_id}/${this.utilityService.encodeURIComponent(e.opportunity_name)}`)}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](v),d["\u0275\u0275directiveInject"](fe.a),d["\u0275\u0275directiveInject"](a.g),d["\u0275\u0275directiveInject"](ue.a))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-my-team-mails-widget"]],viewQuery:function(e,t){if(1&e&&(d["\u0275\u0275viewQuery"](vr,!0),d["\u0275\u0275viewQuery"](yr,!0)),2&e){let e;d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.mail_detail=e.first),d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.mail_detail_summary=e.first)}},inputs:{widget_id:"widget_id"},decls:9,vars:4,consts:[[1,"my-team-activities-card"],["class","loading-wrapper",4,"ngIf"],["class","error",4,"ngIf"],["class","row p-0 justify-content-between",4,"ngIf"],["class","card-body",4,"ngIf"],["mail_detail",""],["mail_detail_summary",""],[1,"loading-wrapper"],[1,"loading"],[1,"error"],[1,"row","p-0","justify-content-between"],[1,"title"],[1,"iconbtn",2,"margin-left","5px"],[2,"font-size","larger",3,"matTooltip"],[3,"filterConfig","filterApplied",4,"ngIf"],[3,"filterConfig","filterApplied"],[1,"card-body"],[3,"tableConfig","tableContent"],[1,"row","box","p-0","border-"],[1,"col-8","p-0"],[1,"col","p-0","overflow"],[1,"row","p-0","justify-content-between","det"],[4,"ngFor","ngForOf"],[1,"row","col","p-0","nowrap"],[1,"overflow"],[1,"row","p-0","border-"],[1,"col-10","p-0","overflow"],[1,"row","p-0","det"],[1,"row","col-6","p-0","nowrap"],[2,"color","#3dbf1c"],["class","row col-6 p-0 pl-2 nowrap overflow","style","flex-wrap: nowrap !important",3,"matMenuTriggerFor","click",4,"ngIf"],["yPosition","above","xPosition","after",1,"history-menu"],["peopleMenu","matMenu"],["style","padding: 5px","class","user-det",4,"ngFor","ngForOf"],[1,"col-4",2,"text-align","end"],[2,"font-weight","400","font-size","11px"],[1,"row","col-6","p-0","pl-2","nowrap","overflow",2,"flex-wrap","nowrap !important",3,"matMenuTriggerFor","click"],[2,"color","#4d8af0"],[1,"p-0","overflow"],[1,"user-det",2,"padding","5px"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"mat-card",0),d["\u0275\u0275template"](1,Cr,3,0,"div",1),d["\u0275\u0275template"](2,xr,2,0,"div",2),d["\u0275\u0275template"](3,wr,9,3,"div",3),d["\u0275\u0275template"](4,Sr,2,2,"div",4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](5,Dr,7,2,"ng-template",null,5,d["\u0275\u0275templateRefExtractor"]),d["\u0275\u0275template"](7,Ir,21,6,"ng-template",null,6,d["\u0275\u0275templateRefExtractor"])),2&e&&(d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.isLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.isError),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!t.isLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!t.isLoading&&!t.isError))},directives:[ve.a,i.NgIf,C.a,y.a,Ao,B,i.NgForOf,Re.g,Re.f],styles:[".my-team-activities-card[_ngcontent-%COMP%]{min-height:300px;margin:7px;height:100%;padding:14px!important;box-shadow:0 6px 18px rgba(0,0,0,.1)}.my-team-activities-card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]{overflow-y:auto;max-height:350px;padding:0!important}.my-team-activities-card[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-weight:500;display:flex;font-size:14px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;padding:0!important;color:#45546e}.error[_ngcontent-%COMP%]{display:flex;height:100%;font-size:small;color:#cf2020;vertical-align:center;justify-content:center;align-items:center;width:100%}.iconbtn[_ngcontent-%COMP%]{color:#7b7b7b!important;padding:0;height:-moz-fit-content;height:fit-content}",".nowrap[_ngcontent-%COMP%] {\n      flex-wrap: nowrap !important;\n    }\n\n    .det[_ngcontent-%COMP%] {\n      font-size: 12px;\n    }\n\n    .border-[_ngcontent-%COMP%] {\n      border-bottom: 1px solid #e8e9ee;\n      height: 55px;\n      margin-bottom: 10px;\n    }\n    .overflow[_ngcontent-%COMP%] {\n      text-overflow: ellipsis;\n      overflow: hidden;\n      white-space: nowrap;\n    }\n    .box[_ngcontent-%COMP%] {\n      position: relative;\n      border-radius: 5px;\n      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n      padding: 5px;\n      cursor: pointer;\n      border-radius: 5px;\n      -webkit-transition: all 0.6s cubic-bezier(0.165, 0.84, 0.44, 1);\n      transition: all 0.6s cubic-bezier(0.165, 0.84, 0.44, 1);\n    }\n    .box[_ngcontent-%COMP%]:hover {\n      color: #4d8af0;\n      font-size: 16px;\n    }",".nowrap[_ngcontent-%COMP%] {\n      flex-wrap: nowrap !important;\n    }\n\n    .det[_ngcontent-%COMP%] {\n      font-size: 12px;\n    }\n\n    .border-[_ngcontent-%COMP%] {\n      border-bottom: 1px solid #e8e9ee;\n      height: 55px;\n      margin-bottom: 10px;\n    }\n\n    .overflow[_ngcontent-%COMP%] {\n      text-overflow: ellipsis;\n      overflow: hidden;\n      white-space: nowrap;\n    }"]}),e})();function Tr(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",5),d["\u0275\u0275elementStart"](1,"div",6),d["\u0275\u0275text"](2,"Loading..."),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function kr(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",7),d["\u0275\u0275text"](1,"Something went wrong :("),d["\u0275\u0275elementEnd"]())}function Lr(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",8),d["\u0275\u0275elementStart"](1,"p"),d["\u0275\u0275text"](2),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](3,"div",9),d["\u0275\u0275elementStart"](4,"mat-icon",10),d["\u0275\u0275text"](5,"info"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate"](e.widgetConfig.widget_name),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("matTooltip",e.widgetConfig.widget_info)}}function Yr(e,t){if(1&e&&(d["\u0275\u0275namespaceSVG"](),d["\u0275\u0275elementStart"](0,"svg"),d["\u0275\u0275elementStart"](1,"g"),d["\u0275\u0275element"](2,"rect",16),d["\u0275\u0275elementStart"](3,"text",17),d["\u0275\u0275text"](4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](3),d["\u0275\u0275attribute"]("fill",n.getColor(e)),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",n.activity_total[e.id].value," ")}}function Ar(e,t){if(1&e&&(d["\u0275\u0275namespaceSVG"](),d["\u0275\u0275elementStart"](0,"svg"),d["\u0275\u0275elementStart"](1,"text",18),d["\u0275\u0275text"](2),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngStyle",e.centerTextStyle),d["\u0275\u0275attribute"]("fill",e.centerTextStyle?e.centerTextStyle.color:"#45546E"),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",e.total," ")}}const Rr=function(){return{size:"14px",color:"#45546E"}};function Nr(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementStart"](1,"dx-pie-chart",11),d["\u0275\u0275element"](2,"dxo-size",12),d["\u0275\u0275element"](3,"dxi-series",13),d["\u0275\u0275elementStart"](4,"dxo-legend",14),d["\u0275\u0275template"](5,Yr,5,2,"svg",15),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](6,Ar,3,3,"svg",15),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("dataSource",e.activity_total)("animation",!0)("innerRadius",.7)("palette",e.palette),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("height",71),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("visible",!0)("margin",10)("visible",!0)("font",d["\u0275\u0275pureFunction0"](11,Rr)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("dxTemplateOf","markerTemplate"),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("dxTemplateOf","centerTemplate")}}let Vr=(()=>{class e{constructor(e){this.salesDashboardService=e,this.palette=["#A0D911","#FA541C"],this.isLoading=!0,this.isError=!1}ngOnInit(){this.widgetConfig=this.salesDashboardService.getConfigFor(this.widget_id),this.sales_activity_type=this.widgetConfig.component_config.sales_activity_type,this.salesDashboardService.getTotalActivities(this.sales_activity_type).subscribe(e=>{this.activity_total=e.response,this.activity_type_name=e.activity_type_name,this.isLoading=!1,this.isError=!1},e=>{this.isError=!0,this.isLoading=!1})}get total(){let e=0;for(let t=0;t<this.activity_total.length;t++)e+=this.activity_total[t].value;return e}getColor(e){return e.visible?e.marker.fill:"#888"}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](v))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-total-activities"]],inputs:{widget_id:"widget_id",palette:"palette",centerTextStyle:"centerTextStyle"},decls:5,vars:4,consts:[[1,"total-activities-card"],["class","loading-wrapper",4,"ngIf"],["class","error",4,"ngIf"],["class","title",4,"ngIf"],[4,"ngIf"],[1,"loading-wrapper"],[1,"loading"],[1,"error"],[1,"title"],[1,"iconbtn",2,"margin-left","5px"],[2,"font-size","larger",3,"matTooltip"],["resolveLabelOverlapping","shift","type","doughnut","centerTemplate","centerTemplate",1,"pie",3,"dataSource","animation","innerRadius","palette"],[3,"height"],["argumentField","name","valueField","value"],["horizontalAlignment","right","verticalAlignment","center","markerTemplate","markerTemplate",3,"visible","margin","font"],[4,"dxTemplate","dxTemplateOf"],["x","0","y","0","width","18","height","18","fill","red","opacity","0"],["x","0","y","15",2,"font-family","Verdana","font-size","14px"],["text-anchor","middle","x","50%","y","50%",2,"font-size","20px","color","#6e7b8f","font-weight","700",3,"ngStyle"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"mat-card",0),d["\u0275\u0275template"](1,Tr,3,0,"div",1),d["\u0275\u0275template"](2,kr,2,0,"div",2),d["\u0275\u0275template"](3,Lr,6,2,"div",3),d["\u0275\u0275template"](4,Nr,7,12,"div",4),d["\u0275\u0275elementEnd"]()),2&e&&(d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.isLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.isError),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!t.isLoading&&!t.isError),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!t.isLoading&&!t.isError))},directives:[ve.a,i.NgIf,C.a,y.a,_t.a,U.Xd,U.E,U.Qc,Q.d,i.NgStyle],styles:[".total-activities-card[_ngcontent-%COMP%]{min-height:150px;margin:7px;height:100%;padding:14px!important;box-shadow:0 6px 18px rgba(0,0,0,.1)}.total-activities-card[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-weight:500;font-size:14px;display:flex;line-height:16px;letter-spacing:.02em;text-transform:capitalize;padding:0!important;color:#45546e}.error[_ngcontent-%COMP%], .pie[_ngcontent-%COMP%]{width:100%}.error[_ngcontent-%COMP%]{display:flex;height:100%;font-size:small;color:#cf2020;vertical-align:center;justify-content:center;align-items:center}.iconbtn[_ngcontent-%COMP%]{color:#7b7b7b!important;padding:0;height:-moz-fit-content;height:fit-content}"]}),e})(),zr=(()=>{class e{transform(e){return l.utc(e).format("DD MMM YYYY")}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=d["\u0275\u0275definePipe"]({name:"salesDashboardDatePipe",type:e,pure:!0}),e})();function jr(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"mat-option",8),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]().$implicit;d["\u0275\u0275property"]("value",e.value),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",e.name," ")}}function Br(e,t){if(1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275template"](1,jr,2,2,"mat-option",21),d["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",""!==e.name&&null!=e.name)}}function $r(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"mat-option",8),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275property"]("value",e.value),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",e.label," ")}}function Ur(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"mat-option",8),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275property"]("value",e.value),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",e.label," ")}}function Qr(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"mat-icon",22),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().downloadUpcomingContracts()})),d["\u0275\u0275text"](1,"download"),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275classProp"]("disabled",e.downloading)}}function qr(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"mat-icon",23),d["\u0275\u0275text"](1,"download"),d["\u0275\u0275elementEnd"]())}function Wr(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275elementStart"](1,"div",26),d["\u0275\u0275elementStart"](2,"div",27),d["\u0275\u0275text"](3),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](4,"div",28),d["\u0275\u0275listener"]("click",(function(){d["\u0275\u0275restoreView"](e);const n=t.$implicit;return d["\u0275\u0275nextContext"](2).sortThisThing(n)})),d["\u0275\u0275elementStart"](5,"mat-icon"),d["\u0275\u0275text"](6,"arrow_upward "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](7,"mat-icon"),d["\u0275\u0275text"](8,"arrow_downward "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=t.$implicit,n=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngClass","col-"+e.col),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("matTooltip",e.title_label),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",e.title_label," "),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("matTooltip",n.getSortTooltip(e.sortOrder))}}const Gr=function(e,t,n,i){return{"renewed-contract":e,"in-progress-contract":t,"terminated-contract":n,"open-contract":i}};function Hr(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementStart"](1,"div",29),d["\u0275\u0275elementStart"](2,"div",30),d["\u0275\u0275elementStart"](3,"div",31),d["\u0275\u0275text"](4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](5,"div",32),d["\u0275\u0275elementStart"](6,"div",31),d["\u0275\u0275text"](7),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](8,"div",33),d["\u0275\u0275text"](9),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](10,"div",33),d["\u0275\u0275element"](11,"app-currency",34),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](12,"div",33),d["\u0275\u0275text"](13),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](14,"div",35),d["\u0275\u0275elementStart"](15,"div",31),d["\u0275\u0275text"](16),d["\u0275\u0275pipe"](17,"salesDashboardDatePipe"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](18,"div",36),d["\u0275\u0275elementStart"](19,"div",37),d["\u0275\u0275text"](20),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](21,"hr"),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275advance"](4),d["\u0275\u0275textInterpolate1"](" ",e.p_and_l||" - "," "),d["\u0275\u0275advance"](3),d["\u0275\u0275textInterpolate1"](" ",e.customer_name||" - "," "),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"](" ",e.item_name||"-"," "),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("currencyList",e.item_value)("showActualAmount",!1),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate"](e.cost_center||"-"),d["\u0275\u0275advance"](3),d["\u0275\u0275textInterpolate1"](" ",d["\u0275\u0275pipeBind1"](17,10,e.closure_date)," "),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("matTooltip",e.contract_name||"Open")("ngClass",d["\u0275\u0275pureFunction4"](12,Gr,"Renewed"===e.contract_name,"Renewal in progress"===e.contract_name,"Terminated"===e.contract_name,!e.contract_name||""===e.contract_name)),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",e.contract_name||"Open"," ")}}function Jr(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementStart"](1,"div",24),d["\u0275\u0275template"](2,Wr,9,4,"ng-container",9),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](3,"hr"),d["\u0275\u0275elementStart"](4,"div",25),d["\u0275\u0275template"](5,Hr,22,17,"div",9),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngForOf",e.headerForContactClosure),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngForOf",e.upComingContactData)}}function Kr(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",38),d["\u0275\u0275elementStart"](1,"div",39),d["\u0275\u0275text"](2,"Loading..."),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function Xr(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",40),d["\u0275\u0275element"](1,"img",41),d["\u0275\u0275elementStart"](2,"div"),d["\u0275\u0275text"](3,"No Contracts Found"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}let Zr=(()=>{class e{constructor(e,t){this.salesDashBoardService=e,this._toaster=t,this.isLoading=!0,this.month=1,this.regionData=[],this.filterD={status:null,PandL:null},this.monthOptions=[{label:"Current Month",value:1},{label:"Next 3 Months",value:4},{label:"Next 6 Months",value:7},{label:"Next 12 Months",value:13}],this.downloading=!1,this.statusData=[{label:"None",value:"None"},{label:"Renewed",value:"Renewed"},{label:"Open",value:"Open"},{label:"Renewal In Progress",value:"In Progress"},{label:"Terminated",value:"Terminated"}]}ngOnInit(){var e;this.widgetConfig=this.salesDashBoardService.getConfigFor(34),this.headerForContactClosure=null===(e=this.widgetConfig)||void 0===e?void 0:e.headerNames.filter(e=>e.is_active),this.getUpcomingContractClosureData()}ngOnDestroy(){this.dataSubscription&&this.dataSubscription.unsubscribe()}handleMonthChange(e){this.month=e,this.getUpcomingContractClosureData().then(()=>{this.handlePLAndStatusChange(this.filterD.status,"ALL",this.filterD.PandL)})}handlePLAndStatusChange(e,t,n){let i=[];"ALL"==t?(this.filterD.PandL=n,this.filterD.status=e):"PandL"==t?this.filterD.PandL=n:"status"==t&&(this.filterD.status=e),i=null!=this.filterD.PandL?this.originalUpcomingContactData.filter(e=>e.p_and_l===this.filterD.PandL):this.originalUpcomingContactData,"Renewed"===this.filterD.status?i=i.filter(e=>"Renewed"===e.contract_name):"Open"===this.filterD.status?i=i.filter(e=>""===e.contract_name):"In Progress"===this.filterD.status?i=i.filter(e=>"Renewal in progress"===e.contract_name):"Terminated"===this.filterD.status&&(i=i.filter(e=>"Terminated"===e.contract_name)),this.filteredActivityDataForDownload=i,i.length>0?(this.upComingContactData=i,this.noDataFlag=!1):this.noDataFlag=!0}getUpcomingContractClosureData(){return new Promise((e,t)=>{this.isLoading=!0,this.dataSubscription=this.salesDashBoardService.getUpcomingContractClosureData(this.month).subscribe(t=>{this.upComingContactData=t.data,this.originalUpcomingContactData=[...this.upComingContactData],this.regionData=this.upComingContactData.map(e=>e.p_and_l).filter((e,t,n)=>n.indexOf(e)===t).map(e=>({name:e,value:e}));let n=!1,i=!1;for(let e of this.regionData)if(e.value===this.filterD.PandL){i=!0;break}i||(this.filterD.PandL=null);for(let e of this.statusData)if(e.value===this.filterD.status){n=!0;break}n||(this.filterD.status=null),this.isLoading=!1,e()},e=>{this._toaster.showError("Cannot fetch Activity widget data","",1e3),this.isLoading=!1,console.error("Error fetching activity data:",e),t(e)})})}sortThisThing(e){e.sortOrder=e.sortOrder?"A"===e.sortOrder?"D":"D"===e.sortOrder?"N":"A":"A",this.upComingContactData=this.upComingContactData.sort("A"===e.sortOrder?(t,n)=>{const i=t[e.key_name]?"string"==typeof t[e.key_name]?t[e.key_name].toLowerCase():t[e.key_name]:"",a=n[e.key_name]?"string"==typeof n[e.key_name]?n[e.key_name].toLowerCase():n[e.key_name]:"";return i<a?-1:i>a?1:0}:"D"===e.sortOrder?(t,n)=>{const i=t[e.key_name]?"string"==typeof t[e.key_name]?t[e.key_name].toLowerCase():t[e.key_name]:"",a=n[e.key_name]?"string"==typeof n[e.key_name]?n[e.key_name].toLowerCase():n[e.key_name]:"";return i>a?-1:i<a?1:0}:(e,t)=>new Date(e.closure_date).getTime()-new Date(t.closure_date).getTime())}applyStylesToCell(e,t){if(t.border){const t={style:"thin",color:{argb:"BFBFBF"}};e.border={bottom:t,left:t,right:t,top:t}}t.bgColor&&(e.fill={type:"pattern",pattern:"solid",fgColor:{argb:t.bgColor}}),t.font&&(e.font=t.font)}isRenewed(e){return 1==e.contract_renewed}getSortTooltip(e){switch(e){case"A":return"Sort Descending";case"D":return"Remove Sort";case"N":return"Sort Ascending";default:return"Sort"}}downloadUpcomingContracts(){var e;this.downloading=!0;const t=(null===(e=this.filteredActivityDataForDownload)||void 0===e?void 0:e.length)>0?this.filteredActivityDataForDownload:this.upComingContactData;if(!t||0===t.length||this.noDataFlag)return this._toaster.showWarning("No data available for download","Dismiss",1e3),void(this.downloading=!1);const n=new Pa.Workbook,i=n.addWorksheet("Upcoming Contracts");i.columns=[{width:30},{width:40},{width:40},{width:30},{width:20},{width:20}];const a=i.getRow(2);a.height=30,i.mergeCells(2,1,2,6);const o=a.getCell(1);o.value="Upcoming Contract Closure Data",o.font={name:"Segoe UI Light",size:22,bold:!0},o.alignment={horizontal:"left",vertical:"middle",wrapText:!0},this.applyStylesToCell(o,{border:!0,bgColor:"C0C0C0"}),this.applyStylesToCell(o,{border:!0,bgColor:"C0C0C0",font:{bold:!0,color:{argb:"000000"},size:12}}),i.addRow(["P & L","Customer Name","Item Name","Item Value(USD)","Cost Center","Closure Date","Status"]).eachCell({includeEmpty:!0},e=>{this.applyStylesToCell(e,{font:{bold:!0}})}),t.forEach(e=>{let t=0;if(e.item_value&&e.item_value.length){const n=e.item_value.find(e=>"USD"===e.currency_code);n&&(t=n.value)}const n=[e.p_and_l||"",e.customer_name||"",e.item_name||"",t,e.cost_center||"",this.formatDateForDownload(e.closure_date)||"",e.contract_name||"Open"];i.addRow(n)}),n.xlsx.writeBuffer().then(e=>{const t=new Blob([e],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"});Ma(t,"upcoming_contracts_closure.xlsx"),this.downloading=!1}).catch(e=>{console.error("Error writing Excel buffer:",e),this._toaster.showError("Couldn't get the excel report!","Dismiss",1e3),this.downloading=!1})}formatDateForDownload(e){return e?s.a.utc(e).format("DD MMM YYYY"):""}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](v),d["\u0275\u0275directiveInject"](_e.a))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-upcoming-contact-closure-sales-dashboard"]],decls:34,vars:12,consts:[[1,"upcoming-contact-card"],[1,"row","p-0","justify-content-between"],[1,"title"],[1,"iconbtn",2,"margin-left","5px"],[2,"font-size","larger",3,"matTooltip"],[1,"functionalities-wrapper"],["appearance","outline",1,"drop-down-filter-pl"],["placeholder","Select P&L",3,"selectionChange"],[3,"value"],[4,"ngFor","ngForOf"],["appearance","outline",1,"drop-down-filter"],[3,"value","valueChange","selectionChange"],[3,"value",4,"ngFor","ngForOf"],["appearance","outline",1,"drop-down-month-filter"],["placeholder","Select Status",3,"selectionChange"],[1,"d-flex","align-item-center","icon-wrapper"],["matTooltip","Download",3,"disabled","click",4,"ngIf"],["matTooltip","Downloading...",4,"ngIf"],[4,"ngIf"],["class","loader-wrap",4,"ngIf"],["class","no-data",4,"ngIf"],[3,"value",4,"ngIf"],["matTooltip","Download",3,"click"],["matTooltip","Downloading..."],[1,"header-wrapper","row"],[1,"response-wrapper"],[1,"header-title",3,"ngClass"],[1,"tilte-name",3,"matTooltip"],[1,"sorting-thing",3,"matTooltip","click"],[1,"response-row","row"],[1,"col-2","response-desc","d-flex","align-items-center"],[1,"body-desc"],[1,"col-2","response-desc","body-desc","d-flex","align-items-center"],[1,"col-2","response-desc"],["type","small",3,"currencyList","showActualAmount"],[1,"col-1","response-desc","d-flex","align-items-center"],[1,"col-1","response-desc","body-desc","d-flex","align-items-center"],[1,"body-desc",3,"matTooltip","ngClass"],[1,"loader-wrap"],[1,"loading"],[1,"no-data"],["src","https://assets.kebs.app/images/timeline.png","alt",""]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"mat-card",0),d["\u0275\u0275elementStart"](1,"div",1),d["\u0275\u0275elementStart"](2,"div",2),d["\u0275\u0275elementStart"](3,"div"),d["\u0275\u0275text"](4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](5,"div",3),d["\u0275\u0275elementStart"](6,"mat-icon",4),d["\u0275\u0275text"](7,"info"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](8,"div",5),d["\u0275\u0275elementStart"](9,"mat-form-field",6),d["\u0275\u0275elementStart"](10,"mat-label"),d["\u0275\u0275text"](11,"P & L"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](12,"mat-select",7),d["\u0275\u0275listener"]("selectionChange",(function(e){return t.handlePLAndStatusChange(null,"PandL",e.value)})),d["\u0275\u0275elementStart"](13,"mat-option",8),d["\u0275\u0275text"](14,"None"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](15,Br,2,1,"ng-container",9),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](16,"mat-form-field",10),d["\u0275\u0275elementStart"](17,"mat-label"),d["\u0275\u0275text"](18,"Duration"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](19,"mat-select",11),d["\u0275\u0275listener"]("valueChange",(function(e){return t.month=e}))("selectionChange",(function(e){return t.handleMonthChange(e.value)})),d["\u0275\u0275template"](20,$r,2,2,"mat-option",12),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](21,"mat-form-field",13),d["\u0275\u0275elementStart"](22,"mat-label"),d["\u0275\u0275text"](23,"Status"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](24,"mat-select",14),d["\u0275\u0275listener"]("selectionChange",(function(e){return t.handlePLAndStatusChange(e.value,"status",null)})),d["\u0275\u0275template"](25,Ur,2,2,"mat-option",12),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](26,"div",15),d["\u0275\u0275template"](27,Qr,2,2,"mat-icon",16),d["\u0275\u0275template"](28,qr,2,0,"mat-icon",17),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](29,"hr"),d["\u0275\u0275elementStart"](30,"div"),d["\u0275\u0275template"](31,Jr,6,2,"div",18),d["\u0275\u0275template"](32,Kr,3,0,"div",19),d["\u0275\u0275template"](33,Xr,4,0,"div",20),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e&&(d["\u0275\u0275advance"](4),d["\u0275\u0275textInterpolate"](t.widgetConfig.widget_name),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("matTooltip",t.widgetConfig.widget_info),d["\u0275\u0275advance"](7),d["\u0275\u0275property"]("value",null),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngForOf",t.regionData),d["\u0275\u0275advance"](4),d["\u0275\u0275property"]("value",t.month),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngForOf",t.monthOptions),d["\u0275\u0275advance"](5),d["\u0275\u0275property"]("ngForOf",t.statusData),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",!t.downloading),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.downloading),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngIf",!t.isLoading&&t.upComingContactData&&!t.noDataFlag),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.isLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!t.isLoading&&!t.upComingContactData||!t.isLoading&&t.noDataFlag))},directives:[ve.a,C.a,y.a,Oe.c,Oe.g,Ee.c,De.p,i.NgForOf,i.NgIf,i.NgClass,Ce.a],pipes:[zr],styles:['.upcoming-contact-card[_ngcontent-%COMP%]{margin:7px;height:95%;min-height:300px;padding:14px!important;color:#45546e;box-shadow:0 6px 18px rgba(0,0,0,.1)}.upcoming-contact-card[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-weight:500;font-size:14px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;padding:0!important;color:#45546e}.upcoming-contact-card[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%], .upcoming-contact-card[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{display:flex;align-items:center}.upcoming-contact-card[_ngcontent-%COMP%]   .functionalities-wrapper[_ngcontent-%COMP%]{display:flex;justify-content:space-evenly;align-items:center;width:28rem}.upcoming-contact-card[_ngcontent-%COMP%]   .functionalities-wrapper[_ngcontent-%COMP%]   .icon-wrapper[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{cursor:pointer}.upcoming-contact-card[_ngcontent-%COMP%]   .functionalities-wrapper[_ngcontent-%COMP%]   .drop-down-filter[_ngcontent-%COMP%]{width:9rem;padding:0 1%;height:1.5rem;color:var(--Blue-Grey-100,#45546e);font-family:Roboto;font-size:12px;font-style:normal;font-weight:400;border-radius:4px;cursor:pointer}.upcoming-contact-card[_ngcontent-%COMP%]   .functionalities-wrapper[_ngcontent-%COMP%]   .drop-down-filter[_ngcontent-%COMP%]     .mat-form-field-flex{height:2rem;align-items:center}.upcoming-contact-card[_ngcontent-%COMP%]   .functionalities-wrapper[_ngcontent-%COMP%]   .drop-down-month-filter[_ngcontent-%COMP%]{width:6rem;padding:0 1%;height:1.5rem;color:var(--Blue-Grey-100,#45546e);font-family:Roboto;font-size:12px;font-style:normal;font-weight:400;border-radius:4px;cursor:pointer}.upcoming-contact-card[_ngcontent-%COMP%]   .functionalities-wrapper[_ngcontent-%COMP%]   .drop-down-month-filter[_ngcontent-%COMP%]     .mat-form-field-flex{height:2rem;align-items:center}.upcoming-contact-card[_ngcontent-%COMP%]   .functionalities-wrapper[_ngcontent-%COMP%]   .drop-down-filter-pl[_ngcontent-%COMP%]{width:7rem;padding:0 1%;height:1.5rem;color:var(--Blue-Grey-100,#45546e);font-family:Roboto;font-size:12px;font-style:normal;font-weight:400;border-radius:4px;cursor:pointer}.upcoming-contact-card[_ngcontent-%COMP%]   .functionalities-wrapper[_ngcontent-%COMP%]   .drop-down-filter-pl[_ngcontent-%COMP%]     .mat-form-field-flex{height:2rem;align-items:center}.upcoming-contact-card[_ngcontent-%COMP%]   .functionalities-wrapper[_ngcontent-%COMP%]   .drop-down-filter-pl[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]{height:2rem}.upcoming-contact-card[_ngcontent-%COMP%]   .header-wrapper[_ngcontent-%COMP%]{display:flex;align-items:center;padding-top:1%}.upcoming-contact-card[_ngcontent-%COMP%]   .header-wrapper[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]{display:flex;justify-content:flex-start;color:var(--Blue-Grey-50,#b9c0ca);font-family:Plus Jakarta Sans;font-size:12px;font-style:normal;font-weight:400;line-height:16px;letter-spacing:.22px;text-transform:uppercase}.upcoming-contact-card[_ngcontent-%COMP%]   .header-wrapper[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]   .tilte-name[_ngcontent-%COMP%]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.upcoming-contact-card[_ngcontent-%COMP%]   .header-wrapper[_ngcontent-%COMP%]   .sorting-thing[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}.upcoming-contact-card[_ngcontent-%COMP%]   .header-wrapper[_ngcontent-%COMP%]   .sorting-thing[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:12px;color:#b9c0ca;font-weight:600;width:10px;margin-top:3%;cursor:pointer}.upcoming-contact-card[_ngcontent-%COMP%]   .response-wrapper[_ngcontent-%COMP%]{height:15rem;overflow-y:scroll}.upcoming-contact-card[_ngcontent-%COMP%]   .response-wrapper[_ngcontent-%COMP%]   .response-row[_ngcontent-%COMP%]{color:var(--Blue-Grey-100,#45546e);font-family:Roboto;font-size:12px;font-style:normal;font-weight:500;line-height:16px;letter-spacing:.24px;text-transform:capitalize}.upcoming-contact-card[_ngcontent-%COMP%]   .response-wrapper[_ngcontent-%COMP%]   .response-row[_ngcontent-%COMP%]   .response-desc[_ngcontent-%COMP%]{display:flex;justify-content:flex-start;align-items:center;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.upcoming-contact-card[_ngcontent-%COMP%]   .response-wrapper[_ngcontent-%COMP%]   .response-row[_ngcontent-%COMP%]   .response-desc[_ngcontent-%COMP%]   .renewed-contract[_ngcontent-%COMP%]{color:#52c41a;font-weight:600;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.upcoming-contact-card[_ngcontent-%COMP%]   .response-wrapper[_ngcontent-%COMP%]   .response-row[_ngcontent-%COMP%]   .response-desc[_ngcontent-%COMP%]   .in-progress-contract[_ngcontent-%COMP%]{color:#ffa502;font-weight:600;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.upcoming-contact-card[_ngcontent-%COMP%]   .response-wrapper[_ngcontent-%COMP%]   .response-row[_ngcontent-%COMP%]   .response-desc[_ngcontent-%COMP%]   .open-contract[_ngcontent-%COMP%]{color:#45546e;font-weight:600;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.upcoming-contact-card[_ngcontent-%COMP%]   .response-wrapper[_ngcontent-%COMP%]   .response-row[_ngcontent-%COMP%]   .response-desc[_ngcontent-%COMP%]   .terminated-contract[_ngcontent-%COMP%]{color:#f66;font-weight:600;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.upcoming-contact-card[_ngcontent-%COMP%]   .response-wrapper[_ngcontent-%COMP%]   .status-dot[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;border:none;height:.9rem;width:.9rem;border-radius:50%;color:#d9d9d9}.upcoming-contact-card[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;flex-direction:column;gap:5%;height:19.5rem;color:var(--Blue-Grey-100,#45546e);font-family:Roboto;font-size:13px;font-style:normal;font-weight:500;line-height:16px;letter-spacing:.24px;text-transform:capitalize}.upcoming-contact-card[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:180px}.upcoming-contact-card[_ngcontent-%COMP%]   .loader-wrap[_ngcontent-%COMP%]{height:19.5rem;display:flex;justify-content:center;align-items:center}.upcoming-contact-card[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]{color:rgba(0,0,0,.3);font-size:16px}.upcoming-contact-card[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]:before{content:"Loading...";position:absolute;overflow:hidden;max-width:7em;white-space:nowrap;background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%);background-clip:text;-webkit-background-clip:text;-webkit-text-fill-color:transparent;animation:loading 6s linear infinite}@keyframes loading{0%{max-width:0}}']}),e})();var el=n("TmG/");const tl=["filterConfigDialog"];function nl(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",7),d["\u0275\u0275elementStart"](1,"div",8),d["\u0275\u0275text"](2,"Loading..."),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}const il=function(e,t,n){return{id:e,name:t,children:n}};function al(e,t){if(1&e&&d["\u0275\u0275element"](0,"app-tree-select-search",34),2&e){const e=d["\u0275\u0275nextContext"](2).$implicit;d["\u0275\u0275property"]("config",d["\u0275\u0275pureFunction3"](4,il,e.master_data_key,e.master_data_name_key,e.master_data_child_key))("formControlName",e.filter_id)("checklistData",e.master_data)("placeholder",e.filter_name)}}const ol=function(){return{applyLabel:"Apply",displayFormat:"DD MMM YYYY",customRangeLabel:"Custom Range",format:"MM-DD-YYYY",clearLabel:"clear"}};function rl(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"input",35),d["\u0275\u0275listener"]("startDateChanged",(function(t){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](4).startDateClicked(t)})),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"](2).$implicit,t=d["\u0275\u0275nextContext"](2);d["\u0275\u0275property"]("formControlName",e.filter_id)("matTooltip",e.filter_name)("showCustomRangeLabel",!0)("locale",d["\u0275\u0275pureFunction0"](9,ol))("alwaysShowCalendars",!0)("ranges",t.dateRangePickerRanges)("linkedCalendars",!0)("placeholder",e.filter_name)("maxDate",t.maxDate)}}function ll(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",30),d["\u0275\u0275elementStart"](1,"span",31),d["\u0275\u0275text"](2),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](3,al,1,8,"app-tree-select-search",32),d["\u0275\u0275template"](4,rl,1,10,"input",33),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]().$implicit,t=d["\u0275\u0275nextContext"](2);d["\u0275\u0275classProp"]("filterColHide",!t.showMoreFilters)("filterCol",t.showMoreFilters),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate"](e.filter_name),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","checklist"==e.filter_type),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","date"==e.filter_type)}}function sl(e,t){if(1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275template"](1,ll,5,7,"div",29),d["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.is_active)}}function cl(e,t){1&e&&d["\u0275\u0275element"](0,"app-opportunity-status-chart",36),2&e&&d["\u0275\u0275property"]("widget_id",1)}function dl(e,t){1&e&&d["\u0275\u0275element"](0,"app-pipeline-status-widget",36),2&e&&d["\u0275\u0275property"]("widget_id",36)}function pl(e,t){1&e&&d["\u0275\u0275element"](0,"app-pipleine-growth-widget",37),2&e&&d["\u0275\u0275property"]("widget_id",35)}function ml(e,t){1&e&&d["\u0275\u0275element"](0,"app-product-category-widget",36),2&e&&d["\u0275\u0275property"]("widget_id",2)}function gl(e,t){1&e&&d["\u0275\u0275element"](0,"app-obv-trend-widget",37),2&e&&d["\u0275\u0275property"]("widget_id",15)}function ul(e,t){1&e&&d["\u0275\u0275element"](0,"app-rev-cost-gm-trend",37),2&e&&d["\u0275\u0275property"]("widget_id",21)}function fl(e,t){1&e&&d["\u0275\u0275element"](0,"app-financial-pie-chart",36),2&e&&d["\u0275\u0275property"]("widget_id",22)}function hl(e,t){1&e&&d["\u0275\u0275element"](0,"app-financial-pie-chart",36),2&e&&d["\u0275\u0275property"]("widget_id",23)}function _l(e,t){1&e&&d["\u0275\u0275element"](0,"app-financial-pie-chart",36),2&e&&d["\u0275\u0275property"]("widget_id",24)}function vl(e,t){1&e&&d["\u0275\u0275element"](0,"app-financial-pie-chart",36),2&e&&d["\u0275\u0275property"]("widget_id",25)}function yl(e,t){1&e&&d["\u0275\u0275element"](0,"app-financial-pie-chart",36),2&e&&d["\u0275\u0275property"]("widget_id",26)}function Cl(e,t){1&e&&d["\u0275\u0275element"](0,"app-financial-pie-chart",36),2&e&&d["\u0275\u0275property"]("widget_id",27)}function xl(e,t){1&e&&d["\u0275\u0275element"](0,"app-financial-pie-chart",36),2&e&&d["\u0275\u0275property"]("widget_id",28)}function bl(e,t){1&e&&d["\u0275\u0275element"](0,"app-financial-pie-chart",36),2&e&&d["\u0275\u0275property"]("widget_id",29)}function wl(e,t){1&e&&d["\u0275\u0275element"](0,"app-financial-pie-chart",36),2&e&&d["\u0275\u0275property"]("widget_id",30)}function Sl(e,t){1&e&&d["\u0275\u0275element"](0,"app-financial-pie-chart",36),2&e&&d["\u0275\u0275property"]("widget_id",31)}function Ol(e,t){1&e&&d["\u0275\u0275element"](0,"app-financial-pie-chart",36),2&e&&d["\u0275\u0275property"]("widget_id",32)}function El(e,t){1&e&&d["\u0275\u0275element"](0,"app-financial-pie-chart",36),2&e&&d["\u0275\u0275property"]("widget_id",33)}function Dl(e,t){1&e&&d["\u0275\u0275element"](0,"app-fte-trend-widget",37),2&e&&d["\u0275\u0275property"]("widget_id",19)}function Ml(e,t){1&e&&d["\u0275\u0275element"](0,"app-pipeline-card",38),2&e&&d["\u0275\u0275property"]("widget_id",3)}function Pl(e,t){1&e&&d["\u0275\u0275element"](0,"app-pipeline-card",36),2&e&&d["\u0275\u0275property"]("widget_id",4)}function Il(e,t){1&e&&d["\u0275\u0275element"](0,"app-recent-sales-activities",36),2&e&&d["\u0275\u0275property"]("widget_id",5)}function Fl(e,t){1&e&&d["\u0275\u0275element"](0,"app-recent-sales-activities",36),2&e&&d["\u0275\u0275property"]("widget_id",6)}function Tl(e,t){1&e&&d["\u0275\u0275element"](0,"app-recent-sales-activities",36),2&e&&d["\u0275\u0275property"]("widget_id",7)}function kl(e,t){1&e&&d["\u0275\u0275element"](0,"app-my-activities-widget",36),2&e&&d["\u0275\u0275property"]("widget_id",8)}function Ll(e,t){1&e&&d["\u0275\u0275element"](0,"app-my-team-calls-widget",36),2&e&&d["\u0275\u0275property"]("widget_id",9)}function Yl(e,t){1&e&&d["\u0275\u0275element"](0,"app-my-team-mails-widget",36),2&e&&d["\u0275\u0275property"]("widget_id",10)}function Al(e,t){1&e&&d["\u0275\u0275element"](0,"app-exception-widget",36),2&e&&d["\u0275\u0275property"]("widget_id",16)}function Rl(e,t){1&e&&d["\u0275\u0275element"](0,"app-quote-discrepencies-widget",36),2&e&&d["\u0275\u0275property"]("widget_id",20)}const Nl=function(){return["#A0D911","#FA541C"]};function Vl(e,t){1&e&&d["\u0275\u0275element"](0,"app-total-activities",39),2&e&&d["\u0275\u0275property"]("widget_id",11)("palette",d["\u0275\u0275pureFunction0"](2,Nl))}const zl=function(){return["#4D8AF0","#FACB62","#3DBF1C","#A0D911","#FA541C"]};function jl(e,t){1&e&&d["\u0275\u0275element"](0,"app-total-activities",40),2&e&&d["\u0275\u0275property"]("widget_id",12)("palette",d["\u0275\u0275pureFunction0"](2,zl))}const Bl=function(){return["#EB2F96","#722ED1"]};function $l(e,t){1&e&&d["\u0275\u0275element"](0,"app-total-activities",41),2&e&&d["\u0275\u0275property"]("widget_id",13)("palette",d["\u0275\u0275pureFunction0"](2,Bl))}const Ul=function(){return{"font-size":"35px",color:"#EB2F96","line-height":"40px"}};function Ql(e,t){1&e&&d["\u0275\u0275element"](0,"app-total-activities",42),2&e&&d["\u0275\u0275property"]("widget_id",14)("centerTextStyle",d["\u0275\u0275pureFunction0"](2,Ul))}function ql(e,t){1&e&&d["\u0275\u0275element"](0,"app-collector-widget",37),2&e&&d["\u0275\u0275property"]("widget_id",17)}function Wl(e,t){1&e&&d["\u0275\u0275element"](0,"app-revenue-widget",37),2&e&&d["\u0275\u0275property"]("widget_id",18)}function Gl(e,t){1&e&&d["\u0275\u0275element"](0,"app-upcoming-contact-closure-sales-dashboard",43),2&e&&d["\u0275\u0275property"]("widget_id",34)}const Hl=function(e){return{height:e}};function Jl(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",9),d["\u0275\u0275elementStart"](1,"div",10),d["\u0275\u0275elementStart"](2,"div",11),d["\u0275\u0275listener"]("contextmenu",(function(t){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().onContextMenu(t)})),d["\u0275\u0275elementStart"](3,"p",12),d["\u0275\u0275text"](4," Filters: "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](5,"form",13),d["\u0275\u0275template"](6,sl,2,1,"ng-container",14),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](7,"div",15),d["\u0275\u0275elementStart"](8,"div"),d["\u0275\u0275elementStart"](9,"button",16),d["\u0275\u0275listener"]("click",(function(){d["\u0275\u0275restoreView"](e);const t=d["\u0275\u0275nextContext"]();return t.showMoreFilters=!t.showMoreFilters})),d["\u0275\u0275text"](10),d["\u0275\u0275elementStart"](11,"mat-icon",17),d["\u0275\u0275text"](12),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](13,"div"),d["\u0275\u0275elementStart"](14,"button",18),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().clearFilters()})),d["\u0275\u0275text"](15," Clear All "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](16,"div",19),d["\u0275\u0275template"](17,cl,1,1,"app-opportunity-status-chart",20),d["\u0275\u0275template"](18,dl,1,1,"app-pipeline-status-widget",20),d["\u0275\u0275template"](19,pl,1,1,"app-pipleine-growth-widget",21),d["\u0275\u0275template"](20,ml,1,1,"app-product-category-widget",20),d["\u0275\u0275template"](21,gl,1,1,"app-obv-trend-widget",21),d["\u0275\u0275template"](22,ul,1,1,"app-rev-cost-gm-trend",21),d["\u0275\u0275template"](23,fl,1,1,"app-financial-pie-chart",20),d["\u0275\u0275template"](24,hl,1,1,"app-financial-pie-chart",20),d["\u0275\u0275template"](25,_l,1,1,"app-financial-pie-chart",20),d["\u0275\u0275template"](26,vl,1,1,"app-financial-pie-chart",20),d["\u0275\u0275template"](27,yl,1,1,"app-financial-pie-chart",20),d["\u0275\u0275template"](28,Cl,1,1,"app-financial-pie-chart",20),d["\u0275\u0275template"](29,xl,1,1,"app-financial-pie-chart",20),d["\u0275\u0275template"](30,bl,1,1,"app-financial-pie-chart",20),d["\u0275\u0275template"](31,wl,1,1,"app-financial-pie-chart",20),d["\u0275\u0275template"](32,Sl,1,1,"app-financial-pie-chart",20),d["\u0275\u0275template"](33,Ol,1,1,"app-financial-pie-chart",20),d["\u0275\u0275template"](34,El,1,1,"app-financial-pie-chart",20),d["\u0275\u0275template"](35,Dl,1,1,"app-fte-trend-widget",21),d["\u0275\u0275template"](36,Ml,1,1,"app-pipeline-card",22),d["\u0275\u0275template"](37,Pl,1,1,"app-pipeline-card",20),d["\u0275\u0275template"](38,Il,1,1,"app-recent-sales-activities",20),d["\u0275\u0275template"](39,Fl,1,1,"app-recent-sales-activities",20),d["\u0275\u0275template"](40,Tl,1,1,"app-recent-sales-activities",20),d["\u0275\u0275template"](41,kl,1,1,"app-my-activities-widget",20),d["\u0275\u0275template"](42,Ll,1,1,"app-my-team-calls-widget",20),d["\u0275\u0275template"](43,Yl,1,1,"app-my-team-mails-widget",20),d["\u0275\u0275template"](44,Al,1,1,"app-exception-widget",20),d["\u0275\u0275template"](45,Rl,1,1,"app-quote-discrepencies-widget",20),d["\u0275\u0275elementStart"](46,"div",23),d["\u0275\u0275template"](47,Vl,1,3,"app-total-activities",24),d["\u0275\u0275template"](48,jl,1,3,"app-total-activities",25),d["\u0275\u0275template"](49,$l,1,3,"app-total-activities",26),d["\u0275\u0275template"](50,Ql,1,3,"app-total-activities",27),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](51,ql,1,1,"app-collector-widget",21),d["\u0275\u0275template"](52,Wl,1,1,"app-revenue-widget",21),d["\u0275\u0275template"](53,Gl,1,1,"app-upcoming-contact-closure-sales-dashboard",28),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](5),d["\u0275\u0275property"]("formGroup",e.filterForm),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngForOf",e.filterConfig),d["\u0275\u0275advance"](3),d["\u0275\u0275classMapInterpolate1"]("p-0 row ",e.showMoreFilters?"ml-2-2":""," c-p"),d["\u0275\u0275styleProp"]("display",e.filterConfig.length>3?"inherit":"none"),d["\u0275\u0275property"]("matTooltip",e.showMoreFilters?"Hide Filters":"Show more Filters"),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",e.showMoreFilters?"Hide":"Show",""),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate"](e.showMoreFilter?"expand_less":"expand_more"),d["\u0275\u0275advance"](4),d["\u0275\u0275property"]("ngStyle",d["\u0275\u0275pureFunction1"](47,Hl,e.showMoreFilters?"85%":"100%")),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isWidgetActive(1)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isWidgetActive(36)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isWidgetActive(35)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isWidgetActive(2)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isWidgetActive(15)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isWidgetActive(21)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isWidgetActive(22)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isWidgetActive(23)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isWidgetActive(24)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isWidgetActive(25)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isWidgetActive(26)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isWidgetActive(27)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isWidgetActive(28)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isWidgetActive(29)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isWidgetActive(30)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isWidgetActive(31)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isWidgetActive(32)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isWidgetActive(33)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isWidgetActive(19)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isWidgetActive(3)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isWidgetActive(4)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isWidgetActive(5)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isWidgetActive(6)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isWidgetActive(7)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isWidgetActive(8)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isWidgetActive(9)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isWidgetActive(10)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isWidgetActive(16)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isWidgetActive(20)),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",e.isWidgetActive(11)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isWidgetActive(12)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isWidgetActive(13)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isWidgetActive(14)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isWidgetActive(17)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isWidgetActive(18)),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isWidgetActive(34))}}function Kl(e,t){if(1&e&&d["\u0275\u0275element"](0,"app-multi-select-search2",54),2&e){const e=d["\u0275\u0275nextContext"]().$implicit;d["\u0275\u0275property"]("placeholder",e.filter_name)("showLabel",!0)("formControlName",e.filter_id+"_default_filter")("list",e.master_data)}}function Xl(e,t){if(1&e&&d["\u0275\u0275element"](0,"app-input-search",55),2&e){const e=d["\u0275\u0275nextContext"]().$implicit;d["\u0275\u0275property"]("placeholder",e.filter_name)("required",e.required)("formControlName",e.filter_id+"_default_filter")("list",e.master_data)}}function Zl(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",50),d["\u0275\u0275template"](1,Kl,1,4,"app-multi-select-search2",51),d["\u0275\u0275template"](2,Xl,1,4,"app-input-search",52),d["\u0275\u0275elementStart"](3,"mat-checkbox",53),d["\u0275\u0275text"](4,"enabled"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","checklist"==e.filter_type),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf","date"==e.filter_type),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("disabled",!!e.required)("formControlName",e.filter_id+"_is_active")}}function es(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",44),d["\u0275\u0275elementStart"](1,"form",45),d["\u0275\u0275listener"]("ngSubmit",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().updateFilterConfig()})),d["\u0275\u0275elementStart"](2,"div"),d["\u0275\u0275text"](3,"Choose default filters"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](4,"div",46),d["\u0275\u0275template"](5,Zl,5,4,"div",47),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](6,"div",48),d["\u0275\u0275elementStart"](7,"button",49),d["\u0275\u0275text"](8," Submit "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("formGroup",e.filterConfigForm),d["\u0275\u0275advance"](4),d["\u0275\u0275property"]("ngForOf",e.filterConfig)}}const ts=[{path:"",component:(()=>{class e{constructor(e,t,n,i,a,o,r,l){this.opportunityService=e,this.leadService=t,this._udrfService=n,this.utilityService=i,this._salesDashboardService=a,this.dialog=o,this.roleService=r,this.snackBar=l,this.primaryColor="#ef4a61",this.application_id=334,this.dataTypeArray=[],this.edittedFilter=!1,this.categorisedDataTypeArray=[],this.minNoOfVisibleSummaryCards=3,this.udrfItemStatusColor=[],this.widgetConfig=[],this.isLoading=!0,this.maxDate=null,this.filterForm=new c.m({}),this.filterConfig=[],this.showMoreFilters=!1,this.dateRangePickerRanges={"This Month":[s()().startOf("month"),s()().endOf("month")],"Last Month":[s()().subtract(1,"month").startOf("month"),s()().subtract(1,"month").endOf("month")],"Next Month":[s()().add(1,"month").startOf("month"),s()().add(1,"month").endOf("month")],"Upcoming 3 Months":[s()().startOf("month"),s()().add(2,"month").endOf("month")],"This Year":[s()().startOf("year"),s()().endOf("year")],"Previous Year":[s()().subtract(1,"year").startOf("year"),s()().subtract(1,"year").endOf("year")],"Current FY":[s()().subtract(s()().month()<3?1:0,"year").month(3).date(1).startOf("day"),s()().add(s()().month()>=3?1:0,"year").month(2).date(31).endOf("day")],"Previous FY":[s()().subtract(s()().month()<3?2:1,"years").month(3).date(1).startOf("day"),s()().subtract(s()().month()<3?1:0,"years").month(2).date(31).endOf("day")]},this.contextMenuPosition={x:"0px",y:"0px"},this.filterConfigForm=new c.m({}),this._filterConfigChanged=new u.b}onResize(){this.calculateDynamicContentHeight()}calculateDynamicContentHeight(){this.dynamicHeight=window.innerHeight-112+"px",document.documentElement.style.setProperty("--dynamicHeight",this.dynamicHeight)}startDateClicked(e){this.maxDate=s()(e.startDate._d).add(3,"year").subtract(1,"day")}ngOnInit(){return Object(o.c)(this,void 0,void 0,(function*(){this.calculateDynamicContentHeight(),this.isLoading=!0,this._salesDashboardService.getMyTeam(),this._salesDashboardService.getDashboardConfig().pipe(Object(Ht.a)(1)).subscribe(e=>{this._salesDashboardService.widgetConfig=e,this.primaryColor=r.where(e,{widget_id:-1})[0].primaryColor||"#ef4a61",document.documentElement.style.setProperty("--primaryColor",this.primaryColor),this.filterConfig=r.where(e,{widget_id:-1})[0].filter_config,this.initFilters(),this.isLoading=!1,this.obvWidget.changes.pipe(Object(Ht.a)(1)).subscribe(e=>{this.obvWidget.forEach(e=>e.getReport(this.formatFilter()))}),this.fteWidget.changes.pipe(Object(Ht.a)(1)).subscribe(e=>{this.fteWidget.forEach(e=>e.getReport(this.formatFilter()))}),this.pipeLineGrowthHozbar.changes.pipe(Object(Ht.a)(1)).subscribe(e=>{this.pipeLineGrowthHozbar.forEach(e=>e.getReport(this.formatFilter()))}),this.pipeLineVerStatusbar.changes.pipe(Object(Ht.a)(1)).subscribe(e=>{this.pipeLineVerStatusbar.forEach(e=>e.getReport(this.formatFilter()))}),this.pipelineWidget.changes.pipe(Object(Ht.a)(1)).subscribe(e=>{this.pipelineWidget.forEach(e=>e.getReport(this.formatFilter()))}),this.exceptionWidget.changes.pipe(Object(Ht.a)(1)).subscribe(e=>{this.exceptionWidget.forEach(e=>{console.log(e),e.getReport(this.formatFilter())})}),this.productCategoryWidget.changes.pipe(Object(Ht.a)(1)).subscribe(e=>{this.productCategoryWidget.forEach(e=>{console.log(e),e.getReport(this.formatFilter())})}),this.collectorWidget.changes.pipe(Object(Ht.a)(1)).subscribe(e=>{this.collectorWidget.forEach(e=>{console.log(e),e.getReport(this.formatFilter())})}),this.revenueWidget.changes.pipe(Object(Ht.a)(1)).subscribe(e=>this.revenueWidget.forEach(e=>e.getReport(this.formatFilter()))),this.statusWidget.changes.pipe(Object(Ht.a)(1)).subscribe(e=>{this.statusWidget.forEach(e=>e.getReport(this.formatFilter()))}),this.quoteDiscrepenciesWidget.changes.pipe(Object(Ht.a)(1)).subscribe(e=>{this.quoteDiscrepenciesWidget.forEach(e=>{console.log(e),e.getReport(this.formatFilter())})}),this.quoteRevCostGmWidget.changes.pipe(Object(Ht.a)(1)).subscribe(e=>{this.quoteRevCostGmWidget.forEach(e=>e.getReport(this.formatFilter()))}),this.financeWidget.changes.pipe(Object(Ht.a)(1)).subscribe(e=>{this.financeWidget.forEach(e=>e.getReport(this.formatFilter()))})})}))}initFilters(){this.filterForm=new c.m({}),this.filterConfigForm=new c.m({});for(let e of this.filterConfig)e.is_active&&this.filterForm.addControl(e.filter_id,new c.j(e.applied_filter)),this.filterConfigForm.addControl(e.filter_id+"_default_filter",new c.j(e.default_filter)),this.filterConfigForm.addControl(e.filter_id+"_is_active",new c.j(!!e.is_active));this._salesDashboardService.currentFilterConfig=this.filterConfig,this.filterForm.valueChanges.pipe(Object(Jt.a)(1e3),Object(Se.a)(this._filterConfigChanged)).subscribe(e=>{for(let t of this.filterConfig)if(e[t.filter_id])if("date"==t.filter_type)if(e[t.filter_id].startDate&&e[t.filter_id].endDate){let n=s()(e[t.filter_id].startDate,"MM-DD-YYYY").startOf("day"),i=s()(e[t.filter_id].endDate,"MM-DD-YYYY").endOf("day");t.applied_filter={startDate:s()(n).format("MM-DD-YYYY"),endDate:s()(i).format("MM-DD-YYYY")}}else t.applied_filter={};else{t.applied_filter=e[t.filter_id]?e[t.filter_id]:[];let n=[];if(Array.isArray(t.applied_filter)&&t.applied_filter.every(e=>Array.isArray(e))){for(let e of t.applied_filter)n.push(...e);t.applied_filter=n}}this._salesDashboardService.currentFilterConfig=this.filterConfig,this.initReport()})}clearFilters(){let e={};for(let t of this.filterConfig)e[t.filter_id]=t.default_filter;this.filterForm.patchValue(e),this.initReport(),this._salesDashboardService.clearFilters()}formatFilter(){return this.filterConfig.map(e=>{var t,n,i=Object(o.h)(e,[]);let a=JSON.parse(JSON.stringify(i));return a.is_active?("date"==a.filter_type&&(console.log(a.applied_filter),a.applied_filter.startDate=(null===(t=a.applied_filter)||void 0===t?void 0:t.startDate)?this.utilityService.getFormattedDate(s()(a.applied_filter.startDate,"MM-DD-YYYY"),s()(s()(a.applied_filter.startDate,"MM-DD-YYYY")).date,0,0,0,0).toDate().toISOString():this.utilityService.getFormattedDate(s()().startOf("year"),s()(s()().startOf("year")).date,0,0,0,0).toDate().toISOString(),a.applied_filter.endDate=(null===(n=a.applied_filter)||void 0===n?void 0:n.endDate)?this.utilityService.getFormattedDate(s()(a.applied_filter.endDate,"MM-DD-YYYY"),s()(s()(a.applied_filter.endDate,"MM-DD-YYYY")).date,15,0,0,0).toDate().toISOString():this.utilityService.getFormattedDate(s()().endOf("year"),s()(s()().endOf("year")).date,15,0,0,0).toDate().toISOString()),a):(a.applied_filter=[],a)})}onContextMenu(e){e.preventDefault(),this._salesDashboardService.checkForAdmin()&&(this.contextMenuPosition.x=e.clientX+"px",this.contextMenuPosition.y=e.clientY+"px",this.contextMenu.openMenu())}openFilterConfig(){this._salesDashboardService.checkForAdmin()&&(this.edittedFilter=!0,this.dialogRef=this.dialog.open(this.filterConfigDialog,{height:"50%",width:"50%"}))}updateFilterConfig(){this._filterConfigChanged.next(),this.edittedFilter=!0,this.filterConfigForm.valid&&this._salesDashboardService.updateFilterConfig(this.filterConfigForm.value,this.filterConfig).pipe(Object(Ht.a)(1)).subscribe(e=>{"E"==e.mesType?(this.snackBar.open("ERROR:Edit failed. Try Again","",{duration:3e3}),this.edittedFilter=!1,this.dialogRef.close()):this._salesDashboardService.getDashboardConfig().pipe(Object(Ht.a)(1)).subscribe(e=>{this.filterConfig=r.where(e,{widget_id:-1})[0].filter_config,this.initFilters(),this.edittedFilter=!1,this.snackBar.open("Successfully edited data!","",{duration:3e3}),this.dialogRef.close()})},e=>{this.snackBar.open("ERROR:Edit failed. Try Again","",{duration:3e3}),this.edittedFilter=!1,this.dialogRef.close()})}initReport(){let e=this.formatFilter();this.obvWidget.forEach(t=>t.getReport(e)),this.fteWidget.forEach(t=>t.getReport(e)),this.pipeLineGrowthHozbar.forEach(t=>t.getReport(e)),this.pipeLineVerStatusbar.forEach(t=>t.getReport(e)),e=this.formatFilter(),this.pipelineWidget.forEach(t=>t.getReport(e)),this.statusWidget.forEach(t=>t.getReport(e)),this.exceptionWidget.forEach(t=>t.getReport(e)),this.productCategoryWidget.forEach(t=>t.getReport(e)),this.collectorWidget.forEach(t=>t.getReport(e)),this.revenueWidget.forEach(t=>t.getReport(e)),this.quoteDiscrepenciesWidget.forEach(t=>t.getReport(e)),this.quoteRevCostGmWidget.forEach(t=>t.getReport(e)),this.financeWidget.forEach(t=>t.getReport(e))}isWidgetActive(e){var t;return(null===(t=this._salesDashboardService.widgetConfig)||void 0===t?void 0:t.findIndex(t=>t.widget_id===e))>=0}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](Kt.a),d["\u0275\u0275directiveInject"](Ga.a),d["\u0275\u0275directiveInject"](Ha.a),d["\u0275\u0275directiveInject"](ue.a),d["\u0275\u0275directiveInject"](v),d["\u0275\u0275directiveInject"](fe.a),d["\u0275\u0275directiveInject"](_.a),d["\u0275\u0275directiveInject"](he.a))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-sales-dashboard"]],viewQuery:function(e,t){if(1&e&&(d["\u0275\u0275viewQuery"](Re.f,!0),d["\u0275\u0275viewQuery"](tl,!0),d["\u0275\u0275viewQuery"](ht,!0),d["\u0275\u0275viewQuery"](kt,!0),d["\u0275\u0275viewQuery"](Gt,!0),d["\u0275\u0275viewQuery"](yn,!0),d["\u0275\u0275viewQuery"](Mn,!0),d["\u0275\u0275viewQuery"](jn,!0),d["\u0275\u0275viewQuery"](Jn,!0),d["\u0275\u0275viewQuery"](si,!0),d["\u0275\u0275viewQuery"](Li,!0),d["\u0275\u0275viewQuery"](aa,!0),d["\u0275\u0275viewQuery"](ha,!0),d["\u0275\u0275viewQuery"](Wa,!0),d["\u0275\u0275viewQuery"](Da,!0)),2&e){let e;d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.contextMenu=e.first),d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.filterConfigDialog=e.first),d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.obvWidget=e),d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.statusWidget=e),d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.pipelineWidget=e),d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.exceptionWidget=e),d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.productCategoryWidget=e),d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.collectorWidget=e),d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.revenueWidget=e),d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.quoteDiscrepenciesWidget=e),d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.quoteRevCostGmWidget=e),d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.fteWidget=e),d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.financeWidget=e),d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.pipeLineGrowthHozbar=e),d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.pipeLineVerStatusbar=e)}},hostBindings:function(e,t){1&e&&d["\u0275\u0275listener"]("resize",(function(){return t.onResize()}),!1,d["\u0275\u0275resolveWindow"])},decls:10,vars:7,consts:[["class","loading-wrapper",4,"ngIf"],["class","container-fluid sales-dashboard-styles pl-0 pr-0",4,"ngIf"],[2,"visibility","hidden","position","fixed",3,"matMenuTriggerFor"],["filterEditTrig","matMenuTrigger"],["filterEdit","matMenu"],["mat-button","",3,"click"],["filterConfigDialog",""],[1,"loading-wrapper"],[1,"loading"],[1,"container-fluid","sales-dashboard-styles","pl-0","pr-0"],[1,"pb-4","main-height"],[1,"container-fluid","align-items-center","row","p-0","m-0","mt-1",3,"contextmenu"],[1,"mb-0","ml-2","mr-2",2,"font-size","14px","font-weight","450","color","#45546e","line-height","45px"],[1,"col-9","filterRow","p-0",3,"formGroup"],[4,"ngFor","ngForOf"],[1,"col-1","mr-2","row","p-0","justify-content-end",2,"flex-wrap","nowrap","margin-left","auto !important"],["mat-button","",2,"flex-wrap","nowrap",3,"matTooltip","click"],[1,"expand-icon"],["mat-button","","matTooltip","Clear all filters",3,"click"],[1,"row","container-fluid",2,"overflow-y","scroll",3,"ngStyle"],["class","col-6 p-0 pb-4",3,"widget_id",4,"ngIf"],["class","col-12 p-0 pb-4",3,"widget_id",4,"ngIf"],["chart_type","probability","class","col-6 p-0 pb-4",3,"widget_id",4,"ngIf"],[1,"row","p-0","col"],["class","col-6 p-0 pb-4","sales_activity_type","2",3,"widget_id","palette",4,"ngIf"],["class","col-6 p-0 pb-4","sales_activity_type","4",3,"widget_id","palette",4,"ngIf"],["class","col-6 p-0 pb-4","sales_activity_type","3",3,"widget_id","palette",4,"ngIf"],["class","col-6 p-0 pb-4","sales_activity_type","8",3,"widget_id","centerTextStyle",4,"ngIf"],["class","col-12 p-0",3,"widget_id",4,"ngIf"],["class","p-0 mr-2",3,"filterColHide","filterCol",4,"ngIf"],[1,"p-0","mr-2"],[1,"filter-name"],[3,"config","formControlName","checklistData","placeholder",4,"ngIf"],["type","text","class","dp-class","ngxDaterangepickerMd","",3,"formControlName","matTooltip","showCustomRangeLabel","locale","alwaysShowCalendars","ranges","linkedCalendars","placeholder","maxDate","startDateChanged",4,"ngIf"],[3,"config","formControlName","checklistData","placeholder"],["type","text","ngxDaterangepickerMd","",1,"dp-class",3,"formControlName","matTooltip","showCustomRangeLabel","locale","alwaysShowCalendars","ranges","linkedCalendars","placeholder","maxDate","startDateChanged"],[1,"col-6","p-0","pb-4",3,"widget_id"],[1,"col-12","p-0","pb-4",3,"widget_id"],["chart_type","probability",1,"col-6","p-0","pb-4",3,"widget_id"],["sales_activity_type","2",1,"col-6","p-0","pb-4",3,"widget_id","palette"],["sales_activity_type","4",1,"col-6","p-0","pb-4",3,"widget_id","palette"],["sales_activity_type","3",1,"col-6","p-0","pb-4",3,"widget_id","palette"],["sales_activity_type","8",1,"col-6","p-0","pb-4",3,"widget_id","centerTextStyle"],[1,"col-12","p-0",3,"widget_id"],[1,"p-4",2,"height","100%","width","100%"],[3,"formGroup","ngSubmit"],[1,"col-8","row","p-3"],["class","col-6",4,"ngFor","ngForOf"],[1,"col-12","row","justify-content-end"],["mat-button","","type","submit",2,"font-size","13px","color","#ffffff","font-weight","bold !important","background","linear-gradient(270deg, #ef4a61 0%, #f27a6c 105.29%)"],[1,"col-6"],[3,"placeholder","showLabel","formControlName","list",4,"ngIf"],[3,"placeholder","required","formControlName","list",4,"ngIf"],[2,"margin-left","6px","font-size","small",3,"disabled","formControlName"],[3,"placeholder","showLabel","formControlName","list"],[3,"placeholder","required","formControlName","list"]],template:function(e,t){if(1&e&&(d["\u0275\u0275template"](0,nl,3,0,"div",0),d["\u0275\u0275template"](1,Jl,54,49,"div",1),d["\u0275\u0275element"](2,"div",2,3),d["\u0275\u0275elementStart"](4,"mat-menu",null,4),d["\u0275\u0275elementStart"](6,"button",5),d["\u0275\u0275listener"]("click",(function(){return t.openFilterConfig()})),d["\u0275\u0275text"](7,"Edit Filters"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](8,es,9,2,"ng-template",null,6,d["\u0275\u0275templateRefExtractor"])),2&e){const e=d["\u0275\u0275reference"](5);d["\u0275\u0275property"]("ngIf",!t._salesDashboardService.widgetConfig),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!!t._salesDashboardService.widgetConfig),d["\u0275\u0275advance"](1),d["\u0275\u0275styleProp"]("left",t.contextMenuPosition.x)("top",t.contextMenuPosition.y),d["\u0275\u0275property"]("matMenuTriggerFor",e)}},directives:[i.NgIf,Re.f,Re.g,xe.a,c.J,c.w,c.n,i.NgForOf,y.a,C.a,i.NgStyle,Ge,c.v,c.l,ci.b,c.e,kt,Da,Wa,Mn,ht,Li,ha,aa,Gt,Ko,lr,_r,Fr,yn,si,Vr,jn,Jn,Zr,ze.a,ke,el.a,c.F],styles:['.sales-dashboard-styles[_ngcontent-%COMP%]     .udrf-header-card{background-color:#fafafa!important}.sales-dashboard-styles[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field{width:40vw;padding-top:5px}.sales-dashboard-styles[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-infix{font-size:14px!important;padding:.5em 0!important;border-top:.54375em solid transparent!important}.sales-dashboard-styles[_ngcontent-%COMP%]   .spinner-align[_ngcontent-%COMP%]{margin:auto;display:inline-block}.sales-dashboard-styles[_ngcontent-%COMP%]   .infinite-scroll-fixed[_ngcontent-%COMP%]{height:395px;overflow-y:scroll}.sales-dashboard-styles[_ngcontent-%COMP%]   .infinite-scroll-fixed-shortened[_ngcontent-%COMP%]{height:345px;overflow-y:scroll}.sales-dashboard-styles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff;font-weight:400;font-size:13px!important;min-width:60px;line-height:30px;padding:0 25px;border-radius:0!important}.sales-dashboard-styles[_ngcontent-%COMP%]   .ta-r[_ngcontent-%COMP%]{text-align:right!important}.sales-dashboard-styles[_ngcontent-%COMP%]   .ta-l[_ngcontent-%COMP%]{text-align:left}.sales-dashboard-styles[_ngcontent-%COMP%]   .is-normal-probability-text[_ngcontent-%COMP%]{color:grey!important}.sales-dashboard-styles[_ngcontent-%COMP%]   .is-low-probability-text[_ngcontent-%COMP%]{color:#cf0001!important}.sales-dashboard-styles[_ngcontent-%COMP%]   .is-moderate-probability-text[_ngcontent-%COMP%]{color:#ff7200!important}.sales-dashboard-styles[_ngcontent-%COMP%]   .is-high-probability-text[_ngcontent-%COMP%]{color:#009432!important}.sales-dashboard-styles[_ngcontent-%COMP%]   .is-normal-probability[_ngcontent-%COMP%]{border-left-color:grey!important}.sales-dashboard-styles[_ngcontent-%COMP%]   .is-low-probability[_ngcontent-%COMP%]{border-left-color:#cf0001!important}.sales-dashboard-styles[_ngcontent-%COMP%]   .is-moderate-probability[_ngcontent-%COMP%]{border-left-color:#ff7200!important}.sales-dashboard-styles[_ngcontent-%COMP%]   .is-high-probability[_ngcontent-%COMP%]{border-left-color:#009432!important}.sales-dashboard-styles[_ngcontent-%COMP%]   .value13Bold[_ngcontent-%COMP%]{color:#000;font-weight:500!important}.sales-dashboard-styles[_ngcontent-%COMP%]   .value13Bold[_ngcontent-%COMP%], .sales-dashboard-styles[_ngcontent-%COMP%]   .value13light[_ngcontent-%COMP%]{font-size:13px!important;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.sales-dashboard-styles[_ngcontent-%COMP%]   .value13light[_ngcontent-%COMP%]{color:#4e4c4c;font-weight:400!important}.sales-dashboard-styles[_ngcontent-%COMP%]   .value14Bold[_ngcontent-%COMP%]{color:#000;font-size:14px!important;font-weight:500!important;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.sales-dashboard-styles[_ngcontent-%COMP%]   .listcard[_ngcontent-%COMP%]{transition:all .25s linear;height:auto}.sales-dashboard-styles[_ngcontent-%COMP%]   .smallSubtleText[_ngcontent-%COMP%]{color:#66615b;font-size:11px}.sales-dashboard-styles[_ngcontent-%COMP%]   .data-type-card[_ngcontent-%COMP%]{min-height:85px;transition:all .3s linear}.sales-dashboard-styles[_ngcontent-%COMP%]   .data-type-card-is-active[_ngcontent-%COMP%]{border-color:#cf0001;background-color:#fff2f2}.sales-dashboard-styles[_ngcontent-%COMP%]   .headingBold[_ngcontent-%COMP%]{color:#1a1a1a;font-weight:600;font-size:20px}.sales-dashboard-styles[_ngcontent-%COMP%]   .valueGrey14[_ngcontent-%COMP%]{font-size:14px}.sales-dashboard-styles[_ngcontent-%COMP%]   .valueGrey12Normal[_ngcontent-%COMP%], .sales-dashboard-styles[_ngcontent-%COMP%]   .valueGrey14[_ngcontent-%COMP%]{color:#4a4a4a;font-weight:400;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.sales-dashboard-styles[_ngcontent-%COMP%]   .valueGrey12Normal[_ngcontent-%COMP%]{font-size:12px}.sales-dashboard-styles[_ngcontent-%COMP%]   .cp[_ngcontent-%COMP%]{cursor:pointer!important}.sales-dashboard-styles[_ngcontent-%COMP%]   .col-2-2[_ngcontent-%COMP%]{flex:0 0 11%;max-width:11%}.sales-dashboard-styles[_ngcontent-%COMP%]   .col-2-8[_ngcontent-%COMP%]{flex:0 0 21.666667%;max-width:21.666667%}.sales-dashboard-styles[_ngcontent-%COMP%]   .alignCenter[_ngcontent-%COMP%]{margin:0 auto;display:block}.sales-dashboard-styles[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]{margin-top:5px!important;line-height:8px;width:26px;height:26px;margin-right:10px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12);animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}.sales-dashboard-styles[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%], .sales-dashboard-styles[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.sales-dashboard-styles[_ngcontent-%COMP%]   .ta-c[_ngcontent-%COMP%]{text-align:center;padding-left:0!important;padding-right:0!important}.sales-dashboard-styles[_ngcontent-%COMP%]   .divAlignItemCenter[_ngcontent-%COMP%]{display:flex;align-items:center}.sales-dashboard-styles[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#3c3c3c;font-weight:400;font-size:18px}.sales-dashboard-styles[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}.sales-dashboard-styles[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}.cp[_ngcontent-%COMP%]{cursor:pointer!important}.value14Bold[_ngcontent-%COMP%]{color:#000;font-size:14px!important;font-weight:500!important;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.dialogAninmation[_ngcontent-%COMP%]{height:300px;width:300px;animation:slide-in .5s}@keyframes slide-in{0%{transform:translateY(-100%)}to{transform:translateY(0)}}  #overlay{position:absolute;display:block;width:100%;height:100%;top:0;left:0;right:0;bottom:0;background-color:initial;z-index:3}  .loading-wrapper{display:flex;width:100%;height:100%;vertical-align:center;justify-content:center;align-items:center}  .loading{color:rgba(0,0,0,.3);font-size:16px}  .loading:before{content:"Loading...";position:absolute;overflow:hidden;max-width:7em;white-space:nowrap;background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%);background-clip:text;-webkit-background-clip:text;-webkit-text-fill-color:transparent;animation:loading 6s linear infinite}@keyframes loading{0%{max-width:0}}  .no-data{color:rgba(0,0,0,.3);font-size:16px}.filter-name[_ngcontent-%COMP%]{font-size:smaller;color:var(--primaryColor)}.sticky-header[_ngcontent-%COMP%]{position:absolute;top:65px}  .md-drppicker.double{width:650px!important}.dp-class[_ngcontent-%COMP%]{background:0 0;border:1px solid #d3d3d3;border-radius:4px;font-size:12px;font-weight:500;width:100%;height:40px;margin-top:4px;cursor:pointer;text-align:center}@media screen and (max-width:1024px){.filterColHide[_ngcontent-%COMP%]{display:none}.filterColHide[_ngcontent-%COMP%]:nth-child(-n+3){display:block}.filterRow[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 1fr 1fr!important}}@media screen and (min-width:1152px){.filterCol[_ngcontent-%COMP%], .filterColHide[_ngcontent-%COMP%]:nth-child(-n+4){display:block}.filterColHide[_ngcontent-%COMP%]{display:none}.filterRow[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 1fr 1fr 1fr}}.main-height[_ngcontent-%COMP%]{height:var(--dynamicHeight)}']}),e})()}];let ns=(()=>{class e{}return e.\u0275mod=d["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=d["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[a.k.forChild(ts)],a.k]}),e})();var is=n("Xi0T");let as=(()=>{class e{}return e.\u0275mod=d["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=d["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,y.b,C.b]]}),e})();var os=n("mEBv");let rs=(()=>{class e{}return e.\u0275mod=d["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=d["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,ns,he.b,is.a,p.g,ve.d,as,os.b,C.b,Ve.b,_t.b,Re.e,xe.b,ye.b,Re.e,Ro,c.p,c.E,Oe.e,y.b,Ee.d,Re.e,Me.b,Ka.b,ci.c.forRoot(),c.p,c.E,$.b,ze.b,Ae.e,Yt.b,Ia.c]]}),e})()},NJ67:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));class i{constructor(){this.disabled=!1}onChange(e){}onTouched(e){}writeValue(e){this.value=e}registerOnChange(e){this.onChange=e}registerOnTouched(e){this.onTouched=e}setDisabledState(e){this.disabled=e}}},R3G1:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var i=n("xG9w"),a=n("fXoL");let o=(()=>{class e{transform(e,t,n){let a=i.findWhere(t,{field_name:e,type:n});return!!a&&!!a.is_active}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=a["\u0275\u0275definePipe"]({name:"checkActive",type:e,pure:!0}),e})()},"TmG/":function(e,t,n){"use strict";n.d(t,"a",(function(){return v}));var i=n("fXoL"),a=n("3Pt+"),o=n("jtHE"),r=n("XNiG"),l=n("NJ67"),s=n("1G5W"),c=n("kmnG"),d=n("ofXK"),p=n("d3UM"),m=n("FKr1"),g=n("WJ5W"),u=n("Qu3c");function f(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-label"),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](e.placeholder)}}function h(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-option",7),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275property"]("value",null),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](e.disableNone?"Select One":"None")}}function _(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"mat-option",8),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const n=t.$implicit;return i["\u0275\u0275nextContext"]().emitChanges(n)})),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;i["\u0275\u0275property"]("value",e.id||e._id)("matTooltip",e.name),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let v=(()=>{class e extends l.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new a.j,this.fieldFilterCtrl=new a.j,this.list=[],this.required=!1,this.disableNone=!1,this.valueChange=new i.EventEmitter,this.disabled=!1,this.hasNoneOption=!0,this.filteredList=new o.a,this.change=new i.EventEmitter,this.hideMatLabel=!1,this._onDestroy=new r.b,this.emitChanges=e=>{this.change.emit(e)}}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(s.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(s.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](i.Renderer2))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search"]],inputs:{list:"list",placeholder:"placeholder",required:"required",disableNone:"disableNone",disabled:"disabled",hasNoneOption:"hasNoneOption",hideMatLabel:"hideMatLabel"},outputs:{valueChange:"valueChange",change:"change"},features:[i["\u0275\u0275ProvidersFeature"]([{provide:a.t,useExisting:Object(i.forwardRef)(()=>e),multi:!0}]),i["\u0275\u0275InheritDefinitionFeature"],i["\u0275\u0275NgOnChangesFeature"]],decls:9,vars:11,consts:[["appearance","outline"],[4,"ngIf"],[3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value",4,"ngIf"],[3,"value","matTooltip","click",4,"ngFor","ngForOf"],[3,"value"],[3,"value","matTooltip","click"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"mat-form-field",0),i["\u0275\u0275template"](1,f,2,1,"mat-label",1),i["\u0275\u0275elementStart"](2,"mat-select",2,3),i["\u0275\u0275elementStart"](4,"mat-option"),i["\u0275\u0275element"](5,"ngx-mat-select-search",4),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](6,h,2,2,"mat-option",5),i["\u0275\u0275template"](7,_,2,3,"mat-option",6),i["\u0275\u0275pipe"](8,"async"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",!t.hideMatLabel),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.hasNoneOption),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngForOf",i["\u0275\u0275pipeBind1"](8,9,t.filteredList)))},directives:[c.c,d.NgIf,p.c,a.v,a.k,a.F,m.p,g.a,d.NgForOf,c.g,u.a],pipes:[d.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%}"]}),e})()},os0P:function(e,t,n){"use strict";n.d(t,"a",(function(){return x}));var i=n("mrSG"),a=n("fXoL"),o=n("3Pt+"),r=n("jtHE"),l=n("XNiG"),s=n("NJ67"),c=n("1G5W"),d=n("xG9w"),p=n("t44d"),m=n("kmnG"),g=n("ofXK"),u=n("d3UM"),f=n("FKr1"),h=n("WJ5W"),_=n("Qu3c");const v=["singleSelect"];function y(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"mat-option",6),a["\u0275\u0275text"](1,"Select one"),a["\u0275\u0275elementEnd"]()),2&e&&a["\u0275\u0275property"]("value",null)}function C(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"mat-option",7),a["\u0275\u0275listener"]("click",(function(){a["\u0275\u0275restoreView"](e);const n=t.$implicit;return a["\u0275\u0275nextContext"]().emitChanges(n)})),a["\u0275\u0275text"](1),a["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;a["\u0275\u0275propertyInterpolate"]("matTooltip",e.name),a["\u0275\u0275property"]("value",e.id),a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let x=(()=>{class e extends s.a{constructor(e,t){super(),this.renderer=e,this.pmMasterService=t,this.fieldCtrl=new o.j,this.fieldFilterCtrl=new o.j,this.list=[],this.required=!1,this.valueChange=new a.EventEmitter,this.disabled=!1,this.disableColor="#E8E9EE",this.disableField=!1,this.showSelect=!0,this.uniqueList=[],this.filteredList=new r.a,this.change=new a.EventEmitter,this._onDestroy=new l.b,this.formConfig=[],this.emitChanges=e=>{this.change.emit(e)}}ngOnInit(){return Object(i.c)(this,void 0,void 0,(function*(){yield this.pmMasterService.getPMFormCustomizeConfigV().then(e=>{e&&(this.formConfig=e)});const e=d.where(this.formConfig,{type:"project-theme",field_name:"styles",is_active:!0});this.fontStyle=e.length>0&&e[0].data.font_style?e[0].data.font_style:"Roboto",document.documentElement.style.setProperty("--inputSearchFont",this.fontStyle),this.fieldFilterCtrl.valueChanges.pipe(Object(c.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(c.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)}),this.updateFilteredList()}))}ngOnChanges(){this.updateFilteredList()}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value,t=this.list.slice();e&&(e=e.toLowerCase(),t=t.filter(t=>t.name.toLowerCase().indexOf(e)>-1)),this.uniqueList.length>0&&(t=t.filter(e=>this.uniqueList.includes(e))),this.filteredList.next(t)}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}isDisabled(){return this.disableField?{"background-color":null!=this.disableColor?this.disableColor:"#E8E9EE","pointer-events":"none",color:"#6E7B8F"}:{}}writeValue(e){this.fieldCtrl.setValue(e)}updateFilteredList(){this.uniqueList.length>0?this.filterBanks():this.filteredList.next(this.list.slice())}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275directiveInject"](a.Renderer2),a["\u0275\u0275directiveInject"](p.a))},e.\u0275cmp=a["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search-name"]],viewQuery:function(e,t){if(1&e&&a["\u0275\u0275viewQuery"](v,!0),2&e){let e;a["\u0275\u0275queryRefresh"](e=a["\u0275\u0275loadQuery"]())&&(t.singleSelect=e.first)}},inputs:{list:"list",placeholder:"placeholder",required:"required",disabled:"disabled",disableColor:"disableColor",disableField:"disableField",showSelect:"showSelect",uniqueList:"uniqueList"},outputs:{valueChange:"valueChange",change:"change"},features:[a["\u0275\u0275ProvidersFeature"]([{provide:o.t,useExisting:Object(a.forwardRef)(()=>e),multi:!0}]),a["\u0275\u0275InheritDefinitionFeature"],a["\u0275\u0275NgOnChangesFeature"]],decls:8,vars:11,consts:[["appearance","outline",1,"app-input-search",3,"ngStyle"],["nameOnly","",3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value",4,"ngIf"],[3,"value","matTooltip","click",4,"ngFor","ngForOf"],[3,"value"],[3,"value","matTooltip","click"]],template:function(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"mat-form-field",0),a["\u0275\u0275elementStart"](1,"mat-select",1,2),a["\u0275\u0275elementStart"](3,"mat-option"),a["\u0275\u0275element"](4,"ngx-mat-select-search",3),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](5,y,2,1,"mat-option",4),a["\u0275\u0275template"](6,C,2,3,"mat-option",5),a["\u0275\u0275pipe"](7,"async"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e&&(a["\u0275\u0275property"]("ngStyle",t.isDisabled()),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",t.showSelect),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngForOf",a["\u0275\u0275pipeBind1"](7,9,t.filteredList)))},directives:[m.c,g.NgStyle,u.c,o.v,o.k,o.F,f.p,h.a,g.NgIf,g.NgForOf,_.a],pipes:[g.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}.mat-form-field[_ngcontent-%COMP%]:disabled{background-color:#0ff}.app-input-search[_ngcontent-%COMP%]     .mat-select-value{color:var(--blue-grey-80,#5f6c81);font-family:var(--inputSearchFont)!important;font-size:13px;font-style:normal;font-weight:400;line-height:16px}"]}),e})()}}]);