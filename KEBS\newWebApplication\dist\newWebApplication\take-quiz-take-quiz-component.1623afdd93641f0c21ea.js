(window.webpackJsonp=window.webpackJsonp||[]).push([[1013],{m0GA:function(e,t,n){"use strict";n.r(t),n.d(t,"TakeQuizComponent",(function(){return T})),n.d(t,"QuizInstructionComponent",(function(){return D}));var i=n("mrSG"),r=n("l5mm"),o=n("33Jv"),s=n("0IaG"),l=n("5+WD"),a=n("PSD3"),c=n.n(a),d=n("fXoL"),m=n("bpb9"),u=n("ofXK"),h=n("NFeN"),v=n("bTqV"),x=n("FKr1");function p(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",37),d["\u0275\u0275elementStart"](1,"div",38),d["\u0275\u0275elementStart"](2,"div",39),d["\u0275\u0275listener"]("click",(function(){d["\u0275\u0275restoreView"](e);const n=t.$implicit;return d["\u0275\u0275nextContext"](3).selectMcqAnswer(n)})),d["\u0275\u0275text"](3),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](4,"div",40),d["\u0275\u0275text"](5),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=t.index,i=d["\u0275\u0275nextContext"](3);d["\u0275\u0275advance"](2),d["\u0275\u0275styleProp"]("background-color",i.isMcqAnswerSelected(e.uuid)?"#29903b":"#e0e0e0")("color",i.isMcqAnswerSelected(e.uuid)?"white":"black"),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",i.getCharCode(65+n)," "),d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"](" ",e.choiceName," ")}}function f(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",35),d["\u0275\u0275elementStart"](1,"div",12),d["\u0275\u0275template"](2,p,6,6,"div",36),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngForOf",e.currentlyActiveQuestion.choices)}}function g(e,t){if(1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275template"](1,f,3,1,"div",34),d["\u0275\u0275elementContainerEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",1==e.currentlyActiveQuestion.questionTypeId)}}function b(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",46),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",e.value," ")}}function S(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",47),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",e.value," ")}}function E(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",35),d["\u0275\u0275elementStart"](1,"div",41),d["\u0275\u0275elementStart"](2,"div",42),d["\u0275\u0275template"](3,b,2,1,"div",43),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](4,"div",41),d["\u0275\u0275elementStart"](5,"div",44),d["\u0275\u0275listener"]("cdkDropListDropped",(function(t){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](2).dropMatch(t)})),d["\u0275\u0275template"](6,S,2,1,"div",45),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngForOf",e.currentlyActiveQuestion.leftArr),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngForOf",e.currentlyActiveQuestion.rightArr)}}function w(e,t){if(1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275template"](1,E,7,2,"div",34),d["\u0275\u0275elementContainerEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",2==e.currentlyActiveQuestion.questionTypeId)}}function y(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",47),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",e.item," ")}}function A(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",35),d["\u0275\u0275elementStart"](1,"div",41),d["\u0275\u0275elementStart"](2,"div",44),d["\u0275\u0275listener"]("cdkDropListDropped",(function(t){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](2).dropOrder(t)})),d["\u0275\u0275template"](3,y,2,1,"div",48),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](4,"pre"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngForOf",e.currentlyActiveQuestion.orderedAnswers)}}function Q(e,t){if(1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275template"](1,A,5,1,"div",34),d["\u0275\u0275elementContainerEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",3==e.currentlyActiveQuestion.questionTypeId)}}function I(e,t){1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275text"](1,"ed"),d["\u0275\u0275elementContainerEnd"]())}const k=function(e){return{backgroundColor:e}},C=function(e){return{visibility:e}};function _(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementStart"](1,"div",49),d["\u0275\u0275listener"]("click",(function(){d["\u0275\u0275restoreView"](e);const n=t.index;return d["\u0275\u0275nextContext"]().moveToQuestion(n)})),d["\u0275\u0275text"](2),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](3,"div",50),d["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=t.index,i=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngStyle",d["\u0275\u0275pureFunction1"](3,k,i.getBubbleColor(e.userStatus))),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",n+1," "),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngStyle",d["\u0275\u0275pureFunction1"](5,C,e.isMarkedReview?"visible":"hidden"))}}const O=function(e){return{color:e}};function M(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",51),d["\u0275\u0275elementStart"](1,"mat-icon",52),d["\u0275\u0275text"](2,"fiber_manual_record"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](3,"span"),d["\u0275\u0275text"](4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngStyle",d["\u0275\u0275pureFunction1"](2,O,e.color)),d["\u0275\u0275advance"](3),d["\u0275\u0275textInterpolate1"](" ",e.name,"")}}const z=function(e){return{filter:e}};let T=(()=>{class e{constructor(e,t,n,r){this._learner=e,this.dialogRef=t,this.data=n,this._dialog=r,this.questions=[],this.selectedAnswer=null,this.isSubmittingQuiz=!1,this.isQuizStarted=!1,this.bubbleIndicators=[{name:"ANSWERED",color:"#129836"},{name:"NOT ANSWERED",color:"#cf0001"},{name:"MARKED FOR REVIEW",color:"#6d4c6d"},{name:"NOT VISITED",color:"#afafaf"}],this.subs=new o.a,this.milliSecondsInASecond=1e3,this.hoursInADay=24,this.minutesInAnHour=60,this.SecondsInAMinute=60,this.secondsTaken=0,this.startQuiz=()=>Object(i.c)(this,void 0,void 0,(function*(){this.lessonData=this.data.lessonData,this.questions=yield this.getQuizQuestions(this.lessonData.lessonId),this.setInitialActiveQn()})),this.showInstructions=()=>{this._dialog.open(D,{width:"52%",disableClose:!0}).afterClosed().subscribe(e=>{"start"==e?(this.isQuizStarted=!0,setTimeout(()=>{this.startTimer()},1e3)):"back"==e&&this.dialogRef.close()})},this.getQuizQuestions=e=>new Promise((t,n)=>{this._learner.getQuizContent(e).subscribe(e=>{this.testTime=e.total_duration,e.data.forEach((e,t)=>{e.userStatus=0==t?"not_answered":"not_visited",e.isMarkedReview=!1}),t(e.data)},e=>{console.error(e),this._learner.showSnack("Unable to fetch question!")})}),this.setTestTime=e=>{this.testTime=e,this.dDay=new Date((new Date).getTime()+6e4*this.testTime)},this.getCharCode=e=>String.fromCharCode(e),this.markForReview=()=>{let e=this.questions[this.currentlyActiveQuestionIndex].isMarkedReview;this.currentlyActiveQuestion.isMarkedReview=!e,this.questions[this.currentlyActiveQuestionIndex].isMarkedReview=!e,console.log(e,!e)}}getTimeDifference(){this.timeDifference=this.dDay.getTime()-(new Date).getTime(),this.allocateTimeUnits(this.timeDifference)}allocateTimeUnits(e){this.secondsToDday=Math.floor(e/this.milliSecondsInASecond%this.SecondsInAMinute),this.minutesToDday=Math.floor(e/(this.milliSecondsInASecond*this.minutesInAnHour)%this.SecondsInAMinute)}onRightClick(e){e.preventDefault()}ngOnInit(){return Object(i.c)(this,void 0,void 0,(function*(){this.showInstructions(),this.startQuiz()}))}startTimer(){console.log("triggered"),this.dateNow=new Date,this.setTestTime(this.testTime),this.subscription=Object(r.a)(1e3).subscribe(e=>{this.secondsTaken++,this.getTimeDifference(),0==this.secondsToDday&&0==this.minutesToDday&&this.submitQuiz()})}setInitialActiveQn(){this.currentlyActiveQuestionIndex=0,this.currentlyActiveQuestion=this.questions[0]}changeQuestion(e){"left"==e&&this.currentlyActiveQuestionIndex>0&&this.moveToQuestion(this.currentlyActiveQuestionIndex-1),"right"==e&&this.currentlyActiveQuestionIndex<this.questions.length-1&&this.moveToQuestion(this.currentlyActiveQuestionIndex+1),this.changeQuestionStatus()}moveToQuestion(e){this.currentlyActiveQuestionIndex=e,this.currentlyActiveQuestion=this.questions[e],this.changeQuestionStatus()}shuffleItems(e){return e.sort(()=>Math.random()-.5)}dropOrder(e){Object(l.h)(this.currentlyActiveQuestion.orderedAnswers,e.previousIndex,e.currentIndex),this.selectedAnswer=this.currentlyActiveQuestion.orderedAnswers}dropMatch(e){Object(l.h)(this.currentlyActiveQuestion.rightArr,e.previousIndex,e.currentIndex),this.selectedAnswer=this.currentlyActiveQuestion.rightArr}changeQuestionStatus(){null==this.currentlyActiveQuestion.answer&&(this.questions[this.currentlyActiveQuestionIndex].userStatus="not_answered")}getBubbleColor(e){return"not_visited"==e?"rgb(175, 175, 175)":"not_answered"==e?"rgb(207, 0, 1)":"answered"==e?"rgb(18, 152, 54)":void 0}selectMcqAnswer(e){this.selectedAnswer=e}isMcqAnswerSelected(e){return this.selectedAnswer?this.selectedAnswer.uuid==e:!(!this.currentlyActiveQuestion.answer||this.currentlyActiveQuestion.answer.uuid!=e)||void 0}saveAnswer(){1==this.currentlyActiveQuestion.questionTypeId?(this.selectedAnswer||this.currentlyActiveQuestion.answer)&&(this.selectedAnswer&&(this.questions[this.currentlyActiveQuestionIndex].answer=this.selectedAnswer),this.questions[this.currentlyActiveQuestionIndex].userStatus="answered",this.selectedAnswer=null,this.changeQuestion("right")):2==this.currentlyActiveQuestion.questionTypeId?(this.selectedAnswer=this.currentlyActiveQuestion.rightArr.map((e,t)=>({uuidLeft:this.currentlyActiveQuestion.leftArr[t].uuid,uuidRight:e.uuid,valueLeft:this.currentlyActiveQuestion.leftArr[t].value,valueRight:e.value})),this.questions[this.currentlyActiveQuestionIndex].answer=this.selectedAnswer,this.questions[this.currentlyActiveQuestionIndex].userStatus="answered",this.selectedAnswer=null,this.changeQuestion("right")):3==this.currentlyActiveQuestion.questionTypeId&&(this.selectedAnswer=this.currentlyActiveQuestion.orderedAnswers,this.questions[this.currentlyActiveQuestionIndex].answer=this.selectedAnswer,this.questions[this.currentlyActiveQuestionIndex].userStatus="answered",this.selectedAnswer=null,this.changeQuestion("right"))}submitQuiz(){return Object(i.c)(this,void 0,void 0,(function*(){this.isSubmittingQuiz=!0,this.subscription.unsubscribe(),this.subs.sink=this._learner.submitQuiz(this.lessonData.lessonId,this.secondsTaken,this.questions).subscribe(e=>Object(i.c)(this,void 0,void 0,(function*(){console.log(e);const{LQuizResultComponent:t}=yield n.e(736).then(n.bind(null,"pAR7"));this._dialog.open(t,{width:"40%",minHeight:"58vh",disableClose:!0,data:e.data}).afterClosed().subscribe(e=>{this.dialogRef.close()})})),e=>{console.error(e),this.isSubmittingQuiz=!1,this._learner.showSnack("Failed to submit the Quiz !")})}))}closeDialog(){c.a.fire({title:"Are you sure?",text:"Your Quiz will be auto submitted !",icon:"warning",showCancelButton:!0,confirmButtonColor:"#3085d6",cancelButtonColor:"#d33",confirmButtonText:"Yes, close it!"}).then(e=>{e.isConfirmed&&this.submitQuiz()})}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](m.a),d["\u0275\u0275directiveInject"](s.h),d["\u0275\u0275directiveInject"](s.a),d["\u0275\u0275directiveInject"](s.b))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["learner-take-quiz"]],hostBindings:function(e,t){1&e&&d["\u0275\u0275listener"]("contextmenu",(function(e){return t.onRightClick(e)}))},decls:72,vars:18,consts:[[1,"row","h-100","quiz-container",3,"ngStyle"],[1,"col-9","border-right","solid"],[1,"row","pt-3","pb-3","border-bottom","solid"],[1,"col-10","large-t"],[1,"col-2"],[1,"row"],[1,"col-3","p-0","d-flex","align-items-center"],[1,"col-9","p-0","d-flex","align-items-center"],[1,"timer","large-t"],["id","minutes"],["id","seconds"],[1,"row","mt-3"],[1,"col-12"],[1,"col-12",2,"font-size","16px"],[1,"col-12",2,"font-size","17px"],[4,"ngIf"],[1,"row","bottom-fixed","pt-2","pb-2"],[1,"col-5"],["mat-button","",3,"click"],["mat-icon-button","",3,"click"],[1,"ml-2","mr-2"],[1,"col-2","d-flex","justify-content-end"],["mat-button","",1,"nxt-btn",3,"click"],[1,"col-3"],[1,"col-10"],[1,"col-2","d-flex","align-items-center"],[1,"d-flex","mt-4","flex-wrap"],[4,"ngFor","ngForOf"],[1,"row","control-pane-bottom"],[1,"d-flex","flex-wrap"],["class","d-flex align-items-center p-2",4,"ngFor","ngForOf"],[1,"row","mb-3","mt-5"],[1,"col-12","d-flex","justify-content-center"],["mat-button","",1,"submit-btn",3,"disabled","click"],["class","row mt-4",4,"ngIf"],[1,"row","mt-4"],["class","row mb-3",4,"ngFor","ngForOf"],[1,"row","mb-3"],[1,"col-0"],["matRipple","",1,"circle-choice",3,"click"],[1,"col","d-flex","align-items-center"],[1,"col-6"],[1,"example-list"],["class","example-box",4,"ngFor","ngForOf"],["cdkDropList","",1,"example-list",3,"cdkDropListDropped"],["class","example-box","cdkDrag","",4,"ngFor","ngForOf"],[1,"example-box"],["cdkDrag","",1,"example-box"],["cdkDrag","","class","example-box",4,"ngFor","ngForOf"],["matRipple","",1,"q-bubble",3,"ngStyle","click"],[1,"dot",3,"ngStyle"],[1,"d-flex","align-items-center","p-2"],[1,"mr-2",3,"ngStyle"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",0),d["\u0275\u0275elementStart"](1,"div",1),d["\u0275\u0275elementStart"](2,"div",2),d["\u0275\u0275elementStart"](3,"div",3),d["\u0275\u0275text"](4," Statistics Chapter Test - Problem Solving "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](5,"div",4),d["\u0275\u0275elementStart"](6,"div",5),d["\u0275\u0275elementStart"](7,"div",6),d["\u0275\u0275elementStart"](8,"mat-icon"),d["\u0275\u0275text"](9,"timer"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](10,"div",7),d["\u0275\u0275elementStart"](11,"div",8),d["\u0275\u0275elementStart"](12,"span",9),d["\u0275\u0275text"](13),d["\u0275\u0275elementEnd"](),d["\u0275\u0275text"](14,"Min "),d["\u0275\u0275elementStart"](15,"span",10),d["\u0275\u0275text"](16),d["\u0275\u0275elementEnd"](),d["\u0275\u0275text"](17,"S "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](18,"div",11),d["\u0275\u0275elementStart"](19,"div",12),d["\u0275\u0275elementStart"](20,"div",5),d["\u0275\u0275elementStart"](21,"div",13),d["\u0275\u0275elementStart"](22,"strong"),d["\u0275\u0275text"](23),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](24,"div",11),d["\u0275\u0275elementStart"](25,"div",14),d["\u0275\u0275text"](26),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](27,g,2,1,"ng-container",15),d["\u0275\u0275template"](28,w,2,1,"ng-container",15),d["\u0275\u0275template"](29,Q,2,1,"ng-container",15),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](30,"div",16),d["\u0275\u0275elementStart"](31,"div",12),d["\u0275\u0275elementStart"](32,"div",5),d["\u0275\u0275elementStart"](33,"div",17),d["\u0275\u0275elementStart"](34,"button",18),d["\u0275\u0275listener"]("click",(function(){return t.markForReview()})),d["\u0275\u0275elementStart"](35,"mat-icon"),d["\u0275\u0275text"](36,"star_border "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275text"](37," Mark"),d["\u0275\u0275template"](38,I,2,0,"ng-container",15),d["\u0275\u0275text"](39," For Review "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](40,"div",17),d["\u0275\u0275elementStart"](41,"button",19),d["\u0275\u0275listener"]("click",(function(){return t.changeQuestion("left")})),d["\u0275\u0275elementStart"](42,"mat-icon"),d["\u0275\u0275text"](43,"chevron_left"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](44,"span",20),d["\u0275\u0275elementStart"](45,"strong"),d["\u0275\u0275text"](46),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](47,"button",19),d["\u0275\u0275listener"]("click",(function(){return t.changeQuestion("right")})),d["\u0275\u0275elementStart"](48,"mat-icon"),d["\u0275\u0275text"](49,"chevron_right"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](50,"div",21),d["\u0275\u0275elementStart"](51,"button",22),d["\u0275\u0275listener"]("click",(function(){return t.saveAnswer()})),d["\u0275\u0275text"](52," Save & Next "),d["\u0275\u0275elementStart"](53,"mat-icon"),d["\u0275\u0275text"](54,"east"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](55,"div",23),d["\u0275\u0275elementStart"](56,"div",2),d["\u0275\u0275element"](57,"div",24),d["\u0275\u0275elementStart"](58,"div",25),d["\u0275\u0275elementStart"](59,"button",19),d["\u0275\u0275listener"]("click",(function(){return t.closeDialog()})),d["\u0275\u0275elementStart"](60,"mat-icon"),d["\u0275\u0275text"](61,"cancel"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](62,"div",26),d["\u0275\u0275template"](63,_,4,7,"div",27),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](64,"div",28),d["\u0275\u0275elementStart"](65,"div",12),d["\u0275\u0275elementStart"](66,"div",29),d["\u0275\u0275template"](67,M,5,4,"div",30),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](68,"div",31),d["\u0275\u0275elementStart"](69,"div",32),d["\u0275\u0275elementStart"](70,"button",33),d["\u0275\u0275listener"]("click",(function(){return t.submitQuiz()})),d["\u0275\u0275text"](71," Submit Quiz "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e&&(d["\u0275\u0275property"]("ngStyle",d["\u0275\u0275pureFunction1"](16,z,t.isQuizStarted?"none":"blur(3px)")),d["\u0275\u0275advance"](13),d["\u0275\u0275textInterpolate1"](" ",t.minutesToDday," "),d["\u0275\u0275advance"](3),d["\u0275\u0275textInterpolate1"](" ",t.secondsToDday," "),d["\u0275\u0275advance"](7),d["\u0275\u0275textInterpolate2"]("Q. ",t.currentlyActiveQuestionIndex+1," of ",t.questions.length,""),d["\u0275\u0275advance"](3),d["\u0275\u0275textInterpolate1"](" ",null==t.currentlyActiveQuestion?null:t.currentlyActiveQuestion.questionName," "),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.currentlyActiveQuestion),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.currentlyActiveQuestion),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.currentlyActiveQuestion),d["\u0275\u0275advance"](5),d["\u0275\u0275classProp"]("mark-for-review-btn-active",t.currentlyActiveQuestion.isMarkedReview),d["\u0275\u0275advance"](4),d["\u0275\u0275property"]("ngIf",t.currentlyActiveQuestion.isMarkedReview),d["\u0275\u0275advance"](8),d["\u0275\u0275textInterpolate"](t.currentlyActiveQuestionIndex+1),d["\u0275\u0275advance"](17),d["\u0275\u0275property"]("ngForOf",t.questions),d["\u0275\u0275advance"](4),d["\u0275\u0275property"]("ngForOf",t.bubbleIndicators),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("disabled",t.isSubmittingQuiz))},directives:[u.NgStyle,h.a,u.NgIf,v.a,u.NgForOf,x.u,l.e,l.a],styles:[".large-t[_ngcontent-%COMP%]{font-size:large}.bottom-fixed[_ngcontent-%COMP%]{position:absolute;width:100%;box-shadow:0 4px 8px 0 rgba(0,0,0,.2),0 6px 20px 0 rgba(0,0,0,.19);bottom:0;left:-1px}.nxt-btn[_ngcontent-%COMP%]{background-color:#236fe0;color:#fff}.q-bubble[_ngcontent-%COMP%]{height:2rem;width:2rem;background-color:grey;color:#fff;display:flex;font-size:17px;align-items:center;margin:11px;justify-content:center;border-radius:50%;cursor:pointer}.control-pane-bottom[_ngcontent-%COMP%]{position:absolute;bottom:0;left:-1px}.submit-btn[_ngcontent-%COMP%]{color:#fff;background-color:#53c353;width:83%;font-size:20px}.circle-choice[_ngcontent-%COMP%]{height:30px;width:30px;background-color:#e0e0e0;display:flex;cursor:pointer;align-items:center;font-weight:800;justify-content:center;border-radius:50%;margin-right:13px}.example-list[_ngcontent-%COMP%]{width:500px;max-width:100%;border:1px solid #ccc;min-height:60px;display:block;background:#fff;border-radius:4px;overflow:hidden}.example-box[_ngcontent-%COMP%]{padding:20px 10px;border-bottom:1px solid #ccc;color:rgba(0,0,0,.87);display:flex;flex-direction:row;align-items:center;justify-content:space-between;box-sizing:border-box;cursor:move;background:#fff;font-size:14px}.cdk-drag-preview[_ngcontent-%COMP%]{box-sizing:border-box;border-radius:4px;box-shadow:0 5px 5px -3px rgba(0,0,0,.2),0 8px 10px 1px rgba(0,0,0,.14),0 3px 14px 2px rgba(0,0,0,.12)}.cdk-drag-placeholder[_ngcontent-%COMP%]{opacity:0}.cdk-drag-animating[_ngcontent-%COMP%]{transition:transform .25s cubic-bezier(0,0,.2,1)}.example-box[_ngcontent-%COMP%]:last-child{border:none}.example-list.cdk-drop-list-dragging[_ngcontent-%COMP%]   .example-box[_ngcontent-%COMP%]:not(.cdk-drag-placeholder){transition:transform .25s cubic-bezier(0,0,.2,1)}.dot[_ngcontent-%COMP%]{background-color:#6d4c6d;height:9px;width:8px;border-radius:50%;right:-43px;top:-44px;position:relative}.mark-for-review-btn-active[_ngcontent-%COMP%]{background:purple;color:#fff}.mark-for-review-btn-active[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#fff;line-height:21px}"]}),e})(),D=(()=>{class e{constructor(e){this.dialogRef=e}closeDialog(){this.dialogRef.close("back")}startQuiz(){this.dialogRef.close("start")}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](s.h))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["quiz-instructions"]],decls:93,vars:0,consts:[[1,"p-3"],[1,"row"],[1,"col-12",2,"font-size","20px"],[1,"row","mt-4"],[1,"col-1","list","d-flex","align-items-center"],[1,"col-11","d-flex","align-items-center","list"],[2,"margin-left","20px"],[2,"font-size","17px"],[1,"row","mt-2"],[2,"margin-left","18px"],[2,"color","#afafaf"],[2,"color","#cf0001"],[2,"color","#129836"],[2,"color","#6d4c6d"],["mat-button","",1,"nxt-btn"],["mat-button","",1,"submit-btn"],[1,"d-flex","justify-content-end"],["mat-button","",1,"action-btn",3,"click"],["mat-button","",1,"action-btn",2,"background","#cf0001","color","white",3,"click"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",0),d["\u0275\u0275elementStart"](1,"div",1),d["\u0275\u0275elementStart"](2,"div",2),d["\u0275\u0275text"](3,"What you need to Know ?"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](4,"div",3),d["\u0275\u0275elementStart"](5,"div",4),d["\u0275\u0275text"](6,"1"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](7,"div",5),d["\u0275\u0275text"](8," Try to complete the Quiz before the timer runs out "),d["\u0275\u0275elementStart"](9,"mat-icon",6),d["\u0275\u0275text"](10,"timer"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](11,"span",7),d["\u0275\u0275text"](12,"10 Min 23 S"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](13,"div",8),d["\u0275\u0275elementStart"](14,"div",4),d["\u0275\u0275text"](15,"2"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](16,"div",5),d["\u0275\u0275text"](17," You can navigate between the question using "),d["\u0275\u0275elementStart"](18,"mat-icon",6),d["\u0275\u0275text"](19,"navigate_before"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](20,"span",7),d["\u0275\u0275text"](21,"1"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](22,"mat-icon"),d["\u0275\u0275text"](23,"navigate_next"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](24,"div",8),d["\u0275\u0275elementStart"](25,"div",4),d["\u0275\u0275text"](26,"3"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](27,"div",5),d["\u0275\u0275text"](28," You can mark your question for review later using "),d["\u0275\u0275elementStart"](29,"mat-icon",9),d["\u0275\u0275text"](30,"star_border "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275text"](31," Mark For Review "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](32,"div",8),d["\u0275\u0275elementStart"](33,"div",4),d["\u0275\u0275text"](34,"4"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](35,"div",5),d["\u0275\u0275text"](36," Questions that are \xa0 "),d["\u0275\u0275elementStart"](37,"span",10),d["\u0275\u0275text"](38,"Not Visited"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275text"](39," \xa0 are indicated by "),d["\u0275\u0275elementStart"](40,"mat-icon",10),d["\u0275\u0275text"](41,"fiber_manual_record"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](42,"div",8),d["\u0275\u0275elementStart"](43,"div",4),d["\u0275\u0275text"](44,"5"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](45,"div",5),d["\u0275\u0275text"](46," Questions that are \xa0 "),d["\u0275\u0275elementStart"](47,"span",11),d["\u0275\u0275text"](48,"Not Answered"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275text"](49,"\xa0 are indicated by "),d["\u0275\u0275elementStart"](50,"mat-icon",11),d["\u0275\u0275text"](51,"fiber_manual_record"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](52,"div",8),d["\u0275\u0275elementStart"](53,"div",4),d["\u0275\u0275text"](54,"6"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](55,"div",5),d["\u0275\u0275text"](56," Questions that are \xa0 "),d["\u0275\u0275elementStart"](57,"span",12),d["\u0275\u0275text"](58,"Answered"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275text"](59," \xa0 are indicated by "),d["\u0275\u0275elementStart"](60,"mat-icon",12),d["\u0275\u0275text"](61,"fiber_manual_record"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](62,"div",8),d["\u0275\u0275elementStart"](63,"div",4),d["\u0275\u0275text"](64,"7"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](65,"div",5),d["\u0275\u0275text"](66," Questions that are \xa0 "),d["\u0275\u0275elementStart"](67,"span",13),d["\u0275\u0275text"](68,"Marked for Review"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275text"](69,"\xa0 are indicated by "),d["\u0275\u0275elementStart"](70,"mat-icon",13),d["\u0275\u0275text"](71,"fiber_manual_record"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](72,"div",8),d["\u0275\u0275elementStart"](73,"div",4),d["\u0275\u0275text"](74,"8"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](75,"div",5),d["\u0275\u0275text"](76," Dont forget to save the question after answering \xa0 "),d["\u0275\u0275elementStart"](77,"button",14),d["\u0275\u0275text"](78," Save & Next "),d["\u0275\u0275elementStart"](79,"mat-icon"),d["\u0275\u0275text"](80,"east"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](81,"div",8),d["\u0275\u0275elementStart"](82,"div",4),d["\u0275\u0275text"](83,"9"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](84,"div",5),d["\u0275\u0275text"](85," Finally submit the quiz using \xa0 "),d["\u0275\u0275elementStart"](86,"button",15),d["\u0275\u0275text"](87,"Submit Quiz"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](88,"div",16),d["\u0275\u0275elementStart"](89,"button",17),d["\u0275\u0275listener"]("click",(function(){return t.closeDialog()})),d["\u0275\u0275text"](90,"Back"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](91,"button",18),d["\u0275\u0275listener"]("click",(function(){return t.startQuiz()})),d["\u0275\u0275text"](92," Start Quiz "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())},directives:[h.a,v.a],styles:[".nxt-btn[_ngcontent-%COMP%]{background-color:#236fe0;color:#fff}.submit-btn[_ngcontent-%COMP%]{color:#fff;background-color:#53c353}.list[_ngcontent-%COMP%]{font-size:16px}"]}),e})()}}]);