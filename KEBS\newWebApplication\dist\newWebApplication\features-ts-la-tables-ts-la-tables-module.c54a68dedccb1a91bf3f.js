(window.webpackJsonp=window.webpackJsonp||[]).push([[692],{qWbu:function(e,t,n){"use strict";n.r(t),n.d(t,"TsLaTablesModule",(function(){return k}));var a=n("ofXK"),i=n("3Pt+"),o=n("+0xr"),l=n("bTqV"),r=n("NFeN"),s=n("Qu3c"),d=n("STbY"),c=n("kmnG"),p=n("qFsG"),m=n("d3UM"),g=n("iadO"),f=n("FKr1"),u=n("jaxi"),h=n("wZkO"),v=n("/1cH"),b=n("Xi0T"),C=n("tyNb"),y=n("fXoL"),S=n("mrSG"),w=n("tk/3"),x=n("BVzC");let E=(()=>{class e{constructor(e,t){this.http=e,this.errorService=t}fetchTableDetails(e,t){return new Promise(n=>{this.http.post("/api/tsPrimary/getDetailsOfTableForDevExt",{oid:e,aid:t}).subscribe(e=>Object(S.c)(this,void 0,void 0,(function*(){let t=[];"S"==e.messType&&e.data.length>0&&(t=e.data),n(t)})),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while searching costcentre data",e&&e.params?e.params:e&&e.error?e.error.params:{})})})}}return e.\u0275fac=function(t){return new(t||e)(y["\u0275\u0275inject"](w.c),y["\u0275\u0275inject"](x.a))},e.\u0275prov=y["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var I=n("XXEo"),M=n("v0+k");function D(e,t){if(1&e&&(y["\u0275\u0275elementStart"](0,"mat-option",9),y["\u0275\u0275text"](1),y["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;y["\u0275\u0275propertyInterpolate"]("matTooltip",e.name),y["\u0275\u0275property"]("value",e),y["\u0275\u0275advance"](1),y["\u0275\u0275textInterpolate1"](" ",e.name," ")}}function _(e,t){if(1&e&&(y["\u0275\u0275elementStart"](0,"div"),y["\u0275\u0275element"](1,"app-data-upload-grid",10),y["\u0275\u0275elementEnd"]()),2&e){const e=y["\u0275\u0275nextContext"]();y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("id",e.selectedTableId)}}const O=[{path:"",component:(()=>{class e{constructor(e,t){this.tsLaTablesService=e,this.authService=t,this.tableList=[],this.selectedTableId=0}ngOnInit(){this.constructDataForTableList()}constructDataForTableList(){this.tsLaTablesService.fetchTableDetails(this.authService.getProfile().profile.oid,this.authService.getProfile().profile.aid).then(e=>{this.tableList=e}),console.log(this.tableList)}fetchTableId(){console.log(this.selectedTable),this.selectedTableId=this.selectedTable.id}}return e.\u0275fac=function(t){return new(t||e)(y["\u0275\u0275directiveInject"](E),y["\u0275\u0275directiveInject"](I.a))},e.\u0275cmp=y["\u0275\u0275defineComponent"]({type:e,selectors:[["app-ts-la-tables-landing-page"]],decls:13,vars:3,consts:[[1,"p-0","container-fluid","ts-la-tables"],[1,"row"],[1,"col-3"],["appearance","outline",2,"width","100%"],["required","",3,"ngModel","ngModelChange"],[3,"value","matTooltip",4,"ngFor","ngForOf"],[1,"col-2"],["mat-flat-button","",1,"class-btn",3,"click"],[4,"ngIf"],[3,"value","matTooltip"],[3,"id"]],template:function(e,t){1&e&&(y["\u0275\u0275elementStart"](0,"div",0),y["\u0275\u0275elementStart"](1,"div",1),y["\u0275\u0275elementStart"](2,"div",2),y["\u0275\u0275elementStart"](3,"mat-form-field",3),y["\u0275\u0275elementStart"](4,"mat-label"),y["\u0275\u0275text"](5,"Select Table"),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](6,"mat-select",4),y["\u0275\u0275listener"]("ngModelChange",(function(e){return t.selectedTable=e})),y["\u0275\u0275template"](7,D,2,3,"mat-option",5),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](8,"div",6),y["\u0275\u0275elementStart"](9,"button",7),y["\u0275\u0275listener"]("click",(function(){return t.fetchTableId()})),y["\u0275\u0275text"](10,"View Table"),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementStart"](11,"div",1),y["\u0275\u0275template"](12,_,2,1,"div",8),y["\u0275\u0275elementEnd"](),y["\u0275\u0275elementEnd"]()),2&e&&(y["\u0275\u0275advance"](6),y["\u0275\u0275property"]("ngModel",t.selectedTable),y["\u0275\u0275advance"](1),y["\u0275\u0275property"]("ngForOf",t.tableList),y["\u0275\u0275advance"](5),y["\u0275\u0275property"]("ngIf",t.selectedTableId>0))},directives:[c.c,c.g,m.c,i.F,i.v,i.y,a.NgForOf,l.a,a.NgIf,f.p,s.a,M.a],styles:[".ts-la-tables[_ngcontent-%COMP%]   .class-btn[_ngcontent-%COMP%]{height:37px;color:#fff;font-size:13px;background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%);border-radius:6px;margin-top:15px!important}"]}),e})()}];let T=(()=>{class e{}return e.\u0275mod=y["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=y["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[C.k.forChild(O)],C.k]}),e})(),k=(()=>{class e{}return e.\u0275mod=y["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=y["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[a.CommonModule,T,i.p,i.E,o.m,l.b,r.b,s.b,p.c,c.e,m.d,d.e,g.h,f.n,u.c,h.g,v.c,b.a]]}),e})()},"v0+k":function(e,t,n){"use strict";n.d(t,"a",(function(){return j}));var a=n("mrSG"),i=n("ySCK"),o=n("3Pt+"),l=n("1yaQ"),r=n("FKr1"),s=n("0IaG"),d=n("fXoL"),c=n("ofXK"),p=n("NFeN"),m=n("Wp6s");function g(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"span",12),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" # ",e.id," - Log History")}}function f(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"span"),d["\u0275\u0275elementStart"](1,"mat-icon",13),d["\u0275\u0275text"](2,"history"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](3,"span",12),d["\u0275\u0275text"](4," Log History "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function u(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",14),d["\u0275\u0275elementStart"](1,"div",15),d["\u0275\u0275elementStart"](2,"span",16),d["\u0275\u0275text"](3,"Loading..."),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function h(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate2"](" ",e.key," : ",e.value," ")}}function v(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",17),d["\u0275\u0275elementStart"](1,"div",18),d["\u0275\u0275elementStart"](2,"mat-card",19),d["\u0275\u0275element"](3,"div",17),d["\u0275\u0275elementStart"](4,"div",20),d["\u0275\u0275elementStart"](5,"div",2),d["\u0275\u0275template"](6,h,2,2,"div",21),d["\u0275\u0275pipe"](7,"keyvalue"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](8,"div",22),d["\u0275\u0275elementStart"](9,"span",23),d["\u0275\u0275text"](10,"By "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](11,"span",23),d["\u0275\u0275text"](12),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](13,"span",23),d["\u0275\u0275text"](14,"on"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](15,"span",23),d["\u0275\u0275text"](16),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;d["\u0275\u0275advance"](6),d["\u0275\u0275property"]("ngForOf",d["\u0275\u0275pipeBind1"](7,3,e.input)),d["\u0275\u0275advance"](6),d["\u0275\u0275textInterpolate"](null==e?null:e.changed_by),d["\u0275\u0275advance"](4),d["\u0275\u0275textInterpolate"](null==e?null:e.changed_on)}}function b(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div"),d["\u0275\u0275elementStart"](1,"div",24),d["\u0275\u0275elementStart"](2,"mat-icon",25),d["\u0275\u0275text"](3,"running_with_errors"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](4,"span",26),d["\u0275\u0275text"](5,"No History Found!"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}let C=(()=>{class e{constructor(e,t){this.dialogRef=e,this.logDetails=t,this.logSpinner=!1,this.logHistoryFound=!1,this.id=null!=t.id?t.id:null}ngOnInit(){return Object(a.c)(this,void 0,void 0,(function*(){this.logSpinner=!1,this.logData=this.logDetails.logDetails,this.logData="string"==typeof this.logData?JSON.parse(this.logData):this.logData,this.logHistoryFound=this.logData.length>0}))}closeLogDialog(){this.dialogRef.close()}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](s.h),d["\u0275\u0275directiveInject"](s.a))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-data-upload-grid-log"]],decls:15,vars:5,consts:[[1,"container-fluid","log-history","mb-3"],[1,"row","p-1","pt-3","pb-3"],[1,"col"],["class","pl-1 heading-top",4,"ngIf"],[4,"ngIf"],[1,"col-1"],[1,"vertical-align:middle",3,"click"],[1,"icon-cancel"],[1,"m-0"],[1,"spinner-class"],["class","pt-4 pb-4 text-center",4,"ngIf"],["class","row",4,"ngFor","ngForOf"],[1,"pl-1","heading-top"],[2,"font-size","21px","color","#cf0001","vertical-align","middle"],[1,"pt-4","pb-4","text-center"],["role","status",1,"spinner-border"],[1,"sr-only"],[1,"row"],[1,"col","p-0"],[1,"mt-4"],[1,"row","log-data"],[4,"ngFor","ngForOf"],[1,"row","d-flex","justify-content-end","pt-1",2,"color","#6E7B8F"],[1,"pr-1"],[1,"row","mt-5","d-flex","justify-content-center"],[1,"no-history-icon"],[1,"pl-2","no-history"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",0),d["\u0275\u0275elementStart"](1,"div",1),d["\u0275\u0275elementStart"](2,"div",2),d["\u0275\u0275template"](3,g,2,1,"span",3),d["\u0275\u0275template"](4,f,5,0,"span",4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](5,"div",5),d["\u0275\u0275elementStart"](6,"span",6),d["\u0275\u0275listener"]("click",(function(){return t.closeLogDialog()})),d["\u0275\u0275elementStart"](7,"mat-icon",7),d["\u0275\u0275text"](8,"close"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](9,"hr",8),d["\u0275\u0275elementStart"](10,"div",9),d["\u0275\u0275template"](11,u,4,0,"div",10),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](12,"div"),d["\u0275\u0275template"](13,v,17,5,"div",11),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](14,b,6,0,"div",4),d["\u0275\u0275elementEnd"]()),2&e&&(d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngIf",t.id),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!t.id),d["\u0275\u0275advance"](7),d["\u0275\u0275property"]("ngIf",1==t.logSpinner),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngForOf",t.logData),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",0==t.logHistoryFound))},directives:[c.NgIf,p.a,c.NgForOf,m.a],pipes:[c.KeyValuePipe],styles:[".log-history[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]{color:#cf0001;font-size:14px}.log-history[_ngcontent-%COMP%]   .heading-top[_ngcontent-%COMP%]{font-family:Roboto;color:#cf0001;font-style:normal;font-weight:600;letter-spacing:.02em;text-transform:capitalize}.log-history[_ngcontent-%COMP%]   .table-heading[_ngcontent-%COMP%]{color:#cf0001;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.log-history[_ngcontent-%COMP%]   .spinner-border[_ngcontent-%COMP%]{color:#cf0001}.log-history[_ngcontent-%COMP%]   .icon-cancel[_ngcontent-%COMP%]{cursor:pointer;color:#a1a1a2;font-size:20px}.log-history[_ngcontent-%COMP%]   .no-history[_ngcontent-%COMP%]{color:#6e7b8f;font-size:18px}.log-history[_ngcontent-%COMP%]   .no-history-icon[_ngcontent-%COMP%]{color:#6e7b8f;font-size:25px;vertical-align:top}.log-history[_ngcontent-%COMP%]   .strike[_ngcontent-%COMP%]{white-space:no-wrap;text-decoration:line-through;overflow:hidden;text-overflow:ellipsis}.log-history[_ngcontent-%COMP%]   .log-data[_ngcontent-%COMP%]{color:#5c5b5b}"]}),e})();var y=n("xG9w"),S=n("wd/R"),w=n.n(S),x=n("XVR1"),E=n("dNgK"),I=n("tyNb"),M=n("ZzPI"),D=n("6t9p"),_=n("kmnG"),O=n("qFsG"),T=n("iadO");function k(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"mat-form-field",16),d["\u0275\u0275elementStart"](1,"mat-label"),d["\u0275\u0275text"](2,"Month-Year"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](3,"input",17),d["\u0275\u0275element"](4,"mat-datepicker-toggle",18),d["\u0275\u0275elementStart"](5,"mat-datepicker",19,20),d["\u0275\u0275listener"]("monthSelected",(function(t){d["\u0275\u0275restoreView"](e);const n=d["\u0275\u0275reference"](6);return d["\u0275\u0275nextContext"](2).setMonthAndYear(t,n)})),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275reference"](6),t=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("matDatepicker",e)("formControl",t.dateControl),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("for",e)}}function F(e,t){if(1&e&&d["\u0275\u0275element"](0,"dxo-lookup",23),2&e){const e=d["\u0275\u0275nextContext"]().$implicit;d["\u0275\u0275property"]("valueExpr",null==e?null:e.valueExpr)("displayExpr",null==e?null:e.displayExpr)("dataSource",null==e?null:e.masterData)}}function P(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"dxi-column",21),d["\u0275\u0275template"](1,F,1,3,"dxo-lookup",22),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=d["\u0275\u0275nextContext"](2);d["\u0275\u0275property"]("dataField",e.valueExpr)("caption",e.caption)("dataType",e.dataType)("allowEditing","true"==e.allowEditing)("width",n.auto)("cellTemplate","changed_log"==e.valueExpr?n.statusCellTemplate:null),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",1==(null==e?null:e.isLoadMasterData))}}function Y(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",1),d["\u0275\u0275elementStart"](1,"div",2),d["\u0275\u0275element"](2,"div",3),d["\u0275\u0275elementStart"](3,"div",4),d["\u0275\u0275elementStart"](4,"span",5),d["\u0275\u0275text"](5),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](6,"div",3),d["\u0275\u0275template"](7,k,7,3,"mat-form-field",6),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](8,"div",7),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](9,"div",8),d["\u0275\u0275elementStart"](10,"dx-data-grid",9),d["\u0275\u0275listener"]("onCellClick",(function(t){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().onColumnNameCellClick(t)}))("onInitNewRow",(function(t){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().onInitiateNewRow(t)}))("onEditingStart",(function(t){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().onEditingStart(t)})),d["\u0275\u0275element"](11,"dxo-filter-row",10),d["\u0275\u0275element"](12,"dxo-header-filter",11),d["\u0275\u0275element"](13,"dxo-search-panel",12),d["\u0275\u0275element"](14,"dxo-paging",13),d["\u0275\u0275element"](15,"dxo-editing",14),d["\u0275\u0275template"](16,P,2,7,"dxi-column",15),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](5),d["\u0275\u0275textInterpolate1"](" ",e.tableConfig.title,""),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",1==e.tableConfig.is_to_get_period_date),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("dataSource",e.adminDataSource)("allowColumnReordering",!0)("columnAutoWidth",!0)("showBorders",!0)("columnHidingEnabled",!1),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("visible",!0)("applyFilter",!0),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("visible",!0),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("visible",!0)("width",240),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("pageSize",10)("pageIndex",0),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("allowUpdating",!0)("allowAdding",e.isInsertAllowed),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngForOf",e.columnConfigData)}}const N=w.a||S,L={parse:{dateInput:"MMMM/YYYY"},display:{dateInput:"MMMM/YYYY",monthYearLabel:"MMMM YYYY",dateA11yLabel:"LL",monthYearA11yLabel:"MMMM YYYY"}};let j=(()=>{class e{constructor(e,t,n,a){this.generalService=e,this.snackBar=t,this._Activatedroute=n,this.openMatDialog=a,this.tableConfig=[],this.columnConfigData=[],this.dateControl=new o.j(N()),this.showMasterData=!1,this.isInitialHit=!0,this.conversion_type={},this.isInsertAllowed=!0,this.clicked=!1,this.statusCellTemplate=(e,t)=>{let n=document.createElement("i");n.className="dx-icon-info",e.appendChild(n)}}ngOnInit(){this.getAdminConfigDetails()}ngOnChanges(){this.getAdminConfigDetails()}getInitialData(){this.tableConfig&&(this.columnConfigData=this.tableConfig.column_config,this.getTableMasterData(this.columnConfigData),this.selectedPeriodMonth=N(this.dateControl.value).format("MM"),this.selectedPeriodYear=N(this.dateControl.value).format("YYYY"),this.adminDataSource={store:new i.a({load:()=>this.generalService.getTableData(this.tableConfig.retrieval_api,{period_month:this.selectedPeriodMonth,period_year:this.selectedPeriodYear,param:this.tableConfig.api_params}).then(e=>e.data),insert:e=>this.generalService.insertTableData(this.tableConfig.insert_api,e,{period_month:this.selectedPeriodMonth,period_year:this.selectedPeriodYear,param:this.tableConfig.api_params}).then(e=>this.snackBar.open(e.message,"Dismiss",{duration:5e3})),update:(e,t)=>{for(let n in t)e[n]=t[n];return this.generalService.updateTableData(this.tableConfig.update_api,e,t,{period_month:this.selectedPeriodMonth,period_year:this.selectedPeriodYear,param:this.tableConfig.api_params}).then(e=>(this.snackBar.open(e.message,"Dismiss",{duration:5e3}),e))}})})}setMonthAndYear(e,t){if(0==this.isInitialHit){const n=this.dateControl.value;n.month(e.month()),n.year(e.year()),console.log("***********************************"),console.log(e.month()),console.log(e.year()),this.dateControl.setValue(n),t.close(),this.getInitialData()}}getTableMasterData(e){return Object(a.c)(this,void 0,void 0,(function*(){let t=e.length;for(let n=0;n<t;n++)if(1==e[n].isLoadMasterData){let t=yield this.generalService.getTableData(e[n].api,1);this.tableConfig.column_config[n].masterData=t.data}this.columnConfigData=this.tableConfig.column_config,this.showMasterData=!0}))}getAdminConfigDetails(){return Object(a.c)(this,void 0,void 0,(function*(){if(this.id){this.isInitialHit=!1;let e=yield this.generalService.getTableConfigData(this.id);if("S"==e.messType){if(0==e.data.length)return this.snackBar.open("MIS data upload table configuration not found !","Dismiss",{duration:5e3});this.tableConfig=e.data[0];let t=JSON.parse(this.tableConfig.column_config);console.log(this.tableConfig.is_insert_allowed),this.isInsertAllowed=0!=this.tableConfig.is_insert_allowed,null!=t?(this.tableConfig.column_config=t,this.getInitialData()):this.snackBar.open("Column configuration not found !","Dismiss",{duration:5e3})}else this.snackBar.open("Error in retrieving table configurations !","Dismiss",{duration:5e3})}else this.snackBar.open("Data upload table configuration not found !","Dismiss",{duration:5e3})}))}onColumnNameCellClick(e){return Object(a.c)(this,void 0,void 0,(function*(){if(console.log(e),null!=e.data.id){let t=y.filter(this.columnConfigData,{isToOpenLogDialog:1});if(console.log(t),t.length>0){let n=t[0];if(console.log(n.valueExpr),console.log(e.column.dataField),n.valueExpr==e.column.dataField){let n=e.data.id,a=yield this.generalService.getApplicationLogs(t[0].api,n);if("E"==a.messType)return this.snackBar.open(a.message,"Dismiss",{duration:5e3});this.clicked||(this.clicked=!0,this.openMatDialog.open(C,{height:"60%",width:"40%",data:{logDetails:a.data[0].changed_log,id:e.data.id}}).afterClosed().subscribe(()=>{this.clicked=!1}))}}}}))}onInitiateNewRow(){y.each(this.columnConfigData,e=>{1==e.disableOnInsert&&(e.allowEditing="false"),0==e.disableOnInsert&&(e.allowEditing="true")})}onEditingStart(){y.each(this.columnConfigData,e=>{1==e.disableOnEdit&&(e.allowEditing="false"),0==e.disableOnEdit&&(e.allowEditing="true")})}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](x.a),d["\u0275\u0275directiveInject"](E.a),d["\u0275\u0275directiveInject"](I.a),d["\u0275\u0275directiveInject"](s.b))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-data-upload-grid"]],inputs:{id:"id"},features:[d["\u0275\u0275ProvidersFeature"]([{provide:r.c,useClass:l.c,deps:[r.f,l.a]},{provide:r.e,useValue:L}]),d["\u0275\u0275NgOnChangesFeature"]],decls:1,vars:1,consts:[["class","container-fluid data-upload-component pt-1",4,"ngIf"],[1,"container-fluid","data-upload-component","pt-1"],[1,"row"],[1,"col-3"],[1,"col"],[2,"font-size","medium","font-weight","500","color","#cf0001"],["appearance","outline","style","width: 80%",4,"ngIf"],[1,"col-1"],[1,"row","header-row"],["id","gridContainer",3,"dataSource","allowColumnReordering","columnAutoWidth","showBorders","columnHidingEnabled","onCellClick","onInitNewRow","onEditingStart"],[3,"visible","applyFilter"],[3,"visible"],["placeholder","Search...",3,"visible","width"],[3,"pageSize","pageIndex"],["mode","row",3,"allowUpdating","allowAdding"],["alignment","left",3,"dataField","caption","dataType","allowEditing","width","cellTemplate",4,"ngFor","ngForOf"],["appearance","outline",2,"width","80%"],["matInput","","placeholder","MMMM-YYYY",3,"matDatepicker","formControl"],["matSuffix","",3,"for"],["startView","multi-year",3,"monthSelected"],["dp",""],["alignment","left",3,"dataField","caption","dataType","allowEditing","width","cellTemplate"],[3,"valueExpr","displayExpr","dataSource",4,"ngIf"],[3,"valueExpr","displayExpr","dataSource"]],template:function(e,t){1&e&&d["\u0275\u0275template"](0,Y,17,17,"div",0),2&e&&d["\u0275\u0275property"]("ngIf",t.tableConfig&&t.columnConfigData)},directives:[c.NgIf,M.a,D.dc,D.Cc,D.Md,D.od,D.Qb,c.NgForOf,_.c,_.g,O.b,T.g,o.e,o.v,o.k,T.i,_.i,T.f,D.g,D.Wc],styles:[".data-upload-component[_ngcontent-%COMP%]     .dx-datagrid-headers .dx-header-row{font-weight:700;color:#000}"]}),e})()}}]);