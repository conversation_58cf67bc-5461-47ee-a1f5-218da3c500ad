(window.webpackJsonp=window.webpackJsonp||[]).push([[687,535,631,634,858],{NJ67:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));class o{constructor(){this.disabled=!1}onChange(e){}onTouched(e){}writeValue(e){this.value=e}registerOnChange(e){this.onChange=e}registerOnTouched(e){this.onTouched=e}setDisabledState(e){this.disabled=e}}},"TmG/":function(e,t,n){"use strict";n.d(t,"a",(function(){return g}));var o=n("fXoL"),l=n("3Pt+"),i=n("jtHE"),r=n("XNiG"),a=n("NJ67"),s=n("1G5W"),c=n("kmnG"),p=n("ofXK"),d=n("d3UM"),u=n("FKr1"),m=n("WJ5W"),h=n("Qu3c");function v(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"mat-label"),o["\u0275\u0275text"](1),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate"](e.placeholder)}}function f(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"mat-option",7),o["\u0275\u0275text"](1),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275property"]("value",null),o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate"](e.disableNone?"Select One":"None")}}function x(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"mat-option",8),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](e);const n=t.$implicit;return o["\u0275\u0275nextContext"]().emitChanges(n)})),o["\u0275\u0275text"](1),o["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;o["\u0275\u0275property"]("value",e.id||e._id)("matTooltip",e.name),o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let g=(()=>{class e extends a.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new l.j,this.fieldFilterCtrl=new l.j,this.list=[],this.required=!1,this.disableNone=!1,this.valueChange=new o.EventEmitter,this.disabled=!1,this.hasNoneOption=!0,this.filteredList=new i.a,this.change=new o.EventEmitter,this.hideMatLabel=!1,this._onDestroy=new r.b,this.emitChanges=e=>{this.change.emit(e)}}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(s.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(s.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](o.Renderer2))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search"]],inputs:{list:"list",placeholder:"placeholder",required:"required",disableNone:"disableNone",disabled:"disabled",hasNoneOption:"hasNoneOption",hideMatLabel:"hideMatLabel"},outputs:{valueChange:"valueChange",change:"change"},features:[o["\u0275\u0275ProvidersFeature"]([{provide:l.t,useExisting:Object(o.forwardRef)(()=>e),multi:!0}]),o["\u0275\u0275InheritDefinitionFeature"],o["\u0275\u0275NgOnChangesFeature"]],decls:9,vars:11,consts:[["appearance","outline"],[4,"ngIf"],[3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value",4,"ngIf"],[3,"value","matTooltip","click",4,"ngFor","ngForOf"],[3,"value"],[3,"value","matTooltip","click"]],template:function(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"mat-form-field",0),o["\u0275\u0275template"](1,v,2,1,"mat-label",1),o["\u0275\u0275elementStart"](2,"mat-select",2,3),o["\u0275\u0275elementStart"](4,"mat-option"),o["\u0275\u0275element"](5,"ngx-mat-select-search",4),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](6,f,2,2,"mat-option",5),o["\u0275\u0275template"](7,x,2,3,"mat-option",6),o["\u0275\u0275pipe"](8,"async"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e&&(o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",!t.hideMatLabel),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",t.hasNoneOption),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngForOf",o["\u0275\u0275pipeBind1"](8,9,t.filteredList)))},directives:[c.c,p.NgIf,d.c,l.v,l.k,l.F,u.p,m.a,p.NgForOf,c.g,h.a],pipes:[p.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%}"]}),e})()},UJ6N:function(e,t,n){"use strict";n.r(t),n.d(t,"TimesheetV2DailyLogReportModule",(function(){return Xo}));var o=n("ofXK"),l=n("tyNb"),i=n("mrSG"),r=n("1yaQ"),a=n("FKr1"),s=n("wd/R"),c=n("xG9w"),p=n("0IaG"),d=n("3Pt+"),u=n("Xi0T"),m=n("NFeN"),h=n("STbY"),v=n("bSwM"),f=n("qFsG"),x=n("kmnG"),g=n("Qu3c"),_=n("fXoL"),y=n("tk/3");let b=(()=>{class e{constructor(e){this._http=e}insertEmailSchedulingConfigurations(e,t,n,o,l,i,r,a,s,c,p,d,u){return this._http.post("api/timesheetv2/reportsMailScheduling/insertEmailSchedulingConfigurations",{applicationId:e,mailTypeConfig:t,attachmentType:n,repeat:o,day:l,monthlyRunAt:i,hours:r,mins:a,createdByAssociateId:s,to:c,cc:p,apiServerUrl:d,payload:u})}getEmployeeNameMasterData(){return this._http.post("api/timesheetv2/Master/getEmployeeNameMasterData",{})}}return e.\u0275fac=function(t){return new(t||e)(_["\u0275\u0275inject"](y.c))},e.\u0275prov=_["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var C=n("1A3m"),S=n("XXEo"),k=n("TmG/"),I=n("me71");const w=function(e,t){return{background:e,color:t}};function E(e,t){if(1&e){const e=_["\u0275\u0275getCurrentView"]();_["\u0275\u0275elementStart"](0,"div",14),_["\u0275\u0275listener"]("click",(function(){_["\u0275\u0275restoreView"](e);const n=t.index;return _["\u0275\u0275nextContext"]().onClickToggle(n)})),_["\u0275\u0275text"](1),_["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=t.index,o=_["\u0275\u0275nextContext"]();_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction2"](2,w,o.selectedToggle==n?"#EE4961":"",o.selectedToggle==n?"#FFF":"")),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",e," ")}}const O=function(e,t){return{color:e,background:t}};function P(e,t){if(1&e){const e=_["\u0275\u0275getCurrentView"]();_["\u0275\u0275elementStart"](0,"div",29),_["\u0275\u0275listener"]("click",(function(){_["\u0275\u0275restoreView"](e);const n=t.index;return _["\u0275\u0275nextContext"](3).onSelectDay(n)})),_["\u0275\u0275text"](1),_["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction2"](2,O,e.isSelected?"#fff":"",e.isSelected?"#ef4a61":"")),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",e.displayValue," ")}}function M(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div"),_["\u0275\u0275elementStart"](1,"p",26),_["\u0275\u0275text"](2,"Every *"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](3,"div",27),_["\u0275\u0275template"](4,P,2,5,"div",28),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2);_["\u0275\u0275advance"](4),_["\u0275\u0275property"]("ngForOf",e.daysList)}}function D(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div"),_["\u0275\u0275elementStart"](1,"p",5),_["\u0275\u0275text"](2,"Monthly Run At *"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275element"](3,"app-input-search",17),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2);_["\u0275\u0275advance"](3),_["\u0275\u0275property"]("list",e.dateList)("hideMatLabel",!0)("formControl",e.date)("disableNone",!0)}}function j(e,t){if(1&e){const e=_["\u0275\u0275getCurrentView"]();_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275elementStart"](1,"div",15),_["\u0275\u0275elementStart"](2,"div",16),_["\u0275\u0275elementStart"](3,"p",5),_["\u0275\u0275text"](4,"Repeat *"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275element"](5,"app-input-search",17),_["\u0275\u0275elementEnd"](),_["\u0275\u0275template"](6,M,5,1,"div",3),_["\u0275\u0275template"](7,D,4,4,"div",3),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](8,"div"),_["\u0275\u0275elementStart"](9,"p",5),_["\u0275\u0275text"](10,"At this time *"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](11,"div",18),_["\u0275\u0275elementStart"](12,"div",19),_["\u0275\u0275elementStart"](13,"input",20),_["\u0275\u0275listener"]("input",(function(t){return _["\u0275\u0275restoreView"](e),_["\u0275\u0275nextContext"]().validate(t)}))("keydown",(function(t){return _["\u0275\u0275restoreView"](e),_["\u0275\u0275nextContext"]().validateNumericInput(t)}))("focus",(function(){_["\u0275\u0275restoreView"](e);const t=_["\u0275\u0275nextContext"]();return t.changeFocus(t.timeUnits.Hours)})),_["\u0275\u0275pipe"](14,"number"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](15,"span",21),_["\u0275\u0275text"](16,":"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](17,"input",22),_["\u0275\u0275listener"]("input",(function(t){return _["\u0275\u0275restoreView"](e),_["\u0275\u0275nextContext"]().validate(t)}))("keydown",(function(t){return _["\u0275\u0275restoreView"](e),_["\u0275\u0275nextContext"]().validateNumericInput(t)}))("focus",(function(){_["\u0275\u0275restoreView"](e);const t=_["\u0275\u0275nextContext"]();return t.changeFocus(t.timeUnits.Minutes)})),_["\u0275\u0275pipe"](18,"number"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](19,"div"),_["\u0275\u0275elementStart"](20,"div",23),_["\u0275\u0275text"](21),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](22,"div",24),_["\u0275\u0275elementStart"](23,"mat-icon",25),_["\u0275\u0275listener"]("click",(function(){return _["\u0275\u0275restoreView"](e),_["\u0275\u0275nextContext"]().changeHoursFormatValue()})),_["\u0275\u0275text"](24,"unfold_more"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](25,"div"),_["\u0275\u0275elementStart"](26,"div",5),_["\u0275\u0275text"](27,"IST"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=_["\u0275\u0275nextContext"]();_["\u0275\u0275advance"](5),_["\u0275\u0275property"]("list",e.repeatList)("hideMatLabel",!0)("formControl",e.repeat)("disableNone",!0),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",2==e.repeat.value),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",3==e.repeat.value),_["\u0275\u0275advance"](6),_["\u0275\u0275property"]("maxlength",3)("ngModel",_["\u0275\u0275pipeBind2"](14,11,e.hours,"2.0-0")),_["\u0275\u0275advance"](4),_["\u0275\u0275property"]("maxlength",3)("ngModel",_["\u0275\u0275pipeBind2"](18,14,e.mins,"2.0-0")),_["\u0275\u0275advance"](4),_["\u0275\u0275textInterpolate1"](" ",e.hoursFormatValue," ")}}const T=function(e){return{"margin-left":e}};function F(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",31),_["\u0275\u0275element"](1,"app-user-image",32),_["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=t.index;_["\u0275\u0275property"]("matTooltip",e.name),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("id",e.oid)("ngStyle",_["\u0275\u0275pureFunction1"](3,T,n>=1?"-4px":""))}}function B(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",18),_["\u0275\u0275template"](1,F,2,5,"div",30),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]();_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngForOf",e.selectedUsers)}}function Y(e,t){if(1&e){const e=_["\u0275\u0275getCurrentView"]();_["\u0275\u0275elementStart"](0,"div",35),_["\u0275\u0275elementStart"](1,"div",36),_["\u0275\u0275elementStart"](2,"mat-checkbox",37),_["\u0275\u0275listener"]("ngModelChange",(function(){_["\u0275\u0275restoreView"](e);const n=t.index;return _["\u0275\u0275nextContext"](2).onCheckboxSelected(n)})),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](3,"div"),_["\u0275\u0275elementStart"](4,"span",38),_["\u0275\u0275text"](5),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](6,"span",39),_["\u0275\u0275text"](7),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngModel",e.isSelected)("name",e.id),_["\u0275\u0275advance"](3),_["\u0275\u0275textInterpolate1"]("",e.name," -"),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" AID ",e.id,"")}}function $(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",33),_["\u0275\u0275listener"]("click",(function(e){return e.stopPropagation()})),_["\u0275\u0275template"](1,Y,8,4,"div",34),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]();_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngForOf",e.employeeList)}}const V=function(e){return{"pointer-events":e}};var A=function(e){return e.Hours="hours",e.Minutes="mins",e}({});let N=(()=>{class e{constructor(e,t,n,o,l){this.data=e,this._dialogRef=t,this.service=n,this.toastService=o,this.authService=l,this.toggleItems=["Send Now","Send Later"],this.selectedToggle=0,this.selectedUsers=[],this.selectedDay=null,this.hours=0,this.mins=0,this.hoursFormatValue="AM",this.timeUnits=A,this.focus=A.Hours,this.isApiInProgress=!1,this.employeeList=[],this.repeatList=[{id:1,name:"Daily"},{id:2,name:"Weekly"},{id:3,name:"Monthly"}],this.dateList=[{id:1,name:"1st of Every Month"},{id:2,name:"15th of Every Month"},{id:3,name:"Last of Every Month"}],this.daysList=[{value:1,displayValue:"M",isSelected:!1},{value:2,displayValue:"T",isSelected:!1},{value:3,displayValue:"W",isSelected:!1},{value:4,displayValue:"T",isSelected:!1},{value:5,displayValue:"F",isSelected:!1},{value:6,displayValue:"S",isSelected:!1},{value:0,displayValue:"S",isSelected:!1}],this.repeat=new d.j,this.date=new d.j,console.log("data",e)}ngOnInit(){return Object(i.c)(this,void 0,void 0,(function*(){this.currentUser=yield this.authService.getProfile().profile,this.oid=this.currentUser.oid,this.aid=this.currentUser.aid,yield this.getEmployeeNameMasterData()}))}onClickToggle(e){this.selectedToggle=e}onClickSend(){return Object(i.c)(this,void 0,void 0,(function*(){(yield this.validateInputs())&&(yield this.insertEmailSchedulingConfigurations(),this._dialogRef.close())}))}onCancel(){this._dialogRef.close()}validateInputs(){return Object(i.c)(this,void 0,void 0,(function*(){return 1==this.selectedToggle?this.data.applicationId?this.data.attachmentType?this.data.mailTypeConfig?this.data.apiServerUrl?this.repeat.value?2==this.repeat.value&&null==this.selectedDay?(this.toastService.showWarning("Kindly Select a Day!",""),!1):3!=this.repeat.value||this.date.value?!this.selectedUsers||0!=this.selectedUsers.length||(this.toastService.showInfo("Kindly Select atleast one Person","",3e3),!1):(this.toastService.showWarning("Monthly Run At Field is Mandatory!",""),!1):(this.toastService.showWarning("Repeat Field is Mandatory!",""),!1):(this.toastService.showInfo("API Server URL is Missing!","",3e3),!1):(this.toastService.showInfo("Mail Type is Missing!","",3e3),!1):(this.toastService.showInfo("Attachment Type is Missing!","",3e3),!1):(this.toastService.showInfo("Application ID is Missing!","",3e3),!1):!this.selectedUsers||0!=this.selectedUsers.length||(this.toastService.showInfo("Kindly Select atleast one Person","",3e3),!1)}))}onCheckboxSelected(e){var t,n;if(this.employeeList[e].isSelected=!(null===(t=this.employeeList[e])||void 0===t?void 0:t.isSelected),null===(n=this.employeeList[e])||void 0===n?void 0:n.isSelected)this.selectedUsers.push(this.employeeList[e]);else{let t=this.employeeList[e].oid;this.selectedUsers=this.selectedUsers.filter(e=>e.oid!=t)}}onSelectDay(e){for(let t=0;t<this.daysList.length;t++)this.daysList[t].isSelected=!1;this.daysList[e].isSelected=!0,this.selectedDay=this.daysList[e].value}changeHoursFormatValue(){this.hoursFormatValue="AM"==this.hoursFormatValue?"PM":"AM"}changeFocus(e){this.focus=e}validate(e){const t=Number(e.target.value);t&&"number"==typeof t||this.resetTime(),this.changeTime(t)}changeTime(e){this[this.focus]=e,this.validateRange()||this.resetTime()}resetTime(){this[this.focus]=null}validateRange(){const e=this[this.focus];return this.focus===A.Hours?e>0&&e<=12:e>0&&e<=59}validateNumericInput(e){const t=/^[0-9]$/g.test(e.key),n=["Backspace","Delete","ArrowLeft","ArrowRight","Home","End"].includes(e.key);t||n||e.preventDefault()}getEmployeeNameMasterData(){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this.service.getEmployeeNameMasterData().subscribe({next:t=>{if("S"==t.messType){let e=t.data.map(e=>Object.assign(Object.assign({},e),{isSelected:!1}));this.employeeList=e}else this.toastService.showWarning("Timesheet App Message",t.messText);e(!0)},error:e=>{this.toastService.showError("Employee Data Retrieval Failed","",6e4),t()}}))}))}insertEmailSchedulingConfigurations(){return Object(i.c)(this,void 0,void 0,(function*(){this.isApiInProgress=!0;let e=1==this.selectedToggle?1==this.repeat.value?"daily":2==this.repeat.value?"weekly":"monthly":"once",t=3==this.repeat.value&&1==this.selectedToggle?this.repeat.value:null,n=2==this.repeat.value&&1==this.selectedToggle?this.selectedDay:null,o=this.hours&&1==this.selectedToggle?"AM"==this.hoursFormatValue&&12==this.hours?0:"PM"==this.hoursFormatValue&&12!=this.hours?this.hours+12:this.hours:0,l=this.mins&&1==this.selectedToggle?this.mins:0;return new Promise((i,r)=>this.service.insertEmailSchedulingConfigurations(this.data.applicationId,this.data.mailTypeConfig,this.data.attachmentType,e,n,t,o,l,this.aid,c.pluck(this.selectedUsers,"id"),null,this.data.apiServerUrl,this.data.payload).subscribe({next:e=>{"S"==e.messType?this.toastService.showSuccess("Timesheet App Message",e.messText,3e3):this.toastService.showWarning("Timesheet App Message",e.messText),this.isApiInProgress=!1,i(!0)},error:e=>{this.toastService.showError("Report Data Retrieval Failed","",6e4),this.isApiInProgress=!1,r()}}))}))}}return e.\u0275fac=function(t){return new(t||e)(_["\u0275\u0275directiveInject"](p.a),_["\u0275\u0275directiveInject"](p.h),_["\u0275\u0275directiveInject"](b),_["\u0275\u0275directiveInject"](C.a),_["\u0275\u0275directiveInject"](S.a))},e.\u0275cmp=_["\u0275\u0275defineComponent"]({type:e,selectors:[["app-schedule-report-modal"]],decls:20,vars:11,consts:[[1,"main-background"],[1,"toggle-switch",2,"margin-bottom","16px"],["class","toggle-switch-button",3,"ngStyle","click",4,"ngFor","ngForOf"],[4,"ngIf"],["style","margin-bottom: 8px","class","align-items-row",4,"ngIf"],[1,"label-text"],[1,"view-type-box",3,"matMenuTriggerFor"],[1,"view-type-text"],[1,"view-type-icon"],[1,"btn",2,"margin-top","16px","padding","0px"],[1,"send-btn",3,"ngStyle","click"],[1,"cancel-btn",3,"ngStyle","click"],["employeePopup","matMenu"],[3,"matMenuContent"],[1,"toggle-switch-button",3,"ngStyle","click"],[1,"align-items-row",2,"align-items","flex-start","margin-bottom","0px"],[2,"margin-right","20px"],["placeholder","Select One",1,"form-field",3,"list","hideMatLabel","formControl","disableNone"],[1,"align-items-row",2,"margin-bottom","8px"],[1,"custom-time-picker"],["placeholder","00",1,"hours",3,"maxlength","ngModel","input","keydown","focus"],[1,"divider"],["placeholder","00",1,"minutes",3,"maxlength","ngModel","input","keydown","focus"],[1,"label-text",2,"margin-left","6px"],[2,"margin-top","6px"],[1,"expand-icon",3,"click"],[1,"label-text",2,"margin-bottom","8px"],[1,"align-items-row","m-0"],["class","rounded-circle",3,"ngStyle","click",4,"ngFor","ngForOf"],[1,"rounded-circle",3,"ngStyle","click"],[3,"matTooltip",4,"ngFor","ngForOf"],[3,"matTooltip"],["imgWidth","24px","imgHeight","24px",3,"id","ngStyle"],[1,"employee-popup",3,"click"],["class","align-items-center",4,"ngFor","ngForOf"],[1,"align-items-center"],[2,"height","16px"],["width","16px","height","16px",3,"ngModel","name","ngModelChange"],[1,"emp-name"],[1,"emp-id"]],template:function(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",0),_["\u0275\u0275elementStart"](1,"div",1),_["\u0275\u0275template"](2,E,2,5,"div",2),_["\u0275\u0275elementEnd"](),_["\u0275\u0275template"](3,j,28,17,"ng-container",3),_["\u0275\u0275template"](4,B,2,1,"div",4),_["\u0275\u0275elementStart"](5,"p",5),_["\u0275\u0275text"](6,"Select People To Send Report"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](7,"div",6),_["\u0275\u0275elementStart"](8,"p",7),_["\u0275\u0275text"](9),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](10,"mat-icon",8),_["\u0275\u0275text"](11,"expand_more"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](12,"div",9),_["\u0275\u0275elementStart"](13,"div",10),_["\u0275\u0275listener"]("click",(function(){return t.onClickSend()})),_["\u0275\u0275text"](14," Send "),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](15,"div",11),_["\u0275\u0275listener"]("click",(function(){return t.onCancel()})),_["\u0275\u0275text"](16," Cancel "),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](17,"mat-menu",null,12),_["\u0275\u0275template"](19,$,2,1,"ng-template",13),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275reference"](18);_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngForOf",t.toggleItems),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",1==t.selectedToggle),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",t.selectedUsers.length>0),_["\u0275\u0275advance"](3),_["\u0275\u0275property"]("matMenuTriggerFor",e),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",t.selectedUsers&&0==t.selectedUsers.length?"Select One":"Selected "+t.selectedUsers.length+" Person"," "),_["\u0275\u0275advance"](4),_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](7,V,t.isApiInProgress?"none":"")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](9,V,t.isApiInProgress?"none":""))}},directives:[o.NgForOf,o.NgIf,h.f,m.a,o.NgStyle,h.g,h.c,k.a,d.v,d.k,d.e,d.q,d.y,g.a,I.a,v.a],pipes:[o.DecimalPipe],styles:[".main-background[_ngcontent-%COMP%]{padding:14px;overflow:hidden}.align-items-row[_ngcontent-%COMP%]{display:flex;align-items:center;margin-bottom:16px}.align-items-row-center[_ngcontent-%COMP%]{display:flex;align-items:center;flex-direction:column}.icon-gap[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;margin-left:6px}.expand-icon[_ngcontent-%COMP%]{font-size:18px;color:#111434;cursor:pointer}.toggle-switch[_ngcontent-%COMP%]{display:flex;width:-moz-fit-content;width:fit-content;gap:8px;padding:4px;border-radius:8px;border:1px solid #dadce2}.toggle-switch-button[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;padding:8px;font-size:14px;font-weight:500;color:#8b95a5;border-radius:4px;cursor:pointer}.btn[_ngcontent-%COMP%]{display:flex;gap:24px}.send-btn[_ngcontent-%COMP%]{color:#fff;border-radius:4px;padding:8px;background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%)}.cancel-btn[_ngcontent-%COMP%], .send-btn[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;font-size:14px;font-weight:700;width:80px;cursor:pointer}.cancel-btn[_ngcontent-%COMP%]{color:#45546e;padding:7px;border:1px solid #45546e;border-radius:4px}.label-text[_ngcontent-%COMP%]{font-size:12px;font-weight:500;color:#6e7b8f;margin-bottom:0}.view-type-box[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;width:100%;padding:8px 12px;border-radius:4px;border:1px solid #8b95a5;background:#fff;cursor:pointer}.view-type-text[_ngcontent-%COMP%]{color:#5f6c81;font-size:12px;font-weight:400;margin-bottom:0}.view-type-icon[_ngcontent-%COMP%]{height:16px;width:16px;font-size:16px;color:#5f6c81}.employee-popup[_ngcontent-%COMP%]{padding:8px 16px;overflow-x:hidden!important;overflow-y:scroll!important;height:184px;width:517px}.employee-popup[_ngcontent-%COMP%]     .mat-checkbox-checked.mat-accent .mat-checkbox-background{background-color:#f27a6c!important}.employee-popup[_ngcontent-%COMP%]   .align-items-center[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;height:25px}.employee-popup[_ngcontent-%COMP%]   .emp-name[_ngcontent-%COMP%]{font-size:12px;font-weight:400;color:#111434}.employee-popup[_ngcontent-%COMP%]   .emp-id[_ngcontent-%COMP%]{font-size:12px;font-weight:400;color:#7d838b}  .mat-menu-panel{min-width:0!important;min-height:0!important;max-width:none!important;max-height:none!important}.form-field[_ngcontent-%COMP%]{font-size:12px!important;font-weight:400!important;color:#5f6c81!important;width:100%;display:block}.rounded-circle[_ngcontent-%COMP%]{width:25px;height:25px;border-radius:50%;color:#6e7b8f;font-weight:700;background-color:#fff;margin-right:5px;display:flex;justify-content:center;align-items:center;border:1px solid #6e7b8f;cursor:pointer;font-size:12px}.border-box[_ngcontent-%COMP%]{border:1px solid #45546e;padding:4px;width:50px;text-align:center;margin-bottom:8px}.custom-time-picker[_ngcontent-%COMP%]{height:30px;display:flex;align-items:center;color:#111434}.custom-time-picker[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{height:100%;padding:0 5px;position:relative;font-size:13px;color:#111434;border:1px solid #8b95a5;outline:none}.custom-time-picker[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{color:inherit}.custom-time-picker[_ngcontent-%COMP%]   .hours[_ngcontent-%COMP%]{width:30px;text-align:right;border-right:none;border-radius:3px 0 0 3px}.custom-time-picker[_ngcontent-%COMP%]   .minutes[_ngcontent-%COMP%]{width:25px;padding-left:1px;border-left:none;border-radius:0 3px 3px 0}.custom-time-picker[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]{height:100%;margin-left:-4px;margin-bottom:1px;display:flex;align-items:center;font-size:13px;color:inherit;z-index:1;-webkit-user-select:none;user-select:none}"]}),e})();var R=n("JIr8"),z=n("nYR2"),L=n("z6cu");let G=(()=>{class e{constructor(e){this._http=e}getBasicTimesheetConfigurations(e,t){return this._http.post("api/timesheetv2/Settings/getBasicTimesheetConfigurations",{oid:e,aid:t})}getTimesheetQueryReportItemData(e,t,n,o,l,i,r,a,s,c,p){return this._http.post("api/tsNodeV2Primary/getTimesheetQueryReportItemData",{aid:e,oid:t,startDate:n,endDate:o,limit:l,viewType:i,filters:r,groupBy:a,preference:s,filterConfig:c,isForDownload:p})}getReportConfigurationsBasedOnId(e){return this._http.post("api/timesheetv2/Master/getReportConfigurationsBasedOnId",{applicationId:e})}downloadTimesheetCalendarReport(e,t,n,o,l,i,r,a,s,c,p,d){return this._http.post("api/tsNodeV2Primary/downloadTimesheetCalendarReport",{aid:e,oid:t,startDate:n,endDate:o,limit:l,viewType:i,filters:r,groupBy:a,preference:s,filterConfig:c,isForDownload:p,columnConfig:d},{responseType:"blob"})}getTimesheetSummaryData(e,t,n,o,l,i,r,a,s,c,p){return this._http.post("api/tsNodeV2Primary/getTimesheetSummaryData",{aid:e,oid:t,startDate:n,endDate:o,limit:l,viewType:i,filters:r,groupBy:a,preference:s,filterConfig:c,isForDownload:p})}getTimesheetReportAccess(e){return this._http.post("/api/timesheetv2/Reports/getTimesheetReportAccess",{applicationId:e})}}return e.\u0275fac=function(t){return new(t||e)(_["\u0275\u0275inject"](y.c))},e.\u0275prov=_["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var U=n("GnQ3"),H=n("HmYF"),W=n("iadO"),Q=n("dlKe");let X=(()=>{class e{transform(e,t){return 1==t?s(e.date).format("dddd - DD MMM YYYY"):2==t?"W"+e.week_number+" "+s(e.week_start_date).format("MMM DD")+" - "+s(e.week_end_date).format("MMM DD"):3==t?e.month:"No Data"}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=_["\u0275\u0275definePipe"]({name:"dateFormat",type:e,pure:!0}),e})(),q=(()=>{class e{transform(e,t){var n,o;return!!e&&(null===(n=e.find(e=>e.item==t))||void 0===n?void 0:n.isActive)&&(null===(o=e.find(e=>e.item==t))||void 0===o?void 0:o.isCheckBoxSelected)}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=_["\u0275\u0275definePipe"]({name:"reportActivePipe",type:e,pure:!1}),e})(),K=(()=>{class e{transform(e,t){if(e&&e.length>0){if(1==t){let t=e.reduce((e,t)=>{var n,o,l,i;return e+=(60*parseInt((null===(n=null==t?void 0:t.total_hours)||void 0===n?void 0:n.slice(0,null===(o=null==t?void 0:t.total_hours)||void 0===o?void 0:o.indexOf("h")))||0)||0)+(parseInt((null===(l=null==t?void 0:t.total_hours)||void 0===l?void 0:l.slice((null===(i=null==t?void 0:t.total_hours)||void 0===i?void 0:i.indexOf("h"))+2))||0)||0),t.tasks&&t.tasks.length&&(e+=t.tasks.reduce((e,t)=>{var n,o,l,i;return e+(60*parseInt((null===(n=null==t?void 0:t.total_hours)||void 0===n?void 0:n.slice(0,null===(o=null==t?void 0:t.total_hours)||void 0===o?void 0:o.indexOf("h")))||0)||0)+(parseInt((null===(l=null==t?void 0:t.total_hours)||void 0===l?void 0:l.slice((null===(i=null==t?void 0:t.total_hours)||void 0===i?void 0:i.indexOf("h"))+2))||0)||0)},0)),t.sub_project&&t.sub_project.length&&(e+=t.sub_project.map(e=>e.tasks.reduce((e,t)=>{var n,o,l,i;return e+(60*parseInt((null===(n=null==t?void 0:t.total_hours)||void 0===n?void 0:n.slice(0,null===(o=null==t?void 0:t.total_hours)||void 0===o?void 0:o.indexOf("h")))||0)||0)+(parseInt((null===(l=null==t?void 0:t.total_hours)||void 0===l?void 0:l.slice((null===(i=null==t?void 0:t.total_hours)||void 0===i?void 0:i.indexOf("h"))+2))||0)||0)},0)).reduce((e,t)=>e+t,0)),e},0);return Math.floor(t/60).toString().padStart(2,"0")+"h "+(t%60).toString().padStart(2,"0")+"m"}if(2==t){let t=e.reduce((e,t)=>{var n,o,l,i;return e+=(60*parseInt((null===(n=null==t?void 0:t.total_billable_hours)||void 0===n?void 0:n.slice(0,null===(o=null==t?void 0:t.total_billable_hours)||void 0===o?void 0:o.indexOf("h")))||0)||0)+(parseInt((null===(l=null==t?void 0:t.total_billable_hours)||void 0===l?void 0:l.slice((null===(i=null==t?void 0:t.total_billable_hours)||void 0===i?void 0:i.indexOf("h"))+2))||0)||0),t.tasks&&t.tasks.length&&(e+=t.tasks.reduce((e,t)=>{var n,o,l,i;return e+(60*parseInt((null===(n=null==t?void 0:t.total_billable_hours)||void 0===n?void 0:n.slice(0,null===(o=null==t?void 0:t.total_billable_hours)||void 0===o?void 0:o.indexOf("h")))||0)||0)+(parseInt((null===(l=null==t?void 0:t.total_billable_hours)||void 0===l?void 0:l.slice((null===(i=null==t?void 0:t.total_billable_hours)||void 0===i?void 0:i.indexOf("h"))+2))||0)||0)},0)),t.sub_project&&t.sub_project.length&&(e+=t.sub_project.map(e=>e.tasks.reduce((e,t)=>{var n,o,l,i;return e+(60*parseInt((null===(n=null==t?void 0:t.total_billable_hours)||void 0===n?void 0:n.slice(0,null===(o=null==t?void 0:t.total_billable_hours)||void 0===o?void 0:o.indexOf("h")))||0)||0)+(parseInt((null===(l=null==t?void 0:t.total_billable_hours)||void 0===l?void 0:l.slice((null===(i=null==t?void 0:t.total_billable_hours)||void 0===i?void 0:i.indexOf("h"))+2))||0)||0)},0)).reduce((e,t)=>e+t,0)),e},0);return Math.floor(t/60).toString().padStart(2,"0")+"h "+(t%60).toString().padStart(2,"0")+"m"}if(3==t){let t=e.reduce((e,t)=>{var n,o,l,i;return e+=(60*parseInt((null===(n=null==t?void 0:t.total_non_billable_hours)||void 0===n?void 0:n.slice(0,null===(o=null==t?void 0:t.total_non_billable_hours)||void 0===o?void 0:o.indexOf("h")))||0)||0)+(parseInt((null===(l=null==t?void 0:t.total_non_billable_hours)||void 0===l?void 0:l.slice((null===(i=null==t?void 0:t.total_non_billable_hours)||void 0===i?void 0:i.indexOf("h"))+2))||0)||0),t.tasks&&t.tasks.length&&(e+=t.tasks.reduce((e,t)=>{var n,o,l,i;return e+(60*parseInt((null===(n=null==t?void 0:t.total_non_billable_hours)||void 0===n?void 0:n.slice(0,null===(o=null==t?void 0:t.total_non_billable_hours)||void 0===o?void 0:o.indexOf("h")))||0)||0)+(parseInt((null===(l=null==t?void 0:t.total_non_billable_hours)||void 0===l?void 0:l.slice((null===(i=null==t?void 0:t.total_non_billable_hours)||void 0===i?void 0:i.indexOf("h"))+2))||0)||0)},0)),t.sub_project&&t.sub_project.length&&(e+=t.sub_project.map(e=>e.tasks.reduce((e,t)=>{var n,o,l,i;return e+(60*parseInt((null===(n=null==t?void 0:t.total_non_billable_hours)||void 0===n?void 0:n.slice(0,null===(o=null==t?void 0:t.total_non_billable_hours)||void 0===o?void 0:o.indexOf("h")))||0)||0)+(parseInt((null===(l=null==t?void 0:t.total_non_billable_hours)||void 0===l?void 0:l.slice((null===(i=null==t?void 0:t.total_non_billable_hours)||void 0===i?void 0:i.indexOf("h"))+2))||0)||0)},0)).reduce((e,t)=>e+t,0)),e},0);return Math.floor(t/60).toString().padStart(2,"0")+"h "+(t%60).toString().padStart(2,"0")+"m"}if(4==t){let t=e.reduce((e,t)=>{var n,o,l,i;return e+=(60*parseInt((null===(n=null==t?void 0:t.total_overtime_hours)||void 0===n?void 0:n.slice(0,null===(o=null==t?void 0:t.total_overtime_hours)||void 0===o?void 0:o.indexOf("h")))||0)||0)+(parseInt((null===(l=null==t?void 0:t.total_overtime_hours)||void 0===l?void 0:l.slice((null===(i=null==t?void 0:t.total_overtime_hours)||void 0===i?void 0:i.indexOf("h"))+2))||0)||0),t.tasks&&t.tasks.length&&(e+=t.tasks.reduce((e,t)=>{var n,o,l,i;return e+(60*parseInt((null===(n=null==t?void 0:t.total_overtime_hours)||void 0===n?void 0:n.slice(0,null===(o=null==t?void 0:t.total_overtime_hours)||void 0===o?void 0:o.indexOf("h")))||0)||0)+(parseInt((null===(l=null==t?void 0:t.total_overtime_hours)||void 0===l?void 0:l.slice((null===(i=null==t?void 0:t.total_overtime_hours)||void 0===i?void 0:i.indexOf("h"))+2))||0)||0)},0)),t.sub_project&&t.sub_project.length&&(e+=t.sub_project.map(e=>e.tasks.reduce((e,t)=>{var n,o,l,i;return e+(60*parseInt((null===(n=null==t?void 0:t.total_overtime_hours)||void 0===n?void 0:n.slice(0,null===(o=null==t?void 0:t.total_overtime_hours)||void 0===o?void 0:o.indexOf("h")))||0)||0)+(parseInt((null===(l=null==t?void 0:t.total_overtime_hours)||void 0===l?void 0:l.slice((null===(i=null==t?void 0:t.total_overtime_hours)||void 0===i?void 0:i.indexOf("h"))+2))||0)||0)},0)).reduce((e,t)=>e+t,0)),e},0);return Math.floor(t/60).toString().padStart(2,"0")+"h "+(t%60).toString().padStart(2,"0")+"m"}return"00h 00m"}return"00h 00m"}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=_["\u0275\u0275definePipe"]({name:"hoursCalcPerColumn",type:e,pure:!0}),e})(),J=(()=>{class e{transform(e,t,n,o){if(e&&e.length>0){let l=[],i=0;return-1==n&&-1==o?(l=e.map(e=>{var n,o,l,i,r,a,s,c;let p=0,d=0,u=(60*parseInt((null===(o=null===(n=e.costcenter[t])||void 0===n?void 0:n.total_hours)||void 0===o?void 0:o.slice(0,null===(i=null===(l=e.costcenter[t])||void 0===l?void 0:l.total_hours)||void 0===i?void 0:i.indexOf("h")))||0)||0)+(parseInt((null===(a=null===(r=e.costcenter[t])||void 0===r?void 0:r.total_hours)||void 0===a?void 0:a.slice((null===(c=null===(s=e.costcenter[t])||void 0===s?void 0:s.total_hours)||void 0===c?void 0:c.indexOf("h"))+2))||0)||0);return e.costcenter[t].sub_project&&e.costcenter[t].sub_project.length&&(d=e.costcenter[t].sub_project.map(e=>e.tasks.reduce((e,t)=>{var n,o,l,i;return e+(60*parseInt((null===(n=null==t?void 0:t.total_hours)||void 0===n?void 0:n.slice(0,null===(o=null==t?void 0:t.total_hours)||void 0===o?void 0:o.indexOf("h")))||0)||0)+(parseInt((null===(l=null==t?void 0:t.total_hours)||void 0===l?void 0:l.slice((null===(i=null==t?void 0:t.total_hours)||void 0===i?void 0:i.indexOf("h"))+2))||0)||0)},0)).reduce((e,t)=>e+t,0)),e.costcenter[t].tasks&&e.costcenter[t].tasks.length&&(p=e.costcenter[t].tasks.reduce((e,t)=>{var n,o,l,i;return e+(60*parseInt((null===(n=null==t?void 0:t.total_hours)||void 0===n?void 0:n.slice(0,null===(o=null==t?void 0:t.total_hours)||void 0===o?void 0:o.indexOf("h")))||0)||0)+(parseInt((null===(l=null==t?void 0:t.total_hours)||void 0===l?void 0:l.slice((null===(i=null==t?void 0:t.total_hours)||void 0===i?void 0:i.indexOf("h"))+2))||0)||0)},0)),{totalHoursFromCostCenter:u,totalHoursFromTasks:p,totalHoursFromWorkstreamTasks:d}}),i=l.reduce((e,t)=>e+t.totalHoursFromCostCenter+t.totalHoursFromTasks+t.totalHoursFromWorkstreamTasks,0)):-1==n&&-1!=o?(l=e.map(e=>{var n,l,i,r,a,s,c,p;return(60*parseInt((null===(l=null===(n=e.costcenter[t].tasks[o])||void 0===n?void 0:n.total_hours)||void 0===l?void 0:l.slice(0,null===(r=null===(i=e.costcenter[t].tasks[o])||void 0===i?void 0:i.total_hours)||void 0===r?void 0:r.indexOf("h")))||0)||0)+(parseInt((null===(s=null===(a=e.costcenter[t].tasks[o])||void 0===a?void 0:a.total_hours)||void 0===s?void 0:s.slice((null===(p=null===(c=e.costcenter[t].tasks[o])||void 0===c?void 0:c.total_hours)||void 0===p?void 0:p.indexOf("h"))+2))||0)||0)}),i=l.reduce((e,t)=>e+t,0)):-1!=n&&-1!=o?(l=e.map(e=>{var l,i,r,a,s,c,p,d;return(60*parseInt((null===(i=null===(l=e.costcenter[t].sub_project[n].tasks[o])||void 0===l?void 0:l.total_hours)||void 0===i?void 0:i.slice(0,null===(a=null===(r=e.costcenter[t].sub_project[n].tasks[o])||void 0===r?void 0:r.total_hours)||void 0===a?void 0:a.indexOf("h")))||0)||0)+(parseInt((null===(c=null===(s=e.costcenter[t].sub_project[n].tasks[o])||void 0===s?void 0:s.total_hours)||void 0===c?void 0:c.slice((null===(d=null===(p=e.costcenter[t].sub_project[n].tasks[o])||void 0===p?void 0:p.total_hours)||void 0===d?void 0:d.indexOf("h"))+2))||0)||0)}),i=l.reduce((e,t)=>e+t,0)):-1!=n&&-1==o&&(l=e.map(e=>e.costcenter[t].sub_project[n].tasks.map(e=>{var t,n,o,l;return(60*parseInt((null===(t=null==e?void 0:e.total_hours)||void 0===t?void 0:t.slice(0,null===(n=null==e?void 0:e.total_hours)||void 0===n?void 0:n.indexOf("h")))||0)||0)+(parseInt((null===(o=null==e?void 0:e.total_hours)||void 0===o?void 0:o.slice((null===(l=null==e?void 0:e.total_hours)||void 0===l?void 0:l.indexOf("h"))+2))||0)||0)})),l=[].concat(...l),i=l.reduce((e,t)=>e+t,0)),Math.floor(i/60).toString().padStart(2,"0")+"h "+(i%60).toString().padStart(2,"0")+"m"}return"00h 00m"}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=_["\u0275\u0275definePipe"]({name:"hoursCalcPerCc",type:e,pure:!0}),e})(),Z=(()=>{class e{transform(e,t,n,o,l){if(e&&e.length>0){let i=[],r=0;return 1==l?-1==n&&-1==o?(i=e.map(e=>{let n=0,o=0,l=e.costcenter[t].leave_id?1:0;return e.costcenter[t].sub_project&&e.costcenter[t].sub_project.length&&(o=e.costcenter[t].sub_project.map(e=>e.tasks.reduce((e,t)=>e+(t.leave_id?1:0),0)).reduce((e,t)=>e+t,0)),e.costcenter[t].tasks&&e.costcenter[t].tasks.length&&(n=e.costcenter[t].tasks.reduce((e,t)=>e+(t.leave_id?1:0),0)),{totalLeavesFromCostCenter:l,totalLeavesFromTasks:n,totalLeavesFromWorkstreamTasks:o}}),r=i.reduce((e,t)=>e+t.totalLeavesFromCostCenter+t.totalLeavesFromTasks+t.totalLeavesFromWorkstreamTasks,0)):-1==n&&-1!=o?(i=e.map(e=>e.costcenter[t].tasks[o].leave_id?1:0),r=i.reduce((e,t)=>e+t,0)):-1!=n&&-1!=o?(i=e.map(e=>e.costcenter[t].sub_project[n].tasks[o].leave_id?1:0),r=i.reduce((e,t)=>e+t,0)):-1!=n&&-1==o&&(i=e.map(e=>e.costcenter[t].sub_project[n].tasks.map(e=>e.leave_id?1:0)),i=[].concat(...i),r=i.reduce((e,t)=>e+t,0)):(i=e.map(e=>{var n,o;return(null===(n=null==e?void 0:e.costcenter[t])||void 0===n?void 0:n.total_leaves)?null===(o=null==e?void 0:e.costcenter[t])||void 0===o?void 0:o.total_leaves:0}),r=i.reduce((e,t)=>e+t,0)),r}return 0}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=_["\u0275\u0275definePipe"]({name:"leavesCalcPerCc",type:e,pure:!0}),e})(),ee=(()=>{class e{transform(e,t){return`rgba(${parseInt(e.slice(1,3),16)}, ${parseInt(e.slice(3,5),16)}, ${parseInt(e.slice(5,7),16)}, ${t})`}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=_["\u0275\u0275definePipe"]({name:"hexToRgb",type:e,pure:!0}),e})();function te(e,t){1&e&&(_["\u0275\u0275elementStart"](0,"p",8),_["\u0275\u0275text"](1,"Leaves"),_["\u0275\u0275elementEnd"]())}function ne(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",9),_["\u0275\u0275text"](1),_["\u0275\u0275pipe"](2,"dateFormat"),_["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=_["\u0275\u0275nextContext"]();_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind2"](2,1,e,n.currentViewType)," ")}}function oe(e,t){if(1&e){const e=_["\u0275\u0275getCurrentView"]();_["\u0275\u0275elementStart"](0,"mat-icon",19),_["\u0275\u0275listener"]("click",(function(){_["\u0275\u0275restoreView"](e);const t=_["\u0275\u0275nextContext"]().$implicit;return t.expanded=!t.expanded})),_["\u0275\u0275text"](1,"indeterminate_check_box"),_["\u0275\u0275elementEnd"]()}}function le(e,t){if(1&e){const e=_["\u0275\u0275getCurrentView"]();_["\u0275\u0275elementStart"](0,"mat-icon",19),_["\u0275\u0275listener"]("click",(function(){_["\u0275\u0275restoreView"](e);const t=_["\u0275\u0275nextContext"]().$implicit;return t.expanded=!t.expanded})),_["\u0275\u0275text"](1,"add_box"),_["\u0275\u0275elementEnd"]()}}function ie(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",20),_["\u0275\u0275elementStart"](1,"p",21),_["\u0275\u0275text"](2),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("matTooltip",(null!=e&&e.total_leaves?e.total_leaves:"0")+" Days"),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null!=e&&e.total_leaves?e.total_leaves:"0"," Days ")}}function re(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",25),_["\u0275\u0275elementStart"](1,"p",28),_["\u0275\u0275text"](2,"Billable"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](3,"p",29),_["\u0275\u0275pipe"](4,"hoursCalcPerColumn"),_["\u0275\u0275text"](5),_["\u0275\u0275pipe"](6,"hoursCalcPerColumn"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275advance"](3),_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBind2"](4,2,e.costcenter,2)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind2"](6,5,e.costcenter,2)," ")}}function ae(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",25),_["\u0275\u0275elementStart"](1,"p",30),_["\u0275\u0275text"](2,"Non-Billable"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](3,"p",31),_["\u0275\u0275pipe"](4,"hoursCalcPerColumn"),_["\u0275\u0275text"](5),_["\u0275\u0275pipe"](6,"hoursCalcPerColumn"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275advance"](3),_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBind2"](4,2,e.costcenter,3)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind2"](6,5,e.costcenter,3)," ")}}function se(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",25),_["\u0275\u0275elementStart"](1,"p",32),_["\u0275\u0275text"](2,"Overtime"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](3,"p",33),_["\u0275\u0275pipe"](4,"hoursCalcPerColumn"),_["\u0275\u0275text"](5),_["\u0275\u0275pipe"](6,"hoursCalcPerColumn"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275advance"](3),_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBind2"](4,2,e.costcenter,4)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind2"](6,5,e.costcenter,4)," ")}}function ce(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",23),_["\u0275\u0275template"](1,re,7,8,"div",24),_["\u0275\u0275pipe"](2,"reportActivePipe"),_["\u0275\u0275template"](3,ae,7,8,"div",24),_["\u0275\u0275pipe"](4,"reportActivePipe"),_["\u0275\u0275template"](5,se,7,8,"div",24),_["\u0275\u0275pipe"](6,"reportActivePipe"),_["\u0275\u0275elementStart"](7,"div",25),_["\u0275\u0275elementStart"](8,"p",26),_["\u0275\u0275text"](9,"Logged Hours"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](10,"p",27),_["\u0275\u0275pipe"](11,"hoursCalcPerColumn"),_["\u0275\u0275text"](12),_["\u0275\u0275pipe"](13,"hoursCalcPerColumn"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=_["\u0275\u0275nextContext"](3);_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](2,5,n.columns,"billable")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](4,8,n.columns,"nonBillable")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](6,11,n.columns,"overtime")),_["\u0275\u0275advance"](5),_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBind2"](11,14,e.costcenter,1)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind2"](13,17,e.costcenter,1)," ")}}function pe(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275template"](1,ce,14,20,"div",22),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngForOf",null==e?null:e.days)}}function de(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",37),_["\u0275\u0275pipe"](1,"hoursCalcPerColumn"),_["\u0275\u0275text"](2),_["\u0275\u0275pipe"](3,"hoursCalcPerColumn"),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBind2"](1,2,e.costcenter,2)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind2"](3,5,e.costcenter,2)," ")}}function ue(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",38),_["\u0275\u0275pipe"](1,"hoursCalcPerColumn"),_["\u0275\u0275text"](2),_["\u0275\u0275pipe"](3,"hoursCalcPerColumn"),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBind2"](1,2,e.costcenter,3)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind2"](3,5,e.costcenter,3)," ")}}function me(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",39),_["\u0275\u0275pipe"](1,"hoursCalcPerColumn"),_["\u0275\u0275text"](2),_["\u0275\u0275pipe"](3,"hoursCalcPerColumn"),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBind2"](1,2,e.costcenter,4)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind2"](3,5,e.costcenter,4)," ")}}function he(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",23),_["\u0275\u0275template"](1,de,4,8,"p",34),_["\u0275\u0275pipe"](2,"reportActivePipe"),_["\u0275\u0275template"](3,ue,4,8,"p",35),_["\u0275\u0275pipe"](4,"reportActivePipe"),_["\u0275\u0275template"](5,me,4,8,"p",36),_["\u0275\u0275pipe"](6,"reportActivePipe"),_["\u0275\u0275elementStart"](7,"p",27),_["\u0275\u0275pipe"](8,"hoursCalcPerColumn"),_["\u0275\u0275text"](9),_["\u0275\u0275pipe"](10,"hoursCalcPerColumn"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=_["\u0275\u0275nextContext"](3);_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](2,5,n.columns,"billable")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](4,8,n.columns,"nonBillable")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](6,11,n.columns,"overtime")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBind2"](8,14,e.costcenter,1)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind2"](10,17,e.costcenter,1)," ")}}function ve(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275template"](1,he,11,20,"div",22),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngForOf",null==e?null:e.days)}}const fe=function(e){return{"border-bottom":e}};function xe(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",11),_["\u0275\u0275elementStart"](1,"div",42),_["\u0275\u0275elementStart"](2,"p",47),_["\u0275\u0275text"](3),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=t.index,o=_["\u0275\u0275nextContext"](2).$implicit;_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](3,fe,(null==o?null:o.tasks.length)-1!=n?"1px solid #f1f3f8;":""))("matTooltip",null!=e&&e.task_name?e.task_name:"NA"),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null!=e&&e.task_name?e.task_name:"NA"," ")}}function ge(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275template"](1,xe,4,5,"div",46),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngForOf",null==e?null:e.tasks)}}function _e(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",11),_["\u0275\u0275elementStart"](1,"div",42),_["\u0275\u0275elementStart"](2,"p",47),_["\u0275\u0275text"](3),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=t.index,o=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](3,fe,(null==o?null:o.tasks.length)-1!=n?"1px solid #f1f3f8;":""))("matTooltip",null!=e&&e.task_name?e.task_name:"NA"),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null!=e&&e.task_name?e.task_name:"NA"," ")}}function ye(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275elementStart"](1,"div",11),_["\u0275\u0275elementStart"](2,"div",42),_["\u0275\u0275elementStart"](3,"p",48),_["\u0275\u0275text"](4),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275template"](5,_e,4,5,"div",46),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;_["\u0275\u0275advance"](3),_["\u0275\u0275property"]("matTooltip",null!=e&&e.sub_project_name?e.sub_project_name:"NA"),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null!=e&&e.sub_project_name?e.sub_project_name:"NA"," "),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngForOf",null==e?null:e.tasks)}}function be(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275template"](1,ye,6,3,"ng-container",7),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngForOf",null==e?null:e.sub_project)}}function Ce(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",15),_["\u0275\u0275elementStart"](1,"div",42),_["\u0275\u0275elementStart"](2,"p",50),_["\u0275\u0275pipe"](3,"hoursCalcPerCc"),_["\u0275\u0275text"](4),_["\u0275\u0275pipe"](5,"hoursCalcPerCc"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=t.index,n=_["\u0275\u0275nextContext"](2),o=n.$implicit,l=n.index,i=_["\u0275\u0275nextContext"](2).index,r=_["\u0275\u0275nextContext"]();_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](13,fe,(null==o?null:o.tasks.length)-1!=e?"1px solid #f1f3f8;":""))("matTooltip",_["\u0275\u0275pipeBind4"](3,3,null==r.logData[i]?null:r.logData[i].days,l,-1,e)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind4"](5,8,null==r.logData[i]?null:r.logData[i].days,l,-1,e)," ")}}function Se(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275template"](1,Ce,6,15,"div",49),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngForOf",null==e?null:e.tasks)}}function ke(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",15),_["\u0275\u0275elementStart"](1,"div",42),_["\u0275\u0275elementStart"](2,"p",50),_["\u0275\u0275pipe"](3,"hoursCalcPerCc"),_["\u0275\u0275text"](4),_["\u0275\u0275pipe"](5,"hoursCalcPerCc"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=t.index,n=_["\u0275\u0275nextContext"](2),o=n.$implicit,l=n.index,i=_["\u0275\u0275nextContext"](2).index,r=_["\u0275\u0275nextContext"](2).index,a=_["\u0275\u0275nextContext"]();_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](13,fe,(null==o?null:o.tasks.length)-1!=e?"1px solid #f1f3f8;":""))("matTooltip",_["\u0275\u0275pipeBind4"](3,3,null==a.logData[r]?null:a.logData[r].days,i,l,e)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind4"](5,8,null==a.logData[r]?null:a.logData[r].days,i,l,e)," ")}}function Ie(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275template"](1,ke,6,15,"div",49),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngForOf",null==e?null:e.tasks)}}function we(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275elementStart"](1,"div",15),_["\u0275\u0275elementStart"](2,"div",42),_["\u0275\u0275elementStart"](3,"p",16),_["\u0275\u0275pipe"](4,"hoursCalcPerCc"),_["\u0275\u0275text"](5),_["\u0275\u0275pipe"](6,"hoursCalcPerCc"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275template"](7,Ie,2,1,"ng-container",18),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit,n=t.index,o=_["\u0275\u0275nextContext"](2).index,l=_["\u0275\u0275nextContext"](2).index,i=_["\u0275\u0275nextContext"]();_["\u0275\u0275advance"](3),_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBind4"](4,3,null==i.logData[l]?null:i.logData[l].days,o,n,-1)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind4"](6,8,null==i.logData[l]?null:i.logData[l].days,o,n,-1)," "),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",(null==e?null:e.tasks)&&(null==e?null:e.tasks.length))}}function Ee(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275template"](1,we,8,13,"ng-container",7),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngForOf",null==e?null:e.sub_project)}}function Oe(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",20),_["\u0275\u0275elementStart"](1,"div",42),_["\u0275\u0275elementStart"](2,"p",53),_["\u0275\u0275text"](3," - "),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=t.index,n=_["\u0275\u0275nextContext"](3).$implicit;_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](1,fe,(null==n?null:n.tasks.length)-1!=e?"1px solid #f1f3f8;":""))}}function Pe(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275template"](1,Oe,4,3,"div",52),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2).$implicit;_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngForOf",null==e?null:e.tasks)}}function Me(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",20),_["\u0275\u0275elementStart"](1,"div",42),_["\u0275\u0275elementStart"](2,"p",53),_["\u0275\u0275text"](3," - "),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=t.index,n=_["\u0275\u0275nextContext"](2).$implicit;_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](1,fe,(null==n?null:n.tasks.length)-1!=e?"1px solid #f1f3f8;":""))}}function De(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275template"](1,Me,4,3,"div",52),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngForOf",null==e?null:e.tasks)}}function je(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275elementStart"](1,"div",20),_["\u0275\u0275elementStart"](2,"div",42),_["\u0275\u0275elementStart"](3,"p",54),_["\u0275\u0275text"](4,"-"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275template"](5,De,2,1,"ng-container",18),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;_["\u0275\u0275advance"](5),_["\u0275\u0275property"]("ngIf",(null==e?null:e.tasks)&&(null==e?null:e.tasks.length))}}function Te(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275template"](1,je,6,1,"ng-container",7),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2).$implicit;_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngForOf",null==e?null:e.sub_project)}}const Fe=function(e,t,n,o,l){return[e,t,n,o,l]};function Be(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",51),_["\u0275\u0275elementStart"](1,"div",20),_["\u0275\u0275elementStart"](2,"div",42),_["\u0275\u0275elementStart"](3,"p",21),_["\u0275\u0275pipe"](4,"leavesCalcPerCc"),_["\u0275\u0275text"](5),_["\u0275\u0275pipe"](6,"leavesCalcPerCc"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275template"](7,Pe,2,1,"ng-container",18),_["\u0275\u0275template"](8,Te,2,1,"ng-container",18),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](),t=e.index,n=e.$implicit,o=_["\u0275\u0275nextContext"](2).index,l=_["\u0275\u0275nextContext"]();_["\u0275\u0275advance"](3),_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBindV"](4,4,_["\u0275\u0275pureFunction5"](16,Fe,null==l.logData[o]?null:l.logData[o].days,t,-1,-1,l.currentViewType))+" Days"),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBindV"](6,10,_["\u0275\u0275pureFunction5"](22,Fe,null==l.logData[o]?null:l.logData[o].days,t,-1,-1,l.currentViewType))," Days "),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",(null==n?null:n.tasks)&&(null==n?null:n.tasks.length)),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",(null==n?null:n.sub_project)&&(null==n?null:n.sub_project.length))}}const Ye=function(e){return[e]};function $e(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",37),_["\u0275\u0275pipe"](1,"hoursCalcPerColumn"),_["\u0275\u0275text"](2),_["\u0275\u0275pipe"](3,"hoursCalcPerColumn"),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2).$implicit,t=_["\u0275\u0275nextContext"]().index;_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBind2"](1,2,_["\u0275\u0275pureFunction1"](8,Ye,null==e?null:e.costcenter[t]),2)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind2"](3,5,_["\u0275\u0275pureFunction1"](10,Ye,null==e?null:e.costcenter[t]),2)," ")}}function Ve(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",38),_["\u0275\u0275pipe"](1,"hoursCalcPerColumn"),_["\u0275\u0275text"](2),_["\u0275\u0275pipe"](3,"hoursCalcPerColumn"),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2).$implicit,t=_["\u0275\u0275nextContext"]().index;_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBind2"](1,2,_["\u0275\u0275pureFunction1"](8,Ye,null==e?null:e.costcenter[t]),3)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind2"](3,5,_["\u0275\u0275pureFunction1"](10,Ye,null==e?null:e.costcenter[t]),3)," ")}}function Ae(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",39),_["\u0275\u0275pipe"](1,"hoursCalcPerColumn"),_["\u0275\u0275text"](2),_["\u0275\u0275pipe"](3,"hoursCalcPerColumn"),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2).$implicit,t=_["\u0275\u0275nextContext"]().index;_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBind2"](1,2,_["\u0275\u0275pureFunction1"](8,Ye,null==e?null:e.costcenter[t]),4)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind2"](3,5,_["\u0275\u0275pureFunction1"](10,Ye,null==e?null:e.costcenter[t]),4)," ")}}function Ne(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",23),_["\u0275\u0275template"](1,$e,4,12,"p",34),_["\u0275\u0275pipe"](2,"reportActivePipe"),_["\u0275\u0275template"](3,Ve,4,12,"p",35),_["\u0275\u0275pipe"](4,"reportActivePipe"),_["\u0275\u0275template"](5,Ae,4,12,"p",36),_["\u0275\u0275pipe"](6,"reportActivePipe"),_["\u0275\u0275elementStart"](7,"p",27),_["\u0275\u0275pipe"](8,"hoursCalcPerColumn"),_["\u0275\u0275text"](9),_["\u0275\u0275pipe"](10,"hoursCalcPerColumn"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().$implicit,t=_["\u0275\u0275nextContext"]().index,n=_["\u0275\u0275nextContext"](3);_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](2,5,n.columns,"billable")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](4,8,n.columns,"nonBillable")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](6,11,n.columns,"overtime")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBind2"](8,14,_["\u0275\u0275pureFunction1"](20,Ye,null==e?null:e.costcenter[t]),1)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind2"](10,17,_["\u0275\u0275pureFunction1"](22,Ye,null==e?null:e.costcenter[t]),1)," ")}}const Re=function(e,t){return{color:e,"background-color":t}};function ze(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",58),_["\u0275\u0275pipe"](1,"hexToRgb"),_["\u0275\u0275text"](2),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().$implicit,t=_["\u0275\u0275nextContext"]().index;_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction2"](5,Re,null==e||null==e.costcenter[t]?null:e.costcenter[t].leave_color_code,_["\u0275\u0275pipeBind2"](1,2,null==e||null==e.costcenter[t]?null:e.costcenter[t].leave_color_code,.1))),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",null!=e&&null!=e.costcenter[t]&&e.costcenter[t].leave_name?null==e||null==e.costcenter[t]?null:e.costcenter[t].leave_name:"NA"," ")}}function Le(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",37),_["\u0275\u0275pipe"](1,"hoursCalcPerColumn"),_["\u0275\u0275text"](2),_["\u0275\u0275pipe"](3,"hoursCalcPerColumn"),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2).$implicit,t=_["\u0275\u0275nextContext"]().index;_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBind2"](1,2,_["\u0275\u0275pureFunction1"](8,Ye,null==e?null:e.costcenter[t]),2)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind2"](3,5,_["\u0275\u0275pureFunction1"](10,Ye,null==e?null:e.costcenter[t]),2)," ")}}function Ge(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",38),_["\u0275\u0275pipe"](1,"hoursCalcPerColumn"),_["\u0275\u0275text"](2),_["\u0275\u0275pipe"](3,"hoursCalcPerColumn"),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2).$implicit,t=_["\u0275\u0275nextContext"]().index;_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBind2"](1,2,_["\u0275\u0275pureFunction1"](8,Ye,null==e?null:e.costcenter[t]),3)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind2"](3,5,_["\u0275\u0275pureFunction1"](10,Ye,null==e?null:e.costcenter[t]),3)," ")}}function Ue(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",39),_["\u0275\u0275pipe"](1,"hoursCalcPerColumn"),_["\u0275\u0275text"](2),_["\u0275\u0275pipe"](3,"hoursCalcPerColumn"),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2).$implicit,t=_["\u0275\u0275nextContext"]().index;_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBind2"](1,2,_["\u0275\u0275pureFunction1"](8,Ye,null==e?null:e.costcenter[t]),4)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind2"](3,5,_["\u0275\u0275pureFunction1"](10,Ye,null==e?null:e.costcenter[t]),4)," ")}}function He(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",59),_["\u0275\u0275elementStart"](1,"div",60),_["\u0275\u0275pipe"](2,"hexToRgb"),_["\u0275\u0275text"](3),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](4,"div",61),_["\u0275\u0275template"](5,Le,4,12,"p",34),_["\u0275\u0275pipe"](6,"reportActivePipe"),_["\u0275\u0275template"](7,Ge,4,12,"p",35),_["\u0275\u0275pipe"](8,"reportActivePipe"),_["\u0275\u0275template"](9,Ue,4,12,"p",36),_["\u0275\u0275pipe"](10,"reportActivePipe"),_["\u0275\u0275elementStart"](11,"p",27),_["\u0275\u0275pipe"](12,"hoursCalcPerColumn"),_["\u0275\u0275text"](13),_["\u0275\u0275pipe"](14,"hoursCalcPerColumn"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().$implicit,t=_["\u0275\u0275nextContext"]().index,n=_["\u0275\u0275nextContext"](3);_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction2"](25,Re,null==e||null==e.costcenter[t]?null:e.costcenter[t].leave_color_code,_["\u0275\u0275pipeBind2"](2,7,null==e||null==e.costcenter[t]?null:e.costcenter[t].leave_color_code,.1))),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",null!=e&&null!=e.costcenter[t]&&e.costcenter[t].leave_name?null==e||null==e.costcenter[t]?null:e.costcenter[t].leave_name:"NA"," "),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](6,10,n.columns,"billable")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](8,13,n.columns,"nonBillable")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](10,16,n.columns,"overtime")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBind2"](12,19,_["\u0275\u0275pureFunction1"](28,Ye,null==e?null:e.costcenter[t]),1)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind2"](14,22,_["\u0275\u0275pureFunction1"](30,Ye,null==e?null:e.costcenter[t]),1)," ")}}function We(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",66),_["\u0275\u0275text"](1),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2).index,t=_["\u0275\u0275nextContext"](2).$implicit,n=_["\u0275\u0275nextContext"](),o=n.$implicit,l=n.index;_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](3,fe,(null==o?null:o.tasks.length)-1!=e?"1px solid #f1f3f8;":""))("matTooltip",null==t||null==t.costcenter[l]||null==t.costcenter[l].tasks[e]?null:t.costcenter[l].tasks[e].total_billable_hours),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null!=t&&null!=t.costcenter[l]&&null!=t.costcenter[l].tasks[e]&&t.costcenter[l].tasks[e].total_billable_hours?null==t||null==t.costcenter[l]||null==t.costcenter[l].tasks[e]?null:t.costcenter[l].tasks[e].total_billable_hours:"00h 00m"," ")}}function Qe(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",67),_["\u0275\u0275text"](1),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2).index,t=_["\u0275\u0275nextContext"](2).$implicit,n=_["\u0275\u0275nextContext"](),o=n.$implicit,l=n.index;_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](3,fe,(null==o?null:o.tasks.length)-1!=e?"1px solid #f1f3f8;":""))("matTooltip",null==t||null==t.costcenter[l]||null==t.costcenter[l].tasks[e]?null:t.costcenter[l].tasks[e].total_non_billable_hours),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null!=t&&null!=t.costcenter[l]&&null!=t.costcenter[l].tasks[e]&&t.costcenter[l].tasks[e].total_non_billable_hours?null==t||null==t.costcenter[l]||null==t.costcenter[l].tasks[e]?null:t.costcenter[l].tasks[e].total_non_billable_hours:"00h 00m"," ")}}function Xe(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",68),_["\u0275\u0275text"](1),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2).index,t=_["\u0275\u0275nextContext"](2).$implicit,n=_["\u0275\u0275nextContext"](),o=n.$implicit,l=n.index;_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](3,fe,(null==o?null:o.tasks.length)-1!=e?"1px solid #f1f3f8;":""))("matTooltip",null==t||null==t.costcenter[l]||null==t.costcenter[l].tasks[e]?null:t.costcenter[l].tasks[e].total_overtime_hours),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null!=t&&null!=t.costcenter[l]&&null!=t.costcenter[l].tasks[e]&&t.costcenter[l].tasks[e].total_overtime_hours?null==t||null==t.costcenter[l]||null==t.costcenter[l].tasks[e]?null:t.costcenter[l].tasks[e].total_overtime_hours:"00h 00m"," ")}}function qe(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",23),_["\u0275\u0275template"](1,We,2,5,"p",62),_["\u0275\u0275pipe"](2,"reportActivePipe"),_["\u0275\u0275template"](3,Qe,2,5,"p",63),_["\u0275\u0275pipe"](4,"reportActivePipe"),_["\u0275\u0275template"](5,Xe,2,5,"p",64),_["\u0275\u0275pipe"](6,"reportActivePipe"),_["\u0275\u0275elementStart"](7,"p",65),_["\u0275\u0275text"](8),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().index,t=_["\u0275\u0275nextContext"](2).$implicit,n=_["\u0275\u0275nextContext"](),o=n.$implicit,l=n.index,i=_["\u0275\u0275nextContext"](3);_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](2,6,i.columns,"billable")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](4,9,i.columns,"nonBillable")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](6,12,i.columns,"overtime")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](15,fe,(null==o?null:o.tasks.length)-1!=e?"1px solid #f1f3f8;":""))("matTooltip",null==t||null==t.costcenter[l]||null==t.costcenter[l].tasks[e]?null:t.costcenter[l].tasks[e].total_hours),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null!=t&&null!=t.costcenter[l]&&null!=t.costcenter[l].tasks[e]&&t.costcenter[l].tasks[e].total_hours?null==t||null==t.costcenter[l]||null==t.costcenter[l].tasks[e]?null:t.costcenter[l].tasks[e].total_hours:"00h 00m"," ")}}function Ke(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",58),_["\u0275\u0275pipe"](1,"hexToRgb"),_["\u0275\u0275text"](2),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().index,t=_["\u0275\u0275nextContext"](2).$implicit,n=_["\u0275\u0275nextContext"]().index;_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction2"](5,Re,null==t||null==t.costcenter[n]||null==t.costcenter[n].tasks[e]?null:t.costcenter[n].tasks[e].leave_color_code,_["\u0275\u0275pipeBind2"](1,2,null==t||null==t.costcenter[n]||null==t.costcenter[n].tasks[e]?null:t.costcenter[n].tasks[e].leave_color_code,.1))),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",null!=t&&null!=t.costcenter[n]&&null!=t.costcenter[n].tasks[e]&&t.costcenter[n].tasks[e].leave_name?null==t||null==t.costcenter[n]||null==t.costcenter[n].tasks[e]?null:t.costcenter[n].tasks[e].leave_name:"NA"," ")}}function Je(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",66),_["\u0275\u0275text"](1),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2).index,t=_["\u0275\u0275nextContext"](2).$implicit,n=_["\u0275\u0275nextContext"](),o=n.$implicit,l=n.index;_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](3,fe,(null==o?null:o.tasks.length)-1!=e?"1px solid #f1f3f8;":""))("matTooltip",null==t||null==t.costcenter[l]||null==t.costcenter[l].tasks[e]?null:t.costcenter[l].tasks[e].total_billable_hours),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null!=t&&null!=t.costcenter[l]&&null!=t.costcenter[l].tasks[e]&&t.costcenter[l].tasks[e].total_billable_hours?null==t||null==t.costcenter[l]||null==t.costcenter[l].tasks[e]?null:t.costcenter[l].tasks[e].total_billable_hours:"00h 00m"," ")}}function Ze(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",67),_["\u0275\u0275text"](1),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2).index,t=_["\u0275\u0275nextContext"](2).$implicit,n=_["\u0275\u0275nextContext"](),o=n.$implicit,l=n.index;_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](3,fe,(null==o?null:o.tasks.length)-1!=e?"1px solid #f1f3f8;":""))("matTooltip",null==t||null==t.costcenter[l]||null==t.costcenter[l].tasks[e]?null:t.costcenter[l].tasks[e].total_non_billable_hours),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null!=t&&null!=t.costcenter[l]&&null!=t.costcenter[l].tasks[e]&&t.costcenter[l].tasks[e].total_non_billable_hours?null==t||null==t.costcenter[l]||null==t.costcenter[l].tasks[e]?null:t.costcenter[l].tasks[e].total_non_billable_hours:"00h 00m"," ")}}function et(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",68),_["\u0275\u0275text"](1),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2).index,t=_["\u0275\u0275nextContext"](2).$implicit,n=_["\u0275\u0275nextContext"](),o=n.$implicit,l=n.index;_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](3,fe,(null==o?null:o.tasks.length)-1!=e?"1px solid #f1f3f8;":""))("matTooltip",null==t||null==t.costcenter[l]||null==t.costcenter[l].tasks[e]?null:t.costcenter[l].tasks[e].total_overtime_hours),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null!=t&&null!=t.costcenter[l]&&null!=t.costcenter[l].tasks[e]&&t.costcenter[l].tasks[e].total_overtime_hours?null==t||null==t.costcenter[l]||null==t.costcenter[l].tasks[e]?null:t.costcenter[l].tasks[e].total_overtime_hours:"00h 00m"," ")}}function tt(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",59),_["\u0275\u0275elementStart"](1,"div",60),_["\u0275\u0275pipe"](2,"hexToRgb"),_["\u0275\u0275text"](3),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](4,"div",61),_["\u0275\u0275template"](5,Je,2,5,"p",62),_["\u0275\u0275pipe"](6,"reportActivePipe"),_["\u0275\u0275template"](7,Ze,2,5,"p",63),_["\u0275\u0275pipe"](8,"reportActivePipe"),_["\u0275\u0275template"](9,et,2,5,"p",64),_["\u0275\u0275pipe"](10,"reportActivePipe"),_["\u0275\u0275elementStart"](11,"p",65),_["\u0275\u0275text"](12),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().index,t=_["\u0275\u0275nextContext"](2).$implicit,n=_["\u0275\u0275nextContext"](),o=n.index,l=n.$implicit,i=_["\u0275\u0275nextContext"](3);_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction2"](20,Re,null==t||null==t.costcenter[o]||null==t.costcenter[o].tasks[e]?null:t.costcenter[o].tasks[e].leave_color_code,_["\u0275\u0275pipeBind2"](2,8,null==t||null==t.costcenter[o]||null==t.costcenter[o].tasks[e]?null:t.costcenter[o].tasks[e].leave_color_code,.1))),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",null!=t&&null!=t.costcenter[o]&&null!=t.costcenter[o].tasks[e]&&t.costcenter[o].tasks[e].leave_name?null==t||null==t.costcenter[o]||null==t.costcenter[o].tasks[e]?null:t.costcenter[o].tasks[e].leave_name:"NA"," "),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](6,11,i.columns,"billable")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](8,14,i.columns,"nonBillable")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](10,17,i.columns,"overtime")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](23,fe,(null==l?null:l.tasks.length)-1!=e?"1px solid #f1f3f8;":""))("matTooltip",null==t||null==t.costcenter[o]||null==t.costcenter[o].tasks[e]?null:t.costcenter[o].tasks[e].total_hours),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null!=t&&null!=t.costcenter[o]&&null!=t.costcenter[o].tasks[e]&&t.costcenter[o].tasks[e].total_hours?null==t||null==t.costcenter[o]||null==t.costcenter[o].tasks[e]?null:t.costcenter[o].tasks[e].total_hours:"00h 00m"," ")}}function nt(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275template"](1,qe,9,17,"div",55),_["\u0275\u0275template"](2,Ke,3,8,"div",56),_["\u0275\u0275template"](3,tt,13,25,"div",57),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.index,n=_["\u0275\u0275nextContext"](2).$implicit,o=_["\u0275\u0275nextContext"]().index;_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",!(null!=n&&null!=n.costcenter[o]&&null!=n.costcenter[o].tasks[e]&&n.costcenter[o].tasks[e].leave_id)),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",(null==n||null==n.costcenter[o]||null==n.costcenter[o].tasks[e]?null:n.costcenter[o].tasks[e].leave_id)&&"00h 00m"==(null==n||null==n.costcenter[o]||null==n.costcenter[o].tasks[e]?null:n.costcenter[o].tasks[e].total_hours)),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",(null==n||null==n.costcenter[o]||null==n.costcenter[o].tasks[e]?null:n.costcenter[o].tasks[e].leave_id)&&"00h 00m"!=(null==n||null==n.costcenter[o]||null==n.costcenter[o].tasks[e]?null:n.costcenter[o].tasks[e].total_hours))}}function ot(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275template"](1,nt,4,3,"ng-container",7),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2).$implicit;_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngForOf",null==e?null:e.tasks)}}function lt(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",37),_["\u0275\u0275pipe"](1,"hoursCalcPerColumn"),_["\u0275\u0275text"](2),_["\u0275\u0275pipe"](3,"hoursCalcPerColumn"),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().index,t=_["\u0275\u0275nextContext"](2).$implicit,n=_["\u0275\u0275nextContext"]().index;_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBind2"](1,2,_["\u0275\u0275pureFunction1"](8,Ye,null==t||null==t.costcenter[n]?null:t.costcenter[n].sub_project[e]),2)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind2"](3,5,_["\u0275\u0275pureFunction1"](10,Ye,null==t||null==t.costcenter[n]?null:t.costcenter[n].sub_project[e]),2)," ")}}function it(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",38),_["\u0275\u0275pipe"](1,"hoursCalcPerColumn"),_["\u0275\u0275text"](2),_["\u0275\u0275pipe"](3,"hoursCalcPerColumn"),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().index,t=_["\u0275\u0275nextContext"](2).$implicit,n=_["\u0275\u0275nextContext"]().index;_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBind2"](1,2,_["\u0275\u0275pureFunction1"](8,Ye,null==t||null==t.costcenter[n]?null:t.costcenter[n].sub_project[e]),3)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind2"](3,5,_["\u0275\u0275pureFunction1"](10,Ye,null==t||null==t.costcenter[n]?null:t.costcenter[n].sub_project[e]),3)," ")}}function rt(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",39),_["\u0275\u0275pipe"](1,"hoursCalcPerColumn"),_["\u0275\u0275text"](2),_["\u0275\u0275pipe"](3,"hoursCalcPerColumn"),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().index,t=_["\u0275\u0275nextContext"](2).$implicit,n=_["\u0275\u0275nextContext"]().index;_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBind2"](1,2,_["\u0275\u0275pureFunction1"](8,Ye,null==t||null==t.costcenter[n]?null:t.costcenter[n].sub_project[e]),4)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind2"](3,5,_["\u0275\u0275pureFunction1"](10,Ye,null==t||null==t.costcenter[n]?null:t.costcenter[n].sub_project[e]),4)," ")}}function at(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",66),_["\u0275\u0275text"](1),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2).index,t=_["\u0275\u0275nextContext"](),n=t.$implicit,o=t.index,l=_["\u0275\u0275nextContext"](2).$implicit,i=_["\u0275\u0275nextContext"]().index;_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](3,fe,(null==n?null:n.tasks.length)-1!=e?"1px solid #f1f3f8;":""))("matTooltip",null==l||null==l.costcenter[i]||null==l.costcenter[i].sub_project[o]||null==l.costcenter[i].sub_project[o].tasks[e]?null:l.costcenter[i].sub_project[o].tasks[e].total_billable_hours),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null!=l&&null!=l.costcenter[i]&&null!=l.costcenter[i].sub_project[o]&&null!=l.costcenter[i].sub_project[o].tasks[e]&&l.costcenter[i].sub_project[o].tasks[e].total_billable_hours?null==l||null==l.costcenter[i]||null==l.costcenter[i].sub_project[o]||null==l.costcenter[i].sub_project[o].tasks[e]?null:l.costcenter[i].sub_project[o].tasks[e].total_billable_hours:"00h 00m"," ")}}function st(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",67),_["\u0275\u0275text"](1),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2).index,t=_["\u0275\u0275nextContext"](),n=t.$implicit,o=t.index,l=_["\u0275\u0275nextContext"](2).$implicit,i=_["\u0275\u0275nextContext"]().index;_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](3,fe,(null==n?null:n.tasks.length)-1!=e?"1px solid #f1f3f8;":""))("matTooltip",null==l||null==l.costcenter[i]||null==l.costcenter[i].sub_project[o]||null==l.costcenter[i].sub_project[o].tasks[e]?null:l.costcenter[i].sub_project[o].tasks[e].total_non_billable_hours),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null!=l&&null!=l.costcenter[i]&&null!=l.costcenter[i].sub_project[o]&&null!=l.costcenter[i].sub_project[o].tasks[e]&&l.costcenter[i].sub_project[o].tasks[e].total_non_billable_hours?null==l||null==l.costcenter[i]||null==l.costcenter[i].sub_project[o]||null==l.costcenter[i].sub_project[o].tasks[e]?null:l.costcenter[i].sub_project[o].tasks[e].total_non_billable_hours:"00h 00m"," ")}}function ct(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",68),_["\u0275\u0275text"](1),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2).index,t=_["\u0275\u0275nextContext"](),n=t.$implicit,o=t.index,l=_["\u0275\u0275nextContext"](2).$implicit,i=_["\u0275\u0275nextContext"]().index;_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](3,fe,(null==n?null:n.tasks.length)-1!=e?"1px solid #f1f3f8;":""))("matTooltip",null==l||null==l.costcenter[i]||null==l.costcenter[i].sub_project[o]||null==l.costcenter[i].sub_project[o].tasks[e]?null:l.costcenter[i].sub_project[o].tasks[e].total_overtime_hours),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null!=l&&null!=l.costcenter[i]&&null!=l.costcenter[i].sub_project[o]&&null!=l.costcenter[i].sub_project[o].tasks[e]&&l.costcenter[i].sub_project[o].tasks[e].total_overtime_hours?null==l||null==l.costcenter[i]||null==l.costcenter[i].sub_project[o]||null==l.costcenter[i].sub_project[o].tasks[e]?null:l.costcenter[i].sub_project[o].tasks[e].total_overtime_hours:"00h 00m"," ")}}function pt(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",23),_["\u0275\u0275template"](1,at,2,5,"p",62),_["\u0275\u0275pipe"](2,"reportActivePipe"),_["\u0275\u0275template"](3,st,2,5,"p",63),_["\u0275\u0275pipe"](4,"reportActivePipe"),_["\u0275\u0275template"](5,ct,2,5,"p",64),_["\u0275\u0275pipe"](6,"reportActivePipe"),_["\u0275\u0275elementStart"](7,"p",65),_["\u0275\u0275text"](8),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().index,t=_["\u0275\u0275nextContext"](),n=t.$implicit,o=t.index,l=_["\u0275\u0275nextContext"](2).$implicit,i=_["\u0275\u0275nextContext"]().index,r=_["\u0275\u0275nextContext"](3);_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](2,6,r.columns,"billable")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](4,9,r.columns,"nonBillable")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](6,12,r.columns,"overtime")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](15,fe,(null==n?null:n.tasks.length)-1!=e?"1px solid #f1f3f8;":""))("matTooltip",null==l||null==l.costcenter[i]||null==l.costcenter[i].sub_project[o]||null==l.costcenter[i].sub_project[o].tasks[e]?null:l.costcenter[i].sub_project[o].tasks[e].total_hours),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null!=l&&null!=l.costcenter[i]&&null!=l.costcenter[i].sub_project[o]&&null!=l.costcenter[i].sub_project[o].tasks[e]&&l.costcenter[i].sub_project[o].tasks[e].total_hours?null==l||null==l.costcenter[i]||null==l.costcenter[i].sub_project[o]||null==l.costcenter[i].sub_project[o].tasks[e]?null:l.costcenter[i].sub_project[o].tasks[e].total_hours:"00h 00m"," ")}}function dt(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",58),_["\u0275\u0275pipe"](1,"hexToRgb"),_["\u0275\u0275text"](2),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().index,t=_["\u0275\u0275nextContext"]().index,n=_["\u0275\u0275nextContext"](2).$implicit,o=_["\u0275\u0275nextContext"]().index;_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction2"](5,Re,null==n||null==n.costcenter[o]||null==n.costcenter[o].sub_project[t]||null==n.costcenter[o].sub_project[t].tasks[e]?null:n.costcenter[o].sub_project[t].tasks[e].leave_color_code,_["\u0275\u0275pipeBind2"](1,2,null==n||null==n.costcenter[o]||null==n.costcenter[o].sub_project[t]||null==n.costcenter[o].sub_project[t].tasks[e]?null:n.costcenter[o].sub_project[t].tasks[e].leave_color_code,.1))),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",null!=n&&null!=n.costcenter[o]&&null!=n.costcenter[o].sub_project[t]&&null!=n.costcenter[o].sub_project[t].tasks[e]&&n.costcenter[o].sub_project[t].tasks[e].leave_name?null==n||null==n.costcenter[o]||null==n.costcenter[o].sub_project[t]||null==n.costcenter[o].sub_project[t].tasks[e]?null:n.costcenter[o].sub_project[t].tasks[e].leave_name:"NA"," ")}}function ut(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",66),_["\u0275\u0275text"](1),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2).index,t=_["\u0275\u0275nextContext"](),n=t.$implicit,o=t.index,l=_["\u0275\u0275nextContext"](2).$implicit,i=_["\u0275\u0275nextContext"]().index;_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](3,fe,(null==n?null:n.tasks.length)-1!=e?"1px solid #f1f3f8;":""))("matTooltip",null==l||null==l.costcenter[i]||null==l.costcenter[i].sub_project[o]||null==l.costcenter[i].sub_project[o].tasks[e]?null:l.costcenter[i].sub_project[o].tasks[e].total_billable_hours),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null!=l&&null!=l.costcenter[i]&&null!=l.costcenter[i].sub_project[o]&&null!=l.costcenter[i].sub_project[o].tasks[e]&&l.costcenter[i].sub_project[o].tasks[e].total_billable_hours?null==l||null==l.costcenter[i]||null==l.costcenter[i].sub_project[o]||null==l.costcenter[i].sub_project[o].tasks[e]?null:l.costcenter[i].sub_project[o].tasks[e].total_billable_hours:"00h 00m"," ")}}function mt(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",67),_["\u0275\u0275text"](1),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2).index,t=_["\u0275\u0275nextContext"](),n=t.$implicit,o=t.index,l=_["\u0275\u0275nextContext"](2).$implicit,i=_["\u0275\u0275nextContext"]().index;_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](3,fe,(null==n?null:n.tasks.length)-1!=e?"1px solid #f1f3f8;":""))("matTooltip",null==l||null==l.costcenter[i]||null==l.costcenter[i].sub_project[o]||null==l.costcenter[i].sub_project[o].tasks[e]?null:l.costcenter[i].sub_project[o].tasks[e].total_non_billable_hours),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null!=l&&null!=l.costcenter[i]&&null!=l.costcenter[i].sub_project[o]&&null!=l.costcenter[i].sub_project[o].tasks[e]&&l.costcenter[i].sub_project[o].tasks[e].total_non_billable_hours?null==l||null==l.costcenter[i]||null==l.costcenter[i].sub_project[o]||null==l.costcenter[i].sub_project[o].tasks[e]?null:l.costcenter[i].sub_project[o].tasks[e].total_non_billable_hours:"00h 00m"," ")}}function ht(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",68),_["\u0275\u0275text"](1),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2).index,t=_["\u0275\u0275nextContext"](),n=t.$implicit,o=t.index,l=_["\u0275\u0275nextContext"](2).$implicit,i=_["\u0275\u0275nextContext"]().index;_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](3,fe,(null==n?null:n.tasks.length)-1!=e?"1px solid #f1f3f8;":""))("matTooltip",null==l||null==l.costcenter[i]||null==l.costcenter[i].sub_project[o]||null==l.costcenter[i].sub_project[o].tasks[e]?null:l.costcenter[i].sub_project[o].tasks[e].total_overtime_hours),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null!=l&&null!=l.costcenter[i]&&null!=l.costcenter[i].sub_project[o]&&null!=l.costcenter[i].sub_project[o].tasks[e]&&l.costcenter[i].sub_project[o].tasks[e].total_overtime_hours?null==l||null==l.costcenter[i]||null==l.costcenter[i].sub_project[o]||null==l.costcenter[i].sub_project[o].tasks[e]?null:l.costcenter[i].sub_project[o].tasks[e].total_overtime_hours:"00h 00m"," ")}}function vt(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",59),_["\u0275\u0275elementStart"](1,"div",60),_["\u0275\u0275pipe"](2,"hexToRgb"),_["\u0275\u0275text"](3),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](4,"div",61),_["\u0275\u0275template"](5,ut,2,5,"p",62),_["\u0275\u0275pipe"](6,"reportActivePipe"),_["\u0275\u0275template"](7,mt,2,5,"p",63),_["\u0275\u0275pipe"](8,"reportActivePipe"),_["\u0275\u0275template"](9,ht,2,5,"p",64),_["\u0275\u0275pipe"](10,"reportActivePipe"),_["\u0275\u0275elementStart"](11,"p",65),_["\u0275\u0275text"](12),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().index,t=_["\u0275\u0275nextContext"](),n=t.index,o=t.$implicit,l=_["\u0275\u0275nextContext"](2).$implicit,i=_["\u0275\u0275nextContext"]().index,r=_["\u0275\u0275nextContext"](3);_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction2"](20,Re,null==l||null==l.costcenter[i]||null==l.costcenter[i].sub_project[n]||null==l.costcenter[i].sub_project[n].tasks[e]?null:l.costcenter[i].sub_project[n].tasks[e].leave_color_code,_["\u0275\u0275pipeBind2"](2,8,null==l||null==l.costcenter[i]||null==l.costcenter[i].sub_project[n]||null==l.costcenter[i].sub_project[n].tasks[e]?null:l.costcenter[i].sub_project[n].tasks[e].leave_color_code,.1))),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",null!=l&&null!=l.costcenter[i]&&null!=l.costcenter[i].sub_project[n]&&null!=l.costcenter[i].sub_project[n].tasks[e]&&l.costcenter[i].sub_project[n].tasks[e].leave_name?null==l||null==l.costcenter[i]||null==l.costcenter[i].sub_project[n]||null==l.costcenter[i].sub_project[n].tasks[e]?null:l.costcenter[i].sub_project[n].tasks[e].leave_name:"NA"," "),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](6,11,r.columns,"billable")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](8,14,r.columns,"nonBillable")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](10,17,r.columns,"overtime")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](23,fe,(null==o?null:o.tasks.length)-1!=e?"1px solid #f1f3f8;":""))("matTooltip",null==l||null==l.costcenter[i]||null==l.costcenter[i].sub_project[n]||null==l.costcenter[i].sub_project[n].tasks[e]?null:l.costcenter[i].sub_project[n].tasks[e].total_hours),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null!=l&&null!=l.costcenter[i]&&null!=l.costcenter[i].sub_project[n]&&null!=l.costcenter[i].sub_project[n].tasks[e]&&l.costcenter[i].sub_project[n].tasks[e].total_hours?null==l||null==l.costcenter[i]||null==l.costcenter[i].sub_project[n]||null==l.costcenter[i].sub_project[n].tasks[e]?null:l.costcenter[i].sub_project[n].tasks[e].total_hours:"00h 00m"," ")}}function ft(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275template"](1,pt,9,17,"div",55),_["\u0275\u0275template"](2,dt,3,8,"div",56),_["\u0275\u0275template"](3,vt,13,25,"div",57),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.index,n=_["\u0275\u0275nextContext"]().index,o=_["\u0275\u0275nextContext"](2).$implicit,l=_["\u0275\u0275nextContext"]().index;_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",!(null!=o&&null!=o.costcenter[l]&&null!=o.costcenter[l].sub_project[n]&&null!=o.costcenter[l].sub_project[n].tasks[e]&&o.costcenter[l].sub_project[n].tasks[e].leave_id)),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",(null==o||null==o.costcenter[l]||null==o.costcenter[l].sub_project[n]||null==o.costcenter[l].sub_project[n].tasks[e]?null:o.costcenter[l].sub_project[n].tasks[e].leave_id)&&"00h 00m"==(null==o||null==o.costcenter[l]||null==o.costcenter[l].sub_project[n]||null==o.costcenter[l].sub_project[n].tasks[e]?null:o.costcenter[l].sub_project[n].tasks[e].total_hours)),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",(null==o||null==o.costcenter[l]||null==o.costcenter[l].sub_project[n]||null==o.costcenter[l].sub_project[n].tasks[e]?null:o.costcenter[l].sub_project[n].tasks[e].leave_id)&&"00h 00m"!=(null==o||null==o.costcenter[l]||null==o.costcenter[l].sub_project[n]||null==o.costcenter[l].sub_project[n].tasks[e]?null:o.costcenter[l].sub_project[n].tasks[e].total_hours))}}function xt(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275elementStart"](1,"div",23),_["\u0275\u0275template"](2,lt,4,12,"p",34),_["\u0275\u0275pipe"](3,"reportActivePipe"),_["\u0275\u0275template"](4,it,4,12,"p",35),_["\u0275\u0275pipe"](5,"reportActivePipe"),_["\u0275\u0275template"](6,rt,4,12,"p",36),_["\u0275\u0275pipe"](7,"reportActivePipe"),_["\u0275\u0275elementStart"](8,"p",27),_["\u0275\u0275pipe"](9,"hoursCalcPerColumn"),_["\u0275\u0275text"](10),_["\u0275\u0275pipe"](11,"hoursCalcPerColumn"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275template"](12,ft,4,3,"ng-container",7),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit,n=t.index,o=_["\u0275\u0275nextContext"](2).$implicit,l=_["\u0275\u0275nextContext"]().index,i=_["\u0275\u0275nextContext"](3);_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](3,6,i.columns,"billable")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](5,9,i.columns,"nonBillable")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](7,12,i.columns,"overtime")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBind2"](9,15,_["\u0275\u0275pureFunction1"](21,Ye,null==o||null==o.costcenter[l]?null:o.costcenter[l].sub_project[n]),1)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind2"](11,18,_["\u0275\u0275pureFunction1"](23,Ye,null==o||null==o.costcenter[l]?null:o.costcenter[l].sub_project[n]),1)," "),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngForOf",null==e?null:e.tasks)}}function gt(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275template"](1,xt,13,25,"ng-container",7),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2).$implicit;_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngForOf",null==e?null:e.sub_project)}}function _t(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275elementStart"](1,"div"),_["\u0275\u0275template"](2,Ne,11,24,"div",55),_["\u0275\u0275template"](3,ze,3,8,"div",56),_["\u0275\u0275template"](4,He,15,32,"div",57),_["\u0275\u0275template"](5,ot,2,1,"ng-container",18),_["\u0275\u0275template"](6,gt,2,1,"ng-container",18),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit,n=_["\u0275\u0275nextContext"](),o=n.index,l=n.$implicit;_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",!(null!=e&&null!=e.costcenter[o]&&e.costcenter[o].leave_id)),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",(null==e||null==e.costcenter[o]?null:e.costcenter[o].leave_id)&&"00h 00m"==(null==e||null==e.costcenter[o]?null:e.costcenter[o].total_hours)),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",(null==e||null==e.costcenter[o]?null:e.costcenter[o].leave_id)&&"00h 00m"!=(null==e||null==e.costcenter[o]?null:e.costcenter[o].total_hours)),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",(null==l?null:l.tasks)&&(null==l?null:l.tasks.length)),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",(null==l?null:l.sub_project)&&(null==l?null:l.sub_project.length))}}function yt(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",10),_["\u0275\u0275elementStart"](1,"div",41),_["\u0275\u0275elementStart"](2,"div",11),_["\u0275\u0275elementStart"](3,"div",42),_["\u0275\u0275elementStart"](4,"p",43),_["\u0275\u0275text"](5),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275template"](6,ge,2,1,"ng-container",18),_["\u0275\u0275template"](7,be,2,1,"ng-container",18),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](8,"div",44),_["\u0275\u0275elementStart"](9,"div",15),_["\u0275\u0275elementStart"](10,"div",42),_["\u0275\u0275elementStart"](11,"p",16),_["\u0275\u0275pipe"](12,"hoursCalcPerCc"),_["\u0275\u0275text"](13),_["\u0275\u0275pipe"](14,"hoursCalcPerCc"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275template"](15,Se,2,1,"ng-container",18),_["\u0275\u0275template"](16,Ee,2,1,"ng-container",18),_["\u0275\u0275elementEnd"](),_["\u0275\u0275template"](17,Be,9,28,"div",45),_["\u0275\u0275template"](18,_t,7,5,"ng-container",7),_["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=t.index,o=_["\u0275\u0275nextContext"](2),l=o.index,i=o.$implicit,r=_["\u0275\u0275nextContext"]();_["\u0275\u0275advance"](4),_["\u0275\u0275property"]("matTooltip",(null!=e&&e.cc_name?e.cc_name:"NA")+" - "+(null!=e&&e.cc_desc?e.cc_desc:"NA")),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate2"](" ",null!=e&&e.cc_name?e.cc_name:"NA"," - ",null!=e&&e.cc_desc?e.cc_desc:"NA"," "),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",(null==e?null:e.tasks)&&(null==e?null:e.tasks.length)),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",(null==e?null:e.sub_project)&&(null==e?null:e.sub_project.length)),_["\u0275\u0275advance"](4),_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBind4"](12,11,null==r.logData[l]?null:r.logData[l].days,n,-1,-1)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind4"](14,16,null==r.logData[l]?null:r.logData[l].days,n,-1,-1)," "),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",(null==e?null:e.tasks)&&(null==e?null:e.tasks.length)),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",(null==e?null:e.sub_project)&&(null==e?null:e.sub_project.length)),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",1!=r.currentViewType),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngForOf",null==i?null:i.days)}}function bt(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275template"](1,yt,19,21,"div",40),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngForOf",null==e||null==e.days[0]?null:e.days[0].costcenter)}}function Ct(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275elementStart"](1,"div",10),_["\u0275\u0275elementStart"](2,"div",11),_["\u0275\u0275template"](3,oe,2,0,"mat-icon",12),_["\u0275\u0275template"](4,le,2,0,"mat-icon",12),_["\u0275\u0275element"](5,"app-user-image",13),_["\u0275\u0275elementStart"](6,"p",14),_["\u0275\u0275text"](7),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](8,"div",15),_["\u0275\u0275elementStart"](9,"p",16),_["\u0275\u0275text"](10),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275template"](11,ie,3,2,"div",17),_["\u0275\u0275template"](12,pe,2,1,"ng-container",18),_["\u0275\u0275template"](13,ve,2,1,"ng-container",18),_["\u0275\u0275elementEnd"](),_["\u0275\u0275template"](14,bt,2,1,"ng-container",18),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit,n=t.index,o=_["\u0275\u0275nextContext"]();_["\u0275\u0275advance"](3),_["\u0275\u0275property"]("ngIf",e.expanded),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",!e.expanded),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("id",e.employee_oid),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("matTooltip",null!=e&&e.employee_name?e.employee_name:"-"),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null!=e&&e.employee_name?e.employee_name:"-"," "),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("matTooltip",null!=e&&e.total_hours?e.total_hours:"00h 00m"),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null!=e&&e.total_hours?e.total_hours:"00h 00m"," "),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",1!=o.currentViewType),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",0==n),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",0!=n),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",e.expanded)}}let St=(()=>{class e{constructor(){this.limitEmit=new _.EventEmitter}ngOnInit(){}onScroll(){this.limit+=15,this.limitEmit.emit(this.limit)}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=_["\u0275\u0275defineComponent"]({type:e,selectors:[["app-ts-v2-daily-log-main"]],inputs:{currentViewType:"currentViewType",logData:"logData",columns:"columns",limit:"limit"},outputs:{limitEmit:"limitEmit"},decls:10,vars:5,consts:[["infinite-scroll","",1,"main-container",3,"infiniteScrollDistance","scrollWindow","scrolled"],[1,"header-sticky"],[1,"row","header-row"],[1,"name-project-div-header"],[1,"total-hours-div-header"],["class","total-leaves-div-header",4,"ngIf"],["class","log-hours-div-header",4,"ngFor","ngForOf"],[4,"ngFor","ngForOf"],[1,"total-leaves-div-header"],[1,"log-hours-div-header"],[1,"row","item-row"],[1,"name-project-div"],["class","expand-icon",3,"click",4,"ngIf"],["imgWidth","16px","imgHeight","16px",2,"margin-left","8px","margin-right","8px","margin-bottom","4px",3,"id"],[1,"name-project-div-content",3,"matTooltip"],[1,"total-hours-div"],[1,"total-hours-div-content",3,"matTooltip"],["class","total-leaves-div",4,"ngIf"],[4,"ngIf"],[1,"expand-icon",3,"click"],[1,"total-leaves-div"],[1,"total-leaves-div-content",3,"matTooltip"],["class","log-hours-div",4,"ngFor","ngForOf"],[1,"log-hours-div"],["class","col p-0",4,"ngIf"],[1,"col","p-0"],[1,"total-hours-text"],[1,"log-hours-div-content","total-hours","col","p-0",3,"matTooltip"],[1,"billable-text"],[1,"log-hours-div-content","billable-hours",3,"matTooltip"],[1,"non-billable-text"],[1,"log-hours-div-content","non-billable-hours",3,"matTooltip"],[1,"overtime-text"],[1,"log-hours-div-content","overtime-hours",3,"matTooltip"],["class","log-hours-div-content billable-hours col p-0",3,"matTooltip",4,"ngIf"],["class","log-hours-div-content non-billable-hours col p-0",3,"matTooltip",4,"ngIf"],["class","log-hours-div-content overtime-hours col p-0",3,"matTooltip",4,"ngIf"],[1,"log-hours-div-content","billable-hours","col","p-0",3,"matTooltip"],[1,"log-hours-div-content","non-billable-hours","col","p-0",3,"matTooltip"],[1,"log-hours-div-content","overtime-hours","col","p-0",3,"matTooltip"],["class","row item-row",4,"ngFor","ngForOf"],[1,"cc-sticky"],[1,"row"],[1,"cc-name",3,"matTooltip"],[1,"total-hours-sticky"],["class","total-leaves-sticky",4,"ngIf"],["class","name-project-div",4,"ngFor","ngForOf"],[1,"task-name",3,"ngStyle","matTooltip"],[1,"sub-cc-name",3,"matTooltip"],["class","total-hours-div",4,"ngFor","ngForOf"],[1,"total-hours-div-content",3,"ngStyle","matTooltip"],[1,"total-leaves-sticky"],["class","total-leaves-div",4,"ngFor","ngForOf"],[1,"total-leaves-div-content",3,"ngStyle"],[1,"total-leaves-div-content"],["class","log-hours-div",4,"ngIf"],["class","log-hours-div","style","align-items: center; justify-content: center",3,"ngStyle",4,"ngIf"],["style","display: flex; flex-direction: column",4,"ngIf"],[1,"log-hours-div",2,"align-items","center","justify-content","center",3,"ngStyle"],[2,"display","flex","flex-direction","column"],[1,"log-hours-div-half-day",2,"align-items","center","justify-content","center",3,"ngStyle"],[1,"log-hours-div-half-day"],["class","log-hours-div-content billable-hours col p-0",3,"ngStyle","matTooltip",4,"ngIf"],["class","log-hours-div-content non-billable-hours col p-0",3,"ngStyle","matTooltip",4,"ngIf"],["class","log-hours-div-content overtime-hours col p-0",3,"ngStyle","matTooltip",4,"ngIf"],[1,"log-hours-div-content","total-hours","col","p-0",3,"ngStyle","matTooltip"],[1,"log-hours-div-content","billable-hours","col","p-0",3,"ngStyle","matTooltip"],[1,"log-hours-div-content","non-billable-hours","col","p-0",3,"ngStyle","matTooltip"],[1,"log-hours-div-content","overtime-hours","col","p-0",3,"ngStyle","matTooltip"]],template:function(e,t){1&e&&(_["\u0275\u0275elementStart"](0,"div",0),_["\u0275\u0275listener"]("scrolled",(function(){return t.onScroll()})),_["\u0275\u0275elementStart"](1,"div",1),_["\u0275\u0275elementStart"](2,"div",2),_["\u0275\u0275elementStart"](3,"p",3),_["\u0275\u0275text"](4,"Name/Projects"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](5,"p",4),_["\u0275\u0275text"](6,"Total Hours"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275template"](7,te,2,0,"p",5),_["\u0275\u0275template"](8,ne,3,4,"p",6),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275template"](9,Ct,15,11,"ng-container",7),_["\u0275\u0275elementEnd"]()),2&e&&(_["\u0275\u0275property"]("infiniteScrollDistance",.1)("scrollWindow",!1),_["\u0275\u0275advance"](7),_["\u0275\u0275property"]("ngIf",1!=t.currentViewType),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngForOf",null==t.logData[0]?null:t.logData[0].days),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngForOf",t.logData))},directives:[Q.a,o.NgIf,o.NgForOf,I.a,g.a,m.a,o.NgStyle],pipes:[X,q,K,J,Z,ee],styles:[".main-container[_ngcontent-%COMP%]{overflow:scroll;height:-webkit-fill-available}.header-row[_ngcontent-%COMP%]{-ms-overflow-style:none;scrollbar-width:none;overflow:unset}.header-row[_ngcontent-%COMP%], .item-row[_ngcontent-%COMP%]{flex-wrap:nowrap;float:left;min-width:-webkit-fill-available}.item-row[_ngcontent-%COMP%]{transition:all .25s linear;height:auto}.header-sticky[_ngcontent-%COMP%]{position:sticky;top:0;z-index:9999999}.expand-icon[_ngcontent-%COMP%]{font-size:18px;color:#7d838b;height:18px!important;width:18px!important;cursor:pointer}.name-project-div-header[_ngcontent-%COMP%]{min-width:300px;max-width:300px;left:0}.name-project-div-header[_ngcontent-%COMP%], .total-hours-div-header[_ngcontent-%COMP%]{display:inline;padding:10px;background-color:#e8e9ee;font-size:12px;font-weight:400;color:#7d838b;border-right:1px solid #f1f3f8;border-bottom:1px solid #f1f3f8;position:sticky;margin-bottom:0;height:42px}.total-hours-div-header[_ngcontent-%COMP%]{min-width:100px;max-width:100px;left:300px;text-align:center}.total-leaves-div-header[_ngcontent-%COMP%]{min-width:100px;max-width:100px;position:sticky;left:400px}.log-hours-div-header[_ngcontent-%COMP%], .total-leaves-div-header[_ngcontent-%COMP%]{display:inline;padding:10px;background-color:#e8e9ee;font-size:12px;font-weight:400;color:#7d838b;border-right:1px solid #f1f3f8;border-bottom:1px solid #f1f3f8;margin-bottom:0;height:42px;text-align:center}.log-hours-div-header[_ngcontent-%COMP%]{min-width:250px;max-width:250px}.name-project-div[_ngcontent-%COMP%]{display:flex;align-items:center;padding:10px;min-width:300px;max-width:300px;background-color:#fff;border-right:1px solid #f1f3f8;border-bottom:1px solid #f1f3f8;position:sticky;left:0;height:42px;z-index:1}.name-project-div-content[_ngcontent-%COMP%]{font-size:12px;font-weight:400;color:#111434;margin-bottom:0;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.total-hours-div[_ngcontent-%COMP%]{align-items:center;padding:10px;min-width:100px;max-width:100px;background-color:#fff;border-right:1px solid #f1f3f8;border-bottom:1px solid #f1f3f8;height:42px;position:sticky;left:300px;z-index:1;display:flex;justify-content:center}.total-hours-div-content[_ngcontent-%COMP%]{font-size:12px;font-weight:500;color:#111434;margin-bottom:0;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.total-leaves-div[_ngcontent-%COMP%]{align-items:center;padding:10px;min-width:100px;max-width:100px;background-color:#fff;border-right:1px solid #f1f3f8;border-bottom:1px solid #f1f3f8;height:42px;position:sticky;left:400px;z-index:1;display:flex;justify-content:center}.total-leaves-div-content[_ngcontent-%COMP%]{font-size:12px;font-weight:500;color:#111434;margin-bottom:0;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.log-hours-div[_ngcontent-%COMP%]{display:flex;min-width:250px;max-width:250px;background-color:#fff;border-right:1px solid #f1f3f8;border-bottom:1px solid #f1f3f8;height:42px}.log-hours-div-content[_ngcontent-%COMP%]{font-size:12px;font-weight:400;color:#111434;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;margin-bottom:0}.billable-hours[_ngcontent-%COMP%], .total-hours[_ngcontent-%COMP%]{display:flex;align-items:center;border-right:1px solid #f1f3f8;justify-content:center}.billable-hours[_ngcontent-%COMP%]{background-color:#eef9e8}.non-billable-hours[_ngcontent-%COMP%]{border-right:1px solid #f1f3f8;background-color:#e7f9f9}.non-billable-hours[_ngcontent-%COMP%], .overtime-hours[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center}.overtime-hours[_ngcontent-%COMP%]{background-color:#f1eafa}.total-hours-text[_ngcontent-%COMP%]{font-size:10px;color:#111434;font-weight:400;border:.25px solid var(--black-40,#a8acb2);margin-bottom:0;text-align:center}.billable-text[_ngcontent-%COMP%]{color:#2a7207;background-color:#eef9e8}.billable-text[_ngcontent-%COMP%], .non-billable-text[_ngcontent-%COMP%]{font-size:10px;font-weight:400;border:.25px solid var(--black-40,#a8acb2);margin-bottom:0;text-align:center;margin-right:1px}.non-billable-text[_ngcontent-%COMP%]{color:#13c2c2;background-color:#e7f9f9}.overtime-text[_ngcontent-%COMP%]{font-size:10px;color:#722ed1;font-weight:400;border:.25px solid var(--black-40,#a8acb2);margin-bottom:0;text-align:center;background-color:#f1eafa}.cc-name[_ngcontent-%COMP%]{font-weight:500;color:#111434;margin-left:18px;width:250px}.cc-name[_ngcontent-%COMP%], .sub-cc-name[_ngcontent-%COMP%]{font-size:12px;margin-bottom:0;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.sub-cc-name[_ngcontent-%COMP%]{font-weight:400;color:#7d838b;margin-left:36px;width:230px}.task-name[_ngcontent-%COMP%]{font-size:12px;font-weight:400;color:#ee4961;margin-left:54px;margin-bottom:0;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;width:220px}.cc-sticky[_ngcontent-%COMP%]{position:sticky;left:0;z-index:1}.total-hours-sticky[_ngcontent-%COMP%]{position:sticky;left:300px;z-index:1}.total-leaves-sticky[_ngcontent-%COMP%]{position:sticky;left:400px;z-index:1}.log-hours-div-half-day[_ngcontent-%COMP%]{display:flex;min-width:250px;max-width:250px;background-color:#fff;border-right:1px solid #f1f3f8;border-bottom:1px solid #f1f3f8;height:21px}"]}),e})(),kt=(()=>{class e{transform(e,t,n){if(e&&e.length>0){if(1==t){let t=e.reduce((e,t)=>{var o,l,i,r,a,s,c,p,d,u;return e+=(60*parseInt((null===(l=null===(o=null==t?void 0:t.days[n])||void 0===o?void 0:o.total_hours)||void 0===l?void 0:l.slice(0,null===(r=null===(i=null==t?void 0:t.days[n])||void 0===i?void 0:i.total_hours)||void 0===r?void 0:r.indexOf("h")))||0)||0)+(parseInt((null===(s=null===(a=null==t?void 0:t.days[n])||void 0===a?void 0:a.total_hours)||void 0===s?void 0:s.slice((null===(p=null===(c=null==t?void 0:t.days[n])||void 0===c?void 0:c.total_hours)||void 0===p?void 0:p.indexOf("h"))+2))||0)||0),(null===(d=t.days[n])||void 0===d?void 0:d.tasks)&&t.days[n].tasks.length&&(e+=t.days[n].tasks.reduce((e,t)=>{var n,o,l,i;return e+(60*parseInt((null===(n=null==t?void 0:t.total_hours)||void 0===n?void 0:n.slice(0,null===(o=null==t?void 0:t.total_hours)||void 0===o?void 0:o.indexOf("h")))||0)||0)+(parseInt((null===(l=null==t?void 0:t.total_hours)||void 0===l?void 0:l.slice((null===(i=null==t?void 0:t.total_hours)||void 0===i?void 0:i.indexOf("h"))+2))||0)||0)},0)),(null===(u=t.days[n])||void 0===u?void 0:u.sub_project)&&t.days[n].sub_project.length&&(e+=t.days[n].sub_project.map(e=>e.tasks.reduce((e,t)=>{var n,o,l,i;return e+(60*parseInt((null===(n=null==t?void 0:t.total_hours)||void 0===n?void 0:n.slice(0,null===(o=null==t?void 0:t.total_hours)||void 0===o?void 0:o.indexOf("h")))||0)||0)+(parseInt((null===(l=null==t?void 0:t.total_hours)||void 0===l?void 0:l.slice((null===(i=null==t?void 0:t.total_hours)||void 0===i?void 0:i.indexOf("h"))+2))||0)||0)},0)).reduce((e,t)=>e+t,0)),e},0);return Math.floor(t/60).toString().padStart(2,"0")+"h "+(t%60).toString().padStart(2,"0")+"m"}if(2==t){let t=e.reduce((e,t)=>{var o,l,i,r,a,s,c,p,d,u,m;return e+=(60*parseInt((null===(l=null===(o=null==t?void 0:t.days[n])||void 0===o?void 0:o.total_billable_hours)||void 0===l?void 0:l.slice(0,null===(r=null===(i=null==t?void 0:t.days[n])||void 0===i?void 0:i.total_billable_hours)||void 0===r?void 0:r.indexOf("h")))||0)||0)+(parseInt((null===(s=null===(a=null==t?void 0:t.days[n])||void 0===a?void 0:a.total_billable_hours)||void 0===s?void 0:s.slice((null===(p=null===(c=null==t?void 0:t.days[n])||void 0===c?void 0:c.total_billable_hours)||void 0===p?void 0:p.indexOf("h"))+2))||0)||0),(null===(d=null==t?void 0:t.days[n])||void 0===d?void 0:d.tasks)&&(null==t?void 0:t.days[n].tasks.length)&&(e+=null==t?void 0:t.days[n].tasks.reduce((e,t)=>{var n,o,l,i;return e+(60*parseInt((null===(n=null==t?void 0:t.total_billable_hours)||void 0===n?void 0:n.slice(0,null===(o=null==t?void 0:t.total_billable_hours)||void 0===o?void 0:o.indexOf("h")))||0)||0)+(parseInt((null===(l=null==t?void 0:t.total_billable_hours)||void 0===l?void 0:l.slice((null===(i=null==t?void 0:t.total_billable_hours)||void 0===i?void 0:i.indexOf("h"))+2))||0)||0)},0)),(null===(u=null==t?void 0:t.days[n])||void 0===u?void 0:u.sub_project)&&(null===(m=null==t?void 0:t.days[n])||void 0===m?void 0:m.sub_project.length)&&(e+=null==t?void 0:t.days[n].sub_project.map(e=>e.tasks.reduce((e,t)=>{var n,o,l,i;return e+(60*parseInt((null===(n=null==t?void 0:t.total_billable_hours)||void 0===n?void 0:n.slice(0,null===(o=null==t?void 0:t.total_billable_hours)||void 0===o?void 0:o.indexOf("h")))||0)||0)+(parseInt((null===(l=null==t?void 0:t.total_billable_hours)||void 0===l?void 0:l.slice((null===(i=null==t?void 0:t.total_billable_hours)||void 0===i?void 0:i.indexOf("h"))+2))||0)||0)},0)).reduce((e,t)=>e+t,0)),e},0);return Math.floor(t/60).toString().padStart(2,"0")+"h "+(t%60).toString().padStart(2,"0")+"m"}if(3==t){let t=e.reduce((e,t)=>{var o,l,i,r,a,s,c,p,d,u,m,h;return e+=(60*parseInt((null===(l=null===(o=null==t?void 0:t.days[n])||void 0===o?void 0:o.total_non_billable_hours)||void 0===l?void 0:l.slice(0,null===(r=null===(i=null==t?void 0:t.days[n])||void 0===i?void 0:i.total_non_billable_hours)||void 0===r?void 0:r.indexOf("h")))||0)||0)+(parseInt((null===(s=null===(a=null==t?void 0:t.days[n])||void 0===a?void 0:a.total_non_billable_hours)||void 0===s?void 0:s.slice((null===(p=null===(c=null==t?void 0:t.days[n])||void 0===c?void 0:c.total_non_billable_hours)||void 0===p?void 0:p.indexOf("h"))+2))||0)||0),(null===(d=null==t?void 0:t.days[n])||void 0===d?void 0:d.tasks)&&(null==t?void 0:t.days[n].tasks.length)&&(e+=null==t?void 0:t.days[n].tasks.reduce((e,t)=>{var n,o,l,i;return e+(60*parseInt((null===(n=null==t?void 0:t.total_non_billable_hours)||void 0===n?void 0:n.slice(0,null===(o=null==t?void 0:t.total_non_billable_hours)||void 0===o?void 0:o.indexOf("h")))||0)||0)+(parseInt((null===(l=null==t?void 0:t.total_non_billable_hours)||void 0===l?void 0:l.slice((null===(i=null==t?void 0:t.total_non_billable_hours)||void 0===i?void 0:i.indexOf("h"))+2))||0)||0)},0)),(null===(u=null==t?void 0:t.days[n])||void 0===u?void 0:u.sub_project)&&(null===(m=null==t?void 0:t.days[n])||void 0===m?void 0:m.sub_project.length)&&(e+=null===(h=null==t?void 0:t.days[n])||void 0===h?void 0:h.sub_project.map(e=>e.tasks.reduce((e,t)=>{var n,o,l,i;return e+(60*parseInt((null===(n=null==t?void 0:t.total_non_billable_hours)||void 0===n?void 0:n.slice(0,null===(o=null==t?void 0:t.total_non_billable_hours)||void 0===o?void 0:o.indexOf("h")))||0)||0)+(parseInt((null===(l=null==t?void 0:t.total_non_billable_hours)||void 0===l?void 0:l.slice((null===(i=null==t?void 0:t.total_non_billable_hours)||void 0===i?void 0:i.indexOf("h"))+2))||0)||0)},0)).reduce((e,t)=>e+t,0)),e},0);return Math.floor(t/60).toString().padStart(2,"0")+"h "+(t%60).toString().padStart(2,"0")+"m"}if(4==t){let t=e.reduce((e,t)=>{var o,l,i,r,a,s,c,p,d,u,m,h,v,f;return e+=(60*parseInt((null===(l=null===(o=null==t?void 0:t.days[n])||void 0===o?void 0:o.total_overtime_hours)||void 0===l?void 0:l.slice(0,null===(r=null===(i=null==t?void 0:t.days[n])||void 0===i?void 0:i.total_overtime_hours)||void 0===r?void 0:r.indexOf("h")))||0)||0)+(parseInt((null===(s=null===(a=null==t?void 0:t.days[n])||void 0===a?void 0:a.total_overtime_hours)||void 0===s?void 0:s.slice((null===(p=null===(c=null==t?void 0:t.days[n])||void 0===c?void 0:c.total_overtime_hours)||void 0===p?void 0:p.indexOf("h"))+2))||0)||0),(null===(d=null==t?void 0:t.days[n])||void 0===d?void 0:d.tasks)&&(null===(u=null==t?void 0:t.days[n])||void 0===u?void 0:u.tasks.length)&&(e+=null===(m=null==t?void 0:t.days[n])||void 0===m?void 0:m.tasks.reduce((e,t)=>{var n,o,l,i;return e+(60*parseInt((null===(n=null==t?void 0:t.total_overtime_hours)||void 0===n?void 0:n.slice(0,null===(o=null==t?void 0:t.total_overtime_hours)||void 0===o?void 0:o.indexOf("h")))||0)||0)+(parseInt((null===(l=null==t?void 0:t.total_overtime_hours)||void 0===l?void 0:l.slice((null===(i=null==t?void 0:t.total_overtime_hours)||void 0===i?void 0:i.indexOf("h"))+2))||0)||0)},0)),(null===(h=null==t?void 0:t.days[n])||void 0===h?void 0:h.sub_project)&&(null===(v=null==t?void 0:t.days[n])||void 0===v?void 0:v.sub_project.length)&&(e+=null===(f=null==t?void 0:t.days[n])||void 0===f?void 0:f.sub_project.map(e=>e.tasks.reduce((e,t)=>{var n,o,l,i;return e+(60*parseInt((null===(n=null==t?void 0:t.total_overtime_hours)||void 0===n?void 0:n.slice(0,null===(o=null==t?void 0:t.total_overtime_hours)||void 0===o?void 0:o.indexOf("h")))||0)||0)+(parseInt((null===(l=null==t?void 0:t.total_overtime_hours)||void 0===l?void 0:l.slice((null===(i=null==t?void 0:t.total_overtime_hours)||void 0===i?void 0:i.indexOf("h"))+2))||0)||0)},0)).reduce((e,t)=>e+t,0)),e},0);return Math.floor(t/60).toString().padStart(2,"0")+"h "+(t%60).toString().padStart(2,"0")+"m"}return"00h 00m"}return"00h 00m"}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=_["\u0275\u0275definePipe"]({name:"hoursCalcPerColumnGroupedByCc",type:e,pure:!0}),e})(),It=(()=>{class e{transform(e,t,n){if(e&&e.length>0){let o=[],l=0;return-1==t&&-1!=n?(o=e.map(e=>{var t,o,l,i,r,a,s,c;return(60*parseInt((null===(o=null===(t=e.tasks[n])||void 0===t?void 0:t.total_hours)||void 0===o?void 0:o.slice(0,null===(i=null===(l=e.tasks[n])||void 0===l?void 0:l.total_hours)||void 0===i?void 0:i.indexOf("h")))||0)||0)+(parseInt((null===(a=null===(r=e.tasks[n])||void 0===r?void 0:r.total_hours)||void 0===a?void 0:a.slice((null===(c=null===(s=e.tasks[n])||void 0===s?void 0:s.total_hours)||void 0===c?void 0:c.indexOf("h"))+2))||0)||0)}),l=o.reduce((e,t)=>e+t,0)):-1!=t&&-1!=n?(o=e.map(e=>{var o,l,i,r;return(60*parseInt((null===(l=null===(o=e.sub_project[t].tasks[n])||void 0===o?void 0:o.total_hours)||void 0===l?void 0:l.slice(0,2))||0)||0)+(parseInt((null===(r=null===(i=e.sub_project[t].tasks[n])||void 0===i?void 0:i.total_hours)||void 0===r?void 0:r.slice(4,6))||0)||0)}),l=o.reduce((e,t)=>e+t,0)):-1!=t&&-1==n&&(o=e.map(e=>e.sub_project[t].tasks.map(e=>{var t,n,o,l;return(60*parseInt((null===(t=null==e?void 0:e.total_hours)||void 0===t?void 0:t.slice(0,null===(n=null==e?void 0:e.total_hours)||void 0===n?void 0:n.indexOf("h")))||0)||0)+(parseInt((null===(o=null==e?void 0:e.total_hours)||void 0===o?void 0:o.slice((null===(l=null==e?void 0:e.total_hours)||void 0===l?void 0:l.indexOf("h"))+2))||0)||0)})),o=[].concat(...o),l=o.reduce((e,t)=>e+t,0)),Math.floor(l/60).toString().padStart(2,"0")+"h "+(l%60).toString().padStart(2,"0")+"m"}return"00h 00m"}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=_["\u0275\u0275definePipe"]({name:"hoursCalcPerCcGroupedByCc",type:e,pure:!0}),e})(),wt=(()=>{class e{transform(e,t){if(e&&e.length>0){if(1==t){let t=e.reduce((e,t)=>e+t.tasks.reduce((e,t)=>{var n,o,l,i;return e+(60*parseInt((null===(n=null==t?void 0:t.total_hours)||void 0===n?void 0:n.slice(0,null===(o=null==t?void 0:t.total_hours)||void 0===o?void 0:o.indexOf("h")))||0)||0)+(parseInt((null===(l=null==t?void 0:t.total_hours)||void 0===l?void 0:l.slice((null===(i=null==t?void 0:t.total_hours)||void 0===i?void 0:i.indexOf("h"))+2))||0)||0)},0),0);return Math.floor(t/60).toString().padStart(2,"0")+"h "+(t%60).toString().padStart(2,"0")+"m"}if(2==t){let t=e.reduce((e,t)=>e+t.tasks.reduce((e,t)=>{var n,o,l,i;return e+(60*parseInt((null===(n=null==t?void 0:t.total_billable_hours)||void 0===n?void 0:n.slice(0,null===(o=null==t?void 0:t.total_billable_hours)||void 0===o?void 0:o.indexOf("h")))||0)||0)+(parseInt((null===(l=null==t?void 0:t.total_billable_hours)||void 0===l?void 0:l.slice((null===(i=null==t?void 0:t.total_billable_hours)||void 0===i?void 0:i.indexOf("h"))+2))||0)||0)},0),0);return Math.floor(t/60).toString().padStart(2,"0")+"h "+(t%60).toString().padStart(2,"0")+"m"}if(3==t){let t=e.reduce((e,t)=>e+t.tasks.reduce((e,t)=>{var n,o,l,i;return e+(60*parseInt((null===(n=null==t?void 0:t.total_non_billable_hours)||void 0===n?void 0:n.slice(0,null===(o=null==t?void 0:t.total_non_billable_hours)||void 0===o?void 0:o.indexOf("h")))||0)||0)+(parseInt((null===(l=null==t?void 0:t.total_non_billable_hours)||void 0===l?void 0:l.slice((null===(i=null==t?void 0:t.total_non_billable_hours)||void 0===i?void 0:i.indexOf("h"))+2))||0)||0)},0),0);return Math.floor(t/60).toString().padStart(2,"0")+"h "+(t%60).toString().padStart(2,"0")+"m"}if(4==t){let t=e.reduce((e,t)=>e+t.tasks.reduce((e,t)=>{var n,o,l,i;return e+(60*parseInt((null===(n=null==t?void 0:t.total_overtime_hours)||void 0===n?void 0:n.slice(0,null===(o=null==t?void 0:t.total_overtime_hours)||void 0===o?void 0:o.indexOf("h")))||0)||0)+(parseInt((null===(l=null==t?void 0:t.total_overtime_hours)||void 0===l?void 0:l.slice((null===(i=null==t?void 0:t.total_overtime_hours)||void 0===i?void 0:i.indexOf("h"))+2))||0)||0)},0),0);return Math.floor(t/60).toString().padStart(2,"0")+"h "+(t%60).toString().padStart(2,"0")+"m"}return"00h 00m"}return"00h 00m"}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=_["\u0275\u0275definePipe"]({name:"hoursCalcPerWorksteamColumnGroupedByCc",type:e,pure:!0}),e})();function Et(e,t){1&e&&(_["\u0275\u0275elementStart"](0,"p",8),_["\u0275\u0275text"](1,"Leaves"),_["\u0275\u0275elementEnd"]())}function Ot(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",9),_["\u0275\u0275text"](1),_["\u0275\u0275pipe"](2,"dateFormat"),_["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=_["\u0275\u0275nextContext"]();_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind2"](2,1,e,n.currentViewType)," ")}}function Pt(e,t){if(1&e){const e=_["\u0275\u0275getCurrentView"]();_["\u0275\u0275elementStart"](0,"mat-icon",18),_["\u0275\u0275listener"]("click",(function(){_["\u0275\u0275restoreView"](e);const t=_["\u0275\u0275nextContext"]().$implicit;return t.expanded=!t.expanded})),_["\u0275\u0275text"](1,"indeterminate_check_box"),_["\u0275\u0275elementEnd"]()}}function Mt(e,t){if(1&e){const e=_["\u0275\u0275getCurrentView"]();_["\u0275\u0275elementStart"](0,"mat-icon",18),_["\u0275\u0275listener"]("click",(function(){_["\u0275\u0275restoreView"](e);const t=_["\u0275\u0275nextContext"]().$implicit;return t.expanded=!t.expanded})),_["\u0275\u0275text"](1,"add_box"),_["\u0275\u0275elementEnd"]()}}function Dt(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",19),_["\u0275\u0275elementStart"](1,"p",20),_["\u0275\u0275text"](2),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("matTooltip",(null!=e&&e.total_leaves?e.total_leaves:"0")+" Days"),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null!=e&&e.total_leaves?e.total_leaves:"0"," Days ")}}function jt(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",24),_["\u0275\u0275elementStart"](1,"p",27),_["\u0275\u0275text"](2,"Billable"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](3,"p",28),_["\u0275\u0275pipe"](4,"hoursCalcPerColumnGroupedByCc"),_["\u0275\u0275text"](5),_["\u0275\u0275pipe"](6,"hoursCalcPerColumnGroupedByCc"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().index,t=_["\u0275\u0275nextContext"](2).$implicit;_["\u0275\u0275advance"](3),_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBind3"](4,2,t.employees,2,e)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind3"](6,6,t.employees,2,e)," ")}}function Tt(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",24),_["\u0275\u0275elementStart"](1,"p",29),_["\u0275\u0275text"](2,"Non-Billable"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](3,"p",30),_["\u0275\u0275pipe"](4,"hoursCalcPerColumnGroupedByCc"),_["\u0275\u0275text"](5),_["\u0275\u0275pipe"](6,"hoursCalcPerColumnGroupedByCc"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().index,t=_["\u0275\u0275nextContext"](2).$implicit;_["\u0275\u0275advance"](3),_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBind3"](4,2,t.employees,3,e)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind3"](6,6,t.employees,3,e)," ")}}function Ft(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",24),_["\u0275\u0275elementStart"](1,"p",31),_["\u0275\u0275text"](2,"Overtime"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](3,"p",32),_["\u0275\u0275pipe"](4,"hoursCalcPerColumnGroupedByCc"),_["\u0275\u0275text"](5),_["\u0275\u0275pipe"](6,"hoursCalcPerColumnGroupedByCc"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().index,t=_["\u0275\u0275nextContext"](2).$implicit;_["\u0275\u0275advance"](3),_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBind3"](4,2,t.employees,4,e)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind3"](6,6,t.employees,4,e)," ")}}function Bt(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",22),_["\u0275\u0275template"](1,jt,7,10,"div",23),_["\u0275\u0275pipe"](2,"reportActivePipe"),_["\u0275\u0275template"](3,Tt,7,10,"div",23),_["\u0275\u0275pipe"](4,"reportActivePipe"),_["\u0275\u0275template"](5,Ft,7,10,"div",23),_["\u0275\u0275pipe"](6,"reportActivePipe"),_["\u0275\u0275elementStart"](7,"div",24),_["\u0275\u0275elementStart"](8,"p",25),_["\u0275\u0275text"](9,"Logged Hours"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](10,"p",26),_["\u0275\u0275pipe"](11,"hoursCalcPerColumnGroupedByCc"),_["\u0275\u0275text"](12),_["\u0275\u0275pipe"](13,"hoursCalcPerColumnGroupedByCc"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=t.index,n=_["\u0275\u0275nextContext"](2).$implicit,o=_["\u0275\u0275nextContext"]();_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](2,5,o.columns,"billable")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](4,8,o.columns,"nonBillable")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](6,11,o.columns,"overtime")),_["\u0275\u0275advance"](5),_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBind3"](11,14,n.employees,1,e)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind3"](13,18,n.employees,1,e)," ")}}function Yt(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275template"](1,Bt,14,22,"div",21),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngForOf",null==e||null==e.employees[0]?null:e.employees[0].days)}}function $t(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",36),_["\u0275\u0275pipe"](1,"hoursCalcPerColumnGroupedByCc"),_["\u0275\u0275text"](2),_["\u0275\u0275pipe"](3,"hoursCalcPerColumnGroupedByCc"),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](3).$implicit,t=_["\u0275\u0275nextContext"]();_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBind3"](1,2,e.employees,2,t.dayIndex)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind3"](3,6,e.employees,2,t.dayIndex)," ")}}function Vt(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",37),_["\u0275\u0275pipe"](1,"hoursCalcPerColumnGroupedByCc"),_["\u0275\u0275text"](2),_["\u0275\u0275pipe"](3,"hoursCalcPerColumnGroupedByCc"),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](3).$implicit,t=_["\u0275\u0275nextContext"]();_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBind3"](1,2,e.employees,3,t.dayIndex)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind3"](3,6,e.employees,3,t.dayIndex)," ")}}function At(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",38),_["\u0275\u0275pipe"](1,"hoursCalcPerColumnGroupedByCc"),_["\u0275\u0275text"](2),_["\u0275\u0275pipe"](3,"hoursCalcPerColumnGroupedByCc"),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](3).$implicit,t=_["\u0275\u0275nextContext"]();_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBind3"](1,2,e.employees,4,t.dayIndex)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind3"](3,6,e.employees,4,t.dayIndex)," ")}}function Nt(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",22),_["\u0275\u0275template"](1,$t,4,10,"p",33),_["\u0275\u0275pipe"](2,"reportActivePipe"),_["\u0275\u0275template"](3,Vt,4,10,"p",34),_["\u0275\u0275pipe"](4,"reportActivePipe"),_["\u0275\u0275template"](5,At,4,10,"p",35),_["\u0275\u0275pipe"](6,"reportActivePipe"),_["\u0275\u0275elementStart"](7,"p",26),_["\u0275\u0275pipe"](8,"hoursCalcPerColumnGroupedByCc"),_["\u0275\u0275text"](9),_["\u0275\u0275pipe"](10,"hoursCalcPerColumnGroupedByCc"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2).$implicit,t=_["\u0275\u0275nextContext"]();_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](2,5,t.columns,"billable")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](4,8,t.columns,"nonBillable")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](6,11,t.columns,"overtime")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBind3"](8,14,e.employees,1,t.dayIndex)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind3"](10,18,e.employees,1,t.dayIndex)," ")}}function Rt(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275template"](1,Nt,11,22,"div",21),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngForOf",null==e||null==e.employees[0]?null:e.employees[0].days)}}const zt=function(e){return{visibility:e}};function Lt(e,t){if(1&e){const e=_["\u0275\u0275getCurrentView"]();_["\u0275\u0275elementStart"](0,"mat-icon",42),_["\u0275\u0275listener"]("click",(function(){_["\u0275\u0275restoreView"](e);const t=_["\u0275\u0275nextContext"](2).$implicit;return t.expanded=!t.expanded})),_["\u0275\u0275text"](1,"indeterminate_check_box"),_["\u0275\u0275elementEnd"]()}if(2&e){const e=_["\u0275\u0275nextContext"](2).$implicit;_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](1,zt,null!=e.days[0]&&e.days[0].tasks&&(null==e.days[0]?null:e.days[0].tasks.length)>0||null!=e.days[0]&&e.days[0].sub_project&&(null==e.days[0]?null:e.days[0].sub_project.length)>0?"":"hidden"))}}function Gt(e,t){if(1&e){const e=_["\u0275\u0275getCurrentView"]();_["\u0275\u0275elementStart"](0,"mat-icon",42),_["\u0275\u0275listener"]("click",(function(){_["\u0275\u0275restoreView"](e);const t=_["\u0275\u0275nextContext"](2).$implicit;return t.expanded=!t.expanded})),_["\u0275\u0275text"](1,"add_box"),_["\u0275\u0275elementEnd"]()}if(2&e){const e=_["\u0275\u0275nextContext"](2).$implicit;_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](1,zt,null!=e.days[0]&&e.days[0].tasks&&(null==e.days[0]?null:e.days[0].tasks.length)>0||null!=e.days[0]&&e.days[0].sub_project&&(null==e.days[0]?null:e.days[0].sub_project.length)>0?"":"hidden"))}}function Ut(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",19),_["\u0275\u0275elementStart"](1,"p",20),_["\u0275\u0275text"](2),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2).$implicit;_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("matTooltip",(null!=e&&e.total_leaves?e.total_leaves:"0")+" Days"),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null!=e&&e.total_leaves?e.total_leaves:"0"," Days ")}}const Ht=function(e){return[e]};function Wt(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",36),_["\u0275\u0275pipe"](1,"hoursCalcPerColumnGroupedByCc"),_["\u0275\u0275text"](2),_["\u0275\u0275pipe"](3,"hoursCalcPerColumnGroupedByCc"),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2).index,t=_["\u0275\u0275nextContext"](2).$implicit;_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBind3"](1,2,_["\u0275\u0275pureFunction1"](10,Ht,t),2,e)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind3"](3,6,_["\u0275\u0275pureFunction1"](12,Ht,t),2,e)," ")}}function Qt(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",37),_["\u0275\u0275pipe"](1,"hoursCalcPerColumnGroupedByCc"),_["\u0275\u0275text"](2),_["\u0275\u0275pipe"](3,"hoursCalcPerColumnGroupedByCc"),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2).index,t=_["\u0275\u0275nextContext"](2).$implicit;_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBind3"](1,2,_["\u0275\u0275pureFunction1"](10,Ht,t),3,e)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind3"](3,6,_["\u0275\u0275pureFunction1"](12,Ht,t),3,e)," ")}}function Xt(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",38),_["\u0275\u0275pipe"](1,"hoursCalcPerColumnGroupedByCc"),_["\u0275\u0275text"](2),_["\u0275\u0275pipe"](3,"hoursCalcPerColumnGroupedByCc"),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2).index,t=_["\u0275\u0275nextContext"](2).$implicit;_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBind3"](1,2,_["\u0275\u0275pureFunction1"](10,Ht,t),4,e)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind3"](3,6,_["\u0275\u0275pureFunction1"](12,Ht,t),4,e)," ")}}function qt(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",22),_["\u0275\u0275template"](1,Wt,4,14,"p",33),_["\u0275\u0275pipe"](2,"reportActivePipe"),_["\u0275\u0275template"](3,Qt,4,14,"p",34),_["\u0275\u0275pipe"](4,"reportActivePipe"),_["\u0275\u0275template"](5,Xt,4,14,"p",35),_["\u0275\u0275pipe"](6,"reportActivePipe"),_["\u0275\u0275elementStart"](7,"p",26),_["\u0275\u0275pipe"](8,"hoursCalcPerColumnGroupedByCc"),_["\u0275\u0275text"](9),_["\u0275\u0275pipe"](10,"hoursCalcPerColumnGroupedByCc"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().index,t=_["\u0275\u0275nextContext"](2).$implicit,n=_["\u0275\u0275nextContext"](2);_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](2,5,n.columns,"billable")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](4,8,n.columns,"nonBillable")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](6,11,n.columns,"overtime")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBind3"](8,14,_["\u0275\u0275pureFunction1"](22,Ht,t),1,e)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind3"](10,18,_["\u0275\u0275pureFunction1"](24,Ht,t),1,e)," ")}}const Kt=function(e,t){return{color:e,"background-color":t}};function Jt(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",46),_["\u0275\u0275pipe"](1,"hexToRgb"),_["\u0275\u0275text"](2),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction2"](5,Kt,null==e?null:e.leave_color_code,_["\u0275\u0275pipeBind2"](1,2,null==e?null:e.leave_color_code,.1))),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",null==e?null:e.leave_name," ")}}function Zt(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",36),_["\u0275\u0275pipe"](1,"hoursCalcPerColumnGroupedByCc"),_["\u0275\u0275text"](2),_["\u0275\u0275pipe"](3,"hoursCalcPerColumnGroupedByCc"),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2).index,t=_["\u0275\u0275nextContext"](2).$implicit;_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBind3"](1,2,_["\u0275\u0275pureFunction1"](10,Ht,t),2,e)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind3"](3,6,_["\u0275\u0275pureFunction1"](12,Ht,t),2,e)," ")}}function en(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",37),_["\u0275\u0275pipe"](1,"hoursCalcPerColumnGroupedByCc"),_["\u0275\u0275text"](2),_["\u0275\u0275pipe"](3,"hoursCalcPerColumnGroupedByCc"),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2).index,t=_["\u0275\u0275nextContext"](2).$implicit;_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBind3"](1,2,_["\u0275\u0275pureFunction1"](10,Ht,t),3,e)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind3"](3,6,_["\u0275\u0275pureFunction1"](12,Ht,t),3,e)," ")}}function tn(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",38),_["\u0275\u0275pipe"](1,"hoursCalcPerColumnGroupedByCc"),_["\u0275\u0275text"](2),_["\u0275\u0275pipe"](3,"hoursCalcPerColumnGroupedByCc"),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2).index,t=_["\u0275\u0275nextContext"](2).$implicit;_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBind3"](1,2,_["\u0275\u0275pureFunction1"](10,Ht,t),4,e)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind3"](3,6,_["\u0275\u0275pureFunction1"](12,Ht,t),4,e)," ")}}function nn(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",47),_["\u0275\u0275elementStart"](1,"div",48),_["\u0275\u0275pipe"](2,"hexToRgb"),_["\u0275\u0275text"](3),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](4,"div",49),_["\u0275\u0275template"](5,Zt,4,14,"p",33),_["\u0275\u0275pipe"](6,"reportActivePipe"),_["\u0275\u0275template"](7,en,4,14,"p",34),_["\u0275\u0275pipe"](8,"reportActivePipe"),_["\u0275\u0275template"](9,tn,4,14,"p",35),_["\u0275\u0275pipe"](10,"reportActivePipe"),_["\u0275\u0275elementStart"](11,"p",26),_["\u0275\u0275pipe"](12,"hoursCalcPerColumnGroupedByCc"),_["\u0275\u0275text"](13),_["\u0275\u0275pipe"](14,"hoursCalcPerColumnGroupedByCc"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](),t=e.$implicit,n=e.index,o=_["\u0275\u0275nextContext"](2).$implicit,l=_["\u0275\u0275nextContext"](2);_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction2"](27,Kt,null==t?null:t.leave_color_code,_["\u0275\u0275pipeBind2"](2,7,null==t?null:t.leave_color_code,.1))),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",null==t?null:t.leave_name," "),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](6,10,l.columns,"billable")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](8,13,l.columns,"nonBillable")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](10,16,l.columns,"overtime")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBind3"](12,19,_["\u0275\u0275pureFunction1"](30,Ht,o),1,n)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind3"](14,23,_["\u0275\u0275pureFunction1"](32,Ht,o),1,n)," ")}}function on(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275template"](1,qt,11,26,"div",43),_["\u0275\u0275template"](2,Jt,3,8,"div",44),_["\u0275\u0275pipe"](3,"hoursCalcPerColumnGroupedByCc"),_["\u0275\u0275template"](4,nn,15,34,"div",45),_["\u0275\u0275pipe"](5,"hoursCalcPerColumnGroupedByCc"),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit,n=t.index,o=_["\u0275\u0275nextContext"](2).$implicit;_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",!(null!=e&&e.leave_id)),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",(null==e?null:e.leave_id)&&"00h 00m"==_["\u0275\u0275pipeBind3"](3,3,_["\u0275\u0275pureFunction1"](11,Ht,o),1,n)),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",(null==e?null:e.leave_id)&&"00h 00m"!=_["\u0275\u0275pipeBind3"](5,7,_["\u0275\u0275pureFunction1"](13,Ht,o),1,n))}}const ln=function(e){return{"border-bottom":e}};function rn(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",11),_["\u0275\u0275elementStart"](1,"div",55),_["\u0275\u0275elementStart"](2,"p",56),_["\u0275\u0275text"](3),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=t.index,o=_["\u0275\u0275nextContext"](2).$implicit;_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](3,ln,(null==o?null:o.tasks.length)-1!=n?"1px solid #f1f3f8;":""))("matTooltip",null!=e&&e.task_name?e.task_name:"NA"),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null!=e&&e.task_name?e.task_name:"NA"," ")}}function an(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275template"](1,rn,4,5,"div",54),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngForOf",null==e?null:e.tasks)}}function sn(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",11),_["\u0275\u0275elementStart"](1,"div",55),_["\u0275\u0275elementStart"](2,"p",56),_["\u0275\u0275text"](3),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=t.index,o=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](3,ln,(null==o?null:o.tasks.length)-1!=n?"1px solid #f1f3f8;":""))("matTooltip",null!=e&&e.task_name?e.task_name:"NA"),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null!=e&&e.task_name?e.task_name:"NA"," ")}}function cn(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275elementStart"](1,"div",11),_["\u0275\u0275elementStart"](2,"div",55),_["\u0275\u0275elementStart"](3,"p",57),_["\u0275\u0275text"](4),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275template"](5,sn,4,5,"div",54),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;_["\u0275\u0275advance"](3),_["\u0275\u0275property"]("matTooltip",null!=e&&e.sub_project_name?e.sub_project_name:"NA"),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null!=e&&e.sub_project_name?e.sub_project_name:"NA"," "),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngForOf",null==e?null:e.tasks)}}function pn(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275template"](1,cn,6,3,"ng-container",7),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngForOf",null==e?null:e.sub_project)}}function dn(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",14),_["\u0275\u0275elementStart"](1,"div",55),_["\u0275\u0275elementStart"](2,"p",59),_["\u0275\u0275pipe"](3,"hoursCalcPerCcGroupedByCc"),_["\u0275\u0275text"](4),_["\u0275\u0275pipe"](5,"hoursCalcPerCcGroupedByCc"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=t.index,n=_["\u0275\u0275nextContext"](2).$implicit,o=_["\u0275\u0275nextContext"](3).index,l=_["\u0275\u0275nextContext"]().index,i=_["\u0275\u0275nextContext"]();_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](11,ln,(null==n?null:n.tasks.length)-1!=e?"1px solid #f1f3f8;":""))("matTooltip",_["\u0275\u0275pipeBind3"](3,3,null==i.logData[l]||null==i.logData[l].employees[o]?null:i.logData[l].employees[o].days,-1,e)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind3"](5,7,null==i.logData[l]||null==i.logData[l].employees[o]?null:i.logData[l].employees[o].days,-1,e)," ")}}function un(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275template"](1,dn,6,13,"div",58),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngForOf",null==e?null:e.tasks)}}function mn(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",14),_["\u0275\u0275elementStart"](1,"div",55),_["\u0275\u0275elementStart"](2,"p",59),_["\u0275\u0275pipe"](3,"hoursCalcPerCcGroupedByCc"),_["\u0275\u0275text"](4),_["\u0275\u0275pipe"](5,"hoursCalcPerCcGroupedByCc"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=t.index,n=_["\u0275\u0275nextContext"](2),o=n.$implicit,l=n.index,i=_["\u0275\u0275nextContext"](5).index,r=_["\u0275\u0275nextContext"]().index,a=_["\u0275\u0275nextContext"]();_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](11,ln,(null==o?null:o.tasks.length)-1!=e?"1px solid #f1f3f8;":""))("matTooltip",_["\u0275\u0275pipeBind3"](3,3,null==a.logData[r]||null==a.logData[r].employees[i]?null:a.logData[r].employees[i].days,l,e)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind3"](5,7,null==a.logData[r]||null==a.logData[r].employees[i]?null:a.logData[r].employees[i].days,l,e)," ")}}function hn(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275template"](1,mn,6,13,"div",58),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngForOf",null==e?null:e.tasks)}}function vn(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275elementStart"](1,"div",14),_["\u0275\u0275elementStart"](2,"div",55),_["\u0275\u0275elementStart"](3,"p",15),_["\u0275\u0275pipe"](4,"hoursCalcPerCcGroupedByCc"),_["\u0275\u0275text"](5),_["\u0275\u0275pipe"](6,"hoursCalcPerCcGroupedByCc"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275template"](7,hn,2,1,"ng-container",17),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit,n=t.index,o=_["\u0275\u0275nextContext"](5).index,l=_["\u0275\u0275nextContext"]().index,i=_["\u0275\u0275nextContext"]();_["\u0275\u0275advance"](3),_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBind3"](4,3,null==i.logData[l]||null==i.logData[l].employees[o]?null:i.logData[l].employees[o].days,n,-1)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind3"](6,7,null==i.logData[l]||null==i.logData[l].employees[o]?null:i.logData[l].employees[o].days,n,-1)," "),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",(null==e?null:e.tasks)&&(null==e?null:e.tasks.length))}}function fn(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275template"](1,vn,8,11,"ng-container",7),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngForOf",null==e?null:e.sub_project)}}function xn(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",19),_["\u0275\u0275elementStart"](1,"div",55),_["\u0275\u0275elementStart"](2,"p",62),_["\u0275\u0275text"](3," - "),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=t.index,n=_["\u0275\u0275nextContext"](3).$implicit;_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](1,ln,(null==n?null:n.tasks.length)-1!=e?"1px solid #f1f3f8;":""))}}function gn(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275template"](1,xn,4,3,"div",61),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2).$implicit;_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngForOf",null==e?null:e.tasks)}}function _n(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",19),_["\u0275\u0275elementStart"](1,"div",55),_["\u0275\u0275elementStart"](2,"p",62),_["\u0275\u0275text"](3," - "),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=t.index,n=_["\u0275\u0275nextContext"](2).$implicit;_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](1,ln,(null==n?null:n.tasks.length)-1!=e?"1px solid #f1f3f8;":""))}}function yn(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275template"](1,_n,4,3,"div",61),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngForOf",null==e?null:e.tasks)}}function bn(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275elementStart"](1,"div",19),_["\u0275\u0275elementStart"](2,"div",55),_["\u0275\u0275elementStart"](3,"p",63),_["\u0275\u0275text"](4,"-"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275template"](5,yn,2,1,"ng-container",17),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;_["\u0275\u0275advance"](5),_["\u0275\u0275property"]("ngIf",(null==e?null:e.tasks)&&(null==e?null:e.tasks.length))}}function Cn(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275template"](1,bn,6,1,"ng-container",7),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2).$implicit;_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngForOf",null==e?null:e.sub_project)}}function Sn(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",60),_["\u0275\u0275template"](1,gn,2,1,"ng-container",17),_["\u0275\u0275template"](2,Cn,2,1,"ng-container",17),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",(null==e?null:e.tasks)&&(null==e?null:e.tasks.length)),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",(null==e?null:e.sub_project)&&(null==e?null:e.sub_project.length))}}function kn(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",68),_["\u0275\u0275text"](1),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2),t=e.index,n=e.$implicit,o=_["\u0275\u0275nextContext"](2).$implicit;_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](3,ln,(null==o?null:o.tasks.length)-1!=t?"1px solid #f1f3f8;":""))("matTooltip",null==n?null:n.total_billable_hours),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null==n?null:n.total_billable_hours," ")}}function In(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",69),_["\u0275\u0275text"](1),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2),t=e.index,n=e.$implicit,o=_["\u0275\u0275nextContext"](2).$implicit;_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](3,ln,(null==o?null:o.tasks.length)-1!=t?"1px solid #f1f3f8;":""))("matTooltip",null==n?null:n.total_non_billable_hours),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null==n?null:n.total_non_billable_hours," ")}}function wn(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",70),_["\u0275\u0275text"](1),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2),t=e.index,n=e.$implicit,o=_["\u0275\u0275nextContext"](2).$implicit;_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](3,ln,(null==o?null:o.tasks.length)-1!=t?"1px solid #f1f3f8;":""))("matTooltip",null==n?null:n.total_overtime_hours),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null==n?null:n.total_overtime_hours," ")}}function En(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",22),_["\u0275\u0275template"](1,kn,2,5,"p",64),_["\u0275\u0275pipe"](2,"reportActivePipe"),_["\u0275\u0275template"](3,In,2,5,"p",65),_["\u0275\u0275pipe"](4,"reportActivePipe"),_["\u0275\u0275template"](5,wn,2,5,"p",66),_["\u0275\u0275pipe"](6,"reportActivePipe"),_["\u0275\u0275elementStart"](7,"p",67),_["\u0275\u0275text"](8),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](),t=e.index,n=e.$implicit,o=_["\u0275\u0275nextContext"](2).$implicit,l=_["\u0275\u0275nextContext"](6);_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](2,6,l.columns,"billable")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](4,9,l.columns,"nonBillable")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](6,12,l.columns,"overtime")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](15,ln,(null==o?null:o.tasks.length)-1!=t?"1px solid #f1f3f8;":""))("matTooltip",null==n?null:n.total_hours),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null==n?null:n.total_hours," ")}}function On(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",46),_["\u0275\u0275pipe"](1,"hexToRgb"),_["\u0275\u0275text"](2),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction2"](5,Kt,null==e?null:e.leave_color_code,_["\u0275\u0275pipeBind2"](1,2,null==e?null:e.leave_color_code,.1))),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",null==e?null:e.leave_name," ")}}function Pn(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",68),_["\u0275\u0275text"](1),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2),t=e.index,n=e.$implicit,o=_["\u0275\u0275nextContext"](2).$implicit;_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](3,ln,(null==o?null:o.tasks.length)-1!=t?"1px solid #f1f3f8;":""))("matTooltip",null==n?null:n.total_billable_hours),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null==n?null:n.total_billable_hours," ")}}function Mn(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",69),_["\u0275\u0275text"](1),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2),t=e.index,n=e.$implicit,o=_["\u0275\u0275nextContext"](2).$implicit;_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](3,ln,(null==o?null:o.tasks.length)-1!=t?"1px solid #f1f3f8;":""))("matTooltip",null==n?null:n.total_non_billable_hours),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null==n?null:n.total_non_billable_hours," ")}}function Dn(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",70),_["\u0275\u0275text"](1),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2),t=e.index,n=e.$implicit,o=_["\u0275\u0275nextContext"](2).$implicit;_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](3,ln,(null==o?null:o.tasks.length)-1!=t?"1px solid #f1f3f8;":""))("matTooltip",null==n?null:n.total_overtime_hours),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null==n?null:n.total_overtime_hours," ")}}function jn(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",47),_["\u0275\u0275elementStart"](1,"div",48),_["\u0275\u0275pipe"](2,"hexToRgb"),_["\u0275\u0275text"](3),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](4,"div",49),_["\u0275\u0275template"](5,Pn,2,5,"p",64),_["\u0275\u0275pipe"](6,"reportActivePipe"),_["\u0275\u0275template"](7,Mn,2,5,"p",65),_["\u0275\u0275pipe"](8,"reportActivePipe"),_["\u0275\u0275template"](9,Dn,2,5,"p",66),_["\u0275\u0275pipe"](10,"reportActivePipe"),_["\u0275\u0275elementStart"](11,"p",67),_["\u0275\u0275text"](12),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](),t=e.$implicit,n=e.index,o=_["\u0275\u0275nextContext"](2).$implicit,l=_["\u0275\u0275nextContext"](6);_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction2"](20,Kt,null==t?null:t.leave_color_code,_["\u0275\u0275pipeBind2"](2,8,null==t?null:t.leave_color_code,.1))),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",null==t?null:t.leave_name," "),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](6,11,l.columns,"billable")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](8,14,l.columns,"nonBillable")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](10,17,l.columns,"overtime")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](23,ln,(null==o?null:o.tasks.length)-1!=n?"1px solid #f1f3f8;":""))("matTooltip",null==t?null:t.total_hours),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null==t?null:t.total_hours," ")}}function Tn(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275template"](1,En,9,17,"div",43),_["\u0275\u0275template"](2,On,3,8,"div",44),_["\u0275\u0275template"](3,jn,13,25,"div",45),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",!(null!=e&&e.leave_id)),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",(null==e?null:e.leave_id)&&"00h 00m"==(null==e?null:e.total_hours)),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",(null==e?null:e.leave_id)&&"00h 00m"!=(null==e?null:e.total_hours))}}function Fn(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275template"](1,Tn,4,3,"ng-container",7),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngForOf",null==e?null:e.tasks)}}function Bn(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",36),_["\u0275\u0275pipe"](1,"hoursCalcPerWorksteamColumnGroupedByCc"),_["\u0275\u0275text"](2),_["\u0275\u0275pipe"](3,"hoursCalcPerWorksteamColumnGroupedByCc"),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().index,t=_["\u0275\u0275nextContext"](2).$implicit;_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBind2"](1,2,_["\u0275\u0275pureFunction1"](8,Ht,null==t?null:t.sub_project[e]),2)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind2"](3,5,_["\u0275\u0275pureFunction1"](10,Ht,null==t?null:t.sub_project[e]),2)," ")}}function Yn(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",37),_["\u0275\u0275pipe"](1,"hoursCalcPerWorksteamColumnGroupedByCc"),_["\u0275\u0275text"](2),_["\u0275\u0275pipe"](3,"hoursCalcPerWorksteamColumnGroupedByCc"),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().index,t=_["\u0275\u0275nextContext"](2).$implicit;_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBind2"](1,2,_["\u0275\u0275pureFunction1"](8,Ht,null==t?null:t.sub_project[e]),3)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind2"](3,5,_["\u0275\u0275pureFunction1"](10,Ht,null==t?null:t.sub_project[e]),3)," ")}}function $n(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",38),_["\u0275\u0275pipe"](1,"hoursCalcPerWorksteamColumnGroupedByCc"),_["\u0275\u0275text"](2),_["\u0275\u0275pipe"](3,"hoursCalcPerWorksteamColumnGroupedByCc"),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().index,t=_["\u0275\u0275nextContext"](2).$implicit;_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBind2"](1,2,_["\u0275\u0275pureFunction1"](8,Ht,null==t?null:t.sub_project[e]),4)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind2"](3,5,_["\u0275\u0275pureFunction1"](10,Ht,null==t?null:t.sub_project[e]),4)," ")}}function Vn(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",68),_["\u0275\u0275text"](1),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2),t=e.index,n=e.$implicit,o=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](3,ln,(null==o?null:o.tasks.length)-1!=t?"1px solid #f1f3f8;":""))("matTooltip",null==n?null:n.total_billable_hours),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null==n?null:n.total_billable_hours," ")}}function An(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",69),_["\u0275\u0275text"](1),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2),t=e.index,n=e.$implicit,o=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](3,ln,(null==o?null:o.tasks.length)-1!=t?"1px solid #f1f3f8;":""))("matTooltip",null==n?null:n.total_non_billable_hours),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null==n?null:n.total_non_billable_hours," ")}}function Nn(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",70),_["\u0275\u0275text"](1),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2),t=e.index,n=e.$implicit,o=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](3,ln,(null==o?null:o.tasks.length)-1!=t?"1px solid #f1f3f8;":""))("matTooltip",null==n?null:n.total_overtime_hours),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null==n?null:n.total_overtime_hours," ")}}function Rn(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",22),_["\u0275\u0275template"](1,Vn,2,5,"p",64),_["\u0275\u0275pipe"](2,"reportActivePipe"),_["\u0275\u0275template"](3,An,2,5,"p",65),_["\u0275\u0275pipe"](4,"reportActivePipe"),_["\u0275\u0275template"](5,Nn,2,5,"p",66),_["\u0275\u0275pipe"](6,"reportActivePipe"),_["\u0275\u0275elementStart"](7,"p",67),_["\u0275\u0275text"](8),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](),t=e.index,n=e.$implicit,o=_["\u0275\u0275nextContext"]().$implicit,l=_["\u0275\u0275nextContext"](8);_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](2,6,l.columns,"billable")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](4,9,l.columns,"nonBillable")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](6,12,l.columns,"overtime")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](15,ln,(null==o?null:o.tasks.length)-1!=t?"1px solid #f1f3f8;":""))("matTooltip",null==n?null:n.total_hours),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null==n?null:n.total_hours," ")}}function zn(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",46),_["\u0275\u0275pipe"](1,"hexToRgb"),_["\u0275\u0275text"](2),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction2"](5,Kt,null==e?null:e.leave_color_code,_["\u0275\u0275pipeBind2"](1,2,null==e?null:e.leave_color_code,.1))),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",null==e?null:e.leave_name," ")}}function Ln(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",68),_["\u0275\u0275text"](1),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2),t=e.index,n=e.$implicit,o=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](3,ln,(null==o?null:o.tasks.length)-1!=t?"1px solid #f1f3f8;":""))("matTooltip",null==n?null:n.total_billable_hours),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null==n?null:n.total_billable_hours," ")}}function Gn(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",69),_["\u0275\u0275text"](1),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2),t=e.index,n=e.$implicit,o=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](3,ln,(null==o?null:o.tasks.length)-1!=t?"1px solid #f1f3f8;":""))("matTooltip",null==n?null:n.total_non_billable_hours),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null==n?null:n.total_non_billable_hours," ")}}function Un(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"p",70),_["\u0275\u0275text"](1),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2),t=e.index,n=e.$implicit,o=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](3,ln,(null==o?null:o.tasks.length)-1!=t?"1px solid #f1f3f8;":""))("matTooltip",null==n?null:n.total_overtime_hours),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null==n?null:n.total_overtime_hours," ")}}function Hn(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",47),_["\u0275\u0275elementStart"](1,"div",48),_["\u0275\u0275pipe"](2,"hexToRgb"),_["\u0275\u0275text"](3),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](4,"div",49),_["\u0275\u0275template"](5,Ln,2,5,"p",64),_["\u0275\u0275pipe"](6,"reportActivePipe"),_["\u0275\u0275template"](7,Gn,2,5,"p",65),_["\u0275\u0275pipe"](8,"reportActivePipe"),_["\u0275\u0275template"](9,Un,2,5,"p",66),_["\u0275\u0275pipe"](10,"reportActivePipe"),_["\u0275\u0275elementStart"](11,"p",67),_["\u0275\u0275text"](12),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](),t=e.$implicit,n=e.index,o=_["\u0275\u0275nextContext"]().$implicit,l=_["\u0275\u0275nextContext"](8);_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction2"](20,Kt,null==t?null:t.leave_color_code,_["\u0275\u0275pipeBind2"](2,8,null==t?null:t.leave_color_code,.1))),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",null==t?null:t.leave_name," "),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](6,11,l.columns,"billable")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](8,14,l.columns,"nonBillable")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](10,17,l.columns,"overtime")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](23,ln,(null==o?null:o.tasks.length)-1!=n?"1px solid #f1f3f8;":""))("matTooltip",null==t?null:t.total_hours),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null==t?null:t.total_hours," ")}}function Wn(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275template"](1,Rn,9,17,"div",43),_["\u0275\u0275template"](2,zn,3,8,"div",44),_["\u0275\u0275template"](3,Hn,13,25,"div",45),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",!(null!=e&&e.leave_id)),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",(null==e?null:e.leave_id)&&"00h 00m"==(null==e?null:e.total_hours)),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",(null==e?null:e.leave_id)&&"00h 00m"!=(null==e?null:e.total_hours))}}function Qn(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275elementStart"](1,"div",22),_["\u0275\u0275template"](2,Bn,4,12,"p",33),_["\u0275\u0275pipe"](3,"reportActivePipe"),_["\u0275\u0275template"](4,Yn,4,12,"p",34),_["\u0275\u0275pipe"](5,"reportActivePipe"),_["\u0275\u0275template"](6,$n,4,12,"p",35),_["\u0275\u0275pipe"](7,"reportActivePipe"),_["\u0275\u0275elementStart"](8,"p",26),_["\u0275\u0275pipe"](9,"hoursCalcPerWorksteamColumnGroupedByCc"),_["\u0275\u0275text"](10),_["\u0275\u0275pipe"](11,"hoursCalcPerWorksteamColumnGroupedByCc"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275template"](12,Wn,4,3,"ng-container",7),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit,n=t.index,o=_["\u0275\u0275nextContext"](2).$implicit,l=_["\u0275\u0275nextContext"](6);_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](3,6,l.columns,"billable")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](5,9,l.columns,"nonBillable")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",_["\u0275\u0275pipeBind2"](7,12,l.columns,"overtime")),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("matTooltip",_["\u0275\u0275pipeBind2"](9,15,_["\u0275\u0275pureFunction1"](21,Ht,null==o?null:o.sub_project[n]),1)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",_["\u0275\u0275pipeBind2"](11,18,_["\u0275\u0275pureFunction1"](23,Ht,null==o?null:o.sub_project[n]),1)," "),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngForOf",null==e?null:e.tasks)}}function Xn(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275template"](1,Qn,13,25,"ng-container",7),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngForOf",null==e?null:e.sub_project)}}function qn(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275elementStart"](1,"div"),_["\u0275\u0275template"](2,Fn,2,1,"ng-container",17),_["\u0275\u0275template"](3,Xn,2,1,"ng-container",17),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",(null==e?null:e.tasks)&&(null==e?null:e.tasks.length)),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",(null==e?null:e.sub_project)&&(null==e?null:e.sub_project.length))}}function Kn(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",10),_["\u0275\u0275elementStart"](1,"div",51),_["\u0275\u0275template"](2,an,2,1,"ng-container",17),_["\u0275\u0275template"](3,pn,2,1,"ng-container",17),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](4,"div",52),_["\u0275\u0275template"](5,un,2,1,"ng-container",17),_["\u0275\u0275template"](6,fn,2,1,"ng-container",17),_["\u0275\u0275elementEnd"](),_["\u0275\u0275template"](7,Sn,3,2,"div",53),_["\u0275\u0275template"](8,qn,4,2,"ng-container",7),_["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=_["\u0275\u0275nextContext"](3).$implicit,o=_["\u0275\u0275nextContext"](2);_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",(null==e?null:e.tasks)&&(null==e?null:e.tasks.length)),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",(null==e?null:e.sub_project)&&(null==e?null:e.sub_project.length)),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngIf",(null==e?null:e.tasks)&&(null==e?null:e.tasks.length)),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",(null==e?null:e.sub_project)&&(null==e?null:e.sub_project.length)),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",1!=o.currentViewType),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngForOf",null==n?null:n.days)}}function Jn(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275template"](1,Kn,9,6,"div",50),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2).$implicit;_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngForOf",_["\u0275\u0275pureFunction1"](1,Ht,null==e?null:e.days[0]))}}function Zn(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275elementStart"](1,"div",10),_["\u0275\u0275elementStart"](2,"div",11),_["\u0275\u0275template"](3,Lt,2,3,"mat-icon",39),_["\u0275\u0275template"](4,Gt,2,3,"mat-icon",39),_["\u0275\u0275element"](5,"app-user-image",40),_["\u0275\u0275elementStart"](6,"p",41),_["\u0275\u0275text"](7),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](8,"div",14),_["\u0275\u0275elementStart"](9,"p",15),_["\u0275\u0275text"](10),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275template"](11,Ut,3,2,"div",16),_["\u0275\u0275template"](12,on,6,15,"ng-container",7),_["\u0275\u0275elementEnd"](),_["\u0275\u0275template"](13,Jn,2,3,"ng-container",17),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().$implicit,t=_["\u0275\u0275nextContext"](2);_["\u0275\u0275advance"](3),_["\u0275\u0275property"]("ngIf",e.expanded),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",!e.expanded),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("id",e.employee_oid),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("matTooltip",null!=e&&e.employee_name?e.employee_name:"-"),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null!=e&&e.employee_name?e.employee_name:"-"," "),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("matTooltip",null!=e&&e.total_hours?e.total_hours:"00h 00m"),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null!=e&&e.total_hours?e.total_hours:"00h 00m"," "),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",1!=t.currentViewType),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngForOf",null==e?null:e.days),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",e.expanded)}}function eo(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275template"](1,Zn,14,10,"ng-container",17),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",e.expanded)}}function to(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275elementStart"](1,"div",10),_["\u0275\u0275elementStart"](2,"div",11),_["\u0275\u0275template"](3,Pt,2,0,"mat-icon",12),_["\u0275\u0275template"](4,Mt,2,0,"mat-icon",12),_["\u0275\u0275elementStart"](5,"p",13),_["\u0275\u0275text"](6),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](7,"div",14),_["\u0275\u0275elementStart"](8,"p",15),_["\u0275\u0275text"](9),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275template"](10,Dt,3,2,"div",16),_["\u0275\u0275template"](11,Yt,2,1,"ng-container",17),_["\u0275\u0275template"](12,Rt,2,1,"ng-container",17),_["\u0275\u0275elementEnd"](),_["\u0275\u0275template"](13,eo,2,1,"ng-container",7),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit,n=t.index,o=_["\u0275\u0275nextContext"]();_["\u0275\u0275advance"](3),_["\u0275\u0275property"]("ngIf",e.expanded),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",!e.expanded),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("matTooltip",(null!=e&&e.cc_name?e.cc_name:"NA")+" - "+(null!=e&&e.cc_desc?e.cc_desc:"NA")),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate2"](" ",null!=e&&e.cc_name?e.cc_name:"NA"," - ",null!=e&&e.cc_desc?e.cc_desc:"NA"," "),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("matTooltip",null!=e&&e.total_hours?e.total_hours:"00h 00m"),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",null!=e&&e.total_hours?e.total_hours:"00h 00m"," "),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",1!=o.currentViewType),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",0==n),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",0!=n),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngForOf",e.employees)}}let no=(()=>{class e{constructor(){this.limitEmit=new _.EventEmitter}ngOnInit(){}onScroll(){this.limit+=15,this.limitEmit.emit(this.limit)}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=_["\u0275\u0275defineComponent"]({type:e,selectors:[["app-ts-v2-daily-log-group-by-cc"]],inputs:{currentViewType:"currentViewType",logData:"logData",columns:"columns",limit:"limit"},outputs:{limitEmit:"limitEmit"},decls:10,vars:5,consts:[["infinite-scroll","",1,"main-container",3,"infiniteScrollDistance","scrollWindow","scrolled"],[1,"header-sticky"],[1,"row","header-row"],[1,"name-project-div-header"],[1,"total-hours-div-header"],["class","total-leaves-div-header",4,"ngIf"],["class","log-hours-div-header",4,"ngFor","ngForOf"],[4,"ngFor","ngForOf"],[1,"total-leaves-div-header"],[1,"log-hours-div-header"],[1,"row","item-row"],[1,"name-project-div"],["class","expand-icon",3,"click",4,"ngIf"],[1,"name-project-div-content",2,"margin-left","8px",3,"matTooltip"],[1,"total-hours-div"],[1,"total-hours-div-content",3,"matTooltip"],["class","total-leaves-div",4,"ngIf"],[4,"ngIf"],[1,"expand-icon",3,"click"],[1,"total-leaves-div"],[1,"total-leaves-div-content",3,"matTooltip"],["class","log-hours-div",4,"ngFor","ngForOf"],[1,"log-hours-div"],["class","col p-0",4,"ngIf"],[1,"col","p-0"],[1,"total-hours-text"],[1,"log-hours-div-content","total-hours","col","p-0",3,"matTooltip"],[1,"billable-text"],[1,"log-hours-div-content","billable-hours",3,"matTooltip"],[1,"non-billable-text"],[1,"log-hours-div-content","non-billable-hours",3,"matTooltip"],[1,"overtime-text"],[1,"log-hours-div-content","overtime-hours",3,"matTooltip"],["class","log-hours-div-content billable-hours col p-0",3,"matTooltip",4,"ngIf"],["class","log-hours-div-content non-billable-hours col p-0",3,"matTooltip",4,"ngIf"],["class","log-hours-div-content overtime-hours col p-0",3,"matTooltip",4,"ngIf"],[1,"log-hours-div-content","billable-hours","col","p-0",3,"matTooltip"],[1,"log-hours-div-content","non-billable-hours","col","p-0",3,"matTooltip"],[1,"log-hours-div-content","overtime-hours","col","p-0",3,"matTooltip"],["class","expand-icon","style","margin-left: 24px",3,"ngStyle","click",4,"ngIf"],["imgWidth","16px","imgHeight","16px",2,"margin-left","8px","margin-right","8px","margin-bottom","4px",3,"id"],[1,"name-project-div-content",3,"matTooltip"],[1,"expand-icon",2,"margin-left","24px",3,"ngStyle","click"],["class","log-hours-div",4,"ngIf"],["class","log-hours-div","style","align-items: center; justify-content: center",3,"ngStyle",4,"ngIf"],["style","display: flex; flex-direction: column",4,"ngIf"],[1,"log-hours-div",2,"align-items","center","justify-content","center",3,"ngStyle"],[2,"display","flex","flex-direction","column"],[1,"log-hours-div-half-day",2,"align-items","center","justify-content","center",3,"ngStyle"],[1,"log-hours-div-half-day"],["class","row item-row",4,"ngFor","ngForOf"],[1,"cc-sticky"],[1,"total-hours-sticky"],["class","total-leaves-sticky",4,"ngIf"],["class","name-project-div",4,"ngFor","ngForOf"],[1,"row"],[1,"task-name",3,"ngStyle","matTooltip"],[1,"sub-cc-name",3,"matTooltip"],["class","total-hours-div",4,"ngFor","ngForOf"],[1,"total-hours-div-content",3,"ngStyle","matTooltip"],[1,"total-leaves-sticky"],["class","total-leaves-div",4,"ngFor","ngForOf"],[1,"total-leaves-div-content",3,"ngStyle"],[1,"total-leaves-div-content"],["class","log-hours-div-content billable-hours col p-0",3,"ngStyle","matTooltip",4,"ngIf"],["class","log-hours-div-content non-billable-hours col p-0",3,"ngStyle","matTooltip",4,"ngIf"],["class","log-hours-div-content overtime-hours col p-0",3,"ngStyle","matTooltip",4,"ngIf"],[1,"log-hours-div-content","total-hours","col","p-0",3,"ngStyle","matTooltip"],[1,"log-hours-div-content","billable-hours","col","p-0",3,"ngStyle","matTooltip"],[1,"log-hours-div-content","non-billable-hours","col","p-0",3,"ngStyle","matTooltip"],[1,"log-hours-div-content","overtime-hours","col","p-0",3,"ngStyle","matTooltip"]],template:function(e,t){1&e&&(_["\u0275\u0275elementStart"](0,"div",0),_["\u0275\u0275listener"]("scrolled",(function(){return t.onScroll()})),_["\u0275\u0275elementStart"](1,"div",1),_["\u0275\u0275elementStart"](2,"div",2),_["\u0275\u0275elementStart"](3,"p",3),_["\u0275\u0275text"](4,"Name/Projects"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](5,"p",4),_["\u0275\u0275text"](6,"Total Hours"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275template"](7,Et,2,0,"p",5),_["\u0275\u0275template"](8,Ot,3,4,"p",6),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275template"](9,to,14,11,"ng-container",7),_["\u0275\u0275elementEnd"]()),2&e&&(_["\u0275\u0275property"]("infiniteScrollDistance",.1)("scrollWindow",!1),_["\u0275\u0275advance"](7),_["\u0275\u0275property"]("ngIf",1!=t.currentViewType),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngForOf",null==t.logData[0]||null==t.logData[0].employees[0]?null:t.logData[0].employees[0].days),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngForOf",t.logData))},directives:[Q.a,o.NgIf,o.NgForOf,g.a,m.a,I.a,o.NgStyle],pipes:[X,q,kt,ee,It,wt],styles:[".main-container[_ngcontent-%COMP%]{overflow:scroll;height:-webkit-fill-available}.header-row[_ngcontent-%COMP%]{-ms-overflow-style:none;scrollbar-width:none;overflow:unset}.header-row[_ngcontent-%COMP%], .item-row[_ngcontent-%COMP%]{flex-wrap:nowrap;float:left;min-width:-webkit-fill-available}.item-row[_ngcontent-%COMP%]{transition:all .25s linear;height:auto}.header-sticky[_ngcontent-%COMP%]{position:sticky;top:0;z-index:9999999}.expand-icon[_ngcontent-%COMP%]{font-size:18px;color:#7d838b;height:18px!important;width:18px!important;cursor:pointer}.name-project-div-header[_ngcontent-%COMP%]{min-width:300px;max-width:300px;left:0}.name-project-div-header[_ngcontent-%COMP%], .total-hours-div-header[_ngcontent-%COMP%]{display:inline;padding:10px;background-color:#e8e9ee;font-size:12px;font-weight:400;color:#7d838b;border-right:1px solid #f1f3f8;border-bottom:1px solid #f1f3f8;position:sticky;margin-bottom:0;height:42px}.total-hours-div-header[_ngcontent-%COMP%]{min-width:100px;max-width:100px;left:300px;text-align:center}.total-leaves-div-header[_ngcontent-%COMP%]{min-width:100px;max-width:100px;position:sticky;left:400px}.log-hours-div-header[_ngcontent-%COMP%], .total-leaves-div-header[_ngcontent-%COMP%]{display:inline;padding:10px;background-color:#e8e9ee;font-size:12px;font-weight:400;color:#7d838b;border-right:1px solid #f1f3f8;border-bottom:1px solid #f1f3f8;margin-bottom:0;height:42px;text-align:center}.log-hours-div-header[_ngcontent-%COMP%]{min-width:250px;max-width:250px}.name-project-div[_ngcontent-%COMP%]{display:flex;align-items:center;padding:10px;min-width:300px;max-width:300px;background-color:#fff;border-right:1px solid #f1f3f8;border-bottom:1px solid #f1f3f8;position:sticky;left:0;height:42px;z-index:1}.name-project-div-content[_ngcontent-%COMP%]{font-size:12px;font-weight:400;color:#111434;margin-bottom:0;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.total-hours-div[_ngcontent-%COMP%]{align-items:center;padding:10px;min-width:100px;max-width:100px;background-color:#fff;border-right:1px solid #f1f3f8;border-bottom:1px solid #f1f3f8;height:42px;position:sticky;left:300px;z-index:1;display:flex;justify-content:center}.total-hours-div-content[_ngcontent-%COMP%]{font-size:12px;font-weight:500;color:#111434;margin-bottom:0;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.total-leaves-div[_ngcontent-%COMP%]{align-items:center;padding:10px;min-width:100px;max-width:100px;background-color:#fff;border-right:1px solid #f1f3f8;border-bottom:1px solid #f1f3f8;height:42px;position:sticky;left:400px;z-index:1;display:flex;justify-content:center}.total-leaves-div-content[_ngcontent-%COMP%]{font-size:12px;font-weight:500;color:#111434;margin-bottom:0;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.log-hours-div[_ngcontent-%COMP%]{display:flex;min-width:250px;max-width:250px;background-color:#fff;border-right:1px solid #f1f3f8;border-bottom:1px solid #f1f3f8;height:42px}.log-hours-div-content[_ngcontent-%COMP%]{font-size:12px;font-weight:400;color:#111434;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;margin-bottom:0}.billable-hours[_ngcontent-%COMP%], .total-hours[_ngcontent-%COMP%]{display:flex;align-items:center;border-right:1px solid #f1f3f8;justify-content:center}.billable-hours[_ngcontent-%COMP%]{background-color:#eef9e8}.non-billable-hours[_ngcontent-%COMP%]{border-right:1px solid #f1f3f8;background-color:#e7f9f9}.non-billable-hours[_ngcontent-%COMP%], .overtime-hours[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center}.overtime-hours[_ngcontent-%COMP%]{background-color:#f1eafa}.total-hours-text[_ngcontent-%COMP%]{font-size:10px;color:#111434;font-weight:400;border:.25px solid var(--black-40,#a8acb2);margin-bottom:0;text-align:center}.billable-text[_ngcontent-%COMP%]{color:#2a7207;background-color:#eef9e8}.billable-text[_ngcontent-%COMP%], .non-billable-text[_ngcontent-%COMP%]{font-size:10px;font-weight:400;border:.25px solid var(--black-40,#a8acb2);margin-bottom:0;text-align:center;margin-right:1px}.non-billable-text[_ngcontent-%COMP%]{color:#13c2c2;background-color:#e7f9f9}.overtime-text[_ngcontent-%COMP%]{font-size:10px;color:#722ed1;font-weight:400;border:.25px solid var(--black-40,#a8acb2);margin-bottom:0;text-align:center;background-color:#f1eafa}.cc-name[_ngcontent-%COMP%]{font-weight:500;color:#111434;margin-left:18px;width:250px}.cc-name[_ngcontent-%COMP%], .sub-cc-name[_ngcontent-%COMP%]{font-size:12px;margin-bottom:0;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.sub-cc-name[_ngcontent-%COMP%]{font-weight:400;color:#7d838b;margin-left:50px;width:230px}.task-name[_ngcontent-%COMP%]{font-size:12px;font-weight:400;color:#ee4961;margin-left:75px;margin-bottom:0;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;width:220px}.cc-sticky[_ngcontent-%COMP%]{position:sticky;left:0;z-index:1}.total-hours-sticky[_ngcontent-%COMP%]{position:sticky;left:300px;z-index:1}.total-leaves-sticky[_ngcontent-%COMP%]{position:sticky;left:400px;z-index:1}.log-hours-div-half-day[_ngcontent-%COMP%]{display:flex;min-width:250px;max-width:250px;background-color:#fff;border-right:1px solid #f1f3f8;border-bottom:1px solid #f1f3f8;height:21px}"]}),e})();var oo=n("Xa2L"),lo=n("QibW");function io(e,t){if(1&e){const e=_["\u0275\u0275getCurrentView"]();_["\u0275\u0275elementStart"](0,"div",36),_["\u0275\u0275listener"]("click",(function(){return _["\u0275\u0275restoreView"](e),_["\u0275\u0275nextContext"](2).downloadTimesheetCalendarReport()})),_["\u0275\u0275elementStart"](1,"p",37),_["\u0275\u0275text"](2,"Download"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](3,"mat-icon",38),_["\u0275\u0275text"](4,"download"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()}if(2&e){const e=_["\u0275\u0275nextContext"](2);_["\u0275\u0275classProp"]("disabled",e.disableDownloadButton)}}function ro(e,t){if(1&e){const e=_["\u0275\u0275getCurrentView"]();_["\u0275\u0275elementStart"](0,"div",39),_["\u0275\u0275listener"]("click",(function(){return _["\u0275\u0275restoreView"](e),_["\u0275\u0275nextContext"](2).scheduleReport()})),_["\u0275\u0275elementStart"](1,"mat-icon",40),_["\u0275\u0275text"](2,"send"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()}}function ao(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",41),_["\u0275\u0275elementStart"](1,"mat-icon",42),_["\u0275\u0275text"](2,"settings"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){_["\u0275\u0275nextContext"](2);const e=_["\u0275\u0275reference"](13);_["\u0275\u0275property"]("matMenuTriggerFor",e)}}function so(e,t){if(1&e){const e=_["\u0275\u0275getCurrentView"]();_["\u0275\u0275elementStart"](0,"div"),_["\u0275\u0275element"](1,"input",43),_["\u0275\u0275elementStart"](2,"mat-datepicker-toggle",44),_["\u0275\u0275elementStart"](3,"mat-icon",45),_["\u0275\u0275text"](4,"calendar_today"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](5,"mat-datepicker",46,47),_["\u0275\u0275listener"]("monthSelected",(function(t){_["\u0275\u0275restoreView"](e);const n=_["\u0275\u0275reference"](6);return _["\u0275\u0275nextContext"](2).onSelectMonthPicker(t,n)})),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()}if(2&e){const e=_["\u0275\u0275reference"](6),t=_["\u0275\u0275nextContext"](2);_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("matDatepicker",e)("formControl",t.month),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("for",e),_["\u0275\u0275advance"](3),_["\u0275\u0275property"]("startAt",t.startDate)}}function co(e,t){if(1&e){const e=_["\u0275\u0275getCurrentView"]();_["\u0275\u0275elementStart"](0,"div"),_["\u0275\u0275element"](1,"input",43),_["\u0275\u0275elementStart"](2,"mat-datepicker-toggle",44),_["\u0275\u0275elementStart"](3,"mat-icon",45),_["\u0275\u0275text"](4,"calendar_today"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](5,"mat-datepicker",48,47),_["\u0275\u0275listener"]("yearSelected",(function(t){_["\u0275\u0275restoreView"](e);const n=_["\u0275\u0275reference"](6);return _["\u0275\u0275nextContext"](2).onSelectYearPicker(t,n)})),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()}if(2&e){const e=_["\u0275\u0275reference"](6),t=_["\u0275\u0275nextContext"](2);_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("matDatepicker",e)("formControl",t.month),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("for",e),_["\u0275\u0275advance"](3),_["\u0275\u0275property"]("startAt",t.startDate)}}const po=function(e){return{"pointer-events":e}};function uo(e,t){if(1&e){const e=_["\u0275\u0275getCurrentView"]();_["\u0275\u0275elementStart"](0,"div",49),_["\u0275\u0275elementStart"](1,"mat-icon",50),_["\u0275\u0275listener"]("click",(function(){return _["\u0275\u0275restoreView"](e),_["\u0275\u0275nextContext"](2).openUdrfModal()})),_["\u0275\u0275text"](2,"filter_list"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()}if(2&e){const e=_["\u0275\u0275nextContext"](2);_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](1,po,e.apiInProgress?"none":""))}}function mo(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",49),_["\u0275\u0275elementStart"](1,"mat-icon",51),_["\u0275\u0275text"](2,"view_compact"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){_["\u0275\u0275nextContext"](2);const e=_["\u0275\u0275reference"](10);_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("matMenuTriggerFor",e)}}function ho(e,t){if(1&e){const e=_["\u0275\u0275getCurrentView"]();_["\u0275\u0275elementStart"](0,"mat-icon",65),_["\u0275\u0275listener"]("click",(function(){_["\u0275\u0275restoreView"](e);const t=_["\u0275\u0275nextContext"](2).index;return _["\u0275\u0275nextContext"](3).viewdroplist(t)})),_["\u0275\u0275text"](1,"keyboard_arrow_down"),_["\u0275\u0275elementEnd"]()}}function vo(e,t){if(1&e){const e=_["\u0275\u0275getCurrentView"]();_["\u0275\u0275elementStart"](0,"mat-icon",65),_["\u0275\u0275listener"]("click",(function(){return _["\u0275\u0275restoreView"](e),_["\u0275\u0275nextContext"](5).closedroplist()})),_["\u0275\u0275text"](1,"keyboard_arrow_up"),_["\u0275\u0275elementEnd"]()}}function fo(e,t){if(1&e){const e=_["\u0275\u0275getCurrentView"]();_["\u0275\u0275elementStart"](0,"div"),_["\u0275\u0275elementStart"](1,"div",66),_["\u0275\u0275elementStart"](2,"p",67),_["\u0275\u0275text"](3),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](4,"mat-checkbox",68),_["\u0275\u0275listener"]("change",(function(n){_["\u0275\u0275restoreView"](e);const o=t.index,l=t.$implicit,i=_["\u0275\u0275nextContext"](2).index;return _["\u0275\u0275nextContext"](3).checkboxvalue(n.checked,i,o,l.checkboxName,l.checkboxId)})),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;_["\u0275\u0275advance"](3),_["\u0275\u0275textInterpolate"](e.checkboxName),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("checked",e.isCheckboxSelected)}}function xo(e,t){if(1&e){const e=_["\u0275\u0275getCurrentView"]();_["\u0275\u0275elementStart"](0,"div",57),_["\u0275\u0275elementStart"](1,"div",58),_["\u0275\u0275elementStart"](2,"span",59),_["\u0275\u0275text"](3),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](4,"span",60),_["\u0275\u0275elementStart"](5,"span",61),_["\u0275\u0275text"](6),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](7,"mat-icon",62),_["\u0275\u0275listener"]("click",(function(){_["\u0275\u0275restoreView"](e);const t=_["\u0275\u0275nextContext"](),n=t.$implicit,o=t.index;return _["\u0275\u0275nextContext"](3).clearonefilter(n,o)})),_["\u0275\u0275text"](8,"clear"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275template"](9,ho,2,0,"mat-icon",63),_["\u0275\u0275template"](10,vo,2,0,"mat-icon",63),_["\u0275\u0275elementStart"](11,"mat-card",64),_["\u0275\u0275template"](12,fo,5,2,"div",54),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()}if(2&e){const e=_["\u0275\u0275nextContext"](),t=e.$implicit,n=e.index,o=_["\u0275\u0275nextContext"](3);_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("matTooltip",t.filterName),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate"](t.filterName),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("matTooltip",t.multiOptionSelectSearchValues[0]),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate"](t.multiOptionSelectSearchValues[0]),_["\u0275\u0275advance"](3),_["\u0275\u0275property"]("ngIf",!o.dropdownflag),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",o.dropdownflag),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngClass",o.dropdownflag&&n==o.selecteddropdown?"droplistvisible":"droplisthidden"),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngForOf",t.checkboxValues)}}function go(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div"),_["\u0275\u0275template"](1,xo,13,8,"div",56),_["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=t.index,o=_["\u0275\u0275nextContext"](3);_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",e.multiOptionSelectSearchValues[0]&&o.displayedFilter==n)}}function _o(e,t){if(1&e){const e=_["\u0275\u0275getCurrentView"]();_["\u0275\u0275elementStart"](0,"div",52),_["\u0275\u0275elementStart"](1,"mat-icon",53),_["\u0275\u0275listener"]("click",(function(){return _["\u0275\u0275restoreView"](e),_["\u0275\u0275nextContext"](2).goToPreviousFilter()})),_["\u0275\u0275text"](2,"keyboard_arrow_left"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275template"](3,go,2,1,"div",54),_["\u0275\u0275elementStart"](4,"mat-icon",55),_["\u0275\u0275listener"]("click",(function(){return _["\u0275\u0275restoreView"](e),_["\u0275\u0275nextContext"](2).goToNextFilter()})),_["\u0275\u0275text"](5,"keyboard_arrow_right"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()}if(2&e){const e=_["\u0275\u0275nextContext"](2);_["\u0275\u0275advance"](3),_["\u0275\u0275property"]("ngForOf",e._udrfService.udrfData.mainFilterArray)}}function yo(e,t){if(1&e){const e=_["\u0275\u0275getCurrentView"]();_["\u0275\u0275elementStart"](0,"div",69),_["\u0275\u0275elementStart"](1,"button",70),_["\u0275\u0275listener"]("click",(function(){return _["\u0275\u0275restoreView"](e),_["\u0275\u0275nextContext"](2).onClear()})),_["\u0275\u0275text"](2," Clear "),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()}}const bo=function(e){return{"border-left":e}};function Co(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",73),_["\u0275\u0275elementStart"](1,"div",74),_["\u0275\u0275text"](2),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](3,"div"),_["\u0275\u0275text"](4),_["\u0275\u0275elementStart"](5,"mat-icon",75),_["\u0275\u0275text"](6),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](6,bo,e.borderLeft)),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate"](e.text),_["\u0275\u0275advance"](1),_["\u0275\u0275classMap"](e.class),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate1"](" ",e.value," "),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate"](e.icon)}}function So(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275template"](1,Co,7,8,"div",72),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",e.isActive&&e.isCheckBoxSelected)}}function ko(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",71),_["\u0275\u0275template"](1,So,2,1,"ng-container",54),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2);_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngForOf",e.summaryCards)}}function Io(e,t){if(1&e){const e=_["\u0275\u0275getCurrentView"]();_["\u0275\u0275elementStart"](0,"app-ts-v2-daily-log-main",78),_["\u0275\u0275listener"]("limitEmit",(function(t){return _["\u0275\u0275restoreView"](e),_["\u0275\u0275nextContext"](3).onScrollFetchData(t)})),_["\u0275\u0275elementEnd"]()}if(2&e){const e=_["\u0275\u0275nextContext"](3);_["\u0275\u0275property"]("currentViewType",e.currentViewType)("logData",e.logData)("columns",e.columns)("limit",e.limit)}}function wo(e,t){if(1&e){const e=_["\u0275\u0275getCurrentView"]();_["\u0275\u0275elementStart"](0,"app-ts-v2-daily-log-group-by-cc",78),_["\u0275\u0275listener"]("limitEmit",(function(t){return _["\u0275\u0275restoreView"](e),_["\u0275\u0275nextContext"](3).onScrollFetchData(t)})),_["\u0275\u0275elementEnd"]()}if(2&e){const e=_["\u0275\u0275nextContext"](3);_["\u0275\u0275property"]("currentViewType",e.currentViewType)("logData",e.logData)("columns",e.columns)("limit",e.limit)}}function Eo(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",76),_["\u0275\u0275template"](1,Io,1,4,"app-ts-v2-daily-log-main",77),_["\u0275\u0275template"](2,wo,1,4,"app-ts-v2-daily-log-group-by-cc",77),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"](2);_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",1==e.currentGroupBy),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",2==e.currentGroupBy)}}function Oo(e,t){1&e&&(_["\u0275\u0275elementStart"](0,"div",79),_["\u0275\u0275element"](1,"mat-spinner",80),_["\u0275\u0275elementEnd"]())}function Po(e,t){1&e&&(_["\u0275\u0275elementStart"](0,"div",81),_["\u0275\u0275elementStart"](1,"div",82),_["\u0275\u0275text"](2,"Loading..."),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]())}function Mo(e,t){1&e&&(_["\u0275\u0275elementStart"](0,"div",83),_["\u0275\u0275element"](1,"img",84),_["\u0275\u0275elementEnd"]())}function Do(e,t){if(1&e){const e=_["\u0275\u0275getCurrentView"]();_["\u0275\u0275elementStart"](0,"div",8),_["\u0275\u0275elementStart"](1,"div",9),_["\u0275\u0275elementStart"](2,"div",10),_["\u0275\u0275elementStart"](3,"div",11),_["\u0275\u0275listener"]("click",(function(){return _["\u0275\u0275restoreView"](e),_["\u0275\u0275nextContext"]().goToReportsMainScreen()})),_["\u0275\u0275elementStart"](4,"mat-icon",12),_["\u0275\u0275text"](5,"chevron_left"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275text"](6," Back "),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](7,"div",13),_["\u0275\u0275text"](8,"Timesheet Daily Log Report - Calendar View"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](9,"div",14),_["\u0275\u0275elementStart"](10,"div",15),_["\u0275\u0275elementStart"](11,"div",16),_["\u0275\u0275elementStart"](12,"p",17),_["\u0275\u0275text"](13),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](14,"mat-icon",18),_["\u0275\u0275text"](15,"expand_more"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](16,"div",15),_["\u0275\u0275elementStart"](17,"div",16),_["\u0275\u0275elementStart"](18,"p",17),_["\u0275\u0275text"](19,"Column"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](20,"mat-icon",18),_["\u0275\u0275text"](21,"expand_more"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275template"](22,io,5,2,"div",19),_["\u0275\u0275template"](23,ro,3,0,"div",20),_["\u0275\u0275template"](24,ao,3,1,"div",21),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](25,"div",22),_["\u0275\u0275elementStart"](26,"div",23),_["\u0275\u0275elementStart"](27,"div",24),_["\u0275\u0275text"](28,"Duration"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](29,"div",25),_["\u0275\u0275elementStart"](30,"div",26),_["\u0275\u0275text"](31),_["\u0275\u0275elementEnd"](),_["\u0275\u0275template"](32,so,7,4,"div",27),_["\u0275\u0275template"](33,co,7,4,"div",27),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275template"](34,uo,3,3,"div",28),_["\u0275\u0275template"](35,mo,3,1,"div",28),_["\u0275\u0275template"](36,_o,6,1,"div",29),_["\u0275\u0275template"](37,yo,3,0,"div",30),_["\u0275\u0275elementEnd"](),_["\u0275\u0275template"](38,ko,2,1,"div",31),_["\u0275\u0275template"](39,Eo,3,2,"div",32),_["\u0275\u0275template"](40,Oo,2,0,"div",33),_["\u0275\u0275template"](41,Po,3,0,"div",34),_["\u0275\u0275template"](42,Mo,2,0,"div",35),_["\u0275\u0275elementEnd"]()}if(2&e){const e=_["\u0275\u0275nextContext"](),t=_["\u0275\u0275reference"](4),n=_["\u0275\u0275reference"](7);_["\u0275\u0275advance"](10),_["\u0275\u0275property"]("matMenuTriggerFor",t),_["\u0275\u0275advance"](3),_["\u0275\u0275textInterpolate"](e.currentDisplayValue),_["\u0275\u0275advance"](3),_["\u0275\u0275property"]("matMenuTriggerFor",n),_["\u0275\u0275advance"](6),_["\u0275\u0275property"]("ngIf",e.downloadVisible),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",e.scheduleVisible),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",e.settingsVisible),_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](20,po,e.apiInProgress?"none":"")),_["\u0275\u0275advance"](5),_["\u0275\u0275textInterpolate2"](" ",e.currentSelectedStartDate," - ",e.currentSelectedEndDate," "),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",3!=e.currentViewType),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",3==e.currentViewType),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",e.filterVisible),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",e.groupByVisible),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",e._udrfService.udrfData.mainFilterArray.length>0),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",e._udrfService.udrfData.mainFilterArray.length>0),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",e.summaryCardVisible),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",e.logData.length>0),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",0==e.logData.length&&e.apiInProgress),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",e.logData.length>0&&e.apiInProgress),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",0==e.logData.length&&!e.apiInProgress)}}function jo(e,t){1&e&&(_["\u0275\u0275elementStart"](0,"div",85),_["\u0275\u0275element"](1,"mat-spinner",80),_["\u0275\u0275elementEnd"]())}function To(e,t){if(1&e){const e=_["\u0275\u0275getCurrentView"]();_["\u0275\u0275elementStart"](0,"mat-radio-button",90),_["\u0275\u0275listener"]("change",(function(t){return _["\u0275\u0275restoreView"](e),_["\u0275\u0275nextContext"](2).handleChangeInViewType(t)})),_["\u0275\u0275text"](1),_["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=_["\u0275\u0275nextContext"](2);_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](4,po,n.apiInProgress?"none":""))("value",e.value)("checked",e.value===n.currentViewType),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate"](e.displayValue)}}function Fo(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",86),_["\u0275\u0275listener"]("click",(function(e){return e.stopPropagation()})),_["\u0275\u0275elementStart"](1,"p",87),_["\u0275\u0275text"](2,"GRID PERIODS"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](3,"mat-radio-group",88),_["\u0275\u0275template"](4,To,2,6,"mat-radio-button",89),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]();_["\u0275\u0275advance"](4),_["\u0275\u0275property"]("ngForOf",e.viewType)}}function Bo(e,t){if(1&e){const e=_["\u0275\u0275getCurrentView"]();_["\u0275\u0275elementStart"](0,"div",10),_["\u0275\u0275elementStart"](1,"div",95),_["\u0275\u0275elementStart"](2,"mat-checkbox",96),_["\u0275\u0275listener"]("ngModelChange",(function(){_["\u0275\u0275restoreView"](e);const t=_["\u0275\u0275nextContext"]().index;return _["\u0275\u0275nextContext"](2).onCheckColumnConfig(t)})),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](3,"div",97),_["\u0275\u0275text"](4),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()}if(2&e){const e=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](4,po,e.isDefaultSelected?"none":"")),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngModel",e.isCheckBoxSelected)("name",e.value),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",e.text," ")}}function Yo(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275template"](1,Bo,5,6,"div",94),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",e.isActive)}}function $o(e,t){1&e&&(_["\u0275\u0275elementStart"](0,"p",98),_["\u0275\u0275text"](1," SUMMARY "),_["\u0275\u0275elementEnd"]())}function Vo(e,t){if(1&e){const e=_["\u0275\u0275getCurrentView"]();_["\u0275\u0275elementStart"](0,"div",10),_["\u0275\u0275elementStart"](1,"div",95),_["\u0275\u0275elementStart"](2,"mat-checkbox",96),_["\u0275\u0275listener"]("ngModelChange",(function(){_["\u0275\u0275restoreView"](e);const t=_["\u0275\u0275nextContext"]().index;return _["\u0275\u0275nextContext"](2).onCheckSummaryConfig(t)})),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](3,"div",97),_["\u0275\u0275text"](4),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()}if(2&e){const e=_["\u0275\u0275nextContext"]().$implicit;_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](4,po,e.isDefaultSelected?"none":"")),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngModel",e.isCheckBoxSelected)("name",e.value),_["\u0275\u0275advance"](2),_["\u0275\u0275textInterpolate1"](" ",e.text," ")}}function Ao(e,t){if(1&e&&(_["\u0275\u0275elementContainerStart"](0),_["\u0275\u0275template"](1,Vo,5,6,"div",94),_["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit,n=_["\u0275\u0275nextContext"](2);_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",e.isActive&&n.summaryCardVisible)}}function No(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",91),_["\u0275\u0275listener"]("click",(function(e){return e.stopPropagation()})),_["\u0275\u0275elementStart"](1,"p",92),_["\u0275\u0275text"](2,"COLUMN"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275template"](3,Yo,2,1,"ng-container",54),_["\u0275\u0275template"](4,$o,2,0,"p",93),_["\u0275\u0275template"](5,Ao,2,1,"ng-container",54),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]();_["\u0275\u0275advance"](3),_["\u0275\u0275property"]("ngForOf",e.columns),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",e.summaryCardVisible),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngForOf",e.summaryCards)}}function Ro(e,t){if(1&e){const e=_["\u0275\u0275getCurrentView"]();_["\u0275\u0275elementStart"](0,"mat-radio-button",90),_["\u0275\u0275listener"]("change",(function(t){return _["\u0275\u0275restoreView"](e),_["\u0275\u0275nextContext"](2).handleChangeInViewGroupBy(t)})),_["\u0275\u0275text"](1),_["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=_["\u0275\u0275nextContext"](2);_["\u0275\u0275property"]("ngStyle",_["\u0275\u0275pureFunction1"](4,po,n.apiInProgress?"none":""))("value",e.value)("checked",e.value===n.currentGroupBy),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate"](e.displayValue)}}function zo(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",86),_["\u0275\u0275listener"]("click",(function(e){return e.stopPropagation()})),_["\u0275\u0275elementStart"](1,"p",87),_["\u0275\u0275text"](2,"GROUP BY"),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](3,"mat-radio-group",88),_["\u0275\u0275template"](4,Ro,2,6,"mat-radio-button",89),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]();_["\u0275\u0275advance"](4),_["\u0275\u0275property"]("ngForOf",e.groupByList)}}function Lo(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"mat-radio-button",100),_["\u0275\u0275text"](1),_["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;_["\u0275\u0275property"]("checked",1===e.value)("value",e.value),_["\u0275\u0275advance"](1),_["\u0275\u0275textInterpolate"](e.text)}}function Go(e,t){if(1&e&&(_["\u0275\u0275elementStart"](0,"div",86),_["\u0275\u0275listener"]("click",(function(e){return e.stopPropagation()})),_["\u0275\u0275elementStart"](1,"mat-radio-group",88),_["\u0275\u0275template"](2,Lo,2,3,"mat-radio-button",99),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementEnd"]()),2&e){const e=_["\u0275\u0275nextContext"]();_["\u0275\u0275advance"](2),_["\u0275\u0275property"]("ngForOf",e.settingsArray)}}const Uo={parse:{dateInput:"MMM YYYY"},display:{dateInput:"MMM YYYY",monthYearLabel:"MMM YYYY",dateA11yLabel:"LL",monthYearA11yLabel:"MMMM YYYY"}},Ho=[{path:"",component:(()=>{class e{constructor(e,t,n,o,l,i,r){this.router=e,this.toastService=t,this.reportService=n,this.authService=o,this._dialog=l,this._udrfService=i,this.excelService=r,this.limit=15,this.logData=[],this.apiInProgress=!0,this.apiDownloadInProgress=!0,this.noDataFound=!1,this.initialSpinner=!0,this.applicationId=600,this.dropdownflag=!1,this.displayedFilter=0,this.startDate=new Date,this.month=new d.j(s()),this.summaryCardVisible=!0,this.filterVisible=!0,this.groupByVisible=!0,this.downloadVisible=!0,this.scheduleVisible=!0,this.settingsVisible=!0,this.currentViewType=1,this.currentDisplayValue="Days",this.viewType=[{value:1,displayValue:"Days"},{value:2,displayValue:"Weeks"},{value:3,displayValue:"Months"}],this.columns=[],this.summaryCards=[],this.currentGroupBy=1,this.groupByList=[],this.settingsArray=[],this.dateLimit=30,this.dateRangePickerRanges={"This Week":[s().startOf("week").add(1,"day"),s().endOf("week").add(1,"day")],"This Month":[s().startOf("month"),s().endOf("month")],"Previous Month":[s().subtract(1,"month").startOf("month"),s().subtract(1,"month").endOf("month")]},this.disableDownloadButton=!1,this.summaryApiInProgress=!0}ngOnInit(){return Object(i.c)(this,void 0,void 0,(function*(){yield this.getTimesheetReportAccess(),this.calculateDynamicContentHeight(),yield this.getReportConfigurationsBasedOnId(),this.currentUser=yield this.authService.getProfile().profile,this.oid=this.currentUser.oid,this.aid=this.currentUser.aid,yield this.getBasicTimesheetConfigurations(),yield this.calculateDateLogic(s().format("YYYY-MM-DD")),this.initialSpinner=!1,yield this.configureUdrf()}))}onResize(){this.calculateDynamicContentHeight()}calculateDynamicContentHeight(){this.dynamicHeight=window.innerHeight-104+"px",document.documentElement.style.setProperty("--dynamicHeight",this.dynamicHeight),this.dynamicReportHeight=window.innerHeight-412+(this.summaryCardVisible?96:0)+"px",document.documentElement.style.setProperty("--dynamicReportHeight",this.dynamicReportHeight)}goToReportsMainScreen(){this.router.navigate(["/main/reports"])}onSelectMonthPicker(e,t){return Object(i.c)(this,void 0,void 0,(function*(){t.close(),this.limit=15,this.noDataFound=!1,this.logData=[];let n=this.month.value;n.month(e.month()),n.year(e.year()),this.month.setValue(n),yield this.calculateDateLogic(s(e).format("YYYY-MM-DD"))}))}onSelectYearPicker(e,t){return Object(i.c)(this,void 0,void 0,(function*(){t.close(),this.limit=15,this.noDataFound=!1,this.logData=[];let n=this.month.value;n.month(e.month()),n.year(e.year()),this.month.setValue(n),yield this.calculateDateLogic(s(e).format("YYYY-MM-DD"))}))}handleChangeInViewType(e){var t;return Object(i.c)(this,void 0,void 0,(function*(){this.limit=15,this.noDataFound=!1,this.logData=[];let n=this.month.value;n.month(s().month()),n.year(s().year()),this.month.setValue(n),this.currentViewType=e.value,this.currentDisplayValue=null===(t=this.viewType[e.value-1])||void 0===t?void 0:t.displayValue,yield this.fetchDateLimit(this.currentViewType),yield this.calculateDateLogic(s().format("YYYY-MM-DD"))}))}handleChangeInViewGroupBy(e){return Object(i.c)(this,void 0,void 0,(function*(){this.limit=15,this.noDataFound=!1,this.logData=[];let t=this.month.value;t.month(s().month()),t.year(s().year()),this.month.setValue(t),this.currentGroupBy=e.value,yield this.calculateDateLogic(s().format("YYYY-MM-DD"))}))}onScrollFetchData(e){return Object(i.c)(this,void 0,void 0,(function*(){this.limit=e,this.logData.length%15!=0||this.noDataFound||(yield this.getTimesheetQueryReportItemData())}))}changeInDateRange(){return Object(i.c)(this,void 0,void 0,(function*(){this.limit=15,this.noDataFound=!1,this.logData=[],yield this.getTimesheetQueryReportItemData(),yield this.getTimesheetSummaryData()}))}fetchDateLimit(e){return Object(i.c)(this,void 0,void 0,(function*(){1==e||2==e?this.dateLimit=30:3==e&&(this.dateLimit=365)}))}calculateDateLogic(e){var t,n,o,l,r,a,c;return Object(i.c)(this,void 0,void 0,(function*(){3!=this.currentViewType&&("END"==(null===(t=this.basicTsConfig)||void 0===t?void 0:t.timesheet_month_end_date)?(this.currentSelectedStartDate=s(e).startOf("month").format("DD/MM/YYYY"),this.currentSelectedEndDate=s(e).endOf("month").format("DD/MM/YYYY"),yield this.getTimesheetQueryReportItemData(),yield this.getTimesheetSummaryData()):(s(e).date()>parseInt(null===(n=this.basicTsConfig)||void 0===n?void 0:n.timesheet_month_end_date)&&(e=s(e).add(1,"month").startOf("month").format("YYYY-MM-DD")),this.currentSelectedStartDate=s(e).subtract(1,"month").date(parseInt(null===(o=this.basicTsConfig)||void 0===o?void 0:o.timesheet_month_end_date)+1).format("DD/MM/YYYY"),this.currentSelectedEndDate=s(e).date(parseInt(null===(l=this.basicTsConfig)||void 0===l?void 0:l.timesheet_month_end_date)).format("DD/MM/YYYY"),yield this.getTimesheetQueryReportItemData(),yield this.getTimesheetSummaryData())),3==this.currentViewType&&("END"==(null===(r=this.basicTsConfig)||void 0===r?void 0:r.timesheet_month_end_date)?(this.currentSelectedStartDate=s(e).startOf("year").format("DD/MM/YYYY"),this.currentSelectedEndDate=s(e).endOf("year").format("DD/MM/YYYY"),yield this.getTimesheetQueryReportItemData(),yield this.getTimesheetSummaryData()):(this.currentSelectedStartDate=s(e).startOf("year").subtract(1,"month").date(parseInt(null===(a=this.basicTsConfig)||void 0===a?void 0:a.timesheet_month_end_date)+1).format("DD/MM/YYYY"),this.currentSelectedEndDate=s(e).endOf("year").date(parseInt(null===(c=this.basicTsConfig)||void 0===c?void 0:c.timesheet_month_end_date)).format("YYYY-MM-DD"),yield this.getTimesheetQueryReportItemData(),yield this.getTimesheetSummaryData()))}))}onCheckColumnConfig(e){this.columns[e].isCheckBoxSelected=!this.columns[e].isCheckBoxSelected,this.columns=this.columns}onCheckSummaryConfig(e){this.summaryCards[e].isCheckBoxSelected=!this.summaryCards[e].isCheckBoxSelected}scheduleReport(){var e,t,n,o,l,r,a;return Object(i.c)(this,void 0,void 0,(function*(){let i,c,p=s(this.month.value).format("YYYY-MM-DD");3!=this.currentViewType&&("END"==(null===(e=this.basicTsConfig)||void 0===e?void 0:e.timesheet_month_end_date)?(i=s(p).startOf("month").format("YYYY-MM-DD"),c=s(p).endOf("month").format("YYYY-MM-DD")):(s(p).date()>parseInt(null===(t=this.basicTsConfig)||void 0===t?void 0:t.timesheet_month_end_date)&&(p=s(p).add(1,"month").startOf("month").format("YYYY-MM-DD")),i=s(p).subtract(1,"month").date(parseInt(null===(n=this.basicTsConfig)||void 0===n?void 0:n.timesheet_month_end_date)+1).format("YYYY-MM-DD"),c=s(p).date(parseInt(null===(o=this.basicTsConfig)||void 0===o?void 0:o.timesheet_month_end_date)).format("YYYY-MM-DD"))),3==this.currentViewType&&("END"==(null===(l=this.basicTsConfig)||void 0===l?void 0:l.timesheet_month_end_date)?(i=s(p).startOf("year").format("YYYY-MM-DD"),c=s(p).endOf("year").format("YYYY-MM-DD")):(i=s(p).startOf("year").subtract(1,"month").date(parseInt(null===(r=this.basicTsConfig)||void 0===r?void 0:r.timesheet_month_end_date)+1).format("YYYY-MM-DD"),c=s(p).endOf("year").date(parseInt(null===(a=this.basicTsConfig)||void 0===a?void 0:a.timesheet_month_end_date)).format("YYYY-MM-DD"))),this._dialog.open(N,{disableClose:!0,width:"545px",position:{top:"120px",right:"24px"},data:{applicationId:600,attachmentType:1,mailTypeConfig:1,apiServerUrl:"/api/timesheetv2/Reports/getTimesheetQueryReportItemData",payload:{aid:this.aid,oid:this.oid,startDate:i,endDate:c,limit:this.limit,viewType:this.currentViewType,filters:[],groupBy:this.currentGroupBy,preference:[],filterConfig:this.filterConfig,isForDownload:!0}}})}))}resolveReportData(e){return e.map(e=>this.resolveSingleReportData(e))}resolveReportDataGroupedByCc(e){return e.map(e=>(e.employees.map(e=>this.resolveSingleReportDataGroupedByCc(e)),e))}resolveSingleReportData(e){let t=[],n=[],o=[],l={},i={},r={},a=[];e.days.forEach(e=>{e.costcenter.forEach(e=>{const a=e.cc_id,s=e.cc_name,c=e.cc_desc;t.some(e=>e.ccId==a)||(t.push({ccId:a,ccName:s,ccDesc:c}),i[a]=[],l[a]=[]),(null==e?void 0:e.sub_project)&&(null==e?void 0:e.sub_project.length)>0?e.sub_project.forEach(e=>{const t=e.sub_project_id,i=e.sub_project_name;o.some(e=>e.subProjectId==t)||(o.push({subProjectId:t,subProjectName:i}),l[a]=[...l[a],t],r[t]=[]),e.tasks.forEach(e=>{const o=e.task_id,l=e.task_name;n.some(e=>e.taskId==o)||(n.push({taskId:o,taskName:l}),r[t]=[...r[t],o])})}):(null==e?void 0:e.tasks)&&(null==e?void 0:e.tasks.length)>0&&e.tasks.forEach(e=>{const t=e.task_id,o=e.task_name;n.some(e=>e.taskId==t)||(n.push({taskId:t,taskName:o}),i[a]=[...i[a],t])})})}),a=c.pluck(t,"ccId");let s=[];return t.forEach(e=>{let t={cc_id:e.ccId,cc_name:e.ccName,cc_desc:e.ccDesc,sub_project:[],tasks:[],total_hours:"00h 00m",total_billable_hours:"00h 00m",total_non_billable_hours:"00h 00m",total_overtime_hours:"00h 00m",leave_id:null,leave_name:null,leave_color_code:null,total_leaves:0};l[e.ccId].length>0?l[e.ccId].forEach(e=>{var l;let i={sub_project_id:e,sub_project_name:null===(l=o.find(t=>t.subProjectId==e))||void 0===l?void 0:l.subProjectName,tasks:[]};r[e].length>0&&r[e].forEach(e=>{var t;let o={task_id:e,task_name:null===(t=n.find(t=>t.taskId==e))||void 0===t?void 0:t.taskName,total_hours:"00h 00m",total_billable_hours:"00h 00m",total_non_billable_hours:"00h 00m",total_overtime_hours:"00h 00m",leave_id:null,leave_name:null,leave_color_code:null};i.tasks.push(o)}),t.sub_project.push(i)}):i[e.ccId].length>0&&i[e.ccId].forEach(e=>{var o;let l={task_id:e,task_name:null===(o=n.find(t=>t.taskId==e))||void 0===o?void 0:o.taskName,total_hours:"00h 00m",total_billable_hours:"00h 00m",total_non_billable_hours:"00h 00m",total_overtime_hours:"00h 00m",leave_id:null,leave_name:null,leave_color_code:null};t.tasks.push(l)}),s.push(t)}),e.days=e.days.map(e=>{let t=e.costcenter,n=c.pluck(t,"cc_id"),o=a.filter(e=>!c.contains(n,e)),p=s.filter(e=>c.contains(o,e.cc_id));return t=[...t,...p],t.map(e=>{var t,n;if((null==e?void 0:e.sub_project)&&e.sub_project.length>0){let n=c.pluck(e.sub_project,"sub_project_id"),o=l[e.cc_id].filter(e=>!c.contains(n,e)),i=null===(t=s.find(t=>t.cc_id==e.cc_id))||void 0===t?void 0:t.sub_project.filter(e=>c.contains(o,e.sub_project_id));e.sub_project=[...e.sub_project,...i],e.sub_project.forEach(t=>{var n,o;let l=c.pluck(t.tasks,"task_id"),i=r[t.sub_project_id].filter(e=>!c.contains(l,e)),a=null===(o=null===(n=s.find(t=>t.cc_id==e.cc_id))||void 0===n?void 0:n.sub_project.find(e=>e.sub_project_id==t.sub_project_id))||void 0===o?void 0:o.tasks.filter(e=>c.contains(i,e.task_id));t.tasks=[...t.tasks,...a]})}else if((null==e?void 0:e.tasks)&&e.tasks.length>0){let t=c.pluck(e.tasks,"task_id"),o=i[e.cc_id].filter(e=>!c.contains(t,e)),l=null===(n=s.find(t=>t.cc_id==e.cc_id))||void 0===n?void 0:n.tasks.filter(e=>c.contains(o,e.task_id));e.tasks=[...e.tasks,...l]}return e}),Object.assign(Object.assign({},e),{costcenter:t})}),e.days.forEach(e=>{e.costcenter.sort((e,t)=>e.cc_id-t.cc_id),e.costcenter.forEach(e=>{(null==e?void 0:e.sub_project)&&e.sub_project.length>0?(e.sub_project.sort((e,t)=>e.sub_project_id-t.sub_project_id),e.sub_project.forEach(e=>{e.tasks.sort((e,t)=>e.task_id-t.task_id)})):(null==e?void 0:e.tasks)&&e.tasks.length>0&&e.tasks.sort((e,t)=>e.task_id-t.task_id)})}),e}resolveSingleReportDataGroupedByCc(e){let t=this.getFieldForEmployee(e);const n=[];let o=[];const l=[];let i={total_hours:"00h 00m",total_billable_hours:"00h 00m",total_non_billable_hours:"00h 00m",total_overtime_hours:"00h 00m",leave_id:null,leave_name:null,leave_color_code:null,leave_background_color_code:null};"sub_project"==t?e.days.forEach(e=>{(null==e?void 0:e.sub_project)&&(null==e?void 0:e.sub_project.length)&&e.sub_project.forEach(e=>{const t=e.sub_project_id,i=e.sub_project_name;l.some(e=>e.subProjectId===t)||(l.push({subProjectId:t,subProjectName:i}),o.push({subProjectId:t,subProjectName:i,tasks:[]})),e.tasks.forEach(e=>{const l=e.task_id,i=e.task_name;n.some(e=>e.taskId===l)||n.push({taskId:l,taskName:i});let r=o.filter(e=>e.subProjectId==t);0==r[0].tasks.filter(e=>e.taskId==l).length&&(r[0].tasks.push({taskId:l,taskName:i}),o=o.filter(e=>e.subProjectId!==t),o=[...o,...r])})})}):"tasks"==t&&e.days.forEach(e=>{(null==e?void 0:e.tasks)&&(null==e?void 0:e.tasks.length)&&e.tasks.forEach(e=>{const t=e.task_id,o=e.task_name;n.some(e=>e.taskId===t)||n.push({taskId:t,taskName:o})})});let r={sub_project:[]};"sub_project"==t&&o.forEach(e=>{let t={sub_project_id:e.subProjectId,sub_project_name:e.subProjectName,tasks:[]};e.tasks.forEach(e=>{t.tasks.push({task_id:e.taskId,task_name:e.taskName,total_hours:"00h 00m",total_billable_hours:"00h 00m",total_non_billable_hours:"00h 00m",total_overtime_hours:"00h 00m",leave_id:null,leave_name:null,leave_color_code:null,leave_background_color_code:null})}),r.sub_project.push(t)});let a={tasks:[]};return"tasks"==t&&n.forEach(e=>{a.tasks.push({task_id:e.taskId,task_name:e.taskName,total_hours:"00h 00m",total_billable_hours:"00h 00m",total_non_billable_hours:"00h 00m",total_overtime_hours:"00h 00m",leave_id:null,leave_name:null,leave_color_code:null,leave_background_color_code:null})}),e.days=e.days.map(e=>{let n=Object.assign({},e);return"sub_project"==t?n=Object.assign(Object.assign(Object.assign({},i),r),e):"tasks"==t?n=Object.assign(Object.assign(Object.assign({},i),a),e):"total_hours"==t&&(n=Object.assign(Object.assign({},i),e)),n}),"sub_project"!=t&&"tasks"!=t||e.days.forEach(e=>{if("sub_project"==t){let t=c.pluck(e.sub_project,"sub_project_id");l.filter(e=>!c.contains(t,e.subProjectId)).forEach(t=>{e.sub_project.push({sub_project_id:t.subProjectId,sub_project_name:t.subProjectName,tasks:[]})}),e.sub_project.sort((e,t)=>e.sub_project_id-t.sub_project_id),e.sub_project.forEach(e=>{let t=c.pluck(e.tasks,"task_id");c.pluck(o.filter(t=>t.subProjectId==e.sub_project_id)[0].tasks,"taskId").filter(e=>!t.includes(e)).forEach(t=>{var o;e.tasks.push({task_id:t,task_name:null===(o=n.find(e=>e.taskId==t))||void 0===o?void 0:o.taskName,total_hours:"00h 00m",total_billable_hours:"00h 00m",total_non_billable_hours:"00h 00m",total_overtime_hours:"00h 00m",leave_id:null,leave_name:null,leave_color_code:null,leave_background_color_code:null})}),e.tasks.sort((e,t)=>e.task_id-t.task_id)})}else{let t=c.pluck(e.tasks,"task_id");n.filter(e=>!c.contains(t,e.taskId)).forEach(t=>{e.tasks.push({task_id:t.taskId,task_name:t.taskName,total_hours:"00h 00m",total_billable_hours:"00h 00m",total_non_billable_hours:"00h 00m",total_overtime_hours:"00h 00m",leave_id:null,leave_name:null,leave_color_code:null,leave_background_color_code:null})}),e.tasks.sort((e,t)=>e.task_id-t.task_id)}}),e}getFieldForEmployee(e){for(const t of e.days){if(t.sub_project&&t.sub_project.length>0)return"sub_project";if(t.tasks&&t.tasks.length>0)return"tasks"}return"total_hours"}openUdrfModal(){return Object(i.c)(this,void 0,void 0,(function*(){this.dropdownflag=!1;const{UdrfModalComponent:e}=yield Promise.all([n.e(4),n.e(998)]).then(n.bind(null,"UIsE"));this._dialog.open(e,{minWidth:"100%",height:"84%",position:{top:"0px",left:"77px"},disableClose:!0})}))}configureUdrf(){return Object(i.c)(this,void 0,void 0,(function*(){this._udrfService.udrfData.applicationId=this.applicationId,this._udrfService.udrfData.isItemDataLoading=!0,this._udrfService.udrfUiData.showItemDataCount=!1,this._udrfService.udrfUiData.showSearchBar=!1,this._udrfService.udrfUiData.showActionButtons=!0,this._udrfService.udrfUiData.showUdrfModalButton=!1,this._udrfService.udrfUiData.showColumnConfigButton=!1,this._udrfService.udrfUiData.showSettingsModalButton=!1,this._udrfService.udrfUiData.showNewReleasesButton=!1,this._udrfService.udrfUiData.showReportDownloadButton=!1,this._udrfService.udrfUiData.isReportDownloading=!1,this._udrfService.udrfUiData.itemHasOpenInNewTab=!1,this._udrfService.udrfUiData.horizontalScroll=!1,this._udrfService.udrfUiData.itemHasQuickCta=!1,this._udrfService.udrfUiData.collapseAll=!1,this._udrfService.udrfUiData.showCollapseButton=!1,this._udrfService.udrfUiData.countForOnlyThisReport=!1,this._udrfService.udrfUiData.toggleChecked=!1,this._udrfService.udrfUiData.countFlag=!1,this._udrfService.udrfUiData.isMultipleView=!1,this._udrfService.udrfUiData.itemHasDownloadButton=!1,this._udrfService.udrfUiData.emailPluginVisible=!1,this._udrfService.udrfUiData.itemHasDownloadButton=!1,this._udrfService.udrfUiData.itemHasAttachFileButton=!1,this._udrfService.udrfUiData.itemHasComments=!1,this._udrfService.udrfUiData.isMoreOptionsNeeded=!1,this._udrfService.udrfUiData.completeProfileBtn=!1,this._udrfService.udrfUiData.searchPlaceholder="",this._udrfService.getAppUdrfConfig(this.applicationId,yield this.initReport.bind(this))}))}initReport(){return Object(i.c)(this,void 0,void 0,(function*(){this.limit=15,this.noDataFound=!1,this.logData=[],this.filterConfig={mainFilterArray:this._udrfService.udrfData.mainFilterArray,searchTableDetails:this._udrfService.udrfData.searchTableDetails,txTableDetails:this._udrfService.udrfData.txTableDetails},yield this.getTimesheetQueryReportItemData(),yield this.getTimesheetSummaryData()}))}onClear(){return Object(i.c)(this,void 0,void 0,(function*(){this.limit=15,this.noDataFound=!1,this.logData=[],this.dropdownflag=!1,this.displayedFilter=0,yield this._udrfService.udrfFunctions.clearSearchAndConfig(),yield this.initReport()}))}clearonefilter(e,t){return Object(i.c)(this,void 0,void 0,(function*(){this._udrfService.udrfFunctions.clearItemConfigApply(e,1),this._udrfService.udrfData.appliedFilterTypeArray.splice(t,1),this._udrfService.udrfData.mainFilterArray.splice(t,1),this.displayedFilter=0,this.dropdownflag=!1}))}viewdroplist(e){this.dropdownflag=!0,this.selecteddropdown=e}closedroplist(){this.dropdownflag=!1,this.selecteddropdown=-1}checkboxvalue(e,t,n,o,l){return Object(i.c)(this,void 0,void 0,(function*(){this._udrfService.udrfData.mainFilterArray[t].checkboxValues[n].isCheckboxSelected=e,e?(this._udrfService.udrfData.mainFilterArray[t].multiOptionSelectSearchValues.push(o),this._udrfService.udrfData.mainFilterArray[t].isIdBased&&this._udrfService.udrfData.mainFilterArray[t].multiOptionSelectSearchValuesWithId.push(l)):this._udrfService.udrfData.mainFilterArray[t].multiOptionSelectSearchValues.forEach((e,n)=>{e===o&&(this._udrfService.udrfData.mainFilterArray[t].multiOptionSelectSearchValues.splice(n,1),this._udrfService.udrfData.mainFilterArray[t].isIdBased&&this._udrfService.udrfData.mainFilterArray[t].multiOptionSelectSearchValuesWithId.splice(n,1))}),0==this._udrfService.udrfData.mainFilterArray[t].multiOptionSelectSearchValues.length?(this.dropdownflag=!1,this.displayedFilter=0,this.clearonefilter(this._udrfService.udrfData.mainFilterArray[t],t)):(this.limit=15,this.noDataFound=!1,this.logData=[],yield this.getTimesheetQueryReportItemData(),yield this.getTimesheetSummaryData())}))}goToPreviousFilter(){this.dropdownflag=!1,0==this.displayedFilter?this.displayedFilter=this._udrfService.udrfData.mainFilterArray.length-1:this.displayedFilter--}goToNextFilter(){this.dropdownflag=!1,this.displayedFilter==this._udrfService.udrfData.mainFilterArray.length-1?this.displayedFilter=0:this.displayedFilter++}getBasicTimesheetConfigurations(){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this.reportService.getBasicTimesheetConfigurations(this.oid,this.aid).subscribe({next:t=>{"S"==t.messType?this.basicTsConfig=t.data:this.toastService.showInfo("Timesheet App Message",t.messText,3e3),e(!0)},error:e=>{this.toastService.showError("Report Data Retrieval Failed","",6e4),t()}}))}))}getTimesheetQueryReportItemData(){var e,t,n,o,l,r,a;return Object(i.c)(this,void 0,void 0,(function*(){let i,c,p=s(this.month.value).format("YYYY-MM-DD");3!=this.currentViewType&&("END"==(null===(e=this.basicTsConfig)||void 0===e?void 0:e.timesheet_month_end_date)?(i=s(p).startOf("month").format("YYYY-MM-DD"),c=s(p).endOf("month").format("YYYY-MM-DD")):(s(p).date()>parseInt(null===(t=this.basicTsConfig)||void 0===t?void 0:t.timesheet_month_end_date)&&(p=s(p).add(1,"month").startOf("month").format("YYYY-MM-DD")),i=s(p).subtract(1,"month").date(parseInt(null===(n=this.basicTsConfig)||void 0===n?void 0:n.timesheet_month_end_date)+1).format("YYYY-MM-DD"),c=s(p).date(parseInt(null===(o=this.basicTsConfig)||void 0===o?void 0:o.timesheet_month_end_date)).format("YYYY-MM-DD"))),3==this.currentViewType&&("END"==(null===(l=this.basicTsConfig)||void 0===l?void 0:l.timesheet_month_end_date)?(i=s(p).startOf("year").format("YYYY-MM-DD"),c=s(p).endOf("year").format("YYYY-MM-DD")):(i=s(p).startOf("year").subtract(1,"month").date(parseInt(null===(r=this.basicTsConfig)||void 0===r?void 0:r.timesheet_month_end_date)+1).format("YYYY-MM-DD"),c=s(p).endOf("year").date(parseInt(null===(a=this.basicTsConfig)||void 0===a?void 0:a.timesheet_month_end_date)).format("YYYY-MM-DD"))),"Invalid date"!==i&&"Invalid date"!==c&&this.filterConfig&&(this.dataSubscription&&this.dataSubscription.unsubscribe(),this.apiInProgress=!0,this.dataSubscription=this.reportService.getTimesheetQueryReportItemData(this.aid,this.oid,i,c,this.limit,this.currentViewType,[],this.currentGroupBy,[],this.filterConfig,!1).pipe(Object(R.a)(e=>(this.toastService.showError("Report Data Retrieval Failed","",6e4),this.apiInProgress=!1,Object(L.a)(e))),Object(z.a)(()=>{this.apiInProgress=!1})).subscribe(e=>{if("S"===e.messType&&e.data.length>0){if(1===this.currentGroupBy){let t=this.resolveReportData(e.data);console.log("report group by employee",t),this.logData=[...this.logData,...t]}else if(2===this.currentGroupBy){let t=this.resolveReportDataGroupedByCc(e.data);console.log("report group by sow",t),this.logData=[...this.logData,...t]}}else this.noDataFound=!0}))}))}getReportConfigurationsBasedOnId(){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this.reportService.getReportConfigurationsBasedOnId(this.applicationId).subscribe({next:t=>{var n,o,l;"S"==t.messType&&t.data.length>0?(this.columns=(null===(n=t.data[0])||void 0===n?void 0:n.columnConfig)||[],this.summaryCards=(null===(o=t.data[0])||void 0===o?void 0:o.summaryCardConfig)||[],this.groupByList=(null===(l=t.data[0])||void 0===l?void 0:l.groupByConfig)||[],this.filterVisible=t.data[0].filterVisible||!1,this.groupByVisible=t.data[0].groupByVisible||!1,this.scheduleVisible=t.data[0].scheduleVisible||!1,this.downloadVisible=t.data[0].downloadVisible||!1,this.settingsVisible=t.data[0].settingsVisible||!1,this.summaryCardVisible=t.data[0].summaryCardVisible||!1,this.settingsArray=t.data[0].settingsConfig||!1,this.calculateDynamicContentHeight()):this.toastService.showInfo("Timesheet App Message",t.messText,3e3),e(!0)},error:e=>{this.toastService.showError("Report Data Retrieval Failed","",6e4),t()}}))}))}downloadTimesheetCalendarReport(){var e,t,n,o,l,i,r;let a,c,p=s(this.month.value).format("YYYY-MM-DD");this.disableDownloadButton=!0,3!=this.currentViewType&&("END"==(null===(e=this.basicTsConfig)||void 0===e?void 0:e.timesheet_month_end_date)?(a=s(p).startOf("month").format("YYYY-MM-DD"),c=s(p).endOf("month").format("YYYY-MM-DD")):(s(p).date()>parseInt(null===(t=this.basicTsConfig)||void 0===t?void 0:t.timesheet_month_end_date)&&(p=s(p).add(1,"month").startOf("month").format("YYYY-MM-DD")),a=s(p).subtract(1,"month").date(parseInt(null===(n=this.basicTsConfig)||void 0===n?void 0:n.timesheet_month_end_date)+1).format("YYYY-MM-DD"),c=s(p).date(parseInt(null===(o=this.basicTsConfig)||void 0===o?void 0:o.timesheet_month_end_date)).format("YYYY-MM-DD"))),3==this.currentViewType&&("END"==(null===(l=this.basicTsConfig)||void 0===l?void 0:l.timesheet_month_end_date)?(a=s(p).startOf("year").format("YYYY-MM-DD"),c=s(p).endOf("year").format("YYYY-MM-DD")):(a=s(p).startOf("year").subtract(1,"month").date(parseInt(null===(i=this.basicTsConfig)||void 0===i?void 0:i.timesheet_month_end_date)+1).format("YYYY-MM-DD"),c=s(p).endOf("year").date(parseInt(null===(r=this.basicTsConfig)||void 0===r?void 0:r.timesheet_month_end_date)).format("YYYY-MM-DD"))),"Invalid date"!==a&&"Invalid date"!==c&&this.filterConfig&&(this.dataDownloadSubscription&&this.dataDownloadSubscription.unsubscribe(),this.apiDownloadInProgress=!0,this.dataDownloadSubscription=this.reportService.downloadTimesheetCalendarReport(this.aid,this.oid,a,c,this.limit,this.currentViewType,[],this.currentGroupBy,[],this.filterConfig,!1,this.columns).pipe(Object(R.a)(e=>(this.toastService.showError("Report Data Retrieval Failed","",6e4),this.apiDownloadInProgress=!1,Object(L.a)(e))),Object(z.a)(()=>{this.apiDownloadInProgress=!1})).subscribe(e=>{this.disableDownloadButton=!1;const t=new Blob([e],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),n=document.createElement("a");n.href=window.URL.createObjectURL(t),n.download="Timesheet Calendar View Report.xlsx",n.click()}))}getTimesheetSummaryData(){var e,t,n,o,l,r,a;return Object(i.c)(this,void 0,void 0,(function*(){let i,c,p=s(this.month.value).format("YYYY-MM-DD");3!=this.currentViewType&&("END"==(null===(e=this.basicTsConfig)||void 0===e?void 0:e.timesheet_month_end_date)?(i=s(p).startOf("month").format("YYYY-MM-DD"),c=s(p).endOf("month").format("YYYY-MM-DD")):(s(p).date()>parseInt(null===(t=this.basicTsConfig)||void 0===t?void 0:t.timesheet_month_end_date)&&(p=s(p).add(1,"month").startOf("month").format("YYYY-MM-DD")),i=s(p).subtract(1,"month").date(parseInt(null===(n=this.basicTsConfig)||void 0===n?void 0:n.timesheet_month_end_date)+1).format("YYYY-MM-DD"),c=s(p).date(parseInt(null===(o=this.basicTsConfig)||void 0===o?void 0:o.timesheet_month_end_date)).format("YYYY-MM-DD"))),3==this.currentViewType&&("END"==(null===(l=this.basicTsConfig)||void 0===l?void 0:l.timesheet_month_end_date)?(i=s(p).startOf("year").format("YYYY-MM-DD"),c=s(p).endOf("year").format("YYYY-MM-DD")):(i=s(p).startOf("year").subtract(1,"month").date(parseInt(null===(r=this.basicTsConfig)||void 0===r?void 0:r.timesheet_month_end_date)+1).format("YYYY-MM-DD"),c=s(p).endOf("year").date(parseInt(null===(a=this.basicTsConfig)||void 0===a?void 0:a.timesheet_month_end_date)).format("YYYY-MM-DD"))),"Invalid date"!==i&&"Invalid date"!==c&&this.filterConfig&&(this.summaryDataSubscription&&this.summaryDataSubscription.unsubscribe(),this.summaryApiInProgress=!0,this.summaryDataSubscription=this.reportService.getTimesheetSummaryData(this.aid,this.oid,i,c,this.limit,this.currentViewType,[],this.currentGroupBy,[],this.filterConfig,!1).pipe(Object(R.a)(e=>(this.toastService.showError("Report Data Retrieval Failed","",6e4),this.summaryApiInProgress=!1,Object(L.a)(e))),Object(z.a)(()=>{this.summaryApiInProgress=!1})).subscribe(e=>{var t,n,o,l;"S"===e.messType&&e.data.length>0?(this.summaryCards[0].value=null===(t=e.data[0])||void 0===t?void 0:t["Total Logged Hours"],this.summaryCards[1].value=null===(n=e.data[0])||void 0===n?void 0:n["Total Billable Logged Hours"],this.summaryCards[2].value=null===(o=e.data[0])||void 0===o?void 0:o["Total Non-Billable Logged Hours"],this.summaryCards[4].value=null===(l=e.data[0])||void 0===l?void 0:l["Total Leaves"]):this.toastService.showInfo("Timesheet Report Message","No Data Found",7e3)}))}))}getTimesheetReportAccess(){return Object(i.c)(this,void 0,void 0,(function*(){this.reportService.getTimesheetReportAccess(600).subscribe({next:e=>{e.reportAccess||this.router.navigateByUrl("/main/reports")},error:e=>{this.toastService.showError("Timesheet App Message",e.messText,3e3)}})}))}}return e.\u0275fac=function(t){return new(t||e)(_["\u0275\u0275directiveInject"](l.g),_["\u0275\u0275directiveInject"](C.a),_["\u0275\u0275directiveInject"](G),_["\u0275\u0275directiveInject"](S.a),_["\u0275\u0275directiveInject"](p.b),_["\u0275\u0275directiveInject"](U.a),_["\u0275\u0275directiveInject"](H.a))},e.\u0275cmp=_["\u0275\u0275defineComponent"]({type:e,selectors:[["app-ts-v2-daily-log-report-landing-page"]],hostBindings:function(e,t){1&e&&_["\u0275\u0275listener"]("resize",(function(){return t.onResize()}),!1,_["\u0275\u0275resolveWindow"])},features:[_["\u0275\u0275ProvidersFeature"]([{provide:a.c,useClass:r.c,deps:[a.f,r.a]},{provide:a.e,useValue:Uo}])],decls:15,vars:2,consts:[[1,"bg-container"],["class","report-screen",4,"ngIf"],["class","main-spinner-height",4,"ngIf"],["viewTypePopup","matMenu"],[3,"matMenuContent"],["columnPopup","matMenu"],["groupByPopup","matMenu"],["settingsPopup","matMenu"],[1,"report-screen"],[1,"align-items-center-between",2,"margin-bottom","16px"],[1,"align-items-center"],[1,"back-btn",3,"click"],[1,"back-btn-icon"],[1,"header-title"],[1,"align-items-center",2,"gap","10px"],[2,"cursor","pointer",3,"matMenuTriggerFor"],[1,"view-type-box"],[1,"view-type-text"],[1,"view-type-icon"],["class","export-btn",3,"disabled","click",4,"ngIf"],["class","send-btn",3,"click",4,"ngIf"],["class","settings-btn",3,"matMenuTriggerFor",4,"ngIf"],[2,"display","flex","align-items","center","margin-bottom","16px","gap","15px"],[1,"col-3","p-0",3,"ngStyle"],[1,"filter-text"],[1,"filter-group-box",2,"display","flex","justify-content","space-between","padding","8px","height","40px"],[1,"date-text"],[4,"ngIf"],["style","margin-top: 12px",4,"ngIf"],["style","\n          margin-top: 22px;\n          margin-right: 10px;\n          display: flex;\n          flex-direction: row;\n        ",4,"ngIf"],["style","margin-right: 10px; margin-top: 14px",4,"ngIf"],["class","align-items-center","style","margin-bottom: 16px; gap: 10px",4,"ngIf"],["class","report-height",4,"ngIf"],["class","spinner-height",4,"ngIf"],["class","lazy-load-spinner",4,"ngIf"],["class","no-data-img-height",4,"ngIf"],[1,"export-btn",3,"click"],[1,"export-text"],[1,"export-icon"],[1,"send-btn",3,"click"],[1,"send-icon"],[1,"settings-btn",3,"matMenuTriggerFor"],[1,"settings-icon"],["matInput","",2,"visibility","hidden","width","1px","height","1px",3,"matDatepicker","formControl"],[3,"for"],["matDatepickerToggleIcon","",1,"calendar-icon"],["startView","multi-year",3,"startAt","monthSelected"],["dp",""],["startView","multi-year",3,"startAt","yearSelected"],[2,"margin-top","12px"],["matTooltip","Filters",1,"header-icon",3,"ngStyle","click"],["matTooltip","Group By",1,"header-icon",3,"matMenuTriggerFor"],[2,"margin-top","22px","margin-right","10px","display","flex","flex-direction","row"],[2,"margin-right","10px","margin-top","6px","cursor","pointer",3,"click"],[4,"ngFor","ngForOf"],[2,"margin-left","10px","margin-top","6px","cursor","pointer",3,"click"],["class","searchField","style","display: flex !important",4,"ngIf"],[1,"searchField",2,"display","flex !important"],[1,"searchboxes","tooltip",2,"display","contents"],[1,"searchtitle","titlemargin","filterfield",3,"matTooltip"],[1,"searchtitle","boxstyle"],[1,"filterval",3,"matTooltip"],[1,"clearonefiltericn",3,"click"],["class","dropdownfilter",3,"click",4,"ngIf"],[1,"tooltiptext","dropdownborder",3,"ngClass"],[1,"dropdownfilter",3,"click"],[2,"display","inline-flex"],[1,"dropdata"],[1,"example-margin",3,"checked","change"],[2,"margin-right","10px","margin-top","14px"],["mat-stroked-button","",1,"clearbtn",3,"click"],[1,"align-items-center",2,"margin-bottom","16px","gap","10px"],["class","hours-tile col-2 p-0",3,"ngStyle",4,"ngIf"],[1,"hours-tile","col-2","p-0",3,"ngStyle"],[1,"hours-tile-text"],[1,"icon-size"],[1,"report-height"],[3,"currentViewType","logData","columns","limit","limitEmit",4,"ngIf"],[3,"currentViewType","logData","columns","limit","limitEmit"],[1,"spinner-height"],["diameter","40"],[1,"lazy-load-spinner"],[1,"loading-text"],[1,"no-data-img-height"],["width","300","height","300","src","https://assets.kebs.app/ts_daily_log_no_data.png"],[1,"main-spinner-height"],[1,"view-type-popup",3,"click"],[1,"view-type-title"],[1,"radio-group"],[3,"ngStyle","value","checked","change",4,"ngFor","ngForOf"],[3,"ngStyle","value","checked","change"],[1,"column-type-popup",3,"click"],[1,"title-text"],["class","title-text","style","margin-top: 12px",4,"ngIf"],["class","align-items-center",4,"ngIf"],[3,"ngStyle"],["width","16px","height","16px",3,"ngModel","name","ngModelChange"],[1,"text-style"],[1,"title-text",2,"margin-top","12px"],["style","cursor: pointer;",3,"checked","value",4,"ngFor","ngForOf"],[2,"cursor","pointer",3,"checked","value"]],template:function(e,t){1&e&&(_["\u0275\u0275elementStart"](0,"div",0),_["\u0275\u0275template"](1,Do,43,22,"div",1),_["\u0275\u0275template"](2,jo,2,0,"div",2),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](3,"mat-menu",null,3),_["\u0275\u0275template"](5,Fo,5,1,"ng-template",4),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](6,"mat-menu",null,5),_["\u0275\u0275template"](8,No,6,3,"ng-template",4),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](9,"mat-menu",null,6),_["\u0275\u0275template"](11,zo,5,1,"ng-template",4),_["\u0275\u0275elementEnd"](),_["\u0275\u0275elementStart"](12,"mat-menu",null,7),_["\u0275\u0275template"](14,Go,3,1,"ng-template",4),_["\u0275\u0275elementEnd"]()),2&e&&(_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",!t.initialSpinner),_["\u0275\u0275advance"](1),_["\u0275\u0275property"]("ngIf",t.initialSpinner))},directives:[o.NgIf,h.g,h.c,m.a,h.f,o.NgStyle,f.b,W.g,d.e,d.v,d.k,W.i,W.j,W.f,g.a,o.NgForOf,o.NgClass,v.a,St,no,oo.c,lo.b,lo.a,d.y],styles:[".mat-calendar-arrow.mat-calendar-invert{display:none}  .mat-calendar-period-button{pointer-events:none}.bg-container[_ngcontent-%COMP%]{background-color:#f1f3f8;overflow:hidden}.report-screen[_ngcontent-%COMP%]{margin:24px;height:var(--dynamicHeight)}.report-screen[_ngcontent-%COMP%]     .md-drppicker{z-index:99999999!important}.report-height[_ngcontent-%COMP%]{height:var(--dynamicReportHeight)}.spinner-height[_ngcontent-%COMP%]{height:var(--dynamicReportHeight)}.main-spinner-height[_ngcontent-%COMP%], .spinner-height[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}.main-spinner-height[_ngcontent-%COMP%]{margin:24px;height:var(--dynamicHeight)}.no-data-img-height[_ngcontent-%COMP%]{height:var(--dynamicReportHeight);justify-content:center}.align-items-center[_ngcontent-%COMP%], .align-items-center-between[_ngcontent-%COMP%], .no-data-img-height[_ngcontent-%COMP%]{display:flex;align-items:center}.align-items-center-between[_ngcontent-%COMP%]{flex-direction:row;justify-content:space-between}.back-btn[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;color:#111434;font-size:11px;font-weight:400;border:1px solid #8b95a5;border-radius:4px;padding:4px;cursor:pointer;width:-moz-fit-content;width:fit-content}.back-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{width:0!important;height:15px!important}.back-btn[_ngcontent-%COMP%]   .back-btn-icon[_ngcontent-%COMP%]{font-size:18px;color:#111434;margin-right:20px;margin-bottom:2px}.header-title[_ngcontent-%COMP%]{font-size:16px;font-weight:600;color:#111434;margin-left:20px}.hours-tile[_ngcontent-%COMP%]{height:80px;background-color:#fff;border:1px solid #e8e9ee;border-radius:4px;display:flex;align-items:center;justify-content:center;flex-direction:column}.hours-tile[_ngcontent-%COMP%]   .hours-tile-text[_ngcontent-%COMP%]{font-size:12px;font-weight:600;display:flex;justify-content:center;margin-bottom:5px}.hours-tile[_ngcontent-%COMP%]   .total-hours-value[_ngcontent-%COMP%]{color:#52c41a}.hours-tile[_ngcontent-%COMP%]   .total-billable-hours-value[_ngcontent-%COMP%], .hours-tile[_ngcontent-%COMP%]   .total-hours-value[_ngcontent-%COMP%]{font-size:18px;font-weight:500;display:flex;justify-content:center;align-items:center}.hours-tile[_ngcontent-%COMP%]   .total-billable-hours-value[_ngcontent-%COMP%]{color:#ee4961}.hours-tile[_ngcontent-%COMP%]   .total-non-billable-hours-value[_ngcontent-%COMP%]{color:#7d838b}.hours-tile[_ngcontent-%COMP%]   .total-non-billable-hours-value[_ngcontent-%COMP%], .hours-tile[_ngcontent-%COMP%]   .total-overtime-value[_ngcontent-%COMP%]{font-size:18px;font-weight:500;display:flex;justify-content:center;align-items:center}.hours-tile[_ngcontent-%COMP%]   .total-overtime-value[_ngcontent-%COMP%]{color:#fa541c}.hours-tile[_ngcontent-%COMP%]   .total-leave-value[_ngcontent-%COMP%]{color:#13c2c2;font-size:18px;font-weight:500;display:flex;justify-content:center;align-items:center}.hours-tile[_ngcontent-%COMP%]   .icon-size[_ngcontent-%COMP%]{font-size:24px}.view-type-box[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;width:120px;padding:8px 12px;border-radius:4px;border:1px solid #8b95a5;background:#fff}.view-type-text[_ngcontent-%COMP%]{color:#5f6c81;font-size:12px;font-weight:400;margin-bottom:0}.view-type-icon[_ngcontent-%COMP%]{height:16px;width:16px;font-size:16px;color:#5f6c81}.export-btn[_ngcontent-%COMP%]{display:flex;padding:8px 12px;align-items:center;gap:8px;border-radius:4px;background-color:#ee4961;cursor:pointer}.export-text[_ngcontent-%COMP%]{color:#fff;font-size:14px;font-weight:700;margin-bottom:0}.export-icon[_ngcontent-%COMP%]{font-size:16px;height:16px;width:16px;color:#fff}.send-btn[_ngcontent-%COMP%]{border-radius:4px;border:1px solid #45546e;padding:8px;cursor:pointer;width:36px;height:36px;display:flex;justify-content:center;align-items:center}.send-icon[_ngcontent-%COMP%]{height:16px;width:16px;font-size:16px;color:#45546e}.settings-btn[_ngcontent-%COMP%]{border-radius:4px;border:1px solid #45546e;padding:8px;cursor:pointer;width:36px;height:36px;display:flex;justify-content:center;align-items:center}.settings-icon[_ngcontent-%COMP%]{height:16px;width:16px;font-size:16px;color:#45546e}.view-type-popup[_ngcontent-%COMP%]{overflow:hidden;padding:12px;width:120px}.view-type-popup[_ngcontent-%COMP%]   .view-type-title[_ngcontent-%COMP%]{font-size:12px;font-weight:500;color:#45546e;margin-bottom:4px}.view-type-popup[_ngcontent-%COMP%]   .radio-group[_ngcontent-%COMP%]{font-size:12px;font-weight:400;color:#45546e}.view-type-popup[_ngcontent-%COMP%]     .mat-radio-button.mat-accent .mat-radio-inner-circle{background-color:#f27a6c!important}.view-type-popup[_ngcontent-%COMP%]     .mat-radio-button.mat-accent.mat-radio-checked .mat-radio-outer-circle{border-color:#f27a6c!important}.view-type-popup[_ngcontent-%COMP%]     .mat-radio-outer-circle{border-color:#f27a6c}.view-type-popup[_ngcontent-%COMP%]     .mat-radio-inner-circle, .view-type-popup[_ngcontent-%COMP%]     .mat-radio-outer-circle{height:16px!important;width:16px!important;margin-top:2px}.view-type-popup[_ngcontent-%COMP%]     .mat-radio-label-content{padding-left:0!important}.filter-text[_ngcontent-%COMP%]{font-size:14px;font-weight:400;color:#45546e}.filter-group-box[_ngcontent-%COMP%]{display:flex;align-items:center;height:48px;border-radius:4px;border:1px solid #8b95a5;background:#fff}.header-icon[_ngcontent-%COMP%]{color:#111434;font-size:24px;cursor:pointer;margin-top:8px}.date-text[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#111434}.column-type-popup[_ngcontent-%COMP%]{overflow:hidden;padding:12px;width:140px}.column-type-popup[_ngcontent-%COMP%]   .title-text[_ngcontent-%COMP%]{font-size:12px;font-weight:500;color:#45546e;margin-bottom:6px;padding-top:0}.column-type-popup[_ngcontent-%COMP%]   .text-style[_ngcontent-%COMP%]{font-size:12px;font-weight:400;color:#45546e;margin-bottom:5px;margin-left:6px}.column-type-popup[_ngcontent-%COMP%]     .mat-checkbox-checked.mat-accent .mat-checkbox-background{background-color:#f27a6c!important}.clearbtn[_ngcontent-%COMP%]{font-weight:600;font-size:12px;line-height:16px;height:34px;border-radius:4px;border:1px solid #ee4961;background-color:rgba(242,212,205,.6784313725490196);color:#ee4961}.searchField[_ngcontent-%COMP%]{display:inline-block;border:thin solid #dadce2;border-radius:4px;height:36px;margin-bottom:8px}.searchboxes[_ngcontent-%COMP%]{display:flex;align-items:center}.titlemargin[_ngcontent-%COMP%]{margin-left:0!important;margin-right:0!important}.searchtitle[_ngcontent-%COMP%]{font-weight:400;font-size:12px;line-height:16px;margin:auto 5px;padding:2px 10px}.clearonefiltericn[_ngcontent-%COMP%], .dropdownfilter[_ngcontent-%COMP%]{font-size:13px;display:inline-flex;justify-content:center;align-items:center;cursor:pointer}.dropdownfilter[_ngcontent-%COMP%]{margin-top:5px}.boxstyle[_ngcontent-%COMP%]{background-color:rgba(242,212,205,.6784313725490196);height:1.5rem;display:inline-flex;align-items:center;justify-content:center;margin-right:0;color:#526179}.filterval[_ngcontent-%COMP%]{width:100px}.filterfield[_ngcontent-%COMP%], .filterval[_ngcontent-%COMP%]{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.filterfield[_ngcontent-%COMP%]{width:57px}.searchFieldtxt[_ngcontent-%COMP%]{outline:none;border:none}.positionFieldlable[_ngcontent-%COMP%], .searchFieldtxt[_ngcontent-%COMP%]{font-weight:400;font-size:12px;line-height:16px}.positionFieldlable[_ngcontent-%COMP%]{margin-top:19px;margin-left:4px;color:#a8acb2}.positionField[_ngcontent-%COMP%]{display:inline-block;margin-left:10px;padding-top:5px;width:147px;margin-right:20px}.positionFieldtxt[_ngcontent-%COMP%]{border-radius:4px;height:40px;width:200px;border-color:#b9c0ca}.tooltip[_ngcontent-%COMP%]{position:relative;display:inline-block}.droplistvisible[_ngcontent-%COMP%]{visibility:visible!important}.droplisthidden[_ngcontent-%COMP%]{visibility:hidden!important}.dropdata[_ngcontent-%COMP%]{font-size:12px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;color:#111434;width:175px;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.example-margin[_ngcontent-%COMP%]{display:flex;right:10px;position:absolute}.dropdownborder[_ngcontent-%COMP%]{z-index:1;width:230px;border:1px solid #d3d3d3;border-radius:4px;margin-top:35px;padding:10px!important}.tooltip[_ngcontent-%COMP%]   .tooltiptext[_ngcontent-%COMP%]{visibility:hidden;background-color:#fff;border-radius:6px;padding:7px 5px 5px;width:250x;height:200px;position:absolute;z-index:99999999;border-radius:4px;overflow-y:scroll;overflow-x:hidden}.tooltip[_ngcontent-%COMP%]:active   .tooltiptext[_ngcontent-%COMP%]{visibility:visible}.search-icon[_ngcontent-%COMP%]{color:#b9c0ca}.dp-class[_ngcontent-%COMP%]{background:0 0;border:1px solid #d3d3d3;border-radius:4px;font-size:12px;font-weight:500;width:100%;height:40px;margin-top:4px;cursor:pointer;text-align:center}.lazy-load-spinner[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;position:relative;bottom:5%}.loading-text[_ngcontent-%COMP%]{position:relative;color:#ee4961;font-size:20px;font-weight:700}.disabled[_ngcontent-%COMP%]{opacity:.6;pointer-events:none}"]}),e})()}];let Wo=(()=>{class e{}return e.\u0275mod=_["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=_["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[l.k.forChild(Ho)],l.k]}),e})();var Qo=n("YhS8");let Xo=(()=>{class e{}return e.\u0275mod=_["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=_["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[o.CommonModule,Wo,m.b,g.b,u.a,lo.c,h.e,d.p,d.E,W.h,f.c,x.e,a.n,oo.b,v.b,p.g,Q.b,Qo.c.forRoot()]]}),e})()}}]);