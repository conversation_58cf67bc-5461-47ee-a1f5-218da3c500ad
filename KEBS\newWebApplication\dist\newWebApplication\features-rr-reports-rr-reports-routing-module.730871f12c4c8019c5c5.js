(window.webpackJsonp=window.webpackJsonp||[]).push([[643],{scVR:function(e,t,r){"use strict";r.r(t),r.d(t,"RrReportsRoutingModule",(function(){return V}));var i=r("tyNb"),a=r("mrSG"),o=r("xG9w"),n=r("wd/R"),l=r("ofXK"),s=r("ZzPI"),c=r("bTqV"),d=r("NFeN"),u=r("Qu3c"),h=r("STbY"),p=(r("iadO"),r("3Pt+"),r("qFsG"),r("kmnG"),r("0IaG")),f=r("JqCM"),m=r("fXoL"),g=r("tk/3"),D=r("dNgK");let b=(()=>{class e{constructor(e,t){this.http=e,this.snackBar=t}getRRReportMenuData(){return new Promise((e,t)=>{this.http.post("/api/misFunctions/getRRReportMenuList",{}).subscribe(t=>e(t),e=>t(e))})}getRRPandLReportData(e,t){return new Promise((r,i)=>{this.http.post("/api/misFunctions/getRRPandLReportData",{report_id:e,filterConfig:t}).subscribe(e=>r(e),e=>i(e))})}getSubdivisionReportsData(e,t){return new Promise((r,i)=>{this.http.post("/api/misFunctions/getsubdivisionReportData",{report_id:e,filterConfig:t}).subscribe(e=>r(e),e=>i(e))})}getCustomerReportData(e,t){return new Promise((r,i)=>{this.http.post("/api/misFunctions/getCustomerReportData",{report_id:e,filterConfig:t}).subscribe(e=>r(e),e=>i(e))})}getFinancialYearDetails(){try{return new Promise((e,t)=>{this.http.post("/api/misFunctions/getFinancialYearDetails",{}).subscribe({next:t=>("E"==t.messType&&this.snackBar.open(t.messText,"Dismiss",{duration:5e3}),e(t)),error:e=>(this.snackBar.open(e.messText,"Dismiss",{duration:5e3}),t(e))})})}catch(e){return Promise.reject()}}getReportingCurrency(){try{return new Promise((e,t)=>{this.http.post("/api/misFunctions/getReportingCurrency",{}).subscribe({next:t=>("E"==t.messType&&this.snackBar.open(t.messText,"Dismiss",{duration:5e3}),e(t)),error:e=>(this.snackBar.open(e.messText,"Dismiss",{duration:5e3}),t(e))})})}catch(e){return Promise.reject()}}getMisUploadDate(){try{return new Promise((e,t)=>{this.http.post("/api/misFunctions/getMisUploadDate",{}).subscribe({next:t=>("E"==t.messType&&this.snackBar.open(t.messText,"Dismiss",{duration:5e3}),e(t)),error:e=>(this.snackBar.open(e.messText,"Dismiss",{duration:5e3}),t(e))})})}catch(e){return Promise.reject()}}}return e.\u0275fac=function(t){return new(t||e)(m["\u0275\u0275inject"](g.c),m["\u0275\u0275inject"](D.a))},e.\u0275prov=m["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var S=r("GnQ3"),x=r("6t9p");function y(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"button",12),m["\u0275\u0275elementStart"](1,"div",13),m["\u0275\u0275elementStart"](2,"div",14),m["\u0275\u0275listener"]("click",(function(){m["\u0275\u0275restoreView"](e);const r=t.$implicit;return m["\u0275\u0275nextContext"]().loadRRView(r.id,r)})),m["\u0275\u0275text"](3),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;m["\u0275\u0275advance"](3),m["\u0275\u0275textInterpolate"](e.report_name)}}function v(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"span",15),m["\u0275\u0275text"](1),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]();m["\u0275\u0275advance"](1),m["\u0275\u0275textInterpolate1"](" ",e.reportName," ")}}function C(e,t){1&e&&(m["\u0275\u0275elementStart"](0,"mat-icon",19),m["\u0275\u0275text"](1," filter_list "),m["\u0275\u0275elementEnd"]())}function k(e,t){1&e&&m["\u0275\u0275element"](0,"mat-spinner",20)}function _(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"button",16),m["\u0275\u0275listener"]("click",(function(){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"]().openUdrfFilterModal()})),m["\u0275\u0275template"](1,C,2,0,"mat-icon",17),m["\u0275\u0275template"](2,k,1,0,"mat-spinner",18),m["\u0275\u0275elementEnd"]()}if(2&e){const e=m["\u0275\u0275nextContext"]();m["\u0275\u0275property"]("disabled",e._udrfService.udrfData.areSortAndFiltersLoading),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",!e._udrfService.udrfData.areSortAndFiltersLoading),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",e._udrfService.udrfData.areSortAndFiltersLoading)}}function Y(e,t){if(1&e&&m["\u0275\u0275element"](0,"dxi-column",32),2&e){const e=t.$implicit;m["\u0275\u0275property"]("dataField",e.valueExpr)("allowReordering",!0)("caption",e.caption)("alignment",e.alignment)("fixed",e.fixed)}}const R=function(){return{mode:"none"}};function F(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div",21),m["\u0275\u0275elementStart"](1,"div",22),m["\u0275\u0275elementStart"](2,"dx-data-grid",23),m["\u0275\u0275listener"]("onCellPrepared",(function(t){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"]().onCellPrepared(t)})),m["\u0275\u0275element"](3,"dxo-export",24),m["\u0275\u0275element"](4,"dxo-column-chooser",25),m["\u0275\u0275element"](5,"dxo-column-fixing",26),m["\u0275\u0275element"](6,"dxo-group-panel",27),m["\u0275\u0275element"](7,"dxo-state-storing",28),m["\u0275\u0275element"](8,"dxo-search-panel",29),m["\u0275\u0275element"](9,"dxo-selection",30),m["\u0275\u0275element"](10,"dxo-header-filter",27),m["\u0275\u0275element"](11,"dxo-filter-row",27),m["\u0275\u0275template"](12,Y,1,5,"dxi-column",31),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}if(2&e){const e=m["\u0275\u0275nextContext"]();m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("allowColumnResizing",!0)("dataSource",e.reportData)("showBorders",!0)("hoverStateEnabled",!0)("columnAutoWidth",!0)("sorting",m["\u0275\u0275pureFunction0"](17,R)),m["\u0275\u0275advance"](1),m["\u0275\u0275propertyInterpolate"]("fileName",e.fileName),m["\u0275\u0275property"]("enabled",!0),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("enabled",!0),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("enabled",!0),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("visible",!0),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("enabled",!0),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("visible",!0)("width",240),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("visible",!0),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("visible",!1),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",e.columnConfig)}}const w=[{path:"",component:(()=>{class e{constructor(e,t,r,i,a){this._rrReportsService=e,this.snackBar=t,this._udrfService=r,this.dialog=i,this._spinnerService=a,this.applicationId=1013,this.subdivsionApplicationId=1048,this.customerApplicationID=1049,this.fyStartMonth=1,this.fyEndMonth=12}getFinancialYearDetails(){return Object(a.c)(this,void 0,void 0,(function*(){}))}ngOnInit(){return Object(a.c)(this,void 0,void 0,(function*(){if(this.reportingCurrenyData=yield this._rrReportsService.getReportingCurrency(),"E"==this.reportingCurrenyData.messType)return this.snackBar.open(this.reportingCurrenyData.messText,"Dismiss",{duration:2e3});this.udrffilterconfig(),this.getRRReportMenuData(),this.getMisUploadDate()}))}initUdrf(){this._udrfService.udrfData.applicationId=this.applicationId,this._udrfService.udrfData.isItemDataLoading=!0,this._udrfService.udrfUiData.showItemDataCount=!1,this._udrfService.udrfUiData.showSearchBar=!1,this._udrfService.udrfUiData.showActionButtons=!0,this._udrfService.udrfUiData.showUdrfModalButton=!0,this._udrfService.udrfUiData.showColumnConfigButton=!1,this._udrfService.udrfUiData.showSettingsModalButton=!1,this._udrfService.udrfUiData.showNewReleasesButton=!1,this._udrfService.udrfUiData.showReportDownloadButton=!1,this._udrfService.udrfUiData.isReportDownloading=!1,this._udrfService.udrfUiData.itemHasOpenInNewTab=!1,this._udrfService.udrfUiData.horizontalScroll=!1,this._udrfService.udrfUiData.itemHasQuickCta=!1,this._udrfService.udrfUiData.collapseAll=!1,this._udrfService.udrfUiData.showCollapseButton=!1,this._udrfService.udrfUiData.countForOnlyThisReport=!1,this._udrfService.udrfUiData.toggleChecked=!1,this._udrfService.udrfUiData.countFlag=!1,this._udrfService.udrfUiData.isMultipleView=!1,this._udrfService.udrfUiData.itemHasDownloadButton=!1,this._udrfService.udrfUiData.emailPluginVisible=!1,this._udrfService.udrfUiData.itemHasDownloadButton=!1,this._udrfService.udrfUiData.itemHasAttachFileButton=!1,this._udrfService.udrfUiData.itemHasComments=!1,this._udrfService.udrfUiData.isMoreOptionsNeeded=!1,this._udrfService.udrfUiData.completeProfileBtn=!1,this._udrfService.getAppUdrfConfig(this.applicationId,this.initReport.bind(this))}getRRReportMenuData(){return Object(a.c)(this,void 0,void 0,(function*(){let e=yield this._rrReportsService.getRRReportMenuData();"S"==e.messType&&(this.rrMenu=e.data,this.applicationId=e.data[0].udrf_app_id,this.reportId=e.data[0].id,this.reportName=e.data[0].report_name,this.fileName=e.data[0].file_name,console.log("this.applicationId"),console.log(this.applicationId))}))}getMisUploadDate(){return Object(a.c)(this,void 0,void 0,(function*(){let e=yield this._rrReportsService.getMisUploadDate();"S"==e.messType&&(this.mis_upload_date=e.mis_upload_date)}))}loadRRView(e,t){return Object(a.c)(this,void 0,void 0,(function*(){this.reportId=e,this.reportName=t.report_name,this.fileName=t.file_name,1==this.reportId&&(this.applicationId=1013,this.udrffilterconfig()),2==this.reportId&&(this.applicationId=1048,this.udrffilterconfig()),3==this.reportId&&(this.applicationId=1049,this.udrffilterconfig())}))}openUdrfFilterModal(){return Object(a.c)(this,void 0,void 0,(function*(){const{UdrfModalComponent:e}=yield Promise.all([r.e(4),r.e(998)]).then(r.bind(null,"UIsE"));this.dialog.open(e,{minWidth:"100%",height:"84%",position:{top:"0px",left:"77px"},disableClose:!0})}))}initReport(){return Object(a.c)(this,void 0,void 0,(function*(){1==this.reportId&&this.getRRDataList(),2==this.reportId&&this.getsubdivsionDataList(),3==this.reportId&&(this._spinnerService.show(),this.getCustomerReportData())}))}getRRDataList(){return Object(a.c)(this,void 0,void 0,(function*(){this._spinnerService.show(),console.log("this._udrfService.udrfData"),console.log(this._udrfService.udrfData);let e=JSON.parse(JSON.stringify(this._udrfService.udrfData.mainFilterArray)),t=e,r=null,i=null,a=null,n=null,l=null,s=null,c=null,d=null,u=null,h=null,p=null,f=null;e&&e.length>0&&(l=o.where(e,{filterId:1}),s=o.where(e,{filterId:2}),c=o.where(e,{filterId:3}),u=o.where(e,{filterId:4}),h=o.where(e,{filterId:5}),p=o.where(e,{filterId:6})),console.log("fyData"),console.log(l),console.log("periodData"),console.log(s),console.log("reportData"),console.log(c),console.log("this.reportPeriodValue"),console.log(this.reportPeriodValue),console.log("this.periodFilter"),console.log(this.periodFilter),console.log("this.reportTypeFilte"),console.log(this.reportTypeFilter),console.log("reportingCurrencyFilterValue"),console.log(this.reportingCurrencyFilterValue),r=l&&l.length>0?l[0].filterStartValue:this.fyFilter,i=s&&s.length>0?s[0].filterEndValue:this.reportPeriodValue,a=s&&s.length>0?s[0].filterStartValue:this.periodFilter,n=c&&c.length>0?c[0].filterStartValue:this.reportTypeFilter,d=u&&u.length>0?u[0].filterStartValue:this.reportingCurrencyFilterValue,f=p&&p.length>0?p[0].multiOptionSelectSearchValues:null,this.filterConfig={startIndex:0,mainFilterArray:t,startDate:this._udrfService.udrfData.mainApiDateRangeStart,endDate:this._udrfService.udrfData.mainApiDateRangeEnd,txTableDetails:this._udrfService.udrfData.txTableDetails,mainSearchParameter:this._udrfService.udrfData.mainSearchParameter,searchTableDetails:this._udrfService.udrfData.searchTableDetails,report_id:this.reportId,fyFilter:r,report_period:a,report_period_value:i,report_type:n,reporting_currency:d,project_id:h&&h.length>0?h[0].multiOptionSelectSearchValues:null,p_and_l:f},console.log("aaaaaaaaaaaaaaaaaaaaaaaa");let m=yield this._rrReportsService.getRRPandLReportData(this.reportId,this.filterConfig);"S"==m.messType?(this._spinnerService.hide(),this.snackBar.open(m.messText,"Dismiss",{duration:2e3}),this.reportData=m.result,this.columnConfig=m.column_config,this.misDataGridSummaryTotalConfig=this.columnConfig.length>0?o.filter(this.columnConfig,e=>{if(e.summaryType)return e}):[]):(this._spinnerService.hide(),this.snackBar.open(m.messText,"Dismiss",{duration:2e3})),this._spinnerService.hide()}))}getsubdivsionDataList(){return Object(a.c)(this,void 0,void 0,(function*(){this._spinnerService.show(),console.log("UDRF call while reset"),console.log("UDRF call while init UDRF"),console.log("this._udrfService.udrfData"),console.log(this._udrfService.udrfData);let e=JSON.parse(JSON.stringify(this._udrfService.udrfData.mainFilterArray)),t=e,r=null,i=null,a=null,n=null,l=null,s=null,c=null,d=null,u=null,h=null,p=null,f=null,m=null,g=null,D=null;e&&e.length>0&&(l=o.where(e,{filterId:1}),s=o.where(e,{filterId:2}),c=o.where(e,{filterId:3}),u=o.where(e,{filterId:4}),p=o.where(e,{filterId:7}),m=o.where(e,{filterId:8}),D=o.where(e,{filterId:9})),console.log("fyData"),console.log(l),console.log("periodData"),console.log(s),console.log("reportData"),console.log(c),console.log("this.reportPeriodValue"),console.log(this.reportPeriodValue),console.log("this.periodFilter"),console.log(this.periodFilter),console.log("this.reportTypeFilte"),console.log(this.reportTypeFilter),console.log("reportingCurrencyFilterValue"),console.log(this.reportingCurrencyFilterValue),r=l&&l.length>0?l[0].filterStartValue:this.fyFilter,i=s&&s.length>0?s[0].filterEndValue:this.reportPeriodValue,a=s&&s.length>0?s[0].filterStartValue:this.periodFilter,n=c&&c.length>0?c[0].filterStartValue:this.reportTypeFilter,d=u&&u.length>0?u[0].filterStartValue:this.reportingCurrencyFilterValue,h=p&&p.length>0?p[0].multiOptionSelectSearchValues:null,f=m&&m.length>0?m[0].multiOptionSelectSearchValues:null,g=D&&D.length>0?D[0].multiOptionSelectSearchValues:null,this.filterConfig={startIndex:0,mainFilterArray:t,startDate:this._udrfService.udrfData.mainApiDateRangeStart,endDate:this._udrfService.udrfData.mainApiDateRangeEnd,txTableDetails:this._udrfService.udrfData.txTableDetails,mainSearchParameter:this._udrfService.udrfData.mainSearchParameter,searchTableDetails:this._udrfService.udrfData.searchTableDetails,report_id:this.reportId,fyFilter:r,report_period:a,report_period_value:i,report_type:n,reporting_currency:d,entity_id:h,division_id:f,subdivision_id:g},console.log("aaaaaaaaaaaaaaaaaaaaaaaa");let b=yield this._rrReportsService.getSubdivisionReportsData(this.reportId,this.filterConfig);"S"==b.messType?(this._spinnerService.hide(),this.snackBar.open(b.messText,"Dismiss",{duration:2e3}),this.reportData=b.result,console.log("this.reportData"),console.log(this.reportData),this.columnConfig=b.column_config,console.log("this.columnConfig"),console.log(this.columnConfig),this.misDataGridSummaryTotalConfig=this.columnConfig.length>0?o.filter(this.columnConfig,e=>{if(e.summaryType)return e}):[]):(this._spinnerService.hide(),this.snackBar.open(b.messText,"Dismiss",{duration:2e3})),this._spinnerService.hide()}))}onCellPrepared(e){let t=null,r=null,i=JSON.parse(JSON.stringify(this._udrfService.udrfData.mainFilterArray));if(i&&i.length>0&&(t=o.where(i,{filterId:3})),r=t&&t.length>0?t[0].filterStartValue:this.reportTypeFilter,"header"===e.rowType&&e.column.dataField&&"Monthly"==r){let t=new Date(e.column.dataField.replace("-"," ")),r=new Date(this.mis_upload_date);const i=t.getFullYear(),a=t.getMonth(),o=r.getFullYear(),n=r.getMonth()+1;isNaN(t.getTime())||(e.cellElement.style.backgroundColor=i<o||i==o&&a<n?"#79BA44":"#515965")}}getCustomerReportData(){return Object(a.c)(this,void 0,void 0,(function*(){this._spinnerService.show(),console.log(this._udrfService.udrfData);let e=JSON.parse(JSON.stringify(this._udrfService.udrfData.mainFilterArray)),t=e,r=null,i=null,a=null,n=null,l=null,s=null,c=null,d=null,u=null,h=null,p=null;e&&e.length>0&&(l=o.where(e,{filterId:1}),s=o.where(e,{filterId:2}),c=o.where(e,{filterId:3}),u=o.where(e,{filterId:4}),p=o.where(e,{filterId:5})),console.log("fyData"),console.log(l),console.log("periodData"),console.log(s),console.log("reportData"),console.log(c),console.log("this.reportPeriodValue"),console.log(this.reportPeriodValue),console.log("this.periodFilter"),console.log(this.periodFilter),console.log("this.reportTypeFilte"),console.log(this.reportTypeFilter),console.log("reportingCurrencyFilterValue"),console.log(this.reportingCurrencyFilterValue),r=l&&l.length>0?l[0].filterStartValue:this.fyFilter,i=s&&s.length>0?s[0].filterEndValue:this.reportPeriodValue,a=s&&s.length>0?s[0].filterStartValue:this.periodFilter,n=c&&c.length>0?c[0].filterStartValue:this.reportTypeFilter,d=u&&u.length>0?u[0].filterStartValue:this.reportingCurrencyFilterValue,h=p&&p.length>0?p[0].multiOptionSelectSearchValues:null,this.filterConfig={startIndex:0,mainFilterArray:t,startDate:this._udrfService.udrfData.mainApiDateRangeStart,endDate:this._udrfService.udrfData.mainApiDateRangeEnd,txTableDetails:this._udrfService.udrfData.txTableDetails,mainSearchParameter:this._udrfService.udrfData.mainSearchParameter,searchTableDetails:this._udrfService.udrfData.searchTableDetails,report_id:this.reportId,fyFilter:r,report_period:a,report_period_value:i,report_type:n,reporting_currency:d,project_id:h},console.log("aaaaaaaaaaaaaaaaaaaaaaaa");let f=yield this._rrReportsService.getCustomerReportData(this.reportId,this.filterConfig);"S"==f.messType?(this._spinnerService.hide(),this.snackBar.open(f.messText,"Dismiss",{duration:2e3}),this.reportData=f.result,console.log("this.reportData"),console.log(this.reportData),this.columnConfig=f.column_config,console.log("this.columnConfig"),console.log(this.columnConfig),this.misDataGridSummaryTotalConfig=this.columnConfig.length>0?o.filter(this.columnConfig,e=>{if(e.summaryType)return e}):[]):(this._spinnerService.hide(),this.snackBar.open(f.messText,"Dismiss",{duration:2e3})),this._spinnerService.hide()}))}udrffilterconfig(){return Object(a.c)(this,void 0,void 0,(function*(){console.log("inside udrf");let e=this.reportingCurrenyData.data,t=[{checkboxId:"CDCRD",checkboxName:e[0].checkboxName,checkboxStartValue:e[0].checkboxName,checkboxEndValue:e[0].checkboxName,isCheckboxDefaultSelected:!0},{checkboxId:"CDCRD1",checkboxName:e[1].checkboxName,checkboxStartValue:e[1].checkboxName,checkboxEndValue:e[1].checkboxName,isCheckboxDefaultSelected:!1}];this._udrfService.resetUdrfData(),console.log("UDRF call while reset");let r=yield this._rrReportsService.getFinancialYearDetails();this.fyStartMonth=r.data[0].fy_start_month,this.fyEndMonth=r.data[0].fy_end_month;let i=this.fyStartMonth,a=this.fyEndMonth;const o=n().year(),l=n().subtract(1,"year").year();let s,c,d,u;i==this.fyStartMonth?(s=n().year(o).month(i-1).endOf("month"),c=n().year(a<i?o+1:o).month(a-1).endOf("month"),d=n().year(l).month(i-1).endOf("month"),u=n().year(a<i?l+1:l).month(a-1).endOf("month")):(s=n().year(o).month(i-1).endOf("month"),c=n().year(o).month(a-1).endOf("month"),d=n().year(l).month(i-1).endOf("month"),u=n().year(l).month(a-1).endOf("month")),this.fyFilter="FY "+n(s).format("YY"),this.durationRanges=[{checkboxId:"CDCRD",checkboxName:"FY "+n(s).subtract(4,"years").format("YY"),checkboxStartValue:n(s).subtract(4,"years").format("YYYY-MM-DD"),checkboxEndValue:n(c).subtract(4,"years").format("YYYY-MM-DD"),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD1",checkboxName:"FY "+n(s).subtract(3,"years").format("YY"),checkboxStartValue:n(s).subtract(3,"years").format("YYYY-MM-DD"),checkboxEndValue:n(c).subtract(3,"years").format("YYYY-MM-DD"),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD2",checkboxName:"FY "+n(s).subtract(2,"years").format("YY"),checkboxStartValue:n(s).subtract(2,"years").format("YYYY-MM-DD"),checkboxEndValue:n(c).subtract(2,"years").format("YYYY-MM-DD"),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD3",checkboxName:"FY "+n(s).subtract(1,"years").format("YY"),checkboxStartValue:n(s).subtract(1,"years").format("YYYY-MM-DD"),checkboxEndValue:n(c).subtract(1,"years").format("YYYY-MM-DD"),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD4",checkboxName:"FY "+n(s).format("YY"),checkboxStartValue:n(s).format("YYYY-MM-DD"),checkboxEndValue:n(c).format("YYYY-MM-DD"),isCheckboxDefaultSelected:!0},{checkboxId:"CDCRD5",checkboxName:"FY "+n(s).add(1,"years").format("YY"),checkboxStartValue:n(s).add(1,"years").format("YYYY-MM-DD"),checkboxEndValue:n(c).add(1,"years").format("YYYY-MM-DD"),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD6",checkboxName:"FY "+n(s).add(2,"years").format("YY"),checkboxStartValue:n(s).add(2,"years").format("YYYY-MM-DD"),checkboxEndValue:n(c).add(2,"years").format("YYYY-MM-DD"),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD7",checkboxName:"FY "+n(s).add(3,"years").format("YY"),checkboxStartValue:n(s).add(3,"years").format("YYYY-MM-DD"),checkboxEndValue:n(c).add(3,"years").format("YYYY-MM-DD"),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD8",checkboxName:"FY "+n(s).add(4,"years").format("YY"),checkboxStartValue:n(s).add(4,"years").format("YYYY-MM-DD"),checkboxEndValue:n(c).add(4,"years").format("YYYY-MM-DD"),isCheckboxDefaultSelected:!1}],this.periodFilter="Yearly",this.reportPeriodValue="Yearly",this.reportTypeFilter="Monthly",this.reportingCurrencyFilterValue="USD",this._udrfService.udrfFunctions.constructCustomRangeData(1,"date",this.durationRanges),this._udrfService.udrfFunctions.constructCustomRangeData(2,"value",[{checkboxId:"CDCRD",checkboxName:"Full Year",checkboxStartValue:"Yearly",checkboxEndValue:"Yearly",isCheckboxDefaultSelected:!0},{checkboxId:"CDCRD1",checkboxName:"Q1",checkboxStartValue:"Quarterly",checkboxEndValue:"Q1",isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD2",checkboxName:"Q2",checkboxStartValue:"Quarterly",checkboxEndValue:"Q2",isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD3",checkboxName:"Q3",checkboxStartValue:"Quarterly",checkboxEndValue:"Q3",isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD4",checkboxName:"Q4",checkboxStartValue:"Quarterly",checkboxEndValue:"Q4",isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD5",checkboxName:"H1",checkboxStartValue:"Half Yearly 1",checkboxEndValue:"H1",isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD6",checkboxName:"H2",checkboxStartValue:"Half Yearly 2",checkboxEndValue:"H2",isCheckboxDefaultSelected:!1}]),this._udrfService.udrfFunctions.constructCustomRangeData(3,"value",[{checkboxId:"CDCRD",checkboxName:"Monthly",checkboxStartValue:"Monthly",checkboxEndValue:"Monthly",isCheckboxDefaultSelected:!0},{checkboxId:"CDCRD1",checkboxName:"Yearly",checkboxStartValue:"Yearly",checkboxEndValue:"Yearly",isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD2",checkboxName:"Quarterly",checkboxStartValue:"Quarterly",checkboxEndValue:"Quarterly",isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD3",checkboxName:"Half-Yearly",checkboxStartValue:"Half Yearly",checkboxEndValue:"Half Yearly",isCheckboxDefaultSelected:!1}]),console.log("reportingCurrencyFilteraaa"),console.log(t),console.log("reportingCurrencyFilterValue"),console.log(this.reportingCurrencyFilterValue),this._udrfService.udrfFunctions.constructCustomRangeData(4,"value",t),yield this.initUdrf(),console.log("UDRF call while init UDRF")}))}}return e.\u0275fac=function(t){return new(t||e)(m["\u0275\u0275directiveInject"](b),m["\u0275\u0275directiveInject"](D.a),m["\u0275\u0275directiveInject"](S.a),m["\u0275\u0275directiveInject"](p.b),m["\u0275\u0275directiveInject"](f.c))},e.\u0275cmp=m["\u0275\u0275defineComponent"]({type:e,selectors:[["app-rr-reports-landing-page"]],decls:15,vars:5,consts:[[1,"container-fluid"],[1,"row"],[1,"col-2","mt-2"],["mat-raised-button","","matTooltip","RR reports",1,"btn-active",2,"font-weight","normal","margin-left","1%","margin-bottom","1%",3,"matMenuTriggerFor"],["menu","matMenu"],["mat-menu-item","","class","matOptions",4,"ngFor","ngForOf"],[1,"col","mt-3","d-flex","justify-content-center"],["style","font-size:14px;font-weight: 500;color:#cf0001",4,"ngIf"],[1,"col-2","mt-2","d-flex","justify-content-end"],["mat-icon-button","","class","view-button-inactive-NonGhost","matTooltip","Apply Filters And Sort",3,"disabled","click",4,"ngIf"],["class","row mt-2",4,"ngIf"],["size","medium","type","ball-clip-rotate","bdColor","rgba(236, 233, 233, 0.8)","color","#cf0001"],["mat-menu-item","",1,"matOptions"],[1,"row","p-1"],[1,"col-8","p-0",3,"click"],[2,"font-size","14px","font-weight","500","color","#cf0001"],["mat-icon-button","","matTooltip","Apply Filters And Sort",1,"view-button-inactive-NonGhost",3,"disabled","click"],["class","iconButton",4,"ngIf"],["class","spinner-align","diameter","18",4,"ngIf"],[1,"iconButton"],["diameter","18",1,"spinner-align"],[1,"row","mt-2"],[1,"col-12"],["id","gridContainer",1,"dev-style","custom-grid",3,"allowColumnResizing","dataSource","showBorders","hoverStateEnabled","columnAutoWidth","sorting","onCellPrepared"],[3,"enabled","fileName"],["mode","select",3,"enabled"],[3,"enabled"],[3,"visible"],["type","localStorage","storageKey","storage",3,"enabled"],["placeholder","Search...",3,"visible","width"],["mode","single"],[3,"dataField","allowReordering","caption","alignment","fixed",4,"ngFor","ngForOf"],[3,"dataField","allowReordering","caption","alignment","fixed"]],template:function(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"div",0),m["\u0275\u0275elementStart"](1,"div",1),m["\u0275\u0275elementStart"](2,"div",2),m["\u0275\u0275elementStart"](3,"button",3),m["\u0275\u0275text"](4,"Reports"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](5,"mat-menu",null,4),m["\u0275\u0275template"](7,y,4,1,"button",5),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](8,"div",6),m["\u0275\u0275template"](9,v,2,1,"span",7),m["\u0275\u0275element"](10,"span"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](11,"div",8),m["\u0275\u0275template"](12,_,3,3,"button",9),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](13,F,13,18,"div",10),m["\u0275\u0275elementEnd"](),m["\u0275\u0275element"](14,"ngx-spinner",11)),2&e){const e=m["\u0275\u0275reference"](6);m["\u0275\u0275advance"](3),m["\u0275\u0275property"]("matMenuTriggerFor",e),m["\u0275\u0275advance"](4),m["\u0275\u0275property"]("ngForOf",t.rrMenu),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("ngIf",t.reportName),m["\u0275\u0275advance"](3),m["\u0275\u0275property"]("ngIf",t._udrfService.udrfUiData.showUdrfModalButton),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",t.reportData)}},directives:[c.a,u.a,h.f,h.g,l.NgForOf,l.NgIf,f.a,h.d,d.a,s.a,x.Sb,x.tb,x.vb,x.xc,x.le,x.Md,x.Od,x.Cc,x.dc,x.g],styles:[".view-button-inactive-NonGhost[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;margin-top:5px!important;line-height:8px;width:30px;height:30px;margin-right:10px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12);animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}.view-button-inactive-NonGhost[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.view-button-inactive-NonGhost[_ngcontent-%COMP%]   .iconButtonWarn[_ngcontent-%COMP%]{color:#cf0001;font-size:18px}.view-button-inactive-NonGhost[_ngcontent-%COMP%]   .iconButtonWarnAnim[_ngcontent-%COMP%]{color:#cf0001;font-size:18px;animation:blink 1.1s cubic-bezier(.5,0,1,1) infinite alternate}.view-button-inactive-NonGhost[_ngcontent-%COMP%]   .spinner-align[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}.btn-active[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff;font-weight:400;font-size:13px!important;min-width:60px;line-height:33px;padding:0 8px;border-radius:4px;margin-right:6px!important;margin-bottom:3px}[_nghost-%COMP%]     .dev-style.custom-grid .dx-header-row{background-color:#f27a6c!important;color:#fff!important;font-weight:700!important}"]}),e})()}];let V=(()=>{class e{}return e.\u0275mod=m["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=m["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.k.forChild(w)],i.k]}),e})()}}]);