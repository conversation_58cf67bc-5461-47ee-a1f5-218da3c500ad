(window.webpackJsonp=window.webpackJsonp||[]).push([[726,214],{"/1uk":function(_,e,n){"use strict";n.d(e,"a",(function(){return r}));var t=n("fXoL"),a=n("tk/3");let r=(()=>{class _{constructor(_){this.http=_}getProjectAttachmentList(_,e,n){return new Promise((t,a)=>{this.http.post("/api/pm/planning/getProjectAttachmentList",{project_id:_,item_id:e,source:n}).subscribe(_=>t(_),_=>a(_))})}insertAttachmentFiles(_,e,n,t,a){return new Promise((r,i)=>{this.http.post("/api/pm/planning/insertAttachmentFiles",{project_id:_,item_id:e,uploadedFiles:n,parent:t,source:a}).subscribe(_=>r(_),_=>i(_))})}insertFolder(_,e,n,t,a){return new Promise((r,i)=>{this.http.post("/api/pm/planning/insertFolder",{project_id:_,item_id:e,folder_name:n,parent:t,source:a}).subscribe(_=>r(_),_=>i(_))})}deleteAttachmentRow(_,e,n,t){return new Promise((a,r)=>{this.http.post("/api/pm/planning/deleteAttachmentRow",{project_id:_,item_id:e,id:n,source:t}).subscribe(_=>a(_),_=>r(_))})}updateUploadedFiles(_){return new Promise((e,n)=>{this.http.post("/api/pm/planning/updateAttachmentFiles",_).subscribe(_=>e(_),_=>n(_))})}updateFolderName(_){return new Promise((e,n)=>{this.http.post("/api/pm/planning/updateFolder",_).subscribe(_=>e(_),_=>n(_))})}getExistingTags(){return new Promise((_,e)=>{this.http.post("/api/pm/planning/getExistingTags",{}).subscribe(e=>_(e),_=>e(_))})}getTagsColor(){return new Promise((_,e)=>{this.http.post("/api/pm/planning/getTagsColor",{}).subscribe(e=>_(e),_=>e(_))})}insertTag(_,e){return new Promise((n,t)=>{this.http.post("/api/pm/planning/insertTag",{name:_,color:e}).subscribe(_=>n(_),_=>t(_))})}getTagsProject(_){return new Promise((e,n)=>{this.http.post("/api/pm/planning/getTagsProject",{item_id:_}).subscribe(_=>e(_),_=>n(_))})}getMasterDataUsingApi(_,e,n){return new Promise((t,a)=>{this.http[e](_,n).subscribe(_=>{console.log(_),t(_)},_=>{console.log(_),a(_)})})}getDocumentTypeMaster(_){return new Promise((e,n)=>{this.http.post("/api/pm/masterData/getDocumentTypeMaster",{source:_}).subscribe(_=>e(_),_=>n(_))})}getEmployeeNames(_){return new Promise((e,n)=>{this.http.post("/api/pm/planning/getEmployeeNames",{associateIds:_}).subscribe(_=>e(_),_=>n(_))})}checkDocumentType(){return new Promise((_,e)=>{this.http.post("/api/pm/report/checkProjectDocument",{}).subscribe(e=>_(e),_=>e(_))})}}return _.\u0275fac=function(e){return new(e||_)(t["\u0275\u0275inject"](a.c))},_.\u0275prov=t["\u0275\u0275defineInjectable"]({token:_,factory:_.\u0275fac,providedIn:"root"}),_})()},"1+mW":function(_,e,n){"use strict";n.r(e),n.d(e,"ApplicantTrackingSystemModule",(function(){return N}));var t=n("ofXK"),a=n("tyNb"),r=n("mrSG"),i=n("1G5W"),o=n("XNiG"),c=n("yuIm"),l=n("fXoL"),s=n("c7zN"),E=n("XXEo"),g=n("XNFG");let p=(()=>{class _{constructor(_,e,n){this._utilitiesService=_,this._loginService=e,this._toaster=n,this._onDestroy=new o.b}canActivate(_,e){return Object(r.c)(this,void 0,void 0,(function*(){return yield this.checkAccessForAssociate(),!0}))}checkAccessForAssociate(){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((_,e)=>this._utilitiesService.checkAccessForAssociate(this._loginService.getProfile().profile.aid).pipe(Object(i.a)(this._onDestroy)).subscribe({next:e=>{if(0==e.err)c.setRoleAccessList(e.data);else{let _=!(!e||!e.hasOwnProperty("is_rds_peak"))&&e.is_rds_peak;c.changeRDSvalue(_||!1),c.setRoleAccessList([]),this._toaster.showError("Error",e.msg,7e3)}_(!0)},error:_=>{this._toaster.showError("Error",_.message?_.message:"Role Data Retrieval Failed!",7e3),c.setRoleAccessList([]),e()}}))}))}}return _.\u0275fac=function(e){return new(e||_)(l["\u0275\u0275inject"](s.a),l["\u0275\u0275inject"](E.a),l["\u0275\u0275inject"](g.a))},_.\u0275prov=l["\u0275\u0275defineInjectable"]({token:_,factory:_.\u0275fac,providedIn:"root"}),_})();const u=[{path:"",canActivate:[p],pathMatch:"full",redirectTo:"jobs"},{path:"jobs",canActivate:[p],loadChildren:()=>n.e(872).then(n.bind(null,"Lp0Z")).then(_=>_.JobsModule),data:{breadcrumb:"Jobs"}},{path:"candidates",canActivate:[p],loadChildren:()=>n.e(871).then(n.bind(null,"X1Mi")).then(_=>_.CandidatesModule),data:{breadcrumb:"Candidates"}},{path:"settings",canActivate:[p],loadChildren:()=>n.e(880).then(n.bind(null,"VSH1")).then(_=>_.SettingsModule),data:{breadcrumb:"Settings"}},{path:"reports",canActivate:[],loadChildren:()=>n.e(0).then(n.bind(null,"Utwq")).then(_=>_.ProductReportModule),data:{breadcrumb:"Reports"}},{path:"onboarding",canActivate:[p],loadChildren:()=>n.e(873).then(n.bind(null,"vMvN")).then(_=>_.OnboardingModule),data:{breadcrumb:"Onboarding"}}];let P=(()=>{class _{}return _.\u0275mod=l["\u0275\u0275defineNgModule"]({type:_}),_.\u0275inj=l["\u0275\u0275defineInjector"]({factory:function(e){return new(e||_)},imports:[[a.k.forChild(u)],a.k]}),_})();var d=n("Xi0T"),O=n("0IaG"),M=n("bSwM"),D=n("Qu3c"),C=n("lVl8"),m=n("NFeN"),h=n("5+WD"),I=n("3Pt+"),L=n("kmnG"),A=n("qFsG"),T=n("rDax"),f=n("Wp6s"),R=n("iadO"),B=n("Xa2L"),U=n("1jcm"),v=n("4/q7"),W=n("f0Cb"),K=n("A5z7"),x=n("d3UM"),b=n("vxfF"),y=n("wZkO"),w=n("dlKe"),S=n("cZdB"),k=n("w4ga"),j=n("WJ5W"),V=n("YhS8"),F=n("pzj6"),q=n("mgaL"),z=n("3beV");let N=(()=>{class _{}return _.\u0275mod=l["\u0275\u0275defineNgModule"]({type:_}),_.\u0275inj=l["\u0275\u0275defineInjector"]({factory:function(e){return new(e||_)},imports:[[t.CommonModule,z.a,P,d.a,O.g,M.b,D.b,C.b,m.b,w.b,h.g,I.E,I.p,L.e,A.c,T.h,f.d,R.h,S.b,B.b,v.b,U.b,k.b,W.b,K.e,x.d,j.b,V.c.forRoot(),b.g,y.g,F.b,q.a]]}),_})()},"1mzr":function(_,e,n){"use strict";n.d(e,"a",(function(){return a}));var t=n("fXoL");let a=(()=>{class _{transform(_,e,n){if(!e||""==e)return"-";let t="string"==typeof _?parseFloat(_):_,a="INR"==e?"en-IN":"en-US",r="INR"==e?"Cr":"M";n&&(t="INR"==e?(t/1e7).toFixed(2):(t/1e6).toFixed(2));let i=new Intl.NumberFormat(a,{currency:e,minimumFractionDigits:2,maximumFractionDigits:2}).format(t);return isNaN(t)?"-":i+""+(n?" "+r:"")}}return _.\u0275fac=function(e){return new(e||_)},_.\u0275pipe=t["\u0275\u0275definePipe"]({name:"currencyFormat",type:_,pure:!0}),_})()},CU7G:function(_,e,n){"use strict";n.d(e,"a",(function(){return u}));var t=n("mrSG"),a=n("fXoL"),r=n("/1uk"),i=n("lVl8"),o=n("ofXK");const c=["customTooltip"];function l(_,e){if(1&_&&(a["\u0275\u0275elementStart"](0,"div"),a["\u0275\u0275elementStart"](1,"div",4),a["\u0275\u0275elementStart"](2,"div",5),a["\u0275\u0275elementStart"](3,"span"),a["\u0275\u0275elementStart"](4,"b"),a["\u0275\u0275text"](5,"Name"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](6,"span",6),a["\u0275\u0275text"](7," :"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](8,"span",7),a["\u0275\u0275text"](9),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&_){const _=e.$implicit;a["\u0275\u0275advance"](9),a["\u0275\u0275textInterpolate1"]("",null==_?null:_.employee_name," ")}}function s(_,e){if(1&_&&a["\u0275\u0275template"](0,l,10,1,"div",3),2&_){const _=a["\u0275\u0275nextContext"]();a["\u0275\u0275property"]("ngForOf",_.peopleListData)}}function E(_,e){if(1&_&&(a["\u0275\u0275elementStart"](0,"div"),a["\u0275\u0275elementStart"](1,"div",8),a["\u0275\u0275elementStart"](2,"div",9),a["\u0275\u0275namespaceSVG"](),a["\u0275\u0275elementStart"](3,"svg",10),a["\u0275\u0275elementStart"](4,"g",11),a["\u0275\u0275element"](5,"path",12),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](6,"defs"),a["\u0275\u0275elementStart"](7,"clipPath",13),a["\u0275\u0275element"](8,"rect",14),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275namespaceHTML"](),a["\u0275\u0275elementStart"](9,"div",15),a["\u0275\u0275namespaceSVG"](),a["\u0275\u0275elementStart"](10,"svg",10),a["\u0275\u0275elementStart"](11,"g",11),a["\u0275\u0275element"](12,"path",12),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](13,"defs"),a["\u0275\u0275elementStart"](14,"clipPath",13),a["\u0275\u0275element"](15,"rect",14),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275namespaceHTML"](),a["\u0275\u0275elementStart"](16,"div",16),a["\u0275\u0275text"](17),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&_){const _=a["\u0275\u0275nextContext"]();a["\u0275\u0275advance"](16),a["\u0275\u0275styleProp"]("width",_.width,"px")("height",_.height,"px")("font-size",_.font_size,"px"),a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate1"](" +",_.count-2," ")}}function g(_,e){1&_&&(a["\u0275\u0275elementStart"](0,"div"),a["\u0275\u0275elementStart"](1,"div",8),a["\u0275\u0275elementStart"](2,"div",9),a["\u0275\u0275namespaceSVG"](),a["\u0275\u0275elementStart"](3,"svg",10),a["\u0275\u0275elementStart"](4,"g",11),a["\u0275\u0275element"](5,"path",12),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](6,"defs"),a["\u0275\u0275elementStart"](7,"clipPath",13),a["\u0275\u0275element"](8,"rect",14),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275namespaceHTML"](),a["\u0275\u0275elementStart"](9,"div",15),a["\u0275\u0275namespaceSVG"](),a["\u0275\u0275elementStart"](10,"svg",10),a["\u0275\u0275elementStart"](11,"g",11),a["\u0275\u0275element"](12,"path",12),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](13,"defs"),a["\u0275\u0275elementStart"](14,"clipPath",13),a["\u0275\u0275element"](15,"rect",14),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]())}function p(_,e){1&_&&(a["\u0275\u0275elementStart"](0,"div"),a["\u0275\u0275elementStart"](1,"div",8),a["\u0275\u0275elementStart"](2,"div",9),a["\u0275\u0275namespaceSVG"](),a["\u0275\u0275elementStart"](3,"svg",10),a["\u0275\u0275elementStart"](4,"g",11),a["\u0275\u0275element"](5,"path",12),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](6,"defs"),a["\u0275\u0275elementStart"](7,"clipPath",13),a["\u0275\u0275element"](8,"rect",14),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]())}let u=(()=>{class _{constructor(_,e){this.renderer=_,this.pmSharedService=e,this.peopleList=[],this.width="14px",this.height="14px",this.fontSize="12px",this.count=3,this.bgColor="#F27A6C",this.oid=!1,this.isTooltipOpen=!1,this.tooltipStyles={}}ngOnInit(){return Object(t.c)(this,void 0,void 0,(function*(){document.documentElement.style.setProperty("--bgColor",this.bgColor),this.peopleList&&this.peopleList.length&&this.peopleList.length>0&&(yield this.pmSharedService.getEmployeeNames(this.peopleList).then(_=>{"S"==_.messType&&_.data.length>0&&(this.peopleListData=_.data)}))}))}ngOnChanges(){return Object(t.c)(this,void 0,void 0,(function*(){this.peopleList&&this.peopleList.length&&this.peopleList.length>0?(this.count=this.peopleList.length,yield this.pmSharedService.getEmployeeNames(this.peopleList).then(_=>{"S"==_.messType&&_.data.length>0&&(this.peopleListData=_.data)})):(this.peopleList=[],this.count=0)}))}showCustomTooltip(_){this.tooltipStyles={top:_.clientY-230+"px",display:"block"},this.isTooltipOpen=!0,this.renderer.setStyle(this.customTooltip.nativeElement,"display","block")}hideCustomTooltip(){this.isTooltipOpen=!1,this.renderer.setStyle(this.customTooltip.nativeElement,"display","none")}}return _.\u0275fac=function(e){return new(e||_)(a["\u0275\u0275directiveInject"](a.Renderer2),a["\u0275\u0275directiveInject"](r.a))},_.\u0275cmp=a["\u0275\u0275defineComponent"]({type:_,selectors:[["app-people-icon-display"]],viewQuery:function(_,e){if(1&_&&a["\u0275\u0275viewQuery"](c,!0),2&_){let _;a["\u0275\u0275queryRefresh"](_=a["\u0275\u0275loadQuery"]())&&(e.customTooltip=_.first)}},inputs:{peopleList:"peopleList",width:"width",height:"height",fontSize:"fontSize",count:"count",bgColor:"bgColor"},features:[a["\u0275\u0275NgOnChangesFeature"]],decls:6,vars:4,consts:[["peopleListTooltip",""],["content-type","template","max-width","300","placement","top",1,"people-display",3,"tooltip"],[4,"ngIf"],[4,"ngFor","ngForOf"],[1,"row",2,"font-size","12px","border-bottom","1px solid gray"],[1,"row",2,"font-size","12px","width","100%"],[2,"padding-left","1rem !important"],[1,"ml-1"],[1,"row","image-circle"],[1,"first-div"],["width","10","height","10","viewBox","0 0 10 10","fill","none","xmlns","http://www.w3.org/2000/svg",1,"svg-icon"],["clip-path","url(#clip0_14538_151133)"],["d","M2.11108 8.61117C2.11108 7.84499 2.41545 7.11018 2.95722 6.56841C3.49899 6.02664 4.23379 5.72228 4.99997 5.72228C5.76615 5.72228 6.50095 6.02664 7.04273 6.56841C7.5845 7.11018 7.88886 7.84499 7.88886 8.61117H7.16664C7.16664 8.03653 6.93837 7.48543 6.53204 7.0791C6.12571 6.67277 5.57461 6.4445 4.99997 6.4445C4.42534 6.4445 3.87424 6.67277 3.46791 7.0791C3.06158 7.48543 2.83331 8.03653 2.83331 8.61117H2.11108ZM4.99997 5.36117C3.80289 5.36117 2.83331 4.39158 2.83331 3.1945C2.83331 1.99742 3.80289 1.02783 4.99997 1.02783C6.19706 1.02783 7.16664 1.99742 7.16664 3.1945C7.16664 4.39158 6.19706 5.36117 4.99997 5.36117ZM4.99997 4.63894C5.79803 4.63894 6.44442 3.99255 6.44442 3.1945C6.44442 2.39644 5.79803 1.75005 4.99997 1.75005C4.20192 1.75005 3.55553 2.39644 3.55553 3.1945C3.55553 3.99255 4.20192 4.63894 4.99997 4.63894Z","fill","white"],["id","clip0_14538_151133"],["width","8.66667","height","8.66667","fill","white","transform","translate(0.666626 0.666504)"],[1,"second-div"],[1,"third-div"]],template:function(_,e){if(1&_&&(a["\u0275\u0275template"](0,s,1,1,"ng-template",null,0,a["\u0275\u0275templateRefExtractor"]),a["\u0275\u0275elementStart"](2,"div",1),a["\u0275\u0275template"](3,E,18,7,"div",2),a["\u0275\u0275template"](4,g,16,0,"div",2),a["\u0275\u0275template"](5,p,9,0,"div",2),a["\u0275\u0275elementEnd"]()),2&_){const _=a["\u0275\u0275reference"](1);a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("tooltip",_),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",e.count>2),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",2==e.count),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",1==e.count)}},directives:[i.a,o.NgIf,o.NgForOf],styles:[".people-display[_ngcontent-%COMP%]{display:inline-flex;cursor:pointer}.people-display[_ngcontent-%COMP%]   .first-div[_ngcontent-%COMP%]{width:24px;height:24px;border:1px solid var(--bgColor);border-radius:50%;margin-right:-10px;margin-top:0;background-color:var(--bgColor)}.people-display[_ngcontent-%COMP%]   .first-div[_ngcontent-%COMP%]   .svg-icon[_ngcontent-%COMP%]{margin-left:6px}.people-display[_ngcontent-%COMP%]   .second-div[_ngcontent-%COMP%]{width:24px;height:24px;border:1px solid #fff;border-radius:50%;margin-left:3px;background-color:var(--bgColor)}.people-display[_ngcontent-%COMP%]   .second-div[_ngcontent-%COMP%]   .svg-icon[_ngcontent-%COMP%]{margin-left:6px}.people-display[_ngcontent-%COMP%]   .third-div[_ngcontent-%COMP%]{width:24px;height:24px;background-color:#fff;border-radius:50%;margin-left:-8px;align-items:center;text-align:center;display:flex;justify-content:center;color:grey;border:1px solid grey;font-size:10px}.custom-tooltip[_ngcontent-%COMP%]{display:none;position:absolute;background-color:#fff!important;border:1px solid #ccc;padding:8px;z-index:9999}.image-circle[_ngcontent-%COMP%]{display:flex;min-width:26px}"]}),_})()},IEgo:function(_,e,n){"use strict";n.d(e,"a",(function(){return r}));var t=n("wd/R"),a=n("fXoL");let r=(()=>{class _{transform(_,e){return _?t(_).utc().format(e):"-"}}return _.\u0275fac=function(e){return new(e||_)},_.\u0275pipe=a["\u0275\u0275definePipe"]({name:"dateFormat",type:_,pure:!0}),_})()},OhSF:function(_,e,n){"use strict";n.d(e,"a",(function(){return E}));var t=n("ofXK"),a=n("tyNb"),r=n("fXoL");const i=[];let o=(()=>{class _{}return _.\u0275mod=r["\u0275\u0275defineNgModule"]({type:_}),_.\u0275inj=r["\u0275\u0275defineInjector"]({factory:function(e){return new(e||_)},imports:[[a.k.forChild(i)],a.k]}),_})();var c=n("NFeN"),l=n("wiS1"),s=n("STbY");let E=(()=>{class _{}return _.\u0275mod=r["\u0275\u0275defineNgModule"]({type:_}),_.\u0275inj=r["\u0275\u0275defineInjector"]({factory:function(e){return new(e||_)},imports:[[t.CommonModule,o,c.b,s.e,l.a]]}),_})()},OoYh:function(_,e,n){"use strict";n.d(e,"a",(function(){return a}));var t=n("fXoL");let a=(()=>{class _{transform(_,e,n){if("left"==n){let n=_.filter(_=>1==(null==_?void 0:_.is_pinned_left));if(1==n.length)return"0px";if(n.length>1){let t=_[e].id,a=n.findIndex(_=>_.id==t);if(0==a)return"0px";{let _=0;for(let e=0;e<a;e++)_+=parseInt(n[e].width)||0;return _+"px"}}}if("right"==n){let n=_.filter(_=>1==(null==_?void 0:_.is_pinned_right));if(1==n.length)return"0px";if(n.length>1){let t=_[e].id,a=n.findIndex(_=>_.id==t);if(a==n.length-1)return"0px";{let _=0;for(let e=a+1;e<n.length;e++)_+=parseInt(n[e].width)||0;return _+"px"}}}}}return _.\u0275fac=function(e){return new(e||_)},_.\u0275pipe=t["\u0275\u0275definePipe"]({name:"calculatePositionOfPinnedColumns",type:_,pure:!1}),_})()},VA38:function(_,e,n){"use strict";n.d(e,"a",(function(){return u}));var t=n("mrSG"),a=n("fXoL"),r=n("5+WD"),i=n("3Pt+"),o=n("XNFG"),c=n("ofXK"),l=n("bSwM"),s=n("Qu3c"),E=n("NFeN");function g(_,e){if(1&_&&(a["\u0275\u0275elementStart"](0,"span"),a["\u0275\u0275text"](1),a["\u0275\u0275elementEnd"]()),2&_){const _=a["\u0275\u0275nextContext"]().$implicit;a["\u0275\u0275classMap"](_.is_default_visible||null!=_&&_.is_pinned_left||null!=_&&_.is_pinned_right?"text-default-visible":"text-not-default-visible"),a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate1"](" ",_.label," ")}}function p(_,e){if(1&_&&(a["\u0275\u0275elementContainerStart"](0),a["\u0275\u0275elementStart"](1,"div",6),a["\u0275\u0275elementStart"](2,"div",7),a["\u0275\u0275elementStart"](3,"div"),a["\u0275\u0275element"](4,"mat-checkbox",8),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](5,"div",9),a["\u0275\u0275text"](6),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](7,"div",10),a["\u0275\u0275elementStart"](8,"mat-icon",11),a["\u0275\u0275text"](9,"drag_indicator"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](10,g,2,3,"span",12),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementContainerEnd"]()),2&_){const _=e.$implicit;a["\u0275\u0275advance"](3),a["\u0275\u0275classMap"](_.is_default_visible||null!=_&&_.is_pinned_left||null!=_&&_.is_pinned_right?"checkbox-disabled":"checkbox"),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("disabled",_.is_default_visible||(null==_?null:_.is_pinned_left)||(null==_?null:_.is_pinned_right))("formControlName",_.id),a["\u0275\u0275advance"](1),a["\u0275\u0275classMap"](_.is_default_visible||null!=_&&_.is_pinned_left||null!=_&&_.is_pinned_right?"text-default-visible":"text-not-default-visible"),a["\u0275\u0275property"]("matTooltip",_.label),a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate1"](" ",_.label," ")}}let u=(()=>{class _{constructor(_,e){this._fb=_,this._toaster=e,this.onApply=new a.EventEmitter,this.customizationData=[],this.columnCustomForms=this._fb.group({})}ngOnInit(){return Object(t.c)(this,void 0,void 0,(function*(){this.customizationData=[...this.customization];for(let _=0;_<this.customizationData.length;_++)this.columnCustomForms.addControl(this.customizationData[_].id,new i.j(this.customizationData[_].is_visible))}))}dropField(_){var e,n,t,a;let i=this.customizationData;console.log(_),(null===(e=i[_.previousIndex])||void 0===e?void 0:e.is_pinned_right)||(null===(n=i[_.previousIndex])||void 0===n?void 0:n.is_pinned_left)||(null===(t=i[_.currentIndex])||void 0===t?void 0:t.is_pinned_right)||(null===(a=i[_.currentIndex])||void 0===a?void 0:a.is_pinned_left)?this._toaster.showWarning("Warning \u26a0\ufe0f","Pinned Columns Cannot Be Altered!",7e3):(Object(r.h)(i,_.previousIndex,_.currentIndex),i.forEach((_,e)=>{i[e].position=e}),this.customizationData=i)}applyChanges(){let _=this.columnCustomForms.getRawValue();this.customizationData.forEach((e,n)=>{this.customizationData[n].is_visible=_[this.customizationData[n].id]}),this.onApply.emit(this.customizationData)}}return _.\u0275fac=function(e){return new(e||_)(a["\u0275\u0275directiveInject"](i.i),a["\u0275\u0275directiveInject"](o.a))},_.\u0275cmp=a["\u0275\u0275defineComponent"]({type:_,selectors:[["app-inbox-column-customization"]],inputs:{customization:"customization"},outputs:{onApply:"onApply"},decls:8,vars:2,consts:[[1,"bg-container",3,"formGroup"],[1,"d-flex","align-items-center","justify-content-between"],[1,"title-text"],[1,"apply-btn",3,"click"],["cdkDropList","",1,"d-flex","flex-column","list",3,"cdkDropListDropped"],[4,"ngFor","ngForOf"],["cdkDrag","",1,"d-flex","align-items-center","justify-content-between"],[1,"d-flex","align-items-center"],[3,"disabled","formControlName"],[3,"matTooltip"],["cdkDragHandle",""],[1,"icon"],[3,"class",4,"cdkDragPreview"]],template:function(_,e){1&_&&(a["\u0275\u0275elementStart"](0,"div",0),a["\u0275\u0275elementStart"](1,"div",1),a["\u0275\u0275elementStart"](2,"div",2),a["\u0275\u0275text"](3,"Customization Field"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](4,"div",3),a["\u0275\u0275listener"]("click",(function(){return e.applyChanges()})),a["\u0275\u0275text"](5,"Apply"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](6,"div",4),a["\u0275\u0275listener"]("cdkDropListDropped",(function(_){return e.dropField(_)})),a["\u0275\u0275template"](7,p,11,8,"ng-container",5),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&_&&(a["\u0275\u0275property"]("formGroup",e.columnCustomForms),a["\u0275\u0275advance"](7),a["\u0275\u0275property"]("ngForOf",e.customizationData))},directives:[i.w,i.n,r.e,c.NgForOf,r.a,l.a,i.v,i.l,s.a,r.b,E.a,r.d],styles:[".bg-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;border:3px solid #b9c0ca;border-radius:8px;padding:16px;width:225px;gap:8px;background-color:#fff}.title-text[_ngcontent-%COMP%]{font-weight:400;color:#b9c0ca}.apply-btn[_ngcontent-%COMP%], .title-text[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-size:12px}.apply-btn[_ngcontent-%COMP%]{font-weight:700;padding:4px 8px;border-radius:4px;background:linear-gradient(270deg,var(--kebsPrimaryColor),var(--kebsPrimaryColor) 105.29%);cursor:pointer;color:#fff}.checkbox[_ngcontent-%COMP%]{height:16px;width:16px;margin-right:8px}.checkbox[_ngcontent-%COMP%]     .mat-checkbox-checked.mat-accent .mat-checkbox-background{background-color:var(--kebsPrimaryColor)!important}.checkbox[_ngcontent-%COMP%]     .mat-checkbox-frame{border-color:#b9c0ca!important}.checkbox-disabled[_ngcontent-%COMP%]{height:16px;width:16px;margin-right:8px}.checkbox-disabled[_ngcontent-%COMP%]     .mat-checkbox-disabled .mat-checkbox-background{background-color:#b9c0ca!important}.checkbox-disabled[_ngcontent-%COMP%]     .mat-checkbox-frame{border-color:#b9c0ca!important}.text-default-visible[_ngcontent-%COMP%]{color:#5f6c81}.text-default-visible[_ngcontent-%COMP%], .text-not-default-visible[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-size:14px;font-weight:400;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;width:140px}.text-not-default-visible[_ngcontent-%COMP%]{color:var(--kebsPrimaryColor)}.icon[_ngcontent-%COMP%]{width:16px;height:16px;font-size:16px;color:#d4d6d8;cursor:move}.list[_ngcontent-%COMP%]{gap:8px;max-height:300px;overflow-y:auto}.list[_ngcontent-%COMP%]::-webkit-scrollbar{width:6px!important}"]}),_})()},"d6+l":function(_,e,n){"use strict";n.d(e,"a",(function(){return i}));var t=n("wd/R"),a=n("fXoL"),r=n("tk/3");let i=(()=>{class _{constructor(_){this._http=_}getInboxApplications(){return this._http.post("api/insights/kebsHomePage/getInboxApplications",{})}getDataDynamically(_,e){return e=e?Object.assign(Object.assign({},e),{currentDate:t().format("YYYY-MM-DD")}):{},this._http.post(_,e)}onClickActionsIcon(_,e){return this._http.post(e,_)}onClickDownloadAttachments(_,e){return this._http.post(e,_,{responseType:"blob"})}}return _.\u0275fac=function(e){return new(e||_)(a["\u0275\u0275inject"](r.c))},_.\u0275prov=a["\u0275\u0275defineInjectable"]({token:_,factory:_.\u0275fac,providedIn:"root"}),_})()},krUa:function(_,e,n){"use strict";n.r(e),n.d(e,"InboxModule",(function(){return R}));var t=n("ofXK"),a=n("tyNb"),r=n("yNiW"),i=n("fXoL");const o=[{path:"",component:r.a}];let c=(()=>{class _{}return _.\u0275mod=i["\u0275\u0275defineNgModule"]({type:_}),_.\u0275inj=i["\u0275\u0275defineInjector"]({factory:function(e){return new(e||_)},imports:[[a.k.forChild(o)],a.k]}),_})();var l=n("h6+J"),s=n("lr1n"),E=n("Utwq"),g=n("Xi0T"),p=n("+lh5"),u=n("mCRr"),P=n("f0Cb"),d=n("Qu3c"),O=n("lVl8"),M=n("NFeN"),D=n("jaxi"),C=n("3Pt+"),m=n("qFsG"),h=n("5+WD"),I=n("rDax"),L=n("bSwM"),A=n("dlKe"),T=n("0IaG"),f=n("STbY");let R=(()=>{class _{}return _.\u0275mod=i["\u0275\u0275defineNgModule"]({type:_}),_.\u0275inj=i["\u0275\u0275defineInjector"]({factory:function(e){return new(e||_)},imports:[[t.CommonModule,c,l.KebsHomeModule,s.a,E.ProductReportModule,g.a,p.TsV2ApprovalModule,u.a,P.b,d.b,O.b,M.b,D.c,C.p,C.E,m.c,h.g,I.h,L.b,A.b,T.g,f.e]]}),_})()},mCRr:function(_,e,n){"use strict";n.d(e,"a",(function(){return T}));var t=n("ofXK"),a=n("tyNb"),r=n("fXoL");const i=[];let o=(()=>{class _{}return _.\u0275mod=r["\u0275\u0275defineNgModule"]({type:_}),_.\u0275inj=r["\u0275\u0275defineInjector"]({factory:function(e){return new(e||_)},imports:[[a.k.forChild(i)],a.k]}),_})();var c=n("Xi0T"),l=n("OhSF"),s=n("1+mW"),E=n("NFeN"),g=n("f0Cb"),p=n("dlKe"),u=n("STbY"),P=n("Qu3c"),d=n("lVl8"),O=n("kmnG"),M=n("qFsG"),D=n("3Pt+"),C=n("cZdB"),m=n("rDax"),h=n("VI6+"),I=n("ZzPI"),L=n("pzj6"),A=n("XPKZ");let T=(()=>{class _{}return _.\u0275mod=r["\u0275\u0275defineNgModule"]({type:_}),_.\u0275inj=r["\u0275\u0275defineInjector"]({factory:function(e){return new(e||_)},imports:[[t.CommonModule,o,c.a,l.a,s.ApplicantTrackingSystemModule,E.b,g.b,u.e,P.b,p.b,d.b,O.e,M.c,D.p,D.E,h.b,I.b,L.b,A.b,C.b,m.h]]}),_})()},wiS1:function(_,e,n){"use strict";n.d(e,"a",(function(){return D}));var t=n("ofXK"),a=n("fXoL");function r(_,e){if(1&_&&a["\u0275\u0275element"](0,"div",5),2&_){const _=a["\u0275\u0275nextContext"]();a["\u0275\u0275propertyInterpolate1"]("ngClass","ngx-org-connector-",_.direction,"")}}const i=function(_){return{"background-image":_}};function o(_,e){if(1&_&&a["\u0275\u0275element"](0,"div",6),2&_){const _=a["\u0275\u0275nextContext"]();a["\u0275\u0275property"]("ngStyle",a["\u0275\u0275pureFunction1"](1,i,"url('"+(null==_.node?null:_.node.image)+"')"))}}function c(_,e){if(1&_&&a["\u0275\u0275element"](0,"div",5),2&_){const _=a["\u0275\u0275nextContext"]();a["\u0275\u0275propertyInterpolate1"]("ngClass","ngx-org-connector-",_.direction,"")}}function l(_,e){if(1&_){const _=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"ngx-chart-node",2),a["\u0275\u0275listener"]("itemClick",(function(e){return a["\u0275\u0275restoreView"](_),a["\u0275\u0275nextContext"]().itemClick.emit(e)})),a["\u0275\u0275elementEnd"]()}if(2&_){const _=a["\u0275\u0275nextContext"]();a["\u0275\u0275propertyInterpolate1"]("ngClass","ngx-org-",_.direction,""),a["\u0275\u0275property"]("node",_.node)("hasParent",_.hasParent)("direction",_.direction)}}function s(_,e){if(1&_){const _=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementContainerStart"](0),a["\u0275\u0275elementStart"](1,"div",5),a["\u0275\u0275elementStart"](2,"div",6),a["\u0275\u0275element"](3,"div",7),a["\u0275\u0275element"](4,"div",8),a["\u0275\u0275element"](5,"div",7),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](6,"ngx-chart-designer",9),a["\u0275\u0275listener"]("itemClick",(function(e){return a["\u0275\u0275restoreView"](_),a["\u0275\u0275nextContext"](2).itemClick.emit(e)})),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementContainerEnd"]()}if(2&_){const _=e.$implicit,n=e.first,t=e.last,r=a["\u0275\u0275nextContext"](2);a["\u0275\u0275advance"](1),a["\u0275\u0275propertyInterpolate1"]("ngClass","ngx-org-org-container-",r.direction,""),a["\u0275\u0275advance"](1),a["\u0275\u0275propertyInterpolate1"]("ngClass","ngx-org-connector-container-",r.direction,""),a["\u0275\u0275advance"](1),a["\u0275\u0275styleProp"]("border-color",n?"transparent":""),a["\u0275\u0275advance"](2),a["\u0275\u0275styleProp"]("border-color",t?"transparent":""),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("node",_)("hasParent",!0)("direction",r.direction)}}function E(_,e){if(1&_&&(a["\u0275\u0275elementStart"](0,"div",3),a["\u0275\u0275template"](1,s,7,9,"ng-container",4),a["\u0275\u0275elementEnd"]()),2&_){const _=a["\u0275\u0275nextContext"]();a["\u0275\u0275propertyInterpolate1"]("ngClass","ngx-org-reports-",_.direction,""),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngForOf",null==_.node?null:_.node.childs)}}function g(_,e){if(1&_){const _=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementContainerStart"](0),a["\u0275\u0275elementStart"](1,"div"),a["\u0275\u0275elementStart"](2,"ngx-chart-designer",2),a["\u0275\u0275listener"]("itemClick",(function(e){return a["\u0275\u0275restoreView"](_),a["\u0275\u0275nextContext"](2).itemClick.emit(e)})),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementContainerEnd"]()}if(2&_){const _=e.$implicit,n=a["\u0275\u0275nextContext"](2);a["\u0275\u0275advance"](1),a["\u0275\u0275classMapInterpolate1"]("ngx-org-self-",n.direction,""),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("node",_)("direction",n.direction)}}function p(_,e){if(1&_&&(a["\u0275\u0275elementStart"](0,"div"),a["\u0275\u0275template"](1,g,3,5,"ng-container",1),a["\u0275\u0275elementEnd"]()),2&_){const _=a["\u0275\u0275nextContext"]();a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngForOf",_.nodes)}}let u=(()=>{class _{constructor(){this.hasParent=!1,this.direction="vertical",this.itemClick=new a.EventEmitter}}return _.\u0275fac=function(e){return new(e||_)},_.\u0275cmp=a["\u0275\u0275defineComponent"]({type:_,selectors:[["ngx-chart-node"]],inputs:{hasParent:"hasParent",direction:"direction",node:"node"},outputs:{itemClick:"itemClick"},decls:9,vars:6,consts:[["class","ngx-org-border",3,"ngClass",4,"ngIf"],[1,"ngx-org-box","ngx-org-border","ngx-org-background",3,"ngClass","click"],["class","ngx-org-image ngx-org-border",3,"ngStyle",4,"ngIf"],[1,"ngx-org-name"],[1,"ngx-org-title"],[1,"ngx-org-border",3,"ngClass"],[1,"ngx-org-image","ngx-org-border",3,"ngStyle"]],template:function(_,e){1&_&&(a["\u0275\u0275template"](0,r,1,1,"div",0),a["\u0275\u0275elementStart"](1,"div",1),a["\u0275\u0275listener"]("click",(function(){return e.itemClick.emit(e.node)})),a["\u0275\u0275template"](2,o,1,3,"div",2),a["\u0275\u0275elementStart"](3,"div"),a["\u0275\u0275elementStart"](4,"div",3),a["\u0275\u0275text"](5),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](6,"div",4),a["\u0275\u0275text"](7),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](8,c,1,1,"div",0)),2&_&&(a["\u0275\u0275property"]("ngIf",e.hasParent),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngClass",null==e.node?null:e.node.cssClass),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",null==e.node?null:e.node.image),a["\u0275\u0275advance"](3),a["\u0275\u0275textInterpolate"](null==e.node?null:e.node.name),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate"](null==e.node?null:e.node.title),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",null==e.node||null==e.node.childs?null:e.node.childs.length))},directives:[t.NgIf,t.NgClass,t.NgStyle],styles:["[_nghost-%COMP%]{display:flex;align-items:center}.ngx-org-box[_ngcontent-%COMP%]{cursor:pointer;display:flex;align-items:center}"]}),_})(),P=(()=>{class _{}return _.\u0275mod=a["\u0275\u0275defineNgModule"]({type:_,bootstrap:function(){return[u]}}),_.\u0275inj=a["\u0275\u0275defineInjector"]({factory:function(e){return new(e||_)},imports:[[t.CommonModule]]}),_})(),d=(()=>{class _{constructor(){this.hasParent=!1,this.direction="vertical",this.itemClick=new a.EventEmitter}get hostClass(){return"vertical"===this.direction?"column":""}}return _.\u0275fac=function(e){return new(e||_)},_.\u0275cmp=a["\u0275\u0275defineComponent"]({type:_,selectors:[["ngx-chart-designer"]],hostVars:2,hostBindings:function(_,e){2&_&&a["\u0275\u0275styleProp"]("flex-direction",e.hostClass)},inputs:{hasParent:"hasParent",direction:"direction",node:"node"},outputs:{itemClick:"itemClick"},decls:2,vars:2,consts:[[3,"ngClass","node","hasParent","direction","itemClick",4,"ngIf"],["class","ngx-org-reports",3,"ngClass",4,"ngIf"],[3,"ngClass","node","hasParent","direction","itemClick"],[1,"ngx-org-reports",3,"ngClass"],[4,"ngFor","ngForOf"],[1,"ngx-org-org-container",3,"ngClass"],[1,"ngx-org-connector-container",3,"ngClass"],[1,"ngx-org-connector","ngx-org-border"],[1,"ngx-org-border"],[3,"node","hasParent","direction","itemClick"]],template:function(_,e){1&_&&(a["\u0275\u0275template"](0,l,1,4,"ngx-chart-node",0),a["\u0275\u0275template"](1,E,2,2,"div",1)),2&_&&(a["\u0275\u0275property"]("ngIf",e.node),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",null==e.node||null==e.node.childs?null:e.node.childs.length))},directives:[t.NgIf,u,t.NgClass,t.NgForOf,_],styles:["[_nghost-%COMP%]{display:flex;align-items:center;flex:1}.ngx-org-vertical[_ngcontent-%COMP%]{flex-direction:column}.ngx-org-org-container[_ngcontent-%COMP%]{display:flex}.ngx-org-org-container-vertical[_ngcontent-%COMP%]{flex-direction:column}.ngx-org-connector[_ngcontent-%COMP%]{flex:1}.ngx-org-connector-container[_ngcontent-%COMP%]{display:flex}.ngx-org-connector-container-horizontal[_ngcontent-%COMP%]{flex-direction:column}.ngx-org-reports[_ngcontent-%COMP%]{display:flex;flex:1}.ngx-org-reports-horizontal[_ngcontent-%COMP%]{flex-direction:column}"]}),_})(),O=(()=>{class _{}return _.\u0275mod=a["\u0275\u0275defineNgModule"]({type:_,bootstrap:function(){return[d]}}),_.\u0275inj=a["\u0275\u0275defineInjector"]({factory:function(e){return new(e||_)},imports:[[t.CommonModule,P]]}),_})(),M=(()=>{class _{constructor(){this.hasParent=!1,this.direction="vertical",this.itemClick=new a.EventEmitter}}return _.\u0275fac=function(e){return new(e||_)},_.\u0275cmp=a["\u0275\u0275defineComponent"]({type:_,selectors:[["ngx-org-chart"]],inputs:{hasParent:"hasParent",direction:"direction",nodes:"nodes"},outputs:{itemClick:"itemClick"},decls:1,vars:1,consts:[[4,"ngIf"],[4,"ngFor","ngForOf"],[3,"node","direction","itemClick"]],template:function(_,e){1&_&&a["\u0275\u0275template"](0,p,2,1,"div",0),2&_&&a["\u0275\u0275property"]("ngIf",null==e.nodes?null:e.nodes.length)},directives:[t.NgIf,t.NgForOf,d],styles:['body[_ngcontent-%COMP%], html[_ngcontent-%COMP%]{display:flex;flex:1}.ngx-org-name[_ngcontent-%COMP%]{font-family:"Patua One",cursive}.ngx-org-title[_ngcontent-%COMP%]{font-family:Oswald,sans-serif}.ngx-org-border[_ngcontent-%COMP%]{border-color:#9e9e9e}.ngx-org-box[_ngcontent-%COMP%]{color:#000;width:10em}.ngx-org-self-vertical[_ngcontent-%COMP%]{margin-bottom:2%}']}),_})(),D=(()=>{class _{}return _.\u0275mod=a["\u0275\u0275defineNgModule"]({type:_,bootstrap:function(){return[M]}}),_.\u0275inj=a["\u0275\u0275defineInjector"]({factory:function(e){return new(e||_)},imports:[[t.CommonModule,O]]}),_})()},yNiW:function(module,__webpack_exports__,__webpack_require__){"use strict";__webpack_require__.d(__webpack_exports__,"a",(function(){return LandingPageComponent}));var tslib__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("mrSG"),rxjs__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("XNiG"),rxjs_operators__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__("1G5W"),underscore__WEBPACK_IMPORTED_MODULE_3__=__webpack_require__("xG9w"),_angular_cdk_portal__WEBPACK_IMPORTED_MODULE_4__=__webpack_require__("+rOU"),subsink__WEBPACK_IMPORTED_MODULE_5__=__webpack_require__("33Jv"),_angular_core__WEBPACK_IMPORTED_MODULE_6__=__webpack_require__("fXoL"),src_app_modules_applicant_tracking_system_shared_components_ats_custom_toast_toaster_service__WEBPACK_IMPORTED_MODULE_7__=__webpack_require__("XNFG"),_services_master_master_service__WEBPACK_IMPORTED_MODULE_8__=__webpack_require__("TNcG"),_services_utilities_utilities_service__WEBPACK_IMPORTED_MODULE_9__=__webpack_require__("GXg6"),_services_inbox_inbox_service__WEBPACK_IMPORTED_MODULE_10__=__webpack_require__("d6+l"),src_app_services_acl_roles_service__WEBPACK_IMPORTED_MODULE_11__=__webpack_require__("flaP"),src_app_modules_project_management_shared_lazy_loaded_components_filters_common_dialog_services_filter_service_service__WEBPACK_IMPORTED_MODULE_12__=__webpack_require__("1YnU"),src_app_services_login_login_service__WEBPACK_IMPORTED_MODULE_13__=__webpack_require__("XXEo"),_angular_router__WEBPACK_IMPORTED_MODULE_14__=__webpack_require__("tyNb"),_angular_cdk_overlay__WEBPACK_IMPORTED_MODULE_15__=__webpack_require__("rDax"),_angular_material_dialog__WEBPACK_IMPORTED_MODULE_16__=__webpack_require__("0IaG"),src_app_modules_employee_directory_services_employee_directory_service__WEBPACK_IMPORTED_MODULE_17__=__webpack_require__("jAlA"),_angular_common__WEBPACK_IMPORTED_MODULE_18__=__webpack_require__("ofXK"),_angular_forms__WEBPACK_IMPORTED_MODULE_19__=__webpack_require__("3Pt+"),_project_management_shared_lazy_loaded_components_filters_common_dialog_components_filter_display_filter_display_component__WEBPACK_IMPORTED_MODULE_20__=__webpack_require__("Atg/"),ngx_infinite_scroll__WEBPACK_IMPORTED_MODULE_21__=__webpack_require__("dlKe"),_angular_material_checkbox__WEBPACK_IMPORTED_MODULE_22__=__webpack_require__("bSwM"),_angular_material_tooltip__WEBPACK_IMPORTED_MODULE_23__=__webpack_require__("Qu3c"),_app_shared_app_shared_components_user_image_user_image_component__WEBPACK_IMPORTED_MODULE_24__=__webpack_require__("me71"),_angular_material_icon__WEBPACK_IMPORTED_MODULE_25__=__webpack_require__("NFeN"),_angular_material_menu__WEBPACK_IMPORTED_MODULE_26__=__webpack_require__("STbY"),_project_management_shared_lazy_loaded_components_people_icon_display_people_icon_display_component__WEBPACK_IMPORTED_MODULE_27__=__webpack_require__("CU7G"),_components_inbox_column_customization_inbox_column_customization_component__WEBPACK_IMPORTED_MODULE_28__=__webpack_require__("VA38"),_product_report_pipes_svgSecurityBypass_svg_security_bypass_pipe__WEBPACK_IMPORTED_MODULE_29__=__webpack_require__("yuNy"),_product_report_pipes_calculatePositionOfPinnedColumns_calculate_position_of_pinned_columns_pipe__WEBPACK_IMPORTED_MODULE_30__=__webpack_require__("OoYh"),_timesheet_v2_features_ts_v2_approval_pipes_hoursWorkedSplit_hours_worked_split_pipe__WEBPACK_IMPORTED_MODULE_31__=__webpack_require__("mns1"),_project_management_shared_lazy_loaded_pipes_date_format_pipe__WEBPACK_IMPORTED_MODULE_32__=__webpack_require__("IEgo"),_product_report_pipes_currency_currency_pipe__WEBPACK_IMPORTED_MODULE_33__=__webpack_require__("1mzr");const _c0=["triggerColumnCustomizationTemplateRef"];function LandingPageComponent_ng_container_9_Template(_,e){if(1&_&&(_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerStart"](0),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](1,"div",8),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275element"](2,"img",9),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](3,"div",10),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](4,"div",11),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275text"](5,"Loading..."),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerEnd"]()),2&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"]();_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](2),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("src",_.loadingGif||"",_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275sanitizeUrl"])}}function LandingPageComponent_ng_container_10_ng_container_1_Template(_,e){if(1&_&&(_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerStart"](0),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](1,"div",8),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275element"](2,"img",12),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](3,"div",13),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275text"](4),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerEnd"]()),2&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](2);_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](2),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("src",_.homePageUiConfig["INBOX-EMPTY-STATE-IMAGE"]||"",_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275sanitizeUrl"]),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](2),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275textInterpolate1"](" ",_.homePageUiConfig["INBOX-EMPTY-STATE-TEXT"]||""," ")}}const _c1=function(_){return{"selected-application":_}},_c2=function(_,e){return{"margin-left":_,"margin-right":e}};function LandingPageComponent_ng_container_10_ng_container_2_div_4_Template(_,e){if(1&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275getCurrentView"]();_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](0,"div",28),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275listener"]("click",(function(){_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275restoreView"](_);const n=e.index;return _angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](3).switchApplication(n)})),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275text"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"]()}if(2&_){const _=e.$implicit,n=e.index,t=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](3);_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngClass",_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pureFunction1"](4,_c1,null==_?null:_.isSelected))("ngStyle",_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pureFunction2"](6,_c2,0==n?"0px":"",n==t.applications.length-1?"0px":"")),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275textInterpolate2"](" ",_.application_name," ",null!=_&&_.count?"("+(null==_?null:_.count)+")":""," ")}}const _c3=function(_,e){return{"selected-button":_,"unselected-button":e}};function LandingPageComponent_ng_container_10_ng_container_2_div_8_Template(_,e){if(1&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275getCurrentView"]();_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](0,"div",19),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275listener"]("click",(function(){return _angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275restoreView"](_),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](3).switchToPreviousRequest()})),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275text"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"]()}if(2&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](3);_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngClass",_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pureFunction2"](2,_c3,!_.isPendingRequestSelected,_.isPendingRequestSelected)),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275textInterpolate1"](" ",_.homePageUiConfig["REQUEST-TEXT-002"]," ")}}function LandingPageComponent_ng_container_10_ng_container_2_div_10_Template(_,e){if(1&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275getCurrentView"]();_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](0,"div",29),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](1,"div",30),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275text"](2),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](3,"div",31),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275listener"]("click",(function(){return _angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275restoreView"](_),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](3).onBulkApprove()})),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275text"](4,"Approve"),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"]()}if(2&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](3);_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](2),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275textInterpolate2"](" ",_.countSelected," ",_.countSelected&&_.countSelected>1?"Requests":"Request"," Selected ")}}function LandingPageComponent_ng_container_10_ng_container_2_ng_container_12_div_1_div_1_Template(_,e){1&_&&(_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](0,"div"),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275namespaceSVG"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](1,"svg",36),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](2,"g",37),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275element"](3,"path",38),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](4,"defs"),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](5,"clipPath",39),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275element"](6,"rect",40),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"]())}function LandingPageComponent_ng_container_10_ng_container_2_ng_container_12_div_1_div_4_Template(_,e){if(1&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275getCurrentView"]();_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](0,"div",41),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275listener"]("click",(function(){return _angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275restoreView"](_),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](5).onClearSearch()})),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275namespaceSVG"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](1,"svg",42),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275element"](2,"path",43),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"]()}}function LandingPageComponent_ng_container_10_ng_container_2_ng_container_12_div_1_Template(_,e){if(1&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275getCurrentView"]();_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](0,"div",33),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](1,LandingPageComponent_ng_container_10_ng_container_2_ng_container_12_div_1_div_1_Template,7,0,"div",5),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](2,"div",34),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](3,"input",35),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275listener"]("ngModelChange",(function(e){return _angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275restoreView"](_),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](4).searchParams=e}))("keydown.enter",(function(){return _angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275restoreView"](_),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](4).onEnterSearch()})),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](4,LandingPageComponent_ng_container_10_ng_container_2_ng_container_12_div_1_div_4_Template,3,0,"div",26),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"]()}if(2&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](4);_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf",""==_.searchParams),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](2),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngModel",_.searchParams),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf",""!=_.searchParams)}}function LandingPageComponent_ng_container_10_ng_container_2_ng_container_12_div_2_Template(_,e){if(1&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275getCurrentView"]();_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](0,"div",41),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275listener"]("click",(function(){return _angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275restoreView"](_),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](4).onClickSearchIcon()})),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275namespaceSVG"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](1,"svg",36),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](2,"g",44),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275element"](3,"path",45),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](4,"defs"),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](5,"clipPath",46),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275element"](6,"rect",40),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"]()}}function LandingPageComponent_ng_container_10_ng_container_2_ng_container_12_Template(_,e){if(1&_&&(_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerStart"](0),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](1,LandingPageComponent_ng_container_10_ng_container_2_ng_container_12_div_1_Template,5,3,"div",32),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](2,LandingPageComponent_ng_container_10_ng_container_2_ng_container_12_div_2_Template,7,0,"div",26),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerEnd"]()),2&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](3);_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf",_.isSearchBarVisible),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf",!_.isSearchBarVisible)}}function LandingPageComponent_ng_container_10_ng_container_2_div_13_Template(_,e){1&_&&(_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](0,"div",47),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275namespaceSVG"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](1,"svg",48),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](2,"g",49),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275element"](3,"circle",50),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275element"](4,"circle",51),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275element"](5,"circle",52),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275element"](6,"circle",53),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](7,"defs"),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](8,"clipPath",54),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275element"](9,"rect",55),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"]())}function LandingPageComponent_ng_container_10_ng_container_2_div_14_Template(_,e){if(1&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275getCurrentView"]();_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](0,"div",56,57),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275listener"]("click",(function(){_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275restoreView"](_);const e=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275reference"](2);return _angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](3).openCustomizationOverlay(e)})),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275namespaceSVG"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](3,"svg",48),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](4,"mask",58),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275element"](5,"rect",59),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](6,"g",60),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275element"](7,"path",61),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"]()}}function LandingPageComponent_ng_container_10_ng_container_2_div_15_Template(_,e){if(1&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275getCurrentView"]();_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](0,"div",41),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275listener"]("click",(function(){return _angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275restoreView"](_),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](3).openFilterDialog()})),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275namespaceSVG"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](1,"svg",36),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275element"](2,"path",62),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"]()}}function LandingPageComponent_ng_container_10_ng_container_2_div_16_Template(_,e){if(1&_&&(_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](0,"div",63),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275element"](1,"app-filter-display",64),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"]()),2&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](3);_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("applicationId",_.currentSelectedApplicationId)("internalApplicationId",_.currentFilterSubApplicationId)}}function LandingPageComponent_ng_container_10_ng_container_2_ng_container_17_Template(_,e){if(1&_&&(_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerStart"](0),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](1,"div",65),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275element"](2,"img",66),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](3,"div",10),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](4,"div",11),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275text"](5,"Loading..."),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerEnd"]()),2&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](3);_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](2),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("src",_.loadingGif||"",_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275sanitizeUrl"])}}function LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_1_Template(_,e){if(1&_&&(_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerStart"](0),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](1,"div",65),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275element"](2,"img",12),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](3,"div",13),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275text"](4),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](5,"div",67),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275text"](6),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerEnd"]()),2&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](4);_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](2),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("src",_.homePageUiConfig["INBOX-APP-EMPTY-STATE-IMAGE"]||"",_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275sanitizeUrl"]),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](2),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275textInterpolate1"](" ",_.homePageUiConfig["INBOX-APP-EMPTY-STATE-TEXT-001"]||""," "),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](2),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275textInterpolate1"](" ",_.homePageUiConfig["INBOX-APP-EMPTY-STATE-TEXT-002"]||""," ")}}const _c4=function(_){return{"pointer-events":_}};function LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_4_div_1_div_4_Template(_,e){if(1&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275getCurrentView"]();_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](0,"div",79),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](1,"mat-checkbox",80),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275listener"]("ngModelChange",(function(e){return _angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275restoreView"](_),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](7).pendingCheckAll=e}))("ngModelChange",(function(){return _angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275restoreView"](_),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](7).onChangePendingCheckAll()})),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"]()}if(2&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](7);_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngStyle",_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pureFunction1"](2,_c4,_.isLoading||_.isListViewLoading||_.isRejectApiInProgress||_.isApproveApiInProgress||_.isRejectApprovedDataApiInProgress?"none":"")),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngModel",_.pendingCheckAll)}}function LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_4_div_1_span_5_Template(_,e){if(1&_&&(_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](0,"span",81),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275text"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"]()),2&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](2).$implicit;_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("matTooltip",_.label),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275textInterpolate1"](" ",_.label," ")}}function LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_4_div_1_div_7_Template(_,e){if(1&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275getCurrentView"]();_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](0,"div",82),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275listener"]("click",(function(){_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275restoreView"](_);const e=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](2).index;return _angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](5).onClickSort(1,e)})),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275namespaceSVG"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](1,"svg",83),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](2,"g",84),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275element"](3,"path",85),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](4,"defs"),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](5,"clipPath",86),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275element"](6,"rect",87),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"]()}}function LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_4_div_1_div_8_Template(_,e){if(1&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275getCurrentView"]();_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](0,"div",82),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275listener"]("click",(function(){_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275restoreView"](_);const e=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](2).index;return _angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](5).onClickSort(2,e)})),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275namespaceSVG"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](1,"svg",88),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275element"](2,"polygon",89),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"]()}}function LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_4_div_1_div_9_Template(_,e){if(1&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275getCurrentView"]();_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](0,"div",82),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275listener"]("click",(function(){_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275restoreView"](_);const e=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](2).index;return _angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](5).onClickSort(0,e)})),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275namespaceSVG"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](1,"svg",88),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275element"](2,"polygon",90),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"]()}}const _c5=function(_,e,n,t,a,r){return{"min-width":_,"max-width":e,left:n,right:t,"border-left":a,"border-right":r}};function LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_4_div_1_Template(_,e){if(1&_&&(_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](0,"div",73),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pipe"](1,"calculatePositionOfPinnedColumns"),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pipe"](2,"calculatePositionOfPinnedColumns"),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](3,"div",74),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](4,LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_4_div_1_div_4_Template,2,4,"div",75),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](5,LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_4_div_1_span_5_Template,2,2,"span",76),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](6,"div",77),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](7,LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_4_div_1_div_7_Template,7,0,"div",78),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](8,LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_4_div_1_div_8_Template,3,0,"div",78),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](9,LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_4_div_1_div_9_Template,3,0,"div",78),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"]()),2&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](),e=_.$implicit,n=_.index,t=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](5);_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275classMap"](null!=e&&e.is_pinned_left?"header-row-left-pin":null!=e&&e.is_pinned_right?"header-row-right-pin":""),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngStyle",_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pureFunction6"](16,_c5,e.width,e.width,null!=e&&e.is_pinned_left?_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pipeBind3"](1,8,t.currentColumnConfig,n,"left"):"",null!=e&&e.is_pinned_right?_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pipeBind3"](2,12,t.currentColumnConfig,n,"right"):"",null==e||!e.is_pinned_right||null!=t.currentColumnConfig[n-1]&&t.currentColumnConfig[n-1].is_pinned_right?"":"1px solid #E8E9EE",null==e||!e.is_pinned_left||null!=t.currentColumnConfig[n+1]&&t.currentColumnConfig[n+1].is_pinned_left?"":"1px solid #E8E9EE")),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](4),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf",t.isPendingRequestSelected&&0==n&&t.applications[t.currentSelectedApplicationIndex].header_customization.is_bulk_approve_allowed),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf",!(null!=e&&e.is_search_active&&null!=e&&e.is_search_icon_clicked)),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](2),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf",e.is_sort_active&&0==e.sort_order),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf",e.is_sort_active&&1==e.sort_order),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf",e.is_sort_active&&2==e.sort_order)}}function LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_4_Template(_,e){if(1&_&&(_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerStart"](0),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](1,LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_4_div_1_Template,10,23,"div",72),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerEnd"]()),2&_){const _=e.$implicit;_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf",_.is_visible)}}function LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_div_3_Template(_,e){if(1&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275getCurrentView"]();_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](0,"div",79),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](1,"mat-checkbox",95),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275listener"]("ngModelChange",(function(e){return _angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275restoreView"](_),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](3).$implicit.isChecked=e}))("ngModelChange",(function(){return _angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275restoreView"](_),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](8).onIndividualCheckboxSelected()}))("click",(function(e){_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275restoreView"](_);const n=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](8);return n.stopParentPropagation(e),n.onIndividualCheckboxSelected()})),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"]()}if(2&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](3).$implicit,e=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](5);_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngStyle",_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pureFunction1"](2,_c4,e.isLoading||e.isListViewLoading||e.isRejectApiInProgress||e.isApproveApiInProgress||e.isRejectApprovedDataApiInProgress?"none":"")),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngModel",_.isChecked)}}function LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_4_Template(_,e){if(1&_&&(_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerStart"](0),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](1,"span",96),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275text"](2),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerEnd"]()),2&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](2).$implicit,e=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"]().$implicit;_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("matTooltip",e[_.column_key[0]]||"-"),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275textInterpolate1"](" ",e[_.column_key[0]]||"-"," ")}}function LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_5_Template(_,e){if(1&_&&(_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerStart"](0),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](1,"span",96),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pipe"](2,"hoursWorkedSplit"),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275text"](3),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pipe"](4,"hoursWorkedSplit"),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerEnd"]()),2&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](2).$implicit,e=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"]().$implicit;_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("matTooltip",_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pipeBind1"](2,2,e[_.column_key[0]])),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](2),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275textInterpolate1"](" ",_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pipeBind1"](4,4,e[_.column_key[0]])," ")}}const _c6=function(_){return{color:_}};function LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_6_Template(_,e){if(1&_&&(_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerStart"](0),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](1,"span",97),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275text"](2),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerEnd"]()),2&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](2).$implicit,e=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"]().$implicit;_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("matTooltip",e[_.column_key[0]]||"-")("ngStyle",_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pureFunction1"](3,_c6,e[_.column_key[1]])),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275textInterpolate1"](" ",e[_.column_key[0]]||"-"," ")}}const _c7=function(){return[null,void 0,!1]};function LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_7_Template(_,e){if(1&_&&(_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerStart"](0),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](1,"span",96),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275text"](2),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerEnd"]()),2&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](2).$implicit,e=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"]().$implicit;_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("matTooltip",_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pureFunction0"](2,_c7).includes(e[_.column_key[0]])?"-":e[_.column_key[0]]),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275textInterpolate1"](" ",_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pureFunction0"](3,_c7).includes(e[_.column_key[0]])?"-":e[_.column_key[0]]," ")}}function LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_8_Template(_,e){if(1&_&&(_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerStart"](0),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](1,"span",96),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pipe"](2,"dateFormat"),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275text"](3),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pipe"](4,"dateFormat"),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerEnd"]()),2&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](2).$implicit,e=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"]().$implicit;_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("matTooltip",_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pipeBind2"](2,2,e[_.column_key[0]],_.column_key[1]||"DD MMM YYYY")),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](2),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275textInterpolate1"](" ",_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pipeBind2"](4,5,e[_.column_key[0]],_.column_key[1]||"DD MMM YYYY")," ")}}const _c8=function(_,e){return{"background-color":_,color:e}};function LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_9_Template(_,e){if(1&_&&(_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerStart"](0),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](1,"div",98),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275text"](2),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerEnd"]()),2&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](2).$implicit,e=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"]().$implicit;_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngStyle",_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pureFunction2"](3,_c8,e[_.column_key[1]],e[_.column_key[2]]))("matTooltip",e[_.column_key[0]]||"-"),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275textInterpolate1"](" ",e[_.column_key[0]]||"-"," ")}}function LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_10_span_1_Template(_,e){if(1&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275getCurrentView"]();_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](0,"span",101),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275listener"]("click",(function(){_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275restoreView"](_);const e=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](3).$implicit,n=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"]().$implicit;return _angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](5).openHyperLink(n[e.column_key[1]])})),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275text"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"]()}if(2&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](3).$implicit,e=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"]().$implicit;_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("matTooltip",e[_.column_key[0]]),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275textInterpolate1"](" ",e[_.column_key[0]]," ")}}function LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_10_span_2_Template(_,e){1&_&&(_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](0,"span",102),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275text"](1," - "),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"]())}function LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_10_Template(_,e){if(1&_&&(_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerStart"](0),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](1,LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_10_span_1_Template,2,2,"span",99),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](2,LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_10_span_2_Template,2,0,"span",100),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerEnd"]()),2&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](2).$implicit,e=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"]().$implicit;_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf",e[_.column_key[0]]&&e[_.column_key[1]]),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf",!e[_.column_key[0]])}}function LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_11_Template(_,e){if(1&_&&(_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerStart"](0),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](1,"span",96),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pipe"](2,"dateFormat"),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pipe"](3,"dateFormat"),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275text"](4),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pipe"](5,"dateFormat"),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pipe"](6,"dateFormat"),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerEnd"]()),2&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](2).$implicit,e=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"]().$implicit;_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("matTooltip",_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pipeBind2"](2,3,_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pipeBind2"](3,6,e[_.column_key[0]],_.column_key[2]||"DD MMM YYYY - "+e[_.column_key[1]]),_.column_key[2]||"DD MMM YYYY")),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](3),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275textInterpolate2"](" ",_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pipeBind2"](5,9,e[_.column_key[0]],_.column_key[2]||"DD MMM YYYY")," - ",_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pipeBind2"](6,12,e[_.column_key[1]],_.column_key[2]||"DD MMM YYYY")," ")}}function LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_12_Template(_,e){if(1&_&&(_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerStart"](0),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275element"](1,"app-user-image",103),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](2,"span",96),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275text"](3),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerEnd"]()),2&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](2).$implicit,e=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"]().$implicit;_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("oid",e[_.column_key[1]]),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("matTooltip",e[_.column_key[0]]||"-"),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275textInterpolate1"](" ",e[_.column_key[0]]||"-"," ")}}function LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_13_Template(_,e){if(1&_&&(_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerStart"](0),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275element"](1,"app-user-image",103),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](2,"div",104),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](3,"span",105),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275text"](4),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](5,"span",106),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275text"](6),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerEnd"]()),2&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](2).$implicit,e=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"]().$implicit;_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("oid",e[_.column_key[3]]),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](2),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("matTooltip",e[_.column_key[0]]||"-"),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275textInterpolate1"](" ",e[_.column_key[0]]||"-"," "),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("matTooltip",_.column_key[2]+" "+(e[_.column_key[1]]||"-")),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275textInterpolate2"](" ",_.column_key[2]," ",e[_.column_key[1]]||"-"," ")}}function LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_14_ng_container_2_div_1_div_1_Template(_,e){if(1&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275getCurrentView"]();_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](0,"div",110),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275listener"]("click",(function(){_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275restoreView"](_);const e=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](2),n=e.$implicit,t=e.index,a=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](3).index,r=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](),i=r.$implicit,o=r.index;return _angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](5).onClickActionsIcon(i,n.action_type,n.click_action,null==n?null:n.file_name,o,a,t)})),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pipe"](1,"svgSecurityBypass"),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"]()}if(2&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](2).$implicit;_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("innerHTML",_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pipeBind1"](1,2,_.icon),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275sanitizeHtml"])("matTooltip",_.label)}}function LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_14_ng_container_2_div_1_Template(_,e){if(1&_&&(_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](0,"div"),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](1,LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_14_ng_container_2_div_1_div_1_Template,2,4,"div",109),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"]()),2&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"]().$implicit,e=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](4).$implicit;_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf","svg"==_.type&&"hyperlink"==_.action_type&&e[_.click_action]||"svg"==_.type&&"hyperlink"!=_.action_type)}}function LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_14_ng_container_2_div_2_Template(_,e){1&_&&(_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](0,"div",111),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](1,"div",112),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](2,"span",113),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275text"](3,"Loading..."),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"]())}const _c9=function(_,e,n,t){return{"font-size":_,width:e,height:n,color:t}};function LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_14_ng_container_2_div_3_div_1_Template(_,e){if(1&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275getCurrentView"]();_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](0,"div"),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](1,"mat-icon",114),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275listener"]("click",(function(){_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275restoreView"](_);const e=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](2),n=e.$implicit,t=e.index,a=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](3).index,r=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](),i=r.$implicit,o=r.index;return _angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](5).onClickActionsIcon(i,n.action_type,n.click_action,null==n?null:n.file_name,o,a,t)})),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275text"](2),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"]()}if(2&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](2).$implicit;_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngStyle",_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pureFunction4"](3,_c9,_.font_size,_.font_size,_.font_size,_.color))("matTooltip",_.label),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275textInterpolate1"](" ",_.icon," ")}}function LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_14_ng_container_2_div_3_Template(_,e){if(1&_&&(_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](0,"div"),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](1,LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_14_ng_container_2_div_3_div_1_Template,3,8,"div",5),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"]()),2&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"]().$implicit,e=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](4).$implicit;_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf","mat-icon"==_.type&&"hyperlink"==_.action_type&&e[_.click_action]||"mat-icon"==_.type&&"hyperlink"!=_.action_type)}}function LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_14_ng_container_2_Template(_,e){if(1&_&&(_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerStart"](0),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](1,LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_14_ng_container_2_div_1_Template,2,1,"div",5),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](2,LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_14_ng_container_2_div_2_Template,4,0,"div",108),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](3,LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_14_ng_container_2_div_3_Template,2,1,"div",5),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerEnd"]()),2&_){const _=e.$implicit,n=e.index,t=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](3).$implicit,a=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"]().$implicit;_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf",!(null!=_&&_.showLoader)||0==(null==_?null:_.showLoader)||1==(null==_?null:_.showLoader)&&!a[(null==t?null:t.id)+"_"+n+"_loader"]),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf",1==(null==_?null:_.showLoader)&&a[(null==t?null:t.id)+"_"+n+"_loader"]),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf",!(null!=_&&_.showLoader)||0==(null==_?null:_.showLoader)||1==(null==_?null:_.showLoader)&&!a[(null==t?null:t.id)+"_"+n+"_loader"])}}function LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_14_Template(_,e){if(1&_&&(_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerStart"](0),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](1,"div",107),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](2,LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_14_ng_container_2_Template,4,3,"ng-container",71),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerEnd"]()),2&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](2).$implicit;_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](2),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngForOf",_.click_actions)}}function LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_15_Template(_,e){if(1&_&&(_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerStart"](0),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](1,"span",96),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275text"](2),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pipe"](3,"currencyFormat"),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerEnd"]()),2&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](2).$implicit,e=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"]().$implicit;_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("matTooltip",e[_.column_key[1]]&&e[_.column_key[0]]?e[_.column_key[1]]+" "+e[_.column_key[0]]:"-"),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275textInterpolate2"](" ",e[_.column_key[1]]," ",_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pipeBind3"](3,3,e[_.column_key[0]],e[_.column_key[1]],_.column_key[2])," ")}}const _c10=function(_){return{"disable-btn":_}};function LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_16_Template(_,e){if(1&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275getCurrentView"]();_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerStart"](0),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](1,"div",115),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](2,"div",116),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275listener"]("click",(function(){_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275restoreView"](_);const e=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](3).index;return _angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](5).onSingleDataReject(e)})),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275text"](3," Reject "),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](4,"div",117),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275listener"]("click",(function(){_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275restoreView"](_);const e=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](3).index;return _angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](5).onSingleDataApprove(e)})),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275text"](5," Approve "),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerEnd"]()}if(2&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](8);_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](2),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngClass",_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pureFunction1"](2,_c10,_.pendingCheckAll||_.countSelected||_.isRejectApiInProgress||_.isApproveApiInProgress)),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](2),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngClass",_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pureFunction1"](4,_c10,_.pendingCheckAll||_.countSelected||_.isRejectApiInProgress||_.isApproveApiInProgress))}}const _c11=function(_,e){return{"pointer-events":_,opacity:e}};function LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_17_mat_icon_1_Template(_,e){if(1&_&&(_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](0,"mat-icon",122),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275text"](1," more_vert "),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"]()),2&_){_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"]();const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275reference"](3),e=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](8);_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("matMenuTriggerFor",_)("ngStyle",_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pureFunction2"](2,_c11,e.isLoading||e.isListViewLoading||e.isRejectApiInProgress||e.isApproveApiInProgress||e.isRejectApprovedDataApiInProgress?"none":"",e.isLoading||e.isListViewLoading||e.isRejectApiInProgress||e.isApproveApiInProgress||e.isRejectApprovedDataApiInProgress?.3:null))}}const _c12=function(){return{"border-bottom":"none"}};function LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_17_Template(_,e){if(1&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275getCurrentView"]();_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerStart"](0),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](1,LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_17_mat_icon_1_Template,2,5,"mat-icon",118),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](2,"mat-menu",119,120),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](4,"button",121),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275listener"]("click",(function(){_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275restoreView"](_);const e=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](3).index;return _angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](5).onRejectApprovedItem(e)})),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275text"](5," Reject "),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerEnd"]()}if(2&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](3).$implicit;_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf","Approved"===(null==_?null:_.status)),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](3),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngStyle",_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pureFunction0"](2,_c12))}}function LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_18_span_1_Template(_,e){if(1&_&&(_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](0,"span"),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275element"](1,"app-people-icon-display",123),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"]()),2&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](3).$implicit,e=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"]().$implicit;_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("peopleList",e[_.column_key[0]])("count",null==e[_.column_key[0]]?null:e[_.column_key[0]].length)}}function LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_18_Template(_,e){if(1&_&&(_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerStart"](0),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](1,LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_18_span_1_Template,2,2,"span",5),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerEnd"]()),2&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](2).$implicit,e=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"]().$implicit;_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf",e[_.column_key[0]])}}const _c13=function(_,e,n,t,a,r,i,o,c){return{"min-width":_,"max-width":e,left:n,right:t,"border-left":a,"border-right":r,"justify-content":i,"padding-right":o,cursor:c}};function LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_Template(_,e){if(1&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275getCurrentView"]();_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](0,"div",94),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275listener"]("click",(function(){_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275restoreView"](_);const e=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"]().$implicit,n=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"]().$implicit;return _angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](5).columnClickAction(e.click_actions,n,n[e.click_actions.column_key])})),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pipe"](1,"calculatePositionOfPinnedColumns"),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pipe"](2,"calculatePositionOfPinnedColumns"),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](3,LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_div_3_Template,2,4,"div",75),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](4,LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_4_Template,3,2,"ng-container",5),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](5,LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_5_Template,5,6,"ng-container",5),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](6,LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_6_Template,3,5,"ng-container",5),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](7,LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_7_Template,3,4,"ng-container",5),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](8,LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_8_Template,5,8,"ng-container",5),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](9,LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_9_Template,3,6,"ng-container",5),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](10,LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_10_Template,3,2,"ng-container",5),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](11,LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_11_Template,7,15,"ng-container",5),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](12,LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_12_Template,4,3,"ng-container",5),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](13,LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_13_Template,7,6,"ng-container",5),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](14,LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_14_Template,3,1,"ng-container",5),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](15,LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_15_Template,4,7,"ng-container",5),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](16,LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_16_Template,6,6,"ng-container",5),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](17,LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_17_Template,6,3,"ng-container",5),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](18,LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_ng_container_18_Template,2,1,"ng-container",5),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"]()}if(2&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](),e=_.$implicit,n=_.index,t=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](6);_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275classMap"](null!=e&&e.is_pinned_left?"content-row-left-pin":null!=e&&e.is_pinned_right?"content-row-right-pin":""),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngStyle",_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pureFunctionV"](27,_c13,[e.width,e.width,null!=e&&e.is_pinned_left?_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pipeBind3"](1,19,t.currentColumnConfig,n,"left"):"",null!=e&&e.is_pinned_right?_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pipeBind3"](2,23,t.currentColumnConfig,n,"right"):"",null==e||!e.is_pinned_right||null!=t.currentColumnConfig[n-1]&&t.currentColumnConfig[n-1].is_pinned_right?"":"1px solid #E8E9EE",null==e||!e.is_pinned_left||null!=t.currentColumnConfig[n+1]&&t.currentColumnConfig[n+1].is_pinned_left?"":"1px solid #E8E9EE","currency"==(null==e?null:e.column_type)?"end":"start","currency"==(null==e?null:e.column_type)?"10px":"",e.click_actions?"pointer":""])),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](3),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf",t.isPendingRequestSelected&&0==n&&t.applications[t.currentSelectedApplicationIndex].header_customization.is_bulk_approve_allowed),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf","text"==e.column_type),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf","time-format"==e.column_type),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf","text-color"==e.column_type),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf","number"==e.column_type),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf","date"==e.column_type),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf","chip"==e.column_type),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf","hyperlink"==e.column_type),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf","date-range"==e.column_type),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf","employee-display"==e.column_type),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf","employee-display-aid"==e.column_type),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf","actions"==e.column_type),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf","currency"==e.column_type),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf","approve-reject-button"==e.column_type),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf","reject-approved-item-button"==e.column_type),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf","approvers"==e.column_type)}}function LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_Template(_,e){if(1&_&&(_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerStart"](0),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](1,LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_div_1_Template,19,37,"div",93),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerEnd"]()),2&_){const _=e.$implicit;_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf",_.is_visible)}}function LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_Template(_,e){if(1&_&&(_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerStart"](0),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](1,"div",91),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](2,"div",92),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](3,LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_ng_container_3_Template,2,1,"ng-container",71),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerEnd"]()),2&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](5);_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](3),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngForOf",_.currentColumnConfig)}}function LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_Template(_,e){if(1&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275getCurrentView"]();_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerStart"](0),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](1,"div",68),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275listener"]("scrolled",(function(){return _angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275restoreView"](_),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](4).onDataScroll()})),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](2,"div",69),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](3,"div",70),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](4,LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_4_Template,2,1,"ng-container",71),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](5,LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_ng_container_5_Template,4,1,"ng-container",71),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerEnd"]()}if(2&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](4);_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("infiniteScrollDistance",3)("infiniteScrollThrottle",100)("scrollWindow",!1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](3),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngForOf",_.currentColumnConfig),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngForOf",_.currentData)}}function LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_Template(_,e){if(1&_&&(_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerStart"](0),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](1,LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_1_Template,7,3,"ng-container",5),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](2,LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_ng_container_2_Template,6,5,"ng-container",5),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerEnd"]()),2&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](3);_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf",0==_.currentData.length),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf",_.currentData.length>0)}}const _c14=function(_,e){return{"pointer-events":_,"justify-content":e}};function LandingPageComponent_ng_container_10_ng_container_2_Template(_,e){if(1&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275getCurrentView"]();_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerStart"](0),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](1,"div",14),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](2,"div",15),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](3,"div",16),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](4,LandingPageComponent_ng_container_10_ng_container_2_div_4_Template,2,9,"div",17),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](5,"div",18),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](6,"div",19),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275listener"]("click",(function(){return _angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275restoreView"](_),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](2).switchToPendingRequest()})),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275text"](7),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](8,LandingPageComponent_ng_container_10_ng_container_2_div_8_Template,2,5,"div",20),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](9,"div",21),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](10,LandingPageComponent_ng_container_10_ng_container_2_div_10_Template,5,2,"div",22),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](11,"div",23),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](12,LandingPageComponent_ng_container_10_ng_container_2_ng_container_12_Template,3,2,"ng-container",5),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](13,LandingPageComponent_ng_container_10_ng_container_2_div_13_Template,10,0,"div",24),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](14,LandingPageComponent_ng_container_10_ng_container_2_div_14_Template,8,0,"div",25),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](15,LandingPageComponent_ng_container_10_ng_container_2_div_15_Template,3,0,"div",26),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](16,LandingPageComponent_ng_container_10_ng_container_2_div_16_Template,2,2,"div",27),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](17,LandingPageComponent_ng_container_10_ng_container_2_ng_container_17_Template,6,1,"ng-container",5),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](18,LandingPageComponent_ng_container_10_ng_container_2_ng_container_18_Template,3,2,"ng-container",5),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerEnd"]()}if(2&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"](2);_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](2),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngStyle",_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pureFunction1"](15,_c4,_.isLoading||_.isListViewLoading||_.isRejectApiInProgress||_.isApproveApiInProgress||_.isRejectApprovedDataApiInProgress?"none":"")),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](2),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngForOf",_.applications),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](2),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngClass",_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pureFunction2"](17,_c3,_.isPendingRequestSelected,!_.isPendingRequestSelected)),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275textInterpolate2"](" ",_.homePageUiConfig["REQUEST-TEXT-001"]," ",_.applications&&_.applications.length>0&&_.applications[_.currentSelectedApplicationIndex].count?"("+_.applications[_.currentSelectedApplicationIndex].count+")":""," "),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf",_.applications[_.currentSelectedApplicationIndex].header_customization.is_previous_requests_visible),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngStyle",_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pureFunction2"](20,_c14,_.isLoading||_.isListViewLoading||_.isRejectApiInProgress||_.isApproveApiInProgress||_.isRejectApprovedDataApiInProgress?"none":"",_.countSelected?"space-between":"end")),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf",_.countSelected),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](2),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf",_.applications[_.currentSelectedApplicationIndex].header_customization.is_search_visible),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf",_.applications[_.currentSelectedApplicationIndex].header_customization.is_group_by_visible),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf",_.applications[_.currentSelectedApplicationIndex].header_customization.is_column_customization_visible),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf",_.applications[_.currentSelectedApplicationIndex].header_customization.is_filter_visible),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf",_.currentFilterSubApplicationId&&!_.isListViewLoading),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf",_.isListViewLoading),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf",!_.isListViewLoading)}}function LandingPageComponent_ng_container_10_Template(_,e){if(1&_&&(_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerStart"](0),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](1,LandingPageComponent_ng_container_10_ng_container_1_Template,5,2,"ng-container",5),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](2,LandingPageComponent_ng_container_10_ng_container_2_Template,19,23,"ng-container",5),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementContainerEnd"]()),2&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"]();_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf",0==_.applications.length),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf",_.applications.length>0)}}function LandingPageComponent_ng_template_11_Template(_,e){if(1&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275getCurrentView"]();_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](0,"app-inbox-column-customization",124),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275listener"]("onApply",(function(e){return _angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275restoreView"](_),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"]().onApplyColumnCustomization(e)})),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"]()}if(2&_){const _=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275nextContext"]();_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("customization",_.currentColumnConfig)}}const _c15=function(_,e){return{background:_,visibility:e}};let LandingPageComponent=(()=>{class LandingPageComponent{constructor(_,e,n,t,a,r,i,o,c,l,s,E,g){this._toaster=_,this._masterService=e,this._utilitiesService=n,this._inboxService=t,this._rolesService=a,this._filterService=r,this._loginService=i,this._router=o,this._activatedRoute=c,this._overlay=l,this._viewContainerRef=s,this._dialog=E,this._edService=g,this._onDestroy=new rxjs__WEBPACK_IMPORTED_MODULE_1__.b,this.loadingGif="",this.currentSelectedApplicationId=null,this.currentSelectedApprovalType=null,this.currentSelectedApplicationIndex=null,this.homePageUiConfig={},this.profile={},this.applications=[],this.subs=new subsink__WEBPACK_IMPORTED_MODULE_5__.a,this.isLoading=!0,this.isListViewLoading=!1,this.isPendingRequestSelected=!0,this.isSearchBarVisible=!1,this.isRejectApprovedDataApiInProgress=!1,this.isApproveApiInProgress=!1,this.isRejectApiInProgress=!1,this.skip=0,this.limit=15,this.filterData={},this.filterQuery="",this.appliedFilter=[],this.searchParams="",this.currentFilterSubApplicationId="",this.currentColumnConfig=[],this.currentData=[],this.pendingCheckAll=!1,this.countSelected=0,this.selectedDetails=[],this.unselectedDetails=[],this.approvalPendings=[]}ngOnInit(){return Object(tslib__WEBPACK_IMPORTED_MODULE_0__.c)(this,void 0,void 0,(function*(){this.profile=this._loginService.getProfile().profile,this.associateId=this.profile.aid,this._rolesService.checkApplicationForRoles("KEBS Homepage")?(this.calculateDynamicContentHeight(),this.getUiConfiguration("homePage"),this._utilitiesService.loadingGif$.subscribe(_=>{this.loadingGif=_}),yield this.getInboxApplications(),yield this.addInitialWidthFieldInAllApplications(),this._activatedRoute.queryParams.subscribe(_=>Object(tslib__WEBPACK_IMPORTED_MODULE_0__.c)(this,void 0,void 0,(function*(){if(this.currentSelectedApplicationId=_.applicationId?_.applicationId:null,this.currentSelectedApprovalType=_.approvalType?_.approvalType:null,this.currentSelectedApplicationId){let _=this.applications.findIndex(_=>(null==_?void 0:_.application_id)==this.currentSelectedApplicationId&&(null==_?void 0:_.approval_type)==this.currentSelectedApprovalType);this.updateIsSelectedApplication(-1==_?0:_)}else this.applications.length>0?(this._router.navigate([],{relativeTo:this._activatedRoute,queryParams:{applicationId:this.applications[0].application_id,approvalType:this.applications[0].approval_type||null},queryParamsHandling:"merge"}),this.currentSelectedApplicationId=this.applications[0].application_id,this.currentSelectedApprovalType=this.applications[0].approval_type,this.updateIsSelectedApplication(0)):this._router.navigateByUrl("/main")})))):this._router.navigateByUrl("/main/dashboard"),this.currentSelectedApplicationId&&(yield this.fetchAllPendingApprovalsCountData())}))}onResize(){this.calculateDynamicContentHeight()}calculateDynamicContentHeight(){let _=window.innerWidth-128,e=this.currentColumnConfig.reduce((_,e)=>_+parseInt(e.initial_width),0);if(e<_){const n=_/e;this.currentColumnConfig=this.currentColumnConfig.map(_=>{const e=parseInt(_.initial_width),t=Math.round(e*n);return Object.assign(Object.assign({},_),{width:t+"px"})})}else this.currentColumnConfig=this.currentColumnConfig.map(_=>Object.assign(Object.assign({},_),{width:_.initial_width}));document.documentElement.style.setProperty("--homePageDynamicHeight",window.innerHeight-55+"px"),document.documentElement.style.setProperty("--homePageInboxDynamicHeight",window.innerHeight-91+"px"),this.filterQuery&&""!=this.filterQuery?document.documentElement.style.setProperty("--homePageInboxListDynamicHeight",window.innerHeight-91-180+"px"):document.documentElement.style.setProperty("--homePageInboxListDynamicHeight",window.innerHeight-91-128+"px")}initializeLocalStorageData(){return Object(tslib__WEBPACK_IMPORTED_MODULE_0__.c)(this,void 0,void 0,(function*(){let _=this.isPendingRequestSelected?"-pending-request-":"-previous-request-",e=JSON.parse(localStorage.getItem(this.currentSelectedApplicationId+(this.currentSelectedApprovalType||"")+_+"-inbox-list-view-customization-"+this.profile.aid));if(e&&e.length>0){let n=underscore__WEBPACK_IMPORTED_MODULE_3__.pluck(this.currentColumnConfig,"id"),t=underscore__WEBPACK_IMPORTED_MODULE_3__.pluck(e,"id");n.every(_=>t.includes(_))?this.currentColumnConfig=e:localStorage.removeItem(this.currentSelectedApplicationId+(this.currentSelectedApprovalType||"")+_+"-inbox-list-view-customization-"+this.profile.aid)}this.calculateDynamicContentHeight()}))}addInitialWidthFieldInAllApplications(){return Object(tslib__WEBPACK_IMPORTED_MODULE_0__.c)(this,void 0,void 0,(function*(){if(this.applications&&this.applications.length>0)for(let _=0;_<this.applications.length;_++){if(this.applications[_].pending_request_list_view_configurations)for(let e=0;e<this.applications[_].pending_request_list_view_configurations.length;e++)this.applications[_].pending_request_list_view_configurations[e].initial_width=this.applications[_].pending_request_list_view_configurations[e].width;if(this.applications[_].previous_request_list_view_configurations)for(let e=0;e<this.applications[_].previous_request_list_view_configurations.length;e++)this.applications[_].previous_request_list_view_configurations[e].initial_width=this.applications[_].previous_request_list_view_configurations[e].width}}))}openCustomizationOverlay(_){var e;if(!(null===(e=this.overlayRef)||void 0===e?void 0:e.hasAttached())){const e=this._overlay.position().flexibleConnectedTo(_).withFlexibleDimensions(!0).withPush(!0).withViewportMargin(25).withGrowAfterOpen(!0).withPositions([{originX:"center",originY:"bottom",overlayX:"center",overlayY:"top"}]);e.withDefaultOffsetY(20);const n=this._overlay.scrollStrategies.close();this.overlayRef=this._overlay.create({positionStrategy:e,scrollStrategy:n,hasBackdrop:!0,panelClass:["pop-up"]});const t=new _angular_cdk_portal__WEBPACK_IMPORTED_MODULE_4__.h(this.triggerColumnCustomizationTemplateRef,this._viewContainerRef);this.overlayRef.attach(t),this.overlayRef.backdropClick().subscribe(()=>{this.closeOverlay()})}}closeOverlay(){var _;null===(_=this.overlayRef)||void 0===_||_.dispose()}onApplyColumnCustomization(_){return Object(tslib__WEBPACK_IMPORTED_MODULE_0__.c)(this,void 0,void 0,(function*(){let e=this.isPendingRequestSelected?"-pending-request-":"-previous-request-";this.currentColumnConfig=[..._],localStorage.setItem(this.currentSelectedApplicationId+(this.currentSelectedApprovalType||"")+e+"-inbox-list-view-customization-"+this.profile.aid,JSON.stringify(this.currentColumnConfig)),this.closeOverlay(),this.calculateDynamicContentHeight()}))}stopParentPropagation(_){_&&_.stopPropagation()}onIndividualCheckboxSelected(){if(this.currentData&&this.currentData.length>0){if(this.pendingCheckAll){let _=this.currentData.filter(_=>0==_.isChecked);this.countSelected=this.applications[this.currentSelectedApplicationIndex].count-_.length}else this.countSelected=this.currentData.filter(_=>1==_.isChecked).length;let _=this.currentData.filter(_=>1==_.isChecked);this.selectedDetails=_;let e=this.currentData.filter(_=>0==_.isChecked);this.unselectedDetails=e,this.countSelected&&0!=this.countSelected||(this.pendingCheckAll=!1)}}onChangePendingCheckAll(){this.currentData.forEach(_=>_.isChecked=this.pendingCheckAll),this.onIndividualCheckboxSelected()}openHomeScreen(){this._router.navigateByUrl("/main/home/<USER>")}openApprovalsScreen(){this._router.navigateByUrl("/main/home/<USER>")}updateIsSelectedApplication(_){return Object(tslib__WEBPACK_IMPORTED_MODULE_0__.c)(this,void 0,void 0,(function*(){this.applications.forEach(_=>{_.isSelected=!1}),this.applications[_].isSelected=!0,this.currentSelectedApplicationIndex=_,this.currentSelectedApplicationId=this.applications[_].application_id,this.currentSelectedApprovalType=this.applications[_].approval_type,this._router.navigate([],{relativeTo:this._activatedRoute,queryParams:{applicationId:this.applications[_].application_id,approvalType:this.applications[_].approval_type||null},queryParamsHandling:"merge"}),this.currentColumnConfig=this.isPendingRequestSelected?this.applications[_].pending_request_list_view_configurations||[]:this.applications[_].previous_request_list_view_configurations||[],this.currentFilterSubApplicationId=this.isPendingRequestSelected?this.applications[_].pending_request_filter_sub_application_id||"":this.applications[_].previous_request_filter_sub_application_id||"",this.initializeLocalStorageData(),this.filterSubscription$=this._filterService.getFilterConfig(this.currentSelectedApplicationId,this.currentFilterSubApplicationId).subscribe(_=>{this.applyFilter()}),this.calculateDynamicContentHeight()}))}switchApplication(_){return Object(tslib__WEBPACK_IMPORTED_MODULE_0__.c)(this,void 0,void 0,(function*(){this.currentSelectedApplicationIndex!=_&&(this.isPendingRequestSelected=!0,this.pendingCheckAll=!1,this.countSelected=0,this.currentData=[],this.skip=0,this.searchParams="",this.isSearchBarVisible=!1,yield this.updateIsSelectedApplication(_),this._router.navigate([],{relativeTo:this._activatedRoute,queryParams:{applicationId:this.applications[_].application_id,approvalType:this.applications[_].approval_type||null},queryParamsHandling:"merge"}))}))}switchToPendingRequest(){return Object(tslib__WEBPACK_IMPORTED_MODULE_0__.c)(this,void 0,void 0,(function*(){this.isPendingRequestSelected||(this.isPendingRequestSelected=!0,this.isSearchBarVisible=!1,this.pendingCheckAll=!1,this.countSelected=0,this.currentData=[],this.skip=0,this.searchParams="",yield this.updateIsSelectedApplication(this.currentSelectedApplicationIndex))}))}switchToPreviousRequest(){return Object(tslib__WEBPACK_IMPORTED_MODULE_0__.c)(this,void 0,void 0,(function*(){this.isPendingRequestSelected&&(this.isPendingRequestSelected=!1,this.isSearchBarVisible=!1,this.pendingCheckAll=!1,this.countSelected=0,this.currentData=[],this.skip=0,this.searchParams="",yield this.updateIsSelectedApplication(this.currentSelectedApplicationIndex))}))}onClickSearchIcon(){this.isSearchBarVisible=!0,setTimeout(()=>{document.getElementById("inputSearchField").focus()},100)}openHyperLink(_){_&&""!=_&&"string"==typeof _?window.open(_):this._toaster.showWarning("Warning \u26a0\ufe0f","Invalid URL or No URL Found!",7e3)}columnClickAction(_,e,n){_&&("hyperlink"==(null==_?void 0:_.action_type)?this.openHyperLink(n):"function"==(null==_?void 0:_.action_type)&&this[null==_?void 0:_.function](_,e))}openProjectsDetailsPage(_,e){return Object(tslib__WEBPACK_IMPORTED_MODULE_0__.c)(this,void 0,void 0,(function*(){if(console.log(e),"hyperlink"==(null==e?void 0:e.type))this.openHyperLink(null==e?void 0:e.url);else if("dialog"==(null==e?void 0:e.type)){const{ApprovalsDetailViewComponent:_}=yield Promise.all([__webpack_require__.e(23),__webpack_require__.e(109),__webpack_require__.e(158)]).then(__webpack_require__.bind(null,"h3/9"));this._dialog.open(_,{height:"100%",minWidth:"50%",position:{right:"0px"},data:{formConfig:null,details:e}}).afterClosed().subscribe(_=>Object(tslib__WEBPACK_IMPORTED_MODULE_0__.c)(this,void 0,void 0,(function*(){_&&this.getRequestDataDynamically(!0)})))}}))}onClickActionsIcon(data,type,apiURL,fileName,listIndex,fieldIndex,iconIndex){if("api"==type)return!data||""==apiURL||apiURL?void this._toaster.showWarning("Warning \u26a0\ufe0f","API Configurations Missing!",7e3):new Promise((_,e)=>this._inboxService.onClickActionsIcon(data,apiURL).pipe(Object(rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?this._toaster.showSuccess("Success \u2705",e.msg,7e3):this._toaster.showError("Error",e.msg,7e3),_(!0)},error:_=>{this._toaster.showError("Error",_.msg?_.msg:"API Failed!",7e3),e()}}));if("hyperlink"==type){let _=data[apiURL];this.openHyperLink(_)}else if("attachment_download"==type){if(!apiURL)return void this._toaster.showWarning("Warning \u26a0\ufe0f","API Configurations Missing!",7e3);this.currentData[listIndex][`${this.currentColumnConfig[fieldIndex].id}_${iconIndex}_loader`]=!0,fileName=eval(fileName),this._inboxService.onClickDownloadAttachments(data,apiURL).pipe(Object(rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.a)(this._onDestroy)).subscribe(_=>{const e=window.URL.createObjectURL(_),n=document.createElement("a");n.href=e,n.download=fileName,document.body.appendChild(n),n.click(),document.body.removeChild(n),window.URL.revokeObjectURL(e),this.currentData[listIndex][`${this.currentColumnConfig[fieldIndex].id}_${iconIndex}_loader`]=!1},_=>{this.currentData[listIndex][`${this.currentColumnConfig[fieldIndex].id}_${iconIndex}_loader`]=!1,this._toaster.showWarning("Warning \u26a0\ufe0f","No Attachments Found!",7e3)})}}onSingleDataApprove(_){return Object(tslib__WEBPACK_IMPORTED_MODULE_0__.c)(this,void 0,void 0,(function*(){let e=this.applications[this.currentSelectedApplicationIndex].api_configurations.approve_api,n=this.applications[this.currentSelectedApplicationIndex].api_response_type;if(!e||!n)return this._toaster.showError("Error","API Configurations Not Found!",7e3);let t={aid:this.profile.aid,oid:this.profile.oid,selectedDetails:[this.currentData[_]],status:"A",isApproveApi:!0};return this.isApproveApiInProgress=!0,new Promise((a,r)=>{this._inboxService.getDataDynamically(e,t).pipe(Object(rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.a)(this._onDestroy)).subscribe({next:e=>{n&&"messType"==n?"S"==e.messType?(this.currentData.splice(_,1),this.applications[this.currentSelectedApplicationIndex].count-=1,this._toaster.showSuccess("Success \u2705",e.messText,7e3)):this._toaster.showError("Error",e.messText,7e3):n&&"err"==n&&(0==e.err?(this.currentData.splice(_,1),this.applications[this.currentSelectedApplicationIndex].count-=1,this._toaster.showSuccess("Success \u2705",e.msg,7e3)):this._toaster.showError("Error",e.msg,7e3)),this.isApproveApiInProgress=!1,a(!0)},error:_=>{this._toaster.showError("Error","Error in Approve API",7e3),this.isApproveApiInProgress=!1,r()}})})}))}onSingleDataReject(_){return Object(tslib__WEBPACK_IMPORTED_MODULE_0__.c)(this,void 0,void 0,(function*(){let e=this.applications[this.currentSelectedApplicationIndex].api_configurations.reject_api,n=this.applications[this.currentSelectedApplicationIndex].api_response_type;if(!e||!n)return this._toaster.showError("Error","API Configurations Not Found!",7e3);const{InboxRejectDialogComponent:t}=yield __webpack_require__.e(957).then(__webpack_require__.bind(null,"0IPq"));this._dialog.open(t,{width:"800px",disableClose:!0,data:{data:this.applications[this.currentSelectedApplicationIndex].rejection_comments||[]}}).afterClosed().subscribe(t=>{if(t){let a={aid:this.profile.aid,oid:this.profile.oid,selectedDetails:[this.currentData[_]],status:"R",comments:t,isRejectApi:!0};return this.isRejectApiInProgress=!0,new Promise((t,r)=>{this._inboxService.getDataDynamically(e,a).pipe(Object(rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.a)(this._onDestroy)).subscribe({next:e=>{n&&"messType"==n?"S"==e.messType?(this.currentData.splice(_,1),this.applications[this.currentSelectedApplicationIndex].count-=1,this._toaster.showSuccess("Success \u2705",e.messText,7e3)):this._toaster.showError("Error",e.messText,7e3):n&&"err"==n&&(0==e.err?(this.currentData.splice(_,1),this.applications[this.currentSelectedApplicationIndex].count-=1,this._toaster.showSuccess("Success \u2705",e.msg,7e3)):this._toaster.showError("Error",e.msg,7e3)),this.isRejectApiInProgress=!1,t(!0)},error:_=>{this._toaster.showError("Error","Error in Reject API",7e3),this.isRejectApiInProgress=!1,r()}})})}})}))}onBulkApprove(){return Object(tslib__WEBPACK_IMPORTED_MODULE_0__.c)(this,void 0,void 0,(function*(){let _=this.applications[this.currentSelectedApplicationIndex].api_configurations.bulk_approve_api;if(!_)return this._toaster.showError("Error","API Configurations Not Found!",7e3);let e=this.currentData.filter(_=>1==_.isChecked);this.selectedDetails=e;let n=this.currentData.filter(_=>0==_.isChecked);this.unselectedDetails=n,this._toaster.showSuccess("Success \u2705","Selected Request(s) has been sent for Approval Process. All the Requests will be Approved in sometime!",7e3);let t={aid:this.profile.aid,oid:this.profile.oid,selectedDetails:this.selectedDetails,unselectedDetails:this.unselectedDetails,bulkApprove:this.pendingCheckAll,searchParams:this.searchParams,filter:this.appliedFilter,filterQuery:this.filterQuery,status:"A",isApproveApi:!0};return this.applications[this.currentSelectedApplicationIndex].count=this.applications[this.currentSelectedApplicationIndex].count-this.countSelected,this.countSelected=0,this.currentData=this.currentData.filter(_=>!1===_.isChecked),this.pendingCheckAll=!1,new Promise((e,n)=>{this._inboxService.getDataDynamically(_,t).pipe(Object(rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.a)(this._onDestroy)).subscribe({next:_=>{e(!0)},error:_=>{this._toaster.showError("Error","Error in Bulk Approve API",7e3),n()}})})}))}onRejectApprovedItem(_){return Object(tslib__WEBPACK_IMPORTED_MODULE_0__.c)(this,void 0,void 0,(function*(){let e=this.applications[this.currentSelectedApplicationIndex].api_configurations.reject_approved_item_api,n=this.applications[this.currentSelectedApplicationIndex].api_response_type;if(!e||!n)return this._toaster.showError("Error","API Configurations Not Found!",7e3);const{InboxRejectDialogComponent:t}=yield __webpack_require__.e(957).then(__webpack_require__.bind(null,"0IPq"));this._dialog.open(t,{width:"800px",disableClose:!0,data:{data:this.applications[this.currentSelectedApplicationIndex].rejection_comments||[]}}).afterClosed().subscribe(t=>{if(t){let a={aid:this.profile.aid,oid:this.profile.oid,comments:[t],status:"R",approvedItemDetails:[this.currentData[_]],statusId:3};return this.isRejectApprovedDataApiInProgress=!0,new Promise((_,t)=>{this._inboxService.getDataDynamically(e,a).pipe(Object(rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.a)(this._onDestroy)).subscribe({next:e=>{n&&"messType"==n?"S"==e.messType?(this.getRequestDataDynamically(!0),this._toaster.showSuccess("Success \u2705",e.messText,7e3)):this._toaster.showError("Error",e.messText,7e3):n&&"err"==n&&(0==e.err?(this.getRequestDataDynamically(!0),this._toaster.showSuccess("Success \u2705",e.msg,7e3)):this._toaster.showError("Error",e.msg,7e3)),this.isRejectApprovedDataApiInProgress=!1,_(!0)},error:_=>{this._toaster.showError("Error","Error in Reject API",7e3),this.isRejectApprovedDataApiInProgress=!1,t()}})})}})}))}onClearSearch(){this.searchParams="",this.isSearchBarVisible=!1,this.getRequestDataDynamically(!0)}onEnterSearch(){this.getRequestDataDynamically(!0)}openFilterDialog(){return Object(tslib__WEBPACK_IMPORTED_MODULE_0__.c)(this,void 0,void 0,(function*(){this._filterService.openFilterLandingPage(this.currentSelectedApplicationId,this.currentFilterSubApplicationId,this.filterData)}))}onClickSort(_,e){return Object(tslib__WEBPACK_IMPORTED_MODULE_0__.c)(this,void 0,void 0,(function*(){let n=this.isPendingRequestSelected?"-pending-request-":"-previous-request-";this.currentColumnConfig.forEach(_=>{_.sort_order=0}),this.currentColumnConfig[e].sort_order=_,localStorage.setItem(this.currentSelectedApplicationId+(this.currentSelectedApprovalType||"")+n+"-inbox-list-view-customization-"+this.profile.aid,JSON.stringify(this.currentColumnConfig)),this.getRequestDataDynamically(!0)}))}applyFilter(){return Object(tslib__WEBPACK_IMPORTED_MODULE_0__.c)(this,void 0,void 0,(function*(){if(this.isListViewLoading)return;this.isListViewLoading=!0,this.filterQuery="",this.appliedFilter=[];let _=yield this.getUserFilterConfig();if(_&&""!==_&&null!=_){let e=_&&_.filterConfig&&_.filterConfig.filterData?_.filterConfig.filterData:[],n=this._filterService.generateConditionBasedQuery(e);this.appliedFilter=e,this.filterQuery=n||""}yield this.getRequestDataDynamically(!0)}))}onDataScroll(){return Object(tslib__WEBPACK_IMPORTED_MODULE_0__.c)(this,void 0,void 0,(function*(){this.applications[this.currentSelectedApplicationIndex].is_api_lazy_loaded&&(this.skip+=15,yield this.getRequestDataDynamically(!1))}))}fetchAllPendingApprovalsCountData(){return Object(tslib__WEBPACK_IMPORTED_MODULE_0__.c)(this,void 0,void 0,(function*(){let _={aid:this.profile.aid,oid:this.profile.oid,isFromInbox:!0};return new Promise((e,n)=>{const t=this.applications.map((e,n)=>new Promise((t,a)=>{var r;this._inboxService.getDataDynamically(null===(r=null==e?void 0:e.api_configurations)||void 0===r?void 0:r.pending_request_count_api,_).pipe(Object(rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.a)(this._onDestroy)).subscribe({next:_=>{(null==e?void 0:e.api_response_type)?(null==e?void 0:e.api_response_type)&&"messType"==(null==e?void 0:e.api_response_type)?"S"==_.messType&&(this.applications[n].count=_.data||0):(null==e?void 0:e.api_response_type)&&"err"==(null==e?void 0:e.api_response_type)&&0==_.err&&(this.applications[n].count=_.data||0):this.applications[n].count=_||0,t(_)},error:_=>{a(_)}})}));Promise.all(t).then(_=>{this.isLoading=!1,e()}).catch(_=>{this.isLoading=!1,this.isListViewLoading=!1,n()})})}))}getUiConfiguration(_){return Object(tslib__WEBPACK_IMPORTED_MODULE_0__.c)(this,void 0,void 0,(function*(){return new Promise((e,n)=>this._masterService.getUiConfiguration(_).pipe(Object(rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.a)(this._onDestroy)).subscribe({next:n=>{0==n.err?"homePage"==_&&(this.homePageUiConfig=n.data):this._toaster.showError("Error",n.msg,7e3),e(!0)},error:_=>{this._toaster.showError("Error",_.message?_.message:"UI Configuration Retrieval Failed!",7e3),n()}}))}))}getInboxApplications(){return Object(tslib__WEBPACK_IMPORTED_MODULE_0__.c)(this,void 0,void 0,(function*(){return new Promise((_,e)=>this._inboxService.getInboxApplications().pipe(Object(rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?this.applications=e.data:this._toaster.showError("Error",e.msg,7e3),_(!0)},error:_=>{this._toaster.showError("Error",_.message?_.message:"Application Configuration Retrieval Failed!",7e3),this.isLoading=!1,this.isListViewLoading=!1,e()}}))}))}getUserFilterConfig(){if(this.currentFilterSubApplicationId&&""!=this.currentFilterSubApplicationId)return new Promise((_,e)=>{this._filterService.getFilterUserConfig(this.currentSelectedApplicationId,this.currentFilterSubApplicationId).pipe(Object(rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.a)(this._onDestroy)).subscribe({next:e=>{_("S"==e.messType?e.data:[])},error:e=>{this._toaster.showError("Error","Error in Fetching User Filter Config",7e3),_([])}})})}getRequestDataDynamically(_){return Object(tslib__WEBPACK_IMPORTED_MODULE_0__.c)(this,void 0,void 0,(function*(){_&&(this.pendingCheckAll=!1,this.countSelected=0,this.currentData=[],this.skip=0,this.isListViewLoading=!0,this.isPendingRequestSelected&&(yield this.getRequestCountDynamically()));let e=this.isPendingRequestSelected?this.applications[this.currentSelectedApplicationIndex].api_configurations.pending_request_data_api||null:this.applications[this.currentSelectedApplicationIndex].api_configurations.previous_request_data_api||null,n=this.currentColumnConfig.filter(_=>(null==_?void 0:_.is_sort_active)&&(null==_?void 0:_.sort_order)),t=this.applications[this.currentSelectedApplicationIndex].api_response_type,a={aid:this.profile.aid,oid:this.profile.oid,searchParams:this.searchParams,filter:this.appliedFilter,filterQuery:this.filterQuery,sort:n,skip:this.skip,limit:this.limit};return new Promise((n,r)=>{this._inboxService.getDataDynamically(e,a).pipe(Object(rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.a)(this._onDestroy)).subscribe({next:e=>{if(_&&(this.currentData=[]),t){if(t&&"messType"==t){if("S"==e.messType){const _=e.data.map(_=>Object.assign(Object.assign({},_),{isChecked:!!this.isPendingRequestSelected&&this.pendingCheckAll}));this.currentData=[...this.currentData,..._]}}else if(t&&"err"==t&&0==e.err){const _=e.data.map(_=>Object.assign(Object.assign({},_),{isChecked:!!this.isPendingRequestSelected&&this.pendingCheckAll}));this.currentData=[...this.currentData,..._]}}else{const _=e.map(_=>Object.assign(Object.assign({},_),{isChecked:!!this.isPendingRequestSelected&&this.pendingCheckAll}));this.currentData=[...this.currentData,..._]}this.isListViewLoading=!1,n(!0)},error:_=>{this._toaster.showError("Error","Error in Request Data",7e3),this.isListViewLoading=!1,r()}})})}))}getRequestCountDynamically(){return Object(tslib__WEBPACK_IMPORTED_MODULE_0__.c)(this,void 0,void 0,(function*(){let _=this.applications[this.currentSelectedApplicationIndex].api_configurations.pending_request_count_api||null,e=this.applications[this.currentSelectedApplicationIndex].api_response_type,n={aid:this.profile.aid,oid:this.profile.oid,searchParams:this.searchParams,filter:this.appliedFilter,filterQuery:this.filterQuery};return new Promise((t,a)=>{this._inboxService.getDataDynamically(_,n).pipe(Object(rxjs_operators__WEBPACK_IMPORTED_MODULE_2__.a)(this._onDestroy)).subscribe({next:_=>{e?e&&"messType"==e?"S"==_.messType&&(this.applications[this.currentSelectedApplicationIndex].count=_.data||0):e&&"err"==e&&0==_.err&&(this.applications[this.currentSelectedApplicationIndex].count=_.data||0):this.applications[this.currentSelectedApplicationIndex].count=_||0,t(!0)},error:_=>{this._toaster.showError("Error","Error in Fetching Inbox Data",7e3),this.isListViewLoading=!1,a()}})})}))}getApprovalPendingDetailsDetails(_){return new Promise((e,n)=>{this.subs.sink=this._edService.getPendingApprovalsForEmployeeAID(_,"skill_details").subscribe(_=>{_.err||e(_.approval_list)},_=>{this._toaster.showError("Error","Failed to Update Skill Validation!",2e3),console.log(_),n(_)})})}openApprovalPendingDialog(_,e){var n,t,a;return Object(tslib__WEBPACK_IMPORTED_MODULE_0__.c)(this,void 0,void 0,(function*(){let _;console.log(e),this.isPendingRequestSelected?(this.approvalPendings=yield this.getApprovalPendingDetailsDetails(e.submitted_by_aid),console.log(e.submitted_by_aid),_={associateId:parseInt(e.submitted_by_aid),approvalPendings:null===(t=null===(n=this.approvalPendings[0])||void 0===n?void 0:n.submission_item)||void 0===t?void 0:t.approval_fields,pendingMetaData:this.approvalPendings[0]}):_={associateId:parseInt(e.submitted_by_aid),approvalPendings:null===(a=null==e?void 0:e.submission_item)||void 0===a?void 0:a.approval_fields,pendingMetaData:e,showlength:!1};const{ApprovalPendingDetailScreenComponent:r}=yield Promise.all([__webpack_require__.e(19),__webpack_require__.e(954)]).then(__webpack_require__.bind(null,"slBa"));this._dialog.open(r,{width:"700px",panelClass:"e360-approval-pending-dialog-box",autoFocus:!1,data:{modalParams:_}}).afterClosed().subscribe(_=>{},_=>{this._toaster.showError("Error","Failed to retrieve employee !",2e3)})}))}}return LandingPageComponent.\u0275fac=function(_){return new(_||LandingPageComponent)(_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275directiveInject"](src_app_modules_applicant_tracking_system_shared_components_ats_custom_toast_toaster_service__WEBPACK_IMPORTED_MODULE_7__.a),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275directiveInject"](_services_master_master_service__WEBPACK_IMPORTED_MODULE_8__.a),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275directiveInject"](_services_utilities_utilities_service__WEBPACK_IMPORTED_MODULE_9__.a),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275directiveInject"](_services_inbox_inbox_service__WEBPACK_IMPORTED_MODULE_10__.a),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275directiveInject"](src_app_services_acl_roles_service__WEBPACK_IMPORTED_MODULE_11__.a),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275directiveInject"](src_app_modules_project_management_shared_lazy_loaded_components_filters_common_dialog_services_filter_service_service__WEBPACK_IMPORTED_MODULE_12__.a),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275directiveInject"](src_app_services_login_login_service__WEBPACK_IMPORTED_MODULE_13__.a),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275directiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_14__.g),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275directiveInject"](_angular_router__WEBPACK_IMPORTED_MODULE_14__.a),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275directiveInject"](_angular_cdk_overlay__WEBPACK_IMPORTED_MODULE_15__.e),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275directiveInject"](_angular_core__WEBPACK_IMPORTED_MODULE_6__.ViewContainerRef),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275directiveInject"](_angular_material_dialog__WEBPACK_IMPORTED_MODULE_16__.b),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275directiveInject"](src_app_modules_employee_directory_services_employee_directory_service__WEBPACK_IMPORTED_MODULE_17__.a))},LandingPageComponent.\u0275cmp=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275defineComponent"]({type:LandingPageComponent,selectors:[["app-landing-page"]],viewQuery:function(_,e){if(1&_&&_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275viewQuery"](_c0,!0),2&_){let _;_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275queryRefresh"](_=_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275loadQuery"]())&&(e.triggerColumnCustomizationTemplateRef=_.first)}},hostBindings:function(_,e){1&_&&_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275listener"]("resize",(function(){return e.onResize()}),!1,_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275resolveWindow"])},decls:13,vars:14,consts:[[1,"bg-container"],[1,"header",3,"ngStyle"],[1,"svg",3,"innerHTML","click"],[1,"bar-text"],[1,"approvals-text",3,"click"],[4,"ngIf"],["cdkConnectedOverlay","",3,"cdkConnectedOverlayOrigin"],["triggerColumnCustomizationTemplateRef",""],[1,"empty-state"],[1,"image",3,"src"],[1,"loading-wrapper"],[1,"loading"],[1,"empty-state-image",3,"src"],[1,"empty-state-text"],[1,"inbox-container"],[1,"inbox-container-header",3,"ngStyle"],[1,"inbox-container-header-1"],["class","application",3,"ngClass","ngStyle","click",4,"ngFor","ngForOf"],[1,"inbox-container-header-2"],[3,"ngClass","click"],[3,"ngClass","click",4,"ngIf"],[1,"inbox-container-sub-header",3,"ngStyle"],["class","inbox-container-sub-header-1",4,"ngIf"],[1,"inbox-container-sub-header-2"],["class","icon",4,"ngIf"],["class","icon","cdkOverlayOrigin","",3,"click",4,"ngIf"],["class","icon",3,"click",4,"ngIf"],["class","filter-display",4,"ngIf"],[1,"application",3,"ngClass","ngStyle","click"],[1,"inbox-container-sub-header-1"],[1,"request-selected-text"],[1,"approve-btn",3,"click"],["class","search-ui",4,"ngIf"],[1,"search-ui"],[1,"search-bar"],["id","inputSearchField","type","text","placeholder","Search",3,"ngModel","ngModelChange","keydown.enter"],["width","16","height","16","viewBox","0 0 16 16","fill","none"],["clip-path","url(#clip0_1512_23213)"],["d","M12.0194 11.0767L14.8747 13.9314L13.9314 14.8747L11.0767 12.0194C10.0145 12.8708 8.69337 13.334 7.33203 13.332C4.02003 13.332 1.33203 10.644 1.33203 7.33203C1.33203 4.02003 4.02003 1.33203 7.33203 1.33203C10.644 1.33203 13.332 4.02003 13.332 7.33203C13.334 8.69337 12.8708 10.0145 12.0194 11.0767ZM10.682 10.582C11.5281 9.71196 12.0006 8.54565 11.9987 7.33203C11.9987 4.75336 9.91003 2.66536 7.33203 2.66536C4.75336 2.66536 2.66536 4.75336 2.66536 7.33203C2.66536 9.91003 4.75336 11.9987 7.33203 11.9987C8.54565 12.0006 9.71196 11.5281 10.582 10.682L10.682 10.582Z","fill","#B9C0CA"],["id","clip0_1512_23213"],["width","16","height","16","fill","white"],[1,"icon",3,"click"],["width","10","height","10","viewBox","0 0 10 10","fill","none"],["d","M1.22756 9.8355L0.164062 8.772L3.93706 4.999L0.164062 1.251L1.22756 0.1875L5.00056 3.9605L8.74856 0.1875L9.81206 1.251L6.03906 4.999L9.81206 8.772L8.74856 9.8355L5.00056 6.0625L1.22756 9.8355Z","fill","#45546E"],["clip-path","url(#clip0_1540_292)"],["d","M12.0482 11.0737L15 14.0248L14.0248 15L11.0737 12.0482C9.9757 12.9285 8.60993 13.4072 7.20262 13.4052C3.77877 13.4052 1 10.6265 1 7.20262C1 3.77877 3.77877 1 7.20262 1C10.6265 1 13.4052 3.77877 13.4052 7.20262C13.4072 8.60993 12.9285 9.9757 12.0482 11.0737ZM10.6657 10.5624C11.5404 9.66291 12.0289 8.45722 12.0269 7.20262C12.0269 4.53687 9.86768 2.37836 7.20262 2.37836C4.53687 2.37836 2.37836 4.53687 2.37836 7.20262C2.37836 9.86768 4.53687 12.0269 7.20262 12.0269C8.45722 12.0289 9.66291 11.5404 10.5624 10.6657L10.6657 10.5624Z","fill","#515965"],["id","clip0_1540_292"],[1,"icon"],["width","24","height","24","viewBox","0 0 24 24","fill","none"],["clip-path","url(#clip0_1512_23123)"],["cx","7.5","cy","7.5","r","3","fill","#515965"],["cx","7.5","cy","16.5","r","3","fill","#515965"],["cx","16.5","cy","7.5","r","3","fill","#515965"],["cx","16.5","cy","16.5","r","3","fill","#515965"],["id","clip0_1512_23123"],["width","24","height","24","fill","white"],["cdkOverlayOrigin","",1,"icon",3,"click"],["triggerColumnCustomization","cdkOverlayOrigin","triggerColumnCustomizationField",""],["id","mask0_1512_23124","maskUnits","userSpaceOnUse","x","0","y","0","width","24","height","24",2,"mask-type","alpha"],["width","24","height","24","fill","#D9D9D9"],["mask","url(#mask0_1512_23124)"],["d","M5.9215 19.75C5.70892 19.75 5.53083 19.6781 5.38725 19.5343C5.24367 19.3906 5.17187 19.2125 5.17187 19V12.6923H3.92185C3.70935 12.6923 3.53123 12.6203 3.38748 12.4766C3.24374 12.3327 3.17188 12.1545 3.17188 11.942C3.17188 11.7294 3.24374 11.5513 3.38748 11.4077C3.53123 11.2641 3.70935 11.1923 3.92185 11.1923H7.92185C8.13435 11.1923 8.31247 11.2642 8.4562 11.408C8.59995 11.5518 8.67182 11.73 8.67182 11.9426C8.67182 12.1552 8.59995 12.3333 8.4562 12.4769C8.31247 12.6205 8.13435 12.6923 7.92185 12.6923H6.67182V19C6.67182 19.2125 6.59992 19.3906 6.4561 19.5343C6.3123 19.6781 6.1341 19.75 5.9215 19.75ZM5.9215 8.80765C5.70892 8.80765 5.53083 8.73578 5.38725 8.59203C5.24367 8.44829 5.17187 8.27017 5.17187 8.05768V4.99998C5.17187 4.78748 5.24377 4.60935 5.38757 4.4656C5.53139 4.32187 5.70959 4.25 5.92218 4.25C6.13478 4.25 6.31287 4.32187 6.45645 4.4656C6.60003 4.60935 6.67182 4.78748 6.67182 4.99998V8.05768C6.67182 8.27017 6.59992 8.44829 6.4561 8.59203C6.3123 8.73578 6.1341 8.80765 5.9215 8.80765ZM9.99878 8.80765C9.78627 8.80765 9.60816 8.73574 9.46443 8.59193C9.32067 8.44813 9.2488 8.26993 9.2488 8.05733C9.2488 7.84474 9.32067 7.66666 9.46443 7.52308C9.60816 7.37949 9.78627 7.3077 9.99878 7.3077H11.2488V4.99998C11.2488 4.78748 11.3207 4.60935 11.4645 4.4656C11.6083 4.32187 11.7865 4.25 11.9991 4.25C12.2117 4.25 12.3898 4.32187 12.5334 4.4656C12.677 4.60935 12.7487 4.78748 12.7487 4.99998V7.3077H13.9988C14.2113 7.3077 14.3894 7.3796 14.5331 7.5234C14.6769 7.66722 14.7488 7.84542 14.7488 8.058C14.7488 8.2706 14.6769 8.44869 14.5331 8.59228C14.3894 8.73586 14.2113 8.80765 13.9988 8.80765H9.99878ZM11.9985 19.75C11.7859 19.75 11.6078 19.6781 11.4642 19.5343C11.3206 19.3906 11.2488 19.2125 11.2488 19V11.9423C11.2488 11.7298 11.3207 11.5517 11.4645 11.4079C11.6083 11.2642 11.7865 11.1923 11.9991 11.1923C12.2117 11.1923 12.3898 11.2642 12.5334 11.4079C12.677 11.5517 12.7487 11.7298 12.7487 11.9423V19C12.7487 19.2125 12.6768 19.3906 12.533 19.5343C12.3892 19.6781 12.211 19.75 11.9985 19.75ZM18.0754 19.75C17.8628 19.75 17.6847 19.6781 17.5411 19.5343C17.3975 19.3906 17.3257 19.2125 17.3257 19V16.6923H16.0757C15.8632 16.6923 15.6851 16.6203 15.5414 16.4766C15.3976 16.3327 15.3257 16.1545 15.3257 15.942C15.3257 15.7294 15.3976 15.5513 15.5414 15.4077C15.6851 15.2641 15.8632 15.1923 16.0757 15.1923H20.0757C20.2882 15.1923 20.4663 15.2642 20.6101 15.408C20.7538 15.5518 20.8257 15.73 20.8257 15.9426C20.8257 16.1552 20.7538 16.3333 20.6101 16.4769C20.4663 16.6205 20.2882 16.6923 20.0757 16.6923H18.8257V19C18.8257 19.2125 18.7538 19.3906 18.61 19.5343C18.4662 19.6781 18.288 19.75 18.0754 19.75ZM18.0754 12.8077C17.8628 12.8077 17.6847 12.7358 17.5411 12.592C17.3975 12.4483 17.3257 12.2702 17.3257 12.0577V4.99998C17.3257 4.78748 17.3976 4.60935 17.5414 4.4656C17.6852 4.32187 17.8635 4.25 18.076 4.25C18.2886 4.25 18.4667 4.32187 18.6103 4.4656C18.7539 4.60935 18.8257 4.78748 18.8257 4.99998V12.0577C18.8257 12.2702 18.7538 12.4483 18.61 12.592C18.4662 12.7358 18.288 12.8077 18.0754 12.8077Z","fill","#45546E"],["d","M7.38573 15.5C7.13445 15.5 6.92419 15.4153 6.75496 15.2461C6.58573 15.0769 6.50111 14.8666 6.50111 14.6153V8.82688L0.903059 1.71538C0.710759 1.45896 0.682876 1.19229 0.819409 0.915376C0.955942 0.63846 1.18638 0.5 1.51073 0.5H14.4914C14.8158 0.5 15.0462 0.63846 15.1828 0.915376C15.3193 1.19229 15.2914 1.45896 15.0991 1.71538L9.50106 8.82688V14.6153C9.50106 14.8666 9.41644 15.0769 9.24721 15.2461C9.07798 15.4153 8.86772 15.5 8.61643 15.5H7.38573ZM8.00108 8.29998L12.9511 1.99998H3.05108L8.00108 8.29998Z","fill","#45546E"],[1,"filter-display"],[3,"applicationId","internalApplicationId"],[1,"list-view-empty-state"],[1,"loading-image",3,"src"],[1,"empty-state-sub-text"],["infinite-scroll","",1,"list-view",3,"infiniteScrollDistance","infiniteScrollThrottle","scrollWindow","scrolled"],[1,"header-sticky"],[1,"row","header-row-main"],[4,"ngFor","ngForOf"],["class","header-row",3,"class","ngStyle",4,"ngIf"],[1,"header-row",3,"ngStyle"],[1,"header-row-content"],["class","checkbox",3,"ngStyle",4,"ngIf"],["class","list-title",3,"matTooltip",4,"ngIf"],[1,"d-flex","align-items-center"],["class","svg",3,"click",4,"ngIf"],[1,"checkbox",3,"ngStyle"],[3,"ngModel","ngModelChange"],[1,"list-title",3,"matTooltip"],[1,"svg",3,"click"],["width","12","height","12","viewBox","0 0 12 12","fill","none"],["clip-path","url(#clip0_1314_18487)"],["d","M6 4.5H4.0005L4 10.5H3V4.5H1L3.5 2L6 4.5ZM11 8.5L8.5 11L6 8.5H8V2.5H9V8.5H11Z","fill","#6E7B8F"],["id","clip0_1314_18487"],["width","12","height","12","fill","white"],["height","10px","width","8px","fill","#6E7B8F","version","1.1","id","Layer_1","viewBox","0 0 512 512","enable-background","new 0 0 512 512",0,"xml","space","preserve"],["points","245,0 74.3,213.3 202.3,213.3 202.3,512 287.7,512 287.7,213.3 415.7,213.3 "],["points","283.7,298.7 283.7,0 198.3,0 198.3,298.7 70.3,298.7 241,512 411.7,298.7 "],[1,"content-sticky"],[1,"row","content-row-main"],["class","content-row",3,"class","ngStyle","click",4,"ngIf"],[1,"content-row",3,"ngStyle","click"],[3,"ngModel","ngModelChange","click"],[1,"normal-text",3,"matTooltip"],[1,"normal-text",3,"matTooltip","ngStyle"],[1,"chip",3,"ngStyle","matTooltip"],["class","hyperlink-text",3,"matTooltip","click",4,"ngIf"],["class","normal-text",4,"ngIf"],[1,"hyperlink-text",3,"matTooltip","click"],[1,"normal-text"],["imgWidth","24px","imgHeight","24px",2,"padding-right","8px",3,"oid"],[1,"d-flex","flex-column",2,"width","70%"],[1,"employee-text",3,"matTooltip"],[1,"employee-aid",3,"matTooltip"],[1,"actions"],["class","ml-2",4,"ngIf"],["style","cursor: pointer",3,"innerHTML","matTooltip","click",4,"ngIf"],[2,"cursor","pointer",3,"innerHTML","matTooltip","click"],[1,"ml-2"],["role","status",1,"spinner-border","spinner-border-sm","spinner"],[1,"sr-only"],[2,"cursor","pointer",3,"ngStyle","matTooltip","click"],[1,"approve-reject-buttons"],[1,"reject-btn",3,"ngClass","click"],[1,"approve-btn",3,"ngClass","click"],["class","reject-more-vert",3,"matMenuTriggerFor","ngStyle",4,"ngIf"],["yPosition","below","xPosition","before",1,"custom-menu"],["rejectMenu","matMenu"],["mat-menu-item","",1,"menu-item",3,"ngStyle","click"],[1,"reject-more-vert",3,"matMenuTriggerFor","ngStyle"],[3,"peopleList","count"],[3,"customization","onApply"]],template:function(_,e){1&_&&(_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](0,"div",0),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](1,"div",1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](2,"div",2),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275listener"]("click",(function(){return e.openHomeScreen()})),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pipe"](3,"svgSecurityBypass"),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275element"](4,"div",3),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](5,"div",2),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275listener"]("click",(function(){return e.openApprovalsScreen()})),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pipe"](6,"svgSecurityBypass"),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementStart"](7,"div",4),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275listener"]("click",(function(){return e.openApprovalsScreen()})),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275text"](8),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](9,LandingPageComponent_ng_container_9_Template,6,1,"ng-container",5),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](10,LandingPageComponent_ng_container_10_Template,3,2,"ng-container",5),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275elementEnd"](),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275template"](11,LandingPageComponent_ng_template_11_Template,1,1,"ng-template",6,7,_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275templateRefExtractor"])),2&_&&(_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngStyle",_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pureFunction2"](11,_c15,e.homePageUiConfig["HEADER-BACKGROUND-001"],e.isLoading?"hidden":"")),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("innerHTML",_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pipeBind1"](3,7,e.homePageUiConfig["HEADER-ICON-001-01"]||""),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275sanitizeHtml"]),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](3),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("innerHTML",_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275pipeBind1"](6,9,e.homePageUiConfig["HEADER-ICON-002-02"]||""),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275sanitizeHtml"]),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](3),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275textInterpolate1"](" ",e.homePageUiConfig["HEADER-ICON-TEXT-001"]||""," "),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf",e.isLoading),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("ngIf",!e.isLoading),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275advance"](1),_angular_core__WEBPACK_IMPORTED_MODULE_6__["\u0275\u0275property"]("cdkConnectedOverlayOrigin",e.triggerColumnCustomization))},directives:[_angular_common__WEBPACK_IMPORTED_MODULE_18__.NgStyle,_angular_common__WEBPACK_IMPORTED_MODULE_18__.NgIf,_angular_cdk_overlay__WEBPACK_IMPORTED_MODULE_15__.a,_angular_common__WEBPACK_IMPORTED_MODULE_18__.NgForOf,_angular_common__WEBPACK_IMPORTED_MODULE_18__.NgClass,_angular_forms__WEBPACK_IMPORTED_MODULE_19__.e,_angular_forms__WEBPACK_IMPORTED_MODULE_19__.v,_angular_forms__WEBPACK_IMPORTED_MODULE_19__.y,_angular_cdk_overlay__WEBPACK_IMPORTED_MODULE_15__.b,_project_management_shared_lazy_loaded_components_filters_common_dialog_components_filter_display_filter_display_component__WEBPACK_IMPORTED_MODULE_20__.a,ngx_infinite_scroll__WEBPACK_IMPORTED_MODULE_21__.a,_angular_material_checkbox__WEBPACK_IMPORTED_MODULE_22__.a,_angular_material_tooltip__WEBPACK_IMPORTED_MODULE_23__.a,_app_shared_app_shared_components_user_image_user_image_component__WEBPACK_IMPORTED_MODULE_24__.a,_angular_material_icon__WEBPACK_IMPORTED_MODULE_25__.a,_angular_material_menu__WEBPACK_IMPORTED_MODULE_26__.g,_angular_material_menu__WEBPACK_IMPORTED_MODULE_26__.d,_angular_material_menu__WEBPACK_IMPORTED_MODULE_26__.f,_project_management_shared_lazy_loaded_components_people_icon_display_people_icon_display_component__WEBPACK_IMPORTED_MODULE_27__.a,_components_inbox_column_customization_inbox_column_customization_component__WEBPACK_IMPORTED_MODULE_28__.a],pipes:[_product_report_pipes_svgSecurityBypass_svg_security_bypass_pipe__WEBPACK_IMPORTED_MODULE_29__.a,_product_report_pipes_calculatePositionOfPinnedColumns_calculate_position_of_pinned_columns_pipe__WEBPACK_IMPORTED_MODULE_30__.a,_timesheet_v2_features_ts_v2_approval_pipes_hoursWorkedSplit_hours_worked_split_pipe__WEBPACK_IMPORTED_MODULE_31__.a,_project_management_shared_lazy_loaded_pipes_date_format_pipe__WEBPACK_IMPORTED_MODULE_32__.a,_product_report_pipes_currency_currency_pipe__WEBPACK_IMPORTED_MODULE_33__.a],styles:['.bg-container[_ngcontent-%COMP%]{background-color:#fff;height:var(--homePageDynamicHeight)}.header[_ngcontent-%COMP%]{display:flex;align-items:center;height:36px;padding:6px 0 6px 24px;gap:16px;background:linear-gradient(90deg,#fff,#fbdbe1)}.header[_ngcontent-%COMP%]   .bar-text[_ngcontent-%COMP%]{height:100%;width:.5px;background:#d4d6d8}.header[_ngcontent-%COMP%]   .approvals-text[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-size:12px;font-weight:400;color:var(--kebsPrimaryColor);cursor:pointer}.header[_ngcontent-%COMP%]   .svg[_ngcontent-%COMP%]{cursor:pointer}.empty-state[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;gap:12px;height:var(--homePageInboxDynamicHeight)}.empty-state[_ngcontent-%COMP%]   .empty-state-image[_ngcontent-%COMP%]{width:50%;height:50%}.empty-state[_ngcontent-%COMP%]   .image[_ngcontent-%COMP%]{height:60px;width:60px}.empty-state[_ngcontent-%COMP%]   .loading-wrapper[_ngcontent-%COMP%]{display:flex;vertical-align:center;justify-content:center;align-items:center}.empty-state[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]{color:rgba(0,0,0,.3);font-size:16px}.empty-state[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]:before{content:"Loading...";position:absolute;overflow:hidden;max-width:7em;white-space:nowrap;background:linear-gradient(270deg,var(--kebsPrimaryColor),var(--kebsPrimaryColor) 105.29%);background-clip:text;-webkit-background-clip:text;-webkit-text-fill-color:transparent;animation:loading 6s linear infinite}.empty-state[_ngcontent-%COMP%]   .empty-state-text[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-size:12px;font-weight:700;color:#45546e}.list-view-empty-state[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;gap:12px;height:var(--homePageInboxListDynamicHeight)}.list-view-empty-state[_ngcontent-%COMP%]   .empty-state-image[_ngcontent-%COMP%]{height:calc(var(--homePageInboxListDynamicHeight) - 200px);width:calc(var(--homePageInboxListDynamicHeight) - 200px +((var(--homePageInboxListDynamicHeight) - 200px) / 2))}.list-view-empty-state[_ngcontent-%COMP%]   .loading-image[_ngcontent-%COMP%]{height:60px;width:60px}.list-view-empty-state[_ngcontent-%COMP%]   .loading-wrapper[_ngcontent-%COMP%]{display:flex;vertical-align:center;justify-content:center;align-items:center}.list-view-empty-state[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]{color:rgba(0,0,0,.3);font-size:16px}.list-view-empty-state[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]:before{content:"Loading...";position:absolute;overflow:hidden;max-width:7em;white-space:nowrap;background:linear-gradient(270deg,var(--kebsPrimaryColor),var(--kebsPrimaryColor) 105.29%);background-clip:text;-webkit-background-clip:text;-webkit-text-fill-color:transparent;animation:loading 6s linear infinite}@keyframes loading{0%{max-width:0}}.list-view-empty-state[_ngcontent-%COMP%]   .empty-state-text[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-size:14px;font-weight:500;color:#1b2140}.list-view-empty-state[_ngcontent-%COMP%]   .empty-state-sub-text[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-size:14px;font-weight:400;color:#8b95a5}.inbox-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;padding:12px 24px;height:var(--homePageInboxDynamicHeight);gap:8px}.inbox-container[_ngcontent-%COMP%]   .inbox-container-header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;height:36px}.inbox-container[_ngcontent-%COMP%]   .inbox-container-header[_ngcontent-%COMP%]   .inbox-container-header-1[_ngcontent-%COMP%]{display:flex;align-items:center;border-bottom:.5px solid #e8e9ee;margin-left:24px}.inbox-container[_ngcontent-%COMP%]   .inbox-container-header[_ngcontent-%COMP%]   .inbox-container-header-1[_ngcontent-%COMP%]   .application[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-size:14px;font-weight:500;color:#8b95a5;padding-bottom:4px;margin:0 12px;cursor:pointer}.inbox-container[_ngcontent-%COMP%]   .inbox-container-header[_ngcontent-%COMP%]   .inbox-container-header-1[_ngcontent-%COMP%]   .selected-application[_ngcontent-%COMP%]{border-bottom:2px solid var(--kebsPrimaryColor);color:var(--kebsPrimaryColor)}.inbox-container[_ngcontent-%COMP%]   .inbox-container-header[_ngcontent-%COMP%]   .inbox-container-header-2[_ngcontent-%COMP%]{display:flex;align-items:center;gap:10px}.inbox-container[_ngcontent-%COMP%]   .inbox-container-header[_ngcontent-%COMP%]   .inbox-container-header-2[_ngcontent-%COMP%]   .unselected-button[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-size:12px;font-weight:500;color:#8b95a5;padding:6px 12px;background-color:#fff;border-radius:8px;cursor:pointer}.inbox-container[_ngcontent-%COMP%]   .inbox-container-header[_ngcontent-%COMP%]   .inbox-container-header-2[_ngcontent-%COMP%]   .selected-button[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-size:12px;font-weight:500;color:#fff;padding:6px 12px;background-color:var(--kebsPrimaryColor);border-radius:8px}.inbox-container[_ngcontent-%COMP%]   .inbox-container-sub-header[_ngcontent-%COMP%]{display:flex;align-items:center;height:36px}.inbox-container[_ngcontent-%COMP%]   .inbox-container-sub-header[_ngcontent-%COMP%]   .inbox-container-sub-header-1[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px}.inbox-container[_ngcontent-%COMP%]   .inbox-container-sub-header[_ngcontent-%COMP%]   .inbox-container-sub-header-1[_ngcontent-%COMP%]   .request-selected-text[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-size:14px;font-weight:700;color:#1b2140}.inbox-container[_ngcontent-%COMP%]   .inbox-container-sub-header[_ngcontent-%COMP%]   .inbox-container-sub-header-1[_ngcontent-%COMP%]   .approve-btn[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-size:14px;font-weight:700;color:#fff;border-radius:4px;padding:6px 16px;cursor:pointer;background-color:#52c41a}.inbox-container[_ngcontent-%COMP%]   .inbox-container-sub-header[_ngcontent-%COMP%]   .inbox-container-sub-header-2[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px}.inbox-container[_ngcontent-%COMP%]   .inbox-container-sub-header[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{cursor:pointer}.inbox-container[_ngcontent-%COMP%]   .inbox-container-sub-header[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]{display:flex;align-items:center;width:300px;height:36px;padding:0 12px;border:1px solid #dadce2;border-radius:8px;gap:8px;cursor:text}.inbox-container[_ngcontent-%COMP%]   .inbox-container-sub-header[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]{width:-webkit-fill-available}.inbox-container[_ngcontent-%COMP%]   .inbox-container-sub-header[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{height:100%;width:100%;font-family:var(--kebsFontFamily);font-size:12px;font-weight:400;color:#45546e;outline:none;border:none}.inbox-container[_ngcontent-%COMP%]   .inbox-container-sub-header[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{font-family:var(--kebsFontFamily);font-size:12px;font-weight:400;color:#b9c0ca}.inbox-container[_ngcontent-%COMP%]   .filter-display[_ngcontent-%COMP%]{width:-webkit-fill-available}.list-view[_ngcontent-%COMP%]{display:flex;flex-direction:column;overflow:auto;height:var(--homePageInboxListDynamicHeight)}.list-view[_ngcontent-%COMP%]   .header-sticky[_ngcontent-%COMP%]{position:sticky;top:0;z-index:99}.list-view[_ngcontent-%COMP%]   .header-row-main[_ngcontent-%COMP%]{flex-wrap:nowrap;float:left;min-width:-webkit-fill-available;-ms-overflow-style:none;scrollbar-width:none;overflow:unset}.list-view[_ngcontent-%COMP%]   .header-row-main[_ngcontent-%COMP%]   .header-row[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;min-height:32px;background:#fff;border-bottom:.5px solid #e8e9ee;padding-left:15px;background:#f2f3f6}.list-view[_ngcontent-%COMP%]   .header-row-main[_ngcontent-%COMP%]   .header-row[_ngcontent-%COMP%]   .header-row-content[_ngcontent-%COMP%]{display:flex;align-items:center;width:98%;gap:8px}.list-view[_ngcontent-%COMP%]   .header-row-main[_ngcontent-%COMP%]   .header-row[_ngcontent-%COMP%]   .list-title[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-size:12px;font-weight:400;color:#5f6c81;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.list-view[_ngcontent-%COMP%]   .header-row-main[_ngcontent-%COMP%]   .header-row[_ngcontent-%COMP%]   .svg[_ngcontent-%COMP%]{cursor:pointer}.list-view[_ngcontent-%COMP%]   .header-row-main[_ngcontent-%COMP%]   .header-row[_ngcontent-%COMP%]   .vertical-divider-wrapper[_ngcontent-%COMP%]{width:2%;cursor:ew-resize}.list-view[_ngcontent-%COMP%]   .header-row-main[_ngcontent-%COMP%]   .header-row[_ngcontent-%COMP%]   .vertical-divider[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:end;color:#dadce2;border-right-width:2px;height:20px}.list-view[_ngcontent-%COMP%]   .header-row-main[_ngcontent-%COMP%]   .header-row-left-pin[_ngcontent-%COMP%], .list-view[_ngcontent-%COMP%]   .header-row-main[_ngcontent-%COMP%]   .header-row-right-pin[_ngcontent-%COMP%]{position:sticky;z-index:10}.list-view[_ngcontent-%COMP%]   .content-sticky[_ngcontent-%COMP%]{z-index:1;position:sticky}.list-view[_ngcontent-%COMP%]   .content-row-main[_ngcontent-%COMP%]{flex-wrap:nowrap;float:left;min-width:-webkit-fill-available;transition:all .25s linear;height:auto}.list-view[_ngcontent-%COMP%]   .content-row-main[_ngcontent-%COMP%]   .content-row[_ngcontent-%COMP%]{display:flex;align-items:center;min-height:48px;border-bottom:.5px solid #d4d6d8;padding-left:15px;background-color:#fff}.list-view[_ngcontent-%COMP%]   .content-row-main[_ngcontent-%COMP%]   .content-row[_ngcontent-%COMP%]   .normal-text[_ngcontent-%COMP%]{font-weight:400}.list-view[_ngcontent-%COMP%]   .content-row-main[_ngcontent-%COMP%]   .content-row[_ngcontent-%COMP%]   .employee-text[_ngcontent-%COMP%], .list-view[_ngcontent-%COMP%]   .content-row-main[_ngcontent-%COMP%]   .content-row[_ngcontent-%COMP%]   .normal-text[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-size:12px;color:#272a47;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.list-view[_ngcontent-%COMP%]   .content-row-main[_ngcontent-%COMP%]   .content-row[_ngcontent-%COMP%]   .employee-text[_ngcontent-%COMP%]{font-weight:500}.list-view[_ngcontent-%COMP%]   .content-row-main[_ngcontent-%COMP%]   .content-row[_ngcontent-%COMP%]   .employee-aid[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-size:12px;font-weight:400;color:#7d838b;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.list-view[_ngcontent-%COMP%]   .content-row-main[_ngcontent-%COMP%]   .content-row[_ngcontent-%COMP%]   .hyperlink-text[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-size:14px;font-weight:400;color:#1890ff;text-decoration:underline;cursor:pointer;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.list-view[_ngcontent-%COMP%]   .content-row-main[_ngcontent-%COMP%]   .content-row[_ngcontent-%COMP%]   .chip[_ngcontent-%COMP%]{border-radius:2px;text-align:center;padding:4px 12px;font-family:var(--kebsFontFamily);font-size:12px;font-weight:500;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.list-view[_ngcontent-%COMP%]   .content-row-main[_ngcontent-%COMP%]   .content-row[_ngcontent-%COMP%]   .actions[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.list-view[_ngcontent-%COMP%]   .content-row-main[_ngcontent-%COMP%]   .content-row[_ngcontent-%COMP%]   .actions[_ngcontent-%COMP%]   .svg[_ngcontent-%COMP%]{cursor:pointer}.list-view[_ngcontent-%COMP%]   .content-row-main[_ngcontent-%COMP%]   .content-row[_ngcontent-%COMP%]   .approve-reject-buttons[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}.list-view[_ngcontent-%COMP%]   .content-row-main[_ngcontent-%COMP%]   .content-row[_ngcontent-%COMP%]   .approve-reject-buttons[_ngcontent-%COMP%]   .reject-btn[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-size:12px;font-weight:400;color:#45546e;border:1px solid #45546e;border-radius:4px;padding:3px 7px;cursor:pointer}.list-view[_ngcontent-%COMP%]   .content-row-main[_ngcontent-%COMP%]   .content-row[_ngcontent-%COMP%]   .approve-reject-buttons[_ngcontent-%COMP%]   .approve-btn[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-size:12px;font-weight:400;color:#fff;border-radius:4px;padding:4px 8px;cursor:pointer;background-color:#52c41a}.list-view[_ngcontent-%COMP%]   .content-row-main[_ngcontent-%COMP%]   .content-row[_ngcontent-%COMP%]   .approve-reject-buttons[_ngcontent-%COMP%]   .disable-btn[_ngcontent-%COMP%]{background-color:#e8e9ee;color:#b9c0ca;padding:4px 8px;border:none;pointer-events:none}.list-view[_ngcontent-%COMP%]   .content-row-main[_ngcontent-%COMP%]   .content-row-left-pin[_ngcontent-%COMP%], .list-view[_ngcontent-%COMP%]   .content-row-main[_ngcontent-%COMP%]   .content-row-right-pin[_ngcontent-%COMP%]{position:sticky;z-index:3}.spinner[_ngcontent-%COMP%]{color:var(--kebsPrimaryColor);width:20px;height:20px;font-weight:100}.reject-more-vert[_ngcontent-%COMP%]{color:#45546e;cursor:pointer}.reject-menu[_ngcontent-%COMP%]{overflow:hidden;padding:0 16px}.reject-menu[_ngcontent-%COMP%]   .reject-menu-item[_ngcontent-%COMP%]{cursor:pointer;font-size:11px;color:#6e7b8f;margin:0}.menu-item[_ngcontent-%COMP%], .reject-menu[_ngcontent-%COMP%]   .reject-menu-item[_ngcontent-%COMP%]{font-family:var(--kebsFontFamily);font-weight:400}.menu-item[_ngcontent-%COMP%]{font-size:12px;color:#526179;border-bottom:1px solid #e8e9ee;height:48px;display:flex;align-items:center}.checkbox[_ngcontent-%COMP%]{width:16px;height:16px;margin-right:8px}.checkbox[_ngcontent-%COMP%]     .mat-checkbox-checked.mat-accent .mat-checkbox-background, .checkbox[_ngcontent-%COMP%]     .mat-checkbox-indeterminate .mat-checkbox-background{background-color:var(--kebsPrimaryColor)!important}.checkbox[_ngcontent-%COMP%]     .mat-checkbox-inner-container{width:16px!important;height:16px!important}.checkbox[_ngcontent-%COMP%]     .mat-checkbox-frame{border-color:#dadce2!important}']}),LandingPageComponent})()},yuIm:function(_,e,n){"use strict";n.r(e),n.d(e,"roleAccessList",(function(){return t})),n.d(e,"currentJobRoleAccessList",(function(){return a})),n.d(e,"is_rds_peak",(function(){return r})),n.d(e,"moduleId",(function(){return i})),n.d(e,"subModuleId",(function(){return o})),n.d(e,"sectionId",(function(){return c})),n.d(e,"subSectionId",(function(){return l})),n.d(e,"getRoleAccessList",(function(){return s})),n.d(e,"setRoleAccessList",(function(){return E})),n.d(e,"getCurrentJobRoleAccessList",(function(){return g})),n.d(e,"setCurrentJobRoleAccessList",(function(){return p})),n.d(e,"checkAccessForGeneralRole",(function(){return u})),n.d(e,"checkAccessForJobRole",(function(){return P})),n.d(e,"changeRDSvalue",(function(){return d}));let t=[],a=[],r=!1;const i={jobs:1,candidates:2,reports:3,dashboard:4,settings:5,campusJobs:6,onboarding:8},o={manageJob:1,newJob:2,draftJob:3,manageCandidate:4,talentPipeline:5,templateSettings:10,collegeSettings:17,manageCampusJob:15,campusTalentPipeline:16,activityLogs:20,userSettings:6,profileSettings:7,notificationSettings:8,emailSettings:11,companySettings:12,vendorSettings:13,rolesAndPermissions:9,onboardingTaskSettings:28,onboardingChecklistSettings:29,onboardingChecklists:30,onboardingCandidates:31},c={manageJobDetailView:17,manageCampusJobDetailView:18,manageJobDetailViewOverview:1,manageJobDetailViewCandidate:2,manageJobDetailViewScorecard:3,manageJobDetailViewAdverts:4,manageJobDetailViewInsights:5,manageJobDetailViewEmailLogs:15,manageJobDetailViewHistoryLogs:19,manageCampusJobDetailViewOverview:6,manageCampusJobDetailViewCandidate:7,manageCampusJobDetailViewScorecard:8,manageCampusJobDetailViewAdverts:9,manageCampusJobDetailViewInsights:10,manageCampusJobDetailViewEmailLogs:16,manageCampusJobDetailViewHistoryLogs:20,manageJobCandidateDetailView:11,allCandidatesCandidateDetailView:12,talentPipelineCandidateDetailView:13,manageCampusJobCandidateDetailView:14,userSettingsDetailView:21,generalDetailView:22,themeSettings:23,emailSync:24,emailSignature:25,blockemail:26,calendarIntegration:27,organizationSettings:28,vendorDetailView:29,vendorAssignUser:30,campusTalentPipelineCandidateDetailView:31,onboardingCandidateDetailView:32},l={manageJobCandidateDetailViewDetails:1,manageJobCandidateDetailViewResume:2,manageJobCandidateDetailViewScorecard:3,manageJobCandidateDetailViewComments:4,manageJobCandidateDetailViewInterview:5,manageJobCandidateDetailViewDocuments:6,manageJobCandidateDetailViewHistory:7,manageJobCandidateDetailViewApplications:8,manageJobCandidateDetailViewInterviewScheduling:9,manageJobCandidateDetailViewSendOffer:10,manageJobCandidateDetailViewCustomQuestions:49,allCandidatesDetailViewDetails:37,allCandidatesDetailViewResume:11,allCandidatesDetailViewScorecard:12,allCandidatesDetailViewComments:13,allCandidatesDetailViewInterview:14,allCandidatesDetailViewDocuments:15,allCandidatesDetailViewHistory:16,allCandidatesDetailViewApplications:17,allCandidatesDetailViewCertificates:18,talentPipelineCandidatesDetailViewDetails:38,talentPipelineCandidatesDetailViewResume:19,talentPipelineCandidatesDetailViewScorecard:20,talentPipelineCandidatesDetailViewComments:21,talentPipelineCandidatesDetailViewInterview:22,talentPipelineCandidatesDetailViewDocuments:23,talentPipelineCandidatesDetailViewHistory:24,talentPipelineCandidatesDetailViewApplications:25,talentPipelineCandidatesDetailViewCertificates:26,manageCampusJobCandidateDetailViewDetails:27,manageCampusJobCandidateDetailViewResume:28,manageCampusJobCandidateDetailViewScorecard:29,manageCampusJobCandidateDetailViewComments:30,manageCampusJobCandidateDetailViewInterview:31,manageCampusJobCandidateDetailViewDocuments:32,manageCampusJobCandidateDetailViewHistory:33,manageCampusJobCandidateDetailViewApplications:34,manageCampusJobCandidateDetailViewCustomQuestions:50,manageCampusJobCandidateDetailViewInterviewScheduling:35,manageCampusJobCandidateDetailViewSendOffer:36,manageJobCandidateDetailViewOfferTrack:63,manageCampusJobCandidateDetailViewOfferTrack:64,allCandidatesDetailViewOfferTrack:65,talentPipelineCandidatesDetailViewOfferTrack:66,campusTalentPipelineCandidatesDetailViewDetails:67,campusTalentPipelineCandidatesDetailViewResume:68,campusTalentPipelineCandidatesDetailViewScorecard:69,campusTalentPipelineCandidatesDetailViewComments:70,campusTalentPipelineCandidatesDetailViewInterview:71,campusTalentPipelineCandidatesDetailViewDocuments:72,campusTalentPipelineCandidatesDetailViewHistory:73,campusTalentPipelineCandidatesDetailViewApplications:74,campusTalentPipelineCandidatesDetailViewCertificates:75,campusTalentPipelineCandidatesDetailViewOfferTrack:76,onboardingCandidateDetailViewChecklists:77,onboardingCandidateDetailViewDocuments:78};function s(){return t}function E(_){t=_}function g(){return a}function p(_){a=_}function u(_=0,e=0,n=0,a=0,r=""){if(!r||""==r)return!1;let i={module_id:_,sub_module_id:e,section_id:n,sub_section_id:a};"V"==r&&(i.view_permission=1),"C"==r&&(i.create_permission=1),"E"==r&&(i.edit_permission=1),"DE"==r&&(i.delete_permission=1),"DO"==r&&(i.download_permission=1),"U"==r&&(i.upload_permission=1),"B"==r&&(i.bulk_operation=1);const o=Object.keys(i);return t.find(_=>o.every(e=>_[e]===i[e]))}function P(_=0,e=0,n=0,t=0,r=""){if(!r||""==r)return!1;let i={module_id:_,sub_module_id:e,section_id:n,sub_section_id:t};"V"==r&&(i.view_permission=1),"C"==r&&(i.create_permission=1),"E"==r&&(i.edit_permission=1),"DE"==r&&(i.delete_permission=1),"DO"==r&&(i.download_permission=1),"U"==r&&(i.upload_permission=1),"B"==r&&(i.bulk_operation=1);const o=Object.keys(i);return a.find(_=>o.every(e=>_[e]===i[e]))}function d(_){return r=_,r}},yuNy:function(_,e,n){"use strict";n.d(e,"a",(function(){return r}));var t=n("fXoL"),a=n("jhN1");let r=(()=>{class _{constructor(_){this._sanitizer=_}transform(_){return this._sanitizer.bypassSecurityTrustHtml(_)}}return _.\u0275fac=function(e){return new(e||_)(t["\u0275\u0275directiveInject"](a.c))},_.\u0275pipe=t["\u0275\u0275definePipe"]({name:"svgSecurityBypass",type:_,pure:!0}),_})()}}]);