(window.webpackJsonp=window.webpackJsonp||[]).push([[834],{"Xn/N":function(e,t,i){"use strict";i.r(t),i.d(t,"OverviewModule",(function(){return k}));var r=i("ofXK"),a=i("NFeN"),n=i("bTqV"),s=i("TU8p"),o=i("Xa2L"),l=i("3Pt+"),d=i("bSwM"),h=i("1jcm"),c=i("Qu3c"),p=i("tyNb"),b=i("mrSG"),g=i("xG9w"),m=i("1G5W"),S=i("XNiG"),f=i("R0Ic"),v=i("fXoL"),u=i("flaP"),y=i("BVzC"),C=i("0IaG"),P=i("tk/3");let M=(()=>{class e{constructor(e){this.http=e}roundToTwo(e){return Number(e).toFixed(2)}getdbData(e){return this.http.post("/api/appraisal/employeeAppraisal/getOrgAndScoreDetailForDashBoard",e)}getdbTypeData(e){return this.http.post("/api/appraisal/configuration/getOrgForDashBoardByType",e)}}return e.\u0275fac=function(t){return new(t||e)(v["\u0275\u0275inject"](P.c))},e.\u0275prov=v["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var _=i("LcQX");function x(e,t){if(1&e){const e=v["\u0275\u0275getCurrentView"]();v["\u0275\u0275elementStart"](0,"div",11),v["\u0275\u0275elementStart"](1,"mat-checkbox",12),v["\u0275\u0275listener"]("change",(function(){v["\u0275\u0275restoreView"](e);const i=t.$implicit;return v["\u0275\u0275nextContext"]().dbRegionTypeFilterCbClick(i)})),v["\u0275\u0275text"](2),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;v["\u0275\u0275property"]("matTooltip",e.type),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("checked",e.isActive),v["\u0275\u0275advance"](1),v["\u0275\u0275textInterpolate"](e.type)}}function w(e,t){if(1&e){const e=v["\u0275\u0275getCurrentView"]();v["\u0275\u0275elementStart"](0,"div",11),v["\u0275\u0275elementStart"](1,"mat-checkbox",13),v["\u0275\u0275listener"]("change",(function(){v["\u0275\u0275restoreView"](e);const i=t.$implicit;return v["\u0275\u0275nextContext"]().dbfiltercheck(i)})),v["\u0275\u0275text"](2),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;v["\u0275\u0275property"]("matTooltip",e.fy),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("checked",e.isActive)("disabled",e.isDisabled),v["\u0275\u0275advance"](1),v["\u0275\u0275textInterpolate1"](" ",e.fy," ")}}function L(e,t){1&e&&(v["\u0275\u0275elementStart"](0,"div",14),v["\u0275\u0275element"](1,"mat-spinner",15),v["\u0275\u0275elementEnd"]())}const O=[{path:"",component:(()=>{class e{constructor(e,t,r,a,n,s){this.rolesService=e,this._ErrorService=t,this.router=r,this._dialog=a,this.dashboardService=n,this.utilityService=s,this._onDestroy=new S.b,this._onFYFilterCalled=new S.b,this.default_region="Group",this.default_fy="",this.dbHasNonDefaultFilters=!1,this.dbFilterPanelVisible=!1,this.dbIsGroupChartSet=!0,this.dbFYFilterCbs=[],this.orgType=[],this.newDbPMSLineChartData=[],this.data=[],this.typeData=[],this.type_org_data=[],this.finYear="2021",this.dbPMSLineChartSeriesIndexArray=[],this.IsDbPMSLoading=!0,this.dbFirstCallPMS=!0,this.startYear=0,this.endYear=0,this.orgName="Delivery",this.currentStartYear=0,this.isPMSDashBoardChartVisible=!0,this.evn="",this.openDetailView=e=>Object(b.c)(this,void 0,void 0,(function*(){const{OverviewDetailComponent:t}=yield i.e(833).then(i.bind(null,"BRBV"));this._dialog.open(t,{width:"60%",height:"80%",autoFocus:!1,maxWidth:"90vw",data:{org_id:e[0].practise_org_id,org_name:e[0].practise_org_name,org_avg_appraisal_score:e[0].avg,org_employee_details:e[0].employee}})}))}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete(),this._onFYFilterCalled.complete(),this.dbPMSLineChart&&this.dbPMSLineChart.dispose()}onFYFilterCalled(){this._onFYFilterCalled.next()}ngOnInit(){Promise.all([Promise.all([i.e(36),i.e(38),i.e(42)]).then(i.bind(null,"cclQ")),Promise.all([i.e(36),i.e(38),i.e(46)]).then(i.bind(null,"xJfa")),Promise.all([i.e(36),i.e(209)]).then(i.bind(null,"WlQZ")),i.e(1002).then(i.bind(null,"VrR4"))]).then(e=>Object(b.c)(this,void 0,void 0,(function*(){this.am4core=e[0],this.am4charts=e[1];const t=e[3].am4themes_custom;this.am4core.useTheme(e[2].default),this.am4core.useTheme(t),this.calculateYears(),this.getOrgTypes(),this.getdbtype("Delivery")}))).catch(e=>{console.error("Dashboard Amcharts Module Loading Error: ",e)})}getdbtype(e){this.dashboardService.getdbTypeData({configuration_name:"dashboard_data",type:e}).pipe(Object(m.a)(this._onDestroy)).subscribe(e=>{console.log(e),this.typeData=e.data[0].org_details,console.log(this.typeData),this.type_org_data=[];for(let t=0;t<this.typeData.length;t++)this.type_org_data.push(this.typeData[t].org_code);this.getDbdata(this.finYear)},e=>{this.IsDbPMSLoading=!1,this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}getDbdata(e){this.dashboardService.getdbData({appraisal_year:e,practise_org_id_arr:this.type_org_data,org_details:this.typeData}).pipe(Object(m.a)(this._onDestroy)).subscribe(e=>{this.data=e.data,this.loadPMSLineChartSeries()},e=>{this.IsDbPMSLoading=!1,this.data=[],this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage)})}dbfiltercheck(e){return Object(b.c)(this,void 0,void 0,(function*(){console.log(e),e.isActive=!e.isActive;var t=g.where(this.dbFYFilterCbs,{isActive:!0});for(let i=0;i<t.length;i++)t[i].fy!=e.fy&&(t[i].isActive=!1);console.log(t),this.finYear=e.fy,console.log(String(this.finYear)),this.getDbdata(String(this.finYear))}))}calculateYears(){var e,t,i,r,a,n,s=new Date,o=s.getFullYear();s.getMonth()>2?(i=o+1,e=t=o,n=o,r=a=o-1):(i=o,e=t=o-1,n=o-1,r=a=o-2),this.dbFYFilterCbs.push({fy:e,year_1:t,year_2:i,isActive:!0},{fy:r,year_1:a,year_2:n,isActive:!1}),this.default_fy=e,this.startYear=t,this.endYear=i,this.currentStartYear=t}getOrgTypes(){this.orgType.push({type:"Delivery",isActive:!0},{type:"Support",isActive:!1})}dbRegionTypeFilterCbClick(e){console.log(e),e.isActive=!e.isActive;var t=g.where(this.orgType,{isActive:!0});this.orgName=e.type;for(let i=0;i<t.length;i++)t[i].type!=e.type&&(t[i].isActive=!1);console.log(t),this.getdbtype(e.type)}loadPMSLineChartSeries(){if(this.dbPMSLineChart&&this.dbPMSLineChart.dispose(),this.isPMSDashBoardChartVisible){this.dbPMSLineChart=this.am4core.create("dbPMSLineChart",this.am4charts.XYChart),this.dbPMSLineChart.data=this.data,this.dbPMSLineChart.yAxes.push(new this.am4charts.ValueAxis).title.text="Average PMS Score (%)";let e=this.dbPMSLineChart.xAxes.push(new this.am4charts.CategoryAxis);e.dataFields.category="practise_org_name",e.title.text="Organization",e.renderer.minGridDistance=60,this.dbPMSLineChart.cursor=new this.am4charts.XYCursor,this.dbPMSLineChart.cursor.maxTooltipDistance=-1,this.dbPMSLineChart.legend=new this.am4charts.Legend,this.dbPMSLineChart.legend.labels.template.truncate=!1,this.dbPMSLineChart.legend.labels.template.wrap=!0,this.dbPMSLineChartSeries=this.dbPMSLineChart.series.push(new this.am4charts.LineSeries),this.dbPMSLineChartSeries.name=this.orgName,this.dbPMSLineChartSeries.dataFields.categoryX="practise_org_name",this.dbPMSLineChartSeries.dataFields.valueY="avg",this.dbPMSLineChartSeries.fillOpacity=.2,this.dbPMSLineChartSeries.strokeWidth=2,this.dbPMSLineChartSeries.stroke=this.am4core.color("#ff0000"),this.dbPMSLineChartSeries.fill=this.am4core.color("#ff0000"),this.dbPMSLineChartSeries.propertyFields.strokeDasharray="dashLength",this.dbPMSLineChartSeries.tooltipText="[bold]Org code : {practise_org_id}[/]\n      [bold]Org Name : {categoryX}[/]\n      [bold]Average PMS Score : {avg}\n      ",this.dbPMSLineChartSeries.tooltip.background.cornerRadius=20,this.dbPMSLineChartSeries.tooltip.background.strokeOpacity=0,this.dbPMSLineChartSeries.tooltip.pointerOrientation="vertical",this.dbPMSLineChartSeries.tooltip.label.minWidth=40,this.dbPMSLineChartSeries.tooltip.label.minHeight=40,this.dbPMSLineChartSeries.tooltip.label.textAlign="left",this.dbPMSLineChartSeries.minBulletDistance=15;let t=this.dbPMSLineChartSeries.bullets.push(new this.am4charts.CircleBullet);t.circle.strokeWidth=2,t.circle.radius=4,t.circle.fill=this.am4core.color("#cf0001"),t.states.create("hover").properties.scale=1.3,t.events.on("hit",e=>{console.log(e.target._dataItem.categories.categoryX),console.log(this.dbPMSLineChart.data),this.evn=e.target._dataItem.categories.categoryX,console.log(this.evn);let t=g.where(this.dbPMSLineChart.data,{practise_org_name:this.evn});console.log(t),this.openDetailView(t)});let i=e.renderer.labels.template;i.truncate=!0,i.maxWidth=200,i.tooltipText="{practise_org_name}",e.events.on("sizechanged",(function(e){let t=e.target;t.pixelWidth/(t.endIndex-t.startIndex)<t.renderer.labels.template.maxWidth?(t.renderer.labels.template.rotation=-45,t.renderer.labels.template.horizontalCenter="right",t.renderer.labels.template.verticalCenter="middle"):(t.renderer.labels.template.rotation=0,t.renderer.labels.template.horizontalCenter="middle",t.renderer.labels.template.verticalCenter="top")})),this.IsDbPMSLoading=!1}}dbFilterPanelClick(){this.dbFilterPanelVisible=!this.dbFilterPanelVisible}dbFilterPanelClearFilterClick(){return Object(b.c)(this,void 0,void 0,(function*(){this.dbIsGroupChartSet=!0,this.dbFilterPanelClick()}))}getLength(e){return e.length}}return e.\u0275fac=function(t){return new(t||e)(v["\u0275\u0275directiveInject"](u.a),v["\u0275\u0275directiveInject"](y.a),v["\u0275\u0275directiveInject"](p.g),v["\u0275\u0275directiveInject"](C.b),v["\u0275\u0275directiveInject"](M),v["\u0275\u0275directiveInject"](_.a))},e.\u0275cmp=v["\u0275\u0275defineComponent"]({type:e,selectors:[["app-overview"]],decls:17,vars:5,consts:[[1,"col-md-12","dashboard-css","p-2"],[1,"d-flex",2,"margin-bottom","1%"],[1,"col-md-3","p-1"],[1,"filter-text","mb-1"],[1,"row"],["class","col-12","style","font-size: 14px !important",3,"matTooltip",4,"ngFor","ngForOf"],[1,"col-md-2","p-1"],[1,"card","mt-4",3,"hidden"],[1,"card-body",2,"padding","10px 13px !important"],["style","height: 60vh !important; display: flex; align-items: center; justify-content: center;",4,"ngIf"],["id","dbPMSLineChart",1,"db-chart",2,"margin-bottom","1%",3,"hidden"],[1,"col-12",2,"font-size","14px !important",3,"matTooltip"],[3,"checked","change"],[3,"checked","disabled","change"],[2,"height","60vh !important","display","flex","align-items","center","justify-content","center"],["color","primary","diameter","40",1,"mx-auto"]],template:function(e,t){1&e&&(v["\u0275\u0275elementStart"](0,"div",0),v["\u0275\u0275element"](1,"div"),v["\u0275\u0275elementStart"](2,"div",1),v["\u0275\u0275elementStart"](3,"div",2),v["\u0275\u0275elementStart"](4,"p",3),v["\u0275\u0275text"](5,"Org Type"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](6,"div",4),v["\u0275\u0275template"](7,x,3,3,"div",5),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](8,"div",6),v["\u0275\u0275elementStart"](9,"p",3),v["\u0275\u0275text"](10,"Financial Year"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](11,"div",4),v["\u0275\u0275template"](12,w,3,4,"div",5),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](13,"div",7),v["\u0275\u0275elementStart"](14,"div",8),v["\u0275\u0275template"](15,L,2,0,"div",9),v["\u0275\u0275element"](16,"div",10),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"]()),2&e&&(v["\u0275\u0275advance"](7),v["\u0275\u0275property"]("ngForOf",t.orgType),v["\u0275\u0275advance"](5),v["\u0275\u0275property"]("ngForOf",t.dbFYFilterCbs),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("hidden",!t.isPMSDashBoardChartVisible),v["\u0275\u0275advance"](2),v["\u0275\u0275property"]("ngIf",t.IsDbPMSLoading),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("hidden",t.IsDbPMSLoading))},directives:[r.NgForOf,r.NgIf,c.a,d.a,o.c],styles:[".dashboard-css[_ngcontent-%COMP%]     .mat-checkbox-layout{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.dashboard-css[_ngcontent-%COMP%]   .left-right-div[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;width:100%;padding:0}.dashboard-css[_ngcontent-%COMP%]   .db-chart[_ngcontent-%COMP%]{width:100%!important;height:60vh!important}.dashboard-css[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-weight:500;color:#cf0001;font-size:14px;margin-bottom:25px;padding-left:10px}.dashboard-css[_ngcontent-%COMP%]   .filter-text[_ngcontent-%COMP%]{font-weight:500;color:#615e5e;text-align:left;font-size:13px}.dashboard-css[_ngcontent-%COMP%]   .filter-content[_ngcontent-%COMP%], .dashboard-css[_ngcontent-%COMP%]   .mat-list-option[_ngcontent-%COMP%]{font-size:13px!important;height:24px!important;line-height:24px!important}.dashboard-css[_ngcontent-%COMP%]   .col-3x[_ngcontent-%COMP%]{flex:0 0 33%;max-width:33%;overflow:hidden!important;white-space:nowrap!important;text-overflow:ellipsis!important}"],data:{animation:[Object(f.o)("slideInOut",[Object(f.l)("in",Object(f.m)({height:"*",overflow:"hidden"})),Object(f.l)("out",Object(f.m)({height:0,overflow:"hidden"})),Object(f.n)("* => in",[Object(f.m)({height:0}),Object(f.e)(250,Object(f.m)({height:"*"}))]),Object(f.n)("in=> *",[Object(f.m)({height:"*"}),Object(f.e)(250,Object(f.m)({height:0}))])])]}}),e})()}];let D=(()=>{class e{}return e.\u0275mod=v["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=v["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[p.k.forChild(O)],p.k]}),e})();var F=i("5RNC"),j=i("f0Cb"),I=i("STbY"),A=i("Xi0T");let k=(()=>{class e{}return e.\u0275mod=v["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=v["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[r.CommonModule,D,a.b,n.b,s.b,o.b,l.p,d.b,h.b,c.b,C.g,j.b,F.b,I.e,A.a]]}),e})()}}]);