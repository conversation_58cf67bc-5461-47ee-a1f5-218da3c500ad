(window.webpackJsonp=window.webpackJsonp||[]).push([[950,535,631,634,858],{NJ67:function(e,t,a){"use strict";a.d(t,"a",(function(){return n}));class n{constructor(){this.disabled=!1}onChange(e){}onTouched(e){}writeValue(e){this.value=e}registerOnChange(e){this.onChange=e}registerOnTouched(e){this.onTouched=e}setDisabledState(e){this.disabled=e}}},"TmG/":function(e,t,a){"use strict";a.d(t,"a",(function(){return y}));var n=a("fXoL"),l=a("3Pt+"),r=a("jtHE"),i=a("XNiG"),o=a("NJ67"),s=a("1G5W"),d=a("kmnG"),c=a("ofXK"),m=a("d3UM"),v=a("FKr1"),u=a("WJ5W"),p=a("Qu3c");function h(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"mat-label"),n["\u0275\u0275text"](1),n["\u0275\u0275elementEnd"]()),2&e){const e=n["\u0275\u0275nextContext"]();n["\u0275\u0275advance"](1),n["\u0275\u0275textInterpolate"](e.placeholder)}}function f(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"mat-option",7),n["\u0275\u0275text"](1),n["\u0275\u0275elementEnd"]()),2&e){const e=n["\u0275\u0275nextContext"]();n["\u0275\u0275property"]("value",null),n["\u0275\u0275advance"](1),n["\u0275\u0275textInterpolate"](e.disableNone?"Select One":"None")}}function g(e,t){if(1&e){const e=n["\u0275\u0275getCurrentView"]();n["\u0275\u0275elementStart"](0,"mat-option",8),n["\u0275\u0275listener"]("click",(function(){n["\u0275\u0275restoreView"](e);const a=t.$implicit;return n["\u0275\u0275nextContext"]().emitChanges(a)})),n["\u0275\u0275text"](1),n["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;n["\u0275\u0275property"]("value",e.id||e._id)("matTooltip",e.name),n["\u0275\u0275advance"](1),n["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let y=(()=>{class e extends o.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new l.j,this.fieldFilterCtrl=new l.j,this.list=[],this.required=!1,this.disableNone=!1,this.valueChange=new n.EventEmitter,this.disabled=!1,this.hasNoneOption=!0,this.filteredList=new r.a,this.change=new n.EventEmitter,this.hideMatLabel=!1,this._onDestroy=new i.b,this.emitChanges=e=>{this.change.emit(e)}}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(s.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(s.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}}return e.\u0275fac=function(t){return new(t||e)(n["\u0275\u0275directiveInject"](n.Renderer2))},e.\u0275cmp=n["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search"]],inputs:{list:"list",placeholder:"placeholder",required:"required",disableNone:"disableNone",disabled:"disabled",hasNoneOption:"hasNoneOption",hideMatLabel:"hideMatLabel"},outputs:{valueChange:"valueChange",change:"change"},features:[n["\u0275\u0275ProvidersFeature"]([{provide:l.t,useExisting:Object(n.forwardRef)(()=>e),multi:!0}]),n["\u0275\u0275InheritDefinitionFeature"],n["\u0275\u0275NgOnChangesFeature"]],decls:9,vars:11,consts:[["appearance","outline"],[4,"ngIf"],[3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value",4,"ngIf"],[3,"value","matTooltip","click",4,"ngFor","ngForOf"],[3,"value"],[3,"value","matTooltip","click"]],template:function(e,t){1&e&&(n["\u0275\u0275elementStart"](0,"mat-form-field",0),n["\u0275\u0275template"](1,h,2,1,"mat-label",1),n["\u0275\u0275elementStart"](2,"mat-select",2,3),n["\u0275\u0275elementStart"](4,"mat-option"),n["\u0275\u0275element"](5,"ngx-mat-select-search",4),n["\u0275\u0275elementEnd"](),n["\u0275\u0275template"](6,f,2,2,"mat-option",5),n["\u0275\u0275template"](7,g,2,3,"mat-option",6),n["\u0275\u0275pipe"](8,"async"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e&&(n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf",!t.hideMatLabel),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),n["\u0275\u0275advance"](3),n["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf",t.hasNoneOption),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngForOf",n["\u0275\u0275pipeBind1"](8,9,t.filteredList)))},directives:[d.c,c.NgIf,m.c,l.v,l.k,l.F,v.p,u.a,c.NgForOf,d.g,p.a],pipes:[c.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%}"]}),e})()},tzTD:function(e,t,a){"use strict";a.r(t),a.d(t,"LeaveBalanceDetailsComponent",(function(){return M})),a.d(t,"LeaveBalanceDetailsModule",(function(){return _}));var n=a("mrSG"),l=a("33Jv"),r=a("xG9w"),i=a("wd/R"),o=a("0IaG"),s=a("ofXK"),d=a("Xi0T"),c=a("kmnG"),m=a("qFsG"),v=a("3Pt+"),u=a("iadO"),p=a("FKr1"),h=a("NFeN"),f=a("bTqV"),g=a("Xa2L"),y=a("Qu3c"),b=a("1jcm"),D=a("1yaQ"),C=a("QyBZ"),E=a("fXoL"),S=a("jAlA"),B=a("1A3m"),w=a("TmG/"),x=a("fbGU");function L(e,t){1&e&&(E["\u0275\u0275elementContainerStart"](0),E["\u0275\u0275elementStart"](1,"div",2),E["\u0275\u0275element"](2,"mat-spinner",3),E["\u0275\u0275elementEnd"](),E["\u0275\u0275elementContainerEnd"]())}function O(e,t){1&e&&(E["\u0275\u0275elementStart"](0,"div",30),E["\u0275\u0275element"](1,"mat-divider"),E["\u0275\u0275elementEnd"]())}function F(e,t){if(1&e){const e=E["\u0275\u0275getCurrentView"]();E["\u0275\u0275elementStart"](0,"div",13),E["\u0275\u0275elementStart"](1,"div",14),E["\u0275\u0275elementStart"](2,"div",15),E["\u0275\u0275elementStart"](3,"div",16),E["\u0275\u0275elementStart"](4,"div",17),E["\u0275\u0275text"](5," Start Date "),E["\u0275\u0275elementStart"](6,"span",18),E["\u0275\u0275text"](7," \xa0*"),E["\u0275\u0275elementEnd"](),E["\u0275\u0275elementEnd"](),E["\u0275\u0275elementStart"](8,"div",15),E["\u0275\u0275elementStart"](9,"mat-form-field",19),E["\u0275\u0275element"](10,"input",20),E["\u0275\u0275element"](11,"mat-datepicker-toggle",21),E["\u0275\u0275element"](12,"mat-datepicker",null,22),E["\u0275\u0275elementEnd"](),E["\u0275\u0275elementEnd"](),E["\u0275\u0275elementEnd"](),E["\u0275\u0275elementStart"](14,"div",16),E["\u0275\u0275elementStart"](15,"div",17),E["\u0275\u0275text"](16," End Date "),E["\u0275\u0275elementStart"](17,"span",18),E["\u0275\u0275text"](18," \xa0*"),E["\u0275\u0275elementEnd"](),E["\u0275\u0275elementEnd"](),E["\u0275\u0275elementStart"](19,"div",15),E["\u0275\u0275elementStart"](20,"mat-form-field",19),E["\u0275\u0275element"](21,"input",23),E["\u0275\u0275element"](22,"mat-datepicker-toggle",21),E["\u0275\u0275element"](23,"mat-datepicker",null,24),E["\u0275\u0275elementEnd"](),E["\u0275\u0275elementEnd"](),E["\u0275\u0275elementEnd"](),E["\u0275\u0275elementEnd"](),E["\u0275\u0275elementStart"](25,"div",15),E["\u0275\u0275elementStart"](26,"div",16),E["\u0275\u0275elementStart"](27,"div",17),E["\u0275\u0275text"](28," Leave Type "),E["\u0275\u0275elementStart"](29,"span",18),E["\u0275\u0275text"](30," \xa0*"),E["\u0275\u0275elementEnd"](),E["\u0275\u0275elementEnd"](),E["\u0275\u0275elementStart"](31,"div",15),E["\u0275\u0275elementStart"](32,"app-input-search",25),E["\u0275\u0275listener"]("change",(function(a){E["\u0275\u0275restoreView"](e);const n=t.index;return E["\u0275\u0275nextContext"](2).validateLeaveTypeQuotaRestriction(a,n)})),E["\u0275\u0275elementEnd"](),E["\u0275\u0275elementEnd"](),E["\u0275\u0275elementEnd"](),E["\u0275\u0275elementStart"](33,"div",16),E["\u0275\u0275elementStart"](34,"div",17),E["\u0275\u0275text"](35," Leave Balance "),E["\u0275\u0275elementStart"](36,"span",18),E["\u0275\u0275text"](37," \xa0*"),E["\u0275\u0275elementEnd"](),E["\u0275\u0275elementEnd"](),E["\u0275\u0275elementStart"](38,"div",15),E["\u0275\u0275elementStart"](39,"mat-form-field",19),E["\u0275\u0275element"](40,"input",26),E["\u0275\u0275elementEnd"](),E["\u0275\u0275elementEnd"](),E["\u0275\u0275elementEnd"](),E["\u0275\u0275elementStart"](41,"div",16),E["\u0275\u0275elementStart"](42,"div",17),E["\u0275\u0275text"](43," Leave Quota "),E["\u0275\u0275elementEnd"](),E["\u0275\u0275elementStart"](44,"div",15),E["\u0275\u0275elementStart"](45,"mat-form-field",19),E["\u0275\u0275element"](46,"input",27),E["\u0275\u0275elementEnd"](),E["\u0275\u0275elementEnd"](),E["\u0275\u0275elementEnd"](),E["\u0275\u0275elementEnd"](),E["\u0275\u0275elementStart"](47,"div",15),E["\u0275\u0275elementStart"](48,"div",28),E["\u0275\u0275elementStart"](49,"span",8),E["\u0275\u0275listener"]("click",(function(){E["\u0275\u0275restoreView"](e);const a=t.index;return E["\u0275\u0275nextContext"](2).removeLeaveBalanceType(a)})),E["\u0275\u0275text"](50,"- Remove This Leave Balance"),E["\u0275\u0275elementEnd"](),E["\u0275\u0275elementEnd"](),E["\u0275\u0275elementEnd"](),E["\u0275\u0275template"](51,O,2,0,"div",29),E["\u0275\u0275elementEnd"](),E["\u0275\u0275elementEnd"]()}if(2&e){const e=t.index,a=E["\u0275\u0275reference"](13),n=E["\u0275\u0275reference"](24),l=E["\u0275\u0275nextContext"](2);E["\u0275\u0275advance"](1),E["\u0275\u0275property"]("formGroupName",e),E["\u0275\u0275advance"](9),E["\u0275\u0275property"]("matDatepicker",a)("min",l.defaultDatesForDisplay.startDateOfYear)("max",l.defaultDatesForDisplay.endDateOfYear),E["\u0275\u0275advance"](1),E["\u0275\u0275property"]("for",a),E["\u0275\u0275advance"](10),E["\u0275\u0275property"]("matDatepicker",n)("min",l.getLeaveStartDate(e))("max",l.defaultDatesForDisplay.endDateOfYear),E["\u0275\u0275advance"](1),E["\u0275\u0275property"]("for",n),E["\u0275\u0275advance"](10),E["\u0275\u0275property"]("list",l.leaveTypeMaster)("disableNone",!0),E["\u0275\u0275advance"](8),E["\u0275\u0275property"]("allowDecimal",!0),E["\u0275\u0275advance"](11),E["\u0275\u0275property"]("ngIf",l.leaveBalanceDetailsFormArray.controls.length>1)}}function I(e,t){if(1&e){const e=E["\u0275\u0275getCurrentView"]();E["\u0275\u0275elementContainerStart"](0),E["\u0275\u0275elementStart"](1,"div",4),E["\u0275\u0275text"](2,"Leave Balance Details"),E["\u0275\u0275elementEnd"](),E["\u0275\u0275elementStart"](3,"form",5),E["\u0275\u0275listener"]("keydown.enter",(function(e){return e.preventDefault()})),E["\u0275\u0275template"](4,F,52,13,"div",6),E["\u0275\u0275elementStart"](5,"div",7),E["\u0275\u0275elementStart"](6,"span",8),E["\u0275\u0275listener"]("click",(function(){return E["\u0275\u0275restoreView"](e),E["\u0275\u0275nextContext"]().addNewLeaveBalanceType()})),E["\u0275\u0275text"](7,"+ Add New Leave Balance"),E["\u0275\u0275elementEnd"](),E["\u0275\u0275elementEnd"](),E["\u0275\u0275elementStart"](8,"div",9),E["\u0275\u0275elementStart"](9,"div",10),E["\u0275\u0275elementStart"](10,"button",11),E["\u0275\u0275listener"]("click",(function(){return E["\u0275\u0275restoreView"](e),E["\u0275\u0275nextContext"]().cancel()})),E["\u0275\u0275text"](11," Cancel "),E["\u0275\u0275elementEnd"](),E["\u0275\u0275elementStart"](12,"button",12),E["\u0275\u0275listener"]("click",(function(){return E["\u0275\u0275restoreView"](e),E["\u0275\u0275nextContext"]().saveDetails()})),E["\u0275\u0275text"](13," Submit "),E["\u0275\u0275elementEnd"](),E["\u0275\u0275elementEnd"](),E["\u0275\u0275elementEnd"](),E["\u0275\u0275elementEnd"](),E["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=E["\u0275\u0275nextContext"]();E["\u0275\u0275advance"](3),E["\u0275\u0275property"]("formGroup",e.leaveBalanceDetailsFormGroup),E["\u0275\u0275advance"](1),E["\u0275\u0275property"]("ngForOf",e.leaveBalanceDetailsFormArray.controls),E["\u0275\u0275advance"](8),E["\u0275\u0275property"]("disabled",e.loaderObject.isFormSubmitLoading)}}let M=(()=>{class e{constructor(e,t,a,n){this._edService=e,this._toaster=t,this.fb=a,this.injector=n,this.subs=new l.a,this.loaderObject={isComponentLoading:!1,isFormSubmitLoading:!1},this.leaveBalanceDetailsFormGroup=this.fb.group({leaveBalanceArr:this.fb.array([])}),this.leaveTypeMaster=[],this.leaveTypeMasterCopy=[],this.leaveBalanceDetailsPayload={},this.removedExistingleaveBalanceDetailsRecord=[],this.operation="edit",this.dialogRef=this.injector.get(o.h,null),this.dialogData=this.injector.get(o.a,null),this.dialogData.modalParams&&(this.associateId=this.dialogData.modalParams.associateId)}getLeaveType(e){return new Promise((t,a)=>{this.subs.sink=this._edService.getLeaveTypeBasedOnassociateGroup(e).subscribe(e=>{t(e.data)},e=>{console.log(e),a(e)})})}getDateOfJoining(e){return new Promise((t,a)=>{this.subs.sink=this._edService.getDateOfJoining(e).subscribe(e=>{e.err?a(e):t(e.doj)},e=>{console.log(e),a(e)})})}createNewLeaveBalanceGroup(e){return this.fb.group({recordId:[e?e.record_id:""],leaveStartDate:[e?e.leaveStartDate:"",v.H.required],leaveEndDate:[e?e.leaveEndDate:"",v.H.required],leaveType:[e?e.leaveType:"",v.H.required],leaveBalance:[e?e.leaveBalance:"",v.H.required],leaveQuota:[""]})}get leaveBalanceDetailsFormArray(){return this.leaveBalanceDetailsFormGroup.get("leaveBalanceArr")}getLeaveStartDate(e){return this.leaveBalanceDetailsFormArray.at(e).get("leaveStartDate").value}ngOnInit(){return Object(n.c)(this,void 0,void 0,(function*(){this.loaderObject.isComponentLoading=!0,this.leaveTypeMaster=yield this.getLeaveType(this.associateId),this.leaveTypeMasterCopy=[...this.leaveTypeMaster],this.employeeDoj=yield this.getDateOfJoining(this.associateId),this.defaultDatesForDisplay=yield this.getDefaultLeaveDates(),this.loaderObject.isComponentLoading=!1,yield this.bindSavedResponse(),this.createInitValue(),this.handleLeaveBalanceDetailsPayloadObject(),this.valueChangeListener()}))}valueChangeListener(){this.leaveBalanceDetailsFormGroup.valueChanges.subscribe(e=>{this.handleLeaveBalanceDetailsPayloadObject()})}handleLeaveBalanceDetailsPayloadObject(){let e=this.leaveBalanceDetailsFormGroup.get("leaveBalanceArr").value;this.leaveBalanceDetailsPayload=[],e.forEach((e,t)=>{this.leaveBalanceDetailsPayload[t]={value:e,isChanged:this.checkIfChanged(e,this.leaveBalanceDetailsInitValue[t])}}),console.log(this.leaveBalanceDetailsPayload)}checkIfObjectChanged(e,t,a){return"object"==typeof e[a]?!r.isEqual(e[a],t[a]):e[a]!==t[a]}checkIfChanged(e,t){return!r.isEqual(e,t)}createInitValue(){this.leaveBalanceDetailsInitValue=JSON.parse(JSON.stringify(this.leaveBalanceDetailsFormGroup.get("leaveBalanceArr").value))}bindSavedResponse(){return new Promise((e,t)=>{this.subs.sink=this._edService.getLeaveBalanceDetails(this.associateId).subscribe(t=>{if(!t.err){let a=t.data;a&&a.length>0?a.forEach((e,t)=>{this.leaveBalanceDetailsFormArray.push(this.createNewLeaveBalanceGroup({record_id:e?e.record_id:"",leaveStartDate:e?e.leaveb_start_date:"",leaveEndDate:e?e.leaveb_end_date:"",leaveType:e?e.leave_type:null,leaveBalance:e?e.leave_balance:"",leaveQuota:e?e.leave_quota:0})),this.addLeaveTypeQuota()}):this.addNewLeaveBalanceType(),e(!0)}},e=>{console.log(e),t(e)})})}addNewLeaveBalanceType(){this.leaveBalanceDetailsFormArray.push(this.createNewLeaveBalanceGroup(null))}removeLeaveBalanceType(e){this.addDeletedRecordId(this.removedExistingleaveBalanceDetailsRecord,e,this.leaveBalanceDetailsFormArray),this.leaveBalanceDetailsFormArray.removeAt(e)}clearFirstForm(e){let t=this.leaveBalanceDetailsFormGroup.get("leaveBalanceArr"),a=t.controls[e].get("recordId").value;t.controls[e].reset(),t.controls[e].get("recordId").patchValue(a)}validateIfChanged(){let e=!1;console.log(this.leaveBalanceDetailsPayload);for(let t of this.leaveBalanceDetailsPayload)if(t.isChanged){e=!0;break}return this.removedExistingleaveBalanceDetailsRecord.length>0&&(e=!0),e}saveDetails(){if(this.leaveBalanceDetailsFormGroup.valid)if(this.validateIfChanged()){let e={associate_id:this.associateId,leaveBalanceArr:this.leaveBalanceDetailsPayload,removedExistingleaveBalanceDetailsRecord:this.removedExistingleaveBalanceDetailsRecord};this.handleDateFormatPayLoad(e),this.subs.sink=this._edService.saveLeaveBalanceDetails(e).subscribe(e=>{e.err?this._toaster.showError("Error","Failed to save., Kindly contact KEBS team to resolve",2e3):(this._toaster.showSuccess("Success","Leave Balance details updated successfully !",2e3),this.resetFormFields(),this.closeDialog("Updated"))},e=>{this._toaster.showError("Error","Failed to save. Kindly contact KEBS team to resolve",2e3),console.log(e)})}else this._toaster.showWarning("No changes","No new changes were made !");else this._toaster.showWarning("Invalid data","Kindly fill all mandatory fields to proceed !")}resetFormFields(){this.leaveBalanceDetailsFormGroup.reset()}addDeletedRecordId(e,t,a){let n=a.controls[t].get("recordId").value;n&&e.push(n)}cancel(){this.closeDialog("")}closeDialog(e){this.dialogRef.close(e)}validateLeaveTypeQuotaRestriction(e,t){this.addLeaveTypeQuota();let a=this.leaveBalanceDetailsFormGroup.get("leaveBalanceArr").value;if(a.splice(t,1),r.where(a,{leaveType:e.id}).length>0){this._toaster.showWarning("Warning","Leave Type Already Exist");let e=this.leaveBalanceDetailsFormGroup.get("leaveBalanceArr");e.controls[t].get("leaveType").reset(),e.controls[t].get("leaveQuota").reset()}}addLeaveTypeQuota(){let e=this.leaveBalanceDetailsFormGroup.get("leaveBalanceArr").value,t=this.leaveBalanceDetailsFormGroup.get("leaveBalanceArr");for(let a=0;a<e.length;a++){let n=r.where(this.leaveTypeMasterCopy,{id:e[a].leaveType});t.controls[a].get("leaveQuota").patchValue(n.length>0&&n[0].quota?n[0].quota:0)}}validateLeaveTypeQuota(){let e=this.leaveBalanceDetailsFormGroup.get("leaveBalanceArr").value;for(let t=0;t<e.length;t++)if(e[t].leaveBalance>e[t].leaveQuota)return!1;return!0}handleDateFormatPayLoad(e){e.leaveBalanceArr.forEach((e,t)=>{e.value.leaveStartDate=e.value.leaveStartDate?i(e.value.leaveStartDate).format("YYYY-MM-DD"):"",e.value.leaveEndDate=e.value.leaveEndDate?i(e.value.leaveEndDate).format("YYYY-MM-DD"):""})}getDefaultLeaveDates(){return new Promise((e,t)=>{this.subs.sink=this._edService.getDefaultLeaveDates().subscribe(t=>{e(t.data)},e=>{console.log(e),t(e)})})}ngOnDestroy(){this.subs.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(E["\u0275\u0275directiveInject"](S.a),E["\u0275\u0275directiveInject"](B.a),E["\u0275\u0275directiveInject"](v.i),E["\u0275\u0275directiveInject"](E.Injector))},e.\u0275cmp=E["\u0275\u0275defineComponent"]({type:e,selectors:[["app-leave-balance-details"]],decls:3,vars:2,consts:[[1,"container-fluid","leave-balance-details"],[4,"ngIf"],[1,"d-flex","justify-content-center","mt-3"],["matTooltip","Please wait...","diameter","30"],[1,"row","section-header","mb-2"],[3,"formGroup","keydown.enter"],["class","row","formArrayName","leaveBalanceArr",4,"ngFor","ngForOf"],[1,"row","add-link","mt-2"],[3,"click"],[1,"row","mt-4"],[1,"d-flex","flex-row"],["type","button","mat-stroked-button","",1,"cancel-btn",3,"click"],["type","button","mat-raised-button","",1,"ml-3","create-employee-btn",3,"disabled","click"],["formArrayName","leaveBalanceArr",1,"row"],[1,"col-12","px-0",3,"formGroupName"],[1,"row"],[1,"col-3","px-0","mr-4"],[1,"row","field-title"],[1,"required-star"],["appearance","outline",2,"width","100%"],["matInput","","required","","formControlName","leaveStartDate","placeholder","DD MM YYYY","readonly","",3,"matDatepicker","min","max"],["matSuffix","",3,"for"],["picker2",""],["matInput","","required","","formControlName","leaveEndDate","placeholder","DD MM YYYY","readonly","",3,"matDatepicker","min","max"],["picker3",""],["hideMatLabel","false","required","true","placeholder","Select One","formControlName","leaveType",2,"width","100%",3,"list","disableNone","change"],["required","","matInput","","digitOnly","","placeholder","Enter one","formControlName","leaveBalance",3,"allowDecimal"],["matInput","","readonly","","type","number","placeholder","Enter one","formControlName","leaveQuota"],[1,"col-6","my-auto","px-0","add-link"],["class","row my-2",4,"ngIf"],[1,"row","my-2"]],template:function(e,t){1&e&&(E["\u0275\u0275elementStart"](0,"div",0),E["\u0275\u0275template"](1,L,3,0,"ng-container",1),E["\u0275\u0275template"](2,I,14,3,"ng-container",1),E["\u0275\u0275elementEnd"]()),2&e&&(E["\u0275\u0275advance"](1),E["\u0275\u0275property"]("ngIf",t.loaderObject.isComponentLoading),E["\u0275\u0275advance"](1),E["\u0275\u0275property"]("ngIf",!t.loaderObject.isComponentLoading))},directives:[s.NgIf,g.c,y.a,v.J,v.w,v.n,s.NgForOf,f.a,v.h,v.o,c.c,m.b,v.e,u.g,v.F,v.v,v.l,u.i,c.i,u.f,w.a,x.a,v.A],styles:[".leave-balance-details[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]{font-size:16px;font-weight:500;color:#45546e}.leave-balance-details[_ngcontent-%COMP%]   .field-title[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#6e7b8f}.leave-balance-details[_ngcontent-%COMP%]   .required-star[_ngcontent-%COMP%]{color:#cf0001}.leave-balance-details[_ngcontent-%COMP%]     .mat-form-field-outline, .leave-balance-details[_ngcontent-%COMP%]     mat-datepicker-toggle{color:#b9c0ca!important}.leave-balance-details[_ngcontent-%COMP%]   .add-link[_ngcontent-%COMP%]{text-decoration:underline;color:#f27a6c;font-size:14px;cursor:pointer;font-weight:500}.leave-balance-details[_ngcontent-%COMP%]   .create-employee-btn[_ngcontent-%COMP%]{background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%)!important;color:#fff;height:42px}.leave-balance-details[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{color:#45546e;height:42px}.leave-balance-details[_ngcontent-%COMP%]     .mat-form-field-disabled{opacity:.5}.leave-balance-details[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}"]}),e})(),_=(()=>{class e{}return e.\u0275mod=E["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=E["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},providers:[{provide:p.c,useClass:D.c,deps:[p.f,D.a]},{provide:p.e,useValue:{parse:{dateInput:"DD-MM-YYYY"},display:{dateInput:"DD-MM-YYYY",monthYearLabel:"MMM YYYY"}}}],imports:[[s.CommonModule,d.a,c.e,m.c,v.E,v.p,u.h,p.x,h.b,f.b,g.b,y.b,b.b,C.a]]}),e})()}}]);