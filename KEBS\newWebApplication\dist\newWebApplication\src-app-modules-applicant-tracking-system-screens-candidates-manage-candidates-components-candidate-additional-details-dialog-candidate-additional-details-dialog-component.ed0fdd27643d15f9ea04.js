(window.webpackJsonp=window.webpackJsonp||[]).push([[910],{Suhe:function(t,e,n){"use strict";n.r(e),n.d(e,"CandidateAdditionalDetailsDialogComponent",(function(){return s}));var a=n("0IaG"),l=n("fXoL"),i=n("NFeN"),o=n("ofXK"),d=n("f0Cb"),c=n("Qu3c"),r=n("IEgo");function p(t,e){if(1&t&&(l["\u0275\u0275elementStart"](0,"div",13),l["\u0275\u0275text"](1),l["\u0275\u0275elementEnd"]()),2&t){const t=l["\u0275\u0275nextContext"]().$implicit,e=l["\u0275\u0275nextContext"]();l["\u0275\u0275property"]("matTooltip",(null==e.data?null:e.data.data[t.colName])||"-"),l["\u0275\u0275advance"](1),l["\u0275\u0275textInterpolate1"](" ",(null==e.data?null:e.data.data[t.colName])||"-"," ")}}function m(t,e){if(1&t&&(l["\u0275\u0275elementContainerStart"](0),l["\u0275\u0275elementStart"](1,"div",10),l["\u0275\u0275elementStart"](2,"div",11),l["\u0275\u0275text"](3),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](4,p,2,2,"div",12),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementContainerEnd"]()),2&t){const t=e.$implicit;l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("matTooltip",null==t?null:t.label),l["\u0275\u0275advance"](1),l["\u0275\u0275textInterpolate1"](" ",null==t?null:t.label," "),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf","text"==(null==t?null:t.fieldType))}}function f(t,e){if(1&t&&(l["\u0275\u0275elementStart"](0,"div",13),l["\u0275\u0275text"](1),l["\u0275\u0275elementEnd"]()),2&t){const t=l["\u0275\u0275nextContext"]().$implicit,e=l["\u0275\u0275nextContext"]();l["\u0275\u0275property"]("matTooltip",(null==e.data?null:e.data.data[t.colName])||"-"),l["\u0275\u0275advance"](1),l["\u0275\u0275textInterpolate1"](" ",(null==e.data?null:e.data.data[t.colName])||"-"," ")}}function g(t,e){if(1&t&&(l["\u0275\u0275elementStart"](0,"div",13),l["\u0275\u0275pipe"](1,"dateFormat"),l["\u0275\u0275text"](2),l["\u0275\u0275pipe"](3,"dateFormat"),l["\u0275\u0275elementEnd"]()),2&t){const t=l["\u0275\u0275nextContext"]().$implicit,e=l["\u0275\u0275nextContext"]();l["\u0275\u0275property"]("matTooltip",l["\u0275\u0275pipeBind2"](1,2,null==e.data?null:e.data.data[t.colName],"DD MMM YYYY")),l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate1"](" ",l["\u0275\u0275pipeBind2"](3,5,null==e.data?null:e.data.data[t.colName],"DD MMM YYYY")," ")}}function u(t,e){if(1&t&&(l["\u0275\u0275elementContainerStart"](0),l["\u0275\u0275elementStart"](1,"div",10),l["\u0275\u0275elementStart"](2,"div",11),l["\u0275\u0275text"](3),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](4,f,2,2,"div",12),l["\u0275\u0275template"](5,g,4,8,"div",12),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementContainerEnd"]()),2&t){const t=e.$implicit;l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("matTooltip",null==t?null:t.label),l["\u0275\u0275advance"](1),l["\u0275\u0275textInterpolate1"](" ",null==t?null:t.label," "),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf","text"==(null==t?null:t.fieldType)),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf","dateTime"==(null==t?null:t.fieldType))}}let s=(()=>{class t{constructor(t,e){this.data=t,this._dialogRef=e}ngOnInit(){}onClose(){this._dialogRef.close()}}return t.\u0275fac=function(e){return new(e||t)(l["\u0275\u0275directiveInject"](a.a),l["\u0275\u0275directiveInject"](a.h))},t.\u0275cmp=l["\u0275\u0275defineComponent"]({type:t,selectors:[["app-candidate-additional-details-dialog"]],decls:17,vars:4,consts:[[1,"bg-container"],[1,"d-flex","align-items-center","justify-content-between"],[1,"title","p-0"],[1,"icon"],[1,"icon",3,"click"],[1,"d-flex","align-items-center",2,"gap","16px"],["src","assets/images/User.png",1,"image"],[1,"d-flex","flex-column",2,"gap","8px"],[4,"ngFor","ngForOf"],[1,"divider"],[1,"d-flex","align-items-center",2,"gap","8px"],[1,"label-text",3,"matTooltip"],["class","content-text",3,"matTooltip",4,"ngIf"],[1,"content-text",3,"matTooltip"]],template:function(t,e){1&t&&(l["\u0275\u0275elementStart"](0,"div",0),l["\u0275\u0275elementStart"](1,"div",1),l["\u0275\u0275elementStart"](2,"div",2),l["\u0275\u0275text"](3),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](4,"div",3),l["\u0275\u0275elementStart"](5,"mat-icon",4),l["\u0275\u0275listener"]("click",(function(){return e.onClose()})),l["\u0275\u0275text"](6," close "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](7,"div",5),l["\u0275\u0275elementStart"](8,"div"),l["\u0275\u0275element"](9,"img",6),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](10,"div",7),l["\u0275\u0275template"](11,m,5,3,"ng-container",8),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275element"](12,"mat-divider",9),l["\u0275\u0275elementStart"](13,"div",2),l["\u0275\u0275text"](14),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](15,"div",7),l["\u0275\u0275template"](16,u,6,4,"ng-container",8),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&t&&(l["\u0275\u0275advance"](3),l["\u0275\u0275textInterpolate1"](" ",null==e.data||null==e.data.fieldConfig?null:e.data.fieldConfig.candidateDetailsDialogTitle," "),l["\u0275\u0275advance"](8),l["\u0275\u0275property"]("ngForOf",null==e.data||null==e.data.fieldConfig?null:e.data.fieldConfig.candidateDetails),l["\u0275\u0275advance"](3),l["\u0275\u0275textInterpolate1"](" ",null==e.data||null==e.data.fieldConfig?null:e.data.fieldConfig.candidateScoreDetailsTitle," "),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngForOf",null==e.data||null==e.data.fieldConfig?null:e.data.fieldConfig.candidateScoreDetails))},directives:[i.a,o.NgForOf,d.a,c.a,o.NgIf],pipes:[r.a],styles:[".bg-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;padding:24px;gap:16px}.bg-container[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:var(--fontFamily);font-size:18px;font-weight:700;color:#111434}.bg-container[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{width:18px;height:18px;font-size:18px;color:#1b2140;cursor:pointer}.bg-container[_ngcontent-%COMP%]   .image[_ngcontent-%COMP%]{border-radius:12px;height:100px;width:100px}.bg-container[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]{color:#e8e9ee}.bg-container[_ngcontent-%COMP%]   .label-text[_ngcontent-%COMP%]{width:143px;font-weight:400;color:#8b95a5}.bg-container[_ngcontent-%COMP%]   .content-text[_ngcontent-%COMP%], .bg-container[_ngcontent-%COMP%]   .label-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.bg-container[_ngcontent-%COMP%]   .content-text[_ngcontent-%COMP%]{width:260px;font-weight:500;color:#45546e}"]}),t})()}}]);