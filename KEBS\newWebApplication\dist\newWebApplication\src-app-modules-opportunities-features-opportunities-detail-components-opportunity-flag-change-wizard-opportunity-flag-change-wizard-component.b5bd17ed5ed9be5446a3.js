(window.webpackJsonp=window.webpackJsonp||[]).push([[965,985],{"A+V/":function(t,n,e){"use strict";e.d(n,"a",(function(){return l}));var o=e("mrSG"),a=e("XNiG"),g=e("xG9w"),i=e("fXoL"),c=e("tk/3"),r=e("flaP");let l=(()=>{class t{constructor(t,n){this._http=t,this.roleService=n,this.tabLoadingSubject=new a.b,this.getTabLoadingObservable=this.tabLoadingSubject.asObservable(),this.isActivityApproval=t=>new Promise((n,e)=>{this._http.post("/api/opportunity/isActivityAppStarted",{oppId:t}).subscribe(t=>n(t),t=>e(t))}),this.colorConfigUrl="/api/opportunity/tabs",this.tabColor=null}setTabLoadingObservable(t){this.tabLoadingSubject.next(t)}getOpportunityTeamDetails(t){return new Promise((n,e)=>{this._http.post("/api/opportunity/getOpportunityTeamDetails",{opportunityId:t}).subscribe(t=>n(t),t=>e(t))})}updateInternalStakeholderAsPrimary(t,n,e,o,a){return new Promise((g,i)=>{this._http.post("/api/opportunity/updateInternalStakeholderAsPrimary",{stakeholderId:t,ownerType:n,opportunityId:e,practice_id:o,member:a}).subscribe(t=>g(t),t=>i(t))})}addTeamMember(t,n){return new Promise((e,o)=>{this._http.post("/api/opportunity/addOpportunityTeamMember",{opportunityId:t,member:n}).subscribe(t=>e(t),t=>o(t))})}removeMember(t){return new Promise((n,e)=>{this._http.post("/api/opportunity/removeMember",{stakeholderId:t}).subscribe(t=>n(t),t=>e(t))})}getPastStakeholders(t){return new Promise((n,e)=>{this._http.post("/api/opportunity/getPastMembers",{opportunityId:t}).subscribe(t=>n(t),t=>e(t))})}getOpportunityStakeholderMaster(t){return new Promise((n,e)=>{this._http.post("api/opportunity/getOpportunityStakeholderMaster",{stakeholderType:t}).subscribe(t=>n(t),t=>e(t))})}getNotifyFormFieldCollection(t){return this._http.post("/api/activity/getNotifyFormFieldCollection",{application_id:t})}updateNotificationStakeholeder(t){return new Promise((n,e)=>{this._http.post("api/opportunity/updateNotificationStakeholeder",{updateNotificationParams:t}).subscribe(t=>n(t),t=>e(t))})}checkEditAcessISH(){g.where(this.roleService.roles,{application_id:36,role_id:1});let t=g.where(this.roleService.roles,{application_id:36,object_id:6,operation:"Read"});return console.log("accessList ISH",t),!(t.length>0)}getTheme(){return Object(o.c)(this,void 0,void 0,(function*(){if(this.tabColor&&null!=this.loaderConfig)return Promise.resolve({tabColor:this.tabColor,loaderConfig:this.loaderConfig});try{const t=yield this._http.get(this.colorConfigUrl).toPromise();return this.tabColor=t.tabColor||"#defaultColor",this.loaderConfig=null==t?void 0:t.loaderConfig,{tabColor:this.tabColor,loaderConfig:t.loaderConfig||!1}}catch(t){return console.error(t),{tabColor:"#79ba44"}}}))}}return t.\u0275fac=function(n){return new(n||t)(i["\u0275\u0275inject"](c.c),i["\u0275\u0275inject"](r.a))},t.\u0275prov=i["\u0275\u0275defineInjectable"]({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()},aKSU:function(t,n,e){"use strict";e.r(n),e.d(n,"OpportunityFlagChangeWizardComponent",(function(){return y}));var o=e("mrSG"),a=e("0IaG"),g=e("fXoL"),i=e("pgif"),c=e("A+V/"),r=e("LcQX"),l=e("ofXK"),p=e("NFeN"),s=e("Qu3c"),f=e("bTqV");function d(t,n){1&t&&(g["\u0275\u0275elementStart"](0,"div",3),g["\u0275\u0275element"](1,"div",4),g["\u0275\u0275elementEnd"]())}const h=function(t,n){return{"checkbox1 checkbox-error":t,checkbox:n}};function x(t,n){if(1&t&&(g["\u0275\u0275elementStart"](0,"div",23),g["\u0275\u0275element"](1,"div",24),g["\u0275\u0275elementStart"](2,"span",25),g["\u0275\u0275text"](3),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()),2&t){const t=n.$implicit;g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngClass",g["\u0275\u0275pureFunction2"](2,h,!t.status,t.status)),g["\u0275\u0275advance"](2),g["\u0275\u0275textInterpolate"](t.constraint)}}function m(t,n){if(1&t&&(g["\u0275\u0275elementStart"](0,"div",19),g["\u0275\u0275elementStart"](1,"h3",20),g["\u0275\u0275text"](2," Checklist "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](3,"div",21),g["\u0275\u0275template"](4,x,4,5,"div",22),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()),2&t){const t=g["\u0275\u0275nextContext"](2);g["\u0275\u0275advance"](4),g["\u0275\u0275property"]("ngForOf",t.constraints_list)}}function _(t,n){1&t&&(g["\u0275\u0275elementStart"](0,"div",26),g["\u0275\u0275elementStart"](1,"div",27),g["\u0275\u0275element"](2,"div"),g["\u0275\u0275elementStart"](3,"span"),g["\u0275\u0275text"](4," Opportunity Already Secured! "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]())}function C(t,n){if(1&t){const t=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"button",32),g["\u0275\u0275listener"]("click",(function(){return g["\u0275\u0275restoreView"](t),g["\u0275\u0275nextContext"](3).redirect()})),g["\u0275\u0275text"](1," Redirect "),g["\u0275\u0275elementEnd"]()}}function O(t,n){if(1&t){const t=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div",28),g["\u0275\u0275elementStart"](1,"div",29),g["\u0275\u0275template"](2,C,2,0,"button",30),g["\u0275\u0275elementStart"](3,"button",31),g["\u0275\u0275listener"]("click",(function(){return g["\u0275\u0275restoreView"](t),g["\u0275\u0275nextContext"](2).proceed()})),g["\u0275\u0275text"](4),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}if(2&t){const t=g["\u0275\u0275nextContext"](2);g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("ngIf",t.hasRedirect),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("disabled",!t.flagcheck),g["\u0275\u0275advance"](1),g["\u0275\u0275textInterpolate1"](" ",t.save," ")}}const P=function(t){return{"background-color":t}};function M(t,n){if(1&t&&(g["\u0275\u0275elementStart"](0,"span"),g["\u0275\u0275elementStart"](1,"div",5),g["\u0275\u0275element"](2,"div",6),g["\u0275\u0275elementStart"](3,"div",7),g["\u0275\u0275text"](4),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](5,"div",6),g["\u0275\u0275elementStart"](6,"mat-icon",8),g["\u0275\u0275text"](7,"close"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](8,"div",9),g["\u0275\u0275elementStart"](9,"div",10),g["\u0275\u0275elementStart"](10,"span",11),g["\u0275\u0275text"](11,"Risk Status -"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](12,"div",12),g["\u0275\u0275elementStart"](13,"span",13),g["\u0275\u0275text"](14),g["\u0275\u0275elementEnd"](),g["\u0275\u0275element"](15,"span",14),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275template"](16,m,5,1,"div",15),g["\u0275\u0275template"](17,_,5,0,"div",16),g["\u0275\u0275element"](18,"div",17),g["\u0275\u0275template"](19,O,5,3,"div",18),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()),2&t){const t=g["\u0275\u0275nextContext"]();g["\u0275\u0275advance"](4),g["\u0275\u0275textInterpolate"](t.dialogData.label||"Flag Change Wizard"),g["\u0275\u0275advance"](8),g["\u0275\u0275property"]("ngStyle",g["\u0275\u0275pureFunction1"](6,P,1==t.dialogData.at_risk?"#1890FF":"#F27A6C")),g["\u0275\u0275advance"](2),g["\u0275\u0275textInterpolate"](1==t.dialogData.at_risk?"Secured":(null==t.dialogData?null:t.dialogData.riskLabel)||"At Risk"),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("ngIf",1==t.dialogData.at_risk),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",0==t.dialogData.at_risk),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("ngIf",1==t.dialogData.at_risk)}}let y=(()=>{class t{constructor(t,n,e,o,a){this.opportunityService=t,this.dialogRef=n,this.dialogData=e,this._stakeholderService=o,this.utilityService=a,this.flagcheck=!1,this.save="Complete",this.selectedColor="white",this.loading=!0,this.allowStatus=!1,this.hasRedirect=!1,this.constraints_list=[]}ngOnInit(){var t;return Object(o.c)(this,void 0,void 0,(function*(){this.currentStatus=0===this.dialogData.at_risk?"Secured":"Risk",this.selectedColor="Secured"===this.currentStatus?"#1890FF":"#F27A6C",this.hasRedirect=(null===(t=this.dialogData)||void 0===t?void 0:t.reDirect)||!1;const n=yield this._stakeholderService.getTheme();document.documentElement.style.setProperty("--intButton",n.tabColor);try{const t=yield this.opportunityService.checkOpportunity(this.dialogData.opportunity_id,this.dialogData.project_id);"S"===t.messType&&(this.constraints_list=t.data,this.flagcheck=this.constraints_list.every(t=>t.status),this.allowStatus=this.flagcheck)}catch(e){console.error("Error checking opportunity:",e)}finally{this.loading=!1}}))}proceed(){return Object(o.c)(this,void 0,void 0,(function*(){const t=this.dialogData.at_risk?0:1;try{const n=yield this.opportunityService.updateRiskStatus(this.dialogData.opportunity_id,t);this.dialogRef.close("S"===n.messType?{messType:"S",at_risk:t}:{messType:"E"})}catch(n){console.error("Error updating risk status:",n),this.dialogRef.close({messType:"E",error:n})}}))}redirect(){window.open(`${window.location.origin}/main/opportunities/${this.dialogData.opportunity_id}/${this.utilityService.encodeURIComponent(this.dialogData.opportunity_name)}`)}}return t.\u0275fac=function(n){return new(n||t)(g["\u0275\u0275directiveInject"](i.a),g["\u0275\u0275directiveInject"](a.h),g["\u0275\u0275directiveInject"](a.a),g["\u0275\u0275directiveInject"](c.a),g["\u0275\u0275directiveInject"](r.a))},t.\u0275cmp=g["\u0275\u0275defineComponent"]({type:t,selectors:[["app-opportunity-flag-change-wizard"]],decls:3,vars:2,consts:[[1,"flag-change-style"],["class","loader-container",4,"ngIf"],[4,"ngIf"],[1,"loader-container"],[1,"loader"],[1,"row","header"],[1,"col-4"],[1,"header-title","col-4"],["mat-icon-button","","matTooltip","Close",1,"close-button",3,"mat-dialog-close"],[1,"col-12","content",2,"overflow","hidden","padding-bottom","30px"],[1,"risk-status-wrapper"],[1,"risk"],[1,"rc-stat",3,"ngStyle"],[1,"status"],[1,"arrow-down"],["class","checklist-box",4,"ngIf"],["class","checklist-boxn",4,"ngIf"],[1,"divider"],["class","row footer-buttons",4,"ngIf"],[1,"checklist-box"],[1,"tab-title"],[1,"scroll"],["class","checklist-item",4,"ngFor","ngForOf"],[1,"checklist-item"],[3,"ngClass"],[1,"item3"],[1,"checklist-boxn"],[1,"checklist-item3"],[1,"row","footer-buttons"],[1,"button-container","d-flex","align-items-center","justify-content-end"],["class","save-button","mat-raised-button","",3,"click",4,"ngIf"],["mat-raised-button","",1,"save-button",3,"disabled","click"],["mat-raised-button","",1,"save-button",3,"click"]],template:function(t,n){1&t&&(g["\u0275\u0275elementStart"](0,"div",0),g["\u0275\u0275template"](1,d,2,0,"div",1),g["\u0275\u0275template"](2,M,20,8,"span",2),g["\u0275\u0275elementEnd"]()),2&t&&(g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",n.loading),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",!n.loading))},directives:[l.NgIf,p.a,s.a,a.d,l.NgStyle,l.NgForOf,l.NgClass,f.a],styles:['@charset "UTF-8";.flag-change-style[_ngcontent-%COMP%]{width:650px;max-height:550px;top:97px;left:172px;overflow:hidden;background:#fff}.flag-change-style[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{background-color:#fff;min-height:66px;zoom:90%}.flag-change-style[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]{color:#000;margin-top:-25px;background-color:#fff;padding-top:20px;font-size:18px;font-weight:600;line-height:24px;letter-spacing:0}.flag-change-style[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%], .flag-change-style[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;font-family:Roboto;text-align:left}.flag-change-style[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%]{height:40px;padding:12px 16px;border-radius:4px;gap:8px;font-size:14px;font-weight:700;line-height:16px;letter-spacing:-.02em;color:#fff;background:var(--intButton)!important;margin-right:1rem;width:89px}.flag-change-style[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%]:disabled{background-color:#dadce2!important;color:#8b95a5;cursor:not-allowed}.flag-change-style[_ngcontent-%COMP%]   .footer-buttons[_ngcontent-%COMP%]{height:60px;zoom:80%}.flag-change-style[_ngcontent-%COMP%]   .dropdown[_ngcontent-%COMP%]{margin-top:24px;min-width:76px;min-height:20px;margin-left:24px}.flag-change-style[_ngcontent-%COMP%]   #dropdown[_ngcontent-%COMP%]{padding:5px;border-radius:4px;border:1px solid #ccc;min-width:96px;min-height:24px;-webkit-appearance:none;-moz-appearance:none;appearance:none}.flag-change-style[_ngcontent-%COMP%]   .checklist-box[_ngcontent-%COMP%]{background-color:#fff;border:1px solid #ccc;border-radius:8px;min-height:100px;align-items:center;margin:10px 40px 20px;zoom:100%}.flag-change-style[_ngcontent-%COMP%]   .scroll[_ngcontent-%COMP%]{overflow-y:auto;max-height:140px}.flag-change-style[_ngcontent-%COMP%]   .finance-scroll[_ngcontent-%COMP%]{overflow-y:auto;max-height:120px}.flag-change-style[_ngcontent-%COMP%]   .finance-box[_ngcontent-%COMP%]{border:1px solid #ccc;border-radius:8px;margin:15px 40px 20px;max-height:250px;background-color:#fff;zoom:94%}.flag-change-style[_ngcontent-%COMP%]   .checklist-box[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .flag-change-style[_ngcontent-%COMP%]   .finance-box[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0;padding:10px;background-color:#e8e9ee;color:#1b2140;border-top-left-radius:8px;border-top-right-radius:8px}.flag-change-style[_ngcontent-%COMP%]   .poNumberq[_ngcontent-%COMP%]{border-radius:5px;display:flow;background-color:#fff;padding-left:0!important;padding-right:0!important;font-size:12px;background-color:#e8e9ee;font-family:Roboto;width:80.5px;margin-top:10px}.flag-change-style[_ngcontent-%COMP%]   .tab-title[_ngcontent-%COMP%]{color:#1b2140;font-size:14px;font-weight:600;font:Roboto;line-height:24px;letter-spacing:.03rem;text-align:left}.flag-change-style[_ngcontent-%COMP%]   .checklist-item[_ngcontent-%COMP%]{display:flex;align-items:center;margin:10px}.flag-change-style[_ngcontent-%COMP%]   .checklist-item3[_ngcontent-%COMP%]{font-size:13px;font-weight:500;font-family:Roboto;display:grid;place-content:center;height:200px}.flag-change-style[_ngcontent-%COMP%]   .checklist-boxn[_ngcontent-%COMP%]{background-color:#fff;border:1px solid #ccc;border-radius:8px;min-height:200px;align-items:center;margin:10px 40px 20px;zoom:100%}.flag-change-style[_ngcontent-%COMP%]   .item3[_ngcontent-%COMP%]{font-size:13px;font-family:Roboto;font-weight:500}.flag-change-style[_ngcontent-%COMP%]   .checkbox[_ngcontent-%COMP%]{border:2px solid #4caf50;border-radius:50%;background-color:#4caf50}.flag-change-style[_ngcontent-%COMP%]   .checkbox[_ngcontent-%COMP%], .flag-change-style[_ngcontent-%COMP%]   .checkbox1[_ngcontent-%COMP%]{width:16px;height:16px;display:flex;align-items:center;justify-content:center;margin-right:10px;position:relative}.flag-change-style[_ngcontent-%COMP%]   .checkbox1[_ngcontent-%COMP%]{border:2px solid #ff3a46;border-radius:50%;background-color:#ff3a46}.flag-change-style[_ngcontent-%COMP%]   .checkbox[_ngcontent-%COMP%]:after{content:"\u2714";color:#fff;font-size:12px;position:absolute;top:-4px;left:2px}.flag-change-style[_ngcontent-%COMP%]   .checkbox-error[_ngcontent-%COMP%]{border:2px solid #ff3a46}.flag-change-style[_ngcontent-%COMP%]   .checkbox-error[_ngcontent-%COMP%]:after{content:"\u2718";color:#fff;font-size:12px;position:absolute;top:-4px;left:2px}.flag-change-style[_ngcontent-%COMP%]   .errbck[_ngcontent-%COMP%]{background-color:#ff3a46}.flag-change-style[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]{margin:10px}.flag-change-style[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{display:block;margin-bottom:5px}.flag-change-style[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{width:calc(100% - 20px);padding:8px;border:1px solid #ccc;border-radius:4px;margin:5px}.flag-change-style[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]{background-color:#d4d6d8}.flag-change-style[_ngcontent-%COMP%]   .footer[_ngcontent-%COMP%]{text-align:right;padding:15px;background-color:#f5f5f5;border-top:1px solid #ccc}.flag-change-style[_ngcontent-%COMP%]   .footer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{width:Hug 98px px;height:Hug 40px px;top:16px;left:612px;padding:12px 16px;gap:8px;border-radius:8px 0 0 0;opacity:0}.flag-change-style[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{cursor:pointer;margin-left:75%!important;color:grey;position:absolute;font-size:20px;margin-top:16px}.flag-change-style[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{cursor:pointer;font-family:Roboto;font-size:12px;font-weight:400;line-height:1px;letter-spacing:.02em;text-align:left;background:#f7f9fb;text-wrap:nowrap}.flag-change-style[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{gap:24px;background-color:#f6f6f6;height:337px;overflow:auto}.flag-change-style[_ngcontent-%COMP%]   .watermark[_ngcontent-%COMP%]{width:107px;height:55px;margin:-40px 0 0}.flag-change-style[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;font-size:10px;position:absolute;left:615px;top:165px}.flag-change-style[_ngcontent-%COMP%]   .button-next[_ngcontent-%COMP%]{height:40px;margin-bottom:10px;background-color:#fff;border-radius:5px;color:#79ba44;width:110;display:flex;justify-content:center;align-items:center;text-wrap:nowrap;margin-top:20px;border:1px solid #79ba44;border-bottom-left-radius:10px;border-top-right-radius:10px}.flag-change-style[_ngcontent-%COMP%]   .footer-buttons[_ngcontent-%COMP%]{left:0;right:0;border-top:1px solid grey;zoom:85%}.flag-change-style[_ngcontent-%COMP%]     .green-spinner circle{stroke:var(--stepperColor)!important}.flag-change-style[_ngcontent-%COMP%]   .tittle[_ngcontent-%COMP%]{width:272px;height:24px;font-family:Roboto;font-size:14px;font-weight:700;line-height:24px;letter-spacing:.02em;text-align:center;color:#45546e}.flag-change-style[_ngcontent-%COMP%]   .info-icon[_ngcontent-%COMP%]{font-size:11px;margin-left:5px;height:10px;cursor:pointer;margin-top:2.5px;color:grey;position:absolute}.flag-change-style[_ngcontent-%COMP%]   .required-star[_ngcontent-%COMP%]{color:#ec5f6e}.flag-change-style[_ngcontent-%COMP%]   .full-width-stepper[_ngcontent-%COMP%]{width:100%}.flag-change-style[_ngcontent-%COMP%]   .footer-buttons[_ngcontent-%COMP%]{position:sticky;bottom:0;z-index:100;background-color:#f6f6f6;height:10vh;display:flex;margin-top:3px;border-top:1.3px solid #dadce2}.flag-change-style[_ngcontent-%COMP%]   .button-skip[_ngcontent-%COMP%]{margin-top:10px}.flag-change-style[_ngcontent-%COMP%]   .button-back[_ngcontent-%COMP%], .flag-change-style[_ngcontent-%COMP%]   .button-skip[_ngcontent-%COMP%]{align-items:center;font-weight:700!important;border-radius:5px;color:#515965;font-family:Roboto;font-size:14px;letter-spacing:-.02em;text-align:left}.flag-change-style[_ngcontent-%COMP%]   .button-back[_ngcontent-%COMP%]{margin-top:20px;margin-left:72px;cursor:pointer}.flag-change-style[_ngcontent-%COMP%]   textarea.mat-input-element[_ngcontent-%COMP%]{border:1px solid #d3d3d3;margin-left:15px;border-radius:5px;padding-top:11px;padding-left:10px;background-color:#fff;margin-top:4px;font-family:Roboto;font-size:13px;font-weight:400;line-height:16px;letter-spacing:0;text-align:left;color:#45546e;max-height:42px}.flag-change-style[_ngcontent-%COMP%]   .font-family[_ngcontent-%COMP%]{font-family:Roboto}.flag-change-style[_ngcontent-%COMP%]   .content-title[_ngcontent-%COMP%]{font-size:14px;font-size:12px}.flag-change-style[_ngcontent-%COMP%]   .content-title[_ngcontent-%COMP%], .flag-change-style[_ngcontent-%COMP%]   .content-title1[_ngcontent-%COMP%]{padding-top:20px;color:#6e7b8f;font-family:Roboto;font-weight:500;line-height:16px;letter-spacing:.02em;text-align:left}.flag-change-style[_ngcontent-%COMP%]   .content-title1[_ngcontent-%COMP%]{font-size:14px;font-size:10.5px}.flag-change-style[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]{border-radius:5px;display:flow;background-color:#fff;padding-left:0!important;padding-right:0!important}.flag-change-style[_ngcontent-%COMP%]   .address-title[_ngcontent-%COMP%]{font-size:16px;color:#e42626}.flag-change-style[_ngcontent-%COMP%]     .mat-horizontal-stepper-header-container{background-color:#fff;width:90%;margin-left:7%;padding-bottom:0}.flag-change-style[_ngcontent-%COMP%]     .mat-slide-toggle-bar{background-color:#d3d3d3}.flag-change-style[_ngcontent-%COMP%]     .mat-slide-toggle-thumb{background-color:var(--intButton)!important;box-shadow:none!important}.flag-change-style[_ngcontent-%COMP%]     .mat-slide-toggle-thumb:hover{box-shadow:none!important}.flag-change-style[_ngcontent-%COMP%]     .mat-checkbox-inner-container{background-color:#fff!important}.flag-change-style[_ngcontent-%COMP%]     .mat-step-header .mat-step-icon-selected{background-color:#fff;color:#000}.flag-change-style[_ngcontent-%COMP%]     .mat-step-header .mat-step-icon-state-done{background-color:#32cd32;color:#fff}.flag-change-style[_ngcontent-%COMP%]     .mat-horizontal-content-container{overflow:hidden!important;padding:0!important}.flag-change-style[_ngcontent-%COMP%]     .mat-step-header .mat-step-icon-state-edit{background-color:#cd5c5c;color:#fff}.flag-change-style[_ngcontent-%COMP%]     .mat-mdc-tooltip{transform:scale(1.5)!important;position:relative!important}.flag-change-style[_ngcontent-%COMP%]     .primary-tooltip{--mdc-plain-tooltip-container-color:#fff!important;--mdc-plain-tooltip-supporting-text-color:#000!important;border:1px solid #000!important}.flag-change-style[_ngcontent-%COMP%]   .mat-form-field-appearance-outline[_ngcontent-%COMP%]   .mat-form-field-flex[_ngcontent-%COMP%]{padding:0 .75em;position:relative;background-color:#fff!important}.flag-change-style[_ngcontent-%COMP%]   .customer-name[_ngcontent-%COMP%]{border:1px solid #d3d3d3;height:34px;border-radius:5px;padding:8px;background:#e8e9ee}.flag-change-style[_ngcontent-%COMP%]     .mat-horizontal-stepper-footer{background-color:grey}.flag-change-style[_ngcontent-%COMP%]   .center-container[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}.flag-change-style[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]{margin:0 10px;max-width:300px;width:270px;height:115px;cursor:pointer;padding:24px 16px;border-radius:4px;border:2px solid #fff;gap:8px;background-color:#f6f6f6}.flag-change-style[_ngcontent-%COMP%]   .template_button[_ngcontent-%COMP%]{justify-content:center;align-items:center}.flag-change-style[_ngcontent-%COMP%]   .card_content[_ngcontent-%COMP%]{margin:0 10px;max-width:300px;width:270px;height:115px;align-items:center!important}.flag-change-style[_ngcontent-%COMP%]   .mat-button-toggle[_ngcontent-%COMP%]{background-color:none;width:100px}.flag-change-style[_ngcontent-%COMP%]   .mat-button-toggle[_ngcontent-%COMP%]:hover{background-color:none}.flag-change-style[_ngcontent-%COMP%]   .mat-button-toggle-checked[_ngcontent-%COMP%]{background-color:var(--intButton)!important}.flag-change-style[_ngcontent-%COMP%]   .main_card[_ngcontent-%COMP%]{gap:8px!important}.flag-change-style[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%]{justify-content:left}.flag-change-style[_ngcontent-%COMP%]   .card-text[_ngcontent-%COMP%]{justify-content:left;width:180px}.flag-change-style[_ngcontent-%COMP%]   .custom-button[_ngcontent-%COMP%]{margin-right:20px;max-width:300px;width:242px;height:126px;padding:24px 16px;gap:8px;background-color:#fff;border:2px solid #fff;border-radius:5px;box-shadow:0 2px 4px rgba(0,0,0,.1);margin-top:-40px}.flag-change-style[_ngcontent-%COMP%]   .custom-button[_ngcontent-%COMP%]:hover{border-color:var(--intButton);transform:translateY(-2px)}.flag-change-style[_ngcontent-%COMP%]   .centered-icon[_ngcontent-%COMP%]{font-size:32px;color:var(--intButton)!important}.flag-change-style[_ngcontent-%COMP%]   .card-radio[_ngcontent-%COMP%]{position:absolute;top:10px;right:10px;display:flex;align-items:center}.flag-change-style[_ngcontent-%COMP%]   .card-radio[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]:checked + span[_ngcontent-%COMP%]{background:var(--intButton);border-color:var(--intButton)}.flag-change-style[_ngcontent-%COMP%]   .card-radio[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]{margin-right:5px;background:var(--intButton)}.flag-change-style[_ngcontent-%COMP%]   .card.selected[_ngcontent-%COMP%], .flag-change-style[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]:hover{border-color:var(--intButton)}.flag-change-style[_ngcontent-%COMP%]   .card.selected[_ngcontent-%COMP%]   .card-radio[_ngcontent-%COMP%]{opacity:1}.flag-change-style[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]{margin-left:-10px;margin-top:10px;overflow-x:auto;white-space:nowrap;padding-bottom:10px;padding-top:5px}.flag-change-style[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{padding:27.5px 16px;border:1px solid #ccc;background-color:#fff;cursor:pointer;height:80px}.flag-change-style[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   button.active[_ngcontent-%COMP%]{background:linear-gradient(0deg,var(--shades),var(--shades));color:var(--intButton)!important;border:1.4px solid var(--intButton)!important}.flag-change-style[_ngcontent-%COMP%]   .button-content[_ngcontent-%COMP%]{text-align:left;white-space:normal;margin-top:-27px;position:absolute}.flag-change-style[_ngcontent-%COMP%]   .button-container[_ngcontent-%COMP%]{margin-top:10px;margin-left:auto}.flag-change-style[_ngcontent-%COMP%]   .button-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0;font-size:16px;color:#45546e;font-family:Roboto;font-size:12px;font-weight:700;line-height:16px;letter-spacing:.02em;text-align:left;text-overflow:ellipsis;overflow:hidden;width:123px;text-wrap:nowrap}.flag-change-style[_ngcontent-%COMP%]   .button-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:14px;word-wrap:normal;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.flag-change-style[_ngcontent-%COMP%]   .truncate[_ngcontent-%COMP%]{max-width:150px}.flag-change-style[_ngcontent-%COMP%]   .cus-button[_ngcontent-%COMP%]{margin:0 10px;max-width:175px;width:221px;height:80px;padding:24px 16px;gap:8px;background-color:#fff;border:2px solid #fff;border-radius:5px;box-shadow:0 2px 4px rgba(0,0,0,.1)}.flag-change-style[_ngcontent-%COMP%]   .cus-button[_ngcontent-%COMP%]:hover{border-color:var(--intButton)!important;transform:translateY(-2px)}.flag-change-style[_ngcontent-%COMP%]   .custom-input[_ngcontent-%COMP%]{background-color:#fff;width:100%}.flag-change-style[_ngcontent-%COMP%]   .custom-mat-form-field[_ngcontent-%COMP%]{background-color:#fff}.flag-change-style[_ngcontent-%COMP%]   .custom-mat-form-field[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{background-color:inherit;border:none;outline:none;width:100%;padding:8px}.flag-change-style[_ngcontent-%COMP%]   .field-title[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#6e7b8f;margin-left:18px}.flag-change-style[_ngcontent-%COMP%]   .outlineCheck[_ngcontent-%COMP%]{outline:solid;outline-color:#e8e9ee!important;outline-width:2px;border-radius:3px;margin-top:2px;background-color:#fff}.flag-change-style[_ngcontent-%COMP%]   .highlightCard[_ngcontent-%COMP%]{border-color:var(--intButton);outline-color:var(--intButton)!important}.flag-change-style[_ngcontent-%COMP%]   .iconbtnSave[_ngcontent-%COMP%]{background-color:var(--intButton)!important;outline:solid;outline-color:var(--intButton)!important;outline-width:1px;border-radius:3px;color:#fff;box-shadow:-1 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12);margin-top:10px;justify-content:center;font-family:Roboto;font-size:14px;font-weight:400;line-height:27px;letter-spacing:0;text-align:left}.flag-change-style[_ngcontent-%COMP%]   .iconbtnCancel[_ngcontent-%COMP%]{outline:solid;outline-color:#bec9d9!important;outline-width:1px;border-radius:3px;color:#fff;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12);width:80px}.flag-change-style[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}.flag-change-style[_ngcontent-%COMP%]   .orgsdname[_ngcontent-%COMP%]{font-family:Roboto;font-size:13px;font-weight:400;line-height:16px;letter-spacing:0;text-align:left;margin-left:35px;margin-top:3px;color:#45546e;text-overflow:ellipsis;overflow:hidden;white-space:nowrap;padding-left:21px}.flag-change-style[_ngcontent-%COMP%]   .orgdname[_ngcontent-%COMP%]{height:16px;margin-left:32px;padding-left:14px}.flag-change-style[_ngcontent-%COMP%]   .orgdname[_ngcontent-%COMP%], .flag-change-style[_ngcontent-%COMP%]   .orgename[_ngcontent-%COMP%]{width:288px;font-family:Roboto;font-size:13px;font-weight:400;line-height:16px;letter-spacing:0;text-align:left;margin-top:3px;color:#45546e;text-overflow:ellipsis;overflow:hidden;white-space:nowrap}.flag-change-style[_ngcontent-%COMP%]   .orgename[_ngcontent-%COMP%]{height:15px;margin-left:-16px;padding-left:5px}.flag-change-style[_ngcontent-%COMP%]   .mat-body[_ngcontent-%COMP%]{position:relative;width:100%}.flag-change-style[_ngcontent-%COMP%]   .mat-dialog-container[_ngcontent-%COMP%]{overflow:hidden!important}.flag-change-style[_ngcontent-%COMP%]   .create-project-img[_ngcontent-%COMP%]{width:160px;height:122px;top:106px;left:173px}.flag-change-style[_ngcontent-%COMP%]   .watermark[_ngcontent-%COMP%]{position:absolute;padding:0;width:160px;height:115px;margin:-35px 0 0}.flag-change-style[_ngcontent-%COMP%]   .watermark[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{display:block;max-width:100%;height:auto}.flag-change-style[_ngcontent-%COMP%]   .watermark[_ngcontent-%COMP%]:after{content:"";position:absolute;display:block;left:0;top:0;width:100%;height:100%;background:transparent linear-gradient(180deg,transparent 0,rgba(0,0,0,.6) 70%) repeat 0 0;z-index:1}.flag-change-style[_ngcontent-%COMP%]   .add_new_color[_ngcontent-%COMP%]{width:81px;height:32px;padding:6px;border-radius:4px;border:1px solid #45546e;gap:8px;margin-left:65px;text-align:center;cursor:pointer;margin-bottom:20px}.flag-change-style[_ngcontent-%COMP%]   .field-row[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:10px}.flag-change-style[_ngcontent-%COMP%]   .field[_ngcontent-%COMP%]{flex:1;margin-right:10px}.flag-change-style[_ngcontent-%COMP%]   .Financial-body[_ngcontent-%COMP%]{width:932px;height:188px;gap:24px}.flag-change-style[_ngcontent-%COMP%]   .center-body[_ngcontent-%COMP%]{justify-content:center;align-items:center}.flag-change-style[_ngcontent-%COMP%]   .colored-check-icon[_ngcontent-%COMP%]{color:var(--intButton)!important}.flag-change-style[_ngcontent-%COMP%]   .request[_ngcontent-%COMP%]{width:159px;height:36px;padding:8px,12px,8px,12px;border-radius:4px;gap:8px;background:linear-gradient(0deg,#fff,#fff);border:1px solid #45546e;margin-top:46.5px;margin-left:40px;display:flex}.flag-change-style[_ngcontent-%COMP%]   .request-name[_ngcontent-%COMP%]{font-family:Roboto;font-size:14px;font-weight:700;line-height:16px;letter-spacing:-.02em;text-align:left;color:#45546e;margin-top:10px;margin-left:6px}.flag-change-style[_ngcontent-%COMP%]   .request-icon[_ngcontent-%COMP%]{margin-left:0;margin-top:5px;color:#45546e}.flag-change-style[_ngcontent-%COMP%]   .people-body[_ngcontent-%COMP%]{width:970px;height:32px;padding:8px,4px,8px,4px;border:0 0 1px;gap:10px;border-bottom:1px solid #d4d6d8;margin-left:4px;margin-top:20px;display:flex}.flag-change-style[_ngcontent-%COMP%]   .people-name[_ngcontent-%COMP%]{width:51px;height:16px;font-family:Roboto;font-size:14px;font-weight:400;line-height:16px;letter-spacing:0;text-align:center;color:#000;font-weight:700;margin-left:-120px}.flag-change-style[_ngcontent-%COMP%]   .people-count[_ngcontent-%COMP%]{width:24px;height:16px;padding:0,8px,0,8px;border-radius:6px;gap:10px;background:var(--intButton)!important;margin-left:-130px}.flag-change-style[_ngcontent-%COMP%]   .people-count-result[_ngcontent-%COMP%]{width:8px;height:16px;font-family:Roboto;font-size:14px;font-weight:400;line-height:16px;letter-spacing:0;text-align:center;color:#fff;margin-left:7px}.flag-change-style[_ngcontent-%COMP%]   .risk-body[_ngcontent-%COMP%]{width:84px;height:30px;padding:5px;border-radius:4px;gap:8px;background:#ff3a46;display:flex;margin-top:48px;margin-left:-69px}.flag-change-style[_ngcontent-%COMP%]   .risk-name[_ngcontent-%COMP%]{width:47px;height:16px;font-family:Roboto;font-size:14px;font-weight:400;line-height:16px;letter-spacing:0;text-align:left;color:#fff;margin-top:2px}.flag-change-style[_ngcontent-%COMP%]   .risk-status-wrapper[_ngcontent-%COMP%]{display:flex;align-items:center;margin-top:15px;zoom:85%;gap:8px}.flag-change-style[_ngcontent-%COMP%]   .risk[_ngcontent-%COMP%]{color:#45546e;font-family:Roboto;font-size:14px}.flag-change-style[_ngcontent-%COMP%]   .rc-stat[_ngcontent-%COMP%]{width:86px;height:30px;padding:4px 16px;display:flex;align-items:center;justify-content:center;border-radius:4px;gap:4px;color:#fff;font-size:12px;position:relative}.flag-change-style[_ngcontent-%COMP%]   .status[_ngcontent-%COMP%]{font-family:Roboto;font-size:14px;font-weight:500}.flag-change-style[_ngcontent-%COMP%]   .scroll[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{min-height:30px!important}.flag-change-style[_ngcontent-%COMP%]   .risk-icon[_ngcontent-%COMP%]{font-size:20px;color:#fff}.flag-change-style[_ngcontent-%COMP%]     .mat-button-toggle-button{border:0;background:none;color:inherit;padding:0;margin:0;font:inherit;outline:none;width:100%;cursor:pointer;height:40px!important;align-items:center}.flag-change-style[_ngcontent-%COMP%]   .para[_ngcontent-%COMP%]{width:120px;font-family:Roboto;font-size:11px;font-weight:400;line-height:12px;letter-spacing:0;text-align:left;color:#8b95a5;text-overflow:ellipsis;overflow:hidden;display:-webkit-box;-webkit-line-clamp:3;-webkit-box-orient:vertical;max-height:3.6em;margin-top:4px}.flag-change-style[_ngcontent-%COMP%]     .mat-radio-outer-circle{border-color:var(--intButton)!important}.flag-change-style[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-wrapper{padding-bottom:0!important}.flag-change-style[_ngcontent-%COMP%]     .mat-form-field{position:relative;text-align:left;display:flow}.flag-change-style[_ngcontent-%COMP%]   .resources[_ngcontent-%COMP%]{width:1200px;height:16px;gap:42px;display:flex;margin-left:15px;margin-top:10px;margin-bottom:10px}.flag-change-style[_ngcontent-%COMP%]   .resources-name[_ngcontent-%COMP%]{width:160px}.flag-change-style[_ngcontent-%COMP%]   .resources-name[_ngcontent-%COMP%], .flag-change-style[_ngcontent-%COMP%]   .resources-start-date[_ngcontent-%COMP%]{height:16px;font-family:Roboto;font-size:12px;font-weight:700;line-height:16px;letter-spacing:.02em;text-align:left;color:#6e7b8f}.flag-change-style[_ngcontent-%COMP%]   .resources-start-date[_ngcontent-%COMP%]{width:185px}.flag-change-style[_ngcontent-%COMP%]   .resources-end-date[_ngcontent-%COMP%]{width:185px;margin-left:-50px}.flag-change-style[_ngcontent-%COMP%]   .resources-end-date[_ngcontent-%COMP%], .flag-change-style[_ngcontent-%COMP%]   .resources-role[_ngcontent-%COMP%]{height:16px;font-family:Roboto;font-size:12px;font-weight:700;line-height:16px;letter-spacing:.02em;text-align:left;color:#6e7b8f}.flag-change-style[_ngcontent-%COMP%]   .resources-role[_ngcontent-%COMP%]{width:170px;margin-left:-55px}.flag-change-style[_ngcontent-%COMP%]   .resources-split[_ngcontent-%COMP%]{width:280px;height:16px;font-family:Roboto;font-size:12px;font-weight:700;line-height:16px;letter-spacing:.02em;text-align:left;color:#6e7b8f;margin-left:-23px}.flag-change-style[_ngcontent-%COMP%]   .resources-billable[_ngcontent-%COMP%]{margin-left:-178px}.flag-change-style[_ngcontent-%COMP%]   .resources-billable[_ngcontent-%COMP%], .flag-change-style[_ngcontent-%COMP%]   .resources-head[_ngcontent-%COMP%]{width:134px;height:16px;font-family:Roboto;font-size:12px;font-weight:700;line-height:16px;letter-spacing:.02em;text-align:left;color:#6e7b8f}.flag-change-style[_ngcontent-%COMP%]   .resources-head[_ngcontent-%COMP%]{margin-left:-109px}.flag-change-style[_ngcontent-%COMP%]   .resources-body[_ngcontent-%COMP%]{width:100%;gap:12px;overflow-y:auto;padding-bottom:30px;height:267px}.flag-change-style[_ngcontent-%COMP%]   .resources-data[_ngcontent-%COMP%]{width:950px;height:32px;gap:16px}.flag-change-style[_ngcontent-%COMP%]   .resources-body-name[_ngcontent-%COMP%]{width:100%;height:16px;gap:8px;display:flex;margin-top:15px;margin-left:25px}.flag-change-style[_ngcontent-%COMP%]   .person-name[_ngcontent-%COMP%]{width:155px;font-family:Roboto;font-size:12px;font-weight:400;line-height:16px;letter-spacing:.02em;text-align:left;color:#111434;margin-top:12px}.flag-change-style[_ngcontent-%COMP%]   .person-start-date[_ngcontent-%COMP%]{margin-left:15px}.flag-change-style[_ngcontent-%COMP%]   .person-end-date[_ngcontent-%COMP%], .flag-change-style[_ngcontent-%COMP%]   .person-start-date[_ngcontent-%COMP%]{width:150px;padding:8px,12px,8px,12px;border:1px;gap:8px;border-radius:5px;display:flow;background-color:#fff;padding-left:0!important;padding-right:0!important;font-size:13.4px;font-family:Roboto}.flag-change-style[_ngcontent-%COMP%]   .person-end-date[_ngcontent-%COMP%]{margin-left:19px}.flag-change-style[_ngcontent-%COMP%]   .person-role[_ngcontent-%COMP%]{width:170px;margin-left:13px;font-size:12.9px;margin-top:2.5px;color:#45546e}.flag-change-style[_ngcontent-%COMP%]   .person-role[_ngcontent-%COMP%], .flag-change-style[_ngcontent-%COMP%]   .person-split[_ngcontent-%COMP%]{padding:8px,12px,8px,12px;border:1px;gap:8px;border-radius:5px;display:flow;background-color:#fff;padding-left:0!important;padding-right:0!important;font-family:Roboto}.flag-change-style[_ngcontent-%COMP%]   .person-split[_ngcontent-%COMP%]{width:130px;margin-left:12px;font-size:13.4px}.flag-change-style[_ngcontent-%COMP%]   .person-billable[_ngcontent-%COMP%]{width:16px;height:16px;border-radius:4px;margin-left:10.5px;margin-top:14px}.flag-change-style[_ngcontent-%COMP%]   .person-head[_ngcontent-%COMP%]{width:16px;height:16px;border-radius:4px;margin-left:43px;margin-top:14px}.flag-change-style[_ngcontent-%COMP%]   .person-remove[_ngcontent-%COMP%]{width:16px;height:16px;margin-left:-60px;font-size:15px;margin-top:13px}.flag-change-style[_ngcontent-%COMP%]   .person-img[_ngcontent-%COMP%]{width:16px;height:16px;margin-top:8px;margin-left:-10px;font-size:21px;color:#722ed0}.flag-change-style[_ngcontent-%COMP%]   .refresh-icon[_ngcontent-%COMP%]{color:red}.flag-change-style[_ngcontent-%COMP%]   .refresh-icon[_ngcontent-%COMP%], .flag-change-style[_ngcontent-%COMP%]   .valid-icon[_ngcontent-%COMP%]{font-size:14px;position:absolute;top:62%;right:-2px;transform:translateY(-50%);cursor:pointer}.flag-change-style[_ngcontent-%COMP%]   .valid-icon[_ngcontent-%COMP%]{color:#32cd32}.flag-change-style[_ngcontent-%COMP%]   .invalid-icon[_ngcontent-%COMP%]{color:red;font-size:14px;position:absolute;top:62%;right:-2px;transform:translateY(-50%);cursor:pointer}.flag-change-style[_ngcontent-%COMP%]   .minimize-button[_ngcontent-%COMP%]{cursor:pointer;margin-left:250px!important;margin-top:-6px;color:grey;position:absolute;font-size:23px}.flag-change-style[_ngcontent-%COMP%]   .loader-container[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;height:100vh;background-color:#fff!important}.flag-change-style[_ngcontent-%COMP%]   .service-type-radio[_ngcontent-%COMP%]{margin-left:120px;margin-top:-33px;position:absolute}.flag-change-style[_ngcontent-%COMP%]   .service-type-radio[_ngcontent-%COMP%]   .radio-button-unchecked[_ngcontent-%COMP%]{color:#dadce2;font-size:16px}.flag-change-style[_ngcontent-%COMP%]   .service-type-radio[_ngcontent-%COMP%]   .radio-button-checked[_ngcontent-%COMP%]{color:var(--intButton)!important;font-size:16px}.flag-change-style[_ngcontent-%COMP%]   .loader[_ngcontent-%COMP%]{border:4px solid #fff!important;border-top:4px solid var(--intButton)!important;border-radius:50%;width:40px;height:40px;margin-bottom:25%!important;animation:spin 1s linear infinite}@keyframes spin{0%{transform:rotate(0deg)}to{transform:rotate(1turn)}}.flag-change-style[_ngcontent-%COMP%]   .internal-stakeholders[_ngcontent-%COMP%]{height:294px;overflow-x:hidden;margin-left:9px;overflow-y:hidden}.flag-change-style[_ngcontent-%COMP%]   .add_member[_ngcontent-%COMP%]{border-radius:4px;border:1px solid #45546e}.flag-change-style[_ngcontent-%COMP%]   .add-member[_ngcontent-%COMP%], .flag-change-style[_ngcontent-%COMP%]   .add_member[_ngcontent-%COMP%]{width:143px;height:32px;padding:6px;gap:8px;text-align:center;cursor:pointer;margin-top:-5px;margin-left:703px}.flag-change-style[_ngcontent-%COMP%]   [_ngcontent-%COMP%]::-webkit-scrollbar{height:4px!important}.flag-change-style[_ngcontent-%COMP%]   .project_code[_ngcontent-%COMP%]{width:165px}.flag-change-style[_ngcontent-%COMP%]   .project_code[_ngcontent-%COMP%], .flag-change-style[_ngcontent-%COMP%]   .project_name[_ngcontent-%COMP%]{border-radius:5px;display:flow;background-color:#fff;padding-left:0!important;padding-right:0!important;font-size:12px;font-family:Roboto;margin-top:10px}.flag-change-style[_ngcontent-%COMP%]   .project_name[_ngcontent-%COMP%]{width:534px}.flag-change-style[_ngcontent-%COMP%]   .sow_reference_number[_ngcontent-%COMP%]{font-size:12px}.flag-change-style[_ngcontent-%COMP%]   .portfolio[_ngcontent-%COMP%], .flag-change-style[_ngcontent-%COMP%]   .sow_reference_number[_ngcontent-%COMP%]{border-radius:5px;display:flow;background-color:#fff;padding-left:0!important;padding-right:0!important;width:190px;font-family:Roboto;margin-top:10px}.flag-change-style[_ngcontent-%COMP%]   .portfolio[_ngcontent-%COMP%]{font-size:10.9px}.flag-change-style[_ngcontent-%COMP%]   .customer_details[_ngcontent-%COMP%]{border-radius:5px;display:flow;background-color:#e8e9ee;padding-left:0!important;padding-right:0!important;width:230px;font-size:12px;font-family:Roboto;margin-top:10px}.flag-change-style[_ngcontent-%COMP%]   .end_date[_ngcontent-%COMP%], .flag-change-style[_ngcontent-%COMP%]   .start_date[_ngcontent-%COMP%]{background-color:#fff;width:165px;font-size:12px}.flag-change-style[_ngcontent-%COMP%]   .end_date[_ngcontent-%COMP%], .flag-change-style[_ngcontent-%COMP%]   .project_type[_ngcontent-%COMP%], .flag-change-style[_ngcontent-%COMP%]   .start_date[_ngcontent-%COMP%]{border-radius:5px;display:flow;padding-left:0!important;padding-right:0!important;font-family:Roboto;margin-top:10px}.flag-change-style[_ngcontent-%COMP%]   .project_type[_ngcontent-%COMP%]{width:178px;font-size:10.9px}.flag-change-style[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]::-webkit-scrollbar{width:5px!important;height:5px!important}.flag-change-style[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{min-height:10px;border-radius:4px!important;background-color:#babfc4!important}.flag-change-style[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]:not(.mat-form-field-appearance-legacy)   .mat-form-field-suffix[_ngcontent-%COMP%]   .mat-icon-button[_ngcontent-%COMP%]   .mat-datepicker-toggle-default-icon[_ngcontent-%COMP%], .flag-change-style[_ngcontent-%COMP%]     .mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button .mat-datepicker-toggle-default-icon{margin:auto;color:#db6e61}.flag-change-style[_ngcontent-%COMP%]   .custom-datepicker-popup[_ngcontent-%COMP%]{max-width:200px;max-height:200px}.flag-change-style[_ngcontent-%COMP%]   .division[_ngcontent-%COMP%], .flag-change-style[_ngcontent-%COMP%]   .entity[_ngcontent-%COMP%], .flag-change-style[_ngcontent-%COMP%]   .subDivision[_ngcontent-%COMP%]{background-color:#fff;color:#5f6c81}.flag-change-style[_ngcontent-%COMP%]   .division[_ngcontent-%COMP%], .flag-change-style[_ngcontent-%COMP%]   .entity[_ngcontent-%COMP%], .flag-change-style[_ngcontent-%COMP%]   .opportunity[_ngcontent-%COMP%], .flag-change-style[_ngcontent-%COMP%]   .subDivision[_ngcontent-%COMP%]{border-radius:5px;display:flow;padding-left:0!important;padding-right:0!important;font-size:10.9px;font-family:Roboto;margin-top:10px}.flag-change-style[_ngcontent-%COMP%]   .opportunity[_ngcontent-%COMP%]{background-color:#e8e9ee;color:#45546e;width:87.5px}.flag-change-style[_ngcontent-%COMP%]   .quote[_ngcontent-%COMP%]{background-color:#e8e9ee;font-size:12px;width:64px}.flag-change-style[_ngcontent-%COMP%]   .quote[_ngcontent-%COMP%], .flag-change-style[_ngcontent-%COMP%]   .template[_ngcontent-%COMP%]{border-radius:5px;display:flow;padding-left:0!important;padding-right:0!important;font-family:Roboto;margin-top:10px}.flag-change-style[_ngcontent-%COMP%]   .template[_ngcontent-%COMP%]{background-color:#fff;font-size:10.9px;color:#45546e;width:231px}.flag-change-style[_ngcontent-%COMP%]   .currency[_ngcontent-%COMP%]{font-size:10.9px;color:#45546e;width:102px}.flag-change-style[_ngcontent-%COMP%]   .currency[_ngcontent-%COMP%], .flag-change-style[_ngcontent-%COMP%]   .poNumber[_ngcontent-%COMP%]{border-radius:5px;display:flow;background-color:#fff;padding-left:0!important;padding-right:0!important;font-family:Roboto;margin-top:10px}.flag-change-style[_ngcontent-%COMP%]   .poNumber[_ngcontent-%COMP%]{font-size:12px;width:80.5px}.flag-change-style[_ngcontent-%COMP%]   .poValue[_ngcontent-%COMP%]{font-size:12px;width:100px}.flag-change-style[_ngcontent-%COMP%]   .poReference[_ngcontent-%COMP%], .flag-change-style[_ngcontent-%COMP%]   .poValue[_ngcontent-%COMP%]{border-radius:5px;display:flow;background-color:#e8e9ee;padding-left:0!important;padding-right:0!important;font-family:Roboto;margin-top:10px}.flag-change-style[_ngcontent-%COMP%]   .poReference[_ngcontent-%COMP%]{font-size:12px!important;width:65.5px}.flag-change-style[_ngcontent-%COMP%]   .partner[_ngcontent-%COMP%]{font-size:11px!important;width:89px}.flag-change-style[_ngcontent-%COMP%]   .partner[_ngcontent-%COMP%], .flag-change-style[_ngcontent-%COMP%]   .paymentTerms[_ngcontent-%COMP%]{border-radius:5px;display:flow;background-color:#e8e9ee;padding-left:0!important;padding-right:0!important;font-family:Roboto;margin-top:10px}.flag-change-style[_ngcontent-%COMP%]   .paymentTerms[_ngcontent-%COMP%]{font-size:11px;width:100px}.flag-change-style[_ngcontent-%COMP%]   .poNumber2[_ngcontent-%COMP%]{background-color:#fff}.flag-change-style[_ngcontent-%COMP%]   .poNumber2[_ngcontent-%COMP%], .flag-change-style[_ngcontent-%COMP%]   .poValue2[_ngcontent-%COMP%]{border-radius:5px;display:flow;padding-left:0!important;padding-right:0!important;font-size:12px;font-family:Roboto;width:200px;margin-top:10px}.flag-change-style[_ngcontent-%COMP%]   .poValue2[_ngcontent-%COMP%]{background-color:#e8e9ee}.flag-change-style[_ngcontent-%COMP%]   .poReference2[_ngcontent-%COMP%]{font-size:12px!important}.flag-change-style[_ngcontent-%COMP%]   .partner2[_ngcontent-%COMP%], .flag-change-style[_ngcontent-%COMP%]   .poReference2[_ngcontent-%COMP%]{border-radius:5px;display:flow;background-color:#e8e9ee;padding-left:0!important;padding-right:0!important;font-family:Roboto;width:200px;margin-top:10px}.flag-change-style[_ngcontent-%COMP%]   .partner2[_ngcontent-%COMP%]{font-size:11px!important}.flag-change-style[_ngcontent-%COMP%]   .paymentTerms2[_ngcontent-%COMP%]{border-radius:5px;display:flow;background-color:#e8e9ee;padding-left:0!important;padding-right:0!important;font-size:12px;font-family:Roboto;width:200px;margin-top:10px}.flag-change-style[_ngcontent-%COMP%]   .adGroup[_ngcontent-%COMP%]{font-size:10.9px;margin-top:10px}.flag-change-style[_ngcontent-%COMP%]   .adGroup[_ngcontent-%COMP%], .flag-change-style[_ngcontent-%COMP%]   .employeeName[_ngcontent-%COMP%]{border-radius:5px;display:flow;background-color:#fff;padding-left:0!important;padding-right:0!important;font-family:Roboto;color:#45546e}.flag-change-style[_ngcontent-%COMP%]   .employeeName[_ngcontent-%COMP%]{font-size:17.9px;margin-top:-1px}.flag-change-style[_ngcontent-%COMP%]   .workLocation[_ngcontent-%COMP%]{font-size:10.9px;color:#45546e}.flag-change-style[_ngcontent-%COMP%]   .location[_ngcontent-%COMP%], .flag-change-style[_ngcontent-%COMP%]   .workLocation[_ngcontent-%COMP%]{border-radius:5px;display:flow;background-color:#fff;padding-left:0!important;padding-right:0!important;font-family:Roboto;margin-top:10px}.flag-change-style[_ngcontent-%COMP%]   .location[_ngcontent-%COMP%]{font-size:12px}.flag-change-style[_ngcontent-%COMP%]   .holidayCalendar[_ngcontent-%COMP%], .flag-change-style[_ngcontent-%COMP%]   .leave_paid[_ngcontent-%COMP%]{border-radius:5px;display:flow;background-color:#fff;padding-left:0!important;padding-right:0!important;font-size:10.9px;font-family:Roboto;color:#45546e;margin-top:10px}.flag-change-style[_ngcontent-%COMP%]   .leave_paid[_ngcontent-%COMP%]{width:90px}.flag-change-style[_ngcontent-%COMP%]   .holiday_paid[_ngcontent-%COMP%]{border-radius:5px;display:flow;background-color:#fff;padding-left:0!important;padding-right:0!important;font-size:10.9px;font-family:Roboto;color:#45546e;margin-top:10px;width:94px}.flag-change-style[_ngcontent-%COMP%]   .monthly_hours[_ngcontent-%COMP%]{width:92px}.flag-change-style[_ngcontent-%COMP%]   .daily_working_hours[_ngcontent-%COMP%], .flag-change-style[_ngcontent-%COMP%]   .monthly_hours[_ngcontent-%COMP%]{border-radius:5px;display:flow;background-color:#fff;padding-left:0!important;padding-right:0!important;font-size:12px;font-family:Roboto;margin-top:10px}.flag-change-style[_ngcontent-%COMP%]   .daily_working_hours[_ngcontent-%COMP%]{width:98px}.flag-change-style[_ngcontent-%COMP%]   [_ngcontent-%COMP%]::-webkit-scrollbar-thumb{min-height:140px;border-radius:4px!important;background-color:#babfc4!important}.flag-change-style[_ngcontent-%COMP%]   .mat-datepicker-content[_ngcontent-%COMP%]   .mat-calendar-next-button[_ngcontent-%COMP%], .flag-change-style[_ngcontent-%COMP%]   .mat-datepicker-content[_ngcontent-%COMP%]   .mat-calendar-previous-button[_ngcontent-%COMP%], .flag-change-style[_ngcontent-%COMP%]     .mat-datepicker-toggle{color:var(--intButton)!important}.flag-change-style[_ngcontent-%COMP%]     .green-spinner circle{stroke:#fff!important}.flag-change-style[_ngcontent-%COMP%]   .project-code[_ngcontent-%COMP%]{padding-right:32px!important}.flag-change-style[_ngcontent-%COMP%]   .template-font[_ngcontent-%COMP%]{color:#45546e;font-family:Roboto;font-style:normal;font-weight:400;text-transform:capitalize}.flag-change-style[_ngcontent-%COMP%]   .input-time[_ngcontent-%COMP%]{font-size:14px!important}.flag-change-style[_ngcontent-%COMP%]   .ad-group[_ngcontent-%COMP%]{margin-left:8px}.flag-change-style[_ngcontent-%COMP%]   .reason[_ngcontent-%COMP%]{background-color:#e8e9ee}.flag-change-style[_ngcontent-%COMP%]   .reason[_ngcontent-%COMP%], .flag-change-style[_ngcontent-%COMP%]   .reason2[_ngcontent-%COMP%]{border-radius:5px;display:flow;padding-left:0!important;padding-right:0!important;font-size:10.9px;font-family:Roboto;color:#45546e;margin-top:10px;width:71px}.flag-change-style[_ngcontent-%COMP%]   .reason2[_ngcontent-%COMP%]{background-color:#fff}.flag-change-style[_ngcontent-%COMP%]   .po_date[_ngcontent-%COMP%], .flag-change-style[_ngcontent-%COMP%]   .po_date2[_ngcontent-%COMP%]{font-size:11px!important;margin-top:9px;background:#e8e9ee;border-radius:5px;width:120px}.flag-change-style[_ngcontent-%COMP%]   .currency-code[_ngcontent-%COMP%]{position:absolute;font-weight:400;color:#5f6c81}.flag-change-style[_ngcontent-%COMP%]   .currency-code[_ngcontent-%COMP%], .flag-change-style[_ngcontent-%COMP%]   .currency-code1[_ngcontent-%COMP%]{right:10px;top:7px;font-family:Roboto;font-size:12px;line-height:16px;letter-spacing:0;text-align:left}.flag-change-style[_ngcontent-%COMP%]   .currency-code1[_ngcontent-%COMP%]{font-weight:500;color:#6e7b8f}.flag-change-style[_ngcontent-%COMP%]   .template-name[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}.flag-change-style[_ngcontent-%COMP%]   .edit-template[_ngcontent-%COMP%]{font-size:16px;display:flex;align-items:center;position:absolute;top:0;right:0}.flag-change-style[_ngcontent-%COMP%]   .card-image[_ngcontent-%COMP%]{width:200px;height:200px;border-radius:10px}.flag-change-style[_ngcontent-%COMP%]   .toggle-content[_ngcontent-%COMP%]{font-weight:500;margin-left:14px;margin-right:14px;margin-top:3px;font-size:15px;color:grey!important;padding-left:5px;padding-right:5px}.flag-change-style[_ngcontent-%COMP%]   .toggle-content-glow[_ngcontent-%COMP%]{width:max-content;border-radius:50px;display:flex;justify-content:center;color:var(--intButton)!important}.flag-change-style[_ngcontent-%COMP%]   .week-days[_ngcontent-%COMP%]{color:var(--intButton)!important}.flag-change-style[_ngcontent-%COMP%]   .week-button[_ngcontent-%COMP%]{border:1px solid var(--intButton)!important;background-color:var(--shades)!important}.flag-change-style[_ngcontent-%COMP%]   .template-outLine[_ngcontent-%COMP%]{border:2px solid var(--intButton)!important}.flag-change-style[_ngcontent-%COMP%]   .toggle-switch[_ngcontent-%COMP%]   input[type=checkbox][_ngcontent-%COMP%]:checked + .slider[_ngcontent-%COMP%]{transform:translateX(13px)}.flag-change-style[_ngcontent-%COMP%]   .toggle-switch[_ngcontent-%COMP%]   input[type=checkbox][_ngcontent-%COMP%]:not(:checked) + .slider[_ngcontent-%COMP%]{transform:translateX(0)}.flag-change-style[_ngcontent-%COMP%]   .toggle-switch[_ngcontent-%COMP%]   input[type=checkbox][_ngcontent-%COMP%]{display:none}.flag-change-style[_ngcontent-%COMP%]   .toggle-switch[_ngcontent-%COMP%]{display:inline-block;position:relative;width:30px;height:16px;background-color:#fff;border-radius:15px;cursor:pointer;margin-right:0!important;margin-top:4px;border:1px solid #d3d3d3;box-shadow:0 0 5px rgba(221,51,221,.2)}.flag-change-style[_ngcontent-%COMP%]   .slider[_ngcontent-%COMP%]{position:absolute;top:1.3px;left:2px;width:12px;height:12px;background-color:var(--intButton);border-radius:50%;transition:transform .3s}.flag-change-style[_ngcontent-%COMP%]     .checkbox-input{border:none;border-radius:4px;margin-left:15px;margin-right:7px;margin-bottom:3px;color:var(--blue-grey-70,#212529);font-family:Roboto;font-size:13px;font-style:normal;font-weight:500;line-height:16px;letter-spacing:.24px;text-transform:capitalize}.flag-change-style[_ngcontent-%COMP%]     .mat-checkbox-inner-container{display:inline-block;height:14px;line-height:0;margin:auto 8px auto auto;order:0;position:relative;vertical-align:middle;white-space:nowrap;width:14px;flex-shrink:0}.flag-change-style[_ngcontent-%COMP%]     .checkbox-input .mat-checkbox-inner-container{border-radius:10px}.flag-change-style[_ngcontent-%COMP%]     .mat-checkbox-checked.mat-accent .mat-checkbox-background, .flag-change-style[_ngcontent-%COMP%]     .mat-checkbox-indeterminate.mat-accent .mat-checkbox-background, .flag-change-style[_ngcontent-%COMP%]     .mat-checkbox-ripple .mat-ripple-element{background-color:var(--intButton)!important}.flag-change-style[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-outline-thick{color:var(--projectFieldOutline)!important}.flag-change-style[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]:focus, .flag-change-style[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]:hover{border:2px var(--projectFieldOutline)!important;outline:none}']}),t})()}}]);