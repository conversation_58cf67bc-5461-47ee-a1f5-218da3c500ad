(window.webpackJsonp=window.webpackJsonp||[]).push([[748],{"P/lo":function(e,t,n){"use strict";n.r(t),n.d(t,"CreateBillableClaimsPopupComponent",(function(){return de}));var i=n("mrSG"),a=n("0IaG"),l=n("wd/R"),o=n("1G5W"),s=n("XNiG"),r=n("xG9w"),c=n("PSD3"),m=n.n(c),p=n("Kj3r"),d=n("/uUt"),h=n("ofXK"),u=n("bTqV"),g=n("NFeN"),C=n("Qu3c"),f=n("kmnG"),b=n("qFsG"),x=n("3Pt+"),y=n("d3UM"),v=n("bSwM"),_=n("YhS8"),S=(n("Xi0T"),n("Xa2L")),P=n("dlKe"),M=(n("KFJe"),n("JqCM")),I=n("fXoL"),L=n("tk/3"),O=n("FKr1"),w=n("WJ5W");const E=["matSelect"];function k(e,t){1&e&&(I["\u0275\u0275elementStart"](0,"mat-option"),I["\u0275\u0275elementStart"](1,"div",9),I["\u0275\u0275element"](2,"div"),I["\u0275\u0275element"](3,"div"),I["\u0275\u0275element"](4,"div"),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"]())}function A(e,t){if(1&e&&(I["\u0275\u0275elementStart"](0,"mat-option",10),I["\u0275\u0275text"](1),I["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;I["\u0275\u0275property"]("matTooltip",e.kmsis_field)("value",e),I["\u0275\u0275advance"](1),I["\u0275\u0275textInterpolate1"](" ",e.kmsis_field,"")}}function j(e,t){1&e&&(I["\u0275\u0275elementStart"](0,"mat-option"),I["\u0275\u0275elementStart"](1,"div",9),I["\u0275\u0275element"](2,"div"),I["\u0275\u0275element"](3,"div"),I["\u0275\u0275element"](4,"div"),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"]())}function V(e,t){if(1&e){const e=I["\u0275\u0275getCurrentView"]();I["\u0275\u0275elementStart"](0,"div",11),I["\u0275\u0275elementStart"](1,"button",12),I["\u0275\u0275listener"]("click",(function(){return I["\u0275\u0275restoreView"](e),I["\u0275\u0275nextContext"]().closeFilter()})),I["\u0275\u0275text"](2,"Close"),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](3,"button",13),I["\u0275\u0275listener"]("click",(function(t){return I["\u0275\u0275restoreView"](e),I["\u0275\u0275nextContext"]().clearFilter(t)})),I["\u0275\u0275text"](4,"Clear"),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](5,"button",14),I["\u0275\u0275listener"]("click",(function(){return I["\u0275\u0275restoreView"](e),I["\u0275\u0275nextContext"]().applyFilter()})),I["\u0275\u0275text"](6,"Apply"),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"]()}}let D=(()=>{class e{constructor(e){this._http=e,this.apiParamItems=[],this.selectedValues=new I.EventEmitter,this.list=[],this.startIndex=0,this.endIndex=0,this.isLazyLoadingComplete=!1,this.searchCtrl=new x.j,this.selectedValCtrl=new x.j,this.searchString="",this._onDestroy=new s.b,this.isLoading=!1,this.isLazyLoading=!1,this.selectedValuesArray=[],this.msg=new s.b,this.removeOption=e=>{this.msg.next(e)}}ngOnInit(){return Object(i.c)(this,void 0,void 0,(function*(){this.searchCtrl.valueChanges.pipe(Object(o.a)(this._onDestroy),Object(p.a)(700)).subscribe(e=>Object(i.c)(this,void 0,void 0,(function*(){if(null!=e&&""!=e)this.resetIndices(),this.searchString=this.searchCtrl.value,this.fetchData(!1);else if(null!=e&&""==e){let e=10;null!=this.lazyLoadingCount&&this.lazyLoadingCount>10&&(e=this.lazyLoadingCount),this.list.length<e&&(this.resetIndices(),this.initCall())}}))),this.selectedValCtrl.valueChanges.subscribe(e=>{this.selectedValuesArray=e}),this.msg.asObservable(),this.resetIndices(),this.initCall()}))}applyFilter(){this.selectedValues.emit(this.selectedValuesArray),this.closeFilter()}clearFilter(e){this.selectedValuesArray=[];let t=this.selectedValuesArray&&this.selectedValuesArray.length>0;this.selectedValCtrl.reset(),e.stopPropagation(),t?this.applyFilter():(this.applyFilter(),this.closeFilter())}closeFilter(){this.matSelect.close()}initCall(){this.searchString="",this.fetchData(!1)}removeSelectedOption(e){let t=[];t=this.selectedValCtrl.value,t.splice(e,1),this.selectedValCtrl.patchValue([...t]),this.selectedValues.emit(this.selectedValCtrl.value)}fetchData(e){const t={headers:new L.f({"Content-Type":"application/json",Authorization:"Bearer "+this.token})};e?(this.isLazyLoading=!0,this.isLoading=!1):(this.isLazyLoading=!1,this.isLoading=!0);let n={searchText:this.searchString,isFromMulSelSearchInput:!0};null!=this.lazyLoadingCount&&this.lazyLoadingCount>0&&(n.startIndex=this.startIndex,n.endIndex=this.endIndex),n.apiParamItems=this.apiParamItems,this._http.post(this.API_URL?this.API_URL:"",n,t).subscribe(t=>{if(t&&t.messType&&"S"==t.messType&&t.data&&t.data.length>0){let e=r.filter(t.data,(function(e){let t={};for(let i of this.optionLabel)t[i]=e[i];let n=r.where(this.list,t);if(!(n&&n.length>0))return e}),this);this.list=this.list.concat(e);for(let t of this.list){t.kmsis_field="";for(let e=0;e<this.optionLabel.length;e++)0!=e&&(t.kmsis_field+=" - "),t.kmsis_field+=t[this.optionLabel[e]]}null!=this.lazyLoadingCount&&this.lazyLoadingCount>0&&(this.startIndex+=this.lazyLoadingCount,this.endIndex+=this.lazyLoadingCount)}else t&&t.messType&&"S"==t.messType&&t.data&&0==t.data.length&&null!=this.lazyLoadingCount&&this.lazyLoadingCount>0&&(this.isLazyLoadingComplete=!0);e?this.isLazyLoading=!1:this.isLoading=!1},t=>{e?this.isLazyLoading=!1:this.isLoading=!1,console.error(t)})}resetIndices(){this.list=null!=this.selectedValCtrl.value?this.selectedValCtrl.value:[];for(let e of this.list){e.kmsis_field="";for(let t=0;t<this.optionLabel.length;t++)0!=t&&(e.kmsis_field+=" - "),e.kmsis_field+=e[this.optionLabel[t]]}null!=this.lazyLoadingCount&&this.lazyLoadingCount>0&&(this.startIndex=0,this.endIndex=this.lazyLoadingCount,this.isLazyLoadingComplete=!1)}onScroll(e,t){e&&this[t].panel.nativeElement.addEventListener("scroll",e=>{this.isLazyLoadingComplete||this.isLazyLoading||this[t].panel.nativeElement.scrollTop!==this[t].panel.nativeElement.scrollHeight-this[t].panel.nativeElement.offsetHeight||this.fetchData(!0)})}}return e.\u0275fac=function(t){return new(t||e)(I["\u0275\u0275directiveInject"](L.c))},e.\u0275cmp=I["\u0275\u0275defineComponent"]({type:e,selectors:[["app-expense-multi-select"]],viewQuery:function(e,t){if(1&e&&I["\u0275\u0275viewQuery"](E,!0),2&e){let e;I["\u0275\u0275queryRefresh"](e=I["\u0275\u0275loadQuery"]())&&(t.matSelect=e.first)}},inputs:{label:"label",optionLabel:"optionLabel",API_URL:"API_URL",token:"token",lazyLoadingCount:"lazyLoadingCount",apiParamItems:"apiParamItems"},outputs:{selectedValues:"selectedValues"},decls:14,vars:11,consts:[["appearance","outline",1,"full-width"],["multiple","",1,"col-10-5","p-0",3,"placeholder","formControl","openedChange"],["matSelect",""],["noEntriesFoundLabel","No Options Found!",3,"disableScrollToActiveOnOptionsChanged","formControl","placeholderLabel","hideClearSearchButton"],[4,"ngIf"],[3,"matTooltip","value",4,"ngFor","ngForOf"],["class","bottom-buttons p-0 row",4,"ngIf"],["mat-icon-button","","matTooltip","Clear Filter",1,"col-1","p-0","clear-button",3,"click"],[1,"clear-button-icon"],["matTooltip","Loading",1,"lds-facebook"],[3,"matTooltip","value"],[1,"bottom-buttons","p-0","row"],["mat-raised-button","","matTooltip","Close",1,"close-filter-button","p-0","pl-2","pr-2","mr-2",3,"click"],["mat-raised-button","","matTooltip","Clear",1,"clear-filter-button","p-0","pl-2","pr-2","mr-2",3,"click"],["mat-raised-button","","matTooltip","Apply",1,"apply-filter-button","p-0","pl-2","pr-2",3,"click"]],template:function(e,t){1&e&&(I["\u0275\u0275elementStart"](0,"mat-form-field",0),I["\u0275\u0275elementStart"](1,"mat-label"),I["\u0275\u0275text"](2),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](3,"mat-select",1,2),I["\u0275\u0275listener"]("openedChange",(function(e){return t.onScroll(e,"matSelect")})),I["\u0275\u0275elementStart"](5,"mat-option"),I["\u0275\u0275element"](6,"ngx-mat-select-search",3),I["\u0275\u0275elementEnd"](),I["\u0275\u0275template"](7,k,5,0,"mat-option",4),I["\u0275\u0275template"](8,A,2,3,"mat-option",5),I["\u0275\u0275template"](9,j,5,0,"mat-option",4),I["\u0275\u0275template"](10,V,7,0,"div",6),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](11,"button",7),I["\u0275\u0275listener"]("click",(function(e){return t.clearFilter(e)})),I["\u0275\u0275elementStart"](12,"mat-icon",8),I["\u0275\u0275text"](13,"clear"),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"]()),2&e&&(I["\u0275\u0275advance"](2),I["\u0275\u0275textInterpolate"](t.label),I["\u0275\u0275advance"](1),I["\u0275\u0275propertyInterpolate"]("placeholder",t.label),I["\u0275\u0275property"]("formControl",t.selectedValCtrl),I["\u0275\u0275advance"](3),I["\u0275\u0275property"]("disableScrollToActiveOnOptionsChanged",!0)("formControl",t.searchCtrl)("placeholderLabel","Search "+t.label+" ...")("hideClearSearchButton",!1),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",t.isLoading),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngForOf",t.list),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",t.isLazyLoading),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",!t.isLazyLoading&&!t.isLoading))},directives:[f.c,f.g,y.c,x.v,x.k,O.p,w.a,h.NgIf,h.NgForOf,u.a,C.a,g.a],styles:[".full-width[_ngcontent-%COMP%]{width:100%}.col-10-5[_ngcontent-%COMP%]{flex:0 0 87.666667%;max-width:87.666667%}.clear-button[_ngcontent-%COMP%]{margin-top:-2px;margin-left:7px}.clear-button[_ngcontent-%COMP%], .clear-button-icon[_ngcontent-%COMP%]{height:18px!important;width:18px!important;line-height:18px!important}.clear-button-icon[_ngcontent-%COMP%]{font-size:15px!important;color:#a9a9a9!important;margin-bottom:6px!important}.bottom-buttons[_ngcontent-%COMP%]{width:100%;position:sticky;bottom:0;background:#fff}.apply-filter-button[_ngcontent-%COMP%]{width:30%;border-radius:0!important;color:#fff;background-color:#df514c}.clear-filter-button[_ngcontent-%COMP%]{border-radius:0!important;color:#df514c;border:1px solid #df514c}.clear-filter-button[_ngcontent-%COMP%], .close-filter-button[_ngcontent-%COMP%]{width:30%;background:#fff;margin-right:.85rem!important}.close-filter-button[_ngcontent-%COMP%]{border-radius:0!important;color:grey;border:1px solid grey}.lds-facebook[_ngcontent-%COMP%]{display:inline-block;position:sticky;width:100%;height:24px;margin-left:95px}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{display:inline-block;position:absolute;left:4px;width:5px;background:#cf0001;animation:lds-facebook 1.2s cubic-bezier(0,.5,.5,1) infinite}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:first-child{left:8px;animation-delay:-.24s}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:nth-child(2){left:16px;animation-delay:-.12s}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:nth-child(3){left:24px;animation-delay:0}@keyframes lds-facebook{0%{top:8px;height:32px}50%,to{top:12px;height:16px}}"]}),e})();n("/1cH");var T=n("LcQX"),F=n("zinu"),z=n("XXEo"),N=n("vxfF"),Y=n("me71");function B(e,t){if(1&e){const e=I["\u0275\u0275getCurrentView"]();I["\u0275\u0275elementStart"](0,"button",34),I["\u0275\u0275listener"]("click",(function(){return I["\u0275\u0275restoreView"](e),I["\u0275\u0275nextContext"]().clearMilestoneName()})),I["\u0275\u0275elementStart"](1,"mat-icon",35),I["\u0275\u0275text"](2," close "),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"]()}}function R(e,t){if(1&e){const e=I["\u0275\u0275getCurrentView"]();I["\u0275\u0275elementStart"](0,"button",34),I["\u0275\u0275listener"]("click",(function(){return I["\u0275\u0275restoreView"](e),I["\u0275\u0275nextContext"]().clearCostCenterSearch()})),I["\u0275\u0275elementStart"](1,"mat-icon",36),I["\u0275\u0275text"](2," close "),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"]()}}function U(e,t){1&e&&I["\u0275\u0275element"](0,"hr",45)}function q(e,t){if(1&e){const e=I["\u0275\u0275getCurrentView"]();I["\u0275\u0275elementStart"](0,"div",39),I["\u0275\u0275elementStart"](1,"div",40),I["\u0275\u0275elementStart"](2,"div",41),I["\u0275\u0275listener"]("click",(function(){I["\u0275\u0275restoreView"](e);const n=t.$implicit;return I["\u0275\u0275nextContext"](2).showBillableClaims(n)})),I["\u0275\u0275elementStart"](3,"span"),I["\u0275\u0275text"](4),I["\u0275\u0275elementStart"](5,"span",42),I["\u0275\u0275text"](6),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](7,"span",43),I["\u0275\u0275text"](8),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275template"](9,U,1,0,"hr",44),I["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=I["\u0275\u0275nextContext"](2);I["\u0275\u0275advance"](2),I["\u0275\u0275propertyInterpolate3"]("matTooltip","",null!=e&&e.name&&null!=(null==e?null:e.name)?null==e?null:e.name:""," - ",null!=e&&e.description&&null!=(null==e?null:e.description)?null==e?null:e.description:""," (",null!=e&&e.claim_count&&null!=(null==e?null:e.claim_count)?null==e?null:e.claim_count:"",")"),I["\u0275\u0275advance"](2),I["\u0275\u0275textInterpolate1"]("",null==e?null:e.name," - "),I["\u0275\u0275advance"](2),I["\u0275\u0275textInterpolate"](null==e?null:e.description),I["\u0275\u0275advance"](2),I["\u0275\u0275textInterpolate1"]("(",null==e?null:e.claim_count,")"),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",n.costCenterClaimList.length>1)}}function K(e,t){if(1&e){const e=I["\u0275\u0275getCurrentView"]();I["\u0275\u0275elementStart"](0,"div",37),I["\u0275\u0275listener"]("scrolled",(function(){return I["\u0275\u0275restoreView"](e),I["\u0275\u0275nextContext"]().getCostCenterList()})),I["\u0275\u0275element"](1,"div"),I["\u0275\u0275elementStart"](2,"div"),I["\u0275\u0275template"](3,q,10,7,"div",38),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"]()}if(2&e){const e=I["\u0275\u0275nextContext"]();I["\u0275\u0275classMapInterpolate1"]("col-12 p-0 ",!e.isProjectAndItemApisLoading&&e.isProjectAndItemVisible?"claim-list-with-project":"claim-list",""),I["\u0275\u0275property"]("infiniteScrollDistance",2)("scrollWindow",!1)("stopPropagation",!0),I["\u0275\u0275advance"](3),I["\u0275\u0275property"]("ngForOf",e.costCenterClaimList)}}function W(e,t){1&e&&I["\u0275\u0275element"](0,"mat-spinner",46)}function G(e,t){1&e&&(I["\u0275\u0275elementStart"](0,"p",11),I["\u0275\u0275text"](1," Portfolio Name"),I["\u0275\u0275elementEnd"]())}function X(e,t){if(1&e&&(I["\u0275\u0275elementStart"](0,"p",47),I["\u0275\u0275text"](1),I["\u0275\u0275elementEnd"]()),2&e){const e=I["\u0275\u0275nextContext"]();I["\u0275\u0275property"]("matTooltip",e.projectName),I["\u0275\u0275advance"](1),I["\u0275\u0275textInterpolate1"](" ",e.projectName,"")}}function J(e,t){1&e&&(I["\u0275\u0275elementStart"](0,"p",11),I["\u0275\u0275text"](1," Project Name"),I["\u0275\u0275elementEnd"]())}function H(e,t){if(1&e&&(I["\u0275\u0275elementStart"](0,"p",47),I["\u0275\u0275text"](1),I["\u0275\u0275elementEnd"]()),2&e){const e=I["\u0275\u0275nextContext"]();I["\u0275\u0275property"]("matTooltip",e.itemName),I["\u0275\u0275advance"](1),I["\u0275\u0275textInterpolate1"](" ",e.itemName,"")}}function Q(e,t){if(1&e){const e=I["\u0275\u0275getCurrentView"]();I["\u0275\u0275elementStart"](0,"button",34),I["\u0275\u0275listener"]("click",(function(){return I["\u0275\u0275restoreView"](e),I["\u0275\u0275nextContext"](2).clearSearch()})),I["\u0275\u0275elementStart"](1,"mat-icon",36),I["\u0275\u0275text"](2," close "),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"]()}}function $(e,t){if(1&e){const e=I["\u0275\u0275getCurrentView"]();I["\u0275\u0275elementStart"](0,"mat-form-field",48),I["\u0275\u0275elementStart"](1,"span",13),I["\u0275\u0275elementStart"](2,"mat-icon",14),I["\u0275\u0275text"](3,"search"),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](4,"input",49),I["\u0275\u0275listener"]("ngModelChange",(function(t){return I["\u0275\u0275restoreView"](e),I["\u0275\u0275nextContext"]().searchParameter=t}))("keyup.enter",(function(){return I["\u0275\u0275restoreView"](e),I["\u0275\u0275nextContext"]().callSearchApi()}))("ngModelChange",(function(){return I["\u0275\u0275restoreView"](e),I["\u0275\u0275nextContext"]().onSearchParameterChange()})),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](5,"mat-icon",9),I["\u0275\u0275template"](6,Q,3,0,"button",10),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"]()}if(2&e){const e=I["\u0275\u0275nextContext"]();I["\u0275\u0275advance"](4),I["\u0275\u0275property"]("ngModel",e.searchParameter),I["\u0275\u0275advance"](2),I["\u0275\u0275property"]("ngIf",e.searchParameter&&""!=e.searchParameter)}}const Z=function(){return["name"]};function ee(e,t){if(1&e){const e=I["\u0275\u0275getCurrentView"]();I["\u0275\u0275elementStart"](0,"app-expense-multi-select",50),I["\u0275\u0275listener"]("selectedValues",(function(t){return I["\u0275\u0275restoreView"](e),I["\u0275\u0275nextContext"]().selectCategory(t)})),I["\u0275\u0275elementEnd"]()}if(2&e){const e=I["\u0275\u0275nextContext"]();I["\u0275\u0275property"]("token",e.token)("optionLabel",I["\u0275\u0275pureFunction0"](4,Z))("API_URL",e.searchClaimCategories)("lazyLoadingCount",10)}}const te=function(){return{applyLabel:"Apply",format:"DD MMM YYYY",customRangeLabel:"Custom Duration"}};function ne(e,t){if(1&e){const e=I["\u0275\u0275getCurrentView"]();I["\u0275\u0275elementStart"](0,"input",51),I["\u0275\u0275listener"]("ngModelChange",(function(t){return I["\u0275\u0275restoreView"](e),I["\u0275\u0275nextContext"]().duration=t}))("change",(function(t){return I["\u0275\u0275restoreView"](e),I["\u0275\u0275nextContext"]().selectDuration(t)})),I["\u0275\u0275elementEnd"]()}if(2&e){const e=I["\u0275\u0275nextContext"]();I["\u0275\u0275property"]("showCustomRangeLabel",!0)("locale",I["\u0275\u0275pureFunction0"](7,te))("alwaysShowCalendars",!0)("showCancel",!0)("ranges",e.durationRanges)("linkedCalendars",!0)("ngModel",e.duration)}}function ie(e,t){1&e&&(I["\u0275\u0275elementStart"](0,"div",52),I["\u0275\u0275element"](1,"mat-spinner",53),I["\u0275\u0275elementEnd"]())}function ae(e,t){if(1&e){const e=I["\u0275\u0275getCurrentView"]();I["\u0275\u0275elementStart"](0,"div",59),I["\u0275\u0275listener"]("click",(function(){I["\u0275\u0275restoreView"](e);const n=t.$implicit;return I["\u0275\u0275nextContext"](2).selectExpenseListItem(n)})),I["\u0275\u0275elementStart"](1,"div",60),I["\u0275\u0275element"](2,"mat-checkbox",61),I["\u0275\u0275elementStart"](3,"p",62),I["\u0275\u0275text"](4),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](5,"p",63),I["\u0275\u0275text"](6," - "),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](7,"p",64),I["\u0275\u0275text"](8),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](9,"div",65),I["\u0275\u0275elementStart"](10,"div",66),I["\u0275\u0275elementStart"](11,"p",67),I["\u0275\u0275text"](12,"Expense Code"),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](13,"p",68),I["\u0275\u0275text"](14),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](15,"div",69),I["\u0275\u0275elementStart"](16,"p",67),I["\u0275\u0275text"](17,"Amount Requested"),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](18,"p",68),I["\u0275\u0275text"](19),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](20,"div",69),I["\u0275\u0275elementStart"](21,"p",67),I["\u0275\u0275text"](22,"Amount Claimed"),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](23,"p",68),I["\u0275\u0275text"](24),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](25,"div",69),I["\u0275\u0275elementStart"](26,"p",67),I["\u0275\u0275text"](27,"Requested By"),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](28,"div",70),I["\u0275\u0275element"](29,"app-user-image",71),I["\u0275\u0275elementStart"](30,"p",72),I["\u0275\u0275text"](31),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](32,"div",73),I["\u0275\u0275elementStart"](33,"p",67),I["\u0275\u0275text"](34,"Billed On"),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](35,"p",68),I["\u0275\u0275text"](36),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;I["\u0275\u0275advance"](2),I["\u0275\u0275property"]("checked",e.isChecked),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("matTooltip",e.expense_category),I["\u0275\u0275advance"](1),I["\u0275\u0275textInterpolate1"](" ",e.expense_category,""),I["\u0275\u0275advance"](3),I["\u0275\u0275property"]("matTooltip",e.department),I["\u0275\u0275advance"](1),I["\u0275\u0275textInterpolate1"](" ",e.department,""),I["\u0275\u0275advance"](5),I["\u0275\u0275property"]("matTooltip",e.expense_code),I["\u0275\u0275advance"](1),I["\u0275\u0275textInterpolate"](e.expense_code),I["\u0275\u0275advance"](4),I["\u0275\u0275property"]("matTooltip",e.expense_request_amount),I["\u0275\u0275advance"](1),I["\u0275\u0275textInterpolate1"]("",e.expense_request_amount," "),I["\u0275\u0275advance"](4),I["\u0275\u0275property"]("matTooltip",e.expense_claim_amount),I["\u0275\u0275advance"](1),I["\u0275\u0275textInterpolate1"]("",e.expense_claim_amount," "),I["\u0275\u0275advance"](4),I["\u0275\u0275property"]("matTooltip",e.requested_by),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("id",e?e.requested_by_oid:"")("imgWidth","25px")("imgHeight","25px"),I["\u0275\u0275advance"](2),I["\u0275\u0275textInterpolate"](e.requested_by),I["\u0275\u0275advance"](4),I["\u0275\u0275property"]("matTooltip",e.billed_on),I["\u0275\u0275advance"](1),I["\u0275\u0275textInterpolate1"](" ",e.billed_on,"")}}function le(e,t){if(1&e){const e=I["\u0275\u0275getCurrentView"]();I["\u0275\u0275elementStart"](0,"div",54),I["\u0275\u0275listener"]("scrolled",(function(){return I["\u0275\u0275restoreView"](e),I["\u0275\u0275nextContext"]().getExpenseListBasedOnCostCentre(!1)})),I["\u0275\u0275elementStart"](1,"span",55),I["\u0275\u0275listener"]("click",(function(){return I["\u0275\u0275restoreView"](e),I["\u0275\u0275nextContext"]().handleCheckBox("")})),I["\u0275\u0275elementStart"](2,"mat-checkbox",56),I["\u0275\u0275elementStart"](3,"span",57),I["\u0275\u0275text"](4,"Select All"),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275template"](5,ae,37,18,"div",58),I["\u0275\u0275elementEnd"]()}if(2&e){const e=I["\u0275\u0275nextContext"]();I["\u0275\u0275classMapInterpolate1"]("col-12 p-0 pl-3 pr-3 ",e.areExpenseMilestonesLoading?"milestone-list-loading-more":"milestone-list",""),I["\u0275\u0275property"]("infiniteScrollDistance",2)("scrollWindow",!1),I["\u0275\u0275advance"](2),I["\u0275\u0275property"]("checked",e.isAllChecked),I["\u0275\u0275advance"](3),I["\u0275\u0275property"]("ngForOf",e.expenseList)}}function oe(e,t){1&e&&I["\u0275\u0275element"](0,"mat-spinner",74)}function se(e,t){if(1&e&&(I["\u0275\u0275elementStart"](0,"div",79),I["\u0275\u0275elementStart"](1,"p",80),I["\u0275\u0275text"](2,"Total Claim Value"),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](3,"p",81),I["\u0275\u0275text"](4),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"]()),2&e){const e=I["\u0275\u0275nextContext"](2);I["\u0275\u0275advance"](4),I["\u0275\u0275textInterpolate1"]("",e.totalClaimValue," ")}}function re(e,t){1&e&&I["\u0275\u0275element"](0,"mat-spinner",82)}function ce(e,t){if(1&e){const e=I["\u0275\u0275getCurrentView"]();I["\u0275\u0275elementStart"](0,"button",83),I["\u0275\u0275listener"]("click",(function(){return I["\u0275\u0275restoreView"](e),I["\u0275\u0275nextContext"](2).createBillableClaim()})),I["\u0275\u0275text"](1,"Create"),I["\u0275\u0275elementStart"](2,"mat-icon",84),I["\u0275\u0275text"](3,"navigate_next "),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"]()}if(2&e){const e=I["\u0275\u0275nextContext"](2);I["\u0275\u0275property"]("disabled",!e.areAnyExpensesSelected)}}function me(e,t){if(1&e&&(I["\u0275\u0275elementStart"](0,"div",75),I["\u0275\u0275template"](1,se,5,1,"div",76),I["\u0275\u0275template"](2,re,1,0,"mat-spinner",77),I["\u0275\u0275template"](3,ce,4,1,"button",78),I["\u0275\u0275elementEnd"]()),2&e){const e=I["\u0275\u0275nextContext"]();I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",e.areAnyExpensesSelected),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",e.isCreatingMilestone),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",!e.isCreatingMilestone)}}function pe(e,t){if(1&e&&(I["\u0275\u0275elementStart"](0,"div",85),I["\u0275\u0275element"](1,"img",86),I["\u0275\u0275elementStart"](2,"p",4),I["\u0275\u0275text"](3,"No Claims Here"),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](4,"p",87),I["\u0275\u0275text"](5),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"]()),2&e){const e=I["\u0275\u0275nextContext"]();I["\u0275\u0275advance"](5),I["\u0275\u0275textInterpolate1"]("Populate claim list by selecting a ",e.costCenterFieldLabel?e.costCenterFieldLabel:"Cost center","!")}}let de=(()=>{class e{constructor(e,t,n,i,a,o){this.dialogRef=e,this.inData=t,this.utilityService=n,this.expensesBillableClaimsService=i,this.authService=a,this.spinnerService=o,this.token=this.authService.getToken(),this.searchClaimCategories=window.location.origin+"/api/exPrimary/searchClaimCategories",this.searchParameter="",this.milestoneName="",this.projectName="",this.itemName="",this.projectId=0,this.itemId=0,this.isProjectAndItemVisible=!1,this.areExpenseMilestonesLoading=!1,this.expenseList=[],this.totalClaimValue="0",this.selectedCategoryList=[],this.costCenterClaimList=[],this.duration={startDate:"",endDate:""},this.durationRanges={"This Month":[l().startOf("month"),l().endOf("month")],"Previuos Year & Current Year":[l().subtract(2,"year").startOf("year"),l().endOf("year")],"Last Month":[l().subtract(1,"month").startOf("month"),l().subtract(1,"month").endOf("month")],"Next Month":[l().add(1,"month").startOf("month"),l().add(1,"month").endOf("month")],"Upcoming 3 Months":[l().startOf("month"),l().add(2,"month").endOf("month")],"This Year":[l().startOf("year"),l().endOf("year")],"Previous Year":[l().subtract(1,"year").startOf("year"),l().subtract(1,"year").endOf("year")]},this.isProjectAndItemApisLoading=!1,this._onDestroy=new s.b,this.startIndex=0,this.noOfRecords=10,this.areAnyExpensesSelected=!1,this.isCreatingMilestone=!1,this.costCentreListStartIndex=0,this.costCenterNofRecords=50,this.subjectKeyUp=new s.b,this.isAllChecked=!1,this.subjectKeyUp.pipe(Object(p.a)(100),Object(d.a)()).subscribe(e=>{this.getCostCenterSearchResult(e)})}handleCheckBox(e){return Object(i.c)(this,void 0,void 0,(function*(){if(this.isAllChecked=!this.isAllChecked,!this.isAllChecked)return this.totalClaimValue=this.utilityService.getAmountCurrencyFormat(0,this.modalParams.defaultCurrency),r.each(this.expenseList,e=>{e.isChecked=this.isAllChecked}),void(this.areAnyExpensesSelected=!1);1==this.isAllChecked&&(this.areAnyExpensesSelected=!0),r.each(this.expenseList,e=>{e.isChecked=this.isAllChecked});let e={user_oid:this.authService.getProfile().profile.oid,params:{project_id:this.projectId,project_item_id:this.itemId,milestone_name:this.milestoneName,cost_center:this.costCentreTemp.name,total_selected_claims_approved_value:this.resolveClaimApprovedValue(),expense_list_all:this.expenseList,expense_list:this.resolveExpenseList()},isAllItemsChecked:this.isAllChecked,cost_center:this.costCentreTemp.name,search_parameter:this.searchParameter,filter_params:{expense_category:this.selectedCategoryList,t_approved_on_duration:{start_date:l(this.duration.startDate,this.modalParams.defaultDateFormat).format("YYYY-MM-DD"),end_date:l(this.duration.endDate,this.modalParams.defaultDateFormat).format("YYYY-MM-DD")}}},t=yield this.expensesBillableClaimsService.getAllBillableClaimByCostCenter(this.costCenterSearchParameter,e);if("S"==t.messType){let e=r.where(t.data,{currency_code:this.modalParams.defaultCurrency});if(e.length>0){let t=this.utilityService.getAmountCurrencyFormat(e[0].value,this.modalParams.defaultCurrency);this.totalClaimValue=this.modalParams.defaultCurrency+" "+t}else this.totalClaimValue=this.modalParams.defaultCurrency+" "+this.utilityService.getAmountCurrencyFormat(0,this.modalParams.defaultCurrency)}else this.totalClaimValue=this.modalParams.defaultCurrency+" "+this.utilityService.getAmountCurrencyFormat(0,this.modalParams.defaultCurrency)}))}ngOnInit(){this.getFieldConfig(),this.getBillableClaimGroupbyCostCenter(),this.modalParams=this.inData.modalParams,this.duration={startDate:l().subtract(2,"year").startOf("year").format(this.modalParams.defaultDateFormat),endDate:l().endOf("year").format(this.modalParams.defaultDateFormat)}}getBillableClaimGroupbyCostCenter(){return Object(i.c)(this,void 0,void 0,(function*(){let e=yield this.expensesBillableClaimsService.getBillableClaimByCostCenter(this.costCentreListStartIndex,this.costCenterNofRecords,this.costCenterSearchParameter);this.costCenterClaimList=this.costCenterClaimList.concat(e.data)}))}getCostCenterSearchResult(e){return Object(i.c)(this,void 0,void 0,(function*(){this.costCenterClaimList=[],this.costCenterSearchParameter=e;let t=yield this.expensesBillableClaimsService.getBillableClaimByCostCenter(this.costCentreListStartIndex,this.costCenterNofRecords,e);this.costCenterClaimList=t.data}))}onCostCenterSearchParameterChange(){this.costCenterClaimList=[],this.getBillableClaimGroupbyCostCenter()}getCostCenterList(){this.costCentreListStartIndex+=50,this.getBillableClaimGroupbyCostCenter()}clearCostCenterSearch(){this.subjectKeyUp.next("")}getFieldConfig(){this.expensesBillableClaimsService.getExpenseFieldConfig().pipe(Object(o.a)(this._onDestroy)).subscribe(e=>Object(i.c)(this,void 0,void 0,(function*(){this.formFieldConfig=e.data,this.formFieldConfig&&null!=this.formFieldConfig&&r.each(this.formFieldConfig,e=>{"costCenterCode"==e.field_key&&(this.costCenterFieldLabel=null!=e.field_label?e.field_label:"Cost center")})})),e=>{console.log(e)})}showBillableClaims(e){this.costCentre=JSON.stringify(e),this.onCostCentreChange()}onSearchCostCenter(e){this.subjectKeyUp.next(e.target.value)}onCostCentreChange(){this.costCentre?(this.isProjectAndItemVisible=!1,this.isProjectAndItemApisLoading=!0,this.costCentreTemp=JSON.parse(this.costCentre),this.changeCostCentre()):(this.costCentreTemp=null,this.isProjectAndItemVisible=!1,this.projectName="",this.itemName="",this.projectId=0,this.itemId=0,this.areAnyExpensesSelected=!1,this.expenseList=[])}changeCostCentre(){this.getCostCentreProjectItemName()}getCostCentreProjectItemName(){null!=this.costCentreTemp&&this.expensesBillableClaimsService.getCostCentreProjectItemName({cost_center:this.costCentreTemp.name}).pipe(Object(o.a)(this._onDestroy)).subscribe(e=>Object(i.c)(this,void 0,void 0,(function*(){"S"==e.messType&&e.data&&e.data.length>0?(this.projectName=e.data[0].project_name,this.itemName=e.data[0].item_name,this.projectId=e.data[0].project_id,this.itemId=e.data[0].item_id,this.getExpenseListBasedOnCostCentre(!0)):"S"==e.messType&&e.data&&0==e.data.length?(this.projectName="N/A",this.itemName="N/A",this.projectId=0,this.itemId=0,this.getExpenseListBasedOnCostCentre(!0)):"E"==e.messType&&(this.utilityService.showErrorMessage(e,"KEBS"),this.projectName="",this.itemName="",this.isProjectAndItemVisible=!1,this.isProjectAndItemApisLoading=!1)})),e=>{this.isProjectAndItemVisible=!1,this.isProjectAndItemApisLoading=!1,this.projectName="",this.itemName="",console.log(e),this.utilityService.showErrorMessage(e,"KEBS")})}getExpenseListBasedOnCostCentre(e){if(null!=this.costCentreTemp){e?this.startIndex=0:this.areExpenseMilestonesLoading=!0;let t={cost_center:this.costCentreTemp.name,start_index:this.startIndex,no_of_records:this.noOfRecords,search_parameter:this.searchParameter,filter_params:{expense_category:this.selectedCategoryList,t_approved_on_duration:{start_date:l(this.duration.startDate,this.modalParams.defaultDateFormat).format("YYYY-MM-DD"),end_date:l(this.duration.endDate,this.modalParams.defaultDateFormat).format("YYYY-MM-DD")}}};this.expensesBillableClaimsService.getExpenseListBasedOnCostCentre(t).pipe(Object(o.a)(this._onDestroy)).subscribe(t=>Object(i.c)(this,void 0,void 0,(function*(){if("S"==t.messType&&t.data&&t.data.length>0){e?this.expenseList=t.data:(r.each(t.data,e=>{e.isChecked=this.isAllChecked}),this.expenseList=this.expenseList.concat(t.data)),this.startIndex=this.expenseList.length;for(let e of this.expenseList)e.billed_on=l(e.billed_on).format(this.modalParams.defaultDateFormat),e.expense_request_amount=this.resolveAmountAndCurrency(e.amount_requested),e.expense_approved_amount=this.resolveAmountAndValue(e.total_approved_amount),e.expense_claim_amount=this.resolveAmountAndCurrency(e.amount_claimed)}else"E"==t.messType?this.utilityService.showErrorMessage(t,"KEBS"):"S"==t.messType&&t.data&&0==t.data.length&&e&&(this.expenseList=[]);this.isProjectAndItemVisible=!0,this.areExpenseMilestonesLoading=!1,this.isProjectAndItemApisLoading=!1})),e=>{this.isProjectAndItemVisible=!1,this.areExpenseMilestonesLoading=!1,this.isProjectAndItemApisLoading=!1,this.expenseList=[],console.log(e),this.utilityService.showErrorMessage(e,"KEBS")})}}resolveAmountAndCurrency(e){let t=r.where(e,{currency_code:this.modalParams.defaultCurrency}),n=t.length>0?t[0].value:0;return n=this.utilityService.getAmountCurrencyFormat(n,this.modalParams.defaultCurrency),this.modalParams.defaultCurrency+" "+n}resolveAmountAndValue(e){let t=r.where(e,{currency_code:this.modalParams.defaultCurrency});return t.length>0?t[0].value:0}selectCategory(e){this.selectedCategoryList=r.pluck(e,"name"),this.areExpenseMilestonesLoading=!0,this.totalClaimValue="0.00",this.isAllChecked=!1,this.areAnyExpensesSelected=!1,this.getExpenseListBasedOnCostCentre(!0)}createBillableClaim(){let e=!1;for(let t of this.expenseList)t.isChecked&&(e=!0);if(""==this.milestoneName)return this.utilityService.showToastMessage("Milestone Name is mandatory!");if(null==this.costCentreTemp||""==this.costCentreTemp.name)return this.utilityService.showToastMessage("Cost Centre is mandatory!");if(!this.areAnyExpensesSelected)return this.utilityService.showToastMessage("At least one expense must be selected!");if(!e)return this.utilityService.showToastMessage("At least one expense must be selected!");if(null!=this.costCentreTemp){this.isCreatingMilestone=!0;let e={user_oid:this.authService.getProfile().profile.oid,params:{project_id:this.projectId,project_item_id:this.itemId,milestone_name:this.milestoneName,cost_center:this.costCentreTemp.name,total_selected_claims_approved_value:this.resolveClaimApprovedValue(),expense_list_all:this.expenseList,expense_list:this.resolveExpenseList()},isAllItemsChecked:this.isAllChecked,cost_center:this.costCentreTemp.name,search_parameter:this.searchParameter,filter_params:{expense_category:this.selectedCategoryList,t_approved_on_duration:{start_date:l(this.duration.startDate,this.modalParams.defaultDateFormat).format("YYYY-MM-DD"),end_date:l(this.duration.endDate,this.modalParams.defaultDateFormat).format("YYYY-MM-DD")}}};this.utilityService.openConfirmationSweetAlertWithCustom("Create New Expense Milestone ?","").then(t=>{1==t?(this.spinnerService.show(),this.expensesBillableClaimsService.billableMilestoneCreation(e).pipe(Object(o.a)(this._onDestroy)).subscribe(e=>Object(i.c)(this,void 0,void 0,(function*(){"S"==e.messType?(this.spinnerService.hide(),m.a.fire({title:"Milestone Created Successfully !",icon:"success",showConfirmButton:!0}),this.clearMilestoneName(),this.isCreatingMilestone=!1,this.closeModal()):"E"==e.messType&&(m.a.fire({title:"Milestone Creation Failed !",icon:"info",showConfirmButton:!0}),this.spinnerService.hide())})),e=>{console.error(e),m.a.fire({title:"Milestone Creation Failed !",icon:"info",showConfirmButton:!0}),this.isCreatingMilestone=!1,this.spinnerService.hide(),console.log(e)})):(this.isCreatingMilestone=!1,this.spinnerService.hide())})}}resolveClaimApprovedValue(){let e=[];for(let t of this.expenseList)if(t.isChecked)for(let n of t.total_approved_amount){let t=r.where(e,{currency_code:n.currency_code});0==t.length?e.push({currency_code:n.currency_code,value:n.value}):t[0].value+=n.value}return e}resolveExpenseList(){let e=[];for(let t of this.expenseList)t.isChecked&&e.push({expense_header_id:t.expense_header_id});return e}callSearchApi(){this.getExpenseListBasedOnCostCentre(!0)}clearSearch(){this.searchParameter="",this.callSearchApi()}onSearchParameterChange(){this.totalClaimValue="0.00",this.isAllChecked=!1,this.areAnyExpensesSelected=!1,""==this.searchParameter&&this.callSearchApi()}clearMilestoneName(){this.milestoneName=""}selectExpenseListItem(e){return Object(i.c)(this,void 0,void 0,(function*(){if(this.isAllChecked){e.isChecked=!e.isChecked,e.isChecked||(this.isAllChecked=!1),1==this.isAllChecked&&(this.areAnyExpensesSelected=!0);let t={user_oid:this.authService.getProfile().profile.oid,params:{project_id:this.projectId,project_item_id:this.itemId,milestone_name:this.milestoneName,cost_center:this.costCentreTemp.name,total_selected_claims_approved_value:this.resolveClaimApprovedValue(),expense_list_all:this.expenseList,expense_list:this.resolveExpenseList()},isAllItemsChecked:this.isAllChecked,cost_center:this.costCentreTemp.name,search_parameter:this.searchParameter,filter_params:{expense_category:this.selectedCategoryList,t_approved_on_duration:{start_date:l(this.duration.startDate,this.modalParams.defaultDateFormat).format("YYYY-MM-DD"),end_date:l(this.duration.endDate,this.modalParams.defaultDateFormat).format("YYYY-MM-DD")}}},n=yield this.expensesBillableClaimsService.getAllBillableClaimByCostCenter(this.costCenterSearchParameter,t);if("S"==n.messType){let e=r.where(n.data,{currency_code:this.modalParams.defaultCurrency});if(e.length>0){let t=this.utilityService.getAmountCurrencyFormat(e[0].value,this.modalParams.defaultCurrency);this.totalClaimValue=t}else this.totalClaimValue=this.utilityService.getAmountCurrencyFormat(0,this.modalParams.defaultCurrency)}else this.totalClaimValue=this.utilityService.getAmountCurrencyFormat(0,this.modalParams.defaultCurrency)}else{e.isChecked=!e.isChecked,this.areAnyExpensesSelected=!1,this.totalClaimValue=this.utilityService.getAmountCurrencyFormat(0,this.modalParams.defaultCurrency);let t=0;for(let e of this.expenseList)e.isChecked&&(t+=e.expense_approved_amount,this.areAnyExpensesSelected=!0);this.totalClaimValue=this.modalParams.defaultCurrency+" "+this.utilityService.getAmountCurrencyFormat(t,this.modalParams.defaultCurrency)}}))}selectDuration(e){if(e.startDate){let t=l(e.startDate).startOf("day"),n=l(e.endDate).endOf("day");this.duration={startDate:l(t).format("DD MMM YYYY"),endDate:l(n).format("DD MMM YYYY")}}this.areExpenseMilestonesLoading=!0,this.totalClaimValue="0.00",this.isAllChecked=!1,this.areAnyExpensesSelected=!1,this.getExpenseListBasedOnCostCentre(!0)}closeModal(){this.dialogRef.close({event:"Milestone Created"})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}}return e.\u0275fac=function(t){return new(t||e)(I["\u0275\u0275directiveInject"](a.h),I["\u0275\u0275directiveInject"](a.a),I["\u0275\u0275directiveInject"](T.a),I["\u0275\u0275directiveInject"](F.a),I["\u0275\u0275directiveInject"](z.a),I["\u0275\u0275directiveInject"](M.c))},e.\u0275cmp=I["\u0275\u0275defineComponent"]({type:e,selectors:[["create-billable-claims-popup"]],decls:45,vars:19,consts:[[1,"create-billable-claims-popup-styles","row"],[1,"col-3","p-0","left-half"],[1,"col-12","p-0","pl-3","pr-3","pt-3",2,"height","100%"],[2,"height","75%"],[1,"create-milestone","col-12","p-0","m-0"],[1,"milestone-header","col-12","p-0","pt-4","m-0","mb-1"],[1,"mandatory-red"],["appearance","outline",2,"width","100%","height","45px"],["matInput","","placeholder","Milestone Name","autocomplete","off",2,"padding-bottom","1px !important",3,"ngModel","ngModelChange"],["matSuffix",""],["mat-button","","matSuffix","","mat-icon-button","","style","height: 30px; width: 30px; line-height: 1",3,"click",4,"ngIf"],[1,"milestone-header","col-12","p-0","pt-3","m-0","mb-1"],["appearance","outline",1,"ml-auto","mr-auto",2,"width","100%"],["matPrefix",""],[2,"font-size","20px !important","color","black !important"],["matInput","","placeholder","Search","autocomplete","off",2,"padding-bottom","1px !important",3,"ngModel","ngModelChange","keyup.enter","keyup"],["infinite-scroll","","cdkScrollable","",3,"class","infiniteScrollDistance","scrollWindow","stopPropagation","scrolled",4,"ngIf"],[2,"height","25%"],["class","mt-4 ml-auto mr-auto","diameter","25","matTooltip","Loading ...",4,"ngIf"],["class","milestone-header col-12 p-0 pt-3 m-0 mb-1",4,"ngIf"],["class","milestone-description col-12 p-0 m-0",3,"matTooltip",4,"ngIf"],[1,"col-9","p-0"],[1,"col-12","p-0","row","pl-3","pt-2","pb-3"],["appearance","outline","style","width: 35%; height: 45px;","class","mr-4",4,"ngIf"],["style","width: 26%; height: 45px;","class","mr-4","label","Category",3,"token","optionLabel","API_URL","lazyLoadingCount","selectedValues",4,"ngIf"],["matTooltip","Duration","type","text","style","width: 26%; height: 41px;","class","mr-3 dp-class","ngxDaterangepickerMd","","placeholder","Duration",3,"showCustomRangeLabel","locale","alwaysShowCalendars","showCancel","ranges","linkedCalendars","ngModel","ngModelChange","change",4,"ngIf"],["mat-icon-button","","matTooltip","Close",1,"align-right","mr-2"],[1,"close-button",3,"click"],["class","col-12 p-0 milestone-list-loading",4,"ngIf"],["infinite-scroll","","cdkScrollable","",3,"class","infiniteScrollDistance","scrollWindow","scrolled",4,"ngIf"],["class","mt-3 ml-auto mr-auto","diameter","25","matTooltip","Loading ...",4,"ngIf"],["class","col-12 row p-0 pl-3 pr-3 mt-3",4,"ngIf"],["class","col-12 p-0 milestone-list-loading row","style","text-align: center;",4,"ngIf"],["size","medium","type","ball-clip-rotate","bdColor","rgba(236, 233, 233, 0.8)","color","#cf0001"],["mat-button","","matSuffix","","mat-icon-button","",2,"height","30px","width","30px","line-height","1",3,"click"],["matTooltip","Clear",2,"font-size","20px !important","color","#66615b !important"],["matTooltip","Clear Search",2,"font-size","20px !important","color","#66615b !important"],["infinite-scroll","","cdkScrollable","",3,"infiniteScrollDistance","scrollWindow","stopPropagation","scrolled"],["class","cost-centre-item",4,"ngFor","ngForOf"],[1,"cost-centre-item"],[1,"row","pt-2","pb-2"],[1,"col","p-0","pl-1",2,"white-space","nowrap","overflow","hidden","text-overflow","ellipsis","width","100%",3,"matTooltip","click"],[2,"color","#45546e"],[1,"pl-1",2,"color","#cf0001"],["style","margin-top: 0%; margin-bottom: 0%;",4,"ngIf"],[2,"margin-top","0%","margin-bottom","0%"],["diameter","25","matTooltip","Loading ...",1,"mt-4","ml-auto","mr-auto"],[1,"milestone-description","col-12","p-0","m-0",3,"matTooltip"],["appearance","outline",1,"mr-4",2,"width","35%","height","45px"],["matInput","","placeholder","Search and press Enter","autocomplete","off",2,"padding-bottom","1px !important",3,"ngModel","ngModelChange","keyup.enter"],["label","Category",1,"mr-4",2,"width","26%","height","45px",3,"token","optionLabel","API_URL","lazyLoadingCount","selectedValues"],["matTooltip","Duration","type","text","ngxDaterangepickerMd","","placeholder","Duration",1,"mr-3","dp-class",2,"width","26%","height","41px",3,"showCustomRangeLabel","locale","alwaysShowCalendars","showCancel","ranges","linkedCalendars","ngModel","ngModelChange","change"],[1,"col-12","p-0","milestone-list-loading"],["diameter","25","matTooltip","Loading ...",1,"mt-auto","mb-auto","ml-auto","mr-auto"],["infinite-scroll","","cdkScrollable","",3,"infiniteScrollDistance","scrollWindow","scrolled"],[3,"click"],["disabled","",1,"mt-1","ml-2",2,"cursor","pointer",3,"checked"],["disabled","",2,"color","#66615b","font-weight","400","font-size","12px"],["class","col-12 p-0 mb-2 milestone-list-item",3,"click",4,"ngFor","ngForOf"],[1,"col-12","p-0","mb-2","milestone-list-item",3,"click"],[1,"col-12","p-2","row"],["disabled","",1,"mt-1","mr-3",2,"cursor","pointer",3,"checked"],[1,"milestone-category","col-3","p-0","m-0",3,"matTooltip"],[1,"milestone-department","p-0","m-0","col-1"],[1,"milestone-department","col-7","p-0","m-0",3,"matTooltip"],[1,"col-12","p-2","row","milestone-list-bottom"],[1,"col-2-5","milestone-item"],[1,"milestone-item-header","col-12","p-0","m-0"],[1,"milestone-item-description","col-12","p-0","m-0",3,"matTooltip"],[1,"col-2-5","milestone-item","ml-2"],[1,"col-12","p-0","row",2,"align-items","center",3,"matTooltip"],["content-type","template",1,"mr-2",3,"id","imgWidth","imgHeight"],[1,"milestone-item-description","m-0","col-9","p-0"],[1,"col-2-5","ml-2"],["diameter","25","matTooltip","Loading ...",1,"mt-3","ml-auto","mr-auto"],[1,"col-12","row","p-0","pl-3","pr-3","mt-3"],["class","align-left col-8 p-0",4,"ngIf"],["class","mr-4 mt-2 align-right","diameter","25","matTooltip","Creating Milestone ...",4,"ngIf"],["mat-flat-button","","class","pl-0 pr-0 mr-2 red-btn align-right",3,"disabled","click",4,"ngIf"],[1,"align-left","col-8","p-0"],[1,"total-milestone-header","col-12","p-0","m-0"],[1,"total-milestone-description","col-12","p-0","m-0"],["diameter","25","matTooltip","Creating Milestone ...",1,"mr-4","mt-2","align-right"],["mat-flat-button","",1,"pl-0","pr-0","mr-2","red-btn","align-right",3,"disabled","click"],["matListIcon","",1,"save-btn-icon"],[1,"col-12","p-0","milestone-list-loading","row",2,"text-align","center"],["src","https://assets.kebs.app/images/no_claims_here.png"],[1,"milestone-header-bigger","col-12","p-0","m-0"]],template:function(e,t){1&e&&(I["\u0275\u0275elementStart"](0,"div",0),I["\u0275\u0275elementStart"](1,"div",1),I["\u0275\u0275elementStart"](2,"div",2),I["\u0275\u0275elementStart"](3,"div",3),I["\u0275\u0275elementStart"](4,"p",4),I["\u0275\u0275text"](5,"Create Milestone"),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](6,"p",5),I["\u0275\u0275text"](7,"Milestone Name"),I["\u0275\u0275elementStart"](8,"span",6),I["\u0275\u0275text"](9,"*"),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](10,"mat-form-field",7),I["\u0275\u0275elementStart"](11,"input",8),I["\u0275\u0275listener"]("ngModelChange",(function(e){return t.milestoneName=e})),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](12,"mat-icon",9),I["\u0275\u0275template"](13,B,3,0,"button",10),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](14,"p",11),I["\u0275\u0275text"](15),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](16,"div"),I["\u0275\u0275elementStart"](17,"mat-form-field",12),I["\u0275\u0275elementStart"](18,"span",13),I["\u0275\u0275elementStart"](19,"mat-icon",14),I["\u0275\u0275text"](20,"search"),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](21,"input",15),I["\u0275\u0275listener"]("ngModelChange",(function(e){return t.costCenterSearchParameter=e}))("keyup.enter",(function(){return t.callSearchApi()}))("keyup",(function(e){return t.onSearchCostCenter(e)})),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](22,"mat-icon",9),I["\u0275\u0275template"](23,R,3,0,"button",10),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275template"](24,K,4,7,"div",16),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](25,"div",17),I["\u0275\u0275template"](26,W,1,0,"mat-spinner",18),I["\u0275\u0275template"](27,G,2,0,"p",19),I["\u0275\u0275template"](28,X,2,2,"p",20),I["\u0275\u0275template"](29,J,2,0,"p",19),I["\u0275\u0275template"](30,H,2,2,"p",20),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](31,"div",21),I["\u0275\u0275elementStart"](32,"div",22),I["\u0275\u0275template"](33,$,7,2,"mat-form-field",23),I["\u0275\u0275template"](34,ee,1,5,"app-expense-multi-select",24),I["\u0275\u0275template"](35,ne,1,8,"input",25),I["\u0275\u0275elementStart"](36,"button",26),I["\u0275\u0275elementStart"](37,"mat-icon",27),I["\u0275\u0275listener"]("click",(function(){return t.closeModal()})),I["\u0275\u0275text"](38,"close"),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275template"](39,ie,2,0,"div",28),I["\u0275\u0275template"](40,le,6,7,"div",29),I["\u0275\u0275template"](41,oe,1,0,"mat-spinner",30),I["\u0275\u0275template"](42,me,4,3,"div",31),I["\u0275\u0275template"](43,pe,6,1,"div",32),I["\u0275\u0275elementEnd"](),I["\u0275\u0275element"](44,"ngx-spinner",33),I["\u0275\u0275elementEnd"]()),2&e&&(I["\u0275\u0275advance"](11),I["\u0275\u0275property"]("ngModel",t.milestoneName),I["\u0275\u0275advance"](2),I["\u0275\u0275property"]("ngIf",t.milestoneName&&""!=t.milestoneName),I["\u0275\u0275advance"](2),I["\u0275\u0275textInterpolate"](t.costCenterFieldLabel?t.costCenterFieldLabel:"Cost center"),I["\u0275\u0275advance"](6),I["\u0275\u0275property"]("ngModel",t.costCenterSearchParameter),I["\u0275\u0275advance"](2),I["\u0275\u0275property"]("ngIf",t.costCenterSearchParameter&&""!=t.costCenterSearchParameter),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",t.costCenterClaimList.length>0),I["\u0275\u0275advance"](2),I["\u0275\u0275property"]("ngIf",t.isProjectAndItemApisLoading&&!t.isProjectAndItemVisible),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",!t.isProjectAndItemApisLoading&&t.isProjectAndItemVisible),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",!t.isProjectAndItemApisLoading&&t.isProjectAndItemVisible),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",!t.isProjectAndItemApisLoading&&t.isProjectAndItemVisible),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",!t.isProjectAndItemApisLoading&&t.isProjectAndItemVisible),I["\u0275\u0275advance"](3),I["\u0275\u0275property"]("ngIf",!t.isProjectAndItemApisLoading&&t.isProjectAndItemVisible),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",!t.isProjectAndItemApisLoading&&t.isProjectAndItemVisible),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",!t.isProjectAndItemApisLoading&&t.isProjectAndItemVisible),I["\u0275\u0275advance"](4),I["\u0275\u0275property"]("ngIf",t.isProjectAndItemApisLoading&&!t.isProjectAndItemVisible),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",!t.isProjectAndItemApisLoading&&t.isProjectAndItemVisible&&t.expenseList.length>0),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",1==t.areExpenseMilestonesLoading),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",!t.isProjectAndItemApisLoading&&t.isProjectAndItemVisible&&t.expenseList.length>0),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",!t.isProjectAndItemApisLoading&&!t.isProjectAndItemVisible))},directives:function(){return[f.c,b.b,x.e,x.v,x.y,g.a,f.i,h.NgIf,f.h,u.a,C.a,M.a,P.a,N.b,h.NgForOf,S.c,D,_.b,v.a,Y.a]},styles:[".create-billable-claims-popup-styles[_ngcontent-%COMP%]{min-height:100%}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .left-half[_ngcontent-%COMP%]{background-color:#f6f6f0}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .create-milestone[_ngcontent-%COMP%]{font-weight:500;font-size:15px}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .milestone-header[_ngcontent-%COMP%]{font-weight:450;font-size:13px;color:#a3a3a3}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .milestone-header-bigger[_ngcontent-%COMP%]{font-weight:450;font-size:14px;color:#a3a3a3}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .milestone-category[_ngcontent-%COMP%], .create-billable-claims-popup-styles[_ngcontent-%COMP%]   .milestone-description[_ngcontent-%COMP%]{font-weight:500;font-size:13px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .milestone-category[_ngcontent-%COMP%]{color:#f15b64;margin-top:.15rem!important}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .milestone-list-item[_ngcontent-%COMP%]{border:1px solid #d6cece;cursor:pointer}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .milestone-item[_ngcontent-%COMP%]{border-right:1px solid #d6cece}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .milestone-item-header[_ngcontent-%COMP%]{font-weight:450;font-size:12px;color:#a3a3a3}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .milestone-item-description[_ngcontent-%COMP%]{font-weight:500;font-size:12px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .milestone-department[_ngcontent-%COMP%]{font-weight:500;font-size:13px;margin-top:.15rem!important;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .milestone-list[_ngcontent-%COMP%]{height:67vh!important;overflow-y:scroll}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .milestone-list-loading[_ngcontent-%COMP%]{height:67vh!important;display:flex;align-items:center;justify-content:center}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .milestone-list-loading-more[_ngcontent-%COMP%]{height:60vh!important;overflow-y:scroll}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .claim-list-with-project[_ngcontent-%COMP%]{height:35vh!important;overflow-y:scroll}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .claim-list[_ngcontent-%COMP%]{height:50vh!important;overflow-y:scroll}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .milestone-list-bottom[_ngcontent-%COMP%]{background-color:#f6f6f0}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{color:#868683;font-size:20px}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .align-right[_ngcontent-%COMP%]{margin-right:0;margin-left:auto}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .save-btn-icon[_ngcontent-%COMP%]{height:24px;font-size:20px;width:30px;margin-left:5px}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .mandatory-red[_ngcontent-%COMP%]{margin-left:1%;color:#cf0001}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .custom-ts-select-option[_ngcontent-%COMP%]{font-size:13px;text-align:center;color:#1a1a1a}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .col-2-5[_ngcontent-%COMP%]{flex:0 0 19%;max-width:19%}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .red-btn[_ngcontent-%COMP%]{width:100%;height:45px;max-width:100px;background:#f15b64;font-size:13px;color:#fff;font-weight:500}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .red-btn[_ngcontent-%COMP%]:disabled{background:grey}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .total-milestone-header[_ngcontent-%COMP%]{font-weight:450;font-size:14px;color:#a3a3a3}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .total-milestone-description[_ngcontent-%COMP%]{font-weight:450;font-size:14px}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .dp-class[_ngcontent-%COMP%]{background:0 0;border:1px solid #d3d3d3;border-radius:4px;font-size:12px;font-weight:500;height:40px;margin-top:3px;cursor:pointer;text-align:center}.create-billable-claims-popup-styles[_ngcontent-%COMP%]     .md-drppicker{top:17px!important;left:270px!important}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .app-input-search-class[_ngcontent-%COMP%]     .mat-form-field-flex{padding-bottom:3px!important}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .app-input-search-class[_ngcontent-%COMP%]     input.mat-input-element{font-size:14px!important;padding-top:4px!important}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .app-input-search-class[_ngcontent-%COMP%]     .mat-form-field-infix{font-size:14px!important}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .cost-centre-item[_ngcontent-%COMP%]{cursor:pointer;border-radius:6px}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .cost-centre-item[_ngcontent-%COMP%]:hover{cursor:pointer;border-radius:6px;background-color:#fff}.create-billable-claims-popup-styles[_ngcontent-%COMP%]     .mat-checkbox-checked.mat-accent .mat-checkbox-background:disabled, .create-billable-claims-popup-styles[_ngcontent-%COMP%]     .mat-checkbox-disabled.mat-checkbox-checked .mat-checkbox-background, .create-billable-claims-popup-styles[_ngcontent-%COMP%]     .mat-checkbox .mat-ripple-element{background:#f15b64!important}"]}),e})()},XvyE:function(e,t,n){"use strict";n.r(t),n.d(t,"CreateBillableClaimsPopupComponent",(function(){return de}));var i=n("mrSG"),a=n("0IaG"),l=n("wd/R"),o=n("1G5W"),s=n("XNiG"),r=n("xG9w"),c=n("PSD3"),m=n.n(c),p=n("Kj3r"),d=n("/uUt"),h=n("ofXK"),u=n("bTqV"),g=n("NFeN"),C=n("Qu3c"),f=n("kmnG"),b=n("qFsG"),x=n("3Pt+"),y=n("d3UM"),v=n("bSwM"),_=n("YhS8"),S=(n("Xi0T"),n("Xa2L")),P=n("dlKe"),M=(n("KFJe"),n("JqCM")),I=n("fXoL"),L=n("tk/3"),O=n("FKr1"),w=n("WJ5W");const E=["matSelect"];function k(e,t){1&e&&(I["\u0275\u0275elementStart"](0,"mat-option"),I["\u0275\u0275elementStart"](1,"div",9),I["\u0275\u0275element"](2,"div"),I["\u0275\u0275element"](3,"div"),I["\u0275\u0275element"](4,"div"),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"]())}function A(e,t){if(1&e&&(I["\u0275\u0275elementStart"](0,"mat-option",10),I["\u0275\u0275text"](1),I["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;I["\u0275\u0275property"]("matTooltip",e.kmsis_field)("value",e),I["\u0275\u0275advance"](1),I["\u0275\u0275textInterpolate1"](" ",e.kmsis_field,"")}}function j(e,t){1&e&&(I["\u0275\u0275elementStart"](0,"mat-option"),I["\u0275\u0275elementStart"](1,"div",9),I["\u0275\u0275element"](2,"div"),I["\u0275\u0275element"](3,"div"),I["\u0275\u0275element"](4,"div"),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"]())}function V(e,t){if(1&e){const e=I["\u0275\u0275getCurrentView"]();I["\u0275\u0275elementStart"](0,"div",11),I["\u0275\u0275elementStart"](1,"button",12),I["\u0275\u0275listener"]("click",(function(){return I["\u0275\u0275restoreView"](e),I["\u0275\u0275nextContext"]().closeFilter()})),I["\u0275\u0275text"](2,"Close"),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](3,"button",13),I["\u0275\u0275listener"]("click",(function(t){return I["\u0275\u0275restoreView"](e),I["\u0275\u0275nextContext"]().clearFilter(t)})),I["\u0275\u0275text"](4,"Clear"),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](5,"button",14),I["\u0275\u0275listener"]("click",(function(){return I["\u0275\u0275restoreView"](e),I["\u0275\u0275nextContext"]().applyFilter()})),I["\u0275\u0275text"](6,"Apply"),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"]()}}let D=(()=>{class e{constructor(e){this._http=e,this.apiParamItems=[],this.selectedValues=new I.EventEmitter,this.list=[],this.startIndex=0,this.endIndex=0,this.isLazyLoadingComplete=!1,this.searchCtrl=new x.j,this.selectedValCtrl=new x.j,this.searchString="",this._onDestroy=new s.b,this.isLoading=!1,this.isLazyLoading=!1,this.selectedValuesArray=[],this.msg=new s.b,this.removeOption=e=>{this.msg.next(e)}}ngOnInit(){return Object(i.c)(this,void 0,void 0,(function*(){this.searchCtrl.valueChanges.pipe(Object(o.a)(this._onDestroy),Object(p.a)(700)).subscribe(e=>Object(i.c)(this,void 0,void 0,(function*(){if(null!=e&&""!=e)this.resetIndices(),this.searchString=this.searchCtrl.value,this.fetchData(!1);else if(null!=e&&""==e){let e=10;null!=this.lazyLoadingCount&&this.lazyLoadingCount>10&&(e=this.lazyLoadingCount),this.list.length<e&&(this.resetIndices(),this.initCall())}}))),this.selectedValCtrl.valueChanges.subscribe(e=>{this.selectedValuesArray=e}),this.msg.asObservable(),this.resetIndices(),this.initCall()}))}applyFilter(){this.selectedValues.emit(this.selectedValuesArray),this.closeFilter()}clearFilter(e){this.selectedValuesArray=[];let t=this.selectedValuesArray&&this.selectedValuesArray.length>0;this.selectedValCtrl.reset(),e.stopPropagation(),t?this.applyFilter():(this.applyFilter(),this.closeFilter())}closeFilter(){this.matSelect.close()}initCall(){this.searchString="",this.fetchData(!1)}removeSelectedOption(e){let t=[];t=this.selectedValCtrl.value,t.splice(e,1),this.selectedValCtrl.patchValue([...t]),this.selectedValues.emit(this.selectedValCtrl.value)}fetchData(e){const t={headers:new L.f({"Content-Type":"application/json",Authorization:"Bearer "+this.token})};e?(this.isLazyLoading=!0,this.isLoading=!1):(this.isLazyLoading=!1,this.isLoading=!0);let n={searchText:this.searchString,isFromMulSelSearchInput:!0};null!=this.lazyLoadingCount&&this.lazyLoadingCount>0&&(n.startIndex=this.startIndex,n.endIndex=this.endIndex),n.apiParamItems=this.apiParamItems,this._http.post(this.API_URL?this.API_URL:"",n,t).subscribe(t=>{if(t&&t.messType&&"S"==t.messType&&t.data&&t.data.length>0){let e=r.filter(t.data,(function(e){let t={};for(let i of this.optionLabel)t[i]=e[i];let n=r.where(this.list,t);if(!(n&&n.length>0))return e}),this);this.list=this.list.concat(e);for(let t of this.list){t.kmsis_field="";for(let e=0;e<this.optionLabel.length;e++)0!=e&&(t.kmsis_field+=" - "),t.kmsis_field+=t[this.optionLabel[e]]}null!=this.lazyLoadingCount&&this.lazyLoadingCount>0&&(this.startIndex+=this.lazyLoadingCount,this.endIndex+=this.lazyLoadingCount)}else t&&t.messType&&"S"==t.messType&&t.data&&0==t.data.length&&null!=this.lazyLoadingCount&&this.lazyLoadingCount>0&&(this.isLazyLoadingComplete=!0);e?this.isLazyLoading=!1:this.isLoading=!1},t=>{e?this.isLazyLoading=!1:this.isLoading=!1,console.error(t)})}resetIndices(){this.list=null!=this.selectedValCtrl.value?this.selectedValCtrl.value:[];for(let e of this.list){e.kmsis_field="";for(let t=0;t<this.optionLabel.length;t++)0!=t&&(e.kmsis_field+=" - "),e.kmsis_field+=e[this.optionLabel[t]]}null!=this.lazyLoadingCount&&this.lazyLoadingCount>0&&(this.startIndex=0,this.endIndex=this.lazyLoadingCount,this.isLazyLoadingComplete=!1)}onScroll(e,t){e&&this[t].panel.nativeElement.addEventListener("scroll",e=>{this.isLazyLoadingComplete||this.isLazyLoading||this[t].panel.nativeElement.scrollTop!==this[t].panel.nativeElement.scrollHeight-this[t].panel.nativeElement.offsetHeight||this.fetchData(!0)})}}return e.\u0275fac=function(t){return new(t||e)(I["\u0275\u0275directiveInject"](L.c))},e.\u0275cmp=I["\u0275\u0275defineComponent"]({type:e,selectors:[["app-expense-multi-select"]],viewQuery:function(e,t){if(1&e&&I["\u0275\u0275viewQuery"](E,!0),2&e){let e;I["\u0275\u0275queryRefresh"](e=I["\u0275\u0275loadQuery"]())&&(t.matSelect=e.first)}},inputs:{label:"label",optionLabel:"optionLabel",API_URL:"API_URL",token:"token",lazyLoadingCount:"lazyLoadingCount",apiParamItems:"apiParamItems"},outputs:{selectedValues:"selectedValues"},decls:14,vars:11,consts:[["appearance","outline",1,"full-width"],["multiple","",1,"col-10-5","p-0",3,"placeholder","formControl","openedChange"],["matSelect",""],["noEntriesFoundLabel","No Options Found!",3,"disableScrollToActiveOnOptionsChanged","formControl","placeholderLabel","hideClearSearchButton"],[4,"ngIf"],[3,"matTooltip","value",4,"ngFor","ngForOf"],["class","bottom-buttons p-0 row",4,"ngIf"],["mat-icon-button","","matTooltip","Clear Filter",1,"col-1","p-0","clear-button",3,"click"],[1,"clear-button-icon"],["matTooltip","Loading",1,"lds-facebook"],[3,"matTooltip","value"],[1,"bottom-buttons","p-0","row"],["mat-raised-button","","matTooltip","Close",1,"close-filter-button","p-0","pl-2","pr-2","mr-2",3,"click"],["mat-raised-button","","matTooltip","Clear",1,"clear-filter-button","p-0","pl-2","pr-2","mr-2",3,"click"],["mat-raised-button","","matTooltip","Apply",1,"apply-filter-button","p-0","pl-2","pr-2",3,"click"]],template:function(e,t){1&e&&(I["\u0275\u0275elementStart"](0,"mat-form-field",0),I["\u0275\u0275elementStart"](1,"mat-label"),I["\u0275\u0275text"](2),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](3,"mat-select",1,2),I["\u0275\u0275listener"]("openedChange",(function(e){return t.onScroll(e,"matSelect")})),I["\u0275\u0275elementStart"](5,"mat-option"),I["\u0275\u0275element"](6,"ngx-mat-select-search",3),I["\u0275\u0275elementEnd"](),I["\u0275\u0275template"](7,k,5,0,"mat-option",4),I["\u0275\u0275template"](8,A,2,3,"mat-option",5),I["\u0275\u0275template"](9,j,5,0,"mat-option",4),I["\u0275\u0275template"](10,V,7,0,"div",6),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](11,"button",7),I["\u0275\u0275listener"]("click",(function(e){return t.clearFilter(e)})),I["\u0275\u0275elementStart"](12,"mat-icon",8),I["\u0275\u0275text"](13,"clear"),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"]()),2&e&&(I["\u0275\u0275advance"](2),I["\u0275\u0275textInterpolate"](t.label),I["\u0275\u0275advance"](1),I["\u0275\u0275propertyInterpolate"]("placeholder",t.label),I["\u0275\u0275property"]("formControl",t.selectedValCtrl),I["\u0275\u0275advance"](3),I["\u0275\u0275property"]("disableScrollToActiveOnOptionsChanged",!0)("formControl",t.searchCtrl)("placeholderLabel","Search "+t.label+" ...")("hideClearSearchButton",!1),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",t.isLoading),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngForOf",t.list),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",t.isLazyLoading),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",!t.isLazyLoading&&!t.isLoading))},directives:[f.c,f.g,y.c,x.v,x.k,O.p,w.a,h.NgIf,h.NgForOf,u.a,C.a,g.a],styles:[".full-width[_ngcontent-%COMP%]{width:100%}.col-10-5[_ngcontent-%COMP%]{flex:0 0 87.666667%;max-width:87.666667%}.clear-button[_ngcontent-%COMP%]{margin-top:-2px;margin-left:7px}.clear-button[_ngcontent-%COMP%], .clear-button-icon[_ngcontent-%COMP%]{height:18px!important;width:18px!important;line-height:18px!important}.clear-button-icon[_ngcontent-%COMP%]{font-size:15px!important;color:#a9a9a9!important;margin-bottom:6px!important}.bottom-buttons[_ngcontent-%COMP%]{width:100%;position:sticky;bottom:0;background:#fff}.apply-filter-button[_ngcontent-%COMP%]{width:30%;border-radius:0!important;color:#fff;background-color:#df514c}.clear-filter-button[_ngcontent-%COMP%]{border-radius:0!important;color:#df514c;border:1px solid #df514c}.clear-filter-button[_ngcontent-%COMP%], .close-filter-button[_ngcontent-%COMP%]{width:30%;background:#fff;margin-right:.85rem!important}.close-filter-button[_ngcontent-%COMP%]{border-radius:0!important;color:grey;border:1px solid grey}.lds-facebook[_ngcontent-%COMP%]{display:inline-block;position:sticky;width:100%;height:24px;margin-left:95px}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{display:inline-block;position:absolute;left:4px;width:5px;background:#cf0001;animation:lds-facebook 1.2s cubic-bezier(0,.5,.5,1) infinite}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:first-child{left:8px;animation-delay:-.24s}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:nth-child(2){left:16px;animation-delay:-.12s}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:nth-child(3){left:24px;animation-delay:0}@keyframes lds-facebook{0%{top:8px;height:32px}50%,to{top:12px;height:16px}}"]}),e})();n("/1cH");var T=n("LcQX"),F=n("n/nF"),z=n("XXEo"),N=n("vxfF"),Y=n("me71");function B(e,t){if(1&e){const e=I["\u0275\u0275getCurrentView"]();I["\u0275\u0275elementStart"](0,"button",34),I["\u0275\u0275listener"]("click",(function(){return I["\u0275\u0275restoreView"](e),I["\u0275\u0275nextContext"]().clearMilestoneName()})),I["\u0275\u0275elementStart"](1,"mat-icon",35),I["\u0275\u0275text"](2," close "),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"]()}}function R(e,t){if(1&e){const e=I["\u0275\u0275getCurrentView"]();I["\u0275\u0275elementStart"](0,"button",34),I["\u0275\u0275listener"]("click",(function(){return I["\u0275\u0275restoreView"](e),I["\u0275\u0275nextContext"]().clearCostCenterSearch()})),I["\u0275\u0275elementStart"](1,"mat-icon",36),I["\u0275\u0275text"](2," close "),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"]()}}function U(e,t){1&e&&I["\u0275\u0275element"](0,"hr",45)}function q(e,t){if(1&e){const e=I["\u0275\u0275getCurrentView"]();I["\u0275\u0275elementStart"](0,"div",39),I["\u0275\u0275elementStart"](1,"div",40),I["\u0275\u0275elementStart"](2,"div",41),I["\u0275\u0275listener"]("click",(function(){I["\u0275\u0275restoreView"](e);const n=t.$implicit;return I["\u0275\u0275nextContext"](2).showBillableClaims(n)})),I["\u0275\u0275elementStart"](3,"span"),I["\u0275\u0275text"](4),I["\u0275\u0275elementStart"](5,"span",42),I["\u0275\u0275text"](6),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](7,"span",43),I["\u0275\u0275text"](8),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275template"](9,U,1,0,"hr",44),I["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=I["\u0275\u0275nextContext"](2);I["\u0275\u0275advance"](2),I["\u0275\u0275propertyInterpolate3"]("matTooltip","",null!=e&&e.name&&null!=(null==e?null:e.name)?null==e?null:e.name:""," - ",null!=e&&e.description&&null!=(null==e?null:e.description)?null==e?null:e.description:""," (",null!=e&&e.claim_count&&null!=(null==e?null:e.claim_count)?null==e?null:e.claim_count:"",")"),I["\u0275\u0275advance"](2),I["\u0275\u0275textInterpolate1"]("",null==e?null:e.name," - "),I["\u0275\u0275advance"](2),I["\u0275\u0275textInterpolate"](null==e?null:e.description),I["\u0275\u0275advance"](2),I["\u0275\u0275textInterpolate1"]("(",null==e?null:e.claim_count,")"),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",n.costCenterClaimList.length>1)}}function K(e,t){if(1&e){const e=I["\u0275\u0275getCurrentView"]();I["\u0275\u0275elementStart"](0,"div",37),I["\u0275\u0275listener"]("scrolled",(function(){return I["\u0275\u0275restoreView"](e),I["\u0275\u0275nextContext"]().getCostCenterList()})),I["\u0275\u0275element"](1,"div"),I["\u0275\u0275elementStart"](2,"div"),I["\u0275\u0275template"](3,q,10,7,"div",38),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"]()}if(2&e){const e=I["\u0275\u0275nextContext"]();I["\u0275\u0275classMapInterpolate1"]("col-12 p-0 ",!e.isProjectAndItemApisLoading&&e.isProjectAndItemVisible?"claim-list-with-project":"claim-list",""),I["\u0275\u0275property"]("infiniteScrollDistance",2)("scrollWindow",!1)("stopPropagation",!0),I["\u0275\u0275advance"](3),I["\u0275\u0275property"]("ngForOf",e.costCenterClaimList)}}function W(e,t){1&e&&I["\u0275\u0275element"](0,"mat-spinner",46)}function G(e,t){1&e&&(I["\u0275\u0275elementStart"](0,"p",11),I["\u0275\u0275text"](1," Portfolio Name"),I["\u0275\u0275elementEnd"]())}function X(e,t){if(1&e&&(I["\u0275\u0275elementStart"](0,"p",47),I["\u0275\u0275text"](1),I["\u0275\u0275elementEnd"]()),2&e){const e=I["\u0275\u0275nextContext"]();I["\u0275\u0275property"]("matTooltip",e.projectName),I["\u0275\u0275advance"](1),I["\u0275\u0275textInterpolate1"](" ",e.projectName,"")}}function J(e,t){1&e&&(I["\u0275\u0275elementStart"](0,"p",11),I["\u0275\u0275text"](1," Project Name"),I["\u0275\u0275elementEnd"]())}function H(e,t){if(1&e&&(I["\u0275\u0275elementStart"](0,"p",47),I["\u0275\u0275text"](1),I["\u0275\u0275elementEnd"]()),2&e){const e=I["\u0275\u0275nextContext"]();I["\u0275\u0275property"]("matTooltip",e.itemName),I["\u0275\u0275advance"](1),I["\u0275\u0275textInterpolate1"](" ",e.itemName,"")}}function Q(e,t){if(1&e){const e=I["\u0275\u0275getCurrentView"]();I["\u0275\u0275elementStart"](0,"button",34),I["\u0275\u0275listener"]("click",(function(){return I["\u0275\u0275restoreView"](e),I["\u0275\u0275nextContext"](2).clearSearch()})),I["\u0275\u0275elementStart"](1,"mat-icon",36),I["\u0275\u0275text"](2," close "),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"]()}}function $(e,t){if(1&e){const e=I["\u0275\u0275getCurrentView"]();I["\u0275\u0275elementStart"](0,"mat-form-field",48),I["\u0275\u0275elementStart"](1,"span",13),I["\u0275\u0275elementStart"](2,"mat-icon",14),I["\u0275\u0275text"](3,"search"),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](4,"input",49),I["\u0275\u0275listener"]("ngModelChange",(function(t){return I["\u0275\u0275restoreView"](e),I["\u0275\u0275nextContext"]().searchParameter=t}))("keyup.enter",(function(){return I["\u0275\u0275restoreView"](e),I["\u0275\u0275nextContext"]().callSearchApi()}))("ngModelChange",(function(){return I["\u0275\u0275restoreView"](e),I["\u0275\u0275nextContext"]().onSearchParameterChange()})),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](5,"mat-icon",9),I["\u0275\u0275template"](6,Q,3,0,"button",10),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"]()}if(2&e){const e=I["\u0275\u0275nextContext"]();I["\u0275\u0275advance"](4),I["\u0275\u0275property"]("ngModel",e.searchParameter),I["\u0275\u0275advance"](2),I["\u0275\u0275property"]("ngIf",e.searchParameter&&""!=e.searchParameter)}}const Z=function(){return["name"]};function ee(e,t){if(1&e){const e=I["\u0275\u0275getCurrentView"]();I["\u0275\u0275elementStart"](0,"app-expense-multi-select",50),I["\u0275\u0275listener"]("selectedValues",(function(t){return I["\u0275\u0275restoreView"](e),I["\u0275\u0275nextContext"]().selectCategory(t)})),I["\u0275\u0275elementEnd"]()}if(2&e){const e=I["\u0275\u0275nextContext"]();I["\u0275\u0275property"]("token",e.token)("optionLabel",I["\u0275\u0275pureFunction0"](4,Z))("API_URL",e.searchClaimCategories)("lazyLoadingCount",20)}}const te=function(){return{applyLabel:"Apply",format:"DD MMM YYYY",customRangeLabel:"Custom Duration"}};function ne(e,t){if(1&e){const e=I["\u0275\u0275getCurrentView"]();I["\u0275\u0275elementStart"](0,"input",51),I["\u0275\u0275listener"]("ngModelChange",(function(t){return I["\u0275\u0275restoreView"](e),I["\u0275\u0275nextContext"]().duration=t}))("change",(function(t){return I["\u0275\u0275restoreView"](e),I["\u0275\u0275nextContext"]().selectDuration(t)})),I["\u0275\u0275elementEnd"]()}if(2&e){const e=I["\u0275\u0275nextContext"]();I["\u0275\u0275property"]("showCustomRangeLabel",!0)("locale",I["\u0275\u0275pureFunction0"](7,te))("alwaysShowCalendars",!0)("showCancel",!0)("ranges",e.durationRanges)("linkedCalendars",!0)("ngModel",e.duration)}}function ie(e,t){1&e&&(I["\u0275\u0275elementStart"](0,"div",52),I["\u0275\u0275element"](1,"mat-spinner",53),I["\u0275\u0275elementEnd"]())}function ae(e,t){if(1&e){const e=I["\u0275\u0275getCurrentView"]();I["\u0275\u0275elementStart"](0,"div",59),I["\u0275\u0275listener"]("click",(function(){I["\u0275\u0275restoreView"](e);const n=t.$implicit;return I["\u0275\u0275nextContext"](2).selectExpenseListItem(n)})),I["\u0275\u0275elementStart"](1,"div",60),I["\u0275\u0275element"](2,"mat-checkbox",61),I["\u0275\u0275elementStart"](3,"p",62),I["\u0275\u0275text"](4),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](5,"div",63),I["\u0275\u0275elementStart"](6,"div",64),I["\u0275\u0275elementStart"](7,"p",65),I["\u0275\u0275text"](8,"Expense Code"),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](9,"p",66),I["\u0275\u0275text"](10),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](11,"div",67),I["\u0275\u0275elementStart"](12,"p",65),I["\u0275\u0275text"](13,"Amount Requested"),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](14,"p",66),I["\u0275\u0275text"](15),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](16,"div",67),I["\u0275\u0275elementStart"](17,"p",65),I["\u0275\u0275text"](18,"Amount Verified"),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](19,"p",66),I["\u0275\u0275text"](20),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](21,"div",67),I["\u0275\u0275elementStart"](22,"p",65),I["\u0275\u0275text"](23,"Requested By"),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](24,"div",68),I["\u0275\u0275element"](25,"app-user-image",69),I["\u0275\u0275elementStart"](26,"p",70),I["\u0275\u0275text"](27),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](28,"div",71),I["\u0275\u0275elementStart"](29,"p",65),I["\u0275\u0275text"](30,"Claim Created On"),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](31,"p",66),I["\u0275\u0275text"](32),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;I["\u0275\u0275advance"](2),I["\u0275\u0275property"]("checked",e.isChecked),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("matTooltip",e.expense_category),I["\u0275\u0275advance"](1),I["\u0275\u0275textInterpolate1"](" ",e.expense_category,""),I["\u0275\u0275advance"](5),I["\u0275\u0275property"]("matTooltip",e.expense_code),I["\u0275\u0275advance"](1),I["\u0275\u0275textInterpolate"](e.expense_code),I["\u0275\u0275advance"](4),I["\u0275\u0275property"]("matTooltip",e.expense_request_amount),I["\u0275\u0275advance"](1),I["\u0275\u0275textInterpolate1"]("",e.expense_request_amount," "),I["\u0275\u0275advance"](4),I["\u0275\u0275property"]("matTooltip",e.expense_claim_amount),I["\u0275\u0275advance"](1),I["\u0275\u0275textInterpolate1"]("",e.expense_claim_amount," "),I["\u0275\u0275advance"](4),I["\u0275\u0275property"]("matTooltip",e.requested_by),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("id",e?e.requested_by_oid:"")("imgWidth","25px")("imgHeight","25px"),I["\u0275\u0275advance"](2),I["\u0275\u0275textInterpolate"](e.requested_by),I["\u0275\u0275advance"](4),I["\u0275\u0275property"]("matTooltip",e.billed_on),I["\u0275\u0275advance"](1),I["\u0275\u0275textInterpolate1"](" ",e.billed_on,"")}}function le(e,t){if(1&e){const e=I["\u0275\u0275getCurrentView"]();I["\u0275\u0275elementStart"](0,"div",54),I["\u0275\u0275listener"]("scrolled",(function(){return I["\u0275\u0275restoreView"](e),I["\u0275\u0275nextContext"]().getExpenseListBasedOnCostCentre(!1)})),I["\u0275\u0275elementStart"](1,"span",55),I["\u0275\u0275listener"]("click",(function(){return I["\u0275\u0275restoreView"](e),I["\u0275\u0275nextContext"]().handleCheckBox("")})),I["\u0275\u0275elementStart"](2,"mat-checkbox",56),I["\u0275\u0275elementStart"](3,"span",57),I["\u0275\u0275text"](4,"Select All"),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275template"](5,ae,33,16,"div",58),I["\u0275\u0275elementEnd"]()}if(2&e){const e=I["\u0275\u0275nextContext"]();I["\u0275\u0275classMapInterpolate1"]("col-12 p-0 pl-3 pr-3 ",e.areExpenseMilestonesLoading?"milestone-list-loading-more":"milestone-list",""),I["\u0275\u0275property"]("infiniteScrollDistance",2)("scrollWindow",!1),I["\u0275\u0275advance"](2),I["\u0275\u0275property"]("checked",e.isAllChecked),I["\u0275\u0275advance"](3),I["\u0275\u0275property"]("ngForOf",e.expenseList)}}function oe(e,t){1&e&&I["\u0275\u0275element"](0,"mat-spinner",72)}function se(e,t){if(1&e&&(I["\u0275\u0275elementStart"](0,"div",77),I["\u0275\u0275elementStart"](1,"p",78),I["\u0275\u0275text"](2,"Total Claim Value"),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](3,"p",79),I["\u0275\u0275text"](4),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"]()),2&e){const e=I["\u0275\u0275nextContext"](2);I["\u0275\u0275advance"](4),I["\u0275\u0275textInterpolate1"]("",e.totalClaimValue," ")}}function re(e,t){1&e&&I["\u0275\u0275element"](0,"mat-spinner",80)}function ce(e,t){if(1&e){const e=I["\u0275\u0275getCurrentView"]();I["\u0275\u0275elementStart"](0,"button",81),I["\u0275\u0275listener"]("click",(function(){return I["\u0275\u0275restoreView"](e),I["\u0275\u0275nextContext"](2).createBillableClaim()})),I["\u0275\u0275text"](1,"Create"),I["\u0275\u0275elementStart"](2,"mat-icon",82),I["\u0275\u0275text"](3,"navigate_next "),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"]()}if(2&e){const e=I["\u0275\u0275nextContext"](2);I["\u0275\u0275property"]("disabled",!e.areAnyExpensesSelected)}}function me(e,t){if(1&e&&(I["\u0275\u0275elementStart"](0,"div",73),I["\u0275\u0275template"](1,se,5,1,"div",74),I["\u0275\u0275template"](2,re,1,0,"mat-spinner",75),I["\u0275\u0275template"](3,ce,4,1,"button",76),I["\u0275\u0275elementEnd"]()),2&e){const e=I["\u0275\u0275nextContext"]();I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",e.areAnyExpensesSelected),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",e.isCreatingMilestone),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",!e.isCreatingMilestone)}}function pe(e,t){if(1&e&&(I["\u0275\u0275elementStart"](0,"div",83),I["\u0275\u0275element"](1,"img",84),I["\u0275\u0275elementStart"](2,"p",4),I["\u0275\u0275text"](3,"No Claims Here"),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](4,"p",85),I["\u0275\u0275text"](5),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"]()),2&e){const e=I["\u0275\u0275nextContext"]();I["\u0275\u0275advance"](5),I["\u0275\u0275textInterpolate1"]("Populate claim list by selecting a ",e.costCenterFieldLabel?e.costCenterFieldLabel:"Cost center","!")}}let de=(()=>{class e{constructor(e,t,n,i,a,o){this.dialogRef=e,this.inData=t,this.utilityService=n,this.expensesBillableClaimsService=i,this.authService=a,this.spinnerService=o,this.token=this.authService.getToken(),this.searchClaimCategories=window.location.origin+"/api/exPrimary2/searchClaimCategories",this.searchParameter="",this.milestoneName="",this.projectName="",this.itemName="",this.projectId=0,this.itemId=0,this.isProjectAndItemVisible=!1,this.areExpenseMilestonesLoading=!1,this.expenseList=[],this.totalClaimValue="0",this.selectedCategoryList=[],this.costCenterClaimList=[],this.duration={startDate:"",endDate:""},this.durationRanges={"This Month":[l().startOf("month"),l().endOf("month")],"Previuos Year & Current Year":[l().subtract(2,"year").startOf("year"),l().endOf("year")],"Last Month":[l().subtract(1,"month").startOf("month"),l().subtract(1,"month").endOf("month")],"Next Month":[l().add(1,"month").startOf("month"),l().add(1,"month").endOf("month")],"Upcoming 3 Months":[l().startOf("month"),l().add(2,"month").endOf("month")],"This Year":[l().startOf("year"),l().endOf("year")],"Previous Year":[l().subtract(1,"year").startOf("year"),l().subtract(1,"year").endOf("year")]},this.isProjectAndItemApisLoading=!1,this._onDestroy=new s.b,this.startIndex=0,this.noOfRecords=10,this.areAnyExpensesSelected=!1,this.isCreatingMilestone=!1,this.costCentreListStartIndex=0,this.costCenterNofRecords=50,this.subjectKeyUp=new s.b,this.isAllChecked=!1,this.subjectKeyUp.pipe(Object(p.a)(100),Object(d.a)()).subscribe(e=>{this.getCostCenterSearchResult(e)})}handleCheckBox(e){return Object(i.c)(this,void 0,void 0,(function*(){if(this.isAllChecked=!this.isAllChecked,!this.isAllChecked)return this.totalClaimValue=this.utilityService.getAmountCurrencyFormat(0,this.modalParams.defaultCurrency),r.each(this.expenseList,e=>{e.isChecked=this.isAllChecked}),void(this.areAnyExpensesSelected=!1);1==this.isAllChecked&&(this.areAnyExpensesSelected=!0),r.each(this.expenseList,e=>{e.isChecked=this.isAllChecked});let e={user_oid:this.authService.getProfile().profile.oid,params:{project_id:this.projectId,project_item_id:this.itemId,milestone_name:this.milestoneName,cost_center:this.costCentreTemp.name,total_selected_claims_approved_value:this.resolveClaimApprovedValue(),expense_list_all:this.expenseList,expense_list:this.resolveExpenseList()},isAllItemsChecked:this.isAllChecked,cost_center:this.costCentreTemp.name,search_parameter:this.searchParameter,filter_params:{expense_category:this.selectedCategoryList,t_approved_on_duration:{start_date:l(this.duration.startDate,this.modalParams.defaultDateFormat).format("YYYY-MM-DD"),end_date:l(this.duration.endDate,this.modalParams.defaultDateFormat).format("YYYY-MM-DD")}}},t=yield this.expensesBillableClaimsService.getAllBillableClaimByCostCenter(this.costCenterSearchParameter,e);if("S"==t.messType){let e=r.where(t.data,{currency_code:this.modalParams.defaultCurrency});if(e.length>0){let t=this.utilityService.getAmountCurrencyFormat(e[0].value,this.modalParams.defaultCurrency);this.totalClaimValue=this.modalParams.defaultCurrency+" "+t}else this.totalClaimValue=this.modalParams.defaultCurrency+" "+this.utilityService.getAmountCurrencyFormat(0,this.modalParams.defaultCurrency)}else this.totalClaimValue=this.modalParams.defaultCurrency+" "+this.utilityService.getAmountCurrencyFormat(0,this.modalParams.defaultCurrency)}))}ngOnInit(){this.getFieldConfig(),this.getBillableClaimGroupbyCostCenter(),this.modalParams=this.inData.modalParams,this.duration={startDate:l().subtract(2,"year").startOf("year").format(this.modalParams.defaultDateFormat),endDate:l().endOf("year").format(this.modalParams.defaultDateFormat)}}getBillableClaimGroupbyCostCenter(){return Object(i.c)(this,void 0,void 0,(function*(){let e=yield this.expensesBillableClaimsService.getBillableClaimByCostCenter(this.costCentreListStartIndex,this.costCenterNofRecords,this.costCenterSearchParameter);this.costCenterClaimList=this.costCenterClaimList.concat(e.data)}))}getCostCenterSearchResult(e){return Object(i.c)(this,void 0,void 0,(function*(){this.costCenterClaimList=[],this.costCenterSearchParameter=e;let t=yield this.expensesBillableClaimsService.getBillableClaimByCostCenter(this.costCentreListStartIndex,this.costCenterNofRecords,e);this.costCenterClaimList=t.data}))}onCostCenterSearchParameterChange(){this.costCenterClaimList=[],this.getBillableClaimGroupbyCostCenter()}getCostCenterList(){this.costCentreListStartIndex+=50,this.getBillableClaimGroupbyCostCenter()}clearCostCenterSearch(){this.subjectKeyUp.next("")}getFieldConfig(){this.expensesBillableClaimsService.getExpenseFieldConfig().pipe(Object(o.a)(this._onDestroy)).subscribe(e=>Object(i.c)(this,void 0,void 0,(function*(){this.formFieldConfig=e.data,this.formFieldConfig&&null!=this.formFieldConfig&&r.each(this.formFieldConfig,e=>{"costCenterCode"==e.field_key&&(this.costCenterFieldLabel=null!=e.field_label?e.field_label:"Cost center")})})),e=>{console.log(e)})}showBillableClaims(e){this.costCentre=JSON.stringify(e),this.onCostCentreChange()}onSearchCostCenter(e){this.subjectKeyUp.next(e.target.value)}onCostCentreChange(){this.costCentre?(this.isProjectAndItemVisible=!1,this.isProjectAndItemApisLoading=!0,this.costCentreTemp=JSON.parse(this.costCentre),this.changeCostCentre()):(this.costCentreTemp=null,this.isProjectAndItemVisible=!1,this.projectName="",this.itemName="",this.projectId=0,this.itemId=0,this.areAnyExpensesSelected=!1,this.expenseList=[])}changeCostCentre(){this.getCostCentreProjectItemName()}getCostCentreProjectItemName(){null!=this.costCentreTemp&&this.expensesBillableClaimsService.getCostCentreProjectItemName({cost_center:this.costCentreTemp.name}).pipe(Object(o.a)(this._onDestroy)).subscribe(e=>Object(i.c)(this,void 0,void 0,(function*(){"S"==e.messType&&e.data&&e.data.length>0?(this.projectName=e.data[0].project_name,this.itemName=e.data[0].item_name,this.projectId=e.data[0].project_id,this.itemId=e.data[0].item_id,this.getExpenseListBasedOnCostCentre(!0)):"S"==e.messType&&e.data&&0==e.data.length?(this.projectName="N/A",this.itemName="N/A",this.projectId=0,this.itemId=0,this.getExpenseListBasedOnCostCentre(!0)):"E"==e.messType&&(this.utilityService.showErrorMessage(e,"KEBS"),this.projectName="",this.itemName="",this.isProjectAndItemVisible=!1,this.isProjectAndItemApisLoading=!1)})),e=>{this.isProjectAndItemVisible=!1,this.isProjectAndItemApisLoading=!1,this.projectName="",this.itemName="",console.log(e),this.utilityService.showErrorMessage(e,"KEBS")})}getExpenseListBasedOnCostCentre(e){if(null!=this.costCentreTemp){e?this.startIndex=0:this.areExpenseMilestonesLoading=!0;let t={cost_center:this.costCentreTemp.name,start_index:this.startIndex,no_of_records:this.noOfRecords,search_parameter:this.searchParameter,filter_params:{expense_category:this.selectedCategoryList,t_approved_on_duration:{start_date:l(this.duration.startDate,this.modalParams.defaultDateFormat).format("YYYY-MM-DD"),end_date:l(this.duration.endDate,this.modalParams.defaultDateFormat).format("YYYY-MM-DD")}}};this.expensesBillableClaimsService.getExpenseListBasedOnCostCentre(t).pipe(Object(o.a)(this._onDestroy)).subscribe(t=>Object(i.c)(this,void 0,void 0,(function*(){if("S"==t.messType&&t.data&&t.data.length>0){e?this.expenseList=t.data:(r.each(t.data,e=>{e.isChecked=this.isAllChecked}),this.expenseList=this.expenseList.concat(t.data)),this.startIndex=this.expenseList.length;for(let e of this.expenseList)e.billed_on=l(e.billed_on).format(this.modalParams.defaultDateFormat),e.expense_request_amount=this.resolveAmountAndCurrency(e.amount_requested),e.expense_approved_amount=this.resolveAmountAndValue(e.total_approved_amount),e.expense_claim_amount=this.resolveAmountAndCurrency(e.amount_claimed)}else"E"==t.messType?this.utilityService.showErrorMessage(t,"KEBS"):"S"==t.messType&&t.data&&0==t.data.length&&e&&(this.expenseList=[]);this.isProjectAndItemVisible=!0,this.areExpenseMilestonesLoading=!1,this.isProjectAndItemApisLoading=!1})),e=>{this.isProjectAndItemVisible=!1,this.areExpenseMilestonesLoading=!1,this.isProjectAndItemApisLoading=!1,this.expenseList=[],console.log(e),this.utilityService.showErrorMessage(e,"KEBS")})}}resolveAmountAndCurrency(e){let t=r.where(e,{currency_code:this.modalParams.defaultCurrency}),n=t.length>0?t[0].value:0;return n=this.utilityService.getAmountCurrencyFormat(n,this.modalParams.defaultCurrency),this.modalParams.defaultCurrency+" "+n}resolveAmountAndValue(e){let t=r.where(e,{currency_code:this.modalParams.defaultCurrency});return t.length>0?t[0].value:0}selectCategory(e){this.selectedCategoryList=r.pluck(e,"name"),this.areExpenseMilestonesLoading=!0,this.totalClaimValue="0.00",this.isAllChecked=!1,this.areAnyExpensesSelected=!1,this.getExpenseListBasedOnCostCentre(!0)}createBillableClaim(){let e=!1;for(let t of this.expenseList)t.isChecked&&(e=!0);if(!this.milestoneName.trim())return this.utilityService.showToastMessage("Milestone Name is mandatory!");if(null==this.costCentreTemp||""==this.costCentreTemp.name)return this.utilityService.showToastMessage("Cost Centre is mandatory!");if(!this.areAnyExpensesSelected)return this.utilityService.showToastMessage("At least one expense must be selected!");if(!e)return this.utilityService.showToastMessage("At least one expense must be selected!");if(null!=this.costCentreTemp){this.isCreatingMilestone=!0;let e={user_oid:this.authService.getProfile().profile.oid,params:{project_id:this.projectId,project_item_id:this.itemId,milestone_name:this.milestoneName,cost_center:this.costCentreTemp.name,total_selected_claims_approved_value:this.resolveClaimApprovedValue(),expense_list_all:this.expenseList,expense_list:this.resolveExpenseList()},isAllItemsChecked:this.isAllChecked,cost_center:this.costCentreTemp.name,search_parameter:this.searchParameter,filter_params:{expense_category:this.selectedCategoryList,t_approved_on_duration:{start_date:l(this.duration.startDate,this.modalParams.defaultDateFormat).format("YYYY-MM-DD"),end_date:l(this.duration.endDate,this.modalParams.defaultDateFormat).format("YYYY-MM-DD")}}};this.utilityService.openConfirmationSweetAlertWithCustom("Create New Expense Milestone ?","").then(t=>{1==t?(this.spinnerService.show(),this.expensesBillableClaimsService.billableMilestoneCreation(e).pipe(Object(o.a)(this._onDestroy)).subscribe(e=>Object(i.c)(this,void 0,void 0,(function*(){"S"==e.messType?(this.spinnerService.hide(),m.a.fire({title:"Milestone Created Successfully !",icon:"success",showConfirmButton:!0}),this.clearMilestoneName(),this.isCreatingMilestone=!1,this.closeModal()):"E"==e.messType&&(m.a.fire({title:"Milestone Creation Failed !",icon:"info",showConfirmButton:!0}),this.spinnerService.hide())})),e=>{console.error(e),m.a.fire({title:"Milestone Creation Failed !",icon:"info",showConfirmButton:!0}),this.isCreatingMilestone=!1,this.spinnerService.hide(),console.log(e)})):(this.isCreatingMilestone=!1,this.spinnerService.hide())})}}resolveClaimApprovedValue(){let e=[];for(let t of this.expenseList)if(t.isChecked)for(let n of t.total_approved_amount){let t=r.where(e,{currency_code:n.currency_code});0==t.length?e.push({currency_code:n.currency_code,value:n.value}):t[0].value+=n.value}return e}resolveExpenseList(){let e=[];for(let t of this.expenseList)t.isChecked&&e.push({expense_header_id:t.expense_header_id});return e}callSearchApi(){this.getExpenseListBasedOnCostCentre(!0)}clearSearch(){this.searchParameter="",this.callSearchApi()}onSearchParameterChange(){this.totalClaimValue="0.00",this.isAllChecked=!1,this.areAnyExpensesSelected=!1,""==this.searchParameter&&this.callSearchApi()}clearMilestoneName(){this.milestoneName=""}selectExpenseListItem(e){return Object(i.c)(this,void 0,void 0,(function*(){if(this.isAllChecked){e.isChecked=!e.isChecked,e.isChecked||(this.isAllChecked=!1),1==this.isAllChecked&&(this.areAnyExpensesSelected=!0);let t={user_oid:this.authService.getProfile().profile.oid,params:{project_id:this.projectId,project_item_id:this.itemId,milestone_name:this.milestoneName,cost_center:this.costCentreTemp.name,total_selected_claims_approved_value:this.resolveClaimApprovedValue(),expense_list_all:this.expenseList,expense_list:this.resolveExpenseList()},isAllItemsChecked:this.isAllChecked,cost_center:this.costCentreTemp.name,search_parameter:this.searchParameter,filter_params:{expense_category:this.selectedCategoryList,t_approved_on_duration:{start_date:l(this.duration.startDate,this.modalParams.defaultDateFormat).format("YYYY-MM-DD"),end_date:l(this.duration.endDate,this.modalParams.defaultDateFormat).format("YYYY-MM-DD")}}},n=yield this.expensesBillableClaimsService.getAllBillableClaimByCostCenter(this.costCenterSearchParameter,t);if("S"==n.messType){let e=r.where(n.data,{currency_code:this.modalParams.defaultCurrency});if(e.length>0){let t=this.utilityService.getAmountCurrencyFormat(e[0].value,this.modalParams.defaultCurrency);this.totalClaimValue=t}else this.totalClaimValue=this.utilityService.getAmountCurrencyFormat(0,this.modalParams.defaultCurrency)}else this.totalClaimValue=this.utilityService.getAmountCurrencyFormat(0,this.modalParams.defaultCurrency)}else{e.isChecked=!e.isChecked,this.areAnyExpensesSelected=!1,this.totalClaimValue=this.utilityService.getAmountCurrencyFormat(0,this.modalParams.defaultCurrency);let t=0;for(let e of this.expenseList)e.isChecked&&(t+=e.expense_approved_amount,this.areAnyExpensesSelected=!0);this.totalClaimValue=this.modalParams.defaultCurrency+" "+this.utilityService.getAmountCurrencyFormat(t,this.modalParams.defaultCurrency)}}))}selectDuration(e){if(e.startDate){let t=l(e.startDate).startOf("day"),n=l(e.endDate).endOf("day");this.duration={startDate:l(t).format("DD MMM YYYY"),endDate:l(n).format("DD MMM YYYY")}}this.areExpenseMilestonesLoading=!0,this.totalClaimValue="0.00",this.isAllChecked=!1,this.areAnyExpensesSelected=!1,this.getExpenseListBasedOnCostCentre(!0)}closeModal(){this.dialogRef.close({event:"Milestone Created"})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}}return e.\u0275fac=function(t){return new(t||e)(I["\u0275\u0275directiveInject"](a.h),I["\u0275\u0275directiveInject"](a.a),I["\u0275\u0275directiveInject"](T.a),I["\u0275\u0275directiveInject"](F.a),I["\u0275\u0275directiveInject"](z.a),I["\u0275\u0275directiveInject"](M.c))},e.\u0275cmp=I["\u0275\u0275defineComponent"]({type:e,selectors:[["create-billable-claims-popup"]],decls:45,vars:19,consts:[[1,"create-billable-claims-popup-styles","row"],[1,"col-3","p-0","left-half"],[1,"col-12","p-0","pl-3","pr-3","pt-3",2,"height","100%"],[2,"height","75%"],[1,"create-milestone","col-12","p-0","m-0"],[1,"milestone-header","col-12","p-0","pt-4","m-0","mb-1"],[1,"mandatory-red"],["appearance","outline",2,"width","100%","height","45px"],["matInput","","placeholder","Milestone Name","autocomplete","off",2,"padding-bottom","1px !important",3,"ngModel","ngModelChange"],["matSuffix",""],["mat-button","","matSuffix","","mat-icon-button","","style","height: 30px; width: 30px; line-height: 1",3,"click",4,"ngIf"],[1,"milestone-header","col-12","p-0","pt-3","m-0","mb-1"],["appearance","outline",1,"ml-auto","mr-auto",2,"width","100%"],["matPrefix",""],[2,"font-size","20px !important","color","black !important"],["matInput","","placeholder","Search","autocomplete","off",2,"padding-bottom","1px !important",3,"ngModel","ngModelChange","keyup.enter","keyup"],["infinite-scroll","","cdkScrollable","",3,"class","infiniteScrollDistance","scrollWindow","stopPropagation","scrolled",4,"ngIf"],[2,"height","25%"],["class","mt-4 ml-auto mr-auto","diameter","25","matTooltip","Loading ...",4,"ngIf"],["class","milestone-header col-12 p-0 pt-3 m-0 mb-1",4,"ngIf"],["class","milestone-description col-12 p-0 m-0",3,"matTooltip",4,"ngIf"],[1,"col-9","p-0"],[1,"col-12","p-0","row","pl-3","pt-2","pb-3"],["appearance","outline","style","width: 35%; height: 45px;","class","mr-4",4,"ngIf"],["style","width: 26%; height: 45px;","class","mr-4","label","Claim type",3,"token","optionLabel","API_URL","lazyLoadingCount","selectedValues",4,"ngIf"],["matTooltip","Duration","type","text","style","width: 26%; height: 41px;","class","mr-3 dp-class","ngxDaterangepickerMd","","placeholder","Duration",3,"showCustomRangeLabel","locale","alwaysShowCalendars","showCancel","ranges","linkedCalendars","ngModel","ngModelChange","change",4,"ngIf"],["mat-icon-button","","matTooltip","Close",1,"align-right","mr-2"],[1,"close-button",3,"click"],["class","col-12 p-0 milestone-list-loading",4,"ngIf"],["infinite-scroll","","cdkScrollable","",3,"class","infiniteScrollDistance","scrollWindow","scrolled",4,"ngIf"],["class","mt-3 ml-auto mr-auto","diameter","25","matTooltip","Loading ...",4,"ngIf"],["class","col-12 row p-0 pl-3 pr-3 mt-3",4,"ngIf"],["class","col-12 p-0 milestone-list-loading row","style","text-align: center;",4,"ngIf"],["size","medium","type","ball-clip-rotate","bdColor","rgba(236, 233, 233, 0.8)","color","#cf0001"],["mat-button","","matSuffix","","mat-icon-button","",2,"height","30px","width","30px","line-height","1",3,"click"],["matTooltip","Clear",2,"font-size","20px !important","color","#66615b !important"],["matTooltip","Clear Search",2,"font-size","20px !important","color","#66615b !important"],["infinite-scroll","","cdkScrollable","",3,"infiniteScrollDistance","scrollWindow","stopPropagation","scrolled"],["class","cost-centre-item",4,"ngFor","ngForOf"],[1,"cost-centre-item"],[1,"row","pt-2","pb-2"],[1,"col","p-0","pl-1",2,"white-space","nowrap","overflow","hidden","text-overflow","ellipsis","width","100%",3,"matTooltip","click"],[2,"color","#45546e"],[1,"pl-1",2,"color","#cf0001"],["style","margin-top: 0%; margin-bottom: 0%;",4,"ngIf"],[2,"margin-top","0%","margin-bottom","0%"],["diameter","25","matTooltip","Loading ...",1,"mt-4","ml-auto","mr-auto"],[1,"milestone-description","col-12","p-0","m-0",3,"matTooltip"],["appearance","outline",1,"mr-4",2,"width","35%","height","45px"],["matInput","","placeholder","Search and press Enter","autocomplete","off",2,"padding-bottom","1px !important",3,"ngModel","ngModelChange","keyup.enter"],["label","Claim type",1,"mr-4",2,"width","26%","height","45px",3,"token","optionLabel","API_URL","lazyLoadingCount","selectedValues"],["matTooltip","Duration","type","text","ngxDaterangepickerMd","","placeholder","Duration",1,"mr-3","dp-class",2,"width","26%","height","41px",3,"showCustomRangeLabel","locale","alwaysShowCalendars","showCancel","ranges","linkedCalendars","ngModel","ngModelChange","change"],[1,"col-12","p-0","milestone-list-loading"],["diameter","25","matTooltip","Loading ...",1,"mt-auto","mb-auto","ml-auto","mr-auto"],["infinite-scroll","","cdkScrollable","",3,"infiniteScrollDistance","scrollWindow","scrolled"],[3,"click"],["disabled","",1,"mt-1","ml-2",2,"cursor","pointer",3,"checked"],["disabled","",2,"color","#66615b","font-weight","400","font-size","12px"],["class","col-12 p-0 mb-2 milestone-list-item",3,"click",4,"ngFor","ngForOf"],[1,"col-12","p-0","mb-2","milestone-list-item",3,"click"],[1,"col-12","p-2","row"],["disabled","",1,"mt-1","mr-3",2,"cursor","pointer",3,"checked"],[1,"milestone-category","col-3","p-0","m-0",3,"matTooltip"],[1,"col-12","p-2","row","milestone-list-bottom"],[1,"col-2-5","milestone-item"],[1,"milestone-item-header","col-12","p-0","m-0"],[1,"milestone-item-description","col-12","p-0","m-0",3,"matTooltip"],[1,"col-2-5","milestone-item","ml-2"],[1,"col-12","p-0","row",2,"align-items","center",3,"matTooltip"],["content-type","template",1,"mr-2",3,"id","imgWidth","imgHeight"],[1,"milestone-item-description","m-0","col-9","p-0"],[1,"col-2-5","ml-2"],["diameter","25","matTooltip","Loading ...",1,"mt-3","ml-auto","mr-auto"],[1,"col-12","row","p-0","pl-3","pr-3","mt-3"],["class","align-left col-8 p-0",4,"ngIf"],["class","mr-4 mt-2 align-right","diameter","25","matTooltip","Creating Milestone ...",4,"ngIf"],["mat-flat-button","","class","pl-0 pr-0 mr-2 red-btn align-right",3,"disabled","click",4,"ngIf"],[1,"align-left","col-8","p-0"],[1,"total-milestone-header","col-12","p-0","m-0"],[1,"total-milestone-description","col-12","p-0","m-0"],["diameter","25","matTooltip","Creating Milestone ...",1,"mr-4","mt-2","align-right"],["mat-flat-button","",1,"pl-0","pr-0","mr-2","red-btn","align-right",3,"disabled","click"],["matListIcon","",1,"save-btn-icon"],[1,"col-12","p-0","milestone-list-loading","row",2,"text-align","center"],["src","https://assets.kebs.app/images/no_claims_here.png"],[1,"milestone-header-bigger","col-12","p-0","m-0"]],template:function(e,t){1&e&&(I["\u0275\u0275elementStart"](0,"div",0),I["\u0275\u0275elementStart"](1,"div",1),I["\u0275\u0275elementStart"](2,"div",2),I["\u0275\u0275elementStart"](3,"div",3),I["\u0275\u0275elementStart"](4,"p",4),I["\u0275\u0275text"](5,"Create Milestone"),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](6,"p",5),I["\u0275\u0275text"](7,"Milestone Name"),I["\u0275\u0275elementStart"](8,"span",6),I["\u0275\u0275text"](9,"*"),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](10,"mat-form-field",7),I["\u0275\u0275elementStart"](11,"input",8),I["\u0275\u0275listener"]("ngModelChange",(function(e){return t.milestoneName=e})),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](12,"mat-icon",9),I["\u0275\u0275template"](13,B,3,0,"button",10),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](14,"p",11),I["\u0275\u0275text"](15),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](16,"div"),I["\u0275\u0275elementStart"](17,"mat-form-field",12),I["\u0275\u0275elementStart"](18,"span",13),I["\u0275\u0275elementStart"](19,"mat-icon",14),I["\u0275\u0275text"](20,"search"),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](21,"input",15),I["\u0275\u0275listener"]("ngModelChange",(function(e){return t.costCenterSearchParameter=e}))("keyup.enter",(function(){return t.callSearchApi()}))("keyup",(function(e){return t.onSearchCostCenter(e)})),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](22,"mat-icon",9),I["\u0275\u0275template"](23,R,3,0,"button",10),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275template"](24,K,4,7,"div",16),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](25,"div",17),I["\u0275\u0275template"](26,W,1,0,"mat-spinner",18),I["\u0275\u0275template"](27,G,2,0,"p",19),I["\u0275\u0275template"](28,X,2,2,"p",20),I["\u0275\u0275template"](29,J,2,0,"p",19),I["\u0275\u0275template"](30,H,2,2,"p",20),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](31,"div",21),I["\u0275\u0275elementStart"](32,"div",22),I["\u0275\u0275template"](33,$,7,2,"mat-form-field",23),I["\u0275\u0275template"](34,ee,1,5,"app-expense-multi-select",24),I["\u0275\u0275template"](35,ne,1,8,"input",25),I["\u0275\u0275elementStart"](36,"button",26),I["\u0275\u0275elementStart"](37,"mat-icon",27),I["\u0275\u0275listener"]("click",(function(){return t.closeModal()})),I["\u0275\u0275text"](38,"close"),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275template"](39,ie,2,0,"div",28),I["\u0275\u0275template"](40,le,6,7,"div",29),I["\u0275\u0275template"](41,oe,1,0,"mat-spinner",30),I["\u0275\u0275template"](42,me,4,3,"div",31),I["\u0275\u0275template"](43,pe,6,1,"div",32),I["\u0275\u0275elementEnd"](),I["\u0275\u0275element"](44,"ngx-spinner",33),I["\u0275\u0275elementEnd"]()),2&e&&(I["\u0275\u0275advance"](11),I["\u0275\u0275property"]("ngModel",t.milestoneName),I["\u0275\u0275advance"](2),I["\u0275\u0275property"]("ngIf",t.milestoneName&&""!=t.milestoneName),I["\u0275\u0275advance"](2),I["\u0275\u0275textInterpolate"](t.costCenterFieldLabel?t.costCenterFieldLabel:"Cost center"),I["\u0275\u0275advance"](6),I["\u0275\u0275property"]("ngModel",t.costCenterSearchParameter),I["\u0275\u0275advance"](2),I["\u0275\u0275property"]("ngIf",t.costCenterSearchParameter&&""!=t.costCenterSearchParameter),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",t.costCenterClaimList.length>0),I["\u0275\u0275advance"](2),I["\u0275\u0275property"]("ngIf",t.isProjectAndItemApisLoading&&!t.isProjectAndItemVisible),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",!t.isProjectAndItemApisLoading&&t.isProjectAndItemVisible),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",!t.isProjectAndItemApisLoading&&t.isProjectAndItemVisible),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",!t.isProjectAndItemApisLoading&&t.isProjectAndItemVisible),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",!t.isProjectAndItemApisLoading&&t.isProjectAndItemVisible),I["\u0275\u0275advance"](3),I["\u0275\u0275property"]("ngIf",!t.isProjectAndItemApisLoading&&t.isProjectAndItemVisible),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",!t.isProjectAndItemApisLoading&&t.isProjectAndItemVisible),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",!t.isProjectAndItemApisLoading&&t.isProjectAndItemVisible),I["\u0275\u0275advance"](4),I["\u0275\u0275property"]("ngIf",t.isProjectAndItemApisLoading&&!t.isProjectAndItemVisible),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",!t.isProjectAndItemApisLoading&&t.isProjectAndItemVisible&&t.expenseList.length>0),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",1==t.areExpenseMilestonesLoading),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",!t.isProjectAndItemApisLoading&&t.isProjectAndItemVisible&&t.expenseList.length>0),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",!t.isProjectAndItemApisLoading&&!t.isProjectAndItemVisible))},directives:function(){return[f.c,b.b,x.e,x.v,x.y,g.a,f.i,h.NgIf,f.h,u.a,C.a,M.a,P.a,N.b,h.NgForOf,S.c,D,_.b,v.a,Y.a]},styles:[".create-billable-claims-popup-styles[_ngcontent-%COMP%]{min-height:100%}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .left-half[_ngcontent-%COMP%]{background-color:#f6f6f0}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .create-milestone[_ngcontent-%COMP%]{font-weight:500;font-size:15px}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .milestone-header[_ngcontent-%COMP%]{font-weight:450;font-size:13px;color:#a3a3a3}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .milestone-header-bigger[_ngcontent-%COMP%]{font-weight:450;font-size:14px;color:#a3a3a3}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .milestone-category[_ngcontent-%COMP%], .create-billable-claims-popup-styles[_ngcontent-%COMP%]   .milestone-description[_ngcontent-%COMP%]{font-weight:500;font-size:13px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .milestone-category[_ngcontent-%COMP%]{color:#f15b64;margin-top:.15rem!important}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .milestone-list-item[_ngcontent-%COMP%]{border:1px solid #d6cece;cursor:pointer}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .milestone-item[_ngcontent-%COMP%]{border-right:1px solid #d6cece}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .milestone-item-header[_ngcontent-%COMP%]{font-weight:450;font-size:12px;color:#a3a3a3}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .milestone-item-description[_ngcontent-%COMP%]{font-weight:500;font-size:12px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .milestone-department[_ngcontent-%COMP%]{font-weight:500;font-size:13px;margin-top:.15rem!important;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .milestone-list[_ngcontent-%COMP%]{height:67vh!important;overflow-y:scroll}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .milestone-list-loading[_ngcontent-%COMP%]{height:67vh!important;display:flex;align-items:center;justify-content:center}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .milestone-list-loading-more[_ngcontent-%COMP%]{height:60vh!important;overflow-y:scroll}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .claim-list-with-project[_ngcontent-%COMP%]{height:35vh!important;overflow-y:scroll}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .claim-list[_ngcontent-%COMP%]{height:50vh!important;overflow-y:scroll}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .milestone-list-bottom[_ngcontent-%COMP%]{background-color:#f6f6f0}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{color:#868683;font-size:20px}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .align-right[_ngcontent-%COMP%]{margin-right:0;margin-left:auto}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .save-btn-icon[_ngcontent-%COMP%]{height:24px;font-size:20px;width:30px;margin-left:5px}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .mandatory-red[_ngcontent-%COMP%]{margin-left:1%;color:#cf0001}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .custom-ts-select-option[_ngcontent-%COMP%]{font-size:13px;text-align:center;color:#1a1a1a}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .col-2-5[_ngcontent-%COMP%]{flex:0 0 19%;max-width:19%}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .red-btn[_ngcontent-%COMP%]{width:100%;height:45px;max-width:100px;background:#f15b64;font-size:13px;color:#fff;font-weight:500}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .red-btn[_ngcontent-%COMP%]:disabled{background:grey}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .total-milestone-header[_ngcontent-%COMP%]{font-weight:450;font-size:14px;color:#a3a3a3}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .total-milestone-description[_ngcontent-%COMP%]{font-weight:450;font-size:14px}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .dp-class[_ngcontent-%COMP%]{background:0 0;border:1px solid #d3d3d3;border-radius:4px;font-size:12px;font-weight:500;height:40px;margin-top:3px;cursor:pointer;text-align:center}.create-billable-claims-popup-styles[_ngcontent-%COMP%]     .md-drppicker{top:17px!important;left:270px!important}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .app-input-search-class[_ngcontent-%COMP%]     .mat-form-field-flex{padding-bottom:3px!important}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .app-input-search-class[_ngcontent-%COMP%]     input.mat-input-element{font-size:14px!important;padding-top:4px!important}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .app-input-search-class[_ngcontent-%COMP%]     .mat-form-field-infix{font-size:14px!important}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .cost-centre-item[_ngcontent-%COMP%]{cursor:pointer;border-radius:6px}.create-billable-claims-popup-styles[_ngcontent-%COMP%]   .cost-centre-item[_ngcontent-%COMP%]:hover{cursor:pointer;border-radius:6px;background-color:#fff}.create-billable-claims-popup-styles[_ngcontent-%COMP%]     .mat-checkbox-checked.mat-accent .mat-checkbox-background:disabled, .create-billable-claims-popup-styles[_ngcontent-%COMP%]     .mat-checkbox-disabled.mat-checkbox-checked .mat-checkbox-background, .create-billable-claims-popup-styles[_ngcontent-%COMP%]     .mat-checkbox .mat-ripple-element{background:#f15b64!important}.create-billable-claims-popup-styles[_ngcontent-%COMP%]     .md-drppicker{margin-top:15px!important;font-family:Plus Jakarta Sans!important}.create-billable-claims-popup-styles[_ngcontent-%COMP%]     .md-drppicker   .btn{background-color:#79ba44!important;color:#fff!important;border-radius:4px;padding:0 13px}.create-billable-claims-popup-styles[_ngcontent-%COMP%]     .md-drppicker   .btn-default{background-color:#fff!important;border:1px solid #dadce2!important;color:#45546e!important;border-radius:4px;padding:0 12px}.create-billable-claims-popup-styles[_ngcontent-%COMP%]     .md-drppicker button.active{background-color:#79ba44!important;color:#fff!important}.create-billable-claims-popup-styles[_ngcontent-%COMP%]     .md-drppicker button{color:#45546e!important}.create-billable-claims-popup-styles[_ngcontent-%COMP%]     .md-drppicker table{border-collapse:initial}.create-billable-claims-popup-styles[_ngcontent-%COMP%]     .md-drppicker td.in-range{background:#eef9e8!important;border-radius:4px;color:#79ba44!important}.create-billable-claims-popup-styles[_ngcontent-%COMP%]     .md-drppicker td.today{background-color:#79ba44!important;color:#fff!important;border-radius:4px}.create-billable-claims-popup-styles[_ngcontent-%COMP%]     .md-drppicker td.start-date{background:#79ba44!important;border-radius:4px;color:#fff!important}.create-billable-claims-popup-styles[_ngcontent-%COMP%]     .md-drppicker td.active:hover, .create-billable-claims-popup-styles[_ngcontent-%COMP%]     .md-drppicker td.end-date{background-color:#79ba44!important;color:#fff!important;border-radius:4px}"]}),e})()}}]);