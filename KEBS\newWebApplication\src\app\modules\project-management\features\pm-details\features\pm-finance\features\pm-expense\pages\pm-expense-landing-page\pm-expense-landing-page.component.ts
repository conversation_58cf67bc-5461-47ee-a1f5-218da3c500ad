import { Component, <PERSON><PERSON><PERSON><PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { ActivatedRoute } from '@angular/router';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { PmExpenseServiceService } from '../../services/pm-expense-service.service';
import { PmExpenseIntegrationService } from '../../services/pm-expense-integration.service';
import { MilestoneCreationDialogComponent } from '../../components/milestone-creation-dialog/milestone-creation-dialog.component';

// Expense data interface matching the SQL query structure
export interface ExpenseData {
  id: number;
  legal_entity_id: number;
  total_amount: number;
  cost_centre: string;
  cost_centre_description: string;
  amount_claimed: number;
  billable_milestone_id: number;
  description: string;
  created_on: Date;
  requested_by: string;
  expense_status: number;
  isSelected?: boolean; // For checkbox selection
  status_display?: string; // For status display (Open, Billing, SOW)
}

// Milestone creation interface
export interface MilestoneCreationData {
  milestone_name: string;
  quote_id: number;
  selected_expenses: ExpenseData[];
  total_amount: number;
}

// Quote selection interface
export interface QuoteOption {
  id: number;
  name: string;
  value: number;
}

@Component({
  selector: 'app-pm-expense-landing-page',
  templateUrl: './pm-expense-landing-page.component.html',
  styleUrls: ['./pm-expense-landing-page.component.scss']
})
export class PmExpenseLandingPageComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Component properties
  expenseData: ExpenseData[] = [];
  selectedExpenses: ExpenseData[] = [];
  isLoading = false;
  projectId: number;
  itemId: number;

  constructor(
    private expenseService: PmExpenseServiceService,
    private integrationService: PmExpenseIntegrationService,
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
    private route: ActivatedRoute
  ) {
    // Get project and item IDs from route parameters
    this.projectId = +this.route.snapshot.params['projectId'] || 0;
    this.itemId = +this.route.snapshot.params['itemId'] || 0;
  }

  ngOnInit(): void {
    this.loadExpenseData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Load expense data from the service
   */
  loadExpenseData(): void {
    this.isLoading = true;
    this.expenseService.getExpenseData(this.projectId, this.itemId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (data) => {
          this.expenseData = data.map(expense => ({
            ...expense,
            status_display: this.expenseService.getStatusDisplay(expense.expense_status),
            isSelected: false
          }));
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error loading expense data:', error);
          this.snackBar.open('Error loading expense data', 'Close', { duration: 3000 });
          this.isLoading = false;
        }
      });
  }

  /**
   * Handle selection changes in the data grid
   */
  onSelectionChanged(event: any): void {
    this.selectedExpenses = event.selectedRowsData || [];

    // Update isSelected property for all expenses
    this.expenseData.forEach(expense => {
      expense.isSelected = this.selectedExpenses.some(selected => selected.id === expense.id);
    });
  }

  /**
   * Calculate total amount of selected expenses
   */
  getTotalSelectedAmount(): number {
    return this.selectedExpenses.reduce((total, expense) => total + expense.total_amount, 0);
  }

  /**
   * Open milestone creation dialog
   */
  openMilestoneCreationDialog(): void {
    if (this.selectedExpenses.length === 0) {
      this.snackBar.open('Please select at least one expense', 'Close', { duration: 3000 });
      return;
    }

    // Only allow selection of 'Open' status expenses
    const openExpenses = this.selectedExpenses.filter(expense => expense.expense_status === 1);

    if (openExpenses.length === 0) {
      this.snackBar.open('Only expenses with "Open" status can be selected for milestone creation', 'Close', { duration: 3000 });
      return;
    }

    if (openExpenses.length !== this.selectedExpenses.length) {
      this.snackBar.open('Some selected expenses are not in "Open" status and will be excluded', 'Close', { duration: 3000 });
    }

    const dialogRef = this.dialog.open(MilestoneCreationDialogComponent, {
      width: '600px',
      disableClose: true,
      data: {
        selectedExpenses: openExpenses,
        totalAmount: openExpenses.reduce((total, expense) => total + expense.total_amount, 0),
        projectId: this.projectId,
        itemId: this.itemId
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result && result.success) {
        this.handleMilestoneCreationSuccess(openExpenses);
      }
    });
  }

  /**
   * Handle successful milestone creation
   */
  private handleMilestoneCreationSuccess(processedExpenses: ExpenseData[]): void {
    // Update expense status from 1 to 2 for processed expenses
    const expenseIds = processedExpenses.map(expense => expense.id);

    this.expenseService.updateExpenseStatus(expenseIds, 2)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.snackBar.open('Milestone created and expense status updated successfully', 'Close', { duration: 5000 });

          // Refresh related tabs
          this.integrationService.refreshRelatedTabs();

          // Reload data to reflect changes
          this.loadExpenseData();
          this.selectedExpenses = []; // Clear selection
        },
        error: (error) => {
          console.error('Error updating expense status:', error);
          this.snackBar.open('Milestone created but failed to update expense status', 'Close', { duration: 5000 });
        }
      });
  }
}
