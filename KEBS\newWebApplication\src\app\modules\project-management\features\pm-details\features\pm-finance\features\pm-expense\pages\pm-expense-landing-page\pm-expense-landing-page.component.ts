import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';
import { ActivatedRoute } from '@angular/router';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { PmExpenseServiceService } from '../../services/pm-expense-service.service';
import Swal from 'sweetalert2';

// Expense data interface matching the SQL query structure
export interface ExpenseData {
  id: number;
  legal_entity_id: number;
  total_amount: number;
  cost_centre: string;
  cost_centre_description: string;
  amount_claimed: number;
  billable_milestone_id: number;
  description: string;
  created_on: Date;
  requested_by: string;
  expense_status: number;
  isSelected?: boolean; // For checkbox selection
  status_display?: string; // For status display (Open, Billing, SOW)
}

// Milestone creation interface for billableMilestoneCreation API
export interface BillableMilestoneCreationData {
  milestone_name: string;
  quote_id: number;
  cost_center: string;
  selected_expense_ids: number[];
  total_amount: any; // Currency object format
  project_id: number;
  item_id: number;
}

// Quote selection interface
export interface QuoteOption {
  id: number;
  quote_name: string;
  project_id: number;
  item_id: number;
}

@Component({
  selector: 'app-pm-expense-landing-page',
  templateUrl: './pm-expense-landing-page.component.html',
  styleUrls: ['./pm-expense-landing-page.component.scss']
})
export class PmExpenseLandingPageComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Component properties
  expenseData: ExpenseData[] = [];
  selectedExpenses: ExpenseData[] = [];
  isLoading = false;
  projectId: number;
  itemId: number;
  costCenter: string;
  availableQuotes: QuoteOption[] = [];

  constructor(
    private expenseService: PmExpenseServiceService,
    private snackBar: MatSnackBar,
    private route: ActivatedRoute
  ) {
    // Get project and item IDs from route parameters
    this.projectId = +this.route.snapshot.params['projectId'] || 0;
    this.itemId = +this.route.snapshot.params['itemId'] || 0;
    this.costCenter = this.route.snapshot.queryParams['costCenter'] || '';
  }

  ngOnInit(): void {
    this.loadExpenseData();
    this.loadAvailableQuotes();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Load expense data from the service using cost center
   */
  loadExpenseData(): void {
    if (!this.costCenter) {
      this.snackBar.open('Cost center is required', 'Close', { duration: 3000 });
      return;
    }

    this.isLoading = true;
    this.expenseService.getExpenseListBasedOnCostCenter(this.costCenter)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: any) => {
          if (response.messType === 'S' && response.data) {
            this.expenseData = response.data.map((expense: any) => ({
              ...expense,
              status_display: this.expenseService.getStatusDisplay(expense.expense_status),
              isSelected: false
            }));
          } else {
            this.expenseData = [];
          }
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error loading expense data:', error);
          this.snackBar.open('Error loading expense data', 'Close', { duration: 3000 });
          this.isLoading = false;
        }
      });
  }

  /**
   * Load available quotes for the project
   */
  loadAvailableQuotes(): void {
    if (this.projectId && this.itemId) {
      this.expenseService.getAvailableQuotes(this.projectId, this.itemId)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (quotes) => {
            this.availableQuotes = quotes;
          },
          error: (error) => {
            console.error('Error loading quotes:', error);
          }
        });
    }
  }

  /**
   * Handle selection changes in the data grid
   */
  onSelectionChanged(event: any): void {
    this.selectedExpenses = event.selectedRowsData || [];

    // Update isSelected property for all expenses
    this.expenseData.forEach(expense => {
      expense.isSelected = this.selectedExpenses.some(selected => selected.id === expense.id);
    });
  }

  /**
   * Calculate total amount of selected expenses
   */
  getTotalSelectedAmount(): number {
    return this.selectedExpenses.reduce((total, expense) => total + expense.total_amount, 0);
  }

  /**
   * Open milestone creation dialog using SweetAlert
   */
  async openMilestoneCreationDialog(): Promise<void> {
    if (this.selectedExpenses.length === 0) {
      this.snackBar.open('Please select at least one expense', 'Close', { duration: 3000 });
      return;
    }

    // Only allow selection of 'Open' status expenses
    const openExpenses = this.selectedExpenses.filter(expense => expense.expense_status === 1);

    if (openExpenses.length === 0) {
      this.snackBar.open('Only expenses with "Open" status can be selected for milestone creation', 'Close', { duration: 3000 });
      return;
    }

    if (openExpenses.length !== this.selectedExpenses.length) {
      this.snackBar.open('Some selected expenses are not in "Open" status and will be excluded', 'Close', { duration: 3000 });
    }

    // Create quote options HTML
    const quoteOptionsHtml = this.availableQuotes.map(quote =>
      `<option value="${quote.id}">${quote.quote_name}</option>`
    ).join('');

    const { value: formValues } = await Swal.fire({
      title: 'Create Milestone',
      html: `
        <div style="text-align: left; margin-bottom: 20px;">
          <p><strong>Selected Expenses:</strong> ${openExpenses.length} items</p>
          <p><strong>Total Amount:</strong> ${this.getTotalSelectedAmount().toFixed(2)} USD</p>
        </div>
        <input id="milestone-name" class="swal2-input" placeholder="Enter milestone name" required>
        <select id="quote-select" class="swal2-select" required>
          <option value="">Select Quote</option>
          ${quoteOptionsHtml}
        </select>
      `,
      focusConfirm: false,
      showCancelButton: true,
      confirmButtonText: 'Create Milestone',
      preConfirm: () => {
        const milestoneName = (document.getElementById('milestone-name') as HTMLInputElement).value;
        const quoteId = (document.getElementById('quote-select') as HTMLSelectElement).value;

        if (!milestoneName) {
          Swal.showValidationMessage('Please enter milestone name');
          return false;
        }
        if (!quoteId) {
          Swal.showValidationMessage('Please select a quote');
          return false;
        }

        return { milestoneName, quoteId: parseInt(quoteId) };
      }
    });

    if (formValues) {
      this.createMilestone(formValues.milestoneName, formValues.quoteId, openExpenses);
    }
  }

  /**
   * Create milestone using the billableMilestoneCreation API
   */
  private createMilestone(milestoneName: string, quoteId: number, selectedExpenses: ExpenseData[]): void {
    const totalAmount = selectedExpenses.reduce((total, expense) => total + expense.total_amount, 0);

    const milestoneData: BillableMilestoneCreationData = {
      milestone_name: milestoneName,
      quote_id: quoteId,
      cost_center: this.costCenter,
      selected_expense_ids: selectedExpenses.map(expense => expense.id),
      total_amount: [{ currency_code: 'USD', value: totalAmount }], // Format as expected by API
      project_id: this.projectId,
      item_id: this.itemId
    };

    this.expenseService.createBillableMilestone(milestoneData)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: any) => {
          if (response.messType === 'S') {
            Swal.fire({
              title: 'Milestone Created Successfully!',
              icon: 'success',
              showConfirmButton: true
            });

            // Update expense status and reload data
            this.updateExpenseStatusAfterMilestone(selectedExpenses);
          } else {
            Swal.fire({
              title: 'Milestone Creation Failed!',
              text: response.messText || 'Unknown error occurred',
              icon: 'error',
              showConfirmButton: true
            });
          }
        },
        error: (error) => {
          console.error('Error creating milestone:', error);
          Swal.fire({
            title: 'Milestone Creation Failed!',
            text: 'An error occurred while creating the milestone',
            icon: 'error',
            showConfirmButton: true
          });
        }
      });
  }

  /**
   * Update expense status after milestone creation
   */
  private updateExpenseStatusAfterMilestone(processedExpenses: ExpenseData[]): void {
    const expenseIds = processedExpenses.map(expense => expense.id);

    this.expenseService.updateExpenseStatus(expenseIds, 'Open', 'Billing')
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.snackBar.open('Expense status updated successfully', 'Close', { duration: 3000 });
          this.loadExpenseData(); // Reload data to reflect changes
          this.selectedExpenses = []; // Clear selection
        },
        error: (error) => {
          console.error('Error updating expense status:', error);
          this.snackBar.open('Failed to update expense status', 'Close', { duration: 3000 });
        }
      });
  }

  /**
   * Handle status click for Open status expenses
   */
  async onStatusClick(expense: ExpenseData): Promise<void> {
    if (expense.expense_status === 1) { // Only for Open status
      const { value: newStatus } = await Swal.fire({
        title: 'Change Status',
        text: `Change status for expense ${expense.id}`,
        input: 'select',
        inputOptions: {
          'Billing': 'Billing',
          'SOW': 'SOW'
        },
        inputPlaceholder: 'Select new status',
        showCancelButton: true,
        confirmButtonText: 'Update Status'
      });

      if (newStatus) {
        this.updateSingleExpenseStatus(expense, newStatus as string);
      }
    }
  }

  /**
   * Update status for a single expense
   */
  private updateSingleExpenseStatus(expense: ExpenseData, newStatus: string): void {
    this.expenseService.updateExpenseStatus([expense.id], 'Open', newStatus)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.snackBar.open(`Expense status updated to ${newStatus}`, 'Close', { duration: 3000 });
          this.loadExpenseData(); // Reload data to reflect changes
        },
        error: (error) => {
          console.error('Error updating expense status:', error);
          this.snackBar.open('Failed to update expense status', 'Close', { duration: 3000 });
        }
      });
  }
}
