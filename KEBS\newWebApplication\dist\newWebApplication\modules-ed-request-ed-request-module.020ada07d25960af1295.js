(window.webpackJsonp=window.webpackJsonp||[]).push([[815],{"TmG/":function(e,t,n){"use strict";n.d(t,"a",(function(){return x}));var i=n("fXoL"),o=n("3Pt+"),r=n("jtHE"),a=n("XNiG"),l=n("NJ67"),s=n("1G5W"),c=n("kmnG"),d=n("ofXK"),p=n("d3UM"),m=n("FKr1"),g=n("WJ5W"),f=n("Qu3c");function h(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-label"),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](e.placeholder)}}function u(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-option",7),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275property"]("value",null),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](e.disableNone?"Select One":"None")}}function v(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"mat-option",8),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const n=t.$implicit;return i["\u0275\u0275nextContext"]().emitChanges(n)})),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;i["\u0275\u0275property"]("value",e.id||e._id)("matTooltip",e.name),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let x=(()=>{class e extends l.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new o.j,this.fieldFilterCtrl=new o.j,this.list=[],this.required=!1,this.disableNone=!1,this.valueChange=new i.EventEmitter,this.disabled=!1,this.hasNoneOption=!0,this.filteredList=new r.a,this.change=new i.EventEmitter,this.hideMatLabel=!1,this._onDestroy=new a.b,this.emitChanges=e=>{this.change.emit(e)}}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(s.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(s.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](i.Renderer2))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search"]],inputs:{list:"list",placeholder:"placeholder",required:"required",disableNone:"disableNone",disabled:"disabled",hasNoneOption:"hasNoneOption",hideMatLabel:"hideMatLabel"},outputs:{valueChange:"valueChange",change:"change"},features:[i["\u0275\u0275ProvidersFeature"]([{provide:o.t,useExisting:Object(i.forwardRef)(()=>e),multi:!0}]),i["\u0275\u0275InheritDefinitionFeature"],i["\u0275\u0275NgOnChangesFeature"]],decls:9,vars:11,consts:[["appearance","outline"],[4,"ngIf"],[3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value",4,"ngIf"],[3,"value","matTooltip","click",4,"ngFor","ngForOf"],[3,"value"],[3,"value","matTooltip","click"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"mat-form-field",0),i["\u0275\u0275template"](1,h,2,1,"mat-label",1),i["\u0275\u0275elementStart"](2,"mat-select",2,3),i["\u0275\u0275elementStart"](4,"mat-option"),i["\u0275\u0275element"](5,"ngx-mat-select-search",4),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](6,u,2,2,"mat-option",5),i["\u0275\u0275template"](7,v,2,3,"mat-option",6),i["\u0275\u0275pipe"](8,"async"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",!t.hideMatLabel),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.hasNoneOption),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngForOf",i["\u0275\u0275pipeBind1"](8,9,t.filteredList)))},directives:[c.c,d.NgIf,p.c,o.v,o.k,o.F,m.p,g.a,d.NgForOf,c.g,f.a],pipes:[d.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%}"]}),e})()},dDjw:function(e,t,n){"use strict";n.r(t),n.d(t,"EdRequestModule",(function(){return ni}));var i=n("ofXK"),o=n("tyNb"),r=n("mrSG"),a=n("BUry"),l=n("ds6q"),s=n("xG9w"),c=n("wd/R"),d=n.n(c),p=n("fXoL"),m=n("jAlA"),g=n("XXEo"),f=n("bxdq"),h=n("1A3m"),u=n("Wp6s"),v=n("NFeN"),x=n("Qu3c");function _(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"span",12),p["\u0275\u0275text"](1,"(In Progress)"),p["\u0275\u0275elementEnd"]())}const C=function(){return["/ed-home-page"]},y=function(e,t){return{display:e,"pointer-events":t}};function b(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",7),p["\u0275\u0275listener"]("click",(function(){p["\u0275\u0275restoreView"](e);const t=p["\u0275\u0275nextContext"](),n=t.$implicit,i=t.index,o=p["\u0275\u0275nextContext"]();return o.handleModuleSelection(n,i,o.selectModule)})),p["\u0275\u0275elementStart"](1,"div",8),p["\u0275\u0275elementStart"](2,"mat-icon",9),p["\u0275\u0275text"](3),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](4,"span",10),p["\u0275\u0275text"](5),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](6,_,2,0,"span",11),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"]().$implicit,t=p["\u0275\u0275nextContext"]();p["\u0275\u0275property"]("ngClass",e.isSelected?"module-tile-selected row":"module-tile row")("routerLink",p["\u0275\u0275pureFunction0"](8,C))("routerLinkActive","active")("ngStyle",p["\u0275\u0275pureFunction2"](9,y,e.isVisible?"revert":"none",t.disableNavigation?"none":"visible")),p["\u0275\u0275advance"](3),p["\u0275\u0275textInterpolate"](e.matIcon),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("matTooltip",e.name),p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate"](e.name),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",t.statusOfResignation)}}function M(e,t){if(1&e&&(p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275template"](1,b,7,12,"div",6),p["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",e.isVisible)}}let S=(()=>{class e{constructor(e,t,n,i,o,r){this._router=e,this.route=t,this._edService=n,this._loginService=i,this.edreqService=o,this._toaster=r,this.prevValue=0,this.currentDate=new Date,this.featureModuleList=[{id:1,name:"Resignation",path:"ed-resignation",isSelected:!1,matIcon:"meeting_room",isVisible:!0,objectId:611}],this._onDestroy=new l.Subject,this.applicationRoleAccessList=[],this.subs=new a.SubSink,this.statusOfResignation=!1}ngOnInit(){return Object(r.c)(this,void 0,void 0,(function*(){console.log("user Aid in Request landing page",this._edService.ed_aid),this.aid=this._loginService.getProfile().profile.aid,this.applicationRoleAccessList=this._edService.getAllRoleAccessForExit(),this.routeCondition=yield this.fetchSubmittedExitRequestWithdraw(),yield this.getEmployeeNameById(this._edService.ed_aid);for(let e of this.featureModuleList)e.isVisible=s.where(this.applicationRoleAccessList,{object_id:e.objectId}).length>0,this.prevValue+=1==e.isVisible?1:0,console.log(e,e.isVisible,"isVisible Console")}))}handleModuleSelection(e,t,n){this.aid==this._edService.ed_aid&&(this.routeCondition.length>0?(console.log("routeCondition",this.routeCondition),this._router.navigateByUrl("/main/employee-central/employeeCentralDetail/"+this._edService.ed_aid+"/request/resigantion-withdrawn"),this.featureModuleList[e].isSelected=!0,this.statusOfResignation=!0,this._toaster.showInfo("Info","You already have an existing request",2e3)):(this._router.navigate(["ed-home-page"],{relativeTo:this.route}),this.featureModuleList[e].isSelected=!0))}fetchSubmittedExitRequestWithdraw(){let e={params:{user_aid:this._edService.ed_aid,current_date:d()(this.currentDate).format("YYYY-MM-DD")}};return new Promise(t=>{this.subs.sink=this.edreqService.fetchSubmittedExitRequestWithdraw(e).subscribe(e=>{e.err||t(e.data)})})}getEmployeeNameById(e){return new Promise((t,n)=>{this.subs.sink=this._edService.getEmployeeNameById(e).subscribe(e=>{!e.err&&e.data&&e.data.length>0?(this.employeeName=e.data[0].name,t(!0)):t(!1)},e=>{n(e)})})}ngOnDestroy(){this.subs.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(p["\u0275\u0275directiveInject"](o.g),p["\u0275\u0275directiveInject"](o.a),p["\u0275\u0275directiveInject"](m.a),p["\u0275\u0275directiveInject"](g.a),p["\u0275\u0275directiveInject"](f.a),p["\u0275\u0275directiveInject"](h.a))},e.\u0275cmp=p["\u0275\u0275defineComponent"]({type:e,selectors:[["app-ed-request-landing-page"]],decls:7,vars:1,consts:[[1,"container-fluid","pl-0","ed-request-landing-page"],[1,"row"],[1,"col-9","p-0"],[1,"col-3","p-0","pt-3"],[2,"width","100%","height","77vh","overflow-y","scroll"],[4,"ngFor","ngForOf"],["class","module-tile row",3,"ngClass","routerLink","routerLinkActive","ngStyle","click",4,"ngIf"],[1,"module-tile","row",3,"ngClass","routerLink","routerLinkActive","ngStyle","click"],[1,"d-flex","flex-row","py-2","px-4",2,"width","100%"],[1,"display-icon"],[1,"over-flow-ctrl",3,"matTooltip"],["class","status-span",4,"ngIf"],[1,"status-span"]],template:function(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"div",0),p["\u0275\u0275elementStart"](1,"div",1),p["\u0275\u0275elementStart"](2,"div",2),p["\u0275\u0275element"](3,"router-outlet"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](4,"div",3),p["\u0275\u0275elementStart"](5,"mat-card",4),p["\u0275\u0275template"](6,M,2,1,"ng-container",5),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e&&(p["\u0275\u0275advance"](6),p["\u0275\u0275property"]("ngForOf",t.featureModuleList))},directives:[o.l,u.a,i.NgForOf,i.NgIf,i.NgClass,o.h,o.i,i.NgStyle,v.a,x.a],styles:[".ed-request-landing-page[_ngcontent-%COMP%]   .module-tile[_ngcontent-%COMP%]{color:#000;font-size:13px}.ed-request-landing-page[_ngcontent-%COMP%]   .module-tile[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#7d838b;font-size:20px;margin-right:8px!important}.ed-request-landing-page[_ngcontent-%COMP%]   .module-tile[_ngcontent-%COMP%]:hover{color:#111434;font-size:15px;font-weight:500;cursor:pointer;transition:.2s;background:#f6f6f7}.ed-request-landing-page[_ngcontent-%COMP%]   .module-tile[_ngcontent-%COMP%]:hover   mat-icon[_ngcontent-%COMP%]{color:#111434;font-size:20px;margin-right:8px!important}.ed-request-landing-page[_ngcontent-%COMP%]   .module-tile-selected[_ngcontent-%COMP%]{color:#111434;font-size:15px;font-weight:500;cursor:pointer;background:#f6f6f7;border-left:3px solid #111434}.ed-request-landing-page[_ngcontent-%COMP%]   .module-tile-selected[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#111434;font-size:20px;margin-right:8px!important}.ed-request-landing-page[_ngcontent-%COMP%]   .module-tile[_ngcontent-%COMP%]:focus{color:#111434;font-size:15px;font-weight:500;cursor:pointer;background:#f6f6f7;border-left:3px solid #111434}.ed-request-landing-page[_ngcontent-%COMP%]   .module-tile[_ngcontent-%COMP%]:focus   mat-icon[_ngcontent-%COMP%]{color:#111434;font-size:20px;margin-right:8px!important}.ed-request-landing-page[_ngcontent-%COMP%]   .module-tile.active[_ngcontent-%COMP%]{color:#111434;font-size:15px;font-weight:500;cursor:pointer;background:#f6f6f7;border-left:3px solid #111434}.ed-request-landing-page[_ngcontent-%COMP%]   .module-tile.active[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#111434;font-size:20px;margin-right:8px!important}.ed-request-landing-page[_ngcontent-%COMP%]   .over-flow-ctrl[_ngcontent-%COMP%], .ed-request-landing-page[_ngcontent-%COMP%]   .over-flow-ctrl-name[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;text-transform:capitalize;max-width:91%}.ed-request-landing-page[_ngcontent-%COMP%]   .display-icon[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;padding-right:8px!important}.ed-request-landing-page[_ngcontent-%COMP%]   .status-span[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;color:#ffb906;font-family:Roboto;padding:0 1rem;font-size:12px;font-style:normal;font-weight:400;line-height:normal;text-transform:capitalize}.ed-request-landing-page[_ngcontent-%COMP%]     .mat-ink-bar{visibility:hidden!important}"]}),e})(),P=(()=>{class e{constructor(e,t,n){this._router=e,this.route=t,this._edService=n,this.info=["No Request Found!"],this.desc=["But that's okay, you can raise a request anytime by clicking the buttons provided on the right side."]}ngOnInit(){console.log("Aid in No request page",this._edService.ed_aid),this._router.navigateByUrl("/main/employee-central/employeeCentralDetail/"+this._edService.ed_aid+"/request/resignation-withdrawn")}}return e.\u0275fac=function(t){return new(t||e)(p["\u0275\u0275directiveInject"](o.g),p["\u0275\u0275directiveInject"](o.a),p["\u0275\u0275directiveInject"](m.a))},e.\u0275cmp=p["\u0275\u0275defineComponent"]({type:e,selectors:[["app-ed-no-request"]],decls:5,vars:2,consts:[[1,"no-req"]],template:function(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"div",0),p["\u0275\u0275elementStart"](1,"h3"),p["\u0275\u0275text"](2),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](3,"p"),p["\u0275\u0275text"](4),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e&&(p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate"](t.info),p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate"](t.desc))},styles:[".no-req[_ngcontent-%COMP%]{font-family:Roboto;justify-content:center;text-align:center;margin-top:20%;font-style:normal;text-transform:capitalize}.no-req[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:18px;font-weight:500;color:#000}.no-req[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-weight:400;font-size:13px;color:#525252}"]}),e})();var O=n("iCUz");function w(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"mat-icon",7),p["\u0275\u0275text"](1,"check_box "),p["\u0275\u0275elementEnd"]())}const E=function(e){return{navchange:e}},I=function(e,t){return{activelabel:e,navchange:t}};function k(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",4),p["\u0275\u0275elementStart"](1,"span",5),p["\u0275\u0275text"](2),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](3,w,2,0,"mat-icon",6),p["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=p["\u0275\u0275nextContext"]();p["\u0275\u0275property"]("routerLink",e.path)("ngClass",p["\u0275\u0275pureFunction1"](5,E,e.isNavChecked)),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngClass",p["\u0275\u0275pureFunction2"](7,I,n._edReqHomeService.activedRoutePath==e.path,e.isNavChecked)),p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate"](e.label),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",e.isNavChecked)}}let D=(()=>{class e{constructor(e,t,n){this._edReqHomeService=e,this.router=t,this.route=n}ngOnInit(){}}return e.\u0275fac=function(t){return new(t||e)(p["\u0275\u0275directiveInject"](O.a),p["\u0275\u0275directiveInject"](o.g),p["\u0275\u0275directiveInject"](o.a))},e.\u0275cmp=p["\u0275\u0275defineComponent"]({type:e,selectors:[["app-ed-request-home-page"]],decls:6,vars:1,consts:[[1,"ed-request-home-page"],[1,"row"],[1,"nav-bar","pl-1","pr-1",2,"display","flex","justify-content","center","column-gap","7%","padding","4px 0px 0px","pointer-events","none"],["class","component-main","routerLinkActive","active",3,"routerLink","ngClass",4,"ngFor","ngForOf"],["routerLinkActive","active",1,"component-main",3,"routerLink","ngClass"],[1,"label-name",3,"ngClass"],["class","check-box","style","font-size: 18px; height: auto",4,"ngIf"],[1,"check-box",2,"font-size","18px","height","auto"]],template:function(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"mat-card",0),p["\u0275\u0275elementStart"](1,"div",1),p["\u0275\u0275elementStart"](2,"nav",2),p["\u0275\u0275template"](3,k,4,10,"div",3),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275element"](4,"hr"),p["\u0275\u0275element"](5,"router-outlet"),p["\u0275\u0275elementEnd"]()),2&e&&(p["\u0275\u0275advance"](3),p["\u0275\u0275property"]("ngForOf",t._edReqHomeService.requestTabs))},directives:[u.a,i.NgForOf,o.l,o.i,o.h,i.NgClass,i.NgIf,v.a],styles:[".ed-request-home-page[_ngcontent-%COMP%]{background-color:#fff;height:77vh;margin-left:2%;width:96%;margin-top:1rem}.ed-request-home-page[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]{z-index:1;position:relative;display:flex!important;justify-content:center!important;margin-top:-2px}.ed-request-home-page[_ngcontent-%COMP%]   nav[_ngcontent-%COMP%]{width:100%;z-index:1}.ed-request-home-page[_ngcontent-%COMP%]   .component-main[_ngcontent-%COMP%]{display:flex!important;justify-content:center!important;align-items:center!important;color:#5e5d5d}.ed-request-home-page[_ngcontent-%COMP%]   .component-main[_ngcontent-%COMP%]   .label-name[_ngcontent-%COMP%]{color:var(--grey,#5e5d5d);font-size:13px;font-family:Roboto;font-weight:500;text-transform:capitalize}.ed-request-home-page[_ngcontent-%COMP%]   .component-main[_ngcontent-%COMP%]   .activelabel[_ngcontent-%COMP%]{color:#cf0001!important}.ed-request-home-page[_ngcontent-%COMP%]   .check-box[_ngcontent-%COMP%]   .mat-tab-label-active[_ngcontent-%COMP%]{display:none}.ed-request-home-page[_ngcontent-%COMP%]   .navchange[_ngcontent-%COMP%]{color:#009432!important}.ed-request-home-page[_ngcontent-%COMP%]   .label-name.active[_ngcontent-%COMP%], .ed-request-home-page[_ngcontent-%COMP%]   .label-name.active-route[_ngcontent-%COMP%], .ed-request-home-page[_ngcontent-%COMP%]   .label-name.navchange[_ngcontent-%COMP%]{color:#cf0001}.ed-request-home-page[_ngcontent-%COMP%]   hr[_ngcontent-%COMP%]{margin-top:.7rem;margin-bottom:.7rem}"]}),e})();var j=n("33Jv"),F=n("Xa2L"),q=n("me71");function R(e,t){1&e&&(p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275elementStart"](1,"div",2),p["\u0275\u0275element"](2,"mat-spinner",3),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementContainerEnd"]())}function z(e,t){1&e&&p["\u0275\u0275element"](0,"div",20)}function Y(e,t){if(1&e&&p["\u0275\u0275element"](0,"app-user-image",21),2&e){const e=p["\u0275\u0275nextContext"](4);p["\u0275\u0275property"]("id",e.profileOid)}}function N(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",12),p["\u0275\u0275elementStart"](1,"div",13),p["\u0275\u0275elementStart"](2,"div",14),p["\u0275\u0275elementStart"](3,"a"),p["\u0275\u0275text"](4),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](5,z,1,0,"div",15),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](6,"div",16),p["\u0275\u0275elementStart"](7,"div",17),p["\u0275\u0275template"](8,Y,1,1,"app-user-image",18),p["\u0275\u0275elementStart"](9,"h3",19),p["\u0275\u0275text"](10),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](11,"p",19),p["\u0275\u0275text"](12),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=t.index,i=p["\u0275\u0275nextContext"](3);p["\u0275\u0275advance"](4),p["\u0275\u0275textInterpolate"](n+1),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",n+1!=(null==i.data1?null:i.data1.data.length)),p["\u0275\u0275advance"](3),p["\u0275\u0275property"]("ngIf",null!=e.replace_text),p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"](" ",e.process_display_name," "),p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate"](e.process_desc)}}function A(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div"),p["\u0275\u0275elementStart"](1,"div",5),p["\u0275\u0275elementStart"](2,"div",6),p["\u0275\u0275elementStart"](3,"header"),p["\u0275\u0275text"](4,"Exit Process"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275element"](5,"br"),p["\u0275\u0275elementStart"](6,"div",7),p["\u0275\u0275template"](7,N,13,5,"div",8),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](8,"div",9),p["\u0275\u0275elementStart"](9,"button",10),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](2).backtoreq()})),p["\u0275\u0275text"](10," Cancel "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](11,"button",11),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](2).directToTerms()})),p["\u0275\u0275text"](12," View Terms & Conditions "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"](2);p["\u0275\u0275advance"](7),p["\u0275\u0275property"]("ngForOf",null==e.data1?null:e.data1.data)}}function T(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"div",22),p["\u0275\u0275elementStart"](1,"div",23),p["\u0275\u0275elementStart"](2,"div",24),p["\u0275\u0275element"](3,"img",25),p["\u0275\u0275elementEnd"](),p["\u0275\u0275element"](4,"br"),p["\u0275\u0275elementStart"](5,"div",26),p["\u0275\u0275text"](6,"Approvers Not Found"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]())}function V(e,t){if(1&e&&(p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275template"](1,A,13,1,"div",1),p["\u0275\u0275template"](2,T,7,0,"div",4),p["\u0275\u0275elementContainerEnd"]()),2&e){const e=p["\u0275\u0275nextContext"]();p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",e.displayCondition&&(null==e.data1?null:e.data1.data.length)>0),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",e.displayCondition&&0==(null==e.data1?null:e.data1.data.length))}}let W=(()=>{class e{constructor(e,t,n,i,o,r){this._router=e,this.route=t,this.edreqHomeService=n,this.edreqService=i,this._toaster=o,this._loginService=r,this.subs=new j.a,this.displayCondition=!1,this.currentDate=new Date,this.profileOid=this._loginService.getProfile().profile.oid,this.navbarChecked=!1}ngOnInit(){return Object(r.c)(this,void 0,void 0,(function*(){this.edreqHomeService.uncheckAll(),this.edreqHomeService.checkVisited(this.route.snapshot.url[0].path),this.page_loader=!0,this.isActiveRoute(),this.data1=yield this.fetchExitProcess(),this.page_loader=!1}))}fetchExitProcess(){let e={params:{user_aid:this._loginService.getProfile().profile.aid,user_oid:this._loginService.getProfile().profile.oid,current_date:d()(this.currentDate).format("YYYY-MM-DD")}};return new Promise((t,n)=>{this.subs.sink=this.edreqService.fetchExitProcess(e).subscribe(e=>{e.err?this._toaster.showWarning("Error","Something went wrong."):(this.displayCondition=!0,t(e))},e=>{this._toaster.showError("Error","Failed to retrieve Exit Process !",2e3),console.log(e),n(e)})})}isActiveRoute(){const e=this.route.snapshot.url[0].path;console.log(e,"Route is active"),this.edreqHomeService.activedRoutePath=e}directToTerms(){this.edreqHomeService.setNavBarChecked(1),this._router.navigate(["../terms-and-conditions"],{relativeTo:this.route})}backtoreq(){this._router.navigate(["../../resignation-withdrawn"],{relativeTo:this.route})}}return e.\u0275fac=function(t){return new(t||e)(p["\u0275\u0275directiveInject"](o.g),p["\u0275\u0275directiveInject"](o.a),p["\u0275\u0275directiveInject"](O.a),p["\u0275\u0275directiveInject"](f.a),p["\u0275\u0275directiveInject"](h.a),p["\u0275\u0275directiveInject"](g.a))},e.\u0275cmp=p["\u0275\u0275defineComponent"]({type:e,selectors:[["app-ed-know-the-process"]],decls:3,vars:2,consts:[[1,"container-fluid","RequestForm","formpage"],[4,"ngIf"],[1,"d-flex","align-items-center","justify-content-center","mt-3",2,"height","65vh","margin-top","0rem !important"],["matTooltip","Please wait...","diameter","30"],["class","error-message",4,"ngIf"],[1,"Scrollstyle"],[1,"container"],[1,"process-and-descripition"],["class","step active",4,"ngFor","ngForOf"],[1,"button-know-the-process",2,"display","flex","margin-left","4%"],[1,"cancel",2,"margin-right","10px",3,"click"],[1,"approve-previous",3,"click"],[1,"step","active"],[1,"v-stepper"],[1,"circle"],["class","line",4,"ngIf"],[1,"content"],[2,"display","flex","justify-content","flex-start","gap","2%","align-items","baseline"],["type","name","imgWidth","28px","imgHeight","28px",3,"id",4,"ngIf"],[1,"overflowText"],[1,"line"],["type","name","imgWidth","28px","imgHeight","28px",3,"id"],[1,"error-message"],[1,"error-contents"],[1,"error-image"],["src","https://assets.kebs.app/images/no-approvers-found.png","alt","No Approvers found",2,"width","250px"],[1,"error-text"]],template:function(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"div",0),p["\u0275\u0275template"](1,R,3,0,"ng-container",1),p["\u0275\u0275template"](2,V,3,2,"ng-container",1),p["\u0275\u0275elementEnd"]()),2&e&&(p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",t.page_loader),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!t.page_loader))},directives:[i.NgIf,F.c,x.a,i.NgForOf,q.a],styles:[".container[_ngcontent-%COMP%]{width:70%;margin-left:5%;margin-top:3%;font-family:Roboto}.container[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-style:normal;font-weight:500;font-size:14px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;color:#000}.container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-weight:400;font-size:12px;opacity:.5;margin-top:-2%}.container[_ngcontent-%COMP%]   header[_ngcontent-%COMP%], .container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-style:normal;line-height:20px;letter-spacing:.02em;color:#000}.container[_ngcontent-%COMP%]   header[_ngcontent-%COMP%]{font-weight:300;font-size:20px;opacity:.8}a[_ngcontent-%COMP%]{font-style:normal;font-weight:400;font-size:14px;line-height:20px;letter-spacing:.02em;color:#000}.step[_ngcontent-%COMP%]{padding:0;display:flex;flex-direction:row;justify-content:flex-start;background-color:cream;height:10%}.v-stepper[_ngcontent-%COMP%]{position:relative}.step[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{background-color:#fff;border:1px solid #525252;border-radius:100%;width:35px;height:35px;display:inline-block;display:flex;justify-content:center;align-items:center;font-weight:500}.line1[_ngcontent-%COMP%]{border-left:1px solid transparent}.line1[_ngcontent-%COMP%], .step[_ngcontent-%COMP%]   .line[_ngcontent-%COMP%]{top:35px;left:17px;height:100%;position:absolute}.step[_ngcontent-%COMP%]   .line[_ngcontent-%COMP%]{border-left:.8px solid #5e5d5d}.step.completed[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{visibility:visible;background-color:#000;border-color:#000}.step.completed[_ngcontent-%COMP%]   .line[_ngcontent-%COMP%]{border-left:3px solid #000}.step.active[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{visibility:visible;border-color:#000}.step.empty[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{visibility:hidden}.step.empty[_ngcontent-%COMP%]   .line[_ngcontent-%COMP%]{top:0;height:150%}.overflowText[_ngcontent-%COMP%]{flex-wrap:wrap;white-space:normal;overflow:hidden;text-overflow:ellipsis}.step[_ngcontent-%COMP%]:last-child   .line[_ngcontent-%COMP%]{border-left:3px solid #fff;z-index:-1}.content[_ngcontent-%COMP%]{margin-top:1vh;margin-left:40px;display:inline-block}.button-know-the-process[_ngcontent-%COMP%]   .approve-previous[_ngcontent-%COMP%]{background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%)!important}.button-know-the-process[_ngcontent-%COMP%]   .approve-previous[_ngcontent-%COMP%], .button-know-the-process[_ngcontent-%COMP%]   .approve-previous[_ngcontent-%COMP%]:hover{border:1px solid #fff;border-radius:5px;text-transform:capitalize;color:#fff;width:180px;height:40px;display:flex;line-height:33px;padding:2px 10px;justify-content:center;font-family:Roboto;font-style:normal;font-weight:600;font-size:12px;letter-spacing:.01em;flex:none;order:0;flex-grow:0}.button-know-the-process[_ngcontent-%COMP%]   .approve-previous[_ngcontent-%COMP%]:hover{background:linear-gradient(270deg,#f27a6c 105.29%)!important}.button-know-the-process[_ngcontent-%COMP%]   .cancel[_ngcontent-%COMP%], .button-know-the-process[_ngcontent-%COMP%]   .cancel[_ngcontent-%COMP%]:hover{border:1px solid #6b7a99;border-radius:5px;text-transform:capitalize;color:#6b7a99;height:40px;display:flex;line-height:33px;padding:3px 10px;justify-content:center;width:85px;font-family:Roboto;font-style:normal;font-weight:600;font-size:12px;letter-spacing:.01em;flex:none;order:0;flex-grow:0}.error-message[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;font-weight:400;font-size:16px;margin-top:43px;width:50%;display:contents;letter-spacing:.02em;color:#6e7b8f;justify-content:center;text-align:center}.error-message[_ngcontent-%COMP%]   .error-contents[_ngcontent-%COMP%]{display:block;padding:8rem}.Scrollstyle[_ngcontent-%COMP%]{max-height:65vh;overflow-y:scroll}.Scrollstyle[_ngcontent-%COMP%]::-webkit-scrollbar{display:none}"]}),e})();var H=n("3Pt+"),B=n("0IaG"),L=n("kmnG");function $(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"mat-error",8),p["\u0275\u0275text"](1),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"]();p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" ",e.getErrorMessage()," ")}}function G(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"span"),p["\u0275\u0275element"](1,"mat-spinner",9),p["\u0275\u0275elementEnd"]())}function K(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"span"),p["\u0275\u0275text"](1," Ok "),p["\u0275\u0275elementEnd"]())}let J=(()=>{class e{constructor(e,t,n,i,o,r){this.dialogRef=e,this.route=t,this.edreqService=n,this._toaster=i,this._loginService=o,this.data1=r,this.currentDate=new Date,this.email=new H.j("",[H.H.required,H.H.email]),this.subs=new j.a,this.onSubmit=!1,this.disableSubmit=!1}ngOnInit(){}submitNewPersonalEmail(e){return new Promise((t,n)=>{this.subs.sink=this.edreqService.submitNewPersonalEmail(e).subscribe(e=>{t(e)},e=>{this._toaster.showError("Error","Failed to Update your Email!",2e3),console.log(e),n(e)})})}submitEmail(){return Object(r.c)(this,void 0,void 0,(function*(){if(this.email.invalid)return;let e={user_aid:this._loginService.getProfile().profile.aid,user_oid:this._loginService.getProfile().profile.oid,email:this.email.value,current_date:d()(this.currentDate).format("YYYY-MM-DD")};this.disableSubmit=!0,this.pleasewait_loader=!0,this.onSubmit=yield this.submitNewPersonalEmail(e),this.pleasewait_loader=!1,this.dialogRef.close({status:this.onSubmit.err,email:this.email.value}),this._toaster.showInfo("Success","Your email has been updated",2e3)}))}getErrorMessage(){return this.email.hasError("required")?"You must enter a value":this.email.hasError("email")?"Not a valid email":""}close(){}}return e.\u0275fac=function(t){return new(t||e)(p["\u0275\u0275directiveInject"](B.h),p["\u0275\u0275directiveInject"](o.a),p["\u0275\u0275directiveInject"](f.a),p["\u0275\u0275directiveInject"](h.a),p["\u0275\u0275directiveInject"](g.a),p["\u0275\u0275directiveInject"](B.a))},e.\u0275cmp=p["\u0275\u0275defineComponent"]({type:e,selectors:[["app-email-edit-personal"]],decls:11,vars:5,consts:[[1,"email-card"],[1,"header"],[1,"form-group"],["type","text","id","exampleInputEmail1","aria-describedby","emailHelp","placeholder","Enter email",1,"text-email-field","form-control",3,"formControl"],["style","font-family: 'Roboto'; font-size: 10px; color: #ef4a61",4,"ngIf"],[1,"lower"],[1,"submit-email",3,"disabled","click"],[4,"ngIf"],[2,"font-family","Roboto","font-size","10px","color","#ef4a61"],["matTooltip","Your request is being submitted!!","diameter","20"]],template:function(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"mat-card",0),p["\u0275\u0275elementStart"](1,"div",1),p["\u0275\u0275text"](2,"Enter your personal email ID"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](3,"form"),p["\u0275\u0275elementStart"](4,"div",2),p["\u0275\u0275element"](5,"input",3),p["\u0275\u0275template"](6,$,2,1,"mat-error",4),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](7,"div",5),p["\u0275\u0275elementStart"](8,"button",6),p["\u0275\u0275listener"]("click",(function(){return t.submitEmail()})),p["\u0275\u0275template"](9,G,2,0,"span",7),p["\u0275\u0275template"](10,K,2,0,"span",7),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e&&(p["\u0275\u0275advance"](5),p["\u0275\u0275property"]("formControl",t.email),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",t.email.invalid&&t.email.touched),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("disabled",t.disableSubmit),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",t.pleasewait_loader),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!t.pleasewait_loader))},directives:[u.a,H.J,H.w,H.x,H.e,H.v,H.k,i.NgIf,L.b,F.c,x.a],styles:[".email-card[_ngcontent-%COMP%]{height:180px;width:350px;margin-left:auto;margin-right:auto;border:1px solid #5e5d5d}.email-card[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{padding-left:1rem;font-weight:500;font-size:16px;line-height:19px;color:#ef4a61}.email-card[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]{padding-left:2rem;margin-top:1rem;width:90%}.email-card[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .text-email-field[_ngcontent-%COMP%]{resize:none;box-shadow:none!important}.email-card[_ngcontent-%COMP%]   .lower[_ngcontent-%COMP%]{display:flex;justify-content:flex-start;align-items:center;gap:2%}.email-card[_ngcontent-%COMP%]   .lower[_ngcontent-%COMP%]   .submit-email[_ngcontent-%COMP%]{margin-left:6%;margin-top:1rem;padding:6px 16px;gap:6px;width:61px;height:32px;background:#ee4961;border:none;border-radius:5px;font-weight:700;font-size:14px;line-height:140%;letter-spacing:.01em;text-transform:capitalize;color:#fff}.email-card[_ngcontent-%COMP%]   .lower[_ngcontent-%COMP%]   .spinner[_ngcontent-%COMP%], .email-card[_ngcontent-%COMP%]   .lower[_ngcontent-%COMP%]   .submit-email[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center}button.submit-email[_ngcontent-%COMP%]   [_ngcontent-%COMP%]:disabled{opacity:.8}"]}),e})();var U=n("PSD3"),X=n.n(U),Q=n("dNgK"),Z=n("TmG/");function ee(e,t){1&e&&(p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275elementStart"](1,"div",2),p["\u0275\u0275element"](2,"mat-spinner",3),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementContainerEnd"]())}function te(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div"),p["\u0275\u0275elementStart"](1,"div",24),p["\u0275\u0275elementStart"](2,"div",9),p["\u0275\u0275text"](3),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](4,"div",10),p["\u0275\u0275text"](5),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](6,"div",10),p["\u0275\u0275text"](7),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](8,"div",9),p["\u0275\u0275text"](9),p["\u0275\u0275pipe"](10,"date"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](11,"div",10),p["\u0275\u0275text"](12),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=p["\u0275\u0275nextContext"](3);p["\u0275\u0275advance"](3),p["\u0275\u0275textInterpolate"](e.associate_id),p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"](" ",e.NAME," "),p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate"](e.employment_type),p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate"](p["\u0275\u0275pipeBind2"](10,5,n.currentDate,"dd-MMM-YYYY")),p["\u0275\u0275advance"](3),p["\u0275\u0275textInterpolate"](e.personal_email)}}function ne(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"span",33),p["\u0275\u0275text"](1,"\xa0*"),p["\u0275\u0275elementEnd"]())}function ie(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"p",34),p["\u0275\u0275text"](1),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](4);p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" Enter ",(null==e.fieldConfig||null==e.fieldConfig.nsr?null:e.fieldConfig.nsr.field_label)||"NSR Number"," ")}}function oe(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",25),p["\u0275\u0275elementStart"](1,"div",26),p["\u0275\u0275elementStart"](2,"div",27),p["\u0275\u0275text"](3),p["\u0275\u0275template"](4,ne,2,0,"span",28),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](5,"div",29),p["\u0275\u0275elementStart"](6,"div",30),p["\u0275\u0275element"](7,"input",31),p["\u0275\u0275template"](8,ie,2,1,"p",32),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](3);let t=null;p["\u0275\u0275advance"](3),p["\u0275\u0275textInterpolate1"](" ",null==e.fieldConfig||null==e.fieldConfig.nsr?null:e.fieldConfig.nsr.field_label," "),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",null==e.fieldConfig||null==e.fieldConfig.nsr?null:e.fieldConfig.nsr.is_mandatory),p["\u0275\u0275advance"](4),p["\u0275\u0275property"]("ngIf",(null==e.fieldConfig||null==e.fieldConfig.nsr?null:e.fieldConfig.nsr.is_mandatory)&&e.errtxt&&(null==e.resignreq||null==(t=e.resignreq.get("nsr"))?null:t.invalid))}}function re(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"span",33),p["\u0275\u0275text"](1,"\xa0*"),p["\u0275\u0275elementEnd"]())}function ae(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"p",34),p["\u0275\u0275text"](1),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](4);p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" Enter ",null!=e.fieldConfig&&null!=e.fieldConfig.primary_reason&&e.fieldConfig.primary_reason.field_label?null==e.fieldConfig||null==e.fieldConfig.primary_reason?null:e.fieldConfig.primary_reason.field_label:"Primary Reason"," ")}}function le(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",35),p["\u0275\u0275elementStart"](1,"div",36),p["\u0275\u0275text"](2),p["\u0275\u0275template"](3,re,2,0,"span",28),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](4,"div",29),p["\u0275\u0275elementStart"](5,"div",30),p["\u0275\u0275elementStart"](6,"app-input-search",37),p["\u0275\u0275listener"]("change",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](3).getSecondaryReasonsMasterdata()})),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](7,ae,2,1,"p",32),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"](3);let t=null;p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"](" ",null==e.fieldConfig||null==e.fieldConfig.primary_reason?null:e.fieldConfig.primary_reason.field_label," "),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",null==e.fieldConfig||null==e.fieldConfig.primary_reason?null:e.fieldConfig.primary_reason.is_mandatory),p["\u0275\u0275advance"](3),p["\u0275\u0275propertyInterpolate1"]("placeholder","Select ",null!=e.fieldConfig&&null!=e.fieldConfig.primary_reason&&e.fieldConfig.primary_reason.field_label?null==e.fieldConfig||null==e.fieldConfig.primary_reason?null:e.fieldConfig.primary_reason.field_label:"Primary Reason",""),p["\u0275\u0275property"]("required",null==e.fieldConfig||null==e.fieldConfig.primary_reason?null:e.fieldConfig.primary_reason.is_mandatory)("list",e.primaryReasonsMasterData)("disableNone",!0)("hideMatLabel",!0),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",(null==e.fieldConfig||null==e.fieldConfig.primary_reason?null:e.fieldConfig.primary_reason.is_mandatory)&&e.errtxt&&(null==e.resignreq||null==(t=e.resignreq.get("primary_reason"))?null:t.invalid))}}function se(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"span",33),p["\u0275\u0275text"](1,"\xa0*"),p["\u0275\u0275elementEnd"]())}function ce(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"p",34),p["\u0275\u0275text"](1),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](4);p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" Enter ",(null==e.fieldConfig||null==e.fieldConfig.secondary_reason?null:e.fieldConfig.secondary_reason.field_label)||"Secondary Reason"," ")}}function de(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",35),p["\u0275\u0275elementStart"](1,"div",36),p["\u0275\u0275text"](2),p["\u0275\u0275template"](3,se,2,0,"span",28),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](4,"div",29),p["\u0275\u0275elementStart"](5,"div",30),p["\u0275\u0275elementStart"](6,"app-input-search",38),p["\u0275\u0275listener"]("change",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](3).getTertiaryReasonsMasterdata()})),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](7,ce,2,1,"p",32),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"](3);let t=null;p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"](" ",null==e.fieldConfig||null==e.fieldConfig.secondary_reason?null:e.fieldConfig.secondary_reason.field_label," "),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",null==e.fieldConfig||null==e.fieldConfig.secondary_reason?null:e.fieldConfig.secondary_reason.is_mandatory),p["\u0275\u0275advance"](3),p["\u0275\u0275propertyInterpolate1"]("placeholder","Select ",null!=e.fieldConfig&&null!=e.fieldConfig.secondary_reason&&e.fieldConfig.secondary_reason.field_label?null==e.fieldConfig||null==e.fieldConfig.secondary_reason?null:e.fieldConfig.secondary_reason.field_label:"Secondary Reason",""),p["\u0275\u0275property"]("required",null==e.fieldConfig||null==e.fieldConfig.secondary_reason?null:e.fieldConfig.secondary_reason.is_mandatory)("list",e.secondaryReasonsMasterData)("disableNone",!0)("hideMatLabel",!0),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",(null==e.fieldConfig||null==e.fieldConfig.secondary_reason?null:e.fieldConfig.secondary_reason.is_mandatory)&&e.errtxt&&(null==e.resignreq||null==(t=e.resignreq.get("secondary_reason"))?null:t.invalid))}}function pe(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"span",33),p["\u0275\u0275text"](1,"\xa0*"),p["\u0275\u0275elementEnd"]())}function me(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"p",34),p["\u0275\u0275text"](1),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](4);p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" Enter ",null!=e.fieldConfig&&null!=e.fieldConfig.tertiary_reason&&e.fieldConfig.tertiary_reason.field_label?null==e.fieldConfig||null==e.fieldConfig.tertiary_reason?null:e.fieldConfig.tertiary_reason.field_label:"Tertiary Reason"," ")}}function ge(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",35),p["\u0275\u0275elementStart"](1,"div",36),p["\u0275\u0275text"](2),p["\u0275\u0275template"](3,pe,2,0,"span",28),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](4,"div",29),p["\u0275\u0275elementStart"](5,"div",30),p["\u0275\u0275element"](6,"app-input-search",39),p["\u0275\u0275template"](7,me,2,1,"p",32),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](3);let t=null;p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"](" ",null==e.fieldConfig||null==e.fieldConfig.tertiary_reason?null:e.fieldConfig.tertiary_reason.field_label," "),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",null==e.fieldConfig||null==e.fieldConfig.tertiary_reason?null:e.fieldConfig.tertiary_reason.is_mandatory),p["\u0275\u0275advance"](3),p["\u0275\u0275propertyInterpolate1"]("placeholder","Select ",null!=e.fieldConfig&&null!=e.fieldConfig.tertiary_reason&&e.fieldConfig.tertiary_reason.field_label?null==e.fieldConfig||null==e.fieldConfig.tertiary_reason?null:e.fieldConfig.tertiary_reason.field_label:"Tertiary Reason",""),p["\u0275\u0275property"]("required",null==e.fieldConfig||null==e.fieldConfig.tertiary_reason?null:e.fieldConfig.tertiary_reason.is_mandatory)("list",e.tertiaryReasonsMasterData)("disableNone",!0)("hideMatLabel",!0),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",(null==e.fieldConfig||null==e.fieldConfig.tertiary_reason?null:e.fieldConfig.tertiary_reason.is_mandatory)&&e.errtxt&&(null==e.resignreq||null==(t=e.resignreq.get("tertiary_reason"))?null:t.invalid))}}function fe(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"span",33),p["\u0275\u0275text"](1,"\xa0* "),p["\u0275\u0275elementEnd"]())}function he(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"p",45),p["\u0275\u0275text"](1),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](4);p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" Enter ",null!=e.fieldConfig&&null!=e.fieldConfig.notes&&e.fieldConfig.notes.field_label?null==e.fieldConfig||null==e.fieldConfig.notes?null:e.fieldConfig.notes.field_label:"Notes"," ")}}function ue(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",40),p["\u0275\u0275elementStart"](1,"div",41),p["\u0275\u0275elementStart"](2,"label",42),p["\u0275\u0275text"](3),p["\u0275\u0275template"](4,fe,2,0,"span",28),p["\u0275\u0275elementEnd"](),p["\u0275\u0275element"](5,"textarea",43),p["\u0275\u0275template"](6,he,2,1,"p",44),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](3);let t=null;p["\u0275\u0275property"]("hideMatLabel",!0)("disableNone",!0),p["\u0275\u0275advance"](3),p["\u0275\u0275textInterpolate1"]("",null==e.fieldConfig||null==e.fieldConfig.notes?null:e.fieldConfig.notes.field_label," "),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",null==e.fieldConfig||null==e.fieldConfig.notes?null:e.fieldConfig.notes.is_mandatory),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngIf",(null==e.fieldConfig||null==e.fieldConfig.notes?null:e.fieldConfig.notes.is_mandatory)&&e.errtxt&&(null==e.resignreq||null==(t=e.resignreq.get("notes"))?null:t.invalid))}}function ve(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"span"),p["\u0275\u0275element"](1,"mat-spinner",46),p["\u0275\u0275elementEnd"]())}function xe(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"span"),p["\u0275\u0275text"](1," Submit Resignation "),p["\u0275\u0275elementEnd"]())}function _e(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",5),p["\u0275\u0275elementStart"](1,"div",6),p["\u0275\u0275elementStart"](2,"div",7),p["\u0275\u0275elementStart"](3,"div",8),p["\u0275\u0275elementStart"](4,"div",9),p["\u0275\u0275text"](5,"Emp ID"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](6,"div",9),p["\u0275\u0275text"](7,"Name"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](8,"div",10),p["\u0275\u0275text"](9,"Employment Type"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](10,"div",9),p["\u0275\u0275text"](11,"Date"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](12,"div",9),p["\u0275\u0275elementStart"](13,"div",11),p["\u0275\u0275text"](14," Email ID "),p["\u0275\u0275elementStart"](15,"div",12),p["\u0275\u0275elementStart"](16,"mat-icon",13),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](2).editPersonalEmail()})),p["\u0275\u0275text"](17,"edit"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](18,te,13,8,"div",14),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](19,"header",15),p["\u0275\u0275text"](20,"Reason for Exit"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](21,"form",16),p["\u0275\u0275template"](22,oe,9,3,"div",17),p["\u0275\u0275elementStart"](23,"div",18),p["\u0275\u0275elementContainerStart"](24),p["\u0275\u0275template"](25,le,8,8,"div",19),p["\u0275\u0275template"](26,de,8,8,"div",19),p["\u0275\u0275template"](27,ge,8,8,"div",19),p["\u0275\u0275elementContainerEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](28,ue,7,5,"div",20),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](29,"div",21),p["\u0275\u0275elementStart"](30,"button",22),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](2).backtoterms()})),p["\u0275\u0275text"](31," Cancel "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](32,"button",23),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](2).finish()})),p["\u0275\u0275template"](33,ve,2,0,"span",1),p["\u0275\u0275template"](34,xe,2,0,"span",1),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"](2);p["\u0275\u0275advance"](18),p["\u0275\u0275property"]("ngForOf",e.empdata),p["\u0275\u0275advance"](3),p["\u0275\u0275property"]("formGroup",e.resignreq),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf","number"==(null==e.fieldConfig||null==e.fieldConfig.nsr?null:e.fieldConfig.nsr.field_type)),p["\u0275\u0275advance"](3),p["\u0275\u0275property"]("ngIf",null==e.fieldConfig||null==e.fieldConfig.primary_reason?null:e.fieldConfig.primary_reason.is_active_field),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",null==e.fieldConfig||null==e.fieldConfig.secondary_reason?null:e.fieldConfig.secondary_reason.is_active_field),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",null==e.fieldConfig||null==e.fieldConfig.tertiary_reason?null:e.fieldConfig.tertiary_reason.is_active_field),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",null==e.fieldConfig||null==e.fieldConfig.notes?null:e.fieldConfig.notes.is_active_field),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("disabled",e.disableSubmit),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("disabled",e.disableSubmit),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",e.pleasewait_loader),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!e.pleasewait_loader)}}function Ce(e,t){if(1&e&&(p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275template"](1,_e,35,11,"div",4),p["\u0275\u0275elementContainerEnd"]()),2&e){const e=p["\u0275\u0275nextContext"]();p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",e.displayCondition)}}let ye=(()=>{class e{constructor(e,t,n,i,o,r,a,l,s){this._router=e,this.route=t,this.formBuilder=n,this._snackBar=i,this.edreqService=o,this._toaster=r,this._loginService=a,this.dialog=l,this.edreqHomeService=s,this.required=!0,this.resignreq=new H.m({primary_reason:new H.j,secondary_reason:new H.j,tertiary_reason:new H.j,notes:new H.j,nsr:new H.j}),this.subs=new j.a,this.fieldConfig={},this.currentDate=new Date,this.empdata=[],this.displayCondition=!1,this.disableSubmit=!1,this.errtxt=!1}ngOnInit(){return Object(r.c)(this,void 0,void 0,(function*(){this.my_aid=this._loginService.getProfile().profile.aid,this.page_loader=!0,this.isActiveRoute(),this.edreqHomeService.checkVisited(this.route.snapshot.url[0].path),this.data1=yield this.handleFieldConfig(),this.empdata=yield this.fetchEmployeeDetailsResignationRequest(),this.primaryReasonsMasterData=yield this.getDropDownDataPrimaryReason(),this.page_loader=!1}))}editPersonalEmail(){this.dialog.open(J,{height:"180px",width:"350px"}).afterClosed().subscribe(e=>{console.log(e),this.empdata[0].personal_email=e.status?"":e.email})}fetchResignationRequest(){let e={params:{user_aid:this._loginService.getProfile().profile.aid,user_oid:this._loginService.getProfile().profile.oid,key:"resignation_request"}};return new Promise((t,n)=>{this.subs.sink=this.edreqService.fetchResignationRequest(e).subscribe(e=>{e.err?this._toaster.showWarning("Error","Something went wrong."):(this.displayCondition=!0,t(e.data)),t(e.data)},e=>{this._toaster.showError("Error","Failed to retrieve Resignation Request!",2e3),console.log(e),n(e)})})}handleFieldConfig(){return Object(r.c)(this,void 0,void 0,(function*(){let e=yield this.fetchResignationRequest();e.length>0&&e.forEach((e,t)=>{this.fieldConfig[e.field_key]=e;let n=this.resignreq.get(e.field_key);e.is_mandatory?n.setValidators(H.H.required):n.clearValidators(),n.updateValueAndValidity()})}))}fetchEmployeeDetailsResignationRequest(){let e={params:{user_aid:this._loginService.getProfile().profile.aid,user_oid:this._loginService.getProfile().profile.oid,current_date:d()(this.currentDate).format("YYYY-MM-DD")}};return new Promise((t,n)=>{this.subs.sink=this.edreqService.fetchEmployeeDetailsResignationRequest(e).subscribe(e=>{t(e.data)},e=>{this._toaster.showError("Error","Failed to retrieve employee data!",2e3),console.log(e),n(e)})})}getDropDownDataPrimaryReason(){let e={user_aid:this._loginService.getProfile().profile.aid};return new Promise((t,n)=>{this.subs.sink=this.edreqService.getDropDownDataPrimaryReason(e).subscribe(e=>{t(e.data)},e=>{this._toaster.showError("Error","Failed to retrive drop down menu!",2e3),console.log(e),n(e)})})}getSecondaryReasonsMasterdata(){console.log("getSecondaryReasonsMasterdata called !");let e=this.resignreq.get("primary_reason").value;this.resignreq.get("secondary_reason").reset(),this.resignreq.get("tertiary_reason").reset();let t={user_aid:this._loginService.getProfile().profile.aid,reasons:[e]};return new Promise((e,n)=>{this.subs.sink=this.edreqService.getDropDownDataSecondaryReason(t).subscribe(t=>{this.secondaryReasonsMasterData=t.data,e(t.data)},e=>{this._toaster.showError("Error","Failed to retrive drop down menu!",2e3),console.log(e),n(e)})})}getTertiaryReasonsMasterdata(){console.log("getTertiatyReasonsMasterdata called !");let e=this.resignreq.get("primary_reason").value,t=this.resignreq.get("secondary_reason").value;this.resignreq.get("tertiary_reason").reset();let n={user_aid:this._loginService.getProfile().profile.aid,reasons:[e,t]};return new Promise((e,t)=>{this.subs.sink=this.edreqService.getDropDownDataTertiaryReason(n).subscribe(t=>{this.tertiaryReasonsMasterData=t.data,e(t.data)},e=>{this._toaster.showError("Error","Failed to retrive drop down menu!",2e3),console.log(e),t(e)})})}submitExitRequest(e){return new Promise((t,n)=>{this.subs.sink=this.edreqService.submitExitRequest(e).subscribe(e=>{"P"!=e.status?this.handleSuccessfulSubmission():this.handleExistingRequest(),t(e)},e=>{this._toaster.showError("Error","Kindly fill all the mandatory fields",2e3),console.log(e),n(e)})})}finish(){return Object(r.c)(this,void 0,void 0,(function*(){if(this.resignreq.valid){const e=this.createParams();this.disableSubmit=!0,this.pleasewait_loader=!0,this.onSubmit=yield this.submitExitRequest(e),this.pleasewait_loader=!1}else this.handleInvalidForm()}))}createParams(){const e=this._loginService.getProfile().profile;return{user_aid:e.aid,user_oid:e.oid,current_date:d()(this.currentDate).format("YYYY-MM-DD"),primary_reason:this.resignreq.get("primary_reason").value,secondary_reason:this.resignreq.get("secondary_reason").value,tertiary_reason:this.resignreq.get("tertiary_reason").value,notes:this.resignreq.get("notes").value,nsr:this.resignreq.get("nsr").value}}handleExistingRequest(){this._toaster.showWarning("Request Already Exist!!","You can see the status here"),this._router.navigate(["../../resigantion-withdrawn"],{relativeTo:this.route})}handleSuccessfulSubmission(){this._router.navigate(["../../detail-resignation-page"],{relativeTo:this.route}),X.a.fire("Exit request created Successfully !","A.ID : "+this.my_aid,"success")}handleInvalidForm(){this._toaster.showWarning("Mandatory fields are not filled!!","Kindly fill all the mandatory fields"),this.errtxt=!0}isActiveRoute(){const e=this.route.snapshot.url[0].path;console.log(e,"Route is active"),this.edreqHomeService.activedRoutePath=e}backtoterms(){this.edreqHomeService.setNavBarUnChecked(2),this._router.navigate(["../terms-and-conditions"],{relativeTo:this.route})}}return e.\u0275fac=function(t){return new(t||e)(p["\u0275\u0275directiveInject"](o.g),p["\u0275\u0275directiveInject"](o.a),p["\u0275\u0275directiveInject"](H.i),p["\u0275\u0275directiveInject"](Q.a),p["\u0275\u0275directiveInject"](f.a),p["\u0275\u0275directiveInject"](h.a),p["\u0275\u0275directiveInject"](g.a),p["\u0275\u0275directiveInject"](B.b),p["\u0275\u0275directiveInject"](O.a))},e.\u0275cmp=p["\u0275\u0275defineComponent"]({type:e,selectors:[["app-ed-resignation-request"]],decls:3,vars:2,consts:[[1,"container-fluid","RequestForm","formpage"],[4,"ngIf"],[1,"d-flex","align-items-center","justify-content-center","mt-3",2,"height","65vh","margin-top","0rem !important"],["matTooltip","Please wait...","diameter","30"],["class","p-2","style","width: 100%",4,"ngIf"],[1,"p-2",2,"width","100%"],[1,"Scrollstyle"],[1,"table-light"],[1,"table-content-head","row"],[1,"col"],[1,"overflowText","col"],[1,"personal-email"],[1,"email-edit-option"],[3,"click"],[4,"ngFor","ngForOf"],[1,"reason-header"],[3,"formGroup"],["style","padding-left: 1rem;",4,"ngIf"],[1,"reason-for-exit-head","row"],["class","col-4","style","padding: 1% 0% 1%",4,"ngIf"],["class","reason-briefing",3,"hideMatLabel","disableNone",4,"ngIf"],[1,"button-resignation-request","row",2,"display","flex","margin","70px","padding","12px"],[1,"cancel",2,"margin-right","10px",3,"disabled","click"],[1,"approve-previous",3,"disabled","click"],[1,"table-content","row"],[2,"padding-left","1rem"],[2,"padding","1% 0% 1%"],[1,"nsr"],["class","required-star",4,"ngIf"],[1,"reason-for-exit"],[2,"display","block"],["formControlName","nsr","id","textAreaExample","placeholder","Enter NSR number","type","number",1,"form-control",2,"font-size","14px","resize","none"],["class","errMessage",4,"ngIf"],[1,"required-star"],[1,"errMessage"],[1,"col-4",2,"padding","1% 0% 1%"],[1,"reason-head"],["hideMatLabel","false","formControlName","primary_reason",1,"reason-for-exit-field-inputsearch","col-4",2,"width","100%",3,"required","placeholder","list","disableNone","hideMatLabel","change"],["hideMatLabel","false","formControlName","secondary_reason",1,"reason-for-exit-field-inputsearch","col-4",2,"width","100%",3,"required","placeholder","list","disableNone","hideMatLabel","change"],["hideMatLabel","false","formControlName","tertiary_reason",1,"reason-for-exit-field-inputsearch","col-4",2,"width","100%",3,"required","placeholder","list","disableNone","hideMatLabel"],[1,"reason-briefing",3,"hideMatLabel","disableNone"],[2,"width","138%","margin-left","1%","height","90px","margin-top","25px","resize","none"],["for","textAreaExample",1,"form-label"],["formControlName","notes","id","textAreaExample","placeholder","Type here!!","rows","4",1,"form-control",2,"font-size","14px","resize","none"],["class","errMessage-notes",4,"ngIf"],[1,"errMessage-notes"],["matTooltip","Your request is being submitted!!","diameter","25"]],template:function(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"div",0),p["\u0275\u0275template"](1,ee,3,0,"ng-container",1),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](2,Ce,2,1,"ng-container",1)),2&e&&(p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",t.page_loader),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!t.page_loader))},directives:[i.NgIf,F.c,x.a,v.a,i.NgForOf,H.J,H.w,H.n,H.e,H.A,H.v,H.l,Z.a,H.F],pipes:[i.DatePipe],styles:["[_nghost-%COMP%]{display:flex;flex-direction:column;align-items:flex-start}.required-star[_ngcontent-%COMP%]{color:#cf0001}.table-light[_ngcontent-%COMP%]{font-family:Roboto;font-size:12px;line-height:14px;letter-spacing:.02em;text-align:left;margin-top:1%}.table-light[_ngcontent-%COMP%]   .table-content-head[_ngcontent-%COMP%]{font-weight:400}.reason-header[_ngcontent-%COMP%], .table-light[_ngcontent-%COMP%]   .table-content[_ngcontent-%COMP%]{font-weight:600}.reason-header[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;font-size:14px;line-height:16px;letter-spacing:.02em;color:#5e5d5d;flex:none;order:0;flex-grow:0;padding:3% 1rem 1%}.reason-head[_ngcontent-%COMP%]{padding:1% 6%}.nsr[_ngcontent-%COMP%]{padding:0;margin-bottom:1%;color:#6e7b8f}.overflowText[_ngcontent-%COMP%]{flex-wrap:wrap;white-space:normal;overflow:hidden;text-overflow:ellipsis}.reason-for-exit-head[_ngcontent-%COMP%]{color:#6e7b8f}.reason-for-exit[_ngcontent-%COMP%], .reason-for-exit-head[_ngcontent-%COMP%]{display:flex;justify-content:flex-start}.reason-for-exit[_ngcontent-%COMP%]{gap:10%}.reason-for-exit[_ngcontent-%COMP%]     .mat-form-field   .mat-form-field-wrapper{padding-bottom:0!important}.reason-for-exit[_ngcontent-%COMP%]     .mat-form-field{font-size:13px!important;width:auto!important}.resign-req[_ngcontent-%COMP%]{font-family:Roboto;font-size:13px;font-weight:400;line-height:24px;letter-spacing:.02em;text-align:left;color:#45546e}.reason-briefing[_ngcontent-%COMP%]{margin-left:1%;margin-top:0;width:70%}.reason-briefing[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;font-size:12px;font-weight:400;line-height:24px;letter-spacing:.02em;text-align:left;color:#000;margin-top:2%}.approve-previous[_ngcontent-%COMP%]{position:relative;overflow:hidden;align-items:center}.approve-previous[_ngcontent-%COMP%], .approve-previous[_ngcontent-%COMP%]:hover{border:1px solid #fff;border-radius:5px;text-transform:capitalize;color:#fff;background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%)!important;width:160px;height:40px;display:flex;line-height:33px;padding:2px 10px;justify-content:center;font-family:Roboto;font-style:normal;font-weight:600;font-size:12px;letter-spacing:.01em;flex:none;order:0;flex-grow:0}.approve-previous[_ngcontent-%COMP%]   .loader[_ngcontent-%COMP%]{position:absolute;display:flex;justify-content:center;transform:translate(-50%,-50%);display:none;width:20px;height:20px;border:2px solid #fff;border-top-color:#000;border-radius:50%;animation:loader-rotate 1s linear infinite}@keyframes loader-rotate{0%{transform:translate(-50%,-50%) rotate(0deg)}to{transform:translate(-50%,-50%) rotate(1turn)}}.approve-previous.loading[_ngcontent-%COMP%]   .loader[_ngcontent-%COMP%]{display:block}.approve-previous.loading[_ngcontent-%COMP%]{pointer-events:none;opacity:.7}.mat-spinner[_ngcontent-%COMP%]   circle[_ngcontent-%COMP%],   .approve-previous .mat-progress-spinner circle{stroke:#fff}.cancel[_ngcontent-%COMP%], .cancel[_ngcontent-%COMP%]:hover{border:1px solid #6b7a99;border-radius:5px;text-transform:capitalize;color:#6b7a99;height:40px;display:flex;line-height:33px;padding:3px 10px;justify-content:center;width:85px;font-family:Roboto;font-style:normal;font-weight:600;font-size:12px;letter-spacing:.01em;flex:none;order:0;flex-grow:0}.reason-briefing[_ngcontent-%COMP%]     .form-control:focus{border-color:#b9c0ca;box-shadow:none}.personal-email[_ngcontent-%COMP%]{display:flex;justify-content:flex-start;column-gap:3%}.personal-email[_ngcontent-%COMP%]   .email-edit-option[_ngcontent-%COMP%]{margin-top:-2px;color:#5e5d5d}.personal-email[_ngcontent-%COMP%]   .email-edit-option[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%]{font-size:14px}.email-edit-option[_ngcontent-%COMP%]   [_ngcontent-%COMP%]:hover{cursor:pointer;color:#3d3c3c}.email-edit-option[_ngcontent-%COMP%]   [_ngcontent-%COMP%]:hover   .mat-icon[_ngcontent-%COMP%]{font-size:15px}.Scrollstyle[_ngcontent-%COMP%]{max-height:65vh;overflow-y:scroll}.Scrollstyle[_ngcontent-%COMP%]   [_ngcontent-%COMP%]::-webkit-scrollbar-track{background-color:#f1f1f1}.Scrollstyle[_ngcontent-%COMP%]   [_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background-color:#ef4a61}.Scrollstyle[_ngcontent-%COMP%]   [_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background-color:#e92f48}.errMessage[_ngcontent-%COMP%]{margin-left:7%}.errMessage[_ngcontent-%COMP%], .errMessage-notes[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;font-size:10px;color:#ef4a61}button.approve-previous[_ngcontent-%COMP%]:disabled, button.cancel[_ngcontent-%COMP%]   [_ngcontent-%COMP%]:disabled{opacity:.8}"]}),e})();var be=n("bSwM");function Me(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"ng-contianer"),p["\u0275\u0275elementStart"](1,"div",2),p["\u0275\u0275element"](2,"mat-spinner",3),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]())}function Se(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"ol",13),p["\u0275\u0275elementStart"](1,"div",14),p["\u0275\u0275text"](2),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=t.index;p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate2"]("",n+1," . ",e.terms_and_condition,"")}}function Pe(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div"),p["\u0275\u0275elementStart"](1,"div",4),p["\u0275\u0275elementStart"](2,"div",5),p["\u0275\u0275elementStart"](3,"h2",6),p["\u0275\u0275text"](4,"Terms & Conditions"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](5,Se,3,2,"ol",7),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](6,"section",8),p["\u0275\u0275elementStart"](7,"mat-checkbox",9),p["\u0275\u0275listener"]("ngModelChange",(function(t){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](2).isChecked=t})),p["\u0275\u0275text"](8," I have read and understood the Terms & conditions "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](9,"div",10),p["\u0275\u0275elementStart"](10,"button",11),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](2).backToExitProcess()})),p["\u0275\u0275text"](11," Cancel "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](12,"button",12),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](2).directToResign()})),p["\u0275\u0275text"](13," Proceed Request "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"](2);p["\u0275\u0275advance"](5),p["\u0275\u0275property"]("ngForOf",e.data),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngModel",e.isChecked)}}function Oe(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"ng-contianer"),p["\u0275\u0275template"](1,Pe,14,2,"div",1),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"]();p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",e.displayCondition)}}let we=(()=>{class e{constructor(e,t,n,i,o,r,a){this._router=e,this.route=t,this._snackBar=n,this.edreqHomeService=i,this.edreqService=o,this._toaster=r,this._loginService=a,this.isChecked=!1,this.subs=new j.a,this.currentDate=new Date}ngOnInit(){return Object(r.c)(this,void 0,void 0,(function*(){this.page_loader=!0,this.isActiveRoute(),this.edreqHomeService.checkVisited(this.route.snapshot.url[0].path),this.data=yield this.fetchTermsAndConditions(),this.page_loader=!1}))}fetchTermsAndConditions(){let e={params:{user_aid:this._loginService.getProfile().profile.aid,user_oid:this._loginService.getProfile().profile.oid,current_date:d()(this.currentDate).format("YYYY-MM-DD")}};return new Promise((t,n)=>{this.subs.sink=this.edreqService.fetchTermsAndConditions(e).subscribe(e=>{e.err?this._toaster.showWarning("Error","Something went wrong."):(this.displayCondition=!0,t(e.data)),t(e.data)},e=>{this._toaster.showError("Error","Failed to retrieve Terms And Conditions!",2e3),console.log(e),n(e)})})}isActiveRoute(){const e=this.route.snapshot.url[0].path;console.log(e,"Route is active"),this.edreqHomeService.activedRoutePath=e}directToResign(){this.isChecked?(this.edreqHomeService.setNavBarChecked(2),console.log(this.edreqHomeService.requestTabs),this._router.navigate(["../resignation-request"],{relativeTo:this.route})):this.openSnackBar("Accept the Terms and Conditions","Ok")}backToExitProcess(){this.edreqHomeService.setNavBarUnChecked(1),this._router.navigate(["../know-the-process"],{relativeTo:this.route})}openSnackBar(e,t){this._snackBar.open(e,t,{duration:3e3})}}return e.\u0275fac=function(t){return new(t||e)(p["\u0275\u0275directiveInject"](o.g),p["\u0275\u0275directiveInject"](o.a),p["\u0275\u0275directiveInject"](Q.a),p["\u0275\u0275directiveInject"](O.a),p["\u0275\u0275directiveInject"](f.a),p["\u0275\u0275directiveInject"](h.a),p["\u0275\u0275directiveInject"](g.a))},e.\u0275cmp=p["\u0275\u0275defineComponent"]({type:e,selectors:[["app-ed-terms-and-conditions"]],decls:3,vars:2,consts:[[1,"container-fluid","RequestForm","formpage"],[4,"ngIf"],[1,"d-flex","align-items-center","justify-content-center","mt-3",2,"height","65vh","margin-top","0rem !important"],["matTooltip","Please wait...","diameter","30"],[1,"Scrollstyle"],[1,"terms"],[1,"header-text-style"],["class","conditions",4,"ngFor","ngForOf"],[1,"checkbox-light"],[1,"checkbox-text",3,"ngModel","ngModelChange"],[1,"termsbuttons"],[1,"cancel",2,"margin-right","10px",3,"click"],[1,"approve-previous",3,"click"],[1,"conditions"],[1,"one"]],template:function(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"div",0),p["\u0275\u0275template"](1,Me,3,0,"ng-contianer",1),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](2,Oe,2,1,"ng-contianer",1)),2&e&&(p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",t.page_loader),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!t.page_loader))},directives:[i.NgIf,F.c,x.a,i.NgForOf,be.a,H.v,H.y],styles:[".terms[_ngcontent-%COMP%]{font-family:Roboto;margin-top:25px;margin-left:69px;margin-right:140px}.header-text-style[_ngcontent-%COMP%]{font-weight:700;font-size:15px;line-height:24px;color:#000;display:flex;align-items:center;font-family:Roboto}.conditions[_ngcontent-%COMP%]{font-style:normal;font-weight:400;line-height:20px;letter-spacing:.02em;color:#000;opacity:.5;margin-left:-4%}.checkbox-light[_ngcontent-%COMP%], .conditions[_ngcontent-%COMP%]{font-family:Roboto;font-size:12px}.checkbox-light[_ngcontent-%COMP%]{display:block;position:relative;cursor:pointer;font-weight:450;padding:.5rem 5rem}.checkbox-text[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;font-weight:400;font-size:12px;line-height:16px;letter-spacing:.02em;color:#5e5d5d;flex:none;order:1;flex-grow:0}.mat-checkbox-checked.mat-accent[_ngcontent-%COMP%]   .mat-checkbox-background[_ngcontent-%COMP%], .mat-checkbox-indeterminate.mat-accent[_ngcontent-%COMP%]   .mat-checkbox-background[_ngcontent-%COMP%]{background-color:#ef4a61!important}.mat-checkbox-checked.mat-accent[_ngcontent-%COMP%]   .mat-checkbox-background[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]   path[_ngcontent-%COMP%], .mat-checkbox-indeterminate.mat-accent[_ngcontent-%COMP%]   .mat-checkbox-background[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]   path[_ngcontent-%COMP%]{fill:#ef4a61!important}a[_ngcontent-%COMP%]{color:#f27a6c}a[_ngcontent-%COMP%]:hover{text-decoration:underline}.termsbuttons[_ngcontent-%COMP%]{display:flex;padding:1rem 4rem}.termsbuttons[_ngcontent-%COMP%]   .approve-previous[_ngcontent-%COMP%]{background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%)!important}.termsbuttons[_ngcontent-%COMP%]   .approve-previous[_ngcontent-%COMP%], .termsbuttons[_ngcontent-%COMP%]   .approve-previous[_ngcontent-%COMP%]:hover{border:1px solid #fff;border-radius:5px;text-transform:capitalize;color:#fff;width:160px;height:40px;display:flex;line-height:33px;padding:2px 10px;justify-content:center;font-family:Roboto;font-style:normal;font-weight:600;font-size:12px;letter-spacing:.01em;flex:none;order:0;flex-grow:0}.termsbuttons[_ngcontent-%COMP%]   .approve-previous[_ngcontent-%COMP%]:hover{background:linear-gradient(270deg,#f27a6c 105.29%)!important}.termsbuttons[_ngcontent-%COMP%]   .cancel[_ngcontent-%COMP%], .termsbuttons[_ngcontent-%COMP%]   .cancel[_ngcontent-%COMP%]:hover{margin-right:10px;border:1px solid #6b7a99;border-radius:5px;text-transform:capitalize;color:#6b7a99;height:40px;display:flex;line-height:33px;padding:3px 10px;justify-content:center;width:85px;font-family:Roboto;font-style:normal;font-weight:600;font-size:12px;letter-spacing:.01em;flex:none;order:0;flex-grow:0}.Scrollstyle[_ngcontent-%COMP%]{max-height:65vh;overflow-y:scroll}.Scrollstyle[_ngcontent-%COMP%]::-webkit-scrollbar{display:none}"]}),e})(),Ee=(()=>{class e{constructor(e,t,n,i,o,r,a){this.dialogRef=e,this._router=t,this.route=n,this.edreqService=i,this._toaster=o,this._loginService=r,this.data1=a,this.reason=new H.m({reasonForWithdrawl:new H.j("",H.H.required)}),this.subs=new j.a,this.currentDate=new Date,this.onSubmit=!1}submitWithdrawExitRequest(e){return new Promise((t,n)=>{this.subs.sink=this.edreqService.submitWithdrawExitRequest(e).subscribe(e=>{t(e.data)},e=>{this._toaster.showError("Error","Failed to submit your request!",2e3),console.log(e),n(e)})})}ngOnInit(){console.log(this.reason.value)}onNoClick(){this.dialogRef.close(!0)}withdraw(){return Object(r.c)(this,void 0,void 0,(function*(){if("VALID"===this.reason.status){let e={exit_employee_id:this.data1.emp_Id,is_withdrawn:1,withdrawn_reason:this.reason.get("reasonForWithdrawl").value,user_aid:this._loginService.getProfile().profile.aid,current_date:d()(this.currentDate).format("YYYY-MM-DD")};this.onSubmit=yield this.submitWithdrawExitRequest(e),this.dialogRef.close(),this._router.navigateByUrl(`/main/employee-central/employeeCentralDetail/${e.user_aid}/request/resignation-withdrawn`),this._toaster.showSuccess("Success","Request withdrawn Successfully !",2e3)}else this._toaster.showWarning("Info","Kindly enter the reason for withdrawl")}))}}return e.\u0275fac=function(t){return new(t||e)(p["\u0275\u0275directiveInject"](B.h),p["\u0275\u0275directiveInject"](o.g),p["\u0275\u0275directiveInject"](o.a),p["\u0275\u0275directiveInject"](f.a),p["\u0275\u0275directiveInject"](h.a),p["\u0275\u0275directiveInject"](g.a),p["\u0275\u0275directiveInject"](B.a))},e.\u0275cmp=p["\u0275\u0275defineComponent"]({type:e,selectors:[["app-resignation-withdrawl-dialogbox"]],decls:19,vars:1,consts:[[1,"welcomeback-dialogbox"],["src","https://assets.kebs.app/images/welcome_back.png","alt","Welcome Back",1,"welcomeback-image"],[1,"textindialog"],[1,"reason-for-withdrawn"],[1,"reason-header"],[3,"formGroup"],[1,"reason-input"],["type","text","placeholder","Enter here","formControlName","reasonForWithdrawl"],["mat-dialog-actions","",1,"spiderman"],["mat-button","",1,"no-btn",3,"click"],["mat-button","","tabindex","2",1,"yes-btn",3,"click"]],template:function(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"div",0),p["\u0275\u0275element"](1,"img",1),p["\u0275\u0275elementStart"](2,"div",2),p["\u0275\u0275elementStart"](3,"h5"),p["\u0275\u0275text"](4,"We Are Excited to have you back"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](5,"p"),p["\u0275\u0275text"](6,"Are you sure to withdraw the request?"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](7,"div",3),p["\u0275\u0275elementStart"](8,"div",4),p["\u0275\u0275text"](9,"Reason for Withdrawal"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](10,"form",5),p["\u0275\u0275elementStart"](11,"div",6),p["\u0275\u0275elementStart"](12,"textarea",7),p["\u0275\u0275text"](13,"        "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](14,"div",8),p["\u0275\u0275elementStart"](15,"button",9),p["\u0275\u0275listener"]("click",(function(){return t.onNoClick()})),p["\u0275\u0275text"](16,"No"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](17,"button",10),p["\u0275\u0275listener"]("click",(function(){return t.withdraw()})),p["\u0275\u0275text"](18," Yes "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e&&(p["\u0275\u0275advance"](10),p["\u0275\u0275property"]("formGroup",t.reason))},directives:[H.J,H.w,H.n,H.e,H.v,H.l,B.c],styles:[".welcomeback-dialogbox[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal}.welcomeback-dialogbox[_ngcontent-%COMP%]   .welcomeback-image[_ngcontent-%COMP%]{display:block;margin-left:8%;width:295px}.welcomeback-dialogbox[_ngcontent-%COMP%]   .spiderman[_ngcontent-%COMP%]{display:flex;margin-left:9%;gap:10%;justify-content:center;margin-top:2%;width:80%}.welcomeback-dialogbox[_ngcontent-%COMP%]   .textindialog[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal}.welcomeback-dialogbox[_ngcontent-%COMP%]   .textindialog[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{font-weight:500;font-size:18px;line-height:21px;text-align:center;text-transform:none;color:#000;flex:none;order:0;flex-grow:0}.welcomeback-dialogbox[_ngcontent-%COMP%]   .textindialog[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-weight:1000;font-size:12px;line-height:14px;text-align:center;color:#525252;flex:none;order:1;flex-grow:0}.welcomeback-dialogbox[_ngcontent-%COMP%]   .mat-dialog-actions[_ngcontent-%COMP%]{display:flex;justify-content:center;margin-top:0}.welcomeback-dialogbox[_ngcontent-%COMP%]   .mat-dialog-actions[_ngcontent-%COMP%]   .no-btn[_ngcontent-%COMP%]{border:1px solid #6b7a99!important;border-radius:5px!important;color:#6b7a99!important;height:38px!important;line-height:32px!important;padding:3px 10px!important}.welcomeback-dialogbox[_ngcontent-%COMP%]   .mat-dialog-actions[_ngcontent-%COMP%]   .no-btn[_ngcontent-%COMP%], .welcomeback-dialogbox[_ngcontent-%COMP%]   .mat-dialog-actions[_ngcontent-%COMP%]   .yes-btn[_ngcontent-%COMP%]{text-transform:none!important;display:flex!important;justify-content:center!important;width:70px!important;font-family:Roboto!important;font-style:normal!important;font-weight:500!important;font-size:14px!important;letter-spacing:.01em!important;flex:none!important;order:0!important;flex-grow:0!important}.welcomeback-dialogbox[_ngcontent-%COMP%]   .mat-dialog-actions[_ngcontent-%COMP%]   .yes-btn[_ngcontent-%COMP%]{border:1px solid #fff!important;border-radius:5px!important;color:#fff!important;background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%)!important;height:40px!important;line-height:33px!important;padding:2px 10px!important}.welcomeback-dialogbox[_ngcontent-%COMP%]   .reason-for-withdrawn[_ngcontent-%COMP%]   .reason-header[_ngcontent-%COMP%]{display:flex;justify-content:flex-start;padding:1rem 1.2rem 0;margin-left:2%;font-weight:500;font-size:14px;line-height:24px;letter-spacing:.02em;text-transform:none;color:#6e7b8f}.welcomeback-dialogbox[_ngcontent-%COMP%]   .reason-for-withdrawn[_ngcontent-%COMP%]   .reason-input[_ngcontent-%COMP%]{padding:.5rem 1rem}.welcomeback-dialogbox[_ngcontent-%COMP%]   .reason-for-withdrawn[_ngcontent-%COMP%]   .reason-input[_ngcontent-%COMP%], .welcomeback-dialogbox[_ngcontent-%COMP%]   .reason-for-withdrawn[_ngcontent-%COMP%]   .reason-input[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{display:flex;justify-content:center;font-weight:400;font-size:14px;width:100%;line-height:24px;letter-spacing:.02em;text-transform:none;color:#6e7b8f}.welcomeback-dialogbox[_ngcontent-%COMP%]   .reason-for-withdrawn[_ngcontent-%COMP%]   .reason-input[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{height:10vh;border:3px solid #b9c0ca!important;border-radius:6px;padding:.5rem;opacity:.6}"]}),e})();var Ie=n("mS9j");function ke(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"ng-contianer"),p["\u0275\u0275elementStart"](1,"div",2),p["\u0275\u0275element"](2,"mat-spinner",3),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]())}function De(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"mat-icon",25),p["\u0275\u0275text"](1,"done"),p["\u0275\u0275elementEnd"]())}function je(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"mat-icon",25),p["\u0275\u0275text"](1,"clear"),p["\u0275\u0275elementEnd"]())}const Fe=function(e,t){return{"blurData step active":e,"step active":t}},qe=function(e){return{background:e}},Re=function(e){return{"background-color":e}};function ze(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div"),p["\u0275\u0275elementStart"](1,"div",13),p["\u0275\u0275elementStart"](2,"div",14),p["\u0275\u0275elementStart"](3,"a"),p["\u0275\u0275template"](4,De,2,0,"mat-icon",15),p["\u0275\u0275template"](5,je,2,0,"mat-icon",15),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](6,"div",16),p["\u0275\u0275elementStart"](7,"div"),p["\u0275\u0275elementStart"](8,"div",17),p["\u0275\u0275elementStart"](9,"div",18),p["\u0275\u0275element"](10,"app-user-image",19),p["\u0275\u0275text"](11,"\xa0\xa0 "),p["\u0275\u0275element"](12,"app-user-profile",20),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](13,"div",21),p["\u0275\u0275text"](14,"Completed on"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](15,"div",21),p["\u0275\u0275text"](16,"Status"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](17,"div",17),p["\u0275\u0275elementStart"](18,"div",22),p["\u0275\u0275text"](19),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](20,"div",23),p["\u0275\u0275text"](21),p["\u0275\u0275pipe"](22,"date"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](23,"div",24),p["\u0275\u0275element"](24,"div",14),p["\u0275\u0275elementStart"](25,"div"),p["\u0275\u0275text"](26),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=t.index,i=p["\u0275\u0275nextContext"](3);p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngClass",p["\u0275\u0275pureFunction2"](13,Fe,i.blurFlag&&"reject"!=e.type,!i.blurFlag)),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngStyle",p["\u0275\u0275pureFunction1"](16,qe,e.color)),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngIf","reject"!=e.type),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf","reject"==e.type),p["\u0275\u0275advance"](5),p["\u0275\u0275property"]("id",e.approver_oid),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("oid",e.approver_oid),p["\u0275\u0275advance"](7),p["\u0275\u0275textInterpolate1"](" ",0==n?"Direct Manager":"Functional Head"," "),p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"](" ",e.completed_on?p["\u0275\u0275pipeBind2"](22,10,e.completed_on,"dd-MMM-YYYY"):"--"," "),p["\u0275\u0275advance"](3),p["\u0275\u0275property"]("ngStyle",p["\u0275\u0275pureFunction1"](18,Re,e.color)),p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate"](e.status)}}function Ye(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",6),p["\u0275\u0275elementStart"](1,"mat-card",7),p["\u0275\u0275elementStart"](2,"div",8),p["\u0275\u0275elementStart"](3,"div",9),p["\u0275\u0275text"](4),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](5,"div",10),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](2).close()})),p["\u0275\u0275elementStart"](6,"mat-icon",11),p["\u0275\u0275text"](7,"close"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275element"](8,"br"),p["\u0275\u0275elementStart"](9,"main"),p["\u0275\u0275template"](10,ze,27,20,"div",12),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"](2);p["\u0275\u0275advance"](4),p["\u0275\u0275textInterpolate"](e.header),p["\u0275\u0275advance"](6),p["\u0275\u0275property"]("ngForOf",null==e.approvalData?null:e.approvalData.data)}}function Ne(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"div",26),p["\u0275\u0275elementStart"](1,"div",27),p["\u0275\u0275elementStart"](2,"div",28),p["\u0275\u0275element"](3,"img",29),p["\u0275\u0275elementEnd"](),p["\u0275\u0275element"](4,"br"),p["\u0275\u0275elementStart"](5,"div",30),p["\u0275\u0275text"](6,"Approvers Not Found"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]())}function Ae(e,t){if(1&e&&(p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275template"](1,Ye,11,2,"div",4),p["\u0275\u0275template"](2,Ne,7,0,"div",5),p["\u0275\u0275elementContainerEnd"]()),2&e){const e=p["\u0275\u0275nextContext"]();p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",e.displayCondition&&(null==e.approvalData?null:e.approvalData.data.length)>0),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",e.displayCondition&&0==(null==e.approvalData?null:e.approvalData.data.length))}}let Te=(()=>{class e{constructor(e,t,n,i,o,r){this.dialogRef=e,this.route=t,this.edreqService=n,this._toaster=i,this._loginService=o,this.data1=r,this.header=["Manager Approval"],this.subs=new j.a,this.displayCondition=!1,this.currentDate=new Date,this.blurFlag=!1}ngOnInit(){var e;return Object(r.c)(this,void 0,void 0,(function*(){this.page_loader=!0,this.approvalData=yield this.fetchManagerApprovalStatus(),null===(e=this.approvalData)||void 0===e||e.data.forEach(e=>{"reject"==e.type&&(this.blurFlag=!0)}),yield this.getEmpExitID(),this.page_loader=!1}))}getEmpExitID(){this.edreqService.recieveEmpExitId().subscribe(e=>{this.empExitId=e})}fetchManagerApprovalStatus(){let e={params:{user_aid:this._loginService.getProfile().profile.aid,user_oid:this._loginService.getProfile().profile.oid,type:"manager_acceptance",exit_employee_id:this.data1.emp_Id,current_date:d()(this.currentDate).format("YYYY-MM-DD")}};return new Promise((t,n)=>{this.subs.sink=this.edreqService.fetchManagerApprovalStatus(e).subscribe(e=>{e.err?this._toaster.showWarning("Error","Something went wrong."):(this.displayCondition=!0,t(e))},e=>{this._toaster.showError("Error","Failed to retrieve Manageral Approval !",2e3),console.log(e),n(e)})})}onNoClick(){this.dialogRef.close()}close(){this.dialogRef.close()}}return e.\u0275fac=function(t){return new(t||e)(p["\u0275\u0275directiveInject"](B.h),p["\u0275\u0275directiveInject"](o.a),p["\u0275\u0275directiveInject"](f.a),p["\u0275\u0275directiveInject"](h.a),p["\u0275\u0275directiveInject"](g.a),p["\u0275\u0275directiveInject"](B.a))},e.\u0275cmp=p["\u0275\u0275defineComponent"]({type:e,selectors:[["app-manageral-approval"]],decls:3,vars:2,consts:[[1,"container-fluid","RequestForm","formpage"],[4,"ngIf"],[1,"loaDer"],["matTooltip","Please wait...","diameter","30"],["class","whole",4,"ngIf"],["class","error-message",4,"ngIf"],[1,"whole"],[2,"width","100%","height","100vh"],[1,"top-element"],[1,"header"],[1,"cancel-btn",3,"click"],[2,"font-size","20px"],[4,"ngFor","ngForOf"],[2,"height","13%","display","flex","margin-bottom","1rem",3,"ngClass"],[1,"circle",3,"ngStyle"],["style","\n                    color: #fff;\n                    font-size: 20px;\n                    margin-top: 11%;\n                    margin-left: 8%;\n                  ",4,"ngIf"],[1,"resign-req"],[1,"content","row"],[1,"head-content","col-6"],["imgWidth","28px","imgHeight","28px",3,"id"],["type","name",3,"oid"],[1,"comp-stat","col"],[1,"desc-content","col-6"],[1,"comp-date","col"],[1,"stat-approve","col"],[2,"color","#fff","font-size","20px","margin-top","11%","margin-left","8%"],[1,"error-message"],[1,"error-contents"],[1,"error-image"],["src","https://assets.kebs.app/images/no-approvers-found.png","alt","No Approvers found",2,"width","250px"],[1,"error-text"]],template:function(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"div",0),p["\u0275\u0275template"](1,ke,3,0,"ng-contianer",1),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](2,Ae,3,2,"ng-container",1)),2&e&&(p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",t.page_loader),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!t.page_loader))},directives:[i.NgIf,F.c,x.a,u.a,v.a,i.NgForOf,i.NgClass,i.NgStyle,q.a,Ie.a],pipes:[i.DatePipe],styles:[".whole[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal}.whole[_ngcontent-%COMP%]   .blurData[_ngcontent-%COMP%]{opacity:.6;cursor:not-allowed;height:13%}.whole[_ngcontent-%COMP%]   .top-element[_ngcontent-%COMP%]{display:flex;justify-content:space-between;padding:4% 2% 2%}.whole[_ngcontent-%COMP%]   .top-element[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{font-weight:500;font-size:24px;line-height:28px;letter-spacing:.02em;color:#000}.whole[_ngcontent-%COMP%]   .top-element[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{cursor:pointer;color:#03053d;font-size:15px;opacity:.7}.whole[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]{padding:6px;display:flex;flex-direction:row;width:98%}.whole[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{border-radius:100%;width:36px;height:35px;display:inline-block;display:flex;justify-content:center;align-items:center;font-weight:500;position:relative;z-index:1;margin-top:26px;left:2%}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;margin-top:1%}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .head-content[_ngcontent-%COMP%]{font-style:normal;font-weight:400;font-size:13px;line-height:15px;display:flex;align-items:center;color:#000}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .desc-content[_ngcontent-%COMP%]{display:flex;align-items:center;font-weight:400;font-size:12px;line-height:20px;letter-spacing:.02em;color:#000;opacity:.6}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .comp-stat[_ngcontent-%COMP%]{line-height:14px;opacity:.5}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .comp-date[_ngcontent-%COMP%], .whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .comp-stat[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;font-weight:400;font-size:12px;letter-spacing:.02em;text-transform:capitalize;color:#000}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .comp-date[_ngcontent-%COMP%]{line-height:16px}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .stat-approve[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;font-weight:400;font-size:12px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;color:#000;gap:6%}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .stat-approve[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{width:13px;height:13px;border-radius:50%;margin-top:0}.resign-req[_ngcontent-%COMP%]{background-color:#f3f5fc;width:100%;border-radius:5px;padding:1rem;margin-left:0}.error-message[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;font-weight:400;font-size:16px;margin-top:43px;width:50%;display:contents;letter-spacing:.02em;color:#6e7b8f;justify-content:center;text-align:center}.error-message[_ngcontent-%COMP%]   .error-contents[_ngcontent-%COMP%]{display:block;padding:15rem 13rem}.loaDer[_ngcontent-%COMP%]{height:100vh;margin-top:0!important;display:flex;justify-content:center;align-items:center}"]}),e})();function Ve(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"ng-contianer"),p["\u0275\u0275elementStart"](1,"div",2),p["\u0275\u0275element"](2,"mat-spinner",3),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]())}const We=function(e){return{background:e}},He=function(e){return{"background-color":e}};function Be(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div"),p["\u0275\u0275elementStart"](1,"div",13),p["\u0275\u0275elementStart"](2,"div",14),p["\u0275\u0275elementStart"](3,"a"),p["\u0275\u0275elementStart"](4,"mat-icon",15),p["\u0275\u0275text"](5,"done"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](6,"div",16),p["\u0275\u0275elementStart"](7,"div"),p["\u0275\u0275elementStart"](8,"div",17),p["\u0275\u0275elementStart"](9,"div",18),p["\u0275\u0275element"](10,"app-user-image",19),p["\u0275\u0275text"](11,"\xa0\xa0 "),p["\u0275\u0275element"](12,"app-user-profile",20),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](13,"div",21),p["\u0275\u0275text"](14,"Completed on"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](15,"div",21),p["\u0275\u0275text"](16,"Status"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](17,"div",17),p["\u0275\u0275elementStart"](18,"div",22),p["\u0275\u0275text"](19," ERM Manager "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](20,"div",23),p["\u0275\u0275text"](21),p["\u0275\u0275pipe"](22,"date"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](23,"div",24),p["\u0275\u0275element"](24,"div",14),p["\u0275\u0275elementStart"](25,"div"),p["\u0275\u0275text"](26),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngStyle",p["\u0275\u0275pureFunction1"](9,We,e.color)),p["\u0275\u0275advance"](8),p["\u0275\u0275property"]("id",e.approver_oid),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("oid",e.approver_oid),p["\u0275\u0275advance"](9),p["\u0275\u0275textInterpolate1"](" ",e.completed_on?p["\u0275\u0275pipeBind2"](22,6,e.completed_on,"dd-MMM-YYYY"):"--"," "),p["\u0275\u0275advance"](3),p["\u0275\u0275property"]("ngStyle",p["\u0275\u0275pureFunction1"](11,He,e.color)),p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate"](e.status)}}function Le(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",6),p["\u0275\u0275elementStart"](1,"mat-card",7),p["\u0275\u0275elementStart"](2,"div",8),p["\u0275\u0275elementStart"](3,"div",9),p["\u0275\u0275text"](4),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](5,"div",10),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](2).close()})),p["\u0275\u0275elementStart"](6,"mat-icon",11),p["\u0275\u0275text"](7,"close"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275element"](8,"br"),p["\u0275\u0275elementStart"](9,"main"),p["\u0275\u0275template"](10,Be,27,13,"div",12),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"](2);p["\u0275\u0275advance"](4),p["\u0275\u0275textInterpolate"](e.header),p["\u0275\u0275advance"](6),p["\u0275\u0275property"]("ngForOf",null==e.approvalData?null:e.approvalData.data)}}function $e(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"div",25),p["\u0275\u0275elementStart"](1,"div",26),p["\u0275\u0275elementStart"](2,"div",27),p["\u0275\u0275element"](3,"img",28),p["\u0275\u0275elementEnd"](),p["\u0275\u0275element"](4,"br"),p["\u0275\u0275elementStart"](5,"div",29),p["\u0275\u0275text"](6,"Approvers Not Found"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]())}function Ge(e,t){if(1&e&&(p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275template"](1,Le,11,2,"div",4),p["\u0275\u0275template"](2,$e,7,0,"div",5),p["\u0275\u0275elementContainerEnd"]()),2&e){const e=p["\u0275\u0275nextContext"]();p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",e.displayCondition&&(null==e.approvalData?null:e.approvalData.data.length)>0),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",e.displayCondition&&0==(null==e.approvalData?null:e.approvalData.data.length))}}let Ke=(()=>{class e{constructor(e,t,n,i,o,r){this.dialogRef=e,this.route=t,this.edreqService=n,this._toaster=i,this._loginService=o,this.data1=r,this.header=["ERM Approval"],this.subs=new j.a,this.displayCondition=!1,this.currentDate=new Date}ngOnInit(){return Object(r.c)(this,void 0,void 0,(function*(){this.page_loader=!0,this.approvalData=yield this.fetchManagerApprovalStatus(),yield this.getEmpExitID(),this.page_loader=!1}))}getEmpExitID(){this.edreqService.recieveEmpExitId().subscribe(e=>{this.empExitId=e})}fetchManagerApprovalStatus(){let e={params:{user_aid:this._loginService.getProfile().profile.aid,user_oid:this._loginService.getProfile().profile.oid,type:"erm_discussion",exit_employee_id:this.data1.emp_Id,current_date:d()(this.currentDate).format("YYYY-MM-DD")}};return new Promise((t,n)=>{this.subs.sink=this.edreqService.fetchManagerApprovalStatus(e).subscribe(e=>{e.err?this._toaster.showWarning("Error","Something went wrong."):(this.displayCondition=!0,t(e))},e=>{this._toaster.showError("Error","Failed to retrieve Manageral Approval !",2e3),console.log(e),n(e)})})}onNoClick(){this.dialogRef.close()}close(){this.dialogRef.close()}}return e.\u0275fac=function(t){return new(t||e)(p["\u0275\u0275directiveInject"](B.h),p["\u0275\u0275directiveInject"](o.a),p["\u0275\u0275directiveInject"](f.a),p["\u0275\u0275directiveInject"](h.a),p["\u0275\u0275directiveInject"](g.a),p["\u0275\u0275directiveInject"](B.a))},e.\u0275cmp=p["\u0275\u0275defineComponent"]({type:e,selectors:[["app-erm-approval"]],decls:3,vars:2,consts:[[1,"container-fluid","RequestForm","formpage"],[4,"ngIf"],[1,"loaDer"],["matTooltip","Please wait...","diameter","30"],["class","whole",4,"ngIf"],["class","error-message",4,"ngIf"],[1,"whole"],[2,"width","100%","height","100vh"],[1,"top-element"],[1,"header"],[1,"cancel-btn",3,"click"],[2,"font-size","20px"],[4,"ngFor","ngForOf"],[1,"step","active",2,"height","13%"],[1,"circle",3,"ngStyle"],[2,"color","#fff","font-size","20px","margin-top","11%","margin-left","8%"],[1,"resign-req"],[1,"content","row"],[1,"head-content","col-6"],["imgWidth","28px","imgHeight","28px",3,"id"],["type","name",3,"oid"],[1,"comp-stat","col"],[1,"desc-content","col-6"],[1,"comp-date","col"],[1,"stat-approve","col"],[1,"error-message"],[1,"error-contents"],[1,"error-image"],["src","https://assets.kebs.app/images/no-approvers-found.png","alt","No Approvers found",2,"width","250px"],[1,"error-text"]],template:function(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"div",0),p["\u0275\u0275template"](1,Ve,3,0,"ng-contianer",1),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](2,Ge,3,2,"ng-container",1)),2&e&&(p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",t.page_loader),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!t.page_loader))},directives:[i.NgIf,F.c,x.a,u.a,v.a,i.NgForOf,i.NgStyle,q.a,Ie.a],pipes:[i.DatePipe],styles:[".whole[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal}.whole[_ngcontent-%COMP%]   .top-element[_ngcontent-%COMP%]{display:flex;justify-content:space-between;padding:4% 2% 2%}.whole[_ngcontent-%COMP%]   .top-element[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{font-weight:500;font-size:24px;line-height:28px;letter-spacing:.02em;color:#000}.whole[_ngcontent-%COMP%]   .top-element[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{cursor:pointer;color:#03053d;font-size:15px;opacity:.7}.whole[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]{padding:6px;display:flex;flex-direction:row;width:98%}.whole[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{border-radius:100%;width:36px;height:35px;display:inline-block;display:flex;justify-content:center;align-items:center;font-weight:500;position:relative;z-index:1;margin:26px left left;left:2%}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;margin-top:1%}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .head-content[_ngcontent-%COMP%]{font-style:normal;font-weight:400;font-size:13px;line-height:15px;display:flex;align-items:center;color:#000}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .desc-content[_ngcontent-%COMP%]{display:flex;align-items:center;font-weight:400;font-size:12px;line-height:20px;letter-spacing:.02em;color:#000;opacity:.6}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .comp-stat[_ngcontent-%COMP%]{line-height:14px;opacity:.5}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .comp-date[_ngcontent-%COMP%], .whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .comp-stat[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;font-weight:400;font-size:12px;letter-spacing:.02em;text-transform:capitalize;color:#000}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .comp-date[_ngcontent-%COMP%]{line-height:16px}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .stat-approve[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;font-weight:400;font-size:12px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;color:#000;gap:6%}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .stat-approve[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{width:13px;height:13px;border-radius:50%;margin-top:0}.resign-req[_ngcontent-%COMP%]{background-color:#f3f5fc;width:100%;border-radius:5px;padding:1rem;margin-left:0}.error-message[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;font-weight:400;font-size:16px;margin-top:43px;width:50%;display:contents;letter-spacing:.02em;color:#6e7b8f;justify-content:center;text-align:center}.error-message[_ngcontent-%COMP%]   .error-contents[_ngcontent-%COMP%]{display:block;padding:15rem 13rem}.loaDer[_ngcontent-%COMP%]{height:100vh;margin-top:0!important;display:flex;justify-content:center;align-items:center}"]}),e})();function Je(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"ng-contianer"),p["\u0275\u0275elementStart"](1,"div",2),p["\u0275\u0275element"](2,"mat-spinner",3),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]())}const Ue=function(e){return{background:e}},Xe=function(e){return{"background-color":e}};function Qe(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div"),p["\u0275\u0275elementStart"](1,"div",13),p["\u0275\u0275elementStart"](2,"div",14),p["\u0275\u0275elementStart"](3,"a"),p["\u0275\u0275elementStart"](4,"mat-icon",15),p["\u0275\u0275text"](5,"done"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](6,"div",16),p["\u0275\u0275elementStart"](7,"div"),p["\u0275\u0275elementStart"](8,"div",17),p["\u0275\u0275elementStart"](9,"div",18),p["\u0275\u0275element"](10,"app-user-image",19),p["\u0275\u0275text"](11,"\xa0\xa0 "),p["\u0275\u0275element"](12,"app-user-profile",20),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](13,"div",21),p["\u0275\u0275text"](14,"Completed on"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](15,"div",21),p["\u0275\u0275text"](16,"Status"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](17,"div",17),p["\u0275\u0275elementStart"](18,"div",22),p["\u0275\u0275text"](19," ERM Manager "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](20,"div",23),p["\u0275\u0275text"](21),p["\u0275\u0275pipe"](22,"date"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](23,"div",24),p["\u0275\u0275element"](24,"div",14),p["\u0275\u0275elementStart"](25,"div"),p["\u0275\u0275text"](26),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngStyle",p["\u0275\u0275pureFunction1"](9,Ue,e.color)),p["\u0275\u0275advance"](8),p["\u0275\u0275property"]("id",e.approver_oid),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("oid",e.approver_oid),p["\u0275\u0275advance"](9),p["\u0275\u0275textInterpolate1"](" ",e.completed_on?p["\u0275\u0275pipeBind2"](22,6,e.completed_on,"dd-MMM-YYYY"):"--"," "),p["\u0275\u0275advance"](3),p["\u0275\u0275property"]("ngStyle",p["\u0275\u0275pureFunction1"](11,Xe,e.color)),p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate"](e.status)}}function Ze(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",6),p["\u0275\u0275elementStart"](1,"mat-card",7),p["\u0275\u0275elementStart"](2,"div",8),p["\u0275\u0275elementStart"](3,"div",9),p["\u0275\u0275text"](4),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](5,"div",10),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](2).close()})),p["\u0275\u0275elementStart"](6,"mat-icon",11),p["\u0275\u0275text"](7,"close"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275element"](8,"br"),p["\u0275\u0275elementStart"](9,"main"),p["\u0275\u0275template"](10,Qe,27,13,"div",12),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"](2);p["\u0275\u0275advance"](4),p["\u0275\u0275textInterpolate"](e.header),p["\u0275\u0275advance"](6),p["\u0275\u0275property"]("ngForOf",null==e.approvalData?null:e.approvalData.data)}}function et(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"div",25),p["\u0275\u0275elementStart"](1,"div",26),p["\u0275\u0275elementStart"](2,"div",27),p["\u0275\u0275element"](3,"img",28),p["\u0275\u0275elementEnd"](),p["\u0275\u0275element"](4,"br"),p["\u0275\u0275elementStart"](5,"div",29),p["\u0275\u0275text"](6,"Approvers Not Found"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]())}function tt(e,t){if(1&e&&(p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275template"](1,Ze,11,2,"div",4),p["\u0275\u0275template"](2,et,7,0,"div",5),p["\u0275\u0275elementContainerEnd"]()),2&e){const e=p["\u0275\u0275nextContext"]();p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",e.displayCondition&&(null==e.approvalData?null:e.approvalData.data.length)>0),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",e.displayCondition&&0==(null==e.approvalData?null:e.approvalData.data.length))}}let nt=(()=>{class e{constructor(e,t,n,i,o,r){this.dialogRef=e,this.route=t,this.edreqService=n,this._toaster=i,this._loginService=o,this.data1=r,this.header=["RMG Approval"],this.subs=new j.a,this.displayCondition=!1,this.currentDate=new Date}ngOnInit(){return Object(r.c)(this,void 0,void 0,(function*(){this.page_loader=!0,this.approvalData=yield this.fetchManagerApprovalStatus(),yield this.getEmpExitID(),this.page_loader=!1}))}getEmpExitID(){this.edreqService.recieveEmpExitId().subscribe(e=>{this.empExitId=e})}fetchManagerApprovalStatus(){let e={params:{user_aid:this._loginService.getProfile().profile.aid,user_oid:this._loginService.getProfile().profile.oid,type:"rmg_approval",exit_employee_id:this.data1.emp_Id,current_date:d()(this.currentDate).format("YYYY-MM-DD")}};return new Promise((t,n)=>{this.subs.sink=this.edreqService.fetchManagerApprovalStatus(e).subscribe(e=>{e.err?this._toaster.showWarning("Error","Something went wrong."):(this.displayCondition=!0,t(e))},e=>{this._toaster.showError("Error","Failed to retrieve Manageral Approval !",2e3),console.log(e),n(e)})})}onNoClick(){this.dialogRef.close()}close(){this.dialogRef.close()}}return e.\u0275fac=function(t){return new(t||e)(p["\u0275\u0275directiveInject"](B.h),p["\u0275\u0275directiveInject"](o.a),p["\u0275\u0275directiveInject"](f.a),p["\u0275\u0275directiveInject"](h.a),p["\u0275\u0275directiveInject"](g.a),p["\u0275\u0275directiveInject"](B.a))},e.\u0275cmp=p["\u0275\u0275defineComponent"]({type:e,selectors:[["app-rmg-approval"]],decls:3,vars:2,consts:[[1,"container-fluid","RequestForm","formpage"],[4,"ngIf"],[1,"loaDer"],["matTooltip","Please wait...","diameter","30"],["class","whole",4,"ngIf"],["class","error-message",4,"ngIf"],[1,"whole"],[2,"width","100%","height","100vh"],[1,"top-element"],[1,"header"],[1,"cancel-btn",3,"click"],[2,"font-size","20px"],[4,"ngFor","ngForOf"],[1,"step","active",2,"height","13%"],[1,"circle",3,"ngStyle"],[2,"color","#fff","font-size","20px","margin-top","11%","margin-left","8%"],[1,"resign-req"],[1,"content","row"],[1,"head-content","col-6"],["imgWidth","28px","imgHeight","28px",3,"id"],["type","name",3,"oid"],[1,"comp-stat","col"],[1,"desc-content","col-6"],[1,"comp-date","col"],[1,"stat-approve","col"],[1,"error-message"],[1,"error-contents"],[1,"error-image"],["src","https://assets.kebs.app/images/no-approvers-found.png","alt","No Approvers found",2,"width","250px"],[1,"error-text"]],template:function(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"div",0),p["\u0275\u0275template"](1,Je,3,0,"ng-contianer",1),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](2,tt,3,2,"ng-container",1)),2&e&&(p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",t.page_loader),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!t.page_loader))},directives:[i.NgIf,F.c,x.a,u.a,v.a,i.NgForOf,i.NgStyle,q.a,Ie.a],pipes:[i.DatePipe],styles:[".whole[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal}.whole[_ngcontent-%COMP%]   .top-element[_ngcontent-%COMP%]{display:flex;justify-content:space-between;padding:4% 2% 2%}.whole[_ngcontent-%COMP%]   .top-element[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{font-weight:500;font-size:24px;line-height:28px;letter-spacing:.02em;color:#000}.whole[_ngcontent-%COMP%]   .top-element[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{cursor:pointer;color:#03053d;font-size:15px;opacity:.7}.whole[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]{padding:6px;display:flex;flex-direction:row;width:98%}.whole[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{border-radius:100%;width:36px;height:35px;display:inline-block;display:flex;justify-content:center;align-items:center;font-weight:500;position:relative;z-index:1;margin:26px left left;left:2%}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;margin-top:1%}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .head-content[_ngcontent-%COMP%]{font-style:normal;font-weight:400;font-size:13px;line-height:15px;display:flex;align-items:center;color:#000}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .desc-content[_ngcontent-%COMP%]{display:flex;align-items:center;font-weight:400;font-size:12px;line-height:20px;letter-spacing:.02em;color:#000;opacity:.6}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .comp-stat[_ngcontent-%COMP%]{line-height:14px;opacity:.5}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .comp-date[_ngcontent-%COMP%], .whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .comp-stat[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;font-weight:400;font-size:12px;letter-spacing:.02em;text-transform:capitalize;color:#000}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .comp-date[_ngcontent-%COMP%]{line-height:16px}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .stat-approve[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;font-weight:400;font-size:12px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;color:#000;gap:6%}.whole[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .stat-approve[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{width:13px;height:13px;border-radius:50%;margin-top:0}.resign-req[_ngcontent-%COMP%]{background-color:#f3f5fc;width:100%;border-radius:5px;padding:1rem;margin-left:0}.error-message[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;font-weight:400;font-size:16px;margin-top:43px;width:50%;display:contents;letter-spacing:.02em;color:#6e7b8f;justify-content:center;text-align:center}.error-message[_ngcontent-%COMP%]   .error-contents[_ngcontent-%COMP%]{display:block;padding:15rem 13rem}.loaDer[_ngcontent-%COMP%]{height:100vh;margin-top:0!important;display:flex;justify-content:center;align-items:center}"]}),e})();function it(e,t){1&e&&(p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275elementStart"](1,"div",2),p["\u0275\u0275element"](2,"mat-spinner",3),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementContainerEnd"]())}function ot(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",30),p["\u0275\u0275elementStart"](1,"div",31),p["\u0275\u0275text"](2),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"]().$implicit;p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate"](e.lwd_change_reason)}}function rt(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div"),p["\u0275\u0275elementStart"](1,"div",22),p["\u0275\u0275elementStart"](2,"div",13),p["\u0275\u0275text"](3),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](4,"div",23),p["\u0275\u0275text"](5),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](6,"div",24),p["\u0275\u0275text"](7),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](8,"div",13),p["\u0275\u0275text"](9),p["\u0275\u0275pipe"](10,"date"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](11,"div",25),p["\u0275\u0275elementStart"](12,"div",26),p["\u0275\u0275elementStart"](13,"div",27),p["\u0275\u0275text"](14),p["\u0275\u0275pipe"](15,"date"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](16,"div",28),p["\u0275\u0275text"](17),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](18,ot,3,1,"div",29),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;p["\u0275\u0275advance"](3),p["\u0275\u0275textInterpolate"](e.associate_id),p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate"](e.NAME),p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate"](e.personal_email),p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"](" ",p["\u0275\u0275pipeBind2"](10,7,e.submitted_on,"dd-MMM-YYYY")," "),p["\u0275\u0275advance"](5),p["\u0275\u0275textInterpolate1"](" ",p["\u0275\u0275pipeBind2"](15,10,e.last_working_day,"dd-MMM-YYYY")," "),p["\u0275\u0275advance"](3),p["\u0275\u0275textInterpolate1"](" ",e.days_left," Days Left "),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",1==e.is_lwd_changed)}}function at(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",32),p["\u0275\u0275elementStart"](1,"div",33),p["\u0275\u0275text"](2),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate"](e)}}function lt(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"a",51),p["\u0275\u0275text"](1),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"]().index;p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate"](e+1)}}function st(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"mat-icon",52),p["\u0275\u0275text"](1,"done"),p["\u0275\u0275elementEnd"]())}function ct(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"mat-icon",52),p["\u0275\u0275text"](1,"clear"),p["\u0275\u0275elementEnd"]())}function dt(e,t){1&e&&p["\u0275\u0275element"](0,"div",53)}const pt=function(e,t){return{"background-color":e,"border-color":t,color:"white"}},mt=function(e){return{opacity:e}},gt=function(e){return{"background-color":e}};function ft(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div"),p["\u0275\u0275elementStart"](1,"div",34),p["\u0275\u0275elementStart"](2,"div",35),p["\u0275\u0275template"](3,lt,2,1,"a",36),p["\u0275\u0275template"](4,st,2,0,"mat-icon",37),p["\u0275\u0275template"](5,ct,2,0,"mat-icon",37),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](6,dt,1,0,"div",38),p["\u0275\u0275elementStart"](7,"mat-card",39),p["\u0275\u0275elementStart"](8,"div",40),p["\u0275\u0275elementStart"](9,"div",41),p["\u0275\u0275elementStart"](10,"div",42),p["\u0275\u0275text"](11),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](12,"div",43),p["\u0275\u0275text"](13,"Completed on"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](14,"div",43),p["\u0275\u0275text"](15,"Status"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](16,"div",44),p["\u0275\u0275elementStart"](17,"div",45),p["\u0275\u0275text"](18),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](19,"div",46),p["\u0275\u0275text"](20),p["\u0275\u0275pipe"](21,"date"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](22,"div",47),p["\u0275\u0275element"](23,"div",48),p["\u0275\u0275elementStart"](24,"div"),p["\u0275\u0275text"](25),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](26,"div",49),p["\u0275\u0275elementStart"](27,"mat-icon",50),p["\u0275\u0275listener"]("click",(function(){p["\u0275\u0275restoreView"](e);const n=t.$implicit;return p["\u0275\u0275nextContext"](3).nextbtn(n)})),p["\u0275\u0275text"](28," chevron_right "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=t.index,i=p["\u0275\u0275nextContext"](3);p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngStyle",p["\u0275\u0275pureFunction2"](14,pt,e.color,e.color)),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf","inprogress"==e.status_type),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf","accept"==e.status_type||"submit"==e.status_type),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf","reject"==e.status_type),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",n+1!=i.resignationSteps.length),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngStyle",p["\u0275\u0275pureFunction1"](17,mt,e.is_display_process?"none":"0.6")),p["\u0275\u0275advance"](4),p["\u0275\u0275textInterpolate1"](" ",e.process_display_name," "),p["\u0275\u0275advance"](7),p["\u0275\u0275textInterpolate1"](" ",e.process_desc," "),p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"](" ",e.completed_on?p["\u0275\u0275pipeBind2"](21,11,e.completed_on,"dd-MMM-YYYY"):"--"," "),p["\u0275\u0275advance"](3),p["\u0275\u0275property"]("ngStyle",p["\u0275\u0275pureFunction1"](19,gt,e.color)),p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate"](e.status)}}function ht(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div"),p["\u0275\u0275elementStart"](1,"div",5),p["\u0275\u0275elementStart"](2,"div",6),p["\u0275\u0275elementStart"](3,"div",7),p["\u0275\u0275element"](4,"app-user-image",8),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](5,"div",9),p["\u0275\u0275text"](6,"You Requested Resignation"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](7,"div"),p["\u0275\u0275elementStart"](8,"button",10),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](2).withdrawDialog()})),p["\u0275\u0275text"](9," Withdraw Resignation "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](10,"div",11),p["\u0275\u0275elementStart"](11,"div",12),p["\u0275\u0275elementStart"](12,"div",13),p["\u0275\u0275text"](13,"Emp ID"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](14,"div",13),p["\u0275\u0275text"](15,"Name"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](16,"div",14),p["\u0275\u0275text"](17,"Personal Email"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](18,"div",13),p["\u0275\u0275text"](19,"Submitted On"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](20,"div",14),p["\u0275\u0275text"](21,"last Working Day"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](22,rt,19,13,"div",15),p["\u0275\u0275elementEnd"](),p["\u0275\u0275element"](23,"hr"),p["\u0275\u0275elementStart"](24,"div",16),p["\u0275\u0275elementStart"](25,"div",17),p["\u0275\u0275elementStart"](26,"div",18),p["\u0275\u0275elementStart"](27,"div",19),p["\u0275\u0275text"](28,"Reason for resignation"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](29,at,3,1,"div",20),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](30,"div",21),p["\u0275\u0275elementStart"](31,"div",19),p["\u0275\u0275text"](32,"Notes"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](33,"div"),p["\u0275\u0275text"](34),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275element"](35,"hr"),p["\u0275\u0275template"](36,ft,29,21,"div",15),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"](2);p["\u0275\u0275advance"](4),p["\u0275\u0275property"]("id",e.employeeDetails.user_oid),p["\u0275\u0275advance"](18),p["\u0275\u0275property"]("ngForOf",e.employeeDetails),p["\u0275\u0275advance"](7),p["\u0275\u0275property"]("ngForOf",e.parseValue(e.resignaitonInfo[0].reason_for_resignation)),p["\u0275\u0275advance"](5),p["\u0275\u0275textInterpolate"](e.resignaitonInfo[0].notes),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngForOf",e.resignationSteps)}}function ut(e,t){if(1&e&&(p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275elementStart"](1,"mat-card",4),p["\u0275\u0275template"](2,ht,37,5,"div",1),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementContainerEnd"]()),2&e){const e=p["\u0275\u0275nextContext"]();p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngIf",e.displayCondition)}}let vt=(()=>{class e{constructor(e,t,n,i,o,r,a){this._router=e,this.route=t,this.dialog=n,this._toaster=i,this._loginService=o,this.edreqService=r,this._snackBar=a,this.subs=new j.a,this.displayCondition=!1,this.currentDate=new Date,this.grouped_process_by_id={},this.unApprovedProcess=[],this.resignaitonInfo=[]}ngOnInit(){return Object(r.c)(this,void 0,void 0,(function*(){this.page_loader=!0,[this.employeeDetails,this.resignationSteps,this.resignaitonInfo]=yield Promise.all([this.fetchEmployeeDetaisForDetailResignationRequest(),this.fetchDetailResignationSteps(),this.fetchResignationinfo()]),console.log("this.resignaitonInfo",this.resignaitonInfo),this.page_loader=!1,this.grouped_process_by_id=s.groupBy(this.resignationSteps,"id"),console.log("grouped_process_by_id",this.grouped_process_by_id)}))}fetchResignationinfo(){let e={params:{user_aid:this._loginService.getProfile().profile.aid,exit_employee_id:this.edreqService.empExId}};return new Promise((t,n)=>{this.subs.sink=this.edreqService.fetchResignationinfo(e).subscribe(e=>{e.err?this._toaster.showWarning("Error","Failed to retrive resignaiton info"):(this.displayCondition=!0,t(e.data))},e=>{this._toaster.showError("Error","Failed to retrieve Resignation Info!",2e3),console.log(e),n(e)})})}parseValue(e){return JSON.parse(e)}withdrawDialog(){this.dialog.open(Ee,{disableClose:!0,height:"495px",width:"350px",data:{emp_Id:this.employeeDetails[0].exit_employee_id}})}nextbtn(e){if(1==(null==e?void 0:e.is_display_process))if(null!=(null==e?void 0:e.enable_process_config)){let t=0;this.unApprovedProcess=[],console.log("Enable config",null==e?void 0:e.enable_process_config);for(let n of null==e?void 0:e.enable_process_config)console.log("Status",this.grouped_process_by_id[n][0].status_type),"accept"!=this.grouped_process_by_id[n][0].status_type&&(t=1,this.unApprovedProcess.push(this.grouped_process_by_id[n][0].process_name));if(t)this.openSnackBar(1==this.unApprovedProcess.length?this.unApprovedProcess+" process is not yet Approved":this.unApprovedProcess+" process are not yet Approved","Ok");else{console.log(e);const{id:t,exit_employee_id:n,type:i,status_type:o}=e;switch(i){case"manager_acceptance":this.nextManagerApproval(t,n,i);break;case"ip_out_sign":this.nextipsignup(t,n,o);break;case"handover_takeover":this.nexthandover(t,n,o);break;case"employee_exit_survey":this.nextexitsurvey(t,n);break;case"erm_discussion":this.nexterm(t,n,i);break;case"rmg_approval":this.nextrmg(t,n,i);break;case"department_clearance":this.nextdepatclearance(t,n)}}}else{console.log(e);const{id:t,exit_employee_id:n,type:i,status_type:o}=e;switch(i){case"manager_acceptance":this.nextManagerApproval(t,n,i);break;case"ip_out_sign":this.nextipsignup(t,n,o);break;case"handover_takeover":this.nexthandover(t,n,o);break;case"employee_exit_survey":this.nextexitsurvey(t,n);break;case"department_clearance":this.nextdepatclearance(t,n);break;case"erm_discussion":this.nexterm(t,n,i);break;case"rmg_approval":this.nextrmg(t,n,i)}}else this.openSnackBar("notApplicable"==e.status_type?e.process_name+" process is not Applicable":e.process_name+" process is not enabled now","Ok")}fetchEmployeeDetaisForDetailResignationRequest(){let e={params:{user_aid:this._loginService.getProfile().profile.aid,user_oid:this._loginService.getProfile().profile.oid,current_date:d()(this.currentDate).format("YYYY-MM-DD")}};return new Promise((t,n)=>{this.subs.sink=this.edreqService.fetchEmployeeDetaisForDetailResignationRequest(e).subscribe(e=>{e.err?this._toaster.showWarning("Error","Something went wrong."):(this.displayCondition=!0,t(e.data))},e=>{this._toaster.showError("Error","Failed to retrieve Employee Details !",2e3),console.log(e),n(e)})})}fetchDetailResignationSteps(){let e={params:{user_aid:this._loginService.getProfile().profile.aid,user_oid:this._loginService.getProfile().profile.oid,current_date:d()(this.currentDate).format("YYYY-MM-DD")}};return new Promise((t,n)=>{this.subs.sink=this.edreqService.fetchDetailResignationSteps(e).subscribe(e=>{e.err?this._toaster.showWarning("Error","Something went wrong."):(this.displayCondition=!0,t(e.data))},e=>{this._toaster.showError("Error","Failed to retrieve Exit Process !",2e3),console.log(e),n(e)})})}nextManagerApproval(e,t,n){this.edreqService.sendEmpExitId(t),this.dialog.open(Te,{height:"100vh",width:"50%",position:{top:"0px",right:"0px"},data:{process_Id:e,emp_Id:t,type:n}})}nexterm(e,t,n){this.edreqService.sendEmpExitId(t),console.log("ERM process is clicked"),this.dialog.open(Ke,{height:"100vh",width:"50%",position:{top:"0px",right:"0px"},data:{process_Id:e,emp_Id:t,type:n}})}nextrmg(e,t,n){this.edreqService.sendEmpExitId(t),this.dialog.open(nt,{height:"100vh",width:"50%",position:{top:"0px",right:"0px"},data:{process_Id:e,emp_Id:t,type:n}})}nextipsignup(e,t,n){this.edreqService.status_type=n,this.setEmpExIdAndProcessId(t,e),this.navigateToRoute("../ip-signout")}nexthandover(e,t,n){this.edreqService.sendEmpExitId(t),this.edreqService.status_type=n,this.setEmpExIdAndProcessId(t,e),this.navigateToRoute("../take-over")}nextexitsurvey(e,t){this.setEmpExIdAndProcessId(t,e),this.navigateToRoute("../exit-survey")}nextdepatclearance(e,t){this.setEmpExIdAndProcessId(t,e),this.navigateToRoute("../dept-clearance")}setEmpExIdAndProcessId(e,t){this.edreqService.empExId=e,this.edreqService.processId=t}navigateToRoute(e){this._router.navigate([e],{relativeTo:this.route})}openSnackBar(e,t){this._snackBar.open(e,t,{duration:2e3})}}return e.\u0275fac=function(t){return new(t||e)(p["\u0275\u0275directiveInject"](o.g),p["\u0275\u0275directiveInject"](o.a),p["\u0275\u0275directiveInject"](B.b),p["\u0275\u0275directiveInject"](h.a),p["\u0275\u0275directiveInject"](g.a),p["\u0275\u0275directiveInject"](f.a),p["\u0275\u0275directiveInject"](Q.a))},e.\u0275cmp=p["\u0275\u0275defineComponent"]({type:e,selectors:[["app-ed-detail-resignation-page"]],decls:3,vars:2,consts:[[1,"container-fluid","RequestForm","formpage"],[4,"ngIf"],[1,"d-flex","align-items-center","justify-content-center","mt-3",2,"height","77vh","margin-top","0rem !important"],["matTooltip","Please wait...","diameter","30"],[1,"resign-req","pl-1","pr-1","mat-card"],[1,"top-stat"],[1,"pro-reg"],[1,"svg-resignation"],["imgWidth","28px","imgHeight","28px",2,"margin-right","3px","overflow","hidden","text-overflow","ellipsis",3,"id"],[1,"header-request"],[1,"withdraw-btn",3,"click"],[1,"table-light"],[1,"table-content-head","row"],[1,"col-2"],[1,"col-3"],[4,"ngFor","ngForOf"],[1,"ScrollStyle"],[1,"row","reasonsBox"],[1,"col-4"],[1,"headField"],["class","reasons",4,"ngFor","ngForOf"],[1,"col-8"],[1,"table-content","row"],[1,"overflowText","col-2"],[1,"overflowText","col-3"],[1,"lat-working","col-3"],[1,"row","lst-row"],[1,"d-flex","align-items-center","col-7",2,"margin-left","-8%"],[1,"days-left","col-5"],["class","hidediv",4,"ngIf"],[1,"hidediv"],[1,"reason_title"],[1,"reasons"],[1,"reason"],[1,"step","active",2,"height","15%"],[1,"circle",3,"ngStyle"],["style","font-weight: 450;",4,"ngIf"],["class","circle_style",4,"ngIf"],["class","line",4,"ngIf"],[1,"resign-request-steps",3,"ngStyle"],[2,"display","block","width","97%","padding","0.2rem"],[1,"content","row",2,"padding-top","1%"],["mat-cell","",1,"head-content","col-8"],[1,"comp-stat","col-2"],[1,"content","row"],["mat-cell","",1,"des-cont","col-8"],[1,"comp-date","col-2"],[1,"stat-approve","col-2"],[1,"circle-stat",3,"ngStyle"],[1,"next-btn"],[3,"click"],[2,"font-weight","450"],[1,"circle_style"],[1,"line"]],template:function(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"div",0),p["\u0275\u0275template"](1,it,3,0,"ng-container",1),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](2,ut,3,1,"ng-container",1)),2&e&&(p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",t.page_loader),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!t.page_loader))},directives:[i.NgIf,F.c,x.a,u.a,q.a,i.NgForOf,i.NgStyle,v.a],pipes:[i.DatePipe],styles:[".resign-req[_ngcontent-%COMP%]{background:#fff;margin-left:2%;border-radius:6px;width:96%;display:inline-block;margin-top:1rem;height:77vh}.reasonsBox[_ngcontent-%COMP%]{font-family:Roboto;font-size:12px;line-height:14px;letter-spacing:.02em;text-align:left;padding:1rem}.headField[_ngcontent-%COMP%]{margin-bottom:1rem;opacity:.4;font-weight:400}.reasons[_ngcontent-%COMP%]{font-weight:400;text-transform:capitalize;width:250px;height:33px;line-height:31px;border-radius:18px;border:1px solid var(--primary-maroon,#ee4961);letter-spacing:.26px}.reasons[_ngcontent-%COMP%], .resign-request-steps[_ngcontent-%COMP%]{display:flex;justify-content:center}.resign-request-steps[_ngcontent-%COMP%]{background-color:#f3f5fc;align-items:center;height:15%;width:96%;margin-left:2%;border-radius:6px}.circle_style[_ngcontent-%COMP%]{align-items:center;font-size:20px;display:flex;justify-content:center}.lat-working[_ngcontent-%COMP%], .overflowText[_ngcontent-%COMP%]{flex-wrap:wrap;white-space:normal;overflow:hidden;text-overflow:ellipsis}.lat-working[_ngcontent-%COMP%]{cursor:pointer;display:flex;justify-content:center;align-items:center;column-gap:2%;position:unset}.top-stat[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;flex-direction:row;justify-content:space-between;padding:.5rem 1rem}.top-stat[_ngcontent-%COMP%], .top-stat[_ngcontent-%COMP%]   .pro-reg[_ngcontent-%COMP%]{display:flex;align-items:center;width:100%}.top-stat[_ngcontent-%COMP%]   .pro-reg[_ngcontent-%COMP%]{justify-content:flex-start;gap:1%}.top-stat[_ngcontent-%COMP%]   .svg-resignation[_ngcontent-%COMP%]{margin-left:0}.top-stat[_ngcontent-%COMP%]   .header-request[_ngcontent-%COMP%]{font-weight:500;font-size:14px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;color:#000;width:75%}.top-stat[_ngcontent-%COMP%]   .withdraw-btn[_ngcontent-%COMP%]{border:1px solid #fff;border-radius:5px;text-transform:capitalize;color:#fff;background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%)!important;width:176px;height:35px;display:flex;line-height:33px;align-items:center;justify-content:center;font-weight:500;font-size:13px;letter-spacing:.01em}.table-light[_ngcontent-%COMP%]{font-family:Roboto;font-size:12px;line-height:14px;letter-spacing:.02em;text-align:left;padding:1rem}.table-light[_ngcontent-%COMP%]   .table-content-head[_ngcontent-%COMP%]{font-weight:400;opacity:.4;padding:1px}.table-light[_ngcontent-%COMP%]   .table-content[_ngcontent-%COMP%]{font-weight:400;display:flex;justify-content:center;align-items:center;padding-top:.3rem}.table-light[_ngcontent-%COMP%]   .table-content[_ngcontent-%COMP%]   .days-left[_ngcontent-%COMP%]{font-weight:400;font-size:10px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;color:#cf0001;opacity:.7;width:120px;height:25px;margin-left:-10%;display:flex;justify-content:center;line-height:23px;border:1px solid #cf0001;border-radius:17px}hr[_ngcontent-%COMP%]{margin-top:0;opacity:.1;border:.5px solid #000}.step[_ngcontent-%COMP%]{padding:6px;display:flex;flex-direction:row;justify-content:flex-start;width:98%}.circle[_ngcontent-%COMP%]{border-radius:100%;width:38px;height:38px;display:inline-block;display:flex;justify-content:center;align-items:center;font-weight:500;background:#d7d7d7;border:2px solid #d7d7d7;position:relative;z-index:1;margin-top:26px;left:4%}.step.empty[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{visibility:hidden}.content[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;padding-top:5px;padding-bottom:5px}.content[_ngcontent-%COMP%]   .head-content[_ngcontent-%COMP%]{font-weight:535;font-size:14px;line-height:16px;display:flex;align-items:center;color:#26303e;flex-wrap:wrap;white-space:normal;overflow:hidden;text-overflow:ellipsis}.content[_ngcontent-%COMP%]   .desc-cont[_ngcontent-%COMP%]{font-size:12px;line-height:20px;opacity:.6}.content[_ngcontent-%COMP%]   .comp-stat[_ngcontent-%COMP%], .content[_ngcontent-%COMP%]   .desc-cont[_ngcontent-%COMP%]{font-weight:400;letter-spacing:.02em;color:#000;flex-wrap:wrap;white-space:normal;overflow:hidden;text-overflow:ellipsis}.content[_ngcontent-%COMP%]   .comp-stat[_ngcontent-%COMP%]{font-size:13px;opacity:.5;margin-right:-2%}.content[_ngcontent-%COMP%]   .comp-date[_ngcontent-%COMP%], .content[_ngcontent-%COMP%]   .comp-stat[_ngcontent-%COMP%]{line-height:14px;text-transform:capitalize;display:flex;justify-content:flex-start;align-items:center}.content[_ngcontent-%COMP%]   .comp-date[_ngcontent-%COMP%]{font-weight:401;font-size:12px!important;color:#000;flex-wrap:wrap;white-space:normal;overflow:hidden;text-overflow:ellipsis}.content[_ngcontent-%COMP%]   .stat-approve[_ngcontent-%COMP%]{display:flex;align-items:center;font-weight:401;font-size:12px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;gap:4%;padding-left:0;justify-content:flex-start}.content[_ngcontent-%COMP%]   .stat-approve[_ngcontent-%COMP%]   .circle-stat[_ngcontent-%COMP%]{width:11px;height:11px;border-radius:50%;margin-top:0}.next-btn[_ngcontent-%COMP%]{background:#f3f5fc;font-size:35px;opacity:.7}.next-btn[_ngcontent-%COMP%], .next-btn[_ngcontent-%COMP%]:hover{cursor:pointer;display:flex;justify-content:center;align-items:center;border-radius:100%;width:30px;height:30px;color:#a4a8ac}.next-btn[_ngcontent-%COMP%]:hover{background:#d7d7d7;opacity:1;transition:background-color .1s linear}.step[_ngcontent-%COMP%]  .mat-card:not([class*=mat-elevation-z]){box-shadow:none!important}.ScrollStyle[_ngcontent-%COMP%]{height:53vh;overflow-y:scroll}.hidediv[_ngcontent-%COMP%]{display:none}.hidediv[_ngcontent-%COMP%], .lst-row[_ngcontent-%COMP%]:hover + .hidediv[_ngcontent-%COMP%]{height:100px;border:1px solid #d3d3d3;border-radius:4px;z-index:1;position:absolute;top:24%;left:75%;width:20%;background-color:#fff}.lst-row[_ngcontent-%COMP%]:hover + .hidediv[_ngcontent-%COMP%]{display:flex;visibility:visible}.reason_title[_ngcontent-%COMP%]{height:-moz-fit-content;height:fit-content;padding:10px;margin:10px;font-weight:800;color:#2799ee;background-color:#e8f6fb}"]}),e})();var xt=n("1n7x"),_t=n("jhN1"),Ct=n("Rubt");function yt(e,t){1&e&&(p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275elementStart"](1,"div",2),p["\u0275\u0275element"](2,"mat-spinner",3),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementContainerEnd"]())}function bt(e,t){if(1&e&&(p["\u0275\u0275element"](0,"div",28),p["\u0275\u0275pipe"](1,"safeHtml")),2&e){const e=p["\u0275\u0275nextContext"](3);p["\u0275\u0275property"]("innerHTML",p["\u0275\u0275pipeBind1"](1,1,e.editTemplate),p["\u0275\u0275sanitizeHtml"])}}function Mt(e,t){if(1&e&&p["\u0275\u0275element"](0,"div",28),2&e){const e=p["\u0275\u0275nextContext"](3);p["\u0275\u0275property"]("innerHTML",e.dynamicContent,p["\u0275\u0275sanitizeHtml"])}}function St(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",26),p["\u0275\u0275template"](1,bt,2,3,"div",27),p["\u0275\u0275template"](2,Mt,1,1,"div",27),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](2);p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf","accept"!=e.isIpFormApproved),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf","accept"==e.isIpFormApproved)}}function Pt(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"span"),p["\u0275\u0275element"](1,"mat-spinner",29),p["\u0275\u0275elementEnd"]())}function Ot(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"span"),p["\u0275\u0275text"](1," Submit Resignation "),p["\u0275\u0275elementEnd"]())}const wt=function(e){return{"pointer-events":e}};function Et(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275elementStart"](1,"div"),p["\u0275\u0275elementStart"](2,"mat-card",4),p["\u0275\u0275elementStart"](3,"div",5),p["\u0275\u0275elementStart"](4,"div",6),p["\u0275\u0275elementStart"](5,"div",7),p["\u0275\u0275elementStart"](6,"h2",8),p["\u0275\u0275text"](7," UNDERTAKING "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](8,"h4",9),p["\u0275\u0275text"](9," TO WHOMSOEVER IT MAY CONCERN "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](10,St,3,2,"div",10),p["\u0275\u0275elementEnd"](),p["\u0275\u0275element"](11,"br"),p["\u0275\u0275elementStart"](12,"div",11),p["\u0275\u0275elementStart"](13,"div",12),p["\u0275\u0275elementStart"](14,"div",13),p["\u0275\u0275text"](15,"Signed at Chennai on this :"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](16,"div",14),p["\u0275\u0275text"](17),p["\u0275\u0275pipe"](18,"date"),p["\u0275\u0275pipe"](19,"date"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](20,"div",15),p["\u0275\u0275elementStart"](21,"div",16),p["\u0275\u0275text"](22,"Name of the Employee :"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](23,"div",17),p["\u0275\u0275text"](24),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275element"](25,"hr",18),p["\u0275\u0275element"](26,"br"),p["\u0275\u0275elementStart"](27,"div",19),p["\u0275\u0275elementStart"](28,"section",20),p["\u0275\u0275elementStart"](29,"mat-checkbox",21),p["\u0275\u0275listener"]("ngModelChange",(function(t){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"]().isChecked=t})),p["\u0275\u0275text"](30," I have read and understood the IP Signout "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](31,"div",22),p["\u0275\u0275elementStart"](32,"button",23),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"]().generatePdf()})),p["\u0275\u0275elementStart"](33,"mat-icon",24),p["\u0275\u0275text"](34,"download"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275text"](35,"Download "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](36,"button",25),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"]().ipsubmit()})),p["\u0275\u0275template"](37,Pt,2,0,"span",1),p["\u0275\u0275template"](38,Ot,2,0,"span",1),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"]();p["\u0275\u0275advance"](10),p["\u0275\u0275property"]("ngForOf",e.termsAndConditions),p["\u0275\u0275advance"](7),p["\u0275\u0275textInterpolate1"](" ",null!=e.termsAndConditions[0]&&e.termsAndConditions[0].created_on?p["\u0275\u0275pipeBind2"](18,7,null==e.termsAndConditions[0]?null:e.termsAndConditions[0].created_on,"dd-MMM-YYYY"):p["\u0275\u0275pipeBind2"](19,10,e.currentDate,"dd-MMM-YYYY")," "),p["\u0275\u0275advance"](7),p["\u0275\u0275textInterpolate1"](" ",e.employeeName[0].NAME," "),p["\u0275\u0275advance"](5),p["\u0275\u0275property"]("ngModel",e.isChecked)("ngStyle",p["\u0275\u0275pureFunction1"](13,wt,e.edit?"none":"auto")),p["\u0275\u0275advance"](8),p["\u0275\u0275property"]("ngIf",e.pleasewait_loader),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!e.pleasewait_loader)}}let It=(()=>{class e{constructor(e,t,n,i,o,r,a){this._router=e,this.route=t,this._snackBar=n,this.edreqService=i,this._toaster=o,this._loginService=r,this.sanitizer=a,this.isChecked=!1,this.currentDate=new Date,this.onSubmitIpSignOut=!1,this.inputFields={},this.inputFieldsWithValue={},this.name="",this.ipForm=new H.m({name:new H.j("")}),this.subs=new j.a,this.displayCondition=!1,this.edit=!1,this.pleasewait_loader=!1,this.disableSubmit=!1,this.empData=[]}ngOnInit(){return Object(r.c)(this,void 0,void 0,(function*(){this.isIpFormApproved=this.edreqService.status_type,console.log("isIpFormApproved Status",this.isIpFormApproved),this.page_loader=!0,this.termsAndConditions=yield this.fetchIpSignOutForm(),console.log("Responce",this.termsAndConditions),this.employeeName=yield this.fetchIpSignOutFormEmpDetails(),this.edreqService.recieveEmpExitId().subscribe(e=>{this.empExId=e}),console.log("Exit emp id",this.empExId,this.edreqService.empExId),this.empData=yield this.fetchEmployeeDetaisForDetailResignationRequest(),console.log("empData",this.empData),yield this.getEmpExitID(),yield this.getProcessId();const e=["name","designation_name","age"];if(this.edit){let e=JSON.parse(this.termsAndConditions[0].form_values),t=this.termsAndConditions[0].ip_out_sign_form;for(let n in e){const i=new RegExp("\\$\\{"+n+"\\}","g");t=t.replace(i,e[n])}this.editTemplate=`<div>${t}</div>`,console.log(t)}else{let t=this.termsAndConditions[0].base_form;for(let i of e){console.log("item",i);const e=new RegExp("\\$\\{"+i+"\\}","g");t=t.replace(e,"name"==i?this.empData[0].NAME:"designation_name"==i?this.empData[0].designation:"age"==i?this.empData[0].age:"")}this.editTemplate=`\n      <div>\n        ${t}\n      </div>\n    `;const n=document.createElement("div");n.innerHTML=this.editTemplate,n.querySelectorAll("input").forEach(e=>{const t=e.id;this.inputFields[t]="name"==t?this.empData[0].NAME:"designation_name"==t?this.empData[0].designation:"age"==t?this.empData[0].age:"",console.log("Id:",t)}),console.log("Input Fields",this.inputFields)}const t=document.createElement("div");t.innerHTML=this.editTemplate,t.querySelectorAll("input").forEach(e=>{const t=e.id;this.inputFields[t]="",console.log("Id:",t)}),console.log("Input Fields",this.inputFields),this.page_loader=!1}))}fetchIpSignOutForm(){let e={params:{user_aid:this._loginService.getProfile().profile.aid,user_oid:this._loginService.getProfile().profile.oid,exit_process_id:this.edreqService.processId,exit_employee_id:this.edreqService.empExId}};return new Promise((t,n)=>{this.subs.sink=this.edreqService.fetchIpSignOutForm(e).subscribe(e=>{e.err?this._toaster.showWarning("Error","Something went wrong."):(this.displayCondition=!0,this.edit=1==e.form_filled,this.isChecked=1==e.form_filled,t(e.data))},e=>{this._toaster.showError("Error","Failed to retrieve IP Signout Form !",2e3),console.log(e),n(e)})})}fetchIpSignOutFormEmpDetails(){let e={params:{user_aid:this._loginService.getProfile().profile.aid,user_oid:this._loginService.getProfile().profile.oid,current_date:d()(this.currentDate).format("YYYY-MM-DD")}};return new Promise((t,n)=>{this.subs.sink=this.edreqService.fetchIpSignOutFormEmpDetails(e).subscribe(e=>{e.err?this._toaster.showWarning("Error","Something went wrong."):(this.displayCondition=!0,t(e.data))},e=>{this._toaster.showError("Error","Failed to retrieve employee name !",2e3),console.log(e),n(e)})})}submitIPSignOutForm(e){return new Promise((t,n)=>{this.subs.sink=this.edreqService.submitIPSignOutForm(e).subscribe(e=>{t(e.data)},e=>{this._toaster.showError("Error","Failed to submit your IP SignOut Form!",2e3),console.log(e),n(e)})})}getEmpExitID(){this.edreqService.recieveEmpExitId().subscribe(e=>{this.empExitId=e})}getProcessId(){this.edreqService.getProcessId().subscribe(e=>{this.processId=e})}ipsubmit(){return Object(r.c)(this,void 0,void 0,(function*(){if(this.disableSubmit||"accept"==this.isIpFormApproved)this._toaster.showWarning("Submission denied","IP Sign out form is already approved by Manager");else{let e={},t=!1;console.log("inputFields",this.inputFields);for(let n in this.inputFields){if(this.inputFieldsWithValue[n]=document.getElementById(n).value,this.inputFieldsWithValue[n].length>50){t=!0,this._toaster.showWarning("Enter less than 50 characters",`Maximum character exceeded in ${n.toUpperCase()} field`);break}if(0==this.inputFieldsWithValue[n].length){t=!0,this._toaster.showWarning("Enter the value",`In ${n.toUpperCase()} field`);break}}if(!t){console.log("InputFields",this.inputFields),console.log("InputFieldsWithValue",this.inputFieldsWithValue),e.ip_out_sign_form=this.termsAndConditions[0].base_edit_form?this.termsAndConditions[0].base_edit_form:this.termsAndConditions[0].ip_out_sign_form,e.employee_acknowledgement=this.isChecked?1:0,e.exit_employee_id=this.edreqService.empExId,e.exit_process_id=this.edreqService.processId,e.current_date=d()(this.currentDate).format("YYYY-MM-DD"),e.form_values=this.inputFieldsWithValue;const t=this.inputFieldsWithValue.doj.split("/");if(3==t.length){const n=parseInt(t[1],10),i=parseInt(t[0],10)-1,o=parseInt(t[2],10),r=new Date(o,i,n);isNaN(r.getDate())||r.getFullYear()!==o||r.getMonth()!==i||r.getDate()!==n||4!=t[2].length?this._toaster.showWarning("Invalid date In DOJ field","Enter date in (mm/dd/yyyy) format"):(console.log("Valid date:",r),this.isChecked?(this.disableSubmit=!0,this.pleasewait_loader=!0,this.onSubmitIpSignOut=yield this.submitIPSignOutForm(e),this.pleasewait_loader=!1,this.disableSubmit=!1,this._toaster.showSuccess("Success","IP Sign Out Form submitted successfully",2e3),this._router.navigate(["../ip-signout-status"],{relativeTo:this.route})):this.openSnackBar("Accept the IP Conditions","Ok"))}else this._toaster.showWarning("Invalid date In DOJ field","Enter date in (mm/dd/yyyy) format")}}}))}fetchEmployeeDetaisForDetailResignationRequest(){let e={params:{user_aid:this._loginService.getProfile().profile.aid,user_oid:this._loginService.getProfile().profile.oid,current_date:d()(this.currentDate).format("YYYY-MM-DD")}};return new Promise((t,n)=>{this.subs.sink=this.edreqService.fetchEmployeeDetaisForDetailResignationRequest(e).subscribe(e=>{e.err?this._toaster.showWarning("Error","Something went wrong."):(this.displayCondition=!0,t(e.data))},e=>{this._toaster.showError("Error","Failed to retrieve Employee Details !",2e3),console.log(e),n(e)})})}openSnackBar(e,t){this._snackBar.open(e,t,{duration:3e3})}get dynamicContent(){let e=JSON.parse(this.termsAndConditions[0].form_values),t=this.termsAndConditions[0].ip_out_sign_form;for(let n in e){const i=new RegExp("\\$\\{"+n+"\\}","g");t=t.replace(i,e[n])}return this.editTemplate=`<div>${t}</div>`,console.log(t),this.sanitizer.bypassSecurityTrustHtml(t)}generatePdf(){if(this.edit){var e=document.getElementById("Ip-form");xt().from(e).set({filename:"Ip-Form.pdf",margin:[.3,.3,.3,.3],image:{type:"jpeg",quality:.98},jsPDF:{unit:"in",format:"a4",orientation:"landscape"}}).save()}else this._toaster.showWarning("Warning","Submit the form to download")}onTabKey(e){e.preventDefault()}}return e.\u0275fac=function(t){return new(t||e)(p["\u0275\u0275directiveInject"](o.g),p["\u0275\u0275directiveInject"](o.a),p["\u0275\u0275directiveInject"](Q.a),p["\u0275\u0275directiveInject"](f.a),p["\u0275\u0275directiveInject"](h.a),p["\u0275\u0275directiveInject"](g.a),p["\u0275\u0275directiveInject"](_t.c))},e.\u0275cmp=p["\u0275\u0275defineComponent"]({type:e,selectors:[["app-ip-signout-form"]],decls:3,vars:2,consts:[[1,"container-fluid","RequestForm","formpage"],[4,"ngIf"],[1,"d-flex","align-items-center","justify-content-center","mt-3",2,"height","77vh","margin-top","0rem !important"],["matTooltip","Please wait...","diameter","30"],[1,"pl-1","pr-1","mat-card","slide-from-down",2,"margin-left","3%","width","94%","margin-top","2vh","height","77vh"],[1,"ip-singout"],["id","Ip-form"],[1,"ip-docs"],[1,"header-text-style","d-flex","justify-content-center","mt-3"],[1,"sub-header-text-style","d-flex","justify-content-center","mt-3"],["class","conditions",4,"ngFor","ngForOf"],[1,"empdetails","row"],[1,"col-6",2,"display","flex"],[1,"date","col-6"],[1,"date-ts",2,"margin-left","-20px"],[1,"col-5",2,"display","flex"],[1,"empname","col-7"],[1,"empname-ts",2,"margin-left","-25px"],[2,"color","#d4d6d8"],[1,"bottomRow"],[1,"ip-checkbox"],[3,"ngModel","ngStyle","ngModelChange"],[2,"display","flex"],[1,"ip-download",3,"click"],[1,"download-icon"],[1,"ip-submit",3,"click"],[1,"conditions"],["class","one",3,"innerHTML",4,"ngIf"],[1,"one",3,"innerHTML"],["matTooltip","Your request is being submitted!!","diameter","25"]],template:function(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"div",0),p["\u0275\u0275template"](1,yt,3,0,"ng-container",1),p["\u0275\u0275template"](2,Et,39,15,"ng-container",1),p["\u0275\u0275elementEnd"]()),2&e&&(p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",t.page_loader),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!t.page_loader))},directives:[i.NgIf,F.c,x.a,u.a,i.NgForOf,be.a,H.v,H.y,i.NgStyle,v.a],pipes:[i.DatePipe,Ct.a],styles:[".ip-singout[_ngcontent-%COMP%]{font-family:Roboto;max-height:75vh;overflow-y:scroll}.ip-singout[_ngcontent-%COMP%]   .header-text-style[_ngcontent-%COMP%]{font-weight:500;font-size:18px;line-height:21px;letter-spacing:.02em;text-transform:capitalize;color:#7d838b}.ip-singout[_ngcontent-%COMP%]   .sub-header-text-style[_ngcontent-%COMP%]{font-weight:500;font-size:14px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;color:#515965}.ip-singout[_ngcontent-%COMP%]   .ip-docs[_ngcontent-%COMP%]{margin-top:0;margin-left:1%;margin-right:3%}.ip-singout[_ngcontent-%COMP%]   .conditions[_ngcontent-%COMP%]{font-weight:400;font-size:14px;line-height:22px;text-align:justify;letter-spacing:.02em;color:#000}.ip-singout[_ngcontent-%COMP%]   .conditions[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .ip-singout[_ngcontent-%COMP%]   .conditions[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{background-color:#f6f6f6!important;border-color:#f6f6f6!important}.ip-singout[_ngcontent-%COMP%]   .ip-consents[_ngcontent-%COMP%]{font-style:normal;font-weight:400;font-size:12px;line-height:20px;letter-spacing:.02em;color:#000;opacity:.5}.ip-singout[_ngcontent-%COMP%]   .ip-checkbox[_ngcontent-%COMP%]{display:block;position:relative;margin-bottom:12px;cursor:pointer;margin-left:41px;color:#5e5d5d}.ip-singout[_ngcontent-%COMP%]   .ip-checkbox[_ngcontent-%COMP%], .ip-singout[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{font-size:12px;font-weight:400;line-height:16px;letter-spacing:.02em;flex:none;flex-grow:0}.ip-singout[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{-webkit-text-decoration-line:underline;text-decoration-line:underline;text-transform:capitalize;color:#f27a6c;order:2}.ip-singout[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{text-decoration:underline}.ip-singout[_ngcontent-%COMP%]   .empdetails[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;margin-left:40px!important;display:flex}.ip-singout[_ngcontent-%COMP%]   .empdetails[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%], .ip-singout[_ngcontent-%COMP%]   .empdetails[_ngcontent-%COMP%]   .empname[_ngcontent-%COMP%]{font-weight:400;font-size:14px;line-height:20px;letter-spacing:.02em;color:#000}.ip-singout[_ngcontent-%COMP%]   .empdetails[_ngcontent-%COMP%]   .date-ts[_ngcontent-%COMP%], .ip-singout[_ngcontent-%COMP%]   .empdetails[_ngcontent-%COMP%]   .empname-ts[_ngcontent-%COMP%]{display:flex;text-align:center;justify-content:flex-start;font-style:normal;font-weight:500;font-size:16px;line-height:20px;letter-spacing:.02em;color:#000}.ip-singout[_ngcontent-%COMP%]   .bottomRow[_ngcontent-%COMP%]{display:flex;margin:10px;padding:10px;justify-content:space-between}.ip-singout[_ngcontent-%COMP%]   .bottomRow[_ngcontent-%COMP%]   .ip-download[_ngcontent-%COMP%]{border:1px solid #45546e;border-radius:5px;color:#45546e;background:#fff!important;margin-right:4px}.ip-singout[_ngcontent-%COMP%]   .bottomRow[_ngcontent-%COMP%]   .ip-download[_ngcontent-%COMP%], .ip-singout[_ngcontent-%COMP%]   .bottomRow[_ngcontent-%COMP%]   .ip-submit[_ngcontent-%COMP%]{position:relative;overflow:hidden;text-transform:capitalize;width:150px;height:40px;display:flex;line-height:12px;font-size:12px;font-weight:600;padding:2px 10px;justify-content:center;align-items:center}.ip-singout[_ngcontent-%COMP%]   .bottomRow[_ngcontent-%COMP%]   .ip-submit[_ngcontent-%COMP%], .ip-singout[_ngcontent-%COMP%]   .bottomRow[_ngcontent-%COMP%]   .ip-submit[_ngcontent-%COMP%]:hover{border:1px solid #fff;border-radius:5px;color:#fff;background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%)!important}.ip-singout[_ngcontent-%COMP%]   .bottomRow[_ngcontent-%COMP%]   .ip-submit[_ngcontent-%COMP%]:hover{text-transform:capitalize;height:40px;display:flex;line-height:12px;font-size:12px;font-weight:600;padding:2px 10px;justify-content:center;align-items:center}.ip-singout[_ngcontent-%COMP%]   .bottomRow[_ngcontent-%COMP%]   .ip-submit[_ngcontent-%COMP%]   .loader[_ngcontent-%COMP%]{position:absolute;display:flex;justify-content:center;transform:translate(-50%,-50%);display:none;width:20px;height:20px;border:2px solid #fff;border-top-color:#000;border-radius:50%;animation:loader-rotate 1s linear infinite}@keyframes loader-rotate{0%{transform:translate(-50%,-50%) rotate(0deg)}to{transform:translate(-50%,-50%) rotate(1turn)}}.ip-singout[_ngcontent-%COMP%]   .bottomRow[_ngcontent-%COMP%]   .ip-submit.loading[_ngcontent-%COMP%]   .loader[_ngcontent-%COMP%]{display:block}.ip-singout[_ngcontent-%COMP%]   .bottomRow[_ngcontent-%COMP%]   .ip-submit.loading[_ngcontent-%COMP%]{pointer-events:none;opacity:.7}.ip-singout[_ngcontent-%COMP%]   .bottomRow[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   .ip-submit[_ngcontent-%COMP%]:disabled{opacity:.5}.mat-spinner[_ngcontent-%COMP%]   circle[_ngcontent-%COMP%],   .ip-submit .mat-progress-spinner circle{stroke:#fff}.ScrollStyle[_ngcontent-%COMP%]{max-height:77vh;overflow-y:scroll}"]}),e})();function kt(e,t){1&e&&(p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275elementStart"](1,"div",2),p["\u0275\u0275element"](2,"mat-spinner",3),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementContainerEnd"]())}function Dt(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"span",18),p["\u0275\u0275text"](1," \xa0*"),p["\u0275\u0275elementEnd"]())}function jt(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",23),p["\u0275\u0275elementContainerStart"](1),p["\u0275\u0275elementStart"](2,"input",24),p["\u0275\u0275listener"]("change",(function(){p["\u0275\u0275restoreView"](e);const t=p["\u0275\u0275nextContext"]().index,n=p["\u0275\u0275nextContext"]().index,i=p["\u0275\u0275nextContext"]().index;return p["\u0275\u0275nextContext"](3).changeSingleSelect(i,n,t)})),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](3,"label",25),p["\u0275\u0275text"](4),p["\u0275\u0275elementEnd"](),p["\u0275\u0275element"](5,"br"),p["\u0275\u0275elementContainerEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"](),t=e.index,n=e.$implicit,i=p["\u0275\u0275nextContext"]().index,o=p["\u0275\u0275nextContext"]().index,r=p["\u0275\u0275nextContext"](3);p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("checked",r.params.exitSurveySection[o].lables[i].options[t].isChecked),p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate"](r.getFormControlValue(n,"checkbox_name"))}}function Ft(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",26),p["\u0275\u0275elementContainerStart"](1),p["\u0275\u0275elementStart"](2,"input",27),p["\u0275\u0275listener"]("change",(function(){p["\u0275\u0275restoreView"](e);const t=p["\u0275\u0275nextContext"]().index,n=p["\u0275\u0275nextContext"]().index,i=p["\u0275\u0275nextContext"]().index;return p["\u0275\u0275nextContext"](3).changeMultipleSelect(i,n,t)})),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](3,"label",28),p["\u0275\u0275text"](4),p["\u0275\u0275elementEnd"](),p["\u0275\u0275element"](5,"br"),p["\u0275\u0275elementContainerEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"]().$implicit,t=p["\u0275\u0275nextContext"](5);p["\u0275\u0275advance"](4),p["\u0275\u0275textInterpolate"](t.getFormControlValue(e,"checkbox_name"))}}function qt(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275elementStart"](1,"input",29),p["\u0275\u0275listener"]("change",(function(t){p["\u0275\u0275restoreView"](e);const n=p["\u0275\u0275nextContext"]().index,i=p["\u0275\u0275nextContext"]().index,o=p["\u0275\u0275nextContext"]().index;return p["\u0275\u0275nextContext"](3).changeTextSelect(o,i,n,t)})),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementContainerEnd"]()}}function Rt(e,t){if(1&e&&(p["\u0275\u0275elementContainerStart"](0,19),p["\u0275\u0275template"](1,jt,6,2,"div",20),p["\u0275\u0275template"](2,Ft,6,1,"div",21),p["\u0275\u0275elementStart"](3,"div",22),p["\u0275\u0275template"](4,qt,2,0,"ng-container",1),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit,n=t.index,i=p["\u0275\u0275nextContext"](5);p["\u0275\u0275property"]("formGroupName",n),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf","checkbox_single"==i.getFormControlValue(e,"field_type")),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf","checkbox_multiple"==i.getFormControlValue(e,"field_type")),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngIf","freeTextWithLabel"==i.getFormControlValue(e,"field_type"))}}function zt(e,t){if(1&e&&(p["\u0275\u0275elementContainerStart"](0,11),p["\u0275\u0275elementStart"](1,"mat-card",13),p["\u0275\u0275elementStart"](2,"div",14),p["\u0275\u0275text"](3),p["\u0275\u0275template"](4,Dt,2,0,"span",15),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](5,"div"),p["\u0275\u0275elementStart"](6,"div",16),p["\u0275\u0275template"](7,Rt,5,4,"ng-container",17),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit,n=t.index,i=p["\u0275\u0275nextContext"]().index,o=p["\u0275\u0275nextContext"](3);p["\u0275\u0275property"]("formGroupName",n),p["\u0275\u0275advance"](3),p["\u0275\u0275textInterpolate1"](" ",o.getFormControlValue(e,"lable_name")," "),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",1==o.params.exitSurveySection[i].lables[n].is_mandatory),p["\u0275\u0275advance"](3),p["\u0275\u0275property"]("ngForOf",o.getOptionFormArray(i,n).controls)}}function Yt(e,t){if(1&e&&(p["\u0275\u0275elementContainerStart"](0,11),p["\u0275\u0275elementContainerStart"](1,12),p["\u0275\u0275template"](2,zt,8,4,"ng-container",8),p["\u0275\u0275elementContainerEnd"](),p["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.index,n=p["\u0275\u0275nextContext"](3);p["\u0275\u0275property"]("formGroupName",e),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngForOf",n.getLableFormArray(e).controls)}}function Nt(e,t){1&e&&(p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275element"](1,"mat-spinner",30),p["\u0275\u0275elementContainerEnd"]())}function At(e,t){1&e&&(p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275elementStart"](1,"mat-icon",31),p["\u0275\u0275text"](2,"done_all"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275text"](3,"Submit "),p["\u0275\u0275elementContainerEnd"]())}const Tt=function(e){return{"pointer-events":e}};function Vt(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div"),p["\u0275\u0275elementStart"](1,"mat-card",4),p["\u0275\u0275elementStart"](2,"div",5),p["\u0275\u0275text"](3,"Exit Survey"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](4,"form",6),p["\u0275\u0275elementContainerStart"](5,7),p["\u0275\u0275template"](6,Yt,3,2,"ng-container",8),p["\u0275\u0275elementContainerEnd"](),p["\u0275\u0275elementStart"](7,"div",9),p["\u0275\u0275elementStart"](8,"button",10),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](2).submitBtn()})),p["\u0275\u0275template"](9,Nt,2,0,"ng-container",1),p["\u0275\u0275template"](10,At,4,0,"ng-container",1),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"](2);p["\u0275\u0275advance"](4),p["\u0275\u0275property"]("formGroup",e.exitSurveyForm)("ngStyle",p["\u0275\u0275pureFunction1"](6,Tt,e.submitFlag||e.submit_loader?"none":"auto")),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngForOf",e.getSectionFormArray.controls),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("disabled",e.submitFlag||e.submit_loader),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",e.submit_loader),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!e.submit_loader)}}function Wt(e,t){if(1&e&&(p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275template"](1,Vt,11,8,"div",1),p["\u0275\u0275elementContainerEnd"]()),2&e){const e=p["\u0275\u0275nextContext"]();p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",e.displayCondition)}}let Ht=(()=>{class e{constructor(e,t,n,i,o,r,a){this._router=e,this.route=t,this._snackBar=n,this.edreqService=i,this._toaster=o,this._loginService=r,this.fb=a,this.subs=new j.a,this.displayCondition=!1,this.currentDate=new Date,this.exitSurvey=[],this.submitFlag=!0,this.optionsDisableFlag=!0,this.exitResponse=[],this.exitSurveyForm=this.fb.group({exitSurveySection:this.fb.array([])}),this.submit_loader=!1,this.mandt_lables=[],this.selected_lables=[]}ngOnInit(){return Object(r.c)(this,void 0,void 0,(function*(){this.page_loader=!0,this.exitResponse=yield this.viewFilledExitSurvey(),0==this.exitResponse.length&&(this.submitFlag=!1,this.optionsDisableFlag=!1,this.exitResponse=yield this.fetchEmployeeExitSurvey()),yield this.generateExitForm(),this.params=this.exitSurveyForm.value,this.page_loader=!1}))}get getSectionFormArray(){return this.exitSurveyForm.get("exitSurveySection")}getLableFormArray(e){return this.exitSurveyForm.get("exitSurveySection").at(e).get("lables")}getOptionFormArray(e,t){return this.exitSurveyForm.get("exitSurveySection").at(e).get("lables").at(t).get("options")}getFormControlValue(e,t){return e.get(t).value}changeSingleSelect(e,t,n){1==this.params.exitSurveySection[e].lables[t].options[n].isChecked?(this.params.exitSurveySection[e].lables[t].options[n].isChecked=0,this.selected_lables=this.selected_lables.filter(n=>n!==this.params.exitSurveySection[e].lables[t].lable_id)):(this.params.exitSurveySection[e].lables[t].options[n].isChecked=1,this.selected_lables.push(this.params.exitSurveySection[e].lables[t].lable_id),console.log("Default mandatory lable ids",this.mandt_lables),console.log("Selected lable ids",this.selected_lables));for(let i=0;i<this.params.exitSurveySection[e].lables[t].options.length;i++)i!=n&&(this.params.exitSurveySection[e].lables[t].options[i].isChecked=0)}changeMultipleSelect(e,t,n){if(1==this.params.exitSurveySection[e].lables[t].options[n].isChecked){this.params.exitSurveySection[e].lables[t].options[n].isChecked=0;let i=this.selected_lables.indexOf(this.params.exitSurveySection[e].lables[t].lable_id);-1!==i&&this.selected_lables.splice(i,1)}else this.params.exitSurveySection[e].lables[t].options[n].isChecked=1,this.selected_lables.push(this.params.exitSurveySection[e].lables[t].lable_id),console.log("Default mandatory lable ids",this.mandt_lables),console.log("Selected lable ids",this.selected_lables)}changeTextSelect(e,t,n,i){let o=i.target.value;console.log("Text value",o),this.params.exitSurveySection[e].lables[t].options[n].isChecked=1,this.params.exitSurveySection[e].lables[t].options[n].textbox_lable_name=o,this.selected_lables.push(this.params.exitSurveySection[e].lables[t].lable_id),console.log("Default mandatory lable ids",this.mandt_lables),console.log("Selected lable ids",this.selected_lables)}generateExitForm(){let e=this.exitResponse.map(e=>this.createSection(e));this.exitSurveyForm.setControl("exitSurveySection",this.fb.array(e))}createSection(e){return this.fb.group({section_id:[e.section_id,H.H.required],section_name:[e.section_name,H.H.required],section_description:[e.section_description,H.H.required],lables:this.fb.array(this.loadLablesArray(e.lables))})}loadLablesArray(e){return e.map(e=>this.createLable(e))}createLable(e){return 1==e.is_mandatory&&this.mandt_lables.push(e.lable_id),this.fb.group({lable_id:[e.lable_id,H.H.required],lable_name:[e.lable_name,H.H.required],is_mandatory:[e.is_mandatory,H.H.required],options:this.fb.array(this.loadOptionsArray(e.options))})}loadOptionsArray(e){return e.map(e=>this.createOption(e))}createOption(e){return this.fb.group({field_type:[e.field_type],checkbox_id:[e.checkbox_id],checkbox_name:[e.checkbox_name],textbox_lable_id:[e.textbox_lable_id],textbox_lable_name:[e.textbox_lable_name?e.textbox_lable_name:e.textbox_lable_value],isChecked:[e.isChecked]})}fetchEmployeeExitSurvey(){let e={params:{user_aid:this._loginService.getProfile().profile.aid,form_name:"exit_survey",current_date:d()(this.currentDate).format("YYYY-MM-DD")}};return new Promise((t,n)=>{this.subs.sink=this.edreqService.fetchEmployeeExitSurvey(e).subscribe(e=>{e.err?this._toaster.showWarning("Error","Something went wrong."):(this.displayCondition=!0,t(e.data))},e=>{this._toaster.showError("Error","Failed to retrieve Exit Survey Form !",2e3),console.log(e),n(e)})})}viewFilledExitSurvey(){let e={params:{user_aid:this._loginService.getProfile().profile.aid,exit_employee_id:this.edreqService.empExId,form_name:"exit_survey",current_date:d()(this.currentDate).format("YYYY-MM-DD")}};return new Promise((t,n)=>{this.subs.sink=this.edreqService.viewFilledExitSurvey(e).subscribe(e=>{e.err?this._toaster.showWarning("Error","Something went wrong."):(this.displayCondition=!0,t(e.data))},e=>{this._toaster.showError("Error","Failed to retrieve Exit Process !",2e3),console.log(e),n(e)})})}isSubset(e,t){return e.every(e=>t.includes(e))}submitBtn(){return Object(r.c)(this,void 0,void 0,(function*(){console.log("Default mandatory lable ids",this.mandt_lables),console.log("Selected lable ids",this.selected_lables);let e=this.isSubset(this.mandt_lables,this.selected_lables);if(console.log("Is mandatory fields are filled",e),e){let e={exit_employee_id:this.edreqService.empExId,exit_process_id:this.edreqService.processId,current_date:d()(this.currentDate).format("YYYY-MM-DD"),user_aid:this._loginService.getProfile().profile.aid,form_name:"exit_survey",form_values:this.params.exitSurveySection};this.submit_loader=!0,this.submitFlag=!0,this.onSubmit=yield this.saveExitForm(e),this.submit_loader=!1,this.submitFlag=!1,this._router.navigate(["../detail-resignation-page"],{relativeTo:this.route}),this.openSnackBar("Your survey has been received","Ok")}else this._toaster.showWarning("Warning","Fill all mandatory fields")}))}saveExitForm(e){return new Promise((t,n)=>{this.subs.sink=this.edreqService.saveExitForm(e).subscribe(e=>{t(e)},e=>{this._toaster.showError("Error","Failed to submit your Form!",2e3),console.log(e),n(e)})})}openSnackBar(e,t){this._snackBar.open(e,t,{duration:3e3})}backtodetailed(){this._router.navigate(["../detail-resignation-page"],{relativeTo:this.route})}}return e.\u0275fac=function(t){return new(t||e)(p["\u0275\u0275directiveInject"](o.g),p["\u0275\u0275directiveInject"](o.a),p["\u0275\u0275directiveInject"](Q.a),p["\u0275\u0275directiveInject"](f.a),p["\u0275\u0275directiveInject"](h.a),p["\u0275\u0275directiveInject"](g.a),p["\u0275\u0275directiveInject"](H.i))},e.\u0275cmp=p["\u0275\u0275defineComponent"]({type:e,selectors:[["app-exit-survey"]],decls:3,vars:2,consts:[[1,"container-fluid","RequestForm","formpage"],[4,"ngIf"],[1,"d-flex","align-items-center","justify-content-center","mt-3",2,"height","77vh","margin-top","0rem !important"],["matTooltip","Please wait...","diameter","30"],[1,"exit-survey-form-whole"],[1,"exit-survey-header"],[3,"formGroup","ngStyle"],["formArrayName","exitSurveySection"],[3,"formGroupName",4,"ngFor","ngForOf"],[1,"pl-3","pt-3"],[1,"submit-button",3,"disabled","click"],[3,"formGroupName"],["formArrayName","lables"],[1,"label-outline"],[1,"label-name"],["class","required-star",4,"ngIf"],["formArrayName","options",1,"options-to-send"],["class","option-container",3,"formGroupName",4,"ngFor","ngForOf"],[1,"required-star"],[1,"option-container",3,"formGroupName"],["class","single-select-main",4,"ngIf"],["class","multi-select-main",4,"ngIf"],[1,"text-field-main"],[1,"single-select-main"],["type","checkbox","formControlName","isChecked",1,"single-checkbox-type",3,"checked","change"],[1,"single-check-label"],[1,"multi-select-main"],["type","checkbox","formControlName","isChecked",1,"multi-select-checkbox-type",3,"change"],[1,"mulit-select-label"],["type","text","formControlName","textbox_lable_name",1,"text-field-exit-survey",3,"change"],["matTooltip","Please wait...","diameter","23",1,"loader"],[1,"doneall-icon"]],template:function(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"div",0),p["\u0275\u0275template"](1,kt,3,0,"ng-container",1),p["\u0275\u0275template"](2,Wt,2,1,"ng-container",1),p["\u0275\u0275elementEnd"]()),2&e&&(p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",t.page_loader),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!t.page_loader))},directives:[i.NgIf,F.c,x.a,u.a,H.J,H.w,H.n,i.NgStyle,H.h,i.NgForOf,H.o,H.b,H.v,H.l,H.e,v.a],styles:['.exit-survey-form-whole[_ngcontent-%COMP%]{width:95%;margin-top:17px;margin-left:3%;height:77vh;overflow-x:hidden!important;overflow-y:scroll;font-family:Roboto}.exit-survey-form-whole[_ngcontent-%COMP%]   input[type=checkbox][_ngcontent-%COMP%]:checked{background:#ee4961;color:#fff}.exit-survey-form-whole[_ngcontent-%COMP%]   input[type=checkbox][_ngcontent-%COMP%]{cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;outline:0;background:#fff;height:15px;width:15px;border:1px solid;color:#d3d3d3;border-radius:4px}.exit-survey-form-whole[_ngcontent-%COMP%]   input[type=checkbox][_ngcontent-%COMP%]:after{content:" ";position:relative;left:40%;top:20%;width:15%;height:40%;border:solid #fff;border-width:0 2px 2px 0;transform:rotate(50deg);display:none}.exit-survey-form-whole[_ngcontent-%COMP%]   input[type=checkbox][_ngcontent-%COMP%]:checked:after{display:block}.exit-survey-form-whole[_ngcontent-%COMP%]   .exit-survey-header[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;color:var(--black-100,#000);text-align:center;font-size:14px;font-style:normal;font-weight:500;line-height:normal;letter-spacing:.28px;text-transform:capitalize}.exit-survey-form-whole[_ngcontent-%COMP%]   .label-outline[_ngcontent-%COMP%]{border-top-right-radius:15px;border-top-left-radius:15px;border-bottom-left-radius:0;background-color:#f3f5fc;width:97%;margin-top:1%}.exit-survey-form-whole[_ngcontent-%COMP%]   .label-outline[_ngcontent-%COMP%]   .label-name[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;font-weight:400;font-size:13px;line-height:24px;display:flex;align-items:center;letter-spacing:.02em;text-transform:none;color:#000}.exit-survey-form-whole[_ngcontent-%COMP%]   .options-to-send[_ngcontent-%COMP%]{width:97%;border:1px solid var(--blue-grey-40,#dadce2);min-height:50px;justify-content:flex-start;align-items:center;padding-left:2%}.exit-survey-form-whole[_ngcontent-%COMP%]   .options-to-send[_ngcontent-%COMP%]   .single-select-main[_ngcontent-%COMP%]{display:flex;justify-content:flex-start;align-items:center;padding:5px;width:10rem}.exit-survey-form-whole[_ngcontent-%COMP%]   .options-to-send[_ngcontent-%COMP%]   .single-select-main[_ngcontent-%COMP%]   .single-select-checkbox-type[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;gap:2%}.exit-survey-form-whole[_ngcontent-%COMP%]   .options-to-send[_ngcontent-%COMP%]   .single-select-main[_ngcontent-%COMP%]   .single-check-label[_ngcontent-%COMP%]{width:auto;margin-bottom:0!important;padding-left:4px}.exit-survey-form-whole[_ngcontent-%COMP%]   .options-to-send[_ngcontent-%COMP%]   .multi-select-main[_ngcontent-%COMP%]{display:flex;justify-content:flex-start;align-items:center;margin-right:10px}.exit-survey-form-whole[_ngcontent-%COMP%]   .options-to-send[_ngcontent-%COMP%]   .multi-select-main[_ngcontent-%COMP%]   .multi-select-checkbox-type[_ngcontent-%COMP%]{height:14px;width:14px}.exit-survey-form-whole[_ngcontent-%COMP%]   .options-to-send[_ngcontent-%COMP%]   .multi-select-main[_ngcontent-%COMP%]   .mulit-select-label[_ngcontent-%COMP%]{margin-bottom:0!important;width:auto;padding-left:4px}.exit-survey-form-whole[_ngcontent-%COMP%]   .options-to-send[_ngcontent-%COMP%]   .text-field-main[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;border:none;width:100%}.exit-survey-form-whole[_ngcontent-%COMP%]   .options-to-send[_ngcontent-%COMP%]   .text-field-main[_ngcontent-%COMP%]   .text-field-exit-survey[_ngcontent-%COMP%]{width:97%;margin-top:8px;justify-content:flex-start;align-items:center;border:1px solid var(--blue-grey-40,#dadce2);height:5vh;padding:0 10px}.exit-survey-form-whole[_ngcontent-%COMP%]   .option-container[_ngcontent-%COMP%]{width:calc(100% / 3);box-sizing:border-box;padding:.5rem}.exit-survey-form-whole[_ngcontent-%COMP%]   .submit-button[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;background:#f1f3f8;border-radius:8px;border:1px solid var(--blue-grey-100,#45546e);color:var(--blue-grey-100,#45546e);font-size:14px;font-style:normal;font-weight:700;line-height:16px;text-transform:capitalize;width:100px;height:40px}.exit-survey-form-whole[_ngcontent-%COMP%]   .submit-button[_ngcontent-%COMP%]   .doneall-icon[_ngcontent-%COMP%]{justify-content:flex-start;align-items:center;display:flex;font-size:16px}.exit-survey-form-whole[_ngcontent-%COMP%]   .submit-button[_ngcontent-%COMP%]   [_ngcontent-%COMP%]:disabled{opacity:.6}.required-star[_ngcontent-%COMP%]{color:#cf0001}  .input{outline:none!important;box-shadow:none!important}  .mat-card:not([class*=mat-elevation-z]){box-shadow:none}.mat-spinner[_ngcontent-%COMP%]   circle[_ngcontent-%COMP%],   .submit-button .mat-progress-spinner circle{stroke:var(--palette-100-green,#5f6c81)}']}),e})();var Bt=n("7EHt");function Lt(e,t){1&e&&(p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275elementStart"](1,"div",2),p["\u0275\u0275element"](2,"mat-spinner",3),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementContainerEnd"]())}function $t(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",30),p["\u0275\u0275elementStart"](1,"div",31),p["\u0275\u0275text"](2),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](3,"div",32),p["\u0275\u0275elementStart"](4,"div",33),p["\u0275\u0275elementStart"](5,"div"),p["\u0275\u0275text"](6),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"]().$implicit,t=p["\u0275\u0275nextContext"](5);p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"](" ",t.fieldConfig[e].field_label," "),p["\u0275\u0275advance"](4),p["\u0275\u0275textInterpolate1"](" ",null==t.fieldConfig[e].options[(null==t.departmentDescriptions?null:t.departmentDescriptions.data[0][e])-1]?null:t.fieldConfig[e].options[(null==t.departmentDescriptions?null:t.departmentDescriptions.data[0][e])-1].name," ")}}function Gt(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",30),p["\u0275\u0275elementStart"](1,"div",31),p["\u0275\u0275text"](2),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](3,"div",32),p["\u0275\u0275elementStart"](4,"div",33),p["\u0275\u0275elementStart"](5,"div",34),p["\u0275\u0275text"](6),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"]().$implicit,t=p["\u0275\u0275nextContext"](5);p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"](" ",t.fieldConfig[e].field_label," "),p["\u0275\u0275advance"](3),p["\u0275\u0275property"]("matTooltip",null==t.departmentDescriptions?null:t.departmentDescriptions.data[0][e]),p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" ",null!=t.departmentDescriptions&&t.departmentDescriptions.data[0][e]?null==t.departmentDescriptions?null:t.departmentDescriptions.data[0][e]:"--"," ")}}function Kt(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div"),p["\u0275\u0275template"](1,$t,7,2,"div",29),p["\u0275\u0275template"](2,Gt,7,3,"div",29),p["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=p["\u0275\u0275nextContext"](5);p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf","toggle-button"==n.fieldConfig[e].field_type&&n.fieldConfig[e].is_active&&n.fieldConfig[e].is_active_field),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf","text"==n.fieldConfig[e].field_type&&n.fieldConfig[e].is_active&&n.fieldConfig[e].is_active_field)}}function Jt(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",28),p["\u0275\u0275template"](1,Kt,3,2,"div",7),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](4);p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngForOf",e.sortedKeys(e.fieldConfig))}}function Ut(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",30),p["\u0275\u0275elementStart"](1,"div",31),p["\u0275\u0275text"](2),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](3,"div",32),p["\u0275\u0275elementStart"](4,"div",33),p["\u0275\u0275elementStart"](5,"div"),p["\u0275\u0275text"](6),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"]().$implicit,t=p["\u0275\u0275nextContext"](5);p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"](" ",t.fieldConfig[e].field_label," "),p["\u0275\u0275advance"](4),p["\u0275\u0275textInterpolate1"](" ",null==t.fieldConfig[e].options[(null==t.departmentDescriptions?null:t.departmentDescriptions.data[0][e])-1]?null:t.fieldConfig[e].options[(null==t.departmentDescriptions?null:t.departmentDescriptions.data[0][e])-1].name," ")}}function Xt(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",30),p["\u0275\u0275elementStart"](1,"div",31),p["\u0275\u0275text"](2),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](3,"div",32),p["\u0275\u0275elementStart"](4,"div",33),p["\u0275\u0275elementStart"](5,"div",34),p["\u0275\u0275text"](6),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"]().$implicit,t=p["\u0275\u0275nextContext"](5);p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"](" ",t.fieldConfig[e].field_label," "),p["\u0275\u0275advance"](3),p["\u0275\u0275property"]("matTooltip",null==t.departmentDescriptions?null:t.departmentDescriptions.data[0][e]),p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" ",null!=t.departmentDescriptions&&t.departmentDescriptions.data[0][e]?null==t.departmentDescriptions?null:t.departmentDescriptions.data[0][e]:"--"," ")}}function Qt(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div"),p["\u0275\u0275template"](1,Ut,7,2,"div",29),p["\u0275\u0275template"](2,Xt,7,3,"div",29),p["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=p["\u0275\u0275nextContext"](5);p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf","toggle-button"==n.fieldConfig[e].field_type&&n.fieldConfig[e].is_active&&n.fieldConfig[e].is_active_field),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf","text"==n.fieldConfig[e].field_type&&n.fieldConfig[e].is_active&&n.fieldConfig[e].is_active_field)}}function Zt(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",28),p["\u0275\u0275template"](1,Qt,3,2,"div",7),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](4);p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngForOf",e.sortedKeys(e.fieldConfig))}}function en(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",30),p["\u0275\u0275elementStart"](1,"div",31),p["\u0275\u0275text"](2),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](3,"div",32),p["\u0275\u0275elementStart"](4,"div",33),p["\u0275\u0275elementStart"](5,"div"),p["\u0275\u0275text"](6),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"]().$implicit,t=p["\u0275\u0275nextContext"](5);p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"](" ",t.fieldConfig[e].field_label," "),p["\u0275\u0275advance"](4),p["\u0275\u0275textInterpolate1"](" ",null==t.fieldConfig[e].options[(null==t.departmentDescriptions?null:t.departmentDescriptions.data[0][e])-1]?null:t.fieldConfig[e].options[(null==t.departmentDescriptions?null:t.departmentDescriptions.data[0][e])-1].name," ")}}function tn(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",30),p["\u0275\u0275elementStart"](1,"div",31),p["\u0275\u0275text"](2),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](3,"div",32),p["\u0275\u0275elementStart"](4,"div",33),p["\u0275\u0275elementStart"](5,"div",34),p["\u0275\u0275text"](6),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"]().$implicit,t=p["\u0275\u0275nextContext"](5);p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"](" ",t.fieldConfig[e].field_label," "),p["\u0275\u0275advance"](3),p["\u0275\u0275property"]("matTooltip",null==t.departmentDescriptions?null:t.departmentDescriptions.data[0][e]),p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" ",null!=t.departmentDescriptions&&t.departmentDescriptions.data[0][e]?null==t.departmentDescriptions?null:t.departmentDescriptions.data[0][e]:"--"," ")}}function nn(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div"),p["\u0275\u0275template"](1,en,7,2,"div",29),p["\u0275\u0275template"](2,tn,7,3,"div",29),p["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=p["\u0275\u0275nextContext"](5);p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf","toggle-button"==n.fieldConfig[e].field_type&&n.fieldConfig[e].is_active&&n.fieldConfig[e].is_active_field),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf","text"==n.fieldConfig[e].field_type&&n.fieldConfig[e].is_active&&n.fieldConfig[e].is_active_field)}}function on(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",28),p["\u0275\u0275template"](1,nn,3,2,"div",7),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](4);p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngForOf",e.sortedKeys(e.fieldConfig))}}function rn(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",30),p["\u0275\u0275elementStart"](1,"div",31),p["\u0275\u0275text"](2),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](3,"div",32),p["\u0275\u0275elementStart"](4,"div",33),p["\u0275\u0275elementStart"](5,"div"),p["\u0275\u0275text"](6),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"]().$implicit,t=p["\u0275\u0275nextContext"](5);p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"](" ",t.fieldConfig[e].field_label," "),p["\u0275\u0275advance"](4),p["\u0275\u0275textInterpolate1"](" ",null==t.fieldConfig[e].options[(null==t.departmentDescriptions?null:t.departmentDescriptions.data[0][e])-1]?null:t.fieldConfig[e].options[(null==t.departmentDescriptions?null:t.departmentDescriptions.data[0][e])-1].name," ")}}function an(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",30),p["\u0275\u0275elementStart"](1,"div",31),p["\u0275\u0275text"](2),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](3,"div",32),p["\u0275\u0275elementStart"](4,"div",33),p["\u0275\u0275elementStart"](5,"div",34),p["\u0275\u0275text"](6),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"]().$implicit,t=p["\u0275\u0275nextContext"](5);p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"](" ",t.fieldConfig[e].field_label," "),p["\u0275\u0275advance"](3),p["\u0275\u0275property"]("matTooltip",null==t.departmentDescriptions?null:t.departmentDescriptions.data[0][e]),p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" ",null!=t.departmentDescriptions&&t.departmentDescriptions.data[0][e]?null==t.departmentDescriptions?null:t.departmentDescriptions.data[0][e]:"--"," ")}}function ln(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div"),p["\u0275\u0275template"](1,rn,7,2,"div",29),p["\u0275\u0275template"](2,an,7,3,"div",29),p["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=p["\u0275\u0275nextContext"](5);p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf","checkbox"==n.fieldConfig[e].field_type&&n.fieldConfig[e].is_active&&n.fieldConfig[e].is_active_field),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf","text"==n.fieldConfig[e].field_type&&n.fieldConfig[e].is_active&&n.fieldConfig[e].is_active_field)}}function sn(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",28),p["\u0275\u0275template"](1,ln,3,2,"div",7),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](4);p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngForOf",e.sortedKeys(e.fieldConfig))}}const cn=function(e){return{color:e}},dn=function(e){return{"background-color":e}};function pn(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275elementStart"](1,"div",8),p["\u0275\u0275listener"]("click",(function(){p["\u0275\u0275restoreView"](e);const n=t.$implicit,i=p["\u0275\u0275nextContext"](3);return i.fetchDepartmentClearanceFormDetails(null==i.departmentalClearance[n][0]?null:i.departmentalClearance[n][0].type)})),p["\u0275\u0275elementStart"](2,"mat-expansion-panel",9),p["\u0275\u0275elementStart"](3,"mat-expansion-panel-header",10),p["\u0275\u0275elementStart"](4,"mat-panel-title"),p["\u0275\u0275elementStart"](5,"div",11),p["\u0275\u0275elementStart"](6,"div",12),p["\u0275\u0275elementStart"](7,"div",13),p["\u0275\u0275elementStart"](8,"mat-icon",14),p["\u0275\u0275text"](9,"done_all"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](10,"div",15),p["\u0275\u0275text"](11),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](12,"div",16),p["\u0275\u0275text"](13,"Approver"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](14,"div",16),p["\u0275\u0275text"](15," Completed On "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](16,"div",16),p["\u0275\u0275text"](17,"Status"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](18,"div",17),p["\u0275\u0275elementStart"](19,"div",18),p["\u0275\u0275text"](20),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](21,"div",19),p["\u0275\u0275elementStart"](22,"div",20),p["\u0275\u0275element"](23,"app-user-image",21),p["\u0275\u0275text"](24,"\xa0 "),p["\u0275\u0275element"](25,"app-user-profile",22),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](26,"div",23),p["\u0275\u0275text"](27),p["\u0275\u0275pipe"](28,"date"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](29,"div",24),p["\u0275\u0275element"](30,"div",25),p["\u0275\u0275elementStart"](31,"div"),p["\u0275\u0275text"](32),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](33,"mat-panel-description"),p["\u0275\u0275elementStart"](34,"div",26),p["\u0275\u0275template"](35,Jt,2,1,"div",27),p["\u0275\u0275template"](36,Zt,2,1,"div",27),p["\u0275\u0275template"](37,on,2,1,"div",27),p["\u0275\u0275template"](38,sn,2,1,"div",27),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=t.$implicit,n=p["\u0275\u0275nextContext"](3);p["\u0275\u0275advance"](8),p["\u0275\u0275property"]("ngStyle",p["\u0275\u0275pureFunction1"](16,cn,n.departmentalClearance[e][0].status_color)),p["\u0275\u0275advance"](3),p["\u0275\u0275textInterpolate1"](" ",n.departmentalClearance[e][0].dept_name," "),p["\u0275\u0275advance"](9),p["\u0275\u0275textInterpolate1"](" ",n.departmentalClearance[e][0].process_desc," "),p["\u0275\u0275advance"](3),p["\u0275\u0275property"]("tooltip",n.AwardapproverTooltip)("id",n.departmentalClearance[e][0].approver_oid),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("oid",n.departmentalClearance[e][0].approver_oid),p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"](" ",n.departmentalClearance[e][0].completed_on?p["\u0275\u0275pipeBind2"](28,13,n.departmentalClearance[e][0].completed_on,"dd-MMM-YYYY"):"--"," "),p["\u0275\u0275advance"](3),p["\u0275\u0275property"]("ngStyle",p["\u0275\u0275pureFunction1"](18,dn,n.departmentalClearance[e][0].status_color)),p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"](" ",n.departmentalClearance[e][0].process_status," "),p["\u0275\u0275advance"](3),p["\u0275\u0275property"]("ngIf","admin"==e&&n.fieldConfig&&n.departmentDescriptions&&(null==n.departmentDescriptions?null:n.departmentDescriptions.data.length)>0),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf","finance"==e&&n.fieldConfig&&n.departmentDescriptions&&(null==n.departmentDescriptions?null:n.departmentDescriptions.data.length)>0),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf","system_admin"==e&&n.fieldConfig&&n.departmentDescriptions&&(null==n.departmentDescriptions?null:n.departmentDescriptions.data.length)>0),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf","hr"==e&&n.fieldConfig&&n.departmentDescriptions&&(null==n.departmentDescriptions?null:n.departmentDescriptions.data.length)>0)}}function mn(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div"),p["\u0275\u0275elementStart"](1,"mat-card",4),p["\u0275\u0275elementStart"](2,"div",5),p["\u0275\u0275elementStart"](3,"div",6),p["\u0275\u0275elementStart"](4,"mat-accordion"),p["\u0275\u0275template"](5,pn,39,20,"ng-container",7),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](2);p["\u0275\u0275advance"](5),p["\u0275\u0275property"]("ngForOf",e.department)}}function gn(e,t){if(1&e&&(p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275template"](1,mn,6,1,"div",1),p["\u0275\u0275elementContainerEnd"]()),2&e){const e=p["\u0275\u0275nextContext"]();p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",e.displayCondition)}}let fn=(()=>{class e{constructor(e,t,n,i,o){this._router=e,this.route=t,this.edreqService=n,this._toaster=i,this._loginService=o,this.subs=new j.a,this.displayCondition=!1,this.currentDate=new Date,this.departmentDescriptions={data:[]},this.empDetails={params:{user_aid:this._loginService.getProfile().profile.aid,type:"",current_date:d()(this.currentDate).format("YYYY-MM-DD"),exit_employee_id:this.edreqService.empExId}},this.fieldConfig={},this.formfields=[],this.isFormDetailsFetched=!1}ngOnInit(){return Object(r.c)(this,void 0,void 0,(function*(){this.page_loader=!0,null==this.edreqService.empExId?this.backbtn():(this.departmentalClearance=yield this.fetchDepartmentClearance(),this.department=Object.keys(this.departmentalClearance)),this.page_loader=!1}))}fetchDepartmentClearance(){let e={params:{user_aid:this._loginService.getProfile().profile.aid,user_oid:this._loginService.getProfile().profile.oid,current_date:d()(this.currentDate).format("YYYY-MM-DD"),type:"department_clearance",exit_employee_id:this.edreqService.empExId}};return new Promise((t,n)=>{this.subs.sink=this.edreqService.fetchDepartmentClearance(e).subscribe(e=>{e.err?this._toaster.showWarning("Error","Something went wrong."):(this.displayCondition=!0,t(e.data))},e=>{this._toaster.showError("Error","Failed to retrieve Departmental Clearance !",2e3),console.log(e),n(e)})})}fetchDepartmentClearanceFormDetails(e){var t,n;return Object(r.c)(this,void 0,void 0,(function*(){console.log("Clicked",e),this.departmentDescriptions={data:[]},this.formfields=[];let i={params:{user_aid:this._loginService.getProfile().profile.aid,type:e,current_date:d()(this.currentDate).format("YYYY-MM-DD"),exit_employee_id:this.edreqService.empExId}};"hr"==e?this.formfields=yield this.fetchHRFormConfig():"admin"==e?this.formfields=yield this.getAdminFormConfig():"system_admin"==e?this.formfields=yield this.getSystemAdminFormConfig():"finance"==e&&(this.formfields=yield this.getFinanceFormConfig()),this.handleFieldConfig();try{const e=yield this.edreqService.fetchDepartmentClearanceFormDetails(i).toPromise();return e.err?this._toaster.showWarning("Error","Something went wrong."):(this.departmentDescriptions=e,console.log("departmentDescriptions",null===(t=this.departmentDescriptions)||void 0===t?void 0:t.data),0==(null===(n=this.departmentDescriptions)||void 0===n?void 0:n.data.length)&&this._toaster.showWarning("Approver Not yet Submitted ","Kindly wait for the Approver to fill out the Form")),e.data}catch(o){throw this._toaster.showError("Error","Failed to retrieve Departmental Clearance!",1e3),console.log(o),o}}))}backbtn(){this._router.navigate(["../detail-resignation-page"],{relativeTo:this.route})}getAdminFormConfig(){return new Promise((e,t)=>{this.subs.sink=this.edreqService.getAdminFormConfig().subscribe(t=>{t.err?this._toaster.showWarning("Error","Something went wrong."):(this.displayCondition=!0,e(t.data))},e=>{this._toaster.showError("Error","Failed to retrieve IP Signout Form !",2e3),console.log(e),t(e)})})}getSystemAdminFormConfig(){return new Promise((e,t)=>{this.subs.sink=this.edreqService.getSystemAdminFormConfig().subscribe(t=>{t.err?this._toaster.showWarning("Error","Something went wrong."):(this.displayCondition=!0,e(t.data))},e=>{this._toaster.showError("Error","Failed to retrieve System Admin Form !",2e3),console.log(e),t(e)})})}getFinanceFormConfig(){return new Promise((e,t)=>{this.subs.sink=this.edreqService.getFinanceFormConfig().subscribe(t=>{t.err?this._toaster.showWarning("Error","Something went wrong."):(this.displayCondition=!0,e(t.data))},e=>{this._toaster.showError("Error","Failed to retrive Finance Form!",2e3),console.log(e),t(e)})})}fetchHRFormConfig(){return new Promise((e,t)=>{this.subs.sink=this.edreqService.fetchHRFormConfig().subscribe(t=>{e(t.data)},e=>{this._toaster.showError("Error","Failed to change to Inapplicable!",2e3),console.log(e),t(e)})})}sortedKeys(e){return Object.keys(e).sort((t,n)=>e[t].sort_order-e[n].sort_order)}handleFieldConfig(){return Object(r.c)(this,void 0,void 0,(function*(){this.fieldConfig={};let e=this.formfields;if(console.log("fieldList",e),e.length>0){const t=new Map;e.forEach((e,n)=>{t.set(e.field_key,e)}),t.forEach((e,t)=>{this.fieldConfig[t]=e}),console.log(this.fieldConfig)}console.log("Field Config",this.fieldConfig)}))}}return e.\u0275fac=function(t){return new(t||e)(p["\u0275\u0275directiveInject"](o.g),p["\u0275\u0275directiveInject"](o.a),p["\u0275\u0275directiveInject"](f.a),p["\u0275\u0275directiveInject"](h.a),p["\u0275\u0275directiveInject"](g.a))},e.\u0275cmp=p["\u0275\u0275defineComponent"]({type:e,selectors:[["app-department-clearance"]],features:[p["\u0275\u0275ProvidersFeature"]([],[Bt.c])],decls:3,vars:2,consts:[[1,"container-fluid","RequestForm","formpage"],[4,"ngIf"],[1,"d-flex","align-items-center","justify-content-center","mt-3",2,"height","77vh","margin-top","0rem !important"],["matTooltip","Please wait...","diameter","30"],[2,"width","95%","margin-top","17px","margin-left","3%","height","77vh"],[1,"department-clearance"],[1,"Scrollstyle"],[4,"ngFor","ngForOf"],[3,"click"],["hideToggle",""],[2,"height","15vh"],[2,"width","100%"],[1,"tab-dept-clearance-head"],[1,"tick-mark","row"],[3,"ngStyle"],[1,"dept-header","col-3",2,"white-space","pre-wrap"],[1,"headerforclearance","col-3"],[1,"tab-dept-clearance-desc",2,"display","flex","justify-content","center","align-items","baseline"],[1,"step-desc","col-3"],[1,"approver-desc","col-3"],[2,"display","flex","justify-content","center","align-items","center"],["content-type","template","placement","top","imgWidth","28px","imgHeight","28px",2,"margin-right","3px","overflow","hidden","text-overflow","ellipsis",3,"tooltip","id"],["type","name",3,"oid"],[1,"date-desc","col-3"],[1,"status-desc","col-3"],[1,"circle-stat",3,"ngStyle"],[1,"accordion-body","d-flex","justify-content-center"],["class","admin-description id-card",4,"ngIf"],[1,"admin-description","id-card"],["class","row","style","margin-bottom: 5px",4,"ngIf"],[1,"row",2,"margin-bottom","5px"],[1,"id-card-header","col-9"],[1,"id-card-status","col-3"],[1,"status-desc"],[1,"ellip",3,"matTooltip"]],template:function(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"div",0),p["\u0275\u0275template"](1,Lt,3,0,"ng-container",1),p["\u0275\u0275template"](2,gn,2,1,"ng-container",1),p["\u0275\u0275elementEnd"]()),2&e&&(p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",t.page_loader),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!t.page_loader))},directives:[i.NgIf,F.c,x.a,u.a,Bt.a,i.NgForOf,Bt.c,Bt.g,Bt.h,v.a,i.NgStyle,q.a,Ie.a,Bt.f],pipes:[i.DatePipe],styles:[".department-clearance[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal}.department-clearance[_ngcontent-%COMP%]   .accordion-body[_ngcontent-%COMP%]{margin-left:5%}.department-clearance[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]{font-weight:500;font-size:14px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;color:#000;border:none;height:40px;display:flex;padding:3px 10px;justify-content:flex-start;background:none}.department-clearance[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]{display:flex;justify-content:center}.department-clearance[_ngcontent-%COMP%]   .tab-dept-clearance-head[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;display:flex;justify-content:center;margin-left:2%}.department-clearance[_ngcontent-%COMP%]   .tab-dept-clearance-head[_ngcontent-%COMP%]   .dept-header[_ngcontent-%COMP%]{font-weight:400;font-size:14px;line-height:16px;display:flex;justify-content:flex-start;align-items:center;color:#26303e;margin-left:0}.department-clearance[_ngcontent-%COMP%]   .tab-dept-clearance-head[_ngcontent-%COMP%]   .headerforclearance[_ngcontent-%COMP%]{display:flex;justify-content:center;font-weight:400;font-size:12px;line-height:14px;letter-spacing:.02em;text-transform:capitalize;color:#000;opacity:.5}.department-clearance[_ngcontent-%COMP%]   .circle-stat[_ngcontent-%COMP%]{width:11px;height:11px;border-radius:50%;margin-top:0}.department-clearance[_ngcontent-%COMP%]   .tab-dept-clearance-desc[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;display:flex;justify-content:center;margin-left:2%}.department-clearance[_ngcontent-%COMP%]   .tab-dept-clearance-desc[_ngcontent-%COMP%]   .step-desc[_ngcontent-%COMP%]{justify-content:flex-start;font-weight:400;font-size:14px;line-height:16px;display:flex;align-items:center;color:#26303e;opacity:.7;margin-left:1.6rem}.department-clearance[_ngcontent-%COMP%]   .tab-dept-clearance-desc[_ngcontent-%COMP%]   .approver-desc[_ngcontent-%COMP%], .department-clearance[_ngcontent-%COMP%]   .tab-dept-clearance-desc[_ngcontent-%COMP%]   .date-desc[_ngcontent-%COMP%], .department-clearance[_ngcontent-%COMP%]   .tab-dept-clearance-desc[_ngcontent-%COMP%]   .status-desc[_ngcontent-%COMP%]{display:flex;justify-content:center;font-weight:400;font-size:13px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;color:#000}.department-clearance[_ngcontent-%COMP%]   .tab-dept-clearance-desc[_ngcontent-%COMP%]   .status-desc[_ngcontent-%COMP%]{align-items:center;gap:2%}.department-clearance[_ngcontent-%COMP%]   .Scrollstyle[_ngcontent-%COMP%]{max-height:65vh;overflow-y:scroll}.overflowText[_ngcontent-%COMP%]{flex-wrap:wrap;white-space:normal;overflow:hidden;text-overflow:ellipsis}.admin-description[_ngcontent-%COMP%]{height:auto;width:850px;border:1px solid #f27a6c;border-radius:4px;padding:1rem}.admin-description[_ngcontent-%COMP%]   .hr-block[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;padding:.5rem;display:flex;justify-content:flex-start}.admin-description[_ngcontent-%COMP%]   .hr-block[_ngcontent-%COMP%]   .hr-header[_ngcontent-%COMP%]{font-weight:500;font-size:14px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;color:#000;width:100px}.admin-description[_ngcontent-%COMP%]   .hr-block[_ngcontent-%COMP%]   .hr-status[_ngcontent-%COMP%]{display:flex;justify-content:flex-start;gap:17%;align-items:center}.admin-description[_ngcontent-%COMP%]   .hr-block[_ngcontent-%COMP%]   .hr-status[_ngcontent-%COMP%]   .status-header[_ngcontent-%COMP%]{font-weight:400;font-size:12px;line-height:14px;letter-spacing:.02em;color:#000;opacity:.4}.admin-description[_ngcontent-%COMP%]   .hr-block[_ngcontent-%COMP%]   .hr-status[_ngcontent-%COMP%]   .status-desc[_ngcontent-%COMP%]{font-weight:400;font-size:14px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;color:#bcb8b8;display:flex;align-self:center}.admin-description[_ngcontent-%COMP%]   .hr-block[_ngcontent-%COMP%]   .idcard-cost[_ngcontent-%COMP%]{display:flex;justify-content:flex-start;gap:10%;align-items:center;width:18%}.admin-description[_ngcontent-%COMP%]   .hr-block[_ngcontent-%COMP%]   .idcard-cost[_ngcontent-%COMP%]   .cost-header[_ngcontent-%COMP%]{font-weight:400;font-size:12px;line-height:14px;letter-spacing:.02em;color:#000;opacity:.4}.admin-description[_ngcontent-%COMP%]   .hr-block[_ngcontent-%COMP%]   .idcard-cost[_ngcontent-%COMP%]   .cost-desc[_ngcontent-%COMP%]{font-weight:500;font-size:14px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;color:#000}.admin-description[_ngcontent-%COMP%]   .id-card[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;padding:.5rem;display:flex;justify-content:flex-start;gap:10%}.admin-description[_ngcontent-%COMP%]   .id-card[_ngcontent-%COMP%]   .id-card-header[_ngcontent-%COMP%]{font-weight:500;font-size:14px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;color:#26303e;width:100px}.admin-description[_ngcontent-%COMP%]   .id-card[_ngcontent-%COMP%]   .id-card-status[_ngcontent-%COMP%]{display:flex;justify-content:flex-start;gap:17%;align-items:center}.admin-description[_ngcontent-%COMP%]   .id-card[_ngcontent-%COMP%]   .id-card-status[_ngcontent-%COMP%]   .status-header[_ngcontent-%COMP%]{font-weight:400;font-size:12px;line-height:14px;letter-spacing:.02em;color:#000;opacity:.4}.admin-description[_ngcontent-%COMP%]   .id-card[_ngcontent-%COMP%]   .id-card-status[_ngcontent-%COMP%]   .status-desc[_ngcontent-%COMP%]{font-weight:500;font-size:14px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;color:#000;display:flex;align-self:center}.admin-description[_ngcontent-%COMP%]   .id-card[_ngcontent-%COMP%]   .id-card-status[_ngcontent-%COMP%]   .ellip[_ngcontent-%COMP%]{overflow:hidden;text-overflow:ellipsis;width:11rem}.admin-description[_ngcontent-%COMP%]   .id-card[_ngcontent-%COMP%]   .idcard-cost[_ngcontent-%COMP%]{display:flex;justify-content:flex-start;gap:10%;align-items:center;width:18%}.admin-description[_ngcontent-%COMP%]   .id-card[_ngcontent-%COMP%]   .idcard-cost[_ngcontent-%COMP%]   .cost-header[_ngcontent-%COMP%]{font-weight:400;font-size:12px;line-height:14px;letter-spacing:.02em;color:#000;opacity:.4}.admin-description[_ngcontent-%COMP%]   .id-card[_ngcontent-%COMP%]   .idcard-cost[_ngcontent-%COMP%]   .cost-desc[_ngcontent-%COMP%]{font-weight:500;font-size:14px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;color:#000}.admin-description[_ngcontent-%COMP%]   .access-card[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;padding:.5rem .5rem .5rem .7rem;display:flex;justify-content:flex-start;gap:10%}.admin-description[_ngcontent-%COMP%]   .access-card[_ngcontent-%COMP%]   .access-header[_ngcontent-%COMP%], .admin-description[_ngcontent-%COMP%]   .access-card[_ngcontent-%COMP%]   .status[_ngcontent-%COMP%]{font-weight:400;font-size:12px;line-height:14px}.admin-description[_ngcontent-%COMP%]   .sim-card[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;padding:.5rem .5rem .5rem .7rem;display:flex;justify-content:flex-start;gap:15%}.admin-description[_ngcontent-%COMP%]   .sim-card[_ngcontent-%COMP%]   .sim-desc[_ngcontent-%COMP%], .admin-description[_ngcontent-%COMP%]   .sim-card[_ngcontent-%COMP%]   .sim-header[_ngcontent-%COMP%]{font-weight:400;font-size:12px;line-height:14px}.finance-description[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;height:auto;width:850px;border:1px solid #f27a6c;border-radius:4px;padding:1rem}.finance-description[_ngcontent-%COMP%]   .main-header[_ngcontent-%COMP%]{font-weight:500;font-size:14px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;color:#000}.finance-description[_ngcontent-%COMP%]   .decriptives[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;gap:7%;padding:1.5rem 3px 3px}.finance-description[_ngcontent-%COMP%]   .decriptives[_ngcontent-%COMP%]   .loan-type[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;gap:5%;width:20%}.finance-description[_ngcontent-%COMP%]   .decriptives[_ngcontent-%COMP%]   .loan-type[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{font-weight:400;font-size:12px;line-height:14px;letter-spacing:.02em;color:#000;opacity:.4}.finance-description[_ngcontent-%COMP%]   .decriptives[_ngcontent-%COMP%]   .loan-type[_ngcontent-%COMP%]   .type[_ngcontent-%COMP%]{font-weight:500;font-size:14px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;color:#000}"]}),e})();function hn(e,t){1&e&&(p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275elementStart"](1,"div",2),p["\u0275\u0275element"](2,"mat-spinner",3),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementContainerEnd"]())}function un(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"p"),p["\u0275\u0275text"](1),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"]().$implicit;p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" ",e.section_description," ")}}function vn(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"b"),p["\u0275\u0275elementStart"](1,"u"),p["\u0275\u0275text"](2),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"]().$implicit;p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate"](e)}}function xn(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"span"),p["\u0275\u0275text"](1),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"]().$implicit;p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate"](e)}}function _n(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div"),p["\u0275\u0275template"](1,vn,3,1,"b",1),p["\u0275\u0275template"](2,xn,2,1,"span",1),p["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf","Product & Business Support Services"==e),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf","Product & Business Support Services"!=e)}}function Cn(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"mat-checkbox",15),p["\u0275\u0275text"](1),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"]().$implicit;p["\u0275\u0275property"]("value",e.is_checked)("checked",e.isChecked),p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" ",null==e?null:e.checkbox_name," ")}}function yn(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",16),p["\u0275\u0275elementStart"](1,"label",17),p["\u0275\u0275text"](2,"Remarks"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275element"](3,"textarea",18),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"]().$implicit;p["\u0275\u0275advance"](3),p["\u0275\u0275propertyInterpolate"]("placeholder",e.placeholder),p["\u0275\u0275property"]("value",e.textbox_lable_value)}}function bn(e,t){if(1&e&&(p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275template"](1,Cn,2,3,"mat-checkbox",13),p["\u0275\u0275template"](2,yn,4,2,"div",14),p["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf","checkbox_single"==e.field_type),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf","freeTextWithLabel"==e.field_type)}}function Mn(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div"),p["\u0275\u0275elementStart"](1,"mat-card",9),p["\u0275\u0275elementStart"](2,"div",10),p["\u0275\u0275template"](3,_n,3,2,"div",8),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](4,"div",11),p["\u0275\u0275elementStart"](5,"div",12),p["\u0275\u0275template"](6,bn,3,2,"ng-container",8),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275element"](7,"br"),p["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=p["\u0275\u0275nextContext"](5);p["\u0275\u0275advance"](3),p["\u0275\u0275property"]("ngForOf",n.getFormControlValue(e.lable_name)),p["\u0275\u0275advance"](3),p["\u0275\u0275property"]("ngForOf",null==e?null:e.options)}}function Sn(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div"),p["\u0275\u0275template"](1,Mn,8,2,"div",8),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"]().$implicit;p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngForOf",null==e?null:e.lables)}}function Pn(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div"),p["\u0275\u0275template"](1,un,2,1,"p",1),p["\u0275\u0275template"](2,Sn,2,1,"div",1),p["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf","project_handover_takeover"!=e.section_name),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf","project_handover_takeover"!=e.section_name)}}function On(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div"),p["\u0275\u0275elementStart"](1,"mat-card",5),p["\u0275\u0275elementStart"](2,"div",6),p["\u0275\u0275elementStart"](3,"div",7),p["\u0275\u0275template"](4,Pn,3,2,"div",8),p["\u0275\u0275elementEnd"](),p["\u0275\u0275element"](5,"br"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](2);p["\u0275\u0275advance"](4),p["\u0275\u0275property"]("ngForOf",e.handoverDetails.data)}}function wn(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",19),p["\u0275\u0275element"](1,"img",20),p["\u0275\u0275text"](2," Manager Not yet submitted "),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](2);p["\u0275\u0275advance"](1),p["\u0275\u0275propertyInterpolate"]("alt",e.handoverDetails.msg)}}function En(e,t){if(1&e&&(p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275template"](1,On,6,1,"div",1),p["\u0275\u0275template"](2,wn,3,1,"div",4),p["\u0275\u0275elementContainerEnd"]()),2&e){const e=p["\u0275\u0275nextContext"]();p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",e.displayCondition&&(null==e.handoverDetails?null:e.handoverDetails.data.length)>0),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",e.displayCondition&&0==(null==e.handoverDetails?null:e.handoverDetails.data.length))}}let In=(()=>{class e{constructor(e,t,n,i,o,r,a){this._router=e,this.route=t,this._snackBar=n,this.edreqService=i,this._toaster=o,this._loginService=r,this.formBuilder=a,this.subs=new j.a,this.displayCondition=!1,this.currentDate=new Date}ngOnInit(){return Object(r.c)(this,void 0,void 0,(function*(){this.page_loader=!0,this.edreqService.recieveEmpExitId().subscribe(e=>{this.empExId=e}),this.handoverDetails=yield this.viewHandoverSurvey(),this.page_loader=!1}))}getFormControlValue(e){return e.split(/:|\|/)}viewHandoverSurvey(){let e={params:{user_aid:this._loginService.getProfile().profile.aid,exit_employee_id:this.edreqService.empExId,form_name:"Handover_takeover",current_date:d()(this.currentDate).format("YYYY-MM-DD")}};return new Promise((t,n)=>{this.subs.sink=this.edreqService.viewHandoverSurvey(e).subscribe(e=>{e.err?this._toaster.showWarning("Error","Something went wrong."):(this.displayCondition=!0,t(e))},e=>{this._toaster.showError("Error","Failed to retrieve Handover Takeover!",2e3),console.log(e),n(e)})})}backtodetailed(){this._router.navigate(["../detail-resignation-page"],{relativeTo:this.route})}}return e.\u0275fac=function(t){return new(t||e)(p["\u0275\u0275directiveInject"](o.g),p["\u0275\u0275directiveInject"](o.a),p["\u0275\u0275directiveInject"](Q.a),p["\u0275\u0275directiveInject"](f.a),p["\u0275\u0275directiveInject"](h.a),p["\u0275\u0275directiveInject"](g.a),p["\u0275\u0275directiveInject"](H.i))},e.\u0275cmp=p["\u0275\u0275defineComponent"]({type:e,selectors:[["app-handover-form"]],decls:3,vars:2,consts:[[1,"container-fluid","RequestForm","formpage"],[4,"ngIf"],[1,"d-flex","align-items-center","justify-content-center","mt-3",2,"height","77vh","margin-top","0rem !important"],["matTooltip","Please wait...","diameter","30"],["class","error-message",4,"ngIf"],[2,"width","95%","margin-top","17px","margin-left","3%","height","77vh"],[1,"ScrollStyle"],[2,"pointer-events","none"],[4,"ngFor","ngForOf"],[2,"border-top-right-radius","15px","border-top-left-radius","15px","border-bottom-left-radius","0px","border-bottom-left-radius","0px","background-color","#f3f5fc","width","97%"],[1,"docs-header",2,"display","block"],[1,"option-handoverform",2,"height","60px","border","1px solid #ced4da","margin-left","0%","width","97%"],[1,"feedback-from-manager",2,"padding","1rem","gap","6rem"],["type","checkbox",3,"value","checked",4,"ngIf"],["class","remarks-textfield",4,"ngIf"],["type","checkbox",3,"value","checked"],[1,"remarks-textfield"],[1,"form-label"],["rows","1",1,"form-control",2,"font-size","14px","height","39px","resize","none",3,"placeholder","value"],[1,"error-message"],["src","src/assets/images/no_approval_requests.png",3,"alt"]],template:function(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"div",0),p["\u0275\u0275template"](1,hn,3,0,"ng-container",1),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](2,En,3,2,"ng-container",1)),2&e&&(p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",t.page_loader),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!t.page_loader))},directives:[i.NgIf,F.c,x.a,u.a,i.NgForOf,be.a],styles:[".handover-approve[_ngcontent-%COMP%]{border:1px solid #52c41a;text-transform:none;height:40px;display:flex;line-height:33px;padding:3px 10px;justify-content:flex-start;font-weight:400;font-size:12px;letter-spacing:-.02em;background:rgba(0,148,50,.07);border-radius:4px}  .mat-card:not([class*=mat-elevation-z]){box-shadow:none}.docs-header[_ngcontent-%COMP%]{font-size:12px;text-transform:none}.docs-header[_ngcontent-%COMP%], .option-handover[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;font-weight:400;line-height:24px;display:flex;align-items:center;letter-spacing:.02em;color:#000}.option-handover[_ngcontent-%COMP%]{font-size:14px;justify-content:space-around}.ScrollStyle[_ngcontent-%COMP%]{max-height:64vh;overflow-y:scroll}  .input{outline:none!important;box-shadow:none!important}.feedback-from-manager[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;font-weight:400;font-size:14px;line-height:24px;display:flex;align-items:center;letter-spacing:.02em;color:#000}.remarks-textfield[_ngcontent-%COMP%]{display:flex;gap:2%;align-items:center;width:50%;margin-left:1%}.error-message[_ngcontent-%COMP%]{font-family:Roboto;justify-content:center;text-align:center;margin-top:20%;font-style:normal;text-transform:none;display:flex;align-items:center;font-size:18px;font-weight:500;color:#000}"]}),e})();function kn(e,t){1&e&&(p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275elementStart"](1,"div",2),p["\u0275\u0275element"](2,"mat-spinner",3),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementContainerEnd"]())}function Dn(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",16),p["\u0275\u0275elementStart"](1,"h5"),p["\u0275\u0275text"](2,"Taken Over Details"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](3,"div",17),p["\u0275\u0275elementStart"](4,"div",18),p["\u0275\u0275elementStart"](5,"div",19),p["\u0275\u0275text"](6,"Taken Over From"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](7,"div",20),p["\u0275\u0275text"](8,"Name"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](9,"div",21),p["\u0275\u0275text"](10),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](11,"div",18),p["\u0275\u0275elementStart"](12,"div",19),p["\u0275\u0275text"](13,"Taken Over By"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](14,"div",20),p["\u0275\u0275text"](15,"Name"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](16,"div",21),p["\u0275\u0275text"](17),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](18,"div",17),p["\u0275\u0275elementStart"](19,"div",18),p["\u0275\u0275elementStart"](20,"div",20),p["\u0275\u0275text"](21,"Designation"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](22,"div",21),p["\u0275\u0275text"](23),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](24,"div",18),p["\u0275\u0275elementStart"](25,"div",20),p["\u0275\u0275text"](26,"Designation"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](27,"div",21),p["\u0275\u0275text"](28),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=p["\u0275\u0275nextContext"](3);p["\u0275\u0275advance"](10),p["\u0275\u0275textInterpolate"](e.taken_from_name),p["\u0275\u0275advance"](7),p["\u0275\u0275textInterpolate"](n.handoverApproverData[0].NAME),p["\u0275\u0275advance"](6),p["\u0275\u0275textInterpolate1"](" ",e.taken_from_designation," "),p["\u0275\u0275advance"](5),p["\u0275\u0275textInterpolate"](n.handoverApproverData[0].position)}}function jn(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div"),p["\u0275\u0275elementStart"](1,"h5"),p["\u0275\u0275text"](2,"Taken Over Details"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](3,"div",17),p["\u0275\u0275elementStart"](4,"div",18),p["\u0275\u0275elementStart"](5,"div",20),p["\u0275\u0275text"](6),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](7,"div",21),p["\u0275\u0275text"](8),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](9,"div",18),p["\u0275\u0275elementStart"](10,"div",20),p["\u0275\u0275text"](11),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](12,"div",21),p["\u0275\u0275text"](13),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](14,"div",17),p["\u0275\u0275elementStart"](15,"div",18),p["\u0275\u0275elementStart"](16,"div",20),p["\u0275\u0275text"](17),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](18,"div",21),p["\u0275\u0275text"](19),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](20,"div",18),p["\u0275\u0275elementStart"](21,"div",20),p["\u0275\u0275text"](22),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](23,"div",21),p["\u0275\u0275text"](24),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](25,"div",17),p["\u0275\u0275elementStart"](26,"div",18),p["\u0275\u0275elementStart"](27,"div",20),p["\u0275\u0275text"](28),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](29,"div",21),p["\u0275\u0275text"](30),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"]().$implicit;p["\u0275\u0275advance"](6),p["\u0275\u0275textInterpolate"](e.lables[0].lable_name),p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"](" ",e.lables[0].options[0].textbox_lable_value?e.lables[0].options[0].textbox_lable_value:"--"," "),p["\u0275\u0275advance"](3),p["\u0275\u0275textInterpolate"](e.lables[3].lable_name),p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"](" ",e.lables[3].options[0].textbox_lable_value?e.lables[3].options[0].textbox_lable_value:"--"," "),p["\u0275\u0275advance"](4),p["\u0275\u0275textInterpolate"](e.lables[1].lable_name),p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"](" ",e.lables[1].options[0].textbox_lable_value?e.lables[1].options[0].textbox_lable_value:"--"," "),p["\u0275\u0275advance"](3),p["\u0275\u0275textInterpolate1"](" ",e.lables[4].lable_name,""),p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"](" ",e.lables[4].options[0].textbox_lable_value?e.lables[4].options[0].textbox_lable_value:"--"," "),p["\u0275\u0275advance"](4),p["\u0275\u0275textInterpolate"](e.lables[2].lable_name),p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"](" ",e.lables[2].options[0].textbox_lable_value?e.lables[2].options[0].textbox_lable_value:"--"," ")}}function Fn(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",16),p["\u0275\u0275template"](1,jn,31,10,"div",1),p["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf","project_handover_takeover"==e.section_name)}}function qn(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div"),p["\u0275\u0275elementStart"](1,"mat-card",5),p["\u0275\u0275elementStart"](2,"div",6),p["\u0275\u0275elementStart"](3,"div",7),p["\u0275\u0275elementStart"](4,"h3"),p["\u0275\u0275text"](5,"PROJECT HANDOVER TAKEOVER"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](6,"button",8),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](2).approved()})),p["\u0275\u0275elementStart"](7,"mat-icon",9),p["\u0275\u0275text"](8," done_all"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275text"](9),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](10,"div",10),p["\u0275\u0275elementStart"](11,"mat-card",11),p["\u0275\u0275template"](12,Dn,29,4,"div",12),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](13,"mat-card",11),p["\u0275\u0275template"](14,Fn,2,1,"div",12),p["\u0275\u0275elementEnd"](),p["\u0275\u0275element"](15,"br"),p["\u0275\u0275element"](16,"hr"),p["\u0275\u0275elementStart"](17,"div",13),p["\u0275\u0275elementStart"](18,"div",14),p["\u0275\u0275text"](19," Click to view Handover Takeover form details "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](20,"button",15),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](2).tohandover()})),p["\u0275\u0275text"](21," Handover Takeover Form "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"](2);p["\u0275\u0275advance"](9),p["\u0275\u0275textInterpolate1"](" Handover Takeover form has been ","accept"==e.status_type?"Approved":"Submitted"," "),p["\u0275\u0275advance"](3),p["\u0275\u0275property"]("ngForOf",null==e.handoverTakenoverDetails?null:e.handoverTakenoverDetails.data),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngForOf",null==e.handoverDetails?null:e.handoverDetails.data)}}function Rn(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"div",22),p["\u0275\u0275elementStart"](1,"div",23),p["\u0275\u0275elementStart"](2,"div",24),p["\u0275\u0275element"](3,"img",25),p["\u0275\u0275elementEnd"](),p["\u0275\u0275element"](4,"br"),p["\u0275\u0275elementStart"](5,"div",26),p["\u0275\u0275elementStart"](6,"h4"),p["\u0275\u0275text"](7,"No Handover Takeover form has been submitted by your manager"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](8,"p",27),p["\u0275\u0275text"](9," We will let you know once the action has been taken "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]())}function zn(e,t){if(1&e&&(p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275template"](1,qn,22,3,"div",1),p["\u0275\u0275template"](2,Rn,10,0,"div",4),p["\u0275\u0275elementContainerEnd"]()),2&e){const e=p["\u0275\u0275nextContext"]();p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",e.displayCondition&&(null==e.handoverTakenoverDetails?null:e.handoverTakenoverDetails.data.length)>0&&(null==e.handoverDetails?null:e.handoverDetails.data.length)>0),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",0==(null==e.handoverDetails?null:e.handoverDetails.data.length))}}let Yn=(()=>{class e{constructor(e,t,n,i,o,r,a){this._router=e,this.route=t,this._snackBar=n,this.edreqService=i,this._toaster=o,this._loginService=r,this.formBuilder=a,this.subs=new j.a,this.displayCondition=!1,this.currentDate=new Date}ngOnInit(){return Object(r.c)(this,void 0,void 0,(function*(){this.page_loader=!0,this.status_type=this.edreqService.status_type,this.handoverTakenoverDetails=yield this.viewHandoverTakenoverSurvey(),this.handoverApproverData=yield this.getHandoverApproverData(),this.handoverDetails=yield this.viewHandoverSurvey(),this.page_loader=!1}))}getHandoverApproverData(){let e={params:{exit_employee_id:this.edreqService.empExId}};return new Promise((t,n)=>{this.subs.sink=this.edreqService.getHandoverApproverData(e).subscribe(e=>{e.err?(this._toaster.showWarning("Error","Something went wrong."),n("Error: Something went wrong.")):(this.displayCondition=!0,t(e.data))},e=>{this._toaster.showError("Error","Failed to retrieve Approver details",2e3),console.log(e),n(e)})})}viewHandoverSurvey(){let e={params:{user_aid:this._loginService.getProfile().profile.aid,exit_employee_id:this.edreqService.empExId,form_name:"Handover_takeover",current_date:d()(this.currentDate).format("YYYY-MM-DD")}};return new Promise((t,n)=>{this.subs.sink=this.edreqService.viewHandoverSurvey(e).subscribe(e=>{e.err?(this._toaster.showWarning("Error","Something went wrong."),n("Error: Something went wrong.")):(this.displayCondition=!0,t(e))},e=>{this._toaster.showError("Error","Failed to retrieve Managerial Approval!",2e3),console.log(e),n(e)})})}viewHandoverTakenoverSurvey(){let e={params:{user_aid:this._loginService.getProfile().profile.aid,exit_employee_id:this.edreqService.empExId,current_date:d()(this.currentDate).format("YYYY-MM-DD")}};return new Promise((t,n)=>{this.subs.sink=this.edreqService.viewHandoverTakenoverSurvey(e).subscribe(e=>{e.err?this._toaster.showWarning("Error","Something went wrong."):(this.displayCondition=!0,t(e))},e=>{this._toaster.showError("Error","Failed to retrieve Handover Takeover Form !",2e3),console.log(e),n(e)})})}tohandover(){this._router.navigate(["../hand-over"],{relativeTo:this.route})}approved(){console.log("approved")}}return e.\u0275fac=function(t){return new(t||e)(p["\u0275\u0275directiveInject"](o.g),p["\u0275\u0275directiveInject"](o.a),p["\u0275\u0275directiveInject"](Q.a),p["\u0275\u0275directiveInject"](f.a),p["\u0275\u0275directiveInject"](h.a),p["\u0275\u0275directiveInject"](g.a),p["\u0275\u0275directiveInject"](H.i))},e.\u0275cmp=p["\u0275\u0275defineComponent"]({type:e,selectors:[["app-takeover-form"]],decls:3,vars:2,consts:[[1,"container-fluid","RequestForm","formpage"],[4,"ngIf"],[1,"d-flex","align-items-center","justify-content-center","mt-3",2,"height","77vh","margin-top","0rem !important"],["matTooltip","Please wait...","diameter","30"],["class","error-message",4,"ngIf"],[2,"margin-top","2vh","width","98%","margin-left","1%","max-height","77vh"],[1,"handover-takeover-note"],[2,"display","flex","justify-content","space-between"],[1,"handover-approve",3,"click"],[2,"color","#52c41a","margin-top","1%"],[1,"firstrow",2,"display","flex","justify-content","flex-start","column-gap","0%"],[2,"background-color","#f6f6f6","width","60%","margin-top","2%"],["class","takenover-details-box",4,"ngFor","ngForOf"],[1,"conclude-stat-box"],[1,"conclude-statement"],[1,"to-handoverform",3,"click"],[1,"takenover-details-box"],[2,"display","flex","justify-content","space-between","width","100%"],[1,"details-from-manager"],[1,"red-head"],[1,"takeover-head"],[1,"takeover-detail"],[1,"error-message"],[1,"error-contents"],[1,"error-image"],["src","https://assets.kebs.app/images/no-approvers-found.png","alt","Manager not yet approved",2,"width","250px"],[1,"error-text"],[1,"d-flex","justify-content-center"]],template:function(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"div",0),p["\u0275\u0275template"](1,kn,3,0,"ng-container",1),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](2,zn,3,2,"ng-container",1)),2&e&&(p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",t.page_loader),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!t.page_loader))},directives:[i.NgIf,F.c,x.a,u.a,v.a,i.NgForOf],styles:[".handover-takeover-note[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;padding:1rem;overflow-Y:scroll;height:75vh;overflow-x:hidden}.handover-takeover-note[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-weight:500;font-size:16px;line-height:24px;display:flex;align-items:center;text-transform:capitalize;color:#45546e;flex:none;order:0;flex-grow:0}.handover-takeover-note[_ngcontent-%COMP%]   .handover-approve[_ngcontent-%COMP%]{border:1px solid #52c41a;text-transform:capitalize;height:38px;display:flex;line-height:33px;padding:3px 10px;justify-content:center;font-weight:400;font-size:12px;letter-spacing:.02em;background:rgba(0,148,50,.07);border-radius:4px}.handover-takeover-note[_ngcontent-%COMP%]   .headers-input[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;font-weight:500;font-size:13px;line-height:24px;letter-spacing:.02em;text-transform:capitalize;color:#6e7b8f;flex:none;order:0;flex-grow:0}.handover-takeover-note[_ngcontent-%COMP%]   .takenover-details-box[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal}.handover-takeover-note[_ngcontent-%COMP%]   .takenover-details-box[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{font-weight:500;font-size:13px;line-height:24px;display:flex;align-items:center;text-transform:capitalize;color:#45546e;flex:none;order:0;flex-grow:0}.handover-takeover-note[_ngcontent-%COMP%]   .takenover-details-box[_ngcontent-%COMP%]   .red-head[_ngcontent-%COMP%]{font-weight:500;font-size:14px;line-height:24px;display:flex;align-items:center;text-transform:capitalize;color:#ff3a46}.handover-takeover-note[_ngcontent-%COMP%]   .takenover-details-box[_ngcontent-%COMP%]   .takeover-head[_ngcontent-%COMP%]{font-weight:500;font-size:12px;line-height:24px;letter-spacing:.02em;text-transform:capitalize;color:#6e7b8f;flex:none;order:0;flex-grow:0}.handover-takeover-note[_ngcontent-%COMP%]   .takenover-details-box[_ngcontent-%COMP%]   .takeover-detail[_ngcontent-%COMP%]{font-weight:400;font-size:12px;line-height:24px;letter-spacing:.02em;color:#000;flex:none;order:0;flex-grow:0}.handover-takeover-note[_ngcontent-%COMP%]   .conclude-stat-box[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal}.handover-takeover-note[_ngcontent-%COMP%]   .conclude-stat-box[_ngcontent-%COMP%]   .conclude-statement[_ngcontent-%COMP%]{font-weight:500;font-size:13px;line-height:24px;letter-spacing:.02em;color:#6e7b8f}.handover-takeover-note[_ngcontent-%COMP%]   .conclude-stat-box[_ngcontent-%COMP%]   .to-handoverform[_ngcontent-%COMP%]{border:1px solid #6b7a99;background-color:#fff;border-radius:5px;text-transform:capitalize;color:#45546e;height:35px;align-items:center;display:flex;line-height:33px;padding:3px 10px;justify-content:center;width:160px;font-weight:550;font-size:12px;letter-spacing:-.02em}.error-message[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;font-weight:400;font-size:16px;margin-top:43px;width:50%;display:contents;letter-spacing:.02em;color:#6e7b8f;justify-content:center;text-align:center}.error-message[_ngcontent-%COMP%]   .error-contents[_ngcontent-%COMP%]{display:block;padding:8rem}  .mat-card:not([class*=mat-elevation-z]){box-shadow:none!important}.details-from-manager[_ngcontent-%COMP%]{margin-top:1%;width:100%}.meeting-planner[_ngcontent-%COMP%]{line-height:24px;align-items:center;color:#45546e}.back-btn[_ngcontent-%COMP%], .meeting-planner[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;font-weight:500;font-size:14px;display:flex;text-transform:capitalize}.back-btn[_ngcontent-%COMP%]{line-height:16px;letter-spacing:.02em;color:#000;border:none;background:none;border-radius:4px}.back-btn[_ngcontent-%COMP%], .handover-approve[_ngcontent-%COMP%]{height:40px;padding:3px 10px;justify-content:flex-start}.handover-approve[_ngcontent-%COMP%]{border:1px solid #52c41a;text-transform:capitalize;display:flex;line-height:33px;font-weight:400;font-size:12px;letter-spacing:-.02em;background:rgba(0,148,50,.07);border-radius:4px}  .mat-card:not([class*=mat-elevation-z]){box-shadow:none}.docs-header[_ngcontent-%COMP%]{font-size:12px;text-transform:capitalize}.docs-header[_ngcontent-%COMP%], .option-handover[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;font-weight:400;line-height:24px;display:flex;align-items:center;letter-spacing:.02em;color:#000}.option-handover[_ngcontent-%COMP%]{font-size:14px}.ScrollStyle[_ngcontent-%COMP%]{max-height:64vh;overflow-y:scroll}  .input{outline:none!important;box-shadow:none!important}.option-handoverform[_ngcontent-%COMP%], .option-handoverform[_ngcontent-%COMP%]   .feedback-to-manager[_ngcontent-%COMP%]{display:flex;justify-content:space-around;align-items:center}.option-handoverform[_ngcontent-%COMP%]   .feedback-to-manager[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;font-weight:400;font-size:14px;line-height:24px;width:100%;letter-spacing:.02em;color:#000}.option-handoverform[_ngcontent-%COMP%]   .feedback-to-manager[_ngcontent-%COMP%]   .remarks-textfield[_ngcontent-%COMP%]{display:flex;gap:2%;align-items:center;width:50%;margin-left:1%}"]}),e})();function Nn(e,t){1&e&&(p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275elementStart"](1,"div",2),p["\u0275\u0275element"](2,"mat-spinner",3),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementContainerEnd"]())}function An(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div"),p["\u0275\u0275elementStart"](1,"button",11),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](3).requestResignaion()})),p["\u0275\u0275text"](2," Request Resignation "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}}function Tn(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div"),p["\u0275\u0275elementStart"](1,"button",11),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](3).withdrawDialog()})),p["\u0275\u0275text"](2," Withdraw Resignation "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}}function Vn(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",20),p["\u0275\u0275elementStart"](1,"div",21),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](4).showDetailStatus()})),p["\u0275\u0275elementStart"](2,"mat-icon",22),p["\u0275\u0275text"](3," chevron_right "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}}function Wn(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",23),p["\u0275\u0275elementStart"](1,"div",24),p["\u0275\u0275elementStart"](2,"h4"),p["\u0275\u0275text"](3,"Reason for withdrawal"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](4,"p"),p["\u0275\u0275text"](5),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"]().$implicit;p["\u0275\u0275advance"](5),p["\u0275\u0275textInterpolate"](e.withdrawn_reason)}}const Hn=function(e){return{color:e}};function Bn(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",12),p["\u0275\u0275elementStart"](1,"div",13),p["\u0275\u0275elementStart"](2,"div",14),p["\u0275\u0275elementStart"](3,"div",15),p["\u0275\u0275text"](4,"Request"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](5,"div",15),p["\u0275\u0275text"](6,"Submitted Date"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](7,"div",15),p["\u0275\u0275text"](8,"Reverted"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](9,"div",15),p["\u0275\u0275text"](10,"Status"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](11,Vn,4,0,"div",16),p["\u0275\u0275elementStart"](12,"div",14),p["\u0275\u0275elementStart"](13,"div",17),p["\u0275\u0275text"](14,"Resignation"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](15,"div",17),p["\u0275\u0275text"](16),p["\u0275\u0275pipe"](17,"date"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](18,"div",17),p["\u0275\u0275text"](19),p["\u0275\u0275pipe"](20,"date"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](21,"div",18),p["\u0275\u0275text"](22),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](23,Wn,6,1,"div",19),p["\u0275\u0275elementEnd"](),p["\u0275\u0275element"](24,"hr"),p["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=p["\u0275\u0275nextContext"](3);p["\u0275\u0275advance"](11),p["\u0275\u0275property"]("ngIf",!e.is_withdrawn&&!e.is_exit_complete&&n.login_aid==n.role_aid),p["\u0275\u0275advance"](5),p["\u0275\u0275textInterpolate1"](" ",p["\u0275\u0275pipeBind2"](17,6,e.submitted_date,"dd-MMM-YYYY")," "),p["\u0275\u0275advance"](3),p["\u0275\u0275textInterpolate1"](" ","--"!==e.reverted_date?p["\u0275\u0275pipeBind2"](20,9,e.reverted_date,"dd-MMM-YYYY"):"--"," "),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngStyle",p["\u0275\u0275pureFunction1"](12,Hn,e.color)),p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" ",e.status," "),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",1==e.is_withdrawn)}}function Ln(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div"),p["\u0275\u0275elementStart"](1,"mat-card",5),p["\u0275\u0275elementStart"](2,"div"),p["\u0275\u0275elementStart"](3,"div",6),p["\u0275\u0275elementStart"](4,"div",7),p["\u0275\u0275element"](5,"app-user-image",8),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](6,An,3,0,"div",1),p["\u0275\u0275template"](7,Tn,3,0,"div",1),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](8,"div",9),p["\u0275\u0275template"](9,Bn,25,14,"div",10),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](2);p["\u0275\u0275advance"](5),p["\u0275\u0275property"]("id",e.empOid),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",0!=(null==e.withdrawnstatus||null==e.withdrawnstatus.data[0]?null:e.withdrawnstatus.data[0].is_withdrawn)&&e.login_aid==e.role_aid),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf","Rejected"==(null==e.withdrawnstatus||null==e.withdrawnstatus.data[0]?null:e.withdrawnstatus.data[0].status)&&e.login_aid==e.role_aid),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngForOf",null==e.withdrawnstatus?null:e.withdrawnstatus.data)}}function $n(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"div",25),p["\u0275\u0275elementStart"](1,"p",26),p["\u0275\u0275text"](2,"No Requests Found!"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](3,"p",27),p["\u0275\u0275text"](4,"But That's okay, you can raise a request at anytime"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](5,"p",27),p["\u0275\u0275text"](6,"Clicking the button provided on the right side"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]())}function Gn(e,t){if(1&e&&(p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275template"](1,Ln,10,4,"div",1),p["\u0275\u0275template"](2,$n,7,0,"div",4),p["\u0275\u0275elementContainerEnd"]()),2&e){const e=p["\u0275\u0275nextContext"]();p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",e.displayCondition&&(null==e.withdrawnstatus?null:e.withdrawnstatus.data.length)>0),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",0==(null==e.withdrawnstatus||null==e.withdrawnstatus.data?null:e.withdrawnstatus.data.length))}}const Kn=[{path:"",component:S,children:[{path:"",component:P},{path:"ed-home-page",component:D,children:[{path:"",redirectTo:"know-the-process"},{path:"no-request",component:P},{path:"know-the-process",component:W,data:{breadcrumb:"1.Know the process"}},{path:"terms-and-conditions",component:we,data:{breadcrumb:"2. Terms and Conditions,"}},{path:"resignation-request",component:ye,data:{breadcrumb:"3. Resignation Request"}}]},{path:"detail-resignation-page",component:vt,data:{breadcrumb:"Resignation Status"}},{path:"manageral-approval",component:Te,data:{breadcrumb:"Manageral Approval"}},{path:"ip-signout",component:It,data:{breadcrumb:"IP Signout Form"}},{path:"ip-signout-status",component:(()=>{class e{constructor(e,t){this._router=e,this.route=t,this.headername=["IP Signout"],this.matIconDone=["done_all"],this.appoveStatus=["IP Signout form has been Submitted"],this.backIcon=["arrow_back"]}ngOnInit(){}backtoipsignup(){this._router.navigate(["../ip-signout"],{relativeTo:this.route})}backtodetailed(){this._router.navigate(["../detail-resignation-page"],{relativeTo:this.route})}downloadIPForm(){}}return e.\u0275fac=function(t){return new(t||e)(p["\u0275\u0275directiveInject"](o.g),p["\u0275\u0275directiveInject"](o.a))},e.\u0275cmp=p["\u0275\u0275defineComponent"]({type:e,selectors:[["app-ip-signout-status"]],decls:20,vars:4,consts:[[1,"ip-status-comp"],[1,"top-bar"],[1,"back-btn",3,"click"],[1,"back-icon"],[1,"status-btn"],[1,"header-ipsigout"],[1,"handover-approve"],[1,"done-icon"],[1,"edit-ipsignup"],[1,"edit-header"],[1,"edit-btn",3,"click"]],template:function(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"mat-card",0),p["\u0275\u0275elementStart"](1,"div",1),p["\u0275\u0275elementStart"](2,"button",2),p["\u0275\u0275listener"]("click",(function(){return t.backtodetailed()})),p["\u0275\u0275elementStart"](3,"mat-icon",3),p["\u0275\u0275text"](4),p["\u0275\u0275elementEnd"](),p["\u0275\u0275text"](5," Back "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275element"](6,"hr"),p["\u0275\u0275elementStart"](7,"div",4),p["\u0275\u0275elementStart"](8,"h6",5),p["\u0275\u0275text"](9),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](10,"button",6),p["\u0275\u0275elementStart"](11,"mat-icon",7),p["\u0275\u0275text"](12),p["\u0275\u0275elementEnd"](),p["\u0275\u0275text"](13),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](14,"div",8),p["\u0275\u0275elementStart"](15,"div",9),p["\u0275\u0275text"](16,"Edit your IP Signout Form"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275element"](17,"br"),p["\u0275\u0275elementStart"](18,"button",10),p["\u0275\u0275listener"]("click",(function(){return t.backtoipsignup()})),p["\u0275\u0275text"](19,"Edit"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e&&(p["\u0275\u0275advance"](4),p["\u0275\u0275textInterpolate"](t.backIcon),p["\u0275\u0275advance"](5),p["\u0275\u0275textInterpolate"](t.headername),p["\u0275\u0275advance"](3),p["\u0275\u0275textInterpolate"](t.matIconDone),p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" ",t.appoveStatus," "))},directives:[u.a,v.a],styles:[".ip-status-comp[_ngcontent-%COMP%]{margin:1% 2%;width:96%;height:76vh}.ip-status-comp[_ngcontent-%COMP%]   hr[_ngcontent-%COMP%]{margin:0 0 1rem;border:0;border-top:1px solid rgba(0,0,0,.1)}.ip-status-comp[_ngcontent-%COMP%]   .top-bar[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]{font-family:Roboto;font-weight:500;font-size:14px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;color:#000;border:none;height:40px;display:flex;padding:3px 10px;justify-content:flex-start;background:none;border-radius:4px}.ip-status-comp[_ngcontent-%COMP%]   .top-bar[_ngcontent-%COMP%]   .back-btn[_ngcontent-%COMP%]   .back-icon[_ngcontent-%COMP%]{color:#5e5d5d;font-size:15px}.ip-status-comp[_ngcontent-%COMP%]   .status-btn[_ngcontent-%COMP%]{padding:1rem 2rem}.ip-status-comp[_ngcontent-%COMP%]   .status-btn[_ngcontent-%COMP%]   .header-ipsigout[_ngcontent-%COMP%]{font-family:Roboto;font-weight:500;font-size:12px;line-height:14px;letter-spacing:.02em;text-transform:capitalize}.ip-status-comp[_ngcontent-%COMP%]   .status-btn[_ngcontent-%COMP%]   .handover-approve[_ngcontent-%COMP%]{background:rgba(255,185,6,.07);border:1px solid #ffbd3d;text-transform:capitalize;height:40px;display:flex;line-height:33px;padding:3px 10px;justify-content:flex-start;font-weight:400;font-size:12px;letter-spacing:.02em;border-radius:4px;color:#1b2140;column-gap:6px;align-items:center}.ip-status-comp[_ngcontent-%COMP%]   .status-btn[_ngcontent-%COMP%]   .handover-approve[_ngcontent-%COMP%]   .done-icon[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;font-size:20px;color:#ffb906}.ip-status-comp[_ngcontent-%COMP%]   .edit-ipsignup[_ngcontent-%COMP%]{padding:2rem;font-family:Roboto;font-weight:500}.ip-status-comp[_ngcontent-%COMP%]   .edit-ipsignup[_ngcontent-%COMP%]   .edit-header[_ngcontent-%COMP%]{font-size:12px;line-height:14px;letter-spacing:.02em;text-transform:capitalize;color:#7d838b}.ip-status-comp[_ngcontent-%COMP%]   .edit-ipsignup[_ngcontent-%COMP%]   .download-btn[_ngcontent-%COMP%], .ip-status-comp[_ngcontent-%COMP%]   .edit-ipsignup[_ngcontent-%COMP%]   .edit-btn[_ngcontent-%COMP%]{border:1px solid #6b7a99;background-color:#fff;border-radius:5px;text-transform:capitalize;color:#45546e;height:35px;display:flex;justify-content:center;align-items:center;width:70px;font-size:12px;letter-spacing:.02em}.ip-status-comp[_ngcontent-%COMP%]   .edit-ipsignup[_ngcontent-%COMP%]   .download-btn[_ngcontent-%COMP%]   .download-icon[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;font-size:20px;color:#45546e}"]}),e})(),data:{breadcrumb:"IP Signout Form Status"}},{path:"hand-over",component:In,data:{breadcrumb:"Handover Info"}},{path:"take-over",component:Yn,data:{breadcrumb:"Takeover Info"}},{path:"exit-survey",component:Ht,data:{breadcrumb:"Exit Survey"}},{path:"dept-clearance",component:fn,data:{breadcrumb:"Departmental Clearance"}},{path:"resignation-withdrawn",component:(()=>{class e{constructor(e,t,n,i,o,r,a){this._router=e,this.route=t,this._edService=n,this._toaster=i,this._loginService=o,this.edreqService=r,this.dialog=a,this.currentDate=new Date,this.displayCondition=!1,this.subs=new j.a}ngOnInit(){return Object(r.c)(this,void 0,void 0,(function*(){this.page_loader=!0,this.login_aid=yield this._loginService.getProfile().profile.aid,this.role_aid=this._edService.ed_aid;let e=yield this._loginService.getProfile().profile.aid;this._edService.ed_aid?(console.log("user Aid in withdrawn page",this._edService.ed_aid),this.empOid=yield this._loginService.getProfile().profile.oid,this.withdrawnstatus=yield this.fetchSubmittedExitRequestWithdraw(),this.withdrawnstatus.length>0&&this._router.navigateByUrl("/main/employee-central/employeeCentralDetail/"+e+"/request")):this._router.navigateByUrl("/main/employee-central/employeeCentralDetail/"+e+"/overview/profile"),this.page_loader=!1}))}fetchSubmittedExitRequestWithdraw(){let e={params:{user_aid:this._edService.ed_aid,current_date:d()(this.currentDate).format("YYYY-MM-DD")}};return new Promise((t,n)=>{this.subs.sink=this.edreqService.fetchSubmittedExitRequestWithdraw(e).subscribe(e=>{e.err?this._toaster.showWarning("Error","Something went wrong."):(this.displayCondition=!0,t(e))},e=>{this._toaster.showError("Error","Failed to retrieve Resignation Withdrawn Details !",2e3),console.log(e),n(e)})})}showDetailStatus(){this._router.navigate(["../detail-resignation-page"],{relativeTo:this.route})}requestResignaion(){this._router.navigate(["../ed-home-page"],{relativeTo:this.route})}withdrawDialog(){var e;this.dialog.open(Ee,{disableClose:!0,height:"495px",width:"350px",data:{emp_Id:null===(e=this.withdrawnstatus)||void 0===e?void 0:e.data[0].exit_employee_id}}).afterClosed().subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){e||window.location.reload()})))}}return e.\u0275fac=function(t){return new(t||e)(p["\u0275\u0275directiveInject"](o.g),p["\u0275\u0275directiveInject"](o.a),p["\u0275\u0275directiveInject"](m.a),p["\u0275\u0275directiveInject"](h.a),p["\u0275\u0275directiveInject"](g.a),p["\u0275\u0275directiveInject"](f.a),p["\u0275\u0275directiveInject"](B.b))},e.\u0275cmp=p["\u0275\u0275defineComponent"]({type:e,selectors:[["app-resignation-withdrawn-page"]],decls:3,vars:2,consts:[[1,"container-fluid","RequestForm","formpage"],[4,"ngIf"],[1,"loaDer"],["matTooltip","Please wait...","diameter","30"],["class","error-message",4,"ngIf"],[1,"whole-page"],[1,"top-bar"],[1,"mat-icon"],["content-type","template","placement","top","imgWidth","28px","imgHeight","28px",3,"id"],[1,"scrollStyle"],["class","status",4,"ngFor","ngForOf"],[1,"request-resignation-btn",3,"click"],[1,"status"],[1,"current-status"],[1,"row"],[1,"current-status-header","col"],["class","forward-btn col",4,"ngIf"],[1,"current-status-details","col"],[1,"status-history-details","col",2,"font-family","Roboto","font-style","normal","font-weight","500","font-size","13px","line-height","16px","letter-spacing","0.02em","text-transform","capitalize",3,"ngStyle"],["class","reason-for-withdrawl",4,"ngIf"],[1,"forward-btn","col"],[1,"redirect-resignation-btn",3,"click"],[1,"m-forward-btn"],[1,"reason-for-withdrawl"],[1,"reason-for-withdrawl-header"],[1,"error-message"],[1,"norequest"],[1,"msg"]],template:function(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"div",0),p["\u0275\u0275template"](1,Nn,3,0,"ng-container",1),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](2,Gn,3,2,"ng-container",1)),2&e&&(p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",t.page_loader),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!t.page_loader))},directives:[i.NgIf,F.c,x.a,u.a,q.a,i.NgForOf,i.NgStyle,v.a],pipes:[i.DatePipe],styles:[".top-bar[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:1%}.top-bar[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%]{margin-top:-1%;font-size:70px}.top-bar[_ngcontent-%COMP%]   .request-resignation-btn[_ngcontent-%COMP%]{border:1px solid #fff;border-radius:5px;text-transform:capitalize;color:#fff;background:#ee4961;width:150px;height:38px;display:flex;line-height:33px;justify-content:center;font-weight:500;font-size:12px;letter-spacing:.01em;flex:none;order:0;flex-grow:0}.status[_ngcontent-%COMP%], .top-bar[_ngcontent-%COMP%]   .request-resignation-btn[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal}.status[_ngcontent-%COMP%]   .current-status[_ngcontent-%COMP%]{margin-top:1%}.status[_ngcontent-%COMP%]   .current-status[_ngcontent-%COMP%]   .forward-btn[_ngcontent-%COMP%]{display:flex;justify-content:flex-end;align-items:center;padding-right:1rem;height:1vh}.status[_ngcontent-%COMP%]   .current-status[_ngcontent-%COMP%]   .forward-btn[_ngcontent-%COMP%]   .redirect-resignation-btn[_ngcontent-%COMP%]{cursor:pointer;display:flex;justify-content:center;align-items:center;border-radius:50%;width:30px;height:30px;background:#f3f5fc;font-size:35px;opacity:.7}.status[_ngcontent-%COMP%]   .current-status[_ngcontent-%COMP%]   .forward-btn[_ngcontent-%COMP%]   .redirect-resignation-btn[_ngcontent-%COMP%]   .m-forward-btn[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}.status[_ngcontent-%COMP%]   .current-status[_ngcontent-%COMP%]   .current-status-header[_ngcontent-%COMP%]{font-weight:400;font-size:11px;line-height:16px;letter-spacing:.02em;text-transform:uppercase;color:#b9c0ca}.status[_ngcontent-%COMP%]   .current-status[_ngcontent-%COMP%]   .current-status-details[_ngcontent-%COMP%]{font-weight:500;font-size:12px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;color:#526179}.status[_ngcontent-%COMP%]   .status-history[_ngcontent-%COMP%]   .status-history-header[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;font-weight:400;font-size:11px;line-height:16px;letter-spacing:.02em;text-transform:uppercase;color:#b9c0ca}.status[_ngcontent-%COMP%]   .status-history[_ngcontent-%COMP%]   .status-history-details[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;font-weight:500;font-size:12px;line-height:16px;letter-spacing:.02em;text-transform:capitalize}.status[_ngcontent-%COMP%]   hr[_ngcontent-%COMP%]{border:1px solid #d4d6d8;width:97%}.error-message[_ngcontent-%COMP%]{font-family:Roboto;justify-content:center;text-align:center;margin-top:20%;font-style:normal;text-transform:capitalize;display:block;align-items:center;font-size:18px;font-weight:500}.error-message[_ngcontent-%COMP%]   .norequest[_ngcontent-%COMP%]{color:#111434;font-size:15px;font-weight:500}.error-message[_ngcontent-%COMP%]   .msg[_ngcontent-%COMP%]{color:rgba(128,121,121,.8901960784313725);font-size:13px}.whole-page[_ngcontent-%COMP%]{width:95%;margin-top:17px;margin-left:3%;height:77vh}.reason-for-withdrawl[_ngcontent-%COMP%]{margin-top:2%;background:#f7f9fb;margin-left:0;width:97%;font-family:Roboto;font-style:normal}.reason-for-withdrawl[_ngcontent-%COMP%]   .reason-for-withdrawl-header[_ngcontent-%COMP%]{margin-top:2%;padding:1.3rem}.reason-for-withdrawl[_ngcontent-%COMP%]   .reason-for-withdrawl-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#000}.reason-for-withdrawl[_ngcontent-%COMP%]   .reason-for-withdrawl-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .reason-for-withdrawl[_ngcontent-%COMP%]   .reason-for-withdrawl-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-weight:400;font-size:13px;line-height:20px;letter-spacing:.02em;flex-wrap:wrap;white-space:normal;overflow:hidden;text-overflow:ellipsis}.reason-for-withdrawl[_ngcontent-%COMP%]   .reason-for-withdrawl-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#526179}.redirect-resignation-btn[_ngcontent-%COMP%]   [_ngcontent-%COMP%]:hover{cursor:pointer;display:flex;justify-content:center;align-items:center;border-radius:50%;width:30px;height:30px;background:#d7d7d7;color:#a4a8ac;opacity:1;transition:background-color .1s linear}.scrollStyle[_ngcontent-%COMP%]{max-height:65vh;overflow-y:scroll}.loaDer[_ngcontent-%COMP%]{height:77vh;margin-top:0!important;display:flex;justify-content:center;align-items:center}"]}),e})(),data:{breadcrumb:"Resignation Process Status"}}]}];let Jn=(()=>{class e{}return e.\u0275mod=p["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=p["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[o.k.forChild(Kn)],o.k]}),e})();var Un=n("Xi0T"),Xn=n("wZkO"),Qn=n("qFsG"),Zn=n("d3UM"),ei=n("xHqg"),ti=n("jW8c");let ni=(()=>{class e{}return e.\u0275mod=p["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=p["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,Jn,u.d,v.b,Xn.g,x.b,Xn.g,be.b,L.e,Zn.d,ei.f,H.E,u.d,Qn.c,v.b,H.p,Q.b,Un.a,B.g,F.b,ti.a,Bt.b]]}),e})()}}]);