(window.webpackJsonp=window.webpackJsonp||[]).push([[724,634,765,821,822,858,861,977,981,983,987,990,991],{HmYF:function(e,t,i){"use strict";i.d(t,"a",(function(){return d}));var r=i("mrSG"),n=i("Iab2"),a=i("EUZL"),o=i("wd/R"),s=i("xG9w"),l=i("fXoL");let d=(()=>{class e{constructor(){this.formatColumn=(e,t,i)=>{const r=a.utils.decode_range(e["!ref"]);for(let n=r.s.r+1;n<=r.e.r;++n){const r=a.utils.encode_cell({r:n,c:t});e[r]&&e[r].v&&(e[r].t="d",e[r].z=i)}}}exportAsExcelFile(e,t,i,r,n){console.log("Excel to JSON Service",e);const o=a.utils.json_to_sheet(e);if(n&&n.length){const e=a.utils.sheet_to_json(o,{header:1}).shift();for(const t of n){const i=e.indexOf(t.fieldKey);this.formatColumn(o,i,t.fieldFormat)}}null==i&&(i=[]),null==r&&(r="DD-MM-YYYY"),this.formatExcelDateData(o,i,r);const s=a.write({Sheets:{data:o},SheetNames:["data"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(s,t)}formatExcelDateData(e,t,i){for(let a of Object.keys(e))if(null!=e[a]&&null!=e[a].t&&null!=e[a].v&&o(e[a].v,i,!0).isValid()){let r=a.replace(/[0-9]/g,"")+"1";0==s.where(t,{value:e[r].v}).length&&null!=e[r]&&null!=e[r].t&&t.push({value:e[r].v,format:i})}let r=[],n=1;for(let a of t)for(let t of Object.keys(e)){let i=parseInt(t.replace(/[^0-9]/g,""));i>n&&(n=i),null!=e[t]&&null!=e[t].v&&e[t].v==a.value&&r.push({value:t.replace(/[0-9]/g,""),format:a.format})}for(let a of r)for(let t=2;t<=n;t++)null!=e[a.value+""+t]&&null!=e[a.value+""+t].t&&(e[a.value+""+t].t="d",null!=e[a.value+""+t].v&&"Invalid date"!=e[a.value+""+t].v?e[a.value+""+t].v=o(e[a.value+""+t].v,a.format).format("YYYY/MM/DD"):(console.log(e[a.value+""+t].t),e[a.value+""+t].v="",e[a.value+""+t].t="s"))}saveAsExcelFile(e,t){const i=new Blob([e],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8"});n.saveAs(i,t.concat(".xlsx"))}exportAsExcelFileWFH(e,t,i){const r=a.utils.json_to_sheet(e),n=a.utils.json_to_sheet(t),o=a.write({Sheets:{All_Approvals:r,Pending_Approvals:n},SheetNames:["All_Approvals","Pending_Approvals"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(o,i)}exportAsExcelFileForPayroll(e,t,i,r,n,o){const s=a.utils.json_to_sheet(e),l=a.utils.json_to_sheet(t),d=a.utils.json_to_sheet(i),c=a.utils.json_to_sheet(r),p=a.utils.json_to_sheet(n),h=a.write({Sheets:{Regular_Report:s,Intern_Report:l,Contract_Report:d,Perdiem_Report:c,RP_Report:p},SheetNames:["Regular_Report","Intern_Report","Contract_Report","Perdiem_Report","RP_Report"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(h,o)}exportAsCsvFileWithSheetName(e,t){return Object(r.c)(this,void 0,void 0,(function*(){let i=a.utils.book_new();for(let t of e){let e=a.utils.json_to_sheet(t.data);a.utils.book_append_sheet(i,e,t.sheetName)}let r=a.write(i,{bookType:"xlsx",type:"binary"});yield this.saveAsCsvFile(r,t)}))}saveAsCsvFile(e,t){return Object(r.c)(this,void 0,void 0,(function*(){const i=new Blob([yield this.s2ab(e)],{type:"application/octet-stream"});n.saveAs(i,t.concat(".csv"))}))}s2ab(e){return Object(r.c)(this,void 0,void 0,(function*(){for(var t=new ArrayBuffer(e.length),i=new Uint8Array(t),r=0;r<e.length;r++)i[r]=255&e.charCodeAt(r);return t}))}exportAsExcelFileWithCellMerge(e,t,i){const r=a.utils.json_to_sheet(e);r["!merges"]=i;const n=a.write({Sheets:{data:r},SheetNames:["data"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(n,t)}exportJsonToExcelWithMultipleSheets(e,t){return Object(r.c)(this,void 0,void 0,(function*(){let i=a.utils.book_new();for(let t of e){let e=a.utils.json_to_sheet(t.data);a.utils.book_append_sheet(i,e,t.sheetName)}let r=a.write(i,{bookType:"xlsx",type:"array"});this.saveAsExcelFile(r,t)}))}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275prov=l["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},NJ67:function(e,t,i){"use strict";i.d(t,"a",(function(){return r}));class r{constructor(){this.disabled=!1}onChange(e){}onTouched(e){}writeValue(e){this.value=e}registerOnChange(e){this.onChange=e}registerOnTouched(e){this.onTouched=e}setDisabledState(e){this.disabled=e}}},SnDH:function(e,t,i){"use strict";i.r(t),i.d(t,"HumanResourceModule",(function(){return ye}));var r=i("ofXK"),n=i("3Pt+"),a=i("NFeN"),o=i("d3UM"),s=i("bTqV"),l=i("kmnG"),d=i("qFsG"),c=i("iadO"),p=i("Xa2L"),h=i("wZkO"),m=i("Qu3c"),u=i("XhcP"),g=i("MutI"),f=i("TU8p"),v=i("/QRN"),S=i("v2UZ"),C=i("XfUL"),b=i("A5z7"),O=i("/1cH"),y=i("bSwM"),E=i("tyNb"),w=i("mrSG"),_=i("R0Ic"),x=i("jtHE"),D=i("XNiG"),I=i("1G5W"),A=i("xG9w"),P=i("Kj3r"),M=i("7pIB"),k=i("wd/R"),F=i.n(k),R=i("fXoL"),N=i("flaP"),T=i("HMJD"),j=i("JqCM"),U=i("LcQX"),G=i("XXEo"),B=i("GnQ3"),V=i("F97M"),H=i("BVzC"),q=i("HmYF"),z=i("1d+P"),L=i("f0Cb"),W=i("8SgF"),X=i("TmG/"),Q=i("FKr1"),J=i("xi/V"),Y=i("Wk3H");const K=["drawer"],$=["roleList"],Z=["statusList"];function ee(e,t){if(1&e&&(R["\u0275\u0275elementStart"](0,"mat-option",41),R["\u0275\u0275text"](1),R["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;R["\u0275\u0275property"]("value",e.role_id),R["\u0275\u0275advance"](1),R["\u0275\u0275textInterpolate"](e.role_description)}}function te(e,t){if(1&e&&(R["\u0275\u0275elementStart"](0,"div",14),R["\u0275\u0275elementStart"](1,"div",11),R["\u0275\u0275elementStart"](2,"mat-form-field",17),R["\u0275\u0275elementStart"](3,"mat-label"),R["\u0275\u0275text"](4,"System Role"),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementStart"](5,"mat-select",39),R["\u0275\u0275template"](6,ee,2,2,"mat-option",40),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"]()),2&e){const e=R["\u0275\u0275nextContext"]();R["\u0275\u0275advance"](6),R["\u0275\u0275property"]("ngForOf",e.roles)}}function ie(e,t){1&e&&(R["\u0275\u0275elementStart"](0,"div",14),R["\u0275\u0275elementStart"](1,"div",23),R["\u0275\u0275elementStart"](2,"mat-form-field",17),R["\u0275\u0275elementStart"](3,"mat-label"),R["\u0275\u0275text"](4,"Leave Balance (LB)"),R["\u0275\u0275elementEnd"](),R["\u0275\u0275element"](5,"input",42),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementStart"](6,"div",23),R["\u0275\u0275elementStart"](7,"mat-form-field",17),R["\u0275\u0275elementStart"](8,"mat-label"),R["\u0275\u0275text"](9,"Carry Forward (CF)"),R["\u0275\u0275elementEnd"](),R["\u0275\u0275element"](10,"input",43),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"]())}function re(e,t){if(1&e&&(R["\u0275\u0275elementStart"](0,"mat-option",41),R["\u0275\u0275text"](1),R["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;R["\u0275\u0275property"]("value",e.code),R["\u0275\u0275advance"](1),R["\u0275\u0275textInterpolate"](e.description)}}function ne(e,t){if(1&e&&(R["\u0275\u0275elementStart"](0,"mat-select",44),R["\u0275\u0275template"](1,re,2,2,"mat-option",40),R["\u0275\u0275elementEnd"]()),2&e){const e=R["\u0275\u0275nextContext"]();R["\u0275\u0275advance"](1),R["\u0275\u0275property"]("ngForOf",e.temp_inactive)}}function ae(e,t){if(1&e&&(R["\u0275\u0275elementStart"](0,"mat-option",41),R["\u0275\u0275text"](1),R["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;R["\u0275\u0275property"]("value",e.code),R["\u0275\u0275advance"](1),R["\u0275\u0275textInterpolate"](e.description)}}function oe(e,t){if(1&e&&(R["\u0275\u0275elementStart"](0,"mat-select",44),R["\u0275\u0275template"](1,ae,2,2,"mat-option",40),R["\u0275\u0275elementEnd"]()),2&e){const e=R["\u0275\u0275nextContext"]();R["\u0275\u0275advance"](1),R["\u0275\u0275property"]("ngForOf",e.temp_active)}}function se(e,t){1&e&&(R["\u0275\u0275elementStart"](0,"div",58),R["\u0275\u0275elementStart"](1,"span",59),R["\u0275\u0275text"](2,"Loading..."),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"]())}function le(e,t){1&e&&(R["\u0275\u0275elementStart"](0,"span"),R["\u0275\u0275elementStart"](1,"span",60),R["\u0275\u0275text"](2," done "),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"]())}function de(e,t){1&e&&(R["\u0275\u0275elementStart"](0,"span"),R["\u0275\u0275elementStart"](1,"span",60),R["\u0275\u0275text"](2," close "),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"]())}function ce(e,t){1&e&&(R["\u0275\u0275elementStart"](0,"span"),R["\u0275\u0275elementStart"](1,"span",60),R["\u0275\u0275text"](2," error "),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"]())}function pe(e,t){if(1&e&&(R["\u0275\u0275elementStart"](0,"div"),R["\u0275\u0275elementStart"](1,"strong"),R["\u0275\u0275text"](2),R["\u0275\u0275elementEnd"](),R["\u0275\u0275text"](3),R["\u0275\u0275pipe"](4,"number"),R["\u0275\u0275template"](5,se,3,0,"div",56),R["\u0275\u0275template"](6,le,3,0,"span",57),R["\u0275\u0275template"](7,de,3,0,"span",57),R["\u0275\u0275template"](8,ce,3,0,"span",57),R["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,i=R["\u0275\u0275nextContext"](2);R["\u0275\u0275advance"](2),R["\u0275\u0275textInterpolate"](null==e||null==e.file?null:e.file.name),R["\u0275\u0275advance"](1),R["\u0275\u0275textInterpolate1"](" (",R["\u0275\u0275pipeBind2"](4,6,(null==e||null==e.file?null:e.file.size)/1024/1024,".2")," MB) "),R["\u0275\u0275advance"](2),R["\u0275\u0275property"]("ngIf",i.isUpload),R["\u0275\u0275advance"](1),R["\u0275\u0275property"]("ngIf",!i.isUpload&&e.isSuccess),R["\u0275\u0275advance"](1),R["\u0275\u0275property"]("ngIf",e.isCancel),R["\u0275\u0275advance"](1),R["\u0275\u0275property"]("ngIf",!i.isUpload&&e.isError)}}function he(e,t){1&e&&(R["\u0275\u0275elementStart"](0,"span",61),R["\u0275\u0275text"](1," Drag and Drop your attachments here! "),R["\u0275\u0275elementEnd"]())}function me(e,t){if(1&e){const e=R["\u0275\u0275getCurrentView"]();R["\u0275\u0275elementStart"](0,"div",62),R["\u0275\u0275elementStart"](1,"div",63),R["\u0275\u0275elementStart"](2,"button",64),R["\u0275\u0275listener"]("click",(function(){return R["\u0275\u0275restoreView"](e),R["\u0275\u0275nextContext"](2).uploader.uploadAll()})),R["\u0275\u0275text"](3," Upload "),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementStart"](4,"button",65),R["\u0275\u0275listener"]("click",(function(){return R["\u0275\u0275restoreView"](e),R["\u0275\u0275nextContext"](2).uploader.uploadAll()}))("click",(function(){return R["\u0275\u0275restoreView"](e),R["\u0275\u0275nextContext"](2).uploader.clearQueue()})),R["\u0275\u0275text"](5," Clear "),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementStart"](6,"strong"),R["\u0275\u0275text"](7),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"]()}if(2&e){const e=R["\u0275\u0275nextContext"](2);R["\u0275\u0275advance"](2),R["\u0275\u0275property"]("disabled",!e.uploader.getNotUploadedItems().length),R["\u0275\u0275advance"](2),R["\u0275\u0275property"]("disabled",!e.uploader.queue.length),R["\u0275\u0275advance"](3),R["\u0275\u0275textInterpolate"](e.uploader.progress+"%")}}const ue=function(e){return{"nv-file-over":e}};function ge(e,t){if(1&e){const e=R["\u0275\u0275getCurrentView"]();R["\u0275\u0275elementStart"](0,"div",45),R["\u0275\u0275elementStart"](1,"div",46),R["\u0275\u0275listener"]("fileOver",(function(t){return R["\u0275\u0275restoreView"](e),R["\u0275\u0275nextContext"]().fileOverBase(t)})),R["\u0275\u0275elementStart"](2,"div",47),R["\u0275\u0275elementStart"](3,"div",48),R["\u0275\u0275template"](4,pe,9,9,"div",49),R["\u0275\u0275template"](5,he,2,0,"span",50),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementStart"](6,"div",51),R["\u0275\u0275elementStart"](7,"span",52),R["\u0275\u0275listener"]("click",(function(){return R["\u0275\u0275restoreView"](e),R["\u0275\u0275reference"](10).click()})),R["\u0275\u0275text"](8," cloud_upload "),R["\u0275\u0275element"](9,"input",53,54),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275template"](11,me,8,3,"div",55),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"]()}if(2&e){const e=R["\u0275\u0275nextContext"]();R["\u0275\u0275advance"](1),R["\u0275\u0275property"]("ngClass",R["\u0275\u0275pureFunction1"](6,ue,e.hasBaseDropZoneOver))("uploader",e.uploader),R["\u0275\u0275advance"](3),R["\u0275\u0275property"]("ngForOf",e.uploader.queue),R["\u0275\u0275advance"](1),R["\u0275\u0275property"]("ngIf",0==e.uploader.queue.length),R["\u0275\u0275advance"](4),R["\u0275\u0275property"]("uploader",e.uploader),R["\u0275\u0275advance"](2),R["\u0275\u0275property"]("ngIf",e.uploader.queue.length>0)}}function fe(e,t){if(1&e){const e=R["\u0275\u0275getCurrentView"]();R["\u0275\u0275elementStart"](0,"div",68),R["\u0275\u0275elementStart"](1,"span",69),R["\u0275\u0275listener"]("click",(function(){R["\u0275\u0275restoreView"](e);const i=t.index;return R["\u0275\u0275nextContext"](2).downloadFile(i)})),R["\u0275\u0275text"](2),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,i=t.index;R["\u0275\u0275advance"](1),R["\u0275\u0275property"]("matTooltip",null==e?null:e.fileName),R["\u0275\u0275advance"](1),R["\u0275\u0275textInterpolate2"](" ",i+1,". ",null==e?null:e.fileName," ")}}function ve(e,t){if(1&e&&(R["\u0275\u0275elementStart"](0,"div",45),R["\u0275\u0275elementStart"](1,"div",47),R["\u0275\u0275elementStart"](2,"div",63),R["\u0275\u0275elementStart"](3,"span",66),R["\u0275\u0275text"](4,"Candidate Attachments:"),R["\u0275\u0275elementEnd"](),R["\u0275\u0275template"](5,fe,3,3,"div",67),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"]()),2&e){const e=R["\u0275\u0275nextContext"]();R["\u0275\u0275advance"](5),R["\u0275\u0275property"]("ngForOf",e.attachment)}}const Se=function(){return{standalone:!0}},Ce=[{path:"",component:(()=>{class e{constructor(e,t,i,r,a,o,s,l,d,c,p,h){this.rolesService=e,this.hrService=t,this.spinner=i,this.utilityService=r,this.loginService=a,this.formBuilder=o,this.udrfService=s,this.graphApi=l,this.errorService=d,this.excelService=c,this.router=p,this._fileSaver=h,this.destroyed$=new x.a(1),this._onDestroy=new D.b,this._onAppApiCalled=new D.b,this.applicationId=27,this.allEmployees=[],this.employeeStartIndex=0,this.roleAccess=this.hrService.getAdminAccess(),this.allEmployeesAID=[],this.fullData=[],this.allEmployeesOID=[],this.initialList=[],this.sortBackupList=[],this.temp=[],this.roles=[],this.editValue=[],this.orgData=[],this.desgData=[],this.roleData=[],this.practiceData=[],this.user=this.loginService.getProfile().profile,this.searchTextControl=new n.j,this.spinnerIsVisible=!1,this.isClearVisible=!1,this.isloading=!1,this.noDataFlag=0,this.allUserRoles=[],this.role_id_array=[],this.filterIsApplied=!1,this.isFilterApplied=!1,this.isCreateScreen=!1,this.isEditScreen=!1,this.isCurrentAssociateAdmin=!1,this.isOrganizationHead=!1,this.filterList=[],this.filterVisible=!1,this.final_temp=[],this.statusNames=[],this.statusNames1=[],this.roleNamesOptions=[],this.selectedStatusOptions=[],this.selectedRoles=[],this.selectedDesgn=[],this.selectedOrg=[],this.selectedStatus=[],this.selectedStatus1=[],this.temp_active=[],this.temp_inactive=[],this.byName=!0,this.byID=!0,this.isSortApplied=!1,this.isBadgeVisible=!1,this.finalAttachments=[],this.attachment=[],this.isUpload=!1,this.UPLOAD_URL="/api/appraisal/configuration/uploadAppraisalAttachment",this.maxFileSize=10485760,this.udrfBodyColumns=[{item:"associate_id",header:"AID",isVisible:"true",type:"text",position:1,isActive:!0,colSize:"1",textClass:"value13Bold",headerTextClass:"text-center",filterId:3,sortOrder:"N",width:50,hasColumnClick:!1},{item:"employee_name",header:"Name",isVisible:"true",type:"profileImg",position:2,isActive:!0,colSize:"2",textClass:"value13Bold",headerTextClass:"text-center",filterId:13,sortOrder:"N",width:240,hasColumnClick:!1},{item:"desgn_name",header:"Designation",isVisible:"true",type:"text",position:3,isActive:!0,colSize:"2",textClass:"value13Bold",headerTextClass:"text-center",filterId:3,sortOrder:"N",width:200,hasColumnClick:!1},{item:"role_description",header:"System Role",isVisible:"true",type:"text",position:4,isActive:!0,colSize:"2",textClass:"value13Bold",headerTextClass:"text-center",filterId:3,sortOrder:"N",width:200,hasColumnClick:!1},{item:"org_name",header:"Organization",isVisible:"true",type:"text",position:5,isActive:!0,colSize:"2",textClass:"value13Bold",headerTextClass:"text-center",filterId:3,sortOrder:"N",width:240,hasColumnClick:!1},{item:"employee_email",header:"Email",isVisible:"true",type:"text",position:6,isActive:!0,colSize:"3",textClass:"value13Bold",headerTextClass:"text-center",filterId:3,sortOrder:"N",width:200,hasColumnClick:!1},{item:"is_active",header:"Status",isVisible:"true",type:"text",position:7,isActive:!0,colSize:"1",textClass:"value13Bold",headerTextClass:"text-center",filterId:3,sortOrder:"N",width:75,hasColumnClick:!1},{item:"is_bot_active",header:"Bot",isVisible:"true",type:"text",position:8,isActive:!0,colSize:"1",textClass:"value13Bold",headerTextClass:"text-center",filterId:3,sortOrder:"N",width:75,hasColumnClick:!1},{item:"assoc_group",header:"Ass.Group",isVisible:"true",type:"text",position:9,isActive:!0,colSize:"1",textClass:"value13Bold",headerTextClass:"text-center",filterId:3,sortOrder:"N",width:80,hasColumnClick:!1}],this.downloadFile=e=>{let t=this.attachment[e];this.hrService.getCandidateAttachmentFromS3(t.key).pipe(Object(I.a)(this._onDestroy)).subscribe(e=>Object(w.c)(this,void 0,void 0,(function*(){"S"==e.messType?"image/png"==t.type||"image/jpg"==t.type||"image/jpeg"==t.type||"application/pdf"==t.type?(window.open("").document.write(`<iframe width=100% height=100% src=data:${t.type};base64,${e.data.fileData}></iframe>`),this._fileSaver.saveAsFile(e.data.fileData,t.fileName,t.type)):this._fileSaver.saveAsFile(e.data.fileData,t.fileName,t.type):this.utilityService.showMessage(e.messText,"Dismiss")})),e=>{this.errorService.userErrorAlert(e.error.errorCode,"Error downloading file",e.error.errMessage)})},this.uploader=new M.d({url:this.UPLOAD_URL,authToken:"Bearer "+this.loginService.getToken(),disableMultipart:!1,maxFileSize:this.maxFileSize}),this.hasBaseDropZoneOver=!1,this.detectUploadChanges()}fileOverBase(e){this.hasBaseDropZoneOver=e}ngOnInit(){this.employeeStartIndex=0,this.rolesService.getAllUserRoles(this.user).subscribe(e=>{this.allUserRoles=e,this.role_id_array=A.where(this.allUserRoles,{application_id:27})},e=>{console.log(e)}),this.hrService.getAssocGroups().subscribe(e=>{this.empGroupData=e.data,this.changeStatus()},e=>{console.error(e)}),this.hrService.getFilterData().pipe(Object(I.a)(this.destroyed$)).subscribe(e=>{this.filterData=e},e=>{console.error(e)}),this.CreateEmployeeFormGroup=this.formBuilder.group({empName:["",n.H.required],empRole:["",n.H.required],empDesignation:["",n.H.required],empMail:["",n.H.required],candidateMail:["",n.H.email],empHrRole:["",n.H.required],empOrg:["",n.H.required],empNo:[""],empBatch:[""],empStatus:["",n.H.required],empGroup:["",n.H.required],empAid:["",n.H.required],empPractice:["",n.H.required],lb:["",n.H.required],cf:["",n.H.required]}),this.getOrgDesgData(),this.getPracticeList(),this.getAIDsAndOIDs(),this.hrService.getRoleGroup().then(e=>{this.roles=e},e=>{console.log(e)}),this.searchTextControl.valueChanges.pipe(Object(P.a)(1e3)).subscribe(e=>Object(w.c)(this,void 0,void 0,(function*(){e&&""!=e?(this.isloading=!0,this.spinnerIsVisible=!0,this.hrService.getEmployeeDataForSearch(e).subscribe(e=>{this.allEmployeesAID=e,this.spinnerIsVisible=!1,this.isloading=!1,this.isClearVisible=!0,this.noDataFlag=0,0==this.allEmployeesAID.length&&(this.noDataFlag=1)},e=>{console.log(e)})):(this.allEmployeesAID=this.fullData,this.noDataFlag=0)}))),this.CreateEmployeeFormGroup.get("empOrg").valueChanges.subscribe(e=>Object(w.c)(this,void 0,void 0,(function*(){e&&this.validateHead()}))),this.finalAttachments=[],this.udrfService.udrfUiData.showItemDataCount=!0,this.udrfService.udrfUiData.itemDataType="Employees",this.udrfService.udrfUiData.totalItemDataCount=0,this.udrfService.udrfUiData.showSearchBar=!0,this.udrfService.udrfUiData.showActionButtons=!0,this.udrfService.udrfUiData.itemHasOpenBtn=!1,this.udrfService.udrfUiData.itemHasOpenProfileViewBtn=!0,this.udrfService.udrfUiData.itemHasOpenInNewTab=!1,this.udrfService.udrfUiData.openProfileViewClicked=this.openProfileView.bind(this),this.udrfService.udrfUiData.showUdrfModalButton=!0,this.udrfService.udrfUiData.showSettingsModalButton=!1,this.udrfService.udrfUiData.isReportDownloading=!1,this.udrfService.udrfUiData.showColumnConfigButton=!0,this.udrfService.udrfUiData.itemDataScrollDown=this.hrReportScrollDown.bind(this),this.udrfService.udrfUiData.callInlineEditApi=()=>{},this.udrfService.udrfUiData.inlineEditData={},this.udrfService.udrfUiData.inlineEditDropDownMasterDatas={},this.udrfService.udrfUiData.udrfBodyColumns=this.udrfBodyColumns,this.udrfService.udrfUiData.udrfVisibleBodyColumns=this.udrfService.udrfUiData.udrfVisibleBodyColumns,this.udrfService.udrfUiData.udrfInvisibleBodyColumns=this.udrfService.udrfUiData.udrfInvisibleBodyColumns,this.udrfService.udrfUiData.variant=0,this.udrfService.udrfUiData.itemHasQuickCta=!1,this.udrfService.udrfUiData.itemHasComments=!1,this.udrfService.udrfUiData.quickCTAInput={},this.udrfService.udrfUiData.commentsInput={},this.udrfService.udrfUiData.commentsContext={},this.udrfService.udrfUiData.updateItemCard=()=>{},this.udrfService.udrfUiData.attachFile=()=>{},this.udrfService.udrfUiData.deleteFile=()=>{},this.udrfService.udrfUiData.downloadFile=()=>{},this.udrfService.udrfUiData.closeCTA=()=>{},this.udrfService.udrfUiData.updateItemCardData={},this.udrfService.udrfUiData.createNewComponent=this.createEmployee.bind(this),this.udrfService.udrfUiData.horizontalScroll=!0,this.udrfService.udrfUiData.isHeaderSort=!1,this.udrfService.udrfUiData.showCreateNewComponentButton=!0,this.udrfService.udrfUiData.itemHasEdit=!0,this.udrfService.udrfUiData.showNewReleasesButton=!0,this.udrfService.udrfUiData.showReportDownloadButton=!0,this.udrfService.udrfUiData.multiToolTipForSingleIcon=!0,this.udrfService.udrfUiData.callEditFunction=this.getDetailsOfEditEmployee.bind(this),this.udrfService.udrfUiData.downloadItemDataReport=this.downloadReport.bind(this),this.udrfService.getAppUdrfConfig(this.applicationId,this.initReport.bind(this)),this.udrfService.getNotifyReleasesUDRF()}detectUploadChanges(){this.uploader.onProgressItem=e=>{this.isUpload=!0},this.uploader.onCompleteItem=(e,t,i,r)=>{this.isUpload=!1;let n=JSON.parse(t);if(n.error)this.utilityService.showMessage("Unable to upload","Dismiss");else{let e=[];e=e.concat(n.files_json),this.finalAttachments.push(e)}},this.uploader.onWhenAddingFileFailed=()=>{this.isUpload=!1}}initReport(){this.employeeStartIndex=0,this._onAppApiCalled.next(),this.udrfService.udrfUiData.resolveColumnConfig(),this.udrfService.udrfBodyData=[],this.hrEmployeeData()}hrEmployeeData(){this.spinner.show();let e=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray));for(let t of e)5==t.filterId&&(2==t.multiOptionSelectSearchValues.length?(t.multiOptionSelectSearchValues=[],t.multiOptionSelectSearchValues=[0,"P","C","O","I",null]):0==t.multiOptionSelectSearchValues.length?t.multiOptionSelectSearchValues=[]:t.multiOptionSelectSearchValues[0]="Active"==t.multiOptionSelectSearchValues[0]?["P","C","O","I",null]:0),4==t.filterId&&(2==t.multiOptionSelectSearchValues.length?(t.multiOptionSelectSearchValues=[],t.multiOptionSelectSearchValues=[0,1]):0==t.multiOptionSelectSearchValues.length?t.multiOptionSelectSearchValues=[]:t.multiOptionSelectSearchValues[0]="Active"==t.multiOptionSelectSearchValues[0]?1:0);this.hrService.getHrEmployeeData({startIndex:this.employeeStartIndex,mainFilterArray:e,txTableDetails:this.udrfService.udrfData.txTableDetails,mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,searchTableDetails:this.udrfService.udrfData.searchTableDetails}).pipe(Object(I.a)(this._onDestroy)).pipe(Object(I.a)(this._onAppApiCalled)).subscribe(e=>Object(w.c)(this,void 0,void 0,(function*(){e.data&&"S"==e.messType?this.udrfService.udrfBodyData=this.udrfService.udrfBodyData.concat(e.data):(this.udrfService.udrfBodyData=this.udrfService.udrfBodyData.concat([]),this.udrfService.udrfData.noItemDataFound=!0),this.udrfService.udrfData.isItemDataLoading=!1,this.totalCount()})),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while retrieving the Employee Data for HR",e&&e.params?e.params:e&&e.error?e.error.params:{})})}changeStatus(){for(let e of this.empGroupData)1==e.is_status?this.temp_active.push({code:e.code,description:e.description}):this.temp_inactive.push({code:e.code,description:e.description})}UpdateStatus(e){this.empGroupData.code=0==e?0:1}toggleFilter(){this.filterVisible=!this.filterVisible}clearSearch(){this.noDataFlag=0,this.searchTextControl.patchValue(""),this.allEmployeesAID=this.fullData,this.isloading=!1,this.isClearVisible=!1}getOrgDesgData(){this.hrService.getOrgDesgData().pipe(Object(I.a)(this.destroyed$)).subscribe(e=>{this.orgData=e.org_data,this.desgData=e.desg_data,this.roleData=e.role_data},e=>{console.error(e)})}getPracticeList(){this.hrService.getPracticeList().pipe(Object(I.a)(this.destroyed$)).subscribe(e=>{console.log(e),this.practiceData=e},e=>{console.error(e)})}getAIDsAndOIDs(){this.hrService.listAllEmployeesAID().then(e=>{this.allEmployeesAID=e,this.fullData=this.allEmployeesAID},e=>{console.log(e)}),this.hrService.listAllEmployeesOID().then(e=>{this.allEmployeesOID=e},e=>{console.log(e)})}createEmployee(){this.isCreateScreen=!0,this.isEditScreen=!1,this.sideDrawerName="Create Employee",this.CreateEmployeeFormGroup.reset(),this.CreateEmployeeFormGroup.controls.empName.enable(),this.CreateEmployeeFormGroup.controls.empMail.enable(),this.isOrganizationHead=!1,this.isCurrentAssociateAdmin=!0,this.drawer.toggle(),this.uploader.clearQueue(),1==this.isCreateScreen&&0==this.isEditScreen&&this.CreateEmployeeFormGroup.get("empName").valueChanges.subscribe(e=>Object(w.c)(this,void 0,void 0,(function*(){null!=e&&(yield this.graphApi.getUserProfile(e).then(e=>{let t=e.mail;this.employeeName=e.displayName;let i=e.officeLocation;if(null!=i){let e=i.match(/(\d+)/);this.employeeId=e[1]}this.CreateEmployeeFormGroup.patchValue({empAid:this.employeeId,empMail:t})}))})))}openMatDrawer(e){this.editEmployee(e.emp_detail)}validateHead(){if(this.isOrganizationHead){let e=this.CreateEmployeeFormGroup.get("empOrg").value;this.hrService.validateHead(e).pipe(Object(I.a)(this.destroyed$)).subscribe(e=>Object(w.c)(this,void 0,void 0,(function*(){"E"==e.messType?e.data!=this.backupOid&&(this.isOrganizationHead=!1,this.utilityService.showMessage(e.messText,"Dismiss",3e3)):this.isOrganizationHead=!0})))}}getDetailsOfEditEmployee(){this.hrService.getEmployeesOnebyOne(this.udrfService.udrfUiData.associateId).then(e=>{this.allEmployeesOneByOne=e[0],null!=this.allEmployeesOneByOne.candidate_attachments&&(this.attachment=JSON.parse(this.allEmployeesOneByOne.candidate_attachments)),0==this.allEmployeesOneByOne.phone_number&&(this.allEmployeesOneByOne.phone_number=""),0==this.allEmployeesOneByOne.is_active&&(this.allEmployeesOneByOne.status1=!0,this.allEmployeesOneByOne.status2=!1),1==this.allEmployeesOneByOne.is_active&&(this.allEmployeesOneByOne.status1=!1,this.allEmployeesOneByOne.status2=!0),this.editEmployee(this.allEmployeesOneByOne)})}editEmployee(e){console.log(e),console.log(this.practiceData),this.isCurrentAssociateAdmin=!!this.roleAccess,this.isOrganizationHead=1==e.is_org_head,this.isEditScreen=!0,this.isCreateScreen=!1,this.sideDrawerName="Edit Employee",this.CreateEmployeeFormGroup.patchValue({empName:e.oid,empRole:e.role_id,empGroup:e.assoc_group,empDesignation:e.desgn_code,empHrRole:e.role_code,empMail:e.employee_email,candidateMail:e.candidate_email?e.candidate_email:null,empOrg:e.org_code,empStatus:e.is_active.toString(),empNo:e.phone_number,empAid:e.associate_id,empPractice:e.practice_id,lb:e.LB,cf:e.CF}),console.log(this.CreateEmployeeFormGroup.get("empPractice").value),this.backupOid=e.oid,this.backupStatus=e.is_active,this.backupRole=e.role_id,this.backupOrg=e.org_code,this.backupOrgHead=this.isOrganizationHead,this.backupDesg=e.desgn_code,this.backupHrRole=e.role_code,this.backupGroup=e.assoc_group,this.backupAid=e.associate_id,this.backupPractice=e.practice_id,this.drawer.toggle(),this.CreateEmployeeFormGroup.controls.empName.disable(),this.CreateEmployeeFormGroup.controls.empMail.disable()}updateData(e){if("Create Employee"==this.sideDrawerName){this.isEditScreen=!1,this.isCreateScreen=!0;let e=this.CreateEmployeeFormGroup.get("empName").value,t=this.CreateEmployeeFormGroup.get("empRole").value,i=this.CreateEmployeeFormGroup.get("empMail").value,r=this.CreateEmployeeFormGroup.get("candidateMail").value,n=this.CreateEmployeeFormGroup.get("empNo").value,a=this.CreateEmployeeFormGroup.get("empBatch").value,o=this.CreateEmployeeFormGroup.get("empStatus").value,s=this.CreateEmployeeFormGroup.get("empDesignation").value,l=this.CreateEmployeeFormGroup.get("empHrRole").value,d=this.CreateEmployeeFormGroup.get("empOrg").value,c=this.CreateEmployeeFormGroup.get("empGroup").value,p=this.isOrganizationHead?1:0,h=this.CreateEmployeeFormGroup.get("empAid").value,m=this.CreateEmployeeFormGroup.get("empPractice").value,u=this.CreateEmployeeFormGroup.get("lb").value?this.CreateEmployeeFormGroup.get("lb").value:0,g=this.CreateEmployeeFormGroup.get("cf").value?this.CreateEmployeeFormGroup.get("cf").value:0,f=A.where(this.allEmployeesOID,{oid:e}),v=this.finalAttachments.length>0?JSON.stringify(A.flatten(this.finalAttachments)):null;this.finalAttachments=[],console.log(v),f.length>0?e==f[0].oid&&(this.utilityService.showMessage("Employee data already exists in KEBS","Dismiss",3e3),this.drawer.toggle()):null==e||null==t||null==o||null==s||null==l||null==d||null==h?this.utilityService.showMessage("Please Fill in the mandatory fields","Dismiss",3e3):this.hrService.addEmployee(e,this.employeeName,h,t,i,n,a,o,s,l,d,p,u,g,c,m,r,v).then(e=>{this.drawer.toggle(),this.utilityService.showMessage("Employee added Successfully","Dismiss",3e3),this.ngOnInit()},e=>{this.utilityService.showMessage("Unable to add Employee","Dismiss",3e3),console.log(e)})}if("Edit Employee"==this.sideDrawerName){this.isCreateScreen=!1,this.isEditScreen=!0;let e=this.CreateEmployeeFormGroup.get("candidateMail").value,t=this.CreateEmployeeFormGroup.get("empName").value,i=this.CreateEmployeeFormGroup.get("empNo").value,r=this.CreateEmployeeFormGroup.get("empRole").value,n=this.CreateEmployeeFormGroup.get("empDesignation").value,a=this.CreateEmployeeFormGroup.get("empHrRole").value,o=this.CreateEmployeeFormGroup.get("empOrg").value,s=this.isOrganizationHead?1:0,l=this.CreateEmployeeFormGroup.get("empBatch").value,d=this.CreateEmployeeFormGroup.get("empStatus").value,c=this.CreateEmployeeFormGroup.get("empGroup").value,p=this.CreateEmployeeFormGroup.get("empAid").value,h=this.CreateEmployeeFormGroup.get("empPractice").value,m=this.CreateEmployeeFormGroup.get("lb").value?this.CreateEmployeeFormGroup.get("lb").value:0,u=this.CreateEmployeeFormGroup.get("cf").value?this.CreateEmployeeFormGroup.get("cf").value:0;null==i&&(i=""),""==i&&d==this.backupStatus&&r==this.backupRole&&n==this.backupDesg&&a==this.backupHrRole&&o==this.backupOrg&&this.isOrganizationHead==this.backupOrgHead&&c==this.backupGroup&&null==m&&null==u&&p==this.backupAid&&h==this.backupPractice?this.utilityService.showMessage("No changes made, Please make any valid changes to Update","Dismiss",3e3):(this.drawer.toggle(),this.hrService.updateEmployee(t,i,r,l,d,o,s,n,a,m,u,c,p,h,e).then(e=>{this.utilityService.showMessage("Employee Details Updated Successfully","Dismiss",3e3),this.editValue=e[0],this.getAIDsAndOIDs(),this.initReport()},e=>{this.utilityService.showMessage("Unable to edit Employee","Dismiss",3e3),console.log(e)}))}}numberOnly(e){const t=e.which?e.which:e.keyCode;return!(t>31&&(t<48||t>57))}downloadReport(){let e=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray));this.hrService.getHrEmployeeDataDownload({startDate:this.udrfService.udrfData.mainApiDateRangeStart,endDate:this.udrfService.udrfData.mainApiDateRangeEnd,mainFilterArray:e,txTableDetails:this.udrfService.udrfData.txTableDetails,mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,searchTableDetails:this.udrfService.udrfData.searchTableDetails}).pipe(Object(I.a)(this._onDestroy)).pipe(Object(I.a)(this._onAppApiCalled)).subscribe(e=>Object(w.c)(this,void 0,void 0,(function*(){this.udrfService.udrfUiData.isReportDownloading=!1,e.data&&"S"==e.messType?this.excelService.exportAsExcelFile(e.data,"employee_master("+F()().format("DD-MMM-YYYY")+")"):this.utilityService.showMessage("Report Download Failed","Dismiss",3e3),this.udrfService.udrfData.isItemDataLoading=!1})),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while downloading the Employee Data for HR",e&&e.params?e.params:e&&e.error?e.error.params:{})})}totalCount(){let e=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray));for(let t of e)5==t.filterId&&(2==t.multiOptionSelectSearchValues.length?(t.multiOptionSelectSearchValues=[],t.multiOptionSelectSearchValues=[0,"P","C","O","I",null]):0==t.multiOptionSelectSearchValues.length?t.multiOptionSelectSearchValues=[]:t.multiOptionSelectSearchValues[0]="Active"==t.multiOptionSelectSearchValues[0]?["P","C","O","I",null]:0),4==t.filterId&&(2==t.multiOptionSelectSearchValues.length?(t.multiOptionSelectSearchValues=[],t.multiOptionSelectSearchValues=[0,1]):0==t.multiOptionSelectSearchValues.length?t.multiOptionSelectSearchValues=[]:t.multiOptionSelectSearchValues[0]="Active"==t.multiOptionSelectSearchValues[0]?[1]:0);this.hrService.getHrEmployeeDataDownload({mainFilterArray:e,txTableDetails:this.udrfService.udrfData.txTableDetails,mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,searchTableDetails:this.udrfService.udrfData.searchTableDetails}).pipe(Object(I.a)(this._onDestroy)).pipe(Object(I.a)(this._onAppApiCalled)).subscribe(e=>Object(w.c)(this,void 0,void 0,(function*(){e.data?this.udrfService.udrfUiData.totalItemDataCount=e.totallength:this.utilityService.showMessage("Total Data Fetch Failed","Dismiss",3e3),this.udrfService.udrfData.isItemDataLoading=!1})),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error getting Total Employee count for HR",e&&e.params?e.params:e&&e.error?e.error.params:{})})}hrReportScrollDown(){return Object(w.c)(this,void 0,void 0,(function*(){this.udrfService.udrfData.noItemDataFound||this.udrfService.udrfData.isItemDataLoading||(this.employeeStartIndex+=this.udrfService.udrfData.defaultRecordsPerFetch,this.udrfService.udrfData.isItemDataLoading=!0,yield this.hrEmployeeData())}))}openProfileView(){console.log(this.udrfService.udrfUiData.openProfileViewData.data);let e=this.udrfService.udrfUiData.openProfileViewData.data.candidate_email;e&&null!=e?this.openOnboardingEmployee(e):this.utilityService.showMessage("Email not linked","Dismiss")}openOnboardingEmployee(e){let t="/onboarding/home?emailId="+e;this.router.navigate([]).then(e=>{window.open(t,"_blank")})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete(),this.udrfService.resetUdrfData()}}return e.\u0275fac=function(t){return new(t||e)(R["\u0275\u0275directiveInject"](N.a),R["\u0275\u0275directiveInject"](T.a),R["\u0275\u0275directiveInject"](j.c),R["\u0275\u0275directiveInject"](U.a),R["\u0275\u0275directiveInject"](G.a),R["\u0275\u0275directiveInject"](n.i),R["\u0275\u0275directiveInject"](B.a),R["\u0275\u0275directiveInject"](V.a),R["\u0275\u0275directiveInject"](H.a),R["\u0275\u0275directiveInject"](q.a),R["\u0275\u0275directiveInject"](E.g),R["\u0275\u0275directiveInject"](z.a))},e.\u0275cmp=R["\u0275\u0275defineComponent"]({type:e,selectors:[["app-hr-landing-page"]],viewQuery:function(e,t){if(1&e&&(R["\u0275\u0275viewQuery"](K,!0),R["\u0275\u0275viewQuery"]($,!0),R["\u0275\u0275viewQuery"](Z,!0),R["\u0275\u0275viewQuery"](g.h,!0)),2&e){let e;R["\u0275\u0275queryRefresh"](e=R["\u0275\u0275loadQuery"]())&&(t.drawer=e.first),R["\u0275\u0275queryRefresh"](e=R["\u0275\u0275loadQuery"]())&&(t.roleList=e.first),R["\u0275\u0275queryRefresh"](e=R["\u0275\u0275loadQuery"]())&&(t.statusList=e.first),R["\u0275\u0275queryRefresh"](e=R["\u0275\u0275loadQuery"]())&&(t.list=e)}},decls:88,vars:18,consts:[[1,"view-hr","container-fluid"],["hasBackdrop","false",1,"drawer"],["mode","over","position","end","disableClose","true",1,"side-drawer","overflow-hidden"],["drawer",""],[1,"row","heading"],[1,"col-6","col-sm-6","col-md-11","col-lg-11","pr-0","mt-0","pt-2",2,"color","#cf0001","font-weight","500","padding-left","31px !important"],[1,"col-6","col-sm-6","col-md-1","col-lg-1",2,"padding-left","0%"],["mat-icon-button","",1,"close-button",3,"click"],["matTooltip","Close",1,"close-Icon"],[3,"formGroup"],[1,"row","ename","pt-2"],[1,"col-12"],["label","Employee name","required","true",2,"width","100% !important",3,"isGraphApi","isAutocomplete","formControl"],["class","row role",4,"ngIf"],[1,"row","role"],[1,"col-12","desc"],["required","true","placeholder","Designation","formControlName","empDesignation",3,"list"],["appearance","outline",1,"desc"],["matInput","","type","email","name","empMail","ngModel","","email","true","formControlName","empMail","required",""],["matSuffix","",2,"color","#66615B !important","font-size","21px !important"],["matInput","","type","email","name","candidateMail","ngModel","","email","true","formControlName","candidateMail"],["required","true","placeholder","Employee(HR) role","formControlName","empHrRole",3,"list"],["required","true","placeholder","Organization","formControlName","empOrg",3,"list"],[1,"col-6"],["matInput","","type","number","formControlName","empAid","required",""],["placeholder","Practice","formControlName","empPractice",3,"list"],["matInput","","name","empNo","formControlName","empNo",3,"keypress"],[1,"col-6","my-auto"],[3,"ngModel","ngModelOptions","ngModelChange"],[1,"col-6","col-sm-12","col-lg-6","col-md-6"],["formControlName","empGroup","required","",4,"ngIf"],["formControlName","empStatus","required","",3,"valueChange"],["value","1"],["value","0"],["class","col-11 mt-2 mb-2",4,"ngIf"],[1,"row",2,"margin-top","15px"],[1,"col-6","col-sm-6","col-md-6","col-lg-6","quote"],[1,"col-6","col-sm-6","col-md-6","col-lg-6"],["matTooltip","Save","mat-mini-fab","",1,"mini-tick","ml-5",3,"click"],["formControlName","empRole","required",""],[3,"value",4,"ngFor","ngForOf"],[3,"value"],["matInput","","type","number","formControlName","lb","required",""],["matInput","","type","number","formControlName","cf","required",""],["formControlName","empGroup","required",""],[1,"col-11","mt-2","mb-2"],["ng2FileDrop","",1,"ml-3","upload-box",3,"ngClass","uploader","fileOver"],[1,"row","pl-3","mt-3"],[1,"col-9","p-0"],[4,"ngFor","ngForOf"],["style","font-size: 12px; color: gray",4,"ngIf"],[1,"col-3","p-0"],[1,"material-icons","upload-icon",3,"click"],["type","file","ng2FileSelect","","multiple","",2,"display","none",3,"uploader"],["fileInput",""],["class","row pl-3 mt-3 mb-3",4,"ngIf"],["class","spinner-border","style","width: 1rem;height: 1rem;","role","status",4,"ngIf"],[4,"ngIf"],["role","status",1,"spinner-border",2,"width","1rem","height","1rem"],[1,"sr-only"],[1,"material-icons"],[2,"font-size","12px","color","gray"],[1,"row","pl-3","mt-3","mb-3"],[1,"col-12","p-0"],["mat-raised-button","",2,"color","#cf0001",3,"disabled","click"],["mat-raised-button","",1,"ml-3","mr-3",2,"color","#cf0001",3,"disabled","click"],[2,"font-size","14px","font-weight","500","color","#cf0001"],["class","pt-2 pl-2",4,"ngFor","ngForOf"],[1,"pt-2","pl-2"],[2,"cursor","pointer",3,"matTooltip","click"]],template:function(e,t){if(1&e){const e=R["\u0275\u0275getCurrentView"]();R["\u0275\u0275elementStart"](0,"div",0),R["\u0275\u0275elementStart"](1,"mat-drawer-container",1),R["\u0275\u0275elementStart"](2,"mat-drawer",2,3),R["\u0275\u0275elementStart"](4,"div",4),R["\u0275\u0275elementStart"](5,"div",5),R["\u0275\u0275text"](6),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementStart"](7,"div",6),R["\u0275\u0275elementStart"](8,"button",7),R["\u0275\u0275listener"]("click",(function(){return R["\u0275\u0275restoreView"](e),R["\u0275\u0275reference"](3).toggle()})),R["\u0275\u0275elementStart"](9,"mat-icon",8),R["\u0275\u0275text"](10," close "),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275element"](11,"mat-divider"),R["\u0275\u0275elementStart"](12,"form",9),R["\u0275\u0275elementStart"](13,"div",10),R["\u0275\u0275elementStart"](14,"div",11),R["\u0275\u0275element"](15,"app-search-user",12),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275template"](16,te,7,1,"div",13),R["\u0275\u0275elementStart"](17,"div",14),R["\u0275\u0275elementStart"](18,"div",15),R["\u0275\u0275element"](19,"app-input-search",16),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementStart"](20,"div",14),R["\u0275\u0275elementStart"](21,"div",11),R["\u0275\u0275elementStart"](22,"mat-form-field",17),R["\u0275\u0275elementStart"](23,"mat-label"),R["\u0275\u0275text"](24,"Microsoft Email"),R["\u0275\u0275elementEnd"](),R["\u0275\u0275element"](25,"input",18),R["\u0275\u0275elementStart"](26,"mat-icon",19),R["\u0275\u0275text"](27,"email"),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementStart"](28,"div",14),R["\u0275\u0275elementStart"](29,"div",11),R["\u0275\u0275elementStart"](30,"mat-form-field",17),R["\u0275\u0275elementStart"](31,"mat-label"),R["\u0275\u0275text"](32,"Candidate Email"),R["\u0275\u0275elementEnd"](),R["\u0275\u0275element"](33,"input",20),R["\u0275\u0275elementStart"](34,"mat-icon",19),R["\u0275\u0275text"](35,"email"),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementStart"](36,"div",14),R["\u0275\u0275elementStart"](37,"div",15),R["\u0275\u0275element"](38,"app-input-search",21),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementStart"](39,"div",14),R["\u0275\u0275elementStart"](40,"div",15),R["\u0275\u0275element"](41,"app-input-search",22),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementStart"](42,"div",14),R["\u0275\u0275elementStart"](43,"div",23),R["\u0275\u0275elementStart"](44,"mat-form-field",17),R["\u0275\u0275elementStart"](45,"mat-label"),R["\u0275\u0275text"](46,"Associate ID"),R["\u0275\u0275elementEnd"](),R["\u0275\u0275element"](47,"input",24),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementStart"](48,"div",23),R["\u0275\u0275element"](49,"app-input-search",25),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275template"](50,ie,11,0,"div",13),R["\u0275\u0275elementStart"](51,"div",14),R["\u0275\u0275elementStart"](52,"div",23),R["\u0275\u0275elementStart"](53,"mat-form-field",17),R["\u0275\u0275elementStart"](54,"mat-label"),R["\u0275\u0275text"](55,"Phone #"),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementStart"](56,"input",26),R["\u0275\u0275listener"]("keypress",(function(e){return t.numberOnly(e)})),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementStart"](57,"div",27),R["\u0275\u0275elementStart"](58,"mat-checkbox",28),R["\u0275\u0275listener"]("ngModelChange",(function(e){return t.isOrganizationHead=e}))("ngModelChange",(function(){return t.validateHead()})),R["\u0275\u0275text"](59," Organization Head "),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementStart"](60,"div",14),R["\u0275\u0275elementStart"](61,"div",29),R["\u0275\u0275elementStart"](62,"mat-form-field",17),R["\u0275\u0275elementStart"](63,"mat-label"),R["\u0275\u0275text"](64,"Employee Type"),R["\u0275\u0275elementEnd"](),R["\u0275\u0275template"](65,ne,2,1,"mat-select",30),R["\u0275\u0275template"](66,oe,2,1,"mat-select",30),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementStart"](67,"div",29),R["\u0275\u0275elementStart"](68,"mat-form-field",17),R["\u0275\u0275elementStart"](69,"mat-label"),R["\u0275\u0275text"](70,"Status"),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementStart"](71,"mat-select",31),R["\u0275\u0275listener"]("valueChange",(function(e){return t.UpdateStatus(e)})),R["\u0275\u0275elementStart"](72,"mat-option",32),R["\u0275\u0275text"](73,"Active"),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementStart"](74,"mat-option",33),R["\u0275\u0275text"](75,"Inactive"),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275template"](76,ge,12,8,"div",34),R["\u0275\u0275template"](77,ve,6,1,"div",34),R["\u0275\u0275elementStart"](78,"div",35),R["\u0275\u0275elementStart"](79,"div",36),R["\u0275\u0275text"](80,' "We are humans first,resources later" '),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementStart"](81,"div",37),R["\u0275\u0275elementStart"](82,"button",38),R["\u0275\u0275listener"]("click",(function(){return t.updateData(t.sideDrawerName)})),R["\u0275\u0275elementStart"](83,"mat-icon"),R["\u0275\u0275text"](84,"done_all"),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementStart"](85,"mat-drawer-content"),R["\u0275\u0275element"](86,"udrf-header"),R["\u0275\u0275element"](87,"udrf-body"),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"](),R["\u0275\u0275elementEnd"]()}2&e&&(R["\u0275\u0275advance"](6),R["\u0275\u0275textInterpolate1"](" ",t.sideDrawerName," "),R["\u0275\u0275advance"](6),R["\u0275\u0275property"]("formGroup",t.CreateEmployeeFormGroup),R["\u0275\u0275advance"](3),R["\u0275\u0275property"]("isGraphApi",!0)("isAutocomplete",!0)("formControl",t.CreateEmployeeFormGroup.controls.empName),R["\u0275\u0275advance"](1),R["\u0275\u0275property"]("ngIf",t.isCurrentAssociateAdmin),R["\u0275\u0275advance"](3),R["\u0275\u0275property"]("list",t.desgData),R["\u0275\u0275advance"](19),R["\u0275\u0275property"]("list",t.roleData),R["\u0275\u0275advance"](3),R["\u0275\u0275property"]("list",t.orgData),R["\u0275\u0275advance"](8),R["\u0275\u0275property"]("list",t.practiceData),R["\u0275\u0275advance"](1),R["\u0275\u0275property"]("ngIf",t.roleAccess),R["\u0275\u0275advance"](8),R["\u0275\u0275property"]("ngModel",t.isOrganizationHead)("ngModelOptions",R["\u0275\u0275pureFunction0"](17,Se)),R["\u0275\u0275advance"](7),R["\u0275\u0275property"]("ngIf",0==t.empGroupData.code),R["\u0275\u0275advance"](1),R["\u0275\u0275property"]("ngIf",0!=t.empGroupData.code),R["\u0275\u0275advance"](10),R["\u0275\u0275property"]("ngIf",t.isCreateScreen),R["\u0275\u0275advance"](1),R["\u0275\u0275property"]("ngIf",t.isEditScreen&&t.attachment.length>0))},directives:[u.b,u.a,s.a,a.a,m.a,L.a,n.J,n.w,n.n,W.a,n.F,n.v,n.k,r.NgIf,X.a,n.l,l.c,l.g,d.b,n.e,n.f,l.i,n.A,y.a,n.y,o.c,Q.p,u.c,J.a,Y.a,r.NgForOf,M.a,r.NgClass,M.b],pipes:[r.DecimalPipe],styles:[".view-hr[_ngcontent-%COMP%]   virtual-scroll[_ngcontent-%COMP%]{display:block;width:100%;height:476px}.view-hr[_ngcontent-%COMP%]   .tileName[_ngcontent-%COMP%]{color:#9a9a9a;font-size:13px!important}.view-hr[_ngcontent-%COMP%]   .info[_ngcontent-%COMP%]{color:#b1b1b1;font-size:12px!important}.view-hr[_ngcontent-%COMP%]   .mat-list-base[_ngcontent-%COMP%]   .mat-subheader[_ngcontent-%COMP%]{height:30px!important;line-height:16px!important}.view-hr[_ngcontent-%COMP%]   .mat-list-option[_ngcontent-%COMP%]{height:24px!important;line-height:24px!important}.view-hr[_ngcontent-%COMP%]   .card-header-row[_ngcontent-%COMP%]{font-size:11px;color:grey}.view-hr[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]{font-weight:400;color:#1a1a1a!important;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;width:50px;font-size:14px}.view-hr[_ngcontent-%COMP%]     .icon-tray-button{width:10px!important;height:10px!important;line-height:10px!important;font-size:10px!important;color:#9a9a9a!important;visibility:hidden}.view-hr[_ngcontent-%COMP%]   .cost-center[_ngcontent-%COMP%]{font-weight:400;color:grey;font-size:11px!important;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.view-hr[_ngcontent-%COMP%]   .listcard[_ngcontent-%COMP%]{transition:all .25s linear;height:39px;max-height:39px!important}.view-hr[_ngcontent-%COMP%]   .listcard[_ngcontent-%COMP%]:hover{box-shadow:0 4px 8px 0 rgba(0,0,0,.2),0 6px 20px 0 rgba(0,0,0,.19);cursor:pointer}.view-hr[_ngcontent-%COMP%]   .listcard[_ngcontent-%COMP%]:hover   .icon-tray-button[_ngcontent-%COMP%]{visibility:visible!important;animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}.view-hr[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]{color:#1a1a1a;font-size:14px;font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.view-hr[_ngcontent-%COMP%]   .listCardButton[_ngcontent-%COMP%]{height:35px!important;width:35px!important;line-height:35px!important}@keyframes slide-top{0%{transform:translateY(10px)}to{transform:translateY(0)}}.view-hr[_ngcontent-%COMP%]   .smallCardIcon[_ngcontent-%COMP%]{font-size:18px!important;color:#868683!important}.view-hr[_ngcontent-%COMP%]   .wrapper[_ngcontent-%COMP%]{display:inline-block;border-radius:4px;margin-right:6px;width:16px;height:23px;cursor:pointer!important}.view-hr[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field{width:50vw;padding-top:5px}.view-hr[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-infix{font-size:14px!important;padding:.5em 0!important;border-top:.54375em solid transparent!important}.view-hr[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-flex{padding:0 .75em;margin-top:-1.2em;position:relative}.view-hr[_ngcontent-%COMP%]   .drawer[_ngcontent-%COMP%]{width:100%}.view-hr[_ngcontent-%COMP%]   .side-drawer[_ngcontent-%COMP%]{width:600px!important}.view-hr[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]{margin-top:10px;margin-bottom:5px}.view-hr[_ngcontent-%COMP%]   .mat-list-option[_ngcontent-%COMP%]{font-size:13px!important;line-height:20px!important}.view-hr[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.view-hr[_ngcontent-%COMP%]   .btn-fab[_ngcontent-%COMP%]{height:20px;width:20px;color:#000;background-color:#d3d3d3;box-shadow:0 3px 1px -2px rgba(0,0,0,.2),0 2px 2px 0 rgba(0,0,0,.14),0 1px 5px 0 rgba(0,0,0,.12)}.view-hr[_ngcontent-%COMP%]     .mat-mini-fab .mat-button-wrapper{padding:4px 0;display:flex;line-height:15px}.view-hr[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.view-hr[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}.view-hr[_ngcontent-%COMP%]   .desc[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:100%;font-size:13px}.view-hr[_ngcontent-%COMP%]   .ename[_ngcontent-%COMP%], .view-hr[_ngcontent-%COMP%]   .role[_ngcontent-%COMP%]{margin-left:20px!important;margin-right:20px!important}.view-hr[_ngcontent-%COMP%]   .role[_ngcontent-%COMP%]{margin-top:10px}.view-hr[_ngcontent-%COMP%]   .status[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:100%;font-size:13px;margin-left:20px;margin-top:10px}.view-hr[_ngcontent-%COMP%]   .quote[_ngcontent-%COMP%]{padding-left:30px;color:#9a9a9a!important;font-size:15px!important;font-style:italic;font-weight:400}.view-hr[_ngcontent-%COMP%]   .mini-tick[_ngcontent-%COMP%]{height:40px!important;width:40px!important;line-height:40px;background-color:#cf0001!important;color:#fff!important;padding-left:8px!important}.view-hr[_ngcontent-%COMP%]   .title-class[_ngcontent-%COMP%]{font-size:18px!important}.view-hr[_ngcontent-%COMP%]   .cancel-button-class[_ngcontent-%COMP%], .view-hr[_ngcontent-%COMP%]   .confirm-button-class[_ngcontent-%COMP%]{margin:10px!important;font-size:15px!important;padding:11px 35px!important;box-shadow:none;font-weight:500}.view-hr[_ngcontent-%COMP%]   .status1[_ngcontent-%COMP%]{border-left-color:#9a9a9a!important}.view-hr[_ngcontent-%COMP%]   .status2[_ngcontent-%COMP%]{border-left-color:#009432!important}.view-hr[_ngcontent-%COMP%]   .header-button[_ngcontent-%COMP%]{width:40px!important;height:40px!important;line-height:10px!important;color:#9a9a9a!important}.view-hr[_ngcontent-%COMP%]   .HeaderIcon[_ngcontent-%COMP%]{font-size:18px!important;color:#66615b!important}.view-hr[_ngcontent-%COMP%]   .upload-box[_ngcontent-%COMP%]{min-height:128px;border:solid;border-color:#d6d6d6;border-width:1px;border-radius:6px}.view-hr[_ngcontent-%COMP%]   .my-drop-zone[_ngcontent-%COMP%]{border:3px dotted #d3d3d3}.view-hr[_ngcontent-%COMP%]   .nv-file-over[_ngcontent-%COMP%]{border:3px dotted red}.view-hr[_ngcontent-%COMP%]   .upload-icon[_ngcontent-%COMP%]{position:absolute;opacity:.1;cursor:pointer;font-size:6rem}"],data:{animation:[Object(_.o)("slideInOut",[Object(_.l)("in",Object(_.m)({height:"*",overflow:"hidden"})),Object(_.l)("out",Object(_.m)({height:0,overflow:"hidden"})),Object(_.n)("* => in",[Object(_.m)({height:0}),Object(_.e)(250,Object(_.m)({height:"*"}))]),Object(_.n)("in=> *",[Object(_.m)({height:"*"}),Object(_.e)(250,Object(_.m)({height:0}))])])]}}),e})(),canActivate:[(()=>{class e{constructor(e,t,i,r,n){this._auth=e,this.hrService=t,this._util=i,this.router=r,this.role_service=n,this.checkAccess=()=>A.where(this.role_service.roles,{application_id:27,object_id:161}).length>0,this.profile=this._auth.getProfile().profile}canActivate(e){return Object(w.c)(this,void 0,void 0,(function*(){let e=yield this.checkAccess();return 0==e&&(this._util.showMessage("You Dont Have Access For HR Application! Contact KEBS Support.","dismiss",2e3),this.router.navigateByUrl("/main")),e}))}}return e.\u0275fac=function(t){return new(t||e)(R["\u0275\u0275inject"](G.a),R["\u0275\u0275inject"](T.a),R["\u0275\u0275inject"](U.a),R["\u0275\u0275inject"](E.g),R["\u0275\u0275inject"](N.a))},e.\u0275prov=R["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()]}];let be=(()=>{class e{}return e.\u0275mod=R["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=R["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[E.k.forChild(Ce)],E.k]}),e})();var Oe=i("Xi0T");let ye=(()=>{class e{}return e.\u0275mod=R["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=R["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[r.CommonModule,be,n.p,n.E,a.b,o.d,s.b,l.e,d.c,c.h,p.b,h.g,m.b,u.g,g.d,f.b,v.a,S.VirtualScrollModule,C.a,b.e,O.c,O.c,y.b,Oe.a,M.c]]}),e})()},XfUL:function(e,t,i){"use strict";i.d(t,"a",(function(){return p}));var r=i("ofXK"),n=i("fXoL"),a=i("bTqV"),o=i("bv9b"),s=i("Xa2L"),l=i("FKr1"),d=i("NFeN");const c=new n.InjectionToken("Global Config");let p=(()=>{class e{static forRoot(t){return{ngModule:e,providers:[{provide:c,useValue:t}]}}}return e.\u0275mod=n["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=n["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[r.CommonModule,a.b,o.b,s.b,l.v,d.b]]}),e})()},hJL4:function(e,t,i){"use strict";i.d(t,"a",(function(){return p}));var r=i("mrSG"),n=i("XNiG"),a=i("xG9w"),o=i("fXoL"),s=i("tk/3"),l=i("LcQX"),d=i("XXEo"),c=i("flaP");let p=(()=>{class e{constructor(e,t,i,r){this.http=e,this.UtilityService=t,this.loginService=i,this.roleService=r,this.msg=new n.b,this.taskStatusColor=[],this.approveRequest=(e,t)=>this.http.post("/api/isa/request/approveResourceRequest",{requestDetails:e,approverOid:t}),this.rejectRequest=(e,t)=>this.http.post("/api/isa/request/rejectResourceRequest",{requestDetails:e,approverOid:t}),this.getTaskStatusColor()}getAllRequestsOfUser(e,t,i,r,n,a,o){let s=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getAllRequestsOfUser",{oid:e,statusId:t,statusCode:i,objectIds:r,skip:n,limit:a,filterConfig:o,orgIds:s})}getAllRoleAccess(){return a.where(this.roleService.roles,{application_id:139})}getRequestsBasedOnStatus(e,t,i,r,n,a,o){let s=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getRequestsBasedOnStatus",{oid:e,statusId:t,statusCode:i,objectIds:r,skip:n,limit:a,filterConfig:o,orgIds:s})}getRequestsForAwaitingApproval(e,t,i,r){return this.http.post("/api/isa/request/getRequestsForAwaitingApproval",{oid:e,skip:t,limit:i,filterConfig:r})}getObjectIdsBasedOnOid(e){return this.http.post("/api/isa/request/getObjectIdsBasedOnOid",{oid:e})}getStatusCountBasedOnOid(e,t,i,r){let n=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getStatusCountBasedOnOid",{oid:e,objectIds:t,dataTypeArray:i,filterConfig:r,orgIds:n})}getHeaderCount(e){return this.http.post("/api/isa/request/getHeaderCount",{oid:e})}getRequestBasedOnStatus(e,t){return this.http.post("/api/isa/request/getRequestBasedOnStatus",{oid:e,status:t})}getDataTypeArray(){return this.http.post("/api/isa/request/getISAStatusMasterDataUDRF",{})}sendMsg(e){this.msg.next(e)}getMsg(){return this.msg.asObservable()}getTaskStatusColor(){return new Promise((e,t)=>{this.http.post("/api/isa/request/getTaskStatusNameColor",{}).subscribe(t=>(this.taskStatusColor=t,e(t)),e=>{})})}getAllRequestsForRmg(e){return this.http.post("/api/isa/request/getAllRequestsForRmg",{filterConfig:e})}getTotalForRmg(e){return new Promise((t,i)=>{this.http.post("/api/isa/request/getTotalForRmg",{filterConfig:e}).subscribe(e=>t(e),e=>{})})}getDefaultISAStatusToDisplay(){return this.http.post("/api/isa/request/getISADefaultStatusToDisplay",{})}getCTCbreakUpForSkillSet(e){return new Promise((t,i)=>{this.http.post("/api/isa/request/getCTCbreakUpForSkillSet",e).subscribe(e=>t(e.data),e=>{i(e)})})}getCurrentUserRole(){return this.roleService&&this.roleService.roles&&this.roleService.roles.length>0?this.roleService.roles[0].role_id:null}getTemplateForUser(e){return this.http.post("/api/isa/request/getVisibleFormFieldsForUser",e)}saveAndApproveRequest(e){return this.http.post("/api/isa/request/updBudgetInfoByRequestId",e)}getVisibilityMatrix(e){return this.http.post("/api/isa/request/getAuthorzedFieldsByRoleId",e)}getISAAttachmentFromS3(e){return this.http.post("/api/isa/attachment/getISAAttachmentFromS3",{key:e})}getSkillType(){return this.http.post("/api/isa/request/getSkillType",{})}getSkillLevel(){return this.http.post("/api/isa/request/getSkillLevel",{})}getSkillExperience(){return this.http.post("/api/isa/request/getSkillExperience",{})}getSkillName(e){return this.http.post("/api/isa/request/getSkillName",{skill_type_id:e})}getIsaActivityReportDownload(){return this.http.post("/api/isa/request/getIsaActivityReportDownload",{})}getISASLAreport(e){return this.http.post("/api/isa/request/getISASLAreport",{filterConfig:e})}getISACustomReport(e){return this.http.post("/api/isa/request/getISACustomReport",{filterConfig:e})}getPositionType(){return this.http.post("/api/employee360/masterData/getPositionType",{})}calculateNetCostBasedOnPosition(e,t,i,n,o,s,l){return Object(r.c)(this,void 0,void 0,(function*(){let r;r=s&&s.length>1&&(yield this.getManpowerCostByOId(s,i,o,2))||(yield this.getManpowerCostBasedOnPosition(e,t,i,o,l));let d=yield this.getNonManpowerCost(t,i,n,o,2),c=yield this.getAllocatedCost(),p=0;p=(r?r.cost:0)+d.length>0?a.reduce(a.pluck(d,"cost"),(e,t)=>e+t,0):0;let h=c.length>0?a.reduce(a.pluck(c,"percentage"),(e,t)=>e+t,0):0;return{cost:p,currency:r&&r.currency_code?r.currency_code:"",manpowerCost:r,nonManpowerCost:d,allocatedCost:c,allocatedCostValue:p*(h/100)}}))}getManpowerCostBasedOnPosition(e,t,i,r,n){return new Promise((a,o)=>{this.http.post("/api/isa/configuration/getManpowerCostBasedOnPosition",{skillRoleId:e,nationalityId:t,locationGroupId:i,unit:r,position:n}).subscribe(e=>a(e),e=>(console.log(e),o(e)))})}getNonManpowerCost(e,t,i,r,n){return new Promise((a,o)=>{this.http.post("/api/project/getNonManpowerCost",{nationalityId:e,locationGroupId:t,locationId:i,unit:r,currency_id:n}).subscribe(e=>a(e),e=>(console.log(e),o(e)))})}getAllocatedCost(){return new Promise((e,t)=>{this.http.post("/api/project/getAllocatedCost","").subscribe(t=>e(t),e=>(console.log(e),t(e)))})}getManpowerCostByOId(e,t,i,r){return new Promise((n,a)=>{this.http.post("/api/project/getEmployeeSalary",{oid:e,location_group_id:t,unit_id:i,currency_id:r}).subscribe(e=>n(e),e=>(console.log(e),a(e)))})}getSkillNameMaster(){return this.http.post("/api/isa/configuration/getSkillName",{})}getVendorOid(e,t){return this.http.post("/api/isa/request/getVendorOid",{vendorNames:e,docID:t})}getVendorList(e,t){return this.http.post("/api/isa/request/getVendorList",{documentID:e,limitval:t})}removeVendor(e,t){return this.http.post("/api/isa/request/removeVendors",{vendorOid:e,documentID:t})}checkVendorOrNot(){return this.http.post("/api/isa/request/checkVendorOrNot",{})}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275inject"](s.c),o["\u0275\u0275inject"](l.a),o["\u0275\u0275inject"](d.a),o["\u0275\u0275inject"](c.a))},e.\u0275prov=o["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},ucYs:function(e,t,i){"use strict";i.d(t,"a",(function(){return l}));var r=i("mrSG"),n=i("xG9w"),a=i("fXoL"),o=i("tk/3"),s=i("BVzC");let l=(()=>{class e{constructor(e,t){this.http=e,this.errorService=t,this.workflowStatusList=[],this.getWorkflowStatusList()}getWorkflowStatusList(){this.http.post("/api/wfPrimary/getWorkflowStatusList","").subscribe(e=>{this.workflowStatusList=e.data},e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server While Getting Workflow Status List",e&&e.params?e.params:e&&e.error?e.error.params:{})})}getWorkflowProperties(e){return new Promise((t,i)=>{this.http.post("/api/wfPrimary/getWorkflowProperties",{applicationId:e}).subscribe(e=>t(e),e=>i(e))})}getWorkflowPropertiesByWorkflowId(e){return new Promise((t,i)=>{this.http.post("/api/wfPrimary/getWorkflowPropertiesByWorkflowId",{workflowId:e}).subscribe(e=>t(e),e=>i(e))})}getApproversHierarchy(e){return new Promise((t,i)=>{this.http.post("/api/wfPrimary/getApproversHierarchy",e).subscribe(e=>t(e),e=>i(e))})}createWorkflowItems(e){return new Promise((t,i)=>{this.http.post("/api/wfPrimary/createWorkflowItems",e).subscribe(e=>t(e),e=>i(e))})}getWorkflowDetails(e){return new Promise((t,i)=>{this.http.post("/api/wfPrimary/getWorkflowDetails",{workflowId:e}).subscribe(e=>t(e),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while getting workflow details",e&&e.params?e.params:e&&e.error?e.error.params:{})})})}formatApproversHierarchy(e,t){return Object(r.c)(this,void 0,void 0,(function*(){0==e.length&&t.cc0&&t.cc0.appr0&&t.cc0.appr0.length>0&&e.push([{approvalType:"I",purposeOfInsertingIntoArray:"Just to ensure that 'I' type data is used, if it exists"}]);for(let i=0;i<e.length;i++){let r=[],a=n.keys(t["cc"+i]);for(let n=0;n<a.length;n++)for(let o=0;o<t["cc"+i][a[n]].length;o++){let s={name:t["cc"+i][a[n]][o].DELEGATE_NAME,oid:t["cc"+i][a[n]][o].DELEGATE_OID,level:n+1,designation:t["cc"+i][a[n]][o].DELEGATE_DESIGNATION_NAME,is_delegated:t["cc"+i][a[n]][o].IS_DELEGATED,role:t["cc"+i][a[n]][o].DELEGATE_ROLE_NAME};if(1==t["cc"+i][a[n]][o].IS_DELEGATED&&(s.delegated_by={name:t["cc"+i][a[n]][o].APPROVER_NAME,oid:t["cc"+i][a[n]][o].APPROVER_OID,level:n+1,designation:t["cc"+i][a[n]][o].APPROVER_DESIGNATION_NAME}),r.push(s),i==e.length-1&&n==a.length-1&&o==t["cc"+i][a[n]].length-1)return r}}}))}storeComments(e,t,i){return new Promise((r,n)=>{this.http.post("/api/wfPrimary/updateComments",{workflowHeaderId:e,newComments:t,commentor:i}).subscribe(e=>r(e),e=>(this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server Error while Updating Workflow Comments",e&&e.params?e.params:e&&e.error?e.error.params:{}),n(e)))})}updateWorkflowItems(e){return new Promise((t,i)=>{this.http.post("/api/wfPrimary/updateWorkflowItems",e).subscribe(e=>t(e),e=>(console.log(e),i(e)))})}formatApproversHierarchyForOpportunityApprovalActivity(e){return Object(r.c)(this,void 0,void 0,(function*(){for(let t=0;t<1;t++){let i=[],r=n.keys(e["cc"+t]);for(let n=0;n<r.length;n++)for(let a=0;a<e["cc"+t][r[n]].length;a++){let o={name:e["cc"+t][r[n]][a].DELEGATE_NAME,oid:e["cc"+t][r[n]][a].DELEGATE_OID,level:e["cc"+t][r[n]][a].APPROVAL_ORDER,designation:e["cc"+t][r[n]][a].DELEGATE_DESIGNATION_NAME,is_delegated:e["cc"+t][r[n]][a].IS_DELEGATED};if(1==e["cc"+t][r[n]][a].IS_DELEGATED&&(o.delegated_by={name:e["cc"+t][r[n]][a].APPROVER_NAME,oid:e["cc"+t][r[n]][a].APPROVER_OID,level:e["cc"+t][r[n]][a].APPROVAL_ORDER,designation:e["cc"+t][r[n]][a].APPROVER_DESIGNATION_NAME}),i.push(o),n==r.length-1&&a==e["cc"+t][r[n]].length-1)return i}}}))}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275inject"](o.c),a["\u0275\u0275inject"](s.a))},e.\u0275prov=a["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},w76M:function(e,t,i){"use strict";i.d(t,"a",(function(){return d})),i.d(t,"b",(function(){return c}));var r=i("jhN1"),n=i("fXoL"),a=i("oHs6"),o=i("PVOt"),s=i("6t9p");const l=["*"];let d=(()=>{let e=class extends o.b{constructor(e,t,i,r,n,a,o,s){super(e,t,i,r,o,s),this._watcherHelper=r,this._idh=n,this._createEventEmitters([{subscribe:"contentReady",emit:"onContentReady"},{subscribe:"disposing",emit:"onDisposing"},{subscribe:"hidden",emit:"onHidden"},{subscribe:"hiding",emit:"onHiding"},{subscribe:"initialized",emit:"onInitialized"},{subscribe:"optionChanged",emit:"onOptionChanged"},{subscribe:"resize",emit:"onResize"},{subscribe:"resizeEnd",emit:"onResizeEnd"},{subscribe:"resizeStart",emit:"onResizeStart"},{subscribe:"showing",emit:"onShowing"},{subscribe:"shown",emit:"onShown"},{subscribe:"titleRendered",emit:"onTitleRendered"},{emit:"accessKeyChange"},{emit:"animationChange"},{emit:"closeOnOutsideClickChange"},{emit:"containerChange"},{emit:"contentTemplateChange"},{emit:"deferRenderingChange"},{emit:"disabledChange"},{emit:"dragEnabledChange"},{emit:"elementAttrChange"},{emit:"focusStateEnabledChange"},{emit:"fullScreenChange"},{emit:"heightChange"},{emit:"hintChange"},{emit:"hoverStateEnabledChange"},{emit:"maxHeightChange"},{emit:"maxWidthChange"},{emit:"minHeightChange"},{emit:"minWidthChange"},{emit:"positionChange"},{emit:"resizeEnabledChange"},{emit:"rtlEnabledChange"},{emit:"shadingChange"},{emit:"shadingColorChange"},{emit:"showCloseButtonChange"},{emit:"showTitleChange"},{emit:"tabIndexChange"},{emit:"titleChange"},{emit:"titleTemplateChange"},{emit:"toolbarItemsChange"},{emit:"visibleChange"},{emit:"widthChange"}]),this._idh.setHost(this),a.setHost(this)}get accessKey(){return this._getOption("accessKey")}set accessKey(e){this._setOption("accessKey",e)}get animation(){return this._getOption("animation")}set animation(e){this._setOption("animation",e)}get closeOnOutsideClick(){return this._getOption("closeOnOutsideClick")}set closeOnOutsideClick(e){this._setOption("closeOnOutsideClick",e)}get container(){return this._getOption("container")}set container(e){this._setOption("container",e)}get contentTemplate(){return this._getOption("contentTemplate")}set contentTemplate(e){this._setOption("contentTemplate",e)}get deferRendering(){return this._getOption("deferRendering")}set deferRendering(e){this._setOption("deferRendering",e)}get disabled(){return this._getOption("disabled")}set disabled(e){this._setOption("disabled",e)}get dragEnabled(){return this._getOption("dragEnabled")}set dragEnabled(e){this._setOption("dragEnabled",e)}get elementAttr(){return this._getOption("elementAttr")}set elementAttr(e){this._setOption("elementAttr",e)}get focusStateEnabled(){return this._getOption("focusStateEnabled")}set focusStateEnabled(e){this._setOption("focusStateEnabled",e)}get fullScreen(){return this._getOption("fullScreen")}set fullScreen(e){this._setOption("fullScreen",e)}get height(){return this._getOption("height")}set height(e){this._setOption("height",e)}get hint(){return this._getOption("hint")}set hint(e){this._setOption("hint",e)}get hoverStateEnabled(){return this._getOption("hoverStateEnabled")}set hoverStateEnabled(e){this._setOption("hoverStateEnabled",e)}get maxHeight(){return this._getOption("maxHeight")}set maxHeight(e){this._setOption("maxHeight",e)}get maxWidth(){return this._getOption("maxWidth")}set maxWidth(e){this._setOption("maxWidth",e)}get minHeight(){return this._getOption("minHeight")}set minHeight(e){this._setOption("minHeight",e)}get minWidth(){return this._getOption("minWidth")}set minWidth(e){this._setOption("minWidth",e)}get position(){return this._getOption("position")}set position(e){this._setOption("position",e)}get resizeEnabled(){return this._getOption("resizeEnabled")}set resizeEnabled(e){this._setOption("resizeEnabled",e)}get rtlEnabled(){return this._getOption("rtlEnabled")}set rtlEnabled(e){this._setOption("rtlEnabled",e)}get shading(){return this._getOption("shading")}set shading(e){this._setOption("shading",e)}get shadingColor(){return this._getOption("shadingColor")}set shadingColor(e){this._setOption("shadingColor",e)}get showCloseButton(){return this._getOption("showCloseButton")}set showCloseButton(e){this._setOption("showCloseButton",e)}get showTitle(){return this._getOption("showTitle")}set showTitle(e){this._setOption("showTitle",e)}get tabIndex(){return this._getOption("tabIndex")}set tabIndex(e){this._setOption("tabIndex",e)}get title(){return this._getOption("title")}set title(e){this._setOption("title",e)}get titleTemplate(){return this._getOption("titleTemplate")}set titleTemplate(e){this._setOption("titleTemplate",e)}get toolbarItems(){return this._getOption("toolbarItems")}set toolbarItems(e){this._setOption("toolbarItems",e)}get visible(){return this._getOption("visible")}set visible(e){this._setOption("visible",e)}get width(){return this._getOption("width")}set width(e){this._setOption("width",e)}get toolbarItemsChildren(){return this._getOption("toolbarItems")}set toolbarItemsChildren(e){this.setChildren("toolbarItems",e)}_createInstance(e,t){return new a.a(e,t)}ngOnDestroy(){this._destroyWidget()}ngOnChanges(e){super.ngOnChanges(e),this.setupChanges("toolbarItems",e)}setupChanges(e,t){e in this._optionsToUpdate||this._idh.setup(e,t)}ngDoCheck(){this._idh.doCheck("toolbarItems"),this._watcherHelper.checkWatchers(),super.ngDoCheck(),super.clearChangedOptions()}_setOption(e,t){let i=this._idh.setupSingle(e,t),r=null!==this._idh.getChanges(e,t);(i||r)&&super._setOption(e,t)}};return e.\u0275fac=function(t){return new(t||e)(n["\u0275\u0275directiveInject"](n.ElementRef),n["\u0275\u0275directiveInject"](n.NgZone),n["\u0275\u0275directiveInject"](o.e),n["\u0275\u0275directiveInject"](o.j),n["\u0275\u0275directiveInject"](o.g),n["\u0275\u0275directiveInject"](o.i),n["\u0275\u0275directiveInject"](r.h),n["\u0275\u0275directiveInject"](n.PLATFORM_ID))},e.\u0275cmp=n["\u0275\u0275defineComponent"]({type:e,selectors:[["dx-popup"]],contentQueries:function(e,t,i){if(1&e&&n["\u0275\u0275contentQuery"](i,s.L,!1),2&e){let e;n["\u0275\u0275queryRefresh"](e=n["\u0275\u0275loadQuery"]())&&(t.toolbarItemsChildren=e)}},inputs:{accessKey:"accessKey",animation:"animation",closeOnOutsideClick:"closeOnOutsideClick",container:"container",contentTemplate:"contentTemplate",deferRendering:"deferRendering",disabled:"disabled",dragEnabled:"dragEnabled",elementAttr:"elementAttr",focusStateEnabled:"focusStateEnabled",fullScreen:"fullScreen",height:"height",hint:"hint",hoverStateEnabled:"hoverStateEnabled",maxHeight:"maxHeight",maxWidth:"maxWidth",minHeight:"minHeight",minWidth:"minWidth",position:"position",resizeEnabled:"resizeEnabled",rtlEnabled:"rtlEnabled",shading:"shading",shadingColor:"shadingColor",showCloseButton:"showCloseButton",showTitle:"showTitle",tabIndex:"tabIndex",title:"title",titleTemplate:"titleTemplate",toolbarItems:"toolbarItems",visible:"visible",width:"width"},outputs:{onContentReady:"onContentReady",onDisposing:"onDisposing",onHidden:"onHidden",onHiding:"onHiding",onInitialized:"onInitialized",onOptionChanged:"onOptionChanged",onResize:"onResize",onResizeEnd:"onResizeEnd",onResizeStart:"onResizeStart",onShowing:"onShowing",onShown:"onShown",onTitleRendered:"onTitleRendered",accessKeyChange:"accessKeyChange",animationChange:"animationChange",closeOnOutsideClickChange:"closeOnOutsideClickChange",containerChange:"containerChange",contentTemplateChange:"contentTemplateChange",deferRenderingChange:"deferRenderingChange",disabledChange:"disabledChange",dragEnabledChange:"dragEnabledChange",elementAttrChange:"elementAttrChange",focusStateEnabledChange:"focusStateEnabledChange",fullScreenChange:"fullScreenChange",heightChange:"heightChange",hintChange:"hintChange",hoverStateEnabledChange:"hoverStateEnabledChange",maxHeightChange:"maxHeightChange",maxWidthChange:"maxWidthChange",minHeightChange:"minHeightChange",minWidthChange:"minWidthChange",positionChange:"positionChange",resizeEnabledChange:"resizeEnabledChange",rtlEnabledChange:"rtlEnabledChange",shadingChange:"shadingChange",shadingColorChange:"shadingColorChange",showCloseButtonChange:"showCloseButtonChange",showTitleChange:"showTitleChange",tabIndexChange:"tabIndexChange",titleChange:"titleChange",titleTemplateChange:"titleTemplateChange",toolbarItemsChange:"toolbarItemsChange",visibleChange:"visibleChange",widthChange:"widthChange"},features:[n["\u0275\u0275ProvidersFeature"]([o.e,o.j,o.i,o.g]),n["\u0275\u0275InheritDefinitionFeature"],n["\u0275\u0275NgOnChangesFeature"]],ngContentSelectors:l,decls:1,vars:0,template:function(e,t){1&e&&(n["\u0275\u0275projectionDef"](),n["\u0275\u0275projection"](0))},encapsulation:2}),e})(),c=(()=>{let e=class{};return e.\u0275mod=n["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=n["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[s.bb,s.Gc,s.Vd,s.vd,s.hb,s.lb,s.sb,s.id,s.jd,s.M,o.c,o.f,r.b],s.bb,s.Gc,s.Vd,s.vd,s.hb,s.lb,s.sb,s.id,s.jd,s.M,o.f]}),e})()}}]);