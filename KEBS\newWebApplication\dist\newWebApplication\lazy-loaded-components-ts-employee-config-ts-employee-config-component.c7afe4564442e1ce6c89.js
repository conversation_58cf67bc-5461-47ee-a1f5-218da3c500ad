(window.webpackJsonp=window.webpackJsonp||[]).push([[768,535,631,634,858],{NJ67:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));class i{constructor(){this.disabled=!1}onChange(e){}onTouched(e){}writeValue(e){this.value=e}registerOnChange(e){this.onChange=e}registerOnTouched(e){this.onTouched=e}setDisabledState(e){this.disabled=e}}},"TmG/":function(e,t,n){"use strict";n.d(t,"a",(function(){return v}));var i=n("fXoL"),r=n("3Pt+"),s=n("jtHE"),o=n("XNiG"),a=n("NJ67"),l=n("1G5W"),c=n("kmnG"),d=n("ofXK"),m=n("d3UM"),p=n("FKr1"),h=n("WJ5W"),u=n("Qu3c");function f(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-label"),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](e.placeholder)}}function g(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-option",7),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275property"]("value",null),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](e.disableNone?"Select One":"None")}}function C(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"mat-option",8),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const n=t.$implicit;return i["\u0275\u0275nextContext"]().emitChanges(n)})),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;i["\u0275\u0275property"]("value",e.id||e._id)("matTooltip",e.name),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let v=(()=>{class e extends a.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new r.j,this.fieldFilterCtrl=new r.j,this.list=[],this.required=!1,this.disableNone=!1,this.valueChange=new i.EventEmitter,this.disabled=!1,this.hasNoneOption=!0,this.filteredList=new s.a,this.change=new i.EventEmitter,this.hideMatLabel=!1,this._onDestroy=new o.b,this.emitChanges=e=>{this.change.emit(e)}}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(l.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(l.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](i.Renderer2))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search"]],inputs:{list:"list",placeholder:"placeholder",required:"required",disableNone:"disableNone",disabled:"disabled",hasNoneOption:"hasNoneOption",hideMatLabel:"hideMatLabel"},outputs:{valueChange:"valueChange",change:"change"},features:[i["\u0275\u0275ProvidersFeature"]([{provide:r.t,useExisting:Object(i.forwardRef)(()=>e),multi:!0}]),i["\u0275\u0275InheritDefinitionFeature"],i["\u0275\u0275NgOnChangesFeature"]],decls:9,vars:11,consts:[["appearance","outline"],[4,"ngIf"],[3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value",4,"ngIf"],[3,"value","matTooltip","click",4,"ngFor","ngForOf"],[3,"value"],[3,"value","matTooltip","click"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"mat-form-field",0),i["\u0275\u0275template"](1,f,2,1,"mat-label",1),i["\u0275\u0275elementStart"](2,"mat-select",2,3),i["\u0275\u0275elementStart"](4,"mat-option"),i["\u0275\u0275element"](5,"ngx-mat-select-search",4),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](6,g,2,2,"mat-option",5),i["\u0275\u0275template"](7,C,2,3,"mat-option",6),i["\u0275\u0275pipe"](8,"async"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",!t.hideMatLabel),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.hasNoneOption),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngForOf",i["\u0275\u0275pipeBind1"](8,9,t.filteredList)))},directives:[c.c,d.NgIf,m.c,r.v,r.k,r.F,p.p,h.a,d.NgForOf,c.g,u.a],pipes:[d.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%}"]}),e})()},nYau:function(e,t,n){"use strict";n.r(t),n.d(t,"TsEmployeeConfigComponent",(function(){return D}));var i=n("mrSG"),r=n("0IaG"),s=n("3Pt+"),o=n("fXoL"),a=n("Z2Ud"),l=n("LcQX"),c=n("BVzC"),d=n("NFeN"),m=n("Qu3c"),p=n("wZkO"),h=n("jaxi"),u=n("ofXK"),f=n("kmnG"),g=n("qFsG"),C=n("d3UM"),v=n("bTqV"),S=n("TmG/"),b=n("FKr1");function x(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"span"),o["\u0275\u0275text"](1),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]().$implicit;o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate"](e.cost_centre)}}function E(e,t){if(1&e&&o["\u0275\u0275element"](0,"app-input-search",41),2&e){const e=o["\u0275\u0275nextContext"](3);o["\u0275\u0275property"]("list",e.costCenterList)}}function y(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"span"),o["\u0275\u0275text"](1),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]().$implicit;o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate"](e.location)}}function w(e,t){if(1&e&&o["\u0275\u0275element"](0,"app-input-search",42),2&e){const e=o["\u0275\u0275nextContext"](3);o["\u0275\u0275property"]("list",e.locationList)}}function I(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"span"),o["\u0275\u0275text"](1),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]().$implicit;o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate"](e.percentage)}}function T(e,t){1&e&&o["\u0275\u0275element"](0,"input",43)}function L(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"tr"),o["\u0275\u0275elementStart"](1,"td"),o["\u0275\u0275template"](2,x,2,1,"span",19),o["\u0275\u0275template"](3,E,1,1,"app-input-search",34),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](4,"td"),o["\u0275\u0275template"](5,y,2,1,"span",19),o["\u0275\u0275template"](6,w,1,1,"app-input-search",35),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](7,"td"),o["\u0275\u0275template"](8,I,2,1,"span",19),o["\u0275\u0275template"](9,T,1,0,"input",36),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](10,"td"),o["\u0275\u0275elementStart"](11,"button",37),o["\u0275\u0275listener"]("click",(function(){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"](2).saveSubmit()})),o["\u0275\u0275elementStart"](12,"mat-icon",5),o["\u0275\u0275text"](13,"save"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](14,"button",38),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](e);const n=t.$implicit,i=t.index;return o["\u0275\u0275nextContext"](2).editCostCentre(n,i)})),o["\u0275\u0275elementStart"](15,"mat-icon",5),o["\u0275\u0275text"](16,"edit"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](17,"button",39),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](e);const n=t.index;return o["\u0275\u0275nextContext"](2).deleteCostCentre(n)})),o["\u0275\u0275elementStart"](18,"mat-icon",5),o["\u0275\u0275text"](19,"delete"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](20,"button",40),o["\u0275\u0275listener"]("click",(function(){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"](2).addCostCentre()})),o["\u0275\u0275elementStart"](21,"mat-icon",5),o["\u0275\u0275text"](22,"add"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngIf",!e.editable),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",e.editable),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngIf",!e.editable),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",e.editable),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngIf",!e.editable),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",e.editable)}}function O(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div"),o["\u0275\u0275elementStart"](1,"form",33),o["\u0275\u0275elementStart"](2,"table",31),o["\u0275\u0275elementStart"](3,"thead"),o["\u0275\u0275elementStart"](4,"tr"),o["\u0275\u0275elementStart"](5,"th"),o["\u0275\u0275text"](6,"Cost Centre"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](7,"th"),o["\u0275\u0275text"](8,"Location"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](9,"th"),o["\u0275\u0275text"](10,"Percentage"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](11,"th"),o["\u0275\u0275text"](12,"Operations"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](13,L,23,6,"tr",32),o["\u0275\u0275elementEnd"](),o["\u0275\u0275element"](14,"tbody"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("formGroup",e.ccForm),o["\u0275\u0275advance"](12),o["\u0275\u0275property"]("ngForOf",e.ccList)}}function M(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div"),o["\u0275\u0275elementStart"](1,"div"),o["\u0275\u0275elementStart"](2,"h4",44),o["\u0275\u0275text"](3," No Cost Centre Is Mapped ! "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](4,"div",45),o["\u0275\u0275element"](5,"img",46),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](6,"button",47),o["\u0275\u0275listener"]("click",(function(){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"]().addCostCentre()})),o["\u0275\u0275text"](7," Add New Cost Centre "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}}function _(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"mat-option",48),o["\u0275\u0275text"](1),o["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;o["\u0275\u0275property"]("value",e.id),o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" ",e.name," ")}}function A(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"td"),o["\u0275\u0275elementStart"](1,"span"),o["\u0275\u0275text"](2),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]().$implicit;o["\u0275\u0275attribute"]("rowspan",e.rowspan),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate"](e.id)}}function F(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"td"),o["\u0275\u0275elementStart"](1,"span"),o["\u0275\u0275text"](2),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]().$implicit;o["\u0275\u0275attribute"]("rowspan",e.rowspan),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate"](e.name)}}function N(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"tr"),o["\u0275\u0275template"](1,A,3,2,"td",19),o["\u0275\u0275template"](2,F,3,2,"td",19),o["\u0275\u0275elementStart"](3,"td"),o["\u0275\u0275elementStart"](4,"span"),o["\u0275\u0275text"](5),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](6,"td"),o["\u0275\u0275elementStart"](7,"span"),o["\u0275\u0275text"](8),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](9,"td"),o["\u0275\u0275elementStart"](10,"span"),o["\u0275\u0275text"](11),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",e.displayAll),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",e.displayAll),o["\u0275\u0275advance"](3),o["\u0275\u0275textInterpolate"](e.sessionCode),o["\u0275\u0275advance"](3),o["\u0275\u0275textInterpolate"](e.sessionStTime),o["\u0275\u0275advance"](3),o["\u0275\u0275textInterpolate"](e.sessionEndTime)}}let D=(()=>{class e{constructor(e,t,n,i,r,s){this._myteamService=e,this.dialogRef=t,this.utilityService=n,this.inData=i,this.fb=r,this.errorService=s,this.ccList=[],this.isVisible=!1,this.costCenterList=[],this.locationList=[],this.newAttribute={editable:!0},this.addNewCCFlag=!1,this.costCenter="",this.location="",this.splitup=null,this.restrictCC="",this.rfidAssociateId="",this.timesheetSchedule=0,this.schedule=[],this.sessionArray=[],this.scheduleArrayDisplay=[],this.editIndex=-1,this.ops="",this.ccList=[],this.associateData=this.inData.modalParams,this.restrictCC=0==this.associateData.restrictMappedCC?"no":"yes"}editCostCentre(e,t){e.editable=!e.editable,this.editIndex=t,this.ops="edit"}ngOnInit(){this.ccForm=this.fb.group({costCenter:[null,s.H.required],costCenterDescription:[null],location:[null,s.H.required],orgType:[null],splitup:[0]}),this.getMappedCCForEmployee(),this.getCostCentreList(),this.getLocationList(),this.getTimesheetScheduleAndSession()}getCostCentreList(){this._myteamService.getCostCentreList().subscribe(e=>Object(i.c)(this,void 0,void 0,(function*(){e.data&&e.data.length>0&&"S"==e.messType?this.costCenterList=e.data:this.utilityService.showToastMessage("No Cost Centre List Found")})),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server Error while retrieving Cost Centre List",e&&e.params?e.params:e&&e.error?e.error.params:{})})}getLocationList(){this._myteamService.getLocationList().subscribe(e=>Object(i.c)(this,void 0,void 0,(function*(){e.data&&e.data.length>0&&"S"==e.messType?this.locationList=e.data:this.utilityService.showToastMessage("No Location List Found")})),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server Error while retrieving Location List",e&&e.params?e.params:e&&e.error?e.error.params:{})})}getMappedCCForEmployee(){this._myteamService.getMappedCCForEmployee(this.associateData.oid).subscribe(e=>Object(i.c)(this,void 0,void 0,(function*(){if(e.data&&"S"==e.messType){console.log(e.data),this.ccList=e.data;for(let e of this.ccList)e.editable=!1}else this.ccList=[],this.utilityService.showToastMessage(e.messText)})),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server Error while retrieving the mapped cost centre for the employee ",e&&e.params?e.params:e&&e.error?e.error.params:{})})}addCostCentre(){this.ops="add",this.editIndex=-1,this.ccList.push(this.newAttribute),this.addNewCCFlag=!0,this.newAttribute={editable:!0}}deleteCostCentre(e){this._myteamService.deleteCostCentre(this.ccList[e],this.associateData.oid).subscribe(e=>Object(i.c)(this,void 0,void 0,(function*(){"S"==e.messType?(this.utilityService.showToastMessage(e.messText),this.getMappedCCForEmployee()):this.utilityService.showToastMessage(e.messText)})),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server Error while delelting the costcentre data",e&&e.params?e.params:e&&e.error?e.error.params:{})})}saveSubmit(){let e;this.setCostCentreAndLocation(),this.ccForm.valid&&(e=this.ccForm.value),""!=this.ops?this._myteamService.saveEmployeeCC(e,this.associateData.oid,this.ops,this.editIndex).subscribe(e=>Object(i.c)(this,void 0,void 0,(function*(){"S"==e.messType?(this.getMappedCCForEmployee(),this.addNewCCFlag=!1,this.utilityService.showToastMessage(e.messText)):this.utilityService.showToastMessage(e.messText)})),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server Error while Saving The Employee Details",e&&e.params?e.params:e&&e.error?e.error.params:{})}):this.utilityService.showToastMessage("Already Data Saved")}setCostCentreAndLocation(){let e=this.ccForm.get("costCenter").value,t=this.ccForm.get("location").value;for(let n of this.costCenterList)n.id==e&&(this.ccForm.get("costCenter").patchValue(n.cost_centre),this.ccForm.get("costCenterDescription").patchValue(n.cc_description),this.ccForm.get("orgType").patchValue(n.org_type));for(let n of this.locationList)n.id==t&&this.ccForm.get("location").patchValue(n.name)}closeForm(){this.dialogRef.close()}toggleCCRestriction(e){this.restrictCC=e.value,this._myteamService.restrictMappecCostCentre(this.restrictCC,this.associateData.oid).subscribe(e=>Object(i.c)(this,void 0,void 0,(function*(){"S"==e.messType?(this.associateData.restrictMappedCC="yes"==this.restrictCC?1:0,this.utilityService.showToastMessage(e.messText)):this.utilityService.showToastMessage(e.messText)})),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server Error while Making The Cost Centre Restriction In Timesheet For The Employee",e&&e.params?e.params:e&&e.error?e.error.params:{})})}getTimesheetScheduleAndSession(){this.schedule=[],this.sessionArray=[],this.scheduleArrayDisplay=[],this._myteamService.getTimesheetScheduleAndSession(this.associateData.oid).subscribe(e=>Object(i.c)(this,void 0,void 0,(function*(){if("S"==e.messType&&e.data&&e.data.length>0){this.rfidAssociateId=null!=e.data[0].rfid_id?e.data[0].rfid_id:0,this.timesheetSchedule=null!=e.data[0].timesheet_schedule?e.data[0].timesheet_schedule:0,this.schedule=e.schedule,this.sessionArray=e.session;for(let e of this.schedule)e.session=this.sessionArray.length>0?this.sessionArray.filter(t=>Array.isArray(JSON.parse(t.timesheet_schedule_id))&&t.timesheet_schedule_id.includes(e.id)):[];for(let e=0;e<this.schedule.length;e++)if(1==this.schedule[e].session.length)this.scheduleArrayDisplay.push({id:this.schedule[e].id,name:this.schedule[e].name,sessionCode:this.schedule[e].session[0].sessionCode,sessionStTime:this.schedule[e].session[0].start_time,sessionEndTime:this.schedule[e].session[0].end_time,displayAll:!0,rowspan:this.schedule[e].session.length});else{this.scheduleArrayDisplay.push({id:this.schedule[e].id,name:this.schedule[e].name,sessionCode:this.schedule[e].session[0].sessionCode,sessionStTime:this.schedule[e].session[0].start_time,sessionEndTime:this.schedule[e].session[0].end_time,displayAll:!0,rowspan:this.schedule[e].session.length});for(let t=1;t<this.schedule[e].session.length;e++)this.scheduleArrayDisplay.push({sessionCode:this.schedule[e].session[t].sessionCode,sessionStTime:this.schedule[e].session[t].start_time,sessionEndTime:this.schedule[e].session[t].end_time,rowspan:this.schedule[e].session.length})}console.log(this.scheduleArrayDisplay)}else this.utilityService.showToastMessage(e.messText)})),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server Error While Getting The RFID ID, Session And Schedule Of The Employee",e&&e.params?e.params:e&&e.error?e.error.params:{})})}updateTimesheetScheduleAndRfidId(){this._myteamService.updateTimesheetScheduleAndRfidId(this.associateData.oid,this.rfidAssociateId,this.timesheetSchedule).subscribe(e=>Object(i.c)(this,void 0,void 0,(function*(){"S"==e.messType?(this.utilityService.showToastMessage(e.messText),this.getTimesheetScheduleAndSession()):this.utilityService.showToastMessage(e.messText)})),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server Error While Updating The RFID ID, Session And Schedule Of The Employee",e&&e.params?e.params:e&&e.error?e.error.params:{})})}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](a.a),o["\u0275\u0275directiveInject"](r.h),o["\u0275\u0275directiveInject"](l.a),o["\u0275\u0275directiveInject"](r.a),o["\u0275\u0275directiveInject"](s.i),o["\u0275\u0275directiveInject"](c.a))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-ts-employee-config"]],decls:77,vars:13,consts:[[1,"container-fluild","pl-2","pr-2","ts-employee-config-style"],[1,"col-12"],[1,"row","border-bottom","solid"],[1,"col-11","pt-2","pb-2","d-flex"],["mat-icon-button","",1,"bubble"],[1,"iconButton"],[1,"name","my-auto","ml-2"],[1,"col-1","d-flex","pt-2"],["matTooltip","Close",1,"close-Icon",2,"cursor","pointer","color","#4d4d4b",3,"click"],[1,"col-12","pt-3","pb-3"],[1,"row"],[1,"col-3"],[1,"value14RedBold"],[1,"value14",3,"matTooltip"],["label","Employee Cost Centre Config"],[1,"col-9","my-auto"],["name","favoriteColor","aria-label","Favorite Color",3,"value","change"],["value","yes",2,"width","70px",3,"ngClass"],["value","no",2,"width","70px",3,"ngClass"],[4,"ngIf"],["label","Employee Session Config"],[1,"col-12","pt-3"],[1,"col-5"],["appearance","outline","floatLabel","always"],["matInput","","type","text","required","",3,"ngModel","ngModelChange"],[3,"ngModel","ngModelChange"],["required","",3,"value",4,"ngFor","ngForOf"],[1,"col-2"],["mat-icon-button","","matTooltip","Save",1,"iconsSize","ml-2",3,"click"],[1,"iconsSize"],[1,"pt-2","pb-2","pl-2"],[1,"table","table-hover"],[4,"ngFor","ngForOf"],[3,"formGroup"],["class","cost-centre-search-field-inputsearch","required","true","placeholder","Cost Center","formControlName","costCenter",3,"list",4,"ngIf"],["class","location-search-field-inputsearch","required","true","placeholder","Location","formControlName","location",3,"list",4,"ngIf"],["type","number","min","0","max","100","class","form-control","formControlName","splitup",4,"ngIf"],["mat-icon-button","","matTooltip","Save",1,"trend-button-inactive",3,"click"],["mat-icon-button","","matTooltip","Edit",1,"trend-button-inactive",3,"click"],["mat-icon-button","","matTooltip","Delete",1,"trend-button-inactive",3,"click"],["mat-icon-button","","matTooltip","Add Cost Centre",1,"trend-button-inactive",3,"click"],["required","true","placeholder","Cost Center","formControlName","costCenter",1,"cost-centre-search-field-inputsearch",3,"list"],["required","true","placeholder","Location","formControlName","location",1,"location-search-field-inputsearch",3,"list"],["type","number","min","0","max","100","formControlName","splitup",1,"form-control"],[1,"d-flex","justify-content-center","align-items-center","mt-0","mb-0","slide-in-top"],[1,"d-flex","justify-content-center","align-items-center","slide-from-down"],["src","https://assets.kebs.app/images/nomilestone.png","height","220","width","250",1,"mt-3"],["mat-raised-button","",1,"mt-3","btn-active","slide-from-down",3,"click"],["required","",3,"value"]],template:function(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"div",0),o["\u0275\u0275elementStart"](1,"div",1),o["\u0275\u0275elementStart"](2,"div",2),o["\u0275\u0275elementStart"](3,"div",3),o["\u0275\u0275elementStart"](4,"div",4),o["\u0275\u0275elementStart"](5,"mat-icon",5),o["\u0275\u0275text"](6,"badge"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](7,"span",6),o["\u0275\u0275text"](8,"Employee Configuration"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](9,"div",7),o["\u0275\u0275elementStart"](10,"mat-icon",8),o["\u0275\u0275listener"]("click",(function(){return t.closeForm()})),o["\u0275\u0275text"](11,"close"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](12,"div",9),o["\u0275\u0275elementStart"](13,"div",10),o["\u0275\u0275elementStart"](14,"div",11),o["\u0275\u0275elementStart"](15,"p",12),o["\u0275\u0275text"](16,"Employee Name"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](17,"div",11),o["\u0275\u0275elementStart"](18,"p",13),o["\u0275\u0275text"](19),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](20,"div",11),o["\u0275\u0275elementStart"](21,"p",12),o["\u0275\u0275text"](22,"Employment Type"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](23,"div",11),o["\u0275\u0275elementStart"](24,"p",13),o["\u0275\u0275text"](25),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](26,"mat-tab-group"),o["\u0275\u0275elementStart"](27,"mat-tab",14),o["\u0275\u0275elementStart"](28,"div",9),o["\u0275\u0275elementStart"](29,"div",10),o["\u0275\u0275elementStart"](30,"div",15),o["\u0275\u0275elementStart"](31,"p",12),o["\u0275\u0275text"](32,"Allow Only Mapped Cost Centre In Timesheet"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](33,"div",11),o["\u0275\u0275elementStart"](34,"mat-button-toggle-group",16),o["\u0275\u0275listener"]("change",(function(e){return t.toggleCCRestriction(e)})),o["\u0275\u0275elementStart"](35,"mat-button-toggle",17),o["\u0275\u0275text"](36,"Yes"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](37,"mat-button-toggle",18),o["\u0275\u0275text"](38,"No"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](39,O,15,2,"div",19),o["\u0275\u0275template"](40,M,8,0,"div",19),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](41,"mat-tab",20),o["\u0275\u0275elementStart"](42,"div",21),o["\u0275\u0275elementStart"](43,"div",10),o["\u0275\u0275elementStart"](44,"div",22),o["\u0275\u0275elementStart"](45,"mat-form-field",23),o["\u0275\u0275elementStart"](46,"mat-label"),o["\u0275\u0275text"](47,"RFID Id"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](48,"input",24),o["\u0275\u0275listener"]("ngModelChange",(function(e){return t.rfidAssociateId=e})),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](49,"div",22),o["\u0275\u0275elementStart"](50,"mat-form-field",23),o["\u0275\u0275elementStart"](51,"mat-label"),o["\u0275\u0275text"](52,"Auto Timesheet Schedule"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](53,"mat-select",25),o["\u0275\u0275listener"]("ngModelChange",(function(e){return t.timesheetSchedule=e})),o["\u0275\u0275template"](54,_,2,2,"mat-option",26),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](55,"div",27),o["\u0275\u0275elementStart"](56,"button",28),o["\u0275\u0275listener"]("click",(function(){return t.updateTimesheetScheduleAndRfidId()})),o["\u0275\u0275elementStart"](57,"mat-icon",29),o["\u0275\u0275text"](58,"save"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](59,"div",21),o["\u0275\u0275elementStart"](60,"div",30),o["\u0275\u0275elementStart"](61,"span",12),o["\u0275\u0275text"](62," Auto Timesheet Session And Schedule Details "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](63,"table",31),o["\u0275\u0275elementStart"](64,"thead"),o["\u0275\u0275elementStart"](65,"tr"),o["\u0275\u0275elementStart"](66,"th"),o["\u0275\u0275text"](67,"Schedule Id"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](68,"th"),o["\u0275\u0275text"](69,"Schedule Name"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](70,"th"),o["\u0275\u0275text"](71,"Session Code"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](72,"th"),o["\u0275\u0275text"](73,"Session Start Time"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](74,"th"),o["\u0275\u0275text"](75,"Session End Time"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](76,N,12,5,"tr",32),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e&&(o["\u0275\u0275advance"](18),o["\u0275\u0275property"]("matTooltip",t.associateData.name),o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate"](t.associateData.name),o["\u0275\u0275advance"](5),o["\u0275\u0275property"]("matTooltip",t.associateData.group_description),o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate"](t.associateData.group_description),o["\u0275\u0275advance"](9),o["\u0275\u0275property"]("value",t.restrictCC),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngClass","yes"==t.restrictCC?"btn-toggle-selected":""),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngClass","no"==t.restrictCC?"btn-toggle-selected":""),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngIf",t.ccList.length>0),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",0==t.ccList.length),o["\u0275\u0275advance"](8),o["\u0275\u0275property"]("ngModel",t.rfidAssociateId),o["\u0275\u0275advance"](5),o["\u0275\u0275property"]("ngModel",t.timesheetSchedule),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngForOf",t.schedule),o["\u0275\u0275advance"](22),o["\u0275\u0275property"]("ngForOf",t.scheduleArrayDisplay))},directives:[d.a,m.a,p.c,p.a,h.b,h.a,u.NgClass,u.NgIf,f.c,f.g,g.b,s.e,s.F,s.v,s.y,C.c,u.NgForOf,v.a,s.J,s.w,s.n,S.a,s.l,s.A,b.p],styles:[".cost-centre-search-field-inputsearch[_ngcontent-%COMP%], .location-search-field-inputsearch[_ngcontent-%COMP%]{font-size:13px!important}.cost-centre-search-field-inputsearch[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%], .location-search-field-inputsearch[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]{width:70%!important}.trend-button-inactive[_ngcontent-%COMP%]{line-height:8px;width:26px;height:26px;margin-right:12px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.trend-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.trend-button-inactive[_ngcontent-%COMP%]   .iconButtonWarn[_ngcontent-%COMP%]{color:#cf0001;font-size:18px}.trend-button-inactive[_ngcontent-%COMP%]   .iconButtonWarnAnim[_ngcontent-%COMP%]{color:#cf0001;font-size:18px;animation:blink 1.1s cubic-bezier(.5,0,1,1) infinite alternate}.btn-active[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff;font-weight:400;font-size:13px!important;min-width:60px;line-height:30px;padding:0 25px;border-radius:0!important;left:285px}.value14RedBold[_ngcontent-%COMP%]{color:#cf0001;font-weight:500!important;display:inline}.value14[_ngcontent-%COMP%], .value14RedBold[_ngcontent-%COMP%]{font-size:14px!important;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.value14[_ngcontent-%COMP%]{color:#000!important;font-weight:400!important}.btn-toggle-selected[_ngcontent-%COMP%]{font-size:12px!important;background-color:#c92020!important;color:#fff}.slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}.slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}"]}),e})()}}]);