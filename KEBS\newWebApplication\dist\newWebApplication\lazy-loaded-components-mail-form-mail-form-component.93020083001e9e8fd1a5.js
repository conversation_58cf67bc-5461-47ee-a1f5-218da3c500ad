(window.webpackJsonp=window.webpackJsonp||[]).push([[757],{"2QF7":function(t,e,i){"use strict";i.r(e),i.d(e,"MailFormComponent",(function(){return C}));var n=i("fXoL"),a=i("3Pt+"),o=i("wd/R"),r=i("ofXK"),c=i("dNgK"),s=i("kmnG"),l=(i("d3UM"),i("qFsG")),d=i("NFeN"),m=(i("/1cH"),i("bTqV")),p=i("iadO"),h=(i("WJ5W"),i("0IaG")),u=i("9jeV"),g=i("VsNQ"),f=i("tyNb"),v=i("XXEo"),b=i("ihCf");function y(t,e){if(1&t){const t=n["\u0275\u0275getCurrentView"]();n["\u0275\u0275elementStart"](0,"button",26),n["\u0275\u0275listener"]("click",(function(){return n["\u0275\u0275restoreView"](t),n["\u0275\u0275nextContext"]().createMail()})),n["\u0275\u0275elementStart"](1,"mat-icon"),n["\u0275\u0275text"](2," done_all"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()}}function M(t,e){if(1&t){const t=n["\u0275\u0275getCurrentView"]();n["\u0275\u0275elementStart"](0,"button",27),n["\u0275\u0275listener"]("click",(function(){return n["\u0275\u0275restoreView"](t),n["\u0275\u0275nextContext"]().editMail()})),n["\u0275\u0275elementStart"](1,"mat-icon"),n["\u0275\u0275text"](2," done_all"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()}}let C=(()=>{class t{constructor(t,e,i,o,r,c,s,l,d){this.dialogRef=t,this.dialogData=e,this.dialog=i,this.fb=o,this.activityToMainService=r,this.snackBar=c,this.contactService=s,this.route=l,this.loginService=d,this.close=new n.EventEmitter,this.currentUser=this.loginService.getProfile().profile,this.mailForm=this.fb.group({title:["",a.H.required],notes:[""],attachment:[""],remindMe:[""],dueDate:[""],applicationName:[""],contactId:[""],applicationId:[""],governanceType:[""]}),this.closeClicked=()=>{this.close.emit(),this.dialogRef.close()},this.editMail=()=>{this.mailForm.valid?this.contactService.editMail(this.activityId,this.mailForm.value).then(t=>{console.log(t),this.mailForm.reset(),this.snackBar.open("Mail updated Successfully!","Dismiss",{duration:2e3}),this.close.emit("close"),this.dialogRef.close("Update Required"),this.activityToMainService.sendMsg("reload")},t=>{this.snackBar.open("Failed to Edit mail.","Dismiss",{duration:2e3}),console.error(t)}):this.snackBar.open("Enter all Mandatory Fields!","Dismiss",{duration:2e3})},this.createMail=()=>{this.mailForm.valid?this.contactService.createMail(this.mailForm.value).subscribe(t=>{this.mailForm.reset(),this.snackBar.open("Mail Created Successfully!","Dismiss",{duration:2e3}),this.close.emit("close"),this.dialogRef.close("Update Required"),this.activityToMainService.sendMsg("reload"),console.log(t)},t=>{this.snackBar.open("Failed to create mail.","Dismiss",{duration:2e3}),console.error(t)}):this.snackBar.open("Enter all Mandatory Fields!","Dismiss",{duration:2e3})}}ngOnChanges(){}updateFormWithMailDetails(){if(console.log(this.mode,this.activityId),"Edit"==this.mode){let t=this.data?this.data:null;t?this.mailForm.patchValue({title:t.title,notes:t.notes?t.notes[0].notes:[],remindMe:t.reminder,dueDate:t.task_due_date,governanceType:t.governance_activity_id}):console.error("data not received!")}else this.mailForm.reset()}ngOnInit(){let t;this.mode=this.dialogData.mode,this.activityId=this.dialogData.activityId,this.data=this.dialogData.data,"Edit"==this.mode&&this.updateFormWithMailDetails(),"contacts"==window.location.pathname.split("/")[2]&&(t=34),this.contactService.getContactGovernanceType(34).subscribe(t=>{this.contactGovernanceTypes=t},t=>{console.log(t)}),this.mailForm.patchValue({applicationName:window.location.pathname.split("/")[2],contactId:window.location.pathname.split("/")[3],applicationId:t,governanceType:47,dueDate:o().format()})}ngOnDestroy(){}}return t.\u0275fac=function(e){return new(e||t)(n["\u0275\u0275directiveInject"](h.h),n["\u0275\u0275directiveInject"](h.a),n["\u0275\u0275directiveInject"](h.b),n["\u0275\u0275directiveInject"](a.i),n["\u0275\u0275directiveInject"](u.a),n["\u0275\u0275directiveInject"](c.a),n["\u0275\u0275directiveInject"](g.a),n["\u0275\u0275directiveInject"](f.a),n["\u0275\u0275directiveInject"](v.a))},t.\u0275cmp=n["\u0275\u0275defineComponent"]({type:t,selectors:[["app-mail-form"]],hostBindings:function(t,e){1&t&&n["\u0275\u0275listener"]("beforeunload",(function(){return e.ngOnDestroy()}),!1,n["\u0275\u0275resolveWindow"])},outputs:{close:"close"},features:[n["\u0275\u0275NgOnChangesFeature"]],decls:43,vars:6,consts:[[3,"formGroup"],[1,"container","createMailStyles"],[1,"row","border-bottom","solid"],[1,"col-11","pt-2","createAccount"],[1,"col-1","d-flex"],["mat-icon-button","",1,"ml-auto","close-button",3,"click"],[1,"close-Icon"],[1,"row",2,"margin-top","10px"],[1,"col-12"],[1,"row"],[1,"col-9","pl-0"],["appearance","outline",1,"create-account-field"],["matInput","","placeholder","title *","formControlName","title"],["matInput","","formControlName","dueDate",3,"matDatepicker"],["matSuffix","",3,"for"],["picker0",""],["appearance","outline",2,"width","100%","font-size","14px !important"],["matInput","","cdkTextareaAutosize","","cdkAutosizeMinRows","6","cdkAutosizeMaxRows","","placeholder","Notes","formControlName","notes"],["autosize","cdkTextareaAutosize"],[1,"row","pl-0","pt-4"],[1,"col-3","pl-0"],["mat-icon-button","","matTooltip","Attach File","type","submit",1,"iconbtn","ml-2","mt-0"],[1,"row","pt-4"],[1,"col-9","pr-5","quotes"],["mat-icon-button","","class","iconbtn ml-2 mt-0","matTooltip","Create Mail","type","submit",3,"click",4,"ngIf"],["mat-icon-button","","class","iconbtn ml-2 mt-0","matTooltip","Edit Mail","type","submit",3,"click",4,"ngIf"],["mat-icon-button","","matTooltip","Create Mail","type","submit",1,"iconbtn","ml-2","mt-0",3,"click"],["mat-icon-button","","matTooltip","Edit Mail","type","submit",1,"iconbtn","ml-2","mt-0",3,"click"]],template:function(t,e){if(1&t&&(n["\u0275\u0275elementStart"](0,"form",0),n["\u0275\u0275elementStart"](1,"div",1),n["\u0275\u0275elementStart"](2,"div",2),n["\u0275\u0275elementStart"](3,"div",3),n["\u0275\u0275text"](4),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](5,"div",4),n["\u0275\u0275elementStart"](6,"button",5),n["\u0275\u0275listener"]("click",(function(){return e.closeClicked()})),n["\u0275\u0275elementStart"](7,"mat-icon",6),n["\u0275\u0275text"](8,"close"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](9,"div",7),n["\u0275\u0275elementStart"](10,"div",8),n["\u0275\u0275elementStart"](11,"div",9),n["\u0275\u0275elementStart"](12,"div",10),n["\u0275\u0275elementStart"](13,"mat-form-field",11),n["\u0275\u0275elementStart"](14,"mat-label"),n["\u0275\u0275text"](15,"Title"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275element"](16,"input",12),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](17,"div",9),n["\u0275\u0275elementStart"](18,"div",10),n["\u0275\u0275elementStart"](19,"mat-form-field",11),n["\u0275\u0275elementStart"](20,"mat-label"),n["\u0275\u0275text"](21,"Due Date"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275element"](22,"input",13),n["\u0275\u0275element"](23,"mat-datepicker-toggle",14),n["\u0275\u0275element"](24,"mat-datepicker",null,15),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](26,"div",9),n["\u0275\u0275elementStart"](27,"div",10),n["\u0275\u0275elementStart"](28,"mat-form-field",16),n["\u0275\u0275element"](29,"textarea",17,18),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](31,"div",19),n["\u0275\u0275elementStart"](32,"div",20),n["\u0275\u0275elementStart"](33,"button",21),n["\u0275\u0275elementStart"](34,"mat-icon"),n["\u0275\u0275text"](35," attach_file"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275element"](36,"div",22),n["\u0275\u0275elementStart"](37,"div",22),n["\u0275\u0275elementStart"](38,"div",23),n["\u0275\u0275text"](39,' "What we dwell is, what we become" '),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](40,"div",20),n["\u0275\u0275template"](41,y,3,0,"button",24),n["\u0275\u0275template"](42,M,3,0,"button",25),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&t){const t=n["\u0275\u0275reference"](25);n["\u0275\u0275property"]("formGroup",e.mailForm),n["\u0275\u0275advance"](4),n["\u0275\u0275textInterpolate1"]("",e.mode," Mail "),n["\u0275\u0275advance"](18),n["\u0275\u0275property"]("matDatepicker",t),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("for",t),n["\u0275\u0275advance"](18),n["\u0275\u0275property"]("ngIf","Edit"!=e.mode),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf","Edit"==e.mode)}},directives:[a.J,a.w,a.n,m.a,d.a,s.c,s.g,l.b,a.e,a.v,a.l,p.g,p.i,s.i,p.f,b.b,r.NgIf],styles:[".createMailStyles[_ngcontent-%COMP%]{background-image:url(createMail.f39681f78ab7afb320f8.png);background-size:240px 200px;background-repeat:no-repeat;overflow-y:hidden!important;background-position:100% 40%;min-height:95vh}.createMailStyles[_ngcontent-%COMP%]   .side-title[_ngcontent-%COMP%]{color:#cf0001;font-size:14px}.createMailStyles[_ngcontent-%COMP%]   .field-invalid[_ngcontent-%COMP%]{font-size:small}.createMailStyles[_ngcontent-%COMP%]   .createAccount[_ngcontent-%COMP%]{font-size:14px;color:#cf0001;font-weight:500}.createMailStyles[_ngcontent-%COMP%]   .create-account-field[_ngcontent-%COMP%]{font-size:14px!important;width:100%!important;color:#66615b}.createMailStyles[_ngcontent-%COMP%]   .inputSearch[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-flex{padding:0 .75em;margin-top:-.25em;position:relative;height:41px;width:204px}.createMailStyles[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.createMailStyles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}.createMailStyles[_ngcontent-%COMP%]   .iconbtn[_ngcontent-%COMP%]{background-color:#c92020;color:#fff;padding:0;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.createMailStyles[_ngcontent-%COMP%]   .check-box-text[_ngcontent-%COMP%]{color:#66615b;font-size:14px}.createMailStyles[_ngcontent-%COMP%]   .vip-contact[_ngcontent-%COMP%]{color:#1a1a1a;font-size:14px}.createMailStyles[_ngcontent-%COMP%]   .quotes[_ngcontent-%COMP%]{font-style:italic;font-weight:500;color:#66615b;font-size:14px}.createMailStyles[_ngcontent-%COMP%]   .textArea[_ngcontent-%COMP%]{color:#66615b}"]}),t})()},"8uEH":function(t,e,i){"use strict";i.d(e,"a",(function(){return r}));var n=i("XNiG"),a=i("fXoL"),o=i("tk/3");let r=(()=>{class t{constructor(t){this.http=t,this.toMain=new n.b,this.sendMsg=t=>{this.toMain.next(t)},this.getMsg=()=>this.toMain.asObservable()}}return t.\u0275fac=function(e){return new(e||t)(a["\u0275\u0275inject"](o.c))},t.\u0275prov=a["\u0275\u0275defineInjectable"]({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()},"97Zw":function(t,e,i){"use strict";i.r(e),i.d(e,"MailFormComponent",(function(){return C}));var n=i("fXoL"),a=i("3Pt+"),o=i("wd/R"),r=i("ofXK"),c=i("dNgK"),s=i("kmnG"),l=(i("d3UM"),i("qFsG")),d=i("NFeN"),m=(i("/1cH"),i("bTqV")),p=i("iadO"),h=(i("WJ5W"),i("0IaG")),u=i("RJSY"),g=i("+yIk"),f=i("tyNb"),v=i("XXEo"),b=i("ihCf");function y(t,e){if(1&t){const t=n["\u0275\u0275getCurrentView"]();n["\u0275\u0275elementStart"](0,"button",26),n["\u0275\u0275listener"]("click",(function(){return n["\u0275\u0275restoreView"](t),n["\u0275\u0275nextContext"]().createMail()})),n["\u0275\u0275elementStart"](1,"mat-icon"),n["\u0275\u0275text"](2," done_all"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()}}function M(t,e){if(1&t){const t=n["\u0275\u0275getCurrentView"]();n["\u0275\u0275elementStart"](0,"button",27),n["\u0275\u0275listener"]("click",(function(){return n["\u0275\u0275restoreView"](t),n["\u0275\u0275nextContext"]().editMail()})),n["\u0275\u0275elementStart"](1,"mat-icon"),n["\u0275\u0275text"](2," done_all"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()}}let C=(()=>{class t{constructor(t,e,i,o,r,c,s,l,d){this.dialogRef=t,this.dialogData=e,this.dialog=i,this.fb=o,this.activityToMainService=r,this.snackBar=c,this.leadService=s,this.route=l,this.loginService=d,this.close=new n.EventEmitter,this.currentUser=this.loginService.getProfile().profile,this.mailForm=this.fb.group({title:["",a.H.required],notes:[""],attachment:[""],remindMe:[""],dueDate:[""],applicationName:[""],applicationReferenceId:[""],applicationId:[""],governanceType:[""]}),this.closeClicked=()=>{this.close.emit(),this.dialogRef.close()},this.editMail=()=>{this.mailForm.valid?this.leadService.editMail(this.activityId,this.mailForm.value).then(t=>{console.log(t),this.mailForm.reset(),this.snackBar.open("Mail updated Successfully!","Dismiss",{duration:2e3}),this.close.emit("close"),this.dialogRef.close("Update Required"),this.activityToMainService.sendMsg("reload")},t=>{this.snackBar.open("Failed to Edit mail.","Dismiss",{duration:2e3}),console.error(t)}):this.snackBar.open("Enter all Mandatory Fields!","Dismiss",{duration:2e3})},this.createMail=()=>{this.mailForm.valid?this.leadService.createMail(this.mailForm.value).subscribe(t=>{this.mailForm.reset(),this.snackBar.open("Mail Created Successfully!","Dismiss",{duration:2e3}),this.close.emit("close"),this.dialogRef.close("Update Required"),this.activityToMainService.sendMsg("reload"),console.log(t)},t=>{this.snackBar.open("Failed to create mail.","Dismiss",{duration:2e3}),console.error(t)}):this.snackBar.open("Enter all Mandatory Fields!","Dismiss",{duration:2e3})}}ngOnChanges(){}updateFormWithMailDetails(){if(console.log(this.mode,this.activityId),"Edit"==this.mode){let t=this.data?this.data:null;t?this.mailForm.patchValue({title:t.title,notes:t.notes?t.notes[0].notes:[],remindMe:t.reminder,dueDate:t.task_due_date,governanceType:t.governance_activity_id}):console.error("data not received!")}else this.mailForm.reset()}ngOnInit(){let t;this.mode=this.dialogData.mode,this.activityId=this.dialogData.activityId,this.data=this.dialogData.data,"Edit"==this.mode&&this.updateFormWithMailDetails(),"leads"==window.location.pathname.split("/")[2]?t=35:"opportunities"==window.location.pathname.split("/")[2]&&(t=36),this.leadService.getLeadsGovernanceType(75).subscribe(t=>{this.leadGovernanceTypes=t},t=>{console.log(t)}),this.mailForm.patchValue({applicationName:window.location.pathname.split("/")[2],applicationReferenceId:window.location.pathname.split("/")[3],applicationId:t,governanceType:37,dueDate:o().format()})}ngOnDestroy(){}}return t.\u0275fac=function(e){return new(e||t)(n["\u0275\u0275directiveInject"](h.h),n["\u0275\u0275directiveInject"](h.a),n["\u0275\u0275directiveInject"](h.b),n["\u0275\u0275directiveInject"](a.i),n["\u0275\u0275directiveInject"](u.a),n["\u0275\u0275directiveInject"](c.a),n["\u0275\u0275directiveInject"](g.a),n["\u0275\u0275directiveInject"](f.a),n["\u0275\u0275directiveInject"](v.a))},t.\u0275cmp=n["\u0275\u0275defineComponent"]({type:t,selectors:[["app-mail-form"]],hostBindings:function(t,e){1&t&&n["\u0275\u0275listener"]("beforeunload",(function(){return e.ngOnDestroy()}),!1,n["\u0275\u0275resolveWindow"])},outputs:{close:"close"},features:[n["\u0275\u0275NgOnChangesFeature"]],decls:43,vars:6,consts:[[3,"formGroup"],[1,"container","createMailStyles"],[1,"row","border-bottom","solid"],[1,"col-11","pt-2","createAccount"],[1,"col-1","d-flex"],["mat-icon-button","",1,"ml-auto","close-button",3,"click"],[1,"close-Icon"],[1,"row",2,"margin-top","10px"],[1,"col-12"],[1,"row"],[1,"col-9","pl-0"],["appearance","outline",1,"create-account-field"],["matInput","","placeholder","title *","formControlName","title"],["matInput","","formControlName","dueDate",3,"matDatepicker"],["matSuffix","",3,"for"],["picker0",""],["appearance","outline",2,"width","100%","font-size","14px !important"],["matInput","","cdkTextareaAutosize","","cdkAutosizeMinRows","6","cdkAutosizeMaxRows","","placeholder","Notes","formControlName","notes"],["autosize","cdkTextareaAutosize"],[1,"row","pl-0","pt-4"],[1,"col-3","pl-0"],["mat-icon-button","","matTooltip","Attach File","type","submit",1,"iconbtn","ml-2","mt-0"],[1,"row","pt-4"],[1,"col-9","pr-5","quotes"],["mat-icon-button","","class","iconbtn ml-2 mt-0","matTooltip","Create Mail","type","submit",3,"click",4,"ngIf"],["mat-icon-button","","class","iconbtn ml-2 mt-0","matTooltip","Edit Mail","type","submit",3,"click",4,"ngIf"],["mat-icon-button","","matTooltip","Create Mail","type","submit",1,"iconbtn","ml-2","mt-0",3,"click"],["mat-icon-button","","matTooltip","Edit Mail","type","submit",1,"iconbtn","ml-2","mt-0",3,"click"]],template:function(t,e){if(1&t&&(n["\u0275\u0275elementStart"](0,"form",0),n["\u0275\u0275elementStart"](1,"div",1),n["\u0275\u0275elementStart"](2,"div",2),n["\u0275\u0275elementStart"](3,"div",3),n["\u0275\u0275text"](4),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](5,"div",4),n["\u0275\u0275elementStart"](6,"button",5),n["\u0275\u0275listener"]("click",(function(){return e.closeClicked()})),n["\u0275\u0275elementStart"](7,"mat-icon",6),n["\u0275\u0275text"](8,"close"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](9,"div",7),n["\u0275\u0275elementStart"](10,"div",8),n["\u0275\u0275elementStart"](11,"div",9),n["\u0275\u0275elementStart"](12,"div",10),n["\u0275\u0275elementStart"](13,"mat-form-field",11),n["\u0275\u0275elementStart"](14,"mat-label"),n["\u0275\u0275text"](15,"Title"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275element"](16,"input",12),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](17,"div",9),n["\u0275\u0275elementStart"](18,"div",10),n["\u0275\u0275elementStart"](19,"mat-form-field",11),n["\u0275\u0275elementStart"](20,"mat-label"),n["\u0275\u0275text"](21,"Due Date"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275element"](22,"input",13),n["\u0275\u0275element"](23,"mat-datepicker-toggle",14),n["\u0275\u0275element"](24,"mat-datepicker",null,15),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](26,"div",9),n["\u0275\u0275elementStart"](27,"div",10),n["\u0275\u0275elementStart"](28,"mat-form-field",16),n["\u0275\u0275element"](29,"textarea",17,18),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](31,"div",19),n["\u0275\u0275elementStart"](32,"div",20),n["\u0275\u0275elementStart"](33,"button",21),n["\u0275\u0275elementStart"](34,"mat-icon"),n["\u0275\u0275text"](35," attach_file"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275element"](36,"div",22),n["\u0275\u0275elementStart"](37,"div",22),n["\u0275\u0275elementStart"](38,"div",23),n["\u0275\u0275text"](39,' "What we dwell is, what we become" '),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](40,"div",20),n["\u0275\u0275template"](41,y,3,0,"button",24),n["\u0275\u0275template"](42,M,3,0,"button",25),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&t){const t=n["\u0275\u0275reference"](25);n["\u0275\u0275property"]("formGroup",e.mailForm),n["\u0275\u0275advance"](4),n["\u0275\u0275textInterpolate1"]("",e.mode," Mail "),n["\u0275\u0275advance"](18),n["\u0275\u0275property"]("matDatepicker",t),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("for",t),n["\u0275\u0275advance"](18),n["\u0275\u0275property"]("ngIf","Edit"!=e.mode),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf","Edit"==e.mode)}},directives:[a.J,a.w,a.n,m.a,d.a,s.c,s.g,l.b,a.e,a.v,a.l,p.g,p.i,s.i,p.f,b.b,r.NgIf],styles:[".createMailStyles[_ngcontent-%COMP%]{background-image:url(createMail.f39681f78ab7afb320f8.png);background-size:240px 200px;background-repeat:no-repeat;overflow-y:hidden!important;background-position:100% 40%;min-height:95vh}.createMailStyles[_ngcontent-%COMP%]   .side-title[_ngcontent-%COMP%]{color:#cf0001;font-size:14px}.createMailStyles[_ngcontent-%COMP%]   .field-invalid[_ngcontent-%COMP%]{font-size:small}.createMailStyles[_ngcontent-%COMP%]   .createAccount[_ngcontent-%COMP%]{font-size:14px;color:#cf0001;font-weight:500}.createMailStyles[_ngcontent-%COMP%]   .create-account-field[_ngcontent-%COMP%]{font-size:14px!important;width:100%!important;color:#66615b}.createMailStyles[_ngcontent-%COMP%]   .inputSearch[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-flex{padding:0 .75em;margin-top:-.25em;position:relative;height:41px;width:204px}.createMailStyles[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.createMailStyles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}.createMailStyles[_ngcontent-%COMP%]   .iconbtn[_ngcontent-%COMP%]{background-color:#c92020;color:#fff;padding:0;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.createMailStyles[_ngcontent-%COMP%]   .check-box-text[_ngcontent-%COMP%]{color:#66615b;font-size:14px}.createMailStyles[_ngcontent-%COMP%]   .vip-contact[_ngcontent-%COMP%]{color:#1a1a1a;font-size:14px}.createMailStyles[_ngcontent-%COMP%]   .quotes[_ngcontent-%COMP%]{font-style:italic;font-weight:500;color:#66615b;font-size:14px}.createMailStyles[_ngcontent-%COMP%]   .textArea[_ngcontent-%COMP%]{color:#66615b}"]}),t})()},VsNQ:function(t,e,i){"use strict";i.d(e,"a",(function(){return l}));var n=i("mrSG"),a=i("xG9w"),o=i("XNiG"),r=i("fXoL"),c=i("tk/3"),s=i("flaP");let l=(()=>{class t{constructor(t,e){this.http=t,this._roleService=e,this.contactReload=new o.b,this.isHierarchyVisible=(t,e)=>a.where(this._roleService.roles,{application_id:t,object_id:e}).length>0,this.getContactsByAccounts=t=>this.http.post("api/accounts/getContactsByAccounts",{account_id:t}),this.saveContact=t=>this.http.post("/api/contacts/createContact",{contactDetails:t}),this.deactivateAccess=()=>{let t=a.where(this._roleService.roles,{application_id:34,object_id:29370});return console.log("accessList",t),t.length>0},this.getContactGovernanceType=t=>this.http.post("/api/activity/getGovernanceActivityType",{applicationId:t}),this.getActivityFilterMasterDate=()=>this.http.post("/api/activity/activityFilterMasterData",{}),this.updateTaskDuedate=(t,e)=>this.http.post("/api/contacts/updateTaskDueDate",{activity_id:t,date:e}),this.getActivityNotes=t=>(console.log(t),this.http.post("/api/contacts/activityNotes",{activity_id:t,operation_id:2})),this.createActivityNote=(t,e,i,n)=>this.http.post("/api/contacts/activityNotes",{operation_id:1,activity_id:t,message:n,color_code:e,title:i}),this.editActivityNote=(t,e,i,n)=>this.http.post("/api/contacts/activityNotes",{activity_id:t,operation_id:3,note_id:e,message:i,title:n}),this.deleteActivityNote=(t,e)=>this.http.post("/api/contacts/activityNotes",{activity_id:t,operation_id:4,note_id:e}),this.getUserProfileFromContacts=t=>{try{return new Promise((e,i)=>{this.http.post("/api/contacts/getContactInfo",{oid:t}).subscribe(t=>e(t),t=>(console.log(t),i(t)))})}catch(e){return Promise.reject()}},this.updateTaskDetailInline=(t,e)=>this.http.post("/api/contacts/editTaskInline",{taskFormDetails:e,activityId:t}),this.saveImage=(t,e)=>this.http.post("/api/contacts/saveImage",{result:t,contactId:e}),this.getContactsList=(t,e)=>this.http.post("/api/contacts/getContactsList",{org_codes:e,filterConfig:t}),this.getTotalContacts=(t,e)=>new Promise((i,n)=>{this.http.post("/api/contacts/getTotalContacts",{org_codes:e,filterConfig:t}).subscribe(t=>i(t),t=>n(t))}),this.getContactPersona=()=>new Promise((t,e)=>{this.http.post("/api/contacts/getReportFilterPersonainContact",{}).subscribe(e=>t(e),t=>e(t))}),this.getAccessTokenInfo=()=>this.http.post("/api/collector/getTokenConfigDetails",{}),this.getMailConfigFlag=()=>this.http.post("/api/contacts/getMailConfigFlag",{}),this.getLabelForOpportunity=(t,e)=>{let i=a.where(this._roleService.label,{application_id:t,id:e});return i.length>0?i[0].label_name:""},this.getUDRFContactConfig=()=>new Promise((t,e)=>{this.http.post("/api/contacts/getUDRFContactConfig",{}).subscribe(e=>{t(e)},t=>{e(t)})}),this.getContactHierarchyData=(t,e)=>this.http.post("/api/contacts/getContactHierarchyData",{application_reference_id:t,application_id:e}),this.downloadContactsList=(t,e)=>this.http.post("/api/contacts/downloadContactsList",{org_codes:e,filterConfig:t}),this.checkIfAccountExist=t=>this.http.post("/api/accounts/checkIfAccountExist",{accountId:t})}getAllAccounts(t){return new Promise((e,i)=>{this.http.post("/api/contacts/getAllAccounts",{orgCodes:t}).subscribe(t=>e(t),t=>i(t))})}getContactsByAccountIds(t){return new Promise((e,i)=>{this.http.post("/api/contacts/contactsByAccountIds",{orgCodes:t}).subscribe(t=>e(t),t=>i(t))})}getFilterMaster(){return this.http.get("/api/contacts/masterDataForContactFilter")}getFilterData(t,e){return this.http.post("/api/contacts/ContactsFilter",{filterData:t,orgCodes:e})}getUserProfileFromDB(t){return Object(n.c)(this,void 0,void 0,(function*(){try{return new Promise((e,i)=>{this.http.post("/api/project/getUserProfileFromDB",{oid:t}).subscribe(t=>e(t),t=>(console.log(t),i(t)))})}catch(e){return Promise.reject()}}))}removeMember(t){return Object(n.c)(this,void 0,void 0,(function*(){return new Promise((e,i)=>{this.http.post("/api/project/removeMember",{stakeholder_table_id:t}).subscribe(t=>e(t))})}))}setContactVipStatus(t,e){return this.http.post("/api/contacts/AddOrRemoveContactFav",{flag:t,contact_id:e})}getContacts(t){return this.http.post("/api/contacts/allActiveContacts",{orgCodes:t})}updateContactById(t,e){return this.http.post("/api/contacts/updateContact",{contact_id:t,contact_details:e})}performAddNotesOperation(t,e,i,n){return this.http.post("/api/contacts/noteOperationsContacts",{contact_id:t,operation_id:1,message:e,title:i,color_code:n})}performGetNotes(t){return this.http.post("/api/contacts/noteOperationsContacts",{contact_id:t,operation_id:2})}performEditNotes(t,e,i,n,a){return this.http.post("/api/contacts/noteOperationsContacts",{operation_id:3,note_id:t,message:e,title:i,color_code:n,contact_id:a})}performDeleteNotes(t,e){return this.http.post("/api/contacts/noteOperationsContacts",{operation_id:4,contact_id:t,note_id:e})}getContactsDetailsById(t){return this.http.post("/api/contacts/getContactDetailsById",{contact_id:t})}getContactsOverview(t){return this.http.post("api/contacts/getContactDetailsById",{contact_id:t})}personaMaster(){return new Promise((t,e)=>{this.http.post("/api/salesMaster/contactPersona",{}).subscribe(e=>t(e),t=>e(t))})}getContactAttachments(t){return this.http.post("/api/contacts/getContactAttachments",{contact_id:t})}getCRMAttachmentsConfigs(){return new Promise((t,e)=>{this.http.get("/api/salesMaster/getCRMAttachmentsConfigs").subscribe(e=>t(e),t=>e(t))})}updateContactAttachment(t,e){return this.http.post("/api/contacts/updateContactAttachment",{contact_id:t,file:e})}deleteContactAttachment(t,e){return this.http.post("/api/contacts/deleteContactAttachment",{contact_id:t,file:e})}searchContact(t,e){return this.http.post("/api/contacts/ContactsSearch",{search_parameter:t,orgCodes:e})}deleteContact(t){return this.http.post("/api/contacts/deactivateContact",{contact_id:t})}responseType(){return new Promise((t,e)=>{this.http.post("/api/salesMaster/getResponseMaster",{}).subscribe(e=>t(e),t=>e(t))})}contactType(){return new Promise((t,e)=>{this.http.post("/api/salesMaster/getContactType",{}).subscribe(e=>t(e),t=>e(t))})}editCallLog(t,e){return new Promise((i,n)=>{this.http.post("/api/contacts/editCallLog",{activity_id:t,callLogFormDetails:e}).subscribe(t=>i(t),t=>n(t))})}createCallLog(t){return this.http.post("/api/contacts/createCallLog",{callLogFormDetails:t})}editMail(t,e){return new Promise((i,n)=>{this.http.post("/api/contacts/editMail",{activity_id:t,mailFormDetails:e}).subscribe(t=>i(t),t=>n(t))})}createMail(t){return this.http.post("/api/contacts/createMail",{mailFormDetails:t})}meetingTypeList(){return new Promise((t,e)=>{this.http.post("/api/salesMaster/getSalesActivityLocationType",{}).subscribe(e=>t(e),t=>e(t))})}locationList(){return new Promise((t,e)=>{this.http.post("/api/salesMaster/getActivityLocations",{}).subscribe(e=>t(e),t=>e(t))})}peopleList(){return new Promise((t,e)=>{this.http.post("/api/salesMaster/peopleList",{}).subscribe(e=>t(e),t=>e(t))})}editMeeting(t,e){return new Promise((i,n)=>{this.http.post("/api/contacts/editMeeting",{activity_id:t,meetingFormDetails:e}).subscribe(t=>i(t),t=>n(t))})}createMeeting(t){return this.http.post("/api/contacts/createMeeting",{meetingFormDetails:t})}editTask(t,e){return new Promise((i,n)=>{this.http.post("/api/contacts/editTask",{activity_id:t,taskFormDetails:e}).subscribe(t=>i(t),t=>n(t))})}createTask(t){return this.http.post("/api/contacts/createTask",{taskFormDetails:t})}activityList(t,e){return new Promise((i,n)=>{this.http.post("/api/contacts/getActivityList",{application_id:t,contact_id:e}).subscribe(t=>i(t),t=>n(t))})}searchContactActivity(t,e){return new Promise((i,n)=>{this.http.post("/api/contacts/searchContactActivity",{search_parameter:t,contact_id:e}).subscribe(t=>i(t),t=>n(t))})}getFullDetailOfActivity(t){return this.http.post("/api/contacts/getActivityDetail",{activity_id:t})}openActivity(t){return new Promise((e,i)=>{this.http.post("/api/contacts/openActivity",{activity_id:t}).subscribe(t=>e(t),t=>i(t))})}activityDetails(t){return new Promise((e,i)=>{this.http.post("/api/contacts/getActivityDetails",{activity_id:t}).subscribe(t=>(console.log("activitydetail",t),e(t)),t=>i(t))})}completeActivity(t,e){return new Promise((i,n)=>{this.http.post("/api/contacts/completeActivity",{activity_id:t,is_completed:e}).subscribe(t=>i(t),t=>n(t))})}startActivity(t){return new Promise((e,i)=>{this.http.post("/api/contacts/startActivity",{activity_id:t}).subscribe(t=>e(t),t=>i(t))})}taskFormDetails(t){return new Promise((e,i)=>{this.http.post("/api/activity/taskFormDetails",{activity_id:t}).subscribe(t=>e(t),t=>i(t))})}meetingFormDetails(t){return new Promise((e,i)=>{this.http.post("/api/activity/meetingFormDetails",{activity_id:t}).subscribe(t=>e(t),t=>i(t))})}callLogFormDetails(t){return new Promise((e,i)=>{this.http.post("/api/activity/callLogFormDetails",{activity_id:t}).subscribe(t=>e(t),t=>i(t))})}mailFormDetails(t){return new Promise((e,i)=>{this.http.post("/api/activity/mailFormDetails",{activity_id:t}).subscribe(t=>e(t),t=>i(t))})}deleteActivity(t){return new Promise((e,i)=>{this.http.post("/api/contacts/deleteActivity",{activity_id:t}).subscribe(t=>e(t),t=>i(t))})}updateActivityAssignedTo(t,e){return this.http.post("/api/contacts/editActivityAssignedTo",{activityId:t,oid:e})}updatePlannedHours(t,e){return this.http.post("api/contacts/updatePlannedHrsForContactAct",{activity_id:t,planned_hours:e})}updateJobTitle(t,e){return this.http.post("/api/contacts/updateJobTitle",{id:t,jobTitle:e})}updateEmail(t,e){return this.http.post("/api/contacts/updateEmail",{id:t,email:e})}updateMobileNumber(t,e){return this.http.post("/api/contacts/updateMobileNumber",{id:t,mobileNumber:e})}updatePersona(t,e){return this.http.post("/api/contacts/updatePersona",{id:t,personaId:e})}updateContactOwner(t,e,i){return this.http.post("/api/contacts/updateContactOwner",{id:t,name:e,ownerOid:i})}updateLinkedinProfile(t,e){return this.http.post("/api/contacts/updateLinkedinProfile",{id:t,linkedinProfile:e})}updateContactAddress(t,e){return this.http.post("/api/contacts/updateContactAddress",{id:t,address:e})}marketSegment(){return new Promise((t,e)=>{this.http.post("/api/contacts/getMarketSegment",{}).subscribe(e=>t(e),t=>e(t))})}updateMarketSegment(t,e){return this.http.post("/api/contacts/updateMarketSegment",{id:t,market_segment:e})}setContactDunningEmail(t,e){return this.http.post("/api/contacts/setContactAsDunning",{flag:t,contact_id:e})}getFormFieldCollection(){return new Promise((t,e)=>{this.http.post("/api/contacts/getFormFieldCollection",{}).subscribe(e=>{t(e)},t=>{e(t)})})}}return t.\u0275fac=function(e){return new(e||t)(r["\u0275\u0275inject"](c.c),r["\u0275\u0275inject"](s.a))},t.\u0275prov=r["\u0275\u0275defineInjectable"]({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()},g7z7:function(t,e,i){"use strict";i.r(e),i.d(e,"MailFormComponent",(function(){return D}));var n=i("mrSG"),a=i("fXoL"),o=i("3Pt+"),r=i("xG9w"),c=i("wd/R"),s=i("ofXK"),l=i("jtHE"),d=i("XNiG"),m=i("NJ67"),p=i("1G5W"),h=i("kmnG"),u=i("d3UM"),g=i("FKr1"),f=i("WJ5W");const v=["singleSelect"];function b(t,e){if(1&t){const t=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"mat-option",6),a["\u0275\u0275listener"]("click",(function(){a["\u0275\u0275restoreView"](t);const i=e.$implicit;return a["\u0275\u0275nextContext"]().emitChanges(i)})),a["\u0275\u0275text"](1),a["\u0275\u0275elementEnd"]()}if(2&t){const t=e.$implicit;a["\u0275\u0275property"]("value",t.id),a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate1"](" ",t.name," ")}}let y=(()=>{class t extends m.a{constructor(t){super(),this.renderer=t,this.fieldCtrl=new o.j,this.fieldFilterCtrl=new o.j,this.list=[],this.required=!1,this.valueChange=new a.EventEmitter,this.disabled=!1,this.filteredList=new l.a,this.change=new a.EventEmitter,this._onDestroy=new d.b,this.emitChanges=t=>{this.change.emit(t)}}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(p.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(p.a)(this._onDestroy)).subscribe(t=>{this.value=t,this.onChange(t),this.valueChange.emit(t)})}ngOnChanges(){null!=this.list&&this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let t=this.fieldFilterCtrl.value;t?(t=t.toLowerCase(),this.filteredList.next(this.list.filter(e=>e.name.toLowerCase().indexOf(t)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(t){t?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(t){this.fieldCtrl.setValue(t)}}return t.\u0275fac=function(e){return new(e||t)(a["\u0275\u0275directiveInject"](a.Renderer2))},t.\u0275cmp=a["\u0275\u0275defineComponent"]({type:t,selectors:[["app-input-search"]],viewQuery:function(t,e){if(1&t&&a["\u0275\u0275viewQuery"](v,!0),2&t){let t;a["\u0275\u0275queryRefresh"](t=a["\u0275\u0275loadQuery"]())&&(e.singleSelect=t.first)}},inputs:{list:"list",placeholder:"placeholder",required:"required",disabled:"disabled"},outputs:{valueChange:"valueChange",change:"change"},features:[a["\u0275\u0275ProvidersFeature"]([{provide:o.t,useExisting:Object(a.forwardRef)(()=>t),multi:!0}]),a["\u0275\u0275InheritDefinitionFeature"],a["\u0275\u0275NgOnChangesFeature"]],decls:11,vars:11,consts:[["appearance","outline"],[3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value"],[3,"value","click",4,"ngFor","ngForOf"],[3,"value","click"]],template:function(t,e){1&t&&(a["\u0275\u0275elementStart"](0,"mat-form-field",0),a["\u0275\u0275elementStart"](1,"mat-label"),a["\u0275\u0275text"](2),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](3,"mat-select",1,2),a["\u0275\u0275elementStart"](5,"mat-option"),a["\u0275\u0275element"](6,"ngx-mat-select-search",3),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](7,"mat-option",4),a["\u0275\u0275text"](8,"None"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](9,b,2,2,"mat-option",5),a["\u0275\u0275pipe"](10,"async"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&t&&(a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate"](e.placeholder),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("formControl",e.fieldCtrl)("placeholder",e.placeholder)("required",e.required)("disabled",e.disabled),a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("formControl",e.fieldFilterCtrl)("placeholderLabel",e.placeholder),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("value",null),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("ngForOf",a["\u0275\u0275pipeBind1"](10,9,e.filteredList)))},directives:[h.c,h.g,u.c,o.v,o.k,o.F,g.p,f.a,s.NgForOf],pipes:[s.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}"]}),t})();var M=i("dNgK"),C=i("qFsG"),S=i("NFeN"),_=(i("/1cH"),i("bTqV")),w=i("iadO"),x=i("0IaG"),E=i("8uEH"),k=i("pgif"),P=i("tyNb"),I=i("ihCf");function O(t,e){if(1&t){const t=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"button",27),a["\u0275\u0275listener"]("click",(function(){return a["\u0275\u0275restoreView"](t),a["\u0275\u0275nextContext"]().createMail()})),a["\u0275\u0275elementStart"](1,"mat-icon"),a["\u0275\u0275text"](2," done_all"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()}}function F(t,e){if(1&t){const t=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"button",28),a["\u0275\u0275listener"]("click",(function(){return a["\u0275\u0275restoreView"](t),a["\u0275\u0275nextContext"]().editMail()})),a["\u0275\u0275elementStart"](1,"mat-icon"),a["\u0275\u0275text"](2," done_all"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()}}let D=(()=>{class t{constructor(t,e,i,n,r,c,s,l){this.dialogRef=t,this.dialogData=e,this.dialog=i,this.fb=n,this.activityToMainService=r,this.snackBar=c,this.opportunityService=s,this.route=l,this.close=new a.EventEmitter,this.currentUser=this.opportunityService.currentUser.oid,this.mailForm=this.fb.group({phase:[""],title:["",o.H.required],notes:[""],attachment:[""],remindMe:[""],dueDate:[""],applicationName:[""],applicationReferenceId:[""],applicationId:[""],governanceType:[""]}),this.closeClicked=()=>{this.close.emit(),this.dialogRef.close()},this.editMail=()=>{this.mailForm.valid?this.opportunityService.editMail(this.activityId,this.mailForm.value).then(t=>{console.log(t),this.mailForm.reset(),this.snackBar.open("Mail updated Successfully!","Dismiss",{duration:2e3}),this.close.emit("close"),this.dialogRef.close("Update Required"),this.activityToMainService.sendMsg("reload")},t=>{this.snackBar.open("Failed to Edit mail.","Dismiss",{duration:2e3}),console.error(t)}):this.snackBar.open("Enter all Mandatory Fields!","Dismiss",{duration:2e3})},this.getPhase=()=>(console.log("I've been called"),new Promise((t,e)=>{this.opportunityService.getActivityPhase(this.opportunityId).then(e=>{t(e)},t=>{throw console.error(t),t})})),this.createMail=()=>{this.mailForm.valid?this.opportunityService.createMail(this.mailForm.value).subscribe(t=>{this.mailForm.reset(),this.snackBar.open("Mail Created Successfully!","Dismiss",{duration:2e3}),this.close.emit("close"),this.dialogRef.close("Update Required"),this.activityToMainService.sendMsg("reload"),console.log(t)},t=>{this.snackBar.open("Failed to create mail.","Dismiss",{duration:2e3}),console.error(t)}):this.snackBar.open("Enter all Mandatory Fields!","Dismiss",{duration:2e3})}}ngOnChanges(){}updateFormWithMailDetails(){if(console.log(this.mode,this.activityId),"Edit"==this.mode){let t=this.data?this.data:null;t?this.mailForm.patchValue({phase:parseInt(t.phase),title:t.title,notes:t.notes?t.notes[0].notes:[],remindMe:t.reminder,dueDate:t.task_due_date,governanceType:t.governance_activity_id}):console.error("data not received!")}else this.mailForm.reset()}ngOnInit(){return Object(n.c)(this,void 0,void 0,(function*(){let t;this.mode=this.dialogData.mode,this.activityId=this.dialogData.activityId,this.data=this.dialogData.data,this.opportunityId=this.dialogData.opportunityId,"Edit"==this.mode&&this.updateFormWithMailDetails(),"opportunities"==window.location.pathname.split("/")[2]&&(t=36),yield this.opportunityService.getLeadsGovernanceType(72).then(t=>{this.leadGovernanceTypes=t},t=>{console.log(t)}),yield this.opportunityService.getActivityPhase(this.opportunityId).then(t=>{this.phase=t},t=>{console.log(t)}),this.patchValue(t)}))}patchValue(t){let e=r.where(this.leadGovernanceTypes,{name:"Mails"}),i=r.where(this.phase,{name:"Logs"});this.mailForm.patchValue({applicationName:"opportunities",applicationReferenceId:this.opportunityId,applicationId:t,dueDate:c().format(),governanceType:e.length>0?e[0].id:null,phase:i.length>0?i[0].id:null})}ngOnDestroy(){}}return t.\u0275fac=function(e){return new(e||t)(a["\u0275\u0275directiveInject"](x.h),a["\u0275\u0275directiveInject"](x.a),a["\u0275\u0275directiveInject"](x.b),a["\u0275\u0275directiveInject"](o.i),a["\u0275\u0275directiveInject"](E.a),a["\u0275\u0275directiveInject"](M.a),a["\u0275\u0275directiveInject"](k.a),a["\u0275\u0275directiveInject"](P.a))},t.\u0275cmp=a["\u0275\u0275defineComponent"]({type:t,selectors:[["app-mail-form"]],hostBindings:function(t,e){1&t&&a["\u0275\u0275listener"]("beforeunload",(function(){return e.ngOnDestroy()}),!1,a["\u0275\u0275resolveWindow"])},outputs:{close:"close"},features:[a["\u0275\u0275NgOnChangesFeature"]],decls:46,vars:7,consts:[[3,"formGroup"],[1,"container","createMailStyles"],[1,"row","border-bottom","solid"],[1,"col-11","pt-2","createAccount"],[1,"col-1","d-flex"],["mat-icon-button","",1,"ml-auto","close-button",3,"click"],[1,"close-Icon"],[1,"row",2,"margin-top","10px"],[1,"col-12"],[1,"row"],[1,"col-9","pl-0"],["placeholder","Phase","formControlName","phase",1,"create-account-field",3,"list"],["appearance","outline",1,"create-account-field"],["matInput","","placeholder","title *","formControlName","title"],["matInput","","formControlName","dueDate",3,"matDatepicker"],["matSuffix","",3,"for"],["picker0",""],["appearance","outline",2,"width","100%","font-size","14px !important"],["matInput","","cdkTextareaAutosize","","cdkAutosizeMinRows","6","cdkAutosizeMaxRows","","placeholder","Notes","formControlName","notes"],["autosize","cdkTextareaAutosize"],[1,"row","pl-0","pt-4"],[1,"col-3","pl-0"],["mat-icon-button","","matTooltip","Attach File","type","submit",1,"iconbtn","ml-2","mt-0"],[1,"row","pt-4"],[1,"col-9","pr-5","quotes"],["mat-icon-button","","class","iconbtn ml-2 mt-0","matTooltip","Create Mail","type","submit",3,"click",4,"ngIf"],["mat-icon-button","","class","iconbtn ml-2 mt-0","matTooltip","Edit Mail","type","submit",3,"click",4,"ngIf"],["mat-icon-button","","matTooltip","Create Mail","type","submit",1,"iconbtn","ml-2","mt-0",3,"click"],["mat-icon-button","","matTooltip","Edit Mail","type","submit",1,"iconbtn","ml-2","mt-0",3,"click"]],template:function(t,e){if(1&t&&(a["\u0275\u0275elementStart"](0,"form",0),a["\u0275\u0275elementStart"](1,"div",1),a["\u0275\u0275elementStart"](2,"div",2),a["\u0275\u0275elementStart"](3,"div",3),a["\u0275\u0275text"](4),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](5,"div",4),a["\u0275\u0275elementStart"](6,"button",5),a["\u0275\u0275listener"]("click",(function(){return e.closeClicked()})),a["\u0275\u0275elementStart"](7,"mat-icon",6),a["\u0275\u0275text"](8,"close"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](9,"div",7),a["\u0275\u0275elementStart"](10,"div",8),a["\u0275\u0275elementStart"](11,"div",9),a["\u0275\u0275elementStart"](12,"div",10),a["\u0275\u0275element"](13,"app-input-search",11),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](14,"div",9),a["\u0275\u0275elementStart"](15,"div",10),a["\u0275\u0275elementStart"](16,"mat-form-field",12),a["\u0275\u0275elementStart"](17,"mat-label"),a["\u0275\u0275text"](18,"Title"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275element"](19,"input",13),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](20,"div",9),a["\u0275\u0275elementStart"](21,"div",10),a["\u0275\u0275elementStart"](22,"mat-form-field",12),a["\u0275\u0275elementStart"](23,"mat-label"),a["\u0275\u0275text"](24,"Due Date"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275element"](25,"input",14),a["\u0275\u0275element"](26,"mat-datepicker-toggle",15),a["\u0275\u0275element"](27,"mat-datepicker",null,16),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](29,"div",9),a["\u0275\u0275elementStart"](30,"div",10),a["\u0275\u0275elementStart"](31,"mat-form-field",17),a["\u0275\u0275element"](32,"textarea",18,19),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](34,"div",20),a["\u0275\u0275elementStart"](35,"div",21),a["\u0275\u0275elementStart"](36,"button",22),a["\u0275\u0275elementStart"](37,"mat-icon"),a["\u0275\u0275text"](38," attach_file"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275element"](39,"div",23),a["\u0275\u0275elementStart"](40,"div",23),a["\u0275\u0275elementStart"](41,"div",24),a["\u0275\u0275text"](42,' "What we dwell is, what we become" '),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](43,"div",21),a["\u0275\u0275template"](44,O,3,0,"button",25),a["\u0275\u0275template"](45,F,3,0,"button",26),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&t){const t=a["\u0275\u0275reference"](28);a["\u0275\u0275property"]("formGroup",e.mailForm),a["\u0275\u0275advance"](4),a["\u0275\u0275textInterpolate1"]("",e.mode," Mail "),a["\u0275\u0275advance"](9),a["\u0275\u0275property"]("list",e.phase),a["\u0275\u0275advance"](12),a["\u0275\u0275property"]("matDatepicker",t),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("for",t),a["\u0275\u0275advance"](18),a["\u0275\u0275property"]("ngIf","Edit"!=e.mode),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf","Edit"==e.mode)}},directives:function(){return[o.J,o.w,o.n,_.a,S.a,y,o.v,o.l,h.c,h.g,C.b,o.e,w.g,w.i,h.i,w.f,I.b,s.NgIf]},styles:[".createMailStyles[_ngcontent-%COMP%]{background-image:url(createMail.f39681f78ab7afb320f8.png);background-size:240px 200px;background-repeat:no-repeat;overflow-y:hidden!important;background-position:100% 40%;min-height:95vh}.createMailStyles[_ngcontent-%COMP%]   .side-title[_ngcontent-%COMP%]{color:#cf0001;font-size:14px}.createMailStyles[_ngcontent-%COMP%]   .field-invalid[_ngcontent-%COMP%]{font-size:small}.createMailStyles[_ngcontent-%COMP%]   .createAccount[_ngcontent-%COMP%]{font-size:14px;color:#cf0001;font-weight:500}.createMailStyles[_ngcontent-%COMP%]   .create-account-field[_ngcontent-%COMP%]{font-size:14px!important;width:100%!important;color:#66615b}.createMailStyles[_ngcontent-%COMP%]   .inputSearch[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-flex{padding:0 .75em;margin-top:-.25em;position:relative;height:41px;width:204px}.createMailStyles[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.createMailStyles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}.createMailStyles[_ngcontent-%COMP%]   .iconbtn[_ngcontent-%COMP%]{background-color:#c92020;color:#fff;padding:0;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.createMailStyles[_ngcontent-%COMP%]   .check-box-text[_ngcontent-%COMP%]{color:#66615b;font-size:14px}.createMailStyles[_ngcontent-%COMP%]   .vip-contact[_ngcontent-%COMP%]{color:#1a1a1a;font-size:14px}.createMailStyles[_ngcontent-%COMP%]   .quotes[_ngcontent-%COMP%]{font-style:italic;font-weight:500;color:#66615b;font-size:14px}.createMailStyles[_ngcontent-%COMP%]   .textArea[_ngcontent-%COMP%]{color:#66615b}"]}),t})()},phqL:function(t,e,i){"use strict";i.r(e),i.d(e,"MailFormComponent",(function(){return C}));var n=i("fXoL"),a=i("3Pt+"),o=i("wd/R"),r=i("ofXK"),c=i("dNgK"),s=i("kmnG"),l=(i("d3UM"),i("qFsG")),d=i("NFeN"),m=(i("/1cH"),i("bTqV")),p=i("iadO"),h=(i("WJ5W"),i("0IaG")),u=i("RUbJ"),g=i("WGBV"),f=i("tyNb"),v=i("XXEo"),b=i("ihCf");function y(t,e){if(1&t){const t=n["\u0275\u0275getCurrentView"]();n["\u0275\u0275elementStart"](0,"button",26),n["\u0275\u0275listener"]("click",(function(){return n["\u0275\u0275restoreView"](t),n["\u0275\u0275nextContext"]().createMail()})),n["\u0275\u0275elementStart"](1,"mat-icon"),n["\u0275\u0275text"](2," done_all"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()}}function M(t,e){if(1&t){const t=n["\u0275\u0275getCurrentView"]();n["\u0275\u0275elementStart"](0,"button",27),n["\u0275\u0275listener"]("click",(function(){return n["\u0275\u0275restoreView"](t),n["\u0275\u0275nextContext"]().editMail()})),n["\u0275\u0275elementStart"](1,"mat-icon"),n["\u0275\u0275text"](2," done_all"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()}}let C=(()=>{class t{constructor(t,e,i,o,r,c,s,l,d){this.dialogRef=t,this.dialogData=e,this.dialog=i,this.fb=o,this.activityToMainService=r,this.snackBar=c,this.accountService=s,this.route=l,this.loginService=d,this.close=new n.EventEmitter,this.currentUser=this.loginService.getProfile().profile,this.mailForm=this.fb.group({title:["",a.H.required],notes:[""],attachment:[""],remindMe:[""],dueDate:[""],applicationName:[""],accountId:[""],applicationId:[""],governanceType:[""]}),this.closeClicked=()=>{this.close.emit(),this.dialogRef.close()},this.editMail=()=>{this.mailForm.valid?this.accountService.editMail(this.activityId,this.mailForm.value).then(t=>{console.log(t),this.mailForm.reset(),this.snackBar.open("Mail updated Successfully!","Dismiss",{duration:2e3}),this.close.emit("close"),this.dialogRef.close("Update Required"),this.activityToMainService.sendMsg("reload")},t=>{this.snackBar.open("Failed to Edit mail.","Dismiss",{duration:2e3}),console.error(t)}):this.snackBar.open("Enter all Mandatory Fields!","Dismiss",{duration:2e3})},this.createMail=()=>{this.mailForm.valid?this.accountService.createMail(this.mailForm.value).subscribe(t=>{this.mailForm.reset(),this.snackBar.open("Mail Created Successfully!","Dismiss",{duration:2e3}),this.close.emit("close"),this.dialogRef.close("Update Required"),this.activityToMainService.sendMsg("reload"),console.log(t)},t=>{this.snackBar.open("Failed to create mail.","Dismiss",{duration:2e3}),console.error(t)}):this.snackBar.open("Enter all Mandatory Fields!","Dismiss",{duration:2e3})}}ngOnChanges(){}updateFormWithMailDetails(){if(console.log(this.mode,this.activityId),"Edit"==this.mode){let t=this.data?this.data:null;t?this.mailForm.patchValue({title:t.title,notes:t.notes?t.notes[0].notes:[],remindMe:t.reminder,dueDate:t.task_due_date,governanceType:t.governance_activity_id}):console.error("data not received!")}else this.mailForm.reset()}ngOnInit(){let t;this.mode=this.dialogData.mode,this.activityId=this.dialogData.activityId,this.data=this.dialogData.data,"Edit"==this.mode&&this.updateFormWithMailDetails(),"accounts"==window.location.pathname.split("/")[2]&&(t=33),this.accountService.getAccountGovernanceType(34).subscribe(t=>{this.accountGovernanceTypes=t},t=>{console.log(t)}),this.mailForm.patchValue({applicationName:window.location.pathname.split("/")[2],accountId:window.location.pathname.split("/")[3],applicationId:t,governanceType:47,dueDate:o().format()})}ngOnDestroy(){}}return t.\u0275fac=function(e){return new(e||t)(n["\u0275\u0275directiveInject"](h.h),n["\u0275\u0275directiveInject"](h.a),n["\u0275\u0275directiveInject"](h.b),n["\u0275\u0275directiveInject"](a.i),n["\u0275\u0275directiveInject"](u.a),n["\u0275\u0275directiveInject"](c.a),n["\u0275\u0275directiveInject"](g.a),n["\u0275\u0275directiveInject"](f.a),n["\u0275\u0275directiveInject"](v.a))},t.\u0275cmp=n["\u0275\u0275defineComponent"]({type:t,selectors:[["app-mail-form"]],hostBindings:function(t,e){1&t&&n["\u0275\u0275listener"]("beforeunload",(function(){return e.ngOnDestroy()}),!1,n["\u0275\u0275resolveWindow"])},outputs:{close:"close"},features:[n["\u0275\u0275NgOnChangesFeature"]],decls:43,vars:6,consts:[[3,"formGroup"],[1,"container","createMailStyles"],[1,"row","border-bottom","solid"],[1,"col-11","pt-2","createAccount"],[1,"col-1","d-flex"],["mat-icon-button","",1,"ml-auto","close-button",3,"click"],[1,"close-Icon"],[1,"row",2,"margin-top","10px"],[1,"col-12"],[1,"row"],[1,"col-9","pl-0"],["appearance","outline",1,"create-account-field"],["matInput","","placeholder","title *","formControlName","title"],["matInput","","formControlName","dueDate",3,"matDatepicker"],["matSuffix","",3,"for"],["picker0",""],["appearance","outline",2,"width","100%","font-size","14px !important"],["matInput","","cdkTextareaAutosize","","cdkAutosizeMinRows","6","cdkAutosizeMaxRows","","placeholder","Notes","formControlName","notes"],["autosize","cdkTextareaAutosize"],[1,"row","pl-0","pt-4"],[1,"col-3","pl-0"],["mat-icon-button","","matTooltip","Attach File","type","submit",1,"iconbtn","ml-2","mt-0"],[1,"row","pt-4"],[1,"col-9","pr-5","quotes"],["mat-icon-button","","class","iconbtn ml-2 mt-0","matTooltip","Create Mail","type","submit",3,"click",4,"ngIf"],["mat-icon-button","","class","iconbtn ml-2 mt-0","matTooltip","Edit Mail","type","submit",3,"click",4,"ngIf"],["mat-icon-button","","matTooltip","Create Mail","type","submit",1,"iconbtn","ml-2","mt-0",3,"click"],["mat-icon-button","","matTooltip","Edit Mail","type","submit",1,"iconbtn","ml-2","mt-0",3,"click"]],template:function(t,e){if(1&t&&(n["\u0275\u0275elementStart"](0,"form",0),n["\u0275\u0275elementStart"](1,"div",1),n["\u0275\u0275elementStart"](2,"div",2),n["\u0275\u0275elementStart"](3,"div",3),n["\u0275\u0275text"](4),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](5,"div",4),n["\u0275\u0275elementStart"](6,"button",5),n["\u0275\u0275listener"]("click",(function(){return e.closeClicked()})),n["\u0275\u0275elementStart"](7,"mat-icon",6),n["\u0275\u0275text"](8,"close"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](9,"div",7),n["\u0275\u0275elementStart"](10,"div",8),n["\u0275\u0275elementStart"](11,"div",9),n["\u0275\u0275elementStart"](12,"div",10),n["\u0275\u0275elementStart"](13,"mat-form-field",11),n["\u0275\u0275elementStart"](14,"mat-label"),n["\u0275\u0275text"](15,"Title"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275element"](16,"input",12),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](17,"div",9),n["\u0275\u0275elementStart"](18,"div",10),n["\u0275\u0275elementStart"](19,"mat-form-field",11),n["\u0275\u0275elementStart"](20,"mat-label"),n["\u0275\u0275text"](21,"Due Date"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275element"](22,"input",13),n["\u0275\u0275element"](23,"mat-datepicker-toggle",14),n["\u0275\u0275element"](24,"mat-datepicker",null,15),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](26,"div",9),n["\u0275\u0275elementStart"](27,"div",10),n["\u0275\u0275elementStart"](28,"mat-form-field",16),n["\u0275\u0275element"](29,"textarea",17,18),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](31,"div",19),n["\u0275\u0275elementStart"](32,"div",20),n["\u0275\u0275elementStart"](33,"button",21),n["\u0275\u0275elementStart"](34,"mat-icon"),n["\u0275\u0275text"](35," attach_file"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275element"](36,"div",22),n["\u0275\u0275elementStart"](37,"div",22),n["\u0275\u0275elementStart"](38,"div",23),n["\u0275\u0275text"](39,' "What we dwell is, what we become" '),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](40,"div",20),n["\u0275\u0275template"](41,y,3,0,"button",24),n["\u0275\u0275template"](42,M,3,0,"button",25),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&t){const t=n["\u0275\u0275reference"](25);n["\u0275\u0275property"]("formGroup",e.mailForm),n["\u0275\u0275advance"](4),n["\u0275\u0275textInterpolate1"]("",e.mode," Mail "),n["\u0275\u0275advance"](18),n["\u0275\u0275property"]("matDatepicker",t),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("for",t),n["\u0275\u0275advance"](18),n["\u0275\u0275property"]("ngIf","Edit"!=e.mode),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf","Edit"==e.mode)}},directives:[a.J,a.w,a.n,m.a,d.a,s.c,s.g,l.b,a.e,a.v,a.l,p.g,p.i,s.i,p.f,b.b,r.NgIf],styles:[".createMailStyles[_ngcontent-%COMP%]{background-image:url(createMail.f39681f78ab7afb320f8.png);background-size:240px 200px;background-repeat:no-repeat;overflow-y:hidden!important;background-position:100% 40%;min-height:95vh}.createMailStyles[_ngcontent-%COMP%]   .side-title[_ngcontent-%COMP%]{color:#cf0001;font-size:14px}.createMailStyles[_ngcontent-%COMP%]   .field-invalid[_ngcontent-%COMP%]{font-size:small}.createMailStyles[_ngcontent-%COMP%]   .createAccount[_ngcontent-%COMP%]{font-size:14px;color:#cf0001;font-weight:500}.createMailStyles[_ngcontent-%COMP%]   .create-account-field[_ngcontent-%COMP%]{font-size:14px!important;width:100%!important;color:#66615b}.createMailStyles[_ngcontent-%COMP%]   .inputSearch[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-flex{padding:0 .75em;margin-top:-.25em;position:relative;height:41px;width:204px}.createMailStyles[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.createMailStyles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}.createMailStyles[_ngcontent-%COMP%]   .iconbtn[_ngcontent-%COMP%]{background-color:#c92020;color:#fff;padding:0;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.createMailStyles[_ngcontent-%COMP%]   .check-box-text[_ngcontent-%COMP%]{color:#66615b;font-size:14px}.createMailStyles[_ngcontent-%COMP%]   .vip-contact[_ngcontent-%COMP%]{color:#1a1a1a;font-size:14px}.createMailStyles[_ngcontent-%COMP%]   .quotes[_ngcontent-%COMP%]{font-style:italic;font-weight:500;color:#66615b;font-size:14px}.createMailStyles[_ngcontent-%COMP%]   .textArea[_ngcontent-%COMP%]{color:#66615b}"]}),t})()}}]);