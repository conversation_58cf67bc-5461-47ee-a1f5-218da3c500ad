(window.webpackJsonp=window.webpackJsonp||[]).push([[865,634,765,783,821,822,858,861,977,981,983,987,990,991],{H44p:function(e,t,i){"use strict";i.d(t,"a",(function(){return f}));var r=i("xG9w"),n=i("fXoL"),a=i("flaP"),o=i("ofXK"),s=i("Qu3c"),c=i("NFeN");function l(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"div",9),n["\u0275\u0275elementStart"](1,"div",10),n["\u0275\u0275elementStart"](2,"div"),n["\u0275\u0275text"](3),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](4,"div"),n["\u0275\u0275elementStart"](5,"p",11),n["\u0275\u0275text"](6),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](7,"p",12),n["\u0275\u0275text"](8),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e){const e=n["\u0275\u0275nextContext"](2);n["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),n["\u0275\u0275advance"](3),n["\u0275\u0275textInterpolate"](null==e.currency[e.index]?null:e.currency[e.index].currency_code),n["\u0275\u0275advance"](3),n["\u0275\u0275textInterpolate"](e.label),n["\u0275\u0275advance"](2),n["\u0275\u0275textInterpolate1"](" ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function d(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"div",13),n["\u0275\u0275elementStart"](1,"span"),n["\u0275\u0275text"](2),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e){const e=n["\u0275\u0275nextContext"](2);n["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),n["\u0275\u0275advance"](2),n["\u0275\u0275textInterpolate1"](" ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function u(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"div",14),n["\u0275\u0275elementStart"](1,"span",15),n["\u0275\u0275text"](2),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e){const e=n["\u0275\u0275nextContext"](2);n["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),n["\u0275\u0275advance"](2),n["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function h(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"div",16),n["\u0275\u0275elementStart"](1,"span",15),n["\u0275\u0275text"](2),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e){const e=n["\u0275\u0275nextContext"](2);n["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),n["\u0275\u0275advance"](2),n["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function p(e,t){if(1&e&&(n["\u0275\u0275elementStart"](0,"div",17),n["\u0275\u0275elementStart"](1,"span",18),n["\u0275\u0275text"](2),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()),2&e){const e=n["\u0275\u0275nextContext"](2);n["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),n["\u0275\u0275advance"](2),n["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function m(e,t){1&e&&(n["\u0275\u0275elementStart"](0,"mat-icon",19),n["\u0275\u0275text"](1,"loop"),n["\u0275\u0275elementEnd"]())}function g(e,t){if(1&e){const e=n["\u0275\u0275getCurrentView"]();n["\u0275\u0275elementStart"](0,"div",1),n["\u0275\u0275listener"]("click",(function(){return n["\u0275\u0275restoreView"](e),n["\u0275\u0275nextContext"]().change()})),n["\u0275\u0275template"](1,l,9,4,"div",2),n["\u0275\u0275template"](2,d,3,2,"div",3),n["\u0275\u0275template"](3,u,3,3,"div",4),n["\u0275\u0275template"](4,h,3,3,"div",5),n["\u0275\u0275template"](5,p,3,3,"div",6),n["\u0275\u0275elementStart"](6,"div",7),n["\u0275\u0275template"](7,m,2,0,"mat-icon",8),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()}if(2&e){const e=n["\u0275\u0275nextContext"]();n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf","big"==e.type),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf","small"==e.type),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf","medium"==e.type),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf","large"==e.type),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf","overview"==e.type),n["\u0275\u0275advance"](2),n["\u0275\u0275property"]("ngIf",e.toDisplay)}}let f=(()=>{class e{constructor(e){this.roleService=e,this.showActualAmount=!1,this.currency=[]}set currencyList(e){if(this.currency=e,e){const t=r.findIndex(e,e=>e.currency_code==this.currency_code);-1!=t&&(this.index=t,this.currency[this.index].value=Math.trunc(this.currency[this.index].value))}}ngOnInit(){null==this.toDisplay&&(this.toDisplay=!0),this.tenant_info=this.roleService.currency_info,this.tenantName=this.tenant_info.tenant_name,this.currency_code=this.defaultCurrencyCode?this.defaultCurrencyCode:this.tenant_info.default_currency?this.tenant_info.default_currency:"INR",this.setCurrency(),this.isConvertValue=null==this.tenant_info.is_to_convert_currency_value||this.tenant_info.is_to_convert_currency_value}setCurrency(){if(this.currency){const e=r.findIndex(this.currency,e=>e.currency_code==this.currency_code);-1!=e&&(this.index=e,this.currency[this.index].value=Math.trunc(this.currency[this.index].value))}}change(){void 0!==event.stopPropagation?event.stopPropagation():void 0!==event.cancelBubble&&(event.cancelBubble=!0),this.currency.length>1&&(this.index=++this.index%this.currency.length),this.currency[this.index].value=Math.trunc(this.currency[this.index].value)}convert(e,t){return e?(this.showActualAmount?e=new Intl.NumberFormat("INR"==t?"en-IN":"en-US",{}).format(e)+" "+t:1==this.isConvertValue?e="INR"==t?(e/1e7).toFixed(2)+" Cr":(e/1e6).toFixed(2)+" M":0!=this.isConvertValue||r.contains(["big","small"],this.type)?0==this.isConvertValue&&r.contains(["big","small"],this.type)&&(e="INR"==t?"big"!=this.type?"INR "+new Intl.NumberFormat("en-IN",{}).format(e):new Intl.NumberFormat("en-IN",{}).format(e):"big"!=this.type?t+" "+new Intl.NumberFormat("en-US",{}).format(e):new Intl.NumberFormat("en-US",{}).format(e)):e="INR"==t?(e/1e7).toFixed(2)+" Cr":(e/1e6).toFixed(2)+" M",e):"-"}}return e.\u0275fac=function(t){return new(t||e)(n["\u0275\u0275directiveInject"](a.a))},e.\u0275cmp=n["\u0275\u0275defineComponent"]({type:e,selectors:[["app-currency"]],inputs:{type:"type",label:"label",showActualAmount:"showActualAmount",acl:"acl",toDisplay:"toDisplay",defaultCurrencyCode:"defaultCurrencyCode",currencyList:"currencyList"},decls:1,vars:1,consts:[["class","d-flex currency-wrapper",3,"click",4,"ngIf"],[1,"d-flex","currency-wrapper",3,"click"],["class","d-flex",3,"matTooltip",4,"ngIf"],["class","data-label",3,"matTooltip",4,"ngIf"],["class","data-medium d-flex",3,"matTooltip",4,"ngIf"],["class","data-large d-flex",3,"matTooltip",4,"ngIf"],["class","justify-content-center",3,"matTooltip",4,"ngIf"],[1,"d-flex","align-items-center"],["class","change-icon pl-2","matTooltip","Next currency",4,"ngIf"],[1,"d-flex",3,"matTooltip"],[1,"pr-3","my-auto","currency-code","justify-content-center"],[1,"m-0","header"],[1,"m-0","data-label","pt-1"],[1,"data-label",3,"matTooltip"],[1,"data-medium","d-flex",3,"matTooltip"],[1,"justify-content-center"],[1,"data-large","d-flex",3,"matTooltip"],[1,"justify-content-center",3,"matTooltip"],[1,"data-overview"],["matTooltip","Next currency",1,"change-icon","pl-2"]],template:function(e,t){1&e&&n["\u0275\u0275template"](0,g,8,6,"div",0),2&e&&n["\u0275\u0275property"]("ngIf",t.currency)},directives:[o.NgIf,s.a,c.a],styles:[".header[_ngcontent-%COMP%]{color:#5f5f5f;font-size:11px!important}.data-label[_ngcontent-%COMP%]{font-size:13px!important}.change-icon[_ngcontent-%COMP%]{font-size:18px;width:auto;height:auto;visibility:hidden;cursor:default}.currency-code[_ngcontent-%COMP%]{padding-right:8px;font-size:14px!important}.currency-wrapper[_ngcontent-%COMP%]{white-space:nowrap!important;height:100%}.currency-wrapper[_ngcontent-%COMP%]:hover   .change-icon[_ngcontent-%COMP%]{visibility:visible}.data-medium[_ngcontent-%COMP%]{color:#252422!important;font-size:15px!important}.data-large[_ngcontent-%COMP%], .data-medium[_ngcontent-%COMP%]{text-align:center!important;font-weight:440!important;padding-right:4px!important}.data-large[_ngcontent-%COMP%]{font-size:large!important}.data-overview[_ngcontent-%COMP%]{color:#252422!important;font-size:15px!important;padding-left:24px!important;font-weight:500!important;padding-right:4px!important;text-align:center!important;margin:auto!important}"]}),e})()},HmYF:function(e,t,i){"use strict";i.d(t,"a",(function(){return l}));var r=i("mrSG"),n=i("Iab2"),a=i("EUZL"),o=i("wd/R"),s=i("xG9w"),c=i("fXoL");let l=(()=>{class e{constructor(){this.formatColumn=(e,t,i)=>{const r=a.utils.decode_range(e["!ref"]);for(let n=r.s.r+1;n<=r.e.r;++n){const r=a.utils.encode_cell({r:n,c:t});e[r]&&e[r].v&&(e[r].t="d",e[r].z=i)}}}exportAsExcelFile(e,t,i,r,n){console.log("Excel to JSON Service",e);const o=a.utils.json_to_sheet(e);if(n&&n.length){const e=a.utils.sheet_to_json(o,{header:1}).shift();for(const t of n){const i=e.indexOf(t.fieldKey);this.formatColumn(o,i,t.fieldFormat)}}null==i&&(i=[]),null==r&&(r="DD-MM-YYYY"),this.formatExcelDateData(o,i,r);const s=a.write({Sheets:{data:o},SheetNames:["data"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(s,t)}formatExcelDateData(e,t,i){for(let a of Object.keys(e))if(null!=e[a]&&null!=e[a].t&&null!=e[a].v&&o(e[a].v,i,!0).isValid()){let r=a.replace(/[0-9]/g,"")+"1";0==s.where(t,{value:e[r].v}).length&&null!=e[r]&&null!=e[r].t&&t.push({value:e[r].v,format:i})}let r=[],n=1;for(let a of t)for(let t of Object.keys(e)){let i=parseInt(t.replace(/[^0-9]/g,""));i>n&&(n=i),null!=e[t]&&null!=e[t].v&&e[t].v==a.value&&r.push({value:t.replace(/[0-9]/g,""),format:a.format})}for(let a of r)for(let t=2;t<=n;t++)null!=e[a.value+""+t]&&null!=e[a.value+""+t].t&&(e[a.value+""+t].t="d",null!=e[a.value+""+t].v&&"Invalid date"!=e[a.value+""+t].v?e[a.value+""+t].v=o(e[a.value+""+t].v,a.format).format("YYYY/MM/DD"):(console.log(e[a.value+""+t].t),e[a.value+""+t].v="",e[a.value+""+t].t="s"))}saveAsExcelFile(e,t){const i=new Blob([e],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8"});n.saveAs(i,t.concat(".xlsx"))}exportAsExcelFileWFH(e,t,i){const r=a.utils.json_to_sheet(e),n=a.utils.json_to_sheet(t),o=a.write({Sheets:{All_Approvals:r,Pending_Approvals:n},SheetNames:["All_Approvals","Pending_Approvals"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(o,i)}exportAsExcelFileForPayroll(e,t,i,r,n,o){const s=a.utils.json_to_sheet(e),c=a.utils.json_to_sheet(t),l=a.utils.json_to_sheet(i),d=a.utils.json_to_sheet(r),u=a.utils.json_to_sheet(n),h=a.write({Sheets:{Regular_Report:s,Intern_Report:c,Contract_Report:l,Perdiem_Report:d,RP_Report:u},SheetNames:["Regular_Report","Intern_Report","Contract_Report","Perdiem_Report","RP_Report"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(h,o)}exportAsCsvFileWithSheetName(e,t){return Object(r.c)(this,void 0,void 0,(function*(){let i=a.utils.book_new();for(let t of e){let e=a.utils.json_to_sheet(t.data);a.utils.book_append_sheet(i,e,t.sheetName)}let r=a.write(i,{bookType:"xlsx",type:"binary"});yield this.saveAsCsvFile(r,t)}))}saveAsCsvFile(e,t){return Object(r.c)(this,void 0,void 0,(function*(){const i=new Blob([yield this.s2ab(e)],{type:"application/octet-stream"});n.saveAs(i,t.concat(".csv"))}))}s2ab(e){return Object(r.c)(this,void 0,void 0,(function*(){for(var t=new ArrayBuffer(e.length),i=new Uint8Array(t),r=0;r<e.length;r++)i[r]=255&e.charCodeAt(r);return t}))}exportAsExcelFileWithCellMerge(e,t,i){const r=a.utils.json_to_sheet(e);r["!merges"]=i;const n=a.write({Sheets:{data:r},SheetNames:["data"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(n,t)}exportJsonToExcelWithMultipleSheets(e,t){return Object(r.c)(this,void 0,void 0,(function*(){let i=a.utils.book_new();for(let t of e){let e=a.utils.json_to_sheet(t.data);a.utils.book_append_sheet(i,e,t.sheetName)}let r=a.write(i,{bookType:"xlsx",type:"array"});this.saveAsExcelFile(r,t)}))}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275prov=c["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},NJ67:function(e,t,i){"use strict";i.d(t,"a",(function(){return r}));class r{constructor(){this.disabled=!1}onChange(e){}onTouched(e){}writeValue(e){this.value=e}registerOnChange(e){this.onChange=e}registerOnTouched(e){this.onTouched=e}setDisabledState(e){this.disabled=e}}},hJL4:function(e,t,i){"use strict";i.d(t,"a",(function(){return u}));var r=i("mrSG"),n=i("XNiG"),a=i("xG9w"),o=i("fXoL"),s=i("tk/3"),c=i("LcQX"),l=i("XXEo"),d=i("flaP");let u=(()=>{class e{constructor(e,t,i,r){this.http=e,this.UtilityService=t,this.loginService=i,this.roleService=r,this.msg=new n.b,this.taskStatusColor=[],this.approveRequest=(e,t)=>this.http.post("/api/isa/request/approveResourceRequest",{requestDetails:e,approverOid:t}),this.rejectRequest=(e,t)=>this.http.post("/api/isa/request/rejectResourceRequest",{requestDetails:e,approverOid:t}),this.getTaskStatusColor()}getAllRequestsOfUser(e,t,i,r,n,a,o){let s=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getAllRequestsOfUser",{oid:e,statusId:t,statusCode:i,objectIds:r,skip:n,limit:a,filterConfig:o,orgIds:s})}getAllRoleAccess(){return a.where(this.roleService.roles,{application_id:139})}getRequestsBasedOnStatus(e,t,i,r,n,a,o){let s=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getRequestsBasedOnStatus",{oid:e,statusId:t,statusCode:i,objectIds:r,skip:n,limit:a,filterConfig:o,orgIds:s})}getRequestsForAwaitingApproval(e,t,i,r){return this.http.post("/api/isa/request/getRequestsForAwaitingApproval",{oid:e,skip:t,limit:i,filterConfig:r})}getObjectIdsBasedOnOid(e){return this.http.post("/api/isa/request/getObjectIdsBasedOnOid",{oid:e})}getStatusCountBasedOnOid(e,t,i,r){let n=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getStatusCountBasedOnOid",{oid:e,objectIds:t,dataTypeArray:i,filterConfig:r,orgIds:n})}getHeaderCount(e){return this.http.post("/api/isa/request/getHeaderCount",{oid:e})}getRequestBasedOnStatus(e,t){return this.http.post("/api/isa/request/getRequestBasedOnStatus",{oid:e,status:t})}getDataTypeArray(){return this.http.post("/api/isa/request/getISAStatusMasterDataUDRF",{})}sendMsg(e){this.msg.next(e)}getMsg(){return this.msg.asObservable()}getTaskStatusColor(){return new Promise((e,t)=>{this.http.post("/api/isa/request/getTaskStatusNameColor",{}).subscribe(t=>(this.taskStatusColor=t,e(t)),e=>{})})}getAllRequestsForRmg(e){return this.http.post("/api/isa/request/getAllRequestsForRmg",{filterConfig:e})}getTotalForRmg(e){return new Promise((t,i)=>{this.http.post("/api/isa/request/getTotalForRmg",{filterConfig:e}).subscribe(e=>t(e),e=>{})})}getDefaultISAStatusToDisplay(){return this.http.post("/api/isa/request/getISADefaultStatusToDisplay",{})}getCTCbreakUpForSkillSet(e){return new Promise((t,i)=>{this.http.post("/api/isa/request/getCTCbreakUpForSkillSet",e).subscribe(e=>t(e.data),e=>{i(e)})})}getCurrentUserRole(){return this.roleService&&this.roleService.roles&&this.roleService.roles.length>0?this.roleService.roles[0].role_id:null}getTemplateForUser(e){return this.http.post("/api/isa/request/getVisibleFormFieldsForUser",e)}saveAndApproveRequest(e){return this.http.post("/api/isa/request/updBudgetInfoByRequestId",e)}getVisibilityMatrix(e){return this.http.post("/api/isa/request/getAuthorzedFieldsByRoleId",e)}getISAAttachmentFromS3(e){return this.http.post("/api/isa/attachment/getISAAttachmentFromS3",{key:e})}getSkillType(){return this.http.post("/api/isa/request/getSkillType",{})}getSkillLevel(){return this.http.post("/api/isa/request/getSkillLevel",{})}getSkillExperience(){return this.http.post("/api/isa/request/getSkillExperience",{})}getSkillName(e){return this.http.post("/api/isa/request/getSkillName",{skill_type_id:e})}getIsaActivityReportDownload(){return this.http.post("/api/isa/request/getIsaActivityReportDownload",{})}getISASLAreport(e){return this.http.post("/api/isa/request/getISASLAreport",{filterConfig:e})}getISACustomReport(e){return this.http.post("/api/isa/request/getISACustomReport",{filterConfig:e})}getPositionType(){return this.http.post("/api/employee360/masterData/getPositionType",{})}calculateNetCostBasedOnPosition(e,t,i,n,o,s,c){return Object(r.c)(this,void 0,void 0,(function*(){let r;r=s&&s.length>1&&(yield this.getManpowerCostByOId(s,i,o,2))||(yield this.getManpowerCostBasedOnPosition(e,t,i,o,c));let l=yield this.getNonManpowerCost(t,i,n,o,2),d=yield this.getAllocatedCost(),u=0;u=(r?r.cost:0)+l.length>0?a.reduce(a.pluck(l,"cost"),(e,t)=>e+t,0):0;let h=d.length>0?a.reduce(a.pluck(d,"percentage"),(e,t)=>e+t,0):0;return{cost:u,currency:r&&r.currency_code?r.currency_code:"",manpowerCost:r,nonManpowerCost:l,allocatedCost:d,allocatedCostValue:u*(h/100)}}))}getManpowerCostBasedOnPosition(e,t,i,r,n){return new Promise((a,o)=>{this.http.post("/api/isa/configuration/getManpowerCostBasedOnPosition",{skillRoleId:e,nationalityId:t,locationGroupId:i,unit:r,position:n}).subscribe(e=>a(e),e=>(console.log(e),o(e)))})}getNonManpowerCost(e,t,i,r,n){return new Promise((a,o)=>{this.http.post("/api/project/getNonManpowerCost",{nationalityId:e,locationGroupId:t,locationId:i,unit:r,currency_id:n}).subscribe(e=>a(e),e=>(console.log(e),o(e)))})}getAllocatedCost(){return new Promise((e,t)=>{this.http.post("/api/project/getAllocatedCost","").subscribe(t=>e(t),e=>(console.log(e),t(e)))})}getManpowerCostByOId(e,t,i,r){return new Promise((n,a)=>{this.http.post("/api/project/getEmployeeSalary",{oid:e,location_group_id:t,unit_id:i,currency_id:r}).subscribe(e=>n(e),e=>(console.log(e),a(e)))})}getSkillNameMaster(){return this.http.post("/api/isa/configuration/getSkillName",{})}getVendorOid(e,t){return this.http.post("/api/isa/request/getVendorOid",{vendorNames:e,docID:t})}getVendorList(e,t){return this.http.post("/api/isa/request/getVendorList",{documentID:e,limitval:t})}removeVendor(e,t){return this.http.post("/api/isa/request/removeVendors",{vendorOid:e,documentID:t})}checkVendorOrNot(){return this.http.post("/api/isa/request/checkVendorOrNot",{})}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275inject"](s.c),o["\u0275\u0275inject"](c.a),o["\u0275\u0275inject"](l.a),o["\u0275\u0275inject"](d.a))},e.\u0275prov=o["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},ucYs:function(e,t,i){"use strict";i.d(t,"a",(function(){return c}));var r=i("mrSG"),n=i("xG9w"),a=i("fXoL"),o=i("tk/3"),s=i("BVzC");let c=(()=>{class e{constructor(e,t){this.http=e,this.errorService=t,this.workflowStatusList=[],this.getWorkflowStatusList()}getWorkflowStatusList(){this.http.post("/api/wfPrimary/getWorkflowStatusList","").subscribe(e=>{this.workflowStatusList=e.data},e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server While Getting Workflow Status List",e&&e.params?e.params:e&&e.error?e.error.params:{})})}getWorkflowProperties(e){return new Promise((t,i)=>{this.http.post("/api/wfPrimary/getWorkflowProperties",{applicationId:e}).subscribe(e=>t(e),e=>i(e))})}getWorkflowPropertiesByWorkflowId(e){return new Promise((t,i)=>{this.http.post("/api/wfPrimary/getWorkflowPropertiesByWorkflowId",{workflowId:e}).subscribe(e=>t(e),e=>i(e))})}getApproversHierarchy(e){return new Promise((t,i)=>{this.http.post("/api/wfPrimary/getApproversHierarchy",e).subscribe(e=>t(e),e=>i(e))})}createWorkflowItems(e){return new Promise((t,i)=>{this.http.post("/api/wfPrimary/createWorkflowItems",e).subscribe(e=>t(e),e=>i(e))})}getWorkflowDetails(e){return new Promise((t,i)=>{this.http.post("/api/wfPrimary/getWorkflowDetails",{workflowId:e}).subscribe(e=>t(e),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while getting workflow details",e&&e.params?e.params:e&&e.error?e.error.params:{})})})}formatApproversHierarchy(e,t){return Object(r.c)(this,void 0,void 0,(function*(){0==e.length&&t.cc0&&t.cc0.appr0&&t.cc0.appr0.length>0&&e.push([{approvalType:"I",purposeOfInsertingIntoArray:"Just to ensure that 'I' type data is used, if it exists"}]);for(let i=0;i<e.length;i++){let r=[],a=n.keys(t["cc"+i]);for(let n=0;n<a.length;n++)for(let o=0;o<t["cc"+i][a[n]].length;o++){let s={name:t["cc"+i][a[n]][o].DELEGATE_NAME,oid:t["cc"+i][a[n]][o].DELEGATE_OID,level:n+1,designation:t["cc"+i][a[n]][o].DELEGATE_DESIGNATION_NAME,is_delegated:t["cc"+i][a[n]][o].IS_DELEGATED,role:t["cc"+i][a[n]][o].DELEGATE_ROLE_NAME};if(1==t["cc"+i][a[n]][o].IS_DELEGATED&&(s.delegated_by={name:t["cc"+i][a[n]][o].APPROVER_NAME,oid:t["cc"+i][a[n]][o].APPROVER_OID,level:n+1,designation:t["cc"+i][a[n]][o].APPROVER_DESIGNATION_NAME}),r.push(s),i==e.length-1&&n==a.length-1&&o==t["cc"+i][a[n]].length-1)return r}}}))}storeComments(e,t,i){return new Promise((r,n)=>{this.http.post("/api/wfPrimary/updateComments",{workflowHeaderId:e,newComments:t,commentor:i}).subscribe(e=>r(e),e=>(this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server Error while Updating Workflow Comments",e&&e.params?e.params:e&&e.error?e.error.params:{}),n(e)))})}updateWorkflowItems(e){return new Promise((t,i)=>{this.http.post("/api/wfPrimary/updateWorkflowItems",e).subscribe(e=>t(e),e=>(console.log(e),i(e)))})}formatApproversHierarchyForOpportunityApprovalActivity(e){return Object(r.c)(this,void 0,void 0,(function*(){for(let t=0;t<1;t++){let i=[],r=n.keys(e["cc"+t]);for(let n=0;n<r.length;n++)for(let a=0;a<e["cc"+t][r[n]].length;a++){let o={name:e["cc"+t][r[n]][a].DELEGATE_NAME,oid:e["cc"+t][r[n]][a].DELEGATE_OID,level:e["cc"+t][r[n]][a].APPROVAL_ORDER,designation:e["cc"+t][r[n]][a].DELEGATE_DESIGNATION_NAME,is_delegated:e["cc"+t][r[n]][a].IS_DELEGATED};if(1==e["cc"+t][r[n]][a].IS_DELEGATED&&(o.delegated_by={name:e["cc"+t][r[n]][a].APPROVER_NAME,oid:e["cc"+t][r[n]][a].APPROVER_OID,level:e["cc"+t][r[n]][a].APPROVAL_ORDER,designation:e["cc"+t][r[n]][a].APPROVER_DESIGNATION_NAME}),i.push(o),n==r.length-1&&a==e["cc"+t][r[n]].length-1)return i}}}))}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275inject"](o.c),a["\u0275\u0275inject"](s.a))},e.\u0275prov=a["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},w76M:function(e,t,i){"use strict";i.d(t,"a",(function(){return l})),i.d(t,"b",(function(){return d}));var r=i("jhN1"),n=i("fXoL"),a=i("oHs6"),o=i("PVOt"),s=i("6t9p");const c=["*"];let l=(()=>{let e=class extends o.b{constructor(e,t,i,r,n,a,o,s){super(e,t,i,r,o,s),this._watcherHelper=r,this._idh=n,this._createEventEmitters([{subscribe:"contentReady",emit:"onContentReady"},{subscribe:"disposing",emit:"onDisposing"},{subscribe:"hidden",emit:"onHidden"},{subscribe:"hiding",emit:"onHiding"},{subscribe:"initialized",emit:"onInitialized"},{subscribe:"optionChanged",emit:"onOptionChanged"},{subscribe:"resize",emit:"onResize"},{subscribe:"resizeEnd",emit:"onResizeEnd"},{subscribe:"resizeStart",emit:"onResizeStart"},{subscribe:"showing",emit:"onShowing"},{subscribe:"shown",emit:"onShown"},{subscribe:"titleRendered",emit:"onTitleRendered"},{emit:"accessKeyChange"},{emit:"animationChange"},{emit:"closeOnOutsideClickChange"},{emit:"containerChange"},{emit:"contentTemplateChange"},{emit:"deferRenderingChange"},{emit:"disabledChange"},{emit:"dragEnabledChange"},{emit:"elementAttrChange"},{emit:"focusStateEnabledChange"},{emit:"fullScreenChange"},{emit:"heightChange"},{emit:"hintChange"},{emit:"hoverStateEnabledChange"},{emit:"maxHeightChange"},{emit:"maxWidthChange"},{emit:"minHeightChange"},{emit:"minWidthChange"},{emit:"positionChange"},{emit:"resizeEnabledChange"},{emit:"rtlEnabledChange"},{emit:"shadingChange"},{emit:"shadingColorChange"},{emit:"showCloseButtonChange"},{emit:"showTitleChange"},{emit:"tabIndexChange"},{emit:"titleChange"},{emit:"titleTemplateChange"},{emit:"toolbarItemsChange"},{emit:"visibleChange"},{emit:"widthChange"}]),this._idh.setHost(this),a.setHost(this)}get accessKey(){return this._getOption("accessKey")}set accessKey(e){this._setOption("accessKey",e)}get animation(){return this._getOption("animation")}set animation(e){this._setOption("animation",e)}get closeOnOutsideClick(){return this._getOption("closeOnOutsideClick")}set closeOnOutsideClick(e){this._setOption("closeOnOutsideClick",e)}get container(){return this._getOption("container")}set container(e){this._setOption("container",e)}get contentTemplate(){return this._getOption("contentTemplate")}set contentTemplate(e){this._setOption("contentTemplate",e)}get deferRendering(){return this._getOption("deferRendering")}set deferRendering(e){this._setOption("deferRendering",e)}get disabled(){return this._getOption("disabled")}set disabled(e){this._setOption("disabled",e)}get dragEnabled(){return this._getOption("dragEnabled")}set dragEnabled(e){this._setOption("dragEnabled",e)}get elementAttr(){return this._getOption("elementAttr")}set elementAttr(e){this._setOption("elementAttr",e)}get focusStateEnabled(){return this._getOption("focusStateEnabled")}set focusStateEnabled(e){this._setOption("focusStateEnabled",e)}get fullScreen(){return this._getOption("fullScreen")}set fullScreen(e){this._setOption("fullScreen",e)}get height(){return this._getOption("height")}set height(e){this._setOption("height",e)}get hint(){return this._getOption("hint")}set hint(e){this._setOption("hint",e)}get hoverStateEnabled(){return this._getOption("hoverStateEnabled")}set hoverStateEnabled(e){this._setOption("hoverStateEnabled",e)}get maxHeight(){return this._getOption("maxHeight")}set maxHeight(e){this._setOption("maxHeight",e)}get maxWidth(){return this._getOption("maxWidth")}set maxWidth(e){this._setOption("maxWidth",e)}get minHeight(){return this._getOption("minHeight")}set minHeight(e){this._setOption("minHeight",e)}get minWidth(){return this._getOption("minWidth")}set minWidth(e){this._setOption("minWidth",e)}get position(){return this._getOption("position")}set position(e){this._setOption("position",e)}get resizeEnabled(){return this._getOption("resizeEnabled")}set resizeEnabled(e){this._setOption("resizeEnabled",e)}get rtlEnabled(){return this._getOption("rtlEnabled")}set rtlEnabled(e){this._setOption("rtlEnabled",e)}get shading(){return this._getOption("shading")}set shading(e){this._setOption("shading",e)}get shadingColor(){return this._getOption("shadingColor")}set shadingColor(e){this._setOption("shadingColor",e)}get showCloseButton(){return this._getOption("showCloseButton")}set showCloseButton(e){this._setOption("showCloseButton",e)}get showTitle(){return this._getOption("showTitle")}set showTitle(e){this._setOption("showTitle",e)}get tabIndex(){return this._getOption("tabIndex")}set tabIndex(e){this._setOption("tabIndex",e)}get title(){return this._getOption("title")}set title(e){this._setOption("title",e)}get titleTemplate(){return this._getOption("titleTemplate")}set titleTemplate(e){this._setOption("titleTemplate",e)}get toolbarItems(){return this._getOption("toolbarItems")}set toolbarItems(e){this._setOption("toolbarItems",e)}get visible(){return this._getOption("visible")}set visible(e){this._setOption("visible",e)}get width(){return this._getOption("width")}set width(e){this._setOption("width",e)}get toolbarItemsChildren(){return this._getOption("toolbarItems")}set toolbarItemsChildren(e){this.setChildren("toolbarItems",e)}_createInstance(e,t){return new a.a(e,t)}ngOnDestroy(){this._destroyWidget()}ngOnChanges(e){super.ngOnChanges(e),this.setupChanges("toolbarItems",e)}setupChanges(e,t){e in this._optionsToUpdate||this._idh.setup(e,t)}ngDoCheck(){this._idh.doCheck("toolbarItems"),this._watcherHelper.checkWatchers(),super.ngDoCheck(),super.clearChangedOptions()}_setOption(e,t){let i=this._idh.setupSingle(e,t),r=null!==this._idh.getChanges(e,t);(i||r)&&super._setOption(e,t)}};return e.\u0275fac=function(t){return new(t||e)(n["\u0275\u0275directiveInject"](n.ElementRef),n["\u0275\u0275directiveInject"](n.NgZone),n["\u0275\u0275directiveInject"](o.e),n["\u0275\u0275directiveInject"](o.j),n["\u0275\u0275directiveInject"](o.g),n["\u0275\u0275directiveInject"](o.i),n["\u0275\u0275directiveInject"](r.h),n["\u0275\u0275directiveInject"](n.PLATFORM_ID))},e.\u0275cmp=n["\u0275\u0275defineComponent"]({type:e,selectors:[["dx-popup"]],contentQueries:function(e,t,i){if(1&e&&n["\u0275\u0275contentQuery"](i,s.L,!1),2&e){let e;n["\u0275\u0275queryRefresh"](e=n["\u0275\u0275loadQuery"]())&&(t.toolbarItemsChildren=e)}},inputs:{accessKey:"accessKey",animation:"animation",closeOnOutsideClick:"closeOnOutsideClick",container:"container",contentTemplate:"contentTemplate",deferRendering:"deferRendering",disabled:"disabled",dragEnabled:"dragEnabled",elementAttr:"elementAttr",focusStateEnabled:"focusStateEnabled",fullScreen:"fullScreen",height:"height",hint:"hint",hoverStateEnabled:"hoverStateEnabled",maxHeight:"maxHeight",maxWidth:"maxWidth",minHeight:"minHeight",minWidth:"minWidth",position:"position",resizeEnabled:"resizeEnabled",rtlEnabled:"rtlEnabled",shading:"shading",shadingColor:"shadingColor",showCloseButton:"showCloseButton",showTitle:"showTitle",tabIndex:"tabIndex",title:"title",titleTemplate:"titleTemplate",toolbarItems:"toolbarItems",visible:"visible",width:"width"},outputs:{onContentReady:"onContentReady",onDisposing:"onDisposing",onHidden:"onHidden",onHiding:"onHiding",onInitialized:"onInitialized",onOptionChanged:"onOptionChanged",onResize:"onResize",onResizeEnd:"onResizeEnd",onResizeStart:"onResizeStart",onShowing:"onShowing",onShown:"onShown",onTitleRendered:"onTitleRendered",accessKeyChange:"accessKeyChange",animationChange:"animationChange",closeOnOutsideClickChange:"closeOnOutsideClickChange",containerChange:"containerChange",contentTemplateChange:"contentTemplateChange",deferRenderingChange:"deferRenderingChange",disabledChange:"disabledChange",dragEnabledChange:"dragEnabledChange",elementAttrChange:"elementAttrChange",focusStateEnabledChange:"focusStateEnabledChange",fullScreenChange:"fullScreenChange",heightChange:"heightChange",hintChange:"hintChange",hoverStateEnabledChange:"hoverStateEnabledChange",maxHeightChange:"maxHeightChange",maxWidthChange:"maxWidthChange",minHeightChange:"minHeightChange",minWidthChange:"minWidthChange",positionChange:"positionChange",resizeEnabledChange:"resizeEnabledChange",rtlEnabledChange:"rtlEnabledChange",shadingChange:"shadingChange",shadingColorChange:"shadingColorChange",showCloseButtonChange:"showCloseButtonChange",showTitleChange:"showTitleChange",tabIndexChange:"tabIndexChange",titleChange:"titleChange",titleTemplateChange:"titleTemplateChange",toolbarItemsChange:"toolbarItemsChange",visibleChange:"visibleChange",widthChange:"widthChange"},features:[n["\u0275\u0275ProvidersFeature"]([o.e,o.j,o.i,o.g]),n["\u0275\u0275InheritDefinitionFeature"],n["\u0275\u0275NgOnChangesFeature"]],ngContentSelectors:c,decls:1,vars:0,template:function(e,t){1&e&&(n["\u0275\u0275projectionDef"](),n["\u0275\u0275projection"](0))},encapsulation:2}),e})(),d=(()=>{let e=class{};return e.\u0275mod=n["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=n["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[s.bb,s.Gc,s.Vd,s.vd,s.hb,s.lb,s.sb,s.id,s.jd,s.M,o.c,o.f,r.b],s.bb,s.Gc,s.Vd,s.vd,s.hb,s.lb,s.sb,s.id,s.jd,s.M,o.f]}),e})()},x6xq:function(e,t,i){"use strict";i.r(t),i.d(t,"TreasureModule",(function(){return O}));var r=i("ofXK"),n=i("tyNb"),a=i("mrSG"),o=i("xG9w"),s=i("wd/R"),c=i("R0Ic"),l=i("XNiG"),d=i("1G5W"),u=i("fXoL"),h=i("GnQ3"),p=i("LcQX"),m=i("JqCM"),g=i("a1r6"),f=i("0IaG"),y=i("HmYF"),b=i("xi/V"),C=i("Wk3H");const v=[{path:"",component:(()=>{class e{constructor(e,t,i,r,n,c,u){this._udrfService=e,this._utilityService=t,this.spinner=i,this._p2pGeneralService=r,this.router=n,this.dialog=c,this._excelService=u,this.applicationId=254,this.$onAppApiCalled=new l.b,this.selectedCard=[],this.skip=0,this.limit=15,this.$onDestroy=new l.b,this.udrfBodyColumns=[{item:"pr_code",header:"PR code",isActive:!0,isVisible:"true",type:"text",position:1,colSize:1,sortOrder:"N",width:75,textClass:"value13Bold cp"},{item:"vendor_name",header:"Vendor",isActive:!0,isVisible:"true",type:"text",textClass:"value13Bold cp colorRed",position:2,colSize:3,sortOrder:"N",width:300},{item:"is_msme",header:"Vendor Type",isActive:!0,isVisible:"false",type:"text",textClass:"value13Bold cp colorRed",position:3,colSize:3,sortOrder:"N",width:300},{item:"description",header:"PR Description",isActive:!0,isVisible:"true",type:"text",position:4,colSize:3,sortOrder:"N",width:300},{item:"amount",header:"Amount",isActive:!0,isVisible:"true",type:"currency",textClass:"value13Bold cp colorRed",position:5,colSize:3,sortOrder:"N",width:240},{item:"status",header:"Status",isActive:!0,isVisible:"true",type:"status",position:6,colSize:2,sortOrder:"N",width:180},{item:"payment_model",header:"Payment model",isActive:!0,isVisible:"true",type:"text",position:12,colSize:3,sortOrder:"N",width:240},{item:"pref_amt",header:"Vendor Currency",isActive:!0,isVisible:"true",type:"currency",position:13,colSize:3,sortOrder:"N",width:240,textClass:"value13Bold cp colorRed text-right"},{item:"created_on",header:"Created on",isActive:!0,isVisible:"true",type:"date",textClass:"value13Bold cp",position:8,colSize:1,sortOrder:"N",width:130},{item:"created_by_name",header:"Created by",isActive:!0,isVisible:"true",type:"text",position:10,colSize:1,sortOrder:"N",width:180},{item:"cost_center_name",header:"Cost center",isActive:!0,isVisible:"true",type:"text",textClass:"value13Bold cp colorRed",position:4,colSize:3,sortOrder:"N",width:240},{item:"sub_group_name",header:"Sub group",isActive:!0,isVisible:"true",type:"text",position:7,colSize:3,sortOrder:"N",width:240},{item:"invoice_date",header:"Invoice Date",isActive:!0,isVisible:"false",type:"date",textClass:"value13Bold cp",position:9,colSize:1,sortOrder:"N",width:130},{item:"po_number",header:"PO Number",isActive:!0,isVisible:"false",type:"text",position:13,colSize:3,sortOrder:"N",width:240},{item:"action",header:"Actions",isActive:!0,isVisible:"false",type:"action",position:11,colSize:3,width:240,sortOrder:"N"},{item:"aging",header:"Aging",isActive:!0,isVisible:"true",type:"text",position:11,colSize:3,width:240,sortOrder:"N"}],this.isSelectedCurrency=!1,this.minNoOfVisibleSummaryCards=3,this.maxNoOfVisibleSummaryCards=6,this.isCardClicked=!1,this.cardClicked="",this.itemDataCurrentIndex=0,this.quickCTAInput={},this.commentsInput={},this.commentsContext={},this.categorisedDataTypeArray=[],this.dataTypeArray=[],this.udrfItemStatusColor=[],this.isBillablePRAailable=!1,this.isToCreateBillablePRAllowed=!1,this.initReport=()=>{this.$onAppApiCalled.next(),this.itemDataCurrentIndex=0,this.skip=0,this._udrfService.udrfBodyData=[];for(let e=0;e<this.dataTypeArray.length;e++)this.dataTypeArray[e].isActive=!1;this.isCardClicked=!1,this.cardClicked="",this._udrfService.udrfUiData.resolveColumnConfig(),this.getP2pTreasureList()},this.resolveVisibleDataTypeArray=()=>{for(let e of this._udrfService.udrfUiData.summaryCards){let t;this._udrfService.udrfData.udrfSummaryCardCodes.length>0&&(t=o.contains(this._udrfService.udrfData.udrfSummaryCardCodes,e.dataTypeCode),e.isVisible=t,t&&(this._udrfService.udrfUiData.summaryCardsItem=e))}},this.itemCardClicked=()=>{this.router.navigateByUrl("/main/p2p/treasuryDetail/"+this._udrfService.udrfUiData.itemCardSelecteditem.p2p_header_id)},this.dataTypeCardSelected=()=>{this._udrfService.udrfData.isItemDataLoading=!0,this._udrfService.udrfData.noItemDataFound=!1;let e=this._udrfService.udrfUiData.summaryCardsItem;for(let t=0;t<this.dataTypeArray.length;t++)this.dataTypeArray[t].dataType!=e.dataType&&(this.dataTypeArray[t].isActive=!1);e.isActive=!e.isActive,this.isCardClicked=!0,this.cardClicked=e.dataType,this.itemDataCurrentIndex=0,this._udrfService.udrfBodyData=[],this.getP2pTreasureList()},this.itemDataScrollDown=()=>{this._udrfService.udrfData.noItemDataFound||this._udrfService.udrfData.isItemDataLoading||(this.itemDataCurrentIndex+=this._udrfService.udrfData.defaultRecordsPerFetch,this._udrfService.udrfData.isItemDataLoading=!0,this.getP2pTreasureList())},this.getP2pTreasureList=()=>{this.spinner.show();let e=JSON.parse(JSON.stringify(this._udrfService.udrfData.mainFilterArray)),t=o.where(this.dataTypeArray,{isActive:!0}),i=[],r=!1;if(t.length>0&&(i=o.where(t,{cardType:"status"}),i.length>0))if(e.length>0){for(let i of e)"Status"==i.filterName&&(i.multiOptionSelectSearchValues=[t[0].dataType],r=!0);if(0==r){let i=JSON.parse(JSON.stringify(o.where(this._udrfService.udrfData.filterTypeArray,{filterName:"Status"})));e.push(i[0]);for(let n of e)"Status"==n.filterName&&(n.multiOptionSelectSearchValues=[t[0].dataType],r=!0)}}else e=JSON.parse(JSON.stringify(o.where(this._udrfService.udrfData.filterTypeArray,{filterName:"Status"}))),e[0].multiOptionSelectSearchValues=[t[0].dataType];let n={startIndex:this.itemDataCurrentIndex,startDate:this._udrfService.udrfData.mainApiDateRangeStart,endDate:this._udrfService.udrfData.mainApiDateRangeEnd,mainFilterArray:e,txTableDetails:this._udrfService.udrfData.txTableDetails,mainSearchParameter:this._udrfService.udrfData.mainSearchParameter,searchTableDetails:this._udrfService.udrfData.searchTableDetails};this.requestDataSubscription&&this.requestDataSubscription.unsubscribe(),this.requestDataSubscription=this._p2pGeneralService.getTreasuryReportList("PR",n).pipe(Object(d.a)(this.$onDestroy)).pipe(Object(d.a)(this.$onAppApiCalled)).subscribe(e=>Object(a.c)(this,void 0,void 0,(function*(){this._p2pGeneralService.getVendorType().pipe(Object(d.a)(this.$onDestroy)).pipe(Object(d.a)(this.$onAppApiCalled)).subscribe(t=>Object(a.c)(this,void 0,void 0,(function*(){if(this.vendorTypeDetails=t,"S"==e.messType&&e.data&&e.data.length>0){let t;if(this.isCardClicked||0!=this.itemDataCurrentIndex){o.each(e.data,e=>{e.expanded=this._udrfService.udrfUiData.collapseAll});let t=JSON.parse(JSON.stringify(this._udrfService.udrfBodyData));t=t.concat(e.data),t=o.uniq(t,e=>e.p2p_header_id),this._udrfService.udrfBodyData=t}else{o.each(e.data,e=>{null!=e.is_msme?(t=this.vendorTypeDetails.filter(t=>t.is_msme===e.is_msme),e.is_msme=t[0].vendortype):e.is_msme="-",e.expanded=this._udrfService.udrfUiData.collapseAll});let i=JSON.parse(JSON.stringify(this._udrfService.udrfBodyData));i=i.concat(e.data),i=o.uniq(i,e=>e.p2p_header_id),this._udrfService.udrfBodyData=i,yield this.getP2pTreasureSummaryCardData()}}else this._udrfService.udrfBodyData=this._udrfService.udrfBodyData.concat([]),this._udrfService.udrfData.noItemDataFound=!0;this._udrfService.udrfData.isItemDataLoading=!1})))})))},this.getP2pTreasureSummaryCardData=()=>{this._udrfService.udrfUiData.totalItemDataCount=0;let e=JSON.parse(JSON.stringify(this._udrfService.udrfData.mainFilterArray));this._p2pGeneralService.getTreasuryReportSummaryCard("PR",0,{startIndex:this.itemDataCurrentIndex,startDate:this._udrfService.udrfData.mainApiDateRangeStart,endDate:this._udrfService.udrfData.mainApiDateRangeEnd,mainFilterArray:e,txTableDetails:this._udrfService.udrfData.txTableDetails,mainSearchParameter:this._udrfService.udrfData.mainSearchParameter,searchTableDetails:this._udrfService.udrfData.searchTableDetails}).pipe(Object(d.a)(this.$onDestroy)).pipe(Object(d.a)(this.$onAppApiCalled)).subscribe(e=>Object(a.c)(this,void 0,void 0,(function*(){if("S"==e.messType&&e.messData&&e.messData.length>0){for(let e=0;e<this.dataTypeArray.length;e++)this.dataTypeArray[e].isActive=!1,this.dataTypeArray[e].dataTypeValue="0";this._udrfService.udrfUiData.totalItemDataCount=e.total,this.dataTypeArray=this.dataTypeArray.map((t,i)=>{const r=e.messData.find(e=>t.dataType==e.dataType);return Object.assign(Object.assign({},t),r)}),this._udrfService.udrfUiData.summaryCards=this.dataTypeArray}else{for(let e=0;e<this.dataTypeArray.length;e++)this.dataTypeArray[e].isActive=!1,this.dataTypeArray[e].dataTypeValue="0";this._udrfService.udrfUiData.summaryCards=this.dataTypeArray}})))},this.showErrorMessage=e=>{this._utilityService.showErrorMessage("Error:Unable to fetch Data","KEBS")},this.downloadItemDataReport=()=>{this._udrfService.udrfUiData.isReportDownloading=!0;let e=JSON.parse(JSON.stringify(this._udrfService.udrfData.mainFilterArray));this._p2pGeneralService.getTreasuryReportList("PR",{startIndex:"D",startDate:this._udrfService.udrfData.mainApiDateRangeStart,endDate:this._udrfService.udrfData.mainApiDateRangeEnd,mainFilterArray:e,txTableDetails:this._udrfService.udrfData.txTableDetails,mainSearchParameter:this._udrfService.udrfData.mainSearchParameter,searchTableDetails:this._udrfService.udrfData.searchTableDetails}).pipe(Object(d.a)(this.$onDestroy)).pipe(Object(d.a)(this.$onAppApiCalled)).subscribe(e=>Object(a.c)(this,void 0,void 0,(function*(){this._p2pGeneralService.getVendorType().pipe(Object(d.a)(this.$onDestroy)).pipe(Object(d.a)(this.$onAppApiCalled)).subscribe(t=>Object(a.c)(this,void 0,void 0,(function*(){if(this.vendorTypeDetails=t,"S"==e.messType&&e.data.length>0){let t;o.each(e.data,e=>{e.created_on=null!=e.created_on?s(e.created_on).format("DD-MM-YYYY"):e.created_on;let t="";"string"!=typeof e.amount&&(o.each(e.amount,e=>{t+=`${e.value} ${e.currency_code}  `}),e.amount=t),delete e.l2}),o.each(e.data,e=>{null!=e.is_msme&&(t=this.vendorTypeDetails.filter(t=>t.is_msme===e.is_msme),e.is_msme=t[0].vendortype)}),console.log("Global Vendor Type on PR : ",e.data),this._excelService.exportAsExcelFile(e.data,e.fileName),this._utilityService.showToastMessage(e.messText)}else this._p2pGeneralService.showMessage("Failed to download report, Kindly contact KEBS team !");this._udrfService.udrfUiData.isReportDownloading=!1})))})),e=>{this._udrfService.udrfUiData.isReportDownloading=!1,this._utilityService.showErrorMessage(e,"KEBS")})},this.getPurchaseRequestTenantConfig=e=>Object(a.c)(this,void 0,void 0,(function*(){return new Promise((t,i)=>{this._p2pGeneralService.getP2pTenantConfig(e).subscribe(e=>{"S"==e.messType?(this.udrfBodyColumns.push({item:"selected_amount",header:"Selected Amount",isActive:!0,isVisible:"true",type:"text",position:12,colSize:3,sortOrder:"N",width:240}),t(!0)):t(!1)},e=>{i(e)})})})),this.ngOnDestroy=()=>{this.$onDestroy.next(),this.$onDestroy.complete(),this._udrfService.resetUdrfData(),this.requestDataSubscription&&this.requestDataSubscription.unsubscribe()},this.getPrStatusMasterData=()=>new Promise((e,t)=>{this._p2pGeneralService.getPurchaseRequestHeaderStatus().pipe(Object(d.a)(this.$onDestroy)).subscribe(i=>{i.err?t(i):e(i.data)},e=>{console.log(e)})}),this.openBillablePRs=()=>{console.log("Billable PR Button has been clicked..."),this.router.navigateByUrl("/main/p2p/treasury/billable-prs")}}ngOnInit(){return Object(a.c)(this,void 0,void 0,(function*(){this.isBillablePRAailable=yield this.getPurchaseRequestTenantConfig("CBBNBPR"),this.isBillablePRAailable&&(this.udrfBodyColumns.push({item:"billing_type",header:"Billing Type",isActive:!0,isVisible:"false",type:"text",position:11,colSize:3,width:240,sortOrder:"N"}),this.checkForBillablePRAccess()),this.resolveObservables();let e=yield this.getPrStatusMasterData();this.initStatusData(e),this.initUdrfConfig(),this.isSelectedCurrency=yield this.getPurchaseRequestTenantConfig("SCE")}))}initStatusData(e){let t=[],i=[];e.forEach((e,r)=>{t.push({status:e.name,color:e.color}),i.push({dataType:e.name,dataTypeValue:"0",isActive:!1,isVisible:!!e.is_visible,cardType:"status",statusColor:e.color,dataTypeCode:e.code})}),this.dataTypeArray=i,this.udrfItemStatusColor=t,this.categorisedDataTypeArray=[{categoryType:"Status Cards",categoryCardCodes:o.pluck(e,"code"),categoryCards:[]}]}initUdrfConfig(){return Object(a.c)(this,void 0,void 0,(function*(){let e=[{checkboxId:"CDCRD",checkboxName:"Current Year",checkboxStartValue:s().startOf("year"),checkboxEndValue:s().endOf("year"),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD2",checkboxName:"This Week",checkboxStartValue:this._utilityService.getFormattedDate(s().startOf("week"),s(s().startOf("week")).date,15,0,0,0),checkboxEndValue:this._utilityService.getFormattedDate(s().endOf("week"),s(s().endOf("week")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD3",checkboxName:"This Month",checkboxStartValue:this._utilityService.getFormattedDate(s().startOf("month"),s(s().startOf("month")).date,15,0,0,0),checkboxEndValue:this._utilityService.getFormattedDate(s().endOf("month"),s(s().endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD4",checkboxName:"Previous Month",checkboxStartValue:this._utilityService.getFormattedDate(s().subtract(1,"months").startOf("month"),s(s().subtract(1,"months").startOf("month")).date,15,0,0,0),checkboxEndValue:this._utilityService.getFormattedDate(s().subtract(1,"months").endOf("month"),s(s().subtract(1,"months").endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD5",checkboxName:"All",checkboxStartValue:s("1920-01-01"),checkboxEndValue:s("2100-12-12"),isCheckboxDefaultSelected:!0}];this._udrfService.udrfFunctions.constructCustomRangeData(4,"date",e),this.itemDataCurrentIndex=0,this._udrfService.udrfBodyData=[],this._udrfService.udrfData.applicationId=this.applicationId,this._udrfService.udrfUiData.showNewReleasesButton=!0,this._udrfService.udrfUiData.showItemDataCount=!0,this._udrfService.udrfUiData.itemDataType="",this._udrfService.udrfUiData.totalItemDataCount=0,this._udrfService.udrfUiData.showSearchBar=!0,this._udrfService.udrfUiData.showActionButtons=!0,this._udrfService.udrfUiData.showUdrfModalButton=!0,this._udrfService.udrfUiData.showSettingsModalButton=!0,this._udrfService.udrfUiData.isReportDownloading=!1,this._udrfService.udrfUiData.showReportDownloadButton=!1,this._udrfService.udrfUiData.showColumnConfigButton=!0,this._udrfService.udrfUiData.udrfItemStatusColor=this.udrfItemStatusColor,this._udrfService.udrfUiData.horizontalScroll=!0,this._udrfService.udrfUiData.resolveVisibleSummaryCards=this.resolveVisibleDataTypeArray.bind(this),this._udrfService.udrfUiData.summaryCardsSelected=this.dataTypeCardSelected.bind(this),this._udrfService.udrfUiData.summaryCardsItem={},this._udrfService.udrfUiData.downloadItemDataReport=()=>{},this._udrfService.udrfUiData.itemDataScrollDown=this.itemDataScrollDown.bind(this),this._udrfService.udrfUiData.summaryCards=this.dataTypeArray,this._udrfService.udrfUiData.udrfBodyColumns=this.udrfBodyColumns,this._udrfService.udrfUiData.udrfVisibleBodyColumns=this._udrfService.udrfUiData.udrfVisibleBodyColumns,this._udrfService.udrfUiData.udrfInvisibleBodyColumns=this._udrfService.udrfUiData.udrfInvisibleBodyColumns,this._udrfService.udrfUiData.categorisedSummaryCards=this.categorisedDataTypeArray,this._udrfService.udrfUiData.minNoOfVisibleSummaryCards=this.minNoOfVisibleSummaryCards,this._udrfService.udrfUiData.maxNoOfVisibleSummaryCards=this.maxNoOfVisibleSummaryCards,this._udrfService.udrfUiData.selectedCard=this.selectedCard,this._udrfService.udrfUiData.variant=1,this._udrfService.udrfUiData.itemcardSelected=this.itemCardClicked.bind(this),this._udrfService.udrfUiData.quickCTAInput=this.quickCTAInput,this._udrfService.udrfUiData.commentsInput=this.commentsInput,this._udrfService.udrfUiData.commentsContext=this.commentsContext,this._udrfService.udrfUiData.collapseAll=!0,this._udrfService.udrfUiData.showCollapseButton=!0,this._udrfService.udrfUiData.showActualCurrencyForCurrencyComponent=!0,this._udrfService.udrfUiData.itemHasComments=!0,this._udrfService.udrfUiData.openComments=this.openComments.bind(this),this._udrfService.udrfUiData.openCommentsData={},this._udrfService.udrfFunctions.resolveVisibleColumnConfigItems(),this._udrfService.udrfUiData.isReportDownloading=!1,this._udrfService.udrfUiData.showReportDownloadButton=!0,this._udrfService.udrfUiData.downloadItemDataReport=this.downloadItemDataReport.bind(this),this.isToCreateBillablePRAllowed&&(this._udrfService.udrfUiData.billableButton=!0,this._udrfService.udrfUiData.openBillablePRs=this.openBillablePRs.bind(this)),this._udrfService.getAppUdrfConfig(this.applicationId,this.initReport.bind(this)),this._udrfService.getNotifyReleasesUDRF(),this._udrfService.udrfUiData.resolveColumnConfig()}))}openComments(){return Object(a.c)(this,void 0,void 0,(function*(){let e,t=this._udrfService.udrfUiData.openCommentsData.data;if(console.log(t),t){e={application_id:244,unique_id_1:t.p2p_header_id,application_name:"Procure To Pay",title:t.pr_code?t.pr_code:""};let r={inputData:e,context:{"Vendor name":t.vendor_name?t.vendor_name:"","Payment term":t.payment_term?t.payment_term:"","Invoice Date":t.invoice_date?s(t.invoice_date).format("DD-MMM-YY"):""},commentBoxHeight:"100vh",commentBoxScrollHeight:"80%"};const{ChatCommentContextModalComponent:n}=yield Promise.all([i.e(4),i.e(63),i.e(75),i.e(983)]).then(i.bind(null,"vg2w"));this.dialog.open(n,{height:"100%",width:"65%",position:{right:"0px"},data:{modalParams:r}})}}))}resolveObservables(){this._p2pGeneralService.$compDestroyObservable.subscribe(e=>{"treasury"==e&&"all"!=e||this.ngOnDestroy()})}checkForBillablePRAccess(){return Object(a.c)(this,void 0,void 0,(function*(){this.isToCreateBillablePRAllowed=yield this._p2pGeneralService.checkForBillablePRAcces()}))}}return e.\u0275fac=function(t){return new(t||e)(u["\u0275\u0275directiveInject"](h.a),u["\u0275\u0275directiveInject"](p.a),u["\u0275\u0275directiveInject"](m.c),u["\u0275\u0275directiveInject"](g.a),u["\u0275\u0275directiveInject"](n.g),u["\u0275\u0275directiveInject"](f.b),u["\u0275\u0275directiveInject"](y.a))},e.\u0275cmp=u["\u0275\u0275defineComponent"]({type:e,selectors:[["app-treasure-report-landing-page"]],decls:3,vars:0,consts:[[1,"container-fluid","treasure-report-component","pl-0","pr-0"]],template:function(e,t){1&e&&(u["\u0275\u0275elementStart"](0,"div",0),u["\u0275\u0275element"](1,"udrf-header"),u["\u0275\u0275element"](2,"udrf-body"),u["\u0275\u0275elementEnd"]())},directives:[b.a,C.a],styles:[".treasure-report-component[_ngcontent-%COMP%]   .status-circular[_ngcontent-%COMP%]{height:13px;width:13px;margin-top:4px;border-radius:50%;box-shadow:0 2px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.treasure-report-component[_ngcontent-%COMP%]     .udrf-header-card{background-color:#fafafa!important}.treasure-report-component[_ngcontent-%COMP%]   .is-submitted[_ngcontent-%COMP%]{background:#ff7200;color:#fff!important}.treasure-report-component[_ngcontent-%COMP%]   .is-draft[_ngcontent-%COMP%]{background:#c7c4c4;color:#fff!important}.treasure-report-component[_ngcontent-%COMP%]   .is-approved[_ngcontent-%COMP%]{background:#009432;color:#fff!important}.treasure-report-component[_ngcontent-%COMP%]   .is-wfh[_ngcontent-%COMP%]{background:#9980fa;color:#fff!important}.treasure-report-component[_ngcontent-%COMP%]   .is-rejected[_ngcontent-%COMP%]{background:#cf0001;color:#fff!important}.treasure-report-component[_ngcontent-%COMP%]   .is-cancelled[_ngcontent-%COMP%]{background:#9f2825;color:#fff!important}.treasure-report-component[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field{width:40vw;padding-top:25px}.treasure-report-component[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-infix{font-size:14px!important;padding:.5em 0!important;border-top:.54375em solid transparent!important}.treasure-report-component[_ngcontent-%COMP%]   .spinner-align[_ngcontent-%COMP%]{margin:auto;display:inline-block}.treasure-report-component[_ngcontent-%COMP%]     .infinite-scroll-fixed{max-height:55vh!important;min-height:55vh!important;overflow-y:scroll}.treasure-report-component[_ngcontent-%COMP%]     .infinite-scroll-fixed-shortened{max-height:55vh!important;min-height:55vh!important}.treasure-report-component[_ngcontent-%COMP%]   .ta-r[_ngcontent-%COMP%]{text-align:right!important}.treasure-report-component[_ngcontent-%COMP%]   .ta-l[_ngcontent-%COMP%]{text-align:left}.treasure-report-component[_ngcontent-%COMP%]   .is-normal-probability-text[_ngcontent-%COMP%]{color:grey!important}.treasure-report-component[_ngcontent-%COMP%]   .is-low-probability-text[_ngcontent-%COMP%]{color:#cf0001!important}.treasure-report-component[_ngcontent-%COMP%]   .is-moderate-probability-text[_ngcontent-%COMP%]{color:#ff7200!important}.treasure-report-component[_ngcontent-%COMP%]   .is-high-probability-text[_ngcontent-%COMP%]{color:#009432!important}.treasure-report-component[_ngcontent-%COMP%]   .is-normal-probability[_ngcontent-%COMP%]{border-left-color:grey!important}.treasure-report-component[_ngcontent-%COMP%]   .is-low-probability[_ngcontent-%COMP%]{border-left-color:#cf0001!important}.treasure-report-component[_ngcontent-%COMP%]   .is-moderate-probability[_ngcontent-%COMP%]{border-left-color:#ff7200!important}.treasure-report-component[_ngcontent-%COMP%]   .is-high-probability[_ngcontent-%COMP%]{border-left-color:#009432!important}.treasure-report-component[_ngcontent-%COMP%]   .value13Bold[_ngcontent-%COMP%]{font-size:13px!important}.treasure-report-component[_ngcontent-%COMP%]   .value13Bold[_ngcontent-%COMP%], .treasure-report-component[_ngcontent-%COMP%]   .value14Bold[_ngcontent-%COMP%]{color:#000;font-weight:500!important;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.treasure-report-component[_ngcontent-%COMP%]   .value14Bold[_ngcontent-%COMP%]{font-size:14px!important}.treasure-report-component[_ngcontent-%COMP%]   .listcard[_ngcontent-%COMP%]{transition:all .25s linear;height:auto}.treasure-report-component[_ngcontent-%COMP%]   .smallSubtleText[_ngcontent-%COMP%]{color:#66615b;font-size:11px}.treasure-report-component[_ngcontent-%COMP%]   .data-type-card[_ngcontent-%COMP%]{min-height:85px;transition:all .3s linear}.treasure-report-component[_ngcontent-%COMP%]   .data-type-card-is-active[_ngcontent-%COMP%]{border-color:#cf0001;background-color:#fff2f2}.treasure-report-component[_ngcontent-%COMP%]   .headingBold[_ngcontent-%COMP%]{color:#1a1a1a;font-weight:600;font-size:22px}.treasure-report-component[_ngcontent-%COMP%]   .valueGrey14[_ngcontent-%COMP%]{font-size:14px;font-weight:300}.treasure-report-component[_ngcontent-%COMP%]   .valueGrey12Normal[_ngcontent-%COMP%], .treasure-report-component[_ngcontent-%COMP%]   .valueGrey14[_ngcontent-%COMP%]{color:#4a4a4a;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.treasure-report-component[_ngcontent-%COMP%]   .valueGrey12Normal[_ngcontent-%COMP%]{font-size:12px;font-weight:400}.treasure-report-component[_ngcontent-%COMP%]   .cp[_ngcontent-%COMP%]{cursor:pointer!important}.treasure-report-component[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]{margin-top:5px!important;line-height:8px;width:26px;height:26px;margin-right:10px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12);animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}.treasure-report-component[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%], .treasure-report-component[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.treasure-report-component[_ngcontent-%COMP%]   .ta-c[_ngcontent-%COMP%]{text-align:center;padding-left:0!important;padding-right:0!important}.treasure-report-component[_ngcontent-%COMP%]   .divAlignItemCenter[_ngcontent-%COMP%]{display:flex;align-items:center}.treasure-report-component[_ngcontent-%COMP%]   .menu-icons[_ngcontent-%COMP%]{font-size:4px}.treasure-report-component[_ngcontent-%COMP%]   .example-container[_ngcontent-%COMP%]{width:400px;max-width:100%;margin:0 25px 25px 0;display:inline-block;vertical-align:top}.treasure-report-component[_ngcontent-%COMP%]   .example-list[_ngcontent-%COMP%]{border:1px solid #ccc;min-height:60px;background:#fff;border-radius:4px;overflow:hidden;display:block}.treasure-report-component[_ngcontent-%COMP%]     .example-box{border-bottom:1px solid #ccc;color:rgba(0,0,0,.87);display:flex;flex-direction:row;align-items:left;justify-content:space-between;box-sizing:border-box;cursor:move;background:#fff;font-size:4px}.treasure-report-component[_ngcontent-%COMP%]     .menu-list{cursor:move;font-size:8px}.treasure-report-component[_ngcontent-%COMP%]   .cdk-drag-preview[_ngcontent-%COMP%]{box-sizing:border-box;border-radius:4px;box-shadow:0 5px 5px -3px rgba(0,0,0,.2),0 8px 10px 1px rgba(0,0,0,.14),0 3px 14px 2px rgba(0,0,0,.12)}.treasure-report-component[_ngcontent-%COMP%]   .cdk-drag-placeholder[_ngcontent-%COMP%]{opacity:0}.treasure-report-component[_ngcontent-%COMP%]   .cdk-drag-animating[_ngcontent-%COMP%]{transition:transform .25s cubic-bezier(0,0,.2,1)}.treasure-report-component[_ngcontent-%COMP%]   .example-box[_ngcontent-%COMP%]:last-child{border:none}.treasure-report-component[_ngcontent-%COMP%]   .example-list.cdk-drop-list-dragging[_ngcontent-%COMP%]   .example-box[_ngcontent-%COMP%]:not(.cdk-drag-placeholder){transition:transform .25s cubic-bezier(0,0,.2,1)}"],data:{animation:[Object(c.o)("slideInOut",[Object(c.l)("in",Object(c.m)({height:"*",overflow:"hidden"})),Object(c.l)("out",Object(c.m)({height:0,overflow:"hidden"})),Object(c.n)("* => in",[Object(c.m)({height:0}),Object(c.e)(250,Object(c.m)({height:"*"}))]),Object(c.n)("in=> *",[Object(c.m)({height:"*"}),Object(c.e)(250,Object(c.m)({height:0}))])]),Object(c.o)("smallCardAnimation",[Object(c.n)("* => *",[Object(c.i)(":leave",[Object(c.k)(100,[Object(c.e)("0.5s",Object(c.m)({opacity:0}))])],{optional:!0}),Object(c.i)(":enter",[Object(c.m)({opacity:0}),Object(c.k)(100,[Object(c.e)("0.5s",Object(c.m)({opacity:1}))])],{optional:!0})])])]}}),e})()}];let _=(()=>{class e{}return e.\u0275mod=u["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=u["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[n.k.forChild(v)],n.k]}),e})();var S=i("Xi0T");let O=(()=>{class e{}return e.\u0275mod=u["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=u["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[r.CommonModule,_,S.a]]}),e})()}}]);