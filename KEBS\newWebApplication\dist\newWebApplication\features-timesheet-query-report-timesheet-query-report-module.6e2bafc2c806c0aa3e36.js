(window.webpackJsonp=window.webpackJsonp||[]).push([[678],{PZeg:function(e,t,n){"use strict";n.r(t),n.d(t,"TimesheetQueryReportModule",(function(){return J}));var o=n("ofXK"),i=n("tyNb"),a=n("mrSG"),l=n("XNiG"),r=n("1G5W"),d=n("wd/R"),m=n("xG9w"),c=n("FKr1"),s=n("1yaQ"),p=n("3Pt+"),u=n("EUZL"),h=n("Iab2"),x=n("fXoL"),v=n("tk/3");let f=(()=>{class e{constructor(e){this._http=e}getTimesheetAdminReport(e,t,n,o,i,a){return this._http.post("/api/tsPrimary/getQueryReportForTimesheet",{operation:e,startDate:t,endDate:n,aid:o,inactive:i,autoTs:a})}getTimesheetProperties(e){return this._http.post("/api/tsPrimary/getTimesheetProperties",{associate_id:e})}}return e.\u0275fac=function(t){return new(t||e)(x["\u0275\u0275inject"](v.c))},e.\u0275prov=x["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var g=n("HmYF"),w=n("1A3m"),y=n("BVzC"),S=n("XXEo"),D=n("kmnG"),b=n("d3UM"),C=n("bTqV"),M=n("qFsG"),E=n("iadO"),Y=n("bSwM"),I=n("1jcm"),F=n("ZzPI"),T=n("6t9p");function R(e,t){if(1&e){const e=x["\u0275\u0275getCurrentView"]();x["\u0275\u0275elementStart"](0,"mat-option",13),x["\u0275\u0275listener"]("click",(function(){x["\u0275\u0275restoreView"](e);const n=t.$implicit;return x["\u0275\u0275nextContext"]().callToCheckDateComponent(n)})),x["\u0275\u0275text"](1),x["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;x["\u0275\u0275property"]("value",e.value),x["\u0275\u0275advance"](1),x["\u0275\u0275textInterpolate"](e.report_name)}}function N(e,t){if(1&e){const e=x["\u0275\u0275getCurrentView"]();x["\u0275\u0275elementStart"](0,"div",14),x["\u0275\u0275elementStart"](1,"mat-form-field",4),x["\u0275\u0275elementStart"](2,"mat-label"),x["\u0275\u0275text"](3,"Choose Month"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275element"](4,"input",15),x["\u0275\u0275element"](5,"mat-datepicker-toggle",16),x["\u0275\u0275elementStart"](6,"mat-datepicker",17,18),x["\u0275\u0275listener"]("monthSelected",(function(t){x["\u0275\u0275restoreView"](e);const n=x["\u0275\u0275reference"](7);return x["\u0275\u0275nextContext"]().setMonthAndYear(t,n)})),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()}if(2&e){const e=x["\u0275\u0275reference"](7),t=x["\u0275\u0275nextContext"]();x["\u0275\u0275advance"](4),x["\u0275\u0275property"]("matDatepicker",e)("formControl",t.dateControl),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("for",e)}}function _(e,t){if(1&e){const e=x["\u0275\u0275getCurrentView"]();x["\u0275\u0275elementStart"](0,"div",19),x["\u0275\u0275elementStart"](1,"section",20),x["\u0275\u0275elementStart"](2,"mat-checkbox",21),x["\u0275\u0275listener"]("ngModelChange",(function(t){return x["\u0275\u0275restoreView"](e),x["\u0275\u0275nextContext"]().inactive=t})),x["\u0275\u0275text"](3,"Inactive & Exit Employees"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()}if(2&e){const e=x["\u0275\u0275nextContext"]();x["\u0275\u0275advance"](2),x["\u0275\u0275property"]("ngModel",e.inactive)}}function k(e,t){if(1&e){const e=x["\u0275\u0275getCurrentView"]();x["\u0275\u0275elementStart"](0,"div",22),x["\u0275\u0275elementStart"](1,"section",20),x["\u0275\u0275elementStart"](2,"mat-slide-toggle",5),x["\u0275\u0275listener"]("ngModelChange",(function(t){return x["\u0275\u0275restoreView"](e),x["\u0275\u0275nextContext"]().autoTS=t})),x["\u0275\u0275text"](3," Auto TS Employees "),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()}if(2&e){const e=x["\u0275\u0275nextContext"]();x["\u0275\u0275advance"](2),x["\u0275\u0275property"]("ngModel",e.autoTS)}}function P(e,t){if(1&e&&(x["\u0275\u0275elementStart"](0,"div",23),x["\u0275\u0275elementStart"](1,"dx-data-grid",24,25),x["\u0275\u0275element"](3,"dxo-filter-row",26),x["\u0275\u0275element"](4,"dxo-header-filter",26),x["\u0275\u0275element"](5,"dxo-column-chooser",27),x["\u0275\u0275element"](6,"dxo-scrolling",28),x["\u0275\u0275element"](7,"dxi-column",29),x["\u0275\u0275element"](8,"dxi-column",30),x["\u0275\u0275element"](9,"dxi-column",31),x["\u0275\u0275element"](10,"dxi-column",32),x["\u0275\u0275element"](11,"dxi-column",33),x["\u0275\u0275element"](12,"dxi-column",34),x["\u0275\u0275element"](13,"dxi-column",35),x["\u0275\u0275element"](14,"dxi-column",36),x["\u0275\u0275element"](15,"dxi-column",37),x["\u0275\u0275element"](16,"dxi-column",38),x["\u0275\u0275element"](17,"dxi-column",39),x["\u0275\u0275element"](18,"dxi-column",40),x["\u0275\u0275element"](19,"dxi-column",41),x["\u0275\u0275element"](20,"dxi-column",42),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()),2&e){const e=x["\u0275\u0275nextContext"]();x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("height",e.dynamicGridReportHeight)("dataSource",e.downloadData)("showBorders",!0)("columnAutoWidth",!0)("showColumnLines",!0)("showRowLines",!0),x["\u0275\u0275advance"](2),x["\u0275\u0275property"]("visible",!0),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("visible",!0),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("allowSearch",!0)("enabled",!0)}}function L(e,t){if(1&e&&(x["\u0275\u0275elementStart"](0,"div",23),x["\u0275\u0275elementStart"](1,"dx-data-grid",24,25),x["\u0275\u0275element"](3,"dxo-filter-row",26),x["\u0275\u0275element"](4,"dxo-header-filter",26),x["\u0275\u0275element"](5,"dxo-column-chooser",27),x["\u0275\u0275element"](6,"dxo-scrolling",28),x["\u0275\u0275element"](7,"dxi-column",29),x["\u0275\u0275element"](8,"dxi-column",31),x["\u0275\u0275element"](9,"dxi-column",33),x["\u0275\u0275element"](10,"dxi-column",34),x["\u0275\u0275element"](11,"dxi-column",35),x["\u0275\u0275element"](12,"dxi-column",36),x["\u0275\u0275element"](13,"dxi-column",37),x["\u0275\u0275element"](14,"dxi-column",43),x["\u0275\u0275element"](15,"dxi-column",44),x["\u0275\u0275element"](16,"dxi-column",45),x["\u0275\u0275element"](17,"dxi-column",46),x["\u0275\u0275element"](18,"dxi-column",47),x["\u0275\u0275element"](19,"dxi-column",48),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()),2&e){const e=x["\u0275\u0275nextContext"]();x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("height",e.dynamicGridReportHeight)("dataSource",e.downloadData)("showBorders",!0)("columnAutoWidth",!0)("showColumnLines",!0)("showRowLines",!0),x["\u0275\u0275advance"](2),x["\u0275\u0275property"]("visible",!0),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("visible",!0),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("allowSearch",!0)("enabled",!0)}}function O(e,t){if(1&e&&(x["\u0275\u0275elementStart"](0,"div",23),x["\u0275\u0275elementStart"](1,"dx-data-grid",24,25),x["\u0275\u0275element"](3,"dxo-filter-row",26),x["\u0275\u0275element"](4,"dxo-header-filter",26),x["\u0275\u0275element"](5,"dxo-column-chooser",27),x["\u0275\u0275element"](6,"dxo-scrolling",28),x["\u0275\u0275element"](7,"dxi-column",29),x["\u0275\u0275element"](8,"dxi-column",31),x["\u0275\u0275element"](9,"dxi-column",33),x["\u0275\u0275element"](10,"dxi-column",34),x["\u0275\u0275element"](11,"dxi-column",35),x["\u0275\u0275element"](12,"dxi-column",36),x["\u0275\u0275element"](13,"dxi-column",37),x["\u0275\u0275element"](14,"dxi-column",43),x["\u0275\u0275element"](15,"dxi-column",44),x["\u0275\u0275element"](16,"dxi-column",45),x["\u0275\u0275element"](17,"dxi-column",46),x["\u0275\u0275element"](18,"dxi-column",49),x["\u0275\u0275element"](19,"dxi-column",50),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()),2&e){const e=x["\u0275\u0275nextContext"]();x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("height",e.dynamicGridReportHeight)("dataSource",e.downloadData)("showBorders",!0)("columnAutoWidth",!0)("showColumnLines",!0)("showRowLines",!0),x["\u0275\u0275advance"](2),x["\u0275\u0275property"]("visible",!0),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("visible",!0),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("allowSearch",!0)("enabled",!0)}}function A(e,t){if(1&e&&(x["\u0275\u0275elementStart"](0,"div",23),x["\u0275\u0275elementStart"](1,"dx-data-grid",24,25),x["\u0275\u0275element"](3,"dxo-filter-row",26),x["\u0275\u0275element"](4,"dxo-header-filter",26),x["\u0275\u0275element"](5,"dxo-column-chooser",27),x["\u0275\u0275element"](6,"dxo-scrolling",28),x["\u0275\u0275element"](7,"dxi-column",48),x["\u0275\u0275element"](8,"dxi-column",29),x["\u0275\u0275element"](9,"dxi-column",31),x["\u0275\u0275element"](10,"dxi-column",33),x["\u0275\u0275element"](11,"dxi-column",34),x["\u0275\u0275element"](12,"dxi-column",35),x["\u0275\u0275element"](13,"dxi-column",36),x["\u0275\u0275element"](14,"dxi-column",37),x["\u0275\u0275element"](15,"dxi-column",43),x["\u0275\u0275element"](16,"dxi-column",44),x["\u0275\u0275element"](17,"dxi-column",51),x["\u0275\u0275element"](18,"dxi-column",52),x["\u0275\u0275element"](19,"dxi-column",46),x["\u0275\u0275element"](20,"dxi-column",53),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()),2&e){const e=x["\u0275\u0275nextContext"]();x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("height",e.dynamicGridReportHeight)("dataSource",e.downloadData)("showBorders",!0)("columnAutoWidth",!0)("showColumnLines",!0)("showRowLines",!0),x["\u0275\u0275advance"](2),x["\u0275\u0275property"]("visible",!0),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("visible",!0),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("allowSearch",!0)("enabled",!0)}}function j(e,t){if(1&e&&(x["\u0275\u0275elementStart"](0,"div",23),x["\u0275\u0275elementStart"](1,"dx-data-grid",24,25),x["\u0275\u0275element"](3,"dxo-filter-row",26),x["\u0275\u0275element"](4,"dxo-header-filter",26),x["\u0275\u0275element"](5,"dxo-column-chooser",27),x["\u0275\u0275element"](6,"dxo-scrolling",28),x["\u0275\u0275element"](7,"dxi-column",29),x["\u0275\u0275element"](8,"dxi-column",31),x["\u0275\u0275element"](9,"dxi-column",33),x["\u0275\u0275element"](10,"dxi-column",34),x["\u0275\u0275element"](11,"dxi-column",54),x["\u0275\u0275element"](12,"dxi-column",55),x["\u0275\u0275element"](13,"dxi-column",56),x["\u0275\u0275element"](14,"dxi-column",43),x["\u0275\u0275element"](15,"dxi-column",44),x["\u0275\u0275element"](16,"dxi-column",45),x["\u0275\u0275element"](17,"dxi-column",47),x["\u0275\u0275element"](18,"dxi-column",48),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()),2&e){const e=x["\u0275\u0275nextContext"]();x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("height",e.dynamicGridReportHeight)("dataSource",e.downloadData)("showBorders",!0)("columnAutoWidth",!0)("showColumnLines",!0)("showRowLines",!0),x["\u0275\u0275advance"](2),x["\u0275\u0275property"]("visible",!0),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("visible",!0),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("allowSearch",!0)("enabled",!0)}}function W(e,t){if(1&e&&(x["\u0275\u0275elementStart"](0,"div",23),x["\u0275\u0275elementStart"](1,"dx-data-grid",24,25),x["\u0275\u0275element"](3,"dxo-filter-row",26),x["\u0275\u0275element"](4,"dxo-header-filter",26),x["\u0275\u0275element"](5,"dxo-column-chooser",27),x["\u0275\u0275element"](6,"dxo-scrolling",28),x["\u0275\u0275element"](7,"dxi-column",29),x["\u0275\u0275element"](8,"dxi-column",31),x["\u0275\u0275element"](9,"dxi-column",33),x["\u0275\u0275element"](10,"dxi-column",34),x["\u0275\u0275element"](11,"dxi-column",35),x["\u0275\u0275element"](12,"dxi-column",36),x["\u0275\u0275element"](13,"dxi-column",37),x["\u0275\u0275element"](14,"dxi-column",43),x["\u0275\u0275element"](15,"dxi-column",44),x["\u0275\u0275element"](16,"dxi-column",45),x["\u0275\u0275element"](17,"dxi-column",46),x["\u0275\u0275element"](18,"dxi-column",49),x["\u0275\u0275element"](19,"dxi-column",47),x["\u0275\u0275element"](20,"dxi-column",57),x["\u0275\u0275element"](21,"dxi-column",58),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"]()),2&e){const e=x["\u0275\u0275nextContext"]();x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("height",e.dynamicGridReportHeight)("dataSource",e.downloadData)("showBorders",!0)("columnAutoWidth",!0)("showColumnLines",!0)("showRowLines",!0),x["\u0275\u0275advance"](2),x["\u0275\u0275property"]("visible",!0),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("visible",!0),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("allowSearch",!0)("enabled",!0)}}const H={parse:{dateInput:"MMMM YYYY"},display:{dateInput:"MMMM YYYY",monthYearLabel:"MMMM YYYY",dateA11yLabel:"LL",monthYearA11yLabel:"MMMM YYYY"}},V=[{path:"",component:(()=>{class e{constructor(e,t,n,o,i){this._tsQueryReportService=e,this._excelToJsonService=t,this._toasterService=n,this._errorService=o,this._loginService=i,this.selectedReport=0,this._onDestroy=new l.b,this.report=[],this.dateControl=new p.j(d()),this.buttonDisable=!1,this.isDateComponentNeedToShown=!0,this.inactive=!1,this.autoTS=!1,this.isCheckboxNeedToShown=!0,this.downloadData=[]}ngOnInit(){this.currentUser=this._loginService.getProfile().profile,this.getTimesheetProperties()}downloadReport(){this.buttonDisable=!0;let e="END"==this.tsProperties.month_end_date?d(this.dateControl.value).endOf("month").format("DD"):this.tsProperties.month_end_date;this.isDateComponentNeedToShown?(this.endDate=d([d(this.selectedDate).year(),d(this.selectedDate).month(),e]).format("YYYY-MM-DD"),this.startDate="END"==this.tsProperties.month_end_date?d(this.selectedDate).startOf("month").format("YYYY-MM-DD"):d(this.endDate).subtract(1,"month").add(1,"days").format("YYYY-MM-DD")):(this.startDate=d().format("YYYY-MM-DD"),this.endDate=d().format("YYYY-MM-DD")),this._tsQueryReportService.getTimesheetAdminReport(this.selectedReport,this.startDate,this.endDate,this.currentUser.aid,this.inactive,this.autoTS).pipe(Object(r.a)(this._onDestroy)).subscribe(e=>Object(a.c)(this,void 0,void 0,(function*(){"S"==e.messType&&e.data&&e&&e.data.length>0?(this.downloadData=e.data,this.exportToExcel(e.data,e.reportName),this.buttonDisable=!1):(this._toasterService.showInfo("Timesheet Admin Report","No Data Found",3e3),this.buttonDisable=!1)})),e=>{this.buttonDisable=!1,this._errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Report Download Failed, Kindly Try After Sometime",e&&e.params?e.params:e&&e.error?e.error.params:{})})}getTimesheetProperties(){this._tsQueryReportService.getTimesheetProperties(this.currentUser.aid).pipe(Object(r.a)(this._onDestroy)).subscribe(e=>Object(a.c)(this,void 0,void 0,(function*(){"S"==e.messType&&e.data.length>0&&(this.tsProperties=e.data[0],this.report=JSON.parse(this.tsProperties.timesheet_query_report),this.report=m.default.filter(this.report,{isActive:1}))})),e=>{this._errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Something went wrong, Kindly Try After Sometime",e&&e.params?e.params:e&&e.error?e.error.params:{})})}setMonthAndYear(e,t){const n=this.dateControl.value;n.month(e.month()),n.year(e.year()),this.dateControl.setValue(n),this.selectedDate=d(this.dateControl.value).format("YYYY-MM-DD"),t.close()}callToCheckDateComponent(e){this.downloadData=[],this.isDateComponentNeedToShown=e.isdateComponentNeeded,this.isCheckboxNeedToShown=e.isCheckboxNeedToShown}exportToExcel(e,t){const n=u.utils.json_to_sheet(e);n["!cols"]=[{wch:20},{wch:20},Object.assign({wch:20},{t:"d",z:"dd-mm-yyyy"})];const o=u.write({Sheets:{data:n},SheetNames:["data"]},{bookType:"xlsx",type:"array"}),i=new Blob([o],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"});Object(h.saveAs)(i,t+".xlsx")}}return e.\u0275fac=function(t){return new(t||e)(x["\u0275\u0275directiveInject"](f),x["\u0275\u0275directiveInject"](g.a),x["\u0275\u0275directiveInject"](w.a),x["\u0275\u0275directiveInject"](y.a),x["\u0275\u0275directiveInject"](S.a))},e.\u0275cmp=x["\u0275\u0275defineComponent"]({type:e,selectors:[["app-timesheet-query-report-landing-page"]],features:[x["\u0275\u0275ProvidersFeature"]([{provide:c.c,useClass:s.c,deps:[c.f,s.a]},{provide:c.e,useValue:H}])],decls:21,vars:12,consts:[[1,"p-0","container-fluid","ts-admin-persona"],[1,"col-12","p-2"],[1,"row"],[1,"col-2"],["appearance","outline"],[3,"ngModel","ngModelChange"],[3,"value","click",4,"ngFor","ngForOf"],["class","col-2 pl-4",4,"ngIf"],["class","col-2 pl-4 row",4,"ngIf"],["class","col-2 pl-5 row",4,"ngIf"],[1,"col-2","pl-5"],["mat-raised-button","","color","primary",3,"disabled","click"],["class","col-12",4,"ngIf"],[3,"value","click"],[1,"col-2","pl-4"],["matInput","","placeholder","MMMM-YYYY",3,"matDatepicker","formControl"],["matIconSuffix","",3,"for"],["startView","multi-year",3,"monthSelected"],["dp",""],[1,"col-2","pl-4","row"],[1,"example-section"],[1,"example-margin",3,"ngModel","ngModelChange"],[1,"col-2","pl-5","row"],[1,"col-12"],[1,"data-grid",3,"height","dataSource","showBorders","columnAutoWidth","showColumnLines","showRowLines"],["dataGrid",""],[3,"visible"],["mode","select",3,"allowSearch","enabled"],["mode","infinite"],["caption","Employee Id","dataField","Employee Id"],["caption","Microsoft O365 Id","dataField","Microsoft O365 Id"],["caption","Employee Name","dataField","Employee Name"],["caption","Date Of Joining","dataField","Date Of Joining"],["caption","Employment Type","dataField","Employment Type"],["caption","Employment Status","dataField","Employment Status"],["caption","Entity","dataField","Entity"],["caption","Division","dataField","Division"],["caption","Sub Division","dataField","Sub Division"],["caption","Timesheet Status","dataField","Timesheet Status"],["caption","Timesheet Month & Year","dataField","Timesheet Month & Year"],["caption","Week Status","dataField","Week Status"],["caption","Direct Manager","dataField","Direct Manager"],["caption","Approvers","dataField","Approvers"],["caption","Charge Code","dataField","Charge Code"],["caption","Charge Code Description","dataField","Charge Code Description"],["caption","Location","dataField","Location"],["caption","Year","dataField","Year"],["caption","Working Hours","dataField","Working Hours"],["caption","Date","dataField","Date"],["caption","Work Schedule","dataField","Work Schedule"],["caption","Utilization Percentage","dataField","Utilization Percentage"],["caption","Hours","dataField","Hours"],["caption","Month","dataField","Month"],["caption","Holiday Name","dataField","Holiday Name"],["caption","Entity Name","dataField","Entity Name"],["caption","Division Name","dataField","Division Name"],["caption","Sub Division Name","dataField","Sub Division Name"],["caption","MIS Working Hours","dataField","MIS Working Hours"],["caption","MIS Utilization Percentage","dataField","MIS Utilization Percentage"]],template:function(e,t){1&e&&(x["\u0275\u0275elementStart"](0,"div",0),x["\u0275\u0275elementStart"](1,"div",1),x["\u0275\u0275elementStart"](2,"div",2),x["\u0275\u0275elementStart"](3,"div",3),x["\u0275\u0275elementStart"](4,"mat-form-field",4),x["\u0275\u0275elementStart"](5,"mat-label"),x["\u0275\u0275text"](6,"Select Report"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementStart"](7,"mat-select",5),x["\u0275\u0275listener"]("ngModelChange",(function(e){return t.selectedReport=e})),x["\u0275\u0275template"](8,R,2,2,"mat-option",6),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275template"](9,N,8,3,"div",7),x["\u0275\u0275template"](10,_,4,1,"div",8),x["\u0275\u0275template"](11,k,4,1,"div",9),x["\u0275\u0275elementStart"](12,"div",10),x["\u0275\u0275elementStart"](13,"button",11),x["\u0275\u0275listener"]("click",(function(){return t.downloadReport()})),x["\u0275\u0275text"](14,"View and Download Report"),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275elementEnd"](),x["\u0275\u0275template"](15,P,21,10,"div",12),x["\u0275\u0275template"](16,L,20,10,"div",12),x["\u0275\u0275template"](17,O,20,10,"div",12),x["\u0275\u0275template"](18,A,21,10,"div",12),x["\u0275\u0275template"](19,j,19,10,"div",12),x["\u0275\u0275template"](20,W,22,10,"div",12),x["\u0275\u0275elementEnd"]()),2&e&&(x["\u0275\u0275advance"](7),x["\u0275\u0275property"]("ngModel",t.selectedReport),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngForOf",t.report),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",t.isDateComponentNeedToShown),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",t.isCheckboxNeedToShown),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",t.isCheckboxNeedToShown),x["\u0275\u0275advance"](2),x["\u0275\u0275property"]("disabled",t.buttonDisable),x["\u0275\u0275advance"](2),x["\u0275\u0275property"]("ngIf",1==t.selectedReport&&(null==t.downloadData?null:t.downloadData.length)>0),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",2==t.selectedReport&&(null==t.downloadData?null:t.downloadData.length)>0),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",3==t.selectedReport&&(null==t.downloadData?null:t.downloadData.length)>0),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",4==t.selectedReport&&(null==t.downloadData?null:t.downloadData.length)>0),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",5==t.selectedReport&&(null==t.downloadData?null:t.downloadData.length)>0),x["\u0275\u0275advance"](1),x["\u0275\u0275property"]("ngIf",6==t.selectedReport&&(null==t.downloadData?null:t.downloadData.length)>0))},directives:[D.c,D.g,b.c,p.v,p.y,o.NgForOf,o.NgIf,C.a,c.p,M.b,E.g,p.e,p.k,E.i,E.f,Y.a,I.a,F.a,T.dc,T.Cc,T.tb,T.Jd,T.g],styles:[".ts-admin-persona[_ngcontent-%COMP%]     .mat-form-field-infix{display:flex;height:40px}.ts-admin-persona[_ngcontent-%COMP%]     .mat-icon-button{bottom:15px}.ts-admin-persona[_ngcontent-%COMP%]   .example-section[_ngcontent-%COMP%]{margin:12px 0}.ts-admin-persona[_ngcontent-%COMP%]   .example-margin[_ngcontent-%COMP%]{margin:0 12px}"]}),e})()}];let G=(()=>{class e{}return e.\u0275mod=x["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=x["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.k.forChild(V)],i.k]}),e})();var B=n("NFeN"),U=n("Qu3c"),z=n("PVOt");let J=(()=>{class e{}return e.\u0275mod=x["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=x["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[o.CommonModule,G,b.d,D.e,C.b,B.b,U.b,M.c,E.h,p.p,p.E,Y.b,I.b,F.b,z.f]]}),e})()}}]);