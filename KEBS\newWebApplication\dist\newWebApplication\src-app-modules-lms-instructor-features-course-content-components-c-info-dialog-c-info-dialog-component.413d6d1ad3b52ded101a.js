(window.webpackJsonp=window.webpackJsonp||[]).push([[960],{"8b9G":function(e,t,n){"use strict";n.r(t),n.d(t,"CInfoDialogComponent",(function(){return b}));var r=n("3Pt+"),o=n("0IaG"),i=n("fXoL"),a=n("OWx6"),l=n("bTqV"),s=n("NFeN"),c=n("f0Cb"),m=n("kmnG"),u=n("qFsG"),d=n("ofXK");function p(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",19),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const t=i["\u0275\u0275nextContext"]().index;return i["\u0275\u0275nextContext"]().removeCourseGain(t)})),i["\u0275\u0275elementStart"](1,"mat-icon",20),i["\u0275\u0275text"](2,"remove_circle_outline"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}}function f(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",21),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"](2).addCourseGain("")})),i["\u0275\u0275elementStart"](1,"mat-icon",20),i["\u0275\u0275text"](2,"add_circle_outline"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}}function g(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",12),i["\u0275\u0275elementStart"](1,"div",13),i["\u0275\u0275elementStart"](2,"span",14),i["\u0275\u0275text"](3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"mat-form-field",15),i["\u0275\u0275element"](5,"input",16),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](6,p,3,0,"button",17),i["\u0275\u0275template"](7,f,3,0,"button",18),i["\u0275\u0275elementEnd"]()),2&e){const e=t.index,n=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("formGroupName",e),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate"](e+1),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngIf",0!=e||e!=n.courseForm.get("courseGain").controls.length-1),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",e==n.courseForm.get("courseGain").controls.length-1)}}function h(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",19),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const t=i["\u0275\u0275nextContext"]().index;return i["\u0275\u0275nextContext"]().removePrerequisites(t)})),i["\u0275\u0275elementStart"](1,"mat-icon",20),i["\u0275\u0275text"](2,"remove_circle_outline"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}}function x(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",21),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"](2).addPrerequisites()})),i["\u0275\u0275elementStart"](1,"mat-icon",20),i["\u0275\u0275text"](2,"add_circle_outline"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}}function v(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",22),i["\u0275\u0275elementStart"](1,"div",13),i["\u0275\u0275elementStart"](2,"span",14),i["\u0275\u0275text"](3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"mat-form-field",15),i["\u0275\u0275element"](5,"input",16),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](6,h,3,0,"button",17),i["\u0275\u0275template"](7,x,3,0,"button",18),i["\u0275\u0275elementEnd"]()),2&e){const e=t.index,n=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("formGroupName",e),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate"](e+1),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngIf",0!=e||e!=n.courseForm.get("coursePrerequisites").controls.length-1),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",e==n.courseForm.get("coursePrerequisites").controls.length-1)}}let b=(()=>{class e{constructor(e,t,n,r){this.matDialogRef=e,this._LmsInstructorService=t,this.data=n,this.fb=r}ngOnInit(){console.log("md",this.data),this.patchForm()}patchForm(){this.courseForm=this.fb.group({courseTitle:[this.data.courseTitle,r.H.required],courseAudience:[this.data.courseAudience,r.H.required],courseGain:this.fb.array([]),coursePrerequisites:this.fb.array([])}),this.data.courseGain.forEach(e=>this.addCourseGain(e)),this.data.coursePrerequisites.forEach(e=>this.addPrerequisites(e)),console.log(this.courseForm.value)}addCourseGain(e){this.courseForm.get("courseGain").push(this.fb.group({label:[e,r.H.required]}))}removeCourseGain(e){this.courseForm.get("courseGain").removeAt(e)}addPrerequisites(e){this.courseForm.get("coursePrerequisites").push(this.fb.group({label:[e,r.H.required]}))}removePrerequisites(e){this.courseForm.get("coursePrerequisites").removeAt(e)}updateForm(){this.courseForm.invalid?this._LmsInstructorService.showSnack("Kindly fill the required fields."):(console.log(this.courseForm),this.matDialogRef.close(this.courseForm.value))}closeDialog(){this.matDialogRef.close()}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](o.h),i["\u0275\u0275directiveInject"](a.a),i["\u0275\u0275directiveInject"](o.a),i["\u0275\u0275directiveInject"](r.i))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-c-info-dialog"]],decls:27,vars:3,consts:[[1,"row","justify-content-between","align-items-center","p-1"],[1,"pl-2",2,"color","#cf0000"],["mat-icon-button","",3,"click"],[1,"m-3","form-styles","slide-in-top",3,"formGroup"],[1,"form-title","pt-0"],["appearance","outline"],["type","text","matInput","","placeholder","Title Ex : Understanding UX","formControlName","courseTitle"],[1,"form-title"],["type","text","matInput","","placeholder","Eg: This course is for everyone interested in putting existing Marketing ideas","formControlName","courseAudience"],["formArrayName","courseGain","class","position-relative",4,"ngFor","ngForOf"],["formArrayName","coursePrerequisites","class","position-relative",4,"ngFor","ngForOf"],["matStepperNext","","mat-icon-button","",1,"cinfo-next-btn",3,"click"],["formArrayName","courseGain",1,"position-relative"],[3,"formGroupName"],[1,"circle","mr-3"],["appearance","outline",1,"slide-in-top",2,"width","83% !important"],["type","text","matInput","","placeholder","Title","formControlName","label"],["mat-icon-button","","class","addCourseGain",3,"click",4,"ngIf"],["style","left: calc(85% + 75px)","mat-icon-button","","class","addCourseGain",3,"click",4,"ngIf"],["mat-icon-button","",1,"addCourseGain",3,"click"],[2,"font-size","18px"],["mat-icon-button","",1,"addCourseGain",2,"left","calc(85% + 75px)",3,"click"],["formArrayName","coursePrerequisites",1,"position-relative"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275elementStart"](1,"strong",1),i["\u0275\u0275text"](2,"Edit course"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](3,"div"),i["\u0275\u0275elementStart"](4,"button",2),i["\u0275\u0275listener"]("click",(function(){return t.closeDialog()})),i["\u0275\u0275elementStart"](5,"mat-icon"),i["\u0275\u0275text"](6,"close"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](7,"mat-divider"),i["\u0275\u0275elementStart"](8,"div",3),i["\u0275\u0275elementStart"](9,"div",4),i["\u0275\u0275text"](10," What's course title ? "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](11,"mat-form-field",5),i["\u0275\u0275element"](12,"input",6),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](13,"div",7),i["\u0275\u0275text"](14," Who this course is for ? "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](15,"mat-form-field",5),i["\u0275\u0275element"](16,"input",8),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](17,"div",7),i["\u0275\u0275text"](18," What learners will learn when they take your course ? "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](19,g,8,4,"div",9),i["\u0275\u0275elementStart"](20,"div",7),i["\u0275\u0275text"](21," Is there any prerequisites ? "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](22,v,8,4,"div",10),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](23,"div"),i["\u0275\u0275elementStart"](24,"button",11),i["\u0275\u0275listener"]("click",(function(){return t.updateForm()})),i["\u0275\u0275elementStart"](25,"mat-icon"),i["\u0275\u0275text"](26,"done_all"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](8),i["\u0275\u0275property"]("formGroup",t.courseForm),i["\u0275\u0275advance"](11),i["\u0275\u0275property"]("ngForOf",t.courseForm.get("courseGain").controls),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngForOf",t.courseForm.get("coursePrerequisites").controls))},directives:[l.a,s.a,c.a,r.w,r.n,m.c,u.b,r.e,r.v,r.l,d.NgForOf,r.h,r.o,d.NgIf],styles:[".form-styles[_ngcontent-%COMP%]{overflow:auto;height:calc(100vh - 170px);scrollbar-width:none}.form-styles[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{width:calc(83% + 40px)!important}.form-styles[_ngcontent-%COMP%]::-webkit-scrollbar{display:none}.form-title[_ngcontent-%COMP%]{font-size:15px;max-width:93%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;font-weight:500;padding:15px 0;display:block}.addCourseGain[_ngcontent-%COMP%]{position:absolute;bottom:13px;color:grey;left:calc(85% + 40px)}.info-icon[_ngcontent-%COMP%]{font-size:18px;height:18px;width:18px;vertical-align:middle;line-height:18px}.circle[_ngcontent-%COMP%]{border:1px solid #ccc;border-radius:50%;display:inline-block;width:25px;line-height:25px;text-align:center;height:25px}.cinfo-next-btn[_ngcontent-%COMP%]{background:#cf0000;color:#f5f5f5;position:absolute;bottom:1rem;right:3rem;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}"]}),e})()}}]);