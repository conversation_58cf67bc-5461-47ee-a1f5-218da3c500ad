(window.webpackJsonp=window.webpackJsonp||[]).push([[958],{"23oC":function(e,t,n){"use strict";n.r(t),n.d(t,"LcdpCreateTaskComponent",(function(){return q}));var a=n("mrSG"),l=n("0IaG"),i=n("3Pt+"),o=n("wd/R"),r=n("xG9w"),s=n("1G5W"),d=n("XNiG"),c=n("7pIB"),m=n("ofXK"),p=n("kmnG"),u=n("qFsG"),h=n("bTqV"),g=n("NFeN"),f=n("Qu3c"),v=(n("/1cH"),n("d3UM")),k=n("iadO"),y=n("Xa2L"),x=n("4/q7"),b=(n("Xi0T"),n("fXoL")),C=n("XXEo"),_=n("z52X"),D=n("LcQX"),S=n("6t9p"),M=n("8SgF"),T=n("me71"),E=n("FKr1");function w(e,t){if(1&e){const e=b["\u0275\u0275getCurrentView"]();b["\u0275\u0275elementStart"](0,"div"),b["\u0275\u0275elementStart"](1,"button",53),b["\u0275\u0275listener"]("click",(function(){b["\u0275\u0275restoreView"](e);const n=t.index;return b["\u0275\u0275nextContext"]().startDatesClicked(n)})),b["\u0275\u0275text"](2),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;b["\u0275\u0275advance"](1),b["\u0275\u0275propertyInterpolate2"]("matTooltip","",e.duration,"\n",e.displayValue,""),b["\u0275\u0275property"]("ngClass",e.clicked?"btn-active":"btn-not-active"),b["\u0275\u0275advance"](1),b["\u0275\u0275textInterpolate1"](" ",e.label," ")}}function O(e,t){if(1&e){const e=b["\u0275\u0275getCurrentView"]();b["\u0275\u0275elementStart"](0,"div"),b["\u0275\u0275elementStart"](1,"button",53),b["\u0275\u0275listener"]("click",(function(){b["\u0275\u0275restoreView"](e);const n=t.index;return b["\u0275\u0275nextContext"]().endDatesClicked(n)})),b["\u0275\u0275text"](2),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;b["\u0275\u0275advance"](1),b["\u0275\u0275propertyInterpolate2"]("matTooltip","",e.duration,"\n",e.displayValue,""),b["\u0275\u0275property"]("ngClass",e.clicked?"btn-active":"btn-not-active"),b["\u0275\u0275advance"](1),b["\u0275\u0275textInterpolate1"](" ",e.label," ")}}function P(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"mat-option",54),b["\u0275\u0275text"](1),b["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;b["\u0275\u0275property"]("value",e._id),b["\u0275\u0275advance"](1),b["\u0275\u0275textInterpolate"](e.type_name)}}function F(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"mat-option",54),b["\u0275\u0275text"](1),b["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;b["\u0275\u0275property"]("value",e.name),b["\u0275\u0275advance"](1),b["\u0275\u0275textInterpolate"](e.name)}}function I(e,t){if(1&e){const e=b["\u0275\u0275getCurrentView"]();b["\u0275\u0275elementStart"](0,"div",55),b["\u0275\u0275listener"]("fileOver",(function(t){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().fileOverDropZone(t)}))("onFileDrop",(function(t){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275nextContext"]().filedrop(t)})),b["\u0275\u0275elementStart"](1,"div",56),b["\u0275\u0275elementStart"](2,"div",57),b["\u0275\u0275elementStart"](3,"small",36),b["\u0275\u0275text"](4,"Drag and drop your attachments here"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](5,"div",58),b["\u0275\u0275elementStart"](6,"button",59),b["\u0275\u0275listener"]("click",(function(){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275reference"](9).click()})),b["\u0275\u0275text"](7," Upload "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](8,"input",60,61),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](10,"div",57),b["\u0275\u0275elementStart"](11,"div",62),b["\u0275\u0275elementStart"](12,"mat-icon",63),b["\u0275\u0275text"](13,"cloud_upload"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()}if(2&e){const e=b["\u0275\u0275nextContext"]();b["\u0275\u0275property"]("ngClass",e.isFileOverDropZone?"file-present":"file-empty")("uploader",e.uploader),b["\u0275\u0275advance"](8),b["\u0275\u0275property"]("uploader",e.uploader)("accept",e.allowedMimeType)}}function Y(e,t){if(1&e){const e=b["\u0275\u0275getCurrentView"]();b["\u0275\u0275elementStart"](0,"div"),b["\u0275\u0275elementStart"](1,"div",36),b["\u0275\u0275elementStart"](2,"span",69),b["\u0275\u0275text"](3),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](4,"span",65),b["\u0275\u0275elementStart"](5,"button",70),b["\u0275\u0275listener"]("click",(function(){b["\u0275\u0275restoreView"](e);const n=t.$implicit;return b["\u0275\u0275nextContext"](2).uploader.removeFromQueue(n)})),b["\u0275\u0275elementStart"](6,"mat-icon",8),b["\u0275\u0275text"](7,"close"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;b["\u0275\u0275advance"](2),b["\u0275\u0275property"]("matTooltip",null==e||null==e.file?null:e.file.name),b["\u0275\u0275advance"](1),b["\u0275\u0275textInterpolate1"](" ",null==e||null==e.file?null:e.file.name,"")}}function L(e,t){if(1&e){const e=b["\u0275\u0275getCurrentView"]();b["\u0275\u0275elementStart"](0,"div"),b["\u0275\u0275elementStart"](1,"div",36),b["\u0275\u0275elementStart"](2,"div",64),b["\u0275\u0275text"](3," Files attached "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](4,"div",65),b["\u0275\u0275elementStart"](5,"button",66),b["\u0275\u0275listener"]("click",(function(){return b["\u0275\u0275restoreView"](e),b["\u0275\u0275reference"](9).click()})),b["\u0275\u0275elementStart"](6,"mat-icon",4),b["\u0275\u0275text"](7,"cloud_upload"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](8,"input",60,67),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](10,"div",68),b["\u0275\u0275template"](11,Y,8,2,"div",37),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()}if(2&e){const e=b["\u0275\u0275nextContext"]();b["\u0275\u0275advance"](8),b["\u0275\u0275property"]("uploader",e.uploader)("accept",e.allowedMimeType),b["\u0275\u0275advance"](3),b["\u0275\u0275property"]("ngForOf",e.uploader.queue)}}function V(e,t){1&e&&(b["\u0275\u0275elementStart"](0,"mat-icon"),b["\u0275\u0275text"](1,"done_all"),b["\u0275\u0275elementEnd"]())}function j(e,t){1&e&&b["\u0275\u0275element"](0,"mat-spinner",71)}const N=function(e){return{"background-color":e}},H=function(e){return{"is-disabled":e}};let q=(()=>{class e{constructor(e,t,n,a,l,m){this.dialogRef=e,this.inData=t,this.fb=n,this._login=a,this._lcdpService=l,this._util=m,this.currentDate=o(),this.currentUser=this._login.getProfile().profile,this._onDestroy=new d.b,this.taskForm=this.fb.group({taskName:[null,i.H.required],taskDesc:[null],assignedTo:[null,i.H.required],startDate:[null,i.H.required],endDate:[null,i.H.required],plannedHours:[null],location:[null,i.H.required],taskType:["Ask",i.H.required],attachments:[null]}),this.valueType="html",this.startDateTypes=[{label:"Today",value:o(),duration:"0 days",displayValue:"Due on : "+o().format("DD-MMM-YY"),clicked:!1},{label:"1 D",value:o().add(1,"d"),duration:"1 Day",displayValue:"Due on : "+o().add(1,"d").format("DD-MMM-YY"),clicked:!1},{label:"2 D",value:o().add(2,"d"),duration:"2 Days",displayValue:"Due on : "+o().add(2,"d").format("DD-MMM-YY"),clicked:!1},{label:"1 W",value:o().add(1,"w"),duration:"1 Week",displayValue:"Due on : "+o().add(1,"w").format("DD-MMM-YY"),clicked:!1}],this.endDateTypes=[{label:"Today",value:o(),duration:"0 days",displayValue:"Due on : "+o().format("DD-MMM-YY"),clicked:!1},{label:"1 D",value:o().add(1,"d"),duration:"1 Day",displayValue:"Due on : "+o().add(1,"d").format("DD-MMM-YY"),clicked:!1},{label:"2 D",value:o().add(2,"d"),duration:"2 Days",displayValue:"Due on : "+o().add(2,"d").format("DD-MMM-YY"),clicked:!1},{label:"1 W",value:o().add(1,"w"),duration:"1 Week",displayValue:"Due on : "+o().add(1,"w").format("DD-MMM-YY"),clicked:!1}],this.taskTypes=[],this.allowedMimeType=["*/*"],this.maxFileSize=10485760,this.fileList=[],this.filesJsonConcat=[],this.isFileOverDropZone=!1,this.uploader=new c.d({url:"/api/appBuilder/lcdp/attachments/uploadAttachment",authToken:"Bearer "+this._login.getToken(),disableMultipart:!1,maxFileSize:this.maxFileSize,headers:[{name:"user",value:this.currentUser.name}]}),this.isLoading=!1,this.locationList=[],this.minDate=o(),this.fileUploadChanges=()=>{this.uploader.onProgressItem=e=>{this.isLoading=!0},this.uploader.onCompleteItem=(e,t,n,a)=>{if(200==n&&t&&t.length>0){this.isLoading=!1;let e=JSON.parse(t);this.filesJsonConcat=this.filesJsonConcat.concat(e.data),this.uploader.queue.length==this.filesJsonConcat.length&&(this.taskForm.get("attachments").patchValue(this.filesJsonConcat),this.addTask())}else this._util.showToastMessage("Unable to upload")}},this.detectFormControlChanges=()=>{this.taskForm.get("startDate").valueChanges.pipe(Object(s.a)(this._onDestroy)).subscribe(e=>{for(let t=0;t<this.startDateTypes.length;t++)this.startDateTypes[t].clicked=o(e).format("YYYY-MM-DD")===o(this.startDateTypes[t].value).format("YYYY-MM-DD")}),this.taskForm.get("endDate").valueChanges.pipe(Object(s.a)(this._onDestroy)).subscribe(e=>{for(let t=0;t<this.endDateTypes.length;t++)this.endDateTypes[t].clicked=o(e).format("YYYY-MM-DD")===o(this.endDateTypes[t].value).format("YYYY-MM-DD")}),this.taskForm.get("location").valueChanges.pipe(Object(s.a)(this._onDestroy)).subscribe(e=>{if(e){let t=r.where(this.locationList,{name:e});t.length>0&&(this.location={location_id:t[0].location_id,office_id:t[0].office_id})}}),this.taskForm.get("taskType").valueChanges.pipe(Object(s.a)(this._onDestroy)).subscribe(e=>{e&&r.where(this.taskTypes,{_id:e})})},this.startDatesClicked=e=>{for(let t=0;t<this.startDateTypes.length;t++)this.startDateTypes[t].clicked=t===e;this.taskForm.patchValue({startDate:this.startDateTypes[e].value})},this.endDatesClicked=e=>{for(let t=0;t<this.endDateTypes.length;t++)this.endDateTypes[t].clicked=t===e;this.taskForm.patchValue({endDate:this.endDateTypes[e].value})},this.valueChange=e=>{e&&this.taskForm.get("taskDesc").patchValue(e)},this.setCurrentEmployee=e=>{e&&e!=this.taskForm.get("assignedTo").value&&this.taskForm.get("assignedTo").patchValue(e)},this.saveTask=()=>{"VALID"==this.taskForm.status?this.uploader.queue.length>0?this.uploader.uploadAll():this.addTask():this.showValidationAlert()},this.addTask=()=>{this.isLoading=!0,this._lcdpService.addTask(this._lcdpService.lcdpDetails.lcdpApplicationId,this._lcdpService.lcdpDetails.recordId,this.getTaskDetails()).pipe(Object(s.a)(this._onDestroy)).subscribe(e=>{this.isLoading=!1,"S"==e.messType?(this.dialogRef.close({event:"Submit",data:{taskInsertId:e.data.insertedId,taskDetails:this.getTaskDetails()}}),this._util.showToastMessage("Task Created Successfully !")):this._util.showToastMessage("Error in Creation !")},e=>{this._lcdpService.showErrorMessage(e)})},this.getTaskDetails=()=>({task_name:this.taskForm.get("taskName").value,task_description:this.taskForm.get("taskDesc").value,task_status:this.modalParams.taskStatus?this.modalParams.taskStatus:"Open",assigned_to:this.taskForm.get("assignedTo").value,start_date:this.taskForm.get("startDate").value?o(this.taskForm.get("startDate").value).format("YYYY-MM-DD"):null,end_date:this.taskForm.get("endDate").value?o(this.taskForm.get("endDate").value).format("YYYY-MM-DD"):null,planned_hours:this.taskForm.get("plannedHours").value?this.taskForm.get("plannedHours").value:0,approved_planned_hours:0,actual_hours:[],location:this.taskForm.get("location").value?this.location:null,task_type:this.taskForm.get("taskType").value,timesheet_ids:[],attachments:this.taskForm.get("attachments").value?this.taskForm.get("attachments").value:[],comments:[],created_on:this.currentDate,created_by:this.currentUser.oid,parent_id:this.modalParams.parentId?this.modalParams.parentId:null,form_id:this.selectedTypeFormId?this.selectedTypeFormId:null,is_active:!0}),this.showValidationAlert=()=>{let e=this.getTaskDetails();this._util.showToastMessage(null==e.task_name?"Kindly enter Task name":null==e.assigned_to?"Kindly assign a person for the Task":null==e.start_date?"Kindly enter start date for the Task":null==e.end_date?"Kindly enter end date for the Task":null==e.task_type?"Kindly select type of task":null==e.location?"Kindly select location for the Task":"Kindly check if all values are filled")},this.closeTaskModal=()=>{this.dialogRef.close({event:"Close"})},this.fileUploadChanges()}ngOnInit(){this.modalParams=this.inData.modalParams,this.modalParams&&this.modalParams.taskTypeList&&this.modalParams.taskTypeList.length>0&&(this.taskTypes=this.modalParams.taskTypeList,this.taskForm.patchValue({taskType:this.taskTypes[0]._id}),this.modalParams.parentTypeId)&&(r.where(this.taskTypes,{_id:this.modalParams.parentTypeId}),this.taskForm.patchValue({taskType:this.modalParams.parentTypeId})),this.uploader.setOptions({headers:[{name:"lcdp-application-id",value:this._lcdpService.lcdpDetails.lcdpApplicationId},{name:"record-id",value:this._lcdpService.lcdpDetails.recordId}]}),this.getLocationList(),this.detectFormControlChanges(),this.taskForm.get("startDate").patchValue(this.currentDate)}getLocationList(){this._lcdpService.getLocationList().pipe(Object(s.a)(this._onDestroy)).subscribe(e=>Object(a.c)(this,void 0,void 0,(function*(){if("S"==e.messType&&e.data.length>0){this.locationList=e.data;let t=r.where(this.locationList,{name:"Chennai - TRIL"});this.taskForm.get("location").patchValue(t[0].name),this.location={location_id:t[0].location_id,office_id:t[0].office_id}}else this._util.showToastMessage(e.messText)})),e=>{this._lcdpService.showErrorMessage(e)})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}}return e.\u0275fac=function(t){return new(t||e)(b["\u0275\u0275directiveInject"](l.h),b["\u0275\u0275directiveInject"](l.a),b["\u0275\u0275directiveInject"](i.i),b["\u0275\u0275directiveInject"](C.a),b["\u0275\u0275directiveInject"](_.a),b["\u0275\u0275directiveInject"](D.a))},e.\u0275cmp=b["\u0275\u0275defineComponent"]({type:e,selectors:[["app-lcdp-create-task"]],decls:103,vars:26,consts:[[1,"container-fluid","lcdp-create-task-styles"],[1,"row",2,"border-bottom","solid 1px #cacaca"],[1,"col-11","pt-1","pb-2","d-flex","pl-2"],["mat-icon-button","",1,"bubble","mt-1"],[1,"iconButton"],[1,"headingBold","pt-2","ml-3"],[1,"col-1","d-flex"],["matTooltip","Close","mat-icon-button","",1,"ml-auto","close-button","mt-1",3,"click"],[1,"close-Icon"],[3,"formGroup"],[1,"row","pt-2"],[1,"col-10"],["appearance","outline",1,"form-field-class"],["matInput","","placeholder","Task name","formControlName","taskName"],["placeholder","Task description",1,"dev-extreme-styles",3,"valueType","valueChange"],["name","bold"],["name","italic"],["name","strike"],["name","underline"],["name","separator"],["name","alignLeft"],["name","alignCenter"],["name","alignRight"],["name","alignJustify"],["name","orderedList"],["name","bulletList"],[1,"row","pt-3"],[1,"col-5"],["label","Assign to","formControlName","assignedTo",2,"width","100% !important",3,"isAutocomplete","required"],[1,"col-1","mt-2"],["imgWidth","30px","imgHeight","30px",2,"cursor","pointer",3,"matTooltip","id","click"],[1,"col-4"],["matInput","","formControlName","startDate",3,"min","matDatepicker"],["matSuffix","",3,"for"],["startDatepicker",""],[1,"col-5","pr-0","pl-0","pt-1"],[1,"row"],[4,"ngFor","ngForOf"],["matInput","","formControlName","endDate",3,"min","matDatepicker"],["endDatepicker",""],[1,"headingBold","pl-1"],["formControlName","taskType"],[3,"value",4,"ngFor","ngForOf"],[1,"headingBold"],[1,"heaingBold"],["matInput","","type","number","placeholder","Planned Hours - HH:MM","formControlName","plannedHours"],["formControlName","location"],[1,"col-8"],["ng2FileDrop","","style","height: 6rem",3,"ngClass","uploader","fileOver","onFileDrop",4,"ngIf"],[4,"ngIf"],[1,"col-2","d-flex","my-auto","justify-content-center"],["mat-icon-button","","matTooltip","Create Task",1,"iconbtn",3,"ngStyle","ngClass","disabled","click"],["diameter","30","class","spinner-align",4,"ngIf"],["mat-raised-button","","matTooltipPosition","above","matTooltipClass","my-tooltip-multi-line",1,"ml-2",3,"matTooltip","ngClass","click"],[3,"value"],["ng2FileDrop","",2,"height","6rem",3,"ngClass","uploader","fileOver","onFileDrop"],[1,"row","justify-content-center","pt-2"],[1,"col-6"],[1,"row","pt-3","justify-content-center"],["mat-raised-button","",1,"upload-btn",3,"click"],["hidden","","type","file","ng2FileSelect","",3,"uploader","accept"],["fileInput",""],[1,"row","d-flex","justify-content-center",2,"min-height","5rem"],[1,"my-auto","cloud-icon"],[1,"col-11","pl-0","headingBold"],[1,"col-1","pl-0","pr-0"],["mat-icon-button","","matTooltip","Add more files",1,"view-button-inactive",3,"click"],["moreFileInput",""],[1,"scroll-window"],[1,"col-11","pl-2","pt-2","pr-0","normalFont",3,"matTooltip"],["mat-icon-button","","matTooltip","Remove file",1,"ml-auto","close-button",3,"click"],["diameter","30",1,"spinner-align"]],template:function(e,t){if(1&e&&(b["\u0275\u0275elementStart"](0,"div",0),b["\u0275\u0275elementStart"](1,"div",1),b["\u0275\u0275elementStart"](2,"div",2),b["\u0275\u0275elementStart"](3,"div",3),b["\u0275\u0275elementStart"](4,"mat-icon",4),b["\u0275\u0275text"](5,"add_task"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](6,"span",5),b["\u0275\u0275text"](7," Create Task "),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](8,"div",6),b["\u0275\u0275elementStart"](9,"button",7),b["\u0275\u0275listener"]("click",(function(){return t.closeTaskModal()})),b["\u0275\u0275elementStart"](10,"mat-icon",8),b["\u0275\u0275text"](11,"close"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](12,"form",9),b["\u0275\u0275elementStart"](13,"div",10),b["\u0275\u0275elementStart"](14,"div",11),b["\u0275\u0275elementStart"](15,"mat-form-field",12),b["\u0275\u0275elementStart"](16,"mat-label"),b["\u0275\u0275text"](17,"Task name"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](18,"input",13),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](19,"div",10),b["\u0275\u0275elementStart"](20,"div",11),b["\u0275\u0275elementStart"](21,"dx-html-editor",14),b["\u0275\u0275listener"]("valueType",(function(){return t.valueType}))("valueChange",(function(e){return t.valueChange(e)})),b["\u0275\u0275elementStart"](22,"dxo-toolbar"),b["\u0275\u0275element"](23,"dxi-item",15),b["\u0275\u0275element"](24,"dxi-item",16),b["\u0275\u0275element"](25,"dxi-item",17),b["\u0275\u0275element"](26,"dxi-item",18),b["\u0275\u0275element"](27,"dxi-item",19),b["\u0275\u0275element"](28,"dxi-item",20),b["\u0275\u0275element"](29,"dxi-item",21),b["\u0275\u0275element"](30,"dxi-item",22),b["\u0275\u0275element"](31,"dxi-item",23),b["\u0275\u0275element"](32,"dxi-item",19),b["\u0275\u0275element"](33,"dxi-item",24),b["\u0275\u0275element"](34,"dxi-item",25),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](35,"div",26),b["\u0275\u0275elementStart"](36,"div",27),b["\u0275\u0275element"](37,"app-search-user",28),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](38,"div",29),b["\u0275\u0275elementStart"](39,"app-user-image",30),b["\u0275\u0275listener"]("click",(function(){return t.setCurrentEmployee(t.currentUser.oid)})),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](40,"div",31),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](41,"div",10),b["\u0275\u0275elementStart"](42,"div",27),b["\u0275\u0275elementStart"](43,"mat-form-field",12),b["\u0275\u0275elementStart"](44,"mat-label"),b["\u0275\u0275text"](45,"Start date"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](46,"input",32),b["\u0275\u0275element"](47,"mat-datepicker-toggle",33),b["\u0275\u0275element"](48,"mat-datepicker",null,34),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](50,"div",35),b["\u0275\u0275elementStart"](51,"div",36),b["\u0275\u0275template"](52,w,3,4,"div",37),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](53,"div",10),b["\u0275\u0275elementStart"](54,"div",27),b["\u0275\u0275elementStart"](55,"mat-form-field",12),b["\u0275\u0275elementStart"](56,"mat-label"),b["\u0275\u0275text"](57,"End date"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](58,"input",38),b["\u0275\u0275element"](59,"mat-datepicker-toggle",33),b["\u0275\u0275element"](60,"mat-datepicker",null,39),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](62,"div",35),b["\u0275\u0275elementStart"](63,"div",36),b["\u0275\u0275template"](64,O,3,4,"div",37),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](65,"div",36),b["\u0275\u0275elementStart"](66,"div",11),b["\u0275\u0275elementStart"](67,"span",40),b["\u0275\u0275text"](68,"Task Type"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](69,"div",26),b["\u0275\u0275elementStart"](70,"div",27),b["\u0275\u0275elementStart"](71,"mat-form-field",12),b["\u0275\u0275elementStart"](72,"mat-label"),b["\u0275\u0275text"](73,"Type"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](74,"mat-select",41),b["\u0275\u0275template"](75,P,2,2,"mat-option",42),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](76,"div",10),b["\u0275\u0275elementStart"](77,"div",27),b["\u0275\u0275elementStart"](78,"span",43),b["\u0275\u0275text"](79,"Hours"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](80,"div",27),b["\u0275\u0275elementStart"](81,"span",44),b["\u0275\u0275text"](82,"Location"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](83,"div",10),b["\u0275\u0275elementStart"](84,"div",27),b["\u0275\u0275elementStart"](85,"mat-form-field",12),b["\u0275\u0275elementStart"](86,"mat-label"),b["\u0275\u0275text"](87,"Planned Hours"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275element"](88,"input",45),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](89,"div",27),b["\u0275\u0275elementStart"](90,"mat-form-field",12),b["\u0275\u0275elementStart"](91,"mat-label"),b["\u0275\u0275text"](92,"Location"),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](93,"mat-select",46),b["\u0275\u0275template"](94,F,2,2,"mat-option",42),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](95,"div",10),b["\u0275\u0275elementStart"](96,"div",47),b["\u0275\u0275template"](97,I,14,4,"div",48),b["\u0275\u0275template"](98,L,12,3,"div",49),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementStart"](99,"div",50),b["\u0275\u0275elementStart"](100,"button",51),b["\u0275\u0275listener"]("click",(function(){return t.saveTask()})),b["\u0275\u0275template"](101,V,2,0,"mat-icon",49),b["\u0275\u0275template"](102,j,1,0,"mat-spinner",52),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"](),b["\u0275\u0275elementEnd"]()),2&e){const e=b["\u0275\u0275reference"](49),n=b["\u0275\u0275reference"](61);b["\u0275\u0275advance"](12),b["\u0275\u0275property"]("formGroup",t.taskForm),b["\u0275\u0275advance"](25),b["\u0275\u0275property"]("isAutocomplete",!0)("required",!0),b["\u0275\u0275advance"](2),b["\u0275\u0275property"]("matTooltip",t.currentUser.name)("id",t.currentUser.oid),b["\u0275\u0275advance"](7),b["\u0275\u0275property"]("min",t.minDate)("matDatepicker",e),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("for",e),b["\u0275\u0275advance"](5),b["\u0275\u0275property"]("ngForOf",t.startDateTypes),b["\u0275\u0275advance"](6),b["\u0275\u0275property"]("min",t.taskForm.get("startDate").value)("matDatepicker",n),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("for",n),b["\u0275\u0275advance"](5),b["\u0275\u0275property"]("ngForOf",t.endDateTypes),b["\u0275\u0275advance"](11),b["\u0275\u0275property"]("ngForOf",t.taskTypes),b["\u0275\u0275advance"](19),b["\u0275\u0275property"]("ngForOf",t.locationList),b["\u0275\u0275advance"](3),b["\u0275\u0275property"]("ngIf",0==t.uploader.queue.length),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf",t.uploader.queue.length>0),b["\u0275\u0275advance"](2),b["\u0275\u0275property"]("ngStyle",b["\u0275\u0275pureFunction1"](22,N,t.isLoading?"#f3f3f3":"#cf0001"))("ngClass",b["\u0275\u0275pureFunction1"](24,H,t.isLoading))("disabled",t.isLoading),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf",!t.isLoading),b["\u0275\u0275advance"](1),b["\u0275\u0275property"]("ngIf",t.isLoading)}},directives:[g.a,h.a,f.a,i.J,i.w,i.n,p.c,p.g,u.b,i.e,i.v,i.l,x.a,S.Ge,S.o,M.a,i.F,T.a,k.g,k.i,p.i,k.f,m.NgForOf,v.c,i.A,m.NgIf,m.NgStyle,m.NgClass,E.p,c.a,c.b,y.c],styles:[".lcdp-create-task-styles[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]{display:flex;line-height:26px;border-radius:50%;cursor:context-menu!important;width:26px;height:26px;background-color:#fbfbfb;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.lcdp-create-task-styles[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#cf0001;font-size:18px;margin-top:3px!important;margin-left:4px!important}.lcdp-create-task-styles[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.lcdp-create-task-styles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}.lcdp-create-task-styles[_ngcontent-%COMP%]   .headingBold[_ngcontent-%COMP%]{color:#cf0001;font-weight:500;font-size:14px}.lcdp-create-task-styles[_ngcontent-%COMP%]   .normalFont[_ngcontent-%COMP%]{color:#181818;font-size:14px;font-weight:400;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.lcdp-create-task-styles[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-flex{position:relative;height:2.5rem;width:100%}.lcdp-create-task-styles[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:100%;font-size:13px}.lcdp-create-task-styles[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding-bottom:8px!important}.lcdp-create-task-styles[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-form-field-underline{bottom:0}.lcdp-create-task-styles[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-form-field-infix{padding:.65em 0!important;border-top:.54375em solid transparent!important;font-size:13px}.lcdp-create-task-styles[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-select-arrow{margin:0 4px!important}.lcdp-create-task-styles[_ngcontent-%COMP%]   .dev-extreme-styles[_ngcontent-%COMP%]     .dx-htmleditor-toolbar-wrapper .dx-toolbar-items-container{height:48px!important}.lcdp-create-task-styles[_ngcontent-%COMP%]   .dev-extreme-styles[_ngcontent-%COMP%]     .dx-htmleditor-toolbar-wrapper .dx-button-content .dx-icon{font-size:16px!important;color:#66615b!important}.lcdp-create-task-styles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff}.lcdp-create-task-styles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%], .lcdp-create-task-styles[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]{font-weight:400;font-size:13px!important;min-width:60px;line-height:33px;padding:0 8px;border-radius:4px;margin-right:6px!important;margin-bottom:3px}.lcdp-create-task-styles[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]{color:rgba(0,0,0,.534);background-color:rgba(194,193,193,.24)}.lcdp-create-task-styles[_ngcontent-%COMP%]   .file-present[_ngcontent-%COMP%]{border:3px dotted #e44a4a;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.lcdp-create-task-styles[_ngcontent-%COMP%]   .file-present[_ngcontent-%COMP%]   .cloud-icon[_ngcontent-%COMP%]{color:#e44a4a}.lcdp-create-task-styles[_ngcontent-%COMP%]   .file-empty[_ngcontent-%COMP%]{border:2px solid #cacaca}.lcdp-create-task-styles[_ngcontent-%COMP%]   .cloud-icon[_ngcontent-%COMP%]{font-size:4rem;color:#b8b7b5;display:flex;align-items:center;justify-content:center}.lcdp-create-task-styles[_ngcontent-%COMP%]   .upload-btn[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff;font-weight:400;margin-bottom:1%;width:7rem;text-align:center;overflow:hidden;height:30px;font-size:12px;line-height:10px;text-overflow:ellipsis;white-space:nowrap}.lcdp-create-task-styles[_ngcontent-%COMP%]   .spinner-align[_ngcontent-%COMP%]{margin:auto;display:inline-block}.lcdp-create-task-styles[_ngcontent-%COMP%]   .iconbtn[_ngcontent-%COMP%]{background-color:#c92020;color:#fff;padding:0;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.lcdp-create-task-styles[_ngcontent-%COMP%]   .is-disabled[_ngcontent-%COMP%]{pointer-events:none}.lcdp-create-task-styles[_ngcontent-%COMP%]   .scroll-window[_ngcontent-%COMP%]{border:2px solid #cacaca;height:8rem;overflow:scroll;overflow-x:hidden;margin-top:10px}.lcdp-create-task-styles[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]{line-height:8px;width:26px;height:26px;position:absolute;margin-right:5px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12);animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}.lcdp-create-task-styles[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}"]}),e})()}}]);