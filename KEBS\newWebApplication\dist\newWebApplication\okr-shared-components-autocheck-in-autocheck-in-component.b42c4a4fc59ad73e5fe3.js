(window.webpackJsonp=window.webpackJsonp||[]).push([[828],{fbxu:function(e,t,n){"use strict";n.r(t),n.d(t,"AutocheckInComponent",(function(){return v}));var o=n("0IaG"),a=n("fXoL"),l=n("25DO"),r=n("LcQX"),i=n("BVzC"),c=n("bTqV"),s=n("NFeN"),d=n("kmnG"),m=n("d3UM"),p=n("ofXK"),u=n("FKr1");function h(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"mat-option",19),a["\u0275\u0275text"](1),a["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;a["\u0275\u0275property"]("value",e),a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate"](e.name)}}function g(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"mat-icon",20),a["\u0275\u0275text"](1,"done_all"),a["\u0275\u0275elementEnd"]())}function f(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"div",21),a["\u0275\u0275elementStart"](1,"span",22),a["\u0275\u0275text"](2,"Loading..."),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]())}let v=(()=>{class e{constructor(e,t,n,o,a,l){this._okr=e,this._util=t,this._matDialogRef=n,this.data=o,this._dialog=a,this._ErrorService=l,this.isUpdate=!1,console.log(this.data),this.arr=this.data.autoCheckInData}ngOnInit(){}getSelectedName(e){this.selectedName=e,console.log(this.selectedName)}onSave(){this.isUpdate=!0;let e={kr_id:this.data.krId,auto_checkin_url:this.selectedName.auto_checkin_url,auto_checkin_dataName:this.selectedName.auto_checkin_dataName};console.log(e),this._okr.updateAutoCheckInKR(e).subscribe(e=>{this._util.showMessage("Autocheck-In successfull","Dismiss",3e3),this.closeDialog("sucess")},e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage),this.closeDialog("sucess")})}closeDialog(e){this._matDialogRef.close(e)}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275directiveInject"](l.a),a["\u0275\u0275directiveInject"](r.a),a["\u0275\u0275directiveInject"](o.h),a["\u0275\u0275directiveInject"](o.a),a["\u0275\u0275directiveInject"](o.b),a["\u0275\u0275directiveInject"](i.a))},e.\u0275cmp=a["\u0275\u0275defineComponent"]({type:e,selectors:[["app-autocheck-in"]],decls:26,vars:4,consts:[[1,"row","px-3","py-2"],[1,"col-12","p-0","m-0"],[1,"row","mt-3","p-0"],[1,"col-12","m-0","p-0"],[1,"row"],[1,"col-10","pl-0","pr-0"],[1,"pl-2",2,"font-size","16px","font-weight","500","color","brown"],[1,"col-2","p-0"],["mat-icon-button","",1,"close-btn",3,"click"],[2,"font-size","20px","line-height","5px"],[1,"row","mt-1","p-0"],[1,"col-12"],[1,"col-12",2,"padding-bottom","5px"],["appearance","outline"],["placeholder","Select Name",3,"selectionChange"],[3,"value",4,"ngFor","ngForOf"],["mat-mini-fab","",1,"done-all-btn",3,"disabled","click"],["style","color: white;font-size: 21px;",4,"ngIf"],["class","spinner-border text-danger","role","status",4,"ngIf"],[3,"value"],[2,"color","white","font-size","21px"],["role","status",1,"spinner-border","text-danger"],[1,"sr-only"]],template:function(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"div",0),a["\u0275\u0275elementStart"](1,"div",1),a["\u0275\u0275elementStart"](2,"div",2),a["\u0275\u0275elementStart"](3,"div",3),a["\u0275\u0275elementStart"](4,"div",4),a["\u0275\u0275elementStart"](5,"div",5),a["\u0275\u0275elementStart"](6,"span",6),a["\u0275\u0275text"](7,"Configure Autocheck-In"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](8,"div",7),a["\u0275\u0275elementStart"](9,"button",8),a["\u0275\u0275listener"]("click",(function(){return t.closeDialog("")})),a["\u0275\u0275elementStart"](10,"mat-icon",9),a["\u0275\u0275text"](11,"close"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](12,"div",10),a["\u0275\u0275elementStart"](13,"div",11),a["\u0275\u0275elementStart"](14,"span"),a["\u0275\u0275text"](15,"Select Name:"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](16,"div",12),a["\u0275\u0275elementStart"](17,"mat-form-field",13),a["\u0275\u0275elementStart"](18,"mat-select",14),a["\u0275\u0275listener"]("selectionChange",(function(e){return t.getSelectedName(e.value)})),a["\u0275\u0275template"](19,h,2,2,"mat-option",15),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](20,"div",10),a["\u0275\u0275elementStart"](21,"div",11),a["\u0275\u0275elementStart"](22,"button",16),a["\u0275\u0275listener"]("click",(function(){return t.onSave()})),a["\u0275\u0275template"](23,g,2,0,"mat-icon",17),a["\u0275\u0275template"](24,f,3,0,"div",18),a["\u0275\u0275text"](25," Done "),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e&&(a["\u0275\u0275advance"](19),a["\u0275\u0275property"]("ngForOf",t.arr),a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("disabled",t.isUpdate),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",!t.isUpdate),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",t.isUpdate))},directives:[c.a,s.a,d.c,m.c,p.NgForOf,p.NgIf,u.p],styles:[".close-btn[_ngcontent-%COMP%]{color:grey}.done-all-btn[_ngcontent-%COMP%]{width:12.5rem;border-radius:4px;background-color:#cf0001;color:#fff;box-shadow:0 1px 3px 0 rgba(0,0,0,.51),0 0 14px 0 rgba(0,0,0,.11);margin-bottom:8px}"]}),e})()}}]);