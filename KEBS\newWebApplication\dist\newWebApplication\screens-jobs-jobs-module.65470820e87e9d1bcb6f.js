(window.webpackJsonp=window.webpackJsonp||[]).push([[872],{Lp0Z:function(e,n,o){"use strict";o.r(n),o.d(n,"JobsModule",(function(){return i}));var t=o("ofXK"),a=o("tyNb"),l=o("fXoL");const r=[{path:"",pathMatch:"full",redirectTo:"job-list"},{path:"job-list",loadChildren:()=>Promise.all([o.e(2),o.e(3),o.e(7),o.e(8),o.e(19),o.e(22),o.e(24),o.e(27),o.e(35),o.e(39),o.e(43),o.e(70),o.e(68),o.e(88),o.e(111),o.e(180),o.e(137),o.e(0),o.e(797)]).then(o.bind(null,"fq6j")).then(e=>e.ManageJobModule),data:{breadcrumb:"All Job"}},{path:"new-job",loadChildren:()=>Promise.all([o.e(19),o.e(33),o.e(88),o.e(0),o.e(824)]).then(o.bind(null,"iMGq")).then(e=>e.NewJobModule),data:{breadcrumb:"Create Job"}},{path:"draft-job-list",loadChildren:()=>Promise.all([o.e(39),o.e(43),o.e(70),o.e(351)]).then(o.bind(null,"8XW1")).then(e=>e.DraftJobModule),data:{breadcrumb:"Draft Job"}},{path:"campus-job-list",loadChildren:()=>Promise.all([o.e(2),o.e(3),o.e(7),o.e(8),o.e(19),o.e(22),o.e(24),o.e(27),o.e(35),o.e(39),o.e(43),o.e(70),o.e(68),o.e(88),o.e(111),o.e(180),o.e(137),o.e(0),o.e(797)]).then(o.bind(null,"fq6j")).then(e=>e.ManageJobModule),data:{breadcrumb:"Manage Campus Job"}}];let d=(()=>{class e{}return e.\u0275mod=l["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=l["\u0275\u0275defineInjector"]({factory:function(n){return new(n||e)},imports:[[a.k.forChild(r)],a.k]}),e})(),i=(()=>{class e{}return e.\u0275mod=l["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=l["\u0275\u0275defineInjector"]({factory:function(n){return new(n||e)},imports:[[t.CommonModule,d]]}),e})()}}]);