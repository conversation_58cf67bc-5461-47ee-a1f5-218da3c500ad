(window.webpackJsonp=window.webpackJsonp||[]).push([[917],{"/iMz":function(t,e,n){"use strict";n.r(e),n.d(e,"SendCampusInvitationDialogComponent",(function(){return L}));var a=n("mrSG"),r=n("3Pt+"),i=n("0IaG"),o=n("XNiG"),s=n("1G5W"),l=n("fXoL"),c=n("rQiX"),m=n("XNFG"),d=n("JLuW"),h=n("YVm3"),g=n("URR/"),p=n("NFeN"),C=n("ofXK"),f=n("Xa2L"),u=n("II/y");function M(t,e){1&t&&(l["\u0275\u0275elementStart"](0,"div",21),l["\u0275\u0275element"](1,"mat-spinner",22),l["\u0275\u0275elementEnd"]())}function v(t,e){if(1&t){const t=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",23),l["\u0275\u0275elementStart"](1,"div",24),l["\u0275\u0275elementStart"](2,"app-mail-preview",25),l["\u0275\u0275listener"]("attachmentChanges",(function(e){return l["\u0275\u0275restoreView"](t),l["\u0275\u0275nextContext"]().onAttachmentChanges(e)})),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&t){const t=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("mailFormGroup",t.mailFormGroup)("mailFormConfig",t.mailFormFields)("templateMasterData",t.templateMasterData)("fromMailPlaceHolderMasterData",t.fromMailPlaceHolderMasterData)("mailPlaceHolderMasterData",t.mailPlaceHolderMasterData)("placeholdersMasterData",t.placeholdersMasterData)("attachmentCount",t.attachmentCount)("attachmentConfig",t.attachmentConfig)("attachmentPath",t.attachmentPath)("filesUploaded",t.filesUploaded)("previewImg",t.data.previewImg)("companyLogo",t.data.companyLogo)}}const _=function(t){return{"pointer-events":t}};let L=(()=>{class t{constructor(t,e,n,a,r,i,s,l){this.data=t,this._dialogRef=e,this._fb=n,this._atsMasterService=a,this._toaster=r,this._sharedService=i,this._atsJobService=s,this._atsTemplateSettingsService=l,this._onDestroy=new o.b,this.mailFormFields=[],this.templateMasterData=[],this.fromMailPlaceHolderMasterData=[],this.mailPlaceHolderMasterData=[],this.placeholdersMasterData=[],this.attachmentCount=0,this.attachmentConfig={},this.filesUploaded=[],this.attachmentPath=null,this.isLoading=!0,this.mailFormGroup=this._fb.group({})}ngOnInit(){return Object(a.c)(this,void 0,void 0,(function*(){yield this.getAttachmentConfig(),yield this.getAtsFormsConfig("sendCampusInvite"),yield this.createMailForm(),this.initializeFormMaster()}))}initializeFormMaster(){Promise.all([this.getPlaceHolders(),this.getReceiverEmail(),this.getSenderEmail(),this.fetchAllEmailTemplates()]).then(t=>{this.isLoading=!1}).catch(t=>{this.isLoading=!1,this.onClose(!1)})}onClose(t){this._dialogRef.close(t)}onAttachmentChanges(t){this.attachmentCount=t.attachmentCount,this.filesUploaded=t.filesUploaded,this.attachmentPath=t.attachmentPath}createMailForm(){return Object(a.c)(this,void 0,void 0,(function*(){for(let t=0;t<this.mailFormFields.length;t++)this.mailFormGroup.addControl(this.mailFormFields[t].key,this._fb.control(null,[this.mailFormFields[t].isMandatory?r.H.required:null].filter(t=>null!=t))),"signature"==this.mailFormFields[t].key&&this.mailFormGroup.addControl("isSignatureOn",this._fb.control(!1)),this.mailFormGroup.addControl("header",this._fb.control(null))}))}sendMail(){return Object(a.c)(this,void 0,void 0,(function*(){if(this.mailFormGroup.invalid)return void this._toaster.showWarning("Warning \u26a0\ufe0f","Kindly Fill All Mandatory Fields!",7e3);this.isLoading=!0;let t=this.mailFormGroup.getRawValue();t.jobId=this.data.jobId,t.collegeId=this.data.collegeId,t.attachment=this.attachmentPath,t.logo=this.data.companyLogo,t.filter_details=this.data.filterDetails;let e=yield this.sendEmailToCollege(t);this.isLoading=!1,e&&this.onClose(!0)}))}getAtsFormsConfig(t){return Object(a.c)(this,void 0,void 0,(function*(){return new Promise((e,n)=>this._atsMasterService.getAtsFormsConfig(t).pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?(this.mailFormFields=t.data.form[0].formFields,e(!0)):(this._toaster.showError("Error",t.msg,7e3),e(!1))},error:t=>{this._toaster.showError("Error",t.message?t.message:"Forms Configuration Retrieval Failed!",7e3),n()}}))}))}getAttachmentConfig(){return Object(a.c)(this,void 0,void 0,(function*(){return new Promise((t,e)=>this._atsMasterService.getAttachmentConfig(1).pipe(Object(s.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?e.data&&e.data.length>0&&(this.attachmentConfig=e.data[0]):this._toaster.showError("Error",e.msg,7e3),t(!0)},error:t=>{this._toaster.showError("Error",t.message?t.message:"Master Data Retrieval Failed!",7e3),e()}}))}))}getSenderEmail(){return Object(a.c)(this,void 0,void 0,(function*(){return new Promise((t,e)=>this._atsMasterService.getSenderEmail().pipe(Object(s.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?this.fromMailPlaceHolderMasterData=e.data:this._toaster.showError("Error",e.msg,7e3),t(!0)},error:t=>{this._toaster.showError("Error",t.message?t.message:"Master Data Retrieval Failed!",7e3),e()}}))}))}getReceiverEmail(){return Object(a.c)(this,void 0,void 0,(function*(){return new Promise((t,e)=>this._atsMasterService.getReceiverEmail().pipe(Object(s.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?this.mailPlaceHolderMasterData=e.data:this._toaster.showError("Error",e.msg,7e3),t(!0)},error:t=>{this._toaster.showError("Error",t.message?t.message:"Master Data Retrieval Failed!",7e3),e()}}))}))}getPlaceHolders(){return Object(a.c)(this,void 0,void 0,(function*(){return new Promise((t,e)=>this._atsMasterService.getPlaceHolders().pipe(Object(s.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?this.placeholdersMasterData=e.data:this._toaster.showError("Error",e.msg,7e3),t(!0)},error:t=>{this._toaster.showError("Error",t.message?t.message:"Master Data Retrieval Failed!",7e3),e()}}))}))}fetchAllEmailTemplates(){return Object(a.c)(this,void 0,void 0,(function*(){return new Promise((t,e)=>this._atsTemplateSettingsService.fetchAllEmailTemplates(null,null,3).pipe(Object(s.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?this.templateMasterData=e.data:this._toaster.showError("Error",e.msg,7e3),t(!0)},error:t=>{this._toaster.showError("Error",t.message?t.message:"Master Data Retrieval Failed!",7e3),e()}}))}))}sendEmailToCollege(t){return Object(a.c)(this,void 0,void 0,(function*(){return new Promise((e,n)=>this._atsJobService.sendEmailToCollege(t).pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?(this._toaster.showSuccess("Success","E-mail triggered successfully!",7e3),e(!0)):(this._toaster.showError("Error",t.msg,7e3),e(!1))},error:t=>{this._toaster.showError("Error",t.message?t.message:"Master Data Retrieval Failed!",7e3),this.isLoading=!1,n()}}))}))}}return t.\u0275fac=function(e){return new(e||t)(l["\u0275\u0275directiveInject"](i.a),l["\u0275\u0275directiveInject"](i.h),l["\u0275\u0275directiveInject"](r.i),l["\u0275\u0275directiveInject"](c.a),l["\u0275\u0275directiveInject"](m.a),l["\u0275\u0275directiveInject"](d.a),l["\u0275\u0275directiveInject"](h.a),l["\u0275\u0275directiveInject"](g.a))},t.\u0275cmp=l["\u0275\u0275defineComponent"]({type:t,selectors:[["app-send-campus-invitation-dialog"]],decls:25,vars:5,consts:[[1,"main-container"],[1,"d-flex","align-items-center","justify-content-between","header"],[1,"title","p-0"],[1,"d-flex","align-items-center"],["width","269","height","56","viewBox","0 0 269 56","fill","none","xmlns","http://www.w3.org/2000/svg"],["d","M133.603 -22.5168V4.76469C133.603 8.35397 132.087 11.811 129.39 14.4067C125.91 17.7302 120.857 19.1903 115.947 18.2782L115.946 18.2781L93.9778 14.2877L103.74 5.17581L116.468 7.51274L116.468 7.51278C119.364 8.04124 122.088 5.97651 122.088 3.19398V-11.8208L133.603 -22.5168Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M179.019 14.2879L157.079 18.2781L157.078 18.2782C152.174 19.1903 147.127 17.73 143.651 14.4063L143.649 14.4043C140.909 11.856 139.394 8.40031 139.394 4.81089V-22.5164L150.895 -11.8208V2.96299C150.895 5.88334 153.764 8.04193 156.758 7.46638C156.758 7.46632 156.758 7.46626 156.759 7.46621L169.221 5.17578L179.019 14.2879Z","fill","#EAEDF3","stroke","#DADCE2","stroke-width","0.421296"],["d","M133.603 37.6429V64.9403L122.088 54.256V38.8889C122.088 36.2929 119.563 34.3707 116.817 34.8512L116.816 34.8515L103.74 37.2322L93.9781 28.1306L115.946 24.1447L115.947 24.1445C120.857 23.2335 125.91 24.6921 129.391 28.012C132.087 30.6048 133.603 34.0579 133.603 37.6429Z","fill","#EAEDF3","stroke","#DADCE2","stroke-width","0.421296"],["d","M139.394 64.9399V37.6584C139.394 34.0692 140.91 30.6122 143.607 28.0166C147.087 24.693 152.141 23.2328 157.05 24.1448L157.051 24.145L179.018 28.1352L169.258 37.2012L155.933 34.7718C155.933 34.7718 155.933 34.7718 155.933 34.7718C153.335 34.2893 150.91 36.1234 150.91 38.6285V54.2438L139.394 64.9399Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M40.0758 14.7878V42.0694C40.0758 45.6587 38.5596 49.1157 35.8631 51.7114C32.3828 55.0348 27.3294 56.495 22.4198 55.5829L22.419 55.5828L0.45043 51.5924L10.2125 42.4805L22.9402 44.8174L22.9404 44.8175C25.8368 45.3459 28.5604 43.2812 28.5604 40.4987V25.4839L40.0758 14.7878Z","fill","#F3F5FA","stroke","#DADCE2","stroke-width","0.421296"],["d","M85.4914 51.5926L63.5512 55.5828L63.5504 55.5829C58.6468 56.495 53.5996 55.0347 50.1236 51.711L50.1214 51.709C47.3813 49.1606 45.8669 45.705 45.8669 42.1156V14.7883L57.3676 25.4838V40.2677C57.3676 43.188 60.2364 45.3466 63.2304 44.7711C63.2307 44.771 63.231 44.771 63.2313 44.7709L75.6933 42.4805L85.4914 51.5926Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M40.0758 14.4832V41.7647C40.0758 45.354 38.5596 48.811 35.8631 51.4067C32.3828 54.7302 27.3294 56.1903 22.4198 55.2782L22.419 55.2781L0.45043 51.2877L10.2125 42.1758L22.9402 44.5127L22.9404 44.5128C25.8368 45.0412 28.5604 42.9765 28.5604 40.194V25.1792L40.0758 14.4832Z","fill","#EAEDF3","fill-opacity","0.5","stroke","#DADCE2","stroke-width","0.421296"],["d","M85.4914 51.2879L63.5512 55.2781L63.5504 55.2782C58.6468 56.1903 53.5996 54.73 50.1236 51.4063L50.1214 51.4043C47.3813 48.856 45.8669 45.4003 45.8669 41.8109V14.4836L57.3676 25.1792V39.963C57.3676 42.8833 60.2364 45.0419 63.2304 44.4664C63.2307 44.4663 63.231 44.4663 63.2313 44.4662L75.6933 42.1758L85.4914 51.2879Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M227.13 14.7878V42.0694C227.13 45.6587 225.614 49.1157 222.918 51.7114C219.437 55.0348 214.384 56.495 209.475 55.5829L209.474 55.5828L187.505 51.5924L197.267 42.4805L209.995 44.8174L209.995 44.8175C212.892 45.3459 215.615 43.2812 215.615 40.4987V25.4839L227.13 14.7878Z","stroke","#DADCE2","stroke-width","0.421296"],["d","M272.546 51.5926L250.606 55.5828L250.605 55.5829C245.702 56.495 240.654 55.0347 237.178 51.711L237.176 51.709C234.436 49.1606 232.922 45.705 232.922 42.1156V14.7883L244.422 25.4838V40.2677C244.422 43.188 247.291 45.3466 250.285 44.7711C250.285 44.771 250.286 44.771 250.286 44.7709L262.748 42.4805L272.546 51.5926Z","stroke","#DADCE2","stroke-width","0.421296"],[1,"icon",3,"click"],[1,"icon"],["class","d-flex align-items-center justify-content-center loading-screen",4,"ngIf"],["class","main-screen",4,"ngIf"],[1,"d-flex","align-items-center","justify-content-end","footer"],[1,"button",3,"ngStyle","click"],[1,"d-flex","align-items-center","justify-content-center","loading-screen"],["diameter","40",1,"green-spinner"],[1,"main-screen"],[1,"d-flex","flex-column","forms"],[3,"mailFormGroup","mailFormConfig","templateMasterData","fromMailPlaceHolderMasterData","mailPlaceHolderMasterData","placeholdersMasterData","attachmentCount","attachmentConfig","attachmentPath","filesUploaded","previewImg","companyLogo","attachmentChanges"]],template:function(t,e){1&t&&(l["\u0275\u0275elementStart"](0,"div",0),l["\u0275\u0275elementStart"](1,"div",1),l["\u0275\u0275elementStart"](2,"div",2),l["\u0275\u0275text"](3,"Send Email"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](4,"div",3),l["\u0275\u0275elementStart"](5,"div"),l["\u0275\u0275namespaceSVG"](),l["\u0275\u0275elementStart"](6,"svg",4),l["\u0275\u0275element"](7,"path",5),l["\u0275\u0275element"](8,"path",6),l["\u0275\u0275element"](9,"path",7),l["\u0275\u0275element"](10,"path",8),l["\u0275\u0275element"](11,"path",9),l["\u0275\u0275element"](12,"path",10),l["\u0275\u0275element"](13,"path",11),l["\u0275\u0275element"](14,"path",12),l["\u0275\u0275element"](15,"path",13),l["\u0275\u0275element"](16,"path",14),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275namespaceHTML"](),l["\u0275\u0275elementStart"](17,"div",15),l["\u0275\u0275listener"]("click",(function(){return e.onClose(!1)})),l["\u0275\u0275elementStart"](18,"mat-icon",16),l["\u0275\u0275text"](19,"close"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](20,M,2,0,"div",17),l["\u0275\u0275template"](21,v,3,12,"div",18),l["\u0275\u0275elementStart"](22,"div",19),l["\u0275\u0275elementStart"](23,"div",20),l["\u0275\u0275listener"]("click",(function(){return e.sendMail()})),l["\u0275\u0275text"](24," Send "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&t&&(l["\u0275\u0275advance"](20),l["\u0275\u0275property"]("ngIf",e.isLoading),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",!e.isLoading),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngStyle",l["\u0275\u0275pureFunction1"](3,_,e.isLoading?"none":"")))},directives:[p.a,C.NgIf,C.NgStyle,f.c,u.a],styles:[".main-container[_ngcontent-%COMP%]{overflow:hidden}.main-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{width:78%;height:56px;padding:8px 24px;background-color:#f4f4f6;position:absolute;z-index:1}.main-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:18px;font-weight:700;color:#111434}.main-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{font-size:18px;width:18px;height:18px;color:#1c1b1f;cursor:pointer}.main-container[_ngcontent-%COMP%]   .loading-screen[_ngcontent-%COMP%]{margin-top:20%}.main-container[_ngcontent-%COMP%]   .loading-screen[_ngcontent-%COMP%]     .green-spinner circle{stroke:var(--atsprimaryColor)!important}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]{padding:20px 24px;margin-top:56px;margin-bottom:56px}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .forms[_ngcontent-%COMP%]{width:100%;gap:16px}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .forms[_ngcontent-%COMP%]     .mat-slide-toggle-thumb{width:12px!important;height:12px!important;transform:translate(50%,50%)}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .forms[_ngcontent-%COMP%]     .mat-slide-toggle-bar{background-color:#dadce2;border-radius:15px!important;height:16px!important;width:32px!important}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .forms[_ngcontent-%COMP%]     .mat-slide-toggle-thumb-container{top:-4px!important;left:-5px!important}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .forms[_ngcontent-%COMP%]     .mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-bar{background-color:var(--atssecondaryColor)}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .forms[_ngcontent-%COMP%]     .mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-thumb{background-color:#fff}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .forms[_ngcontent-%COMP%]     .mat-checkbox-checked.mat-accent .mat-checkbox-background{background-color:var(--atssecondaryColor)!important}.main-container[_ngcontent-%COMP%]   .main-screen[_ngcontent-%COMP%]   .forms[_ngcontent-%COMP%]     .mat-checkbox-frame{border-color:#b9c0ca!important}.main-container[_ngcontent-%COMP%]   .footer[_ngcontent-%COMP%]{width:78%;height:56px;padding:8px 24px;background-color:#fff;position:absolute;z-index:1;bottom:0}.main-container[_ngcontent-%COMP%]   .footer[_ngcontent-%COMP%]   .button[_ngcontent-%COMP%]{background-color:#111434;border-radius:8px;padding:8px 14px;cursor:pointer;font-family:var(--atsfontFamily);font-size:14px;font-weight:700;color:#fff}"]}),t})()}}]);