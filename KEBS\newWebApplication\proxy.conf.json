{"/api/project/*": {"target": "http://localhost:3001", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/master/*": {"target": "http://localhost:3001", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/acl/*": {"target": "http://localhost:3002", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/itemPayment/*": {"target": "http://localhost:3001", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/invoice/*": {"target": "http://localhost:3001", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/costing/*": {"target": "http://localhost:3001", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/misFunctions/*": {"target": "http://localhost:3039", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/cta/*": {"target": "http://localhost:3001", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/isa/*": {"target": "http://localhost:3021", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/resource/*": {"target": "http://localhost:3001", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/hr/*": {"target": "http://localhost:3001", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/general/*": {"target": "http://localhost:3001", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/migrationEngine/*": {"target": "http://localhost:3001", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/backgroundJobs/*": {"target": "http://localhost:3001", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/accounts/*": {"target": "http://localhost:3011", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/integrationLayer/*": {"target": "http://localhost:3022", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/salesMaster/*": {"target": "http://localhost:3011", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/contacts/*": {"target": "http://localhost:3011", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/leads/*": {"target": "http://localhost:3011", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/opportunity/*": {"target": "http://localhost:3011", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/userExperience/*": {"target": "http://localhost:3001", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/activity/*": {"target": "http://localhost:3011", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/wfPrimary/*": {"target": "http://localhost:3012", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/timesheetv2/*": {"target": "http://localhost:3013", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/tsPrimary/*": {"target": "http://localhost:3013", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/tsSecondary/*": {"target": "http://localhost:3013", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/exPrimary/*": {"target": "http://localhost:3014", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/error/*": {"target": "http://localhost:3001", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/ts_wfh/*": {"target": "http://localhost:3013", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/udrf/*": {"target": "http://localhost:3001", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/sortFilter/*": {"target": "http://localhost:3001", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/appraisal/*": {"target": "http://localhost:3016", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/ams/*": {"target": "http://localhost:3017", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/awards/*": {"target": "http://localhost:3018", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/upload/*": {"target": "http://localhost:3001", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/appBuilder/*": {"target": "http://localhost:3020", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/okr/*": {"target": "http://localhost:3019", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/auth/*": {"target": "http://localhost:3800", "secure": false, "logLevel": "debug"}, "/api/tenant/*": {"target": "http://localhost:3801", "secure": false, "logLevel": "debug"}, "/api/workflowBot/*": {"target": "http://localhost:3001", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/meeting/*": {"target": "http://localhost:3021", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/lms/*": {"target": "http://localhost:3023", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/adminLog/*": {"target": "http://localhost:3001", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/compliance/*": {"target": "http://localhost:3026", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/wf/*": {"target": "http://localhost:3012", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/dmc/*": {"target": "http://localhost:3001", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/collector/*": {"target": "http://localhost:3001", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/bgjPrimary/*": {"target": "http://localhost:3025", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/pmo/*": {"target": "http://localhost:3001", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/onboarding/*": {"target": "http://localhost:3001", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/obPrimary/*": {"target": "http://localhost:3029", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/purchaseRequest/*": {"target": "http://localhost:3750", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/crmNotes/*": {"target": "http://localhost:3011", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/rr/*": {"target": "http://localhost:3040", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/employee360/*": {"target": "http://localhost:3028", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/hubspotBgj/*": {"target": "http://localhost:3042", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/kebshelp/*": {"target": "http://localhost:3010", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/leaveapp/*": {"target": "http://localhost:3751", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/rmg/*": {"target": "http://localhost:3030", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/salesapproval/*": {"target": "http://localhost:3011", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/incident/*": {"target": "http://localhost:3901", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/gdadmin/*": {"target": "http://localhost:3902", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/pm/*": {"target": "http://localhost:3061", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/mmaPrimary/*": {"target": "http://localhost:3034", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/qb/*": {"target": "http://localhost:3033", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/payrollapp/*": {"target": "http://localhost:3752", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/exit/*": {"target": "http://localhost:3031", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/auditLog/*": {"target": "http://localhost:3011", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/kebsintegration/*": {"target": "http://localhost:3035", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/atsai/*": {"target": "http://localhost:5000", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/ats/*": {"target": "http://localhost:3075", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/tsNodeV2Primary/*": {"target": "http://localhost:3036", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/atsCareerWebsite/*": {"target": "http://localhost:3090", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/insights/*": {"target": "http://localhost:3091", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/utilService/*": {"target": "http://localhost:3011", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/employeeCapacity/*": {"target": "http://localhost:3822", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/integration/*": {"target": "http://localhost:3022", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/ux/*": {"target": "http://localhost:3099", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/v2/notifications/*": {"target": "http://localhost:3041", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/notification/*": {"target": "http://localhost:3001", "secure": false, "logLevel": "debug", "changeOrigin": true}, "/api/roleAccess/*": {"target": "http://localhost:3002", "secure": false, "logLevel": "debug", "changeOrigin": true}}