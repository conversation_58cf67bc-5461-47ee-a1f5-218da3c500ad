(window.webpackJsonp=window.webpackJsonp||[]).push([[691,765],{"+Nod":function(t,e,n){"use strict";n.r(e),n.d(e,"TsApprovalsModule",(function(){return un}));var s=n("ofXK"),i=n("tyNb"),o=n("mrSG"),a=n("3Pt+"),r=n("xG9w"),l=(n("DlyV"),n("4PPT"),n("sx9y"),n("wd/R")),c=n("1G5W"),p=n("XNiG"),d=n("1yaQ"),m=n("FKr1"),u=n("Kh6U"),h=n("fXoL"),g=n("9WLo"),f=n("rq16"),v=n("0IaG"),b=n("LcQX"),x=n("XXEo"),y=n("ug40"),C=n("Rnji"),_=n("BVzC"),S=n("JLuW"),w=n("jaxi"),k=n("kmnG"),A=n("NFeN"),I=n("qFsG"),O=n("bTqV"),M=n("Qu3c"),D=n("STbY"),T=n("iadO"),E=n("Xa2L"),P=n("me71"),H=n("lVl8"),j=n("bSwM"),R=n("/QRN");const V=function(t){return{"btn-toggle-selected":t}};function L(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"mat-button-toggle",8),h["\u0275\u0275text"](1),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"]();h["\u0275\u0275property"]("ngClass",h["\u0275\u0275pureFunction1"](2,V,"wfh"==t.selectedToggle)),h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate1"]("Remote WorX (",t.wfhApprovals?t.wfhApprovals.length:0,") ")}}function $(t,e){1&t&&h["\u0275\u0275element"](0,"div",38)}function Y(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",39),h["\u0275\u0275elementStart"](1,"div",40),h["\u0275\u0275element"](2,"div",41),h["\u0275\u0275elementStart"](3,"div",42),h["\u0275\u0275elementStart"](4,"button",43),h["\u0275\u0275elementStart"](5,"mat-icon",44),h["\u0275\u0275listener"]("click",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](2).goToPreviousMonth()})),h["\u0275\u0275text"](6,"keyboard_arrow_left"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](7,"div",45),h["\u0275\u0275elementStart"](8,"span",46),h["\u0275\u0275text"](9,"For : "),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](10,"div",47),h["\u0275\u0275elementStart"](11,"mat-form-field",48),h["\u0275\u0275element"](12,"input",49),h["\u0275\u0275element"](13,"mat-datepicker-toggle",50),h["\u0275\u0275elementStart"](14,"mat-datepicker",51,52),h["\u0275\u0275listener"]("yearSelected",(function(e){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](2).selectYear(e)}))("monthSelected",(function(e){h["\u0275\u0275restoreView"](t);const n=h["\u0275\u0275reference"](15);return h["\u0275\u0275nextContext"](2).selectMonth(e,n)})),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](16,"div",53),h["\u0275\u0275elementStart"](17,"button",54),h["\u0275\u0275elementStart"](18,"mat-icon",44),h["\u0275\u0275listener"]("click",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](2).goToNextMonth()})),h["\u0275\u0275text"](19,"keyboard_arrow_right"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}if(2&t){const t=h["\u0275\u0275reference"](15),e=h["\u0275\u0275nextContext"](2);h["\u0275\u0275advance"](12),h["\u0275\u0275property"]("matDatepicker",t)("formControl",e.monthYearDate),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("for",t)}}function F(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"button",55),h["\u0275\u0275listener"]("click",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](2).clearSearch()})),h["\u0275\u0275elementStart"](1,"mat-icon",56),h["\u0275\u0275text"](2,"close "),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}}function B(t,e){1&t&&(h["\u0275\u0275elementStart"](0,"mat-icon",34),h["\u0275\u0275text"](1,"done"),h["\u0275\u0275elementEnd"]())}function q(t,e){1&t&&h["\u0275\u0275element"](0,"mat-spinner",57)}function U(t,e){1&t&&(h["\u0275\u0275elementStart"](0,"mat-icon",34),h["\u0275\u0275text"](1,"close"),h["\u0275\u0275elementEnd"]())}function W(t,e){1&t&&h["\u0275\u0275element"](0,"mat-spinner",57)}function N(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"button",58),h["\u0275\u0275elementStart"](1,"mat-icon",59),h["\u0275\u0275listener"]("dblclick",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](2).closeAdminSideNav()}))("click",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](2).openAdminSidenav()})),h["\u0275\u0275text"](2,"person"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"](2);h["\u0275\u0275property"]("matTooltip",t.isAdminViewActivated?"Double Click to get back to your view":"Admin View")("ngClass",t.isAdminViewActivated?"view-button-active":"view-button-inactive")}}function z(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",63),h["\u0275\u0275text"](1),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](2,"div",63),h["\u0275\u0275text"](3),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"](3);h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate"](null==t.adminBasedUser?null:t.adminBasedUser.name),h["\u0275\u0275advance"](2),h["\u0275\u0275textInterpolate"](null==t.adminBasedUser?null:t.adminBasedUser.role)}}function X(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"span",60),h["\u0275\u0275element"](1,"app-user-image",61),h["\u0275\u0275template"](2,z,4,2,"ng-template",null,62,h["\u0275\u0275templateRefExtractor"]),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275reference"](3),e=h["\u0275\u0275nextContext"](2);h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("id",e.adminBasedUser?e.adminBasedUser.oid:"")("tooltip",t)}}function G(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"button",64),h["\u0275\u0275elementStart"](1,"mat-icon",44),h["\u0275\u0275listener"]("click",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](2).default()})),h["\u0275\u0275text"](2,"settings_backup_restore"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}}function J(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"button",65),h["\u0275\u0275listener"]("click",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](2).openNewReleasesComponent()})),h["\u0275\u0275elementStart"](1,"mat-icon",66),h["\u0275\u0275text"](2,"new_releases"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"](2);h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngClass",t.notifyReleaseAnimated?"iconButtonWarnAnim":t.notifyRelease?"iconButtonWarn":"iconButton")}}function Q(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"mat-checkbox",76),h["\u0275\u0275listener"]("ngModelChange",(function(e){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](3).selectAllActivated=e}))("change",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](3).selectAll()})),h["\u0275\u0275elementEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"](3);h["\u0275\u0275property"]("ngModel",t.selectAllActivated)}}function K(t,e){1&t&&(h["\u0275\u0275elementStart"](0,"div",73),h["\u0275\u0275text"](1,"Cost center - Region"),h["\u0275\u0275elementEnd"]())}function Z(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",73),h["\u0275\u0275text"](1),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"](3);h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate1"]("",t.tsProperties.ts_ui_text," - Region")}}function tt(t,e){1&t&&(h["\u0275\u0275elementStart"](0,"div",71),h["\u0275\u0275text"](1,"Submitted For"),h["\u0275\u0275elementEnd"]())}function et(t,e){1&t&&(h["\u0275\u0275elementStart"](0,"div",71),h["\u0275\u0275text"](1,"Approvers "),h["\u0275\u0275elementEnd"]())}function nt(t,e){1&t&&(h["\u0275\u0275elementStart"](0,"div",71),h["\u0275\u0275text"](1,"Action Taken"),h["\u0275\u0275elementEnd"]())}function st(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"mat-checkbox",76),h["\u0275\u0275listener"]("ngModelChange",(function(e){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"]().$implicit.checked=e}))("change",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](4).selectIndividual()})),h["\u0275\u0275elementEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"]().$implicit;h["\u0275\u0275property"]("ngModel",t.checked)}}function it(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"span",96),h["\u0275\u0275text"](1),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"]().$implicit;h["\u0275\u0275property"]("matTooltip",t.group_description),h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate1"]("(",t.group_code,")")}}function ot(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",97),h["\u0275\u0275listener"]("click",(function(){h["\u0275\u0275restoreView"](t);const e=h["\u0275\u0275nextContext"]().index;return h["\u0275\u0275nextContext"](3).approvalsCardClicked(!1,e)})),h["\u0275\u0275text"](1),h["\u0275\u0275elementEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"]().$implicit,e=h["\u0275\u0275reference"](15);h["\u0275\u0275property"]("tooltip",e),h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate1"](" ",t.details," ")}}function at(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",98),h["\u0275\u0275listener"]("click",(function(){h["\u0275\u0275restoreView"](t);const e=h["\u0275\u0275nextContext"]().index;return h["\u0275\u0275nextContext"](3).approvalsCardClicked(!1,e)})),h["\u0275\u0275text"](1),h["\u0275\u0275elementEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"]().$implicit;h["\u0275\u0275property"]("matTooltip",t.details),h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate1"](" ",t.details," ")}}function rt(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",63),h["\u0275\u0275text"](1),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](2,"div",63),h["\u0275\u0275text"](3),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"]().$implicit;h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate1"](" Regular Hours : ",t.total_hours-t.ot_hours," hours "),h["\u0275\u0275advance"](2),h["\u0275\u0275textInterpolate1"](" Over Time : ",t.ot_hours," hours ")}}function lt(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"span"),h["\u0275\u0275text"](1),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"]().$implicit;h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate1"](" - ",t.Region," ")}}function ct(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",99),h["\u0275\u0275elementStart"](1,"span",100),h["\u0275\u0275listener"]("click",(function(){h["\u0275\u0275restoreView"](t);const e=h["\u0275\u0275nextContext"]().index;return h["\u0275\u0275nextContext"](3).approvalsCardClicked(!1,e)})),h["\u0275\u0275text"](2),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"]().$implicit;h["\u0275\u0275advance"](2),h["\u0275\u0275textInterpolate2"](" ",t.displayMonth," - ",t.displayYear,"")}}const pt=function(t){return{color:t}};function dt(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",63),h["\u0275\u0275text"](1),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](2,"div",63),h["\u0275\u0275text"](3),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](4,"div",63),h["\u0275\u0275text"](5),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](6,"div",106),h["\u0275\u0275elementStart"](7,"div",107),h["\u0275\u0275text"](8),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](9,"mat-icon",108),h["\u0275\u0275text"](10,"fiber_manual_record"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"]().$implicit,e=h["\u0275\u0275nextContext"](5);h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate"](t.name),h["\u0275\u0275advance"](2),h["\u0275\u0275textInterpolate"](t.role),h["\u0275\u0275advance"](2),h["\u0275\u0275textInterpolate1"]("Level ",t.level,""),h["\u0275\u0275advance"](3),h["\u0275\u0275textInterpolate"](e.getStatusDescription(t.status)),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngStyle",h["\u0275\u0275pureFunction1"](5,pt,e.borderStatusColor(t.status)))}}function mt(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"span"),h["\u0275\u0275element"](1,"app-user-image",104),h["\u0275\u0275template"](2,dt,11,7,"ng-template",105,62,h["\u0275\u0275templateRefExtractor"]),h["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit,n=h["\u0275\u0275reference"](3),s=h["\u0275\u0275nextContext"](5);h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("tooltip",n)("id",t.oid)("borderColor",s.borderStatusColor(t.status))}}function ut(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",101,102),h["\u0275\u0275listener"]("mousedown",(function(e){h["\u0275\u0275restoreView"](t);const n=h["\u0275\u0275reference"](1);return h["\u0275\u0275nextContext"](4).startDragging(e,!1,n)}))("mouseup",(function(e){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](4).stopDragging(e,!1)}))("mouseleave",(function(e){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](4).stopDragging(e,!1)}))("mousemove",(function(e){h["\u0275\u0275restoreView"](t);const n=h["\u0275\u0275reference"](1);return h["\u0275\u0275nextContext"](4).moveEvent(e,n)})),h["\u0275\u0275template"](2,mt,4,3,"span",103),h["\u0275\u0275elementEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"]().$implicit;h["\u0275\u0275advance"](2),h["\u0275\u0275property"]("ngForOf",t.approvers)}}function ht(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"button",112),h["\u0275\u0275listener"]("click",(function(){h["\u0275\u0275restoreView"](t);const e=h["\u0275\u0275nextContext"](2).index;return h["\u0275\u0275nextContext"](3).approveIndividual(!1,e)})),h["\u0275\u0275elementStart"](1,"mat-icon",113),h["\u0275\u0275text"](2,"done"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}}function gt(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"button",114),h["\u0275\u0275listener"]("click",(function(){h["\u0275\u0275restoreView"](t);const e=h["\u0275\u0275nextContext"](2).index;return h["\u0275\u0275nextContext"](3).rejectIndividual(!1,e)})),h["\u0275\u0275elementStart"](1,"mat-icon",113),h["\u0275\u0275text"](2,"close"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}}function ft(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",109),h["\u0275\u0275template"](1,ht,3,0,"button",110),h["\u0275\u0275template"](2,gt,3,0,"button",111),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"](4);h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",(!t.isCurrentDateGreaterThanCutOff||t.isCurrentUserAdmin)&&!t.approveActivated&&!t.rejectActivated),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",(!t.isCurrentDateGreaterThanCutOff||t.isCurrentUserAdmin)&&!t.approveActivated&&!t.rejectActivated)}}function vt(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",115),h["\u0275\u0275element"](1,"span",116),h["\u0275\u0275elementStart"](2,"span",117),h["\u0275\u0275elementStart"](3,"span",118),h["\u0275\u0275text"](4," on "),h["\u0275\u0275elementEnd"](),h["\u0275\u0275text"](5),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"]().$implicit,e=h["\u0275\u0275nextContext"](3);h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngClass","Draft"==t.status?"is-draft":"Submitted"==t.status?"is-submitted":"Approved"==t.status?"is-approved":"Rejected"==t.status?"is-rejected":"Unsubmitted"==t.status?"is-draft":""),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("matTooltip",t.action_on?e.getLocalDateHistory(t.action_on):"-"),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("matTooltip",t.status),h["\u0275\u0275advance"](2),h["\u0275\u0275textInterpolate1"](" ",t.action_on?e.getLocalDateHistory(t.action_on):"-"," ")}}function bt(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",77),h["\u0275\u0275elementStart"](1,"div",78),h["\u0275\u0275elementStart"](2,"div",79),h["\u0275\u0275elementStart"](3,"div",80),h["\u0275\u0275elementStart"](4,"div",81),h["\u0275\u0275elementStart"](5,"div",82),h["\u0275\u0275template"](6,st,1,1,"mat-checkbox",69),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](7,"div",83),h["\u0275\u0275listener"]("click",(function(){h["\u0275\u0275restoreView"](t);const n=e.index;return h["\u0275\u0275nextContext"](3).approvalsCardClicked(!1,n)})),h["\u0275\u0275element"](8,"app-user-image",84),h["\u0275\u0275elementStart"](9,"span",85),h["\u0275\u0275text"](10),h["\u0275\u0275template"](11,it,2,2,"span",86),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](12,ot,2,2,"div",87),h["\u0275\u0275template"](13,at,2,2,"div",88),h["\u0275\u0275template"](14,rt,4,2,"ng-template",null,89,h["\u0275\u0275templateRefExtractor"]),h["\u0275\u0275elementStart"](16,"div",90),h["\u0275\u0275listener"]("click",(function(){h["\u0275\u0275restoreView"](t);const n=e.index;return h["\u0275\u0275nextContext"](3).approvalsCardClicked(!1,n)})),h["\u0275\u0275text"](17),h["\u0275\u0275template"](18,lt,2,1,"span",37),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](19,"div",91),h["\u0275\u0275listener"]("click",(function(){h["\u0275\u0275restoreView"](t);const n=e.index;return h["\u0275\u0275nextContext"](3).approvalsCardClicked(!1,n)})),h["\u0275\u0275text"](20),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](21,ct,3,2,"div",92),h["\u0275\u0275template"](22,ut,3,1,"div",93),h["\u0275\u0275template"](23,ft,3,2,"div",94),h["\u0275\u0275template"](24,vt,6,4,"div",95),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}if(2&t){const t=e.$implicit,n=h["\u0275\u0275nextContext"](3);h["\u0275\u0275advance"](6),h["\u0275\u0275property"]("ngIf",!n.approvalsHistoryActivated),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("matTooltip",n.getLength(t.name?t.name:"a")>15?t.name:""),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("id",t.oid),h["\u0275\u0275advance"](2),h["\u0275\u0275textInterpolate2"]("",t.associate_id," - ",t.name," "),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf","P"!=t.group_code),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",t.ot_hours&&t.ot_hours>0),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!(t.ot_hours&&t.ot_hours>0)),h["\u0275\u0275advance"](3),h["\u0275\u0275property"]("matTooltip",t.toolTip),h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate2"](" #",t.costcenter," - ",t.cc_description," "),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",""!=t.Region),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("matTooltip",t.location),h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate1"](" ",t.location," "),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!n.approvalsHistoryActivated),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!n.approvalsHistoryActivated||n.approvalsHistoryActivated),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!n.approvalsHistoryActivated),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",n.approvalsHistoryActivated)}}function xt(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div"),h["\u0275\u0275elementStart"](1,"div",67),h["\u0275\u0275elementStart"](2,"div",68),h["\u0275\u0275template"](3,Q,1,1,"mat-checkbox",69),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](4,"div",70),h["\u0275\u0275text"](5,"Name"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](6,"div",71),h["\u0275\u0275text"](7,"Details"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](8,K,2,0,"div",72),h["\u0275\u0275template"](9,Z,2,1,"div",72),h["\u0275\u0275elementStart"](10,"div",73),h["\u0275\u0275text"](11,"Location"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](12,tt,2,0,"div",74),h["\u0275\u0275template"](13,et,2,0,"div",74),h["\u0275\u0275template"](14,nt,2,0,"div",74),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](15,bt,25,18,"div",75),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"](2);h["\u0275\u0275advance"](3),h["\u0275\u0275property"]("ngIf",!t.approvalsHistoryActivated),h["\u0275\u0275advance"](5),h["\u0275\u0275property"]("ngIf",0==t.tsProperties.ts_ui_change_and_download_icon),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",1==t.tsProperties.ts_ui_change_and_download_icon),h["\u0275\u0275advance"](3),h["\u0275\u0275property"]("ngIf",!t.approvalsHistoryActivated),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!t.approvalsHistoryActivated||t.approvalsHistoryActivated),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!t.approvalsHistoryActivated||t.approvalsHistoryActivated),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngForOf",t.associateData)}}function yt(t,e){1&t&&(h["\u0275\u0275elementStart"](0,"div",73),h["\u0275\u0275text"](1,"Cost center"),h["\u0275\u0275elementEnd"]())}function Ct(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",73),h["\u0275\u0275text"](1),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"](3);h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate"](t.tsProperties.ts_ui_text)}}function _t(t,e){1&t&&(h["\u0275\u0275elementStart"](0,"div",71),h["\u0275\u0275text"](1,"Approvers "),h["\u0275\u0275elementEnd"]())}function St(t,e){1&t&&(h["\u0275\u0275elementStart"](0,"div",71),h["\u0275\u0275text"](1,"Action Taken"),h["\u0275\u0275elementEnd"]())}function wt(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"mat-checkbox",76),h["\u0275\u0275listener"]("ngModelChange",(function(e){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"]().$implicit.checked=e}))("change",(function(){h["\u0275\u0275restoreView"](t);const e=h["\u0275\u0275nextContext"]().$implicit;return h["\u0275\u0275nextContext"](3).selectAllOfCostCenters(e)})),h["\u0275\u0275elementEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"]().$implicit;h["\u0275\u0275property"]("ngModel",t.checked)}}function kt(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"span",125),h["\u0275\u0275text"](1),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"]().$implicit;h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate1"](" (OT:",t.cc_ot_hours,")")}}function At(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"mat-checkbox",76),h["\u0275\u0275listener"]("ngModelChange",(function(e){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"]().$implicit.checked=e}))("change",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](5).selectIndividual()})),h["\u0275\u0275elementEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"]().$implicit;h["\u0275\u0275property"]("ngModel",t.checked)}}function It(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"span",96),h["\u0275\u0275text"](1),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"]().$implicit;h["\u0275\u0275property"]("matTooltip",t.group_description),h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate1"]("(",t.group_code,")")}}function Ot(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",63),h["\u0275\u0275text"](1),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](2,"div",63),h["\u0275\u0275text"](3),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"]().$implicit;h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate1"](" Regular Hours : ",t.total_hours-t.ot_hours," hours "),h["\u0275\u0275advance"](2),h["\u0275\u0275textInterpolate1"](" Over Time : ",t.ot_hours," hours ")}}function Mt(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"span"),h["\u0275\u0275text"](1),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"]().$implicit;h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate1"](" - ",t.Region," ")}}function Dt(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",130),h["\u0275\u0275listener"]("click",(function(){h["\u0275\u0275restoreView"](t);const e=h["\u0275\u0275nextContext"]().index,n=h["\u0275\u0275nextContext"]().$implicit;return h["\u0275\u0275nextContext"](3).approvalsCardClicked(n.costcenter,e)})),h["\u0275\u0275text"](1),h["\u0275\u0275elementEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"]().$implicit;h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate2"](" ",t.displayMonth," - ",t.year," ")}}function Tt(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",115),h["\u0275\u0275element"](1,"span",116),h["\u0275\u0275elementStart"](2,"span",117),h["\u0275\u0275elementStart"](3,"span",118),h["\u0275\u0275text"](4," on "),h["\u0275\u0275elementEnd"](),h["\u0275\u0275text"](5),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"]().$implicit,e=h["\u0275\u0275nextContext"](4);h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngClass","Draft"==t.status?"is-draft":"Submitted"==t.status?"is-submitted":"Approved"==t.status?"is-approved":"Rejected"==t.status?"is-rejected":"Unsubmitted"==t.status?"is-draft":""),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("matTooltip",t.action_on?e.getLocalDateHistory(t.action_on):"-"),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("matTooltip",t.status),h["\u0275\u0275advance"](2),h["\u0275\u0275textInterpolate1"](" ",t.action_on?e.getLocalDateHistory(t.action_on):"-","")}}function Et(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",63),h["\u0275\u0275text"](1),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](2,"div",63),h["\u0275\u0275text"](3),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](4,"div",63),h["\u0275\u0275text"](5),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](6,"div",106),h["\u0275\u0275elementStart"](7,"div",107),h["\u0275\u0275text"](8),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](9,"mat-icon",108),h["\u0275\u0275text"](10,"fiber_manual_record"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"]().$implicit,e=h["\u0275\u0275nextContext"](6);h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate"](t.name),h["\u0275\u0275advance"](2),h["\u0275\u0275textInterpolate"](t.role),h["\u0275\u0275advance"](2),h["\u0275\u0275textInterpolate1"]("Level ",t.level,""),h["\u0275\u0275advance"](3),h["\u0275\u0275textInterpolate"](e.getStatusDescription(t.status)),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngStyle",h["\u0275\u0275pureFunction1"](5,pt,e.borderStatusColor(t.status)))}}function Pt(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"span"),h["\u0275\u0275element"](1,"app-user-image",104),h["\u0275\u0275template"](2,Et,11,7,"ng-template",105,62,h["\u0275\u0275templateRefExtractor"]),h["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit,n=h["\u0275\u0275reference"](3),s=h["\u0275\u0275nextContext"](6);h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("tooltip",n)("id",t.oid)("borderColor",s.borderStatusColor(t.status))}}function Ht(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",101,102),h["\u0275\u0275listener"]("mousedown",(function(e){h["\u0275\u0275restoreView"](t);const n=h["\u0275\u0275reference"](1);return h["\u0275\u0275nextContext"](5).startDragging(e,!1,n)}))("mouseup",(function(e){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](5).stopDragging(e,!1)}))("mouseleave",(function(e){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](5).stopDragging(e,!1)}))("mousemove",(function(e){h["\u0275\u0275restoreView"](t);const n=h["\u0275\u0275reference"](1);return h["\u0275\u0275nextContext"](5).moveEvent(e,n)})),h["\u0275\u0275template"](2,Pt,4,3,"span",103),h["\u0275\u0275elementEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"]().$implicit;h["\u0275\u0275advance"](2),h["\u0275\u0275property"]("ngForOf",t.approvers)}}function jt(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"button",135),h["\u0275\u0275listener"]("click",(function(){h["\u0275\u0275restoreView"](t);const e=h["\u0275\u0275nextContext"](2).index,n=h["\u0275\u0275nextContext"]().$implicit;return h["\u0275\u0275nextContext"](3).approveIndividual(n.costcenter,e)})),h["\u0275\u0275elementStart"](1,"mat-icon",113),h["\u0275\u0275text"](2,"done"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}}function Rt(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"button",136),h["\u0275\u0275listener"]("click",(function(){h["\u0275\u0275restoreView"](t);const e=h["\u0275\u0275nextContext"](2).index,n=h["\u0275\u0275nextContext"]().$implicit;return h["\u0275\u0275nextContext"](3).rejectIndividual(n.costcenter,e)})),h["\u0275\u0275elementStart"](1,"mat-icon",113),h["\u0275\u0275text"](2,"close"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}}function Vt(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",131),h["\u0275\u0275template"](1,jt,3,0,"button",132),h["\u0275\u0275template"](2,Rt,3,0,"button",133),h["\u0275\u0275elementStart"](3,"button",134),h["\u0275\u0275listener"]("click",(function(){h["\u0275\u0275restoreView"](t);const e=h["\u0275\u0275nextContext"]().index,n=h["\u0275\u0275nextContext"]().$implicit;return h["\u0275\u0275nextContext"](3).showAssociateSummary(n.costcenter,e)})),h["\u0275\u0275elementStart"](4,"mat-icon",113),h["\u0275\u0275text"](5,"show_chart"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"](5);h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",(!t.isCurrentDateGreaterThanCutOff||t.isCurrentUserAdmin)&&!t.approveActivated&&!t.rejectActivated),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",(!t.isCurrentDateGreaterThanCutOff||t.isCurrentUserAdmin)&&!t.approveActivated&&!t.rejectActivated)}}function Lt(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",77),h["\u0275\u0275elementStart"](1,"div",78),h["\u0275\u0275elementStart"](2,"div",79),h["\u0275\u0275elementStart"](3,"div",80),h["\u0275\u0275elementStart"](4,"div",81),h["\u0275\u0275elementStart"](5,"div",82),h["\u0275\u0275template"](6,At,1,1,"mat-checkbox",69),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](7,"div",83),h["\u0275\u0275listener"]("click",(function(){h["\u0275\u0275restoreView"](t);const n=e.index,s=h["\u0275\u0275nextContext"]().$implicit;return h["\u0275\u0275nextContext"](3).approvalsCardClicked(s.costcenter,n)})),h["\u0275\u0275element"](8,"app-user-image",84),h["\u0275\u0275elementStart"](9,"span",85),h["\u0275\u0275text"](10),h["\u0275\u0275template"](11,It,2,2,"span",86),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](12,"div",126),h["\u0275\u0275listener"]("click",(function(){h["\u0275\u0275restoreView"](t);const n=e.index,s=h["\u0275\u0275nextContext"]().$implicit;return h["\u0275\u0275nextContext"](3).approvalsCardClicked(s.costcenter,n)})),h["\u0275\u0275text"](13),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](14,Ot,4,2,"ng-template",null,127,h["\u0275\u0275templateRefExtractor"]),h["\u0275\u0275elementStart"](16,"div",90),h["\u0275\u0275listener"]("click",(function(){h["\u0275\u0275restoreView"](t);const n=e.index,s=h["\u0275\u0275nextContext"]().$implicit;return h["\u0275\u0275nextContext"](3).approvalsCardClicked(s.costcenter,n)})),h["\u0275\u0275text"](17),h["\u0275\u0275template"](18,Mt,2,1,"span",37),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](19,"div",91),h["\u0275\u0275listener"]("click",(function(){h["\u0275\u0275restoreView"](t);const n=e.index,s=h["\u0275\u0275nextContext"]().$implicit;return h["\u0275\u0275nextContext"](3).approvalsCardClicked(s.costcenter,n)})),h["\u0275\u0275text"](20),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](21,Dt,2,2,"div",128),h["\u0275\u0275template"](22,Tt,6,4,"div",95),h["\u0275\u0275template"](23,Ht,3,1,"div",93),h["\u0275\u0275template"](24,Vt,6,2,"div",129),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}if(2&t){const t=e.$implicit,n=h["\u0275\u0275nextContext"](4);h["\u0275\u0275advance"](6),h["\u0275\u0275property"]("ngIf",!n.approvalsHistoryActivated),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("matTooltip",n.getLength(t.name?t.name:"a")>15?t.name:""),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("id",t.oid),h["\u0275\u0275advance"](2),h["\u0275\u0275textInterpolate1"]("",t.name," "),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf","P"!=t.group_code),h["\u0275\u0275advance"](2),h["\u0275\u0275textInterpolate1"](" ",t.details," "),h["\u0275\u0275advance"](3),h["\u0275\u0275property"]("matTooltip",n.getLength(t.toolTip?t.toolTip:"a")>10?t.toolTip:""),h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate2"](" #",t.costcenter," - ",t.cc_description," "),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",""!=t.Region),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("matTooltip",t.location),h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate1"](" ",t.location," "),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!n.approvalsHistoryActivated),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",n.approvalsHistoryActivated),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!n.approvalsHistoryActivated||n.approvalsHistoryActivated),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!n.approvalsHistoryActivated)}}function $t(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div"),h["\u0275\u0275elementStart"](1,"div",121),h["\u0275\u0275elementStart"](2,"div",122),h["\u0275\u0275template"](3,wt,1,1,"mat-checkbox",69),h["\u0275\u0275elementStart"](4,"span",123),h["\u0275\u0275text"](5),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](6,kt,2,1,"span",124),h["\u0275\u0275elementStart"](7,"span",125),h["\u0275\u0275text"](8),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](9,"div"),h["\u0275\u0275template"](10,Lt,25,16,"div",75),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit,n=h["\u0275\u0275nextContext"](3);h["\u0275\u0275advance"](3),h["\u0275\u0275property"]("ngIf",!n.approvalsHistoryActivated),h["\u0275\u0275advance"](2),h["\u0275\u0275textInterpolate4"]("#",t.costcenter," - ",t.cc_name," : ",n.costCenterWiseAssociateData[t.costcenter].length," (",t.cc_total_hours,") "),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",t.cc_ot_hours>0),h["\u0275\u0275advance"](2),h["\u0275\u0275textInterpolate1"](" (",t.cc_wo_count,")"),h["\u0275\u0275advance"](2),h["\u0275\u0275property"]("ngForOf",n.costCenterWiseAssociateData[t.costcenter])}}function Yt(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div"),h["\u0275\u0275elementStart"](1,"div",67),h["\u0275\u0275element"](2,"div",119),h["\u0275\u0275elementStart"](3,"div",70),h["\u0275\u0275text"](4,"Name"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](5,"div",71),h["\u0275\u0275text"](6,"Details"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](7,yt,2,0,"div",72),h["\u0275\u0275template"](8,Ct,2,1,"div",72),h["\u0275\u0275elementStart"](9,"div",73),h["\u0275\u0275text"](10,"Location"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](11,"div",120),h["\u0275\u0275text"](12),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](13,_t,2,0,"div",74),h["\u0275\u0275template"](14,St,2,0,"div",74),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](15,$t,11,8,"div",103),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"](2);h["\u0275\u0275advance"](7),h["\u0275\u0275property"]("ngIf",0==t.tsProperties.ts_ui_change_and_download_icon),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",1==t.tsProperties.ts_ui_change_and_download_icon),h["\u0275\u0275advance"](4),h["\u0275\u0275textInterpolate1"](" ",t.approvalsHistoryActivated?"Action taken":"Submitted For"," "),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!t.approvalsHistoryActivated||t.approvalsHistoryActivated),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!t.approvalsHistoryActivated),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngForOf",t.allCostCenters)}}function Ft(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div"),h["\u0275\u0275elementStart"](1,"div"),h["\u0275\u0275elementStart"](2,"h4",137),h["\u0275\u0275text"](3),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](4,"div",138),h["\u0275\u0275element"](5,"img",139),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"](2);h["\u0275\u0275advance"](3),h["\u0275\u0275textInterpolate1"](" ",t.approvalsHistoryActivated?"No History Found !":"No Pending Approvals !"," ")}}function Bt(t,e){1&t&&(h["\u0275\u0275elementStart"](0,"div",73),h["\u0275\u0275text"](1,"Cost center"),h["\u0275\u0275elementEnd"]())}function qt(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",73),h["\u0275\u0275text"](1),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"](3);h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate"](t.tsProperties.ts_ui_text)}}function Ut(t,e){1&t&&(h["\u0275\u0275elementStart"](0,"div"),h["\u0275\u0275elementStart"](1,"div",141),h["\u0275\u0275elementStart"](2,"div",78),h["\u0275\u0275elementStart"](3,"div",142),h["\u0275\u0275elementStart"](4,"div",80),h["\u0275\u0275elementStart"](5,"div",81),h["\u0275\u0275elementStart"](6,"div",143),h["\u0275\u0275element"](7,"mat-checkbox"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](8,"div",144),h["\u0275\u0275elementStart"](9,"ngx-content-loading",145),h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275element"](10,"g",146),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275namespaceHTML"](),h["\u0275\u0275elementStart"](11,"div",147),h["\u0275\u0275elementStart"](12,"ngx-content-loading",145),h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275element"](13,"g",146),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275namespaceHTML"](),h["\u0275\u0275elementStart"](14,"div",148),h["\u0275\u0275elementStart"](15,"ngx-content-loading",145),h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275element"](16,"g",146),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275namespaceHTML"](),h["\u0275\u0275elementStart"](17,"div",149),h["\u0275\u0275elementStart"](18,"ngx-content-loading",145),h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275element"](19,"g",146),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275namespaceHTML"](),h["\u0275\u0275elementStart"](20,"div",150),h["\u0275\u0275elementStart"](21,"ngx-content-loading",145),h["\u0275\u0275namespaceSVG"](),h["\u0275\u0275element"](22,"g",146),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275namespaceHTML"](),h["\u0275\u0275elementStart"](23,"div",151),h["\u0275\u0275elementStart"](24,"button",152),h["\u0275\u0275elementStart"](25,"mat-icon",113),h["\u0275\u0275text"](26,"done"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](27,"button",153),h["\u0275\u0275elementStart"](28,"mat-icon",113),h["\u0275\u0275text"](29,"close"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()),2&t&&(h["\u0275\u0275advance"](9),h["\u0275\u0275property"]("speed","1500ms")("width",1e3)("height",100)("primaryColor","#ebebeb")("secondaryColor","#f7f7f7"),h["\u0275\u0275advance"](3),h["\u0275\u0275property"]("speed","1500ms")("width",1e3)("height",100)("primaryColor","#ebebeb")("secondaryColor","#f7f7f7"),h["\u0275\u0275advance"](3),h["\u0275\u0275property"]("speed","1500ms")("width",1e3)("height",100)("primaryColor","#ebebeb")("secondaryColor","#f7f7f7"),h["\u0275\u0275advance"](3),h["\u0275\u0275property"]("speed","1500ms")("width",1e3)("height",100)("primaryColor","#ebebeb")("secondaryColor","#f7f7f7"),h["\u0275\u0275advance"](3),h["\u0275\u0275property"]("speed","1500ms")("width",1e3)("height",100)("primaryColor","#ebebeb")("secondaryColor","#f7f7f7"))}function Wt(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div"),h["\u0275\u0275elementStart"](1,"div",140),h["\u0275\u0275element"](2,"div",119),h["\u0275\u0275elementStart"](3,"div",70),h["\u0275\u0275text"](4,"Name"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](5,"div",73),h["\u0275\u0275text"](6,"Details"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](7,Bt,2,0,"div",72),h["\u0275\u0275template"](8,qt,2,1,"div",72),h["\u0275\u0275elementStart"](9,"div",73),h["\u0275\u0275text"](10,"Location"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](11,"div",73),h["\u0275\u0275text"](12,"Submitted For"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](13,Ut,30,25,"div",103),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"](2);h["\u0275\u0275advance"](7),h["\u0275\u0275property"]("ngIf",0==t.cc_config),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",1==t.cc_config),h["\u0275\u0275advance"](5),h["\u0275\u0275property"]("ngForOf",t.loadArr)}}function Nt(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",9),h["\u0275\u0275elementStart"](1,"div",10),h["\u0275\u0275elementStart"](2,"div",11),h["\u0275\u0275elementStart"](3,"span",12),h["\u0275\u0275text"](4," Total : "),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](5,"span",13),h["\u0275\u0275text"](6),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](7,$,1,0,"div",14),h["\u0275\u0275template"](8,Y,20,3,"div",15),h["\u0275\u0275elementStart"](9,"div",16),h["\u0275\u0275elementStart"](10,"mat-form-field",17),h["\u0275\u0275elementStart"](11,"span",18),h["\u0275\u0275elementStart"](12,"mat-icon",19),h["\u0275\u0275text"](13,"search"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](14,"input",20),h["\u0275\u0275listener"]("keyup",(function(e){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"]().applySearch(e.target.value)})),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](15,"mat-icon",21),h["\u0275\u0275template"](16,F,3,0,"button",22),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](17,"div",23),h["\u0275\u0275elementStart"](18,"button",24),h["\u0275\u0275elementStart"](19,"mat-icon",25),h["\u0275\u0275text"](20,"visibility"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](21,"mat-menu",null,26),h["\u0275\u0275elementStart"](23,"button",27),h["\u0275\u0275listener"]("click",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"]().costCenter()})),h["\u0275\u0275text"](24," By CostCenter "),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](25,"button",28),h["\u0275\u0275listener"]("click",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"]().approveAll()})),h["\u0275\u0275template"](26,B,2,0,"mat-icon",29),h["\u0275\u0275template"](27,q,1,0,"mat-spinner",30),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](28,"button",28),h["\u0275\u0275listener"]("click",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"]().rejectAll()})),h["\u0275\u0275template"](29,U,2,0,"mat-icon",29),h["\u0275\u0275template"](30,W,1,0,"mat-spinner",30),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](31,N,3,2,"button",31),h["\u0275\u0275template"](32,X,4,2,"span",32),h["\u0275\u0275elementStart"](33,"button",33),h["\u0275\u0275listener"]("click",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"]().showApprovalsHistory()})),h["\u0275\u0275elementStart"](34,"mat-icon",34),h["\u0275\u0275text"](35,"history"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](36,G,3,0,"button",35),h["\u0275\u0275template"](37,J,3,1,"button",36),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](38,xt,16,7,"div",37),h["\u0275\u0275template"](39,Yt,16,6,"div",37),h["\u0275\u0275template"](40,Ft,6,1,"div",37),h["\u0275\u0275template"](41,Wt,14,3,"div",37),h["\u0275\u0275elementEnd"]()}if(2&t){const t=h["\u0275\u0275reference"](22),e=h["\u0275\u0275nextContext"]();h["\u0275\u0275advance"](6),h["\u0275\u0275textInterpolate1"](" ",e.associateData.length," "),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!e.approvalsHistoryActivated),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",e.approvalsHistoryActivated),h["\u0275\u0275advance"](8),h["\u0275\u0275property"]("ngIf",e.isClearVisible),h["\u0275\u0275advance"](2),h["\u0275\u0275property"]("ngClass",e.costCenterViewActivated?"view-button-active":"view-button-inactive"),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("matMenuTriggerFor",t),h["\u0275\u0275advance"](6),h["\u0275\u0275property"]("matTooltip",e.approveActivated?"Approving":"Approve")("disabled",e.isCurrentDateGreaterThanCutOff&&!e.isCurrentUserAdmin||e.approvalsHistoryActivated||e.approveActivated||e.rejectActivated),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!e.approveActivated),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",e.approveActivated),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("matTooltip",e.rejectActivated?"Rejecting":"Reject")("disabled",e.isCurrentDateGreaterThanCutOff&&!e.isCurrentUserAdmin||e.approvalsHistoryActivated||e.approveActivated||e.rejectActivated),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!e.rejectActivated),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",e.rejectActivated),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!!e.wfProperties&&1==e.wfProperties.can_admin_appr&&(e.isCurrentUserAdmin||e.isCurrentUserApprovalAdmin)),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",e.isAdminViewActivated&&""!=e.adminBasedUser.oid&&""!=e.adminBasedUser.name),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("matTooltip",e.approvalsHistoryActivated?"Show Pending Approvals":"Show Approvals History")("ngClass",e.approvalsHistoryActivated?"view-button-active":"view-button-inactive"),h["\u0275\u0275advance"](3),h["\u0275\u0275property"]("ngIf",e.costCenterViewActivated),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!1),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",e.defaultViewActivated&&e.associateData.length>0),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",e.costCenterViewActivated&&e.associateData.length>0),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",e.noData),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",0==e.associateData.length&&0==e.noData)}}function zt(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"button",55),h["\u0275\u0275listener"]("click",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](2).searchText=""})),h["\u0275\u0275elementStart"](1,"mat-icon",56),h["\u0275\u0275text"](2,"close "),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}}function Xt(t,e){1&t&&(h["\u0275\u0275elementStart"](0,"mat-icon",34),h["\u0275\u0275text"](1,"done"),h["\u0275\u0275elementEnd"]())}function Gt(t,e){1&t&&h["\u0275\u0275element"](0,"mat-spinner",57)}function Jt(t,e){1&t&&(h["\u0275\u0275elementStart"](0,"mat-icon",34),h["\u0275\u0275text"](1,"close"),h["\u0275\u0275elementEnd"]())}function Qt(t,e){1&t&&h["\u0275\u0275element"](0,"mat-spinner",57)}function Kt(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",173),h["\u0275\u0275elementStart"](1,"span"),h["\u0275\u0275elementStart"](2,"b"),h["\u0275\u0275text"](3,"WFH Duration"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](4,"span",174),h["\u0275\u0275text"](5," :"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](6,"span",175),h["\u0275\u0275text"](7),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](8,"div",173),h["\u0275\u0275elementStart"](9,"span"),h["\u0275\u0275elementStart"](10,"b"),h["\u0275\u0275text"](11,"Reporting Days"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](12,"span",176),h["\u0275\u0275text"](13," : "),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](14,"span",175),h["\u0275\u0275text"](15),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](16,"div",173),h["\u0275\u0275elementStart"](17,"span"),h["\u0275\u0275elementStart"](18,"b"),h["\u0275\u0275text"](19,"Reporting Weeks"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](20,"span",177),h["\u0275\u0275text"](21,":"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](22,"span",175),h["\u0275\u0275text"](23),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"]().$implicit;h["\u0275\u0275advance"](7),h["\u0275\u0275textInterpolate1"]("",t.wfhDuration," "),h["\u0275\u0275advance"](8),h["\u0275\u0275textInterpolate1"](" ",t.reportingDays,""),h["\u0275\u0275advance"](8),h["\u0275\u0275textInterpolate"](t.reportingWeeks)}}function Zt(t,e){1&t&&(h["\u0275\u0275elementStart"](0,"mat-icon",113),h["\u0275\u0275text"](1,"done"),h["\u0275\u0275elementEnd"]())}function te(t,e){1&t&&h["\u0275\u0275element"](0,"mat-spinner",57)}function ee(t,e){1&t&&(h["\u0275\u0275elementStart"](0,"mat-icon",113),h["\u0275\u0275text"](1,"close"),h["\u0275\u0275elementEnd"]())}function ne(t,e){1&t&&h["\u0275\u0275element"](0,"mat-spinner",57)}function se(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",77),h["\u0275\u0275elementStart"](1,"div",78),h["\u0275\u0275elementStart"](2,"mat-card",79),h["\u0275\u0275elementStart"](3,"div",80),h["\u0275\u0275elementStart"](4,"div",81),h["\u0275\u0275elementStart"](5,"div",82),h["\u0275\u0275elementStart"](6,"mat-checkbox",164),h["\u0275\u0275listener"]("change",(function(n){h["\u0275\u0275restoreView"](t);const s=e.index;return h["\u0275\u0275nextContext"](3).groupSelect(s),n.stopPropagation()})),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](7,"div",165),h["\u0275\u0275element"](8,"app-user-image",84),h["\u0275\u0275elementStart"](9,"span",166),h["\u0275\u0275listener"]("click",(function(){h["\u0275\u0275restoreView"](t);const n=e.$implicit;return h["\u0275\u0275nextContext"](3).openWfhOverview(n.wfh_id)})),h["\u0275\u0275text"](10),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](11,"div",167),h["\u0275\u0275listener"]("click",(function(){h["\u0275\u0275restoreView"](t);const n=e.$implicit;return h["\u0275\u0275nextContext"](3).openWfhOverview(n.wfh_id)})),h["\u0275\u0275text"](12),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](13,Kt,24,3,"ng-template",null,168,h["\u0275\u0275templateRefExtractor"]),h["\u0275\u0275elementStart"](15,"div",169),h["\u0275\u0275listener"]("click",(function(){h["\u0275\u0275restoreView"](t);const n=e.$implicit;return h["\u0275\u0275nextContext"](3).openWfhOverview(n.wfh_id)})),h["\u0275\u0275text"](16),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](17,"div",170),h["\u0275\u0275listener"]("click",(function(){h["\u0275\u0275restoreView"](t);const n=e.$implicit;return h["\u0275\u0275nextContext"](3).openWfhOverview(n.wfh_id)})),h["\u0275\u0275text"](18),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](19,"div",171),h["\u0275\u0275elementStart"](20,"button",112),h["\u0275\u0275listener"]("click",(function(){h["\u0275\u0275restoreView"](t);const n=e.index;return h["\u0275\u0275nextContext"](3).wfhApproval(n,"A")})),h["\u0275\u0275template"](21,Zt,2,0,"mat-icon",172),h["\u0275\u0275template"](22,te,1,0,"mat-spinner",30),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](23,"button",114),h["\u0275\u0275listener"]("click",(function(){h["\u0275\u0275restoreView"](t);const n=e.index;return h["\u0275\u0275nextContext"](3).wfhApproval(n,"R")})),h["\u0275\u0275template"](24,ee,2,0,"mat-icon",172),h["\u0275\u0275template"](25,ne,1,0,"mat-spinner",30),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}if(2&t){const t=e.$implicit,n=h["\u0275\u0275reference"](14);h["\u0275\u0275advance"](6),h["\u0275\u0275property"]("checked",1==t.isSelected),h["\u0275\u0275advance"](2),h["\u0275\u0275property"]("id",t.associate_oid),h["\u0275\u0275advance"](2),h["\u0275\u0275textInterpolate"](t.employee_name?t.employee_name:" - "),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("tooltip",n),h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate1"](" ",t.duration_selection_text?t.duration_selection_text:" - "," "),h["\u0275\u0275advance"](4),h["\u0275\u0275textInterpolate1"](" ",t.start_date+" to "+t.end_date," "),h["\u0275\u0275advance"](2),h["\u0275\u0275textInterpolate1"](" ",t.submitted_on," "),h["\u0275\u0275advance"](3),h["\u0275\u0275property"]("ngIf",!t.isBeingApproved),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",t.isBeingApproved),h["\u0275\u0275advance"](2),h["\u0275\u0275property"]("ngIf",!t.isBeingRejected),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",t.isBeingRejected)}}function ie(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div"),h["\u0275\u0275elementStart"](1,"div",67),h["\u0275\u0275elementStart"](2,"div",68),h["\u0275\u0275elementStart"](3,"mat-checkbox",160),h["\u0275\u0275listener"]("change",(function(e){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](2).selectAllWfhApprovals(e)})),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](4,"div",70),h["\u0275\u0275text"](5,"Name"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](6,"div",161),h["\u0275\u0275text"](7,"Details"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](8,"div",162),h["\u0275\u0275text"](9,"Duration"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](10,"div",163),h["\u0275\u0275text"](11,"Submitted On"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](12,se,26,11,"div",75),h["\u0275\u0275elementEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"](2);h["\u0275\u0275advance"](12),h["\u0275\u0275property"]("ngForOf",t.wfhApprovals)}}function oe(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",154),h["\u0275\u0275elementStart"](1,"div",10),h["\u0275\u0275elementStart"](2,"div",155),h["\u0275\u0275elementStart"](3,"span",12),h["\u0275\u0275text"](4," Total : "),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](5,"span",13),h["\u0275\u0275text"](6),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275element"](7,"div",156),h["\u0275\u0275elementStart"](8,"div",16),h["\u0275\u0275elementStart"](9,"mat-form-field",157),h["\u0275\u0275elementStart"](10,"span",18),h["\u0275\u0275elementStart"](11,"mat-icon",19),h["\u0275\u0275text"](12,"search"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275element"](13,"input",158),h["\u0275\u0275elementStart"](14,"mat-icon",21),h["\u0275\u0275template"](15,zt,3,0,"button",22),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](16,"div",23),h["\u0275\u0275elementStart"](17,"button",159),h["\u0275\u0275listener"]("click",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"]().approveAllWfhRequests()})),h["\u0275\u0275template"](18,Xt,2,0,"mat-icon",29),h["\u0275\u0275template"](19,Gt,1,0,"mat-spinner",30),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](20,"button",159),h["\u0275\u0275listener"]("click",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"]().rejectAllWfhRequests()})),h["\u0275\u0275template"](21,Jt,2,0,"mat-icon",29),h["\u0275\u0275template"](22,Qt,1,0,"mat-spinner",30),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](23,ie,13,1,"div",37),h["\u0275\u0275elementEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"]();h["\u0275\u0275advance"](6),h["\u0275\u0275textInterpolate1"](" ",t.wfhApprovals.length," "),h["\u0275\u0275advance"](9),h["\u0275\u0275property"]("ngIf",t.isClearVisible),h["\u0275\u0275advance"](3),h["\u0275\u0275property"]("ngIf",!t.overallWfhApprove),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",t.overallWfhApprove),h["\u0275\u0275advance"](2),h["\u0275\u0275property"]("ngIf",!t.overallWfhReject),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",t.overallWfhReject),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",t.wfhApprovals)}}const ae={parse:{dateInput:"MMMM YYYY"},display:{dateInput:"MMMM YYYY",monthYearLabel:"MMMM YYYY",dateA11yLabel:"LL",monthYearA11yLabel:"MMMM YYYY"}};let re=(()=>{class t{constructor(t,e,n,s,i,o,c,d,m,h){this.router=t,this.tsApprovalsService=e,this.wfPrimaryService=n,this.dialog=s,this.utilityService=i,this.loginService=o,this.tsPrimaryService=c,this.wfhService=d,this.errorService=m,this.sharedLazyLoadedComponentsService=h,this._onDestroy=new p.b,this.searchTextControl=new a.j,this.currentUser={},this.adminBasedUser={},this.wfhApprovals=[],this.overallWfhApprove=!1,this.overallWfhReject=!1,this.associateData=[],this.initialList=[],this.allCostCenters=[],this.costCenterWiseAssociateData=[],this.initialCCList=[],this.loadArr=[1,1,1,1,1],this.currentUserGroupDayQuota=[],this.currentUserMetaSubm=[],this.summaryOfTheYear=[],this.isClearVisible=!1,this.noData=!1,this.isloading=!1,this.defaultViewActivated=!1,this.costCenterViewActivated=!1,this.summaryActivated=!1,this.approveActivated=!1,this.rejectActivated=!1,this.selectAllActivated=!1,this.isCurrentUserAdmin=!1,this.isAdminViewActivated=!1,this.isCurrentDateGreaterThanCutOff=!1,this.approvalsHistoryActivated=!1,this.isSingleClick=!1,this.needToShowRemoteWorkx=!1,this.monthYearDate=new a.j(l()),this.monthEndDateVal=31,this.monthEndDate=l().date(this.monthEndDateVal).hour(15).minute(0).second(0).millisecond(0),this.formattedMonthYearDate="",this.formattedMonthYearDateEnd="",this.formattedYearEnd=l().year(),this.selectedToggle="ts",this.slider=document.querySelector(".approversDrag"),this.mouseDown=!1,this.notifyRelease=!1,this.notifyReleaseAnimated=!1,this.isCurrentUserApprovalAdmin=!1,this.cc_config=0,this.getWfhApprovals=()=>{this.selectedToggle="wfh",this.wfhService.getWFHApprovals(this.currentUser.oid).subscribe(t=>{r.map(t,t=>{t.isSelected=!1,t.isBeingApproved=!1,t.isBeingRejected=!1}),this.wfhApprovals=t},t=>{this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Internal Server Error while retrieving WFH Approvals",t&&t.params?t.params:t&&t.error?t.error.params:{})})},this.clearWfhApprovals=()=>{this.selectedToggle="wfh";let t=this.currentUser.oid;this.isClearVisible=!1,document.getElementById("wfhSearchId").value&&(document.getElementById("wfhSearchId").value=null),this.wfhService.getWFHApprovals(t).subscribe(t=>{r.map(t,t=>{t.isSelected=!1,t.isBeingApproved=!1,t.isBeingRejected=!1}),this.wfhApprovals=t},t=>{this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Internal Server Error while retrieving WFH Approvals",t&&t.params?t.params:t&&t.error?t.error.params:{})})},this.selectAllWfhApprovals=t=>(r.map(this.wfhApprovals,e=>{e.isSelected=t.checked}),this.wfhApprovals),this.approveAllWfhRequests=()=>{this.overallWfhApprove=!0;let t=r.where(this.wfhApprovals,{isSelected:!0});r.map(t,t=>{t.status="A",t.approverOid=this.currentUser.oid}),this.wfhService.getWFHApprovalFunc(t).subscribe(t=>(t&&(this.overallWfhApprove=!1,this.getWfhApprovals()),t),t=>{this.overallWfhApprove=!1,this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Unable to update WFH Approver status due to server error",t&&t.params?t.params:t&&t.error?t.error.params:{})})},this.rejectAllWfhRequests=()=>{this.overallWfhReject=!0;let t=r.where(this.wfhApprovals,{isSelected:!0});r.map(t,t=>{t.status="R",t.approverOid=this.currentUser.oid}),this.wfhService.getWFHApprovalFunc(t).subscribe(t=>(t&&(this.overallWfhReject=!1,this.getWfhApprovals()),t),t=>{this.overallWfhReject=!1,this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Unable to update WFH Approver status due to server error",t&&t.params?t.params:t&&t.error?t.error.params:{})})},this.wfhApproval=(t,e)=>{let n=this.wfhApprovals[t];"A"==e?this.wfhApprovals[t].isBeingApproved=!0:"R"==e&&(this.wfhApprovals[t].isBeingRejected=!0),n.status=e,n.approverOid=this.currentUser.oid,n=[n],this.wfhService.getWFHApprovalFunc(n).subscribe(n=>(n&&(this.getWfhApprovals(),"A"==e?this.wfhApprovals[t].isBeingApproved=!1:"R"==e&&(this.wfhApprovals[t].isBeingRejected=!1)),n),n=>{"A"==e?this.wfhApprovals[t].isBeingApproved=!1:"R"==e&&(this.wfhApprovals[t].isBeingRejected=!1),this.errorService.userErrorAlert(n&&n.code?n.code:n&&n.error?n.error.code:"NIL","Unable to update WFH Approver status due to server error",n&&n.params?n.params:n&&n.error?n.error.params:{})})},this.groupSelect=t=>{this.wfhApprovals[t].isSelected=!this.wfhApprovals[t].isSelected},this.openWfhOverview=t=>{this.dialog.open(u.a,{height:"99%",width:"86%",maxWidth:"86%",data:t})}}ngOnInit(){this.defaultViewActivated=!0,this.currentUser=this.loginService.getProfile().profile,this.getTimesheetWorkflowProperties(),this.getTimesheetProperties(),this.checkIfCurrentUserAdmin(),this.adminBasedUser=sessionStorage.getItem("approverAdmin")?JSON.parse(sessionStorage.getItem("approverAdmin")):{},this.adminBasedUser.oid?(this.currentUserOid=this.adminBasedUser.oid,this.isAdminViewActivated=!0):(this.currentUserOid=this.currentUser.oid,this.isAdminViewActivated=!1),"ts"==this.selectedToggle?this.getPendingApprovals():this.getWfhApprovals()}selectMonth(t,e){const n=this.monthYearDate.value;n.month()!=t.month()&&(n.month(t.month()).year(t.year()).date(1),this.changeMonthYear(n)),e.close()}selectYear(t){const e=this.monthYearDate.value;e.year()!=t.year()&&(e.year(t.year()),this.changeMonthYear(e))}changeMonthYear(t){this.monthYearDate.setValue(t),this.monthEndDateVal="END"==this.tsProperties.month_end_date?"END":parseInt(this.tsProperties.month_end_date),this.calculateFormattedDates(this.monthYearDate.value),this.getHistoryOfApprovals()}calculateFormattedDates(t){"END"!=this.monthEndDateVal?(this.monthEndDate=l(t).date(this.monthEndDateVal).hour(15).minute(0).second(0).millisecond(0),this.formattedMonthYearDate=l(t).subtract(1,"months").date(this.monthEndDateVal+1).hour(15).minute(0).second(0).millisecond(0).format("YYYY-MM-DD"),this.formattedMonthYearDateEnd=l(t).date(this.monthEndDateVal).hour(15).minute(0).second(0).millisecond(0).format("YYYY-MM-DD"),this.formattedYearEnd=l(t).date(this.monthEndDateVal).hour(15).minute(0).second(0).millisecond(0).year()):(this.monthEndDate=l(t).date(l().daysInMonth()).hour(15).minute(0).second(0).millisecond(0),this.formattedMonthYearDate=l(t).hour(15).minute(0).second(0).millisecond(0).format("YYYY-MM-DD"),this.formattedMonthYearDateEnd=l(t).date(l().daysInMonth()).hour(15).minute(0).second(0).millisecond(0).format("YYYY-MM-DD"),this.formattedYearEnd=l(t).date(l().daysInMonth()).hour(15).minute(0).second(0).millisecond(0).year())}goToPreviousMonth(){this.changeMonthYear(this.monthYearDate.value.subtract(1,"M"))}goToNextMonth(){this.changeMonthYear(this.monthYearDate.value.add(1,"M"))}checkIfCurrentUserAdmin(){this.tsPrimaryService.checkIfTsAdmin(this.currentUser.oid).pipe(Object(c.a)(this._onDestroy)).subscribe(t=>Object(o.c)(this,void 0,void 0,(function*(){"S"==t.messType&&"Admin"==t.messText?this.isCurrentUserAdmin=!0:"S"==t.messType&&"Approver Admin"==t.messText?this.isCurrentUserApprovalAdmin=!0:this.isCurrentUserAdmin=!1})),t=>{this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Error while checking current user is an Admin",t&&t.params?t.params:t&&t.error?t.error.params:{})})}getTimesheetWorkflowProperties(){return Object(o.c)(this,void 0,void 0,(function*(){this.wfPrimaryService.getWorkflowProperties({applicationId:37}).pipe(Object(c.a)(this._onDestroy)).subscribe(t=>Object(o.c)(this,void 0,void 0,(function*(){"S"==t.messType&&t.data.length>0?this.wfProperties=r.where(t.data,{sub_application_id:"R"})[0]:this.utilityService.showMessage(t.messText,"Dismiss",3e3)})),t=>{this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Error while retrieving Workflow Properties",t&&t.params?t.params:t&&t.error?t.error.params:{})})}))}getTimesheetProperties(){this.tsPrimaryService.getTimesheetProperties(this.currentUser.aid).pipe(Object(c.a)(this._onDestroy)).subscribe(t=>Object(o.c)(this,void 0,void 0,(function*(){"S"==t.messType&&t.data.length>0?(this.tsProperties=t.data[0],this.cc_config=this.tsProperties.ts_ui_change_and_download_icon,this.needToShowRemoteWorkx=1==this.tsProperties.need_to_show_remote_works,this.isCurrentDateGreaterThanCutOff=l().date()==this.tsProperties.monthly_appr_cut_off_date?l().hour()>this.tsProperties.monthly_appr_cut_off_hour||l().hour()==this.tsProperties.monthly_appr_cut_off_hour&&l().minute()>this.tsProperties.monthly_appr_cut_off_minute:l().date()>this.tsProperties.monthly_appr_cut_off_date&&!this.tsProperties.is_payroll_frozen):this.utilityService.showMessage(t.messText,"Dismiss",3e3)})),t=>{this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Error while retrieving the Timesheet Properties",t&&t.params?t.params:t&&t.error?t.error.params:{})})}getPendingApprovals(){this.associateData=[],this.initialList=[],this.initialCCList=[],this.noData=!1,this.tsApprovalsService.getPendingApprovals(this.currentUserOid,null,null,null).pipe(Object(c.a)(this._onDestroy)).subscribe(t=>Object(o.c)(this,void 0,void 0,(function*(){"S"==t.messType&&t.data.length>0?(this.noData=!1,this.associateData=t.data,this.approvalUIValue=t.monthDateForApprovals,this.resolveAssociateData(),this.groupCostCentreData(!1)):(this.noData=!0,this.utilityService.showMessage(t.messText,"Dismiss",3e3))})),t=>{this.noData=!0,this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Error in getting pending Approvals",t&&t.params?t.params:t&&t.error?t.error.params:{})})}costCenter(){this.defaultViewActivated=!1,this.costCenterViewActivated=!0,console.log(this.allCostCenters),console.log(this.costCenterWiseAssociateData)}groupCostCentreData(t){if(t){this.associateData=[];for(let t=0;t<this.allCostCenters.length;t++)for(let e=0;e<this.costCenterWiseAssociateData[this.allCostCenters[t].costcenter].length;e++)this.associateData.push(this.costCenterWiseAssociateData[this.allCostCenters[t].costcenter][e]);this.initialList=this.associateData}this.allCostCenters=[],this.costCenterWiseAssociateData=[];let e=r.uniq(r.pluck(this.associateData,"costcenter"));r.each(e,t=>{let e=r.where(this.associateData,{costcenter:t}),n=0,s=0,i=0;r.each(e,t=>{n+=t.total_hours,i+=t.ot_hours?t.ot_hours:0,s+=t.wo_count}),this.allCostCenters.push({costcenter:t,cc_name:e[0].cc_description,cc_total_hours:n+" hours",cc_ot_hours:i,cc_wo_count:s+" WO",checked:!1})}),this.costCenterWiseAssociateData=r.groupBy(this.associateData,t=>t.costcenter),this.initialCCList=this.costCenterWiseAssociateData,console.log(this.costCenterWiseAssociateData),this.noData=0==this.associateData.length}showSummary(){this.summaryActivated=!this.summaryActivated}approveAll(){let t=[];if(this.defaultViewActivated)t=r.where(this.associateData,{checked:!0});else for(let e=0;e<this.allCostCenters.length;e++)for(let n=0;n<this.costCenterWiseAssociateData[this.allCostCenters[e].costcenter].length;n++)this.costCenterWiseAssociateData[this.allCostCenters[e].costcenter][n].checked&&t.push(this.costCenterWiseAssociateData[this.allCostCenters[e].costcenter][n]);0==t.length?(this.rejectActivated=!1,this.approveActivated=!1,this.utilityService.showMessage("Please select associates you want to Approve","Dismiss",3e3)):(this.updateTsDetails(t,"A",null),this.filterCards(),this.utilityService.showMessage("Approved Successfully","Dismiss",3e3))}rejectAll(){return Object(o.c)(this,void 0,void 0,(function*(){let t=[];if(this.defaultViewActivated)t=r.where(this.associateData,{checked:!0});else for(let e=0;e<this.allCostCenters.length;e++)for(let n=0;n<this.costCenterWiseAssociateData[this.allCostCenters[e].costcenter].length;n++)this.costCenterWiseAssociateData[this.allCostCenters[e].costcenter][n].checked&&t.push(this.costCenterWiseAssociateData[this.allCostCenters[e].costcenter][n]);if(0==t.length)this.approveActivated=!1,this.rejectActivated=!1,this.utilityService.showMessage("Please select associates you want to reject","Dismiss",3e3);else{r.each(t,t=>{t.isForWeek=!1,t.isForMonth=!0});const{TsApprovalsRejectModalComponent:e}=yield n.e(194).then(n.bind(null,"FoXP"));this.dialog.open(e,{width:"80%",height:"auto",maxHeight:"100vh",disableClose:!0,data:{associateData:t}}).afterClosed().subscribe(e=>{if("Submit"==e.event){let n=[];r.each(e.data.reason,t=>{n.push({oid:this.currentUser.oid,name:this.currentUser.name,status:"Rejected",text:t,date:l().utc().format("DD - MMM - YYYY HH : mm : ss")})}),this.selectAllActivated=!1,this.updateTsDetails(t,"R",n),this.filterCards(),this.utilityService.showMessage("Rejected Successfully","Dismiss",3e3)}})}}))}filterCards(){let t=[];for(let e=0;e<this.associateData.length;e++)this.associateData[e].checked&&t.push(e);for(let e=t.length-1;e>=0;e--)this.associateData.splice(t[e],1);if(r.each(this.associateData,t=>{t.checked=!1}),r.each(this.allCostCenters,t=>{t.checked=!1}),this.costCenterViewActivated)for(let e=0;e<this.allCostCenters.length;e++){let t=[];for(let n=0;n<this.costCenterWiseAssociateData[this.allCostCenters[e].costcenter].length;n++)this.costCenterWiseAssociateData[this.allCostCenters[e].costcenter][n].checked&&t.push(n);for(let n=t.length-1;n>=0;n--)this.costCenterWiseAssociateData[this.allCostCenters[e].costcenter].splice(t[n],1)}this.groupCostCentreData(this.costCenterViewActivated)}default(){this.defaultViewActivated=!0,this.costCenterViewActivated=!1,this.noData=0==this.associateData.length}selectAll(){r.each(this.associateData,this.selectAllActivated?t=>{t.checked=!0}:t=>{t.checked=!1})}selectAllOfCostCenters(t){r.each(this.costCenterWiseAssociateData[t.costcenter],t.checked?t=>{t.checked=!0}:t=>{t.checked=!1})}approvalsCardClicked(t,e){if(this.approvalsHistoryActivated){this.tsApprovalsService.viewType="History",this.tsApprovalsService.emitData(this.associateData);let n=[t?this.costCenterWiseAssociateData[t][e]:this.associateData[e]],s=this.isAdminViewActivated?1:0,i=this.isCurrentUserAdmin;this.router.navigateByUrl("/main/timesheet/approvals/"+this.encodeURIComponent(n[0].oid)+"/"+this.encodeURIComponent(n[0].name)+"/"+this.encodeURIComponent(n[0].costcenter)+"/"+n[0].month+"/"+n[0].year+"/"+this.encodeURIComponent(n[0].location)+"/"+s+"/"+i+"/"+this.encodeURIComponent(this.currentUserOid)+"/timesheetDetails")}else{this.tsApprovalsService.viewType="Approval",this.tsApprovalsService.emitData(this.associateData);let n=[t?this.costCenterWiseAssociateData[t][e]:this.associateData[e]],s=this.isAdminViewActivated?1:0,i=this.isCurrentUserAdmin;this.router.navigateByUrl("/main/timesheet/approvals/"+this.encodeURIComponent(n[0].oid)+"/"+this.encodeURIComponent(n[0].name)+"/"+this.encodeURIComponent(n[0].costcenter)+"/"+n[0].month+"/"+n[0].displayYear+"/"+this.encodeURIComponent(n[0].location)+"/"+s+"/"+i+"/"+this.encodeURIComponent(this.currentUserOid)+"/timesheetDetails")}}encodeURIComponent(t){return encodeURIComponent(t).replace(/[!'()*]/g,(function(t){return"%"+t.charCodeAt(0).toString(16)}))}getLength(t){return t.length}approveIndividual(t,e){this.updateTsDetails([t?this.costCenterWiseAssociateData[t][e]:this.associateData[e]],"A",null),t?this.costCenterWiseAssociateData[t].splice(e,1):this.associateData.splice(e,1),this.groupCostCentreData(t),this.utilityService.showMessage("Approved Successfully","Dismiss",3e3)}rejectIndividual(t,e){return Object(o.c)(this,void 0,void 0,(function*(){let s=[t?this.costCenterWiseAssociateData[t][e]:this.associateData[e]];r.each(s,t=>{t.isForWeek=!1,t.isForMonth=!0});const{TsApprovalsRejectModalComponent:i}=yield n.e(194).then(n.bind(null,"FoXP"));this.dialog.open(i,{width:"80%",height:"auto",maxWidth:"100vw",maxHeight:"100vh",disableClose:!0,data:{associateData:s}}).afterClosed().subscribe(n=>{if("Submit"==n.event){let i=[];r.each(n.data.reason,t=>{i.push({oid:this.currentUser.oid,name:this.currentUser.name,status:"Rejected",text:t,date:l().utc().format("DD - MMM - YYYY HH : mm : ss")})}),this.selectAllActivated=!1,this.updateTsDetails(s,"R",i),t?this.costCenterWiseAssociateData[t].splice(e,1):this.associateData.splice(e,1),this.groupCostCentreData(t),this.utilityService.showMessage("Rejected Successfully","Dismiss",3e3)}})}))}updateTsDetails(t,e,n){this.tsApprovalsService.updateTsForMonth(t,this.currentUserOid,e,n,this.isAdminViewActivated?1:0,this.currentUser.oid).pipe(Object(c.a)(this._onDestroy)).subscribe(t=>Object(o.c)(this,void 0,void 0,(function*(){})),t=>{this.rejectActivated=!1,this.approveActivated=!1})}seeComment(t,e){}selectIndividual(){if(this.defaultViewActivated){let t=[];r.each(this.associateData,e=>{t.push(e.checked)}),this.selectAllActivated=!r.contains(t,!1)}else for(let t=0;t<this.allCostCenters.length;t++){let e=[];for(let n=0;n<this.costCenterWiseAssociateData[this.allCostCenters[t].costcenter].length;n++)e.push(this.costCenterWiseAssociateData[this.allCostCenters[t].costcenter][n].checked);this.allCostCenters[t].checked=!r.contains(e,!1)}}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}applySearch(t){var e=new RegExp(t,"i");if(this.costCenterViewActivated)if(t){this.isClearVisible=!0,this.initialCCList=r.groupBy(this.associateData,t=>t.costcenter);let t=!1,n=!1;r.each(this.allCostCenters,s=>{this.costCenterWiseAssociateData[s.costcenter]=r.filter(this.initialCCList[s.costcenter],t=>t.name&&e.test(t.name)||t.costcenter&&e.test(t.costcenter)||t.cc_description&&e.test(t.cc_description)||t.location&&e.test(t.location)||t.displayMonth&&e.test(t.displayMonth)||t.year&&e.test(t.year)),0==this.costCenterWiseAssociateData[s.costcenter].length?n=!0:t=!0}),this.noData=!t}else this.isClearVisible=!1,this.costCenterWiseAssociateData=r.groupBy(this.associateData,t=>t.costcenter),this.initialCCList=this.costCenterWiseAssociateData,this.noData=!1;else this.initialList.length>0&&(t?(this.isClearVisible=!0,this.associateData=r.filter(this.initialList,t=>t.name&&e.test(t.name)||t.costcenter&&e.test(t.costcenter)||t.cc_description&&e.test(t.cc_description)||t.location&&e.test(t.location)||t.displayMonth&&e.test(t.displayMonth)||t.year&&e.test(t.year)),this.noData=0==this.associateData.length):(this.isClearVisible=!1,this.associateData=this.initialList,this.noData=!1))}clearSearch(){this.costCenterViewActivated?this.costCenterWiseAssociateData=this.initialCCList:this.associateData=this.initialList,this.noData=0==this.associateData.length,this.isClearVisible=!1,document.getElementById("searchId").value=null}openAdminSidenav(){this.isSingleClick=!0,setTimeout(()=>Object(o.c)(this,void 0,void 0,(function*(){if(this.isSingleClick){let t={currentView:"Approvals",isApproverAdmin:this.isCurrentUserApprovalAdmin};const{TsAdminViewModalComponent:e}=yield Promise.all([n.e(4),n.e(0),n.e(765)]).then(n.bind(null,"Fh+A"));this.dialog.open(e,{height:"100%",width:"50%",position:{right:"0px"},data:{modalParams:t}}).afterClosed().subscribe(t=>{"Submit"==t.event&&this.adminBasedUser.oid!=t.data.adminViewResponse.oid&&(this.adminBasedUser={},this.adminBasedUser=t.data.adminViewResponse,this.currentUserOid=this.adminBasedUser.oid,this.isAdminViewActivated=!0,sessionStorage.setItem("approverAdmin",JSON.stringify(this.adminBasedUser)),this.approvalsHistoryActivated||this.getPendingApprovals())})}})),250)}closeAdminSideNav(){this.isSingleClick=!1,this.currentUserOid!=this.currentUser.oid&&(this.adminBasedUser={},this.currentUserOid=this.currentUser.oid,this.approvalsHistoryActivated||this.getPendingApprovals(),sessionStorage.removeItem("approverAdmin"),this.isAdminViewActivated=!1)}getLocalDate(t){return"-"==t?t:l(t,"DD - MMM - YYYY HH : mm : ss").utc(t).local().format("DD - MMM - YYYY HH : mm : ss")}borderStatusColor(t){return"S"==t?"#ff7200":"A"==t||"E"==t?"#009432":"R"==t?"#af0505":"white"}getStatusDescription(t){return"S"==t?"Awaiting Approval":"A"==t?"Approved":"E"==t?"Escalated":"R"==t?"Rejected":""}resolveAssociateData(){for(let t=0;t<this.associateData.length;t++)this.associateData[t].total_hours=Math.round(100*this.associateData[t].total_hours)/100,this.associateData[t].details=this.associateData[t].total_hours+" hours",this.associateData[t].displayMonth=l().month(this.associateData[t].month_sub-1).format("MMMM"),this.associateData[t].year=this.associateData[t].display_year,this.associateData[t].displayYear=this.associateData[t].display_year,this.associateData[t].checked=!1,this.associateData[t].ot_hours>0&&(this.associateData[t].details=this.associateData[t].details+" (OT: "+this.associateData[t].ot_hours+")"),this.associateData[t].leave_count>0&&(this.associateData[t].details=this.associateData[t].details+","+this.associateData[t].leave_count+" L"),this.associateData[t].fh_count>0&&(this.associateData[t].details=this.associateData[t].details+","+this.associateData[t].fh_count+" FH"),this.associateData[t].wo_count>0&&(this.associateData[t].details=this.associateData[t].details+","+this.associateData[t].wo_count+" WO"),this.associateData[t].cl_count>0&&(this.associateData[t].details=this.associateData[t].details+","+this.associateData[t].cl_count+" CL"),this.associateData[t].pl_count>0&&(this.associateData[t].details=this.associateData[t].details+","+this.associateData[t].pl_count+" PL"),this.associateData[t].hc_count>0&&(this.associateData[t].details=this.associateData[t].details+","+this.associateData[t].hc_count+" H-C"),this.associateData[t].hp_count>0&&(this.associateData[t].details=this.associateData[t].details+","+this.associateData[t].hp_count+" H-P"),this.associateData[t].oh_count>0&&(this.associateData[t].details=this.associateData[t].details+","+this.associateData[t].oh_count+" OH");this.initialList=this.associateData}showApprovalsHistory(){this.approvalsHistoryActivated=!this.approvalsHistoryActivated,this.approvalsHistoryActivated?this.changeMonthYear(l()):this.getPendingApprovals()}getHistoryOfApprovals(){this.associateData=[],this.initialList=[],this.initialCCList=[],this.noData=!1,this.tsApprovalsService.getHistoryOfApprovals(this.currentUserOid,this.formattedMonthYearDate,this.formattedMonthYearDateEnd,null).pipe(Object(c.a)(this._onDestroy)).subscribe(t=>Object(o.c)(this,void 0,void 0,(function*(){"S"==t.messType&&t.data.length>0?(this.noData=!1,this.associateData=t.data,this.resolveAssociateData(),this.groupCostCentreData(!1)):(this.noData=!0,this.utilityService.showMessage(t.messText,"Dismiss",3e3))})),t=>{this.noData=!0,this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Error while getting approval histories",t&&t.params?t.params:t&&t.error?t.error.params:{})})}showErrorMessage(t){this.utilityService.showErrorMessage(t,"KEBS or HR")}showAssociateSummary(t,e){this.getAssocMetaSubm(t?this.costCenterWiseAssociateData[t][e]:this.associateData[e])}getAssocMetaSubm(t){this.tsPrimaryService.getAssocMetaSubm(t.oid,this.formattedMonthYearDateEnd).pipe(Object(c.a)(this._onDestroy)).subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){this.currentUserMetaSubm="S"==e.messType&&e.data.length>0?e.data:[],this.getAssocGroupDayQuota(t)})),t=>{this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Error while retrieving the Timesheet Properties",t&&t.params?t.params:t&&t.error?t.error.params:{})})}getAssocGroupDayQuota(t){this.tsPrimaryService.getAssocGroupDayQuota(t.oid).pipe(Object(c.a)(this._onDestroy)).subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){if("S"==e.messType&&e.data.length>0){this.currentUserGroupDayQuota=e.data;let s=r.where(this.currentUserMetaSubm,{day_type:"l"}),i=0;for(let t of s)i+=parseFloat(t.value);let o=r.where(this.currentUserMetaSubm,{day_type:"lb"}),a=0;if(o.length>0){let t=r.sortBy(o,(function(t){return t}));t=t.reverse(),a=t[0].value}let l=r.where(this.currentUserMetaSubm,{day_type:"cf"}),c=0;if(l.length>0){let t=r.sortBy(l,(function(t){return t}));t=t.reverse(),c=t[0].value}a+=c;let p=r.where(this.currentUserGroupDayQuota,{day_type:"fh",interval_code:"y"}),d=p[0]?parseFloat(p[0].quota):0,m=r.where(this.currentUserMetaSubm,{day_type:"fh"}),u=0;for(let t of m)u+=parseFloat(t.value);let h=r.where(this.currentUserMetaSubm,{day_type:"ch"});for(let t of h)u+=parseFloat(t.value);let g=r.where(this.currentUserGroupDayQuota,{day_type:"co",interval_code:"y"}),f=g[0]?parseFloat(g[0].quota):0,v=r.where(this.currentUserMetaSubm,{day_type:"co"}),b=0;for(let t of v)b+=parseFloat(t.value);this.summaryOfTheYear=[];let x=0,y=0,C=r.where(this.currentUserMetaSubm,{day_type:"s"});for(let t of C)this.summaryOfTheYear.push(t),x+=t.week?t.week:0,y+=t.value?t.value:0;let _={sidenavName:"Submission-Summary-Modal",summaryOfTheYear:this.summaryOfTheYear,associateName:t.name?t.name:"",currentAssociateOId:t.oid,currentYear:this.formattedYearEnd,yearly_hours:y,yearly_wh:x,yearly_leave_taken:i,overall_leave_balance:a,yearly_fh_taken_quota:u+" / "+d,yearly_co_taken_quota:b+" / "+f};const{TsSubmissionSummaryModalComponent:S}=yield n.e(773).then(n.bind(null,"hHH2"));this.dialog.open(S,{height:"100%",width:"95%",position:{right:"0px"},data:{modalParams:_}})}else this.utilityService.showMessage(e.messText,"Dismiss",3e3)})),t=>{this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Error while retrieving the Timesheet Properties",t&&t.params?t.params:t&&t.error?t.error.params:{})})}wfhApprovalSearch(t){let e=this.currentUser.oid;this.isClearVisible=!0,this.wfhApprovalsSubscription&&this.wfhApprovalsSubscription.unsubscribe(),this.wfhApprovalsSubscription=this.wfhService.searchInMyApprovals(t,e).subscribe(t=>{r.map(t,t=>{t.isSelected=!1,t.isBeingApproved=!1,t.isBeingRejected=!1}),this.wfhApprovals=t},t=>{this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Data not found while searching in my approvals",t&&t.params?t.params:t&&t.error?t.error.params:{})})}selectToggle(t){this.selectedToggle=t.value,"wfh"==this.selectedToggle?this.getWfhApprovals():this.getPendingApprovals()}startDragging(t,e,n){console.log(n),this.mouseDown=!0,this.startX=t.pageX-n.offsetLeft,this.scrollLeft=n.scrollLeft}stopDragging(t,e){this.mouseDown=!1}moveEvent(t,e){t.preventDefault(),this.mouseDown&&(e.scrollLeft=this.scrollLeft-(t.pageX-e.offsetLeft-this.startX))}getLocalDateHistory(t){return"-"==t?t:l(t,"DD - MMM - YYYY HH : mm : ss").utc(t).local().format("DD - MM - YY HH : mm : ss")}openNewReleasesComponent(){return Object(o.c)(this,void 0,void 0,(function*(){this.notifyRelease=Boolean(yield this.sharedLazyLoadedComponentsService.openNewReleasesModal(100037,this.dialog))}))}getNotifyReleases(){return Object(o.c)(this,void 0,void 0,(function*(){this.sharedLazyLoadedComponentsService.getNotifyReleases(100037).subscribe(t=>Object(o.c)(this,void 0,void 0,(function*(){localStorage.getItem("Application100037Version")!=t.data.version&&(this.notifyReleaseAnimated=!0,yield this.openNewReleasesComponent(),this.notifyReleaseAnimated=!1,this.notifyRelease=t.data._id,localStorage.setItem("Application100037Version",t.data.version))})))}))}}return t.\u0275fac=function(e){return new(e||t)(h["\u0275\u0275directiveInject"](i.g),h["\u0275\u0275directiveInject"](g.a),h["\u0275\u0275directiveInject"](f.a),h["\u0275\u0275directiveInject"](v.b),h["\u0275\u0275directiveInject"](b.a),h["\u0275\u0275directiveInject"](x.a),h["\u0275\u0275directiveInject"](y.a),h["\u0275\u0275directiveInject"](C.a),h["\u0275\u0275directiveInject"](_.a),h["\u0275\u0275directiveInject"](S.a))},t.\u0275cmp=h["\u0275\u0275defineComponent"]({type:t,selectors:[["app-ts-approvals-landing-page"]],features:[h["\u0275\u0275ProvidersFeature"]([{provide:m.c,useClass:d.c,deps:[m.f,d.a]},{provide:m.e,useValue:ae}])],decls:9,vars:8,consts:[[1,"row","pt-2"],[1,"col-6"],["name","fontStyle","aria-label","Font Style",3,"value","change"],["group","matButtonToggleGroup"],["value","ts",2,"padding","2px 10px !important",3,"ngClass"],["value","wfh","style","padding: 2px 30px !important",3,"ngClass",4,"ngIf"],["class","container-fluid approvers-styles pl-0 pr-0",4,"ngIf"],["class","approvers-styles",4,"ngIf"],["value","wfh",2,"padding","2px 30px !important",3,"ngClass"],[1,"container-fluid","approvers-styles","pl-0","pr-0"],[1,"row"],[1,"col-1","d-flex","pt-3","pl-4","pr-0"],[1,"mediumSubtleText","pl-3"],[1,"heading","pl-2"],["class","col-3",4,"ngIf"],["class","col-3 pt-1",4,"ngIf"],[1,"d-flex","col","col-5","search-bar"],["appearance","outline",1,"ml-auto"],["matPrefix",""],[2,"font-size","21px !important","color","#66615b !important"],["id","searchId","matInput","","type","search","name","search","placeholder","Search Associates",3,"keyup"],["matSuffix",""],["mat-button","","matSuffix","","mat-icon-button","","aria-label","Clear","style","height: 30px; width: 30px; line-height: 1",3,"click",4,"ngIf"],[1,"col-3","d-flex","pt-2"],["mat-icon-button","","matTooltip","View By",2,"margin-left","9px",3,"ngClass"],[1,"iconButton",3,"matMenuTriggerFor"],["visibility","matMenu"],["mat-menu-item","",1,"drop-btn",3,"click"],["mat-icon-button","",1,"view-button-inactive",2,"margin-left","9px",3,"matTooltip","disabled","click"],["class","iconButton",4,"ngIf"],["class","spinner-align","diameter","18",4,"ngIf"],["mat-icon-button","","style","margin-left: 9px",3,"matTooltip","ngClass",4,"ngIf"],["style","margin-left: 9px",4,"ngIf"],["mat-icon-button","",2,"margin-left","9px",3,"matTooltip","ngClass","click"],[1,"iconButton"],["mat-icon-button","","matTooltip","Back to Default View","class","view-button-inactive","style","margin-left: 9px",4,"ngIf"],["mat-icon-button","","class","view-button-inactive","style","margin-left: 9px","matTooltip","New Releases",3,"click",4,"ngIf"],[4,"ngIf"],[1,"col-3"],[1,"col-3","pt-1"],[1,"row","pl-0","pr-0"],[1,"col-1","p-0"],[1,"col-1","pl-0","pr-0","mr-2"],["mat-icon-button","","matTooltip","Previous month"],[1,"iconButton",3,"click"],[1,"col-2","pr-0","pt-2"],["for","monthYearDp",1,"title"],[1,"col-6","pr-0","pl-0","pt-2"],[2,"width","100% !important"],["id","monthYearDpInput","matInput","","readonly","",1,"ib13",3,"matDatepicker","formControl"],["matSuffix","","matTooltip","Select from Calendar",3,"for"],["disabled","false","startView","year","panelClass","example-month-picker",3,"yearSelected","monthSelected"],["monthYearDp",""],[1,"col-1","pl-0","ml-2"],["mat-icon-button","","matTooltip","Next month"],["mat-button","","matSuffix","","mat-icon-button","","aria-label","Clear",2,"height","30px","width","30px","line-height","1",3,"click"],["matTooltip","Clear search",2,"font-size","18px !important","color","#66615b !important"],["diameter","18",1,"spinner-align"],["mat-icon-button","",2,"margin-left","9px",3,"matTooltip","ngClass"],[1,"iconButton",3,"dblclick","click"],[2,"margin-left","9px"],["content-type","template",3,"id","tooltip"],["approverTooltip",""],[1,"row","tooltip-text"],["mat-icon-button","","matTooltip","Back to Default View",1,"view-button-inactive",2,"margin-left","9px"],["mat-icon-button","","matTooltip","New Releases",1,"view-button-inactive",2,"margin-left","9px",3,"click"],[3,"ngClass"],[1,"row","pl-4","pt-2","pb-1"],[1,"col-1","ml-1","pl-3","smallSubtleText"],[3,"ngModel","ngModelChange","change",4,"ngIf"],[1,"col-2","smallSubtleText"],[1,"col-1","smallSubtleText","pl-0"],["class","col-2 smallSubtleText pl-0",4,"ngIf"],[1,"col-2","smallSubtleText","pl-0"],["class","col-1 smallSubtleText pl-0",4,"ngIf"],["class","row","style","padding-bottom: 1px",4,"ngFor","ngForOf"],[3,"ngModel","ngModelChange","change"],[1,"row",2,"padding-bottom","1px"],[1,"col-12","pl-4","pr-4"],[1,"card","listcard",2,"border-left","3px solid #FF7200 !important","padding","1px"],[1,"card-body",2,"padding","2px !important"],[1,"row","card-details","p-0"],[1,"col-1","pt-1","mt-1"],[1,"col-2","pl-0","pt-1","name",3,"matTooltip","click"],["imgWidth","28px","imgHeight","28px",3,"id"],[1,"name","ml-3"],[3,"matTooltip",4,"ngIf"],["class","col-1 details pt-1 pl-0","content-type","template",3,"tooltip","click",4,"ngIf"],["class","col-1 details pt-1 pl-0",3,"matTooltip","click",4,"ngIf"],["otHoursTooltip",""],[1,"col-2","costcenter","pt-1","pl-0","pr-0",3,"matTooltip","click"],[1,"col-2","location","pt-1",3,"matTooltip","click"],["class","date pl-0 pl-4 pr-4 pt-1 d-flex","style","text-align: center; flex: 0 0 11.333%; max-width: 12%;",4,"ngIf"],["class","approversDrag",3,"mousedown","mouseup","mouseleave","mousemove",4,"ngIf"],["class","date","style","width: 100px;",4,"ngIf"],["class","col-2 date pl-0 pl-4 pr-4 pt-1 d-flex",4,"ngIf"],[3,"matTooltip"],["content-type","template",1,"col-1","details","pt-1","pl-0",3,"tooltip","click"],[1,"col-1","details","pt-1","pl-0",3,"matTooltip","click"],[1,"date","pl-0","pl-4","pr-4","pt-1","d-flex",2,"text-align","center","flex","0 0 11.333%","max-width","12%"],[3,"click"],[1,"approversDrag",3,"mousedown","mouseup","mouseleave","mousemove"],["elemt",""],[4,"ngFor","ngForOf"],["content-type","template","placement","top","imgHeight","30px","imgWidth","30px","borderStyle","solid","borderWidth","2px",2,"margin","2px",3,"tooltip","id","borderColor"],["placement","top"],[1,"d-flex","flex-row","mx-auto"],[1,"pb-1","row","tooltip-text"],[1,"tooltip-status-indicator","p-0","mt-0","mb-0","ml-2",3,"ngStyle"],[1,"date",2,"width","100px"],["mat-icon-button","","matTooltip","Approve","class","icon-tray-button ml-auto mr-2",3,"click",4,"ngIf"],["mat-icon-button","","matTooltip","Reject","class","icon-tray-button mr-2",3,"click",4,"ngIf"],["mat-icon-button","","matTooltip","Approve",1,"icon-tray-button","ml-auto","mr-2",3,"click"],[1,"smallCardIcon"],["mat-icon-button","","matTooltip","Reject",1,"icon-tray-button","mr-2",3,"click"],[1,"col-2","date","pl-0","pl-4","pr-4","pt-1","d-flex"],[1,"status-circular",3,"ngClass"],[1,"location","ml-2",3,"matTooltip"],[1,"smallSubtleText","pl-1","pr-1",3,"matTooltip"],[1,"col-1","ml-1","smallSubtleText"],[1,"col-1","smallSubtleText","pl-0","ml-5"],[1,"row","pt-2","pb-2",2,"margin-left","31px !important"],[1,"col-12"],[1,"ml-2","heading",2,"vertical-align","text-bottom"],["class"," heading","style","vertical-align: text-bottom",4,"ngIf"],[1,"heading",2,"vertical-align","text-bottom"],[1,"col-2","details","pt-1","pl-0",3,"click"],["ccOtHoursTooltip",""],["class","col-1 date pl-0 pt-1","style","text-align: center",3,"click",4,"ngIf"],["class","d-flex","style","width: 100px;",4,"ngIf"],[1,"col-1","date","pl-0","pt-1",2,"text-align","center",3,"click"],[1,"d-flex",2,"width","100px"],["mat-icon-button","","matTooltip","Approve","class","icon-tray-button ml-auto",3,"click",4,"ngIf"],["mat-icon-button","","matTooltip","Reject","class","icon-tray-button ml-auto",3,"click",4,"ngIf"],["mat-icon-button","","matTooltip","Show Summary",1,"icon-tray-button","ml-auto",3,"click"],["mat-icon-button","","matTooltip","Approve",1,"icon-tray-button","ml-auto",3,"click"],["mat-icon-button","","matTooltip","Reject",1,"icon-tray-button","ml-auto",3,"click"],[1,"d-flex","justify-content-center","align-items-center","mt-4","slide-in-top"],[1,"d-flex","justify-content-center","align-items-center","slide-from-down"],["src","https://assets.kebs.app/images/emty%20approval.png","height","260","width","310",1,"mt-4"],[1,"row","pl-4","pt-2","pb-2"],[1,"row","list-card-styles"],[1,"card","listcard",2,"border-left","3px solid #FF7200 !important","padding","5px"],[1,"col-1","pt-1","accountId"],[1,"col-2","pl-3","name"],[3,"speed","width","height","primaryColor","secondaryColor"],["ngx-rect","","width","1000","height","70","y","50","x","0","rx","5","ry","5"],[1,"col-2","saleUnit"],[1,"col-2","pl-2","industry"],[1,"col-2","accountOwner"],[1,"col-2","parent",2,"padding-left","37px"],[1,"col-1","d-flex"],["mat-icon-button","","matTooltip","Edit accounts",1,"icon-tray-button","ml-auto"],["mat-icon-button","","matTooltip","View more",1,"icon-tray-button","ml-auto"],[1,"approvers-styles"],[1,"col-2","d-flex","pt-3","pl-4"],[1,"col-2"],["appearance","outline",1,"ml-0"],["id","searchId","matInput","","type","search","name","search","placeholder","Search Associates"],["mat-icon-button","",1,"view-button-inactive",2,"margin-left","9px",3,"click"],[3,"change"],[1,"col-3","smallSubtleText","pl-0"],[1,"col-2","smallSubtleText","pl-0",2,"padding-left","2rem !important"],[1,"col-2","smallSubtleText","pl-0",2,"padding-left","2.4rem !important"],[3,"checked","change"],[1,"col-2","pl-0","pt-1","name"],[1,"name","ml-3",3,"click"],["content-type","template","max-width","300",1,"col-3","details","pt-2","pl-0","text",3,"tooltip","click"],["wfhTooltip",""],[1,"col-2","ml-5","costcenter","pt-2","pl-0","pr-0","text",2,"font-weight","500 !important",3,"click"],[1,"col-2","location","pt-2","text",3,"click"],[1,"col-1","date","pl-0","pl-4","pr-4","pt-1","d-flex",2,"text-align","center"],["class","smallCardIcon",4,"ngIf"],[1,"row",2,"font-size","12px"],[2,"padding-left","1.5rem !important"],[1,"ml-2"],[2,"padding-left","1.1rem !important"],[2,"padding-left","0.5rem !important"]],template:function(t,e){1&t&&(h["\u0275\u0275elementStart"](0,"div",0),h["\u0275\u0275elementStart"](1,"div",1),h["\u0275\u0275elementStart"](2,"mat-button-toggle-group",2,3),h["\u0275\u0275listener"]("change",(function(t){return e.selectToggle(t)})),h["\u0275\u0275elementStart"](4,"mat-button-toggle",4),h["\u0275\u0275text"](5),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](6,L,2,4,"mat-button-toggle",5),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](7,Nt,42,24,"div",6),h["\u0275\u0275template"](8,oe,24,7,"div",7)),2&t&&(h["\u0275\u0275advance"](2),h["\u0275\u0275property"]("value",e.selectedToggle),h["\u0275\u0275advance"](2),h["\u0275\u0275property"]("ngClass",h["\u0275\u0275pureFunction1"](6,V,"ts"==e.selectedToggle)),h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate1"]("Time Sheet (",e.associateData?e.associateData.length:0,")"),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",e.needToShowRemoteWorkx),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf","ts"==e.selectedToggle),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf","wfh"==e.selectedToggle))},directives:[w.b,w.a,s.NgClass,s.NgIf,k.c,k.h,A.a,I.b,k.i,O.a,M.a,D.f,D.g,D.d,T.g,a.e,a.v,a.k,T.i,T.f,E.c,P.a,H.a,s.NgForOf,j.a,a.y,s.NgStyle,R.b,R.c],styles:[".approvers-styles[_ngcontent-%COMP%]   .multiline-tooltip[_ngcontent-%COMP%]{white-space:pre-line}.approvers-styles[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]{color:#1a1a1a;font-size:14px;font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.approvers-styles[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]{margin-top:5px!important;line-height:8px;width:26px;height:26px;margin-right:5px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.approvers-styles[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.approvers-styles[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]   .iconButtonWarn[_ngcontent-%COMP%]{color:#cf0001;font-size:18px}.approvers-styles[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]   .iconButtonWarnAnim[_ngcontent-%COMP%]{color:#cf0001;font-size:18px;animation:blink 1.1s cubic-bezier(.5,0,1,1) infinite alternate}.approvers-styles[_ngcontent-%COMP%]   .view-button-active[_ngcontent-%COMP%]{background-color:#cf0001!important;margin-top:5px!important;line-height:8px;width:26px;height:26px;margin-right:5px!important;box-shadow:0 3px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.approvers-styles[_ngcontent-%COMP%]   .view-button-active[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#fff;font-size:18px}.approvers-styles[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.approvers-styles[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field{width:35vw;padding-top:5px}.approvers-styles[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-infix{font-size:14px!important;padding:.5em 0!important;border-top:.54375em solid transparent!important}.approvers-styles[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#3c3c3c;font-weight:400;font-size:18px}.approvers-styles[_ngcontent-%COMP%]   .mediumSubtleText[_ngcontent-%COMP%]{font-size:14px;color:#66615b}.approvers-styles[_ngcontent-%COMP%]   .smallSubtleText[_ngcontent-%COMP%]{color:#66615b;font-size:11px}.approvers-styles[_ngcontent-%COMP%]   .drop-btn[_ngcontent-%COMP%]{font-size:13px!important;height:38px!important;line-height:2!important;padding:0 13px}.approvers-styles[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}.approvers-styles[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}.approvers-styles[_ngcontent-%COMP%]   .listcard[_ngcontent-%COMP%]{transition:all .25s linear;height:39px;max-height:39px!important}.approvers-styles[_ngcontent-%COMP%]   .listcard[_ngcontent-%COMP%]:hover{box-shadow:0 4px 8px 0 rgba(0,0,0,.2),0 6px 20px 0 rgba(0,0,0,.19);cursor:pointer}.approvers-styles[_ngcontent-%COMP%]   .listcard[_ngcontent-%COMP%]:hover   .icon-tray-button[_ngcontent-%COMP%]{visibility:visible!important;animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}.approvers-styles[_ngcontent-%COMP%]   .icon-tray-button[_ngcontent-%COMP%]{width:27px!important;height:27px!important;line-height:25px!important;visibility:hidden}.approvers-styles[_ngcontent-%COMP%]   .icon-tray-button-visible[_ngcontent-%COMP%]{width:27px!important;height:27px!important;line-height:25px!important;visibility:visible!important}.approvers-styles[_ngcontent-%COMP%]   .smallCardIcon[_ngcontent-%COMP%]{font-size:18px!important;color:#868683!important}.approvers-styles[_ngcontent-%COMP%]   .name[_ngcontent-%COMP%]{color:#cf0001;font-family:Roboto;font-weight:500}.approvers-styles[_ngcontent-%COMP%]   .costcenter[_ngcontent-%COMP%], .approvers-styles[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%], .approvers-styles[_ngcontent-%COMP%]   .details[_ngcontent-%COMP%], .approvers-styles[_ngcontent-%COMP%]   .location[_ngcontent-%COMP%], .approvers-styles[_ngcontent-%COMP%]   .name[_ngcontent-%COMP%]{font-size:14px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}@keyframes slide-top{0%{transform:translateY(10px)}to{transform:translateY(0)}}.approvers-styles[_ngcontent-%COMP%]   .spinner-align[_ngcontent-%COMP%]{margin:auto;display:inline-block}.approvers-styles[_ngcontent-%COMP%]     .mat-icon-button[disabled][disabled]{background:#ddd!important}.approvers-styles[_ngcontent-%COMP%]   .status-circular[_ngcontent-%COMP%]{height:13px;width:13px;margin-top:4px;border-radius:50%;box-shadow:0 2px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.approvers-styles[_ngcontent-%COMP%]   .is-draft[_ngcontent-%COMP%]{background:grey;color:#fff!important}.approvers-styles[_ngcontent-%COMP%]   .is-submitted[_ngcontent-%COMP%]{background:#ff7200;color:#fff!important}.approvers-styles[_ngcontent-%COMP%]   .is-approved[_ngcontent-%COMP%]{background:#009432;color:#fff!important}.approvers-styles[_ngcontent-%COMP%]   .is-rejected[_ngcontent-%COMP%]{background:#cf0001;color:#fff!important}.approvers-styles[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]{font-size:14px!important}.approvers-styles[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-form-field-infix{padding:0!important;border-top:0!important}.approvers-styles[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding-bottom:0!important}.approvers-styles[_ngcontent-%COMP%]     .mat-form-field-underline{display:none}.approvers-styles[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:14px;color:#66615b;font-weight:400}.approvers-styles[_ngcontent-%COMP%]   .ib13[_ngcontent-%COMP%]{font-size:14px;font-weight:400;text-align:center;color:#1a1a1a;display:inline}.approvers-styles[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{font-size:13.5px}.approvers-styles[_ngcontent-%COMP%]   .status-text-Approved[_ngcontent-%COMP%]{color:#009432}.approvers-styles[_ngcontent-%COMP%]   .status-text-Rejected[_ngcontent-%COMP%]{color:#cf0001}.approvers-styles[_ngcontent-%COMP%]   .approversDrag[_ngcontent-%COMP%]{overflow-x:hidden;display:flex;cursor:grab;width:100px}.btn-toggle[_ngcontent-%COMP%], .btn-toggle-selected[_ngcontent-%COMP%]{font-size:12px!important}.btn-toggle-selected[_ngcontent-%COMP%]{background-color:#c92020!important;color:#fff}.multiline-tooltip[_ngcontent-%COMP%]{white-space:pre-line}@keyframes blink{0%{opacity:1}to{opacity:0}}"]}),t})();var le=n("TC2u");function ce(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"span",24),h["\u0275\u0275text"](1),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"]();h["\u0275\u0275property"]("matTooltip",t.submissionDetails[0]?t.submissionDetails[0].group_description:"-"),h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate1"]("(",t.submissionDetails[0]?t.submissionDetails[0].group_code:"-",")")}}function pe(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",25),h["\u0275\u0275elementStart"](1,"button",26),h["\u0275\u0275elementStart"](2,"span",27),h["\u0275\u0275text"](3),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](4,"button",28),h["\u0275\u0275elementStart"](5,"span",18),h["\u0275\u0275text"](6),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](7,"button",29),h["\u0275\u0275elementStart"](8,"span",18),h["\u0275\u0275text"](9),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"]();h["\u0275\u0275advance"](3),h["\u0275\u0275textInterpolate"](t.machineHours),h["\u0275\u0275advance"](3),h["\u0275\u0275textInterpolate1"]("",t.wfhHours," "),h["\u0275\u0275advance"](3),h["\u0275\u0275textInterpolate1"]("",t.overallLeaveBalance," ")}}function de(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",30),h["\u0275\u0275elementStart"](1,"div",31),h["\u0275\u0275text"](2,"Leave Balance :"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](3,"button",29),h["\u0275\u0275elementStart"](4,"span",18),h["\u0275\u0275text"](5),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"]();h["\u0275\u0275advance"](5),h["\u0275\u0275textInterpolate1"]("",t.overallLeaveBalance," ")}}function me(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"span",35),h["\u0275\u0275text"](1),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"](2);h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate1"](" (OT: ",t.otHours,") ")}}function ue(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"span"),h["\u0275\u0275elementStart"](1,"span",36),h["\u0275\u0275text"](2),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit;h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngClass","L"==t.dayType?"is-leave-day":"FH"==t.dayType?"is-fh-day":"CH"==t.dayType?"is-ch-day":"CO"==t.dayType?"is-co-day":"T"==t.dayType?"is-t-day":"CL"==t.dayType?"is-cl-day":"CVL"==t.dayType?"is-cvl-day":"PL"==t.dayType?"is-pl-day":"ML"==t.dayType?"is-ml-day":"J"==t.dayType?"is-j-day":"LW"==t.dayType?"is-lw-day":"OT"==t.dayType?"is-ot-day":"LOP"==t.dayType?"is-lop-day":"CEL"==t.dayType?"is-cel-day":"BL"==t.dayType?"is-bl-day":"SL"==t.dayType?"is-sl-day":"LOH"==t.dayType?"is-lop-day":"OH"==t.dayType||"H-O"==t.dayType?"is-oh-day":"HF"==t.dayType?"is-fh-day":"H-C"==t.dayType?"is-cl-day":"H-P"==t.dayType?"is-pl-day":"is-empty-day"),h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate2"](" ",t.count," ",t.dayType," ")}}function he(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",32),h["\u0275\u0275elementStart"](1,"div",31),h["\u0275\u0275text"](2,"Details : "),h["\u0275\u0275elementStart"](3,"span",33),h["\u0275\u0275text"](4),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](5,me,2,1,"span",34),h["\u0275\u0275template"](6,ue,3,3,"span",23),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"](),e=h["\u0275\u0275reference"](16);h["\u0275\u0275property"]("tooltip",e),h["\u0275\u0275advance"](4),h["\u0275\u0275textInterpolate1"](" ",t.overallHours>0?t.overallHours:"-"," hours "),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",t.otHours>0),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngForOf",t.nonRegularDaysData)}}function ge(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"span",35),h["\u0275\u0275text"](1),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"](2);h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate1"](" (OT: ",t.otHours,") ")}}function fe(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"span"),h["\u0275\u0275elementStart"](1,"span",36),h["\u0275\u0275text"](2),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit;h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngClass","L"==t.dayType?"is-leave-day":"FH"==t.dayType?"is-fh-day":"CH"==t.dayType?"is-ch-day":"CO"==t.dayType?"is-co-day":"T"==t.dayType?"is-t-day":"CL"==t.dayType?"is-cl-day":"CVL"==t.dayType?"is-cvl-day":"PL"==t.dayType?"is-pl-day":"ML"==t.dayType?"is-ml-day":"J"==t.dayType?"is-j-day":"LW"==t.dayType?"is-lw-day":"OT"==t.dayType?"is-ot-day":"LOP"==t.dayType?"is-lop-day":"CEL"==t.dayType?"is-cel-day":"BL"==t.dayType?"is-bl-day":"SL"==t.dayType?"is-sl-day":"OH"==t.dayType?"is-oh-day":"H"==t.dayType?"is-fh-day":"WL"==t.dayType?"is-wl-day":"LOH"==t.dayType?"is-lop-day":"HF"==t.dayType?"is-fh-day":"H-P"==t.dayType?"is-pl-day":"H-O"==t.dayType?"is-oh-day":"H-C"==t.dayType?"is-cl-day":"is-empty-day"),h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate2"](" ",t.count," ",t.dayType," ")}}function ve(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",37),h["\u0275\u0275elementStart"](1,"div",31),h["\u0275\u0275text"](2,"Details : "),h["\u0275\u0275elementStart"](3,"span",33),h["\u0275\u0275text"](4),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](5,ge,2,1,"span",34),h["\u0275\u0275template"](6,fe,3,3,"span",23),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"]();h["\u0275\u0275advance"](4),h["\u0275\u0275textInterpolate1"](" ",t.overallHours>0?t.overallHours:"-"," hours "),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",t.otHours>0),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngForOf",t.nonRegularDaysData)}}function be(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",38),h["\u0275\u0275text"](1),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](2,"div",38),h["\u0275\u0275text"](3),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"]();h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate1"](" Regular Hours : ",t.overallHours-t.otHours," hours "),h["\u0275\u0275advance"](2),h["\u0275\u0275textInterpolate1"](" Over Time : ",t.otHours," hours ")}}function xe(t,e){1&t&&(h["\u0275\u0275elementStart"](0,"mat-icon",18),h["\u0275\u0275text"](1,"done"),h["\u0275\u0275elementEnd"]())}function ye(t,e){1&t&&h["\u0275\u0275element"](0,"mat-spinner",42)}function Ce(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"button",39),h["\u0275\u0275listener"]("click",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"]().approveAll()})),h["\u0275\u0275template"](1,xe,2,0,"mat-icon",40),h["\u0275\u0275template"](2,ye,1,0,"mat-spinner",41),h["\u0275\u0275elementEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"]();h["\u0275\u0275property"]("matTooltip",t.approveActivated?"Approving":"Approve")("disabled",t.isCurrentDateGreaterThanCutOff&&"false"==t.isCurrentUserAdmin||0==t.selectAllVisible),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",0==t.approveActivated),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",t.approveActivated)}}function _e(t,e){1&t&&(h["\u0275\u0275elementStart"](0,"mat-icon",18),h["\u0275\u0275text"](1,"close"),h["\u0275\u0275elementEnd"]())}function Se(t,e){1&t&&h["\u0275\u0275element"](0,"mat-spinner",42)}function we(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"button",43),h["\u0275\u0275listener"]("click",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"]().rejectAll()})),h["\u0275\u0275template"](1,_e,2,0,"mat-icon",40),h["\u0275\u0275template"](2,Se,1,0,"mat-spinner",41),h["\u0275\u0275elementEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"]();h["\u0275\u0275property"]("matTooltip",t.rejectActivated?"Rejecting":"Reject")("disabled",t.isCurrentDateGreaterThanCutOff&&"false"==t.isCurrentUserAdmin||0==t.selectAllVisible),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",0==t.rejectActivated),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",t.rejectActivated)}}function ke(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"mat-checkbox",50),h["\u0275\u0275listener"]("ngModelChange",(function(e){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](2).selectAllActivated=e}))("change",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](2).selectAllWeeks()})),h["\u0275\u0275elementEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"](2);h["\u0275\u0275property"]("ngModel",t.selectAllActivated)}}function Ae(t,e){1&t&&(h["\u0275\u0275elementStart"](0,"span",58),h["\u0275\u0275text"](1,"Cost Centre : "),h["\u0275\u0275elementEnd"]())}function Ie(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"span",58),h["\u0275\u0275text"](1),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"](4);h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate1"]("",t.tsProperties.ts_ui_text," : ")}}function Oe(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",65),h["\u0275\u0275elementStart"](1,"span",58),h["\u0275\u0275text"](2,"Region : "),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](3,"span",55),h["\u0275\u0275text"](4),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"](4);h["\u0275\u0275advance"](3),h["\u0275\u0275property"]("matTooltip",t.region),h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate1"](" ",t.region," ")}}function Me(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"span",35),h["\u0275\u0275text"](1),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"](3).$implicit;h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate1"]("(OT:",t.otHours,")")}}function De(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",66),h["\u0275\u0275elementStart"](1,"span",58),h["\u0275\u0275text"](2,"Total Hours:"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](3,"span",33),h["\u0275\u0275text"](4),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](5,Me,2,1,"span",34),h["\u0275\u0275elementEnd"]()),2&t){h["\u0275\u0275nextContext"]();const t=h["\u0275\u0275reference"](16),e=h["\u0275\u0275nextContext"]().$implicit;h["\u0275\u0275property"]("tooltip",t),h["\u0275\u0275advance"](4),h["\u0275\u0275textInterpolate1"]("",e.totalHours," hours "),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",e.otHours>0)}}function Te(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"span",35),h["\u0275\u0275text"](1),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"](3).$implicit;h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate1"]("(OT:",t.otHours,")")}}function Ee(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",67),h["\u0275\u0275elementStart"](1,"span",68),h["\u0275\u0275text"](2,"Total Hours:"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](3,"span",33),h["\u0275\u0275text"](4),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](5,Te,2,1,"span",34),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"](2).$implicit;h["\u0275\u0275advance"](4),h["\u0275\u0275textInterpolate1"]("",t.totalHours," hours "),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",t.otHours>0)}}function Pe(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",38),h["\u0275\u0275text"](1),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](2,"div",38),h["\u0275\u0275text"](3),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"](2).$implicit;h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate1"](" Regular Hours : ",t.totalHours-t.otHours," hours "),h["\u0275\u0275advance"](2),h["\u0275\u0275textInterpolate1"](" Over Time : ",t.otHours," hours ")}}const He=function(t){return{color:t}};function je(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div"),h["\u0275\u0275elementStart"](1,"div",38),h["\u0275\u0275text"](2),h["\u0275\u0275elementStart"](3,"mat-icon",74),h["\u0275\u0275text"](4,"fiber_manual_record "),h["\u0275\u0275elementEnd"](),h["\u0275\u0275text"](5),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit,n=h["\u0275\u0275nextContext"](6);h["\u0275\u0275advance"](2),h["\u0275\u0275textInterpolate1"]("",t.weekLabel," : "),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngStyle",h["\u0275\u0275pureFunction1"](3,He,n.borderStatusColor(t.weekStatus))),h["\u0275\u0275advance"](2),h["\u0275\u0275textInterpolate1"](" ",t.weekStatus," ")}}function Re(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",73),h["\u0275\u0275text"](1),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](2,"div",38),h["\u0275\u0275text"](3),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](4,"div",38),h["\u0275\u0275text"](5),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](6,je,6,5,"div",23)),2&t){const t=h["\u0275\u0275nextContext"]().$implicit;h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate1"]("",t.name," "),h["\u0275\u0275advance"](2),h["\u0275\u0275textInterpolate1"](" ",t.role,""),h["\u0275\u0275advance"](2),h["\u0275\u0275textInterpolate1"](" Level : ",t.level,""),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngForOf",t.approverStatus)}}function Ve(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"span",69),h["\u0275\u0275element"](1,"app-user-image",70),h["\u0275\u0275template"](2,Re,7,4,"ng-template",71,72,h["\u0275\u0275templateRefExtractor"]),h["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit,n=h["\u0275\u0275reference"](3),s=h["\u0275\u0275nextContext"](4);h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("tooltip",n)("id",t.oid)("borderColor",s.borderStatusColorForSubmissionPage(t.approverStatus))}}function Le(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"button",75),h["\u0275\u0275listener"]("click",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](4).editApprovers()})),h["\u0275\u0275elementStart"](1,"mat-icon",18),h["\u0275\u0275text"](2,"edit"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}}function $e(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div"),h["\u0275\u0275elementStart"](1,"div",52),h["\u0275\u0275elementStart"](2,"div",53),h["\u0275\u0275template"](3,Ae,2,0,"span",54),h["\u0275\u0275template"](4,Ie,2,1,"span",54),h["\u0275\u0275elementStart"](5,"span",55),h["\u0275\u0275text"](6),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](7,Oe,5,2,"div",56),h["\u0275\u0275elementStart"](8,"div",57),h["\u0275\u0275elementStart"](9,"span",58),h["\u0275\u0275text"](10,"Location : "),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](11,"span",55),h["\u0275\u0275text"](12),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](13,De,6,3,"div",59),h["\u0275\u0275template"](14,Ee,6,2,"div",60),h["\u0275\u0275template"](15,Pe,4,2,"ng-template",null,61,h["\u0275\u0275templateRefExtractor"]),h["\u0275\u0275elementStart"](17,"div",62),h["\u0275\u0275elementStart"](18,"span",58),h["\u0275\u0275text"](19,"Approvers : "),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](20,Ve,4,3,"span",63),h["\u0275\u0275template"](21,Le,3,0,"button",64),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"]().$implicit,e=h["\u0275\u0275nextContext"](2);h["\u0275\u0275advance"](3),h["\u0275\u0275property"]("ngIf",0==e.tsProperties.ts_ui_change_and_download_icon),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",1==e.tsProperties.ts_ui_change_and_download_icon),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("matTooltip",e.getLength(t.costCentreName?t.costCentreName:"a")>10?t.costCentreName:""),h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate2"](" #",t.costCentre," - ",t.costCentreName,""),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",1==e.tsProperties.display_region_in_approval),h["\u0275\u0275advance"](4),h["\u0275\u0275property"]("matTooltip",e.getLength(t.location?t.location:"a")>10?t.location:""),h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate1"](" ",t.location,""),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",t.otHours>0),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!(t.otHours>0)),h["\u0275\u0275advance"](6),h["\u0275\u0275property"]("ngForOf",t.approvers),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!!e.wfProperties&&1==e.wfProperties.can_appr_edit_appr&&0==e.isAdmin)}}function Ye(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div"),h["\u0275\u0275template"](1,$e,22,12,"div",51),h["\u0275\u0275elementEnd"]()),2&t){const t=e.index;h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",0==t)}}function Fe(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",44),h["\u0275\u0275elementStart"](1,"div",22),h["\u0275\u0275elementStart"](2,"div",21),h["\u0275\u0275elementStart"](3,"div",45),h["\u0275\u0275template"](4,ke,1,1,"mat-checkbox",46),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](5,"div",47),h["\u0275\u0275elementStart"](6,"div",48),h["\u0275\u0275elementStart"](7,"div",49),h["\u0275\u0275template"](8,Ye,2,1,"div",23),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"]();h["\u0275\u0275advance"](4),h["\u0275\u0275property"]("ngIf",t.selectAllVisible&&!t.historyDisplay),h["\u0275\u0275advance"](4),h["\u0275\u0275property"]("ngForOf",t.costCentreItems)}}function Be(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"mat-checkbox",50),h["\u0275\u0275listener"]("ngModelChange",(function(e){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](2).$implicit.checkBoxActivated=e}))("change",(function(){h["\u0275\u0275restoreView"](t);const e=h["\u0275\u0275nextContext"](2).$implicit;return h["\u0275\u0275nextContext"]().selectIndividualWeek(e)})),h["\u0275\u0275elementEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"](2).$implicit;h["\u0275\u0275property"]("ngModel",t.checkBoxActivated)}}function qe(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",45),h["\u0275\u0275template"](1,Be,1,1,"mat-checkbox",46),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"]().$implicit;h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf","Submitted"==t.status)}}function Ue(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",84),h["\u0275\u0275text"](1),h["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit;h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate1"](" ","*"==t.date?"":t.date,"")}}function We(t,e){1&t&&(h["\u0275\u0275elementStart"](0,"div",96),h["\u0275\u0275text"](1,"Cost Centre"),h["\u0275\u0275elementEnd"]())}function Ne(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",96),h["\u0275\u0275text"](1),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"](4);h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate"](t.tsProperties.ts_ui_text)}}const ze=function(t){return{width:t}};function Xe(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"input",101),h["\u0275\u0275listener"]("ngModelChange",(function(e){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"]().$implicit.submission=e}))("ngModelOptions",(function(){return{debounce:500}}))("ngModelChange",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](5).calculateHours()})),h["\u0275\u0275elementEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"]().$implicit,e=h["\u0275\u0275nextContext"](4);h["\u0275\u0275property"]("disabled",!0)("ngStyle",h["\u0275\u0275pureFunction1"](4,ze,e.tsProperties.is_decimal?"55px":"40px"))("ngClass",e.rfidDisplay&&t.submission>0?"is-lop-changed":"FH"==t.submission?"is-fh-day":"WO"==t.submission?"is-wo-day":8==t.submission?"is-regular-day":t.submission<"8"&&""!=t.submission?"is-less-regular-day":"L"==t.submission?"is-leave-day":"CH"==t.submission?"is-ch-day":"CO"==t.submission?"is-co-day":"T"==t.submission?"is-t-day":"CL"==t.submission?"is-cl-day":"CVL"==t.submission?"is-cvl-day":"PL"==t.submission?"is-pl-day":"ML"==t.submission?"is-ml-day":"J"==t.submission?"is-j-day":"LW"==t.submission?"is-lw-day":"BL"==t.submission?"is-bl-day":"CEL"==t.submission?"is-cel-day":"SL"==t.submission?"is-sl-day":"OH"==t.submission?"is-oh-day":"H"==t.submission?"is-h-day":"WL"==t.submission?"is-wl-day":"OD"==t.submission?"is-od-day":"WH"==t.submission?"is-wh-day":"LOP"==t.submission||"LOH"==t.submission?"is-lop-day":"HF"==t.submission?"is-fh-day":"H-O"==t.submission?"is-oh-day":"H-P"==t.submission?"is-pl-day":"H-C"==t.submission?"is-cl-day":"is-empty-day")("ngModel",t.submission)}}function Ge(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"input",102,103),h["\u0275\u0275listener"]("ngModelChange",(function(e){h["\u0275\u0275restoreView"](t);const n=h["\u0275\u0275nextContext"]().$implicit;return n.isShWo&&0==n.submission?"WO":n.isLOP&&n.submission>=0?"LOP":n.submission=e}))("ngModelOptions",(function(){return{debounce:500}}))("ngModelChange",(function(){return h["\u0275\u0275restoreView"](t),h["\u0275\u0275nextContext"](5).calculateHours()}))("click",(function(){h["\u0275\u0275restoreView"](t);const e=h["\u0275\u0275reference"](1),n=h["\u0275\u0275nextContext"]().$implicit,s=h["\u0275\u0275nextContext"]().$implicit;return h["\u0275\u0275nextContext"](3).activateInlineEdit(e,s,n)})),h["\u0275\u0275elementEnd"]()}if(2&t){const t=h["\u0275\u0275nextContext"]().$implicit,e=h["\u0275\u0275nextContext"](4);h["\u0275\u0275property"]("ngStyle",h["\u0275\u0275pureFunction1"](2,ze,e.tsProperties.is_decimal?"55px":"40px"))("ngModel",t.isShWo&&0==t.submission?"WO":t.isLOP&&t.submission>=0?"LOP":t.submission)}}function Je(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",108),h["\u0275\u0275text"](1," Data From: "),h["\u0275\u0275elementStart"](2,"span",106),h["\u0275\u0275text"](3),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"]().$implicit;h["\u0275\u0275advance"](3),h["\u0275\u0275textInterpolate"](t.from)}}function Qe(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div"),h["\u0275\u0275template"](1,Je,4,1,"div",107),h["\u0275\u0275elementStart"](2,"div",108),h["\u0275\u0275text"](3," Session: "),h["\u0275\u0275elementStart"](4,"span",106),h["\u0275\u0275text"](5),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](6,"div",108),h["\u0275\u0275text"](7," InTime : "),h["\u0275\u0275elementStart"](8,"span",106),h["\u0275\u0275text"](9),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](10,"div",108),h["\u0275\u0275text"](11," OutTime : "),h["\u0275\u0275elementStart"](12,"span",106),h["\u0275\u0275text"](13),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](14,"div",108),h["\u0275\u0275text"](15," Total Hours : "),h["\u0275\u0275elementStart"](16,"span",106),h["\u0275\u0275text"](17),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275element"](18,"div",109),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit;h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf","No Data"!=t.from),h["\u0275\u0275advance"](4),h["\u0275\u0275textInterpolate"](t.sessionId?t.sessionId:0),h["\u0275\u0275advance"](4),h["\u0275\u0275textInterpolate"](t.inTime?t.inTime:0),h["\u0275\u0275advance"](4),h["\u0275\u0275textInterpolate"](t.outTime?t.outTime:0),h["\u0275\u0275advance"](4),h["\u0275\u0275textInterpolate"](t.totalHour?t.totalHour:0)}}function Ke(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",104),h["\u0275\u0275template"](1,Qe,19,5,"div",23),h["\u0275\u0275elementStart"](2,"div",105),h["\u0275\u0275text"](3," Day Total Hours : "),h["\u0275\u0275elementStart"](4,"span",106),h["\u0275\u0275text"](5),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"]().$implicit;h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngForOf",t.rfidData),h["\u0275\u0275advance"](4),h["\u0275\u0275textInterpolate"](t.totalHoursOfTheDay?t.totalHoursOfTheDay:0)}}function Ze(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",97),h["\u0275\u0275template"](1,Xe,1,6,"input",98),h["\u0275\u0275template"](2,Ge,2,4,"input",99),h["\u0275\u0275template"](3,Ke,6,2,"div",100),h["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit,n=h["\u0275\u0275nextContext"](4);h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf","*"!=t.date&&!t.hasInlineData),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf","*"!=t.date&&t.hasInlineData),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",n.rfidDisplay)}}function tn(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",110),h["\u0275\u0275elementStart"](1,"div",96),h["\u0275\u0275text"](2,"Submitted On"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](3,"div",111),h["\u0275\u0275text"](4),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"]().$implicit,e=h["\u0275\u0275nextContext"](2).index,n=h["\u0275\u0275nextContext"]();h["\u0275\u0275advance"](3),h["\u0275\u0275property"]("matTooltip",n.getLocalDate(t.submissions[e].submittedOn)),h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate1"](" ",n.getLocalDate(t.submissions[e].submittedOn),"")}}function en(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div"),h["\u0275\u0275elementStart"](1,"button",112),h["\u0275\u0275listener"]("click",(function(){h["\u0275\u0275restoreView"](t);const e=h["\u0275\u0275nextContext"]().$implicit,n=h["\u0275\u0275nextContext"](2).index;return h["\u0275\u0275nextContext"]().openHistory(e.submissions[n].weekHistory)})),h["\u0275\u0275elementStart"](2,"mat-icon",18),h["\u0275\u0275text"](3,"history"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}}function nn(t,e){if(1&t){const t=h["\u0275\u0275getCurrentView"]();h["\u0275\u0275elementStart"](0,"div",85),h["\u0275\u0275elementStart"](1,"div",21),h["\u0275\u0275elementStart"](2,"div",86),h["\u0275\u0275template"](3,We,2,0,"div",87),h["\u0275\u0275template"](4,Ne,2,1,"div",87),h["\u0275\u0275elementStart"](5,"div",88),h["\u0275\u0275text"](6),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](7,Ze,4,3,"div",89),h["\u0275\u0275elementStart"](8,"div",90),h["\u0275\u0275elementStart"](9,"div",91),h["\u0275\u0275template"](10,tn,5,2,"div",92),h["\u0275\u0275elementStart"](11,"div",93),h["\u0275\u0275elementStart"](12,"div"),h["\u0275\u0275elementStart"](13,"button",94),h["\u0275\u0275listener"]("click",(function(){h["\u0275\u0275restoreView"](t);const n=e.$implicit,s=h["\u0275\u0275nextContext"](2).index;return h["\u0275\u0275nextContext"]().openComments(n,s)})),h["\u0275\u0275elementStart"](14,"mat-icon",95),h["\u0275\u0275text"](15," chat"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](16,en,4,0,"div",51),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()}if(2&t){const t=e.$implicit,n=h["\u0275\u0275nextContext"](2).index,s=h["\u0275\u0275nextContext"]();h["\u0275\u0275advance"](3),h["\u0275\u0275property"]("ngIf",0==s.tsProperties.ts_ui_change_and_download_icon),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",1==s.tsProperties.ts_ui_change_and_download_icon),h["\u0275\u0275advance"](2),h["\u0275\u0275textInterpolate1"]("#",t.costCentre,""),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngForOf",t.submissions[n].weekSubmissions),h["\u0275\u0275advance"](3),h["\u0275\u0275property"]("ngIf",t.submissions[n].submittedOn),h["\u0275\u0275advance"](3),h["\u0275\u0275property"]("matTooltip",t.submissions[n].weekComments.length>0?"See Comments":"Add Comments"),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngClass",t.submissions[n].weekComments.length>0?"has-comments":""),h["\u0275\u0275advance"](2),h["\u0275\u0275property"]("ngIf",null!=t.submissions[n].weekHistory)}}function sn(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div",48),h["\u0275\u0275elementStart"](1,"div",78),h["\u0275\u0275elementStart"](2,"div",21),h["\u0275\u0275elementStart"](3,"div",79),h["\u0275\u0275text"](4),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](5,Ue,2,1,"div",80),h["\u0275\u0275elementStart"](6,"div",81),h["\u0275\u0275element"](7,"span",82),h["\u0275\u0275text"](8),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](9,nn,17,8,"div",83),h["\u0275\u0275elementEnd"]()),2&t){const t=h["\u0275\u0275nextContext"]().$implicit,e=h["\u0275\u0275nextContext"]();h["\u0275\u0275advance"](4),h["\u0275\u0275textInterpolate"](t.week),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngForOf",t.dates),h["\u0275\u0275advance"](2),h["\u0275\u0275property"]("ngClass","Draft"==t.status?"is-draft":"Submitted"==t.status?"is-submitted":"Approved"==t.status?"is-approved":"Rejected"==t.status?"is-rejected":""),h["\u0275\u0275advance"](1),h["\u0275\u0275textInterpolate"](t.status),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngForOf",e.costCentreItems)}}function on(t,e){if(1&t&&(h["\u0275\u0275elementStart"](0,"div"),h["\u0275\u0275elementStart"](1,"div",21),h["\u0275\u0275template"](2,qe,2,1,"div",76),h["\u0275\u0275elementStart"](3,"div",47),h["\u0275\u0275template"](4,sn,10,5,"div",77),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit;h["\u0275\u0275advance"](2),h["\u0275\u0275property"]("ngIf",t.isActive),h["\u0275\u0275advance"](2),h["\u0275\u0275property"]("ngIf",t.isActive)}}const an=[{path:"",children:[{path:"",component:re},{path:":associateOId/:label/:costCentre/:month/:year/:location/:isAdmin/:isCurrentUserAdmin/:approverOId/timesheetDetails",component:(()=>{class t{constructor(t,e,n,s,i,o,r,c,d,m,u){this.viewContainerRef=t,this.route=e,this.dialog=n,this.wfPrimaryService=s,this.tsApprovalsService=i,this.router=o,this.tsPrimaryService=r,this.utilityService=c,this.loginService=d,this.errorService=m,this.tsInlineEntryPopupService=u,this.monthEndDateVal=31,this.monthEndDate=l().date(this.monthEndDateVal).hour(15).minute(0).second(0).millisecond(0),this.formattedMonthYearDate="",this.formattedMonthYearDateEnd="",this._onDestroy=new p.b,this.globalCC={},this.currentUser={},this.globalLocation={},this.globalMonth=Number,this.globalYear=Number,this.globalName={},this.isAdmin=0,this.overallHours=0,this.otHours=0,this.overallLeaveBalance=0,this.associateData=[],this.costCentreItems=[],this.costCentreList=[],this.locationList=[],this.weekItems=[],this.userWeekOffDays=[0,6],this.submissionDetails=[],this.nonRegularDays=[],this.nonRegularDaysData=[],this.monthYearDate=new a.j(l()),this.totalHours="-",this.submType="W",this.areCostCentreItemsExpanded=!1,this.selectAllActivated=!1,this.previousPageDisabled=!1,this.nextPageDisabled=!1,this.approveActivated=!1,this.rejectActivated=!1,this.selectAllVisible=!0,this.myTeamViewActivated=!1,this.isCurrentDateGreaterThanCutOff=!1,this.historyDisplay=!1,this.machineHours=0,this.wfhHours=0,this.rfidDisplay=!1}ngOnInit(){this.currentUser=this.loginService.getProfile().profile,this.route.params.subscribe(t=>Object(o.c)(this,void 0,void 0,(function*(){this.globalOid=t.associateOId,this.globalCC=decodeURIComponent(t.costCentre),this.globalMonth=t.month,this.globalYear=t.year,this.globalLocation=decodeURIComponent(t.location),this.globalName=t.label,this.isAdmin=t.isAdmin,this.approverOId=t.approverOId,this.isCurrentUserAdmin=t.isCurrentUserAdmin,this.selectAllActivated=!1,this.selectAllVisible=!0,this.nonRegularDays=[],this.nonRegularDaysData=[],this.tsApprovalsService.getAssociates.subscribe(t=>{this.associateData=t,this.globalIndex=this.associateData.findIndex(t=>{if(t.oid==this.globalOid&&t.costcenter==this.globalCC&&t.month==this.globalMonth&&t.year==this.globalYear&&t.location==this.globalLocation)return t}),this.previousPageDisabled=0==this.globalIndex,this.nextPageDisabled=this.globalIndex==t.length-1}),this.getTimesheetProperties()})))}setAssociateData(){return Object(o.c)(this,void 0,void 0,(function*(){this.monthYearDate.setValue(l().month(this.globalMonth-1).year(this.globalYear)),this.monthYearDate.value.date(1),this.monthEndDateVal="END"==this.tsProperties.month_end_date?"END":parseInt(this.tsProperties.month_end_date),this.calculateFormattedDates(this.monthYearDate.value),this.generateMonthCalendar(this.monthYearDate.value),r.each(this.weekItems,t=>{t.isActive=!1});let t={costCentre:"",costCentreName:"",location:"",submissions:this.generateCostCentreSubmissions(),approvers:"",balance:"",totalHours:""};this.costCentreItems.pop(),this.costCentreItems.push(t);let e="Approval"==this.tsApprovalsService.viewType?"Approval":"";this.historyDisplay="Approval"!=this.tsApprovalsService.viewType,this.tsApprovalsService.getDetailsOfPendingApproval(this.globalOid,this.approverOId,this.globalCC,this.formattedMonthYearDate,this.formattedMonthYearDateEnd,this.globalLocation,e).pipe(Object(c.a)(this._onDestroy)).subscribe(t=>Object(o.c)(this,void 0,void 0,(function*(){if("S"==t.messType&&t.data.length>0){this.submissionDetails=t.data,this.overallLeaveBalance=this.submissionDetails[0].LB?this.submissionDetails[0].LB:0,this.rfidDisplay=1==t.isRfidAllowed,this.machineHours=t.tsMachineHours>0?t.tsMachineHours:0,this.wfhHours=t.rfIdMachineHours>0?t.rfIdMachineHours:0;for(let t of this.submissionDetails)t.date=l(t.date,"YYYY-MM-DD").utcOffset(0,!0).format("YYYY-M-D"),this.region=t.Region,t.formatDate=this.calculateFormatDate(l(t.date).day(),l(t.date).date(),l(t.date));for(let t=0;t<this.weekItems.length;t++)for(let e=0;e<this.submissionDetails.length;e++)for(let n=0;n<this.weekItems[t].dates.length;n++)this.weekItems[t].dates[n].date==this.submissionDetails[e].formatDate&&(this.weekItems[t].status=this.submissionDetails[e].status_name,this.weekItems[t].isActive=!0);this.resolveSubmissionData()}else this.utilityService.showMessage(t.messText,"Dismiss",3e3)})),t=>{this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Error in getting pending approvals submission",t&&t.params?t.params:t&&t.error?t.error.params:{})})}))}getLength(t){return t.length}activateInlineEdit(t,e,n){let s=[];for(let l of n.submissions){let t=JSON.parse(l.inline_application_details);if(t.tsTaskId){let e=r.where(s,{tsTaskId:t.tsTaskId});0==e.length?s.push({tsTaskId:t.tsTaskId,inlineApplicationDetails:t,submissions:[l]}):e[0].submissions.push(l)}}let i=[];for(let r of s){let t=[],e=0;for(let n of r.submissions)t.push({submissionId:n.submission_id,date:n.date,hours:n.hours,location:n.location?n.location:"",dayType:n.day_type,billingType:n.billing_type}),e+=n.hours;i.push({inlineApplicationDetails:r.inlineApplicationDetails,totalHours:e,entries:t})}let o=this.getMonthBasedOnDate(n.date),a=o.month(),c=o.year();this.tsInlineEntryPopupService.setInlineEditActiveDataSubject({inlineEditTs:t,viewContainerRef:this.viewContainerRef,costCentreItem:{costCentre:e.costCentre,costCentreDescription:e.costCentreName,costCentreType:e.costCentreType,location:e.location,approvers:e.approvers},currentUserOid:this.globalOid,wfProperties:this.wfProperties,activeWfProperties:this.wfProperties,week:n.week.replace(/^\D+/g,""),inlineApplicationDetails:i[0].inlineApplicationDetails,hasMultipleTasks:i.length>1,currentTaskItemIndex:0,taskItems:i,totalHours:i[0].totalHours,entries:i[0].entries,objectDetailsVisible:!0,isFromTs:!0,isSubmissionDisabled:!1,canEnterTs:!1,canOpenPopup:!0,canDateBeChanged:!1,formattedMonthYearDate:l().month(a).year(c).date(n.date).format("YYYY-MM-DD"),defaultFormattedMonthYearDate:l().month(a).year(c).date(n.date).format("YYYY-MM-DD"),monthYearDate:l().month(a).year(c).date(n.date),defaultMonthYearDate:l().month(a).year(c).date(n.date),defaultDate:l().month(a).year(c).date(n.date).format("DD-MMM-YY"),defaultBackupDate:l().month(a).year(c).date(n.date).format("DD-MMM-YY"),defaultHours:8,defaultBackupHours:8,defaultDayType:"R",defaultBackupDayType:"R",defaultBillingType:"billable",defaultBackupBillingType:"billable"})}resolveSubmissionData(){this.nonRegularDays=[];for(let t=0;t<this.submissionDetails.length;t++)for(let e=0;e<this.costCentreItems.length;e++){this.costCentreItems[e].costCentre=this.submissionDetails[t].cost_centre,this.costCentreItems[e].costCentreName=this.submissionDetails[t].cc_description,this.costCentreItems[e].location=this.submissionDetails[t].location,this.costCentreItems[e].approvers=JSON.parse(this.submissionDetails[t].approvers);for(let n=0;n<this.costCentreItems[e].submissions.length;n++){for(let s=0;s<this.costCentreItems[e].submissions[n].weekSubmissions.length;s++)this.costCentreItems[e].submissions[n].weekSubmissions[s].date==l(this.submissionDetails[t].date,"YYYY-MM-DD").utcOffset(0,!0).date()&&(this.costCentreItems[e].submissions[n].submittedOn=this.submissionDetails[t].submitted_date,this.costCentreItems[e].submissions[n].weekComments="string"==typeof this.submissionDetails[t].comments?JSON.parse(this.submissionDetails[t].comments):this.submissionDetails[t].comments,this.costCentreItems[e].submissions[n].weekHistory="string"==typeof this.submissionDetails[t].history?JSON.parse(this.submissionDetails[t].history):this.submissionDetails[t].history,this.submissionDetails[t].day_type=this.submissionDetails[t].day_type.toUpperCase(),this.submissionDetails[t].billing_type&&"NA"!=this.submissionDetails[t].billing_type?(this.costCentreItems[e].submissions[n].weekSubmissions[s].hasInlineData=!0,"wo"==this.submissionDetails[t].day_type.toLowerCase()&&(this.costCentreItems[e].submissions[n].weekSubmissions[s].isShWo=!0),"lop"==this.submissionDetails[t].day_type.toLowerCase()&&(this.costCentreItems[e].submissions[n].weekSubmissions[s].isLOP=!0),""==this.costCentreItems[e].submissions[n].weekSubmissions[s].submission?this.costCentreItems[e].submissions[n].weekSubmissions[s].submission=this.submissionDetails[t].hours:this.costCentreItems[e].submissions[n].weekSubmissions[s].submission+=this.submissionDetails[t].hours,this.costCentreItems[e].submissions[n].weekSubmissions[s].submissions.push(this.submissionDetails[t]),this.costCentreItems[e].submissions[n].weekSubmissions[s].sessionDetails=this.submissionDetails[t].rfidData,this.costCentreItems[e].submissions[n].weekSubmissions[s].totalHours=this.submissionDetails[t].totalHoursOfTheDay):(this.costCentreItems[e].submissions[n].weekSubmissions[s].submission="R"==this.submissionDetails[t].day_type?0==this.submissionDetails[t].hours?"":this.submissionDetails[t].hours:this.submissionDetails[t].day_type,"R"!=this.submissionDetails[t].day_type&&"WO"!=this.submissionDetails[t].day_type&&this.nonRegularDays.push(this.submissionDetails[t].day_type),this.costCentreItems[e].submissions[n].weekSubmissions[s].sessionDetails=this.submissionDetails[t].rfidData,this.costCentreItems[e].submissions[n].weekSubmissions[s].totalHours=this.submissionDetails[t].totalHoursOfTheDay));console.log(this.costCentreItems[e].submissions[n])}}this.calculateApproversStatus(),this.calculateHours(),this.calculateNonRegularDays()}calculateApproversStatus(){for(let t=0;t<this.costCentreItems.length;t++)for(let e=0;e<this.costCentreItems[t].approvers.length;e++){this.costCentreItems[t].approvers[e].approverStatus=[];for(let n=0;n<this.costCentreItems[t].submissions.length;n++)if(this.costCentreItems[t].submissions[n].weekHistory&&this.costCentreItems[t].submissions[n].weekHistory.length>0)if(this.costCentreItems[t].submissions[n].weekHistory=this.costCentreItems[t].submissions[n].weekHistory.sort((t,e)=>t.date>e.date?-1:1),"Auto Approved"==this.costCentreItems[t].submissions[n].weekHistory[0].name)this.costCentreItems[t].approvers[e].approverStatus.push({weekLabel:this.costCentreItems[t].submissions[n].week,weekStatus:"Auto Approved"});else{let s=!1;for(let i=0;i<this.costCentreItems[t].submissions[n].weekHistory.length;i++)this.costCentreItems[t].approvers[e].oid!=this.costCentreItems[t].submissions[n].weekHistory[i].oid||s||(s=!0,this.costCentreItems[t].approvers[e].approverStatus.push({weekLabel:this.costCentreItems[t].submissions[n].week,weekStatus:this.costCentreItems[t].submissions[n].weekHistory[i].status}));if(!s){let s=r.where(this.costCentreItems[t].submissions[n].weekHistory,{original_appr_oid:this.costCentreItems[t].approvers[e].oid,is_admin_approved:1});s.length>0&&this.costCentreItems[t].approvers[e].approverStatus.push({weekLabel:this.costCentreItems[t].submissions[n].week,weekStatus:"Admin "+s[0].status})}}}}borderStatusColor(t){return"Draft"==t||"Recalled"==t?"#808080":"Submitted"==t||"Awaiting Approval"==t?"#FF7200":"Approved"==t||"Admin Approved"==t||"Auto Approved"==t||"Escalated"==t?"#009432":"Rejected"==t||"Admin Rejected"==t||"Cancelled"==t?"#cf0001":"#808080"}getLocalDate(t){return"-"==t?t:l(t,"DD - MMM - YYYY HH : mm : ss").utc(t).local().format("DD - MMM - YYYY HH : mm : ss")}backToApproversHome(){this.router.navigateByUrl("/main/timesheet/approvals")}generateMonthCalendar(t){this.weekItems=[],this.calculateFormattedDates(t);let e="END"==this.monthEndDateVal?t.day():l(t).subtract(1,"months").date(this.monthEndDateVal+1).day(),n="END"==this.monthEndDateVal?t.date():this.monthEndDateVal+1,s="END"!=this.monthEndDateVal,i="END"==this.monthEndDateVal?t.daysInMonth():l(t).subtract(1,"months").daysInMonth();for(let o=1;o<=this.calculateWeekCountOfMonth(t);o++){let a={week:"Week "+o,dates:[],status:"Draft",checkBoxActivated:!1,isActive:!1};for(let t=0;t<e;t++)a.dates.push({date:"*",isWeekOff:!1});let r=0;for(r=e;r<7&&n<=i;r++){let e;e=s?l(t).subtract(1,"months"):t,a.dates.push(this.calculateDate(r,n,e)),s&&"END"!=this.monthEndDateVal&&n==i?(s=!1,n=1,i=this.monthEndDateVal):n++}for(let t=r;t<7;t++)a.dates.push({date:"*",isWeekOff:!1});e=0,this.weekItems.push(a)}}calculateFormattedDates(t){"END"!=this.monthEndDateVal?(this.monthEndDate=l(t).date(this.monthEndDateVal).hour(15).minute(0).second(0).millisecond(0),this.formattedMonthYearDate=l(t).subtract(1,"months").date(this.monthEndDateVal+1).hour(15).minute(0).second(0).millisecond(0).format("YYYY-MM-DD"),this.formattedMonthYearDateEnd=l(t).date(this.monthEndDateVal).hour(15).minute(0).second(0).millisecond(0).format("YYYY-MM-DD")):(this.monthEndDate=l(t).date(l().daysInMonth()).hour(15).minute(0).second(0).millisecond(0),this.formattedMonthYearDate=l(t).hour(15).minute(0).second(0).millisecond(0).format("YYYY-MM-DD"),this.formattedMonthYearDateEnd=l(t).date(l().daysInMonth()).hour(15).minute(0).second(0).millisecond(0).format("YYYY-MM-DD"))}calculateWeekCountOfMonth(t){let e="END"==this.monthEndDateVal?t.daysInMonth():l(this.monthEndDate).diff(l(t).subtract(1,"months").date(this.monthEndDateVal+1),"days")+2,n="END"==this.monthEndDateVal?t.day():l(t).subtract(1,"months").date(this.monthEndDateVal+1).day(),s=Math.ceil(e/7);return(e>=31&&n>4||30==e&&n>5||28==e&&n>0)&&s++,s}getMonthBasedOnDate(t){let e,n="END"!=this.monthEndDateVal;return n&&parseInt(t)<=this.monthEndDateVal&&(n=!1),e=n?l(this.monthYearDate.value).subtract(1,"months"):this.monthYearDate.value,e}calculateDate(t,e,n){let s=r.contains(this.userWeekOffDays,t);return{date:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"][t]+", "+e+" "+n.format("MMM"),isWeekOff:s}}calculateFormatDate(t,e,n){return["Sun","Mon","Tue","Wed","Thu","Fri","Sat"][t]+", "+e+" "+n.format("MMM")}generateCostCentreSubmissions(){let t=[];for(let e of this.weekItems){let n="",s=[],i=0;for(let t of e.dates)n=e.week,s.push({date:"*"==t.date?t.date:t.date.match(/\d/g).join(""),submission:"",week:e.week,hasInlineData:!1,isShWo:!1,isLOP:!1,submissions:[]});t.push({week:n,weekSubmissions:s,weekHours:i})}return t}openComments(t,e){return Object(o.c)(this,void 0,void 0,(function*(){let s={currentCostCentreWeekComments:t.submissions[e].weekComments,currentUser:this.currentUser,currentStatus:"Awaiting Approval",viewMode:"Submitted"!=this.weekItems[e].status};const{TsCommentsModalComponent:i}=yield n.e(767).then(n.bind(null,"rtmT"));this.dialog.open(i,{width:"65%",height:"auto",autoFocus:!1,maxWidth:"90vw",data:{modalParams:s}}).afterClosed().subscribe(n=>{"Submit"==n.event&&(t.submissions[e].weekComments=n.data.comments)})}))}openHistory(t){return Object(o.c)(this,void 0,void 0,(function*(){let e={currentCostCentreWeekHistory:t=r.sortBy(t,(function(t){return[t.date,t.level].join("_")}))};const{TsHistoryModalComponent:s}=yield n.e(769).then(n.bind(null,"bqX/"));this.dialog.open(s,{width:"65%",height:"auto",autoFocus:!1,maxWidth:"90vw",data:{historyModalParams:e}})}))}selectAllWeeks(){r.each(this.weekItems,this.selectAllActivated?t=>{t.checkBoxActivated=!("Submitted"!=t.status||!t.isActive)}:t=>{t.isActive&&(t.checkBoxActivated=!1)})}selectIndividualWeek(t){let e=[];r.each(this.weekItems,t=>{t.isActive&&e.push(t.checkBoxActivated)}),this.selectAllActivated=!r.contains(e,!1)}goBack(){this.router.navigateByUrl("/main/timesheet/approvals/"+this.encodeURIComponent(this.associateData[this.globalIndex-1].oid)+"/"+this.encodeURIComponent(this.associateData[this.globalIndex-1].name)+"/"+this.encodeURIComponent(this.associateData[this.globalIndex-1].costcenter)+"/"+this.associateData[this.globalIndex-1].month+"/"+this.associateData[this.globalIndex-1].year+"/"+this.encodeURIComponent(this.associateData[this.globalIndex-1].location)+"/"+this.isAdmin+"/"+this.isCurrentUserAdmin+"/"+this.encodeURIComponent(this.approverOId)+"/timesheetDetails")}goNext(){this.router.navigateByUrl("/main/timesheet/approvals/"+this.encodeURIComponent(this.associateData[this.globalIndex+1].oid)+"/"+this.encodeURIComponent(this.associateData[this.globalIndex+1].name)+"/"+this.encodeURIComponent(this.associateData[this.globalIndex+1].costcenter)+"/"+this.associateData[this.globalIndex+1].month+"/"+this.associateData[this.globalIndex+1].year+"/"+this.encodeURIComponent(this.associateData[this.globalIndex+1].location)+"/"+this.isAdmin+"/"+this.isCurrentUserAdmin+"/"+this.encodeURIComponent(this.approverOId)+"/timesheetDetails")}encodeURIComponent(t){return encodeURIComponent(t).replace(/[!'()*]/g,(function(t){return"%"+t.charCodeAt(0).toString(16)}))}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}calculateHours(){this.overallHours=0,this.otHours=0;for(let t=0;t<this.costCentreItems.length;t++){this.costCentreItems[t].totalHours=0,this.overallHours=0,this.otHours=0;for(let e=0;e<this.weekItems.length;e++)if(this.weekItems[e].isActive){this.costCentreItems[t].submissions[e].weekHours=0;for(let n=0;n<this.costCentreItems[t].submissions[e].weekSubmissions.length;n++)if("*"!=this.costCentreItems[t].submissions[e].weekSubmissions[n].date){let s=""==this.costCentreItems[t].submissions[e].weekSubmissions[n].submission||isNaN(this.costCentreItems[t].submissions[e].weekSubmissions[n].submission)?0:parseFloat(this.costCentreItems[t].submissions[e].weekSubmissions[n].submission);if(s=Math.round(100*s)/100,this.costCentreItems[t].submissions[e].weekHours+=s,this.costCentreItems[t].submissions[e].weekHours=Math.round(100*this.costCentreItems[t].submissions[e].weekHours)/100,this.costCentreItems[t].totalHours+=s,this.costCentreItems[t].totalHours=Math.round(100*this.costCentreItems[t].totalHours)/100,this.overallHours+=s,this.overallHours=Math.round(100*this.overallHours)/100,this.costCentreItems[t].submissions[e].weekSubmissions[n].submissions.length>0){let i=0,o=this.costCentreItems[t].submissions[e].weekSubmissions[n].submissions;for(let t of o)"OT"==t.day_type&&(i+=t.ot_hours?t.ot_hours:0,s=Math.round(100*i)/100);this.costCentreItems[t].otHours=i,this.costCentreItems[t].otHours=Math.round(100*this.costCentreItems[t].otHours)/100,this.otHours=i,this.otHours=Math.round(100*this.otHours)/100}}}}}rejectAll(){return Object(o.c)(this,void 0,void 0,(function*(){let t=r.where(this.weekItems,{checkBoxActivated:!0});if(0==t.length)this.utilityService.showMessage("Please select weeks you want to reject","Dismiss",3e3);else{let e=[];for(let n=0;n<t.length;n++){let s=[];r.each(t[n].dates,t=>{"*"!=t.date&&s.push(t.date)});let i=[];for(let t=0;t<s.length;t++)for(let e=0;e<this.submissionDetails.length;e++)s[t]==this.submissionDetails[e].formatDate&&i.push(this.submissionDetails[e].workflowHeaderId);e.push(r.uniq(i))}let s=[].concat.apply([],e);s=r.uniq(s),r.each(t,t=>{t.isForWeek=!0,t.isForMonth=!1,t.displayMonth=l().month(this.globalMonth-1).format("MMMM"),t.name=this.submissionDetails[0].name,t.costcenter=this.globalCC});const{TsApprovalsRejectModalComponent:i}=yield n.e(194).then(n.bind(null,"FoXP"));this.dialog.open(i,{width:"80%",height:"auto",maxHeight:"100vh",disableClose:!0,data:{associateData:t}}).afterClosed().subscribe(t=>{if("Submit"==t.event){this.rejectActivated=!0;let e=t.data.reason,n=[],i=[],a=[];for(let t=0;t<this.weekItems.length;t++)this.weekItems[t].checkBoxActivated&&a.push(t);for(let t=0;t<a.length;t++){for(let e=0;e<this.costCentreItems[0].submissions[a[t]].weekComments.length;e++)this.costCentreItems[0].submissions[a[t]].weekComments[e].oid==this.currentUser.oid&&"Awaiting Approval"==this.costCentreItems[0].submissions[a[t]].weekComments[e].status&&(this.costCentreItems[0].submissions[a[t]].weekComments[e].status="Rejected",i.push([this.costCentreItems[0].submissions[a[t]].weekComments[e]]));!i[t]&&i.push([])}for(let t=0;t<e.length;t++)n.push({oid:this.currentUser.oid,name:this.currentUser.name,status:"Rejected",text:e[t],date:l().utc().format("DD - MMM - YYYY HH : mm : ss")}),i[t]?i[t].push(n[t]):i.push([n[t]]);this.tsApprovalsService.updateTsForWeek(this.approverOId,s,"R",i,this.isAdmin,this.currentUser.oid).pipe(Object(c.a)(this._onDestroy)).subscribe(t=>Object(o.c)(this,void 0,void 0,(function*(){if("S"==t.messType){let t=[];for(let e=0;e<n.length;e++){this.costCentreItems[0].submissions[a[e]].weekComments.push(n[e]);for(let t=0;t<this.costCentreItems[0].submissions[a[e]].weekHistory.length;t++)this.costCentreItems[0].submissions[a[e]].weekHistory[t].oid==this.currentUser.oid&&"Awaiting Approval"==this.costCentreItems[0].submissions[a[e]].weekHistory[t].status&&(this.costCentreItems[0].submissions[a[e]].weekHistory[t].status="Rejected",this.costCentreItems[0].submissions[a[e]].weekHistory[t].date=l().utc().format("DD - MMM - YYYY HH : mm : ss"))}r.each(this.weekItems,e=>{e.checkBoxActivated&&(e.status="Rejected"),e.isActive&&t.push(e.status),e.checkBoxActivated=!1}),this.selectAllVisible=r.contains(t,"Submitted"),!r.contains(t,"Submitted")&&(this.wfProperties.can_appr_edit_appr=0),this.selectAllActivated=!1,this.rejectActivated=!1,this.utilityService.showMessage("Rejected Successfully","Dismiss",3e3)}else this.utilityService.showMessage(t.messText,"Dismiss",3e3)})),t=>{this.rejectActivated=!1,this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Error while getting timesheet weekwise data",t&&t.params?t.params:t&&t.error?t.error.params:{})})}})}}))}calculateNonRegularDays(){let t={};this.nonRegularDays.forEach((function(e){t[e]=(t[e]||0)+1}));for(let e in t)this.nonRegularDaysData.push({dayType:e,count:t[e]})}approveAll(){let t=r.where(this.weekItems,{checkBoxActivated:!0});if(0==t.length)this.utilityService.showMessage("Please select weeks you want to approve","Dismiss",3e3);else{this.approveActivated=!0;let e=[],n=[];for(let t=0;t<this.weekItems.length;t++)this.weekItems[t].checkBoxActivated&&e.push(t);for(let t=0;t<e.length;t++){for(let s=0;s<this.costCentreItems[0].submissions[e[t]].weekComments.length;s++)this.costCentreItems[0].submissions[e[t]].weekComments[s].oid==this.currentUser.oid&&"Awaiting Approval"==this.costCentreItems[0].submissions[e[t]].weekComments[s].status&&(this.costCentreItems[0].submissions[e[t]].weekComments[s].status="Approved",n.push([this.costCentreItems[0].submissions[e[t]].weekComments[s]]));!n[t]&&n.push([])}let s=[];r.each(t,t=>{r.each(t.dates,t=>{"*"!=t.date&&s.push(t.date)})});let i=[];for(let t=0;t<s.length;t++)for(let e=0;e<this.submissionDetails.length;e++)s[t]==this.submissionDetails[e].formatDate&&i.push(this.submissionDetails[e].workflowHeaderId);i=r.uniq(i),this.tsApprovalsService.updateTsForWeek(this.approverOId,i,"A",n,this.isAdmin,this.currentUser.oid).pipe(Object(c.a)(this._onDestroy)).subscribe(t=>Object(o.c)(this,void 0,void 0,(function*(){if("S"==t.messType){let t=[];for(let n=0;n<e.length;n++)for(let t=0;t<this.costCentreItems[0].submissions[e[n]].weekHistory.length;t++)this.costCentreItems[0].submissions[e[n]].weekHistory[t].oid==this.currentUser.oid&&"Awaiting Approval"==this.costCentreItems[0].submissions[e[n]].weekHistory[t].status&&(this.costCentreItems[0].submissions[e[n]].weekHistory[t].status="Approved",this.costCentreItems[0].submissions[e[n]].weekHistory[t].date=l().utc().format("DD - MMM - YYYY HH : mm : ss"));r.each(this.weekItems,e=>{e.checkBoxActivated&&(e.status="Approved"),e.isActive&&t.push(e.status),e.checkBoxActivated=!1}),this.selectAllVisible=r.contains(t,"Submitted"),!r.contains(t,"Submitted")&&(this.wfProperties.can_appr_edit_appr=0),this.approveActivated=!1,this.utilityService.showMessage("Approved Successfully","Dismiss",3e3)}else this.utilityService.showMessage(t.messText,"Dismiss",3e3)})),t=>{this.approveActivated=!1,this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Error while getting timesheet weekwise data",t&&t.params?t.params:t&&t.error?t.error.params:{})})}}calculateOverallSubmitStatus(){for(let t=0;t<this.weekItems.length;t++){let e=this.costCentreItems.length>0?this.costCentreItems[0].submissions[t].status:"";for(let n=0;n<this.costCentreItems.length&&""!=e;n++)this.costCentreItems[n].submissions[t].status!=e&&(e="");this.weekItems[t].status=e}}getTimesheetWorkflowProperties(){return Object(o.c)(this,void 0,void 0,(function*(){this.wfPrimaryService.getWorkflowProperties({applicationId:37}).pipe(Object(c.a)(this._onDestroy)).subscribe(t=>Object(o.c)(this,void 0,void 0,(function*(){"S"==t.messType&&t.data.length>0?(this.wfProperties=r.where(t.data,{sub_application_id:"R"})[0],yield this.setAssociateData()):this.utilityService.showMessage(t.messText,"Dismiss",3e3)})),t=>{this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Error while retrieving Workflow Properties",t&&t.params?t.params:t&&t.error?t.error.params:{})})}))}getTimesheetProperties(){return Object(o.c)(this,void 0,void 0,(function*(){this.tsPrimaryService.getTimesheetProperties(this.currentUser.aid).pipe(Object(c.a)(this._onDestroy)).subscribe(t=>Object(o.c)(this,void 0,void 0,(function*(){"S"==t.messType&&t.data.length>0?(this.tsProperties=t.data[0],this.isCurrentDateGreaterThanCutOff=l().date()==this.tsProperties.monthly_appr_cut_off_date?l().hour()>this.tsProperties.monthly_appr_cut_off_hour||l().hour()==this.tsProperties.monthly_appr_cut_off_hour&&l().minute()>this.tsProperties.monthly_appr_cut_off_minute:l().date()>this.tsProperties.monthly_appr_cut_off_date&&!this.tsProperties.is_payroll_frozen,this.getTimesheetWorkflowProperties()):this.utilityService.showMessage(t.messText,"Dismiss",3e3)})),t=>{this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Error while retrieving the Timesheet Properties",t&&t.params?t.params:t&&t.error?t.error.params:{})})}))}editApprovers(){return Object(o.c)(this,void 0,void 0,(function*(){let t={sidenavName:"Approvers-Modal",currentCostCentreItem:this.costCentreItems[0],noOfMandLevels:this.wfProperties.no_of_mand_lvls,canAddApprovers:this.wfProperties.can_add_appr,canEditApprovers:this.wfProperties.can_edit_auto_appr,canDeleteApprovers:this.wfProperties.can_del_appr,viewMode:!1,currentView:"Approvals"};const{TsApproversModalComponent:e}=yield n.e(766).then(n.bind(null,"Oc0b"));this.dialog.open(e,{height:"100%",width:"95%",position:{right:"0px"},data:{modalParams:t}}).afterClosed().subscribe(t=>{if("Submit"==t.event){this.costCentreItems[0]=t.data.currentCostCentreItem;let e=[];r.each(this.weekItems,t=>{r.each(t.dates,n=>{"Submitted"==t.status&&e.push(n.date)})});let n=[];for(let t=0;t<e.length;t++)for(let s=0;s<this.submissionDetails.length;s++)e[t]==this.submissionDetails[s].formatDate&&n.push(this.submissionDetails[s].workflowHeaderId);n=r.uniq(n);let s={workflowHeaderId:n,approvers:this.costCentreItems[0].approvers,currentLevel:r.where(this.costCentreItems[0].approvers,{oid:this.currentUser.oid})[0].level};this.tsApprovalsService.alterApprovers(s).pipe(Object(c.a)(this._onDestroy)).subscribe(t=>Object(o.c)(this,void 0,void 0,(function*(){"S"==t.messType?t.data&&this.utilityService.showMessage(t.messText,"Dismiss",3e3):this.utilityService.showMessage(t.messText,"Dismiss",3e3)})),t=>{this.errorService.userErrorAlert(t&&t.code?t.code:t&&t.error?t.error.code:"NIL","Error while altering the approvers",t&&t.params?t.params:t&&t.error?t.error.params:{})})}})}))}showErrorMessage(t){this.utilityService.showErrorMessage(t,"KEBS or HR")}borderStatusColorForSubmissionPage(t){return r.find(t,{weekStatus:"Rejected"})||r.find(t,{weekStatus:"Admin Rejected"})||r.find(t,{weekStatus:"Cancelled"})?"#cf0001":r.find(t,{weekStatus:"Draft"})||r.find(t,{weekStatus:"Recalled"})?"#808080":r.find(t,{weekStatus:"Submitted"})||r.find(t,{weekStatus:"Awaiting Approval"})?"#FF7200":r.find(t,{weekStatus:"Approved"})||r.find(t,{weekStatus:"Admin Approved"})||r.find(t,{weekStatus:"Auto Approved"})||r.find(t,{weekStatus:"Escalated"})?"#009432":void 0}checkIfLOPChanged(t,e){return e<8&&t>=8}}return t.\u0275fac=function(e){return new(e||t)(h["\u0275\u0275directiveInject"](h.ViewContainerRef),h["\u0275\u0275directiveInject"](i.a),h["\u0275\u0275directiveInject"](v.b),h["\u0275\u0275directiveInject"](f.a),h["\u0275\u0275directiveInject"](g.a),h["\u0275\u0275directiveInject"](i.g),h["\u0275\u0275directiveInject"](y.a),h["\u0275\u0275directiveInject"](b.a),h["\u0275\u0275directiveInject"](x.a),h["\u0275\u0275directiveInject"](_.a),h["\u0275\u0275directiveInject"](le.a))},t.\u0275cmp=h["\u0275\u0275defineComponent"]({type:t,selectors:[["app-ts-approvals-details"]],decls:30,vars:13,consts:[[1,"container-fluid","timesheet-details","pl-0","pr-0"],[1,"row","pt-1"],[1,"col-3","d-flex","pl-3"],["mat-icon-button","","matTooltip","Back"],[1,"iconButton",3,"click"],[1,"ml-4","my-auto"],["imgWidth","28px","imgHeight","28px",3,"id"],[1,"name","ml-3","my-auto"],[3,"matTooltip",4,"ngIf"],["class","col-4 d-flex",4,"ngIf"],["class","col-2 d-flex",4,"ngIf"],["class","col-3 d-flex","content-type","template",3,"tooltip",4,"ngIf"],["class","col-3 d-flex",4,"ngIf"],["hoursTooltip",""],[1,"col-2","d-flex","pl-5","pt-0"],["mat-icon-button","","class","view-button-inactive ml-5","style","margin-left:9px",3,"matTooltip","disabled","click",4,"ngIf"],["mat-icon-button","","class","view-button-inactive","style","margin-left:9px",3,"matTooltip","disabled","click",4,"ngIf"],["mat-icon-button","","matTooltip","Previous",1,"view-button-inactive",2,"margin-left","9px",3,"disabled","click"],[1,"iconButton"],["mat-icon-button","","matTooltip","Next",1,"view-button-inactive",2,"margin-left","9px",3,"disabled","click"],["class","row mt-2",4,"ngIf"],[1,"row"],[1,"col-12","pl-0","pr-0"],[4,"ngFor","ngForOf"],[3,"matTooltip"],[1,"col-4","d-flex"],["mat-icon-button","",1,"ml-2","my-auto","overall-rfidmachinehours-button"],["matTooltip","Machine Hours",1,"iconButton"],["mat-icon-button","","matTooltip","WFH Hours",1,"ml-2","my-auto","overall-machinehours-button"],["mat-icon-button","","matTooltip","Leave Balance",1,"ml-2","my-auto","leave-balance-button"],[1,"col-2","d-flex"],[1,"mediumSubtleText","my-auto"],["content-type","template",1,"col-3","d-flex",3,"tooltip"],[1,"heading","ml-2"],["class","heading ml-1",4,"ngIf"],[1,"heading","ml-1"],[1,"status-dot-days","ml-1","my-auto",3,"ngClass"],[1,"col-3","d-flex"],[1,"row","tooltip-text"],["mat-icon-button","",1,"view-button-inactive","ml-5",2,"margin-left","9px",3,"matTooltip","disabled","click"],["class","iconButton",4,"ngIf"],["class","spinner-align","diameter","18",4,"ngIf"],["diameter","18",1,"spinner-align"],["mat-icon-button","",1,"view-button-inactive",2,"margin-left","9px",3,"matTooltip","disabled","click"],[1,"row","mt-2"],[1,"col-1x","ml-4","my-auto"],[3,"ngModel","ngModelChange","change",4,"ngIf"],[1,"col-11x","pl-3","pr-3"],[1,"card","mb-2","slide-in-right"],[1,"card-body"],[3,"ngModel","ngModelChange","change"],[4,"ngIf"],[1,"row","d-flex"],[1,"col-3","my-auto","wrap-text"],["class","mediumSubtleText",4,"ngIf"],[1,"heading","ml-2",3,"matTooltip"],["class","col-2 my-auto wrap-text",4,"ngIf"],[1,"col-2","my-auto","wrap-text",2,"padding","0px"],[1,"mediumSubtleText"],["class","col-2 my-auto","content-type","template","style","padding: 0px;",3,"tooltip",4,"ngIf"],["class","col-2 my-auto","style","padding: 0px;",4,"ngIf"],["cchoursTooltip",""],[1,"col-3","my-auto","approver-position",2,"padding","0px"],["class","p-0",4,"ngFor","ngForOf"],["mat-icon-button","","class","edit-button","matTooltip","Edit Approvers",3,"click",4,"ngIf"],[1,"col-2","my-auto","wrap-text"],["content-type","template",1,"col-2","my-auto",2,"padding","0px",3,"tooltip"],[1,"col-2","my-auto",2,"padding","0px"],[1,"mediumSubtleText",2,"padding","0px"],[1,"p-0"],["imgWidth","30px","imgHeight","30px","placement","top","content-type","template","max-width","300","borderWidth","2px",3,"tooltip","id","borderColor"],["placement","top"],["approverTooltip",""],[1,"row","tooltip-text",2,"text-align","center"],[1,"tooltip-status-indicator","p-0","m-0",3,"ngStyle"],["mat-icon-button","","matTooltip","Edit Approvers",1,"edit-button",3,"click"],["class","col-1x ml-4 my-auto",4,"ngIf"],["class","card mb-2 slide-in-right",4,"ngIf"],[1,"card-body","week-card"],[1,"col-2","week-font","di"],["class","col-2x week-font pl-4",4,"ngFor","ngForOf"],[1,"col-2","week-font","pl-0","di"],[1,"status-dot","mr-2",3,"ngClass"],["class","card-body p-1",4,"ngFor","ngForOf"],[1,"col-2x","week-font","pl-4"],[1,"card-body","p-1"],[1,"col-2","di","my-auto"],["class","smallSubtleText mb-1",4,"ngIf"],[1,"heading","mb-0"],["class","col-2x di my-auto",4,"ngFor","ngForOf"],[1,"col-2"],[1,"row","justify-content-center"],["class","col-8 my-auto",4,"ngIf"],[1,"col-4"],["mat-icon-button","",1,"week-action-buttons","di",3,"matTooltip","click"],[1,"iconButton",3,"ngClass"],[1,"smallSubtleText","mb-1"],[1,"col-2x","di","my-auto"],["class","i14","style","color: white;","maxLength","2",3,"disabled","ngStyle","ngClass","ngModel","ngModelChange","ngModelOptions",4,"ngIf"],["class","i14 is-inline-day","readonly","","maxLength","2",3,"ngStyle","ngModel","ngModelChange","ngModelOptions","click",4,"ngIf"],["class","tooltipdisplay",4,"ngIf"],["maxLength","2",1,"i14",2,"color","white",3,"disabled","ngStyle","ngClass","ngModel","ngModelChange","ngModelOptions"],["readonly","","maxLength","2",1,"i14","is-inline-day",3,"ngStyle","ngModel","ngModelChange","ngModelOptions","click"],["inlineEditTs",""],[1,"tooltipdisplay"],[1,"row","tooltip-text",2,"position","relative"],[2,"position","absolute","right","5px"],["style","position: relative;","class","row tooltip-text","style","width: 230px;",4,"ngIf"],[1,"row","tooltip-text",2,"width","230px"],[1,"row","tooltip-text",2,"width","219px","border","1px solid white"],[1,"col-8","my-auto"],[1,"heading","mb-0",3,"matTooltip"],["mat-icon-button","","matTooltip","See History",1,"week-action-buttons","di",3,"click"]],template:function(t,e){1&t&&(h["\u0275\u0275elementStart"](0,"div",0),h["\u0275\u0275elementStart"](1,"div",1),h["\u0275\u0275elementStart"](2,"div",2),h["\u0275\u0275elementStart"](3,"button",3),h["\u0275\u0275elementStart"](4,"mat-icon",4),h["\u0275\u0275listener"]("click",(function(){return e.backToApproversHome()})),h["\u0275\u0275text"](5,"keyboard_arrow_left"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](6,"span",5),h["\u0275\u0275element"](7,"app-user-image",6),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](8,"span",7),h["\u0275\u0275text"](9),h["\u0275\u0275template"](10,ce,2,2,"span",8),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](11,pe,10,3,"div",9),h["\u0275\u0275template"](12,de,6,1,"div",10),h["\u0275\u0275template"](13,he,7,4,"div",11),h["\u0275\u0275template"](14,ve,7,3,"div",12),h["\u0275\u0275template"](15,be,4,2,"ng-template",null,13,h["\u0275\u0275templateRefExtractor"]),h["\u0275\u0275elementStart"](17,"div",14),h["\u0275\u0275template"](18,Ce,3,4,"button",15),h["\u0275\u0275template"](19,we,3,4,"button",16),h["\u0275\u0275elementStart"](20,"button",17),h["\u0275\u0275listener"]("click",(function(){return e.goBack()})),h["\u0275\u0275elementStart"](21,"mat-icon",18),h["\u0275\u0275text"](22,"first_page"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementStart"](23,"button",19),h["\u0275\u0275listener"]("click",(function(){return e.goNext()})),h["\u0275\u0275elementStart"](24,"mat-icon",18),h["\u0275\u0275text"](25,"last_page"),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275template"](26,Fe,9,2,"div",20),h["\u0275\u0275elementStart"](27,"div",21),h["\u0275\u0275elementStart"](28,"div",22),h["\u0275\u0275template"](29,on,5,2,"div",23),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"](),h["\u0275\u0275elementEnd"]()),2&t&&(h["\u0275\u0275advance"](7),h["\u0275\u0275property"]("id",e.globalOid),h["\u0275\u0275advance"](2),h["\u0275\u0275textInterpolate1"]("",e.globalName," "),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf","P"!=(e.submissionDetails[0]?e.submissionDetails[0].group_code:"P")),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",e.rfidDisplay),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!e.rfidDisplay),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",e.otHours>0),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!(e.otHours>0)),h["\u0275\u0275advance"](4),h["\u0275\u0275property"]("ngIf",!e.historyDisplay),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("ngIf",!e.historyDisplay),h["\u0275\u0275advance"](1),h["\u0275\u0275property"]("disabled",e.previousPageDisabled),h["\u0275\u0275advance"](3),h["\u0275\u0275property"]("disabled",e.nextPageDisabled),h["\u0275\u0275advance"](3),h["\u0275\u0275property"]("ngIf",e.costCentreItems.length>0),h["\u0275\u0275advance"](3),h["\u0275\u0275property"]("ngForOf",e.weekItems))},directives:[O.a,M.a,A.a,P.a,s.NgIf,s.NgForOf,H.a,s.NgClass,E.c,j.a,a.v,a.y,s.NgStyle,a.e],styles:['.timesheet-details[_ngcontent-%COMP%]   .smallSubtleText[_ngcontent-%COMP%]{font-size:11px;color:#66615b}.timesheet-details[_ngcontent-%COMP%]   .mediumSubtleText[_ngcontent-%COMP%]{font-size:14px;color:#66615b}.timesheet-details[_ngcontent-%COMP%]   .view-button-active[_ngcontent-%COMP%]{background-color:#cf0001!important;margin-top:5px!important;line-height:8px;width:26px;height:26px;margin-right:5px!important;box-shadow:0 3px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.timesheet-details[_ngcontent-%COMP%]   .view-button-active[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#fff;font-size:18px}.timesheet-details[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]{margin-top:5px!important;line-height:8px;width:26px;height:26px;margin-right:5px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.timesheet-details[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%], .timesheet-details[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.timesheet-details[_ngcontent-%COMP%]   .iconButtonDisabled[_ngcontent-%COMP%]{color:#d3d3d3;font-size:18px}.timesheet-details[_ngcontent-%COMP%]   .name[_ngcontent-%COMP%]{color:#cf0001;font-family:Roboto}.timesheet-details[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%], .timesheet-details[_ngcontent-%COMP%]   .name[_ngcontent-%COMP%]{font-size:14px;font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.timesheet-details[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]{color:#1a1a1a}.timesheet-details[_ngcontent-%COMP%]   .col-2x[_ngcontent-%COMP%]{flex:0 0 9.333333%;max-width:9.333333%}.timesheet-details[_ngcontent-%COMP%]   .col-1x[_ngcontent-%COMP%]{flex:0 0 1.333333%;max-width:1.333333%}.timesheet-details[_ngcontent-%COMP%]   .col-3x[_ngcontent-%COMP%]{flex:0 0 23.666667%;max-width:23.666667%}.timesheet-details[_ngcontent-%COMP%]   .week-font[_ngcontent-%COMP%]{font-size:12px;color:#fff;font-weight:400}.timesheet-details[_ngcontent-%COMP%]   .card-action-buttons[_ngcontent-%COMP%]{width:28px!important;height:28px!important;line-height:28px!important}.timesheet-details[_ngcontent-%COMP%]   .card-action-buttons[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.timesheet-details[_ngcontent-%COMP%]   .week-action-buttons[_ngcontent-%COMP%]{width:35px;height:35px!important;line-height:35px!important}.timesheet-details[_ngcontent-%COMP%]   .week-action-buttons[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.timesheet-details[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%]{width:35px;height:35px!important;visibility:hidden;line-height:35px!important}.timesheet-details[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.timesheet-details[_ngcontent-%COMP%]   .status-dot[_ngcontent-%COMP%]{height:13px;width:13px;border-radius:50%;display:inline-block;vertical-align:middle}.timesheet-details[_ngcontent-%COMP%]   .status-dot-days[_ngcontent-%COMP%]{height:30px;width:37px;font-weight:400;line-height:27px;border-radius:50%;display:inline-block;vertical-align:middle;font-size:13px!important;text-align:center;box-shadow:0 3px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.timesheet-details[_ngcontent-%COMP%]   .week-card[_ngcontent-%COMP%]{background-color:#2c3a47;height:50px!important}.timesheet-details[_ngcontent-%COMP%]   .di[_ngcontent-%COMP%]{display:inline-block!important;text-align:center;vertical-align:middle}.timesheet-details[_ngcontent-%COMP%]   .i14[_ngcontent-%COMP%]{border-radius:5px;border-color:#c7c4c4;padding:5px;height:40px;font-weight:400;text-align:center;text-transform:uppercase;cursor:pointer}.timesheet-details[_ngcontent-%COMP%]   .i14[_ngcontent-%COMP%], .timesheet-details[_ngcontent-%COMP%]   .is-fh-day[_ngcontent-%COMP%]{font-size:15px;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.timesheet-details[_ngcontent-%COMP%]   .is-fh-day[_ngcontent-%COMP%]{border:1px solid #6ab04c;background:#6ab04c;color:#fff}.timesheet-details[_ngcontent-%COMP%]   .is-regular-day[_ngcontent-%COMP%]{border:1px solid #20bf6b;background:#20bf6b}.timesheet-details[_ngcontent-%COMP%]   .is-regular-day[_ngcontent-%COMP%], .timesheet-details[_ngcontent-%COMP%]   .is-wh-day[_ngcontent-%COMP%]{font-size:15px;color:#fff;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.timesheet-details[_ngcontent-%COMP%]   .is-wh-day[_ngcontent-%COMP%]{border:1px solid #9980fa;background:#9980fa}.timesheet-details[_ngcontent-%COMP%]   .is-less-regular-day[_ngcontent-%COMP%]{border:1px solid #badc58;background:#badc58}.timesheet-details[_ngcontent-%COMP%]   .is-leave-day[_ngcontent-%COMP%], .timesheet-details[_ngcontent-%COMP%]   .is-less-regular-day[_ngcontent-%COMP%]{font-size:15px;color:#fff;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.timesheet-details[_ngcontent-%COMP%]   .is-leave-day[_ngcontent-%COMP%]{border:1px solid #eb3b5a;background:#eb3b5a}.timesheet-details[_ngcontent-%COMP%]   .is-empty-day[_ngcontent-%COMP%]{font-size:15px;border:1px solid #c7c4c4}.timesheet-details[_ngcontent-%COMP%]   .is-empty-day[_ngcontent-%COMP%], .timesheet-details[_ngcontent-%COMP%]   .is-lop-day[_ngcontent-%COMP%]{color:#1a1a1a!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.timesheet-details[_ngcontent-%COMP%]   .is-lop-day[_ngcontent-%COMP%]{font-size:10px!important;border:1px solid #cf0001;background:#cf0001}.timesheet-details[_ngcontent-%COMP%]   .is-wo-day[_ngcontent-%COMP%]{font-size:15px;border:1px solid #d1d8e0;background:#d1d8e0;color:#1a1a1a!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.timesheet-details[_ngcontent-%COMP%]   .is-ch-day[_ngcontent-%COMP%]{border:1px solid #01a3a4;background:#01a3a4}.timesheet-details[_ngcontent-%COMP%]   .is-ch-day[_ngcontent-%COMP%], .timesheet-details[_ngcontent-%COMP%]   .is-co-day[_ngcontent-%COMP%]{font-size:15px;color:#fff;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.timesheet-details[_ngcontent-%COMP%]   .is-co-day[_ngcontent-%COMP%]{border:1px solid #5b8c5b;background:#5b8c5b}.timesheet-details[_ngcontent-%COMP%]   .is-t-day[_ngcontent-%COMP%]{border:1px solid #e1b49a;background:#e1b49a}.timesheet-details[_ngcontent-%COMP%]   .is-cl-day[_ngcontent-%COMP%], .timesheet-details[_ngcontent-%COMP%]   .is-t-day[_ngcontent-%COMP%]{font-size:15px;color:#fff;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.timesheet-details[_ngcontent-%COMP%]   .is-cl-day[_ngcontent-%COMP%]{border:1px solid #7f7f7f;background:#7f7f7f}.timesheet-details[_ngcontent-%COMP%]   .is-cvl-day[_ngcontent-%COMP%]{color:#fff;border:1px solid #eb0a3a;background:#eb0a3a}.timesheet-details[_ngcontent-%COMP%]   .is-cvl-day[_ngcontent-%COMP%], .timesheet-details[_ngcontent-%COMP%]   .is-pl-day[_ngcontent-%COMP%]{font-size:15px;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.timesheet-details[_ngcontent-%COMP%]   .is-pl-day[_ngcontent-%COMP%]{color:#1a1a1a!important;border:1px solid #feca57;background:#feca57}.timesheet-details[_ngcontent-%COMP%]   .is-ml-day[_ngcontent-%COMP%]{border:1px solid #f78fb3;background:#f78fb3}.timesheet-details[_ngcontent-%COMP%]   .is-ml-day[_ngcontent-%COMP%], .timesheet-details[_ngcontent-%COMP%]   .is-od-day[_ngcontent-%COMP%]{font-size:15px;color:#fff;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.timesheet-details[_ngcontent-%COMP%]   .is-od-day[_ngcontent-%COMP%]{border:1px solid #a6995f;background:#a6995f}.timesheet-details[_ngcontent-%COMP%]   .is-j-day[_ngcontent-%COMP%]{border:1px solid #16a085;background:#16a085}.timesheet-details[_ngcontent-%COMP%]   .is-j-day[_ngcontent-%COMP%], .timesheet-details[_ngcontent-%COMP%]   .is-lw-day[_ngcontent-%COMP%]{font-size:15px;color:#fff;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.timesheet-details[_ngcontent-%COMP%]   .is-lw-day[_ngcontent-%COMP%]{border:1px solid #d35400;background:#d35400}.timesheet-details[_ngcontent-%COMP%]   .is-inline-day[_ngcontent-%COMP%], .timesheet-details[_ngcontent-%COMP%]   .is-ot-day[_ngcontent-%COMP%]{font-size:15px;color:#fff;border:1px solid #2d2d2d;background:#2d2d2d;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.timesheet-details[_ngcontent-%COMP%]   .is-draft[_ngcontent-%COMP%]{background:grey;color:#fff!important}.timesheet-details[_ngcontent-%COMP%]   .is-submitted[_ngcontent-%COMP%]{background:#ff7200;color:#fff!important}.timesheet-details[_ngcontent-%COMP%]   .is-approved[_ngcontent-%COMP%]{background:#009432;color:#fff!important}.timesheet-details[_ngcontent-%COMP%]   .is-rejected[_ngcontent-%COMP%]{background:#cf0001;color:#fff!important}.timesheet-details[_ngcontent-%COMP%]   .col-11x[_ngcontent-%COMP%]{flex:0 0 95.666667%;max-width:95.666667%}.timesheet-details[_ngcontent-%COMP%]   .slide-in-right[_ngcontent-%COMP%]{animation:slide-in-right .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-right{0%{transform:translateX(30px);opacity:0}to{transform:translateX(0);opacity:1}}.timesheet-details[_ngcontent-%COMP%]   .spinner-align[_ngcontent-%COMP%]{margin:auto;display:inline-block}.timesheet-details[_ngcontent-%COMP%]   .tooltip-text[_ngcontent-%COMP%]{font-size:12px;font-family:Roboto,Helvetica Neue,"sans-serif"!important}.timesheet-details[_ngcontent-%COMP%]   .tooltip-status-indicator[_ngcontent-%COMP%]{font-size:10px;margin-top:9px}.timesheet-details[_ngcontent-%COMP%]   .approver-position[_ngcontent-%COMP%]:hover{cursor:pointer}.timesheet-details[_ngcontent-%COMP%]   .approver-position[_ngcontent-%COMP%]:hover   .edit-button[_ngcontent-%COMP%]{visibility:visible!important;animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}.timesheet-details[_ngcontent-%COMP%]   .wrap-text[_ngcontent-%COMP%]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.timesheet-details[_ngcontent-%COMP%]   .has-comments[_ngcontent-%COMP%]{color:#cf0001!important}.timesheet-details[_ngcontent-%COMP%]     .mat-icon-button[disabled][disabled]{background:#ddd!important}.timesheet-details[_ngcontent-%COMP%]   .leave-balance-button[_ngcontent-%COMP%]{background-color:#eb3b5a!important;cursor:default!important;line-height:8px;width:38px;height:27px;margin-right:12px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.timesheet-details[_ngcontent-%COMP%]   .leave-balance-button[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#fff;font-size:13px;font-weight:700}.timesheet-details[_ngcontent-%COMP%]   .overall-machinehours-button[_ngcontent-%COMP%]{background-color:#bf2020!important;cursor:default!important;line-height:8px;width:38px;height:27px;margin-right:12px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.timesheet-details[_ngcontent-%COMP%]   .overall-machinehours-button[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#fff;font-size:13px;font-weight:700}.timesheet-details[_ngcontent-%COMP%]   .is-lop-changed[_ngcontent-%COMP%]{font-size:15px;color:#fff;background:#20bf6b;border:3px solid #cf0001}.timesheet-details[_ngcontent-%COMP%]   .is-lop-changed[_ngcontent-%COMP%], .timesheet-details[_ngcontent-%COMP%]   .overall-rfidmachinehours-button[_ngcontent-%COMP%]{box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.timesheet-details[_ngcontent-%COMP%]   .overall-rfidmachinehours-button[_ngcontent-%COMP%]{background-color:#f0824f!important;cursor:default!important;line-height:8px;width:38px;height:27px;margin-right:12px!important}.timesheet-details[_ngcontent-%COMP%]   .overall-rfidmachinehours-button[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#fff;font-size:13px;font-weight:700}.timesheet-details[_ngcontent-%COMP%]   .overall-rfiddiffmachinehours-button[_ngcontent-%COMP%]{background-color:#20bf6b!important;border:2px solid #cf0001;cursor:default!important;line-height:8px;width:38px;height:27px;margin-right:12px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.timesheet-details[_ngcontent-%COMP%]   .overall-rfiddiffmachinehours-button[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#fff;font-size:13px;font-weight:700}.timesheet-details[_ngcontent-%COMP%]   .is-bl-day[_ngcontent-%COMP%]{border:1px solid #d82909;background:#d82909}.timesheet-details[_ngcontent-%COMP%]   .is-bl-day[_ngcontent-%COMP%], .timesheet-details[_ngcontent-%COMP%]   .is-cel-day[_ngcontent-%COMP%]{font-size:15px;color:#fff;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.timesheet-details[_ngcontent-%COMP%]   .is-cel-day[_ngcontent-%COMP%]{border:1px solid purple;background:purple}.timesheet-details[_ngcontent-%COMP%]   .is-sl-day[_ngcontent-%COMP%]{font-size:15px;color:#fff;border:1px solid #45546e;background:#45546e;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.timesheet-details[_ngcontent-%COMP%]   .di[_ngcontent-%COMP%]   .tooltipdisplay[_ngcontent-%COMP%]{margin-left:9px;max-width:280px;visibility:hidden;background-color:#000;border-radius:4px;border:1px solid #000;position:absolute;z-index:1;padding:5px;margin-bottom:-175px;opacity:0;transition:opacity .5s;overflow-y:auto;min-width:auto;width:230px;min-height:auto;max-height:250px;color:#fff;overflow-x:hidden;font-size:12px;font-family:Roboto,Helvetica Neue,"sans-serif"!important}.timesheet-details[_ngcontent-%COMP%]   .di[_ngcontent-%COMP%]   .tooltipdisplay[_ngcontent-%COMP%]:after{content:" ";position:absolute;top:5%;right:100%;margin-top:-5px;border:5px solid transparent;border-right-color:#aeaeae}.timesheet-details[_ngcontent-%COMP%]   .di[_ngcontent-%COMP%]:hover   .tooltipdisplay[_ngcontent-%COMP%]{visibility:visible;opacity:1}.timesheet-details[_ngcontent-%COMP%]   .is-oh-day[_ngcontent-%COMP%]{font-size:15px;color:#1a1a1a;border:1px solid #575afe;background:#575afe;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.timesheet-details[_ngcontent-%COMP%]   .is-h-day[_ngcontent-%COMP%]{border:1px solid #6ab04c;background:#6ab04c}.timesheet-details[_ngcontent-%COMP%]   .is-h-day[_ngcontent-%COMP%], .timesheet-details[_ngcontent-%COMP%]   .is-wl-day[_ngcontent-%COMP%]{font-size:15px;color:#fff;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.timesheet-details[_ngcontent-%COMP%]   .is-wl-day[_ngcontent-%COMP%]{border:1px solid #b7d251;background:#b7d251}']}),t})()}]}];let rn=(()=>{class t{}return t.\u0275mod=h["\u0275\u0275defineNgModule"]({type:t}),t.\u0275inj=h["\u0275\u0275defineInjector"]({factory:function(e){return new(e||t)},imports:[[i.k.forChild(an)],i.k]}),t})();var ln=n("Xi0T"),cn=n("d3UM"),pn=n("/1cH"),dn=n("MutI"),mn=n("3beV");let un=(()=>{class t{}return t.\u0275mod=h["\u0275\u0275defineNgModule"]({type:t}),t.\u0275inj=h["\u0275\u0275defineInjector"]({factory:function(e){return new(e||t)},imports:[[s.CommonModule,rn,ln.a,O.b,M.b,a.p,a.E,A.b,cn.d,pn.c,D.e,m.v,dn.d,k.e,I.c,T.h,E.b,v.g,R.a,H.b,w.c,j.b,mn.a]]}),t})()},"4PPT":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),n("8Yhr")},"8Yhr":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var s=n("qCKp"),i=n("Vzig");s.Observable.prototype.throttleTime=i.throttleTime},DlyV:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),n("QtPd")},"LOr+":function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var s=n("qCKp"),i=n("kU1M");e.debounceTime=function(t,e){return void 0===e&&(e=s.asyncScheduler),i.debounceTime(t,e)(this)}},QtPd:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var s=n("qCKp"),i=n("LOr+");s.Observable.prototype.debounceTime=i.debounceTime},Vzig:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var s=n("qCKp"),i=n("w0v+"),o=n("kU1M");e.throttleTime=function(t,e,n){return void 0===e&&(e=s.asyncScheduler),void 0===n&&(n=i.defaultThrottleConfig),o.throttleTime(t,e,n)(this)}},Xj2z:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var s=n("qCKp");s.Observable.fromEvent=s.fromEvent},hJL4:function(t,e,n){"use strict";n.d(e,"a",(function(){return d}));var s=n("mrSG"),i=n("XNiG"),o=n("xG9w"),a=n("fXoL"),r=n("tk/3"),l=n("LcQX"),c=n("XXEo"),p=n("flaP");let d=(()=>{class t{constructor(t,e,n,s){this.http=t,this.UtilityService=e,this.loginService=n,this.roleService=s,this.msg=new i.b,this.taskStatusColor=[],this.approveRequest=(t,e)=>this.http.post("/api/isa/request/approveResourceRequest",{requestDetails:t,approverOid:e}),this.rejectRequest=(t,e)=>this.http.post("/api/isa/request/rejectResourceRequest",{requestDetails:t,approverOid:e}),this.getTaskStatusColor()}getAllRequestsOfUser(t,e,n,s,i,o,a){let r=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getAllRequestsOfUser",{oid:t,statusId:e,statusCode:n,objectIds:s,skip:i,limit:o,filterConfig:a,orgIds:r})}getAllRoleAccess(){return o.where(this.roleService.roles,{application_id:139})}getRequestsBasedOnStatus(t,e,n,s,i,o,a){let r=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getRequestsBasedOnStatus",{oid:t,statusId:e,statusCode:n,objectIds:s,skip:i,limit:o,filterConfig:a,orgIds:r})}getRequestsForAwaitingApproval(t,e,n,s){return this.http.post("/api/isa/request/getRequestsForAwaitingApproval",{oid:t,skip:e,limit:n,filterConfig:s})}getObjectIdsBasedOnOid(t){return this.http.post("/api/isa/request/getObjectIdsBasedOnOid",{oid:t})}getStatusCountBasedOnOid(t,e,n,s){let i=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getStatusCountBasedOnOid",{oid:t,objectIds:e,dataTypeArray:n,filterConfig:s,orgIds:i})}getHeaderCount(t){return this.http.post("/api/isa/request/getHeaderCount",{oid:t})}getRequestBasedOnStatus(t,e){return this.http.post("/api/isa/request/getRequestBasedOnStatus",{oid:t,status:e})}getDataTypeArray(){return this.http.post("/api/isa/request/getISAStatusMasterDataUDRF",{})}sendMsg(t){this.msg.next(t)}getMsg(){return this.msg.asObservable()}getTaskStatusColor(){return new Promise((t,e)=>{this.http.post("/api/isa/request/getTaskStatusNameColor",{}).subscribe(e=>(this.taskStatusColor=e,t(e)),t=>{})})}getAllRequestsForRmg(t){return this.http.post("/api/isa/request/getAllRequestsForRmg",{filterConfig:t})}getTotalForRmg(t){return new Promise((e,n)=>{this.http.post("/api/isa/request/getTotalForRmg",{filterConfig:t}).subscribe(t=>e(t),t=>{})})}getDefaultISAStatusToDisplay(){return this.http.post("/api/isa/request/getISADefaultStatusToDisplay",{})}getCTCbreakUpForSkillSet(t){return new Promise((e,n)=>{this.http.post("/api/isa/request/getCTCbreakUpForSkillSet",t).subscribe(t=>e(t.data),t=>{n(t)})})}getCurrentUserRole(){return this.roleService&&this.roleService.roles&&this.roleService.roles.length>0?this.roleService.roles[0].role_id:null}getTemplateForUser(t){return this.http.post("/api/isa/request/getVisibleFormFieldsForUser",t)}saveAndApproveRequest(t){return this.http.post("/api/isa/request/updBudgetInfoByRequestId",t)}getVisibilityMatrix(t){return this.http.post("/api/isa/request/getAuthorzedFieldsByRoleId",t)}getISAAttachmentFromS3(t){return this.http.post("/api/isa/attachment/getISAAttachmentFromS3",{key:t})}getSkillType(){return this.http.post("/api/isa/request/getSkillType",{})}getSkillLevel(){return this.http.post("/api/isa/request/getSkillLevel",{})}getSkillExperience(){return this.http.post("/api/isa/request/getSkillExperience",{})}getSkillName(t){return this.http.post("/api/isa/request/getSkillName",{skill_type_id:t})}getIsaActivityReportDownload(){return this.http.post("/api/isa/request/getIsaActivityReportDownload",{})}getISASLAreport(t){return this.http.post("/api/isa/request/getISASLAreport",{filterConfig:t})}getISACustomReport(t){return this.http.post("/api/isa/request/getISACustomReport",{filterConfig:t})}getPositionType(){return this.http.post("/api/employee360/masterData/getPositionType",{})}calculateNetCostBasedOnPosition(t,e,n,i,a,r,l){return Object(s.c)(this,void 0,void 0,(function*(){let s;s=r&&r.length>1&&(yield this.getManpowerCostByOId(r,n,a,2))||(yield this.getManpowerCostBasedOnPosition(t,e,n,a,l));let c=yield this.getNonManpowerCost(e,n,i,a,2),p=yield this.getAllocatedCost(),d=0;d=(s?s.cost:0)+c.length>0?o.reduce(o.pluck(c,"cost"),(t,e)=>t+e,0):0;let m=p.length>0?o.reduce(o.pluck(p,"percentage"),(t,e)=>t+e,0):0;return{cost:d,currency:s&&s.currency_code?s.currency_code:"",manpowerCost:s,nonManpowerCost:c,allocatedCost:p,allocatedCostValue:d*(m/100)}}))}getManpowerCostBasedOnPosition(t,e,n,s,i){return new Promise((o,a)=>{this.http.post("/api/isa/configuration/getManpowerCostBasedOnPosition",{skillRoleId:t,nationalityId:e,locationGroupId:n,unit:s,position:i}).subscribe(t=>o(t),t=>(console.log(t),a(t)))})}getNonManpowerCost(t,e,n,s,i){return new Promise((o,a)=>{this.http.post("/api/project/getNonManpowerCost",{nationalityId:t,locationGroupId:e,locationId:n,unit:s,currency_id:i}).subscribe(t=>o(t),t=>(console.log(t),a(t)))})}getAllocatedCost(){return new Promise((t,e)=>{this.http.post("/api/project/getAllocatedCost","").subscribe(e=>t(e),t=>(console.log(t),e(t)))})}getManpowerCostByOId(t,e,n,s){return new Promise((i,o)=>{this.http.post("/api/project/getEmployeeSalary",{oid:t,location_group_id:e,unit_id:n,currency_id:s}).subscribe(t=>i(t),t=>(console.log(t),o(t)))})}getSkillNameMaster(){return this.http.post("/api/isa/configuration/getSkillName",{})}getVendorOid(t,e){return this.http.post("/api/isa/request/getVendorOid",{vendorNames:t,docID:e})}getVendorList(t,e){return this.http.post("/api/isa/request/getVendorList",{documentID:t,limitval:e})}removeVendor(t,e){return this.http.post("/api/isa/request/removeVendors",{vendorOid:t,documentID:e})}checkVendorOrNot(){return this.http.post("/api/isa/request/checkVendorOrNot",{})}}return t.\u0275fac=function(e){return new(e||t)(a["\u0275\u0275inject"](r.c),a["\u0275\u0275inject"](l.a),a["\u0275\u0275inject"](c.a),a["\u0275\u0275inject"](p.a))},t.\u0275prov=a["\u0275\u0275defineInjectable"]({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()},sx9y:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),n("Xj2z")},v2fc:function(t,e,n){"use strict";n.d(e,"a",(function(){return m}));var s=n("xG9w"),i=n("wd/R"),o=n("2Vo4"),a=n("XNiG"),r=n("fXoL"),l=n("tk/3"),c=n("LcQX"),p=n("flaP"),d=n("XXEo");let m=(()=>{class t{constructor(t,e,n,s){this.$http=t,this._util=e,this._roles=n,this._auth=s,this.currentUser=this._auth.getProfile().profile,this.token=this._auth.getJwtToken(),this.isaBudgettedAttachmentSubject=new o.a({}),this.getisaBudgettedAttachmentObservable=this.isaBudgettedAttachmentSubject.asObservable(),this.activitySubject=new a.b,this.getActivityObservable=this.activitySubject.asObservable(),this.priority_status_colors=[{statusName:"Low",statusColor:"#BADC58"},{statusName:"Medium",statusColor:"#91AECB"},{statusName:"High",statusColor:"#FFA502"},{statusName:"Very High",statusColor:"#cf0001"}],this.getVendorDetailsData=()=>this.$http.post("/api/isa/request/getVendorForList",{})}showMessage(t){this._util.showToastMessage(t)}showErrorMessage(t){this._util.showErrorMessage(t,"KEBS")}getToken(){return this._auth.getToken()}getCurrentUserRole(){return this._roles&&this._roles.roles&&this._roles.roles.length>0?this._roles.roles[0].role_id:null}setActivityObservable(t){this.activitySubject.next(t)}getStatusObjectEntries(){let t=s.where(this._roles.roles,{application_id:139,object_id:68});return t.length>0?JSON.parse(t[0].object_entries):null}resolveActivityTemplate(t,e,n,o,a,r){let l=s.where(t,{activity_name:("Status"==e?"status":"Priority"==e?"priority":"RMG Owner"==e?"rmgOwner":"TAG Owner"==e?"tagOwner":null)||e});return l.length>0?{activity_type_id:l[0]._id,activity_description:this.getActivityDescription(l[0],n,o,a,r),activity_by:this.currentUser.oid,activity_created_date:i()}:{}}getActivityDescription(t,e,n,s,i){let o="";return o=t.activity_template[e],n&&(o=o.replace("from_value",n)),s&&(o=o.replace("to_value",s)),i&&(o=o.replace("object_name",i)),o}getRequestById(t){return this.$http.post("/api/isa/request/getRequestById",{requestId:t})}getActivityTypes(){return this.$http.post("/api/isa/request/getActivityTypeMasterData",{})}getStatusMasterData(){return this.$http.post("/api/isa/request/getISAStatusMasterData",{statusValues:this.getStatusObjectEntries()})}updateRequestStatus(t,e,n,s){return this.$http.post("/api/isa/request/statusChange",{requestId:t,statusRefId:e,currentStatusId:n,activityTemplate:s})}updateKeyValueInResourceRequest(t,e,n){return this.$http.post("/api/isa/request/updateKeyValueInResourceRequest",{requestId:t,activityTemplate:e,modifyKeyValue:n})}changeTagOrRmgOwner(t,e,n,s){return this.$http.post("/api/isa/request/changeTagOrRmgOwner",{requestId:t,activityTemplate:e,type:n,changedOid:s})}getActivity(t){return this.$http.post("/api/isa/request/activityRetreivalBasedOnRequestIdOrActivityId",{activityId:t})}insertISAActivity(t,e){return this.$http.post("/api/isa/request/insertISAActivity",{requestId:t,activityTemplate:e})}editISAActivity(t,e){return this.$http.post("/api/isa/request/editISAActivity",{activity_id:t,activity_details:e})}getISAAttachmentById(t){return this.$http.post("/api/isa/attachment/getISAAttachmentById",{request_id:t})}getISAAttachmentFromS3(t){return this.$http.post("/api/isa/attachment/getISAAttachmentFromS3",{key:t})}deleteISAAttachment(t,e,n){return this.$http.post("/api/isa/attachment/deleteISAAttachment",{requestId:t,file:e,activityTemplate:n})}updateISAAttachment(t,e,n,s){return this.$http.post("/api/isa/attachment/updateISAAttachment",{requestId:t,file:n,attachmentId:e,activityTemplate:s})}getApproverStatuses(t,e){return this.$http.post("/api/isa/request/getApproverStatusForRequest",{workflowHeaderId:t,approvers:e})}createTask(t,e,n){return this.$http.post("/api/isa/tasks/createTasks",{request_id:t,tasks:e,activityTemplate:n})}getRequestTasks(t){return this.$http.post("/api/isa/tasks/getTaskById",{request_id:t})}addTask(t,e,n,s){return this.$http.post("/api/isa/tasks/addTasks",{task_id:t,request_id:e,tasks:n,activityTemplate:s})}updateTaskObject(t,e,n,s,i){return this.$http.post("/api/isa/tasks/updateTaskObject",{task_id:t,sub_task_id:e,key:n,object:s,activityTemplate:i})}getTaskTemplate(){return this.$http.post("/api/isa/tasks/getTaskTemplates",{})}createTaskFromTemplate(t,e,n,s,i,o,a,r){return this.$http.post("/api/isa/tasks/assignTaskFromTemplate",{task_template_id:t,request_id:e,task_id:n,rmg_owner:s,tag_owner:i,activityTemplate:o,taskTypesList:a,requestSkillId:r})}updateTaskDataFromAttachment(t){return this.$http.post("/api/isa/tasks/updateTaskNameAndOwner",t)}updateExtTaskExtAtchId(t){return this.$http.post("/api/isa/tasks/updateExtTaskExtAtchId",t)}getTaskStatusList(){return this.$http.post("/api/isa/tasks/getTaskStatus",{})}getTaskTypeList(){return this.$http.post("/api/isa/tasks/getTaskTypeMasterData",{})}updateTypeListInReq(t){return this.$http.post("/api/isa/request/insertTaskTypeOwnerDetail",t)}updateTypeAssigned(t){return this.$http.post("/api/isa/request/updTaskTypeOwnerDetail",t)}deleteTask(t,e,n,s,i){return this.$http.post("/api/isa/tasks/changeTaskFlag",{request_id:t,task_id:e,sub_task_id:n,is_active:s,activityTemplate:i})}updateTaskAssigned(t,e,n,s,i){return this.$http.post("/api/isa/tasks/updateAssignedToTask",{request_id:t,task_id:e,sub_task_id:n,assigned_to:s,activityTemplate:i})}updateTaskData(t,e,n,s,i,o){return this.$http.post("/api/isa/tasks/updateTaskData",{request_id:t,task_id:e,sub_task_id:n,key:s,value:i,activityTemplate:o})}getRequestCTAs(t){return this.$http.post("/api/isa/configuration/getRequestCTAs",{requestId:t})}getISAWfConfig(){return this.$http.post("/api/isa/configuration/getISAWfConfig",{})}getTodoDetails(t){return this.$http.post("/api/isa/todo/getTodo",{todoId:t})}createTodo(t,e,n){return this.$http.post("/api/isa/todo/createToDo",{request_id:t,to_do_list:e,activityTemplate:n})}insertTodo(t,e,n,s){return this.$http.post("/api/isa/todo/insertToDo",{request_id:t,to_do_id:e,to_do_list:n,activityTemplate:s})}editTodo(t,e,n,s){return this.$http.post("/api/isa/todo/editToDo",{request_id:t,to_do_id:e,to_do_details:n,activityTemplate:s})}updateResourceInRequest(t,e,n,s,i){return this.$http.post("/api/isa/request/updateResourceInRequest",{requestId:t,activityTemplate:e,wfActivityTemplate:n,statusRefId:s,resourceOid:i})}triggerWfOnSubmission(t,e,n,s,i,o,a,r){return this.$http.post("/api/isa/request/triggerWfOnSubmission",{requestId:t,activityTemplate:e,wfActivityTemplate:n,statusRefId:s,task_id:i,sub_task_id:o,task_status:a,wfConfig:r})}addIsaTaskActualHours(t,e,n,s,i){return this.$http.post("/api/isa/tasks/addIsaTaskActualHours",{task_id:t,task_item:e,actual_hours:n,timesheet_id:s,request_id:i})}deleteIsaTaskActualHours(t,e,n,s,i){return this.$http.post("/api/isa/tasks/deleteIsaTaskActualHours",{task_id:t,task_item:e,actual_hours:n,request_id:s,timesheet_id:i})}getSLAByRequestId(t){return this.$http.post("/api/isa/request/getSLAByRequestId",{requestId:t})}getRequestBasedOnSearch(t){return this.$http.post("/api/isa/request/getRequestBasedOnSearch",{searchParameter:t})}moveTaskToRequest(t,e,n,s,i,o){return this.$http.post("/api/isa/tasks/moveTaskToRequest",{fromRequestId:t,fromTaskId:e,mainTaskId:n,toRequestId:s,toTaskId:i,activityTemplate:o})}removeResourceFromRequest(t,e,n,s,i){return this.$http.post("/api/isa/request/removeResourceFromRequest",{requestId:t,statusId:e,statusRefId:n,resActivityTemplate:s,statusActivityTemplate:i})}checkIfUserHasAccessToRequest(t){return this.$http.post("/api/isa/request/checkIfUserHasAccessToRequest",{requestId:t,oid:this.currentUser.oid})}updateEmployeeStatus(t){return this.$http.post("/api/obPrimary/updateEmployeeStatus",t)}checkIfEmailHasRequestId(t){return this.$http.post("/api/obPrimary/checkIfEmailHasRequestId",{apiParams:{emailId:t}})}createISAAttachment(t){return this.$http.post("/api/isa/attachment/createIsaAttachment",t)}getSourceList(){return this.$http.post("/api/isa/attachment/getAllSource",{})}saveEmailtoTask(t){return this.$http.post("/api/isa/tasks/saveEmailtoTask",t)}setISABudgetedAttachmentActivityObservable(t){this.isaBudgettedAttachmentSubject.next(t)}getTemplateForUser(t){return this.$http.post("/api/isa/request/getVisibleFormFieldsForUser",t)}budgetApprovalCheck(t){return this.$http.post("/api/isa/request/ctcRangeCheck",t)}checkElligibleToSubmit(t){return new Promise((e,n)=>{this.$http.post("/api/isa/request/budgetApprovalCheck",t).subscribe(t=>e(t),t=>n(t))})}offerApprovalCheck(t){return this.$http.post("/api/isa/request/offerApprovalCheck",t)}triggerWfOnBudgetApproval(t,e,n,s,i){return this.$http.post("/api/isa/request/triggerWfOnBudgetApproval",{requestId:t,activityTemplate:e,wfActivityTemplate:n,statusRefId:s,wfConfig:i})}getMasterDataByMasterDataName(t){return this.$http.post("/api/isa/request/getMasterDataByMasterDataName",t)}getVisibilityMatrix(t){return this.$http.post("/api/isa/request/getAuthorzedFieldsByRoleId",t)}getAllRoleAccess(){return s.where(this._roles.roles,{application_id:139})}}return t.\u0275fac=function(e){return new(e||t)(r["\u0275\u0275inject"](l.c),r["\u0275\u0275inject"](c.a),r["\u0275\u0275inject"](p.a),r["\u0275\u0275inject"](d.a))},t.\u0275prov=r["\u0275\u0275defineInjectable"]({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()},"w0v+":function(t,e,n){"use strict";n.r(e),n.d(e,"config",(function(){return s.a})),n.d(e,"InnerSubscriber",(function(){return i.a})),n.d(e,"OuterSubscriber",(function(){return o.a})),n.d(e,"Scheduler",(function(){return a.a})),n.d(e,"AnonymousSubject",(function(){return r.a})),n.d(e,"SubjectSubscription",(function(){return l.a})),n.d(e,"Subscriber",(function(){return c.a})),n.d(e,"fromPromise",(function(){return u})),n.d(e,"fromIterable",(function(){return f})),n.d(e,"ajax",(function(){return j})),n.d(e,"webSocket",(function(){return Y})),n.d(e,"ajaxGet",(function(){return _})),n.d(e,"ajaxPost",(function(){return S})),n.d(e,"ajaxDelete",(function(){return w})),n.d(e,"ajaxPut",(function(){return k})),n.d(e,"ajaxPatch",(function(){return A})),n.d(e,"ajaxGetJSON",(function(){return O})),n.d(e,"AjaxObservable",(function(){return M})),n.d(e,"AjaxSubscriber",(function(){return D})),n.d(e,"AjaxResponse",(function(){return T})),n.d(e,"AjaxError",(function(){return E})),n.d(e,"AjaxTimeoutError",(function(){return H})),n.d(e,"WebSocketSubject",(function(){return $})),n.d(e,"CombineLatestOperator",(function(){return F.a})),n.d(e,"dispatch",(function(){return B.a})),n.d(e,"SubscribeOnObservable",(function(){return q.a})),n.d(e,"Timestamp",(function(){return U.a})),n.d(e,"TimeInterval",(function(){return W.a})),n.d(e,"GroupedObservable",(function(){return N.a})),n.d(e,"defaultThrottleConfig",(function(){return z.a})),n.d(e,"rxSubscriber",(function(){return X.a})),n.d(e,"iterator",(function(){return G.a})),n.d(e,"observable",(function(){return J.a})),n.d(e,"ArgumentOutOfRangeError",(function(){return Q.a})),n.d(e,"EmptyError",(function(){return K.a})),n.d(e,"Immediate",(function(){return Z.a})),n.d(e,"ObjectUnsubscribedError",(function(){return tt.a})),n.d(e,"TimeoutError",(function(){return et.a})),n.d(e,"UnsubscriptionError",(function(){return nt.a})),n.d(e,"applyMixins",(function(){return st})),n.d(e,"errorObject",(function(){return it})),n.d(e,"hostReportError",(function(){return ot.a})),n.d(e,"identity",(function(){return at.a})),n.d(e,"isArray",(function(){return rt.a})),n.d(e,"isArrayLike",(function(){return lt.a})),n.d(e,"isDate",(function(){return ct.a})),n.d(e,"isFunction",(function(){return pt.a})),n.d(e,"isIterable",(function(){return dt.a})),n.d(e,"isNumeric",(function(){return mt.a})),n.d(e,"isObject",(function(){return ut.a})),n.d(e,"isObservable",(function(){return ht.a})),n.d(e,"isPromise",(function(){return gt.a})),n.d(e,"isScheduler",(function(){return ft.a})),n.d(e,"noop",(function(){return vt.a})),n.d(e,"not",(function(){return bt.a})),n.d(e,"pipe",(function(){return xt.a})),n.d(e,"root",(function(){return y})),n.d(e,"subscribeTo",(function(){return yt.a})),n.d(e,"subscribeToArray",(function(){return Ct.a})),n.d(e,"subscribeToIterable",(function(){return h.a})),n.d(e,"subscribeToObservable",(function(){return _t.a})),n.d(e,"subscribeToPromise",(function(){return d.a})),n.d(e,"subscribeToResult",(function(){return St.a})),n.d(e,"toSubscriber",(function(){return wt.a})),n.d(e,"tryCatch",(function(){return It}));var s=n("2fFW"),i=n("51Dv"),o=n("l7GE"),a=n("Y/cZ"),r=n("XNiG"),l=n("Ylt2"),c=n("7o/Q"),p=n("HDdC"),d=n("a7t3"),m=n("4yVj");function u(t,e){return e?Object(m.a)(t,e):new p.a(Object(d.a)(t))}var h=n("pLzU"),g=n("MBAA");function f(t,e){if(!t)throw new Error("Iterable cannot be null");return e?Object(g.a)(t,e):new p.a(Object(h.a)(t))}const v="undefined"!=typeof window&&window,b="undefined"!=typeof self&&"undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&self,x="undefined"!=typeof global&&global,y=v||x||b;var C=n("lJxs");function _(t,e=null){return new M({method:"GET",url:t,headers:e})}function S(t,e,n){return new M({method:"POST",url:t,body:e,headers:n})}function w(t,e){return new M({method:"DELETE",url:t,headers:e})}function k(t,e,n){return new M({method:"PUT",url:t,body:e,headers:n})}function A(t,e,n){return new M({method:"PATCH",url:t,body:e,headers:n})}const I=Object(C.a)((t,e)=>t.response);function O(t,e){return I(new M({method:"GET",url:t,responseType:"json",headers:e}))}let M=(()=>{class t extends p.a{constructor(t){super();const e={async:!0,createXHR:function(){return this.crossDomain?function(){if(y.XMLHttpRequest)return new y.XMLHttpRequest;if(y.XDomainRequest)return new y.XDomainRequest;throw new Error("CORS is not supported by your browser")}():function(){if(y.XMLHttpRequest)return new y.XMLHttpRequest;{let e;try{const n=["Msxml2.XMLHTTP","Microsoft.XMLHTTP","Msxml2.XMLHTTP.4.0"];for(let s=0;s<3;s++)try{if(e=n[s],new y.ActiveXObject(e))break}catch(t){}return new y.ActiveXObject(e)}catch(t){throw new Error("XMLHttpRequest is not supported by your browser")}}}()},crossDomain:!0,withCredentials:!1,headers:{},method:"GET",responseType:"json",timeout:0};if("string"==typeof t)e.url=t;else for(const n in t)t.hasOwnProperty(n)&&(e[n]=t[n]);this.request=e}_subscribe(t){return new D(t,this.request)}}return t.create=(()=>{const e=e=>new t(e);return e.get=_,e.post=S,e.delete=w,e.put=k,e.patch=A,e.getJSON=O,e})(),t})();class D extends c.a{constructor(t,e){super(t),this.request=e,this.done=!1;const n=e.headers=e.headers||{};e.crossDomain||this.getHeader(n,"X-Requested-With")||(n["X-Requested-With"]="XMLHttpRequest"),this.getHeader(n,"Content-Type")||y.FormData&&e.body instanceof y.FormData||void 0===e.body||(n["Content-Type"]="application/x-www-form-urlencoded; charset=UTF-8"),e.body=this.serializeBody(e.body,this.getHeader(e.headers,"Content-Type")),this.send()}next(t){this.done=!0;const{xhr:e,request:n,destination:s}=this;let i;try{i=new T(t,e,n)}catch(o){return s.error(o)}s.next(i)}send(){const{request:t,request:{user:e,method:n,url:s,async:i,password:o,headers:a,body:r}}=this;try{const l=this.xhr=t.createXHR();this.setupEvents(l,t),e?l.open(n,s,i,e,o):l.open(n,s,i),i&&(l.timeout=t.timeout,l.responseType=t.responseType),"withCredentials"in l&&(l.withCredentials=!!t.withCredentials),this.setHeaders(l,a),r?l.send(r):l.send()}catch(l){this.error(l)}}serializeBody(t,e){if(!t||"string"==typeof t)return t;if(y.FormData&&t instanceof y.FormData)return t;if(e){const t=e.indexOf(";");-1!==t&&(e=e.substring(0,t))}switch(e){case"application/x-www-form-urlencoded":return Object.keys(t).map(e=>`${encodeURIComponent(e)}=${encodeURIComponent(t[e])}`).join("&");case"application/json":return JSON.stringify(t);default:return t}}setHeaders(t,e){for(let n in e)e.hasOwnProperty(n)&&t.setRequestHeader(n,e[n])}getHeader(t,e){for(let n in t)if(n.toLowerCase()===e.toLowerCase())return t[n]}setupEvents(t,e){const n=e.progressSubscriber;function s(t){const{subscriber:e,progressSubscriber:n,request:i}=s;let o;n&&n.error(t);try{o=new H(this,i)}catch(a){o=a}e.error(o)}if(t.ontimeout=s,s.request=e,s.subscriber=this,s.progressSubscriber=n,t.upload&&"withCredentials"in t){if(n){let e;e=function(t){const{progressSubscriber:n}=e;n.next(t)},y.XDomainRequest?t.onprogress=e:t.upload.onprogress=e,e.progressSubscriber=n}let s;s=function(t){const{progressSubscriber:e,subscriber:n,request:i}=s;let o;e&&e.error(t);try{o=new E("ajax error",this,i)}catch(a){o=a}n.error(o)},t.onerror=s,s.request=e,s.subscriber=this,s.progressSubscriber=n}function i(t){}function o(t){const{subscriber:e,progressSubscriber:n,request:s}=o;if(4===this.readyState){let o=1223===this.status?204:this.status,a="text"===this.responseType?this.response||this.responseText:this.response;if(0===o&&(o=a?200:0),o<400)n&&n.complete(),e.next(t),e.complete();else{let a;n&&n.error(t);try{a=new E("ajax error "+o,this,s)}catch(i){a=i}e.error(a)}}}t.onreadystatechange=i,i.subscriber=this,i.progressSubscriber=n,i.request=e,t.onload=o,o.subscriber=this,o.progressSubscriber=n,o.request=e}unsubscribe(){const{done:t,xhr:e}=this;!t&&e&&4!==e.readyState&&"function"==typeof e.abort&&e.abort(),super.unsubscribe()}}class T{constructor(t,e,n){this.originalEvent=t,this.xhr=e,this.request=n,this.status=e.status,this.responseType=e.responseType||n.responseType,this.response=P(this.responseType,e)}}const E=(()=>{function t(t,e,n){return Error.call(this),this.message=t,this.name="AjaxError",this.xhr=e,this.request=n,this.status=e.status,this.responseType=e.responseType||n.responseType,this.response=P(this.responseType,e),this}return t.prototype=Object.create(Error.prototype),t})();function P(t,e){switch(t){case"json":return function(t){return"response"in t?t.responseType?t.response:JSON.parse(t.response||t.responseText||"null"):JSON.parse(t.responseText||"null")}(e);case"xml":return e.responseXML;case"text":default:return"response"in e?e.response:e.responseText}}const H=function(t,e){return E.call(this,"ajax timeout",t,e),this.name="AjaxTimeoutError",this},j=(()=>M.create)();var R=n("quSY"),V=n("jtHE");const L={url:"",deserializer:t=>JSON.parse(t.data),serializer:t=>JSON.stringify(t)};class $ extends r.a{constructor(t,e){if(super(),t instanceof p.a)this.destination=e,this.source=t;else{const e=this._config=Object.assign({},L);if(this._output=new r.b,"string"==typeof t)e.url=t;else for(let n in t)t.hasOwnProperty(n)&&(e[n]=t[n]);if(!e.WebSocketCtor&&WebSocket)e.WebSocketCtor=WebSocket;else if(!e.WebSocketCtor)throw new Error("no WebSocket constructor can be found");this.destination=new V.a}}lift(t){const e=new $(this._config,this.destination);return e.operator=t,e.source=this,e}_resetState(){this._socket=null,this.source||(this.destination=new V.a),this._output=new r.b}multiplex(t,e,n){const s=this;return new p.a(i=>{try{s.next(t())}catch(a){i.error(a)}const o=s.subscribe(t=>{try{n(t)&&i.next(t)}catch(a){i.error(a)}},t=>i.error(t),()=>i.complete());return()=>{try{s.next(e())}catch(a){i.error(a)}o.unsubscribe()}})}_connectSocket(){const{WebSocketCtor:t,protocol:e,url:n,binaryType:s}=this._config,i=this._output;let o=null;try{o=e?new t(n,e):new t(n),this._socket=o,s&&(this._socket.binaryType=s)}catch(r){return void i.error(r)}const a=new R.a(()=>{this._socket=null,o&&1===o.readyState&&o.close()});o.onopen=t=>{const{_socket:e}=this;if(!e)return o.close(),void this._resetState();const{openObserver:n}=this._config;n&&n.next(t);const s=this.destination;this.destination=c.a.create(e=>{if(1===o.readyState)try{const{serializer:t}=this._config;o.send(t(e))}catch(t){this.destination.error(t)}},t=>{const{closingObserver:e}=this._config;e&&e.next(void 0),t&&t.code?o.close(t.code,t.reason):i.error(new TypeError("WebSocketSubject.error must be called with an object with an error code, and an optional reason: { code: number, reason: string }")),this._resetState()},()=>{const{closingObserver:t}=this._config;t&&t.next(void 0),o.close(),this._resetState()}),s&&s instanceof V.a&&a.add(s.subscribe(this.destination))},o.onerror=t=>{this._resetState(),i.error(t)},o.onclose=t=>{this._resetState();const{closeObserver:e}=this._config;e&&e.next(t),t.wasClean?i.complete():i.error(t)},o.onmessage=t=>{try{const{deserializer:e}=this._config;i.next(e(t))}catch(e){i.error(e)}}}_subscribe(t){const{source:e}=this;return e?e.subscribe(t):(this._socket||this._connectSocket(),this._output.subscribe(t),t.add(()=>{const{_socket:t}=this;0===this._output.observers.length&&(t&&1===t.readyState&&t.close(),this._resetState())}),t)}unsubscribe(){const{_socket:t}=this;t&&1===t.readyState&&t.close(),this._resetState(),super.unsubscribe()}}function Y(t){return new $(t)}var F=n("itXk"),B=n("NNCq"),q=n("O4y0"),U=n("r0WS"),W=n("4hIw"),N=n("OQgR"),z=n("yuhW"),X=n("2QA8"),G=n("Lhse"),J=n("kJWO"),Q=n("4I5i"),K=n("sVev"),Z=n("c7jc"),tt=n("9ppp"),et=n("Y6u4"),nt=n("pjAE");function st(t,e){for(let n=0,s=e.length;n<s;n++){const s=e[n],i=Object.getOwnPropertyNames(s.prototype);for(let e=0,n=i.length;e<n;e++){const n=i[e];t.prototype[n]=s.prototype[n]}}}const it={e:{}};var ot=n("NJ4a"),at=n("SpAZ"),rt=n("DH7j"),lt=n("I55L"),ct=n("mlxB"),pt=n("n6bG"),dt=n("CMyj"),mt=n("Y7HM"),ut=n("XoHu"),ht=n("QIAL"),gt=n("c2HN"),ft=n("z+Ro"),vt=n("KqfI"),bt=n("F97/"),xt=n("mCNh"),yt=n("SeVD"),Ct=n("ngJS"),_t=n("CRDf"),St=n("ZUHj"),wt=n("WyKG");let kt;function At(){it.e=void 0;try{return kt.apply(this,arguments)}catch(t){return it.e=t,it}finally{kt=void 0}}function It(t){return kt=t,At}},yu80:function(t,e,n){"use strict";n.d(e,"a",(function(){return m}));var s=n("mrSG"),i=n("xG9w"),o=n("XNiG"),a=n("2Vo4"),r=n("fXoL"),l=n("tk/3"),c=n("flaP"),p=n("XXEo"),d=n("LcQX");let m=(()=>{class t{constructor(t,e,n,s){this.$http=t,this._roles=e,this._auth=n,this._util=s,this.applicationName="AMS",this.currentUser=this._auth.getProfile().profile,this.activitySubject=new o.b,this.getActivityObservable=this.activitySubject.asObservable(),this.actualHoursSubject=new a.a({}),this.getActualHoursObservable=this.actualHoursSubject.asObservable(),this.taskRefreshSubject=new o.b,this.getTaskRefreshObservable=this.taskRefreshSubject.asObservable(),this.task_status_colors=[{statusName:"Open",statusColor:"#928F8D"},{statusName:"In Progress",statusColor:"#ff7200"},{statusName:"Completed",statusColor:"#4caf50"}],this.supportTeams=[],this.statusColorArray=[],this.priorityArray=[],this.getAmsAccessForUser(),this.getCurrentUserRole(),this.getSupportTeamData()}setActivityObservable(t){this.activitySubject.next(t)}setTaskRefreshObservable(t){this.taskRefreshSubject.next(t)}setActualHoursObservable(t){this.actualHoursSubject.next(t)}showMessage(t){this._util.showToastMessage(t)}showErrorMessage(t){this._util.showErrorMessage(t,"KEBS")}getToken(){return this._auth.getToken()}getCurrentUserRole(){return this._roles&&this._roles.roles&&this._roles.roles.length>0?this._roles.roles[0].role_id:null}getAmsAccessForUser(){let t=i.findWhere(this._roles.roles,{application_id:95,object_id:13});return!t||"Update"!=t.operation&&"*"!=t.operation}getStatusObjectEntries(){let t=i.where(this._roles.roles,{application_id:95,object_id:66});return t.length>0?JSON.parse(t[0].object_entries):null}getTicketCreationObjectEntries(){return Object(s.c)(this,void 0,void 0,(function*(){return i.where(this._roles.roles,{application_id:95,object_id:565})}))}getCreateAccess(){let t=i.where(this._roles.roles,{application_id:95,object_id:67});return t.length>0&&"*"==t[0].object_value}checkIfTicketIsEditable(t,e){if(this.supportTeams.length>0)for(let n of this.supportTeams){n.team_coordinator="string"==typeof n.team_coordinator?JSON.parse(n.team_coordinator):n.team_coordinator;for(let t of n.team_coordinator)if(t==this.currentUser.oid)return!0;n.team_head="string"==typeof n.team_head?JSON.parse(n.team_head):n.team_head;for(let t of n.team_head)if(t==this.currentUser.oid)return!0}return!i.contains(t.ticket_non_editable,e.status[0]._id)}getSupportTeamData(){return new Promise((t,e)=>{this.getSupportTeams().subscribe(n=>{"S"==n.messType&&n.data.length>0?(this.supportTeams=n.data,t(!0)):e("Not data found !")},t=>{e(t)})})}checkIfUserIsCustomer(){return!(!this._roles.roles||!this._roles.roles[0]||35!=this._roles.roles[0].role_id)}getCurrentCustomerDetails(){return this.$http.post("/api/ams/configuration/getCurrentCustomerDetails",{})}getStatusList(){return this.$http.post("/api/ams/configuration/getStatusList",{statusValues:this.getStatusObjectEntries()})}getTypeList(){return this.$http.post("/api/ams/configuration/getTypeList",{})}getAmsPostingDate(){return this.$http.post("/api/ams/configuration/getAmsPostingDate",{})}getAMSNewReleases(){return this.$http.post("/api/ams/configuration/getAMSNewReleases",{})}saveTicketToCollection(t){return this.$http.post("/api/ams/ticket/createTicket",{ticketDetails:t})}createTask(t,e){return this.$http.post("/api/ams/tasks/createTasks",{ticket_id:t,tasks:e})}createTaskFromTemplate(t,e,n,s,i){return this.$http.post("/api/ams/tasks/assignTaskFromTemplate",{task_template_id:t,ticket_id:e,estimated_closure_date:n,created_on:s,assigned_to:i})}addTask(t,e,n){return this.$http.post("/api/ams/tasks/addTasks",{task_id:t,ticket_id:e,tasks:n})}updateTaskObject(t,e,n,s){return this.$http.post("/api/ams/tasks/updateTaskObject",{task_id:t,sub_task_id:e,key:n,object:s})}editTaskStatus(t,e,n,s){return this.$http.post("/api/ams/tasks/editTaskStatus",{task_id:t,task_item:e,status:n,ticket_id:s})}editTaskDueDate(t,e,n,s){return this.$http.post("/api/ams/tasks/editTaskDueDate",{task_id:t,task_item:e,due_on:n,ticket_id:s})}editTaskPlannedHours(t,e,n,s){return this.$http.post("/api/ams/tasks/editTaskPlannedHours",{task_id:t,task_item:e,planned_hours:n,ticket_id:s})}editTaskName(t,e,n,s){return this.$http.post("/api/ams/tasks/editTaskName",{task_id:t,task_item:e,task_name:n,ticket_id:s})}editTaskAssigned(t,e,n,s){return this.$http.post("/api/ams/tasks/editTaskAssigned",{task_id:t,task_item:e,assigned_to:n,ticket_id:s})}addTaskActualHours(t,e,n,s,i){return this.$http.post("/api/ams/tasks/addTaskActualHours",{task_id:t,task_item:e,actual_hours:n,timesheet_id:s,ticket_id:i})}editTaskActualHours(t,e,n,s){return this.$http.post("/api/ams/tasks/editTaskActualHours",{task_id:t,task_item:e,actual_hours:n,ticket_id:s})}deleteTaskActualHours(t,e,n,s,i){return this.$http.post("/api/ams/tasks/deleteTaskActualHours",{task_id:t,task_item:e,actual_hours:n,ticket_id:s,timesheet_id:i})}getTicketMasterData(){return this.$http.post("/api/ams/configuration/getTicketMasterData",{})}getTicketProperties(){return this.$http.post("/api/ams/configuration/getTicketProperties",{})}getTicketById(t){return this.$http.post("/api/ams/ticket/getTicketById",{ticketId:t})}getTicketBasedOnSearch(t,e){return this.$http.post("/api/ams/ticket/getTicketBasedOnSearch",{searchParameter:t,ticketList:e})}getTicketMetaData(t){return this.$http.post("/api/ams/ticket/getTicketMetaData",{ticketId:t})}getTicketAttachment(t){return this.$http.post("/api/ams/ticket/getTicketAttachment",{ticketId:t})}getFileDataForDownload(t){return this.$http.post("/api/ams/configuration/getAMSAttachment",{key:t})}deleteTicketAttachment(t,e){return this.$http.post("/api/ams/configuration/deleteAMSAttachment",{ticketId:t,file:e})}updateTicketAttachment(t,e){return this.$http.post("/api/ams/configuration/updateAMSAttachment",{ticketId:t,file:e})}getTicketsForEmployee(t){return this.$http.post("/api/ams/ticket/getAllTicketsForEmployee",{employee_oid:t,org_codes:this._roles.getUserRoleOrgCodes("AMS"),role_id:this._roles.roles[0].role_id,is_read_only:this.getAmsAccessForUser()})}getConsultantBasedOnProjectModule(t,e){return this.$http.post("/api/ams/configuration/getConsultantBasedOnProjectModule",{project_item_id:t,module_id:e})}getActivity(t){return this.$http.post("/api/ams/activities/getActivity",{activityId:t})}addNoteActivity(t,e){return this.$http.post("/api/ams/activities/addNoteActivity",{activity_id:t,activities:e})}editActivity(t,e){return this.$http.post("/api/ams/activities/editActivity",{activity_id:t,activity_details:e})}getTodoDetails(t){return this.$http.post("/api/ams/todo/getTodo",{todoId:t})}getTicketCTAs(t){return this.$http.post("/api/ams/configuration/getTicketCTAs",{ticketId:t})}getTicketTasks(t){return this.$http.post("/api/ams/tasks/getTaskById",{task_id:t})}getTaskTemplate(){return this.$http.post("/api/ams/tasks/getTaskTemplates",{})}getConfigBasedOnProject(t){return this.$http.post("/api/ams/configuration/getConfigBasedOnProject",{itemId:t})}getModuleBasedOnSubmodule(t){return this.$http.post("/api/ams/configuration/getModuleBasedOnSubmodule",{subModuleId:t})}createTodo(t,e){return this.$http.post("/api/ams/todo/createToDo",{ticket_id:t,to_do_list:e})}insertTodo(t,e,n){return this.$http.post("/api/ams/todo/insertToDo",{ticket_id:t,to_do_id:e,to_do_list:n})}editTodo(t,e,n,s){return this.$http.post("/api/ams/todo/editToDo",{ticket_id:t,to_do_id:e,to_do_details:n,change_type:s})}getTrDetails(t){return this.$http.post("/api/ams/tr/getTr",{trId:t})}createTr(t,e){return this.$http.post("/api/ams/tr/createTr",{ticket_id:t,tr_list:e})}insertTr(t,e,n){return this.$http.post("/api/ams/tr/insertTr",{ticket_id:t,tr_id:e,tr_list:n})}editTr(t,e,n){return this.$http.post("/api/ams/tr/editTr",{ticket_id:t,tr_id:e,tr_details:n})}editTicketPriority(t,e,n){return this.$http.post("/api/ams/ticket/editTicketPriority",{ticket_id:t,prev_priority:e,priority:n})}editTicketStatus(t,e,n,s,i,o){return this.$http.post("/api/ams/ticket/editTicketStatus",{ticket_id:t,status_id:s,prev_status:e,status_name:i,status_ref_id:n,wf_plugin_data:o})}editTicketType(t,e,n,s){return this.$http.post("/api/ams/ticket/editTicketType",{ticket_id:t,type_id:n,prev_type:e,type_name:s})}editTicketConsultants(t,e,n,s,i,o){return this.$http.post("/api/ams/ticket/editTicketConsultants",{ticket_id:t,employee_oid:n,employee_name:s,level_name:i,emp_ref_id:o,prev_oid:e})}getFlaggedTickets(t){return this.$http.post("/api/ams/configuration/getFlaggedTickets",{associateOId:t})}updateFlaggedTickets(t,e){return this.$http.post("/api/ams/configuration/updateFlaggedTickets",{associateOId:t,flaggedTickets:e})}getTicketsFilter(t,e,n,s){return this.$http.post("/api/ams/reports/getTicketsFilter",{status:t,filters:e,ticketList:n,searchParameter:s})}getStatusCount(t){return this.$http.post("/api/ams/reports/getStatusCount",{ticketList:t})}downloadTickets(t){return this.$http.post("/api/ams/reports/downloadTickets",{ticketList:t})}getSLAByTicketId(t){return this.$http.post("/api/ams/ticket/getSLAByTicketId",{ticketId:t})}getLocationList(){return this.$http.post("/api/tsPrimary/getLocationList",{})}updateTaskLocation(t,e,n,s,i,o){return this.$http.post("/api/ams/tasks/updateTaskLocation",{task_id:t,taskItem:e,old_loc:n,new_loc:s,value:i,ticket_id:o})}getSupportTeams(){return this.$http.post("/api/ams/configuration/getSupportTeams",{})}getReportedByList(t){return this.$http.post("/api/ams/configuration/getReportedByList",{project_item_id:t})}editTicketDetails(t,e,n,s){return this.$http.post("/api/ams/ticket/editTicketDetails",{ticket_id:t,key:e,value:n,prev_value:s})}getTicketsFilterSR(t,e){return this.$http.post("/api/ams/reports/getTicketsFilter",{filterConfig:t,ticketList:e})}updateNewWfPluginId(t,e,n){return this.$http.post("/api/ams/ticket/updateNewWfPluginId",{wf_plugin_data:e,ticket_id:t,status_object:n})}getActiveLabel(){return this.$http.post("/api/ams/ticket/getActiveLabelData",{})}getActivePriority(){return new Promise((t,e)=>{0==this.priorityArray.length?this.$http.post("/api/ams/ticket/getActivePriorityData",{}).subscribe(e=>{this.priorityArray=e,t(this.priorityArray)}):t(this.priorityArray)})}getActivePriorityFromDB(){return Object(s.c)(this,void 0,void 0,(function*(){return this.$http.post("/api/ams/ticket/getActivePriorityData",{}).subscribe(t=>(this.priorityArray=t,this.priorityArray))}))}getStatusColor(){return new Promise((t,e)=>{0==this.statusColorArray.length?this.$http.post("/api/ams/ticket/getStatusColorData",{}).subscribe(e=>{this.statusColorArray=e,t(this.statusColorArray)}):t(this.statusColorArray)})}getStatusColorFromDB(){return Object(s.c)(this,void 0,void 0,(function*(){this.$http.post("/api/ams/ticket/getStatusColorData",{}).subscribe(t=>(this.statusColorArray=t,this.statusColorArray))}))}checkAdminRole(){return this.$http.post("/api/ams/reports/checkForAdmin",{object_id:530,application_id:95})}getSupportTeamsForAdmin(){return this.$http.post("/api/ams/reports/getSupportTeamsForAdmin",{})}updateSupportTeamForAdmin(t){return this.$http.post("/api/ams/reports/updateSupportTeamForAdmin",{formData:t})}deleteSupportTeamForAdmin(t){return this.$http.post("/api/ams/reports/deleteSupportTeamForAdmin",{team_id:t})}}return t.\u0275fac=function(e){return new(e||t)(r["\u0275\u0275inject"](l.c),r["\u0275\u0275inject"](c.a),r["\u0275\u0275inject"](p.a),r["\u0275\u0275inject"](d.a))},t.\u0275prov=r["\u0275\u0275defineInjectable"]({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()}}]);