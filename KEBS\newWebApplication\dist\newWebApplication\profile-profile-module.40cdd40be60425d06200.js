(window.webpackJsonp=window.webpackJsonp||[]).push([[852],{"/rGH":function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var i=n("fXoL");let o=(()=>{class e{transform(e,t,n="name"){return e||0===e?(Array.isArray(e)||(e=[e]),e.map(e=>{let i=t.find(t=>t.id==e);return i?i[n]:"-"}).join(", ")):"-"}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=i["\u0275\u0275definePipe"]({name:"masterData",type:e,pure:!0}),e})()},"Qlw+":function(e,t,n){"use strict";n.d(t,"a",(function(){return l}));var i=n("jhN1"),o=n("fXoL"),r=(n("4ivh"),n("PVOt")),a=n("6t9p");let l=(()=>{let e=class{};return e.\u0275mod=o["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=o["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[a.p,r.c,r.f,i.b],a.p,r.f]}),e})()},WYlB:function(e,t,n){"use strict";n.d(t,"a",(function(){return s})),n.d(t,"b",(function(){return c}));var i=n("jhN1"),o=n("fXoL"),r=n("B61+"),a=n("PVOt");const l=["*"];let s=(()=>{let e=class extends a.b{constructor(e,t,n,i,o,r,a){super(e,t,n,i,r,a),this._createEventEmitters([{subscribe:"click",emit:"onClick"},{subscribe:"contentReady",emit:"onContentReady"},{subscribe:"disposing",emit:"onDisposing"},{subscribe:"initialized",emit:"onInitialized"},{subscribe:"optionChanged",emit:"onOptionChanged"},{emit:"accessKeyChange"},{emit:"activeStateEnabledChange"},{emit:"disabledChange"},{emit:"elementAttrChange"},{emit:"focusStateEnabledChange"},{emit:"heightChange"},{emit:"hintChange"},{emit:"hoverStateEnabledChange"},{emit:"iconChange"},{emit:"rtlEnabledChange"},{emit:"stylingModeChange"},{emit:"tabIndexChange"},{emit:"templateChange"},{emit:"textChange"},{emit:"typeChange"},{emit:"useSubmitBehaviorChange"},{emit:"validationGroupChange"},{emit:"visibleChange"},{emit:"widthChange"}]),o.setHost(this)}get accessKey(){return this._getOption("accessKey")}set accessKey(e){this._setOption("accessKey",e)}get activeStateEnabled(){return this._getOption("activeStateEnabled")}set activeStateEnabled(e){this._setOption("activeStateEnabled",e)}get disabled(){return this._getOption("disabled")}set disabled(e){this._setOption("disabled",e)}get elementAttr(){return this._getOption("elementAttr")}set elementAttr(e){this._setOption("elementAttr",e)}get focusStateEnabled(){return this._getOption("focusStateEnabled")}set focusStateEnabled(e){this._setOption("focusStateEnabled",e)}get height(){return this._getOption("height")}set height(e){this._setOption("height",e)}get hint(){return this._getOption("hint")}set hint(e){this._setOption("hint",e)}get hoverStateEnabled(){return this._getOption("hoverStateEnabled")}set hoverStateEnabled(e){this._setOption("hoverStateEnabled",e)}get icon(){return this._getOption("icon")}set icon(e){this._setOption("icon",e)}get rtlEnabled(){return this._getOption("rtlEnabled")}set rtlEnabled(e){this._setOption("rtlEnabled",e)}get stylingMode(){return this._getOption("stylingMode")}set stylingMode(e){this._setOption("stylingMode",e)}get tabIndex(){return this._getOption("tabIndex")}set tabIndex(e){this._setOption("tabIndex",e)}get template(){return this._getOption("template")}set template(e){this._setOption("template",e)}get text(){return this._getOption("text")}set text(e){this._setOption("text",e)}get type(){return this._getOption("type")}set type(e){this._setOption("type",e)}get useSubmitBehavior(){return this._getOption("useSubmitBehavior")}set useSubmitBehavior(e){this._setOption("useSubmitBehavior",e)}get validationGroup(){return this._getOption("validationGroup")}set validationGroup(e){this._setOption("validationGroup",e)}get visible(){return this._getOption("visible")}set visible(e){this._setOption("visible",e)}get width(){return this._getOption("width")}set width(e){this._setOption("width",e)}_createInstance(e,t){return new r.a(e,t)}ngOnDestroy(){this._destroyWidget()}};return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](o.ElementRef),o["\u0275\u0275directiveInject"](o.NgZone),o["\u0275\u0275directiveInject"](a.e),o["\u0275\u0275directiveInject"](a.j),o["\u0275\u0275directiveInject"](a.i),o["\u0275\u0275directiveInject"](i.h),o["\u0275\u0275directiveInject"](o.PLATFORM_ID))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["dx-button"]],inputs:{accessKey:"accessKey",activeStateEnabled:"activeStateEnabled",disabled:"disabled",elementAttr:"elementAttr",focusStateEnabled:"focusStateEnabled",height:"height",hint:"hint",hoverStateEnabled:"hoverStateEnabled",icon:"icon",rtlEnabled:"rtlEnabled",stylingMode:"stylingMode",tabIndex:"tabIndex",template:"template",text:"text",type:"type",useSubmitBehavior:"useSubmitBehavior",validationGroup:"validationGroup",visible:"visible",width:"width"},outputs:{onClick:"onClick",onContentReady:"onContentReady",onDisposing:"onDisposing",onInitialized:"onInitialized",onOptionChanged:"onOptionChanged",accessKeyChange:"accessKeyChange",activeStateEnabledChange:"activeStateEnabledChange",disabledChange:"disabledChange",elementAttrChange:"elementAttrChange",focusStateEnabledChange:"focusStateEnabledChange",heightChange:"heightChange",hintChange:"hintChange",hoverStateEnabledChange:"hoverStateEnabledChange",iconChange:"iconChange",rtlEnabledChange:"rtlEnabledChange",stylingModeChange:"stylingModeChange",tabIndexChange:"tabIndexChange",templateChange:"templateChange",textChange:"textChange",typeChange:"typeChange",useSubmitBehaviorChange:"useSubmitBehaviorChange",validationGroupChange:"validationGroupChange",visibleChange:"visibleChange",widthChange:"widthChange"},features:[o["\u0275\u0275ProvidersFeature"]([a.e,a.j,a.i]),o["\u0275\u0275InheritDefinitionFeature"]],ngContentSelectors:l,decls:1,vars:0,template:function(e,t){1&e&&(o["\u0275\u0275projectionDef"](),o["\u0275\u0275projection"](0))},encapsulation:2}),e})(),c=(()=>{let e=class{};return e.\u0275mod=o["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=o["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[a.c,a.f,i.b],a.f]}),e})()},gMzk:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var i=n("jhN1"),o=n("fXoL"),r=(n("5xO4"),n("3Pt+"),n("PVOt"));let a=(()=>{let e=class{};return e.\u0275mod=o["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=o["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[r.c,r.f,i.b],r.f]}),e})()},j9Fs:function(e,t,n){"use strict";n.r(t),n.d(t,"ProfileModule",(function(){return qe}));var i=n("ofXK"),o=n("1+mW"),r=n("Xi0T"),a=n("tyNb"),l=n("mrSG"),s=n("yuIm"),c=n("fXoL"),g=n("IRv6"),d=n("wZkO"),p=n("f0Cb"),m=n("XNiG"),h=n("1G5W"),u=n("3Pt+"),f=n("+rOU"),C=n("4/q7"),v=n("rDax"),_=n("rQiX"),M=n("XNFG"),O=n("XQl4"),y=n("jhN1"),P=n("NFeN"),x=n("kmnG"),b=n("qFsG"),S=n("pF25"),w=n("6t9p"),E=n("TmG/"),k=n("/rGH"),I=n("pEYl");const F=["triggerDialogOverlayTemplateRef"],D=["dragDropFileInput"];function T(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"div",20),c["\u0275\u0275listener"]("click",(function(){c["\u0275\u0275restoreView"](e);const t=c["\u0275\u0275nextContext"](2);return t.openReuploadOverlay(t.triggerDialogOverlayField)})),c["\u0275\u0275text"](1," Change "),c["\u0275\u0275elementEnd"]()}}function L(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"div",21),c["\u0275\u0275elementStart"](1,"div",22),c["\u0275\u0275listener"]("click",(function(){return c["\u0275\u0275restoreView"](e),c["\u0275\u0275nextContext"](2).onClickCancel()})),c["\u0275\u0275text"](2," Cancel "),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](3,"div",23),c["\u0275\u0275listener"]("click",(function(){return c["\u0275\u0275restoreView"](e),c["\u0275\u0275nextContext"](2).onClickSave()})),c["\u0275\u0275text"](4," Save "),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()}}function A(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"div",24),c["\u0275\u0275namespaceSVG"](),c["\u0275\u0275elementStart"](1,"svg",25),c["\u0275\u0275listener"]("click",(function(){return c["\u0275\u0275restoreView"](e),c["\u0275\u0275nextContext"](2).onClickEdit()})),c["\u0275\u0275element"](2,"path",26),c["\u0275\u0275element"](3,"path",27),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()}}function j(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"div",28),c["\u0275\u0275elementStart"](1,"div",29),c["\u0275\u0275elementStart"](2,"mat-icon",30),c["\u0275\u0275listener"]("click",(function(){return c["\u0275\u0275restoreView"](e),c["\u0275\u0275nextContext"](2).closeOverlay()})),c["\u0275\u0275text"](3," close "),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](4,"div",31),c["\u0275\u0275listener"]("fileDropped",(function(t){return c["\u0275\u0275restoreView"](e),c["\u0275\u0275nextContext"](2).dragDropHandler(t)})),c["\u0275\u0275elementStart"](5,"input",32,33),c["\u0275\u0275listener"]("change",(function(t){return c["\u0275\u0275restoreView"](e),c["\u0275\u0275nextContext"](2).selectFromBrowseHandler(t.target.files)})),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](7,"div"),c["\u0275\u0275namespaceSVG"](),c["\u0275\u0275elementStart"](8,"svg",34),c["\u0275\u0275element"](9,"ellipse",35),c["\u0275\u0275element"](10,"ellipse",36),c["\u0275\u0275element"](11,"ellipse",37),c["\u0275\u0275element"](12,"ellipse",38),c["\u0275\u0275element"](13,"ellipse",39),c["\u0275\u0275element"](14,"path",40),c["\u0275\u0275element"](15,"path",41),c["\u0275\u0275element"](16,"path",42),c["\u0275\u0275element"](17,"path",43),c["\u0275\u0275element"](18,"path",44),c["\u0275\u0275element"](19,"path",45),c["\u0275\u0275element"](20,"path",46),c["\u0275\u0275element"](21,"path",44),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275namespaceHTML"](),c["\u0275\u0275elementStart"](22,"div",47),c["\u0275\u0275elementStart"](23,"span",48),c["\u0275\u0275text"](24,"Drag And Drop File Here Or"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](25,"span",49),c["\u0275\u0275text"](26,"Choose File"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](27,"div",50),c["\u0275\u0275elementStart"](28,"div",51),c["\u0275\u0275text"](29," Supported formats: image/jpeg, image/png, image/gif "),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](30,"div",51),c["\u0275\u0275text"](31,"Maximum Size: 100KB"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()}}function z(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"sup",63),c["\u0275\u0275text"](1,"*"),c["\u0275\u0275elementEnd"]())}function V(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div",61),c["\u0275\u0275text"](1),c["\u0275\u0275elementStart"](2,"span"),c["\u0275\u0275template"](3,z,2,0,"sup",62),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"](3).$implicit;c["\u0275\u0275advance"](1),c["\u0275\u0275textInterpolate1"](" ",e.label," "),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("ngIf",e.isMandatory)}}function H(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div"),c["\u0275\u0275elementStart"](1,"mat-form-field",64),c["\u0275\u0275element"](2,"input",65),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"](3).$implicit;c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("placeholder",e.placeholder)("formControlName",e.key)}}function R(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div",66),c["\u0275\u0275elementStart"](1,"div",67),c["\u0275\u0275element"](2,"app-country-code-input-search",68),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](3,"div",69),c["\u0275\u0275elementStart"](4,"mat-form-field",70),c["\u0275\u0275element"](5,"input",71),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"](3).$implicit,t=c["\u0275\u0275nextContext"](3);c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("list",t.phoneCodeList)("value",t.generalDetailValue.countryCode)("hideMatLabel",!0),c["\u0275\u0275advance"](3),c["\u0275\u0275property"]("formControlName",e.key)}}function B(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div"),c["\u0275\u0275element"](1,"textarea",72),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"](3).$implicit;c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("placeholder",e.placeholder)("formControlName",e.key)}}function N(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div"),c["\u0275\u0275elementStart"](1,"mat-form-field",64),c["\u0275\u0275element"](2,"input",73),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"](3).$implicit;c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("placeholder",e.placeholder)("formControlName",e.key)}}const G=function(){return["Arial","Courier New","Georgia","Impact","Lucida Console","Tahoma","Times New Roman","Verdana"]},$=function(){return{"aria-label":"Font family"}},U=function(e){return{inputAttr:e}},Z=function(){return[!1,1,2,3,4,5]},K=function(){return{"aria-label":"Header"}},Q=function(){return["8pt","10pt","12pt","14pt","18pt","24pt","36pt"]},q=function(){return{"aria-label":"Font size"}};function W(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div"),c["\u0275\u0275elementStart"](1,"div",74),c["\u0275\u0275elementStart"](2,"dx-html-editor",75),c["\u0275\u0275elementStart"](3,"dxo-toolbar",76),c["\u0275\u0275element"](4,"dxi-item",77),c["\u0275\u0275element"](5,"dxi-item",78),c["\u0275\u0275element"](6,"dxi-item",79),c["\u0275\u0275element"](7,"dxi-item",80),c["\u0275\u0275element"](8,"dxi-item",81),c["\u0275\u0275element"](9,"dxi-item",82),c["\u0275\u0275element"](10,"dxi-item",83),c["\u0275\u0275element"](11,"dxi-item",84),c["\u0275\u0275element"](12,"dxi-item",85),c["\u0275\u0275element"](13,"dxi-item",86),c["\u0275\u0275element"](14,"dxi-item",87),c["\u0275\u0275element"](15,"dxi-item",88),c["\u0275\u0275element"](16,"dxi-item",89),c["\u0275\u0275element"](17,"dxi-item",90),c["\u0275\u0275element"](18,"dxi-item",91),c["\u0275\u0275element"](19,"dxi-item",92),c["\u0275\u0275element"](20,"dxi-item",93),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"](3).$implicit,t=c["\u0275\u0275nextContext"](3);c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("formControlName",e.key)("value",t.signatureContentEditMode),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("multiline",t.isMultiline),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("acceptedValues",c["\u0275\u0275pureFunction0"](9,G))("options",c["\u0275\u0275pureFunction1"](11,U,c["\u0275\u0275pureFunction0"](10,$))),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("acceptedValues",c["\u0275\u0275pureFunction0"](13,Z))("options",c["\u0275\u0275pureFunction1"](15,U,c["\u0275\u0275pureFunction0"](14,K))),c["\u0275\u0275advance"](13),c["\u0275\u0275property"]("acceptedValues",c["\u0275\u0275pureFunction0"](17,Q))("options",c["\u0275\u0275pureFunction1"](19,U,c["\u0275\u0275pureFunction0"](18,q)))}}const J=function(){return[]};function X(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div"),c["\u0275\u0275element"](1,"app-input-search",94),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"](3).$implicit,t=c["\u0275\u0275nextContext"](3);c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("list","role"===e.key?t.roleListDetail:"timeZone"===e.key?t.timeZoneListDetail:c["\u0275\u0275pureFunction0"](6,J))("disableNone",!0)("placeholder",e.placeholder)("formControlName",e.key)("hideMatLabel",!0)("disabled",e.isDisabled)}}function Y(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div",58),c["\u0275\u0275template"](1,V,4,2,"div",59),c["\u0275\u0275template"](2,H,3,2,"div",1),c["\u0275\u0275template"](3,R,6,4,"div",60),c["\u0275\u0275template"](4,B,2,2,"div",1),c["\u0275\u0275template"](5,N,3,2,"div",1),c["\u0275\u0275template"](6,W,21,21,"div",1),c["\u0275\u0275template"](7,X,2,7,"div",1),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"](2).$implicit;c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","countryCode"!=e.key),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","text"===e.fieldType||"email"===e.fieldType||"url"===e.fieldType||"name"===e.fieldType),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","phonenumber"===e.fieldType),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","text-area"===e.fieldType),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","display"===e.fieldType),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","signature"===e.fieldType),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","single-select"==e.fieldType)}}function ee(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div",61),c["\u0275\u0275text"](1),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"](3).$implicit;c["\u0275\u0275advance"](1),c["\u0275\u0275textInterpolate1"](" ",e.label," ")}}function te(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div",99),c["\u0275\u0275text"](1),c["\u0275\u0275pipe"](2,"masterData"),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"](3).$implicit,t=c["\u0275\u0275nextContext"](3);c["\u0275\u0275advance"](1),c["\u0275\u0275textInterpolate1"](" ",c["\u0275\u0275pipeBind2"](2,1,t.generalDetailValue[e.key],t.timeZoneListDetail)," ")}}function ne(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div",99),c["\u0275\u0275text"](1),c["\u0275\u0275pipe"](2,"masterData"),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"](3).$implicit,t=c["\u0275\u0275nextContext"](3);c["\u0275\u0275advance"](1),c["\u0275\u0275textInterpolate1"](" ",c["\u0275\u0275pipeBind2"](2,1,t.generalDetailValue[e.key],t.roleListDetail)," ")}}function ie(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div",99),c["\u0275\u0275text"](1),c["\u0275\u0275pipe"](2,"masterData"),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"](3).$implicit,t=c["\u0275\u0275nextContext"](3);c["\u0275\u0275advance"](1),c["\u0275\u0275textInterpolate2"](" ",c["\u0275\u0275pipeBind2"](2,2,t.generalDetailValue.countryCode,t.phoneCodeList),"",t.generalDetailValue[e.key]?"-"+t.generalDetailValue[e.key]:""," ")}}function oe(e,t){if(1&e&&c["\u0275\u0275element"](0,"div",100),2&e){const e=c["\u0275\u0275nextContext"](6);c["\u0275\u0275property"]("innerHTML",e.signatureContent,c["\u0275\u0275sanitizeHtml"])}}function re(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div",99),c["\u0275\u0275text"](1),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"](3).$implicit,t=c["\u0275\u0275nextContext"](3);c["\u0275\u0275advance"](1),c["\u0275\u0275textInterpolate1"](" ",t.generalDetailValue[e.key]||"-"," ")}}function ae(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div",95),c["\u0275\u0275elementStart"](1,"div",96),c["\u0275\u0275template"](2,ee,2,1,"div",59),c["\u0275\u0275template"](3,te,3,4,"div",97),c["\u0275\u0275template"](4,ne,3,4,"div",97),c["\u0275\u0275template"](5,ie,3,5,"div",97),c["\u0275\u0275template"](6,oe,1,1,"div",98),c["\u0275\u0275template"](7,re,2,1,"div",97),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"](2).$implicit;c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("ngIf","countryCode"!=e.key),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","timeZone"==e.key),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","role"==e.key),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","phone_number"==e.key),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","signature_content"==e.key),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",!("timeZone"==e.key||"phone_number"==e.key||"countryCode"==e.key||"signature_content"==e.key||"role"==e.key))}}function le(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div"),c["\u0275\u0275template"](1,Y,8,7,"div",56),c["\u0275\u0275template"](2,ae,8,6,"div",57),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]().$implicit,t=c["\u0275\u0275nextContext"](3);c["\u0275\u0275classMapInterpolate1"]("col-",e.col," over-all-filed "),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",t.isEditMode),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",!t.isEditMode)}}function se(e,t){if(1&e&&(c["\u0275\u0275elementContainerStart"](0),c["\u0275\u0275template"](1,le,3,5,"div",55),c["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","countryCode"!=e.key)}}function ce(e,t){if(1&e&&(c["\u0275\u0275elementContainerStart"](0),c["\u0275\u0275elementStart"](1,"div",52),c["\u0275\u0275text"](2),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](3,"div",53),c["\u0275\u0275elementStart"](4,"div",54),c["\u0275\u0275template"](5,se,2,1,"ng-container",19),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;c["\u0275\u0275advance"](2),c["\u0275\u0275textInterpolate"](e.label),c["\u0275\u0275advance"](3),c["\u0275\u0275property"]("ngForOf",e.formSectionFields)}}const ge=function(e,t,n){return[e,t,n,0,"E"]};function de(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div",2),c["\u0275\u0275elementStart"](1,"div",3),c["\u0275\u0275elementStart"](2,"div",4),c["\u0275\u0275elementStart"](3,"div",5),c["\u0275\u0275elementStart"](4,"div",6),c["\u0275\u0275elementStart"](5,"div",7),c["\u0275\u0275element"](6,"img",8),c["\u0275\u0275elementEnd"](),c["\u0275\u0275template"](7,T,2,0,"div",9),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](8,"div"),c["\u0275\u0275elementStart"](9,"div"),c["\u0275\u0275elementStart"](10,"div",10),c["\u0275\u0275text"](11),c["\u0275\u0275pipe"](12,"masterData"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](13,"div",11),c["\u0275\u0275text"](14),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](15,"div",12),c["\u0275\u0275text"](16),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](17,"div",13),c["\u0275\u0275template"](18,L,5,0,"div",14),c["\u0275\u0275template"](19,A,4,0,"div",15),c["\u0275\u0275pipe"](20,"access"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275template"](21,j,32,0,"ng-template",16,17,c["\u0275\u0275templateRefExtractor"]),c["\u0275\u0275elementStart"](23,"form",18),c["\u0275\u0275template"](24,ce,6,2,"ng-container",19),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275advance"](6),c["\u0275\u0275property"]("src",e.image,c["\u0275\u0275sanitizeUrl"]),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",e.isEditMode),c["\u0275\u0275advance"](1),c["\u0275\u0275classProp"]("column-two-view",!e.isEditMode)("column-two-edit",e.isEditMode),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("ngStyle",e.getStatusStyle(e.selectedStatus)),c["\u0275\u0275advance"](1),c["\u0275\u0275textInterpolate1"](" ",c["\u0275\u0275pipeBind2"](12,15,e.selectedStatus,e.statusListDetail)," "),c["\u0275\u0275advance"](3),c["\u0275\u0275textInterpolate1"](" ",(e.generalDetailValue.first_name?e.generalDetailValue.first_name+" ":"")+(e.generalDetailValue.middle_name?e.generalDetailValue.middle_name+" ":"")+(e.generalDetailValue.last_name?e.generalDetailValue.last_name:"")," "),c["\u0275\u0275advance"](2),c["\u0275\u0275textInterpolate1"](" ",e.generalDetailValue.email," "),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("ngIf",e.isEditMode),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",!e.isEditMode&&c["\u0275\u0275pipeBindV"](20,18,c["\u0275\u0275pureFunction3"](24,ge,e.access.moduleId.settings,e.access.subModuleId.profileSettings,e.access.sectionId.generalDetailView))),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("cdkConnectedOverlayOrigin",e.triggerDialogOverlay),c["\u0275\u0275advance"](2),c["\u0275\u0275property"]("formGroup",e.generalDetailForm),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngForOf",e.formSection)}}function pe(e,t){if(1&e&&(c["\u0275\u0275elementContainerStart"](0),c["\u0275\u0275elementStart"](1,"div",101),c["\u0275\u0275elementStart"](2,"div",102),c["\u0275\u0275element"](3,"img",103),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](4,"div",104),c["\u0275\u0275elementStart"](5,"div",105),c["\u0275\u0275text"](6,"Loading..."),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementContainerEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275advance"](3),c["\u0275\u0275property"]("src",e.uiTextConfig["UI-LOADING-001"],c["\u0275\u0275sanitizeUrl"])}}let me=(()=>{class e{constructor(e,t,n,i,o,r,a){this._overlay=e,this._viewContainerRef=t,this._atsMasterService=n,this._toaster=i,this._fb=o,this._settingService=r,this.sanitizer=a,this._onDestroy=new m.b,this.isEditMode=!1,this.formSection=[],this.image="assets/images/User.png",this.generalDetailValue={},this.selectedStatus=1,this.isLoading=!0,this.spinnerText="Loading...",this.uiTextConfig={},this.isMultiline=!1,this.timeZoneListDetail=[],this.phoneCodeList=[],this.access=s}ngOnInit(){return Object(l.c)(this,void 0,void 0,(function*(){this.isLoading=!0,this.placeHolderHtmlContent='\n    <p style="margin: 0;">[Your Full Name]</p>\n    <p style="margin: 0;">[Your Email]</p>\n    <p style="margin: 0;">[Your Job Title]</p>\n    <p style="margin: 0;">[Your Company Name]</p>\n    <p style="margin: 0;">[Your Company Website Link]</p>\n  ',this.signatureContent=this.sanitizer.bypassSecurityTrustHtml(this.placeHolderHtmlContent),yield this.getAtsMasterUiConfig("collegesettingconfig"),this.generalDetailForm=this._fb.group({}),yield this.getAtsFormsConfig("generalDetailProfileSettings"),yield this.getProfileById(),yield this.getProfileStatusList(),yield this.getTimezone(),yield this.getPhoneCode(),yield this.getAllRole(),yield this.createForm(),this.isLoading=!1}))}createForm(){this.formSection.forEach(e=>{e.formSectionFields.forEach(e=>{const t=[e.isMandatory?u.H.required:null,"name"===e.fieldType?u.H.pattern(/^[a-zA-Z ]+$/):null,"url"===e.fieldType?u.H.pattern(/^(https?:\/\/)?(www\.)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(:[0-9]+)?(\/\S*)?$/):null,"phonenumber"===e.fieldType?u.H.pattern(/^\d{10}$/):null,"email"===e.fieldType?u.H.pattern(/^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/):null].filter(e=>null!==e);this.generalDetailForm.addControl(e.key,this._fb.control(this.generalDetailValue[e.key],t))})})}openReuploadOverlay(e){var t;if(!(null===(t=this.overlayRef)||void 0===t?void 0:t.hasAttached())){const e=this._overlay.position().global().centerHorizontally().centerVertically(),t=this._overlay.scrollStrategies.close();this.overlayRef=this._overlay.create({positionStrategy:e,scrollStrategy:t,hasBackdrop:!0,panelClass:["pop-up"]});const n=new f.h(this.triggerDialogOverlayTemplateRef,this._viewContainerRef);this.overlayRef.attach(n),this.overlayRef.backdropClick().subscribe(()=>{this.closeOverlay()})}}onClickSave(){return Object(l.c)(this,void 0,void 0,(function*(){if(this.generalDetailForm.valid){this.isLoading=!0;const e=this.bodyEditor.instance.option("value"),t=this.modifyHtmlContent(e);this.signatureContent=this.sanitizer.bypassSecurityTrustHtml(e?t:this.placeHolderHtmlContent),this.generalDetailValue=this.generalDetailForm.value,yield this.updateProfileDetail(this.generalDetailValue,this.image,this.selectedStatus),this.isEditMode=!1,this.isLoading=!1}else{const e=[];Object.keys(this.generalDetailForm.controls).forEach(t=>{const n=this.generalDetailForm.get(t);if(n.errors){const i=this.findLabelByKey(t,this.formSection[0].formSectionFields);n.errors.required?e.push(i+" is Mandatory"):n.errors.pattern&&e.push("Invalid "+i)}}),e.reverse(),e.forEach(e=>{this._toaster.showError("Warning",e,7e3)})}}))}findLabelByKey(e,t){const n=t.find(t=>t.key===e);return n?n.label:void 0}modifyHtmlContent(e){const t=(new DOMParser).parseFromString(e,"text/html");return t.body.querySelectorAll("p").forEach(e=>{e.style.margin="0",e.style.padding="0"}),t.documentElement.outerHTML}onClickCancel(){this.isEditMode=!1,this.selectedStatus=this.currentStatus,this.image=this.currenImage}onClickEdit(){this.isLoading=!0,this.currentStatus=this.selectedStatus,this.currenImage=this.image,this.generalDetailForm=this._fb.group({}),this.createForm(),this.signatureContent&&this.placeHolderHtmlContent!=this.signatureContent&&(this.signatureContentEditMode=this.sanitizer.sanitize(c.SecurityContext.HTML,this.signatureContent)),this.isEditMode=!0,this.isLoading=!1}closeOverlay(){var e;null===(e=this.overlayRef)||void 0===e||e.dispose()}dragDropHandler(e){return Object(l.c)(this,void 0,void 0,(function*(){yield this.prepareTemplateFileItem(e)}))}selectFromBrowseHandler(e){return Object(l.c)(this,void 0,void 0,(function*(){yield this.prepareTemplateFileItem(e)}))}prepareTemplateFileItem(e){var t;return Object(l.c)(this,void 0,void 0,(function*(){if(null!=this.showFileSizeInBytes(e[0].size)){const n=new FormData;n.append("file",e[0]),yield this.updateImage(n),null===(t=this.overlayRef)||void 0===t||t.dispose()}else this._toaster.showWarning("Warning \u26a0\ufe0f","File size is greater than 100KB",7e3)}))}showFileSizeInBytes(e,t=2){if(e>1e5)return null;if(0===e)return"0 Bytes";const n=t<=0?0:t,i=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,i)).toFixed(n))+" "+["Bytes","KB","MB","GB","TB","PB","EB","ZB","YB"][i]}getStatusStyle(e){if(this.statusListDetail.length>0){const t=this.statusListDetail.find(t=>t.id===e);return{color:t?t.statusColor:"","background-color":t?t.statusBgColor:""}}return{color:"","background-color":""}}getAtsFormsConfig(e){return Object(l.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._atsMasterService.getAtsFormsConfig(e).pipe(Object(h.a)(this._onDestroy)).subscribe({next:n=>{0==n.err?"generalDetailProfileSettings"==e&&(this.formSection=n.data.formSection):this._toaster.showError("Error",n.msg,7e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Forms Configuration Retrieval Failed!",7e3),n()}}))}))}updateImage(e){return Object(l.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._settingService.updateImage(e).pipe(Object(h.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?(this.image=e.data.fileKey,this._toaster.showSuccess("Success",e.msg,7e3)):this._toaster.showError("Error",e.msg,7e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Update Image Failed!",7e3),n()}}))}))}getAtsMasterUiConfig(e){return Object(l.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._atsMasterService.getAtsMasterUiConfig(e).pipe(Object(h.a)(this._onDestroy)).subscribe({next:n=>{0==n.err?"collegesettingconfig"==e&&(this.uiTextConfig=n.data.collegeConfigurations.uiTextConfig):this._toaster.showError("Error",n.msg,7e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS UI Configuration Retrieval Failed!",7e3),n()}}))}))}getTimezone(){return Object(l.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getTimezone().pipe(Object(h.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.timeZoneListDetail=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Time Zone Retrieval Failed!",7e3),t()}}))}))}getProfileStatusList(){return Object(l.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getProfileStatusList().pipe(Object(h.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.statusListDetail=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Profile Status Retrival Failed!",7e3),t()}}))}))}getProfileById(){return Object(l.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._settingService.getProfileById().pipe(Object(h.a)(this._onDestroy)).subscribe({next:t=>{if(0==t.err)if(t.data){this.generalDetailValue=t.data.profileDetailValue,this.status=t.data.status;const n=this.modifyHtmlContent(t.data.profileDetailValue.signature_content?t.data.profileDetailValue.signature_content:"");this.signatureContent=this.sanitizer.bypassSecurityTrustHtml(t.data.profileDetailValue.signature_content?n:this.placeHolderHtmlContent),this.selectedStatus=this.status.status_id,this.image=null!=t.data.image?t.data.image:this.image,e(!0)}else e(!1);else this._toaster.showError("Error",t.msg,7e3),e(!1)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Profile Detail by ID Retrieval Failed!",7e3),t()}}))}))}updateProfileDetail(e,t,n){return Object(l.c)(this,void 0,void 0,(function*(){return new Promise((i,o)=>this._settingService.updateProfileDetail(e,t,n).pipe(Object(h.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?this._toaster.showSuccess("Success",e.msg,7e3):this._toaster.showError("Error",e.msg,7e3),i(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Update Profile Detail Failed!",7e3),o()}}))}))}getPhoneCode(){return Object(l.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getPhoneCode().pipe(Object(h.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.phoneCodeList=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Phone Code Retrival Failed!",7e3),t()}}))}))}getAllRole(){return Object(l.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getAllRole().pipe(Object(h.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.roleListDetail=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Role Master Data Retrieval Failed!",7e3),t()}}))}))}}return e.\u0275fac=function(t){return new(t||e)(c["\u0275\u0275directiveInject"](v.e),c["\u0275\u0275directiveInject"](c.ViewContainerRef),c["\u0275\u0275directiveInject"](_.a),c["\u0275\u0275directiveInject"](M.a),c["\u0275\u0275directiveInject"](u.i),c["\u0275\u0275directiveInject"](O.a),c["\u0275\u0275directiveInject"](y.c))},e.\u0275cmp=c["\u0275\u0275defineComponent"]({type:e,selectors:[["app-general-detail"]],viewQuery:function(e,t){if(1&e&&(c["\u0275\u0275viewQuery"](F,!0),c["\u0275\u0275viewQuery"](D,!0),c["\u0275\u0275viewQuery"](C.a,!0)),2&e){let e;c["\u0275\u0275queryRefresh"](e=c["\u0275\u0275loadQuery"]())&&(t.triggerDialogOverlayTemplateRef=e.first),c["\u0275\u0275queryRefresh"](e=c["\u0275\u0275loadQuery"]())&&(t.dragDropFileInput=e.first),c["\u0275\u0275queryRefresh"](e=c["\u0275\u0275loadQuery"]())&&(t.bodyEditor=e.first)}},decls:2,vars:2,consts:[["class","general-detail",4,"ngIf"],[4,"ngIf"],[1,"general-detail"],[1,"profile-container"],[1,"cont-profile"],[1,"d-flex","columns"],[1,"column-one"],[1,"general-profile-photo"],[1,"profile-image",3,"src"],["class","change-profile",3,"click",4,"ngIf"],[1,"general-profile-status-view",3,"ngStyle"],[1,"general-profile-name"],[1,"general-profile-email"],[1,"container-two"],["class","buttons",4,"ngIf"],["class","edit-button",4,"ngIf"],["cdkConnectedOverlay","",3,"cdkConnectedOverlayOrigin"],["triggerDialogOverlayTemplateRef",""],[3,"formGroup"],[4,"ngFor","ngForOf"],[1,"change-profile",3,"click"],[1,"buttons"],["type","button",1,"cancel-button",3,"click"],["type","submit",1,"save-button",3,"click"],[1,"edit-button"],["width","24","height","24","viewBox","0 0 24 24","fill","none",1,"edit-svg",3,"click"],["d","M11.187 4.91345L4.25979 12.2456C3.99823 12.5241 3.7451 13.0725 3.69448 13.4522L3.38229 16.1859C3.2726 17.1731 3.98135 17.8481 4.9601 17.6794L7.67698 17.2153C8.05667 17.1478 8.58823 16.8694 8.84979 16.5825L15.777 9.25032C16.9751 7.9847 17.5151 6.54188 15.6504 4.77845C13.7942 3.03188 12.3851 3.64782 11.187 4.91345Z","stroke","#8B95A5","stroke-miterlimit","10","stroke-linecap","round","stroke-linejoin","round"],["d","M10.0312 6.13672C10.3941 8.46547 12.2841 10.2458 14.6297 10.482","stroke","#8B95A5","stroke-miterlimit","10","stroke-linecap","round","stroke-linejoin","round"],[1,"reupload-overlay"],[1,"close-btn"],[1,"material-symbols-outlined","close-icon",3,"click"],["appDnd","",1,"d-flex","flex-column","align-items-center","justify-content-center","drop-file",3,"fileDropped"],["type","file","accept","image/*","id","dragDropFileInput",3,"change"],["dragDropFileInput",""],["width","161","height","50","viewBox","0 0 161 122","fill","none"],["cx","25.4433","cy","23.2989","rx","3.91991","ry","3.91996","fill","#EFEFEF"],["cx","126.829","cy","7.79756","rx","7.30529","ry","7.30538","fill","#EFEFEF"],["cx","26.695","cy","105.797","rx","6.94894","ry","6.94902","fill","#EFEFEF"],["cx","151.062","cy","99.0262","rx","6.94894","ry","6.94902","fill","#EFEFEF"],["cx","137.514","cy","106.866","rx","3.38538","ry","3.38542","fill","#EFEFEF"],["d","M94.0522 5.67206C93.8456 5.62812 93.8456 5.33326 94.0522 5.28932L97.1759 4.62505C97.2512 4.60904 97.3101 4.55037 97.3265 4.47514L97.999 1.37342C98.0436 1.16784 98.3369 1.16784 98.3814 1.37342L99.054 4.47514C99.0703 4.55037 99.1292 4.60904 99.2045 4.62505L102.328 5.28932C102.535 5.33326 102.535 5.62812 102.328 5.67206L99.2045 6.33633C99.1292 6.35234 99.0703 6.41101 99.054 6.48623L98.3814 9.58795C98.3369 9.79353 98.0436 9.79353 97.999 9.58795L97.3265 6.48623C97.3101 6.41101 97.2512 6.35234 97.1759 6.33633L94.0522 5.67206Z","fill","#1B2140"],["d","M9.13197 74.8365C9.21171 74.4598 9.74949 74.4598 9.82924 74.8365L11.0205 80.4644C11.0495 80.6015 11.1563 80.7089 11.2932 80.7388L16.8629 81.9521C17.2369 82.0336 17.2369 82.567 16.8629 82.6485L11.2932 83.8619C11.1563 83.8917 11.0495 83.9991 11.0205 84.1363L9.82924 89.7641C9.74949 90.1408 9.21171 90.1408 9.13197 89.7641L7.94074 84.1363C7.91171 83.9991 7.80495 83.8917 7.66796 83.8619L2.09829 82.6485C1.72429 82.567 1.72429 82.0336 2.09829 81.9521L7.66796 80.7388C7.80495 80.7089 7.91171 80.6015 7.94074 80.4644L9.13197 74.8365Z","fill","#1B2140"],["d","M139.953 24.9459C140.033 24.5692 140.571 24.5692 140.651 24.9459L141.346 28.2291C141.375 28.3662 141.481 28.4736 141.618 28.5035L144.877 29.2133C145.251 29.2948 145.251 29.8282 144.877 29.9097L141.618 30.6196C141.481 30.6494 141.375 30.7568 141.346 30.894L140.651 34.1771C140.571 34.5539 140.033 34.5539 139.953 34.1771L139.258 30.894C139.229 30.7568 139.123 30.6494 138.986 30.6196L135.727 29.9097C135.353 29.8282 135.353 29.2948 135.727 29.2133L138.986 28.5035C139.123 28.4736 139.229 28.3662 139.258 28.2291L139.953 24.9459Z","fill","#1B2140"],["d","M154.325 77.584L155.417 82.7396L160.499 83.8467L155.417 84.9538L154.325 90.1094L153.234 84.9538L148.152 83.8467L153.234 82.7396L154.325 77.584Z","fill","white"],["d","M101.335 88.8411L85.0013 72.5078L68.668 88.8411","stroke","black","stroke-opacity","0.4","stroke-width","2","stroke-linecap","round","stroke-linejoin","round"],["d","M85 72.5078V109.258","stroke","black","stroke-opacity","0.4","stroke-width","2","stroke-linecap","round","stroke-linejoin","round"],["d","M119.26 98.6C123.243 96.4288 126.389 92.9931 128.202 88.8353C130.015 84.6774 130.392 80.0341 129.273 75.6382C128.154 71.2423 125.604 67.3442 122.023 64.5591C118.443 61.774 114.037 60.2605 109.501 60.2575H104.356C103.12 55.4769 100.816 51.0387 97.6182 47.2766C94.4201 43.5145 90.4107 40.5263 85.8915 38.5367C81.3723 36.5471 76.4608 35.6079 71.5264 35.7897C66.592 35.9715 61.763 37.2696 57.4024 39.5863C53.0419 41.9031 49.2632 45.1782 46.3506 49.1655C43.438 53.1528 41.4673 57.7485 40.5864 62.6071C39.7056 67.4657 39.9377 72.4607 41.2652 77.2167C42.5927 81.9727 44.9812 86.3658 48.2509 90.0659","stroke","black","stroke-opacity","0.4","stroke-width","2","stroke-linecap","round","stroke-linejoin","round"],[1,"d-flex","align-items-center",2,"gap","4px"],[1,"text-1"],["for","dragDropFileInput",1,"text-2"],[1,"d-flex","align-items-center","justify-content-between","mini-content"],[1,"text"],[1,"section-label"],[1,"generaldetail-container"],[1,"row"],[3,"class",4,"ngIf"],["class","d-flex flex-column label-input-value",4,"ngIf"],["class","view",4,"ngIf"],[1,"d-flex","flex-column","label-input-value"],["class","form-label",4,"ngIf"],["class","phone-number",4,"ngIf"],[1,"form-label"],["class","required-field",4,"ngIf"],[1,"required-field"],["appearance","outline","color","warn",1,"mat-form-field"],["matInput","",3,"placeholder","formControlName"],[1,"phone-number"],[1,"country-code"],["placeholder","Eg:+91","formControlName","countryCode",3,"list","value","hideMatLabel"],[1,"number"],["appearance","outline"],["matInput","","digitOnly","","placeholder","00000 00000",3,"formControlName"],[3,"placeholder","formControlName"],["matInput","","readonly","",3,"placeholder","formControlName"],[1,"signature-content-edit-mode"],["height","250px",3,"formControlName","value"],[3,"multiline"],["name","font",3,"acceptedValues","options"],["name","header",3,"acceptedValues","options"],["name","bold"],["name","italic"],["name","strike"],["name","underline"],["name","color"],["name","background"],["name","orderedList"],["name","bulletList"],["name","alignLeft"],["name","alignRight"],["name","link"],["name","image"],["name","size",3,"acceptedValues","options"],["name","alignCenter"],["name","alignJustify"],[1,"generalDetailDropDown",3,"list","disableNone","placeholder","formControlName","hideMatLabel","disabled"],[1,"view"],[1,"label-value-container"],["class","view-value",4,"ngIf"],["class","signature-content",3,"innerHTML",4,"ngIf"],[1,"view-value"],[1,"signature-content",3,"innerHTML"],[1,"loading-img"],[2,"max-width","60px","max-height","60px"],[2,"max-width","100%","max-height","100%",3,"src"],[1,"loading-wrapper"],[1,"loading"]],template:function(e,t){1&e&&(c["\u0275\u0275template"](0,de,25,28,"div",0),c["\u0275\u0275template"](1,pe,7,1,"ng-container",1)),2&e&&(c["\u0275\u0275property"]("ngIf",!t.isLoading),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",t.isLoading))},directives:[i.NgIf,i.NgStyle,v.a,u.J,u.w,u.n,i.NgForOf,P.a,x.c,b.b,u.e,u.v,u.l,S.a,C.a,w.Ge,w.o,E.a],pipes:[k.a,I.a],styles:['.profile-container[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);padding:20px 20px 0;position:relative;display:flex;text-wrap:nowrap}.profile-container[_ngcontent-%COMP%]   .cont-profile[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;width:100%}.profile-container[_ngcontent-%COMP%]   .cont-profile[_ngcontent-%COMP%]   .columns[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);padding:0 10px 10px 0}.profile-container[_ngcontent-%COMP%]   .cont-profile[_ngcontent-%COMP%]   .columns[_ngcontent-%COMP%]   .column-one[_ngcontent-%COMP%]{display:grid;gap:3px}.profile-container[_ngcontent-%COMP%]   .cont-profile[_ngcontent-%COMP%]   .columns[_ngcontent-%COMP%]   .column-one[_ngcontent-%COMP%]   .profile-image[_ngcontent-%COMP%]{height:60px;width:60px;border-radius:50%}.profile-container[_ngcontent-%COMP%]   .cont-profile[_ngcontent-%COMP%]   .columns[_ngcontent-%COMP%]   .column-one[_ngcontent-%COMP%]   .change-profile[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);align-items:center;display:flex;cursor:pointer;color:#1890ff;justify-content:center;font-size:11px;font-weight:400;letter-spacing:.02em}.profile-container[_ngcontent-%COMP%]   .cont-profile[_ngcontent-%COMP%]   .columns[_ngcontent-%COMP%]   .column-two-view[_ngcontent-%COMP%]{margin-left:20px;display:flex;flex-direction:column;justify-content:center;gap:5px}.profile-container[_ngcontent-%COMP%]   .cont-profile[_ngcontent-%COMP%]   .columns[_ngcontent-%COMP%]   .column-two-view[_ngcontent-%COMP%]   .general-profile-name[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);color:#111434;font-size:16px;font-weight:700;line-height:20.83px;text-align:left;text-overflow:ellipsis;overflow:hidden}.profile-container[_ngcontent-%COMP%]   .cont-profile[_ngcontent-%COMP%]   .columns[_ngcontent-%COMP%]   .column-two-view[_ngcontent-%COMP%]   .general-profile-email[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:400;line-height:18.23px;color:#7d838b;text-overflow:ellipsis;overflow:hidden}.profile-container[_ngcontent-%COMP%]   .cont-profile[_ngcontent-%COMP%]   .columns[_ngcontent-%COMP%]   .column-two-view[_ngcontent-%COMP%]   .general-profile-status[_ngcontent-%COMP%]{background-color:#eef9e8;color:#52c41a!important;width:80px}.profile-container[_ngcontent-%COMP%]   .cont-profile[_ngcontent-%COMP%]   .columns[_ngcontent-%COMP%]   .column-two-view[_ngcontent-%COMP%]   .general-profile-status[_ngcontent-%COMP%], .profile-container[_ngcontent-%COMP%]   .cont-profile[_ngcontent-%COMP%]   .columns[_ngcontent-%COMP%]   .column-two-view[_ngcontent-%COMP%]   .general-profile-status-view[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:12px;font-weight:500;line-height:16.8px;letter-spacing:.02em;text-align:left;height:21px;padding:2px 6px;gap:10px;border-radius:4px}.profile-container[_ngcontent-%COMP%]   .cont-profile[_ngcontent-%COMP%]   .columns[_ngcontent-%COMP%]   .column-two-view[_ngcontent-%COMP%]   .general-profile-status-view[_ngcontent-%COMP%]{width:-moz-fit-content;width:fit-content}.profile-container[_ngcontent-%COMP%]   .cont-profile[_ngcontent-%COMP%]   .columns[_ngcontent-%COMP%]   .column-two-edit[_ngcontent-%COMP%]{margin-left:20px;display:flex;flex-direction:column;gap:5px}.profile-container[_ngcontent-%COMP%]   .cont-profile[_ngcontent-%COMP%]   .columns[_ngcontent-%COMP%]   .column-two-edit[_ngcontent-%COMP%]   .general-profile-name[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);color:#111434;font-size:16px;font-weight:700;line-height:20.83px;text-align:left;text-overflow:ellipsis;overflow:hidden}.profile-container[_ngcontent-%COMP%]   .cont-profile[_ngcontent-%COMP%]   .columns[_ngcontent-%COMP%]   .column-two-edit[_ngcontent-%COMP%]   .general-profile-email[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:400;line-height:18.23px;color:#7d838b;text-overflow:ellipsis;overflow:hidden}.profile-container[_ngcontent-%COMP%]   .cont-profile[_ngcontent-%COMP%]   .columns[_ngcontent-%COMP%]   .column-two-edit[_ngcontent-%COMP%]   .general-profile-status-view[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:12px;font-weight:500;line-height:16.8px;letter-spacing:.02em;text-align:left;width:-moz-fit-content;width:fit-content;height:21px;padding:2px 6px;gap:10px;border-radius:4px}.profile-container[_ngcontent-%COMP%]   .cont-profile[_ngcontent-%COMP%]   .buttons[_ngcontent-%COMP%]{display:flex;gap:20px;position:relative;align-items:center;justify-content:center}.profile-container[_ngcontent-%COMP%]   .cont-profile[_ngcontent-%COMP%]   .buttons[_ngcontent-%COMP%]   .cancel-button[_ngcontent-%COMP%]{text-align:left;color:#45546e;border-radius:5px;border:1px solid #45546e}.profile-container[_ngcontent-%COMP%]   .cont-profile[_ngcontent-%COMP%]   .buttons[_ngcontent-%COMP%]   .cancel-button[_ngcontent-%COMP%], .profile-container[_ngcontent-%COMP%]   .cont-profile[_ngcontent-%COMP%]   .buttons[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:700;line-height:16px;letter-spacing:-.02em;height:40px;padding:10px;justify-content:center;align-items:center;display:flex;width:64px;cursor:pointer}.profile-container[_ngcontent-%COMP%]   .cont-profile[_ngcontent-%COMP%]   .buttons[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%]{border-radius:5px;color:#fff;background:linear-gradient(270deg,var(--atsprimaryColor),var(--atssecondaryColor) 105.29%)}.profile-container[_ngcontent-%COMP%]   .cont-profile[_ngcontent-%COMP%]   .edit-svg[_ngcontent-%COMP%]{cursor:pointer}.section-label[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:700;line-height:18.23px;color:var(--atsprimaryColor);padding-left:29px;padding-top:15px}.generaldetail-container[_ngcontent-%COMP%]{padding:0 20px 20px}.generaldetail-container[_ngcontent-%COMP%]   .over-all-filed[_ngcontent-%COMP%]{padding:10px}.generaldetail-container[_ngcontent-%COMP%]   .over-all-filed[_ngcontent-%COMP%]   .label-input-value[_ngcontent-%COMP%]{gap:4px!important}.generaldetail-container[_ngcontent-%COMP%]   .over-all-filed[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:11px;font-weight:400;line-height:16px;letter-spacing:.02em;color:#5f6c81}.generaldetail-container[_ngcontent-%COMP%]   .over-all-filed[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%]   .required-field[_ngcontent-%COMP%]{color:red;font-size:15px;top:0}.generaldetail-container[_ngcontent-%COMP%]   .over-all-filed[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]{width:100%;font-family:var(--atsfontFamily);font-size:12px;font-weight:400;line-height:16px;letter-spacing:.02em;color:#45546e}.generaldetail-container[_ngcontent-%COMP%]   .over-all-filed[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-flex, .generaldetail-container[_ngcontent-%COMP%]   .over-all-filed[_ngcontent-%COMP%]     .mat-form-field-type-mat-select:not(.mat-form-field-disabled) .mat-form-field-flex{height:40px!important;display:flex!important;align-items:center!important}.generaldetail-container[_ngcontent-%COMP%]   .over-all-filed[_ngcontent-%COMP%]     .mat-select-value-text{width:100%!important;font-family:var(--atsfontFamily)!important;font-size:12px!important;font-weight:400!important;line-height:16px!important;letter-spacing:.02em!important;color:#45546e!important}.generaldetail-container[_ngcontent-%COMP%]   .over-all-filed[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-outline{height:40px!important}.generaldetail-container[_ngcontent-%COMP%]   .over-all-filed[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-wrapper{margin:0!important}.generaldetail-container[_ngcontent-%COMP%]   .over-all-filed[_ngcontent-%COMP%]   .phone-number[_ngcontent-%COMP%]{display:flex}.generaldetail-container[_ngcontent-%COMP%]   .over-all-filed[_ngcontent-%COMP%]   .phone-number[_ngcontent-%COMP%]   .country-code[_ngcontent-%COMP%]{padding:0;width:25%}.generaldetail-container[_ngcontent-%COMP%]   .over-all-filed[_ngcontent-%COMP%]   .phone-number[_ngcontent-%COMP%]   .country-code[_ngcontent-%COMP%]     .mat-form-field-wrapper{width:100%!important}.generaldetail-container[_ngcontent-%COMP%]   .over-all-filed[_ngcontent-%COMP%]   .phone-number[_ngcontent-%COMP%]   .country-code[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-infix{height:44px!important}.generaldetail-container[_ngcontent-%COMP%]   .over-all-filed[_ngcontent-%COMP%]   .phone-number[_ngcontent-%COMP%]   .country-code[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-wrapper{padding-bottom:6px!important}.generaldetail-container[_ngcontent-%COMP%]   .over-all-filed[_ngcontent-%COMP%]   .phone-number[_ngcontent-%COMP%]   .country-code[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-outline-end{border-radius:0!important}.generaldetail-container[_ngcontent-%COMP%]   .over-all-filed[_ngcontent-%COMP%]   .phone-number[_ngcontent-%COMP%]   .number[_ngcontent-%COMP%]{padding:0;width:75%}.generaldetail-container[_ngcontent-%COMP%]   .over-all-filed[_ngcontent-%COMP%]   .phone-number[_ngcontent-%COMP%]   .number[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-outline-start{border-radius:0!important}.generaldetail-container[_ngcontent-%COMP%]   .over-all-filed[_ngcontent-%COMP%]   .signature-content-edit-mode[_ngcontent-%COMP%]{position:relative;width:100%;height:100%;margin-top:20px;font-family:var(--atsfontFamily);font-size:12px;font-weight:500;line-height:24px;letter-spacing:.02em;color:#45546e}.generaldetail-container[_ngcontent-%COMP%]   .over-all-filed[_ngcontent-%COMP%]   .signature-content-edit-mode[_ngcontent-%COMP%]     .dx-htmleditor-content img{vertical-align:middle;padding-right:10px}.generaldetail-container[_ngcontent-%COMP%]   .over-all-filed[_ngcontent-%COMP%]   .signature-content-edit-mode[_ngcontent-%COMP%]     .dx-htmleditor-content table{width:50%}.generaldetail-container[_ngcontent-%COMP%]   .over-all-filed[_ngcontent-%COMP%]   .signature-content-edit-mode[_ngcontent-%COMP%]     .options{background-color:hsla(0,0%,74.9%,.15);margin-top:20px;padding:20px 10px 20px 20px}.generaldetail-container[_ngcontent-%COMP%]   .over-all-filed[_ngcontent-%COMP%]   .signature-content-edit-mode[_ngcontent-%COMP%]     .caption{font-size:18px;font-weight:500}.generaldetail-container[_ngcontent-%COMP%]   .over-all-filed[_ngcontent-%COMP%]   .signature-content-edit-mode[_ngcontent-%COMP%]     .option{margin-top:10px;display:inline-block;margin-right:40px}.generaldetail-container[_ngcontent-%COMP%]   .over-all-filed[_ngcontent-%COMP%]   .signature-content-edit-mode[_ngcontent-%COMP%]     .option>.dx-selectbox, .generaldetail-container[_ngcontent-%COMP%]   .over-all-filed[_ngcontent-%COMP%]   .signature-content-edit-mode[_ngcontent-%COMP%]     .option>.label{display:inline-block;vertical-align:middle}.generaldetail-container[_ngcontent-%COMP%]   .over-all-filed[_ngcontent-%COMP%]   .signature-content-edit-mode[_ngcontent-%COMP%]     .option>.label{margin-right:10px}.generaldetail-container[_ngcontent-%COMP%]   .over-all-filed[_ngcontent-%COMP%]   .signature-content[_ngcontent-%COMP%]{border:1px solid #dadce2;min-height:100px;border-radius:10px;padding:20px;font-family:var(--atsfontFamily);font-size:12px;font-weight:500;line-height:24px;letter-spacing:.02em;color:#45546e;height:165px;overflow:auto}.generaldetail-container[_ngcontent-%COMP%]   .over-all-filed[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{resize:none;width:100%;height:100px;border:1px solid #d2d2d2;border-radius:8px;outline:none;font-family:var(--atsfontFamily);font-size:12px;font-weight:400;color:#111434;padding:8px}.generaldetail-container[_ngcontent-%COMP%]   .over-all-filed[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]:disabled{background-color:#fff!important}.generaldetail-container[_ngcontent-%COMP%]   .over-all-filed[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]::placeholder{font-family:var(--atsfontFamily);font-size:12px;font-weight:400;color:#111434}.generaldetail-container[_ngcontent-%COMP%]   .over-all-filed[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]::-webkit-scrollbar{width:3px!important}.generaldetail-container[_ngcontent-%COMP%]   .over-all-filed[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{min-height:20px!important}.generaldetail-container[_ngcontent-%COMP%]   .label-value-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:4px}.generaldetail-container[_ngcontent-%COMP%]   .label-value-container[_ngcontent-%COMP%]   .view-value[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:12px;font-weight:500;line-height:24px;letter-spacing:.02em;color:#45546e;text-overflow:ellipsis;overflow:hidden}.reupload-overlay[_ngcontent-%COMP%]{background:#fff;padding:10px 32px 32px;border-radius:8px;width:600px}.reupload-overlay[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]{text-align:end;padding-bottom:10px}.reupload-overlay[_ngcontent-%COMP%]   .close-btn[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%]{cursor:pointer}.reupload-overlay[_ngcontent-%COMP%]   .drop-file[_ngcontent-%COMP%]{width:98%;height:150px;border:2px dashed #7d838b;border-radius:12px;gap:10px;cursor:pointer;padding:15px;position:relative}.reupload-overlay[_ngcontent-%COMP%]   .drop-file[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{cursor:pointer;opacity:0;position:absolute;z-index:2;width:100%;height:100%;top:0;left:0}.reupload-overlay[_ngcontent-%COMP%]   .drop-file[_ngcontent-%COMP%]   .text-1[_ngcontent-%COMP%]{font-family:var(--fontFamily);font-size:12px;font-weight:400;color:#7d838b}.reupload-overlay[_ngcontent-%COMP%]   .drop-file[_ngcontent-%COMP%]   .text-2[_ngcontent-%COMP%]{font-family:var(--fontFamily);font-size:12px;font-weight:500;color:#111434;text-decoration:underline}.reupload-overlay[_ngcontent-%COMP%]   .mini-content[_ngcontent-%COMP%]{width:98%}.reupload-overlay[_ngcontent-%COMP%]   .mini-content[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{font-family:var(--fontFamily);font-size:12px;font-weight:400;color:#7d838b;padding-top:7px}.loading-img[_ngcontent-%COMP%]{height:100%;flex-direction:column;background-color:#fff}.loading-img[_ngcontent-%COMP%], .loading-img[_ngcontent-%COMP%]   .loading-wrapper[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center}.loading-img[_ngcontent-%COMP%]   .loading-wrapper[_ngcontent-%COMP%]{vertical-align:center}.loading-img[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]{color:rgba(0,0,0,.3);font-size:16px}.loading-img[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]:before{content:"Loading...";position:absolute;overflow:hidden;max-width:7em;white-space:nowrap;background:linear-gradient(270deg,var(--atsprimaryColor),var(--atssecondaryColor) 105.29%);background-clip:text;-webkit-background-clip:text;-webkit-text-fill-color:transparent;animation:loading 6s linear infinite}@keyframes loading{0%{max-width:0}}']}),e})();var he=n("XXEo");const ue=function(e){return{color:e}};function fe(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div",36),c["\u0275\u0275elementStart"](1,"mat-icon",37),c["\u0275\u0275text"](2,"check_circle"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]().$implicit;c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngStyle",c["\u0275\u0275pureFunction1"](1,ue,e.primaryColor))}}const Ce=function(e,t,n){return{"box-shadow":e,"border-radius.px":t,border:n}},ve=function(e){return{"background-color":e}};function _e(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"div",17),c["\u0275\u0275listener"]("click",(function(){c["\u0275\u0275restoreView"](e);const n=t.index;return c["\u0275\u0275nextContext"](2).onClickSelectTheme(n)})),c["\u0275\u0275elementStart"](1,"div",3),c["\u0275\u0275elementStart"](2,"div",18),c["\u0275\u0275element"](3,"div",19),c["\u0275\u0275element"](4,"div",20),c["\u0275\u0275element"](5,"div",21),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](6,"div",8),c["\u0275\u0275elementStart"](7,"div",22),c["\u0275\u0275elementStart"](8,"div",23),c["\u0275\u0275elementStart"](9,"div",24),c["\u0275\u0275text"](10,"Aa"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275element"](11,"div",25),c["\u0275\u0275elementEnd"](),c["\u0275\u0275element"](12,"div",26),c["\u0275\u0275elementStart"](13,"div",27),c["\u0275\u0275elementStart"](14,"div",28),c["\u0275\u0275namespaceSVG"](),c["\u0275\u0275elementStart"](15,"svg",29),c["\u0275\u0275element"](16,"path",30),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275namespaceHTML"](),c["\u0275\u0275element"](17,"div",31),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](18,"div",32),c["\u0275\u0275element"](19,"div",33),c["\u0275\u0275element"](20,"div",34),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275template"](21,fe,3,3,"div",35),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=c["\u0275\u0275nextContext"](2);c["\u0275\u0275property"]("ngStyle",c["\u0275\u0275pureFunction3"](5,Ce,n.selectedTheme.id===e.id?"2px 5px 5px 1px "+e.primaryColor+"50":"none",n.selectedTheme.id===e.id?11:9,n.selectedTheme.id===e.id?"3px solid "+e.primaryColor:"1px solid #b9c0ca")),c["\u0275\u0275advance"](11),c["\u0275\u0275property"]("ngStyle",c["\u0275\u0275pureFunction1"](9,ve,e.primaryColor)),c["\u0275\u0275advance"](3),c["\u0275\u0275property"]("ngStyle",c["\u0275\u0275pureFunction1"](11,ve,e.primaryColor)),c["\u0275\u0275advance"](3),c["\u0275\u0275property"]("ngStyle",c["\u0275\u0275pureFunction1"](13,ve,e.primaryColor)),c["\u0275\u0275advance"](4),c["\u0275\u0275property"]("ngIf",n.selectedTheme.id===e.id)}}function Me(e,t){1&e&&c["\u0275\u0275element"](0,"img",38)}function Oe(e,t){1&e&&c["\u0275\u0275element"](0,"img",39)}function ye(e,t){1&e&&c["\u0275\u0275element"](0,"img",40)}function Pe(e,t){1&e&&c["\u0275\u0275element"](0,"img",41)}function xe(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"div",2),c["\u0275\u0275elementStart"](1,"div",3),c["\u0275\u0275elementStart"](2,"div",4),c["\u0275\u0275elementStart"](3,"div",5),c["\u0275\u0275text"](4,"Choose Theme"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](5,"div",6),c["\u0275\u0275template"](6,_e,22,15,"div",7),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](7,"div",8),c["\u0275\u0275elementStart"](8,"div",9),c["\u0275\u0275elementStart"](9,"div",10),c["\u0275\u0275text"](10,"Preview"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](11,"div",11),c["\u0275\u0275listener"]("click",(function(){return c["\u0275\u0275restoreView"](e),c["\u0275\u0275nextContext"]().onClickSave()})),c["\u0275\u0275text"](12,"Save"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](13,"div",12),c["\u0275\u0275template"](14,Me,1,0,"img",13),c["\u0275\u0275template"](15,Oe,1,0,"img",14),c["\u0275\u0275template"](16,ye,1,0,"img",15),c["\u0275\u0275template"](17,Pe,1,0,"img",16),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()}if(2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275advance"](6),c["\u0275\u0275property"]("ngForOf",e.themeList),c["\u0275\u0275advance"](8),c["\u0275\u0275property"]("ngIf","#79BA44"==e.selectedTheme.primaryColor),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","#F7A713"==e.selectedTheme.primaryColor),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","#1890FF"==e.selectedTheme.primaryColor),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","#ee4961"==e.selectedTheme.primaryColor)}}function be(e,t){if(1&e&&(c["\u0275\u0275elementContainerStart"](0),c["\u0275\u0275elementStart"](1,"div",42),c["\u0275\u0275elementStart"](2,"div",43),c["\u0275\u0275element"](3,"img",44),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](4,"div",45),c["\u0275\u0275elementStart"](5,"div",46),c["\u0275\u0275text"](6,"Loading..."),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementContainerEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275advance"](3),c["\u0275\u0275property"]("src",e.uiTextConfig["UI-LOADING-001"],c["\u0275\u0275sanitizeUrl"])}}let Se=(()=>{class e{constructor(e,t,n,i){this.elementRef=e,this._loginService=t,this._toaster=n,this._atsMasterService=i,this._onDestroy=new m.b,this.themeList=[],this.isLoading=!0,this.uiTextConfig={}}ngOnInit(){return Object(l.c)(this,void 0,void 0,(function*(){yield this.getAtsMasterUiConfig("collegesettingconfig"),this.aid=this._loginService.getProfile().profile.aid,yield this.getAtsMasterTheme(),this.selectedTheme=yield this.getDefaultTheme(),this.selectedTheme=JSON.parse(localStorage.getItem("selected-theme-ats"+this.aid))||this.selectedTheme,this.isLoading=!1}))}onClickSelectTheme(e){this.selectedTheme=this.themeList[e]}onClickSave(){return Object(l.c)(this,void 0,void 0,(function*(){localStorage.setItem("selected-theme-ats"+this.aid,JSON.stringify(this.selectedTheme)),document.documentElement.style.setProperty("--atsprimaryColor",this.selectedTheme.primaryColor),document.documentElement.style.setProperty("--atssecondaryColor",this.selectedTheme.secondaryColor),document.documentElement.style.setProperty("--atssecondaryColor1",this.selectedTheme.secondaryColor1),document.documentElement.style.setProperty("--atssecondaryColor2",this.selectedTheme.secondaryColor2),document.documentElement.style.setProperty("--atssecondaryColor3",this.selectedTheme.secondaryColor3),document.documentElement.style.setProperty("--atssecondaryColor4",this.selectedTheme.secondaryColor4),this._toaster.showSuccess("Success","ATS Theme Saved Successfully",7e3)}))}getDefaultTheme(){return this.themeList.find(e=>!0===e.is_default)}getAtsMasterTheme(){return Object(l.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._atsMasterService.getAtsMasterTheme().pipe(Object(h.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.themeList=t.data:this._toaster.showError("Error",t.msg,7e3),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Master Theme Retrieval Failed!",7e3),t()}}))}))}getAtsMasterUiConfig(e){return Object(l.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._atsMasterService.getAtsMasterUiConfig(e).pipe(Object(h.a)(this._onDestroy)).subscribe({next:n=>{0==n.err?"collegesettingconfig"==e&&(this.uiTextConfig=n.data.collegeConfigurations.uiTextConfig):this._toaster.showError("Error",n.msg,7e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS UI Configuration Retrieval Failed!",7e3),n()}}))}))}}return e.\u0275fac=function(t){return new(t||e)(c["\u0275\u0275directiveInject"](c.ElementRef),c["\u0275\u0275directiveInject"](he.a),c["\u0275\u0275directiveInject"](M.a),c["\u0275\u0275directiveInject"](_.a))},e.\u0275cmp=c["\u0275\u0275defineComponent"]({type:e,selectors:[["app-theme"]],decls:2,vars:2,consts:[["class","theme-main-container",4,"ngIf"],[4,"ngIf"],[1,"theme-main-container"],[1,"column-one"],[1,"theme-conatiner"],[1,"choose-theme-word"],[1,"theme-list"],["class","single-theme",3,"ngStyle","click",4,"ngFor","ngForOf"],[1,"column-two"],[1,"header"],[1,"preview"],[1,"save-button",3,"click"],[1,"theme-demo"],["class","theme-demo-image","src","https://assets.kebs.app/Theme%20Green.png","alt","",4,"ngIf"],["class","theme-demo-image","src","https://assets.kebs.app/Orange%20Theme.png","alt","",4,"ngIf"],["class","theme-demo-image","src","https://assets.kebs.app/Blue%20Theme.png","alt","",4,"ngIf"],["class","theme-demo-image","src","https://assets.kebs.app/Theme%20Red.png","alt","",4,"ngIf"],[1,"single-theme",3,"ngStyle","click"],[1,"lines"],[1,"darker-line"],[1,"medium-line"],[1,"lighter-line"],[1,"rows"],[1,"line-one"],[1,"letter"],[1,"line-one-color",3,"ngStyle"],[1,"line-two"],[1,"line-three"],[1,"person",3,"ngStyle"],["width","4","height","5","viewBox","0 0 4 5","fill","none","xmlns","http://www.w3.org/2000/svg"],["d","M0.439453 4.93921C0.439453 4.52688 0.60325 4.13144 0.89481 3.83988C1.18637 3.54832 1.58181 3.38452 1.99414 3.38452C2.40647 3.38452 2.80191 3.54832 3.09347 3.83988C3.38503 4.13144 3.54883 4.52688 3.54883 4.93921H3.16016C3.16016 4.62996 3.03731 4.33338 2.81864 4.11471C2.59997 3.89604 2.30339 3.77319 1.99414 3.77319C1.68489 3.77319 1.38831 3.89604 1.16964 4.11471C0.950973 4.33338 0.828125 4.62996 0.828125 4.93921H0.439453ZM1.99414 3.19019C1.34992 3.19019 0.828125 2.66839 0.828125 2.02417C0.828125 1.37995 1.34992 0.858154 1.99414 0.858154C2.63836 0.858154 3.16016 1.37995 3.16016 2.02417C3.16016 2.66839 2.63836 3.19019 1.99414 3.19019ZM1.99414 2.80151C2.42362 2.80151 2.77148 2.45365 2.77148 2.02417C2.77148 1.59469 2.42362 1.24683 1.99414 1.24683C1.56466 1.24683 1.2168 1.59469 1.2168 2.02417C1.2168 2.45365 1.56466 2.80151 1.99414 2.80151Z","fill","white"],[1,"line-three-color",3,"ngStyle"],[1,"line-four"],[1,"dot"],[1,"line-four-color"],["class","tick-mark",4,"ngIf"],[1,"tick-mark"],[3,"ngStyle"],["src","https://assets.kebs.app/Theme%20Green.png","alt","",1,"theme-demo-image"],["src","https://assets.kebs.app/Orange%20Theme.png","alt","",1,"theme-demo-image"],["src","https://assets.kebs.app/Blue%20Theme.png","alt","",1,"theme-demo-image"],["src","https://assets.kebs.app/Theme%20Red.png","alt","",1,"theme-demo-image"],[1,"loading-img"],[2,"max-width","60px","max-height","60px"],[2,"max-width","100%","max-height","100%",3,"src"],[1,"loading-wrapper"],[1,"loading"]],template:function(e,t){1&e&&(c["\u0275\u0275template"](0,xe,18,5,"div",0),c["\u0275\u0275template"](1,be,7,1,"ng-container",1)),2&e&&(c["\u0275\u0275property"]("ngIf",!t.isLoading),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",t.isLoading))},directives:[i.NgIf,i.NgForOf,i.NgStyle,P.a],styles:['.theme-main-container[_ngcontent-%COMP%]{display:flex;width:100%;height:100%;margin-top:25px}.theme-main-container[_ngcontent-%COMP%]   .column-one[_ngcontent-%COMP%]{background-color:#f1f3f8;width:50%}.theme-main-container[_ngcontent-%COMP%]   .column-one[_ngcontent-%COMP%]   .theme-conatiner[_ngcontent-%COMP%]{background-color:#f1f3f8;padding-inline:7%;padding-top:4%;height:80%}.theme-main-container[_ngcontent-%COMP%]   .column-one[_ngcontent-%COMP%]   .theme-conatiner[_ngcontent-%COMP%]   .choose-theme-word[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:medium;font-weight:700;line-height:20.83px;color:#1f2347}.theme-main-container[_ngcontent-%COMP%]   .column-one[_ngcontent-%COMP%]   .theme-conatiner[_ngcontent-%COMP%]   .theme-list[_ngcontent-%COMP%]{margin-top:10px;display:grid;grid-template-rows:1fr 1fr;grid-template-columns:1fr 1fr;height:92%;gap:3%;row-gap:5%}.theme-main-container[_ngcontent-%COMP%]   .column-one[_ngcontent-%COMP%]   .theme-conatiner[_ngcontent-%COMP%]   .theme-list[_ngcontent-%COMP%]   .single-theme[_ngcontent-%COMP%]{width:100%;height:100%;border-radius:8px 0 0 0;border:1px 0 0;border-radius:8px;display:flex;cursor:pointer;box-sizing:border-box;padding:0}.theme-main-container[_ngcontent-%COMP%]   .column-one[_ngcontent-%COMP%]   .theme-conatiner[_ngcontent-%COMP%]   .theme-list[_ngcontent-%COMP%]   .single-theme[_ngcontent-%COMP%]   .column-one[_ngcontent-%COMP%]{background-color:#111434;height:100%;width:40%;border-radius:8px 0 0 8px}.theme-main-container[_ngcontent-%COMP%]   .column-one[_ngcontent-%COMP%]   .theme-conatiner[_ngcontent-%COMP%]   .theme-list[_ngcontent-%COMP%]   .single-theme[_ngcontent-%COMP%]   .column-one[_ngcontent-%COMP%]   .lines[_ngcontent-%COMP%]{padding:20% 12% 0;display:flex;flex-direction:column;justify-content:space-between;height:40%}.theme-main-container[_ngcontent-%COMP%]   .column-one[_ngcontent-%COMP%]   .theme-conatiner[_ngcontent-%COMP%]   .theme-list[_ngcontent-%COMP%]   .single-theme[_ngcontent-%COMP%]   .column-one[_ngcontent-%COMP%]   .lines[_ngcontent-%COMP%]   .darker-line[_ngcontent-%COMP%]{background-color:#fff;height:20%}.theme-main-container[_ngcontent-%COMP%]   .column-one[_ngcontent-%COMP%]   .theme-conatiner[_ngcontent-%COMP%]   .theme-list[_ngcontent-%COMP%]   .single-theme[_ngcontent-%COMP%]   .column-one[_ngcontent-%COMP%]   .lines[_ngcontent-%COMP%]   .medium-line[_ngcontent-%COMP%]{background-color:hsla(0,0%,100%,.5);height:20%}.theme-main-container[_ngcontent-%COMP%]   .column-one[_ngcontent-%COMP%]   .theme-conatiner[_ngcontent-%COMP%]   .theme-list[_ngcontent-%COMP%]   .single-theme[_ngcontent-%COMP%]   .column-one[_ngcontent-%COMP%]   .lines[_ngcontent-%COMP%]   .lighter-line[_ngcontent-%COMP%]{background-color:hsla(0,0%,100%,.5);height:1%}.theme-main-container[_ngcontent-%COMP%]   .column-one[_ngcontent-%COMP%]   .theme-conatiner[_ngcontent-%COMP%]   .theme-list[_ngcontent-%COMP%]   .single-theme[_ngcontent-%COMP%]   .column-two[_ngcontent-%COMP%]{background-color:#fff;height:100%;width:100%;border-radius:0 8px 8px 0;box-sizing:border-box;display:flex;flex-direction:column;justify-content:space-between;padding:10px 10px 0;margin:0}.theme-main-container[_ngcontent-%COMP%]   .column-one[_ngcontent-%COMP%]   .theme-conatiner[_ngcontent-%COMP%]   .theme-list[_ngcontent-%COMP%]   .single-theme[_ngcontent-%COMP%]   .column-two[_ngcontent-%COMP%]   .rows[_ngcontent-%COMP%]{width:100%;display:flex;flex-direction:column;gap:3%;height:70%}.theme-main-container[_ngcontent-%COMP%]   .column-one[_ngcontent-%COMP%]   .theme-conatiner[_ngcontent-%COMP%]   .theme-list[_ngcontent-%COMP%]   .single-theme[_ngcontent-%COMP%]   .column-two[_ngcontent-%COMP%]   .rows[_ngcontent-%COMP%]   .line-one[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;width:100%}.theme-main-container[_ngcontent-%COMP%]   .column-one[_ngcontent-%COMP%]   .theme-conatiner[_ngcontent-%COMP%]   .theme-list[_ngcontent-%COMP%]   .single-theme[_ngcontent-%COMP%]   .column-two[_ngcontent-%COMP%]   .rows[_ngcontent-%COMP%]   .line-one[_ngcontent-%COMP%]   .letter[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);-webkit-user-select:none;user-select:none;font-size:medium}.theme-main-container[_ngcontent-%COMP%]   .column-one[_ngcontent-%COMP%]   .theme-conatiner[_ngcontent-%COMP%]   .theme-list[_ngcontent-%COMP%]   .single-theme[_ngcontent-%COMP%]   .column-two[_ngcontent-%COMP%]   .rows[_ngcontent-%COMP%]   .line-one[_ngcontent-%COMP%]   .line-one-color[_ngcontent-%COMP%]{height:70%;width:30%;border-radius:2px}.theme-main-container[_ngcontent-%COMP%]   .column-one[_ngcontent-%COMP%]   .theme-conatiner[_ngcontent-%COMP%]   .theme-list[_ngcontent-%COMP%]   .single-theme[_ngcontent-%COMP%]   .column-two[_ngcontent-%COMP%]   .rows[_ngcontent-%COMP%]   .line-two[_ngcontent-%COMP%]{height:3.5%;background-color:#8b95a5}.theme-main-container[_ngcontent-%COMP%]   .column-one[_ngcontent-%COMP%]   .theme-conatiner[_ngcontent-%COMP%]   .theme-list[_ngcontent-%COMP%]   .single-theme[_ngcontent-%COMP%]   .column-two[_ngcontent-%COMP%]   .rows[_ngcontent-%COMP%]   .line-three[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4%;height:30%}.theme-main-container[_ngcontent-%COMP%]   .column-one[_ngcontent-%COMP%]   .theme-conatiner[_ngcontent-%COMP%]   .theme-list[_ngcontent-%COMP%]   .single-theme[_ngcontent-%COMP%]   .column-two[_ngcontent-%COMP%]   .rows[_ngcontent-%COMP%]   .line-three[_ngcontent-%COMP%]   .person[_ngcontent-%COMP%]{padding:5%;border-radius:50%;display:flex;justify-content:center;align-items:center}.theme-main-container[_ngcontent-%COMP%]   .column-one[_ngcontent-%COMP%]   .theme-conatiner[_ngcontent-%COMP%]   .theme-list[_ngcontent-%COMP%]   .single-theme[_ngcontent-%COMP%]   .column-two[_ngcontent-%COMP%]   .rows[_ngcontent-%COMP%]   .line-three[_ngcontent-%COMP%]   .line-three-color[_ngcontent-%COMP%]{width:70%;height:25%}.theme-main-container[_ngcontent-%COMP%]   .column-one[_ngcontent-%COMP%]   .theme-conatiner[_ngcontent-%COMP%]   .theme-list[_ngcontent-%COMP%]   .single-theme[_ngcontent-%COMP%]   .column-two[_ngcontent-%COMP%]   .rows[_ngcontent-%COMP%]   .line-four[_ngcontent-%COMP%]{display:flex;align-items:center;gap:11%;height:30%}.theme-main-container[_ngcontent-%COMP%]   .column-one[_ngcontent-%COMP%]   .theme-conatiner[_ngcontent-%COMP%]   .theme-list[_ngcontent-%COMP%]   .single-theme[_ngcontent-%COMP%]   .column-two[_ngcontent-%COMP%]   .rows[_ngcontent-%COMP%]   .line-four[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%]{padding:3%;border-radius:50%;background-color:#111434;position:relative;left:3%}.theme-main-container[_ngcontent-%COMP%]   .column-one[_ngcontent-%COMP%]   .theme-conatiner[_ngcontent-%COMP%]   .theme-list[_ngcontent-%COMP%]   .single-theme[_ngcontent-%COMP%]   .column-two[_ngcontent-%COMP%]   .rows[_ngcontent-%COMP%]   .line-four[_ngcontent-%COMP%]   .line-four-color[_ngcontent-%COMP%]{width:70%;height:25%;background-color:#111434}.theme-main-container[_ngcontent-%COMP%]   .column-one[_ngcontent-%COMP%]   .theme-conatiner[_ngcontent-%COMP%]   .theme-list[_ngcontent-%COMP%]   .single-theme[_ngcontent-%COMP%]   .column-two[_ngcontent-%COMP%]   .tick-mark[_ngcontent-%COMP%]{position:absolute;bottom:5%;right:5%;-webkit-user-select:none;user-select:none}.theme-main-container[_ngcontent-%COMP%]   .column-one[_ngcontent-%COMP%]   .theme-conatiner[_ngcontent-%COMP%]   .theme-list[_ngcontent-%COMP%]   .single-theme[_ngcontent-%COMP%]   .column-two[_ngcontent-%COMP%]   .tick-mark[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%]{height:-moz-fit-content;height:fit-content;width:100%;display:flex;font-size:larger}.theme-main-container[_ngcontent-%COMP%]   .column-two[_ngcontent-%COMP%]{background-color:#fbfbfb;position:relative;margin-right:10px;overflow:hidden;width:50%}.theme-main-container[_ngcontent-%COMP%]   .column-two[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.theme-main-container[_ngcontent-%COMP%]   .column-two[_ngcontent-%COMP%]   .preview[_ngcontent-%COMP%]{color:#f1f3f8;width:302px;height:104px;top:-19px;left:-14px;font-family:var(--atsfontFamily);font-size:4rem;font-weight:700;line-height:104.16px;position:relative}.theme-main-container[_ngcontent-%COMP%]   .column-two[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);height:-moz-fit-content;height:fit-content;display:flex;font-size:small;align-items:center;justify-content:center;font-weight:700;padding:8px 16px;color:#fff;border-radius:8px;background-color:#111434;cursor:pointer}.theme-main-container[_ngcontent-%COMP%]   .column-two[_ngcontent-%COMP%]   .theme-demo[_ngcontent-%COMP%]{display:flex;align-items:flex-start;justify-content:end;overflow:hidden}.theme-main-container[_ngcontent-%COMP%]   .column-two[_ngcontent-%COMP%]   .theme-demo[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:83%;position:absolute;top:20%}.loading-img[_ngcontent-%COMP%]{height:100%;flex-direction:column;background-color:#fff}.loading-img[_ngcontent-%COMP%], .loading-img[_ngcontent-%COMP%]   .loading-wrapper[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center}.loading-img[_ngcontent-%COMP%]   .loading-wrapper[_ngcontent-%COMP%]{vertical-align:center}.loading-img[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]{color:rgba(0,0,0,.3);font-size:16px}.loading-img[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]:before{content:"Loading...";position:absolute;overflow:hidden;max-width:7em;white-space:nowrap;background:linear-gradient(270deg,var(--atsprimaryColor),var(--atssecondaryColor) 105.29%);background-clip:text;-webkit-background-clip:text;-webkit-text-fill-color:transparent;animation:loading 6s linear infinite}@keyframes loading{0%{max-width:0}}']}),e})();var we=n("Jzeh");function Ee(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"a",20),c["\u0275\u0275listener"]("click",(function(){c["\u0275\u0275restoreView"](e);const t=c["\u0275\u0275nextContext"]().$implicit;return c["\u0275\u0275nextContext"](2).onTabClick(t.path)})),c["\u0275\u0275text"](1),c["\u0275\u0275elementEnd"]()}if(2&e){const e=c["\u0275\u0275nextContext"]().$implicit,t=c["\u0275\u0275nextContext"](2);c["\u0275\u0275classProp"]("active-link",t.selectedTab==e.path),c["\u0275\u0275advance"](1),c["\u0275\u0275textInterpolate1"](" ",e.label," ")}}function ke(e,t){1&e&&c["\u0275\u0275element"](0,"mat-divider",21),2&e&&c["\u0275\u0275property"]("vertical",!0)}function Ie(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div",17),c["\u0275\u0275template"](1,Ee,2,3,"a",18),c["\u0275\u0275template"](2,ke,1,1,"mat-divider",19),c["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=t.last;c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",e.toDisplay),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",!n)}}function Fe(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"div",22),c["\u0275\u0275element"](1,"app-general-detail"),c["\u0275\u0275elementEnd"]())}function De(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"div",23),c["\u0275\u0275element"](1,"app-theme"),c["\u0275\u0275elementEnd"]())}function Te(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"div"),c["\u0275\u0275element"](1,"app-setting-header-overall"),c["\u0275\u0275elementStart"](2,"div",2),c["\u0275\u0275elementStart"](3,"div",3),c["\u0275\u0275elementStart"](4,"div",4),c["\u0275\u0275listener"]("click",(function(){return c["\u0275\u0275restoreView"](e),c["\u0275\u0275nextContext"]().goBackToPreviousRoute()})),c["\u0275\u0275namespaceSVG"](),c["\u0275\u0275elementStart"](5,"svg",5),c["\u0275\u0275element"](6,"path",6),c["\u0275\u0275element"](7,"path",7),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275namespaceHTML"](),c["\u0275\u0275elementStart"](8,"div",8),c["\u0275\u0275elementStart"](9,"div",9),c["\u0275\u0275text"](10,"Profile Setting"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](11,"div",10),c["\u0275\u0275text"](12," Streamline profile settings for seamless management and customization. "),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](13,"div",11),c["\u0275\u0275elementStart"](14,"div",12),c["\u0275\u0275elementStart"](15,"nav",13),c["\u0275\u0275template"](16,Ie,3,2,"div",14),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275template"](17,Fe,2,0,"div",15),c["\u0275\u0275template"](18,De,2,0,"div",16),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()}if(2&e){const e=c["\u0275\u0275nextContext"]();c["\u0275\u0275advance"](16),c["\u0275\u0275property"]("ngForOf",e.profileSettingTabLinks),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","general"==e.selectedTab),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf","theme"==e.selectedTab)}}function Le(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"div",24),c["\u0275\u0275element"](1,"app-access-denied"),c["\u0275\u0275elementEnd"]())}const Ae=[{path:"",component:(()=>{class e{constructor(e){this._activatedRoute=e,this.access=s,this.profileSettingTabLinks=[],this.selectedTab="general",this.hasAccess=!1}onTabClick(e){this.selectedTab=e}ngOnInit(){return Object(l.c)(this,void 0,void 0,(function*(){s.checkAccessForGeneralRole(s.moduleId.settings,s.subModuleId.profileSettings,s.sectionId.generalDetailView,0,"V"),s.checkAccessForGeneralRole(s.moduleId.settings,s.subModuleId.profileSettings,s.sectionId.generalDetailView,0,"V")&&(this.profileSettingTabLinks.push({label:"General",path:"general",toDisplay:!0}),this.hasAccess||(this.selectedTab="general",this.hasAccess=!0)),s.checkAccessForGeneralRole(s.moduleId.settings,s.subModuleId.profileSettings,s.sectionId.themeSettings,0,"V")&&(this.profileSettingTabLinks.push({label:"Theme",path:"theme",toDisplay:!0}),this.hasAccess||(this.selectedTab="theme",this.hasAccess=!0)),yield this.calculateDynamicContentHeight()}))}onResize(){this.calculateDynamicContentHeight()}calculateDynamicContentHeight(){return Object(l.c)(this,void 0,void 0,(function*(){this.dynamicHeight=window.innerHeight-57-56+"px",document.documentElement.style.setProperty("--dynamicHeight",this.dynamicHeight),this.dynamicSubHeight=window.innerHeight-57-63-128-153+"px",document.documentElement.style.setProperty("--dynamicSubHeight",this.dynamicSubHeight),this.dynamicTabHeight=window.innerHeight-57-63-127+"px",document.documentElement.style.setProperty("--dynamicTabHeight",this.dynamicTabHeight)}))}goBackToPreviousRoute(){history.back()}}return e.\u0275fac=function(t){return new(t||e)(c["\u0275\u0275directiveInject"](a.a))},e.\u0275cmp=c["\u0275\u0275defineComponent"]({type:e,selectors:[["app-landing-page"]],hostBindings:function(e,t){1&e&&c["\u0275\u0275listener"]("resize",(function(){return t.onResize()}),!1,c["\u0275\u0275resolveWindow"])},decls:2,vars:2,consts:[[4,"ngIf"],["style","margin: 0px 24px",4,"ngIf"],[1,"profile-detail-view"],[1,"profile-setting"],[3,"click"],["width","16","height","16","viewBox","0 0 16 16","fill","none",1,"arrow-svg"],["d","M6.00065 14.6668H10.0007C13.334 14.6668 14.6673 13.3335 14.6673 10.0002V6.00016C14.6673 2.66683 13.334 1.3335 10.0007 1.3335H6.00065C2.66732 1.3335 1.33398 2.66683 1.33398 6.00016V10.0002C1.33398 13.3335 2.66732 14.6668 6.00065 14.6668Z","stroke","#526179","stroke-width","0.8","stroke-linecap","round","stroke-linejoin","round"],["d","M8.84081 10.3532L6.49414 7.99982L8.84081 5.64648","stroke","#526179","stroke-width","0.8","stroke-linecap","round","stroke-linejoin","round"],[1,"profile-setting-lines"],[1,"profile-settings-title"],[1,"profile-settings-subtitle"],[1,"profile-container"],[1,"tab"],["mat-tab-nav-bar",""],["class","d-flex align-items-center",4,"ngFor","ngForOf"],["class","position-absolute profile-tab-details",4,"ngIf"],["class","position-absolute theme-tab-details",4,"ngIf"],[1,"d-flex","align-items-center"],["class","link-color","mat-tab-link","",3,"active-link","click",4,"ngIf"],["class","divider",3,"vertical",4,"ngIf"],["mat-tab-link","",1,"link-color",3,"click"],[1,"divider",3,"vertical"],[1,"position-absolute","profile-tab-details"],[1,"position-absolute","theme-tab-details"],[2,"margin","0px 24px"]],template:function(e,t){1&e&&(c["\u0275\u0275template"](0,Te,19,3,"div",0),c["\u0275\u0275template"](1,Le,2,0,"div",1)),2&e&&(c["\u0275\u0275property"]("ngIf",t.hasAccess),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",!t.hasAccess))},directives:[i.NgIf,g.a,d.f,i.NgForOf,d.e,p.a,me,Se,we.a],styles:['.profile-detail-view[_ngcontent-%COMP%]{padding:15px;background:#f1f3f8;height:var(--dynamicHeight);overflow:hidden}.profile-detail-view[_ngcontent-%COMP%]   .profile-setting[_ngcontent-%COMP%]{display:flex;padding:15px;background:#f9fafc}.profile-detail-view[_ngcontent-%COMP%]   .profile-setting[_ngcontent-%COMP%]   .arrow-svg[_ngcontent-%COMP%]{margin-top:2px;cursor:pointer}.profile-detail-view[_ngcontent-%COMP%]   .profile-setting[_ngcontent-%COMP%]   .profile-setting-lines[_ngcontent-%COMP%]{margin-left:10px}.profile-detail-view[_ngcontent-%COMP%]   .profile-setting[_ngcontent-%COMP%]   .profile-setting-lines[_ngcontent-%COMP%]   .profile-settings-title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:16px;font-weight:700;line-height:20.83px;text-align:left;color:#111434;width:-moz-fit-content;width:fit-content;height:21px}.profile-detail-view[_ngcontent-%COMP%]   .profile-setting[_ngcontent-%COMP%]   .profile-setting-lines[_ngcontent-%COMP%]   .profile-settings-subtitle[_ngcontent-%COMP%]{color:#8b95a5;font-family:var(--atsfontFamily);font-size:11px;font-weight:400;line-height:16px;letter-spacing:.02em;text-align:left;width:-moz-fit-content;width:fit-content;height:16px}.profile-detail-view[_ngcontent-%COMP%]   .profile-container[_ngcontent-%COMP%]{background-color:#fff;position:relative}.profile-detail-view[_ngcontent-%COMP%]   .profile-container[_ngcontent-%COMP%]   .tab[_ngcontent-%COMP%]     .mat-tab-header{border-bottom:1px solid #e8e9ee!important}.profile-detail-view[_ngcontent-%COMP%]   .profile-container[_ngcontent-%COMP%]   .tab[_ngcontent-%COMP%]     .mat-tab-nav-bar.mat-primary .mat-ink-bar{background-color:var(--color)!important}.profile-detail-view[_ngcontent-%COMP%]   .profile-container[_ngcontent-%COMP%]   .tab[_ngcontent-%COMP%]     .mat-tab-nav-bar{padding-left:16px!important;margin-left:0!important}.profile-detail-view[_ngcontent-%COMP%]   .profile-container[_ngcontent-%COMP%]   .tab[_ngcontent-%COMP%]     .mat-tab-link{height:39px!important;position:relative}.profile-detail-view[_ngcontent-%COMP%]   .profile-container[_ngcontent-%COMP%]   .tab[_ngcontent-%COMP%]     .mat-tab-link:after{content:"";position:absolute;left:0;bottom:0;width:100%;height:2px;background-color:initial;transition:background-color .3s ease,border-bottom-color .3s ease}.profile-detail-view[_ngcontent-%COMP%]   .profile-container[_ngcontent-%COMP%]   .tab[_ngcontent-%COMP%]     .mat-tab-link.active:after, .profile-detail-view[_ngcontent-%COMP%]   .profile-container[_ngcontent-%COMP%]   .tab[_ngcontent-%COMP%]     .mat-tab-link:hover:after{background-color:var(--atsprimaryColor);border-bottom:1px solid var(--atsprimaryColor)}.profile-detail-view[_ngcontent-%COMP%]   .profile-container[_ngcontent-%COMP%]   .link-color[_ngcontent-%COMP%]{color:rgba(0,0,0,.87);font-weight:500!important;font-size:14px!important;text-decoration:none!important}.profile-detail-view[_ngcontent-%COMP%]   .profile-container[_ngcontent-%COMP%]   .active-link[_ngcontent-%COMP%]{color:var(--atsprimaryColor)!important;border-bottom:1px solid var(--atsprimaryColor)}.profile-detail-view[_ngcontent-%COMP%]   .profile-container[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]{height:20px}.profile-detail-view[_ngcontent-%COMP%]   .profile-container[_ngcontent-%COMP%]   .profile-tab-details[_ngcontent-%COMP%]{overflow:auto;width:100%;height:var(--dynamicTabHeight);background-color:#fff}.profile-detail-view[_ngcontent-%COMP%]   .profile-container[_ngcontent-%COMP%]   .theme-tab-details[_ngcontent-%COMP%]{overflow:hidden;width:100%;height:var(--dynamicTabHeight);background-color:#fff}']}),e})()}];let je=(()=>{class e{}return e.\u0275mod=c["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=c["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[a.k.forChild(Ae)],a.k]}),e})();var ze=n("vxfF"),Ve=n("QibW"),He=n("iadO"),Re=n("1jcm"),Be=n("Qu3c"),Ne=n("d3UM"),Ge=(n("uVfO"),n("PVOt"));let $e=(()=>{let e=class{};return e.\u0275mod=c["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=c["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[w.bb,w.Gc,w.Vd,w.p,w.vd,w.hb,w.lb,w.sb,w.id,w.jd,w.Ud,w.Wd,w.Mb,Ge.c,Ge.f,y.b],w.bb,w.Gc,w.Vd,w.p,w.vd,w.hb,w.lb,w.sb,w.id,w.jd,w.Ud,w.Wd,w.Mb,Ge.f]}),e})();var Ue=n("Qlw+"),Ze=n("WYlB"),Ke=n("gMzk"),Qe=n("Gkpw");let qe=(()=>{class e{}return e.\u0275mod=c["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=c["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,je,o.ApplicantTrackingSystemModule,v.h,P.b,p.b,d.g,Ne.d,Be.b,b.c,u.p,u.E,Re.b,He.h,Ve.c,x.e,ze.g,r.a,$e,Ue.a,Ze.b,C.b,Ke.a,Qe.a]]}),e})()},rQiX:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var i=n("fXoL"),o=n("tk/3");let r=(()=>{class e{constructor(e){this._http=e}getAtsMasterUiConfig(e){return this._http.post("api/ats/masterService/getAtsMasterUiConfig",{configKey:e})}getAtsFormsConfig(e){return this._http.post("api/ats/masterService/getAtsFormsConfig",{formKey:e})}getAtsFormsConfigForCandidateBasedOnJob(e){return this._http.post("api/ats/masterService/getAtsFormsConfigForCandidateBasedOnJob",{jobId:e})}getToken(e){return this._http.post("api/ats/masterService/getToken",{db:e})}getDBdetails(e){return this._http.post("api/ats/masterService/getDBdetails",{hostName:e})}refreshToken(e){return this._http.post("api/ats/masterService/refreshToken",{tokenDetails:e})}getAtsHiringMasterData(e){return this._http.post("api/ats/masterService/getAtsHiringMasterData",{jobId:e})}getPreviewMasterData(e){return this._http.post("api/ats/masterService/getPreviewMasterData",{key:e})}getEntity(){return this._http.post("api/ats/masterService/getEntity",{})}getDivision(e){return this._http.post("api/ats/masterService/getDivision",{entityId:e})}getSubDivision(e,t){return this._http.post("api/ats/masterService/getSubDivision",{entityId:e,divisionId:t})}getDepartment(){return this._http.post("api/ats/masterService/getDepartment",{})}getFrequency(){return this._http.post("api/ats/masterService/getFrequency",{})}getCurrency(){return this._http.post("api/ats/masterService/getCurrency",{})}getEmploymentType(){return this._http.post("api/ats/masterService/getEmploymentType",{})}getWorkLocation(){return this._http.post("api/ats/masterService/getWorkLocation",{})}getExperience(){return this._http.post("api/ats/masterService/getExperience",{})}getCountry(){return this._http.post("api/ats/masterService/getCountry",{})}getAtsEmployee(){return this._http.post("api/ats/masterService/getAtsEmployee",{})}getSkill(){return this._http.post("api/ats/masterService/getSkill",{})}getJobStatus(){return this._http.post("api/ats/masterService/getJobStatus",{})}getStageActionType(){return this._http.post("api/ats/masterService/getStageActionType",{})}getStageTransitionType(){return this._http.post("api/ats/masterService/getStageTransitionType",{})}getReminderFrequency(){return this._http.post("api/ats/masterService/getReminderFrequency",{})}getReceiverEmail(){return this._http.post("api/ats/masterService/getReceiverEmail",{})}getSenderEmail(){return this._http.post("api/ats/masterService/getSenderEmail",{})}getPlaceHolders(){return this._http.post("api/ats/masterService/getPlaceHolders",{})}getAttachmentConfig(e){return this._http.post("api/ats/masterService/getAttachmentConfig",{module_id:e})}getHiringFlowStatusByStageId(e){return this._http.post("api/ats/masterService/getHiringFlowStatusByStageId",{stageId:e})}fetchAllTemplates(e){return this._http.post("api/ats/candidate/fetchAllTemplates",{templateCategory:e})}getAtsCustomQuestionType(){return this._http.post("api/ats/masterService/getAtsCustomQuestionType",{})}getAllRole(){return this._http.post("api/ats/profile/getAllRole",{})}getAssociateDetailsInSystem(){return this._http.post("api/ats/profile/getAssociateDetailsInSystem",{})}getShiftTiming(){return this._http.post("api/ats/masterService/getShiftTiming",{})}getGenderDetails(){return this._http.post("api/ats/masterService/getGenderDetails",{})}getState(e){return this._http.post("api/ats/masterService/getState",{countryId:e})}getCity(e){return this._http.post("api/ats/masterService/getCity",{stateId:e})}getCandidateApplyType(){return this._http.post("api/ats/masterService/getCandidateApplyType",{})}getNotificationMode(){return this._http.post("api/ats/masterService/getNotificationMode",{})}getScorecardReminderType(){return this._http.post("api/ats/masterService/getScorecardReminderType",{})}getReminderTiming(){return this._http.post("api/ats/masterService/getReminderTiming",{})}getReminderStopConditions(){return this._http.post("api/ats/masterService/getReminderStopConditions",{})}getStageTransitionFrequency(){return this._http.post("api/ats/masterService/getStageTransitionFrequency",{})}getIntershipDuration(){return this._http.post("api/ats/masterService/getIntershipDuration",{})}getDesignation(){return this._http.post("api/ats/masterService/getDesignation",{})}getStageStatus(){return this._http.post("api/ats/masterService/getStageStatus",{})}getJobHiringStageBasedOnCategory(e,t){return this._http.post("api/ats/masterService/getJobHiringStageBasedOnCategory",{jobId:e,hiringStageCategory:t})}getInterviewPlatform(){return this._http.post("api/ats/masterService/getInterviewPlatform",{})}getScheduleType(){return this._http.post("api/ats/masterService/getScheduleType",{})}getTimezone(){return this._http.post("api/ats/masterService/getTimezone",{})}getInterviewMode(){return this._http.post("api/ats/masterService/getInterviewMode",{})}getCommentsVisibilityTypeMasterData(){return this._http.post("api/ats/masterService/getCommentsVisibilityTypeMasterData",{})}getAllCollegeDetails(){return this._http.post("api/ats/masterService/getAllCollegeDetails",{})}getDegreeList(){return this._http.post("api/ats/masterService/getDegreeList",{})}getCourseList(){return this._http.post("api/ats/masterService/getCourseList",{})}getEducationLevelList(){return this._http.post("api/ats/masterService/getEducationLevelList",{})}getScorecardInterviewStatus(e,t){return this._http.post("api/ats/masterService/getScorecardInterviewStatus",{jobID:e,currentUserAID:t})}getNationality(){return this._http.post("api/ats/masterService/getNationality",{})}getTemplateModuleType(){return this._http.post("api/ats/masterService/getTemplateModuleType",{})}getCustomQuestionsFormSections(e){return this._http.post("api/ats/masterService/getCustomQuestionsFormSections",{jobId:e})}getAtsVendorType(){return this._http.post("api/ats/masterService/getAtsVendorType",{})}getVendorStatusList(){return this._http.post("api/ats/masterService/getVendorStatusList",{})}getPhoneCode(){return this._http.post("api/ats/masterService/getPhoneCode",{})}getProfileStatusList(){return this._http.post("api/ats/masterService/getProfileStatusList",{})}getAtsMasterTheme(){return this._http.post("api/ats/masterService/getAtsMasterTheme",{})}getAtsMasterMailPrivacyConfig(){return this._http.post("api/ats/masterService/getAtsMasterMailPrivacyConfig",{})}getAtsMasterMailDetailConfig(){return this._http.post("api/ats/masterService/getAtsMasterMailDetailConfig",{})}getAtsCalendarDetailConfig(){return this._http.post("api/ats/masterService/getAtsCalendarDetailConfig",{})}getAtsMasterCalendarPrivacyConfig(){return this._http.post("api/ats/masterService/getAtsMasterCalendarPrivacyConfig",{})}getAtsCandidateAvailability(){return this._http.post("api/ats/masterService/getAtsCandidateAvailability",{})}getDegreeListForSettings(e,t){return this._http.post("api/ats/masterService/getDegreeListForSettings",{searchParams:e,sortParams:t})}getCourseListForSettings(e,t){return this._http.post("api/ats/masterService/getCourseListForSettings",{searchParams:e,sortParams:t})}getAssociatedDegreeMasterDetails(){return this._http.post("api/ats/masterService/getAssociatedDegreeMasterDetails",{})}getCourseDegreeMappingDetails(e,t){return this._http.post("api/ats/masterService/getCourseDegreeMappingDetails",{searchParams:e,sortParams:t})}getCourseMasterDetails(){return this._http.post("api/ats/masterService/getCourseMasterDetails",{})}getDegreeMasterDetails(){return this._http.post("api/ats/masterService/getDegreeMasterDetails",{})}getStateListForSettings(e,t,n,i){return this._http.post("api/ats/masterService/getStateListForSettings",{searchParams:e,sortParams:t,skip:n,limit:i})}getCountryMasterForSettings(){return this._http.post("api/ats/masterService/getCountryMasterForSettings",{})}getCountryListForSettings(e,t,n,i){return this._http.post("api/ats/masterService/getCountryListForSettings",{searchParams:e,sortParams:t,skip:n,limit:i})}getRegionListForSettings(e,t,n,i){return this._http.post("api/ats/masterService/getRegionListForSettings",{searchParams:e,sortParams:t,skip:n,limit:i})}getCityMasterForSettings(){return this._http.post("api/ats/masterService/getCityMasterForSettings",{})}getCityListForSettings(e,t,n,i){return this._http.post("api/ats/masterService/getCityListForSettings",{searchParams:e,sortParams:t,skip:n,limit:i})}getStateMasterForSettings(){return this._http.post("api/ats/masterService/getStateMasterForSettings",{})}getSalutationMaster(){return this._http.post("/api/ats/masterService/getSalutationMaster",{})}getRelationshipStatusMaster(){return this._http.post("/api/ats/masterService/getRelationshipStatusMaster",{})}getRelationshipTypeMaster(){return this._http.post("/api/ats/masterService/getRelationshipTypeMaster",{})}getCandidateNationalityDocumentsMaster(){return this._http.post("/api/ats/masterService/getCandidateNationalityDocumentsMaster",{})}getBloodGroupMaster(){return this._http.post("/api/ats/masterService/getBloodGroupMaster",{})}getSkills(){return this._http.post("/api/ats/masterService/getSkills",{})}getSkillLevelMaster(){return this._http.post("/api/ats/masterService/getSkillLevelMaster",{})}getWorkSchedule(){return this._http.post("/api/ats/masterService/getWorkSchedule",{})}getHolidayCalendar(){return this._http.post("/api/ats/masterService/getHolidayCalendar",{})}getRegion(){return this._http.post("/api/ats/masterService/getRegion",{})}getLevel(){return this._http.post("/api/ats/masterService/getLevel",{})}getJobType(){return this._http.post("/api/ats/masterService/getJobType",{})}getPayrollMasterData(){return this._http.post("/api/payrollapp/payrollPrimary/getPayrollMasterData",{})}generateCTCSplittup(e,t,n,i,o){return this._http.post("/api/ats/utilService/generateCTC",{ctc:e,payscaleArea:t,payscaleGroup:n,payscaleLevel:i,currency:o})}getAllCities(){return this._http.post("api/ats/masterService/getAllCities",{})}getDegreeCourseMapping(){return this._http.post("api/ats/masterService/getDegreeCourseMapping",{})}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275inject"](o.c))},e.\u0275prov=i["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()}}]);