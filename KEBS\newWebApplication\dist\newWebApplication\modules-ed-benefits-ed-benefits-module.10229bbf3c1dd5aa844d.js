(window.webpackJsonp=window.webpackJsonp||[]).push([[808],{"6cfA":function(e,t,n){"use strict";n.r(t),n.d(t,"EdBenefitsModule",(function(){return y}));var o=n("ofXK"),i=n("tyNb"),s=n("fXoL"),r=n("33Jv"),c=n("jAlA"),l=n("1A3m");const a=["contentContainer"],d=[{path:"",component:(()=>{class e{constructor(e,t,n,o,i){this._router=e,this._edService=t,this._route=n,this._toaster=o,this._compiler=i,this.subs=new r.a}ngOnInit(){this.getEmployeeId(),this.loadBenefitsViewDetailContainer()}getEmployeeId(){this.subs.sink=this._edService.getEmployeeId().subscribe(e=>{console.log(e),this.associateId=e})}loadBenefitsViewDetailContainer(){this.contentContainerRef&&this.contentContainerRef.clear(),Promise.all([n.e(20),n.e(25),n.e(392)]).then(n.bind(null,"o/qA")).then(e=>{const t=this._compiler.compileModuleSync(e.BenefitsDetailModule).create(this.contentContainerRef.injector).componentFactoryResolver.resolveComponentFactory(e.BenefitsDetailComponent);this.contentContainerRef.createComponent(t).instance.associateId=this.associateId})}ngOnDestroy(){this.subs.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275directiveInject"](i.g),s["\u0275\u0275directiveInject"](c.a),s["\u0275\u0275directiveInject"](i.a),s["\u0275\u0275directiveInject"](l.a),s["\u0275\u0275directiveInject"](s.Compiler))},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-ed-benefits-landing-page"]],viewQuery:function(e,t){if(1&e&&s["\u0275\u0275viewQuery"](a,!0,s.ViewContainerRef),2&e){let e;s["\u0275\u0275queryRefresh"](e=s["\u0275\u0275loadQuery"]())&&(t.contentContainerRef=e.first)}},decls:5,vars:0,consts:[[1,"container-fluid","ed-benefits-detail-item-styles"],[1,"row"],[1,"col-12","p-0"],["contentContainer",""]],template:function(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"div",0),s["\u0275\u0275elementStart"](1,"div",1),s["\u0275\u0275elementStart"](2,"div",2),s["\u0275\u0275elementContainer"](3,null,3),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]())},styles:[".ed-benefits-detail-item-styles[_ngcontent-%COMP%]   .overflow[_ngcontent-%COMP%]{height:28rem;overflow:scroll;overflow-x:hidden}.ed-benefits-detail-item-styles[_ngcontent-%COMP%]   .normalFont[_ngcontent-%COMP%]{color:#181818;font-size:14px;font-weight:600;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}"]}),e})()}];let f=(()=>{class e{}return e.\u0275mod=s["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=s["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.k.forChild(d)],i.k]}),e})();var u=n("bTqV"),m=n("NFeN"),p=n("Qu3c"),h=n("jaxi"),w=n("STbY"),v=n("MutI"),C=n("0IaG");let y=(()=>{class e{}return e.\u0275mod=s["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=s["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[o.CommonModule,f,m.b,u.b,p.b,h.c,w.e,v.d,C.g]]}),e})()}}]);