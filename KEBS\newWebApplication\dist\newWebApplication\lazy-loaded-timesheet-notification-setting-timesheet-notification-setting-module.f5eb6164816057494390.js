(window.webpackJsonp=window.webpackJsonp||[]).push([[778],{Eg4Z:function(e,t,n){"use strict";n.r(t),n.d(t,"TimesheetNotificationSettingModule",(function(){return j}));var i=n("ofXK"),o=n("tyNb"),r=n("mrSG"),a=n("XNiG"),s=n("1G5W"),c=n("bEYa"),d=n("fXoL"),l=n("oIGK"),m=n("1A3m"),g=n("BVzC"),p=n("AK7O"),h=n("/zTS"),f=n("f0Cb"),v=n("Qu3c"),u=n("3Pt+");function x(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275namespaceSVG"](),d["\u0275\u0275namespaceHTML"](),d["\u0275\u0275elementStart"](0,"div",12),d["\u0275\u0275elementStart"](1,"div",13),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().updateNotificationTemplate()})),d["\u0275\u0275text"](2," Save "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}}const C=function(e,t){return{"background-color":e,color:t}};function S(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",16),d["\u0275\u0275elementStart"](1,"div",17),d["\u0275\u0275elementStart"](2,"div",18),d["\u0275\u0275elementStart"](3,"div",19),d["\u0275\u0275elementStart"](4,"div",20),d["\u0275\u0275elementStart"](5,"div",21),d["\u0275\u0275text"](6),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](7,"div",22),d["\u0275\u0275elementStart"](8,"div",23),d["\u0275\u0275text"](9),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](10,"div",22),d["\u0275\u0275elementStart"](11,"div",21),d["\u0275\u0275text"](12),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275element"](13,"div",24),d["\u0275\u0275elementStart"](14,"div",25),d["\u0275\u0275elementStart"](15,"div",26),d["\u0275\u0275listener"]("click",(function(){d["\u0275\u0275restoreView"](e);const n=t.$implicit;return d["\u0275\u0275nextContext"](2).viewNotificationDetailContent(n.id,n)})),d["\u0275\u0275namespaceSVG"](),d["\u0275\u0275elementStart"](16,"svg",27),d["\u0275\u0275element"](17,"path",28),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275namespaceHTML"](),d["\u0275\u0275element"](18,"mat-divider",29),d["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;d["\u0275\u0275advance"](6),d["\u0275\u0275textInterpolate1"](" ",e.notification_description," "),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngStyle",d["\u0275\u0275pureFunction2"](4,C,e.bg_color,e.color)),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",e.notification_type," "),d["\u0275\u0275advance"](3),d["\u0275\u0275textInterpolate"](e.notification_send_type)}}function b(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",14),d["\u0275\u0275template"](1,S,19,7,"div",15),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngForOf",e.emailTemplate)}}function M(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",14),d["\u0275\u0275elementStart"](1,"div",30),d["\u0275\u0275elementStart"](2,"div",31),d["\u0275\u0275elementStart"](3,"div",30),d["\u0275\u0275element"](4,"ngx-editor-menu",32),d["\u0275\u0275elementStart"](5,"ngx-editor",33),d["\u0275\u0275listener"]("ngModelChange",(function(t){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().reminderTemplate=t})),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](4),d["\u0275\u0275property"]("editor",e.emailEditor),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("editor",e.emailEditor)("placeholder","Update Timesheet Notification Template Template")("ngModel",e.reminderTemplate)}}const _=[{path:"",component:(()=>{class e{constructor(e,t,n,i,o,r){this.tsSubmissionService=e,this._toasterService=t,this._errorService=n,this.tsSettingService=i,this.router=o,this.tsService=r,this.emailTemplate=[],this.id=1,this._onDestroy=new a.b,this.showDetailNotification=!1,this.mainText="Timesheet Notification Settings"}ngOnInit(){return Object(r.c)(this,void 0,void 0,(function*(){yield this.checkSettingsAccess(),this.mainText="Timesheet Notification Settings",this.emailEditor=new c.a,this.getNotificationTemplateMaster(),this.getReminderText(0)}))}getReminderText(e){var t,n;this.id=null===(t=this.emailTemplate[e])||void 0===t?void 0:t.id,this.reminderTemplate=null===(n=this.emailTemplate[e])||void 0===n?void 0:n.data}getNotificationTemplateMaster(){this.tsSubmissionService.getNotificationTemplateMaster().pipe(Object(s.a)(this._onDestroy)).subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){e&&"S"==e.messType&&e.data&&e.data.length>0?this.emailTemplate=e.data:this._toasterService.showError("Timesheet Reminder Config",e.messText,3e3)})),e=>{console.log(e),this._errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Something Went Wrong, Kindly Try After Sometime ",e&&e.params?e.params:e&&e.error?e.error.params:{})})}updateNotificationTemplate(){if(null==this.reminderTemplate||""==this.reminderTemplate)return this._toasterService.showError("Timesheet Reminder Config","Kindly enter the template text",3e3);let e=(new DOMParser).parseFromString(this.reminderTemplate,"text/html");this.tsSubmissionService.updateNotificationMasterData(this.id,e.body.innerHTML).pipe(Object(s.a)(this._onDestroy)).subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){"S"==e.messType?this._toasterService.showSuccess("Timesheet Reminder Config",e.messText,3e3):this._toasterService.showError("Timesheet Reminder Config",e.messText,3e3)})),e=>{console.log(e),this._errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Something Went Wrong, Kindly Try After Sometime ",e&&e.params?e.params:e&&e.error?e.error.params:{})})}naviagteToTimesheetSettings(){this.showDetailNotification?(this.getNotificationTemplateMaster(),this.mainText="Timesheet Notification Settings",this.showDetailNotification=!1):this.router.navigateByUrl("/main/timesheetv2/settings")}viewNotificationDetailContent(e,t){this.mainText=t.notification_description,this.showDetailNotification=!0,this.id=e,this.reminderTemplate=t.data}ngOnDestroy(){this.emailEditor.destroy(),this._onDestroy.unsubscribe()}checkSettingsAccess(){return Object(r.c)(this,void 0,void 0,(function*(){this.tsService.checkTimesheetAccess().subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){"S"==e.messType&&(e.settingAccess||this.router.navigateByUrl("/main/timesheetv2/submission"))})))}))}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](l.a),d["\u0275\u0275directiveInject"](m.a),d["\u0275\u0275directiveInject"](g.a),d["\u0275\u0275directiveInject"](p.a),d["\u0275\u0275directiveInject"](o.g),d["\u0275\u0275directiveInject"](h.a))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-timesheet-notification-settings"]],decls:14,vars:4,consts:[[1,"settings-container"],[1,"settings"],[1,"settings-header"],[1,"title","d-flex"],[1,"d-flex","back-button",3,"click"],["_ngcontent-gbh-c448","","width","16","height","16","viewBox","0 0 16 16","fill","none",1,"arrow-svg"],["_ngcontent-gbh-c448","","d","M6.00065 14.6668H10.0007C13.334 14.6668 14.6673 13.3335 14.6673 10.0002V6.00016C14.6673 2.66683 13.334 1.3335 10.0007 1.3335H6.00065C2.66732 1.3335 1.33398 2.66683 1.33398 6.00016V10.0002C1.33398 13.3335 2.66732 14.6668 6.00065 14.6668Z","stroke","#526179","stroke-width","0.8","stroke-linecap","round","stroke-linejoin","round"],["_ngcontent-gbh-c448","","d","M8.84081 10.3532L6.49414 7.99982L8.84081 5.64648","stroke","#526179","stroke-width","0.8","stroke-linecap","round","stroke-linejoin","round"],["class","buttons",4,"ngIf"],[2,"padding-top","5px"],[1,"mat-start-divider"],["class","settings-content",4,"ngIf"],[1,"buttons"],[1,"save-button",3,"click"],[1,"settings-content"],["class","col-12 pt-2 pb-2 pl-0 pr-0",4,"ngFor","ngForOf"],[1,"col-12","pt-2","pb-2","pl-0","pr-0"],[1,"d-flex",2,"margin-bottom","10px"],[1,"col-2","header-text"],[1,"d-flex",2,"gap","15px"],[1,"d-flex","flex-column","content"],[1,"header-text"],[1,"col-2"],[1,"header-text","chip",3,"ngStyle"],[1,"col-4"],[1,"col-2","d-flex",2,"gap","25px"],["matTooltip","Edit Notification Content",2,"cursor","pointer",3,"click"],["opacity","0.5","width","16","height","16","viewBox","0 0 16 16","fill","none"],["d","M1.41175 14.5883H2.59907L12.2335 4.9538L11.0462 3.76648L1.41175 13.4009V14.5883ZM0 16V12.8145L12.4145 0.405437C12.5569 0.276166 12.714 0.176275 12.886 0.105765C13.0579 0.0352549 13.2383 0 13.427 0C13.6157 0 13.7985 0.0334908 13.9754 0.100471C14.1523 0.167436 14.3089 0.273915 14.4452 0.419907L15.5946 1.5837C15.7406 1.72004 15.8446 1.87694 15.9068 2.05438C15.9689 2.23181 16 2.40924 16 2.58667C16 2.77592 15.9677 2.95653 15.903 3.1285C15.8384 3.30049 15.7356 3.45764 15.5946 3.59996L3.18549 16H0ZM11.6294 4.37055L11.0462 3.76648L12.2335 4.9538L11.6294 4.37055Z","fill","#1C1B1F"],[1,"mat-end-divider"],[1,"col-12"],[1,"row"],[3,"editor"],[3,"editor","placeholder","ngModel","ngModelChange"]],template:function(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",0),d["\u0275\u0275elementStart"](1,"div",1),d["\u0275\u0275elementStart"](2,"div",2),d["\u0275\u0275elementStart"](3,"div",3),d["\u0275\u0275elementStart"](4,"div",4),d["\u0275\u0275listener"]("click",(function(){return t.naviagteToTimesheetSettings()})),d["\u0275\u0275namespaceSVG"](),d["\u0275\u0275elementStart"](5,"svg",5),d["\u0275\u0275element"](6,"path",6),d["\u0275\u0275element"](7,"path",7),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275text"](8),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](9,x,3,0,"div",8),d["\u0275\u0275elementEnd"](),d["\u0275\u0275namespaceHTML"](),d["\u0275\u0275elementStart"](10,"div",9),d["\u0275\u0275element"](11,"mat-divider",10),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](12,b,2,1,"div",11),d["\u0275\u0275template"](13,M,6,4,"div",11),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e&&(d["\u0275\u0275advance"](8),d["\u0275\u0275textInterpolate1"](" ",t.mainText," "),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.showDetailNotification),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngIf",!t.showDetailNotification),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",t.showDetailNotification))},directives:[i.NgIf,f.a,i.NgForOf,i.NgStyle,v.a,c.b,c.c,u.v,u.y],styles:[".settings-container[_ngcontent-%COMP%]{background-color:#f1f3f8;overflow:hidden}.settings-container[_ngcontent-%COMP%]   .settings[_ngcontent-%COMP%]{margin-left:16px;margin-right:16px;margin-bottom:16px;padding:4px 16px 14px;height:var(--dynamicHeight);border-radius:4px;background-color:#fff;border:1px solid #e8e9ee;position:relative;gap:5px;display:flex;flex-direction:column}.settings-container[_ngcontent-%COMP%]   .settings[_ngcontent-%COMP%]   .settings-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;padding:10px 10px 10px 0}.settings-container[_ngcontent-%COMP%]   .settings[_ngcontent-%COMP%]   .settings-header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{color:#45546e;font-family:var(--fontFamily);font-size:14px;font-weight:900;line-height:16px;letter-spacing:.02em}.settings-container[_ngcontent-%COMP%]   .settings[_ngcontent-%COMP%]   .settings-header[_ngcontent-%COMP%]   .buttons[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%]{padding:8px 12px;background-color:#79ba44;color:#fff;font-family:var(--fontFamily);font-size:14px;font-weight:600;line-height:16px;border-radius:5px;cursor:pointer}.settings-container[_ngcontent-%COMP%]   .settings[_ngcontent-%COMP%]   .settings-content[_ngcontent-%COMP%]{overflow:auto;height:100%}.settings-container[_ngcontent-%COMP%]   .settings[_ngcontent-%COMP%]   .settings-content[_ngcontent-%COMP%]::-webkit-scrollbar{width:7px!important}.settings-container[_ngcontent-%COMP%]   .settings[_ngcontent-%COMP%]   .settings-content[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:transparent!important}.settings-container[_ngcontent-%COMP%]   .settings[_ngcontent-%COMP%]   .mat-start-divider[_ngcontent-%COMP%]{position:absolute;width:98%;background:#45546e}.settings-container[_ngcontent-%COMP%]   .settings[_ngcontent-%COMP%]   .header-text[_ngcontent-%COMP%]{font-family:var(--fontFamily);font-weight:500;font-size:14px;color:#45546e}.settings-container[_ngcontent-%COMP%]   .settings[_ngcontent-%COMP%]   .header-detail[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-weight:400;font-size:12px;color:#b9c0ca}.settings-container[_ngcontent-%COMP%]   .auto-ts-btn[_ngcontent-%COMP%]{background:linear-gradient(270deg,var(--color),var(--color) 105.29%)!important;color:#fff;font-size:12px;border-radius:5px}.settings-container[_ngcontent-%COMP%]   .example-form[_ngcontent-%COMP%]{min-width:150px;max-width:500px;width:100%}.settings-container[_ngcontent-%COMP%]   .example-full-width[_ngcontent-%COMP%]{width:100%}.settings-container[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]{cursor:pointer;margin-right:15px}.settings-container[_ngcontent-%COMP%]   .chip[_ngcontent-%COMP%]{border-radius:24px;text-align:center;padding:2px 12px;font-weight:500}"]}),e})()}];let w=(()=>{class e{}return e.\u0275mod=d["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=d["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[o.k.forChild(_)],o.k]}),e})();var O=n("kmnG"),T=n("qFsG"),y=n("bSwM"),P=n("1jcm"),E=n("QibW"),k=n("d3UM"),N=n("NFeN"),I=n("Xi0T");let j=(()=>{class e{}return e.\u0275mod=d["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=d["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,w,u.E,O.e,T.c,y.b,P.b,E.c,k.d,N.b,f.b,v.b,I.a,c.d,u.p]]}),e})()}}]);