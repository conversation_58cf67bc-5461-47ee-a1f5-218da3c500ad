(window.webpackJsonp=window.webpackJsonp||[]).push([[741],{LqJ8:function(e,t,i){"use strict";i.r(t),i.d(t,"ApprovalCreationComponent",(function(){return N}));var n=i("mrSG"),a=i("fXoL"),r=i("3Pt+"),o=i("wd/R"),s=i("0IaG"),l=i("ofXK"),c=i("jtHE"),p=i("XNiG"),d=i("NJ67"),h=i("1G5W"),u=i("kmnG"),m=i("d3UM"),g=i("FKr1"),v=i("WJ5W");const f=["singleSelect"];function b(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"mat-option",6),a["\u0275\u0275listener"]("click",(function(){a["\u0275\u0275restoreView"](e);const i=t.$implicit;return a["\u0275\u0275nextContext"]().emitChanges(i)})),a["\u0275\u0275text"](1),a["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;a["\u0275\u0275property"]("value",e.id),a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let y=(()=>{class e extends d.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new r.j,this.fieldFilterCtrl=new r.j,this.list=[],this.required=!1,this.valueChange=new a.EventEmitter,this.disabled=!1,this.filteredList=new c.a,this.change=new a.EventEmitter,this._onDestroy=new p.b,this.emitChanges=e=>{this.change.emit(e)}}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(h.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(h.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275directiveInject"](a.Renderer2))},e.\u0275cmp=a["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search"]],viewQuery:function(e,t){if(1&e&&a["\u0275\u0275viewQuery"](f,!0),2&e){let e;a["\u0275\u0275queryRefresh"](e=a["\u0275\u0275loadQuery"]())&&(t.singleSelect=e.first)}},inputs:{list:"list",placeholder:"placeholder",required:"required",disabled:"disabled"},outputs:{valueChange:"valueChange",change:"change"},features:[a["\u0275\u0275ProvidersFeature"]([{provide:r.t,useExisting:Object(a.forwardRef)(()=>e),multi:!0}]),a["\u0275\u0275InheritDefinitionFeature"],a["\u0275\u0275NgOnChangesFeature"]],decls:11,vars:11,consts:[["appearance","outline"],[3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value"],[3,"value","click",4,"ngFor","ngForOf"],[3,"value","click"]],template:function(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"mat-form-field",0),a["\u0275\u0275elementStart"](1,"mat-label"),a["\u0275\u0275text"](2),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](3,"mat-select",1,2),a["\u0275\u0275elementStart"](5,"mat-option"),a["\u0275\u0275element"](6,"ngx-mat-select-search",3),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](7,"mat-option",4),a["\u0275\u0275text"](8,"None"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](9,b,2,2,"mat-option",5),a["\u0275\u0275pipe"](10,"async"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e&&(a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate"](t.placeholder),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("value",null),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("ngForOf",a["\u0275\u0275pipeBind1"](10,9,t.filteredList)))},directives:[u.c,u.g,m.c,r.v,r.k,r.F,g.p,v.a,l.NgForOf],pipes:[l.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}"]}),e})();var S=i("Kj3r"),w=i("F97M"),C=i("XVR1"),O=i("qFsG"),x=i("/1cH"),D=i("NFeN");function M(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"mat-label"),a["\u0275\u0275text"](1),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"]();a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate"](e.label)}}function E(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"mat-option",7),a["\u0275\u0275elementStart"](1,"small"),a["\u0275\u0275text"](2),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,i=a["\u0275\u0275nextContext"]();a["\u0275\u0275property"]("hidden",!i.isAutocomplete)("value",e),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate2"]("",e.displayName," | ",e.mail,"")}}let F=(()=>{class e extends d.a{constructor(e,t){super(),this.graphApi=e,this._AppShareService=t,this.userSearchSubject=new p.b,this.isAutocomplete=!1,this.Formwidth="",this.showUserList=new a.EventEmitter,this.selectedUser=new a.EventEmitter,this.label="",this.blur=new a.EventEmitter,this.required=!1,this.fieldCtrl=new r.j,this.disabled=!1,this.isGraphApi=0,this._onDestroy=new p.b}ngOnInit(){this.userSearchSubject.pipe(Object(S.a)(600)).subscribe(e=>Object(n.c)(this,void 0,void 0,(function*(){if(console.log(e),this.isGraphApi)this.graphApi.getUserSuggestions(e).then(t=>Object(n.c)(this,void 0,void 0,(function*(){if(t&&t.length>0)this.showUserList.emit(t);else{let t=yield this.getUserSuggestionsFromDB(e);this.showUserList.emit(t)}})),t=>Object(n.c)(this,void 0,void 0,(function*(){console.log(t);let i=yield this.getUserSuggestionsFromDB(e);this.showUserList.emit(i)})));else{let t=yield this.getUserSuggestionsFromDB(e);this.showUserList.emit(t)}}))),this.fieldCtrl.valueChanges.pipe(Object(h.a)(this._onDestroy)).subscribe(e=>{console.log(e),e?(this.value=e.id,this.onChange(e.id)):(this.value="",this.onChange(""))})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}ngAfterViewInit(){}searchUser(e){if(!e)return this.graphApi.userSuggestions=[];this.userSearchSubject.next(e)}resetSuggestion(){this.graphApi.userSuggestions=[]}selectedOption(e){this.selectedUser.emit(e.option.value),this.value=e.option.value,this.blur.emit()}displayUserName(e){return e?e.displayName:""}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){return Object(n.c)(this,void 0,void 0,(function*(){if(console.log("inisde writevalue"),console.log(e),e)if(this.isGraphApi)this.graphApi.getUserProfile(e).then(t=>Object(n.c)(this,void 0,void 0,(function*(){if(t)this.fieldCtrl.setValue(t);else{let t=yield this.getUserProfileFromDB(e);this.fieldCtrl.setValue(t)}})));else{let t=yield this.getUserProfileFromDB(e);console.log(t),this.fieldCtrl.setValue(t)}else this.fieldCtrl.setValue("")}))}getUserSuggestionsFromDB(e){return Object(n.c)(this,void 0,void 0,(function*(){let t=yield this._AppShareService.getUserSuggestionsFromDB(e);return this.graphApi.userSuggestions=t.value,t.value}))}getUserProfileFromDB(e){return Object(n.c)(this,void 0,void 0,(function*(){let t=yield this._AppShareService.getUserProfileFromDB(e);return console.log(t),t&&t.length>0?t[0]:""}))}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275directiveInject"](w.a),a["\u0275\u0275directiveInject"](C.a))},e.\u0275cmp=a["\u0275\u0275defineComponent"]({type:e,selectors:[["app-search-user"]],inputs:{isAutocomplete:"isAutocomplete",Formwidth:"Formwidth",label:"label",required:"required",disabled:"disabled",isGraphApi:"isGraphApi"},outputs:{showUserList:"showUserList",selectedUser:"selectedUser",blur:"blur"},features:[a["\u0275\u0275ProvidersFeature"]([{provide:r.t,useExisting:Object(a.forwardRef)(()=>e),multi:!0}]),a["\u0275\u0275InheritDefinitionFeature"]],decls:9,vars:9,consts:[["appearance","outline","width","Formwidth"],[4,"ngIf"],["matInput","",3,"matAutocomplete","placeholder","required","formControl","disabled","keyup","focus"],["matSuffix","",2,"color","#66615B !important","font-size","21px !important"],[3,"hidden","displayWith","optionSelected"],["auto","matAutocomplete"],[3,"hidden","value",4,"ngFor","ngForOf"],[3,"hidden","value"]],template:function(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div"),a["\u0275\u0275elementStart"](1,"mat-form-field",0),a["\u0275\u0275template"](2,M,2,1,"mat-label",1),a["\u0275\u0275elementStart"](3,"input",2),a["\u0275\u0275listener"]("keyup",(function(e){return t.searchUser(e.target.value)}))("focus",(function(){return t.resetSuggestion()})),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](4,"mat-icon",3),a["\u0275\u0275text"](5,"person_pin"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](6,"mat-autocomplete",4,5),a["\u0275\u0275listener"]("optionSelected",(function(e){return t.selectedOption(e)})),a["\u0275\u0275template"](8,E,3,4,"mat-option",6),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275reference"](7);a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("ngIf",t.label),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("matAutocomplete",e)("placeholder",t.label)("required",t.required)("formControl",t.fieldCtrl)("disabled",t.disabled),a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("hidden",!t.isAutocomplete)("displayWith",t.displayUserName),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("ngForOf",t.graphApi.userSuggestions)}},directives:[u.c,l.NgIf,O.b,x.d,r.e,r.F,r.v,r.k,D.a,u.i,x.b,l.NgForOf,u.g,g.p],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding-bottom:8px!important}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-underline{bottom:0}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-infix{padding:.65em 0!important;border-top:.54375em solid transparent!important;font-size:13px}.mat-form-field[_ngcontent-%COMP%]     .mat-select-arrow{margin:0 4px!important}"]}),e})();var A=i("dNgK"),I=i("bTqV"),_=i("iadO"),k=(i("1yaQ"),i("9044")),j=i("tyNb"),T=i("Tzv/"),P=i("LcQX"),G=i("ihCf");function U(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"button",33),a["\u0275\u0275listener"]("click",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"]().createApproval()})),a["\u0275\u0275elementStart"](1,"mat-icon"),a["\u0275\u0275text"](2," done_all"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()}}function V(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"button",34),a["\u0275\u0275listener"]("click",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"]().updateApproval()})),a["\u0275\u0275elementStart"](1,"mat-icon"),a["\u0275\u0275text"](2," done_all"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()}}const B=function(e,t){return{"btn-active":e,"btn-not-active":t}};let N=(()=>{class e{constructor(e,t,i,s,l,c,p,d,h){this.fb=e,this.dialogRef=t,this.dialogData=i,this.dialog=s,this.opportunityService=l,this._route=c,this.snackBar=p,this.reloadService=d,this.utilityService=h,this.close=new a.EventEmitter,this.approvalForm=this.fb.group({phase:["",r.H.required],title:["",r.H.required],description:[""],predecessorActivityId:[""],governanceType:[""],salesGovernanceType:[""],dueOn:["",r.H.required],newDueOn:[""],organizer:["",r.H.required],applicationReferenceId:[parseInt(window.location.pathname.split("/")[5]),r.H.required]}),this.flag=!1,this.offset=o().local().utcOffset,this.checkWhetherDueOnTodaysDateOrTommorowsDate=e=>{let t=new Date(this.approvalForm.value.dueOn).getFullYear(),i=new Date(this.approvalForm.value.dueOn).getDate(),n=new Date(this.approvalForm.value.dueOn).getMonth();if("today"==e){if(this.approvalForm.value.dueOn){let e=(new Date).getFullYear();return i+"-"+n+"-"+t==(new Date).getDate()+"-"+(new Date).getMonth()+"-"+e}return!1}if("tommorrow"==e){if(this.approvalForm.value.dueOn){const e=new Date,a=new Date(e);a.setDate(a.getDate()+1);let r=a.getFullYear();return i+"-"+n+"-"+t==a.getDate()+"-"+a.getMonth()+"-"+r}return!1}},this.fillUpDueOn=e=>{if("today"===e)this.approvalForm.patchValue({dueOn:new Date});else if("tommorow"===e){const e=new Date,t=new Date(e);t.setDate(t.getDate()+1),this.approvalForm.patchValue({dueOn:t})}},this.updateApproval=()=>{"Edit"==this.mode&&this.approvalForm.valid?(console.log("***************DATE"),console.log(this.approvalForm.value),o(this.approvalForm.get("dueOn").value).isSame(o(this.approvalForm.get("newDueOn").value))?this.opportunityService.updateApproval(this.activityId,this.approvalForm.value,!1).subscribe(e=>{console.log(e),this.snackBar.open("Approval edited successfully!","Dismiss",{duration:2e3}),this.approvalForm.reset(),this.close.emit("close"),this.dialogRef.close("update required")},e=>{this.snackBar.open("Updation failed!","Dismiss",{duration:2e3})}):this.utilityService.openConfirmationForUpdatingAllOpportunityActivities("Do you want to update dates for Dependant Activities?").then(e=>Object(n.c)(this,void 0,void 0,(function*(){"All"==e?(console.log(e),this.opportunityService.updateApproval(this.activityId,this.approvalForm.value,!0).subscribe(e=>{console.log(e),this.snackBar.open("Approval edited successfully!","Dismiss",{duration:2e3}),this.approvalForm.reset(),this.close.emit("close"),this.dialogRef.close("close"),this.reloadService.sendNotification("reload")},e=>{this.snackBar.open("Updation failed!","Dismiss",{duration:2e3})})):"Single"==e&&(console.log(e),this.opportunityService.updateApproval(this.activityId,this.approvalForm.value,!1).subscribe(e=>{console.log(e),this.snackBar.open("Approval edited successfully!","Dismiss",{duration:2e3}),this.approvalForm.reset(),this.close.emit("close"),this.dialogRef.close("update required")},e=>{this.snackBar.open("Updation failed!","Dismiss",{duration:2e3})}))})))):this.snackBar.open("Enter all mandatory fields!","Dismiss",{duration:2e3})},this.getAllActivityList=()=>new Promise((e,t)=>{console.log(this.opportunityId,this.flag,this.activityId),this.opportunityService.getAllActivityList(this.opportunityId,this.flag,this.activityId).subscribe(t=>{console.log(this.activityList),this.activityList=t,e(t)},e=>{throw console.error(e),e})}),this.getPhase=()=>new Promise((e,t)=>{this.opportunityService.getActivityPhase(this.opportunityId).subscribe(t=>{e(t)},e=>{throw console.error(e),e})}),this.getGovernanceTypes=()=>new Promise((e,t)=>{this.opportunityService.getGovernanceTypes(66).then(t=>{e(t)},e=>{throw console.error(e),e})}),this.getSalesGovernanceTypes=()=>new Promise((e,t)=>{this.opportunityService.getSalesGovernanceTypes(72).then(t=>{this.salesGovernanceTypes=t,e(t)},e=>{throw console.error(e),e})}),this.getMarketingGovernanceTypes=()=>new Promise((e,t)=>{this.opportunityService.getSalesGovernanceTypes(74).then(t=>{this.marketingGovernanceTypes=t,e(t)},e=>{throw console.error(e),e})})}ngOnInit(){return Object(n.c)(this,void 0,void 0,(function*(){this.mode=this.dialogData.mode,this.activityId=this.dialogData.activityId,this.data=this.dialogData.data,console.log(this.data),console.log(this.activityId),this.opportunityId=parseInt(window.location.pathname.split("/")[5]),console.log(window.location.pathname),console.log(window.location.pathname.split("/")[5]),this.phase=yield this.getPhase(),this.governanceTypes=yield this.getGovernanceTypes(),this.salesGovernanceTypes=yield this.getSalesGovernanceTypes(),this.marketingGovernanceTypes=this.getMarketingGovernanceTypes(),this.activityList=yield this.getAllActivityList(),"Edit"==this.mode?(this.opportunityId=parseInt(this.data.application_reference_id),this.flag=!0,this.activityId=this.data.activity_id,this.activityList=yield this.getAllActivityList(),this.phase=yield this.getPhase(),console.log(this.phase),this.approvalForm.patchValue({phase:parseInt(this.data.phase_id),title:this.data.title,description:this.data.description,dueOn:o(this.data.task_due_date),newDueOn:o(this.data.task_due_date),governanceType:this.data.governance_activity_id,salesGovernanceType:this.data.governance_activity_id.toString(),organizer:this.data.assigned_to,predecessorActivityId:this.data.predecessor_id,applicationReferenceId:this.opportunityId})):(this.flag=!1,this.approvalForm.reset())}))}ngOnChanges(){return Object(n.c)(this,void 0,void 0,(function*(){}))}createApproval(){console.log(this.opportunityId),this.approvalForm.patchValue({applicationReferenceId:parseInt(window.location.pathname.split("/")[5])}),this.approvalForm.valid?this.opportunityService.createApprovalActivity(this.approvalForm.value,this.opportunityId).subscribe(e=>{console.log(e),this.snackBar.open("Approval created successfully!","Dismiss",{duration:2e3}),this.approvalForm.reset(),this.close.emit("close"),this.dialogRef.close("update required"),this.reloadService.sendNotification("reload")},e=>{console.log(e),this.snackBar.open("Approval creation failed!","Dismiss",{duration:2e3})}):this.snackBar.open("Enter all mandatory fields!","Dismiss",{duration:2e3})}closeDialog(){this.dialogRef.close()}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275directiveInject"](r.i),a["\u0275\u0275directiveInject"](s.h),a["\u0275\u0275directiveInject"](s.a),a["\u0275\u0275directiveInject"](s.b),a["\u0275\u0275directiveInject"](k.a),a["\u0275\u0275directiveInject"](j.a),a["\u0275\u0275directiveInject"](A.a),a["\u0275\u0275directiveInject"](T.a),a["\u0275\u0275directiveInject"](P.a))},e.\u0275cmp=a["\u0275\u0275defineComponent"]({type:e,selectors:[["app-approval-creation"]],outputs:{close:"close"},features:[a["\u0275\u0275NgOnChangesFeature"]],decls:57,vars:19,consts:[[3,"formGroup"],[1,"container","createMailStyles"],[1,"row","border-bottom","solid"],[1,"col-11","pt-2","createAccount"],[1,"col-1","d-flex"],["mat-icon-button","",1,"ml-auto","close-button",3,"click"],[1,"close-Icon"],[1,"row",2,"margin-top","10px"],[1,"col-12"],[1,"row"],[1,"col-9","pl-0"],["required","true","placeholder","Phase","formControlName","phase",1,"create-account-field",3,"list"],["placeholder","Presales Governance Activity","formControlName","governanceType",1,"create-account-field",3,"list"],["placeholder","Sales Governance Activity","formControlName","governanceType",1,"create-account-field",3,"list"],["placeholder","Marketing Governance Activity","formControlName","governanceType",1,"create-account-field",3,"list"],["appearance","outline",1,"create-account-field"],["matInput","","placeholder","Title *","formControlName","title"],["appearance","outline",2,"width","100%","font-size","14px !important"],["matInput","","cdkTextareaAutosize","","cdkAutosizeMinRows","3","cdkAutosizeMaxRows","","placeholder","Description","formControlName","description"],["autosize","cdkTextareaAutosize"],[1,"col-5","pl-0"],["matInput","","required","true","formControlName","dueOn",3,"matDatepicker"],["matSuffix","",3,"for"],["picker0",""],[1,"col-4","ml-2","pt-2"],["mat-raised-button","","color","primary",2,"margin-left","2%",3,"ngClass","click"],[1,"col-9","pl-0","organizer"],["label","Owner","required","true","formControlName","organizer",3,"isAutocomplete"],[1,"row","pt-5"],[1,"col-9","pr-5","quotes"],[1,"col-3","pl-0"],["mat-icon-button","","class","iconbtn ml-2 mt-0","matTooltip","Create approval","type","submit",3,"click",4,"ngIf"],["mat-icon-button","","class","iconbtn ml-2 mt-0","matTooltip","update Approval","type","submit",3,"click",4,"ngIf"],["mat-icon-button","","matTooltip","Create approval","type","submit",1,"iconbtn","ml-2","mt-0",3,"click"],["mat-icon-button","","matTooltip","update Approval","type","submit",1,"iconbtn","ml-2","mt-0",3,"click"]],template:function(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"form",0),a["\u0275\u0275elementStart"](1,"div",1),a["\u0275\u0275elementStart"](2,"div",2),a["\u0275\u0275elementStart"](3,"div",3),a["\u0275\u0275text"](4),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](5,"div",4),a["\u0275\u0275elementStart"](6,"button",5),a["\u0275\u0275listener"]("click",(function(){return t.closeDialog()})),a["\u0275\u0275elementStart"](7,"mat-icon",6),a["\u0275\u0275text"](8,"close"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](9,"div",7),a["\u0275\u0275elementStart"](10,"div",8),a["\u0275\u0275elementStart"](11,"div",9),a["\u0275\u0275elementStart"](12,"div",10),a["\u0275\u0275element"](13,"app-input-search",11),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](14,"div",9),a["\u0275\u0275elementStart"](15,"div",10),a["\u0275\u0275element"](16,"app-input-search",12),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](17,"div",9),a["\u0275\u0275elementStart"](18,"div",10),a["\u0275\u0275element"](19,"app-input-search",13),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](20,"div",9),a["\u0275\u0275elementStart"](21,"div",10),a["\u0275\u0275element"](22,"app-input-search",14),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](23,"div",9),a["\u0275\u0275elementStart"](24,"div",10),a["\u0275\u0275elementStart"](25,"mat-form-field",15),a["\u0275\u0275elementStart"](26,"mat-label"),a["\u0275\u0275text"](27,"Title"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275element"](28,"input",16),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](29,"div",9),a["\u0275\u0275elementStart"](30,"div",10),a["\u0275\u0275elementStart"](31,"mat-form-field",17),a["\u0275\u0275element"](32,"textarea",18,19),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](34,"div",9),a["\u0275\u0275elementStart"](35,"div",20),a["\u0275\u0275elementStart"](36,"mat-form-field",15),a["\u0275\u0275elementStart"](37,"mat-label"),a["\u0275\u0275text"](38,"Due on"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275element"](39,"input",21),a["\u0275\u0275element"](40,"mat-datepicker-toggle",22),a["\u0275\u0275element"](41,"mat-datepicker",null,23),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](43,"div",24),a["\u0275\u0275elementStart"](44,"button",25),a["\u0275\u0275listener"]("click",(function(){return t.fillUpDueOn("today")})),a["\u0275\u0275text"](45," Today"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](46,"button",25),a["\u0275\u0275listener"]("click",(function(){return t.fillUpDueOn("tommorow")})),a["\u0275\u0275text"](47," Tommorow"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](48,"div",9),a["\u0275\u0275elementStart"](49,"div",26),a["\u0275\u0275element"](50,"app-search-user",27),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](51,"div",28),a["\u0275\u0275elementStart"](52,"div",29),a["\u0275\u0275text"](53,' "What we dwell is, what we become" '),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](54,"div",30),a["\u0275\u0275template"](55,U,3,0,"button",31),a["\u0275\u0275template"](56,V,3,0,"button",32),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275reference"](42);a["\u0275\u0275property"]("formGroup",t.approvalForm),a["\u0275\u0275advance"](4),a["\u0275\u0275textInterpolate1"]("",t.mode," Approval "),a["\u0275\u0275advance"](9),a["\u0275\u0275property"]("list",t.phase),a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("list",t.governanceTypes),a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("list",t.salesGovernanceTypes),a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("list",t.marketingGovernanceTypes),a["\u0275\u0275advance"](17),a["\u0275\u0275property"]("matDatepicker",e),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("for",e),a["\u0275\u0275advance"](4),a["\u0275\u0275property"]("ngClass",a["\u0275\u0275pureFunction2"](13,B,1==t.checkWhetherDueOnTodaysDateOrTommorowsDate("today"),0==t.checkWhetherDueOnTodaysDateOrTommorowsDate("today"))),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("ngClass",a["\u0275\u0275pureFunction2"](16,B,1==t.checkWhetherDueOnTodaysDateOrTommorowsDate("tommorrow"),0==t.checkWhetherDueOnTodaysDateOrTommorowsDate("tommorrow"))),a["\u0275\u0275advance"](4),a["\u0275\u0275property"]("isAutocomplete",!0),a["\u0275\u0275advance"](5),a["\u0275\u0275property"]("ngIf","Edit"!=t.mode),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf","Edit"==t.mode)}},directives:function(){return[r.J,r.w,r.n,I.a,D.a,y,r.F,r.v,r.l,u.c,u.g,O.b,r.e,G.b,_.g,_.i,u.i,_.f,l.NgClass,F,l.NgIf]},styles:[".createMailStyles[_ngcontent-%COMP%]{background-image:url(approvers_bg.46d8dd6ded673479012f.png);background-size:210px 181px;background-repeat:no-repeat;overflow-y:hidden!important;background-position:100% 60%;min-height:95vh}.createMailStyles[_ngcontent-%COMP%]   .side-title[_ngcontent-%COMP%]{color:#cf0001;font-size:14px}.createMailStyles[_ngcontent-%COMP%]   .field-invalid[_ngcontent-%COMP%]{font-size:small}.createMailStyles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff;font-weight:400;font-size:13px!important;min-width:60px;line-height:33px;padding:0 8px;border-radius:4px;margin-right:6px!important;margin-bottom:3px}.createMailStyles[_ngcontent-%COMP%]   .organizer[_ngcontent-%COMP%]     .mat-form-field-wrapper .mat-form-field-flex{height:42px}.createMailStyles[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]{margin-right:6px!important;margin-bottom:3px;color:rgba(0,0,0,.534);background-color:rgba(194,193,193,.24);font-weight:400;font-size:13px!important;min-width:60px;line-height:33px;padding:0 8px;border-radius:4px}.createMailStyles[_ngcontent-%COMP%]   .createAccount[_ngcontent-%COMP%]{font-size:14px;color:#cf0001;font-weight:500}.createMailStyles[_ngcontent-%COMP%]   .create-account-field[_ngcontent-%COMP%]{font-size:14px!important;width:100%!important;color:#66615b}.createMailStyles[_ngcontent-%COMP%]   .inputSearch[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-flex{padding:0 .75em;margin-top:-.25em;position:relative;height:41px;width:204px}.createMailStyles[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.createMailStyles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}.createMailStyles[_ngcontent-%COMP%]   .iconbtn[_ngcontent-%COMP%]{background-color:#c92020;color:#fff;padding:0;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.createMailStyles[_ngcontent-%COMP%]   .check-box-text[_ngcontent-%COMP%]{color:#66615b;font-size:14px}.createMailStyles[_ngcontent-%COMP%]   .vip-contact[_ngcontent-%COMP%]{color:#1a1a1a;font-size:14px}.createMailStyles[_ngcontent-%COMP%]   .quotes[_ngcontent-%COMP%]{font-style:italic;font-weight:500;color:#66615b;font-size:14px}.createMailStyles[_ngcontent-%COMP%]   .textArea[_ngcontent-%COMP%]{color:#66615b}"]}),e})()},"Tzv/":function(e,t,i){"use strict";i.d(t,"a",(function(){return s}));var n=i("mrSG"),a=i("XNiG"),r=i("2Vo4"),o=i("fXoL");let s=(()=>{class e{constructor(){this.reloadSubject=new a.b,this.pipelineSubject=new a.b,this.searchRetainSubject=new r.a(""),this.searchData=this.searchRetainSubject.asObservable(),this.salesGovernanceTypeSubject=new r.a([]),this.salesReportGovernanceTypeSubject=new r.a([]),this.salesProposalTypeSubject=new a.b,this.bidManagerSearchSubject=new r.a(""),this.globalBidManagerSearchData=this.bidManagerSearchSubject.asObservable(),this.bidManagerSearchClear=new r.a(!1),this.bidManagerSearchClearFlag=this.bidManagerSearchSubject.asObservable(),this.salesGovernanceSearchSubject=new r.a(""),this.bidManagerUserVariants=new r.a([]),this.refreshVariant=new a.b}sendNotification(e){console.log(e),this.reloadSubject.next(e)}getNotification(){return this.reloadSubject.asObservable()}sendPiplelineContent(e){console.log(e),this.pipelineSubject.next(e)}getPiplelineContent(){return this.pipelineSubject.asObservable()}getRetainSearchContent(){return this.searchData}sendRetainSearchContent(e){console.log(e),this.searchRetainSubject.next(e)}sendGovTypes(e){console.log(e),this.salesGovernanceTypeSubject.next(e)}getGovTypes(){return this.salesGovernanceTypeSubject.asObservable()}getGovTypesValue(){return Object(n.c)(this,void 0,void 0,(function*(){return this.salesGovernanceTypeSubject.getValue()}))}sendSalesGovTypes(e){console.log(e),this.salesReportGovernanceTypeSubject.next(e)}getSalesGovTypes(){return this.salesReportGovernanceTypeSubject.asObservable()}getSalesGovTypesValue(){return Object(n.c)(this,void 0,void 0,(function*(){return this.salesReportGovernanceTypeSubject.getValue()}))}sendProposalFilter(e){console.log(e),this.pipelineSubject.next(e)}getProposalFilter(){return this.pipelineSubject.asObservable()}sendBidManagerSearch(e){console.log(e),this.bidManagerSearchSubject.next(e)}getBidManagerSearchValue(){return this.globalBidManagerSearchData}sendClearBidManagerSearch(e){this.bidManagerSearchClear.next(e)}getClearBidManagerSearch(){return this.bidManagerSearchClear.asObservable()}getClearBidManagerSearchValue(){return this.bidManagerSearchClear.getValue()}sendBidManagerUserVariants(e){this.bidManagerUserVariants.next(e)}getBidManagerUserVariants(){return this.bidManagerUserVariants.asObservable()}refreshBidManagerUserVariants(){return this.refreshVariant.asObservable()}sendRefreshFlag(e){this.refreshVariant.next(e)}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275prov=o["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()}}]);