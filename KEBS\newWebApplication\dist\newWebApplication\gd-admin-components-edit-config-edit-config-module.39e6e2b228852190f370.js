(window.webpackJsonp=window.webpackJsonp||[]).push([[720,634,858],{DnxM:function(e,t,n){"use strict";n.r(t),n.d(t,"EditConfigModule",(function(){return re}));var i=n("ofXK"),o=n("XhcP"),r=n("kmnG"),a=n("/1cH"),s=n("3Pt+"),l=n("NFeN"),d=n("qFsG"),c=n("d3UM"),m=n("+0xr"),p=n("f0Cb"),h=n("Xa2L"),g=n("Qu3c"),u=n("STbY"),f=n("Xi0T"),b=n("dlKe"),v=n("tyNb"),x=n("mrSG"),C=n("XNiG"),w=n("1G5W"),S=n("xG9w"),_=n("GUC0"),y=n.n(_),O=n("fXoL"),E=n("ye8K"),M=n("1A3m"),P=n("0IaG"),D=n("XXEo"),L=n("FKr1"),U=n("Kj3r"),k=n("NJ67"),j=n("F97M");function N(e,t){if(1&e&&(O["\u0275\u0275elementStart"](0,"mat-label"),O["\u0275\u0275text"](1),O["\u0275\u0275elementEnd"]()),2&e){const e=O["\u0275\u0275nextContext"]();O["\u0275\u0275advance"](1),O["\u0275\u0275textInterpolate"](e.label)}}function F(e,t){if(1&e&&(O["\u0275\u0275elementStart"](0,"mat-option",7),O["\u0275\u0275elementStart"](1,"small"),O["\u0275\u0275text"](2),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=O["\u0275\u0275nextContext"]();O["\u0275\u0275property"]("hidden",!n.isAutocomplete)("value",e),O["\u0275\u0275advance"](2),O["\u0275\u0275textInterpolate2"]("",e.displayName," | ",e.mail,"")}}let A=(()=>{class e extends k.a{constructor(e,t){super(),this.graphApi=e,this._apiService=t,this.userSearchSubject=new C.b,this.isAutocomplete=!1,this.Formwidth="",this.showUserList=new O.EventEmitter,this.selectedUser=new O.EventEmitter,this.label="",this.blur=new O.EventEmitter,this.required=!1,this.vcoe_flag=!1,this.fieldCtrl=new s.j,this.disabled=!1,this.isGraphApi=0,this._onDestroy=new C.b}ngOnInit(){this.userSearchSubject.pipe(Object(U.a)(600)).subscribe(e=>Object(x.c)(this,void 0,void 0,(function*(){let t=yield this.getUserSuggestionsFromDB(e,this.vcoe_flag);console.log(t),this.showUserList.emit(t)}))),this.fieldCtrl.valueChanges.pipe(Object(w.a)(this._onDestroy)).subscribe(e=>{e?(this.value=e.id,this.onChange(e.id)):(this.value="",this.onChange(""))})}ngOnChanges(){return Object(x.c)(this,void 0,void 0,(function*(){if(this.vcoe_flag){this.onChange("");let e=yield this.getUserSuggestionsFromDB("",this.vcoe_flag);this.showUserList.emit(e)}}))}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}ngAfterViewInit(){}searchUser(e){if(!e)return this.graphApi.userSuggestions=[];this.userSearchSubject.next(e)}resetSuggestion(){this.graphApi.userSuggestions=[]}selectedOption(e){this.selectedUser.emit(e.option.value),this.value=e.option.value,this.blur.emit()}displayUserName(e){return e?e.displayName:""}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}getUserSuggestionsFromDB(e,t){return Object(x.c)(this,void 0,void 0,(function*(){let n=yield this._apiService.getUserSuggestionsFromDB(e,t);return this.graphApi.userSuggestions=n.data,n.data}))}}return e.\u0275fac=function(t){return new(t||e)(O["\u0275\u0275directiveInject"](j.a),O["\u0275\u0275directiveInject"](E.a))},e.\u0275cmp=O["\u0275\u0275defineComponent"]({type:e,selectors:[["app-user-search"]],inputs:{isAutocomplete:"isAutocomplete",Formwidth:"Formwidth",label:"label",required:"required",vcoe_flag:"vcoe_flag",disabled:"disabled",isGraphApi:"isGraphApi"},outputs:{showUserList:"showUserList",selectedUser:"selectedUser",blur:"blur"},features:[O["\u0275\u0275ProvidersFeature"]([{provide:s.t,useExisting:Object(O.forwardRef)(()=>e),multi:!0}]),O["\u0275\u0275InheritDefinitionFeature"],O["\u0275\u0275NgOnChangesFeature"]],decls:9,vars:9,consts:[["appearance","outline","width","Formwidth"],[4,"ngIf"],["matInput","",3,"matAutocomplete","placeholder","required","formControl","disabled","keyup","focus"],["matSuffix","",2,"color","#66615B !important","font-size","21px !important"],[3,"hidden","displayWith","optionSelected"],["auto","matAutocomplete"],[3,"hidden","value",4,"ngFor","ngForOf"],[3,"hidden","value"]],template:function(e,t){if(1&e&&(O["\u0275\u0275elementStart"](0,"div"),O["\u0275\u0275elementStart"](1,"mat-form-field",0),O["\u0275\u0275template"](2,N,2,1,"mat-label",1),O["\u0275\u0275elementStart"](3,"input",2),O["\u0275\u0275listener"]("keyup",(function(e){return t.searchUser(e.target.value)}))("focus",(function(){return t.resetSuggestion()})),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementStart"](4,"mat-icon",3),O["\u0275\u0275text"](5,"person_pin"),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementStart"](6,"mat-autocomplete",4,5),O["\u0275\u0275listener"]("optionSelected",(function(e){return t.selectedOption(e)})),O["\u0275\u0275template"](8,F,3,4,"mat-option",6),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementEnd"]()),2&e){const e=O["\u0275\u0275reference"](7);O["\u0275\u0275advance"](2),O["\u0275\u0275property"]("ngIf",t.label),O["\u0275\u0275advance"](1),O["\u0275\u0275property"]("matAutocomplete",e)("placeholder",t.label)("required",t.required)("formControl",t.fieldCtrl)("disabled",t.disabled),O["\u0275\u0275advance"](3),O["\u0275\u0275property"]("hidden",!t.isAutocomplete)("displayWith",t.displayUserName),O["\u0275\u0275advance"](2),O["\u0275\u0275property"]("ngForOf",t.graphApi.userSuggestions)}},directives:[r.c,i.NgIf,d.b,a.d,s.e,s.F,s.v,s.k,l.a,r.i,a.b,i.NgForOf,r.g,L.p],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding-bottom:8px!important}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-underline{bottom:0}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-infix{padding:.65em 0!important;border-top:.54375em solid transparent!important;font-size:13px}.mat-form-field[_ngcontent-%COMP%]     .mat-select-arrow{margin:0 4px!important}"]}),e})();var I=n("me71");let T=(()=>{class e{constructor(e){this._api=e,this.removeClicked=new O.EventEmitter,this.updateClicked=new O.EventEmitter}ngOnInit(){}removeMember(){this.removeClicked.emit()}updateMember(e){this.updateClicked.emit(e)}}return e.\u0275fac=function(t){return new(t||e)(O["\u0275\u0275directiveInject"](E.a))},e.\u0275cmp=O["\u0275\u0275defineComponent"]({type:e,selectors:[["app-user-profile-detail"]],inputs:{user:"user",memberList:["membersList","memberList"]},outputs:{removeClicked:"removeClicked",updateClicked:"updateClicked"},decls:11,vars:4,consts:[[1,"card","card-design"],[1,"card-body",2,"padding","6px !important"],[1,"row","justify-content-end",2,"margin","-7px !important"],[1,"row","justify-content-center",2,"margin-top","28px"],["imgWidth","55px","imgHeight","55px",3,"id"],[1,"row","justify-content-center","pt-1",3,"matTooltip"],[1,"username"],[1,"row","justify-content-center","pt-1"]],template:function(e,t){1&e&&(O["\u0275\u0275elementStart"](0,"div",0),O["\u0275\u0275elementStart"](1,"div",1),O["\u0275\u0275element"](2,"div",2),O["\u0275\u0275elementStart"](3,"div",3),O["\u0275\u0275element"](4,"app-user-image",4),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementStart"](5,"div",5),O["\u0275\u0275elementStart"](6,"span",6),O["\u0275\u0275text"](7),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementStart"](8,"div",7),O["\u0275\u0275elementStart"](9,"span",6),O["\u0275\u0275text"](10),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementEnd"]()),2&e&&(O["\u0275\u0275advance"](4),O["\u0275\u0275property"]("id",t.memberList.created_by),O["\u0275\u0275advance"](1),O["\u0275\u0275property"]("matTooltip",t.memberList.displayName),O["\u0275\u0275advance"](2),O["\u0275\u0275textInterpolate"](t.memberList.displayName),O["\u0275\u0275advance"](3),O["\u0275\u0275textInterpolate"](t.memberList.description))},directives:[I.a,g.a],styles:[".card-design[_ngcontent-%COMP%]{max-width:160x!important;min-width:160px!important;min-height:155px!important;max-height:155px!important;padding-left:2px!important}.data-label[_ngcontent-%COMP%]{font-size:14px!important}.header[_ngcontent-%COMP%]{color:#5f5f5f;font-size:11px!important}.text-overflow[_ngcontent-%COMP%]{width:81%}.text-overflow[_ngcontent-%COMP%], .text-overflow-expense[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.text-overflow-expense[_ngcontent-%COMP%]{padding-top:5px;width:90%;font-size:13px!important}.username[_ngcontent-%COMP%]{color:#1e2733;font-size:14px!important}.text-overflow-name[_ngcontent-%COMP%], .username[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.filter-user-name[_ngcontent-%COMP%]{padding-top:2px!important}"]}),e})();const R=["drawer"];function z(e,t){if(1&e&&(O["\u0275\u0275elementStart"](0,"mat-option",37),O["\u0275\u0275text"](1),O["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;O["\u0275\u0275property"]("value",e.id),O["\u0275\u0275advance"](1),O["\u0275\u0275textInterpolate1"](" ",e.description," ")}}function B(e,t){1&e&&(O["\u0275\u0275elementStart"](0,"th",38),O["\u0275\u0275text"](1,"Name"),O["\u0275\u0275elementEnd"]())}function V(e,t){if(1&e&&(O["\u0275\u0275elementStart"](0,"td",39),O["\u0275\u0275text"](1),O["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;O["\u0275\u0275advance"](1),O["\u0275\u0275textInterpolate1"](" ",e.displayName," ")}}function $(e,t){1&e&&(O["\u0275\u0275elementStart"](0,"th",38),O["\u0275\u0275text"](1,"DB Name"),O["\u0275\u0275elementEnd"]())}function K(e,t){if(1&e&&(O["\u0275\u0275elementStart"](0,"mat-option",37),O["\u0275\u0275text"](1),O["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;O["\u0275\u0275property"]("value",e.dbname),O["\u0275\u0275advance"](1),O["\u0275\u0275textInterpolate1"](" ",e.dbname," ")}}function q(e,t){if(1&e){const e=O["\u0275\u0275getCurrentView"]();O["\u0275\u0275elementStart"](0,"td",39),O["\u0275\u0275elementStart"](1,"mat-form-field",5),O["\u0275\u0275elementStart"](2,"mat-label"),O["\u0275\u0275text"](3,"DB Name"),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementStart"](4,"mat-select",40),O["\u0275\u0275listener"]("ngModelChange",(function(e){return t.$implicit.dbname=e}))("selectionChange",(function(n){O["\u0275\u0275restoreView"](e);const i=t.$implicit,o=O["\u0275\u0275nextContext"]();return o.selectedDB(n,i,o.i)})),O["\u0275\u0275elementStart"](5,"mat-option"),O["\u0275\u0275text"](6,"--"),O["\u0275\u0275elementEnd"](),O["\u0275\u0275template"](7,K,2,2,"mat-option",7),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=O["\u0275\u0275nextContext"]();O["\u0275\u0275advance"](4),O["\u0275\u0275property"]("ngModel",e.dbname),O["\u0275\u0275advance"](3),O["\u0275\u0275property"]("ngForOf",n.dbNameList)}}function G(e,t){1&e&&(O["\u0275\u0275elementStart"](0,"th",41),O["\u0275\u0275text"](1,"Table Name"),O["\u0275\u0275elementEnd"]())}function H(e,t){if(1&e&&(O["\u0275\u0275elementStart"](0,"mat-option",37),O["\u0275\u0275text"](1),O["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;O["\u0275\u0275property"]("value",e.table_name_1),O["\u0275\u0275advance"](1),O["\u0275\u0275textInterpolate1"](" ",e.table_name_1," ")}}function W(e,t){if(1&e){const e=O["\u0275\u0275getCurrentView"]();O["\u0275\u0275elementStart"](0,"td",42),O["\u0275\u0275elementStart"](1,"mat-form-field",5),O["\u0275\u0275elementStart"](2,"mat-label"),O["\u0275\u0275text"](3,"TABLE"),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementStart"](4,"mat-select",43),O["\u0275\u0275listener"]("ngModelChange",(function(e){return t.$implicit.tablename=e})),O["\u0275\u0275elementStart"](5,"div",44),O["\u0275\u0275elementStart"](6,"span",45),O["\u0275\u0275elementStart"](7,"mat-icon",46),O["\u0275\u0275text"](8,"search"),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementEnd"](),O["\u0275\u0275element"](9,"input",47),O["\u0275\u0275elementStart"](10,"span",48),O["\u0275\u0275listener"]("click",(function(){return O["\u0275\u0275restoreView"](e),O["\u0275\u0275nextContext"]().clearSearchValue()})),O["\u0275\u0275elementStart"](11,"mat-icon",49),O["\u0275\u0275text"](12,"highlight_off"),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementEnd"](),O["\u0275\u0275template"](13,H,2,2,"mat-option",7),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=O["\u0275\u0275nextContext"]();O["\u0275\u0275advance"](4),O["\u0275\u0275property"]("ngModel",e.tablename),O["\u0275\u0275advance"](5),O["\u0275\u0275property"]("formControl",n.searchField),O["\u0275\u0275advance"](4),O["\u0275\u0275property"]("ngForOf",n.searchTableNameList)}}function X(e,t){1&e&&O["\u0275\u0275element"](0,"th",50)}function Y(e,t){if(1&e){const e=O["\u0275\u0275getCurrentView"]();O["\u0275\u0275elementStart"](0,"td",51),O["\u0275\u0275elementStart"](1,"button",52),O["\u0275\u0275listener"]("click",(function(){O["\u0275\u0275restoreView"](e);const n=t.$implicit,i=O["\u0275\u0275nextContext"]();return i.addUsersList(n,i.i)})),O["\u0275\u0275text"](2," Add "),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;O["\u0275\u0275advance"](1),O["\u0275\u0275property"]("disabled",e.disable)}}function J(e,t){1&e&&O["\u0275\u0275element"](0,"tr",53)}function Q(e,t){1&e&&O["\u0275\u0275element"](0,"tr",54)}function Z(e,t){if(1&e){const e=O["\u0275\u0275getCurrentView"]();O["\u0275\u0275elementStart"](0,"app-user-profile-detail",55),O["\u0275\u0275listener"]("removeClicked",(function(){O["\u0275\u0275restoreView"](e);const n=t.$implicit;return O["\u0275\u0275nextContext"]().removeMember(n)})),O["\u0275\u0275elementEnd"]()}2&e&&O["\u0275\u0275property"]("membersList",t.$implicit)}function ee(e,t){if(1&e){const e=O["\u0275\u0275getCurrentView"]();O["\u0275\u0275elementStart"](0,"div"),O["\u0275\u0275elementStart"](1,"div",63),O["\u0275\u0275elementStart"](2,"div",64),O["\u0275\u0275text"](3),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementStart"](4,"div",64),O["\u0275\u0275text"](5),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementStart"](6,"div",65),O["\u0275\u0275text"](7),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementStart"](8,"div",64),O["\u0275\u0275text"](9),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementStart"](10,"div",64),O["\u0275\u0275elementStart"](11,"button",66),O["\u0275\u0275listener"]("click",(function(){O["\u0275\u0275restoreView"](e);const n=t.$implicit;return O["\u0275\u0275nextContext"](2).navigateToEdit(n)})),O["\u0275\u0275elementStart"](12,"mat-icon"),O["\u0275\u0275text"](13,"edit_note"),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementStart"](14,"button",66),O["\u0275\u0275listener"]("click",(function(){O["\u0275\u0275restoreView"](e);const n=t.$implicit;return O["\u0275\u0275nextContext"](2).navigateToDelete(n)})),O["\u0275\u0275elementStart"](15,"mat-icon"),O["\u0275\u0275text"](16,"delete"),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementEnd"](),O["\u0275\u0275element"](17,"mat-divider"),O["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;O["\u0275\u0275advance"](3),O["\u0275\u0275textInterpolate1"](" ",e.displayName," "),O["\u0275\u0275advance"](2),O["\u0275\u0275textInterpolate1"](" ",e.db_name," "),O["\u0275\u0275advance"](2),O["\u0275\u0275textInterpolate1"](" ",e.table_name_1," "),O["\u0275\u0275advance"](2),O["\u0275\u0275textInterpolate1"](" ",e.description," ")}}function te(e,t){if(1&e&&(O["\u0275\u0275elementStart"](0,"div",56),O["\u0275\u0275elementStart"](1,"div",57),O["\u0275\u0275elementStart"](2,"div",58),O["\u0275\u0275elementStart"](3,"div",59),O["\u0275\u0275elementStart"](4,"span",60),O["\u0275\u0275text"](5,"DISPLAY NAME"),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementStart"](6,"div",59),O["\u0275\u0275elementStart"](7,"span",60),O["\u0275\u0275text"](8,"DB NAME"),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementStart"](9,"div",4),O["\u0275\u0275elementStart"](10,"span",60),O["\u0275\u0275text"](11,"TABLE NAME"),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementStart"](12,"div",59),O["\u0275\u0275elementStart"](13,"span",60),O["\u0275\u0275text"](14,"ROLE"),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementStart"](15,"div",61),O["\u0275\u0275elementStart"](16,"div",33),O["\u0275\u0275template"](17,ee,18,4,"div",62),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementEnd"]()),2&e){const e=O["\u0275\u0275nextContext"]();O["\u0275\u0275advance"](17),O["\u0275\u0275property"]("ngForOf",e.usersDataList)}}function ne(e,t){1&e&&(O["\u0275\u0275elementStart"](0,"div",67),O["\u0275\u0275elementStart"](1,"div",68),O["\u0275\u0275element"](2,"mat-spinner",69),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementEnd"]())}const ie=[{path:"",component:(()=>{class e{constructor(e,t,n,i,o){this._apiService=e,this._toaster=t,this.matDialog=n,this._router=i,this.authService=o,this.searchUser=new s.j,this.searchField=new s.j,this.getadminRoles=0,this.getdbName="",this._onDestroy=new C.b,this.vcoe_flag=!1,this.adminRolesArray=[],this.usersList=[],this.totalUsers=[],this.dbNameList=[],this.usersDataList=[],this.usersAdminList=[],this.displayedColumns=["displayName","dbName","tableName","add"],this.tableNameList=[],this.searchTableNameList=[],this.isLoading=!0,this.onSaveClick=!1,this.currentUser={},this.setConsultantList=e=>{console.log(e),this.usersList=e;for(var t=0;t<this.usersList.length;t++)this.usersList.dbname="",this.usersList.tablename="",this.usersList.disable=!1}}ngOnInit(){return Object(x.c)(this,void 0,void 0,(function*(){this.currentUser=this.authService.getProfile().profile,console.log(this.currentUser),this.authKey=yield this.getAuthKey(),this.getUsersRolesData(),this.getDBnameFun(),console.log(this.getdbName),yield this.checkUserConfig(),this.getRoles(),this.searchField.valueChanges.pipe(Object(w.a)(this._onDestroy)).subscribe(e=>{this.filtertable(e||"")})}))}getRoles(){this._apiService.getAdminUserRoles(this.currentUser,this.authKey).pipe(Object(w.a)(this._onDestroy)).subscribe(e=>Object(x.c)(this,void 0,void 0,(function*(){this.adminRolesArray="S"==e.messType&&e.data.length>0?e.data:[]})),e=>{this._toaster.showError(e,"Error",3e3)})}addUsersList(e,t){console.log(this.getadminRoles),""!=e.dbname&&null!=e.dbname&&""!=e.tablename&&null!=e.tablename?(console.log(e),console.log(t),this.totalUsers.push(e),console.log(this.totalUsers),e.disable=!0):this._toaster.showWarning("Please Fill Mandatory Fields !","Warning")}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}selectedDB(e,t,n){console.log(e),console.log(t),console.log(n),this._apiService.getTableName(t.dbname).pipe(Object(w.a)(this._onDestroy)).subscribe(e=>Object(x.c)(this,void 0,void 0,(function*(){"S"==e.messType&&e.data&&e.data.length>0?(this.tableNameList=e.data,this.searchTableNameList=e.data,console.log(this.tableNameList)):this.tableNameList=[]})),e=>{console.log(e)})}dialogueClose(){this.searchUser.reset(),console.log(this.searchUser),this.usersList=[],this.totalUsers=[],this.getUsersRolesData()}onSave(){console.log(this.getadminRoles),null==this.getadminRoles||0==this.getadminRoles?this._toaster.showWarning("Please Fill User Role!","Warning"):(this.onSaveClick=!0,this._apiService.insertUserRoles(this.totalUsers,this.getadminRoles,this.currentUser,this.authKey).pipe(Object(w.a)(this._onDestroy)).subscribe(e=>Object(x.c)(this,void 0,void 0,(function*(){"S"==e.messType?(this.onSaveClick=!1,this._toaster.showSuccess("User roles inserted successfully !","Success",3e3),this.usersList=[],this.totalUsers=[],this.drawer.toggle(),this.searchUser.reset(),this.getUsersRolesData()):(this.onSaveClick=!1,this._toaster.showWarning(e.messText,"Warning"))})),e=>{this.onSaveClick=!1,this._toaster.showError(e,"Error",3e3)}))}getUsersRolesData(){this.isLoading=!0,console.log("oninit came"),this._apiService.getUserRoles(this.currentUser,this.authKey).pipe(Object(w.a)(this._onDestroy)).subscribe(e=>Object(x.c)(this,void 0,void 0,(function*(){"S"==e.messType&&e.data.length>0?(this.isLoading=!1,console.log(e.data),this.usersDataList=S.filter(e.data,(function(e){return 2==e.gd_admin_id||1==e.gd_admin_id})),this.usersAdminList=S.filter(e.data,(function(e){return 3==e.gd_admin_id})),console.log(this.usersDataList),console.log(this.usersAdminList)):(this.isLoading=!1,this.usersDataList=[],this.usersAdminList=[])})),e=>{this.isLoading=!1,this._toaster.showError(e,"Error",3e3)})}navigateToEdit(e){return Object(x.c)(this,void 0,void 0,(function*(){console.log(e),console.log(typeof e),e.currentUser=this.currentUser,e.authKey=this.authKey;const{EditUserConfigComponent:t}=yield n.e(888).then(n.bind(null,"90Cu"));this.matDialog.open(t,{height:"100%",width:"50%",position:{right:"0px"},data:e}).afterClosed().subscribe(e=>{this.getUsersRolesData()})}))}removeMember(e){return Object(x.c)(this,void 0,void 0,(function*(){const{EditUserConfigComponent:t}=yield n.e(888).then(n.bind(null,"90Cu"));this.matDialog.open(t,{height:"100%",width:"50%",position:{right:"0px"},data:e}).afterClosed().subscribe(e=>{this.getUsersRolesData()})}))}checkUserConfig(){return Object(x.c)(this,void 0,void 0,(function*(){this._apiService.checkConfigAccess().pipe(Object(w.a)(this._onDestroy)).subscribe(e=>Object(x.c)(this,void 0,void 0,(function*(){"S"!=e.messType&&this._router.navigateByUrl("main")})),e=>{this._router.navigateByUrl("main"),console.log(e)})}))}navigateToDelete(e){y()({title:"Are you sure?",text:"Once deleted, you will not be able to recover this imaginary file!",icon:"warning",dangerMode:!0}).then(t=>Object(x.c)(this,void 0,void 0,(function*(){t&&(yield this._apiService.deleteUserRoles(e.created_by,this.currentUser,this.authKey).pipe(Object(w.a)(this._onDestroy)).subscribe(e=>Object(x.c)(this,void 0,void 0,(function*(){"S"==e.messType?(y()("Deletion Successfull",{icon:"success"}),this.getUsersRolesData()):y()("Deletion Failed !")})),e=>{this._router.navigateByUrl("main"),console.log(e)}))})))}getDBnameFun(){this._apiService.getDbNameConfig(this.currentUser,this.authKey).pipe(Object(w.a)(this._onDestroy)).subscribe(e=>Object(x.c)(this,void 0,void 0,(function*(){"S"==e.messType?this.dbNameList.push({dbname:e.data}):this._toaster.showError(e.messText,"Error",3e3)})),e=>{this._toaster.showError(e,"Error",3e3),console.log(e)})}setFormValue(e){console.log(e)}clearSearchValue(){this.searchField.reset()}filtertable(e){let t=this.tableNameList.filter(t=>0===t.table_name_1.toLowerCase().indexOf(e.toLowerCase()));this.searchTableNameList=t}getAuthKey(){return new Promise((e,t)=>{this._apiService.getAuthingKey(this.currentUser).pipe(Object(w.a)(this._onDestroy)).subscribe(n=>Object(x.c)(this,void 0,void 0,(function*(){if("S"==n.messType&&n.data.length>0)return e(n.data);this._toaster.showError(n.messText,"Error",3e3),t()})),e=>{console.log(e),t()})})}navigateToSelectLogs(){this._router.navigateByUrl("main/gdadmin/home/<USER>")}}return e.\u0275fac=function(t){return new(t||e)(O["\u0275\u0275directiveInject"](E.a),O["\u0275\u0275directiveInject"](M.a),O["\u0275\u0275directiveInject"](P.b),O["\u0275\u0275directiveInject"](v.g),O["\u0275\u0275directiveInject"](D.a))},e.\u0275cmp=O["\u0275\u0275defineComponent"]({type:e,selectors:[["app-edit-config-landing-page"]],viewQuery:function(e,t){if(1&e&&O["\u0275\u0275viewQuery"](R,!0),2&e){let e;O["\u0275\u0275queryRefresh"](e=O["\u0275\u0275loadQuery"]())&&(t.drawer=e.first)}},decls:51,vars:12,consts:[["hasBackdrop","false",1,"drawer"],["mode","over","position","end",1,"side-drawer"],["drawer",""],[1,"row","pt-2",2,"border-bottom","rgba(0, 0, 0, 0.12) 1px solid !important","margin-left","5px !important","margin-right","5px !important"],[1,"col-3"],["appearance","outline"],["required","true",3,"ngModel","ngModelChange"],[3,"value",4,"ngFor","ngForOf"],[1,"col-4","ml-auto"],["label","Search User",3,"isAutocomplete","formControl","vcoe_flag","showUserList"],[1,"col-2","d-flex"],["mat-icon-button","","matTooltip","Close",1,"btn-fab","mx-auto",3,"click"],[2,"color","white !important","font-size","16px !important"],["mat-icon-button","","matTooltip","Save",1,"btn-fab","mx-auto",3,"disabled","click"],[1,"row"],[1,"col-lg-12","col-12"],["mat-table","",1,"consultanttable","w-100",3,"dataSource"],["matColumnDef","displayName"],["mat-header-cell","","class","p-3",4,"matHeaderCellDef"],["mat-cell","","class","p-3",4,"matCellDef"],["matColumnDef","dbName"],["matColumnDef","tableName"],["mat-header-cell","","class","py-3 pl-3 pr-0",4,"matHeaderCellDef"],["mat-cell","","class","py-3 pl-1 pr-0",4,"matCellDef"],["matColumnDef","add"],["mat-header-cell","","class","pr-3",4,"matHeaderCellDef"],["mat-cell","","class","pr-1",4,"matCellDef"],["mat-header-row","",4,"matHeaderRowDef"],["mat-row","",4,"matRowDef","matRowDefColumns"],[1,"row","tileName","pl-5","pt-2","pb-1"],[1,"ml-auto","pr-4"],["matTooltip","ViewSelectLogs",2,"border","none","background-color","transparent","margin-right","50px",3,"click"],["mat-button","",1,"addUser",3,"click"],[1,"col-12"],["class","col-2","type","medium","style","\n                        padding-left: 12px;padding-bottom: 12px;",3,"membersList","removeClicked",4,"ngFor","ngForOf"],["class","tableClass",4,"ngIf"],["class","row container d-flex mb-2 mt-6 flex-column horizontal-item-loading",4,"ngIf"],[3,"value"],["mat-header-cell","",1,"p-3"],["mat-cell","",1,"p-3"],["required","true",3,"ngModel","ngModelChange","selectionChange"],["mat-header-cell","",1,"py-3","pl-3","pr-0"],["mat-cell","",1,"py-3","pl-1","pr-0"],["multiple","","required","true",3,"ngModel","ngModelChange"],[2,"width","100%","border","none"],[1,"pl-1","icon-search"],["matPrefix",""],[1,"p-3","pl-0",2,"width","80%","border","none",3,"formControl"],[1,"icon-search-cancel",3,"click"],["matSuffix","",1,"mr-1"],["mat-header-cell","",1,"pr-3"],["mat-cell","",1,"pr-1"],["mat-raised-button","",1,"ml-2","btn",3,"disabled","click"],["mat-header-row",""],["mat-row",""],["type","medium",1,"col-2",2,"padding-left","12px","padding-bottom","12px",3,"membersList","removeClicked"],[1,"tableClass"],[1,"col-12","pt-2"],[1,"row","pb-2","pt-2","mt-3",2,"background-color","white","border-radius","5px"],[1,"col-2"],[1,"tableHeader"],[1,"landingTable"],[4,"ngFor","ngForOf"],[1,"row","pb-2","pt-3"],[1,"col-2","tableData"],[1,"col-3","tableData"],[1,"navigateButton",3,"click"],[1,"row","container","d-flex","mb-2","mt-6","flex-column","horizontal-item-loading"],[1,"row","justify-content-center"],["diameter","40","matTooltip","Loading ..."]],template:function(e,t){if(1&e){const e=O["\u0275\u0275getCurrentView"]();O["\u0275\u0275elementStart"](0,"mat-drawer-container",0),O["\u0275\u0275elementStart"](1,"mat-drawer",1,2),O["\u0275\u0275elementStart"](3,"div",3),O["\u0275\u0275elementStart"](4,"div",4),O["\u0275\u0275elementStart"](5,"mat-form-field",5),O["\u0275\u0275elementStart"](6,"mat-label"),O["\u0275\u0275text"](7,"Role"),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementStart"](8,"mat-select",6),O["\u0275\u0275listener"]("ngModelChange",(function(e){return t.getadminRoles=e})),O["\u0275\u0275elementStart"](9,"mat-option"),O["\u0275\u0275text"](10,"--"),O["\u0275\u0275elementEnd"](),O["\u0275\u0275template"](11,z,2,2,"mat-option",7),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementStart"](12,"div",8),O["\u0275\u0275elementStart"](13,"app-user-search",9),O["\u0275\u0275listener"]("showUserList",(function(e){return t.setConsultantList(e)})),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementStart"](14,"div",10),O["\u0275\u0275elementStart"](15,"button",11),O["\u0275\u0275listener"]("click",(function(){return O["\u0275\u0275restoreView"](e),O["\u0275\u0275reference"](2).toggle(),t.dialogueClose()})),O["\u0275\u0275elementStart"](16,"mat-icon",12),O["\u0275\u0275text"](17,"close"),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementStart"](18,"button",13),O["\u0275\u0275listener"]("click",(function(){return t.onSave()})),O["\u0275\u0275elementStart"](19,"mat-icon",12),O["\u0275\u0275text"](20,"done"),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementStart"](21,"div",14),O["\u0275\u0275elementStart"](22,"div",15),O["\u0275\u0275elementStart"](23,"table",16),O["\u0275\u0275elementContainerStart"](24,17),O["\u0275\u0275template"](25,B,2,0,"th",18),O["\u0275\u0275template"](26,V,2,1,"td",19),O["\u0275\u0275elementContainerEnd"](),O["\u0275\u0275elementContainerStart"](27,20),O["\u0275\u0275template"](28,$,2,0,"th",18),O["\u0275\u0275template"](29,q,8,2,"td",19),O["\u0275\u0275elementContainerEnd"](),O["\u0275\u0275elementContainerStart"](30,21),O["\u0275\u0275template"](31,G,2,0,"th",22),O["\u0275\u0275template"](32,W,14,3,"td",23),O["\u0275\u0275elementContainerEnd"](),O["\u0275\u0275elementContainerStart"](33,24),O["\u0275\u0275template"](34,X,1,0,"th",25),O["\u0275\u0275template"](35,Y,3,1,"td",26),O["\u0275\u0275elementContainerEnd"](),O["\u0275\u0275template"](36,J,1,0,"tr",27),O["\u0275\u0275template"](37,Q,1,0,"tr",28),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementStart"](38,"mat-drawer-content"),O["\u0275\u0275elementStart"](39,"div",29),O["\u0275\u0275elementStart"](40,"div",30),O["\u0275\u0275elementStart"](41,"button",31),O["\u0275\u0275listener"]("click",(function(){return t.navigateToSelectLogs()})),O["\u0275\u0275elementStart"](42,"mat-icon"),O["\u0275\u0275text"](43,"visibility"),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementStart"](44,"button",32),O["\u0275\u0275listener"]("click",(function(){return O["\u0275\u0275restoreView"](e),O["\u0275\u0275reference"](2).toggle()})),O["\u0275\u0275text"](45," + user "),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementStart"](46,"div",33),O["\u0275\u0275elementStart"](47,"div",14),O["\u0275\u0275template"](48,Z,1,1,"app-user-profile-detail",34),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementEnd"](),O["\u0275\u0275template"](49,te,18,1,"div",35),O["\u0275\u0275template"](50,ne,3,0,"div",36),O["\u0275\u0275elementEnd"](),O["\u0275\u0275elementEnd"]()}2&e&&(O["\u0275\u0275advance"](8),O["\u0275\u0275property"]("ngModel",t.getadminRoles),O["\u0275\u0275advance"](3),O["\u0275\u0275property"]("ngForOf",t.adminRolesArray),O["\u0275\u0275advance"](2),O["\u0275\u0275property"]("isAutocomplete",!1)("formControl",t.searchUser)("vcoe_flag",t.vcoe_flag),O["\u0275\u0275advance"](5),O["\u0275\u0275property"]("disabled",0==(null==t.totalUsers?null:t.totalUsers.length)||t.onSaveClick),O["\u0275\u0275advance"](5),O["\u0275\u0275property"]("dataSource",t.usersList),O["\u0275\u0275advance"](13),O["\u0275\u0275property"]("matHeaderRowDef",t.displayedColumns),O["\u0275\u0275advance"](1),O["\u0275\u0275property"]("matRowDefColumns",t.displayedColumns),O["\u0275\u0275advance"](11),O["\u0275\u0275property"]("ngForOf",t.usersAdminList),O["\u0275\u0275advance"](1),O["\u0275\u0275property"]("ngIf",!t.isLoading),O["\u0275\u0275advance"](1),O["\u0275\u0275property"]("ngIf",t.isLoading))},directives:[o.b,o.a,r.c,r.g,c.c,s.F,s.v,s.y,L.p,i.NgForOf,A,s.k,g.a,l.a,m.k,m.c,m.e,m.b,m.g,m.j,o.c,i.NgIf,m.d,m.a,r.h,s.e,r.i,m.f,m.i,T,p.a,h.c],styles:[".drawer[_ngcontent-%COMP%]{width:100%;height:150vh}.drawer[_ngcontent-%COMP%]   .card-design[_ngcontent-%COMP%]{width:190px!important;max-height:270px!important;padding:0!important}.drawer[_ngcontent-%COMP%]   input[type=number][_ngcontent-%COMP%]::-webkit-inner-spin-button, .drawer[_ngcontent-%COMP%]   input[type=number][_ngcontent-%COMP%]::-webkit-outer-spin-button{-webkit-appearance:none}.drawer[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:disabled{background:hsla(0,0%,40.4%,.7490196078431373)}.drawer[_ngcontent-%COMP%]   .tileName[_ngcontent-%COMP%]{color:#9a9a9a;font-size:15px!important}.drawer[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-prefix, .drawer[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-suffix{margin:auto}.drawer[_ngcontent-%COMP%]   .mat-drawer-content[_ngcontent-%COMP%]{padding:8px 42px!important}.drawer[_ngcontent-%COMP%]   .mat-drawer-content[_ngcontent-%COMP%]   .invoiceClass[_ngcontent-%COMP%]{width:60%;display:inline;overflow:auto;white-space:nowrap;margin:0 auto}.drawer[_ngcontent-%COMP%]   .mat-drawer-content[_ngcontent-%COMP%]   .invoiceClass[_ngcontent-%COMP%]   .invoiceId[_ngcontent-%COMP%]{font-size:16px;font-weight:500;color:#cf0001}.drawer[_ngcontent-%COMP%]   .side-drawer[_ngcontent-%COMP%]{width:53vw!important}.drawer[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{font-weight:400;color:#fff;background-color:#cf0001;height:30px;line-height:3px}.drawer[_ngcontent-%COMP%]   .btn-fab[_ngcontent-%COMP%]{height:35px;width:35px}.drawer[_ngcontent-%COMP%]   .btn-attach[_ngcontent-%COMP%], .drawer[_ngcontent-%COMP%]   .btn-fab[_ngcontent-%COMP%]{line-height:1px;color:#fff;background-color:#cf0001;box-shadow:0 3px 1px -2px rgba(0,0,0,.2),0 2px 2px 0 rgba(0,0,0,.14),0 1px 5px 0 rgba(0,0,0,.12)}.drawer[_ngcontent-%COMP%]   .btn-attach[_ngcontent-%COMP%]{height:36px;width:36px}.drawer[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]{margin-top:0!important;margin-bottom:1px!important;padding:0 5px!important;font-size:13px!important}.drawer[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}.drawer[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}.drawer[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#3c3c3c;font-weight:400;font-size:19px}.card-design[_ngcontent-%COMP%]{width:190px!important;max-height:270px!important;padding:0!important}input[type=number][_ngcontent-%COMP%]::-webkit-inner-spin-button, input[type=number][_ngcontent-%COMP%]::-webkit-outer-spin-button{-webkit-appearance:none}button[_ngcontent-%COMP%]:disabled{background:#ddd}.tileName[_ngcontent-%COMP%]{color:#9a9a9a;font-size:15px!important}.mat-drawer-content[_ngcontent-%COMP%]{padding:8px 42px!important}.mat-drawer-content[_ngcontent-%COMP%]   .invoiceClass[_ngcontent-%COMP%]{width:60%;display:inline;overflow:auto;white-space:nowrap;margin:0 auto}.mat-drawer-content[_ngcontent-%COMP%]   .invoiceClass[_ngcontent-%COMP%]   .invoiceId[_ngcontent-%COMP%]{font-size:16px;font-weight:500;color:#cf0001}.side-drawer[_ngcontent-%COMP%]{width:52vw!important}.btn[_ngcontent-%COMP%]{font-weight:400;color:#fff;background-color:#cf0001;height:30px;line-height:3px}.btn-fab[_ngcontent-%COMP%]{height:35px;width:35px;border-radius:20px;border:none}.btn-attach[_ngcontent-%COMP%], .btn-fab[_ngcontent-%COMP%]{line-height:1px;color:#fff;background-color:#cf0001;box-shadow:0 3px 1px -2px rgba(0,0,0,.2),0 2px 2px 0 rgba(0,0,0,.14),0 1px 5px 0 rgba(0,0,0,.12)}.btn-attach[_ngcontent-%COMP%]{height:36px;width:36px}.mat-form-field[_ngcontent-%COMP%]{margin-top:0!important;margin-bottom:1px!important;padding:0 5px!important;font-size:13px!important}.slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}.slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}h4[_ngcontent-%COMP%]{color:#3c3c3c;font-weight:400;font-size:19px}.addUser[_ngcontent-%COMP%]{border:none;width:90px;height:35px;background:#ee4961;border-radius:4px;color:#fff}.tableHeader[_ngcontent-%COMP%]{width:92px;height:16px;font-weight:400;font-size:11px;text-transform:uppercase;color:#b9c0ca}.tableData[_ngcontent-%COMP%], .tableHeader[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;line-height:16px;letter-spacing:.02em}.tableData[_ngcontent-%COMP%]{font-weight:500;font-size:12px;text-transform:capitalize;color:#526179}.navigateButton[_ngcontent-%COMP%]{border:none;background-color:initial}.tableClass[_ngcontent-%COMP%]{width:1240px;height:320px;background:#fff;box-shadow:0 2px 1px rgba(0,0,0,.12);border-radius:4px;overflow-y:scroll}"]}),e})()}];let oe=(()=>{class e{}return e.\u0275mod=O["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=O["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[v.k.forChild(ie)],v.k]}),e})(),re=(()=>{class e{}return e.\u0275mod=O["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=O["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,oe,o.g,a.c,s.p,s.E,l.b,r.e,d.c,c.d,m.m,p.b,h.b,g.b,f.a,u.e,b.b]]}),e})()},NJ67:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));class i{constructor(){this.disabled=!1}onChange(e){}onTouched(e){}writeValue(e){this.value=e}registerOnChange(e){this.onChange=e}registerOnTouched(e){this.onTouched=e}setDisabledState(e){this.disabled=e}}}}]);