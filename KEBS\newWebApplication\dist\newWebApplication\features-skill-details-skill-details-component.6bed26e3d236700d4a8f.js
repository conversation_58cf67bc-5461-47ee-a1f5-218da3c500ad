(window.webpackJsonp=window.webpackJsonp||[]).push([[652,535,631,634,765,821,822,858,983,987,990,991],{"3vwd":function(e,t,n){"use strict";n.d(t,"a",(function(){return b}));var i=n("fXoL"),l=n("wO+i"),a=n("m5YA"),r=n("B0y8"),o=n("0IaG"),s=n("JLuW"),d=n("ofXK"),c=n("bTqV"),m=n("TU8p"),p=n("NFeN"),u=n("Qu3c"),h=n("tvi8"),g=n("H/9Z");function f(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",2),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"]().openPopUp()})),i["\u0275\u0275elementStart"](1,"mat-icon"),i["\u0275\u0275text"](2,"attach_file"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275propertyInterpolate"]("matBadge",e.fileCount),i["\u0275\u0275property"]("matBadgeHidden",0==e.fileCount)}}function v(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",4),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"](2).openPopUp()})),i["\u0275\u0275elementStart"](1,"span",5),i["\u0275\u0275text"](2),i["\u0275\u0275elementEnd"](),i["\u0275\u0275text"](3," Attachments "),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate"](e.fileCount)}}function y(e,t){if(1&e&&(i["\u0275\u0275element"](0,"img",10),i["\u0275\u0275pipe"](1,"fileIcon")),2&e){const e=i["\u0275\u0275nextContext"](3);i["\u0275\u0275property"]("src",i["\u0275\u0275pipeBind1"](1,1,e.singleFileDetail.file_format),i["\u0275\u0275sanitizeUrl"])}}function k(e,t){if(1&e&&(i["\u0275\u0275element"](0,"img",10),i["\u0275\u0275pipe"](1,"async"),i["\u0275\u0275pipe"](2,"getUrl")),2&e){const e=i["\u0275\u0275nextContext"](3);i["\u0275\u0275property"]("src",i["\u0275\u0275pipeBind1"](1,1,i["\u0275\u0275pipeBind1"](2,3,e.singleFileDetail.cdn_link)),i["\u0275\u0275sanitizeUrl"])}}function C(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",4),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const t=i["\u0275\u0275nextContext"](2);return t.viewFile(t.singleFileDetail)})),i["\u0275\u0275elementStart"](1,"span",6),i["\u0275\u0275template"](2,y,2,3,"img",7),i["\u0275\u0275template"](3,k,3,5,"ng-template",null,8,i["\u0275\u0275templateRefExtractor"]),i["\u0275\u0275elementEnd"](),i["\u0275\u0275text"](5,"\xa0 "),i["\u0275\u0275elementStart"](6,"div",9),i["\u0275\u0275text"](7),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275reference"](4),t=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf","png"!=t.singleFileDetail.file_format&&"jpg"!=t.singleFileDetail.file_format&&"jpeg"!=t.singleFileDetail.file_format)("ngIfElse",e),i["\u0275\u0275advance"](4),i["\u0275\u0275propertyInterpolate"]("matTooltip",t.singleFileDetail.file_name),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" ",t.singleFileDetail.file_name," ")}}function S(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"span"),i["\u0275\u0275text"](1,"-"),i["\u0275\u0275elementEnd"]())}function x(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div"),i["\u0275\u0275template"](1,v,4,1,"button",3),i["\u0275\u0275template"](2,C,8,4,"button",3),i["\u0275\u0275template"](3,S,2,0,"span",1),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",e.fileCount>1),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",1==e.fileCount&&e.singleFileDetail),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",0==e.fileCount)}}let b=(()=>{class e{constructor(e,t){this.dialog=e,this._shared=t,this.change=new i.EventEmitter,this.btnColor="#cf0001",this.isDialogOpened=!1,this.isViewFileOpened=!1,this.fileCount=0}ngOnInit(){document.documentElement.style.setProperty("--btnColor",this.btnColor),this.destinationBucket&&this.contextId&&this._shared.getObjectCount(this.destinationBucket,this.contextId).pipe(Object(l.a)("data")).subscribe(e=>{this.fileCount=e,1==this.fileCount&&this.getFileDetail()},e=>{console.error(e)})}getFileDetail(){this._shared.retrieveUploadedObjects(this.destinationBucket,this.contextId).pipe(Object(l.a)("data")).subscribe(e=>{let t=[];t.push(...e),t.length>0&&(this.singleFileDetail=t[0]),console.log(this.singleFileDetail)},e=>{console.error(e)})}openPopUp(){let e=null;0==this.isDialogOpened&&(e=this.dialog.open(r.a,{width:"100%",height:"90%",data:{data:{destinationBucket:this.destinationBucket,routingKey:this.routingKey,contextId:this.contextId,allowEdit:this.allowEdit,myFilesDefaultFolder:this.myFilesDefaultFolder},expHeaderId:this.expHeaderId},disableClose:!0}),this.isDialogOpened=!0),e.afterClosed().subscribe(e=>{this.isDialogOpened=!1;let t=e.contextId;this.fileCount=e.fileCount,this.change.emit(t)})}viewFile(e){console.log(this.isViewFileOpened);let t=e.cdn_link,n=null;0==this.isViewFileOpened&&(this.isViewFileOpened=!0,this._shared.getDownloadUrl(t).subscribe(t=>{n=this.dialog.open(a.a,{width:"100%",height:"100%",data:{selectedFileUrl:t.data,fileFormat:e.file_format,expHeaderId:this.expHeaderId}}),n.afterClosed().subscribe(e=>{this.isViewFileOpened=!1})}))}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](o.b),i["\u0275\u0275directiveInject"](s.a))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["attachment-upload-btn"]],inputs:{destinationBucket:"destinationBucket",routingKey:"routingKey",contextId:"contextId",allowEdit:"allowEdit",myFilesDefaultFolder:"myFilesDefaultFolder",expHeaderId:"expHeaderId",btnColor:"btnColor"},outputs:{change:"change"},decls:2,vars:2,consts:[["mat-icon-button","","matBadgeColor","warn","class","upload-btn",3,"matBadge","matBadgeHidden","click",4,"ngIf"],[4,"ngIf"],["mat-icon-button","","matBadgeColor","warn",1,"upload-btn",3,"matBadge","matBadgeHidden","click"],["mat-button","",3,"click",4,"ngIf"],["mat-button","",3,"click"],[1,"attachment-count"],[1,"thumbnail"],["style","height: 26px; width: 26px; border-radius: 50%","alt","",3,"src",4,"ngIf","ngIfElse"],["showimg",""],[1,"file-name",3,"matTooltip"],["alt","",2,"height","26px","width","26px","border-radius","50%",3,"src"]],template:function(e,t){1&e&&(i["\u0275\u0275template"](0,f,3,2,"button",0),i["\u0275\u0275template"](1,x,4,3,"div",1)),2&e&&(i["\u0275\u0275property"]("ngIf",t.allowEdit),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",!t.allowEdit))},directives:[d.NgIf,c.a,m.a,p.a,u.a],pipes:[h.a,d.AsyncPipe,g.a],styles:[".file-name[_ngcontent-%COMP%]{white-space:nowrap;width:81px;overflow:hidden;text-overflow:ellipsis;display:inline-block}.attachment-count[_ngcontent-%COMP%]{color:var(--btnColor)!important}"]}),e})()},"9fkf":function(e,t,n){"use strict";n.r(t),n.d(t,"SkillDetailsComponent",(function(){return ue})),n.d(t,"SkillDetailsModule",(function(){return he}));var i=n("mrSG"),l=n("fXoL"),a=n("xG9w"),r=n("wd/R"),o=n("0IaG"),s=n("FKr1"),d=n("1yaQ"),c=n("ofXK"),m=n("Xi0T"),p=n("kmnG"),u=n("qFsG"),h=n("3Pt+"),g=n("iadO"),f=n("NFeN"),v=n("bTqV"),y=n("Xa2L"),k=n("Qu3c"),C=n("1jcm"),S=n("33Jv"),x=n("d3UM"),b=n("bSwM"),w=n("QibW"),E=n("pA3K"),F=n("jAlA"),D=n("1A3m"),I=n("XXEo"),_=n("TmG/"),A=n("3vwd"),P=n("qFYv");function O(e,t){1&e&&(l["\u0275\u0275elementContainerStart"](0),l["\u0275\u0275elementStart"](1,"div",2),l["\u0275\u0275element"](2,"mat-spinner",3),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementContainerEnd"]())}function M(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",6),l["\u0275\u0275elementStart"](1,"div",7),l["\u0275\u0275text"](2," Skill Type "),l["\u0275\u0275elementStart"](3,"span",8),l["\u0275\u0275text"](4," \xa0*"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](5,"div",5),l["\u0275\u0275elementStart"](6,"app-input-search",31),l["\u0275\u0275listener"]("change",(function(){l["\u0275\u0275restoreView"](e);const t=l["\u0275\u0275nextContext"]().index;return l["\u0275\u0275nextContext"](2).handleSkillTypeChange(t)})),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&e){const e=l["\u0275\u0275nextContext"](3);l["\u0275\u0275advance"](6),l["\u0275\u0275property"]("list",e.skillTypeMaster)("disableNone",!0)}}function N(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",6),l["\u0275\u0275elementStart"](1,"div",7),l["\u0275\u0275text"](2," Skill Library "),l["\u0275\u0275elementStart"](3,"span",8),l["\u0275\u0275text"](4," \xa0*"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](5,"div",5),l["\u0275\u0275elementStart"](6,"app-input-search",31),l["\u0275\u0275listener"]("change",(function(){l["\u0275\u0275restoreView"](e);const t=l["\u0275\u0275nextContext"]().index;return l["\u0275\u0275nextContext"](2).handleSkillTypeChange(t)})),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&e){const e=l["\u0275\u0275nextContext"](3);l["\u0275\u0275advance"](6),l["\u0275\u0275property"]("list",e.skillTypeMaster)("disableNone",!0)}}function T(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"span",8),l["\u0275\u0275text"](1," \xa0*"),l["\u0275\u0275elementEnd"]())}function L(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div",6),l["\u0275\u0275elementStart"](1,"div",7),l["\u0275\u0275text"](2," Skill Category "),l["\u0275\u0275template"](3,T,2,0,"span",32),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](4,"div",5),l["\u0275\u0275element"](5,"app-input-search",33),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"](3);l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("ngIf",null==e.tenantFields||null==e.tenantFields.skillCategory?null:e.tenantFields.skillCategory.isMandatory),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("required",null==e.tenantFields||null==e.tenantFields.skillCategory?null:e.tenantFields.skillCategory.isMandatory)("list",e.skillCategoryMaster)("disableNone",!0)}}function V(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"span",8),l["\u0275\u0275text"](1," \xa0*"),l["\u0275\u0275elementEnd"]())}function j(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div",6),l["\u0275\u0275elementStart"](1,"div",7),l["\u0275\u0275text"](2," Experience "),l["\u0275\u0275template"](3,V,2,0,"span",32),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](4,"div",5),l["\u0275\u0275element"](5,"app-input-search",34),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"](3);l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("ngIf",null==e.tenantFields||null==e.tenantFields.experience?null:e.tenantFields.experience.isMandatory),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("required",null==e.tenantFields||null==e.tenantFields.experience?null:e.tenantFields.experience.isMandatory)("list",e.skillExperienceMaster)("disableNone",!0)}}function R(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"span",8),l["\u0275\u0275text"](1," \xa0*"),l["\u0275\u0275elementEnd"]())}function G(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div",6),l["\u0275\u0275elementStart"](1,"div",7),l["\u0275\u0275text"](2,"Endorse"),l["\u0275\u0275template"](3,R,2,0,"span",32),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](4,"div",5),l["\u0275\u0275elementStart"](5,"mat-form-field",9),l["\u0275\u0275element"](6,"input",35),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"](3);l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("ngIf",null==e.tenantFields||null==e.tenantFields.endorse?null:e.tenantFields.endorse.isMandatory)}}function q(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"span",8),l["\u0275\u0275text"](1," \xa0*"),l["\u0275\u0275elementEnd"]())}function K(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div",6),l["\u0275\u0275elementStart"](1,"div",7),l["\u0275\u0275text"](2," Skill Group "),l["\u0275\u0275template"](3,q,2,0,"span",32),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](4,"div",5),l["\u0275\u0275element"](5,"app-input-search",36),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"](3);l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("ngIf",null==e.tenantFields||null==e.tenantFields.skillGroup?null:e.tenantFields.skillGroup.isMandatory),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("required",null==e.tenantFields||null==e.tenantFields.skillGroup?null:e.tenantFields.skillGroup.isMandatory)("list",e.skillGroupMaster)("disableNone",!0)}}function B(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"span",8),l["\u0275\u0275text"](1," \xa0*"),l["\u0275\u0275elementEnd"]())}function U(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"mat-option",39),l["\u0275\u0275text"](1),l["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;l["\u0275\u0275property"]("value",e.id),l["\u0275\u0275advance"](1),l["\u0275\u0275textInterpolate"](e.name)}}function H(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div",6),l["\u0275\u0275elementStart"](1,"div",7),l["\u0275\u0275text"](2," SAP Activate "),l["\u0275\u0275template"](3,B,2,0,"span",32),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](4,"div",5),l["\u0275\u0275elementStart"](5,"mat-form-field",9),l["\u0275\u0275elementStart"](6,"mat-select",37),l["\u0275\u0275template"](7,U,2,2,"mat-option",38),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"]().index,t=l["\u0275\u0275nextContext"](2);l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("ngIf",t.showSapActive[e]),l["\u0275\u0275advance"](4),l["\u0275\u0275property"]("ngForOf",t.sapActivateMaster)}}function W(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"div",6),l["\u0275\u0275elementStart"](1,"div",28),l["\u0275\u0275elementStart"](2,"mat-checkbox",40),l["\u0275\u0275text"](3,"Primary Skill"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]())}function Y(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",42),l["\u0275\u0275elementStart"](1,"span",43),l["\u0275\u0275listener"]("click",(function(){l["\u0275\u0275restoreView"](e);const t=l["\u0275\u0275nextContext"](2).index;return l["\u0275\u0275nextContext"](2).removeSkill(t)})),l["\u0275\u0275text"](2,"- Remove this skill"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}}function J(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div",5),l["\u0275\u0275template"](1,Y,3,0,"div",41),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"](3);l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",!(null!=e.tenantFields&&null!=e.tenantFields.action&&e.tenantFields.action.isActiveField)||1!=e.isFromModal)}}function X(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",23),l["\u0275\u0275elementStart"](1,"div",24),l["\u0275\u0275elementStart"](2,"div",5),l["\u0275\u0275template"](3,M,7,2,"div",25),l["\u0275\u0275template"](4,N,7,2,"div",25),l["\u0275\u0275elementStart"](5,"div",6),l["\u0275\u0275elementStart"](6,"div",7),l["\u0275\u0275text"](7," Skill Name "),l["\u0275\u0275elementStart"](8,"span",8),l["\u0275\u0275text"](9," \xa0*"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](10,"div",5),l["\u0275\u0275element"](11,"app-input-search",26),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](12,"div",6),l["\u0275\u0275elementStart"](13,"div",7),l["\u0275\u0275text"](14," Skill Level "),l["\u0275\u0275elementStart"](15,"span",8),l["\u0275\u0275text"](16," \xa0*"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](17,"div",5),l["\u0275\u0275element"](18,"app-input-search",27),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](19,L,6,4,"div",25),l["\u0275\u0275template"](20,j,6,4,"div",25),l["\u0275\u0275template"](21,G,7,1,"div",25),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](22,"div",5),l["\u0275\u0275template"](23,K,6,4,"div",25),l["\u0275\u0275elementStart"](24,"div",6),l["\u0275\u0275elementStart"](25,"div",7),l["\u0275\u0275text"](26,"Upload Certificate(If any)"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](27,"div",28),l["\u0275\u0275elementStart"](28,"attachment-upload-btn",29),l["\u0275\u0275listener"]("change",(function(n){l["\u0275\u0275restoreView"](e);const i=t.index;return l["\u0275\u0275nextContext"](2).changeInattachments(n,i)})),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](29,"div",5),l["\u0275\u0275template"](30,H,8,2,"div",25),l["\u0275\u0275template"](31,W,4,0,"div",25),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](32,J,2,1,"div",30),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&e){const e=t.index,n=l["\u0275\u0275nextContext"](2);l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("formGroupName",e),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf",null==n.tenantFields||null==n.tenantFields.skillType?null:n.tenantFields.skillType.isActiveField),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",null==n.tenantFields||null==n.tenantFields.skillLibrary?null:n.tenantFields.skillLibrary.isActiveField),l["\u0275\u0275advance"](7),l["\u0275\u0275property"]("list",n.getSkillMaster(e))("disableNone",!0),l["\u0275\u0275advance"](7),l["\u0275\u0275property"]("list",n.skillLevelMaster)("disableNone",!0),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",null==n.tenantFields||null==n.tenantFields.skillCategory?null:n.tenantFields.skillCategory.isActiveField),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",null==n.tenantFields||null==n.tenantFields.experience?null:n.tenantFields.experience.isActiveField),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",null==n.tenantFields||null==n.tenantFields.endorse?null:n.tenantFields.endorse.isActiveField),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf",null==n.tenantFields||null==n.tenantFields.skillGroup?null:n.tenantFields.skillGroup.isActiveField),l["\u0275\u0275advance"](5),l["\u0275\u0275property"]("destinationBucket",n.attachmentPluginConfig.destinationBucket)("routingKey",n.attachmentPluginConfig.routingKey)("allowEdit",n.attachmentPluginConfig.allowEdit)("contextId",n.getSkillAttachmentValue(e)),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf",(null==n.tenantFields||null==n.tenantFields.sapActivate?null:n.tenantFields.sapActivate.isActiveField)&&n.showSapActive[e]),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",null==n.tenantFields||null==n.tenantFields.isPrimarySkill?null:n.tenantFields.isPrimarySkill.isActiveField),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",e>=1)}}function $(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",44),l["\u0275\u0275elementStart"](1,"span",43),l["\u0275\u0275listener"]("click",(function(){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"](2).addNewSkill()})),l["\u0275\u0275text"](2,"+ Add New Skill"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}}function z(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"span",8),l["\u0275\u0275text"](1," \xa0*"),l["\u0275\u0275elementEnd"]())}function Q(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"span",8),l["\u0275\u0275text"](1," \xa0*"),l["\u0275\u0275elementEnd"]())}function Z(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",55),l["\u0275\u0275elementStart"](1,"div",42),l["\u0275\u0275elementStart"](2,"span",43),l["\u0275\u0275listener"]("click",(function(){l["\u0275\u0275restoreView"](e);const t=l["\u0275\u0275nextContext"]().index;return l["\u0275\u0275nextContext"](3).removeLanguage(t)})),l["\u0275\u0275text"](3,"- Remove this language"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}}function ee(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div",47),l["\u0275\u0275elementStart"](1,"div",24),l["\u0275\u0275elementStart"](2,"div",5),l["\u0275\u0275elementStart"](3,"div",6),l["\u0275\u0275elementStart"](4,"div",7),l["\u0275\u0275text"](5," Language Name "),l["\u0275\u0275template"](6,z,2,0,"span",32),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](7,"div",5),l["\u0275\u0275element"](8,"app-input-search",48),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](9,"div",49),l["\u0275\u0275elementStart"](10,"div",7),l["\u0275\u0275text"](11," Proficiency "),l["\u0275\u0275template"](12,Q,2,0,"span",32),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](13,"div",50),l["\u0275\u0275elementStart"](14,"mat-checkbox",51),l["\u0275\u0275text"](15,"Read"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](16,"mat-checkbox",52),l["\u0275\u0275text"](17,"Write"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](18,"mat-checkbox",53),l["\u0275\u0275text"](19,"Speak"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](20,Z,4,0,"div",54),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&e){const e=t.index,n=l["\u0275\u0275nextContext"](3);l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("formGroupName",e),l["\u0275\u0275advance"](5),l["\u0275\u0275property"]("ngIf",null==n.tenantFields||null==n.tenantFields.language?null:n.tenantFields.language.isMandatory),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("required",null==n.tenantFields||null==n.tenantFields.language?null:n.tenantFields.language.isMandatory)("list",n.languagesList)("disableNone",!0),l["\u0275\u0275advance"](4),l["\u0275\u0275property"]("ngIf",null==n.tenantFields||null==n.tenantFields.language?null:n.tenantFields.language.isMandatory),l["\u0275\u0275advance"](8),l["\u0275\u0275property"]("ngIf",e>0)}}function te(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"form",13),l["\u0275\u0275listener"]("keydown.enter",(function(e){return e.preventDefault()})),l["\u0275\u0275elementStart"](1,"div",14),l["\u0275\u0275text"](2,"Languages Known"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](3,ee,21,7,"div",45),l["\u0275\u0275elementStart"](4,"div",46),l["\u0275\u0275elementStart"](5,"span",43),l["\u0275\u0275listener"]("click",(function(){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"](2).addNewLanguage()})),l["\u0275\u0275text"](6,"+ Add New Language"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&e){const e=l["\u0275\u0275nextContext"](2);l["\u0275\u0275property"]("formGroup",e.languageDetailsFormGroup),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("ngForOf",e.languageDetailFormArray.controls)}}const ne=function(){return["activity_name"]};function ie(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"div",6),l["\u0275\u0275elementStart"](1,"div",7),l["\u0275\u0275text"](2," Activity Type "),l["\u0275\u0275elementStart"](3,"span",8),l["\u0275\u0275text"](4," \xa0*"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](5,"div",5),l["\u0275\u0275element"](6,"app-input-search-huge-input",58),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&e&&(l["\u0275\u0275advance"](6),l["\u0275\u0275property"]("optionLabel",l["\u0275\u0275pureFunction0"](2,ne))("apiUri","/api/employee360/masterData/getExtraCurricularActivity"))}function le(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"span",8),l["\u0275\u0275text"](1," \xa0*"),l["\u0275\u0275elementEnd"]())}function ae(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div",6),l["\u0275\u0275elementStart"](1,"div",7),l["\u0275\u0275text"](2," Activity Name "),l["\u0275\u0275template"](3,le,2,0,"span",32),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](4,"div",5),l["\u0275\u0275elementStart"](5,"mat-form-field",9),l["\u0275\u0275element"](6,"input",59),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"](4);l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("ngIf",null==e.tenantFields||null==e.tenantFields.activityName?null:e.tenantFields.activityName.isMandatory),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("required",null==e.tenantFields||null==e.tenantFields.activityName?null:e.tenantFields.activityName.isMandatory)}}function re(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",55),l["\u0275\u0275elementStart"](1,"div",42),l["\u0275\u0275elementStart"](2,"span",43),l["\u0275\u0275listener"]("click",(function(){l["\u0275\u0275restoreView"](e);const t=l["\u0275\u0275nextContext"]().index;return l["\u0275\u0275nextContext"](3).removeActivity(t)})),l["\u0275\u0275text"](3,"- Remove this activity"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}}function oe(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div",57),l["\u0275\u0275elementStart"](1,"div",24),l["\u0275\u0275elementStart"](2,"div",5),l["\u0275\u0275template"](3,ie,7,3,"div",25),l["\u0275\u0275template"](4,ae,7,2,"div",25),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](5,re,4,0,"div",54),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&e){const e=t.index,n=l["\u0275\u0275nextContext"](3);l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("formGroupName",e),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngIf",null==n.tenantFields||null==n.tenantFields.activityType?null:n.tenantFields.activityType.isActiveField),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",null==n.tenantFields||null==n.tenantFields.activityName?null:n.tenantFields.activityName.isActiveField),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",e>0)}}function se(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"form",4),l["\u0275\u0275listener"]("keydown.enter",(function(e){return e.preventDefault()})),l["\u0275\u0275elementStart"](1,"div",14),l["\u0275\u0275text"](2,"Extra-Curricular Activities"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](3,oe,6,4,"div",56),l["\u0275\u0275elementStart"](4,"div",44),l["\u0275\u0275elementStart"](5,"span",43),l["\u0275\u0275listener"]("click",(function(){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"](2).addNewActivity()})),l["\u0275\u0275text"](6,"+ Add New Activity"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()}if(2&e){const e=l["\u0275\u0275nextContext"](2);l["\u0275\u0275property"]("formGroup",e.extraCurricularActivityFormGroup),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("ngForOf",e.activityDetailFormArray.controls)}}function de(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"div",19),l["\u0275\u0275elementStart"](1,"div",7),l["\u0275\u0275text"](2,"Achievements & Memberships If Any"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]())}function ce(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"div",5),l["\u0275\u0275elementStart"](1,"mat-form-field",61),l["\u0275\u0275element"](2,"textarea",62),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]())}function me(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"form",4),l["\u0275\u0275listener"]("keydown.enter",(function(e){return e.preventDefault()})),l["\u0275\u0275template"](1,de,3,0,"div",60),l["\u0275\u0275template"](2,ce,3,0,"div",30),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"](2);l["\u0275\u0275property"]("formGroup",e.skillMetaDataFormGroup),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",null==e.tenantFields||null==e.tenantFields.achivAndMemDescription?null:e.tenantFields.achivAndMemDescription.isActiveField),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",null==e.tenantFields||null==e.tenantFields.achivAndMemDescription?null:e.tenantFields.achivAndMemDescription.isActiveField)}}function pe(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementContainerStart"](0),l["\u0275\u0275elementStart"](1,"form",4),l["\u0275\u0275listener"]("keydown.enter",(function(e){return e.preventDefault()})),l["\u0275\u0275elementStart"](2,"div",5),l["\u0275\u0275elementStart"](3,"div",6),l["\u0275\u0275elementStart"](4,"div",7),l["\u0275\u0275text"](5," Effective Date "),l["\u0275\u0275elementStart"](6,"span",8),l["\u0275\u0275text"](7," \xa0*"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](8,"div",5),l["\u0275\u0275elementStart"](9,"mat-form-field",9),l["\u0275\u0275element"](10,"input",10),l["\u0275\u0275element"](11,"mat-datepicker-toggle",11),l["\u0275\u0275element"](12,"mat-datepicker",null,12),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](14,"form",13),l["\u0275\u0275listener"]("keydown.enter",(function(e){return e.preventDefault()})),l["\u0275\u0275elementStart"](15,"div",14),l["\u0275\u0275text"](16,"Skills"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](17,X,33,18,"div",15),l["\u0275\u0275template"](18,$,3,0,"div",16),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](19,te,7,2,"form",17),l["\u0275\u0275template"](20,se,7,2,"form",18),l["\u0275\u0275template"](21,me,3,3,"form",18),l["\u0275\u0275elementStart"](22,"div",19),l["\u0275\u0275elementStart"](23,"div",20),l["\u0275\u0275elementStart"](24,"button",21),l["\u0275\u0275listener"]("click",(function(){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"]().skipSection()})),l["\u0275\u0275text"](25),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](26,"button",22),l["\u0275\u0275listener"]("click",(function(){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"]().saveDetails()})),l["\u0275\u0275text"](27),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=l["\u0275\u0275reference"](13),t=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("formGroup",t.effectiveDateFormGroup),l["\u0275\u0275advance"](9),l["\u0275\u0275property"]("matDatepicker",e)("min",t.employeeDoj)("readonly",!0),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("for",e),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("formGroup",t.skillDetailsFormGroup),l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("ngForOf",t.skillDetailFormArray.controls),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",!(null!=t.tenantFields&&null!=t.tenantFields.action&&t.tenantFields.action.isActiveField)||1!=t.isFromModal),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",null==t.tenantFields||null==t.tenantFields.language?null:t.tenantFields.language.isActiveField),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",(null==t.tenantFields||null==t.tenantFields.activityType?null:t.tenantFields.activityType.isActiveField)||(null==t.tenantFields||null==t.tenantFields.activityName?null:t.tenantFields.activityName.isActiveField)),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",null==t.tenantFields||null==t.tenantFields.achivAndMemDescription?null:t.tenantFields.achivAndMemDescription.isActiveField),l["\u0275\u0275advance"](4),l["\u0275\u0275textInterpolate1"](" ",t.isFromModal?"Cancel":"Skip"," "),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("disabled",t.loaderObject.isFormSubmitLoading),l["\u0275\u0275advance"](1),l["\u0275\u0275textInterpolate1"](" ",t.isFromModal?"Update":"Save & Next >"," ")}}let ue=(()=>{class e{constructor(e,t,n,i,a){this._edService=e,this._toaster=t,this.fb=n,this.injector=i,this._loginService=a,this.dialogRef=null,this.isFromModal=!1,this.isFromMyProfile=!1,this.showSapActive=[],this.skillDetailsRes=new l.EventEmitter,this.subs=new S.a,this.loaderObject={isComponentLoading:!1,isFormSubmitLoading:!1},this.effectiveDateFormGroup=this.fb.group({effectiveDate:["",h.H.required]}),this.effectiveDatePayload={},this.skillDetailsFormGroup=this.fb.group({skillDetailArr:this.fb.array([])}),this.skillTypeMaster=[],this.skillLevelMaster=[],this.skillExperienceMaster=[],this.skillDetailsPayload=[],this.skillNameMaster=[],this.skillCategoryMaster=[],this.skillGroupMaster=[],this.languageDetailsFormGroup=this.fb.group({languageDetailArr:this.fb.array([])}),this.languageDetailsPayload=[],this.extraCurricularActivityFormGroup=this.fb.group({activityArr:this.fb.array([])}),this.skillMetaDataFormGroup=this.fb.group({achivAndMemDescription:[""],isCourtProceedings:[""],courtProceedingsDesc:[""]}),this.activityTypeMaster=[],this.extraCurricularActivityPayload=[],this.skillMetaDataPayload={},this.sapActivateMaster=[],this.languagesList=[],this.isAddNewSkill=!1,this.attachmentPluginConfig={destinationBucket:"",routingKey:"",allowEdit:!0},this.tenantFields={},this.removedSkillRecord=[],this.removedLangRecord=[],this.removedActivityRecord=[],this.dialogRef=this.injector.get(o.h,null),this.dialogData=this.injector.get(o.a,null)}createNewSkill(e){return this.fb.group({recordId:[e?e.recordId:null],skillType:[e?e.type:"",h.H.required],skillMaster:[e?e.skillMaster:""],skillName:[e?e.skillName:""],skillLevel:[e?e.skillLevel:"",h.H.required],experience:[e?e.experience:""],endorse:[e?e.endorse:""],certificate:[e?e.certificate:null],isPrimarySkill:[e?e.isPrimarySkill:""],sapActivate:[e?e.sapActivate:""],skillCategory:[e?e.skillCategory:""],skillGroup:[e?e.skillGroup:""]})}createNewLanguage(e){return this.fb.group({recordId:[e?e.recordId:null],language:[e?e.language:""],read:[e?e.read:""],write:[e?e.write:""],speak:[e?e.speak:""]})}createNewActivity(e){return this.fb.group({recordId:[e?e.recordId:null],activityType:[e?e.type:""],activityName:[e?e.activityName:""]})}get skillDetailFormArray(){return this.skillDetailsFormGroup.get("skillDetailArr")}get languageDetailFormArray(){return this.languageDetailsFormGroup.get("languageDetailArr")}get activityDetailFormArray(){return this.extraCurricularActivityFormGroup.get("activityArr")}addNewSkill(){this.skillDetailFormArray.push(this.createNewSkill(null)),this.setTenantFieldValidators(),this.showSapActive.push(!1)}removeSkill(e){this.addDeletedRecordId(this.removedSkillRecord,e,this.skillDetailFormArray),this.skillDetailFormArray.removeAt(e),this.showSapActive.splice(e,1)}addNewLanguage(){this.languageDetailFormArray.push(this.createNewLanguage(null))}removeLanguage(e){this.addDeletedRecordId(this.removedLangRecord,e,this.languageDetailFormArray),this.languageDetailFormArray.removeAt(e)}addNewActivity(){this.activityDetailFormArray.push(this.createNewActivity(null))}removeActivity(e){this.addDeletedRecordId(this.removedActivityRecord,e,this.activityDetailFormArray),this.activityDetailFormArray.removeAt(e)}addDeletedRecordId(e,t,n){let i=n.controls[t].get("recordId").value;i&&e.push(i)}getSkillMaster(e){return this.skillDetailFormArray.at(e).get("skillMaster").value}getSkillType(){return new Promise((e,t)=>{this.subs.sink=this._edService.getSkillType().subscribe(t=>{e(t.data)},e=>{console.log(e),t(e)})})}getSkillName(e){return new Promise((t,n)=>{this.subs.sink=this._edService.getSkillName(e).subscribe(e=>{t(e.data)},e=>{console.log(e),n(e)})})}handleSkillTypeChange(e){var t;return Object(i.c)(this,void 0,void 0,(function*(){this.skillDetailFormArray.at(e).get("skillName").reset();let n=this.skillDetailFormArray.at(e).get("skillType").value;this.skillNameMaster=yield this.getSkillName(n),this.skillDetailFormArray.at(e).get("skillMaster").patchValue(this.skillNameMaster),(null===(t=this.tenantFields.sapActivate)||void 0===t?void 0:t.isActiveField)&&this.setMandatoryBasedOnSkillType(e)}))}getSkillLevel(){return new Promise((e,t)=>{this.subs.sink=this._edService.getSkillLevel().subscribe(t=>{e(t.data)},e=>{console.log(e),t(e)})})}getSkillExperience(){return new Promise((e,t)=>{this.subs.sink=this._edService.getSkillExperience().subscribe(t=>{e(t.data)},e=>{console.log(e),t(e)})})}getSkillGroup(){return new Promise((e,t)=>{this.subs.sink=this._edService.getSkillGroup().subscribe(t=>{e(t.data)},e=>{console.log(e),t(e)})})}getSkillCategory(){return new Promise((e,t)=>{this.subs.sink=this._edService.getSkillCategory().subscribe(t=>{e(t.data)},e=>{console.log(e),t(e)})})}getLanguagesList(){return new Promise((e,t)=>{this.subs.sink=this._edService.getLanguagesList().subscribe(t=>{e(t.data)},e=>{console.log(e),t(e)})})}getE360Config(){return new Promise((e,t)=>{this.subs.sink=this._edService.getE360Config().subscribe(t=>{e(t)},e=>{console.log(e),t(e)})})}getDateOfJoining(e){return new Promise((t,n)=>{this.subs.sink=this._edService.getDateOfJoining(e).subscribe(e=>{e.err?n(e):t(e.doj)},e=>{console.log(e),n(e)})})}getFieldTenantConfig(e){return new Promise((t,n)=>{this.subs.sink=this._edService.getFieldTenantConfig(e).subscribe(e=>{t(e.data)},e=>{console.log(e),n(e)})})}getSapActivate(){return new Promise((e,t)=>{this.subs.sink=this._edService.getSapActivateMaster().subscribe(t=>{e(t.data)},e=>{console.log(e),t(e)})})}ngOnInit(){var e,t,n,l,a,r,o,s,d,c;return Object(i.c)(this,void 0,void 0,(function*(){this.loaderObject.isComponentLoading=!0,yield this.getAttachmentPluginConfig(),this.associateId=(null===(t=null===(e=this.dialogData)||void 0===e?void 0:e.modalParams)||void 0===t?void 0:t.associateId)?null===(l=null===(n=this.dialogData)||void 0===n?void 0:n.modalParams)||void 0===l?void 0:l.associateId:this.associateId,this.isFromModal=!!(yield null===(r=null===(a=this.dialogData)||void 0===a?void 0:a.modalParams)||void 0===r?void 0:r.isFromModal)&&(null===(s=null===(o=this.dialogData)||void 0===o?void 0:o.modalParams)||void 0===s?void 0:s.isFromModal),console.log(this.isFromModal),this.isFromModal&&(this.record_id=null===(c=null===(d=this.dialogData)||void 0===d?void 0:d.modalParams)||void 0===c?void 0:c.record_id),console.log(this.dialogData),this.skillTypeMaster=yield this.getSkillType(),yield this.handleTenantWiseFieldConfig(),yield this.bindSavedResponse(),this.employeeDoj=yield this.getDateOfJoining(this.associateId),this.setEffectiveDateValidation(),this.loaderObject.isComponentLoading=!1,this.skillLevelMaster=yield this.getSkillLevel(),this.skillExperienceMaster=yield this.getSkillExperience(),this.languagesList=yield this.getLanguagesList(),this.sapActivateMaster=yield this.getSapActivate(),this.skillCategoryMaster=yield this.getSkillCategory(),this.skillGroupMaster=yield this.getSkillGroup(),this.createInitValue(),this.handleEffectiveDatePayloadObject(),this.handleSkillDetailsPayloadObject(),this.handleLanguageDetailsPayloadObject(),this.handleECAPayloadObject(),this.handleMetaDataPayload(),this.valueChangeListener(),this.isFromMyProfile=this._loginService.getProfile().profile.aid==this.associateId}))}handleTenantWiseFieldConfig(){return Object(i.c)(this,void 0,void 0,(function*(){let e=yield this.getFieldTenantConfig("skill_details");e.length>0&&e.forEach((e,t)=>{this.tenantFields[e.field]={isMandatory:!!e.is_mandatory,isActiveField:!!e.is_active_field},"action"==e.field&&e.is_active_field&&this.isFromModal&&(this.isAddNewSkill=!this.record_id,console.log(this.isAddNewSkill)),e.is_mandatory&&this.skillDetailFormArray.controls.forEach(t=>{let n=t.get("skillType").value,i=a.findWhere(this.skillTypeMaster,{id:n});if("sapActivate"==e.field){if(i&&(null==i?void 0:i.is_sap_active)){let n=t.get(e.field);n&&n.setValidators([h.H.required])}}else{let n=t.get(e.field);n&&n.setValidators([h.H.required])}})}),console.log(this.tenantFields)}))}setTenantFieldValidators(){Object.keys(this.tenantFields).forEach((e,t)=>{this.skillDetailFormArray.controls.forEach(t=>{this.tenantFields[e].isMandatory?t.get(e).setValidators([h.H.required]):t.get(e).clearValidators()})})}setMandatoryBasedOnSkillType(e){var t,n;let i=this.skillDetailFormArray.at(e).get("skillType").value;if(i){let l=a.where(this.skillTypeMaster,{id:i});l.length>0&&(null===(t=l[0])||void 0===t?void 0:t.is_sap_active)&&(null===(n=l[0])||void 0===n?void 0:n.is_sap_active)?(this.skillDetailFormArray.controls[e].get("sapActivate").setValidators([h.H.required]),this.showSapActive[e]=!0):(this.skillDetailFormArray.at(e).get("sapActivate").reset(),this.skillDetailFormArray.controls[e].get("sapActivate").clearValidators(),this.skillDetailFormArray.controls[e].get("sapActivate").setErrors(null),this.skillDetailFormArray.controls[e].get("sapActivate").updateValueAndValidity(),this.showSapActive[e]=!1)}}getAttachmentPluginConfig(){return Object(i.c)(this,void 0,void 0,(function*(){let e=yield this.getE360Config();if(!e.err){let t=e.data.aws.attachment_plugin_config;this.attachmentPluginConfig.destinationBucket=t.destination_bucket,this.attachmentPluginConfig.routingKey=t.routing_key}}))}changeInattachments(e,t){let n=e||null;this.skillDetailFormArray.at(t).get("certificate").patchValue(n)}getSkillAttachmentValue(e){return this.skillDetailFormArray.at(e).get("certificate").value}createInitValue(){this.effectiveDateInitValue=JSON.parse(JSON.stringify(this.effectiveDateFormGroup.value)),this.skillDetailsInitValue=JSON.parse(JSON.stringify(this.skillDetailsFormGroup.get("skillDetailArr").value)),this.languageDetailsInitValue=JSON.parse(JSON.stringify(this.languageDetailsFormGroup.get("languageDetailArr").value)),this.extraCurricularActivityInitValue=JSON.parse(JSON.stringify(this.extraCurricularActivityFormGroup.get("activityArr").value)),this.skillMetaDataInitValue=JSON.parse(JSON.stringify(this.skillMetaDataFormGroup.value))}valueChangeListener(){this.effectiveDateFormGroup.valueChanges.subscribe(e=>{this.handleEffectiveDatePayloadObject()}),this.skillDetailsFormGroup.valueChanges.subscribe(e=>{this.handleSkillDetailsPayloadObject()}),this.languageDetailsFormGroup.valueChanges.subscribe(e=>{this.handleLanguageDetailsPayloadObject()}),this.extraCurricularActivityFormGroup.valueChanges.subscribe(e=>{this.handleECAPayloadObject()}),this.skillMetaDataFormGroup.valueChanges.subscribe(e=>{this.handleMetaDataPayload()})}handleEffectiveDatePayloadObject(){let e=this.effectiveDateFormGroup.value,t=Object.keys(e);for(let n of t)this.effectiveDatePayload[n]={value:e[n],isChanged:this.checkIfObjectChanged(e,this.effectiveDateInitValue,n)};console.log(this.effectiveDatePayload)}handleSkillDetailsPayloadObject(){let e=this.skillDetailsFormGroup.get("skillDetailArr").value;this.skillDetailsPayload=[],e.forEach((e,t)=>{this.skillDetailsPayload[t]={value:e,isChanged:this.checkIfChanged(e,this.skillDetailsInitValue[t])}}),console.log(this.skillDetailsPayload)}handleLanguageDetailsPayloadObject(){let e=this.languageDetailsFormGroup.get("languageDetailArr").value;this.languageDetailsPayload=[],e.forEach((e,t)=>{this.languageDetailsPayload[t]={value:e,isChanged:this.checkIfChanged(e,this.languageDetailsInitValue[t])}})}handleECAPayloadObject(){let e=this.extraCurricularActivityFormGroup.get("activityArr").value;this.extraCurricularActivityPayload=[],e.forEach((e,t)=>{this.extraCurricularActivityPayload[t]={value:e,isChanged:this.checkIfChanged(e,this.extraCurricularActivityInitValue[t])}})}handleMetaDataPayload(){let e=this.skillMetaDataFormGroup.value,t=Object.keys(e);for(let n of t)this.skillMetaDataPayload[n]={value:e[n],isChanged:this.checkIfObjectChanged(e,this.skillMetaDataInitValue,n)};console.log(this.skillMetaDataPayload)}checkIfChanged(e,t){return!a.isEqual(e,t)}checkIfObjectChanged(e,t,n){return"object"==typeof e[n]?!a.isEqual(e[n],t[n]):e[n]!==t[n]}bindSavedResponse(){return new Promise((e,t)=>{this.subs.sink=this._edService.getSkillDetails(this.associateId,this.record_id).subscribe(t=>Object(i.c)(this,void 0,void 0,(function*(){var n;if(!t.err){if(this.isAddNewSkill&&(t.is_empty=!0),t.is_empty)this.skillDetailFormArray.push(this.createNewSkill(null)),this.languageDetailFormArray.push(this.createNewLanguage(null)),this.activityDetailFormArray.push(this.createNewActivity(null));else{let e=t.data;for(let t=0;t<e.skill_details.length;t++){let i=e.skill_details[t],l=JSON.parse(i.sap_activate),r={recordId:i.record_id,type:i.type?i.type:"",skillName:i.name?i.name:"",skillLevel:i.level?i.level:"",experience:i.experience?i.experience:"",endorse:i.endorse?i.endorse:"",isPrimarySkill:i.is_primary_skill?i.is_primary_skill:"",sapActivate:l||"",skillGroup:i.skill_group?i.skill_group:"",skillCategory:i.skill_category?i.skill_category:"",skillMaster:yield this.getSkillName(i.type)};this.skillDetailFormArray.push(this.createNewSkill(r)),this.changeInattachments(i.certificate,t);let o=a.where(this.skillTypeMaster,{id:i.type});this.showSapActive[t]=!!(o.length>0&&(null===(n=o[0])||void 0===n?void 0:n.is_sap_active))}e.lang_details.forEach((e,t)=>{this.languageDetailFormArray.push(this.createNewLanguage({recordId:e.record_id,language:e.language_name?e.language_name:"",read:!!e.can_read,write:!!e.can_write,speak:!!e.can_speak}))}),e.eca_detils.forEach((e,t)=>{let n="object"==typeof e.type?JSON.stringify(e.type):e.type;this.activityDetailFormArray.push(this.createNewActivity({recordId:e.record_id,type:n||null,activityName:e.activity_name?e.activity_name:""}))}),this.patchValueInForm(this.skillMetaDataFormGroup,"achivAndMemDescription",e,"achievements_desc"),this.patchValueInForm(this.skillMetaDataFormGroup,"courtProceedingsDesc",e,"court_proceeding_desc"),this.skillMetaDataFormGroup.get("isCourtProceedings").patchValue(!!e.is_court_proceedings)}e(!0)}})),e=>{console.log(e),t(e)})})}patchValueInForm(e,t,n,i){let l=n[i]?n[i]:"";e.get(t).patchValue(l,{emitEvent:!1})}saveDetails(){if(this.skillDetailsFormGroup.valid&&this.effectiveDateFormGroup.valid&&this.languageDetailsFormGroup.valid&&this.extraCurricularActivityFormGroup.valid)if(this.validateIfChanged()){this.loaderObject.isFormSubmitLoading=!0;let e=Object.assign(Object.assign({associateId:this.associateId,skillDetailArr:this.skillDetailsPayload,languageDetailArr:this.languageDetailsPayload,activityArr:this.extraCurricularActivityPayload,removedSkillRecord:this.removedSkillRecord,removedLangRecord:this.removedLangRecord,removedActivityRecord:this.removedActivityRecord,skillDetailsArrInitValue:this.skillDetailsInitValue,isFromMyProfile:this.isFromMyProfile},this.skillMetaDataPayload),this.effectiveDatePayload);this.handleDateFormatPayLoad(e),console.log(e),this.subs.sink=this._edService.saveSkillDetails(e).subscribe(e=>{e.err?this._toaster.showError("Error","Failed to save.,"+e.msg,2e3):(this.isFromMyProfile?this._toaster.showSuccess("Success","Skill update approval initiated successfully !",3e3):this._toaster.showSuccess("Success","Skill details updated successfully !",2e3),this.isFromModal?this.closeDialog("Updated"):this.skillDetailsRes.emit({isCompleted:!0})),this.loaderObject.isFormSubmitLoading=!1},e=>{this._toaster.showError("Error","No Approvers Found",2e3),console.log(e),this.loaderObject.isFormSubmitLoading=!1})}else this._toaster.showWarning("No changes","No new changes were made !");else this._toaster.showWarning("Invalid data","Kindly fill all mandatory fields to proceed !")}validateIfChanged(){let e=Object.assign(Object.assign({},this.skillMetaDataPayload),this.effectiveDatePayload),t=Object.keys(e);for(let i of t)if(e[i].isChanged)return!0;let n=[...this.skillDetailsPayload,...this.languageDetailsPayload,...this.extraCurricularActivityPayload];for(let i of n)if(i.isChanged)return!0;return this.removedSkillRecord.length>0||this.removedLangRecord.length>0||this.removedActivityRecord.length>0}resetFormFields(){this.effectiveDateFormGroup.reset(),this.extraCurricularActivityFormGroup.reset(),this.languageDetailsFormGroup.reset(),this.skillDetailsFormGroup.reset()}skipSection(){this.isFromModal?this.closeDialog("Close"):this.skillDetailsRes.emit({isCompleted:!1,isSkipped:!0})}closeDialog(e){this.dialogRef.close(e)}setEffectiveDateValidation(){let e=this._edService.getEffectiveDate(this.employeeDoj);this.effectiveDateFormGroup.get("effectiveDate").patchValue(e,{emitEvent:!1})}handleDateFormatPayLoad(e){e.effectiveDate.value=e.effectiveDate.value?r(e.effectiveDate.value).format("YYYY-MM-DD"):""}ngOnDestroy(){this.subs.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(l["\u0275\u0275directiveInject"](F.a),l["\u0275\u0275directiveInject"](D.a),l["\u0275\u0275directiveInject"](h.i),l["\u0275\u0275directiveInject"](l.Injector),l["\u0275\u0275directiveInject"](I.a))},e.\u0275cmp=l["\u0275\u0275defineComponent"]({type:e,selectors:[["ed-skill-details"]],inputs:{associateId:"associateId"},outputs:{skillDetailsRes:"skillDetailsRes"},features:[l["\u0275\u0275ProvidersFeature"]([{provide:s.c,useClass:d.c,deps:[s.f,d.a]},{provide:s.e,useValue:{parse:{dateInput:"DD-MMM-YYYY"},display:{dateInput:"DD-MMM-YYYY",monthYearLabel:"MMM YYYY"}}}])],decls:3,vars:2,consts:[[1,"container-fluid","skill-details"],[4,"ngIf"],[1,"d-flex","justify-content-center","mt-3"],["matTooltip","Please wait...","diameter","30"],[1,"slide-from-down","mb-4",3,"formGroup","keydown.enter"],[1,"row"],[1,"col-3","px-0","mr-4"],[1,"row","field-title"],[1,"required-star"],["appearance","outline",2,"width","100%"],["matInput","","required","","formControlName","effectiveDate","placeholder","DD-MMM-YYYY",3,"matDatepicker","min","readonly"],["matSuffix","",3,"for"],["picker1",""],[1,"slide-from-down","mb-5",3,"formGroup","keydown.enter"],[1,"row","section-header","mb-2"],["formArrayName","skillDetailArr","class","row my-2",4,"ngFor","ngForOf"],["class","row mt-3 add-link",4,"ngIf"],["class","slide-from-down mb-5",3,"formGroup","keydown.enter",4,"ngIf"],["class","slide-from-down mb-4",3,"formGroup","keydown.enter",4,"ngIf"],[1,"row","mt-4"],[1,"d-flex","flex-row"],["type","button","mat-stroked-button","",1,"cancel-btn",3,"click"],["type","button","mat-raised-button","",1,"ml-3","create-employee-btn",3,"disabled","click"],["formArrayName","skillDetailArr",1,"row","my-2"],[1,"col-12","px-0",3,"formGroupName"],["class","col-3 px-0 mr-4",4,"ngIf"],["hideMatLabel","false","required","true","placeholder","Select One","formControlName","skillName",2,"width","100%",3,"list","disableNone"],["hideMatLabel","false","required","true","placeholder","Select One","formControlName","skillLevel",2,"width","100%",3,"list","disableNone"],[1,"row","mt-2"],[3,"destinationBucket","routingKey","allowEdit","contextId","change"],["class","row",4,"ngIf"],["hideMatLabel","false","required","true","placeholder","Select One","formControlName","skillType",2,"width","100%",3,"list","disableNone","change"],["class","required-star",4,"ngIf"],["hideMatLabel","false","placeholder","Select One","formControlName","skillCategory",2,"width","100%",3,"required","list","disableNone"],["hideMatLabel","false","placeholder","Select One","formControlName","experience",2,"width","100%",3,"required","list","disableNone"],["matInput","","placeholder","Enter here","formControlName","endorse"],["hideMatLabel","false","placeholder","Select One","formControlName","skillGroup",2,"width","100%",3,"required","list","disableNone"],["formControlName","sapActivate","multiple",""],[3,"value",4,"ngFor","ngForOf"],[3,"value"],["formControlName","isPrimarySkill",1,"mr-3","field-title"],["class","col-2 my-auto px-0",4,"ngIf"],[1,"col-2","my-auto","px-0"],[1,"add-link",3,"click"],[1,"row","mt-3","add-link"],["formArrayName","languageDetailArr","class","row my-2",4,"ngFor","ngForOf"],[1,"row","mt-3"],["formArrayName","languageDetailArr",1,"row","my-2"],["hideMatLabel","false","placeholder","Select One","formControlName","language",2,"width","100%",3,"required","list","disableNone"],[1,"col-5","px-0","mr-4"],[1,"d-flex","flex-row","my-auto"],["formControlName","read",1,"mr-3","field-title"],["formControlName","write",1,"mr-3","field-title"],["formControlName","speak",1,"mr-3","field-title"],["class","row mt-1",4,"ngIf"],[1,"row","mt-1"],["class","row","formArrayName","activityArr",4,"ngFor","ngForOf"],["formArrayName","activityArr",1,"row"],["formControlName","activityType",2,"width","100%",3,"optionLabel","apiUri"],["matInput","","placeholder","Enter here","formControlName","activityName",3,"required"],["class","row mt-4",4,"ngIf"],["appearance","outline",2,"width","80%"],["formControlName","achivAndMemDescription","matInput","",2,"height","13vh"]],template:function(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"div",0),l["\u0275\u0275template"](1,O,3,0,"ng-container",1),l["\u0275\u0275template"](2,pe,28,14,"ng-container",1),l["\u0275\u0275elementEnd"]()),2&e&&(l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",t.loaderObject.isComponentLoading),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",!t.loaderObject.isComponentLoading))},directives:[c.NgIf,y.c,k.a,h.J,h.w,h.n,p.c,u.b,h.e,g.g,h.F,h.v,h.l,g.i,p.i,g.f,c.NgForOf,v.a,h.h,h.o,_.a,A.a,x.c,s.p,b.a,P.a],styles:[".skill-details[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]{font-size:16px;font-weight:500;color:#45546e}.skill-details[_ngcontent-%COMP%]   .field-title[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#6e7b8f}.skill-details[_ngcontent-%COMP%]   .required-star[_ngcontent-%COMP%]{color:#cf0001}.skill-details[_ngcontent-%COMP%]     .mat-form-field-outline, .skill-details[_ngcontent-%COMP%]     mat-datepicker-toggle{color:#b9c0ca!important}.skill-details[_ngcontent-%COMP%]   .add-link[_ngcontent-%COMP%]{text-decoration:underline;color:#f27a6c;font-size:14px;cursor:pointer;font-weight:500}.skill-details[_ngcontent-%COMP%]   .create-employee-btn[_ngcontent-%COMP%]{background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%)!important;color:#fff;height:42px}.skill-details[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{color:#45546e;height:42px}.skill-details[_ngcontent-%COMP%]     .mat-form-field-disabled{opacity:.5}.skill-details[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}"]}),e})(),he=(()=>{class e{}return e.\u0275mod=l["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=l["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[c.CommonModule,m.a,p.e,u.c,h.E,h.p,g.h,s.x,f.b,v.b,y.b,k.b,C.b,b.b,w.c,x.d,E.a]]}),e})()},NJ67:function(e,t,n){"use strict";n.d(t,"a",(function(){return i}));class i{constructor(){this.disabled=!1}onChange(e){}onTouched(e){}writeValue(e){this.value=e}registerOnChange(e){this.onChange=e}registerOnTouched(e){this.onTouched=e}setDisabledState(e){this.disabled=e}}},"TmG/":function(e,t,n){"use strict";n.d(t,"a",(function(){return y}));var i=n("fXoL"),l=n("3Pt+"),a=n("jtHE"),r=n("XNiG"),o=n("NJ67"),s=n("1G5W"),d=n("kmnG"),c=n("ofXK"),m=n("d3UM"),p=n("FKr1"),u=n("WJ5W"),h=n("Qu3c");function g(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-label"),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](e.placeholder)}}function f(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-option",7),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275property"]("value",null),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](e.disableNone?"Select One":"None")}}function v(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"mat-option",8),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const n=t.$implicit;return i["\u0275\u0275nextContext"]().emitChanges(n)})),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;i["\u0275\u0275property"]("value",e.id||e._id)("matTooltip",e.name),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let y=(()=>{class e extends o.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new l.j,this.fieldFilterCtrl=new l.j,this.list=[],this.required=!1,this.disableNone=!1,this.valueChange=new i.EventEmitter,this.disabled=!1,this.hasNoneOption=!0,this.filteredList=new a.a,this.change=new i.EventEmitter,this.hideMatLabel=!1,this._onDestroy=new r.b,this.emitChanges=e=>{this.change.emit(e)}}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(s.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(s.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](i.Renderer2))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search"]],inputs:{list:"list",placeholder:"placeholder",required:"required",disableNone:"disableNone",disabled:"disabled",hasNoneOption:"hasNoneOption",hideMatLabel:"hideMatLabel"},outputs:{valueChange:"valueChange",change:"change"},features:[i["\u0275\u0275ProvidersFeature"]([{provide:l.t,useExisting:Object(i.forwardRef)(()=>e),multi:!0}]),i["\u0275\u0275InheritDefinitionFeature"],i["\u0275\u0275NgOnChangesFeature"]],decls:9,vars:11,consts:[["appearance","outline"],[4,"ngIf"],[3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value",4,"ngIf"],[3,"value","matTooltip","click",4,"ngFor","ngForOf"],[3,"value"],[3,"value","matTooltip","click"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"mat-form-field",0),i["\u0275\u0275template"](1,g,2,1,"mat-label",1),i["\u0275\u0275elementStart"](2,"mat-select",2,3),i["\u0275\u0275elementStart"](4,"mat-option"),i["\u0275\u0275element"](5,"ngx-mat-select-search",4),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](6,f,2,2,"mat-option",5),i["\u0275\u0275template"](7,v,2,3,"mat-option",6),i["\u0275\u0275pipe"](8,"async"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",!t.hideMatLabel),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.hasNoneOption),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngForOf",i["\u0275\u0275pipeBind1"](8,9,t.filteredList)))},directives:[d.c,c.NgIf,m.c,l.v,l.k,l.F,p.p,u.a,c.NgForOf,d.g,h.a],pipes:[c.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%}"]}),e})()},hJL4:function(e,t,n){"use strict";n.d(t,"a",(function(){return m}));var i=n("mrSG"),l=n("XNiG"),a=n("xG9w"),r=n("fXoL"),o=n("tk/3"),s=n("LcQX"),d=n("XXEo"),c=n("flaP");let m=(()=>{class e{constructor(e,t,n,i){this.http=e,this.UtilityService=t,this.loginService=n,this.roleService=i,this.msg=new l.b,this.taskStatusColor=[],this.approveRequest=(e,t)=>this.http.post("/api/isa/request/approveResourceRequest",{requestDetails:e,approverOid:t}),this.rejectRequest=(e,t)=>this.http.post("/api/isa/request/rejectResourceRequest",{requestDetails:e,approverOid:t}),this.getTaskStatusColor()}getAllRequestsOfUser(e,t,n,i,l,a,r){let o=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getAllRequestsOfUser",{oid:e,statusId:t,statusCode:n,objectIds:i,skip:l,limit:a,filterConfig:r,orgIds:o})}getAllRoleAccess(){return a.where(this.roleService.roles,{application_id:139})}getRequestsBasedOnStatus(e,t,n,i,l,a,r){let o=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getRequestsBasedOnStatus",{oid:e,statusId:t,statusCode:n,objectIds:i,skip:l,limit:a,filterConfig:r,orgIds:o})}getRequestsForAwaitingApproval(e,t,n,i){return this.http.post("/api/isa/request/getRequestsForAwaitingApproval",{oid:e,skip:t,limit:n,filterConfig:i})}getObjectIdsBasedOnOid(e){return this.http.post("/api/isa/request/getObjectIdsBasedOnOid",{oid:e})}getStatusCountBasedOnOid(e,t,n,i){let l=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getStatusCountBasedOnOid",{oid:e,objectIds:t,dataTypeArray:n,filterConfig:i,orgIds:l})}getHeaderCount(e){return this.http.post("/api/isa/request/getHeaderCount",{oid:e})}getRequestBasedOnStatus(e,t){return this.http.post("/api/isa/request/getRequestBasedOnStatus",{oid:e,status:t})}getDataTypeArray(){return this.http.post("/api/isa/request/getISAStatusMasterDataUDRF",{})}sendMsg(e){this.msg.next(e)}getMsg(){return this.msg.asObservable()}getTaskStatusColor(){return new Promise((e,t)=>{this.http.post("/api/isa/request/getTaskStatusNameColor",{}).subscribe(t=>(this.taskStatusColor=t,e(t)),e=>{})})}getAllRequestsForRmg(e){return this.http.post("/api/isa/request/getAllRequestsForRmg",{filterConfig:e})}getTotalForRmg(e){return new Promise((t,n)=>{this.http.post("/api/isa/request/getTotalForRmg",{filterConfig:e}).subscribe(e=>t(e),e=>{})})}getDefaultISAStatusToDisplay(){return this.http.post("/api/isa/request/getISADefaultStatusToDisplay",{})}getCTCbreakUpForSkillSet(e){return new Promise((t,n)=>{this.http.post("/api/isa/request/getCTCbreakUpForSkillSet",e).subscribe(e=>t(e.data),e=>{n(e)})})}getCurrentUserRole(){return this.roleService&&this.roleService.roles&&this.roleService.roles.length>0?this.roleService.roles[0].role_id:null}getTemplateForUser(e){return this.http.post("/api/isa/request/getVisibleFormFieldsForUser",e)}saveAndApproveRequest(e){return this.http.post("/api/isa/request/updBudgetInfoByRequestId",e)}getVisibilityMatrix(e){return this.http.post("/api/isa/request/getAuthorzedFieldsByRoleId",e)}getISAAttachmentFromS3(e){return this.http.post("/api/isa/attachment/getISAAttachmentFromS3",{key:e})}getSkillType(){return this.http.post("/api/isa/request/getSkillType",{})}getSkillLevel(){return this.http.post("/api/isa/request/getSkillLevel",{})}getSkillExperience(){return this.http.post("/api/isa/request/getSkillExperience",{})}getSkillName(e){return this.http.post("/api/isa/request/getSkillName",{skill_type_id:e})}getIsaActivityReportDownload(){return this.http.post("/api/isa/request/getIsaActivityReportDownload",{})}getISASLAreport(e){return this.http.post("/api/isa/request/getISASLAreport",{filterConfig:e})}getISACustomReport(e){return this.http.post("/api/isa/request/getISACustomReport",{filterConfig:e})}getPositionType(){return this.http.post("/api/employee360/masterData/getPositionType",{})}calculateNetCostBasedOnPosition(e,t,n,l,r,o,s){return Object(i.c)(this,void 0,void 0,(function*(){let i;i=o&&o.length>1&&(yield this.getManpowerCostByOId(o,n,r,2))||(yield this.getManpowerCostBasedOnPosition(e,t,n,r,s));let d=yield this.getNonManpowerCost(t,n,l,r,2),c=yield this.getAllocatedCost(),m=0;m=(i?i.cost:0)+d.length>0?a.reduce(a.pluck(d,"cost"),(e,t)=>e+t,0):0;let p=c.length>0?a.reduce(a.pluck(c,"percentage"),(e,t)=>e+t,0):0;return{cost:m,currency:i&&i.currency_code?i.currency_code:"",manpowerCost:i,nonManpowerCost:d,allocatedCost:c,allocatedCostValue:m*(p/100)}}))}getManpowerCostBasedOnPosition(e,t,n,i,l){return new Promise((a,r)=>{this.http.post("/api/isa/configuration/getManpowerCostBasedOnPosition",{skillRoleId:e,nationalityId:t,locationGroupId:n,unit:i,position:l}).subscribe(e=>a(e),e=>(console.log(e),r(e)))})}getNonManpowerCost(e,t,n,i,l){return new Promise((a,r)=>{this.http.post("/api/project/getNonManpowerCost",{nationalityId:e,locationGroupId:t,locationId:n,unit:i,currency_id:l}).subscribe(e=>a(e),e=>(console.log(e),r(e)))})}getAllocatedCost(){return new Promise((e,t)=>{this.http.post("/api/project/getAllocatedCost","").subscribe(t=>e(t),e=>(console.log(e),t(e)))})}getManpowerCostByOId(e,t,n,i){return new Promise((l,a)=>{this.http.post("/api/project/getEmployeeSalary",{oid:e,location_group_id:t,unit_id:n,currency_id:i}).subscribe(e=>l(e),e=>(console.log(e),a(e)))})}getSkillNameMaster(){return this.http.post("/api/isa/configuration/getSkillName",{})}getVendorOid(e,t){return this.http.post("/api/isa/request/getVendorOid",{vendorNames:e,docID:t})}getVendorList(e,t){return this.http.post("/api/isa/request/getVendorList",{documentID:e,limitval:t})}removeVendor(e,t){return this.http.post("/api/isa/request/removeVendors",{vendorOid:e,documentID:t})}checkVendorOrNot(){return this.http.post("/api/isa/request/checkVendorOrNot",{})}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275inject"](o.c),r["\u0275\u0275inject"](s.a),r["\u0275\u0275inject"](d.a),r["\u0275\u0275inject"](c.a))},e.\u0275prov=r["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},m5YA:function(e,t,n){"use strict";n.d(t,"a",(function(){return h}));var i=n("0IaG"),l=n("fXoL"),a=n("tk/3"),r=n("bTqV"),o=n("NFeN"),s=n("Qu3c"),d=n("ofXK"),c=n("w4ga");function m(e,t){1&e&&(l["\u0275\u0275elementStart"](0,"div"),l["\u0275\u0275elementStart"](1,"div",8),l["\u0275\u0275element"](2,"img",9),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](3,"div",8),l["\u0275\u0275text"](4," Please wait, we are opening the file! "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]())}function p(e,t){if(1&e){const e=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"ngx-doc-viewer",10),l["\u0275\u0275listener"]("loaded",(function(){return l["\u0275\u0275restoreView"](e),l["\u0275\u0275nextContext"]().isLoaded=!0})),l["\u0275\u0275elementEnd"]()}if(2&e){const e=l["\u0275\u0275nextContext"]();l["\u0275\u0275property"]("url",e.data.selectedFileUrl)}}function u(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div",11),l["\u0275\u0275element"](1,"img",12),l["\u0275\u0275elementEnd"]()),2&e){const e=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("src",e.data.selectedFileUrl,l["\u0275\u0275sanitizeUrl"])}}let h=(()=>{class e{constructor(e,t,n){this.data=e,this.dialogRef=t,this.http=n,this.isLoaded=!1,this.expHeaderId=e.expHeaderId}ngOnInit(){}getBlobUrl(e){return this.http.get(e,{responseType:"blob"})}downloadFile(e){if(this.expHeaderId){let t=e.substring(e.lastIndexOf("/")+1,e.indexOf("?"));this.getBlobUrl(e).subscribe(e=>{let n=URL.createObjectURL(e),i=document.createElement("a");i.href=n;let l=decodeURIComponent(t.replace(/%20/g," "));i.download="EX"+this.expHeaderId+"-"+l,i.click()})}else window.open(e)}}return e.\u0275fac=function(t){return new(t||e)(l["\u0275\u0275directiveInject"](i.a),l["\u0275\u0275directiveInject"](i.h),l["\u0275\u0275directiveInject"](a.c))},e.\u0275cmp=l["\u0275\u0275defineComponent"]({type:e,selectors:[["attachment-doc-viewer"]],decls:13,vars:3,consts:[[1,"row"],[1,"col-10"],[1,"col-2"],["mat-icon-button","",3,"click"],["mat-icon-button","","matTooltip","Close",3,"click"],[4,"ngIf"],["viewer","google","style","height: 100%",3,"url","loaded",4,"ngIf","ngIfElse"],["showimg",""],[1,"d-flex","align-items-center","justify-content-center"],["src","https://assets.kebs.app/images/spinner.svg","alt",""],["viewer","google",2,"height","100%",3,"url","loaded"],[1,"d-flex","align-items-center","justify-content-center","mt-5"],["alt","",2,"height","80%","width","80%",3,"src"]],template:function(e,t){if(1&e&&(l["\u0275\u0275elementStart"](0,"div",0),l["\u0275\u0275element"](1,"div",1),l["\u0275\u0275elementStart"](2,"div",2),l["\u0275\u0275elementStart"](3,"button",3),l["\u0275\u0275listener"]("click",(function(){return t.downloadFile(t.data.selectedFileUrl)})),l["\u0275\u0275elementStart"](4,"mat-icon"),l["\u0275\u0275text"](5,"download"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](6,"button",4),l["\u0275\u0275listener"]("click",(function(){return t.dialogRef.close()})),l["\u0275\u0275elementStart"](7,"mat-icon"),l["\u0275\u0275text"](8,"close"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](9,m,5,0,"div",5),l["\u0275\u0275template"](10,p,1,1,"ngx-doc-viewer",6),l["\u0275\u0275template"](11,u,2,1,"ng-template",null,7,l["\u0275\u0275templateRefExtractor"])),2&e){const e=l["\u0275\u0275reference"](12);l["\u0275\u0275advance"](9),l["\u0275\u0275property"]("ngIf",!t.isLoaded&&"png"!=t.data.fileFormat&&"jpg"!=t.data.fileFormat&&"jpeg"!=t.data.fileFormat&&"JPEG"!=t.data.fileFormat&&"JPG"!=t.data.fileFormat),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf","png"!=t.data.fileFormat&&"jpg"!=t.data.fileFormat&&"JPEG"!=t.data.fileFormat&&"JPG"!=t.data.fileFormat&&"jpeg"!=t.data.fileFormat)("ngIfElse",e)}},directives:[r.a,o.a,s.a,d.NgIf,c.a],styles:[""]}),e})()},qFYv:function(e,t,n){"use strict";n.d(t,"a",(function(){return b}));var i=n("fXoL"),l=n("tk/3"),a=n("XNiG"),r=n("3Pt+"),o=n("NJ67"),s=n("1G5W"),d=n("Kj3r"),c=n("XXEo"),m=n("kmnG"),p=n("ofXK"),u=n("qFsG"),h=n("/1cH"),g=n("bTqV"),f=n("NFeN"),v=n("Qu3c"),y=n("FKr1");function k(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",7),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"]().checkAndClearInput(!0)})),i["\u0275\u0275elementStart"](1,"mat-icon",8),i["\u0275\u0275text"](2," close "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}}function C(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"mat-option",9),i["\u0275\u0275elementStart"](1,"div",10),i["\u0275\u0275element"](2,"div"),i["\u0275\u0275element"](3,"div"),i["\u0275\u0275element"](4,"div"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]())}function S(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"mat-option",12),i["\u0275\u0275listener"]("onSelectionChange",(function(){i["\u0275\u0275restoreView"](e);const n=t.$implicit;return i["\u0275\u0275nextContext"](2).resultClicked(n)})),i["\u0275\u0275elementStart"](1,"span"),i["\u0275\u0275elementStart"](2,"small",13),i["\u0275\u0275text"](3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](2),i["\u0275\u0275classMap"](n.ngClasses),i["\u0275\u0275propertyInterpolate3"]("matTooltip","",e[n.optionLabel[0]],"",e[n.optionLabel[1]]?" - ":"","",e[n.optionLabel[1]]?e[n.optionLabel[1]]:"",""),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate3"]("",e[n.optionLabel[0]],"",e[n.optionLabel[1]]?" - ":"","",e[n.optionLabel[1]]?e[n.optionLabel[1]]:"","")}}function x(e,t){if(1&e&&(i["\u0275\u0275elementContainerStart"](0),i["\u0275\u0275template"](1,S,4,9,"mat-option",11),i["\u0275\u0275elementContainerEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngForOf",e.searchResult)}}let b=(()=>{class e extends o.a{constructor(e,t){super(),this.http=e,this._login=t,this.label="",this.placeholder="",this.isRequired=!1,this.isReadOnly=!1,this.emitVal=new i.EventEmitter,this.optClicked=!1,this.searchTextControl=new r.j,this.searchResult=[],this.isLoading=!1,this.ifPlaceholderExists=!1,this._onDestroy=new a.b,this.resultClicked=e=>{setTimeout(()=>{this.optClicked=!0,this.outputParam?(this.onChange(e[this.outputParam]),this.emitVal.emit(e[this.outputParam])):(this.onChange(JSON.stringify(e)),this.emitVal.emit(JSON.stringify(e))),setTimeout(()=>{document.getElementById("searchTextInput").blur()},100),this.searchTextControl.patchValue(e[this.optionLabel[0]]+(this.optionLabel.length>1?" - "+e[this.optionLabel[1]]:""))},100)}}ngOnInit(){""!=this.placeholder&&""==this.label&&(this.label=this.placeholder,this.ifPlaceholderExists=!0),this.searchTxtControlSubscription=this.searchTextControl.valueChanges.pipe(Object(s.a)(this._onDestroy),Object(d.a)(700)).subscribe(e=>{e&&e.length>0&&this.callApiFn(e)})}callInitApi(){null==this.searchTextControl.value&&0==this.searchResult.length&&this.callApiFn("")}callApiFn(e){this.isLoading=!0;let t={searchParameter:e,options:this.bodyParams},n={headers:new l.f({Authorization:"Bearer "+this._login.getToken()})};this.http.post(this.apiUri,t,n).pipe(Object(s.a)(this._onDestroy)).subscribe(e=>{this.isLoading=!1,this.searchResult=e},e=>{console.error(e)})}checkAndClearInput(e){this.optClicked&&!e||0!=this.isReadOnly||(this.searchTextControl.reset(),this.onChange(null),this.emitVal.emit(null)),e&&setTimeout(()=>{document.getElementById("searchTextInput").blur(),setTimeout(()=>{document.getElementById("searchTextInput").focus()},100)},100)}writeValue(e){if(e){let t=JSON.parse(e);this.searchTextControl.patchValue(t[this.optionLabel[0]]+(this.optionLabel.length>1?" - "+t[this.optionLabel[1]]:""))}else this.searchTextControl.patchValue("")}resetSuggestion(){this.searchTextControl.patchValue("")}ngOnDestroy(){this.searchTxtControlSubscription&&this.searchTxtControlSubscription.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](l.c),i["\u0275\u0275directiveInject"](c.a))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search-huge-input"]],inputs:{apiUri:"apiUri",bodyParams:"bodyParams",outputParam:"outputParam",optionLabel:"optionLabel",optionValue:"optionValue",label:"label",placeholder:"placeholder",ngClasses:"ngClasses",isRequired:"isRequired",isReadOnly:"isReadOnly"},outputs:{emitVal:"emitVal"},features:[i["\u0275\u0275ProvidersFeature"]([{provide:r.t,useExisting:Object(i.forwardRef)(()=>e),multi:!0}]),i["\u0275\u0275InheritDefinitionFeature"]],decls:9,vars:11,consts:[["appearance","outline",3,"ngClass"],["id","searchTextInput","matInput","","autocomplete","off",3,"placeholder","matAutocomplete","formControl","required","readonly","focusout","focusin","keyup"],["mat-button","","matSuffix","","mat-icon-button","","style","height: 30px; width: 30px; line-height: 1",3,"click",4,"ngIf"],[3,"displayWith"],["auto","matAutocomplete"],["class","is-loading",4,"ngIf"],[4,"ngIf"],["mat-button","","matSuffix","","mat-icon-button","",2,"height","30px","width","30px","line-height","1",3,"click"],["matTooltip","Clear",2,"font-size","20px !important","color","#66615b !important"],[1,"is-loading"],[1,"lds-facebook"],[3,"onSelectionChange",4,"ngFor","ngForOf"],[3,"onSelectionChange"],[2,"padding-left","12px",3,"matTooltip"]],template:function(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-form-field",0),i["\u0275\u0275elementStart"](1,"mat-label"),i["\u0275\u0275text"](2),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](3,"input",1),i["\u0275\u0275listener"]("focusout",(function(){return t.checkAndClearInput(!1)}))("focusin",(function(){return t.callInitApi()}))("keyup",(function(){return t.optClicked=!1})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](4,k,3,0,"button",2),i["\u0275\u0275elementStart"](5,"mat-autocomplete",3,4),i["\u0275\u0275template"](7,C,5,0,"mat-option",5),i["\u0275\u0275template"](8,x,2,1,"ng-container",6),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275reference"](6);i["\u0275\u0275property"]("ngClass",t.ifPlaceholderExists?"label-no-float":""),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate"](t.label),i["\u0275\u0275advance"](1),i["\u0275\u0275propertyInterpolate"]("placeholder",t.label),i["\u0275\u0275property"]("matAutocomplete",e)("formControl",t.searchTextControl)("required",t.isRequired)("readonly",t.isReadOnly),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null!=t.searchTextControl.value),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("displayWith",t.displayFn),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf",t.isLoading),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",0==t.isLoading)}},directives:[m.c,p.NgClass,m.g,u.b,h.d,r.e,r.v,r.k,r.F,p.NgIf,h.b,g.a,m.i,f.a,v.a,y.p,p.NgForOf],styles:[".label-no-float.mat-form-field-appearance-outline.mat-form-field-should-float .mat-form-field-label{display:none}  .label-no-float.mat-form-field-appearance-outline.mat-form-field-should-float .mat-form-field-outline-gap{border-top-color:initial}.mat-form-field[_ngcontent-%COMP%]{width:100%}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding-bottom:8px!important}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-underline{bottom:0}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-infix{padding:.65em 0!important;border-top:.54375em solid transparent!important;font-size:13px}.mat-form-field[_ngcontent-%COMP%]     .mat-select-arrow{margin:0 4px!important}.lds-facebook[_ngcontent-%COMP%]{display:inline-block;position:sticky;width:80px;height:24px;margin-left:110px}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{display:inline-block;position:absolute;left:4px;width:5px;background:#cf0001;animation:lds-facebook 1.2s cubic-bezier(0,.5,.5,1) infinite}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:first-child{left:8px;animation-delay:-.24s}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:nth-child(2){left:16px;animation-delay:-.12s}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:nth-child(3){left:24px;animation-delay:0}@keyframes lds-facebook{0%{top:8px;height:32px}50%,to{top:12px;height:16px}}"]}),e})()},ucYs:function(e,t,n){"use strict";n.d(t,"a",(function(){return s}));var i=n("mrSG"),l=n("xG9w"),a=n("fXoL"),r=n("tk/3"),o=n("BVzC");let s=(()=>{class e{constructor(e,t){this.http=e,this.errorService=t,this.workflowStatusList=[],this.getWorkflowStatusList()}getWorkflowStatusList(){this.http.post("/api/wfPrimary/getWorkflowStatusList","").subscribe(e=>{this.workflowStatusList=e.data},e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server While Getting Workflow Status List",e&&e.params?e.params:e&&e.error?e.error.params:{})})}getWorkflowProperties(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/getWorkflowProperties",{applicationId:e}).subscribe(e=>t(e),e=>n(e))})}getWorkflowPropertiesByWorkflowId(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/getWorkflowPropertiesByWorkflowId",{workflowId:e}).subscribe(e=>t(e),e=>n(e))})}getApproversHierarchy(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/getApproversHierarchy",e).subscribe(e=>t(e),e=>n(e))})}createWorkflowItems(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/createWorkflowItems",e).subscribe(e=>t(e),e=>n(e))})}getWorkflowDetails(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/getWorkflowDetails",{workflowId:e}).subscribe(e=>t(e),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while getting workflow details",e&&e.params?e.params:e&&e.error?e.error.params:{})})})}formatApproversHierarchy(e,t){return Object(i.c)(this,void 0,void 0,(function*(){0==e.length&&t.cc0&&t.cc0.appr0&&t.cc0.appr0.length>0&&e.push([{approvalType:"I",purposeOfInsertingIntoArray:"Just to ensure that 'I' type data is used, if it exists"}]);for(let n=0;n<e.length;n++){let i=[],a=l.keys(t["cc"+n]);for(let l=0;l<a.length;l++)for(let r=0;r<t["cc"+n][a[l]].length;r++){let o={name:t["cc"+n][a[l]][r].DELEGATE_NAME,oid:t["cc"+n][a[l]][r].DELEGATE_OID,level:l+1,designation:t["cc"+n][a[l]][r].DELEGATE_DESIGNATION_NAME,is_delegated:t["cc"+n][a[l]][r].IS_DELEGATED,role:t["cc"+n][a[l]][r].DELEGATE_ROLE_NAME};if(1==t["cc"+n][a[l]][r].IS_DELEGATED&&(o.delegated_by={name:t["cc"+n][a[l]][r].APPROVER_NAME,oid:t["cc"+n][a[l]][r].APPROVER_OID,level:l+1,designation:t["cc"+n][a[l]][r].APPROVER_DESIGNATION_NAME}),i.push(o),n==e.length-1&&l==a.length-1&&r==t["cc"+n][a[l]].length-1)return i}}}))}storeComments(e,t,n){return new Promise((i,l)=>{this.http.post("/api/wfPrimary/updateComments",{workflowHeaderId:e,newComments:t,commentor:n}).subscribe(e=>i(e),e=>(this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server Error while Updating Workflow Comments",e&&e.params?e.params:e&&e.error?e.error.params:{}),l(e)))})}updateWorkflowItems(e){return new Promise((t,n)=>{this.http.post("/api/wfPrimary/updateWorkflowItems",e).subscribe(e=>t(e),e=>(console.log(e),n(e)))})}formatApproversHierarchyForOpportunityApprovalActivity(e){return Object(i.c)(this,void 0,void 0,(function*(){for(let t=0;t<1;t++){let n=[],i=l.keys(e["cc"+t]);for(let l=0;l<i.length;l++)for(let a=0;a<e["cc"+t][i[l]].length;a++){let r={name:e["cc"+t][i[l]][a].DELEGATE_NAME,oid:e["cc"+t][i[l]][a].DELEGATE_OID,level:e["cc"+t][i[l]][a].APPROVAL_ORDER,designation:e["cc"+t][i[l]][a].DELEGATE_DESIGNATION_NAME,is_delegated:e["cc"+t][i[l]][a].IS_DELEGATED};if(1==e["cc"+t][i[l]][a].IS_DELEGATED&&(r.delegated_by={name:e["cc"+t][i[l]][a].APPROVER_NAME,oid:e["cc"+t][i[l]][a].APPROVER_OID,level:e["cc"+t][i[l]][a].APPROVAL_ORDER,designation:e["cc"+t][i[l]][a].APPROVER_DESIGNATION_NAME}),n.push(r),l==i.length-1&&a==e["cc"+t][i[l]].length-1)return n}}}))}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275inject"](r.c),a["\u0275\u0275inject"](o.a))},e.\u0275prov=a["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},x7lz:function(e,t,n){"use strict";n.r(t),n.d(t,"SkillDetailsComponent",(function(){return fe})),n.d(t,"SkillDetailsModule",(function(){return ve}));var i=n("mrSG"),l=n("33Jv"),a=n("ofXK"),r=n("Xi0T"),o=n("NFeN"),s=n("bTqV"),d=n("Xa2L"),c=n("Qu3c"),m=n("STbY"),p=n("+0xr"),u=n("Dh3D"),h=n("f6nW"),g=n("f0Cb"),f=n("fXoL"),v=n("jAlA"),y=n("1A3m"),k=n("0IaG"),C=n("XXEo");const S=["menuTrigger"],x=["skillTbSort"],b=["extraCurricularTbSort"],w=["languagesKnownTbSort"];function E(e,t){1&e&&(f["\u0275\u0275elementContainerStart"](0),f["\u0275\u0275elementStart"](1,"div",2),f["\u0275\u0275element"](2,"mat-spinner",3),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementContainerEnd"]())}function F(e,t){if(1&e){const e=f["\u0275\u0275getCurrentView"]();f["\u0275\u0275elementStart"](0,"div",25),f["\u0275\u0275elementStart"](1,"mat-card",26),f["\u0275\u0275elementStart"](2,"div",27),f["\u0275\u0275elementStart"](3,"div",8),f["\u0275\u0275elementStart"](4,"div",28),f["\u0275\u0275elementStart"](5,"span"),f["\u0275\u0275elementStart"](6,"mat-icon",29),f["\u0275\u0275text"](7,"info_outline"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](8,"span",30),f["\u0275\u0275text"](9),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](10,"span",31),f["\u0275\u0275listener"]("click",(function(){return f["\u0275\u0275restoreView"](e),f["\u0275\u0275nextContext"](2).openApprovalPendingDialog()})),f["\u0275\u0275text"](11,"View"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]()}if(2&e){const e=f["\u0275\u0275nextContext"](2);f["\u0275\u0275advance"](9),f["\u0275\u0275textInterpolate1"]("",null==e.approvalPendings[0]||null==e.approvalPendings[0].submission_item?null:e.approvalPendings[0].submission_item.approval_fields.length," Data Update is under Validation .")}}function D(e,t){if(1&e){const e=f["\u0275\u0275getCurrentView"]();f["\u0275\u0275elementStart"](0,"button",32),f["\u0275\u0275listener"]("click",(function(){return f["\u0275\u0275restoreView"](e),f["\u0275\u0275nextContext"](2).editSkillDetails(null)})),f["\u0275\u0275pipe"](1,"async"),f["\u0275\u0275elementStart"](2,"span",33),f["\u0275\u0275text"](3,"Add Skill "),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]()}if(2&e){const e=f["\u0275\u0275nextContext"](2);f["\u0275\u0275propertyInterpolate"]("disabled",!e.isWorkflowNotActive||f["\u0275\u0275pipeBind1"](1,1,e.$isEmpRetired))}}function I(e,t){if(1&e){const e=f["\u0275\u0275getCurrentView"]();f["\u0275\u0275elementStart"](0,"button",34),f["\u0275\u0275listener"]("click",(function(){return f["\u0275\u0275restoreView"](e),f["\u0275\u0275nextContext"](2).editSkillDetails()})),f["\u0275\u0275pipe"](1,"async"),f["\u0275\u0275text"](2," Edit "),f["\u0275\u0275elementEnd"]()}if(2&e){const e=f["\u0275\u0275nextContext"](2);f["\u0275\u0275propertyInterpolate"]("disabled",!e.isWorkflowNotActive||!e.showEditButton||f["\u0275\u0275pipeBind1"](1,1,e.$isEmpRetired))}}function _(e,t){1&e&&(f["\u0275\u0275elementStart"](0,"div",43),f["\u0275\u0275elementStart"](1,"span",44),f["\u0275\u0275text"](2,"Add Skill Details By Clicking the Button Below"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]())}function A(e,t){if(1&e){const e=f["\u0275\u0275getCurrentView"]();f["\u0275\u0275elementStart"](0,"div",39),f["\u0275\u0275elementStart"](1,"button",45),f["\u0275\u0275listener"]("click",(function(){return f["\u0275\u0275restoreView"](e),f["\u0275\u0275nextContext"](3).editSkillDetails()})),f["\u0275\u0275pipe"](2,"async"),f["\u0275\u0275pipe"](3,"async"),f["\u0275\u0275text"](4," Add Details > "),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]()}if(2&e){const e=f["\u0275\u0275nextContext"](3);f["\u0275\u0275advance"](1),f["\u0275\u0275propertyInterpolate"]("disabled",f["\u0275\u0275pipeBind1"](2,2,e.$isEmpRetired)),f["\u0275\u0275property"]("ngClass",f["\u0275\u0275pipeBind1"](3,4,e.$isEmpRetired)?"disabled-add-button":"add-details-btn")}}function P(e,t){if(1&e&&(f["\u0275\u0275elementStart"](0,"div",35),f["\u0275\u0275elementStart"](1,"div",36),f["\u0275\u0275elementStart"](2,"div",37),f["\u0275\u0275element"](3,"img",38),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](4,"div",39),f["\u0275\u0275elementStart"](5,"span",40),f["\u0275\u0275text"](6,"No Data Here"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275template"](7,_,3,0,"div",41),f["\u0275\u0275template"](8,A,5,6,"div",42),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]()),2&e){const e=f["\u0275\u0275nextContext"](2);f["\u0275\u0275advance"](7),f["\u0275\u0275property"]("ngIf",e.showAddButton),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",e.showAddButton)}}function O(e,t){1&e&&(f["\u0275\u0275elementStart"](0,"div",50),f["\u0275\u0275elementStart"](1,"span",40),f["\u0275\u0275text"](2,"No Data Here"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]())}function M(e,t){if(1&e&&(f["\u0275\u0275elementStart"](0,"mat-header-cell",62),f["\u0275\u0275elementStart"](1,"span",63),f["\u0275\u0275text"](2),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]()),2&e){const e=f["\u0275\u0275nextContext"](2).$implicit;f["\u0275\u0275advance"](2),f["\u0275\u0275textInterpolate"](e.colName)}}function N(e,t){if(1&e){const e=f["\u0275\u0275getCurrentView"]();f["\u0275\u0275elementStart"](0,"mat-cell",64),f["\u0275\u0275elementStart"](1,"span",65),f["\u0275\u0275elementStart"](2,"Button",22),f["\u0275\u0275listener"]("click",(function(){f["\u0275\u0275restoreView"](e);const n=t.$implicit;return f["\u0275\u0275nextContext"](6).editSkillDetails(n)})),f["\u0275\u0275elementStart"](3,"mat-icon"),f["\u0275\u0275text"](4,"edit"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=f["\u0275\u0275nextContext"](2).$implicit;f["\u0275\u0275property"]("ngClass",n.className)("matTooltip",n.isToolTip?null==n?null:n.toolTipVal(e):"")}}function T(e,t){1&e&&(f["\u0275\u0275elementContainerStart"](0),f["\u0275\u0275template"](1,M,3,1,"mat-header-cell",60),f["\u0275\u0275template"](2,N,5,2,"mat-cell",61),f["\u0275\u0275elementContainerEnd"]())}function L(e,t){if(1&e&&(f["\u0275\u0275elementStart"](0,"mat-header-cell",62),f["\u0275\u0275elementStart"](1,"span",63),f["\u0275\u0275text"](2),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]()),2&e){const e=f["\u0275\u0275nextContext"](2).$implicit;f["\u0275\u0275advance"](2),f["\u0275\u0275textInterpolate"](e.colName)}}function V(e,t){if(1&e){const e=f["\u0275\u0275getCurrentView"]();f["\u0275\u0275elementStart"](0,"mat-cell",64),f["\u0275\u0275elementStart"](1,"span",65),f["\u0275\u0275elementStart"](2,"Button",22),f["\u0275\u0275listener"]("click",(function(){f["\u0275\u0275restoreView"](e);const n=t.$implicit;return f["\u0275\u0275nextContext"](6).removeSkill(n)})),f["\u0275\u0275elementStart"](3,"mat-icon"),f["\u0275\u0275text"](4,"delete"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=f["\u0275\u0275nextContext"](2).$implicit;f["\u0275\u0275property"]("ngClass",n.className)("matTooltip",n.isToolTip?null==n?null:n.toolTipVal(e):"")}}function j(e,t){1&e&&(f["\u0275\u0275elementContainerStart"](0),f["\u0275\u0275template"](1,L,3,1,"mat-header-cell",60),f["\u0275\u0275template"](2,V,5,2,"mat-cell",61),f["\u0275\u0275elementContainerEnd"]())}function R(e,t){if(1&e&&(f["\u0275\u0275elementStart"](0,"mat-header-cell",62),f["\u0275\u0275elementStart"](1,"span",66),f["\u0275\u0275text"](2),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]()),2&e){const e=f["\u0275\u0275nextContext"](2).$implicit;f["\u0275\u0275advance"](2),f["\u0275\u0275textInterpolate"](e.colName)}}function G(e,t){if(1&e&&(f["\u0275\u0275elementStart"](0,"mat-cell",64),f["\u0275\u0275elementStart"](1,"span",65),f["\u0275\u0275text"](2),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=f["\u0275\u0275nextContext"](2).$implicit;f["\u0275\u0275property"]("ngClass",n.className)("matTooltip",n.isToolTip?null==n?null:n.toolTipVal(e):""),f["\u0275\u0275advance"](2),f["\u0275\u0275textInterpolate"](n.cell(e))}}function q(e,t){1&e&&(f["\u0275\u0275template"](0,R,3,1,"mat-header-cell",60),f["\u0275\u0275template"](1,G,3,3,"mat-cell",61))}function K(e,t){if(1&e&&(f["\u0275\u0275elementContainerStart"](0,57),f["\u0275\u0275template"](1,T,3,0,"ng-container",58),f["\u0275\u0275template"](2,j,3,0,"ng-container",58),f["\u0275\u0275template"](3,q,2,0,"ng-template",null,59,f["\u0275\u0275templateRefExtractor"]),f["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit,n=f["\u0275\u0275reference"](4),i=f["\u0275\u0275nextContext"](4);f["\u0275\u0275property"]("matColumnDef",e.colKey),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf","action"===e.colKey&&e.show)("ngIfElse",i.second),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf","remove"===e.colKey&&e.show)("ngIfElse",n)}}function B(e,t){1&e&&f["\u0275\u0275element"](0,"mat-header-row")}function U(e,t){1&e&&f["\u0275\u0275element"](0,"mat-row")}function H(e,t){if(1&e){const e=f["\u0275\u0275getCurrentView"]();f["\u0275\u0275elementStart"](0,"div",51),f["\u0275\u0275elementStart"](1,"mat-table",52,53),f["\u0275\u0275listener"]("matSortChange",(function(){return f["\u0275\u0275restoreView"](e),f["\u0275\u0275nextContext"](3).skillTableSortData()})),f["\u0275\u0275template"](3,K,5,5,"ng-container",54),f["\u0275\u0275template"](4,B,1,0,"mat-header-row",55),f["\u0275\u0275template"](5,U,1,0,"mat-row",56),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]()}if(2&e){const e=f["\u0275\u0275nextContext"](3);f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("dataSource",e.skillTableDataSource),f["\u0275\u0275advance"](2),f["\u0275\u0275property"]("ngForOf",e.skillColumns),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("matHeaderRowDef",e.skillDisplayedColumns),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("matRowDefColumns",e.skillDisplayedColumns)}}function W(e,t){1&e&&(f["\u0275\u0275elementStart"](0,"div",25),f["\u0275\u0275elementStart"](1,"div",67),f["\u0275\u0275elementStart"](2,"span",10),f["\u0275\u0275text"](3,"Languages Known"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]())}function Y(e,t){1&e&&(f["\u0275\u0275elementStart"](0,"div",50),f["\u0275\u0275elementStart"](1,"span",40),f["\u0275\u0275text"](2,"No Data Here"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]())}function J(e,t){if(1&e&&(f["\u0275\u0275elementStart"](0,"mat-header-cell",62),f["\u0275\u0275elementStart"](1,"span",63),f["\u0275\u0275text"](2),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]()),2&e){const e=f["\u0275\u0275nextContext"]().$implicit;f["\u0275\u0275advance"](2),f["\u0275\u0275textInterpolate"](e.colName)}}function X(e,t){if(1&e&&(f["\u0275\u0275elementStart"](0,"mat-cell",64),f["\u0275\u0275elementStart"](1,"span",65),f["\u0275\u0275text"](2),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=f["\u0275\u0275nextContext"]().$implicit;f["\u0275\u0275property"]("ngClass",n.className)("matTooltip",n.isToolTip?null==n?null:n.toolTipVal(e):""),f["\u0275\u0275advance"](2),f["\u0275\u0275textInterpolate"](n.cell(e))}}function $(e,t){1&e&&(f["\u0275\u0275elementContainerStart"](0,57),f["\u0275\u0275template"](1,J,3,1,"mat-header-cell",60),f["\u0275\u0275template"](2,X,3,3,"mat-cell",61),f["\u0275\u0275elementContainerEnd"]()),2&e&&f["\u0275\u0275property"]("matColumnDef",t.$implicit.colKey)}function z(e,t){1&e&&f["\u0275\u0275element"](0,"mat-header-row")}function Q(e,t){1&e&&f["\u0275\u0275element"](0,"mat-row")}function Z(e,t){if(1&e){const e=f["\u0275\u0275getCurrentView"]();f["\u0275\u0275elementStart"](0,"div",46),f["\u0275\u0275elementStart"](1,"mat-table",52,68),f["\u0275\u0275listener"]("matSortChange",(function(){return f["\u0275\u0275restoreView"](e),f["\u0275\u0275nextContext"](4).languagesKnownTableSortData()})),f["\u0275\u0275template"](3,$,3,1,"ng-container",54),f["\u0275\u0275template"](4,z,1,0,"mat-header-row",55),f["\u0275\u0275template"](5,Q,1,0,"mat-row",56),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]()}if(2&e){const e=f["\u0275\u0275nextContext"](4);f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("dataSource",e.languagesKnownTableDataSource),f["\u0275\u0275advance"](2),f["\u0275\u0275property"]("ngForOf",e.languagesKnownColumns),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("matHeaderRowDef",e.languagesKnownDisplayedColumns),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("matRowDefColumns",e.languagesKnownDisplayedColumns)}}function ee(e,t){if(1&e&&(f["\u0275\u0275elementStart"](0,"div",25),f["\u0275\u0275template"](1,Y,3,0,"div",48),f["\u0275\u0275template"](2,Z,6,4,"div",24),f["\u0275\u0275elementEnd"]()),2&e){const e=f["\u0275\u0275nextContext"](3);f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",0==e.employeeLanguagesKnownDetails.length),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",e.employeeLanguagesKnownDetails.length>0)}}function te(e,t){1&e&&(f["\u0275\u0275elementStart"](0,"div",25),f["\u0275\u0275elementStart"](1,"div",67),f["\u0275\u0275elementStart"](2,"span",10),f["\u0275\u0275text"](3,"Extra-Curricular Activities"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]())}function ne(e,t){1&e&&(f["\u0275\u0275elementStart"](0,"div",50),f["\u0275\u0275elementStart"](1,"span",40),f["\u0275\u0275text"](2,"No Data Here"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]())}function ie(e,t){if(1&e&&(f["\u0275\u0275elementStart"](0,"mat-header-cell",62),f["\u0275\u0275elementStart"](1,"span",63),f["\u0275\u0275text"](2),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]()),2&e){const e=f["\u0275\u0275nextContext"]().$implicit;f["\u0275\u0275advance"](2),f["\u0275\u0275textInterpolate"](e.colName)}}function le(e,t){if(1&e&&(f["\u0275\u0275elementStart"](0,"mat-cell",64),f["\u0275\u0275elementStart"](1,"span",65),f["\u0275\u0275text"](2),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=f["\u0275\u0275nextContext"]().$implicit;f["\u0275\u0275property"]("ngClass",n.className)("matTooltip",n.isToolTip?null==n?null:n.toolTipVal(e):""),f["\u0275\u0275advance"](2),f["\u0275\u0275textInterpolate"](n.cell(e))}}function ae(e,t){1&e&&(f["\u0275\u0275elementContainerStart"](0,57),f["\u0275\u0275template"](1,ie,3,1,"mat-header-cell",60),f["\u0275\u0275template"](2,le,3,3,"mat-cell",61),f["\u0275\u0275elementContainerEnd"]()),2&e&&f["\u0275\u0275property"]("matColumnDef",t.$implicit.colKey)}function re(e,t){1&e&&f["\u0275\u0275element"](0,"mat-header-row")}function oe(e,t){1&e&&f["\u0275\u0275element"](0,"mat-row")}function se(e,t){if(1&e){const e=f["\u0275\u0275getCurrentView"]();f["\u0275\u0275elementStart"](0,"div",46),f["\u0275\u0275elementStart"](1,"mat-table",52,69),f["\u0275\u0275listener"]("matSortChange",(function(){return f["\u0275\u0275restoreView"](e),f["\u0275\u0275nextContext"](4).extraCurricularTableSortData()})),f["\u0275\u0275template"](3,ae,3,1,"ng-container",54),f["\u0275\u0275template"](4,re,1,0,"mat-header-row",55),f["\u0275\u0275template"](5,oe,1,0,"mat-row",56),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]()}if(2&e){const e=f["\u0275\u0275nextContext"](4);f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("dataSource",e.extraCurricularActivitiesTableDataSource),f["\u0275\u0275advance"](2),f["\u0275\u0275property"]("ngForOf",e.extraCurricularActivitiesColumns),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("matHeaderRowDef",e.extraCurricularActivitiesDisplayedColumns),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("matRowDefColumns",e.extraCurricularActivitiesDisplayedColumns)}}function de(e,t){if(1&e&&(f["\u0275\u0275elementStart"](0,"div",25),f["\u0275\u0275template"](1,ne,3,0,"div",48),f["\u0275\u0275template"](2,se,6,4,"div",24),f["\u0275\u0275elementEnd"]()),2&e){const e=f["\u0275\u0275nextContext"](3);f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",0==e.employeeExtraCurricularDetails.length),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",e.employeeExtraCurricularDetails.length>0)}}function ce(e,t){1&e&&(f["\u0275\u0275elementStart"](0,"div",25),f["\u0275\u0275elementStart"](1,"div",67),f["\u0275\u0275elementStart"](2,"span",10),f["\u0275\u0275text"](3,"Achievements & Membership If Any"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]())}function me(e,t){1&e&&(f["\u0275\u0275elementStart"](0,"div",50),f["\u0275\u0275elementStart"](1,"span",40),f["\u0275\u0275text"](2,"No Data Here"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]())}function pe(e,t){if(1&e&&(f["\u0275\u0275elementStart"](0,"div",71),f["\u0275\u0275elementStart"](1,"span",72),f["\u0275\u0275text"](2),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"]()),2&e){const e=f["\u0275\u0275nextContext"](4);f["\u0275\u0275advance"](2),f["\u0275\u0275textInterpolate1"](" ",null!=e.employeeLegalAndAchievementsDetails[0]&&e.employeeLegalAndAchievementsDetails[0].achievements_desc?null==e.employeeLegalAndAchievementsDetails[0]?null:e.employeeLegalAndAchievementsDetails[0].achievements_desc:"-"," ")}}function ue(e,t){if(1&e&&(f["\u0275\u0275elementStart"](0,"div",25),f["\u0275\u0275template"](1,me,3,0,"div",48),f["\u0275\u0275template"](2,pe,3,1,"div",70),f["\u0275\u0275elementEnd"]()),2&e){const e=f["\u0275\u0275nextContext"](3);f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",0==e.employeeLegalAndAchievementsDetails.length),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",e.employeeLegalAndAchievementsDetails.length>0)}}function he(e,t){if(1&e&&(f["\u0275\u0275elementStart"](0,"div",46),f["\u0275\u0275elementStart"](1,"div",47),f["\u0275\u0275template"](2,O,3,0,"div",48),f["\u0275\u0275template"](3,H,6,4,"div",49),f["\u0275\u0275elementEnd"](),f["\u0275\u0275template"](4,W,4,0,"div",4),f["\u0275\u0275template"](5,ee,3,2,"div",4),f["\u0275\u0275template"](6,te,4,0,"div",4),f["\u0275\u0275template"](7,de,3,2,"div",4),f["\u0275\u0275template"](8,ce,4,0,"div",4),f["\u0275\u0275template"](9,ue,3,2,"div",4),f["\u0275\u0275elementEnd"]()),2&e){const e=f["\u0275\u0275nextContext"](2);f["\u0275\u0275advance"](2),f["\u0275\u0275property"]("ngIf",0==e.employeeSkillDetails.length),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",e.employeeSkillDetails.length>0),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",null==e.tenantFields||null==e.tenantFields.language?null:e.tenantFields.language.isActiveField),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",null==e.tenantFields||null==e.tenantFields.language?null:e.tenantFields.language.isActiveField),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",(null==e.tenantFields||null==e.tenantFields.activityName?null:e.tenantFields.activityName.isActiveField)||(null==e.tenantFields||null==e.tenantFields.activityType?null:e.tenantFields.activityType.isActiveField)),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",(null==e.tenantFields||null==e.tenantFields.activityName?null:e.tenantFields.activityName.isActiveField)||(null==e.tenantFields||null==e.tenantFields.activityType?null:e.tenantFields.activityType.isActiveField)),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",null==e.tenantFields||null==e.tenantFields.achivAndMemDescription?null:e.tenantFields.achivAndMemDescription.isActiveField),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",null==e.tenantFields||null==e.tenantFields.achivAndMemDescription?null:e.tenantFields.achivAndMemDescription.isActiveField)}}function ge(e,t){if(1&e){const e=f["\u0275\u0275getCurrentView"]();f["\u0275\u0275elementContainerStart"](0),f["\u0275\u0275template"](1,F,12,1,"div",4),f["\u0275\u0275elementStart"](2,"div",5),f["\u0275\u0275elementStart"](3,"mat-card",6),f["\u0275\u0275elementStart"](4,"div",7),f["\u0275\u0275elementStart"](5,"div",8),f["\u0275\u0275elementStart"](6,"div",9),f["\u0275\u0275elementStart"](7,"span",10),f["\u0275\u0275text"](8,"Skill Details"),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](9,"div",11),f["\u0275\u0275elementStart"](10,"div",12),f["\u0275\u0275template"](11,D,4,3,"button",13),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](12,"div",14),f["\u0275\u0275elementStart"](13,"button",15,16),f["\u0275\u0275elementStart"](15,"mat-icon",17),f["\u0275\u0275text"](16," edit "),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](17,"span",18),f["\u0275\u0275text"](18," Edit "),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](19,"mat-icon",19),f["\u0275\u0275text"](20," keyboard_arrow_down "),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementStart"](21,"mat-menu",null,20),f["\u0275\u0275template"](23,I,3,3,"button",21),f["\u0275\u0275elementStart"](24,"button",22),f["\u0275\u0275listener"]("click",(function(){return f["\u0275\u0275restoreView"](e),f["\u0275\u0275nextContext"]().openEditHistory()})),f["\u0275\u0275text"](25," Edit History "),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275template"](26,P,9,2,"div",23),f["\u0275\u0275template"](27,he,10,8,"div",24),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementEnd"](),f["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=f["\u0275\u0275reference"](22),t=f["\u0275\u0275nextContext"]();f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",t.approvalPendings.length>0&&t.isFromMyProfile),f["\u0275\u0275advance"](10),f["\u0275\u0275property"]("ngIf",null==t.tenantFields||null==t.tenantFields.action?null:t.tenantFields.action.isActiveField),f["\u0275\u0275advance"](2),f["\u0275\u0275property"]("matMenuTriggerFor",e)("disabled",t.isNoDataFound),f["\u0275\u0275advance"](10),f["\u0275\u0275property"]("ngIf",!(null!=t.tenantFields&&null!=t.tenantFields.action&&t.tenantFields.action.isActiveField)),f["\u0275\u0275advance"](3),f["\u0275\u0275property"]("ngIf",t.isNoDataFound),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",!t.isNoDataFound)}}let fe=(()=>{class e{constructor(e,t,n,i){this._edService=e,this._toaster=t,this.dialog=n,this._loginService=i,this.subs=new l.a,this.isNoDataFound=!1,this.employeeSkillDetails=[],this.employeeLanguagesKnownDetails=[],this.employeeExtraCurricularDetails=[],this.employeeLegalAndAchievementsDetails=[],this.showAddButton=!1,this.showEditButton=!1,this.tenantFields={},this.loaderObject={isComponentLoading:!1},this.skillDisplayedColumns=[],this.extraCurricularActivitiesDisplayedColumns=[],this.languagesKnownDisplayedColumns=[],this.isWorkflowNotActive=!0,this.isFromMyProfile=!1,this.approvalPendings=[],this.skillColumns=[{colKey:"skill_type",colName:"type",className:"dark-txt",isToolTip:!0,show:!1,cell:e=>""+(e.skill_type?e.skill_type:"-"),toolTipVal:e=>""+(e.skill_type?e.skill_type:"-")},{colKey:"skill_library",colName:"Skill Library",className:"dark-txt",isToolTip:!0,show:!1,cell:e=>""+(e.skill_type?e.skill_type:"-"),toolTipVal:e=>""+(e.skill_type?e.skill_type:"-")},{colKey:"skill_name",colName:"skill name",className:"dark-txt",isToolTip:!0,show:!0,cell:e=>""+(e.skill_name?e.skill_name:"-"),toolTipVal:e=>""+(e.skill_name?e.skill_name:"-")},{colKey:"skill_level",colName:"skill level",className:"dark-txt",isToolTip:!0,show:!0,cell:e=>""+(e.skill_level?e.skill_level:"-"),toolTipVal:e=>""+(e.skill_level?e.skill_level:"-")},{colKey:"experience",colName:"experience",className:"dark-txt",isToolTip:!0,show:!1,cell:e=>""+(e.experience?e.experience:"-"),toolTipVal:e=>""+(e.experience?e.experience:"-")},{colKey:"skill_group",colName:"Skill Group",className:"dark-txt",isToolTip:!0,show:!1,cell:e=>""+(e.skill_group?e.skill_group:"-"),toolTipVal:e=>""+(e.skill_group?e.skill_group:"-")},{colKey:"skill_category",colName:"Skill Category",className:"dark-txt",isToolTip:!0,show:!1,cell:e=>""+(e.skill_category?e.skill_category:"-"),toolTipVal:e=>""+(e.skill_category?e.skill_category:"-")},{colKey:"endorse",colName:"endrose",className:"dark-txt",isToolTip:!0,show:!1,cell:e=>""+(e.endorse?e.endorse:"-"),toolTipVal:e=>""+(e.endorse?e.endorse:"-")},{colKey:"isPrimarySkill",colName:"Is Primary Skill",className:"dark-txt",isToolTip:!0,show:!1,cell:e=>e.isPrimarySkill?"Yes":"No",toolTipVal:e=>e.isPrimarySkill?"Yes":"No"},{colKey:"sapActivate",colName:"SAP Activate",className:"dark-txt",isToolTip:!0,show:!1,cell:e=>""+(e.sapActivate?e.sapActivate:"-"),toolTipVal:e=>""+(e.sapActivate?e.sapActivate:"-")},{colKey:"action",colName:"Action",className:"dark-txt",isToolTip:!0,show:!1,cell:e=>""+(e.record_id?e.record_id:""),toolTipVal:e=>""+(e.record_id?e.record_id:"")},{colKey:"remove",colName:"Remove",className:"dark-txt",isToolTip:!0,show:!1,cell:e=>""+(e.record_id?e.record_id:""),toolTipVal:e=>""+(e.record_id?e.record_id:"")}],this.extraCurricularActivitiesColumns=[{colKey:"activity_type_name",colName:"Type",className:"dark-txt",isToolTip:!0,cell:e=>""+(e.activity_type_name?e.activity_type_name:"-"),toolTipVal:e=>""+(e.activity_type_name?e.activity_type_name:"-")},{colKey:"activity_name",colName:"Activity Name",className:"dark-txt",isToolTip:!0,cell:e=>""+(e.activity_name?e.activity_name:"-"),toolTipVal:e=>""+(e.activity_name?e.activity_name:"-")}],this.languagesKnownColumns=[{colKey:"language_name",colName:"language",className:"dark-txt",isToolTip:!0,show:!1,cell:e=>""+(e.language_name?e.language_name:"-"),toolTipVal:e=>""+(e.language_name?e.language_name:"-")},{colKey:"can_read",colName:"read",className:"dark-txt",isToolTip:!0,show:!1,cell:e=>""+(e.can_read?e.can_read:"-"),toolTipVal:e=>""+(e.can_read?e.can_read:"-")},{colKey:"can_write",colName:"write",className:"dark-txt",isToolTip:!0,show:!1,cell:e=>""+(e.can_write?e.can_write:"-"),toolTipVal:e=>""+(e.can_write?e.can_write:"-")},{colKey:"can_speak",colName:"speak",className:"dark-txt",isToolTip:!0,show:!1,cell:e=>""+(e.can_speak?e.can_speak:"-"),toolTipVal:e=>""+(e.can_speak?e.can_speak:"-")}]}ngOnInit(){return Object(i.c)(this,void 0,void 0,(function*(){this.skillTableData=[],this.isFromMyProfile=this._loginService.getProfile().profile.aid==this.associateId,this.extraCurricularActivitiesTableData=[],this.languagesKnownTableData=[],this.loaderObject.isComponentLoading=!0,this.employeeSkillDetails=yield this.getEmployeeSkillDetails(this.associateId),this.employeeLanguagesKnownDetails=yield this.getEmployeeLanguagesKnownDetails(this.associateId),this.employeeExtraCurricularDetails=yield this.getEmployeeExtraCurricularActivityDetails(this.associateId),this.employeeLegalAndAchievementsDetails=yield this.getEmployeeLegalAndAchievementsDetails(this.associateId),yield this.handleTenantWiseFieldConfig(),this.isWorkflowNotActive=yield this.CheckWorkflowStatus();let e=this._edService.checkViewAndEditAccess(245);this.showAddButton=e,this.showEditButton=e,this.skillTableData=this.employeeSkillDetails,this.skillTableDataSource=new p.l(this.skillTableData),this.skillDisplayedColumns=this.skillColumns.filter(e=>!!e.show).map(e=>e.colKey),this.extraCurricularActivitiesTableData=this.employeeExtraCurricularDetails,yield this.checkColoumnVisibility(),this.extraCurricularActivitiesTableDataSource=new p.l(this.extraCurricularActivitiesTableData),this.extraCurricularActivitiesDisplayedColumns=this.extraCurricularActivitiesColumns.map(e=>e.colKey),this.languagesKnownTableData=this.employeeLanguagesKnownDetails,this.languagesKnownTableDataSource=new p.l(this.languagesKnownTableData),this.languagesKnownDisplayedColumns=this.languagesKnownColumns.map(e=>e.colKey),this.$isEmpRetired=this._edService.getEmployeeRetiredStatus(),this.isNoDataFound=!(this.employeeSkillDetails.length>0||this.employeeLanguagesKnownDetails.length>0||this.employeeExtraCurricularDetails.length>0||this.employeeLegalAndAchievementsDetails.length>0),this.isFromMyProfile&&(this.approvalPendings=yield this.getApprovalPendingDetailsDetails()),this.loaderObject.isComponentLoading=!1}))}CheckWorkflowStatus(){return new Promise((e,t)=>{this.subs.sink=this._edService.checkWorkflowStatus(parseInt(this.associateId),"skill_details").subscribe(t=>{e(t.allow_edit)},e=>{console.log(e),t(e)})})}getFieldTenantConfig(e){return new Promise((t,n)=>{this.subs.sink=this._edService.getFieldTenantConfig(e).subscribe(e=>{t(e.data)},e=>{console.log(e),n(e)})})}removeSkill(e){return Object(i.c)(this,void 0,void 0,(function*(){this._edService.removeSkillRecord({associateId:this.associateId,recordId:null==e?void 0:e.id}).subscribe(e=>{e.err?this._toaster.showError("Error","Failed to save., Kindly contact KEBS team to resolve",2e3):(this._toaster.showSuccess("Success","Skill details Removed successfully !",2e3),this.ngOnInit())},e=>{this._toaster.showError("Error","Failed to save. Kindly contact KEBS team to resolve",2e3),console.log(e)})}))}handleTenantWiseFieldConfig(){return Object(i.c)(this,void 0,void 0,(function*(){let e=yield this.getFieldTenantConfig("skill_details");e.length>0&&e.forEach((e,t)=>{this.tenantFields[e.field]={isMandatory:!!e.is_mandatory,isActiveField:!!e.is_active_field},e.is_active_field&&this.skillColumns.forEach(t=>{t.colKey==e.field&&(t.show=!0)})}),console.log(this.tenantFields)}))}checkColoumnVisibility(){this.showEditButton&&this.skillDisplayedColumns.forEach(e=>{"action"==e.colKey&&(e.show=!0),"remove"==e.colKey&&(e.show=!0)})}getEmployeeSkillDetails(e){if(e)return new Promise((t,n)=>{this.subs.sink=this._edService.getEmployeeSkillDetails(e).subscribe(e=>{t(e.data)},e=>{this._toaster.showError("Error","Failed to retrieve employee !",2e3),console.log(e),n(e)})})}getEmployeeLanguagesKnownDetails(e){if(e)return new Promise((t,n)=>{this.subs.sink=this._edService.getEmployeeLanguagesKnownDetails(e).subscribe(e=>{t(e.data)},e=>{this._toaster.showError("Error","Failed to retrieve employee !",2e3),console.log(e),n(e)})})}getEmployeeExtraCurricularActivityDetails(e){if(e)return new Promise((t,n)=>{this.subs.sink=this._edService.getEmployeeExtraCurricularActivityDetails(e).subscribe(e=>{t(e.data)},e=>{this._toaster.showError("Error","Failed to retrieve employee !",2e3),console.log(e),n(e)})})}getEmployeeLegalAndAchievementsDetails(e){if(e)return new Promise((t,n)=>{this.subs.sink=this._edService.getEmployeeLegalAndAchievementsDetails(e).subscribe(e=>{t(e.data)},e=>{this._toaster.showError("Error","Failed to retrieve employee !",2e3),console.log(e),n(e)})})}skillTableSortData(){console.log("sort"),console.log(this.skillTableDataSource.sort),console.log(this.skillTbSort),this.skillTableDataSource.sort=this.skillTbSort}extraCurricularTableSortData(){this.extraCurricularActivitiesTableDataSource.sort=this.extraCurricularTbSort}languagesKnownTableSortData(){this.languagesKnownTableDataSource.sort=this.languagesKnownTbSort}editSkillDetails(e){var t;return Object(i.c)(this,void 0,void 0,(function*(){null===(t=this.trigger)||void 0===t||t.closeMenu();let i=null==e?void 0:e.id,l={associateId:parseInt(this.associateId),isFromModal:!0,record_id:i||null};const{SkillDetailsComponent:a}=yield Promise.resolve().then(n.bind(null,"9fkf"));this.dialog.open(a,{height:"75%",width:"80vw",panelClass:"e360-skill-details-modalbox",data:{modalParams:l}}).afterClosed().subscribe(e=>{"Updated"==e&&this.ngOnInit()},e=>{this._toaster.showError("Error","Failed to retrieve employee !",2e3)})}))}openEditHistory(){var e;return Object(i.c)(this,void 0,void 0,(function*(){null===(e=this.trigger)||void 0===e||e.closeMenu();let t={associateId:parseInt(this.associateId),tabKey:"skill_details"};const{EditHistoryModalComponent:i}=yield Promise.all([n.e(0),n.e(955)]).then(n.bind(null,"Qcrq"));this.dialog.open(i,{height:"100%",width:"40%",position:{right:"0px"},data:{modalParams:t}})}))}ngOnDestroy(){this.subs.unsubscribe()}getApprovalPendingDetailsDetails(){return new Promise((e,t)=>{this.subs.sink=this._edService.getPendingApprovalsForEmployeeAID(this.associateId,"skill_details").subscribe(t=>{t.err||e(t.approval_list)},e=>{this._toaster.showError("Error","Failed to Update Skill Validation!",2e3),console.log(e),t(e)})})}openApprovalPendingDialog(){var e,t;return Object(i.c)(this,void 0,void 0,(function*(){let i={associateId:parseInt(this.associateId),approvalPendings:null===(t=null===(e=this.approvalPendings[0])||void 0===e?void 0:e.submission_item)||void 0===t?void 0:t.approval_fields,pendingMetaData:this.approvalPendings[0]};const{ApprovalPendingDetailScreenComponent:l}=yield Promise.all([n.e(19),n.e(954)]).then(n.bind(null,"slBa"));this.dialog.open(l,{width:"700px",panelClass:"e360-approval-pending-dialog-box",autoFocus:!1,data:{modalParams:i}}).afterClosed().subscribe(e=>{},e=>{this._toaster.showError("Error","Failed to retrieve employee !",2e3)})}))}}return e.\u0275fac=function(t){return new(t||e)(f["\u0275\u0275directiveInject"](v.a),f["\u0275\u0275directiveInject"](y.a),f["\u0275\u0275directiveInject"](k.b),f["\u0275\u0275directiveInject"](C.a))},e.\u0275cmp=f["\u0275\u0275defineComponent"]({type:e,selectors:[["app-skill-details"]],viewQuery:function(e,t){if(1&e&&(f["\u0275\u0275viewQuery"](S,!0),f["\u0275\u0275viewQuery"](x,!0),f["\u0275\u0275viewQuery"](b,!0),f["\u0275\u0275viewQuery"](w,!0)),2&e){let e;f["\u0275\u0275queryRefresh"](e=f["\u0275\u0275loadQuery"]())&&(t.trigger=e.first),f["\u0275\u0275queryRefresh"](e=f["\u0275\u0275loadQuery"]())&&(t.skillTbSort=e.first),f["\u0275\u0275queryRefresh"](e=f["\u0275\u0275loadQuery"]())&&(t.extraCurricularTbSort=e.first),f["\u0275\u0275queryRefresh"](e=f["\u0275\u0275loadQuery"]())&&(t.languagesKnownTbSort=e.first)}},inputs:{associateId:"associateId"},decls:3,vars:2,consts:[[1,"container-fluid","p-0","pr-1","mt-2","skill-details-styles"],[4,"ngIf"],[1,"d-flex","justify-content-center","mt-3"],["matTooltip","Please wait...","diameter","30"],["class","row pt-3",4,"ngIf"],[1,"row","pt-3","pb-2"],[1,"pl-1","pr-1","mat-card","slide-from-down",2,"min-height","65vh"],[1,"col-12","px-2","py-2"],[1,"row"],[1,"col-9"],[1,"section-heading"],[1,"col-3","d-flex"],[1,"col-8","p-0"],["mat-raised-button","","class","ml-3 add-button",3,"disabled","click",4,"ngIf"],[1,"col-5","p-0"],["mat-button","",1,"edit-button",3,"matMenuTriggerFor","disabled"],["menuTrigger","matMenuTrigger"],[1,"edit-icon"],[1,"pl-0","edit-text"],[1,"pl-0","drop-down-icon"],["editMenu","matMenu"],["mat-menu-item","",3,"disabled","click",4,"ngIf"],["mat-menu-item","",3,"click"],["class","row pt-5 pb-2 justify-content-center",4,"ngIf"],["class","col-12 p-0",4,"ngIf"],[1,"row","pt-3"],[1,"pl-0","pt-0","mat-card","slide-from-down",2,"height","6vh","background","#FFFBE7"],[1,"col-12","px-3","pt-2"],[1,"d-flex","justify-content-center","align-items-center"],[1,"approval-pending-icon"],[1,"approval-pending-text"],[1,"approval-pending-text","pl-1",2,"text-decoration","underline","font-weight","500","cursor","pointer",3,"click"],["mat-raised-button","",1,"ml-3","add-button",3,"disabled","click"],[1,"add-text"],["mat-menu-item","",3,"disabled","click"],[1,"row","pt-5","pb-2","justify-content-center"],[1,"col-4"],[1,"d-flex","justify-content-center","align-items-center","slide-from-down"],["src","https://assets.kebs.app/images/empty_re_opql.png","height","150","width","200",1,"mt-2"],[1,"pt-3","no-data-found-text"],[1,"item-value",2,"font-size","15px !important","font-weight","700 !important"],["class","pt-2 no-data-found-text",4,"ngIf"],["class","pt-3 no-data-found-text",4,"ngIf"],[1,"pt-2","no-data-found-text"],[1,"item-value",2,"white-space","normal !important","text-align","center","font-weight","400 !important"],["mat-raised-button","",1,"ml-3",3,"disabled","ngClass","click"],[1,"col-12","p-0"],[1,"row","pt-3",2,"width","100%"],["class","col-12 p-0 no-data-found-text",4,"ngIf"],["class","col-12 p-0 scroll skill",4,"ngIf"],[1,"col-12","p-0","no-data-found-text"],[1,"col-12","p-0","scroll","skill"],["matSort","",1,"mat-elevation-z0","slide-from-down",3,"dataSource","matSortChange"],["skillTbSort","matSort"],[3,"matColumnDef",4,"ngFor","ngForOf"],[4,"matHeaderRowDef"],[4,"matRowDef","matRowDefColumns"],[3,"matColumnDef"],[4,"ngIf","ngIfElse"],["third",""],["mat-sort-header","",4,"cdkHeaderCellDef"],[3,"ngClass","matTooltip",4,"cdkCellDef"],["mat-sort-header",""],[2,"color","#5f6c81","text-transform","uppercase"],[3,"ngClass","matTooltip"],[1,"overflow-ctrl"],[2,"color","#5F6C81","text-transform","uppercase"],[1,"col-10"],["languagesKnownTbSort","matSort"],["extraCurricularTbSort","matSort"],["class","col-12",4,"ngIf"],[1,"col-12"],[1,"item-value",2,"white-space","normal !important","color","black !important"]],template:function(e,t){1&e&&(f["\u0275\u0275elementStart"](0,"div",0),f["\u0275\u0275template"](1,E,3,0,"ng-container",1),f["\u0275\u0275template"](2,ge,28,7,"ng-container",1),f["\u0275\u0275elementEnd"]()),2&e&&(f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",t.loaderObject.isComponentLoading),f["\u0275\u0275advance"](1),f["\u0275\u0275property"]("ngIf",!t.loaderObject.isComponentLoading))},directives:[a.NgIf,d.c,c.a,s.a,m.f,o.a,m.g,m.d,a.NgClass,p.k,u.a,a.NgForOf,p.g,p.j,p.c,h.k,h.c,p.d,u.b,p.a,p.f,p.i],pipes:[a.AsyncPipe],styles:[".skill-details-styles[_ngcontent-%COMP%]{overflow-x:auto;height:70vh;scrollbar-width:none}.skill-details-styles[_ngcontent-%COMP%]::-webkit-scrollbar{display:none}.skill-details-styles[_ngcontent-%COMP%]   .mat-card[_ngcontent-%COMP%]{width:100%;border:1px solid #fff;background-color:#fff;border-radius:5px}.skill-details-styles[_ngcontent-%COMP%]   .add-button[_ngcontent-%COMP%]{line-height:24px;padding:3px 14px 2px;display:flex;flex-direction:row;align-items:center;background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%)!important}.skill-details-styles[_ngcontent-%COMP%]   .add-button[_ngcontent-%COMP%]   .add-text[_ngcontent-%COMP%]{color:#fff;font-size:13px;font-weight:400}.skill-details-styles[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%]{border:1px solid #dadce2;border-radius:4px;line-height:24px;padding:2px;float:right;display:flex;flex-direction:row;align-items:center}.skill-details-styles[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%]   .edit-icon[_ngcontent-%COMP%]{font-size:14px;line-height:22px}.skill-details-styles[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%]   .edit-text[_ngcontent-%COMP%]{color:#45546e;font-size:14px;font-weight:400}.skill-details-styles[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%]   .drop-down-icon[_ngcontent-%COMP%]{font-size:21px;line-height:24px}.skill-details-styles[_ngcontent-%COMP%]   .item-value[_ngcontent-%COMP%]{font-size:14px;color:#45546e}.skill-details-styles[_ngcontent-%COMP%]   .item-value[_ngcontent-%COMP%], .skill-details-styles[_ngcontent-%COMP%]   .section-heading[_ngcontent-%COMP%]{font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;text-transform:capitalize;max-width:96%}.skill-details-styles[_ngcontent-%COMP%]   .section-heading[_ngcontent-%COMP%]{color:#ee4961;font-size:15px}.skill-details-styles[_ngcontent-%COMP%]   .skill[_ngcontent-%COMP%]   .mat-cell[_ngcontent-%COMP%], .skill-details-styles[_ngcontent-%COMP%]   .skill[_ngcontent-%COMP%]   .mat-header-cell[_ngcontent-%COMP%]{word-wrap:normal;display:table-cell;padding:0 10px;line-break:unset;width:100%;white-space:nowrap;overflow:hidden;vertical-align:middle;border-bottom:1px solid #e0e0e0}.skill-details-styles[_ngcontent-%COMP%]   .skill[_ngcontent-%COMP%]   .mat-header-cell[_ngcontent-%COMP%]{border-bottom:none}.skill-details-styles[_ngcontent-%COMP%]   .skill[_ngcontent-%COMP%]   .mat-header-row[_ngcontent-%COMP%], .skill-details-styles[_ngcontent-%COMP%]   .skill[_ngcontent-%COMP%]   .mat-row[_ngcontent-%COMP%]{display:table-row}.skill-details-styles[_ngcontent-%COMP%]   .skill[_ngcontent-%COMP%]   .mat-table[_ngcontent-%COMP%]{overflow-x:auto;overflow-y:auto;max-height:50vh}.skill-details-styles[_ngcontent-%COMP%]   table[_ngcontent-%COMP%]{width:100%}.skill-details-styles[_ngcontent-%COMP%]   .scroll[_ngcontent-%COMP%]{overflow:auto}.skill-details-styles[_ngcontent-%COMP%]   th.mat-sort-header-sorted[_ngcontent-%COMP%]{color:#000}.skill-details-styles[_ngcontent-%COMP%]   .dark-txt[_ngcontent-%COMP%], .skill-details-styles[_ngcontent-%COMP%]   .relationship-name[_ngcontent-%COMP%]{font-weight:500;color:#000;font-size:13px}.skill-details-styles[_ngcontent-%COMP%]   .overflow-ctrl[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;text-transform:capitalize;max-width:96%}.skill-details-styles[_ngcontent-%COMP%]   mat-footer-row[_ngcontent-%COMP%], .skill-details-styles[_ngcontent-%COMP%]   mat-header-row[_ngcontent-%COMP%]{border-bottom-width:0}.skill-details-styles[_ngcontent-%COMP%]   .add-details-btn[_ngcontent-%COMP%]{background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%)!important;color:#fff;height:42px}.skill-details-styles[_ngcontent-%COMP%]   .no-data-found-text[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}.skill-details-styles[_ngcontent-%COMP%]   .disabled-add-button[_ngcontent-%COMP%]{background:#d3d3d3;color:grey}.skill-details-styles[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}.skill-details-styles[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}  .e360-skill-details-modalbox mat-dialog-container{padding:24px!important;overflow:auto}"]}),e})(),ve=(()=>{class e{}return e.\u0275mod=f["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=f["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[a.CommonModule,r.a,o.b,s.b,d.b,c.b,m.e,p.m,u.c,h.r,g.b]]}),e})()}}]);