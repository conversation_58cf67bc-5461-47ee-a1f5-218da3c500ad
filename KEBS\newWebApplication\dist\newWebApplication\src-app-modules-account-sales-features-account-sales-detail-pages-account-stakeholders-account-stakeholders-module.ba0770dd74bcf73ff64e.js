(window.webpackJsonp=window.webpackJsonp||[]).push([[904],{"3O2q":function(e,t,n){"use strict";n.d(t,"a",(function(){return d}));var i=n("0IaG"),o=n("fXoL"),a=n("Qu3c"),r=n("ofXK"),l=n("NFeN"),s=n("Nzva");function c(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"div",18),o["\u0275\u0275elementStart"](1,"div"),o["\u0275\u0275text"](2,"Primary"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](3,"mat-icon"),o["\u0275\u0275text"](4,"bookmark"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]())}let d=(()=>{class e{constructor(e){this.data=e}ngOnInit(){var e;this.viewInfoData=null===(e=this.data)||void 0===e?void 0:e.selectedData}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](i.a))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-view-info"]],decls:33,vars:14,consts:[[1,"view-stake-info","row"],[1,"profile-brief-comet","col-4"],[1,"brief-comet"],[1,"circle"],[3,"src"],[1,"prof-name",3,"matTooltip"],[1,"prof-aid"],[1,"prof-email"],[1,"profile-detail-comet","col-8"],[1,"col"],[1,"row","header-title"],[1,"col-4"],[1,"row","desc-detail-comet"],[1,"col-4","desc-main"],[1,"user-profile"],[1,"body-desc",3,"matTooltip"],[1,"col-4","prima-holder"],["class","is-primary ml-auto",4,"ngIf"],[1,"is-primary","ml-auto"]],template:function(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"div",0),o["\u0275\u0275elementStart"](1,"div",1),o["\u0275\u0275elementStart"](2,"div",2),o["\u0275\u0275elementStart"](3,"div",3),o["\u0275\u0275element"](4,"img",4),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](5,"div",5),o["\u0275\u0275text"](6),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](7,"div",6),o["\u0275\u0275text"](8),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](9,"div",7),o["\u0275\u0275text"](10),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](11,"div",8),o["\u0275\u0275elementStart"](12,"div",9),o["\u0275\u0275elementStart"](13,"div",10),o["\u0275\u0275elementStart"](14,"div",11),o["\u0275\u0275text"](15,"Added By"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](16,"div",11),o["\u0275\u0275text"](17,"Added Date"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](18,"div",11),o["\u0275\u0275text"](19,"Role in this Opportunity"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](20,"div",12),o["\u0275\u0275elementStart"](21,"div",13),o["\u0275\u0275elementStart"](22,"div",14),o["\u0275\u0275element"](23,"img",4),o["\u0275\u0275elementStart"](24,"div",15),o["\u0275\u0275text"](25),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](26,"div",13),o["\u0275\u0275text"](27),o["\u0275\u0275pipe"](28,"dynamicDatePipe"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](29,"div",16),o["\u0275\u0275elementStart"](30,"div",15),o["\u0275\u0275text"](31),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](32,c,5,0,"div",17),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e&&(o["\u0275\u0275advance"](4),o["\u0275\u0275property"]("src",t.viewInfoData.stakeholder_list[0].last_modified_by_profile_url,o["\u0275\u0275sanitizeUrl"]),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("matTooltip",t.viewInfoData.stakeholder_list[0].last_modified_by_name),o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" ",t.viewInfoData.stakeholder_list[0].last_modified_by_name||" - "," "),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate1"]("A.ID ",t.viewInfoData.aid,""),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate"](t.viewInfoData.email),o["\u0275\u0275advance"](13),o["\u0275\u0275property"]("src",t.viewInfoData.created_by_profile_url,o["\u0275\u0275sanitizeUrl"]),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("matTooltip",t.viewInfoData.created_by_name),o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" ",t.viewInfoData.created_by_name||" - "," "),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate"](o["\u0275\u0275pipeBind1"](28,12,t.viewInfoData.created_on)),o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("matTooltip",t.viewInfoData.name),o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate"](t.viewInfoData.name),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",t.viewInfoData.is_primary))},directives:[a.a,r.NgIf,l.a],pipes:[s.a],styles:[".view-stake-info[_ngcontent-%COMP%]{padding:2rem;display:flex;justify-content:space-around;align-items:center;height:45vh}.view-stake-info[_ngcontent-%COMP%]   .profile-brief-comet[_ngcontent-%COMP%], .view-stake-info[_ngcontent-%COMP%]   .profile-brief-comet[_ngcontent-%COMP%]   .brief-comet[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}.view-stake-info[_ngcontent-%COMP%]   .profile-brief-comet[_ngcontent-%COMP%]   .brief-comet[_ngcontent-%COMP%]{flex-direction:column}.view-stake-info[_ngcontent-%COMP%]   .profile-brief-comet[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{width:5rem;height:5rem;border-radius:50%;display:flex;justify-content:center;align-items:center}.view-stake-info[_ngcontent-%COMP%]   .profile-brief-comet[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;font-size:40px;color:#f0f8ff}.view-stake-info[_ngcontent-%COMP%]   .profile-brief-comet[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{border-radius:50%!important;height:5rem;width:5rem}.view-stake-info[_ngcontent-%COMP%]   .profile-brief-comet[_ngcontent-%COMP%]   .prof-name[_ngcontent-%COMP%]{color:var(--Blue-Grey-100,#45546e);font-family:Roboto;font-size:20px;font-style:normal;font-weight:700;line-height:24px;text-transform:capitalize;padding:2rem 2rem 0;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;width:300px;text-align:center}.view-stake-info[_ngcontent-%COMP%]   .profile-brief-comet[_ngcontent-%COMP%]   .prof-aid[_ngcontent-%COMP%]{color:var(--Blue-Grey-100,#45546e);font-family:Plus Jakarta Sans;font-size:16px;font-style:normal;font-weight:400;line-height:24px;padding:2%;text-transform:capitalize}.view-stake-info[_ngcontent-%COMP%]   .profile-brief-comet[_ngcontent-%COMP%]   .prof-email[_ngcontent-%COMP%]{color:var(--Blue-Grey-100,#45546e);font-family:Plus Jakarta Sans;font-size:13px;font-style:normal;font-weight:500;line-height:16px;padding:2%;letter-spacing:.26px}.view-stake-info[_ngcontent-%COMP%]   .desc-main[_ngcontent-%COMP%]{display:flex;align-items:center}.view-stake-info[_ngcontent-%COMP%]   .desc-main[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]{display:flex;justify-content:start;align-items:center;gap:5%}.view-stake-info[_ngcontent-%COMP%]   .desc-main[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{border-radius:50%!important;height:25px;width:25px}.view-stake-info[_ngcontent-%COMP%]   .desc-main[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .body-desc[_ngcontent-%COMP%]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;width:95px}.view-stake-info[_ngcontent-%COMP%]   .profile-detail-comet[_ngcontent-%COMP%]   .prima-holder[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}.view-stake-info[_ngcontent-%COMP%]   .profile-detail-comet[_ngcontent-%COMP%]   .prima-holder[_ngcontent-%COMP%]   .body-desc[_ngcontent-%COMP%]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;width:200px}.view-stake-info[_ngcontent-%COMP%]   .profile-detail-comet[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]{color:var(--Black-60,#7d838b);font-weight:400}.view-stake-info[_ngcontent-%COMP%]   .profile-detail-comet[_ngcontent-%COMP%]   .desc-detail-comet[_ngcontent-%COMP%], .view-stake-info[_ngcontent-%COMP%]   .profile-detail-comet[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]{font-family:Plus Jakarta Sans;font-size:13px;font-style:normal;line-height:16px;letter-spacing:.26px;text-transform:capitalize}.view-stake-info[_ngcontent-%COMP%]   .profile-detail-comet[_ngcontent-%COMP%]   .desc-detail-comet[_ngcontent-%COMP%]{color:var(--Blue-Grey-100,#45546e);font-weight:500;margin-top:1%}.view-stake-info[_ngcontent-%COMP%]   .profile-detail-comet[_ngcontent-%COMP%]   .is-primary[_ngcontent-%COMP%]{margin-left:2%;width:70px;padding-left:5px;display:flex;justify-content:center;height:25px;border-radius:4px;background:var(--intButton)!important;color:var(--Neutral-White,#fff);font-family:Plus Jakarta Sans;font-size:11px;font-style:normal;font-weight:400;line-height:16px;align-items:center;text-transform:capitalize}.view-stake-info[_ngcontent-%COMP%]   .profile-detail-comet[_ngcontent-%COMP%]   .is-primary[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:20px!important;align-items:center;display:flex;justify-content:center}"]}),e})()},"9TmB":function(e,t,n){"use strict";n.r(t),n.d(t,"AccountStakeholdersModule",(function(){return Qe}));var i=n("ofXK"),o=n("tyNb"),a=n("mrSG"),r=n("fXoL"),l=n("3Pt+"),s=n("0IaG"),c=n("XNiG"),d=n("xG9w"),p=n("Yd6m"),m=n("1A3m"),g=n("flaP"),f=n("pgif"),h=n("bTqV"),u=n("Qu3c"),v=n("NFeN"),_=n("Kj3r"),y=n("1G5W"),C=n("NJ67"),x=n("F97M"),O=n("XVR1"),w=n("kmnG"),k=n("qFsG"),S=n("/1cH"),P=n("FKr1");function M(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"mat-label"),r["\u0275\u0275text"](1),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate"](e.label)}}function b(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"mat-option",7),r["\u0275\u0275elementStart"](1,"small"),r["\u0275\u0275text"](2),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=r["\u0275\u0275nextContext"]();r["\u0275\u0275property"]("hidden",!n.isAutocomplete)("value",e),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate2"]("",e.displayName," | ",e.mail,"")}}let E=(()=>{class e extends C.a{constructor(e,t,n){super(),this.graphApi=e,this._opportunityService=t,this.AppSharedComponentsService=n,this.userSearchSubject=new c.b,this.isAutocomplete=!1,this.Formwidth="",this.showUserList=new r.EventEmitter,this.selectedUser=new r.EventEmitter,this.label="",this.blur=new r.EventEmitter,this.required=!1,this.vcoe_flag=!1,this.fieldCtrl=new l.j,this.disabled=!1,this.isGraphApi=0,this._onDestroy=new c.b}ngOnInit(){this.userSearchSubject.pipe(Object(_.a)(600)).subscribe(e=>Object(a.c)(this,void 0,void 0,(function*(){let t=yield this.getUserSuggestionsFromDB(e,this.vcoe_flag);this.showUserList.emit(t)}))),this.fieldCtrl.valueChanges.pipe(Object(y.a)(this._onDestroy)).subscribe(e=>{e?(this.value=e.id,this.onChange(e.id)):(this.value="",this.onChange(""))})}ngOnChanges(){return Object(a.c)(this,void 0,void 0,(function*(){if(this.vcoe_flag){this.onChange("");let e=yield this.getUserSuggestionsFromDB("",this.vcoe_flag);this.showUserList.emit(e)}}))}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}ngAfterViewInit(){}searchUser(e){if(!e)return this.graphApi.userSuggestions=[];this.userSearchSubject.next(e)}resetSuggestion(){this.graphApi.userSuggestions=[]}selectedOption(e){this.selectedUser.emit(e.option.value),this.value=e.option.value,this.blur.emit()}displayUserName(e){return e?e.displayName:""}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){return Object(a.c)(this,void 0,void 0,(function*(){if(e)if(this.isGraphApi)this.graphApi.getUserProfile(e).then(t=>Object(a.c)(this,void 0,void 0,(function*(){if(t)this.fieldCtrl.setValue(t);else{let t=yield this.getUserProfileFromDB(e);this.fieldCtrl.setValue(t)}})),t=>Object(a.c)(this,void 0,void 0,(function*(){let t=yield this.getUserProfileFromDB(e);this.fieldCtrl.setValue(t)})));else{let t=yield this.getUserProfileFromDB(e);this.fieldCtrl.setValue(t)}else this.fieldCtrl.setValue("")}))}getUserSuggestionsFromDB(e,t){return Object(a.c)(this,void 0,void 0,(function*(){let n=yield this._opportunityService.getUserSuggestionsFromDB(e,t);return this.graphApi.userSuggestions=n.value,n.value}))}getUserProfileFromDB(e){return Object(a.c)(this,void 0,void 0,(function*(){let t=yield this.AppSharedComponentsService.getUserProfileFromDB(e);return t&&t.length>0?t[0]:""}))}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](x.a),r["\u0275\u0275directiveInject"](f.a),r["\u0275\u0275directiveInject"](O.a))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["app-account-stakeholder-search"]],inputs:{isAutocomplete:"isAutocomplete",Formwidth:"Formwidth",label:"label",required:"required",vcoe_flag:"vcoe_flag",disabled:"disabled",isGraphApi:"isGraphApi"},outputs:{showUserList:"showUserList",selectedUser:"selectedUser",blur:"blur"},features:[r["\u0275\u0275InheritDefinitionFeature"],r["\u0275\u0275NgOnChangesFeature"]],decls:7,vars:9,consts:[[1,"opp-search"],["appearance","outline","width","Formwidth"],[4,"ngIf"],["matInput","",3,"matAutocomplete","placeholder","required","formControl","disabled","keyup","focus"],[3,"hidden","displayWith","optionSelected"],["auto","matAutocomplete"],[3,"hidden","value",4,"ngFor","ngForOf"],[3,"hidden","value"]],template:function(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",0),r["\u0275\u0275elementStart"](1,"mat-form-field",1),r["\u0275\u0275template"](2,M,2,1,"mat-label",2),r["\u0275\u0275elementStart"](3,"input",3),r["\u0275\u0275listener"]("keyup",(function(e){return t.searchUser(e.target.value)}))("focus",(function(){return t.resetSuggestion()})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](4,"mat-autocomplete",4,5),r["\u0275\u0275listener"]("optionSelected",(function(e){return t.selectedOption(e)})),r["\u0275\u0275template"](6,b,3,4,"mat-option",6),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275reference"](5);r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngIf",t.label),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("matAutocomplete",e)("placeholder",t.label)("required",t.required)("formControl",t.fieldCtrl)("disabled",t.disabled),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("hidden",!t.isAutocomplete)("displayWith",t.displayUserName),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngForOf",t.graphApi.userSuggestions)}},directives:[w.c,i.NgIf,k.b,S.d,l.e,l.F,l.v,l.k,S.b,i.NgForOf,w.g,P.p],styles:[".opp-search[_ngcontent-%COMP%]{width:30rem}.opp-search[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]{width:100%}.opp-search[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding-bottom:8px!important}.opp-search[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-form-field-underline{bottom:0}.opp-search[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-form-field-infix{padding:.65em 0!important;border-top:.54375em solid transparent!important;font-size:13px}.opp-search[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-select-arrow{margin:0 4px!important}"]}),e})();var I=n("TmG/"),D=n("Xa2L");function A(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"div",20),r["\u0275\u0275elementStart"](1,"div",14),r["\u0275\u0275text"](2),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](3,"div",15),r["\u0275\u0275text"](4),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](5,"div",21),r["\u0275\u0275text"](6),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](7,"div",22),r["\u0275\u0275elementStart"](8,"app-input-search",23),r["\u0275\u0275listener"]("ngModelChange",(function(e){return t.$implicit.role=e})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](9,"div",24),r["\u0275\u0275elementStart"](10,"button",25),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const n=t.$implicit;return r["\u0275\u0275nextContext"](2).addConsultant(n)})),r["\u0275\u0275text"](11," Add "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=r["\u0275\u0275nextContext"](2);r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate"](e.associate_id),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate"](e.displayName),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate1"](" ",(null==e?null:e.desgn_name)||" - "," "),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngModel",e.role)("list",n.dup_stakeholderList)}}function j(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",10),r["\u0275\u0275elementStart"](1,"div",11),r["\u0275\u0275elementStart"](2,"div",12),r["\u0275\u0275elementStart"](3,"div",13),r["\u0275\u0275elementStart"](4,"div",14),r["\u0275\u0275text"](5,"A.ID"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](6,"div",15),r["\u0275\u0275text"](7,"Name"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](8,"div",15),r["\u0275\u0275text"](9,"Designation"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](10,"div",16),r["\u0275\u0275text"](11,"Role in the Account"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275element"](12,"div",17),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](13,"div",18),r["\u0275\u0275template"](14,A,12,5,"div",19),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](14),r["\u0275\u0275property"]("ngForOf",e.consultantList)}}function T(e,t){1&e&&r["\u0275\u0275element"](0,"mat-spinner",29)}function L(e,t){if(1&e&&r["\u0275\u0275element"](0,"img",30),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275property"]("src",(null==e._stakeholderService.loaderConfig?null:e._stakeholderService.loaderConfig.loaderUrl)||"https://assets.kebs.app/KEBS_MAIN_GIF.gif",r["\u0275\u0275sanitizeUrl"])("width",(null==e._stakeholderService.loaderConfig?null:e._stakeholderService.loaderConfig.width)||70)("height",(null==e._stakeholderService.loaderConfig?null:e._stakeholderService.loaderConfig.height)||70)("matTooltip",(null==e._stakeholderService.loaderConfig?null:e._stakeholderService.loaderConfig.toolTipContent)||"Loading...")}}function z(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",26),r["\u0275\u0275template"](1,T,1,0,"mat-spinner",27),r["\u0275\u0275template"](2,L,1,4,"img",28),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",!e._stakeholderService.loaderConfig),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",e._stakeholderService.loaderConfig)}}function N(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div",31),r["\u0275\u0275element"](1,"img",32),r["\u0275\u0275elementStart"](2,"div"),r["\u0275\u0275text"](3," Kindly Search for the Stakeholders "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]())}let F=(()=>{class e{constructor(e,t,n,i,o,a,s){this.dialogRef=e,this._stakeholderService=t,this.toaster=n,this._router=i,this.data=o,this.roleService=a,this.opportunityService=s,this.disabled=!1,this.membersList=[],this.stakeholdersList=new r.EventEmitter,this.addNewMember=new r.EventEmitter,this.refreshTeamList=new r.EventEmitter,this.stakeholderList=[],this.dup_stakeholderList=[],this._onDestroy=new c.b,this.searchUser=new l.j,this.consultantList=[],this.updateOwner=0,this.updateUser=null,this.approvalStarted=!1,this.vcoe_flag=!1,this.isLoading=!1,this.searchUser=new l.j}ngOnInit(){var e;return Object(a.c)(this,void 0,void 0,(function*(){this.title=this.data.title,this.accountId=this._router.url.split("/")[3],this.stakeholderList=yield this._stakeholderService.getAccountStakeholderMaster("internal"),(null===(e=this.data)||void 0===e?void 0:e.role)&&(this.stakeholderList=this.stakeholderList.filter(e=>{var t;return e.name==(null===(t=this.data)||void 0===t?void 0:t.role)})),this.dup_stakeholderList=this.stakeholderList}))}setConsultantList(e){return Object(a.c)(this,void 0,void 0,(function*(){if(this.isLoading=!0,e)try{yield this.delay(2e3),this.consultantList=e}catch(t){console.error("Error during search:",t),this.toaster.showError("Error during search","Search Again",2e3)}finally{this.isLoading=!1}}))}delay(e){return new Promise(t=>setTimeout(t,e))}addConsultant(e){var t;return Object(a.c)(this,void 0,void 0,(function*(){this.dup_stakeholderList=this.stakeholderList,1==this.updateOwner&&(this.updateOwner=0,this.updateUser=null);const n=d.filter(this.stakeholderList,t=>t._id==e.role);if(0==n.length)this.toaster.showWarning("Kindly select Role!","",this.opportunityService.mediumInterval);else try{yield this._stakeholderService.addTeamMember(this.accountId,{aid:e.associate_id,role:n[0].name,practice_id:n[0]._id,owner_type:n[0].owner_type,is_mandatory:n[0].is_mandatory,is_primary:(null===(t=this.data)||void 0===t?void 0:t.is_primary)||!1,type:"Replace Stakeholder"==this.title?"REPLACE":"ADD"}).then(e=>{var t,n;"S"!==e.messType&&"E"!==e.messType&&"DUP"!=e.messType||("Replace Stakeholder"!=this.title&&(null===(n=(t=this.toaster)[e.descriptionType])||void 0===n||n.call(t,e.description,"",this.opportunityService.mediumInterval)),("S"===e.messType||"DUP"==e.messType)&&this.dialogRef.close({event:"submit",result:e.description}))}),this.addNewMember.emit({aid:e.id,role:n[0].name,practice_id:n[0]._id,owner_type:n[0].owner_type}),this.consultantList=[],this.searchUser.reset()}catch(i){console.log(i),this.toaster.showSuccess(i.error&&i.error.description?i.error.description:"Failed to add new member","","")}}))}closeDialog(){this.dialogRef.close({event:"close"}),this.vcoe_flag=!1,this.dup_stakeholderList=this.stakeholderList,this.updateOwner=0,this.consultantList=[]}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](s.h),r["\u0275\u0275directiveInject"](p.a),r["\u0275\u0275directiveInject"](m.a),r["\u0275\u0275directiveInject"](o.g),r["\u0275\u0275directiveInject"](s.a),r["\u0275\u0275directiveInject"](g.a),r["\u0275\u0275directiveInject"](f.a))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["app-add-stakeholder"]],inputs:{opportunityName:"opportunityName",type:"type",disabled:"disabled",membersList:"membersList"},outputs:{stakeholdersList:"stakeholdersList",addNewMember:"addNewMember",refreshTeamList:"refreshTeamList"},decls:15,vars:7,consts:[[1,"add-stakeholders"],[1,"header-main"],["matTooltip","Close","mat-icon-button","",1,"close-icon",3,"click"],[1,"search-main-wrapper"],[1,"stakeholder-search"],[1,"row","pt-2",2,"border-bottom","rgba(0, 0, 0, 0.12) 1px solid !important","margin-left","5px !important","margin-right","5px !important"],["label","Search for member",3,"isAutocomplete","formControl","vcoe_flag","showUserList"],["class","row searched-lister",4,"ngIf"],["class","loading-lister",4,"ngIf"],["class","kindly-text",4,"ngIf"],[1,"row","searched-lister"],[1,"col-lg-12","col-12"],[1,"searched-list","w-100"],[1,"row","header-row"],[1,"col-1","p-3"],[1,"col-3","p-3"],[1,"col-3","py-3","pl-3","pr-0"],[1,"col-2","pr-3"],[1,"scroll-sytle"],["class","row desc-body",4,"ngFor","ngForOf"],[1,"row","desc-body"],[1,"col-3","pl-3",2,"width","38% !important"],[1,"col-3","py-3","pl-1","pr-0"],["placeholder","Select Role",3,"ngModel","list","ngModelChange"],[1,"col-2","pb-2"],["mat-raised-button","",1,"ml-2","add-btn",3,"click"],[1,"loading-lister"],["matTooltip","Loading...","class","spinner-align","diameter","25",4,"ngIf"],["alt","Loading...",3,"src","width","height","matTooltip",4,"ngIf"],["matTooltip","Loading...","diameter","25",1,"spinner-align"],["alt","Loading...",3,"src","width","height","matTooltip"],[1,"kindly-text"],["src","https://assets.kebs.app/images/timeline.png","alt",""]],template:function(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div",0),r["\u0275\u0275elementStart"](1,"div",1),r["\u0275\u0275elementStart"](2,"h3"),r["\u0275\u0275text"](3),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](4,"button",2),r["\u0275\u0275listener"]("click",(function(){return t.closeDialog()})),r["\u0275\u0275elementStart"](5,"mat-icon"),r["\u0275\u0275text"](6,"close"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](7,"div",3),r["\u0275\u0275elementStart"](8,"div",4),r["\u0275\u0275elementStart"](9,"div",5),r["\u0275\u0275elementStart"](10,"div"),r["\u0275\u0275elementStart"](11,"app-account-stakeholder-search",6),r["\u0275\u0275listener"]("showUserList",(function(e){return t.setConsultantList(e)})),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](12,j,15,1,"div",7),r["\u0275\u0275template"](13,z,3,2,"div",8),r["\u0275\u0275template"](14,N,4,0,"div",9),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e&&(r["\u0275\u0275advance"](3),r["\u0275\u0275textInterpolate"](t.title),r["\u0275\u0275advance"](8),r["\u0275\u0275property"]("isAutocomplete",!1)("formControl",t.searchUser)("vcoe_flag",t.vcoe_flag),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",0!=t.consultantList.length),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",t.isLoading&&0==t.consultantList.length),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",0===t.consultantList.length&&!t.isLoading))},directives:[h.a,u.a,v.a,E,l.v,l.k,i.NgIf,i.NgForOf,I.a,l.y,D.c],styles:[".add-stakeholders[_ngcontent-%COMP%]{padding:1rem;min-height:65vh;overflow:hidden}.add-stakeholders[_ngcontent-%COMP%]   .header-main[_ngcontent-%COMP%]{display:flex;justify-content:space-between;color:var(--Black-100,#111434);font-family:Roboto;font-size:16px;font-style:normal;font-weight:600;line-height:24px;text-transform:capitalize}.add-stakeholders[_ngcontent-%COMP%]   .header-main[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:20px;cursor:pointer}.add-stakeholders[_ngcontent-%COMP%]   .search-main-wrapper[_ngcontent-%COMP%]{justify-content:center;align-items:center}.add-stakeholders[_ngcontent-%COMP%]   .scroll-sytle[_ngcontent-%COMP%]{overflow-y:scroll;height:40vh}.add-stakeholders[_ngcontent-%COMP%]   .kindly-text[_ngcontent-%COMP%]{background-color:#f6f6f6;display:flex;justify-content:center;align-items:center;flex-direction:column;height:46vh;color:var(--Black-80,#515965);font-family:Roboto;font-size:16px;font-style:normal;font-weight:600;line-height:14px;letter-spacing:.32px;text-transform:capitalize;padding:5px}.add-stakeholders[_ngcontent-%COMP%]   .kindly-text[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:200px;mix-blend-mode:multiply}.add-stakeholders[_ngcontent-%COMP%]   .loading-lister[_ngcontent-%COMP%]{height:40vh}.add-stakeholders[_ngcontent-%COMP%]   .loading-lister[_ngcontent-%COMP%], .add-stakeholders[_ngcontent-%COMP%]   .loading-lister[_ngcontent-%COMP%]   .spinner-align[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}.add-stakeholders[_ngcontent-%COMP%]   .searched-lister[_ngcontent-%COMP%]{height:-webkit-fill-available;background-color:#f6f6f6}.add-stakeholders[_ngcontent-%COMP%]   .searched-lister[_ngcontent-%COMP%]   .header-row[_ngcontent-%COMP%]{color:var(--Black-40,#a8acb2);font-family:Plus Jakarta Sans;font-size:11px;font-style:normal;font-weight:400;line-height:16px;letter-spacing:.22px;text-transform:capitalize}.add-stakeholders[_ngcontent-%COMP%]   .searched-lister[_ngcontent-%COMP%]   .desc-body[_ngcontent-%COMP%]{color:var(--Black-100,#111434);font-family:Roboto;font-weight:600;letter-spacing:.24px}.add-stakeholders[_ngcontent-%COMP%]   .searched-lister[_ngcontent-%COMP%]   .add-btn[_ngcontent-%COMP%], .add-stakeholders[_ngcontent-%COMP%]   .searched-lister[_ngcontent-%COMP%]   .desc-body[_ngcontent-%COMP%]{font-size:12px;font-style:normal;line-height:16px;text-transform:capitalize;display:flex;align-items:center}.add-stakeholders[_ngcontent-%COMP%]   .searched-lister[_ngcontent-%COMP%]   .add-btn[_ngcontent-%COMP%]{color:var(--Blue-Grey-100,#45546e);font-family:Plus Jakarta Sans;font-weight:500;height:30px;width:45px;justify-content:center;letter-spacing:-.24px;border-radius:4px;border:1px solid var(--Blue-Grey-100,#45546e)}.add-stakeholders[_ngcontent-%COMP%]   .footer-row[_ngcontent-%COMP%]{display:flex;justify-content:space-around;align-items:center}.add-stakeholders[_ngcontent-%COMP%]   .footer-row[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{color:var(--Blue-Grey-100,#45546e)}.add-stakeholders[_ngcontent-%COMP%]   .footer-row[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%], .add-stakeholders[_ngcontent-%COMP%]   .footer-row[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%]{border-radius:4px;font-family:Roboto;font-size:14px;font-style:normal;font-weight:700;line-height:16px;letter-spacing:-.28px;text-transform:capitalize;height:35px;border:none;width:60px}.add-stakeholders[_ngcontent-%COMP%]   .footer-row[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%]{background:var(--Primary-Maroon,#ee4961);color:var(--Neutral-White,#fff)}"]}),e})();var B=n("3O2q"),R=n("A5z7");const U=function(e,t){return{"selected-chip":e,"unselected-chip":t}};function V(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"mat-chip",18),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const t=r["\u0275\u0275nextContext"]().$implicit;return r["\u0275\u0275nextContext"](3).selectApplication(t)})),r["\u0275\u0275elementContainerStart"](1),r["\u0275\u0275text"](2),r["\u0275\u0275elementContainerEnd"](),r["\u0275\u0275elementEnd"]()}if(2&e){const e=r["\u0275\u0275nextContext"]().$implicit;r["\u0275\u0275property"]("ngClass",r["\u0275\u0275pureFunction2"](2,U,e.isSelected,!e.isSelected)),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate1"](" ",e.description," ")}}function G(e,t){if(1&e&&(r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275template"](1,V,3,5,"mat-chip",17),r["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",null!=e.description)}}function W(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",12),r["\u0275\u0275elementStart"](1,"div",13),r["\u0275\u0275text"](2,"Applicable Notification that stakeholder recieves"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](3,"div",14),r["\u0275\u0275elementStart"](4,"mat-chip-list",15),r["\u0275\u0275template"](5,G,2,1,"ng-container",16),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275advance"](5),r["\u0275\u0275property"]("ngForOf",e.notificationApplication)}}function J(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"div"),r["\u0275\u0275elementStart"](1,"div",3),r["\u0275\u0275elementStart"](2,"div",4),r["\u0275\u0275element"](3,"img",5),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](4,"div",6),r["\u0275\u0275elementStart"](5,"div",7),r["\u0275\u0275text"](6),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](7,"div",8),r["\u0275\u0275text"](8),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](9,W,6,1,"div",9),r["\u0275\u0275elementStart"](10,"div",10),r["\u0275\u0275elementStart"](11,"button",11),r["\u0275\u0275listener"]("click",(function(){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"]().applyNotification()})),r["\u0275\u0275text"](12,"Save"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&e){const e=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("src",e.viewInfoData.stakeholder_list[0].last_modified_by_profile_url,r["\u0275\u0275sanitizeUrl"]),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("matTooltip",e.viewInfoData.stakeholder_list[0].last_modified_by_name),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"](" ",e.viewInfoData.stakeholder_list[0].last_modified_by_name||" - "," "),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate"](e.viewInfoData.name),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",!e.isLoading),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("disabled",e.isLoading)}}function H(e,t){1&e&&r["\u0275\u0275element"](0,"mat-spinner",22)}function $(e,t){if(1&e&&r["\u0275\u0275element"](0,"img",23),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275property"]("src",(null==e._stakeholderService.loaderConfig?null:e._stakeholderService.loaderConfig.loaderUrl)||"https://assets.kebs.app/KEBS_MAIN_GIF.gif",r["\u0275\u0275sanitizeUrl"])("width",(null==e._stakeholderService.loaderConfig?null:e._stakeholderService.loaderConfig.width)||70)("height",(null==e._stakeholderService.loaderConfig?null:e._stakeholderService.loaderConfig.height)||70)("matTooltip",(null==e._stakeholderService.loaderConfig?null:e._stakeholderService.loaderConfig.toolTipContent)||"Loading...")}}function q(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",19),r["\u0275\u0275template"](1,H,1,0,"mat-spinner",20),r["\u0275\u0275template"](2,$,1,4,"img",21),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",!e._stakeholderService.loaderConfig),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",e._stakeholderService.loaderConfig)}}function Y(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div",19),r["\u0275\u0275text"](1," No Notification Available "),r["\u0275\u0275elementEnd"]())}let K=(()=>{class e{constructor(e,t,n,i){this.data=e,this.dialogRef=t,this._stakeholderService=n,this.toaster=i,this.selectedApplications=[],this.isLoading=!1,this.editAccessISH=!0}ngOnInit(){var e;this.viewInfoData=null===(e=this.data)||void 0===e?void 0:e.selectedData,this.editAccessISH=this._stakeholderService.checkEditAcessISH(),this.getNotifyFormFieldCollection()}getNotifyFormFieldCollection(){this.isLoading=!0,this.subscription=this._stakeholderService.getNotifyFormFieldCollection(33).subscribe(e=>{var t;if(this.notificationApplication=e,this.viewInfoData.notify_for)for(let n of this.notificationApplication)n.isSelected=!(!(null===(t=this.viewInfoData)||void 0===t?void 0:t.notify_for)||!this.viewInfoData.notify_for.includes(n._id));this.isLoading=!1},e=>{console.error("Error occurred while getting notifiable applications:",e),this.toaster.showError("Error occurred while getting notifiable applications","",""),this.isLoading=!1})}isSelected(e){var t,n;return this.selectedApplications.some(t=>t._id===e._id)||(null===(n=null===(t=this.viewInfoData)||void 0===t?void 0:t.notify_for)||void 0===n?void 0:n.includes(e._id))}selectApplication(e){e.isSelected=!e.isSelected}applyNotification(){var e;if(!this.editAccessISH)return void this.toaster.showWarning("Access Restricted!","");const t=this.notificationApplication.filter(e=>e.isSelected).map(e=>e._id);if(t&&t.length>0){this.isLoading=!0;try{const n={selectedIds:t,stakeholder_table_id:null===(e=this.viewInfoData)||void 0===e?void 0:e.stakeholder_table_id};this._stakeholderService.updateNotificationStakeholeder(n),this.toaster.showSuccess("Notification Applied!","",""),this.isLoading=!1,this.dialogRef.close({event:"submit"})}catch(n){console.log(n),this.toaster.showError("Error occurred while enabling notification!","",""),this.isLoading=!1}}else this.toaster.showWarning("Kindly Select the Application to apply Notification",""),this.isLoading=!1}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](s.a),r["\u0275\u0275directiveInject"](s.h),r["\u0275\u0275directiveInject"](p.a),r["\u0275\u0275directiveInject"](m.a))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["app-apply-notification"]],decls:4,vars:3,consts:[[1,"apply-notification"],[4,"ngIf"],["class","loader",4,"ngIf"],[1,"profile-info"],[1,"circle"],[3,"src"],[1,"detail-comet"],[1,"prof-name",3,"matTooltip"],[1,"prof-role"],["class","select-wrapper",4,"ngIf"],[1,"save-wrapper"],["mat-button","",1,"save-btn",3,"disabled","click"],[1,"select-wrapper"],[1,"note-class"],[1,"chip-wrapper"],["aria-label","Notification Applications","multiple","",1,"chips"],[4,"ngFor","ngForOf"],[3,"ngClass","click",4,"ngIf"],[3,"ngClass","click"],[1,"loader"],["matTooltip","Loading...","class","spinner-align","diameter","25",4,"ngIf"],["alt","Loading...",3,"src","width","height","matTooltip",4,"ngIf"],["matTooltip","Loading...","diameter","25",1,"spinner-align"],["alt","Loading...",3,"src","width","height","matTooltip"]],template:function(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div",0),r["\u0275\u0275template"](1,J,13,6,"div",1),r["\u0275\u0275template"](2,q,3,2,"div",2),r["\u0275\u0275template"](3,Y,2,0,"div",2),r["\u0275\u0275elementEnd"]()),2&e&&(r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",!t.isLoading&&(null==t.notificationApplication?null:t.notificationApplication.length)>0),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",t.isLoading),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",!t.isLoading&&0==(null==t.notificationApplication?null:t.notificationApplication.length)))},directives:[i.NgIf,u.a,h.a,R.c,i.NgForOf,R.a,i.NgClass,D.c],styles:[".apply-notification[_ngcontent-%COMP%]{border-radius:8px;background:#fff;box-shadow:0 20px 40px 0 rgba(0,0,0,.1);padding:.5rem;height:40%;width:100%}.apply-notification[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]{display:flex;justify-content:start;align-items:center;padding:1rem;gap:1.5rem}.apply-notification[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{width:3rem;height:3rem;border-radius:50%;display:flex;justify-content:center;align-items:center}.apply-notification[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{border-radius:50%!important;height:3rem;width:3rem}.apply-notification[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]   .detail-comet[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;flex-direction:column;gap:.3rem}.apply-notification[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]   .detail-comet[_ngcontent-%COMP%]   .prof-name[_ngcontent-%COMP%]{color:var(--Blue-Grey-100,#45546e);font-family:Roboto;font-size:16px;font-weight:600;letter-spacing:.32px}.apply-notification[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]   .detail-comet[_ngcontent-%COMP%]   .prof-name[_ngcontent-%COMP%], .apply-notification[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]   .detail-comet[_ngcontent-%COMP%]   .prof-role[_ngcontent-%COMP%]{font-style:normal;line-height:14px;text-transform:capitalize;display:flex;justify-content:flex-start;align-items:center;width:15rem}.apply-notification[_ngcontent-%COMP%]   .profile-info[_ngcontent-%COMP%]   .detail-comet[_ngcontent-%COMP%]   .prof-role[_ngcontent-%COMP%]{color:var(--Blue-Grey-60,#8b95a5);font-family:Plus Jakarta Sans;font-size:14px;font-weight:400;letter-spacing:.28px}.apply-notification[_ngcontent-%COMP%]   .select-wrapper[_ngcontent-%COMP%]{padding:0 1rem}.apply-notification[_ngcontent-%COMP%]   .select-wrapper[_ngcontent-%COMP%]   .chip-wrapper[_ngcontent-%COMP%]{height:7rem;padding:.5rem 0;overflow-y:auto;margin-bottom:1rem}.apply-notification[_ngcontent-%COMP%]   .select-wrapper[_ngcontent-%COMP%]   .selected-chip[_ngcontent-%COMP%]{background-color:var(--intButton)!important;color:var(--Neutral-White,#fff)}.apply-notification[_ngcontent-%COMP%]   .select-wrapper[_ngcontent-%COMP%]   .selected-chip[_ngcontent-%COMP%], .apply-notification[_ngcontent-%COMP%]   .select-wrapper[_ngcontent-%COMP%]   .unselected-chip[_ngcontent-%COMP%]{font-family:Plus Jakarta Sans;font-size:12px;font-style:normal;font-weight:400;line-height:16px;letter-spacing:.24px;text-transform:capitalize}.apply-notification[_ngcontent-%COMP%]   .select-wrapper[_ngcontent-%COMP%]   .note-class[_ngcontent-%COMP%]{color:var(--Black-80,#515965);font-family:Plus Jakarta Sans;font-size:14px;font-style:normal;font-weight:400;line-height:24px;padding:0 .5rem}.apply-notification[_ngcontent-%COMP%]   .select-wrapper[_ngcontent-%COMP%]   .chips[_ngcontent-%COMP%]{display:flex;justify-content:start;align-items:center;padding:.5rem}.apply-notification[_ngcontent-%COMP%]   .loader[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;height:15rem;color:var(--Blue-Grey-100,#45546e);font-family:Roboto;font-size:16px;font-style:normal;font-weight:600;line-height:14px;letter-spacing:.32px}.apply-notification[_ngcontent-%COMP%]   .save-wrapper[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:end;padding:0 1rem}.apply-notification[_ngcontent-%COMP%]   .save-wrapper[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%]{color:#fff;width:80px;border-radius:4px;height:29px;align-items:center;border:none;font-weight:500;font-size:14px;display:flex;justify-content:center;background:#ef4a61}"]}),e})();var X=n("STbY");function Q(e,t){1&e&&r["\u0275\u0275element"](0,"mat-spinner",7)}function Z(e,t){if(1&e&&r["\u0275\u0275element"](0,"img",8),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275property"]("src",(null==e._stakeholderService.loaderConfig?null:e._stakeholderService.loaderConfig.loaderUrl)||"https://assets.kebs.app/KEBS_MAIN_GIF.gif",r["\u0275\u0275sanitizeUrl"])("width",(null==e._stakeholderService.loaderConfig?null:e._stakeholderService.loaderConfig.width)||70)("height",(null==e._stakeholderService.loaderConfig?null:e._stakeholderService.loaderConfig.height)||70)("matTooltip",(null==e._stakeholderService.loaderConfig?null:e._stakeholderService.loaderConfig.toolTipContent)||"Loading...")}}function ee(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",4),r["\u0275\u0275template"](1,Q,1,0,"mat-spinner",5),r["\u0275\u0275template"](2,Z,1,4,"img",6),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",!e._stakeholderService.loaderConfig),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",e._stakeholderService.loaderConfig)}}function te(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div",26),r["\u0275\u0275elementStart"](1,"div"),r["\u0275\u0275text"](2,"Primary"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](3,"mat-icon"),r["\u0275\u0275text"](4,"bookmark"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]())}function ne(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"button",24),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const t=r["\u0275\u0275nextContext"]().$implicit;return r["\u0275\u0275nextContext"](2).onOptionClick("remove",t)})),r["\u0275\u0275elementStart"](1,"div",23),r["\u0275\u0275elementStart"](2,"mat-icon"),r["\u0275\u0275text"](3,"delete "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](4,"div"),r["\u0275\u0275text"](5,"Remove"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}}function ie(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"div",11),r["\u0275\u0275elementStart"](1,"div",12),r["\u0275\u0275elementStart"](2,"div",13),r["\u0275\u0275element"](3,"img",14),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](4,"div",15),r["\u0275\u0275elementStart"](5,"mat-icon"),r["\u0275\u0275text"](6,"more_horiz"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](7,"div",16),r["\u0275\u0275elementStart"](8,"div",17),r["\u0275\u0275text"](9),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](10,"div",18),r["\u0275\u0275text"](11),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](12,te,5,0,"div",19),r["\u0275\u0275elementStart"](13,"mat-menu",20,21),r["\u0275\u0275elementStart"](15,"button",22),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const n=t.$implicit;return r["\u0275\u0275nextContext"](2).onOptionClick("asPrimary",n)})),r["\u0275\u0275elementStart"](16,"div",23),r["\u0275\u0275elementStart"](17,"mat-icon"),r["\u0275\u0275text"](18,"bookmark"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](19,"div"),r["\u0275\u0275text"](20,"Mark As Primary"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](21,"button",24),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const n=t.$implicit;return r["\u0275\u0275nextContext"](2).onOptionClick("applyNotification",n)})),r["\u0275\u0275elementStart"](22,"div",23),r["\u0275\u0275elementStart"](23,"mat-icon"),r["\u0275\u0275text"](24,"notifications"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](25,"div"),r["\u0275\u0275text"](26,"Applicable Notification"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](27,"button",24),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const n=t.$implicit;return r["\u0275\u0275nextContext"](2).onOptionClick("replace",n)})),r["\u0275\u0275elementStart"](28,"div",23),r["\u0275\u0275elementStart"](29,"mat-icon"),r["\u0275\u0275text"](30,"find_replace "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](31,"div"),r["\u0275\u0275text"](32,"Replace"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](33,"button",24),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const n=t.$implicit;return r["\u0275\u0275nextContext"](2).onOptionClick("viewInfo",n)})),r["\u0275\u0275elementStart"](34,"div",23),r["\u0275\u0275elementStart"](35,"mat-icon"),r["\u0275\u0275text"](36,"account_circle "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](37,"div"),r["\u0275\u0275text"](38,"View Info"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](39,ne,6,0,"button",25),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=r["\u0275\u0275reference"](14);r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("src",e.stakeholder_list[0].last_modified_by_profile_url,r["\u0275\u0275sanitizeUrl"]),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("matMenuTriggerFor",n),r["\u0275\u0275advance"](4),r["\u0275\u0275property"]("matTooltip",e.stakeholder_list[0].last_modified_by_name),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"](" ",e.stakeholder_list[0].last_modified_by_name||" - "," "),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate"](e.name),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",e.is_primary),r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("disabled",e.is_primary),r["\u0275\u0275advance"](24),r["\u0275\u0275property"]("ngIf",!e.is_primary)}}function oe(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div"),r["\u0275\u0275elementStart"](1,"div",9),r["\u0275\u0275template"](2,ie,40,8,"div",10),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngForOf",e.internalStakeData)}}function ae(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div",27),r["\u0275\u0275elementStart"](1,"div"),r["\u0275\u0275elementStart"](2,"h4",28),r["\u0275\u0275text"](3," Assemble Team Members "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](4,"div",29),r["\u0275\u0275element"](5,"img",30),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]())}let re=(()=>{class e{constructor(e,t,n,i){this._dialog=e,this._stakeholderService=t,this.toaster=n,this._router=i,this.internalStakeData=[],this.initialStakeholderList=[],this.updateOwner=0,this.editAccessISH=!0}ngOnInit(){this.accountId=this._router.url.split("/")[3],this.getOpportunityTeamDetails(this.accountId),this.editAccessISH=this._stakeholderService.checkEditAcessISH(),this.getTabLoadingObservableSubscription=this._stakeholderService.getTabLoadingObservable.subscribe(e=>{e&&(this.internalStakeData=[],this.getOpportunityTeamDetails(this.accountId))})}getOpportunityTeamDetails(e){return Object(a.c)(this,void 0,void 0,(function*(){try{this._loader=!0,this.internalStakeData=null,this.internalStakeData=yield this._stakeholderService.getAccountTeamDetails(e)}catch(t){console.error("An error occurred while fetching opportunity team details:",t),this.toaster.showWarning("Error occurred while fetching team details","Dismiss")}finally{this._loader=!1}}))}onOptionClick(e,t){switch(e){case"asPrimary":this.markAsPrimary(t);break;case"applyNotification":this.applicableNotificaiton(t);break;case"replace":this.openPersonAddDialog(t);break;case"viewInfo":this.openViewInfo(t);break;case"remove":this.removeStakeholder(t,"remove");break;default:console.warn("Unknown icon clicked: "+e)}}markAsPrimary(e){return Object(a.c)(this,void 0,void 0,(function*(){if(this.editAccessISH)if(e.is_primary)this.toaster.showWarning("Already a Primary Stakeholder!","");else try{yield this._stakeholderService.updateInternalStakeholderAsPrimary(e.stakeholder_table_id,e.owner_type,e.accountId,e.practice_id,{aid:e.aid,oid:e.oid}),0==this.updateOwner&&(this.toaster.showSuccess("Marked As Primary!","",""),this.ngOnInit())}catch(t){console.log(t),this.toaster.showError("Error occurred while marking as primary","","")}else this.toaster.showWarning("Access Restricted!","")}))}applicableNotificaiton(e){return Object(a.c)(this,void 0,void 0,(function*(){this._dialog.open(K,{maxWidth:"40%",minWidth:"40%",data:{selectedData:e}}).afterClosed().subscribe(e=>{e&&"submit"==e.event&&this._stakeholderService.setTabLoadingObservable(!0)})}))}openPersonAddDialog(e){if(this.editAccessISH)return this._dialog.open(F,{width:"70%",minHeight:"65vh",data:{title:"Replace Stakeholder",is_primary:e.is_primary||!1,role:e.name}}).afterClosed().subscribe(t=>Object(a.c)(this,void 0,void 0,(function*(){if(t&&"submit"==t.event){if(t&&"Member already assigned"==t.result)return void this.toaster.showWarning("Cannot Replace as member already assigned!","");(yield this.removeStakeholder(e,"replace"))?this.toaster.showSuccess("Stakeholder Replaced Successfully!","",""):this.toaster.showError("Stakeholder Replacement Failed.","","")}})));this.toaster.showWarning("Access Restricted!","")}openViewInfo(e){this._dialog.open(B.a,{width:"60%",minHeight:"45vh",data:{selectedData:e}})}removeStakeholder(e,t){return Object(a.c)(this,void 0,void 0,(function*(){if(this.editAccessISH){if(null!=e)try{if(yield this._stakeholderService.removeMember(e.stakeholder_table_id),0==this.updateOwner)return"remove"===t&&this.toaster.showSuccess("Stakeholder Removed Successfully!","",""),this.ngOnInit(),!0}catch(n){console.log(n),this.toaster.showError("Error occurred while removing stakeholder","","")}return!1}this.toaster.showWarning("Access Restricted!","")}))}ngOnDestroy(){this.getTabLoadingObservableSubscription&&this.getTabLoadingObservableSubscription.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](s.b),r["\u0275\u0275directiveInject"](p.a),r["\u0275\u0275directiveInject"](m.a),r["\u0275\u0275directiveInject"](o.g))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["app-card-view"]],decls:4,vars:3,consts:[[1,"card-view-stakeholders"],["class","loader",4,"ngIf"],[4,"ngIf"],["class","d-flex justify-content-center align-items-center slide-from-down","style","height: 70vh;flex-direction: column;",4,"ngIf"],[1,"loader"],["matTooltip","Loading...","class","spinner-align","diameter","35",4,"ngIf"],["alt","Loading...",3,"src","width","height","matTooltip",4,"ngIf"],["matTooltip","Loading...","diameter","35",1,"spinner-align"],["alt","Loading...",3,"src","width","height","matTooltip"],[1,"card-wrapper","row"],["class","main-card col-md-3",4,"ngFor","ngForOf"],[1,"main-card","col-md-3"],[1,"three-dotz"],[1,"circle"],[3,"src"],[1,"d-flex","justify-content-end","custom-more-options",3,"matMenuTriggerFor"],[1,"stakeholder-name"],[1,"body-desc",3,"matTooltip"],[1,"stakeholder-role"],["class","is-primary ml-auto",4,"ngIf"],[1,"custom-menu"],["optionsMenu","matMenu"],["mat-menu-item","",3,"disabled","click"],[1,"d-flex","align-items-center","pop-button"],["mat-menu-item","",3,"click"],["mat-menu-item","",3,"click",4,"ngIf"],[1,"is-primary","ml-auto"],[1,"d-flex","justify-content-center","align-items-center","slide-from-down",2,"height","70vh","flex-direction","column"],[1,"mt-4","d-flex","justify-content-center","align-items-center","mb-4","slide-in-top"],[1,"d-flex","justify-content-center","align-items-center","slide-from-down"],["src","https://assets.kebs.app/images/assembleteam.png","height","240px","width","285px",1,"m-auto"]],template:function(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div",0),r["\u0275\u0275template"](1,ee,3,2,"div",1),r["\u0275\u0275template"](2,oe,3,1,"div",2),r["\u0275\u0275template"](3,ae,6,0,"div",3),r["\u0275\u0275elementEnd"]()),2&e&&(r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",t._loader),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",t.internalStakeData&&(null==t.internalStakeData?null:t.internalStakeData.length)>0&&!t._loader),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",!(t.internalStakeData&&0!==(null==t.internalStakeData?null:t.internalStakeData.length)||t._loader)))},directives:[i.NgIf,D.c,u.a,i.NgForOf,X.f,v.a,X.g,X.d],styles:[".card-view-stakeholders[_ngcontent-%COMP%]{padding:1rem}.card-view-stakeholders[_ngcontent-%COMP%]   .loader[_ngcontent-%COMP%]{width:100%;height:70vh;display:grid;place-items:center;overflow:hidden}.card-view-stakeholders[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{width:4rem;height:4rem;border-radius:50%;margin-left:2%;margin-top:-12%;display:flex;justify-content:center;align-items:center}.card-view-stakeholders[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;font-size:40px;color:#f0f8ff}.card-view-stakeholders[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{border-radius:50%!important;height:4rem;width:4rem}.card-view-stakeholders[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]{border-radius:8px;border:1px solid var(--Black-20,#d4d6d8);background:var(--Neutral-White,#fff);padding:.5rem;height:7rem;margin:2rem 2rem 1rem}.card-view-stakeholders[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .stakeholder-name[_ngcontent-%COMP%]{color:var(--Black-80,#515965);font-family:Roboto;font-size:16px;font-style:normal;font-weight:600;line-height:14px;letter-spacing:.32px;text-transform:capitalize;padding:5px}.card-view-stakeholders[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .stakeholder-role[_ngcontent-%COMP%]{color:var(--Blue-Grey-60,#8b95a5);font-family:Plus Jakarta Sans;font-size:14px;font-style:normal;font-weight:400;line-height:14px;letter-spacing:.28px;text-transform:capitalize;padding:0 5px}.card-view-stakeholders[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .is-primary[_ngcontent-%COMP%]{margin-left:2%;width:70px;display:flex;justify-content:center;height:25px;border-radius:4px;background:var(--intButton)!important;color:var(--Neutral-White,#fff);font-family:Plus Jakarta Sans;font-size:11px;font-style:normal;font-weight:400;line-height:16px;align-items:center;text-transform:capitalize}.card-view-stakeholders[_ngcontent-%COMP%]   .main-card[_ngcontent-%COMP%]   .is-primary[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:20px!important;align-items:center;display:flex;justify-content:center}.card-view-stakeholders[_ngcontent-%COMP%]   .three-dotz[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.card-view-stakeholders[_ngcontent-%COMP%]   .three-dotz[_ngcontent-%COMP%]   .custom-more-options[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{cursor:pointer}.card-view-stakeholders[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#3c3c3c;font-weight:400;font-size:19px}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}"]}),e})();var le=n("Nzva");function se(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div",18),r["\u0275\u0275elementStart"](1,"div"),r["\u0275\u0275text"](2,"Primary"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](3,"mat-icon"),r["\u0275\u0275text"](4,"bookmark"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]())}let ce=(()=>{class e{constructor(e){this.data=e}ngOnInit(){var e;this.viewInfoData=null===(e=this.data)||void 0===e?void 0:e.selectedData}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](s.a))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["app-view-info-dialog"]],decls:33,vars:14,consts:[[1,"view-stake-info","row"],[1,"profile-brief-comet","col-4"],[1,"brief-comet"],[1,"circle"],[3,"src"],[1,"prof-name",3,"matTooltip"],[1,"prof-aid"],[1,"prof-email"],[1,"profile-detail-comet","col-8"],[1,"col"],[1,"row","header-title"],[1,"col-4"],[1,"row","desc-detail-comet"],[1,"col-4","desc-main"],[1,"user-profile"],[1,"body-desc",3,"matTooltip"],[1,"col-4","prima-holder"],["class","is-primary ml-auto",4,"ngIf"],[1,"is-primary","ml-auto"]],template:function(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div",0),r["\u0275\u0275elementStart"](1,"div",1),r["\u0275\u0275elementStart"](2,"div",2),r["\u0275\u0275elementStart"](3,"div",3),r["\u0275\u0275element"](4,"img",4),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](5,"div",5),r["\u0275\u0275text"](6),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](7,"div",6),r["\u0275\u0275text"](8),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](9,"div",7),r["\u0275\u0275text"](10),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](11,"div",8),r["\u0275\u0275elementStart"](12,"div",9),r["\u0275\u0275elementStart"](13,"div",10),r["\u0275\u0275elementStart"](14,"div",11),r["\u0275\u0275text"](15,"Added By"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](16,"div",11),r["\u0275\u0275text"](17,"Added Date"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](18,"div",11),r["\u0275\u0275text"](19,"Role in this Account"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](20,"div",12),r["\u0275\u0275elementStart"](21,"div",13),r["\u0275\u0275elementStart"](22,"div",14),r["\u0275\u0275element"](23,"img",4),r["\u0275\u0275elementStart"](24,"div",15),r["\u0275\u0275text"](25),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](26,"div",13),r["\u0275\u0275text"](27),r["\u0275\u0275pipe"](28,"dynamicDatePipe"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](29,"div",16),r["\u0275\u0275elementStart"](30,"div",15),r["\u0275\u0275text"](31),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](32,se,5,0,"div",17),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e&&(r["\u0275\u0275advance"](4),r["\u0275\u0275property"]("src",t.viewInfoData.stakeholder_list[0].last_modified_by_profile_url,r["\u0275\u0275sanitizeUrl"]),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("matTooltip",t.viewInfoData.stakeholder_list[0].last_modified_by_name),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"](" ",t.viewInfoData.stakeholder_list[0].last_modified_by_name||" - "," "),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate1"]("A.ID ",t.viewInfoData.aid,""),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate"](t.viewInfoData.email),r["\u0275\u0275advance"](13),r["\u0275\u0275property"]("src",t.viewInfoData.created_by_profile_url,r["\u0275\u0275sanitizeUrl"]),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("matTooltip",t.viewInfoData.created_by_name),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"](" ",t.viewInfoData.created_by_name||" - "," "),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate"](r["\u0275\u0275pipeBind1"](28,12,t.viewInfoData.created_on)),r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("matTooltip",t.viewInfoData.name),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate"](t.viewInfoData.name),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",t.viewInfoData.is_primary))},directives:[u.a,i.NgIf,v.a],pipes:[le.a],styles:[".view-stake-info[_ngcontent-%COMP%]{padding:2rem;display:flex;justify-content:space-around;align-items:center;height:45vh}.view-stake-info[_ngcontent-%COMP%]   .profile-brief-comet[_ngcontent-%COMP%], .view-stake-info[_ngcontent-%COMP%]   .profile-brief-comet[_ngcontent-%COMP%]   .brief-comet[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}.view-stake-info[_ngcontent-%COMP%]   .profile-brief-comet[_ngcontent-%COMP%]   .brief-comet[_ngcontent-%COMP%]{flex-direction:column}.view-stake-info[_ngcontent-%COMP%]   .profile-brief-comet[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{width:5rem;height:5rem;border-radius:50%;display:flex;justify-content:center;align-items:center}.view-stake-info[_ngcontent-%COMP%]   .profile-brief-comet[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;font-size:40px;color:#f0f8ff}.view-stake-info[_ngcontent-%COMP%]   .profile-brief-comet[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{border-radius:50%!important;height:5rem;width:5rem}.view-stake-info[_ngcontent-%COMP%]   .profile-brief-comet[_ngcontent-%COMP%]   .prof-name[_ngcontent-%COMP%]{color:var(--Blue-Grey-100,#45546e);font-family:Roboto;font-size:20px;font-style:normal;font-weight:700;line-height:24px;text-transform:capitalize;padding:2rem 2rem 0;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;width:300px;text-align:center}.view-stake-info[_ngcontent-%COMP%]   .profile-brief-comet[_ngcontent-%COMP%]   .prof-aid[_ngcontent-%COMP%]{color:var(--Blue-Grey-100,#45546e);font-family:Plus Jakarta Sans;font-size:16px;font-style:normal;font-weight:400;line-height:24px;padding:2%;text-transform:capitalize}.view-stake-info[_ngcontent-%COMP%]   .profile-brief-comet[_ngcontent-%COMP%]   .prof-email[_ngcontent-%COMP%]{color:var(--Blue-Grey-100,#45546e);font-family:Plus Jakarta Sans;font-size:13px;font-style:normal;font-weight:500;line-height:16px;padding:2%;letter-spacing:.26px}.view-stake-info[_ngcontent-%COMP%]   .desc-main[_ngcontent-%COMP%]{display:flex;align-items:center}.view-stake-info[_ngcontent-%COMP%]   .desc-main[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]{display:flex;justify-content:start;align-items:center;gap:5%}.view-stake-info[_ngcontent-%COMP%]   .desc-main[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{border-radius:50%!important;height:25px;width:25px}.view-stake-info[_ngcontent-%COMP%]   .desc-main[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .body-desc[_ngcontent-%COMP%]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;width:95px}.view-stake-info[_ngcontent-%COMP%]   .profile-detail-comet[_ngcontent-%COMP%]   .prima-holder[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}.view-stake-info[_ngcontent-%COMP%]   .profile-detail-comet[_ngcontent-%COMP%]   .prima-holder[_ngcontent-%COMP%]   .body-desc[_ngcontent-%COMP%]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;width:200px}.view-stake-info[_ngcontent-%COMP%]   .profile-detail-comet[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]{color:var(--Black-60,#7d838b);font-weight:400}.view-stake-info[_ngcontent-%COMP%]   .profile-detail-comet[_ngcontent-%COMP%]   .desc-detail-comet[_ngcontent-%COMP%], .view-stake-info[_ngcontent-%COMP%]   .profile-detail-comet[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]{font-family:Plus Jakarta Sans;font-size:13px;font-style:normal;line-height:16px;letter-spacing:.26px;text-transform:capitalize}.view-stake-info[_ngcontent-%COMP%]   .profile-detail-comet[_ngcontent-%COMP%]   .desc-detail-comet[_ngcontent-%COMP%]{color:var(--Blue-Grey-100,#45546e);font-weight:500;margin-top:1%}.view-stake-info[_ngcontent-%COMP%]   .profile-detail-comet[_ngcontent-%COMP%]   .is-primary[_ngcontent-%COMP%]{margin-left:2%;width:70px;padding-left:5px;display:flex;justify-content:center;height:25px;border-radius:4px;background:var(--intButton)!important;color:var(--Neutral-White,#fff);font-family:Plus Jakarta Sans;font-size:11px;font-style:normal;font-weight:400;line-height:16px;align-items:center;text-transform:capitalize}.view-stake-info[_ngcontent-%COMP%]   .profile-detail-comet[_ngcontent-%COMP%]   .is-primary[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:20px!important;align-items:center;display:flex;justify-content:center}"]}),e})();function de(e,t){1&e&&r["\u0275\u0275element"](0,"mat-spinner",7)}function pe(e,t){if(1&e&&r["\u0275\u0275element"](0,"img",8),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275property"]("src",(null==e._stakeholderService.loaderConfig?null:e._stakeholderService.loaderConfig.loaderUrl)||"https://assets.kebs.app/KEBS_MAIN_GIF.gif",r["\u0275\u0275sanitizeUrl"])("width",(null==e._stakeholderService.loaderConfig?null:e._stakeholderService.loaderConfig.width)||70)("height",(null==e._stakeholderService.loaderConfig?null:e._stakeholderService.loaderConfig.height)||70)("matTooltip",(null==e._stakeholderService.loaderConfig?null:e._stakeholderService.loaderConfig.toolTipContent)||"Loading...")}}function me(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",4),r["\u0275\u0275template"](1,de,1,0,"mat-spinner",5),r["\u0275\u0275template"](2,pe,1,4,"img",6),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",!e._stakeholderService.loaderConfig),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",e._stakeholderService.loaderConfig)}}function ge(e,t){if(1&e&&(r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275elementStart"](1,"div",12),r["\u0275\u0275elementStart"](2,"div"),r["\u0275\u0275text"](3),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngClass","col-"+e.col),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate1"](" ",e.title," ")}}function fe(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div",31),r["\u0275\u0275elementStart"](1,"div"),r["\u0275\u0275text"](2,"Primary"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](3,"mat-icon"),r["\u0275\u0275text"](4,"bookmark"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]())}function he(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"button",29),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const t=r["\u0275\u0275nextContext"]().$implicit;return r["\u0275\u0275nextContext"](2).onOptionClick("remove",t)})),r["\u0275\u0275elementStart"](1,"div",28),r["\u0275\u0275elementStart"](2,"mat-icon"),r["\u0275\u0275text"](3,"delete "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](4,"div"),r["\u0275\u0275text"](5,"Remove"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}}function ue(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275elementStart"](1,"div",13),r["\u0275\u0275elementStart"](2,"div",14),r["\u0275\u0275text"](3),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](4,"div",15),r["\u0275\u0275elementStart"](5,"div",16),r["\u0275\u0275element"](6,"img",17),r["\u0275\u0275elementStart"](7,"div",18),r["\u0275\u0275text"](8),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](9,fe,5,0,"div",19),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](10,"div",20),r["\u0275\u0275text"](11),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](12,"div",21),r["\u0275\u0275text"](13),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](14,"div",21),r["\u0275\u0275elementStart"](15,"div",16),r["\u0275\u0275element"](16,"img",17),r["\u0275\u0275elementStart"](17,"div",18),r["\u0275\u0275text"](18),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](19,"div",22),r["\u0275\u0275text"](20),r["\u0275\u0275pipe"](21,"dynamicDatePipe"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](22,"div",23),r["\u0275\u0275elementStart"](23,"mat-icon",24),r["\u0275\u0275text"](24,"more_vert"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](25,"mat-menu",25,26),r["\u0275\u0275elementStart"](27,"button",27),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const n=t.$implicit;return r["\u0275\u0275nextContext"](2).onOptionClick("asPrimary",n)})),r["\u0275\u0275elementStart"](28,"div",28),r["\u0275\u0275elementStart"](29,"mat-icon"),r["\u0275\u0275text"](30,"bookmark"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](31,"div"),r["\u0275\u0275text"](32,"Mark As Primary"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](33,"button",29),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const n=t.$implicit;return r["\u0275\u0275nextContext"](2).onOptionClick("applyNotification",n)})),r["\u0275\u0275elementStart"](34,"div",28),r["\u0275\u0275elementStart"](35,"mat-icon"),r["\u0275\u0275text"](36,"notifications"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](37,"div"),r["\u0275\u0275text"](38,"Applicable Notification"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](39,"button",29),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const n=t.$implicit;return r["\u0275\u0275nextContext"](2).onOptionClick("replace",n)})),r["\u0275\u0275elementStart"](40,"div",28),r["\u0275\u0275elementStart"](41,"mat-icon"),r["\u0275\u0275text"](42,"find_replace "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](43,"div"),r["\u0275\u0275text"](44,"Replace"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](45,"button",29),r["\u0275\u0275listener"]("click",(function(){r["\u0275\u0275restoreView"](e);const n=t.$implicit;return r["\u0275\u0275nextContext"](2).onOptionClick("viewInfo",n)})),r["\u0275\u0275elementStart"](46,"div",28),r["\u0275\u0275elementStart"](47,"mat-icon"),r["\u0275\u0275text"](48,"account_circle "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](49,"div"),r["\u0275\u0275text"](50,"View Info"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](51,he,6,0,"button",30),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275element"](52,"hr"),r["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=t.$implicit,n=r["\u0275\u0275reference"](26);r["\u0275\u0275advance"](3),r["\u0275\u0275textInterpolate"](e.aid||"-"),r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("src",e.stakeholder_list[0].last_modified_by_profile_url,r["\u0275\u0275sanitizeUrl"]),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("matTooltip",e.stakeholder_list[0].last_modified_by_name),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"](" ",e.stakeholder_list[0].last_modified_by_name||" - "," "),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",e.is_primary),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("matTooltip",e.stakeholder_list[0].employee_email),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate"](e.stakeholder_list[0].employee_email||" - "),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate"](e.name||" - "),r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("src",e.created_by_profile_url,r["\u0275\u0275sanitizeUrl"]),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("matTooltip",e.created_by_name),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"](" ",e.created_by_name||" - "," "),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate"](r["\u0275\u0275pipeBind1"](21,15,e.created_on)),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("matMenuTriggerFor",n),r["\u0275\u0275advance"](5),r["\u0275\u0275property"]("disabled",e.is_primary),r["\u0275\u0275advance"](24),r["\u0275\u0275property"]("ngIf",!e.is_primary)}}function ve(e,t){if(1&e&&(r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275elementStart"](1,"div",9),r["\u0275\u0275template"](2,ge,4,2,"ng-container",10),r["\u0275\u0275elementEnd"](),r["\u0275\u0275element"](3,"hr"),r["\u0275\u0275elementStart"](4,"div",11),r["\u0275\u0275template"](5,ue,53,17,"ng-container",10),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementContainerEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngForOf",e.headerColumns),r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("ngForOf",e.internalStakeData)}}function _e(e,t){1&e&&(r["\u0275\u0275elementContainerStart"](0,32),r["\u0275\u0275elementStart"](1,"div"),r["\u0275\u0275elementStart"](2,"h4",33),r["\u0275\u0275text"](3," Assemble Team Members "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](4,"div",34),r["\u0275\u0275element"](5,"img",35),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementContainerEnd"]())}let ye=(()=>{class e{constructor(e,t,n,i){this._dialog=e,this._stakeholderService=t,this.toaster=n,this._router=i,this.internalStakeData=[],this.initialStakeholderList=[],this.updateOwner=0,this.editAccessISH=!0,this.headerColumns=[{title:"AID",key_value:"aid",col:1},{title:"Name",key_value:"last_modified_by_name",col:2},{title:"Email",key_value:"employee_email",col:3},{title:"Role",key_value:"name",col:2},{title:"Assigned By",key_value:"created_by_name",col:2},{title:"Assigned date",key_value:"created_on",col:2}]}ngOnInit(){this.accountId=this._router.url.split("/")[3],this.getOpportunityTeamDetails(this.accountId),this.editAccessISH=this._stakeholderService.checkEditAcessISH(),this.getTabLoadingObservableSubscription=this._stakeholderService.getTabLoadingObservable.subscribe(e=>{e&&(this.internalStakeData=[],this.getOpportunityTeamDetails(this.accountId))})}getOpportunityTeamDetails(e){return Object(a.c)(this,void 0,void 0,(function*(){try{this._loader=!0,this.internalStakeData=[],this.internalStakeData=yield this._stakeholderService.getAccountTeamDetails(e)}catch(t){console.error("An error occurred while fetching opportunity team details:",t),this.toaster.showWarning("Error occurred while fetching team details","Dismiss")}finally{this._loader=!1}}))}sortThisThing(e){if(e.sortOrder=e.sortOrder?"A"===e.sortOrder?"D":"D"===e.sortOrder?"N":"A":"A",console.log("Sort Order:",e.sortOrder),"A"===e.sortOrder)this.internalStakeData=this.internalStakeData.sort((t,n)=>this.compare(t[e.key_name],n[e.key_name]));else{if("D"!==e.sortOrder)return this.internalStakeData;this.internalStakeData=this.internalStakeData.sort((t,n)=>this.compare(n[e.key_name],t[e.key_name]))}}compare(e,t){return e<t?-1:e>t?1:0}getSortTooltip(e){switch(e){case"A":return"Sort Descending";case"D":return"Remove Sort";case"N":return"Sort Ascending";default:return"Sort"}}onOptionClick(e,t){switch(e){case"asPrimary":this.markAsPrimary(t);break;case"applyNotification":this.applicableNotificaiton(t);break;case"replace":this.openPersonAddDialog(t);break;case"viewInfo":this.openViewInfo(t);break;case"remove":this.removeStakeholder(t,"remove");break;default:console.warn("Unknown icon clicked: "+e)}}markAsPrimary(e){return Object(a.c)(this,void 0,void 0,(function*(){if(this.editAccessISH)if(e.is_primary)this.toaster.showWarning("Already a Primary Stakeholder!","");else try{yield this._stakeholderService.updateInternalStakeholderAsPrimary(e.stakeholder_table_id,e.owner_type,e.accountId,e.practice_id,{aid:e.aid,oid:e.oid}),0==this.updateOwner&&(this.toaster.showSuccess("Marked As Primary!","",""),this.ngOnInit())}catch(t){console.log(t),this.toaster.showError("Error occurred while marking as primary","","")}else this.toaster.showWarning("Access Restricted!","")}))}applicableNotificaiton(e){return Object(a.c)(this,void 0,void 0,(function*(){this._dialog.open(K,{maxWidth:"40%",minWidth:"40%",data:{selectedData:e}}).afterClosed().subscribe(e=>{e&&"submit"==e.event&&this._stakeholderService.setTabLoadingObservable(!0)})}))}openPersonAddDialog(e){if(this.editAccessISH)return this._dialog.open(F,{width:"70%",minHeight:"65vh",data:{title:"Replace Stakeholder",is_primary:e.is_primary||!1,role:e.name}}).afterClosed().subscribe(t=>Object(a.c)(this,void 0,void 0,(function*(){if(t&&"submit"==t.event){if(t&&"Member already assigned!"==t.result)return void this.toaster.showWarning("Cannot Replace as member already assigned!","");(yield this.removeStakeholder(e,"replace"))?this.toaster.showSuccess("Stakeholder Replaced Successfully!","",""):this.toaster.showError("Stakeholder Replacement Failed.","","")}})));this.toaster.showWarning("Access Restricted!","")}openViewInfo(e){this._dialog.open(ce,{width:"60%",minHeight:"45vh",data:{selectedData:e}})}removeStakeholder(e,t){return Object(a.c)(this,void 0,void 0,(function*(){if(this.editAccessISH){if(null!=e)try{if(yield this._stakeholderService.removeMember(e.stakeholder_table_id),0==this.updateOwner)return"remove"===t&&this.toaster.showSuccess("Stakeholder Removed Successfully!","",""),this.ngOnInit(),!0}catch(n){console.log(n),this.toaster.showError("Error occurred while removing stakeholder","","")}return!1}this.toaster.showWarning("Access Restricted!","")}))}ngOnDestroy(){this.getTabLoadingObservableSubscription&&this.getTabLoadingObservableSubscription.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](s.b),r["\u0275\u0275directiveInject"](p.a),r["\u0275\u0275directiveInject"](m.a),r["\u0275\u0275directiveInject"](o.g))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["app-internal-stakeholder-list"]],decls:4,vars:3,consts:[[1,"container-fluid","is-styles","p-3"],["class","loader",4,"ngIf"],[4,"ngIf"],["class","d-flex justify-content-center align-items-center slide-from-down","style","height: 70vh;flex-direction: column;",4,"ngIf"],[1,"loader"],["matTooltip","Loading...","class","spinner-align","diameter","35",4,"ngIf"],["alt","Loading...",3,"src","width","height","matTooltip",4,"ngIf"],["matTooltip","Loading...","diameter","35",1,"spinner-align"],["alt","Loading...",3,"src","width","height","matTooltip"],[1,"header-wrapper","row"],[4,"ngFor","ngForOf"],[1,"response-wrapper"],[1,"header-title",3,"ngClass"],[1,"response-row","row"],[1,"col-1"],[1,"col-2","d-flex","align-items-center"],[1,"user-profile"],[3,"src"],[1,"body-desc",3,"matTooltip"],["class","is-primary",4,"ngIf"],[1,"col-3","body-desc",3,"matTooltip"],[1,"col-2","body-desc"],[1,"col-1","body-desc"],[1,"col-1","more-options",3,"matMenuTriggerFor"],["matTooltip","More Options"],[1,"custom-menu"],["optionsMenu","matMenu"],["mat-menu-item","",3,"disabled","click"],[1,"d-flex","align-items-center","pop-button"],["mat-menu-item","",3,"click"],["mat-menu-item","",3,"click",4,"ngIf"],[1,"is-primary"],[1,"d-flex","justify-content-center","align-items-center","slide-from-down",2,"height","70vh","flex-direction","column"],[1,"mt-4","d-flex","justify-content-center","align-items-center","mb-4","slide-in-top"],[1,"d-flex","justify-content-center","align-items-center","slide-from-down"],["src","https://assets.kebs.app/images/assembleteam.png","height","240px","width","285px",1,"m-auto"]],template:function(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div",0),r["\u0275\u0275template"](1,me,3,2,"div",1),r["\u0275\u0275template"](2,ve,6,2,"ng-container",2),r["\u0275\u0275template"](3,_e,6,0,"ng-container",3),r["\u0275\u0275elementEnd"]()),2&e&&(r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",t._loader),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",t.internalStakeData&&(null==t.internalStakeData?null:t.internalStakeData.length)>0&&!t._loader),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",!(t.internalStakeData&&0!==(null==t.internalStakeData?null:t.internalStakeData.length)||t._loader)))},directives:[i.NgIf,D.c,u.a,i.NgForOf,i.NgClass,X.f,v.a,X.g,X.d],pipes:[le.a],styles:[".is-styles[_ngcontent-%COMP%]{overflow-y:scroll;height:70vh}.is-styles[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]{display:flex;justify-content:start;align-items:center;gap:5%}.is-styles[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{border-radius:50%!important;height:25px;width:25px}.is-styles[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .body-desc[_ngcontent-%COMP%]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;width:90px}.is-styles[_ngcontent-%COMP%]   .loader[_ngcontent-%COMP%]{width:100%;height:-webkit-fill-available;display:grid;place-items:center;overflow:hidden}.is-styles[_ngcontent-%COMP%]   .header-wrapper[_ngcontent-%COMP%]{display:flex;align-items:center}.is-styles[_ngcontent-%COMP%]   .header-wrapper[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:flex-start;color:var(--Blue-Grey-50,#b9c0ca);font-family:Plus Jakarta Sans;font-size:11px;gap:4%;font-style:normal;font-weight:400;line-height:16px;letter-spacing:.22px;text-transform:uppercase}.is-styles[_ngcontent-%COMP%]   .header-wrapper[_ngcontent-%COMP%]   .sorting-thing[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}.is-styles[_ngcontent-%COMP%]   .header-wrapper[_ngcontent-%COMP%]   .sorting-thing[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:12px;color:#b9c0ca;font-weight:600;width:10px;cursor:pointer;margin-top:10px}.is-styles[_ngcontent-%COMP%]   .header-wrapper[_ngcontent-%COMP%]   hr[_ngcontent-%COMP%]{margin-top:.5%}.is-styles[_ngcontent-%COMP%]   .response-wrapper[_ngcontent-%COMP%]   .response-row[_ngcontent-%COMP%]{color:var(--Blue-Grey-100,#45546e);font-family:Plus Jakarta Sans;font-size:12px;font-style:normal;font-weight:500;line-height:16px;display:flex;align-items:center}.is-styles[_ngcontent-%COMP%]   .response-wrapper[_ngcontent-%COMP%]   .response-row[_ngcontent-%COMP%]   .is-primary[_ngcontent-%COMP%]{margin-left:2%;width:70px;padding-left:5px;display:flex;justify-content:center;height:21px;border-radius:4px;background:var(--intButton)!important;color:var(--Neutral-White,#fff);font-family:Plus Jakarta Sans;font-size:11px;font-style:normal;font-weight:400;line-height:16px;align-items:center;text-transform:capitalize}.is-styles[_ngcontent-%COMP%]   .response-wrapper[_ngcontent-%COMP%]   .response-row[_ngcontent-%COMP%]   .is-primary[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:17px!important;align-items:center;display:flex;justify-content:center}.is-styles[_ngcontent-%COMP%]   .response-wrapper[_ngcontent-%COMP%]   .more-options[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:20px;cursor:pointer}.is-styles[_ngcontent-%COMP%]   .response-wrapper[_ngcontent-%COMP%]   .custom-menu[_ngcontent-%COMP%]{color:var(--Black-100,#111434);font-family:Plus Jakarta Sans;font-size:11px;font-style:normal;font-weight:400;line-height:16px;letter-spacing:.22px;text-transform:capitalize;border-radius:4px}.is-styles[_ngcontent-%COMP%]   .response-wrapper[_ngcontent-%COMP%]   .custom-menu[_ngcontent-%COMP%]   .pop-button[_ngcontent-%COMP%]{display:flex;align-items:center;gap:5%!important}.is-styles[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#3c3c3c;font-weight:400;font-size:19px}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}"]}),e})();function Ce(e,t){1&e&&r["\u0275\u0275element"](0,"mat-spinner",7)}function xe(e,t){if(1&e&&r["\u0275\u0275element"](0,"img",8),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275property"]("src",(null==e._stakeholderService.loaderConfig?null:e._stakeholderService.loaderConfig.loaderUrl)||"https://assets.kebs.app/KEBS_MAIN_GIF.gif",r["\u0275\u0275sanitizeUrl"])("width",(null==e._stakeholderService.loaderConfig?null:e._stakeholderService.loaderConfig.width)||70)("height",(null==e._stakeholderService.loaderConfig?null:e._stakeholderService.loaderConfig.height)||70)("matTooltip",(null==e._stakeholderService.loaderConfig?null:e._stakeholderService.loaderConfig.toolTipContent)||"Loading...")}}function Oe(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",4),r["\u0275\u0275template"](1,Ce,1,0,"mat-spinner",5),r["\u0275\u0275template"](2,xe,1,4,"img",6),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",!e._stakeholderService.loaderConfig),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",e._stakeholderService.loaderConfig)}}function we(e,t){if(1&e&&(r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275elementStart"](1,"div",12),r["\u0275\u0275elementStart"](2,"div"),r["\u0275\u0275text"](3),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngClass","col-"+e.col),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate1"](" ",e.title," ")}}function ke(e,t){if(1&e&&(r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275elementStart"](1,"div",13),r["\u0275\u0275elementStart"](2,"div",14),r["\u0275\u0275text"](3),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](4,"div",15),r["\u0275\u0275elementStart"](5,"div",16),r["\u0275\u0275element"](6,"img",17),r["\u0275\u0275elementStart"](7,"div",18),r["\u0275\u0275text"](8),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](9,"div",19),r["\u0275\u0275text"](10),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](11,"div",20),r["\u0275\u0275text"](12),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](13,"div",20),r["\u0275\u0275elementStart"](14,"div",16),r["\u0275\u0275element"](15,"img",17),r["\u0275\u0275elementStart"](16,"div",18),r["\u0275\u0275text"](17),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](18,"div",21),r["\u0275\u0275text"](19),r["\u0275\u0275pipe"](20,"dynamicDatePipe"),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275element"](21,"hr"),r["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;r["\u0275\u0275advance"](3),r["\u0275\u0275textInterpolate"](e.aid),r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("src",e.stakeholder_list[0].last_modified_by_profile_url,r["\u0275\u0275sanitizeUrl"]),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("matTooltip",e.stakeholder_list[0].last_modified_by_name),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"](" ",e.stakeholder_list[0].last_modified_by_name||" - "," "),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("matTooltip",e.stakeholder_list[0].employee_email),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"](" ",e.stakeholder_list[0].employee_email||" - "," "),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate"](e.name||" - "),r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("src",e.created_by_profile_url,r["\u0275\u0275sanitizeUrl"]),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("matTooltip",e.created_by_name),r["\u0275\u0275advance"](1),r["\u0275\u0275textInterpolate1"](" ",e.created_by_name||" - "," "),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate"](r["\u0275\u0275pipeBind1"](20,11,e.created_on))}}function Se(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div"),r["\u0275\u0275elementStart"](1,"div",9),r["\u0275\u0275template"](2,we,4,2,"ng-container",10),r["\u0275\u0275elementEnd"](),r["\u0275\u0275element"](3,"hr"),r["\u0275\u0275elementStart"](4,"div",11),r["\u0275\u0275template"](5,ke,22,13,"ng-container",10),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngForOf",e.headerColumns),r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("ngForOf",e.pastStakeData)}}function Pe(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div",22),r["\u0275\u0275elementStart"](1,"div"),r["\u0275\u0275elementStart"](2,"h4",23),r["\u0275\u0275text"](3," Assemble Team Members "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](4,"div",24),r["\u0275\u0275element"](5,"img",25),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]())}let Me=(()=>{class e{constructor(e,t,n){this._stakeholderService=e,this.toaster=t,this._router=n,this.pastStakeData=[],this.initialStakeholderList=[],this.updateOwner=0,this.headerColumns=[{title:"AID",col:1},{title:"Name",col:2},{title:"Email",col:3},{title:"Role",col:2},{title:"Assigned By",col:2},{title:"Assigned date",col:2}]}ngOnInit(){this.accountId=this._router.url.split("/")[3],this.getPastStakeholders(this.accountId)}getPastStakeholders(e){return Object(a.c)(this,void 0,void 0,(function*(){try{this._loader=!0,this.pastStakeData=yield this._stakeholderService.getPastStakeholders(e)}catch(t){console.error("An error occurred while fetching opportunity team details:",t),this.toaster.showWarning("Error occurred while fetching team details","Dismiss")}finally{this._loader=!1}}))}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](p.a),r["\u0275\u0275directiveInject"](m.a),r["\u0275\u0275directiveInject"](o.g))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["app-past-members-list"]],decls:4,vars:3,consts:[[1,"container-fluid","pm-styles","p-3"],["class","loader",4,"ngIf"],[4,"ngIf"],["class","d-flex justify-content-center align-items-center slide-from-down","style","height: 70vh; flex-direction: column;",4,"ngIf"],[1,"loader"],["matTooltip","Loading...","class","spinner-align","diameter","35",4,"ngIf"],["alt","Loading...",3,"src","width","height","matTooltip",4,"ngIf"],["matTooltip","Loading...","diameter","35",1,"spinner-align"],["alt","Loading...",3,"src","width","height","matTooltip"],[1,"header-wrapper","row"],[4,"ngFor","ngForOf"],[1,"response-wrapper"],[1,"header-title",3,"ngClass"],[1,"response-row","row"],[1,"col-1"],[1,"col-2","d-flex","align-items-center"],[1,"user-profile"],[3,"src"],[1,"body-desc",3,"matTooltip"],[1,"col-3","body-desc",3,"matTooltip"],[1,"col-2","body-desc"],[1,"col-2"],[1,"d-flex","justify-content-center","align-items-center","slide-from-down",2,"height","70vh","flex-direction","column"],[1,"mt-4","d-flex","justify-content-center","align-items-center","mb-4","slide-in-top"],[1,"d-flex","justify-content-center","align-items-center","slide-from-down"],["src","https://assets.kebs.app/images/assembleteam.png","height","240px","width","285px",1,"m-auto"]],template:function(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div",0),r["\u0275\u0275template"](1,Oe,3,2,"div",1),r["\u0275\u0275template"](2,Se,6,2,"div",2),r["\u0275\u0275template"](3,Pe,6,0,"div",3),r["\u0275\u0275elementEnd"]()),2&e&&(r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",t._loader),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",t.pastStakeData&&t.pastStakeData.length>0&&!t._loader),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",!(t.pastStakeData&&0!==(null==t.pastStakeData?null:t.pastStakeData.length)||t._loader)))},directives:[i.NgIf,D.c,u.a,i.NgForOf,i.NgClass],pipes:[le.a],styles:[".pm-styles[_ngcontent-%COMP%]{overflow-y:scroll;height:70vh}.pm-styles[_ngcontent-%COMP%]   .loader[_ngcontent-%COMP%]{width:100%;height:70vh;display:grid;place-items:center}.pm-styles[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]{display:flex;justify-content:start;align-items:center;gap:5%}.pm-styles[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{border-radius:50%!important;height:25px;width:25px}.pm-styles[_ngcontent-%COMP%]   .user-profile[_ngcontent-%COMP%]   .body-desc[_ngcontent-%COMP%]{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;width:95px}.pm-styles[_ngcontent-%COMP%]   .header-wrapper[_ngcontent-%COMP%]{display:flex;align-items:center}.pm-styles[_ngcontent-%COMP%]   .header-wrapper[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:flex-start;color:var(--Blue-Grey-50,#b9c0ca);font-family:Plus Jakarta Sans;font-size:11px;font-style:normal;font-weight:400;line-height:16px;letter-spacing:.22px;text-transform:uppercase}.pm-styles[_ngcontent-%COMP%]   .header-wrapper[_ngcontent-%COMP%]   .sorting-thing[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}.pm-styles[_ngcontent-%COMP%]   .header-wrapper[_ngcontent-%COMP%]   .sorting-thing[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:12px;color:#b9c0ca;font-weight:600;width:10px;margin-top:3%}.pm-styles[_ngcontent-%COMP%]   .response-wrapper[_ngcontent-%COMP%]   .response-row[_ngcontent-%COMP%]{color:var(--Blue-Grey-100,#45546e);font-family:Plus Jakarta Sans;font-size:12px;font-style:normal;font-weight:500;line-height:16px}.pm-styles[_ngcontent-%COMP%]   .response-wrapper[_ngcontent-%COMP%]   .response-row[_ngcontent-%COMP%]   .is-primary[_ngcontent-%COMP%]{margin-left:2%;width:70px;display:flex;justify-content:center;height:25px;border-radius:4px;background:var(--intButton)!important;color:var(--Neutral-White,#fff);font-family:Plus Jakarta Sans;font-size:11px;font-style:normal;font-weight:400;line-height:16px;align-items:center;text-transform:capitalize}.pm-styles[_ngcontent-%COMP%]   .response-wrapper[_ngcontent-%COMP%]   .response-row[_ngcontent-%COMP%]   .is-primary[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:20px!important;align-items:center;display:flex;justify-content:center}.pm-styles[_ngcontent-%COMP%]   .response-wrapper[_ngcontent-%COMP%]   .more-options[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:20px;cursor:pointer}.pm-styles[_ngcontent-%COMP%]   .response-wrapper[_ngcontent-%COMP%]   .custom-menu[_ngcontent-%COMP%]{color:var(--Black-100,#111434);font-family:Plus Jakarta Sans;font-size:11px;font-style:normal;font-weight:400;line-height:16px;letter-spacing:.22px;text-transform:capitalize;border-radius:4px}.pm-styles[_ngcontent-%COMP%]   .response-wrapper[_ngcontent-%COMP%]   .custom-menu[_ngcontent-%COMP%]   .pop-button[_ngcontent-%COMP%]{display:flex;align-items:center;gap:5%!important}.pm-styles[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#3c3c3c;font-weight:400;font-size:19px}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}"]}),e})();var be=n("jaxi");const Ee=function(e){return{"btn-toggle-selected":e}};function Ie(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"mat-button-toggle",7,8),r["\u0275\u0275listener"]("change",(function(){r["\u0275\u0275restoreView"](e);const t=r["\u0275\u0275nextContext"]().$implicit;return r["\u0275\u0275nextContext"]().selectToggle(t.field_name)})),r["\u0275\u0275text"](2),r["\u0275\u0275elementEnd"]()}if(2&e){const e=r["\u0275\u0275reference"](1),t=r["\u0275\u0275nextContext"]().$implicit,n=r["\u0275\u0275nextContext"]();r["\u0275\u0275property"]("value",t.field_name)("routerLink",t.link)("active",e.is_active)("ngClass",r["\u0275\u0275pureFunction1"](5,Ee,n.selectedToggle==t.field_name)),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate1"](" ",t.label," ")}}function De(e,t){if(1&e&&(r["\u0275\u0275elementContainerStart"](0),r["\u0275\u0275template"](1,Ie,3,7,"mat-button-toggle",6),r["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf",e.is_active)}}const Ae=function(e){return{clicked:e}};function je(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"div",9),r["\u0275\u0275elementStart"](1,"button",10),r["\u0275\u0275listener"]("click",(function(){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"]().onIconClick("list_alt")})),r["\u0275\u0275elementStart"](2,"mat-icon"),r["\u0275\u0275text"](3," list_alt "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](4,"button",11),r["\u0275\u0275listener"]("click",(function(){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"]().onIconClick("cards")})),r["\u0275\u0275elementStart"](5,"mat-icon"),r["\u0275\u0275text"](6," view_agenda "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](7,"button",12),r["\u0275\u0275listener"]("click",(function(){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"]().onIconClick("person_add")})),r["\u0275\u0275elementStart"](8,"mat-icon"),r["\u0275\u0275text"](9," person_add "),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&e){const e=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngClass",r["\u0275\u0275pureFunction1"](2,Ae,"list_alt"===e.selectedIcon)),r["\u0275\u0275advance"](3),r["\u0275\u0275property"]("ngClass",r["\u0275\u0275pureFunction1"](4,Ae,"cards"===e.selectedIcon))}}const Te=function(){return{display:"flex","justify-content":"space-between"}};let Le=(()=>{class e{constructor(e,t,n,i,o){this._router=e,this._dialog=t,this._activatedRoute=n,this._stakeholderService=i,this.toaster=o,this.isComponentLoading=!1,this.selectedToggle="internal_stakeholder",this.toggle="#EE4961",this.formConfig=[],this.editAccessISH=!0,this.stakeholderList=[{field_name:"internal_stakeholder",is_active:!0,is_mandant:!1,label:"Internal Stakeholders",link:"internal-stakeholder-list"},{field_name:"past_members",is_active:!0,is_mandant:!1,label:"Past Members",link:"past-members-list"}]}ngOnInit(){return Object(a.c)(this,void 0,void 0,(function*(){this.updateStateBasedOnRoute(this._router.url),this._router.events.subscribe(e=>{e instanceof o.c&&this.updateStateBasedOnRoute(e.url)});const e=yield this._stakeholderService.getTheme();document.documentElement.style.setProperty("--intButton",e.tabColor),this.editAccessISH=this._stakeholderService.checkEditAcessISH()}))}updateStateBasedOnRoute(e){this.currentRoute=e.split("/")[6];const t=this.stakeholderList.find(e=>e.link===this.currentRoute);t&&(this.selectedToggle=t.field_name),"past-members-list"===this.currentRoute||"internal-stakeholder-list"===this.currentRoute?this.selectedIcon="list_alt":"internal-stakeholder-view"===this.currentRoute&&(this.selectedIcon="cards")}selectToggle(e){this.selectedToggle=e}onIconClick(e){switch(e){case"cards":this.openCardViewDialog();break;case"info":break;case"person_add":this.openPersonAddDialog();break;case"list_alt":this.openListView()}this.selectedIcon=e}openPersonAddDialog(){this.editAccessISH?this._dialog.open(F,{width:"70%",minHeight:"65vh",data:{title:"Add Stakeholder"}}).afterClosed().subscribe(e=>{e&&"submit"==e.event&&this._stakeholderService.setTabLoadingObservable(!0)}):this.toaster.showWarning("Access Restricted!","")}openCardViewDialog(){this._router.navigate(["./internal-stakeholder-view"],{relativeTo:this._activatedRoute})}openListView(){this._router.navigate(["./internal-stakeholder-list"],{relativeTo:this._activatedRoute})}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](o.g),r["\u0275\u0275directiveInject"](s.b),r["\u0275\u0275directiveInject"](o.a),r["\u0275\u0275directiveInject"](p.a),r["\u0275\u0275directiveInject"](m.a))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["app-header-toggle"]],decls:6,vars:5,consts:[[1,"container-fluid","crm-internal-stakeholders"],[1,"row","pt-3",3,"ngStyle"],[1,"col-5"],[3,"value"],[4,"ngFor","ngForOf"],["class","col-3 icon-wrapper",4,"ngIf"],["routerLinkActive","","class","toggle-btn",3,"value","routerLink","active","ngClass","change",4,"ngIf"],["routerLinkActive","",1,"toggle-btn",3,"value","routerLink","active","ngClass","change"],["rla","routerLinkActive"],[1,"col-3","icon-wrapper"],["mat-icon-button","","routerLink","/internal-stakeholder-list","matTooltip","List View",3,"ngClass","click"],["mat-icon-button","","routerLink","/internal-stakeholder-view","matTooltip","Card View",3,"ngClass","click"],["mat-icon-button","","matTooltip","Add Stakeholder",3,"click"]],template:function(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div",0),r["\u0275\u0275elementStart"](1,"div",1),r["\u0275\u0275elementStart"](2,"div",2),r["\u0275\u0275elementStart"](3,"mat-button-toggle-group",3),r["\u0275\u0275template"](4,De,2,1,"ng-container",4),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275template"](5,je,10,6,"div",5),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e&&(r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngStyle",r["\u0275\u0275pureFunction0"](4,Te)),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("value",t.selectedToggle),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngForOf",t.stakeholderList),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","internal_stakeholder"===t.selectedToggle))},directives:[i.NgStyle,be.b,i.NgForOf,i.NgIf,be.a,o.i,o.h,i.NgClass,h.a,u.a,v.a],styles:[".crm-internal-stakeholders[_ngcontent-%COMP%]   .toggle-btn[_ngcontent-%COMP%]{min-width:7rem;font-family:Plus Jakarta Sans!important}.crm-internal-stakeholders[_ngcontent-%COMP%]   .btn-toggle-selected[_ngcontent-%COMP%]{font-size:12px!important;background-color:var(--intButton)!important;color:#fff}.crm-internal-stakeholders[_ngcontent-%COMP%]   .filter[_ngcontent-%COMP%]{width:24px;height:24px;gap:16px;cursor:pointer;margin-top:10px;margin-left:15px}.crm-internal-stakeholders[_ngcontent-%COMP%]   .filter-icon[_ngcontent-%COMP%]{width:18px;height:12px;top:6px;left:3px;color:#5f6c81;font-size:20px}.crm-internal-stakeholders[_ngcontent-%COMP%]   .icons[_ngcontent-%COMP%]{white-space:nowrap;display:flex}.crm-internal-stakeholders[_ngcontent-%COMP%]   .add-people[_ngcontent-%COMP%]{width:30px;height:30px;padding:10px;border-radius:30px;gap:10px;background:#f7f9fb;margin-left:10px;cursor:pointer;justify-content:center;margin-top:5px}.crm-internal-stakeholders[_ngcontent-%COMP%]   .add-icon[_ngcontent-%COMP%]{width:19px;height:21px;top:1px;left:4px;color:#52c41a;font-size:20px;margin-top:-5px;margin-left:-5px}.crm-internal-stakeholders[_ngcontent-%COMP%]   .request-people[_ngcontent-%COMP%]{width:30px;height:30px;padding:10px;border-radius:30px;gap:10px;background:#f7f9fb;margin-left:10px;cursor:pointer;justify-content:center;margin-top:5px}.crm-internal-stakeholders[_ngcontent-%COMP%]   .request-icon[_ngcontent-%COMP%]{width:19px;height:21px;top:1px;left:4px;color:#1890ff;font-size:20px;margin-top:-5px;margin-left:-5px}.crm-internal-stakeholders[_ngcontent-%COMP%]   .customize[_ngcontent-%COMP%]{width:24px;height:24px;gap:16px;margin-left:65%;cursor:pointer;margin-top:10px}.crm-internal-stakeholders[_ngcontent-%COMP%]   .customize-icon[_ngcontent-%COMP%]{width:18px;height:12px;top:6px;left:3px;color:#5f6c81;font-size:20px}.crm-internal-stakeholders[_ngcontent-%COMP%]   .project-team[_ngcontent-%COMP%]{height:40vh}.crm-internal-stakeholders[_ngcontent-%COMP%]   .router-component[_ngcontent-%COMP%]{height:71vh}.crm-internal-stakeholders[_ngcontent-%COMP%]   .clicked[_ngcontent-%COMP%]{background-color:#dcdcdc!important;color:var(--intButton)!important}.crm-internal-stakeholders[_ngcontent-%COMP%]   .clicked[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:var(--intButton)!important}.crm-internal-stakeholders[_ngcontent-%COMP%]   .icon-wrapper[_ngcontent-%COMP%]{display:flex;justify-content:space-around;align-items:center}.crm-internal-stakeholders[_ngcontent-%COMP%]   .icon-wrapper[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:23px;color:#7d838b}"]}),e})(),ze=(()=>{class e{constructor(){}ngOnInit(){}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["app-account-stakeholder-landing-page"]],decls:3,vars:0,consts:[[1,"w-100"]],template:function(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"div",0),r["\u0275\u0275element"](1,"app-header-toggle"),r["\u0275\u0275element"](2,"router-outlet"),r["\u0275\u0275elementEnd"]())},directives:[Le,o.l],styles:[""]}),e})();var Ne=n("0GbU");const Fe={application_id:33,object_id:29382},Be=[{path:"",component:ze,canActivate:[Ne.a],data:Fe,children:[{path:"",redirectTo:"internal-stakeholder-list",pathMatch:"full"},{path:"internal-stakeholder-list",component:ye,canActivate:[Ne.a],data:Fe},{path:"internal-stakeholder-view",component:re,canActivate:[Ne.a],data:Fe},{path:"past-members-list",component:Me,canActivate:[Ne.a],data:Fe}]}];let Re=(()=>{class e{}return e.\u0275mod=r["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=r["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[o.k.forChild(Be)],o.k]}),e})();var Ue=n("Wp6s"),Ve=n("bSwM"),Ge=n("iadO"),We=n("bv9b"),Je=n("d3UM"),He=n("xHqg"),$e=n("+0xr"),qe=n("wZkO"),Ye=n("WJ5W"),Ke=n("Xi0T"),Xe=n("IVXw");let Qe=(()=>{class e{}return e.\u0275mod=r["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=r["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,Re,be.c,v.b,We.b,R.e,u.b,He.f,k.c,l.E,l.p,Ge.h,Je.d,Ye.b,P.n,h.b,w.e,P.q,S.c,Ue.d,Ve.b,qe.g,P.k,X.e,Ke.a,D.b,$e.m,Xe.a]]}),e})()}}]);