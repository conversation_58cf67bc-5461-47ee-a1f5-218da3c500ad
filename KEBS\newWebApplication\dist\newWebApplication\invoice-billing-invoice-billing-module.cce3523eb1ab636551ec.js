(window.webpackJsonp=window.webpackJsonp||[]).push([[729,267,634,853,858],{"3vwd":function(t,e,n){"use strict";n.d(e,"a",(function(){return O}));var a=n("fXoL"),o=n("wO+i"),i=n("m5YA"),r=n("B0y8"),l=n("0IaG"),c=n("JLuW"),m=n("ofXK"),d=n("bTqV"),s=n("TU8p"),g=n("NFeN"),p=n("Qu3c"),u=n("tvi8"),b=n("H/9Z");function f(t,e){if(1&t){const t=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"button",2),a["\u0275\u0275listener"]("click",(function(){return a["\u0275\u0275restoreView"](t),a["\u0275\u0275nextContext"]().openPopUp()})),a["\u0275\u0275elementStart"](1,"mat-icon"),a["\u0275\u0275text"](2,"attach_file"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()}if(2&t){const t=a["\u0275\u0275nextContext"]();a["\u0275\u0275propertyInterpolate"]("matBadge",t.fileCount),a["\u0275\u0275property"]("matBadgeHidden",0==t.fileCount)}}function C(t,e){if(1&t){const t=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"button",4),a["\u0275\u0275listener"]("click",(function(){return a["\u0275\u0275restoreView"](t),a["\u0275\u0275nextContext"](2).openPopUp()})),a["\u0275\u0275elementStart"](1,"span",5),a["\u0275\u0275text"](2),a["\u0275\u0275elementEnd"](),a["\u0275\u0275text"](3," Attachments "),a["\u0275\u0275elementEnd"]()}if(2&t){const t=a["\u0275\u0275nextContext"](2);a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate"](t.fileCount)}}function h(t,e){if(1&t&&(a["\u0275\u0275element"](0,"img",10),a["\u0275\u0275pipe"](1,"fileIcon")),2&t){const t=a["\u0275\u0275nextContext"](3);a["\u0275\u0275property"]("src",a["\u0275\u0275pipeBind1"](1,1,t.singleFileDetail.file_format),a["\u0275\u0275sanitizeUrl"])}}function v(t,e){if(1&t&&(a["\u0275\u0275element"](0,"img",10),a["\u0275\u0275pipe"](1,"async"),a["\u0275\u0275pipe"](2,"getUrl")),2&t){const t=a["\u0275\u0275nextContext"](3);a["\u0275\u0275property"]("src",a["\u0275\u0275pipeBind1"](1,1,a["\u0275\u0275pipeBind1"](2,3,t.singleFileDetail.cdn_link)),a["\u0275\u0275sanitizeUrl"])}}function _(t,e){if(1&t){const t=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"button",4),a["\u0275\u0275listener"]("click",(function(){a["\u0275\u0275restoreView"](t);const e=a["\u0275\u0275nextContext"](2);return e.viewFile(e.singleFileDetail)})),a["\u0275\u0275elementStart"](1,"span",6),a["\u0275\u0275template"](2,h,2,3,"img",7),a["\u0275\u0275template"](3,v,3,5,"ng-template",null,8,a["\u0275\u0275templateRefExtractor"]),a["\u0275\u0275elementEnd"](),a["\u0275\u0275text"](5,"\xa0 "),a["\u0275\u0275elementStart"](6,"div",9),a["\u0275\u0275text"](7),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()}if(2&t){const t=a["\u0275\u0275reference"](4),e=a["\u0275\u0275nextContext"](2);a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("ngIf","png"!=e.singleFileDetail.file_format&&"jpg"!=e.singleFileDetail.file_format&&"jpeg"!=e.singleFileDetail.file_format)("ngIfElse",t),a["\u0275\u0275advance"](4),a["\u0275\u0275propertyInterpolate"]("matTooltip",e.singleFileDetail.file_name),a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate1"](" ",e.singleFileDetail.file_name," ")}}function P(t,e){1&t&&(a["\u0275\u0275elementStart"](0,"span"),a["\u0275\u0275text"](1,"-"),a["\u0275\u0275elementEnd"]())}function M(t,e){if(1&t&&(a["\u0275\u0275elementStart"](0,"div"),a["\u0275\u0275template"](1,C,4,1,"button",3),a["\u0275\u0275template"](2,_,8,4,"button",3),a["\u0275\u0275template"](3,P,2,0,"span",1),a["\u0275\u0275elementEnd"]()),2&t){const t=a["\u0275\u0275nextContext"]();a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",t.fileCount>1),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",1==t.fileCount&&t.singleFileDetail),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",0==t.fileCount)}}let O=(()=>{class t{constructor(t,e){this.dialog=t,this._shared=e,this.change=new a.EventEmitter,this.btnColor="#cf0001",this.isDialogOpened=!1,this.isViewFileOpened=!1,this.fileCount=0}ngOnInit(){document.documentElement.style.setProperty("--btnColor",this.btnColor),this.destinationBucket&&this.contextId&&this._shared.getObjectCount(this.destinationBucket,this.contextId).pipe(Object(o.a)("data")).subscribe(t=>{this.fileCount=t,1==this.fileCount&&this.getFileDetail()},t=>{console.error(t)})}getFileDetail(){this._shared.retrieveUploadedObjects(this.destinationBucket,this.contextId).pipe(Object(o.a)("data")).subscribe(t=>{let e=[];e.push(...t),e.length>0&&(this.singleFileDetail=e[0]),console.log(this.singleFileDetail)},t=>{console.error(t)})}openPopUp(){let t=null;0==this.isDialogOpened&&(t=this.dialog.open(r.a,{width:"100%",height:"90%",data:{data:{destinationBucket:this.destinationBucket,routingKey:this.routingKey,contextId:this.contextId,allowEdit:this.allowEdit,myFilesDefaultFolder:this.myFilesDefaultFolder},expHeaderId:this.expHeaderId},disableClose:!0}),this.isDialogOpened=!0),t.afterClosed().subscribe(t=>{this.isDialogOpened=!1;let e=t.contextId;this.fileCount=t.fileCount,this.change.emit(e)})}viewFile(t){console.log(this.isViewFileOpened);let e=t.cdn_link,n=null;0==this.isViewFileOpened&&(this.isViewFileOpened=!0,this._shared.getDownloadUrl(e).subscribe(e=>{n=this.dialog.open(i.a,{width:"100%",height:"100%",data:{selectedFileUrl:e.data,fileFormat:t.file_format,expHeaderId:this.expHeaderId}}),n.afterClosed().subscribe(t=>{this.isViewFileOpened=!1})}))}}return t.\u0275fac=function(e){return new(e||t)(a["\u0275\u0275directiveInject"](l.b),a["\u0275\u0275directiveInject"](c.a))},t.\u0275cmp=a["\u0275\u0275defineComponent"]({type:t,selectors:[["attachment-upload-btn"]],inputs:{destinationBucket:"destinationBucket",routingKey:"routingKey",contextId:"contextId",allowEdit:"allowEdit",myFilesDefaultFolder:"myFilesDefaultFolder",expHeaderId:"expHeaderId",btnColor:"btnColor"},outputs:{change:"change"},decls:2,vars:2,consts:[["mat-icon-button","","matBadgeColor","warn","class","upload-btn",3,"matBadge","matBadgeHidden","click",4,"ngIf"],[4,"ngIf"],["mat-icon-button","","matBadgeColor","warn",1,"upload-btn",3,"matBadge","matBadgeHidden","click"],["mat-button","",3,"click",4,"ngIf"],["mat-button","",3,"click"],[1,"attachment-count"],[1,"thumbnail"],["style","height: 26px; width: 26px; border-radius: 50%","alt","",3,"src",4,"ngIf","ngIfElse"],["showimg",""],[1,"file-name",3,"matTooltip"],["alt","",2,"height","26px","width","26px","border-radius","50%",3,"src"]],template:function(t,e){1&t&&(a["\u0275\u0275template"](0,f,3,2,"button",0),a["\u0275\u0275template"](1,M,4,3,"div",1)),2&t&&(a["\u0275\u0275property"]("ngIf",e.allowEdit),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",!e.allowEdit))},directives:[m.NgIf,d.a,s.a,g.a,p.a],pipes:[u.a,m.AsyncPipe,b.a],styles:[".file-name[_ngcontent-%COMP%]{white-space:nowrap;width:81px;overflow:hidden;text-overflow:ellipsis;display:inline-block}.attachment-count[_ngcontent-%COMP%]{color:var(--btnColor)!important}"]}),t})()},"8SgF":function(t,e,n){"use strict";n.d(e,"a",(function(){return _}));var a=n("mrSG"),o=n("fXoL"),i=n("XNiG"),r=n("Kj3r"),l=n("1G5W"),c=n("3Pt+"),m=n("NJ67"),d=n("F97M"),s=n("XVR1"),g=n("kmnG"),p=n("ofXK"),u=n("qFsG"),b=n("/1cH"),f=n("NFeN"),C=n("FKr1");function h(t,e){if(1&t&&(o["\u0275\u0275elementStart"](0,"mat-label"),o["\u0275\u0275text"](1),o["\u0275\u0275elementEnd"]()),2&t){const t=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate"](t.label)}}function v(t,e){if(1&t&&(o["\u0275\u0275elementStart"](0,"mat-option",7),o["\u0275\u0275elementStart"](1,"small"),o["\u0275\u0275text"](2),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit,n=o["\u0275\u0275nextContext"]();o["\u0275\u0275property"]("hidden",!n.isAutocomplete)("value",t),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate2"]("",t.displayName," | ",t.mail,"")}}let _=(()=>{class t extends m.a{constructor(t,e){super(),this.graphApi=t,this._AppShareService=e,this.userSearchSubject=new i.b,this.isAutocomplete=!1,this.Formwidth="",this.showUserList=new o.EventEmitter,this.selectedUser=new o.EventEmitter,this.label="",this.blur=new o.EventEmitter,this.required=!1,this.fieldCtrl=new c.j,this.disabled=!1,this.readonly=!1,this.isGraphApi=0,this.optClicked=!1,this._onDestroy=new i.b}ngOnInit(){this.userSearchSubject.pipe(Object(r.a)(600)).subscribe(t=>Object(a.c)(this,void 0,void 0,(function*(){if(console.log(t),this.isGraphApi)this.graphApi.getUserSuggestions(t).then(e=>Object(a.c)(this,void 0,void 0,(function*(){if(e&&e.length>0)this.showUserList.emit(e);else{let e=yield this.getUserSuggestionsFromDB(t);this.showUserList.emit(e)}})),e=>Object(a.c)(this,void 0,void 0,(function*(){console.log(e);let n=yield this.getUserSuggestionsFromDB(t);this.showUserList.emit(n)})));else{let e=yield this.getUserSuggestionsFromDB(t);this.showUserList.emit(e)}}))),this.fieldCtrl.valueChanges.pipe(Object(l.a)(this._onDestroy)).subscribe(t=>{console.log(t),t?(this.value=t.id,this.onChange(t.id)):(this.value="",this.onChange(""))})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}ngAfterViewInit(){}searchUser(t){if(this.text=t.target.value,this.optClicked="Enter"==t.key,!this.text)return this.graphApi.userSuggestions=[];this.userSearchSubject.next(this.text)}resetSuggestion(){this.graphApi.userSuggestions=[]}checkAndClearInput(){this.optClicked||0!=this.readonly||this.fieldCtrl.setValue("")}selectedOption(t){this.optClicked=!0,this.selectedUser.emit(t.option.value),this.value=t.option.value,this.blur.emit()}displayUserName(t){return t?t.displayName:""}setDisabledState(t){t?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(t){return Object(a.c)(this,void 0,void 0,(function*(){if(console.log("inisde writevalue"),console.log(t),t)if(this.isGraphApi)this.graphApi.getUserProfile(t).then(e=>Object(a.c)(this,void 0,void 0,(function*(){if(e)this.fieldCtrl.setValue(e);else{let e=yield this.getUserProfileFromDB(t);this.fieldCtrl.setValue(e)}})),e=>Object(a.c)(this,void 0,void 0,(function*(){let e=yield this.getUserProfileFromDB(t);this.fieldCtrl.setValue(e)})));else{let e=yield this.getUserProfileFromDB(t);console.log(e),this.fieldCtrl.setValue(e)}else this.fieldCtrl.setValue("")}))}getUserSuggestionsFromDB(t){return Object(a.c)(this,void 0,void 0,(function*(){let e=yield this._AppShareService.getUserSuggestionsFromDB(t);return this.graphApi.userSuggestions=e.value,e.value}))}getUserProfileFromDB(t){return Object(a.c)(this,void 0,void 0,(function*(){let e=yield this._AppShareService.getUserProfileFromDB(t);return console.log(e),e&&e.length>0?e[0]:""}))}}return t.\u0275fac=function(e){return new(e||t)(o["\u0275\u0275directiveInject"](d.a),o["\u0275\u0275directiveInject"](s.a))},t.\u0275cmp=o["\u0275\u0275defineComponent"]({type:t,selectors:[["app-search-user"]],inputs:{isAutocomplete:"isAutocomplete",Formwidth:"Formwidth",label:"label",required:"required",disabled:"disabled",readonly:"readonly",isGraphApi:"isGraphApi"},outputs:{showUserList:"showUserList",selectedUser:"selectedUser",blur:"blur"},features:[o["\u0275\u0275ProvidersFeature"]([{provide:c.t,useExisting:Object(o.forwardRef)(()=>t),multi:!0}]),o["\u0275\u0275InheritDefinitionFeature"]],decls:9,vars:10,consts:[["appearance","outline","width","Formwidth"],[4,"ngIf"],["matInput","",3,"matAutocomplete","placeholder","required","formControl","disabled","readonly","keyup","focus","focusout"],["matSuffix","",2,"color","#66615B !important","font-size","21px !important"],[3,"hidden","displayWith","optionSelected"],["auto","matAutocomplete"],[3,"hidden","value",4,"ngFor","ngForOf"],[3,"hidden","value"]],template:function(t,e){if(1&t&&(o["\u0275\u0275elementStart"](0,"div"),o["\u0275\u0275elementStart"](1,"mat-form-field",0),o["\u0275\u0275template"](2,h,2,1,"mat-label",1),o["\u0275\u0275elementStart"](3,"input",2),o["\u0275\u0275listener"]("keyup",(function(t){return e.searchUser(t)}))("focus",(function(){return e.resetSuggestion()}))("focusout",(function(){return e.checkAndClearInput()})),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](4,"mat-icon",3),o["\u0275\u0275text"](5,"person_pin"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](6,"mat-autocomplete",4,5),o["\u0275\u0275listener"]("optionSelected",(function(t){return e.selectedOption(t)})),o["\u0275\u0275template"](8,v,3,4,"mat-option",6),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&t){const t=o["\u0275\u0275reference"](7);o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngIf",e.label),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("matAutocomplete",t)("placeholder",e.label)("required",e.required)("formControl",e.fieldCtrl)("disabled",e.disabled)("readonly",e.readonly),o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("hidden",!e.isAutocomplete)("displayWith",e.displayUserName),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngForOf",e.graphApi.userSuggestions)}},directives:[g.c,p.NgIf,u.b,b.d,c.e,c.F,c.v,c.k,f.a,g.i,b.b,p.NgForOf,g.g,C.p],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding-bottom:8px!important}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-underline{bottom:0}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-infix{padding:.65em 0!important;border-top:.54375em solid transparent!important;font-size:13px}.mat-form-field[_ngcontent-%COMP%]     .mat-select-arrow{margin:0 4px!important}"]}),t})()},NJ67:function(t,e,n){"use strict";n.d(e,"a",(function(){return a}));class a{constructor(){this.disabled=!1}onChange(t){}onTouched(t){}writeValue(t){this.value=t}registerOnChange(t){this.onChange=t}registerOnTouched(t){this.onTouched=t}setDisabledState(t){this.disabled=t}}},QUrN:function(t,e,n){"use strict";n.d(e,"a",(function(){return i})),n.d(e,"b",(function(){return l}));var a=n("fXoL");const o=n("wd/R");let i=(()=>{class t{transform(t,...e){return t?o(t).format(e[0]):""}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275pipe=a["\u0275\u0275definePipe"]({name:"amDateFormat",type:t,pure:!0}),t})();const r=new a.InjectionToken("NGX_MOMENT_OPTIONS");let l=(()=>{class t{static forRoot(e){return{ngModule:t,providers:[{provide:r,useValue:Object.assign({},e)}]}}}return t.\u0275mod=a["\u0275\u0275defineNgModule"]({type:t}),t.\u0275inj=a["\u0275\u0275defineInjector"]({factory:function(e){return new(e||t)}}),t})()},S0eQ:function(t,e,n){"use strict";n.r(e),n.d(e,"InvoiceBillingModule",(function(){return gt}));var a=n("ofXK"),o=n("tyNb"),i=n("mrSG"),r=n("B/XX"),l=n("xG9w"),c=n("3Pt+"),m=n("PSD3"),d=n.n(m),s=n("wd/R"),g=n("fXoL"),p=n("0IaG"),u=n("FOcL"),b=n("LcQX"),f=n("dNgK"),C=n("t+tn"),h=n("T4Li"),v=n("JqCM"),_=n("bTqV"),P=n("NFeN"),M=n("Qu3c"),O=n("H44p"),x=n("xHqg"),y=n("wZkO"),k=n("mS9j"),w=n("XNiG"),S=n("1G5W"),I=n("NJ67"),E=n("F97M"),D=n("kmnG"),F=n("qFsG"),z=n("iadO"),N=n("8SgF"),T=n("QUrN");function A(t,e){if(1&t){const t=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div"),g["\u0275\u0275elementStart"](1,"mat-form-field",5),g["\u0275\u0275elementStart"](2,"input",6),g["\u0275\u0275listener"]("blur",(function(e){return g["\u0275\u0275restoreView"](t),g["\u0275\u0275nextContext"](2).onBlur(e)}))("keyup",(function(e){return g["\u0275\u0275restoreView"](t),g["\u0275\u0275nextContext"](2).looseFocus(e)})),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}if(2&t){const t=g["\u0275\u0275nextContext"](2);g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("type",t.inputType)("placeholder",t.label)("required",t.required)("formControl",t.fieldCtrl)("disabled",t.disabled)}}function R(t,e){if(1&t){const t=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div"),g["\u0275\u0275elementStart"](1,"mat-form-field",5),g["\u0275\u0275elementStart"](2,"textarea",7),g["\u0275\u0275listener"]("blur",(function(e){return g["\u0275\u0275restoreView"](t),g["\u0275\u0275nextContext"](2).onBlur(e)})),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}if(2&t){const t=g["\u0275\u0275nextContext"](2);g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("type",t.inputType)("required",t.required)("formControl",t.fieldCtrl)("disabled",t.disabled)}}function U(t,e){if(1&t){const t=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div"),g["\u0275\u0275elementStart"](1,"mat-form-field",5),g["\u0275\u0275elementStart"](2,"input",8),g["\u0275\u0275listener"]("dateChange",(function(e){return g["\u0275\u0275restoreView"](t),g["\u0275\u0275nextContext"](2).onBlur(e)}))("keyup",(function(e){return g["\u0275\u0275restoreView"](t),g["\u0275\u0275nextContext"](2).looseFocus(e)})),g["\u0275\u0275elementEnd"](),g["\u0275\u0275element"](3,"mat-datepicker-toggle",9),g["\u0275\u0275element"](4,"mat-datepicker",null,10),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}if(2&t){const t=g["\u0275\u0275reference"](5),e=g["\u0275\u0275nextContext"](2);g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("matDatepicker",t)("placeholder",e.label)("required",e.required)("formControl",e.fieldCtrl)("disabled",e.disabled)("min",e.min)("max",e.max),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("for",t)}}function V(t,e){if(1&t){const t=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div"),g["\u0275\u0275elementStart"](1,"mat-form-field",11),g["\u0275\u0275elementStart"](2,"input",12),g["\u0275\u0275listener"]("dateChange",(function(e){return g["\u0275\u0275restoreView"](t),g["\u0275\u0275nextContext"](2).onBlur(e)}))("keyup",(function(e){return g["\u0275\u0275restoreView"](t),g["\u0275\u0275nextContext"](2).looseFocus(e)})),g["\u0275\u0275elementEnd"](),g["\u0275\u0275element"](3,"mat-datepicker-toggle",9),g["\u0275\u0275element"](4,"mat-datepicker",null,10),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}if(2&t){const t=g["\u0275\u0275reference"](5),e=g["\u0275\u0275nextContext"](2);g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("matDatepicker",t)("placeholder",e.label)("required",e.required)("formControl",e.fieldCtrl)("disabled",e.disabled)("min",e.min)("max",e.max),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("for",t)}}function B(t,e){if(1&t){const t=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div"),g["\u0275\u0275elementStart"](1,"app-search-user",13),g["\u0275\u0275listener"]("blur",(function(e){return g["\u0275\u0275restoreView"](t),g["\u0275\u0275nextContext"](2).onBlur(e)}))("keyup",(function(e){return g["\u0275\u0275restoreView"](t),g["\u0275\u0275nextContext"](2).looseFocus(e)}))("selectedUser",(function(e){return g["\u0275\u0275restoreView"](t),g["\u0275\u0275nextContext"](2).setOwnerName(e)})),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}if(2&t){const t=g["\u0275\u0275nextContext"](2);g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("required",t.required)("formControl",t.fieldCtrl)("disabled",t.disabled)("isAutocomplete",!0)}}function H(t,e){if(1&t&&(g["\u0275\u0275elementStart"](0,"div"),g["\u0275\u0275elementStart"](1,"div",3),g["\u0275\u0275template"](2,A,3,5,"div",4),g["\u0275\u0275template"](3,R,3,4,"div",4),g["\u0275\u0275template"](4,U,6,8,"div",4),g["\u0275\u0275template"](5,V,6,8,"div",4),g["\u0275\u0275template"](6,B,2,4,"div",4),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()),2&t){const t=g["\u0275\u0275nextContext"]();g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngSwitch",t.type),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngSwitchCase","input"),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngSwitchCase","textarea"),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngSwitchCase","date"),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngSwitchCase","textDate"),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngSwitchCase","user")}}function j(t,e){if(1&t){const t=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div",14),g["\u0275\u0275listener"]("click",(function(){g["\u0275\u0275restoreView"](t);const e=g["\u0275\u0275nextContext"]();return e.edit(e.value)}))("focus",(function(){g["\u0275\u0275restoreView"](t);const e=g["\u0275\u0275nextContext"]();return e.edit(e.value)})),g["\u0275\u0275elementEnd"]()}if(2&t){const t=g["\u0275\u0275nextContext"]();g["\u0275\u0275property"]("innerHTML",t.fieldCtrl.value?t.fieldCtrl.value+" "+t.endFormatter:t.label,g["\u0275\u0275sanitizeHtml"])}}function K(t,e){if(1&t){const t=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div",15),g["\u0275\u0275listener"]("click",(function(){g["\u0275\u0275restoreView"](t);const e=g["\u0275\u0275nextContext"]();return e.edit(e.value)}))("focus",(function(){g["\u0275\u0275restoreView"](t);const e=g["\u0275\u0275nextContext"]();return e.edit(e.value)})),g["\u0275\u0275text"](1),g["\u0275\u0275pipe"](2,"amDateFormat"),g["\u0275\u0275elementEnd"]()}if(2&t){const t=g["\u0275\u0275nextContext"]();g["\u0275\u0275advance"](1),g["\u0275\u0275textInterpolate1"](" ",g["\u0275\u0275pipeBind2"](2,1,t.fieldCtrl.value,"DD-MM-YY")||t.label,"\n")}}function q(t,e){if(1&t){const t=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div",15),g["\u0275\u0275listener"]("click",(function(){g["\u0275\u0275restoreView"](t);const e=g["\u0275\u0275nextContext"]();return e.edit(e.value)}))("focus",(function(){g["\u0275\u0275restoreView"](t);const e=g["\u0275\u0275nextContext"]();return e.edit(e.value)})),g["\u0275\u0275element"](1,"app-user-profile",16),g["\u0275\u0275elementEnd"]()}if(2&t){const t=g["\u0275\u0275nextContext"]();g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("oid",t.fieldCtrl.value)}}let L=(()=>{class t extends I.a{constructor(t){super(),this.graphApi=t,this.label="",this.type="input",this.inputType="text",this.required=!1,this.endFormatter="",this.fieldCtrl=new c.j,this.disabled=!1,this._onDestroy=new w.b,this.onChange=Function.prototype,this.onTouched=Function.prototype,this.preValue="",this.editing=!1,this.setOwnerName1=new g.EventEmitter}ngOnInit(){this.fieldCtrl.valueChanges.pipe(Object(S.a)(this._onDestroy)).subscribe(t=>{this.value=t,this.onChange(t)})}ngOnChanges(){this.fieldCtrl.valueChanges.pipe(Object(S.a)(this._onDestroy)).subscribe(t=>{this.value=t,this.onChange(t)})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}setDisabledState(t){t?(this.disabled=!0,this.fieldCtrl.disable()):(this.disabled=!1,this.fieldCtrl.enable())}writeValue(t){this.fieldCtrl.setValue(t)}onBlur(t){this.editing=!1}looseFocus(t){13==t.keyCode&&document.getElementById("inlineEditControl")&&document.getElementById("inlineEditControl").blur()}edit(t){this.disabled||(this.preValue=t,this.editing=!0,setTimeout(t=>{document.getElementById("inlineEditControl")&&document.getElementById("inlineEditControl").focus()}))}setOwnerName(t){this.setOwnerName1.emit(t.displayName)}}return t.\u0275fac=function(e){return new(e||t)(g["\u0275\u0275directiveInject"](E.a))},t.\u0275cmp=g["\u0275\u0275defineComponent"]({type:t,selectors:[["inline-form-field"]],inputs:{label:"label",type:"type",inputType:"inputType",required:"required",endFormatter:"endFormatter",disabled:"disabled",min:"min",max:"max"},outputs:{setOwnerName1:"setOwnerName1"},features:[g["\u0275\u0275ProvidersFeature"]([{provide:c.t,useExisting:Object(g.forwardRef)(()=>t),multi:!0}]),g["\u0275\u0275InheritDefinitionFeature"],g["\u0275\u0275NgOnChangesFeature"]],decls:4,vars:4,consts:[[4,"ngIf"],["class","value-field",3,"innerHTML","click","focus",4,"ngIf"],["class","value-field",3,"click","focus",4,"ngIf"],[3,"ngSwitch"],[4,"ngSwitchCase"],["appearance","outline",1,"w-100"],["matInput","","id","inlineEditControl",3,"type","placeholder","required","formControl","disabled","blur","keyup"],["matInput","","id","inlineEditControl",2,"height","130px !important",3,"type","required","formControl","disabled","blur"],["matInput","","id","inlineEditControl",3,"matDatepicker","placeholder","required","formControl","disabled","min","max","dateChange","keyup"],["matSuffix","",3,"for"],["picker",""],["appearance","fill",1,"w-100"],["matInput","","id","inlineEditControl",1,"w-80","textDateClass",3,"matDatepicker","placeholder","required","formControl","disabled","min","max","dateChange","keyup"],[3,"required","formControl","disabled","isAutocomplete","blur","keyup","selectedUser"],[1,"value-field",3,"innerHTML","click","focus"],[1,"value-field",3,"click","focus"],["type","small",3,"oid"]],template:function(t,e){1&t&&(g["\u0275\u0275template"](0,H,7,6,"div",0),g["\u0275\u0275template"](1,j,1,1,"div",1),g["\u0275\u0275template"](2,K,3,4,"div",2),g["\u0275\u0275template"](3,q,2,1,"div",2)),2&t&&(g["\u0275\u0275property"]("ngIf",e.editing),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",!e.editing&&"date"!=e.type&&"user"!=e.type),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",!e.editing&&"date"==e.type),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",!e.editing&&"user"==e.type))},directives:[a.NgIf,a.NgSwitch,a.NgSwitchCase,D.c,F.b,c.e,c.F,c.v,c.k,z.g,z.i,D.i,z.f,N.a,k.a],pipes:[T.a],styles:["input[_ngcontent-%COMP%]{font-size:14px!important;padding:3px!important}.value-field[_ngcontent-%COMP%]{padding:4px!important}.value-field[_ngcontent-%COMP%]:hover{outline:1px solid #a5a4a2!important}.textDateClass[_ngcontent-%COMP%]{background:transparent!important}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding:0!important;margin:0!important}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper   .mat-form-field-flex{padding:0!important}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper   .mat-form-field-infix{padding:6px!important;margin:auto!important;border-top-width:0!important}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper   .mat-form-field-suffix{top:0}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper   .mat-form-field-outline{top:0!important;line-height:1.7!important}"]}),t})();var Y=n("Wp6s"),G=n("7EHt"),$=n("3vwd");const Q=["stepper"];function X(t,e){1&t&&g["\u0275\u0275element"](0,"mat-spinner",57)}function J(t,e){}function W(t,e){if(1&t){const t=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"mat-step",58),g["\u0275\u0275elementStart"](1,"div",59),g["\u0275\u0275template"](2,J,0,0,"ng-template",60),g["\u0275\u0275elementStart"](3,"div",61),g["\u0275\u0275elementStart"](4,"div",62),g["\u0275\u0275text"](5),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](6,"div",63),g["\u0275\u0275elementStart"](7,"button",64),g["\u0275\u0275listener"]("click",(function(){g["\u0275\u0275restoreView"](t);const n=e.index;return g["\u0275\u0275nextContext"]().saveActivity(n)})),g["\u0275\u0275elementStart"](8,"mat-icon"),g["\u0275\u0275text"](9,"save"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](10,"button",65),g["\u0275\u0275listener"]("click",(function(){g["\u0275\u0275restoreView"](t);const n=e.index;return g["\u0275\u0275nextContext"]().completeActivity(n)})),g["\u0275\u0275elementStart"](11,"mat-icon"),g["\u0275\u0275text"](12,"done_all"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](13,"div",15),g["\u0275\u0275elementStart"](14,"div",66),g["\u0275\u0275text"](15," Customer Responsible Person "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](16,"div",67),g["\u0275\u0275element"](17,"inline-form-field",68),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](18,"div",66),g["\u0275\u0275text"](19," Responsible Person "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](20,"div",67),g["\u0275\u0275element"](21,"inline-form-field",69),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](22,"div",70),g["\u0275\u0275elementStart"](23,"div",71),g["\u0275\u0275text"](24," Customer Person's Role "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](25,"div",67),g["\u0275\u0275element"](26,"inline-form-field",72),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](27,"div",71),g["\u0275\u0275text"](28," Start Date: "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](29,"div",67),g["\u0275\u0275element"](30,"inline-form-field",73),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](31,"div",74),g["\u0275\u0275elementStart"](32,"div",66),g["\u0275\u0275text"](33," Customer Email "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](34,"div",67),g["\u0275\u0275element"](35,"inline-form-field",75),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](36,"div",71),g["\u0275\u0275text"](37," Planned End Date: "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](38,"div",67),g["\u0275\u0275element"](39,"inline-form-field",76),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](40,"div",74),g["\u0275\u0275elementStart"](41,"div",71),g["\u0275\u0275text"](42," Number Of Days : "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](43,"div",77),g["\u0275\u0275element"](44,"inline-form-field",78),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](45,"div",71),g["\u0275\u0275text"](46," Completion Date: "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](47,"div",67),g["\u0275\u0275element"](48,"inline-form-field",79),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](49,"div",80),g["\u0275\u0275element"](50,"div",24),g["\u0275\u0275elementStart"](51,"div",81),g["\u0275\u0275elementStart"](52,"button",82),g["\u0275\u0275listener"]("click",(function(){return g["\u0275\u0275restoreView"](t),g["\u0275\u0275nextContext"](),g["\u0275\u0275reference"](93).previous()})),g["\u0275\u0275elementStart"](53,"mat-icon"),g["\u0275\u0275text"](54,"chevron_left"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](55,"button",82),g["\u0275\u0275listener"]("click",(function(){return g["\u0275\u0275restoreView"](t),g["\u0275\u0275nextContext"](),g["\u0275\u0275reference"](93).next()})),g["\u0275\u0275elementStart"](56,"mat-icon"),g["\u0275\u0275text"](57,"chevron_right"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}if(2&t){const t=e.index,n=g["\u0275\u0275nextContext"]();g["\u0275\u0275property"]("completed",1==n.activityCompletion[t]),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("formGroupName",t),g["\u0275\u0275advance"](4),g["\u0275\u0275textInterpolate1"](" ",n.activityForm.value.activities[t].activityName," "),g["\u0275\u0275advance"](5),g["\u0275\u0275property"]("disabled",1==n.activityCompletion[t])}}function Z(t,e){if(1&t&&(g["\u0275\u0275elementStart"](0,"tr"),g["\u0275\u0275elementStart"](1,"td"),g["\u0275\u0275text"](2),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](3,"td"),g["\u0275\u0275element"](4,"app-currency",19),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](5,"td"),g["\u0275\u0275text"](6),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit,n=g["\u0275\u0275nextContext"](3);g["\u0275\u0275advance"](2),g["\u0275\u0275textInterpolate"](t.tally_no),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("currencyList",t.amount_received),g["\u0275\u0275advance"](2),g["\u0275\u0275textInterpolate1"](" ",n.getDate(n.utilityService.convertToLocalTime(t.received_date))," ")}}function tt(t,e){if(1&t&&(g["\u0275\u0275elementStart"](0,"mat-accordion"),g["\u0275\u0275elementStart"](1,"mat-expansion-panel"),g["\u0275\u0275elementStart"](2,"mat-expansion-panel-header"),g["\u0275\u0275elementStart"](3,"mat-panel-title",91),g["\u0275\u0275text"](4," Payment History "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](5,"mat-panel-description",91),g["\u0275\u0275text"](6," Click to view previous payment records "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](7,"table",92),g["\u0275\u0275elementStart"](8,"thead"),g["\u0275\u0275elementStart"](9,"tr"),g["\u0275\u0275elementStart"](10,"th"),g["\u0275\u0275text"](11,"Reference Number"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](12,"th"),g["\u0275\u0275text"](13,"Amount Received"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](14,"th"),g["\u0275\u0275text"](15,"Received Date"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](16,"tbody"),g["\u0275\u0275template"](17,Z,7,3,"tr",93),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()),2&t){const t=g["\u0275\u0275nextContext"](2);g["\u0275\u0275advance"](17),g["\u0275\u0275property"]("ngForOf",t.paymentInfo)}}function et(t,e){1&t&&(g["\u0275\u0275elementStart"](0,"mat-error",104),g["\u0275\u0275text"](1," Amount Received should not be '-ve' "),g["\u0275\u0275elementEnd"]())}function nt(t,e){if(1&t&&(g["\u0275\u0275elementStart"](0,"div",94),g["\u0275\u0275elementStart"](1,"div",95),g["\u0275\u0275elementStart"](2,"mat-form-field",96),g["\u0275\u0275elementStart"](3,"mat-label"),g["\u0275\u0275text"](4,"Reference Number"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275element"](5,"input",97),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](6,"div",95),g["\u0275\u0275elementStart"](7,"mat-form-field",96),g["\u0275\u0275elementStart"](8,"mat-label"),g["\u0275\u0275text"](9,"Amount Received"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275element"](10,"input",98),g["\u0275\u0275template"](11,et,2,0,"mat-error",99),g["\u0275\u0275elementStart"](12,"span",100),g["\u0275\u0275text"](13),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](14,"div",95),g["\u0275\u0275elementStart"](15,"mat-form-field",96),g["\u0275\u0275elementStart"](16,"mat-label"),g["\u0275\u0275text"](17,"Received Date"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275element"](18,"input",101),g["\u0275\u0275element"](19,"mat-datepicker-toggle",102),g["\u0275\u0275element"](20,"mat-datepicker",null,103),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()),2&t){const t=g["\u0275\u0275reference"](21),e=g["\u0275\u0275nextContext"](2);g["\u0275\u0275advance"](11),g["\u0275\u0275property"]("ngIf",e.activityForm.controls.amountReceived.hasError("min")),g["\u0275\u0275advance"](2),g["\u0275\u0275textInterpolate1"](" ",e.activityForm.value.invoiceRaisedCurrency," "),g["\u0275\u0275advance"](5),g["\u0275\u0275property"]("matDatepicker",t)("max",e.today),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("for",t)}}function at(t,e){if(1&t){const t=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div",74),g["\u0275\u0275elementStart"](1,"button",105),g["\u0275\u0275listener"]("click",(function(){return g["\u0275\u0275restoreView"](t),g["\u0275\u0275nextContext"](2).submitPayment()})),g["\u0275\u0275text"](2," Submit "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}if(2&t){const t=g["\u0275\u0275nextContext"](2);g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("disabled",""==t.activityForm.value.tallyRef||""==t.activityForm.value.amountReceived||""==t.activityForm.value.receivedDate||1==t.submitButtonDisabled)}}function ot(t,e){if(1&t&&(g["\u0275\u0275elementStart"](0,"mat-step",83),g["\u0275\u0275elementStart"](1,"div",84),g["\u0275\u0275elementStart"](2,"mat-card",85),g["\u0275\u0275template"](3,tt,18,1,"mat-accordion",86),g["\u0275\u0275elementStart"](4,"div"),g["\u0275\u0275template"](5,nt,22,5,"div",87),g["\u0275\u0275elementStart"](6,"div",88),g["\u0275\u0275elementStart"](7,"span",89),g["\u0275\u0275text"](8),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275template"](9,at,3,1,"div",90),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()),2&t){const t=g["\u0275\u0275nextContext"]();g["\u0275\u0275property"]("completed",0==t.activityForm.value.amountToBeCollected),g["\u0275\u0275advance"](3),g["\u0275\u0275property"]("ngIf",0!=t.paymentInfo.length),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("ngIf",0!=t.activityForm.value.amountToBeCollected),g["\u0275\u0275advance"](3),g["\u0275\u0275textInterpolate2"]("Amount To be Collected: ",t.activityForm.value.amountToBeCollected," ",t.activityForm.value.invoiceRaisedCurrency,""),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",0!=t.activityForm.value.amountToBeCollected)}}function it(t,e){1&t&&(g["\u0275\u0275elementStart"](0,"div",106),g["\u0275\u0275elementStart"](1,"span",89),g["\u0275\u0275text"](2," *** No pdf available *** "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]())}function rt(t,e){if(1&t){const t=g["\u0275\u0275getCurrentView"]();g["\u0275\u0275elementStart"](0,"div",107),g["\u0275\u0275elementStart"](1,"attachment-upload-btn",108),g["\u0275\u0275listener"]("change",(function(e){return g["\u0275\u0275restoreView"](t),g["\u0275\u0275nextContext"]().changeInAttachment(e)})),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]()}if(2&t){const t=g["\u0275\u0275nextContext"]();g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("destinationBucket","kebs-invoices")("routingKey","invoices")("contextId",t.contextId)("allowEdit",!0)}}const lt=[{path:"",component:(()=>{class t{constructor(t,e,a,o,r,m,d,s,g,p,u){this.dialog=t,this.cdr=e,this.fb=a,this.route=o,this.invoiceService=r,this.router=m,this.utilityService=d,this._snackBar=s,this.tenantService=g,this.invoiceCommonService=p,this.spinnerService=u,this.num_activities=0,this.initialSelectedIndex=0,this.invoicePDfDataFinal=[],this.stepNo=1,this.lastStepperVisible=!1,this.activityCompletion=[],this.submitButtonDisabled=!1,this.submitSpinnerVisible=!1,this.showAttachment=!1,this.contextId=null,this.isCollectionShow=!0,this.openAnnexure=()=>{this.invoiceService.viewInvoice(this.billingId).subscribe(t=>{let e=[];if(e.push(t),e&&e.length>0){let e=t;this.invoiceService.viewAnnexure(this.billingId,this.serviceTypeId).subscribe(t=>Object(i.c)(this,void 0,void 0,(function*(){if(null!=t){console.log(t);let a=[];a.push({serviceTypeId:this.serviceTypeId,data:t,invoiceNo:l.pluck(e,"invoiceNo").reverse()});const{ViewAnnexureComponent:o}=yield Promise.all([n.e(53),n.e(775)]).then(n.bind(null,"qnkw"));this.dialog.open(o,{width:"65%",height:"100%",autoFocus:!1,maxWidth:"90vw",data:a})}else this.utilityService.showMessage("No Data found for annexure","Dismiss")})))}else this.utilityService.showMessage("No Data found","Dismiss")},t=>{console.error(t)})},this.getInvoiceTenantRoleCheckDetail=(t,e)=>new Promise((n,a)=>{this.invoiceCommonService.getInvoiceTenantCheckDetail(t,e).subscribe(t=>{n(t)},t=>{console.error(t),a(t)})}),this.activityForm=this.fb.group({tallyRef:[""],amountToBeCollected:[[""],c.H.required],amountReceived:["",[c.H.required,c.H.min(1)]],receivedDate:["",c.H.required],invoiceRaisedCurrency:["",c.H.required],activities:this.fb.array([])})}viewInvoice(){console.log(this.paymentTermsList),this.invoiceService.viewInvoice(this.billingId).subscribe(t=>Object(i.c)(this,void 0,void 0,(function*(){let e=[];if(e.push(t),e&&e.length>0){console.log(t);const{ViewInvoiceComponent:e}=yield Promise.all([n.e(53),n.e(114),n.e(176),n.e(175)]).then(n.bind(null,"CPl1"));this.dialog.open(e,{width:"65%",height:"100%",autoFocus:!1,maxWidth:"90vw",data:{pdfData:t,billingId:this.billingId,invoiceTenantDetails:this.invoiceTenantDetails,paymentTermsList:this.paymentTermsList}})}else this.utilityService.showMessage("No Data found","Dismiss")})),t=>{console.error(t)})}submitPayment(){this.submitButtonDisabled=!0,this.submitSpinnerVisible=!0;let t=this.invoiceInfo.amount_to_be_Collected-this.activityForm.value.amountReceived,e=this.utilityService.convertToLocalTime(this.activityForm.value.receivedDate);this.invoiceService.savepaymentInfo(this.activityForm.value.amountReceived,e,this.billingId,this.activityForm.value.tallyRef,this.milestoneId,this.itemId,t).subscribe(t=>{"S"==t.messType&&this.invoiceService.getInvoiceInformationInBilledScreen(this.billingId).subscribe(t=>{console.log(t),this.submitButtonDisabled=!1,this.invoiceInfo=t,this.activityForm.patchValue({tallyRef:"",amountReceived:"",receivedDate:"",amountToBeCollected:this.invoiceInfo.amount_to_be_Collected}),0==this.activityForm.value.amountToBeCollected?(d.a.fire({icon:"success",title:"Payment for this milestone recorded succesfully !",showConfirmButton:!0}),this.router.navigateByUrl("/main/invoice/invoicelist/1")):(d.a.fire({icon:"success",title:"Partial payment recorded succesfully !",showConfirmButton:!0}),this.router.navigateByUrl("/main/invoice/invoicelist/1"),this.submitSpinnerVisible=!1,this.getPaymentAndActivityRelatedInfo())},t=>{console.error(t)})},t=>{this._snackBar.open(t.error.messText,"Dismiss",{duration:2e3}),this.submitButtonDisabled=!1,console.error(t)})}savePlannedCollectionAmount(t){this.plannedCollectionAmount=t,console.log(this.projectId+"**"+this.currencyId+"**"+this.plannedCollectionAmount+"***"+this.invoiceRaisedDate+"***"+this.billingId),this.invoiceService.updatePlannedCollection(this.projectId,this.currencyId,this.plannedCollectionAmount,this.invoiceRaisedDate,this.billingId).subscribe(t=>{console.log(t),"Success"==t&&this._snackBar.open("updated successfully!","close",{duration:2e3})})}get ActivityArray(){return this.activityForm.get("activities")}getDate(t){return s(t).format("DD-MM-YY")}completeActivity(t){this.invoiceService.completePaymentActivity(this.activityForm.value.activities[t].activityID).subscribe(e=>{console.log(e),"Payment Activity completed"==e.message?(d.a.fire({customClass:{title:"title-class",confirmButton:"confirm-button-class"},type:"success",title:e.message}),this.activityCompletion[t]=1,this.stepper.next()):d.a.fire({customClass:{title:"title-class",confirmButton:"confirm-button-class"},type:"error",title:"Oops...",text:"Activity Not Completed"})},t=>{console.error(t)})}setActivityForm(t,e){this.activityForm.patchValue({amountToBeCollected:e.amount_to_be_Collected,invoiceRaisedCurrency:e.currency});let n=this.activityForm.controls.activities;console.log(t),t.forEach((t,e)=>{n.push(this.fb.group({activityName:t.activity_name,customerRespPerson:t.customer_responsible_person,customerPersonsRole:t.customer_person_role,customerEmail:t.customer_person_email,numberOfDays:t.no_of_days,KaarResponsiblePerson:t.assigned_to_id,startDate:null!=t.start_date?this.utilityService.convertToLocalTime(t.start_date):null,plannedEndDate:null!=t.due_date?this.utilityService.convertToLocalTime(t.due_date):null,completionDate:null!=t.end_date?this.utilityService.convertToLocalTime(t.end_date):null,activityID:t.payment_activity_id})),console.log(t),console.log("&*&*&*"),console.log(this.utilityService.convertToLocalTime(t.start_date)),this.activityCompletion.push(t.is_completed)}),console.log(t),console.log(this.activityCompletion)}saveActivity(t){console.log(this.activityForm.value.activities[t]),this.invoiceService.saveActivityDetails(this.activityForm.value.activities[t],this.milestoneId).subscribe(t=>{console.log(t),d.a.fire("Activity Details Saved!"==t.message?{customClass:{title:"title-class",confirmButton:"confirm-button-class"},type:"success",title:t.message}:{customClass:{title:"title-class",confirmButton:"confirm-button-class"},type:"error",title:"Oops...",text:"Activity not Saved"})},t=>{console.error(t)})}ngOnInit(){this.spinnerService.show(),this.getPaymentTerms(),this.itemId=this.route.snapshot.params.itemId,this.milestoneId=this.route.snapshot.params.milestoneId,this.billingId=this.route.snapshot.params.billingId,this.projectId=this.route.snapshot.params.projectId,this.fromScreen=this.route.snapshot.params.itemType,this.today=new Date,this.projectName=decodeURIComponent(this.route.snapshot.params.projectName),console.log(this.projectName),this.getInvoiceInfoDisplayedInleftSide(),this.getPaymentAndActivityRelatedInfo(),this.getTenantInfo()}getinvoiceNo(t){let e=[];t.forEach((n,a)=>{null!=n&&e.push(n),t.length-1==a&&(this.invoiceNoArray=e)})}updatePlannedCollection(t){console.log(t)}getPaymentAndActivityRelatedInfo(){this.invoiceService.getPaymentInformation(this.billingId).subscribe(t=>{this.paymentInfo=t,this.calculateAmountToBeCollected(this.paymentInfo),console.log(t)},t=>{console.error(t)})}launchProjectFromInvoice(t,e){console.log(window.location.origin);let n=window.location.origin+"/main/project/"+t+"/"+this.encodeURIComponent(e);window.open(n)}encodeURIComponent(t){return encodeURIComponent(t).replace(/[!'()*]/g,(function(t){return"%"+t.charCodeAt(0).toString(16)}))}checkIfStepComplete(t){for(let e=0;e<t.length;e++)if("1"!=t[e].is_completed){setTimeout(()=>{this.initialSelectedIndex=e,console.log(this.initialSelectedIndex)},0);break}}getInvoiceInfoDisplayedInleftSide(){this.invoiceService.getInvoiceInformationInBilledScreen(this.billingId).subscribe(t=>{console.log(t),this.invoiceInfo=t,this.serviceTypeId=this.invoiceInfo.service_type_id,this.currencyId=this.invoiceInfo.currencyId,this.invoiceRaisedDate=this.invoiceInfo.invoice_raised_on,this.contextId=null!=this.invoiceInfo.context_id?this.invoiceInfo.context_id:null,console.log(this.serviceTypeId),this.invoiceService.getPaymentActivityListForMilestone(this.milestoneId).subscribe(t=>{this.spinnerService.hide(),console.log(t),this.activities=t,this.setActivityForm(t,this.invoiceInfo),this.checkIfStepComplete(this.activities),this.lastStepperVisible=!0,this.showAttachment=!0},t=>{console.log(t),this.spinnerService.hide()})},t=>{console.error(t),this.spinnerService.hide()})}ngAfterViewInit(){this.cdr.detectChanges()}calculateAmountToBeCollected(t){console.log(t)}ngOnDestroy(){}back(){"billed"==this.fromScreen?this.router.navigateByUrl("/main/invoice/invoicelist/1"):"PP"==this.fromScreen?this.router.navigateByUrl("/main/invoice/invoicelist/2"):"PR"==this.fromScreen&&this.router.navigateByUrl("/main/invoice/invoicelist/3")}getTenantInfo(){return Object(i.c)(this,void 0,void 0,(function*(){this.tenantService.getTenantInfo().then(t=>Object(i.c)(this,void 0,void 0,(function*(){let e=t;this.invoiceTenantDetails=yield this.getInvoiceTenantRoleCheckDetail(e.tenant_name,"Role"),this.isCollectionShow=this.invoiceTenantDetails.data.is_collection_show_in_kebs})),t=>{console.log("cbfRoleCheck func error"),console.log(t)})}))}getPaymentTerms(){this.invoiceCommonService.getPaymentTerms().then(t=>{for(let e of t)e.id=e.name;this.paymentTermsList=t},t=>{console.log(t)})}changeInAttachment(t){this.contextId=null,this.contextId=t,this.contextId&&this.invoiceService.updateInvoiceAttachments(this.contextId,this.billingId).subscribe(t=>{console.log(t)},t=>{console.log(t)})}}return t.\u0275fac=function(e){return new(e||t)(g["\u0275\u0275directiveInject"](p.b),g["\u0275\u0275directiveInject"](g.ChangeDetectorRef),g["\u0275\u0275directiveInject"](c.i),g["\u0275\u0275directiveInject"](o.a),g["\u0275\u0275directiveInject"](u.a),g["\u0275\u0275directiveInject"](o.g),g["\u0275\u0275directiveInject"](b.a),g["\u0275\u0275directiveInject"](f.a),g["\u0275\u0275directiveInject"](C.a),g["\u0275\u0275directiveInject"](h.a),g["\u0275\u0275directiveInject"](v.c))},t.\u0275cmp=g["\u0275\u0275defineComponent"]({type:t,selectors:[["app-invoice-billing-page"]],viewQuery:function(t,e){if(1&t&&g["\u0275\u0275viewQuery"](Q,!0),2&t){let t;g["\u0275\u0275queryRefresh"](t=g["\u0275\u0275loadQuery"]())&&(e.stepper=t.first)}},features:[g["\u0275\u0275ProvidersFeature"]([{provide:r.h,useValue:{displayDefaultIndicatorType:!1}}])],decls:131,vars:22,consts:[[3,"formGroup"],[1,"container-fluid","invoice-billed-component"],[1,"row","pl-0","pr-2","pt-2","pb-2"],[1,"col-1x-sm","pr-0"],["mat-icon-button","",1,"icon-tray-button",3,"click"],[1,"smallCardIcon"],[1,"col-11x","pl-0","pr-0"],[1,"card","mw-100",2,"overflow","hidden"],[1,"card-body","p-3"],[1,"row"],[1,"col-12","col-md-12","col-lg-3","col-sm-12","pr-0","pl-1",2,"border-right","rgba(180,180,180,0.8) 1px solid"],[1,"pb-1"],[1,"heading","pr-3"],["mat-icon-button","","matTooltip","Go to project",1,"go-to-project-btn",3,"click"],[1,"go-to-prj-icon"],[1,"row","pt-1"],[1,"col-6","col-lg-4","col-md-4","pt-1","side-heading"],[1,"col-6","col-lg-7","col-md-5","content"],[3,"matTooltip"],["type","small",1,"",3,"currencyList"],[1,"col-6","col-lg-7","col-md-5","content",3,"matTooltip"],[2,"padding-left","3px"],[1,"col-12","col-md-12","col-lg-9","col-sm-12","pl-3","pr-2"],[1,"row","pb-1"],[1,"col-10"],[1,"heading","pb-1","pt-1"],[1,"col-2",2,"padding-left","7rem","padding-right","0"],["diameter","25",4,"ngIf"],[1,"col-12","pl-1","pr-1","pb-1"],[1,"row","activityDetails","row-border"],[3,"selectedIndex"],["stepper",""],["formArrayName","activities",3,"completed",4,"ngFor","ngForOf"],[3,"completed",4,"ngIf"],[1,"row","h-100","pl-1","attachment-tab"],[1,"col-12","col-lg-8","col-md-8","w-100"],[1,"tab-height"],["label","Attachments"],[1,"row","pb-2","pt-2",2,"border-bottom","#b3b3b3 1px solid","margin-left","1px !important"],[1,"col","pl-4","txt-heading"],[1,"pt-1","row","pb-1","select-row",2,"border-bottom","#b3b3b3 1px solid","margin-left","12px !important",3,"click"],[1,"col-1","pr-0"],["src","../../../../../assets/images/PDF_logo.png","height","15px","width","15px"],[1,"col-4","my-auto","file-name","single-line","pl-0"],[1,"col-4","my-auto","file-name","pl-2"],[1,"col-3","file-name","my-auto","single-line","pl-2"],["noData",""],[1,"col-12","col-lg-4","col-md-12","col-sm-12","h-100","pt-2","pl-2"],[1,"card","card-up"],[1,"card-body"],[1,"col-12","content"],["type","user-badge","role","Invoice raised by",3,"oid"],[1,"row","pt-3"],[1,"col-7"],["class","col-1 mt-3 p-0 pr-5 d-flex justify-content-end","class","upload-attachment",4,"ngIf"],[1,"col"],["size","medium","type","ball-clip-rotate","bdColor","rgba(236, 233, 233, 0.8)","color","#cf0001"],["diameter","25"],["formArrayName","activities",3,"completed"],[3,"formGroupName"],["matStepLabel",""],[1,"row","pb-0","pt-1"],[1,"col-7","side-heading-stepper","pl-0"],[1,"col-5","d-flex","pr-0"],["color","primary","mat-icon-button","",1,"ml-auto","mr-2",3,"click"],["mat-icon-button","",1,"",3,"disabled","click"],[1,"col-lg-3","category","pt-2"],[1,"col-3","content","col-lg-3","pt-1"],["type","input","label","Enter Name","formControlName","customerRespPerson"],["type","user","label","Responsible Person","formControlName","KaarResponsiblePerson"],[1,"row","pt-0"],[1,"col-lg-3","pt-2","category"],["type","input","label","Enter Role","formControlName","customerPersonsRole"],["type","date","label","Start Date","formControlName","startDate"],[1,"row","pt-2"],["type","input","label","Enter Customer Email","formControlName","customerEmail"],["type","date","label","Planned End Date","formControlName","plannedEndDate"],[1,"col-3","content","pt-1","col-lg-3"],["type","input","label","No Of Days","formControlName","numberOfDays"],["type","date","label","Completion Date","formControlName","completionDate"],[1,"d-flex"],[1,"col-2"],["mat-icon-button","",1,"ml-auto",3,"click"],[3,"completed"],[1,"row","mt-1","justify-content-center"],[1,"paymentDoneStep","mb-1"],[4,"ngIf"],["class","row mt-1  pt-2 justify-content-center",4,"ngIf"],[1,"row","d-flex","pt-2"],[1,"mx-auto"],["class","row pt-2 ",4,"ngIf"],[2,"font-size","14px !important"],[1,"table","table-hover"],[4,"ngFor","ngForOf"],[1,"row","mt-1","pt-2","justify-content-center"],[1,"col-lg-4"],["appearance","outline"],["matInput","","type","text","placeholder","Reference Number","formControlName","tallyRef","required",""],["matInput","","type","number","placeholder","Amount Received","formControlName","amountReceived","min","1","required",""],["class","pt-2",4,"ngIf"],["matSuffix",""],["matInput","","name","endDate","required","","placeholder","Received Date","formControlName","receivedDate","readonly","",3,"matDatepicker","max"],["matSuffix","",3,"for"],["picker4",""],[1,"pt-2"],["mat-raised-button","",1,"mx-auto","submit",3,"disabled","click"],[1,"pt-1","row","pb-1","select-row","d-flex",2,"border-bottom","#b3b3b3 1px solid","margin-left","12px !important","font-size","12px","color","#b3b3b3"],[1,"upload-attachment"],[3,"destinationBucket","routingKey","contextId","allowEdit","change"]],template:function(t,e){1&t&&(g["\u0275\u0275elementStart"](0,"form",0),g["\u0275\u0275elementStart"](1,"div",1),g["\u0275\u0275elementStart"](2,"div",2),g["\u0275\u0275elementStart"](3,"div",3),g["\u0275\u0275elementStart"](4,"button",4),g["\u0275\u0275listener"]("click",(function(){return e.back()})),g["\u0275\u0275elementStart"](5,"mat-icon",5),g["\u0275\u0275text"](6," chevron_left "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](7,"div",6),g["\u0275\u0275elementStart"](8,"div",7),g["\u0275\u0275elementStart"](9,"div",8),g["\u0275\u0275elementStart"](10,"div",9),g["\u0275\u0275elementStart"](11,"div",10),g["\u0275\u0275elementStart"](12,"div",11),g["\u0275\u0275elementStart"](13,"span",12),g["\u0275\u0275text"](14,"Invoice Information"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](15,"button",13),g["\u0275\u0275listener"]("click",(function(){return e.launchProjectFromInvoice(e.projectId,e.projectName)})),g["\u0275\u0275elementStart"](16,"mat-icon",14),g["\u0275\u0275text"](17,"launch"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](18,"div",15),g["\u0275\u0275elementStart"](19,"div",16),g["\u0275\u0275text"](20," Invoice # "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](21,"div",17),g["\u0275\u0275text"](22),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](23,"div",15),g["\u0275\u0275elementStart"](24,"div",16),g["\u0275\u0275text"](25," Cost center "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](26,"div",17),g["\u0275\u0275text"](27),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](28,"div",15),g["\u0275\u0275elementStart"](29,"div",16),g["\u0275\u0275text"](30," Milestone name "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](31,"div",17),g["\u0275\u0275elementStart"](32,"span",18),g["\u0275\u0275text"](33),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](34,"div",15),g["\u0275\u0275elementStart"](35,"div",16),g["\u0275\u0275text"](36," Service Type "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](37,"div",17),g["\u0275\u0275text"](38),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](39,"div",15),g["\u0275\u0275elementStart"](40,"div",16),g["\u0275\u0275text"](41," Invoice value "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](42,"div",17),g["\u0275\u0275element"](43,"app-currency",19),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](44,"div",15),g["\u0275\u0275elementStart"](45,"div",16),g["\u0275\u0275text"](46," TDS Value "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](47,"div",17),g["\u0275\u0275element"](48,"app-currency",19),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](49,"div",15),g["\u0275\u0275elementStart"](50,"div",16),g["\u0275\u0275text"](51," Raised on "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](52,"div",17),g["\u0275\u0275text"](53),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](54,"div",15),g["\u0275\u0275elementStart"](55,"div",16),g["\u0275\u0275text"](56," Actual Due "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](57,"div",17),g["\u0275\u0275text"](58),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](59,"div",15),g["\u0275\u0275elementStart"](60,"div",16),g["\u0275\u0275text"](61," Expected on "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](62,"div",17),g["\u0275\u0275text"](63),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](64,"div",15),g["\u0275\u0275elementStart"](65,"div",16),g["\u0275\u0275text"](66," Legal entity "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](67,"div",17),g["\u0275\u0275text"](68),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](69,"div",15),g["\u0275\u0275elementStart"](70,"div",16),g["\u0275\u0275text"](71," Customer "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](72,"div",20),g["\u0275\u0275text"](73),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](74,"div",15),g["\u0275\u0275elementStart"](75,"div",16),g["\u0275\u0275text"](76," Planned collection "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](77,"div",17),g["\u0275\u0275elementStart"](78,"span"),g["\u0275\u0275text"](79),g["\u0275\u0275elementStart"](80,"span",21),g["\u0275\u0275text"](81),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](82,"div",22),g["\u0275\u0275elementStart"](83,"div",23),g["\u0275\u0275elementStart"](84,"div",24),g["\u0275\u0275elementStart"](85,"div",25),g["\u0275\u0275text"](86," Invoice to collection tracking "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](87,"div",26),g["\u0275\u0275template"](88,X,1,0,"mat-spinner",27),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](89,"div",9),g["\u0275\u0275elementStart"](90,"div",28),g["\u0275\u0275elementStart"](91,"div",29),g["\u0275\u0275elementStart"](92,"mat-horizontal-stepper",30,31),g["\u0275\u0275template"](94,W,58,4,"mat-step",32),g["\u0275\u0275template"](95,ot,10,6,"mat-step",33),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](96,"div",34),g["\u0275\u0275elementStart"](97,"div",35),g["\u0275\u0275elementStart"](98,"mat-tab-group",36),g["\u0275\u0275elementStart"](99,"mat-tab",37),g["\u0275\u0275elementStart"](100,"div",38),g["\u0275\u0275elementStart"](101,"div",39),g["\u0275\u0275text"](102," Type "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](103,"div"),g["\u0275\u0275elementStart"](104,"div",40),g["\u0275\u0275listener"]("click",(function(){return e.viewInvoice()})),g["\u0275\u0275elementStart"](105,"div",41),g["\u0275\u0275element"](106,"img",42),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](107,"div",43),g["\u0275\u0275text"](108," Invoice PDFs "),g["\u0275\u0275elementEnd"](),g["\u0275\u0275element"](109,"div",44),g["\u0275\u0275element"](110,"div",45),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275template"](111,it,3,0,"ng-template",null,46,g["\u0275\u0275templateRefExtractor"]),g["\u0275\u0275elementStart"](113,"div"),g["\u0275\u0275elementStart"](114,"div",40),g["\u0275\u0275listener"]("click",(function(){return e.openAnnexure()})),g["\u0275\u0275elementStart"](115,"div",41),g["\u0275\u0275element"](116,"img",42),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](117,"div",43),g["\u0275\u0275elementStart"](118,"span"),g["\u0275\u0275text"](119,"Annexure PDFs"),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](120,"div",47),g["\u0275\u0275elementStart"](121,"div",48),g["\u0275\u0275elementStart"](122,"div",49),g["\u0275\u0275elementStart"](123,"div",15),g["\u0275\u0275elementStart"](124,"div",50),g["\u0275\u0275element"](125,"app-user-profile",51),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementStart"](126,"div",52),g["\u0275\u0275element"](127,"div",53),g["\u0275\u0275template"](128,rt,2,4,"div",54),g["\u0275\u0275element"](129,"div",55),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"](),g["\u0275\u0275element"](130,"ngx-spinner",56)),2&t&&(g["\u0275\u0275property"]("formGroup",e.activityForm),g["\u0275\u0275advance"](22),g["\u0275\u0275textInterpolate1"](" ",null==e.invoiceInfo?null:e.invoiceInfo.invoice_no," "),g["\u0275\u0275advance"](5),g["\u0275\u0275textInterpolate1"](" ",null==e.invoiceInfo?null:e.invoiceInfo.cost_center," "),g["\u0275\u0275advance"](5),g["\u0275\u0275property"]("matTooltip",null==e.invoiceInfo?null:e.invoiceInfo.milestone_name),g["\u0275\u0275advance"](1),g["\u0275\u0275textInterpolate1"](" ",null==e.invoiceInfo?null:e.invoiceInfo.milestone_name," "),g["\u0275\u0275advance"](5),g["\u0275\u0275textInterpolate1"](" ",null==e.invoiceInfo?null:e.invoiceInfo.service_type," "),g["\u0275\u0275advance"](5),g["\u0275\u0275property"]("currencyList",null==e.invoiceInfo?null:e.invoiceInfo.total_invoice_value),g["\u0275\u0275advance"](5),g["\u0275\u0275property"]("currencyList",null==e.invoiceInfo?null:e.invoiceInfo.tds_converted_value),g["\u0275\u0275advance"](5),g["\u0275\u0275textInterpolate1"](" ",null==e.invoiceInfo?null:e.invoiceInfo.invoice_raised_on," "),g["\u0275\u0275advance"](5),g["\u0275\u0275textInterpolate1"](" ",null==e.invoiceInfo?null:e.invoiceInfo.actual_due," "),g["\u0275\u0275advance"](5),g["\u0275\u0275textInterpolate1"](" ",null==e.invoiceInfo?null:e.invoiceInfo.expected_on," "),g["\u0275\u0275advance"](5),g["\u0275\u0275textInterpolate1"](" ",null==e.invoiceInfo?null:e.invoiceInfo.entity_name," "),g["\u0275\u0275advance"](4),g["\u0275\u0275property"]("matTooltip",null==e.invoiceInfo?null:e.invoiceInfo.customer_name),g["\u0275\u0275advance"](1),g["\u0275\u0275textInterpolate1"](" ",null==e.invoiceInfo?null:e.invoiceInfo.customer_name," "),g["\u0275\u0275advance"](6),g["\u0275\u0275textInterpolate1"]("",e.activityForm.value.amountToBeCollected," "),g["\u0275\u0275advance"](2),g["\u0275\u0275textInterpolate"](e.activityForm.value.invoiceRaisedCurrency),g["\u0275\u0275advance"](7),g["\u0275\u0275property"]("ngIf",1==e.submitSpinnerVisible),g["\u0275\u0275advance"](4),g["\u0275\u0275property"]("selectedIndex",e.initialSelectedIndex),g["\u0275\u0275advance"](2),g["\u0275\u0275property"]("ngForOf",null==e.ActivityArray?null:e.ActivityArray.controls),g["\u0275\u0275advance"](1),g["\u0275\u0275property"]("ngIf",1==e.lastStepperVisible&&e.isCollectionShow),g["\u0275\u0275advance"](30),g["\u0275\u0275property"]("oid",null==e.invoiceInfo?null:e.invoiceInfo.invoice_raised_by),g["\u0275\u0275advance"](3),g["\u0275\u0275property"]("ngIf",1==e.showAttachment))},directives:[c.J,c.w,c.n,_.a,P.a,M.a,O.a,a.NgIf,x.a,a.NgForOf,y.c,y.a,k.a,v.a,x.b,c.h,c.o,x.c,L,c.v,c.l,Y.a,G.a,G.c,G.g,G.h,G.f,D.c,D.g,F.b,c.e,c.F,c.A,D.i,z.g,z.i,z.f,D.b,$.a],styles:['.invoice-billed-component[_ngcontent-%COMP%]   .detailsCard[_ngcontent-%COMP%]{height:"80%"}.invoice-billed-component[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]{color:#cf0001;font-weight:500;font-size:15px!important}.invoice-billed-component[_ngcontent-%COMP%]   .generate-button[_ngcontent-%COMP%]{padding:0 12px!important;font-weight:400;color:#fff;background-color:#cf0001;height:30px;font-size:14px;line-height:3px}.invoice-billed-component[_ngcontent-%COMP%]   .side-heading[_ngcontent-%COMP%]{color:#868383;font-size:12px;padding-left:0}.invoice-billed-component[_ngcontent-%COMP%]   .side-heading-stepper[_ngcontent-%COMP%]{color:#cf0001;font-weight:500;font-size:12px;padding-left:0}.invoice-billed-component[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{font-size:14px;padding-top:4px;color:#1a1a1a;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.invoice-billed-component[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]{padding:.98rem}.invoice-billed-component[_ngcontent-%COMP%]   .go-to-prj-icon[_ngcontent-%COMP%]{font-size:18px;color:#847e7e}.invoice-billed-component[_ngcontent-%COMP%]   .go-to-prj-btn[_ngcontent-%COMP%]{height:35px!important;width:35px!important;line-height:38px!important}.invoice-billed-component[_ngcontent-%COMP%]   .icon-tray-button[_ngcontent-%COMP%]{width:27px!important;height:27px!important;line-height:25px!important;visibility:visible}.invoice-billed-component[_ngcontent-%COMP%]   .smallCardIcon[_ngcontent-%COMP%]{font-size:18px!important;color:#868683!important}.invoice-billed-component[_ngcontent-%COMP%]   .col-1x-sm[_ngcontent-%COMP%]{flex:0 0 2.333333%;max-width:2.333333%}.invoice-billed-component[_ngcontent-%COMP%]   .mat-stepper-horizontal[_ngcontent-%COMP%]{width:100%!important}.invoice-billed-component[_ngcontent-%COMP%]   .mat-stepper-horizontal[_ngcontent-%COMP%]     .mat-horizontal-stepper-header{padding:2px 3px 2px 20px!important}.invoice-billed-component[_ngcontent-%COMP%]   .mat-stepper-horizontal[_ngcontent-%COMP%]     .mat-horizontal-stepper-header .mat-step-label{padding:0!important}.invoice-billed-component[_ngcontent-%COMP%]   .mat-stepper-horizontal[_ngcontent-%COMP%]     .mat-horizontal-stepper-header:after, .invoice-billed-component[_ngcontent-%COMP%]   .mat-stepper-horizontal[_ngcontent-%COMP%]     .mat-horizontal-stepper-header:before{top:26px!important}.invoice-billed-component[_ngcontent-%COMP%]   .mat-stepper-horizontal[_ngcontent-%COMP%]     .mat-horizontal-content-container{overflow:hidden;padding:0 16px 16px}.invoice-billed-component[_ngcontent-%COMP%]   .mat-stepper-horizontal[_ngcontent-%COMP%]   .mat-step-icon-state-selected[_ngcontent-%COMP%], .invoice-billed-component[_ngcontent-%COMP%]   .mat-stepper-horizontal[_ngcontent-%COMP%]     .mat-step-icon-state-done{background-color:#009432;color:#fff}.invoice-billed-component[_ngcontent-%COMP%]   .mat-stepper-horizontal[_ngcontent-%COMP%]     .mat-stepper-horizontal-line{top:26px!important;min-width:66px!important}.invoice-billed-component[_ngcontent-%COMP%]   .mat-stepper-horizontal[_ngcontent-%COMP%]     .mat-step-icon-state-edit{background-color:#fb8c00;color:#fff}.invoice-billed-component[_ngcontent-%COMP%]   .mat-stepper-horizontal[_ngcontent-%COMP%]     .mat-horizontal-content-container{min-height:241px!important}.invoice-billed-component[_ngcontent-%COMP%]   mat-horizontal-stepper-header[_ngcontent-%COMP%]{height:57px}.invoice-billed-component[_ngcontent-%COMP%]   .mat-step-header[_ngcontent-%COMP%]{height:32px!important;padding-top:0!important}.invoice-billed-component[_ngcontent-%COMP%]   .mat-step-header[_ngcontent-%COMP%]   .mat-step-label[_ngcontent-%COMP%]{font-weight:400;color:#a4a4a4}.invoice-billed-component[_ngcontent-%COMP%]   .mat-step-header[_ngcontent-%COMP%]   .mat-step-label-selected[_ngcontent-%COMP%]{font-weight:500;font-size:16px!important}.invoice-billed-component[_ngcontent-%COMP%]   .paymentDoneStep[_ngcontent-%COMP%]{font-size:13px!important}.invoice-billed-component[_ngcontent-%COMP%]   .paymentDoneStep[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]{width:100%!important}.invoice-billed-component[_ngcontent-%COMP%]   .data-label-partners[_ngcontent-%COMP%]{font-size:13px!important;font-weight:420!important;color:#1c1a1a!important;margin-bottom:3px!important}.invoice-billed-component[_ngcontent-%COMP%]   .payment-col[_ngcontent-%COMP%]{min-width:58.33%!important}.invoice-billed-component[_ngcontent-%COMP%]   .category[_ngcontent-%COMP%]{font-weight:400!important;color:hsla(0,0%,42.7%,.9019607843137255);font-size:12px}.invoice-billed-component[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{font-size:13px;font-weight:400;color:#161515}.invoice-billed-component[_ngcontent-%COMP%]   .content-side[_ngcontent-%COMP%]{font-size:12px;font-weight:400;color:#161515}.invoice-billed-component[_ngcontent-%COMP%]   .content-side[_ngcontent-%COMP%], .invoice-billed-component[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%], .invoice-billed-component[_ngcontent-%COMP%]   .overflow-control[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.invoice-billed-component[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]{font-size:13px;font-weight:400;color:#161515}.invoice-billed-component[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]     .inline-form-field{width:60%!important}.invoice-billed-component[_ngcontent-%COMP%]   .col-11x[_ngcontent-%COMP%]{flex:0 0 96.666667%;max-width:97.666667%}.invoice-billed-component[_ngcontent-%COMP%]   .submit[_ngcontent-%COMP%]{font-weight:400;font-size:14px!important;line-height:31px;padding:0 14px}.invoice-billed-component[_ngcontent-%COMP%]   .title-class[_ngcontent-%COMP%]{font-size:18px!important}.invoice-billed-component[_ngcontent-%COMP%]   .confirm-button-class[_ngcontent-%COMP%]{margin:10px!important;font-size:15px!important;padding:11px 35px!important;box-shadow:none;font-weight:500}.invoice-billed-component[_ngcontent-%COMP%]     .mat-expansion-panel-header{background:#fbfbfb!important;height:42px!important}.invoice-billed-component[_ngcontent-%COMP%]   .spinner[_ngcontent-%COMP%]{margin:10px auto;width:60px;height:50px;text-align:center}.invoice-billed-component[_ngcontent-%COMP%]   .spinner[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{background-color:#cf0001;height:100%;width:6px;display:inline-block;margin-right:3px;animation:sk-stretchdelay 1.2s ease-in-out infinite}.invoice-billed-component[_ngcontent-%COMP%]   .spinner[_ngcontent-%COMP%]   .rect2[_ngcontent-%COMP%]{animation-delay:-1.1s}.invoice-billed-component[_ngcontent-%COMP%]   .spinner[_ngcontent-%COMP%]   .rect3[_ngcontent-%COMP%]{animation-delay:-1s}.invoice-billed-component[_ngcontent-%COMP%]   .spinner[_ngcontent-%COMP%]   .rect4[_ngcontent-%COMP%]{animation-delay:-.9s}.invoice-billed-component[_ngcontent-%COMP%]   .spinner[_ngcontent-%COMP%]   .rect5[_ngcontent-%COMP%]{animation-delay:-.8s}@keyframes sk-stretchdelay{0%,40%,to{transform:scaleY(.4);-webkit-transform:scaleY(.4)}20%{transform:scaleY(1);-webkit-transform:scaleY(1)}}.invoice-billed-component[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{font-weight:500!important;font-size:13px!important}.invoice-billed-component[_ngcontent-%COMP%]   .txt-heading[_ngcontent-%COMP%]{color:#777474;font-size:11px!important;font-weight:400}.invoice-billed-component[_ngcontent-%COMP%]   .file-name[_ngcontent-%COMP%]{font-size:12px!important;color:#161515;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.invoice-billed-component[_ngcontent-%COMP%]   .file-selected[_ngcontent-%COMP%], .invoice-billed-component[_ngcontent-%COMP%]   .select-row-selected[_ngcontent-%COMP%], .invoice-billed-component[_ngcontent-%COMP%]   .select-row[_ngcontent-%COMP%]:hover{background-color:rgba(223,223,217,.562)!important;cursor:pointer}.invoice-billed-component[_ngcontent-%COMP%]   .select-row-selected[_ngcontent-%COMP%]   .button-delete[_ngcontent-%COMP%], .invoice-billed-component[_ngcontent-%COMP%]   .select-row[_ngcontent-%COMP%]:hover   .button-delete[_ngcontent-%COMP%]{visibility:visible!important}.invoice-billed-component[_ngcontent-%COMP%]   .card-up[_ngcontent-%COMP%]{animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-top{0%{transform:translateY(20px)}to{transform:translateY(0)}}.invoice-billed-component[_ngcontent-%COMP%]     .attachment .file-name{padding-left:10px;width:100%}.invoice-billed-component[_ngcontent-%COMP%]   .upload-attachment[_ngcontent-%COMP%]{position:relative;z-index:2}']}),t})()}];let ct=(()=>{class t{}return t.\u0275mod=g["\u0275\u0275defineNgModule"]({type:t}),t.\u0275inj=g["\u0275\u0275defineInjector"]({factory:function(e){return new(e||t)},imports:[[o.k.forChild(lt)],o.k]}),t})();var mt=n("/1cH"),dt=n("pA3K"),st=n("Xi0T");let gt=(()=>{class t{}return t.\u0275mod=g["\u0275\u0275defineNgModule"]({type:t}),t.\u0275inj=g["\u0275\u0275defineInjector"]({factory:function(e){return new(e||t)},imports:[[a.CommonModule,ct,D.e,F.c,Y.d,x.f,mt.c,y.g,z.h,G.b,c.E,c.p,M.b,P.b,_.b,st.a,dt.a,T.b,v.b]]}),t})()},lzeh:function(t,e,n){"use strict";n.r(e),n.d(e,"InvoiceBillingModule",(function(){return ae}));var a=n("ofXK"),o=n("tyNb"),i=n("mrSG"),r=n("B/XX"),l=n("xG9w"),c=n("3Pt+"),m=n("PSD3"),d=n.n(m),s=n("wd/R"),g=n("7pIB"),p=n("wO+i"),u=n("m5YA"),b=n("Dh3D"),f=n("+0xr"),C=n("FKr1"),h=n("vg2w"),v=n("fXoL"),_=n("0IaG"),P=n("JLTg"),M=n("LcQX"),O=n("dNgK"),x=n("t+tn"),y=n("A6Kz"),k=n("JqCM"),w=n("XXEo"),S=n("8XCP"),I=n("F97M"),E=n("5gEQ"),D=n("YIj/"),F=n("JLuW"),z=n("bTqV"),N=n("Qu3c"),T=n("NFeN"),A=n("lVl8"),R=n("jaxi"),U=n("kmnG"),V=n("qFsG"),B=n("iadO"),H=n("d3UM"),j=n("ihCf");let K=(()=>{class t{transform(t){let e="";switch(t){case"docx":e="https://assets.kebs.app/docx.svg";break;case"doc":e="https://assets.kebs.app/doc.svg";break;case"pdf":e="https://assets.kebs.app/pdf.svg";break;case"png":e="https://assets.kebs.app/png.svg";break;case"csv":e="https://assets.kebs.app/csv.svg";break;case"xlsx":e="https://assets.kebs.app/xlsx.svg";break;case"jpg":e="https://assets.kebs.app/jpg.svg";break;case"jpeg":e="https://assets.kebs.app/jpeg.svg";break;case"txt":e="https://assets.kebs.app/txt.svg";break;case"zip":e="https://assets.kebs.app/zip.svg";break;case"mpp":e="https://assets.kebs.app/mpp.png";break;case"folder":e="https://assets.kebs.app/folder.svg"}return e}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275pipe=v["\u0275\u0275definePipe"]({name:"fileIcon",type:t,pure:!0}),t})();const q=["stepper"],L=["fileInput"],Y=["paginator"];function G(t,e){1&t&&(v["\u0275\u0275elementStart"](0,"mat-icon",69),v["\u0275\u0275text"](1,"check_circle"),v["\u0275\u0275elementEnd"]())}function $(t,e){if(1&t){const t=v["\u0275\u0275getCurrentView"]();v["\u0275\u0275elementStart"](0,"div",16),v["\u0275\u0275elementStart"](1,"div",68),v["\u0275\u0275listener"]("click",(function(){v["\u0275\u0275restoreView"](t);const e=v["\u0275\u0275nextContext"](2);return e.sendMail(null==e.invoiceInfo?null:e.invoiceInfo.billing_id,null==e.invoiceInfo?null:e.invoiceInfo.milestone_id,null==e.invoiceInfo?null:e.invoiceInfo.gantt_id)})),v["\u0275\u0275elementStart"](2,"mat-icon",18),v["\u0275\u0275text"](3,"send"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275template"](4,G,2,0,"mat-icon",24),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](5,"span",19),v["\u0275\u0275text"](6,"Send Mail"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"]()}if(2&t){const t=v["\u0275\u0275nextContext"](2);v["\u0275\u0275advance"](4),v["\u0275\u0275property"]("ngIf",(null==t.invoiceInfo?null:t.invoiceInfo.mail_sent_counter)>0&&1==(null==t.invoiceInfo?null:t.invoiceInfo.mail_sent_status))}}const Q=function(t){return{"green-icon":t}};function X(t,e){if(1&t){const t=v["\u0275\u0275getCurrentView"]();v["\u0275\u0275elementStart"](0,"div",16),v["\u0275\u0275elementStart"](1,"div",70),v["\u0275\u0275listener"]("click",(function(){v["\u0275\u0275restoreView"](t);const e=v["\u0275\u0275nextContext"](2);return e.markAsSent(null==e.invoiceInfo?null:e.invoiceInfo.billing_id)})),v["\u0275\u0275elementStart"](2,"mat-icon",71),v["\u0275\u0275text"](3,"mark_email_read"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](4,"span",19),v["\u0275\u0275text"](5,"Mark as Sent"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"]()}if(2&t){const t=v["\u0275\u0275nextContext"](2);v["\u0275\u0275advance"](2),v["\u0275\u0275property"]("ngClass",v["\u0275\u0275pureFunction1"](1,Q,2==(null==t.invoiceInfo?null:t.invoiceInfo.mail_sent_status)))}}function J(t,e){1&t&&(v["\u0275\u0275elementStart"](0,"mat-icon",69),v["\u0275\u0275text"](1,"check_circle"),v["\u0275\u0275elementEnd"]())}function W(t,e){if(1&t&&(v["\u0275\u0275elementStart"](0,"div",72),v["\u0275\u0275elementStart"](1,"div",73),v["\u0275\u0275elementStart"](2,"div",74),v["\u0275\u0275elementStart"](3,"mat-icon",18),v["\u0275\u0275text"](4),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](5,"div",75),v["\u0275\u0275elementStart"](6,"div",76),v["\u0275\u0275text"](7),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](8,"div",77),v["\u0275\u0275text"](9),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"]()),2&t){const t=v["\u0275\u0275nextContext"](2);v["\u0275\u0275advance"](2),v["\u0275\u0275styleProp"]("background-color",null==t.fieldKeyConfig||null==t.fieldKeyConfig.milestone_name?null:t.fieldKeyConfig.milestone_name.icon_background_color),v["\u0275\u0275advance"](1),v["\u0275\u0275styleProp"]("color",null==t.fieldKeyConfig||null==t.fieldKeyConfig.milestone_name?null:t.fieldKeyConfig.milestone_name.icon_color),v["\u0275\u0275advance"](1),v["\u0275\u0275textInterpolate"](null!=t.fieldKeyConfig&&null!=t.fieldKeyConfig.milestone_name&&t.fieldKeyConfig.milestone_name.icon?null==t.fieldKeyConfig||null==t.fieldKeyConfig.milestone_name?null:t.fieldKeyConfig.milestone_name.icon:"crisis_alert"),v["\u0275\u0275advance"](3),v["\u0275\u0275textInterpolate"](null!=t.fieldKeyConfig&&null!=t.fieldKeyConfig.milestone_name&&t.fieldKeyConfig.milestone_name.label_name?null==t.fieldKeyConfig||null==t.fieldKeyConfig.milestone_name?null:t.fieldKeyConfig.milestone_name.label_name:"Milestone Name"),v["\u0275\u0275advance"](2),v["\u0275\u0275textInterpolate"](null==t.invoiceInfo?null:t.invoiceInfo.milestone_name)}}function Z(t,e){if(1&t&&(v["\u0275\u0275elementStart"](0,"div",72),v["\u0275\u0275elementStart"](1,"div",73),v["\u0275\u0275elementStart"](2,"div",74),v["\u0275\u0275elementStart"](3,"mat-icon",18),v["\u0275\u0275text"](4),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](5,"div",75),v["\u0275\u0275elementStart"](6,"div",76),v["\u0275\u0275text"](7),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](8,"div",77),v["\u0275\u0275text"](9),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"]()),2&t){const t=v["\u0275\u0275nextContext"](2);v["\u0275\u0275advance"](2),v["\u0275\u0275styleProp"]("background-color",null==t.fieldKeyConfig||null==t.fieldKeyConfig.cost_center?null:t.fieldKeyConfig.cost_center.icon_background_color),v["\u0275\u0275advance"](1),v["\u0275\u0275styleProp"]("color",null==t.fieldKeyConfig||null==t.fieldKeyConfig.cost_center?null:t.fieldKeyConfig.cost_center.icon_color),v["\u0275\u0275advance"](1),v["\u0275\u0275textInterpolate"](null!=t.fieldKeyConfig&&null!=t.fieldKeyConfig.cost_center&&t.fieldKeyConfig.cost_center.icon?null==t.fieldKeyConfig||null==t.fieldKeyConfig.cost_center?null:t.fieldKeyConfig.cost_center.icon:"account_balance"),v["\u0275\u0275advance"](3),v["\u0275\u0275textInterpolate"](null!=t.fieldKeyConfig&&null!=t.fieldKeyConfig.cost_center&&t.fieldKeyConfig.cost_center.label_name?null==t.fieldKeyConfig||null==t.fieldKeyConfig.cost_center?null:t.fieldKeyConfig.cost_center.label_name:"Cost Center"),v["\u0275\u0275advance"](2),v["\u0275\u0275textInterpolate"](null==t.invoiceInfo?null:t.invoiceInfo.cost_center)}}function tt(t,e){if(1&t&&(v["\u0275\u0275elementStart"](0,"div",72),v["\u0275\u0275elementStart"](1,"div",73),v["\u0275\u0275elementStart"](2,"div",74),v["\u0275\u0275elementStart"](3,"mat-icon",18),v["\u0275\u0275text"](4),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](5,"div",75),v["\u0275\u0275elementStart"](6,"div",76),v["\u0275\u0275text"](7),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](8,"div",78),v["\u0275\u0275text"](9),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"]()),2&t){const t=v["\u0275\u0275nextContext"](2);v["\u0275\u0275advance"](2),v["\u0275\u0275styleProp"]("background-color",null==t.fieldKeyConfig||null==t.fieldKeyConfig.service_type?null:t.fieldKeyConfig.service_type.icon_background_color),v["\u0275\u0275advance"](1),v["\u0275\u0275styleProp"]("color",null==t.fieldKeyConfig||null==t.fieldKeyConfig.service_type?null:t.fieldKeyConfig.service_type.icon_color),v["\u0275\u0275advance"](1),v["\u0275\u0275textInterpolate"](null!=t.fieldKeyConfig&&null!=t.fieldKeyConfig.service_type&&t.fieldKeyConfig.service_type.icon?null==t.fieldKeyConfig||null==t.fieldKeyConfig.service_type?null:t.fieldKeyConfig.service_type.icon:"construction"),v["\u0275\u0275advance"](3),v["\u0275\u0275textInterpolate"](null!=t.fieldKeyConfig&&null!=t.fieldKeyConfig.service_type&&t.fieldKeyConfig.service_type.label_name?null==t.fieldKeyConfig||null==t.fieldKeyConfig.service_type?null:t.fieldKeyConfig.service_type.label_name:"Service Type"),v["\u0275\u0275advance"](2),v["\u0275\u0275textInterpolate1"](" ",null==t.invoiceInfo?null:t.invoiceInfo.service_type,"")}}function et(t,e){if(1&t&&(v["\u0275\u0275elementStart"](0,"div",72),v["\u0275\u0275elementStart"](1,"div",73),v["\u0275\u0275elementStart"](2,"div",74),v["\u0275\u0275elementStart"](3,"mat-icon",18),v["\u0275\u0275text"](4),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](5,"div",75),v["\u0275\u0275elementStart"](6,"div",76),v["\u0275\u0275text"](7),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](8,"div",78),v["\u0275\u0275elementStart"](9,"span"),v["\u0275\u0275text"](10),v["\u0275\u0275elementStart"](11,"span",79),v["\u0275\u0275text"](12),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"]()),2&t){const t=v["\u0275\u0275nextContext"](2);v["\u0275\u0275advance"](2),v["\u0275\u0275styleProp"]("background-color",null==t.fieldKeyConfig||null==t.fieldKeyConfig.amount_to_be_Collected?null:t.fieldKeyConfig.amount_to_be_Collected.icon_background_color),v["\u0275\u0275advance"](1),v["\u0275\u0275styleProp"]("color",null==t.fieldKeyConfig||null==t.fieldKeyConfig.amount_to_be_Collected?null:t.fieldKeyConfig.amount_to_be_Collected.icon_color),v["\u0275\u0275advance"](1),v["\u0275\u0275textInterpolate"](null!=t.fieldKeyConfig&&null!=t.fieldKeyConfig.amount_to_be_Collected&&t.fieldKeyConfig.amount_to_be_Collected.icon?null==t.fieldKeyConfig||null==t.fieldKeyConfig.amount_to_be_Collected?null:t.fieldKeyConfig.amount_to_be_Collected.icon:"account_balance_wallet"),v["\u0275\u0275advance"](3),v["\u0275\u0275textInterpolate"](null!=t.fieldKeyConfig&&null!=t.fieldKeyConfig.amount_to_be_Collected&&t.fieldKeyConfig.amount_to_be_Collected.label_name?null==t.fieldKeyConfig||null==t.fieldKeyConfig.amount_to_be_Collected?null:t.fieldKeyConfig.amount_to_be_Collected.label_name:"Planned Collection"),v["\u0275\u0275advance"](3),v["\u0275\u0275textInterpolate1"]("",t.fixNumber(t.activityForm.value.amountToBeCollected)," "),v["\u0275\u0275advance"](2),v["\u0275\u0275textInterpolate"](t.activityForm.value.invoiceRaisedCurrency)}}function nt(t,e){if(1&t&&(v["\u0275\u0275elementStart"](0,"div",3),v["\u0275\u0275elementStart"](1,"div",82),v["\u0275\u0275text"](2," Tax "),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](3,"div",82),v["\u0275\u0275text"](4),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](5,"div",82),v["\u0275\u0275text"](6),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"]()),2&t){const t=v["\u0275\u0275nextContext"](3);v["\u0275\u0275advance"](4),v["\u0275\u0275textInterpolate1"](" ",t.currentCurrency," "),v["\u0275\u0275advance"](2),v["\u0275\u0275textInterpolate1"](" ",t.fixNumber(t.taxAmount)," ")}}function at(t,e){if(1&t&&(v["\u0275\u0275elementStart"](0,"div",3),v["\u0275\u0275elementStart"](1,"div",82),v["\u0275\u0275text"](2," TDS "),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](3,"div",82),v["\u0275\u0275text"](4),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](5,"div",82),v["\u0275\u0275text"](6),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"]()),2&t){const t=v["\u0275\u0275nextContext"](3);v["\u0275\u0275advance"](4),v["\u0275\u0275textInterpolate1"](" ",t.currentCurrency," "),v["\u0275\u0275advance"](2),v["\u0275\u0275textInterpolate1"](" ",t.fixNumber(t.tdsValue)," ")}}function ot(t,e){if(1&t&&(v["\u0275\u0275elementStart"](0,"div",3),v["\u0275\u0275elementStart"](1,"div",82),v["\u0275\u0275text"](2," TCS "),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](3,"div",82),v["\u0275\u0275text"](4),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](5,"div",82),v["\u0275\u0275text"](6),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"]()),2&t){const t=v["\u0275\u0275nextContext"](3);v["\u0275\u0275advance"](4),v["\u0275\u0275textInterpolate1"](" ",t.currentCurrency," "),v["\u0275\u0275advance"](2),v["\u0275\u0275textInterpolate1"](" ",t.fixNumber(t.tcsValue)," ")}}function it(t,e){if(1&t&&(v["\u0275\u0275elementStart"](0,"div",3),v["\u0275\u0275elementStart"](1,"div",82),v["\u0275\u0275text"](2," Discount "),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](3,"div",82),v["\u0275\u0275text"](4),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](5,"div",82),v["\u0275\u0275text"](6),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"]()),2&t){const t=v["\u0275\u0275nextContext"](3);v["\u0275\u0275advance"](4),v["\u0275\u0275textInterpolate1"](" ",t.currentCurrency," "),v["\u0275\u0275advance"](2),v["\u0275\u0275textInterpolate1"](" ",t.fixNumber(t.discountAmount)," ")}}function rt(t,e){if(1&t&&(v["\u0275\u0275elementStart"](0,"div",3),v["\u0275\u0275elementStart"](1,"div",82),v["\u0275\u0275text"](2," Retention "),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](3,"div",82),v["\u0275\u0275text"](4),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](5,"div",82),v["\u0275\u0275text"](6),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"]()),2&t){const t=v["\u0275\u0275nextContext"](3);v["\u0275\u0275advance"](4),v["\u0275\u0275textInterpolate1"](" ",t.currentCurrency," "),v["\u0275\u0275advance"](2),v["\u0275\u0275textInterpolate1"](" ",t.fixNumber(t.retentionAmount)," ")}}function lt(t,e){if(1&t&&(v["\u0275\u0275elementStart"](0,"div",80),v["\u0275\u0275elementStart"](1,"div",81),v["\u0275\u0275elementStart"](2,"span"),v["\u0275\u0275text"](3,"Invoice Value Summary"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](4,"div",3),v["\u0275\u0275elementStart"](5,"div",82),v["\u0275\u0275text"](6," SubTotal "),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](7,"div",82),v["\u0275\u0275text"](8),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](9,"div",82),v["\u0275\u0275text"](10),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275template"](11,nt,7,2,"div",83),v["\u0275\u0275template"](12,at,7,2,"div",83),v["\u0275\u0275template"](13,ot,7,2,"div",83),v["\u0275\u0275template"](14,it,7,2,"div",83),v["\u0275\u0275template"](15,rt,7,2,"div",83),v["\u0275\u0275elementEnd"]()),2&t){const t=v["\u0275\u0275nextContext"](2);v["\u0275\u0275advance"](8),v["\u0275\u0275textInterpolate1"](" ",t.currentCurrency," "),v["\u0275\u0275advance"](2),v["\u0275\u0275textInterpolate1"](" ",t.fixNumber(t.subTotalValue)," "),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("ngIf",null==t.invoiceInfo?null:t.invoiceInfo.tax_amount),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("ngIf",null==t.invoiceInfo?null:t.invoiceInfo.tds_value),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("ngIf",null==t.invoiceInfo?null:t.invoiceInfo.tcs_value),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("ngIf",null==t.invoiceInfo?null:t.invoiceInfo.discount_value),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("ngIf",null==t.invoiceInfo?null:t.invoiceInfo.retention_value)}}function ct(t,e){if(1&t&&(v["\u0275\u0275elementStart"](0,"div",63),v["\u0275\u0275elementStart"](1,"mat-icon",84),v["\u0275\u0275text"](2,"access_time"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](3,"div",85),v["\u0275\u0275text"](4),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"]()),2&t){const t=v["\u0275\u0275nextContext"](2);v["\u0275\u0275advance"](4),v["\u0275\u0275textInterpolate1"](" ",null==t.invoiceInfo?null:t.invoiceInfo.expected_days," days left")}}const mt=function(t){return{"btn-toggle-selected":t}};function dt(t,e){if(1&t){const t=v["\u0275\u0275getCurrentView"]();v["\u0275\u0275elementStart"](0,"mat-button-toggle",97),v["\u0275\u0275listener"]("click",(function(){return v["\u0275\u0275restoreView"](t),v["\u0275\u0275nextContext"](3).navigateScreen("history")})),v["\u0275\u0275text"](1,"History"),v["\u0275\u0275elementEnd"]()}if(2&t){const t=v["\u0275\u0275nextContext"](3);v["\u0275\u0275property"]("ngClass",v["\u0275\u0275pureFunction1"](1,mt,"history"===t.currentScreen))}}const st=function(t){return{"button-disabled":t}},gt=function(t){return{color:t,"font-size":"19px","margin-top":"3px"}};function pt(t,e){if(1&t){const t=v["\u0275\u0275getCurrentView"]();v["\u0275\u0275elementStart"](0,"div",98),v["\u0275\u0275elementStart"](1,"div",3),v["\u0275\u0275elementStart"](2,"button",99),v["\u0275\u0275listener"]("click",(function(){return v["\u0275\u0275restoreView"](t),v["\u0275\u0275nextContext"](3).submitPayment()})),v["\u0275\u0275text"](3," Submit "),v["\u0275\u0275elementStart"](4,"mat-icon",100),v["\u0275\u0275text"](5," check_circle_outline "),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"]()}if(2&t){const t=v["\u0275\u0275nextContext"](3);v["\u0275\u0275advance"](2),v["\u0275\u0275property"]("ngClass",v["\u0275\u0275pureFunction1"](3,st,t.activityForm.get("amountReceived").invalid||t.activityForm.get("receivedDate").invalid||t.submitButtonDisabled))("disabled",t.activityForm.get("amountReceived").invalid||t.activityForm.get("receivedDate").invalid||1==t.submitButtonDisabled),v["\u0275\u0275advance"](2),v["\u0275\u0275property"]("ngStyle",v["\u0275\u0275pureFunction1"](5,gt,t.activityForm.get("amountReceived").invalid||t.activityForm.get("receivedDate").invalid||t.submitButtonDisabled?"":"white"))}}function ut(t,e){1&t&&(v["\u0275\u0275elementStart"](0,"mat-error",119),v["\u0275\u0275text"](1," Amount Received can't be 0 or -ve "),v["\u0275\u0275elementEnd"]())}function bt(t,e){if(1&t&&(v["\u0275\u0275elementStart"](0,"mat-option",120),v["\u0275\u0275text"](1),v["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit;v["\u0275\u0275property"]("value",t.bank_name),v["\u0275\u0275advance"](1),v["\u0275\u0275textInterpolate"](t.bank_name)}}function ft(t,e){if(1&t){const t=v["\u0275\u0275getCurrentView"]();v["\u0275\u0275elementStart"](0,"div",3),v["\u0275\u0275elementStart"](1,"div",101),v["\u0275\u0275elementStart"](2,"div",3),v["\u0275\u0275elementStart"](3,"div",102),v["\u0275\u0275elementStart"](4,"div",103),v["\u0275\u0275text"](5,"Amount Received "),v["\u0275\u0275elementStart"](6,"span",104),v["\u0275\u0275text"](7,"*"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](8,"div",105),v["\u0275\u0275elementStart"](9,"mat-form-field",106),v["\u0275\u0275elementStart"](10,"input",107),v["\u0275\u0275listener"]("input",(function(e){v["\u0275\u0275restoreView"](t);const n=v["\u0275\u0275nextContext"](3);return n.formatInput(e,"amountReceived",n.activityForm,e)})),v["\u0275\u0275elementEnd"](),v["\u0275\u0275template"](11,ut,2,0,"mat-error",108),v["\u0275\u0275elementStart"](12,"span",109),v["\u0275\u0275text"](13),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](14,"div",102),v["\u0275\u0275elementStart"](15,"div",103),v["\u0275\u0275text"](16,"Received Date "),v["\u0275\u0275elementStart"](17,"span",104),v["\u0275\u0275text"](18,"*"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](19,"div",110),v["\u0275\u0275elementStart"](20,"mat-form-field",106),v["\u0275\u0275element"](21,"input",111),v["\u0275\u0275element"](22,"mat-datepicker-toggle",112),v["\u0275\u0275element"](23,"mat-datepicker",null,113),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](25,"div",114),v["\u0275\u0275elementStart"](26,"div",102),v["\u0275\u0275elementStart"](27,"div",103),v["\u0275\u0275text"](28,"Payment Received Bank"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](29,"div",3),v["\u0275\u0275elementStart"](30,"mat-form-field",106),v["\u0275\u0275elementStart"](31,"mat-select",115),v["\u0275\u0275elementStart"](32,"mat-option",116),v["\u0275\u0275text"](33,"Select"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275template"](34,bt,2,2,"mat-option",117),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](35,"div",102),v["\u0275\u0275elementStart"](36,"div",103),v["\u0275\u0275text"](37,"Bank Reference Number"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](38,"div",3),v["\u0275\u0275elementStart"](39,"mat-form-field",106),v["\u0275\u0275element"](40,"input",118),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"]()}if(2&t){const t=v["\u0275\u0275reference"](24),e=v["\u0275\u0275nextContext"](3);v["\u0275\u0275advance"](11),v["\u0275\u0275property"]("ngIf",!(null!=e.activityForm.controls.amountReceived&&null!=e.activityForm.controls.amountReceived.errors&&e.activityForm.controls.amountReceived.errors.validAmount)),v["\u0275\u0275advance"](2),v["\u0275\u0275textInterpolate1"](" ",e.activityForm.value.invoiceRaisedCurrency," "),v["\u0275\u0275advance"](8),v["\u0275\u0275property"]("matDatepicker",t)("max",e.today)("placeholder",e.invoiceDateFormat),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("for",t),v["\u0275\u0275advance"](12),v["\u0275\u0275property"]("ngForOf",e.bankDetails)}}function Ct(t,e){1&t&&(v["\u0275\u0275elementStart"](0,"div",121),v["\u0275\u0275elementStart"](1,"div",122),v["\u0275\u0275elementStart"](2,"span",123),v["\u0275\u0275text"](3,"! Important: "),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](4,"span",124),v["\u0275\u0275text"](5,"You should attach documents or notes to complete the payment"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"]())}function ht(t,e){1&t&&(v["\u0275\u0275elementStart"](0,"div",125),v["\u0275\u0275elementStart"](1,"div",3),v["\u0275\u0275elementStart"](2,"span",126),v["\u0275\u0275text"](3,"Notes "),v["\u0275\u0275elementStart"](4,"span",104),v["\u0275\u0275text"](5,"*"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275element"](6,"br"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](7,"div",127),v["\u0275\u0275elementStart"](8,"div",9),v["\u0275\u0275elementStart"](9,"mat-form-field",128),v["\u0275\u0275element"](10,"textarea",129,130),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"]())}function vt(t,e){if(1&t){const t=v["\u0275\u0275getCurrentView"]();v["\u0275\u0275elementStart"](0,"span",143),v["\u0275\u0275element"](1,"img",144),v["\u0275\u0275pipe"](2,"fileIcon"),v["\u0275\u0275elementStart"](3,"div",145),v["\u0275\u0275elementStart"](4,"span",140),v["\u0275\u0275listener"]("click",(function(){v["\u0275\u0275restoreView"](t);const n=e.$implicit;return v["\u0275\u0275nextContext"](4).viewFile(n)})),v["\u0275\u0275text"](5),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](6,"mat-icon",146),v["\u0275\u0275listener"]("click",(function(){v["\u0275\u0275restoreView"](t);const n=e.$implicit;return v["\u0275\u0275nextContext"](4).deleteFile(n)})),v["\u0275\u0275text"](7,"delete_outline"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"]()}if(2&t){const t=e.$implicit;v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("src",v["\u0275\u0275pipeBind1"](2,3,null==t?null:t.file_format),v["\u0275\u0275sanitizeUrl"]),v["\u0275\u0275advance"](2),v["\u0275\u0275property"]("matTooltip",t.file_name),v["\u0275\u0275advance"](2),v["\u0275\u0275textInterpolate"](t.file_name)}}function _t(t,e){if(1&t){const t=v["\u0275\u0275getCurrentView"]();v["\u0275\u0275elementStart"](0,"div",131),v["\u0275\u0275elementStart"](1,"div",132),v["\u0275\u0275elementStart"](2,"div",133),v["\u0275\u0275text"](3," Or"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](4,"div",134),v["\u0275\u0275text"](5," Attach Payment Remittance Document"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](6,"div",135),v["\u0275\u0275elementStart"](7,"div",136),v["\u0275\u0275elementStart"](8,"span",137),v["\u0275\u0275text"](9),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](10,"input",138,139),v["\u0275\u0275listener"]("change",(function(e){return v["\u0275\u0275restoreView"](t),v["\u0275\u0275nextContext"](3).onFileSelected(e)})),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](12,"mat-icon",140),v["\u0275\u0275listener"]("click",(function(){return v["\u0275\u0275restoreView"](t),v["\u0275\u0275nextContext"](3).triggerFileInput()})),v["\u0275\u0275text"](13,"cloud_upload"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](14,"span"),v["\u0275\u0275text"](15,"Upload"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](16,"div",141),v["\u0275\u0275template"](17,vt,8,5,"span",142),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"]()}if(2&t){const t=v["\u0275\u0275nextContext"](3);v["\u0275\u0275advance"](9),v["\u0275\u0275textInterpolate"](t.selectedFiles.length),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("uploader",t.uploader),v["\u0275\u0275advance"](7),v["\u0275\u0275property"]("ngForOf",t.selectedFiles)}}function Pt(t,e){if(1&t){const t=v["\u0275\u0275getCurrentView"]();v["\u0275\u0275elementStart"](0,"div",86),v["\u0275\u0275elementStart"](1,"div",87),v["\u0275\u0275elementStart"](2,"div",88),v["\u0275\u0275elementStart"](3,"div",89),v["\u0275\u0275elementStart"](4,"div",3),v["\u0275\u0275elementStart"](5,"mat-button-toggle-group"),v["\u0275\u0275elementStart"](6,"mat-button-toggle",90),v["\u0275\u0275listener"]("click",(function(){return v["\u0275\u0275restoreView"](t),v["\u0275\u0275nextContext"](2).navigateScreen("payment")})),v["\u0275\u0275text"](7,"Add Payments"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275template"](8,dt,2,3,"mat-button-toggle",91),v["\u0275\u0275elementStart"](9,"mat-button-toggle",92),v["\u0275\u0275listener"]("click",(function(){return v["\u0275\u0275restoreView"](t),v["\u0275\u0275nextContext"](2).navigateScreen("documents")})),v["\u0275\u0275text"](10,"Documents"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275template"](11,pt,6,7,"div",93),v["\u0275\u0275elementEnd"](),v["\u0275\u0275template"](12,ft,41,7,"div",83),v["\u0275\u0275template"](13,Ct,6,0,"div",94),v["\u0275\u0275template"](14,ht,12,0,"div",95),v["\u0275\u0275template"](15,_t,18,3,"div",96),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"]()}if(2&t){const t=v["\u0275\u0275nextContext"](2);v["\u0275\u0275property"]("formGroup",t.activityForm),v["\u0275\u0275advance"](6),v["\u0275\u0275property"]("ngClass",v["\u0275\u0275pureFunction1"](9,mt,"payment"===t.currentScreen)),v["\u0275\u0275advance"](2),v["\u0275\u0275property"]("ngIf",9!=(null==t.invoiceInfo?null:t.invoiceInfo.status)),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("ngClass",v["\u0275\u0275pureFunction1"](11,mt,"documents"===t.currentScreen)),v["\u0275\u0275advance"](2),v["\u0275\u0275property"]("ngIf","payment"==t.currentScreen),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("ngIf","payment"==t.currentScreen),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("ngIf","payment"==t.currentScreen&&t.showPaymentRemittance),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("ngIf","payment"==t.currentScreen&&t.showPaymentRemittance),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("ngIf","payment"==t.currentScreen&&t.showPaymentRemittance)}}function Mt(t,e){if(1&t){const t=v["\u0275\u0275getCurrentView"]();v["\u0275\u0275elementStart"](0,"mat-button-toggle",90),v["\u0275\u0275listener"]("click",(function(){return v["\u0275\u0275restoreView"](t),v["\u0275\u0275nextContext"](3).navigateScreen("payment")})),v["\u0275\u0275text"](1,"Add Payments"),v["\u0275\u0275elementEnd"]()}if(2&t){const t=v["\u0275\u0275nextContext"](3);v["\u0275\u0275property"]("ngClass",v["\u0275\u0275pureFunction1"](1,mt,"payment"===t.currentScreen))}}function Ot(t,e){if(1&t){const t=v["\u0275\u0275getCurrentView"]();v["\u0275\u0275elementStart"](0,"mat-button-toggle",97),v["\u0275\u0275listener"]("click",(function(){return v["\u0275\u0275restoreView"](t),v["\u0275\u0275nextContext"](3).navigateScreen("history")})),v["\u0275\u0275text"](1,"History"),v["\u0275\u0275elementEnd"]()}if(2&t){const t=v["\u0275\u0275nextContext"](3);v["\u0275\u0275property"]("ngClass",v["\u0275\u0275pureFunction1"](1,mt,"history"===t.currentScreen))}}function xt(t,e){if(1&t&&(v["\u0275\u0275elementStart"](0,"div",166),v["\u0275\u0275text"](1),v["\u0275\u0275elementEnd"]()),2&t){const t=v["\u0275\u0275nextContext"]().$implicit;v["\u0275\u0275advance"](1),v["\u0275\u0275textInterpolate1"](" Notes: ",t.notes," ")}}function yt(t,e){if(1&t){const t=v["\u0275\u0275getCurrentView"]();v["\u0275\u0275elementStart"](0,"span"),v["\u0275\u0275elementStart"](1,"span",169),v["\u0275\u0275listener"]("click",(function(){v["\u0275\u0275restoreView"](t);const n=e.$implicit;return v["\u0275\u0275nextContext"](6).viewFile(n)})),v["\u0275\u0275text"](2),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"]()}if(2&t){const t=e.$implicit;v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("matTooltip",t.file_name),v["\u0275\u0275advance"](1),v["\u0275\u0275textInterpolate"](null==t?null:t.file_name)}}function kt(t,e){if(1&t&&(v["\u0275\u0275elementStart"](0,"div",167),v["\u0275\u0275template"](1,yt,3,2,"span",168),v["\u0275\u0275elementEnd"]()),2&t){const t=v["\u0275\u0275nextContext"]().$implicit,e=v["\u0275\u0275nextContext"](4);v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("ngForOf",e.getFilesForPayment(t.payment_id))}}function wt(t,e){if(1&t&&(v["\u0275\u0275elementStart"](0,"div",157),v["\u0275\u0275elementStart"](1,"span",158),v["\u0275\u0275text"](2),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](3,"div",82),v["\u0275\u0275elementStart"](4,"div",114),v["\u0275\u0275elementStart"](5,"div",159),v["\u0275\u0275elementStart"](6,"div",160),v["\u0275\u0275text"](7," Bank Reference Number "),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](8,"div",161),v["\u0275\u0275text"](9),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](10,"div",159),v["\u0275\u0275elementStart"](11,"div",160),v["\u0275\u0275text"](12," Payment Received Bank "),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](13,"div",161),v["\u0275\u0275text"](14),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](15,"div",82),v["\u0275\u0275elementStart"](16,"div",160),v["\u0275\u0275text"](17," Payment Received Date "),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](18,"div",162),v["\u0275\u0275text"](19),v["\u0275\u0275element"](20,"div"),v["\u0275\u0275element"](21,"div",82),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](22,"div",163),v["\u0275\u0275template"](23,xt,2,1,"div",164),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](24,"div",3),v["\u0275\u0275template"](25,kt,2,1,"div",165),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit,n=v["\u0275\u0275nextContext"](4);v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("matTooltip",null==t?null:t.toolTipValue),v["\u0275\u0275advance"](1),v["\u0275\u0275textInterpolate"](null==t?null:t.formattedValue),v["\u0275\u0275advance"](7),v["\u0275\u0275textInterpolate1"](" ",t.bank_reference_no?t.bank_reference_no:t.tally_no?t.tally_no:"-"," "),v["\u0275\u0275advance"](5),v["\u0275\u0275textInterpolate1"](" ",t.payment_received_bank?t.payment_received_bank:"-"," "),v["\u0275\u0275advance"](5),v["\u0275\u0275textInterpolate1"](" ",n.getDate(t.received_date)," "),v["\u0275\u0275advance"](4),v["\u0275\u0275property"]("ngIf",t.notes),v["\u0275\u0275advance"](2),v["\u0275\u0275property"]("ngIf",t.payment_context_id)}}function St(t,e){if(1&t&&(v["\u0275\u0275elementStart"](0,"div",155),v["\u0275\u0275elementStart"](1,"div",82),v["\u0275\u0275template"](2,wt,26,7,"div",156),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"]()),2&t){const t=v["\u0275\u0275nextContext"](3);v["\u0275\u0275advance"](2),v["\u0275\u0275property"]("ngForOf",t.paymentInfo)}}function It(t,e){if(1&t){const t=v["\u0275\u0275getCurrentView"]();v["\u0275\u0275elementStart"](0,"div",147),v["\u0275\u0275elementStart"](1,"div",87),v["\u0275\u0275elementStart"](2,"div",148),v["\u0275\u0275elementStart"](3,"div",149),v["\u0275\u0275elementStart"](4,"div",3),v["\u0275\u0275elementStart"](5,"mat-button-toggle-group"),v["\u0275\u0275template"](6,Mt,2,3,"mat-button-toggle",150),v["\u0275\u0275template"](7,Ot,2,3,"mat-button-toggle",91),v["\u0275\u0275elementStart"](8,"mat-button-toggle",92),v["\u0275\u0275listener"]("click",(function(){return v["\u0275\u0275restoreView"](t),v["\u0275\u0275nextContext"](2).navigateScreen("documents")})),v["\u0275\u0275text"](9,"Documents"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](10,"div",151),v["\u0275\u0275elementStart"](11,"div",152),v["\u0275\u0275elementStart"](12,"mat-icon",153),v["\u0275\u0275listener"]("click",(function(){return v["\u0275\u0275restoreView"](t),v["\u0275\u0275nextContext"](2).changeHistoryAmount()})),v["\u0275\u0275text"](13,"loop"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](14,"span"),v["\u0275\u0275text"](15),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275template"](16,St,3,1,"div",154),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"]()}if(2&t){const t=v["\u0275\u0275nextContext"](2);v["\u0275\u0275advance"](6),v["\u0275\u0275property"]("ngIf",11!=(null==t.invoiceInfo?null:t.invoiceInfo.status)),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("ngIf",9!=(null==t.invoiceInfo?null:t.invoiceInfo.status)),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("ngClass",v["\u0275\u0275pureFunction1"](5,mt,"documents"===t.currentScreen)),v["\u0275\u0275advance"](7),v["\u0275\u0275textInterpolate"](t.paymentHistoryCurrency?t.paymentHistoryCurrency:"-"),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("ngIf",(null==t.paymentInfo?null:t.paymentInfo.length)>0)}}function Et(t,e){if(1&t){const t=v["\u0275\u0275getCurrentView"]();v["\u0275\u0275elementStart"](0,"mat-button-toggle",90),v["\u0275\u0275listener"]("click",(function(){return v["\u0275\u0275restoreView"](t),v["\u0275\u0275nextContext"](3).navigateScreen("payment")})),v["\u0275\u0275text"](1,"Add Payments"),v["\u0275\u0275elementEnd"]()}if(2&t){const t=v["\u0275\u0275nextContext"](3);v["\u0275\u0275property"]("ngClass",v["\u0275\u0275pureFunction1"](1,mt,"payment"===t.currentScreen))}}function Dt(t,e){if(1&t){const t=v["\u0275\u0275getCurrentView"]();v["\u0275\u0275elementStart"](0,"mat-button-toggle",97),v["\u0275\u0275listener"]("click",(function(){return v["\u0275\u0275restoreView"](t),v["\u0275\u0275nextContext"](3).navigateScreen("history")})),v["\u0275\u0275text"](1,"History"),v["\u0275\u0275elementEnd"]()}if(2&t){const t=v["\u0275\u0275nextContext"](3);v["\u0275\u0275property"]("ngClass",v["\u0275\u0275pureFunction1"](1,mt,"history"===t.currentScreen))}}function Ft(t,e){if(1&t){const t=v["\u0275\u0275getCurrentView"]();v["\u0275\u0275elementStart"](0,"div",174),v["\u0275\u0275elementStart"](1,"button",175),v["\u0275\u0275listener"]("click",(function(){return v["\u0275\u0275restoreView"](t),v["\u0275\u0275nextContext"](3).triggerFileInput()})),v["\u0275\u0275text"](2," Add Document "),v["\u0275\u0275elementStart"](3,"mat-icon",176),v["\u0275\u0275text"](4," attach_file "),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"]()}}function zt(t,e){1&t&&(v["\u0275\u0275elementStart"](0,"mat-header-cell",187),v["\u0275\u0275text"](1," File Type "),v["\u0275\u0275elementEnd"]())}function Nt(t,e){if(1&t&&(v["\u0275\u0275elementStart"](0,"mat-cell",188),v["\u0275\u0275element"](1,"img",189),v["\u0275\u0275pipe"](2,"fileIcon"),v["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit;v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("src",v["\u0275\u0275pipeBind1"](2,1,t.file_format),v["\u0275\u0275sanitizeUrl"])}}function Tt(t,e){1&t&&(v["\u0275\u0275elementStart"](0,"mat-header-cell",190),v["\u0275\u0275text"](1," File Name "),v["\u0275\u0275elementEnd"]())}function At(t,e){if(1&t){const t=v["\u0275\u0275getCurrentView"]();v["\u0275\u0275elementStart"](0,"mat-cell",191),v["\u0275\u0275listener"]("click",(function(){v["\u0275\u0275restoreView"](t);const n=e.$implicit;return v["\u0275\u0275nextContext"](4).viewFile(n)})),v["\u0275\u0275text"](1),v["\u0275\u0275elementEnd"]()}if(2&t){const t=e.$implicit;v["\u0275\u0275advance"](1),v["\u0275\u0275textInterpolate1"](" ",t.file_name," ")}}function Rt(t,e){1&t&&v["\u0275\u0275element"](0,"mat-header-row")}function Ut(t,e){1&t&&v["\u0275\u0275element"](0,"mat-row")}const Vt=function(){return["file_type","file_name"]};function Bt(t,e){if(1&t){const t=v["\u0275\u0275getCurrentView"]();v["\u0275\u0275elementStart"](0,"div",3),v["\u0275\u0275elementStart"](1,"div",177),v["\u0275\u0275elementStart"](2,"mat-table",178),v["\u0275\u0275listener"]("matSortChange",(function(){return v["\u0275\u0275restoreView"](t),v["\u0275\u0275nextContext"](3).sortData()})),v["\u0275\u0275elementContainerStart"](3,179),v["\u0275\u0275template"](4,zt,2,0,"mat-header-cell",180),v["\u0275\u0275template"](5,Nt,3,3,"mat-cell",181),v["\u0275\u0275elementContainerEnd"](),v["\u0275\u0275elementContainerStart"](6,182),v["\u0275\u0275template"](7,Tt,2,0,"mat-header-cell",183),v["\u0275\u0275template"](8,At,2,1,"mat-cell",184),v["\u0275\u0275elementContainerEnd"](),v["\u0275\u0275template"](9,Rt,1,0,"mat-header-row",185),v["\u0275\u0275template"](10,Ut,1,0,"mat-row",186),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"]()}if(2&t){const t=v["\u0275\u0275nextContext"](3);v["\u0275\u0275advance"](2),v["\u0275\u0275property"]("dataSource",t.dataSource),v["\u0275\u0275advance"](7),v["\u0275\u0275property"]("matHeaderRowDef",v["\u0275\u0275pureFunction0"](3,Vt)),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("matRowDefColumns",v["\u0275\u0275pureFunction0"](4,Vt))}}function Ht(t,e){1&t&&(v["\u0275\u0275elementStart"](0,"div",3),v["\u0275\u0275elementStart"](1,"div",192),v["\u0275\u0275elementStart"](2,"div",193),v["\u0275\u0275text"](3,"No Documents Found!"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275element"](4,"img",194),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"]())}function jt(t,e){if(1&t){const t=v["\u0275\u0275getCurrentView"]();v["\u0275\u0275elementStart"](0,"div",147),v["\u0275\u0275elementStart"](1,"div",170),v["\u0275\u0275elementStart"](2,"div",171),v["\u0275\u0275elementStart"](3,"div",3),v["\u0275\u0275elementStart"](4,"mat-button-toggle-group"),v["\u0275\u0275template"](5,Et,2,3,"mat-button-toggle",150),v["\u0275\u0275template"](6,Dt,2,3,"mat-button-toggle",91),v["\u0275\u0275elementStart"](7,"mat-button-toggle",92),v["\u0275\u0275listener"]("click",(function(){return v["\u0275\u0275restoreView"](t),v["\u0275\u0275nextContext"](2).navigateScreen("documents")})),v["\u0275\u0275text"](8,"Documents"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](9,"div",172),v["\u0275\u0275elementStart"](10,"input",138,139),v["\u0275\u0275listener"]("change",(function(e){return v["\u0275\u0275restoreView"](t),v["\u0275\u0275nextContext"](2).onMilestoneFileSelected(e)})),v["\u0275\u0275elementEnd"](),v["\u0275\u0275template"](12,Ft,5,0,"div",173),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275template"](13,Bt,11,5,"div",83),v["\u0275\u0275template"](14,Ht,5,0,"div",83),v["\u0275\u0275elementEnd"]()}if(2&t){const t=v["\u0275\u0275nextContext"](2);v["\u0275\u0275advance"](5),v["\u0275\u0275property"]("ngIf",11!=(null==t.invoiceInfo?null:t.invoiceInfo.status)),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("ngIf",9!=(null==t.invoiceInfo?null:t.invoiceInfo.status)),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("ngClass",v["\u0275\u0275pureFunction1"](7,mt,"documents"===t.currentScreen)),v["\u0275\u0275advance"](3),v["\u0275\u0275property"]("uploader",t.milestoneUploader),v["\u0275\u0275advance"](2),v["\u0275\u0275property"]("ngIf","documents"==t.currentScreen),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("ngIf",(null==t.milestoneFiles?null:t.milestoneFiles.length)>0),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("ngIf",0==(null==t.milestoneFiles?null:t.milestoneFiles.length))}}const Kt=function(){return{"tooltip-class":"tooltip-feedback"}};function qt(t,e){if(1&t){const t=v["\u0275\u0275getCurrentView"]();v["\u0275\u0275elementStart"](0,"div",2),v["\u0275\u0275elementStart"](1,"div",3),v["\u0275\u0275elementStart"](2,"div",4),v["\u0275\u0275elementStart"](3,"div",5),v["\u0275\u0275elementStart"](4,"div",6),v["\u0275\u0275elementStart"](5,"button",7),v["\u0275\u0275listener"]("click",(function(){return v["\u0275\u0275restoreView"](t),v["\u0275\u0275nextContext"]().back()})),v["\u0275\u0275elementStart"](6,"mat-icon",8),v["\u0275\u0275text"](7,"chevron_left"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](8,"div",9),v["\u0275\u0275elementStart"](9,"div",10),v["\u0275\u0275elementStart"](10,"span",11),v["\u0275\u0275text"](11," Invoice No."),v["\u0275\u0275elementStart"](12,"span",12),v["\u0275\u0275text"](13),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](14,"div",6),v["\u0275\u0275elementStart"](15,"button",13),v["\u0275\u0275listener"]("click",(function(){v["\u0275\u0275restoreView"](t);const e=v["\u0275\u0275nextContext"]();return e.launchProjectFromInvoice(e.projectId,e.projectName,e.itemId,e.itemName)})),v["\u0275\u0275elementStart"](16,"mat-icon",8),v["\u0275\u0275text"](17,"launch"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275element"](18,"hr",14),v["\u0275\u0275elementStart"](19,"div",15),v["\u0275\u0275elementStart"](20,"div",16),v["\u0275\u0275elementStart"](21,"div",17),v["\u0275\u0275listener"]("click",(function(){return v["\u0275\u0275restoreView"](t),v["\u0275\u0275nextContext"]().viewInvoice()})),v["\u0275\u0275elementStart"](22,"mat-icon",18),v["\u0275\u0275text"](23,"receipt"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](24,"span",19),v["\u0275\u0275text"](25,"Invoice"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275template"](26,$,7,1,"div",20),v["\u0275\u0275template"](27,X,6,3,"div",20),v["\u0275\u0275elementStart"](28,"div",16),v["\u0275\u0275elementStart"](29,"div",21),v["\u0275\u0275listener"]("click",(function(){return v["\u0275\u0275restoreView"](t),v["\u0275\u0275nextContext"]().openAnnexure()})),v["\u0275\u0275elementStart"](30,"mat-icon",18),v["\u0275\u0275text"](31,"description"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](32,"span",19),v["\u0275\u0275text"](33,"Annexure"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](34,"div",16),v["\u0275\u0275elementStart"](35,"div",22),v["\u0275\u0275listener"]("click",(function(){return v["\u0275\u0275restoreView"](t),v["\u0275\u0275nextContext"]().addNotes()})),v["\u0275\u0275elementStart"](36,"mat-icon",23),v["\u0275\u0275text"](37,"chat_bubble_outline"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275template"](38,J,2,0,"mat-icon",24),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](39,"span",19),v["\u0275\u0275text"](40,"Comments"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](41,"div",25),v["\u0275\u0275elementStart"](42,"div",26),v["\u0275\u0275elementStart"](43,"div",27),v["\u0275\u0275element"](44,"div",28),v["\u0275\u0275element"](45,"div",29),v["\u0275\u0275elementStart"](46,"div",30),v["\u0275\u0275elementStart"](47,"div",31),v["\u0275\u0275text"](48,"Legal Entity"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](49,"div",32),v["\u0275\u0275text"](50),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](51,"div",33),v["\u0275\u0275element"](52,"div",34),v["\u0275\u0275elementStart"](53,"div",30),v["\u0275\u0275elementStart"](54,"div",31),v["\u0275\u0275text"](55,"Customer"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](56,"div",32),v["\u0275\u0275text"](57),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275template"](58,W,10,7,"div",35),v["\u0275\u0275element"](59,"hr",14),v["\u0275\u0275template"](60,Z,10,7,"div",35),v["\u0275\u0275element"](61,"hr",14),v["\u0275\u0275template"](62,tt,10,7,"div",35),v["\u0275\u0275element"](63,"hr",14),v["\u0275\u0275template"](64,et,13,8,"div",35),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](65,"div",36),v["\u0275\u0275elementStart"](66,"div",37),v["\u0275\u0275elementStart"](67,"div",38),v["\u0275\u0275elementStart"](68,"div",3),v["\u0275\u0275elementStart"](69,"div",39),v["\u0275\u0275elementStart"](70,"div",40),v["\u0275\u0275elementStart"](71,"div",41),v["\u0275\u0275text"](72,"Invoice Value"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](73,"mat-icon",42),v["\u0275\u0275listener"]("click",(function(){return v["\u0275\u0275restoreView"](t),v["\u0275\u0275nextContext"]().changeCurrency()})),v["\u0275\u0275text"](74,"loop"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementContainerStart"](75),v["\u0275\u0275elementStart"](76,"div",43),v["\u0275\u0275text"](77),v["\u0275\u0275elementStart"](78,"mat-icon",44),v["\u0275\u0275text"](79,"info_outline"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275template"](80,lt,16,7,"ng-template",null,45,v["\u0275\u0275templateRefExtractor"]),v["\u0275\u0275elementContainerEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](82,"div",46),v["\u0275\u0275elementStart"](83,"div",47),v["\u0275\u0275elementStart"](84,"mat-icon",48),v["\u0275\u0275text"](85,"attach_money"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](86,"div",49),v["\u0275\u0275elementStart"](87,"div",50),v["\u0275\u0275text"](88,"Payment Received"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](89,"div",43),v["\u0275\u0275text"](90),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](91,"div",46),v["\u0275\u0275elementStart"](92,"div",47),v["\u0275\u0275elementStart"](93,"mat-icon",48),v["\u0275\u0275text"](94,"attach_money"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](95,"div",49),v["\u0275\u0275elementStart"](96,"div",50),v["\u0275\u0275text"](97,"Outstanding"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](98,"div",51),v["\u0275\u0275text"](99),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](100,"div",52),v["\u0275\u0275elementStart"](101,"div",53),v["\u0275\u0275elementStart"](102,"span",54),v["\u0275\u0275text"](103),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](104,"div",55),v["\u0275\u0275elementStart"](105,"div",56),v["\u0275\u0275text"](106,"Collection"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](107,"div",57),v["\u0275\u0275text"](108),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275element"](109,"hr",14),v["\u0275\u0275elementStart"](110,"div",58),v["\u0275\u0275elementStart"](111,"div",59),v["\u0275\u0275elementStart"](112,"div",3),v["\u0275\u0275elementStart"](113,"div",60),v["\u0275\u0275elementStart"](114,"div",61),v["\u0275\u0275elementStart"](115,"mat-icon",62),v["\u0275\u0275text"](116,"calendar_month"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](117,"div",63),v["\u0275\u0275elementStart"](118,"div",50),v["\u0275\u0275text"](119,"Raised On"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](120,"div",64),v["\u0275\u0275text"](121),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](122,"div",63),v["\u0275\u0275elementStart"](123,"div",50),v["\u0275\u0275text"](124,"Actual Due"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](125,"div",64),v["\u0275\u0275text"](126),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](127,"div",63),v["\u0275\u0275elementStart"](128,"div",50),v["\u0275\u0275text"](129,"Credit Period"),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementStart"](130,"div",64),v["\u0275\u0275text"](131),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275template"](132,ct,5,1,"div",65),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275template"](133,Pt,16,13,"div",66),v["\u0275\u0275template"](134,It,17,7,"div",67),v["\u0275\u0275template"](135,jt,15,9,"div",67),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"](),v["\u0275\u0275elementEnd"]()}if(2&t){const t=v["\u0275\u0275reference"](81),e=v["\u0275\u0275nextContext"]();v["\u0275\u0275advance"](13),v["\u0275\u0275textInterpolate1"](" ",null==e.invoiceInfo?null:e.invoiceInfo.invoice_no," "),v["\u0275\u0275advance"](13),v["\u0275\u0275property"]("ngIf",9==(null==e.invoiceInfo?null:e.invoiceInfo.status)&&e.isSendMailAuthorized),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("ngIf",9==(null==e.invoiceInfo?null:e.invoiceInfo.status)),v["\u0275\u0275advance"](11),v["\u0275\u0275property"]("ngIf",e.isCommentPresent),v["\u0275\u0275advance"](12),v["\u0275\u0275textInterpolate"](null==e.invoiceInfo?null:e.invoiceInfo.entity_name),v["\u0275\u0275advance"](7),v["\u0275\u0275textInterpolate"](null==e.invoiceInfo?null:e.invoiceInfo.customer_name),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("ngIf",null==e.fieldKeyConfig||null==e.fieldKeyConfig.milestone_name?null:e.fieldKeyConfig.milestone_name.is_active),v["\u0275\u0275advance"](2),v["\u0275\u0275property"]("ngIf",null==e.fieldKeyConfig||null==e.fieldKeyConfig.cost_center?null:e.fieldKeyConfig.cost_center.is_active),v["\u0275\u0275advance"](2),v["\u0275\u0275property"]("ngIf",null==e.fieldKeyConfig||null==e.fieldKeyConfig.service_type?null:e.fieldKeyConfig.service_type.is_active),v["\u0275\u0275advance"](2),v["\u0275\u0275property"]("ngIf",null==e.fieldKeyConfig||null==e.fieldKeyConfig.amount_to_be_Collected?null:e.fieldKeyConfig.amount_to_be_Collected.is_active),v["\u0275\u0275advance"](13),v["\u0275\u0275textInterpolate2"]("",e.currentCurrency," ",e.fixNumberOnUI(e.invoiceValue)," "),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("tooltip",t)("max-width",1200)("options",v["\u0275\u0275pureFunction0"](30,Kt)),v["\u0275\u0275advance"](12),v["\u0275\u0275textInterpolate2"]("",e.currentCurrency," ",e.fixNumberOnUI(e.receivedValue),""),v["\u0275\u0275advance"](9),v["\u0275\u0275textInterpolate2"]("",e.currentCurrency," ",e.fixNumberOnUI(e.collectedValue),""),v["\u0275\u0275advance"](2),v["\u0275\u0275styleProp"]("background",e.conicGradient()),v["\u0275\u0275advance"](2),v["\u0275\u0275textInterpolate1"](" ",e.percentageCompleted?e.percentageCompleted:0,"% "),v["\u0275\u0275advance"](5),v["\u0275\u0275textInterpolate1"](" ",e.getStatus(),""),v["\u0275\u0275advance"](13),v["\u0275\u0275textInterpolate"](null==e.invoiceInfo?null:e.invoiceInfo.invoice_raised_on),v["\u0275\u0275advance"](5),v["\u0275\u0275textInterpolate"](null==e.invoiceInfo?null:e.invoiceInfo.expected_on),v["\u0275\u0275advance"](5),v["\u0275\u0275textInterpolate1"]("",null!=e.invoiceInfo&&e.invoiceInfo.CP?null==e.invoiceInfo?null:e.invoiceInfo.CP:"0"," Days"),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("ngIf",9==(null==e.invoiceInfo?null:e.invoiceInfo.status)||10==(null==e.invoiceInfo?null:e.invoiceInfo.status)),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("ngIf","payment"==e.currentScreen&&11!=(null==e.invoiceInfo?null:e.invoiceInfo.status)),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("ngIf","history"==e.currentScreen&&0!=(null==e.paymentInfo?null:e.paymentInfo.length)&&9!=(null==e.invoiceInfo?null:e.invoiceInfo.status)),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("ngIf","documents"==e.currentScreen)}}function Lt(t,e){1&t&&v["\u0275\u0275element"](0,"ngx-spinner",195)}const Yt=[{path:"",component:(()=>{class t{constructor(t,e,a,o,r,m,d,s,g,u,b,C,h,v,_,P,M,O,x){this.dialog=t,this.cdr=e,this.fb=a,this.route=o,this.invoiceService=r,this.router=m,this.utilityService=d,this._snackBar=s,this.tenantService=g,this.invoiceCommonService=u,this.spinnerService=b,this._login=C,this.invoiceHomeService=h,this._graphService=v,this.mailUtilityService=_,this.dunningService=P,this._sharedService=M,this.matDateFormats=O,this.dateAdapter=x,this.num_activities=0,this.initialSelectedIndex=0,this.invoicePDfDataFinal=[],this.stepNo=1,this.lastStepperVisible=!1,this.activityCompletion=[],this.submitButtonDisabled=!1,this.submitSpinnerVisible=!1,this.showAttachment=!1,this.contextId=null,this.isCollectionShow=!0,this.showBankPaymentDetails=!1,this.fieldKeyConfig={},this.URL="/api/exPrimary/uploadObjectFromDevice",this.paymentContextId=null,this.mailOpened=!1,this.selectedFiles=[],this.milestoneFiles=[],this.itemsPerPage=[5,10,15,20],this.isDialogOpened=!1,this.show=!1,this.showPaymentRemittance=!1,this.paymentHistoryFiles=[],this.commentsInput={application_id:null,unique_id_1:"",unique_id_2:"",application_name:null,title:""},this.generateAndStorePDFInS3=!1,this.isCommentPresent=!1,this.openAnnexure=()=>{this.invoiceService.viewInvoice(this.billingId).subscribe(t=>{let e=[];if(e.push(t),e&&e.length>0){let e=t;this.invoiceService.viewAnnexure(this.billingId,this.serviceTypeId).subscribe(t=>Object(i.c)(this,void 0,void 0,(function*(){if(null!=t){console.log(t);let a=[];a.push({serviceTypeId:t.serviceTypeId||this.serviceTypeId,data:t.data,invoiceNo:l.pluck(e,"invoiceNo").reverse(),dateFormats:this.dateFormats,invoiceTenantDetails:this.invoiceTenantDetails});const{ViewAnnexureComponent:o}=yield Promise.all([n.e(53),n.e(775)]).then(n.bind(null,"YyJd"));this.dialog.open(o,{width:"65%",height:"100%",autoFocus:!1,maxWidth:"90vw",data:a})}else this.utilityService.showMessage("No Data found for annexure","Dismiss")})))}else this.utilityService.showMessage("No Data found","Dismiss")},t=>{console.error(t)})},this.getInvoiceTenantRoleCheckDetail=(t,e)=>new Promise((n,a)=>{this.invoiceCommonService.getInvoiceTenantCheckDetail(t,e).subscribe(t=>{n(t)},t=>{console.error(t),a(t)})}),this.containsSpecialChars=t=>/[`!@#$%^&*()_+\-=\[\]{};':"\\|,<>\/?~]/.test(t),this.retrieveUploadedFiles=(t,e,n)=>{this._sharedService.retrieveUploadedObjects(t,e).pipe(Object(p.a)("data")).subscribe(t=>{const e=t.map(t=>Object.assign(Object.assign({},t),{checked:!0}));if("payment"==this.currentScreen&&"payment"==n)this.selectedFiles.push(...e);else if("documents"==this.currentScreen&&"documents"==n)this.milestoneFiles.push(...e),this.dataSource=new f.l(this.milestoneFiles),this.dataSource.paginator=this.paginator,this.sortData(),this.cdr.detectChanges();else if("history"==this.currentScreen&&"history"==n)return e},t=>{console.error(t)})},this.url=this.route._routerState.snapshot.url,this.activityForm=this.fb.group({tallyRef:["",c.H.required],amountToBeCollected:[[""],c.H.required],amountReceived:["",[c.H.required,function(t){return t.value<=0?{validAmount:!1}:null}]],receivedDate:["",c.H.required],invoiceRaisedCurrency:["",c.H.required],bankRefNo:[""],paymentReceivedBank:[""],activities:this.fb.array([]),note:""}),this.dataSource=new f.l(this.milestoneFiles)}viewInvoice(){var t;return Object(i.c)(this,void 0,void 0,(function*(){if(this.generateAndStorePDFInS3){let e="kebs-invoices",n=yield this.invoiceHomeService.retrieveUploadedObjects(e,this.invoiceInfo.milestone_id).toPromise();n&&(null===(t=n.data)||void 0===t?void 0:t.length)>0?this.viewFile(n.data[0]):this._snackBar.open("Kindly contact KEBS team to resolve","Close",{duration:5e3})}else this.invoiceService.viewInvoice(this.billingId).subscribe(t=>Object(i.c)(this,void 0,void 0,(function*(){let e=[];if(e.push(t),e&&e.length>0){console.log(t);const{ViewInvoiceComponent:e}=yield Promise.all([n.e(53),n.e(114),n.e(176),n.e(175)]).then(n.bind(null,"ZCtA"));this.dialog.open(e,{width:"65%",height:"100%",autoFocus:!1,maxWidth:"90vw",data:{pdfData:t,billingId:this.billingId,invoiceTenantDetails:this.invoiceTenantDetails,paymentTermsList:this.paymentTermsList}})}else this.utilityService.showMessage("No Data found","Dismiss")})),t=>{console.error(t)})}))}submitPayment(){if(this.showPaymentRemittance&&!this.activityForm.value.note&&0===this.selectedFiles.length)return void d.a.fire({icon:"info",title:"Please attach document(s) or notes to complete the payment!",showConfirmButton:!0});this.submitButtonDisabled=!0,this.submitSpinnerVisible=!0;let t=this.invoiceInfo.amount_to_be_Collected-this.parseValue(this.activityForm.value.amountReceived),e=this.utilityService.convertToLocalTime(this.activityForm.value.receivedDate),n=this.activityForm.value.tallyRef,a=this.activityForm.value.bankRefNo,o=this.activityForm.value.paymentReceivedBank,i=this.parseValue(this.activityForm.value.amountReceived);this.invoiceService.savepaymentInfo(i,e,this.billingId,n,this.milestoneId,this.itemId,t,a,o,this.paymentContextId,this.activityForm.value.note).subscribe(t=>{"S"==t.messType&&this.invoiceService.getInvoiceInformationInBilledScreen(this.billingId).subscribe(t=>{console.log(t),this.submitButtonDisabled=!1,this.invoiceInfo=t,this.activityForm.patchValue({tallyRef:"",amountReceived:"",receivedDate:"",bankRefNo:"",amountToBeCollected:this.invoiceInfo.amount_to_be_Collected}),0==this.activityForm.value.amountToBeCollected?(d.a.fire({icon:"success",title:"Payment for this milestone recorded succesfully !",showConfirmButton:!0}),this.router.navigateByUrl("/main/invoice-v1.2/invoicelist/1")):(d.a.fire({icon:"success",title:"Partial payment recorded succesfully !",showConfirmButton:!0}),this.router.navigateByUrl("/main/invoice-v1.2/invoicelist/1"),this.submitSpinnerVisible=!1,this.getPaymentAndActivityRelatedInfo())},t=>{console.error(t)})},t=>{this._snackBar.open(t.error.messText,"Dismiss",{duration:2e3}),this.submitButtonDisabled=!1,console.error(t)})}savePlannedCollectionAmount(t){this.plannedCollectionAmount=t,console.log(this.projectId+"**"+this.currencyId+"**"+this.plannedCollectionAmount+"***"+this.invoiceRaisedDate+"***"+this.billingId),this.invoiceService.updatePlannedCollection(this.projectId,this.currencyId,this.plannedCollectionAmount,this.invoiceRaisedDate,this.billingId).subscribe(t=>{"Success"==t&&this._snackBar.open("updated successfully!","close",{duration:2e3})})}get ActivityArray(){return this.activityForm.get("activities")}getDate(t){var e,n;let a=(null===(e=this.dateFormats)||void 0===e?void 0:e.invoice_general_date_format)?null===(n=this.dateFormats)||void 0===n?void 0:n.invoice_general_date_format:"DD-MM-YYYY";return this.invoiceDateFormat=a,s(t).format(a)}completeActivity(t){this.invoiceService.completePaymentActivity(this.activityForm.value.activities[t].activityID).subscribe(e=>{console.log(e),"Payment Activity completed"==e.message?(d.a.fire({customClass:{title:"title-class",confirmButton:"confirm-button-class"},type:"success",title:e.message}),this.activityCompletion[t]=1,this.stepper.next()):d.a.fire({customClass:{title:"title-class",confirmButton:"confirm-button-class"},type:"error",title:"Oops...",text:"Activity Not Completed"})},t=>{console.error(t)})}setActivityForm(t,e){this.activityForm.patchValue({amountToBeCollected:e.amount_to_be_Collected,invoiceRaisedCurrency:e.currency});let n=this.activityForm.controls.activities;console.log(t),t.forEach((t,e)=>{n.push(this.fb.group({activityName:t.activity_name,customerRespPerson:t.customer_responsible_person,customerPersonsRole:t.customer_person_role,customerEmail:t.customer_person_email,numberOfDays:t.no_of_days,KaarResponsiblePerson:t.assigned_to_id,startDate:null!=t.start_date?this.utilityService.convertToLocalTime(t.start_date):null,plannedEndDate:null!=t.due_date?this.utilityService.convertToLocalTime(t.due_date):null,completionDate:null!=t.end_date?this.utilityService.convertToLocalTime(t.end_date):null,activityID:t.payment_activity_id})),this.activityCompletion.push(t.is_completed)})}saveActivity(t){this.invoiceService.saveActivityDetails(this.activityForm.value.activities[t],this.milestoneId).subscribe(t=>{console.log(t),d.a.fire("Activity Details Saved!"==t.message?{customClass:{title:"title-class",confirmButton:"confirm-button-class"},type:"success",title:t.message}:{customClass:{title:"title-class",confirmButton:"confirm-button-class"},type:"error",title:"Oops...",text:"Activity not Saved"})},t=>{console.error(t)})}ngOnInit(){var t,e,n,a;return Object(i.c)(this,void 0,void 0,(function*(){this.calculateDynamicContentHeight(),this.currentScreen="payment",this.title="ADD PAYMENT",this.percentageCompleted=80,this.spinnerService.show(),this.dateFormats=yield this.getTenantDateFormats();let o=(null===(t=this.dateFormats)||void 0===t?void 0:t.invoice_general_date_format)?null===(e=this.dateFormats)||void 0===e?void 0:e.invoice_general_date_format:"DD-MM-YY";Object.assign(this.matDateFormats,{parse:{dateInput:o},display:{dateInput:o,monthYearLabel:o,dateA11yLabel:o,monthYearA11yLabel:o}}),this.fieldConfig=yield this.formFieldConfig(),this.paymentFieldConfig=yield this.paymentFormFieldConfig(),this.fieldConfig&&this.fieldConfig.forEach(t=>{this.fieldKeyConfig[t.key_name]=t}),this.getPaymentTerms(),this.itemId=this.route.snapshot.params.itemId,this.milestoneId=this.route.snapshot.params.milestoneId,this.billingId=this.route.snapshot.params.billingId,this.projectId=this.route.snapshot.params.projectId,this.fromScreen=this.route.snapshot.params.itemType,this.today=new Date,this.selectedFiles=[],this.milestoneFiles=[],this.destinationBucket="kebs-invoices",this.routingKey="invoices",this.paymentContextId=`${this.milestoneId}-${this.create_UUID()}`,yield this.getInvoiceInfoDisplayedInleftSide(),this.dataSource=new f.l(this.milestoneFiles);let i=yield this.getSendMailAuthorization();this.isSendMailAuthorized=i,this.getPaymentAndActivityRelatedInfo(),this.bankDetails=yield this.invoiceService.getBankDetails(this.legalEntityId),yield this.getTenantInfo(),this.attachmentRetrievalDetails=[{destinationBucket:"kebs-invoices",routingKey:"invoices",contextId:this.contextId,type:"documents"},{destinationBucket:"kebs-project-s3",routingKey:"project",contextId:`PRJ_${this.milestoneId}${this.ganttId}`,type:"documents"}];for(let t of this.attachmentRetrievalDetails)t.contextId&&(this.milestoneFiles=[],this.retrieveUploadedFiles(t.destinationBucket,t.contextId,t.type));this.uploader=new g.d({url:this.URL,authToken:"Bearer "+this._login.getToken(),disableMultipart:!1,headers:[{name:"context-id",value:this.paymentContextId},{name:"routing-key",value:this.routingKey},{name:"bucket-name",value:this.destinationBucket}],maxFileSize:********}),this.milestoneUploader=new g.d({url:this.URL,authToken:"Bearer "+this._login.getToken(),disableMultipart:!1,headers:[{name:"context-id",value:this.contextId},{name:"routing-key",value:this.routingKey},{name:"bucket-name",value:this.destinationBucket}],maxFileSize:********}),this.detectUploadChanges();let r=(null===(n=this.dateFormats)||void 0===n?void 0:n.invoice_general_date_format)?null===(a=this.dateFormats)||void 0===a?void 0:a.invoice_general_date_format:"DD-MM-YYYY";this.invoiceDateFormat=r,this.getInvoiceConfig()}))}getinvoiceNo(t){let e=[];t.forEach((n,a)=>{null!=n&&e.push(n),t.length-1==a&&(this.invoiceNoArray=e)})}updatePlannedCollection(t){console.log(t)}getPaymentAndActivityRelatedInfo(){this.invoiceService.getPaymentInformation(this.billingId).subscribe(t=>{this.paymentInfo=t,this.retrievePaymentHistoryFiles(),this.getValue(this.paymentInfo),this.calculateAmountToBeCollected(this.paymentInfo),console.log(t)},t=>{console.error(t)})}launchProjectFromInvoice(t,e,n,a){console.log(window.location.origin);let o=window.location.origin+"/main/project-management/"+t+"/"+this.encodeURIComponent(e)+"/"+n+"/"+this.encodeURIComponent(a)+"/overview";window.open(o)}encodeURIComponent(t){return encodeURIComponent(t).replace(/[!'()*]/g,(function(t){return"%"+t.charCodeAt(0).toString(16)}))}checkIfStepComplete(t){for(let e=0;e<t.length;e++)if("1"!=t[e].is_completed){setTimeout(()=>{this.initialSelectedIndex=e,console.log(this.initialSelectedIndex)},0);break}}getInvoiceInfoDisplayedInleftSide(){var t;return Object(i.c)(this,void 0,void 0,(function*(){try{const e=yield this.invoiceService.getInvoiceInformationInBilledScreen(this.billingId).toPromise();console.log(e),this.invoiceInfo=e,this.itemName=e.item_name,this.projectName=e.project_name,this.serviceTypeId=e.service_type_id,this.currencyId=e.currencyId,this.invoiceRaisedDate=e.invoice_raised_on,this.legalEntityId=e.legal_entity_id,this.ganttId=e.gantt_id;let n=e.milestone_id;this.contextId=null!=e.context_id?e.context_id:`INV_${n}${this.ganttId}`,11==(null===(t=this.invoiceInfo)||void 0===t?void 0:t.status)&&(this.currentScreen="history",this.title="PAYMENT HISTORY"),this.initialCurrencyValue();let a=s(e.expected_date,"DD-MM-YY").startOf("day"),o=s().startOf("day"),i=a.diff(o,"days");e.expected_days=i,this.isCommentPresent=e.is_comment_present;const r=yield this.invoiceService.getPaymentActivityListForMilestone(this.milestoneId).toPromise();this.show=!0,this.spinnerService.hide(),this.activities=r,this.setActivityForm(r,this.invoiceInfo),this.checkIfStepComplete(r),this.lastStepperVisible=!0,this.showAttachment=!0}catch(e){console.error(e),this.spinnerService.hide()}}))}ngAfterViewInit(){this.cdr.detectChanges(),this.milestoneFiles.sort=this.sort,this.dataSource.sort=this.sort}calculateAmountToBeCollected(t){console.log(t)}ngOnDestroy(){}back(){"billed"==this.fromScreen?this.router.navigateByUrl("/main/invoice-v1.2/invoicelist/1"):"PP"==this.fromScreen?this.router.navigateByUrl("/main/invoice-v1.2/invoicelist/2"):"PR"==this.fromScreen&&this.router.navigateByUrl("/main/invoice-v1.2/invoicelist/3")}getTenantInfo(){return Object(i.c)(this,void 0,void 0,(function*(){this.tenantService.getTenantInfo().then(t=>Object(i.c)(this,void 0,void 0,(function*(){var e,n;let a=t;this.invoiceTenantDetails=yield this.getInvoiceTenantRoleCheckDetail(a.tenant_name,"Role"),this.isCollectionShow=this.invoiceTenantDetails.data.is_collection_show_in_kebs,this.showBankPaymentDetails=this.invoiceTenantDetails.data.is_show_bank_payment_details,this.showPaymentRemittance=!!(null===(e=this.invoiceTenantDetails.data)||void 0===e?void 0:e.is_to_show_payment_remittance)&&(null===(n=this.invoiceTenantDetails.data)||void 0===n?void 0:n.is_to_show_payment_remittance)})),t=>{console.log(t)})}))}getPaymentTerms(){this.invoiceCommonService.getPaymentTerms().then(t=>{for(let e of t)e.id=e.name;this.paymentTermsList=t},t=>{console.log(t)})}changeInAttachment(){this.contextId&&this.invoiceService.updateInvoiceAttachments(this.contextId,this.billingId).subscribe(t=>{console.log(t)},t=>{console.log(t)})}getTenantDateFormats(){return new Promise((t,e)=>{this.invoiceCommonService.getTenantDateFormats().subscribe(e=>{t(e.data)},e=>{console.log(e),t(e)})})}formFieldConfig(){return new Promise((t,e)=>{this.invoiceService.FormFieldConfig().subscribe(e=>{t(e.data)},t=>{console.log(t),e(t)})})}paymentFormFieldConfig(){return new Promise((t,e)=>{this.invoiceService.paymentFormFieldConfig().subscribe(e=>{t(e.data)},t=>{console.log(t),e(t)})})}triggerFileInput(){this.fileInput.nativeElement.click()}onFileSelected(t){const e=t.target.files;for(let n=0;n<e.length;n++)this.selectedFiles.push(e[n])}onMilestoneFileSelected(t){const e=t.target.files;for(let n=0;n<e.length;n++)this.milestoneFiles=this.milestoneFiles.push(e[n]);this.dataSource=new f.l(this.milestoneFiles),this.dataSource.paginator=this.paginator,this.cdr.detectChanges()}sortData(){this.dataSource.sort=this.sort}detectUploadChanges(){this.uploader.onProgressItem=t=>{},this.uploader.onCompleteItem=(t,e,n,a)=>{this.uploader.removeFromQueue(t);let o=JSON.parse(e).data;o.isUploaded=!0,this.selectedFiles.push(o)},this.uploader.onAfterAddingFile=t=>{{let e="";this.containsSpecialChars(t.file.name)?(e=t.file.name.replace(/[^a-zA-Z0-9. ]/g,"_"),t.file.name.split(".").pop(),this.uploader.uploadItem(t)):(e=t.file.name,t.file.name.split(".").pop(),this.uploader.uploadItem(t))}},this.milestoneUploader.onProgressItem=t=>{},this.milestoneUploader.onCompleteItem=(t,e,n,a)=>{this.milestoneUploader.removeFromQueue(t);let o=JSON.parse(e).data;o.isUploaded=!0,this.milestoneFiles.push(o),this.dataSource=new f.l(this.milestoneFiles),this.dataSource.paginator=this.paginator,this.sortData(),this.cdr.detectChanges()},this.milestoneUploader.onAfterAddingFile=t=>{{let e="";this.containsSpecialChars(t.file.name)?(e=t.file.name.replace(/[^a-zA-Z0-9. ]/g,"_"),t.file.name.split(".").pop(),this.milestoneUploader.uploadItem(t)):(e=t.file.name,t.file.name.split(".").pop(),this.milestoneUploader.uploadItem(t))}}}conicGradient(){this.percentageCompleted=this.receivedValue&&this.invoiceValue&&"-"!==this.receivedValue&&"-"!==this.invoiceValue?Math.round(this.receivedValue/this.invoiceValue*100):0;let t=this.percentageCompleted;return t<0&&(t=0),`conic-gradient(#52C41A ${t/100*360}deg, #ededed 0deg)`}changeCurrency(){var t,e,n,a,o,i,r,l,c;let m=null===(t=this.invoiceInfo)||void 0===t?void 0:t.total_invoice_value,d=null===(e=this.invoiceInfo)||void 0===e?void 0:e.total_amount_received,s=null===(n=this.invoiceInfo)||void 0===n?void 0:n.Balance_amount;m.length>1&&(this.currencyIndex=++this.currencyIndex%m.length),this.invoiceValue=null===(a=m[this.currencyIndex])||void 0===a?void 0:a.value,this.currentCurrency=null===(o=m[this.currencyIndex])||void 0===o?void 0:o.currency_code,this.receivedValue=null!==(r=null===(i=null==d?void 0:d.find(t=>t.currency_code===this.currentCurrency))||void 0===i?void 0:i.value)&&void 0!==r?r:0,this.collectedValue=null!==(c=null===(l=null==s?void 0:s.find(t=>t.currency_code===this.currentCurrency))||void 0===l?void 0:l.value)&&void 0!==c?c:0,this.changeSummaryValue()}changeSummaryValue(){var t,e,n,a,o,i,r,l,c,m,d,s,g,p,u,b;let f=null===(t=this.invoiceInfo)||void 0===t?void 0:t.total_invoice_value,C=null===(e=this.invoiceInfo)||void 0===e?void 0:e.subtotal_value,h=null===(n=this.invoiceInfo)||void 0===n?void 0:n.tax_amount,v=null===(a=this.invoiceInfo)||void 0===a?void 0:a.tcs_value,_=null===(o=this.invoiceInfo)||void 0===o?void 0:o.tds_value,P=null===(i=this.invoiceInfo)||void 0===i?void 0:i.discount_value,M=null===(r=this.invoiceInfo)||void 0===r?void 0:r.retention_value,O=null===(l=this.invoiceInfo)||void 0===l?void 0:l.currency;if(O==this.currentCurrency)this.subTotalValue=null!==(m=null===(c=null==C?void 0:C.find(t=>t.currency_code===this.currentCurrency))||void 0===c?void 0:c.value)&&void 0!==m?m:0,this.tdsValue=_,this.tcsValue=v,this.taxAmount=h,this.discountAmount=P,this.retentionAmount=M;else{let t=null!==(s=null===(d=null==f?void 0:f.find(t=>t.currency_code===O))||void 0===d?void 0:d.value)&&void 0!==s?s:0,e=null!==(p=null===(g=null==f?void 0:f.find(t=>t.currency_code===this.currentCurrency))||void 0===g?void 0:g.value)&&void 0!==p?p:0;if(t&&e){let n=e/t;console.log(n),n&&(this.subTotalValue=null!==(b=null===(u=null==C?void 0:C.find(t=>t.currency_code===this.currentCurrency))||void 0===u?void 0:u.value)&&void 0!==b?b:0,this.tdsValue=_*n,this.tcsValue=v*n,this.taxAmount=h*n,this.discountAmount=P*n,this.retentionAmount=M*n)}}}initialCurrencyValue(){var t,e,n,a,o,i,r,l,c,m,d,s,g;let p=null===(t=this.invoiceInfo)||void 0===t?void 0:t.total_invoice_value,u=null===(e=this.invoiceInfo)||void 0===e?void 0:e.currency;this.currentCurrency=null===(n=this.invoiceInfo)||void 0===n?void 0:n.currency;let b=null===(a=this.invoiceInfo)||void 0===a?void 0:a.total_amount_received,f=null===(o=this.invoiceInfo)||void 0===o?void 0:o.Balance_amount;this.currencyIndex=null==p?void 0:p.findIndex(t=>t.currency_code===u),this.invoiceValue=null===(i=p[this.currencyIndex])||void 0===i?void 0:i.value,this.invoiceValue=-1!==this.currencyIndex&&p?null!==(l=null===(r=p[this.currencyIndex])||void 0===r?void 0:r.value)&&void 0!==l?l:0:"-",this.invoiceValueBillingCurrency=null===(c=p[this.currencyIndex])||void 0===c?void 0:c.value,this.receivedValue=null!==(d=null===(m=null==b?void 0:b.find(t=>t.currency_code===this.currentCurrency))||void 0===m?void 0:m.value)&&void 0!==d?d:"-",this.collectedValue=null!==(g=null===(s=null==f?void 0:f.find(t=>t.currency_code===this.currentCurrency))||void 0===s?void 0:s.value)&&void 0!==g?g:"-",this.changeSummaryValue()}fixNumberOnUI(t){if(0==t||"0"==t||""==t||"-"==t)return"0.00";let e=parseFloat(t);return"INR"==this.currentCurrency?new Intl.NumberFormat("en-IN",{minimumFractionDigits:2,maximumFractionDigits:2}).format(Number(e)):new Intl.NumberFormat("en-US",{minimumFractionDigits:2,maximumFractionDigits:2}).format(Number(e))}fixNumber(t){if(0==t||"0"==t||""==t||"-"==t)return"0.00";let e=parseFloat(t);return"INR"==this.currentCurrency?new Intl.NumberFormat("en-IN",{minimumFractionDigits:2,maximumFractionDigits:2}).format(Number(e)):new Intl.NumberFormat("en-US",{minimumFractionDigits:2,maximumFractionDigits:2}).format(Number(e))}navigateScreen(t){if(this.currentScreen=t,"history"==t&&(this.getValue(this.paymentInfo),this.title="PAYMENT HISTORY"),"documents"==t){this.title="DOCUMENTS";for(let t of this.attachmentRetrievalDetails)t.contextId&&(this.milestoneFiles=[],this.retrieveUploadedFiles(t.destinationBucket,t.contextId,t.type))}}backToPayment(){this.currentScreen="payment",this.title="ADD PAYMENT"}backToHistory(t){11==t?(this.currentScreen="history",this.title="PAYMENT HISTORY"):(this.currentScreen="payment",this.title="ADD PAYMENT")}markAsSent(t){return Object(i.c)(this,void 0,void 0,(function*(){d.a.fire({customClass:{title:"title-class",confirmButton:"confirm-button-class",cancelButton:"confirm-button-class"},title:"Are you sure want to mark this invoice as sent?",type:"warning",showCancelButton:!0,confirmButtonColor:"#3085d6",cancelButtonColor:"#d33",confirmButtonText:"Yes"}).then(e=>{if(e.value)return new Promise((e,n)=>{this.invoiceHomeService.markInvoiceAsSent(t).subscribe(t=>{this.back(),d.a.fire({title:"Invoice marked as sent successfully !",icon:"success",showConfirmButton:!0})},t=>{d.a.fire({title:"Failed to mark invoice as sent! !",icon:"success",showConfirmButton:!0})})})})}))}sendMail(t,e,a){var o;return Object(i.c)(this,void 0,void 0,(function*(){if(this.mailOpened)return;let i,r;this.mailOpened=!0,this.initMailBox();let c=yield this.invoiceHomeService.getMailTemplate();i=yield this.invoiceHomeService.getCustomerBillingDetails(t),this.mailBillingDetails=i,this.mailTemplate=c,this.dateFormats=yield this.getTenantDateFormats();let m=this.invoiceHomeService.formatDataForMailComponent(i,c,this.dateFormats);if(this.mailUtilityService.mUtilityData.newMailTemplateData.push(m),this.mailUtilityService.mUtilityData.authorizedMailSenders=m.authorizedMailSenders,this.mailUtilityService.mUtilityData.saveRecipientMailIdType.uniqueId=m.uniqueId,this.mailUtilityService.mUtilityData.formatTableForTemplate=!0,this.mailUtilityService.mUtilityData.isTableLayoutAuto=!!(c&&c.length>0&&c[0].table_auto_structure)&&1==(null===(o=c[0])||void 0===o?void 0:o.table_auto_structure),this.mailUtilityService.mUtilityData.currentMailMode={mode:"create"},(null==i?void 0:i.send_mail_from_common_id)?(this.mailUtilityService.mUtilityData.fromCommonMailId=!0,this.mailUtilityService.mUtilityData.currentUserMailId=null==i?void 0:i.common_from_mail_id,this.mailUtilityService.mUtilityData.sendMailAPIDetails={url:"/api/invoice/v2/sendMailFromCommonId",jwtToken:this._login.getJwtToken()}):(this.mailUtilityService.mUtilityData.o365Token={token:yield this._graphService.getO365Token()},this.mailUtilityService.mUtilityData.fromCommonMailId=!1),this.mailUtilityService.mUtilityData.saveHistoryInTable=!0,this.mailUtilityService.mUtilityData.saveHistoryInKebsApiData={url:"/api/invoice/saveMailHistoryInTable",jwtToken:this._login.getJwtToken(),paramsArr:[{billingId:t}]},this.mailUtilityService.mUtilityData.initiateNewMailTemplateData={url:"/api/invoice/refreshMailTemplate",jwtToken:this._login.getJwtToken(),paramsArr:[{billingId:t}]},this.mailUtilityService.mailUiData.showHistoryButton=!0,this.mailUtilityService.mUtilityData.retrieveHistoryFromApi={url:"/api/invoice/retrieveHistoryFromTable",jwtToken:this._login.getJwtToken(),paramsArr:[{billingId:t}]},c&&c.length>0&&c[0].is_to_show_spoc&&(this.mailUtilityService.mailUiData.showSpocName=!0),c&&c.length>0&&c[0].is_to_show_title){this.mailUtilityService.mailUiData.showTitle=!0;let t=l.uniq(l.pluck(null==i?void 0:i.result,"customerName"));t&&(this.mailUtilityService.mailUiData.title="Mail to "+t)}let d=this.invoiceTenantDetails.data;d&&d.hasOwnProperty("is_mail_auto_attachment_applicable")&&d.is_mail_auto_attachment_applicable&&(this.mailUtilityService.mUtilityData.autoAttachmentApplicable=!0,this.mailUtilityService.mUtilityData.destinationBucket="kebs-invoices-mail-attachments",this.mailUtilityService.mUtilityData.routingKey="invoices-mail-attachments",this.mailUtilityService.mUtilityData.contextId=`INVM_${e}${a}`,this.mailUtilityService.mUtilityData.attachmentRetrievalDetails=[{destinationBucket:"kebs-invoices",routingKey:"invoice-pdf",contextId:"INV_"+e},{destinationBucket:"kebs-invoices",routingKey:"invoices",contextId:`INV_${e}${a}`},{destinationBucket:"kebs-invoices-mail-attachments",routingKey:"invoices-mail-attachments",contextId:`INVM_${e}${a}`},{destinationBucket:"kebs-invoices",routingKey:"invoice-template-pdf",contextId:e}],r=yield this.invoiceHomeService.getAttachmentDetails(e),"S"==r.messType&&(r=r.data,this.mailUtilityService.mUtilityData.attachmentRetrievalDetails.push(null==r?void 0:r.AttachmentsDetails)));const{ViewMailComponent:s}=yield n.e(900).then(n.bind(null,"gzal"));this.dialog.open(s,{width:"96%",height:"97%",maxWidth:"100vw",maxHeight:"100vw",data:{},disableClose:!0}).afterClosed().subscribe(t=>{this.mailUtilityService.resetMailData(),this.mailOpened=!1})}))}initMailBox(){let t=this._login.getProfile().profile;this.mailUtilityService.mUtilityData.saveRecipientMailIds=this.saveMailIds.bind(this),this.mailUtilityService.mUtilityData.applicationId=10,this.mailUtilityService.mUtilityData.isCcRecipientFieldHasSaveButton=!0,this.mailUtilityService.mUtilityData.isBccRecipientFieldHasSaveButton=!0,this.mailUtilityService.mUtilityData.isToRecipientFieldHasSaveButton=!0,this.mailUtilityService.mUtilityData.hasInitiateNewMailTemplate=!0,this.mailUtilityService.mUtilityData.formatTableForTemplate=!0,this.mailUtilityService.mUtilityData.currentUserMailId=t.email,this.mailUtilityService.mUtilityData.saveSpocName=this.saveSpocNameForCustomer.bind(this)}saveMailIds(){let t=this.mailUtilityService.mUtilityData.saveRecipientMailIdType.type;this.mailUtilityService.sendSweetAlert(`Are you sure you want to save the ${"toMailId"===t?"To":"ccMailId"===t?"CC":"bccMailIds"===t?"Bcc":""} recipients against customer?`,"").then(e=>{if(e.value){let e=this.mailUtilityService.mUtilityData.saveRecipientMailIdType.mailFields,n=this.mailUtilityService.mUtilityData.saveRecipientMailIdType.uniqueId;console.log("customer",n);let a=[];"toMailId"==t?e.value.toRecipients.length>0?l.each(e.value.toRecipients,t=>{a.push(t.name)}):a=[]:"ccMailId"==t?e.value.ccRecipients.length>0?l.each(e.value.ccRecipients,t=>{a.push(t.name)}):a=[]:"bccMailIds"==t&&(e.value.bccRecipients.length>0?l.each(e.value.bccRecipients,t=>{a.push(t.name)}):a=[]),this.dunningService.saveDunningRecipientEmail(t,a,n).subscribe(t=>{"S"==t.messType?this._snackBar.open(t.userMess,"Dismiss",{duration:2e3}):console.log(t)},t=>{console.log(t)})}})}saveSpocNameForCustomer(){this.mailUtilityService.mUtilityData.spocSaveEnabled=!0;let t=this.mailUtilityService.mUtilityData.selectedNewMailTemplateData.uniqueId,e=this.mailUtilityService.mailUiData.mailInputFields.value.spocName;this.mailBillingDetails.spoc_name=this.mailUtilityService.mailUiData.mailInputFields.value.spocName,this.dunningService.saveDunningSpocName(e,t).subscribe(t=>{"S"==t.messType?this._snackBar.open(t.userMess,"Dismiss",{duration:2e3}):console.log(t)},t=>{console.log(t)});let n=this.mailUtilityService.mailUiData.mailInputFields.value.body;n=n.replace(/<a[^>]*>/,"<a>");const a=document.createElement("div");a.innerHTML=n;const o=a.querySelector("p > a");o&&(o.textContent=this.mailUtilityService.mailUiData.mailInputFields.value.spocName);const i=a.innerHTML;let r=this.invoiceHomeService.formatDataForMailComponent(this.mailBillingDetails,this.mailTemplate,this.dateFormats);r.mailBody=i,this.mailUtilityService.mUtilityData.newMailTemplateData=[],this.mailUtilityService.mUtilityData.newMailTemplateData.push(r),this.mailUtilityService.mUtilityData.currentMailMode={mode:"not selected"},this.mailUtilityService.initiateNewMail()}getValue(t){var e,n,a,o,i,r,l,c;let m=null===(e=this.invoiceInfo)||void 0===e?void 0:e.currency;this.paymentHistoryCurrency=null===(n=this.invoiceInfo)||void 0===n?void 0:n.currency,this.paymentHistoryCurrencyIndex=null===(a=t[0].amount_received)||void 0===a?void 0:a.findIndex(t=>t.currency_code===m);for(let d of t){let t,e=d.amount_received;(null==e?void 0:e.length)>1&&(this.paymentHistoryCurrencyIndex=this.paymentHistoryCurrencyIndex%(null==e?void 0:e.length)),t=(null===(o=e[this.paymentHistoryCurrencyIndex])||void 0===o?void 0:o.value)?null===(i=e[this.paymentHistoryCurrencyIndex])||void 0===i?void 0:i.value:0,this.paymentHistoryCurrency=null===(r=e[this.paymentHistoryCurrencyIndex])||void 0===r?void 0:r.currency_code,d.toolTipValue=t,"INR"==this.paymentHistoryCurrency?(d.toolTipValue=null===(l=t)||void 0===l?void 0:l.toFixed(2),t=(t/1e7).toFixed(2)+" Cr"):(d.toolTipValue=null===(c=t)||void 0===c?void 0:c.toFixed(2),t=(t/1e6).toFixed(2)+" M"),d.formattedValue=t}}changeHistoryAmount(){var t,e,n,a,o,i;this.paymentHistoryCurrencyIndex=++this.paymentHistoryCurrencyIndex%(null===(t=this.paymentInfo[0].amount_received)||void 0===t?void 0:t.length);for(let r of this.paymentInfo){let t,l=r.amount_received;t=(null===(e=l[this.paymentHistoryCurrencyIndex])||void 0===e?void 0:e.value)?null===(n=l[this.paymentHistoryCurrencyIndex])||void 0===n?void 0:n.value:0,this.paymentHistoryCurrency=null===(a=l[this.paymentHistoryCurrencyIndex])||void 0===a?void 0:a.currency_code,r.toolTipValue=t,"INR"==this.paymentHistoryCurrency?(r.toolTipValue=null===(o=t)||void 0===o?void 0:o.toFixed(2),t=(t/1e7).toFixed(2)+" Cr"):(r.toolTipValue=null===(i=t)||void 0===i?void 0:i.toFixed(2),t=(t/1e6).toFixed(2)+" M"),r.formattedValue=t}}create_UUID(){var t=(new Date).getTime();return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var n=(t+16*Math.random())%16|0;return t=Math.floor(t/16),("x"==e?n:3&n|8).toString(16)}))}viewFile(t){let e=t.cdn_link,n=null;0==this.isDialogOpened&&(this.isDialogOpened=!0,this._sharedService.getDownloadUrl(e).subscribe(e=>{n=this.dialog.open(u.a,{width:"100%",height:"100%",data:{selectedFileUrl:e.data,fileFormat:t.file_format,expHeaderId:this.expHeaderId}}),n.afterClosed().subscribe(t=>{this.isDialogOpened=!1})}))}deleteFile(t){let e=null==t?void 0:t.cdn_link;const n=this.selectedFiles.findIndex(t=>t.cdn_link===e);this.onContextMenuAction(t,"Delete",n)}onContextMenuAction(t,e,n){"Delete"==e&&d.a.fire({title:"Are you sure?",text:"Your file will deleted permanently!",icon:"warning",showCancelButton:!0,confirmButtonColor:"#3085d6",cancelButtonColor:"#d33",confirmButtonText:"Delete"}).then(e=>{e.isConfirmed&&this._sharedService.deleteObj(t,"t_app_attachments_meta").subscribe(t=>{this.selectedFiles.splice(n,1)},t=>{console.error(t)})})}onResize(){this.calculateDynamicContentHeight()}calculateDynamicContentHeight(){document.documentElement.style.setProperty("--dynamicHeight",window.innerHeight-112+"px"),document.documentElement.style.setProperty("--dynamicSubHeight",window.innerHeight-294+"px"),document.documentElement.style.setProperty("--dynamicPaymentHistoryHeight",window.innerHeight-369+"px")}retrievePaymentHistoryFiles(){var t;if(this.paymentInfo&&(null===(t=this.paymentInfo)||void 0===t?void 0:t.length)){this.paymentHistoryFiles=[];for(let t of this.paymentInfo)t.payment_context_id&&this._sharedService.retrieveUploadedObjects(this.destinationBucket,t.payment_context_id).pipe(Object(p.a)("data")).subscribe(e=>{const n=e.map(e=>Object.assign(Object.assign({},e),{paymentId:t.payment_id}));this.paymentHistoryFiles.push(...n)},t=>{console.error(t)})}}getFilesForPayment(t){var e;return null===(e=this.paymentHistoryFiles)||void 0===e?void 0:e.filter(e=>e.paymentId===t)}formatInput(t,e,n,a){var o,i,r,l,c,m;const d=t.target;let s=d.value;const g=a.inputType,p=d.selectionStart;if(s=s.replace(/,/g,"").replace(/^0+(?=\d)/,"0"),!/^\d*\.?\d*$/.test(s))return d.value="",void(null===(i=null===(o=this.activityForm)||void 0===o?void 0:o.get(e))||void 0===i||i.setValue(""));const u=parseFloat(s);if(isNaN(u))d.value="",null===(m=null===(c=this.activityForm)||void 0===c?void 0:c.get(e))||void 0===m||m.setValue("");else{if(""===s)return d.value="",void(null===(l=null===(r=this.activityForm)||void 0===r?void 0:r.get(e))||void 0===l||l.setValue(""));s=s.trim();const t=s.split(".");let n=t[0],a=void 0!==t[1]?"."+t[1]:"";n="INR"==this.activityForm.value.invoiceRaisedCurrency?new Intl.NumberFormat("en-IN").format(Number(n)):new Intl.NumberFormat("en-US").format(Number(n)),s=a?`${n}${a}`:n,d.value=s,"deleteContentBackward"!==g&&"deleteContentForward"!==g||d.setSelectionRange(p,p)}}parseValue(t){if("string"==typeof t){const e=t.trim();return""===e?0:parseFloat(e.replace(/,/g,""))}return"number"==typeof t?t:0}getStatus(){var t,e;return 11==(null===(t=this.invoiceInfo)||void 0===t?void 0:t.status)?"Completed":10==(null===(e=this.invoiceInfo)||void 0===e?void 0:e.status)?"Inprogress":"Open"}addNotes(){var t,e,n,a,o;this.commentsInput.application_id=915,this.commentsInput.application_name="Invoices",this.commentsInput.title=this.invoiceInfo.milestone_name,this.commentsInput.unique_id_1=this.invoiceInfo.milestone_id;let r=(null===(t=this.dateFormats)||void 0===t?void 0:t.invoice_general_date_format)?null===(e=this.dateFormats)||void 0===e?void 0:e.invoice_general_date_format:"DD-MM-YYYY",l={inputData:this.commentsInput,context:{"Milestone Name":this.invoiceInfo.milestone_name,"Milestone Start Date":s(null===(n=this.invoiceInfo)||void 0===n?void 0:n.planned_start_date).utc().format(r),"Milestone End Date":s(null===(a=this.invoiceInfo)||void 0===a?void 0:a.planned_end_date).utc().format(r),"Milestone Status":null===(o=this.invoiceInfo)||void 0===o?void 0:o.milestone_status_name},commentBoxHeight:"100vh",commentBoxScrollHeight:"80%"};this.dialog.open(h.ChatCommentContextModalComponent,{height:"100%",width:"75%",position:{right:"0px"},data:{modalParams:l},disableClose:!0}).afterClosed().subscribe(t=>Object(i.c)(this,void 0,void 0,(function*(){console.log(t);let e=yield this.invoiceHomeService.getCommentStatus(this.invoiceInfo.milestone_id);this.isCommentPresent=!(!e||"S"!=e.messType)&&e.data})))}getInvoiceConfig(){return Object(i.c)(this,void 0,void 0,(function*(){let t=yield this.invoiceHomeService.getInvoiceConfig();if(t.data[0]&&null!=t.data[0].config){let e=JSON.parse(t.data[0].config);this.generateAndStorePDFInS3=!(!e.hasOwnProperty("generate_store_pdf_in_s3")||!e.generate_store_pdf_in_s3)&&e.generate_store_pdf_in_s3}}))}getSendMailAuthorization(){return new Promise((t,e)=>{this.invoiceHomeService.getSendMailAuthorization().subscribe(e=>{t(e.data)},e=>{console.log(e),t(e)})})}}return t.\u0275fac=function(e){return new(e||t)(v["\u0275\u0275directiveInject"](_.b),v["\u0275\u0275directiveInject"](v.ChangeDetectorRef),v["\u0275\u0275directiveInject"](c.i),v["\u0275\u0275directiveInject"](o.a),v["\u0275\u0275directiveInject"](P.a),v["\u0275\u0275directiveInject"](o.g),v["\u0275\u0275directiveInject"](M.a),v["\u0275\u0275directiveInject"](O.a),v["\u0275\u0275directiveInject"](x.a),v["\u0275\u0275directiveInject"](y.a),v["\u0275\u0275directiveInject"](k.c),v["\u0275\u0275directiveInject"](w.a),v["\u0275\u0275directiveInject"](S.a),v["\u0275\u0275directiveInject"](I.a),v["\u0275\u0275directiveInject"](E.a),v["\u0275\u0275directiveInject"](D.a),v["\u0275\u0275directiveInject"](F.a),v["\u0275\u0275directiveInject"](C.e),v["\u0275\u0275directiveInject"](C.c))},t.\u0275cmp=v["\u0275\u0275defineComponent"]({type:t,selectors:[["app-invoice-billing-page"]],viewQuery:function(t,e){if(1&t&&(v["\u0275\u0275viewQuery"](q,!0),v["\u0275\u0275viewQuery"](L,!0),v["\u0275\u0275viewQuery"](b.a,!0),v["\u0275\u0275viewQuery"](Y,!0)),2&t){let t;v["\u0275\u0275queryRefresh"](t=v["\u0275\u0275loadQuery"]())&&(e.stepper=t.first),v["\u0275\u0275queryRefresh"](t=v["\u0275\u0275loadQuery"]())&&(e.fileInput=t.first),v["\u0275\u0275queryRefresh"](t=v["\u0275\u0275loadQuery"]())&&(e.sort=t.first),v["\u0275\u0275queryRefresh"](t=v["\u0275\u0275loadQuery"]())&&(e.paginator=t.first)}},hostBindings:function(t,e){1&t&&v["\u0275\u0275listener"]("resize",(function(){return e.onResize()}),!1,v["\u0275\u0275resolveWindow"])},features:[v["\u0275\u0275ProvidersFeature"]([{provide:r.h,useValue:{displayDefaultIndicatorType:!1}}])],decls:2,vars:2,consts:[["class","container-fluid invoice-billed-component","style","overflow: hidden;",4,"ngIf"],["size","medium","type","ball-clip-rotate","bdColor","rgba(236, 233, 233, 0.8)","color","#cf0001",4,"ngIf"],[1,"container-fluid","invoice-billed-component",2,"overflow","hidden"],[1,"row"],[1,"col-3","card","cardHeight"],[1,"row","d-flex","align-items-center","pl-0","pb-2","pr-2"],[1,"col-1","p-0"],["mat-icon-button","","matTooltip","Back",1,"icon-tray-button",3,"click"],[1,"smallCardIcon"],[1,"col-10","p-0"],[1,"col-10","d-flex"],[1,"p-1","mx-2","invoice-no"],[1,"p-1","mx-2",2,"word-wrap","break-word","white-space","normal"],["mat-icon-button","","matTooltip","Go to project",1,"icon-tray-button","pr-3",3,"click"],[1,"mt-1","mb-1"],[1,"row","mt-4","mb-2",2,"height","30px","justify-content","center"],[1,"col-2","icon-container"],[1,"icon",2,"background","#E7F9F9","cursor","pointer",3,"click"],[1,"iconClass","pt-2"],[1,"hover-text"],["class","col-2 icon-container",4,"ngIf"],[1,"icon",2,"background","#FDEAF4","cursor","pointer",3,"click"],[1,"icon",2,"background","#CDDEEE","cursor","pointer",3,"click"],[1,"iconClass","pt-2",2,"width","18px !important"],["class","checkmark",4,"ngIf"],[1,"row","mt-3",2,"background-color","#F7F9FB","height","145px","position","relative"],[1,"col","pt-3","pb-3"],[1,"row","position-relative","align-items-center","mt-2",2,"height","43px"],[1,"le-circle"],[1,"line"],[1,"col",2,"margin-left","20px"],[1,"row","labelName"],[1,"row","value","text-wrap"],[1,"row","position-relative","align-items-center",2,"height","72px"],[1,"cus-circle"],["class","row d-flex pt-2 pb-2",4,"ngIf"],[1,"col","ml-2","mr-0","pr-0","cardHeight"],[1,"row","card"],[1,"col","p-1"],[1,"col-3"],[1,"icon-container",2,"display","flex"],[1,"row","pb-2","pt-2","amount-label"],["matTooltip","Next currency",1,"change-icon","pl-2","pt-1",2,"display","flex","justify-content","end","font-size","19px","cursor","pointer",3,"click"],[1,"row","amount-value"],["content-type","template","placement","bottom",1,"pl-1",2,"font-size","14px","color","#6E7B8F","margin-top","-1px","margin-left","2px","font-weight","600",3,"tooltip","max-width","options"],["invoiceVal",""],[1,"col-3","d-flex"],[1,"dollar-icon",2,"background-color","#EEF9E8"],[2,"color","#52C41A","font-size","21px","margin-top","7px"],[1,"ml-2"],[1,"row","pb-2","amount-label"],[1,"ml-0",2,"color","#FF3A46 !important"],[1,"col-3","d-flex","align-items-center"],[1,"circular-progress"],[1,"progress-value"],[1,"ml-3"],[1,"row","collection-label"],[1,"row","inprogress-label"],[1,"row","pt-3"],[1,"col",2,"padding-left","8px"],[1,"col-1","p-0",2,"max-width","61.3px !important"],[1,"cal-circle",2,"background-color","#E8F4FF"],[1,"cal-icon"],[1,"col-2","p-0",2,"max-width","13% !important"],[1,"row","amount-value",2,"color","#1B2140 !important","font-weight","500"],["class","col-2 p-0","style","max-width: 13% !important;",4,"ngIf"],["class","row card mt-3 subHeadingHeight","style","overflow-y: auto; overflow-x: hidden; padding: 0px !important;",3,"formGroup",4,"ngIf"],["class","row card mt-3 subHeadingHeight","style","overflow-y: auto; padding: 0px !important;",4,"ngIf"],[1,"icon",2,"background","#EAEEFD","cursor","pointer",3,"click"],[1,"checkmark"],[1,"icon",2,"background","#F1EAFA","cursor","pointer",3,"click"],[1,"iconClass","pt-2",3,"ngClass"],[1,"row","d-flex","pt-2","pb-2"],[1,"col-1","p-0","m-0"],[1,"icon"],[1,"col","p-0",2,"display","block"],[1,"labelName"],[1,"value","text-wrap"],[1,"value"],[2,"padding-left","3px"],[1,""],[1,"row","mb-1"],[1,"col"],["class","row",4,"ngIf"],[1,"clock-icon",2,"color","#EE4961 !important","font-size","20px"],[1,"days-text","amount-value",2,"color","#EE4961 !important","font-weight","500"],[1,"row","card","mt-3","subHeadingHeight",2,"overflow-y","auto","overflow-x","hidden","padding","0px !important",3,"formGroup"],[1,"col-12","pt-3"],[1,"row","d-flex","mb-4","sticky-header"],[1,"col-10","mt-2"],["value","payment",1,"toggle-btn",3,"ngClass","click"],["value","history","class","toggle-btn",3,"ngClass","click",4,"ngIf"],["value","documents",1,"toggle-btn",3,"ngClass","click"],["class","col-2 mt-2","style","display: flex; justify-content: end;",4,"ngIf"],["class","row mt-3",4,"ngIf"],["class","row mt-3","style","padding-left: 32px;",4,"ngIf"],["class","row pl-2",4,"ngIf"],["value","history",1,"toggle-btn",3,"ngClass","click"],[1,"col-2","mt-2",2,"display","flex","justify-content","end"],["mat-raised-button","",1,"submit",3,"ngClass","disabled","click"],[3,"ngStyle"],[1,"col-10"],[1,"col-5"],[1,"row","payment-label","pb-2"],[1,"pl-2",2,"color","red"],[1,"row","amount-received"],["appearance","outline","floatLabel","always",2,"width","80%"],["matInput","","type","text","placeholder","0.00","formControlName","amountReceived","min","1","required","","onkeydown","return event.keyCode !== 69",1,"pr-1",3,"input"],["class","pt-2",4,"ngIf"],["matSuffix","",1,"currency-suffix"],[1,"row","date-picker"],["matInput","","name","endDate","required","","formControlName","receivedDate","readonly","",3,"matDatepicker","max","placeholder"],["matSuffix","",3,"for"],["picker4",""],[1,"row","mt-2"],["formControlName","paymentReceivedBank"],["value","",1,"selectValue",2,"color","rgba(239, 225, 225, 0.54) !important"],[3,"value",4,"ngFor","ngForOf"],["matInput","","type","text","placeholder","Enter Ref No","formControlName","bankRefNo"],[1,"pt-2"],[3,"value"],[1,"row","mt-3"],[2,"padding-left","32px"],[2,"color","#EE4961","font-size","12px","font-family","DM Sans","line-height","12px","font-weight","600"],[2,"color","#45546E","font-size","12px","font-family","DM Sans","line-height","12px","font-weight","400"],[1,"row","mt-3",2,"padding-left","32px"],[2,"color","#52C41A","font-size","12px","font-family","DM Sans","line-height","12px","font-weight","500"],[1,"row",2,"width","100%"],["appearance","outline",1,"textArea"],["matInput","","cdkTextareaAutosize","","cdkAutosizeMinRows","5","cdkAutosizeMaxRows","","formControlName","note",1,"label-name"],["autosize","cdkTextareaAutosize"],[1,"row","pl-2"],[1,"col","pl-4"],[1,"row","mb-3",2,"font-size","12px","font-family","DM Sans","line-height","16px","font-weight","500","color","#000000"],[1,"row","mb-3",2,"font-size","12px","font-weight","500","color","#52C41A","font-size","12px","font-family","DM Sans","line-height","12px"],[1,"row","mb-3","d-flex"],[1,"col-1","upload-container","mt-2","position-relative"],[1,"file-count"],["type","file","multiple","","ng2FileSelect","",2,"display","none",3,"uploader","change"],["fileInput",""],[3,"click"],[1,"col-11",2,"display","flex","flex-wrap","wrap"],["style","display: flex; align-items: center; border: 1px solid lightgray; background-color: lightgray; margin-right: 8px; padding: 5px; cursor: pointer; margin-bottom: 10px;",4,"ngFor","ngForOf"],[2,"display","flex","align-items","center","border","1px solid lightgray","background-color","lightgray","margin-right","8px","padding","5px","cursor","pointer","margin-bottom","10px"],["alt","",1,"pr-2",2,"height","20px","width","20px","margin-right","8px",3,"src"],[1,"mt-1","filename",2,"flex-grow","1","max-width","188px","overflow","hidden","text-overflow","ellipsis","white-space","nowrap",3,"matTooltip"],[1,"pl-1","delete-icon",2,"cursor","pointer","color","#000000",3,"click"],[1,"row","card","mt-3","subHeadingHeight",2,"overflow-y","auto","padding","0px !important"],[1,"row","d-flex","justify-content-between","align-items-center","sticky-header","mb-4"],[1,"col-6","mt-2","d-flex","align-items-center"],["value","payment","class","toggle-btn",3,"ngClass","click",4,"ngIf"],[1,"col-6","mt-2","d-flex","justify-content-end","pr-4"],[1,"d-flex","align-items-center"],[1,"pr-1",2,"font-size","18px","cursor","pointer",3,"click"],["class","row mt-3 pt-3 paymentHistoryHeight","style","overflow-y: auto;",4,"ngIf"],[1,"row","mt-3","pt-3","paymentHistoryHeight",2,"overflow-y","auto"],["class","row pb-3 mb-3 pt-3","style","border: 1px solid #B9C0CA",4,"ngFor","ngForOf"],[1,"row","pb-3","mb-3","pt-3",2,"border","1px solid #B9C0CA"],[1,"payment-history-amount",3,"matTooltip"],[1,"col",2,"border-right","1px solid #C3C7CF"],[1,"row","payment-history-label"],[1,"row","payment-history-value","text-wrap"],[1,"row","payment-history-value"],[1,"row","mt-1"],["class","col payment-history-value",4,"ngIf"],["class","col d-flex",4,"ngIf"],[1,"col","payment-history-value"],[1,"col","d-flex"],[4,"ngFor","ngForOf"],[1,"payment-history-value","pr-3","payment-history-filename",3,"matTooltip","click"],[1,"row","pt-4","mb-2","d-flex","justify-content-between","align-items-center","sticky-header"],[1,"col-6","d-flex"],[1,"col-6",2,"justify-content","end","display","flex"],["class","row","style","justify-content: end !important;",4,"ngIf"],[1,"row",2,"justify-content","end !important"],["mat-raised-button","",1,"mx-auto","addDocument",3,"click"],[2,"transform","rotate(42deg)","font-size","18px","height","17px","margin-left","3px"],[1,"table-container"],["matSort","",1,"custom-table",2,"width","100%",3,"dataSource","matSortChange"],["matColumnDef","file_type"],["mat-sort-header","","style","flex: 0 0 120px; text-align: left; font-size: 12px; color: #A8ACB2; line-height: 16px; font-weight: 400;",4,"matHeaderCellDef"],["style","flex: 0 0 120px; text-align: left; font-size: 12px; color: #111434; line-height: 16px; font-weight: 500;",4,"matCellDef"],["matColumnDef","file_name"],["mat-sort-header","","style","flex: 1; text-align: left; font-size: 12px; color: #A8ACB2; line-height: 16px; font-weight: 400;",4,"matHeaderCellDef"],["style","flex: 1; text-align: left; font-size: 12px; color: #111434; line-height: 16px; font-weight: 500; cursor: pointer;",3,"click",4,"matCellDef"],[4,"matHeaderRowDef"],[4,"matRowDef","matRowDefColumns"],["mat-sort-header","",2,"flex","0 0 120px","text-align","left","font-size","12px","color","#A8ACB2","line-height","16px","font-weight","400"],[2,"flex","0 0 120px","text-align","left","font-size","12px","color","#111434","line-height","16px","font-weight","500"],["alt","file icon",2,"height","20px","width","20px",3,"src"],["mat-sort-header","",2,"flex","1","text-align","left","font-size","12px","color","#A8ACB2","line-height","16px","font-weight","400"],[2,"flex","1","text-align","left","font-size","12px","color","#111434","line-height","16px","font-weight","500","cursor","pointer",3,"click"],[1,"table-container",2,"display","flex","justify-content","center","text-align","center","align-items","center","flex-direction","column"],[1,"pb-1",2,"color","#52C41A","font-size","16px","font-weight","500","font-family","DM Sans"],["src","https://assets.kebs.app/images/no_data_found.png","height","150","width","250"],["size","medium","type","ball-clip-rotate","bdColor","rgba(236, 233, 233, 0.8)","color","#cf0001"]],template:function(t,e){1&t&&(v["\u0275\u0275template"](0,qt,136,31,"div",0),v["\u0275\u0275template"](1,Lt,1,0,"ngx-spinner",1)),2&t&&(v["\u0275\u0275property"]("ngIf",e.show),v["\u0275\u0275advance"](1),v["\u0275\u0275property"]("ngIf",!e.show))},directives:[a.NgIf,z.a,N.a,T.a,A.a,a.NgClass,c.w,c.n,R.b,R.a,a.NgStyle,U.c,V.b,c.e,c.v,c.l,c.F,U.i,B.g,B.i,B.f,H.c,C.p,a.NgForOf,U.b,j.b,g.b,f.k,b.a,f.c,f.e,f.b,f.g,f.j,f.d,b.b,f.a,f.f,f.i,k.a],pipes:[K],styles:['.mat-badge-content[_ngcontent-%COMP%]{font-weight:600;font-size:12px;font-family:Roboto,Helvetica Neue,sans-serif}.mat-badge-small[_ngcontent-%COMP%]   .mat-badge-content[_ngcontent-%COMP%]{font-size:9px}.mat-badge-large[_ngcontent-%COMP%]   .mat-badge-content[_ngcontent-%COMP%]{font-size:24px}.mat-h1[_ngcontent-%COMP%], .mat-headline[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font:400 24px/32px Roboto,Helvetica Neue,sans-serif;letter-spacing:normal;margin:0 0 16px}.mat-h2[_ngcontent-%COMP%], .mat-title[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font:500 20px/32px Roboto,Helvetica Neue,sans-serif;letter-spacing:normal;margin:0 0 16px}.mat-h3[_ngcontent-%COMP%], .mat-subheading-2[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font:400 16px/28px Roboto,Helvetica Neue,sans-serif;letter-spacing:normal;margin:0 0 16px}.mat-h4[_ngcontent-%COMP%], .mat-subheading-1[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font:400 15px/24px Roboto,Helvetica Neue,sans-serif;letter-spacing:normal;margin:0 0 16px}.mat-h5[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{font:400 calc(14px * .83)/20px Roboto,Helvetica Neue,sans-serif;margin:0 0 12px}.mat-h6[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{font:400 calc(14px * .67)/20px Roboto,Helvetica Neue,sans-serif;margin:0 0 12px}.mat-body-2[_ngcontent-%COMP%], .mat-body-strong[_ngcontent-%COMP%]{font:500 14px/24px Roboto,Helvetica Neue,sans-serif;letter-spacing:normal}.mat-body[_ngcontent-%COMP%], .mat-body-1[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]{font:400 14px/20px Roboto,Helvetica Neue,sans-serif;letter-spacing:normal}.mat-body-1[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .mat-body[_ngcontent-%COMP%]   p[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 12px}.mat-caption[_ngcontent-%COMP%], .mat-small[_ngcontent-%COMP%]{font:400 12px/20px Roboto,Helvetica Neue,sans-serif;letter-spacing:normal}.mat-display-4[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]   .mat-display-4[_ngcontent-%COMP%]{font:300 112px/112px Roboto,Helvetica Neue,sans-serif;letter-spacing:-.05em;margin:0 0 56px}.mat-display-3[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]   .mat-display-3[_ngcontent-%COMP%]{font:400 56px/56px Roboto,Helvetica Neue,sans-serif;letter-spacing:-.02em;margin:0 0 64px}.mat-display-2[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]   .mat-display-2[_ngcontent-%COMP%]{font:400 45px/48px Roboto,Helvetica Neue,sans-serif;letter-spacing:-.005em;margin:0 0 64px}.mat-display-1[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]   .mat-display-1[_ngcontent-%COMP%]{font:400 34px/40px Roboto,Helvetica Neue,sans-serif;letter-spacing:normal;margin:0 0 64px}.mat-bottom-sheet-container[_ngcontent-%COMP%]{font:400 14px/20px Roboto,Helvetica Neue,sans-serif;letter-spacing:normal}.mat-button[_ngcontent-%COMP%], .mat-fab[_ngcontent-%COMP%], .mat-flat-button[_ngcontent-%COMP%], .mat-icon-button[_ngcontent-%COMP%], .mat-mini-fab[_ngcontent-%COMP%], .mat-raised-button[_ngcontent-%COMP%], .mat-stroked-button[_ngcontent-%COMP%]{font-family:Roboto,Helvetica Neue,sans-serif;font-size:14px;font-weight:500}.mat-button-toggle[_ngcontent-%COMP%], .mat-card[_ngcontent-%COMP%]{font-family:Roboto,Helvetica Neue,sans-serif}.mat-card-title[_ngcontent-%COMP%]{font-size:24px;font-weight:500}.mat-card-header[_ngcontent-%COMP%]   .mat-card-title[_ngcontent-%COMP%]{font-size:20px}.mat-card-content[_ngcontent-%COMP%], .mat-card-subtitle[_ngcontent-%COMP%]{font-size:14px}.mat-checkbox[_ngcontent-%COMP%]{font-family:Roboto,Helvetica Neue,sans-serif}.mat-checkbox-layout[_ngcontent-%COMP%]   .mat-checkbox-label[_ngcontent-%COMP%]{line-height:24px}.mat-chip[_ngcontent-%COMP%]{font-size:14px;font-weight:500}.mat-chip[_ngcontent-%COMP%]   .mat-chip-remove.mat-icon[_ngcontent-%COMP%], .mat-chip[_ngcontent-%COMP%]   .mat-chip-trailing-icon.mat-icon[_ngcontent-%COMP%]{font-size:18px}.mat-table[_ngcontent-%COMP%]{font-family:Roboto,Helvetica Neue,sans-serif}.mat-header-cell[_ngcontent-%COMP%]{font-size:12px;font-weight:500}.mat-cell[_ngcontent-%COMP%], .mat-footer-cell[_ngcontent-%COMP%]{font-size:14px}.mat-calendar[_ngcontent-%COMP%]{font-family:Roboto,Helvetica Neue,sans-serif}.mat-calendar-body[_ngcontent-%COMP%]{font-size:13px}.mat-calendar-body-label[_ngcontent-%COMP%], .mat-calendar-period-button[_ngcontent-%COMP%]{font-size:14px;font-weight:500}.mat-calendar-table-header[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{font-size:11px;font-weight:400}.mat-dialog-title[_ngcontent-%COMP%]{font:500 20px/32px Roboto,Helvetica Neue,sans-serif;letter-spacing:normal}.mat-expansion-panel-header[_ngcontent-%COMP%]{font-family:Roboto,Helvetica Neue,sans-serif;font-size:15px;font-weight:400}.mat-expansion-panel-content[_ngcontent-%COMP%]{font:400 14px/20px Roboto,Helvetica Neue,sans-serif;letter-spacing:normal}.mat-form-field[_ngcontent-%COMP%]{font-size:inherit;font-weight:400;line-height:1.125;font-family:Roboto,Helvetica Neue,sans-serif;letter-spacing:normal}.mat-form-field-wrapper[_ngcontent-%COMP%]{padding-bottom:1.34375em}.mat-form-field-prefix[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%], .mat-form-field-suffix[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%]{font-size:150%;line-height:1.125}.mat-form-field-prefix[_ngcontent-%COMP%]   .mat-icon-button[_ngcontent-%COMP%], .mat-form-field-suffix[_ngcontent-%COMP%]   .mat-icon-button[_ngcontent-%COMP%]{height:1.5em;width:1.5em}.mat-form-field-prefix[_ngcontent-%COMP%]   .mat-icon-button[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%], .mat-form-field-suffix[_ngcontent-%COMP%]   .mat-icon-button[_ngcontent-%COMP%]   .mat-icon[_ngcontent-%COMP%]{height:1.125em;line-height:1.125}.mat-form-field-infix[_ngcontent-%COMP%]{padding:.5em 0;border-top:.84375em solid transparent}.mat-form-field-can-float.mat-form-field-should-float[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%], .mat-form-field-can-float[_ngcontent-%COMP%]   .mat-input-server[_ngcontent-%COMP%]:focus + .mat-form-field-label-wrapper[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%]{transform:translateY(-1.34375em) scale(.75);width:133.3333333333%}.mat-form-field-can-float[_ngcontent-%COMP%]   .mat-input-server[label][_ngcontent-%COMP%]:not(:label-shown) + .mat-form-field-label-wrapper[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%]{transform:translateY(-1.34374em) scale(.75);width:133.3333433333%}.mat-form-field-label-wrapper[_ngcontent-%COMP%]{top:-.84375em;padding-top:.84375em}.mat-form-field-label[_ngcontent-%COMP%]{top:1.34375em}.mat-form-field-underline[_ngcontent-%COMP%]{bottom:1.34375em}.mat-form-field-subscript-wrapper[_ngcontent-%COMP%]{font-size:75%;margin-top:.6666666667em;top:calc(100% - 1.7916666667em)}.mat-form-field-appearance-legacy[_ngcontent-%COMP%]   .mat-form-field-wrapper[_ngcontent-%COMP%]{padding-bottom:1.25em}.mat-form-field-appearance-legacy[_ngcontent-%COMP%]   .mat-form-field-infix[_ngcontent-%COMP%]{padding:.4375em 0}.mat-form-field-appearance-legacy.mat-form-field-can-float.mat-form-field-should-float[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%], .mat-form-field-appearance-legacy.mat-form-field-can-float[_ngcontent-%COMP%]   .mat-input-server[_ngcontent-%COMP%]:focus + .mat-form-field-label-wrapper[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%]{transform:translateY(-1.28125em) scale(.75) perspective(100px) translateZ(.001px);-ms-transform:translateY(-1.28125em) scale(.75);width:133.3333333333%}.mat-form-field-appearance-legacy.mat-form-field-can-float[_ngcontent-%COMP%]   .mat-form-field-autofill-control[_ngcontent-%COMP%]:-webkit-autofill + .mat-form-field-label-wrapper[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%]{transform:translateY(-1.28125em) scale(.75) perspective(100px) translateZ(.00101px);-ms-transform:translateY(-1.28124em) scale(.75);width:133.3333433333%}.mat-form-field-appearance-legacy.mat-form-field-can-float[_ngcontent-%COMP%]   .mat-input-server[label][_ngcontent-%COMP%]:not(:label-shown) + .mat-form-field-label-wrapper[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%]{transform:translateY(-1.28125em) scale(.75) perspective(100px) translateZ(.00102px);-ms-transform:translateY(-1.28123em) scale(.75);width:133.3333533333%}.mat-form-field-appearance-legacy[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%]{top:1.28125em}.mat-form-field-appearance-legacy[_ngcontent-%COMP%]   .mat-form-field-underline[_ngcontent-%COMP%]{bottom:1.25em}.mat-form-field-appearance-legacy[_ngcontent-%COMP%]   .mat-form-field-subscript-wrapper[_ngcontent-%COMP%]{margin-top:.5416666667em;top:calc(100% - 1.6666666667em)}@media print{.mat-form-field-appearance-legacy.mat-form-field-can-float.mat-form-field-should-float[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%], .mat-form-field-appearance-legacy.mat-form-field-can-float[_ngcontent-%COMP%]   .mat-input-server[_ngcontent-%COMP%]:focus + .mat-form-field-label-wrapper[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%]{transform:translateY(-1.28122em) scale(.75)}.mat-form-field-appearance-legacy.mat-form-field-can-float[_ngcontent-%COMP%]   .mat-form-field-autofill-control[_ngcontent-%COMP%]:-webkit-autofill + .mat-form-field-label-wrapper[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%]{transform:translateY(-1.28121em) scale(.75)}.mat-form-field-appearance-legacy.mat-form-field-can-float[_ngcontent-%COMP%]   .mat-input-server[label][_ngcontent-%COMP%]:not(:label-shown) + .mat-form-field-label-wrapper[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%]{transform:translateY(-1.2812em) scale(.75)}}.mat-form-field-appearance-fill[_ngcontent-%COMP%]   .mat-form-field-infix[_ngcontent-%COMP%]{padding:.25em 0 .75em}.mat-form-field-appearance-fill[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%]{top:1.09375em;margin-top:-.5em}.mat-form-field-appearance-fill.mat-form-field-can-float.mat-form-field-should-float[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%], .mat-form-field-appearance-fill.mat-form-field-can-float[_ngcontent-%COMP%]   .mat-input-server[_ngcontent-%COMP%]:focus + .mat-form-field-label-wrapper[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%]{transform:translateY(-.59375em) scale(.75);width:133.3333333333%}.mat-form-field-appearance-fill.mat-form-field-can-float[_ngcontent-%COMP%]   .mat-input-server[label][_ngcontent-%COMP%]:not(:label-shown) + .mat-form-field-label-wrapper[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%]{transform:translateY(-.59374em) scale(.75);width:133.3333433333%}.mat-form-field-appearance-outline[_ngcontent-%COMP%]   .mat-form-field-infix[_ngcontent-%COMP%]{padding:1em 0}.mat-form-field-appearance-outline[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%]{top:1.84375em;margin-top:-.25em}.mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%], .mat-form-field-appearance-outline.mat-form-field-can-float[_ngcontent-%COMP%]   .mat-input-server[_ngcontent-%COMP%]:focus + .mat-form-field-label-wrapper[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%]{transform:translateY(-1.59375em) scale(.75);width:133.3333333333%}.mat-form-field-appearance-outline.mat-form-field-can-float[_ngcontent-%COMP%]   .mat-input-server[label][_ngcontent-%COMP%]:not(:label-shown) + .mat-form-field-label-wrapper[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%]{transform:translateY(-1.59374em) scale(.75);width:133.3333433333%}.mat-grid-tile-footer[_ngcontent-%COMP%], .mat-grid-tile-header[_ngcontent-%COMP%]{font-size:14px}.mat-grid-tile-footer[_ngcontent-%COMP%]   .mat-line[_ngcontent-%COMP%], .mat-grid-tile-header[_ngcontent-%COMP%]   .mat-line[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-footer[_ngcontent-%COMP%]   .mat-line[_ngcontent-%COMP%]:nth-child(n+2), .mat-grid-tile-header[_ngcontent-%COMP%]   .mat-line[_ngcontent-%COMP%]:nth-child(n+2){font-size:12px}input.mat-input-element[_ngcontent-%COMP%]{margin-top:-.0625em}.mat-menu-item[_ngcontent-%COMP%]{font-family:Roboto,Helvetica Neue,sans-serif;font-size:14px;font-weight:400}.mat-paginator[_ngcontent-%COMP%], .mat-paginator-page-size[_ngcontent-%COMP%]   .mat-select-trigger[_ngcontent-%COMP%]{font-family:Roboto,Helvetica Neue,sans-serif;font-size:12px}.mat-radio-button[_ngcontent-%COMP%], .mat-select[_ngcontent-%COMP%]{font-family:Roboto,Helvetica Neue,sans-serif}.mat-select-trigger[_ngcontent-%COMP%]{height:1.125em}.mat-slide-toggle-content[_ngcontent-%COMP%], .mat-slider-thumb-label-text[_ngcontent-%COMP%]{font-family:Roboto,Helvetica Neue,sans-serif}.mat-slider-thumb-label-text[_ngcontent-%COMP%]{font-size:12px;font-weight:500}.mat-stepper-horizontal[_ngcontent-%COMP%], .mat-stepper-vertical[_ngcontent-%COMP%]{font-family:Roboto,Helvetica Neue,sans-serif}.mat-step-label[_ngcontent-%COMP%]{font-size:14px;font-weight:400}.mat-step-sub-label-error[_ngcontent-%COMP%]{font-weight:400}.mat-step-label-error[_ngcontent-%COMP%]{font-size:14px}.mat-step-label-selected[_ngcontent-%COMP%]{font-size:14px;font-weight:500}.mat-tab-group[_ngcontent-%COMP%], .mat-tab-label[_ngcontent-%COMP%], .mat-tab-link[_ngcontent-%COMP%]{font-family:Roboto,Helvetica Neue,sans-serif}.mat-tab-label[_ngcontent-%COMP%], .mat-tab-link[_ngcontent-%COMP%]{font-size:14px;font-weight:500}.mat-toolbar[_ngcontent-%COMP%], .mat-toolbar[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%], .mat-toolbar[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .mat-toolbar[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .mat-toolbar[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .mat-toolbar[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%], .mat-toolbar[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{font:500 20px/32px Roboto,Helvetica Neue,sans-serif;letter-spacing:normal;margin:0}.mat-tooltip[_ngcontent-%COMP%]{font-family:Roboto,Helvetica Neue,sans-serif;font-size:10px;padding-top:6px;padding-bottom:6px}.mat-tooltip-handset[_ngcontent-%COMP%]{font-size:14px;padding-top:8px;padding-bottom:8px}.mat-list-item[_ngcontent-%COMP%], .mat-list-option[_ngcontent-%COMP%]{font-family:Roboto,Helvetica Neue,sans-serif}.mat-list-base[_ngcontent-%COMP%]   .mat-list-item[_ngcontent-%COMP%]{font-size:16px}.mat-list-base[_ngcontent-%COMP%]   .mat-list-item[_ngcontent-%COMP%]   .mat-line[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-list-base[_ngcontent-%COMP%]   .mat-list-item[_ngcontent-%COMP%]   .mat-line[_ngcontent-%COMP%]:nth-child(n+2){font-size:14px}.mat-list-base[_ngcontent-%COMP%]   .mat-list-option[_ngcontent-%COMP%]{font-size:16px}.mat-list-base[_ngcontent-%COMP%]   .mat-list-option[_ngcontent-%COMP%]   .mat-line[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-list-base[_ngcontent-%COMP%]   .mat-list-option[_ngcontent-%COMP%]   .mat-line[_ngcontent-%COMP%]:nth-child(n+2){font-size:14px}.mat-list-base[_ngcontent-%COMP%]   .mat-subheader[_ngcontent-%COMP%]{font-family:Roboto,Helvetica Neue,sans-serif;font-size:14px;font-weight:500}.mat-list-base[dense][_ngcontent-%COMP%]   .mat-list-item[_ngcontent-%COMP%]{font-size:12px}.mat-list-base[dense][_ngcontent-%COMP%]   .mat-list-item[_ngcontent-%COMP%]   .mat-line[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-list-base[dense][_ngcontent-%COMP%]   .mat-list-item[_ngcontent-%COMP%]   .mat-line[_ngcontent-%COMP%]:nth-child(n+2), .mat-list-base[dense][_ngcontent-%COMP%]   .mat-list-option[_ngcontent-%COMP%]{font-size:12px}.mat-list-base[dense][_ngcontent-%COMP%]   .mat-list-option[_ngcontent-%COMP%]   .mat-line[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-list-base[dense][_ngcontent-%COMP%]   .mat-list-option[_ngcontent-%COMP%]   .mat-line[_ngcontent-%COMP%]:nth-child(n+2){font-size:12px}.mat-list-base[dense][_ngcontent-%COMP%]   .mat-subheader[_ngcontent-%COMP%]{font-family:Roboto,Helvetica Neue,sans-serif;font-size:12px;font-weight:500}.mat-option[_ngcontent-%COMP%]{font-family:Roboto,Helvetica Neue,sans-serif;font-size:16px}.mat-optgroup-label[_ngcontent-%COMP%]{font:500 14px/24px Roboto,Helvetica Neue,sans-serif;letter-spacing:normal}.mat-simple-snackbar[_ngcontent-%COMP%]{font-family:Roboto,Helvetica Neue,sans-serif;font-size:14px}.mat-simple-snackbar-action[_ngcontent-%COMP%]{line-height:1;font-family:inherit;font-size:inherit;font-weight:500}.mat-tree[_ngcontent-%COMP%]{font-family:Roboto,Helvetica Neue,sans-serif}.mat-nested-tree-node[_ngcontent-%COMP%], .mat-tree-node[_ngcontent-%COMP%]{font-weight:400;font-size:14px}.mat-ripple[_ngcontent-%COMP%]{overflow:hidden;position:relative}.mat-ripple[_ngcontent-%COMP%]:not(:empty){transform:translateZ(0)}.mat-ripple.mat-ripple-unbounded[_ngcontent-%COMP%]{overflow:visible}.mat-ripple-element[_ngcontent-%COMP%]{position:absolute;border-radius:50%;pointer-events:none;transition:opacity,transform 0ms cubic-bezier(0,0,.2,1);transform:scale(0)}.cdk-high-contrast-active[_ngcontent-%COMP%]   .mat-ripple-element[_ngcontent-%COMP%]{display:none}.cdk-visually-hidden[_ngcontent-%COMP%]{border:0;clip:rect(0 0 0 0);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px;outline:0;-webkit-appearance:none;-moz-appearance:none}.cdk-global-overlay-wrapper[_ngcontent-%COMP%], .cdk-overlay-container[_ngcontent-%COMP%]{pointer-events:none;top:0;left:0;height:100%;width:100%}.cdk-overlay-container[_ngcontent-%COMP%]{position:fixed;z-index:1000}.cdk-overlay-container[_ngcontent-%COMP%]:empty{display:none}.cdk-global-overlay-wrapper[_ngcontent-%COMP%], .cdk-overlay-pane[_ngcontent-%COMP%]{display:flex;position:absolute;z-index:1000}.cdk-overlay-pane[_ngcontent-%COMP%]{pointer-events:auto;box-sizing:border-box;max-width:100%;max-height:100%}.cdk-overlay-backdrop[_ngcontent-%COMP%]{position:absolute;top:0;bottom:0;left:0;right:0;z-index:1000;pointer-events:auto;-webkit-tap-highlight-color:transparent;transition:opacity .4s cubic-bezier(.25,.8,.25,1);opacity:0}.cdk-overlay-backdrop.cdk-overlay-backdrop-showing[_ngcontent-%COMP%]{opacity:1}@media screen and (-ms-high-contrast:active){.cdk-overlay-backdrop.cdk-overlay-backdrop-showing[_ngcontent-%COMP%]{opacity:.6}}.cdk-overlay-dark-backdrop[_ngcontent-%COMP%]{background:rgba(0,0,0,.32)}.cdk-overlay-transparent-backdrop[_ngcontent-%COMP%], .cdk-overlay-transparent-backdrop.cdk-overlay-backdrop-showing[_ngcontent-%COMP%]{opacity:0}.cdk-overlay-connected-position-bounding-box[_ngcontent-%COMP%]{position:absolute;z-index:1000;display:flex;flex-direction:column;min-width:1px;min-height:1px}.cdk-global-scrollblock[_ngcontent-%COMP%]{position:fixed;width:100%;overflow-y:scroll}@keyframes cdk-text-field-autofill-start{}@keyframes cdk-text-field-autofill-end{}.cdk-text-field-autofill-monitored[_ngcontent-%COMP%]:-webkit-autofill{animation:cdk-text-field-autofill-start 0s 1ms}.cdk-text-field-autofill-monitored[_ngcontent-%COMP%]:not(:-webkit-autofill){animation:cdk-text-field-autofill-end 0s 1ms}textarea.cdk-textarea-autosize[_ngcontent-%COMP%]{resize:none}textarea.cdk-textarea-autosize-measuring[_ngcontent-%COMP%]{padding:2px 0!important;box-sizing:initial!important;height:auto!important;overflow:hidden!important}textarea.cdk-textarea-autosize-measuring-firefox[_ngcontent-%COMP%]{padding:2px 0!important;box-sizing:initial!important;height:0!important}.mat-focus-indicator[_ngcontent-%COMP%], .mat-mdc-focus-indicator[_ngcontent-%COMP%]{position:relative}.mat-ripple-element[_ngcontent-%COMP%]{background-color:rgba(0,0,0,.1)}.mat-option[_ngcontent-%COMP%]{color:rgba(0,0,0,.87)}.mat-option.mat-active[_ngcontent-%COMP%], .mat-option.mat-selected[_ngcontent-%COMP%]:not(.mat-option-multiple):not(.mat-option-disabled), .mat-option[_ngcontent-%COMP%]:focus:not(.mat-option-disabled), .mat-option[_ngcontent-%COMP%]:hover:not(.mat-option-disabled){background:rgba(0,0,0,.04)}.mat-option.mat-active[_ngcontent-%COMP%]{color:rgba(0,0,0,.87)}.mat-option.mat-option-disabled[_ngcontent-%COMP%]{color:rgba(0,0,0,.38)}.mat-primary[_ngcontent-%COMP%]   .mat-option.mat-selected[_ngcontent-%COMP%]:not(.mat-option-disabled){color:#3f51b5}.mat-accent[_ngcontent-%COMP%]   .mat-option.mat-selected[_ngcontent-%COMP%]:not(.mat-option-disabled){color:#ff4081}.mat-warn[_ngcontent-%COMP%]   .mat-option.mat-selected[_ngcontent-%COMP%]:not(.mat-option-disabled){color:#f44336}.mat-optgroup-label[_ngcontent-%COMP%]{color:rgba(0,0,0,.54)}.mat-optgroup-disabled[_ngcontent-%COMP%]   .mat-optgroup-label[_ngcontent-%COMP%]{color:rgba(0,0,0,.38)}.mat-pseudo-checkbox[_ngcontent-%COMP%]{color:rgba(0,0,0,.54)}.mat-pseudo-checkbox[_ngcontent-%COMP%]:after{color:#fafafa}.mat-pseudo-checkbox-disabled[_ngcontent-%COMP%]{color:#b0b0b0}.mat-primary[_ngcontent-%COMP%]   .mat-pseudo-checkbox-checked[_ngcontent-%COMP%], .mat-primary[_ngcontent-%COMP%]   .mat-pseudo-checkbox-indeterminate[_ngcontent-%COMP%]{background:#3f51b5}.mat-accent[_ngcontent-%COMP%]   .mat-pseudo-checkbox-checked[_ngcontent-%COMP%], .mat-accent[_ngcontent-%COMP%]   .mat-pseudo-checkbox-indeterminate[_ngcontent-%COMP%], .mat-pseudo-checkbox-checked[_ngcontent-%COMP%], .mat-pseudo-checkbox-indeterminate[_ngcontent-%COMP%]{background:#ff4081}.mat-warn[_ngcontent-%COMP%]   .mat-pseudo-checkbox-checked[_ngcontent-%COMP%], .mat-warn[_ngcontent-%COMP%]   .mat-pseudo-checkbox-indeterminate[_ngcontent-%COMP%]{background:#f44336}.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled[_ngcontent-%COMP%], .mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled[_ngcontent-%COMP%]{background:#b0b0b0}.mat-app-background[_ngcontent-%COMP%]{background-color:#fafafa;color:rgba(0,0,0,.87)}.mat-elevation-z0[_ngcontent-%COMP%]{box-shadow:0 0 0 0 rgba(0,0,0,.2),0 0 0 0 rgba(0,0,0,.14),0 0 0 0 rgba(0,0,0,.12)}.mat-elevation-z1[_ngcontent-%COMP%]{box-shadow:0 2px 1px -1px rgba(0,0,0,.2),0 1px 1px 0 rgba(0,0,0,.14),0 1px 3px 0 rgba(0,0,0,.12)}.mat-elevation-z2[_ngcontent-%COMP%]{box-shadow:0 3px 1px -2px rgba(0,0,0,.2),0 2px 2px 0 rgba(0,0,0,.14),0 1px 5px 0 rgba(0,0,0,.12)}.mat-elevation-z3[_ngcontent-%COMP%]{box-shadow:0 3px 3px -2px rgba(0,0,0,.2),0 3px 4px 0 rgba(0,0,0,.14),0 1px 8px 0 rgba(0,0,0,.12)}.mat-elevation-z4[_ngcontent-%COMP%]{box-shadow:0 2px 4px -1px rgba(0,0,0,.2),0 4px 5px 0 rgba(0,0,0,.14),0 1px 10px 0 rgba(0,0,0,.12)}.mat-elevation-z5[_ngcontent-%COMP%]{box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 5px 8px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.mat-elevation-z6[_ngcontent-%COMP%]{box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.mat-elevation-z7[_ngcontent-%COMP%]{box-shadow:0 4px 5px -2px rgba(0,0,0,.2),0 7px 10px 1px rgba(0,0,0,.14),0 2px 16px 1px rgba(0,0,0,.12)}.mat-elevation-z8[_ngcontent-%COMP%]{box-shadow:0 5px 5px -3px rgba(0,0,0,.2),0 8px 10px 1px rgba(0,0,0,.14),0 3px 14px 2px rgba(0,0,0,.12)}.mat-elevation-z9[_ngcontent-%COMP%]{box-shadow:0 5px 6px -3px rgba(0,0,0,.2),0 9px 12px 1px rgba(0,0,0,.14),0 3px 16px 2px rgba(0,0,0,.12)}.mat-elevation-z10[_ngcontent-%COMP%]{box-shadow:0 6px 6px -3px rgba(0,0,0,.2),0 10px 14px 1px rgba(0,0,0,.14),0 4px 18px 3px rgba(0,0,0,.12)}.mat-elevation-z11[_ngcontent-%COMP%]{box-shadow:0 6px 7px -4px rgba(0,0,0,.2),0 11px 15px 1px rgba(0,0,0,.14),0 4px 20px 3px rgba(0,0,0,.12)}.mat-elevation-z12[_ngcontent-%COMP%]{box-shadow:0 7px 8px -4px rgba(0,0,0,.2),0 12px 17px 2px rgba(0,0,0,.14),0 5px 22px 4px rgba(0,0,0,.12)}.mat-elevation-z13[_ngcontent-%COMP%]{box-shadow:0 7px 8px -4px rgba(0,0,0,.2),0 13px 19px 2px rgba(0,0,0,.14),0 5px 24px 4px rgba(0,0,0,.12)}.mat-elevation-z14[_ngcontent-%COMP%]{box-shadow:0 7px 9px -4px rgba(0,0,0,.2),0 14px 21px 2px rgba(0,0,0,.14),0 5px 26px 4px rgba(0,0,0,.12)}.mat-elevation-z15[_ngcontent-%COMP%]{box-shadow:0 8px 9px -5px rgba(0,0,0,.2),0 15px 22px 2px rgba(0,0,0,.14),0 6px 28px 5px rgba(0,0,0,.12)}.mat-elevation-z16[_ngcontent-%COMP%]{box-shadow:0 8px 10px -5px rgba(0,0,0,.2),0 16px 24px 2px rgba(0,0,0,.14),0 6px 30px 5px rgba(0,0,0,.12)}.mat-elevation-z17[_ngcontent-%COMP%]{box-shadow:0 8px 11px -5px rgba(0,0,0,.2),0 17px 26px 2px rgba(0,0,0,.14),0 6px 32px 5px rgba(0,0,0,.12)}.mat-elevation-z18[_ngcontent-%COMP%]{box-shadow:0 9px 11px -5px rgba(0,0,0,.2),0 18px 28px 2px rgba(0,0,0,.14),0 7px 34px 6px rgba(0,0,0,.12)}.mat-elevation-z19[_ngcontent-%COMP%]{box-shadow:0 9px 12px -6px rgba(0,0,0,.2),0 19px 29px 2px rgba(0,0,0,.14),0 7px 36px 6px rgba(0,0,0,.12)}.mat-elevation-z20[_ngcontent-%COMP%]{box-shadow:0 10px 13px -6px rgba(0,0,0,.2),0 20px 31px 3px rgba(0,0,0,.14),0 8px 38px 7px rgba(0,0,0,.12)}.mat-elevation-z21[_ngcontent-%COMP%]{box-shadow:0 10px 13px -6px rgba(0,0,0,.2),0 21px 33px 3px rgba(0,0,0,.14),0 8px 40px 7px rgba(0,0,0,.12)}.mat-elevation-z22[_ngcontent-%COMP%]{box-shadow:0 10px 14px -6px rgba(0,0,0,.2),0 22px 35px 3px rgba(0,0,0,.14),0 8px 42px 7px rgba(0,0,0,.12)}.mat-elevation-z23[_ngcontent-%COMP%]{box-shadow:0 11px 14px -7px rgba(0,0,0,.2),0 23px 36px 3px rgba(0,0,0,.14),0 9px 44px 8px rgba(0,0,0,.12)}.mat-elevation-z24[_ngcontent-%COMP%]{box-shadow:0 11px 15px -7px rgba(0,0,0,.2),0 24px 38px 3px rgba(0,0,0,.14),0 9px 46px 8px rgba(0,0,0,.12)}.mat-theme-loaded-marker[_ngcontent-%COMP%]{display:none}.mat-autocomplete-panel[_ngcontent-%COMP%]{background:#fff;color:rgba(0,0,0,.87)}.mat-autocomplete-panel[_ngcontent-%COMP%]:not([class*=mat-elevation-z]){box-shadow:0 2px 4px -1px rgba(0,0,0,.2),0 4px 5px 0 rgba(0,0,0,.14),0 1px 10px 0 rgba(0,0,0,.12)}.mat-autocomplete-panel[_ngcontent-%COMP%]   .mat-option.mat-selected[_ngcontent-%COMP%]:not(.mat-active):not(:hover){background:#fff}.mat-autocomplete-panel[_ngcontent-%COMP%]   .mat-option.mat-selected[_ngcontent-%COMP%]:not(.mat-active):not(:hover):not(.mat-option-disabled){color:rgba(0,0,0,.87)}.mat-badge-content[_ngcontent-%COMP%]{color:#fff;background:#3f51b5}.cdk-high-contrast-active[_ngcontent-%COMP%]   .mat-badge-content[_ngcontent-%COMP%]{outline:1px solid;border-radius:0}.mat-badge-accent[_ngcontent-%COMP%]   .mat-badge-content[_ngcontent-%COMP%]{background:#ff4081;color:#fff}.mat-badge-warn[_ngcontent-%COMP%]   .mat-badge-content[_ngcontent-%COMP%]{color:#fff;background:#f44336}.mat-badge[_ngcontent-%COMP%]{position:relative}.mat-badge-hidden[_ngcontent-%COMP%]   .mat-badge-content[_ngcontent-%COMP%]{display:none}.mat-badge-disabled[_ngcontent-%COMP%]   .mat-badge-content[_ngcontent-%COMP%]{background:#b9b9b9;color:rgba(0,0,0,.38)}.mat-badge-content[_ngcontent-%COMP%]{position:absolute;text-align:center;display:inline-block;border-radius:50%;transition:transform .2s ease-in-out;transform:scale(.6);overflow:hidden;white-space:nowrap;text-overflow:ellipsis;pointer-events:none}.mat-badge-content._mat-animation-noopable[_ngcontent-%COMP%], .ng-animate-disabled[_ngcontent-%COMP%]   .mat-badge-content[_ngcontent-%COMP%]{transition:none}.mat-badge-content.mat-badge-active[_ngcontent-%COMP%]{transform:none}.mat-badge-small[_ngcontent-%COMP%]   .mat-badge-content[_ngcontent-%COMP%]{width:16px;height:16px;line-height:16px}.mat-badge-small.mat-badge-above[_ngcontent-%COMP%]   .mat-badge-content[_ngcontent-%COMP%]{top:-8px}.mat-badge-small.mat-badge-below[_ngcontent-%COMP%]   .mat-badge-content[_ngcontent-%COMP%]{bottom:-8px}.mat-badge-small.mat-badge-before[_ngcontent-%COMP%]   .mat-badge-content[_ngcontent-%COMP%]{left:-16px}[dir=rtl][_ngcontent-%COMP%]   .mat-badge-small.mat-badge-before[_ngcontent-%COMP%]   .mat-badge-content[_ngcontent-%COMP%]{left:auto;right:-16px}.mat-badge-small.mat-badge-after[_ngcontent-%COMP%]   .mat-badge-content[_ngcontent-%COMP%]{right:-16px}[dir=rtl][_ngcontent-%COMP%]   .mat-badge-small.mat-badge-after[_ngcontent-%COMP%]   .mat-badge-content[_ngcontent-%COMP%]{right:auto;left:-16px}.mat-badge-small.mat-badge-overlap.mat-badge-before[_ngcontent-%COMP%]   .mat-badge-content[_ngcontent-%COMP%]{left:-8px}[dir=rtl][_ngcontent-%COMP%]   .mat-badge-small.mat-badge-overlap.mat-badge-before[_ngcontent-%COMP%]   .mat-badge-content[_ngcontent-%COMP%]{left:auto;right:-8px}.mat-badge-small.mat-badge-overlap.mat-badge-after[_ngcontent-%COMP%]   .mat-badge-content[_ngcontent-%COMP%]{right:-8px}[dir=rtl][_ngcontent-%COMP%]   .mat-badge-small.mat-badge-overlap.mat-badge-after[_ngcontent-%COMP%]   .mat-badge-content[_ngcontent-%COMP%]{right:auto;left:-8px}.mat-badge-medium[_ngcontent-%COMP%]   .mat-badge-content[_ngcontent-%COMP%]{width:22px;height:22px;line-height:22px}.mat-badge-medium.mat-badge-above[_ngcontent-%COMP%]   .mat-badge-content[_ngcontent-%COMP%]{top:-11px}.mat-badge-medium.mat-badge-below[_ngcontent-%COMP%]   .mat-badge-content[_ngcontent-%COMP%]{bottom:-11px}.mat-badge-medium.mat-badge-before[_ngcontent-%COMP%]   .mat-badge-content[_ngcontent-%COMP%]{left:-22px}[dir=rtl][_ngcontent-%COMP%]   .mat-badge-medium.mat-badge-before[_ngcontent-%COMP%]   .mat-badge-content[_ngcontent-%COMP%]{left:auto;right:-22px}.mat-badge-medium.mat-badge-after[_ngcontent-%COMP%]   .mat-badge-content[_ngcontent-%COMP%]{right:-22px}[dir=rtl][_ngcontent-%COMP%]   .mat-badge-medium.mat-badge-after[_ngcontent-%COMP%]   .mat-badge-content[_ngcontent-%COMP%]{right:auto;left:-22px}.mat-badge-medium.mat-badge-overlap.mat-badge-before[_ngcontent-%COMP%]   .mat-badge-content[_ngcontent-%COMP%]{left:-11px}[dir=rtl][_ngcontent-%COMP%]   .mat-badge-medium.mat-badge-overlap.mat-badge-before[_ngcontent-%COMP%]   .mat-badge-content[_ngcontent-%COMP%]{left:auto;right:-11px}.mat-badge-medium.mat-badge-overlap.mat-badge-after[_ngcontent-%COMP%]   .mat-badge-content[_ngcontent-%COMP%]{right:-11px}[dir=rtl][_ngcontent-%COMP%]   .mat-badge-medium.mat-badge-overlap.mat-badge-after[_ngcontent-%COMP%]   .mat-badge-content[_ngcontent-%COMP%]{right:auto;left:-11px}.mat-badge-large[_ngcontent-%COMP%]   .mat-badge-content[_ngcontent-%COMP%]{width:28px;height:28px;line-height:28px}.mat-badge-large.mat-badge-above[_ngcontent-%COMP%]   .mat-badge-content[_ngcontent-%COMP%]{top:-14px}.mat-badge-large.mat-badge-below[_ngcontent-%COMP%]   .mat-badge-content[_ngcontent-%COMP%]{bottom:-14px}.mat-badge-large.mat-badge-before[_ngcontent-%COMP%]   .mat-badge-content[_ngcontent-%COMP%]{left:-28px}[dir=rtl][_ngcontent-%COMP%]   .mat-badge-large.mat-badge-before[_ngcontent-%COMP%]   .mat-badge-content[_ngcontent-%COMP%]{left:auto;right:-28px}.mat-badge-large.mat-badge-after[_ngcontent-%COMP%]   .mat-badge-content[_ngcontent-%COMP%]{right:-28px}[dir=rtl][_ngcontent-%COMP%]   .mat-badge-large.mat-badge-after[_ngcontent-%COMP%]   .mat-badge-content[_ngcontent-%COMP%]{right:auto;left:-28px}.mat-badge-large.mat-badge-overlap.mat-badge-before[_ngcontent-%COMP%]   .mat-badge-content[_ngcontent-%COMP%]{left:-14px}[dir=rtl][_ngcontent-%COMP%]   .mat-badge-large.mat-badge-overlap.mat-badge-before[_ngcontent-%COMP%]   .mat-badge-content[_ngcontent-%COMP%]{left:auto;right:-14px}.mat-badge-large.mat-badge-overlap.mat-badge-after[_ngcontent-%COMP%]   .mat-badge-content[_ngcontent-%COMP%]{right:-14px}[dir=rtl][_ngcontent-%COMP%]   .mat-badge-large.mat-badge-overlap.mat-badge-after[_ngcontent-%COMP%]   .mat-badge-content[_ngcontent-%COMP%]{right:auto;left:-14px}.mat-bottom-sheet-container[_ngcontent-%COMP%]{box-shadow:0 8px 10px -5px rgba(0,0,0,.2),0 16px 24px 2px rgba(0,0,0,.14),0 6px 30px 5px rgba(0,0,0,.12);background:#fff;color:rgba(0,0,0,.87)}.mat-button[_ngcontent-%COMP%], .mat-icon-button[_ngcontent-%COMP%], .mat-stroked-button[_ngcontent-%COMP%]{color:inherit;background:transparent}.mat-button.mat-primary[_ngcontent-%COMP%], .mat-icon-button.mat-primary[_ngcontent-%COMP%], .mat-stroked-button.mat-primary[_ngcontent-%COMP%]{color:#3f51b5}.mat-button.mat-accent[_ngcontent-%COMP%], .mat-icon-button.mat-accent[_ngcontent-%COMP%], .mat-stroked-button.mat-accent[_ngcontent-%COMP%]{color:#ff4081}.mat-button.mat-warn[_ngcontent-%COMP%], .mat-icon-button.mat-warn[_ngcontent-%COMP%], .mat-stroked-button.mat-warn[_ngcontent-%COMP%]{color:#f44336}.mat-button.mat-accent.mat-button-disabled[_ngcontent-%COMP%], .mat-button.mat-button-disabled.mat-button-disabled[_ngcontent-%COMP%], .mat-button.mat-primary.mat-button-disabled[_ngcontent-%COMP%], .mat-button.mat-warn.mat-button-disabled[_ngcontent-%COMP%], .mat-icon-button.mat-accent.mat-button-disabled[_ngcontent-%COMP%], .mat-icon-button.mat-button-disabled.mat-button-disabled[_ngcontent-%COMP%], .mat-icon-button.mat-primary.mat-button-disabled[_ngcontent-%COMP%], .mat-icon-button.mat-warn.mat-button-disabled[_ngcontent-%COMP%], .mat-stroked-button.mat-accent.mat-button-disabled[_ngcontent-%COMP%], .mat-stroked-button.mat-button-disabled.mat-button-disabled[_ngcontent-%COMP%], .mat-stroked-button.mat-primary.mat-button-disabled[_ngcontent-%COMP%], .mat-stroked-button.mat-warn.mat-button-disabled[_ngcontent-%COMP%]{color:rgba(0,0,0,.26)}.mat-button.mat-primary[_ngcontent-%COMP%]   .mat-button-focus-overlay[_ngcontent-%COMP%], .mat-icon-button.mat-primary[_ngcontent-%COMP%]   .mat-button-focus-overlay[_ngcontent-%COMP%], .mat-stroked-button.mat-primary[_ngcontent-%COMP%]   .mat-button-focus-overlay[_ngcontent-%COMP%]{background-color:#3f51b5}.mat-button.mat-accent[_ngcontent-%COMP%]   .mat-button-focus-overlay[_ngcontent-%COMP%], .mat-icon-button.mat-accent[_ngcontent-%COMP%]   .mat-button-focus-overlay[_ngcontent-%COMP%], .mat-stroked-button.mat-accent[_ngcontent-%COMP%]   .mat-button-focus-overlay[_ngcontent-%COMP%]{background-color:#ff4081}.mat-button.mat-warn[_ngcontent-%COMP%]   .mat-button-focus-overlay[_ngcontent-%COMP%], .mat-icon-button.mat-warn[_ngcontent-%COMP%]   .mat-button-focus-overlay[_ngcontent-%COMP%], .mat-stroked-button.mat-warn[_ngcontent-%COMP%]   .mat-button-focus-overlay[_ngcontent-%COMP%]{background-color:#f44336}.mat-button.mat-button-disabled[_ngcontent-%COMP%]   .mat-button-focus-overlay[_ngcontent-%COMP%], .mat-icon-button.mat-button-disabled[_ngcontent-%COMP%]   .mat-button-focus-overlay[_ngcontent-%COMP%], .mat-stroked-button.mat-button-disabled[_ngcontent-%COMP%]   .mat-button-focus-overlay[_ngcontent-%COMP%]{background-color:initial}.mat-button[_ngcontent-%COMP%]   .mat-ripple-element[_ngcontent-%COMP%], .mat-icon-button[_ngcontent-%COMP%]   .mat-ripple-element[_ngcontent-%COMP%], .mat-stroked-button[_ngcontent-%COMP%]   .mat-ripple-element[_ngcontent-%COMP%]{opacity:.1;background-color:currentColor}.mat-button-focus-overlay[_ngcontent-%COMP%]{background:#000}.mat-stroked-button[_ngcontent-%COMP%]:not(.mat-button-disabled){border-color:rgba(0,0,0,.12)}.mat-fab[_ngcontent-%COMP%], .mat-flat-button[_ngcontent-%COMP%], .mat-mini-fab[_ngcontent-%COMP%], .mat-raised-button[_ngcontent-%COMP%]{color:rgba(0,0,0,.87);background-color:#fff}.mat-fab.mat-accent[_ngcontent-%COMP%], .mat-fab.mat-primary[_ngcontent-%COMP%], .mat-fab.mat-warn[_ngcontent-%COMP%], .mat-flat-button.mat-accent[_ngcontent-%COMP%], .mat-flat-button.mat-primary[_ngcontent-%COMP%], .mat-flat-button.mat-warn[_ngcontent-%COMP%], .mat-mini-fab.mat-accent[_ngcontent-%COMP%], .mat-mini-fab.mat-primary[_ngcontent-%COMP%], .mat-mini-fab.mat-warn[_ngcontent-%COMP%], .mat-raised-button.mat-accent[_ngcontent-%COMP%], .mat-raised-button.mat-primary[_ngcontent-%COMP%], .mat-raised-button.mat-warn[_ngcontent-%COMP%]{color:#fff}.mat-fab.mat-accent.mat-button-disabled[_ngcontent-%COMP%], .mat-fab.mat-button-disabled.mat-button-disabled[_ngcontent-%COMP%], .mat-fab.mat-primary.mat-button-disabled[_ngcontent-%COMP%], .mat-fab.mat-warn.mat-button-disabled[_ngcontent-%COMP%], .mat-flat-button.mat-accent.mat-button-disabled[_ngcontent-%COMP%], .mat-flat-button.mat-button-disabled.mat-button-disabled[_ngcontent-%COMP%], .mat-flat-button.mat-primary.mat-button-disabled[_ngcontent-%COMP%], .mat-flat-button.mat-warn.mat-button-disabled[_ngcontent-%COMP%], .mat-mini-fab.mat-accent.mat-button-disabled[_ngcontent-%COMP%], .mat-mini-fab.mat-button-disabled.mat-button-disabled[_ngcontent-%COMP%], .mat-mini-fab.mat-primary.mat-button-disabled[_ngcontent-%COMP%], .mat-mini-fab.mat-warn.mat-button-disabled[_ngcontent-%COMP%], .mat-raised-button.mat-accent.mat-button-disabled[_ngcontent-%COMP%], .mat-raised-button.mat-button-disabled.mat-button-disabled[_ngcontent-%COMP%], .mat-raised-button.mat-primary.mat-button-disabled[_ngcontent-%COMP%], .mat-raised-button.mat-warn.mat-button-disabled[_ngcontent-%COMP%]{color:rgba(0,0,0,.26)}.mat-fab.mat-primary[_ngcontent-%COMP%], .mat-flat-button.mat-primary[_ngcontent-%COMP%], .mat-mini-fab.mat-primary[_ngcontent-%COMP%], .mat-raised-button.mat-primary[_ngcontent-%COMP%]{background-color:#3f51b5}.mat-fab.mat-accent[_ngcontent-%COMP%], .mat-flat-button.mat-accent[_ngcontent-%COMP%], .mat-mini-fab.mat-accent[_ngcontent-%COMP%], .mat-raised-button.mat-accent[_ngcontent-%COMP%]{background-color:#ff4081}.mat-fab.mat-warn[_ngcontent-%COMP%], .mat-flat-button.mat-warn[_ngcontent-%COMP%], .mat-mini-fab.mat-warn[_ngcontent-%COMP%], .mat-raised-button.mat-warn[_ngcontent-%COMP%]{background-color:#f44336}.mat-fab.mat-accent.mat-button-disabled[_ngcontent-%COMP%], .mat-fab.mat-button-disabled.mat-button-disabled[_ngcontent-%COMP%], .mat-fab.mat-primary.mat-button-disabled[_ngcontent-%COMP%], .mat-fab.mat-warn.mat-button-disabled[_ngcontent-%COMP%], .mat-flat-button.mat-accent.mat-button-disabled[_ngcontent-%COMP%], .mat-flat-button.mat-button-disabled.mat-button-disabled[_ngcontent-%COMP%], .mat-flat-button.mat-primary.mat-button-disabled[_ngcontent-%COMP%], .mat-flat-button.mat-warn.mat-button-disabled[_ngcontent-%COMP%], .mat-mini-fab.mat-accent.mat-button-disabled[_ngcontent-%COMP%], .mat-mini-fab.mat-button-disabled.mat-button-disabled[_ngcontent-%COMP%], .mat-mini-fab.mat-primary.mat-button-disabled[_ngcontent-%COMP%], .mat-mini-fab.mat-warn.mat-button-disabled[_ngcontent-%COMP%], .mat-raised-button.mat-accent.mat-button-disabled[_ngcontent-%COMP%], .mat-raised-button.mat-button-disabled.mat-button-disabled[_ngcontent-%COMP%], .mat-raised-button.mat-primary.mat-button-disabled[_ngcontent-%COMP%], .mat-raised-button.mat-warn.mat-button-disabled[_ngcontent-%COMP%]{background-color:rgba(0,0,0,.12)}.mat-fab.mat-accent[_ngcontent-%COMP%]   .mat-ripple-element[_ngcontent-%COMP%], .mat-fab.mat-primary[_ngcontent-%COMP%]   .mat-ripple-element[_ngcontent-%COMP%], .mat-fab.mat-warn[_ngcontent-%COMP%]   .mat-ripple-element[_ngcontent-%COMP%], .mat-flat-button.mat-accent[_ngcontent-%COMP%]   .mat-ripple-element[_ngcontent-%COMP%], .mat-flat-button.mat-primary[_ngcontent-%COMP%]   .mat-ripple-element[_ngcontent-%COMP%], .mat-flat-button.mat-warn[_ngcontent-%COMP%]   .mat-ripple-element[_ngcontent-%COMP%], .mat-mini-fab.mat-accent[_ngcontent-%COMP%]   .mat-ripple-element[_ngcontent-%COMP%], .mat-mini-fab.mat-primary[_ngcontent-%COMP%]   .mat-ripple-element[_ngcontent-%COMP%], .mat-mini-fab.mat-warn[_ngcontent-%COMP%]   .mat-ripple-element[_ngcontent-%COMP%], .mat-raised-button.mat-accent[_ngcontent-%COMP%]   .mat-ripple-element[_ngcontent-%COMP%], .mat-raised-button.mat-primary[_ngcontent-%COMP%]   .mat-ripple-element[_ngcontent-%COMP%], .mat-raised-button.mat-warn[_ngcontent-%COMP%]   .mat-ripple-element[_ngcontent-%COMP%]{background-color:hsla(0,0%,100%,.1)}.mat-flat-button[_ngcontent-%COMP%]:not([class*=mat-elevation-z]), .mat-stroked-button[_ngcontent-%COMP%]:not([class*=mat-elevation-z]){box-shadow:0 0 0 0 rgba(0,0,0,.2),0 0 0 0 rgba(0,0,0,.14),0 0 0 0 rgba(0,0,0,.12)}.mat-raised-button[_ngcontent-%COMP%]:not([class*=mat-elevation-z]){box-shadow:0 3px 1px -2px rgba(0,0,0,.2),0 2px 2px 0 rgba(0,0,0,.14),0 1px 5px 0 rgba(0,0,0,.12)}.mat-raised-button[_ngcontent-%COMP%]:not(.mat-button-disabled):active:not([class*=mat-elevation-z]){box-shadow:0 5px 5px -3px rgba(0,0,0,.2),0 8px 10px 1px rgba(0,0,0,.14),0 3px 14px 2px rgba(0,0,0,.12)}.mat-raised-button.mat-button-disabled[_ngcontent-%COMP%]:not([class*=mat-elevation-z]){box-shadow:0 0 0 0 rgba(0,0,0,.2),0 0 0 0 rgba(0,0,0,.14),0 0 0 0 rgba(0,0,0,.12)}.mat-fab[_ngcontent-%COMP%]:not([class*=mat-elevation-z]), .mat-mini-fab[_ngcontent-%COMP%]:not([class*=mat-elevation-z]){box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.mat-fab[_ngcontent-%COMP%]:not(.mat-button-disabled):active:not([class*=mat-elevation-z]), .mat-mini-fab[_ngcontent-%COMP%]:not(.mat-button-disabled):active:not([class*=mat-elevation-z]){box-shadow:0 7px 8px -4px rgba(0,0,0,.2),0 12px 17px 2px rgba(0,0,0,.14),0 5px 22px 4px rgba(0,0,0,.12)}.mat-fab.mat-button-disabled[_ngcontent-%COMP%]:not([class*=mat-elevation-z]), .mat-mini-fab.mat-button-disabled[_ngcontent-%COMP%]:not([class*=mat-elevation-z]){box-shadow:0 0 0 0 rgba(0,0,0,.2),0 0 0 0 rgba(0,0,0,.14),0 0 0 0 rgba(0,0,0,.12)}.mat-button-toggle-group[_ngcontent-%COMP%], .mat-button-toggle-standalone[_ngcontent-%COMP%]{box-shadow:0 3px 1px -2px rgba(0,0,0,.2),0 2px 2px 0 rgba(0,0,0,.14),0 1px 5px 0 rgba(0,0,0,.12)}.mat-button-toggle-group-appearance-standard[_ngcontent-%COMP%], .mat-button-toggle-standalone.mat-button-toggle-appearance-standard[_ngcontent-%COMP%]{box-shadow:none}.mat-button-toggle[_ngcontent-%COMP%]{color:rgba(0,0,0,.38)}.mat-button-toggle[_ngcontent-%COMP%]   .mat-button-toggle-focus-overlay[_ngcontent-%COMP%]{background-color:rgba(0,0,0,.12)}.mat-button-toggle-appearance-standard[_ngcontent-%COMP%]{color:rgba(0,0,0,.87);background:#fff}.mat-button-toggle-appearance-standard[_ngcontent-%COMP%]   .mat-button-toggle-focus-overlay[_ngcontent-%COMP%]{background-color:#000}.mat-button-toggle-group-appearance-standard[_ngcontent-%COMP%]   .mat-button-toggle[_ngcontent-%COMP%] + .mat-button-toggle[_ngcontent-%COMP%]{border-left:1px solid rgba(0,0,0,.12)}[dir=rtl][_ngcontent-%COMP%]   .mat-button-toggle-group-appearance-standard[_ngcontent-%COMP%]   .mat-button-toggle[_ngcontent-%COMP%] + .mat-button-toggle[_ngcontent-%COMP%]{border-left:none;border-right:1px solid rgba(0,0,0,.12)}.mat-button-toggle-group-appearance-standard.mat-button-toggle-vertical[_ngcontent-%COMP%]   .mat-button-toggle[_ngcontent-%COMP%] + .mat-button-toggle[_ngcontent-%COMP%]{border-left:none;border-right:none;border-top:1px solid rgba(0,0,0,.12)}.mat-button-toggle-checked[_ngcontent-%COMP%]{background-color:#e0e0e0;color:rgba(0,0,0,.54)}.mat-button-toggle-checked.mat-button-toggle-appearance-standard[_ngcontent-%COMP%]{color:rgba(0,0,0,.87)}.mat-button-toggle-disabled[_ngcontent-%COMP%]{color:rgba(0,0,0,.26);background-color:#eee}.mat-button-toggle-disabled.mat-button-toggle-appearance-standard[_ngcontent-%COMP%]{background:#fff}.mat-button-toggle-disabled.mat-button-toggle-checked[_ngcontent-%COMP%]{background-color:#bdbdbd}.mat-button-toggle-group-appearance-standard[_ngcontent-%COMP%], .mat-button-toggle-standalone.mat-button-toggle-appearance-standard[_ngcontent-%COMP%]{border:1px solid rgba(0,0,0,.12)}.mat-button-toggle-appearance-standard[_ngcontent-%COMP%]   .mat-button-toggle-label-content[_ngcontent-%COMP%]{line-height:48px}.mat-card[_ngcontent-%COMP%]{background:#fff;color:rgba(0,0,0,.87)}.mat-card[_ngcontent-%COMP%]:not([class*=mat-elevation-z]){box-shadow:0 2px 1px -1px rgba(0,0,0,.2),0 1px 1px 0 rgba(0,0,0,.14),0 1px 3px 0 rgba(0,0,0,.12)}.mat-card.mat-card-flat[_ngcontent-%COMP%]:not([class*=mat-elevation-z]){box-shadow:0 0 0 0 rgba(0,0,0,.2),0 0 0 0 rgba(0,0,0,.14),0 0 0 0 rgba(0,0,0,.12)}.mat-card-subtitle[_ngcontent-%COMP%]{color:rgba(0,0,0,.54)}.mat-checkbox-frame[_ngcontent-%COMP%]{border-color:rgba(0,0,0,.54)}.mat-checkbox-checkmark[_ngcontent-%COMP%]{fill:#fafafa}.mat-checkbox-checkmark-path[_ngcontent-%COMP%]{stroke:#fafafa!important}.mat-checkbox-mixedmark[_ngcontent-%COMP%]{background-color:#fafafa}.mat-checkbox-checked.mat-primary[_ngcontent-%COMP%]   .mat-checkbox-background[_ngcontent-%COMP%], .mat-checkbox-indeterminate.mat-primary[_ngcontent-%COMP%]   .mat-checkbox-background[_ngcontent-%COMP%]{background-color:#3f51b5}.mat-checkbox-checked.mat-accent[_ngcontent-%COMP%]   .mat-checkbox-background[_ngcontent-%COMP%], .mat-checkbox-indeterminate.mat-accent[_ngcontent-%COMP%]   .mat-checkbox-background[_ngcontent-%COMP%]{background-color:#ff4081}.mat-checkbox-checked.mat-warn[_ngcontent-%COMP%]   .mat-checkbox-background[_ngcontent-%COMP%], .mat-checkbox-indeterminate.mat-warn[_ngcontent-%COMP%]   .mat-checkbox-background[_ngcontent-%COMP%]{background-color:#f44336}.mat-checkbox-disabled.mat-checkbox-checked[_ngcontent-%COMP%]   .mat-checkbox-background[_ngcontent-%COMP%], .mat-checkbox-disabled.mat-checkbox-indeterminate[_ngcontent-%COMP%]   .mat-checkbox-background[_ngcontent-%COMP%]{background-color:#b0b0b0}.mat-checkbox-disabled[_ngcontent-%COMP%]:not(.mat-checkbox-checked)   .mat-checkbox-frame[_ngcontent-%COMP%]{border-color:#b0b0b0}.mat-checkbox-disabled[_ngcontent-%COMP%]   .mat-checkbox-label[_ngcontent-%COMP%]{color:rgba(0,0,0,.54)}.mat-checkbox[_ngcontent-%COMP%]   .mat-ripple-element[_ngcontent-%COMP%]{background-color:#000}.mat-checkbox-checked[_ngcontent-%COMP%]:not(.mat-checkbox-disabled).mat-primary   .mat-ripple-element[_ngcontent-%COMP%], .mat-checkbox[_ngcontent-%COMP%]:active:not(.mat-checkbox-disabled).mat-primary   .mat-ripple-element[_ngcontent-%COMP%]{background:#3f51b5}.mat-checkbox-checked[_ngcontent-%COMP%]:not(.mat-checkbox-disabled).mat-accent   .mat-ripple-element[_ngcontent-%COMP%], .mat-checkbox[_ngcontent-%COMP%]:active:not(.mat-checkbox-disabled).mat-accent   .mat-ripple-element[_ngcontent-%COMP%]{background:#ff4081}.mat-checkbox-checked[_ngcontent-%COMP%]:not(.mat-checkbox-disabled).mat-warn   .mat-ripple-element[_ngcontent-%COMP%], .mat-checkbox[_ngcontent-%COMP%]:active:not(.mat-checkbox-disabled).mat-warn   .mat-ripple-element[_ngcontent-%COMP%]{background:#f44336}.mat-chip.mat-standard-chip[_ngcontent-%COMP%]{background-color:#e0e0e0;color:rgba(0,0,0,.87)}.mat-chip.mat-standard-chip[_ngcontent-%COMP%]   .mat-chip-remove[_ngcontent-%COMP%]{color:rgba(0,0,0,.87);opacity:.4}.mat-chip.mat-standard-chip[_ngcontent-%COMP%]:not(.mat-chip-disabled):active{box-shadow:0 3px 3px -2px rgba(0,0,0,.2),0 3px 4px 0 rgba(0,0,0,.14),0 1px 8px 0 rgba(0,0,0,.12)}.mat-chip.mat-standard-chip[_ngcontent-%COMP%]:not(.mat-chip-disabled)   .mat-chip-remove[_ngcontent-%COMP%]:hover{opacity:.54}.mat-chip.mat-standard-chip.mat-chip-disabled[_ngcontent-%COMP%]{opacity:.4}.mat-chip.mat-standard-chip[_ngcontent-%COMP%]:after{background:#000}.mat-chip.mat-standard-chip.mat-chip-selected.mat-primary[_ngcontent-%COMP%]{background-color:#3f51b5;color:#fff}.mat-chip.mat-standard-chip.mat-chip-selected.mat-primary[_ngcontent-%COMP%]   .mat-chip-remove[_ngcontent-%COMP%]{color:#fff;opacity:.4}.mat-chip.mat-standard-chip.mat-chip-selected.mat-primary[_ngcontent-%COMP%]   .mat-ripple-element[_ngcontent-%COMP%]{background-color:hsla(0,0%,100%,.1)}.mat-chip.mat-standard-chip.mat-chip-selected.mat-warn[_ngcontent-%COMP%]{background-color:#f44336;color:#fff}.mat-chip.mat-standard-chip.mat-chip-selected.mat-warn[_ngcontent-%COMP%]   .mat-chip-remove[_ngcontent-%COMP%]{color:#fff;opacity:.4}.mat-chip.mat-standard-chip.mat-chip-selected.mat-warn[_ngcontent-%COMP%]   .mat-ripple-element[_ngcontent-%COMP%]{background-color:hsla(0,0%,100%,.1)}.mat-chip.mat-standard-chip.mat-chip-selected.mat-accent[_ngcontent-%COMP%]{background-color:#ff4081;color:#fff}.mat-chip.mat-standard-chip.mat-chip-selected.mat-accent[_ngcontent-%COMP%]   .mat-chip-remove[_ngcontent-%COMP%]{color:#fff;opacity:.4}.mat-chip.mat-standard-chip.mat-chip-selected.mat-accent[_ngcontent-%COMP%]   .mat-ripple-element[_ngcontent-%COMP%]{background-color:hsla(0,0%,100%,.1)}.mat-table[_ngcontent-%COMP%]{background:#fff}.mat-table-sticky[_ngcontent-%COMP%], .mat-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%], .mat-table[_ngcontent-%COMP%]   tfoot[_ngcontent-%COMP%], .mat-table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%], [mat-footer-row][_ngcontent-%COMP%], [mat-header-row][_ngcontent-%COMP%], [mat-row][_ngcontent-%COMP%], mat-footer-row[_ngcontent-%COMP%], mat-header-row[_ngcontent-%COMP%], mat-row[_ngcontent-%COMP%]{background:inherit}mat-footer-row[_ngcontent-%COMP%], mat-header-row[_ngcontent-%COMP%], mat-row[_ngcontent-%COMP%], td.mat-cell[_ngcontent-%COMP%], td.mat-footer-cell[_ngcontent-%COMP%], th.mat-header-cell[_ngcontent-%COMP%]{border-bottom-color:rgba(0,0,0,.12)}.mat-header-cell[_ngcontent-%COMP%]{color:rgba(0,0,0,.54)}.mat-cell[_ngcontent-%COMP%], .mat-footer-cell[_ngcontent-%COMP%]{color:rgba(0,0,0,.87)}.mat-calendar-arrow[_ngcontent-%COMP%]{border-top-color:rgba(0,0,0,.54)}.mat-datepicker-content[_ngcontent-%COMP%]   .mat-calendar-next-button[_ngcontent-%COMP%], .mat-datepicker-content[_ngcontent-%COMP%]   .mat-calendar-previous-button[_ngcontent-%COMP%], .mat-datepicker-toggle[_ngcontent-%COMP%]{color:rgba(0,0,0,.54)}.mat-calendar-table-header[_ngcontent-%COMP%]{color:rgba(0,0,0,.38)}.mat-calendar-table-header-divider[_ngcontent-%COMP%]:after{background:rgba(0,0,0,.12)}.mat-calendar-body-label[_ngcontent-%COMP%]{color:rgba(0,0,0,.54)}.mat-calendar-body-cell-content[_ngcontent-%COMP%], .mat-date-range-input-separator[_ngcontent-%COMP%]{color:rgba(0,0,0,.87);border-color:transparent}.mat-calendar-body-disabled[_ngcontent-%COMP%] > .mat-calendar-body-cell-content[_ngcontent-%COMP%]:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical), .mat-form-field-disabled[_ngcontent-%COMP%]   .mat-date-range-input-separator[_ngcontent-%COMP%]{color:rgba(0,0,0,.38)}.cdk-keyboard-focused[_ngcontent-%COMP%]   .mat-calendar-body-active[_ngcontent-%COMP%] > .mat-calendar-body-cell-content[_ngcontent-%COMP%]:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical), .cdk-program-focused[_ngcontent-%COMP%]   .mat-calendar-body-active[_ngcontent-%COMP%] > .mat-calendar-body-cell-content[_ngcontent-%COMP%]:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical), .mat-calendar-body-cell[_ngcontent-%COMP%]:not(.mat-calendar-body-disabled):hover > .mat-calendar-body-cell-content[_ngcontent-%COMP%]:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){background-color:rgba(0,0,0,.04)}.mat-calendar-body-in-preview[_ngcontent-%COMP%]{color:rgba(0,0,0,.24)}.mat-calendar-body-today[_ngcontent-%COMP%]:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){border-color:rgba(0,0,0,.38)}.mat-calendar-body-disabled[_ngcontent-%COMP%] > .mat-calendar-body-today[_ngcontent-%COMP%]:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical){border-color:rgba(0,0,0,.18)}.mat-calendar-body-in-range[_ngcontent-%COMP%]:before{background:rgba(63,81,181,.2)}.mat-calendar-body-comparison-identical[_ngcontent-%COMP%], .mat-calendar-body-in-comparison-range[_ngcontent-%COMP%]:before{background:rgba(249,171,0,.2)}.mat-calendar-body-comparison-bridge-start[_ngcontent-%COMP%]:before, [dir=rtl][_ngcontent-%COMP%]   .mat-calendar-body-comparison-bridge-end[_ngcontent-%COMP%]:before{background:linear-gradient(90deg,rgba(63,81,181,.2) 50%,rgba(249,171,0,.2) 0)}.mat-calendar-body-comparison-bridge-end[_ngcontent-%COMP%]:before, [dir=rtl][_ngcontent-%COMP%]   .mat-calendar-body-comparison-bridge-start[_ngcontent-%COMP%]:before{background:linear-gradient(270deg,rgba(63,81,181,.2) 50%,rgba(249,171,0,.2) 0)}.mat-calendar-body-in-comparison-range.mat-calendar-body-in-range[_ngcontent-%COMP%]:after, .mat-calendar-body-in-range[_ngcontent-%COMP%] > .mat-calendar-body-comparison-identical[_ngcontent-%COMP%]{background:#a8dab5}.mat-calendar-body-comparison-identical.mat-calendar-body-selected[_ngcontent-%COMP%], .mat-calendar-body-in-comparison-range[_ngcontent-%COMP%] > .mat-calendar-body-selected[_ngcontent-%COMP%]{background:#46a35e}.mat-calendar-body-selected[_ngcontent-%COMP%]{background-color:#3f51b5;color:#fff}.mat-calendar-body-disabled[_ngcontent-%COMP%] > .mat-calendar-body-selected[_ngcontent-%COMP%]{background-color:rgba(63,81,181,.4)}.mat-calendar-body-today.mat-calendar-body-selected[_ngcontent-%COMP%]{box-shadow:inset 0 0 0 1px #fff}.mat-datepicker-content[_ngcontent-%COMP%]{box-shadow:0 2px 4px -1px rgba(0,0,0,.2),0 4px 5px 0 rgba(0,0,0,.14),0 1px 10px 0 rgba(0,0,0,.12);background-color:#fff;color:rgba(0,0,0,.87)}.mat-datepicker-content.mat-accent[_ngcontent-%COMP%]   .mat-calendar-body-in-range[_ngcontent-%COMP%]:before{background:rgba(255,64,129,.2)}.mat-datepicker-content.mat-accent[_ngcontent-%COMP%]   .mat-calendar-body-comparison-identical[_ngcontent-%COMP%], .mat-datepicker-content.mat-accent[_ngcontent-%COMP%]   .mat-calendar-body-in-comparison-range[_ngcontent-%COMP%]:before{background:rgba(249,171,0,.2)}.mat-datepicker-content.mat-accent[_ngcontent-%COMP%]   .mat-calendar-body-comparison-bridge-start[_ngcontent-%COMP%]:before, .mat-datepicker-content.mat-accent[_ngcontent-%COMP%]   [dir=rtl][_ngcontent-%COMP%]   .mat-calendar-body-comparison-bridge-end[_ngcontent-%COMP%]:before{background:linear-gradient(90deg,rgba(255,64,129,.2) 50%,rgba(249,171,0,.2) 0)}.mat-datepicker-content.mat-accent[_ngcontent-%COMP%]   .mat-calendar-body-comparison-bridge-end[_ngcontent-%COMP%]:before, .mat-datepicker-content.mat-accent[_ngcontent-%COMP%]   [dir=rtl][_ngcontent-%COMP%]   .mat-calendar-body-comparison-bridge-start[_ngcontent-%COMP%]:before{background:linear-gradient(270deg,rgba(255,64,129,.2) 50%,rgba(249,171,0,.2) 0)}.mat-datepicker-content.mat-accent[_ngcontent-%COMP%]   .mat-calendar-body-in-comparison-range.mat-calendar-body-in-range[_ngcontent-%COMP%]:after, .mat-datepicker-content.mat-accent[_ngcontent-%COMP%]   .mat-calendar-body-in-range[_ngcontent-%COMP%] > .mat-calendar-body-comparison-identical[_ngcontent-%COMP%]{background:#a8dab5}.mat-datepicker-content.mat-accent[_ngcontent-%COMP%]   .mat-calendar-body-comparison-identical.mat-calendar-body-selected[_ngcontent-%COMP%], .mat-datepicker-content.mat-accent[_ngcontent-%COMP%]   .mat-calendar-body-in-comparison-range[_ngcontent-%COMP%] > .mat-calendar-body-selected[_ngcontent-%COMP%]{background:#46a35e}.mat-datepicker-content.mat-accent[_ngcontent-%COMP%]   .mat-calendar-body-selected[_ngcontent-%COMP%]{background-color:#ff4081;color:#fff}.mat-datepicker-content.mat-accent[_ngcontent-%COMP%]   .mat-calendar-body-disabled[_ngcontent-%COMP%] > .mat-calendar-body-selected[_ngcontent-%COMP%]{background-color:rgba(255,64,129,.4)}.mat-datepicker-content.mat-accent[_ngcontent-%COMP%]   .mat-calendar-body-today.mat-calendar-body-selected[_ngcontent-%COMP%]{box-shadow:inset 0 0 0 1px #fff}.mat-datepicker-content.mat-warn[_ngcontent-%COMP%]   .mat-calendar-body-in-range[_ngcontent-%COMP%]:before{background:rgba(244,67,54,.2)}.mat-datepicker-content.mat-warn[_ngcontent-%COMP%]   .mat-calendar-body-comparison-identical[_ngcontent-%COMP%], .mat-datepicker-content.mat-warn[_ngcontent-%COMP%]   .mat-calendar-body-in-comparison-range[_ngcontent-%COMP%]:before{background:rgba(249,171,0,.2)}.mat-datepicker-content.mat-warn[_ngcontent-%COMP%]   .mat-calendar-body-comparison-bridge-start[_ngcontent-%COMP%]:before, .mat-datepicker-content.mat-warn[_ngcontent-%COMP%]   [dir=rtl][_ngcontent-%COMP%]   .mat-calendar-body-comparison-bridge-end[_ngcontent-%COMP%]:before{background:linear-gradient(90deg,rgba(244,67,54,.2) 50%,rgba(249,171,0,.2) 0)}.mat-datepicker-content.mat-warn[_ngcontent-%COMP%]   .mat-calendar-body-comparison-bridge-end[_ngcontent-%COMP%]:before, .mat-datepicker-content.mat-warn[_ngcontent-%COMP%]   [dir=rtl][_ngcontent-%COMP%]   .mat-calendar-body-comparison-bridge-start[_ngcontent-%COMP%]:before{background:linear-gradient(270deg,rgba(244,67,54,.2) 50%,rgba(249,171,0,.2) 0)}.mat-datepicker-content.mat-warn[_ngcontent-%COMP%]   .mat-calendar-body-in-comparison-range.mat-calendar-body-in-range[_ngcontent-%COMP%]:after, .mat-datepicker-content.mat-warn[_ngcontent-%COMP%]   .mat-calendar-body-in-range[_ngcontent-%COMP%] > .mat-calendar-body-comparison-identical[_ngcontent-%COMP%]{background:#a8dab5}.mat-datepicker-content.mat-warn[_ngcontent-%COMP%]   .mat-calendar-body-comparison-identical.mat-calendar-body-selected[_ngcontent-%COMP%], .mat-datepicker-content.mat-warn[_ngcontent-%COMP%]   .mat-calendar-body-in-comparison-range[_ngcontent-%COMP%] > .mat-calendar-body-selected[_ngcontent-%COMP%]{background:#46a35e}.mat-datepicker-content.mat-warn[_ngcontent-%COMP%]   .mat-calendar-body-selected[_ngcontent-%COMP%]{background-color:#f44336;color:#fff}.mat-datepicker-content.mat-warn[_ngcontent-%COMP%]   .mat-calendar-body-disabled[_ngcontent-%COMP%] > .mat-calendar-body-selected[_ngcontent-%COMP%]{background-color:rgba(244,67,54,.4)}.mat-datepicker-content.mat-warn[_ngcontent-%COMP%]   .mat-calendar-body-today.mat-calendar-body-selected[_ngcontent-%COMP%]{box-shadow:inset 0 0 0 1px #fff}.mat-datepicker-content-touch[_ngcontent-%COMP%]{box-shadow:0 0 0 0 rgba(0,0,0,.2),0 0 0 0 rgba(0,0,0,.14),0 0 0 0 rgba(0,0,0,.12)}.mat-datepicker-toggle-active[_ngcontent-%COMP%]{color:#3f51b5}.mat-datepicker-toggle-active.mat-accent[_ngcontent-%COMP%]{color:#ff4081}.mat-datepicker-toggle-active.mat-warn[_ngcontent-%COMP%]{color:#f44336}.mat-date-range-input-inner[disabled][_ngcontent-%COMP%]{color:rgba(0,0,0,.38)}.mat-dialog-container[_ngcontent-%COMP%]{box-shadow:0 11px 15px -7px rgba(0,0,0,.2),0 24px 38px 3px rgba(0,0,0,.14),0 9px 46px 8px rgba(0,0,0,.12);background:#fff;color:rgba(0,0,0,.87)}.mat-divider[_ngcontent-%COMP%]{border-top-color:rgba(0,0,0,.12)}.mat-divider-vertical[_ngcontent-%COMP%]{border-right-color:rgba(0,0,0,.12)}.mat-expansion-panel[_ngcontent-%COMP%]{background:#fff;color:rgba(0,0,0,.87)}.mat-expansion-panel[_ngcontent-%COMP%]:not([class*=mat-elevation-z]){box-shadow:0 3px 1px -2px rgba(0,0,0,.2),0 2px 2px 0 rgba(0,0,0,.14),0 1px 5px 0 rgba(0,0,0,.12)}.mat-action-row[_ngcontent-%COMP%]{border-top-color:rgba(0,0,0,.12)}.mat-expansion-panel[_ngcontent-%COMP%]   .mat-expansion-panel-header.cdk-keyboard-focused[_ngcontent-%COMP%]:not([aria-disabled=true]), .mat-expansion-panel[_ngcontent-%COMP%]   .mat-expansion-panel-header.cdk-program-focused[_ngcontent-%COMP%]:not([aria-disabled=true]), .mat-expansion-panel[_ngcontent-%COMP%]:not(.mat-expanded)   .mat-expansion-panel-header[_ngcontent-%COMP%]:hover:not([aria-disabled=true]){background:rgba(0,0,0,.04)}@media(hover:none){.mat-expansion-panel[_ngcontent-%COMP%]:not(.mat-expanded):not([aria-disabled=true])   .mat-expansion-panel-header[_ngcontent-%COMP%]:hover{background:#fff}}.mat-expansion-panel-header-title[_ngcontent-%COMP%]{color:rgba(0,0,0,.87)}.mat-expansion-indicator[_ngcontent-%COMP%]:after, .mat-expansion-panel-header-description[_ngcontent-%COMP%]{color:rgba(0,0,0,.54)}.mat-expansion-panel-header[aria-disabled=true][_ngcontent-%COMP%]{color:rgba(0,0,0,.26)}.mat-expansion-panel-header[aria-disabled=true][_ngcontent-%COMP%]   .mat-expansion-panel-header-description[_ngcontent-%COMP%], .mat-expansion-panel-header[aria-disabled=true][_ngcontent-%COMP%]   .mat-expansion-panel-header-title[_ngcontent-%COMP%]{color:inherit}.mat-expansion-panel-header[_ngcontent-%COMP%]{height:48px}.mat-expansion-panel-header.mat-expanded[_ngcontent-%COMP%]{height:64px}.mat-form-field-label[_ngcontent-%COMP%], .mat-hint[_ngcontent-%COMP%]{color:rgba(0,0,0,.6)}.mat-form-field.mat-focused[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%]{color:#3f51b5}.mat-form-field.mat-focused[_ngcontent-%COMP%]   .mat-form-field-label.mat-accent[_ngcontent-%COMP%]{color:#ff4081}.mat-form-field.mat-focused[_ngcontent-%COMP%]   .mat-form-field-label.mat-warn[_ngcontent-%COMP%]{color:#f44336}.mat-focused[_ngcontent-%COMP%]   .mat-form-field-required-marker[_ngcontent-%COMP%]{color:#ff4081}.mat-form-field-ripple[_ngcontent-%COMP%]{background-color:rgba(0,0,0,.87)}.mat-form-field.mat-focused[_ngcontent-%COMP%]   .mat-form-field-ripple[_ngcontent-%COMP%]{background-color:#3f51b5}.mat-form-field.mat-focused[_ngcontent-%COMP%]   .mat-form-field-ripple.mat-accent[_ngcontent-%COMP%]{background-color:#ff4081}.mat-form-field.mat-focused[_ngcontent-%COMP%]   .mat-form-field-ripple.mat-warn[_ngcontent-%COMP%]{background-color:#f44336}.mat-form-field-type-mat-native-select.mat-focused[_ngcontent-%COMP%]:not(.mat-form-field-invalid)   .mat-form-field-infix[_ngcontent-%COMP%]:after{color:#3f51b5}.mat-form-field-type-mat-native-select.mat-focused[_ngcontent-%COMP%]:not(.mat-form-field-invalid).mat-accent   .mat-form-field-infix[_ngcontent-%COMP%]:after{color:#ff4081}.mat-form-field-type-mat-native-select.mat-focused[_ngcontent-%COMP%]:not(.mat-form-field-invalid).mat-warn   .mat-form-field-infix[_ngcontent-%COMP%]:after, .mat-form-field.mat-form-field-invalid[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%], .mat-form-field.mat-form-field-invalid[_ngcontent-%COMP%]   .mat-form-field-label.mat-accent[_ngcontent-%COMP%], .mat-form-field.mat-form-field-invalid[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%]   .mat-form-field-required-marker[_ngcontent-%COMP%]{color:#f44336}.mat-form-field.mat-form-field-invalid[_ngcontent-%COMP%]   .mat-form-field-ripple[_ngcontent-%COMP%], .mat-form-field.mat-form-field-invalid[_ngcontent-%COMP%]   .mat-form-field-ripple.mat-accent[_ngcontent-%COMP%]{background-color:#f44336}.mat-error[_ngcontent-%COMP%]{color:#f44336}.mat-form-field-appearance-legacy[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%], .mat-form-field-appearance-legacy[_ngcontent-%COMP%]   .mat-hint[_ngcontent-%COMP%]{color:rgba(0,0,0,.54)}.mat-form-field-appearance-legacy[_ngcontent-%COMP%]   .mat-form-field-underline[_ngcontent-%COMP%]{background-color:rgba(0,0,0,.42)}.mat-form-field-appearance-legacy.mat-form-field-disabled[_ngcontent-%COMP%]   .mat-form-field-underline[_ngcontent-%COMP%]{background-image:linear-gradient(90deg,rgba(0,0,0,.42) 0,rgba(0,0,0,.42) 33%,transparent 0);background-size:4px 100%;background-repeat:repeat-x}.mat-form-field-appearance-standard[_ngcontent-%COMP%]   .mat-form-field-underline[_ngcontent-%COMP%]{background-color:rgba(0,0,0,.42)}.mat-form-field-appearance-standard.mat-form-field-disabled[_ngcontent-%COMP%]   .mat-form-field-underline[_ngcontent-%COMP%]{background-image:linear-gradient(90deg,rgba(0,0,0,.42) 0,rgba(0,0,0,.42) 33%,transparent 0);background-size:4px 100%;background-repeat:repeat-x}.mat-form-field-appearance-fill[_ngcontent-%COMP%]   .mat-form-field-flex[_ngcontent-%COMP%]{background-color:rgba(0,0,0,.04)}.mat-form-field-appearance-fill.mat-form-field-disabled[_ngcontent-%COMP%]   .mat-form-field-flex[_ngcontent-%COMP%]{background-color:rgba(0,0,0,.02)}.mat-form-field-appearance-fill[_ngcontent-%COMP%]   .mat-form-field-underline[_ngcontent-%COMP%]:before{background-color:rgba(0,0,0,.42)}.mat-form-field-appearance-fill.mat-form-field-disabled[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%]{color:rgba(0,0,0,.38)}.mat-form-field-appearance-fill.mat-form-field-disabled[_ngcontent-%COMP%]   .mat-form-field-underline[_ngcontent-%COMP%]:before{background-color:initial}.mat-form-field-appearance-outline[_ngcontent-%COMP%]   .mat-form-field-outline[_ngcontent-%COMP%]{color:rgba(0,0,0,.12)}.mat-form-field-appearance-outline[_ngcontent-%COMP%]   .mat-form-field-outline-thick[_ngcontent-%COMP%]{color:rgba(0,0,0,.87)}.mat-form-field-appearance-outline.mat-focused[_ngcontent-%COMP%]   .mat-form-field-outline-thick[_ngcontent-%COMP%]{color:#3f51b5}.mat-form-field-appearance-outline.mat-focused.mat-accent[_ngcontent-%COMP%]   .mat-form-field-outline-thick[_ngcontent-%COMP%]{color:#ff4081}.mat-form-field-appearance-outline.mat-focused.mat-warn[_ngcontent-%COMP%]   .mat-form-field-outline-thick[_ngcontent-%COMP%], .mat-form-field-appearance-outline.mat-form-field-invalid.mat-form-field-invalid[_ngcontent-%COMP%]   .mat-form-field-outline-thick[_ngcontent-%COMP%]{color:#f44336}.mat-form-field-appearance-outline.mat-form-field-disabled[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%]{color:rgba(0,0,0,.38)}.mat-form-field-appearance-outline.mat-form-field-disabled[_ngcontent-%COMP%]   .mat-form-field-outline[_ngcontent-%COMP%]{color:rgba(0,0,0,.06)}.mat-icon.mat-primary[_ngcontent-%COMP%]{color:#3f51b5}.mat-icon.mat-accent[_ngcontent-%COMP%]{color:#ff4081}.mat-icon.mat-warn[_ngcontent-%COMP%]{color:#f44336}.mat-form-field-type-mat-native-select[_ngcontent-%COMP%]   .mat-form-field-infix[_ngcontent-%COMP%]:after{color:rgba(0,0,0,.54)}.mat-form-field-type-mat-native-select.mat-form-field-disabled[_ngcontent-%COMP%]   .mat-form-field-infix[_ngcontent-%COMP%]:after, .mat-input-element[_ngcontent-%COMP%]:disabled{color:rgba(0,0,0,.38)}.mat-input-element[_ngcontent-%COMP%]{caret-color:#3f51b5}.mat-input-element[_ngcontent-%COMP%]::placeholder{color:rgba(0,0,0,.42)}.mat-input-element[_ngcontent-%COMP%]::-moz-placeholder{color:rgba(0,0,0,.42)}.mat-input-element[_ngcontent-%COMP%]::-webkit-input-placeholder{color:rgba(0,0,0,.42)}.mat-input-element[_ngcontent-%COMP%]:-ms-input-placeholder{color:rgba(0,0,0,.42)}.mat-form-field.mat-accent[_ngcontent-%COMP%]   .mat-input-element[_ngcontent-%COMP%]{caret-color:#ff4081}.mat-form-field-invalid[_ngcontent-%COMP%]   .mat-input-element[_ngcontent-%COMP%], .mat-form-field.mat-warn[_ngcontent-%COMP%]   .mat-input-element[_ngcontent-%COMP%]{caret-color:#f44336}.mat-form-field-type-mat-native-select.mat-form-field-invalid[_ngcontent-%COMP%]   .mat-form-field-infix[_ngcontent-%COMP%]:after{color:#f44336}.mat-list-base[_ngcontent-%COMP%]   .mat-list-item[_ngcontent-%COMP%], .mat-list-base[_ngcontent-%COMP%]   .mat-list-option[_ngcontent-%COMP%]{color:rgba(0,0,0,.87)}.mat-list-base[_ngcontent-%COMP%]   .mat-subheader[_ngcontent-%COMP%]{color:rgba(0,0,0,.54)}.mat-list-item-disabled[_ngcontent-%COMP%]{background-color:#eee}.mat-action-list[_ngcontent-%COMP%]   .mat-list-item[_ngcontent-%COMP%]:focus, .mat-action-list[_ngcontent-%COMP%]   .mat-list-item[_ngcontent-%COMP%]:hover, .mat-list-option[_ngcontent-%COMP%]:focus, .mat-list-option[_ngcontent-%COMP%]:hover, .mat-nav-list[_ngcontent-%COMP%]   .mat-list-item[_ngcontent-%COMP%]:focus, .mat-nav-list[_ngcontent-%COMP%]   .mat-list-item[_ngcontent-%COMP%]:hover{background:rgba(0,0,0,.04)}.mat-list-single-selected-option[_ngcontent-%COMP%], .mat-list-single-selected-option[_ngcontent-%COMP%]:focus, .mat-list-single-selected-option[_ngcontent-%COMP%]:hover{background:rgba(0,0,0,.12)}.mat-menu-panel[_ngcontent-%COMP%]{background:#fff}.mat-menu-panel[_ngcontent-%COMP%]:not([class*=mat-elevation-z]){box-shadow:0 2px 4px -1px rgba(0,0,0,.2),0 4px 5px 0 rgba(0,0,0,.14),0 1px 10px 0 rgba(0,0,0,.12)}.mat-menu-item[_ngcontent-%COMP%]{background:transparent;color:rgba(0,0,0,.87)}.mat-menu-item[disabled][_ngcontent-%COMP%], .mat-menu-item[disabled][_ngcontent-%COMP%]:after{color:rgba(0,0,0,.38)}.mat-menu-item-submenu-trigger[_ngcontent-%COMP%]:after, .mat-menu-item[_ngcontent-%COMP%]   .mat-icon-no-color[_ngcontent-%COMP%]{color:rgba(0,0,0,.54)}.mat-menu-item-highlighted[_ngcontent-%COMP%]:not([disabled]), .mat-menu-item.cdk-keyboard-focused[_ngcontent-%COMP%]:not([disabled]), .mat-menu-item.cdk-program-focused[_ngcontent-%COMP%]:not([disabled]), .mat-menu-item[_ngcontent-%COMP%]:hover:not([disabled]){background:rgba(0,0,0,.04)}.mat-paginator[_ngcontent-%COMP%]{background:#fff}.mat-paginator[_ngcontent-%COMP%], .mat-paginator-page-size[_ngcontent-%COMP%]   .mat-select-trigger[_ngcontent-%COMP%]{color:rgba(0,0,0,.54)}.mat-paginator-decrement[_ngcontent-%COMP%], .mat-paginator-increment[_ngcontent-%COMP%]{border-top:2px solid rgba(0,0,0,.54);border-right:2px solid rgba(0,0,0,.54)}.mat-paginator-first[_ngcontent-%COMP%], .mat-paginator-last[_ngcontent-%COMP%]{border-top:2px solid rgba(0,0,0,.54)}.mat-icon-button[disabled][_ngcontent-%COMP%]   .mat-paginator-decrement[_ngcontent-%COMP%], .mat-icon-button[disabled][_ngcontent-%COMP%]   .mat-paginator-first[_ngcontent-%COMP%], .mat-icon-button[disabled][_ngcontent-%COMP%]   .mat-paginator-increment[_ngcontent-%COMP%], .mat-icon-button[disabled][_ngcontent-%COMP%]   .mat-paginator-last[_ngcontent-%COMP%]{border-color:rgba(0,0,0,.38)}.mat-paginator-container[_ngcontent-%COMP%]{min-height:56px}.mat-progress-bar-background[_ngcontent-%COMP%]{fill:#c5cae9}.mat-progress-bar-buffer[_ngcontent-%COMP%]{background-color:#c5cae9}.mat-progress-bar-fill[_ngcontent-%COMP%]:after{background-color:#3f51b5}.mat-progress-bar.mat-accent[_ngcontent-%COMP%]   .mat-progress-bar-background[_ngcontent-%COMP%]{fill:#ff80ab}.mat-progress-bar.mat-accent[_ngcontent-%COMP%]   .mat-progress-bar-buffer[_ngcontent-%COMP%]{background-color:#ff80ab}.mat-progress-bar.mat-accent[_ngcontent-%COMP%]   .mat-progress-bar-fill[_ngcontent-%COMP%]:after{background-color:#ff4081}.mat-progress-bar.mat-warn[_ngcontent-%COMP%]   .mat-progress-bar-background[_ngcontent-%COMP%]{fill:#ffcdd2}.mat-progress-bar.mat-warn[_ngcontent-%COMP%]   .mat-progress-bar-buffer[_ngcontent-%COMP%]{background-color:#ffcdd2}.mat-progress-bar.mat-warn[_ngcontent-%COMP%]   .mat-progress-bar-fill[_ngcontent-%COMP%]:after{background-color:#f44336}.mat-progress-spinner[_ngcontent-%COMP%]   circle[_ngcontent-%COMP%], .mat-spinner[_ngcontent-%COMP%]   circle[_ngcontent-%COMP%]{stroke:#3f51b5}.mat-progress-spinner.mat-accent[_ngcontent-%COMP%]   circle[_ngcontent-%COMP%], .mat-spinner.mat-accent[_ngcontent-%COMP%]   circle[_ngcontent-%COMP%]{stroke:#ff4081}.mat-progress-spinner.mat-warn[_ngcontent-%COMP%]   circle[_ngcontent-%COMP%], .mat-spinner.mat-warn[_ngcontent-%COMP%]   circle[_ngcontent-%COMP%]{stroke:#f44336}.mat-radio-outer-circle[_ngcontent-%COMP%]{border-color:rgba(0,0,0,.54)}.mat-radio-button.mat-primary.mat-radio-checked[_ngcontent-%COMP%]   .mat-radio-outer-circle[_ngcontent-%COMP%]{border-color:#3f51b5}.mat-radio-button.mat-primary.mat-radio-checked[_ngcontent-%COMP%]   .mat-radio-persistent-ripple[_ngcontent-%COMP%], .mat-radio-button.mat-primary[_ngcontent-%COMP%]   .mat-radio-inner-circle[_ngcontent-%COMP%], .mat-radio-button.mat-primary[_ngcontent-%COMP%]   .mat-radio-ripple[_ngcontent-%COMP%]   .mat-ripple-element[_ngcontent-%COMP%]:not(.mat-radio-persistent-ripple), .mat-radio-button.mat-primary[_ngcontent-%COMP%]:active   .mat-radio-persistent-ripple[_ngcontent-%COMP%]{background-color:#3f51b5}.mat-radio-button.mat-accent.mat-radio-checked[_ngcontent-%COMP%]   .mat-radio-outer-circle[_ngcontent-%COMP%]{border-color:#ff4081}.mat-radio-button.mat-accent.mat-radio-checked[_ngcontent-%COMP%]   .mat-radio-persistent-ripple[_ngcontent-%COMP%], .mat-radio-button.mat-accent[_ngcontent-%COMP%]   .mat-radio-inner-circle[_ngcontent-%COMP%], .mat-radio-button.mat-accent[_ngcontent-%COMP%]   .mat-radio-ripple[_ngcontent-%COMP%]   .mat-ripple-element[_ngcontent-%COMP%]:not(.mat-radio-persistent-ripple), .mat-radio-button.mat-accent[_ngcontent-%COMP%]:active   .mat-radio-persistent-ripple[_ngcontent-%COMP%]{background-color:#ff4081}.mat-radio-button.mat-warn.mat-radio-checked[_ngcontent-%COMP%]   .mat-radio-outer-circle[_ngcontent-%COMP%]{border-color:#f44336}.mat-radio-button.mat-warn.mat-radio-checked[_ngcontent-%COMP%]   .mat-radio-persistent-ripple[_ngcontent-%COMP%], .mat-radio-button.mat-warn[_ngcontent-%COMP%]   .mat-radio-inner-circle[_ngcontent-%COMP%], .mat-radio-button.mat-warn[_ngcontent-%COMP%]   .mat-radio-ripple[_ngcontent-%COMP%]   .mat-ripple-element[_ngcontent-%COMP%]:not(.mat-radio-persistent-ripple), .mat-radio-button.mat-warn[_ngcontent-%COMP%]:active   .mat-radio-persistent-ripple[_ngcontent-%COMP%]{background-color:#f44336}.mat-radio-button.mat-radio-disabled.mat-radio-checked[_ngcontent-%COMP%]   .mat-radio-outer-circle[_ngcontent-%COMP%], .mat-radio-button.mat-radio-disabled[_ngcontent-%COMP%]   .mat-radio-outer-circle[_ngcontent-%COMP%]{border-color:rgba(0,0,0,.38)}.mat-radio-button.mat-radio-disabled[_ngcontent-%COMP%]   .mat-radio-inner-circle[_ngcontent-%COMP%], .mat-radio-button.mat-radio-disabled[_ngcontent-%COMP%]   .mat-radio-ripple[_ngcontent-%COMP%]   .mat-ripple-element[_ngcontent-%COMP%]{background-color:rgba(0,0,0,.38)}.mat-radio-button.mat-radio-disabled[_ngcontent-%COMP%]   .mat-radio-label-content[_ngcontent-%COMP%]{color:rgba(0,0,0,.38)}.mat-radio-button[_ngcontent-%COMP%]   .mat-ripple-element[_ngcontent-%COMP%]{background-color:#000}.mat-select-value[_ngcontent-%COMP%]{color:rgba(0,0,0,.87)}.mat-select-placeholder[_ngcontent-%COMP%]{color:rgba(0,0,0,.42)}.mat-select-disabled[_ngcontent-%COMP%]   .mat-select-value[_ngcontent-%COMP%]{color:rgba(0,0,0,.38)}.mat-select-arrow[_ngcontent-%COMP%]{color:rgba(0,0,0,.54)}.mat-select-panel[_ngcontent-%COMP%]{background:#fff}.mat-select-panel[_ngcontent-%COMP%]:not([class*=mat-elevation-z]){box-shadow:0 2px 4px -1px rgba(0,0,0,.2),0 4px 5px 0 rgba(0,0,0,.14),0 1px 10px 0 rgba(0,0,0,.12)}.mat-select-panel[_ngcontent-%COMP%]   .mat-option.mat-selected[_ngcontent-%COMP%]:not(.mat-option-multiple){background:rgba(0,0,0,.12)}.mat-form-field.mat-focused.mat-primary[_ngcontent-%COMP%]   .mat-select-arrow[_ngcontent-%COMP%]{color:#3f51b5}.mat-form-field.mat-focused.mat-accent[_ngcontent-%COMP%]   .mat-select-arrow[_ngcontent-%COMP%]{color:#ff4081}.mat-form-field.mat-focused.mat-warn[_ngcontent-%COMP%]   .mat-select-arrow[_ngcontent-%COMP%], .mat-form-field[_ngcontent-%COMP%]   .mat-select.mat-select-invalid[_ngcontent-%COMP%]   .mat-select-arrow[_ngcontent-%COMP%]{color:#f44336}.mat-form-field[_ngcontent-%COMP%]   .mat-select.mat-select-disabled[_ngcontent-%COMP%]   .mat-select-arrow[_ngcontent-%COMP%]{color:rgba(0,0,0,.38)}.mat-drawer-container[_ngcontent-%COMP%]{background-color:#fafafa;color:rgba(0,0,0,.87)}.mat-drawer[_ngcontent-%COMP%]{color:rgba(0,0,0,.87)}.mat-drawer[_ngcontent-%COMP%], .mat-drawer.mat-drawer-push[_ngcontent-%COMP%]{background-color:#fff}.mat-drawer[_ngcontent-%COMP%]:not(.mat-drawer-side){box-shadow:0 8px 10px -5px rgba(0,0,0,.2),0 16px 24px 2px rgba(0,0,0,.14),0 6px 30px 5px rgba(0,0,0,.12)}.mat-drawer-side[_ngcontent-%COMP%]{border-right:1px solid rgba(0,0,0,.12)}.mat-drawer-side.mat-drawer-end[_ngcontent-%COMP%], [dir=rtl][_ngcontent-%COMP%]   .mat-drawer-side[_ngcontent-%COMP%]{border-left:1px solid rgba(0,0,0,.12);border-right:none}[dir=rtl][_ngcontent-%COMP%]   .mat-drawer-side.mat-drawer-end[_ngcontent-%COMP%]{border-left:none;border-right:1px solid rgba(0,0,0,.12)}.mat-drawer-backdrop.mat-drawer-shown[_ngcontent-%COMP%]{background-color:rgba(0,0,0,.6)}.mat-slide-toggle.mat-checked[_ngcontent-%COMP%]   .mat-slide-toggle-thumb[_ngcontent-%COMP%]{background-color:#ff4081}.mat-slide-toggle.mat-checked[_ngcontent-%COMP%]   .mat-slide-toggle-bar[_ngcontent-%COMP%]{background-color:rgba(255,64,129,.54)}.mat-slide-toggle.mat-checked[_ngcontent-%COMP%]   .mat-ripple-element[_ngcontent-%COMP%]{background-color:#ff4081}.mat-slide-toggle.mat-primary.mat-checked[_ngcontent-%COMP%]   .mat-slide-toggle-thumb[_ngcontent-%COMP%]{background-color:#3f51b5}.mat-slide-toggle.mat-primary.mat-checked[_ngcontent-%COMP%]   .mat-slide-toggle-bar[_ngcontent-%COMP%]{background-color:rgba(63,81,181,.54)}.mat-slide-toggle.mat-primary.mat-checked[_ngcontent-%COMP%]   .mat-ripple-element[_ngcontent-%COMP%]{background-color:#3f51b5}.mat-slide-toggle.mat-warn.mat-checked[_ngcontent-%COMP%]   .mat-slide-toggle-thumb[_ngcontent-%COMP%]{background-color:#f44336}.mat-slide-toggle.mat-warn.mat-checked[_ngcontent-%COMP%]   .mat-slide-toggle-bar[_ngcontent-%COMP%]{background-color:rgba(244,67,54,.54)}.mat-slide-toggle.mat-warn.mat-checked[_ngcontent-%COMP%]   .mat-ripple-element[_ngcontent-%COMP%]{background-color:#f44336}.mat-slide-toggle[_ngcontent-%COMP%]:not(.mat-checked)   .mat-ripple-element[_ngcontent-%COMP%]{background-color:#000}.mat-slide-toggle-thumb[_ngcontent-%COMP%]{box-shadow:0 2px 1px -1px rgba(0,0,0,.2),0 1px 1px 0 rgba(0,0,0,.14),0 1px 3px 0 rgba(0,0,0,.12);background-color:#fafafa}.mat-slide-toggle-bar[_ngcontent-%COMP%]{background-color:rgba(0,0,0,.38)}.mat-slider-track-background[_ngcontent-%COMP%]{background-color:rgba(0,0,0,.26)}.mat-primary[_ngcontent-%COMP%]   .mat-slider-thumb[_ngcontent-%COMP%], .mat-primary[_ngcontent-%COMP%]   .mat-slider-thumb-label[_ngcontent-%COMP%], .mat-primary[_ngcontent-%COMP%]   .mat-slider-track-fill[_ngcontent-%COMP%]{background-color:#3f51b5}.mat-primary[_ngcontent-%COMP%]   .mat-slider-thumb-label-text[_ngcontent-%COMP%]{color:#fff}.mat-primary[_ngcontent-%COMP%]   .mat-slider-focus-ring[_ngcontent-%COMP%]{background-color:rgba(63,81,181,.2)}.mat-accent[_ngcontent-%COMP%]   .mat-slider-thumb[_ngcontent-%COMP%], .mat-accent[_ngcontent-%COMP%]   .mat-slider-thumb-label[_ngcontent-%COMP%], .mat-accent[_ngcontent-%COMP%]   .mat-slider-track-fill[_ngcontent-%COMP%]{background-color:#ff4081}.mat-accent[_ngcontent-%COMP%]   .mat-slider-thumb-label-text[_ngcontent-%COMP%]{color:#fff}.mat-accent[_ngcontent-%COMP%]   .mat-slider-focus-ring[_ngcontent-%COMP%]{background-color:rgba(255,64,129,.2)}.mat-warn[_ngcontent-%COMP%]   .mat-slider-thumb[_ngcontent-%COMP%], .mat-warn[_ngcontent-%COMP%]   .mat-slider-thumb-label[_ngcontent-%COMP%], .mat-warn[_ngcontent-%COMP%]   .mat-slider-track-fill[_ngcontent-%COMP%]{background-color:#f44336}.mat-warn[_ngcontent-%COMP%]   .mat-slider-thumb-label-text[_ngcontent-%COMP%]{color:#fff}.mat-warn[_ngcontent-%COMP%]   .mat-slider-focus-ring[_ngcontent-%COMP%]{background-color:rgba(244,67,54,.2)}.cdk-focused[_ngcontent-%COMP%]   .mat-slider-track-background[_ngcontent-%COMP%], .mat-slider[_ngcontent-%COMP%]:hover   .mat-slider-track-background[_ngcontent-%COMP%]{background-color:rgba(0,0,0,.38)}.mat-slider-disabled[_ngcontent-%COMP%]   .mat-slider-thumb[_ngcontent-%COMP%], .mat-slider-disabled[_ngcontent-%COMP%]   .mat-slider-track-background[_ngcontent-%COMP%], .mat-slider-disabled[_ngcontent-%COMP%]   .mat-slider-track-fill[_ngcontent-%COMP%], .mat-slider-disabled[_ngcontent-%COMP%]:hover   .mat-slider-track-background[_ngcontent-%COMP%]{background-color:rgba(0,0,0,.26)}.mat-slider-min-value[_ngcontent-%COMP%]   .mat-slider-focus-ring[_ngcontent-%COMP%]{background-color:rgba(0,0,0,.12)}.mat-slider-min-value.mat-slider-thumb-label-showing[_ngcontent-%COMP%]   .mat-slider-thumb[_ngcontent-%COMP%], .mat-slider-min-value.mat-slider-thumb-label-showing[_ngcontent-%COMP%]   .mat-slider-thumb-label[_ngcontent-%COMP%]{background-color:rgba(0,0,0,.87)}.mat-slider-min-value.mat-slider-thumb-label-showing.cdk-focused[_ngcontent-%COMP%]   .mat-slider-thumb[_ngcontent-%COMP%], .mat-slider-min-value.mat-slider-thumb-label-showing.cdk-focused[_ngcontent-%COMP%]   .mat-slider-thumb-label[_ngcontent-%COMP%]{background-color:rgba(0,0,0,.26)}.mat-slider-min-value[_ngcontent-%COMP%]:not(.mat-slider-thumb-label-showing)   .mat-slider-thumb[_ngcontent-%COMP%]{border-color:rgba(0,0,0,.26);background-color:initial}.mat-slider-min-value[_ngcontent-%COMP%]:not(.mat-slider-thumb-label-showing).cdk-focused   .mat-slider-thumb[_ngcontent-%COMP%], .mat-slider-min-value[_ngcontent-%COMP%]:not(.mat-slider-thumb-label-showing):hover   .mat-slider-thumb[_ngcontent-%COMP%]{border-color:rgba(0,0,0,.38)}.mat-slider-min-value[_ngcontent-%COMP%]:not(.mat-slider-thumb-label-showing).cdk-focused.mat-slider-disabled   .mat-slider-thumb[_ngcontent-%COMP%], .mat-slider-min-value[_ngcontent-%COMP%]:not(.mat-slider-thumb-label-showing):hover.mat-slider-disabled   .mat-slider-thumb[_ngcontent-%COMP%]{border-color:rgba(0,0,0,.26)}.mat-slider-has-ticks[_ngcontent-%COMP%]   .mat-slider-wrapper[_ngcontent-%COMP%]:after{border-color:rgba(0,0,0,.7)}.mat-slider-horizontal[_ngcontent-%COMP%]   .mat-slider-ticks[_ngcontent-%COMP%]{background-image:repeating-linear-gradient(90deg,rgba(0,0,0,.7),rgba(0,0,0,.7) 2px,transparent 0,transparent);background-image:-moz-repeating-linear-gradient(.0001deg,rgba(0,0,0,.7),rgba(0,0,0,.7) 2px,transparent 0,transparent)}.mat-slider-vertical[_ngcontent-%COMP%]   .mat-slider-ticks[_ngcontent-%COMP%]{background-image:repeating-linear-gradient(180deg,rgba(0,0,0,.7),rgba(0,0,0,.7) 2px,transparent 0,transparent)}.mat-step-header.cdk-keyboard-focused[_ngcontent-%COMP%], .mat-step-header.cdk-program-focused[_ngcontent-%COMP%], .mat-step-header[_ngcontent-%COMP%]:hover{background-color:rgba(0,0,0,.04)}@media(hover:none){.mat-step-header[_ngcontent-%COMP%]:hover{background:none}}.mat-step-header[_ngcontent-%COMP%]   .mat-step-label[_ngcontent-%COMP%], .mat-step-header[_ngcontent-%COMP%]   .mat-step-optional[_ngcontent-%COMP%]{color:rgba(0,0,0,.54)}.mat-step-header[_ngcontent-%COMP%]   .mat-step-icon[_ngcontent-%COMP%]{background-color:rgba(0,0,0,.54);color:#fff}.mat-step-header[_ngcontent-%COMP%]   .mat-step-icon-selected[_ngcontent-%COMP%], .mat-step-header[_ngcontent-%COMP%]   .mat-step-icon-state-done[_ngcontent-%COMP%], .mat-step-header[_ngcontent-%COMP%]   .mat-step-icon-state-edit[_ngcontent-%COMP%]{background-color:#3f51b5;color:#fff}.mat-step-header[_ngcontent-%COMP%]   .mat-step-icon-state-error[_ngcontent-%COMP%]{background-color:initial;color:#f44336}.mat-step-header[_ngcontent-%COMP%]   .mat-step-label.mat-step-label-active[_ngcontent-%COMP%]{color:rgba(0,0,0,.87)}.mat-step-header[_ngcontent-%COMP%]   .mat-step-label.mat-step-label-error[_ngcontent-%COMP%]{color:#f44336}.mat-stepper-horizontal[_ngcontent-%COMP%], .mat-stepper-vertical[_ngcontent-%COMP%]{background-color:#fff}.mat-stepper-vertical-line[_ngcontent-%COMP%]:before{border-left-color:rgba(0,0,0,.12)}.mat-horizontal-stepper-header[_ngcontent-%COMP%]:after, .mat-horizontal-stepper-header[_ngcontent-%COMP%]:before, .mat-stepper-horizontal-line[_ngcontent-%COMP%]{border-top-color:rgba(0,0,0,.12)}.mat-horizontal-stepper-header[_ngcontent-%COMP%]{height:72px}.mat-stepper-label-position-bottom[_ngcontent-%COMP%]   .mat-horizontal-stepper-header[_ngcontent-%COMP%], .mat-vertical-stepper-header[_ngcontent-%COMP%]{padding:24px}.mat-stepper-vertical-line[_ngcontent-%COMP%]:before{top:-16px;bottom:-16px}.mat-stepper-label-position-bottom[_ngcontent-%COMP%]   .mat-horizontal-stepper-header[_ngcontent-%COMP%]:after, .mat-stepper-label-position-bottom[_ngcontent-%COMP%]   .mat-horizontal-stepper-header[_ngcontent-%COMP%]:before, .mat-stepper-label-position-bottom[_ngcontent-%COMP%]   .mat-stepper-horizontal-line[_ngcontent-%COMP%]{top:36px}.mat-sort-header-arrow[_ngcontent-%COMP%]{color:#757575}.mat-tab-header[_ngcontent-%COMP%], .mat-tab-nav-bar[_ngcontent-%COMP%]{border-bottom:1px solid rgba(0,0,0,.12)}.mat-tab-group-inverted-header[_ngcontent-%COMP%]   .mat-tab-header[_ngcontent-%COMP%], .mat-tab-group-inverted-header[_ngcontent-%COMP%]   .mat-tab-nav-bar[_ngcontent-%COMP%]{border-top:1px solid rgba(0,0,0,.12);border-bottom:none}.mat-tab-label[_ngcontent-%COMP%], .mat-tab-link[_ngcontent-%COMP%]{color:rgba(0,0,0,.87)}.mat-tab-label.mat-tab-disabled[_ngcontent-%COMP%], .mat-tab-link.mat-tab-disabled[_ngcontent-%COMP%]{color:rgba(0,0,0,.38)}.mat-tab-header-pagination-chevron[_ngcontent-%COMP%]{border-color:rgba(0,0,0,.87)}.mat-tab-header-pagination-disabled[_ngcontent-%COMP%]   .mat-tab-header-pagination-chevron[_ngcontent-%COMP%]{border-color:rgba(0,0,0,.38)}.mat-tab-group[class*=mat-background-][_ngcontent-%COMP%]   .mat-tab-header[_ngcontent-%COMP%], .mat-tab-nav-bar[class*=mat-background-][_ngcontent-%COMP%]{border-bottom:none;border-top:none}.mat-tab-group.mat-primary[_ngcontent-%COMP%]   .mat-tab-label.cdk-keyboard-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled), .mat-tab-group.mat-primary[_ngcontent-%COMP%]   .mat-tab-label.cdk-program-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled), .mat-tab-group.mat-primary[_ngcontent-%COMP%]   .mat-tab-link.cdk-keyboard-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled), .mat-tab-group.mat-primary[_ngcontent-%COMP%]   .mat-tab-link.cdk-program-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled), .mat-tab-nav-bar.mat-primary[_ngcontent-%COMP%]   .mat-tab-label.cdk-keyboard-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled), .mat-tab-nav-bar.mat-primary[_ngcontent-%COMP%]   .mat-tab-label.cdk-program-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled), .mat-tab-nav-bar.mat-primary[_ngcontent-%COMP%]   .mat-tab-link.cdk-keyboard-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled), .mat-tab-nav-bar.mat-primary[_ngcontent-%COMP%]   .mat-tab-link.cdk-program-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled){background-color:rgba(197,202,233,.3)}.mat-tab-group.mat-primary[_ngcontent-%COMP%]   .mat-ink-bar[_ngcontent-%COMP%], .mat-tab-nav-bar.mat-primary[_ngcontent-%COMP%]   .mat-ink-bar[_ngcontent-%COMP%]{background-color:#3f51b5}.mat-tab-group.mat-primary.mat-background-primary[_ngcontent-%COMP%]   .mat-ink-bar[_ngcontent-%COMP%], .mat-tab-nav-bar.mat-primary.mat-background-primary[_ngcontent-%COMP%]   .mat-ink-bar[_ngcontent-%COMP%]{background-color:#fff}.mat-tab-group.mat-accent[_ngcontent-%COMP%]   .mat-tab-label.cdk-keyboard-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled), .mat-tab-group.mat-accent[_ngcontent-%COMP%]   .mat-tab-label.cdk-program-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled), .mat-tab-group.mat-accent[_ngcontent-%COMP%]   .mat-tab-link.cdk-keyboard-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled), .mat-tab-group.mat-accent[_ngcontent-%COMP%]   .mat-tab-link.cdk-program-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled), .mat-tab-nav-bar.mat-accent[_ngcontent-%COMP%]   .mat-tab-label.cdk-keyboard-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled), .mat-tab-nav-bar.mat-accent[_ngcontent-%COMP%]   .mat-tab-label.cdk-program-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled), .mat-tab-nav-bar.mat-accent[_ngcontent-%COMP%]   .mat-tab-link.cdk-keyboard-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled), .mat-tab-nav-bar.mat-accent[_ngcontent-%COMP%]   .mat-tab-link.cdk-program-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled){background-color:rgba(255,128,171,.3)}.mat-tab-group.mat-accent[_ngcontent-%COMP%]   .mat-ink-bar[_ngcontent-%COMP%], .mat-tab-nav-bar.mat-accent[_ngcontent-%COMP%]   .mat-ink-bar[_ngcontent-%COMP%]{background-color:#ff4081}.mat-tab-group.mat-accent.mat-background-accent[_ngcontent-%COMP%]   .mat-ink-bar[_ngcontent-%COMP%], .mat-tab-nav-bar.mat-accent.mat-background-accent[_ngcontent-%COMP%]   .mat-ink-bar[_ngcontent-%COMP%]{background-color:#fff}.mat-tab-group.mat-warn[_ngcontent-%COMP%]   .mat-tab-label.cdk-keyboard-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled), .mat-tab-group.mat-warn[_ngcontent-%COMP%]   .mat-tab-label.cdk-program-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled), .mat-tab-group.mat-warn[_ngcontent-%COMP%]   .mat-tab-link.cdk-keyboard-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled), .mat-tab-group.mat-warn[_ngcontent-%COMP%]   .mat-tab-link.cdk-program-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled), .mat-tab-nav-bar.mat-warn[_ngcontent-%COMP%]   .mat-tab-label.cdk-keyboard-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled), .mat-tab-nav-bar.mat-warn[_ngcontent-%COMP%]   .mat-tab-label.cdk-program-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled), .mat-tab-nav-bar.mat-warn[_ngcontent-%COMP%]   .mat-tab-link.cdk-keyboard-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled), .mat-tab-nav-bar.mat-warn[_ngcontent-%COMP%]   .mat-tab-link.cdk-program-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled){background-color:rgba(255,205,210,.3)}.mat-tab-group.mat-warn[_ngcontent-%COMP%]   .mat-ink-bar[_ngcontent-%COMP%], .mat-tab-nav-bar.mat-warn[_ngcontent-%COMP%]   .mat-ink-bar[_ngcontent-%COMP%]{background-color:#f44336}.mat-tab-group.mat-warn.mat-background-warn[_ngcontent-%COMP%]   .mat-ink-bar[_ngcontent-%COMP%], .mat-tab-nav-bar.mat-warn.mat-background-warn[_ngcontent-%COMP%]   .mat-ink-bar[_ngcontent-%COMP%]{background-color:#fff}.mat-tab-group.mat-background-primary[_ngcontent-%COMP%]   .mat-tab-label.cdk-keyboard-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled), .mat-tab-group.mat-background-primary[_ngcontent-%COMP%]   .mat-tab-label.cdk-program-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled), .mat-tab-group.mat-background-primary[_ngcontent-%COMP%]   .mat-tab-link.cdk-keyboard-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled), .mat-tab-group.mat-background-primary[_ngcontent-%COMP%]   .mat-tab-link.cdk-program-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled), .mat-tab-nav-bar.mat-background-primary[_ngcontent-%COMP%]   .mat-tab-label.cdk-keyboard-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled), .mat-tab-nav-bar.mat-background-primary[_ngcontent-%COMP%]   .mat-tab-label.cdk-program-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled), .mat-tab-nav-bar.mat-background-primary[_ngcontent-%COMP%]   .mat-tab-link.cdk-keyboard-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled), .mat-tab-nav-bar.mat-background-primary[_ngcontent-%COMP%]   .mat-tab-link.cdk-program-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled){background-color:rgba(197,202,233,.3)}.mat-tab-group.mat-background-primary[_ngcontent-%COMP%]   .mat-tab-header[_ngcontent-%COMP%], .mat-tab-group.mat-background-primary[_ngcontent-%COMP%]   .mat-tab-header-pagination[_ngcontent-%COMP%], .mat-tab-group.mat-background-primary[_ngcontent-%COMP%]   .mat-tab-links[_ngcontent-%COMP%], .mat-tab-nav-bar.mat-background-primary[_ngcontent-%COMP%]   .mat-tab-header[_ngcontent-%COMP%], .mat-tab-nav-bar.mat-background-primary[_ngcontent-%COMP%]   .mat-tab-header-pagination[_ngcontent-%COMP%], .mat-tab-nav-bar.mat-background-primary[_ngcontent-%COMP%]   .mat-tab-links[_ngcontent-%COMP%]{background-color:#3f51b5}.mat-tab-group.mat-background-primary[_ngcontent-%COMP%]   .mat-tab-label[_ngcontent-%COMP%], .mat-tab-group.mat-background-primary[_ngcontent-%COMP%]   .mat-tab-link[_ngcontent-%COMP%], .mat-tab-nav-bar.mat-background-primary[_ngcontent-%COMP%]   .mat-tab-label[_ngcontent-%COMP%], .mat-tab-nav-bar.mat-background-primary[_ngcontent-%COMP%]   .mat-tab-link[_ngcontent-%COMP%]{color:#fff}.mat-tab-group.mat-background-primary[_ngcontent-%COMP%]   .mat-tab-label.mat-tab-disabled[_ngcontent-%COMP%], .mat-tab-group.mat-background-primary[_ngcontent-%COMP%]   .mat-tab-link.mat-tab-disabled[_ngcontent-%COMP%], .mat-tab-nav-bar.mat-background-primary[_ngcontent-%COMP%]   .mat-tab-label.mat-tab-disabled[_ngcontent-%COMP%], .mat-tab-nav-bar.mat-background-primary[_ngcontent-%COMP%]   .mat-tab-link.mat-tab-disabled[_ngcontent-%COMP%]{color:hsla(0,0%,100%,.4)}.mat-tab-group.mat-background-primary[_ngcontent-%COMP%]   .mat-tab-header-pagination-chevron[_ngcontent-%COMP%], .mat-tab-nav-bar.mat-background-primary[_ngcontent-%COMP%]   .mat-tab-header-pagination-chevron[_ngcontent-%COMP%]{border-color:#fff}.mat-tab-group.mat-background-primary[_ngcontent-%COMP%]   .mat-tab-header-pagination-disabled[_ngcontent-%COMP%]   .mat-tab-header-pagination-chevron[_ngcontent-%COMP%], .mat-tab-nav-bar.mat-background-primary[_ngcontent-%COMP%]   .mat-tab-header-pagination-disabled[_ngcontent-%COMP%]   .mat-tab-header-pagination-chevron[_ngcontent-%COMP%]{border-color:hsla(0,0%,100%,.4)}.mat-tab-group.mat-background-primary[_ngcontent-%COMP%]   .mat-ripple-element[_ngcontent-%COMP%], .mat-tab-nav-bar.mat-background-primary[_ngcontent-%COMP%]   .mat-ripple-element[_ngcontent-%COMP%]{background-color:hsla(0,0%,100%,.12)}.mat-tab-group.mat-background-accent[_ngcontent-%COMP%]   .mat-tab-label.cdk-keyboard-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled), .mat-tab-group.mat-background-accent[_ngcontent-%COMP%]   .mat-tab-label.cdk-program-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled), .mat-tab-group.mat-background-accent[_ngcontent-%COMP%]   .mat-tab-link.cdk-keyboard-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled), .mat-tab-group.mat-background-accent[_ngcontent-%COMP%]   .mat-tab-link.cdk-program-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled), .mat-tab-nav-bar.mat-background-accent[_ngcontent-%COMP%]   .mat-tab-label.cdk-keyboard-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled), .mat-tab-nav-bar.mat-background-accent[_ngcontent-%COMP%]   .mat-tab-label.cdk-program-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled), .mat-tab-nav-bar.mat-background-accent[_ngcontent-%COMP%]   .mat-tab-link.cdk-keyboard-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled), .mat-tab-nav-bar.mat-background-accent[_ngcontent-%COMP%]   .mat-tab-link.cdk-program-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled){background-color:rgba(255,128,171,.3)}.mat-tab-group.mat-background-accent[_ngcontent-%COMP%]   .mat-tab-header[_ngcontent-%COMP%], .mat-tab-group.mat-background-accent[_ngcontent-%COMP%]   .mat-tab-header-pagination[_ngcontent-%COMP%], .mat-tab-group.mat-background-accent[_ngcontent-%COMP%]   .mat-tab-links[_ngcontent-%COMP%], .mat-tab-nav-bar.mat-background-accent[_ngcontent-%COMP%]   .mat-tab-header[_ngcontent-%COMP%], .mat-tab-nav-bar.mat-background-accent[_ngcontent-%COMP%]   .mat-tab-header-pagination[_ngcontent-%COMP%], .mat-tab-nav-bar.mat-background-accent[_ngcontent-%COMP%]   .mat-tab-links[_ngcontent-%COMP%]{background-color:#ff4081}.mat-tab-group.mat-background-accent[_ngcontent-%COMP%]   .mat-tab-label[_ngcontent-%COMP%], .mat-tab-group.mat-background-accent[_ngcontent-%COMP%]   .mat-tab-link[_ngcontent-%COMP%], .mat-tab-nav-bar.mat-background-accent[_ngcontent-%COMP%]   .mat-tab-label[_ngcontent-%COMP%], .mat-tab-nav-bar.mat-background-accent[_ngcontent-%COMP%]   .mat-tab-link[_ngcontent-%COMP%]{color:#fff}.mat-tab-group.mat-background-accent[_ngcontent-%COMP%]   .mat-tab-label.mat-tab-disabled[_ngcontent-%COMP%], .mat-tab-group.mat-background-accent[_ngcontent-%COMP%]   .mat-tab-link.mat-tab-disabled[_ngcontent-%COMP%], .mat-tab-nav-bar.mat-background-accent[_ngcontent-%COMP%]   .mat-tab-label.mat-tab-disabled[_ngcontent-%COMP%], .mat-tab-nav-bar.mat-background-accent[_ngcontent-%COMP%]   .mat-tab-link.mat-tab-disabled[_ngcontent-%COMP%]{color:hsla(0,0%,100%,.4)}.mat-tab-group.mat-background-accent[_ngcontent-%COMP%]   .mat-tab-header-pagination-chevron[_ngcontent-%COMP%], .mat-tab-nav-bar.mat-background-accent[_ngcontent-%COMP%]   .mat-tab-header-pagination-chevron[_ngcontent-%COMP%]{border-color:#fff}.mat-tab-group.mat-background-accent[_ngcontent-%COMP%]   .mat-tab-header-pagination-disabled[_ngcontent-%COMP%]   .mat-tab-header-pagination-chevron[_ngcontent-%COMP%], .mat-tab-nav-bar.mat-background-accent[_ngcontent-%COMP%]   .mat-tab-header-pagination-disabled[_ngcontent-%COMP%]   .mat-tab-header-pagination-chevron[_ngcontent-%COMP%]{border-color:hsla(0,0%,100%,.4)}.mat-tab-group.mat-background-accent[_ngcontent-%COMP%]   .mat-ripple-element[_ngcontent-%COMP%], .mat-tab-nav-bar.mat-background-accent[_ngcontent-%COMP%]   .mat-ripple-element[_ngcontent-%COMP%]{background-color:hsla(0,0%,100%,.12)}.mat-tab-group.mat-background-warn[_ngcontent-%COMP%]   .mat-tab-label.cdk-keyboard-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled), .mat-tab-group.mat-background-warn[_ngcontent-%COMP%]   .mat-tab-label.cdk-program-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled), .mat-tab-group.mat-background-warn[_ngcontent-%COMP%]   .mat-tab-link.cdk-keyboard-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled), .mat-tab-group.mat-background-warn[_ngcontent-%COMP%]   .mat-tab-link.cdk-program-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled), .mat-tab-nav-bar.mat-background-warn[_ngcontent-%COMP%]   .mat-tab-label.cdk-keyboard-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled), .mat-tab-nav-bar.mat-background-warn[_ngcontent-%COMP%]   .mat-tab-label.cdk-program-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled), .mat-tab-nav-bar.mat-background-warn[_ngcontent-%COMP%]   .mat-tab-link.cdk-keyboard-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled), .mat-tab-nav-bar.mat-background-warn[_ngcontent-%COMP%]   .mat-tab-link.cdk-program-focused[_ngcontent-%COMP%]:not(.mat-tab-disabled){background-color:rgba(255,205,210,.3)}.mat-tab-group.mat-background-warn[_ngcontent-%COMP%]   .mat-tab-header[_ngcontent-%COMP%], .mat-tab-group.mat-background-warn[_ngcontent-%COMP%]   .mat-tab-header-pagination[_ngcontent-%COMP%], .mat-tab-group.mat-background-warn[_ngcontent-%COMP%]   .mat-tab-links[_ngcontent-%COMP%], .mat-tab-nav-bar.mat-background-warn[_ngcontent-%COMP%]   .mat-tab-header[_ngcontent-%COMP%], .mat-tab-nav-bar.mat-background-warn[_ngcontent-%COMP%]   .mat-tab-header-pagination[_ngcontent-%COMP%], .mat-tab-nav-bar.mat-background-warn[_ngcontent-%COMP%]   .mat-tab-links[_ngcontent-%COMP%]{background-color:#f44336}.mat-tab-group.mat-background-warn[_ngcontent-%COMP%]   .mat-tab-label[_ngcontent-%COMP%], .mat-tab-group.mat-background-warn[_ngcontent-%COMP%]   .mat-tab-link[_ngcontent-%COMP%], .mat-tab-nav-bar.mat-background-warn[_ngcontent-%COMP%]   .mat-tab-label[_ngcontent-%COMP%], .mat-tab-nav-bar.mat-background-warn[_ngcontent-%COMP%]   .mat-tab-link[_ngcontent-%COMP%]{color:#fff}.mat-tab-group.mat-background-warn[_ngcontent-%COMP%]   .mat-tab-label.mat-tab-disabled[_ngcontent-%COMP%], .mat-tab-group.mat-background-warn[_ngcontent-%COMP%]   .mat-tab-link.mat-tab-disabled[_ngcontent-%COMP%], .mat-tab-nav-bar.mat-background-warn[_ngcontent-%COMP%]   .mat-tab-label.mat-tab-disabled[_ngcontent-%COMP%], .mat-tab-nav-bar.mat-background-warn[_ngcontent-%COMP%]   .mat-tab-link.mat-tab-disabled[_ngcontent-%COMP%]{color:hsla(0,0%,100%,.4)}.mat-tab-group.mat-background-warn[_ngcontent-%COMP%]   .mat-tab-header-pagination-chevron[_ngcontent-%COMP%], .mat-tab-nav-bar.mat-background-warn[_ngcontent-%COMP%]   .mat-tab-header-pagination-chevron[_ngcontent-%COMP%]{border-color:#fff}.mat-tab-group.mat-background-warn[_ngcontent-%COMP%]   .mat-tab-header-pagination-disabled[_ngcontent-%COMP%]   .mat-tab-header-pagination-chevron[_ngcontent-%COMP%], .mat-tab-nav-bar.mat-background-warn[_ngcontent-%COMP%]   .mat-tab-header-pagination-disabled[_ngcontent-%COMP%]   .mat-tab-header-pagination-chevron[_ngcontent-%COMP%]{border-color:hsla(0,0%,100%,.4)}.mat-tab-group.mat-background-warn[_ngcontent-%COMP%]   .mat-ripple-element[_ngcontent-%COMP%], .mat-tab-nav-bar.mat-background-warn[_ngcontent-%COMP%]   .mat-ripple-element[_ngcontent-%COMP%]{background-color:hsla(0,0%,100%,.12)}.mat-toolbar[_ngcontent-%COMP%]{background:#f5f5f5;color:rgba(0,0,0,.87)}.mat-toolbar.mat-primary[_ngcontent-%COMP%]{background:#3f51b5;color:#fff}.mat-toolbar.mat-accent[_ngcontent-%COMP%]{background:#ff4081;color:#fff}.mat-toolbar.mat-warn[_ngcontent-%COMP%]{background:#f44336;color:#fff}.mat-toolbar[_ngcontent-%COMP%]   .mat-focused[_ngcontent-%COMP%]   .mat-form-field-ripple[_ngcontent-%COMP%], .mat-toolbar[_ngcontent-%COMP%]   .mat-form-field-ripple[_ngcontent-%COMP%], .mat-toolbar[_ngcontent-%COMP%]   .mat-form-field-underline[_ngcontent-%COMP%]{background-color:currentColor}.mat-toolbar[_ngcontent-%COMP%]   .mat-focused[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%], .mat-toolbar[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%], .mat-toolbar[_ngcontent-%COMP%]   .mat-form-field.mat-focused[_ngcontent-%COMP%]   .mat-select-arrow[_ngcontent-%COMP%], .mat-toolbar[_ngcontent-%COMP%]   .mat-select-arrow[_ngcontent-%COMP%], .mat-toolbar[_ngcontent-%COMP%]   .mat-select-value[_ngcontent-%COMP%]{color:inherit}.mat-toolbar[_ngcontent-%COMP%]   .mat-input-element[_ngcontent-%COMP%]{caret-color:currentColor}.mat-toolbar-multiple-rows[_ngcontent-%COMP%]{min-height:64px}.mat-toolbar-row[_ngcontent-%COMP%], .mat-toolbar-single-row[_ngcontent-%COMP%]{height:64px}@media(max-width:599px){.mat-toolbar-multiple-rows[_ngcontent-%COMP%]{min-height:56px}.mat-toolbar-row[_ngcontent-%COMP%], .mat-toolbar-single-row[_ngcontent-%COMP%]{height:56px}}.mat-tooltip[_ngcontent-%COMP%]{background:rgba(97,97,97,.9)}.mat-tree[_ngcontent-%COMP%]{background:#fff}.mat-nested-tree-node[_ngcontent-%COMP%], .mat-tree-node[_ngcontent-%COMP%]{color:rgba(0,0,0,.87)}.mat-tree-node[_ngcontent-%COMP%]{min-height:48px}.mat-snack-bar-container[_ngcontent-%COMP%]{color:hsla(0,0%,100%,.7);background:#323232;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.mat-simple-snackbar-action[_ngcontent-%COMP%]{color:#ff4081}.cardHeight[_ngcontent-%COMP%]{height:var(--dynamicHeight)}.subHeadingHeight[_ngcontent-%COMP%]{height:var(--dynamicSubHeight)}.paymentHistoryHeight[_ngcontent-%COMP%]{height:var(--dynamicPaymentHistoryHeight)}.card[_ngcontent-%COMP%]{background-color:#fff;border:1px solid #ddd;box-shadow:0 4px 8px rgba(0,0,0,.1);padding:16px;overflow-y:auto}.icon[_ngcontent-%COMP%]{border-radius:50%;height:33px;display:flex;line-height:9px;text-align:center;justify-content:center;vertical-align:middle;width:32px;transition:transform .3s ease-in-out}.iconClass[_ngcontent-%COMP%]{font-size:16px}.labelName[_ngcontent-%COMP%]{font-size:11px;color:#515965;font-weight:400;letter-spacing:.22px}.labelName[_ngcontent-%COMP%], .value[_ngcontent-%COMP%]{font-family:DM Sans;line-height:16px;text-transform:capitalize;font-style:normal}.value[_ngcontent-%COMP%]{color:var(--Blue-Grey-90,#526179);font-size:12px;font-weight:600;letter-spacing:.24px}.header[_ngcontent-%COMP%]{color:var(--Black-100,#111434);font-family:DM Sans;font-size:16px;font-style:normal;font-weight:500;line-height:16px;letter-spacing:.32px;text-transform:capitalize}  .mat-tab-label{color:#52c41a}  .mat-ink-bar,   .mat-tab-group.mat-primary .mat-ink-bar{background-color:#52c41a}.paymentLabel[_ngcontent-%COMP%]{color:var(--Blue-Grey-100,#45546e);font-family:DM Sans;font-size:12px;font-style:normal;font-weight:500;line-height:16px;letter-spacing:.24px;text-transform:capitalize}.mx-2[_ngcontent-%COMP%]{margin-left:.5rem;margin-right:.5rem}.le-circle[_ngcontent-%COMP%]{border-radius:50%;background-color:#515965}.cus-circle[_ngcontent-%COMP%], .le-circle[_ngcontent-%COMP%]{width:16px;height:16px;position:absolute;left:10px}.cus-circle[_ngcontent-%COMP%]{border-radius:50%;border:2px solid #ffbd3d}.line[_ngcontent-%COMP%]{width:1px;background-color:#dadce2;position:absolute;left:17px;top:34px;height:31px}.labelName[_ngcontent-%COMP%], .value[_ngcontent-%COMP%]{margin-left:20px}.smallCardIcon[_ngcontent-%COMP%]{font-size:20px}.invoice-no[_ngcontent-%COMP%]{color:#111434;font-family:DM Sans;font-weight:500;line-height:16px}.go-to-prj-icon[_ngcontent-%COMP%], .invoice-no[_ngcontent-%COMP%]{font-size:16px}.amount-label[_ngcontent-%COMP%]{font-size:12px;color:#515965;font-weight:400}.amount-label[_ngcontent-%COMP%], .amount-value[_ngcontent-%COMP%]{font-family:DM Sans;line-height:16px}.amount-value[_ngcontent-%COMP%]{font-size:14px;color:#52c41a;font-weight:598}.dollar-icon[_ngcontent-%COMP%]{width:33px;height:34px;border-radius:50%;text-align:center;justify-content:center;vertical-align:middle;margin-top:-3px}.cal-icon[_ngcontent-%COMP%]{color:#1890ff;font-size:24px;margin-top:10px}.cal-circle[_ngcontent-%COMP%]{width:39px;height:43px;border-radius:50%;text-align:center;justify-content:center;vertical-align:middle}.d-flex[_ngcontent-%COMP%], .progress-circle[_ngcontent-%COMP%]{display:flex;align-items:center}.progress-circle[_ngcontent-%COMP%]{--size:35px;--percentage:50;--thickness:4px;position:relative;width:var(--size);height:var(--size);border-radius:50%;justify-content:center}.progress-circle[_ngcontent-%COMP%]:before{content:"";position:absolute;width:55px;height:55px;border-radius:50%;background:conic-gradient(red calc(var(--percentage) * 1%),transparent 0);transform:rotate(180deg)}.progress-circle-inner[_ngcontent-%COMP%]{position:absolute;width:calc(var(--size) - 10px);height:calc(var(--size) - 10px);border-radius:50%;background:#fff;display:flex;align-items:center;justify-content:center;font-size:12px;font-weight:700;color:#000}.ml-3[_ngcontent-%COMP%]{margin-left:16px}.collection-label[_ngcontent-%COMP%]{color:#1b2140}.collection-label[_ngcontent-%COMP%], .inprogress-label[_ngcontent-%COMP%]{font-size:12px;font-family:DM Sans;font-weight:400;line-height:16px}.inprogress-label[_ngcontent-%COMP%]{color:#7d838b}.circular-progress[_ngcontent-%COMP%]{position:relative;height:55px;width:55px;border-radius:50%}.circular-progress[_ngcontent-%COMP%]:before{content:"";position:absolute;height:87%;width:90%;border-radius:50%;margin-top:4.1px;margin-left:2.6px;background:#f7f9fb;background:#fff;transform:rotate(180deg)}.circular-progress[_ngcontent-%COMP%]:before, .progress-value[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center}.progress-value[_ngcontent-%COMP%]{position:relative;font-size:12px;font-weight:700;height:100%;width:100%;color:#272a47}.payment-label[_ngcontent-%COMP%]{font-size:12px;color:#45546e;font-family:DM Sans;font-weight:500;line-height:16px}.submit[_ngcontent-%COMP%]{font-family:DM Sans;background-color:#52c41a;border:#52c41a}.button-disabled[_ngcontent-%COMP%], .submit[_ngcontent-%COMP%]{font-weight:400;font-size:14px!important;line-height:31px;padding:0 14px;color:#fff}.button-disabled[_ngcontent-%COMP%]{background-color:#d18c96;border:#cf0001}.text-wrap[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:300px;display:inline-block}.payment-history-value[_ngcontent-%COMP%]{color:#45546e;font-weight:500}.payment-history-label[_ngcontent-%COMP%], .payment-history-value[_ngcontent-%COMP%]{font-size:12px;font-family:DM Sans;line-height:16px}.payment-history-label[_ngcontent-%COMP%]{color:#8b95a5;font-weight:400}.payment-history-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;padding-top:30px}.payment-history-amount[_ngcontent-%COMP%]{border:1px solid #52c41a;border-radius:40px;height:26px;width:66px;background-color:#eef9e8;font-size:12px;line-height:16px;font-weight:500;color:#52c41a;justify-content:center;margin-bottom:10px;top:-28px;left:10px}.currency-suffix[_ngcontent-%COMP%], .payment-history-amount[_ngcontent-%COMP%]{display:flex;align-items:center;position:relative}.currency-suffix[_ngcontent-%COMP%]{padding-left:8px;padding-right:8px;height:48px;top:-4px!important;font-size:14px;line-height:24px;font-weight:400;color:#45546e;background-color:#f7f9fb;width:49px;border-bottom-right-radius:4px;border-top-right-radius:4px;border:1px solid #d3d3d3;padding-bottom:4px}  .tooltip-feedback{background-color:#1b2140!important;color:#fff}.mat-form-field-appearance-outline[_ngcontent-%COMP%]   .mat-form-field-prefix[_ngcontent-%COMP%], .mat-form-field-appearance-outline[_ngcontent-%COMP%]   .mat-form-field-suffix[_ngcontent-%COMP%]{top:0}  .mat-form-field .mat-form-field-infix{border-bottom:.37em solid transparent!important}.file-item[_ngcontent-%COMP%]{height:28px}.file-item[_ngcontent-%COMP%], .filename[_ngcontent-%COMP%]{margin-right:10px;white-space:nowrap;align-items:center}.filename[_ngcontent-%COMP%]{max-width:188px;display:block;text-overflow:ellipsis;overflow:hidden;font-size:14px}.upload-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;width:100px;height:73px;border:2px dashed #b9c0ca;border-radius:4px;padding:10px;text-align:center;box-sizing:border-box;position:relative}.upload-container[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:16px;margin-bottom:8px;color:#8b95a5}.upload-container[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:14px;color:#8b95a5}.file-count[_ngcontent-%COMP%]{position:absolute;top:-8px;right:-8px;border-radius:50%;width:21px;height:21px;display:flex;align-items:center;justify-content:center;font-size:10px!important;font-weight:700;box-shadow:0 0 5px rgba(0,0,0,.2)}.addDocument[_ngcontent-%COMP%], .file-count[_ngcontent-%COMP%]{background-color:#52c41a;color:#fff!important}.custom-table[_ngcontent-%COMP%]{width:100%;display:block!important}mat-cell[_ngcontent-%COMP%], mat-header-cell[_ngcontent-%COMP%]{display:flex;align-items:center;flex:1;box-sizing:border-box;padding:8px 16px}mat-header-row[_ngcontent-%COMP%], mat-row[_ngcontent-%COMP%]{display:flex}mat-header-cell[mat-sort-header][_ngcontent-%COMP%]{justify-content:flex-start}.amount-received[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-flex{padding:0 0 0 .75em!important}.sticky-header[_ngcontent-%COMP%]{position:sticky;top:0;z-index:1000;background:#fff}.selectValue[_ngcontent-%COMP%]     .mat-select-value-text{color:rgba(0,0,0,.54)}.textArea[_ngcontent-%COMP%]{width:57%}.table-container[_ngcontent-%COMP%]{height:var(--dynamicPaymentHistoryHeight);overflow:auto;width:100%}.icon[_ngcontent-%COMP%]:hover{transform:scale(1.3);transform-origin:center}.hover-text[_ngcontent-%COMP%]{visibility:hidden;opacity:0;color:var(--Blue-Grey-90,#526179);font-family:DM Sans;font-size:12px;font-style:normal;font-weight:400;line-height:16px;transform:translateX(-35%);transition:opacity .3s ease-in-out;width:80px;display:block;white-space:normal;word-wrap:break-word;line-height:1.2;margin-top:3px}.icon-container[_ngcontent-%COMP%]:hover   .hover-text[_ngcontent-%COMP%]{visibility:visible;opacity:1}.icon-container[_ngcontent-%COMP%]{position:relative;display:inline-block;text-align:center}.payment-history-filename[_ngcontent-%COMP%]{max-width:188px;display:inline-block;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.green-icon[_ngcontent-%COMP%]{color:#52c41a!important}.checkmark[_ngcontent-%COMP%]{position:absolute;top:0;left:27;color:green;font-size:11px;transform:translate(39%,-12%);z-index:2}.toggle-btn[_ngcontent-%COMP%]{min-width:5rem;color:#000;font-family:DM Sans}.btn-toggle-selected[_ngcontent-%COMP%], .toggle-btn[_ngcontent-%COMP%]{height:39px;align-items:center;display:flex}.btn-toggle-selected[_ngcontent-%COMP%]{font-size:12px!important;background-color:#ee4961!important;color:#fff}.mat-form-field[_ngcontent-%COMP%]:not(.mat-form-field-appearance-legacy)   .mat-form-field-suffix[_ngcontent-%COMP%]   .mat-icon-button[_ngcontent-%COMP%],   .date-picker .mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button,   .date-picker .mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button{height:2em!important}  .date-picker .mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-datepicker-toggle-default-icon{height:3em!important}']}),t})()}];let Gt=(()=>{class t{}return t.\u0275mod=v["\u0275\u0275defineNgModule"]({type:t}),t.\u0275inj=v["\u0275\u0275defineInjector"]({factory:function(e){return new(e||t)},imports:[[o.k.forChild(Yt)],o.k]}),t})();var $t=n("7EHt"),Qt=n("/1cH"),Xt=n("xHqg"),Jt=n("Wp6s"),Wt=n("wZkO"),Zt=n("pA3K"),te=n("QUrN"),ee=n("zZRP"),ne=n("M9IT");let ae=(()=>{class t{}return t.\u0275mod=v["\u0275\u0275defineNgModule"]({type:t}),t.\u0275inj=v["\u0275\u0275defineInjector"]({factory:function(e){return new(e||t)},imports:[[a.CommonModule,Gt,U.e,V.c,Jt.d,Xt.f,Qt.c,Wt.g,B.h,$t.b,c.E,c.p,N.b,T.b,z.b,Zt.a,te.b,k.b,H.d,ee.InvoiceModule,g.c,A.b,f.m,b.c,ne.b,ee.InvoiceModule,R.c]]}),t})()}}]);