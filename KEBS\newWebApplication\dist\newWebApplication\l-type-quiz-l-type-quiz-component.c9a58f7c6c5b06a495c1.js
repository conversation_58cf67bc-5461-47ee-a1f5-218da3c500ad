(window.webpackJsonp=window.webpackJsonp||[]).push([[738],{eCUb:function(e,t,n){"use strict";n.r(t),n.d(t,"LTypeQuizComponent",(function(){return d})),n.d(t,"LTypeQuizComponentModule",(function(){return m}));var s=n("mrSG"),i=n("ofXK"),o=n("0IaG"),a=n("bTqV"),l=n("NFeN"),r=n("33Jv"),u=n("fXoL"),c=n("bpb9");let d=(()=>{class e{constructor(e,t){this._dialog=e,this._learner=t,this.subs=new r.a,this.quizDetail={},this.getQuizDetail=()=>{this.subs.sink=this._learner.getQuizDetail(this.lessonData.lessonId).subscribe(e=>{this.quizDetail=e.data},e=>{console.error(e),this._learner.showSnack("Unable to get Quiz details!")})},this.getQuizSubmissions=()=>new Promise((e,t)=>{this.subs.sink=this._learner.getQuizSubmissions(this.lessonData.lessonId).subscribe(t=>{e(t.data)},e=>{this._learner.showSnack("Failed to get Submissions!"),t(e)})})}ngOnInit(){this.getQuizDetail()}viewSubmissions(){return Object(s.c)(this,void 0,void 0,(function*(){const{LQuizSubHistoryComponent:e}=yield n.e(737).then(n.bind(null,"m5a7"));let t=yield this.getQuizSubmissions();this._dialog.open(e,{width:"58%",minHeight:"52vh",maxHeight:"87vh",data:{lessonId:this.lessonData.lessonId,submissions:t}})}))}takeQuiz(){return Object(s.c)(this,void 0,void 0,(function*(){const{TakeQuizComponent:e}=yield n.e(1013).then(n.bind(null,"m0GA"));let t=new o.e;t={maxWidth:"none",height:"100%",width:"100%",disableClose:!0,panelClass:"full-screen-modal",data:{lessonData:this.lessonData}},this._dialog.open(e,t).afterClosed().subscribe(e=>{this.logCurrentLesson(this.lessonData.lessonId)})}))}logCurrentLesson(e){this.subs.sink=this._learner.logLessonProgress(e).subscribe(e=>{this._learner.sendLearnerMsg({msgCode:"MCCOMPLETE",data:e.data})},e=>{console.error(e)})}ngOnDestroy(){this.subs.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(u["\u0275\u0275directiveInject"](o.b),u["\u0275\u0275directiveInject"](c.a))},e.\u0275cmp=u["\u0275\u0275defineComponent"]({type:e,selectors:[["learner-l-type-quiz"]],inputs:{lessonData:"lessonData"},decls:17,vars:4,consts:[[1,"quiz-content"],[1,"row","pt-5"],[1,"col-12"],[1,"row"],[1,"col-12",2,"color","white"],[1,"d-flex","justify-content-center"],["src","https://assets.kebs.app/lms/svgs/choose-animate.svg","alt","",2,"height","15rem"],["mat-button","",1,"action-btn","mr-3",3,"click"],["mat-button","",1,"action-btn",3,"click"]],template:function(e,t){1&e&&(u["\u0275\u0275elementStart"](0,"div",0),u["\u0275\u0275elementStart"](1,"div",1),u["\u0275\u0275elementStart"](2,"div",2),u["\u0275\u0275elementStart"](3,"div",3),u["\u0275\u0275elementStart"](4,"div",2),u["\u0275\u0275elementStart"](5,"h1"),u["\u0275\u0275text"](6),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](7,"div",3),u["\u0275\u0275elementStart"](8,"div",4),u["\u0275\u0275text"](9),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](10,"div",5),u["\u0275\u0275element"](11,"img",6),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](12,"div",5),u["\u0275\u0275elementStart"](13,"button",7),u["\u0275\u0275listener"]("click",(function(){return t.viewSubmissions()})),u["\u0275\u0275text"](14," View Submissions "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementStart"](15,"button",8),u["\u0275\u0275listener"]("click",(function(){return t.takeQuiz()})),u["\u0275\u0275text"](16," Take Quiz "),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"](),u["\u0275\u0275elementEnd"]()),2&e&&(u["\u0275\u0275advance"](6),u["\u0275\u0275textInterpolate"](t.lessonData.lessonName),u["\u0275\u0275advance"](3),u["\u0275\u0275textInterpolate3"](" ",t.quizDetail.total_marks," Marks ",t.quizDetail.total_questions," Questions ",t.quizDetail.duration," Minutes "))},directives:[a.a],styles:[".quiz-content[_ngcontent-%COMP%]{color:#fff;background-color:#565656;height:67vh}.arrow-btns[_ngcontent-%COMP%]{color:#fff;background:#3e3e3e}.action-btn[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff;width:12rem}.full-screen-modal[_ngcontent-%COMP%]   .mat-dialog-container[_ngcontent-%COMP%]{max-width:none}"]}),e})(),m=(()=>{class e{}return e.\u0275mod=u["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=u["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,a.b,l.b,o.g]]}),e})()}}]);