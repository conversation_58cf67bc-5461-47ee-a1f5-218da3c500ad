(window.webpackJsonp=window.webpackJsonp||[]).push([[979],{hg9u:function(e,t,n){"use strict";n.r(t),n.d(t,"LogHistoryComponent",(function(){return u}));var o=n("mrSG"),l=n("0IaG"),i=n("fXoL"),a=n("NFeN"),r=n("ofXK"),c=n("Wp6s"),d=n("Qu3c");function s(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",12),i["\u0275\u0275elementStart"](1,"div",13),i["\u0275\u0275elementStart"](2,"span",14),i["\u0275\u0275text"](3,"Loading..."),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]())}function p(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",27),i["\u0275\u0275elementStart"](1,"div",28),i["\u0275\u0275text"](2),i["\u0275\u0275pipe"](3,"json"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]().$implicit,t=i["\u0275\u0275nextContext"]().$implicit;i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate"](i["\u0275\u0275pipeBind1"](3,1,null==t?null:t.old[e]))}}function m(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",29),i["\u0275\u0275elementStart"](1,"div",30),i["\u0275\u0275text"](2),i["\u0275\u0275pipe"](3,"json"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]().$implicit,t=i["\u0275\u0275nextContext"]().$implicit;i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate"](i["\u0275\u0275pipeBind1"](3,1,null==t?null:t.new[e]))}}function g(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",16),i["\u0275\u0275elementStart"](1,"div",21),i["\u0275\u0275elementStart"](2,"span",22),i["\u0275\u0275text"](3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](4,"div",5),i["\u0275\u0275template"](5,p,4,3,"div",23),i["\u0275\u0275pipe"](6,"json"),i["\u0275\u0275template"](7,m,4,3,"div",24),i["\u0275\u0275pipe"](8,"json"),i["\u0275\u0275elementStart"](9,"div",25),i["\u0275\u0275element"](10,"hr",26),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=i["\u0275\u0275nextContext"]().$implicit;i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("matTooltip",e),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](e),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf","{}"!=i["\u0275\u0275pipeBind1"](6,4,null==n?null:n.old)),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf","{}"!=i["\u0275\u0275pipeBind1"](8,6,null==n?null:n.new))}}function f(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",16),i["\u0275\u0275elementStart"](1,"div",17),i["\u0275\u0275elementStart"](2,"mat-card",18),i["\u0275\u0275element"](3,"div",16),i["\u0275\u0275template"](4,g,11,8,"div",15),i["\u0275\u0275elementStart"](5,"div",19),i["\u0275\u0275elementStart"](6,"span",20),i["\u0275\u0275text"](7,"By "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](8,"span",20),i["\u0275\u0275text"](9),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](10,"span",20),i["\u0275\u0275text"](11,"on"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](12,"span",20),i["\u0275\u0275text"](13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;i["\u0275\u0275advance"](4),i["\u0275\u0275property"]("ngForOf",e.logHistory),i["\u0275\u0275advance"](5),i["\u0275\u0275textInterpolate"](null==e?null:e.changedBy),i["\u0275\u0275advance"](4),i["\u0275\u0275textInterpolate"](null==e?null:e.changedOn)}}function h(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div"),i["\u0275\u0275template"](1,f,14,3,"div",15),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngForOf",e.logData)}}function v(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div"),i["\u0275\u0275elementStart"](1,"div",31),i["\u0275\u0275elementStart"](2,"mat-icon",32),i["\u0275\u0275text"](3,"running_with_errors"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"span",33),i["\u0275\u0275text"](5,"No History Found!"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]())}let u=(()=>{class e{constructor(e,t){this.dialogRef=e,this.logDetails=t}ngOnInit(){return Object(o.c)(this,void 0,void 0,(function*(){this.logSpinner=!1,this.logData=this.logDetails.data,this.logData=this.logData.reverse(),this.logData.length>0?(this.logHistoryFound=!0,this.addlogKeys()):this.logHistoryFound=!1}))}addlogKeys(){for(let e=0;e<this.logData.length;e++)this.logData[e].logHistory=[],Object.keys(this.logData[e].new).forEach(t=>{this.logData[e].logHistory.push(t)})}closeLogDialog(){this.dialogRef.close()}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](l.h),i["\u0275\u0275directiveInject"](l.a))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-log-history"]],decls:16,vars:3,consts:[[1,"container-fluid","log-history","mb-3"],[1,"row","p-1","pt-3","pb-3"],[1,"col"],[2,"font-size","21px","color","#cf0001","vertical-align","middle"],[1,"pl-1","heading-top"],[1,"col-1"],[1,"vertical-align:middle",3,"click"],[1,"icon-cancel"],[1,"m-0"],[1,"spinner-class"],["class","pt-4 pb-4 text-center",4,"ngIf"],[4,"ngIf"],[1,"pt-4","pb-4","text-center"],["role","status",1,"spinner-border"],[1,"sr-only"],["class","row",4,"ngFor","ngForOf"],[1,"row"],[1,"col","p-0"],[1,"mt-4"],[1,"row","d-flex","justify-content-end",2,"color","#6E7B8F"],[1,"pr-1"],[1,"col-3","p-0","table-heading"],[3,"matTooltip"],["class","col-4 p-0",4,"ngIf"],["class","col pl-0",4,"ngIf"],[2,"width","100%"],[1,"mb-0","mb-2"],[1,"col-4","p-0"],[1,"strike","pt-1"],[1,"col","pl-0"],[1,"pt-1"],[1,"row","mt-5","d-flex","justify-content-center"],[1,"no-history-icon"],[1,"pl-2","no-history"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275elementStart"](1,"div",1),i["\u0275\u0275elementStart"](2,"div",2),i["\u0275\u0275elementStart"](3,"mat-icon",3),i["\u0275\u0275text"](4,"history"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"span",4),i["\u0275\u0275text"](6,"Log History"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](7,"div",5),i["\u0275\u0275elementStart"](8,"span",6),i["\u0275\u0275listener"]("click",(function(){return t.closeLogDialog()})),i["\u0275\u0275elementStart"](9,"mat-icon",7),i["\u0275\u0275text"](10,"close"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](11,"hr",8),i["\u0275\u0275elementStart"](12,"div",9),i["\u0275\u0275template"](13,s,4,0,"div",10),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](14,h,2,1,"div",11),i["\u0275\u0275template"](15,v,6,0,"div",11),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](13),i["\u0275\u0275property"]("ngIf",1==t.logSpinner),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",1==t.logHistoryFound),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",0==t.logHistoryFound))},directives:[a.a,r.NgIf,r.NgForOf,c.a,d.a],pipes:[r.JsonPipe],styles:[".log-history[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]{color:#cf0001;font-size:14px}.log-history[_ngcontent-%COMP%]   .heading-top[_ngcontent-%COMP%]{font-family:Roboto;color:#cf0001;font-style:normal;font-weight:600;letter-spacing:.02em;text-transform:capitalize}.log-history[_ngcontent-%COMP%]   .table-heading[_ngcontent-%COMP%]{color:#cf0001;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.log-history[_ngcontent-%COMP%]   .spinner-border[_ngcontent-%COMP%]{color:#cf0001}.log-history[_ngcontent-%COMP%]   .icon-cancel[_ngcontent-%COMP%]{cursor:pointer;color:#a1a1a2;font-size:20px}.log-history[_ngcontent-%COMP%]   .no-history[_ngcontent-%COMP%]{color:#6e7b8f;font-size:18px}.log-history[_ngcontent-%COMP%]   .no-history-icon[_ngcontent-%COMP%]{color:#6e7b8f;font-size:25px;vertical-align:top}.log-history[_ngcontent-%COMP%]   .strike[_ngcontent-%COMP%]{white-space:no-wrap;text-decoration:line-through;overflow:hidden;text-overflow:ellipsis}"]}),e})()}}]);