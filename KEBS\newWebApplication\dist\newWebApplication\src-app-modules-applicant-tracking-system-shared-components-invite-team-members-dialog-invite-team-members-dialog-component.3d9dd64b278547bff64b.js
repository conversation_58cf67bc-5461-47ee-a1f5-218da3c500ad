(window.webpackJsonp=window.webpackJsonp||[]).push([[939],{Ls7d:function(e,t,n){"use strict";n.r(t),n.d(t,"InviteTeamMembersDialogComponent",(function(){return g}));var a=n("mrSG"),l=n("0IaG"),i=n("fXoL"),o=n("XNFG"),s=n("NFeN"),r=n("UVjm"),d=n("ofXK"),c=n("su5B");function m(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",13),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" *Only ",e.maxMembers," individual can be invited. ")}}const f=function(e,t){return{"pointer-events":e,opacity:t}};let g=(()=>{class e{constructor(e,t,n){this.data=e,this._dialogRef=t,this._toaster=n,this.selectedRole=null,this.selectedTeamMembers=null,this.maxMembers=null,this.mandatory=null}ngOnInit(){var e,t;return Object(a.c)(this,void 0,void 0,(function*(){this.mandatory=(null===(e=this.data.roleMasterData.find(e=>e.id==this.data.selectedRoleValue))||void 0===e?void 0:e.is_mandatory)||null,this.maxMembers=(null===(t=this.data.roleMasterData.find(e=>e.id==this.data.selectedRoleValue))||void 0===t?void 0:t.max_number_of_members)||null,this.selectedRole=this.data.selectedRoleValue,this.selectedTeamMembers=this.data.selectedTeamMembers}))}onClose(){this._dialogRef.close()}sendInviteToTeam(){this.selectedRole?this.selectedTeamMembers&&0!=this.selectedTeamMembers.length||!this.mandatory?this.selectedTeamMembers&&this.selectedTeamMembers.length>this.maxMembers?this._toaster.showWarning("Warning \u26a0\ufe0f","Only "+this.maxMembers+" individual can be invited for this Role!",3e3):this._dialogRef.close({selectedRole:this.selectedRole,selectedTeamMembers:this.selectedTeamMembers}):this._toaster.showWarning("Warning \u26a0\ufe0f","Kindly select a Team Member",3e3):this._toaster.showWarning("Warning \u26a0\ufe0f","Kindly select a Role",3e3)}onChangeInRole(e){var t,n;this.selectedRole=e.val,this.mandatory=(null===(t=this.data.roleMasterData.find(t=>t.id==e.val))||void 0===t?void 0:t.is_mandatory)||null,this.maxMembers=(null===(n=this.data.roleMasterData.find(t=>t.id==e.val))||void 0===n?void 0:n.max_number_of_members)||null}onTeamMemberChange(e){this.selectedTeamMembers=e.val}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](l.a),i["\u0275\u0275directiveInject"](l.h),i["\u0275\u0275directiveInject"](o.a))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-invite-team-members-dialog"]],decls:22,vars:18,consts:[[1,"d-flex","flex-column","bg-container"],[1,"d-flex","align-items-center","justify-content-between"],[1,"title","p-0"],[1,"icon"],[1,"icon",3,"click"],[1,"d-flex","flex-column","col-12","p-0"],[1,"sub-text"],[3,"ngStyle","masterData","selectedValue","displayClose","onValueChange"],[1,"desc-text"],[3,"placeholder","isMultiline","type","masterData","selectedValues","onValueChange"],["class","warning-text",4,"ngIf"],[1,"col-12","p-0","d-flex","justify-content-end"],[1,"send-btn",3,"click"],[1,"warning-text"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275elementStart"](1,"div",1),i["\u0275\u0275elementStart"](2,"div",2),i["\u0275\u0275text"](3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"div",3),i["\u0275\u0275elementStart"](5,"mat-icon",4),i["\u0275\u0275listener"]("click",(function(){return t.onClose()})),i["\u0275\u0275text"](6,"close"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](7,"div",5),i["\u0275\u0275elementStart"](8,"div",6),i["\u0275\u0275text"](9),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](10,"app-single-select-chip",7),i["\u0275\u0275listener"]("onValueChange",(function(e){return t.onChangeInRole(e)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](11,"div",5),i["\u0275\u0275elementStart"](12,"div",6),i["\u0275\u0275text"](13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](14,"div",8),i["\u0275\u0275text"](15),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](16,"div",5),i["\u0275\u0275elementStart"](17,"app-multi-select-chip",9),i["\u0275\u0275listener"]("onValueChange",(function(e){return t.onTeamMemberChange(e)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](18,m,2,1,"div",10),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](19,"div",11),i["\u0275\u0275elementStart"](20,"div",12),i["\u0275\u0275listener"]("click",(function(){return t.sendInviteToTeam()})),i["\u0275\u0275text"](21),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](3),i["\u0275\u0275textInterpolate"](t.data.textConfig["UI-BJ-ITM-001"]),i["\u0275\u0275advance"](6),i["\u0275\u0275textInterpolate"](t.data.textConfig["UI-BJ-ITM-002"]),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngStyle",i["\u0275\u0275pureFunction2"](15,f,"E"==t.data.mode?"none":"","C"==t.data.mode?"0.5":""))("masterData",t.data.roleMasterData)("selectedValue",t.selectedRole)("displayClose",!1),i["\u0275\u0275advance"](3),i["\u0275\u0275textInterpolate"](t.data.textConfig["UI-BJ-ITM-003"]),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate"](t.data.textConfig["UI-BJ-ITM-004"]),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("placeholder","Select Team Members")("isMultiline",!0)("type",2)("masterData",t.data.teamMasterData)("selectedValues",t.selectedTeamMembers),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.maxMembers),i["\u0275\u0275advance"](3),i["\u0275\u0275textInterpolate1"](" ","C"==t.data.mode?t.data.textConfig["UI-BJ-ITM-005"]:t.data.textConfig["UI-BJ-ITM-006"]," "))},directives:[s.a,r.a,d.NgStyle,c.a,d.NgIf],styles:[".bg-container[_ngcontent-%COMP%]{padding:32px;gap:16px}.bg-container[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:18px;font-weight:700;color:#111434}.bg-container[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{width:18px;height:18px;font-size:18px;color:#1b2140;cursor:pointer}.bg-container[_ngcontent-%COMP%]   .sub-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:12px;font-weight:500;color:#6e7b8f}.bg-container[_ngcontent-%COMP%]   .desc-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:10px;font-weight:400;color:#6e7b8f}.bg-container[_ngcontent-%COMP%]   .send-btn[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:700;color:#fff;padding:8px 12px;border-radius:8px;background:linear-gradient(270deg,var(--atsprimaryColor),var(--atssecondaryColor) 105.29%);cursor:pointer}.bg-container[_ngcontent-%COMP%]   .warning-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:10px;font-weight:400;color:#ff3a46}"]}),e})()}}]);