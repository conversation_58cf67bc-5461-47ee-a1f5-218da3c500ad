(window.webpackJsonp=window.webpackJsonp||[]).push([[991,987,990],{ucYs:function(r,e,t){"use strict";t.d(e,"a",(function(){return n}));var o=t("mrSG"),s=t("xG9w"),i=t("fXoL"),c=t("tk/3"),a=t("BVzC");let n=(()=>{class r{constructor(r,e){this.http=r,this.errorService=e,this.workflowStatusList=[],this.getWorkflowStatusList()}getWorkflowStatusList(){this.http.post("/api/wfPrimary/getWorkflowStatusList","").subscribe(r=>{this.workflowStatusList=r.data},r=>{this.errorService.userErrorAlert(r&&r.code?r.code:r&&r.error?r.error.code:"NIL","Internal Server While Getting Workflow Status List",r&&r.params?r.params:r&&r.error?r.error.params:{})})}getWorkflowProperties(r){return new Promise((e,t)=>{this.http.post("/api/wfPrimary/getWorkflowProperties",{applicationId:r}).subscribe(r=>e(r),r=>t(r))})}getWorkflowPropertiesByWorkflowId(r){return new Promise((e,t)=>{this.http.post("/api/wfPrimary/getWorkflowPropertiesByWorkflowId",{workflowId:r}).subscribe(r=>e(r),r=>t(r))})}getApproversHierarchy(r){return new Promise((e,t)=>{this.http.post("/api/wfPrimary/getApproversHierarchy",r).subscribe(r=>e(r),r=>t(r))})}createWorkflowItems(r){return new Promise((e,t)=>{this.http.post("/api/wfPrimary/createWorkflowItems",r).subscribe(r=>e(r),r=>t(r))})}getWorkflowDetails(r){return new Promise((e,t)=>{this.http.post("/api/wfPrimary/getWorkflowDetails",{workflowId:r}).subscribe(r=>e(r),r=>{this.errorService.userErrorAlert(r&&r.code?r.code:r&&r.error?r.error.code:"NIL","Error while getting workflow details",r&&r.params?r.params:r&&r.error?r.error.params:{})})})}formatApproversHierarchy(r,e){return Object(o.c)(this,void 0,void 0,(function*(){0==r.length&&e.cc0&&e.cc0.appr0&&e.cc0.appr0.length>0&&r.push([{approvalType:"I",purposeOfInsertingIntoArray:"Just to ensure that 'I' type data is used, if it exists"}]);for(let t=0;t<r.length;t++){let o=[],i=s.keys(e["cc"+t]);for(let s=0;s<i.length;s++)for(let c=0;c<e["cc"+t][i[s]].length;c++){let a={name:e["cc"+t][i[s]][c].DELEGATE_NAME,oid:e["cc"+t][i[s]][c].DELEGATE_OID,level:s+1,designation:e["cc"+t][i[s]][c].DELEGATE_DESIGNATION_NAME,is_delegated:e["cc"+t][i[s]][c].IS_DELEGATED,role:e["cc"+t][i[s]][c].DELEGATE_ROLE_NAME};if(1==e["cc"+t][i[s]][c].IS_DELEGATED&&(a.delegated_by={name:e["cc"+t][i[s]][c].APPROVER_NAME,oid:e["cc"+t][i[s]][c].APPROVER_OID,level:s+1,designation:e["cc"+t][i[s]][c].APPROVER_DESIGNATION_NAME}),o.push(a),t==r.length-1&&s==i.length-1&&c==e["cc"+t][i[s]].length-1)return o}}}))}storeComments(r,e,t){return new Promise((o,s)=>{this.http.post("/api/wfPrimary/updateComments",{workflowHeaderId:r,newComments:e,commentor:t}).subscribe(r=>o(r),r=>(this.errorService.userErrorAlert(r&&r.code?r.code:r&&r.error?r.error.code:"NIL","Internal Server Error while Updating Workflow Comments",r&&r.params?r.params:r&&r.error?r.error.params:{}),s(r)))})}updateWorkflowItems(r){return new Promise((e,t)=>{this.http.post("/api/wfPrimary/updateWorkflowItems",r).subscribe(r=>e(r),r=>(console.log(r),t(r)))})}formatApproversHierarchyForOpportunityApprovalActivity(r){return Object(o.c)(this,void 0,void 0,(function*(){for(let e=0;e<1;e++){let t=[],o=s.keys(r["cc"+e]);for(let s=0;s<o.length;s++)for(let i=0;i<r["cc"+e][o[s]].length;i++){let c={name:r["cc"+e][o[s]][i].DELEGATE_NAME,oid:r["cc"+e][o[s]][i].DELEGATE_OID,level:r["cc"+e][o[s]][i].APPROVAL_ORDER,designation:r["cc"+e][o[s]][i].DELEGATE_DESIGNATION_NAME,is_delegated:r["cc"+e][o[s]][i].IS_DELEGATED};if(1==r["cc"+e][o[s]][i].IS_DELEGATED&&(c.delegated_by={name:r["cc"+e][o[s]][i].APPROVER_NAME,oid:r["cc"+e][o[s]][i].APPROVER_OID,level:r["cc"+e][o[s]][i].APPROVAL_ORDER,designation:r["cc"+e][o[s]][i].APPROVER_DESIGNATION_NAME}),t.push(c),s==o.length-1&&i==r["cc"+e][o[s]].length-1)return t}}}))}}return r.\u0275fac=function(e){return new(e||r)(i["\u0275\u0275inject"](c.c),i["\u0275\u0275inject"](a.a))},r.\u0275prov=i["\u0275\u0275defineInjectable"]({token:r,factory:r.\u0275fac,providedIn:"root"}),r})()}}]);