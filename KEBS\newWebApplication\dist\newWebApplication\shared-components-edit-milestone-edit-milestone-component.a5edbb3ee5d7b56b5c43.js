(window.webpackJsonp=window.webpackJsonp||[]).push([[887],{"TmG/":function(e,t,n){"use strict";n.d(t,"a",(function(){return v}));var i=n("fXoL"),o=n("3Pt+"),l=n("jtHE"),r=n("XNiG"),a=n("NJ67"),s=n("1G5W"),c=n("kmnG"),d=n("ofXK"),p=n("d3UM"),u=n("FKr1"),m=n("WJ5W"),h=n("Qu3c");function g(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-label"),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](e.placeholder)}}function f(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-option",7),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275property"]("value",null),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](e.disableNone?"Select One":"None")}}function C(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"mat-option",8),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const n=t.$implicit;return i["\u0275\u0275nextContext"]().emitChanges(n)})),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;i["\u0275\u0275property"]("value",e.id||e._id)("matTooltip",e.name),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let v=(()=>{class e extends a.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new o.j,this.fieldFilterCtrl=new o.j,this.list=[],this.required=!1,this.disableNone=!1,this.valueChange=new i.EventEmitter,this.disabled=!1,this.hasNoneOption=!0,this.filteredList=new l.a,this.change=new i.EventEmitter,this.hideMatLabel=!1,this._onDestroy=new r.b,this.emitChanges=e=>{this.change.emit(e)}}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(s.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(s.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](i.Renderer2))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search"]],inputs:{list:"list",placeholder:"placeholder",required:"required",disableNone:"disableNone",disabled:"disabled",hasNoneOption:"hasNoneOption",hideMatLabel:"hideMatLabel"},outputs:{valueChange:"valueChange",change:"change"},features:[i["\u0275\u0275ProvidersFeature"]([{provide:o.t,useExisting:Object(i.forwardRef)(()=>e),multi:!0}]),i["\u0275\u0275InheritDefinitionFeature"],i["\u0275\u0275NgOnChangesFeature"]],decls:9,vars:11,consts:[["appearance","outline"],[4,"ngIf"],[3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value",4,"ngIf"],[3,"value","matTooltip","click",4,"ngFor","ngForOf"],[3,"value"],[3,"value","matTooltip","click"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"mat-form-field",0),i["\u0275\u0275template"](1,g,2,1,"mat-label",1),i["\u0275\u0275elementStart"](2,"mat-select",2,3),i["\u0275\u0275elementStart"](4,"mat-option"),i["\u0275\u0275element"](5,"ngx-mat-select-search",4),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](6,f,2,2,"mat-option",5),i["\u0275\u0275template"](7,C,2,3,"mat-option",6),i["\u0275\u0275pipe"](8,"async"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",!t.hideMatLabel),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.hasNoneOption),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngForOf",i["\u0275\u0275pipeBind1"](8,9,t.filteredList)))},directives:[c.c,d.NgIf,p.c,o.v,o.k,o.F,u.p,m.a,d.NgForOf,c.g,h.a],pipes:[d.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%}"]}),e})()},znuk:function(e,t,n){"use strict";n.r(t),n.d(t,"EditMilestoneComponent",(function(){return F})),n.d(t,"EditMilestoneModule",(function(){return A}));var i=n("mrSG"),o=n("0IaG"),l=n("xG9w"),r=n("ofXK"),a=n("kmnG"),s=n("qFsG"),c=n("3Pt+"),d=n("jaxi"),p=n("iadO"),u=n("FKr1"),m=n("lVl8"),h=n("7pIB"),g=n("NFeN"),f=n("bTqV"),C=n("Xi0T"),v=n("Xa2L"),b=n("Qu3c"),x=n("1jcm"),S=n("Wp6s"),M=n("fXoL"),y=n("dNgK"),E=n("a1r6"),_=n("TmG/");function w(e,t){if(1&e&&(M["\u0275\u0275elementStart"](0,"mat-hint",24),M["\u0275\u0275text"](1),M["\u0275\u0275elementEnd"]()),2&e){const e=M["\u0275\u0275nextContext"]().$implicit,t=M["\u0275\u0275nextContext"]();M["\u0275\u0275advance"](1),M["\u0275\u0275textInterpolate4"]("Amount must be greater or lesser than ",t.tolerance," percentage of ",e.orginalAmount," ( ",t.toleranceNegative," - ",t.tolerancePositive," ) ")}}const I=function(){return{standalone:!0}};function O(e,t){if(1&e){const e=M["\u0275\u0275getCurrentView"]();M["\u0275\u0275elementContainerStart"](0),M["\u0275\u0275elementStart"](1,"div",10),M["\u0275\u0275elementStart"](2,"mat-card",13),M["\u0275\u0275elementStart"](3,"div",10),M["\u0275\u0275elementStart"](4,"mat-form-field",14),M["\u0275\u0275elementStart"](5,"mat-label"),M["\u0275\u0275text"](6,"Cost Center"),M["\u0275\u0275elementEnd"](),M["\u0275\u0275elementStart"](7,"input",15),M["\u0275\u0275listener"]("ngModelChange",(function(e){return t.$implicit.cost_center_name=e}))("ngModelChange",(function(){M["\u0275\u0275restoreView"](e);const n=t.index;return M["\u0275\u0275nextContext"]().onChangeCalProduct(0,n)})),M["\u0275\u0275elementEnd"](),M["\u0275\u0275elementEnd"](),M["\u0275\u0275elementStart"](8,"mat-form-field",16),M["\u0275\u0275elementStart"](9,"mat-label"),M["\u0275\u0275text"](10,"Milestone Id"),M["\u0275\u0275elementEnd"](),M["\u0275\u0275elementStart"](11,"input",17),M["\u0275\u0275listener"]("ngModelChange",(function(e){return t.$implicit.milestone_id=e}))("ngModelChange",(function(){M["\u0275\u0275restoreView"](e);const n=t.index;return M["\u0275\u0275nextContext"]().onChangeCalProduct(0,n)})),M["\u0275\u0275elementEnd"](),M["\u0275\u0275elementEnd"](),M["\u0275\u0275elementStart"](12,"mat-form-field",16),M["\u0275\u0275elementStart"](13,"mat-label"),M["\u0275\u0275text"](14,"Header Id"),M["\u0275\u0275elementEnd"](),M["\u0275\u0275elementStart"](15,"input",18),M["\u0275\u0275listener"]("ngModelChange",(function(e){return t.$implicit.p2p_header_id=e}))("ngModelChange",(function(){M["\u0275\u0275restoreView"](e);const n=t.index;return M["\u0275\u0275nextContext"]().onChangeCalProduct(0,n)})),M["\u0275\u0275elementEnd"](),M["\u0275\u0275elementEnd"](),M["\u0275\u0275elementStart"](16,"app-input-search",19),M["\u0275\u0275listener"]("ngModelChange",(function(e){return t.$implicit.subgroup_id=e}))("ngModelChange",(function(){M["\u0275\u0275restoreView"](e);const n=t.$implicit,i=t.index;return M["\u0275\u0275nextContext"]().onChangeSubGroup(n.subgroup_id,i)})),M["\u0275\u0275elementEnd"](),M["\u0275\u0275elementEnd"](),M["\u0275\u0275elementStart"](17,"div",10),M["\u0275\u0275elementStart"](18,"mat-form-field",20),M["\u0275\u0275elementStart"](19,"mat-label"),M["\u0275\u0275text"](20,"Amount"),M["\u0275\u0275elementEnd"](),M["\u0275\u0275elementStart"](21,"input",21),M["\u0275\u0275listener"]("ngModelChange",(function(e){return t.$implicit.changeAmount=e}))("ngModelChange",(function(){M["\u0275\u0275restoreView"](e);const n=t.$implicit,i=t.index;return M["\u0275\u0275nextContext"]().onChangeAmt(n.orginalAmount,n.changeAmount,i)})),M["\u0275\u0275elementEnd"](),M["\u0275\u0275template"](22,w,2,4,"mat-hint",22),M["\u0275\u0275elementEnd"](),M["\u0275\u0275elementStart"](23,"app-input-search",23),M["\u0275\u0275listener"]("ngModelChange",(function(e){return t.$implicit.currency_code=e}))("ngModelChange",(function(){M["\u0275\u0275restoreView"](e);const n=t.$implicit,i=t.index;return M["\u0275\u0275nextContext"]().onChangeCurr(n.currency_code,i)})),M["\u0275\u0275elementEnd"](),M["\u0275\u0275elementEnd"](),M["\u0275\u0275elementEnd"](),M["\u0275\u0275elementEnd"](),M["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=t.$implicit,n=M["\u0275\u0275nextContext"]();M["\u0275\u0275advance"](7),M["\u0275\u0275property"]("disabled",n.isDisabled)("ngModel",e.cost_center_name)("ngModelOptions",M["\u0275\u0275pureFunction0"](18,I)),M["\u0275\u0275advance"](4),M["\u0275\u0275property"]("disabled",n.isDisabled)("ngModel",e.milestone_id)("ngModelOptions",M["\u0275\u0275pureFunction0"](19,I)),M["\u0275\u0275advance"](4),M["\u0275\u0275property"]("disabled",n.isDisabled)("ngModel",e.p2p_header_id)("ngModelOptions",M["\u0275\u0275pureFunction0"](20,I)),M["\u0275\u0275advance"](1),M["\u0275\u0275property"]("ngModel",e.subgroup_id)("ngModelOptions",M["\u0275\u0275pureFunction0"](21,I))("list",e.subGroup),M["\u0275\u0275advance"](5),M["\u0275\u0275property"]("ngModel",e.changeAmount)("ngModelOptions",M["\u0275\u0275pureFunction0"](22,I)),M["\u0275\u0275advance"](1),M["\u0275\u0275property"]("ngIf",e.toleranceCheck),M["\u0275\u0275advance"](1),M["\u0275\u0275property"]("ngModel",e.currency_code)("ngModelOptions",M["\u0275\u0275pureFunction0"](23,I))("list",e.currency_code_list)}}function k(e,t){1&e&&M["\u0275\u0275element"](0,"mat-spinner",25),2&e&&M["\u0275\u0275property"]("diameter",50)}let F=(()=>{class e{constructor(e,t,n,i,o,l){this.snackBar=e,this.dialogRef=t,this.data=n,this.dialog=i,this.fb=o,this._p2pGeneral=l,this.toleranceCheck=!1,this.isDisabled=!0,this.overallCheck=!0,this.spinner=!1,this.editMilestoneForm=this.fb.group({costCenter:[""],milestoneId:[""],p2pHeaderId:[""],amountInr:[""],amountUsd:[""],lessons:this.fb.array([])})}ngOnInit(){console.log("data",this.data),this.editDataForPatching=this.data.editDataForPatching,this._p2pGeneral.checkTolerancePercentage().subscribe(e=>Object(i.c)(this,void 0,void 0,(function*(){this.tolerance=e.percentage}))),this.ccSplitUps=this.editDataForPatching.ccSplitups,this.ccSplitUps.forEach(e=>{console.log("element.cost_center",e.cost_center),this._p2pGeneral.getCcSubGroup(e.cost_center,e.p2p_header_id).subscribe(t=>Object(i.c)(this,void 0,void 0,(function*(){t.length>0?(e.subGroup=t,e.subgroup_id=t[0].sub_group_name):e.subGroup=null}))),console.log("subgroup",e.subGroup),e.toleranceCheck=!1,e.currency_code=e.amount.length?e.amount[0].currency_code:"USD",e.orginalAmount=e.amount.length>0?e.amount[0].value:0,this.ccArr=l.pluck(e.amount,"currency_code");let t=[];for(let n of this.ccArr)t.push({id:n,name:n});if(e.currency_code_list=t,0==e.amount){let t=l.where(e.amount,{currency_code:"USD"});t.length>0?(e.orginalAmount=t[0].value,e.changeAmount=e.orginalAmount):e.orginalAmount=0}}),console.log("ccsplituos",this.ccSplitUps),this.amtArr=l.pluck(this.ccSplitUps,"amount"),this.mArr=l.pluck(this.ccSplitUps,"milestone_id"),this.phArr=l.pluck(this.ccSplitUps,"p2p_header_id"),console.log("ccs",this.ccArr)}onChangeAmt(e,t,n){this.ccSplitUps[n].toleranceCheck=!1,this.overallCheck=!1;let i=parseInt(this.ccSplitUps[n].orginalAmount),o=parseInt(t);console.log("orginal amount",i),this.tolerancePositive=i+i*(this.tolerance/100),this.toleranceNegative=i-i*(this.tolerance/100),console.log("tolamount",this.tolerancePositive,this.toleranceNegative),console.log("changeamt",o),o>this.tolerancePositive||o<this.toleranceNegative?(this.ccSplitUps[n].toleranceCheck=!0,this.overallCheck=!0,console.log("true")):this.ccSplitUps[n].changeAmount=o}onChangeCurr(e,t){this.ccSplitUps[t].toleranceCheck=!1;let n=l.where(this.ccSplitUps[t].amount,{currency_code:e});console.log("amtlist",n),n.length>0&&(this.ccSplitUps[t].orginalAmount=n[0].value,this.ccSplitUps[t].changeAmount=n[0].value)}onChangeSubGroup(e,t){this.ccSplitUps[t].subgroup_id=e}verify(){if(console.log("pass",this.overallCheck),this.overallCheck)this.snackBar.open("Please try again after giving valid inputs !","Dismiss",{duration:2e3}),this.spinner=!1;else{this.spinner=!0;let e=this.ccSplitUps,t=this.ccArr;console.log("ccSP",e),this._p2pGeneral.editMilestone(e,t).subscribe(e=>Object(i.c)(this,void 0,void 0,(function*(){console.log("insideapi"),"S"==e.messType?(this.snackBar.open("Milestone Updated Successfully!","Dismiss",{duration:2e3}),this.dialogRef.close("update"),console.log("success")):(this.snackBar.open("Please try again after selecting all fields!","Dismiss",{duration:2e3}),this.spinner=!1)})))}}}return e.\u0275fac=function(t){return new(t||e)(M["\u0275\u0275directiveInject"](y.a),M["\u0275\u0275directiveInject"](o.h),M["\u0275\u0275directiveInject"](o.a),M["\u0275\u0275directiveInject"](o.b),M["\u0275\u0275directiveInject"](c.i),M["\u0275\u0275directiveInject"](E.a))},e.\u0275cmp=M["\u0275\u0275defineComponent"]({type:e,selectors:[["app-edit-milestone"]],decls:19,vars:3,consts:[[1,"container"],[1,"row","mt-1",2,"border-bottom","2px solid #cf0001"],[1,"col-lg-10"],[1,"mt-2","mr-1",2,"color","#cf0001"],[1,"ml-1",2,"color","#cf0001"],[1,"col-1","p-0","d-flex","justify-content-end"],["matTooltip","Close","mat-icon-button","",1,"close-btn",3,"click"],[1,"add-lessons-form",3,"formGroup"],[4,"ngFor","ngForOf"],[1,"row","d-flex","justify-content-end"],[1,"row"],["mat-raised-button","",1,"mt-4",2,"background-color","#cf0001","color","white","margin-right","3vw",3,"click"],[3,"diameter",4,"ngIf"],[1,"mt-2",2,"background-color","#FFF9F9","width","45vw"],["appearance","outline",1,"create-account-field",2,"width","130px"],["type","text","matInput","","placeholder","Cost Center",3,"disabled","ngModel","ngModelOptions","ngModelChange"],["appearance","outline",1,"create-account-field",2,"width","100px"],["type","text","matInput","","placeholder","Milestone Id",3,"disabled","ngModel","ngModelOptions","ngModelChange"],["type","text","matInput","","placeholder","Header Id",3,"disabled","ngModel","ngModelOptions","ngModelChange"],["placeholder","Sub Group","width","100px",1,"create-account-field","mt-2",3,"ngModel","ngModelOptions","list","ngModelChange"],["appearance","outline",1,"create-account-field",2,"width","320px"],["type","number","matInput","","placeholder","Amount","oninput","if(this.value<0){this.value= this.value * -1}",3,"ngModel","ngModelOptions","ngModelChange"],["style","color: #cf0001;font-size: 10px;margin-top:5px",4,"ngIf"],["placeholder","Currency","width","100px",1,"create-account-field","mt-2",3,"ngModel","ngModelOptions","list","ngModelChange"],[2,"color","#cf0001","font-size","10px","margin-top","5px"],[3,"diameter"]],template:function(e,t){1&e&&(M["\u0275\u0275elementStart"](0,"div",0),M["\u0275\u0275elementStart"](1,"div",1),M["\u0275\u0275elementStart"](2,"div",2),M["\u0275\u0275elementStart"](3,"mat-icon",3),M["\u0275\u0275text"](4," edit "),M["\u0275\u0275elementEnd"](),M["\u0275\u0275elementStart"](5,"b",4),M["\u0275\u0275text"](6,"Edit Milestone"),M["\u0275\u0275elementEnd"](),M["\u0275\u0275elementEnd"](),M["\u0275\u0275elementStart"](7,"div",5),M["\u0275\u0275elementStart"](8,"button",6),M["\u0275\u0275listener"]("click",(function(){return t.dialogRef.close("close")})),M["\u0275\u0275elementStart"](9,"mat-icon"),M["\u0275\u0275text"](10,"close"),M["\u0275\u0275elementEnd"](),M["\u0275\u0275elementEnd"](),M["\u0275\u0275elementEnd"](),M["\u0275\u0275elementEnd"](),M["\u0275\u0275elementStart"](11,"div",7),M["\u0275\u0275template"](12,O,24,24,"ng-container",8),M["\u0275\u0275elementStart"](13,"div",9),M["\u0275\u0275elementStart"](14,"div",10),M["\u0275\u0275elementStart"](15,"button",11),M["\u0275\u0275listener"]("click",(function(){return t.verify()})),M["\u0275\u0275text"](16,"Edit"),M["\u0275\u0275elementEnd"](),M["\u0275\u0275elementEnd"](),M["\u0275\u0275elementStart"](17,"div",10),M["\u0275\u0275template"](18,k,1,1,"mat-spinner",12),M["\u0275\u0275elementEnd"](),M["\u0275\u0275elementEnd"](),M["\u0275\u0275elementEnd"](),M["\u0275\u0275elementEnd"]()),2&e&&(M["\u0275\u0275advance"](11),M["\u0275\u0275property"]("formGroup",t.editMilestoneForm),M["\u0275\u0275advance"](1),M["\u0275\u0275property"]("ngForOf",t.ccSplitUps),M["\u0275\u0275advance"](6),M["\u0275\u0275property"]("ngIf",t.spinner))},directives:[g.a,f.a,b.a,c.w,c.n,r.NgForOf,r.NgIf,S.a,a.c,a.g,s.b,c.e,c.v,c.y,_.a,c.A,a.f,v.c],styles:[".create-account-field[_ngcontent-%COMP%]{margin-top:10px;margin-left:5px;margin-right:5px}"]}),e})(),A=(()=>{class e{}return e.\u0275mod=M["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=M["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},providers:[],imports:[[r.CommonModule,o.g,a.e,s.c,c.E,c.p,d.c,p.h,u.x,m.b,h.c,g.b,f.b,C.a,v.b,b.b,x.b,S.d]]}),e})()}}]);