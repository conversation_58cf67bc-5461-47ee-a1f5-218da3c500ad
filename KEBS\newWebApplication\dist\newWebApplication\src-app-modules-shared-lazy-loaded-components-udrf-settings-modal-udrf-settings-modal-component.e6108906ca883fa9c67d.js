(window.webpackJsonp=window.webpackJsonp||[]).push([[1e3,535,631,634,858],{NJ67:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));class a{constructor(){this.disabled=!1}onChange(e){}onTouched(e){}writeValue(e){this.value=e}registerOnChange(e){this.onChange=e}registerOnTouched(e){this.onTouched=e}setDisabledState(e){this.disabled=e}}},"TmG/":function(e,t,n){"use strict";n.d(t,"a",(function(){return v}));var a=n("fXoL"),r=n("3Pt+"),i=n("jtHE"),l=n("XNiG"),o=n("NJ67"),s=n("1G5W"),d=n("kmnG"),c=n("ofXK"),m=n("d3UM"),u=n("FKr1"),p=n("WJ5W"),g=n("Qu3c");function f(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"mat-label"),a["\u0275\u0275text"](1),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"]();a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate"](e.placeholder)}}function h(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"mat-option",7),a["\u0275\u0275text"](1),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"]();a["\u0275\u0275property"]("value",null),a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate"](e.disableNone?"Select One":"None")}}function C(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"mat-option",8),a["\u0275\u0275listener"]("click",(function(){a["\u0275\u0275restoreView"](e);const n=t.$implicit;return a["\u0275\u0275nextContext"]().emitChanges(n)})),a["\u0275\u0275text"](1),a["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;a["\u0275\u0275property"]("value",e.id||e._id)("matTooltip",e.name),a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let v=(()=>{class e extends o.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new r.j,this.fieldFilterCtrl=new r.j,this.list=[],this.required=!1,this.disableNone=!1,this.valueChange=new a.EventEmitter,this.disabled=!1,this.hasNoneOption=!0,this.filteredList=new i.a,this.change=new a.EventEmitter,this.hideMatLabel=!1,this._onDestroy=new l.b,this.emitChanges=e=>{this.change.emit(e)}}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(s.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(s.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275directiveInject"](a.Renderer2))},e.\u0275cmp=a["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search"]],inputs:{list:"list",placeholder:"placeholder",required:"required",disableNone:"disableNone",disabled:"disabled",hasNoneOption:"hasNoneOption",hideMatLabel:"hideMatLabel"},outputs:{valueChange:"valueChange",change:"change"},features:[a["\u0275\u0275ProvidersFeature"]([{provide:r.t,useExisting:Object(a.forwardRef)(()=>e),multi:!0}]),a["\u0275\u0275InheritDefinitionFeature"],a["\u0275\u0275NgOnChangesFeature"]],decls:9,vars:11,consts:[["appearance","outline"],[4,"ngIf"],[3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value",4,"ngIf"],[3,"value","matTooltip","click",4,"ngFor","ngForOf"],[3,"value"],[3,"value","matTooltip","click"]],template:function(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"mat-form-field",0),a["\u0275\u0275template"](1,f,2,1,"mat-label",1),a["\u0275\u0275elementStart"](2,"mat-select",2,3),a["\u0275\u0275elementStart"](4,"mat-option"),a["\u0275\u0275element"](5,"ngx-mat-select-search",4),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](6,h,2,2,"mat-option",5),a["\u0275\u0275template"](7,C,2,3,"mat-option",6),a["\u0275\u0275pipe"](8,"async"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e&&(a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",!t.hideMatLabel),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",t.hasNoneOption),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngForOf",a["\u0275\u0275pipeBind1"](8,9,t.filteredList)))},directives:[d.c,c.NgIf,m.c,r.v,r.k,r.F,u.p,p.a,c.NgForOf,d.g,g.a],pipes:[c.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%}"]}),e})()},c1X9:function(e,t,n){"use strict";n.r(t),n.d(t,"UdrfSettingsModalComponent",(function(){return R}));var a=n("mrSG"),r=n("0IaG"),i=n("3Pt+"),l=n("xG9w"),o=n("ofXK"),s=n("bTqV"),d=n("NFeN"),c=n("Qu3c"),m=n("jaxi"),u=n("5+WD"),p=(n("Xi0T"),n("fXoL")),g=n("LcQX"),f=n("GnQ3"),h=n("flaP"),C=n("TmG/");function v(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",17),p["\u0275\u0275elementStart"](1,"form",18),p["\u0275\u0275elementStart"](2,"div"),p["\u0275\u0275element"](3,"app-input-search",19),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](2);p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("formGroup",e.currencyFormGroup),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("list",e.udrfService.udrfUiData.currencyValues)}}function y(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",11),p["\u0275\u0275elementStart"](1,"div",12),p["\u0275\u0275elementStart"](2,"p",13),p["\u0275\u0275text"](3),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](4,v,4,2,"div",14),p["\u0275\u0275elementStart"](5,"div",15),p["\u0275\u0275elementStart"](6,"button",16),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"]().saveSettings()})),p["\u0275\u0275elementStart"](7,"mat-icon",4),p["\u0275\u0275text"](8,"save"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"]();p["\u0275\u0275advance"](3),p["\u0275\u0275textInterpolate1"]("You can choose the summary cards that should be visible in the report (Maximum ",e.modalParams.maxNoOfVisibleSummaryCards," cards) "),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",86==e.udrfService.udrfData.applicationId||242==e.udrfService.udrfData.applicationId)}}function x(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"span",39),p["\u0275\u0275text"](1),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"]().$implicit;p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate"](e.dataTypeUnit)}}function b(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"span",39),p["\u0275\u0275text"](1),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](4);p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate"](e.modalParams.dataTypeUnit)}}function S(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",31),p["\u0275\u0275elementStart"](1,"div",32),p["\u0275\u0275elementStart"](2,"div",33),p["\u0275\u0275listener"]("click",(function(){p["\u0275\u0275restoreView"](e);const n=t.$implicit;return p["\u0275\u0275nextContext"](3).categoryCardSelected(n)})),p["\u0275\u0275elementStart"](3,"div",34),p["\u0275\u0275elementStart"](4,"span",35),p["\u0275\u0275text"](5),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](6,x,2,1,"span",36),p["\u0275\u0275template"](7,b,2,1,"span",36),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](8,"div",37),p["\u0275\u0275elementStart"](9,"span",38),p["\u0275\u0275text"](10),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=p["\u0275\u0275nextContext"](3);p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngClass",e.isVisible?"data-type-card-is-active":""),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("matTooltip",e.dataTypeDescription),p["\u0275\u0275advance"](3),p["\u0275\u0275textInterpolate1"](" ",e.dataTypeValue," "),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",e.dataTypeUnit),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",n.modalParams.dataTypeUnit),p["\u0275\u0275advance"](3),p["\u0275\u0275textInterpolate2"](" ",e.dataType," ",e.dataTypeCurrency," ")}}function P(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div"),p["\u0275\u0275elementStart"](1,"div",27),p["\u0275\u0275elementStart"](2,"p",28),p["\u0275\u0275text"](3),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](4,"div",29),p["\u0275\u0275template"](5,S,11,7,"div",30),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;p["\u0275\u0275advance"](3),p["\u0275\u0275textInterpolate"](e.categoryType),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngForOf",e.categoryCards)}}function O(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",40),p["\u0275\u0275elementStart"](1,"div",20),p["\u0275\u0275elementStart"](2,"p",41),p["\u0275\u0275text"](3,"Currency"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](4,"mat-icon",42),p["\u0275\u0275text"](5,"info"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](6,"div",29),p["\u0275\u0275elementStart"](7,"div",17),p["\u0275\u0275elementStart"](8,"form",18),p["\u0275\u0275elementStart"](9,"div"),p["\u0275\u0275element"](10,"app-input-search",43),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"](2);p["\u0275\u0275advance"](8),p["\u0275\u0275property"]("formGroup",e.currencyFormGroup),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("list",e.udrfService.udrfUiData.currencyValues)}}function M(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",20),p["\u0275\u0275elementStart"](1,"div",21),p["\u0275\u0275template"](2,P,6,2,"div",22),p["\u0275\u0275template"](3,O,11,2,"div",23),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](4,"div",24),p["\u0275\u0275elementStart"](5,"div",25),p["\u0275\u0275element"](6,"img",26),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"]();p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngForOf",e.modalParams.categorisedSummaryCards),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",36==e.udrfService.udrfData.applicationId)}}function w(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",44),p["\u0275\u0275elementStart"](1,"div",45),p["\u0275\u0275elementStart"](2,"p",13),p["\u0275\u0275text"](3),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](4,"div",46),p["\u0275\u0275elementStart"](5,"button",16),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"]().saveSettings()})),p["\u0275\u0275elementStart"](6,"mat-icon",4),p["\u0275\u0275text"](7,"save"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](8,"button",47),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"]().clearSettings()})),p["\u0275\u0275elementStart"](9,"mat-icon",4),p["\u0275\u0275text"](10,"clear"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"]();p["\u0275\u0275advance"](3),p["\u0275\u0275textInterpolate1"]("You can choose the ",e.modalParams.noOfVisibleSummaryCards," cards that should be visible in the widget view and set the other settings ")}}const V=function(e){return{backgroundColor:e}};function E(e,t){if(1&e&&p["\u0275\u0275element"](0,"div",69),2&e){const e=p["\u0275\u0275nextContext"]().$implicit;p["\u0275\u0275property"]("ngStyle",p["\u0275\u0275pureFunction1"](1,V,e.statusColor))}}function _(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"span",70),p["\u0275\u0275text"](1),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"]().$implicit;p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate"](e.dataTypeName?e.dataTypeName:e.description)}}function I(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"span",38),p["\u0275\u0275text"](1),p["\u0275\u0275elementEnd"]()),2&e){const e=p["\u0275\u0275nextContext"]().$implicit;p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate"](e.dataTypeName?e.dataTypeName:e.description)}}function T(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",65),p["\u0275\u0275listener"]("click",(function(){p["\u0275\u0275restoreView"](e);const n=t.$implicit;return p["\u0275\u0275nextContext"](3).categoryCardSelected(n)})),p["\u0275\u0275template"](1,E,1,3,"div",66),p["\u0275\u0275template"](2,_,2,1,"span",67),p["\u0275\u0275template"](3,I,2,1,"span",68),p["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=p["\u0275\u0275nextContext"]().$implicit;p["\u0275\u0275property"]("cdkDragDisabled",n.isDisabled)("ngClass",e.isSelected||e.to_display_in_ngr?"example-box-is-active":"")("matTooltip",e.dataTypeDescription?e.dataTypeDescription:e.dataTypeName?e.dataTypeName:e.description),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",e.statusColor),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",e.statusColor),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!e.statusColor)}}function N(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div"),p["\u0275\u0275elementStart"](1,"div",27),p["\u0275\u0275elementStart"](2,"p",28),p["\u0275\u0275text"](3),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](4,"div",29),p["\u0275\u0275elementStart"](5,"div",63),p["\u0275\u0275listener"]("cdkDropListDropped",(function(n){p["\u0275\u0275restoreView"](e);const a=t.index;return p["\u0275\u0275nextContext"](2).dropCategoryCard(n,a)})),p["\u0275\u0275template"](6,T,4,6,"div",64),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;p["\u0275\u0275advance"](3),p["\u0275\u0275textInterpolate"](e.categoryType),p["\u0275\u0275advance"](3),p["\u0275\u0275property"]("ngForOf",e.categoryCards)}}function F(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",57),p["\u0275\u0275elementStart"](1,"p",28),p["\u0275\u0275text"](2,"Show Variance in MIS Budget?"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](3,"div",29),p["\u0275\u0275elementStart"](4,"mat-button-toggle-group",51),p["\u0275\u0275listener"]("change",(function(t){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](2).toggleVarianceVisible(t)})),p["\u0275\u0275elementStart"](5,"mat-button-toggle",58),p["\u0275\u0275text"](6," Yes "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](7,"mat-button-toggle",59),p["\u0275\u0275text"](8," No "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"](2);p["\u0275\u0275advance"](4),p["\u0275\u0275property"]("value",e.modalParams.isMisVarianceVisible),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngClass","Yes"==e.modalParams.isMisVarianceVisible?"btn-toggle-selected":""),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngClass","No"==e.modalParams.isMisVarianceVisible?"btn-toggle-selected":"")}}function D(e,t){1&e&&p["\u0275\u0275element"](0,"div",57)}function B(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",57),p["\u0275\u0275elementStart"](1,"p",28),p["\u0275\u0275text"](2,"Should MIS Cards Be Full-Sized?"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](3,"div",29),p["\u0275\u0275elementStart"](4,"mat-button-toggle-group",51),p["\u0275\u0275listener"]("change",(function(t){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"](2).toggleMisCardsFullSized(t)})),p["\u0275\u0275elementStart"](5,"mat-button-toggle",58),p["\u0275\u0275text"](6," Yes "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](7,"mat-button-toggle",59),p["\u0275\u0275text"](8," No "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"](2);p["\u0275\u0275advance"](4),p["\u0275\u0275property"]("value",e.modalParams.shouldMisCardsBeFullSized),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngClass","Yes"==e.modalParams.shouldMisCardsBeFullSized?"btn-toggle-selected":""),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngClass","No"==e.modalParams.shouldMisCardsBeFullSized?"btn-toggle-selected":"")}}function k(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"mat-button-toggle",71),p["\u0275\u0275text"](1),p["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=p["\u0275\u0275nextContext"](2);p["\u0275\u0275property"]("value",e)("ngClass",n.modalParams.defaultCurrency==e?"btn-toggle-selected":""),p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" ",e," ")}}function G(e,t){if(1&e){const e=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",20),p["\u0275\u0275elementStart"](1,"div",48),p["\u0275\u0275elementStart"](2,"div",49),p["\u0275\u0275elementStart"](3,"div",50),p["\u0275\u0275elementStart"](4,"p",28),p["\u0275\u0275text"](5,"Report View"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](6,"div",29),p["\u0275\u0275elementStart"](7,"mat-button-toggle-group",51),p["\u0275\u0275listener"]("change",(function(t){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"]().toggleDefaultReportView(t)})),p["\u0275\u0275elementStart"](8,"mat-button-toggle",52),p["\u0275\u0275text"](9," Card "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](10,"mat-button-toggle",53),p["\u0275\u0275text"](11,"Widget "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](12,"div",54),p["\u0275\u0275elementStart"](13,"div",21),p["\u0275\u0275elementStart"](14,"p",28),p["\u0275\u0275text"](15,"Overall PoC Calculation Method"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](16,"div",29),p["\u0275\u0275elementStart"](17,"mat-button-toggle-group",51),p["\u0275\u0275listener"]("change",(function(t){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"]().toggleOverallPoCCalcMethod(t)})),p["\u0275\u0275elementStart"](18,"mat-button-toggle",55),p["\u0275\u0275text"](19," Average of PoCs "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](20,"mat-button-toggle",56),p["\u0275\u0275text"](21," Based on Planned-Actual "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](22,"div",54),p["\u0275\u0275elementStart"](23,"div",50),p["\u0275\u0275template"](24,N,7,2,"div",22),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](25,"div",54),p["\u0275\u0275elementStart"](26,"div",57),p["\u0275\u0275elementStart"](27,"p",28),p["\u0275\u0275text"](28,"Show Budget in MIS?"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](29,"div",29),p["\u0275\u0275elementStart"](30,"mat-button-toggle-group",51),p["\u0275\u0275listener"]("change",(function(t){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"]().toggleBudgetVisible(t)})),p["\u0275\u0275elementStart"](31,"mat-button-toggle",58),p["\u0275\u0275text"](32," Yes "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](33,"mat-button-toggle",59),p["\u0275\u0275text"](34," No "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](35,F,9,3,"div",60),p["\u0275\u0275template"](36,D,1,0,"div",60),p["\u0275\u0275template"](37,B,9,3,"div",60),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](38,"div",61),p["\u0275\u0275elementStart"](39,"div",57),p["\u0275\u0275elementStart"](40,"p",28),p["\u0275\u0275text"](41,"Show Full Value?"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](42,"div",29),p["\u0275\u0275elementStart"](43,"mat-button-toggle-group",51),p["\u0275\u0275listener"]("change",(function(t){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"]().toggleFullValue(t)})),p["\u0275\u0275elementStart"](44,"mat-button-toggle",58),p["\u0275\u0275text"](45," Yes "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](46,"mat-button-toggle",59),p["\u0275\u0275text"](47," No "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](48,"div",57),p["\u0275\u0275elementStart"](49,"p",28),p["\u0275\u0275text"](50,"Currency"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](51,"div",29),p["\u0275\u0275elementStart"](52,"mat-button-toggle-group",51),p["\u0275\u0275listener"]("change",(function(t){return p["\u0275\u0275restoreView"](e),p["\u0275\u0275nextContext"]().toggleDefaultCurrency(t)})),p["\u0275\u0275template"](53,k,2,3,"mat-button-toggle",62),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](54,"div",24),p["\u0275\u0275elementStart"](55,"div",25),p["\u0275\u0275element"](56,"img",26),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&e){const e=p["\u0275\u0275nextContext"]();p["\u0275\u0275advance"](7),p["\u0275\u0275property"]("value",e.modalParams.defaultReportView),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngClass","Card"==e.modalParams.defaultReportView?"btn-toggle-selected":""),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngClass","Widget"==e.modalParams.defaultReportView?"btn-toggle-selected":""),p["\u0275\u0275advance"](7),p["\u0275\u0275property"]("value",e.modalParams.overallPoCCalcMethod),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngClass","Average of PoCs"==e.modalParams.overallPoCCalcMethod?"btn-toggle-selected":""),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngClass","Based on Planned-Actual"==e.modalParams.overallPoCCalcMethod?"btn-toggle-selected":""),p["\u0275\u0275advance"](4),p["\u0275\u0275property"]("ngForOf",e.modalParams.categorisedSummaryCards),p["\u0275\u0275advance"](6),p["\u0275\u0275property"]("value",e.modalParams.isMisBudgetVisible),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngClass","Yes"==e.modalParams.isMisBudgetVisible?"btn-toggle-selected":""),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngClass","No"==e.modalParams.isMisBudgetVisible?"btn-toggle-selected":""),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngIf","Yes"==e.modalParams.isMisBudgetVisible),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf","No"==e.modalParams.isMisBudgetVisible),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",e.calculateNoOfAdditions()>1),p["\u0275\u0275advance"](6),p["\u0275\u0275property"]("value",e.modalParams.isFullValue),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngClass","Yes"==e.modalParams.isFullValue?"btn-toggle-selected":""),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngClass","No"==!e.modalParams.isFullValue?"btn-toggle-selected":""),p["\u0275\u0275advance"](6),p["\u0275\u0275property"]("value",e.modalParams.defaultCurrency),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngForOf",e.modalParams.reportingCurrencies)}}let R=(()=>{class e{constructor(e,t,n,a,r,i){this.dialogRef=e,this.inData=t,this.utilityService=n,this.udrfService=a,this.fb=r,this.roleService=i,this.hasCategorisedSummaryCardsChanged=!1,this.currencyFormGroup=this.fb.group({currency_value:[""]})}ngOnInit(){return Object(a.c)(this,void 0,void 0,(function*(){if(this.modalParams=this.inData.modalParams,"BBR"==this.modalParams.reportType&&(this.tenant_info=this.roleService.currency_info,this.currency_code=this.tenant_info.default_currency?this.tenant_info.default_currency:"INR",this.isConvertValue=null==this.tenant_info.is_to_convert_currency_value||this.tenant_info.is_to_convert_currency_value,86==this.udrfService.udrfData.applicationId||242==this.udrfService.udrfData.applicationId||36==this.udrfService.udrfData.applicationId)){let e=l.where(this.udrfService.udrfUiData.currencyValues,{name:this.udrfService.udrfUiData.selectedCurrencyValue});0!=e.length&&this.currencyFormGroup.patchValue({currency_value:e[0].id}),this.currencyFormGroup.get("currency_value").valueChanges.subscribe(e=>Object(a.c)(this,void 0,void 0,(function*(){if(e){console.log("valuechange");let t=l.where(this.udrfService.udrfUiData.currencyValues,{id:e});this.udrfService.udrfData.currencyType=t,this.udrfService.udrfUiData.selectedCurrencyValue=t[0].name,36==this.udrfService.udrfData.applicationId&&(yield this.udrfService.udrfUiData.updateGeneralConfigUserLevel("OPP_CURRENCY",t)),l.each(this.modalParams.categorisedSummaryCards,e=>{l.each(e.categoryCards,e=>{"dataTypeCurrency"in e&&("INR"!=t[0].name?(e.dataTypeValueOriginal=this.utilityService.getDisplayValueInUSDFormat((e.dataTypeCurrencyValueOriginal*t[0].currency_value).toFixed(2)),e.dataTypeValue=0==this.isConvertValue?e.dataTypeValueOriginal:this.utilityService.convertFromMillion(e.dataTypeCurrencyValueOriginal*t[0].currency_value),e.dataTypeUnit=0==this.isConvertValue?t[0].name:"M",e.dataTypeCurrency=t[0].name):(e.dataTypeValueOriginal=this.utilityService.getDisplayValueInInrFormat(parseFloat(e.dataTypeCurrencyValueOriginal).toFixed(2)),e.dataTypeValue=0==this.isConvertValue?e.dataTypeValueOriginal:this.utilityService.convertFromCr(parseFloat(e.dataTypeCurrencyValueOriginal)),e.dataTypeUnit=0==this.isConvertValue?"INR":"Cr",e.dataTypeCurrency=t[0].name))})})}})))}}))}categoryCardSelected(e){let t=0;if("BBR"==this.modalParams.reportType){for(let e of this.modalParams.categorisedSummaryCards)for(let n of e.categoryCards)n.isVisible&&t++;e.isVisible?t>this.modalParams.minNoOfVisibleSummaryCards?e.isVisible=!e.isVisible:this.utilityService.showToastMessage("A minimum of "+this.modalParams.minNoOfVisibleSummaryCards+" summary card(s) must be visible in the report !"):t<this.modalParams.maxNoOfVisibleSummaryCards?e.isVisible=!e.isVisible:this.utilityService.showToastMessage("You have already chosen "+this.modalParams.maxNoOfVisibleSummaryCards+" summary card(s) that will be visible in the report !")}else null!=e.isSelected&&(e.isSelected=!e.isSelected),null!=e.to_display_in_ngr&&(e.to_display_in_ngr=!e.to_display_in_ngr),this.hasCategorisedSummaryCardsChanged=!0,this.updateNoOfVisibleCards()}saveSettings(){if(this.changeCurrency(),"BBR"==this.modalParams.reportType)this.dialogRef.close({event:"Save",categorisedSummaryCards:this.modalParams.categorisedSummaryCards});else if("NGR"==this.modalParams.reportType){let e=0,t=0;for(let n of this.modalParams.categorisedSummaryCards){e=0;for(let t of n.categoryCards)t.isSelected&&e++;if("GBM Cards"==n.categoryType)e<1?(t=1,this.utilityService.showToastMessage("A minimum of 1 GBM card(s) must be visible in the report !")):e>6&&(t=1,this.utilityService.showToastMessage("A maximum of 6 GBM card(s) can be visible in the report !"));else if("MIS Cards"==n.categoryType)if(e<this.modalParams.noOfVisibleSummaryCards){let t=this.modalParams.noOfVisibleSummaryCards-e;this.utilityService.showToastMessage(t+" summary card(s) have been selected according to your settings. You may customize as needed again.");for(let e of this.modalParams.categorisedSummaryCards)for(let n=0;t>0;n++)e.categoryCards[n].isSelected||(this.categoryCardSelected(e.categoryCards[n]),t--)}else if(e>this.modalParams.noOfVisibleSummaryCards){let t=e-this.modalParams.noOfVisibleSummaryCards;this.utilityService.showToastMessage(t+" summary card(s) have been unselected according to your settings. You may customize as needed again.");for(let e of this.modalParams.categorisedSummaryCards)for(let n=0;t>0;n++)e.categoryCards[n].isSelected&&(this.categoryCardSelected(e.categoryCards[n]),t--)}}0==t&&this.dialogRef.close({event:"Save",categorisedSummaryCards:this.modalParams.categorisedSummaryCards,hasCategorisedSummaryCardsChanged:this.hasCategorisedSummaryCardsChanged,defaultReportView:this.modalParams.defaultReportView,overallPoCCalcMethod:this.modalParams.overallPoCCalcMethod,isFullValue:this.modalParams.isFullValue,defaultCurrency:this.modalParams.defaultCurrency,isMisBudgetVisible:this.modalParams.isMisBudgetVisible,isMisVarianceVisible:this.modalParams.isMisVarianceVisible,shouldMisCardsBeFullSized:this.modalParams.shouldMisCardsBeFullSized,noOfVisibleSummaryCards:this.modalParams.noOfVisibleSummaryCards,misCardClass:this.modalParams.misCardClass,misDetailedModeCardClass:this.modalParams.misDetailedModeCardClass})}}clearSettings(){return Object(a.c)(this,void 0,void 0,(function*(){yield this.udrfService.udrfFunctions.resetUserUdrfConfig(this.modalParams.udrfData)}))}toggleDefaultReportView(e){this.modalParams.defaultReportView=e.value}toggleOverallPoCCalcMethod(e){this.modalParams.overallPoCCalcMethod=e.value}toggleFullValue(e){this.modalParams.isFullValue=e.value,this.updateNoOfVisibleCards()}toggleDefaultCurrency(e){this.modalParams.defaultCurrency=e.value}toggleBudgetVisible(e){this.modalParams.isMisBudgetVisible=e.value,"No"==this.modalParams.isMisBudgetVisible&&(this.modalParams.isMisVarianceVisible=this.modalParams.isMisBudgetVisible),this.updateNoOfVisibleCards()}toggleVarianceVisible(e){this.modalParams.isMisVarianceVisible=e.value,this.updateNoOfVisibleCards()}toggleMisCardsFullSized(e){this.modalParams.shouldMisCardsBeFullSized=e.value,this.updateNoOfVisibleCards()}calculateNoOfAdditions(){let e=0;return"Yes"==this.modalParams.isMisBudgetVisible&&e++,"Yes"==this.modalParams.isFullValue&&e++,"Yes"==this.modalParams.isMisVarianceVisible&&e++,e}updateNoOfVisibleCards(){let e=this.calculateNoOfAdditions(),t=[];for(let n of this.modalParams.categorisedSummaryCards)if("MIS Cards"==n.categoryType)for(let e of n.categoryCards)e.isSelected&&t.push(e);e>1?(this.modalParams.noOfVisibleSummaryCards=t.length,this.modalParams.misDetailedModeCardClass="col-1-16-5"):e>0?(this.modalParams.noOfVisibleSummaryCards=this.modalParams.maxNoOfVisibleSummaryCards,this.modalParams.misCardClass="col-5-5 mt-2",this.modalParams.misDetailedModeCardClass="col-1-10-5"):(this.modalParams.noOfVisibleSummaryCards=this.modalParams.minNoOfVisibleSummaryCards,this.modalParams.misCardClass="col-3-5",this.modalParams.misDetailedModeCardClass="col-1-10-5")}dropCategoryCard(e,t){Object(u.h)(this.modalParams.categorisedSummaryCards[t].categoryCards,e.previousIndex,e.currentIndex);let n=1;for(let a of this.modalParams.categorisedSummaryCards[t].categoryCards)a.order=n++;this.hasCategorisedSummaryCardsChanged=!0}changeCurrency(){if(36==this.udrfService.udrfData.applicationId){console.log(this.currencyFormGroup.get("currency_value").value);let e=l.where(this.udrfService.udrfUiData.currencyValues,{id:this.currencyFormGroup.get("currency_value").value});console.log(e),this.udrfService.udrfFormData.selectedCurrency.patchValue(e[0].name),this.utilityService.showToastMessage("Settings saved")}}closeModal(){this.dialogRef.close({event:"Close"})}}return e.\u0275fac=function(t){return new(t||e)(p["\u0275\u0275directiveInject"](r.h),p["\u0275\u0275directiveInject"](r.a),p["\u0275\u0275directiveInject"](g.a),p["\u0275\u0275directiveInject"](f.a),p["\u0275\u0275directiveInject"](i.i),p["\u0275\u0275directiveInject"](h.a))},e.\u0275cmp=p["\u0275\u0275defineComponent"]({type:e,selectors:[["udrf-settings-modal"]],decls:16,vars:4,consts:[[1,"container-fluid","udrf-settings-modal-styles"],[1,"row","pt-2","pb-2",2,"border-bottom","solid 1px #cacaca"],[1,"col-1","pl-0","pr-0"],["mat-icon-button","",1,"bubble","alignCenter"],[1,"iconButton"],[1,"col-10","pl-0","pr-0","name","alignVerticalCenter"],["mat-icon-button","","matTooltip","Close",1,"d-flex","ml-auto","mr-2","close-button",3,"click"],[1,"close-icon"],["class","row mt-3 mb-1","style","display: flex; justify-content: space-between;",4,"ngIf"],["class","row",4,"ngIf"],["class","row mt-3 mb-1",4,"ngIf"],[1,"row","mt-3","mb-1",2,"display","flex","justify-content","space-between"],[1,"col-7","pl-4"],[1,"value14"],["class","col-4",4,"ngIf"],[1,"col-1"],["mat-icon-button","","matTooltip","Save Settings",1,"view-button-active",3,"click"],[1,"col-4"],[3,"formGroup"],["required","true","placeholder","Select Currency","formControlName","currency_value",3,"list"],[1,"row"],[1,"col-9","pl-4"],[4,"ngFor","ngForOf"],["class","pb-5",4,"ngIf"],[1,"col-3","pl-0","pr-0","alignVerticalCenter"],[1,"d-flex","justify-content-center","align-items-center","slide-from-down"],["src","https://assets.kebs.app/images/choose_cost_centre.png","height","220","width","250"],[1,"mb-2","mt-2"],[1,"value14RedBold"],[1,"row","mt-2"],["class","col-4 pb-0 pr-4 pl-0 pt-0 mb-3",4,"ngFor","ngForOf"],[1,"col-4","pb-0","pr-4","pl-0","pt-0","mb-3"],[1,"card","data-type-card","slide-in-top",3,"ngClass"],["content-type","template",1,"card-body","p-1","cp",3,"matTooltip","click"],[1,"row","d-flex","justify-content-center","pt-3"],[1,"headingBold"],["class","ml-1 mt-1 valueGrey12Normal",4,"ngIf"],[1,"row","d-flex","justify-content-center","pt-2"],[1,"valueGrey14"],[1,"ml-1","mt-1","valueGrey12Normal"],[1,"pb-5"],[1,"value14RedBold","pr-5"],["matTooltipPosition","above","matTooltip","Select a option to view the Opportunity value in the preferred currency ",1,"info-icon"],["placeholder","Select Currency","formControlName","currency_value",3,"list"],[1,"row","mt-3","mb-1"],[1,"col-10","pl-4"],[1,"col-2"],["mat-icon-button","","matTooltip","Clear Settings",1,"view-button-inactive",3,"click"],[1,"col-9"],[1,"row","mt-2","mb-2"],[1,"col-12","pl-4"],[3,"value","change"],["value","Card",2,"width","70px",3,"ngClass"],["value","Widget",2,"width","70px",3,"ngClass"],[1,"row","mt-3","mb-2"],["value","Average of PoCs",2,"width","150px",3,"ngClass"],["value","Based on Planned-Actual",2,"width","180px",3,"ngClass"],[1,"col-4","pl-4"],["value","Yes",2,"width","70px",3,"ngClass"],["value","No",2,"width","70px",3,"ngClass"],["class","col-4 pl-4",4,"ngIf"],[1,"row","mt-3"],["style","width: 70px;",3,"value","ngClass",4,"ngFor","ngForOf"],["cdkDropList","","cdkDropListOrientation","horizontal",1,"example-list",3,"cdkDropListDropped"],["class","example-box","cdkDrag","","style","min-width: 100px !important;",3,"cdkDragDisabled","ngClass","matTooltip","click",4,"ngFor","ngForOf"],["cdkDrag","",1,"example-box",2,"min-width","100px !important",3,"cdkDragDisabled","ngClass","matTooltip","click"],["class","status-circular",3,"ngStyle",4,"ngIf"],["class","pl-2 valueGrey14",4,"ngIf"],["class","valueGrey14",4,"ngIf"],[1,"status-circular",3,"ngStyle"],[1,"pl-2","valueGrey14"],[2,"width","70px",3,"value","ngClass"]],template:function(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"div",0),p["\u0275\u0275elementStart"](1,"div",1),p["\u0275\u0275elementStart"](2,"div",2),p["\u0275\u0275elementStart"](3,"div",3),p["\u0275\u0275elementStart"](4,"mat-icon",4),p["\u0275\u0275text"](5,"settings"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](6,"div",5),p["\u0275\u0275text"](7,"Settings"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](8,"div",2),p["\u0275\u0275elementStart"](9,"button",6),p["\u0275\u0275listener"]("click",(function(){return t.closeModal()})),p["\u0275\u0275elementStart"](10,"mat-icon",7),p["\u0275\u0275text"](11,"close"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](12,y,9,2,"div",8),p["\u0275\u0275template"](13,M,7,2,"div",9),p["\u0275\u0275template"](14,w,11,1,"div",10),p["\u0275\u0275template"](15,G,57,18,"div",9),p["\u0275\u0275elementEnd"]()),2&e&&(p["\u0275\u0275advance"](12),p["\u0275\u0275property"]("ngIf","BBR"==t.modalParams.reportType),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf","BBR"==t.modalParams.reportType),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf","NGR"==t.modalParams.reportType),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf","NGR"==t.modalParams.reportType))},directives:[d.a,s.a,c.a,o.NgIf,i.J,i.w,i.n,C.a,i.F,i.v,i.l,o.NgForOf,o.NgClass,m.b,m.a,u.e,u.a,o.NgStyle],styles:[".udrf-settings-modal-styles[_ngcontent-%COMP%]{background-size:200px 181px;background-repeat:no-repeat;overflow-y:hidden!important;min-height:95vh;background-position:99% 105%;padding-left:0!important;padding-right:0!important}.udrf-settings-modal-styles[_ngcontent-%COMP%]   .status-circular[_ngcontent-%COMP%]{height:13px;width:13px;margin-top:4px;border-radius:50%;box-shadow:0 2px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.udrf-settings-modal-styles[_ngcontent-%COMP%]   .alignCenter[_ngcontent-%COMP%]{margin:0 auto;display:block}.udrf-settings-modal-styles[_ngcontent-%COMP%]   .alignVerticalCenter[_ngcontent-%COMP%]{display:flex;align-items:center}.udrf-settings-modal-styles[_ngcontent-%COMP%]   .view-button-active[_ngcontent-%COMP%]{background-color:#cf0001!important;align-self:center;line-height:8px;width:26px;height:26px;margin-right:10px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12);animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}.udrf-settings-modal-styles[_ngcontent-%COMP%]   .view-button-active[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#fff;font-size:18px}.udrf-settings-modal-styles[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]{align-self:center;line-height:8px;width:26px;height:26px;margin-right:10px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12);animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}.udrf-settings-modal-styles[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{font-size:18px}.udrf-settings-modal-styles[_ngcontent-%COMP%]   .value14[_ngcontent-%COMP%]{color:#4e4c4c;font-weight:400!important}.udrf-settings-modal-styles[_ngcontent-%COMP%]   .value14[_ngcontent-%COMP%], .udrf-settings-modal-styles[_ngcontent-%COMP%]   .value14RedBold[_ngcontent-%COMP%]{font-size:14px!important;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.udrf-settings-modal-styles[_ngcontent-%COMP%]   .value14RedBold[_ngcontent-%COMP%]{color:#cf0001;font-weight:500!important}.udrf-settings-modal-styles[_ngcontent-%COMP%]   .data-type-card[_ngcontent-%COMP%]{min-height:85px;transition:all .3s linear}.udrf-settings-modal-styles[_ngcontent-%COMP%]   .data-type-card-is-active[_ngcontent-%COMP%]{border-color:#cf0001;background-color:#fff2f2}.udrf-settings-modal-styles[_ngcontent-%COMP%]   .headingBold[_ngcontent-%COMP%]{color:#1a1a1a;font-weight:600;font-size:20px}.udrf-settings-modal-styles[_ngcontent-%COMP%]   .valueGrey14[_ngcontent-%COMP%]{font-size:14px}.udrf-settings-modal-styles[_ngcontent-%COMP%]   .valueGrey12Normal[_ngcontent-%COMP%], .udrf-settings-modal-styles[_ngcontent-%COMP%]   .valueGrey14[_ngcontent-%COMP%]{color:#4a4a4a;font-weight:400;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.udrf-settings-modal-styles[_ngcontent-%COMP%]   .valueGrey12Normal[_ngcontent-%COMP%]{font-size:12px}.udrf-settings-modal-styles[_ngcontent-%COMP%]   .cp[_ngcontent-%COMP%]{cursor:pointer!important}.udrf-settings-modal-styles[_ngcontent-%COMP%]   .name[_ngcontent-%COMP%]{font-size:14px;color:#cf0001;font-family:Roboto;font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.udrf-settings-modal-styles[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]{display:flex;line-height:26px;border-radius:50%;cursor:context-menu!important;width:26px;height:26px;background-color:#fbfbfb;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.udrf-settings-modal-styles[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#cf0001;font-size:18px;margin-top:4px!important;margin-left:3px!important}.udrf-settings-modal-styles[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.udrf-settings-modal-styles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:30px!important;height:30px!important;line-height:30px!important}.udrf-settings-modal-styles[_ngcontent-%COMP%]   .mat-button-toggle-checked[_ngcontent-%COMP%]{font-size:12px!important;background-color:#c92020!important;color:#fff}.udrf-settings-modal-styles[_ngcontent-%COMP%]   .example-list[_ngcontent-%COMP%]{width:900px;max-width:100%;border:1px solid #ccc;min-height:60px;display:flex;flex-direction:row;background:#fff;border-radius:4px;overflow:hidden;overflow-x:scroll}.udrf-settings-modal-styles[_ngcontent-%COMP%]   .example-box[_ngcontent-%COMP%]{padding:20px 10px;border-right:1px solid #ccc;color:#000;display:flex;flex-direction:row;align-items:center;justify-content:space-between;box-sizing:border-box;cursor:move;background:#fff;font-size:14px;flex-grow:1;flex-basis:0}.udrf-settings-modal-styles[_ngcontent-%COMP%]   .cdk-drag-preview[_ngcontent-%COMP%]{box-sizing:border-box;border-radius:4px;box-shadow:0 5px 5px -3px rgba(0,0,0,.2),0 8px 10px 1px rgba(0,0,0,.14),0 3px 14px 2px rgba(0,0,0,.12)}.udrf-settings-modal-styles[_ngcontent-%COMP%]   .cdk-drag-placeholder[_ngcontent-%COMP%]{opacity:0}.udrf-settings-modal-styles[_ngcontent-%COMP%]   .cdk-drag-animating[_ngcontent-%COMP%]{transition:transform .25s cubic-bezier(0,0,.2,1)}.udrf-settings-modal-styles[_ngcontent-%COMP%]   .example-box[_ngcontent-%COMP%]:last-child{border:none}.udrf-settings-modal-styles[_ngcontent-%COMP%]   .example-list.cdk-drop-list-dragging[_ngcontent-%COMP%]   .example-box[_ngcontent-%COMP%]:not(.cdk-drag-placeholder){transition:transform .25s cubic-bezier(0,0,.2,1)}.udrf-settings-modal-styles[_ngcontent-%COMP%]   .example-box-is-active[_ngcontent-%COMP%]{border-color:#cf0001;background-color:#fff2f2}.udrf-settings-modal-styles[_ngcontent-%COMP%]   .info-icon[_ngcontent-%COMP%]{font-size:16px;cursor:pointer;color:#7d838b;display:flex;justify-content:center;align-items:center}.udrf-settings-modal-styles[_ngcontent-%COMP%]   .mat-tooltip-above[_ngcontent-%COMP%]{transform:translateY(-100%)}"]}),e})()}}]);