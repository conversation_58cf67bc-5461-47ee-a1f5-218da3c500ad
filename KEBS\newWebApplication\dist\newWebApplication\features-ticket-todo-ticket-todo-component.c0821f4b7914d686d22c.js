(window.webpackJsonp=window.webpackJsonp||[]).push([[668,267,634,853,858],{"8SgF":function(e,t,o){"use strict";o.d(t,"a",(function(){return C}));var n=o("mrSG"),i=o("fXoL"),r=o("XNiG"),s=o("Kj3r"),a=o("1G5W"),d=o("3Pt+"),l=o("NJ67"),c=o("F97M"),m=o("XVR1"),p=o("kmnG"),h=o("ofXK"),u=o("qFsG"),g=o("/1cH"),f=o("NFeN"),b=o("FKr1");function v(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-label"),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](e.label)}}function y(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-option",7),i["\u0275\u0275elementStart"](1,"small"),i["\u0275\u0275text"](2),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,o=i["\u0275\u0275nextContext"]();i["\u0275\u0275property"]("hidden",!o.isAutocomplete)("value",e),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate2"]("",e.displayName," | ",e.mail,"")}}let C=(()=>{class e extends l.a{constructor(e,t){super(),this.graphApi=e,this._AppShareService=t,this.userSearchSubject=new r.b,this.isAutocomplete=!1,this.Formwidth="",this.showUserList=new i.EventEmitter,this.selectedUser=new i.EventEmitter,this.label="",this.blur=new i.EventEmitter,this.required=!1,this.fieldCtrl=new d.j,this.disabled=!1,this.readonly=!1,this.isGraphApi=0,this.optClicked=!1,this._onDestroy=new r.b}ngOnInit(){this.userSearchSubject.pipe(Object(s.a)(600)).subscribe(e=>Object(n.c)(this,void 0,void 0,(function*(){if(console.log(e),this.isGraphApi)this.graphApi.getUserSuggestions(e).then(t=>Object(n.c)(this,void 0,void 0,(function*(){if(t&&t.length>0)this.showUserList.emit(t);else{let t=yield this.getUserSuggestionsFromDB(e);this.showUserList.emit(t)}})),t=>Object(n.c)(this,void 0,void 0,(function*(){console.log(t);let o=yield this.getUserSuggestionsFromDB(e);this.showUserList.emit(o)})));else{let t=yield this.getUserSuggestionsFromDB(e);this.showUserList.emit(t)}}))),this.fieldCtrl.valueChanges.pipe(Object(a.a)(this._onDestroy)).subscribe(e=>{console.log(e),e?(this.value=e.id,this.onChange(e.id)):(this.value="",this.onChange(""))})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}ngAfterViewInit(){}searchUser(e){if(this.text=e.target.value,this.optClicked="Enter"==e.key,!this.text)return this.graphApi.userSuggestions=[];this.userSearchSubject.next(this.text)}resetSuggestion(){this.graphApi.userSuggestions=[]}checkAndClearInput(){this.optClicked||0!=this.readonly||this.fieldCtrl.setValue("")}selectedOption(e){this.optClicked=!0,this.selectedUser.emit(e.option.value),this.value=e.option.value,this.blur.emit()}displayUserName(e){return e?e.displayName:""}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){return Object(n.c)(this,void 0,void 0,(function*(){if(console.log("inisde writevalue"),console.log(e),e)if(this.isGraphApi)this.graphApi.getUserProfile(e).then(t=>Object(n.c)(this,void 0,void 0,(function*(){if(t)this.fieldCtrl.setValue(t);else{let t=yield this.getUserProfileFromDB(e);this.fieldCtrl.setValue(t)}})),t=>Object(n.c)(this,void 0,void 0,(function*(){let t=yield this.getUserProfileFromDB(e);this.fieldCtrl.setValue(t)})));else{let t=yield this.getUserProfileFromDB(e);console.log(t),this.fieldCtrl.setValue(t)}else this.fieldCtrl.setValue("")}))}getUserSuggestionsFromDB(e){return Object(n.c)(this,void 0,void 0,(function*(){let t=yield this._AppShareService.getUserSuggestionsFromDB(e);return this.graphApi.userSuggestions=t.value,t.value}))}getUserProfileFromDB(e){return Object(n.c)(this,void 0,void 0,(function*(){let t=yield this._AppShareService.getUserProfileFromDB(e);return console.log(t),t&&t.length>0?t[0]:""}))}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](c.a),i["\u0275\u0275directiveInject"](m.a))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-search-user"]],inputs:{isAutocomplete:"isAutocomplete",Formwidth:"Formwidth",label:"label",required:"required",disabled:"disabled",readonly:"readonly",isGraphApi:"isGraphApi"},outputs:{showUserList:"showUserList",selectedUser:"selectedUser",blur:"blur"},features:[i["\u0275\u0275ProvidersFeature"]([{provide:d.t,useExisting:Object(i.forwardRef)(()=>e),multi:!0}]),i["\u0275\u0275InheritDefinitionFeature"]],decls:9,vars:10,consts:[["appearance","outline","width","Formwidth"],[4,"ngIf"],["matInput","",3,"matAutocomplete","placeholder","required","formControl","disabled","readonly","keyup","focus","focusout"],["matSuffix","",2,"color","#66615B !important","font-size","21px !important"],[3,"hidden","displayWith","optionSelected"],["auto","matAutocomplete"],[3,"hidden","value",4,"ngFor","ngForOf"],[3,"hidden","value"]],template:function(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div"),i["\u0275\u0275elementStart"](1,"mat-form-field",0),i["\u0275\u0275template"](2,v,2,1,"mat-label",1),i["\u0275\u0275elementStart"](3,"input",2),i["\u0275\u0275listener"]("keyup",(function(e){return t.searchUser(e)}))("focus",(function(){return t.resetSuggestion()}))("focusout",(function(){return t.checkAndClearInput()})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"mat-icon",3),i["\u0275\u0275text"](5,"person_pin"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](6,"mat-autocomplete",4,5),i["\u0275\u0275listener"]("optionSelected",(function(e){return t.selectedOption(e)})),i["\u0275\u0275template"](8,y,3,4,"mat-option",6),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275reference"](7);i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf",t.label),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("matAutocomplete",e)("placeholder",t.label)("required",t.required)("formControl",t.fieldCtrl)("disabled",t.disabled)("readonly",t.readonly),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("hidden",!t.isAutocomplete)("displayWith",t.displayUserName),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngForOf",t.graphApi.userSuggestions)}},directives:[p.c,h.NgIf,u.b,g.d,d.e,d.F,d.v,d.k,f.a,p.i,g.b,h.NgForOf,p.g,b.p],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding-bottom:8px!important}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-underline{bottom:0}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-infix{padding:.65em 0!important;border-top:.54375em solid transparent!important;font-size:13px}.mat-form-field[_ngcontent-%COMP%]     .mat-select-arrow{margin:0 4px!important}"]}),e})()},NJ67:function(e,t,o){"use strict";o.d(t,"a",(function(){return n}));class n{constructor(){this.disabled=!1}onChange(e){}onTouched(e){}writeValue(e){this.value=e}registerOnChange(e){this.onChange=e}registerOnTouched(e){this.onTouched=e}setDisabledState(e){this.disabled=e}}},xKjX:function(e,t,o){"use strict";o.r(t),o.d(t,"TicketTodoComponent",(function(){return A})),o.d(t,"TicketTodoModule",(function(){return V}));var n=o("mrSG"),i=o("fXoL"),r=o("3Pt+"),s=o("1G5W"),a=o("XNiG"),d=o("wd/R"),l=o("ofXK"),c=o("bTqV"),m=o("NFeN"),p=o("Qu3c"),h=o("kmnG"),u=o("qFsG"),g=o("bSwM"),f=o("Xi0T"),b=o("iadO"),v=o("FKr1"),y=o("1yaQ"),C=o("Xa2L"),x=o("yu80"),S=o("BVzC"),T=o("mS9j"),_=o("8SgF");function I(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div"),i["\u0275\u0275element"](1,"div",4),i["\u0275\u0275elementStart"](2,"div",5),i["\u0275\u0275element"](3,"mat-spinner",6),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](4,"div",4),i["\u0275\u0275elementEnd"]())}function k(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",24),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const t=i["\u0275\u0275nextContext"]().index;return i["\u0275\u0275nextContext"](2).deleteTodoItem(t)})),i["\u0275\u0275elementStart"](1,"mat-icon",25),i["\u0275\u0275text"](2,"close"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}}const w=function(e){return{"is-completed":e}};function E(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementContainerStart"](0),i["\u0275\u0275elementStart"](1,"div",14),i["\u0275\u0275elementStart"](2,"div",15),i["\u0275\u0275elementStart"](3,"mat-checkbox",16),i["\u0275\u0275listener"]("ngModelChange",(function(e){return t.$implicit.completed=e}))("change",(function(){i["\u0275\u0275restoreView"](e);const o=t.index,n=t.$implicit;return i["\u0275\u0275nextContext"](2).changeTodoStatus(o,n.completed)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"div",17),i["\u0275\u0275text"](5),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](6,"div",18),i["\u0275\u0275element"](7,"app-user-profile",19),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](8,"div",20),i["\u0275\u0275elementStart"](9,"mat-icon",21),i["\u0275\u0275text"](10,"notifications"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](11,"small"),i["\u0275\u0275text"](12),i["\u0275\u0275pipe"](13,"date"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](14,"div",22),i["\u0275\u0275template"](15,k,3,0,"button",23),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=t.$implicit,o=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngModel",e.completed)("matTooltip",e.completed?"Mark as open":"Mark as completed"),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction1"](14,w,e.completed)),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" ",e.todo_name," "),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction1"](16,w,e.completed)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("type","name")("oid",e.assigned_to),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngClass",i["\u0275\u0275pureFunction1"](18,w,e.completed)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngClass",e.reminder_date?"reminder":""),i["\u0275\u0275advance"](3),i["\u0275\u0275textInterpolate"](e.reminder_date?i["\u0275\u0275pipeBind2"](13,11,e.reminder_date,"dd-MMM-yy hh:mm a"):"Set Reminder Date"),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngIf",o.isTicketEditable&&o.currentUser.oid==(null==e?null:e.created_by))}}function O(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementContainerStart"](0,26),i["\u0275\u0275elementStart"](1,"div",27),i["\u0275\u0275elementStart"](2,"div",28),i["\u0275\u0275elementStart"](3,"mat-form-field",29),i["\u0275\u0275elementStart"](4,"mat-label"),i["\u0275\u0275text"](5,"To do*"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](6,"input",30),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](7,"div",31),i["\u0275\u0275element"](8,"app-search-user",32),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](9,"div",31),i["\u0275\u0275elementStart"](10,"mat-form-field",29),i["\u0275\u0275elementStart"](11,"mat-label"),i["\u0275\u0275text"](12,"Reminder date*"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](13,"input",33),i["\u0275\u0275element"](14,"mat-datepicker-toggle",34),i["\u0275\u0275element"](15,"mat-datepicker",null,35),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](17,"div",36),i["\u0275\u0275elementStart"](18,"button",37),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const o=t.index;return i["\u0275\u0275nextContext"](2).saveTodoItem(o)})),i["\u0275\u0275elementStart"](19,"mat-icon",38),i["\u0275\u0275text"](20,"done"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](21,"button",39),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const o=t.index;return i["\u0275\u0275nextContext"](2).removeTodoItem(o)})),i["\u0275\u0275elementStart"](22,"mat-icon",40),i["\u0275\u0275text"](23,"highlight_off"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=t.index,o=i["\u0275\u0275reference"](16),n=i["\u0275\u0275nextContext"](2);i["\u0275\u0275property"]("formGroupName",e),i["\u0275\u0275advance"](8),i["\u0275\u0275property"]("isAutocomplete",!0),i["\u0275\u0275advance"](5),i["\u0275\u0275property"]("min",n.ticketItem.created_on)("matDatepicker",o),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("for",o),i["\u0275\u0275advance"](4),i["\u0275\u0275property"]("disabled",n.isTodoBeingSaved),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("disabled",n.isTodoBeingSaved)}}function M(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",27),i["\u0275\u0275elementStart"](1,"div",41),i["\u0275\u0275elementStart"](2,"button",42),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"](2).addTodoItem()})),i["\u0275\u0275elementStart"](3,"mat-icon",25),i["\u0275\u0275text"](4,"add"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"span",43),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"](2).addTodoItem()})),i["\u0275\u0275text"](6,"Add Item"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}}function D(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",7),i["\u0275\u0275elementStart"](1,"div",8),i["\u0275\u0275template"](2,E,16,20,"ng-container",9),i["\u0275\u0275elementStart"](3,"form",10),i["\u0275\u0275elementContainerStart"](4,11),i["\u0275\u0275template"](5,O,24,7,"ng-container",12),i["\u0275\u0275elementContainerEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](6,M,7,0,"div",13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngForOf",e.toDoList),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("formGroup",e.todoForm),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngForOf",e.todoFormData.todoValues.controls),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",e.isTicketEditable)}}function F(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",50),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"](2).createNewTodoList()})),i["\u0275\u0275text"](1," Create To-Do list "),i["\u0275\u0275elementEnd"]()}}function P(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",44),i["\u0275\u0275elementStart"](1,"div",45),i["\u0275\u0275elementStart"](2,"span",46),i["\u0275\u0275text"](3,"No To-Do list found ! "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"div",47),i["\u0275\u0275element"](5,"img",48),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](6,F,2,0,"button",49),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](6),i["\u0275\u0275property"]("ngIf",e.isTicketEditable)}}let A=(()=>{class e{constructor(e,t,o){this.fb=e,this._ticket=t,this._ErrorService=o,this._onDestroy=new a.b,this.updateTodoId=new i.EventEmitter,this.toDoList=[],this.addItemVisible=!1,this.isTodoLoading=!1,this.isTodoBeingSaved=!1,this.isTicketEditable=!0,this.currentUser=this._ticket.currentUser,this.initForm=()=>{this.todoForm=this.fb.group({todoValues:this.fb.array([this.getTodoValues()])})},this.getTodoValues=()=>this.fb.group({todoTitle:[null,r.H.required],assignedTo:[this.currentUser.oid,r.H.required],reminderDate:[null,r.H.required]}),this.addTodoItem=()=>{this.todoFormData.todoValues.push(this.getTodoValues())},this.removeTodoItem=e=>{this.todoFormData.todoValues.removeAt(e)},this.saveTodoItem=e=>Object(n.c)(this,void 0,void 0,(function*(){let t=this.todoFormData.todoValues.at(e).value,o=null==t.todoTitle?"Kindly enter To do Title":null==t.assignedTo||0==t.assignedTo.length?"Kindly enter Assigned to ":null==t.reminderDate?"Kindly enter Reminder Date":0;0!=o?this._ticket.showMessage(o):"VALID"==this.todoForm.status&&(this.todoId?this.insertTodo(this.getTodoItem(e),e):this.createTodo(this.getTodoItem(e),e))})),this.getTodoItem=e=>{let t=this.todoFormData.todoValues.at(e).value;return{todo_name:t.todoTitle,completed:!1,created_by:this.currentUser.oid,created_date:d(),assigned_to:t.assignedTo,reminder_date:t.reminderDate,is_visible_all:!0,is_active:!0}},this.createTodo=(e,t)=>{this.isTodoBeingSaved=!0,this._ticket.createTodo(this.ticketId,e).pipe(Object(s.a)(this._onDestroy)).subscribe(e=>{this.isTodoBeingSaved=!1,"N"==e.error&&e.data?(this.todoId=e.data,this._ticket.showMessage("To do added Successfully !"),this.getTodoDetails(),this.removeTodoItem(t),this.updateTodoId.emit(this.todoId)):this._ticket.showMessage("Error adding todo !")},e=>{this.isTodoBeingSaved=!1,this._ErrorService.userErrorAlert(e.error.errorCode,"Some Error Happened in completing the Activity",e.error.errMessage)})},this.insertTodo=(e,t)=>{this.isTodoBeingSaved=!0,this._ticket.insertTodo(this.ticketId,this.todoId,e).pipe(Object(s.a)(this._onDestroy)).subscribe(e=>{this.isTodoBeingSaved=!1,"N"==e.error?(this._ticket.showMessage("To do added Successfully !"),this.getTodoDetails(),this.removeTodoItem(t)):this._ticket.showMessage("Error adding todo !")},e=>{this.isTodoBeingSaved=!1,this._ErrorService.userErrorAlert(e.error.errorCode,"Some Error Happened in completing the Activity",e.error.errMessage)})},this.deleteTodoItem=e=>{this.toDoList[e].is_active=!1,this.editTodo(e,"deletion"),this.toDoList.splice(e,1)},this.changeTodoStatus=(e,t)=>{this.toDoList[e].completed=t,this.editTodo(e,"status")},this.editTodo=(e,t)=>{this.toDoList[e].modified_by=this.currentUser.oid,this.toDoList[e].modified_date=d(),this._ticket.editTodo(this.ticketId,this.todoId,this.toDoList[e],t).pipe(Object(s.a)(this._onDestroy)).subscribe(e=>{this._ticket.showMessage("N"==e.error?"To do updated Successfully !":"Updation Failed !")},e=>{this._ErrorService.userErrorAlert(e.error.errorCode,"Some Error Happened in completing the Activity",e.error.errMessage)})},this.createNewTodoList=()=>{this.addItemVisible=!0,this.addTodoItem()},this.initForm()}ngOnInit(){this.isTicketEditable=this._ticket.checkIfTicketIsEditable(this.ticketProperties,this.ticketItem),this.removeTodoItem(0),this.getTodoDetails(),this.resolveSubscriptions()}ngOnChanges(){this.isTicketEditable=this._ticket.checkIfTicketIsEditable(this.ticketProperties,this.ticketItem)}resolveSubscriptions(){this.createResponseSubscription||(this.createResponseSubscription=this._ticket.getActivityObservable.subscribe(e=>{e&&"Create To do"==e.type&&(this.addItemVisible=!0,this.addTodoItem())},e=>{this._ErrorService.userErrorAlert(e.error.errorCode,"Some Error Happened in completing the Activity",e.error.errMessage)}))}getTodoDetails(){this.todoId&&(this.isTodoLoading=!0,this._ticket.getTodoDetails(this.todoId).pipe(Object(s.a)(this._onDestroy)).subscribe(e=>{this.isTodoLoading=!1,"N"==e.error&&e.data&&(this.toDoList=e.data.todo_list),this.toDoList.length>0&&(this.addItemVisible=!0)},e=>{this.isTodoLoading=!1,this._ErrorService.userErrorAlert(e.error.errorCode,"Some Error Happened in completing the Activity",e.error.errMessage)}))}get todoFormData(){return this.todoForm.controls}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete(),this.createResponseSubscription&&this.createResponseSubscription.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](r.i),i["\u0275\u0275directiveInject"](x.a),i["\u0275\u0275directiveInject"](S.a))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-ticket-todo"]],inputs:{todoId:"todoId",ticketId:"ticketId",ticketItem:"ticketItem",ticketProperties:"ticketProperties"},outputs:{updateTodoId:"updateTodoId"},features:[i["\u0275\u0275ProvidersFeature"]([{provide:v.c,useClass:y.c,deps:[v.f,y.a]},{provide:v.e,useValue:{parse:{dateInput:"DD-MM-YYYY"},display:{dateInput:"DD-MM-YY",monthYearLabel:"MMM YYYY"}}}]),i["\u0275\u0275NgOnChangesFeature"]],decls:4,vars:3,consts:[[1,"container-fluid","pl-0","to-do-styles"],[4,"ngIf"],["class","card",4,"ngIf"],["style","text-align: center;padding-top: 3rem;",4,"ngIf"],[1,"flex-grow-1"],[1,"row","pt-4","justify-content-center"],["diameter","30"],[1,"card"],[1,"card-body","p-2"],[4,"ngFor","ngForOf"],[3,"formGroup"],["formArrayName","todoValues"],[3,"formGroupName",4,"ngFor","ngForOf"],["class","row pt-2",4,"ngIf"],[1,"row","pt-2","pb-2",2,"border-bottom","solid 1px #cacaca"],[1,"col-1","d-flex","my-auto","pr-0"],[3,"ngModel","matTooltip","ngModelChange","change"],[1,"col-6","normalFont","pt-2","pl-0",3,"ngClass"],[1,"col-2","normalFont","pl-0","pr-0","d-flex","my-auto",3,"ngClass"],[3,"type","oid"],[1,"col-2","pl-0","pr-0","d-flex","my-auto",3,"ngClass"],[1,"close-Icon",3,"ngClass"],[1,"col-1","pl-0"],["style","float: right;","mat-icon-button","","class","ml-auto close-button","matTooltip","Delete Todo",3,"click",4,"ngIf"],["mat-icon-button","","matTooltip","Delete Todo",1,"ml-auto","close-button",2,"float","right",3,"click"],[1,"close-Icon"],[3,"formGroupName"],[1,"row","pt-2"],[1,"col-4"],["appearance","outline",1,"form-field-class"],["matInput","","formControlName","todoTitle","placeholder","Ex: Finish FS"],[1,"col-3"],["label","Assigned to*","formControlName","assignedTo",2,"width","100% !important",3,"isAutocomplete"],["matInput","","formControlName","reminderDate",3,"min","matDatepicker"],["matSuffix","",3,"for"],["reminderDatepicker",""],[1,"col-2","mt-2"],["mat-icon-button","","matTooltip","Save todo",1,"approve-btn","mr-3",3,"disabled","click"],[2,"color","white !important","font-size","18px !important"],["mat-icon-button","","matTooltip","Remove todo",1,"reject-btn",3,"disabled","click"],[2,"color","white !important","font-size","21px !important","margin-bottom","1px"],[1,"col-12","pl-0"],["mat-icon-button","","matTooltip","Add To do",1,"ml-auto","close-button","mt-1",3,"click"],[1,"normalFont","pl-2",2,"cursor","pointer",3,"click"],[2,"text-align","center","padding-top","3rem"],[1,"d-flex","pb-2","justify-content-center","align-items-center","slide-in-top"],[2,"font-size","16px","font-weight","normal"],[1,"d-flex","justify-content-center","align-items-center","slide-from-down","pt-2","pb-2"],["src","assets/images/to_do.png","height","170","width","200",1,"mt-2","mb-2"],["mat-raised-button","","class","mt-2 btn-active slide-from-down",3,"click",4,"ngIf"],["mat-raised-button","",1,"mt-2","btn-active","slide-from-down",3,"click"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275template"](1,I,5,0,"div",1),i["\u0275\u0275template"](2,D,7,4,"div",2),i["\u0275\u0275template"](3,P,7,1,"div",3),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.isTodoLoading),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",!t.isTodoLoading&&t.addItemVisible),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",0==t.toDoList.length&&!t.addItemVisible&&!t.isTodoLoading))},directives:[l.NgIf,C.c,l.NgForOf,r.J,r.w,r.n,r.h,g.a,r.v,r.y,p.a,l.NgClass,T.a,m.a,c.a,r.o,h.c,h.g,u.b,r.e,r.l,_.a,b.g,b.i,h.i,b.f],pipes:[l.DatePipe],styles:[".to-do-styles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%]{background-color:#cf0001;border-color:#cf0001;color:#fff;font-weight:400;font-size:12px!important;min-width:10rem;line-height:28px;border-radius:4px}.to-do-styles[_ngcontent-%COMP%]   .normalFont[_ngcontent-%COMP%]{color:#181818;font-size:14px;font-weight:400;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.to-do-styles[_ngcontent-%COMP%]   .approve-btn[_ngcontent-%COMP%]{background-color:#64d464;width:27px;height:27px;line-height:25px}.to-do-styles[_ngcontent-%COMP%]   .approve-btn[_ngcontent-%COMP%], .to-do-styles[_ngcontent-%COMP%]   .reject-btn[_ngcontent-%COMP%]{font-size:18px;color:#fff;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 3px 0 rgba(0,0,0,.14),0 2px 14px 0 rgba(0,0,0,.12)}.to-do-styles[_ngcontent-%COMP%]   .reject-btn[_ngcontent-%COMP%]{background-color:#fc3939;width:28px;height:28px;line-height:27px}.to-do-styles[_ngcontent-%COMP%]   .is-completed[_ngcontent-%COMP%]{text-decoration:line-through;color:#ccc9c9}.to-do-styles[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.to-do-styles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}.to-do-styles[_ngcontent-%COMP%]   .is-selected[_ngcontent-%COMP%]{color:#ccc9c9}.to-do-styles[_ngcontent-%COMP%]   .reminder[_ngcontent-%COMP%]{color:#cf0001!important}.to-do-styles[_ngcontent-%COMP%]   .mat-checkbox-indeterminate.mat-accent[_ngcontent-%COMP%]   .mat-checkbox-background[_ngcontent-%COMP%], .to-do-styles[_ngcontent-%COMP%]     .mat-checkbox-checked.mat-accent .mat-checkbox-background{background-color:#ccc9c9}.to-do-styles[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-flex{position:relative;height:2.5rem;width:100%}.to-do-styles[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:100%;font-size:13px}.to-do-styles[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding-bottom:8px!important}.to-do-styles[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-form-field-underline{bottom:0}.to-do-styles[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-form-field-infix{padding:.65em 0!important;border-top:.54375em solid transparent!important;font-size:13px}.to-do-styles[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]     .mat-select-arrow{margin:0 4px!important}.to-do-styles[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}.to-do-styles[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}"]}),e})(),V=(()=>{class e{}return e.\u0275mod=i["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=i["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[l.CommonModule,m.b,c.b,p.b,r.p,r.E,h.e,u.c,g.b,f.a,b.h,v.n,C.b]]}),e})()}}]);