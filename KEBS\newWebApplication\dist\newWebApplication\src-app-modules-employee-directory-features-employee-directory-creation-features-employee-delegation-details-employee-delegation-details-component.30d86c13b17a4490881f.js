(window.webpackJsonp=window.webpackJsonp||[]).push([[949,634,858],{"045J":function(e,t,a){"use strict";a.r(t),a.d(t,"EmployeeDelegationDetailsComponent",(function(){return M})),a.d(t,"EmployeeDelegationDetailsModule",(function(){return k}));var i=a("mrSG"),n=a("fXoL"),o=a("3Pt+"),l=a("0IaG"),r=a("FKr1"),s=a("1yaQ"),d=a("33Jv"),c=a("wd/R"),u=a("ofXK"),m=a("kmnG"),p=a("qFsG"),h=a("bTqV"),g=a("iadO"),f=a("bSwM"),v=a("Xa2L"),b=a("NFeN"),D=a("Xi0T"),y=a("jAlA"),C=a("1A3m"),x=a("qFYv");function S(e,t){1&e&&(n["\u0275\u0275elementContainerStart"](0),n["\u0275\u0275elementStart"](1,"div",2),n["\u0275\u0275element"](2,"mat-spinner",3),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementContainerEnd"]())}function O(e,t){if(1&e){const e=n["\u0275\u0275getCurrentView"]();n["\u0275\u0275elementStart"](0,"div",15),n["\u0275\u0275listener"]("click",(function(){return n["\u0275\u0275restoreView"](e),n["\u0275\u0275nextContext"](2).cancel()})),n["\u0275\u0275elementStart"](1,"mat-icon"),n["\u0275\u0275text"](2," close "),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"]()}}const E=function(){return["prefix_associate_id","employee_name"]};function I(e,t){if(1&e){const e=n["\u0275\u0275getCurrentView"]();n["\u0275\u0275elementContainerStart"](0),n["\u0275\u0275elementStart"](1,"div",16),n["\u0275\u0275elementStart"](2,"div",8),n["\u0275\u0275elementStart"](3,"div",9),n["\u0275\u0275text"](4," Start Date "),n["\u0275\u0275elementStart"](5,"span",17),n["\u0275\u0275text"](6," \xa0*"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](7,"div",7),n["\u0275\u0275elementStart"](8,"mat-form-field",18),n["\u0275\u0275element"](9,"input",19),n["\u0275\u0275element"](10,"mat-datepicker-toggle",20),n["\u0275\u0275element"](11,"mat-datepicker",null,21),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](13,"div",8),n["\u0275\u0275elementStart"](14,"div",9),n["\u0275\u0275text"](15," End Date "),n["\u0275\u0275elementStart"](16,"span",17),n["\u0275\u0275text"](17," \xa0*"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](18,"div",7),n["\u0275\u0275elementStart"](19,"mat-form-field",18),n["\u0275\u0275element"](20,"input",22),n["\u0275\u0275element"](21,"mat-datepicker-toggle",20),n["\u0275\u0275element"](22,"mat-datepicker",null,23),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](24,"div",24),n["\u0275\u0275elementStart"](25,"div",8),n["\u0275\u0275elementStart"](26,"div",9),n["\u0275\u0275text"](27," Employee Name "),n["\u0275\u0275elementStart"](28,"span",17),n["\u0275\u0275text"](29," \xa0*"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](30,"div",7),n["\u0275\u0275elementStart"](31,"app-input-search-huge-input",25),n["\u0275\u0275listener"]("input",(function(){return n["\u0275\u0275restoreView"](e),n["\u0275\u0275nextContext"](2).handleAssociateIdChange()})),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=n["\u0275\u0275reference"](12),t=n["\u0275\u0275reference"](23),a=n["\u0275\u0275nextContext"](2);let i=null,o=null;n["\u0275\u0275advance"](9),n["\u0275\u0275property"]("matDatepicker",e)("min",null!=(i=a.delegationDetailsFormGroup.get("isDelegationActive"))&&i.value?a.todayDate():null)("max",null==(o=a.delegationDetailsFormGroup.get("endDate"))?null:o.value),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("for",e),n["\u0275\u0275advance"](10),n["\u0275\u0275property"]("matDatepicker",t)("min",a.getMinEndDate()),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("for",t),n["\u0275\u0275advance"](10),n["\u0275\u0275property"]("label","Search By Employee ID / Name")("optionLabel",n["\u0275\u0275pureFunction0"](10,E))("apiUri","/api/employee360/masterData/getEmployeeListForDelegation")}}function F(e,t){if(1&e){const e=n["\u0275\u0275getCurrentView"]();n["\u0275\u0275elementContainerStart"](0),n["\u0275\u0275template"](1,O,3,0,"div",4),n["\u0275\u0275elementStart"](2,"div",5),n["\u0275\u0275text"](3,"Employee Delegation Details"),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](4,"form",6),n["\u0275\u0275listener"]("keydown.enter",(function(e){return e.preventDefault()})),n["\u0275\u0275elementStart"](5,"div",7),n["\u0275\u0275elementStart"](6,"div",8),n["\u0275\u0275elementStart"](7,"div",9),n["\u0275\u0275text"](8," Is Delegation Active "),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](9,"div",7),n["\u0275\u0275elementStart"](10,"mat-checkbox",10),n["\u0275\u0275text"](11," Active "),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275template"](12,I,32,11,"ng-container",1),n["\u0275\u0275elementStart"](13,"div",11),n["\u0275\u0275elementStart"](14,"div",12),n["\u0275\u0275elementStart"](15,"button",13),n["\u0275\u0275listener"]("click",(function(){return n["\u0275\u0275restoreView"](e),n["\u0275\u0275nextContext"]().cancel()})),n["\u0275\u0275text"](16),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementStart"](17,"button",14),n["\u0275\u0275listener"]("click",(function(){return n["\u0275\u0275restoreView"](e),n["\u0275\u0275nextContext"]().saveDetails()})),n["\u0275\u0275text"](18),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementEnd"](),n["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=n["\u0275\u0275nextContext"]();let t=null;n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf",e.isFromModal),n["\u0275\u0275advance"](3),n["\u0275\u0275property"]("formGroup",e.delegationDetailsFormGroup),n["\u0275\u0275advance"](8),n["\u0275\u0275property"]("ngIf",e.delegationDetailsFormGroup.get("isDelegationActive").value),n["\u0275\u0275advance"](4),n["\u0275\u0275textInterpolate1"](" ",e.isFromModal?"Cancel":"Skip"," "),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("disabled",e.loaderObject.isFormSubmitLoading||(null==(t=e.delegationDetailsFormGroup.get("isDelegationActive"))?null:t.value)&&!e.delegationDetailsFormGroup.valid),n["\u0275\u0275advance"](1),n["\u0275\u0275textInterpolate1"](" ",e.isFromModal?"Update":"Save & Next >"," ")}}let M=(()=>{class e{constructor(e,t,a,i){this._edService=e,this._toaster=t,this.fb=a,this.injector=i,this.delegationDetailsRes=new n.EventEmitter,this.delegationDetailsFormGroup=this.fb.group({startDate:["",o.H.required],endDate:["",o.H.required],isDelegationActive:[!1],employeeName:["",o.H.required],recordId:[""],associateId:[""]}),this.employeeList=[],this.delegationDetailsPayload={},this.isFromModal=!1,this.subs=new d.a,this.loaderObject={isComponentLoading:!1,isFormSubmitLoading:!1},this.dialogRef=null,this.dialogRef=this.injector.get(l.h,null),this.dialogData=this.injector.get(l.a,null)}ngOnInit(){var e,t,a,n,l,r,s,d,c;return Object(i.c)(this,void 0,void 0,(function*(){this.loaderObject.isComponentLoading=!0,this.associateId=(null===(t=null===(e=this.dialogData)||void 0===e?void 0:e.modalParams)||void 0===t?void 0:t.associateId)?null===(n=null===(a=this.dialogData)||void 0===a?void 0:a.modalParams)||void 0===n?void 0:n.associateId:this.associateId,this.isFromModal=!!(null===(r=null===(l=this.dialogData)||void 0===l?void 0:l.modalParams)||void 0===r?void 0:r.isFromModal)&&(null===(d=null===(s=this.dialogData)||void 0===s?void 0:s.modalParams)||void 0===d?void 0:d.isFromModal),yield this.bindSavedResponse(),this.employeeDoj=yield this.getDateOfJoining(this.associateId),this.createInitValue();const i=null===(c=this.delegationDetailsFormGroup.get("isDelegationActive"))||void 0===c?void 0:c.value,u=this.delegationDetailsFormGroup.get("employeeName"),m=this.delegationDetailsFormGroup.get("startDate"),p=this.delegationDetailsFormGroup.get("endDate");i?(null==u||u.enable(),null==u||u.setValidators([o.H.required]),null==m||m.setValidators([o.H.required,this.dateValidator.bind(this)]),null==p||p.setValidators([o.H.required,this.dateValidator.bind(this)])):(null==u||u.disable(),null==u||u.clearValidators(),null==m||m.clearValidators(),null==p||p.clearValidators()),null==u||u.updateValueAndValidity(),null==m||m.updateValueAndValidity(),null==p||p.updateValueAndValidity(),this.valueChangeListener(),this.loaderObject.isComponentLoading=!1}))}getDateOfJoining(e){return new Promise((t,a)=>{this.subs.sink=this._edService.getDateOfJoining(e).subscribe(e=>{e.err?a(e):t(e.doj)},e=>{console.log(e),a(e)})})}bindSavedResponse(){return new Promise((e,t)=>{this.subs.sink=this._edService.getEmployeeDelegationDetails(this.associateId).subscribe(t=>{if(!t.err&&t.data&&t.data.length>0){let e=Array.isArray(t.data)?t.data[0]:t.data;if(e){let t="";e.employee_name&&e.delegated_to&&(t=JSON.stringify({associate_id:e.delegated_to,employee_name:e.employee_name,prefix:null,prefix_active:0,prefix_associate_id:e.delegated_to})),this.delegationDetailsFormGroup.patchValue({startDate:e.start_date?c(e.start_date):"",endDate:e.end_date?c(e.end_date):"",isDelegationActive:1===e.active,employeeName:t,recordId:e.record_id||""},{emitEvent:!1})}}e(!0)},e=>{console.log(e),t(e)})})}createInitValue(){this.delegationDetailsInitValue=JSON.parse(JSON.stringify(this.delegationDetailsFormGroup.value))}valueChangeListener(){var e,t,a;this.delegationDetailsFormGroup.valueChanges.subscribe(e=>{this.handleDelegationDetailsChange()}),null===(e=this.delegationDetailsFormGroup.get("isDelegationActive"))||void 0===e||e.valueChanges.subscribe(e=>{const t=this.delegationDetailsFormGroup.get("employeeName"),a=this.delegationDetailsFormGroup.get("startDate"),i=this.delegationDetailsFormGroup.get("endDate");e?(null==t||t.enable(),null==t||t.setValidators([o.H.required]),null==a||a.setValidators([o.H.required,this.dateValidator.bind(this)]),null==i||i.setValidators([o.H.required,this.dateValidator.bind(this)])):(null==t||t.disable(),null==t||t.clearValidators(),null==t||t.setValue(""),null==a||a.clearValidators(),null==i||i.clearValidators()),null==t||t.updateValueAndValidity(),null==a||a.updateValueAndValidity(),null==i||i.updateValueAndValidity()}),null===(t=this.delegationDetailsFormGroup.get("startDate"))||void 0===t||t.valueChanges.subscribe(()=>{setTimeout(()=>{var e;null===(e=this.delegationDetailsFormGroup.get("endDate"))||void 0===e||e.updateValueAndValidity()},100)}),null===(a=this.delegationDetailsFormGroup.get("endDate"))||void 0===a||a.valueChanges.subscribe(()=>{setTimeout(()=>{var e;null===(e=this.delegationDetailsFormGroup.get("startDate"))||void 0===e||e.updateValueAndValidity()},100)})}handleDelegationDetailsChange(){let e=this.delegationDetailsFormGroup.value,t=Object.keys(e);for(let a of t)this.delegationDetailsPayload[a]={value:e[a],isChanged:this.checkIfChanged(e,this.delegationDetailsInitValue,a)};console.log(this.delegationDetailsPayload)}checkIfChanged(e,t,a){return"startDate"===a||"endDate"===a?(e[a]?c(e[a]).format("YYYY-MM-DD"):"")!==(t[a]?c(t[a]).format("YYYY-MM-DD"):""):e[a]!==t[a]}todayDate(){return c().startOf("day")}getMinEndDate(){var e,t;const a=null===(e=this.delegationDetailsFormGroup.get("isDelegationActive"))||void 0===e?void 0:e.value,i=null===(t=this.delegationDetailsFormGroup.get("startDate"))||void 0===t?void 0:t.value,n=c().startOf("day");if(!a)return i?c(i).startOf("day"):null;if(i){const e=c(i).startOf("day");return e.isSameOrAfter(n)?e:n}return n}saveDetails(){var e;return Object(i.c)(this,void 0,void 0,(function*(){if(null===(e=this.delegationDetailsFormGroup.get("isDelegationActive"))||void 0===e?void 0:e.value)if(console.log("Form valid:",this.delegationDetailsFormGroup.valid),console.log("Form errors:",this.getFormValidationErrors()),this.delegationDetailsFormGroup.valid){this.loaderObject.isFormSubmitLoading=!0;let e=this.prepareSavePayload();try{yield this.saveDelegationDetails(e),this._toaster.showSuccess("Success","Delegation details saved successfully!",2e3),this.isFromModal?this.closeDialog("Updated"):this.delegationDetailsRes.emit("Updated")}catch(t){this._toaster.showError("Error","Failed to save delegation details!",2e3)}this.loaderObject.isFormSubmitLoading=!1}else this._toaster.showError("Error","Please fill all required fields!",2e3);else{this.loaderObject.isFormSubmitLoading=!0;let e=this.prepareSavePayload();try{yield this.saveDelegationDetails(e),this._toaster.showSuccess("Success","Delegation details saved successfully!",2e3),this.isFromModal?this.closeDialog("Updated"):this.delegationDetailsRes.emit("Updated")}catch(t){this._toaster.showError("Error","Failed to save delegation details!",2e3)}this.loaderObject.isFormSubmitLoading=!1}}))}getFormValidationErrors(){let e={};return Object.keys(this.delegationDetailsFormGroup.controls).forEach(t=>{var a;const i=null===(a=this.delegationDetailsFormGroup.get(t))||void 0===a?void 0:a.errors;i&&(e[t]=i)}),e}dateValidator(e){var t,a,i,n;const o=null===(a=null===(t=this.delegationDetailsFormGroup)||void 0===t?void 0:t.get("isDelegationActive"))||void 0===a?void 0:a.value,l=null===(n=null===(i=this.delegationDetailsFormGroup)||void 0===i?void 0:i.get("recordId"))||void 0===n?void 0:n.value;if(!o)return null;if(!e.value)return{required:!0};const r=c(e.value).startOf("day");if(l){const t=c.utc().subtract(1,"day").startOf("day");return c.utc(e.value).startOf("day").isBefore(t)?{pastDate:!0}:null}const s=c().startOf("day");return r.isBefore(s)?{pastDate:!0}:null}prepareSavePayload(){let e=this.delegationDetailsFormGroup.value;return e.isDelegationActive?{associateId:this.associateId,recordId:e.recordId,startDate:e.startDate?c(e.startDate).format("YYYY-MM-DD"):"",endDate:e.endDate?c(e.endDate).format("YYYY-MM-DD"):"",active:e.isDelegationActive?1:0,employeeName:e.employeeName||""}:{associateId:this.associateId,recordId:e.recordId,startDate:"",endDate:"",active:0,employeeName:""}}saveDelegationDetails(e){return new Promise((t,a)=>{this.subs.sink=this._edService.saveDelegationDetailsCP(e).subscribe(e=>{e.err?a(e):t(e)},e=>{a(e)})})}handleAssociateIdChange(){}cancel(){this.isFromModal&&this.closeDialog("Close")}closeDialog(e){this.dialogRef&&this.dialogRef.close(e)}ngOnDestroy(){this.subs.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(n["\u0275\u0275directiveInject"](y.a),n["\u0275\u0275directiveInject"](C.a),n["\u0275\u0275directiveInject"](o.i),n["\u0275\u0275directiveInject"](n.Injector))},e.\u0275cmp=n["\u0275\u0275defineComponent"]({type:e,selectors:[["app-employee-delegation-details"]],inputs:{associateId:"associateId"},outputs:{delegationDetailsRes:"delegationDetailsRes"},features:[n["\u0275\u0275ProvidersFeature"]([{provide:r.c,useClass:s.c,deps:[r.f,s.a]},{provide:r.e,useValue:{parse:{dateInput:"DD-MMM-YYYY"},display:{dateInput:"DD-MMM-YYYY",monthYearLabel:"MMM YYYY"}}}])],decls:3,vars:2,consts:[[1,"container-fluid","delegation-details"],[4,"ngIf"],[1,"d-flex","justify-content-center","mt-3"],["matTooltip","Please wait...","diameter","30"],["class","d-flex flex-row-reverse","style","cursor: pointer",3,"click",4,"ngIf"],[1,"row","section-header","mb-2"],[1,"slide-from-down",3,"formGroup","keydown.enter"],[1,"row"],[1,"col-3","px-0","mr-4"],[1,"row","field-title"],["formControlName","isDelegationActive",1,"delegation-checkbox"],[1,"row","mt-4"],[1,"d-flex","flex-row"],["type","button","mat-stroked-button","",1,"cancel-btn",3,"click"],["type","button","mat-raised-button","",1,"ml-3","create-employee-btn",3,"disabled","click"],[1,"d-flex","flex-row-reverse",2,"cursor","pointer",3,"click"],[1,"row","mt-3"],[1,"required-star"],["appearance","outline",2,"width","100%"],["matInput","","required","","formControlName","startDate","placeholder","DD-MMM-YYYY","readonly","",3,"matDatepicker","min","max"],["matSuffix","",3,"for"],["startDatePicker",""],["matInput","","required","","formControlName","endDate","placeholder","DD-MMM-YYYY","readonly","",3,"matDatepicker","min"],["endDatePicker",""],[1,"row","pt-3"],["formControlName","employeeName",2,"width","100%",3,"label","optionLabel","apiUri","input"]],template:function(e,t){1&e&&(n["\u0275\u0275elementStart"](0,"div",0),n["\u0275\u0275template"](1,S,3,0,"ng-container",1),n["\u0275\u0275template"](2,F,19,6,"ng-container",1),n["\u0275\u0275elementEnd"]()),2&e&&(n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf",t.loaderObject.isComponentLoading),n["\u0275\u0275advance"](1),n["\u0275\u0275property"]("ngIf",!t.loaderObject.isComponentLoading))},directives:[u.NgIf,v.c,o.J,o.w,o.n,f.a,o.v,o.l,h.a,b.a,m.c,p.b,o.e,g.g,o.F,g.i,m.i,g.f,x.a],styles:[".delegation-details[_ngcontent-%COMP%]   .delegation-checkbox[_ngcontent-%COMP%]{margin-top:8px}.delegation-details[_ngcontent-%COMP%]   .delegation-checkbox[_ngcontent-%COMP%]   .mat-checkbox-label[_ngcontent-%COMP%]{font-size:14px;color:#333}.delegation-details[_ngcontent-%COMP%]   .field-title[_ngcontent-%COMP%]{font-weight:600;color:#5f6c81;font-size:14px;margin-bottom:8px}.delegation-details[_ngcontent-%COMP%]   .required-star[_ngcontent-%COMP%]{color:#f44336}.delegation-details[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]{font-size:18px;font-weight:600;color:#333}.delegation-details[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{border:1px solid #ccc;color:#666}.delegation-details[_ngcontent-%COMP%]   .create-employee-btn[_ngcontent-%COMP%]{background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%)!important;color:#fff;height:42px;margin-left:10px}.delegation-details[_ngcontent-%COMP%]   .create-employee-btn[_ngcontent-%COMP%]:disabled{background-color:#ccc;color:#999}"]}),e})(),k=(()=>{class e{}return e.\u0275mod=n["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=n["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[u.CommonModule,o.E,m.e,p.c,h.b,g.h,f.b,v.b,b.b,D.a]]}),e})()},NJ67:function(e,t,a){"use strict";a.d(t,"a",(function(){return i}));class i{constructor(){this.disabled=!1}onChange(e){}onTouched(e){}writeValue(e){this.value=e}registerOnChange(e){this.onChange=e}registerOnTouched(e){this.onTouched=e}setDisabledState(e){this.disabled=e}}},qFYv:function(e,t,a){"use strict";a.d(t,"a",(function(){return S}));var i=a("fXoL"),n=a("tk/3"),o=a("XNiG"),l=a("3Pt+"),r=a("NJ67"),s=a("1G5W"),d=a("Kj3r"),c=a("XXEo"),u=a("kmnG"),m=a("ofXK"),p=a("qFsG"),h=a("/1cH"),g=a("bTqV"),f=a("NFeN"),v=a("Qu3c"),b=a("FKr1");function D(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"button",7),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"]().checkAndClearInput(!0)})),i["\u0275\u0275elementStart"](1,"mat-icon",8),i["\u0275\u0275text"](2," close "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}}function y(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"mat-option",9),i["\u0275\u0275elementStart"](1,"div",10),i["\u0275\u0275element"](2,"div"),i["\u0275\u0275element"](3,"div"),i["\u0275\u0275element"](4,"div"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]())}function C(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"mat-option",12),i["\u0275\u0275listener"]("onSelectionChange",(function(){i["\u0275\u0275restoreView"](e);const a=t.$implicit;return i["\u0275\u0275nextContext"](2).resultClicked(a)})),i["\u0275\u0275elementStart"](1,"span"),i["\u0275\u0275elementStart"](2,"small",13),i["\u0275\u0275text"](3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,a=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](2),i["\u0275\u0275classMap"](a.ngClasses),i["\u0275\u0275propertyInterpolate3"]("matTooltip","",e[a.optionLabel[0]],"",e[a.optionLabel[1]]?" - ":"","",e[a.optionLabel[1]]?e[a.optionLabel[1]]:"",""),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate3"]("",e[a.optionLabel[0]],"",e[a.optionLabel[1]]?" - ":"","",e[a.optionLabel[1]]?e[a.optionLabel[1]]:"","")}}function x(e,t){if(1&e&&(i["\u0275\u0275elementContainerStart"](0),i["\u0275\u0275template"](1,C,4,9,"mat-option",11),i["\u0275\u0275elementContainerEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngForOf",e.searchResult)}}let S=(()=>{class e extends r.a{constructor(e,t){super(),this.http=e,this._login=t,this.label="",this.placeholder="",this.isRequired=!1,this.isReadOnly=!1,this.emitVal=new i.EventEmitter,this.optClicked=!1,this.searchTextControl=new l.j,this.searchResult=[],this.isLoading=!1,this.ifPlaceholderExists=!1,this._onDestroy=new o.b,this.resultClicked=e=>{setTimeout(()=>{this.optClicked=!0,this.outputParam?(this.onChange(e[this.outputParam]),this.emitVal.emit(e[this.outputParam])):(this.onChange(JSON.stringify(e)),this.emitVal.emit(JSON.stringify(e))),setTimeout(()=>{document.getElementById("searchTextInput").blur()},100),this.searchTextControl.patchValue(e[this.optionLabel[0]]+(this.optionLabel.length>1?" - "+e[this.optionLabel[1]]:""))},100)}}ngOnInit(){""!=this.placeholder&&""==this.label&&(this.label=this.placeholder,this.ifPlaceholderExists=!0),this.searchTxtControlSubscription=this.searchTextControl.valueChanges.pipe(Object(s.a)(this._onDestroy),Object(d.a)(700)).subscribe(e=>{e&&e.length>0&&this.callApiFn(e)})}callInitApi(){null==this.searchTextControl.value&&0==this.searchResult.length&&this.callApiFn("")}callApiFn(e){this.isLoading=!0;let t={searchParameter:e,options:this.bodyParams},a={headers:new n.f({Authorization:"Bearer "+this._login.getToken()})};this.http.post(this.apiUri,t,a).pipe(Object(s.a)(this._onDestroy)).subscribe(e=>{this.isLoading=!1,this.searchResult=e},e=>{console.error(e)})}checkAndClearInput(e){this.optClicked&&!e||0!=this.isReadOnly||(this.searchTextControl.reset(),this.onChange(null),this.emitVal.emit(null)),e&&setTimeout(()=>{document.getElementById("searchTextInput").blur(),setTimeout(()=>{document.getElementById("searchTextInput").focus()},100)},100)}writeValue(e){if(e){let t=JSON.parse(e);this.searchTextControl.patchValue(t[this.optionLabel[0]]+(this.optionLabel.length>1?" - "+t[this.optionLabel[1]]:""))}else this.searchTextControl.patchValue("")}resetSuggestion(){this.searchTextControl.patchValue("")}ngOnDestroy(){this.searchTxtControlSubscription&&this.searchTxtControlSubscription.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](n.c),i["\u0275\u0275directiveInject"](c.a))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search-huge-input"]],inputs:{apiUri:"apiUri",bodyParams:"bodyParams",outputParam:"outputParam",optionLabel:"optionLabel",optionValue:"optionValue",label:"label",placeholder:"placeholder",ngClasses:"ngClasses",isRequired:"isRequired",isReadOnly:"isReadOnly"},outputs:{emitVal:"emitVal"},features:[i["\u0275\u0275ProvidersFeature"]([{provide:l.t,useExisting:Object(i.forwardRef)(()=>e),multi:!0}]),i["\u0275\u0275InheritDefinitionFeature"]],decls:9,vars:11,consts:[["appearance","outline",3,"ngClass"],["id","searchTextInput","matInput","","autocomplete","off",3,"placeholder","matAutocomplete","formControl","required","readonly","focusout","focusin","keyup"],["mat-button","","matSuffix","","mat-icon-button","","style","height: 30px; width: 30px; line-height: 1",3,"click",4,"ngIf"],[3,"displayWith"],["auto","matAutocomplete"],["class","is-loading",4,"ngIf"],[4,"ngIf"],["mat-button","","matSuffix","","mat-icon-button","",2,"height","30px","width","30px","line-height","1",3,"click"],["matTooltip","Clear",2,"font-size","20px !important","color","#66615b !important"],[1,"is-loading"],[1,"lds-facebook"],[3,"onSelectionChange",4,"ngFor","ngForOf"],[3,"onSelectionChange"],[2,"padding-left","12px",3,"matTooltip"]],template:function(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-form-field",0),i["\u0275\u0275elementStart"](1,"mat-label"),i["\u0275\u0275text"](2),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](3,"input",1),i["\u0275\u0275listener"]("focusout",(function(){return t.checkAndClearInput(!1)}))("focusin",(function(){return t.callInitApi()}))("keyup",(function(){return t.optClicked=!1})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](4,D,3,0,"button",2),i["\u0275\u0275elementStart"](5,"mat-autocomplete",3,4),i["\u0275\u0275template"](7,y,5,0,"mat-option",5),i["\u0275\u0275template"](8,x,2,1,"ng-container",6),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275reference"](6);i["\u0275\u0275property"]("ngClass",t.ifPlaceholderExists?"label-no-float":""),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate"](t.label),i["\u0275\u0275advance"](1),i["\u0275\u0275propertyInterpolate"]("placeholder",t.label),i["\u0275\u0275property"]("matAutocomplete",e)("formControl",t.searchTextControl)("required",t.isRequired)("readonly",t.isReadOnly),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null!=t.searchTextControl.value),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("displayWith",t.displayFn),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf",t.isLoading),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",0==t.isLoading)}},directives:[u.c,m.NgClass,u.g,p.b,h.d,l.e,l.v,l.k,l.F,m.NgIf,h.b,g.a,u.i,f.a,v.a,b.p,m.NgForOf],styles:[".label-no-float.mat-form-field-appearance-outline.mat-form-field-should-float .mat-form-field-label{display:none}  .label-no-float.mat-form-field-appearance-outline.mat-form-field-should-float .mat-form-field-outline-gap{border-top-color:initial}.mat-form-field[_ngcontent-%COMP%]{width:100%}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding-bottom:8px!important}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-underline{bottom:0}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-infix{padding:.65em 0!important;border-top:.54375em solid transparent!important;font-size:13px}.mat-form-field[_ngcontent-%COMP%]     .mat-select-arrow{margin:0 4px!important}.lds-facebook[_ngcontent-%COMP%]{display:inline-block;position:sticky;width:80px;height:24px;margin-left:110px}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{display:inline-block;position:absolute;left:4px;width:5px;background:#cf0001;animation:lds-facebook 1.2s cubic-bezier(0,.5,.5,1) infinite}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:first-child{left:8px;animation-delay:-.24s}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:nth-child(2){left:16px;animation-delay:-.12s}.lds-facebook[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]:nth-child(3){left:24px;animation-delay:0}@keyframes lds-facebook{0%{top:8px;height:32px}50%,to{top:12px;height:16px}}"]}),e})()}}]);