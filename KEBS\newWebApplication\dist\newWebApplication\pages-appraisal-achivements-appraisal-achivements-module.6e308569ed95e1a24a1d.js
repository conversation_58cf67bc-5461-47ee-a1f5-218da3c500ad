(window.webpackJsonp=window.webpackJsonp||[]).push([[838],{"5gTR":function(e,t,a){"use strict";a.r(t),a.d(t,"AppraisalAchivementsModule",(function(){return M}));var i=a("ofXK"),r=a("tyNb"),o=a("mrSG"),s=a("fXoL"),n=a("wd/R"),l=a("1G5W"),p=a("XNiG"),c=a("7xhW"),d=a("1S+Y"),u=a("sp/H"),h=a("BVzC"),f=a("LcQX"),y=a("XXEo");const m=["scoreBoardContainer"],g=["userWorkSpaceContainer"],C=["moduleDetailContainer"];function _(e,t){1&e&&(s["\u0275\u0275elementContainerStart"](0),s["\u0275\u0275elementStart"](1,"div",2),s["\u0275\u0275text"](2," No Appraisal Cycles Found ! "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](3,"div",3),s["\u0275\u0275element"](4,"img",4),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementContainerEnd"]())}function v(e,t){1&e&&(s["\u0275\u0275elementContainer"](0,null,5),s["\u0275\u0275elementContainer"](2,null,6),s["\u0275\u0275elementContainer"](4,null,7))}const A=[{path:"",component:(()=>{class e{constructor(e,t,a,i,r,o,s,n,l){this.componentFactoryResolver=e,this.compiler=t,this._EmployeeAppraisalsService=a,this._AppraisalEvaluatorsService=i,this.route=r,this._AppraisalCycleService=o,this._ErrorService=s,this._util=n,this._loginService=l,this._onDestroy=new p.b}ngOnDestroy(){this.scoreBoardContainerRef&&this.scoreBoardContainerRef.clear(),this.userWorkSpaceContainerRef&&this.userWorkSpaceContainerRef.clear(),this.moduleDetailContainerRef&&this.moduleDetailContainerRef.clear(),this._onDestroy.next(),this._onDestroy.complete()}ngOnInit(){return Object(o.c)(this,void 0,void 0,(function*(){let e=n();this.appraisalYear=yield this.getDefaultYear(),""==this.appraisalYear&&(this.appraisalYear=e.month()>=3?e.format("YYYY"):e.subtract(1,"years").format("YYYY")),this.route.parent.params.subscribe(e=>{console.log(e),1==e.oid?(this.oid=this._loginService.getProfile().profile.oid,this.getAppraisalCycleData()):(this.oid=e.oid,this.getAppraisalCycleDataForAdmin(e.oid))})}))}getAppraisalCycleData(){return Object(o.c)(this,void 0,void 0,(function*(){this._EmployeeAppraisalsService.getAppraisalMetricesAll(yield this._AppraisalEvaluatorsService.getCurrentUserOID(),this.appraisalYear).subscribe(e=>{"N"==e.error?(this.appraisalCycleData=e.data[0],this.lazyLoadScoreBoard(null==e?void 0:e.defaultDisplayModule).then(()=>{(null==e?void 0:e.defaultDisplayModule)||this.lazyLoadUserWorkSpace(null==e?void 0:e.defaultDisplayModule)})):alert("error")},e=>{console.log(e)})}))}getAppraisalCycleDataForAdmin(e){return Object(o.c)(this,void 0,void 0,(function*(){this._EmployeeAppraisalsService.getAppraisalMetricesAll(e,this.appraisalYear).subscribe(e=>{"N"==e.error?(this.appraisalCycleData=e.data[0],this.lazyLoadScoreBoard(null==e?void 0:e.defaultDisplayModule).then(()=>{(null==e?void 0:e.defaultDisplayModule)||this.lazyLoadUserWorkSpace(null==e?void 0:e.defaultDisplayModule)})):alert("error")},e=>{console.log(e)})}))}lazyLoadScoreBoard(e){return new Promise((t,i)=>{a.e(316).then(a.bind(null,"ceuQ")).then(a=>{var i,r;const o=this.compiler.compileModuleSync(a.ScoreBoardModule).create(this.scoreBoardContainerRef.injector).componentFactoryResolver.resolveComponentFactory(a.ScoreBoardComponent),s=this.scoreBoardContainerRef.createComponent(o);s.instance.scoreBoardIpData=null===(i=this.appraisalCycleData)||void 0===i?void 0:i.employee_appraisal_structure[0],s.instance.scoreBoardIpData.acknowledge_status=null===(r=this.appraisalCycleData)||void 0===r?void 0:r.acknowledge_status,s.instance.scoreBoardIpData.employeeOid=this.oid,s.instance.scoreBoardIpData.defaultDisplayModule=e,s.instance.changeInScoreBoard.subscribe(t=>{console.log(t),"click"==t.event&&"module"==t.changeType?(this.userWorkSpaceContainerRef&&this.userWorkSpaceContainerRef.clear(),this.lazyLoadModuleDetailView(t)):"deselect"==t.event?(this.moduleDetailContainerRef.clear(),this.lazyLoadUserWorkSpace(e)):"year"==t.changeType&&(this.scoreBoardContainerRef&&this.scoreBoardContainerRef.clear(),this.userWorkSpaceContainerRef&&this.userWorkSpaceContainerRef.clear(),this.moduleDetailContainerRef.clear(),this.appraisalYear=t.value,this.route.parent.params.subscribe(e=>{console.log(e),1==e.oid?this.getAppraisalCycleData():this.getAppraisalCycleDataForAdmin(e.oid)}))}),t(1)})})}lazyLoadModuleDetailView(e){this.moduleDetailContainerRef&&this.moduleDetailContainerRef.clear(),Promise.all([a.e(2),a.e(3),a.e(4),a.e(5),a.e(6),a.e(8),a.e(9),a.e(10),a.e(11),a.e(12),a.e(18),a.e(20),a.e(19),a.e(21),a.e(26),a.e(59),a.e(127)]).then(a.bind(null,"aQt1")).then(t=>{const a=this.compiler.compileModuleSync(t.AppraisalModuleDetailModule).create(this.moduleDetailContainerRef.injector).componentFactoryResolver.resolveComponentFactory(t.ModuleDetailComponent);this.moduleDetailContainerRef&&this.moduleDetailContainerRef.clear();const i=this.moduleDetailContainerRef.createComponent(a);i.instance.selectedModuleData=e,i.instance.appraisalYear=this.appraisalYear,i.instance.employeeOid=this.oid,i.instance.empAppraisalAllDetails=this.appraisalCycleData})}lazyLoadUserWorkSpace(e){a.e(249).then(a.bind(null,"9s0L")).then(t=>{const a=this.compiler.compileModuleSync(t.AppraisalWorkSpaceModule).create(this.userWorkSpaceContainerRef.injector).componentFactoryResolver.resolveComponentFactory(t.AppraisalWorkspaceComponent),i=this.userWorkSpaceContainerRef.createComponent(a);i.instance.appraisalYear=this.appraisalYear,i.instance.defaultDisplayModule=e})}getDefaultYear(){let e={configuration_name:"default_display_apprisal_year"};return new Promise((t,a)=>{this._AppraisalCycleService.getDefaultAppraisalYear(e).pipe(Object(l.a)(this._onDestroy)).subscribe(e=>{e.data.length>0?t(e.data[0].configuration_data):(this._util.showMessage("No data Found","Dismiss"),t(""))},e=>{this._ErrorService.userErrorAlert(e.error.code,"Some Error Happened in completing the Activity",e.error.errMessage),a(e)})})}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275directiveInject"](s.ComponentFactoryResolver),s["\u0275\u0275directiveInject"](s.Compiler),s["\u0275\u0275directiveInject"](c.a),s["\u0275\u0275directiveInject"](d.a),s["\u0275\u0275directiveInject"](r.a),s["\u0275\u0275directiveInject"](u.a),s["\u0275\u0275directiveInject"](h.a),s["\u0275\u0275directiveInject"](f.a),s["\u0275\u0275directiveInject"](y.a))},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-appraisal-achivements"]],viewQuery:function(e,t){if(1&e&&(s["\u0275\u0275viewQuery"](m,!0,s.ViewContainerRef),s["\u0275\u0275viewQuery"](g,!0,s.ViewContainerRef),s["\u0275\u0275viewQuery"](C,!0,s.ViewContainerRef)),2&e){let e;s["\u0275\u0275queryRefresh"](e=s["\u0275\u0275loadQuery"]())&&(t.scoreBoardContainerRef=e.first),s["\u0275\u0275queryRefresh"](e=s["\u0275\u0275loadQuery"]())&&(t.userWorkSpaceContainerRef=e.first),s["\u0275\u0275queryRefresh"](e=s["\u0275\u0275loadQuery"]())&&(t.moduleDetailContainerRef=e.first)}},decls:3,vars:2,consts:[[4,"ngIf","ngIfElse"],["showModules",""],[1,"d-flex","justify-content-center","bold-info"],[1,"d-flex","justify-content-center"],["src","https://assets.kebs.app/images/nomilestone.png","height","350","width","400",1,"mt-4"],["scoreBoardContainer",""],["userWorkSpaceContainer",""],["moduleDetailContainer",""]],template:function(e,t){if(1&e&&(s["\u0275\u0275template"](0,_,5,0,"ng-container",0),s["\u0275\u0275template"](1,v,6,0,"ng-template",null,1,s["\u0275\u0275templateRefExtractor"])),2&e){const e=s["\u0275\u0275reference"](2);s["\u0275\u0275property"]("ngIf","N"==t.appraisalCycleData)("ngIfElse",e)}},directives:[i.NgIf],styles:[".bold-info[_ngcontent-%COMP%]{font-weight:500;font-size:large;padding-top:4rem}"]}),e})()}];let D=(()=>{class e{}return e.\u0275mod=s["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=s["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[r.k.forChild(A)],r.k]}),e})();var S=a("bhfF"),R=a("iadO"),P=a("1yaQ");let M=(()=>{class e{}return e.\u0275mod=s["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=s["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,D,S.b,R.h,P.b]]}),e})()},"sp/H":function(e,t,a){"use strict";a.d(t,"a",(function(){return s}));var i=a("fXoL"),r=a("tk/3"),o=a("XXEo");let s=(()=>{class e{constructor(e,t){this._httP=e,this._LoginService=t}createAppraisalCycle(e){return this._httP.post("/api/appraisal/cycles/createAppraisalcycle",e)}createAppraisalModule(e){return this._httP.post("/api/appraisal/modules/createAppraisalModules",e)}createEmployeeAppraisalMetrices(e){return this._httP.post("/api/appraisal/employeeMetrices/createEmployeeAppraisalMetrices",e)}getEvaluatorsList(e){return this._httP.post("/api/appraisal/configuration/getApproversBasedOnWorkflowId",e)}getEmployeeDesignations(){return this._httP.post("/api/appraisal/configuration/getDesignation",{})}getEmployeePositions(){return this._httP.post("/api/appraisal/configuration/getPosition",{})}getDepartments(){return this._httP.post("/api/appraisal/configuration/getDepartment",{})}getSubDivisions(){return this._httP.post("/api/appraisal/configuration/getSubDivisions",{})}getOrgs(){return this._httP.post("/api/appraisal/configuration/getOrgs",{})}getPractice(){return this._httP.post("/api/appraisal/configuration/getPractice",{})}getEmployeeBasedOnConf(e,t,a,i,r,o,s){return this._httP.post("/api/appraisal/configuration/getEmployeeBasedOnConfig",{dept_id:e,subDivision_id:t,org_id:a,desgn_id:i,position_id:r,prac_id:o,joining_month:s})}getEmployeeBasedOnConfForAnnualAppraisalCycle(e,t,a,i,r,o,s,n){return this._httP.post("/api/appraisal/configuration/getEmployeeBasedOnConfig",{dept_id:e,subDivision_id:t,org_id:a,desgn_id:i,position_id:r,prac_id:o,selectedEmployees:s,joining_month:n})}getAppraisalCycleAll(){return this._httP.post("/api/appraisal/cycles/getAppraisalCycleAll",{})}getAppraisalTemplateAll(){return this._httP.post("/api/appraisal/template/getAllTemplate",{})}getAppraisalModuleAll(){return this._httP.post("/api/appraisal/modules/getAllModules",{})}launchAppraisalCycle(e,t){return this._httP.post("/api/appraisal/cycles/changeAppraisalCycleStatus",{appraisal_cycle_id:e,status:t})}addEmployeeToAppraisalCycle(e,t){return this._httP.post("/api/appraisal/cycles/addEmployeeToAppraisalCycle",{cycle_id:e,employee_details:t})}generateCustomerReviewUrl(e){return this._httP.post("/api/appraisal/customerReview/generateCustomerReviewURL",{appraisal_cycle_id:e})}sendMailToCustomers(e){return this._httP.post("/api/appraisal/customerReview/sendMail",{from_oid:this._LoginService.getProfile().profile.oid,cycle_id:e})}createAppraisalTemplate(e){return this._httP.post("/api/appraisal/template/createTemplate",e)}editAppraisalTemplate(e){return this._httP.post("/api/appraisal/template/updateTemplate",e)}createAnnualCycle(e){return this._httP.post("/api/appraisal/cycles/createAnnualAppraisalCycleConfig",e)}getAnnualCycle(){return this._httP.post("/api/appraisal/cycles/getAllAnnualAppraisaCycleConfig",{})}getAppraisalConfigs(e){return this._httP.post("/api/appraisal/configuration/getConfigDataByConfigName",e)}updisPreRequisiteSatisifiedFlag(e){return this._httP.post("/api/appraisal/employeeAppraisal/updisPreRequisiteSatisifiedFlag",e)}getDefaultAppraisalYear(e){return this._httP.post("/api/appraisal/configuration/getConfigDataByConfigName",e)}editAppraisalCycle(e){return this._httP.post("/api/appraisal/cycles/editAppraisalCycle",e)}editAnnualCycle(e){return this._httP.post("/api/appraisal/cycles/editAnnualAppraisalCycleConfig",e)}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275inject"](r.c),i["\u0275\u0275inject"](o.a))},e.\u0275prov=i["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()}}]);