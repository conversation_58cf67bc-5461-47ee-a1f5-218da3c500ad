(window.webpackJsonp=window.webpackJsonp||[]).push([[740],{"2ChS":function(e,t,n){"use strict";n.d(t,"a",(function(){return C})),n.d(t,"b",(function(){return O})),n.d(t,"c",(function(){return w})),n.d(t,"d",(function(){return S}));var a=n("rDax"),i=n("+rOU"),o=n("fXoL"),r=n("FKr1"),l=n("0MNC"),s=n("R0Ic"),c=n("ofXK"),d=n("cH1L"),m=n("XNiG"),p=n("VRyK"),h=n("LRne"),u=n("FtGj"),f=n("pLZG"),g=n("IzEk"),v=n("u47x");function y(e,t){}const C=new o.InjectionToken("MatBottomSheetData");class b{constructor(){this.data=null,this.hasBackdrop=!0,this.disableClose=!1,this.ariaLabel=null,this.closeOnNavigation=!0,this.autoFocus=!1,this.restoreFocus=!0}}const _={bottomSheetState:Object(s.o)("state",[Object(s.l)("void, hidden",Object(s.m)({transform:"translateY(100%)"})),Object(s.l)("visible",Object(s.m)({transform:"translateY(0%)"})),Object(s.n)("visible => void, visible => hidden",Object(s.e)(`${r.b.COMPLEX} ${r.a.ACCELERATION_CURVE}`)),Object(s.n)("void => visible",Object(s.e)(`${r.b.EXITING} ${r.a.DECELERATION_CURVE}`))])};let x=(()=>{class e extends i.a{constructor(e,t,n,a,i,r){super(),this._elementRef=e,this._changeDetectorRef=t,this._focusTrapFactory=n,this.bottomSheetConfig=r,this._animationState="void",this._animationStateChanged=new o.EventEmitter,this._elementFocusedBeforeOpened=null,this.attachDomPortal=e=>(this._validatePortalAttached(),this._setPanelClass(),this._savePreviouslyFocusedElement(),this._portalOutlet.attachDomPortal(e)),this._document=i,this._breakpointSubscription=a.observe([l.b.Medium,l.b.Large,l.b.XLarge]).subscribe(()=>{this._toggleClass("mat-bottom-sheet-container-medium",a.isMatched(l.b.Medium)),this._toggleClass("mat-bottom-sheet-container-large",a.isMatched(l.b.Large)),this._toggleClass("mat-bottom-sheet-container-xlarge",a.isMatched(l.b.XLarge))})}attachComponentPortal(e){return this._validatePortalAttached(),this._setPanelClass(),this._savePreviouslyFocusedElement(),this._portalOutlet.attachComponentPortal(e)}attachTemplatePortal(e){return this._validatePortalAttached(),this._setPanelClass(),this._savePreviouslyFocusedElement(),this._portalOutlet.attachTemplatePortal(e)}enter(){this._destroyed||(this._animationState="visible",this._changeDetectorRef.detectChanges())}exit(){this._destroyed||(this._animationState="hidden",this._changeDetectorRef.markForCheck())}ngOnDestroy(){this._breakpointSubscription.unsubscribe(),this._destroyed=!0}_onAnimationDone(e){"hidden"===e.toState?this._restoreFocus():"visible"===e.toState&&this._trapFocus(),this._animationStateChanged.emit(e)}_onAnimationStart(e){this._animationStateChanged.emit(e)}_toggleClass(e,t){const n=this._elementRef.nativeElement.classList;t?n.add(e):n.remove(e)}_validatePortalAttached(){this._portalOutlet.hasAttached()}_setPanelClass(){const e=this._elementRef.nativeElement,t=this.bottomSheetConfig.panelClass;Array.isArray(t)?t.forEach(t=>e.classList.add(t)):t&&e.classList.add(t)}_trapFocus(){const e=this._elementRef.nativeElement;if(this._focusTrap||(this._focusTrap=this._focusTrapFactory.create(e)),this.bottomSheetConfig.autoFocus)this._focusTrap.focusInitialElementWhenReady();else{const t=this._document.activeElement;t===e||e.contains(t)||e.focus()}}_restoreFocus(){const e=this._elementFocusedBeforeOpened;if(this.bottomSheetConfig.restoreFocus&&e&&"function"==typeof e.focus){const t=this._document.activeElement,n=this._elementRef.nativeElement;t&&t!==this._document.body&&t!==n&&!n.contains(t)||e.focus()}this._focusTrap&&this._focusTrap.destroy()}_savePreviouslyFocusedElement(){this._elementFocusedBeforeOpened=this._document.activeElement,this._elementRef.nativeElement.focus&&Promise.resolve().then(()=>this._elementRef.nativeElement.focus())}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](o.ElementRef),o["\u0275\u0275directiveInject"](o.ChangeDetectorRef),o["\u0275\u0275directiveInject"](v.i),o["\u0275\u0275directiveInject"](l.a),o["\u0275\u0275directiveInject"](c.DOCUMENT,8),o["\u0275\u0275directiveInject"](b))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["mat-bottom-sheet-container"]],viewQuery:function(e,t){if(1&e&&o["\u0275\u0275staticViewQuery"](i.c,!0),2&e){let e;o["\u0275\u0275queryRefresh"](e=o["\u0275\u0275loadQuery"]())&&(t._portalOutlet=e.first)}},hostAttrs:["tabindex","-1","role","dialog","aria-modal","true",1,"mat-bottom-sheet-container"],hostVars:2,hostBindings:function(e,t){1&e&&o["\u0275\u0275syntheticHostListener"]("@state.start",(function(e){return t._onAnimationStart(e)}))("@state.done",(function(e){return t._onAnimationDone(e)})),2&e&&(o["\u0275\u0275attribute"]("aria-label",null==t.bottomSheetConfig?null:t.bottomSheetConfig.ariaLabel),o["\u0275\u0275syntheticHostProperty"]("@state",t._animationState))},features:[o["\u0275\u0275InheritDefinitionFeature"]],decls:1,vars:0,consts:[["cdkPortalOutlet",""]],template:function(e,t){1&e&&o["\u0275\u0275template"](0,y,0,0,"ng-template",0)},directives:[i.c],styles:[".mat-bottom-sheet-container{padding:8px 16px;min-width:100vw;box-sizing:border-box;display:block;outline:0;max-height:80vh;overflow:auto}.cdk-high-contrast-active .mat-bottom-sheet-container{outline:1px solid}.mat-bottom-sheet-container-xlarge,.mat-bottom-sheet-container-large,.mat-bottom-sheet-container-medium{border-top-left-radius:4px;border-top-right-radius:4px}.mat-bottom-sheet-container-medium{min-width:384px;max-width:calc(100vw - 128px)}.mat-bottom-sheet-container-large{min-width:512px;max-width:calc(100vw - 256px)}.mat-bottom-sheet-container-xlarge{min-width:576px;max-width:calc(100vw - 384px)}\n"],encapsulation:2,data:{animation:[_.bottomSheetState]},changeDetection:0}),e})(),w=(()=>{class e{}return e.\u0275mod=o["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=o["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[a.h,r.k,i.g],r.k]}),e})();class S{constructor(e,t,n){this._overlayRef=t,this._afterDismissed=new m.b,this._afterOpened=new m.b,this.containerInstance=e,this.disableClose=e.bottomSheetConfig.disableClose,e._animationStateChanged.pipe(Object(f.a)(e=>"done"===e.phaseName&&"visible"===e.toState),Object(g.a)(1)).subscribe(()=>{this._afterOpened.next(),this._afterOpened.complete()}),e._animationStateChanged.pipe(Object(f.a)(e=>"done"===e.phaseName&&"hidden"===e.toState),Object(g.a)(1)).subscribe(()=>{clearTimeout(this._closeFallbackTimeout),t.dispose()}),t.detachments().pipe(Object(g.a)(1)).subscribe(()=>{this._afterDismissed.next(this._result),this._afterDismissed.complete()}),Object(p.a)(t.backdropClick(),t.keydownEvents().pipe(Object(f.a)(e=>e.keyCode===u.h))).subscribe(e=>{this.disableClose||"keydown"===e.type&&Object(u.t)(e)||(e.preventDefault(),this.dismiss())})}dismiss(e){this._afterDismissed.closed||(this.containerInstance._animationStateChanged.pipe(Object(f.a)(e=>"start"===e.phaseName),Object(g.a)(1)).subscribe(e=>{this._closeFallbackTimeout=setTimeout(()=>{this._overlayRef.dispose()},e.totalTime+100),this._overlayRef.detachBackdrop()}),this._result=e,this.containerInstance.exit())}afterDismissed(){return this._afterDismissed}afterOpened(){return this._afterOpened}backdropClick(){return this._overlayRef.backdropClick()}keydownEvents(){return this._overlayRef.keydownEvents()}}const E=new o.InjectionToken("mat-bottom-sheet-default-options");let O=(()=>{class e{constructor(e,t,n,a,i){this._overlay=e,this._injector=t,this._parentBottomSheet=n,this._location=a,this._defaultOptions=i,this._bottomSheetRefAtThisLevel=null}get _openedBottomSheetRef(){const e=this._parentBottomSheet;return e?e._openedBottomSheetRef:this._bottomSheetRefAtThisLevel}set _openedBottomSheetRef(e){this._parentBottomSheet?this._parentBottomSheet._openedBottomSheetRef=e:this._bottomSheetRefAtThisLevel=e}open(e,t){const n=function(e,t){return Object.assign(Object.assign({},e),t)}(this._defaultOptions||new b,t),a=this._createOverlay(n),r=this._attachContainer(a,n),l=new S(r,a,this._location);if(e instanceof o.TemplateRef)r.attachTemplatePortal(new i.h(e,null,{$implicit:n.data,bottomSheetRef:l}));else{const t=new i.d(e,void 0,this._createInjector(n,l)),a=r.attachComponentPortal(t);l.instance=a.instance}return l.afterDismissed().subscribe(()=>{this._openedBottomSheetRef==l&&(this._openedBottomSheetRef=null)}),this._openedBottomSheetRef?(this._openedBottomSheetRef.afterDismissed().subscribe(()=>l.containerInstance.enter()),this._openedBottomSheetRef.dismiss()):l.containerInstance.enter(),this._openedBottomSheetRef=l,l}dismiss(e){this._openedBottomSheetRef&&this._openedBottomSheetRef.dismiss(e)}ngOnDestroy(){this._bottomSheetRefAtThisLevel&&this._bottomSheetRefAtThisLevel.dismiss()}_attachContainer(e,t){const n=o.Injector.create({parent:t&&t.viewContainerRef&&t.viewContainerRef.injector||this._injector,providers:[{provide:b,useValue:t}]}),a=new i.d(x,t.viewContainerRef,n);return e.attach(a).instance}_createOverlay(e){const t=new a.f({direction:e.direction,hasBackdrop:e.hasBackdrop,disposeOnNavigation:e.closeOnNavigation,maxWidth:"100%",scrollStrategy:e.scrollStrategy||this._overlay.scrollStrategies.block(),positionStrategy:this._overlay.position().global().centerHorizontally().bottom("0")});return e.backdropClass&&(t.backdropClass=e.backdropClass),this._overlay.create(t)}_createInjector(e,t){const n=e&&e.viewContainerRef&&e.viewContainerRef.injector,a=[{provide:S,useValue:t},{provide:C,useValue:e.data}];return!e.direction||n&&n.get(d.c,null)||a.push({provide:d.c,useValue:{value:e.direction,change:Object(h.a)()}}),o.Injector.create({parent:n||this._injector,providers:a})}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275inject"](a.e),o["\u0275\u0275inject"](o.Injector),o["\u0275\u0275inject"](e,12),o["\u0275\u0275inject"](c.Location,8),o["\u0275\u0275inject"](E,8))},e.\u0275prov=Object(o["\u0275\u0275defineInjectable"])({factory:function(){return new e(Object(o["\u0275\u0275inject"])(a.e),Object(o["\u0275\u0275inject"])(o.INJECTOR),Object(o["\u0275\u0275inject"])(e,12),Object(o["\u0275\u0275inject"])(c.Location,8),Object(o["\u0275\u0275inject"])(E,8))},token:e,providedIn:w}),e})()},"74sm":function(e,t,n){"use strict";n.r(t),n.d(t,"AdvanceCreationComponent",(function(){return $}));var a=n("mrSG"),i=n("fXoL"),o=n("wd/R"),r=n("xG9w"),l=n("ofXK"),s=n("bTqV"),c=n("NFeN"),d=n("Qu3c"),m=n("kmnG"),p=n("qFsG"),h=n("3Pt+"),u=(n("iadO"),n("2ChS"),n("Xi0T"),n("lVl8")),f=n("1yaQ"),g=n("FKr1"),v=n("Xa2L"),y=(n("d3UM"),n("pA3K"),n("9mMp"),n("XXEo")),C=n("LcQX"),b=n("ucYs"),_=n("VJID"),x=n("0IaG"),w=n("K8NC"),S=n("3kOA"),E=n("qFYv"),O=n("me71"),M=n("3vwd");function F(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"span",44),i["\u0275\u0275elementStart"](1,"button",45),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const n=t.index;return i["\u0275\u0275nextContext"](2).selectAdvanceType(n)})),i["\u0275\u0275elementStart"](2,"mat-icon",46),i["\u0275\u0275text"](3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275text"](4),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngClass",e.active?"btn-active":"btn-not-active"),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"](" ",e.icon," "),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" ",e.name," ")}}function A(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",12),i["\u0275\u0275elementStart"](1,"div",17),i["\u0275\u0275template"](2,F,5,3,"span",43),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngForOf",e.advanceTypes)}}function P(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",26),i["\u0275\u0275elementStart"](1,"app-exp-input-search",47),i["\u0275\u0275listener"]("change",(function(t){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"]().changeCostCenter(t)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("placeholder",null!=e.fieldConfig&&null!=e.fieldConfig.costCenterCode&&e.fieldConfig.costCenterCode.field_label?e.fieldConfig.costCenterCode.field_label:"Cost Center *")("list",e.costCenterList)}}function I(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",51),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](2,"div",51),i["\u0275\u0275text"](3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"div",51),i["\u0275\u0275text"](5),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]().$implicit;i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](e.name),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"](" ",e.designation," "),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"](" Level ",e.level," ")}}function k(e,t){if(1&e&&(i["\u0275\u0275elementContainerStart"](0),i["\u0275\u0275element"](1,"app-user-image",49),i["\u0275\u0275template"](2,I,6,3,"ng-template",null,50,i["\u0275\u0275templateRefExtractor"]),i["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit,n=i["\u0275\u0275reference"](3);i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("tooltip",n)("id",e.oid)}}function T(e,t){if(1&e&&(i["\u0275\u0275elementContainerStart"](0),i["\u0275\u0275template"](1,k,4,2,"ng-container",48),i["\u0275\u0275elementContainerEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngForOf",e.approvers)}}function V(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",26),i["\u0275\u0275elementStart"](1,"mat-form-field",27),i["\u0275\u0275elementStart"](2,"mat-label"),i["\u0275\u0275text"](3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](4,"input",52),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](3),i["\u0275\u0275textInterpolate"](null!=e.fieldConfig&&null!=e.fieldConfig.department&&e.fieldConfig.department.field_label?e.fieldConfig.department.field_label:"Department"),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("placeholder",null!=e.fieldConfig&&null!=e.fieldConfig.department&&e.fieldConfig.department.field_label?e.fieldConfig.department.field_label:"Department")}}function D(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",53),i["\u0275\u0275elementStart"](1,"mat-form-field",27),i["\u0275\u0275elementStart"](2,"mat-label"),i["\u0275\u0275text"](3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](4,"input",54),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](3),i["\u0275\u0275textInterpolate"](null!=e.fieldConfig&&null!=e.fieldConfig.orgPracticeName&&e.fieldConfig.orgPracticeName.field_label?e.fieldConfig.orgPracticeName.field_label:"Org Name"),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("placeholder",null!=e.fieldConfig&&null!=e.fieldConfig.orgPracticeName&&e.fieldConfig.orgPracticeName.field_label?e.fieldConfig.orgPracticeName.field_label:"Org Name")}}function L(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"mat-error",55),i["\u0275\u0275text"](1," Amount should not be -ve "),i["\u0275\u0275elementEnd"]())}function B(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",16),i["\u0275\u0275elementStart"](1,"div",35),i["\u0275\u0275elementStart"](2,"mat-icon",56),i["\u0275\u0275text"](3,"error_outline"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"span",57),i["\u0275\u0275text"](5," Installment "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](6,"span",58),i["\u0275\u0275text"](7," Advance amount will be deducted from every month salary "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]())}function j(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"span",44),i["\u0275\u0275elementStart"](1,"button",60),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const n=t.index,a=t.$implicit,o=i["\u0275\u0275nextContext"](2);return o.selectInstallmentType(n,o.roundOffValue(o.advanceForm.value.totalAmount/a.noOfMonths))})),i["\u0275\u0275elementStart"](2,"span",61),i["\u0275\u0275text"](3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](4,"br"),i["\u0275\u0275elementStart"](5,"span"),i["\u0275\u0275text"](6),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngClass",e.active?"salary-btn-active":"salary-btn-not-active"),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate"](e.name),i["\u0275\u0275advance"](3),i["\u0275\u0275textInterpolate1"]("",n.roundOffValue(n.advanceForm.value.totalAmount/e.noOfMonths)," / Month ")}}function N(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",59),i["\u0275\u0275elementStart"](1,"div",35),i["\u0275\u0275template"](2,j,7,3,"span",43),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngForOf",e.installments)}}function R(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",35),i["\u0275\u0275element"](1,"app-exp-input-search",62),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("placeholder",null!=e.fieldConfig&&null!=e.fieldConfig.reasons&&e.fieldConfig.reasons.field_label?e.fieldConfig.reasons.field_label:"Reasons")("list",e.salaryAdvanceReasonMaster)}}function q(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",35),i["\u0275\u0275elementStart"](1,"mat-icon",56),i["\u0275\u0275text"](2,"error_outline"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](3,"span",58),i["\u0275\u0275text"](4," Please note that you cannot avail a loan that exceeds 5 times of your basic salary "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]())}function z(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"span",63),i["\u0275\u0275elementStart"](1,"attachment-upload-btn",64),i["\u0275\u0275listener"]("change",(function(t){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"]().changeInFiles(t)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("destinationBucket","kebs-expenses")("routingKey","expenses")("contextId",e.advanceForm.value.contextId)("allowEdit",!0)}}function W(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"mat-icon"),i["\u0275\u0275text"](1," done_all"),i["\u0275\u0275elementEnd"]())}function H(e,t){1&e&&i["\u0275\u0275element"](0,"mat-spinner",65)}const Y=function(){return["currency_code"]},J=function(e){return{"background-color":e}},X=function(e){return{"restict-cursor":e}};let $=(()=>{class e{constructor(e,t,n,l,s,c){this.fb=e,this._auth=t,this._util=n,this._wfService=l,this._expLazyService=s,this.dialogRef=c,this.close=new i.EventEmitter,this.minDate=o(),this.advanceTypes=[],this.selectedInstallmentIndex=null,this.installments=[{name:"1 Month",noOfMonths:1,amount:null,active:!1},{name:"3 Months",noOfMonths:3,amount:null,active:!1},{name:"6 Months",noOfMonths:6,amount:null,active:!1},{name:"9 Months",noOfMonths:9,amount:null,active:!1},{name:"12 Months",noOfMonths:12,amount:null,active:!1}],this.defaultLE="",this.defaultCurrency="",this.advanceTypeCode="",this.totalAmount=0,this.isBeingWorkflowDetermined=!1,this.isBeingSubmitted=!1,this.salaryAdvanceReasonMaster=[],this.costCenterList=[],this.isAttachmentVisible=!1,this.attachmentMandatoryCategory=[],this.isDisableClaimedBy=!1,this.fieldConfig={},this.getExpenseConfig=()=>new Promise((e,t)=>{this._expLazyService.getExpenseConfig().subscribe(t=>{t.data&&e(t.data)},e=>{console.log(e),t(e)})}),this.closeDialog=()=>{this.dialogRef.close()},this.createForm=()=>{this.advanceForm=this.fb.group({expenseType:["A"],isAggregationAllowed:[""],workFlowId:["",h.H.required],originalInitiatorOId:[this.profile.oid,h.H.required],claimedBy:[this.profile.oid,h.H.required],costCenter:[""],department:[""],approvers:["",h.H.required],legalEntity:[this.defaultLE,h.H.required],claimDate:[o().format("YYYY-MM-DD hh:mm:ss"),h.H.required],totalAmount:["",[h.H.required,h.H.min(1),h.H.pattern("^[0-9]+(.[0-9]{0,9})?$")]],currency:[this.defaultCurrency,h.H.required],description:["",h.H.required],advanceTypeId:["",h.H.required],advanceTypeCode:["",h.H.required],advanceTypeName:["",h.H.required],installmentType:[""],installmentAmtPerMonth:[""],orgPracticeName:[""],reasons:[""],isBillable:[0],costCenterCode:[""],legalEntityCode:["",h.H.required],contextId:[null]}),this.detectCostCentreChanges(),this.setValidators()},this.determineWorkflow=()=>Object(a.c)(this,void 0,void 0,(function*(){let e=this.advanceForm.get("currency").value,t=this.totalAmount;if(e&&t&&""!=this.advanceTypeCode){this.expWorkflowProperties=yield this.getWorkFlowProperty("A",this.advanceTypeCode),console.log("Advance Workflow Properties"),console.log(this.expWorkflowProperties),this.determineWorkflowSubscription&&this.determineWorkflowSubscription.unsubscribe();let n=[{currency:e,amount:t,invoiceDate:this.advanceForm.value.claimDate}];this.isBeingWorkflowDetermined=!0,this.determineWorkflowSubscription=this._expLazyService.calculateTotalAmount(n,this.advanceForm.value.legalEntity,this.expWorkflowProperties).subscribe(e=>{if(console.log("Expense Final Workflow Advance"),console.log(e),this.isBeingWorkflowDetermined=!1,null==e)return this._util.showMessage("Workflow not found !","close");this.workflowProperties=e,this.getExpenseApproversHierarchy()},e=>{this.isBeingWorkflowDetermined=!1,console.error(e)})}console.log(this.isBeingWorkflowDetermined),console.log(this.isBeingSubmitted)})),this.getWorkFlowProperty=(e,t)=>new Promise((n,a)=>{this._expLazyService.getExpenseWorkFlowPropertiesOtherTypes(e,t).subscribe(e=>{console.log(e),n(e)},e=>{console.error(e),a(e)})}),this.getAdvanceCategoryMaster=()=>{this.categoryMasterSubscription&&this.categoryMasterSubscription.unsubscribe(),this.categoryMasterSubscription=this._expLazyService.getAdvanceCategory().subscribe(e=>{this.advanceTypes=e.data.map(e=>({active:!1,id:e.id,code:e.code,icon:e.mat_icon.toLowerCase(),name:e.name})),console.log(this.advanceTypes)},e=>{console.error(e)})},this.getSalaryAdvanceReasonsMaster=()=>{this._expLazyService.salaryAdvanceReasonsMaster().subscribe(e=>{this.salaryAdvanceReasonMaster=e},e=>{console.log(e)})},this.setValidators=()=>{const e=this.advanceForm.get("installmentType"),t=this.advanceForm.get("installmentAmtPerMonth"),n=this.advanceForm.get("costCenter"),i=this.advanceForm.get("costCenterCode"),o=this.advanceForm.get("department"),r=this.advanceForm.get("reasons");this.advanceForm.get("advanceTypeCode").valueChanges.subscribe(l=>Object(a.c)(this,void 0,void 0,(function*(){this.advanceTypeCode=l,console.log("Advance Type Code - "+this.advanceTypeCode),this.determineWorkflow(),"SA"===l&&(e.setValidators([h.H.required]),t.setValidators([h.H.required]),r.setValidators([h.H.required]),n.setValidators(null),i.setValidators(null),o.setValidators(null),this.advanceForm.patchValue({orgPracticeName:yield this.getOrgPracticeName()}),this.getSalaryAdvanceReasonsMaster()),"PC"===l&&(e.setValidators(null),t.setValidators(null),n.setValidators([h.H.required]),o.setValidators([h.H.required]),r.setValidators(null)),"T"===l&&(e.setValidators(null),t.setValidators(null),n.setValidators([h.H.required]),o.setValidators([h.H.required]),r.setValidators(null)),e.updateValueAndValidity(),t.updateValueAndValidity(),n.updateValueAndValidity(),o.updateValueAndValidity(),r.updateValueAndValidity()})))},this.detectCostCentreChanges=()=>{this.advanceForm.get("claimedBy").valueChanges.subscribe(e=>Object(a.c)(this,void 0,void 0,(function*(){this.claimedBy=e,1==this.isDefaultCostCenter&&(this.advanceForm.controls.costCenter.patchValue(""),this.advanceForm.controls.costCenterCode.patchValue(""),this.getCostCenterOfClaimer()),this.determineWorkflow(),"SA"==this.advanceTypeCode&&this.claimedBy&&this.advanceForm.patchValue({orgPracticeName:yield this.getOrgPracticeName()})}))),this.advanceForm.get("legalEntity").valueChanges.subscribe(e=>Object(a.c)(this,void 0,void 0,(function*(){e&&(this.legalEntity=e.legalEntity,this.getEntityCurrency(),yield this.determineWorkflow())}))),this.advanceForm.get("costCenter").valueChanges.subscribe(e=>Object(a.c)(this,void 0,void 0,(function*(){e&&(this.costCenter=e.cost_centre,this.getDepartmentMaster(this.costCenter),yield this.determineWorkflow())}))),this.advanceForm.get("currency").valueChanges.subscribe(e=>Object(a.c)(this,void 0,void 0,(function*(){console.log("currency"),console.log(e),e&&(yield this.determineWorkflow())}))),this.advanceForm.get("totalAmount").valueChanges.subscribe(e=>Object(a.c)(this,void 0,void 0,(function*(){console.log("Total Amount - "+e),e&&(this.totalAmount=e,this.approvers=null,this.selectedInstallmentIndex&&this.changeInstallmentTypeOnTotalAmtChange(this.totalAmount),yield this.determineWorkflow())})))},this.getOrgPracticeName=()=>new Promise((e,t)=>[this._expLazyService.getOrgPracticeName(this.profile.oid).subscribe(t=>{console.log(`${this.profile.oid} - ${t}`),e(t)},e=>{t(e)})]),this.getExpenseApproversHierarchy=()=>Object(a.c)(this,void 0,void 0,(function*(){let e=this.advanceForm.value.costCenter;if(e){let t=yield this.workflowProperties;this.advanceForm.patchValue({isAggregationAllowed:t.aggregation_allowed,workFlowId:t.workflow_id});let n={workflowId:t.workflow_id,sortByDesignation:t.sort_by_desgn,initiatorOId:this.claimedBy,approvalType:t.approval_type,approvalParams:t.approval_params,costCentresAndTypes:[{costCentre:e.cost_centre,costCentreType:e.cost_centre_type}]};console.log("approvers template"),console.log(n),this._expLazyService.getExpenseApproversHierarchy(n).subscribe(t=>{this._wfService.formatApproversHierarchy([e],t.data).then(e=>{console.log("Advance approvers"),console.log(e),this.approvers=e,this.advanceForm.get("approvers").patchValue(JSON.stringify(this.approvers))},e=>{console.error(e)})},e=>{console.error(e)})}else{console.log("No Cost Center");let t=yield this.workflowProperties;this.advanceForm.patchValue({isAggregationAllowed:t.aggregation_allowed,workFlowId:t.workflow_id});let n={workflowId:t.workflow_id,sortByDesignation:t.sort_by_desgn,initiatorOId:this.claimedBy,approvalType:t.approval_type,approvalParams:t.approval_params,costCentresAndTypes:[]};console.log("approvers template"),console.log(n),this._expLazyService.getExpenseApproversHierarchy(n).subscribe(t=>{this._wfService.formatApproversHierarchy([e],t.data).then(e=>{console.log("Advance approvers"),console.log(e),this.approvers=e,this.advanceForm.get("approvers").patchValue(JSON.stringify(this.approvers))},e=>{console.error(e)})},e=>{console.error(e)})}})),this.roundOffValue=e=>Math.round(e),this.getDepartmentMaster=e=>{this._expLazyService.getDepartmentMaster(e).subscribe(e=>{e&&1==e.length&&this.advanceForm.get("department").patchValue(e[0].department_name)},e=>{console.error(e)})},this.selectAdvanceType=e=>{this.advanceTypes.forEach((t,n)=>{t.active=n==e}),this.patchadvanceTypeDetail(e),this.approvers=null,this.setValidators(),this.advanceForm.controls.contextId.clearValidators(),this.advanceForm.controls.contextId.updateValueAndValidity(),1==this.isAttachmentVisible&&r.contains(this.attachmentMandatoryCategory,this.advanceForm.value.advanceTypeCode)&&(this.advanceForm.controls.contextId.setValidators([h.H.required]),this.advanceForm.controls.contextId.updateValueAndValidity())},this.patchadvanceTypeDetail=e=>{this.advanceForm.patchValue({advanceTypeId:this.advanceTypes[e].id,advanceTypeCode:this.advanceTypes[e].code,advanceTypeName:this.advanceTypes[e].name})},this.changeInstallmentTypeOnTotalAmtChange=e=>{this.patchInstallmentDetail(this.selectedInstallmentIndex,e/this.installments[this.selectedInstallmentIndex].noOfMonths)},this.selectInstallmentType=(e,t)=>{this.installments.forEach((t,n)=>{t.active=n==e}),this.selectedInstallmentIndex=e,this.patchInstallmentDetail(e,t)},this.patchInstallmentDetail=(e,t)=>{this.advanceForm.patchValue({installmentType:this.installments[e].noOfMonths,installmentAmtPerMonth:t})},this.saveExpense=()=>{if(this.advanceForm.valid){this.isBeingSubmitted=!0;let e=this.advanceForm.value;e.claimDate=this._util.convertToLocalTime(this.advanceForm.value.claimDate),e.isExpenseCreatedFromWeb=1,console.log(e),this._expLazyService.saveAdvanceDetail(e).subscribe(e=>{this.isBeingSubmitted=!1,console.log(e),this._util.showMessage("Advance request created successfully","close"),this.closeAfterSubmit()},e=>{console.error(e),this.isBeingSubmitted=!1,this._util.showMessage("Oops! something went wrong.","close")})}else this.checkValidation()},this.checkValidation=()=>{if(this.advanceForm.get("advanceTypeName").invalid)return this._util.showMessage("Please choose Advance Category ! ","close");if(this.advanceForm.get("claimedBy").invalid)return this._util.showMessage("Please fill Advance Requesting Person ! ","close");if(this.advanceForm.get("legalEntity").invalid)return this._util.showMessage("Please fill the Legal Entity ! ","close");if(this.advanceForm.get("totalAmount").invalid)return this._util.showMessage("Please fill the Amount ! ","close");if(this.advanceForm.get("currency").invalid)return this._util.showMessage("Please fill the Currency ! ","close");if(this.advanceForm.get("description").invalid)return this._util.showMessage("Please fill the Description ! ","close");if(this.advanceForm.get("approvers").invalid)return this._util.showMessage("Approvers not found ! ","close");if(this.advanceForm.get("workFlowId").invalid)return this._util.showMessage("Workflow not found ! ","close");if(this.advanceForm.get("contextId").invalid)return this._util.showMessage("Attachment not found ! ","close");if("PC"==this.advanceTypeCode||"T"==this.advanceTypeCode){if(this.advanceForm.get("costCenter").invalid)return this._util.showMessage("Please fill the Cost Center ! ","close");if(this.advanceForm.get("department").invalid)return this._util.showMessage("Please fill the Department ! ","close")}if("SA"==this.advanceTypeCode){if(this.advanceForm.get("reasons").invalid)return this._util.showMessage("Please choose the Reason ! ","close");if(this.advanceForm.get("installmentType").invalid)return this._util.showMessage("Please choose the Installment Type ! ","close");if(this.advanceForm.get("installmentAmtPerMonth").invalid)return this._util.showMessage("Please choose the Installment amount per month ! ","close")}},this.closeAfterSubmit=()=>{this.dialogRef.close("refresh")},this.profile=this._auth.getProfile().profile,this.createForm()}ngOnInit(){return Object(a.c)(this,void 0,void 0,(function*(){this.getAllCostCenter(),this.getAdvanceCategoryMaster(),this.defaultLegalEntityDetails(),this.getAdvanceFormFieldConfig(),yield this._expLazyService.getAllLegalEntity().then(e=>{e.data.length>0&&(this.legalEntityList=e.data)});let e=yield this.getExpenseConfig();if(e){let t=JSON.parse(e[0].expense_application_config);null!=t&&(this.isDefaultCostCenter=t.is_default_cost_center,this.isAttachmentVisible=t.is_advance_attachments_visible,1==t.is_disable_claimedby&&(this.isDisableClaimedBy=!0),1==this.isAttachmentVisible&&(this.attachmentMandatoryCategory=t.attachment_mandatory_advance_category),1==this.isDefaultCostCenter&&this.advanceForm.value.claimedBy)&&(yield this.getDepartmentCostCenter(this.advanceForm.value.claimedBy))}}))}getDepartmentCostCenter(e){return Object(a.c)(this,void 0,void 0,(function*(){this._expLazyService.getDepartmentCostCenter(e).subscribe(e=>{if("S"==e.messType){let t=e.data[0].cost_center;this.advanceForm.controls.costCenterCode.patchValue(t);let n=r.filter(this.costCenterList,e=>{if(e.id==t)return e});n.length>0&&this.advanceForm.controls.costCenter.patchValue(n[0])}},e=>{console.error(e)})}))}getAllCostCenter(){return Object(a.c)(this,void 0,void 0,(function*(){yield this._expLazyService.getAllCostCenterFunc().then(e=>{this.costCenterList=e,console.log(this.costCenterList)})}))}getCustomerBillingConfig(){return Object(a.c)(this,void 0,void 0,(function*(){this._expLazyService.getExpenseBillingConfig().subscribe(e=>{this.billingConfig=e.data},e=>{console.error(e)})}))}defaultLegalEntityDetails(){return Object(a.c)(this,void 0,void 0,(function*(){let e=yield this._expLazyService.getDefaultLE(this.claimedBy);e="string"==typeof e?JSON.parse(e):e,this.defaultLE=JSON.stringify({entity_id:e.entity_id,entity_name:e.entity_name,company_code:e.company_code,entity_currency_code:e.entity_currency_code}),this.defaultCurrency=JSON.stringify({currency_id:e.currency_id,currency_code:e.currency_code,currency_description:e.currency_description}),this.advanceForm.patchValue({legalEntity:this.defaultLE,currency:this.defaultCurrency,legalEntityCode:JSON.parse(this.defaultLE).entity_id})}))}getAdvanceFormFieldConfig(){return Object(a.c)(this,void 0,void 0,(function*(){yield this._expLazyService.getAdvanceFormFieldConfig().subscribe(e=>{this.formFieldConfig=e.data,this.formFieldConfig&&this.formFieldConfig.forEach(e=>{this.fieldConfig[e.field_key]=e})},e=>{console.error(e)})}))}changeInFiles(e){this.advanceForm.controls.contextId.patchValue(e)}getCostCenterOfClaimer(){return Object(a.c)(this,void 0,void 0,(function*(){yield this.getDepartmentCostCenter(this.claimedBy),this.determineWorkflow()}))}itemLegalEntityChange(){return Object(a.c)(this,void 0,void 0,(function*(){this.legalEntity=this.advanceForm.value.legalEntity,this.getEntityCurrency(),yield this.determineWorkflow()}))}changeCostCenter(e){this.advanceForm.controls.costCenter.patchValue(e)}getEntityCurrency(){return Object(a.c)(this,void 0,void 0,(function*(){this.advanceForm.value.legalEntity&&this._expLazyService.getEntityCurrency(this.advanceForm.value.legalEntityCode).subscribe(e=>{"S"==e.messType&&this.advanceForm.controls.currency.patchValue(JSON.stringify(e.data[0]))},e=>{console.error(e)})}))}changeLegalEntity(e){this.advanceForm.controls.legalEntity.patchValue(e),this.getEntityCurrency()}ngOnDestroy(){this.categoryMasterSubscription&&this.categoryMasterSubscription.unsubscribe(),this.determineWorkflowSubscription&&this.determineWorkflowSubscription.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](h.i),i["\u0275\u0275directiveInject"](y.a),i["\u0275\u0275directiveInject"](C.a),i["\u0275\u0275directiveInject"](b.a),i["\u0275\u0275directiveInject"](_.a),i["\u0275\u0275directiveInject"](x.h))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["ng-component"]],outputs:{close:"close"},features:[i["\u0275\u0275ProvidersFeature"]([{provide:g.c,useClass:f.c,deps:[g.f,f.a]},{provide:g.e,useValue:{parse:{dateInput:"DD-MM-YYYY"},display:{dateInput:"DD-MM-YYYY",monthYearLabel:"MMM YYYY"}}}])],decls:68,vars:36,consts:[[1,"container-fluid","pl-2","pr-2","create-advance-styles"],[3,"formGroup"],[1,"row","p-0"],[1,"col-12","p-0"],[1,"row","border-bottom","solid"],[1,"col-11","pt-2","pb-2","d-flex"],["mat-icon-button","",1,"bubble","mt-1"],[1,"iconButton"],[1,"name","my-auto","ml-3"],[1,"col-1","d-flex"],["mat-icon-button","",1,"ml-auto","close-button",3,"click"],[1,"close-Icon"],[1,"row","pt-3"],[1,"col-3x","pl-0","pr-0",2,"border-right","1px solid #d3d3d3"],["class","row pt-3",4,"ngIf"],[1,"col-9","pt-2","pr-0"],[1,"row"],[1,"col-12"],["required","true","formControlName","claimedBy",3,"isAutocomplete","label","readonly"],[1,"row","pt-2"],["required","true","formControlName","legalEntityCode",1,"create-claim-field-inputsearch",3,"placeholder","list","change"],["class","col-6",4,"ngIf"],[1,"col-6","d-flex","align-items-center"],[1,"mr-3","header","my-auto"],[4,"ngIf"],["class","col-6 pt-2",4,"ngIf"],[1,"col-6"],["appearance","outline",1,"create-advance-field"],["matInput","","required","true","formControlName","claimDate"],["matInput","","type","number","required","true","formControlName","totalAmount","onkeydown","return event.keyCode !== 69",3,"placeholder"],["class","pt-1",4,"ngIf"],["isRequired","true","formControlName","currency",3,"label","optionLabel","optionValue","apiUri"],["class","row",4,"ngIf"],["class","row pt-2 pb-2",4,"ngIf"],["class","col-12 mt-2",4,"ngIf"],[1,"col-12","mt-2"],["matInput","","required","true","formControlName","description",3,"placeholder"],[1,"row","pt-2","pr-3"],[1,"col-12","d-flex","justify-content-end","pr-5"],["class","mr-4",4,"ngIf"],["mat-icon-button","","matTooltip","Create Advance","type","submit",1,"iconbtn","ml-auto","mr-5","mt-1","mb-1",3,"ngStyle","ngClass","disabled","click"],[4,"ngIf","ngIfElse"],["showSubmitSpinner",""],["style","padding: 5px;",4,"ngFor","ngForOf"],[2,"padding","5px"],["mat-raised-button","",3,"ngClass","click"],[1,"claim-icons"],["formControlName","costCenterCode",1,"create-claim-field-inputsearch",3,"placeholder","list","change"],[4,"ngFor","ngForOf"],["content-type","template","placement","top","imgHeight","32px","imgWidth","32px","borderStyle","solid","borderWidth","2px",2,"margin","2px",3,"tooltip","id"],["approverTooltip",""],[1,"row","tooltip-text"],["matInput","","required","true","formControlName","department","readonly","",3,"placeholder"],[1,"col-6","pt-2"],["matInput","","required","true","formControlName","orgPracticeName","readonly","",3,"placeholder"],[1,"pt-1"],[1,"align-middle","mr-0",2,"font-size","21px","color","#b94141"],[2,"font-size","15px","font-weight","500","color","#cf0001"],[1,"ml-2",2,"font-weight","500","font-size","14px","color","#636060"],[1,"row","pt-2","pb-2"],["mat-raised-button","",1,"slide-from-down",3,"ngClass","click"],[2,"font-size","10","font-weight","500"],["formControlName","reasons","required","true",3,"placeholder","list"],[1,"mr-4"],[3,"destinationBucket","routingKey","contextId","allowEdit","change"],["diameter","30",1,"btn-spinner"]],template:function(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275elementStart"](1,"form",1),i["\u0275\u0275elementStart"](2,"div",2),i["\u0275\u0275elementStart"](3,"div",3),i["\u0275\u0275elementStart"](4,"div",4),i["\u0275\u0275elementStart"](5,"div",5),i["\u0275\u0275elementStart"](6,"div",6),i["\u0275\u0275elementStart"](7,"mat-icon",7),i["\u0275\u0275text"](8,"payments"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](9,"span",8),i["\u0275\u0275text"](10,"New Advance"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](11,"div",9),i["\u0275\u0275elementStart"](12,"button",10),i["\u0275\u0275listener"]("click",(function(){return t.closeDialog()})),i["\u0275\u0275elementStart"](13,"mat-icon",11),i["\u0275\u0275text"](14,"close"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](15,"div",12),i["\u0275\u0275elementStart"](16,"div",13),i["\u0275\u0275template"](17,A,3,1,"div",14),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](18,"div",15),i["\u0275\u0275elementStart"](19,"div",16),i["\u0275\u0275elementStart"](20,"div",17),i["\u0275\u0275element"](21,"app-expense-search-user",18),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](22,"div",19),i["\u0275\u0275elementStart"](23,"div",17),i["\u0275\u0275elementStart"](24,"app-exp-input-search",20),i["\u0275\u0275listener"]("change",(function(e){return t.changeLegalEntity(e)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](25,"div",19),i["\u0275\u0275template"](26,P,2,2,"div",21),i["\u0275\u0275elementStart"](27,"div",22),i["\u0275\u0275elementStart"](28,"p",23),i["\u0275\u0275text"](29,"Approvers"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](30,T,2,1,"ng-container",24),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](31,"div",19),i["\u0275\u0275template"](32,V,5,2,"div",21),i["\u0275\u0275template"](33,D,5,2,"div",25),i["\u0275\u0275elementStart"](34,"div",26),i["\u0275\u0275elementStart"](35,"mat-form-field",27),i["\u0275\u0275elementStart"](36,"mat-label"),i["\u0275\u0275text"](37),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](38,"input",28),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](39,"div",19),i["\u0275\u0275elementStart"](40,"div",26),i["\u0275\u0275elementStart"](41,"mat-form-field",27),i["\u0275\u0275elementStart"](42,"mat-label"),i["\u0275\u0275text"](43),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](44,"input",29),i["\u0275\u0275template"](45,L,2,0,"mat-error",30),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](46,"div",26),i["\u0275\u0275element"](47,"app-input-search-huge-input",31),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](48,B,8,0,"div",32),i["\u0275\u0275template"](49,N,3,1,"div",33),i["\u0275\u0275elementStart"](50,"div",16),i["\u0275\u0275template"](51,R,2,2,"div",34),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](52,"div",16),i["\u0275\u0275elementStart"](53,"div",35),i["\u0275\u0275elementStart"](54,"mat-form-field",27),i["\u0275\u0275elementStart"](55,"mat-label"),i["\u0275\u0275text"](56),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](57,"input",36),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](58,"div",16),i["\u0275\u0275template"](59,q,5,0,"div",34),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](60,"div",37),i["\u0275\u0275elementStart"](61,"div",38),i["\u0275\u0275elementStart"](62,"div"),i["\u0275\u0275template"](63,z,2,4,"span",39),i["\u0275\u0275elementStart"](64,"button",40),i["\u0275\u0275listener"]("click",(function(){return t.saveExpense()})),i["\u0275\u0275template"](65,W,2,0,"mat-icon",41),i["\u0275\u0275template"](66,H,1,0,"ng-template",null,42,i["\u0275\u0275templateRefExtractor"]),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275reference"](67);i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("formGroup",t.advanceForm),i["\u0275\u0275advance"](16),i["\u0275\u0275property"]("ngIf",t.advanceTypes.length>0),i["\u0275\u0275advance"](4),i["\u0275\u0275property"]("isAutocomplete",!0)("label",null!=t.fieldConfig&&null!=t.fieldConfig.claimedBy&&t.fieldConfig.claimedBy.field_label?t.fieldConfig.claimedBy.field_label:"Advance Requesting Person")("readonly",t.isDisableClaimedBy),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("placeholder",null!=t.fieldConfig&&null!=t.fieldConfig.legalEntityCode&&t.fieldConfig.legalEntityCode.field_label?t.fieldConfig.legalEntityCode.field_label:"Legal Entity")("list",t.legalEntityList),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf","SA"!=t.advanceTypeCode&&t.costCenterList.length>0),i["\u0275\u0275advance"](4),i["\u0275\u0275property"]("ngIf",t.approvers),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf","SA"!=t.advanceTypeCode),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf","SA"==t.advanceTypeCode),i["\u0275\u0275advance"](4),i["\u0275\u0275textInterpolate"](null!=t.fieldConfig&&null!=t.fieldConfig.claimDate&&t.fieldConfig.claimDate.field_label?t.fieldConfig.claimDate.field_label:"Date of submission"),i["\u0275\u0275advance"](6),i["\u0275\u0275textInterpolate"](null!=t.fieldConfig&&null!=t.fieldConfig.totalAmount&&t.fieldConfig.totalAmount.field_label?t.fieldConfig.totalAmount.field_label:"Amount"),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("placeholder",null!=t.fieldConfig&&null!=t.fieldConfig.totalAmount&&t.fieldConfig.totalAmount.field_label?t.fieldConfig.totalAmount.field_label:"Amount"),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null==t.advanceForm.controls.totalAmount.errors?null:t.advanceForm.controls.totalAmount.errors.min),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("label",null!=t.fieldConfig&&null!=t.fieldConfig.currency&&t.fieldConfig.currency.field_label?t.fieldConfig.currency.field_label:"Currency")("optionLabel",i["\u0275\u0275pureFunction0"](31,Y))("optionValue","currency_id")("apiUri","/api/exPrimary2/currencyMasterData"),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf","SA"==t.advanceForm.value.advanceTypeCode),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf","SA"==t.advanceForm.value.advanceTypeCode&&t.advanceForm.value.totalAmount>0),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf","SA"==t.advanceTypeCode),i["\u0275\u0275advance"](5),i["\u0275\u0275textInterpolate"](null!=t.fieldConfig&&null!=t.fieldConfig.description&&t.fieldConfig.description.field_label?t.fieldConfig.description.field_label:"Description"),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("placeholder",null!=t.fieldConfig&&null!=t.fieldConfig.description&&t.fieldConfig.description.field_label?t.fieldConfig.description.field_label:"Description"),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf","SA"==t.advanceTypeCode),i["\u0275\u0275advance"](4),i["\u0275\u0275property"]("ngIf",1==t.isAttachmentVisible),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngStyle",i["\u0275\u0275pureFunction1"](32,J,0==t.isBeingSubmitted?"#cf0001":"#f3f3f3"))("ngClass",i["\u0275\u0275pureFunction1"](34,X,t.isBeingSubmitted||t.isBeingWorkflowDetermined))("disabled",t.isBeingSubmitted||t.isBeingWorkflowDetermined),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",0==t.isBeingSubmitted)("ngIfElse",e)}},directives:[h.J,h.w,h.n,c.a,s.a,l.NgIf,w.a,h.F,h.v,h.l,S.a,m.c,m.g,p.b,h.e,h.A,E.a,d.a,l.NgStyle,l.NgClass,l.NgForOf,O.a,u.a,m.b,M.a,v.c],styles:[".create-advance-styles[_ngcontent-%COMP%]{background-image:url(expense_bg.64ac4aa5cba931ab8a07.png);background-size:190px 158px;min-height:92%;background-repeat:no-repeat;overflow-y:hidden!important;background-position:3% 103%}.create-advance-styles[_ngcontent-%COMP%]   .restict-cursor[_ngcontent-%COMP%]{cursor:not-allowed}.create-advance-styles[_ngcontent-%COMP%]   .name[_ngcontent-%COMP%]{color:#cf0001;font-weight:500;font-size:14px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.create-advance-styles[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{color:#5f5f5f;font-size:12px!important}.create-advance-styles[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]{display:flex;line-height:26px;border-radius:50%;cursor:context-menu!important;width:26px;height:26px;background-color:#fbfbfb;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.create-advance-styles[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#cf0001;font-size:18px;margin-top:3px!important;margin-left:4px!important}.create-advance-styles[_ngcontent-%COMP%]   .iconbtn[_ngcontent-%COMP%]{background-color:#c92020;color:#fff;padding:0;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.create-advance-styles[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.create-advance-styles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}.create-advance-styles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff;font-weight:400;margin-bottom:6%;width:11rem;text-align:left;padding:2px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.create-advance-styles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%]   .claim-icons[_ngcontent-%COMP%]{font-size:20px;margin-right:6px;margin-left:11px;color:#fff}.create-advance-styles[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]{background-color:#f3f3f3;color:#1a1a1a;margin-bottom:6%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;font-weight:400;text-align:left;width:11rem;padding:2px}.create-advance-styles[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]   .claim-icons[_ngcontent-%COMP%]{font-size:20px;margin-right:6px;margin-left:11px;color:#343434}.create-advance-styles[_ngcontent-%COMP%]   .btn-spinner[_ngcontent-%COMP%]{margin-left:4px}.create-advance-styles[_ngcontent-%COMP%]   .salary-btn-active[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff}.create-advance-styles[_ngcontent-%COMP%]   .salary-btn-active[_ngcontent-%COMP%], .create-advance-styles[_ngcontent-%COMP%]   .salary-btn-not-active[_ngcontent-%COMP%]{font-weight:400;margin-bottom:1%;width:9rem;height:5rem;text-align:center;padding:2px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.create-advance-styles[_ngcontent-%COMP%]   .salary-btn-not-active[_ngcontent-%COMP%]{background-color:#f3f3f3;color:#636060}.create-advance-styles[_ngcontent-%COMP%]   .col-3x[_ngcontent-%COMP%]{flex:0 0 20%;max-width:25%}.create-advance-styles[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}.create-advance-styles[_ngcontent-%COMP%]   .title-table[_ngcontent-%COMP%]{font-size:14px;color:#1a1a1a}.create-advance-styles[_ngcontent-%COMP%]   .create-advance-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%!important}.create-advance-styles[_ngcontent-%COMP%]   .create-claim-field-inputsearch[_ngcontent-%COMP%]{font-size:13px!important}.create-advance-styles[_ngcontent-%COMP%]   .create-claim-field-inputsearch[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]{width:70%!important}"]}),e})()},Uxlz:function(e,t,n){"use strict";n.r(t),n.d(t,"AdvanceCreationComponent",(function(){return G}));var a=n("mrSG"),i=n("fXoL"),o=n("wd/R"),r=n("xG9w"),l=n("ofXK"),s=n("bTqV"),c=n("NFeN"),d=n("Qu3c"),m=n("kmnG"),p=n("qFsG"),h=n("3Pt+"),u=n("iadO"),f=(n("2ChS"),n("Xi0T"),n("lVl8")),g=n("1yaQ"),v=n("FKr1"),y=n("Xa2L"),C=(n("d3UM"),n("pA3K"),n("ltUE"),n("XXEo")),b=n("LcQX"),_=n("ucYs"),x=n("fkeC"),w=n("0IaG"),S=n("mGZT"),E=n("TmG/"),O=n("qFYv"),M=n("me71"),F=n("3vwd");function A(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"span",46),i["\u0275\u0275elementStart"](1,"button",47),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const n=t.index;return i["\u0275\u0275nextContext"](2).selectAdvanceType(n)})),i["\u0275\u0275elementStart"](2,"mat-icon",48),i["\u0275\u0275text"](3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275text"](4),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngClass",e.active?"btn-active":"btn-not-active"),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"](" ",e.icon," "),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" ",e.name," ")}}function P(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",12),i["\u0275\u0275elementStart"](1,"div",17),i["\u0275\u0275template"](2,A,5,3,"span",45),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngForOf",e.advanceTypes)}}function I(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",26),i["\u0275\u0275elementStart"](1,"app-input-search",49),i["\u0275\u0275listener"]("change",(function(t){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"]().changeCostCenter(t)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("placeholder",null!=e.fieldConfig&&null!=e.fieldConfig.costCenterCode&&e.fieldConfig.costCenterCode.field_label?e.fieldConfig.costCenterCode.field_label:"Cost Center *")("list",e.costCenterList)}}function k(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",53),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](2,"div",53),i["\u0275\u0275text"](3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"div",53),i["\u0275\u0275text"](5),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]().$implicit;i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](e.name),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"](" ",e.designation," "),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"](" Level ",e.level," ")}}function T(e,t){if(1&e&&(i["\u0275\u0275elementContainerStart"](0),i["\u0275\u0275element"](1,"app-user-image",51),i["\u0275\u0275template"](2,k,6,3,"ng-template",null,52,i["\u0275\u0275templateRefExtractor"]),i["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit,n=i["\u0275\u0275reference"](3);i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("tooltip",n)("id",e.oid)}}function V(e,t){if(1&e&&(i["\u0275\u0275elementContainerStart"](0),i["\u0275\u0275template"](1,T,4,2,"ng-container",50),i["\u0275\u0275elementContainerEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngForOf",e.approvers)}}function D(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",26),i["\u0275\u0275elementStart"](1,"mat-form-field",27),i["\u0275\u0275elementStart"](2,"mat-label"),i["\u0275\u0275text"](3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](4,"input",54),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](3),i["\u0275\u0275textInterpolate"](null!=e.fieldConfig&&null!=e.fieldConfig.department&&e.fieldConfig.department.field_label?e.fieldConfig.department.field_label:"Department"),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("placeholder",null!=e.fieldConfig&&null!=e.fieldConfig.department&&e.fieldConfig.department.field_label?e.fieldConfig.department.field_label:"Department")}}function L(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",55),i["\u0275\u0275elementStart"](1,"mat-form-field",27),i["\u0275\u0275elementStart"](2,"mat-label"),i["\u0275\u0275text"](3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](4,"input",56),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](3),i["\u0275\u0275textInterpolate"](null!=e.fieldConfig&&null!=e.fieldConfig.orgPracticeName&&e.fieldConfig.orgPracticeName.field_label?e.fieldConfig.orgPracticeName.field_label:"Org Name"),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("placeholder",null!=e.fieldConfig&&null!=e.fieldConfig.orgPracticeName&&e.fieldConfig.orgPracticeName.field_label?e.fieldConfig.orgPracticeName.field_label:"Org Name")}}function B(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"mat-error",57),i["\u0275\u0275text"](1," Amount should not be -ve "),i["\u0275\u0275elementEnd"]())}function j(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",16),i["\u0275\u0275elementStart"](1,"div",37),i["\u0275\u0275elementStart"](2,"mat-icon",58),i["\u0275\u0275text"](3,"error_outline"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"span",59),i["\u0275\u0275text"](5," Installment "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](6,"span",60),i["\u0275\u0275text"](7," Advance amount will be deducted from every month salary "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]())}function N(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"span",46),i["\u0275\u0275elementStart"](1,"button",62),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const n=t.index,a=t.$implicit,o=i["\u0275\u0275nextContext"](2);return o.selectInstallmentType(n,o.roundOffValue(o.advanceForm.value.totalAmount/a.noOfMonths))})),i["\u0275\u0275elementStart"](2,"span",63),i["\u0275\u0275text"](3),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](4,"br"),i["\u0275\u0275elementStart"](5,"span"),i["\u0275\u0275text"](6),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngClass",e.active?"salary-btn-active":"salary-btn-not-active"),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate"](e.name),i["\u0275\u0275advance"](3),i["\u0275\u0275textInterpolate1"]("",n.roundOffValue(n.advanceForm.value.totalAmount/e.noOfMonths)," / Month ")}}function R(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",61),i["\u0275\u0275elementStart"](1,"div",37),i["\u0275\u0275template"](2,N,7,3,"span",45),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngForOf",e.installments)}}function q(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",37),i["\u0275\u0275element"](1,"app-input-search",64),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("placeholder",null!=e.fieldConfig&&null!=e.fieldConfig.reasons&&e.fieldConfig.reasons.field_label?e.fieldConfig.reasons.field_label:"Reasons")("list",e.salaryAdvanceReasonMaster)}}function z(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",37),i["\u0275\u0275elementStart"](1,"mat-icon",58),i["\u0275\u0275text"](2,"error_outline"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](3,"span",60),i["\u0275\u0275text"](4," Please note that you cannot avail a loan that exceeds 5 times of your basic salary "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]())}function W(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"span",65),i["\u0275\u0275elementStart"](1,"attachment-upload-btn",66),i["\u0275\u0275listener"]("change",(function(t){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"]().changeInFiles(t)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("destinationBucket","kebs-expenses")("routingKey","expenses")("contextId",e.advanceForm.value.contextId)("allowEdit",!0)}}function H(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"mat-icon"),i["\u0275\u0275text"](1," done_all"),i["\u0275\u0275elementEnd"]())}function Y(e,t){1&e&&i["\u0275\u0275element"](0,"mat-spinner",67)}const J=function(){return["currency_code"]},X=function(e){return{"background-color":e}},$=function(e){return{"restict-cursor":e}};let G=(()=>{class e{constructor(e,t,n,l,s,c){this.fb=e,this._auth=t,this._util=n,this._wfService=l,this._expLazyService=s,this.dialogRef=c,this.close=new i.EventEmitter,this.minDate=o(),this.advanceTypes=[],this.selectedInstallmentIndex=null,this.installments=[{name:"1 Month",noOfMonths:1,amount:null,active:!1},{name:"3 Months",noOfMonths:3,amount:null,active:!1},{name:"6 Months",noOfMonths:6,amount:null,active:!1},{name:"9 Months",noOfMonths:9,amount:null,active:!1},{name:"12 Months",noOfMonths:12,amount:null,active:!1}],this.defaultLE="",this.defaultCurrency="",this.advanceTypeCode="",this.totalAmount=0,this.isBeingWorkflowDetermined=!1,this.isBeingSubmitted=!1,this.salaryAdvanceReasonMaster=[],this.costCenterList=[],this.isAttachmentVisible=!1,this.attachmentMandatoryCategory=[],this.isDisableClaimedBy=!1,this.fieldConfig={},this.getExpenseConfig=()=>new Promise((e,t)=>{this._expLazyService.getExpenseConfig().subscribe(t=>{t.data&&e(t.data)},e=>{console.log(e),t(e)})}),this.closeDialog=()=>{this.dialogRef.close()},this.createForm=()=>{this.advanceForm=this.fb.group({expenseType:["A"],isAggregationAllowed:[""],workFlowId:["",h.H.required],originalInitiatorOId:[this.profile.oid,h.H.required],claimedBy:[this.profile.oid,h.H.required],costCenter:[""],department:[""],approvers:["",h.H.required],legalEntity:[this.defaultLE,h.H.required],claimDate:[o().format("YYYY-MM-DD hh:mm:ss"),h.H.required],totalAmount:["",[h.H.required,h.H.min(1),h.H.pattern("^[0-9]+(.[0-9]{0,9})?$")]],currency:[this.defaultCurrency,h.H.required],description:["",h.H.required],advanceTypeId:["",h.H.required],advanceTypeCode:["",h.H.required],advanceTypeName:["",h.H.required],installmentType:[""],installmentAmtPerMonth:[""],orgPracticeName:[""],reasons:[""],isBillable:[0],costCenterCode:[""],legalEntityCode:["",h.H.required],contextId:[null]}),this.detectCostCentreChanges(),this.setValidators()},this.determineWorkflow=()=>Object(a.c)(this,void 0,void 0,(function*(){let e=this.advanceForm.get("currency").value,t=this.totalAmount;if(e&&t&&""!=this.advanceTypeCode){this.expWorkflowProperties=yield this.getWorkFlowProperty("A",this.advanceTypeCode),console.log("Advance Workflow Properties"),console.log(this.expWorkflowProperties),this.determineWorkflowSubscription&&this.determineWorkflowSubscription.unsubscribe();let n=[{currency:e,amount:t,invoiceDate:this.advanceForm.value.claimDate}];this.isBeingWorkflowDetermined=!0,this.determineWorkflowSubscription=this._expLazyService.calculateTotalAmount(n,this.advanceForm.value.legalEntity,this.expWorkflowProperties).subscribe(e=>{if(console.log("Expense Final Workflow Advance"),console.log(e),this.isBeingWorkflowDetermined=!1,null==e)return this._util.showMessage("Workflow not found !","close");this.workflowProperties=e,this.getExpenseApproversHierarchy()},e=>{this.isBeingWorkflowDetermined=!1,console.error(e)})}console.log(this.isBeingWorkflowDetermined),console.log(this.isBeingSubmitted)})),this.getWorkFlowProperty=(e,t)=>new Promise((n,a)=>{this._expLazyService.getExpenseWorkFlowPropertiesOtherTypes(e,t).subscribe(e=>{console.log(e),n(e)},e=>{console.error(e),a(e)})}),this.getAdvanceCategoryMaster=()=>{this.categoryMasterSubscription&&this.categoryMasterSubscription.unsubscribe(),this.categoryMasterSubscription=this._expLazyService.getAdvanceCategory().subscribe(e=>{this.advanceTypes=e.data.map(e=>({active:!1,id:e.id,code:e.code,icon:e.mat_icon.toLowerCase(),name:e.name})),console.log(this.advanceTypes)},e=>{console.error(e)})},this.getSalaryAdvanceReasonsMaster=()=>{this._expLazyService.salaryAdvanceReasonsMaster().subscribe(e=>{this.salaryAdvanceReasonMaster=e},e=>{console.log(e)})},this.setValidators=()=>{const e=this.advanceForm.get("installmentType"),t=this.advanceForm.get("installmentAmtPerMonth"),n=this.advanceForm.get("costCenter"),i=this.advanceForm.get("costCenterCode"),o=this.advanceForm.get("department"),r=this.advanceForm.get("reasons");this.advanceForm.get("advanceTypeCode").valueChanges.subscribe(l=>Object(a.c)(this,void 0,void 0,(function*(){this.advanceTypeCode=l,console.log("Advance Type Code - "+this.advanceTypeCode),this.determineWorkflow(),"SA"===l&&(e.setValidators([h.H.required]),t.setValidators([h.H.required]),r.setValidators([h.H.required]),n.setValidators(null),i.setValidators(null),o.setValidators(null),this.advanceForm.patchValue({orgPracticeName:yield this.getOrgPracticeName()}),this.getSalaryAdvanceReasonsMaster()),"PC"===l&&(e.setValidators(null),t.setValidators(null),n.setValidators([h.H.required]),o.setValidators([h.H.required]),r.setValidators(null)),"T"===l&&(e.setValidators(null),t.setValidators(null),n.setValidators([h.H.required]),o.setValidators([h.H.required]),r.setValidators(null)),e.updateValueAndValidity(),t.updateValueAndValidity(),n.updateValueAndValidity(),o.updateValueAndValidity(),r.updateValueAndValidity()})))},this.detectCostCentreChanges=()=>{this.advanceForm.get("claimedBy").valueChanges.subscribe(e=>Object(a.c)(this,void 0,void 0,(function*(){this.claimedBy=e,1==this.isDefaultCostCenter&&(this.advanceForm.controls.costCenter.patchValue(""),this.advanceForm.controls.costCenterCode.patchValue(""),this.getCostCenterOfClaimer()),this.determineWorkflow(),"SA"==this.advanceTypeCode&&this.claimedBy&&this.advanceForm.patchValue({orgPracticeName:yield this.getOrgPracticeName()})}))),this.advanceForm.get("legalEntity").valueChanges.subscribe(e=>Object(a.c)(this,void 0,void 0,(function*(){e&&(this.legalEntity=e.legalEntity,this.getEntityCurrency(),yield this.determineWorkflow())}))),this.advanceForm.get("costCenter").valueChanges.subscribe(e=>Object(a.c)(this,void 0,void 0,(function*(){e&&(this.costCenter=e.cost_centre,this.getDepartmentMaster(this.costCenter),yield this.determineWorkflow())}))),this.advanceForm.get("currency").valueChanges.subscribe(e=>Object(a.c)(this,void 0,void 0,(function*(){console.log("currency"),console.log(e),e&&(yield this.determineWorkflow())}))),this.advanceForm.get("totalAmount").valueChanges.subscribe(e=>Object(a.c)(this,void 0,void 0,(function*(){console.log("Total Amount - "+e),e&&(this.totalAmount=e,this.approvers=null,this.selectedInstallmentIndex&&this.changeInstallmentTypeOnTotalAmtChange(this.totalAmount),yield this.determineWorkflow())})))},this.getOrgPracticeName=()=>new Promise((e,t)=>[this._expLazyService.getOrgPracticeName(this.profile.oid).subscribe(t=>{console.log(`${this.profile.oid} - ${t}`),e(t)},e=>{t(e)})]),this.getExpenseApproversHierarchy=()=>Object(a.c)(this,void 0,void 0,(function*(){let e=this.advanceForm.value.costCenter;if(e){let t=yield this.workflowProperties;this.advanceForm.patchValue({isAggregationAllowed:t.aggregation_allowed,workFlowId:t.workflow_id});let n={workflowId:t.workflow_id,sortByDesignation:t.sort_by_desgn,initiatorOId:this.claimedBy,approvalType:t.approval_type,approvalParams:t.approval_params,costCentresAndTypes:[{costCentre:e.cost_centre,costCentreType:e.cost_centre_type}]};console.log("approvers template"),console.log(n),this._expLazyService.getExpenseApproversHierarchy(n).subscribe(t=>{this._wfService.formatApproversHierarchy([e],t.data).then(e=>{console.log("Advance approvers"),console.log(e),this.approvers=e,this.advanceForm.get("approvers").patchValue(JSON.stringify(this.approvers))},e=>{console.error(e)})},e=>{console.error(e)})}else{console.log("No Cost Center");let t=yield this.workflowProperties;this.advanceForm.patchValue({isAggregationAllowed:t.aggregation_allowed,workFlowId:t.workflow_id});let n={workflowId:t.workflow_id,sortByDesignation:t.sort_by_desgn,initiatorOId:this.claimedBy,approvalType:t.approval_type,approvalParams:t.approval_params,costCentresAndTypes:[]};console.log("approvers template"),console.log(n),this._expLazyService.getExpenseApproversHierarchy(n).subscribe(t=>{this._wfService.formatApproversHierarchy([e],t.data).then(e=>{console.log("Advance approvers"),console.log(e),this.approvers=e,this.advanceForm.get("approvers").patchValue(JSON.stringify(this.approvers))},e=>{console.error(e)})},e=>{console.error(e)})}})),this.roundOffValue=e=>Math.round(e),this.getDepartmentMaster=e=>{this._expLazyService.getDepartmentMaster(e).subscribe(e=>{e&&1==e.length&&this.advanceForm.get("department").patchValue(e[0].department_name)},e=>{console.error(e)})},this.selectAdvanceType=e=>{this.advanceTypes.forEach((t,n)=>{t.active=n==e}),this.patchadvanceTypeDetail(e),this.approvers=null,this.setValidators(),this.advanceForm.controls.contextId.clearValidators(),this.advanceForm.controls.contextId.updateValueAndValidity(),1==this.isAttachmentVisible&&r.contains(this.attachmentMandatoryCategory,this.advanceForm.value.advanceTypeCode)&&(this.advanceForm.controls.contextId.setValidators([h.H.required]),this.advanceForm.controls.contextId.updateValueAndValidity())},this.patchadvanceTypeDetail=e=>{this.advanceForm.patchValue({advanceTypeId:this.advanceTypes[e].id,advanceTypeCode:this.advanceTypes[e].code,advanceTypeName:this.advanceTypes[e].name})},this.changeInstallmentTypeOnTotalAmtChange=e=>{this.patchInstallmentDetail(this.selectedInstallmentIndex,e/this.installments[this.selectedInstallmentIndex].noOfMonths)},this.selectInstallmentType=(e,t)=>{this.installments.forEach((t,n)=>{t.active=n==e}),this.selectedInstallmentIndex=e,this.patchInstallmentDetail(e,t)},this.patchInstallmentDetail=(e,t)=>{this.advanceForm.patchValue({installmentType:this.installments[e].noOfMonths,installmentAmtPerMonth:t})},this.saveExpense=()=>{if(this.advanceForm.valid){this.isBeingSubmitted=!0;let e=this.advanceForm.value;e.claimDate=this._util.convertToLocalTime(this.advanceForm.value.claimDate),e.isExpenseCreatedFromWeb=1,console.log(e),this._expLazyService.saveAdvanceDetail(e).subscribe(e=>{this.isBeingSubmitted=!1,console.log(e),this._util.showMessage("Advance request created successfully","close"),this.closeAfterSubmit()},e=>{console.error(e),this.isBeingSubmitted=!1,this._util.showMessage("Oops! something went wrong.","close")})}else this.checkValidation()},this.checkValidation=()=>{if(this.advanceForm.get("advanceTypeName").invalid)return this._util.showMessage("Please choose Advance Category ! ","close");if(this.advanceForm.get("claimedBy").invalid)return this._util.showMessage("Please fill Advance Requesting Person ! ","close");if(this.advanceForm.get("legalEntity").invalid)return this._util.showMessage("Please fill the Legal Entity ! ","close");if(this.advanceForm.get("totalAmount").invalid)return this._util.showMessage("Please fill the Amount ! ","close");if(this.advanceForm.get("currency").invalid)return this._util.showMessage("Please fill the Currency ! ","close");if(this.advanceForm.get("description").invalid)return this._util.showMessage("Please fill the Description ! ","close");if(this.advanceForm.get("approvers").invalid)return this._util.showMessage("Approvers not found ! ","close");if(this.advanceForm.get("workFlowId").invalid)return this._util.showMessage("Workflow not found ! ","close");if(this.advanceForm.get("contextId").invalid)return this._util.showMessage("Attachment not found ! ","close");if("PC"==this.advanceTypeCode||"T"==this.advanceTypeCode){if(this.advanceForm.get("costCenter").invalid)return this._util.showMessage("Please fill the Cost Center ! ","close");if(this.advanceForm.get("department").invalid)return this._util.showMessage("Please fill the Department ! ","close")}if("SA"==this.advanceTypeCode){if(this.advanceForm.get("reasons").invalid)return this._util.showMessage("Please choose the Reason ! ","close");if(this.advanceForm.get("installmentType").invalid)return this._util.showMessage("Please choose the Installment Type ! ","close");if(this.advanceForm.get("installmentAmtPerMonth").invalid)return this._util.showMessage("Please choose the Installment amount per month ! ","close")}},this.closeAfterSubmit=()=>{this.dialogRef.close("refresh")},this.profile=this._auth.getProfile().profile,this.createForm()}ngOnInit(){return Object(a.c)(this,void 0,void 0,(function*(){this.getAllCostCenter(),this.getAdvanceCategoryMaster(),this.defaultLegalEntityDetails(),this.getAdvanceFormFieldConfig(),yield this._expLazyService.getAllLegalEntity().then(e=>{e.data.length>0&&(this.legalEntityList=e.data)});let e=yield this.getExpenseConfig();if(e){let t=JSON.parse(e[0].expense_application_config);null!=t&&(this.isDefaultCostCenter=t.is_default_cost_center,this.isAttachmentVisible=t.is_advance_attachments_visible,1==t.is_disable_claimedby&&(this.isDisableClaimedBy=!0),1==this.isAttachmentVisible&&(this.attachmentMandatoryCategory=t.attachment_mandatory_advance_category),1==this.isDefaultCostCenter&&this.advanceForm.value.claimedBy)&&(yield this.getDepartmentCostCenter(this.advanceForm.value.claimedBy))}}))}getDepartmentCostCenter(e){return Object(a.c)(this,void 0,void 0,(function*(){this._expLazyService.getDepartmentCostCenter(e).subscribe(e=>{if("S"==e.messType){let t=e.data[0].cost_center;this.advanceForm.controls.costCenterCode.patchValue(t);let n=r.filter(this.costCenterList,e=>{if(e.id==t)return e});n.length>0&&this.advanceForm.controls.costCenter.patchValue(n[0])}},e=>{console.error(e)})}))}getAllCostCenter(){return Object(a.c)(this,void 0,void 0,(function*(){yield this._expLazyService.getAllCostCenter().then(e=>{this.costCenterList=e,console.log(this.costCenterList)})}))}getCustomerBillingConfig(){return Object(a.c)(this,void 0,void 0,(function*(){this._expLazyService.getExpenseBillingConfig().subscribe(e=>{this.billingConfig=e.data},e=>{console.error(e)})}))}defaultLegalEntityDetails(){return Object(a.c)(this,void 0,void 0,(function*(){let e=yield this._expLazyService.getDefaultLE();e="string"==typeof e?JSON.parse(e):e,this.defaultLE=JSON.stringify({entity_id:e.entity_id,entity_name:e.entity_name,company_code:e.company_code,entity_currency_code:e.entity_currency_code}),this.defaultCurrency=JSON.stringify({currency_id:e.currency_id,currency_code:e.currency_code,currency_description:e.currency_description}),this.advanceForm.patchValue({legalEntity:this.defaultLE,currency:this.defaultCurrency,legalEntityCode:JSON.parse(this.defaultLE).entity_id})}))}getAdvanceFormFieldConfig(){return Object(a.c)(this,void 0,void 0,(function*(){yield this._expLazyService.getAdvanceFormFieldConfig().subscribe(e=>{this.formFieldConfig=e.data,this.formFieldConfig&&this.formFieldConfig.forEach(e=>{this.fieldConfig[e.field_key]=e})},e=>{console.error(e)})}))}changeInFiles(e){this.advanceForm.controls.contextId.patchValue(e)}getCostCenterOfClaimer(){return Object(a.c)(this,void 0,void 0,(function*(){yield this.getDepartmentCostCenter(this.claimedBy),this.determineWorkflow()}))}itemLegalEntityChange(){return Object(a.c)(this,void 0,void 0,(function*(){this.legalEntity=this.advanceForm.value.legalEntity,this.getEntityCurrency(),yield this.determineWorkflow()}))}changeCostCenter(e){this.advanceForm.controls.costCenter.patchValue(e)}getEntityCurrency(){return Object(a.c)(this,void 0,void 0,(function*(){this.advanceForm.value.legalEntity&&this._expLazyService.getEntityCurrency(this.advanceForm.value.legalEntityCode).subscribe(e=>{"S"==e.messType&&this.advanceForm.controls.currency.patchValue(JSON.stringify(e.data[0]))},e=>{console.error(e)})}))}changeLegalEntity(e){this.advanceForm.controls.legalEntity.patchValue(e),this.getEntityCurrency()}ngOnDestroy(){this.categoryMasterSubscription&&this.categoryMasterSubscription.unsubscribe(),this.determineWorkflowSubscription&&this.determineWorkflowSubscription.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](h.i),i["\u0275\u0275directiveInject"](C.a),i["\u0275\u0275directiveInject"](b.a),i["\u0275\u0275directiveInject"](_.a),i["\u0275\u0275directiveInject"](x.a),i["\u0275\u0275directiveInject"](w.h))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["ng-component"]],outputs:{close:"close"},features:[i["\u0275\u0275ProvidersFeature"]([{provide:v.c,useClass:g.c,deps:[v.f,g.a]},{provide:v.e,useValue:{parse:{dateInput:"DD-MM-YYYY"},display:{dateInput:"DD-MM-YYYY",monthYearLabel:"MMM YYYY"}}}])],decls:71,vars:39,consts:[[1,"container-fluid","pl-2","pr-2","create-advance-styles"],[3,"formGroup"],[1,"row","p-0"],[1,"col-12","p-0"],[1,"row","border-bottom","solid"],[1,"col-11","pt-2","pb-2","d-flex"],["mat-icon-button","",1,"bubble","mt-1"],[1,"iconButton"],[1,"name","my-auto","ml-3"],[1,"col-1","d-flex"],["mat-icon-button","",1,"ml-auto","close-button",3,"click"],[1,"close-Icon"],[1,"row","pt-3"],[1,"col-3x","pl-0","pr-0",2,"border-right","1px solid #d3d3d3"],["class","row pt-3",4,"ngIf"],[1,"col-9","pt-2","pr-0"],[1,"row"],[1,"col-12"],["required","true","formControlName","claimedBy",3,"isAutocomplete","label","readonly"],[1,"row","pt-2"],["required","true","formControlName","legalEntityCode",1,"create-claim-field-inputsearch",3,"placeholder","list","change"],["class","col-6",4,"ngIf"],[1,"col-6","d-flex","align-items-center"],[1,"mr-3","header","my-auto"],[4,"ngIf"],["class","col-6 pt-2",4,"ngIf"],[1,"col-6"],["appearance","outline",1,"create-advance-field"],["matInput","","required","true","formControlName","claimDate",3,"matDatepicker","min"],["matSuffix","",3,"for"],["pickerAdvance",""],["matInput","","type","number","required","true","formControlName","totalAmount","onkeydown","return event.keyCode !== 69",3,"placeholder"],["class","pt-1",4,"ngIf"],["isRequired","true","formControlName","currency",3,"label","optionLabel","optionValue","apiUri"],["class","row",4,"ngIf"],["class","row pt-2 pb-2",4,"ngIf"],["class","col-12 mt-2",4,"ngIf"],[1,"col-12","mt-2"],["matInput","","required","true","formControlName","description",3,"placeholder"],[1,"row","pt-2","pr-3"],[1,"col-12","d-flex","justify-content-end","pr-5"],["class","mr-4",4,"ngIf"],["mat-icon-button","","matTooltip","Create Advance","type","submit",1,"iconbtn","ml-auto","mr-5","mt-1","mb-1",3,"ngStyle","ngClass","disabled","click"],[4,"ngIf","ngIfElse"],["showSubmitSpinner",""],["style","padding: 5px;",4,"ngFor","ngForOf"],[2,"padding","5px"],["mat-raised-button","",3,"ngClass","click"],[1,"claim-icons"],["formControlName","costCenterCode",1,"create-claim-field-inputsearch",3,"placeholder","list","change"],[4,"ngFor","ngForOf"],["content-type","template","placement","top","imgHeight","32px","imgWidth","32px","borderStyle","solid","borderWidth","2px",2,"margin","2px",3,"tooltip","id"],["approverTooltip",""],[1,"row","tooltip-text"],["matInput","","required","true","formControlName","department","readonly","",3,"placeholder"],[1,"col-6","pt-2"],["matInput","","required","true","formControlName","orgPracticeName","readonly","",3,"placeholder"],[1,"pt-1"],[1,"align-middle","mr-0",2,"font-size","21px","color","#b94141"],[2,"font-size","15px","font-weight","500","color","#cf0001"],[1,"ml-2",2,"font-weight","500","font-size","14px","color","#636060"],[1,"row","pt-2","pb-2"],["mat-raised-button","",1,"slide-from-down",3,"ngClass","click"],[2,"font-size","10","font-weight","500"],["formControlName","reasons","required","true",3,"placeholder","list"],[1,"mr-4"],[3,"destinationBucket","routingKey","contextId","allowEdit","change"],["diameter","30",1,"btn-spinner"]],template:function(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275elementStart"](1,"form",1),i["\u0275\u0275elementStart"](2,"div",2),i["\u0275\u0275elementStart"](3,"div",3),i["\u0275\u0275elementStart"](4,"div",4),i["\u0275\u0275elementStart"](5,"div",5),i["\u0275\u0275elementStart"](6,"div",6),i["\u0275\u0275elementStart"](7,"mat-icon",7),i["\u0275\u0275text"](8,"payments"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](9,"span",8),i["\u0275\u0275text"](10,"New Advance"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](11,"div",9),i["\u0275\u0275elementStart"](12,"button",10),i["\u0275\u0275listener"]("click",(function(){return t.closeDialog()})),i["\u0275\u0275elementStart"](13,"mat-icon",11),i["\u0275\u0275text"](14,"close"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](15,"div",12),i["\u0275\u0275elementStart"](16,"div",13),i["\u0275\u0275template"](17,P,3,1,"div",14),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](18,"div",15),i["\u0275\u0275elementStart"](19,"div",16),i["\u0275\u0275elementStart"](20,"div",17),i["\u0275\u0275element"](21,"app-expense-search-user",18),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](22,"div",19),i["\u0275\u0275elementStart"](23,"div",17),i["\u0275\u0275elementStart"](24,"app-input-search",20),i["\u0275\u0275listener"]("change",(function(e){return t.changeLegalEntity(e)})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](25,"div",19),i["\u0275\u0275template"](26,I,2,2,"div",21),i["\u0275\u0275elementStart"](27,"div",22),i["\u0275\u0275elementStart"](28,"p",23),i["\u0275\u0275text"](29,"Approvers"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](30,V,2,1,"ng-container",24),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](31,"div",19),i["\u0275\u0275template"](32,D,5,2,"div",21),i["\u0275\u0275template"](33,L,5,2,"div",25),i["\u0275\u0275elementStart"](34,"div",26),i["\u0275\u0275elementStart"](35,"mat-form-field",27),i["\u0275\u0275elementStart"](36,"mat-label"),i["\u0275\u0275text"](37),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](38,"input",28),i["\u0275\u0275element"](39,"mat-datepicker-toggle",29),i["\u0275\u0275element"](40,"mat-datepicker",null,30),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](42,"div",19),i["\u0275\u0275elementStart"](43,"div",26),i["\u0275\u0275elementStart"](44,"mat-form-field",27),i["\u0275\u0275elementStart"](45,"mat-label"),i["\u0275\u0275text"](46),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](47,"input",31),i["\u0275\u0275template"](48,B,2,0,"mat-error",32),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](49,"div",26),i["\u0275\u0275element"](50,"app-input-search-huge-input",33),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](51,j,8,0,"div",34),i["\u0275\u0275template"](52,R,3,1,"div",35),i["\u0275\u0275elementStart"](53,"div",16),i["\u0275\u0275template"](54,q,2,2,"div",36),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](55,"div",16),i["\u0275\u0275elementStart"](56,"div",37),i["\u0275\u0275elementStart"](57,"mat-form-field",27),i["\u0275\u0275elementStart"](58,"mat-label"),i["\u0275\u0275text"](59),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](60,"input",38),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](61,"div",16),i["\u0275\u0275template"](62,z,5,0,"div",36),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](63,"div",39),i["\u0275\u0275elementStart"](64,"div",40),i["\u0275\u0275elementStart"](65,"div"),i["\u0275\u0275template"](66,W,2,4,"span",41),i["\u0275\u0275elementStart"](67,"button",42),i["\u0275\u0275listener"]("click",(function(){return t.saveExpense()})),i["\u0275\u0275template"](68,H,2,0,"mat-icon",43),i["\u0275\u0275template"](69,Y,1,0,"ng-template",null,44,i["\u0275\u0275templateRefExtractor"]),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275reference"](41),n=i["\u0275\u0275reference"](70);i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("formGroup",t.advanceForm),i["\u0275\u0275advance"](16),i["\u0275\u0275property"]("ngIf",t.advanceTypes.length>0),i["\u0275\u0275advance"](4),i["\u0275\u0275property"]("isAutocomplete",!0)("label",null!=t.fieldConfig&&null!=t.fieldConfig.claimedBy&&t.fieldConfig.claimedBy.field_label?t.fieldConfig.claimedBy.field_label:"Advance Requesting Person")("readonly",t.isDisableClaimedBy),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("placeholder",null!=t.fieldConfig&&null!=t.fieldConfig.legalEntityCode&&t.fieldConfig.legalEntityCode.field_label?t.fieldConfig.legalEntityCode.field_label:"Legal Entity")("list",t.legalEntityList),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf","SA"!=t.advanceTypeCode&&t.costCenterList.length>0),i["\u0275\u0275advance"](4),i["\u0275\u0275property"]("ngIf",t.approvers),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf","SA"!=t.advanceTypeCode),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf","SA"==t.advanceTypeCode),i["\u0275\u0275advance"](4),i["\u0275\u0275textInterpolate"](null!=t.fieldConfig&&null!=t.fieldConfig.claimDate&&t.fieldConfig.claimDate.field_label?t.fieldConfig.claimDate.field_label:"Date of submission"),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("matDatepicker",e)("min",t.minDate),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("for",e),i["\u0275\u0275advance"](7),i["\u0275\u0275textInterpolate"](null!=t.fieldConfig&&null!=t.fieldConfig.totalAmount&&t.fieldConfig.totalAmount.field_label?t.fieldConfig.totalAmount.field_label:"Amount"),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("placeholder",null!=t.fieldConfig&&null!=t.fieldConfig.totalAmount&&t.fieldConfig.totalAmount.field_label?t.fieldConfig.totalAmount.field_label:"Amount"),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null==t.advanceForm.controls.totalAmount.errors?null:t.advanceForm.controls.totalAmount.errors.min),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("label",null!=t.fieldConfig&&null!=t.fieldConfig.currency&&t.fieldConfig.currency.field_label?t.fieldConfig.currency.field_label:"Currency")("optionLabel",i["\u0275\u0275pureFunction0"](34,J))("optionValue","currency_id")("apiUri","/api/exPrimary/currencyMasterData"),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf","SA"==t.advanceForm.value.advanceTypeCode),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf","SA"==t.advanceForm.value.advanceTypeCode&&t.advanceForm.value.totalAmount>0),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf","SA"==t.advanceTypeCode),i["\u0275\u0275advance"](5),i["\u0275\u0275textInterpolate"](null!=t.fieldConfig&&null!=t.fieldConfig.description&&t.fieldConfig.description.field_label?t.fieldConfig.description.field_label:"Description"),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("placeholder",null!=t.fieldConfig&&null!=t.fieldConfig.description&&t.fieldConfig.description.field_label?t.fieldConfig.description.field_label:"Description"),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf","SA"==t.advanceTypeCode),i["\u0275\u0275advance"](4),i["\u0275\u0275property"]("ngIf",1==t.isAttachmentVisible),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngStyle",i["\u0275\u0275pureFunction1"](35,X,0==t.isBeingSubmitted?"#cf0001":"#f3f3f3"))("ngClass",i["\u0275\u0275pureFunction1"](37,$,t.isBeingSubmitted||t.isBeingWorkflowDetermined))("disabled",t.isBeingSubmitted||t.isBeingWorkflowDetermined),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",0==t.isBeingSubmitted)("ngIfElse",n)}},directives:[h.J,h.w,h.n,c.a,s.a,l.NgIf,S.a,h.F,h.v,h.l,E.a,m.c,m.g,p.b,h.e,u.g,u.i,m.i,u.f,h.A,O.a,d.a,l.NgStyle,l.NgClass,l.NgForOf,M.a,f.a,m.b,F.a,y.c],styles:[".create-advance-styles[_ngcontent-%COMP%]{background-image:url(expense_bg.64ac4aa5cba931ab8a07.png);background-size:190px 158px;min-height:92%;background-repeat:no-repeat;overflow-y:hidden!important;background-position:3% 103%}.create-advance-styles[_ngcontent-%COMP%]   .restict-cursor[_ngcontent-%COMP%]{cursor:not-allowed}.create-advance-styles[_ngcontent-%COMP%]   .name[_ngcontent-%COMP%]{color:#cf0001;font-weight:500;font-size:14px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.create-advance-styles[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{color:#5f5f5f;font-size:12px!important}.create-advance-styles[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]{display:flex;line-height:26px;border-radius:50%;cursor:context-menu!important;width:26px;height:26px;background-color:#fbfbfb;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.create-advance-styles[_ngcontent-%COMP%]   .bubble[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#cf0001;font-size:18px;margin-top:3px!important;margin-left:4px!important}.create-advance-styles[_ngcontent-%COMP%]   .iconbtn[_ngcontent-%COMP%]{background-color:#c92020;color:#fff;padding:0;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.create-advance-styles[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.create-advance-styles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}.create-advance-styles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff;font-weight:400;margin-bottom:6%;width:11rem;text-align:left;padding:2px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.create-advance-styles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%]   .claim-icons[_ngcontent-%COMP%]{font-size:20px;margin-right:6px;margin-left:11px;color:#fff}.create-advance-styles[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]{background-color:#f3f3f3;color:#1a1a1a;margin-bottom:6%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;font-weight:400;text-align:left;width:11rem;padding:2px}.create-advance-styles[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]   .claim-icons[_ngcontent-%COMP%]{font-size:20px;margin-right:6px;margin-left:11px;color:#343434}.create-advance-styles[_ngcontent-%COMP%]   .btn-spinner[_ngcontent-%COMP%]{margin-left:4px}.create-advance-styles[_ngcontent-%COMP%]   .salary-btn-active[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff}.create-advance-styles[_ngcontent-%COMP%]   .salary-btn-active[_ngcontent-%COMP%], .create-advance-styles[_ngcontent-%COMP%]   .salary-btn-not-active[_ngcontent-%COMP%]{font-weight:400;margin-bottom:1%;width:9rem;height:5rem;text-align:center;padding:2px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.create-advance-styles[_ngcontent-%COMP%]   .salary-btn-not-active[_ngcontent-%COMP%]{background-color:#f3f3f3;color:#636060}.create-advance-styles[_ngcontent-%COMP%]   .col-3x[_ngcontent-%COMP%]{flex:0 0 20%;max-width:25%}.create-advance-styles[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}.create-advance-styles[_ngcontent-%COMP%]   .title-table[_ngcontent-%COMP%]{font-size:14px;color:#1a1a1a}.create-advance-styles[_ngcontent-%COMP%]   .create-advance-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%!important}.create-advance-styles[_ngcontent-%COMP%]   .create-claim-field-inputsearch[_ngcontent-%COMP%]{font-size:13px!important}.create-advance-styles[_ngcontent-%COMP%]   .create-claim-field-inputsearch[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]{width:70%!important}"]}),e})()}}]);