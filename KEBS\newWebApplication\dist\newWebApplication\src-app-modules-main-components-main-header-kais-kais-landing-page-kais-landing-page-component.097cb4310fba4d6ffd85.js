(window.webpackJsonp=window.webpackJsonp||[]).push([[964],{"2Pgj":function(t,e,n){"use strict";var i;n.d(e,"a",(function(){return o}));var r=new Uint8Array(16);function o(){if(!i&&!(i="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||"undefined"!=typeof msCrypto&&"function"==typeof msCrypto.getRandomValues&&msCrypto.getRandomValues.bind(msCrypto)))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return i(r)}},"7Cbv":function(t,e,n){"use strict";var i=n("2Pgj"),r=n("WM9j");e.a=function(t,e,n){var o=(t=t||{}).random||(t.rng||i.a)();if(o[6]=15&o[6]|64,o[8]=63&o[8]|128,e){n=n||0;for(var a=0;a<16;++a)e[n+a]=o[a];return e}return Object(r.a)(o)}},BuRe:function(t,e,n){"use strict";var i=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;e.a=function(t){return"string"==typeof t&&i.test(t)}},L3PF:function(t,e,n){"use strict";n.r(e),n.d(e,"KaisLandingPageComponent",(function(){return At}));var i=n("mrSG"),r=n("XNiG"),o=n("1G5W"),a=n("rDax"),c=n("0IaG"),s=n("wd/R"),l=n.n(s),d=n("xG9w"),p=n("fXoL");let h=(()=>{class t{transform(t,e){return e?t.map(t=>{if(t.prompts&&Array.isArray(t.prompts)){const n=t.prompts.filter(t=>t.prompt.toLowerCase().includes(e.toLowerCase()));return Object.assign(Object.assign({},t),{prompts:n})}return Object.assign(Object.assign({},t),{prompts:[]})}).filter(t=>t.prompts.length>0):t}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275pipe=p["\u0275\u0275definePipe"]({name:"filterPromptLibrary",type:t,pure:!0}),t})();var g=n("STbY"),m=n("7Cbv"),C=n("dra5"),u=n("XXEo"),f=n("XNFG"),y=n("WYLN"),x=n("jhN1"),_=n("ofXK"),P=n("lVl8"),v=n("3Pt+"),M=n("NFeN"),O=n("dlKe"),b=n("Qu3c"),w=n("f0Cb"),S=n("UXJo"),T=n("me71"),E=n("ZzAl"),D=n("AFgX"),k=n("XTQC");const I=["chatScrollContainer"];function H(t,e){if(1&t){const t=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",19),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](t),p["\u0275\u0275nextContext"](2).openHistory()})),p["\u0275\u0275namespaceSVG"](),p["\u0275\u0275elementStart"](1,"svg",15),p["\u0275\u0275element"](2,"path",20),p["\u0275\u0275element"](3,"path",21),p["\u0275\u0275element"](4,"path",22),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}}function L(t,e){if(1&t){const t=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",9),p["\u0275\u0275elementStart"](1,"div",10),p["\u0275\u0275element"](2,"img",11),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](3,"div",12),p["\u0275\u0275template"](4,H,5,0,"div",13),p["\u0275\u0275elementStart"](5,"div",14),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](t),p["\u0275\u0275nextContext"]().openNewChat()})),p["\u0275\u0275namespaceSVG"](),p["\u0275\u0275elementStart"](6,"svg",15),p["\u0275\u0275element"](7,"path",16),p["\u0275\u0275element"](8,"path",17),p["\u0275\u0275element"](9,"path",18),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&t){const t=p["\u0275\u0275nextContext"]();p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("src",null==t.data?null:t.data.aiThemeConfig["AI-ICON-SHORT"],p["\u0275\u0275sanitizeUrl"]),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngIf",!t.isHistoryVisible)}}function V(t,e){if(1&t){const t=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",43),p["\u0275\u0275namespaceSVG"](),p["\u0275\u0275elementStart"](1,"svg",44),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](t),p["\u0275\u0275nextContext"](2).onEnterSearchHistory()})),p["\u0275\u0275elementStart"](2,"mask",45),p["\u0275\u0275element"](3,"rect",46),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](4,"g",47),p["\u0275\u0275element"](5,"path",48),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}}function F(t,e){if(1&t){const t=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"mat-icon",49),p["\u0275\u0275listener"]("click",(function(){p["\u0275\u0275restoreView"](t);const e=p["\u0275\u0275nextContext"](2);return e.historySearchParams="",e.onEnterSearchHistory()})),p["\u0275\u0275text"](1," close "),p["\u0275\u0275elementEnd"]()}}function j(t,e){1&t&&p["\u0275\u0275element"](0,"div",52)}function z(t,e){if(1&t&&(p["\u0275\u0275elementStart"](0,"div",50),p["\u0275\u0275template"](1,j,1,0,"div",51),p["\u0275\u0275elementEnd"]()),2&t){const t=p["\u0275\u0275nextContext"](2);p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngForOf",t.repeatArray)}}function R(t,e){1&t&&(p["\u0275\u0275elementStart"](0,"div",53),p["\u0275\u0275text"](1," -No History- "),p["\u0275\u0275elementEnd"]())}const A=function(t){return{"pointer-events":t}};function B(t,e){if(1&t){const t=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",65),p["\u0275\u0275listener"]("click",(function(){p["\u0275\u0275restoreView"](t);const e=p["\u0275\u0275nextContext"]().$implicit;return p["\u0275\u0275nextContext"](4).setSelectedHistoryThread(null==e?null:e.thread_id)})),p["\u0275\u0275text"](1),p["\u0275\u0275elementEnd"]()}if(2&t){const t=p["\u0275\u0275nextContext"]().$implicit,e=p["\u0275\u0275nextContext"](4);p["\u0275\u0275property"]("ngClass",(null==t?null:t.thread_id)==e.historySelectedThreadId?"selected-history-text":"history-text")("matTooltip",null==t?null:t.heading)("ngStyle",p["\u0275\u0275pureFunction1"](4,A,e.isThreadLoading||e.isPromptApiInProgress?"none":"")),p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" ",(null==t?null:t.heading)||"New Chat"," ")}}function G(t,e){if(1&t){const t=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",66),p["\u0275\u0275elementStart"](1,"input",67),p["\u0275\u0275listener"]("ngModelChange",(function(e){return p["\u0275\u0275restoreView"](t),p["\u0275\u0275nextContext"]().$implicit.heading=e}))("keydown.enter",(function(){p["\u0275\u0275restoreView"](t);const e=p["\u0275\u0275nextContext"]().$implicit;return p["\u0275\u0275nextContext"](4).onRenameThread(null==e?null:e._id,null==e?null:e.heading,null==e?null:e.thread_id)})),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&t){const t=p["\u0275\u0275nextContext"](),e=t.index,n=t.$implicit;p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("id","#pinnedHistoryInputSearch-"+e)("ngModel",n.heading)}}function Z(t,e){if(1&t){const t=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",68),p["\u0275\u0275listener"]("click",(function(){p["\u0275\u0275restoreView"](t);const e=p["\u0275\u0275nextContext"](),n=e.$implicit,i=e.index;return p["\u0275\u0275nextContext"](4).setSelectedHistoryThreadData(n,-1,i)})),p["\u0275\u0275namespaceSVG"](),p["\u0275\u0275elementStart"](1,"svg",69),p["\u0275\u0275elementStart"](2,"mask",70),p["\u0275\u0275element"](3,"rect",71),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](4,"g",72),p["\u0275\u0275element"](5,"path",73),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&t){p["\u0275\u0275nextContext"](5);const t=p["\u0275\u0275reference"](9);p["\u0275\u0275property"]("matMenuTriggerFor",t)}}function U(t,e){if(1&t&&(p["\u0275\u0275elementStart"](0,"div",61),p["\u0275\u0275template"](1,B,2,6,"div",62),p["\u0275\u0275template"](2,G,2,2,"div",63),p["\u0275\u0275template"](3,Z,6,1,"div",64),p["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit,n=p["\u0275\u0275nextContext"](4);p["\u0275\u0275property"]("ngClass",(null==t?null:t.thread_id)==n.historySelectedThreadId?"thread-selected":""),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!(null!=t&&t.isRenameInProgress)),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",null==t?null:t.isRenameInProgress),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!(null!=t&&t.isRenameInProgress))}}function N(t,e){if(1&t&&(p["\u0275\u0275elementStart"](0,"div",58),p["\u0275\u0275elementStart"](1,"div",59),p["\u0275\u0275text"](2," Favorites "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](3,U,4,4,"div",60),p["\u0275\u0275elementEnd"]()),2&t){const t=p["\u0275\u0275nextContext"](3);p["\u0275\u0275advance"](3),p["\u0275\u0275property"]("ngForOf",t.pinnedHistoryData)}}function $(t,e){1&t&&p["\u0275\u0275element"](0,"mat-divider",74)}function W(t,e){if(1&t){const t=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",65),p["\u0275\u0275listener"]("click",(function(){p["\u0275\u0275restoreView"](t);const e=p["\u0275\u0275nextContext"]().$implicit;return p["\u0275\u0275nextContext"](4).setSelectedHistoryThread(null==e?null:e.thread_id)})),p["\u0275\u0275text"](1),p["\u0275\u0275elementEnd"]()}if(2&t){const t=p["\u0275\u0275nextContext"]().$implicit,e=p["\u0275\u0275nextContext"](4);p["\u0275\u0275property"]("ngClass",(null==t?null:t.thread_id)==e.historySelectedThreadId?"selected-history-text":"history-text")("matTooltip",null==t?null:t.heading)("ngStyle",p["\u0275\u0275pureFunction1"](4,A,e.isThreadLoading||e.isPromptApiInProgress?"none":"")),p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" ",(null==t?null:t.heading)||"New Chat"," ")}}function X(t,e){if(1&t){const t=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",66),p["\u0275\u0275elementStart"](1,"input",67),p["\u0275\u0275listener"]("ngModelChange",(function(e){return p["\u0275\u0275restoreView"](t),p["\u0275\u0275nextContext"]().$implicit.heading=e}))("keydown.enter",(function(){p["\u0275\u0275restoreView"](t);const e=p["\u0275\u0275nextContext"]().$implicit;return p["\u0275\u0275nextContext"](4).onRenameThread(null==e?null:e._id,null==e?null:e.heading,null==e?null:e.thread_id)})),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&t){const t=p["\u0275\u0275nextContext"](),e=t.index,n=t.$implicit,i=p["\u0275\u0275nextContext"]().index;p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("id","#historyInputSearch-"+i+"-"+e)("ngModel",n.heading)}}function Y(t,e){if(1&t){const t=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",68),p["\u0275\u0275listener"]("click",(function(){p["\u0275\u0275restoreView"](t);const e=p["\u0275\u0275nextContext"](),n=e.$implicit,i=e.index,r=p["\u0275\u0275nextContext"]().index;return p["\u0275\u0275nextContext"](3).setSelectedHistoryThreadData(n,r,i)})),p["\u0275\u0275namespaceSVG"](),p["\u0275\u0275elementStart"](1,"svg",69),p["\u0275\u0275elementStart"](2,"mask",70),p["\u0275\u0275element"](3,"rect",71),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](4,"g",72),p["\u0275\u0275element"](5,"path",73),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&t){p["\u0275\u0275nextContext"](5);const t=p["\u0275\u0275reference"](9);p["\u0275\u0275property"]("matMenuTriggerFor",t)}}function Q(t,e){if(1&t&&(p["\u0275\u0275elementStart"](0,"div",61),p["\u0275\u0275template"](1,W,2,6,"div",62),p["\u0275\u0275template"](2,X,2,2,"div",63),p["\u0275\u0275template"](3,Y,6,1,"div",64),p["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit,n=p["\u0275\u0275nextContext"](4);p["\u0275\u0275property"]("ngClass",(null==t?null:t.thread_id)==n.historySelectedThreadId?"thread-selected":""),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!(null!=t&&t.isRenameInProgress)),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",null==t?null:t.isRenameInProgress),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!(null!=t&&t.isRenameInProgress))}}function q(t,e){1&t&&p["\u0275\u0275element"](0,"mat-divider",74)}function K(t,e){if(1&t&&(p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275elementStart"](1,"div",58),p["\u0275\u0275elementStart"](2,"div",59),p["\u0275\u0275text"](3),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](4,Q,4,4,"div",60),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](5,q,1,0,"mat-divider",56),p["\u0275\u0275elementContainerEnd"]()),2&t){const t=e.$implicit,n=e.index,i=p["\u0275\u0275nextContext"](3);p["\u0275\u0275advance"](3),p["\u0275\u0275textInterpolate1"](" ",t," "),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngForOf",i.historyGroupedData[t]),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",i.historyGroupedDateData.length-1!=n)}}function J(t,e){if(1&t){const t=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",54),p["\u0275\u0275listener"]("scrolled",(function(){return p["\u0275\u0275restoreView"](t),p["\u0275\u0275nextContext"](2).onHistoryDataScroll()})),p["\u0275\u0275template"](1,N,4,1,"div",55),p["\u0275\u0275template"](2,$,1,0,"mat-divider",56),p["\u0275\u0275template"](3,K,6,3,"ng-container",57),p["\u0275\u0275elementEnd"]()}if(2&t){const t=p["\u0275\u0275nextContext"](2);p["\u0275\u0275property"]("infiniteScrollDistance",3)("infiniteScrollThrottle",100)("scrollWindow",!1),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",null==t.pinnedHistoryData?null:t.pinnedHistoryData.length),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",null==t.pinnedHistoryData?null:t.pinnedHistoryData.length),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngForOf",t.historyGroupedDateData)}}function tt(t,e){if(1&t){const t=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",23),p["\u0275\u0275elementStart"](1,"div",24),p["\u0275\u0275elementStart"](2,"div"),p["\u0275\u0275element"](3,"img",25),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](4,"div",26),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](t),p["\u0275\u0275nextContext"]().closeHistory()})),p["\u0275\u0275namespaceSVG"](),p["\u0275\u0275elementStart"](5,"svg",27),p["\u0275\u0275element"](6,"path",28),p["\u0275\u0275element"](7,"path",29),p["\u0275\u0275element"](8,"path",30),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275namespaceHTML"](),p["\u0275\u0275elementStart"](9,"div",31),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](t),p["\u0275\u0275nextContext"]().openNewChat()})),p["\u0275\u0275elementStart"](10,"button",32),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](t),p["\u0275\u0275nextContext"]().openNewChat()})),p["\u0275\u0275namespaceSVG"](),p["\u0275\u0275elementStart"](11,"svg",27),p["\u0275\u0275element"](12,"path",33),p["\u0275\u0275elementEnd"](),p["\u0275\u0275namespaceHTML"](),p["\u0275\u0275elementStart"](13,"span",34),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](t),p["\u0275\u0275nextContext"]().openNewChat()})),p["\u0275\u0275text"](14,"New Chat"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](15,"div",35),p["\u0275\u0275elementStart"](16,"div",36),p["\u0275\u0275elementStart"](17,"input",37),p["\u0275\u0275listener"]("ngModelChange",(function(e){return p["\u0275\u0275restoreView"](t),p["\u0275\u0275nextContext"]().historySearchParams=e}))("keydown.enter",(function(){return p["\u0275\u0275restoreView"](t),p["\u0275\u0275nextContext"]().onEnterSearchHistory()})),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](18,V,6,0,"div",38),p["\u0275\u0275template"](19,F,2,0,"mat-icon",39),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](20,z,2,1,"div",40),p["\u0275\u0275template"](21,R,2,0,"div",41),p["\u0275\u0275template"](22,J,4,6,"div",42),p["\u0275\u0275elementEnd"]()}if(2&t){const t=p["\u0275\u0275nextContext"]();p["\u0275\u0275advance"](3),p["\u0275\u0275property"]("src",null==t.data?null:t.data.aiThemeConfig["AI-ICON"],p["\u0275\u0275sanitizeUrl"]),p["\u0275\u0275advance"](14),p["\u0275\u0275property"]("ngModel",t.historySearchParams),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!t.historySearchParams),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",t.historySearchParams),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",t.isHistoryLoading),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!(t.historyGroupedDateData&&0!=t.historyGroupedDateData.length||t.pinnedHistoryData&&0!=t.pinnedHistoryData.length||t.isHistoryLoading)),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",(t.historyGroupedDateData&&(null==t.historyGroupedDateData?null:t.historyGroupedDateData.length)>0||t.pinnedHistoryData&&(null==t.pinnedHistoryData?null:t.pinnedHistoryData.length)>0)&&!t.isHistoryLoading)}}function et(t,e){if(1&t&&(p["\u0275\u0275elementStart"](0,"div",78),p["\u0275\u0275elementStart"](1,"div",79),p["\u0275\u0275elementStart"](2,"div",80),p["\u0275\u0275text"](3),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](4,"div",81),p["\u0275\u0275text"](5),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&t){const t=p["\u0275\u0275nextContext"](2);p["\u0275\u0275advance"](1),p["\u0275\u0275classProp"]("slide",t.isSliding),p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"](" ",t.threadTexts[t.currentThreadLoadTextIndex].text," "),p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate1"](" ",t.threadTexts[t.currentThreadLoadTextIndex].sub_text," ")}}function nt(t,e){if(1&t&&(p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275elementStart"](1,"div",75),p["\u0275\u0275element"](2,"img",76),p["\u0275\u0275template"](3,et,6,4,"div",77),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementContainerEnd"]()),2&t){const t=p["\u0275\u0275nextContext"]();p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("src",null==t.data?null:t.data.aiThemeConfig["AI-MAIN-LOADER"],p["\u0275\u0275sanitizeUrl"]),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!t.isLoading)}}function it(t,e){if(1&t){const t=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275namespaceSVG"](),p["\u0275\u0275elementStart"](0,"svg",102),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](t),p["\u0275\u0275nextContext"](3).openPromptLibrary()})),p["\u0275\u0275element"](1,"path",103),p["\u0275\u0275elementStart"](2,"defs"),p["\u0275\u0275elementStart"](3,"linearGradient",104),p["\u0275\u0275element"](4,"stop",105),p["\u0275\u0275element"](5,"stop",106),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}}function rt(t,e){if(1&t){const t=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275namespaceSVG"](),p["\u0275\u0275elementStart"](0,"svg",107),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](t),p["\u0275\u0275nextContext"](3).openNewChat()})),p["\u0275\u0275elementStart"](1,"mask",108),p["\u0275\u0275element"](2,"rect",109),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](3,"g",110),p["\u0275\u0275element"](4,"path",111),p["\u0275\u0275elementEnd"](),p["\u0275\u0275element"](5,"line",112),p["\u0275\u0275elementStart"](6,"defs"),p["\u0275\u0275elementStart"](7,"linearGradient",113),p["\u0275\u0275element"](8,"stop",105),p["\u0275\u0275element"](9,"stop",106),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](10,"linearGradient",114),p["\u0275\u0275element"](11,"stop",105),p["\u0275\u0275element"](12,"stop",106),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}}function ot(t,e){if(1&t&&(p["\u0275\u0275elementStart"](0,"div",99),p["\u0275\u0275template"](1,it,6,0,"svg",100),p["\u0275\u0275template"](2,rt,13,0,"svg",101),p["\u0275\u0275elementEnd"]()),2&t){const t=p["\u0275\u0275nextContext"](2);p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!t.isPromptLibraryVisible),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",t.isPromptLibraryVisible)}}function at(t,e){if(1&t&&(p["\u0275\u0275namespaceSVG"](),p["\u0275\u0275namespaceHTML"](),p["\u0275\u0275elementStart"](0,"div",115),p["\u0275\u0275elementStart"](1,"div",116),p["\u0275\u0275element"](2,"img",117),p["\u0275\u0275elementStart"](3,"div",118),p["\u0275\u0275elementStart"](4,"div",119),p["\u0275\u0275text"](5),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](6,"div",120),p["\u0275\u0275text"](7),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&t){const t=p["\u0275\u0275nextContext"](2);p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("src",null==t.data?null:t.data.aiThemeConfig["AI-ICON-SHORT"],p["\u0275\u0275sanitizeUrl"]),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngClass",t.dialogExpanded?"header-content":""),p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" ",t.getGreetingWithName(null==t.data?null:t.data.aiThemeConfig["WELCOME-TEXT-001"],null==t.profile?null:t.profile.name)," "),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngClass",t.dialogExpanded?"header-content":""),p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" ",null==t.data?null:t.data.aiThemeConfig["WELCOME-TEXT-002"]," ")}}function ct(t,e){if(1&t){const t=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",130),p["\u0275\u0275listener"]("click",(function(e){p["\u0275\u0275restoreView"](t);const n=p["\u0275\u0275nextContext"](3);return n.onSearchPrompt(e,n.promptSearchParams)})),p["\u0275\u0275elementStart"](1,"span",43),p["\u0275\u0275namespaceSVG"](),p["\u0275\u0275elementStart"](2,"svg",131),p["\u0275\u0275element"](3,"rect",132),p["\u0275\u0275element"](4,"path",133),p["\u0275\u0275element"](5,"path",134),p["\u0275\u0275elementStart"](6,"defs"),p["\u0275\u0275elementStart"](7,"linearGradient",135),p["\u0275\u0275element"](8,"stop",105),p["\u0275\u0275element"](9,"stop",106),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}}function st(t,e){1&t&&(p["\u0275\u0275elementStart"](0,"div",136),p["\u0275\u0275namespaceSVG"](),p["\u0275\u0275elementStart"](1,"svg",131),p["\u0275\u0275element"](2,"rect",137),p["\u0275\u0275element"](3,"path",133),p["\u0275\u0275element"](4,"path",134),p["\u0275\u0275elementStart"](5,"defs"),p["\u0275\u0275elementStart"](6,"linearGradient",135),p["\u0275\u0275element"](7,"stop",105),p["\u0275\u0275element"](8,"stop",106),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]())}function lt(t,e){if(1&t){const t=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275namespaceSVG"](),p["\u0275\u0275namespaceHTML"](),p["\u0275\u0275elementStart"](0,"div",121),p["\u0275\u0275element"](1,"div",122),p["\u0275\u0275elementStart"](2,"div",123),p["\u0275\u0275elementStart"](3,"div",124),p["\u0275\u0275elementStart"](4,"div",125),p["\u0275\u0275elementStart"](5,"textarea",126,127),p["\u0275\u0275listener"]("blur",(function(){return p["\u0275\u0275restoreView"](t),p["\u0275\u0275nextContext"](2).clearSearchInput()}))("ngModelChange",(function(e){return p["\u0275\u0275restoreView"](t),p["\u0275\u0275nextContext"](2).promptSearchParams=e}))("keydown.enter",(function(e){p["\u0275\u0275restoreView"](t);const n=p["\u0275\u0275nextContext"](2);return n.onSearchPrompt(e,n.promptSearchParams)}))("input",(function(){p["\u0275\u0275restoreView"](t);const e=p["\u0275\u0275reference"](6),n=p["\u0275\u0275nextContext"](2);return n.setTypingTrue(),n.adjustTextareaHeight(e)})),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](7,ct,10,0,"div",128),p["\u0275\u0275template"](8,st,9,0,"div",129),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275element"](9,"div",122),p["\u0275\u0275elementEnd"]()}if(2&t){const t=p["\u0275\u0275nextContext"](2);p["\u0275\u0275advance"](5),p["\u0275\u0275property"]("ngModel",t.promptSearchParams),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngIf",!t.isPromptApiInProgress&&t.promptSearchParams&&t.promptSearchParams.length>0),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",t.isPromptApiInProgress||!t.promptSearchParams||0==t.promptSearchParams.length)}}function dt(t,e){if(1&t){const t=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275namespaceSVG"](),p["\u0275\u0275elementStart"](0,"svg",166),p["\u0275\u0275listener"]("click",(function(){p["\u0275\u0275restoreView"](t);const e=p["\u0275\u0275nextContext"](3);return e.promptLibrarySearchParams="",e.onSearchParamsChange()})),p["\u0275\u0275elementStart"](1,"mask",88),p["\u0275\u0275element"](2,"rect",167),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](3,"g",90),p["\u0275\u0275element"](4,"path",168),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}}const pt=function(t){return{"selected-category-list":t}},ht=function(t){return{"selected-count":t}};function gt(t,e){if(1&t){const t=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275elementStart"](1,"div",169),p["\u0275\u0275listener"]("click",(function(){p["\u0275\u0275restoreView"](t);const n=e.index,i=e.$implicit,r=p["\u0275\u0275nextContext"](3);return r.setPromptLibraryIndex(n),r.setPromptLibraryCategory(i.name)})),p["\u0275\u0275namespaceSVG"](),p["\u0275\u0275elementStart"](2,"svg",170),p["\u0275\u0275element"](3,"path",171),p["\u0275\u0275elementEnd"](),p["\u0275\u0275namespaceHTML"](),p["\u0275\u0275elementStart"](4,"div",172),p["\u0275\u0275text"](5),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](6,"div",173),p["\u0275\u0275text"](7),p["\u0275\u0275pipe"](8,"filterPromptLibrary"),p["\u0275\u0275pipe"](9,"filterPromptLibrary"),p["\u0275\u0275pipe"](10,"filterPromptLibrary"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementContainerEnd"]()}if(2&t){const t=e.$implicit,n=e.index,i=p["\u0275\u0275nextContext"](3);p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngClass",p["\u0275\u0275pureFunction1"](14,pt,i.currentSelectedPromptLibraryCategory==t.name)),p["\u0275\u0275advance"](3),p["\u0275\u0275property"]("matTooltip",t.name),p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" ",t.name," "),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngClass",p["\u0275\u0275pureFunction1"](16,ht,i.currentSelectedPromptLibraryCategory==t.name)),p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" ",p["\u0275\u0275pipeBind2"](8,5,i.promptLibraryData,i.promptLibrarySearchParams)[n].prompts.length>9?p["\u0275\u0275pipeBind2"](9,8,i.promptLibraryData,i.promptLibrarySearchParams)[n].prompts.length:"0"+p["\u0275\u0275pipeBind2"](10,11,i.promptLibraryData,i.promptLibrarySearchParams)[n].prompts.length," ")}}function mt(t,e){if(1&t){const t=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",176),p["\u0275\u0275listener"]("click",(function(){p["\u0275\u0275restoreView"](t);const n=e.$implicit;return p["\u0275\u0275nextContext"](4).copyPromptToClipboard(n)})),p["\u0275\u0275element"](1,"div",177),p["\u0275\u0275elementStart"](2,"div",178),p["\u0275\u0275element"](3,"img",179),p["\u0275\u0275text"](4),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&t){const t=e.$implicit,n=p["\u0275\u0275nextContext"](4);p["\u0275\u0275property"]("cdkCopyToClipboard",(null==t?null:t.prompt)||""),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("innerHTML",n.highlightPromptKey(t.prompt),p["\u0275\u0275sanitizeHtml"]),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("src","https://assets.kebs.app/document-copy.png",p["\u0275\u0275sanitizeUrl"]),p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" ",null!=t&&t.isCopyInProgress?"Copied!":"Copy"," ")}}function Ct(t,e){if(1&t&&(p["\u0275\u0275elementStart"](0,"div",174),p["\u0275\u0275template"](1,mt,5,4,"div",175),p["\u0275\u0275pipe"](2,"filterPromptLibrary"),p["\u0275\u0275elementEnd"]()),2&t){const t=p["\u0275\u0275nextContext"](3);p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngForOf",p["\u0275\u0275pipeBind2"](2,1,t.promptLibraryData,t.promptLibrarySearchParams)[t.currentSelectedPromptLibraryCategoryIndex].prompts)}}function ut(t,e){1&t&&(p["\u0275\u0275elementStart"](0,"div",180),p["\u0275\u0275text"](1," No Categories Found! "),p["\u0275\u0275elementEnd"]())}function ft(t,e){if(1&t){const t=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",181),p["\u0275\u0275listener"]("click",(function(){p["\u0275\u0275restoreView"](t);const e=p["\u0275\u0275nextContext"](3);return e.onSearchPromptFromLibrary(e.promptSearchParams)})),p["\u0275\u0275namespaceSVG"](),p["\u0275\u0275elementStart"](1,"svg",131),p["\u0275\u0275element"](2,"rect",132),p["\u0275\u0275element"](3,"path",133),p["\u0275\u0275element"](4,"path",134),p["\u0275\u0275elementStart"](5,"defs"),p["\u0275\u0275elementStart"](6,"linearGradient",135),p["\u0275\u0275element"](7,"stop",105),p["\u0275\u0275element"](8,"stop",106),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}}function yt(t,e){1&t&&(p["\u0275\u0275elementStart"](0,"div",182),p["\u0275\u0275namespaceSVG"](),p["\u0275\u0275elementStart"](1,"svg",131),p["\u0275\u0275element"](2,"rect",137),p["\u0275\u0275element"](3,"path",133),p["\u0275\u0275element"](4,"path",134),p["\u0275\u0275elementStart"](5,"defs"),p["\u0275\u0275elementStart"](6,"linearGradient",135),p["\u0275\u0275element"](7,"stop",105),p["\u0275\u0275element"](8,"stop",106),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]())}function xt(t,e){if(1&t){const t=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275namespaceSVG"](),p["\u0275\u0275namespaceHTML"](),p["\u0275\u0275elementStart"](0,"div",138),p["\u0275\u0275elementStart"](1,"div",24),p["\u0275\u0275elementStart"](2,"div",139),p["\u0275\u0275elementStart"](3,"mat-icon",140),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](t),p["\u0275\u0275nextContext"](2).openNewChat()})),p["\u0275\u0275text"](4,"arrow_back"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](5,"div",141),p["\u0275\u0275text"](6,"AI Prompt Library"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](7,"div",36),p["\u0275\u0275namespaceSVG"](),p["\u0275\u0275elementStart"](8,"svg",142),p["\u0275\u0275elementStart"](9,"g",143),p["\u0275\u0275element"](10,"path",144),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](11,"defs"),p["\u0275\u0275elementStart"](12,"clipPath",145),p["\u0275\u0275element"](13,"rect",146),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275namespaceHTML"](),p["\u0275\u0275elementStart"](14,"input",147),p["\u0275\u0275listener"]("ngModelChange",(function(e){return p["\u0275\u0275restoreView"](t),p["\u0275\u0275nextContext"](2).promptLibrarySearchParams=e}))("ngModelChange",(function(){return p["\u0275\u0275restoreView"](t),p["\u0275\u0275nextContext"](2).onSearchParamsChange()})),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](15,dt,5,0,"svg",148),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](16,"div",149),p["\u0275\u0275elementStart"](17,"div",150),p["\u0275\u0275elementStart"](18,"div",24),p["\u0275\u0275namespaceSVG"](),p["\u0275\u0275elementStart"](19,"svg",151),p["\u0275\u0275element"](20,"path",152),p["\u0275\u0275element"](21,"path",153),p["\u0275\u0275element"](22,"path",154),p["\u0275\u0275element"](23,"path",155),p["\u0275\u0275elementEnd"](),p["\u0275\u0275namespaceHTML"](),p["\u0275\u0275elementStart"](24,"div",156),p["\u0275\u0275text"](25,"Categories"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](26,"div",157),p["\u0275\u0275text"](27),p["\u0275\u0275pipe"](28,"filterPromptLibrary"),p["\u0275\u0275pipe"](29,"filterPromptLibrary"),p["\u0275\u0275pipe"](30,"filterPromptLibrary"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](31,"div",158),p["\u0275\u0275template"](32,gt,11,18,"ng-container",57),p["\u0275\u0275pipe"](33,"filterPromptLibrary"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](34,Ct,3,4,"div",159),p["\u0275\u0275pipe"](35,"filterPromptLibrary"),p["\u0275\u0275template"](36,ut,2,0,"div",160),p["\u0275\u0275pipe"](37,"filterPromptLibrary"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](38,"div",161),p["\u0275\u0275elementStart"](39,"div",162),p["\u0275\u0275listener"]("mouseenter",(function(){return p["\u0275\u0275restoreView"](t),p["\u0275\u0275nextContext"](2).setTypingTrue()})),p["\u0275\u0275elementStart"](40,"textarea",163),p["\u0275\u0275listener"]("blur",(function(){return p["\u0275\u0275restoreView"](t),p["\u0275\u0275nextContext"](2).clearSearchInput()}))("ngModelChange",(function(e){return p["\u0275\u0275restoreView"](t),p["\u0275\u0275nextContext"](2).promptSearchParams=e}))("keydown.enter",(function(e){p["\u0275\u0275restoreView"](t);const n=p["\u0275\u0275nextContext"](2);return n.onSearchPromptFromLibrary(n.promptSearchParams),n.handleEnter(e)}))("input",(function(){return p["\u0275\u0275restoreView"](t),p["\u0275\u0275nextContext"](2).setTypingTrue()}))("focus",(function(){return p["\u0275\u0275restoreView"](t),p["\u0275\u0275nextContext"](2).setTypingTrue()})),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](41,ft,9,0,"div",164),p["\u0275\u0275template"](42,yt,9,0,"div",165),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&t){const t=p["\u0275\u0275nextContext"](2);p["\u0275\u0275advance"](14),p["\u0275\u0275property"]("ngModel",t.promptLibrarySearchParams),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",t.promptLibrarySearchParams&&t.promptLibrarySearchParams.length>0),p["\u0275\u0275advance"](12),p["\u0275\u0275textInterpolate1"](" ",p["\u0275\u0275pipeBind2"](28,9,t.promptLibraryData,t.promptLibrarySearchParams).length>9?p["\u0275\u0275pipeBind2"](29,12,t.promptLibraryData,t.promptLibrarySearchParams).length:"0"+p["\u0275\u0275pipeBind2"](30,15,t.promptLibraryData,t.promptLibrarySearchParams).length," "),p["\u0275\u0275advance"](5),p["\u0275\u0275property"]("ngForOf",p["\u0275\u0275pipeBind2"](33,18,t.promptLibraryData,t.promptLibrarySearchParams)),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngIf",p["\u0275\u0275pipeBind2"](35,21,t.promptLibraryData,t.promptLibrarySearchParams).length>0),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngIf",0==p["\u0275\u0275pipeBind2"](37,24,t.promptLibraryData,t.promptLibrarySearchParams).length),p["\u0275\u0275advance"](4),p["\u0275\u0275property"]("ngModel",t.promptSearchParams),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!t.isPromptApiInProgress&&t.promptSearchParams&&t.promptSearchParams.length>0),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",t.isPromptApiInProgress||!t.promptSearchParams||0==t.promptSearchParams.length)}}function _t(t,e){if(1&t&&(p["\u0275\u0275elementStart"](0,"div",196),p["\u0275\u0275element"](1,"img",11),p["\u0275\u0275elementStart"](2,"div",197),p["\u0275\u0275element"](3,"img",198),p["\u0275\u0275elementStart"](4,"span",199),p["\u0275\u0275text"](5),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&t){const t=p["\u0275\u0275nextContext"](5);p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("src",null==t.data?null:t.data.aiThemeConfig["AI-ICON-SHORT"],p["\u0275\u0275sanitizeUrl"]),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("src",null==t.data?null:t.data.aiThemeConfig["CHATBOT-CHAT-LOADER"],p["\u0275\u0275sanitizeUrl"]),p["\u0275\u0275advance"](2),p["\u0275\u0275textInterpolate"](t.loaderText)}}function Pt(t,e){if(1&t&&(p["\u0275\u0275elementStart"](0,"div",200),p["\u0275\u0275element"](1,"img",11),p["\u0275\u0275elementStart"](2,"div",197),p["\u0275\u0275namespaceSVG"](),p["\u0275\u0275elementStart"](3,"svg",201),p["\u0275\u0275element"](4,"path",202),p["\u0275\u0275elementEnd"](),p["\u0275\u0275namespaceHTML"](),p["\u0275\u0275elementStart"](5,"span",199),p["\u0275\u0275text"](6,"Ready"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&t){const t=p["\u0275\u0275nextContext"](5);p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("src",null==t.data?null:t.data.aiThemeConfig["AI-ICON-SHORT"],p["\u0275\u0275sanitizeUrl"])}}function vt(t,e){1&t&&(p["\u0275\u0275elementStart"](0,"div"),p["\u0275\u0275namespaceSVG"](),p["\u0275\u0275elementStart"](1,"svg",210),p["\u0275\u0275element"](2,"path",211),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]())}const Mt=function(t,e,n){return{fill:t,cursor:e,"pointer-events":n}};function Ot(t,e){if(1&t){const t=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275namespaceSVG"](),p["\u0275\u0275elementStart"](0,"svg",212),p["\u0275\u0275listener"]("click",(function(){p["\u0275\u0275restoreView"](t);const e=p["\u0275\u0275nextContext"](4),n=e.$implicit,i=e.index;return p["\u0275\u0275nextContext"](3).likeAndDislikeResponse(!0!==n.is_liked||null,n,i)})),p["\u0275\u0275element"](1,"path",213),p["\u0275\u0275elementEnd"]()}if(2&t){const t=p["\u0275\u0275nextContext"](4).$implicit;p["\u0275\u0275property"]("ngStyle",p["\u0275\u0275pureFunction3"](1,Mt,!0===t.is_liked?"#1C1B1F":"#7D838B",!0===t.is_liked?"default":"pointer",!0===t.is_liked?"none":"auto"))}}function bt(t,e){if(1&t){const t=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275namespaceSVG"](),p["\u0275\u0275elementStart"](0,"svg",212),p["\u0275\u0275listener"]("click",(function(){p["\u0275\u0275restoreView"](t);const e=p["\u0275\u0275nextContext"](4),n=e.$implicit,i=e.index;return p["\u0275\u0275nextContext"](3).likeAndDislikeResponse(!1===n.is_liked&&null,n,i)})),p["\u0275\u0275element"](1,"path",214),p["\u0275\u0275elementEnd"]()}if(2&t){const t=p["\u0275\u0275nextContext"](4).$implicit;p["\u0275\u0275property"]("ngStyle",p["\u0275\u0275pureFunction3"](1,Mt,!1===t.is_liked?"#1C1B1F":"#7D838B",!1===t.is_liked?"default":"pointer",!1===t.is_liked?"none":"auto"))}}function wt(t,e){if(1&t&&(p["\u0275\u0275elementStart"](0,"div",207),p["\u0275\u0275elementStart"](1,"div",208),p["\u0275\u0275template"](2,vt,3,0,"div",6),p["\u0275\u0275template"](3,Ot,2,5,"svg",209),p["\u0275\u0275template"](4,bt,2,5,"svg",209),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&t){const t=p["\u0275\u0275nextContext"](3).$implicit,e=p["\u0275\u0275nextContext"](3);p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngIf",!e.hideRegenerate),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",1==t.is_liked||null==t.is_liked),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!0!==t.is_liked)}}function St(t,e){if(1&t&&(p["\u0275\u0275elementStart"](0,"div",203),p["\u0275\u0275element"](1,"img",11),p["\u0275\u0275elementStart"](2,"div",204),p["\u0275\u0275element"](3,"div",205),p["\u0275\u0275pipe"](4,"markdownCustom"),p["\u0275\u0275template"](5,wt,5,3,"div",206),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()),2&t){const t=p["\u0275\u0275nextContext"](2).$implicit,e=p["\u0275\u0275nextContext"](3);p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("src",null==e.data?null:e.data.aiThemeConfig["AI-ICON-SHORT"],p["\u0275\u0275sanitizeUrl"]),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("innerHTML",p["\u0275\u0275pipeBind1"](4,3,null==t?null:t.response),p["\u0275\u0275sanitizeHtml"]),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngIf",t.response)}}function Tt(t,e){if(1&t&&(p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275template"](1,_t,6,3,"div",193),p["\u0275\u0275template"](2,Pt,7,1,"div",194),p["\u0275\u0275template"](3,St,6,5,"div",195),p["\u0275\u0275elementContainerEnd"]()),2&t){const t=p["\u0275\u0275nextContext"]().$implicit;p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",null==t?null:t.isLoading),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",null==t?null:t.isReady),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!(null!=t&&t.isLoading||null!=t&&t.isReady))}}function Et(t,e){if(1&t&&(p["\u0275\u0275element"](0,"div",217),p["\u0275\u0275pipe"](1,"safeHtml")),2&t){const t=p["\u0275\u0275nextContext"](2).$implicit,e=p["\u0275\u0275nextContext"](3);p["\u0275\u0275property"]("innerHTML",p["\u0275\u0275pipeBind1"](1,1,e.errorMessage(null==t?null:t.error_code)),p["\u0275\u0275sanitizeHtml"])}}function Dt(t,e){if(1&t&&(p["\u0275\u0275elementStart"](0,"div",204),p["\u0275\u0275element"](1,"div",205),p["\u0275\u0275pipe"](2,"markdownCustom"),p["\u0275\u0275elementEnd"]()),2&t){const t=p["\u0275\u0275nextContext"](2).$implicit;p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("innerHTML",p["\u0275\u0275pipeBind1"](2,1,null==t?null:t.response),p["\u0275\u0275sanitizeHtml"])}}function kt(t,e){if(1&t&&(p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275elementStart"](1,"div",203),p["\u0275\u0275element"](2,"img",11),p["\u0275\u0275template"](3,Et,2,3,"div",215),p["\u0275\u0275template"](4,Dt,3,3,"ng-template",null,216,p["\u0275\u0275templateRefExtractor"]),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementContainerEnd"]()),2&t){const t=p["\u0275\u0275reference"](5),e=p["\u0275\u0275nextContext"]().$implicit,n=p["\u0275\u0275nextContext"](3);p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("src",null==n.data?null:n.data.aiThemeConfig["AI-ICON-SHORT"],p["\u0275\u0275sanitizeUrl"]),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!(null!=e&&e.isQuestion))("ngIfElse",t)}}function It(t,e){if(1&t&&(p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275elementStart"](1,"div",188),p["\u0275\u0275elementStart"](2,"div",189),p["\u0275\u0275elementStart"](3,"div",190),p["\u0275\u0275elementStart"](4,"div",191),p["\u0275\u0275text"](5),p["\u0275\u0275elementEnd"](),p["\u0275\u0275element"](6,"app-user-image",192),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](7,Tt,4,3,"ng-container",6),p["\u0275\u0275template"](8,kt,6,3,"ng-container",6),p["\u0275\u0275elementContainerEnd"]()),2&t){const t=e.$implicit,n=p["\u0275\u0275nextContext"](3);p["\u0275\u0275advance"](5),p["\u0275\u0275textInterpolate"](t.prompt),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("oid",n.oid),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!(null!=t&&t.isError)),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",null==t?null:t.isError)}}function Ht(t,e){if(1&t){const t=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",218),p["\u0275\u0275listener"]("click",(function(e){p["\u0275\u0275restoreView"](t);const n=p["\u0275\u0275nextContext"](3);return n.onSearchPrompt(e,n.promptSearchParams)})),p["\u0275\u0275namespaceSVG"](),p["\u0275\u0275elementStart"](1,"svg",131),p["\u0275\u0275element"](2,"rect",132),p["\u0275\u0275element"](3,"path",133),p["\u0275\u0275element"](4,"path",134),p["\u0275\u0275elementStart"](5,"defs"),p["\u0275\u0275elementStart"](6,"linearGradient",135),p["\u0275\u0275element"](7,"stop",105),p["\u0275\u0275element"](8,"stop",106),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}}function Lt(t,e){1&t&&(p["\u0275\u0275elementStart"](0,"div",219),p["\u0275\u0275namespaceSVG"](),p["\u0275\u0275elementStart"](1,"svg",131),p["\u0275\u0275element"](2,"rect",137),p["\u0275\u0275element"](3,"path",133),p["\u0275\u0275element"](4,"path",134),p["\u0275\u0275elementStart"](5,"defs"),p["\u0275\u0275elementStart"](6,"linearGradient",135),p["\u0275\u0275element"](7,"stop",105),p["\u0275\u0275element"](8,"stop",106),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]())}function Vt(t,e){if(1&t){const t=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275namespaceSVG"](),p["\u0275\u0275namespaceHTML"](),p["\u0275\u0275elementStart"](0,"div",183),p["\u0275\u0275elementStart"](1,"div",184,185),p["\u0275\u0275template"](3,It,9,4,"ng-container",57),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](4,"div",161),p["\u0275\u0275elementStart"](5,"div",162),p["\u0275\u0275listener"]("mouseenter",(function(){return p["\u0275\u0275restoreView"](t),p["\u0275\u0275nextContext"](2).setTypingTrue()})),p["\u0275\u0275elementStart"](6,"textarea",126),p["\u0275\u0275listener"]("blur",(function(){return p["\u0275\u0275restoreView"](t),p["\u0275\u0275nextContext"](2).clearSearchInput()}))("ngModelChange",(function(e){return p["\u0275\u0275restoreView"](t),p["\u0275\u0275nextContext"](2).promptSearchParams=e}))("keydown.enter",(function(e){p["\u0275\u0275restoreView"](t);const n=p["\u0275\u0275nextContext"](2);return n.onSearchPrompt(e,n.promptSearchParams),n.adjustCursorPosition(e)}))("input",(function(){return p["\u0275\u0275restoreView"](t),p["\u0275\u0275nextContext"](2).setTypingTrue()})),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](7,Ht,9,0,"div",186),p["\u0275\u0275template"](8,Lt,9,0,"div",187),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&t){const t=p["\u0275\u0275nextContext"](2);p["\u0275\u0275advance"](3),p["\u0275\u0275property"]("ngForOf",t.currentThreadData),p["\u0275\u0275advance"](3),p["\u0275\u0275property"]("ngModel",t.promptSearchParams),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!t.isPromptApiInProgress&&t.promptSearchParams&&t.promptSearchParams.length>0),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",t.isPromptApiInProgress||!t.promptSearchParams||0==t.promptSearchParams.length)}}function Ft(t,e){if(1&t){const t=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275elementStart"](1,"div",82),p["\u0275\u0275element"](2,"div",83),p["\u0275\u0275elementStart"](3,"div",84),p["\u0275\u0275template"](4,ot,3,2,"div",85),p["\u0275\u0275elementStart"](5,"div",86),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](t),p["\u0275\u0275nextContext"]().onDialogClose()})),p["\u0275\u0275namespaceSVG"](),p["\u0275\u0275elementStart"](6,"svg",87),p["\u0275\u0275elementStart"](7,"mask",88),p["\u0275\u0275element"](8,"rect",89),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](9,"g",90),p["\u0275\u0275element"](10,"path",91),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](11,at,8,5,"div",92),p["\u0275\u0275template"](12,lt,10,3,"div",93),p["\u0275\u0275template"](13,xt,43,27,"div",94),p["\u0275\u0275template"](14,Vt,9,4,"div",95),p["\u0275\u0275namespaceHTML"](),p["\u0275\u0275element"](15,"img",96),p["\u0275\u0275element"](16,"img",97),p["\u0275\u0275elementStart"](17,"div",98),p["\u0275\u0275text"](18,"Kais can make mistakes. Check important info."),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementContainerEnd"]()}if(2&t){const t=p["\u0275\u0275nextContext"]();p["\u0275\u0275advance"](4),p["\u0275\u0275property"]("ngIf",!t.isLoading&&t.promptLibraryData.length>0),p["\u0275\u0275advance"](7),p["\u0275\u0275property"]("ngIf",t.isHomeScreenVisible),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",t.isHomeScreenVisible),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!t.isLoading&&t.isPromptLibraryVisible),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",t.isChatVisible),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("src",null==t.data?null:t.data.aiThemeConfig["CHATBOT-BG-ICON"],p["\u0275\u0275sanitizeUrl"]),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("src",null==t.data?null:t.data.aiThemeConfig["CHATBOT-BG-ICON"],p["\u0275\u0275sanitizeUrl"])}}function jt(t,e){1&t&&(p["\u0275\u0275namespaceSVG"](),p["\u0275\u0275elementStart"](0,"svg",222),p["\u0275\u0275element"](1,"path",227),p["\u0275\u0275elementEnd"]())}function zt(t,e){1&t&&(p["\u0275\u0275namespaceSVG"](),p["\u0275\u0275elementStart"](0,"svg",222),p["\u0275\u0275element"](1,"path",228),p["\u0275\u0275elementEnd"]())}function Rt(t,e){if(1&t){const t=p["\u0275\u0275getCurrentView"]();p["\u0275\u0275elementStart"](0,"div",220),p["\u0275\u0275elementStart"](1,"button",221),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](t),p["\u0275\u0275nextContext"]().renameThread()})),p["\u0275\u0275namespaceSVG"](),p["\u0275\u0275elementStart"](2,"svg",222),p["\u0275\u0275element"](3,"path",223),p["\u0275\u0275elementEnd"](),p["\u0275\u0275text"](4," Rename "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275namespaceHTML"](),p["\u0275\u0275element"](5,"mat-divider",224),p["\u0275\u0275elementStart"](6,"button",221),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](t),p["\u0275\u0275nextContext"]().deleteThread()})),p["\u0275\u0275namespaceSVG"](),p["\u0275\u0275elementStart"](7,"svg",222),p["\u0275\u0275element"](8,"path",225),p["\u0275\u0275elementEnd"](),p["\u0275\u0275text"](9," Delete "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275namespaceHTML"](),p["\u0275\u0275element"](10,"mat-divider",224),p["\u0275\u0275elementStart"](11,"button",221),p["\u0275\u0275listener"]("click",(function(){return p["\u0275\u0275restoreView"](t),p["\u0275\u0275nextContext"]().pinOrUnpinThread()})),p["\u0275\u0275template"](12,jt,2,0,"svg",226),p["\u0275\u0275template"](13,zt,2,0,"svg",226),p["\u0275\u0275text"](14),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"]()}if(2&t){const t=p["\u0275\u0275nextContext"]();p["\u0275\u0275advance"](12),p["\u0275\u0275property"]("ngIf",null==t.selectedHistoryThreadData?null:t.selectedHistoryThreadData.is_pinned),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!(null!=t.selectedHistoryThreadData&&t.selectedHistoryThreadData.is_pinned)),p["\u0275\u0275advance"](1),p["\u0275\u0275textInterpolate1"](" ",null!=t.selectedHistoryThreadData&&t.selectedHistoryThreadData.is_pinned?"Unpin":"Pin"," ")}}let At=(()=>{class t{constructor(t,e,n,i,o,a,c,s,l,d,p){this.data=t,this._dialogRef=e,this._aiService=n,this._loginService=i,this._toaster=o,this._viewContainerRef=a,this._overlay=c,this._stringSimilarity=s,this.sanitizer=l,this.elementRef=d,this.filter=p,this._onDestroy=new r.b,this.isHistoryVisible=!1,this.isHistoryLoading=!1,this.historySearchParams="",this.historySelectedThreadId=null,this.historySkip=0,this.historyLimit=25,this.historyData=[],this.pinnedHistoryData=[],this.historyGroupedData={},this.historyGroupedDateData=[],this.selectedHistoryDateIndex=null,this.selectedHistoryThreadIndex=null,this.promptSearchParams="",this.searchDefaultPrompts=[],this.searchPromptSkip=0,this.searchLazyLoadedList=[],this.startIndex=0,this.skip=25,this.filteredPrompts=[],this.promptLibraryData=[],this.isPromptLibraryVisible=!1,this.currentSelectedPromptLibraryCategoryIndex=null,this.currentSelectedPromptLibraryCategory=null,this.promptLibrarySearchParams="",this.isHomeScreenVisible=!0,this.currentThreadData=[],this.currentThreadLoadTextIndex=0,this.isChatVisible=!1,this.dialogExpanded=!1,this.repeatArray=[],this.threadTexts=[],this.loaderTexts=[],this.isSliding=!1,this.isThreadLoading=!1,this.isLoading=!0,this.isPromptApiInProgress=!1,this.suggestedPrompts=[],this.modules=[],this.isTyping=!1,this.currentIndex=0,this.flatList=[],this.childrenKey="children",this.likeStatusMap={},this.childIndexMap={},this.isCheckedUserIntent=!1,this.isNewThread=!0,this.loaderCurrentIndex=0,this.isReportsVisible=!1,this.hideRegenerate=!0,this.processStarted=!1,this.loaderText="Reviewing your request..."}ngOnInit(){var t,e;return Object(i.c)(this,void 0,void 0,(function*(){this.profile=this._loginService.getProfile().profile,this.threadTexts=null===(t=this.data)||void 0===t?void 0:t.aiThemeConfig["THREAD-LOADER-TEXT"],this.loaderTexts=null===(e=this.data)||void 0===e?void 0:e.aiThemeConfig["CHAT-LOADER-TEXT"],this.onDialogResizing(!0),setInterval(()=>{this.currentThreadLoadTextIndex=(this.currentThreadLoadTextIndex+1)%this.threadTexts.length,this.isSliding=!0,setTimeout(()=>{this.isSliding=!1},500)},2e3),this.calculateDynamicContentHeight(),Promise.all([this.getPromptLibraryData()]).then(t=>Object(i.c)(this,void 0,void 0,(function*(){this.isLoading=!1}))).catch(t=>{this.isLoading=!1,this.onDialogClose()})}))}onResize(){this.calculateDynamicContentHeight()}onDialogResizing(t=!1){t||(this.dialogExpanded=!this.dialogExpanded),this.calculateDynamicContentHeight();let e="calc(100vw - 76px)",n="55px",i="calc(100vw - 150px)";this.dialogExpanded&&(e="calc(-20vw - 84px + 100vw)",n="55px",i="calc(-20vw - 84px + 100vw)"),document.documentElement.style.setProperty("--kebsChatBotContentWidth",e),document.documentElement.style.setProperty("--FontFamily","DM Sans"),document.documentElement.style.setProperty("--chatBotAdjustedHeight",n),document.documentElement.style.setProperty("--chatBotWidth",i)}calculateDynamicContentHeight(){let t=41;this.dialogExpanded&&(t=0);const e=window.innerHeight-220-t,n=.3*e;document.documentElement.style.setProperty("--kebsChatbotContentHeight",e+"px"),document.documentElement.style.setProperty("--kebsChatbotHistoryContentHeight",n+"px");let i=window.innerHeight-170-t,r=Math.floor(i/34);this.repeatArray=Array(r)}openHistory(){this.isHistoryVisible=!0,this.historyData=[],this.allPinnedHistory(),this.allHistory(!0),document.documentElement.style.setProperty("--chatBotWidth","calc(-20vw - 84px + 100vw)")}closeHistory(){this.isHistoryVisible=!1,document.documentElement.style.setProperty("--chatBotWidth","calc(100vw - 150px)")}allHistory(t){return Object(i.c)(this,void 0,void 0,(function*(){t&&(this.isHistoryLoading=!0,this.historySkip=0,this.historyData=[],this.historyGroupedData={},this.historyGroupedData=[]);let e={aid:this.profile.aid,oid:this.profile.oid,skip:this.historySkip,limit:this.historyLimit,searchParams:this.historySearchParams};return new Promise((t,n)=>{this._aiService.allHistory(e).pipe(Object(o.a)(this._onDestroy)).subscribe({next:e=>{0==e.err&&(this.historyData=[...this.historyData,...e.response]),this.groupHistoryData(),this.isHistoryLoading=!1,t(!0)},error:t=>{this._toaster.showError("Error","Error in Fetching History Data",7e3),this._aiService.recordError({prompt:"From History",error:t}),this.isHistoryLoading=!1,n()}})})}))}allPinnedHistory(){return Object(i.c)(this,void 0,void 0,(function*(){let t={aid:this.profile.aid,oid:this.profile.oid,skip:0,limit:4,searchParams:this.historySearchParams};return new Promise((e,n)=>{this._aiService.allPinnedHistory(t).pipe(Object(o.a)(this._onDestroy)).subscribe({next:t=>{0==t.err&&(this.pinnedHistoryData=t.response),this.isHistoryLoading=!1,e(!0)},error:t=>{this._toaster.showError("Error","Error in Fetching History Data",7e3),this._aiService.recordError({prompt:"From Pinned History",error:t}),this.isHistoryLoading=!1,n()}})})}))}groupHistoryData(){const t=l()().startOf("day"),e=l()().subtract(1,"days").startOf("day"),n=l()().subtract(7,"days").startOf("day"),i=l()().subtract(30,"days").startOf("day"),r={};this.historyData.forEach(o=>{const a=l()(o.created_on).startOf("day");if(a.isSame(t,"day"))r.Today||(r.Today=[]),r.Today.push(o);else if(a.isSame(e,"day"))r.Yesterday||(r.Yesterday=[]),r.Yesterday.push(o);else if(a.isAfter(n))r["Previous 7 Days"]||(r["Previous 7 Days"]=[]),r["Previous 7 Days"].push(o);else if(a.isAfter(i))r["Previous 30 Days"]||(r["Previous 30 Days"]=[]),r["Previous 30 Days"].push(o);else{const t=a.format("MMMM YYYY");r[t]||(r[t]=[]),r[t].push(o)}}),this.historyGroupedData=r,this.historyGroupedDateData=Object.keys(r)}defaultPrompts(){return Object(i.c)(this,void 0,void 0,(function*(){let t={aid:this.profile.aid,oid:this.profile.oid,module_id:915,accessible_module_ids:d.pluck(this.modules,"id")};return this.suggestedPrompts=[],new Promise((e,n)=>{this._aiService.defaultPrompts(t).pipe(Object(o.a)(this._onDestroy)).subscribe({next:t=>{0==t.err&&(this.suggestedPrompts=t.data),e(!0)},error:t=>{this._toaster.showError("Error","Error in Fetching Default Prompts",7e3),this.isLoading=!1,n()}})})}))}getPromptLibraryData(){return Object(i.c)(this,void 0,void 0,(function*(){let t={aid:this.profile.aid,oid:this.profile.oid};return this.promptLibraryData=[],new Promise((e,n)=>{this._aiService.getPromptLibraryData(t).pipe(Object(o.a)(this._onDestroy)).subscribe({next:t=>{0==t.err&&(this.promptLibraryData=t.data),e(!0)},error:t=>{this._toaster.showError("Error","Error in Fetching Modules",7e3),this.isLoading=!1,n()}})})}))}getDefaultPromptsBySearch(){return Object(i.c)(this,void 0,void 0,(function*(){this.searchDefaultPrompts=[];let t={aid:this.profile.aid,oid:this.profile.oid,skip:this.searchPromptSkip,limit:25,module_id:[460,544,915],is_general_prompt:!this.isReportsVisible,is_report_prompt:this.isReportsVisible,search_params:this.promptSearchParams?this.promptSearchParams:""};return new Promise((e,n)=>Object(i.c)(this,void 0,void 0,(function*(){yield this._aiService.getDefaultPromptsBySearch(t).pipe(Object(o.a)(this._onDestroy)).subscribe({next:t=>{0==t.err&&(this.searchDefaultPrompts=t.data,this.searchDefaultPrompts=0==this.searchPromptSkip?t.data:[...this.searchDefaultPrompts,...t.data]),e(!0)},error:t=>{this._toaster.showError("Error","Error in Fetching Search Prompt Data",7e3),n()}})})))}))}onDialogClose(){this._dialogRef.close()}openPromptLibrary(){this.promptLibrarySearchParams="",this.promptSearchParams="",this.resetUiState(["isPromptLibraryVisible"]),this.promptLibraryData&&this.promptLibraryData.length>0&&(this.setPromptLibraryIndex(0),this.setPromptLibraryCategory(this.promptLibraryData[0].name))}setPromptLibraryIndex(t){this.currentSelectedPromptLibraryCategoryIndex=t}setPromptLibraryCategory(t){this.currentSelectedPromptLibraryCategory=t}resetUiState(t){return Object(i.c)(this,void 0,void 0,(function*(){let e=["isChatVisible","isHomeScreenVisible","isPromptLibraryVisible"];for(let t=0;t<e.length;t++)this[e[t]]=!1;for(let n=0;n<t.length;n++)this[t[n]]=!0}))}getGreetingWithName(t,e){return(t||"Hello, ${name}").replace("${name}",e)}onSearchParamsChange(){const t=this.filter.transform(this.promptLibraryData,this.promptLibrarySearchParams);t.length>0?this.findPromptLibraryIndex(t,t[0].id):(this.currentSelectedPromptLibraryCategoryIndex=0,this.currentSelectedPromptLibraryCategory=null)}findPromptLibraryIndex(t,e){var n;let i=t.findIndex(t=>t.id==e),r=null===(n=t.find(t=>t.id==e))||void 0===n?void 0:n.name;-1!=i&&(this.setPromptLibraryIndex(i),this.setPromptLibraryCategory(r))}highlightPromptKey(t){if(!t)return"-";const e=t.replace(/[{[]([^}\]]+)[}\]]/g,(t,e)=>`<span style="color: #ef4a61;">[${e}]</span>`);return this.sanitizer.bypassSecurityTrustHtml(e)}openNewChat(){this.resetUiState(["isHomeScreenVisible"]),this.currentThreadData=[],this.threadIdReference="",this.isCheckedUserIntent=!1,this.isNewThread=!0,this.promptSearchParams="",this.resetThreadId()}resetThreadId(){this.historySelectedThreadId=null}copyPromptToClipboard(t){return Object(i.c)(this,void 0,void 0,(function*(){t.isCopyInProgress=!0,this.promptSearchParams=null==t?void 0:t.prompt,setTimeout(()=>Object(i.c)(this,void 0,void 0,(function*(){t.isCopyInProgress=!1,yield this.onSearchPromptFromLibrary(this.promptSearchParams)})),1e3)}))}onSearchPrompt(t,e){return Object(i.c)(this,void 0,void 0,(function*(){t.preventDefault(),this.processStarted||(this.processStarted=!0,e&&e.length>0&&e.trim().length>0?(this.currentThreadData.push({prompt:e,isLoading:!0}),this.resetUiState(["isChatVisible"]),setTimeout(()=>{this.scrollToBottom()},500),this.promptSearchParams="",this.isPromptApiInProgress=!0,this.triggerCacheResponse(e)):this.processStarted=!1)}))}onSearchPromptFromLibrary(t){return Object(i.c)(this,void 0,void 0,(function*(){t&&t.length>0&&t.trim().length>0&&(this.currentThreadData.push({prompt:t,isLoading:!0}),this.loaderText="Reviewing your request...",this.resetUiState(["isChatVisible"]),setTimeout(()=>{this.scrollToBottom()},500),this.promptSearchParams="",this.isPromptApiInProgress=!0,this.triggerAgentExecutor("faq_agent",t,1,!1))}))}triggerAgentExecutor(t,e,n,r){var a;return Object(i.c)(this,void 0,void 0,(function*(){let c=this.threadIdReference;c?this.currentThreadData[this.currentThreadData.length-1].thread_id=c:(c=Object(m.a)(),this.threadIdReference=c,this.currentThreadData[(null===(a=this.currentThreadData)||void 0===a?void 0:a.length)-1].thread_id=c,this.isNewThread=!0);let s=d.pluck(Array.isArray(null==t?void 0:t.intent)?t.intent:[],"intent"),l={chat_thread_id:c,intent:0==n?s[0]:t,prompt:e,is_deep_search:!1,is_new_intent:r};if(this.currentThreadData[this.currentThreadData.length-1].intent=0==n?s[0]:t,c)return yield this.getHealthStatus(),this.isPromptApiInProgress=!0,new Promise((t,n)=>{this._aiService.getAgentResponse(l).pipe(Object(o.a)(this._onDestroy)).subscribe({next:n=>Object(i.c)(this,void 0,void 0,(function*(){0==n.err?(this.processStarted=!1,this.loaderText="Getting everything in place...",this.isNewThread&&(yield this._aiService.getChatHeading({chat_thread_id:c,prompt:e}).toPromise(),this.allHistory(!0)),this.currentThreadData[this.currentThreadData.length-1].response=n.response.output,this.currentThreadData[this.currentThreadData.length-1].chat_message_id=n.response.chat_message_id,this.currentThreadData[this.currentThreadData.length-1].isReady=!0,this.currentThreadData[this.currentThreadData.length-1].isLoading=!1,setTimeout(()=>{this.currentThreadData[this.currentThreadData.length-1].isReady=!1},1500)):(this.processStarted=!1,this.isNewThread&&(yield this._aiService.getChatHeading({chat_thread_id:c,prompt:e}).toPromise(),this.allHistory(!0)),this.currentThreadData[this.currentThreadData.length-1].isError=!0,this.currentThreadData[this.currentThreadData.length-1].isQuestion=!!(null==n?void 0:n.response)&&n.response.isQuestion,this.currentThreadData[this.currentThreadData.length-1].error_code=(null==n?void 0:n.error_code)||"ERR_API_001",this.currentThreadData[this.currentThreadData.length-1].response=(null==n?void 0:n.response)?n.response.output:"",this.currentThreadData[this.currentThreadData.length-1].chat_message_id=(null==n?void 0:n.response)?n.response.chat_message_id:"",this._aiService.recordError({prompt:e,error:n})),setTimeout(()=>{this.isPromptApiInProgress=!1},500),t(!0)})),error:t=>Object(i.c)(this,void 0,void 0,(function*(){this.processStarted=!1,(504==(null==t?void 0:t.status)||503==(null==t?void 0:t.status))&&this._aiService.notifyError({prompt:e,code:null==t?void 0:t.status}),this._aiService.recordError({prompt:e,error:t}),this.isNewThread&&(yield this._aiService.getChatHeading({chat_thread_id:c,prompt:e}).toPromise(),this.allHistory(!0)),this.currentThreadData[this.currentThreadData.length-1].isLoading=!1,this.currentThreadData[this.currentThreadData.length-1].isError=!0,this.currentThreadData[this.currentThreadData.length-1].error_code="ERR_API_001",setTimeout(()=>{this.isPromptApiInProgress=!1},1500),n()}))})})}))}triggerAgentExecutorForMultiple(t,e,n){return Object(i.c)(this,void 0,void 0,(function*(){let i={intent:e,prompt:t,is_new_intent:n};return this.isCheckedUserIntent=!0,this.isPromptApiInProgress=!0,new Promise((e,n)=>{this._aiService.getAgentResponseForMultipleIntents(i).pipe(Object(o.a)(this._onDestroy)).subscribe({next:n=>{0==n.err?(this.processStarted=!1,this.currentThreadData[this.currentThreadData.length-1].response=n.response.output,this.currentThreadData[this.currentThreadData.length-1].intent=n.response.intent,this.currentThreadData[this.currentThreadData.length-1].chat_message_id=n.response.chat_message_id,this.currentThreadData[this.currentThreadData.length-1].isReady=!0,this.currentThreadData[this.currentThreadData.length-1].isLoading=!1,setTimeout(()=>{this.currentThreadData[this.currentThreadData.length-1].isReady=!1},1500)):(this.processStarted=!1,this.currentThreadData[this.currentThreadData.length-1].isError=!0,this.currentThreadData[this.currentThreadData.length-1].error_code=(null==n?void 0:n.error_code)||"ERR_API_001",this.currentThreadData[this.currentThreadData.length-1].response=(null==n?void 0:n.response)?n.response.output:"",this.currentThreadData[this.currentThreadData.length-1].chat_message_id=(null==n?void 0:n.response)?n.response.chat_message_id:"",this._aiService.recordError({prompt:t,error:n})),setTimeout(()=>{this.isPromptApiInProgress=!1},1500),e(!0)},error:e=>{this.processStarted=!1,(504==(null==e?void 0:e.status)||503==(null==e?void 0:e.status))&&this._aiService.notifyError({prompt:t,code:null==e?void 0:e.status}),this._aiService.recordError({prompt:t,error:e}),this.currentThreadData[this.currentThreadData.length-1].isLoading=!1,this.currentThreadData[this.currentThreadData.length-1].isError=!0,this.currentThreadData[this.currentThreadData.length-1].error_code="ERR_API_001",setTimeout(()=>{this.isPromptApiInProgress=!1},1500),n()}})})}))}findMatchingPrompt(t,e,n=.9){return Object(i.c)(this,void 0,void 0,(function*(){let i={libraryIndex:-1,promptIndex:-1,similarity:0};return e.forEach((e,n)=>{e.prompts.forEach((e,r)=>{const o=this._stringSimilarity.similarity(t,e.prompt);o>i.similarity&&(i={libraryIndex:n,promptIndex:r,similarity:o})})}),i.similarity>=n?i:null}))}errorMessage(t,e=!1){var n,i;t=t||"ERR_API_001";const r=null===(n=this._aiService)||void 0===n?void 0:n.aiThemeConfig["RESPONSE-TEXT"].find(e=>(null==e?void 0:e.info_code)===t);return e?!r||null===(i=null==r?void 0:r.icon_enable)||void 0===i||i:r?null==r?void 0:r.info_html:null}setTypingTrue(){this.isTyping=!0,this.onPromptDataScroll(!0)}onPromptDataScroll(t=!1){var e,n,r,o;return Object(i.c)(this,void 0,void 0,(function*(){if(null===(e=this.searchDefaultPrompts)||void 0===e?void 0:e.length)if(t){this.startIndex=0,this.skip=25,this.searchLazyLoadedList=[];const t=(null===(r=null===(n=this.promptSearchParams)||void 0===n?void 0:n.trim())||void 0===r?void 0:r.toLowerCase())||"";this.filteredPrompts=t?this.searchDefaultPrompts.filter(e=>{var n;return null===(n=e.prompt)||void 0===n?void 0:n.toLowerCase().includes(t)}):[...this.searchDefaultPrompts],this.assignSearchPromptsOnLazyLoading()}else{if(this.startIndex>=(null===(o=this.filteredPrompts)||void 0===o?void 0:o.length))return;this.startIndex=this.skip,this.skip+=25,this.assignSearchPromptsOnLazyLoading()}else this.searchLazyLoadedList=[]}))}assignSearchPromptsOnLazyLoading(){const t=this.filteredPrompts.slice(this.startIndex,this.skip);this.searchLazyLoadedList.push(...t)}setSelectedHistoryThreadData(t,e,n){this.selectedHistoryThreadData=t,this.selectedHistoryDateIndex=e,this.selectedHistoryThreadIndex=n}setSelectedHistoryThread(t){if(this.historySelectedThreadId=t,this.isThreadLoading)return;this.isThreadLoading=!0;let e={aid:this.profile.aid,oid:this.profile.oid,chat_thread_id:t};return this.currentThreadData=[],this.threadIdReference="",this.promptSearchParams="",this.isCheckedUserIntent=!1,this.isReportsVisible=!1,new Promise((t,n)=>{this._aiService.retrieveThreadHistory(e).pipe(Object(o.a)(this._onDestroy)).subscribe({next:e=>{var n;0==e.err?(this.currentThreadData=e.response,this.threadIdReference=e.response&&e.response[0]?null===(n=e.response[0])||void 0===n?void 0:n.thread_id:"",this.resetUiState(["isChatVisible"]),setTimeout(()=>{this.scrollToBottom()},500)):(this.resetUiState(["isHomeScreenVisible"]),this._toaster.showWarning("Warning \u26a0\ufe0f",e.msg,7e3),this._aiService.recordError({prompt:"while retrieving history threads",error:e})),this.isThreadLoading=!1,t(!0)},error:t=>{this._toaster.showError("Error","Error in Fetching Session Data",7e3),this.resetUiState(["isHomeScreenVisible"]),this.isThreadLoading=!1,n()}})})}renameThread(){var t,e,n;let i=null===(t=this.selectedHistoryThreadData)||void 0===t?void 0:t._id,r=null===(e=this.historyData)||void 0===e?void 0:e.findIndex(t=>t._id==i),o=null===(n=this.pinnedHistoryData)||void 0===n?void 0:n.findIndex(t=>t._id==i);-1!=r?(this.historyData[r].isRenameInProgress=!0,this.groupHistoryData()):-1!=o&&(this.pinnedHistoryData[o].isRenameInProgress=!0,this.groupHistoryData()),setTimeout(t=>{if(-1!=r){const t=document.getElementById("#historyInputSearch-"+this.selectedHistoryDateIndex+"-"+this.selectedHistoryThreadIndex);t.focus(),t.select()}else if(-1!=o){const t=document.getElementById("#pinnedHistoryInputSearch-"+this.selectedHistoryThreadIndex);t.focus(),t.select()}},50)}onRenameThread(t,e,n){let i={aid:this.profile.aid,chat_thread_id:n,heading:e};return new Promise((e,n)=>{this._aiService.changeDescriptionOfThread(i).pipe(Object(o.a)(this._onDestroy)).subscribe({next:n=>{var i;if(0==n.err){let e=this.historyData.findIndex(e=>(null==e?void 0:e._id)==t);-1!=e&&(this.historyData[e].isRenameInProgress=!1,this.groupHistoryData());let n=null===(i=this.pinnedHistoryData)||void 0===i?void 0:i.findIndex(e=>(null==e?void 0:e._id)==t);-1!=n&&(this.pinnedHistoryData[n].isRenameInProgress=!1,this.groupHistoryData())}else this._toaster.showError("Error",n.msg,7e3),this._aiService.recordError({prompt:"while renaming the thread",error:n});e(!0)},error:t=>{this._toaster.showError("Error","Error in Renaming Thread",7e3),n()}})})}archiveOrUnarchiveThread(t){var e;let n={aid:this.profile.aid,thread_id:[null===(e=this.selectedHistoryThreadData)||void 0===e?void 0:e._id],archive:t};return new Promise((t,e)=>{this._aiService.archiveOrUnarchiveThread(n).pipe(Object(o.a)(this._onDestroy)).subscribe({next:e=>{var i,r;if(0==e.err){let t=this.historyData.findIndex(t=>(null==t?void 0:t._id)==(null==n?void 0:n.thread_id));-1!=t&&(this.historyData.splice(t,1),this.groupHistoryData());let e=null===(i=this.pinnedHistoryData)||void 0===i?void 0:i.findIndex(t=>(null==t?void 0:t._id)==(null==n?void 0:n.thread_id));-1!=e&&(null===(r=this.pinnedHistoryData)||void 0===r||r.splice(e,1)),this.resetToNewChatBasedOnCurrentThread(null==n?void 0:n.thread_id)}else this._toaster.showError("Error",e.msg,7e3);t(!0)},error:t=>{this._toaster.showError("Error","Error in Archiving Thread",7e3),e()}})})}resetToNewChatBasedOnCurrentThread(t){!this.isHomeScreenVisible&&this.currentThreadData&&this.currentThreadData.length>0&&t.includes(this.currentThreadData[0].thread_id)&&this.openNewChat()}deleteThread(){var t;let e={aid:this.profile.aid,chat_thread_id:null===(t=this.selectedHistoryThreadData)||void 0===t?void 0:t.thread_id};return new Promise((t,n)=>{this._aiService.deleteThread(e).pipe(Object(o.a)(this._onDestroy)).subscribe({next:n=>{var i,r;if(0==n.err){let t=this.historyData.findIndex(t=>(null==t?void 0:t.thread_id)==(null==e?void 0:e.chat_thread_id));-1!=t&&(this.historyData.splice(t,1),this.groupHistoryData());let n=null===(i=this.pinnedHistoryData)||void 0===i?void 0:i.findIndex(t=>(null==t?void 0:t.thread_id)==(null==e?void 0:e.chat_thread_id));-1!=n&&(null===(r=this.pinnedHistoryData)||void 0===r||r.splice(n,1)),this.openNewChat()}else this._toaster.showError("Error",n.msg,7e3),this._aiService.recordError({prompt:"while deleting thread",error:n});t(!0)},error:t=>{this._toaster.showError("Error","Error in Deleting Thread",7e3),n()}})})}pinOrUnpinThread(){var t,e,n,i;if(4==(null===(t=this.pinnedHistoryData)||void 0===t?void 0:t.length)&&!(null===(e=this.selectedHistoryThreadData)||void 0===e?void 0:e.is_pinned))return this._toaster.showInfo("Info \ud83d\udcdd","You can pin up to 4 threads!",7e3);let r={aid:this.profile.aid,chat_thread_id:null===(n=this.selectedHistoryThreadData)||void 0===n?void 0:n.thread_id,is_pinned:!(null===(i=this.selectedHistoryThreadData)||void 0===i?void 0:i.is_pinned)};return new Promise((t,e)=>{this._aiService.pinOrUnpinThread(r).pipe(Object(o.a)(this._onDestroy)).subscribe({next:e=>{if(0==e.err)if(null==r?void 0:r.is_pinned){let t=this.historyData.findIndex(t=>(null==t?void 0:t.thread_id)==(null==r?void 0:r.chat_thread_id));if(-1!=t){let e=this.historyData.splice(t,1).map(t=>Object.assign(Object.assign({},t),{is_pinned:!0}));this.pinnedHistoryData=[...this.pinnedHistoryData||[],...e],this.groupHistoryData()}}else this.allHistory(!0),this.allPinnedHistory();else this._toaster.showError("Error",e.msg,7e3),this._aiService.recordError({prompt:"while pin and unpin thread",error:e});t(!0)},error:t=>{this._toaster.showError("Error","Error in Pin/Unpin Thread",7e3),e()}})})}clearSearchInput(t){if(!t)return;const e=t.target;e&&!e.closest(".overlay")&&setTimeout(()=>{this.promptSearchParams=null,this.isTyping=!1,this.searchPromptSkip=0},100)}regenerateResponse(t,e){return Object(i.c)(this,void 0,void 0,(function*(){}))}onEnterSearchHistory(){this.allHistory(!0)}onHistoryDataScroll(){this.historySkip+=25,this.allHistory(!1)}likeAndDislikeResponse(t,e,n){return Object(i.c)(this,void 0,void 0,(function*(){if(null!==t){let n={chat_message_id:e.chat_message_id,chat_thread_id:e.thread_id,is_liked:t};return new Promise((i,r)=>{this._aiService.likeAndDislikeResponse(n).pipe(Object(o.a)(this._onDestroy)).subscribe({next:n=>{0==n.err?e.is_liked=t:(this._toaster.showError("Error",n.msg,7e3),this._aiService.recordError({prompt:"while like and dislike",error:n})),i(!0)},error:t=>{this._toaster.showError("Error","Error in Updating Response",7e3),r()}})})}}))}startLoadingTextCycle(){this.loaderCurrentIndex=0,this.currentLoadingText=this.loaderTexts[this.loaderCurrentIndex],this.textInterval=setInterval(()=>{var t;this.loaderCurrentIndex=(this.loaderCurrentIndex+1)%(null===(t=this.loaderTexts)||void 0===t?void 0:t.length),this.currentLoadingText=this.loaderTexts[this.loaderCurrentIndex]},2e4)}adjustTextareaHeight(t){t.style.height="auto",t.style.height=t.scrollHeight+"px"}adjustCursorPosition(t){setTimeout(()=>{const e=t.target;e&&(e.focus(),e.setSelectionRange(0,0))},500)}scrollToBottom(){const t=this.chatScrollContainer.nativeElement;t.scrollTop=t.scrollHeight}getHealthStatus(){return Object(i.c)(this,void 0,void 0,(function*(){return new Promise((t,e)=>this._aiService.getHealthStatus().pipe(Object(o.a)(this._onDestroy)).subscribe({next:n=>{0==n.err?t(!0):(this._toaster.showError("Error","System is currently not available!",7e3),this._aiService.notifyError({prompt:"From health status check",code:n}),e())},error:t=>{this._toaster.showError("Error","System is currently not available!",7e3),e(),this._aiService.recordError({prompt:"From health status check",error:t})}}))}))}triggerIntentResponse(t){var e;let n={prompt:t,chat_history:(null===(e=this.currentThreadData)||void 0===e?void 0:e.length)>1?this.currentThreadData.slice(0,this.currentThreadData.length-1):""};return new Promise((e,i)=>{this._aiService.getIntentResponse(n).pipe(Object(o.a)(this._onDestroy)).subscribe({next:n=>{var i;if(0==n.err)if("response_intent_new"==n.response){let e=null===(i=[...this.currentThreadData].slice(0,-1).reverse().find(t=>"feedback_intent"!==t.intent&&"faq_agent"!==t.intent&&"response_intent"!==t.intent))||void 0===i?void 0:i.intent;this.currentThreadData[this.currentThreadData.length-1].is_new_intent=!1,this.triggerAgentExecutor(e,t,1,!1)}else if("response_intent_follow_up"==n.response)this.currentThreadData[this.currentThreadData.length-1].is_new_intent=!1,this.triggerAgentExecutor("response_intent",t,1,!1);else if("feedback_intent"==n.response)this.currentThreadData[this.currentThreadData.length-1].is_new_intent=!1,this.triggerAgentExecutor("feedback_intent",t,1,!1);else if(n.is_multiple&&!this.isCheckedUserIntent){this.currentThreadData[this.currentThreadData.length-1].is_new_intent=!0;let e=d.pluck(n.intent,"intent");this.triggerAgentExecutorForMultiple(t,e,!0)}else this.currentThreadData[this.currentThreadData.length-1].is_new_intent=!0,this.triggerAgentExecutor(n.response,t,0,!0);else this.processStarted=!1,this.currentThreadData[this.currentThreadData.length-1].isError=!0,this.currentThreadData[this.currentThreadData.length-1].error_code=(null==n?void 0:n.error_code)||"ERR_API_001",this.isPromptApiInProgress=!1,this._aiService.recordError({prompt:t,error:n});setTimeout(()=>{},1500),e(!0)},error:e=>{this.processStarted=!1,(504==(null==e?void 0:e.status)||503==(null==e?void 0:e.status))&&this._aiService.notifyError({prompt:t,code:null==e?void 0:e.status}),this._aiService.recordError({prompt:t,error:e}),this.currentThreadData[this.currentThreadData.length-1].isLoading=!1,this.currentThreadData[this.currentThreadData.length-1].isError=!0,this.currentThreadData[this.currentThreadData.length-1].error_code="ERR_API_001",setTimeout(()=>{this.isPromptApiInProgress=!1},1500),i()}})})}triggerCacheResponse(t){var e;return Object(i.c)(this,void 0,void 0,(function*(){let n=this.threadIdReference;n?(this.isNewThread=!1,this.currentThreadData[this.currentThreadData.length-1].thread_id=n):(n=Object(m.a)(),this.threadIdReference=n,this.currentThreadData[(null===(e=this.currentThreadData)||void 0===e?void 0:e.length)-1].thread_id=n,this.isNewThread=!0);let r={chat_thread_id:n,prompt:t};if(n)return yield this.getHealthStatus(),this.isPromptApiInProgress=!0,new Promise((e,a)=>{this._aiService.getCachedOutput(r).pipe(Object(o.a)(this._onDestroy)).subscribe({next:r=>Object(i.c)(this,void 0,void 0,(function*(){0==r.err?(this.processStarted=!1,this.isNewThread&&(yield this._aiService.getChatHeading({chat_thread_id:n,prompt:t}).toPromise(),this.allHistory(!0)),this.currentThreadData[this.currentThreadData.length-1].intent=r.response.intent,this.currentThreadData[this.currentThreadData.length-1].response=r.response.output,this.currentThreadData[this.currentThreadData.length-1].chat_message_id=r.response.chat_message_id,this.currentThreadData[this.currentThreadData.length-1].isReady=!0,this.currentThreadData[this.currentThreadData.length-1].isLoading=!1,setTimeout(()=>{this.currentThreadData[this.currentThreadData.length-1].isReady=!1,this.isPromptApiInProgress=!1},1500)):this.triggerIntentResponse(t),e(!0)})),error:e=>Object(i.c)(this,void 0,void 0,(function*(){this.processStarted=!1,(504==(null==e?void 0:e.status)||503==(null==e?void 0:e.status))&&this._aiService.notifyError({prompt:t,code:null==e?void 0:e.status}),this._aiService.recordError({prompt:t,error:e}),this.isNewThread&&(yield this._aiService.getChatHeading({chat_thread_id:n,prompt:t}).toPromise(),this.allHistory(!0)),this.currentThreadData[this.currentThreadData.length-1].isLoading=!1,this.currentThreadData[this.currentThreadData.length-1].isError=!0,this.currentThreadData[this.currentThreadData.length-1].error_code="ERR_API_001",setTimeout(()=>{this.isPromptApiInProgress=!1},1500),a()}))})})}))}}return t.\u0275fac=function(e){return new(e||t)(p["\u0275\u0275directiveInject"](c.a),p["\u0275\u0275directiveInject"](c.h),p["\u0275\u0275directiveInject"](C.a),p["\u0275\u0275directiveInject"](u.a),p["\u0275\u0275directiveInject"](f.a),p["\u0275\u0275directiveInject"](p.ViewContainerRef),p["\u0275\u0275directiveInject"](a.e),p["\u0275\u0275directiveInject"](y.a),p["\u0275\u0275directiveInject"](x.c),p["\u0275\u0275directiveInject"](p.ElementRef),p["\u0275\u0275directiveInject"](h))},t.\u0275cmp=p["\u0275\u0275defineComponent"]({type:t,selectors:[["app-kais-landing-page"]],viewQuery:function(t,e){if(1&t&&p["\u0275\u0275viewQuery"](I,!0),2&t){let t;p["\u0275\u0275queryRefresh"](t=p["\u0275\u0275loadQuery"]())&&(e.chatScrollContainer=t.first)}},hostBindings:function(t,e){1&t&&p["\u0275\u0275listener"]("resize",(function(){return e.onResize()}),!1,p["\u0275\u0275resolveWindow"])},features:[p["\u0275\u0275ProvidersFeature"]([{provide:g.a,deps:[a.e],useFactory:t=>()=>t.scrollStrategies.reposition()},h])],decls:11,vars:4,consts:[[1,"bg-container"],[1,"chatbot-container"],[1,"row",2,"height","100%","width","100%"],["class","p-1","style","max-width: 65px !important; background-color: #FFF3F4;",4,"ngIf"],["class","history-container",4,"ngIf"],[1,"align-items-with-gap","main-container","p-0"],[4,"ngIf"],["historyMenu","matMenu"],["matMenuContent",""],[1,"p-1",2,"max-width","65px !important","background-color","#FFF3F4"],[1,"row","p-3"],[1,"ai-icon",3,"src"],[1,"row","pt-2","pb-2","pl-3",2,"width","60px","height","60px"],["class","svg-icon pt-4","tooltip","History",3,"click",4,"ngIf"],[1,"pt-4",2,"cursor","pointer",3,"click"],["width","25","height","24","viewBox","0 0 25 24","fill","none","xmlns","http://www.w3.org/2000/svg"],["d","M12.8137 22C18.3991 22 22.969 17.5 22.969 12C22.969 6.5 18.3991 2 12.8137 2C7.22831 2 2.65845 6.5 2.65845 12C2.65845 17.5 7.22831 22 12.8137 22Z","stroke","#EE4961","stroke-width","1.5","stroke-linecap","round","stroke-linejoin","round"],["d","M8.75159 12H16.8758","stroke","#EE4961","stroke-width","1.5","stroke-linecap","round","stroke-linejoin","round"],["d","M12.8137 16V8","stroke","#EE4961","stroke-width","1.5","stroke-linecap","round","stroke-linejoin","round"],["tooltip","History",1,"svg-icon","pt-4",3,"click"],["d","M22.9385 15V9C22.9385 4 20.9074 2 15.8298 2H9.73664C4.65901 2 2.62796 4 2.62796 9V15C2.62796 20 4.65901 22 9.73664 22H15.8298C20.9074 22 22.9385 20 22.9385 15Z","stroke","#EE4961","stroke-width","1.5","stroke-linecap","round","stroke-linejoin","round"],["d","M15.8298 2V22","stroke","#EE4961","stroke-width","1.5","stroke-linecap","round","stroke-linejoin","round"],["d","M8.72113 9.43994L11.3209 11.9999L8.72113 14.5599","stroke","#EE4961","stroke-width","1.5","stroke-linecap","round","stroke-linejoin","round"],[1,"history-container"],[1,"header"],[1,"ai-icon-history",3,"src"],["tooltip","Close History",1,"svg-icon",3,"click"],["width","24","height","24","viewBox","0 0 24 24","fill","none","xmlns","http://www.w3.org/2000/svg"],["d","M14.97 22.75H8.96997C3.53997 22.75 1.21997 20.43 1.21997 15V9C1.21997 3.57 3.53997 1.25 8.96997 1.25H14.97C20.4 1.25 22.72 3.57 22.72 9V15C22.72 20.43 20.41 22.75 14.97 22.75ZM8.96997 2.75C4.35997 2.75 2.71997 4.39 2.71997 9V15C2.71997 19.61 4.35997 21.25 8.96997 21.25H14.97C19.58 21.25 21.22 19.61 21.22 15V9C21.22 4.39 19.58 2.75 14.97 2.75H8.96997Z","fill","#EE4961"],["d","M7.96997 22.75C7.55997 22.75 7.21997 22.41 7.21997 22V2C7.21997 1.59 7.55997 1.25 7.96997 1.25C8.37997 1.25 8.71997 1.59 8.71997 2V22C8.71997 22.41 8.38997 22.75 7.96997 22.75Z","fill","#EE4961"],["d","M14.97 15.3099C14.78 15.3099 14.59 15.2399 14.44 15.0899L11.88 12.5299C11.59 12.2399 11.59 11.7599 11.88 11.4699L14.44 8.90988C14.73 8.61988 15.21 8.61988 15.5 8.90988C15.79 9.19988 15.79 9.67988 15.5 9.96988L13.48 11.9999L15.51 14.0299C15.8 14.3199 15.8 14.7999 15.51 15.0899C15.36 15.2399 15.17 15.3099 14.97 15.3099Z","fill","#EE4961"],[1,"pt-4","pl-4",3,"click"],[1,"new-chat-btn",3,"click"],["d","M12 2C6.49 2 2 6.49 2 12C2 17.51 6.49 22 12 22C17.51 22 22 17.51 22 12C22 6.49 17.51 2 12 2ZM16 12.75H12.75V16C12.75 16.41 12.41 16.75 12 16.75C11.59 16.75 11.25 16.41 11.25 16V12.75H8C7.59 12.75 7.25 12.41 7.25 12C7.25 11.59 7.59 11.25 8 11.25H11.25V8C11.25 7.59 11.59 7.25 12 7.25C12.41 7.25 12.75 7.59 12.75 8V11.25H16C16.41 11.25 16.75 11.59 16.75 12C16.75 12.41 16.41 12.75 16 12.75Z","fill","#EE4961"],[1,"label",3,"click"],[1,"search-ui-history"],[1,"search-bar"],["type","text","placeholder","Search From Threads",3,"ngModel","ngModelChange","keydown.enter"],["class","svg-icon",4,"ngIf"],["class","close-icon",3,"click",4,"ngIf"],["class","loading-state",4,"ngIf"],["class","no-history",4,"ngIf"],["class","content","infinite-scroll","",3,"infiniteScrollDistance","infiniteScrollThrottle","scrollWindow","scrolled",4,"ngIf"],[1,"svg-icon"],["width","16","height","16","viewBox","0 0 16 16","fill","none",3,"click"],["id","mask0_1126_15698","maskUnits","userSpaceOnUse","x","0","y","0","width","16","height","16",2,"mask-type","alpha"],["width","16","height","16","fill","#D9D9D9"],["mask","url(#mask0_1126_15698)"],["d","M12.7116 13.3178L8.7411 9.34708C8.40777 9.60008 8.04227 9.79603 7.6446 9.93492C7.24693 10.0738 6.83654 10.1432 6.41343 10.1432C5.37366 10.1432 4.48971 9.77931 3.7616 9.05142C3.03349 8.32353 2.66943 7.43997 2.66943 6.40075C2.66943 5.36164 3.03338 4.47775 3.76127 3.74908C4.48916 3.02053 5.37271 2.65625 6.41193 2.65625C7.45104 2.65625 8.33493 3.0203 9.0636 3.74842C9.79216 4.47653 10.1564 5.36047 10.1564 6.40025C10.1564 6.83614 10.0849 7.25292 9.94177 7.65058C9.79854 8.04836 9.60471 8.40747 9.36027 8.72792L13.3308 12.6984L12.7116 13.3178ZM6.41293 9.27675C7.21638 9.27675 7.89671 8.99808 8.45393 8.44075C9.01127 7.88353 9.28993 7.20319 9.28993 6.39975C9.28993 5.59631 9.01127 4.91597 8.45393 4.35875C7.89671 3.80142 7.21638 3.52275 6.41293 3.52275C5.60949 3.52275 4.92916 3.80142 4.37193 4.35875C3.8146 4.91597 3.53593 5.59631 3.53593 6.39975C3.53593 7.20319 3.8146 7.88353 4.37193 8.44075C4.92916 8.99808 5.60949 9.27675 6.41293 9.27675Z","fill","#7D838B"],[1,"close-icon",3,"click"],[1,"loading-state"],["class","loader",4,"ngFor","ngForOf"],[1,"loader"],[1,"no-history"],["infinite-scroll","",1,"content",3,"infiniteScrollDistance","infiniteScrollThrottle","scrollWindow","scrolled"],["class","single-date-log",4,"ngIf"],["class","divider",4,"ngIf"],[4,"ngFor","ngForOf"],[1,"single-date-log"],[1,"date"],["class","single-history",3,"ngClass",4,"ngFor","ngForOf"],[1,"single-history",3,"ngClass"],[3,"ngClass","matTooltip","ngStyle","click",4,"ngIf"],["class","inline-edit",4,"ngIf"],["class","svg-icon",3,"matMenuTriggerFor","click",4,"ngIf"],[3,"ngClass","matTooltip","ngStyle","click"],[1,"inline-edit"],["type","text","maxlength","150","placeholder","Enter a description...",3,"id","ngModel","ngModelChange","keydown.enter"],[1,"svg-icon",3,"matMenuTriggerFor","click"],["width","12","height","12","viewBox","0 0 12 12","fill","none"],["id","mask0_1126_15589","maskUnits","userSpaceOnUse","x","0","y","0","width","12","height","12",2,"mask-type","alpha"],["width","12","height","12","fill","#D9D9D9"],["mask","url(#mask0_1126_15589)"],["d","M6 9.63448C5.79375 9.63448 5.61721 9.56103 5.47038 9.41411C5.32346 9.26728 5.25 9.09073 5.25 8.88448C5.25 8.67823 5.32346 8.50165 5.47038 8.35473C5.61721 8.2079 5.79375 8.13448 6 8.13448C6.20625 8.13448 6.38279 8.2079 6.52962 8.35473C6.67654 8.50165 6.75 8.67823 6.75 8.88448C6.75 9.09073 6.67654 9.26728 6.52962 9.41411C6.38279 9.56103 6.20625 9.63448 6 9.63448ZM6 6.74986C5.79375 6.74986 5.61721 6.6764 5.47038 6.52948C5.32346 6.38265 5.25 6.20611 5.25 5.99986C5.25 5.79361 5.32346 5.61707 5.47038 5.47023C5.61721 5.32332 5.79375 5.24986 6 5.24986C6.20625 5.24986 6.38279 5.32332 6.52962 5.47023C6.67654 5.61707 6.75 5.79361 6.75 5.99986C6.75 6.20611 6.67654 6.38265 6.52962 6.52948C6.38279 6.6764 6.20625 6.74986 6 6.74986ZM6 3.86523C5.79375 3.86523 5.61721 3.79182 5.47038 3.64498C5.32346 3.49807 5.25 3.32148 5.25 3.11523C5.25 2.90898 5.32346 2.73244 5.47038 2.58561C5.61721 2.43869 5.79375 2.36523 6 2.36523C6.20625 2.36523 6.38279 2.43869 6.52962 2.58561C6.67654 2.73244 6.75 2.90898 6.75 3.11523C6.75 3.32148 6.67654 3.49807 6.52962 3.64498C6.38279 3.79182 6.20625 3.86523 6 3.86523Z","fill","#5F6C81"],[1,"divider"],[1,"loading-container"],[1,"gif",3,"src"],["class","carousel-text",4,"ngIf"],[1,"carousel-text"],[1,"text-content"],[1,"main-text"],[1,"sub-text"],[1,"row",2,"display","flex","height","60px","border-bottom","1px solid #0000000D","box-shadow","0px 4px 8px #0000000D","align-items","center"],[1,"col-11"],[1,"col-1",2,"display","flex","justify-content","space-evenly","align-items","baseline"],["class","svg-icon","tooltip","FAQ",4,"ngIf"],["tooltip","Close",1,"svg-icon",3,"click"],["width","34","height","35","viewBox","0 0 24 25","fill","none"],["id","mask0_1555_843","maskUnits","userSpaceOnUse","x","0","y","0","width","24","height","25",2,"mask-type","alpha"],["y","0.845703","width","34","height","35","fill","#D9D9D9"],["mask","url(#mask0_1555_843)"],["d","M8.227 17.6813L7.16357 16.6178L10.9367 12.8447L7.16357 9.09663L8.227 8.0332L12.0001 11.8063L15.7482 8.0332L16.8116 9.09663L13.0385 12.8447L16.8116 16.6178L15.7482 17.6813L12.0001 13.9082L8.227 17.6813Z","fill","#1C1B1F"],["class","row pt-3","style","display: flex; justify-content: center; height: 160px;",4,"ngIf"],["class","row text-area",4,"ngIf"],["class","prompt-library-content  pt-2 pl-4 pr-4",4,"ngIf"],["class","p-3 chat-wrapper",4,"ngIf"],[1,"bg-image-1",3,"src"],[1,"bg-image-2",3,"src"],[1,"imp-text"],["tooltip","FAQ",1,"svg-icon"],["width","21","height","21","viewBox","0 0 14 15","fill","none",3,"click",4,"ngIf"],["width","34","height","35","viewBox","0 0 28 25","fill","none",3,"click",4,"ngIf"],["width","21","height","21","viewBox","0 0 14 15","fill","none",3,"click"],["d","M1.79492 10.9117C1.91026 10.857 2.02814 10.8111 2.14859 10.7739C2.26903 10.7367 2.39903 10.7182 2.53859 10.7182H3.25642V2.51302H2.53859C2.32781 2.51302 2.1512 2.58546 2.00876 2.73035C1.8662 2.87524 1.79492 3.05069 1.79492 3.25669V10.9117ZM2.53859 14.1797C2.05426 14.1797 1.64253 14.0114 1.30342 13.6749C0.964422 13.3383 0.794922 12.9296 0.794922 12.4489V3.25669C0.794922 2.77235 0.964422 2.36063 1.30342 2.02152C1.64253 1.68252 2.05426 1.51302 2.53859 1.51302H7.29492V2.51302H4.25642V10.7182H8.66675V8.34635H9.66675V11.7182H2.53859C2.33259 11.7182 2.15714 11.7881 2.01226 11.9279C1.86737 12.0677 1.79492 12.2411 1.79492 12.4479C1.79492 12.6545 1.86737 12.8282 2.01226 12.9689C2.15714 13.1094 2.33259 13.1797 2.53859 13.1797H11.1283V7.67969H12.1283V14.1797H2.53859ZM10.1283 7.67969C10.1283 6.70391 10.4676 5.87663 11.1464 5.19785C11.8252 4.51908 12.6525 4.17969 13.6283 4.17969C12.6525 4.17969 11.8252 3.8403 11.1464 3.16152C10.4676 2.48274 10.1283 1.65547 10.1283 0.679688C10.1283 1.65547 9.78887 2.48274 9.11009 3.16152C8.43131 3.8403 7.60403 4.17969 6.62825 4.17969C7.60403 4.17969 8.43131 4.51908 9.11009 5.19785C9.78887 5.87663 10.1283 6.70391 10.1283 7.67969Z","fill","url(#paint0_linear_2061_126464)"],["id","paint0_linear_2061_126464","x1","13.6283","y1","7.42969","x2","0.11551","y2","7.42969","gradientUnits","userSpaceOnUse"],["stop-color","#EF4A61"],["offset","1","stop-color","#F27A6C"],["width","34","height","35","viewBox","0 0 28 25","fill","none",3,"click"],["id","mask0_1534_29427","maskUnits","userSpaceOnUse","x","6","y","3","width","17","height","17",2,"mask-type","alpha"],["x","6.46204","y","3.84497","width","16","height","16","fill","#D9D9D9"],["mask","url(#mask0_1534_29427)"],["d","M11.2568 14.7167H12.2568V6.51156H11.2568V14.7167ZM20.1287 18.1782H10.539C10.0547 18.1782 9.64296 18.0087 9.30385 17.6697C8.96485 17.3306 8.79535 16.9189 8.79535 16.4346V7.25522C8.79535 6.77089 8.96485 6.35917 9.30385 6.02006C9.64296 5.68106 10.0547 5.51156 10.539 5.51156H14.6492C14.3526 5.87478 14.1212 6.28206 13.955 6.73339C13.7887 7.18461 13.7055 7.66622 13.7055 8.17822C13.7055 9.32689 14.0863 10.3177 14.8478 11.1507C15.6094 11.9836 16.5492 12.4573 17.6672 12.5719V15.7167H10.539C10.333 15.7167 10.1576 15.7866 10.0127 15.9264C9.86779 16.0663 9.79535 16.2396 9.79535 16.4464C9.79535 16.6531 9.86779 16.8267 10.0127 16.9674C10.1576 17.1079 10.333 17.1782 10.539 17.1782H19.1287V12.4847C19.3132 12.4419 19.4878 12.3883 19.6523 12.3237C19.8169 12.2592 19.9757 12.1816 20.1287 12.0911V18.1782ZM18.1287 11.6782C18.1287 10.7024 18.4681 9.87517 19.1468 9.19639C19.8256 8.51761 20.6529 8.17822 21.6287 8.17822C20.6529 8.17822 19.8256 7.83883 19.1468 7.16006C18.4681 6.48128 18.1287 5.654 18.1287 4.67822C18.1287 5.654 17.7893 6.48128 17.1105 7.16006C16.4317 7.83883 15.6045 8.17822 14.6287 8.17822C15.6045 8.17822 16.4317 8.51761 17.1105 9.19639C17.7893 9.87517 18.1287 10.7024 18.1287 11.6782Z","fill","url(#paint0_linear_1534_29427)"],["x1","11.962","y1","21.345","x2","16.962","y2","21.345","stroke","url(#paint1_linear_1534_29427)"],["id","paint0_linear_1534_29427","x1","21.6287","y1","11.4282","x2","8.11594","y2","11.4282","gradientUnits","userSpaceOnUse"],["id","paint1_linear_1534_29427","x1","16.962","y1","22.345","x2","11.6973","y2","22.345","gradientUnits","userSpaceOnUse"],[1,"row","pt-3",2,"display","flex","justify-content","center","height","160px"],[1,"header-content-alignment"],[1,"ai-img-header",3,"src"],[1,"align-items-column"],[1,"main-text",3,"ngClass"],[1,"sub-text",3,"ngClass"],[1,"row","text-area"],[1,"col-1"],[1,"col-10"],[1,"search-ui-new"],[1,"search-bar-new","pb-0"],["type","text","placeholder","",3,"ngModel","blur","ngModelChange","keydown.enter","input"],["textAreaRef",""],["style","display: flex; justify-content: flex-end; padding-right: 13px;",3,"click",4,"ngIf"],["style","display: flex; justify-content: flex-end; padding-right: 13px;",4,"ngIf"],[2,"display","flex","justify-content","flex-end","padding-right","13px",3,"click"],["width","28","height","28","viewBox","0 0 36 36","fill","none"],["width","36","height","36","rx","18","fill","url(#paint0_linear_1555_26595)"],["d","M11.807 21.3475L12.967 19.0275C13.287 18.3808 13.287 17.6275 12.967 16.9808L11.807 14.6541C10.8137 12.6675 12.9537 10.5675 14.9204 11.6075L15.947 12.1541C16.0937 12.2275 16.207 12.3475 16.267 12.4941L20.0604 20.9275C20.2137 21.2741 20.0737 21.6808 19.7404 21.8541L14.9137 24.3941C12.9537 25.4341 10.8137 23.3341 11.807 21.3475Z","fill","white"],["opacity","0.4","d","M20.8738 20.4003L18.3871 14.8803C18.1071 14.2603 18.7738 13.6336 19.3738 13.9536L23.2205 15.9803C24.8538 16.8403 24.8538 19.1736 23.2205 20.0336L21.8605 20.747C21.4938 20.9336 21.0471 20.7803 20.8738 20.4003Z","fill","white"],["id","paint0_linear_1555_26595","x1","36","y1","18","x2","-1.90588","y2","18","gradientUnits","userSpaceOnUse"],[2,"display","flex","justify-content","flex-end","padding-right","13px"],["width","36","height","36","rx","18","fill","grey"],[1,"prompt-library-content","pt-2","pl-4","pr-4"],[1,"prompt-title"],[1,"prompt-title-icon",3,"click"],[1,"prompt-title-text"],["width","16","height","16","viewBox","0 0 16 16","fill","none"],["clip-path","url(#clip0_1497_30370)"],["d","M12.0207 11.0779L14.876 13.9326L13.9327 14.8759L11.078 12.0206C10.0158 12.8721 8.69468 13.3352 7.33334 13.3333C4.02134 13.3333 1.33334 10.6453 1.33334 7.33325C1.33334 4.02125 4.02134 1.33325 7.33334 1.33325C10.6453 1.33325 13.3333 4.02125 13.3333 7.33325C13.3353 8.69459 12.8722 10.0157 12.0207 11.0779ZM10.6833 10.5833C11.5294 9.71318 12.0019 8.54687 12 7.33325C12 4.75459 9.91134 2.66659 7.33334 2.66659C4.75468 2.66659 2.66668 4.75459 2.66668 7.33325C2.66668 9.91125 4.75468 11.9999 7.33334 11.9999C8.54696 12.0018 9.71327 11.5293 10.5833 10.6833L10.6833 10.5833Z","fill","#B9C0CA"],["id","clip0_1497_30370"],["width","16","height","16","fill","white"],["type","text","placeholder","Search Prompts Templates...","maxlength","50",3,"ngModel","ngModelChange"],["style","cursor: pointer","width","24","height","25","viewBox","0 0 24 25","fill","none",3,"click",4,"ngIf"],[1,"content","pb-4"],[1,"categories"],["width","16","height","16","viewBox","0 0 16 16","fill","none",2,"min-width","16px"],["d","M11.3333 6.66658H12.6667C14 6.66658 14.6667 5.99992 14.6667 4.66659V3.33325C14.6667 1.99992 14 1.33325 12.6667 1.33325H11.3333C9.99999 1.33325 9.33333 1.99992 9.33333 3.33325V4.66659C9.33333 5.99992 9.99999 6.66658 11.3333 6.66658Z","stroke","#111434","stroke-width","1.5","stroke-miterlimit","10","stroke-linecap","round","stroke-linejoin","round"],["d","M3.33333 14.6666H4.66666C5.99999 14.6666 6.66666 13.9999 6.66666 12.6666V11.3333C6.66666 9.99992 5.99999 9.33325 4.66666 9.33325H3.33333C1.99999 9.33325 1.33333 9.99992 1.33333 11.3333V12.6666C1.33333 13.9999 1.99999 14.6666 3.33333 14.6666Z","stroke","#111434","stroke-width","1.5","stroke-miterlimit","10","stroke-linecap","round","stroke-linejoin","round"],["d","M3.99999 6.66658C5.47275 6.66658 6.66666 5.47268 6.66666 3.99992C6.66666 2.52716 5.47275 1.33325 3.99999 1.33325C2.52724 1.33325 1.33333 2.52716 1.33333 3.99992C1.33333 5.47268 2.52724 6.66658 3.99999 6.66658Z","stroke","#111434","stroke-width","1.5","stroke-miterlimit","10","stroke-linecap","round","stroke-linejoin","round"],["d","M12 14.6666C13.4728 14.6666 14.6667 13.4727 14.6667 11.9999C14.6667 10.5272 13.4728 9.33325 12 9.33325C10.5272 9.33325 9.33333 10.5272 9.33333 11.9999C9.33333 13.4727 10.5272 14.6666 12 14.6666Z","stroke","#111434","stroke-width","1.5","stroke-miterlimit","10","stroke-linecap","round","stroke-linejoin","round"],[1,"categories-text"],[1,"count"],[1,"categories-list"],["class","category-content",4,"ngIf"],["class","category-content-empty-state",4,"ngIf"],[1,"search-ui-chat"],[1,"search-bar-chat",3,"mouseenter"],["type","text","placeholder","",3,"ngModel","blur","ngModelChange","keydown.enter","input","focus"],["class","svg-icon","style","padding-top: 21px; padding-right: 13px;",3,"click",4,"ngIf"],["style","padding-top: 21px; padding-right: 13px;",4,"ngIf"],["width","24","height","25","viewBox","0 0 24 25","fill","none",2,"cursor","pointer",3,"click"],["y","0.845703","width","24","height","24","fill","#D9D9D9"],["d","M8.227 17.6813L7.16357 16.6178L10.9367 12.8447L7.16357 9.09663L8.227 8.0332L12.0001 11.8063L15.7482 8.0332L16.8116 9.09663L13.0385 12.8447L16.8116 16.6178L15.7482 17.6813L12.0001 13.9082L8.227 17.6813Z","fill","#B9C0CA"],[1,"category-list",3,"ngClass","click"],["width","14","height","20","viewBox","0 0 14 20","fill","none",2,"min-width","14px"],["d","M7.00001 19.5769C6.49489 19.5769 6.05931 19.4019 5.69328 19.0519C5.32726 18.7019 5.12822 18.2743 5.09616 17.7692H8.90386C8.87179 18.2743 8.67275 18.7019 8.30673 19.0519C7.9407 19.4019 7.50512 19.5769 7.00001 19.5769ZM3.25003 16.3846V14.8846H10.75V16.3846H3.25003ZM3.40386 13.5C2.35644 12.8487 1.52728 11.9977 0.91638 10.9471C0.30548 9.89644 3.05176e-05 8.74741 3.05176e-05 7.49998C3.05176e-05 5.55128 0.679514 3.89743 2.03848 2.53845C3.39746 1.17948 5.05131 0.5 7.00001 0.5C8.94871 0.5 10.6025 1.17948 11.9615 2.53845C13.3205 3.89743 14 5.55128 14 7.49998C14 8.74741 13.6945 9.89644 13.0836 10.9471C12.4727 11.9977 11.6436 12.8487 10.5962 13.5H3.40386ZM3.85001 12H10.15C10.9 11.4666 11.4792 10.8083 11.8875 10.025C12.2958 9.24164 12.5 8.39998 12.5 7.49998C12.5 5.96664 11.9667 4.66664 10.9 3.59998C9.83334 2.53331 8.53334 1.99998 7.00001 1.99998C5.46667 1.99998 4.16667 2.53331 3.10001 3.59998C2.03334 4.66664 1.50001 5.96664 1.50001 7.49998C1.50001 8.39998 1.70417 9.24164 2.11251 10.025C2.52084 10.8083 3.10001 11.4666 3.85001 12Z","fill","#D4D6D8"],[1,"text",3,"matTooltip"],[1,"count",3,"ngClass"],[1,"category-content"],["class","single-prompt",3,"cdkCopyToClipboard","click",4,"ngFor","ngForOf"],[1,"single-prompt",3,"cdkCopyToClipboard","click"],[1,"text",3,"innerHTML"],[1,"copy"],["width","12px","height","12px",3,"src"],[1,"category-content-empty-state"],[1,"svg-icon",2,"padding-top","21px","padding-right","13px",3,"click"],[2,"padding-top","21px","padding-right","13px"],[1,"p-3","chat-wrapper"],[1,"chat-container","p-1"],["chatScrollContainer",""],["class","svg-icon","style","padding-top: 25px; padding-right: 13px;",3,"click",4,"ngIf"],["style","padding-top: 25px; padding-right: 13px;",4,"ngIf"],[1,"prompt-outer-ui"],[1,"prompt-column-align"],[1,"prompt-ui"],[1,"prompt-text"],["imgWidth","32px","imgHeight","32px",3,"oid"],["class","compiling-ui pt-3",4,"ngIf"],["class","compiling-ui",4,"ngIf"],["class","response-ui",4,"ngIf"],[1,"compiling-ui","pt-3"],[1,"chat-loader"],["width","44px","height","44px",3,"src"],[1,"compile-text"],[1,"compiling-ui"],["width","20","height","20","viewBox","0 0 20 20","fill","none","xmlns","http://www.w3.org/2000/svg"],["d","M10 0C4.48 0 0 4.48 0 10C0 15.52 4.48 20 10 20C15.52 20 20 15.52 20 10C20 4.48 15.52 0 10 0ZM8 15L3 10L4.41 8.59L8 12.17L15.59 4.58L17 6L8 15Z","fill","#52C41A"],[1,"response-ui"],[1,"response-data"],[1,"response-text",3,"innerHTML"],["class","icons",4,"ngIf"],[1,"icons"],[1,"right-icons"],["width","14","height","13","viewBox","0 0 14 13",3,"ngStyle","click",4,"ngIf"],["width","14","height","15","viewBox","0 0 14 15","fill","none",1,"icon"],["d","M5.43583 14.5391C4.05875 14.1898 2.93354 13.4604 2.06021 12.3508C1.18674 11.2413 0.75 9.95745 0.75 8.49911C0.75 7.75023 0.880625 7.03418 1.14187 6.35099C1.40312 5.66766 1.76875 5.04078 2.23875 4.47036C2.35417 4.34106 2.49361 4.27425 2.65708 4.26995C2.82056 4.26564 2.97118 4.33245 3.10896 4.47036C3.22438 4.58564 3.28424 4.72634 3.28854 4.89245C3.29271 5.0587 3.23715 5.21495 3.12188 5.3612C2.75646 5.79717 2.47813 6.2828 2.28688 6.81807C2.09563 7.35335 2 7.9137 2 8.49911C2 9.6455 2.3459 10.665 3.03771 11.5577C3.72951 12.4503 4.61493 13.0429 5.69396 13.3356C5.83174 13.3752 5.945 13.4527 6.03375 13.5681C6.12236 13.6834 6.16667 13.8099 6.16667 13.9479C6.16667 14.1562 6.09479 14.3202 5.95104 14.4397C5.80743 14.5595 5.63569 14.5926 5.43583 14.5391ZM8.56417 14.5552C8.36431 14.6086 8.19257 14.5734 8.04896 14.4493C7.90521 14.3254 7.83333 14.1593 7.83333 13.951C7.83333 13.8217 7.87764 13.6994 7.96625 13.5841C8.055 13.4687 8.16826 13.3886 8.30604 13.3437C9.37979 13.0156 10.2639 12.4115 10.9583 11.5312C11.6528 10.6508 12 9.64009 12 8.49911C12 7.11023 11.5139 5.92967 10.5417 4.95745C9.56944 3.98523 8.38889 3.49911 7 3.49911H6.70521L7.39104 4.18495C7.51174 4.30578 7.57208 4.45217 7.57208 4.62411C7.57208 4.7962 7.51174 4.94259 7.39104 5.06328C7.27035 5.18398 7.12396 5.24432 6.95188 5.24432C6.77993 5.24432 6.63361 5.18398 6.51292 5.06328L4.85104 3.40141C4.77299 3.32335 4.71792 3.24106 4.68583 3.15453C4.65389 3.068 4.63792 2.97453 4.63792 2.87411C4.63792 2.7737 4.65389 2.68023 4.68583 2.5937C4.71792 2.50717 4.77299 2.42488 4.85104 2.34682L6.51292 0.684948C6.63361 0.564254 6.77993 0.503906 6.95188 0.503906C7.12396 0.503906 7.27035 0.564254 7.39104 0.684948C7.51174 0.805781 7.57208 0.952171 7.57208 1.12411C7.57208 1.2962 7.51174 1.44259 7.39104 1.56328L6.70521 2.24911H7C8.74361 2.24911 10.2212 2.85488 11.4327 4.06641C12.6442 5.27793 13.25 6.7555 13.25 8.49911C13.25 9.94356 12.8119 11.2213 11.9358 12.3324C11.0597 13.4436 9.93583 14.1845 8.56417 14.5552Z","fill","#1C1B1F"],["width","14","height","13","viewBox","0 0 14 13",3,"ngStyle","click"],["d","M12.7942 4.33308C13.1113 4.33308 13.3914 4.45469 13.6345 4.69791C13.8777 4.94102 13.9993 5.22114 13.9993 5.53825V6.61508C13.9993 6.68508 13.9903 6.76008 13.9723 6.84008C13.9545 6.91997 13.9344 6.99458 13.9122 7.06391L12.0015 11.5722C11.9061 11.7855 11.746 11.9656 11.5212 12.1126C11.2964 12.2596 11.0618 12.3331 10.8175 12.3331H3.80702V4.33308L7.68518 0.488247C7.81774 0.355803 7.97118 0.276748 8.14552 0.251081C8.31985 0.225414 8.4869 0.255303 8.64668 0.340748C8.80657 0.426303 8.92368 0.547692 8.99802 0.704914C9.07235 0.862136 9.08818 1.02497 9.04552 1.19341L8.32752 4.33308H12.7942ZM4.80701 4.75875V11.3331H10.8198C10.8668 11.3331 10.9149 11.3202 10.964 11.2946C11.0132 11.2689 11.0507 11.2262 11.0763 11.1664L12.9993 6.66641V5.53825C12.9993 5.47836 12.9801 5.42919 12.9417 5.39075C12.9032 5.3523 12.8541 5.33308 12.7942 5.33308H7.06352L7.89935 1.67925L4.80701 4.75875ZM1.87118 12.3331C1.53974 12.3331 1.25602 12.2151 1.02002 11.9791C0.784016 11.7431 0.666016 11.4594 0.666016 11.1279V5.53825C0.666016 5.2068 0.784016 4.92308 1.02002 4.68708C1.25602 4.45108 1.53974 4.33308 1.87118 4.33308H3.80702V5.33308H1.87118C1.81129 5.33308 1.76213 5.3523 1.72368 5.39075C1.68524 5.42919 1.66602 5.47836 1.66602 5.53825V11.1279C1.66602 11.1878 1.68524 11.237 1.72368 11.2754C1.76213 11.3139 1.81129 11.3331 1.87118 11.3331H3.80702V12.3331H1.87118Z"],["d","M1.20517 8C0.888056 8 0.607944 7.87839 0.364833 7.63517C0.121611 7.39206 0 7.11194 0 6.79483V5.718C0 5.648 0.00899998 5.573 0.027 5.493C0.0448889 5.41311 0.0649445 5.3385 0.0871667 5.26917L1.99783 0.760834C2.09328 0.547612 2.25339 0.3675 2.47817 0.2205C2.70294 0.0734997 2.9375 0 3.18183 0H10.1923V8L6.31417 11.8448C6.18161 11.9773 6.02817 12.0563 5.85383 12.082C5.6795 12.1077 5.51244 12.0778 5.35267 11.9923C5.19278 11.9068 5.07567 11.7854 5.00133 11.6282C4.927 11.4709 4.91117 11.3081 4.95383 11.1397L5.67183 8H1.20517ZM9.19233 7.57433V1H3.1795C3.1325 1 3.08444 1.01283 3.03533 1.0385C2.98611 1.06417 2.94867 1.10689 2.923 1.16667L1 5.66667V6.79483C1 6.85472 1.01922 6.90389 1.05767 6.94233C1.09611 6.98078 1.14528 7 1.20517 7H6.93583L6.1 10.6538L9.19233 7.57433ZM12.1282 0C12.4596 0 12.7433 0.118 12.9793 0.354C13.2153 0.59 13.3333 0.873722 13.3333 1.20517V6.79483C13.3333 7.12628 13.2153 7.41 12.9793 7.646C12.7433 7.882 12.4596 8 12.1282 8H10.1923V7H12.1282C12.1881 7 12.2372 6.98078 12.2757 6.94233C12.3141 6.90389 12.3333 6.85472 12.3333 6.79483V1.20517C12.3333 1.14528 12.3141 1.09611 12.2757 1.05767C12.2372 1.01922 12.1881 1 12.1282 1H10.1923V0H12.1282Z"],["class","response-data",3,"innerHTML",4,"ngIf","ngIfElse"],["showMarkdown",""],[1,"response-data",3,"innerHTML"],[1,"svg-icon",2,"padding-top","25px","padding-right","13px",3,"click"],[2,"padding-top","25px","padding-right","13px"],[1,"menu-content"],["mat-menu-item","",1,"menu-item",3,"click"],["height","20px","viewBox","0 -960 960 960","width","20px","fill","#a8acb2",1,"menu-icon"],["d","M216-216h51l375-375-51-51-375 375v51Zm-72 72v-153l498-498q11-11 23.84-16 12.83-5 27-5 14.16 0 27.16 5t24 16l51 51q11 11 16 24t5 26.54q0 14.45-5.02 27.54T795-642L297-144H144Zm600-549-51-51 51 51Zm-127.95 76.95L591-642l51 51-25.95-25.05Z"],[1,"menu-divider"],["d","M312-144q-29.7 0-50.85-21.15Q240-186.3 240-216v-480h-48v-72h192v-48h192v48h192v72h-48v479.57Q720-186 698.85-165T648-144H312Zm336-552H312v480h336v-480ZM384-288h72v-336h-72v336Zm120 0h72v-336h-72v336ZM312-696v480-480Z"],["class","menu-icon","height","20px","viewBox","0 -960 960 960","width","20px","fill","#a8acb2",4,"ngIf"],["d","M672-816v72h-48v307l-72-72v-235H408v91l-90-90-30-31v-42h384ZM480-48l-36-36v-228H240v-72l96-96v-42.46L90-768l51-51 678 679-51 51-222-223h-30v228l-36 36ZM342-384h132l-66-66-66 66Zm137-192Zm-71 126Z"],["d","m624-480 96 96v72H516v228l-36 36-36-36v-228H240v-72l96-96v-264h-48v-72h384v72h-48v264Zm-282 96h276l-66-66v-294H408v294l-66 66Zm138 0Z"]],template:function(t,e){1&t&&(p["\u0275\u0275elementStart"](0,"div",0),p["\u0275\u0275elementStart"](1,"div",1),p["\u0275\u0275elementStart"](2,"div",2),p["\u0275\u0275template"](3,L,10,2,"div",3),p["\u0275\u0275template"](4,tt,23,7,"div",4),p["\u0275\u0275elementStart"](5,"div",5),p["\u0275\u0275template"](6,nt,4,2,"ng-container",6),p["\u0275\u0275template"](7,Ft,19,7,"ng-container",6),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](8,"mat-menu",null,7),p["\u0275\u0275template"](10,Rt,15,3,"ng-template",8),p["\u0275\u0275elementEnd"]()),2&t&&(p["\u0275\u0275advance"](3),p["\u0275\u0275property"]("ngIf",!e.isHistoryVisible),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",e.isHistoryVisible),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngIf",e.isThreadLoading||e.isLoading),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!e.isThreadLoading&&!e.isLoading))},directives:[_.NgIf,g.g,g.c,P.a,v.e,v.v,v.y,M.a,_.NgForOf,O.a,_.NgClass,b.a,_.NgStyle,v.q,g.f,w.a,S.a,T.a,g.d],pipes:[E.a,D.a,k.a],styles:[".bg-container[_ngcontent-%COMP%], .text-area[_ngcontent-%COMP%]{display:flex}.text-area[_ngcontent-%COMP%]{height:calc(100vh - var(--chatBotAdjustedHeight) - 249px)}.main-container[_ngcontent-%COMP%]{height:calc(100vh - var(--chatBotAdjustedHeight));width:var(--chatBotWidth);overflow:hidden}.imp-text[_ngcontent-%COMP%]{font-family:var(--FontFamily);font-size:10px;font-weight:500;color:#b9c0ca;text-align:center}.ai-icon[_ngcontent-%COMP%]{width:32px;height:32px}.compiling-ui[_ngcontent-%COMP%]{display:flex;gap:8px}.compiling-ui[_ngcontent-%COMP%]   .ai-icon[_ngcontent-%COMP%]{width:32px;height:32px}.compiling-ui[_ngcontent-%COMP%]   .chat-loader[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;width:-moz-fit-content;width:fit-content;gap:8px;height:44px;border:.5px solid #e8e9ee;background-color:#f6f6f6;padding:0 10px;border-radius:0 8px 8px 8px}.compiling-ui[_ngcontent-%COMP%]   .chat-loader[_ngcontent-%COMP%]   .compile-text[_ngcontent-%COMP%]{font-family:var(--FontFamily);font-size:12px;font-weight:400;color:#515965}.bg-image-1[_ngcontent-%COMP%]{top:85px;right:0}.bg-image-1[_ngcontent-%COMP%], .bg-image-2[_ngcontent-%COMP%]{position:absolute;height:170px;width:60px}.bg-image-2[_ngcontent-%COMP%]{bottom:-20px;transform:rotate(180deg)}.response-ui[_ngcontent-%COMP%]{display:flex;justify-content:space-between;gap:8px;width:98%;padding-top:9px;padding-bottom:8px}.response-ui[_ngcontent-%COMP%]   .ai-icon[_ngcontent-%COMP%]{width:32px;height:32px}.response-ui[_ngcontent-%COMP%]   .response-data[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:8px;padding:0 8px;width:100%;overflow:hidden}.response-ui[_ngcontent-%COMP%]   .response-data[_ngcontent-%COMP%]   .response-text[_ngcontent-%COMP%]{font-family:var(--FontFamily);font-size:14px;font-weight:400;color:#45546e;overflow:auto;line-height:24px}.response-ui[_ngcontent-%COMP%]   .response-data[_ngcontent-%COMP%]   .icons[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;border:1px solid #dadce2;border-radius:6px;padding:6px 16px;gap:8px;width:-moz-fit-content;width:fit-content}.response-ui[_ngcontent-%COMP%]   .response-data[_ngcontent-%COMP%]   .icons[_ngcontent-%COMP%]   .left-icons[_ngcontent-%COMP%]{display:flex;align-items:center;gap:6px}.response-ui[_ngcontent-%COMP%]   .response-data[_ngcontent-%COMP%]   .icons[_ngcontent-%COMP%]   .left-icons[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{font-size:17px;width:17px;height:17px;color:#1c1b1f;cursor:pointer}.response-ui[_ngcontent-%COMP%]   .response-data[_ngcontent-%COMP%]   .icons[_ngcontent-%COMP%]   .left-icons[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{font-family:var(--FontFamily);font-size:12px;font-weight:500;color:#45546e}.response-ui[_ngcontent-%COMP%]   .response-data[_ngcontent-%COMP%]   .icons[_ngcontent-%COMP%]   .right-icons[_ngcontent-%COMP%]{display:flex;align-items:center;gap:10px}.response-ui[_ngcontent-%COMP%]   .response-data[_ngcontent-%COMP%]   .icons[_ngcontent-%COMP%]   .right-icons[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{cursor:pointer}.response-ui[_ngcontent-%COMP%]   .response-data[_ngcontent-%COMP%]   .icons[_ngcontent-%COMP%]   .right-icons[_ngcontent-%COMP%]   .tick-icon-copy[_ngcontent-%COMP%]{max-width:14px}.response-ui[_ngcontent-%COMP%]     .align-items-same-line{display:flex;flex-direction:row;gap:4px;align-items:center}.response-ui[_ngcontent-%COMP%]     .align-items-same-line .error-text-header{font-family:var(--FontFamily);font-weight:700;font-size:14px;color:#fa8c16}.response-ui[_ngcontent-%COMP%]    .error-sub-text{font-family:var(--FontFamily);font-weight:400;font-size:14px;color:#272a47}.chat-container[_ngcontent-%COMP%]{height:calc(100vh - var(--chatBotAdjustedHeight) - 187px);overflow-y:scroll}.chat-container[_ngcontent-%COMP%]::-webkit-scrollbar{display:none}.chat-wrapper[_ngcontent-%COMP%]{display:flex;flex-direction:column;height:calc(100vh - var(--chatBotAdjustedHeight) -60px);overflow:hidden}.prompt-outer-ui[_ngcontent-%COMP%]{display:flex;justify-content:end}.prompt-outer-ui[_ngcontent-%COMP%]   .prompt-column-align[_ngcontent-%COMP%]{display:flex;align-items:end;flex-direction:column;gap:4px;width:100%}.prompt-outer-ui[_ngcontent-%COMP%]   .prompt-column-align[_ngcontent-%COMP%]   .prompt-ui[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:end;gap:8px;width:85%}.prompt-outer-ui[_ngcontent-%COMP%]   .prompt-column-align[_ngcontent-%COMP%]   .prompt-ui[_ngcontent-%COMP%]   .prompt-text[_ngcontent-%COMP%]{font-family:var(--FontFamily);font-size:14px;font-weight:400;color:#45546e;border-radius:8px;padding:10px;max-width:100%;background-color:#f6f6f6;text-align:justify;word-wrap:break-word;border:1px solid #e8e9ee;line-height:24px}.prompt-outer-ui[_ngcontent-%COMP%]   .prompt-column-align[_ngcontent-%COMP%]   .error-msg[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px;font-family:var(--FontFamily);font-size:12px;font-weight:400;color:#45546e}.prompt-outer-ui[_ngcontent-%COMP%]   .prompt-column-align[_ngcontent-%COMP%]   .error-msg[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{font-size:16px;width:16px;height:16px;color:#ee4961}.prompt-library-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:16px;height:calc(100vh - var(--chatBotAdjustedHeight) - 80px);overflow:hidden;position:relative;z-index:1}.prompt-library-content[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between}.prompt-library-content[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .prompt-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.prompt-library-content[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .prompt-title[_ngcontent-%COMP%]   .prompt-title-icon[_ngcontent-%COMP%]{font-size:16px;width:16px;height:16px;color:#45546e;cursor:pointer}.prompt-library-content[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .prompt-title[_ngcontent-%COMP%]   .prompt-title-text[_ngcontent-%COMP%]{font-family:var(--FontFamily);font-size:16px;font-weight:600;color:#ee4961}.prompt-library-content[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px;border:1px solid #b9c0ca;border-radius:8px;width:25%;padding:2px 12px;height:28px;margin-right:35px}.prompt-library-content[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{height:100%;width:100%;font-family:var(--FontFamily);font-size:12px;font-weight:400;color:#45546e;outline:none;border:none}.prompt-library-content[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{font-family:var(--FontFamily);font-size:12px;font-weight:400;color:#b9c0ca}.prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{display:flex;align-items:center;width:100%;gap:2%}.prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .categories[_ngcontent-%COMP%]{display:flex;flex-direction:column;height:calc(var(--kebsChatbotContentHeight) - 55px);border:.5px solid #e8e9ee;border-radius:8px;width:20%;padding:8px;gap:8px}.prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .categories[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{display:flex;justify-content:start;align-items:center;gap:8px;padding-bottom:8px;border-bottom:.5px solid #dadce2;margin-bottom:8px}.prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .categories[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .categories-text[_ngcontent-%COMP%]{font-family:var(--FontFamily);font-size:12px;font-weight:400;color:#111434;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;max-width:70%}.prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .categories[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .count[_ngcontent-%COMP%]{background-color:#45546e;padding:2px 6px;border-radius:10px;font-family:var(--FontFamily);font-size:10px;font-weight:500;color:#fff}.prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .categories[_ngcontent-%COMP%]   .categories-list[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:8px;height:calc(var(--kebsChatbotContentHeight) - 68px);overflow-y:auto;padding-right:2%}.prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .categories[_ngcontent-%COMP%]   .categories-list[_ngcontent-%COMP%]   .category-list[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;cursor:pointer;border-radius:4px;padding:4px}.prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .categories[_ngcontent-%COMP%]   .categories-list[_ngcontent-%COMP%]   .category-list[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{font-family:var(--FontFamily);font-size:12px;font-weight:400;color:#111434;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;max-width:70%}.prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .categories[_ngcontent-%COMP%]   .categories-list[_ngcontent-%COMP%]   .category-list[_ngcontent-%COMP%]   .count[_ngcontent-%COMP%]{padding:2px 6px;border-radius:10px;font-family:var(--FontFamily);font-size:10px;font-weight:500;color:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%);background:linear-gradient(270deg,rgba(239,74,97,.1),rgba(242,122,108,.1) 105.29%)}.prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .categories[_ngcontent-%COMP%]   .categories-list[_ngcontent-%COMP%]   .selected-category-list[_ngcontent-%COMP%]{background:linear-gradient(270deg,rgba(239,74,97,.1),rgba(242,122,108,.1) 105.29%)}.prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .categories[_ngcontent-%COMP%]   .categories-list[_ngcontent-%COMP%]   .selected-category-list[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%]{color:#fff;background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%)}.prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .categories[_ngcontent-%COMP%]   .categories-list[_ngcontent-%COMP%]::-webkit-scrollbar{width:6px!important;height:6px!important}.prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .categories[_ngcontent-%COMP%]   .categories-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{min-height:40px!important;max-height:40px!important}.prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .category-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;height:calc(var(--kebsChatbotContentHeight) - 55px);gap:6px;width:77%;padding-right:1%;overflow-y:auto}.prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .category-content[_ngcontent-%COMP%]   .single-prompt[_ngcontent-%COMP%]{display:flex;flex-direction:row;align-items:start;justify-content:space-between;gap:16px;border:.5px solid #dadce2;border-radius:8px;padding:8px;cursor:pointer}.prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .category-content[_ngcontent-%COMP%]   .single-prompt[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{font-family:var(--FontFamily);font-size:12px;font-weight:400;color:#8b95a5;text-align:justify;line-height:2.5}.prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .category-content[_ngcontent-%COMP%]   .single-prompt[_ngcontent-%COMP%]   .copy[_ngcontent-%COMP%]{height:10px;display:none;align-items:center;justify-content:end;gap:4px;font-family:var(--FontFamily);font-size:8px;font-weight:500;color:#ef4a61}.prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .category-content[_ngcontent-%COMP%]   .single-prompt[_ngcontent-%COMP%]:hover{border:.5px solid #ef4a61;background:linear-gradient(270deg,rgba(239,74,97,.05),rgba(242,122,108,.05) 105.29%)}.prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .category-content[_ngcontent-%COMP%]   .single-prompt[_ngcontent-%COMP%]:hover   .copy[_ngcontent-%COMP%]{display:flex}.prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .category-content[_ngcontent-%COMP%]::-webkit-scrollbar{width:6px!important;height:6px!important}.prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .category-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{min-height:40px!important;max-height:40px!important}.prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .category-content-empty-state[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;height:calc(var(--kebsChatbotContentHeight) - 20px);gap:6px;width:63%;padding-right:1%;overflow:hidden;font-family:var(--FontFamily);font-size:14px;font-weight:700;color:#45546e}.search-ui[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;gap:8px;border:1px solid #b9c0ca;border-radius:60px;height:36px;width:100%;padding-right:4px;z-index:10;position:relative;background:#fff}.search-ui[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]{width:-webkit-fill-available;height:100%;padding:4px 8px}.search-ui[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{height:100%;width:100%;font-family:var(--FontFamily);font-size:12px;font-weight:400;color:#45546e;outline:none;border:none}.search-ui[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{font-family:var(--FontFamily);font-size:12px;font-weight:400;color:#b9c0ca}.new-chat-btn[_ngcontent-%COMP%]{background-color:rgba(238,73,97,.10196078431372549);color:#ee4961;border:none;border-radius:10px;padding:10px 20px;font-size:14px;font-weight:700;display:flex;align-items:center;gap:10px;cursor:pointer;outline:none;transition:background-color .3s ease;width:140px;height:48px;font-family:var(--FontFamily)}.search-ui-new[_ngcontent-%COMP%]{display:flex;flex-direction:column;border:1px solid #b9c0ca;border-radius:32px;width:100%;padding:8px;z-index:10;position:relative;background:#fff}.search-ui-new[_ngcontent-%COMP%]   .search-bar-new[_ngcontent-%COMP%]{overflow-y:auto;width:100%;padding:4px 8px}.search-ui-new[_ngcontent-%COMP%]   .search-bar-new[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{max-height:120px;width:100%;resize:none;overflow-y:auto;font-family:var(--FontFamily);font-size:16px;font-weight:400;color:#45546e;outline:none;border:none}.header-content-alignment[_ngcontent-%COMP%]{display:flex;gap:6px;align-items:center}.header-content-alignment[_ngcontent-%COMP%]   .ai-img-header[_ngcontent-%COMP%]{height:60.12px;width:58.5px}.align-items-column[_ngcontent-%COMP%]{display:flex;flex-direction:column;row-gap:0}.align-items-column[_ngcontent-%COMP%]   .main-text[_ngcontent-%COMP%]{font-family:var(--FontFamily);font-weight:600;font-size:22px;color:#ee4961;padding-bottom:9px;background:linear-gradient(270deg,#ffc12f -5.06%,#f16567 31.08%,#f16567 100.33%);-webkit-background-clip:text;-webkit-text-fill-color:transparent}.align-items-column[_ngcontent-%COMP%]   .main-text.header-content[_ngcontent-%COMP%]{font-weight:700;font-size:21px;height:30px}.align-items-column[_ngcontent-%COMP%]   .sub-text[_ngcontent-%COMP%]{font-family:var(--FontFamily);font-weight:400;font-size:17px;color:#b9c0ca;height:20px}.align-items-column[_ngcontent-%COMP%]   .sub-text.header-content[_ngcontent-%COMP%]{font-weight:700;font-size:23px}.history-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:20vw;height:calc(100vh - var(--chatBotAdjustedHeight));border-right:1px solid #e8e9ee;background-color:#fff3f4}.history-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;height:59px;padding:20px 18px 0}.history-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .main-title[_ngcontent-%COMP%]{font-family:var(--FontFamily);font-weight:700;font-size:14px;color:#111434}.history-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .svg-icon[_ngcontent-%COMP%]{cursor:pointer}.history-container[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .ai-icon-history[_ngcontent-%COMP%]{width:69px;height:32px}.history-container[_ngcontent-%COMP%]   .search-ui-history[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;gap:8px;border:.5px solid #d4d6d8;border-radius:8px;height:28px;padding-right:4px;margin:28px 16px;background-color:#fff}.history-container[_ngcontent-%COMP%]   .search-ui-history[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]{width:-webkit-fill-available;height:100%;padding:2px 6px}.history-container[_ngcontent-%COMP%]   .search-ui-history[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{height:100%;width:100%;font-family:var(--FontFamily);font-size:10px;font-weight:400;color:#45546e;outline:none;border:none;background-color:#f7f9fb}.history-container[_ngcontent-%COMP%]   .search-ui-history[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{font-family:var(--FontFamily);font-size:10px;font-weight:400;color:#d4d6d8}.history-container[_ngcontent-%COMP%]   .search-ui-history[_ngcontent-%COMP%]   .svg-icon[_ngcontent-%COMP%]{cursor:pointer}.history-container[_ngcontent-%COMP%]   .search-ui-history[_ngcontent-%COMP%]   .close-icon[_ngcontent-%COMP%]{height:12px;width:12px;font-size:12px;color:#7d838b;cursor:pointer}.history-container[_ngcontent-%COMP%]   .no-history[_ngcontent-%COMP%]{font-family:var(--FontFamily);font-size:10px;font-weight:400;color:#7d838b;width:100%;text-align:center}.history-container[_ngcontent-%COMP%]   .loading-state[_ngcontent-%COMP%]{display:flex;flex-direction:column;overflow:hidden;padding:0 8px 0 16px;margin-right:8px;gap:10px}.history-container[_ngcontent-%COMP%]   .loading-state[_ngcontent-%COMP%]   .loader[_ngcontent-%COMP%]{min-height:24px;width:100%;border-radius:6px;background:linear-gradient(90deg,#cdcdcd -24.18%,#f0f0f0 50.26%,#efefef 114.84%)}.history-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{display:flex;flex-direction:column;overflow-y:auto;padding:0 8px 0 16px;margin-right:8px;gap:10px}.history-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]{color:#d4d6d8}.history-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .single-date-log[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:4px}.history-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .single-date-log[_ngcontent-%COMP%]   .date[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-family:var(--FontFamily);font-weight:700;font-size:12px;color:#515965}.history-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .single-date-log[_ngcontent-%COMP%]   .pin-icon[_ngcontent-%COMP%]{font-size:14px;width:14px;height:14px}.history-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .single-date-log[_ngcontent-%COMP%]   .single-history[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;min-height:24px;gap:8px;padding:4px}.history-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .single-date-log[_ngcontent-%COMP%]   .single-history.thread-selected[_ngcontent-%COMP%]{border-radius:4px;background-color:rgba(238,73,97,.10196078431372549)}.history-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .single-date-log[_ngcontent-%COMP%]   .single-history[_ngcontent-%COMP%]   .history-text[_ngcontent-%COMP%]{font-family:var(--FontFamily);font-weight:400;font-size:12px;color:#515965;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;cursor:pointer;width:99%}.history-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .single-date-log[_ngcontent-%COMP%]   .single-history[_ngcontent-%COMP%]   .selected-history-text[_ngcontent-%COMP%]{font-family:var(--FontFamily);font-weight:700;font-size:12px;color:#ee4961;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;cursor:pointer;width:99%}.history-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .single-date-log[_ngcontent-%COMP%]   .single-history[_ngcontent-%COMP%]   .inline-edit[_ngcontent-%COMP%]{width:100%;height:24px}.history-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .single-date-log[_ngcontent-%COMP%]   .single-history[_ngcontent-%COMP%]   .inline-edit[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{height:100%;width:100%;font-family:var(--FontFamily);font-size:12px;font-weight:400;color:#515965;outline:none;border:.7px solid #1890ff;border-radius:4px}.history-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .single-date-log[_ngcontent-%COMP%]   .single-history[_ngcontent-%COMP%]   .inline-edit[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{font-family:var(--FontFamily);font-size:10px;font-weight:400;color:#6e7b8f}.history-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .single-date-log[_ngcontent-%COMP%]   .single-history[_ngcontent-%COMP%]   .svg-icon[_ngcontent-%COMP%]{cursor:pointer}.history-container[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;gap:8px;height:calc(var(--kebsChatbotContentHeight) - 40px)}.history-container[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .gif[_ngcontent-%COMP%]{height:80px;width:80px}.history-container[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .carousel-text[_ngcontent-%COMP%]{position:relative;height:50px;width:100%;overflow:hidden}.history-container[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]{width:100%;display:flex;flex-direction:column;align-items:center;justify-content:center;transition:transform .5s ease-in,opacity .5s ease-in;position:absolute;opacity:1;transition:opacity 2s ease}.history-container[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]   .main-text[_ngcontent-%COMP%]{font-family:var(--FontFamily);font-size:14px;font-weight:400;color:#7d838b}.history-container[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]   .sub-text[_ngcontent-%COMP%]{font-family:var(--FontFamily);font-size:14px;font-weight:600;color:#ee4961}.history-container[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .slide[_ngcontent-%COMP%]{animation:slideUp 2s ease forwards}.history-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]::-webkit-scrollbar{width:6px!important;height:6px!important}.history-container[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{min-height:40px!important;max-height:40px!important}.search-bar[_ngcontent-%COMP%]{width:-webkit-fill-available;height:100%;padding:2px 6px}.search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{height:100%;width:100%;color:#45546e;outline:none;border:none;background-color:#f7f9fb}.search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{font-family:var(--FontFamily);font-size:10px;font-weight:400}.search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{color:#d4d6d8}.svg-icon[_ngcontent-%COMP%]{right:10px;bottom:10px;cursor:pointer}.disabled-icon[_ngcontent-%COMP%]{cursor:not-allowed;opacity:.6}.chatbot-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;min-height:calc(100vh - var(--chatBotAdjustedHeight));width:var(--kebsChatBotContentWidth);height:calc(100vh - var(--chatBotAdjustedHeight))}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding:16px 24px;background-color:#fff}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-header[_ngcontent-%COMP%]   .align-items-with-gap[_ngcontent-%COMP%]{display:flex;align-items:center;gap:10px}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-header[_ngcontent-%COMP%]   .align-items-with-gap[_ngcontent-%COMP%]   .svg-icon[_ngcontent-%COMP%]{cursor:pointer}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-header[_ngcontent-%COMP%]   .align-items-with-gap[_ngcontent-%COMP%]   .ai-icon[_ngcontent-%COMP%]{width:29px;height:30px}.chatbot-container[_ngcontent-%COMP%]   .svg-icon[_ngcontent-%COMP%]{cursor:pointer}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]{height:var(--kebsChatbotContentHeight);overflow-y:auto;position:relative}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .history-content[_ngcontent-%COMP%]{display:flex;justify-content:space-between;padding:16px 24px;background-color:#fff3f4;height:calc(var(--kebsChatbotContentHeight) + 52px)}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;gap:8px;height:calc(var(--kebsChatbotContentHeight) - 40px)}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .gif[_ngcontent-%COMP%]{height:80px;width:80px}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .carousel-text[_ngcontent-%COMP%]{position:relative;height:50px;width:100%;overflow:hidden}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]{width:100%;display:flex;flex-direction:column;align-items:center;justify-content:center;transition:transform .5s ease-in,opacity .5s ease-in;position:absolute;opacity:1;transition:opacity 2s ease}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]   .main-text[_ngcontent-%COMP%]{font-family:var(--FontFamily);font-size:14px;font-weight:400;color:#7d838b}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]   .sub-text[_ngcontent-%COMP%]{font-family:var(--FontFamily);font-size:14px;font-weight:600;color:#ee4961}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .loading-container[_ngcontent-%COMP%]   .slide[_ngcontent-%COMP%]{animation:slideUp 2s ease forwards}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:16px;height:calc(var(--kebsChatbotContentHeight) + 24px);overflow:hidden;position:relative;z-index:1}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .prompt-title[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .prompt-title[_ngcontent-%COMP%]   .prompt-title-icon[_ngcontent-%COMP%]{font-size:16px;width:16px;height:16px;color:#45546e;cursor:pointer}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .prompt-title[_ngcontent-%COMP%]   .prompt-title-text[_ngcontent-%COMP%]{font-family:var(--FontFamily);font-size:16px;font-weight:600;color:#ee4961}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px;border:1px solid #b9c0ca;border-radius:60px;width:40%;padding:2px 12px;height:28px}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{height:100%;width:100%;font-family:var(--FontFamily);font-size:12px;font-weight:400;color:#45546e;outline:none;border:none}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{font-family:var(--FontFamily);font-size:12px;font-weight:400;color:#b9c0ca}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{display:flex;align-items:center;width:100%;gap:2%}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .categories[_ngcontent-%COMP%]{display:flex;flex-direction:column;height:calc(var(--kebsChatbotContentHeight) - 20px);border:.5px solid #e8e9ee;border-radius:8px;width:35%;padding:8px;gap:8px}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .categories[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{display:flex;justify-content:start;align-items:center;gap:8px;padding-bottom:8px;border-bottom:.5px solid #dadce2;margin-bottom:8px}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .categories[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .categories-text[_ngcontent-%COMP%]{font-family:var(--FontFamily);font-size:12px;font-weight:400;color:#111434;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;max-width:70%}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .categories[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .count[_ngcontent-%COMP%]{background-color:#45546e;padding:2px 6px;border-radius:10px;font-family:var(--FontFamily);font-size:10px;font-weight:500;color:#fff}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .categories[_ngcontent-%COMP%]   .categories-list[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:8px;height:calc(var(--kebsChatbotContentHeight) - 68px);overflow-y:auto;padding-right:2%}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .categories[_ngcontent-%COMP%]   .categories-list[_ngcontent-%COMP%]   .category-list[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;cursor:pointer;border-radius:4px;padding:4px}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .categories[_ngcontent-%COMP%]   .categories-list[_ngcontent-%COMP%]   .category-list[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{font-family:var(--FontFamily);font-size:12px;font-weight:400;color:#111434;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;max-width:70%}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .categories[_ngcontent-%COMP%]   .categories-list[_ngcontent-%COMP%]   .category-list[_ngcontent-%COMP%]   .count[_ngcontent-%COMP%]{padding:2px 6px;border-radius:10px;font-family:var(--FontFamily);font-size:10px;font-weight:500;color:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%);background:linear-gradient(270deg,rgba(239,74,97,.1),rgba(242,122,108,.1) 105.29%)}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .categories[_ngcontent-%COMP%]   .categories-list[_ngcontent-%COMP%]   .selected-category-list[_ngcontent-%COMP%]{background:linear-gradient(270deg,rgba(239,74,97,.1),rgba(242,122,108,.1) 105.29%)}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .categories[_ngcontent-%COMP%]   .categories-list[_ngcontent-%COMP%]   .selected-category-list[_ngcontent-%COMP%]   .selected-count[_ngcontent-%COMP%]{color:#fff;background:linear-gradient(270deg,#ef4a61,#f27a6c 105.29%)}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .categories[_ngcontent-%COMP%]   .categories-list[_ngcontent-%COMP%]::-webkit-scrollbar{width:6px!important;height:6px!important}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .categories[_ngcontent-%COMP%]   .categories-list[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{min-height:40px!important;max-height:40px!important}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .category-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;height:calc(var(--kebsChatbotContentHeight) - 20px);gap:6px;width:63%;padding-right:1%;overflow-y:auto}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .category-content[_ngcontent-%COMP%]   .single-prompt[_ngcontent-%COMP%]{display:flex;flex-direction:row;align-items:start;justify-content:space-between;gap:16px;border:.5px solid #dadce2;border-radius:4px;padding:8px;cursor:pointer}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .category-content[_ngcontent-%COMP%]   .single-prompt[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{font-family:var(--FontFamily);font-size:10px;font-weight:400;color:#8b95a5;text-align:justify;line-height:1.5}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .category-content[_ngcontent-%COMP%]   .single-prompt[_ngcontent-%COMP%]   .copy[_ngcontent-%COMP%]{height:10px;display:none;align-items:center;justify-content:end;gap:4px;font-family:var(--FontFamily);font-size:8px;font-weight:500;color:#ef4a61}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .category-content[_ngcontent-%COMP%]   .single-prompt[_ngcontent-%COMP%]:hover{border:.5px solid #ef4a61;background:linear-gradient(270deg,rgba(239,74,97,.05),rgba(242,122,108,.05) 105.29%)}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .category-content[_ngcontent-%COMP%]   .single-prompt[_ngcontent-%COMP%]:hover   .copy[_ngcontent-%COMP%]{display:flex}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .category-content[_ngcontent-%COMP%]::-webkit-scrollbar{width:6px!important;height:6px!important}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .category-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{min-height:40px!important;max-height:40px!important}.chatbot-container[_ngcontent-%COMP%]   .chatbot-container-content[_ngcontent-%COMP%]   .prompt-library-content[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]   .category-content-empty-state[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;height:calc(var(--kebsChatbotContentHeight) - 20px);gap:6px;width:63%;padding-right:1%;overflow:hidden;font-family:var(--FontFamily);font-size:14px;font-weight:700;color:#45546e}.menu-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;padding:0 12px;gap:4px}.menu-content[_ngcontent-%COMP%]   .menu-item[_ngcontent-%COMP%]{font-family:var(--FontFamily);font-size:10px;font-weight:400;color:#515965;height:20px;display:flex;align-items:center;gap:12px;padding:0}.menu-content[_ngcontent-%COMP%]   .menu-icon[_ngcontent-%COMP%]{font-size:14px;width:14px;height:14px;color:#a8acb2;margin:0}.menu-content[_ngcontent-%COMP%]   .menu-divider[_ngcontent-%COMP%]{color:#e8e9ee}.loading-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;gap:8px;height:calc(100vh - var(--chatBotAdjustedHeight))}.loading-container[_ngcontent-%COMP%]   .gif[_ngcontent-%COMP%]{height:80px;width:80px}.loading-container[_ngcontent-%COMP%]   .carousel-text[_ngcontent-%COMP%]{position:relative;height:50px;width:100%;overflow:hidden}.loading-container[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]{width:100%;display:flex;flex-direction:column;align-items:center;justify-content:center;transition:transform .5s ease-in,opacity .5s ease-in;position:absolute;opacity:1;transition:opacity 2s ease}.loading-container[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]   .main-text[_ngcontent-%COMP%]{font-family:var(--FontFamily);font-size:14px;font-weight:400;color:#7d838b}.loading-container[_ngcontent-%COMP%]   .text-content[_ngcontent-%COMP%]   .sub-text[_ngcontent-%COMP%]{font-family:var(--FontFamily);font-size:14px;font-weight:600;color:#ee4961}.loading-container[_ngcontent-%COMP%]   .slide[_ngcontent-%COMP%]{animation:slideUp 2s ease forwards}@keyframes slideUp{0%{transform:translateY(100%);opacity:0}20%{transform:translateY(0);opacity:1}80%{transform:translateY(0);opacity:1}to{transform:translateY(-100%);opacity:0}}.search-overlay-section[_ngcontent-%COMP%]{display:inline-block;position:relative;width:100%}.search-overlay-section[_ngcontent-%COMP%]   .overlay[_ngcontent-%COMP%]{color:#515965;align-items:center;max-height:181px;overflow:hidden;padding-top:20px;bottom:20px;left:0;width:100%;background:#fff;border-radius:8px;box-shadow:0 2px 1px 0 rgba(0,0,0,.12156862745098039);border:1px solid #e8e9ee;z-index:5;min-height:62px;overflow-y:auto;display:flex;position:absolute;pointer-events:all!important}.search-overlay-section[_ngcontent-%COMP%]   .overlay[_ngcontent-%COMP%]   .prompt-content[_ngcontent-%COMP%]{overflow-y:auto;display:flex;flex-direction:column;padding:0 2px 24px 8px;margin-right:2px;gap:4px;max-height:122px;width:100%;position:relative}.search-overlay-section[_ngcontent-%COMP%]   .overlay[_ngcontent-%COMP%]   .prompt-content[_ngcontent-%COMP%]   .prompt-text[_ngcontent-%COMP%]{font-family:var(--FontFamily);font-weight:400;font-size:12px;padding:6px;border-radius:3px;cursor:pointer;width:100%}.search-overlay-section[_ngcontent-%COMP%]   .overlay[_ngcontent-%COMP%]   .prompt-content[_ngcontent-%COMP%]   .prompt-text[_ngcontent-%COMP%]:hover{background:#ffecee}.search-overlay-section[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;gap:8px;border:1px solid #b9c0ca;border-radius:60px;height:36px;width:100%;padding-right:4px;z-index:10;position:relative;background:#fff}.search-overlay-section[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]{width:-webkit-fill-available;height:100%;padding:4px 8px}.search-overlay-section[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{height:100%;width:100%;font-family:var(--FontFamily);font-size:12px;font-weight:400;color:#45546e;outline:none;border:none}.search-overlay-section[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{font-family:var(--FontFamily);font-size:12px;font-weight:400;color:#b9c0ca}.search-overlay-section[_ngcontent-%COMP%]   .overlay[_ngcontent-%COMP%]::-webkit-scrollbar, .search-overlay-section[_ngcontent-%COMP%]   .prompt-content[_ngcontent-%COMP%]::-webkit-scrollbar{width:6px!important;height:6px!important}.search-overlay-section[_ngcontent-%COMP%]   .overlay[_ngcontent-%COMP%]::-webkit-scrollbar-track, .search-overlay-section[_ngcontent-%COMP%]   .prompt-content[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:#f1f1f1;border-radius:3px;height:68px!important}.search-overlay-section[_ngcontent-%COMP%]   .overlay[_ngcontent-%COMP%]::-webkit-scrollbar-thumb, .search-overlay-section[_ngcontent-%COMP%]   .prompt-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#b9c0ca;border-radius:3px;min-height:50px}.search-overlay-section[_ngcontent-%COMP%]   .overlay[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover, .search-overlay-section[_ngcontent-%COMP%]   .prompt-content[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#999}.search-ui-chat[_ngcontent-%COMP%]{display:flex;border:1px solid #b9c0ca;border-radius:23px;width:100%;padding:8px;z-index:10;position:relative;background:#fff}.search-ui-chat[_ngcontent-%COMP%]   .search-bar-chat[_ngcontent-%COMP%]{overflow-y:auto;width:100%;padding:4px 8px}.search-ui-chat[_ngcontent-%COMP%]   .search-bar-chat[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{max-height:120px;width:100%;resize:none;overflow-y:auto;font-family:var(--FontFamily);font-size:16px;font-weight:400;color:#45546e;outline:none;border:none}"]}),t})()},WM9j:function(t,e,n){"use strict";for(var i=n("BuRe"),r=[],o=0;o<256;++o)r.push((o+256).toString(16).substr(1));e.a=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=(r[t[e+0]]+r[t[e+1]]+r[t[e+2]]+r[t[e+3]]+"-"+r[t[e+4]]+r[t[e+5]]+"-"+r[t[e+6]]+r[t[e+7]]+"-"+r[t[e+8]]+r[t[e+9]]+"-"+r[t[e+10]]+r[t[e+11]]+r[t[e+12]]+r[t[e+13]]+r[t[e+14]]+r[t[e+15]]).toLowerCase();if(!Object(i.a)(n))throw TypeError("Stringified UUID is invalid");return n}}}]);