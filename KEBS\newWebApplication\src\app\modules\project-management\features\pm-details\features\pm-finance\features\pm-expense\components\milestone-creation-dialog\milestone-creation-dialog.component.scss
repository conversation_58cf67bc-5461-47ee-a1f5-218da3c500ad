.milestone-dialog-container {
  min-width: 500px;
  max-width: 600px;
}

.dialog-content {
  padding: 20px 0;
}

.expense-summary {
  margin-bottom: 24px;
}

.summary-card {
  background-color: #f5f5f5;
  border-left: 4px solid #2196f3;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.label {
  font-weight: 500;
  color: #666;
}

.value {
  font-weight: 600;
  color: #333;
}

.total-amount {
  color: #2196f3;
  font-size: 1.1em;
}

.milestone-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.full-width {
  width: 100%;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  
  p {
    margin-top: 16px;
    color: #666;
  }
}

.dialog-actions {
  padding: 16px 24px;
  border-top: 1px solid #e0e0e0;
  margin-top: 16px;
  
  button {
    margin-left: 8px;
  }
}

// Responsive design
@media (max-width: 600px) {
  .milestone-dialog-container {
    min-width: 90vw;
    max-width: 90vw;
  }
}
