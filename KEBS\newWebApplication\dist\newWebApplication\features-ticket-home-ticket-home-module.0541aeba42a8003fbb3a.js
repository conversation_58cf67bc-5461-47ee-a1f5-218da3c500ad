(window.webpackJsonp=window.webpackJsonp||[]).push([[665,634,858,861,977,981],{"1rg7":function(t,e,i){"use strict";i.r(e),i.d(e,"TicketHomeModule",(function(){return nt}));var s=i("ofXK"),a=i("3Pt+"),r=i("tyNb"),n=i("mrSG"),o=i("fXoL"),d=i("1G5W"),l=i("XNiG"),c=i("xG9w"),h=i("wd/R"),u=i("0IaG"),p=i("yu80"),m=i("HmYF"),g=i("BVzC"),f=i("TC2u"),y=i("GnQ3"),v=i("LcQX"),k=i("xi/V"),C=i("Xa2L"),_=i("bTqV");const b=["ticketCardViewContainer"];function T(t,e){1&t&&(o["\u0275\u0275elementStart"](0,"div",4),o["\u0275\u0275element"](1,"div",5),o["\u0275\u0275elementStart"](2,"div",6),o["\u0275\u0275element"](3,"mat-spinner",7),o["\u0275\u0275elementEnd"](),o["\u0275\u0275element"](4,"div",5),o["\u0275\u0275elementEnd"]())}function S(t,e){if(1&t){const t=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"button",14),o["\u0275\u0275listener"]("click",(function(){return o["\u0275\u0275restoreView"](t),o["\u0275\u0275nextContext"](2).openTicketCreationForm()})),o["\u0275\u0275text"](1," Create new Ticket "),o["\u0275\u0275elementEnd"]()}}function w(t,e){if(1&t&&(o["\u0275\u0275elementStart"](0,"div",8),o["\u0275\u0275elementStart"](1,"div",9),o["\u0275\u0275elementStart"](2,"span",10),o["\u0275\u0275text"](3),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](4,"div",11),o["\u0275\u0275element"](5,"img",12),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](6,S,2,0,"button",13),o["\u0275\u0275elementEnd"]()),2&t){const t=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](3),o["\u0275\u0275textInterpolate1"]("",t.showFlagged?"No Flagged Tickets":"No Tickets found !"," "),o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("ngIf",!t.showFlagged&&t.canUserCreateTicket)}}let A=(()=>{class t{constructor(t,e,i,s,r,n,o,d,c){this.dialog=t,this.compiler=e,this.router=i,this._ticket=s,this.excelService=r,this._ErrorService=n,this.tsInlineEntryPopupService=o,this.udrfService=d,this.utilityService=c,this.ticketList=[],this._onDestroy=new l.b,this.searchTextControl=new a.j,this.currentUser=this._ticket.currentUser,this.isTicketsLoading=!0,this.showFlagged=!1,this.isFilterApplied=!1,this.isSearching=!1,this.canUserCreateTicket=!1,this.flaggedTickets=[],this.statusList=[],this.typeList=[],this.applicationId=95,this.newReleases=[],this.dataTypeArray=[{dataType:"Cancelled",dataTypeCode:"C",dataTypeValue:"0",originalValue:0,isActive:!1,isVisible:!1,isDisabled:!1,clicked:!1,cardType:"status",statusColor:"#725151"},{dataType:"Closed",dataTypeCode:"CD",dataTypeValue:"0",originalValue:0,isActive:!1,isVisible:!0,isDisabled:!1,clicked:!1,cardType:"status",statusColor:"#139940"},{dataType:"Completed",dataTypeCode:"CP",dataTypeValue:"0",originalValue:0,isActive:!1,isVisible:!0,isDisabled:!1,clicked:!1,cardType:"status",statusColor:"#BADC58"},{dataType:"Customer Action",dataTypeCode:"CA",dataTypeValue:"0",originalValue:0,isActive:!1,isVisible:!1,isDisabled:!1,clicked:!1,cardType:"status",statusColor:"#D55127"},{dataType:"In Process Functional",dataTypeCode:"INF",dataTypeValue:"0",originalValue:0,isActive:!1,isVisible:!0,isDisabled:!1,clicked:!1,cardType:"status",statusColor:"#FFA502"},{dataType:"In Process Technical",dataTypeCode:"INP",dataTypeValue:"0",originalValue:0,isActive:!1,isVisible:!1,isDisabled:!1,clicked:!1,cardType:"status",statusColor:"#FFA502"},{dataType:"IT Approved",dataTypeCode:"ITA",dataTypeValue:"0",originalValue:0,isActive:!1,isVisible:!1,isDisabled:!1,clicked:!1,cardType:"status",statusColor:"#54AC6C"},{dataType:"IT Rejected",dataTypeCode:"ITR",dataTypeValue:"0",originalValue:0,isActive:!1,isVisible:!1,isDisabled:!1,clicked:!1,cardType:"status",statusColor:"#C54357"},{dataType:"On Hold",dataTypeCode:"OH",dataTypeValue:"0",originalValue:0,isActive:!1,isVisible:!0,isDisabled:!1,clicked:!1,cardType:"status",statusColor:"#7C7C7C"},{dataType:"Open",dataTypeCode:"OP",dataTypeValue:"0",originalValue:0,isActive:!1,isVisible:!0,isDisabled:!1,clicked:!1,cardType:"status",statusColor:"#E0E0E0"},{dataType:"Response",dataTypeCode:"R",dataTypeValue:"0",originalValue:0,isActive:!1,isVisible:!0,isDisabled:!1,clicked:!1,cardType:"status",statusColor:"#FBD023"},{dataType:"Sent to SAP",dataTypeCode:"STS",dataTypeValue:"0",originalValue:0,isActive:!1,isVisible:!1,isDisabled:!1,clicked:!1,cardType:"status",statusColor:"#079992"},{dataType:"UAT",dataTypeCode:"UT",dataTypeValue:"0",originalValue:0,isActive:!1,isVisible:!1,isDisabled:!1,clicked:!1,cardType:"status",statusColor:"#D55127"},{dataType:"Waiting for Input",dataTypeCode:"WFI",dataTypeValue:"0",originalValue:0,isActive:!1,isVisible:!1,isDisabled:!1,clicked:!1,cardType:"status",statusColor:"#91AECB"},{dataType:"Waiting for IT Approval",dataTypeCode:"WFIA",dataTypeValue:"0",originalValue:0,isActive:!1,isVisible:!1,isDisabled:!1,clicked:!1,cardType:"status",statusColor:"#079992"}],this.categorisedDataTypeArray=[{categoryType:"Ticket Status Cards",categoryCardCodes:["C","CD","CP","CA","INP","INF","ITA","ITR","OH","OP","R","STS","UT","WFI","WFIA"],categoryCards:[]}],this.minNoOfVisibleSummaryCards=1,this.maxNoOfVisibleSummaryCards=6,this.cardClicked=!1,this.status="",this.summaryViewActivated=!1,this.isDownloading=!1,this.durationApplied=!1,this.isCurrentUserCustomer=!1,this.current_year_start=h().startOf("year"),this.current_year_end=h().endOf("year"),this._onAppApiCalled=new l.b,this.amsItemDataCurrentIndex=0,this.isCardClicked=!1,this.isAdminAccess=!0}openAdminSettingsReport(){this.router.navigateByUrl("/main/ams/ticketHome/amsSettings")}ngOnInit(){return Object(n.c)(this,void 0,void 0,(function*(){yield this.checkAdminRole(),yield this._ticket.getStatusColorFromDB(),yield this._ticket.getActivePriorityFromDB(),this.udrfService.udrfBodyData=[];let t=[{checkboxId:"CDCRD3",checkboxName:"This Month",checkboxStartValue:this.utilityService.getFormattedDate(h().startOf("month"),h(h().startOf("month")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(h().endOf("month"),h(h().endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD6",checkboxName:"Previous Month",checkboxStartValue:this.utilityService.getFormattedDate(h().add(-1,"month").startOf("month"),h(h().add(-1,"month").startOf("month")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(h().add(-1,"month").endOf("month"),h(h().add(-1,"month").endOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD7",checkboxName:"Last 3 Months",checkboxStartValue:this.utilityService.getFormattedDate(h().subtract(3,"month").endOf("month"),h(h().subtract(3,"month").endOf("month")).date,15,0,0,0),checkboxEndValue:this.utilityService.getFormattedDate(h().startOf("month"),h(h().startOf("month")).date,15,0,0,0),isCheckboxDefaultSelected:!1},{checkboxId:"CDCRD",checkboxName:"This Year",checkboxStartValue:this.current_year_start,checkboxEndValue:this.current_year_end,isCheckboxDefaultSelected:!1}];this.udrfService.udrfFunctions.constructCustomRangeData(10,"date",t),this.udrfService.udrfUiData.showItemDataCount=!0,this.udrfService.udrfUiData.itemDataType="",this.udrfService.udrfUiData.totalItemDataCount=0,this.udrfService.udrfUiData.showSearchBar=!0,this.udrfService.udrfUiData.showActionButtons=!0,this.udrfService.udrfUiData.showUdrfModalButton=!0,this.udrfService.udrfUiData.showSettingsModalButton=!1,this.udrfService.udrfUiData.showSettingsModalButtonForAMS=!1,this.udrfService.udrfUiData.isReportDownloading=!1,this.udrfService.udrfUiData.showReportDownloadButton=!1,this.udrfService.udrfUiData.showColumnConfigButton=!1,this.udrfService.udrfUiData.showToggleIcon=!0,this.udrfService.udrfUiData.showFlagIcon=!0,this.udrfService.udrfUiData.showVersionIcon=!0,this.udrfService.udrfUiData.showDownloadExcelIcon=!0,this.udrfService.udrfUiData.showCreateNewComponentButton=!0,this.udrfService.udrfUiData.resolveVisibleSummaryCards=this.resolveVisibleSummaryCards.bind(this),this.udrfService.udrfUiData.summaryCardsSelected=this.dataTypeCardSelectedSR.bind(this),this.udrfService.udrfUiData.summaryCardsItem={},this.udrfService.udrfUiData.downloadItemDataReport=()=>{},this.udrfService.udrfUiData.itemDataScrollDown=()=>{},this.udrfService.udrfUiData.callInlineEditApi=()=>{},this.udrfService.udrfUiData.inlineEditData={},this.udrfService.udrfUiData.inlineEditDropDownMasterDatas={},this.udrfService.udrfUiData.summaryCards=this.dataTypeArray,this.udrfService.udrfUiData.udrfBodyColumns=[],this.udrfService.udrfUiData.udrfVisibleBodyColumns=this.udrfService.udrfUiData.udrfVisibleBodyColumns,this.udrfService.udrfUiData.udrfInvisibleBodyColumns=this.udrfService.udrfUiData.udrfInvisibleBodyColumns,this.udrfService.udrfUiData.categorisedSummaryCards=this.categorisedDataTypeArray,this.udrfService.udrfUiData.minNoOfVisibleSummaryCards=this.minNoOfVisibleSummaryCards,this.udrfService.udrfUiData.maxNoOfVisibleSummaryCards=this.maxNoOfVisibleSummaryCards,this.udrfService.udrfUiData.selectedCard=[],this.udrfService.udrfUiData.variant=0,this.udrfService.udrfUiData.itemHasQuickCta=!1,this.udrfService.udrfUiData.itemHasComments=!1,this.udrfService.udrfUiData.quickCTAInput={},this.udrfService.udrfUiData.commentsInput={},this.udrfService.udrfUiData.commentsContext={},this.udrfService.udrfUiData.showFlaggedTickets=this.showFlaggedTickets.bind(this),this.udrfService.udrfUiData.openNewReleasesComponent=this.openNewReleasesComponent.bind(this),this.udrfService.udrfUiData.downloadFilteredTickets=this.downloadFilteredTickets.bind(this),this.udrfService.udrfUiData.changeToSummaryView=this.changeToSummaryView.bind(this),this.udrfService.udrfUiData.createNewComponent=this.openTicketCreationForm.bind(this),this.udrfService.udrfUiData.summaryViewActivated=!1,this.udrfService.udrfUiData.isMoreOptionsNeeded=this.isAdminAccess,this.udrfService.udrfUiData.adminSettingButton=this.isAdminAccess,this.udrfService.udrfUiData.openAdminSettingsReport=this.openAdminSettingsReport.bind(this),this.canUserCreateTicket=this._ticket.getCreateAccess(),this.isCurrentUserCustomer=this._ticket.checkIfUserIsCustomer(),this.tsInlineEntryPopupService.getWorkflowProperties({applicationId:37}).then(t=>{this.tsInlineEntryPopupService.wfProperties=t,this.tsInlineEntryPopupService.getTimesheetProperties().then(t=>{this.tsInlineEntryPopupService.tsProperties=t,this.canUserCreateTicket=this._ticket.getCreateAccess(),this.getNewReleases()})}),this.udrfService.getAppUdrfConfig(this.applicationId,this.getTicketData.bind(this))}))}getNewReleases(){this._ticket.getAMSNewReleases().pipe(Object(d.a)(this._onDestroy)).subscribe(t=>{"N"==t.error&&t.data.length>0&&(this.newReleases=t.data,localStorage.getItem("AMS Ticket Releases")!=this.newReleases[0].version&&(this.openNewReleasesComponent(),localStorage.setItem("AMS Ticket Releases",this.newReleases[0].version)))},t=>{this._ErrorService.userErrorAlert(t.error.errorCode,"Some Error Happened in completing the Activity",t.error.errMessage)})}openNewReleasesComponent(){return Object(n.c)(this,void 0,void 0,(function*(){const{TicketNewReleasesComponent:t}=yield i.e(324).then(i.bind(null,"9Iyo"));this.dialog.open(t,{height:"50%",width:"50%",maxWidth:"86%",data:this.newReleases})}))}getInitData(){this.getTicketData()}getTicketData(){this.isTicketsLoading=!0,this._ticket.getTicketsForEmployee(this.currentUser.oid).pipe(Object(d.a)(this._onDestroy)).subscribe(t=>{this.initAmsReportSR(),"N"==t.error&&t.data.length>0?(this.ticketList=t.data,sessionStorage.removeItem("ticketList"),sessionStorage.setItem("ticketList",JSON.stringify(this.ticketList))):(this._ticket.showMessage("No Tickets Found !"),sessionStorage.removeItem("ticketList"),this.isTicketsLoading=!1),0==this.ticketList.length&&(this.isTicketsLoading=!1)},t=>{this.isTicketsLoading=!1,this._ErrorService.userErrorAlert(t.error.errorCode,"Some Error Happened in completing the Activity",t.error.errMessage)})}getStatusList(){this._ticket.getStatusList().pipe(Object(d.a)(this._onDestroy)).subscribe(t=>{"S"==t.messType&&t.data?this.statusList=t.data:this._ticket.showMessage("Status list not Found !"),this.ticketList.length>0&&0==this.typeList.length&&this.getTypeList()},t=>{this.isTicketsLoading=!1,this._ErrorService.userErrorAlert(t.error.errorCode,"Some Error Happened in completing the Activity",t.error.errMessage)})}getTypeList(){this._ticket.getTypeList().pipe(Object(d.a)(this._onDestroy)).subscribe(t=>{"S"==t.messType&&t.data?this.typeList=t.data:this._ticket.showMessage("Ticket type list not Found !"),this.getTicketProperties()},t=>{this.isTicketsLoading=!1,this._ErrorService.userErrorAlert(t.error.errorCode,"Some Error Happened in completing the Activity",t.error.errMessage)})}getTicketProperties(){this._ticket.getTicketProperties().pipe(Object(d.a)(this._onDestroy)).subscribe(t=>{"N"==t.error?this.ticketProperties=t.data[0]:this._ticket.showMessage("Error Fetching Ticket Properties !"),this.isCurrentUserCustomer&&this.customizeFiltersForCustomer(),this.getFlaggedTickets()},t=>{this.isTicketsLoading=!1,this._ErrorService.userErrorAlert(t.error.errorCode,"Some Error Happened in completing the Activity",t.error.errMessage)})}customizeFiltersForCustomer(){this.udrfService.udrfData.filterTypeArray=c.filter(this.udrfService.udrfData.filterTypeArray,t=>c.contains(this.ticketProperties.customer_filter_in_udrf,t.filterId))}clearSearch(){this.searchTextControl.reset()}getFlaggedTickets(){this._ticket.getFlaggedTickets(this.currentUser.oid).pipe(Object(d.a)(this._onDestroy)).subscribe(t=>{if(this.isTicketsLoading=!1,"S"==t.messType&&t.data.length>0){this.flaggedTickets="string"==typeof t.data?JSON.parse(t.data):t.data;for(let t of this.ticketList)t.isFlagged=c.contains(this.flaggedTickets,t.ticket_id);sessionStorage.removeItem("ticketList"),sessionStorage.setItem("ticketList",JSON.stringify(this.ticketList))}this.loadCardView()},t=>{this.isTicketsLoading=!1,this._ticket.showErrorMessage(t),this._ErrorService.userErrorAlert(t.error.errorCode,"Some Error Happened in completing the Activity",t.error.errMessage)})}updateFlaggedTickets(t){this._ticket.updateFlaggedTickets(this.currentUser.oid,this.flaggedTickets).pipe(Object(d.a)(this._onDestroy)).subscribe(e=>{this._ticket.showMessage("S"==e.messType?t?"Ticket Flagged !":"Ticket UnFlagged !":e.messText)},t=>{this._ErrorService.userErrorAlert(t.error.errorCode,"Some Error Happened in completing the Activity",t.error.errMessage)})}showFlaggedTickets(){if(this.udrfService.udrfUiData.showFlagged=!this.udrfService.udrfUiData.showFlagged,this.udrfService.udrfUiData.showFlagged){let t=c.where(this.ticketList,{isFlagged:!0});this.ticketList=t,this.preLoadCard()}else this.getTicketListFromSession(),this.getFlaggedTickets(),this.preLoadCard()}getTicketListFromSession(){return Object(n.c)(this,void 0,void 0,(function*(){let t=sessionStorage.getItem("ticketList");t&&(this.ticketList="string"==typeof t?JSON.parse(t):t)}))}loadCardView(){this.ticketCardViewContainerRef&&this.ticketCardViewContainerRef.clear(),Promise.all([i.e(4),i.e(37),i.e(94),i.e(0),i.e(321)]).then(i.bind(null,"wBxy")).then(t=>{const e=this.compiler.compileModuleSync(t.TicketCardHomeModule).create(this.ticketCardViewContainerRef.injector).componentFactoryResolver.resolveComponentFactory(t.TicketCardHomeComponent),i=this.ticketCardViewContainerRef.createComponent(e);i.instance.ticketList=this.ticketList,i.instance.statusList=this.statusList,i.instance.typeList=this.typeList,i.instance.ticketProperties=this.ticketProperties,i.instance.updateFlag.subscribe(t=>{if(t){t.isFlagged?this.flaggedTickets.push(t.ticketId):this.flaggedTickets.splice(this.flaggedTickets.indexOf(t.ticketId),1);for(let e of this.ticketList)e.ticket_id==t.ticketId&&(e.isFlagged=t.isFlagged);this.updateFlaggedTickets(t.isFlagged)}},t=>{this._ErrorService.userErrorAlert(t.error.errorCode,"Some Error Happened in completing the Activity",t.error.errMessage)})})}openTicketCreationForm(){return Object(n.c)(this,void 0,void 0,(function*(){const{CreateTicketComponent:t}=yield i.e(266).then(i.bind(null,"XJ6s"));this.dialog.open(t,{width:"88%",height:"100%",position:{right:"0px"},autoFocus:!1,maxWidth:"90vw"}).afterClosed().subscribe(t=>{t&&"Submit"==t.event&&this.getInitData()},t=>{this._ErrorService.userErrorAlert(t.error.errorCode,"Some Error Happened in completing the Activity",t.error.errMessage)})}))}initAmsReportSR(){return Object(n.c)(this,void 0,void 0,(function*(){this.amsItemDataCurrentIndex=0,this.udrfService.udrfBodyData=[],this.isCardClicked=!1,this.cardClickeds="",yield this.getTicketListFromSession(),this.ticketList.length>0&&(yield this.getTicketsFilterSR())}))}getSummaryCardCount(){this._ticket.getStatusCount(this.ticketList).pipe().subscribe(t=>{"S"==t.messType&&t.data.length>0&&(this.dataTypeArray=this.dataTypeArray.map((e,i)=>{const s=t.data.find(t=>e.dataType==t.dataType);return Object.assign(Object.assign({},e),s)}),this.udrfService.udrfUiData.summaryCards=this.dataTypeArray)},t=>{this._ErrorService.userErrorAlert(t.error.errorCode,"Some Error Happened in completing the Activity",t.error.errMessage)})}preLoadCard(){0==this.ticketList.length&&this.ticketCardViewContainerRef?this.ticketCardViewContainerRef.clear():0==this.statusList.length?this.getStatusList():this.loadCardView(),0==this.cardClicked&&this.getSummaryCardCount()}getTicketsFilterSR(){return Object(n.c)(this,void 0,void 0,(function*(){let t=this.ticketList,e=JSON.parse(JSON.stringify(this.udrfService.udrfData.mainFilterArray)),i=c.where(this.dataTypeArray,{isActive:!0}),s=[],a=!1;if(i.length>0&&this.summaryViewActivated&&(s=c.where(i,{cardType:"status"}),s.length>0))if(e.length>0){for(let t of e)if("Ticket Status"==t.filterName){let e=this.udrfService.udrfFunctions.applyStatusId([t],i[0].dataType);t.multiOptionSelectSearchValuesWithId=[e],a=!0}if(0==a){let t=JSON.parse(JSON.stringify(c.where(this.udrfService.udrfData.filterTypeArray,{filterName:"Ticket Status"})));e.push(t[0]);for(let s of e)if("Ticket Status"==s.filterName){let t=this.udrfService.udrfFunctions.applyStatusId([s],i[0].dataType);s.multiOptionSelectSearchValuesWithId=[t],a=!0}}}else{e=JSON.parse(JSON.stringify(c.where(this.udrfService.udrfData.filterTypeArray,{filterName:"Ticket Status"})));let t=this.udrfService.udrfFunctions.applyStatusId(e,i[0].dataType);e[0].multiOptionSelectSearchValuesWithId=[t]}let r=c.findIndex(this.udrfService.udrfData.filterTypeArray,{filterName:"Duration"});if(this.udrfService.udrfData.filterTypeArray[r].isCustomButtonActivated)this.startDate=this.udrfService.udrfData.filterTypeArray[r].customButtonValueStart,this.endDate=this.udrfService.udrfData.filterTypeArray[r].customButtonValueEnd;else for(let o of this.udrfService.udrfData.filterTypeArray[r].checkboxValues){if(o.isCheckboxSelected){this.startDate=o.checkboxStartValue,this.endDate=o.checkboxEndValue;break}this.startDate="",this.endDate=""}let n={startIndex:this.amsItemDataCurrentIndex,startDate:this.startDate,endDate:this.endDate,mainFilterArray:e,txTableDetails:this.udrfService.udrfData.txTableDetails,mainSearchParameter:this.udrfService.udrfData.mainSearchParameter,searchTableDetails:this.udrfService.udrfData.searchTableDetails};this.isFilterApplied=e.length>0,this._ticket.getTicketsFilterSR(n,t).pipe(Object(d.a)(this._onDestroy)).pipe(Object(d.a)(this._onAppApiCalled)).subscribe(t=>{this.isTicketsLoading=!1,this.isSearching=!1,"S"==t.messType&&t.data?(this.ticketList=t.data,this.udrfService.udrfUiData.totalItemDataCount=t.data.length,this.preLoadCard()):this._ticket.showMessage("No Tickets Found !")},t=>{this.isTicketsLoading=!1,this.isSearching=!1,this._ErrorService.userErrorAlert(t.error.errorCode,"Some Error Happened in completing the Activity",t.error.errMessage)})}))}resolveVisibleSummaryCards(){return Object(n.c)(this,void 0,void 0,(function*(){for(let t of this.udrfService.udrfUiData.summaryCards){let e;null!=this.udrfService.udrfData.udrfSummaryCardCodes&&(e=c.contains(this.udrfService.udrfData.udrfSummaryCardCodes,t.dataTypeCode),t.isVisible=e,e&&(this.udrfService.udrfUiData.summaryCardsItem=t))}}))}dataTypeCardSelectedSR(){this.udrfService.udrfData.isItemDataLoading=!0,this.udrfService.udrfData.noItemDataFound=!1;let t=this.udrfService.udrfUiData.summaryCardsItem;for(let e=0;e<this.dataTypeArray.length;e++)if(t.dataTypeCode==this.dataTypeArray[e].dataTypeCode&&this.selectedViewCard!=t.dataTypeCode)this.dataTypeArray[e].isActive=!0,t.isActive=!0,this.selectedViewCard=t.dataTypeCode,this.udrfService.udrfUiData.cardClicked=!0;else{t.dataTypeCode==this.dataTypeArray[e].dataTypeCode&&(this.selectedViewCard="");let i=c.where(this.udrfService.udrfUiData.summaryCards,{dataTypeCode:this.dataTypeArray[e].dataTypeCode});i.length>0&&(i[0].isActive=!1),this.dataTypeArray[e].isActive=!1,this.udrfService.udrfUiData.cardClicked=!1}this.isCardClicked=!0,this.cardClicked=t.dataType,this.amsItemDataCurrentIndex=0,this.udrfService.udrfBodyData=[],this.initAmsReportSR()}getTicketsFilter(t,e){this.searchTextControl.value&&(this.isSearching=!0),this._ticket.getTicketsFilter(this.status,t,e,this.searchTextControl.value).pipe(Object(d.a)(this._onDestroy)).subscribe(t=>{this.isTicketsLoading=!1,this.isSearching=!1,"S"==t.messType&&t.data?(this.ticketList=t.data,this.isFilterApplied=!0,this.preLoadCard()):this._ticket.showMessage("No Tickets Found !")},t=>{this.isTicketsLoading=!1,this.isSearching=!1,this._ErrorService.userErrorAlert(t.error.errorCode,"Some Error Happened in completing the Activity",t.error.errMessage)})}changeToSummaryView(){this.udrfService.udrfUiData.showSettingsModalButtonForAMS=!this.udrfService.udrfUiData.showSettingsModalButtonForAMS,this.udrfService.udrfUiData.summaryViewActivated=!this.udrfService.udrfUiData.summaryViewActivated,this.summaryViewActivated=!this.summaryViewActivated,this.udrfService.udrfUiData.cardClicked=!1,this.dataTypeCardSelectedSR()}downloadFilteredTickets(){if(this.ticketList.length>0){this.udrfService.udrfUiData.isDownloading=!0,this.isDownloading=!0;let t=c.uniq(c.pluck(this.ticketList,"ticket_id"));this._ticket.downloadTickets(t).pipe(Object(d.a)(this._onDestroy)).subscribe(t=>Object(n.c)(this,void 0,void 0,(function*(){this.udrfService.udrfUiData.isDownloading=!1,this.isDownloading=!1,"N"==t.error?this.excelService.exportAsExcelFile(t.data,"KEBS - AMS Report"):this._ticket.showMessage(t.data)})),t=>{this.udrfService.udrfUiData.isDownloading=!1,this.isDownloading=!1,this._ErrorService.userErrorAlert(t.error.errorCode,"Some Error Happened in completing the Activity",t.error.errMessage)})}else this._ticket.showMessage("No Tickets found to download !")}checkAdminRole(){return new Promise((t,e)=>this._ticket.checkAdminRole().subscribe(e=>{console.log(e),"S"==e.messType&&(this.isAdminAccess=e.isAdmin),t(e)},t=>{e(t)}))}ngOnDestroy(){this.ticketCardViewContainerRef&&this.ticketCardViewContainerRef.clear(),this._onDestroy.next(),this._onDestroy.complete(),this.udrfService.resetUdrfData()}}return t.\u0275fac=function(e){return new(e||t)(o["\u0275\u0275directiveInject"](u.b),o["\u0275\u0275directiveInject"](o.Compiler),o["\u0275\u0275directiveInject"](r.g),o["\u0275\u0275directiveInject"](p.a),o["\u0275\u0275directiveInject"](m.a),o["\u0275\u0275directiveInject"](g.a),o["\u0275\u0275directiveInject"](f.a),o["\u0275\u0275directiveInject"](y.a),o["\u0275\u0275directiveInject"](v.a))},t.\u0275cmp=o["\u0275\u0275defineComponent"]({type:t,selectors:[["app-ticket-home-landing-page"]],viewQuery:function(t,e){if(1&t&&o["\u0275\u0275viewQuery"](b,!0,o.ViewContainerRef),2&t){let t;o["\u0275\u0275queryRefresh"](t=o["\u0275\u0275loadQuery"]())&&(e.ticketCardViewContainerRef=t.first)}},decls:7,vars:2,consts:[[1,"container-fluid","ticket-home-styles","pl-0"],["class","container d-flex h-100 flex-column",4,"ngIf"],["ticketCardViewContainer",""],["style","text-align: center;padding-top: 3rem;",4,"ngIf"],[1,"container","d-flex","h-100","flex-column"],[1,"flex-grow-1"],[1,"row","pt-4","justify-content-center"],["diameter","30"],[2,"text-align","center","padding-top","3rem"],[1,"d-flex","justify-content-center","align-items-center","slide-in-top"],[2,"font-size","18px","font-weight","normal"],[1,"d-flex","justify-content-center","align-items-center","slide-from-down"],["src","https://assets.kebs.app/images/nomilestone.png","height","300","width","325",1,"mt-2","mb-2"],["mat-raised-button","","class","mt-2 btn-active slide-from-down",3,"click",4,"ngIf"],["mat-raised-button","",1,"mt-2","btn-active","slide-from-down",3,"click"]],template:function(t,e){1&t&&(o["\u0275\u0275elementStart"](0,"div",0),o["\u0275\u0275element"](1,"udrf-header"),o["\u0275\u0275template"](2,T,5,0,"div",1),o["\u0275\u0275elementStart"](3,"div"),o["\u0275\u0275elementContainer"](4,null,2),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](6,w,7,2,"div",3),o["\u0275\u0275elementEnd"]()),2&t&&(o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngIf",e.isTicketsLoading),o["\u0275\u0275advance"](4),o["\u0275\u0275property"]("ngIf",0==e.ticketList.length&&!e.isTicketsLoading))},directives:[k.a,s.NgIf,C.c,_.a],styles:[".ticket-home-styles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%]{background-color:#cf0001;border-color:#cf0001;color:#fff;font-weight:400;font-size:12px!important;min-width:10rem;line-height:28px;border-radius:4px}.ticket-home-styles[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{color:#868383;font-size:14px!important;font-weight:400}.ticket-home-styles[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%], .ticket-home-styles[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ticket-home-styles[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]{color:#1a1a1a;font-size:14px;font-weight:500}.ticket-home-styles[_ngcontent-%COMP%]   .headingBold[_ngcontent-%COMP%]{color:#cf0001;font-weight:500;font-size:14px}.ticket-home-styles[_ngcontent-%COMP%]   .normalFont[_ngcontent-%COMP%]{color:#181818;font-size:14px;font-weight:400;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ticket-home-styles[_ngcontent-%COMP%]   .icon-button-active[_ngcontent-%COMP%]{background-color:#cf0001!important;line-height:8px;width:26px;height:26px;margin-right:15px!important;box-shadow:0 3px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.ticket-home-styles[_ngcontent-%COMP%]   .icon-button-active[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#fff;font-size:18px}.ticket-home-styles[_ngcontent-%COMP%]   .icon-button-inactive[_ngcontent-%COMP%]{line-height:8px;width:26px;height:26px;margin-right:12px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.ticket-home-styles[_ngcontent-%COMP%]   .icon-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.ticket-home-styles[_ngcontent-%COMP%]   .data-type-card[_ngcontent-%COMP%]{min-height:85px;transition:all .3s linear}.ticket-home-styles[_ngcontent-%COMP%]   .data-type-card-is-active[_ngcontent-%COMP%]{border-color:#cf0001;background-color:#fff2f2}.ticket-home-styles[_ngcontent-%COMP%]   .cp[_ngcontent-%COMP%]{cursor:pointer!important}.ticket-home-styles[_ngcontent-%COMP%]   .headingBold[_ngcontent-%COMP%]{color:#1a1a1a;font-weight:600;font-size:22px}.ticket-home-styles[_ngcontent-%COMP%]   .valueGrey14[_ngcontent-%COMP%]{color:#4a4a4a;font-size:14px;font-weight:300;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.ticket-home-styles[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field{width:40vw;padding-top:5px}.ticket-home-styles[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-infix{font-size:14px!important;padding:.5em 0!important;border-top:.54375em solid transparent!important}.ticket-home-styles[_ngcontent-%COMP%]   .status-circular[_ngcontent-%COMP%]{height:13px;width:13px;margin-top:4px;border-radius:50%;box-shadow:0 2px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.ticket-home-styles[_ngcontent-%COMP%]   .spinner-align[_ngcontent-%COMP%]{margin:auto;display:inline-block}.ticket-home-styles[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}.ticket-home-styles[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}"]}),t})();var O=i("Wp6s"),D=i("NFeN");function x(t,e){if(1&t){const t=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div",5),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](t);const i=e.$implicit;return o["\u0275\u0275nextContext"]().selectedSettings(i.id)})),o["\u0275\u0275elementStart"](1,"div",6),o["\u0275\u0275elementStart"](2,"mat-icon",7),o["\u0275\u0275text"](3),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](4,"div",8),o["\u0275\u0275text"](5),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}if(2&t){const t=e.$implicit,i=o["\u0275\u0275nextContext"]();o["\u0275\u0275property"]("ngClass",t.id==i.selectedItemId?"sr-nav-item":"r-nav-item")("routerLink",t.link),o["\u0275\u0275advance"](3),o["\u0275\u0275textInterpolate1"](" ",t.icon," "),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate1"](" ",t.label," ")}}let I=(()=>{class t{constructor(t){this.router=t}ngOnInit(){this.router.navigateByUrl("/main/ams/ticketHome/amsSettings/supportTeams"),this.selectedItemId=1,this.navList=[{id:1,label:"Support Teams",icon:"fact_check",link:"supportTeams",display:!0}]}clearSearchText(){}selectedSettings(t){console.log(t),this.selectedItemId=t}}return t.\u0275fac=function(e){return new(e||t)(o["\u0275\u0275directiveInject"](r.g))},t.\u0275cmp=o["\u0275\u0275defineComponent"]({type:t,selectors:[["app-ams-settings"]],decls:6,vars:1,consts:[[1,"container-fluid","opportunity-settings-style"],[1,"pt-4","layout-wrapper"],[1,"edit-main"],[1,"edit-nav"],["class","py-3 px-3 slide-from-down row",3,"ngClass","routerLink","click",4,"ngFor","ngForOf"],[1,"py-3","px-3","slide-from-down","row",3,"ngClass","routerLink","click"],[1,"mt-1","pl-0","col-2","icon"],[2,"font-size","25px !important","color","#000000ad !important"],[1,"col-8","px-2","my-auto"]],template:function(t,e){1&t&&(o["\u0275\u0275elementStart"](0,"div",0),o["\u0275\u0275elementStart"](1,"div",1),o["\u0275\u0275elementStart"](2,"div",2),o["\u0275\u0275element"](3,"router-outlet"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](4,"mat-card",3),o["\u0275\u0275template"](5,x,6,4,"div",4),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&t&&(o["\u0275\u0275advance"](5),o["\u0275\u0275property"]("ngForOf",e.navList))},directives:[r.l,O.a,s.NgForOf,s.NgClass,r.h,D.a],styles:[".opportunity-settings-style[_ngcontent-%COMP%]{overflow-y:hidden!important}.opportunity-settings-style[_ngcontent-%COMP%]   .layout-wrapper[_ngcontent-%COMP%]{display:flex;height:90vh}.opportunity-settings-style[_ngcontent-%COMP%]   .edit-nav[_ngcontent-%COMP%]{width:20vw;overflow-y:scroll;background-color:#fff;color:#000}.opportunity-settings-style[_ngcontent-%COMP%]   .edit-main[_ngcontent-%COMP%]{width:75vw;padding:5px;overflow-y:scroll!important}.opportunity-settings-style[_ngcontent-%COMP%]   .sr-nav-item[_ngcontent-%COMP%]{background-color:#fddde6!important;font-weight:500;border-left:solid #cf0001;border-radius:2px}.opportunity-settings-style[_ngcontent-%COMP%]   .r-nav-item[_ngcontent-%COMP%]{font-weight:400;cursor:pointer}.opportunity-settings-style[_ngcontent-%COMP%]   .r-nav-item[_ngcontent-%COMP%]:hover{background-color:#fddde6!important;font-weight:500;border-left:solid rgba(0,0,0,.6784313725490196);border-radius:2px}.opportunity-settings-style[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]{width:600px!important}.opportunity-settings-style[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]{margin-left:100px!important}.opportunity-settings-style[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}.opportunity-settings-style[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}"]}),t})();var E=i("ZzPI"),F=i("dNgK"),R=i("WYlB"),M=i("6t9p"),P=i("kmnG"),j=i("qFsG"),L=i("jtHE"),V=i("NJ67"),$=i("d3UM"),N=i("FKr1"),U=i("WJ5W");const B=["allSelected"],q=["singleSelect"];function H(t,e){if(1&t&&(o["\u0275\u0275elementStart"](0,"mat-option",7),o["\u0275\u0275text"](1),o["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit;o["\u0275\u0275property"]("value",t.id),o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" ",t.name," ")}}let z=(()=>{class t extends V.a{constructor(t){super(),this.renderer=t,this.fieldCtrl=new a.j,this.fieldFilterCtrl=new a.j,this.list=[],this.required=!1,this.valueChange=new o.EventEmitter,this.disabled=!1,this.filteredList=new L.a,this._onDestroy=new l.b}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(d.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(d.a)(this._onDestroy)).subscribe(t=>{this.value=t,this.onChange(t),this.valueChange.emit(t)})}ngOnChanges(){null!=this.list&&this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let t=this.fieldFilterCtrl.value;t?(t=t.toLowerCase(),this.filteredList.next(this.list.filter(e=>e.name.toLowerCase().indexOf(t)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(t){t?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(t){this.fieldCtrl.setValue(t)}toggleAllSelection(){this.allSelected.selected&&this.fieldCtrl.patchValue([])}}return t.\u0275fac=function(e){return new(e||t)(o["\u0275\u0275directiveInject"](o.Renderer2))},t.\u0275cmp=o["\u0275\u0275defineComponent"]({type:t,selectors:[["app-multi-select-search2"]],viewQuery:function(t,e){if(1&t&&(o["\u0275\u0275viewQuery"](B,!0),o["\u0275\u0275viewQuery"](q,!0)),2&t){let t;o["\u0275\u0275queryRefresh"](t=o["\u0275\u0275loadQuery"]())&&(e.allSelected=t.first),o["\u0275\u0275queryRefresh"](t=o["\u0275\u0275loadQuery"]())&&(e.singleSelect=t.first)}},inputs:{list:"list",placeholder:"placeholder",required:"required",disabled:"disabled"},outputs:{valueChange:"valueChange"},features:[o["\u0275\u0275ProvidersFeature"]([{provide:a.t,useExisting:Object(o.forwardRef)(()=>t),multi:!0}]),o["\u0275\u0275InheritDefinitionFeature"],o["\u0275\u0275NgOnChangesFeature"]],decls:12,vars:11,consts:[["appearance","outline"],["multiple","",3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value","click"],["allSelected",""],[3,"value",4,"ngFor","ngForOf"],[3,"value"]],template:function(t,e){1&t&&(o["\u0275\u0275elementStart"](0,"mat-form-field",0),o["\u0275\u0275elementStart"](1,"mat-label"),o["\u0275\u0275text"](2),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](3,"mat-select",1,2),o["\u0275\u0275elementStart"](5,"mat-option"),o["\u0275\u0275element"](6,"ngx-mat-select-search",3),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](7,"mat-option",4,5),o["\u0275\u0275listener"]("click",(function(){return e.toggleAllSelection()})),o["\u0275\u0275text"](9,"None"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](10,H,2,2,"mat-option",6),o["\u0275\u0275pipe"](11,"async"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&t&&(o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate"](e.placeholder),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("formControl",e.fieldCtrl)("placeholder",e.placeholder)("required",e.required)("disabled",e.disabled),o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("formControl",e.fieldFilterCtrl)("placeholderLabel",e.placeholder),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("value",0),o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("ngForOf",o["\u0275\u0275pipeBind1"](11,9,e.filteredList)))},directives:[P.c,P.g,$.c,a.v,a.k,a.F,N.p,U.a,s.NgForOf],pipes:[s.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}"]}),t})();const W=["singleSelect"];function G(t,e){if(1&t){const t=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"mat-option",6),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](t);const i=e.$implicit;return o["\u0275\u0275nextContext"]().emitChanges(i)})),o["\u0275\u0275text"](1),o["\u0275\u0275elementEnd"]()}if(2&t){const t=e.$implicit;o["\u0275\u0275property"]("value",t.id),o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" ",t.name," ")}}let Y=(()=>{class t extends V.a{constructor(t){super(),this.renderer=t,this.fieldCtrl=new a.j,this.fieldFilterCtrl=new a.j,this.list=[],this.required=!1,this.valueChange=new o.EventEmitter,this.disabled=!1,this.filteredList=new L.a,this.change=new o.EventEmitter,this._onDestroy=new l.b,this.emitChanges=t=>{this.change.emit(t)}}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(d.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(d.a)(this._onDestroy)).subscribe(t=>{this.value=t,this.onChange(t),this.valueChange.emit(t)})}ngOnChanges(){null!=this.list&&this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let t=this.fieldFilterCtrl.value;t?(t=t.toLowerCase(),this.filteredList.next(this.list.filter(e=>e.name.toLowerCase().indexOf(t)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(t){t?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(t){this.fieldCtrl.setValue(t)}}return t.\u0275fac=function(e){return new(e||t)(o["\u0275\u0275directiveInject"](o.Renderer2))},t.\u0275cmp=o["\u0275\u0275defineComponent"]({type:t,selectors:[["ams-input-search"]],viewQuery:function(t,e){if(1&t&&o["\u0275\u0275viewQuery"](W,!0),2&t){let t;o["\u0275\u0275queryRefresh"](t=o["\u0275\u0275loadQuery"]())&&(e.singleSelect=t.first)}},inputs:{list:"list",placeholder:"placeholder",required:"required",disabled:"disabled"},outputs:{valueChange:"valueChange",change:"change"},features:[o["\u0275\u0275ProvidersFeature"]([{provide:a.t,useExisting:Object(o.forwardRef)(()=>t),multi:!0}]),o["\u0275\u0275InheritDefinitionFeature"],o["\u0275\u0275NgOnChangesFeature"]],decls:11,vars:11,consts:[["appearance","outline"],[3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value"],[3,"value","click",4,"ngFor","ngForOf"],[3,"value","click"]],template:function(t,e){1&t&&(o["\u0275\u0275elementStart"](0,"mat-form-field",0),o["\u0275\u0275elementStart"](1,"mat-label"),o["\u0275\u0275text"](2),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](3,"mat-select",1,2),o["\u0275\u0275elementStart"](5,"mat-option"),o["\u0275\u0275element"](6,"ngx-mat-select-search",3),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](7,"mat-option",4),o["\u0275\u0275text"](8,"None"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](9,G,2,2,"mat-option",5),o["\u0275\u0275pipe"](10,"async"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&t&&(o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate"](e.placeholder),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("formControl",e.fieldCtrl)("placeholder",e.placeholder)("required",e.required)("disabled",e.disabled),o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("formControl",e.fieldFilterCtrl)("placeholderLabel",e.placeholder),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("value",null),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngForOf",o["\u0275\u0275pipeBind1"](10,9,e.filteredList)))},directives:[P.c,P.g,$.c,a.v,a.k,a.F,N.p,U.a,s.NgForOf],pipes:[s.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}"]}),t})();const Q=["editDialog"];function K(t,e){if(1&t){const t=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div",14),o["\u0275\u0275elementStart"](1,"form",15),o["\u0275\u0275listener"]("ngSubmit",(function(){return o["\u0275\u0275restoreView"](t),o["\u0275\u0275nextContext"]().onSubmit()})),o["\u0275\u0275elementStart"](2,"div"),o["\u0275\u0275elementStart"](3,"div",16),o["\u0275\u0275elementStart"](4,"mat-form-field",17),o["\u0275\u0275elementStart"](5,"mat-label"),o["\u0275\u0275text"](6,"Team Name"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275element"](7,"input",18),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](8,"div",19),o["\u0275\u0275element"](9,"app-multi-select-search2",20),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](10,"div"),o["\u0275\u0275elementStart"](11,"div",19),o["\u0275\u0275element"](12,"app-multi-select-search2",21),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](13,"div",19),o["\u0275\u0275element"](14,"ams-input-search",22),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](15,"div",23),o["\u0275\u0275elementStart"](16,"button",24),o["\u0275\u0275text"](17," Submit "),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}if(2&t){const t=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("formGroup",t.editForm),o["\u0275\u0275advance"](8),o["\u0275\u0275property"]("list",t.employee_data),o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("list",t.employee_data),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("list",t.workSchedule)}}const J=[{path:"",component:A},{path:"amsSettings",component:I,data:{breadcrumb:"Settings"},children:[{path:"supportTeams",component:(()=>{class t{constructor(t,e,i,s){this.amsService=t,this.fb=e,this.dialog=i,this.snackBar=s,this.editForm=this.fb.group({team_id:[""],team_name:["",a.H.required],team_work_schedule:["",a.H.required],team_head:["",a.H.required],team_coordinator:["",a.H.required]}),this.selectedItemKeys=[],this.isLoading=!0}ngOnInit(){this.amsService.getSupportTeamsForAdmin().subscribe(t=>{this.tableData=t.data,this.workSchedule=t.work_schedule,this.employee_data=t.employee_data})}selectionChanged(t){this.selectedItemKeys=t.selectedRowKeys}deleteRecords(){this.selectedItemKeys.forEach(t=>{this.tableData.remove(t)}),this.dataGrid.instance.refresh()}onEditorPreparing(t){"dxSelectBox"==t.editorName&&(t.editorOptions.onOpened=function(t){t.component._popup.option("minWidth",400)})}onToolbarPreparing(t){t.toolbarOptions.items.unshift({location:"after",widget:"dxButton",options:{icon:"refresh",onClick:this.refreshDataGrid.bind(this),hint:"Refresh Data"}}),t.toolbarOptions.items.unshift({location:"after",widget:"dxButton",options:{icon:"add",onClick:this.addSupportTeam.bind(this),hint:"Add team"}})}refreshDataGrid(){this.tableData=[],this.isLoading=!0,this.dataGrid.instance.beginCustomLoading("loading..."),this.amsService.getSupportTeamsForAdmin().subscribe(t=>{this.tableData=t.data,this.workSchedule=t.work_schedule,this.employee_data=t.employee_data,this.isLoading=!1,this.dataGrid.instance.endCustomLoading()}),this.dataGrid.instance.refresh()}headColumn_customizeText(t){return console.log(t.value),t.value&&t.value.length>1?`${t.value[0]} (+${t.value.length-1})`:t.value?t.value[0]:"-"}coordinatorColumn_customizeText(t){return console.log(t.value),t.value&&t.value.length>1?`${t.value[0]} (+${t.value.length-1})`:t.value?t.value[0]:"-"}onRowClick(t){this.editForm.reset(),this.editForm.patchValue({team_id:t.data.team_id,team_name:t.data.team_name,team_head:t.data.team_head,team_coordinator:t.data.team_coordinator,team_work_schedule:t.data.team_work_schedule}),this.editDialogRef=this.dialog.open(this.editDialog,{height:"55%",width:"50%"}),console.log(t)}addSupportTeam(){this.editForm.reset(),this.editDialogRef=this.dialog.open(this.editDialog,{height:"55%",width:"50%"})}onDeleteRow(t){let e=new Promise((e,i)=>{this.amsService.deleteSupportTeamForAdmin(t.data.team_id).subscribe(t=>{"E"==t.messType&&(this.snackBar.open("Error Deleting","",{duration:3e3}),i(!0)),this.snackBar.open("Deleted Successfully","",{duration:3e3}),e(!1)},t=>{this.snackBar.open("Error Deleting","",{duration:3e3}),i(t.message)})});t.cancel=e}onSubmit(){console.log(this.editForm),console.log(this.editForm.invalid),this.editForm.invalid?this.snackBar.open("All Fields are required","",{duration:3e3}):this.amsService.updateSupportTeamForAdmin(this.editForm.value).subscribe(t=>{"E"==t.messType&&(this.snackBar.open("ERROR:Edit failed. Try Again","",{duration:3e3}),this.editDialogRef.close()),this.snackBar.open("Successfully edited data!","",{duration:3e3}),this.refreshDataGrid(),this.editDialogRef.close()})}}return t.\u0275fac=function(e){return new(e||t)(o["\u0275\u0275directiveInject"](p.a),o["\u0275\u0275directiveInject"](a.i),o["\u0275\u0275directiveInject"](u.b),o["\u0275\u0275directiveInject"](F.a))},t.\u0275cmp=o["\u0275\u0275defineComponent"]({type:t,selectors:[["app-ams-support-teams"]],viewQuery:function(t,e){if(1&t&&(o["\u0275\u0275viewQuery"](E.a,!0),o["\u0275\u0275viewQuery"](Q,!0,o.TemplateRef)),2&t){let t;o["\u0275\u0275queryRefresh"](t=o["\u0275\u0275loadQuery"]())&&(e.dataGrid=t.first),o["\u0275\u0275queryRefresh"](t=o["\u0275\u0275loadQuery"]())&&(e.editDialog=t.first)}},decls:16,vars:14,consts:[[3,"dataSource","showBorders","allowColumnReordering","columnHidingEnabled","onToolbarPreparing","onRowClick","onRowRemoving","onRowRemoved"],["mode","row",3,"allowDeleting"],["placeholder","Search...",3,"visible","width"],[3,"pageSize","pageIndex"],["fileName","KEBSAMSSupportTeamData",3,"enabled"],["dataField","team_id","caption","ID"],["dataField","team_name","caption","Team Name"],["dataField","team_work_schedule_name","caption","Work Schedule"],["dataField","team_head_names","caption","Team Head",3,"customizeText"],["dataField","team_coordinator_names","caption","Team Coordinator",3,"customizeText"],["name","addRowButton","showText","always"],["location","after"],["icon","trash","text","Delete Selected Records",3,"disabled","onClick"],["editDialog",""],[1,"container","p-3",2,"height","100%","width","100%"],[3,"formGroup","ngSubmit"],[1,"col-10"],["appearance","outline",1,"create-account-field"],["matInput","","placeholder","Team Name","formControlName","team_name"],[1,"col-4"],["placeholder","Team head","formControlName","team_head",1,"create-account-field-inputsearch",3,"list"],["placeholder","Team coordinator","formControlName","team_coordinator",1,"create-account-field-inputsearch",3,"list"],["placeholder","Work Schedule","formControlName","team_work_schedule",1,"create-account-field-inputsearch",3,"list"],[1,"col-10","row","justify-content-end"],["mat-button","","type","submit",2,"font-size","13px","color","#ffffff","font-weight","bold !important","background","linear-gradient(270deg, #ef4a61 0%, #f27a6c 105.29%)"]],template:function(t,e){1&t&&(o["\u0275\u0275elementStart"](0,"dx-data-grid",0),o["\u0275\u0275listener"]("onToolbarPreparing",(function(t){return e.onToolbarPreparing(t)}))("onRowClick",(function(t){return e.onRowClick(t)}))("onRowRemoving",(function(t){return e.onDeleteRow(t)}))("onRowRemoved",(function(){return e.refreshDataGrid()})),o["\u0275\u0275element"](1,"dxo-editing",1),o["\u0275\u0275element"](2,"dxo-search-panel",2),o["\u0275\u0275element"](3,"dxo-paging",3),o["\u0275\u0275element"](4,"dxo-export",4),o["\u0275\u0275element"](5,"dxi-column",5),o["\u0275\u0275element"](6,"dxi-column",6),o["\u0275\u0275element"](7,"dxi-column",7),o["\u0275\u0275element"](8,"dxi-column",8),o["\u0275\u0275element"](9,"dxi-column",9),o["\u0275\u0275elementStart"](10,"dxo-toolbar"),o["\u0275\u0275element"](11,"dxi-item",10),o["\u0275\u0275elementStart"](12,"dxi-item",11),o["\u0275\u0275elementStart"](13,"dx-button",12),o["\u0275\u0275listener"]("onClick",(function(){return e.deleteRecords()})),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](14,K,18,4,"ng-template",null,13,o["\u0275\u0275templateRefExtractor"])),2&t&&(o["\u0275\u0275property"]("dataSource",e.tableData)("showBorders",!0)("allowColumnReordering",!0)("showBorders",!0)("columnHidingEnabled",!1),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("allowDeleting",!0),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("visible",!0)("width",240),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("pageSize",10)("pageIndex",0),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("enabled",!0),o["\u0275\u0275advance"](4),o["\u0275\u0275property"]("customizeText",e.headColumn_customizeText),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("customizeText",e.coordinatorColumn_customizeText),o["\u0275\u0275advance"](4),o["\u0275\u0275property"]("disabled",!e.selectedItemKeys.length))},directives:[E.a,M.Qb,M.Md,M.od,M.Sb,M.g,M.Ge,M.o,R.a,a.J,a.w,a.n,P.c,P.g,j.b,a.e,a.v,a.l,z,Y,_.a],styles:[".create-account-field-inputsearch[_ngcontent-%COMP%]{font-size:13px!important}.create-account-field-inputsearch[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]{width:70%!important}.create-account-field-inputsearch[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-wrapper{width:24rem}.create-account-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%!important}"]}),t})()}]}];let X=(()=>{class t{}return t.\u0275mod=o["\u0275\u0275defineNgModule"]({type:t}),t.\u0275inj=o["\u0275\u0275defineInjector"]({factory:function(e){return new(e||t)},imports:[[r.k.forChild(J)],r.k]}),t})();var Z=i("4/q7"),tt=i("/1cH"),et=i("iadO"),it=i("Qu3c"),st=i("1yaQ"),at=i("Xi0T"),rt=i("xm0x");let nt=(()=>{class t{}return t.\u0275mod=o["\u0275\u0275defineNgModule"]({type:t}),t.\u0275inj=o["\u0275\u0275defineInjector"]({factory:function(e){return new(e||t)},providers:[{provide:N.c,useClass:st.c,deps:[N.f,st.a]},{provide:N.e,useValue:{parse:{dateInput:"DD-MM-YYYY"},display:{dateInput:"DD-MM-YY",monthYearLabel:"MMM YYYY"}}}],imports:[[s.CommonModule,X,u.g,a.p,a.E,Z.b,E.b,P.e,j.c,$.d,U.b,tt.c,et.h,N.n,D.b,_.b,it.b,C.b,O.d,at.a,rt.a,R.b,F.b]]}),t})()},HmYF:function(t,e,i){"use strict";i.d(e,"a",(function(){return l}));var s=i("mrSG"),a=i("Iab2"),r=i("EUZL"),n=i("wd/R"),o=i("xG9w"),d=i("fXoL");let l=(()=>{class t{constructor(){this.formatColumn=(t,e,i)=>{const s=r.utils.decode_range(t["!ref"]);for(let a=s.s.r+1;a<=s.e.r;++a){const s=r.utils.encode_cell({r:a,c:e});t[s]&&t[s].v&&(t[s].t="d",t[s].z=i)}}}exportAsExcelFile(t,e,i,s,a){console.log("Excel to JSON Service",t);const n=r.utils.json_to_sheet(t);if(a&&a.length){const t=r.utils.sheet_to_json(n,{header:1}).shift();for(const e of a){const i=t.indexOf(e.fieldKey);this.formatColumn(n,i,e.fieldFormat)}}null==i&&(i=[]),null==s&&(s="DD-MM-YYYY"),this.formatExcelDateData(n,i,s);const o=r.write({Sheets:{data:n},SheetNames:["data"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(o,e)}formatExcelDateData(t,e,i){for(let r of Object.keys(t))if(null!=t[r]&&null!=t[r].t&&null!=t[r].v&&n(t[r].v,i,!0).isValid()){let s=r.replace(/[0-9]/g,"")+"1";0==o.where(e,{value:t[s].v}).length&&null!=t[s]&&null!=t[s].t&&e.push({value:t[s].v,format:i})}let s=[],a=1;for(let r of e)for(let e of Object.keys(t)){let i=parseInt(e.replace(/[^0-9]/g,""));i>a&&(a=i),null!=t[e]&&null!=t[e].v&&t[e].v==r.value&&s.push({value:e.replace(/[0-9]/g,""),format:r.format})}for(let r of s)for(let e=2;e<=a;e++)null!=t[r.value+""+e]&&null!=t[r.value+""+e].t&&(t[r.value+""+e].t="d",null!=t[r.value+""+e].v&&"Invalid date"!=t[r.value+""+e].v?t[r.value+""+e].v=n(t[r.value+""+e].v,r.format).format("YYYY/MM/DD"):(console.log(t[r.value+""+e].t),t[r.value+""+e].v="",t[r.value+""+e].t="s"))}saveAsExcelFile(t,e){const i=new Blob([t],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8"});a.saveAs(i,e.concat(".xlsx"))}exportAsExcelFileWFH(t,e,i){const s=r.utils.json_to_sheet(t),a=r.utils.json_to_sheet(e),n=r.write({Sheets:{All_Approvals:s,Pending_Approvals:a},SheetNames:["All_Approvals","Pending_Approvals"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(n,i)}exportAsExcelFileForPayroll(t,e,i,s,a,n){const o=r.utils.json_to_sheet(t),d=r.utils.json_to_sheet(e),l=r.utils.json_to_sheet(i),c=r.utils.json_to_sheet(s),h=r.utils.json_to_sheet(a),u=r.write({Sheets:{Regular_Report:o,Intern_Report:d,Contract_Report:l,Perdiem_Report:c,RP_Report:h},SheetNames:["Regular_Report","Intern_Report","Contract_Report","Perdiem_Report","RP_Report"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(u,n)}exportAsCsvFileWithSheetName(t,e){return Object(s.c)(this,void 0,void 0,(function*(){let i=r.utils.book_new();for(let e of t){let t=r.utils.json_to_sheet(e.data);r.utils.book_append_sheet(i,t,e.sheetName)}let s=r.write(i,{bookType:"xlsx",type:"binary"});yield this.saveAsCsvFile(s,e)}))}saveAsCsvFile(t,e){return Object(s.c)(this,void 0,void 0,(function*(){const i=new Blob([yield this.s2ab(t)],{type:"application/octet-stream"});a.saveAs(i,e.concat(".csv"))}))}s2ab(t){return Object(s.c)(this,void 0,void 0,(function*(){for(var e=new ArrayBuffer(t.length),i=new Uint8Array(e),s=0;s<t.length;s++)i[s]=255&t.charCodeAt(s);return e}))}exportAsExcelFileWithCellMerge(t,e,i){const s=r.utils.json_to_sheet(t);s["!merges"]=i;const a=r.write({Sheets:{data:s},SheetNames:["data"]},{bookType:"xlsx",type:"array"});this.saveAsExcelFile(a,e)}exportJsonToExcelWithMultipleSheets(t,e){return Object(s.c)(this,void 0,void 0,(function*(){let i=r.utils.book_new();for(let e of t){let t=r.utils.json_to_sheet(e.data);r.utils.book_append_sheet(i,t,e.sheetName)}let s=r.write(i,{bookType:"xlsx",type:"array"});this.saveAsExcelFile(s,e)}))}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275prov=d["\u0275\u0275defineInjectable"]({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()},NJ67:function(t,e,i){"use strict";i.d(e,"a",(function(){return s}));class s{constructor(){this.disabled=!1}onChange(t){}onTouched(t){}writeValue(t){this.value=t}registerOnChange(t){this.onChange=t}registerOnTouched(t){this.onTouched=t}setDisabledState(t){this.disabled=t}}},v2fc:function(t,e,i){"use strict";i.d(e,"a",(function(){return u}));var s=i("xG9w"),a=i("wd/R"),r=i("2Vo4"),n=i("XNiG"),o=i("fXoL"),d=i("tk/3"),l=i("LcQX"),c=i("flaP"),h=i("XXEo");let u=(()=>{class t{constructor(t,e,i,s){this.$http=t,this._util=e,this._roles=i,this._auth=s,this.currentUser=this._auth.getProfile().profile,this.token=this._auth.getJwtToken(),this.isaBudgettedAttachmentSubject=new r.a({}),this.getisaBudgettedAttachmentObservable=this.isaBudgettedAttachmentSubject.asObservable(),this.activitySubject=new n.b,this.getActivityObservable=this.activitySubject.asObservable(),this.priority_status_colors=[{statusName:"Low",statusColor:"#BADC58"},{statusName:"Medium",statusColor:"#91AECB"},{statusName:"High",statusColor:"#FFA502"},{statusName:"Very High",statusColor:"#cf0001"}],this.getVendorDetailsData=()=>this.$http.post("/api/isa/request/getVendorForList",{})}showMessage(t){this._util.showToastMessage(t)}showErrorMessage(t){this._util.showErrorMessage(t,"KEBS")}getToken(){return this._auth.getToken()}getCurrentUserRole(){return this._roles&&this._roles.roles&&this._roles.roles.length>0?this._roles.roles[0].role_id:null}setActivityObservable(t){this.activitySubject.next(t)}getStatusObjectEntries(){let t=s.where(this._roles.roles,{application_id:139,object_id:68});return t.length>0?JSON.parse(t[0].object_entries):null}resolveActivityTemplate(t,e,i,r,n,o){let d=s.where(t,{activity_name:("Status"==e?"status":"Priority"==e?"priority":"RMG Owner"==e?"rmgOwner":"TAG Owner"==e?"tagOwner":null)||e});return d.length>0?{activity_type_id:d[0]._id,activity_description:this.getActivityDescription(d[0],i,r,n,o),activity_by:this.currentUser.oid,activity_created_date:a()}:{}}getActivityDescription(t,e,i,s,a){let r="";return r=t.activity_template[e],i&&(r=r.replace("from_value",i)),s&&(r=r.replace("to_value",s)),a&&(r=r.replace("object_name",a)),r}getRequestById(t){return this.$http.post("/api/isa/request/getRequestById",{requestId:t})}getActivityTypes(){return this.$http.post("/api/isa/request/getActivityTypeMasterData",{})}getStatusMasterData(){return this.$http.post("/api/isa/request/getISAStatusMasterData",{statusValues:this.getStatusObjectEntries()})}updateRequestStatus(t,e,i,s){return this.$http.post("/api/isa/request/statusChange",{requestId:t,statusRefId:e,currentStatusId:i,activityTemplate:s})}updateKeyValueInResourceRequest(t,e,i){return this.$http.post("/api/isa/request/updateKeyValueInResourceRequest",{requestId:t,activityTemplate:e,modifyKeyValue:i})}changeTagOrRmgOwner(t,e,i,s){return this.$http.post("/api/isa/request/changeTagOrRmgOwner",{requestId:t,activityTemplate:e,type:i,changedOid:s})}getActivity(t){return this.$http.post("/api/isa/request/activityRetreivalBasedOnRequestIdOrActivityId",{activityId:t})}insertISAActivity(t,e){return this.$http.post("/api/isa/request/insertISAActivity",{requestId:t,activityTemplate:e})}editISAActivity(t,e){return this.$http.post("/api/isa/request/editISAActivity",{activity_id:t,activity_details:e})}getISAAttachmentById(t){return this.$http.post("/api/isa/attachment/getISAAttachmentById",{request_id:t})}getISAAttachmentFromS3(t){return this.$http.post("/api/isa/attachment/getISAAttachmentFromS3",{key:t})}deleteISAAttachment(t,e,i){return this.$http.post("/api/isa/attachment/deleteISAAttachment",{requestId:t,file:e,activityTemplate:i})}updateISAAttachment(t,e,i,s){return this.$http.post("/api/isa/attachment/updateISAAttachment",{requestId:t,file:i,attachmentId:e,activityTemplate:s})}getApproverStatuses(t,e){return this.$http.post("/api/isa/request/getApproverStatusForRequest",{workflowHeaderId:t,approvers:e})}createTask(t,e,i){return this.$http.post("/api/isa/tasks/createTasks",{request_id:t,tasks:e,activityTemplate:i})}getRequestTasks(t){return this.$http.post("/api/isa/tasks/getTaskById",{request_id:t})}addTask(t,e,i,s){return this.$http.post("/api/isa/tasks/addTasks",{task_id:t,request_id:e,tasks:i,activityTemplate:s})}updateTaskObject(t,e,i,s,a){return this.$http.post("/api/isa/tasks/updateTaskObject",{task_id:t,sub_task_id:e,key:i,object:s,activityTemplate:a})}getTaskTemplate(){return this.$http.post("/api/isa/tasks/getTaskTemplates",{})}createTaskFromTemplate(t,e,i,s,a,r,n,o){return this.$http.post("/api/isa/tasks/assignTaskFromTemplate",{task_template_id:t,request_id:e,task_id:i,rmg_owner:s,tag_owner:a,activityTemplate:r,taskTypesList:n,requestSkillId:o})}updateTaskDataFromAttachment(t){return this.$http.post("/api/isa/tasks/updateTaskNameAndOwner",t)}updateExtTaskExtAtchId(t){return this.$http.post("/api/isa/tasks/updateExtTaskExtAtchId",t)}getTaskStatusList(){return this.$http.post("/api/isa/tasks/getTaskStatus",{})}getTaskTypeList(){return this.$http.post("/api/isa/tasks/getTaskTypeMasterData",{})}updateTypeListInReq(t){return this.$http.post("/api/isa/request/insertTaskTypeOwnerDetail",t)}updateTypeAssigned(t){return this.$http.post("/api/isa/request/updTaskTypeOwnerDetail",t)}deleteTask(t,e,i,s,a){return this.$http.post("/api/isa/tasks/changeTaskFlag",{request_id:t,task_id:e,sub_task_id:i,is_active:s,activityTemplate:a})}updateTaskAssigned(t,e,i,s,a){return this.$http.post("/api/isa/tasks/updateAssignedToTask",{request_id:t,task_id:e,sub_task_id:i,assigned_to:s,activityTemplate:a})}updateTaskData(t,e,i,s,a,r){return this.$http.post("/api/isa/tasks/updateTaskData",{request_id:t,task_id:e,sub_task_id:i,key:s,value:a,activityTemplate:r})}getRequestCTAs(t){return this.$http.post("/api/isa/configuration/getRequestCTAs",{requestId:t})}getISAWfConfig(){return this.$http.post("/api/isa/configuration/getISAWfConfig",{})}getTodoDetails(t){return this.$http.post("/api/isa/todo/getTodo",{todoId:t})}createTodo(t,e,i){return this.$http.post("/api/isa/todo/createToDo",{request_id:t,to_do_list:e,activityTemplate:i})}insertTodo(t,e,i,s){return this.$http.post("/api/isa/todo/insertToDo",{request_id:t,to_do_id:e,to_do_list:i,activityTemplate:s})}editTodo(t,e,i,s){return this.$http.post("/api/isa/todo/editToDo",{request_id:t,to_do_id:e,to_do_details:i,activityTemplate:s})}updateResourceInRequest(t,e,i,s,a){return this.$http.post("/api/isa/request/updateResourceInRequest",{requestId:t,activityTemplate:e,wfActivityTemplate:i,statusRefId:s,resourceOid:a})}triggerWfOnSubmission(t,e,i,s,a,r,n,o){return this.$http.post("/api/isa/request/triggerWfOnSubmission",{requestId:t,activityTemplate:e,wfActivityTemplate:i,statusRefId:s,task_id:a,sub_task_id:r,task_status:n,wfConfig:o})}addIsaTaskActualHours(t,e,i,s,a){return this.$http.post("/api/isa/tasks/addIsaTaskActualHours",{task_id:t,task_item:e,actual_hours:i,timesheet_id:s,request_id:a})}deleteIsaTaskActualHours(t,e,i,s,a){return this.$http.post("/api/isa/tasks/deleteIsaTaskActualHours",{task_id:t,task_item:e,actual_hours:i,request_id:s,timesheet_id:a})}getSLAByRequestId(t){return this.$http.post("/api/isa/request/getSLAByRequestId",{requestId:t})}getRequestBasedOnSearch(t){return this.$http.post("/api/isa/request/getRequestBasedOnSearch",{searchParameter:t})}moveTaskToRequest(t,e,i,s,a,r){return this.$http.post("/api/isa/tasks/moveTaskToRequest",{fromRequestId:t,fromTaskId:e,mainTaskId:i,toRequestId:s,toTaskId:a,activityTemplate:r})}removeResourceFromRequest(t,e,i,s,a){return this.$http.post("/api/isa/request/removeResourceFromRequest",{requestId:t,statusId:e,statusRefId:i,resActivityTemplate:s,statusActivityTemplate:a})}checkIfUserHasAccessToRequest(t){return this.$http.post("/api/isa/request/checkIfUserHasAccessToRequest",{requestId:t,oid:this.currentUser.oid})}updateEmployeeStatus(t){return this.$http.post("/api/obPrimary/updateEmployeeStatus",t)}checkIfEmailHasRequestId(t){return this.$http.post("/api/obPrimary/checkIfEmailHasRequestId",{apiParams:{emailId:t}})}createISAAttachment(t){return this.$http.post("/api/isa/attachment/createIsaAttachment",t)}getSourceList(){return this.$http.post("/api/isa/attachment/getAllSource",{})}saveEmailtoTask(t){return this.$http.post("/api/isa/tasks/saveEmailtoTask",t)}setISABudgetedAttachmentActivityObservable(t){this.isaBudgettedAttachmentSubject.next(t)}getTemplateForUser(t){return this.$http.post("/api/isa/request/getVisibleFormFieldsForUser",t)}budgetApprovalCheck(t){return this.$http.post("/api/isa/request/ctcRangeCheck",t)}checkElligibleToSubmit(t){return new Promise((e,i)=>{this.$http.post("/api/isa/request/budgetApprovalCheck",t).subscribe(t=>e(t),t=>i(t))})}offerApprovalCheck(t){return this.$http.post("/api/isa/request/offerApprovalCheck",t)}triggerWfOnBudgetApproval(t,e,i,s,a){return this.$http.post("/api/isa/request/triggerWfOnBudgetApproval",{requestId:t,activityTemplate:e,wfActivityTemplate:i,statusRefId:s,wfConfig:a})}getMasterDataByMasterDataName(t){return this.$http.post("/api/isa/request/getMasterDataByMasterDataName",t)}getVisibilityMatrix(t){return this.$http.post("/api/isa/request/getAuthorzedFieldsByRoleId",t)}getAllRoleAccess(){return s.where(this._roles.roles,{application_id:139})}}return t.\u0275fac=function(e){return new(e||t)(o["\u0275\u0275inject"](d.c),o["\u0275\u0275inject"](l.a),o["\u0275\u0275inject"](c.a),o["\u0275\u0275inject"](h.a))},t.\u0275prov=o["\u0275\u0275defineInjectable"]({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()},w76M:function(t,e,i){"use strict";i.d(e,"a",(function(){return l})),i.d(e,"b",(function(){return c}));var s=i("jhN1"),a=i("fXoL"),r=i("oHs6"),n=i("PVOt"),o=i("6t9p");const d=["*"];let l=(()=>{let t=class extends n.b{constructor(t,e,i,s,a,r,n,o){super(t,e,i,s,n,o),this._watcherHelper=s,this._idh=a,this._createEventEmitters([{subscribe:"contentReady",emit:"onContentReady"},{subscribe:"disposing",emit:"onDisposing"},{subscribe:"hidden",emit:"onHidden"},{subscribe:"hiding",emit:"onHiding"},{subscribe:"initialized",emit:"onInitialized"},{subscribe:"optionChanged",emit:"onOptionChanged"},{subscribe:"resize",emit:"onResize"},{subscribe:"resizeEnd",emit:"onResizeEnd"},{subscribe:"resizeStart",emit:"onResizeStart"},{subscribe:"showing",emit:"onShowing"},{subscribe:"shown",emit:"onShown"},{subscribe:"titleRendered",emit:"onTitleRendered"},{emit:"accessKeyChange"},{emit:"animationChange"},{emit:"closeOnOutsideClickChange"},{emit:"containerChange"},{emit:"contentTemplateChange"},{emit:"deferRenderingChange"},{emit:"disabledChange"},{emit:"dragEnabledChange"},{emit:"elementAttrChange"},{emit:"focusStateEnabledChange"},{emit:"fullScreenChange"},{emit:"heightChange"},{emit:"hintChange"},{emit:"hoverStateEnabledChange"},{emit:"maxHeightChange"},{emit:"maxWidthChange"},{emit:"minHeightChange"},{emit:"minWidthChange"},{emit:"positionChange"},{emit:"resizeEnabledChange"},{emit:"rtlEnabledChange"},{emit:"shadingChange"},{emit:"shadingColorChange"},{emit:"showCloseButtonChange"},{emit:"showTitleChange"},{emit:"tabIndexChange"},{emit:"titleChange"},{emit:"titleTemplateChange"},{emit:"toolbarItemsChange"},{emit:"visibleChange"},{emit:"widthChange"}]),this._idh.setHost(this),r.setHost(this)}get accessKey(){return this._getOption("accessKey")}set accessKey(t){this._setOption("accessKey",t)}get animation(){return this._getOption("animation")}set animation(t){this._setOption("animation",t)}get closeOnOutsideClick(){return this._getOption("closeOnOutsideClick")}set closeOnOutsideClick(t){this._setOption("closeOnOutsideClick",t)}get container(){return this._getOption("container")}set container(t){this._setOption("container",t)}get contentTemplate(){return this._getOption("contentTemplate")}set contentTemplate(t){this._setOption("contentTemplate",t)}get deferRendering(){return this._getOption("deferRendering")}set deferRendering(t){this._setOption("deferRendering",t)}get disabled(){return this._getOption("disabled")}set disabled(t){this._setOption("disabled",t)}get dragEnabled(){return this._getOption("dragEnabled")}set dragEnabled(t){this._setOption("dragEnabled",t)}get elementAttr(){return this._getOption("elementAttr")}set elementAttr(t){this._setOption("elementAttr",t)}get focusStateEnabled(){return this._getOption("focusStateEnabled")}set focusStateEnabled(t){this._setOption("focusStateEnabled",t)}get fullScreen(){return this._getOption("fullScreen")}set fullScreen(t){this._setOption("fullScreen",t)}get height(){return this._getOption("height")}set height(t){this._setOption("height",t)}get hint(){return this._getOption("hint")}set hint(t){this._setOption("hint",t)}get hoverStateEnabled(){return this._getOption("hoverStateEnabled")}set hoverStateEnabled(t){this._setOption("hoverStateEnabled",t)}get maxHeight(){return this._getOption("maxHeight")}set maxHeight(t){this._setOption("maxHeight",t)}get maxWidth(){return this._getOption("maxWidth")}set maxWidth(t){this._setOption("maxWidth",t)}get minHeight(){return this._getOption("minHeight")}set minHeight(t){this._setOption("minHeight",t)}get minWidth(){return this._getOption("minWidth")}set minWidth(t){this._setOption("minWidth",t)}get position(){return this._getOption("position")}set position(t){this._setOption("position",t)}get resizeEnabled(){return this._getOption("resizeEnabled")}set resizeEnabled(t){this._setOption("resizeEnabled",t)}get rtlEnabled(){return this._getOption("rtlEnabled")}set rtlEnabled(t){this._setOption("rtlEnabled",t)}get shading(){return this._getOption("shading")}set shading(t){this._setOption("shading",t)}get shadingColor(){return this._getOption("shadingColor")}set shadingColor(t){this._setOption("shadingColor",t)}get showCloseButton(){return this._getOption("showCloseButton")}set showCloseButton(t){this._setOption("showCloseButton",t)}get showTitle(){return this._getOption("showTitle")}set showTitle(t){this._setOption("showTitle",t)}get tabIndex(){return this._getOption("tabIndex")}set tabIndex(t){this._setOption("tabIndex",t)}get title(){return this._getOption("title")}set title(t){this._setOption("title",t)}get titleTemplate(){return this._getOption("titleTemplate")}set titleTemplate(t){this._setOption("titleTemplate",t)}get toolbarItems(){return this._getOption("toolbarItems")}set toolbarItems(t){this._setOption("toolbarItems",t)}get visible(){return this._getOption("visible")}set visible(t){this._setOption("visible",t)}get width(){return this._getOption("width")}set width(t){this._setOption("width",t)}get toolbarItemsChildren(){return this._getOption("toolbarItems")}set toolbarItemsChildren(t){this.setChildren("toolbarItems",t)}_createInstance(t,e){return new r.a(t,e)}ngOnDestroy(){this._destroyWidget()}ngOnChanges(t){super.ngOnChanges(t),this.setupChanges("toolbarItems",t)}setupChanges(t,e){t in this._optionsToUpdate||this._idh.setup(t,e)}ngDoCheck(){this._idh.doCheck("toolbarItems"),this._watcherHelper.checkWatchers(),super.ngDoCheck(),super.clearChangedOptions()}_setOption(t,e){let i=this._idh.setupSingle(t,e),s=null!==this._idh.getChanges(t,e);(i||s)&&super._setOption(t,e)}};return t.\u0275fac=function(e){return new(e||t)(a["\u0275\u0275directiveInject"](a.ElementRef),a["\u0275\u0275directiveInject"](a.NgZone),a["\u0275\u0275directiveInject"](n.e),a["\u0275\u0275directiveInject"](n.j),a["\u0275\u0275directiveInject"](n.g),a["\u0275\u0275directiveInject"](n.i),a["\u0275\u0275directiveInject"](s.h),a["\u0275\u0275directiveInject"](a.PLATFORM_ID))},t.\u0275cmp=a["\u0275\u0275defineComponent"]({type:t,selectors:[["dx-popup"]],contentQueries:function(t,e,i){if(1&t&&a["\u0275\u0275contentQuery"](i,o.L,!1),2&t){let t;a["\u0275\u0275queryRefresh"](t=a["\u0275\u0275loadQuery"]())&&(e.toolbarItemsChildren=t)}},inputs:{accessKey:"accessKey",animation:"animation",closeOnOutsideClick:"closeOnOutsideClick",container:"container",contentTemplate:"contentTemplate",deferRendering:"deferRendering",disabled:"disabled",dragEnabled:"dragEnabled",elementAttr:"elementAttr",focusStateEnabled:"focusStateEnabled",fullScreen:"fullScreen",height:"height",hint:"hint",hoverStateEnabled:"hoverStateEnabled",maxHeight:"maxHeight",maxWidth:"maxWidth",minHeight:"minHeight",minWidth:"minWidth",position:"position",resizeEnabled:"resizeEnabled",rtlEnabled:"rtlEnabled",shading:"shading",shadingColor:"shadingColor",showCloseButton:"showCloseButton",showTitle:"showTitle",tabIndex:"tabIndex",title:"title",titleTemplate:"titleTemplate",toolbarItems:"toolbarItems",visible:"visible",width:"width"},outputs:{onContentReady:"onContentReady",onDisposing:"onDisposing",onHidden:"onHidden",onHiding:"onHiding",onInitialized:"onInitialized",onOptionChanged:"onOptionChanged",onResize:"onResize",onResizeEnd:"onResizeEnd",onResizeStart:"onResizeStart",onShowing:"onShowing",onShown:"onShown",onTitleRendered:"onTitleRendered",accessKeyChange:"accessKeyChange",animationChange:"animationChange",closeOnOutsideClickChange:"closeOnOutsideClickChange",containerChange:"containerChange",contentTemplateChange:"contentTemplateChange",deferRenderingChange:"deferRenderingChange",disabledChange:"disabledChange",dragEnabledChange:"dragEnabledChange",elementAttrChange:"elementAttrChange",focusStateEnabledChange:"focusStateEnabledChange",fullScreenChange:"fullScreenChange",heightChange:"heightChange",hintChange:"hintChange",hoverStateEnabledChange:"hoverStateEnabledChange",maxHeightChange:"maxHeightChange",maxWidthChange:"maxWidthChange",minHeightChange:"minHeightChange",minWidthChange:"minWidthChange",positionChange:"positionChange",resizeEnabledChange:"resizeEnabledChange",rtlEnabledChange:"rtlEnabledChange",shadingChange:"shadingChange",shadingColorChange:"shadingColorChange",showCloseButtonChange:"showCloseButtonChange",showTitleChange:"showTitleChange",tabIndexChange:"tabIndexChange",titleChange:"titleChange",titleTemplateChange:"titleTemplateChange",toolbarItemsChange:"toolbarItemsChange",visibleChange:"visibleChange",widthChange:"widthChange"},features:[a["\u0275\u0275ProvidersFeature"]([n.e,n.j,n.i,n.g]),a["\u0275\u0275InheritDefinitionFeature"],a["\u0275\u0275NgOnChangesFeature"]],ngContentSelectors:d,decls:1,vars:0,template:function(t,e){1&t&&(a["\u0275\u0275projectionDef"](),a["\u0275\u0275projection"](0))},encapsulation:2}),t})(),c=(()=>{let t=class{};return t.\u0275mod=a["\u0275\u0275defineNgModule"]({type:t}),t.\u0275inj=a["\u0275\u0275defineInjector"]({factory:function(e){return new(e||t)},imports:[[o.bb,o.Gc,o.Vd,o.vd,o.hb,o.lb,o.sb,o.id,o.jd,o.M,n.c,n.f,s.b],o.bb,o.Gc,o.Vd,o.vd,o.hb,o.lb,o.sb,o.id,o.jd,o.M,n.f]}),t})()},xm0x:function(t,e,i){"use strict";i.d(e,"a",(function(){return r}));var s=i("ofXK"),a=i("fXoL");let r=(()=>{class t{}return t.\u0275mod=a["\u0275\u0275defineNgModule"]({type:t}),t.\u0275inj=a["\u0275\u0275defineInjector"]({factory:function(e){return new(e||t)},imports:[[s.CommonModule]]}),t})()},yu80:function(t,e,i){"use strict";i.d(e,"a",(function(){return u}));var s=i("mrSG"),a=i("xG9w"),r=i("XNiG"),n=i("2Vo4"),o=i("fXoL"),d=i("tk/3"),l=i("flaP"),c=i("XXEo"),h=i("LcQX");let u=(()=>{class t{constructor(t,e,i,s){this.$http=t,this._roles=e,this._auth=i,this._util=s,this.applicationName="AMS",this.currentUser=this._auth.getProfile().profile,this.activitySubject=new r.b,this.getActivityObservable=this.activitySubject.asObservable(),this.actualHoursSubject=new n.a({}),this.getActualHoursObservable=this.actualHoursSubject.asObservable(),this.taskRefreshSubject=new r.b,this.getTaskRefreshObservable=this.taskRefreshSubject.asObservable(),this.task_status_colors=[{statusName:"Open",statusColor:"#928F8D"},{statusName:"In Progress",statusColor:"#ff7200"},{statusName:"Completed",statusColor:"#4caf50"}],this.supportTeams=[],this.statusColorArray=[],this.priorityArray=[],this.getAmsAccessForUser(),this.getCurrentUserRole(),this.getSupportTeamData()}setActivityObservable(t){this.activitySubject.next(t)}setTaskRefreshObservable(t){this.taskRefreshSubject.next(t)}setActualHoursObservable(t){this.actualHoursSubject.next(t)}showMessage(t){this._util.showToastMessage(t)}showErrorMessage(t){this._util.showErrorMessage(t,"KEBS")}getToken(){return this._auth.getToken()}getCurrentUserRole(){return this._roles&&this._roles.roles&&this._roles.roles.length>0?this._roles.roles[0].role_id:null}getAmsAccessForUser(){let t=a.findWhere(this._roles.roles,{application_id:95,object_id:13});return!t||"Update"!=t.operation&&"*"!=t.operation}getStatusObjectEntries(){let t=a.where(this._roles.roles,{application_id:95,object_id:66});return t.length>0?JSON.parse(t[0].object_entries):null}getTicketCreationObjectEntries(){return Object(s.c)(this,void 0,void 0,(function*(){return a.where(this._roles.roles,{application_id:95,object_id:565})}))}getCreateAccess(){let t=a.where(this._roles.roles,{application_id:95,object_id:67});return t.length>0&&"*"==t[0].object_value}checkIfTicketIsEditable(t,e){if(this.supportTeams.length>0)for(let i of this.supportTeams){i.team_coordinator="string"==typeof i.team_coordinator?JSON.parse(i.team_coordinator):i.team_coordinator;for(let t of i.team_coordinator)if(t==this.currentUser.oid)return!0;i.team_head="string"==typeof i.team_head?JSON.parse(i.team_head):i.team_head;for(let t of i.team_head)if(t==this.currentUser.oid)return!0}return!a.contains(t.ticket_non_editable,e.status[0]._id)}getSupportTeamData(){return new Promise((t,e)=>{this.getSupportTeams().subscribe(i=>{"S"==i.messType&&i.data.length>0?(this.supportTeams=i.data,t(!0)):e("Not data found !")},t=>{e(t)})})}checkIfUserIsCustomer(){return!(!this._roles.roles||!this._roles.roles[0]||35!=this._roles.roles[0].role_id)}getCurrentCustomerDetails(){return this.$http.post("/api/ams/configuration/getCurrentCustomerDetails",{})}getStatusList(){return this.$http.post("/api/ams/configuration/getStatusList",{statusValues:this.getStatusObjectEntries()})}getTypeList(){return this.$http.post("/api/ams/configuration/getTypeList",{})}getAmsPostingDate(){return this.$http.post("/api/ams/configuration/getAmsPostingDate",{})}getAMSNewReleases(){return this.$http.post("/api/ams/configuration/getAMSNewReleases",{})}saveTicketToCollection(t){return this.$http.post("/api/ams/ticket/createTicket",{ticketDetails:t})}createTask(t,e){return this.$http.post("/api/ams/tasks/createTasks",{ticket_id:t,tasks:e})}createTaskFromTemplate(t,e,i,s,a){return this.$http.post("/api/ams/tasks/assignTaskFromTemplate",{task_template_id:t,ticket_id:e,estimated_closure_date:i,created_on:s,assigned_to:a})}addTask(t,e,i){return this.$http.post("/api/ams/tasks/addTasks",{task_id:t,ticket_id:e,tasks:i})}updateTaskObject(t,e,i,s){return this.$http.post("/api/ams/tasks/updateTaskObject",{task_id:t,sub_task_id:e,key:i,object:s})}editTaskStatus(t,e,i,s){return this.$http.post("/api/ams/tasks/editTaskStatus",{task_id:t,task_item:e,status:i,ticket_id:s})}editTaskDueDate(t,e,i,s){return this.$http.post("/api/ams/tasks/editTaskDueDate",{task_id:t,task_item:e,due_on:i,ticket_id:s})}editTaskPlannedHours(t,e,i,s){return this.$http.post("/api/ams/tasks/editTaskPlannedHours",{task_id:t,task_item:e,planned_hours:i,ticket_id:s})}editTaskName(t,e,i,s){return this.$http.post("/api/ams/tasks/editTaskName",{task_id:t,task_item:e,task_name:i,ticket_id:s})}editTaskAssigned(t,e,i,s){return this.$http.post("/api/ams/tasks/editTaskAssigned",{task_id:t,task_item:e,assigned_to:i,ticket_id:s})}addTaskActualHours(t,e,i,s,a){return this.$http.post("/api/ams/tasks/addTaskActualHours",{task_id:t,task_item:e,actual_hours:i,timesheet_id:s,ticket_id:a})}editTaskActualHours(t,e,i,s){return this.$http.post("/api/ams/tasks/editTaskActualHours",{task_id:t,task_item:e,actual_hours:i,ticket_id:s})}deleteTaskActualHours(t,e,i,s,a){return this.$http.post("/api/ams/tasks/deleteTaskActualHours",{task_id:t,task_item:e,actual_hours:i,ticket_id:s,timesheet_id:a})}getTicketMasterData(){return this.$http.post("/api/ams/configuration/getTicketMasterData",{})}getTicketProperties(){return this.$http.post("/api/ams/configuration/getTicketProperties",{})}getTicketById(t){return this.$http.post("/api/ams/ticket/getTicketById",{ticketId:t})}getTicketBasedOnSearch(t,e){return this.$http.post("/api/ams/ticket/getTicketBasedOnSearch",{searchParameter:t,ticketList:e})}getTicketMetaData(t){return this.$http.post("/api/ams/ticket/getTicketMetaData",{ticketId:t})}getTicketAttachment(t){return this.$http.post("/api/ams/ticket/getTicketAttachment",{ticketId:t})}getFileDataForDownload(t){return this.$http.post("/api/ams/configuration/getAMSAttachment",{key:t})}deleteTicketAttachment(t,e){return this.$http.post("/api/ams/configuration/deleteAMSAttachment",{ticketId:t,file:e})}updateTicketAttachment(t,e){return this.$http.post("/api/ams/configuration/updateAMSAttachment",{ticketId:t,file:e})}getTicketsForEmployee(t){return this.$http.post("/api/ams/ticket/getAllTicketsForEmployee",{employee_oid:t,org_codes:this._roles.getUserRoleOrgCodes("AMS"),role_id:this._roles.roles[0].role_id,is_read_only:this.getAmsAccessForUser()})}getConsultantBasedOnProjectModule(t,e){return this.$http.post("/api/ams/configuration/getConsultantBasedOnProjectModule",{project_item_id:t,module_id:e})}getActivity(t){return this.$http.post("/api/ams/activities/getActivity",{activityId:t})}addNoteActivity(t,e){return this.$http.post("/api/ams/activities/addNoteActivity",{activity_id:t,activities:e})}editActivity(t,e){return this.$http.post("/api/ams/activities/editActivity",{activity_id:t,activity_details:e})}getTodoDetails(t){return this.$http.post("/api/ams/todo/getTodo",{todoId:t})}getTicketCTAs(t){return this.$http.post("/api/ams/configuration/getTicketCTAs",{ticketId:t})}getTicketTasks(t){return this.$http.post("/api/ams/tasks/getTaskById",{task_id:t})}getTaskTemplate(){return this.$http.post("/api/ams/tasks/getTaskTemplates",{})}getConfigBasedOnProject(t){return this.$http.post("/api/ams/configuration/getConfigBasedOnProject",{itemId:t})}getModuleBasedOnSubmodule(t){return this.$http.post("/api/ams/configuration/getModuleBasedOnSubmodule",{subModuleId:t})}createTodo(t,e){return this.$http.post("/api/ams/todo/createToDo",{ticket_id:t,to_do_list:e})}insertTodo(t,e,i){return this.$http.post("/api/ams/todo/insertToDo",{ticket_id:t,to_do_id:e,to_do_list:i})}editTodo(t,e,i,s){return this.$http.post("/api/ams/todo/editToDo",{ticket_id:t,to_do_id:e,to_do_details:i,change_type:s})}getTrDetails(t){return this.$http.post("/api/ams/tr/getTr",{trId:t})}createTr(t,e){return this.$http.post("/api/ams/tr/createTr",{ticket_id:t,tr_list:e})}insertTr(t,e,i){return this.$http.post("/api/ams/tr/insertTr",{ticket_id:t,tr_id:e,tr_list:i})}editTr(t,e,i){return this.$http.post("/api/ams/tr/editTr",{ticket_id:t,tr_id:e,tr_details:i})}editTicketPriority(t,e,i){return this.$http.post("/api/ams/ticket/editTicketPriority",{ticket_id:t,prev_priority:e,priority:i})}editTicketStatus(t,e,i,s,a,r){return this.$http.post("/api/ams/ticket/editTicketStatus",{ticket_id:t,status_id:s,prev_status:e,status_name:a,status_ref_id:i,wf_plugin_data:r})}editTicketType(t,e,i,s){return this.$http.post("/api/ams/ticket/editTicketType",{ticket_id:t,type_id:i,prev_type:e,type_name:s})}editTicketConsultants(t,e,i,s,a,r){return this.$http.post("/api/ams/ticket/editTicketConsultants",{ticket_id:t,employee_oid:i,employee_name:s,level_name:a,emp_ref_id:r,prev_oid:e})}getFlaggedTickets(t){return this.$http.post("/api/ams/configuration/getFlaggedTickets",{associateOId:t})}updateFlaggedTickets(t,e){return this.$http.post("/api/ams/configuration/updateFlaggedTickets",{associateOId:t,flaggedTickets:e})}getTicketsFilter(t,e,i,s){return this.$http.post("/api/ams/reports/getTicketsFilter",{status:t,filters:e,ticketList:i,searchParameter:s})}getStatusCount(t){return this.$http.post("/api/ams/reports/getStatusCount",{ticketList:t})}downloadTickets(t){return this.$http.post("/api/ams/reports/downloadTickets",{ticketList:t})}getSLAByTicketId(t){return this.$http.post("/api/ams/ticket/getSLAByTicketId",{ticketId:t})}getLocationList(){return this.$http.post("/api/tsPrimary/getLocationList",{})}updateTaskLocation(t,e,i,s,a,r){return this.$http.post("/api/ams/tasks/updateTaskLocation",{task_id:t,taskItem:e,old_loc:i,new_loc:s,value:a,ticket_id:r})}getSupportTeams(){return this.$http.post("/api/ams/configuration/getSupportTeams",{})}getReportedByList(t){return this.$http.post("/api/ams/configuration/getReportedByList",{project_item_id:t})}editTicketDetails(t,e,i,s){return this.$http.post("/api/ams/ticket/editTicketDetails",{ticket_id:t,key:e,value:i,prev_value:s})}getTicketsFilterSR(t,e){return this.$http.post("/api/ams/reports/getTicketsFilter",{filterConfig:t,ticketList:e})}updateNewWfPluginId(t,e,i){return this.$http.post("/api/ams/ticket/updateNewWfPluginId",{wf_plugin_data:e,ticket_id:t,status_object:i})}getActiveLabel(){return this.$http.post("/api/ams/ticket/getActiveLabelData",{})}getActivePriority(){return new Promise((t,e)=>{0==this.priorityArray.length?this.$http.post("/api/ams/ticket/getActivePriorityData",{}).subscribe(e=>{this.priorityArray=e,t(this.priorityArray)}):t(this.priorityArray)})}getActivePriorityFromDB(){return Object(s.c)(this,void 0,void 0,(function*(){return this.$http.post("/api/ams/ticket/getActivePriorityData",{}).subscribe(t=>(this.priorityArray=t,this.priorityArray))}))}getStatusColor(){return new Promise((t,e)=>{0==this.statusColorArray.length?this.$http.post("/api/ams/ticket/getStatusColorData",{}).subscribe(e=>{this.statusColorArray=e,t(this.statusColorArray)}):t(this.statusColorArray)})}getStatusColorFromDB(){return Object(s.c)(this,void 0,void 0,(function*(){this.$http.post("/api/ams/ticket/getStatusColorData",{}).subscribe(t=>(this.statusColorArray=t,this.statusColorArray))}))}checkAdminRole(){return this.$http.post("/api/ams/reports/checkForAdmin",{object_id:530,application_id:95})}getSupportTeamsForAdmin(){return this.$http.post("/api/ams/reports/getSupportTeamsForAdmin",{})}updateSupportTeamForAdmin(t){return this.$http.post("/api/ams/reports/updateSupportTeamForAdmin",{formData:t})}deleteSupportTeamForAdmin(t){return this.$http.post("/api/ams/reports/deleteSupportTeamForAdmin",{team_id:t})}}return t.\u0275fac=function(e){return new(e||t)(o["\u0275\u0275inject"](d.c),o["\u0275\u0275inject"](l.a),o["\u0275\u0275inject"](c.a),o["\u0275\u0275inject"](h.a))},t.\u0275prov=o["\u0275\u0275defineInjectable"]({token:t,factory:t.\u0275fac,providedIn:"root"}),t})()}}]);