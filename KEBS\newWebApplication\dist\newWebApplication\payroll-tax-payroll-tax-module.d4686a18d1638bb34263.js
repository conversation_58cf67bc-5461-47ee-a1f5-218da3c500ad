(window.webpackJsonp=window.webpackJsonp||[]).push([[844],{"/3CJ":function(t,e,n){"use strict";n.r(e),n.d(e,"PayrollTaxModule",(function(){return d}));var o=n("ofXK"),r=n("tyNb"),i=n("fXoL"),p=n("tk/3"),s=n("XXEo");let a=(()=>{class t{constructor(t,e){this._http=t,this._login=e,this.getPayrollApps=()=>this._http.post("/api/appraisal/configuration/getPayrollApps",{})}}return t.\u0275fac=function(e){return new(e||t)(i["\u0275\u0275inject"](p.c),i["\u0275\u0275inject"](s.a))},t.\u0275prov=i["\u0275\u0275defineInjectable"]({token:t,factory:t.\u0275fac,providedIn:"root"}),t})();function l(t,e){if(1&t){const t=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",9),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](t);const n=e.$implicit;return i["\u0275\u0275nextContext"](2).openApp(n)})),i["\u0275\u0275elementStart"](1,"div",10),i["\u0275\u0275elementStart"](2,"div",11),i["\u0275\u0275element"](3,"img",12),i["\u0275\u0275elementEnd"](),i["\u0275\u0275element"](4,"div",13),i["\u0275\u0275elementStart"](5,"div",14),i["\u0275\u0275elementStart"](6,"span",15),i["\u0275\u0275text"](7),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}if(2&t){const t=e.$implicit;i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("src",t.img_url,i["\u0275\u0275sanitizeUrl"]),i["\u0275\u0275advance"](4),i["\u0275\u0275textInterpolate1"](" ",t.display_name,"")}}function c(t,e){if(1&t&&(i["\u0275\u0275elementStart"](0,"div",5),i["\u0275\u0275elementStart"](1,"div",6),i["\u0275\u0275elementStart"](2,"div",7),i["\u0275\u0275template"](3,l,8,2,"div",8),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&t){const t=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngForOf",t.apps)}}const m=[{path:"",component:(()=>{class t{constructor(t){this._PayrollService=t,this.apps=[]}getApps(){this._PayrollService.getPayrollApps().subscribe(t=>{this.apps=t.data},t=>{console.log(t)})}openApp(t){window.open(t.url,"_blank")}ngOnInit(){this.getApps()}}return t.\u0275fac=function(e){return new(e||t)(i["\u0275\u0275directiveInject"](a))},t.\u0275cmp=i["\u0275\u0275defineComponent"]({type:t,selectors:[["app-payroll-tax"]],decls:7,vars:2,consts:[[1,"container-fluid","reports-home-styles","pl-2","pr-0"],[1,"col-3","d-flex","pl-0","pb-2",2,"padding-top","15px"],[1,"my-auto","sub-heading","pl-2"],[1,"my-auto","heading","pl-2"],["class","row pt-0 pb-2",4,"ngIf"],[1,"row","pt-0","pb-2"],[1,"col-12","pl-2","pr-2"],[1,"tiles-wrapper"],["class","card tiles mr-3 mb-3",3,"click",4,"ngFor","ngForOf"],[1,"card","tiles","mr-3","mb-3",3,"click"],[1,"card-body","pt-2","pb-1","pl-1","pr-1"],[1,"row","pt-2","pb-1","d-flex","justify-content-center"],["height","55px","width","55px",3,"src"],[1,"layer"],[1,"row","d-flex","pt-2","pb-2"],[1,"mx-auto","tiles-title"]],template:function(t,e){1&t&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275elementStart"](1,"div",1),i["\u0275\u0275elementStart"](2,"span",2),i["\u0275\u0275text"](3,"Total Apps : "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](4,"span",3),i["\u0275\u0275text"](5),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](6,c,4,1,"div",4),i["\u0275\u0275elementEnd"]()),2&t&&(i["\u0275\u0275advance"](5),i["\u0275\u0275textInterpolate"](e.apps.length),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",e.apps.length>0))},directives:[o.NgIf,o.NgForOf],styles:[".reports-home-styles[_ngcontent-%COMP%]   .sub-heading[_ngcontent-%COMP%]{color:#66615b;font-size:14px;font-weight:400}.reports-home-styles[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]{color:#1a1a1a;font-size:14px;font-weight:500;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.reports-home-styles[_ngcontent-%COMP%]   .arrow-icons[_ngcontent-%COMP%]{color:#868683;font-size:18px}.reports-home-styles[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#3c3c3c;font-weight:400;font-size:18px}.reports-home-styles[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field{width:40vw;padding-top:5px}.reports-home-styles[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-infix{font-size:14px!important;padding:.5em 0!important;border-top:.54375em solid transparent!important}.reports-home-styles[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-wrapper{padding-bottom:0!important}.reports-home-styles[_ngcontent-%COMP%]   .trend-button-inactive[_ngcontent-%COMP%]{margin-top:3px!important;line-height:8px;width:26px;height:26px;margin-right:5px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.reports-home-styles[_ngcontent-%COMP%]   .trend-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.reports-home-styles[_ngcontent-%COMP%]   .trend-button-active[_ngcontent-%COMP%]{background-color:#cf0001!important;margin-top:3px!important;line-height:8px;width:26px;height:26px;margin-right:5px!important;box-shadow:0 3px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.reports-home-styles[_ngcontent-%COMP%]   .trend-button-active[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#fff;font-size:18px}.reports-home-styles[_ngcontent-%COMP%]   .drop-btn[_ngcontent-%COMP%]{font-size:13px!important;height:38px!important;line-height:2!important;padding:0 13px}.reports-home-styles[_ngcontent-%COMP%]   .tiles-wrapper[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap}.reports-home-styles[_ngcontent-%COMP%]   .tiles[_ngcontent-%COMP%]{width:140px;height:119px;transition:all .3s;overflow:hidden;animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}.reports-home-styles[_ngcontent-%COMP%]   .tiles-title[_ngcontent-%COMP%]{z-index:5;position:relative;font-size:14px;font-weight:300;color:#66615b;transition:all .1s}.reports-home-styles[_ngcontent-%COMP%]   .bookmark-button[_ngcontent-%COMP%]{visibility:hidden}.reports-home-styles[_ngcontent-%COMP%]   .bookmark-button[_ngcontent-%COMP%], .reports-home-styles[_ngcontent-%COMP%]   .bookmark-button-marked[_ngcontent-%COMP%]{width:27px!important;height:27px!important;line-height:25px!important;position:absolute;right:0;top:-5px}.reports-home-styles[_ngcontent-%COMP%]   .tile-bookmark-icon[_ngcontent-%COMP%]{font-size:21px!important;color:#66615b!important}.reports-home-styles[_ngcontent-%COMP%]   .layer[_ngcontent-%COMP%]{height:100px;border-radius:50%;right:-5px;width:108%;bottom:-34px;position:absolute;background-color:rgba(207,0,1,.6705882352941176);visibility:hidden}.reports-home-styles[_ngcontent-%COMP%]   .layer[_ngcontent-%COMP%], .reports-home-styles[_ngcontent-%COMP%]   .tiles[_ngcontent-%COMP%]:hover{box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.reports-home-styles[_ngcontent-%COMP%]   .tiles[_ngcontent-%COMP%]:hover{cursor:pointer}.reports-home-styles[_ngcontent-%COMP%]   .tiles[_ngcontent-%COMP%]:hover   .tiles-title[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#fff}.reports-home-styles[_ngcontent-%COMP%]   .tiles[_ngcontent-%COMP%]:hover   .bookmark-button[_ngcontent-%COMP%]{visibility:visible;animation:slide-in-top-bookmark-icon .2s cubic-bezier(.25,.46,.45,.94) both}.reports-home-styles[_ngcontent-%COMP%]   .tiles[_ngcontent-%COMP%]:hover   .layer[_ngcontent-%COMP%]{visibility:visible;animation:slide-in-bottom .3s cubic-bezier(.25,.46,.45,.94) both}.reports-home-styles[_ngcontent-%COMP%]   .slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-20px);opacity:0}to{transform:translateY(0);opacity:1}}@keyframes slide-in-top-bookmark-icon{0%{transform:translateY(-7px);opacity:0}to{transform:translateY(0);opacity:1}}@keyframes slide-in-bottom{0%{transform:translateY(20px);opacity:0}to{transform:translateY(0);opacity:1}}"]}),t})()}];let g=(()=>{class t{}return t.\u0275mod=i["\u0275\u0275defineNgModule"]({type:t}),t.\u0275inj=i["\u0275\u0275defineInjector"]({factory:function(e){return new(e||t)},imports:[[r.k.forChild(m)],r.k]}),t})(),d=(()=>{class t{}return t.\u0275mod=i["\u0275\u0275defineNgModule"]({type:t}),t.\u0275inj=i["\u0275\u0275defineInjector"]({factory:function(e){return new(e||t)},imports:[[o.CommonModule,g]]}),t})()}}]);