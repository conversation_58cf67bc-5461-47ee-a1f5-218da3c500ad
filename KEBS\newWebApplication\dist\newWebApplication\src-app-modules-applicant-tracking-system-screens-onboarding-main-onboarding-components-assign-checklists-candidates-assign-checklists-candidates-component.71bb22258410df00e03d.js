(window.webpackJsonp=window.webpackJsonp||[]).push([[921],{"0pLx":function(e,t,n){"use strict";n.r(t),n.d(t,"MY_FORMATS",(function(){return F})),n.d(t,"AssignChecklistsCandidatesComponent",(function(){return j}));var a=n("mrSG"),i=n("0IaG"),r=n("3Pt+"),o=n("1yaQ"),l=n("FKr1"),s=n("wd/R"),c=n("1G5W"),d=n("XNiG"),p=n("fXoL"),m=n("XNFG"),g=n("XXEo"),u=n("RThm"),f=n("ofXK"),h=n("su5B"),C=n("UVjm"),v=n("kmnG"),y=n("qFsG"),b=n("iadO"),M=n("NFeN"),_=n("Xa2L");function P(e,t){}function O(e,t){}function x(e,t){}function S(e,t){1&e&&(p["\u0275\u0275elementContainerStart"](0),p["\u0275\u0275text"](1," Assign "),p["\u0275\u0275elementContainerEnd"]())}function k(e,t){1&e&&p["\u0275\u0275element"](0,"mat-spinner",23)}function w(e,t){1&e&&(p["\u0275\u0275elementStart"](0,"span",24),p["\u0275\u0275text"](1,"*"),p["\u0275\u0275elementEnd"]())}const I=function(){return{field:"checklists"}},D=function(){return[]},E=function(){return{field:"priority"}},Y=function(e){return{"pointer-events":e}},F={parse:{dateInput:"DD MMM YYYY"},display:{dateInput:"DD MMM YYYY",monthYearLabel:"MMM YYYY",dateA11yLabel:"LL",monthYearA11yLabel:"MMMM YYYY"}};let j=(()=>{class e{constructor(e,t,n,a,i){this.data=e,this._dialogRef=t,this._toaster=n,this._loginService=a,this._onboardingService=i,this._onDestroy=new d.b,this.currentDate=s(),this.isApiInProgress=!1,this.checklists=new r.j(null,[r.H.required]),this.priority=new r.j(null,[r.H.required]),this.dueDate=new r.j(null,[r.H.required])}ngOnInit(){return Object(a.c)(this,void 0,void 0,(function*(){this.aid=this._loginService.getProfile().profile.aid}))}onClose(){this._dialogRef.close(!1)}onCustomSelectValueChange(e){this[e.data.field].setValue(e.val)}onClickYes(){return Object(a.c)(this,void 0,void 0,(function*(){if(this.checklists.invalid||this.dueDate.invalid||this.priority.invalid)return void this._toaster.showWarning("Warning \u26a0\ufe0f","Kindly Fill All Mandatory Fields!",7e3);let e={updatedValue:{checklistIds:this.checklists.value,priority:this.priority.value,dueDate:s(this.dueDate.value).format("YYYY-MM-DD")},selectedDetails:this.data.selectedDetails,unselectedDetails:this.data.unselectedDetails,isBulkSelectActive:this.data.isBulkSelectActive,searchParams:this.data.searchParams,onboardingStatus:this.data.onboardingStatusId,groupId:this.data.groupId};yield this.assignChecklistsToCandidatesInBulk(e),this._dialogRef.close(!0)}))}assignChecklistsToCandidatesInBulk(e){return Object(a.c)(this,void 0,void 0,(function*(){return this.isApiInProgress=!0,new Promise((t,n)=>this._onboardingService.assignChecklistsToCandidatesInBulk(e).pipe(Object(c.a)(this._onDestroy)).subscribe({next:e=>{0==e.err?this._toaster.showSuccess("Success \u2705",e.msg,3e3):this._toaster.showError("Error",e.msg,3e3),this.isApiInProgress=!1,t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Assign Failed!",3e3),this.isApiInProgress=!1,n()}}))}))}}return e.\u0275fac=function(t){return new(t||e)(p["\u0275\u0275directiveInject"](i.a),p["\u0275\u0275directiveInject"](i.h),p["\u0275\u0275directiveInject"](m.a),p["\u0275\u0275directiveInject"](g.a),p["\u0275\u0275directiveInject"](u.a))},e.\u0275cmp=p["\u0275\u0275defineComponent"]({type:e,selectors:[["app-assign-checklists-candidates"]],features:[p["\u0275\u0275ProvidersFeature"]([{provide:l.c,useClass:o.c,deps:[l.f,o.a]},{provide:l.e,useValue:F}])],decls:37,vars:30,consts:[[1,"d-flex","flex-column","bg-container"],[1,"title-text"],[1,"sub-title-text"],[1,"first-row"],[1,"label"],[3,"ngTemplateOutlet"],[3,"type","placeholder","masterData","selectedValues","data","onValueChange"],[1,"second-row"],[1,"column-align"],[3,"placeholder","masterData","selectedValue","displayClose","data","onValueChange"],["appearance","outline",1,"form-field-class"],[1,"date-picker"],["matInput","","disabled","",3,"placeholder","matDatepicker","min","formControl"],[3,"for"],["matDatepickerToggleIcon","",1,"calendar-icon"],["disabled","false"],["dp",""],[1,"d-flex","justify-content-end",2,"gap","8px","margin-top","16px"],[1,"cancel-btn",3,"ngStyle","click"],[1,"yes-btn",3,"ngStyle","click"],[4,"ngIf","ngIfElse"],["loading",""],["mandatoryTemplate",""],["diameter","20",1,"white-spinner"],[2,"color","#cf0001"]],template:function(e,t){if(1&e&&(p["\u0275\u0275elementStart"](0,"div",0),p["\u0275\u0275elementStart"](1,"div",1),p["\u0275\u0275text"](2,"Assign Checklists"),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](3,"div",2),p["\u0275\u0275text"](4),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](5,"div",3),p["\u0275\u0275elementStart"](6,"div",4),p["\u0275\u0275text"](7," Checklists "),p["\u0275\u0275template"](8,P,0,0,"ng-template",5),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](9,"app-multi-select-chip",6),p["\u0275\u0275listener"]("onValueChange",(function(e){return t.onCustomSelectValueChange(e)})),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](10,"div",7),p["\u0275\u0275elementStart"](11,"div",8),p["\u0275\u0275elementStart"](12,"div",4),p["\u0275\u0275text"](13," Priority "),p["\u0275\u0275template"](14,O,0,0,"ng-template",5),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](15,"app-single-select-chip",9),p["\u0275\u0275listener"]("onValueChange",(function(e){return t.onCustomSelectValueChange(e)})),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](16,"div",8),p["\u0275\u0275elementStart"](17,"div",4),p["\u0275\u0275text"](18," Due Date "),p["\u0275\u0275template"](19,x,0,0,"ng-template",5),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](20,"mat-form-field",10),p["\u0275\u0275elementStart"](21,"div",11),p["\u0275\u0275element"](22,"input",12),p["\u0275\u0275elementStart"](23,"mat-datepicker-toggle",13),p["\u0275\u0275elementStart"](24,"mat-icon",14),p["\u0275\u0275text"](25," calendar_today "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275element"](26,"mat-datepicker",15,16),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](28,"div",17),p["\u0275\u0275elementStart"](29,"div",18),p["\u0275\u0275listener"]("click",(function(){return t.onClose()})),p["\u0275\u0275text"](30," Cancel "),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementStart"](31,"div",19),p["\u0275\u0275listener"]("click",(function(){return t.onClickYes()})),p["\u0275\u0275template"](32,S,2,0,"ng-container",20),p["\u0275\u0275template"](33,k,1,0,"ng-template",null,21,p["\u0275\u0275templateRefExtractor"]),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275elementEnd"](),p["\u0275\u0275template"](35,w,2,0,"ng-template",null,22,p["\u0275\u0275templateRefExtractor"])),2&e){const e=p["\u0275\u0275reference"](27),n=p["\u0275\u0275reference"](34),a=p["\u0275\u0275reference"](36);p["\u0275\u0275advance"](4),p["\u0275\u0275textInterpolate"](t.data.subTitle),p["\u0275\u0275advance"](4),p["\u0275\u0275property"]("ngTemplateOutlet",a),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("type",1)("placeholder","Select Checklists")("masterData",t.data.checklistsMaster)("selectedValues",t.checklists.value)("data",p["\u0275\u0275pureFunction0"](23,I)),p["\u0275\u0275advance"](5),p["\u0275\u0275property"]("ngTemplateOutlet",a),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("placeholder","Select Priority")("masterData",t.data.priorityMaster||p["\u0275\u0275pureFunction0"](24,D))("selectedValue",t.priority.value)("displayClose",!1)("data",p["\u0275\u0275pureFunction0"](25,E)),p["\u0275\u0275advance"](4),p["\u0275\u0275property"]("ngTemplateOutlet",a),p["\u0275\u0275advance"](3),p["\u0275\u0275property"]("placeholder","DD MMM YYYY")("matDatepicker",e)("min",t.currentDate)("formControl",t.dueDate),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("for",e),p["\u0275\u0275advance"](6),p["\u0275\u0275property"]("ngStyle",p["\u0275\u0275pureFunction1"](26,Y,t.isApiInProgress?"none":"")),p["\u0275\u0275advance"](2),p["\u0275\u0275property"]("ngStyle",p["\u0275\u0275pureFunction1"](28,Y,t.isApiInProgress?"none":"")),p["\u0275\u0275advance"](1),p["\u0275\u0275property"]("ngIf",!t.isApiInProgress)("ngIfElse",n)}},directives:[f.NgTemplateOutlet,h.a,C.a,v.c,y.b,b.g,r.e,r.v,r.k,b.i,M.a,b.j,b.f,f.NgStyle,f.NgIf,_.c],styles:[".bg-container[_ngcontent-%COMP%]{padding:24px}.bg-container[_ngcontent-%COMP%]   .title-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:18px;font-weight:700;color:#111434;margin-bottom:4px}.bg-container[_ngcontent-%COMP%]   .sub-title-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:12px;font-weight:400;color:#8b95a5;margin-bottom:12px}.bg-container[_ngcontent-%COMP%]   .first-row[_ngcontent-%COMP%]{width:100%;margin-bottom:12px}.bg-container[_ngcontent-%COMP%]   .second-row[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;gap:16px;width:100%}.bg-container[_ngcontent-%COMP%]   .second-row[_ngcontent-%COMP%]   .column-align[_ngcontent-%COMP%]{display:flex;flex-direction:column;width:100%}.bg-container[_ngcontent-%COMP%]   .date-picker[_ngcontent-%COMP%]{display:flex;align-items:center;height:17px}.bg-container[_ngcontent-%COMP%]   .calendar-icon[_ngcontent-%COMP%]{font-size:16px}.bg-container[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:500;color:#6e7b8f}.bg-container[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{border:1px solid #45546e;border-radius:8px;padding:11px 15px;color:#45546e}.bg-container[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%], .bg-container[_ngcontent-%COMP%]   .yes-btn[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:700;cursor:pointer}.bg-container[_ngcontent-%COMP%]   .yes-btn[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;background:linear-gradient(270deg,var(--atsprimaryColor),var(--atssecondaryColor) 105.29%);border-radius:8px;padding:0 12px;color:#fff}.bg-container[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]{font-size:12px;font-family:var(--atsfontFamily)}.bg-container[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-input-element, .bg-container[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-input-element:disabled{color:#45546e!important}.bg-container[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-form-field{font-size:12px!important;font-family:var(--atsfontFamily)!important}.bg-container[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-form-field-wrapper, .bg-container[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-wrapper{padding-bottom:0!important}  .white-spinner circle{stroke:#fff!important}"]}),e})()}}]);