(window.webpackJsonp=window.webpackJsonp||[]).push([[799],{"8clT":function(t,i,e){"use strict";e.r(i),e.d(i,"PracticeWiseComponent",(function(){return C})),e.d(i,"PracticeWiseModule",(function(){return L}));var n=e("mrSG"),a=e("dHLR"),o=e("33Jv"),s=e("ofXK"),c=e("NFeN"),r=e("bTqV"),l=e("Xa2L"),d=e("Qu3c"),h=e("STbY"),u=e("cguG"),f=e("VI6+"),g=e("fXoL"),m=e("0IaG"),v=e("2Clw"),b=e("ek25");let C=(()=>{class t{constructor(t,i,e,n){this.dialog=t,this._dvService=i,this.changeDectRef=e,this._pmoDashboardService=n,this.subs=new o.a,this.startLimit=0,this.endLimit=15,this.defaultCount=15,this.count=0,this.columnConfig=[]}setBodyLayoutConfig(){this.blInstance.layoutConfig.layoutHeight="45vh"}ngAfterViewInit(){return Object(n.c)(this,void 0,void 0,(function*(){this.setBodyLayoutConfig(),this.subs.sink=this._dvService.getUdrfFilterData().subscribe(t=>Object(n.c)(this,void 0,void 0,(function*(){t&&(t.startLimit=0,t.endLimit=15,this.filterConfig=t,yield this.getPracticeList(t))})))}))}getPracticeList(t){return Object(n.c)(this,void 0,void 0,(function*(){this.blInstance.layoutConfig.isMainDataLoading=!0;for(let t=0;t<this._pmoDashboardService.summaryCardData.length;t++)1==this._pmoDashboardService.summaryCardData[t].isSelected&&(this.count+=1,console.log("Count",this.count),this.columnConfig.push(this._pmoDashboardService.summaryCardData[t].type),console.log("columnConfiggg",this.columnConfig));this._dvService.colConfig=this.columnConfig;let i=yield this.getMainListData(t,"practice");this.formatChildList(i.dataList,!0,0),this.blInstance.mainData.dataList=i.dataList,this.blInstance.mainData.colList=i.colList,this.blInstance.layoutConfig.isMainDataLoading=!1}))}ngOnInit(){return Object(n.c)(this,void 0,void 0,(function*(){}))}formatChildList(t,i,e){var n;for(let a of t)a.loadChild=!1,a.showChild=i,a.rowLevel=e,(null===(n=a.children)||void 0===n?void 0:n.length)>0&&this.formatChildList(a.children,!1,e+1)}getMainListData(t,i){return new Promise((e,n)=>{this.subs.sink=this._dvService.getProjectActivityList(t,i).subscribe(t=>{"S"==t.messType&&e(t)},t=>{console.log(t),n(t)})})}openInfoDialog(t){return Object(n.c)(this,void 0,void 0,(function*(){let i={filterConfig:this.filterConfig,dataParams:t,type:"practice"};const{DetailedTaskDialogComponent:n}=yield e.e(882).then(e.bind(null,"k6no"));this.dialog.open(n,{height:"80%",width:"75%",data:i,panelClass:"detailed-task-dialog"}).afterClosed().subscribe(t=>{console.log(t)})}))}onDataScrolled(t){return Object(n.c)(this,void 0,void 0,(function*(){this.blInstance.layoutConfig.lazyLoadDataLoading=!0,this.filterConfig.startLimit=this.filterConfig.endLimit,this.filterConfig.endLimit+=this.defaultCount;let t=yield this.getMainListData(this.filterConfig,"practice");this.formatChildList(t.dataList,!0,0),this.blInstance.mainData.dataList=this.blInstance.mainData.dataList.concat(t.dataList),this.blInstance.layoutConfig.lazyLoadDataLoading=!1}))}ngOnDestroy(){this.subs.unsubscribe()}}return t.\u0275fac=function(i){return new(i||t)(g["\u0275\u0275directiveInject"](m.b),g["\u0275\u0275directiveInject"](v.a),g["\u0275\u0275directiveInject"](g.ChangeDetectorRef),g["\u0275\u0275directiveInject"](b.a))},t.\u0275cmp=g["\u0275\u0275defineComponent"]({type:t,selectors:[["app-practice-wise"]],viewQuery:function(t,i){if(1&t&&g["\u0275\u0275viewQuery"](a.a,!0),2&t){let t;g["\u0275\u0275queryRefresh"](t=g["\u0275\u0275loadQuery"]())&&(i.blInstance=t.first)}},decls:2,vars:0,consts:[[1,"projects"],[3,"valueClicked","verticalScrollEvent"]],template:function(t,i){1&t&&(g["\u0275\u0275elementStart"](0,"div",0),g["\u0275\u0275elementStart"](1,"pmo-body-layout",1),g["\u0275\u0275listener"]("valueClicked",(function(t){return i.openInfoDialog(t)}))("verticalScrollEvent",(function(t){return i.onDataScrolled(t)})),g["\u0275\u0275elementEnd"](),g["\u0275\u0275elementEnd"]())},directives:[a.a],styles:[""]}),t})(),L=(()=>{class t{}return t.\u0275mod=g["\u0275\u0275defineNgModule"]({type:t}),t.\u0275inj=g["\u0275\u0275defineInjector"]({factory:function(i){return new(i||t)},imports:[[s.CommonModule,u.a,c.b,r.b,l.b,d.b,h.e,f.b]]}),t})()}}]);