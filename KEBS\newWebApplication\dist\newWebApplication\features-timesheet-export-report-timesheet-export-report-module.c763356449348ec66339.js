(window.webpackJsonp=window.webpackJsonp||[]).push([[675,634,858],{ELzc:function(e,t,n){"use strict";n.r(t),n.d(t,"TimesheetExportReportModule",(function(){return K}));var r=n("ofXK"),o=n("tyNb"),l=n("mrSG"),a=n("XNiG"),i=n("1G5W"),s=n("wd/R"),d=n("FKr1"),c=n("1yaQ"),p=n("3Pt+"),m=n("fXoL"),h=n("jtHE"),u=n("NJ67"),f=n("xG9w"),v=n("kmnG"),g=n("d3UM"),y=n("WJ5W"),b=n("bSwM");function C(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"mat-label"),m["\u0275\u0275text"](1),m["\u0275\u0275elementEnd"]()),2&e){const e=m["\u0275\u0275nextContext"]();m["\u0275\u0275advance"](1),m["\u0275\u0275textInterpolate"](e.placeholder)}}const M=function(){return{standalone:!0}};function x(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div",6),m["\u0275\u0275elementStart"](1,"mat-checkbox",7),m["\u0275\u0275listener"]("ngModelChange",(function(t){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"]().allSelected=t}))("change",(function(){return m["\u0275\u0275restoreView"](e),m["\u0275\u0275nextContext"]().toggleAllSelection()})),m["\u0275\u0275text"](2,"Select All"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}if(2&e){const e=m["\u0275\u0275nextContext"]();m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngModel",e.allSelected)("ngModelOptions",m["\u0275\u0275pureFunction0"](2,M))}}function D(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"mat-option",8),m["\u0275\u0275text"](1),m["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=m["\u0275\u0275nextContext"]();m["\u0275\u0275property"]("value",e[n.idKey]),m["\u0275\u0275advance"](1),m["\u0275\u0275textInterpolate1"](" ",e[n.valueKey]," ")}}let S=(()=>{class e extends u.a{constructor(){super(),this.fieldCtrl=new p.j,this.fieldFilterCtrl=new p.j,this.filteredList=new h.a,this.showLabel=!1,this.list=[],this.required=!1,this.idKey="id",this.valueKey="name",this.disabled=!1,this.selectAllFlag=!0,this.valueChange=new m.EventEmitter,this._onDestroy=new a.b,this.allSelected=!1}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(i.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(i.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){null!=this.list&&this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>{var n,r;return(null===(r=null===(n=t[this.valueKey])||void 0===n?void 0:n.toLowerCase())||void 0===r?void 0:r.indexOf(e))>-1}))):this.filteredList.next(this.list.slice())}emptyAllSelection(){this.fieldCtrl.patchValue([]),this.allSelected=!1}toggleAllSelection(){this.fieldCtrl.patchValue(this.allSelected?f.pluck(this.list,"id"):[])}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=m["\u0275\u0275defineComponent"]({type:e,selectors:[["app-multi-select-filter"]],inputs:{showLabel:"showLabel",list:"list",placeholder:"placeholder",required:"required",idKey:"idKey",valueKey:"valueKey",disabled:"disabled",selectAllFlag:"selectAllFlag"},outputs:{valueChange:"valueChange"},features:[m["\u0275\u0275ProvidersFeature"]([{provide:p.t,useExisting:Object(m.forwardRef)(()=>e),multi:!0}]),m["\u0275\u0275InheritDefinitionFeature"],m["\u0275\u0275NgOnChangesFeature"]],decls:8,vars:12,consts:[["appearance","outline",2,"width","100%"],[4,"ngIf"],[3,"formControl","placeholder","required","disabled","multiple"],["noEntriesFoundLabel","No Results Found",3,"formControl","placeholderLabel"],["class","select-all",4,"ngIf"],[3,"value",4,"ngFor","ngForOf"],[1,"select-all"],[3,"ngModel","ngModelOptions","ngModelChange","change"],[3,"value"]],template:function(e,t){1&e&&(m["\u0275\u0275elementStart"](0,"mat-form-field",0),m["\u0275\u0275template"](1,C,2,1,"mat-label",1),m["\u0275\u0275elementStart"](2,"mat-select",2),m["\u0275\u0275elementStart"](3,"mat-option"),m["\u0275\u0275element"](4,"ngx-mat-select-search",3),m["\u0275\u0275elementEnd"](),m["\u0275\u0275template"](5,x,3,3,"div",4),m["\u0275\u0275template"](6,D,2,2,"mat-option",5),m["\u0275\u0275pipe"](7,"async"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()),2&e&&(m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",t.showLabel),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled)("multiple",!0),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngIf",t.selectAllFlag),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",m["\u0275\u0275pipeBind1"](7,10,t.filteredList)))},directives:[v.c,r.NgIf,g.c,p.v,p.k,p.F,d.p,y.a,r.NgForOf,v.g,b.a,p.y],pipes:[r.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}.select-all[_ngcontent-%COMP%]{margin:5px 17px}"]}),e})();var w=n("tk/3");let E=(()=>{class e{constructor(e){this._http=e}getStandardReportData(e,t,n,r){return this._http.post("/api/timesheetv2/Reports/getStandardReportData",{startDate:t,endDate:n,format:e,employeeData:r},{responseType:"blob"})}getEmployeeMasterData(){return this._http.post("/api/timesheetv2/Master/getEmployeeNameMasterData",{})}}return e.\u0275fac=function(t){return new(t||e)(m["\u0275\u0275inject"](w.c))},e.\u0275prov=m["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var F=n("1A3m"),O=n("BVzC"),L=n("qFsG"),Y=n("iadO"),_=n("bTqV");function R(e,t){if(1&e&&(m["\u0275\u0275elementStart"](0,"mat-option",14),m["\u0275\u0275text"](1),m["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;m["\u0275\u0275property"]("value",e.value),m["\u0275\u0275advance"](1),m["\u0275\u0275textInterpolate"](e.report_name)}}const I={parse:{dateInput:"MMMM YYYY"},display:{dateInput:"MMMM YYYY",monthYearLabel:"MMMM YYYY",dateA11yLabel:"LL",monthYearA11yLabel:"MMMM YYYY"}},A=[{path:"",component:(()=>{class e{constructor(e,t,n){this._exportReportService=e,this._toasterService=t,this._errorService=n,this.employeeList=[],this.dateControl=new p.j(s()),this.employeeData=new p.j([]),this._onDestroy=new a.b,this.report=[{isActive:1,report_name:"General Format Report",value:1},{isActive:1,report_name:"Levis Format",value:2},{isActive:1,report_name:"NCS Format",value:3},{isActive:1,report_name:"OCBC Format",value:4}],this.buttonDisable=!1}ngOnInit(){this.getEmployeeListData()}setMonthAndYear(e,t){const n=this.dateControl.value;n.month(e.month()),n.year(e.year()),this.dateControl.setValue(n),this.selectedDate=s(this.dateControl.value).format("YYYY-MM-DD"),t.close()}downloadReport(){let e=s(this.dateControl.value).endOf("month").format("DD");this.endDate=s([s(this.selectedDate).year(),s(this.selectedDate).month(),e]).format("YYYY-MM-DD"),this.startDate=s(this.selectedDate).startOf("month").format("YYYY-MM-DD"),this.buttonDisable=!0,this._exportReportService.getStandardReportData(this.selectedReport,this.startDate,this.endDate,this.employeeData.value&&this.employeeData.value.length>0?this.employeeData.value:[]).pipe(Object(i.a)(this._onDestroy)).subscribe(e=>Object(l.c)(this,void 0,void 0,(function*(){if(0==e.size)return this.buttonDisable=!1,this._toasterService.showInfo("Timesheet Export Report Message","No Data Found",3e3);{const t=new Blob([e],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),n=document.createElement("a");n.href=window.URL.createObjectURL(t),n.download=1==this.selectedReport?"General Format.xlsx":2==this.selectedReport?"Levis Format.xlsx":3==this.selectedReport?"NCS Format.xlsx":4==this.selectedReport?"OCBC Format.xlsx":"Timesheet Report.xlsx",n.click(),this.buttonDisable=!1}})),e=>{this.buttonDisable=!1,this._errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Report Download Failed, Kindly Try After Sometime",e&&e.params?e.params:e&&e.error?e.error.params:{})})}getEmployeeListData(){this._exportReportService.getEmployeeMasterData().pipe(Object(i.a)(this._onDestroy)).subscribe(e=>Object(l.c)(this,void 0,void 0,(function*(){"S"==e.messType&&e.data&&e.data.length>0?(this.employeeList=e.data,console.log("If"),console.log(this.employeeList)):(this.employeeList=[],console.log("Else"),console.log(this.employeeList))})),e=>{this.employeeList=[],this._errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Employee Master Data Could Not Be Retrieved",e&&e.params?e.params:e&&e.error?e.error.params:{})})}}return e.\u0275fac=function(t){return new(t||e)(m["\u0275\u0275directiveInject"](E),m["\u0275\u0275directiveInject"](F.a),m["\u0275\u0275directiveInject"](O.a))},e.\u0275cmp=m["\u0275\u0275defineComponent"]({type:e,selectors:[["app-timesheet-export-report"]],viewQuery:function(e,t){if(1&e&&m["\u0275\u0275viewQuery"](S,!0),2&e){let e;m["\u0275\u0275queryRefresh"](e=m["\u0275\u0275loadQuery"]())&&(t.multiSelectSearch=e)}},features:[m["\u0275\u0275ProvidersFeature"]([{provide:d.c,useClass:c.c,deps:[d.f,c.a]},{provide:d.e,useValue:I}])],decls:22,vars:9,consts:[[1,"p-0","container-fluid","ts-admin-persona"],[1,"col-12","p-2"],[1,"row"],[1,"col-2"],["appearance","outline"],[3,"ngModel","ngModelChange"],[3,"value",4,"ngFor","ngForOf"],["matInput","","placeholder","MMMM-YYYY",3,"matDatepicker","formControl"],["matIconSuffix","",3,"for"],["startView","multi-year",3,"monthSelected"],["dp",""],[1,"col-2","ml-2"],["placeholder","Employee Name",3,"formControl","list","selectAllFlag"],["mat-raised-button","","color","primary",3,"disabled","click"],[3,"value"]],template:function(e,t){if(1&e){const e=m["\u0275\u0275getCurrentView"]();m["\u0275\u0275elementStart"](0,"div",0),m["\u0275\u0275elementStart"](1,"div",1),m["\u0275\u0275elementStart"](2,"div",2),m["\u0275\u0275elementStart"](3,"div",3),m["\u0275\u0275elementStart"](4,"mat-form-field",4),m["\u0275\u0275elementStart"](5,"mat-label"),m["\u0275\u0275text"](6,"Select Report"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](7,"mat-select",5),m["\u0275\u0275listener"]("ngModelChange",(function(e){return t.selectedReport=e})),m["\u0275\u0275template"](8,R,2,2,"mat-option",6),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](9,"div",3),m["\u0275\u0275elementStart"](10,"mat-form-field",4),m["\u0275\u0275elementStart"](11,"mat-label"),m["\u0275\u0275text"](12,"Choose Month"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275element"](13,"input",7),m["\u0275\u0275element"](14,"mat-datepicker-toggle",8),m["\u0275\u0275elementStart"](15,"mat-datepicker",9,10),m["\u0275\u0275listener"]("monthSelected",(function(n){m["\u0275\u0275restoreView"](e);const r=m["\u0275\u0275reference"](16);return t.setMonthAndYear(n,r)})),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](17,"div",11),m["\u0275\u0275element"](18,"app-multi-select-filter",12),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementStart"](19,"div",3),m["\u0275\u0275elementStart"](20,"button",13),m["\u0275\u0275listener"]("click",(function(){return t.downloadReport()})),m["\u0275\u0275text"](21,"Download Report"),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"](),m["\u0275\u0275elementEnd"]()}if(2&e){const e=m["\u0275\u0275reference"](16);m["\u0275\u0275advance"](7),m["\u0275\u0275property"]("ngModel",t.selectedReport),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("ngForOf",t.report),m["\u0275\u0275advance"](5),m["\u0275\u0275property"]("matDatepicker",e)("formControl",t.dateControl),m["\u0275\u0275advance"](1),m["\u0275\u0275property"]("for",e),m["\u0275\u0275advance"](4),m["\u0275\u0275property"]("formControl",t.employeeData)("list",t.employeeList)("selectAllFlag",!1),m["\u0275\u0275advance"](2),m["\u0275\u0275property"]("disabled",t.buttonDisable)}},directives:[v.c,v.g,g.c,p.v,p.y,r.NgForOf,L.b,Y.g,p.e,p.k,Y.i,Y.f,S,_.a,d.p],styles:[".ts-admin-persona[_ngcontent-%COMP%]     .mat-form-field-infix{display:flex;height:40px}.ts-admin-persona[_ngcontent-%COMP%]     .mat-icon-button{bottom:15px}.ts-admin-persona[_ngcontent-%COMP%]   .example-section[_ngcontent-%COMP%]{margin:12px 0}.ts-admin-persona[_ngcontent-%COMP%]   .example-margin[_ngcontent-%COMP%]{margin:0 12px}"]}),e})()}];let j=(()=>{class e{}return e.\u0275mod=m["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=m["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[o.k.forChild(A)],o.k]}),e})();var N=n("NFeN"),k=n("Qu3c"),V=n("1jcm");let K=(()=>{class e{}return e.\u0275mod=m["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=m["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[r.CommonModule,j,g.d,v.e,_.b,N.b,k.b,L.c,Y.h,p.p,p.E,b.b,V.b,y.b]]}),e})()},NJ67:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));class r{constructor(){this.disabled=!1}onChange(e){}onTouched(e){}writeValue(e){this.value=e}registerOnChange(e){this.onChange=e}registerOnTouched(e){this.onTouched=e}setDisabledState(e){this.disabled=e}}}}]);