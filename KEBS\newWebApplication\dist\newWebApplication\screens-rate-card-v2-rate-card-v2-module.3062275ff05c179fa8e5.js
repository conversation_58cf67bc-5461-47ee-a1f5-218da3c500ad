(window.webpackJsonp=window.webpackJsonp||[]).push([[877,214,334,765,878],{"/rGH":function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var i=n("fXoL");let o=(()=>{class e{transform(e,t,n="name"){return e||0===e?(Array.isArray(e)||(e=[e]),e.map(e=>{let i=t.find(t=>t.id==e);return i?i[n]:"-"}).join(", ")):"-"}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=i["\u0275\u0275definePipe"]({name:"masterData",type:e,pure:!0}),e})()},"1+mW":function(e,t,n){"use strict";n.r(t),n.d(t,"ApplicantTrackingSystemModule",(function(){return z}));var i=n("ofXK"),o=n("tyNb"),a=n("mrSG"),r=n("1G5W"),s=n("XNiG"),l=n("yuIm"),c=n("fXoL"),d=n("c7zN"),p=n("XXEo"),m=n("XNFG");let g=(()=>{class e{constructor(e,t,n){this._utilitiesService=e,this._loginService=t,this._toaster=n,this._onDestroy=new s.b}canActivate(e,t){return Object(a.c)(this,void 0,void 0,(function*(){return yield this.checkAccessForAssociate(),!0}))}checkAccessForAssociate(){return Object(a.c)(this,void 0,void 0,(function*(){return new Promise((e,t)=>this._utilitiesService.checkAccessForAssociate(this._loginService.getProfile().profile.aid).pipe(Object(r.a)(this._onDestroy)).subscribe({next:t=>{if(0==t.err)l.setRoleAccessList(t.data);else{let e=!(!t||!t.hasOwnProperty("is_rds_peak"))&&t.is_rds_peak;l.changeRDSvalue(e||!1),l.setRoleAccessList([]),this._toaster.showError("Error",t.msg,7e3)}e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"Role Data Retrieval Failed!",7e3),l.setRoleAccessList([]),t()}}))}))}}return e.\u0275fac=function(t){return new(t||e)(c["\u0275\u0275inject"](d.a),c["\u0275\u0275inject"](p.a),c["\u0275\u0275inject"](m.a))},e.\u0275prov=c["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();const u=[{path:"",canActivate:[g],pathMatch:"full",redirectTo:"jobs"},{path:"jobs",canActivate:[g],loadChildren:()=>n.e(872).then(n.bind(null,"Lp0Z")).then(e=>e.JobsModule),data:{breadcrumb:"Jobs"}},{path:"candidates",canActivate:[g],loadChildren:()=>n.e(871).then(n.bind(null,"X1Mi")).then(e=>e.CandidatesModule),data:{breadcrumb:"Candidates"}},{path:"settings",canActivate:[g],loadChildren:()=>n.e(880).then(n.bind(null,"VSH1")).then(e=>e.SettingsModule),data:{breadcrumb:"Settings"}},{path:"reports",canActivate:[],loadChildren:()=>n.e(0).then(n.bind(null,"Utwq")).then(e=>e.ProductReportModule),data:{breadcrumb:"Reports"}},{path:"onboarding",canActivate:[g],loadChildren:()=>n.e(873).then(n.bind(null,"vMvN")).then(e=>e.OnboardingModule),data:{breadcrumb:"Onboarding"}}];let h=(()=>{class e{}return e.\u0275mod=c["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=c["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[o.k.forChild(u)],o.k]}),e})();var f=n("Xi0T"),C=n("0IaG"),v=n("bSwM"),b=n("Qu3c"),_=n("lVl8"),y=n("NFeN"),O=n("5+WD"),M=n("3Pt+"),w=n("kmnG"),x=n("qFsG"),S=n("rDax"),P=n("Wp6s"),E=n("iadO"),F=n("Xa2L"),D=n("1jcm"),k=n("4/q7"),I=n("f0Cb"),V=n("A5z7"),R=n("d3UM"),L=n("vxfF"),T=n("wZkO"),j=n("dlKe"),A=n("cZdB"),q=n("w4ga"),G=n("WJ5W"),N=n("YhS8"),J=n("pzj6"),B=n("mgaL"),H=n("3beV");let z=(()=>{class e{}return e.\u0275mod=c["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=c["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,H.a,h,f.a,C.g,v.b,b.b,_.b,y.b,j.b,O.g,M.E,M.p,w.e,x.c,S.h,P.d,E.h,A.b,F.b,k.b,D.b,q.b,I.b,V.e,R.d,G.b,N.c.forRoot(),L.g,T.g,J.b,B.a]]}),e})()},"9SfD":function(e,t,n){"use strict";n.r(t),n.d(t,"RateCardV2Module",(function(){return it}));var i=n("ofXK"),o=n("bTqV"),a=n("NFeN"),r=n("Qu3c"),s=n("kmnG"),l=n("bSwM"),c=n("qFsG"),d=n("3Pt+"),p=n("iadO"),m=n("0IaG"),g=n("STbY"),u=n("rDax"),h=n("jaxi"),f=n("QibW"),C=n("FKr1"),v=n("d3UM"),b=n("Xa2L"),_=n("1jcm"),y=n("ZzPI"),O=n("WYlB"),M=n("tyNb"),w=n("mrSG"),x=n("1G5W"),S=n("Kj3r"),P=n("XNiG"),E=n("quSY"),F=n("33Jv"),D=n("1yaQ"),k=n("wd/R"),I=n("fXoL"),V=n("jr6c"),R=n("1A3m"),L=n("0BBf"),T=n("ECHL"),j=n("gVqi"),A=n("pgif"),q=n("UVjm"),G=n("su5B");function N(e,t){1&e&&I["\u0275\u0275elementContainer"](0)}const J=function(e){return{$implicit:e}};function B(e,t){if(1&e&&(I["\u0275\u0275elementContainerStart"](0),I["\u0275\u0275template"](1,N,1,0,"ng-container",19),I["\u0275\u0275elementContainerEnd"]()),2&e){const e=I["\u0275\u0275nextContext"]().$implicit;I["\u0275\u0275nextContext"]();const t=I["\u0275\u0275reference"](16);I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngTemplateOutlet",t)("ngTemplateOutletContext",I["\u0275\u0275pureFunction1"](2,J,e))}}function H(e,t){if(1&e&&(I["\u0275\u0275elementContainerStart"](0),I["\u0275\u0275template"](1,B,2,4,"ng-container",6),I["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit,n=I["\u0275\u0275nextContext"](3);I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",n.section1.includes(e.field_key))}}function z(e,t){1&e&&I["\u0275\u0275elementContainer"](0)}function Q(e,t){if(1&e&&(I["\u0275\u0275elementContainerStart"](0),I["\u0275\u0275template"](1,z,1,0,"ng-container",19),I["\u0275\u0275elementContainerEnd"]()),2&e){const e=I["\u0275\u0275nextContext"]().$implicit;I["\u0275\u0275nextContext"]();const t=I["\u0275\u0275reference"](16);I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngTemplateOutlet",t)("ngTemplateOutletContext",I["\u0275\u0275pureFunction1"](2,J,e))}}function U(e,t){if(1&e&&(I["\u0275\u0275elementContainerStart"](0),I["\u0275\u0275template"](1,Q,2,4,"ng-container",6),I["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit,n=I["\u0275\u0275nextContext"](3);I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",!n.section1.includes(e.field_key)&&!n.section3.includes(e.field_key)&&"is_active"!==e.field_key)}}function W(e,t){1&e&&I["\u0275\u0275elementContainer"](0)}function $(e,t){if(1&e&&(I["\u0275\u0275elementContainerStart"](0),I["\u0275\u0275template"](1,W,1,0,"ng-container",19),I["\u0275\u0275elementContainerEnd"]()),2&e){const e=I["\u0275\u0275nextContext"]().$implicit;I["\u0275\u0275nextContext"]();const t=I["\u0275\u0275reference"](16);I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngTemplateOutlet",t)("ngTemplateOutletContext",I["\u0275\u0275pureFunction1"](2,J,e))}}function K(e,t){if(1&e&&(I["\u0275\u0275elementContainerStart"](0),I["\u0275\u0275template"](1,$,2,4,"ng-container",6),I["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit,n=I["\u0275\u0275nextContext"](3);I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",n.section3.includes(e.field_key))}}function X(e,t){1&e&&(I["\u0275\u0275elementStart"](0,"span",26),I["\u0275\u0275text"](1,"*"),I["\u0275\u0275elementEnd"]())}function Y(e,t){1&e&&I["\u0275\u0275elementContainer"](0)}const Z=function(){return[]},ee=function(e){return{field:e}};function te(e,t){if(1&e){const e=I["\u0275\u0275getCurrentView"]();I["\u0275\u0275elementStart"](0,"app-single-select-chip",27),I["\u0275\u0275listener"]("onValueChange",(function(t){return I["\u0275\u0275restoreView"](e),I["\u0275\u0275nextContext"](4).onCustomSelectValueChange(t)})),I["\u0275\u0275elementEnd"]()}if(2&e){const e=I["\u0275\u0275nextContext"]().$implicit,t=I["\u0275\u0275nextContext"](3);I["\u0275\u0275property"]("masterData",e.master_data||I["\u0275\u0275pureFunction0"](5,Z))("placeholder",e.field_label)("selectedValue",t.rcFormGroup.get(null==e?null:e.field_key).value)("displayClose",!1)("data",I["\u0275\u0275pureFunction1"](6,ee,null==e?null:e.field_key))}}function ne(e,t){if(1&e){const e=I["\u0275\u0275getCurrentView"]();I["\u0275\u0275elementStart"](0,"app-multi-select-chip",28),I["\u0275\u0275listener"]("onValueChange",(function(t){return I["\u0275\u0275restoreView"](e),I["\u0275\u0275nextContext"](4).onCustomSelectValueChange(t)})),I["\u0275\u0275elementEnd"]()}if(2&e){const e=I["\u0275\u0275nextContext"]().$implicit,t=I["\u0275\u0275nextContext"](3);I["\u0275\u0275property"]("type",2)("placeholder",e.field_label)("masterData",e.master_data||I["\u0275\u0275pureFunction0"](6,Z))("selectedValues",t.rcFormGroup.get(null==e?null:e.field_key).value)("displayClose",!0)("data",I["\u0275\u0275pureFunction1"](7,ee,null==e?null:e.field_key))}}function ie(e,t){if(1&e&&I["\u0275\u0275element"](0,"input",33),2&e){const e=I["\u0275\u0275nextContext"](2).$implicit;I["\u0275\u0275property"]("formControlName",e.field_key)("placeholder",e.field_label)}}function oe(e,t){if(1&e&&I["\u0275\u0275element"](0,"input",34),2&e){const e=I["\u0275\u0275nextContext"](2).$implicit;I["\u0275\u0275property"]("formControlName",e.field_key)("placeholder",e.field_label)}}function ae(e,t){if(1&e&&(I["\u0275\u0275elementStart"](0,"span",35),I["\u0275\u0275text"](1),I["\u0275\u0275elementEnd"]()),2&e){const e=I["\u0275\u0275nextContext"](5);let t=null;I["\u0275\u0275advance"](1),I["\u0275\u0275textInterpolate1"](" ",e.getCurrencyName(null==(t=e.rcFormGroup.get("currency"))?null:t.value)," ")}}function re(e,t){if(1&e&&(I["\u0275\u0275elementStart"](0,"mat-form-field",29),I["\u0275\u0275template"](1,ie,1,2,"input",30),I["\u0275\u0275template"](2,oe,1,2,"input",31),I["\u0275\u0275template"](3,ae,2,1,"span",32),I["\u0275\u0275elementEnd"]()),2&e){const e=I["\u0275\u0275nextContext"]().$implicit,t=I["\u0275\u0275nextContext"](3);let n=null;I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",t.isNameField.includes(e.field_key)),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",!t.isNameField.includes(e.field_key)),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",t.suffixFieldNeeded.includes(e.field_key)&&(null==(n=t.rcFormGroup.get("currency"))?null:n.value))}}function se(e,t){if(1&e){const e=I["\u0275\u0275getCurrentView"]();I["\u0275\u0275elementStart"](0,"mat-form-field",36),I["\u0275\u0275elementStart"](1,"div",37),I["\u0275\u0275elementStart"](2,"input",38),I["\u0275\u0275listener"]("focus",(function(){return I["\u0275\u0275restoreView"](e),I["\u0275\u0275reference"](5).open()})),I["\u0275\u0275elementEnd"](),I["\u0275\u0275element"](3,"mat-datepicker-toggle",39),I["\u0275\u0275element"](4,"mat-datepicker",null,40),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"]()}if(2&e){const e=I["\u0275\u0275reference"](5),t=I["\u0275\u0275nextContext"]().$implicit,n=I["\u0275\u0275nextContext"](3);let i=null,o=null;I["\u0275\u0275advance"](2),I["\u0275\u0275property"]("matDatepicker",e)("formControlName",t.field_key)("placeholder",t.field_label)("min","end_date"===t.field_key?null==(i=n.rcFormGroup.get("start_date"))?null:i.value:null)("max","start_date"===t.field_key?null==(o=n.rcFormGroup.get("end_date"))?null:o.value:null)("matDatepicker",e),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("for",e)}}const le=function(e,t){return{fieldKey:e,isMandatory:t}};function ce(e,t){if(1&e&&(I["\u0275\u0275elementStart"](0,"div",20),I["\u0275\u0275elementStart"](1,"p"),I["\u0275\u0275text"](2),I["\u0275\u0275template"](3,X,2,0,"span",21),I["\u0275\u0275template"](4,Y,1,0,"ng-container",19),I["\u0275\u0275elementEnd"](),I["\u0275\u0275template"](5,te,1,8,"app-single-select-chip",22),I["\u0275\u0275template"](6,ne,1,9,"app-multi-select-chip",23),I["\u0275\u0275template"](7,re,4,3,"mat-form-field",24),I["\u0275\u0275template"](8,se,6,7,"mat-form-field",25),I["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;I["\u0275\u0275nextContext"](2);const n=I["\u0275\u0275reference"](9);I["\u0275\u0275advance"](2),I["\u0275\u0275textInterpolate1"]("",e.field_label," "),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",e.is_mandatory),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngTemplateOutlet",n)("ngTemplateOutletContext",I["\u0275\u0275pureFunction2"](8,le,e.field_key,e.is_mandatory)),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",(null==e?null:e.has_master_data)&&"single_select"===(null==e?null:e.field_type)),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",(null==e?null:e.has_master_data)&&"multi_select"===(null==e?null:e.field_type)),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",!(null!=e&&e.has_master_data)&&"toggle"!==e.field_type&&"start_date"!==e.field_key&&"end_date"!==e.field_key),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf","start_date"===e.field_key||"end_date"===e.field_key)}}function de(e,t){if(1&e&&(I["\u0275\u0275elementStart"](0,"div"),I["\u0275\u0275elementStart"](1,"form",12),I["\u0275\u0275elementStart"](2,"div",13),I["\u0275\u0275elementStart"](3,"h3",14),I["\u0275\u0275text"](4,"Rate Card Details"),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](5,"div",15),I["\u0275\u0275template"](6,H,2,1,"ng-container",16),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](7,"h3",17),I["\u0275\u0275text"](8,"Allocation Details"),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](9,"div",15),I["\u0275\u0275template"](10,U,2,1,"ng-container",16),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](11,"h3",17),I["\u0275\u0275text"](12,"Financial Details"),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](13,"div",15),I["\u0275\u0275template"](14,K,2,1,"ng-container",16),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275template"](15,ce,9,11,"ng-template",null,18,I["\u0275\u0275templateRefExtractor"]),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"]()),2&e){const e=I["\u0275\u0275nextContext"](2);I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("formGroup",e.rcFormGroup),I["\u0275\u0275advance"](5),I["\u0275\u0275property"]("ngForOf",e.formConfig),I["\u0275\u0275advance"](4),I["\u0275\u0275property"]("ngForOf",e.formConfig),I["\u0275\u0275advance"](4),I["\u0275\u0275property"]("ngForOf",e.formConfig)}}function pe(e,t){1&e&&(I["\u0275\u0275elementStart"](0,"div"),I["\u0275\u0275text"](1," No Form Config Found "),I["\u0275\u0275elementEnd"]())}function me(e,t){1&e&&(I["\u0275\u0275elementStart"](0,"span",42),I["\u0275\u0275text"](1," This field is required "),I["\u0275\u0275elementEnd"]())}function ge(e,t){if(1&e&&I["\u0275\u0275template"](0,me,2,0,"span",41),2&e){const e=t.fieldKey,n=t.isMandatory,i=I["\u0275\u0275nextContext"](2);let o=null;I["\u0275\u0275property"]("ngIf",n&&(null==(o=i.rcFormGroup.get(e))?null:o.invalid)&&(null==(o=i.rcFormGroup.get(e))?null:o.touched))}}function ue(e,t){if(1&e){const e=I["\u0275\u0275getCurrentView"]();I["\u0275\u0275elementStart"](0,"button",43),I["\u0275\u0275listener"]("click",(function(){return I["\u0275\u0275restoreView"](e),I["\u0275\u0275nextContext"](2).onBackClick("CLEAR")})),I["\u0275\u0275text"](1," Clear Restored Values "),I["\u0275\u0275elementEnd"]()}}function he(e,t){if(1&e){const e=I["\u0275\u0275getCurrentView"]();I["\u0275\u0275elementStart"](0,"div",2),I["\u0275\u0275elementStart"](1,"div",3),I["\u0275\u0275elementStart"](2,"span"),I["\u0275\u0275text"](3),I["\u0275\u0275elementEnd"](),I["\u0275\u0275element"](4,"mat-slide-toggle",4),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](5,"div",5),I["\u0275\u0275template"](6,de,17,4,"div",6),I["\u0275\u0275template"](7,pe,2,0,"div",6),I["\u0275\u0275template"](8,ge,1,1,"ng-template",null,7,I["\u0275\u0275templateRefExtractor"]),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](10,"div",8),I["\u0275\u0275template"](11,ue,2,0,"button",9),I["\u0275\u0275elementStart"](12,"button",10),I["\u0275\u0275listener"]("click",(function(){return I["\u0275\u0275restoreView"](e),I["\u0275\u0275nextContext"]().onBackClick()})),I["\u0275\u0275text"](13),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](14,"button",11),I["\u0275\u0275listener"]("click",(function(){return I["\u0275\u0275restoreView"](e),I["\u0275\u0275nextContext"]().saveForm()})),I["\u0275\u0275text"](15),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"]()}if(2&e){const e=I["\u0275\u0275nextContext"]();I["\u0275\u0275advance"](3),I["\u0275\u0275textInterpolate1"](" ",e.rcLabel," "),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("formControl",e.is_active),I["\u0275\u0275advance"](2),I["\u0275\u0275property"]("ngIf",(null==e.formConfig?null:e.formConfig.length)&&!e.formLoading),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",!(null!=e.formConfig&&e.formConfig.length||e.formLoading)),I["\u0275\u0275advance"](4),I["\u0275\u0275property"]("ngIf","CREATE"==e.formType&&e.storedFormValues),I["\u0275\u0275advance"](2),I["\u0275\u0275textInterpolate"]("CREATE"==e.formType?"Cancel":"Discard"),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("disabled","CREATE"!==e.formType&&(e.disabled||e.noChange))("matTooltip","CREATE"==e.formType?"":e.disabled?"Updating..":e.noChange?"No Change Recognised in the Form":" "),I["\u0275\u0275advance"](1),I["\u0275\u0275textInterpolate"]("CREATE"==e.formType?"Create":"Update")}}function fe(e,t){1&e&&(I["\u0275\u0275elementStart"](0,"div",44),I["\u0275\u0275element"](1,"img",45),I["\u0275\u0275elementEnd"]())}let Ce=(()=>{class e{constructor(e,t,n,i,o,a,r,s,l){this._quoteService=e,this._toaster=t,this._qbMasterService=n,this._RmService=i,this.route=o,this.location=a,this.udrfServices=r,this.dateAdapter=s,this._os=l,this._onDestroy=new P.b,this.wEMappingEnabled=!1,this.sDMappingEnabled=!1,this.is_active=new d.j(!0),this.rcTypelist=[{label:"Manpower",description:"Cost Per Designation",type:1,is_active:!0},{label:"Non-Manpower",description:"Accomadation, Transportation, etc",type:2,is_active:!1},{label:"License",type:3,is_active:!1}],this.subs=new F.a,this.valueChangeSubscription=new E.a,this.manpowerdatalist=[],this.nonmanpowerdatalist=[],this.licenseDataList=[],this.manpowerFieldConfig=[],this.nonManpowerFieldConfig=[],this.licenseFieldConfig=[],this.manpowerdatalistcolumns=[],this.nonmanpowerdatalistcolumns=[],this.licenseDataListColumns=[],this.nationalityList=[],this.workLocationList=[],this.PositionList=[],this.projectLocationList=[],this.currencyList=[],this.EntityMasterData=[],this.divisionMasterData=[],this.SubDivisionMasterData=[],this.unitMasterData=[{id:1,name:"Hour"},{id:2,name:"Day"},{id:3,name:"Month"}],this.quoteUnitList=[],this.licenseUnitList=[],this.experienceList=[],this.serviceList=[],this.accountIds=[],this.accountList=[],this.orgMappingList=[],this.wEMappingList=[],this.sDMappingList=[],this.rcNameList=[],this.versionList=[],this.technologyList=[],this.skillClassList=[],this.certificationList=[],this.levelList=[],this.mpDataObj={entityList:[],divisionList:[],subDivisionList:[]},this.nmpDataObj={entityList:[],divisionList:[],subDivisionList:[]},this.mpRcFieldConfig=[],this.nmpRcFieldConfig=[],this.liRcFieldConfig=[],this.formLoading=!0,this.isNameField=["license_name","manpower_name","nonmanpower_name","description"],this.suffixFieldNeeded=["cost","revenue"],this.multiSelectFields=["services_tagged"],this.dateFormat="DD-MMM-YYYY",this.section1=["description","license_name","manpower_name","nonmanpower_name","rate_card_name","start_date","end_date"],this.section3=["quote_unit","currency","cost","revenue"],this.disabled=!1,this.noChange=!1,this.storedFormValues=null,this.getRcFieldConfig=(e=1)=>new Promise((t,n)=>{this.subs.sink=this._quoteService.getRcFieldConfiguration(e).subscribe(e=>{this.formConfig=e.data;for(const t of this.formConfig)if(t.has_master_data)switch(t.field_key){case"entity":this.EntityMasterData=t.master_data;break;case"division":this.divisionMasterData=t.master_data;break;case"sub_division":this.SubDivisionMasterData=t.master_data;break;case"position":this.PositionList=t.master_data;break;case"currency":this.currencyList=t.master_data;break;case"project_location":this.projectLocationList=t.master_data;break;case"work_experience":this.experienceList=t.master_data;break;case"nationality":this.nationalityList=t.master_data;break;case"work_location":this.workLocationList=t.master_data;break;case"quote_unit":this.quoteUnitList=t.master_data,this.licenseUnitList=this.quoteUnitList.filter(e=>!e.is_for_position),this.quoteUnitList.map(e=>{if(e.applicable_resource_type)try{let t=JSON.parse(e.applicable_resource_type);Array.isArray(t)&&(t.includes(2)&&(this.nonManpowerUnitList.some(t=>t.id===e.id)||this.nonManpowerUnitList.push(e)),t.includes(3)&&(this.licenseUnitList.some(t=>t.id===e.id)||this.licenseUnitList.push(e)),t.includes(1)&&(this.manpowerUnitList.some(t=>t.id===e.id)||this.manpowerUnitList.push(e)))}catch(t){console.error("Error parsing applicable_resource_type for item:",e,t.message)}});break;case"services_tagged":this.serviceList=t.master_data;break;case"account_id":this.accountList=t.master_data;break;case"rate_card_name":this.rcNameList=t.master_data;break;case"version":this.versionList=t.master_data;break;case"technology":this.technologyList=t.master_data;break;case"skill_class":this.skillClassList=t.master_data;break;case"certification":this.certificationList=t.master_data;break;case"level":this.levelList=t.master_data}this.initRCForm(),console.log("Form Config",this.formConfig),t(!0)},e=>{console.log(e),n(e)})}),this.validateWEMapping=e=>{const t=this.formConfig.find(e=>"work_location"===e.key),n=this.formConfig.find(e=>"entity"===e.key);if(e.work_location){const i=this.wEMappingList.find(t=>t.work_location_id===e.work_location);if(i&&i.entity_id!=e.entity)return this._toaster.showError(`${t?t.label:"Work Location"} <-> ${n?n.label:"Entity"} Mapping is Invalid`,`${n?n.label:"Entity"} Selected for Position - ${e.positionName} doesn't match with ${t?t.label:"Work Location"} Mapped (Kindly remove and Position)`,this._os.longInterval),!1}return!0},this.getWEMappingList=()=>new Promise((e,t)=>{this._qbMasterService.wEMapping.subscribe(t=>{this.wEMappingList=t,e(!0)})}),this.getSDMappingList=()=>new Promise((e,t)=>{this._qbMasterService.sDMapping.subscribe(t=>{this.sDMappingList=t,e(!0)})}),this.getOrgMappingList=()=>new Promise((e,t)=>{this._qbMasterService.orgMapping.subscribe(t=>{this.orgMappingList=t,e(!0)})})}ngOnInit(){return Object(w.c)(this,void 0,void 0,(function*(){this.formLoading=!0,yield this.setTheme(),this.getID(),this.getFormType(),yield this.initializeRcFieldConfig(),this.adjustDynamicHeight(),this.fetchQuoteConfiguration(),this.handleReloadPersistence(),this.valueChangeDetection()}))}valueChangeDetection(){this.is_active.valueChanges.subscribe(e=>{var t;null===(t=this.rcFormGroup.get("is_active"))||void 0===t||t.patchValue(e,{emitEvent:!1})})}getID(){this.route.paramMap.subscribe(e=>{this.rc_id=e.get("id"),console.log("Form ID:",this.rc_id)})}setTheme(){return Object(w.c)(this,void 0,void 0,(function*(){const e=yield this._qbMasterService.getTheme();document.documentElement.style.setProperty("--atsprimaryColor",e),document.documentElement.style.setProperty("--atssecondaryColor",e),document.documentElement.style.setProperty("--atsfontFamily","DM Sans")}))}handleReloadPersistence(){const e=localStorage.getItem("formAction");if(this.storedFormValues=localStorage.getItem("formValues"),e){const{action:t}=JSON.parse(e);"CREATE"===t&&this.storedFormValues&&(this.rcFormGroup.patchValue(JSON.parse(this.storedFormValues)),this._toaster.showInfo("The form has been restored to its previous values upon reload.","Please click the `Clear` button to reset it.",2500))}window.addEventListener("beforeunload",()=>{if("CREATE"===this.formType){const e=this.rcFormGroup.value;Object.values(e).some(e=>null!==e&&""!==e&&!(Array.isArray(e)&&0===e.length))&&localStorage.setItem("formValues",JSON.stringify(e))}})}getFormType(){this._quoteService.formAction$.subscribe(({action:e,rcType:t,rcLabel:n,forUpdate:i})=>{this.formType=e,this.rcType=t,this.rcLabel="CREATE"===e?n+" Creation":n+" Update",this.editData=i,console.log(e,t,n,"action, rcType, rcLabel")})}initializeRcFieldConfig(){var e;return Object(w.c)(this,void 0,void 0,(function*(){yield this.getRcFieldConfig({manPower:1,nonManPower:2,license:3}[this.rcType]),this.selectedType=null===(e=this.rcTypelist.find(e=>e.is_active))||void 0===e?void 0:e.type}))}fetchQuoteConfiguration(){this._quoteService.getQuoteConfiguration([this.mandatoryConfigName,"quote_work_location_entity_mapping_enabled","quote_service_division_mapping_enabled"]).pipe(Object(x.a)(this._onDestroy)).subscribe({next:e=>this.processQuoteConfiguration(e),error:e=>this.handleQuoteConfigError(e)})}processQuoteConfiguration(e){"S"===(null==e?void 0:e.messType)&&Array.isArray(null==e?void 0:e.data)&&e.data.forEach(e=>{if(null==e?void 0:e.quote_config_name){switch(e.quote_config_name){case"quote_mandatory_fields":this.quoteMandatoryFieldList=e.quote_config_value;break;case"quote_work_location_entity_mapping_enabled":this.wEMappingEnabled=!!e.quote_config_value;break;case"quote_service_division_mapping_enabled":this.sDMappingEnabled=!!e.quote_config_value}this.mappingCalls(),console.log("QB Mapping Configs",this.quoteMandatoryFieldList,this.wEMappingEnabled,this.sDMappingEnabled)}})}mappingCalls(){this.getOrgMappingList(),this.wEMappingEnabled&&this.getWEMappingList(),this.sDMappingEnabled&&this.getSDMappingList()}handleQuoteConfigError(e){console.error(e),this._toaster.showError("Error","Error in getting Quote configuration",3e3),this.formLoading=!1}noWhitespaceValidator(e){return 0===(e.value||"").trim().length?{whitespace:!0}:null}initRCForm(){var e;const t={};if(this.formConfig.forEach(e=>{const n=[];e.is_mandatory&&n.push(d.H.required),"number"===e.field_type&&n.push(d.H.pattern(/^\d+$/)),e.has_master_data||"text"!==e.field_type||n.push(this.noWhitespaceValidator),t[e.field_key]=new d.j("",n)}),t.id=new d.j(""),this.rcFormGroup=new d.m(t),"UPDATE"===this.formType){const t=localStorage.getItem("forUpdate"),n=t?JSON.parse(t):this.editData;this.rcFormGroup.patchValue(n),this.rcFormGroup.valueChanges.subscribe(e=>{this.noChange=!(JSON.stringify(e)!==JSON.stringify(n))}),this.is_active.patchValue(null===(e=this.rcFormGroup.get("is_active"))||void 0===e?void 0:e.value)}this.rcFormGroup.get("work_location").valueChanges.pipe(Object(S.a)(100),Object(x.a)(this._onDestroy)).subscribe(e=>{if(e&&this.wEMappingList.length&&this.wEMappingEnabled){const t=this.wEMappingList.find(t=>t.work_location_id===e);this.rcFormGroup.get("entity").patchValue(t?t.entity_id:null)}else this.rcFormGroup.get("entity").patchValue(null)}),this.valueChangeSubscription.add(this.rcFormGroup.get("entity").valueChanges.pipe(Object(S.a)(100),Object(x.a)(this._onDestroy)).subscribe(e=>{this.resolveOrgMapping("entity",this.rcFormGroup.value)})),this.valueChangeSubscription.add(this.rcFormGroup.get("division").valueChanges.pipe(Object(S.a)(100),Object(x.a)(this._onDestroy)).subscribe(e=>{this.resolveOrgMapping("division",this.rcFormGroup.value)})),this.valueChangeSubscription.add(this.rcFormGroup.valueChanges.subscribe(e=>{Object.keys(e).forEach(e=>{const t=this.formConfig.find(t=>t.field_key===e),n=this.rcFormGroup.get(e);if("number"===(null==t?void 0:t.field_type)&&n){let e=n.value;("number"!=typeof e||e<0||isNaN(e))&&e>=0&&n.patchValue(0,{emitEvent:!1}),"number"!=typeof e&&e>=0&&(n.patchValue("",{emitEvent:!1}),n.patchValue(e,{emitEvent:!1}))}})})),this.formLoading=!1,console.log("Form Group",this.rcFormGroup)}setupFormFilters(){this.rcFormGroup.get("work_location").valueChanges.pipe(Object(S.a)(100),Object(x.a)(this._onDestroy)).subscribe(()=>{this.updateFilteredData("entity"),this.rcFormGroup.get("entity").reset(),this.rcFormGroup.get("division").disable(),this.rcFormGroup.get("sub_division").disable()}),this.rcFormGroup.get("entity").valueChanges.pipe(Object(S.a)(100),Object(x.a)(this._onDestroy)).subscribe(()=>{this.updateFilteredData("division"),this.rcFormGroup.get("division").reset(),this.rcFormGroup.get("sub_division").disable(),this.rcFormGroup.get("entity").value?this.rcFormGroup.get("division").enable():this.rcFormGroup.get("division").disable()}),this.rcFormGroup.get("division").valueChanges.pipe(Object(S.a)(100),Object(x.a)(this._onDestroy)).subscribe(()=>{this.updateFilteredData("sub_division"),this.rcFormGroup.get("sub_division").reset(),this.rcFormGroup.get("division").value?this.rcFormGroup.get("sub_division").enable():this.rcFormGroup.get("sub_division").disable()})}updateFilteredData(e){for(const t of this.formConfig)if(t.field_key===e)switch(e){case"entity":t.master_data=this.getFilteredEntity();break;case"division":t.master_data=this.getFilteredDivision();break;case"sub_division":t.master_data=this.getFilteredSubDivision()}}getFilteredEntity(){var e,t;const n=null===(t=null===(e=this.rcFormGroup)||void 0===e?void 0:e.get("work_location"))||void 0===t?void 0:t.value;return this.wEMappingEnabled&&n?this.EntityMasterData.filter(e=>{const t=this.wEMappingList.find(e=>e.work_location_id===n);return!!t&&t.entity_id==e.id}):this.EntityMasterData}getFilteredDivision(){var e,t;const n=null===(t=null===(e=this.rcFormGroup)||void 0===e?void 0:e.get("entity"))||void 0===t?void 0:t.value;return this.sDMappingEnabled&&n?this.divisionMasterData.filter(e=>{const t=this.sDMappingList.find(e=>e.entity_id===n);return!!t&&t.division_id==e.id}):this.divisionMasterData}getFilteredSubDivision(){var e,t;const n=null===(t=null===(e=this.rcFormGroup)||void 0===e?void 0:e.get("division"))||void 0===t?void 0:t.value;return n?this.SubDivisionMasterData.filter(e=>{const t=this.orgMappingList.find(e=>e.division_id===n);return!!t&&t.sub_division_id==e.id}):this.SubDivisionMasterData}markAllControlsTouched(){Object.keys(this.rcFormGroup.controls).forEach(e=>{var t;null===(t=this.rcFormGroup.get(e))||void 0===t||t.markAsTouched()})}saveForm(){var e,t,n,i,o,a,r;return Object(w.c)(this,void 0,void 0,(function*(){if(console.log("Form Values:",this.rcFormGroup.value),this.disabled=!0,Object.keys(this.rcFormGroup.controls).some(e=>{var t;return"is_active"!==e&&(null===(t=this.rcFormGroup.get(e))||void 0===t?void 0:t.invalid)}))return this.markAllControlsTouched(),this.disabled=!1,this._toaster.showWarning("Kindly fill all the fields in the form","");const s=null===(e=this.rcFormGroup.get("start_date"))||void 0===e?void 0:e.value,l=null===(t=this.rcFormGroup.get("end_date"))||void 0===t?void 0:t.value;if(s&&l&&new Date(s)>new Date(l))return this.disabled=!1,this._toaster.showWarning("Start Date should not be later than End Date","");null===(n=this.rcFormGroup.get("is_active"))||void 0===n||n.patchValue(this.is_active.value,{emitEvent:!1}),(null===(i=this.rcFormGroup.get("is_active"))||void 0===i?void 0:i.value)&&null!=this.rcFormGroup.get("is_active").value||null===(o=this.rcFormGroup.get("is_active"))||void 0===o||o.patchValue(!1,{emitEvent:!1});let c,d=null===(a=this.rcFormGroup.get("services_tagged"))||void 0===a?void 0:a.value;if("string"==typeof d)try{d=JSON.parse(d),null===(r=this.rcFormGroup.get("services_tagged"))||void 0===r||r.patchValue(d,{emitEvent:!1})}catch(p){return this.disabled=!1,this._toaster.showWarning("Invalid value for services tagged","")}"manPower"===this.rcType?c="CREATE"===this.formType?yield this.insertResourceManpowerCost(this.rcFormGroup.value):yield this.updateResourceManpowerCost(this.rcFormGroup.value):"nonManPower"===this.rcType?c="CREATE"===this.formType?yield this.insertResourceNonManpowerCost(this.rcFormGroup.value):yield this.updateResourceNonManpowerCost(this.rcFormGroup.value):"license"===this.rcType&&(c="CREATE"===this.formType?yield this.licenseRowInserted(this.rcFormGroup.value):yield this.licenseRowUpdated(this.rcFormGroup.value)),"Inserted"!==c&&"Updated"!==c||(localStorage.removeItem("formValues"),localStorage.removeItem("formAction"),localStorage.removeItem("openFormV"),this.disabled=!1,this.storedFormValues=null,window.dispatchEvent(new StorageEvent("storage",{key:"openFormV",newValue:null})),this.calculateDynamicContentHeight())}))}onTypeSelect(e){this.rcTypelist.forEach(t=>{t.is_active=t.type===e})}onCustomSelectValueChange(e){this.rcFormGroup.get(e.data.field).setValue(e.val)}onClick(e){var t,n;((null===(t=this.rcFormGroup.get(e))||void 0===t?void 0:t.invalid)||""===(null===(n=this.rcFormGroup.get(e))||void 0===n?void 0:n.value))&&setTimeout(()=>{var t;null===(t=this.rcFormGroup.get(e))||void 0===t||t.markAsTouched()},2e3)}getCurrencyName(e){if(!e)return"USD";const t=this.formConfig.find(e=>"currency"==e.field_key).master_data.find(t=>t.id===e);return t?t.name:"USD"}onResize(){this.adjustDynamicHeight()}onBackClick(e){"CLEAR"!==e?(localStorage.removeItem("formValues"),localStorage.removeItem("formAction"),localStorage.removeItem("openFormV"),window.dispatchEvent(new StorageEvent("storage",{key:"openFormV",newValue:null}))):(localStorage.removeItem("formValues"),this.rcFormGroup.reset(),this.storedFormValues=null,this._toaster.showSuccess("Form has been reset!","",2e3))}adjustDynamicHeight(){const e=window.innerHeight;let t=Math.max(.8*e-90,100);const n=100*window.devicePixelRatio;n>100&&(t-=.4*(n-100)),document.documentElement.style.setProperty("--dynamicHeight",t+"px")}insertResourceManpowerCost(e){return new Promise((t,n)=>{this.subs.sink=this._RmService.insertResourceManpowerCost(e).subscribe(e=>{this._toaster.showSuccess("Success","Manpower rate card created successfully.",2e3),t("Inserted")},e=>{console.log(e),n(e)})})}insertResourceNonManpowerCost(e){return new Promise((t,n)=>{this.subs.sink=this._RmService.insertResourceNonManpowerCost(e).subscribe(e=>{this._toaster.showSuccess("Success","Non-manpower rate card created successfully.",2e3),t("Inserted")},e=>{console.log(e),n(e)})})}updateResourceManpowerCost(e){return new Promise((t,n)=>{this.subs.sink=this._RmService.updateResourceManpowerCost(e).subscribe(e=>{this._toaster.showSuccess("Success","Manpower rate card updated successfully.",2e3),t("Updated")},e=>{console.log(e),n(e)})})}updateResourceNonManpowerCost(e){return new Promise((t,n)=>{this.subs.sink=this._RmService.updateResourceNonManpowerCost(e).subscribe(e=>{this._toaster.showSuccess("Success","Non-manpower rate card updated successfully.",2e3),t("Updated")},e=>{console.log(e),n(e)})})}licenseRowInserted(e){return new Promise((t,n)=>{this.subs.sink=this._quoteService.insertLicenseConfiguration(e).subscribe({next:e=>{"S"==e.messType?(this._toaster.showSuccess("Success","License created successfully.",2e3),t("Inserted")):(this._toaster.showError("Error","Failed to create license.",2e3),n("Insert failed"))},error:e=>{console.error(e),this._toaster.showError("Error","Error creating license configuration.",2e3),n("Insert error")}})})}licenseRowUpdated(e){return new Promise((t,n)=>{this.subs.sink=this._quoteService.updateLicenseConfiguration(e).subscribe({next:e=>{"S"==e.messType?(this._toaster.showSuccess("Success","License updated successfully.",2e3),t("Updated")):(this._toaster.showError("Error","Failed to update license.",2e3),n("Update failed"))},error:e=>{console.error(e),this._toaster.showError("Error","Error updating license.",2e3),n("Update error")}})})}getGlobalCRMDateFormat(){return Object(w.c)(this,void 0,void 0,(function*(){this.udrfServices.getCRMGlobalDateFormat().subscribe(e=>{if("S"===e.messType&&e.data&&e.data.length>0){const t=JSON.parse(e.data[0].config);this.dateFormat=t.format||this.dateFormat,this.dateAdapter.setLocale("en-US"),this.dateAdapter.format=e=>k(e).format(this.dateFormat)}},e=>{console.error("Error retrieving date format",e)})}))}ngOnChanges(e){e.activeReport&&!e.activeReport.firstChange&&(localStorage.removeItem("formValues"),this.storedFormValues=null,this.rcFormGroup.reset(),this.ngOnInit())}calculateDynamicContentHeight(){document.documentElement.style.setProperty("--reportDynamicHeight",window.innerHeight-140+"px"),document.documentElement.style.setProperty("--reportDynamicSubHeight",window.innerHeight-190+"px"),document.documentElement.style.setProperty("--reportDynamicListViewHeight",window.innerHeight-230+"px")}resolveOrgMapping(e,t){if("entity"===e){const e=!!t.entity;if(this.rcFormGroup.get("division").patchValue(null,{emitEvent:!1}),this.rcFormGroup.get("sub_division").patchValue(null,{emitEvent:!1}),e){this.rcFormGroup.get("division").disabled&&this.rcFormGroup.get("division").enable({emitEvent:!1});const e=this.orgMappingList.filter(e=>e.entity_id===t.entity),n=e.map(e=>e.division_id),i=e.map(e=>e.sub_division_id),o=this.divisionMasterData.filter(e=>n.includes(e.id)),a=this.SubDivisionMasterData.filter(e=>i.includes(e.id));this.updateFormConfigMasterData("division",o),this.updateFormConfigMasterData("sub_division",a),null==t.division||n.includes(t.division)||this.rcFormGroup.get("division").patchValue(null),null==t.sub_division||i.includes(t.sub_division)||this.rcFormGroup.get("sub_division").patchValue(null),this.rcFormGroup.get("sub_division").disable()}else this.rcFormGroup.get("division").disable(),this.rcFormGroup.get("sub_division").disable(),this.updateFormConfigMasterData("division",this.divisionMasterData),this.updateFormConfigMasterData("sub_division",this.SubDivisionMasterData)}if("division"===e){const e=!!t.division;if(this.rcFormGroup.get("sub_division").patchValue(null,{emitEvent:!1}),e){this.rcFormGroup.get("sub_division").disabled&&this.rcFormGroup.get("sub_division").enable({emitEvent:!1});const e=this.orgMappingList.filter(e=>e.division_id===t.division&&(null==t.entity||e.entity_id==t.entity)).map(e=>e.sub_division_id),n=this.SubDivisionMasterData.filter(t=>e.includes(t.id));this.updateFormConfigMasterData("sub_division",n),null==t.sub_division||e.includes(t.sub_division)||this.rcFormGroup.get("sub_division").patchValue(null)}else this.rcFormGroup.get("sub_division").disable(),this.updateFormConfigMasterData("sub_division",this.SubDivisionMasterData)}}updateFormConfigMasterData(e,t){const n=this.formConfig.find(t=>t.field_key===e);n&&(n.master_data=t)}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete(),this.valueChangeSubscription&&this.valueChangeSubscription.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(I["\u0275\u0275directiveInject"](V.a),I["\u0275\u0275directiveInject"](R.a),I["\u0275\u0275directiveInject"](L.a),I["\u0275\u0275directiveInject"](T.a),I["\u0275\u0275directiveInject"](M.a),I["\u0275\u0275directiveInject"](i.Location),I["\u0275\u0275directiveInject"](j.a),I["\u0275\u0275directiveInject"](C.c),I["\u0275\u0275directiveInject"](A.a))},e.\u0275cmp=I["\u0275\u0275defineComponent"]({type:e,selectors:[["app-form-page"]],hostBindings:function(e,t){1&e&&I["\u0275\u0275listener"]("resize",(function(){return t.onResize()}),!1,I["\u0275\u0275resolveWindow"])},inputs:{activeReport:"activeReport"},features:[I["\u0275\u0275ProvidersFeature"]([{provide:C.c,useClass:D.c,deps:[C.f,D.a]},{provide:C.e,useValue:{parse:{dateInput:"DD-MMM-YYYY"},display:{dateInput:"DD-MMM-YYYY",monthYearLabel:"MMM YYYY"}}}]),I["\u0275\u0275NgOnChangesFeature"]],decls:2,vars:2,consts:[["class","common-rate-card-form p-2",4,"ngIf"],["class","d-flex align-items-center justify-content-center loader",4,"ngIf"],[1,"common-rate-card-form","p-2"],[1,"header-class"],[3,"formControl"],[1,"field-config"],[4,"ngIf"],["validationTemplate",""],[1,"action-buttons"],["mat-button","","class","clear-btn",3,"click",4,"ngIf"],["mat-button","",1,"cancel-btn",3,"click"],["mat-button","",1,"save-btn",3,"disabled","matTooltip","click"],[3,"formGroup"],[1,"form-grid"],[1,"m-0"],[1,"form-section"],[4,"ngFor","ngForOf"],[1,"mt-3"],["fieldTemplate",""],[4,"ngTemplateOutlet","ngTemplateOutletContext"],[1,"form-field"],["class","required-symbol","matTooltip","This is a Mandatory Field",4,"ngIf"],["class","form-field-class",3,"masterData","placeholder","selectedValue","displayClose","data","onValueChange",4,"ngIf"],["class","form-field-class",3,"type","placeholder","masterData","selectedValues","displayClose","data","onValueChange",4,"ngIf"],["class","form-field-class2","appearance","none","style","width: 100%; height: 40px; border: 1px solid #d2d2d2;  border-radius: 8px !important;",4,"ngIf"],["class","form-field-class1","appearance","none","appearance","outline","style","width: 100%; height: 40px; border: 1px solid #d2d2d2; border-radius: 8px !important;",4,"ngIf"],["matTooltip","This is a Mandatory Field",1,"required-symbol"],[1,"form-field-class",3,"masterData","placeholder","selectedValue","displayClose","data","onValueChange"],[1,"form-field-class",3,"type","placeholder","masterData","selectedValues","displayClose","data","onValueChange"],["appearance","none",1,"form-field-class2",2,"width","100%","height","40px","border","1px solid #d2d2d2","border-radius","8px !important"],["matInput","","type","text",3,"formControlName","placeholder",4,"ngIf"],["matInput","","type","number",3,"formControlName","placeholder",4,"ngIf"],["class","mr-2","matSuffix","",4,"ngIf"],["matInput","","type","text",3,"formControlName","placeholder"],["matInput","","type","number",3,"formControlName","placeholder"],["matSuffix","",1,"mr-2"],["appearance","none","appearance","outline",1,"form-field-class1",2,"width","100%","height","40px","border","1px solid #d2d2d2","border-radius","8px !important"],[1,"calendar-style"],["matInput","","readonly","",3,"matDatepicker","formControlName","placeholder","min","max","focus"],["matSuffix","",3,"for"],["picker",""],["class","required-template",4,"ngIf"],[1,"required-template"],["mat-button","",1,"clear-btn",3,"click"],[1,"d-flex","align-items-center","justify-content-center","loader"],["src","https://assets.kebs.app/KEBS_MAIN_GIF.gif","alt","Loading...","width","70","height","70"]],template:function(e,t){1&e&&(I["\u0275\u0275template"](0,he,16,9,"div",0),I["\u0275\u0275template"](1,fe,2,0,"div",1)),2&e&&(I["\u0275\u0275property"]("ngIf",!t.formLoading),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",t.formLoading))},directives:[i.NgIf,_.a,d.v,d.k,o.a,r.a,d.J,d.w,d.n,i.NgForOf,i.NgTemplateOutlet,q.a,G.a,s.c,c.b,d.e,d.l,d.A,s.i,p.g,p.i,p.f],styles:[".common-rate-card-form[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]{display:flex;align-items:center;border:1px solid #ccc;border-radius:8px;cursor:pointer;transition:all .3s;text-align:center}.common-rate-card-form[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:19px}.common-rate-card-form[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-family:Roboto;font-weight:500;font-size:13px}.common-rate-card-form[_ngcontent-%COMP%]   .header-class[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;font-family:DM Sans;font-weight:500;font-size:16px;line-height:20.83px;letter-spacing:0;color:var(--atsprimaryColor);padding:1rem 1rem 0 3rem}.common-rate-card-form[_ngcontent-%COMP%]   .header-class[_ngcontent-%COMP%]   .mat-slide-toggle[_ngcontent-%COMP%]{transform:scale(.8)}.common-rate-card-form[_ngcontent-%COMP%]   .header-class[_ngcontent-%COMP%]     .mat-slide-toggle .mat-slide-toggle-bar{width:48px;height:30px;background-color:#dadce2;border-radius:50px}.common-rate-card-form[_ngcontent-%COMP%]   .header-class[_ngcontent-%COMP%]     .mat-slide-toggle .mat-slide-toggle-thumb{background-color:#fff;width:23px;height:23px;margin-left:5px;margin-top:6px}.common-rate-card-form[_ngcontent-%COMP%]   .header-class[_ngcontent-%COMP%]     .mat-slide-toggle.mat-checked .mat-slide-toggle-bar{background-color:var(--atsprimaryColor);width:48px;height:30px;border-radius:50px}.common-rate-card-form[_ngcontent-%COMP%]   .header-class[_ngcontent-%COMP%]     .mat-slide-toggle.mat-checked .mat-slide-toggle-thumb{background-color:#fff;width:23px;height:23px;margin-left:5px;margin-top:6px}.common-rate-card-form[_ngcontent-%COMP%]   .rc-type-container[_ngcontent-%COMP%]   .radio-group[_ngcontent-%COMP%]{display:flex;align-items:center;width:100%;justify-content:space-around}.common-rate-card-form[_ngcontent-%COMP%]   .rc-type-box[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-around;flex-direction:column;padding:1rem;border:1px solid #ccc;border-radius:8px;cursor:pointer;transition:all .3s;text-align:center;width:20rem;height:5rem}.common-rate-card-form[_ngcontent-%COMP%]   .rc-type-box[_ngcontent-%COMP%]   .radio-group[_ngcontent-%COMP%]{display:flex;gap:1rem;flex-wrap:wrap;width:100%;justify-content:space-around}.common-rate-card-form[_ngcontent-%COMP%]   .rc-type-box[_ngcontent-%COMP%]   .rc-type-box[_ngcontent-%COMP%]{border:1px solid #ccc;border-radius:8px;padding:1rem;transition:all .3s ease;cursor:pointer}.common-rate-card-form[_ngcontent-%COMP%]   .rc-type-box[_ngcontent-%COMP%]   .radio-button[_ngcontent-%COMP%]{display:flex;align-items:center;width:100%}.common-rate-card-form[_ngcontent-%COMP%]   .rc-type-box[_ngcontent-%COMP%]   .radio-button.mat-radio-checked[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{font-weight:700;color:#007bff}.common-rate-card-form[_ngcontent-%COMP%]   .rc-type-box[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{font-weight:700;display:flex;flex-direction:column;align-items:start}.common-rate-card-form[_ngcontent-%COMP%]   .field-config[_ngcontent-%COMP%]::-webkit-scrollbar{width:6px!important}.common-rate-card-form[_ngcontent-%COMP%]   .field-config[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{min-height:40px!important}.common-rate-card-form[_ngcontent-%COMP%]   .field-config[_ngcontent-%COMP%]{padding:0 6rem;margin:1rem 0;overflow-y:scroll;height:var(--dynamicHeight);overflow-x:hidden}.common-rate-card-form[_ngcontent-%COMP%]   .field-config[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-family:DM Sans;font-weight:500;font-size:14px;line-height:20px;letter-spacing:2%;color:#5f6c81;margin:0}.common-rate-card-form[_ngcontent-%COMP%]   .field-config[_ngcontent-%COMP%]   mat-label[_ngcontent-%COMP%]{font-family:DM Sans;font-weight:400;font-size:14px;letter-spacing:2%;color:#b9c0ca}.common-rate-card-form[_ngcontent-%COMP%]   .field-config[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]{font-size:13px;font-family:DM Sans;width:100%;height:2.5rem}.common-rate-card-form[_ngcontent-%COMP%]   .field-config[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]   .calendar-style[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;height:17px!important;line-height:11px!important}.common-rate-card-form[_ngcontent-%COMP%]   .field-config[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-input-element:disabled{color:#dadce2!important}.common-rate-card-form[_ngcontent-%COMP%]   .field-config[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-input-element{color:#515965!important}.common-rate-card-form[_ngcontent-%COMP%]   .field-config[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-form-field{font-size:12px!important;font-family:DM Sans}.common-rate-card-form[_ngcontent-%COMP%]   .field-config[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-form-field-wrapper, .common-rate-card-form[_ngcontent-%COMP%]   .field-config[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-wrapper{padding-bottom:0!important}.common-rate-card-form[_ngcontent-%COMP%]   .field-config[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]   .mat-select-trigger[_ngcontent-%COMP%]{height:2.5rem!important;display:flex;align-items:center}.common-rate-card-form[_ngcontent-%COMP%]   .field-config[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]   .mat-form-field-appearance-outline[_ngcontent-%COMP%]   .mat-form-field-outline[_ngcontent-%COMP%]{height:2.5rem!important}.common-rate-card-form[_ngcontent-%COMP%]   .field-config[_ngcontent-%COMP%]   .form-field-class1[_ngcontent-%COMP%]{font-size:13px;font-family:DM Sans;width:100%;height:2.5rem}.common-rate-card-form[_ngcontent-%COMP%]   .field-config[_ngcontent-%COMP%]   .form-field-class1[_ngcontent-%COMP%]   .calendar-style[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;height:17px!important;line-height:20px!important}.common-rate-card-form[_ngcontent-%COMP%]   .field-config[_ngcontent-%COMP%]   .form-field-class1[_ngcontent-%COMP%]     .mat-input-element:disabled{color:#dadce2!important}.common-rate-card-form[_ngcontent-%COMP%]   .field-config[_ngcontent-%COMP%]   .form-field-class1[_ngcontent-%COMP%]     .mat-input-element{color:#515965!important}.common-rate-card-form[_ngcontent-%COMP%]   .field-config[_ngcontent-%COMP%]   .form-field-class1[_ngcontent-%COMP%]     .mat-form-field{font-size:12px!important;font-family:DM Sans}.common-rate-card-form[_ngcontent-%COMP%]   .field-config[_ngcontent-%COMP%]   .form-field-class1[_ngcontent-%COMP%]     .mat-form-field-wrapper, .common-rate-card-form[_ngcontent-%COMP%]   .field-config[_ngcontent-%COMP%]   .form-field-class1[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-wrapper{padding-bottom:0!important}.common-rate-card-form[_ngcontent-%COMP%]   .field-config[_ngcontent-%COMP%]   .form-field-class1[_ngcontent-%COMP%]   .mat-select-trigger[_ngcontent-%COMP%]{height:2.5rem!important;display:flex;align-items:center}.common-rate-card-form[_ngcontent-%COMP%]   .field-config[_ngcontent-%COMP%]   .form-field-class1[_ngcontent-%COMP%]   .mat-form-field-appearance-outline[_ngcontent-%COMP%]   .mat-form-field-outline[_ngcontent-%COMP%]{height:2.5rem!important;display:none!important}.common-rate-card-form[_ngcontent-%COMP%]   .field-config[_ngcontent-%COMP%]   .form-field-class2[_ngcontent-%COMP%]{font-size:13px;font-family:DM Sans;width:100%;height:2.5rem}.common-rate-card-form[_ngcontent-%COMP%]   .field-config[_ngcontent-%COMP%]   .form-field-class2[_ngcontent-%COMP%]   .calendar-style[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;height:17px!important;line-height:20px!important}.common-rate-card-form[_ngcontent-%COMP%]   .field-config[_ngcontent-%COMP%]   .form-field-class2[_ngcontent-%COMP%]     .mat-input-element:disabled{color:#dadce2!important}.common-rate-card-form[_ngcontent-%COMP%]   .field-config[_ngcontent-%COMP%]   .form-field-class2[_ngcontent-%COMP%]     .mat-input-element{color:#515965!important;margin:-4px 1px 4px 8px;width:94%}.common-rate-card-form[_ngcontent-%COMP%]   .field-config[_ngcontent-%COMP%]   .form-field-class2[_ngcontent-%COMP%]     .mat-form-field{font-size:12px!important;font-family:DM Sans}.common-rate-card-form[_ngcontent-%COMP%]   .field-config[_ngcontent-%COMP%]   .form-field-class2[_ngcontent-%COMP%]     .mat-form-field-wrapper, .common-rate-card-form[_ngcontent-%COMP%]   .field-config[_ngcontent-%COMP%]   .form-field-class2[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-wrapper{padding-bottom:0!important}.common-rate-card-form[_ngcontent-%COMP%]   .field-config[_ngcontent-%COMP%]   .form-field-class2[_ngcontent-%COMP%]   .mat-select-trigger[_ngcontent-%COMP%]{height:2.5rem!important;display:flex;align-items:center}.common-rate-card-form[_ngcontent-%COMP%]   .field-config[_ngcontent-%COMP%]   .form-field-class2[_ngcontent-%COMP%]   .mat-form-field-appearance-outline[_ngcontent-%COMP%]   .mat-form-field-outline[_ngcontent-%COMP%]{height:2.5rem!important;display:none!important}.common-rate-card-form[_ngcontent-%COMP%]   .field-config[_ngcontent-%COMP%]   .mat-slide-toggle[_ngcontent-%COMP%]{transform:scale(.8)}.common-rate-card-form[_ngcontent-%COMP%]   .field-config[_ngcontent-%COMP%]     .mat-slide-toggle .mat-slide-toggle-bar{width:48px;height:30px;background-color:#dadce2;border-radius:50px}.common-rate-card-form[_ngcontent-%COMP%]   .field-config[_ngcontent-%COMP%]     .mat-slide-toggle .mat-slide-toggle-thumb{background-color:#fff;width:23px;height:23px;margin-left:5px;margin-top:6px}.common-rate-card-form[_ngcontent-%COMP%]   .field-config[_ngcontent-%COMP%]     .mat-slide-toggle.mat-checked .mat-slide-toggle-bar{background-color:var(--atsprimaryColor);width:48px;height:30px;border-radius:50px}.common-rate-card-form[_ngcontent-%COMP%]   .field-config[_ngcontent-%COMP%]     .mat-slide-toggle.mat-checked .mat-slide-toggle-thumb{background-color:#fff;width:23px;height:23px;margin-left:5px;margin-top:6px}.common-rate-card-form[_ngcontent-%COMP%]   .field-config[_ngcontent-%COMP%]   .required-symbol[_ngcontent-%COMP%]{cursor:default;color:#cf0001}.common-rate-card-form[_ngcontent-%COMP%]   .field-config[_ngcontent-%COMP%]   .form-grid[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:16px;padding:16px;border-radius:8px}.common-rate-card-form[_ngcontent-%COMP%]   .field-config[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:16px;align-items:center}.common-rate-card-form[_ngcontent-%COMP%]   .field-config[_ngcontent-%COMP%]   .form-field[_ngcontent-%COMP%]{display:flex;flex-direction:column;justify-content:center}.common-rate-card-form[_ngcontent-%COMP%]   .field-config[_ngcontent-%COMP%]   .toggle-container[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:flex-start;gap:.75rem}.common-rate-card-form[_ngcontent-%COMP%]   .field-config[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-family:DM Sans;font-weight:500;font-size:15px;line-height:18.23px;letter-spacing:0;color:#111434;margin:0 0 1%}.common-rate-card-form[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]{width:100%;height:2.5rem}.common-rate-card-form[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;padding:0 1rem}.common-rate-card-form[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{width:3rem;margin-left:auto;margin-right:1rem}.common-rate-card-form[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%], .common-rate-card-form[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .clear-btn[_ngcontent-%COMP%]{border:1px solid #45546e;color:#45546e;font-family:DM Sans;font-weight:500!important;font-size:14px;line-height:16px;letter-spacing:-2%;height:2rem;display:flex;align-items:center;justify-content:center;border-radius:4px}.common-rate-card-form[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .clear-btn[_ngcontent-%COMP%]{margin-left:2rem;margin-right:1.5rem}.common-rate-card-form[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%]{height:2rem;width:3rem;font-family:DM Sans;font-weight:500!important;font-size:14px;line-height:16px;letter-spacing:-2%;color:#fff;background-color:var(--atsprimaryColor)!important;display:flex;align-items:center;justify-content:center;border-radius:4px}.common-rate-card-form[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%]:disabled{background-color:#e8e9ee!important;color:#b9c0ca}.common-rate-card-form[_ngcontent-%COMP%]   .required-template[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);color:#cf0001;font-size:11px;padding-left:1%;cursor:default}.common-rate-card-form[_ngcontent-%COMP%]     app-multi-select-chip .main-container{border-radius:8px}.common-rate-card-form[_ngcontent-%COMP%]     app-multi-select-chip .chip-2{padding:3px 7px;border-radius:3px;gap:4px;min-width:10%;background:#e8e9ee!important;max-width:100%;border:none!important}.common-rate-card-form[_ngcontent-%COMP%]     app-single-select-chip .main-container{border-radius:8px}.common-rate-card-form[_ngcontent-%COMP%]     app-single-select-chip .main-container .search-text{font-size:13px!important}.common-rate-card-form[_ngcontent-%COMP%]     .form-field-class.mat-form-field{border-radius:8px!important;height:40px}.common-rate-card-form[_ngcontent-%COMP%]     .form-field-class .mat-form-field-outline{height:40px!important}.common-rate-card-form[_ngcontent-%COMP%]     .form-field-class .mat-input-element{color:#515965!important}.common-rate-card-form[_ngcontent-%COMP%]     .form-field-class .mat-form-field-wrapper{height:40px!important;margin:0!important}.common-rate-card-form[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-outline-thick{color:#dcdcdc!important}.common-rate-card-form[_ngcontent-%COMP%]     .mat-calendar-body-selected{background-color:var(--atsprimaryColor)!important;color:#fff!important}.common-rate-card-form[_ngcontent-%COMP%]     .mat-calendar-body-today:not(.mat-calendar-body-selected){border-color:var(--atsprimaryColor)!important}.common-rate-card-form[_ngcontent-%COMP%]     .mat-calendar-body-active{background-color:var(--atsprimaryColor)!important;color:#fff!important}.common-rate-card-form[_ngcontent-%COMP%]     .mat-calendar-body-cell:hover{background-color:#e3f2fd}.common-rate-card-form[_ngcontent-%COMP%]     .mat-calendar-period-button.mat-calendar-body-selected{background-color:var(--atsprimaryColor)!important;color:#fff!important}  .form-field-class.mat-form-field-appearance-outline.mat-focused .mat-form-field-outline{color:#dcdcdc!important;stroke:#dcdcdc!important;border-color:#dcdcdc!important}  .form-field-class.mat-form-field-appearance-outline .mat-form-field-outline{stroke:#d2d2d2}  .mat-calendar-body-selected{background-color:var(--atsprimaryColor)!important;color:#fff!important}.mat-datepicker-content[_ngcontent-%COMP%]   .mat-calendar-next-button[_ngcontent-%COMP%], .mat-datepicker-content[_ngcontent-%COMP%]   .mat-calendar-previous-button[_ngcontent-%COMP%],   .mat-datepicker-toggle{color:var(--atsprimaryColor)!important;box-shadow:none;height:50px!important}  .mat-calendar-body-today:not(.mat-calendar-body-selected){border-color:var(--atsprimaryColor)!important}  .form-field-class1 .mat-form-field-outline{display:none!important}.loader[_ngcontent-%COMP%]{height:calc(100vh - 111px)}"]}),e})(),ve=(()=>{class e{constructor(){}ngOnInit(){}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=I["\u0275\u0275defineComponent"]({type:e,selectors:[["app-upload-page"]],decls:2,vars:0,template:function(e,t){1&e&&(I["\u0275\u0275elementStart"](0,"p"),I["\u0275\u0275text"](1,"upload-page works!"),I["\u0275\u0275elementEnd"]())},styles:[""]}),e})();var be=n("flaP"),_e=n("PaQD"),ye=n("4qBo");function Oe(e,t){if(1&e){const e=I["\u0275\u0275getCurrentView"]();I["\u0275\u0275elementStart"](0,"button",19),I["\u0275\u0275listener"]("click",(function(){return I["\u0275\u0275restoreView"](e),I["\u0275\u0275nextContext"]().openForm("CREATE")})),I["\u0275\u0275elementStart"](1,"div",20),I["\u0275\u0275elementStart"](2,"mat-icon"),I["\u0275\u0275text"](3,"add"),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](4,"div"),I["\u0275\u0275text"](5),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"]()}if(2&e){const e=I["\u0275\u0275nextContext"]();I["\u0275\u0275advance"](5),I["\u0275\u0275textInterpolate"](e.rcLabel)}}function Me(e,t){if(1&e&&(I["\u0275\u0275elementStart"](0,"div"),I["\u0275\u0275text"](1),I["\u0275\u0275elementEnd"]()),2&e){const e=I["\u0275\u0275nextContext"]().$implicit;I["\u0275\u0275advance"](1),I["\u0275\u0275textInterpolate1"](" ",e.label," ")}}const we=function(e){return{"selected-toggle":e}};function xe(e,t){if(1&e&&(I["\u0275\u0275elementStart"](0,"mat-button-toggle",21),I["\u0275\u0275template"](1,Me,2,1,"div",10),I["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=I["\u0275\u0275nextContext"]();I["\u0275\u0275property"]("value",e.value)("disabled",e.is_disabled)("ngClass",I["\u0275\u0275pureFunction1"](4,we,n.activeReport===e.value)),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",e.is_active)}}function Se(e,t){1&e&&(I["\u0275\u0275elementStart"](0,"mat-icon"),I["\u0275\u0275text"](1,"settings"),I["\u0275\u0275elementEnd"]())}function Pe(e,t){1&e&&I["\u0275\u0275element"](0,"mat-progress-spinner",22),2&e&&I["\u0275\u0275property"]("diameter",20)}function Ee(e,t){if(1&e){const e=I["\u0275\u0275getCurrentView"]();I["\u0275\u0275elementStart"](0,"button",23),I["\u0275\u0275listener"]("click",(function(){I["\u0275\u0275restoreView"](e);const t=I["\u0275\u0275nextContext"](),n=I["\u0275\u0275reference"](25);return t.openMandatoryFieldsDialog(n)})),I["\u0275\u0275elementStart"](1,"div",24),I["\u0275\u0275elementStart"](2,"mat-icon"),I["\u0275\u0275text"](3," display_settings "),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](4,"div"),I["\u0275\u0275text"](5," Field Configuration "),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"]()}}function Fe(e,t){if(1&e){const e=I["\u0275\u0275getCurrentView"]();I["\u0275\u0275elementStart"](0,"button",23),I["\u0275\u0275listener"]("click",(function(){return I["\u0275\u0275restoreView"](e),I["\u0275\u0275nextContext"]().openDataConfigDialog()})),I["\u0275\u0275elementStart"](1,"div",24),I["\u0275\u0275elementStart"](2,"mat-icon"),I["\u0275\u0275text"](3," data_thresholding "),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](4,"div"),I["\u0275\u0275text"](5," Data Configuration "),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"]()}}function De(e,t){1&e&&I["\u0275\u0275element"](0,"app-details-page",25),2&e&&I["\u0275\u0275property"]("applicationId",908)("reportId",20040)}function ke(e,t){1&e&&I["\u0275\u0275element"](0,"app-details-page",25),2&e&&I["\u0275\u0275property"]("applicationId",908)("reportId",20041)}function Ie(e,t){1&e&&I["\u0275\u0275element"](0,"app-details-page",25),2&e&&I["\u0275\u0275property"]("applicationId",908)("reportId",20042)}function Ve(e,t){if(1&e&&I["\u0275\u0275element"](0,"app-form-page",26),2&e){const e=I["\u0275\u0275nextContext"]();I["\u0275\u0275property"]("activeReport",e.activeReport)}}const Re=function(e){return{"btn-toggle-selected":e}};function Le(e,t){if(1&e){const e=I["\u0275\u0275getCurrentView"]();I["\u0275\u0275elementStart"](0,"mat-button-toggle",46),I["\u0275\u0275listener"]("change",(function(){I["\u0275\u0275restoreView"](e);const t=I["\u0275\u0275nextContext"]().$implicit;return I["\u0275\u0275nextContext"](2).selectToggle(t.type)})),I["\u0275\u0275text"](1),I["\u0275\u0275elementEnd"]()}if(2&e){const e=I["\u0275\u0275nextContext"]().$implicit,t=I["\u0275\u0275nextContext"](2);I["\u0275\u0275property"]("value",e.type)("ngClass",I["\u0275\u0275pureFunction1"](3,Re,t.selectedToggle===e.type)),I["\u0275\u0275advance"](1),I["\u0275\u0275textInterpolate1"](" ",e.label," ")}}function Te(e,t){if(1&e&&(I["\u0275\u0275elementContainerStart"](0),I["\u0275\u0275template"](1,Le,2,5,"mat-button-toggle",45),I["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",e.is_active)}}function je(e,t){1&e&&(I["\u0275\u0275elementStart"](0,"div",47),I["\u0275\u0275text"](1," Mandatory in Quote "),I["\u0275\u0275elementEnd"]())}function Ae(e,t){if(1&e&&I["\u0275\u0275element"](0,"mat-slide-toggle",52),2&e){const e=I["\u0275\u0275nextContext"](3).$implicit;I["\u0275\u0275property"]("formControl",e.isQuoteMandatory)}}function qe(e,t){1&e&&I["\u0275\u0275text"](0,"-")}function Ge(e,t){if(1&e&&(I["\u0275\u0275elementStart"](0,"div",39),I["\u0275\u0275template"](1,Ae,1,1,"mat-slide-toggle",54),I["\u0275\u0275template"](2,qe,1,0,"ng-template",null,55,I["\u0275\u0275templateRefExtractor"]),I["\u0275\u0275elementEnd"]()),2&e){const e=I["\u0275\u0275reference"](3),t=I["\u0275\u0275nextContext"](2).$implicit;I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",t.rateFlowEnabled&&t.isMandatory.value)("ngIfElse",e)}}function Ne(e,t){if(1&e&&(I["\u0275\u0275elementStart"](0,"div",50),I["\u0275\u0275elementStart"](1,"div",51),I["\u0275\u0275text"](2),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](3,"div",39),I["\u0275\u0275element"](4,"mat-slide-toggle",52),I["\u0275\u0275elementEnd"](),I["\u0275\u0275template"](5,Ge,4,2,"div",53),I["\u0275\u0275elementEnd"]()),2&e){const e=I["\u0275\u0275nextContext"]().$implicit,t=I["\u0275\u0275nextContext"](3);I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngClass",t.isQuoteEnabled?"col-6":"col-9"),I["\u0275\u0275advance"](1),I["\u0275\u0275textInterpolate1"](" ",e.fieldLabel," "),I["\u0275\u0275advance"](2),I["\u0275\u0275property"]("formControl",e.isMandatory),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",t.isQuoteEnabled)}}function Je(e,t){if(1&e&&(I["\u0275\u0275elementContainerStart"](0),I["\u0275\u0275template"](1,Ne,6,4,"div",49),I["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf","id"!=e.fieldKey)}}function Be(e,t){if(1&e&&(I["\u0275\u0275elementStart"](0,"div",48),I["\u0275\u0275template"](1,Je,2,1,"ng-container",35),I["\u0275\u0275elementEnd"]()),2&e){const e=I["\u0275\u0275nextContext"](2);I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngForOf",e.manpowerFieldsList)}}function He(e,t){if(1&e&&(I["\u0275\u0275elementStart"](0,"div",50),I["\u0275\u0275elementStart"](1,"div",51),I["\u0275\u0275text"](2),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](3,"div",39),I["\u0275\u0275element"](4,"mat-slide-toggle",52),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"]()),2&e){const e=I["\u0275\u0275nextContext"]().$implicit;I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngClass","col-9"),I["\u0275\u0275advance"](1),I["\u0275\u0275textInterpolate1"](" ",e.fieldLabel," "),I["\u0275\u0275advance"](2),I["\u0275\u0275property"]("formControl",e.isMandatory)}}function ze(e,t){if(1&e&&(I["\u0275\u0275elementContainerStart"](0),I["\u0275\u0275template"](1,He,5,3,"div",49),I["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf","id"!=e.fieldKey)}}function Qe(e,t){if(1&e&&(I["\u0275\u0275elementStart"](0,"div",48),I["\u0275\u0275template"](1,ze,2,1,"ng-container",35),I["\u0275\u0275elementEnd"]()),2&e){const e=I["\u0275\u0275nextContext"](2);I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngForOf",e.nonmanpowerFieldsList)}}function Ue(e,t){if(1&e&&(I["\u0275\u0275elementStart"](0,"div",50),I["\u0275\u0275elementStart"](1,"div",51),I["\u0275\u0275text"](2),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](3,"div",39),I["\u0275\u0275element"](4,"mat-slide-toggle",52),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"]()),2&e){const e=I["\u0275\u0275nextContext"]().$implicit;I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngClass","col-9"),I["\u0275\u0275advance"](1),I["\u0275\u0275textInterpolate1"](" ",e.fieldLabel," "),I["\u0275\u0275advance"](2),I["\u0275\u0275property"]("formControl",e.isMandatory)}}function We(e,t){if(1&e&&(I["\u0275\u0275elementContainerStart"](0),I["\u0275\u0275template"](1,Ue,5,3,"div",49),I["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf","id"!=e.fieldKey)}}function $e(e,t){if(1&e&&(I["\u0275\u0275elementStart"](0,"div",48),I["\u0275\u0275template"](1,We,2,1,"ng-container",35),I["\u0275\u0275elementEnd"]()),2&e){const e=I["\u0275\u0275nextContext"](2);I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngForOf",e.licenseFieldsList)}}function Ke(e,t){if(1&e){const e=I["\u0275\u0275getCurrentView"]();I["\u0275\u0275elementStart"](0,"div",27),I["\u0275\u0275elementStart"](1,"div",28),I["\u0275\u0275elementStart"](2,"div",29),I["\u0275\u0275text"](3," Rate Card Field Configuration "),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](4,"div",30),I["\u0275\u0275elementStart"](5,"button",31),I["\u0275\u0275listener"]("click",(function(){return I["\u0275\u0275restoreView"](e),I["\u0275\u0275nextContext"]().closeMandatoryFieldDialog()})),I["\u0275\u0275elementStart"](6,"mat-icon",32),I["\u0275\u0275text"](7,"close"),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](8,"div",33),I["\u0275\u0275elementStart"](9,"mat-button-toggle-group",34),I["\u0275\u0275template"](10,Te,2,1,"ng-container",35),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](11,"div",36),I["\u0275\u0275elementStart"](12,"div",37),I["\u0275\u0275elementStart"](13,"div",38),I["\u0275\u0275text"](14," Field Name "),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](15,"div",39),I["\u0275\u0275text"](16," Mandatory "),I["\u0275\u0275elementEnd"](),I["\u0275\u0275template"](17,je,2,0,"div",40),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275template"](18,Be,2,1,"div",41),I["\u0275\u0275template"](19,Qe,2,1,"div",41),I["\u0275\u0275template"](20,$e,2,1,"div",41),I["\u0275\u0275elementStart"](21,"div",42),I["\u0275\u0275elementStart"](22,"button",43),I["\u0275\u0275listener"]("click",(function(){return I["\u0275\u0275restoreView"](e),I["\u0275\u0275nextContext"]().closeMandatoryFieldDialog()})),I["\u0275\u0275text"](23," Cancel "),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](24,"button",44),I["\u0275\u0275listener"]("click",(function(){return I["\u0275\u0275restoreView"](e),I["\u0275\u0275nextContext"]().saveMandatoryFields()})),I["\u0275\u0275text"](25," Save "),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"]()}if(2&e){const e=I["\u0275\u0275nextContext"]();I["\u0275\u0275advance"](9),I["\u0275\u0275property"]("value",e.selectedToggle),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngForOf",e.rcTypelist),I["\u0275\u0275advance"](3),I["\u0275\u0275property"]("ngClass",e.isQuoteEnabled&&1==e.selectedToggle?"col-6":"col-9"),I["\u0275\u0275advance"](4),I["\u0275\u0275property"]("ngIf",e.isQuoteEnabled&&1==e.selectedToggle),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",1==e.selectedToggle),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",2==e.selectedToggle),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",3==e.selectedToggle)}}const Xe=[{path:"",redirectTo:"list",pathMatch:"full"},{path:"upload",component:ve},{path:"list",component:(()=>{class e{constructor(e,t,n,i,o,a,r,s,l,c){var p;this._dialog=e,this._quoteService=t,this._toaster=n,this._qbMasterService=i,this._roles=o,this._router=a,this.route=r,this.location=s,this.cdr=l,this._rs=c,this._onDestroy=new P.b,this.subs=new F.a,this.isQuoteEnabled=!1,this.isRateCardUploadEnabled=!1,this.isRateFieldConfigEnabled=!1,this.isRateDataConfigEnabled=!1,this.wEMappingEnabled=!1,this.sDMappingEnabled=!1,this.openFormV=!1,this.mandatoryConfigName="quote_mandatory_fields",this.reports=[{label:"Man Power",value:"manPower",is_active:!0,is_disabled:!1},{label:"Non Man Power",value:"nonManPower",is_active:!0,is_disabled:!1},{label:"License",value:"license",is_active:!0,is_disabled:!1}],this.rcTypelist=[{label:"Manpower",type:1,is_active:!0},{label:"Non-Manpower",type:2,is_active:!0},{label:"License",type:3,is_active:!0}],this.rcLabel=(null===(p=this.reports.find(e=>e.is_active))||void 0===p?void 0:p.label)||"manPower",this.isLoadingConfig=!1,this.handleStorageChange=e=>{"openFormV"===e.key&&null===e.newValue&&(this.openFormV=!1,this.cdr.detectChanges())},this.resolveMandatoryFields=()=>{for(const e of this.manpowerFieldsList)e.isMandatory.patchValue(this.rcMandatoryFieldList.includes(e.fieldKey)),e.isQuoteMandatory.patchValue(!!this.isQuoteEnabled&&this.quoteMandatoryFieldList.includes(e.fieldKey));for(const e of this.nonmanpowerFieldsList)e.isMandatory.patchValue(this.nmpMandatoryFieldList.includes(e.fieldKey));for(const e of this.licenseFieldsList)e.isMandatory.patchValue(this.licMandatoryFieldList.includes(e.fieldKey))},this.closeMandatoryFieldDialog=()=>{var e;null===(e=this.mandatoryFieldDialogRef)||void 0===e||e.close(),this.resolveMandatoryFields()},this.saveMandatoryFields=()=>{let e=[],t=[];const n=1==this.selectedToggle?this.manpowerFieldsList:2==this.selectedToggle?this.nonmanpowerFieldsList:this.licenseFieldsList;for(const i of n)t.push({id:i.id,fieldKey:i.fieldKey,isMandatory:i.isMandatory.value}),this.isQuoteEnabled&&i.isMandatory.value&&i.rateFlowEnabled&&i.isQuoteMandatory.value&&e.push(i.fieldKey);this.isQuoteEnabled&&1==this.selectedToggle?this._quoteService.updateQuoteConfiguration([{config_name:this.mandatoryConfigName,config_value:e}]).pipe(Object(x.a)(this._onDestroy)).subscribe(n=>{"S"==n.messType?(this.quoteMandatoryFieldList=e,this.updateRcFieldConfiguration(t)):this._toaster.showError("Error","Error in updating Quote configuration",3e3)},e=>{console.log(e),this._toaster.showError("Error","Error in updating Quote configuration",3e3)}):this.updateRcFieldConfiguration(t)},this.updateRcFieldConfig=(e=[],t=1)=>{if(e.length){let n=[],i=[];for(const t of e){const e={id:t.id,fieldKey:t.field_key,fieldLabel:t.field_label,isMandatory:new d.j(t.is_mandatory),rateFlowEnabled:t.rate_flow_to_quote_enabled,isQuoteMandatory:new d.j(this.quoteMandatoryFieldList.includes(t.field_key))};t.is_mandatory&&i.push(t.field_key),n.push(e)}1==t?(this.manpowerFieldsList=n,this.rcMandatoryFieldList=i):2==t?(this.nonmanpowerFieldsList=n,this.nmpMandatoryFieldList=i):3==t&&(this.licenseFieldsList=n,this.licMandatoryFieldList=i)}},this.updateRcFieldConfiguration=(e=[])=>{this._quoteService.updateRcFieldConfig(e).pipe(Object(x.a)(this._onDestroy)).subscribe(t=>{var n;if("S"==t.messType){const t=e.map(e=>e.isMandatory?e.fieldKey:null).filter(e=>e);1==this.selectedToggle?this.rcMandatoryFieldList=t:2==this.selectedToggle?this.nmpMandatoryFieldList=t:3==this.selectedToggle&&(this.licMandatoryFieldList=t),this._toaster.showSuccess("Updated","Field Configuration updated Successfully !",2e3),null===(n=this.mandatoryFieldDialogRef)||void 0===n||n.close(),this.openFormV&&(this.openFormV=!1,setTimeout(()=>{this.openFormV=!0},5))}else this._toaster.showError("Error","Error in updating Field configuration",3e3)},e=>{console.log(e),this._toaster.showError("Error","Error in updating Field configuration",3e3)})},this.getRcFieldConfig=(e=1)=>Object(w.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>{this.subs.sink=this._quoteService.getRcFieldConfiguration(e).subscribe(n=>{let i=[];n.data.forEach(e=>{i.push({id:e.id,fieldKey:e.field_key,fieldLabel:e.field_label,fieldPosition:e.field_position,isMandatory:new d.j(e.is_mandatory),isQuoteMandatory:new d.j(e.is_mandatory),rateFlowEnabled:e.rate_flow_to_quote_enabled})}),1===e?this.manpowerFieldsList=i:2===e?this.nonmanpowerFieldsList=i:3===e&&(this.licenseFieldsList=i),t(!0)},t=>{console.error(`Error fetching config for rcType ${e}:`,t),n(t)})})}))}ngOnInit(){var e,t,n;return Object(w.c)(this,void 0,void 0,(function*(){this._qbMasterService.quoteEnabled.pipe(Object(x.a)(this._onDestroy)).subscribe(e=>{this.isQuoteEnabled=e.quote_enabled}),this._rs.registerFunction("customFunction",this.openForm.bind(this));let i=this.handleReloadPersistence();this._activeReport=i||(null===(e=this.reports.find(e=>e.is_active))||void 0===e?void 0:e.value)||"manPower",this.rcLabel=null===(t=this.reports.find(e=>e.value===this.activeReport))||void 0===t?void 0:t.label,this.selectedToggle=(null===(n=this.rcTypelist.find(e=>e.label.toLowerCase().replace("-","")===this._activeReport.toLowerCase()))||void 0===n?void 0:n.type)||1,history.pushState(null,"",location.href);const o=yield this._qbMasterService.getTheme();document.documentElement.style.setProperty("--intButton",o);const a=this._roles.roles.filter(e=>908===e.application_id),r=localStorage.getItem("openFormV");this.openFormV=!!r&&JSON.parse(r),window.addEventListener("storage",this.handleStorageChange),a.length&&(this.isRateCardUploadEnabled=a.some(e=>90801===e.object_id),this.isRateFieldConfigEnabled=a.some(e=>90802===e.object_id),this.isRateDataConfigEnabled=a.some(e=>90803===e.object_id)),this.calculateDynamicContentHeight(),this._quoteService.getQuoteConfiguration([this.mandatoryConfigName,"quote_work_location_entity_mapping_enabled","quote_service_division_mapping_enabled"]).pipe(Object(x.a)(this._onDestroy)).subscribe(e=>{if("S"==e.messType&&e.data&&e.data.length){const t=e.data;for(const e of t)if(e&&e.quote_config_name&&e.hasOwnProperty("quote_config_value"))switch(e.quote_config_name){case"quote_mandatory_fields":this.quoteMandatoryFieldList=e.quote_config_value;break;case"quote_work_location_entity_mapping_enabled":this.wEMappingEnabled=e.quote_config_value||!1;break;case"quote_service_division_mapping_enabled":this.sDMappingEnabled=e.quote_config_value||!1}}},e=>{console.log(e),this._toaster.showError("Error","Error in getting Quote configuration",3e3)}),yield this.configMaster()}))}configMaster(){return Object(w.c)(this,void 0,void 0,(function*(){this.isLoadingConfig=!0;try{yield Promise.all([this.getRcFieldConfig(1),this.getRcFieldConfig(2),this.getRcFieldConfig(3)])}catch(e){console.error("Error loading field configs:",e)}finally{this.isLoadingConfig=!1}}))}onResize(){setTimeout(()=>{this.calculateDynamicContentHeight()},10)}calculateDynamicContentHeight(){document.documentElement.style.setProperty("--reportDynamicHeight",window.innerHeight-140+"px"),document.documentElement.style.setProperty("--reportDynamicSubHeight",window.innerHeight-190+"px"),document.documentElement.style.setProperty("--reportDynamicListViewHeight",window.innerHeight-230+"px")}handleReloadPersistence(){const e=localStorage.getItem("formAction");if(e){const{rcType:t}=JSON.parse(e);return t}}openForm(e){var t,n;let i,o=null===(t=this.reports.find(e=>e.value===this.activeReport))||void 0===t?void 0:t.value,a=null===(n=this.reports.find(e=>e.value===this.activeReport))||void 0===n?void 0:n.label;"object"==typeof e&&null!==e&&"key"in e&&"lineItem"in e&&(i=e.lineItem,e=e.key),this._quoteService.setFormAction(e,o,a,i),this.openFormV=!0,localStorage.setItem("openFormV",JSON.stringify(this.openFormV))}onBackClick(){this.handleBackNavigation()}handleBackNavigation(){this.openFormV?(this.openFormV=!1,localStorage.removeItem("formValues"),localStorage.removeItem("formAction"),localStorage.removeItem("openFormV"),history.pushState(null,"",location.href),setTimeout(()=>{this.calculateDynamicContentHeight()},15)):(history.pushState(null,"",location.href),this._router.navigate(["/main/admin-programs"]))}onPopState(e){console.log(e),this.handleBackNavigation()}get activeReport(){return this._activeReport}set activeReport(e){this._activeReport!==e&&(this._activeReport=e,this.onActiveReportChange(),setTimeout(()=>{this.calculateDynamicContentHeight()},15))}onActiveReportChange(){var e,t;localStorage.removeItem("formValues");let n=null===(e=this.reports.find(e=>e.value===this.activeReport))||void 0===e?void 0:e.value;this.rcLabel=null===(t=this.reports.find(e=>e.value===this.activeReport))||void 0===t?void 0:t.label,this._quoteService.setFormAction("CREATE",n,this.rcLabel)}ngOnDestroy(){this.subs.unsubscribe(),window.removeEventListener("storage",this.handleStorageChange)}openMandatoryFieldsDialog(e){var t;this.selectedToggle=(null===(t=this.rcTypelist.find(e=>e.label.toLowerCase().replace("-","")===this._activeReport.toLowerCase()))||void 0===t?void 0:t.type)||1,history.pushState(null,"",location.href),this.mandatoryFieldDialogRef=this._dialog.open(e,{height:"75%",width:this.isQuoteEnabled?"50vw":"30vw"}),this.mandatoryFieldDialogRef.afterClosed().subscribe(e=>{})}openDataConfigDialog(){return Object(w.c)(this,void 0,void 0,(function*(){const{DataConfigPageComponent:e}=yield n.e(345).then(n.bind(null,"yI9G"));this.mandatoryFieldDialogRef=this._dialog.open(e,{height:"100%",width:"auto",minWidth:"45%",position:{right:"0px"}}),this.mandatoryFieldDialogRef.afterClosed().subscribe(e=>{this.openFormV&&e&&"manPower"==this.activeReport&&(this.openFormV=!1,setTimeout(()=>{this.openFormV=!0},5))})}))}selectToggle(e){this.selectedToggle=e,setTimeout(()=>{this.calculateDynamicContentHeight()},15)}}return e.\u0275fac=function(t){return new(t||e)(I["\u0275\u0275directiveInject"](m.b),I["\u0275\u0275directiveInject"](V.a),I["\u0275\u0275directiveInject"](R.a),I["\u0275\u0275directiveInject"](L.a),I["\u0275\u0275directiveInject"](be.a),I["\u0275\u0275directiveInject"](M.g),I["\u0275\u0275directiveInject"](M.a),I["\u0275\u0275directiveInject"](i.Location),I["\u0275\u0275directiveInject"](I.ChangeDetectorRef),I["\u0275\u0275directiveInject"](_e.a))},e.\u0275cmp=I["\u0275\u0275defineComponent"]({type:e,selectors:[["app-landing-page"]],hostBindings:function(e,t){1&e&&I["\u0275\u0275listener"]("resize",(function(){return t.onResize()}),!1,I["\u0275\u0275resolveWindow"])("popstate",(function(e){return t.onPopState(e)}),!1,I["\u0275\u0275resolveWindow"])},decls:26,vars:14,consts:[[1,"rate-card-landing-page-v2"],[1,"header-class"],[1,"d-flex","justify-content-start","align-items-center","p-1","pl-3"],["mat-icon-button","",1,"back-button",3,"click"],[1,"m-0","pl-2"],[1,"toggle-container"],["mat-button","","class","create-card",3,"click",4,"ngIf"],[3,"ngModel","ngModelChange"],["class","toggle-group",3,"value","disabled","ngClass",4,"ngFor","ngForOf"],["mat-icon-button","",1,"gear",3,"disabled","matMenuTriggerFor","matTooltip"],[4,"ngIf"],["mode","indeterminate",3,"diameter",4,"ngIf"],["configMenu","matMenu"],["mat-menu-item","","class","menu-header-class",3,"click",4,"ngIf"],["class","menu-header-class","mat-menu-item","",3,"click",4,"ngIf"],[1,"static"],[3,"applicationId","reportId",4,"ngIf"],[3,"activeReport",4,"ngIf"],["mandatoryFields",""],["mat-button","",1,"create-card",3,"click"],[1,"d-flex","align-items-center","justify-content-center"],[1,"toggle-group",3,"value","disabled","ngClass"],["mode","indeterminate",3,"diameter"],["mat-menu-item","",1,"menu-header-class",3,"click"],[1,"d-flex","align-items-center"],[3,"applicationId","reportId"],[3,"activeReport"],[1,"container-fluid","mandatory-field-styles"],[1,"row","pt-2","pb-1"],[1,"col-10","d-flex","align-items-center",2,"font-weight","500","color","#45546E"],[1,"col-2","d-flex","justify-content-end"],["mat-icon-button","",3,"click"],[2,"font-size","15px","color","#1C1B1F"],[1,"row","col-12","pt-2","toggle-class"],[3,"value"],[4,"ngFor","ngForOf"],[1,"pt-2","header"],[1,"row"],[3,"ngClass"],[1,"col-3","d-flex","justify-content-center"],["class","col-3 d-flex justify-content-center","matTooltip","Mandatory Field in Quote, Also Fetches Rate in Quote when Changes are made to the field",4,"ngIf"],["class","pb-2","style","max-height: 50vh; overflow-x: hidden;",4,"ngIf"],[1,"row","col-12","pt-1","pb-2","pl-2"],["mat-raised-button","",1,"cancel-btn","ml-2","mt-2",3,"click"],["mat-raised-button","",1,"create-btn","ml-2","mt-2",3,"click"],["class","toggle-btn",3,"value","ngClass","change",4,"ngIf"],[1,"toggle-btn",3,"value","ngClass","change"],["matTooltip","Mandatory Field in Quote, Also Fetches Rate in Quote when Changes are made to the field",1,"col-3","d-flex","justify-content-center"],[1,"pb-2",2,"max-height","50vh","overflow-x","hidden"],["class","row pt-2",4,"ngIf"],[1,"row","pt-2"],[1,"text-class",3,"ngClass"],[3,"formControl"],["class","col-3 d-flex justify-content-center",4,"ngIf"],[3,"formControl",4,"ngIf","ngIfElse"],["showEmpty",""]],template:function(e,t){if(1&e&&(I["\u0275\u0275elementStart"](0,"div",0),I["\u0275\u0275elementStart"](1,"div",1),I["\u0275\u0275elementStart"](2,"div",2),I["\u0275\u0275elementStart"](3,"button",3),I["\u0275\u0275listener"]("click",(function(){return t.onBackClick()})),I["\u0275\u0275elementStart"](4,"mat-icon"),I["\u0275\u0275text"](5," arrow_back_ios "),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](6,"p",4),I["\u0275\u0275text"](7," Rate Card Configuration "),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](8,"div",5),I["\u0275\u0275template"](9,Oe,6,1,"button",6),I["\u0275\u0275elementStart"](10,"mat-button-toggle-group",7),I["\u0275\u0275listener"]("ngModelChange",(function(e){return t.activeReport=e})),I["\u0275\u0275template"](11,xe,2,6,"mat-button-toggle",8),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](12,"button",9),I["\u0275\u0275template"](13,Se,2,0,"mat-icon",10),I["\u0275\u0275template"](14,Pe,1,1,"mat-progress-spinner",11),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](15,"mat-menu",null,12),I["\u0275\u0275template"](17,Ee,6,0,"button",13),I["\u0275\u0275template"](18,Fe,6,0,"button",14),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementEnd"](),I["\u0275\u0275elementStart"](19,"div",15),I["\u0275\u0275template"](20,De,1,2,"app-details-page",16),I["\u0275\u0275template"](21,ke,1,2,"app-details-page",16),I["\u0275\u0275template"](22,Ie,1,2,"app-details-page",16),I["\u0275\u0275template"](23,Ve,1,1,"app-form-page",17),I["\u0275\u0275elementEnd"](),I["\u0275\u0275template"](24,Ke,26,7,"ng-template",null,18,I["\u0275\u0275templateRefExtractor"]),I["\u0275\u0275elementEnd"]()),2&e){const e=I["\u0275\u0275reference"](16);I["\u0275\u0275advance"](9),I["\u0275\u0275property"]("ngIf",!t.openFormV),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngModel",t.activeReport),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngForOf",t.reports),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("disabled",t.isLoadingConfig)("matMenuTriggerFor",e)("matTooltip",t.isLoadingConfig?"Configurations Loading..":"Configurations"),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",!t.isLoadingConfig),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",t.isLoadingConfig),I["\u0275\u0275advance"](3),I["\u0275\u0275property"]("ngIf",t.isRateFieldConfigEnabled),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",t.isRateDataConfigEnabled),I["\u0275\u0275advance"](2),I["\u0275\u0275property"]("ngIf","manPower"===t.activeReport&&!t.openFormV),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf","nonManPower"===t.activeReport&&!t.openFormV),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf","license"===t.activeReport&&!t.openFormV),I["\u0275\u0275advance"](1),I["\u0275\u0275property"]("ngIf",t.openFormV)}},directives:[o.a,a.a,i.NgIf,h.b,d.v,d.y,i.NgForOf,g.f,r.a,g.g,h.a,i.NgClass,b.a,g.d,ye.a,Ce,_.a,d.k],styles:[".rate-card-landing-page-v2[_ngcontent-%COMP%]   .create-action[_ngcontent-%COMP%]{display:flex;justify-content:space-between;width:315px}.rate-card-landing-page-v2[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;border:1px solid #ccc;border-radius:8px;cursor:pointer;transition:all .3s;text-align:center;height:1.75rem;width:1.75rem}.rate-card-landing-page-v2[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{font-size:17px;color:#526179;margin-left:8%}.rate-card-landing-page-v2[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-family:DM Sans;font-weight:500;font-size:16px;color:#111434}.rate-card-landing-page-v2[_ngcontent-%COMP%]   .create-card[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;border:1px solid var(--intButton);color:var(--intButton);font-family:DM Sans;font-weight:500!important;font-size:13px;height:1.85rem;background-color:#fff}.rate-card-landing-page-v2[_ngcontent-%COMP%]   .create-card[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;font-size:18px;text-align:center}.rate-card-landing-page-v2[_ngcontent-%COMP%]   .selected-toggle[_ngcontent-%COMP%]{background:var(--intButton)!important;font-family:DM Sans;font-weight:500;font-size:16px;color:#fff}.rate-card-landing-page-v2[_ngcontent-%COMP%]   .header-class[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;border-bottom:1px solid #d4d6d8;height:3.5rem}.rate-card-landing-page-v2[_ngcontent-%COMP%]   .header-class[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-family:DM Sans;font-weight:600;font-size:18px;color:#111434}.rate-card-landing-page-v2[_ngcontent-%COMP%]   .toggle-container[_ngcontent-%COMP%]{width:38rem;display:flex;background-image:url(https://assets.kebs.app/kebs_outline_logo.svg);height:-webkit-fill-available;align-items:center;justify-content:flex-end;gap:2%}.rate-card-landing-page-v2[_ngcontent-%COMP%]   .toggle-container[_ngcontent-%COMP%]   .toggle-group[_ngcontent-%COMP%]{font-family:DM Sans;font-weight:500}.rate-card-landing-page-v2[_ngcontent-%COMP%]   .toggle-container[_ngcontent-%COMP%]   .gear[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}.rate-card-landing-page-v2[_ngcontent-%COMP%]   .toggle-container[_ngcontent-%COMP%]   .gear[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;font-size:20px;color:#8b95a5}.rate-card-landing-page-v2[_ngcontent-%COMP%]   .toggle-container[_ngcontent-%COMP%]   .gear[_ngcontent-%COMP%]     mat-progress-spinner{width:20px!important;height:20px!important}.rate-card-landing-page-v2[_ngcontent-%COMP%]   .toggle-container[_ngcontent-%COMP%]   .gear[_ngcontent-%COMP%]     mat-progress-spinner .mat-progress-spinner circle{stroke:var(--intButton)!important}.rate-card-landing-page-v2[_ngcontent-%COMP%]   .static[_ngcontent-%COMP%]{height:calc(100vh - 111px)}.mandatory-field-styles[_ngcontent-%COMP%]{font-family:DM Sans!important}.mandatory-field-styles[_ngcontent-%COMP%]   .create-btn[_ngcontent-%COMP%]{background:var(--intButton)!important;color:#fff;line-height:30px;border-radius:4px;font-family:DM Sans;font-weight:500;font-size:16px}.mandatory-field-styles[_ngcontent-%COMP%]   .create-btn-disabled[_ngcontent-%COMP%]{font-size:12px!important;line-height:30px;border-radius:4px}.mandatory-field-styles[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{background:#fff;font-size:12px!important;line-height:35px;border-radius:4px}.mandatory-field-styles[_ngcontent-%COMP%]   .text-class[_ngcontent-%COMP%]{color:#5f6c81;font-size:12px;font-style:normal;font-weight:400}.mandatory-field-styles[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{font-size:12px;font-weight:400;color:#45546e}.mandatory-field-styles[_ngcontent-%COMP%]   .service-icon-btn[_ngcontent-%COMP%]{font-size:18px;color:#908b8b}.mandatory-field-styles[_ngcontent-%COMP%]     .mat-slide-toggle-thumb{width:10px!important;height:10px!important;transform:translate(50%,50%)}.mandatory-field-styles[_ngcontent-%COMP%]     .mat-slide-toggle-bar{background-color:#dadce2;border-radius:15px!important;height:16px!important}.mandatory-field-styles[_ngcontent-%COMP%]     .mat-slide-toggle-thumb-container{top:-2px!important}.mandatory-field-styles[_ngcontent-%COMP%]     .mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-bar{background-color:var(--intButton)!important}.mandatory-field-styles[_ngcontent-%COMP%]     .mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-thumb{background-color:#fff}.mandatory-field-styles[_ngcontent-%COMP%]   .menu-header-class[_ngcontent-%COMP%]{display:flex;max-height:5vh;align-items:center;color:var(--Black-100,#111434);font-family:DM Sans;font-size:11px;font-style:normal;font-weight:400}.toggle-class[_ngcontent-%COMP%]{font-family:Plus Jakarta Sans;font-size:14px;font-weight:700;line-height:16px}.toggle-class[_ngcontent-%COMP%]   .toggle-btn[_ngcontent-%COMP%]{min-width:7rem;font-family:Plus Jakarta Sans!important}.toggle-class[_ngcontent-%COMP%]   .btn-toggle-selected[_ngcontent-%COMP%]{font-size:12px!important;background-color:var(--intButton)!important;color:#fff}"]}),e})()},{path:"form/:id",component:Ce}];let Ye=(()=>{class e{}return e.\u0275mod=I["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=I["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[M.k.forChild(Xe)],M.k]}),e})();var Ze=n("Xi0T"),et=n("yq0e"),tt=n("1+mW"),nt=n("OQIA");let it=(()=>{class e{}return e.\u0275mod=I["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=I["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,Ye,et.ReportLandingModule,o.b,a.b,r.b,s.e,l.b,c.c,d.E,d.p,p.h,m.g,g.e,u.h,h.c,f.c,Ze.a,v.d,C.k,C.n,_.b,tt.ApplicantTrackingSystemModule,y.b,O.b,nt.a,b.b]]}),e})()},OQIA:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var i=n("ofXK"),o=n("fXoL");let a=(()=>{class e{}return e.\u0275mod=o["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=o["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule]]}),e})()},R3G1:function(e,t,n){"use strict";n.d(t,"a",(function(){return a}));var i=n("xG9w"),o=n("fXoL");let a=(()=>{class e{transform(e,t,n){let o=i.findWhere(t,{field_name:e,type:n});return!!o&&!!o.is_active}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=o["\u0275\u0275definePipe"]({name:"checkActive",type:e,pure:!0}),e})()},UVjm:function(e,t,n){"use strict";n.d(t,"a",(function(){return P}));var i=n("+rOU"),o=n("fXoL"),a=n("rDax"),r=n("ofXK"),s=n("NFeN"),l=n("Qu3c"),c=n("3Pt+"),d=n("dlKe"),p=n("/rGH");const m=["inputField"],g=["width"],u=["overlayTemplateRef"];function h(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div"),o["\u0275\u0275elementStart"](1,"span",6),o["\u0275\u0275text"](2),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate"](e.placeholder)}}function f(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"div",7),o["\u0275\u0275elementStart"](1,"mat-icon",7),o["\u0275\u0275text"](2,"expand_more"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]())}function C(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",13),o["\u0275\u0275elementStart"](1,"div",14),o["\u0275\u0275pipe"](2,"masterData"),o["\u0275\u0275text"](3),o["\u0275\u0275pipe"](4,"masterData"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("matTooltip",o["\u0275\u0275pipeBind2"](2,2,e.fieldControl,e.masterData)),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate1"](" ",o["\u0275\u0275pipeBind2"](4,5,e.fieldControl,e.masterData)," ")}}function v(e,t){if(1&e&&(o["\u0275\u0275elementStart"](0,"div",14),o["\u0275\u0275pipe"](1,"masterData"),o["\u0275\u0275text"](2),o["\u0275\u0275pipe"](3,"masterData"),o["\u0275\u0275elementEnd"]()),2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275property"]("matTooltip",o["\u0275\u0275pipeBind2"](1,2,e.fieldControl,e.masterData)),o["\u0275\u0275advance"](2),o["\u0275\u0275textInterpolate1"](" ",o["\u0275\u0275pipeBind2"](3,5,e.fieldControl,e.masterData)," ")}}function b(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div",15),o["\u0275\u0275listener"]("click",(function(t){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"](2).clearAll(t)})),o["\u0275\u0275elementStart"](1,"mat-icon",7),o["\u0275\u0275text"](2,"close"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()}}function _(e,t){if(1&e&&(o["\u0275\u0275elementContainerStart"](0),o["\u0275\u0275elementStart"](1,"div",8),o["\u0275\u0275template"](2,C,5,8,"div",9),o["\u0275\u0275template"](3,v,4,8,"div",10),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](4,"div",11),o["\u0275\u0275template"](5,b,3,0,"div",12),o["\u0275\u0275elementStart"](6,"div",7),o["\u0275\u0275elementStart"](7,"mat-icon",7),o["\u0275\u0275text"](8,"expand_more"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementContainerEnd"]()),2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngIf",e.isChip),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",!e.isChip),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngIf",e.fieldControl&&e.displayClose)}}function y(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"div",26),o["\u0275\u0275namespaceSVG"](),o["\u0275\u0275elementStart"](1,"svg",27),o["\u0275\u0275elementStart"](2,"mask",28),o["\u0275\u0275element"](3,"rect",29),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](4,"g",30),o["\u0275\u0275element"](5,"path",31),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]())}function O(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementContainerStart"](0),o["\u0275\u0275elementStart"](1,"div",23),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](e);const n=t.$implicit;return o["\u0275\u0275nextContext"](3).onChangeInValue(n.id)})),o["\u0275\u0275elementStart"](2,"div",24),o["\u0275\u0275text"](3),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](4,y,6,0,"div",25),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=t.$implicit,n=o["\u0275\u0275nextContext"](3);o["\u0275\u0275advance"](2),o["\u0275\u0275classMap"](e.id==n.fieldControl?"checked-list":"list"),o["\u0275\u0275property"]("matTooltip",e.name),o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" ",e.name," "),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",e.id==n.fieldControl)}}function M(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementContainerStart"](0),o["\u0275\u0275elementStart"](1,"div",21),o["\u0275\u0275listener"]("scrolled",(function(){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"](2).loadMoreData()})),o["\u0275\u0275template"](2,O,5,5,"ng-container",22),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"](2);o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("infiniteScrollDistance",3)("infiniteScrollThrottle",100)("scrollWindow",!1),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngForOf",e.filteredData)}}function w(e,t){1&e&&(o["\u0275\u0275elementContainerStart"](0),o["\u0275\u0275elementStart"](1,"span",32),o["\u0275\u0275text"](2,"No Results Found!"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementContainerEnd"]())}const x=function(e){return{width:e}};function S(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div",16),o["\u0275\u0275elementStart"](1,"div",17),o["\u0275\u0275elementStart"](2,"input",18,19),o["\u0275\u0275listener"]("ngModelChange",(function(t){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"]().searchText=t}))("ngModelChange",(function(){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"]().onSearch()})),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275element"](4,"div",20),o["\u0275\u0275template"](5,M,3,4,"ng-container",2),o["\u0275\u0275template"](6,w,3,0,"ng-container",2),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275property"]("ngStyle",o["\u0275\u0275pureFunction1"](4,x,e.overlayWidth)),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngModel",e.searchText),o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("ngIf",e.filteredData.length>0),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",0==e.filteredData.length)}}let P=(()=>{class e{constructor(e,t){this._overlay=e,this._viewContainerRef=t,this.placeholder="Select",this.isChip=!1,this.disabled=!1,this.executeMasterOnChanges=!0,this.height="40px",this.widthHorizontal="100%",this.marginTop=null,this.patchSelectedValue=!0,this.onValueChange=new o.EventEmitter,this.searchText="",this.overlayWidth="0px",this.currentMasterData=[],this.filteredData=[],this.hasMoreData=!1,this.currentPage=1,this.pageSize=15,this.searchQuery="",this.fieldControl=null}ngOnInit(){this.currentMasterData=[...this.masterData],this.filteredData=this.currentMasterData.slice(0,this.pageSize),this.hasMoreData=this.currentMasterData.length>this.pageSize,this.selectedValue&&this.patchSelectedValue&&(this.fieldControl=this.selectedValue)}ngOnChanges(e){e.masterData&&(this.currentMasterData=[...this.masterData],this.executeMasterOnChanges&&(this.currentMasterData.some(e=>e.id==this.selectedValue)||this.onValueChange.emit({data:this.data,val:null}))),e.selectedValue&&(this.currentMasterData=[...this.masterData],this.fieldControl=this.selectedValue&&this.patchSelectedValue?this.selectedValue:null)}onChangeInValue(e){this.patchSelectedValue&&(this.fieldControl=e),this.onValueChange.emit({data:this.data,val:e}),this.closeOverlay()}setValues(){this.searchText="",this.searchQuery="",this.currentPage=1,this.filteredData=[],this.hasMoreData=!1,this.loadMoreData()}onSearch(){this.searchQuery=this.searchText.toLowerCase(),this.currentPage=1,this.filteredData=[],this.hasMoreData=!1,this.loadMoreData()}loadMoreData(){const e=(this.currentPage-1)*this.pageSize,t=e+this.pageSize;let n=this.currentMasterData;this.searchQuery&&(n=n.filter(e=>e.name.toLowerCase().includes(this.searchQuery))),this.filteredData=this.filteredData.concat(n.slice(e,t)),this.hasMoreData=n.length>t,this.currentPage++}clearAll(e){e.stopPropagation(),this.fieldControl=null,this.onValueChange.emit({data:this.data,val:null})}openOverlay(e){var t,n;if(!(null===(t=this.overlayRef)||void 0===t?void 0:t.hasAttached())){const t=this._overlay.position().flexibleConnectedTo(e).withFlexibleDimensions(!0).withPush(!0).withViewportMargin(0).withGrowAfterOpen(!0).withPositions([{originX:"center",originY:"bottom",overlayX:"center",overlayY:"top"}]),o=this._overlay.scrollStrategies.close();t.withDefaultOffsetY(-parseInt(this.height)),this.overlayRef=this._overlay.create({positionStrategy:t,scrollStrategy:o,hasBackdrop:!0,backdropClass:"",panelClass:["pop-up"]});const a=new i.h(this.overlayTemplateRef,this._viewContainerRef);this.overlayRef.attach(a),this.setValues(),setTimeout(()=>{this.inputField&&this.inputField.nativeElement&&this.inputField.nativeElement.focus()},200),this.overlayWidth=this.width&&(null===(n=this.width)||void 0===n?void 0:n.nativeElement)?this.width.nativeElement.offsetWidth+"px":"0px",this.overlayRef.backdropClick().subscribe(()=>{this.closeOverlay()})}}closeOverlay(){var e;null===(e=this.overlayRef)||void 0===e||e.dispose()}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](a.e),o["\u0275\u0275directiveInject"](o.ViewContainerRef))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-single-select-chip"]],viewQuery:function(e,t){if(1&e&&(o["\u0275\u0275viewQuery"](m,!0),o["\u0275\u0275viewQuery"](g,!0),o["\u0275\u0275viewQuery"](u,!0)),2&e){let e;o["\u0275\u0275queryRefresh"](e=o["\u0275\u0275loadQuery"]())&&(t.inputField=e.first),o["\u0275\u0275queryRefresh"](e=o["\u0275\u0275loadQuery"]())&&(t.width=e.first),o["\u0275\u0275queryRefresh"](e=o["\u0275\u0275loadQuery"]())&&(t.overlayTemplateRef=e.first)}},inputs:{masterData:"masterData",selectedValue:"selectedValue",data:"data",placeholder:"placeholder",displayClose:"displayClose",isChip:"isChip",disabled:"disabled",executeMasterOnChanges:"executeMasterOnChanges",height:"height",widthHorizontal:"widthHorizontal",marginTop:"marginTop",patchSelectedValue:"patchSelectedValue"},outputs:{onValueChange:"onValueChange"},features:[o["\u0275\u0275NgOnChangesFeature"]],decls:9,vars:12,consts:[["cdkOverlayOrigin","",1,"main-container",3,"click"],["width","","triggerOverlay","cdkOverlayOrigin","triggerOverlayField",""],[4,"ngIf"],["class","icon",4,"ngIf"],["cdkConnectedOverlay","",3,"cdkConnectedOverlayOrigin"],["overlayTemplateRef",""],[1,"search-text"],[1,"icon"],[1,"d-flex","align-items-center",2,"width","90%"],["class","d-flex align-items-center chip",4,"ngIf"],["class","chip-text",3,"matTooltip",4,"ngIf"],[1,"d-flex","align-items-center",2,"gap","8px"],["class","icon",3,"click",4,"ngIf"],[1,"d-flex","align-items-center","chip"],[1,"chip-text",3,"matTooltip"],[1,"icon",3,"click"],[1,"overlay-container",3,"ngStyle"],[1,"search-bar"],["type","text","placeholder","Search...",3,"ngModel","ngModelChange"],["inputField",""],[1,"divider"],["infinite-scroll","",1,"d-flex","flex-column","list-view",3,"infiniteScrollDistance","infiniteScrollThrottle","scrollWindow","scrolled"],[4,"ngFor","ngForOf"],[1,"d-flex","align-items-center","justify-content-between",2,"cursor","pointer",3,"click"],[3,"matTooltip"],["class","svg",4,"ngIf"],[1,"svg"],["width","18","height","18","viewBox","0 0 18 18"],["id","mask0_9363_260624","maskUnits","userSpaceOnUse","x","0","y","0","width","18","height","18",2,"mask-type","alpha"],["width","18","height","18"],["mask","url(#mask0_9363_260624)"],["d","M7.49961 10.1996L11.9246 5.77461C12.0621 5.63711 12.2371 5.56836 12.4496 5.56836C12.6621 5.56836 12.8371 5.63711 12.9746 5.77461C13.1121 5.91211 13.1809 6.08711 13.1809 6.29961C13.1809 6.51211 13.1121 6.68711 12.9746 6.82461L8.02461 11.7746C7.87461 11.9246 7.69961 11.9996 7.49961 11.9996C7.29961 11.9996 7.12461 11.9246 6.97461 11.7746L5.02461 9.82461C4.88711 9.68711 4.81836 9.51211 4.81836 9.29961C4.81836 9.08711 4.88711 8.91211 5.02461 8.77461C5.16211 8.63711 5.33711 8.56836 5.54961 8.56836C5.76211 8.56836 5.93711 8.63711 6.07461 8.77461L7.49961 10.1996Z"],[1,"no-result-text"]],template:function(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div",0,1),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](e);const n=o["\u0275\u0275reference"](3);return t.openOverlay(n)})),o["\u0275\u0275template"](4,h,3,1,"div",2),o["\u0275\u0275template"](5,f,3,0,"div",3),o["\u0275\u0275template"](6,_,9,3,"ng-container",2),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](7,S,7,6,"ng-template",4,5,o["\u0275\u0275templateRefExtractor"])}if(2&e){const e=o["\u0275\u0275reference"](2);o["\u0275\u0275styleProp"]("pointer-events",t.disabled?"none":"")("height",t.height)("width",t.widthHorizontal)("margin-top",t.marginTop),o["\u0275\u0275advance"](4),o["\u0275\u0275property"]("ngIf",!t.fieldControl),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",!t.fieldControl),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",t.fieldControl),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("cdkConnectedOverlayOrigin",e)}},directives:[a.b,r.NgIf,a.a,s.a,l.a,r.NgStyle,c.e,c.v,c.y,d.a,r.NgForOf],pipes:[p.a],styles:[".main-container[_ngcontent-%COMP%]{display:flex;flex-direction:row;align-items:center;justify-content:space-between;width:-webkit-fill-available;height:40px;border-radius:4px;border:1px solid #d2d2d2;padding:4px 8px;overflow:hidden;white-space:nowrap;cursor:pointer}.main-container[_ngcontent-%COMP%]   .search-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:12px;font-weight:500;color:rgba(0,0,0,.42)}.main-container[_ngcontent-%COMP%]   .chip[_ngcontent-%COMP%]{padding:3px 7px;border:1px solid #94a2ab;border-radius:3px;gap:4px}.main-container[_ngcontent-%COMP%]   .chip-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:12px;font-weight:400;color:#526179;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;width:100%}.main-container[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{font-size:18px;width:18px;height:18px;color:#5f6c81;cursor:pointer}.overlay-container[_ngcontent-%COMP%]{height:160px;background-color:#fff;border-radius:8px;border:1px solid #dadce2;padding:8px}.overlay-container[_ngcontent-%COMP%]     .mat-checkbox-checked.mat-accent .mat-checkbox-background, .overlay-container[_ngcontent-%COMP%]     .mat-checkbox-indeterminate .mat-checkbox-background{background-color:var(--atssecondaryColor)!important}.overlay-container[_ngcontent-%COMP%]     .mat-checkbox-inner-container{width:14px!important;height:14px!important}.overlay-container[_ngcontent-%COMP%]     .mat-checkbox-frame{border-color:#b9c0ca!important}.overlay-container[_ngcontent-%COMP%]   .checkbox[_ngcontent-%COMP%]{width:14px;height:14px;margin:0 8px 0 0}.overlay-container[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]{height:1px;width:100%;background:#e8e9ee;margin-bottom:8px;margin-top:8px}.overlay-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]{width:100%}.overlay-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{height:100%;width:95%;outline:none;border:none}.overlay-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .overlay-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{font-family:var(--atsfontFamily);font-size:11px;font-weight:400;color:#6e7b8f}.overlay-container[_ngcontent-%COMP%]   .list-view[_ngcontent-%COMP%]{overflow-y:auto;height:110px;gap:8px}.overlay-container[_ngcontent-%COMP%]   .list-view[_ngcontent-%COMP%]::-webkit-scrollbar{width:6px!important}.overlay-container[_ngcontent-%COMP%]   .list-view[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{min-height:40px!important}.overlay-container[_ngcontent-%COMP%]   .checked-list[_ngcontent-%COMP%]{color:var(--atsprimaryColor)}.overlay-container[_ngcontent-%COMP%]   .checked-list[_ngcontent-%COMP%], .overlay-container[_ngcontent-%COMP%]   .list[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:400;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.overlay-container[_ngcontent-%COMP%]   .list[_ngcontent-%COMP%]{color:#5f6c81}.overlay-container[_ngcontent-%COMP%]   .svg[_ngcontent-%COMP%]{fill:var(--atsprimaryColor)}.overlay-container[_ngcontent-%COMP%]   .no-result-text[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;font-family:var(--atsfontFamily);font-size:14px;font-weight:400;color:#6e7b8f}"]}),e})()},WYlB:function(e,t,n){"use strict";n.d(t,"a",(function(){return l})),n.d(t,"b",(function(){return c}));var i=n("jhN1"),o=n("fXoL"),a=n("B61+"),r=n("PVOt");const s=["*"];let l=(()=>{let e=class extends r.b{constructor(e,t,n,i,o,a,r){super(e,t,n,i,a,r),this._createEventEmitters([{subscribe:"click",emit:"onClick"},{subscribe:"contentReady",emit:"onContentReady"},{subscribe:"disposing",emit:"onDisposing"},{subscribe:"initialized",emit:"onInitialized"},{subscribe:"optionChanged",emit:"onOptionChanged"},{emit:"accessKeyChange"},{emit:"activeStateEnabledChange"},{emit:"disabledChange"},{emit:"elementAttrChange"},{emit:"focusStateEnabledChange"},{emit:"heightChange"},{emit:"hintChange"},{emit:"hoverStateEnabledChange"},{emit:"iconChange"},{emit:"rtlEnabledChange"},{emit:"stylingModeChange"},{emit:"tabIndexChange"},{emit:"templateChange"},{emit:"textChange"},{emit:"typeChange"},{emit:"useSubmitBehaviorChange"},{emit:"validationGroupChange"},{emit:"visibleChange"},{emit:"widthChange"}]),o.setHost(this)}get accessKey(){return this._getOption("accessKey")}set accessKey(e){this._setOption("accessKey",e)}get activeStateEnabled(){return this._getOption("activeStateEnabled")}set activeStateEnabled(e){this._setOption("activeStateEnabled",e)}get disabled(){return this._getOption("disabled")}set disabled(e){this._setOption("disabled",e)}get elementAttr(){return this._getOption("elementAttr")}set elementAttr(e){this._setOption("elementAttr",e)}get focusStateEnabled(){return this._getOption("focusStateEnabled")}set focusStateEnabled(e){this._setOption("focusStateEnabled",e)}get height(){return this._getOption("height")}set height(e){this._setOption("height",e)}get hint(){return this._getOption("hint")}set hint(e){this._setOption("hint",e)}get hoverStateEnabled(){return this._getOption("hoverStateEnabled")}set hoverStateEnabled(e){this._setOption("hoverStateEnabled",e)}get icon(){return this._getOption("icon")}set icon(e){this._setOption("icon",e)}get rtlEnabled(){return this._getOption("rtlEnabled")}set rtlEnabled(e){this._setOption("rtlEnabled",e)}get stylingMode(){return this._getOption("stylingMode")}set stylingMode(e){this._setOption("stylingMode",e)}get tabIndex(){return this._getOption("tabIndex")}set tabIndex(e){this._setOption("tabIndex",e)}get template(){return this._getOption("template")}set template(e){this._setOption("template",e)}get text(){return this._getOption("text")}set text(e){this._setOption("text",e)}get type(){return this._getOption("type")}set type(e){this._setOption("type",e)}get useSubmitBehavior(){return this._getOption("useSubmitBehavior")}set useSubmitBehavior(e){this._setOption("useSubmitBehavior",e)}get validationGroup(){return this._getOption("validationGroup")}set validationGroup(e){this._setOption("validationGroup",e)}get visible(){return this._getOption("visible")}set visible(e){this._setOption("visible",e)}get width(){return this._getOption("width")}set width(e){this._setOption("width",e)}_createInstance(e,t){return new a.a(e,t)}ngOnDestroy(){this._destroyWidget()}};return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](o.ElementRef),o["\u0275\u0275directiveInject"](o.NgZone),o["\u0275\u0275directiveInject"](r.e),o["\u0275\u0275directiveInject"](r.j),o["\u0275\u0275directiveInject"](r.i),o["\u0275\u0275directiveInject"](i.h),o["\u0275\u0275directiveInject"](o.PLATFORM_ID))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["dx-button"]],inputs:{accessKey:"accessKey",activeStateEnabled:"activeStateEnabled",disabled:"disabled",elementAttr:"elementAttr",focusStateEnabled:"focusStateEnabled",height:"height",hint:"hint",hoverStateEnabled:"hoverStateEnabled",icon:"icon",rtlEnabled:"rtlEnabled",stylingMode:"stylingMode",tabIndex:"tabIndex",template:"template",text:"text",type:"type",useSubmitBehavior:"useSubmitBehavior",validationGroup:"validationGroup",visible:"visible",width:"width"},outputs:{onClick:"onClick",onContentReady:"onContentReady",onDisposing:"onDisposing",onInitialized:"onInitialized",onOptionChanged:"onOptionChanged",accessKeyChange:"accessKeyChange",activeStateEnabledChange:"activeStateEnabledChange",disabledChange:"disabledChange",elementAttrChange:"elementAttrChange",focusStateEnabledChange:"focusStateEnabledChange",heightChange:"heightChange",hintChange:"hintChange",hoverStateEnabledChange:"hoverStateEnabledChange",iconChange:"iconChange",rtlEnabledChange:"rtlEnabledChange",stylingModeChange:"stylingModeChange",tabIndexChange:"tabIndexChange",templateChange:"templateChange",textChange:"textChange",typeChange:"typeChange",useSubmitBehaviorChange:"useSubmitBehaviorChange",validationGroupChange:"validationGroupChange",visibleChange:"visibleChange",widthChange:"widthChange"},features:[o["\u0275\u0275ProvidersFeature"]([r.e,r.j,r.i]),o["\u0275\u0275InheritDefinitionFeature"]],ngContentSelectors:s,decls:1,vars:0,template:function(e,t){1&e&&(o["\u0275\u0275projectionDef"](),o["\u0275\u0275projection"](0))},encapsulation:2}),e})(),c=(()=>{let e=class{};return e.\u0275mod=o["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=o["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[r.c,r.f,i.b],r.f]}),e})()},XPKZ:function(e,t,n){"use strict";n.d(t,"a",(function(){return c})),n.d(t,"b",(function(){return d}));var i=n("jhN1"),o=n("fXoL"),a=n("l9Wm"),r=n("PVOt"),s=n("6t9p");const l=["*"];let c=(()=>{let e=class extends r.b{constructor(e,t,n,i,o,a,r){super(e,t,n,i,a,r),this._createEventEmitters([{subscribe:"contentReady",emit:"onContentReady"},{subscribe:"disposing",emit:"onDisposing"},{subscribe:"hidden",emit:"onHidden"},{subscribe:"hiding",emit:"onHiding"},{subscribe:"initialized",emit:"onInitialized"},{subscribe:"optionChanged",emit:"onOptionChanged"},{subscribe:"showing",emit:"onShowing"},{subscribe:"shown",emit:"onShown"},{emit:"animationChange"},{emit:"closeOnOutsideClickChange"},{emit:"containerChange"},{emit:"contentTemplateChange"},{emit:"deferRenderingChange"},{emit:"disabledChange"},{emit:"elementAttrChange"},{emit:"heightChange"},{emit:"hideEventChange"},{emit:"hintChange"},{emit:"hoverStateEnabledChange"},{emit:"maxHeightChange"},{emit:"maxWidthChange"},{emit:"minHeightChange"},{emit:"minWidthChange"},{emit:"positionChange"},{emit:"rtlEnabledChange"},{emit:"shadingChange"},{emit:"shadingColorChange"},{emit:"showEventChange"},{emit:"targetChange"},{emit:"visibleChange"},{emit:"widthChange"}]),o.setHost(this)}get animation(){return this._getOption("animation")}set animation(e){this._setOption("animation",e)}get closeOnOutsideClick(){return this._getOption("closeOnOutsideClick")}set closeOnOutsideClick(e){this._setOption("closeOnOutsideClick",e)}get container(){return this._getOption("container")}set container(e){this._setOption("container",e)}get contentTemplate(){return this._getOption("contentTemplate")}set contentTemplate(e){this._setOption("contentTemplate",e)}get deferRendering(){return this._getOption("deferRendering")}set deferRendering(e){this._setOption("deferRendering",e)}get disabled(){return this._getOption("disabled")}set disabled(e){this._setOption("disabled",e)}get elementAttr(){return this._getOption("elementAttr")}set elementAttr(e){this._setOption("elementAttr",e)}get height(){return this._getOption("height")}set height(e){this._setOption("height",e)}get hideEvent(){return this._getOption("hideEvent")}set hideEvent(e){this._setOption("hideEvent",e)}get hint(){return this._getOption("hint")}set hint(e){this._setOption("hint",e)}get hoverStateEnabled(){return this._getOption("hoverStateEnabled")}set hoverStateEnabled(e){this._setOption("hoverStateEnabled",e)}get maxHeight(){return this._getOption("maxHeight")}set maxHeight(e){this._setOption("maxHeight",e)}get maxWidth(){return this._getOption("maxWidth")}set maxWidth(e){this._setOption("maxWidth",e)}get minHeight(){return this._getOption("minHeight")}set minHeight(e){this._setOption("minHeight",e)}get minWidth(){return this._getOption("minWidth")}set minWidth(e){this._setOption("minWidth",e)}get position(){return this._getOption("position")}set position(e){this._setOption("position",e)}get rtlEnabled(){return this._getOption("rtlEnabled")}set rtlEnabled(e){this._setOption("rtlEnabled",e)}get shading(){return this._getOption("shading")}set shading(e){this._setOption("shading",e)}get shadingColor(){return this._getOption("shadingColor")}set shadingColor(e){this._setOption("shadingColor",e)}get showEvent(){return this._getOption("showEvent")}set showEvent(e){this._setOption("showEvent",e)}get target(){return this._getOption("target")}set target(e){this._setOption("target",e)}get visible(){return this._getOption("visible")}set visible(e){this._setOption("visible",e)}get width(){return this._getOption("width")}set width(e){this._setOption("width",e)}_createInstance(e,t){return new a.a(e,t)}ngOnDestroy(){this._destroyWidget()}};return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](o.ElementRef),o["\u0275\u0275directiveInject"](o.NgZone),o["\u0275\u0275directiveInject"](r.e),o["\u0275\u0275directiveInject"](r.j),o["\u0275\u0275directiveInject"](r.i),o["\u0275\u0275directiveInject"](i.h),o["\u0275\u0275directiveInject"](o.PLATFORM_ID))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["dx-tooltip"]],inputs:{animation:"animation",closeOnOutsideClick:"closeOnOutsideClick",container:"container",contentTemplate:"contentTemplate",deferRendering:"deferRendering",disabled:"disabled",elementAttr:"elementAttr",height:"height",hideEvent:"hideEvent",hint:"hint",hoverStateEnabled:"hoverStateEnabled",maxHeight:"maxHeight",maxWidth:"maxWidth",minHeight:"minHeight",minWidth:"minWidth",position:"position",rtlEnabled:"rtlEnabled",shading:"shading",shadingColor:"shadingColor",showEvent:"showEvent",target:"target",visible:"visible",width:"width"},outputs:{onContentReady:"onContentReady",onDisposing:"onDisposing",onHidden:"onHidden",onHiding:"onHiding",onInitialized:"onInitialized",onOptionChanged:"onOptionChanged",onShowing:"onShowing",onShown:"onShown",animationChange:"animationChange",closeOnOutsideClickChange:"closeOnOutsideClickChange",containerChange:"containerChange",contentTemplateChange:"contentTemplateChange",deferRenderingChange:"deferRenderingChange",disabledChange:"disabledChange",elementAttrChange:"elementAttrChange",heightChange:"heightChange",hideEventChange:"hideEventChange",hintChange:"hintChange",hoverStateEnabledChange:"hoverStateEnabledChange",maxHeightChange:"maxHeightChange",maxWidthChange:"maxWidthChange",minHeightChange:"minHeightChange",minWidthChange:"minWidthChange",positionChange:"positionChange",rtlEnabledChange:"rtlEnabledChange",shadingChange:"shadingChange",shadingColorChange:"shadingColorChange",showEventChange:"showEventChange",targetChange:"targetChange",visibleChange:"visibleChange",widthChange:"widthChange"},features:[o["\u0275\u0275ProvidersFeature"]([r.e,r.j,r.i]),o["\u0275\u0275InheritDefinitionFeature"]],ngContentSelectors:l,decls:1,vars:0,template:function(e,t){1&e&&(o["\u0275\u0275projectionDef"](),o["\u0275\u0275projection"](0))},encapsulation:2}),e})(),d=(()=>{let e=class{};return e.\u0275mod=o["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=o["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[s.bb,s.Gc,s.Vd,s.Fc,s.vd,s.hb,s.lb,s.sb,s.id,s.jd,s.Ud,r.c,r.f,i.b],s.bb,s.Gc,s.Vd,s.Fc,s.vd,s.hb,s.lb,s.sb,s.id,s.jd,s.Ud,r.f]}),e})()},gVqi:function(e,t,n){"use strict";n.d(t,"a",(function(){return s}));var i=n("2Vo4"),o=n("UXun"),a=n("fXoL"),r=n("tk/3");let s=(()=>{class e{constructor(e){this.http=e,this.refreshListSubject=new i.a(!1),this.refreshList$=this.refreshListSubject.asObservable()}getActivitiesStatus(){return new Promise((e,t)=>{this.http.post("/api/activity/getActivitiesStatus",{}).subscribe(t=>e(t),e=>t(e))})}getDescriptionForActivity(e){return new Promise((t,n)=>{this.http.post("/api/activity/getDescriptionForActivity",{activity_id:e}).subscribe(e=>{t(e.description||"")},e=>{n("")})})}getActivitiesType(){return new Promise((e,t)=>{this.http.post("/api/activity/getActivitiesType",{}).subscribe(t=>e(t),e=>t(e))})}getCRMActivitiesList(e,t,n){return this.http.post("/api/activity/getCRMActivitiesList",{crm_application_id:e,application_reference_id:t,filterConfig:n})}getCRMActivitiesCard(e,t,n){return this.http.post("/api/activity/getCRMActivitiesCard",{crm_application_id:e,application_reference_id:t,filterConfig:n})}getAccountIdForApplication(e,t){return new Promise((n,i)=>{this.http.post("/api/activity/getAccountIdForApplication",{crm_application_id:e,application_reference_id:t}).subscribe(e=>{n(e)},e=>{i(e)})})}deleteCRMActivity(e,t,n){return new Promise((i,o)=>{this.http.post("/api/activity/deleteCRMActivity",{crm_application_id:e,application_reference_id:t,activity_id:n}).subscribe(e=>{i(e)},e=>{o(e)})})}updateActivityStatus(e,t,n){return new Promise((i,o)=>{this.http.post("/api/activity/updateActivityStatus",{activity_id:e,activity_status_id:t,activity_status_name:n}).subscribe(e=>i(e),e=>o(e))})}updateStartDate(e,t){return new Promise((n,i)=>{this.http.post("/api/activity/updateActivityStartDate",{activity_id:e,start_date:t}).subscribe(e=>n(e),e=>i(e))})}updateEndDate(e,t){return new Promise((n,i)=>{this.http.post("/api/activity/updateActivityEndDate",{activity_id:e,end_date:t}).subscribe(e=>n(e),e=>i(e))})}updateActivityOwner(e,t){return this.http.post("/api/opportunity/editActivityAssignedTo",{activityId:e,oid:t})}opportunityDetails(e){return this.http.post("/api/opportunity/getOpportunityDetails",{opportunityId:e})}getLeadsCardDetails(e){return new Promise((t,n)=>{this.http.post("/api/leads/getLeadsCardDetails",{leadsId:e}).subscribe(e=>t(e),e=>n(e))})}getCRMGlobalDateFormat(){return this.dateFormat$||(this.dateFormat$=this.http.post("/api/salesMaster/crm-global/date-format",{}).pipe(Object(o.a)(1))),this.dateFormat$}triggerRefreshList(e=!0){this.refreshListSubject.next(e)}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275inject"](r.c))},e.\u0275prov=a["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},hJL4:function(e,t,n){"use strict";n.d(t,"a",(function(){return p}));var i=n("mrSG"),o=n("XNiG"),a=n("xG9w"),r=n("fXoL"),s=n("tk/3"),l=n("LcQX"),c=n("XXEo"),d=n("flaP");let p=(()=>{class e{constructor(e,t,n,i){this.http=e,this.UtilityService=t,this.loginService=n,this.roleService=i,this.msg=new o.b,this.taskStatusColor=[],this.approveRequest=(e,t)=>this.http.post("/api/isa/request/approveResourceRequest",{requestDetails:e,approverOid:t}),this.rejectRequest=(e,t)=>this.http.post("/api/isa/request/rejectResourceRequest",{requestDetails:e,approverOid:t}),this.getTaskStatusColor()}getAllRequestsOfUser(e,t,n,i,o,a,r){let s=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getAllRequestsOfUser",{oid:e,statusId:t,statusCode:n,objectIds:i,skip:o,limit:a,filterConfig:r,orgIds:s})}getAllRoleAccess(){return a.where(this.roleService.roles,{application_id:139})}getRequestsBasedOnStatus(e,t,n,i,o,a,r){let s=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getRequestsBasedOnStatus",{oid:e,statusId:t,statusCode:n,objectIds:i,skip:o,limit:a,filterConfig:r,orgIds:s})}getRequestsForAwaitingApproval(e,t,n,i){return this.http.post("/api/isa/request/getRequestsForAwaitingApproval",{oid:e,skip:t,limit:n,filterConfig:i})}getObjectIdsBasedOnOid(e){return this.http.post("/api/isa/request/getObjectIdsBasedOnOid",{oid:e})}getStatusCountBasedOnOid(e,t,n,i){let o=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getStatusCountBasedOnOid",{oid:e,objectIds:t,dataTypeArray:n,filterConfig:i,orgIds:o})}getHeaderCount(e){return this.http.post("/api/isa/request/getHeaderCount",{oid:e})}getRequestBasedOnStatus(e,t){return this.http.post("/api/isa/request/getRequestBasedOnStatus",{oid:e,status:t})}getDataTypeArray(){return this.http.post("/api/isa/request/getISAStatusMasterDataUDRF",{})}sendMsg(e){this.msg.next(e)}getMsg(){return this.msg.asObservable()}getTaskStatusColor(){return new Promise((e,t)=>{this.http.post("/api/isa/request/getTaskStatusNameColor",{}).subscribe(t=>(this.taskStatusColor=t,e(t)),e=>{})})}getAllRequestsForRmg(e){return this.http.post("/api/isa/request/getAllRequestsForRmg",{filterConfig:e})}getTotalForRmg(e){return new Promise((t,n)=>{this.http.post("/api/isa/request/getTotalForRmg",{filterConfig:e}).subscribe(e=>t(e),e=>{})})}getDefaultISAStatusToDisplay(){return this.http.post("/api/isa/request/getISADefaultStatusToDisplay",{})}getCTCbreakUpForSkillSet(e){return new Promise((t,n)=>{this.http.post("/api/isa/request/getCTCbreakUpForSkillSet",e).subscribe(e=>t(e.data),e=>{n(e)})})}getCurrentUserRole(){return this.roleService&&this.roleService.roles&&this.roleService.roles.length>0?this.roleService.roles[0].role_id:null}getTemplateForUser(e){return this.http.post("/api/isa/request/getVisibleFormFieldsForUser",e)}saveAndApproveRequest(e){return this.http.post("/api/isa/request/updBudgetInfoByRequestId",e)}getVisibilityMatrix(e){return this.http.post("/api/isa/request/getAuthorzedFieldsByRoleId",e)}getISAAttachmentFromS3(e){return this.http.post("/api/isa/attachment/getISAAttachmentFromS3",{key:e})}getSkillType(){return this.http.post("/api/isa/request/getSkillType",{})}getSkillLevel(){return this.http.post("/api/isa/request/getSkillLevel",{})}getSkillExperience(){return this.http.post("/api/isa/request/getSkillExperience",{})}getSkillName(e){return this.http.post("/api/isa/request/getSkillName",{skill_type_id:e})}getIsaActivityReportDownload(){return this.http.post("/api/isa/request/getIsaActivityReportDownload",{})}getISASLAreport(e){return this.http.post("/api/isa/request/getISASLAreport",{filterConfig:e})}getISACustomReport(e){return this.http.post("/api/isa/request/getISACustomReport",{filterConfig:e})}getPositionType(){return this.http.post("/api/employee360/masterData/getPositionType",{})}calculateNetCostBasedOnPosition(e,t,n,o,r,s,l){return Object(i.c)(this,void 0,void 0,(function*(){let i;i=s&&s.length>1&&(yield this.getManpowerCostByOId(s,n,r,2))||(yield this.getManpowerCostBasedOnPosition(e,t,n,r,l));let c=yield this.getNonManpowerCost(t,n,o,r,2),d=yield this.getAllocatedCost(),p=0;p=(i?i.cost:0)+c.length>0?a.reduce(a.pluck(c,"cost"),(e,t)=>e+t,0):0;let m=d.length>0?a.reduce(a.pluck(d,"percentage"),(e,t)=>e+t,0):0;return{cost:p,currency:i&&i.currency_code?i.currency_code:"",manpowerCost:i,nonManpowerCost:c,allocatedCost:d,allocatedCostValue:p*(m/100)}}))}getManpowerCostBasedOnPosition(e,t,n,i,o){return new Promise((a,r)=>{this.http.post("/api/isa/configuration/getManpowerCostBasedOnPosition",{skillRoleId:e,nationalityId:t,locationGroupId:n,unit:i,position:o}).subscribe(e=>a(e),e=>(console.log(e),r(e)))})}getNonManpowerCost(e,t,n,i,o){return new Promise((a,r)=>{this.http.post("/api/project/getNonManpowerCost",{nationalityId:e,locationGroupId:t,locationId:n,unit:i,currency_id:o}).subscribe(e=>a(e),e=>(console.log(e),r(e)))})}getAllocatedCost(){return new Promise((e,t)=>{this.http.post("/api/project/getAllocatedCost","").subscribe(t=>e(t),e=>(console.log(e),t(e)))})}getManpowerCostByOId(e,t,n,i){return new Promise((o,a)=>{this.http.post("/api/project/getEmployeeSalary",{oid:e,location_group_id:t,unit_id:n,currency_id:i}).subscribe(e=>o(e),e=>(console.log(e),a(e)))})}getSkillNameMaster(){return this.http.post("/api/isa/configuration/getSkillName",{})}getVendorOid(e,t){return this.http.post("/api/isa/request/getVendorOid",{vendorNames:e,docID:t})}getVendorList(e,t){return this.http.post("/api/isa/request/getVendorList",{documentID:e,limitval:t})}removeVendor(e,t){return this.http.post("/api/isa/request/removeVendors",{vendorOid:e,documentID:t})}checkVendorOrNot(){return this.http.post("/api/isa/request/checkVendorOrNot",{})}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275inject"](s.c),r["\u0275\u0275inject"](l.a),r["\u0275\u0275inject"](c.a),r["\u0275\u0275inject"](d.a))},e.\u0275prov=r["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},l9Wm:function(e,t,n){"use strict";var i=n("pG26");t.a=i.a},os0P:function(e,t,n){"use strict";n.d(t,"a",(function(){return y}));var i=n("mrSG"),o=n("fXoL"),a=n("3Pt+"),r=n("jtHE"),s=n("XNiG"),l=n("NJ67"),c=n("1G5W"),d=n("xG9w"),p=n("t44d"),m=n("kmnG"),g=n("ofXK"),u=n("d3UM"),h=n("FKr1"),f=n("WJ5W"),C=n("Qu3c");const v=["singleSelect"];function b(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"mat-option",6),o["\u0275\u0275text"](1,"Select one"),o["\u0275\u0275elementEnd"]()),2&e&&o["\u0275\u0275property"]("value",null)}function _(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"mat-option",7),o["\u0275\u0275listener"]("click",(function(){o["\u0275\u0275restoreView"](e);const n=t.$implicit;return o["\u0275\u0275nextContext"]().emitChanges(n)})),o["\u0275\u0275text"](1),o["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;o["\u0275\u0275propertyInterpolate"]("matTooltip",e.name),o["\u0275\u0275property"]("value",e.id),o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let y=(()=>{class e extends l.a{constructor(e,t){super(),this.renderer=e,this.pmMasterService=t,this.fieldCtrl=new a.j,this.fieldFilterCtrl=new a.j,this.list=[],this.required=!1,this.valueChange=new o.EventEmitter,this.disabled=!1,this.disableColor="#E8E9EE",this.disableField=!1,this.showSelect=!0,this.uniqueList=[],this.filteredList=new r.a,this.change=new o.EventEmitter,this._onDestroy=new s.b,this.formConfig=[],this.emitChanges=e=>{this.change.emit(e)}}ngOnInit(){return Object(i.c)(this,void 0,void 0,(function*(){yield this.pmMasterService.getPMFormCustomizeConfigV().then(e=>{e&&(this.formConfig=e)});const e=d.where(this.formConfig,{type:"project-theme",field_name:"styles",is_active:!0});this.fontStyle=e.length>0&&e[0].data.font_style?e[0].data.font_style:"Roboto",document.documentElement.style.setProperty("--inputSearchFont",this.fontStyle),this.fieldFilterCtrl.valueChanges.pipe(Object(c.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(c.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)}),this.updateFilteredList()}))}ngOnChanges(){this.updateFilteredList()}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value,t=this.list.slice();e&&(e=e.toLowerCase(),t=t.filter(t=>t.name.toLowerCase().indexOf(e)>-1)),this.uniqueList.length>0&&(t=t.filter(e=>this.uniqueList.includes(e))),this.filteredList.next(t)}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}isDisabled(){return this.disableField?{"background-color":null!=this.disableColor?this.disableColor:"#E8E9EE","pointer-events":"none",color:"#6E7B8F"}:{}}writeValue(e){this.fieldCtrl.setValue(e)}updateFilteredList(){this.uniqueList.length>0?this.filterBanks():this.filteredList.next(this.list.slice())}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](o.Renderer2),o["\u0275\u0275directiveInject"](p.a))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search-name"]],viewQuery:function(e,t){if(1&e&&o["\u0275\u0275viewQuery"](v,!0),2&e){let e;o["\u0275\u0275queryRefresh"](e=o["\u0275\u0275loadQuery"]())&&(t.singleSelect=e.first)}},inputs:{list:"list",placeholder:"placeholder",required:"required",disabled:"disabled",disableColor:"disableColor",disableField:"disableField",showSelect:"showSelect",uniqueList:"uniqueList"},outputs:{valueChange:"valueChange",change:"change"},features:[o["\u0275\u0275ProvidersFeature"]([{provide:a.t,useExisting:Object(o.forwardRef)(()=>e),multi:!0}]),o["\u0275\u0275InheritDefinitionFeature"],o["\u0275\u0275NgOnChangesFeature"]],decls:8,vars:11,consts:[["appearance","outline",1,"app-input-search",3,"ngStyle"],["nameOnly","",3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value",4,"ngIf"],[3,"value","matTooltip","click",4,"ngFor","ngForOf"],[3,"value"],[3,"value","matTooltip","click"]],template:function(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"mat-form-field",0),o["\u0275\u0275elementStart"](1,"mat-select",1,2),o["\u0275\u0275elementStart"](3,"mat-option"),o["\u0275\u0275element"](4,"ngx-mat-select-search",3),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](5,b,2,1,"mat-option",4),o["\u0275\u0275template"](6,_,2,3,"mat-option",5),o["\u0275\u0275pipe"](7,"async"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e&&(o["\u0275\u0275property"]("ngStyle",t.isDisabled()),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",t.showSelect),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngForOf",o["\u0275\u0275pipeBind1"](7,9,t.filteredList)))},directives:[m.c,g.NgStyle,u.c,a.v,a.k,a.F,h.p,f.a,g.NgIf,g.NgForOf,C.a],pipes:[g.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}.mat-form-field[_ngcontent-%COMP%]:disabled{background-color:#0ff}.app-input-search[_ngcontent-%COMP%]     .mat-select-value{color:var(--blue-grey-80,#5f6c81);font-family:var(--inputSearchFont)!important;font-size:13px;font-style:normal;font-weight:400;line-height:16px}"]}),e})()},pG26:function(e,t,n){"use strict";var i=n("W2II"),o=n("3VAS"),a=n("CF5h"),r=n("ua+q"),s=n("KckG"),l=n("v5W6"),c=s.a.inherit({_getDefaultOptions:function(){return Object(r.a)(this.callBase(),{toolbarItems:[],showCloseButton:!1,showTitle:!1,title:null,titleTemplate:null,onTitleRendered:null,bottomTemplate:null,propagateOutsideClick:!0})},_render:function(){this.$element().addClass("dx-tooltip"),this.$wrapper().addClass("dx-tooltip-wrapper"),this.callBase()},_renderContent:function(){this.callBase(),this._contentId="dx-"+new o.a,this.$overlayContent().attr({id:this._contentId,role:"tooltip"}),this._toggleAriaDescription(!0)},_toggleAriaDescription:function(e){var t=Object(i.a)(this.option("target")),n=e?this._contentId:void 0;Object(l.p)(t.get(0))||this.setAria("describedby",n,t)}});Object(a.a)("dxTooltip",c),t.a=c},yuIm:function(e,t,n){"use strict";n.r(t),n.d(t,"roleAccessList",(function(){return i})),n.d(t,"currentJobRoleAccessList",(function(){return o})),n.d(t,"is_rds_peak",(function(){return a})),n.d(t,"moduleId",(function(){return r})),n.d(t,"subModuleId",(function(){return s})),n.d(t,"sectionId",(function(){return l})),n.d(t,"subSectionId",(function(){return c})),n.d(t,"getRoleAccessList",(function(){return d})),n.d(t,"setRoleAccessList",(function(){return p})),n.d(t,"getCurrentJobRoleAccessList",(function(){return m})),n.d(t,"setCurrentJobRoleAccessList",(function(){return g})),n.d(t,"checkAccessForGeneralRole",(function(){return u})),n.d(t,"checkAccessForJobRole",(function(){return h})),n.d(t,"changeRDSvalue",(function(){return f}));let i=[],o=[],a=!1;const r={jobs:1,candidates:2,reports:3,dashboard:4,settings:5,campusJobs:6,onboarding:8},s={manageJob:1,newJob:2,draftJob:3,manageCandidate:4,talentPipeline:5,templateSettings:10,collegeSettings:17,manageCampusJob:15,campusTalentPipeline:16,activityLogs:20,userSettings:6,profileSettings:7,notificationSettings:8,emailSettings:11,companySettings:12,vendorSettings:13,rolesAndPermissions:9,onboardingTaskSettings:28,onboardingChecklistSettings:29,onboardingChecklists:30,onboardingCandidates:31},l={manageJobDetailView:17,manageCampusJobDetailView:18,manageJobDetailViewOverview:1,manageJobDetailViewCandidate:2,manageJobDetailViewScorecard:3,manageJobDetailViewAdverts:4,manageJobDetailViewInsights:5,manageJobDetailViewEmailLogs:15,manageJobDetailViewHistoryLogs:19,manageCampusJobDetailViewOverview:6,manageCampusJobDetailViewCandidate:7,manageCampusJobDetailViewScorecard:8,manageCampusJobDetailViewAdverts:9,manageCampusJobDetailViewInsights:10,manageCampusJobDetailViewEmailLogs:16,manageCampusJobDetailViewHistoryLogs:20,manageJobCandidateDetailView:11,allCandidatesCandidateDetailView:12,talentPipelineCandidateDetailView:13,manageCampusJobCandidateDetailView:14,userSettingsDetailView:21,generalDetailView:22,themeSettings:23,emailSync:24,emailSignature:25,blockemail:26,calendarIntegration:27,organizationSettings:28,vendorDetailView:29,vendorAssignUser:30,campusTalentPipelineCandidateDetailView:31,onboardingCandidateDetailView:32},c={manageJobCandidateDetailViewDetails:1,manageJobCandidateDetailViewResume:2,manageJobCandidateDetailViewScorecard:3,manageJobCandidateDetailViewComments:4,manageJobCandidateDetailViewInterview:5,manageJobCandidateDetailViewDocuments:6,manageJobCandidateDetailViewHistory:7,manageJobCandidateDetailViewApplications:8,manageJobCandidateDetailViewInterviewScheduling:9,manageJobCandidateDetailViewSendOffer:10,manageJobCandidateDetailViewCustomQuestions:49,allCandidatesDetailViewDetails:37,allCandidatesDetailViewResume:11,allCandidatesDetailViewScorecard:12,allCandidatesDetailViewComments:13,allCandidatesDetailViewInterview:14,allCandidatesDetailViewDocuments:15,allCandidatesDetailViewHistory:16,allCandidatesDetailViewApplications:17,allCandidatesDetailViewCertificates:18,talentPipelineCandidatesDetailViewDetails:38,talentPipelineCandidatesDetailViewResume:19,talentPipelineCandidatesDetailViewScorecard:20,talentPipelineCandidatesDetailViewComments:21,talentPipelineCandidatesDetailViewInterview:22,talentPipelineCandidatesDetailViewDocuments:23,talentPipelineCandidatesDetailViewHistory:24,talentPipelineCandidatesDetailViewApplications:25,talentPipelineCandidatesDetailViewCertificates:26,manageCampusJobCandidateDetailViewDetails:27,manageCampusJobCandidateDetailViewResume:28,manageCampusJobCandidateDetailViewScorecard:29,manageCampusJobCandidateDetailViewComments:30,manageCampusJobCandidateDetailViewInterview:31,manageCampusJobCandidateDetailViewDocuments:32,manageCampusJobCandidateDetailViewHistory:33,manageCampusJobCandidateDetailViewApplications:34,manageCampusJobCandidateDetailViewCustomQuestions:50,manageCampusJobCandidateDetailViewInterviewScheduling:35,manageCampusJobCandidateDetailViewSendOffer:36,manageJobCandidateDetailViewOfferTrack:63,manageCampusJobCandidateDetailViewOfferTrack:64,allCandidatesDetailViewOfferTrack:65,talentPipelineCandidatesDetailViewOfferTrack:66,campusTalentPipelineCandidatesDetailViewDetails:67,campusTalentPipelineCandidatesDetailViewResume:68,campusTalentPipelineCandidatesDetailViewScorecard:69,campusTalentPipelineCandidatesDetailViewComments:70,campusTalentPipelineCandidatesDetailViewInterview:71,campusTalentPipelineCandidatesDetailViewDocuments:72,campusTalentPipelineCandidatesDetailViewHistory:73,campusTalentPipelineCandidatesDetailViewApplications:74,campusTalentPipelineCandidatesDetailViewCertificates:75,campusTalentPipelineCandidatesDetailViewOfferTrack:76,onboardingCandidateDetailViewChecklists:77,onboardingCandidateDetailViewDocuments:78};function d(){return i}function p(e){i=e}function m(){return o}function g(e){o=e}function u(e=0,t=0,n=0,o=0,a=""){if(!a||""==a)return!1;let r={module_id:e,sub_module_id:t,section_id:n,sub_section_id:o};"V"==a&&(r.view_permission=1),"C"==a&&(r.create_permission=1),"E"==a&&(r.edit_permission=1),"DE"==a&&(r.delete_permission=1),"DO"==a&&(r.download_permission=1),"U"==a&&(r.upload_permission=1),"B"==a&&(r.bulk_operation=1);const s=Object.keys(r);return i.find(e=>s.every(t=>e[t]===r[t]))}function h(e=0,t=0,n=0,i=0,a=""){if(!a||""==a)return!1;let r={module_id:e,sub_module_id:t,section_id:n,sub_section_id:i};"V"==a&&(r.view_permission=1),"C"==a&&(r.create_permission=1),"E"==a&&(r.edit_permission=1),"DE"==a&&(r.delete_permission=1),"DO"==a&&(r.download_permission=1),"U"==a&&(r.upload_permission=1),"B"==a&&(r.bulk_operation=1);const s=Object.keys(r);return o.find(e=>s.every(t=>e[t]===r[t]))}function f(e){return a=e,a}}}]);