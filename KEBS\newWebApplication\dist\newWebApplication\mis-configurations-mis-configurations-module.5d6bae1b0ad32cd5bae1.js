(window.webpackJsonp=window.webpackJsonp||[]).push([[802,765,821,822,983,987,990,991],{H44p:function(e,t,i){"use strict";i.d(t,"a",(function(){return g}));var n=i("xG9w"),r=i("fXoL"),o=i("flaP"),a=i("ofXK"),l=i("Qu3c"),s=i("NFeN");function c(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",9),r["\u0275\u0275elementStart"](1,"div",10),r["\u0275\u0275elementStart"](2,"div"),r["\u0275\u0275text"](3),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](4,"div"),r["\u0275\u0275elementStart"](5,"p",11),r["\u0275\u0275text"](6),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementStart"](7,"p",12),r["\u0275\u0275text"](8),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),r["\u0275\u0275advance"](3),r["\u0275\u0275textInterpolate"](null==e.currency[e.index]?null:e.currency[e.index].currency_code),r["\u0275\u0275advance"](3),r["\u0275\u0275textInterpolate"](e.label),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate1"](" ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function d(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",13),r["\u0275\u0275elementStart"](1,"span"),r["\u0275\u0275text"](2),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate1"](" ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function u(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",14),r["\u0275\u0275elementStart"](1,"span",15),r["\u0275\u0275text"](2),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function m(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",16),r["\u0275\u0275elementStart"](1,"span",15),r["\u0275\u0275text"](2),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function h(e,t){if(1&e&&(r["\u0275\u0275elementStart"](0,"div",17),r["\u0275\u0275elementStart"](1,"span",18),r["\u0275\u0275text"](2),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()),2&e){const e=r["\u0275\u0275nextContext"](2);r["\u0275\u0275property"]("matTooltip",e.toDisplay?(null==e.currency[e.index]?null:e.currency[e.index].currency_code)+" "+(null==e.currency[e.index]?null:e.currency[e.index].value):"Value is masked"),r["\u0275\u0275advance"](2),r["\u0275\u0275textInterpolate2"](" ",null==e.currency[e.index]?null:e.currency[e.index].currency_code," ",e.toDisplay?e.convert(null==e.currency[e.index]?null:e.currency[e.index].value,null==e.currency[e.index]?null:e.currency[e.index].currency_code):"*****"," ")}}function p(e,t){1&e&&(r["\u0275\u0275elementStart"](0,"mat-icon",19),r["\u0275\u0275text"](1,"loop"),r["\u0275\u0275elementEnd"]())}function f(e,t){if(1&e){const e=r["\u0275\u0275getCurrentView"]();r["\u0275\u0275elementStart"](0,"div",1),r["\u0275\u0275listener"]("click",(function(){return r["\u0275\u0275restoreView"](e),r["\u0275\u0275nextContext"]().change()})),r["\u0275\u0275template"](1,c,9,4,"div",2),r["\u0275\u0275template"](2,d,3,2,"div",3),r["\u0275\u0275template"](3,u,3,3,"div",4),r["\u0275\u0275template"](4,m,3,3,"div",5),r["\u0275\u0275template"](5,h,3,3,"div",6),r["\u0275\u0275elementStart"](6,"div",7),r["\u0275\u0275template"](7,p,2,0,"mat-icon",8),r["\u0275\u0275elementEnd"](),r["\u0275\u0275elementEnd"]()}if(2&e){const e=r["\u0275\u0275nextContext"]();r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","big"==e.type),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","small"==e.type),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","medium"==e.type),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","large"==e.type),r["\u0275\u0275advance"](1),r["\u0275\u0275property"]("ngIf","overview"==e.type),r["\u0275\u0275advance"](2),r["\u0275\u0275property"]("ngIf",e.toDisplay)}}let g=(()=>{class e{constructor(e){this.roleService=e,this.showActualAmount=!1,this.currency=[]}set currencyList(e){if(this.currency=e,e){const t=n.findIndex(e,e=>e.currency_code==this.currency_code);-1!=t&&(this.index=t,this.currency[this.index].value=Math.trunc(this.currency[this.index].value))}}ngOnInit(){null==this.toDisplay&&(this.toDisplay=!0),this.tenant_info=this.roleService.currency_info,this.tenantName=this.tenant_info.tenant_name,this.currency_code=this.defaultCurrencyCode?this.defaultCurrencyCode:this.tenant_info.default_currency?this.tenant_info.default_currency:"INR",this.setCurrency(),this.isConvertValue=null==this.tenant_info.is_to_convert_currency_value||this.tenant_info.is_to_convert_currency_value}setCurrency(){if(this.currency){const e=n.findIndex(this.currency,e=>e.currency_code==this.currency_code);-1!=e&&(this.index=e,this.currency[this.index].value=Math.trunc(this.currency[this.index].value))}}change(){void 0!==event.stopPropagation?event.stopPropagation():void 0!==event.cancelBubble&&(event.cancelBubble=!0),this.currency.length>1&&(this.index=++this.index%this.currency.length),this.currency[this.index].value=Math.trunc(this.currency[this.index].value)}convert(e,t){return e?(this.showActualAmount?e=new Intl.NumberFormat("INR"==t?"en-IN":"en-US",{}).format(e)+" "+t:1==this.isConvertValue?e="INR"==t?(e/1e7).toFixed(2)+" Cr":(e/1e6).toFixed(2)+" M":0!=this.isConvertValue||n.contains(["big","small"],this.type)?0==this.isConvertValue&&n.contains(["big","small"],this.type)&&(e="INR"==t?"big"!=this.type?"INR "+new Intl.NumberFormat("en-IN",{}).format(e):new Intl.NumberFormat("en-IN",{}).format(e):"big"!=this.type?t+" "+new Intl.NumberFormat("en-US",{}).format(e):new Intl.NumberFormat("en-US",{}).format(e)):e="INR"==t?(e/1e7).toFixed(2)+" Cr":(e/1e6).toFixed(2)+" M",e):"-"}}return e.\u0275fac=function(t){return new(t||e)(r["\u0275\u0275directiveInject"](o.a))},e.\u0275cmp=r["\u0275\u0275defineComponent"]({type:e,selectors:[["app-currency"]],inputs:{type:"type",label:"label",showActualAmount:"showActualAmount",acl:"acl",toDisplay:"toDisplay",defaultCurrencyCode:"defaultCurrencyCode",currencyList:"currencyList"},decls:1,vars:1,consts:[["class","d-flex currency-wrapper",3,"click",4,"ngIf"],[1,"d-flex","currency-wrapper",3,"click"],["class","d-flex",3,"matTooltip",4,"ngIf"],["class","data-label",3,"matTooltip",4,"ngIf"],["class","data-medium d-flex",3,"matTooltip",4,"ngIf"],["class","data-large d-flex",3,"matTooltip",4,"ngIf"],["class","justify-content-center",3,"matTooltip",4,"ngIf"],[1,"d-flex","align-items-center"],["class","change-icon pl-2","matTooltip","Next currency",4,"ngIf"],[1,"d-flex",3,"matTooltip"],[1,"pr-3","my-auto","currency-code","justify-content-center"],[1,"m-0","header"],[1,"m-0","data-label","pt-1"],[1,"data-label",3,"matTooltip"],[1,"data-medium","d-flex",3,"matTooltip"],[1,"justify-content-center"],[1,"data-large","d-flex",3,"matTooltip"],[1,"justify-content-center",3,"matTooltip"],[1,"data-overview"],["matTooltip","Next currency",1,"change-icon","pl-2"]],template:function(e,t){1&e&&r["\u0275\u0275template"](0,f,8,6,"div",0),2&e&&r["\u0275\u0275property"]("ngIf",t.currency)},directives:[a.NgIf,l.a,s.a],styles:[".header[_ngcontent-%COMP%]{color:#5f5f5f;font-size:11px!important}.data-label[_ngcontent-%COMP%]{font-size:13px!important}.change-icon[_ngcontent-%COMP%]{font-size:18px;width:auto;height:auto;visibility:hidden;cursor:default}.currency-code[_ngcontent-%COMP%]{padding-right:8px;font-size:14px!important}.currency-wrapper[_ngcontent-%COMP%]{white-space:nowrap!important;height:100%}.currency-wrapper[_ngcontent-%COMP%]:hover   .change-icon[_ngcontent-%COMP%]{visibility:visible}.data-medium[_ngcontent-%COMP%]{color:#252422!important;font-size:15px!important}.data-large[_ngcontent-%COMP%], .data-medium[_ngcontent-%COMP%]{text-align:center!important;font-weight:440!important;padding-right:4px!important}.data-large[_ngcontent-%COMP%]{font-size:large!important}.data-overview[_ngcontent-%COMP%]{color:#252422!important;font-size:15px!important;padding-left:24px!important;font-weight:500!important;padding-right:4px!important;text-align:center!important;margin:auto!important}"]}),e})()},"Q+U5":function(e,t,i){"use strict";i.r(t),i.d(t,"EditGlAccountComponent",(function(){return h}));var n=i("mrSG"),r=i("0IaG"),o=i("fXoL"),a=i("RRmA"),l=i("3Pt+");function s(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"span",11),o["\u0275\u0275text"](1,"Create GL Account Config"),o["\u0275\u0275elementEnd"]())}function c(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"span",11),o["\u0275\u0275text"](1,"MIS GL Account Config"),o["\u0275\u0275elementEnd"]())}function d(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"span",20),o["\u0275\u0275text"](1,"Active"),o["\u0275\u0275elementEnd"]())}function u(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"span",20),o["\u0275\u0275text"](1,"Inactive"),o["\u0275\u0275elementEnd"]())}function m(e,t){if(1&e){const e=o["\u0275\u0275getCurrentView"]();o["\u0275\u0275elementStart"](0,"div",12),o["\u0275\u0275elementStart"](1,"div",13),o["\u0275\u0275elementStart"](2,"span",14),o["\u0275\u0275text"](3),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](4,"div",15),o["\u0275\u0275template"](5,d,2,0,"span",16),o["\u0275\u0275template"](6,u,2,0,"span",16),o["\u0275\u0275elementStart"](7,"mat-slide-toggle",17),o["\u0275\u0275listener"]("change",(function(t){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"]().toggleAction(t)})),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](8,"button",18),o["\u0275\u0275listener"]("click",(function(){return o["\u0275\u0275restoreView"](e),o["\u0275\u0275nextContext"]().viewLogHistory()})),o["\u0275\u0275elementStart"](9,"mat-icon",19),o["\u0275\u0275text"](10,"history"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275element"](11,"div",6),o["\u0275\u0275elementEnd"]()}if(2&e){const e=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](3),o["\u0275\u0275textInterpolate1"]("GL Code - ",e.selectedItem.ruleTableDetails.gl_code,""),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngIf",1==e.isActive),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",0==e.isActive),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("matTooltip",1==e.isActive?"Active":"Inactive")("checked",1==e.isActive)}}let h=(()=>{class e{constructor(e,t,i,n,r,o){this.editDialog=e,this.dialogRef=t,this.selectedItem=i,this._misService=n,this.logDialog=r,this.formBuilder=o,this.selectedFieldItem=null,this.misFields=i.misFieldData,null!=i.ruleTableDetails&&(this.isActive=i.ruleTableDetails.is_active),this._misService.$determineMandatory.subscribe(e=>{if(this.selectedFieldItem=null,""!=e){if(this.parentData=e.field.field_name,17==e.id)this.selectedFieldItem="manualPercentage";else{let t=this.misFields.filter(t=>{if(null!=t.parent_dependent&&t.parent_dependent==this.parentData&&-1!=t.field_id.indexOf(e.id))return t});0!=t.length&&(this.selectedFieldItem=t[0].field_name)}e.field.choices.forEach(e=>{for(let t=0;t<this.misFields.length;t++)this.misFields[t].field_name==this.selectedFieldItem&&(this.misFields[t].is_visible=1),e!=this.selectedFieldItem&&null!=this.misFields[t].parent_dependent&&e==this.misFields[t].field_name&&(this.misFields[t].is_visible=0,this.misFields[t].no_to_retrieve="")}),this._misService.patchFieldValue(this.selectedFieldItem),this.selectedFieldItem=null}}),this._misService.misSearchData.subscribe(e=>{this.onSearchMisData(e)})}ngOnInit(){return Object(n.c)(this,void 0,void 0,(function*(){}))}noToRetrieve(e){return Object(n.c)(this,void 0,void 0,(function*(){for(let t=0;t<this.misFields.length;t++)this.misFields[t].field_name==e.field_name&&(this.masterData=yield this._misService.getMasterGlEdit(e.field_name,null,null,null),null==this.misFields[t].options?(this.misFields[t]=[],this.misFields[t].push(this.masterData.data)):this.misFields[t].options=this.misFields[t].options.concat(this.masterData.data))}))}onSearchMisData(e){return Object(n.c)(this,void 0,void 0,(function*(){for(let t=0;t<this.misFields.length;t++)this.misFields[t].field_name==e.field_name&&(this.misFields[t].options=[null],this.misFields[t].options=this.misFields[t].selected_value_pair,this.masterData=yield this._misService.getMasterGlEdit(e.field_name,null,null,null),null==this.misFields[t].options?(this.misFields[t].options=[],this.misFields[t].options.push(this.masterData.data)):this.misFields[t].options=this.misFields[t].options.concat(this.masterData.data))}))}toggleAction(e){this.isActive=e.checked?1:0,this._misService.$toggleVisibility.next(this.isActive)}closeEditDialog(){this.editDialog.close()}viewLogHistory(){return Object(n.c)(this,void 0,void 0,(function*(){let e=yield this._misService.getLogGLData(this.selectedItem.ruleTableDetails.id);const{LogHistoryComponent:t}=yield i.e(979).then(i.bind(null,"hg9u"));this.logDialog.open(t,{height:"60vh",width:"40vw",data:e})}))}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275directiveInject"](r.h),o["\u0275\u0275directiveInject"](r.h),o["\u0275\u0275directiveInject"](r.a),o["\u0275\u0275directiveInject"](a.a),o["\u0275\u0275directiveInject"](r.b),o["\u0275\u0275directiveInject"](l.i))},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-edit-gl-account"]],decls:16,vars:4,consts:[[1,"container-fluid","edit-gl-account"],[1,"row","mt-4","mb-2"],[1,"col"],[1,"pl-2",2,"vertical-align","middle"],[1,"icon"],["class","pl-2 heading",4,"ngIf"],[1,"col-1"],[1,"vertical-align:middle",3,"click"],[1,"icon-cancel"],["class"," mb-2 row",4,"ngIf"],[3,"fields","misEvent"],[1,"pl-2","heading"],[1,"mb-2","row"],[1,"col-8","p-0","pl-3"],[1,"badge","badge-light","pt-2","pb-2","pr-4","heading",2,"color","#cf0001","font-weight","bold","font-size","14px"],[1,"d-flex","justify-content-end","p-0","col"],["class","pt-2","style","color:#66615b",4,"ngIf"],[1,"ml-2","mr-4","pt-1",3,"matTooltip","checked","change"],["mat-mini-fab","","matTooltip","View history",1,"ml-2",2,"font-size","7px","background-color","#fafafa",3,"click"],[2,"font-size","19px","color","#66615b"],[1,"pt-2",2,"color","#66615b"]],template:function(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"div",0),o["\u0275\u0275elementStart"](1,"div",1),o["\u0275\u0275elementStart"](2,"div",2),o["\u0275\u0275elementStart"](3,"span",3),o["\u0275\u0275elementStart"](4,"mat-icon",4),o["\u0275\u0275text"](5,"table_chart"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](6,s,2,0,"span",5),o["\u0275\u0275template"](7,c,2,0,"span",5),o["\u0275\u0275elementEnd"](),o["\u0275\u0275element"](8,"div",6),o["\u0275\u0275elementStart"](9,"div",6),o["\u0275\u0275elementStart"](10,"span",7),o["\u0275\u0275listener"]("click",(function(){return t.closeEditDialog()})),o["\u0275\u0275elementStart"](11,"mat-icon",8),o["\u0275\u0275text"](12,"close"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275element"](13,"hr"),o["\u0275\u0275template"](14,m,12,5,"div",9),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](15,"app-mis-form-builder",10),o["\u0275\u0275listener"]("misEvent",(function(e){return t.noToRetrieve(e)})),o["\u0275\u0275elementEnd"]()),2&e&&(o["\u0275\u0275advance"](6),o["\u0275\u0275property"]("ngIf",0==t.selectedItem.editForm),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",1==t.selectedItem.editForm),o["\u0275\u0275advance"](7),o["\u0275\u0275property"]("ngIf",1==t.selectedItem.editForm),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("fields",t.misFields))},styles:[".edit-gl-account[_ngcontent-%COMP%]   .container-fluid[_ngcontent-%COMP%]{font-family:Roboto}.edit-gl-account[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .edit-gl-account[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus{border:none;outline:none;caret-color:#cf0001}.edit-gl-account[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{color:#cf0001;font-size:20px;vertical-align:middle}.edit-gl-account[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%], .edit-gl-account[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]{color:#cf0001;font-size:14px;font-family:Roboto;font-weight:500}.edit-gl-account[_ngcontent-%COMP%]   .icon-add[_ngcontent-%COMP%]{color:#66615b;vertical-align:middle;cursor:pointer}.edit-gl-account[_ngcontent-%COMP%]   .icon-cancel[_ngcontent-%COMP%]{color:#a1a1a2;font-size:20px;cursor:pointer}.edit-gl-account[_ngcontent-%COMP%]   .icon-search[_ngcontent-%COMP%], .edit-gl-account[_ngcontent-%COMP%]   .icon-search-cancel[_ngcontent-%COMP%]{color:#a1a1a2;font-size:20px;vertical-align:middle}.edit-gl-account[_ngcontent-%COMP%]   .icon-search-cancel[_ngcontent-%COMP%]{cursor:pointer}.edit-gl-account[_ngcontent-%COMP%]   .submit[_ngcontent-%COMP%]{cursor:pointer;background-color:#cf0001;color:#fff}.edit-gl-account[_ngcontent-%COMP%]   .cancel[_ngcontent-%COMP%]{cursor:pointer;background-color:#7f7f81;color:#fff}.edit-gl-account[_ngcontent-%COMP%]   .submit-btn[_ngcontent-%COMP%]{background:linear-gradient(44deg,#e86565 10%,#cf0001 105.29%);border-radius:8px;color:#fff}.edit-gl-account[_ngcontent-%COMP%]   .col-5[_ngcontent-%COMP%]{padding:0}.edit-gl-account[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]{width:100%}.edit-gl-account[_ngcontent-%COMP%]   .disabled-contenct[_ngcontent-%COMP%]{pointer-events:none;opacity:.4}.edit-gl-account[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]{cursor:pointer;font-size:25px;color:#526179;vertical-align:middle}.edit-gl-account[_ngcontent-%COMP%]   .align-middle[_ngcontent-%COMP%]{vertical-align:middle}.edit-gl-account[_ngcontent-%COMP%]   .click[_ngcontent-%COMP%]{background-color:#f9f8f3}.edit-gl-account[_ngcontent-%COMP%]   .mat-fab-icon[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff}.edit-gl-account[_ngcontent-%COMP%]   .mat-mini-fab[_ngcontent-%COMP%]{height:30px;width:31px}.edit-gl-account[_ngcontent-%COMP%]   .in-valid[_ngcontent-%COMP%]{color:#cf0001;font-size:14px}.edit-gl-account[_ngcontent-%COMP%]   .manual-card[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;vertical-align:middle}.edit-gl-account[_ngcontent-%COMP%]   .add-icon[_ngcontent-%COMP%]{cursor:pointer;color:#cf0001;font-size:30px}.edit-gl-account[_ngcontent-%COMP%]     .mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-bar, .edit-gl-account[_ngcontent-%COMP%]     .mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-thumb{background-color:#cf0001}.edit-gl-account[_ngcontent-%COMP%]   .iconbtn[_ngcontent-%COMP%]{background-color:#c92020;color:#fff;padding:0}.edit-gl-account[_ngcontent-%COMP%]   .icon-add[_ngcontent-%COMP%]   .my-mini-fab[_ngcontent-%COMP%]{width:10px;height:10px;line-height:14px;font-size:5px}.edit-gl-account[_ngcontent-%COMP%]   .icon-add[_ngcontent-%COMP%]   .my-mini-fab[_ngcontent-%COMP%]  .mat-button-wrapper{line-height:10px;padding:0}.edit-gl-account[_ngcontent-%COMP%]   .icon-add[_ngcontent-%COMP%]   .my-mini-fab[_ngcontent-%COMP%]  .mat-button-wrapper .mat-icon{font-size:5px;padding-right:4px;padding-top:4px}.edit-gl-account[_ngcontent-%COMP%]   .iconbtnSubmit[_ngcontent-%COMP%]{background-color:#c92020}.edit-gl-account[_ngcontent-%COMP%]   .iconbtnClose[_ngcontent-%COMP%], .edit-gl-account[_ngcontent-%COMP%]   .iconbtnSubmit[_ngcontent-%COMP%]{cursor:pointer;color:#fff;padding:0;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.edit-gl-account[_ngcontent-%COMP%]   .iconbtnClose[_ngcontent-%COMP%]{background-color:#a1a1a2}.edit-gl-account[_ngcontent-%COMP%]   .restrict-cursor[_ngcontent-%COMP%]{cursor:not-allowed;pointer-events:none;background-color:#c84c4c;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.edit-gl-account[_ngcontent-%COMP%]   .history-btn[_ngcontent-%COMP%]   .mat-mini-fab[_ngcontent-%COMP%]{height:26px;width:27px}.edit-gl-account[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]{color:#cf0001}"]}),e})()},QbZZ:function(e,t,i){"use strict";i.r(t),i.d(t,"EditRuleSheetComponent",(function(){return p}));var n=i("mrSG"),r=i("3Pt+"),o=i("0IaG"),a=i("fXoL"),l=i("RRmA"),s=i("XXEo");function c(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"span",11),a["\u0275\u0275text"](1,"Create Config"),a["\u0275\u0275elementEnd"]())}function d(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"span",11),a["\u0275\u0275text"](1,"MIS Rule Config"),a["\u0275\u0275elementEnd"]())}function u(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"span",20),a["\u0275\u0275text"](1,"Active"),a["\u0275\u0275elementEnd"]())}function m(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"span",20),a["\u0275\u0275text"](1,"Inactive"),a["\u0275\u0275elementEnd"]())}function h(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"div",12),a["\u0275\u0275elementStart"](1,"div",13),a["\u0275\u0275elementStart"](2,"span",14),a["\u0275\u0275text"](3),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](4,"div",15),a["\u0275\u0275template"](5,u,2,0,"span",16),a["\u0275\u0275template"](6,m,2,0,"span",16),a["\u0275\u0275elementStart"](7,"mat-slide-toggle",17),a["\u0275\u0275listener"]("change",(function(t){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"]().toggleAction(t)})),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](8,"button",18),a["\u0275\u0275listener"]("click",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"]().viewLogHistory()})),a["\u0275\u0275elementStart"](9,"mat-icon",19),a["\u0275\u0275text"](10,"history"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275element"](11,"div",6),a["\u0275\u0275elementEnd"]()}if(2&e){const e=a["\u0275\u0275nextContext"]();a["\u0275\u0275advance"](3),a["\u0275\u0275textInterpolate1"]("Rule ",e.selectedItem.ruleTableDetails.id,""),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("ngIf",1==e.isActive),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",0==e.isActive),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("matTooltip",1==e.isActive?"Active":"Inactive")("checked",1==e.isActive)}}let p=(()=>{class e{constructor(e,t,i,n,o,a,l){this.editDialog=e,this.dialogRef=t,this.selectedItem=i,this._misService=n,this._auth=o,this.logDialog=a,this.formBuilder=l,this.selectedFieldItem=null,this.manualPercentage=null,this.manualPercentageDisplay=!1,this.fieldChoise=null,this.misFields=i.misFieldData,this.misFields.forEach(e=>{}),this.manualGroup=this.formBuilder.group({manual_percent:["",r.H.required],manual_cost:["",r.H.required]}),null!=i.ruleTableDetails&&(this.isActive=i.ruleTableDetails.is_active),this._misService.$determineMandatory.subscribe(e=>{if(this.selectedFieldItem=null,""!=e){if(this.parentData=e.field.field_name,17==e.id)this.selectedFieldItem="manualPercentage";else{let t=this.misFields.filter(t=>{if(null!=t.parent_dependent&&t.parent_dependent==this.parentData&&t.field_id&&e.id&&-1!=JSON.parse(t.field_id).indexOf(e.id))return t});0!=t.length&&(this.selectedFieldItem=t[0].field_name)}JSON.parse(e.field.choices).forEach(e=>{for(let t=0;t<this.misFields.length;t++)this.misFields[t].field_name==this.selectedFieldItem&&(this.misFields[t].is_visible=1),e!=this.selectedFieldItem&&null!=this.misFields[t].parent_dependent&&e==this.misFields[t].field_name&&(this.misFields[t].is_visible=0)}),this._misService.patchFieldValue(this.selectedFieldItem),this.selectedFieldItem=null}}),this.misFields=i.misFieldData,this.misFields.forEach(e=>{"cost_center"==e.field_name&&(this.manualPercentageArray=e.options)}),this._misService.misSearchData.subscribe(e=>{this.onSearchMisData(e)})}ngOnInit(){return Object(n.c)(this,void 0,void 0,(function*(){}))}noToRetrieve(e){return Object(n.c)(this,void 0,void 0,(function*(){for(let t=0;t<this.misFields.length;t++)this.misFields[t].field_name==e.field_name&&(this.masterData="true"==this.misFields[t].id_value_pair?yield this._misService.getMasterEdit(e.field_name,e.scroll_index,e.id_description,e.search_value):yield this._misService.getMasterEdit(e.field_name,e.scroll_index,e.no_to_retrieve,e.search_value),null==this.misFields[t].options?(this.misFields[t]=[],this.misFields[t].push(this.masterData.data)):this.misFields[t].options=this.misFields[t].options.concat(this.masterData.data))}))}toggleAction(e){e.checked?(this.isActive=1,this._misService.$toggleVisibility.next(this.isActive)):(this.isActive=0,this._misService.$toggleVisibility.next(this.isActive))}onSearchMisData(e){return Object(n.c)(this,void 0,void 0,(function*(){for(let t=0;t<this.misFields.length;t++)if(this.misFields[t].field_name==e.field_name){if(this.misFields[t].options=[],this.misFields[t].options=this.misFields[t].selected_value_pair,"true"==this.misFields[t].id_value_pair){let t=yield this._misService.getMasterEdit(e.field_name,e.scroll_index,e.id_description,e.search_value);this.masterData=t.data}else{let t=yield this._misService.getMasterEdit(e.field_name,e.scroll_index,e.no_to_retrieve,e.search_value);this.masterData=t.data}null==this.misFields[t].options?(this.misFields[t].options=[],this.misFields[t].options=this.misFields[t].options.concat(this.masterData)):this.misFields[t].options=this.misFields[t].options.concat(this.masterData)}}))}closeEditDialog(){this.editDialog.close()}viewLogHistory(){return Object(n.c)(this,void 0,void 0,(function*(){let e=yield this._misService.getLogMisRuleData(this.selectedItem.ruleTableDetails.id);const{LogHistoryComponent:t}=yield i.e(979).then(i.bind(null,"hg9u"));this.logDialog.open(t,{height:"60vh",width:"40vw",data:e})}))}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275directiveInject"](o.h),a["\u0275\u0275directiveInject"](o.h),a["\u0275\u0275directiveInject"](o.a),a["\u0275\u0275directiveInject"](l.a),a["\u0275\u0275directiveInject"](s.a),a["\u0275\u0275directiveInject"](o.b),a["\u0275\u0275directiveInject"](r.i))},e.\u0275cmp=a["\u0275\u0275defineComponent"]({type:e,selectors:[["app-edit-rule-sheet"]],decls:16,vars:5,consts:[[1,"edit-rule-sheet","container-fluid"],[1,"row","mt-4","mb-2"],[1,"col"],[1,"pl-2",2,"vertical-align","middle"],[1,"icon"],["class","pl-2 heading",4,"ngIf"],[1,"col-1"],[1,"vertical-align:middle",3,"click"],[1,"icon-cancel"],["class"," mb-2 row",4,"ngIf"],[3,"fields","fieldArray","misEvent"],[1,"pl-2","heading"],[1,"mb-2","row"],[1,"col-8","p-0","pl-3"],[1,"badge","badge-light","pt-2","pb-2","pr-4","heading",2,"color","#cf0001","font-weight","bold","font-size","14px"],[1,"d-flex","justify-content-end","p-0","col"],["class","pt-2","style","color:#66615b",4,"ngIf"],[1,"ml-2","mr-4","pt-1",3,"matTooltip","checked","change"],["mat-mini-fab","","matTooltip","View history",1,"ml-2",2,"font-size","7px","background-color","#fafafa",3,"click"],[2,"font-size","19px","color","#66615b"],[1,"pt-2",2,"color","#66615b"]],template:function(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"div",0),a["\u0275\u0275elementStart"](1,"div",1),a["\u0275\u0275elementStart"](2,"div",2),a["\u0275\u0275elementStart"](3,"span",3),a["\u0275\u0275elementStart"](4,"mat-icon",4),a["\u0275\u0275text"](5,"table_chart"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](6,c,2,0,"span",5),a["\u0275\u0275template"](7,d,2,0,"span",5),a["\u0275\u0275elementEnd"](),a["\u0275\u0275element"](8,"div",6),a["\u0275\u0275elementStart"](9,"div",6),a["\u0275\u0275elementStart"](10,"span",7),a["\u0275\u0275listener"]("click",(function(){return t.closeEditDialog()})),a["\u0275\u0275elementStart"](11,"mat-icon",8),a["\u0275\u0275text"](12,"close"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275element"](13,"hr"),a["\u0275\u0275template"](14,h,12,5,"div",9),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](15,"app-mis-form-builder",10),a["\u0275\u0275listener"]("misEvent",(function(e){return t.noToRetrieve(e)})),a["\u0275\u0275elementEnd"]()),2&e&&(a["\u0275\u0275advance"](6),a["\u0275\u0275property"]("ngIf",0==t.selectedItem.editForm),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf",1==t.selectedItem.editForm),a["\u0275\u0275advance"](7),a["\u0275\u0275property"]("ngIf",1==t.selectedItem.editForm),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("fields",t.misFields)("fieldArray",t.manualPercentage))},styles:[".edit-rule-sheet[_ngcontent-%COMP%]   .container-fluid[_ngcontent-%COMP%]{font-family:Roboto}.edit-rule-sheet[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .edit-rule-sheet[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus{border:none;outline:none;caret-color:#cf0001}.edit-rule-sheet[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{color:#cf0001;font-size:20px;vertical-align:middle}.edit-rule-sheet[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%], .edit-rule-sheet[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]{color:#cf0001;font-size:14px;font-family:Roboto;font-weight:500}.edit-rule-sheet[_ngcontent-%COMP%]   .icon-add[_ngcontent-%COMP%]{color:#66615b;vertical-align:middle;cursor:pointer}.edit-rule-sheet[_ngcontent-%COMP%]   .icon-cancel[_ngcontent-%COMP%]{color:#a1a1a2;font-size:20px;cursor:pointer}.edit-rule-sheet[_ngcontent-%COMP%]   .icon-search[_ngcontent-%COMP%], .edit-rule-sheet[_ngcontent-%COMP%]   .icon-search-cancel[_ngcontent-%COMP%]{color:#a1a1a2;font-size:20px;vertical-align:middle}.edit-rule-sheet[_ngcontent-%COMP%]   .icon-search-cancel[_ngcontent-%COMP%]{cursor:pointer}.edit-rule-sheet[_ngcontent-%COMP%]   .submit[_ngcontent-%COMP%]{cursor:pointer;background-color:#cf0001;color:#fff}.edit-rule-sheet[_ngcontent-%COMP%]   .cancel[_ngcontent-%COMP%]{cursor:pointer;background-color:#7f7f81;color:#fff}.edit-rule-sheet[_ngcontent-%COMP%]   .submit-btn[_ngcontent-%COMP%]{background:linear-gradient(44deg,#e86565 10%,#cf0001 105.29%);border-radius:8px;color:#fff}.edit-rule-sheet[_ngcontent-%COMP%]   .col-5[_ngcontent-%COMP%]{padding:0}.edit-rule-sheet[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]{width:100%}.edit-rule-sheet[_ngcontent-%COMP%]   .disabled-contenct[_ngcontent-%COMP%]{pointer-events:none;opacity:.4}.edit-rule-sheet[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]{cursor:pointer;font-size:25px;color:#526179;vertical-align:middle}.edit-rule-sheet[_ngcontent-%COMP%]   .align-middle[_ngcontent-%COMP%]{vertical-align:middle}.edit-rule-sheet[_ngcontent-%COMP%]   .click[_ngcontent-%COMP%]{background-color:#f9f8f3}.edit-rule-sheet[_ngcontent-%COMP%]   .mat-fab-icon[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff}.edit-rule-sheet[_ngcontent-%COMP%]   .mat-mini-fab[_ngcontent-%COMP%]{height:30px;width:31px}.edit-rule-sheet[_ngcontent-%COMP%]   .in-valid[_ngcontent-%COMP%]{color:#cf0001;font-size:14px}.edit-rule-sheet[_ngcontent-%COMP%]   .manual-card[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;vertical-align:middle}.edit-rule-sheet[_ngcontent-%COMP%]   .add-icon[_ngcontent-%COMP%]{cursor:pointer;color:#cf0001;font-size:30px}.edit-rule-sheet[_ngcontent-%COMP%]     .mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-bar, .edit-rule-sheet[_ngcontent-%COMP%]     .mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-thumb{background-color:#cf0001}.edit-rule-sheet[_ngcontent-%COMP%]   .iconbtn[_ngcontent-%COMP%]{background-color:#c92020;color:#fff;padding:0}.edit-rule-sheet[_ngcontent-%COMP%]   .icon-add[_ngcontent-%COMP%]   .my-mini-fab[_ngcontent-%COMP%]{width:10px;height:10px;line-height:14px;font-size:5px}.edit-rule-sheet[_ngcontent-%COMP%]   .icon-add[_ngcontent-%COMP%]   .my-mini-fab[_ngcontent-%COMP%]  .mat-button-wrapper{line-height:10px;padding:0}.edit-rule-sheet[_ngcontent-%COMP%]   .icon-add[_ngcontent-%COMP%]   .my-mini-fab[_ngcontent-%COMP%]  .mat-button-wrapper .mat-icon{font-size:5px;padding-right:4px;padding-top:4px}.edit-rule-sheet[_ngcontent-%COMP%]   .iconbtnSubmit[_ngcontent-%COMP%]{background-color:#c92020}.edit-rule-sheet[_ngcontent-%COMP%]   .iconbtnClose[_ngcontent-%COMP%], .edit-rule-sheet[_ngcontent-%COMP%]   .iconbtnSubmit[_ngcontent-%COMP%]{cursor:pointer;color:#fff;padding:0;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.edit-rule-sheet[_ngcontent-%COMP%]   .iconbtnClose[_ngcontent-%COMP%]{background-color:#a1a1a2}.edit-rule-sheet[_ngcontent-%COMP%]   .restrict-cursor[_ngcontent-%COMP%]{cursor:not-allowed;pointer-events:none;background-color:#c84c4c;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.edit-rule-sheet[_ngcontent-%COMP%]   .history-btn[_ngcontent-%COMP%]   .mat-mini-fab[_ngcontent-%COMP%]{height:26px;width:27px}.edit-rule-sheet[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]{color:#cf0001}"]}),e})()},RRmA:function(e,t,i){"use strict";i.d(t,"a",(function(){return d}));var n=i("9lTW"),r=i("2Vo4"),o=i("XNiG"),a=i("fXoL"),l=i("tk/3"),s=i("5eHb"),c=i("JqCM");let d=(()=>{class e{constructor(e,t,i){this.http=e,this.toastr=t,this.spinnerService=i,this.searchParamReload=new r.a(""),this.currentSearchParameter=this.searchParamReload.asObservable(),this.misSearchData=new r.a("search data"),this.$misSearch=this.misSearchData.asObservable(),this.$determineRuleCode=new o.b,this.$determineMandatory=new r.a(""),this.$patchFieldValue=new r.a(""),this.$clearManualPercentage=new o.b,this.$toggleVisibility=new o.b,this.searchSubject=new o.b,this.$searchRetain=this.searchSubject.asObservable()}retainSearch(e){this.searchSubject.next(e)}determineMandatory(e){this.$determineMandatory.next(e)}patchFieldValue(e){null!=e&&this.$patchFieldValue.next(e)}getManualRuleCode(e){this.$determineRuleCode.next(e)}clearManualPercentage(){this.$clearManualPercentage.next()}getMISTotalCount(e){try{return this.http.post("api/project/misFunctions/misRuleCount",{filterConfig:e})}catch(t){return this.spinnerService.hide(),t}}getGlAccountDataCount(e){try{return this.http.post("api/project/misFunctions/glAccountDataCount",{filterConfig:e})}catch(t){return t}}getMISList(e){try{return this.http.post("api/project/misFunctions/getMisRuleData",{filterConfig:e})}catch(t){return this.spinnerService.hide(),t}}getGlAccountData(e){try{return this.http.post("api/project/misFunctions/getGlAccountData",{filterConfig:e})}catch(t){return this.spinnerService.hide(),t}}getMasterEdit(e,t,i,r){try{return new Promise((n,o)=>{this.http.post("api/project/misFunctions/masterEditDisplay",{fieldName:e,startIndex:t,noToRetrieveIds:i,searchParameter:r}).subscribe(e=>n(e),e=>(this.spinnerService.hide(),o(e)))})}catch(o){return Object(n.rejects)(o)}}getMasterGlEdit(e,t,i,r){try{return new Promise((n,o)=>{this.http.post("api/project/misFunctions/masterEditGlAccount",{fieldName:e,startIndex:t,noToRetrieveIds:i,searchParameter:r}).subscribe(e=>n(e),e=>(this.spinnerService.hide(),o(e)))})}catch(o){return Object(n.rejects)(o)}}getMasterData(e,t,i){try{return new Promise((n,r)=>{this.http.post("api/project/misFunctions/getLocationData",{fieldname:"",startIndex:e,noToRetrieveIds:t,searchParameter:i}).subscribe(e=>n(e),e=>(this.spinnerService.hide(),r(e)))})}catch(r){return Object(n.rejects)(r)}}updateConfiguration(e,t){this.searchParamReload.next(t);try{return new Promise((t,i)=>{this.http.post("api/project/misFunctions/updateMisRuleData",{updateMisData:{configuration:e}}).subscribe(e=>("S"==e.messType?this.toastr.success("Update Successful !","Success",{timeOut:3e3}):this.toastr.error("Sorry! couldn't update","Error!",{timeOut:3e3}),t(e)),e=>(this.spinnerService.hide(),this.toastr.error("Sorry! couldn't update","Error!",{timeOut:3e3}),i(e)))})}catch(i){return Object(n.rejects)(i)}}updateGLConfiguration(e,t){this.searchParamReload.next(t);try{return new Promise((t,i)=>{this.http.post("api/project/misFunctions/updateGLAccountData",{updateGlData:{configuration:e}}).subscribe(e=>("S"==e.messType?this.toastr.success("Update Successful !","Success",{timeOut:3e3}):this.toastr.error("Sorry! couldn't update","Error!",{timeOut:3e3}),t(e)),e=>(this.spinnerService.hide(),this.toastr.error("Sorry! couldn't update","Error!",{timeOut:3e3}),i(e)))})}catch(i){return Object(n.rejects)(i)}}insertConfiguration(e){try{return new Promise((t,i)=>{this.http.post("api/project/misFunctions/insertMisRuleData",{insertMisData:{configuration:e}}).subscribe(e=>("S"==e.messType?this.toastr.success("Insertion Successful !","Success",{timeOut:3e3}):this.toastr.error("Sorry! couldn't insert","Error!",{timeOut:3e3}),t(e)),e=>(this.spinnerService.hide(),this.toastr.error("Sorry! couldn't insert","Error!",{timeOut:3e3}),i(e)))})}catch(t){return Object(n.rejects)(t)}}insertGLConfiguration(e){try{return new Promise((t,i)=>{this.http.post("api/project/misFunctions/insertGLAccountData",{insertGlData:{configuration:e}}).subscribe(e=>("S"==e.messType?this.toastr.success("Insertion Successful !","Success",{timeOut:3e3}):this.toastr.error("Sorry! couldn't insert","Error!",{timeOut:3e3}),t(e)),e=>(this.spinnerService.hide(),this.toastr.error("Sorry! couldn't insert","Error!",{timeOut:3e3}),i(e)))})}catch(t){return Object(n.rejects)(t)}}getLogMisRuleData(e){try{return new Promise((t,i)=>{this.http.post("api/project/misFunctions/getLogMisRuleData",{id:e}).subscribe(e=>t(e),e=>(this.spinnerService.hide(),i(e)))})}catch(t){return Object(n.rejects)(t)}}getLogGLData(e){try{return new Promise((t,i)=>{this.http.post("api/project/misFunctions/getLogGlAccountData",{id:e}).subscribe(e=>t(e),e=>(this.spinnerService.hide(),i(e)))})}catch(t){return Object(n.rejects)(t)}}updateLogMisRuleData(e){try{return new Promise((t,i)=>{this.http.post("api/project/misFunctions/updateLogMisRuleData",{updateLogMisData:e}).subscribe(e=>t(e),e=>(this.spinnerService.hide(),i(e)))})}catch(t){return Object(n.rejects)(t)}}updateLogGlAccountData(e){try{return new Promise((t,i)=>{this.http.post("api/project/misFunctions/updateLogGlAccountData",{updateLogGlAccountData:e}).subscribe(e=>t(e),e=>(this.spinnerService.hide(),i(e)))})}catch(t){return Object(n.rejects)(t)}}getConfigurationDetails(e){try{return new Promise((t,i)=>{this.http.post("api/project/misFunctions/getFieldConfiguration",{configurationType:e}).subscribe(e=>t(e),e=>(this.spinnerService.hide(),i(e)))})}catch(t){return Object(n.rejects)(t)}}checkForUniqueGLName(e){try{return new Promise((t,i)=>{this.http.post("api/project/misFunctions/getGLNameDuplicates",{glName:e}).subscribe(e=>t(e),e=>(this.spinnerService.hide(),i(e)))})}catch(t){return Object(n.rejects)(t)}}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275inject"](l.c),a["\u0275\u0275inject"](s.c),a["\u0275\u0275inject"](c.c))},e.\u0275prov=a["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},hJL4:function(e,t,i){"use strict";i.d(t,"a",(function(){return u}));var n=i("mrSG"),r=i("XNiG"),o=i("xG9w"),a=i("fXoL"),l=i("tk/3"),s=i("LcQX"),c=i("XXEo"),d=i("flaP");let u=(()=>{class e{constructor(e,t,i,n){this.http=e,this.UtilityService=t,this.loginService=i,this.roleService=n,this.msg=new r.b,this.taskStatusColor=[],this.approveRequest=(e,t)=>this.http.post("/api/isa/request/approveResourceRequest",{requestDetails:e,approverOid:t}),this.rejectRequest=(e,t)=>this.http.post("/api/isa/request/rejectResourceRequest",{requestDetails:e,approverOid:t}),this.getTaskStatusColor()}getAllRequestsOfUser(e,t,i,n,r,o,a){let l=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getAllRequestsOfUser",{oid:e,statusId:t,statusCode:i,objectIds:n,skip:r,limit:o,filterConfig:a,orgIds:l})}getAllRoleAccess(){return o.where(this.roleService.roles,{application_id:139})}getRequestsBasedOnStatus(e,t,i,n,r,o,a){let l=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getRequestsBasedOnStatus",{oid:e,statusId:t,statusCode:i,objectIds:n,skip:r,limit:o,filterConfig:a,orgIds:l})}getRequestsForAwaitingApproval(e,t,i,n){return this.http.post("/api/isa/request/getRequestsForAwaitingApproval",{oid:e,skip:t,limit:i,filterConfig:n})}getObjectIdsBasedOnOid(e){return this.http.post("/api/isa/request/getObjectIdsBasedOnOid",{oid:e})}getStatusCountBasedOnOid(e,t,i,n){let r=this.roleService.getUserRoleOrgCodes("Internal Stakeholder Application");return this.http.post("/api/isa/request/getStatusCountBasedOnOid",{oid:e,objectIds:t,dataTypeArray:i,filterConfig:n,orgIds:r})}getHeaderCount(e){return this.http.post("/api/isa/request/getHeaderCount",{oid:e})}getRequestBasedOnStatus(e,t){return this.http.post("/api/isa/request/getRequestBasedOnStatus",{oid:e,status:t})}getDataTypeArray(){return this.http.post("/api/isa/request/getISAStatusMasterDataUDRF",{})}sendMsg(e){this.msg.next(e)}getMsg(){return this.msg.asObservable()}getTaskStatusColor(){return new Promise((e,t)=>{this.http.post("/api/isa/request/getTaskStatusNameColor",{}).subscribe(t=>(this.taskStatusColor=t,e(t)),e=>{})})}getAllRequestsForRmg(e){return this.http.post("/api/isa/request/getAllRequestsForRmg",{filterConfig:e})}getTotalForRmg(e){return new Promise((t,i)=>{this.http.post("/api/isa/request/getTotalForRmg",{filterConfig:e}).subscribe(e=>t(e),e=>{})})}getDefaultISAStatusToDisplay(){return this.http.post("/api/isa/request/getISADefaultStatusToDisplay",{})}getCTCbreakUpForSkillSet(e){return new Promise((t,i)=>{this.http.post("/api/isa/request/getCTCbreakUpForSkillSet",e).subscribe(e=>t(e.data),e=>{i(e)})})}getCurrentUserRole(){return this.roleService&&this.roleService.roles&&this.roleService.roles.length>0?this.roleService.roles[0].role_id:null}getTemplateForUser(e){return this.http.post("/api/isa/request/getVisibleFormFieldsForUser",e)}saveAndApproveRequest(e){return this.http.post("/api/isa/request/updBudgetInfoByRequestId",e)}getVisibilityMatrix(e){return this.http.post("/api/isa/request/getAuthorzedFieldsByRoleId",e)}getISAAttachmentFromS3(e){return this.http.post("/api/isa/attachment/getISAAttachmentFromS3",{key:e})}getSkillType(){return this.http.post("/api/isa/request/getSkillType",{})}getSkillLevel(){return this.http.post("/api/isa/request/getSkillLevel",{})}getSkillExperience(){return this.http.post("/api/isa/request/getSkillExperience",{})}getSkillName(e){return this.http.post("/api/isa/request/getSkillName",{skill_type_id:e})}getIsaActivityReportDownload(){return this.http.post("/api/isa/request/getIsaActivityReportDownload",{})}getISASLAreport(e){return this.http.post("/api/isa/request/getISASLAreport",{filterConfig:e})}getISACustomReport(e){return this.http.post("/api/isa/request/getISACustomReport",{filterConfig:e})}getPositionType(){return this.http.post("/api/employee360/masterData/getPositionType",{})}calculateNetCostBasedOnPosition(e,t,i,r,a,l,s){return Object(n.c)(this,void 0,void 0,(function*(){let n;n=l&&l.length>1&&(yield this.getManpowerCostByOId(l,i,a,2))||(yield this.getManpowerCostBasedOnPosition(e,t,i,a,s));let c=yield this.getNonManpowerCost(t,i,r,a,2),d=yield this.getAllocatedCost(),u=0;u=(n?n.cost:0)+c.length>0?o.reduce(o.pluck(c,"cost"),(e,t)=>e+t,0):0;let m=d.length>0?o.reduce(o.pluck(d,"percentage"),(e,t)=>e+t,0):0;return{cost:u,currency:n&&n.currency_code?n.currency_code:"",manpowerCost:n,nonManpowerCost:c,allocatedCost:d,allocatedCostValue:u*(m/100)}}))}getManpowerCostBasedOnPosition(e,t,i,n,r){return new Promise((o,a)=>{this.http.post("/api/isa/configuration/getManpowerCostBasedOnPosition",{skillRoleId:e,nationalityId:t,locationGroupId:i,unit:n,position:r}).subscribe(e=>o(e),e=>(console.log(e),a(e)))})}getNonManpowerCost(e,t,i,n,r){return new Promise((o,a)=>{this.http.post("/api/project/getNonManpowerCost",{nationalityId:e,locationGroupId:t,locationId:i,unit:n,currency_id:r}).subscribe(e=>o(e),e=>(console.log(e),a(e)))})}getAllocatedCost(){return new Promise((e,t)=>{this.http.post("/api/project/getAllocatedCost","").subscribe(t=>e(t),e=>(console.log(e),t(e)))})}getManpowerCostByOId(e,t,i,n){return new Promise((r,o)=>{this.http.post("/api/project/getEmployeeSalary",{oid:e,location_group_id:t,unit_id:i,currency_id:n}).subscribe(e=>r(e),e=>(console.log(e),o(e)))})}getSkillNameMaster(){return this.http.post("/api/isa/configuration/getSkillName",{})}getVendorOid(e,t){return this.http.post("/api/isa/request/getVendorOid",{vendorNames:e,docID:t})}getVendorList(e,t){return this.http.post("/api/isa/request/getVendorList",{documentID:e,limitval:t})}removeVendor(e,t){return this.http.post("/api/isa/request/removeVendors",{vendorOid:e,documentID:t})}checkVendorOrNot(){return this.http.post("/api/isa/request/checkVendorOrNot",{})}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275inject"](l.c),a["\u0275\u0275inject"](s.a),a["\u0275\u0275inject"](c.a),a["\u0275\u0275inject"](d.a))},e.\u0275prov=a["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()},"ttK/":function(e,t,i){"use strict";i.r(t),i.d(t,"MisConfigurationsModule",(function(){return He}));var n=i("ofXK"),r=i("tyNb"),o=i("mrSG"),a=i("XNiG"),l=i("1G5W"),s=i("fXoL"),c=i("GnQ3"),d=i("RRmA"),u=i("BVzC"),m=i("JqCM"),h=i("rDax"),p=i("LcQX"),f=i("0IaG"),g=i("dNgK"),_=i("HmYF"),v=i("xi/V"),b=i("Wk3H");let C=(()=>{class e{constructor(e,t,i,n,r,o,l,s,c,d,u){this._udrfService=e,this._misService=t,this.errorService=i,this.spinnerService=n,this.overlay=r,this.compiler=o,this.router=l,this._utilityService=s,this.dialog=c,this.snackBar=d,this._excelService=u,this.applicationId=523,this.misItemDataCurrentIndex=0,this.$onDestroy=new a.b,this.$onAppApiCalled=new a.b,this.isSideNavLoading=!0,this.dummySideNavData=Array.from({length:15},(e,t)=>t+1),this.sideNavData=[],this.searchParamAfterUpdate=null,this.misFields=null,this.tableConfiguration=null,this.udrfBodyColumns=[{item:"id",header:"Id",isActive:!0,isVisible:"true",type:"text",textClass:"value13Bold cp colorRed",position:1,colSize:3,sortOrder:"N",width:150},{item:"entity_name",header:"Entity Name",isActive:!0,isVisible:"true",type:"text",textClass:"value13Bold cp colorRed",position:1,colSize:3,sortOrder:"N",width:240},{item:"entity_code",header:"Entity Code",isActive:!0,isVisible:"true",type:"text",textClass:"value13Bold",position:2,colSize:2,sortOrder:"N",width:150},{item:"gl_code",header:"GL Code",isActive:!0,isVisible:"true",type:"text",textClass:"value13Bold cp colorRed",position:1,colSize:3,sortOrder:"N",width:150},{item:"gl_description",header:"GL Description",isActive:!0,isVisible:"true",type:"text",textClass:"value13light",position:4,colSize:3,sortOrder:"N",width:240},{item:"rule_name",header:"Rule code",isActive:!0,isVisible:"true",type:"text",textClass:"value13light",position:4,colSize:3,sortOrder:"N",width:180},{item:"cost_center_value",header:"Cost Center",isActive:!0,isVisible:"true",type:"text",textClass:"value13light",position:4,colSize:3,sortOrder:"N",width:240},{item:"determine_distribution_value",header:"Deteremine distribution",isActive:!0,isVisible:"true",type:"text",textClass:"value13light",position:4,colSize:3,sortOrder:"N",width:180},{item:"distribution_p_and_l_value",header:"Distribution P && L",isActive:!0,isVisible:"true",type:"text",textClass:"value13light",position:4,colSize:3,sortOrder:"N",width:240},{item:"distribution_item_value",header:"Distribution ItemId",isActive:!0,isVisible:"true",type:"text",textClass:"value13light",position:4,colSize:3,sortOrder:"N",width:240},{item:"splitup_cost_center_value",header:"Splitup Cost Center",isActive:!0,isVisible:"true",type:"text",textClass:"value13light",position:4,colSize:3,sortOrder:"N",width:240},{item:"distribution_statistical_grouping_id",header:"Distribution Statistical Grouping_ ID",isActive:!0,isVisible:"true",type:"text",textClass:"value13light",position:4,colSize:3,sortOrder:"N",width:240},{item:"alloc_member",header:"Alloc member",isActive:!0,isVisible:"true",type:"text",textClass:"value13light",position:4,colSize:3,sortOrder:"N",width:240},{item:"posting_name",header:"Posting Type",isActive:!0,isVisible:"true",type:"text",textClass:"value13light",position:4,colSize:3,sortOrder:"N",width:240},{item:"manual_cost_center_and_percentage",header:"Manual percentage",isActive:!0,isVisible:"true",type:"text",textClass:"value13light",position:4,colSize:3,sortOrder:"N",width:240},{item:"location_id_value",header:"Location Id",isActive:!0,isVisible:"true",type:"text",textClass:"value13light",position:4,colSize:3,sortOrder:"N",width:240}],this.udrfItemStatusColor=[{status:"Billed",color:"#ffb142"},{status:"Legal",color:"#cf0001"},{status:"Partial Payment",color:"rgb(0, 230, 77)"}]}ngOnInit(){return Object(o.c)(this,void 0,void 0,(function*(){this._misService.$searchRetain.subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){yield this.initUdrf(),this.searchParamAfterUpdate=e}))),yield this.initUdrf()}))}initUdrf(){return this.selectedItemId=1,this._udrfService.getNotifyReleasesUDRF(),this.misItemDataCurrentIndex=0,this._udrfService.udrfBodyData=[],this._udrfService.udrfData.applicationId=this.applicationId,this._udrfService.udrfUiData.showNewReleasesButton=!0,this._udrfService.udrfUiData.showItemDataCount=!0,this._udrfService.udrfUiData.itemDataType="",this._udrfService.udrfUiData.totalItemDataCount=0,this._udrfService.udrfUiData.showSearchBar=!0,this._udrfService.udrfUiData.showActionButtons=!0,this._udrfService.udrfUiData.showUdrfModalButton=!0,this._udrfService.udrfUiData.showSettingsModalButton=!1,this._udrfService.udrfUiData.showCreateNewComponentButton=!0,this._udrfService.udrfUiData.isReportDownloading=!1,this._udrfService.udrfUiData.showColumnConfigButton=!0,this._udrfService.udrfUiData.udrfItemStatusColor=this.udrfItemStatusColor,this._udrfService.udrfUiData.itemcardSelected=this.itemCardClicked.bind(this),this._udrfService.udrfUiData.summaryCardsItem={},this._udrfService.udrfUiData.showHierarchyData={},this._udrfService.udrfUiData.inlineEditData={},this._udrfService.udrfUiData.openCommentsData={},this._udrfService.udrfUiData.itemDataScrollDown=this.onCollectionItemDataScrollDown.bind(this),this._udrfService.udrfUiData.udrfBodyColumns=this.udrfBodyColumns,this._udrfService.udrfUiData.udrfVisibleBodyColumns=this._udrfService.udrfUiData.udrfVisibleBodyColumns,this._udrfService.udrfUiData.udrfInvisibleBodyColumns=this._udrfService.udrfUiData.udrfInvisibleBodyColumns,this._udrfService.udrfUiData.variant=0,this._udrfService.udrfUiData.itemHasQuickCta=!1,this._udrfService.udrfUiData.itemHasComments=!1,this._udrfService.udrfUiData.itemHasHierarchyView=!1,this._udrfService.udrfUiData.itemHasInfoButton=!1,this._udrfService.udrfUiData.itemHasMoreActions=!1,this._udrfService.udrfUiData.showCollapseButton=!1,this._udrfService.udrfUiData.horizontalScroll=!0,this._udrfService.udrfUiData.showGroupByButton=!1,this._udrfService.getAppUdrfConfig(this.applicationId,this.initReport.bind(this)),this._udrfService.getNotifyReleasesUDRF(),this._udrfService.udrfUiData.ghostButtonUI=!1,this._udrfService.udrfUiData.isMoreOptionsNeeded=!0,this._udrfService.udrfUiData.createNewComponent=this.addNewConfigurationRow.bind(this),Promise.resolve()}getFieldData(){return Object(o.c)(this,void 0,void 0,(function*(){try{let e=null;null==this.tableConfiguration&&(yield this.getConfigurationDetails()),e=JSON.parse(this.tableConfiguration);for(let t=0;t<e.length;t++)"text"!=e[t].field_type&&"toggle-visibility"!=e[t].field_type&&null!=this.misFields&&(e[t].options=this.misFields[t].options);return this.misFields=e,Promise.resolve()}catch(e){}}))}listenToParent(){for(let e=0;e<this.misFields.length;e++)if(null!=this.misFields[e].parent_dependent&&null!=this.misFields[e].no_to_retrieve){this.misFields[e].is_visible=1,this.misFields[e].is_required="true";let t=this.misFields[e].parent_dependent,i=this.misFields.filter(e=>e.field_name==t);if(null!=i[0].choises)for(let n=0;n<i[0].choises.length;n++){let t=this.misFields.filter(e=>e.field_name==i[0].choises[n]);0!=t.length&&t.length>0&&t[0].field_name!=this.misFields[e].field_name&&(t[0].is_visible=0,t[0].is_required=!1)}}}getConfigurationDetails(){return Object(o.c)(this,void 0,void 0,(function*(){let e=yield this._misService.getConfigurationDetails("rule_sheet");return this.tableConfiguration=JSON.stringify(e.data),Promise.resolve()}))}openEditConfigurationDialog(){return Object(o.c)(this,void 0,void 0,(function*(){this.spinnerService.show();let e=null;null==this.tableConfiguration&&(yield this.getConfigurationDetails()),e=JSON.parse(this.tableConfiguration);for(let i=0;i<e.length;i++){if(this.fieldOption=null,"drop-down-infinte-search"==e[i].field_type||"drop-down-multiple"==e[i].field_type){if(null!=this.ruleTableDetails[e[i].selected_value_label]&&(this.fieldOption=this.ruleTableDetails[e[i].selected_value_label]),this.ruleTableDetails[e[i].selected_value_label]&&(e[i].selected_value=null!=this.ruleTableDetails[e[i].selected_value_label]?this.ruleTableDetails[e[i].selected_value_label].map(e=>e.id):null),"drop-down-infinte-search"==e[i].field_type&&(e[i].selected_value_pair=this.fieldOption),"true"==e[i].id_value_pair&&(e[i].id_description=[],null!=e[i].selected_value_pair&&e[i].selected_value_pair.forEach(t=>{e[i].id_description.push(t.id+"-"+t.name)})),""!=this.fieldOption&&null!=this.fieldOption){let t=this.fieldOption.map(e=>e.id);e[i].no_to_retrieve=t}if(null==this.misFields||null==this.misFields[i].options||this.misFields[i].options.length<15){let t=null;t="true"==e[i].id_value_pair?yield this._misService.getMasterEdit(e[i].field_name,e[i].scroll_index,e[i].id_description,null):yield this._misService.getMasterEdit(e[i].field_name,e[i].scroll_index,e[i].no_to_retrieve,null),""!=this.fieldOption&&null!=this.fieldOption?(e[i].options=this.fieldOption,e[i].options=e[i].options.concat(t.data)):e[i].options=t.data}else{let t=[];if(null!=this.fieldOption){let n=this.fieldOption.map(e=>e.id);this.misFields[i].options.filter(e=>{n.includes(e.id)||t.push(e)}),e[i].options=this.fieldOption,e[i].options=e[i].options.concat(t)}}}else if("drop-down-single-select-search"==e[i].field_type)if(e[i].options=[],e[i].options.push(this.ruleTableDetails.gl),e[i].no_to_retrieve=this.ruleTableDetails[e[i].field_name],e[i].selected_value=null!=this.ruleTableDetails[e[i].selected_value_label]?this.ruleTableDetails[e[i].selected_value_label]:null,null==this.misFields||null==this.misFields[i].options){let t=yield this._misService.getMasterEdit(e[i].field_name,e[i].scroll_index,e[i].no_to_retrieve,null);e[i].options=e[i].options.concat(t.data)}else e[i].options=this.misFields[i].options;else if("drop-down"==e[i].field_type){if(null==this.misFields||null==this.misFields[i].options){let t=yield this._misService.getMasterEdit(e[i].field_name,e[i].scroll_index,null,null);e[i].options=t.data}else e[i].options=this.misFields[i].options;e[i].no_to_retrieve=this.ruleTableDetails[e[i].field_name],e[i].selected_value=null!=this.ruleTableDetails[e[i].selected_value_label]?this.ruleTableDetails[e[i].selected_value_label]:null}else"text"==e[i].field_type&&(e[i].no_to_retrieve=this.ruleTableDetails[e[i].field_name]);null==e[i].options&&null!=this.misFields&&(e[i].options=this.misFields[i].options)}this.misFields=e,this.listenToParent(),this.spinnerService.hide();let{EditRuleSheetComponent:t}=yield Promise.resolve().then(i.bind(null,"QbZZ"));this.dialog.open(t,{height:"100vh",width:"60vw",position:{right:"0px"},data:{ruleTableDetails:this.ruleTableDetails,editForm:!0,misFieldData:this.misFields,searchParam:this._udrfService.udrfData.mainSearchParameter,table:"ruleSheet"}})}))}addNewConfigurationRow(){return Object(o.c)(this,void 0,void 0,(function*(){this.spinnerService.show(),this.isEntityDiabled="false",yield this.getFieldData();for(let t=0;t<this.misFields.length;t++)if(this.misFields[t].is_disabled=!1,"text"!=this.misFields[t].field_type&&"toggle-visibility"!=this.misFields[t].field_type&&null==this.misFields[t].options){let e=yield this._misService.getMasterEdit(this.misFields[t].field_name,this.misFields[t].scroll_index,null,null);this.misFields[t].options=e.data}this.ruleTableDetails=null,this.spinnerService.hide();let{EditRuleSheetComponent:e}=yield Promise.resolve().then(i.bind(null,"QbZZ"));this.dialog.open(e,{height:"90vh",width:"40vw",data:{ruleTableDetails:this.ruleTableDetails,misFieldData:this.misFields,editForm:!1,searchParam:this._udrfService.udrfData.mainSearchParameter,table:"ruleSheet"}})}))}initReport(){return Object(o.c)(this,void 0,void 0,(function*(){this.$onAppApiCalled.next(),this.misItemDataCurrentIndex=0,this._udrfService.udrfBodyData=[],this.selectedItemId=1,this._udrfService.udrfUiData.resolveColumnConfig(),this.getRuleDataList()}))}getMisRuleCount(){return Object(o.c)(this,void 0,void 0,(function*(){let e=JSON.parse(JSON.stringify(this._udrfService.udrfData.mainFilterArray));this._misService.getMISTotalCount({startIndex:this.misItemDataCurrentIndex,mainFilterArray:e,txTableDetails:this._udrfService.udrfData.txTableDetails,mainSearchParameter:this._udrfService.udrfData.mainSearchParameter,searchTableDetails:this._udrfService.udrfData.searchTableDetails}).pipe(Object(l.a)(this.$onDestroy)).subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){"S"==e.messType&&(this._udrfService.udrfUiData.totalItemDataCount=e.data[0].records_found)})),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while retrieving MIS data",e&&e.params?e.params:e&&e.error?e.error.params:{})})}))}getRuleDataList(){return Object(o.c)(this,void 0,void 0,(function*(){if(null!=this.searchParamAfterUpdate){let e=JSON.parse(JSON.stringify(this._udrfService.udrfData.mainFilterArray));this.filterConfig={startIndex:this.misItemDataCurrentIndex,mainFilterArray:e,txTableDetails:this._udrfService.udrfData.txTableDetails,mainSearchParameter:this.searchParamAfterUpdate,searchTableDetails:this._udrfService.udrfData.searchTableDetails}}else{let e=JSON.parse(JSON.stringify(this._udrfService.udrfData.mainFilterArray));this.filterConfig={startIndex:this.misItemDataCurrentIndex,mainFilterArray:e,txTableDetails:this._udrfService.udrfData.txTableDetails,mainSearchParameter:this._udrfService.udrfData.mainSearchParameter,searchTableDetails:this._udrfService.udrfData.searchTableDetails}}this.$misRuleDataSubscription&&this.$misRuleDataSubscription.unsubscribe(),this.$misRuleDataSubscription=this._misService.getMISList(this.filterConfig).pipe(Object(l.a)(this.$onDestroy)).pipe(Object(l.a)(this.$onAppApiCalled)).subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){this.configurationDetail=e.data;for(let e=0;e<this.configurationDetail.length;e++){if(null!=this.configurationDetail[e].cost_center){let t=JSON.parse(this.configurationDetail[e].cost_center);this.configurationDetail[e].cost_center=t,this.configurationDetail[e].cost_center_value=t.toString()}if(null!=this.configurationDetail[e].splitup_cost_center){let t=JSON.parse(this.configurationDetail[e].splitup_cost_center);this.configurationDetail[e].splitup_cost_center=t,this.configurationDetail[e].splitup_cost_center_value=t.toString()}null!=this.configurationDetail[e].gl_code&&(this.configurationDetail[e].gl={id:this.configurationDetail[e].gl_code,name:this.configurationDetail[e].gl_description}),null!=this.configurationDetail[e].manual_percentage&&(this.configurationDetail[e].manual_cost_center_and_percentage=JSON.parse(this.configurationDetail[e].manual_percentage).map(e=>e.percentage+"%-"+e.id),this.configurationDetail[e].manual_percentage=JSON.parse(this.configurationDetail[e].manual_percentage)),null!=this.configurationDetail[e].determine_distribution&&(1==this.configurationDetail[e].determine_distribution?this.configurationDetail[e].determine_distribution_value="Distribution P and L":2==this.configurationDetail[e].determine_distribution?this.configurationDetail[e].determine_distribution_value="Distribution Item":3==this.configurationDetail[e].determine_distribution&&(this.configurationDetail[e].determine_distribution_value="Distribution Statistical Grouping Id")),null!=this.configurationDetail[e].distribution_p_and_l&&(this.configurationDetail[e].distribution_p_and_l_value=this.configurationDetail[e].p_and_l.map(e=>e.name),this.configurationDetail[e].distribution_p_and_l=JSON.parse(this.configurationDetail[e].distribution_p_and_l)),null!=this.configurationDetail[e].distribution_item_id&&(this.configurationDetail[e].distribution_item_value=this.configurationDetail[e].distribution_item_name.map(e=>e.name),this.configurationDetail[e].distribution_item_id=JSON.parse(this.configurationDetail[e].distribution_item_id)),null!=this.configurationDetail[e].distribution_statistical_grouping_id&&(this.configurationDetail[e].distribution_statistical_grouping_value=this.configurationDetail[e].distribution_statistical_grouping_name.map(e=>e.name),this.configurationDetail[e].distribution_statistical_grouping_id=JSON.parse(this.configurationDetail[e].distribution_statistical_grouping_id)),null!=this.configurationDetail[e].location_id&&(this.configurationDetail[e].location_id_value=this.configurationDetail[e].location_name.map(e=>e.name),this.configurationDetail[e].location_id=JSON.parse(this.configurationDetail[e].location_id))}"S"==e.messType&&e.data&&e.data.length>0?(this._udrfService.udrfBodyData=this._udrfService.udrfBodyData.concat(e.data),this.getMisRuleCount()):(this._udrfService.udrfBodyData=this._udrfService.udrfBodyData.concat([]),this._udrfService.udrfUiData.totalItemDataCount=0,this._udrfService.udrfData.noItemDataFound=!0),this._udrfService.udrfData.isItemDataLoading=!1})),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while retrieving Invoice data",e&&e.params?e.params:e&&e.error?e.error.params:{})}),this.searchParamAfterUpdate=null}))}onCollectionItemDataScrollDown(){return Object(o.c)(this,void 0,void 0,(function*(){this._udrfService.udrfData.noItemDataFound||this._udrfService.udrfData.isItemDataLoading||(this.misItemDataCurrentIndex+=this._udrfService.udrfData.defaultRecordsPerFetch,this._udrfService.udrfData.isItemDataLoading=!0,this._udrfService.udrfData.appliedConfig.activeView.length>0?this._udrfService.udrfData.appliedConfig.activeView[0].groupByView&&(this._udrfService.groupBy=!0):this.getRuleDataList())}))}itemCardClicked(){return Object(o.c)(this,void 0,void 0,(function*(){this.ruleTableDetails=this._udrfService.udrfUiData.itemCardSelecteditem,this.openEditConfigurationDialog()}))}encodeURIComponent(e){return encodeURIComponent(e).replace(/[!'()*]/g,(function(e){return"%"+e.charCodeAt(0).toString(16)}))}ngOnDestroy(){this.$onDestroy.next(),this.$onDestroy.complete(),this._udrfService.resetUdrfData(),this.$misRuleDataSubscription&&this.$misRuleDataSubscription.unsubscribe(),this.$misRuleDataTotalSubscription&&this.$misRuleDataTotalSubscription.unsubscribe(),this._misService.$determineRuleCode.unsubscribe(),this._misService.misSearchData.unsubscribe(),this._misService.searchParamReload.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275directiveInject"](c.a),s["\u0275\u0275directiveInject"](d.a),s["\u0275\u0275directiveInject"](u.a),s["\u0275\u0275directiveInject"](m.c),s["\u0275\u0275directiveInject"](h.e),s["\u0275\u0275directiveInject"](s.Compiler),s["\u0275\u0275directiveInject"](r.g),s["\u0275\u0275directiveInject"](p.a),s["\u0275\u0275directiveInject"](f.b),s["\u0275\u0275directiveInject"](g.a),s["\u0275\u0275directiveInject"](_.a))},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-rule-sheet-landing-page"]],decls:4,vars:0,consts:[[1,"container-fluid","rule-sheet","pl-0","pr-0"],["size","medium","type","ball-clip-rotate","bdColor","rgba(236, 233, 233, 0.8)","color","#cf0001"]],template:function(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"div",0),s["\u0275\u0275element"](1,"udrf-header"),s["\u0275\u0275element"](2,"udrf-body"),s["\u0275\u0275element"](3,"ngx-spinner",1),s["\u0275\u0275elementEnd"]())},directives:[v.a,b.a,m.a],styles:[".rule-sheet[_ngcontent-%COMP%]{overflow:hidden!important;height:83vh}.rule-sheet[_ngcontent-%COMP%]   .status-circular[_ngcontent-%COMP%]{height:13px;width:13px;margin-top:4px;border-radius:50%;box-shadow:0 2px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.rule-sheet[_ngcontent-%COMP%]     .mat-form-field{width:30vw!important;padding-top:25px}.rule-sheet[_ngcontent-%COMP%]     .search-bar{padding-left:0!important}.rule-sheet[_ngcontent-%COMP%]     .udrf-header-card{background-color:#fafafa!important}.rule-sheet[_ngcontent-%COMP%]     owl-carousel-o{max-width:65vw!important}.rule-sheet[_ngcontent-%COMP%]     .owl-item{max-width:15.5vw!important}.rule-sheet[_ngcontent-%COMP%]     .owl-item .mat-select-item{width:15vw!important}.rule-sheet[_ngcontent-%COMP%]   .spinner-align[_ngcontent-%COMP%]{margin:auto;display:inline-block}.rule-sheet[_ngcontent-%COMP%]   .infinite-scroll-fixed[_ngcontent-%COMP%]{height:425px;overflow-y:scroll}.rule-sheet[_ngcontent-%COMP%]   .infinite-scroll-fixed-shortened[_ngcontent-%COMP%]{height:435px;overflow-y:scroll}.rule-sheet[_ngcontent-%COMP%]   .ta-r[_ngcontent-%COMP%]{text-align:right!important}.rule-sheet[_ngcontent-%COMP%]   .ta-l[_ngcontent-%COMP%]{text-align:left}.rule-sheet[_ngcontent-%COMP%]   .is-normal-probability-text[_ngcontent-%COMP%]{color:grey!important}.rule-sheet[_ngcontent-%COMP%]   .is-low-probability-text[_ngcontent-%COMP%]{color:#cf0001!important}.rule-sheet[_ngcontent-%COMP%]   .is-moderate-probability-text[_ngcontent-%COMP%]{color:#ff7200!important}.rule-sheet[_ngcontent-%COMP%]   .is-high-probability-text[_ngcontent-%COMP%]{color:#009432!important}.rule-sheet[_ngcontent-%COMP%]   .is-normal-probability[_ngcontent-%COMP%]{border-left-color:grey!important}.rule-sheet[_ngcontent-%COMP%]   .is-low-probability[_ngcontent-%COMP%]{border-left-color:#cf0001!important}.rule-sheet[_ngcontent-%COMP%]   .is-moderate-probability[_ngcontent-%COMP%]{border-left-color:#ff7200!important}.rule-sheet[_ngcontent-%COMP%]   .is-high-probability[_ngcontent-%COMP%]{border-left-color:#009432!important}.rule-sheet[_ngcontent-%COMP%]   .value13Bold[_ngcontent-%COMP%], .rule-sheet[_ngcontent-%COMP%]   .value14Bold[_ngcontent-%COMP%]{color:#000;display:inline}.rule-sheet[_ngcontent-%COMP%]   .value14Bold[_ngcontent-%COMP%]{font-size:14px!important;font-weight:500!important;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.rule-sheet[_ngcontent-%COMP%]   .listcard[_ngcontent-%COMP%]{transition:all .25s linear;height:auto}.rule-sheet[_ngcontent-%COMP%]   .smallSubtleText[_ngcontent-%COMP%]{color:#66615b;font-size:11px}.rule-sheet[_ngcontent-%COMP%]   .data-type-card[_ngcontent-%COMP%]{min-height:85px;transition:all .3s linear}.rule-sheet[_ngcontent-%COMP%]   .data-type-card-is-active[_ngcontent-%COMP%]{border-color:#cf0001;background-color:#fff2f2}.rule-sheet[_ngcontent-%COMP%]   .headingBold[_ngcontent-%COMP%]{color:#1a1a1a;font-weight:600;font-size:22px}.rule-sheet[_ngcontent-%COMP%]   .valueGrey14[_ngcontent-%COMP%]{font-size:14px;font-weight:300}.rule-sheet[_ngcontent-%COMP%]   .valueGrey12Normal[_ngcontent-%COMP%], .rule-sheet[_ngcontent-%COMP%]   .valueGrey14[_ngcontent-%COMP%]{color:#4a4a4a;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.rule-sheet[_ngcontent-%COMP%]   .valueGrey12Normal[_ngcontent-%COMP%]{font-size:12px;font-weight:400}.rule-sheet[_ngcontent-%COMP%]   .cp[_ngcontent-%COMP%]{cursor:pointer!important}.rule-sheet[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]{margin-top:5px!important;line-height:8px;width:26px;height:26px;margin-right:10px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12);animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}.rule-sheet[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%], .rule-sheet[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.rule-sheet[_ngcontent-%COMP%]   .ta-c[_ngcontent-%COMP%]{text-align:center;padding-left:0!important;padding-right:0!important}.rule-sheet[_ngcontent-%COMP%]   .divAlignItemCenter[_ngcontent-%COMP%]{display:flex;align-items:center}.rule-sheet[_ngcontent-%COMP%]   .menu-icons[_ngcontent-%COMP%]{font-size:4px}.rule-sheet[_ngcontent-%COMP%]   .example-container[_ngcontent-%COMP%]{width:400px;max-width:100%;margin:0 25px 25px 0;display:inline-block;vertical-align:top}.rule-sheet[_ngcontent-%COMP%]   .example-list[_ngcontent-%COMP%]{border:1px solid #ccc;min-height:60px;background:#fff;border-radius:4px;overflow:hidden;display:block}.rule-sheet[_ngcontent-%COMP%]     .example-box{border-bottom:1px solid #ccc;color:rgba(0,0,0,.87);display:flex;flex-direction:row;align-items:left;justify-content:space-between;box-sizing:border-box;cursor:move;background:#fff;font-size:4px}.rule-sheet[_ngcontent-%COMP%]     .menu-list{cursor:move;font-size:8px}.rule-sheet[_ngcontent-%COMP%]   .cdk-drag-preview[_ngcontent-%COMP%]{box-sizing:border-box;border-radius:4px;box-shadow:0 5px 5px -3px rgba(0,0,0,.2),0 8px 10px 1px rgba(0,0,0,.14),0 3px 14px 2px rgba(0,0,0,.12)}.rule-sheet[_ngcontent-%COMP%]   .cdk-drag-placeholder[_ngcontent-%COMP%]{opacity:0}.rule-sheet[_ngcontent-%COMP%]   .cdk-drag-animating[_ngcontent-%COMP%]{transition:transform .25s cubic-bezier(0,0,.2,1)}.rule-sheet[_ngcontent-%COMP%]   .example-box[_ngcontent-%COMP%]:last-child{border:none}.rule-sheet[_ngcontent-%COMP%]   .example-list.cdk-drop-list-dragging[_ngcontent-%COMP%]   .example-box[_ngcontent-%COMP%]:not(.cdk-drag-placeholder){transition:transform .25s cubic-bezier(0,0,.2,1)}.rule-sheet[_ngcontent-%COMP%]   .sr-nav-item[_ngcontent-%COMP%]{background-color:#f5f5f5!important;font-weight:500;border-left:solid #545352;border-radius:2px;transition:.3s;box-shadow:0 4px 8px 0 rgba(0,0,0,.2),0 6px 20px 0 rgba(0,0,0,.19)}.rule-sheet[_ngcontent-%COMP%]   .r-nav-item[_ngcontent-%COMP%]{font-weight:400;cursor:pointer}.rule-sheet[_ngcontent-%COMP%]   .r-nav-item[_ngcontent-%COMP%]:hover{background-color:#f5f5f5!important;font-weight:500;border-left:solid #545352;border-radius:2px;transition:box-shadow .3s;box-shadow:0 4px 8px 0 rgba(0,0,0,.2),0 6px 20px 0 rgba(0,0,0,.19)}.rule-sheet[_ngcontent-%COMP%]   .edit-nav[_ngcontent-%COMP%]{width:15.5vw;overflow-y:scroll;height:83vh;overflow-x:hidden!important;background-color:#fff;color:#000}.rule-sheet[_ngcontent-%COMP%]   .value13light[_ngcontent-%COMP%]{color:#4e4c4c;font-weight:400!important;display:inline}.rule-sheet[_ngcontent-%COMP%]   .value13Bold[_ngcontent-%COMP%], .rule-sheet[_ngcontent-%COMP%]   .value13light[_ngcontent-%COMP%]{font-size:13px!important;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.rule-sheet[_ngcontent-%COMP%]   .value13Bold[_ngcontent-%COMP%]{color:#cf0001;font-weight:500!important}.rule-sheet[_ngcontent-%COMP%]   .shimmer[_ngcontent-%COMP%]{background:#f6f7f8;background-image:linear-gradient(90deg,#f6f7f8 0,#edeef1 20%,#f6f7f8 100%,#f6f7f8 0);background-repeat:no-repeat;background-size:800px 400px;display:inline-block;position:relative}.rule-sheet[_ngcontent-%COMP%]   .sm-lines[_ngcontent-%COMP%]{height:30px;margin-top:10px;width:100px}@keyframes placeholderShimmer{0%{background-position:-468px 0}to{background-position:468px 0}}.rule-sheet[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}.mat-mini-fab[_ngcontent-%COMP%]{height:26px;width:27px}  .mat-mini-fab .mat-button-wrapper{padding:0!important;display:inline-block;line-height:0!important}#spinner[_ngcontent-%COMP%]{animation:frames 1s linear infinite;background:transparent;border-radius:100%;border:1.75vw solid #fff;border-top-color:#df691a;width:20vw;height:20vw;opacity:.6;padding:0;position:absolute;z-index:999}@keyframes frames{0%{transform:rotate(0deg)}to{transform:rotate(359deg)}}#pause[_ngcontent-%COMP%]{display:block;background:rgba(0,0,0,.66) no-repeat 0 0;width:100%;height:100%;position:fixed;bottom:0;left:0;z-index:1000}"]}),e})(),y=(()=>{class e{constructor(e,t,i,n,r,o,l,s,c,d,u){this._udrfService=e,this._misService=t,this.errorService=i,this.spinnerService=n,this.overlay=r,this.compiler=o,this.router=l,this._utilityService=s,this.dialog=c,this.snackBar=d,this._excelService=u,this.applicationId=333,this.misItemDataCurrentIndex=0,this.$onDestroy=new a.b,this.$onAppApiCalled=new a.b,this.isSideNavLoading=!0,this.dummySideNavData=Array.from({length:15},(e,t)=>t+1),this.sideNavData=[],this.udrfBodyColumns=[{item:"id",header:"Id",isActive:!0,isVisible:"true",type:"text",textClass:"value13Bold cp colorRed",position:1,colSize:3,sortOrder:"N",width:150},{item:"gl_code",header:"GL Code",isActive:!0,isVisible:"true",type:"text",textClass:"value13Bold cp colorRed",position:1,colSize:3,sortOrder:"N",width:150},{item:"gl_name",header:"GL Name",isActive:!0,isVisible:"true",type:"text",textClass:"value13Bold",position:2,colSize:2,sortOrder:"N",width:240},{item:"gl_type",header:"GL Type",isActive:!0,isVisible:"true",type:"text",textClass:"value13light",position:4,colSize:3,sortOrder:"N",width:240},{item:"cost_id",header:"Cost Id",isActive:!0,isVisible:"true",type:"text",textClass:"value13light",position:4,colSize:3,sortOrder:"N",width:150},{item:"cost_type",header:"Cost Type",isActive:!0,isVisible:"true",type:"text",textClass:"value13light",position:4,colSize:3,sortOrder:"N",width:240},{item:"salary_column_names_value",header:"Salary Column Names",isActive:!0,isVisible:"true",type:"text",textClass:"value13light",position:4,colSize:3,sortOrder:"N",width:240},{item:"entity_name",header:"GL Entity Name",isActive:!0,isVisible:"true",type:"text",textClass:"value13light",position:4,colSize:3,sortOrder:"N",width:240},{item:"pl_description",header:"P and L",isActive:!0,isVisible:"true",type:"text",textClass:"value13light",position:4,colSize:3,sortOrder:"N",width:240},{item:"is_cc_split_up_allowed",header:"Is CC Split Up Allowed",isActive:!0,isVisible:"true",type:"text",textClass:"value13light",position:4,colSize:3,sortOrder:"N",width:240},{item:"gl_direct_exception_name",header:"GL Direct Exception",isActive:!0,isVisible:"true",type:"text",textClass:"value13light",position:4,colSize:3,sortOrder:"N",width:240},{item:"gl_default_cost_center",header:"GL Default Cost Center",isActive:!0,isVisible:"true",type:"text",textClass:"value13light",position:4,colSize:3,sortOrder:"N",width:240}],this.searchParamAfterUpdate=null,this.tableConfiguration=null,this.isLoading=!1,this.typeSelected="ball-fussion"}ngOnInit(){return Object(o.c)(this,void 0,void 0,(function*(){this._misService.$searchRetain.subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){yield this.initUdrf(),this.searchParamAfterUpdate=e}))),yield this.initUdrf()}))}initUdrf(){return this.selectedItemId=1,this._udrfService.getNotifyReleasesUDRF(),this.misItemDataCurrentIndex=0,this._udrfService.udrfBodyData=[],this._udrfService.udrfData.applicationId=this.applicationId,this._udrfService.udrfUiData.showNewReleasesButton=!0,this._udrfService.udrfUiData.showItemDataCount=!0,this._udrfService.udrfUiData.itemDataType="",this._udrfService.udrfUiData.totalItemDataCount=0,this._udrfService.udrfUiData.showSearchBar=!0,this._udrfService.udrfUiData.showActionButtons=!0,this._udrfService.udrfUiData.showUdrfModalButton=!0,this._udrfService.udrfUiData.showCreateNewComponentButton=!0,this._udrfService.udrfUiData.showSettingsModalButton=!1,this._udrfService.udrfUiData.isReportDownloading=!1,this._udrfService.udrfUiData.showColumnConfigButton=!0,this._udrfService.udrfUiData.itemcardSelected=this.itemCardClicked.bind(this),this._udrfService.udrfUiData.summaryCardsItem={},this._udrfService.udrfUiData.showHierarchyData={},this._udrfService.udrfUiData.inlineEditData={},this._udrfService.udrfUiData.openCommentsData={},this._udrfService.udrfUiData.itemDataScrollDown=this.onCollectionItemDataScrollDown.bind(this),this._udrfService.udrfUiData.udrfBodyColumns=this.udrfBodyColumns,this._udrfService.udrfUiData.udrfVisibleBodyColumns=this._udrfService.udrfUiData.udrfVisibleBodyColumns,this._udrfService.udrfUiData.udrfInvisibleBodyColumns=this._udrfService.udrfUiData.udrfInvisibleBodyColumns,this._udrfService.udrfUiData.variant=0,this._udrfService.udrfUiData.itemHasQuickCta=!1,this._udrfService.udrfUiData.itemHasComments=!1,this._udrfService.udrfUiData.itemHasHierarchyView=!1,this._udrfService.udrfUiData.itemHasInfoButton=!1,this._udrfService.udrfUiData.itemHasMoreActions=!1,this._udrfService.udrfUiData.showCollapseButton=!1,this._udrfService.udrfUiData.horizontalScroll=!0,this._udrfService.udrfUiData.showGroupByButton=!1,this._udrfService.udrfUiData.isMultipleView=!0,this._udrfService.getAppUdrfConfig(this.applicationId,this.initReport.bind(this)),this._udrfService.udrfUiData.createNewComponent=this.addNewConfigurationRow.bind(this),this._udrfService.getNotifyReleasesUDRF(),this._udrfService.udrfUiData.ghostButtonUI=!1,Promise.resolve()}getConfigurationDetails(){return Object(o.c)(this,void 0,void 0,(function*(){try{let e=yield this._misService.getConfigurationDetails("gl_account");return this.tableConfiguration=JSON.stringify(e.data),Promise.resolve()}catch(e){}}))}getFieldData(){return Object(o.c)(this,void 0,void 0,(function*(){try{let e=null;null==this.tableConfiguration&&(yield this.getConfigurationDetails()),e=JSON.parse(this.tableConfiguration);for(let t=0;t<e.length;t++)"text"!=e[t].field_type&&"toggle-visibility"!=e[t].field_type&&null!=this.misFields&&(e[t].options=this.misFields[t].options);return this.misFields=e,Promise.resolve()}catch(e){}}))}listenToParent(){for(let e=0;e<this.misFields.length;e++)if(null!=this.misFields[e].parent_dependent&&null!=this.misFields[e].no_to_retrieve){this.misFields[e].is_visible=1,this.misFields[e].is_required="true";let t=this.misFields[e].parent_dependent,i=this.misFields.filter(e=>e.field_name==t);for(let n=0;n<i[0].choices.length;n++){let t=this.misFields.filter(e=>e.field_name==i[0].choices[n]);0!=t.length&&0!=t.length&&t[0].field_name!=this.misFields[e].field_name&&(t[0].is_visible=0,t[0].is_required=!1)}}}openEditConfigurationDialog(){return Object(o.c)(this,void 0,void 0,(function*(){this.spinnerService.show();let e=null;null==this.tableConfiguration&&(yield this.getConfigurationDetails()),e=JSON.parse(this.tableConfiguration);for(let i=0;i<e.length;i++)if("drop-down-infinte-search"==e[i].field_type||"drop-down-multiple"==e[i].field_type){if("array"!=e[i].retrieve_type&&(e[i].options=null!=this.ruleTableDetails[e[i].selected_value_label]?this.ruleTableDetails[e[i].selected_value_label]:""),null!=this.ruleTableDetails[e[i].selected_value_label]&&(e[i].selected_value=null!=this.ruleTableDetails[e[i].selected_value_label]?this.ruleTableDetails[e[i].selected_value_label].map(e=>e.id):null),"drop-down-infinte-search"==e[i].field_type&&(e[i].selected_value_pair=e[i].options),""!=e[i].options)if("array"==e[i].retrieve_type)e[i].no_to_retrieve=this.ruleTableDetails[e[i].selected_value_label];else{let t=e[i].options.map(e=>e.id);e[i].no_to_retrieve=t}if(null==this.misFields||null==this.misFields[i].options){if("drop-down-multiple"==e[i].field_type){let t=yield this._misService.getMasterGlEdit(e[i].field_name,null,null,null);""!=e[i].options?(e[i].options=[],e[i].options=e[i].options.concat(t.data)):e[i].options=t.data}}else e[i].options=this.misFields[i].options}else if("is_cc_split_up_allowed"==e[i].field_name)e[i].options=JSON.parse(e[i].options),e[i].selected_value={name:1};else if("text"!=e[i].field_type&&"toggle-visibility"!=e[i].field_type&&"split_up"!=e[i].field_type&&"id"!=e[i].field_type){if(null==this.misFields||null==this.misFields[i].options){let t=yield this._misService.getMasterGlEdit(e[i].field_name,null,null,null);e[i].options=t.data}else e[i].options=this.misFields[i].options;e[i].no_to_retrieve=this.ruleTableDetails[e[i].field_name],e[i].selected_value=null!=this.ruleTableDetails[e[i].selected_value_label]?this.ruleTableDetails[e[i].selected_value_label]:null}else e[i].no_to_retrieve=this.ruleTableDetails[e[i].field_name];this.misFields=e,this.listenToParent(),this.spinnerService.hide();let{EditGlAccountComponent:t}=yield Promise.resolve().then(i.bind(null,"Q+U5"));this.dialog.open(t,{height:"100vh",width:"60vw",position:{right:"0px"},data:{ruleTableDetails:this.ruleTableDetails,editForm:!0,misFieldData:this.misFields,searchParam:this._udrfService.udrfData.mainSearchParameter,table:"gl"}})}))}addNewConfigurationRow(){return Object(o.c)(this,void 0,void 0,(function*(){this.spinnerService.show(),yield this.getFieldData();for(let t=0;t<this.misFields.length;t++){if("text"!=this.misFields[t].field_type&&"toggle-visibility"!=this.misFields[t].field_type&&"split_up"!=this.misFields[t].field_type&&"id"!=this.misFields[t].field_type&&null==this.misFields[t].options&&"is_cc_split_up_allowed"!=this.misFields[t].field_name){let e=yield this._misService.getMasterGlEdit(this.misFields[t].field_name,null,null,null);this.misFields[t].options=e.data}"is_cc_split_up_allowed"==this.misFields[t].field_name&&(this.misFields[t].options=Array.isArray(this.misFields[t].options)?this.misFields[t].options:JSON.parse(this.misFields[t].options),this.misFields[t].selected_value={name:1})}this.spinnerService.hide(),this.ruleTableDetails=null;let{EditGlAccountComponent:e}=yield Promise.resolve().then(i.bind(null,"Q+U5"));this.dialog.open(e,{height:"70vh",width:"40vw",data:{ruleTableDetails:this.ruleTableDetails,misFieldData:this.misFields,editForm:!1,searchParam:this._udrfService.udrfData.mainSearchParameter,table:"gl"}})}))}initReport(){return Object(o.c)(this,void 0,void 0,(function*(){this.$onAppApiCalled.next(),this.misItemDataCurrentIndex=0,this._udrfService.udrfBodyData=[],this.selectedItemId=1,this._udrfService.udrfUiData.resolveColumnConfig(),this.getRuleDataList()}))}getMisRuleCount(){return Object(o.c)(this,void 0,void 0,(function*(){let e=JSON.parse(JSON.stringify(this._udrfService.udrfData.mainFilterArray));this._misService.getGlAccountDataCount({startIndex:this.misItemDataCurrentIndex,mainFilterArray:e,txTableDetails:this._udrfService.udrfData.txTableDetails,mainSearchParameter:this._udrfService.udrfData.mainSearchParameter,searchTableDetails:this._udrfService.udrfData.searchTableDetails}).pipe(Object(l.a)(this.$onDestroy)).subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){"S"==e.messType&&(this._udrfService.udrfUiData.totalItemDataCount=e.data[0].record_count)})),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while retrieving MIS data",e&&e.params?e.params:e&&e.error?e.error.params:{})})}))}getRuleDataList(){return Object(o.c)(this,void 0,void 0,(function*(){if(null!=this.searchParamAfterUpdate){let e=JSON.parse(JSON.stringify(this._udrfService.udrfData.mainFilterArray));this.filterConfig={startIndex:this.misItemDataCurrentIndex,mainFilterArray:e,txTableDetails:this._udrfService.udrfData.txTableDetails,mainSearchParameter:this.searchParamAfterUpdate,searchTableDetails:this._udrfService.udrfData.searchTableDetails}}else{let e=JSON.parse(JSON.stringify(this._udrfService.udrfData.mainFilterArray));this.filterConfig={startIndex:this.misItemDataCurrentIndex,mainFilterArray:e,txTableDetails:this._udrfService.udrfData.txTableDetails,mainSearchParameter:this._udrfService.udrfData.mainSearchParameter,searchTableDetails:this._udrfService.udrfData.searchTableDetails}}this.$misRuleDataSubscription&&this.$misRuleDataSubscription.unsubscribe(),this.$misRuleDataSubscription=this._misService.getGlAccountData(this.filterConfig).pipe(Object(l.a)(this.$onDestroy)).pipe(Object(l.a)(this.$onAppApiCalled)).subscribe(e=>Object(o.c)(this,void 0,void 0,(function*(){this.configurationDetail=e.data;for(let e=0;e<this.configurationDetail.length;e++)if(null!=this.configurationDetail[e].salary_column_names){let t=JSON.parse(this.configurationDetail[e].salary_column_names);this.configurationDetail[e].salary_column_names=t,this.configurationDetail[e].salary_column_names_value=t.toString()}"S"==e.messType&&e.data&&e.data.length>0?(this._udrfService.udrfBodyData=this._udrfService.udrfBodyData.concat(e.data),this.getMisRuleCount()):(this._udrfService.udrfBodyData=this._udrfService.udrfBodyData.concat([]),this._udrfService.udrfUiData.totalItemDataCount=0,this._udrfService.udrfData.noItemDataFound=!0),this._udrfService.udrfData.isItemDataLoading=!1})),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while retrieving Invoice data",e&&e.params?e.params:e&&e.error?e.error.params:{})}),this.searchParamAfterUpdate=null}))}onCollectionItemDataScrollDown(){return Object(o.c)(this,void 0,void 0,(function*(){this._udrfService.udrfData.noItemDataFound||this._udrfService.udrfData.isItemDataLoading||(this.misItemDataCurrentIndex+=this._udrfService.udrfData.defaultRecordsPerFetch,this._udrfService.udrfData.isItemDataLoading=!0,this._udrfService.udrfData.appliedConfig.activeView.length>0?this._udrfService.udrfData.appliedConfig.activeView[0].groupByView&&(this._udrfService.groupBy=!0):this.getRuleDataList())}))}itemCardClicked(){return Object(o.c)(this,void 0,void 0,(function*(){this.ruleTableDetails=this._udrfService.udrfUiData.itemCardSelecteditem,this.openEditConfigurationDialog()}))}encodeURIComponent(e){return encodeURIComponent(e).replace(/[!'()*]/g,(function(e){return"%"+e.charCodeAt(0).toString(16)}))}ngOnDestroy(){this.$onDestroy.next(),this.$onDestroy.complete(),this._udrfService.resetUdrfData(),this.$misRuleDataSubscription&&this.$misRuleDataSubscription.unsubscribe(),this.$misRuleDataTotalSubscription&&this.$misRuleDataTotalSubscription.unsubscribe(),this._misService.$determineRuleCode.unsubscribe(),this._misService.misSearchData.unsubscribe(),this._misService.searchParamReload.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275directiveInject"](c.a),s["\u0275\u0275directiveInject"](d.a),s["\u0275\u0275directiveInject"](u.a),s["\u0275\u0275directiveInject"](m.c),s["\u0275\u0275directiveInject"](h.e),s["\u0275\u0275directiveInject"](s.Compiler),s["\u0275\u0275directiveInject"](r.g),s["\u0275\u0275directiveInject"](p.a),s["\u0275\u0275directiveInject"](f.b),s["\u0275\u0275directiveInject"](g.a),s["\u0275\u0275directiveInject"](_.a))},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-gl-account"]],decls:4,vars:0,consts:[[1,"container-fluid","gl-account","pl-0","pr-0"],["size","medium","type","ball-clip-rotate","bdColor","rgba(236, 233, 233, 0.8)","color","#cf0001"]],template:function(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"div",0),s["\u0275\u0275element"](1,"udrf-header"),s["\u0275\u0275element"](2,"udrf-body"),s["\u0275\u0275element"](3,"ngx-spinner",1),s["\u0275\u0275elementEnd"]())},directives:[v.a,b.a,m.a],styles:[".gl-account[_ngcontent-%COMP%]{overflow:hidden!important;height:83vh}.gl-account[_ngcontent-%COMP%]   .status-circular[_ngcontent-%COMP%]{height:13px;width:13px;margin-top:4px;border-radius:50%;box-shadow:0 2px 3px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12)}.gl-account[_ngcontent-%COMP%]     .mat-form-field{width:30vw!important;padding-top:25px}.gl-account[_ngcontent-%COMP%]     .search-bar{padding-left:0!important}.gl-account[_ngcontent-%COMP%]     .udrf-header-card{background-color:#fafafa!important}.gl-account[_ngcontent-%COMP%]     owl-carousel-o{max-width:65vw!important}.gl-account[_ngcontent-%COMP%]     .owl-item{max-width:15.5vw!important}.gl-account[_ngcontent-%COMP%]     .owl-item .mat-select-item{width:15vw!important}.gl-account[_ngcontent-%COMP%]   .spinner-align[_ngcontent-%COMP%]{margin:auto;display:inline-block}.gl-account[_ngcontent-%COMP%]   .infinite-scroll-fixed[_ngcontent-%COMP%]{height:425px;overflow-y:scroll}.gl-account[_ngcontent-%COMP%]   .infinite-scroll-fixed-shortened[_ngcontent-%COMP%]{height:435px;overflow-y:scroll}.gl-account[_ngcontent-%COMP%]   .ta-r[_ngcontent-%COMP%]{text-align:right!important}.gl-account[_ngcontent-%COMP%]   .ta-l[_ngcontent-%COMP%]{text-align:left}.gl-account[_ngcontent-%COMP%]   .is-normal-probability-text[_ngcontent-%COMP%]{color:grey!important}.gl-account[_ngcontent-%COMP%]   .is-low-probability-text[_ngcontent-%COMP%]{color:#cf0001!important}.gl-account[_ngcontent-%COMP%]   .is-moderate-probability-text[_ngcontent-%COMP%]{color:#ff7200!important}.gl-account[_ngcontent-%COMP%]   .is-high-probability-text[_ngcontent-%COMP%]{color:#009432!important}.gl-account[_ngcontent-%COMP%]   .is-normal-probability[_ngcontent-%COMP%]{border-left-color:grey!important}.gl-account[_ngcontent-%COMP%]   .is-low-probability[_ngcontent-%COMP%]{border-left-color:#cf0001!important}.gl-account[_ngcontent-%COMP%]   .is-moderate-probability[_ngcontent-%COMP%]{border-left-color:#ff7200!important}.gl-account[_ngcontent-%COMP%]   .is-high-probability[_ngcontent-%COMP%]{border-left-color:#009432!important}.gl-account[_ngcontent-%COMP%]   .value13Bold[_ngcontent-%COMP%], .gl-account[_ngcontent-%COMP%]   .value14Bold[_ngcontent-%COMP%]{color:#000;display:inline}.gl-account[_ngcontent-%COMP%]   .value14Bold[_ngcontent-%COMP%]{font-size:14px!important;font-weight:500!important;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.gl-account[_ngcontent-%COMP%]   .listcard[_ngcontent-%COMP%]{transition:all .25s linear;height:auto}.gl-account[_ngcontent-%COMP%]   .smallSubtleText[_ngcontent-%COMP%]{color:#66615b;font-size:11px}.gl-account[_ngcontent-%COMP%]   .data-type-card[_ngcontent-%COMP%]{min-height:85px;transition:all .3s linear}.gl-account[_ngcontent-%COMP%]   .data-type-card-is-active[_ngcontent-%COMP%]{border-color:#cf0001;background-color:#fff2f2}.gl-account[_ngcontent-%COMP%]   .headingBold[_ngcontent-%COMP%]{color:#1a1a1a;font-weight:600;font-size:22px}.gl-account[_ngcontent-%COMP%]   .valueGrey14[_ngcontent-%COMP%]{font-size:14px;font-weight:300}.gl-account[_ngcontent-%COMP%]   .valueGrey12Normal[_ngcontent-%COMP%], .gl-account[_ngcontent-%COMP%]   .valueGrey14[_ngcontent-%COMP%]{color:#4a4a4a;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline}.gl-account[_ngcontent-%COMP%]   .valueGrey12Normal[_ngcontent-%COMP%]{font-size:12px;font-weight:400}.gl-account[_ngcontent-%COMP%]   .cp[_ngcontent-%COMP%]{cursor:pointer!important}.gl-account[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]{margin-top:5px!important;line-height:8px;width:26px;height:26px;margin-right:10px!important;box-shadow:0 4px 4px -1px rgba(0,0,0,.2),0 1px 2px 0 rgba(0,0,0,.14),0 1px 14px 0 rgba(0,0,0,.12);animation:slide-top .3s cubic-bezier(.25,.46,.45,.94) both}.gl-account[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%], .gl-account[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]   .iconButton[_ngcontent-%COMP%]{color:#66615b;font-size:18px}.gl-account[_ngcontent-%COMP%]   .ta-c[_ngcontent-%COMP%]{text-align:center;padding-left:0!important;padding-right:0!important}.gl-account[_ngcontent-%COMP%]   .divAlignItemCenter[_ngcontent-%COMP%]{display:flex;align-items:center}.gl-account[_ngcontent-%COMP%]   .menu-icons[_ngcontent-%COMP%]{font-size:4px}.gl-account[_ngcontent-%COMP%]   .example-container[_ngcontent-%COMP%]{width:400px;max-width:100%;margin:0 25px 25px 0;display:inline-block;vertical-align:top}.gl-account[_ngcontent-%COMP%]   .example-list[_ngcontent-%COMP%]{border:1px solid #ccc;min-height:60px;background:#fff;border-radius:4px;overflow:hidden;display:block}.gl-account[_ngcontent-%COMP%]     .example-box{border-bottom:1px solid #ccc;color:rgba(0,0,0,.87);display:flex;flex-direction:row;align-items:left;justify-content:space-between;box-sizing:border-box;cursor:move;background:#fff;font-size:4px}.gl-account[_ngcontent-%COMP%]     .menu-list{cursor:move;font-size:8px}.gl-account[_ngcontent-%COMP%]   .cdk-drag-preview[_ngcontent-%COMP%]{box-sizing:border-box;border-radius:4px;box-shadow:0 5px 5px -3px rgba(0,0,0,.2),0 8px 10px 1px rgba(0,0,0,.14),0 3px 14px 2px rgba(0,0,0,.12)}.gl-account[_ngcontent-%COMP%]   .cdk-drag-placeholder[_ngcontent-%COMP%]{opacity:0}.gl-account[_ngcontent-%COMP%]   .cdk-drag-animating[_ngcontent-%COMP%]{transition:transform .25s cubic-bezier(0,0,.2,1)}.gl-account[_ngcontent-%COMP%]   .example-box[_ngcontent-%COMP%]:last-child{border:none}.gl-account[_ngcontent-%COMP%]   .example-list.cdk-drop-list-dragging[_ngcontent-%COMP%]   .example-box[_ngcontent-%COMP%]:not(.cdk-drag-placeholder){transition:transform .25s cubic-bezier(0,0,.2,1)}.gl-account[_ngcontent-%COMP%]   .sr-nav-item[_ngcontent-%COMP%]{background-color:#f5f5f5!important;font-weight:500;border-left:solid #545352;border-radius:2px;transition:.3s;box-shadow:0 4px 8px 0 rgba(0,0,0,.2),0 6px 20px 0 rgba(0,0,0,.19)}.gl-account[_ngcontent-%COMP%]   .r-nav-item[_ngcontent-%COMP%]{font-weight:400;cursor:pointer}.gl-account[_ngcontent-%COMP%]   .r-nav-item[_ngcontent-%COMP%]:hover{background-color:#f5f5f5!important;font-weight:500;border-left:solid #545352;border-radius:2px;transition:box-shadow .3s;box-shadow:0 4px 8px 0 rgba(0,0,0,.2),0 6px 20px 0 rgba(0,0,0,.19)}.gl-account[_ngcontent-%COMP%]   .edit-nav[_ngcontent-%COMP%]{width:15.5vw;overflow-y:scroll;height:83vh;overflow-x:hidden!important;background-color:#fff;color:#000}.gl-account[_ngcontent-%COMP%]   .value13light[_ngcontent-%COMP%]{color:#4e4c4c;font-weight:400!important;display:inline}.gl-account[_ngcontent-%COMP%]   .value13Bold[_ngcontent-%COMP%], .gl-account[_ngcontent-%COMP%]   .value13light[_ngcontent-%COMP%]{font-size:13px!important;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.gl-account[_ngcontent-%COMP%]   .value13Bold[_ngcontent-%COMP%]{color:#cf0001;font-weight:500!important}.gl-account[_ngcontent-%COMP%]   .shimmer[_ngcontent-%COMP%]{background:#f6f7f8;background-image:linear-gradient(90deg,#f6f7f8 0,#edeef1 20%,#f6f7f8 100%,#f6f7f8 0);background-repeat:no-repeat;background-size:800px 400px;display:inline-block;position:relative}.gl-account[_ngcontent-%COMP%]   .sm-lines[_ngcontent-%COMP%]{height:30px;margin-top:10px;width:100px}@keyframes placeholderShimmer{0%{background-position:-468px 0}to{background-position:468px 0}}.gl-account[_ngcontent-%COMP%]   .slide-from-down[_ngcontent-%COMP%]{animation:slide-from-down .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-from-down{0%{transform:translateY(30px);opacity:0}to{transform:translateY(0);opacity:1}}.mat-mini-fab[_ngcontent-%COMP%]{height:26px;width:27px}  .mat-mini-fab .mat-button-wrapper{padding:0!important;display:inline-block;line-height:0!important}#spinner[_ngcontent-%COMP%]{animation:frames 1s linear infinite;background:transparent;border-radius:100%;border:1.75vw solid #fff;border-top-color:#df691a;width:20vw;height:20vw;opacity:.6;padding:0;position:absolute;z-index:999}@keyframes frames{0%{transform:rotate(0deg)}to{transform:rotate(359deg)}}#pause[_ngcontent-%COMP%]{display:block;background:rgba(0,0,0,.66) no-repeat 0 0;width:100%;height:100%;position:fixed;bottom:0;left:0;z-index:1000}"]}),e})();var x=i("v0+k");let S=(()=>{class e{constructor(){this.configTableId=41}ngOnInit(){}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-statistical-master-config"]],decls:2,vars:1,consts:[[1,"container-fluid","pt-3","pb-2"],[3,"id"]],template:function(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"div",0),s["\u0275\u0275element"](1,"app-data-upload-grid",1),s["\u0275\u0275elementEnd"]()),2&e&&(s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("id",t.configTableId))},directives:[x.a],styles:[""]}),e})();var O=i("NFeN"),P=i("Qu3c");const M=[{path:"ruleSheet",component:C,data:{breadcrumb:"Rule Sheet"}},{path:"glAccount",component:y,data:{breadcrumb:"GL Account"}},{path:"statistical-key-configuration",component:(()=>{class e{constructor(e){this.dialog=e,this.configTableId=39,this.onRefresh=!1,this.onDialogClose=(function(){return Object(o.c)(this,void 0,void 0,(function*(){this.onRefresh=!1}))}).bind(this)}ngOnInit(){}updateGroupingMaster(){return Object(o.c)(this,void 0,void 0,(function*(){this.dialog.open(S,{width:"70%",height:"95%",disableClose:!1}).afterClosed().subscribe(e=>{this.onRefresh=!0,setTimeout(this.onDialogClose,5)})}))}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275directiveInject"](f.b))},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-statistical-project-config"]],decls:7,vars:1,consts:[[1,"container-fluid","pt-3","pb-3"],[1,"row"],[1,"col"],[1,"col-1",2,"position","relative","display","inline-block"],["matTooltip","Update Statistical Grouping Master",2,"cursor","pointer","position","absolute","z-index","1","font-size","25px",3,"click"],["id","39",3,"onRefresh"]],template:function(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"div",0),s["\u0275\u0275elementStart"](1,"div",1),s["\u0275\u0275element"](2,"div",2),s["\u0275\u0275elementStart"](3,"div",3),s["\u0275\u0275elementStart"](4,"mat-icon",4),s["\u0275\u0275listener"]("click",(function(){return t.updateGroupingMaster()})),s["\u0275\u0275text"](5,"add"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275element"](6,"app-data-upload-grid",5),s["\u0275\u0275elementEnd"]()),2&e&&(s["\u0275\u0275advance"](6),s["\u0275\u0275property"]("onRefresh",t.onRefresh))},directives:[O.a,P.a,x.a],styles:[""]}),e})(),data:{breadcrumb:"Project - Statistical Key Figure Configuration"}}];let D=(()=>{class e{}return e.\u0275mod=s["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=s["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[r.k.forChild(M)],r.k]}),e})();var w=i("Xi0T"),I=i("3Pt+"),E=i("wZkO"),F=i("f0Cb"),A=i("Wp6s"),k=i("kmnG"),T=i("d3UM"),V=i("qFsG"),R=i("bTqV"),N=i("1jcm"),j=i("Kj3r"),G=i("/uUt"),U=i("XXEo"),L=i("FKr1");function z(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"mat-option",5),s["\u0275\u0275text"](1),s["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;s["\u0275\u0275property"]("value",e.id),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate2"](" ",e.id," | ",e.name," ")}}function B(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"mat-select",3),s["\u0275\u0275listener"]("selectionChange",(function(t){s["\u0275\u0275restoreView"](e);const i=s["\u0275\u0275nextContext"]();return i.misFieldValueChanges(t,i.field)})),s["\u0275\u0275template"](1,z,2,3,"mat-option",4),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275nextContext"]();s["\u0275\u0275property"]("id",e.field.field_name)("formControlName",e.field.field_name)("placeholder",e.field.field_label)("disabled",e.field.is_disabled),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngForOf",e.field.options)}}function $(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"mat-option",5),s["\u0275\u0275text"](1),s["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;s["\u0275\u0275property"]("value",e.name),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate1"](" ",e.name," ")}}function q(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"mat-select",3),s["\u0275\u0275listener"]("selectionChange",(function(t){s["\u0275\u0275restoreView"](e);const i=s["\u0275\u0275nextContext"]();return i.misFieldValueChanges(t,i.field)})),s["\u0275\u0275template"](1,$,2,2,"mat-option",4),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275nextContext"]();s["\u0275\u0275property"]("id",e.field.field_name)("formControlName",e.field.field_name)("placeholder",e.field.field_label)("disabled",e.field.is_disabled),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngForOf",e.field.options)}}let H=(()=>{class e{constructor(e){this._misService=e,this.selectedItem=null,this._misService.$patchFieldValue.subscribe(e=>{""!=e&&(this.selectedItem=e)})}ngOnInit(){}ngOnDestroy(){this._misService.determineMandatory("")}misFieldValueChanges(e,t){"true"==t.listen_value_change&&this._misService.determineMandatory({id:e.value,field:t}),this.field.selected_value=this.form.controls[this.field.field_name].value,null!=this.selectedItem&&"manualPercentage"!=this.selectedItem&&"true"==this.field.listen_value_change&&null!=this.field.choices?(this.form.controls[this.selectedItem].setValidators([I.H.required]),this.form.controls[this.selectedItem].updateValueAndValidity(),JSON.parse(this.field.choices).forEach(e=>{e!=this.selectedItem&&"manualPercentage"!=e?(this.form.controls[e].patchValue(null),this.form.controls[e].clearValidators(),this.form.controls[e].updateValueAndValidity()):"manualPercentage"==e&&(this._misService.getManualRuleCode("manualPercentage"),this._misService.clearManualPercentage())})):"manualPercentage"==this.selectedItem?(JSON.parse(this.field.choices).forEach(e=>{"manualPercentage"!=e&&(this.form.controls[e].patchValue(null),this.form.controls[e].clearValidators(),this.form.controls[e].updateValueAndValidity())}),this._misService.getManualRuleCode("manualPercentage")):null==this.selectedItem&&"rule_code"==this.field.field_name&&(-1!=JSON.parse(this.field.choices).indexOf("manualPercentage")&&this._misService.clearManualPercentage(),JSON.parse(this.field.choices).forEach(e=>{"manualPercentage"!=e&&(this.form.controls[e].patchValue(null),this.form.controls[e].clearValidators(),this.form.controls[e].updateValueAndValidity())})),this.selectedItem=null}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275directiveInject"](d.a))},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-mis-dropdown"]],inputs:{field:"field",form:"form"},decls:6,vars:4,consts:[[1,"drop-down",3,"formGroup"],["appearance","outline",1,"full-width"],[3,"id","formControlName","placeholder","disabled","selectionChange",4,"ngIf"],[3,"id","formControlName","placeholder","disabled","selectionChange"],[3,"value",4,"ngFor","ngForOf"],[3,"value"]],template:function(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"div",0),s["\u0275\u0275elementStart"](1,"mat-form-field",1),s["\u0275\u0275elementStart"](2,"mat-label"),s["\u0275\u0275text"](3),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](4,B,2,5,"mat-select",2),s["\u0275\u0275template"](5,q,2,5,"mat-select",2),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e&&(s["\u0275\u0275property"]("formGroup",t.form),s["\u0275\u0275advance"](3),s["\u0275\u0275textInterpolate"](t.field.field_label),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf","noId"!=t.field.retrieve_type),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf","noId"==t.field.retrieve_type))},directives:[I.w,I.n,k.c,k.g,n.NgIf,T.c,I.v,I.l,n.NgForOf,L.p],styles:[".drop-down[_ngcontent-%COMP%]   .full-width[_ngcontent-%COMP%]{width:100%!important}"]}),e})(),J=(()=>{class e{constructor(){}ngOnInit(){}misFieldValueChanges(){this.field.selected_value=this.form.controls[this.field.field_name].value}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-mis-input"]],inputs:{field:"field",form:"form"},decls:5,vars:4,consts:[["clas","mis-input",3,"formGroup"],["appearance","outline",1,"full-width",2,"width","100%"],["matInput","",3,"formControlName","placeholder","input"]],template:function(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"div",0),s["\u0275\u0275elementStart"](1,"mat-form-field",1),s["\u0275\u0275elementStart"](2,"mat-label"),s["\u0275\u0275text"](3),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](4,"input",2),s["\u0275\u0275listener"]("input",(function(){return t.misFieldValueChanges()})),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e&&(s["\u0275\u0275property"]("formGroup",t.form),s["\u0275\u0275advance"](3),s["\u0275\u0275textInterpolate"](t.field.field_label),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("formControlName",t.field.field_name)("placeholder",t.field.field_label))},directives:[I.w,I.n,k.c,k.g,V.b,I.e,I.v,I.l],styles:[".mis-input[_ngcontent-%COMP%]   .full-width[_ngcontent-%COMP%]{width:100%!important}"]}),e})();function Q(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"mat-option",5),s["\u0275\u0275text"](1),s["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;s["\u0275\u0275property"]("value",e.id),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate2"](" ",e.id," | ",e.name," ")}}function W(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"mat-select",3),s["\u0275\u0275listener"]("selectionChange",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"]().misFieldValueChanges()})),s["\u0275\u0275template"](1,Q,2,3,"mat-option",4),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275nextContext"]();s["\u0275\u0275property"]("id",e.field.field_name)("formControlName",e.field.field_name)("placeholder",e.field.field_label)("disabled",e.field.is_disabled),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngForOf",e.field.options)}}function X(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"mat-option",5),s["\u0275\u0275text"](1),s["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;s["\u0275\u0275property"]("value",e),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate1"](" ",e," ")}}function K(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"mat-select",3),s["\u0275\u0275listener"]("selectionChange",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"]().misFieldValueChanges()})),s["\u0275\u0275template"](1,X,2,2,"mat-option",4),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275nextContext"]();s["\u0275\u0275property"]("id",e.field.field_name)("formControlName",e.field.field_name)("placeholder",e.field.field_label)("disabled",e.field.is_disabled),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngForOf",e.field.options)}}let Z=(()=>{class e{constructor(){}ngOnInit(){}misFieldValueChanges(){this.field.selected_value=this.form.controls[this.field.field_name].value}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-mis-dropdown-multiple"]],inputs:{field:"field",form:"form"},decls:6,vars:4,consts:[[1,"drop-down",3,"formGroup"],["appearance","outline",1,"full-width"],["multiple","",3,"id","formControlName","placeholder","disabled","selectionChange",4,"ngIf"],["multiple","",3,"id","formControlName","placeholder","disabled","selectionChange"],[3,"value",4,"ngFor","ngForOf"],[3,"value"]],template:function(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"div",0),s["\u0275\u0275elementStart"](1,"mat-form-field",1),s["\u0275\u0275elementStart"](2,"mat-label"),s["\u0275\u0275text"](3),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](4,W,2,5,"mat-select",2),s["\u0275\u0275template"](5,K,2,5,"mat-select",2),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e&&(s["\u0275\u0275property"]("formGroup",t.form),s["\u0275\u0275advance"](3),s["\u0275\u0275textInterpolate"](t.field.field_label),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf","array"!=t.field.retrieve_type&&"noId"!=t.field.retrieve_type),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf","array"==t.field.retrieve_type||"noId"==t.field.retrieve_type))},directives:[I.w,I.n,k.c,k.g,n.NgIf,T.c,I.v,I.l,n.NgForOf,L.p],styles:[".drop-down[_ngcontent-%COMP%]   .full-width[_ngcontent-%COMP%]{width:100%!important}"]}),e})();const Y=["onScroll"];function ee(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"span",10),s["\u0275\u0275elementStart"](1,"mat-icon",11),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"]().closeSearch("gl")})),s["\u0275\u0275text"](2,"highlight_off"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}}function te(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"mat-option",13),s["\u0275\u0275text"](1),s["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;s["\u0275\u0275property"]("value",e.id),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate2"](" ",e.id," | ",e.name," ")}}function ie(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"div"),s["\u0275\u0275template"](1,te,2,3,"mat-option",12),s["\u0275\u0275elementEnd"]()),2&e){const e=s["\u0275\u0275nextContext"]();s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngForOf",e.field.options)}}const ne=function(){return{standalone:!0}};let re=(()=>{class e{constructor(e){this._misService=e,this.$onSearchKeyUp=new a.b,this.noToRetrive=new s.EventEmitter,this.$onSearchKeyUp.pipe(Object(j.a)(1e3),Object(G.a)()).subscribe(e=>{this.getSearchResults(e)})}ngOnInit(){}getDataConfig(){this.field.no_to_retrieve=this.form.controls[this.field.field_name].value,this.field.selected_value_pair=[],null!=this.field.options&&null!=this.field.no_to_retrieve&&this.field.options.filter(e=>{this.field.no_to_retrieve.filter(t=>{e.id==t&&this.field.selected_value_pair.push(e)})})}onAddIdDescription(){"true"==this.field.id_value_pair&&(this.field.id_description=[],null!=this.field.selected_value_pair&&this.field.selected_value_pair.forEach(e=>{this.field.id_description.push(e.id+"-"+e.name)}))}onSearch(e){""!=e.target.value&&(this.field.search_close=!0,this.field.search_value=e.target.value,this.oldOption=this.field.options,this.field.scroll_index=0,this.$onSearchKeyUp.next(e.target.value))}getSearchResults(e){this.getDataConfig(),this.onAddIdDescription(),this.field.options=[],this._misService.misSearchData.next(this.field)}closeSearch(e){this.getDataConfig(),this.field.search_value=null,this.field.search_close=!1,this.field.options=[],this.field.scroll_index=0,this._misService.misSearchData.next(this.field)}onOpenedChange(e,t){console.log(this.field),e&&this[t].panel.nativeElement.addEventListener("scroll",e=>{let i=Math.ceil(this[t].panel.nativeElement.scrollTop),n=this[t].panel.nativeElement.scrollHeight-this[t].panel.nativeElement.offsetHeight;i-1!=n&&i!=n&&i+1!=n||(this.getDataConfig(),this.onAddIdDescription(),this.field.scroll_index=this.field.scroll_index+15,this.noToRetrive.emit(this.field))})}ngOnDestroy(){this._misService.misSearchData.next("search data"),this.$onSearchKeyUp.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275directiveInject"](d.a))},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-mis-dropdown-search-infinite-scroll"]],viewQuery:function(e,t){if(1&e&&s["\u0275\u0275viewQuery"](Y,!0),2&e){let e;s["\u0275\u0275queryRefresh"](e=s["\u0275\u0275loadQuery"]())&&(t.onScroll=e.first)}},inputs:{field:"field",form:"form"},outputs:{noToRetrive:"misEvent"},decls:13,vars:12,consts:[[3,"formGroup"],["appearance","outline",1,"full-width"],["multiple","",3,"id","formControlName","placeholder","disabled","openedChange"],["onScroll",""],[2,"width","100%"],[1,"pl-1","icon-search"],["matPrefix",""],[1,"p-3","pl-0",2,"width","80%",3,"placeholder","ngModel","ngModelOptions","ngModelChange","keyup"],["class","icon-search-cancel",4,"ngIf"],[4,"ngIf"],[1,"icon-search-cancel"],["matSuffix","",1,"mr-1",3,"click"],[3,"value",4,"ngFor","ngForOf"],[3,"value"]],template:function(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"div",0),s["\u0275\u0275elementStart"](1,"mat-form-field",1),s["\u0275\u0275elementStart"](2,"mat-label"),s["\u0275\u0275text"](3),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](4,"mat-select",2,3),s["\u0275\u0275listener"]("openedChange",(function(e){return t.onOpenedChange(e,"onScroll")})),s["\u0275\u0275elementStart"](6,"div",4),s["\u0275\u0275elementStart"](7,"span",5),s["\u0275\u0275elementStart"](8,"mat-icon",6),s["\u0275\u0275text"](9,"search"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](10,"input",7),s["\u0275\u0275listener"]("ngModelChange",(function(e){return t.field.search_value=e}))("keyup",(function(e){return t.onSearch(e)})),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](11,ee,3,0,"span",8),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](12,ie,2,1,"div",9),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e&&(s["\u0275\u0275property"]("formGroup",t.form),s["\u0275\u0275advance"](3),s["\u0275\u0275textInterpolate"](t.field.field_label),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("id",t.field.field_name)("formControlName",t.field.field_name)("placeholder",t.field.field_label)("disabled",t.field.is_disabled),s["\u0275\u0275advance"](6),s["\u0275\u0275property"]("placeholder",t.field.field_label)("ngModel",t.field.search_value)("ngModelOptions",s["\u0275\u0275pureFunction0"](11,ne)),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",1==t.field.search_close),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",""!=t.field.options))},directives:[I.w,I.n,k.c,k.g,T.c,I.v,I.l,O.a,k.h,I.e,I.y,n.NgIf,k.i,n.NgForOf,L.p],styles:[".full-width[_ngcontent-%COMP%]{width:100%!important}input[_ngcontent-%COMP%], input[_ngcontent-%COMP%]:focus{border:none;outline:none;caret-color:#cf0001}.icon-search[_ngcontent-%COMP%], .icon-search-cancel[_ngcontent-%COMP%]{color:#a1a1a2;font-size:20px;vertical-align:middle}.icon-search-cancel[_ngcontent-%COMP%]{cursor:pointer}"]}),e})();const oe=["onScroll"];function ae(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"span",10),s["\u0275\u0275elementStart"](1,"mat-icon",11),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"]().closeSearch("gl")})),s["\u0275\u0275text"](2,"highlight_off"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}}function le(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"mat-option",13),s["\u0275\u0275text"](1),s["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;s["\u0275\u0275property"]("value",e.id),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate2"](" ",e.id," | ",e.name," ")}}function se(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"div"),s["\u0275\u0275template"](1,le,2,3,"mat-option",12),s["\u0275\u0275elementEnd"]()),2&e){const e=s["\u0275\u0275nextContext"]();s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngForOf",e.field.options)}}const ce=function(){return{standalone:!0}};let de=(()=>{class e{constructor(e){this._misService=e,this.$onSearchKeyUp=new a.b,this.noToRetrive=new s.EventEmitter,this.$onSearchKeyUp.pipe(Object(j.a)(1e3),Object(G.a)()).subscribe(e=>{this.getSearchResults(e)})}ngOnInit(){}getDataConfig(){this.field.no_to_retrieve=this.form.controls[this.field.field_name].value}onSearch(e){""!=e.target.value&&(this.field.search_close=!0,this.field.search_value=e.target.value,this.oldRuleDataOption=this.field.options,this.field.scroll_index=0,this.$onSearchKeyUp.next(e.target.value))}getSearchResults(e){this.getDataConfig(),this.field.options=[],this._misService.misSearchData.next(this.field)}closeSearch(e){this.getDataConfig(),this.field.search_value=null,this.field.search_close=!1,this.field.options=[],this.field.scroll_index=0,this._misService.misSearchData.next(this.field)}onRuleDataScroll(e,t){e&&this[t].panel.nativeElement.addEventListener("scroll",e=>{let i=Math.ceil(this[t].panel.nativeElement.scrollTop),n=this[t].panel.nativeElement.scrollHeight-this[t].panel.nativeElement.offsetHeight;i-1!=n&&i!=n&&i+1!=n||(this.getDataConfig(),this.field.scroll_index=this.field.scroll_index+15,this.noToRetrive.emit(this.field))})}ngOnDestroy(){this.$onSearchKeyUp.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275directiveInject"](d.a))},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-mis-dropdown-search-single-select"]],viewQuery:function(e,t){if(1&e&&s["\u0275\u0275viewQuery"](oe,!0),2&e){let e;s["\u0275\u0275queryRefresh"](e=s["\u0275\u0275loadQuery"]())&&(t.onScroll=e.first)}},inputs:{field:"field",form:"form"},outputs:{noToRetrive:"misEvent"},decls:13,vars:12,consts:[[3,"formGroup"],["appearance","outline",1,"full-width"],[3,"id","formControlName","placeholder","disabled","openedChange"],["onScroll",""],[2,"width","100%"],[1,"pl-1","icon-search"],["matPrefix",""],[1,"p-3","pl-0",2,"width","80%",3,"placeholder","ngModel","ngModelOptions","ngModelChange","keyup"],["class","icon-search-cancel",4,"ngIf"],[4,"ngIf"],[1,"icon-search-cancel"],["matSuffix","",1,"mr-1",3,"click"],[3,"value",4,"ngFor","ngForOf"],[3,"value"]],template:function(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"div",0),s["\u0275\u0275elementStart"](1,"mat-form-field",1),s["\u0275\u0275elementStart"](2,"mat-label"),s["\u0275\u0275text"](3),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](4,"mat-select",2,3),s["\u0275\u0275listener"]("openedChange",(function(e){return t.onRuleDataScroll(e,"onScroll")})),s["\u0275\u0275elementStart"](6,"div",4),s["\u0275\u0275elementStart"](7,"span",5),s["\u0275\u0275elementStart"](8,"mat-icon",6),s["\u0275\u0275text"](9,"search"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](10,"input",7),s["\u0275\u0275listener"]("ngModelChange",(function(e){return t.field.search_value=e}))("keyup",(function(e){return t.onSearch(e)})),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](11,ae,3,0,"span",8),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](12,se,2,1,"div",9),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e&&(s["\u0275\u0275property"]("formGroup",t.form),s["\u0275\u0275advance"](3),s["\u0275\u0275textInterpolate"](t.field.field_label),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("id",t.field.field_name)("formControlName",t.field.field_name)("placeholder",t.field.field_label)("disabled",t.field.is_disabled),s["\u0275\u0275advance"](6),s["\u0275\u0275property"]("placeholder",t.field.field_label)("ngModel",t.field.search_value)("ngModelOptions",s["\u0275\u0275pureFunction0"](11,ce)),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",1==t.field.search_close),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",""!=t.field.options))},directives:[I.w,I.n,k.c,k.g,T.c,I.v,I.l,O.a,k.h,I.e,I.y,n.NgIf,k.i,n.NgForOf,L.p],styles:[".full-width[_ngcontent-%COMP%]{width:100%!important}input[_ngcontent-%COMP%], input[_ngcontent-%COMP%]:focus{border:none;outline:none;caret-color:#cf0001}.icon-search[_ngcontent-%COMP%], .icon-search-cancel[_ngcontent-%COMP%]{color:#a1a1a2;font-size:20px;vertical-align:middle}.icon-search-cancel[_ngcontent-%COMP%]{cursor:pointer}"]}),e})();function ue(e,t){if(1&e&&s["\u0275\u0275element"](0,"app-mis-dropdown",4),2&e){const e=s["\u0275\u0275nextContext"]();s["\u0275\u0275property"]("field",e.field)("form",e.form)}}function me(e,t){if(1&e&&s["\u0275\u0275element"](0,"app-mis-input",4),2&e){const e=s["\u0275\u0275nextContext"]();s["\u0275\u0275property"]("field",e.field)("form",e.form)}}function he(e,t){if(1&e&&s["\u0275\u0275element"](0,"app-mis-dropdown-multiple",4),2&e){const e=s["\u0275\u0275nextContext"]();s["\u0275\u0275property"]("field",e.field)("form",e.form)}}function pe(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"app-mis-dropdown-search-infinite-scroll",5),s["\u0275\u0275listener"]("misEvent",(function(t){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"]().noToRetrieve(t)})),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275nextContext"]();s["\u0275\u0275property"]("field",e.field)("form",e.form)}}function fe(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"app-mis-dropdown-search-single-select",5),s["\u0275\u0275listener"]("misEvent",(function(t){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"]().noToRetrieve(t)})),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275nextContext"]();s["\u0275\u0275property"]("field",e.field)("form",e.form)}}let ge=(()=>{class e{constructor(){this.noToRetrieveToForm=new s.EventEmitter}ngOnInit(){}noToRetrieve(e){this.noToRetrieveToForm.emit(e)}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-mis-field-builder"]],inputs:{field:"field",form:"form"},outputs:{noToRetrieveToForm:"misEvent"},decls:7,vars:7,consts:[[3,"formGroup"],[1,"pt-3",2,"width","100%",3,"ngSwitch"],[3,"field","form",4,"ngSwitchCase"],[3,"field","form","misEvent",4,"ngSwitchCase"],[3,"field","form"],[3,"field","form","misEvent"]],template:function(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"div",0),s["\u0275\u0275elementStart"](1,"div",1),s["\u0275\u0275template"](2,ue,1,2,"app-mis-dropdown",2),s["\u0275\u0275template"](3,me,1,2,"app-mis-input",2),s["\u0275\u0275template"](4,he,1,2,"app-mis-dropdown-multiple",2),s["\u0275\u0275template"](5,pe,1,2,"app-mis-dropdown-search-infinite-scroll",3),s["\u0275\u0275template"](6,fe,1,2,"app-mis-dropdown-search-single-select",3),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e&&(s["\u0275\u0275property"]("formGroup",t.form),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngSwitch",t.field.field_type),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngSwitchCase","drop-down"),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngSwitchCase","text"),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngSwitchCase","drop-down-multiple"),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngSwitchCase","drop-down-infinte-search"),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngSwitchCase","drop-down-single-select-search"))},directives:[I.w,I.n,n.NgSwitch,n.NgSwitchCase,H,J,Z,re,de],styles:[""]}),e})();const _e=["onScroll"];function ve(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"div",15),s["\u0275\u0275elementStart"](1,"app-mis-field-builder",16),s["\u0275\u0275listener"]("misEvent",(function(t){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"](2).noToRetrieveToForm(t)})),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275nextContext"]().$implicit,t=s["\u0275\u0275nextContext"]();s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("field",e)("form",t.form)}}function be(e,t){if(1&e&&(s["\u0275\u0275elementContainerStart"](0),s["\u0275\u0275template"](1,ve,2,2,"div",14),s["\u0275\u0275elementContainerEnd"]()),2&e){const e=t.$implicit;s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",1==e.is_visible)}}function Ce(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"span",21),s["\u0275\u0275text"](1," Selected cost center already exist! "),s["\u0275\u0275elementEnd"]())}function ye(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"div",17),s["\u0275\u0275elementStart"](1,"div",18),s["\u0275\u0275text"](2,"Manual Percentage"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](3,"div",19),s["\u0275\u0275template"](4,Ce,2,0,"span",20),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e){const e=s["\u0275\u0275nextContext"]();s["\u0275\u0275advance"](4),s["\u0275\u0275property"]("ngIf",1==e.duplicateCostCenterAlert)}}function xe(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"mat-icon",39),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"](2).closeSearch("manualPercentage")})),s["\u0275\u0275text"](1,"highlight_off"),s["\u0275\u0275elementEnd"]()}}function Se(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"mat-option",41),s["\u0275\u0275text"](1),s["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;s["\u0275\u0275property"]("value",e),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate2"]("",e.id," | ",e.name," ")}}function Oe(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"div"),s["\u0275\u0275template"](1,Se,2,3,"mat-option",40),s["\u0275\u0275elementEnd"]()),2&e){const e=s["\u0275\u0275nextContext"](2);s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngForOf",e.manualPercentageArray)}}function Pe(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"mat-icon",42),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"](2).addManualPercent()})),s["\u0275\u0275text"](1," add_circle"),s["\u0275\u0275elementEnd"]()}}function Me(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"mat-icon",42),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"](2).editManualPercent()})),s["\u0275\u0275text"](1," check_circle"),s["\u0275\u0275elementEnd"]()}}const De=function(){return{standalone:!0}};function we(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"div",22),s["\u0275\u0275elementStart"](1,"div",23),s["\u0275\u0275elementStart"](2,"mat-form-field",24),s["\u0275\u0275elementStart"](3,"mat-label"),s["\u0275\u0275text"](4,"Cost Center"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](5,"mat-select",25,26),s["\u0275\u0275listener"]("openedChange",(function(t){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"]().onScrollManualCostCenter(t,"onScroll")})),s["\u0275\u0275elementStart"](7,"div",27),s["\u0275\u0275elementStart"](8,"span",28),s["\u0275\u0275elementStart"](9,"mat-icon",29),s["\u0275\u0275text"](10,"search"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](11,"input",30),s["\u0275\u0275listener"]("ngModelChange",(function(t){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"]().manualSearchValue=t}))("keyup",(function(t){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"]().onManualCostCenterSearch(t)})),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](12,"span",31),s["\u0275\u0275template"](13,xe,2,0,"mat-icon",32),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](14,Oe,2,1,"div",13),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](15,"div",33),s["\u0275\u0275elementStart"](16,"mat-form-field",24),s["\u0275\u0275elementStart"](17,"mat-label",34),s["\u0275\u0275text"](18,"Manual Percentage"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275element"](19,"input",35),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](20,"div",36),s["\u0275\u0275elementStart"](21,"button",37),s["\u0275\u0275template"](22,Pe,2,0,"mat-icon",38),s["\u0275\u0275template"](23,Me,2,0,"mat-icon",38),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275nextContext"]();s["\u0275\u0275property"]("formGroup",e.manualGroup),s["\u0275\u0275advance"](11),s["\u0275\u0275property"]("ngModel",e.manualSearchValue)("ngModelOptions",s["\u0275\u0275pureFunction0"](8,De)),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("ngIf",1==e.searchManual),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",e.manualPercentageArray.length>0&&null!=e.manualPercentageArray[0]),s["\u0275\u0275advance"](7),s["\u0275\u0275property"]("disabled",!e.manualGroup.valid),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",0==e.enableEditOnManualPercentage),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",1==e.enableEditOnManualPercentage)}}function Ie(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"div"),s["\u0275\u0275elementStart"](1,"mat-card",46),s["\u0275\u0275elementStart"](2,"div",47),s["\u0275\u0275elementStart"](3,"div",48),s["\u0275\u0275listener"]("click",(function(){s["\u0275\u0275restoreView"](e);const t=s["\u0275\u0275nextContext"](),i=t.$implicit,n=t.index;return s["\u0275\u0275nextContext"](2).percentageCardClicked(i.value.id,i.value.percentage,n)})),s["\u0275\u0275elementStart"](4,"span",49),s["\u0275\u0275text"](5),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](6,"span",50),s["\u0275\u0275text"](7),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](8,"div",51),s["\u0275\u0275elementStart"](9,"span",31),s["\u0275\u0275elementStart"](10,"mat-icon",39),s["\u0275\u0275listener"]("click",(function(){s["\u0275\u0275restoreView"](e);const t=s["\u0275\u0275nextContext"]().index;return s["\u0275\u0275nextContext"](2).deleteManualPercentage(t)})),s["\u0275\u0275text"](11," highlight_off"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](12,"div",52),s["\u0275\u0275elementStart"](13,"div",53),s["\u0275\u0275listener"]("click",(function(){s["\u0275\u0275restoreView"](e);const t=s["\u0275\u0275nextContext"](),i=t.$implicit,n=t.index;return s["\u0275\u0275nextContext"](2).percentageCardClicked(i.value.id,i.value.percentage,n)})),s["\u0275\u0275text"](14),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275nextContext"]().$implicit;s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("matTooltip",e.value.name),s["\u0275\u0275advance"](4),s["\u0275\u0275textInterpolate1"]("",e.value.percentage,"%"),s["\u0275\u0275advance"](2),s["\u0275\u0275textInterpolate"](e.value.id),s["\u0275\u0275advance"](7),s["\u0275\u0275textInterpolate1"](" ",e.value.name,"")}}function Ee(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"div",45),s["\u0275\u0275elementStart"](1,"div"),s["\u0275\u0275template"](2,Ie,15,4,"div",13),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,i=s["\u0275\u0275nextContext"](2);s["\u0275\u0275property"]("formArray",i.manualPercentage),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("ngIf",e.value.id)}}function Fe(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"div",43),s["\u0275\u0275template"](1,Ee,3,2,"div",44),s["\u0275\u0275elementEnd"]()),2&e){const e=s["\u0275\u0275nextContext"]();s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngForOf",e.manualPercentage.controls)}}function Ae(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"div",54),s["\u0275\u0275text"](1),s["\u0275\u0275elementEnd"]()),2&e){const e=s["\u0275\u0275nextContext"]();s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate2"]("",e.existingGLName," - ",e.existingGLCode," already exist !")}}function ke(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"div",54),s["\u0275\u0275text"](1,"Please fill all the mandatory fields to submit!"),s["\u0275\u0275elementEnd"]())}function Te(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"mat-icon"),s["\u0275\u0275text"](1," done_all"),s["\u0275\u0275elementEnd"]())}function Ve(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"span",57),s["\u0275\u0275elementStart"](1,"span",58),s["\u0275\u0275text"](2,"Loading..."),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]())}function Re(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"span"),s["\u0275\u0275elementStart"](1,"button",55),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"](2).submitConfiguration()})),s["\u0275\u0275template"](2,Te,2,0,"mat-icon",13),s["\u0275\u0275template"](3,Ve,3,0,"span",56),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275nextContext"](2);s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("disabled",!e.form.valid||e.manualValidity||!e.manualPercentage.valid||e.onSubmitValidation),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",0==e.onSubmitForm),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",1==e.onSubmitForm)}}function Ne(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"span"),s["\u0275\u0275template"](1,Re,4,3,"span",13),s["\u0275\u0275elementEnd"]()),2&e){const e=s["\u0275\u0275nextContext"]();s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",null!=e.form)}}function je(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"mat-icon"),s["\u0275\u0275text"](1," done_all"),s["\u0275\u0275elementEnd"]())}function Ge(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"span",57),s["\u0275\u0275elementStart"](1,"span",58),s["\u0275\u0275text"](2,"Loading..."),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]())}function Ue(e,t){if(1&e){const e=s["\u0275\u0275getCurrentView"]();s["\u0275\u0275elementStart"](0,"span"),s["\u0275\u0275elementStart"](1,"button",55),s["\u0275\u0275listener"]("click",(function(){return s["\u0275\u0275restoreView"](e),s["\u0275\u0275nextContext"](2).submitConfiguration()})),s["\u0275\u0275template"](2,je,2,0,"mat-icon",13),s["\u0275\u0275template"](3,Ge,3,0,"span",56),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()}if(2&e){const e=s["\u0275\u0275nextContext"](2);s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("disabled",!e.form.valid||e.onSubmitValidation),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",0==e.onSubmitForm),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",1==e.onSubmitForm)}}function Le(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"span"),s["\u0275\u0275template"](1,Ue,4,3,"span",13),s["\u0275\u0275elementEnd"]()),2&e){const e=s["\u0275\u0275nextContext"]();s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",null!=e.form)}}let ze=(()=>{class e{constructor(e,t,i,n,r,o,l,c){if(this.editRuleDialog=e,this.editGLdialog=t,this.selectedItem=i,this._misService=n,this._auth=r,this.logDialog=o,this.formBuilder=l,this.snackBar=c,this.onSubmit=new s.EventEmitter,this.noToRetrieveForm=new s.EventEmitter,this.fields=[],this.$searchKeyUp=new a.b,this.manualPercentage=null,this.manualPercentageDisplay=!1,this.manualScrollIndex=0,this.showSubmit=!1,this.onSubmitValidation=!1,this.duplicateCostCenterAlert=!1,this.enableEditOnManualPercentage=!1,this.showAlrearyExistAlert=!1,this.onSubmitForm=!1,this.misFields=i.misFieldData,0==i.editForm)for(let a in this.misFields)null!=this.misFields[a].parent_dependent&&(this.misFields[a].selected_value=null,this.misFields[a].is_visible=0);let d={};for(let a of this.selectedItem.misFieldData)d[a.field_name]=new I.j(a.no_to_retrieve||null),"true"==a.is_required&&d[a.field_name].setValidators([I.H.required]),"is_cc_split_up_allowed"==a.field_name&&d[a.field_name].patchValue(0==this.selectedItem.editForm?1:i.ruleTableDetails.is_cc_split_up_allowed);this.form=new I.m(d),this.showSubmit=!1,this.manualGroup=this.formBuilder.group({manual_percent:["",I.H.required],manual_cost:["",I.H.required]}),null!=i.ruleTableDetails&&this.form.controls.is_active.patchValue(i.ruleTableDetails.is_active),this._misService.$clearManualPercentage.subscribe(()=>{this.manualPercentage=null,this.clearManualData()}),this._misService.$toggleVisibility.subscribe(e=>{this.form.controls.is_active.patchValue(e)}),this._misService.$determineRuleCode.subscribe(e=>{""==e&&null==e||(this.manualPercentage=e=>{this.formBuilder.array([])},this.getManualPercentage())}),null!=i.ruleTableDetails&&null!=i.ruleTableDetails.manual_percentage&&(this.getManualPercentage(),this.manualPercentageDisplay=!0,i.ruleTableDetails.manual_percentage.forEach(e=>{this.manualGroup.controls.manual_cost.patchValue({id:e.id,name:e.name,cost_center_id:e.cost_center_id}),this.manualGroup.controls.manual_percent.patchValue(e.percentage),this.addManualPercent()})),this.misFields=i.misFieldData,this.misFields.forEach(e=>{"cost_center"==e.field_name&&(this.manualPercentageArray=e.options)}),this.$searchKeyUp.pipe(Object(j.a)(1e3),Object(G.a)()).subscribe(e=>{this.getSearchResults(e)})}ngOnInit(){this.manualValidity=null!=this.fieldArray&&!this.fieldArray.valid,this.showSubmit=!0}noToRetrieveToForm(e){this.noToRetrieveForm.emit(e)}getErrorOnValidation(){this.manualDetailInvalid=[];const e=this.form.controls;for(const t in e)e[t].invalid&&(this.validationOnSubmit=!0,this.manualDetailInvalid.push(t));return 1==this.manualPercentageDisplay&&0==this.manualPercentage.valid&&this.manualDetailInvalid.push("manual percentage"),this.manualDetailInvalid}onScrollManualCostCenter(e,t){e&&this[t].panel.nativeElement.addEventListener("scroll",e=>Object(o.c)(this,void 0,void 0,(function*(){let e=Math.ceil(this[t].panel.nativeElement.scrollTop),i=this[t].panel.nativeElement.scrollHeight-this[t].panel.nativeElement.offsetHeight;if(e-1==i||e==i||e+1==i){this.manualScrollIndex+=15,this.manualNoToRetrives=this.manualPercentage.value.map(e=>e.id);let e=yield this._misService.getMasterEdit("cost_center",this.manualScrollIndex,this.manualNoToRetrives,this.manualPercentageSearch);this.manualPercentageArray=this.manualPercentageArray.concat(e.data)}})))}onManualCostCenterSearch(e){""!=e.target.value&&(this.manualPercentageArray=[],this.$searchKeyUp.next(e.target.value))}getSearchResults(e){return Object(o.c)(this,void 0,void 0,(function*(){this.manualPercentageSearch=e,this.searchManual=!0,this.manualNoToRetrives=this.manualPercentage.value.map(e=>e.id),null!=this.manualPercentage.value[0].id&&(this.manualPercentageArray=this.manualPercentage.value);let t=yield this._misService.getMasterEdit("cost_center",this.manualScrollIndex,this.manualNoToRetrives,e);this.manualPercentageArray=this.manualPercentageArray.concat(t.data)}))}closeSearch(e){return Object(o.c)(this,void 0,void 0,(function*(){this.searchManual=!1,this.manualPercentageArray=[],this.manualSearchValue=null,this.manualPercentageArray=this.manualPercentageArray.concat(this.manualNoToRetrives);let e=yield this._misService.getMasterEdit("cost_center",this.manualScrollIndex,this.manualNoToRetrives,null);this.manualPercentageArray=e.data}))}closeEditDialog(){this.editRuleDialog.close(),this.editGLdialog.close()}viewLogHistory(){return Object(o.c)(this,void 0,void 0,(function*(){const{LogHistoryComponent:e}=yield i.e(979).then(i.bind(null,"hg9u"));this.logDialog.open(e,{height:"60vh",width:"40vw",data:this.selectedItem.ruleTableDetails.id})}))}clearManualData(){this.manualPercentageDisplay=!1,this.manualPercentage=this.formBuilder.array([this.formBuilder.group({id:[null],name:[null],cost_center_id:[null],percentage:[null]})])}getManualPercentage(){this.manualPercentageDisplay=!0,this.manualPercentage=this.formBuilder.array([this.formBuilder.group({id:[null,I.H.required],name:[null,I.H.required],cost_center_id:[null,I.H.required],percentage:[null,I.H.required]})])}showAlertOnDuplicateCostCenter(){this.duplicateCostCenterAlert=!0,setTimeout(()=>this.duplicateCostCenterAlert=!1,2e3)}addManualPercent(){this.manualPercentageDisplay=!0,1!=this.manualPercentage.value.map(e=>e.id).includes(this.manualGroup.controls.manual_cost.value.id)?this.manualGroup.controls.manual_percent.value&&(null==this.manualPercentage.controls[0].value.id?this.manualPercentage.controls[0].patchValue({id:this.manualGroup.controls.manual_cost.value.id,name:this.manualGroup.controls.manual_cost.value.name,cost_center_id:this.manualGroup.controls.manual_cost.value.cost_center_id,percentage:this.manualGroup.controls.manual_percent.value}):this.manualPercentage.push(this.formBuilder.group({id:this.manualGroup.controls.manual_cost.value.id,name:this.manualGroup.controls.manual_cost.value.name,cost_center_id:this.manualGroup.controls.manual_cost.value.cost_center_id,percentage:this.manualGroup.controls.manual_percent.value}))):this.showAlertOnDuplicateCostCenter(),this.manualGroup.controls.manual_cost.patchValue(null),this.manualGroup.controls.manual_percent.patchValue(null),this.manualGroup.markAsUntouched()}percentageCardClicked(e,t,i){this.manualIndexToEdit=i,this.enableEditOnManualPercentage=!0;const n=this.manualPercentageArray.find(t=>t.id==e);this.manualGroup.controls.manual_cost.patchValue(n),this.manualGroup.controls.manual_percent.patchValue(t)}editManualPercent(){this.enableEditOnManualPercentage=!1,this.manualPercentage.controls[this.manualIndexToEdit].patchValue({id:this.manualGroup.controls.manual_cost.value.id,name:this.manualGroup.controls.manual_cost.value.name,cost_center_id:this.manualGroup.controls.manual_cost.value.cost_center_id,percentage:this.manualGroup.controls.manual_percent.value}),this.manualGroup.controls.manual_cost.patchValue(null),this.manualGroup.controls.manual_percent.patchValue(null),this.manualGroup.markAsUntouched()}deleteManualPercentage(e){1!=this.manualPercentage.value.length?this.manualPercentage.removeAt(e):this.getManualPercentage()}submitConfiguration(){return Object(o.c)(this,void 0,void 0,(function*(){if(this.showAlrearyExistAlert=!1,this.onSubmitForm=!0,"gl"==this.selectedItem.table)if(0==this.selectedItem.editForm){if(null!=this.form.value.gl_name){let e=yield this._misService.checkForUniqueGLName(this.form.value.gl_name);if(e.data.length>0)return this.existingGLName=e.data[0].gl_name,this.existingGLCode=e.data[0].gl_code,this.showAlrearyExistAlert=!0,void(this.onSubmitForm=!1);this.showAlrearyExistAlert=!1}}else if(this.selectedItem.ruleTableDetails.gl_name!=this.form.value.gl_name){let e=yield this._misService.checkForUniqueGLName(this.form.value.gl_name);if(e.data.length>0)return this.showAlrearyExistAlert=!0,this.existingGLName=e.data[0].gl_name,this.existingGLCode=e.data[0].gl_code,void(this.onSubmitForm=!1)}else this.showAlrearyExistAlert=!1;if(this.onSubmitValidation=!0,this.formValue=this.form.value,this.fields.forEach(e=>{"split-up"==e.retrieve_type&&e.options.filter(t=>{this.formValue[e.field_name]==t.id&&(this.formValue[e.split_up]=t.name)})}),1==this.selectedItem.editForm)this.onSubmitValidation=!0,null!=this.manualPercentage&&(this.formValue.manual_percentage=this.manualPercentage.value),this.formValue.id=this.selectedItem.ruleTableDetails.id,yield this.addToLogHistory(),yield this.updateLog(null);else{if(null!=this.manualPercentage&&(this.formValue.manual_percentage=this.manualPercentage.value),"ruleSheet"==this.selectedItem.table)for(let e in this.formValue)"entity_id"==e&&this.fields.filter(t=>{e==t.field_name&&t.options.filter(t=>{this.formValue[e]==t.id&&(this.entity_name=t.entity_code,this.formValue.entity_name=t.entity_code)})}),"gl_code"==e&&this.fields.filter(t=>{e==t.field_name&&t.options.filter(t=>{this.formValue[e]==t.id&&(this.entity_name=t.name,this.formValue.gl_description=t.name)})});yield this.insertConf(),this.editRuleDialog.close(),this.editGLdialog.close()}this.selectedItem.ruleTableDetails=this.formValue,this.onSubmitValidation=!1,this.onSubmitForm=!1}))}insertConf(){return Object(o.c)(this,void 0,void 0,(function*(){try{if("ruleSheet"==this.selectedItem.table){this.addToLogHistory();let e=yield this._misService.insertConfiguration(this.formValue);yield this.updateLog(e.data.insertId)}else if("gl"==this.selectedItem.table){this.misFields.forEach(e=>{null!=e.split_up&&(this.selectedName=null,e.options.map(t=>{t.id==e.selected_value&&(this.selectedName=t.name)}),this.form.controls[e.split_up].patchValue(this.selectedName))}),this.formValue=this.form.value,this.log={};let e=yield this._misService.insertGLConfiguration(this.formValue);this.formValue.gl_code=e.glCode,yield this.updateLog(e.data.insertId)}return Promise.resolve()}catch(e){}}))}updateLog(e){return Object(o.c)(this,void 0,void 0,(function*(){try{let r=this._auth.getProfile();this.log.changedBy=r.profile.name;var t=new Date,i=t.getFullYear()+"-"+(t.getMonth()+1)+"-"+t.getDate(),n=t.getHours()+":"+t.getMinutes()+":"+t.getSeconds();this.log.changedOn=i+" "+n;let o={};1==this.selectedItem.editForm?(o.id=this.selectedItem.ruleTableDetails.id,o.updateValue=this.log):(this.formValue.is_active=1,"ruleSheet"!=this.selectedItem.table?(this.log.new=this.formValue,this.log.is_active=1,this.log.old=null):this.log.new.is_active=1,o.id=e,o.updateValue=this.log),Object.keys(o.updateValue.new).length>0?(yield this.updateConfigLog(o),1==this.selectedItem.editForm&&(yield this.updateData()),this._misService.retainSearch(this.selectedItem.searchParam)):null==this.selectedItem.ruleTableDetails&&(1==this.selectedItem.editForm&&(yield this.updateConfigLog(o)),this._misService.retainSearch(this.selectedItem.searchParam)),0==Object.keys(o.updateValue.new).length&&1==this.selectedItem.editForm&&this.snackBar.open("No changes found to submit !","Dismiss",{duration:2e3});for(let e in this.log)delete this.log[e];return Promise.resolve()}catch(r){}}))}addToLogHistory(){this.log={},this.log.new={},this.log.old={};for(let e in this.formValue){let t=[];this.oldData=null,this.currentData=null;let i=null;if("manual_percentage"!=e&&(this.misFields.filter(n=>{if(n.field_name==e)return t=n.options,i=n.field_type,n.options}),Array.isArray(this.formValue[e])?(this.currentData=[],t.filter(t=>{this.formValue[e].filter(e=>{t.id==e&&this.currentData.push({id:t.id,name:t.name})})})):"text"!=i&&null!=t&&(this.currentData=t.filter(t=>{if(this.formValue[e]==t.id)return{id:t.id,name:t.name}})),null!=this.selectedItem.ruleTableDetails&&(Array.isArray(this.selectedItem.ruleTableDetails[e])?(this.oldData=[],t.filter(t=>{this.selectedItem.ruleTableDetails[e].filter(e=>{t.id==e&&this.oldData.push({id:t.id,name:t.name})})})):"text"!=i&&null!=t&&(this.oldData=t.filter(t=>{if(this.selectedItem.ruleTableDetails[e]==t.id)return{id:t.id,name:t.name}})))),0==this.selectedItem.editForm&&"ruleSheet"==this.selectedItem.table)this.log.new[e]="manual_percentage"==e?this.formValue[e]:this.currentData;else if("manual_percentage"==e){if(null!=this.formValue[e][0].id&&null!=this.selectedItem.ruleTableDetails[e]){let t=this.formValue[e].map(e=>e.cost_center_id),i=this.selectedItem.ruleTableDetails[e].map(e=>e.cost_center_id),n=t.filter(e=>-1===i.indexOf(e)),r=i.filter(e=>-1===t.indexOf(e));this.formValue[e].filter(t=>{this.selectedItem.ruleTableDetails[e].filter(e=>{t.id==e.id&&t.percentage!=e.percentage&&n.push(t.percentage)})}),(n.length>=1||r.length>=1)&&(this.log.new[e]=this.formValue[e],this.log.old[e]=this.selectedItem.ruleTableDetails[e])}}else if(Array.isArray(this.formValue[e])&&Array.isArray(this.selectedItem.ruleTableDetails[e])){let t=this.formValue[e].filter(t=>-1===this.selectedItem.ruleTableDetails[e].indexOf(t)),i=this.selectedItem.ruleTableDetails[e].filter(t=>-1===this.formValue[e].indexOf(t));this.currentData.length<1&&this.oldData.length<1?(t.length>=1||i.length>=1)&&(this.log.new[e]=this.formValue[e],this.log.old[e]=this.selectedItem.ruleTableDetails[e]):(t.length>=1||i.length>=1)&&(this.log.new[e]=this.currentData,this.log.old[e]=this.oldData)}else Array.isArray(this.formValue[e])||Array.isArray(this.selectedItem.ruleTableDetails[e])?(this.log.new[e]=this.currentData,this.log.old[e]=this.oldData):null==this.currentData&&null==this.oldData&&this.formValue[e]!=this.selectedItem.ruleTableDetails[e]?(this.log.new[e]=this.formValue[e],this.log.old[e]=this.selectedItem.ruleTableDetails[e]):this.formValue[e]!=this.selectedItem.ruleTableDetails[e]&&null!=this.formValue[e]&&""!=this.formValue[e]&&null!=this.selectedItem.ruleTableDetails[e]&&""!=this.selectedItem.ruleTableDetails[e]?Array.isArray(this.formValue[e])||Array.isArray(this.selectedItem.ruleTableDetails)?(this.log.new[e]=this.currentData,this.log.old[e]=this.oldData):null==this.currentData&&null==this.oldData||this.currentData.length<1&&this.oldData.length<1?(this.log.new[e]=this.formValue[e],this.log.old[e]=this.selectedItem.ruleTableDetails[e]):(this.log.new[e]=this.currentData,this.log.old[e]=this.oldData):null==this.formValue[e]&&null!=this.selectedItem.ruleTableDetails[e]||null!=this.formValue[e]&&null==this.selectedItem.ruleTableDetails[e]?(Array.isArray(this.formValue[e])||Array.isArray(this.selectedItem.ruleTableDetails),this.log.new[e]=this.currentData,this.log.old[e]=this.oldData):this.formValue[e]!=this.selectedItem.ruleTableDetails[e]&&(this.log.new[e]=this.formValue[e],this.log.old[e]=this.selectedItem.ruleTableDetails[e])}return Promise.resolve()}updateConfigLog(e){return Object(o.c)(this,void 0,void 0,(function*(){try{return"ruleSheet"==this.selectedItem.table?yield this._misService.updateLogMisRuleData(e):"gl"==this.selectedItem.table&&(yield this._misService.updateLogGlAccountData(e)),Promise.resolve()}catch(t){}}))}updateData(){return Object(o.c)(this,void 0,void 0,(function*(){try{return"ruleSheet"==this.selectedItem.table?(this.selectedItem.misFieldData.forEach(e=>{if("gl_code"==e.field_name){let t=e.options.filter(e=>{if(e.id==this.selectedItem.ruleTableDetails.gl_code)return e});this.glName=t[0].name}}),this.formValue.gl_name=this.glName,yield this._misService.updateConfiguration(this.formValue,this.selectedItem.searchParam),this.editRuleDialog.close()):"gl"==this.selectedItem.table&&(yield this._misService.updateGLConfiguration(this.formValue,this.selectedItem.searchParam),this.editGLdialog.close()),Promise.resolve()}catch(e){}}))}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275directiveInject"](f.h),s["\u0275\u0275directiveInject"](f.h),s["\u0275\u0275directiveInject"](f.a),s["\u0275\u0275directiveInject"](d.a),s["\u0275\u0275directiveInject"](U.a),s["\u0275\u0275directiveInject"](f.b),s["\u0275\u0275directiveInject"](I.i),s["\u0275\u0275directiveInject"](g.a))},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-mis-form-builder"]],viewQuery:function(e,t){if(1&e&&s["\u0275\u0275viewQuery"](_e,!0),2&e){let e;s["\u0275\u0275queryRefresh"](e=s["\u0275\u0275loadQuery"]())&&(t.onScroll=e.first)}},inputs:{fields:"fields",fieldArray:"fieldArray"},outputs:{onSubmit:"onSubmit",noToRetrieveForm:"misEvent"},decls:18,vars:10,consts:[[1,"container-fluid","mt-4","mis-form-builder"],[3,"formGroup","ngSubmit"],[1,"row"],[4,"ngFor","ngForOf"],["class","row mt-4 pl-2",4,"ngIf"],["class","row pt-3 pl-3",3,"formGroup",4,"ngIf"],["class","row pl-3",4,"ngIf"],[1,"row","pt-4"],[1,"col-9"],["class","pt-1 in-valid",4,"ngIf"],[1,"col-1","p-0"],["mat-icon-button","","matTooltip","Cancel","type","submit",1,"iconbtnClose",3,"disabled","click"],[1,"col-1","p-0",3,"click"],[4,"ngIf"],["class","col-6","style","width:100%",4,"ngIf"],[1,"col-6",2,"width","100%"],[3,"field","form","misEvent"],[1,"row","mt-4","pl-2"],[1,"col-3","p-0","section"],[1,"col"],["class","alert",4,"ngIf"],[1,"alert"],[1,"row","pt-3","pl-3",3,"formGroup"],[1,"col-5"],["appearance","outline",2,"width","100%"],["formControlName","manual_cost",3,"openedChange"],["onScroll",""],[2,"width","100%"],[1,"pl-1","icon-search"],["matPrefix",""],["placeholder","Search here",1,"p-3","pl-0",2,"width","80%",3,"ngModel","ngModelOptions","ngModelChange","keyup"],[1,"icon-search-cancel"],["matSuffix","","class","mr-1",3,"click",4,"ngIf"],[1,"pl-5","col-5"],[1,"pl-2"],["formControlName","manual_percent","placeholder","Manual Percentage","matInput","",1,"input-field"],[1,"col-1","icon-add"],["mat-icon-button","",1,"add-icon",3,"disabled"],[3,"click",4,"ngIf"],["matSuffix","",1,"mr-1",3,"click"],[3,"value",4,"ngFor","ngForOf"],[3,"value"],[3,"click"],[1,"row","pl-3"],["class","col-3 p-0 mr-2 mt-2",3,"formArray",4,"ngFor","ngForOf"],[1,"col-3","p-0","mr-2","mt-2",3,"formArray"],[1,"p-0","mr-3",2,"cursor","pointer",3,"matTooltip"],[1,"row","pt-1"],[1,"col","pt-2","pr-2","pl-1","manual-card",3,"click"],[1,"pl-1","pr-2",2,"color","#cf0001"],[1,"pr-2",2,"color","#cf0001"],[1,"col-1","d-flex","justify-content-end","p-0"],[1,"row","pb-1","pt-2","d-flex","justify-content-center"],[1,"col","manual-card",3,"click"],[1,"pt-1","in-valid"],["mat-icon-button","","matTooltip","Submit Rule","type","submit",1,"iconbtnSubmit",3,"disabled","click"],["class","spinner-border","role","status",4,"ngIf"],["role","status",1,"spinner-border"],[1,"sr-only"]],template:function(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"div",0),s["\u0275\u0275elementStart"](1,"form",1),s["\u0275\u0275listener"]("ngSubmit",(function(){return t.onSubmit.emit(t.form.value)})),s["\u0275\u0275elementStart"](2,"div",2),s["\u0275\u0275template"](3,be,2,1,"ng-container",3),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](4,ye,5,1,"div",4),s["\u0275\u0275template"](5,we,24,9,"div",5),s["\u0275\u0275template"](6,Fe,2,1,"div",6),s["\u0275\u0275elementStart"](7,"div",7),s["\u0275\u0275elementStart"](8,"div",8),s["\u0275\u0275template"](9,Ae,2,2,"div",9),s["\u0275\u0275template"](10,ke,2,0,"div",9),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](11,"div",10),s["\u0275\u0275elementStart"](12,"button",11),s["\u0275\u0275listener"]("click",(function(){return t.closeEditDialog()})),s["\u0275\u0275elementStart"](13,"mat-icon"),s["\u0275\u0275text"](14,"close"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](15,"div",12),s["\u0275\u0275listener"]("click",(function(){return t.getErrorOnValidation()})),s["\u0275\u0275template"](16,Ne,2,1,"span",13),s["\u0275\u0275template"](17,Le,2,1,"span",13),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e&&(s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("formGroup",t.form),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("ngForOf",t.fields),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",1==t.manualPercentageDisplay),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",1==t.manualPercentageDisplay),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",1==t.manualPercentageDisplay),s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("ngIf",1==t.showAlrearyExistAlert),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",t.form.invalid&&1==t.validationOnSubmit),s["\u0275\u0275advance"](2),s["\u0275\u0275property"]("disabled",t.onSubmitValidation),s["\u0275\u0275advance"](4),s["\u0275\u0275property"]("ngIf",1==t.showSubmit&&1==t.manualPercentageDisplay),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngIf",1==t.showSubmit&&0==t.manualPercentageDisplay))},directives:[I.J,I.w,I.n,n.NgForOf,n.NgIf,R.a,P.a,O.a,ge,k.c,k.g,T.c,I.v,I.l,k.h,I.e,I.y,V.b,k.i,L.p,A.a],styles:[".mis-form-builder[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .mis-form-builder[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus{border:none!important;outline:none!important;caret-color:#cf0001}.mis-form-builder[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{color:#cf0001;font-size:20px;vertical-align:middle}.mis-form-builder[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%], .mis-form-builder[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]{color:#cf0001;font-size:14px;font-family:Roboto;font-weight:500}.mis-form-builder[_ngcontent-%COMP%]   .icon-add[_ngcontent-%COMP%]{color:#66615b;vertical-align:middle;cursor:pointer}.mis-form-builder[_ngcontent-%COMP%]   .icon-cancel[_ngcontent-%COMP%]{color:#a1a1a2;font-size:20px;cursor:pointer}.mis-form-builder[_ngcontent-%COMP%]   .icon-search[_ngcontent-%COMP%], .mis-form-builder[_ngcontent-%COMP%]   .icon-search-cancel[_ngcontent-%COMP%]{color:#a1a1a2;font-size:20px;vertical-align:middle}.mis-form-builder[_ngcontent-%COMP%]   .icon-search-cancel[_ngcontent-%COMP%]{cursor:pointer}.mis-form-builder[_ngcontent-%COMP%]   .submit[_ngcontent-%COMP%]{cursor:pointer;background-color:#cf0001;color:#fff}.mis-form-builder[_ngcontent-%COMP%]   .cancel[_ngcontent-%COMP%]{cursor:pointer;background-color:#7f7f81;color:#fff}.mis-form-builder[_ngcontent-%COMP%]   .submit-btn[_ngcontent-%COMP%]{background:linear-gradient(44deg,#e86565 10%,#cf0001 105.29%);border-radius:8px;color:#fff}.mis-form-builder[_ngcontent-%COMP%]   .col-5[_ngcontent-%COMP%]{padding:0}.mis-form-builder[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]{width:100%}.mis-form-builder[_ngcontent-%COMP%]   .disabled-contenct[_ngcontent-%COMP%]{pointer-events:none;opacity:.4}.mis-form-builder[_ngcontent-%COMP%]   .action-buttons[_ngcontent-%COMP%]{cursor:pointer;font-size:25px;color:#526179;vertical-align:middle}.mis-form-builder[_ngcontent-%COMP%]   .align-middle[_ngcontent-%COMP%]{vertical-align:middle}.mis-form-builder[_ngcontent-%COMP%]   .click[_ngcontent-%COMP%]{background-color:#f9f8f3}.mis-form-builder[_ngcontent-%COMP%]   .mat-fab-icon[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff}.mis-form-builder[_ngcontent-%COMP%]   .mat-mini-fab[_ngcontent-%COMP%]{height:30px;width:31px}.mis-form-builder[_ngcontent-%COMP%]   .in-valid[_ngcontent-%COMP%]{color:#cf0001;font-size:14px}.mis-form-builder[_ngcontent-%COMP%]   .manual-card[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;vertical-align:middle}.mis-form-builder[_ngcontent-%COMP%]   .add-icon[_ngcontent-%COMP%]{cursor:pointer;color:#cf0001;font-size:30px}.mis-form-builder[_ngcontent-%COMP%]     .mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-bar, .mis-form-builder[_ngcontent-%COMP%]     .mat-slide-toggle.mat-checked:not(.mat-disabled) .mat-slide-toggle-thumb{background-color:#cf0001}.mis-form-builder[_ngcontent-%COMP%]   .iconbtn[_ngcontent-%COMP%]{background-color:#c92020;color:#fff;padding:0}.mis-form-builder[_ngcontent-%COMP%]   .icon-add[_ngcontent-%COMP%]   .my-mini-fab[_ngcontent-%COMP%]{width:10px;height:10px;line-height:14px;font-size:5px}.mis-form-builder[_ngcontent-%COMP%]   .icon-add[_ngcontent-%COMP%]   .my-mini-fab[_ngcontent-%COMP%]  .mat-button-wrapper{line-height:10px;padding:0}.mis-form-builder[_ngcontent-%COMP%]   .icon-add[_ngcontent-%COMP%]   .my-mini-fab[_ngcontent-%COMP%]  .mat-button-wrapper .mat-icon{font-size:5px;padding-right:4px;padding-top:4px}.mis-form-builder[_ngcontent-%COMP%]   .iconbtnSubmit[_ngcontent-%COMP%]{background-color:#c92020}.mis-form-builder[_ngcontent-%COMP%]   .iconbtnClose[_ngcontent-%COMP%], .mis-form-builder[_ngcontent-%COMP%]   .iconbtnSubmit[_ngcontent-%COMP%]{cursor:pointer;color:#fff;padding:0;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.mis-form-builder[_ngcontent-%COMP%]   .iconbtnClose[_ngcontent-%COMP%]{background-color:#a1a1a2}.mis-form-builder[_ngcontent-%COMP%]   .restrict-cursor[_ngcontent-%COMP%]{cursor:not-allowed;pointer-events:none;background-color:#c84c4c;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.mis-form-builder[_ngcontent-%COMP%]   .history-btn[_ngcontent-%COMP%]   .mat-mini-fab[_ngcontent-%COMP%]{height:26px;width:27px}.mis-form-builder[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]{color:#cf0001}.mis-form-builder[_ngcontent-%COMP%]   .spinner-border[_ngcontent-%COMP%]{color:#fff}input[_ngcontent-%COMP%], input[_ngcontent-%COMP%]:focus{border:none!important;outline:none!important;caret-color:#cf0001}.icon-search[_ngcontent-%COMP%], .icon-search-cancel[_ngcontent-%COMP%]{color:#a1a1a2;font-size:20px;vertical-align:middle}.icon-search-cancel[_ngcontent-%COMP%]{cursor:pointer}"]}),e})();var Be=i("QbZZ"),$e=i("Q+U5"),qe=i("ZzPI");let He=(()=>{class e{}return e.\u0275mod=s["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=s["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[n.CommonModule,D,w.a,T.d,I.E,I.p,V.c,k.e,O.b,P.b,R.b,A.d,N.b,F.b,E.g,m.b,g.b,qe.b,f.g]]}),e})();s["\u0275\u0275setComponentScope"](Be.EditRuleSheetComponent,[O.a,n.NgIf,N.a,P.a,R.a,ze],[]),s["\u0275\u0275setComponentScope"]($e.EditGlAccountComponent,[O.a,n.NgIf,N.a,P.a,R.a,ze],[])},ucYs:function(e,t,i){"use strict";i.d(t,"a",(function(){return s}));var n=i("mrSG"),r=i("xG9w"),o=i("fXoL"),a=i("tk/3"),l=i("BVzC");let s=(()=>{class e{constructor(e,t){this.http=e,this.errorService=t,this.workflowStatusList=[],this.getWorkflowStatusList()}getWorkflowStatusList(){this.http.post("/api/wfPrimary/getWorkflowStatusList","").subscribe(e=>{this.workflowStatusList=e.data},e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server While Getting Workflow Status List",e&&e.params?e.params:e&&e.error?e.error.params:{})})}getWorkflowProperties(e){return new Promise((t,i)=>{this.http.post("/api/wfPrimary/getWorkflowProperties",{applicationId:e}).subscribe(e=>t(e),e=>i(e))})}getWorkflowPropertiesByWorkflowId(e){return new Promise((t,i)=>{this.http.post("/api/wfPrimary/getWorkflowPropertiesByWorkflowId",{workflowId:e}).subscribe(e=>t(e),e=>i(e))})}getApproversHierarchy(e){return new Promise((t,i)=>{this.http.post("/api/wfPrimary/getApproversHierarchy",e).subscribe(e=>t(e),e=>i(e))})}createWorkflowItems(e){return new Promise((t,i)=>{this.http.post("/api/wfPrimary/createWorkflowItems",e).subscribe(e=>t(e),e=>i(e))})}getWorkflowDetails(e){return new Promise((t,i)=>{this.http.post("/api/wfPrimary/getWorkflowDetails",{workflowId:e}).subscribe(e=>t(e),e=>{this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Error while getting workflow details",e&&e.params?e.params:e&&e.error?e.error.params:{})})})}formatApproversHierarchy(e,t){return Object(n.c)(this,void 0,void 0,(function*(){0==e.length&&t.cc0&&t.cc0.appr0&&t.cc0.appr0.length>0&&e.push([{approvalType:"I",purposeOfInsertingIntoArray:"Just to ensure that 'I' type data is used, if it exists"}]);for(let i=0;i<e.length;i++){let n=[],o=r.keys(t["cc"+i]);for(let r=0;r<o.length;r++)for(let a=0;a<t["cc"+i][o[r]].length;a++){let l={name:t["cc"+i][o[r]][a].DELEGATE_NAME,oid:t["cc"+i][o[r]][a].DELEGATE_OID,level:r+1,designation:t["cc"+i][o[r]][a].DELEGATE_DESIGNATION_NAME,is_delegated:t["cc"+i][o[r]][a].IS_DELEGATED,role:t["cc"+i][o[r]][a].DELEGATE_ROLE_NAME};if(1==t["cc"+i][o[r]][a].IS_DELEGATED&&(l.delegated_by={name:t["cc"+i][o[r]][a].APPROVER_NAME,oid:t["cc"+i][o[r]][a].APPROVER_OID,level:r+1,designation:t["cc"+i][o[r]][a].APPROVER_DESIGNATION_NAME}),n.push(l),i==e.length-1&&r==o.length-1&&a==t["cc"+i][o[r]].length-1)return n}}}))}storeComments(e,t,i){return new Promise((n,r)=>{this.http.post("/api/wfPrimary/updateComments",{workflowHeaderId:e,newComments:t,commentor:i}).subscribe(e=>n(e),e=>(this.errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Internal Server Error while Updating Workflow Comments",e&&e.params?e.params:e&&e.error?e.error.params:{}),r(e)))})}updateWorkflowItems(e){return new Promise((t,i)=>{this.http.post("/api/wfPrimary/updateWorkflowItems",e).subscribe(e=>t(e),e=>(console.log(e),i(e)))})}formatApproversHierarchyForOpportunityApprovalActivity(e){return Object(n.c)(this,void 0,void 0,(function*(){for(let t=0;t<1;t++){let i=[],n=r.keys(e["cc"+t]);for(let r=0;r<n.length;r++)for(let o=0;o<e["cc"+t][n[r]].length;o++){let a={name:e["cc"+t][n[r]][o].DELEGATE_NAME,oid:e["cc"+t][n[r]][o].DELEGATE_OID,level:e["cc"+t][n[r]][o].APPROVAL_ORDER,designation:e["cc"+t][n[r]][o].DELEGATE_DESIGNATION_NAME,is_delegated:e["cc"+t][n[r]][o].IS_DELEGATED};if(1==e["cc"+t][n[r]][o].IS_DELEGATED&&(a.delegated_by={name:e["cc"+t][n[r]][o].APPROVER_NAME,oid:e["cc"+t][n[r]][o].APPROVER_OID,level:e["cc"+t][n[r]][o].APPROVAL_ORDER,designation:e["cc"+t][n[r]][o].APPROVER_DESIGNATION_NAME}),i.push(a),r==n.length-1&&o==e["cc"+t][n[r]].length-1)return i}}}))}}return e.\u0275fac=function(t){return new(t||e)(o["\u0275\u0275inject"](a.c),o["\u0275\u0275inject"](l.a))},e.\u0275prov=o["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()}}]);