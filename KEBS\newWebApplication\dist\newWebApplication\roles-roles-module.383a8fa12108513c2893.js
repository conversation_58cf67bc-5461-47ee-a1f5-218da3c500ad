(window.webpackJsonp=window.webpackJsonp||[]).push([[869],{IRv6:function(e,t,n){"use strict";n.d(t,"a",(function(){return o}));var i=n("fXoL");let o=(()=>{class e{constructor(){}ngOnInit(){}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-setting-header-overall"]],decls:5,vars:0,consts:[[1,"header-container"],[1,"header-title"],[1,"header-sub-title"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275elementStart"](1,"div",1),i["\u0275\u0275text"](2,"Overall Setup"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](3,"div",2),i["\u0275\u0275text"](4," Empower Your Experience: Customize Your Preferences for Effortless Management "),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]())},styles:[".header-container[_ngcontent-%COMP%]{background:#fff;padding:10px 10px 10px 15px}.header-container[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:16px;font-weight:700;line-height:20.83px;text-align:left;color:#111434;height:21px}.header-container[_ngcontent-%COMP%]   .header-sub-title[_ngcontent-%COMP%]{color:#8b95a5;font-family:var(--atsfontFamily);font-size:11px;font-weight:400;line-height:16px;letter-spacing:.02em;text-align:left;height:16px}"]}),e})()},IeBn:function(e,t,n){"use strict";n.d(t,"a",(function(){return g}));var i=n("fXoL"),o=n("3Pt+"),r=n("ofXK");const a=["inputField"];function c(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275namespaceSVG"](),i["\u0275\u0275elementStart"](0,"svg",9),i["\u0275\u0275listener"]("click",(function(){return i["\u0275\u0275restoreView"](e),i["\u0275\u0275nextContext"]().onEnterKeyPressed()})),i["\u0275\u0275element"](1,"path",10),i["\u0275\u0275elementEnd"]()}}function s(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275namespaceSVG"](),i["\u0275\u0275elementStart"](0,"svg",11),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const t=i["\u0275\u0275nextContext"]();return t.searchText="",t.onEnterKeyPressed()})),i["\u0275\u0275elementStart"](1,"g",12),i["\u0275\u0275element"](2,"path",13),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](3,"defs"),i["\u0275\u0275elementStart"](4,"clipPath",14),i["\u0275\u0275element"](5,"rect",15),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()}}function l(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"div",19),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const n=t.index;return i["\u0275\u0275nextContext"](2).onSelectRecentSearch(n)})),i["\u0275\u0275elementStart"](1,"div",20),i["\u0275\u0275namespaceSVG"](),i["\u0275\u0275elementStart"](2,"svg",21),i["\u0275\u0275elementStart"](3,"mask",22),i["\u0275\u0275element"](4,"rect",23),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"g",24),i["\u0275\u0275element"](6,"path",25),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275namespaceHTML"](),i["\u0275\u0275element"](7,"div",26),i["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=i["\u0275\u0275nextContext"](2);i["\u0275\u0275advance"](7),i["\u0275\u0275property"]("innerHTML",n.highlightSearch(e),i["\u0275\u0275sanitizeHtml"])}}function d(e,t){if(1&e&&(i["\u0275\u0275elementContainerStart"](0),i["\u0275\u0275element"](1,"div",16),i["\u0275\u0275elementStart"](2,"span",17),i["\u0275\u0275text"](3,"Recently Searched"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](4,l,8,1,"div",18),i["\u0275\u0275elementContainerEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](4),i["\u0275\u0275property"]("ngForOf",e.recentSearch)}}let g=(()=>{class e{constructor(){this.onEnter=new i.EventEmitter,this.searchText=""}ngOnInit(){this.currentSearchText&&""!=this.currentSearchText&&(this.searchText=this.currentSearchText)}ngAfterViewInit(){this.inputField&&this.inputField.nativeElement&&this.inputField.nativeElement.focus()}highlightSearch(e){if(!this.searchText)return e;let t=this.searchText.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");const n=new RegExp(t,"gi");return e.replace(n,e=>`<b>${e}</b>`)}onEnterKeyPressed(){this.onEnter.emit(this.searchText.trim())}onSelectRecentSearch(e){this.searchText=this.recentSearch[e],this.onEnterKeyPressed()}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-search-overlay"]],viewQuery:function(e,t){if(1&e&&i["\u0275\u0275viewQuery"](a,!0),2&e){let e;i["\u0275\u0275queryRefresh"](e=i["\u0275\u0275loadQuery"]())&&(t.inputField=e.first)}},inputs:{recentSearch:"recentSearch",currentSearchText:"currentSearchText"},outputs:{onEnter:"onEnter"},decls:9,vars:4,consts:[[1,"bg-container"],[1,"d-flex","align-items-center","justify-content-between"],[1,"search-bar"],["type","text","placeholder","Search Here...",3,"ngModel","ngModelChange","keydown.enter"],["inputField",""],[2,"cursor","pointer"],["width","18","height","18","viewBox","0 0 18 18","fill","none",3,"click",4,"ngIf"],["style","cursor: pointer","width","18","height","18","viewBox","0 0 18 18","fill","none",3,"click",4,"ngIf"],[4,"ngIf"],["width","18","height","18","viewBox","0 0 18 18","fill","none",3,"click"],["d","M7.01916 12.6153C5.31148 12.6153 3.86533 12.023 2.68073 10.8384C1.49612 9.65383 0.903809 8.20768 0.903809 6.49999C0.903809 4.79231 1.49612 3.34616 2.68073 2.16154C3.86533 0.976943 5.31148 0.384644 7.01916 0.384644C8.72684 0.384644 10.173 0.976943 11.3576 2.16154C12.5422 3.34616 13.1345 4.79231 13.1345 6.49999C13.1345 7.21409 13.0147 7.89614 12.7749 8.54614C12.5352 9.19614 12.2153 9.76153 11.8153 10.2423L17.5692 15.9961C17.7076 16.1346 17.7784 16.3087 17.7816 16.5183C17.7849 16.7279 17.714 16.9051 17.5692 17.05C17.4243 17.1948 17.2486 17.2673 17.0422 17.2673C16.8358 17.2673 16.6602 17.1948 16.5153 17.05L10.7615 11.2961C10.2615 11.7089 9.68646 12.032 9.03646 12.2653C8.38646 12.4987 7.71403 12.6153 7.01916 12.6153ZM7.01916 11.1154C8.30763 11.1154 9.39897 10.6683 10.2932 9.77404C11.1874 8.87981 11.6346 7.78846 11.6346 6.49999C11.6346 5.21153 11.1874 4.12018 10.2932 3.22594C9.39897 2.33171 8.30763 1.88459 7.01916 1.88459C5.73069 1.88459 4.63934 2.33171 3.74511 3.22594C2.85089 4.12018 2.40378 5.21153 2.40378 6.49999C2.40378 7.78846 2.85089 8.87981 3.74511 9.77404C4.63934 10.6683 5.73069 11.1154 7.01916 11.1154Z","fill","#45546E"],["width","18","height","18","viewBox","0 0 18 18","fill","none",2,"cursor","pointer",3,"click"],["clip-path","url(#clip0_22386_12410)"],["d","M8.00048 7.05781L11.3005 3.75781L12.2431 4.70048L8.94315 8.00048L12.2431 11.3005L11.3005 12.2431L8.00048 8.94315L4.70048 12.2431L3.75781 11.3005L7.05781 8.00048L3.75781 4.70048L4.70048 3.75781L8.00048 7.05781Z","fill","#6E7B8F"],["id","clip0_22386_12410"],["width","16","height","16","fill","white"],[1,"divider"],[1,"recent-search-title"],["class","d-flex align-items-center search-text-list",3,"click",4,"ngFor","ngForOf"],[1,"d-flex","align-items-center","search-text-list",3,"click"],[2,"margin-bottom","1px"],["width","12","height","12","viewBox","0 0 12 12","fill","none"],["id","mask0_9594_64568","maskUnits","userSpaceOnUse","x","0","y","0","width","12","height","12",2,"mask-type","alpha"],["width","12","height","12","fill","#D9D9D9"],["mask","url(#mask0_9594_64568)"],["d","M5.99166 10.25C4.90897 10.25 3.96538 9.89357 3.1609 9.18075C2.35641 8.46793 1.89328 7.57434 1.77148 6.49999H2.53685C2.66313 7.36345 3.05175 8.07931 3.70271 8.64759C4.35368 9.21586 5.11666 9.49999 5.99166 9.49999C6.96666 9.49999 7.79374 9.16041 8.47291 8.48124C9.15208 7.80207 9.49166 6.97499 9.49166 5.99999C9.49166 5.02499 9.15208 4.1979 8.47291 3.51874C7.79374 2.83957 6.96666 2.49999 5.99166 2.49999C5.44551 2.49999 4.93365 2.6213 4.45608 2.86393C3.97853 3.10655 3.56731 3.44036 3.22243 3.86536H4.53012V4.61535H1.9917V2.07691H2.74167V3.26154C3.14744 2.7827 3.63333 2.41106 4.19936 2.14664C4.76538 1.88221 5.36282 1.75 5.99166 1.75C6.5814 1.75 7.13396 1.86154 7.64935 2.08463C8.16472 2.3077 8.61408 2.6109 8.99741 2.99423C9.38074 3.37756 9.68395 3.82692 9.90702 4.3423C10.1301 4.85768 10.2416 5.41025 10.2416 5.99999C10.2416 6.58973 10.1301 7.14229 9.90702 7.65768C9.68395 8.17306 9.38074 8.62242 8.99741 9.00575C8.61408 9.38908 8.16472 9.69228 7.64935 9.91535C7.13396 10.1384 6.5814 10.25 5.99166 10.25ZM7.49262 8.01344L5.63108 6.15191V3.49999H6.38107V5.84806L8.01953 7.48654L7.49262 8.01344Z","fill","#8B95A5"],[1,"recent-search-text",3,"innerHTML"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275elementStart"](1,"div",1),i["\u0275\u0275elementStart"](2,"div",2),i["\u0275\u0275elementStart"](3,"input",3,4),i["\u0275\u0275listener"]("ngModelChange",(function(e){return t.searchText=e}))("keydown.enter",(function(){return t.onEnterKeyPressed()})),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"div",5),i["\u0275\u0275template"](6,c,2,0,"svg",6),i["\u0275\u0275template"](7,s,6,0,"svg",7),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](8,d,5,1,"ng-container",8),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngModel",t.searchText),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("ngIf",""==t.searchText),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.searchText&&""!=t.searchText),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.recentSearch&&t.recentSearch.length>0))},directives:[o.e,o.v,o.y,r.NgIf,r.NgForOf],styles:[".bg-container[_ngcontent-%COMP%]{width:350px;padding:8px;border:2px solid #b9c0ca;border-radius:8px;box-shadow:0 4px 8px 0 rgba(0,0,0,.25098039215686274);background-color:#fff}.bg-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]{width:100%}.bg-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{height:100%;width:95%;outline:none;border:none}.bg-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .bg-container[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{font-family:var(--atsfontFamily);font-size:11px;font-weight:400;color:#6e7b8f}.bg-container[_ngcontent-%COMP%]   .divider[_ngcontent-%COMP%]{height:1px;width:100%;background:#dadce2;margin-bottom:8px;margin-top:8px}.bg-container[_ngcontent-%COMP%]   .search-text-list[_ngcontent-%COMP%]{cursor:pointer;gap:8px;width:-moz-fit-content;width:fit-content;margin-bottom:4px}.bg-container[_ngcontent-%COMP%]   .recent-search-title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:11px;font-style:italic;font-weight:400;color:#b9c0ca}.bg-container[_ngcontent-%COMP%]   .recent-search-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:11px;color:#8b95a5}"]}),e})()},Jzeh:function(e,t,n){"use strict";n.d(t,"a",(function(){return s}));var i=n("yuIm"),o=n("fXoL"),r=n("ofXK");function a(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"div",4),o["\u0275\u0275text"](1," Oops! You're not authorized to view this content. Contact your administrator for access. "),o["\u0275\u0275elementEnd"]())}function c(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"div",4),o["\u0275\u0275text"](1," KEBS System is unavailable, kindly try after sometime! "),o["\u0275\u0275elementEnd"]())}let s=(()=>{class e{constructor(){this.isRDSPeak=!1}ngOnInit(){this.isRDSPeak=null==i?void 0:i.is_rds_peak}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275cmp=o["\u0275\u0275defineComponent"]({type:e,selectors:[["app-access-denied"]],decls:5,vars:2,consts:[[1,"bg-container"],[1,"contents"],["src","https://assets.kebs.app/ATS-noAccess.png",1,"image-styles"],["class","message",4,"ngIf"],[1,"message"]],template:function(e,t){1&e&&(o["\u0275\u0275elementStart"](0,"div",0),o["\u0275\u0275elementStart"](1,"div",1),o["\u0275\u0275element"](2,"img",2),o["\u0275\u0275template"](3,a,2,0,"div",3),o["\u0275\u0275template"](4,c,2,0,"div",3),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&e&&(o["\u0275\u0275advance"](3),o["\u0275\u0275property"]("ngIf",!t.isRDSPeak),o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("ngIf",t.isRDSPeak))},directives:[r.NgIf],styles:[".bg-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:10px;align-items:center;justify-content:center;background-color:#fff;height:var(--dynamicAccessDeniedHeight)}.bg-container[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:500;color:#111434;text-align:center}.bg-container[_ngcontent-%COMP%]   .image-styles[_ngcontent-%COMP%]{height:200px;width:200px}.bg-container[_ngcontent-%COMP%]   .contents[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center}"]}),e})()},TWZM:function(e,t,n){"use strict";n.r(t),n.d(t,"RolesModule",(function(){return me}));var i=n("ofXK"),o=n("tyNb"),r=n("mrSG"),a=n("+rOU"),c=n("1G5W"),s=n("XNiG"),l=n("yuIm"),d=n("fXoL"),g=n("XNFG"),m=n("tk/3");let p=(()=>{class e{constructor(e){this._http=e}getAllRoles(e){return this._http.post("api/ats/rolesSettings/getAllRoles",{searchParams:e})}getPermissionsForRole(e){return this._http.post("api/ats/rolesSettings/getPermissionsForRole",{roleId:e})}updatePermissionsForRole(e,t){return this._http.post("api/ats/rolesSettings/updatePermissionsForRole",{roleId:e,accessPermission:t})}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275inject"](m.c))},e.\u0275prov=d["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();var h=n("rQiX"),f=n("rDax"),u=n("IRv6"),C=n("3Pt+"),v=n("vzmP"),x=n("Jzeh"),y=n("IeBn"),P=n("pEYl");const _=["triggerSearchBarTemplateRef"],O=["triggerSearchBar"];function w(e,t){1&e&&(d["\u0275\u0275namespaceSVG"](),d["\u0275\u0275elementStart"](0,"svg",27),d["\u0275\u0275element"](1,"path",28),d["\u0275\u0275elementEnd"]())}function M(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275namespaceSVG"](),d["\u0275\u0275elementStart"](0,"svg",29),d["\u0275\u0275listener"]("click",(function(t){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](4).onEnterSearch("",t)})),d["\u0275\u0275elementStart"](1,"g",30),d["\u0275\u0275element"](2,"path",31),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](3,"defs"),d["\u0275\u0275elementStart"](4,"clipPath",32),d["\u0275\u0275element"](5,"rect",33),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}}function b(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",19,20),d["\u0275\u0275listener"]("click",(function(){d["\u0275\u0275restoreView"](e);const t=d["\u0275\u0275reference"](2);return d["\u0275\u0275nextContext"](3).openSearchBarOverlay(t,-37,18)})),d["\u0275\u0275elementStart"](3,"div",21),d["\u0275\u0275elementStart"](4,"input",22,23),d["\u0275\u0275listener"]("ngModelChange",(function(t){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](3).searchParams=t}))("keydown.backspace",(function(){d["\u0275\u0275restoreView"](e);const t=d["\u0275\u0275nextContext"](3);return t.onEnterSearch(t.searchParams)})),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](6,"div",24),d["\u0275\u0275template"](7,w,2,0,"svg",25),d["\u0275\u0275template"](8,M,6,0,"svg",26),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"](3);d["\u0275\u0275advance"](4),d["\u0275\u0275property"]("ngModel",e.searchParams),d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("ngIf",""==e.searchParams),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.searchParams&&""!=e.searchParams)}}function S(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",34,20),d["\u0275\u0275listener"]("click",(function(){d["\u0275\u0275restoreView"](e);const t=d["\u0275\u0275reference"](2);return d["\u0275\u0275nextContext"](3).openSearchBarOverlay(t,-30,25)})),d["\u0275\u0275namespaceSVG"](),d["\u0275\u0275elementStart"](3,"svg",27),d["\u0275\u0275element"](4,"path",28),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}}function E(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",16),d["\u0275\u0275template"](1,b,9,3,"div",17),d["\u0275\u0275template"](2,S,5,0,"div",18),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",""!=e.searchParams&&null!=e.searchParams),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",""==e.searchParams||null==e.searchParams)}}function k(e,t){if(1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275elementStart"](1,"div",35),d["\u0275\u0275elementStart"](2,"div",36),d["\u0275\u0275element"](3,"img",37),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](4,"div",38),d["\u0275\u0275elementStart"](5,"div",39),d["\u0275\u0275text"](6,"Loading..."),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementContainerEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("src",e.uiTextConfig["UI-LOADING-001"],d["\u0275\u0275sanitizeUrl"])}}function I(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",42),d["\u0275\u0275elementStart"](1,"app-list-view",43),d["\u0275\u0275listener"]("onClick",(function(t){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](3).onClickRowData(t)})),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"](3);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("list",e.data)("fieldConfig",e.fieldConfig)("variant",2)("isCheckboxActive",!1)("headerHeight","32px")}}function F(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",44),d["\u0275\u0275element"](1,"img",45),d["\u0275\u0275elementStart"](2,"div",46),d["\u0275\u0275text"](3,"No Roles Found"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function L(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",44),d["\u0275\u0275element"](1,"img",47),d["\u0275\u0275elementStart"](2,"div",46),d["\u0275\u0275text"](3,"No Result Found"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](4,"div",48),d["\u0275\u0275text"](5," Sorry, We Couldn't Find Any Matches For Your Search. "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function R(e,t){if(1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275template"](1,I,2,5,"div",40),d["\u0275\u0275template"](2,F,4,0,"div",41),d["\u0275\u0275template"](3,L,6,0,"div",41),d["\u0275\u0275elementContainerEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.data.length>0),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",0==e.data.length&&""==e.searchParams),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",0==e.data.length&&""!=e.searchParams)}}function T(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275element"](1,"app-setting-header-overall"),d["\u0275\u0275elementStart"](2,"div",3),d["\u0275\u0275elementStart"](3,"div",4),d["\u0275\u0275elementStart"](4,"div",5),d["\u0275\u0275elementStart"](5,"div",6),d["\u0275\u0275elementStart"](6,"div",7),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().goBackToPreviousRoute()})),d["\u0275\u0275namespaceSVG"](),d["\u0275\u0275elementStart"](7,"svg",8),d["\u0275\u0275element"](8,"path",9),d["\u0275\u0275element"](9,"path",10),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275namespaceHTML"](),d["\u0275\u0275elementStart"](10,"div",11),d["\u0275\u0275elementStart"](11,"div",12),d["\u0275\u0275text"](12,"Roles & Permissions"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](13,"div",13),d["\u0275\u0275text"](14," Manage access levels and privileges efficiently "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](15,E,3,2,"div",14),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](16,"div",15),d["\u0275\u0275template"](17,k,7,1,"ng-container",0),d["\u0275\u0275template"](18,R,4,3,"ng-container",0),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](15),d["\u0275\u0275property"]("ngIf",e.data.length>0||""!=e.searchParams),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",e.isLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!e.isLoading)}}function H(e,t){1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275element"](1,"app-access-denied"),d["\u0275\u0275elementContainerEnd"]())}const D=function(){return[]};function V(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"app-search-overlay",49),d["\u0275\u0275listener"]("onEnter",(function(t){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().onEnterSearch(t)})),d["\u0275\u0275elementEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275property"]("currentSearchText",e.searchParams)("recentSearch",d["\u0275\u0275pureFunction0"](2,D))}}const j=function(e,t){return[e,t,0,0,"V"]};let B=(()=>{class e{constructor(e,t,n,i,o,r){this._toaster=e,this._router=t,this._rolesService=n,this._masterDataService=i,this._overlay=o,this._viewContainerRef=r,this._onDestroy=new s.b,this.access=l,this.isLoading=!0,this.searchParams="",this.data=[],this.fieldConfig=[],this.uiTextConfig={}}ngOnInit(){return Object(r.c)(this,void 0,void 0,(function*(){this.calculateDynamicContentHeight(),yield this.getAtsMasterUiConfig("roleSettingsConfig"),yield this.getAllRoles()}))}onResize(){this.calculateDynamicContentHeight()}calculateDynamicContentHeight(){document.documentElement.style.setProperty("--dynamicHeight",window.innerHeight-57+"px"),document.documentElement.style.setProperty("--dynamicMainInnerHeight",window.innerHeight-57-55+"px"),document.documentElement.style.setProperty("--dynamicInnerHeight",window.innerHeight-57-110+"px"),document.documentElement.style.setProperty("--dynamicSubHeight",window.innerHeight-57-170+"px"),document.documentElement.style.setProperty("--dynamicAccessDeniedHeight",window.innerHeight-57+"px")}goBackToPreviousRoute(){history.back()}onClickRowData(e){return Object(r.c)(this,void 0,void 0,(function*(){e.functionName&&""!=e.functionName&&(yield this[e.functionName](e.data))}))}routeToDetailsPage(e){return Object(r.c)(this,void 0,void 0,(function*(){if(!(null==e?void 0:e.id))return this._toaster.showError("Error","Role ID Not Found!",7e3);this._router.navigateByUrl("main/ats/settings/role/"+e.id)}))}openSearchBarOverlay(e,t,n){var i;if(!(null===(i=this.overlayRef)||void 0===i?void 0:i.hasAttached())){const i=this._overlay.position().flexibleConnectedTo(e).withFlexibleDimensions(!0).withPush(!0).withViewportMargin(25).withGrowAfterOpen(!0).withPositions([{originX:"start",originY:"bottom",overlayX:"start",overlayY:"top"}]);i.withDefaultOffsetY(t),n&&i.withDefaultOffsetX(n);const o=this._overlay.scrollStrategies.close();this.overlayRef=this._overlay.create({positionStrategy:i,scrollStrategy:o,hasBackdrop:!0,backdropClass:"",panelClass:["pop-up"]});const r=new a.h(this.triggerSearchBarTemplateRef,this._viewContainerRef);this.overlayRef.attach(r),this.overlayRef.backdropClick().subscribe(()=>{this.closeOverlay()})}}closeOverlay(){var e;null===(e=this.overlayRef)||void 0===e||e.dispose()}onEnterSearch(e,t){return Object(r.c)(this,void 0,void 0,(function*(){t&&(t.stopPropagation(),t.preventDefault()),this.searchParams=e,this.closeOverlay(),this.isLoading=!0,yield this.getAllRoles(),this.isLoading=!1}))}getAllRoles(){return Object(r.c)(this,void 0,void 0,(function*(){return this.isLoading=!0,this.data=[],new Promise((e,t)=>this._rolesService.getAllRoles(this.searchParams).pipe(Object(c.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.data=t.data:this._toaster.showError("Error",t.msg,7e3),this.isLoading=!1,e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Roles Data Retrieval Failed!",7e3),this.isLoading=!1,t()}}))}))}getAtsMasterUiConfig(e){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._masterDataService.getAtsMasterUiConfig(e).pipe(Object(c.a)(this._onDestroy)).subscribe({next:n=>{0==n.err?"roleSettingsConfig"==e&&(this.fieldConfig=n.data.fieldConfig,this.uiTextConfig=n.data.uiTextConfig):this._toaster.showError("Error",n.msg,7e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Master Data Retrieval Failed!",7e3),n()}}))}))}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](g.a),d["\u0275\u0275directiveInject"](o.g),d["\u0275\u0275directiveInject"](p),d["\u0275\u0275directiveInject"](h.a),d["\u0275\u0275directiveInject"](f.e),d["\u0275\u0275directiveInject"](d.ViewContainerRef))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-landing-page"]],viewQuery:function(e,t){if(1&e&&(d["\u0275\u0275viewQuery"](_,!0),d["\u0275\u0275viewQuery"](O,!0)),2&e){let e;d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.triggerSearchBarTemplateRef=e.first),d["\u0275\u0275queryRefresh"](e=d["\u0275\u0275loadQuery"]())&&(t.triggerSearchBar=e.first)}},hostBindings:function(e,t){1&e&&d["\u0275\u0275listener"]("resize",(function(){return t.onResize()}),!1,d["\u0275\u0275resolveWindow"])},decls:6,vars:21,consts:[[4,"ngIf"],["cdkConnectedOverlay","",3,"cdkConnectedOverlayOrigin"],["triggerSearchBarTemplateRef",""],[1,"content-container"],[1,"content-inner-container"],[1,"content-container-header"],[1,"d-flex","align-items-center"],[3,"click"],["width","16","height","16","viewBox","0 0 16 16","fill","none",1,"arrow-svg"],["d","M6.00065 14.6668H10.0007C13.334 14.6668 14.6673 13.3335 14.6673 10.0002V6.00016C14.6673 2.66683 13.334 1.3335 10.0007 1.3335H6.00065C2.66732 1.3335 1.33398 2.66683 1.33398 6.00016V10.0002C1.33398 13.3335 2.66732 14.6668 6.00065 14.6668Z","stroke","#526179","stroke-width","0.8","stroke-linecap","round","stroke-linejoin","round"],["d","M8.84081 10.3532L6.49414 7.99982L8.84081 5.64648","stroke","#526179","stroke-width","0.8","stroke-linecap","round","stroke-linejoin","round"],[1,"header"],[1,"header-title"],[1,"header-subtitle"],["class","content-container-content-header",4,"ngIf"],[1,"content-container-content"],[1,"content-container-content-header"],["class","d-flex align-items-center justify-content-between search-ui","cdkOverlayOrigin","",3,"click",4,"ngIf"],["class","header-icon","cdkOverlayOrigin","",3,"click",4,"ngIf"],["cdkOverlayOrigin","",1,"d-flex","align-items-center","justify-content-between","search-ui",3,"click"],["triggerSearchBar","cdkOverlayOrigin","triggerSearchField",""],[1,"search-bar"],["type","text","placeholder","Search Here...",3,"ngModel","ngModelChange","keydown.backspace"],["inputField",""],[1,"header-icon"],["width","18","height","18","viewBox","0 0 18 18","fill","none",4,"ngIf"],["style","cursor: pointer","width","18","height","18","viewBox","0 0 18 18","fill","none",3,"click",4,"ngIf"],["width","18","height","18","viewBox","0 0 18 18","fill","none"],["d","M7.01916 12.6153C5.31148 12.6153 3.86533 12.023 2.68073 10.8384C1.49612 9.65383 0.903809 8.20768 0.903809 6.49999C0.903809 4.79231 1.49612 3.34616 2.68073 2.16154C3.86533 0.976943 5.31148 0.384644 7.01916 0.384644C8.72684 0.384644 10.173 0.976943 11.3576 2.16154C12.5422 3.34616 13.1345 4.79231 13.1345 6.49999C13.1345 7.21409 13.0147 7.89614 12.7749 8.54614C12.5352 9.19614 12.2153 9.76153 11.8153 10.2423L17.5692 15.9961C17.7076 16.1346 17.7784 16.3087 17.7816 16.5183C17.7849 16.7279 17.714 16.9051 17.5692 17.05C17.4243 17.1948 17.2486 17.2673 17.0422 17.2673C16.8358 17.2673 16.6602 17.1948 16.5153 17.05L10.7615 11.2961C10.2615 11.7089 9.68646 12.032 9.03646 12.2653C8.38646 12.4987 7.71403 12.6153 7.01916 12.6153ZM7.01916 11.1154C8.30763 11.1154 9.39897 10.6683 10.2932 9.77404C11.1874 8.87981 11.6346 7.78846 11.6346 6.49999C11.6346 5.21153 11.1874 4.12018 10.2932 3.22594C9.39897 2.33171 8.30763 1.88459 7.01916 1.88459C5.73069 1.88459 4.63934 2.33171 3.74511 3.22594C2.85089 4.12018 2.40378 5.21153 2.40378 6.49999C2.40378 7.78846 2.85089 8.87981 3.74511 9.77404C4.63934 10.6683 5.73069 11.1154 7.01916 11.1154Z","fill","#45546E"],["width","18","height","18","viewBox","0 0 18 18","fill","none",2,"cursor","pointer",3,"click"],["clip-path","url(#clip0_22386_12410)"],["d","M8.00048 7.05781L11.3005 3.75781L12.2431 4.70048L8.94315 8.00048L12.2431 11.3005L11.3005 12.2431L8.00048 8.94315L4.70048 12.2431L3.75781 11.3005L7.05781 8.00048L3.75781 4.70048L4.70048 3.75781L8.00048 7.05781Z","fill","#6E7B8F"],["id","clip0_22386_12410"],["width","16","height","16","fill","white"],["cdkOverlayOrigin","",1,"header-icon",3,"click"],[1,"loading-img"],[2,"max-width","60px","max-height","60px"],[2,"max-width","100%","max-height","100%",3,"src"],[1,"loading-wrapper"],[1,"loading"],["class","list-view",4,"ngIf"],["class","empty-state",4,"ngIf"],[1,"list-view"],[3,"list","fieldConfig","variant","isCheckboxActive","headerHeight","onClick"],[1,"empty-state"],["src","https://assets.kebs.app/ATS-Empty-State.png"],[1,"no-data-title"],["src","https://assets.kebs.app/ats-no-search-results-found.png"],[1,"no-data-description"],[3,"currentSearchText","recentSearch","onEnter"]],template:function(e,t){1&e&&(d["\u0275\u0275template"](0,T,19,3,"ng-container",0),d["\u0275\u0275pipe"](1,"access"),d["\u0275\u0275template"](2,H,2,0,"ng-container",0),d["\u0275\u0275pipe"](3,"access"),d["\u0275\u0275template"](4,V,1,3,"ng-template",1,2,d["\u0275\u0275templateRefExtractor"])),2&e&&(d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBindV"](1,3,d["\u0275\u0275pureFunction2"](15,j,t.access.moduleId.settings,t.access.subModuleId.rolesAndPermissions))),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",!d["\u0275\u0275pipeBindV"](3,9,d["\u0275\u0275pureFunction2"](18,j,t.access.moduleId.settings,t.access.subModuleId.rolesAndPermissions))),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("cdkConnectedOverlayOrigin",t.triggerSearchBar))},directives:[i.NgIf,f.a,u.a,f.b,C.e,C.v,C.y,v.a,x.a,y.a],pipes:[P.a],styles:['.loading-img[_ngcontent-%COMP%]{height:var(--dynamicSubHeight);flex-direction:column;background-color:#fff}.loading-img[_ngcontent-%COMP%], .loading-img[_ngcontent-%COMP%]   .loading-wrapper[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center}.loading-img[_ngcontent-%COMP%]   .loading-wrapper[_ngcontent-%COMP%]{vertical-align:center}.loading-img[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]{color:rgba(0,0,0,.3);font-size:16px}.loading-img[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]:before{content:"Loading...";position:absolute;overflow:hidden;max-width:7em;white-space:nowrap;background:linear-gradient(270deg,var(--atsprimaryColor),var(--atssecondaryColor) 105.29%);background-clip:text;-webkit-background-clip:text;-webkit-text-fill-color:transparent;animation:loading 6s linear infinite}@keyframes loading{0%{max-width:0}}.content-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;padding:16px;height:var(--dynamicMainInnerHeight);background:#f1f3f8}.content-container[_ngcontent-%COMP%]   .content-inner-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;padding-bottom:16px;height:var(--dynamicInnerHeight);background:#fff}.content-container[_ngcontent-%COMP%]   .content-container-header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding:8px 16px;background:#f9fafc}.content-container[_ngcontent-%COMP%]   .content-container-header[_ngcontent-%COMP%]   .arrow-svg[_ngcontent-%COMP%]{cursor:pointer}.content-container[_ngcontent-%COMP%]   .content-container-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{margin-left:10px}.content-container[_ngcontent-%COMP%]   .content-container-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:700;color:#111434}.content-container[_ngcontent-%COMP%]   .content-container-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .header-subtitle[_ngcontent-%COMP%]{color:#8b95a5;font-family:var(--atsfontFamily);font-size:12px;font-weight:400}.content-container[_ngcontent-%COMP%]   .content-container-header[_ngcontent-%COMP%]   .content-container-content-header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:end;min-height:50px;max-height:50px}.content-container[_ngcontent-%COMP%]   .content-container-header[_ngcontent-%COMP%]   .header-icon[_ngcontent-%COMP%]{cursor:pointer}.content-container[_ngcontent-%COMP%]   .content-container-header[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]{width:350px;height:36px;padding:0 8px;border:2px solid #dadce2;border-radius:8px;cursor:text}.content-container[_ngcontent-%COMP%]   .content-container-header[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]{width:100%}.content-container[_ngcontent-%COMP%]   .content-container-header[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{height:100%;width:95%;outline:none;border:none}.content-container[_ngcontent-%COMP%]   .content-container-header[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .content-container[_ngcontent-%COMP%]   .content-container-header[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]   .search-bar[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]::placeholder{font-family:var(--atsfontFamily);font-size:11px;font-weight:400;color:#6e7b8f}.content-container[_ngcontent-%COMP%]   .content-container-header[_ngcontent-%COMP%]   .search-ui[_ngcontent-%COMP%]   .search-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:11px;font-weight:400;color:#5f6c81;width:280px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.content-container[_ngcontent-%COMP%]   .content-container-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;padding:0 16px 16px;background:#fff;height:var(--dynamicInnerHeight)}.content-container[_ngcontent-%COMP%]   .content-container-content[_ngcontent-%COMP%]   .list-view[_ngcontent-%COMP%]{height:var(--dynamicSubHeight)}.content-container[_ngcontent-%COMP%]   .content-container-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;height:var(--dynamicSubHeight)}.content-container[_ngcontent-%COMP%]   .content-container-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .no-data-title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:500;color:#1b2140;margin-bottom:4px}.content-container[_ngcontent-%COMP%]   .content-container-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .no-data-description[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:400;color:#8b95a5;margin-bottom:4px;text-align:center}']}),e})();var A=n("PSD3"),z=n.n(A),N=n("Qu3c");function U(e,t){if(1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275elementStart"](1,"div",13),d["\u0275\u0275elementStart"](2,"div",14),d["\u0275\u0275element"](3,"img",15),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](4,"div",16),d["\u0275\u0275elementStart"](5,"div",17),d["\u0275\u0275text"](6,"Loading..."),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementContainerEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](3),d["\u0275\u0275property"]("src",e.uiTextConfig["UI-LOADING-001"],d["\u0275\u0275sanitizeUrl"])}}function G(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",28),d["\u0275\u0275elementStart"](1,"div",29),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](4).onClickCancel()})),d["\u0275\u0275text"](2," Cancel "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](3,"div",30),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"](4).onClickSave()})),d["\u0275\u0275text"](4,"Save"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()}}const K=function(e){return{"border-right":e}};function X(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",36),d["\u0275\u0275text"](1),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=t.index,i=d["\u0275\u0275nextContext"](5);d["\u0275\u0275property"]("ngStyle",d["\u0275\u0275pureFunction1"](2,K,n==i.accessTypes.length-1?"none":"")),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",e.label," ")}}function Z(e,t){1&e&&d["\u0275\u0275element"](0,"span",43)}function $(e,t){1&e&&(d["\u0275\u0275namespaceSVG"](),d["\u0275\u0275elementStart"](0,"svg",44),d["\u0275\u0275element"](1,"path",45),d["\u0275\u0275elementEnd"]())}const Q=function(e,t){return[e,t,0,0,"E"]},Y=function(e,t){return{border:e,"pointer-events":t}};function W(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementStart"](0,"div",40),d["\u0275\u0275listener"]("click",(function(){d["\u0275\u0275restoreView"](e);const n=t.$implicit,i=d["\u0275\u0275nextContext"]().index,o=d["\u0275\u0275nextContext"]().$implicit;return d["\u0275\u0275nextContext"](4).onChangeAccessPermission(o.key,null==n?null:n.colKey,i)})),d["\u0275\u0275pipe"](1,"access"),d["\u0275\u0275template"](2,Z,1,0,"span",41),d["\u0275\u0275template"](3,$,2,0,"svg",42),d["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=t.index,i=d["\u0275\u0275nextContext"]().$implicit,o=d["\u0275\u0275nextContext"](5);d["\u0275\u0275property"]("ngStyle",d["\u0275\u0275pureFunction2"](12,Y,n==o.accessTypes.length-1?"none":"",d["\u0275\u0275pipeBindV"](1,3,d["\u0275\u0275pureFunction2"](9,Q,o.access.moduleId.settings,o.access.subModuleId.rolesAndPermissions))?"":"none")),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",0==i[null==e?null:e.colKey]),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",1==i[null==e?null:e.colKey])}}function q(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",37),d["\u0275\u0275elementStart"](1,"div",38),d["\u0275\u0275text"](2),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](3,W,4,15,"div",39),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=d["\u0275\u0275nextContext"](5);d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate"](e.name),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngForOf",n.accessTypes)}}function J(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",31),d["\u0275\u0275elementStart"](1,"div",23),d["\u0275\u0275text"](2),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](3,"div",32),d["\u0275\u0275elementStart"](4,"div",33),d["\u0275\u0275text"](5),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](6,X,2,4,"div",34),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](7,q,4,2,"div",35),d["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=d["\u0275\u0275nextContext"](4);d["\u0275\u0275advance"](2),d["\u0275\u0275textInterpolate1"](" ",n.uiTextConfig[e.key]," LEVEL PERMISSIONS "),d["\u0275\u0275advance"](3),d["\u0275\u0275textInterpolate1"]("",n.uiTextConfig[e.key],"s"),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngForOf",n.accessTypes),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngForOf",n.data[e.key])}}function ee(e,t){if(1&e&&(d["\u0275\u0275elementStart"](0,"div",20),d["\u0275\u0275elementStart"](1,"div",21),d["\u0275\u0275elementStart"](2,"div",22),d["\u0275\u0275elementStart"](3,"div",23),d["\u0275\u0275text"](4),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](5,"div",24),d["\u0275\u0275text"](6),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275template"](7,G,5,0,"div",25),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](8,"div",26),d["\u0275\u0275template"](9,J,8,4,"div",27),d["\u0275\u0275pipe"](10,"keyvalue"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](3);d["\u0275\u0275advance"](4),d["\u0275\u0275textInterpolate1"]("ROLE - ",null==e.roleData?null:e.roleData.name,""),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("matTooltip",null==e.roleData?null:e.roleData.description),d["\u0275\u0275advance"](1),d["\u0275\u0275textInterpolate1"](" ",null==e.roleData?null:e.roleData.description," "),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.isUpdated),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngForOf",d["\u0275\u0275pipeBind1"](10,5,e.data))}}function te(e,t){1&e&&(d["\u0275\u0275elementStart"](0,"div",46),d["\u0275\u0275element"](1,"img",47),d["\u0275\u0275elementStart"](2,"div",48),d["\u0275\u0275text"](3,"No Role Permissions Found"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"]())}function ne(e,t){if(1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275template"](1,ee,11,7,"div",18),d["\u0275\u0275template"](2,te,4,0,"div",19),d["\u0275\u0275elementContainerEnd"]()),2&e){const e=d["\u0275\u0275nextContext"](2);d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",e.data),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!e.data)}}function ie(e,t){if(1&e){const e=d["\u0275\u0275getCurrentView"]();d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275element"](1,"app-setting-header-overall"),d["\u0275\u0275elementStart"](2,"div",1),d["\u0275\u0275elementStart"](3,"div",2),d["\u0275\u0275elementStart"](4,"div",3),d["\u0275\u0275elementStart"](5,"div",4),d["\u0275\u0275elementStart"](6,"div",5),d["\u0275\u0275listener"]("click",(function(){return d["\u0275\u0275restoreView"](e),d["\u0275\u0275nextContext"]().goBackToPreviousRoute()})),d["\u0275\u0275namespaceSVG"](),d["\u0275\u0275elementStart"](7,"svg",6),d["\u0275\u0275element"](8,"path",7),d["\u0275\u0275element"](9,"path",8),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275namespaceHTML"](),d["\u0275\u0275elementStart"](10,"div",9),d["\u0275\u0275elementStart"](11,"div",10),d["\u0275\u0275text"](12,"Roles & Permissions"),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](13,"div",11),d["\u0275\u0275text"](14," Manage access levels and privileges efficiently "),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementStart"](15,"div",12),d["\u0275\u0275template"](16,U,7,1,"ng-container",0),d["\u0275\u0275template"](17,ne,3,2,"ng-container",0),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementEnd"](),d["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=d["\u0275\u0275nextContext"]();d["\u0275\u0275advance"](16),d["\u0275\u0275property"]("ngIf",e.isLoading),d["\u0275\u0275advance"](1),d["\u0275\u0275property"]("ngIf",!e.isLoading)}}function oe(e,t){1&e&&(d["\u0275\u0275elementContainerStart"](0),d["\u0275\u0275element"](1,"app-access-denied"),d["\u0275\u0275elementContainerEnd"]())}const re=function(e,t){return[e,t,0,0,"V"]},ae=[{path:"",component:B},{path:":roleId",component:(()=>{class e{constructor(e,t,n,i,o){this._activatedRoute=e,this._rolesService=t,this._masterDataService=n,this._toaster=i,this._router=o,this._onDestroy=new s.b,this.roleId=null,this.isLoading=!0,this.isUpdated=!1,this.data={},this.roleData={},this.fieldConfig=[],this.uiTextConfig={},this.accessTypes=[],this.access=l}canDeactivate(){return!this.isUpdated}ngOnInit(){return Object(r.c)(this,void 0,void 0,(function*(){this._activatedRoute.params.subscribe(e=>{this.roleId=e.roleId}),this.calculateDynamicContentHeight(),yield this.getAtsMasterUiConfig("roleSettingsConfig"),yield this.getPermissionsForRole()}))}onResize(){this.calculateDynamicContentHeight()}calculateDynamicContentHeight(){document.documentElement.style.setProperty("--dynamicHeight",window.innerHeight-57+"px"),document.documentElement.style.setProperty("--dynamicMainInnerHeight",window.innerHeight-57-55+"px"),document.documentElement.style.setProperty("--dynamicInnerHeight",window.innerHeight-57-110+"px"),document.documentElement.style.setProperty("--dynamicSubHeight",window.innerHeight-57-170+"px"),document.documentElement.style.setProperty("--dynamicSubContainerHeight",window.innerHeight-57-220+"px"),document.documentElement.style.setProperty("--dynamicAccessDeniedHeight",window.innerHeight-57+"px")}goBackToPreviousRoute(){history.back()}onClickCancel(){return Object(r.c)(this,void 0,void 0,(function*(){yield this.getPermissionsForRole(),this.isUpdated=!1}))}onClickSave(){return Object(r.c)(this,void 0,void 0,(function*(){z.a.fire({title:"Permission Update Notice!",html:"Any changes you make at the top level will automatically apply to everything below it.<br><br>For example, if you remove the <strong>Create</strong> permission at the top, it will also be removed from all related items beneath it.",icon:"warning",showCancelButton:!0,confirmButtonText:"Confirm",cancelButtonText:"Cancel"}).then(e=>Object(r.c)(this,void 0,void 0,(function*(){e.isConfirmed?(yield this.updatePermissionsForRole(),z.a.fire({title:"Access Permissions Updated Successfully!",text:"Click OK to refresh the page for role access to reflect, or click Cancel.",icon:"success",showCancelButton:!0,confirmButtonText:"OK",cancelButtonText:"Cancel"}).then(e=>{e.isConfirmed?window.location.reload():e.isDismissed&&z.a.fire({text:"Changes will reflect upon manual reload.",icon:"info",timer:3e3,showConfirmButton:!1,timerProgressBar:!0})}),yield this.getPermissionsForRole()):e.isDismissed&&(yield this.onClickCancel())})))}))}onChangeAccessPermission(e,t,n){this.data[e][n][t]=0==this.data[e][n][t]?1:0,this.isUpdated=!0}getPermissionsForRole(){return Object(r.c)(this,void 0,void 0,(function*(){return this.isLoading=!0,this.data={},new Promise((e,t)=>this._rolesService.getPermissionsForRole(this.roleId).pipe(Object(c.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?(this.data=t.data,this.roleData=t.role_name):(this._router.navigateByUrl("main/ats/settings/role"),this._toaster.showError("Error",t.msg,7e3)),this.isLoading=!1,e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Role Permissions Data Retrieval Failed!",7e3),this.isLoading=!1,t()}}))}))}updatePermissionsForRole(){return Object(r.c)(this,void 0,void 0,(function*(){return this.isLoading=!0,new Promise((e,t)=>this._rolesService.updatePermissionsForRole(this.roleId,this.data).pipe(Object(c.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this.isUpdated=!1:(this._toaster.showError("Error",t.msg,7e3),this.isLoading=!1),e(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Roles Updation Failed!",7e3),this.isLoading=!1,t()}}))}))}getAtsMasterUiConfig(e){return Object(r.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>this._masterDataService.getAtsMasterUiConfig(e).pipe(Object(c.a)(this._onDestroy)).subscribe({next:n=>{0==n.err?"roleSettingsConfig"==e&&(this.fieldConfig=n.data.fieldConfig,this.uiTextConfig=n.data.uiTextConfig,this.accessTypes=n.data.accessTypes):this._toaster.showError("Error",n.msg,7e3),t(!0)},error:e=>{this._toaster.showError("Error",e.message?e.message:"ATS Master Data Retrieval Failed!",7e3),n()}}))}))}}return e.\u0275fac=function(t){return new(t||e)(d["\u0275\u0275directiveInject"](o.a),d["\u0275\u0275directiveInject"](p),d["\u0275\u0275directiveInject"](h.a),d["\u0275\u0275directiveInject"](g.a),d["\u0275\u0275directiveInject"](o.g))},e.\u0275cmp=d["\u0275\u0275defineComponent"]({type:e,selectors:[["app-details-page"]],hostBindings:function(e,t){1&e&&d["\u0275\u0275listener"]("beforeunload",(function(){return t.canDeactivate()}),!1,d["\u0275\u0275resolveWindow"])("resize",(function(){return t.onResize()}),!1,d["\u0275\u0275resolveWindow"])},decls:4,vars:20,consts:[[4,"ngIf"],[1,"content-container"],[1,"content-inner-container"],[1,"content-container-header"],[1,"d-flex","align-items-center"],[3,"click"],["width","16","height","16","viewBox","0 0 16 16","fill","none",1,"arrow-svg"],["d","M6.00065 14.6668H10.0007C13.334 14.6668 14.6673 13.3335 14.6673 10.0002V6.00016C14.6673 2.66683 13.334 1.3335 10.0007 1.3335H6.00065C2.66732 1.3335 1.33398 2.66683 1.33398 6.00016V10.0002C1.33398 13.3335 2.66732 14.6668 6.00065 14.6668Z","stroke","#526179","stroke-width","0.8","stroke-linecap","round","stroke-linejoin","round"],["d","M8.84081 10.3532L6.49414 7.99982L8.84081 5.64648","stroke","#526179","stroke-width","0.8","stroke-linecap","round","stroke-linejoin","round"],[1,"header"],[1,"header-title"],[1,"header-subtitle"],[1,"content-container-content"],[1,"loading-img"],[2,"max-width","60px","max-height","60px"],[2,"max-width","100%","max-height","100%",3,"src"],[1,"loading-wrapper"],[1,"loading"],["class","main-view",4,"ngIf"],["class","empty-state",4,"ngIf"],[1,"main-view"],[1,"main-view-header"],[1,"d-flex","flex-column",2,"width","80%"],[1,"title-text"],[1,"sub-title-text",3,"matTooltip"],["class","buttons",4,"ngIf"],[1,"main-view-content"],["class","single-section",4,"ngFor","ngForOf"],[1,"buttons"],[1,"cancel-button",3,"click"],[1,"save-button",3,"click"],[1,"single-section"],[1,"header-row"],[1,"section-title"],["class","header-title",3,"ngStyle",4,"ngFor","ngForOf"],["class","content-row",4,"ngFor","ngForOf"],[1,"header-title",3,"ngStyle"],[1,"content-row"],[1,"row-title"],["class","value",3,"ngStyle","click",4,"ngFor","ngForOf"],[1,"value",3,"ngStyle","click"],["class","circle",4,"ngIf"],["class","fill-tick","width","16","height","12","viewBox","0 0 16 12","fill","none","xmlns","http://www.w3.org/2000/svg",4,"ngIf"],[1,"circle"],["width","16","height","12","viewBox","0 0 16 12","fill","none","xmlns","http://www.w3.org/2000/svg",1,"fill-tick"],["d","M5.54972 9.15L14.0247 0.675C14.2247 0.475 14.4622 0.375 14.7372 0.375C15.0122 0.375 15.2497 0.475 15.4497 0.675C15.6497 0.875 15.7497 1.1125 15.7497 1.3875C15.7497 1.6625 15.6497 1.9 15.4497 2.1L6.24972 11.3C6.04972 11.5 5.81639 11.6 5.54972 11.6C5.28305 11.6 5.04972 11.5 4.84972 11.3L0.549719 7C0.349719 6.8 0.253885 6.5625 0.262219 6.2875C0.270552 6.0125 0.374719 5.775 0.574719 5.575C0.774719 5.375 1.01222 5.275 1.28722 5.275C1.56222 5.275 1.79972 5.375 1.99972 5.575L5.54972 9.15Z"],[1,"empty-state"],["src","https://assets.kebs.app/ATS-Empty-State.png"],[1,"no-data-title"]],template:function(e,t){1&e&&(d["\u0275\u0275template"](0,ie,18,2,"ng-container",0),d["\u0275\u0275pipe"](1,"access"),d["\u0275\u0275template"](2,oe,2,0,"ng-container",0),d["\u0275\u0275pipe"](3,"access")),2&e&&(d["\u0275\u0275property"]("ngIf",d["\u0275\u0275pipeBindV"](1,2,d["\u0275\u0275pureFunction2"](14,re,t.access.moduleId.settings,t.access.subModuleId.rolesAndPermissions))),d["\u0275\u0275advance"](2),d["\u0275\u0275property"]("ngIf",!d["\u0275\u0275pipeBindV"](3,8,d["\u0275\u0275pureFunction2"](17,re,t.access.moduleId.settings,t.access.subModuleId.rolesAndPermissions))))},directives:[i.NgIf,u.a,N.a,i.NgForOf,i.NgStyle,x.a],pipes:[P.a,i.KeyValuePipe],styles:['.loading-img[_ngcontent-%COMP%]{height:var(--dynamicSubHeight);flex-direction:column;background-color:#fff}.loading-img[_ngcontent-%COMP%], .loading-img[_ngcontent-%COMP%]   .loading-wrapper[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center}.loading-img[_ngcontent-%COMP%]   .loading-wrapper[_ngcontent-%COMP%]{vertical-align:center}.loading-img[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]{color:rgba(0,0,0,.3);font-size:16px}.loading-img[_ngcontent-%COMP%]   .loading[_ngcontent-%COMP%]:before{content:"Loading...";position:absolute;overflow:hidden;max-width:7em;white-space:nowrap;background:linear-gradient(270deg,var(--atsprimaryColor),var(--atssecondaryColor) 105.29%);background-clip:text;-webkit-background-clip:text;-webkit-text-fill-color:transparent;animation:loading 6s linear infinite}@keyframes loading{0%{max-width:0}}.content-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;padding:16px;height:var(--dynamicMainInnerHeight);background:#f1f3f8}.content-container[_ngcontent-%COMP%]   .content-inner-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;padding-bottom:16px;height:var(--dynamicInnerHeight);background:#fff}.content-container[_ngcontent-%COMP%]   .content-container-header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding:8px 16px;background:#f9fafc}.content-container[_ngcontent-%COMP%]   .content-container-header[_ngcontent-%COMP%]   .arrow-svg[_ngcontent-%COMP%]{cursor:pointer}.content-container[_ngcontent-%COMP%]   .content-container-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]{margin-left:10px}.content-container[_ngcontent-%COMP%]   .content-container-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:700;color:#111434}.content-container[_ngcontent-%COMP%]   .content-container-header[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .header-subtitle[_ngcontent-%COMP%]{color:#8b95a5;font-family:var(--atsfontFamily);font-size:12px;font-weight:400}.content-container[_ngcontent-%COMP%]   .content-container-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;padding:0 16px 16px;background:#fff;height:var(--dynamicInnerHeight)}.content-container[_ngcontent-%COMP%]   .content-container-content[_ngcontent-%COMP%]   .main-view[_ngcontent-%COMP%]{display:flex;flex-direction:column;height:var(--dynamicSubHeight)}.content-container[_ngcontent-%COMP%]   .content-container-content[_ngcontent-%COMP%]   .main-view[_ngcontent-%COMP%]   .main-view-header[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;height:50px;padding:0 26px}.content-container[_ngcontent-%COMP%]   .content-container-content[_ngcontent-%COMP%]   .main-view[_ngcontent-%COMP%]   .main-view-header[_ngcontent-%COMP%]   .title-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:700;color:#111434;text-transform:uppercase}.content-container[_ngcontent-%COMP%]   .content-container-content[_ngcontent-%COMP%]   .main-view[_ngcontent-%COMP%]   .main-view-header[_ngcontent-%COMP%]   .sub-title-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:12px;font-weight:400;color:#6e7b8f;height:20px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.content-container[_ngcontent-%COMP%]   .content-container-content[_ngcontent-%COMP%]   .main-view[_ngcontent-%COMP%]   .main-view-header[_ngcontent-%COMP%]   .buttons[_ngcontent-%COMP%]{display:flex;gap:12px}.content-container[_ngcontent-%COMP%]   .content-container-content[_ngcontent-%COMP%]   .main-view[_ngcontent-%COMP%]   .main-view-header[_ngcontent-%COMP%]   .buttons[_ngcontent-%COMP%]   .cancel-button[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:700;color:#45546e;padding:10px;justify-content:center;align-items:center;display:flex;border-radius:5px;border:1px solid #45546e;cursor:pointer}.content-container[_ngcontent-%COMP%]   .content-container-content[_ngcontent-%COMP%]   .main-view[_ngcontent-%COMP%]   .main-view-header[_ngcontent-%COMP%]   .buttons[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:700;padding:10px;justify-content:center;align-items:center;display:flex;border-radius:5px;color:#fff;background:linear-gradient(270deg,var(--atsprimaryColor),var(--atssecondaryColor) 105.29%);cursor:pointer}.content-container[_ngcontent-%COMP%]   .content-container-content[_ngcontent-%COMP%]   .main-view[_ngcontent-%COMP%]   .main-view-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:20px;padding:0 18px 0 26px;overflow-y:auto;height:var(--dynamicSubContainerHeight)}.content-container[_ngcontent-%COMP%]   .content-container-content[_ngcontent-%COMP%]   .main-view[_ngcontent-%COMP%]   .main-view-content[_ngcontent-%COMP%]   .single-section[_ngcontent-%COMP%]{display:flex;flex-direction:column;margin-bottom:20px}.content-container[_ngcontent-%COMP%]   .content-container-content[_ngcontent-%COMP%]   .main-view[_ngcontent-%COMP%]   .main-view-content[_ngcontent-%COMP%]   .single-section[_ngcontent-%COMP%]   .title-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:700;color:var(--atsprimaryColor);text-transform:uppercase;margin-bottom:20px}.content-container[_ngcontent-%COMP%]   .content-container-content[_ngcontent-%COMP%]   .main-view[_ngcontent-%COMP%]   .main-view-content[_ngcontent-%COMP%]   .single-section[_ngcontent-%COMP%]   .header-row[_ngcontent-%COMP%]{display:flex;align-items:center;height:38px;border:1px solid #d4d6d8}.content-container[_ngcontent-%COMP%]   .content-container-content[_ngcontent-%COMP%]   .main-view[_ngcontent-%COMP%]   .main-view-content[_ngcontent-%COMP%]   .single-section[_ngcontent-%COMP%]   .header-row[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:500;color:#111434;width:23%;height:38px;display:flex;align-items:center;padding-left:8px;border-right:1px solid #d4d6d8;border-top:1px solid #d4d6d8;border-bottom:1px solid #d4d6d8;background:#f2f3f6}.content-container[_ngcontent-%COMP%]   .content-container-content[_ngcontent-%COMP%]   .main-view[_ngcontent-%COMP%]   .main-view-content[_ngcontent-%COMP%]   .single-section[_ngcontent-%COMP%]   .header-row[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:500;color:#5f6c81;width:11%;height:38px;display:flex;align-items:center;justify-content:center;text-align:center;border-right:1px solid #d4d6d8;border-top:1px solid #d4d6d8;border-bottom:1px solid #d4d6d8;background:#f2f3f6}.content-container[_ngcontent-%COMP%]   .content-container-content[_ngcontent-%COMP%]   .main-view[_ngcontent-%COMP%]   .main-view-content[_ngcontent-%COMP%]   .single-section[_ngcontent-%COMP%]   .content-row[_ngcontent-%COMP%]{display:flex;align-items:center;min-height:38px;border:1px solid #d4d6d8;border-top:none}.content-container[_ngcontent-%COMP%]   .content-container-content[_ngcontent-%COMP%]   .main-view[_ngcontent-%COMP%]   .main-view-content[_ngcontent-%COMP%]   .single-section[_ngcontent-%COMP%]   .content-row[_ngcontent-%COMP%]   .row-title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:500;color:#45546e;width:23%;height:100%;display:flex;align-items:center;padding:8px;border-right:1px solid #d4d6d8}.content-container[_ngcontent-%COMP%]   .content-container-content[_ngcontent-%COMP%]   .main-view[_ngcontent-%COMP%]   .main-view-content[_ngcontent-%COMP%]   .single-section[_ngcontent-%COMP%]   .content-row[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]{width:11%;height:100%;display:flex;align-items:center;justify-content:center;border-right:1px solid #d4d6d8;cursor:pointer}.content-container[_ngcontent-%COMP%]   .content-container-content[_ngcontent-%COMP%]   .main-view[_ngcontent-%COMP%]   .main-view-content[_ngcontent-%COMP%]   .single-section[_ngcontent-%COMP%]   .content-row[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{width:8px;height:8px;border-radius:50%;background-color:#d4d6d8}.content-container[_ngcontent-%COMP%]   .content-container-content[_ngcontent-%COMP%]   .main-view[_ngcontent-%COMP%]   .main-view-content[_ngcontent-%COMP%]   .single-section[_ngcontent-%COMP%]   .content-row[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%]   .fill-tick[_ngcontent-%COMP%]{fill:var(--atsprimaryColor)}.content-container[_ngcontent-%COMP%]   .content-container-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;height:var(--dynamicSubHeight)}.content-container[_ngcontent-%COMP%]   .content-container-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .no-data-title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:500;color:#1b2140;margin-bottom:4px}.content-container[_ngcontent-%COMP%]   .content-container-content[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%]   .no-data-description[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:400;color:#8b95a5;margin-bottom:4px;text-align:center}']}),e})(),canDeactivate:[(()=>{class e{canDeactivate(e){return!!e.canDeactivate()||confirm("WARNING: You have unsaved changes. Press Cancel to go back and save these changes or OK to lose these changes.")}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275prov=d["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()]}];let ce=(()=>{class e{}return e.\u0275mod=d["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=d["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[o.k.forChild(ae)],o.k]}),e})();var se=n("1+mW"),le=n("Xi0T"),de=n("lVl8"),ge=n("qFsG");let me=(()=>{class e{}return e.\u0275mod=d["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=d["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[i.CommonModule,ce,se.ApplicantTrackingSystemModule,le.a,f.h,de.b,ge.c,C.p,C.E,N.b]]}),e})()},pEYl:function(e,t,n){"use strict";n.d(t,"a",(function(){return r}));var i=n("yuIm"),o=n("fXoL");let r=(()=>{class e{transform(e=0,t=0,n=0,o=0,r=""){if(!r||""==r)return!1;let a={module_id:e,sub_module_id:t,section_id:n,sub_section_id:o};"V"==r&&(a.view_permission=1),"C"==r&&(a.create_permission=1),"E"==r&&(a.edit_permission=1),"DE"==r&&(a.delete_permission=1),"DO"==r&&(a.download_permission=1),"U"==r&&(a.upload_permission=1),"B"==r&&(a.bulk_operation=1);const c=Object.keys(a);return i.roleAccessList.find(e=>c.every(t=>e[t]===a[t]))}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275pipe=o["\u0275\u0275definePipe"]({name:"access",type:e,pure:!0}),e})()}}]);