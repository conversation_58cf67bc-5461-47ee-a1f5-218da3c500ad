(window.webpackJsonp=window.webpackJsonp||[]).push([[916],{"3GN8":function(n,t,e){"use strict";e.r(t),e.d(t,"ScorecardFeedbackDialogComponent",(function(){return g}));var o=e("0IaG"),i=e("fXoL"),a=e("ofXK"),r=e("NFeN"),c=e("me71");function l(n,t){if(1&n){const n=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"mat-icon",11),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](n);const t=i["\u0275\u0275nextContext"]().$implicit;return t.isExpanded=!t.isExpanded})),i["\u0275\u0275text"](1," arrow_drop_down "),i["\u0275\u0275elementEnd"]()}}function d(n,t){if(1&n){const n=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"mat-icon",11),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](n);const t=i["\u0275\u0275nextContext"]().$implicit;return t.isExpanded=!t.isExpanded})),i["\u0275\u0275text"](1," arrow_drop_up "),i["\u0275\u0275elementEnd"]()}}function p(n,t){if(1&n&&(i["\u0275\u0275elementContainerStart"](0),i["\u0275\u0275elementStart"](1,"div",13),i["\u0275\u0275elementStart"](2,"div",14),i["\u0275\u0275element"](3,"app-user-image",15),i["\u0275\u0275elementStart"](4,"div",16),i["\u0275\u0275text"](5),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](6,"div",17),i["\u0275\u0275text"](7),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementContainerEnd"]()),2&n){const n=i["\u0275\u0275nextContext"]().$implicit;i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("oid",n.oid),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate"]((null==n?null:n.name)||"-"),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate1"](" ",(null==n?null:n.value)||"-"," ")}}function s(n,t){if(1&n&&(i["\u0275\u0275elementContainerStart"](0),i["\u0275\u0275template"](1,p,8,3,"ng-container",12),i["\u0275\u0275elementContainerEnd"]()),2&n){const n=i["\u0275\u0275nextContext"]().$implicit;i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null==n?null:n.isExpanded)}}function m(n,t){if(1&n&&(i["\u0275\u0275elementContainerStart"](0),i["\u0275\u0275elementStart"](1,"div",5),i["\u0275\u0275elementStart"](2,"div",6),i["\u0275\u0275elementStart"](3,"span",7),i["\u0275\u0275text"](4),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"span",8),i["\u0275\u0275text"](6),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](7,"div",9),i["\u0275\u0275template"](8,l,2,0,"mat-icon",10),i["\u0275\u0275template"](9,d,2,0,"mat-icon",10),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](10,s,2,1,"ng-container",4),i["\u0275\u0275elementContainerEnd"]()),2&n){const n=t.$implicit,e=t.index;i["\u0275\u0275advance"](4),i["\u0275\u0275textInterpolate1"]("Q",e+1,""),i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate"](n.question||"-"),i["\u0275\u0275advance"](2),i["\u0275\u0275property"]("ngIf",!(null!=n&&n.isExpanded)),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",null==n?null:n.isExpanded),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngForOf",n.response)}}let g=(()=>{class n{constructor(n){this.data=n}ngOnInit(){this.data.data.forEach(n=>n.isExpanded=!0)}}return n.\u0275fac=function(t){return new(t||n)(i["\u0275\u0275directiveInject"](o.a))},n.\u0275cmp=i["\u0275\u0275defineComponent"]({type:n,selectors:[["app-scorecard-feedback-dialog"]],decls:7,vars:2,consts:[[1,"bg-container"],[1,"candidate-name"],[1,"title","p-0"],[1,"d-flex","flex-column","inner-container"],[4,"ngFor","ngForOf"],[1,"d-flex","align-items-center","justify-content-between","question-row"],[1,"d-flex","align-items-center",2,"width","90%"],[1,"question-no"],[1,"question"],[1,"d-flex","align-items-center","justify-content-end",2,"width","10%"],["class","arrow-icon",3,"click",4,"ngIf"],[1,"arrow-icon",3,"click"],[4,"ngIf"],[1,"d-flex","flex-column","response-display-box"],[1,"d-flex","align-items-center",2,"margin-bottom","12px"],["imgWidth","32px","imgHeight","32px",2,"margin-right","12px",3,"oid"],[1,"name"],[1,"content"]],template:function(n,t){1&n&&(i["\u0275\u0275elementStart"](0,"div",0),i["\u0275\u0275elementStart"](1,"div",1),i["\u0275\u0275text"](2),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](3,"div",2),i["\u0275\u0275text"](4,"Scorecards Feedback"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementStart"](5,"div",3),i["\u0275\u0275template"](6,m,11,5,"ng-container",4),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&n&&(i["\u0275\u0275advance"](2),i["\u0275\u0275textInterpolate"]((null==t.data?null:t.data.candidateName)||"-"),i["\u0275\u0275advance"](4),i["\u0275\u0275property"]("ngForOf",t.data.data))},directives:[a.NgForOf,a.NgIf,r.a,c.a],styles:[".bg-container[_ngcontent-%COMP%]{padding:20px;overflow:hidden;height:100%}.bg-container[_ngcontent-%COMP%]   .candidate-name[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:500;color:var(--atsprimaryColor)}.bg-container[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:16px;font-weight:700;color:#111434;margin-bottom:16px}.bg-container[_ngcontent-%COMP%]   .inner-container[_ngcontent-%COMP%]{gap:16px;overflow-y:scroll;height:90%}.bg-container[_ngcontent-%COMP%]   .inner-container[_ngcontent-%COMP%]::-webkit-scrollbar{width:4px!important;height:1px!important}.bg-container[_ngcontent-%COMP%]   .question-row[_ngcontent-%COMP%]{margin:0 16px}.bg-container[_ngcontent-%COMP%]   .question-row[_ngcontent-%COMP%]   .question-no[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:500;color:#111434;opacity:.5;padding-right:8px}.bg-container[_ngcontent-%COMP%]   .question-row[_ngcontent-%COMP%]   .question[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:600;color:#111434;text-align:justify}.bg-container[_ngcontent-%COMP%]   .question-row[_ngcontent-%COMP%]   .arrow-icon[_ngcontent-%COMP%]{font-size:22px;width:22px;height:22px;color:#111434;cursor:pointer}.bg-container[_ngcontent-%COMP%]   .response-display-box[_ngcontent-%COMP%]{padding:12px;background-color:#f7f9fb;border-radius:8px}.bg-container[_ngcontent-%COMP%]   .response-display-box[_ngcontent-%COMP%]   .name[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:500;color:#111434}.bg-container[_ngcontent-%COMP%]   .response-display-box[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:400;color:#515965;word-wrap:break-word;text-align:justify}"]}),n})()}}]);