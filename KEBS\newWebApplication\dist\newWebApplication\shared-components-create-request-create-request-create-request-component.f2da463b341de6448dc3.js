(window.webpackJsonp=window.webpackJsonp||[]).push([[885],{DzVq:function(e,t,n){"use strict";n.r(t),n.d(t,"CreateRequestComponent",(function(){return kt})),n.d(t,"CreateRequestModule",(function(){return _t}));var i=n("mrSG"),l=n("3Pt+"),r=n("33Jv"),o=n("xG9w"),a=n("7pIB"),s=n("0IaG"),c=n("ofXK"),m=n("kmnG"),d=n("qFsG"),p=n("jaxi"),u=n("iadO"),h=n("FKr1"),g=n("lVl8"),f=n("NFeN"),v=n("bTqV"),y=n("Xi0T"),C=n("Xa2L"),S=n("Qu3c"),x=n("wd/R"),D=n("1jcm"),E=n("Kj3r"),b=n("1G5W"),F=n("fXoL"),A=n("jtHE"),I=n("XNiG"),P=n("NJ67"),w=n("d3UM"),k=n("WJ5W");function _(e,t){if(1&e){const e=F["\u0275\u0275getCurrentView"]();F["\u0275\u0275elementStart"](0,"mat-option",6),F["\u0275\u0275listener"]("click",(function(){F["\u0275\u0275restoreView"](e);const n=t.$implicit;return F["\u0275\u0275nextContext"]().emitChanges(n)})),F["\u0275\u0275text"](1),F["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;F["\u0275\u0275property"]("value",e.cc_id)("matTooltip",e.name),F["\u0275\u0275advance"](1),F["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let M=(()=>{class e extends P.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new l.j,this.fieldFilterCtrl=new l.j,this.list=[],this.required=!1,this.disableNone=!1,this.valueChange=new F.EventEmitter,this.disabled=!1,this.filteredList=new A.a,this.change=new F.EventEmitter,this._onDestroy=new I.b,this.emitChanges=e=>{console.log(e),this.change.emit(e)}}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(b.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(b.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}}return e.\u0275fac=function(t){return new(t||e)(F["\u0275\u0275directiveInject"](F.Renderer2))},e.\u0275cmp=F["\u0275\u0275defineComponent"]({type:e,selectors:[["app-cc-input-search"]],inputs:{list:"list",placeholder:"placeholder",required:"required",disableNone:"disableNone",disabled:"disabled"},outputs:{valueChange:"valueChange",change:"change"},features:[F["\u0275\u0275ProvidersFeature"]([{provide:l.t,useExisting:Object(F.forwardRef)(()=>e),multi:!0}]),F["\u0275\u0275InheritDefinitionFeature"],F["\u0275\u0275NgOnChangesFeature"]],decls:11,vars:12,consts:[["appearance","outline"],[3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value"],[3,"value","matTooltip","click",4,"ngFor","ngForOf"],[3,"value","matTooltip","click"]],template:function(e,t){1&e&&(F["\u0275\u0275elementStart"](0,"mat-form-field",0),F["\u0275\u0275elementStart"](1,"mat-label"),F["\u0275\u0275text"](2),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](3,"mat-select",1,2),F["\u0275\u0275elementStart"](5,"mat-option"),F["\u0275\u0275element"](6,"ngx-mat-select-search",3),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](7,"mat-option",4),F["\u0275\u0275text"](8),F["\u0275\u0275elementEnd"](),F["\u0275\u0275template"](9,_,2,3,"mat-option",5),F["\u0275\u0275pipe"](10,"async"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"]()),2&e&&(F["\u0275\u0275advance"](2),F["\u0275\u0275textInterpolate"](t.placeholder),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),F["\u0275\u0275advance"](3),F["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("value",null),F["\u0275\u0275advance"](1),F["\u0275\u0275textInterpolate"](t.disableNone?"Select One":"None"),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngForOf",F["\u0275\u0275pipeBind1"](10,10,t.filteredList)))},directives:[m.c,m.g,w.c,l.v,l.k,l.F,h.p,k.a,c.NgForOf,S.a],pipes:[c.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%}"]}),e})();var V=n("tk/3");const R=["allSelected"],T=["singleSelect"];function G(e,t){if(1&e&&(F["\u0275\u0275elementStart"](0,"mat-option",5),F["\u0275\u0275text"](1),F["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;F["\u0275\u0275property"]("value",e.id),F["\u0275\u0275advance"](1),F["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let N=(()=>{class e extends P.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new l.j,this.fieldFilterCtrl=new l.j,this.list=[],this.required=!1,this.valueChange=new F.EventEmitter,this.disabled=!1,this.filteredList=new A.a,this._onDestroy=new I.b,this.filterToggle=!0}ngOnInit(){this.list&&this.list.length>1&&this.fieldCtrl.setValue(this.list[0]),this.fieldFilterCtrl.valueChanges.pipe(Object(b.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(b.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(e){e.list&&this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(Array.isArray(e)?e:[])}toggleAllSelection(){if(this.allSelected.selected){const e=this.list.map(e=>e.id);this.fieldCtrl.patchValue(e),console.log("Checked values:",e)}}}return e.\u0275fac=function(t){return new(t||e)(F["\u0275\u0275directiveInject"](F.Renderer2))},e.\u0275cmp=F["\u0275\u0275defineComponent"]({type:e,selectors:[["app-filter-options"]],viewQuery:function(e,t){if(1&e&&(F["\u0275\u0275viewQuery"](R,!0),F["\u0275\u0275viewQuery"](T,!0)),2&e){let e;F["\u0275\u0275queryRefresh"](e=F["\u0275\u0275loadQuery"]())&&(t.allSelected=e.first),F["\u0275\u0275queryRefresh"](e=F["\u0275\u0275loadQuery"]())&&(t.singleSelect=e.first)}},inputs:{list:"list",placeholder:"placeholder",required:"required",disabled:"disabled",filterToggle:"filterToggle"},outputs:{valueChange:"valueChange"},features:[F["\u0275\u0275ProvidersFeature"]([{provide:l.t,useExisting:Object(F.forwardRef)(()=>e),multi:!0}]),F["\u0275\u0275InheritDefinitionFeature"],F["\u0275\u0275NgOnChangesFeature"]],decls:9,vars:10,consts:[["appearance","outline",2,"width","100%"],["multiple","",3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value",4,"ngFor","ngForOf"],[3,"value"]],template:function(e,t){1&e&&(F["\u0275\u0275elementStart"](0,"mat-form-field",0),F["\u0275\u0275elementStart"](1,"mat-label"),F["\u0275\u0275text"](2),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](3,"mat-select",1,2),F["\u0275\u0275elementStart"](5,"mat-option"),F["\u0275\u0275element"](6,"ngx-mat-select-search",3),F["\u0275\u0275elementEnd"](),F["\u0275\u0275template"](7,G,2,2,"mat-option",4),F["\u0275\u0275pipe"](8,"async"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"]()),2&e&&(F["\u0275\u0275advance"](2),F["\u0275\u0275textInterpolate"](t.placeholder),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),F["\u0275\u0275advance"](3),F["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngForOf",F["\u0275\u0275pipeBind1"](8,8,t.filteredList)))},directives:[m.c,m.g,w.c,l.v,l.k,l.F,h.p,k.a,c.NgForOf],pipes:[c.AsyncPipe],styles:[""]}),e})();var O=n("vgCS"),$=n("XXEo"),L=n("a1r6"),j=n("ucYs"),B=n("LcQX"),q=n("1A3m"),H=n("TmG/"),Y=n("me71"),U=n("H44p");function z(e,t){1&e&&(F["\u0275\u0275elementStart"](0,"div",3),F["\u0275\u0275element"](1,"mat-spinner",4),F["\u0275\u0275elementEnd"]())}function J(e,t){if(1&e&&(F["\u0275\u0275elementStart"](0,"div",22),F["\u0275\u0275elementStart"](1,"div",0),F["\u0275\u0275elementStart"](2,"span",23),F["\u0275\u0275text"](3,"Create Purchase Request (PR)"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](4,"div",0),F["\u0275\u0275elementStart"](5,"span",24),F["\u0275\u0275text"](6),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"]()),2&e){const e=F["\u0275\u0275nextContext"](2);F["\u0275\u0275advance"](6),F["\u0275\u0275textInterpolate1"](" By ",null==e.loggedInProfile?null:e.loggedInProfile.name," ")}}function W(e,t){if(1&e&&(F["\u0275\u0275elementStart"](0,"div",0),F["\u0275\u0275elementStart"](1,"span",26),F["\u0275\u0275text"](2),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"]()),2&e){const e=F["\u0275\u0275nextContext"](3);F["\u0275\u0275advance"](2),F["\u0275\u0275textInterpolate1"]("PR ",e.data.p2p_header_id,"")}}function Q(e,t){if(1&e&&(F["\u0275\u0275elementStart"](0,"div",22),F["\u0275\u0275template"](1,W,3,1,"div",25),F["\u0275\u0275elementEnd"]()),2&e){const e=F["\u0275\u0275nextContext"](2);F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",e.data&&"recallSubmit"==e.data.isFrom)}}const K=function(e){return{backgroundColor:e}},X=function(e){return{color:e}};function Z(e,t){if(1&e){const e=F["\u0275\u0275getCurrentView"]();F["\u0275\u0275elementStart"](0,"div",27),F["\u0275\u0275elementStart"](1,"div",16),F["\u0275\u0275listener"]("click",(function(){return F["\u0275\u0275restoreView"](e),F["\u0275\u0275nextContext"](2).changeActiveStep("paymentDetail")})),F["\u0275\u0275text"](2," 3 "),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](3,"span",17),F["\u0275\u0275listener"]("click",(function(){return F["\u0275\u0275restoreView"](e),F["\u0275\u0275nextContext"](2).changeActiveStep("checklist")})),F["\u0275\u0275text"](4,"Check list"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"]()}if(2&e){const e=F["\u0275\u0275nextContext"](2);F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngStyle",F["\u0275\u0275pureFunction1"](2,K,"checklist"==e.currentlyActiveStep?"#ff5253":"#817e7e")),F["\u0275\u0275advance"](2),F["\u0275\u0275property"]("ngStyle",F["\u0275\u0275pureFunction1"](4,X,"checklist"==e.currentlyActiveStep?"#ff5253":"#817e7e"))}}const ee=function(e){return{color:e}};function te(e,t){if(1&e&&(F["\u0275\u0275elementStart"](0,"mat-icon",69),F["\u0275\u0275text"](1),F["\u0275\u0275elementEnd"]()),2&e){const e=F["\u0275\u0275nextContext"]().$implicit;F["\u0275\u0275property"]("ngStyle",F["\u0275\u0275pureFunction1"](2,ee,1==e.get("hasBudget").value?"green":0==e.get("hasBudget").value?"#cf0001":"")),F["\u0275\u0275advance"](1),F["\u0275\u0275textInterpolate1"](" ",1==e.get("hasBudget").value?"check_circle":0==e.get("hasBudget").value?"cancel":"","")}}function ne(e,t){if(1&e&&(F["\u0275\u0275elementStart"](0,"div",70),F["\u0275\u0275text"](1),F["\u0275\u0275elementEnd"]()),2&e){const e=F["\u0275\u0275nextContext"]().$implicit;F["\u0275\u0275advance"](1),F["\u0275\u0275textInterpolate1"](" ",e.get("hasBudget").value?"Available":"Unavailable"," ")}}function ie(e,t){if(1&e&&(F["\u0275\u0275elementStart"](0,"div",0),F["\u0275\u0275text"](1," Balance available "),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](2,"div",0),F["\u0275\u0275text"](3),F["\u0275\u0275elementEnd"]()),2&e){const e=F["\u0275\u0275nextContext"]().$implicit;F["\u0275\u0275advance"](3),F["\u0275\u0275textInterpolate1"](" ",e.get("budgetBalance").value+" "+e.get("budgetBalanceCurrency").value," ")}}function le(e,t){if(1&e&&(F["\u0275\u0275elementStart"](0,"div",0),F["\u0275\u0275text"](1),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](2,"div",0),F["\u0275\u0275text"](3),F["\u0275\u0275elementEnd"]()),2&e){const e=F["\u0275\u0275nextContext"]().$implicit;F["\u0275\u0275advance"](1),F["\u0275\u0275textInterpolate1"](" ",e.name," "),F["\u0275\u0275advance"](2),F["\u0275\u0275textInterpolate1"](" ",e.designation," ")}}function re(e,t){if(1&e&&(F["\u0275\u0275elementStart"](0,"span"),F["\u0275\u0275element"](1,"app-user-image",73),F["\u0275\u0275template"](2,le,4,2,"ng-template",null,74,F["\u0275\u0275templateRefExtractor"]),F["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=F["\u0275\u0275reference"](3);F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("tooltip",n)("id",e.oid)}}function oe(e,t){if(1&e&&(F["\u0275\u0275elementStart"](0,"div",71),F["\u0275\u0275template"](1,re,4,2,"span",72),F["\u0275\u0275elementEnd"]()),2&e){const e=F["\u0275\u0275nextContext"]().$implicit;F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngForOf",e.get("approvers").value)}}function ae(e,t){if(1&e&&(F["\u0275\u0275elementStart"](0,"div",75),F["\u0275\u0275elementStart"](1,"span",76),F["\u0275\u0275text"](2,"CC Total"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275text"](3,"\xa0\xa0 "),F["\u0275\u0275element"](4,"app-currency",77),F["\u0275\u0275elementEnd"]()),2&e){const e=F["\u0275\u0275nextContext"]().$implicit;F["\u0275\u0275advance"](4),F["\u0275\u0275property"]("showActualAmount",!0)("currencyList",e.get("ccItemsTotalAmount").value)}}function se(e,t){1&e&&(F["\u0275\u0275elementStart"](0,"div",79),F["\u0275\u0275elementStart"](1,"mat-icon",80),F["\u0275\u0275text"](2," error_outline "),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](3,"span",81),F["\u0275\u0275text"](4," Initiate exceptional PR "),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"]())}function ce(e,t){1&e&&(F["\u0275\u0275elementStart"](0,"div",79),F["\u0275\u0275elementStart"](1,"mat-icon",82),F["\u0275\u0275text"](2," check_circle "),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](3,"span",81),F["\u0275\u0275text"](4," Exceptional PR "),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"]())}function me(e,t){if(1&e){const e=F["\u0275\u0275getCurrentView"]();F["\u0275\u0275elementStart"](0,"button",85),F["\u0275\u0275listener"]("click",(function(){return F["\u0275\u0275restoreView"](e),F["\u0275\u0275reference"](3).click()})),F["\u0275\u0275text"](1,"Upload Attachment "),F["\u0275\u0275elementStart"](2,"input",86,87),F["\u0275\u0275listener"]("change",(function(){F["\u0275\u0275restoreView"](e);const t=F["\u0275\u0275nextContext"](3).index;return F["\u0275\u0275nextContext"](3).uploadExceptionalPrAttachment(t)})),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"]()}if(2&e){const e=F["\u0275\u0275nextContext"](6);F["\u0275\u0275advance"](2),F["\u0275\u0275property"]("uploader",e.uploader)}}function de(e,t){1&e&&F["\u0275\u0275element"](0,"mat-spinner",88)}function pe(e,t){if(1&e&&(F["\u0275\u0275elementStart"](0,"div",79),F["\u0275\u0275template"](1,me,4,1,"button",83),F["\u0275\u0275template"](2,de,1,0,"mat-spinner",84),F["\u0275\u0275elementEnd"]()),2&e){const e=F["\u0275\u0275nextContext"](2).$implicit;F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",!e.get("isExceptionalPRAttachmentLoading").value),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",e.get("isExceptionalPRAttachmentLoading").value)}}function ue(e,t){if(1&e){const e=F["\u0275\u0275getCurrentView"]();F["\u0275\u0275elementStart"](0,"div",79),F["\u0275\u0275elementStart"](1,"span",89),F["\u0275\u0275text"](2),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](3,"span"),F["\u0275\u0275elementStart"](4,"button",90),F["\u0275\u0275listener"]("click",(function(){F["\u0275\u0275restoreView"](e);const t=F["\u0275\u0275nextContext"](2).index;return F["\u0275\u0275nextContext"](3).removeExceptionalPrAttachment(t)})),F["\u0275\u0275elementStart"](5,"mat-icon",91),F["\u0275\u0275text"](6,"close"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"]()}if(2&e){const e=F["\u0275\u0275nextContext"](2).$implicit;F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("matTooltip",null==e.get("exceptionalPRAttachment").value.files_json?null:e.get("exceptionalPRAttachment").value.files_json.fileName),F["\u0275\u0275advance"](1),F["\u0275\u0275textInterpolate1"](" ",null==e.get("exceptionalPRAttachment").value.files_json?null:e.get("exceptionalPRAttachment").value.files_json.fileName," ")}}function he(e,t){if(1&e&&(F["\u0275\u0275elementStart"](0,"div",46),F["\u0275\u0275template"](1,se,5,0,"div",78),F["\u0275\u0275template"](2,ce,5,0,"div",78),F["\u0275\u0275template"](3,pe,3,2,"div",78),F["\u0275\u0275template"](4,ue,7,2,"div",78),F["\u0275\u0275elementEnd"]()),2&e){const e=F["\u0275\u0275nextContext"]().$implicit;F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",!e.get("exceptionalPRAttachment").value.files_json),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",e.get("exceptionalPRAttachment").value.files_json),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",!e.get("exceptionalPRAttachment").value.files_json),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",e.get("exceptionalPRAttachment").value.files_json)}}function ge(e,t){if(1&e&&(F["\u0275\u0275elementStart"](0,"div",105),F["\u0275\u0275elementStart"](1,"span",76),F["\u0275\u0275text"](2,"Total"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275text"](3,"\xa0\xa0 "),F["\u0275\u0275elementStart"](4,"span",106),F["\u0275\u0275text"](5),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"]()),2&e){const e=F["\u0275\u0275nextContext"]().$implicit;F["\u0275\u0275advance"](5),F["\u0275\u0275textInterpolate2"]("",e.get("itemAmount").value," ",e.get("itemCurrencyName").value,"")}}function fe(e,t){if(1&e){const e=F["\u0275\u0275getCurrentView"]();F["\u0275\u0275elementStart"](0,"div",92),F["\u0275\u0275elementStart"](1,"div",93),F["\u0275\u0275elementStart"](2,"div",94),F["\u0275\u0275element"](3,"div",95),F["\u0275\u0275elementStart"](4,"div",42),F["\u0275\u0275elementStart"](5,"div",28),F["\u0275\u0275elementStart"](6,"div",7),F["\u0275\u0275elementStart"](7,"mat-form-field",43),F["\u0275\u0275elementStart"](8,"mat-label"),F["\u0275\u0275text"](9,"Item name*"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275element"](10,"input",96),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](11,"div",31),F["\u0275\u0275elementStart"](12,"div",28),F["\u0275\u0275elementStart"](13,"div",7),F["\u0275\u0275element"](14,"app-input-search",97),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](15,"div",31),F["\u0275\u0275elementStart"](16,"div",28),F["\u0275\u0275elementStart"](17,"div",7),F["\u0275\u0275element"](18,"app-input-search",98),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](19,"div",71),F["\u0275\u0275elementStart"](20,"div",55),F["\u0275\u0275listener"]("click",(function(){F["\u0275\u0275restoreView"](e);const t=F["\u0275\u0275nextContext"]().index;return F["\u0275\u0275nextContext"](3).addItem(t)})),F["\u0275\u0275elementStart"](21,"mat-icon"),F["\u0275\u0275text"](22,"add"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](23,"div",56),F["\u0275\u0275listener"]("click",(function(){F["\u0275\u0275restoreView"](e);const n=t.index,i=F["\u0275\u0275nextContext"]().index,l=F["\u0275\u0275nextContext"](3);return l.removeItem(i,n),l.changeItemAmount(i)})),F["\u0275\u0275elementStart"](24,"mat-icon"),F["\u0275\u0275text"](25,"delete"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](26,"div",94),F["\u0275\u0275element"](27,"div",99),F["\u0275\u0275elementStart"](28,"div",100),F["\u0275\u0275elementStart"](29,"mat-form-field",43),F["\u0275\u0275elementStart"](30,"mat-label"),F["\u0275\u0275text"](31,"No of units*"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](32,"input",101),F["\u0275\u0275listener"]("keyup",(function(){F["\u0275\u0275restoreView"](e);const t=F["\u0275\u0275nextContext"]().index;return F["\u0275\u0275nextContext"](3).changeItemAmount(t)})),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](33,"div",31),F["\u0275\u0275elementStart"](34,"div",28),F["\u0275\u0275elementStart"](35,"div",7),F["\u0275\u0275elementStart"](36,"mat-form-field",43),F["\u0275\u0275elementStart"](37,"mat-label"),F["\u0275\u0275text"](38,"Unit Price*"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](39,"input",102),F["\u0275\u0275listener"]("keyup",(function(){F["\u0275\u0275restoreView"](e);const t=F["\u0275\u0275nextContext"]().index;return F["\u0275\u0275nextContext"](3).changeItemAmount(t)})),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](40,"div",31),F["\u0275\u0275elementStart"](41,"div",28),F["\u0275\u0275elementStart"](42,"div",7),F["\u0275\u0275elementStart"](43,"app-input-search",103),F["\u0275\u0275listener"]("change",(function(){F["\u0275\u0275restoreView"](e);const t=F["\u0275\u0275nextContext"]().index;return F["\u0275\u0275nextContext"](3).changeItemAmount(t)})),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](44,"div",31),F["\u0275\u0275elementStart"](45,"div",28),F["\u0275\u0275elementStart"](46,"div",65),F["\u0275\u0275template"](47,ge,6,2,"div",104),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=t.index,i=F["\u0275\u0275nextContext"](4);F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("formGroupName",n),F["\u0275\u0275advance"](13),F["\u0275\u0275property"]("list",i.services),F["\u0275\u0275advance"](4),F["\u0275\u0275property"]("list",i.uomList),F["\u0275\u0275advance"](25),F["\u0275\u0275property"]("list",i.currencies),F["\u0275\u0275advance"](4),F["\u0275\u0275property"]("ngIf",e.get("itemAmount").valid)}}function ve(e,t){if(1&e){const e=F["\u0275\u0275getCurrentView"]();F["\u0275\u0275elementStart"](0,"div",48),F["\u0275\u0275elementStart"](1,"div",49),F["\u0275\u0275elementStart"](2,"div",41),F["\u0275\u0275elementStart"](3,"div",50),F["\u0275\u0275elementStart"](4,"div",28),F["\u0275\u0275elementStart"](5,"div",7),F["\u0275\u0275elementStart"](6,"app-cc-input-search",51),F["\u0275\u0275listener"]("change",(function(){F["\u0275\u0275restoreView"](e);const n=t.index;return F["\u0275\u0275nextContext"](3).changeCc(n)})),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](7,"div",52),F["\u0275\u0275elementStart"](8,"div",28),F["\u0275\u0275elementStart"](9,"div",7),F["\u0275\u0275elementStart"](10,"app-input-search",53),F["\u0275\u0275listener"]("change",(function(){F["\u0275\u0275restoreView"](e);const n=t.index;return F["\u0275\u0275nextContext"](3).changeCc(n)})),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](11,"div",54),F["\u0275\u0275elementStart"](12,"div",55),F["\u0275\u0275listener"]("click",(function(){return F["\u0275\u0275restoreView"](e),F["\u0275\u0275nextContext"](3).addCC()})),F["\u0275\u0275elementStart"](13,"mat-icon"),F["\u0275\u0275text"](14,"add"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](15,"div",56),F["\u0275\u0275listener"]("click",(function(){F["\u0275\u0275restoreView"](e);const n=t.index;return F["\u0275\u0275nextContext"](3).removeCC(n)})),F["\u0275\u0275elementStart"](16,"mat-icon"),F["\u0275\u0275text"](17,"delete"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](18,"div",57),F["\u0275\u0275elementStart"](19,"div",0),F["\u0275\u0275elementStart"](20,"div",58),F["\u0275\u0275template"](21,te,2,4,"mat-icon",59),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](22,"div",60),F["\u0275\u0275template"](23,ne,2,1,"div",61),F["\u0275\u0275elementStart"](24,"div",62),F["\u0275\u0275text"](25),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275template"](26,ie,4,1,"ng-template",null,63,F["\u0275\u0275templateRefExtractor"]),F["\u0275\u0275template"](28,oe,2,1,"div",64),F["\u0275\u0275elementStart"](29,"div",31),F["\u0275\u0275elementStart"](30,"div",28),F["\u0275\u0275elementStart"](31,"div",65),F["\u0275\u0275template"](32,ae,5,2,"div",66),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275template"](33,he,5,4,"div",67),F["\u0275\u0275elementEnd"](),F["\u0275\u0275template"](34,fe,48,5,"div",68),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=t.index,i=F["\u0275\u0275reference"](27),l=F["\u0275\u0275nextContext"](3);F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("formGroupName",n),F["\u0275\u0275advance"](5),F["\u0275\u0275property"]("list",l.costCenters),F["\u0275\u0275advance"](4),F["\u0275\u0275property"]("list",l.subGroups),F["\u0275\u0275advance"](8),F["\u0275\u0275property"]("tooltip",i),F["\u0275\u0275advance"](3),F["\u0275\u0275property"]("ngIf",e.get("hasBudget").valid),F["\u0275\u0275advance"](2),F["\u0275\u0275property"]("ngIf",e.get("hasBudget").valid),F["\u0275\u0275advance"](2),F["\u0275\u0275textInterpolate1"](" ",e.get("budgetBalance").value+" "+e.get("budgetBalanceCurrency").value," "),F["\u0275\u0275advance"](3),F["\u0275\u0275property"]("ngIf",e.get("approvers").valid),F["\u0275\u0275advance"](4),F["\u0275\u0275property"]("ngIf",""!=e.get("ccItemsTotalAmount").value),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",e.get("isExceptionalPR").value),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngForOf",e.get("items").controls)}}function ye(e,t){1&e&&(F["\u0275\u0275elementStart"](0,"div",36),F["\u0275\u0275elementStart"](1,"div",110),F["\u0275\u0275elementStart"](2,"div",0),F["\u0275\u0275elementStart"](3,"span",30),F["\u0275\u0275text"](4,"Vendor Type"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"]())}function Ce(e,t){if(1&e&&(F["\u0275\u0275elementStart"](0,"div",107),F["\u0275\u0275template"](1,ye,5,0,"div",108),F["\u0275\u0275elementStart"](2,"div",28),F["\u0275\u0275elementStart"](3,"div",109),F["\u0275\u0275elementStart"](4,"div",0),F["\u0275\u0275elementStart"](5,"span",30),F["\u0275\u0275text"](6),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"]()),2&e){const e=F["\u0275\u0275nextContext"](3);F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",""!=e.vendorTypeDisplay),F["\u0275\u0275advance"](5),F["\u0275\u0275textInterpolate"](e.vendorTypeDisplay)}}function Se(e,t){if(1&e&&(F["\u0275\u0275elementStart"](0,"div",35),F["\u0275\u0275elementStart"](1,"div",36),F["\u0275\u0275elementStart"](2,"span",30),F["\u0275\u0275text"](3,"Customer Billing *"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](4,"div",37),F["\u0275\u0275element"](5,"app-input-search",111),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"]()),2&e){const e=F["\u0275\u0275nextContext"](3);F["\u0275\u0275advance"](5),F["\u0275\u0275property"]("list",e.customerBillingType)}}function xe(e,t){if(1&e&&(F["\u0275\u0275elementStart"](0,"div",35),F["\u0275\u0275elementStart"](1,"div",36),F["\u0275\u0275elementStart"](2,"span",30),F["\u0275\u0275text"](3,"Choose People Involved"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](4,"div",37),F["\u0275\u0275element"](5,"app-filter-options",112),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"]()),2&e){const e=F["\u0275\u0275nextContext"](3);F["\u0275\u0275advance"](5),F["\u0275\u0275property"]("list",e.contractEmployeeList)}}function De(e,t){1&e&&(F["\u0275\u0275elementStart"](0,"mat-icon"),F["\u0275\u0275text"](1,"navigate_next"),F["\u0275\u0275elementEnd"]())}function Ee(e,t){if(1&e){const e=F["\u0275\u0275getCurrentView"]();F["\u0275\u0275elementContainerStart"](0),F["\u0275\u0275elementStart"](1,"div",28),F["\u0275\u0275elementStart"](2,"div",29),F["\u0275\u0275elementStart"](3,"div",0),F["\u0275\u0275elementStart"](4,"span",30),F["\u0275\u0275text"](5,"Choose the cost center *"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](6,"div",31),F["\u0275\u0275elementStart"](7,"div",0),F["\u0275\u0275elementStart"](8,"span",30),F["\u0275\u0275text"](9,"Choose the Sub Group *"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](10,"div",32),F["\u0275\u0275elementStart"](11,"div",0),F["\u0275\u0275elementStart"](12,"span",30),F["\u0275\u0275text"](13,"Actions"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](14,"div",33),F["\u0275\u0275elementStart"](15,"div",0),F["\u0275\u0275elementStart"](16,"span",30),F["\u0275\u0275text"](17,"Balance availability"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275template"](18,ve,35,11,"div",34),F["\u0275\u0275elementStart"](19,"div",28),F["\u0275\u0275elementStart"](20,"div",35),F["\u0275\u0275elementStart"](21,"div",36),F["\u0275\u0275elementStart"](22,"span",30),F["\u0275\u0275text"](23,"Choose the Vendor *"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](24,"div",37),F["\u0275\u0275element"](25,"app-input-search",38),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275template"](26,Ce,7,2,"div",39),F["\u0275\u0275elementEnd"](),F["\u0275\u0275template"](27,Se,6,1,"div",40),F["\u0275\u0275template"](28,xe,6,1,"div",40),F["\u0275\u0275elementStart"](29,"div",0),F["\u0275\u0275elementStart"](30,"div",7),F["\u0275\u0275elementStart"](31,"div",0),F["\u0275\u0275elementStart"](32,"span",30),F["\u0275\u0275text"](33,"PR description (Reason for choosing vendor)"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](34,"div",41),F["\u0275\u0275elementStart"](35,"div",42),F["\u0275\u0275elementStart"](36,"mat-form-field",43),F["\u0275\u0275element"](37,"textarea",44),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](38,"div",0),F["\u0275\u0275element"](39,"div",45),F["\u0275\u0275elementStart"](40,"div",46),F["\u0275\u0275elementStart"](41,"button",47),F["\u0275\u0275listener"]("click",(function(){return F["\u0275\u0275restoreView"](e),F["\u0275\u0275nextContext"](2).changeActiveStep("paymentDetail")})),F["\u0275\u0275template"](42,De,2,0,"mat-icon",21),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=F["\u0275\u0275nextContext"](2);F["\u0275\u0275advance"](18),F["\u0275\u0275property"]("ngForOf",e.generalDetailsFormgroup.get("costCenterArr").controls),F["\u0275\u0275advance"](7),F["\u0275\u0275property"]("list",e.vendors),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",e.isVendorTypeVisible),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",e.isBillablePRAailable),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",e.isPeopleInvolvedAvailable),F["\u0275\u0275advance"](14),F["\u0275\u0275property"]("ngIf",!e.isLoading)}}function be(e,t){if(1&e&&(F["\u0275\u0275elementStart"](0,"div",75),F["\u0275\u0275elementStart"](1,"span",76),F["\u0275\u0275text"](2,"Total"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275text"](3,"\xa0\xa0 "),F["\u0275\u0275element"](4,"app-currency",77),F["\u0275\u0275elementEnd"]()),2&e){const e=F["\u0275\u0275nextContext"](3);F["\u0275\u0275advance"](4),F["\u0275\u0275property"]("showActualAmount",!0)("currencyList",e.generalDetailsFormgroup.get("amount").value)}}function Fe(e,t){if(1&e&&(F["\u0275\u0275elementStart"](0,"div",120),F["\u0275\u0275elementStart"](1,"div",75),F["\u0275\u0275elementStart"](2,"span",76),F["\u0275\u0275text"](3),F["\u0275\u0275elementEnd"](),F["\u0275\u0275text"](4,"\xa0\xa0 "),F["\u0275\u0275element"](5,"app-currency",77),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;F["\u0275\u0275advance"](3),F["\u0275\u0275textInterpolate"](e.ccDetail.name),F["\u0275\u0275advance"](2),F["\u0275\u0275property"]("showActualAmount",!0)("currencyList",e.convertedAmount)}}function Ae(e,t){if(1&e&&(F["\u0275\u0275elementStart"](0,"div",118),F["\u0275\u0275template"](1,Fe,6,3,"div",119),F["\u0275\u0275elementEnd"]()),2&e){const e=F["\u0275\u0275nextContext"](4);F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngForOf",e.paymentDetailsFormGroup.get("ccSplitDetails").value)}}function Ie(e,t){if(1&e&&(F["\u0275\u0275elementStart"](0,"div",123),F["\u0275\u0275elementStart"](1,"div",93),F["\u0275\u0275elementStart"](2,"mat-form-field",43),F["\u0275\u0275elementStart"](3,"mat-label"),F["\u0275\u0275text"](4),F["\u0275\u0275elementEnd"](),F["\u0275\u0275element"](5,"input",124),F["\u0275\u0275elementStart"](6,"span",125),F["\u0275\u0275text"](7),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=t.index,i=F["\u0275\u0275nextContext"](6);F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("formGroupName",n),F["\u0275\u0275advance"](3),F["\u0275\u0275textInterpolate1"]("Amount for ",e.get("ccDetail").value.name,""),F["\u0275\u0275advance"](3),F["\u0275\u0275textInterpolate"](i.preferredCurrency)}}function Pe(e,t){if(1&e){const e=F["\u0275\u0275getCurrentView"]();F["\u0275\u0275elementContainerStart"](0),F["\u0275\u0275template"](1,Ie,8,3,"div",121),F["\u0275\u0275elementStart"](2,"div",46),F["\u0275\u0275elementStart"](3,"button",122),F["\u0275\u0275listener"]("click",(function(){return F["\u0275\u0275restoreView"](e),F["\u0275\u0275nextContext"](5).applyEnteredCCSplitups()})),F["\u0275\u0275text"](4,"Apply"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=F["\u0275\u0275nextContext"](5);F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngForOf",e.paymentDetailsFormGroup.get("ccSplitDetails").controls)}}function we(e,t){if(1&e&&(F["\u0275\u0275elementStart"](0,"div",120),F["\u0275\u0275elementStart"](1,"div",75),F["\u0275\u0275elementStart"](2,"span",76),F["\u0275\u0275text"](3),F["\u0275\u0275elementEnd"](),F["\u0275\u0275text"](4,"\xa0\xa0 "),F["\u0275\u0275element"](5,"app-currency",77),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;F["\u0275\u0275advance"](3),F["\u0275\u0275textInterpolate"](e.ccDetail.name),F["\u0275\u0275advance"](2),F["\u0275\u0275property"]("showActualAmount",!0)("currencyList",e.convertedAmount)}}function ke(e,t){if(1&e){const e=F["\u0275\u0275getCurrentView"]();F["\u0275\u0275elementContainerStart"](0),F["\u0275\u0275template"](1,we,6,3,"div",119),F["\u0275\u0275elementStart"](2,"button",126),F["\u0275\u0275listener"]("click",(function(){return F["\u0275\u0275restoreView"](e),F["\u0275\u0275nextContext"](5).isManualSplitApplied=!1})),F["\u0275\u0275elementStart"](3,"mat-icon"),F["\u0275\u0275text"](4,"cancel"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=F["\u0275\u0275nextContext"](5);F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngForOf",e.paymentDetailsFormGroup.get("ccSplitDetails").value)}}function _e(e,t){if(1&e&&(F["\u0275\u0275elementStart"](0,"div",118),F["\u0275\u0275template"](1,Pe,5,1,"ng-container",21),F["\u0275\u0275template"](2,ke,5,1,"ng-container",21),F["\u0275\u0275elementEnd"]()),2&e){const e=F["\u0275\u0275nextContext"](4);F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",!e.isManualSplitApplied),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",e.isManualSplitApplied)}}const Me=function(e){return{"split-active":e}};function Ve(e,t){if(1&e){const e=F["\u0275\u0275getCurrentView"]();F["\u0275\u0275elementContainerStart"](0),F["\u0275\u0275elementStart"](1,"div",0),F["\u0275\u0275elementStart"](2,"div",42),F["\u0275\u0275elementStart"](3,"div",0),F["\u0275\u0275elementStart"](4,"span",30),F["\u0275\u0275text"](5,"Split Amount Between Cost Centers"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](6,"div",28),F["\u0275\u0275elementStart"](7,"div",7),F["\u0275\u0275elementStart"](8,"button",116),F["\u0275\u0275listener"]("click",(function(){return F["\u0275\u0275restoreView"](e),F["\u0275\u0275nextContext"](3).changeCCSplitType("auto")})),F["\u0275\u0275text"](9,"Auto split"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](10,"button",116),F["\u0275\u0275listener"]("click",(function(){return F["\u0275\u0275restoreView"](e),F["\u0275\u0275nextContext"](3).changeCCSplitType("manual")})),F["\u0275\u0275text"](11,"Manual Split"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275template"](12,Ae,2,1,"div",117),F["\u0275\u0275template"](13,_e,3,2,"div",117),F["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=F["\u0275\u0275nextContext"](3);F["\u0275\u0275advance"](8),F["\u0275\u0275property"]("ngClass",F["\u0275\u0275pureFunction1"](4,Me,"auto"==e.paymentDetailsFormGroup.get("ccSplitType").value)),F["\u0275\u0275advance"](2),F["\u0275\u0275property"]("ngClass",F["\u0275\u0275pureFunction1"](6,Me,"manual"==e.paymentDetailsFormGroup.get("ccSplitType").value)),F["\u0275\u0275advance"](2),F["\u0275\u0275property"]("ngIf","auto"==e.paymentDetailsFormGroup.get("ccSplitType").value),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf","manual"==e.paymentDetailsFormGroup.get("ccSplitType").value)}}function Re(e,t){1&e&&(F["\u0275\u0275elementStart"](0,"div",28),F["\u0275\u0275elementStart"](1,"div",42),F["\u0275\u0275elementStart"](2,"div",0),F["\u0275\u0275elementStart"](3,"span",30),F["\u0275\u0275text"](4,"Invoice Date *"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"]())}function Te(e,t){if(1&e){const e=F["\u0275\u0275getCurrentView"]();F["\u0275\u0275elementStart"](0,"button",85),F["\u0275\u0275listener"]("click",(function(){return F["\u0275\u0275restoreView"](e),F["\u0275\u0275reference"](3).click()})),F["\u0275\u0275text"](1,"Upload Invoice "),F["\u0275\u0275elementStart"](2,"input",86,87),F["\u0275\u0275listener"]("change",(function(t){return F["\u0275\u0275restoreView"](e),F["\u0275\u0275nextContext"](5).onSingleFileAdd(t)})),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"]()}if(2&e){const e=F["\u0275\u0275nextContext"](5);F["\u0275\u0275advance"](2),F["\u0275\u0275property"]("uploader",e.uploader)}}function Ge(e,t){1&e&&F["\u0275\u0275element"](0,"mat-spinner",88)}function Ne(e,t){if(1&e){const e=F["\u0275\u0275getCurrentView"]();F["\u0275\u0275elementStart"](0,"div"),F["\u0275\u0275elementStart"](1,"span",138),F["\u0275\u0275text"](2),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](3,"span",139),F["\u0275\u0275elementStart"](4,"button",126),F["\u0275\u0275listener"]("click",(function(){return F["\u0275\u0275restoreView"](e),F["\u0275\u0275nextContext"](5).removeInvoice(0)})),F["\u0275\u0275elementStart"](5,"mat-icon",91),F["\u0275\u0275text"](6,"close"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"]()}if(2&e){const e=F["\u0275\u0275nextContext"](5);F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("matTooltip",null==e.paymentDetailsFormGroup.get("attachmentData").value.files_json?null:e.paymentDetailsFormGroup.get("attachmentData").value.files_json.fileName),F["\u0275\u0275advance"](1),F["\u0275\u0275textInterpolate1"](" ",null==e.paymentDetailsFormGroup.get("attachmentData").value.files_json?null:e.paymentDetailsFormGroup.get("attachmentData").value.files_json.fileName," ")}}function Oe(e,t){if(1&e&&(F["\u0275\u0275elementStart"](0,"div",41),F["\u0275\u0275elementStart"](1,"div",42),F["\u0275\u0275elementStart"](2,"mat-form-field",43),F["\u0275\u0275elementStart"](3,"mat-label"),F["\u0275\u0275text"](4,"Invoice Date"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275element"](5,"input",132),F["\u0275\u0275element"](6,"mat-datepicker-toggle",133),F["\u0275\u0275element"](7,"mat-datepicker",null,134),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](9,"div",135),F["\u0275\u0275elementStart"](10,"mat-form-field",43),F["\u0275\u0275elementStart"](11,"mat-label"),F["\u0275\u0275text"](12,"Invoice number"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275element"](13,"input",136),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](14,"div",137),F["\u0275\u0275template"](15,Te,4,1,"button",83),F["\u0275\u0275template"](16,Ge,1,0,"mat-spinner",84),F["\u0275\u0275template"](17,Ne,7,2,"div",21),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"]()),2&e){const e=F["\u0275\u0275reference"](8),t=F["\u0275\u0275nextContext"](4);F["\u0275\u0275advance"](5),F["\u0275\u0275property"]("matDatepicker",e),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("for",e),F["\u0275\u0275advance"](9),F["\u0275\u0275property"]("ngIf",!t.paymentDetailsFormGroup.get("uploadInProgress").value&&!t.paymentDetailsFormGroup.get("attachmentData").value.files_json),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",1==t.paymentDetailsFormGroup.get("uploadInProgress").value),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",t.paymentDetailsFormGroup.get("attachmentData").value.files_json&&!t.paymentDetailsFormGroup.get("uploadInProgress").value)}}function $e(e,t){if(1&e){const e=F["\u0275\u0275getCurrentView"]();F["\u0275\u0275elementContainerStart"](0),F["\u0275\u0275elementStart"](1,"div",118),F["\u0275\u0275elementStart"](2,"div",42),F["\u0275\u0275elementStart"](3,"div",0),F["\u0275\u0275elementStart"](4,"span",30),F["\u0275\u0275text"](5,"Add recurrence details"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](6,"div",28),F["\u0275\u0275elementStart"](7,"div",42),F["\u0275\u0275element"](8,"app-input-search",140),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](9,"div",46),F["\u0275\u0275elementStart"](10,"mat-form-field",43),F["\u0275\u0275elementStart"](11,"mat-label"),F["\u0275\u0275text"](12,"Payment Start Date"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](13,"input",141),F["\u0275\u0275listener"]("dateChange",(function(){return F["\u0275\u0275restoreView"](e),F["\u0275\u0275nextContext"](4).onStartDateSelection()})),F["\u0275\u0275elementEnd"](),F["\u0275\u0275element"](14,"mat-datepicker-toggle",133),F["\u0275\u0275element"](15,"mat-datepicker",null,142),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](17,"div",46),F["\u0275\u0275elementStart"](18,"mat-form-field",43),F["\u0275\u0275elementStart"](19,"mat-label"),F["\u0275\u0275text"](20,"Payment End Date"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](21,"input",143),F["\u0275\u0275listener"]("dateChange",(function(){return F["\u0275\u0275restoreView"](e),F["\u0275\u0275nextContext"](4).onStartDateSelection()})),F["\u0275\u0275elementEnd"](),F["\u0275\u0275element"](22,"mat-datepicker-toggle",133),F["\u0275\u0275element"](23,"mat-datepicker",null,144),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](25,"div",115),F["\u0275\u0275elementStart"](26,"mat-form-field",145),F["\u0275\u0275elementStart"](27,"mat-label"),F["\u0275\u0275text"](28,"Invoice Date *"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275element"](29,"input",132),F["\u0275\u0275element"](30,"mat-datepicker-toggle",133),F["\u0275\u0275element"](31,"mat-datepicker",null,146),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](33,"div",147),F["\u0275\u0275elementStart"](34,"button",148),F["\u0275\u0275listener"]("click",(function(){return F["\u0275\u0275restoreView"](e),F["\u0275\u0275nextContext"](4).onClearButtonClick()})),F["\u0275\u0275text"](35,"Clear"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=F["\u0275\u0275reference"](16),t=F["\u0275\u0275reference"](24),n=F["\u0275\u0275reference"](32),i=F["\u0275\u0275nextContext"](4);F["\u0275\u0275advance"](8),F["\u0275\u0275property"]("list",i.paymentReccurences),F["\u0275\u0275advance"](5),F["\u0275\u0275property"]("matDatepicker",e),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("for",e),F["\u0275\u0275advance"](7),F["\u0275\u0275property"]("matDatepicker",t)("min",i.MinDateRecurring),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("for",t),F["\u0275\u0275advance"](7),F["\u0275\u0275property"]("matDatepicker",n),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("for",n)}}function Le(e,t){1&e&&(F["\u0275\u0275elementStart"](0,"div",151),F["\u0275\u0275elementStart"](1,"div",29),F["\u0275\u0275elementStart"](2,"div",0),F["\u0275\u0275elementStart"](3,"span",30),F["\u0275\u0275text"](4,"Recurring payments"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](5,"div",152),F["\u0275\u0275elementStart"](6,"div",0),F["\u0275\u0275elementStart"](7,"span",30),F["\u0275\u0275text"](8,"Amount"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](9,"div",29),F["\u0275\u0275elementStart"](10,"span",30),F["\u0275\u0275text"](11,"Invoice Date *"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](12,"div",29),F["\u0275\u0275elementStart"](13,"span",30),F["\u0275\u0275text"](14,"Invoice number"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](15,"div",120),F["\u0275\u0275elementStart"](16,"div",153),F["\u0275\u0275elementStart"](17,"span",30),F["\u0275\u0275text"](18,"Budget availability"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"]())}function je(e,t){if(1&e){const e=F["\u0275\u0275getCurrentView"]();F["\u0275\u0275elementStart"](0,"button",85),F["\u0275\u0275listener"]("click",(function(){return F["\u0275\u0275restoreView"](e),F["\u0275\u0275reference"](3).click()})),F["\u0275\u0275text"](1,"Upload Invoice "),F["\u0275\u0275elementStart"](2,"input",86,166),F["\u0275\u0275listener"]("change",(function(t){F["\u0275\u0275restoreView"](e);const n=F["\u0275\u0275nextContext"]().index;return F["\u0275\u0275nextContext"](5).onFileAdd(n,t)})),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"]()}if(2&e){const e=F["\u0275\u0275nextContext"](6);F["\u0275\u0275advance"](2),F["\u0275\u0275property"]("uploader",e.uploader)}}function Be(e,t){1&e&&F["\u0275\u0275element"](0,"mat-spinner",88)}function qe(e,t){if(1&e){const e=F["\u0275\u0275getCurrentView"]();F["\u0275\u0275elementStart"](0,"div"),F["\u0275\u0275elementStart"](1,"span",138),F["\u0275\u0275text"](2),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](3,"span",139),F["\u0275\u0275elementStart"](4,"button",126),F["\u0275\u0275listener"]("click",(function(){F["\u0275\u0275restoreView"](e);const t=F["\u0275\u0275nextContext"]().index;return F["\u0275\u0275nextContext"](5).removeInvoice(t)})),F["\u0275\u0275elementStart"](5,"mat-icon",91),F["\u0275\u0275text"](6,"close"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"]()}if(2&e){const e=F["\u0275\u0275nextContext"]().$implicit;F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("matTooltip",null==e.get("attachmentData").value.files_json?null:e.get("attachmentData").value.files_json.fileName),F["\u0275\u0275advance"](1),F["\u0275\u0275textInterpolate1"](" ",null==e.get("attachmentData").value.files_json?null:e.get("attachmentData").value.files_json.fileName," ")}}function He(e,t){if(1&e){const e=F["\u0275\u0275getCurrentView"]();F["\u0275\u0275elementStart"](0,"div",167),F["\u0275\u0275listener"]("click",(function(){F["\u0275\u0275restoreView"](e);const t=F["\u0275\u0275nextContext"]().index;return F["\u0275\u0275nextContext"](5).removeMilestone(t)})),F["\u0275\u0275elementStart"](1,"mat-icon"),F["\u0275\u0275text"](2,"delete"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"]()}}function Ye(e,t){1&e&&F["\u0275\u0275element"](0,"mat-spinner",168)}function Ue(e,t){if(1&e&&(F["\u0275\u0275elementStart"](0,"mat-icon",169),F["\u0275\u0275text"](1),F["\u0275\u0275elementEnd"]()),2&e){const e=F["\u0275\u0275nextContext"]().$implicit;F["\u0275\u0275property"]("ngStyle",F["\u0275\u0275pureFunction1"](2,ee,1==e.get("hasBudgetAvailable").value?"green":0==e.get("hasBudgetAvailable").value?"#cf0001":"")),F["\u0275\u0275advance"](1),F["\u0275\u0275textInterpolate1"](" ",1==e.get("hasBudgetAvailable").value?"check_circle":0==e.get("hasBudgetAvailable").value?"cancel":"","")}}function ze(e,t){if(1&e&&(F["\u0275\u0275elementStart"](0,"span",170),F["\u0275\u0275text"](1),F["\u0275\u0275elementEnd"]()),2&e){const e=F["\u0275\u0275nextContext"]().$implicit;F["\u0275\u0275advance"](1),F["\u0275\u0275textInterpolate1"](" ",e.get("hasBudgetAvailable").value?"Budget available":"Budget unavailable"," ")}}function Je(e,t){if(1&e&&(F["\u0275\u0275elementStart"](0,"div",154),F["\u0275\u0275elementStart"](1,"div",155),F["\u0275\u0275elementStart"](2,"div",28),F["\u0275\u0275elementStart"](3,"div",156),F["\u0275\u0275elementStart"](4,"strong"),F["\u0275\u0275text"](5),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](6,"div",157),F["\u0275\u0275element"](7,"app-currency",77),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](8,"div",158),F["\u0275\u0275elementStart"](9,"mat-form-field",43),F["\u0275\u0275elementStart"](10,"mat-label"),F["\u0275\u0275text"](11,"Invoice Date"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275element"](12,"input",132),F["\u0275\u0275element"](13,"mat-datepicker-toggle",133),F["\u0275\u0275element"](14,"mat-datepicker",null,159),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](16,"div",46),F["\u0275\u0275elementStart"](17,"mat-form-field",43),F["\u0275\u0275elementStart"](18,"mat-label"),F["\u0275\u0275text"](19,"Invoice number"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275element"](20,"input",136),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](21,"div",160),F["\u0275\u0275template"](22,je,4,1,"button",83),F["\u0275\u0275template"](23,Be,1,0,"mat-spinner",84),F["\u0275\u0275template"](24,qe,7,2,"div",21),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](25,"div",161),F["\u0275\u0275template"](26,He,3,0,"div",162),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](27,"div",160),F["\u0275\u0275template"](28,Ye,1,0,"mat-spinner",163),F["\u0275\u0275template"](29,Ue,2,4,"mat-icon",164),F["\u0275\u0275template"](30,ze,2,1,"span",165),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=t.index,i=F["\u0275\u0275reference"](15),l=F["\u0275\u0275nextContext"](5);F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("formGroupName",n),F["\u0275\u0275advance"](4),F["\u0275\u0275textInterpolate1"](" ",e.get("milestoneName").value," "),F["\u0275\u0275advance"](2),F["\u0275\u0275property"]("showActualAmount",!0)("currencyList",e.get("milestoneAmount").value),F["\u0275\u0275advance"](5),F["\u0275\u0275property"]("matDatepicker",i),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("for",i),F["\u0275\u0275advance"](9),F["\u0275\u0275property"]("ngIf",!e.get("uploadInProgress").value&&!e.get("attachmentData").value.files_json),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",1==e.get("uploadInProgress").value),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",e.get("attachmentData").value.files_json&&!e.get("uploadInProgress").value),F["\u0275\u0275advance"](2),F["\u0275\u0275property"]("ngIf",l.isAvailable),F["\u0275\u0275advance"](2),F["\u0275\u0275property"]("ngIf",!e.get("hasBudgetAvailable").valid),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",e.get("hasBudgetAvailable").valid),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",e.get("hasBudgetAvailable").valid)}}function We(e,t){if(1&e&&(F["\u0275\u0275elementContainerStart"](0),F["\u0275\u0275template"](1,Le,19,0,"div",149),F["\u0275\u0275template"](2,Je,31,13,"div",150),F["\u0275\u0275elementContainerEnd"]()),2&e){const e=F["\u0275\u0275nextContext"](4);F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",e.paymentDetailsFormGroup.get("milestones").controls.length>0),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngForOf",e.paymentDetailsFormGroup.get("milestones").controls)}}function Qe(e,t){if(1&e){const e=F["\u0275\u0275getCurrentView"]();F["\u0275\u0275elementStart"](0,"button",85),F["\u0275\u0275listener"]("click",(function(){return F["\u0275\u0275restoreView"](e),F["\u0275\u0275reference"](3).click()})),F["\u0275\u0275text"](1,"Upload Invoice "),F["\u0275\u0275elementStart"](2,"input",86,166),F["\u0275\u0275listener"]("change",(function(t){F["\u0275\u0275restoreView"](e);const n=F["\u0275\u0275nextContext"]().index;return F["\u0275\u0275nextContext"](5).onFileAdd(n,t)})),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"]()}if(2&e){const e=F["\u0275\u0275nextContext"](6);F["\u0275\u0275advance"](2),F["\u0275\u0275property"]("uploader",e.uploader)}}function Ke(e,t){1&e&&F["\u0275\u0275element"](0,"mat-spinner",88)}function Xe(e,t){if(1&e){const e=F["\u0275\u0275getCurrentView"]();F["\u0275\u0275elementStart"](0,"div"),F["\u0275\u0275elementStart"](1,"span",138),F["\u0275\u0275text"](2),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](3,"span",139),F["\u0275\u0275elementStart"](4,"button",126),F["\u0275\u0275listener"]("click",(function(){F["\u0275\u0275restoreView"](e);const t=F["\u0275\u0275nextContext"]().index;return F["\u0275\u0275nextContext"](5).removeInvoice(t)})),F["\u0275\u0275elementStart"](5,"mat-icon",91),F["\u0275\u0275text"](6,"close"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"]()}if(2&e){const e=F["\u0275\u0275nextContext"]().$implicit;F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("matTooltip",null==e.get("attachmentData").value.files_json?null:e.get("attachmentData").value.files_json.fileName),F["\u0275\u0275advance"](1),F["\u0275\u0275textInterpolate1"](" ",null==e.get("attachmentData").value.files_json?null:e.get("attachmentData").value.files_json.fileName," ")}}function Ze(e,t){if(1&e&&(F["\u0275\u0275elementStart"](0,"div",120),F["\u0275\u0275elementStart"](1,"div",75),F["\u0275\u0275elementStart"](2,"span",76),F["\u0275\u0275text"](3),F["\u0275\u0275elementEnd"](),F["\u0275\u0275text"](4,"\xa0\xa0 "),F["\u0275\u0275element"](5,"app-currency",77),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;F["\u0275\u0275advance"](3),F["\u0275\u0275textInterpolate"](e.ccDetail.name),F["\u0275\u0275advance"](2),F["\u0275\u0275property"]("showActualAmount",!0)("currencyList",e.convertedAmount)}}function et(e,t){if(1&e&&(F["\u0275\u0275elementStart"](0,"div",118),F["\u0275\u0275template"](1,Ze,6,3,"div",119),F["\u0275\u0275elementEnd"]()),2&e){const e=F["\u0275\u0275nextContext"](2).$implicit;F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngForOf",e.get("ccSplitDetails").value)}}function tt(e,t){if(1&e&&(F["\u0275\u0275elementStart"](0,"div",123),F["\u0275\u0275elementStart"](1,"div",93),F["\u0275\u0275elementStart"](2,"mat-form-field",43),F["\u0275\u0275elementStart"](3,"mat-label"),F["\u0275\u0275text"](4),F["\u0275\u0275elementEnd"](),F["\u0275\u0275element"](5,"input",176),F["\u0275\u0275elementStart"](6,"span",125),F["\u0275\u0275text"](7),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,n=t.index,i=F["\u0275\u0275nextContext"](9);F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("formGroupName",n),F["\u0275\u0275advance"](3),F["\u0275\u0275textInterpolate1"]("Amount for ",e.get("ccDetail").value.name," "),F["\u0275\u0275advance"](3),F["\u0275\u0275textInterpolate"](i.preferredCurrency)}}function nt(e,t){if(1&e){const e=F["\u0275\u0275getCurrentView"]();F["\u0275\u0275elementContainerStart"](0),F["\u0275\u0275template"](1,tt,8,3,"div",121),F["\u0275\u0275elementStart"](2,"div",46),F["\u0275\u0275elementStart"](3,"button",122),F["\u0275\u0275listener"]("click",(function(){F["\u0275\u0275restoreView"](e);const t=F["\u0275\u0275nextContext"](3).index;return F["\u0275\u0275nextContext"](5).applyEnteredMileCCSplitups(t)})),F["\u0275\u0275text"](4,"Apply"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=F["\u0275\u0275nextContext"](3).$implicit;F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngForOf",e.get("ccSplitDetails").controls)}}function it(e,t){if(1&e&&(F["\u0275\u0275elementStart"](0,"div",120),F["\u0275\u0275elementStart"](1,"div",75),F["\u0275\u0275elementStart"](2,"span",76),F["\u0275\u0275text"](3),F["\u0275\u0275elementEnd"](),F["\u0275\u0275text"](4,"\xa0\xa0 "),F["\u0275\u0275element"](5,"app-currency",77),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;F["\u0275\u0275advance"](3),F["\u0275\u0275textInterpolate"](e.ccDetail.name),F["\u0275\u0275advance"](2),F["\u0275\u0275property"]("showActualAmount",!0)("currencyList",e.convertedAmount)}}function lt(e,t){if(1&e){const e=F["\u0275\u0275getCurrentView"]();F["\u0275\u0275elementContainerStart"](0),F["\u0275\u0275template"](1,it,6,3,"div",119),F["\u0275\u0275elementStart"](2,"button",126),F["\u0275\u0275listener"]("click",(function(){F["\u0275\u0275restoreView"](e);const t=F["\u0275\u0275nextContext"](3).index;return F["\u0275\u0275nextContext"](5).changeMileSplitAppliedStatus(t)})),F["\u0275\u0275elementStart"](3,"mat-icon"),F["\u0275\u0275text"](4,"cancel"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=F["\u0275\u0275nextContext"](3).$implicit;F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngForOf",e.get("ccSplitDetails").value)}}function rt(e,t){if(1&e&&(F["\u0275\u0275elementStart"](0,"div",118),F["\u0275\u0275template"](1,nt,5,1,"ng-container",21),F["\u0275\u0275template"](2,lt,5,1,"ng-container",21),F["\u0275\u0275elementEnd"]()),2&e){const e=F["\u0275\u0275nextContext"](2).$implicit;F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",0==e.get("isMileManualSplitApplied").value),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",1==e.get("isMileManualSplitApplied").value)}}function ot(e,t){if(1&e){const e=F["\u0275\u0275getCurrentView"]();F["\u0275\u0275elementContainerStart"](0),F["\u0275\u0275elementStart"](1,"div",0),F["\u0275\u0275elementStart"](2,"div",42),F["\u0275\u0275elementStart"](3,"div",0),F["\u0275\u0275elementStart"](4,"span",30),F["\u0275\u0275text"](5,"Split Amount Between Cost Centers"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](6,"div",28),F["\u0275\u0275elementStart"](7,"div",7),F["\u0275\u0275elementStart"](8,"button",116),F["\u0275\u0275listener"]("click",(function(){F["\u0275\u0275restoreView"](e);const t=F["\u0275\u0275nextContext"]().index;return F["\u0275\u0275nextContext"](5).changeMileSplitType("auto",t)})),F["\u0275\u0275text"](9,"Auto split"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](10,"button",116),F["\u0275\u0275listener"]("click",(function(){F["\u0275\u0275restoreView"](e);const t=F["\u0275\u0275nextContext"]().index;return F["\u0275\u0275nextContext"](5).changeMileSplitType("manual",t)})),F["\u0275\u0275text"](11,"Manual Split"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275template"](12,et,2,1,"div",117),F["\u0275\u0275template"](13,rt,3,2,"div",117),F["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=F["\u0275\u0275nextContext"]().$implicit;F["\u0275\u0275advance"](8),F["\u0275\u0275property"]("ngClass",F["\u0275\u0275pureFunction1"](4,Me,"auto"==e.get("ccSplitType").value)),F["\u0275\u0275advance"](2),F["\u0275\u0275property"]("ngClass",F["\u0275\u0275pureFunction1"](6,Me,"manual"==e.get("ccSplitType").value)),F["\u0275\u0275advance"](2),F["\u0275\u0275property"]("ngIf","auto"==e.get("ccSplitType").value),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf","manual"==e.get("ccSplitType").value)}}function at(e,t){if(1&e){const e=F["\u0275\u0275getCurrentView"]();F["\u0275\u0275elementStart"](0,"div",154),F["\u0275\u0275elementStart"](1,"div",155),F["\u0275\u0275elementStart"](2,"div",28),F["\u0275\u0275elementStart"](3,"div",171),F["\u0275\u0275elementStart"](4,"mat-form-field",43),F["\u0275\u0275elementStart"](5,"mat-label"),F["\u0275\u0275text"](6,"Milestone name"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275element"](7,"input",172),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](8,"div",46),F["\u0275\u0275elementStart"](9,"mat-form-field",43),F["\u0275\u0275elementStart"](10,"mat-label"),F["\u0275\u0275text"](11,"Milestone Amount"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](12,"input",173),F["\u0275\u0275listener"]("change",(function(){F["\u0275\u0275restoreView"](e);const n=t.index;return F["\u0275\u0275nextContext"](5).changeInSplitTypeMileAmt(n)})),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](13,"span",125),F["\u0275\u0275text"](14),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](15,"div",46),F["\u0275\u0275elementStart"](16,"mat-form-field",43),F["\u0275\u0275elementStart"](17,"mat-label"),F["\u0275\u0275text"](18,"Invoice Date"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275element"](19,"input",132),F["\u0275\u0275element"](20,"mat-datepicker-toggle",133),F["\u0275\u0275element"](21,"mat-datepicker",null,159),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](23,"div",171),F["\u0275\u0275elementStart"](24,"mat-form-field",43),F["\u0275\u0275elementStart"](25,"mat-label"),F["\u0275\u0275text"](26,"Invoice number"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275element"](27,"input",136),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](28,"div",160),F["\u0275\u0275template"](29,Qe,4,1,"button",83),F["\u0275\u0275template"](30,Ke,1,0,"mat-spinner",84),F["\u0275\u0275template"](31,Xe,7,2,"div",21),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](32,"div",174),F["\u0275\u0275elementStart"](33,"div",175),F["\u0275\u0275listener"]("click",(function(){return F["\u0275\u0275restoreView"](e),F["\u0275\u0275nextContext"](5).addMilestone()})),F["\u0275\u0275elementStart"](34,"mat-icon"),F["\u0275\u0275text"](35,"add"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](36,"div",167),F["\u0275\u0275listener"]("click",(function(){F["\u0275\u0275restoreView"](e);const n=t.index;return F["\u0275\u0275nextContext"](5).removeMilestone(n)})),F["\u0275\u0275elementStart"](37,"mat-icon"),F["\u0275\u0275text"](38,"delete"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275template"](39,ot,14,8,"ng-container",21),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=t.index,i=F["\u0275\u0275reference"](22),l=F["\u0275\u0275nextContext"](5);F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("formGroupName",n),F["\u0275\u0275advance"](13),F["\u0275\u0275textInterpolate"](l.preferredCurrency),F["\u0275\u0275advance"](5),F["\u0275\u0275property"]("matDatepicker",i),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("for",i),F["\u0275\u0275advance"](9),F["\u0275\u0275property"]("ngIf",!e.get("uploadInProgress").value&&!e.get("attachmentData").value.files_json),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",1==e.get("uploadInProgress").value),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",e.get("attachmentData").value.files_json&&!e.get("uploadInProgress").value),F["\u0275\u0275advance"](8),F["\u0275\u0275property"]("ngIf",l.paymentDetailsFormGroup.get("paymentModel").valid&&3==l.paymentDetailsFormGroup.get("paymentModel").value)}}function st(e,t){if(1&e&&(F["\u0275\u0275elementContainerStart"](0),F["\u0275\u0275elementStart"](1,"div",0),F["\u0275\u0275elementStart"](2,"div",42),F["\u0275\u0275elementStart"](3,"div",0),F["\u0275\u0275elementStart"](4,"span",30),F["\u0275\u0275text"](5,"Add Milestones"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275template"](6,at,40,8,"div",150),F["\u0275\u0275elementContainerEnd"]()),2&e){const e=F["\u0275\u0275nextContext"](4);F["\u0275\u0275advance"](6),F["\u0275\u0275property"]("ngForOf",e.paymentDetailsFormGroup.get("milestones").controls)}}function ct(e,t){1&e&&(F["\u0275\u0275elementStart"](0,"mat-icon"),F["\u0275\u0275text"](1,"navigate_next"),F["\u0275\u0275elementEnd"]())}function mt(e,t){if(1&e){const e=F["\u0275\u0275getCurrentView"]();F["\u0275\u0275elementStart"](0,"div",0),F["\u0275\u0275element"](1,"div",45),F["\u0275\u0275elementStart"](2,"div",46),F["\u0275\u0275elementStart"](3,"button",47),F["\u0275\u0275listener"]("click",(function(){return F["\u0275\u0275restoreView"](e),F["\u0275\u0275nextContext"](4).changeActiveStep("checklist")})),F["\u0275\u0275template"](4,ct,2,0,"mat-icon",21),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"]()}if(2&e){const e=F["\u0275\u0275nextContext"](4);F["\u0275\u0275advance"](4),F["\u0275\u0275property"]("ngIf",!e.isLoading)}}function dt(e,t){if(1&e&&(F["\u0275\u0275elementStart"](0,"mat-icon",181),F["\u0275\u0275text"](1,"done_all"),F["\u0275\u0275elementEnd"]()),2&e){const e=F["\u0275\u0275nextContext"](5);F["\u0275\u0275property"]("matTooltip",e.data&&"recallSubmit"==e.data.isFrom?"ReSubmit PR":"Create PR")}}function pt(e,t){1&e&&F["\u0275\u0275element"](0,"mat-spinner",182)}function ut(e,t){if(1&e){const e=F["\u0275\u0275getCurrentView"]();F["\u0275\u0275elementStart"](0,"div",177),F["\u0275\u0275element"](1,"div",45),F["\u0275\u0275elementStart"](2,"div",46),F["\u0275\u0275elementStart"](3,"button",178),F["\u0275\u0275listener"]("click",(function(){F["\u0275\u0275restoreView"](e);const t=F["\u0275\u0275nextContext"](4);return t.data&&"recallSubmit"==t.data.isFrom?t.reSubmitPR():t.createPR()})),F["\u0275\u0275template"](4,dt,2,1,"mat-icon",179),F["\u0275\u0275template"](5,pt,1,0,"mat-spinner",180),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"]()}if(2&e){const e=F["\u0275\u0275nextContext"](4);F["\u0275\u0275advance"](3),F["\u0275\u0275property"]("ngClass",e.isLoading?"create-pr-btn-loading":"create-pr-btn"),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",!e.isLoading),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",e.isLoading)}}function ht(e,t){if(1&e&&(F["\u0275\u0275elementStart"](0,"div"),F["\u0275\u0275template"](1,Re,5,0,"div",127),F["\u0275\u0275template"](2,Oe,18,5,"div",128),F["\u0275\u0275template"](3,$e,36,8,"ng-container",21),F["\u0275\u0275template"](4,We,3,2,"ng-container",21),F["\u0275\u0275template"](5,st,7,1,"ng-container",21),F["\u0275\u0275elementStart"](6,"div",129),F["\u0275\u0275elementStart"](7,"div",42),F["\u0275\u0275elementStart"](8,"div",0),F["\u0275\u0275elementStart"](9,"span",30),F["\u0275\u0275text"](10,"Terms and conditions"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](11,"div",0),F["\u0275\u0275elementStart"](12,"div",42),F["\u0275\u0275elementStart"](13,"mat-form-field",43),F["\u0275\u0275elementStart"](14,"mat-label"),F["\u0275\u0275text"](15,"Terms and conditions"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275element"](16,"textarea",130),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275template"](17,mt,5,1,"div",25),F["\u0275\u0275template"](18,ut,6,3,"div",131),F["\u0275\u0275elementEnd"]()),2&e){const e=F["\u0275\u0275nextContext"](3);F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",1==e.currentlyActivePaymentModel),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",1==e.currentlyActivePaymentModel),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",2==e.currentlyActivePaymentModel),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",2==e.currentlyActivePaymentModel),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",3==e.currentlyActivePaymentModel),F["\u0275\u0275advance"](12),F["\u0275\u0275property"]("ngIf",e.checklistStepVisible),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",!e.checklistStepVisible)}}function gt(e,t){if(1&e){const e=F["\u0275\u0275getCurrentView"]();F["\u0275\u0275elementContainerStart"](0),F["\u0275\u0275elementStart"](1,"div",28),F["\u0275\u0275elementStart"](2,"div",7),F["\u0275\u0275elementStart"](3,"div",0),F["\u0275\u0275elementStart"](4,"span",76),F["\u0275\u0275text"](5,"Please add all the payment related information here!"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](6,"div",0),F["\u0275\u0275elementStart"](7,"div",42),F["\u0275\u0275elementStart"](8,"div",0),F["\u0275\u0275elementStart"](9,"span",30),F["\u0275\u0275text"](10,"Payment model"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](11,"div",41),F["\u0275\u0275elementStart"](12,"div",42),F["\u0275\u0275elementStart"](13,"app-input-search",113),F["\u0275\u0275listener"]("change",(function(){return F["\u0275\u0275restoreView"](e),F["\u0275\u0275nextContext"](2).checkForDelete()})),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](14,"div",0),F["\u0275\u0275elementStart"](15,"div",42),F["\u0275\u0275elementStart"](16,"div",0),F["\u0275\u0275elementStart"](17,"span",30),F["\u0275\u0275text"](18,"Payment terms"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](19,"div",41),F["\u0275\u0275elementStart"](20,"div",42),F["\u0275\u0275element"](21,"app-input-search",114),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](22,"div",115),F["\u0275\u0275template"](23,be,5,2,"div",66),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275template"](24,Ve,14,8,"ng-container",21),F["\u0275\u0275template"](25,ht,19,7,"div",21),F["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=F["\u0275\u0275nextContext"](2);F["\u0275\u0275advance"](13),F["\u0275\u0275property"]("list",e.paymentModels),F["\u0275\u0275advance"](8),F["\u0275\u0275property"]("list",e.paymentTerms),F["\u0275\u0275advance"](2),F["\u0275\u0275property"]("ngIf",e.generalDetailsFormgroup.get("amount").valid),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",e.paymentDetailsFormGroup.get("paymentModel").valid&&(1==e.paymentDetailsFormGroup.get("paymentModel").value||2==e.paymentDetailsFormGroup.get("paymentModel").value)),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",e.currentlyActivePaymentModel)}}function ft(e,t){1&e&&(F["\u0275\u0275elementStart"](0,"div",188),F["\u0275\u0275elementStart"](1,"div",0),F["\u0275\u0275elementStart"](2,"span",192),F["\u0275\u0275text"](3,"Attachment"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"]())}function vt(e,t){1&e&&(F["\u0275\u0275elementStart"](0,"div",171),F["\u0275\u0275elementStart"](1,"div",0),F["\u0275\u0275elementStart"](2,"span",30),F["\u0275\u0275text"](3,"Comments"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"]())}function yt(e,t){if(1&e){const e=F["\u0275\u0275getCurrentView"]();F["\u0275\u0275elementStart"](0,"div",206),F["\u0275\u0275listener"]("click",(function(){return F["\u0275\u0275restoreView"](e),F["\u0275\u0275reference"](4).click()})),F["\u0275\u0275elementStart"](1,"mat-icon",207),F["\u0275\u0275text"](2," cloud_upload "),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](3,"input",86,87),F["\u0275\u0275listener"]("change",(function(){F["\u0275\u0275restoreView"](e);const t=F["\u0275\u0275nextContext"](2).index;return F["\u0275\u0275nextContext"](4).uploadChecklistAttachment(t)})),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"]()}if(2&e){const e=F["\u0275\u0275nextContext"](6);F["\u0275\u0275advance"](3),F["\u0275\u0275property"]("uploader",e.uploader)}}function Ct(e,t){if(1&e&&(F["\u0275\u0275elementStart"](0,"span",208),F["\u0275\u0275text"](1),F["\u0275\u0275elementEnd"]()),2&e){const e=F["\u0275\u0275nextContext"](2).$implicit;F["\u0275\u0275property"]("matTooltip",null==e.get("attachmentData").value.files_json?null:e.get("attachmentData").value.files_json.fileName),F["\u0275\u0275advance"](1),F["\u0275\u0275textInterpolate1"](" ",null==e.get("attachmentData").value.files_json?null:e.get("attachmentData").value.files_json.fileName," ")}}function St(e,t){if(1&e&&(F["\u0275\u0275elementStart"](0,"div",198),F["\u0275\u0275template"](1,yt,5,1,"div",204),F["\u0275\u0275template"](2,Ct,2,2,"span",205),F["\u0275\u0275elementEnd"]()),2&e){const e=F["\u0275\u0275nextContext"]().$implicit;F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",!e.get("isAttached").value),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",e.get("isAttached").value)}}function xt(e,t){if(1&e){const e=F["\u0275\u0275getCurrentView"]();F["\u0275\u0275elementStart"](0,"div",42),F["\u0275\u0275elementStart"](1,"mat-form-field",43),F["\u0275\u0275elementStart"](2,"textarea",209),F["\u0275\u0275listener"]("change",(function(t){F["\u0275\u0275restoreView"](e);const n=F["\u0275\u0275nextContext"]().index;return F["\u0275\u0275nextContext"](4).checkListComment(t,n)})),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"]()}}const Dt=function(e){return{"border-left-color":e}};function Et(e,t){if(1&e){const e=F["\u0275\u0275getCurrentView"]();F["\u0275\u0275elementStart"](0,"div",193),F["\u0275\u0275elementStart"](1,"div",194),F["\u0275\u0275elementStart"](2,"div",195),F["\u0275\u0275elementStart"](3,"div",196),F["\u0275\u0275text"](4),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](5,"div",197),F["\u0275\u0275text"](6),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](7,"div",198),F["\u0275\u0275elementStart"](8,"div",199),F["\u0275\u0275elementStart"](9,"button",200),F["\u0275\u0275listener"]("click",(function(){F["\u0275\u0275restoreView"](e);const n=t.index;return F["\u0275\u0275nextContext"](4).toggleChecklistStatus(n)})),F["\u0275\u0275elementStart"](10,"mat-icon",201),F["\u0275\u0275text"](11,"check_circle"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275template"](12,St,3,2,"div",202),F["\u0275\u0275template"](13,xt,3,0,"div",203),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,n=t.index,i=F["\u0275\u0275nextContext"](4);F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("formGroupName",n)("ngStyle",F["\u0275\u0275pureFunction1"](7,Dt,e.get("isCompleted").value?"#008000":"#808080")),F["\u0275\u0275advance"](3),F["\u0275\u0275textInterpolate1"](" ",e.get("checklistName").value," "),F["\u0275\u0275advance"](2),F["\u0275\u0275textInterpolate1"](" ",e.get("checklistDescription").value," "),F["\u0275\u0275advance"](3),F["\u0275\u0275property"]("ngStyle",F["\u0275\u0275pureFunction1"](9,X,e.get("isCompleted").value?"#008000":"#808080")),F["\u0275\u0275advance"](3),F["\u0275\u0275property"]("ngIf",i.isChecklistAttachment),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",i.isChecklistAttachment)}}function bt(e,t){if(1&e&&(F["\u0275\u0275elementStart"](0,"div"),F["\u0275\u0275elementStart"](1,"div",28),F["\u0275\u0275elementStart"](2,"div",7),F["\u0275\u0275elementStart"](3,"div",0),F["\u0275\u0275elementStart"](4,"span",76),F["\u0275\u0275text"](5,"Please select the checklist status."),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](6,"div",0),F["\u0275\u0275elementStart"](7,"span",184),F["\u0275\u0275text"](8," Note: This is autogenerated checklist based on the costcenter and subgroup selected in general step. "),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](9,"div",185),F["\u0275\u0275elementStart"](10,"div",186),F["\u0275\u0275elementStart"](11,"div",0),F["\u0275\u0275elementStart"](12,"div",46),F["\u0275\u0275elementStart"](13,"div",0),F["\u0275\u0275elementStart"](14,"span",30),F["\u0275\u0275text"](15,"Checklist Name"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](16,"div",187),F["\u0275\u0275elementStart"](17,"div",0),F["\u0275\u0275elementStart"](18,"span",30),F["\u0275\u0275text"](19,"Description"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](20,"div",188),F["\u0275\u0275elementStart"](21,"div",0),F["\u0275\u0275elementStart"](22,"span",30),F["\u0275\u0275text"](23,"Status"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275template"](24,ft,4,0,"div",189),F["\u0275\u0275template"](25,vt,4,0,"div",190),F["\u0275\u0275elementEnd"](),F["\u0275\u0275template"](26,Et,14,11,"div",191),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"]()),2&e){const e=F["\u0275\u0275nextContext"](3);F["\u0275\u0275advance"](24),F["\u0275\u0275property"]("ngIf",e.isChecklistAttachment),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",e.isChecklistAttachment),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngForOf",e.checklistDetailsFormGroup.get("checklistArr").controls)}}function Ft(e,t){1&e&&(F["\u0275\u0275elementStart"](0,"div",210),F["\u0275\u0275elementStart"](1,"div",211),F["\u0275\u0275elementStart"](2,"span",212),F["\u0275\u0275text"](3,"No checklist found !"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](4,"div",213),F["\u0275\u0275element"](5,"img",214),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"]())}function At(e,t){if(1&e&&(F["\u0275\u0275elementStart"](0,"mat-icon",181),F["\u0275\u0275text"](1,"done_all"),F["\u0275\u0275elementEnd"]()),2&e){const e=F["\u0275\u0275nextContext"](3);F["\u0275\u0275property"]("matTooltip",e.data&&"recallSubmit"==e.data.isFrom?"ReSubmit PR":"Create PR")}}function It(e,t){1&e&&F["\u0275\u0275element"](0,"mat-spinner",182)}function Pt(e,t){if(1&e){const e=F["\u0275\u0275getCurrentView"]();F["\u0275\u0275elementContainerStart"](0),F["\u0275\u0275template"](1,bt,27,3,"div",21),F["\u0275\u0275template"](2,Ft,6,0,"div",183),F["\u0275\u0275elementStart"](3,"div",177),F["\u0275\u0275element"](4,"div",45),F["\u0275\u0275elementStart"](5,"div",46),F["\u0275\u0275elementStart"](6,"button",178),F["\u0275\u0275listener"]("click",(function(){F["\u0275\u0275restoreView"](e);const t=F["\u0275\u0275nextContext"](2);return t.data&&"recallSubmit"==t.data.isFrom?t.reSubmitPR():t.createPR()})),F["\u0275\u0275template"](7,At,2,1,"mat-icon",179),F["\u0275\u0275template"](8,It,1,0,"mat-spinner",180),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementContainerEnd"]()}if(2&e){const e=F["\u0275\u0275nextContext"](2);F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",e.checklistDetailsFormGroup.get("checklistArr").controls.length>0),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",0==e.checklistDetailsFormGroup.get("checklistArr").controls.length),F["\u0275\u0275advance"](4),F["\u0275\u0275property"]("ngClass",e.isLoading?"create-pr-btn-loading":"create-pr-btn"),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",!e.isLoading),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",e.isLoading)}}function wt(e,t){if(1&e){const e=F["\u0275\u0275getCurrentView"]();F["\u0275\u0275elementStart"](0,"div",5),F["\u0275\u0275elementStart"](1,"div",6),F["\u0275\u0275elementStart"](2,"div",7),F["\u0275\u0275elementStart"](3,"div",8),F["\u0275\u0275elementStart"](4,"div",9),F["\u0275\u0275elementStart"](5,"mat-icon",10),F["\u0275\u0275text"](6," shopping_cart "),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275template"](7,J,7,1,"div",11),F["\u0275\u0275template"](8,Q,2,1,"div",11),F["\u0275\u0275elementStart"](9,"div",12),F["\u0275\u0275elementStart"](10,"button",13),F["\u0275\u0275listener"]("click",(function(){return F["\u0275\u0275restoreView"](e),F["\u0275\u0275nextContext"]().dialogRef.close("close")})),F["\u0275\u0275elementStart"](11,"mat-icon"),F["\u0275\u0275text"](12,"close"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](13,"div",14),F["\u0275\u0275elementStart"](14,"div",15),F["\u0275\u0275elementStart"](15,"div",16),F["\u0275\u0275listener"]("click",(function(){return F["\u0275\u0275restoreView"](e),F["\u0275\u0275nextContext"]().changeActiveStep("general")})),F["\u0275\u0275text"](16," 1 "),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](17,"span",17),F["\u0275\u0275listener"]("click",(function(){return F["\u0275\u0275restoreView"](e),F["\u0275\u0275nextContext"]().changeActiveStep("general")})),F["\u0275\u0275text"](18," General"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](19,"div",18),F["\u0275\u0275elementStart"](20,"div",16),F["\u0275\u0275listener"]("click",(function(){return F["\u0275\u0275restoreView"](e),F["\u0275\u0275nextContext"]().changeActiveStep("paymentDetail")})),F["\u0275\u0275text"](21," 2 "),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](22,"span",17),F["\u0275\u0275listener"]("click",(function(){return F["\u0275\u0275restoreView"](e),F["\u0275\u0275nextContext"]().changeActiveStep("paymentDetail")})),F["\u0275\u0275text"](23,"Payment details"),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"](),F["\u0275\u0275template"](24,Z,5,6,"div",19),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](25,"form",20),F["\u0275\u0275template"](26,Ee,43,6,"ng-container",21),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](27,"form",20),F["\u0275\u0275template"](28,gt,26,5,"ng-container",21),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementStart"](29,"form",20),F["\u0275\u0275template"](30,Pt,9,5,"ng-container",21),F["\u0275\u0275elementEnd"](),F["\u0275\u0275elementEnd"]()}if(2&e){const e=F["\u0275\u0275nextContext"]();F["\u0275\u0275advance"](7),F["\u0275\u0275property"]("ngIf",!(e.data&&"recallSubmit"==e.data.isFrom)),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",e.data&&"recallSubmit"==e.data.isFrom),F["\u0275\u0275advance"](6),F["\u0275\u0275property"]("ngClass",e.checklistStepVisible?"col-4 p-3  d-flex align-items-center justify-content-end":"col-6 p-3  d-flex align-items-center justify-content-end"),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngStyle",F["\u0275\u0275pureFunction1"](15,K,"general"==e.currentlyActiveStep?"#ff5253":"#817e7e")),F["\u0275\u0275advance"](2),F["\u0275\u0275property"]("ngStyle",F["\u0275\u0275pureFunction1"](17,X,"general"==e.currentlyActiveStep?"#ff5253":"#817e7e")),F["\u0275\u0275advance"](2),F["\u0275\u0275property"]("ngClass",e.checklistStepVisible?"col-4 p-3  d-flex align-items-center justify-content-center":"col-6 p-3 d-flex align-items-center justify-content-start"),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngStyle",F["\u0275\u0275pureFunction1"](19,K,"paymentDetail"==e.currentlyActiveStep?"#ff5253":"#817e7e")),F["\u0275\u0275advance"](2),F["\u0275\u0275property"]("ngStyle",F["\u0275\u0275pureFunction1"](21,X,"paymentDetail"==e.currentlyActiveStep?"#ff5253":"#817e7e")),F["\u0275\u0275advance"](2),F["\u0275\u0275property"]("ngIf",e.checklistStepVisible),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("formGroup",e.generalDetailsFormgroup),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf","general"==e.currentlyActiveStep),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("formGroup",e.paymentDetailsFormGroup),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf","paymentDetail"==e.currentlyActiveStep),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("formGroup",e.checklistDetailsFormGroup),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf","checklist"==e.currentlyActiveStep)}}let kt=(()=>{class e{constructor(e,t,n,a,s,c,m,d,p,u){this.http=e,this._o365=t,this._auth=n,this.fb=a,this._p2pGeneral=s,this._wfService=c,this._util=m,this._toaster=d,this.dialogRef=p,this.data=u,this.currentlyActiveStep="general",this.currencies=[],this.subGroups=[],this.costCenters=[],this.services=[],this.vendors=[],this.paymentTerms=[],this.paymentModels=[],this.paymentReccurences=[],this.uomList=[],this.subs=new r.a,this.UPLOAD_ATTACHMENT_URL="/api/purchaseRequest/uploadPRAttachment",this.isLoading=!1,this.isModalLoading=!1,this.checklistStepVisible=!1,this.showSplitType=!1,this.preferredCurrency="INR",this.isManualSplitApplied=!1,this.isChecklistAttachment=!1,this.showMilestoneBudgetAvailability=!1,this.prCurrencyCodeList=[],this.isAvailable=!0,this.isExceptionalPRPaymentCreation=!0,this.isItemCurrencyDefault=!1,this.isEntityBasedCurrencyCode=!1,this.selectedCurCode="",this.selectedVendorCurrencyCode="INR",this.prCurrencyCodeListDefault=[],this.isVendorTypeVisible=!1,this.sharepointInvoiceIntegration=!1,this.invoiceFileDetailsSPIntegration=[],this.vendorTypeDisplay="",this.isPeopleInvolvedAvailable=!1,this.isBillablePRAailable=!1,this.isBillablePRApplicable=!1,this.$onDestroy=new I.b,this.getCurrencyList=()=>new Promise((e,t)=>{this.subs.sink=this._p2pGeneral.getCurrency().subscribe(t=>{if(this.isItemCurrencyDefault){let e=this.generalDetailsFormgroup.get("costCenterArr").controls[0].get("items");this.inrValuePick=o.findWhere(t,{name:"INR"}),console.log("inrValuePick");let n=this.inrValuePick.id;this.inrValuePick&&e.controls[0].get("itemCurrency").setValue(n)}e(t)},e=>{console.error(e),t(e)})}),this.getSubGroup=()=>new Promise((e,t)=>{this.subs.sink=this._p2pGeneral.getSubGroup().subscribe(t=>{e(t)},e=>{console.error(e),t(e)})}),this.getCostCenters=()=>new Promise((e,t)=>{this.subs.sink=this._p2pGeneral.getCostCenters().subscribe(t=>{e(t)},e=>{console.error(e),t(e)})}),this.getUOMList=()=>new Promise((e,t)=>{this.subs.sink=this._p2pGeneral.getUomList().subscribe(t=>{e(t)},e=>{console.error(e),t(e)})}),this.getService=()=>new Promise((e,t)=>{this.subs.sink=this._p2pGeneral.getAvailableService().subscribe(t=>{e(t)},e=>{console.error(e),t(e)})}),this.getVendors=()=>new Promise((e,t)=>{this.subs.sink=this._p2pGeneral.getVendors().subscribe(t=>{e(t)},e=>{console.error(e),t(e)})}),this.checkBudgetAvailabilityForMilestone=(e,t,n,i,l)=>new Promise((r,o)=>{this.subs.sink=this._p2pGeneral.checkBudgetAvailabilityForMilestone(e,t,n,i,l).subscribe(e=>{e.err||r(e.budget_available)},e=>{console.log(e),o(e)})}),this.getChecklistForCreateRequest=e=>new Promise((t,n)=>{this.subs.sink=this._p2pGeneral.getChecklistForCreateRequest(e).subscribe(e=>{t(e.err?[]:e.data)},e=>{console.log(e),n(e)})}),this.removeCC=e=>{this.costCenterArr=this.generalDetailsFormgroup.get("costCenterArr"),this.costCenterArr.removeAt(e)},this.addItem=e=>{if(this.costCenterArr=this.generalDetailsFormgroup.get("costCenterArr"),this.itemArr=this.costCenterArr.controls[e].get("items"),this.itemArr.push(this.createItem()),this.isItemCurrencyDefault)for(let t of this.itemArr.controls)t.get("itemCurrency").value||t.get("itemCurrency").patchValue(this.inrValuePick.id),console.log(t.get("itemCurrency").value)},this.removeItem=(e,t)=>{this.costCenterArr=this.generalDetailsFormgroup.get("costCenterArr"),this.itemArr=this.costCenterArr.controls[e].get("items"),this.itemArr.removeAt(t)},this.changeActiveStep=e=>{this.currentlyActiveStep=e},this.calculateTotalAmount=(e,t)=>new Promise((e,n)=>{let i=this.generalDetailsFormgroup.get("costCenterArr").controls[t].get("items").value,l=o.pluck(i,"convertedAmount"),r=[];l.forEach(e=>{r.push({itemAmount:e})}),this._p2pGeneral.calculateTotInallCurrencies(r,this.prCurrencyCodeList).subscribe(t=>{e(t.total)},e=>{console.error(e)})}),this.getExpenseApproversHierarchy=(e,t)=>Object(i.c)(this,void 0,void 0,(function*(){let n=t.code,i={workflowId:e.id,sortByDesignation:e.sort_by_desgn,initiatorOId:this.loggedInProfile.oid,approvalType:e.approval_type,approvalParams:e.approval_params,costCentresAndTypes:[{costCentre:n,costCentreType:t.cost_centre_type,costCentreDescription:""}]};return new Promise((e,t)=>{this._p2pGeneral.getApproversHierarchy(i).subscribe(i=>{this._wfService.formatApproversHierarchy([n],i.data).then(t=>{e(t)},e=>{t(e),console.error(e)})},e=>{t(e),console.error(e)})})})),this.changeItemAmount=e=>{let t=this.generalDetailsFormgroup.get("costCenterArr"),n=t.controls[e].get("items"),i=t.controls[e].get("ccDetail").value.currency_code;this.addPrCurrencyCode(i),n.value.forEach((t,i)=>{if(console.log(t),n.controls[i].get("itemQuantity").valid&&n.controls[i].get("itemUnitPrice").valid){let l=n.controls[i].get("itemQuantity").value*n.controls[i].get("itemUnitPrice").value;console.log(l),n.controls[i].get("itemAmount").patchValue(l);let r=n.controls[i].get("itemAmount").value;if(n.controls[i].get("itemCurrency").valid){let l=o.where(this.currencies,{id:t.itemCurrency})[0].name;n.controls[i].get("itemCurrencyName").patchValue(l),this._p2pGeneral.getMultipleCurrencyConversion(r,l,this.prCurrencyCodeList).subscribe(t=>{t&&"S"==t.messType?(n.controls[i].get("convertedAmount").patchValue(t.result),this.changeCc(e)):this._p2pGeneral.showMessage("Currency convertion failed !")},e=>{console.log(e),this._p2pGeneral.showMessage("Currency convertion failed !")})}}})},this.getPaymentTerms=()=>{this.subs.sink=this._p2pGeneral.getPaymentTerms().subscribe(e=>{this.paymentTerms=e},e=>{console.error(e)})},this.getPaymentModels=()=>{this.subs.sink=this._p2pGeneral.getPaymentModel().subscribe(e=>{this.paymentModels=e},e=>{console.error(e)})},this.detectPaymentModelChanges=()=>{this.subs.sink=this.paymentDetailsFormGroup.get("paymentModel").valueChanges.subscribe(e=>{switch(this.currentlyActivePaymentModel=e,this.calculatePRTotal(),e){case 1:this.paymentDetailsFormGroup.removeControl("paymentReccurance"),this.paymentDetailsFormGroup.removeControl("paymentStartDate"),this.paymentDetailsFormGroup.removeControl("paymentEndDate"),this.paymentDetailsFormGroup.removeControl("milestones"),this.paymentDetailsFormGroup.addControl("invoiceDate",new l.j("",[l.H.required])),this.paymentDetailsFormGroup.addControl("invoiceNumber",new l.j("")),this.paymentDetailsFormGroup.addControl("attachmentData",new l.j("",[l.H.required])),this.paymentDetailsFormGroup.addControl("uploadInProgress",new l.j(!1,[l.H.required]));break;case 2:this.paymentDetailsFormGroup.removeControl("milestones"),this.paymentDetailsFormGroup.addControl("paymentReccurance",new l.j("",[l.H.required])),this.paymentDetailsFormGroup.addControl("paymentStartDate",new l.j("",[l.H.required])),this.paymentDetailsFormGroup.addControl("paymentEndDate",new l.j("",[l.H.required])),this.paymentDetailsFormGroup.addControl("invoiceDate",new l.j("",[l.H.required])),this.paymentDetailsFormGroup.removeControl("attachmentData"),this.paymentDetailsFormGroup.removeControl("uploadInProgress"),this.paymentDetailsFormGroup.addControl("milestones",this.fb.array([])),this.detectChangeInRecurrenceDetails();break;case 3:this.paymentDetailsFormGroup.removeControl("milestones"),this.paymentDetailsFormGroup.removeControl("paymentReccurance"),this.paymentDetailsFormGroup.removeControl("paymentStartDate"),this.paymentDetailsFormGroup.removeControl("paymentEndDate"),this.paymentDetailsFormGroup.removeControl("invoiceDate"),this.paymentDetailsFormGroup.removeControl("attachmentData"),this.paymentDetailsFormGroup.removeControl("uploadInProgress"),this.paymentDetailsFormGroup.addControl("milestones",this.fb.array([this.createMilestoneFormArray()]))}"auto"==this.paymentDetailsFormGroup.get("ccSplitType").value&&(1!=this.paymentDetailsFormGroup.get("paymentModel").value&&2!=this.paymentDetailsFormGroup.get("paymentModel").value||this.calculateccSplitDetails(this.generalDetailsFormgroup.get("amount").value))})},this.detectVendorChanges=()=>{this.generalDetailsFormgroup.get("vendor").valueChanges.subscribe(e=>{let t=o.findWhere(this.vendors,{id:e});t&&(this.isVendorTypeVisible&&(console.log("Selected Vendor Details : ",t),this.vendorTypeDisplay=t.is_msme,this.vendorTypeDisplay=1==this.vendorTypeDisplay?"MSME Vendor":2==this.vendorTypeDisplay?"Non-MSME Vendor":"-"),!o.contains(this.prCurrencyCodeList,t.preferred_currency))&&(this.prCurrencyCodeList.push(t.preferred_currency),this.selectedVendorCurrencyCode=t.preferred_currency,this.generalDetailsFormgroup.get("costCenterArr").controls.forEach((e,t)=>{this.changeItemAmount(t)})),this.selectedVendorCurrencyCode=t.preferred_currency})},this.startAutogen=()=>{if(2==this.paymentDetailsFormGroup.get("paymentModel").value&&this.paymentDetailsFormGroup.get("paymentReccurance").valid&&this.paymentDetailsFormGroup.get("paymentStartDate").valid&&this.paymentDetailsFormGroup.get("paymentEndDate").valid&&this.paymentDetailsFormGroup.get("invoiceDate").valid){let e=new Date(this.paymentDetailsFormGroup.get("paymentStartDate").value),t=new Date(this.paymentDetailsFormGroup.get("paymentEndDate").value);e.getTime()<t.getTime()&&this.autoGenerateRecurringMilestones()}},this.calculatePrTotalBasedOnRecurringMilestone=()=>{let e=this.paymentDetailsFormGroup.get("milestones"),t=this.generalDetailsFormgroup.get("amount").value.map(t=>({currency_code:t.currency_code,value:t.value*e.length}));this.generalDetailsFormgroup.get("amount").patchValue(t),this.calculateOverAllCcSplitup(e.length)},this.calculateOverAllCcSplitup=e=>{this.paymentDetailsFormGroup.get("ccSplitDetails").controls.forEach((t,n)=>{let i=t.get("convertedAmount").value;i=i.map(t=>({currency_code:t.currency_code,value:t.value*e})),t.get("convertedAmount").patchValue(i)})},this.checkMilestoneBudget=()=>Object(i.c)(this,void 0,void 0,(function*(){let e=this.paymentDetailsFormGroup.get("milestones"),t=this.checkIfCcHasExceptionalBudgetFlow();console.log(e.controls);let n=!0;for(let i of e.controls)if(t)i.get("hasBudgetAvailable").patchValue(!0);else{let e=i.get("invoiceDate").value,t=this.generalDetailsFormgroup.get("costCenterArr").value,l=i.get("ccSplitDetails").value,r=this.paymentDetailsFormGroup.get("ccSplitDetails").value;n?(console.log("budget"),console.log(t),i.get("hasBudgetAvailable").patchValue(yield this.checkBudgetAvailabilityForMilestone(e,t,l,r,n)),n=!1):i.get("hasBudgetAvailable").patchValue(yield this.checkBudgetAvailabilityForMilestone(e,t,l,r,n))}})),this.getPaymentRecurrence=()=>this._p2pGeneral.getPaymentRecurrence().subscribe(e=>{this.paymentReccurences=e}),this.addMilestone=()=>{this.milestoneArr=this.paymentDetailsFormGroup.get("milestones");let e=0,t=o.where(this.generalDetailsFormgroup.get("amount").value,{currency_code:this.preferredCurrency})[0];this.milestoneArr.value.forEach((t,n)=>{e+=parseInt(t.milestoneAmount)}),e<t.value?this.milestoneArr.push(this.createMilestoneFormArray()):this._toaster.showError("Exceeding Limit","Total Milestone amount exceeding the PR total",2e3)},this.removeMilestone=e=>{this.milestoneArr=this.paymentDetailsFormGroup.get("milestones"),this.updateCcSplitUpAndPrTotal(this.milestoneArr.length,1),this.milestoneArr.removeAt(e)},this.updateCcSplitUpAndPrTotal=(e,t)=>{if(2==this.currentlyActivePaymentModel&&e>1){this.paymentDetailsFormGroup.get("ccSplitDetails").controls.forEach((n,i)=>{let l=n.get("convertedAmount").value;l=l.map(n=>({currency_code:n.currency_code,value:n.value/e*(e-t)})),n.get("convertedAmount").patchValue(l)});let n=this.generalDetailsFormgroup.get("amount").value.map(n=>({currency_code:n.currency_code,value:n.value/e*(e-t)}));this.generalDetailsFormgroup.get("amount").patchValue(n)}},this.getWorkflowProperties=()=>{this._p2pGeneral.getPurchaseRequestWorkflowProperties().subscribe(e=>{this.workflowProperties=e.data,console.log(this.workflowProperties)},e=>{console.error(e)})},this.checkIfBudgetAvailable=(e,t,n,i,l)=>new Promise((r,o)=>{this.subs.sink=this._p2pGeneral.checkBudgetAvailable(e,t,n,i,l).subscribe(e=>{r(e)},e=>{console.error(e),o(e)})}),this.hasExceptionalPrAllowed=()=>new Promise((e,t)=>{this.subs.sink=this._p2pGeneral.hasExceptionalPrAllowed("ABE").subscribe(t=>{e(t)},e=>{t(e),console.log(e)})}),this.getCompanyCurrency=()=>new Promise((e,t)=>{this.subs.sink=this._p2pGeneral.getCompanyCurrency().subscribe(t=>{e(t.err?[]:t.data)},e=>{console.log(e),t(e)})}),this.getPurchaseRequestTenantConfig=e=>Object(i.c)(this,void 0,void 0,(function*(){return new Promise((t,n)=>{this.subs.sink=this._p2pGeneral.getP2pTenantConfig(e).subscribe(e=>{t("S"==e.messType)},e=>{n(e)})})})),this.addPrCurrencyCode=e=>{var t,n,i;let l=this.generalDetailsFormgroup.get("costCenterArr").value;if(this.isEntityBasedCurrencyCode){let e=o.findWhere(this.currencies,{id:null===(n=null===(t=l[0])||void 0===t?void 0:t.items[0])||void 0===n?void 0:n.itemCurrency});this.selectedCurCode=e.name,this.prCurrencyCodeList.length>2&&this.prCurrencyCodeList.shift(),this.prCurrencyCodeList.unshift(e.name),this.prCurrencyCodeList=[...this.prCurrencyCodeList,...this.prCurrencyCodeListDefault],this.prCurrencyCodeList=o.uniq(this.prCurrencyCodeList),console.log(this.prCurrencyCodeList)}(null===(i=this.selectedVendorCurrencyCode)||void 0===i?void 0:i.length)>0&&this.prCurrencyCodeList.push(this.selectedVendorCurrencyCode),this.prCurrencyCodeList.push(e),this.prCurrencyCodeList=o.uniq(this.prCurrencyCodeList),console.log(this.prCurrencyCodeList)},this.addChecklist=e=>{let t=this.checklistDetailsFormGroup.get("checklistArr");for(;0!==t.controls.length;)t.removeAt(0);e.forEach((e,n)=>{let i=this.fb.group({checklistName:e.checklist_name,checklistDescription:e.checklist_description,isCompleted:!1,isAttached:!1,isCommented:!1,attachmentData:"",commentText:""});t.push(i)})},this.toggleChecklistStatus=e=>{let t=this.checklistDetailsFormGroup.get("checklistArr").controls[e];t.get("isCompleted").patchValue(!t.get("isCompleted").value)},this.handleRecurringCcAmount=()=>{if(2==this.currentlyActivePaymentModel){let e=this.generalDetailsFormgroup.get("costCenterArr"),t=this.paymentDetailsFormGroup.get("milestones");t=t.value.length,e.controls.forEach((n,i)=>{let l=e.controls[i].get("ccItemsTotalAmount").value;console.log(l),l.forEach((e,n)=>{l[n].value=l[n].value*t}),e.controls[i].get("ccItemsTotalAmount").patchValue(l)}),e.controls.forEach((n,i)=>{let l=e.controls[i].get("items");l.controls.forEach((e,n)=>{let i=l.controls[n].get("itemAmount").value;i*=t,l.controls[n].patchValue(i);let r=l.controls[n].get("convertedAmount").value;r.forEach((e,n)=>{r[n].value=r[n].value*t}),l.controls[n].get("convertedAmount").patchValue(r)})})}},this.getApproversForRecurringPayment=()=>{let e=this.generalDetailsFormgroup.get("costCenterArr");for(let t of e.controls){console.log(this.isExceptionalPRPaymentCreation),t.get("isExceptionalPR").value&&this.isExceptionalPRPaymentCreation||t.get("approvers").patchValue([]);let e=o.findWhere(this.subGroups,{id:t.get("subGroup").value}),n=t.get("ccDetail").value.currency_code,l=this.generalDetailsFormgroup.get("amount").value;l=o.findWhere(l,{currency_code:n}),l=l.value;let r=[{category:e.sub_group_id,amount:l,currency_code:n}],a={entity_id:t.get("ccDetail").value.legal_entity_id,entity_currency_code:n};t.get("isExceptionalPR").value&&this.isExceptionalPRPaymentCreation||this._p2pGeneral.determineWfPropertyForEachPurchaseRequest(r,a,this.workflowProperties).subscribe(e=>Object(i.c)(this,void 0,void 0,(function*(){let n=e.data.wfProperty;t.get("workflowProperty").patchValue(n);let i=yield this.getExpenseApproversHierarchy(n,t.get("ccDetail").value);i.forEach(e=>{e.approvalStatus="Submitted"}),t.get("approvers").patchValue(i)}))),console.log(t.get("workflowProperty").value),console.log(this.generalDetailsFormgroup.get("costCenterArr"))}},this.onStartDateSelection=()=>{console.log(this.paymentDetailsFormGroup.get("paymentStartDate").value),this.MinDateRecurring=this.paymentDetailsFormGroup.get("paymentStartDate").value},this.onClearButtonClick=()=>{this.paymentDetailsFormGroup.get("paymentModel").setValue(null),this.paymentDetailsFormGroup.get("invoiceDate").setValue(null),this.MinDateRecurring=this.paymentDetailsFormGroup.get("paymentStartDate").setValue(null),this.MinDateRecurring=this.paymentDetailsFormGroup.get("paymentEndDate").setValue(null),this.MinDateRecurring=this.paymentDetailsFormGroup.get("paymentEndDate").setValue(null),this.MinDateRecurring=this.paymentDetailsFormGroup.get("paymentReccurance").setValue(null)},this.uploadChecklistAttachment=e=>{this.isCheckListUploadInProgress=!0,this.currentCheckListIndex=e,this.uploader.uploadAll()},this.checkListComment=(e,t)=>{let n=this.checklistDetailsFormGroup.get("checklistArr").controls[t];n.get("commentText").patchValue(e.target.value),n.get("commentText").value.length>1?n.get("isCommented").patchValue(!0):n.get("isCommented").patchValue(!1),console.log(this.checklistDetailsFormGroup)},u&&(this.prCreationType=u.isFrom,this.PRDraftData=u.draftData.purchaseDetails,console.log("Payload from recall Submit ",this.prCreationType,this.PRDraftData)),this.loggedInProfile=this._auth.getProfile().profile,this.generalDetailsFormgroup=this.fb.group({amount:["",l.H.required],costCenterArr:this.fb.array([this.createCC()]),vendor:["",l.H.required],attachment:[""],description:[""]}),this.paymentDetailsFormGroup=this.fb.group({paymentModel:["",l.H.required],paymentTerm:["",l.H.required],tAndC:[""],ccSplitType:["auto",l.H.required],ccSplitDetails:this.fb.array([],l.H.required)}),this.checklistDetailsFormGroup=this.fb.group({checklistArr:this.fb.array([])})}changeCCSplitType(e){this.paymentDetailsFormGroup.get("ccSplitType").patchValue(e),"auto"==e&&this.calculateccSplitDetails(this.generalDetailsFormgroup.get("amount").value)}applyEnteredCCSplitups(){let e=this.paymentDetailsFormGroup.get("ccSplitDetails"),t=0;e.value.forEach((n,i)=>{e.controls[i].get("amount").valid&&(t+=n.amount,this._p2pGeneral.convertCurrency(n.amount,this.preferredCurrency).subscribe(n=>{e.controls[i].get("convertedAmount").patchValue(JSON.parse(n.result[0][0].result));let l=o.where(this.generalDetailsFormgroup.get("amount").value,{currency_code:this.preferredCurrency});this.isManualSplitApplied=!0,t!=l[0].value&&(e.controls[i].get("convertedAmount").reset(),this.isManualSplitApplied=!1,this._toaster.showError("Amount not matched !","Total of split ups are not equivalent to total  amount.",2e3))}))})}detectCCArrChanges(){return Object(i.c)(this,void 0,void 0,(function*(){this.generalDetailsFormgroup.get("costCenterArr").valueChanges.pipe(Object(E.a)(700)).subscribe(e=>Object(i.c)(this,void 0,void 0,(function*(){var e,t;console.log("ccArrValueCVhanges");let n=this.generalDetailsFormgroup.get("costCenterArr").value;if(console.log(n),console.log(null===(t=null===(e=n[0])||void 0===e?void 0:e.items[0])||void 0===t?void 0:t.itemCurrency),n.length>0){this.createCostcentreSplitUpsArr();let e=yield this.getChecklistForCreateRequest(n);this.addChecklist(e)}})))}))}calculateccSplitDetails(e){if(1==this.paymentDetailsFormGroup.get("paymentModel").value||2==this.paymentDetailsFormGroup.get("paymentModel").value){let t=this.generalDetailsFormgroup.get("costCenterArr"),n=o.where(this.generalDetailsFormgroup.get("amount").value,{currency_code:this.preferredCurrency}),i=this.paymentDetailsFormGroup.get("ccSplitDetails");t.value.forEach((t,l)=>{let r=o.where(t.ccItemsTotalAmount,{currency_code:this.preferredCurrency});if(r.length>0){let t=r[0].value/n[0].value*100,o=[];e.forEach((e,n)=>{o.push({currency_code:e.currency_code,value:Math.round(t/100*e.value)})}),i.controls[l].get("convertedAmount").patchValue(o)}})}}calculatePRTotal(){let e=this.generalDetailsFormgroup.get("costCenterArr"),t=[],n=[];e.value.forEach((e,t)=>{n=n.concat(e.ccItemsTotalAmount)});let i=o.groupBy(n,e=>e.currency_code),l=Object.keys(i);o.each(l,e=>{let n=i[e].reduce((e,t)=>e+t.value,0);t.push({currency_code:e,value:n})}),this.generalDetailsFormgroup.get("amount").patchValue(t)}createCC(){return this.fb.group({costCenter:["",l.H.required],costCenterDropDown:["",l.H.required],ccDetail:["",l.H.required],subGroup:["",l.H.required],items:this.fb.array([this.createItem()]),ccItemsTotalAmount:["",l.H.required],ccLeCurrency:["",l.H.required],approvers:["",l.H.required],hasBudget:["",l.H.required],budgetBalance:["",l.H.required],budgetBalanceCurrency:[""],workflowProperty:["",l.H.required],isExceptionalPR:[!1],isExceptionalPRAttachmentLoading:[""],exceptionalPRAttachment:[""]})}createCostcentreSplitUpsArr(){this.paymentDetailsFormGroup.get("paymentModel");let e=this.paymentDetailsFormGroup.get("ccSplitDetails"),t=this.generalDetailsFormgroup.get("costCenterArr");for(;0!==e.controls.length;)e.removeAt(0);t.value.forEach((t,n)=>{e.push(this.fb.group({ccDetail:[t.ccDetail,l.H.required],convertedAmount:["",l.H.required],amount:["",l.H.required]}))})}createItem(){return this.fb.group({itemName:["",l.H.required],itemQuantity:[1,l.H.required],itemMeasurementUnit:["",l.H.required],itemUnitPrice:["",l.H.required],service:["",l.H.required],itemAmount:["",l.H.required],itemCurrency:["",l.H.required],itemCurrencyName:[""],convertedAmount:["",l.H.required]})}addCC(){this.costCenterArr=this.generalDetailsFormgroup.get("costCenterArr"),this.costCenterArr.push(this.createCC())}ngOnInit(){return Object(i.c)(this,void 0,void 0,(function*(){this.isModalLoading=!0,this.checklistStepVisible=yield this.getPurchaseRequestTenantConfig("CLT"),this.isChecklistAttachment=yield this.getPurchaseRequestTenantConfig("CAE"),this.isExceptionalPRPaymentCreation=yield this.getPurchaseRequestTenantConfig("EPRC"),this.isItemCurrencyDefault=yield this.getPurchaseRequestTenantConfig("DVIC"),this.isEntityBasedCurrencyCode=yield this.getPurchaseRequestTenantConfig("EBCC"),this.isVendorTypeVisible=yield this.getPurchaseRequestTenantConfig("VNTMN"),this.sharepointInvoiceIntegration=yield this.getPurchaseRequestTenantConfig("SPINV"),this.isBillablePRAailable=yield this.getPurchaseRequestTenantConfig("CBBNBPR"),this.isBillablePRAailable&&(this.isBillablePRApplicable=!0,this.generalDetailsFormgroup.addControl("customerBilling",new l.j("",l.H.required)),yield this._p2pGeneral.getCustomerBillingType().pipe(Object(b.a)(this.$onDestroy)).subscribe(e=>{"S"==e.messType?(this.customerBillingType=e.data,this.generalDetailsFormgroup.get("customerBilling").setValue(this.customerBillingType[0].id)):this.customerBillingType=[]},e=>{this._toaster.showError("Error on customer billing type retrieval !","",2e3),console.error(e)})),this.isPeopleInvolvedAvailable=yield this.getPurchaseRequestTenantConfig("PIPRC"),this.isPeopleInvolvedAvailable&&(this.generalDetailsFormgroup.addControl("peopleInvolved",new l.j("")),yield this._p2pGeneral.getAvailableContractEmployeeList().pipe().subscribe(e=>{this.contractEmployeeList="S"==e.messType?e.data:[]},e=>{this._toaster.showError("Error on Contract Employee List Retrieval !","",2e3),console.error(e)})),console.log(this.isExceptionalPRPaymentCreation),this.PRDraftData&&"recallSubmit"==this.prCreationType||(this.isModalLoading=!1),this.uploader=new a.d({url:this.UPLOAD_ATTACHMENT_URL,authToken:"Bearer "+this._auth.getToken(),disableMultipart:!1}),this.detectUploadChanges(),this.currencies=yield this.getCurrencyList(),this.subGroups=yield this.getSubGroup(),this.costCenters=yield this.getCostCenters(),this.uomList=yield this.getUOMList(),this.services=yield this.getService(),this.vendors=yield this.getVendors(),this.prCurrencyCodeList=yield this.getCompanyCurrency(),this.prCurrencyCodeListDefault=this.prCurrencyCodeList,this.getPaymentModels(),this.getPaymentTerms(),this.getPaymentRecurrence(),this.getWorkflowProperties(),this.PRDraftData&&"recallSubmit"==this.prCreationType||(this.detectPaymentModelChanges(),this.detectCCArrChanges(),this.detectVendorChanges()),this.showMilestoneBudgetAvailability=yield this.getPurchaseRequestTenantConfig("BCCM"),this.PRDraftData&&"recallSubmit"==this.prCreationType&&(this.generalDetailsFormgroup.patchValue({amount:this.PRDraftData.amount,vendor:this.PRDraftData.vendor,attachment:this.PRDraftData.attachment,description:this.PRDraftData.description}),this.isBillablePRAailable&&this.generalDetailsFormgroup.patchValue({customerBilling:1==this.PRDraftData.customerBilling?2:1}),this.isPeopleInvolvedAvailable&&this.generalDetailsFormgroup.patchValue({peopleInvolved:this.PRDraftData.peopleInvolved}),this.patchFormArray(this.PRDraftData.costCenterArr,"generalDetailsFormgroup","costCenterArr"),this.generalDetailsFormgroup.updateValueAndValidity(),this.isModalLoading=!1)}))}detectUploadChanges(){this.uploader.onProgressItem=e=>{this.isCcUploadInProgress?this.generalDetailsFormgroup.get("costCenterArr").controls[this.currentCcUploadindex].get("isExceptionalPRAttachmentLoading").patchValue(!0):1==this.currentlyActivePaymentModel?this.paymentDetailsFormGroup.get("uploadInProgress").patchValue(!0):2!=this.currentlyActivePaymentModel&&3!=this.currentlyActivePaymentModel||this.paymentDetailsFormGroup.get("milestones").controls[this.currentMilestoneUploadIndex].get("uploadInProgress").patchValue(!0)},this.uploader.onCompleteItem=(e,t,n,i)=>{if(t=JSON.parse(t),this.isCcUploadInProgress){let e=this.generalDetailsFormgroup.get("costCenterArr");e.controls[this.currentCcUploadindex].get("isExceptionalPRAttachmentLoading").patchValue(!1),e.controls[this.currentCcUploadindex].get("exceptionalPRAttachment").patchValue(t),this.isCcUploadInProgress=!1}else if(1==this.currentlyActivePaymentModel)this.paymentDetailsFormGroup.get("attachmentData").patchValue(t),this.paymentDetailsFormGroup.get("uploadInProgress").patchValue(!1);else if(3==this.currentlyActivePaymentModel||2==this.currentlyActivePaymentModel){let e=this.paymentDetailsFormGroup.get("milestones");e.controls[this.currentMilestoneUploadIndex].get("uploadInProgress").patchValue(!1),e.controls[this.currentMilestoneUploadIndex].get("attachmentData").patchValue(t)}if(this.isCheckListUploadInProgress){let e=this.checklistDetailsFormGroup.get("checklistArr").controls[this.currentCheckListIndex];e.get("attachmentData").patchValue(t),e.get("isAttached").patchValue(!0),this.isCheckListUploadInProgress=!1}this.uploader.clearQueue()},this.uploader.onWhenAddingFileFailed=()=>{this.isCcUploadInProgress=!1,console.log("adding Failed!")},this.uploader.onAfterAddingFile=e=>{console.log("file added"),this.uploader.uploadAll()}}clearUpload(){this.uploader.clearQueue()}changeCc(e){var t;return Object(i.c)(this,void 0,void 0,(function*(){console.log(this.generalDetailsFormgroup);let n=this.generalDetailsFormgroup.value.costCenterArr[e];console.log(n);let l=o.where(this.costCenters,{cc_id:n.costCenterDropDown});console.log(l),console.log(l),this.costCenterArr=this.generalDetailsFormgroup.get("costCenterArr"),console.log(this.costCenterArr),l.length>=0&&this.costCenterArr.controls[e].get("ccDetail").patchValue(l[0]),this.costCenterArr.controls[e].get("costCenter").setValue(l[0].id),console.log(this.generalDetailsFormgroup),console.log(this.costCenterArr.controls[e].get("ccDetail"));let r=this.costCenterArr.controls[e].get("ccDetail").value.currency_code;console.log(r);let a=yield this.calculateTotalAmount(r,e);console.log(a);let s=null===(t=o.where(a,{currency_code:r})[0])||void 0===t?void 0:t.value;this.costCenterArr.controls[e].get("ccLeCurrency").patchValue(r),this.costCenterArr.controls[e].get("ccItemsTotalAmount").patchValue(a);let c={entity_id:this.costCenterArr.controls[e].get("ccDetail").value.legal_entity_id,entity_currency_code:r},m=o.findWhere(this.subGroups,{id:this.costCenterArr.controls[e].get("subGroup").value}),d=[{category:m.sub_group_id,amount:s,currency_code:r}],p=yield this.checkIfBudgetAvailable(l[0].id,m.id,s,r,l[0].cc_id);if("S"==p.messType){let t=p.balance?p.balance:0,n=t?p.currency:"";if(p.hasBudget)this.costCenterArr.controls[e].get("hasBudget").patchValue(!0),this.costCenterArr.controls[e].get("budgetBalance").patchValue(t),this.costCenterArr.controls[e].get("budgetBalanceCurrency").patchValue(n),this.costCenterArr.controls[e].get("isExceptionalPR").patchValue(!1);else{this.costCenterArr.controls[e].get("hasBudget").patchValue(!1),this.costCenterArr.controls[e].get("budgetBalance").patchValue(0),this.costCenterArr.controls[e].get("budgetBalanceCurrency").patchValue("");let t=yield this.hasExceptionalPrAllowed();t="S"==t.messType,this.costCenterArr.controls[e].get("isExceptionalPR").patchValue(t)}}else this._p2pGeneral.showMessage("Failed to retrieve budget details !");let u=o.pluck(this.costCenterArr.value,"ccItemsTotalAmount"),h=[];if(u.forEach(e=>{h.push({itemAmount:e})}),this._p2pGeneral.calculateTotInallCurrencies(h,this.prCurrencyCodeList).subscribe(e=>{this.generalDetailsFormgroup.get("amount").patchValue(e.total)},e=>{console.error(e)}),this.generalDetailsFormgroup.get("costCenterArr").controls[e].get("isExceptionalPR").value){let t=o.findWhere(this.subGroups,{id:this.costCenterArr.controls[e].get("subGroup").value});this._p2pGeneral.getExceptionalPrWf(t).subscribe(t=>Object(i.c)(this,void 0,void 0,(function*(){if("S"==t.messType){let n=t.data;this.costCenterArr.controls[e].get("workflowProperty").patchValue(n);let i=yield this.getExpenseApproversHierarchy(n,this.costCenterArr.controls[e].get("ccDetail").value);i.forEach(e=>{e.approvalStatus="Submitted"}),this.costCenterArr.controls[e].get("approvers").patchValue(i)}})))}else d[0].amount&&d[0].amount>0&&this._p2pGeneral.determineWfPropertyForEachPurchaseRequest(d,c,this.workflowProperties).subscribe(t=>Object(i.c)(this,void 0,void 0,(function*(){let n=t.data.wfProperty;this.costCenterArr.controls[e].get("workflowProperty").patchValue(n);let i=yield this.getExpenseApproversHierarchy(n,this.costCenterArr.controls[e].get("ccDetail").value);i.forEach(e=>{e.approvalStatus="Submitted"}),this.costCenterArr.controls[e].get("approvers").patchValue(i)})))}))}detectChangeInRecurrenceDetails(){this.paymentDetailsFormGroup.get("paymentReccurance").valueChanges.subscribe(e=>{this.startAutogen()}),this.paymentDetailsFormGroup.get("paymentEndDate").valueChanges.subscribe(e=>{this.startAutogen()}),this.paymentDetailsFormGroup.get("invoiceDate").valueChanges.subscribe(e=>{this.startAutogen()})}autoGenerateRecurringMilestones(){return Object(i.c)(this,void 0,void 0,(function*(){let e=this.paymentDetailsFormGroup.get("milestones");for(this.updateCcSplitUpAndPrTotal(e.controls.length,e.controls.length-1);e.controls.length>0;)e.removeAt(0);let t=this.paymentDetailsFormGroup.get("paymentReccurance").value,n=o.where(this.paymentReccurences,{id:t})[0].months,i=new Date(this.paymentDetailsFormGroup.get("paymentStartDate").value),r=new Date(this.paymentDetailsFormGroup.get("paymentEndDate").value),a=new Date(this.paymentDetailsFormGroup.get("invoiceDate").value),s=JSON.parse(JSON.stringify(this.paymentDetailsFormGroup.get("ccSplitDetails").value));for(i.getTime()<=a.getTime()&&e.push(this.fb.group({milestoneName:["payment for "+x(a).format("ll"),l.H.required],milestoneAmount:[this.generalDetailsFormgroup.get("amount").value,l.H.required],ccSplitDetails:[s,l.H.required],milestoneCurrency:["",l.H.required],invoiceDate:[a,l.H.required],invoiceNumber:[""],attachmentData:[""],uploadInProgress:[!1],hasBudgetAvailable:["",l.H.required]}));a.getTime()<=r.getTime();){let t=this.addMonth(a,n);t.getTime()<=r.getTime()&&e.push(this.fb.group({milestoneName:["payment for "+x(t).format("ll"),l.H.required],milestoneAmount:[this.generalDetailsFormgroup.get("amount").value,l.H.required],ccSplitDetails:[s,l.H.required],milestoneCurrency:["",l.H.required],invoiceDate:[t,l.H.required],invoiceNumber:[""],attachmentData:[""],uploadInProgress:[!1],hasBudgetAvailable:["",l.H.required]})),a=t}this.calculatePrTotalBasedOnRecurringMilestone(),yield this.checkMilestoneBudget(),this.getApproversForRecurringPayment()}))}checkIfCcHasExceptionalBudgetFlow(){let e=!1,t=this.generalDetailsFormgroup.get("costCenterArr");for(let n of t.controls)if(n.get("isExceptionalPR").value){e=!0;break}return e}addMonth(e,t){const n=new Date(e);return n.setMonth(n.getMonth()+t),n}createMilestoneFormArray(){return this.fb.group({milestoneName:["",l.H.required],milestoneAmount:["",l.H.required],currency:[this.preferredCurrency,l.H.required],invoiceDate:["",l.H.required],invoiceNumber:["",l.H.required],attachmentData:[""],uploadInProgress:[!1],ccSplitType:["auto",l.H.required],ccSplitDetails:this.fb.array([],l.H.required),isMileManualSplitApplied:!1,convertedMilestonAmt:["",l.H.required]})}changeInSplitTypeMileAmt(e){let t=this.paymentDetailsFormGroup.get("milestones"),n=o.where(this.generalDetailsFormgroup.get("amount").value,{currency_code:this.preferredCurrency})[0];if(t.controls[e].get("milestoneAmount").valid&&t.controls[e].get("milestoneAmount").value<=n.value){let n=t.controls[e].get("ccSplitType").value,i=t.controls[e].get("milestoneAmount").value;this._p2pGeneral.getMultipleCurrencyConversion(i,this.preferredCurrency,this.prCurrencyCodeList).subscribe(i=>{if(console.log(i),t.controls[e].get("convertedMilestonAmt").patchValue(i.result),"auto"==n){let n=this.generalDetailsFormgroup.get("costCenterArr"),i=t.controls[e].get("convertedMilestonAmt").value,r=o.where(this.generalDetailsFormgroup.get("amount").value,{currency_code:this.preferredCurrency}),a=t.controls[e].get("ccSplitDetails");for(;0!==a.length;)a.removeAt(0);n.value.forEach((e,t)=>{let n=o.where(e.ccItemsTotalAmount,{currency_code:this.preferredCurrency});if(n.length>0){let t=n[0].value/r[0].value*100,o=[];i.forEach((e,n)=>{o.push({currency_code:e.currency_code,value:Math.round(t/100*e.value)})}),a.push(this.fb.group({ccDetail:[e.ccDetail,l.H.required],convertedAmount:[o,l.H.required]}))}})}})}}changeMileSplitType(e,t){let n=this.paymentDetailsFormGroup.get("milestones");if("manual"==e){n.controls[t].get("ccSplitType").patchValue("manual"),n.controls[t].get("isMileManualSplitApplied").patchValue(!1);let e=n.controls[t].get("ccSplitDetails"),i=this.generalDetailsFormgroup.get("costCenterArr");for(;0!==e.length;)e.removeAt(0);i.value.forEach((t,n)=>{e.push(this.fb.group({ccDetail:[t.ccDetail,l.H.required],convertedAmount:["",l.H.required],fieldAmt:["",l.H.required]}))})}else n.controls[t].get("ccSplitType").patchValue("auto"),this.changeInSplitTypeMileAmt(t)}changeMileSplitAppliedStatus(e){this.paymentDetailsFormGroup.get("milestones").controls[e].get("isMileManualSplitApplied").patchValue(!1)}applyEnteredMileCCSplitups(e){let t=this.paymentDetailsFormGroup.get("milestones"),n=t.controls[e].get("ccSplitDetails"),i=0;n.value.forEach((l,r)=>{n.controls[r].get("fieldAmt").valid&&(i+=l.fieldAmt,this._p2pGeneral.convertCurrency(l.fieldAmt,this.preferredCurrency).subscribe(l=>{n.controls[r].get("convertedAmount").patchValue(JSON.parse(l.result[0][0].result));let a=o.where(t.controls[e].get("convertedMilestonAmt").value,{currency_code:this.preferredCurrency});t.controls[e].get("isMileManualSplitApplied").patchValue(!0),i!=a[0].value&&(n.controls[r].get("convertedAmount").reset(),t.controls[e].get("isMileManualSplitApplied").patchValue(!1),this._toaster.showError("Amount not matched !","Total of split ups are not equivalent to total  amount.",2e3))}))})}onFileAdd(e,t){let n=t.srcElement.files[0];console.log("File : ",n);let i=n.name.lastIndexOf("."),l=n.name.substring(i+1),r=n.name.substring(0,i);console.log(`fileName : ${r} type : ${l}`),this.invoiceFileDetailsSPIntegration.push({fileName:r,fileType:l,milestoneIndex:e,file:n}),console.log(e),this.currentMilestoneUploadIndex=e,this.uploader.uploadAll()}onSingleFileAdd(e){this.invoiceFileDetailsSPIntegration=[];let t=e.srcElement.files[0];console.log("File : ",t);let n=t.name.lastIndexOf("."),i=t.name.substring(n+1),l=t.name.substring(0,n);this.invoiceFileDetailsSPIntegration.push({fileName:l,fileType:i,file:t}),console.log(`fileName : ${l} type : ${i}`),this.uploader.uploadAll()}uploadExceptionalPrAttachment(e){this.isCcUploadInProgress=!0,this.currentCcUploadindex=e,this.uploader.uploadAll()}createPR(){return Object(i.c)(this,void 0,void 0,(function*(){if(this.checkForFormValidation()&&this.validateExceptionalPr()&&this.validateMilestone()&&this.validateChecklist()){this.handleRecurringCcAmount(),this.isLoading=!0;let e=Object.assign(Object.assign(Object.assign({},this.generalDetailsFormgroup.value),this.paymentDetailsFormGroup.value),this.checklistDetailsFormGroup.value);if(console.log("People Involved Data : ",e),this.finalResponse=e,this.finalResponse.customerBilling=null==this.finalResponse.customerBilling||null==this.finalResponse.customerBilling||1==this.finalResponse.customerBilling?0:1,this.finalResponse.invoiceDate&&(this.finalResponse.invoiceDate=x(this.finalResponse.invoiceDate).format("YYYY-MM-DD HH:mm:ss")),this.finalResponse.paymentStartDate&&(this.finalResponse.paymentStartDate=x(this.finalResponse.paymentStartDate).format("YYYY-MM-DD HH:mm:ss")),this.finalResponse.paymentEndDate&&(this.finalResponse.paymentEndDate=x(this.finalResponse.paymentEndDate).format("YYYY-MM-DD HH:mm:ss")),this.finalResponse.milestones&&o.each(this.finalResponse.milestones,e=>{e.invoiceDate=x(e.invoiceDate).format("YYYY-MM-DD HH:mm:ss")}),this.sharepointInvoiceIntegration)if(1==this.finalResponse.paymentModel){if(this.finalResponse.invoiceDate){let e,t,n,l,r=new Date(this.finalResponse.invoiceDate).getFullYear().toString(),a=["January","February","March","April","May","June","July","August","September","October","November","December"][new Date(this.finalResponse.invoiceDate).getMonth()];console.log("Invoice Year & Invoice Month ",r,a),yield this._o365.getToken().then(t=>{e=t});let s=[],c=[],m=[],d=244;this._p2pGeneral.getSharePointIntegrationDetails(d).subscribe(p=>Object(i.c)(this,void 0,void 0,(function*(){if("S"==p.messType){if(t=p.data.teams_channel_name,n=p.data.group_id,l=p.data.channel_item_id,l||(l=yield this.getChannelItemID(e,n,t),this._p2pGeneral.postSharePointIntegrationDetails(d,l,n,t).subscribe(e=>{"S"==e.messType&&console.log("Channel Itam ID Updated successfully!")})),s=yield this.getFolderDetails(e,n,l),console.log("Year Folders : ",s),s.map(e=>e.name).includes(r)){let t=s.find(e=>e.name==r);if(t=t.id,console.log(`Existing folder identified for the folder ${r}...: ${t}`),c=yield this.getFolderDetails(e,n,t),console.log("Month Folders : ",c),c.map(e=>e.name).includes(a)){let t=c.find(e=>e.name==a);t=t.id,console.log(`Existing folder identified for the folder ${a}...: ${t}`),m=yield this.getFolderDetails(e,n,t),console.log("Vendor Folders : ",m);let l=m.map(e=>e.name),r=this.vendors.find(e=>e.id==this.finalResponse.vendor);if(l.includes(r.name.trim())){let t=m.find(e=>e.name==r.name.trim());t=t.id,console.log(`Existing folder identified for the folder ${r.name.trim()}...: ${t}`),yield this.getFileandFolderDetails(e,n,t),console.log("Vendor File Folders : ",m),console.log(`In this Vendor folder ${r.name.trim()} files have to be stored....`),this.invoiceFileDetailsSPIntegration.length>0?o.each(this.invoiceFileDetailsSPIntegration,l=>Object(i.c)(this,void 0,void 0,(function*(){l.fileName&&l.fileType&&l.file&&(yield this.uploadFiles(e,n,t,`Inv_${x(this.finalResponse.invoiceDate).format("YYYY-MM-DD")}_${l.fileName}`,l.fileType,l.file))}))):console.log("No file to upload!")}else{yield this.createVendorFolder(e,n,t,r.name.trim()),m=yield this.getFolderDetails(e,n,t),console.log("Vendor Folders : ",c),s.map(e=>e.name);let l=m.find(e=>e.name==r.name.trim());l=l.id,console.log(`Existing folder identified for the folder ${r.name.trim()}...: ${l}`),yield this.getFileandFolderDetails(e,n,l),console.log("Vendor File Folders : ",m),console.log(`In this Vendor folder ${r.name.trim()} files have to be stored....`),this.invoiceFileDetailsSPIntegration.length>0?o.each(this.invoiceFileDetailsSPIntegration,t=>Object(i.c)(this,void 0,void 0,(function*(){t.fileName&&t.fileType&&t.file&&(yield this.uploadFiles(e,n,l,`Inv_${x(this.finalResponse.invoiceDate).format("YYYY-MM-DD")}_${t.fileName}`,t.fileType,t.file))}))):console.log("No file to upload!")}}else{yield this.createMonthFolder(e,n,t,a),c=yield this.getFolderDetails(e,n,t),console.log("Month Folders : ",c),c.map(e=>e.name);let l=c.find(e=>e.name==a);l=l.id,console.log(`Existing folder identified for the folder ${a}...: ${l}`);let r=this.vendors.find(e=>e.id==this.finalResponse.vendor);yield this.createVendorFolder(e,n,l,r.name.trim()),m=yield this.getFolderDetails(e,n,l),console.log("Vendor Folders : ",m),m.map(e=>e.name);let s=m.find(e=>e.name==r.name.trim());s=s.id,console.log(`Existing folder identified for the folder ${r.name.trim()}...: ${s}`),console.log("Attachemnts will be posted inside this vendor folder : ",r.name.trim(),s),this.invoiceFileDetailsSPIntegration.length>0?o.each(this.invoiceFileDetailsSPIntegration,t=>Object(i.c)(this,void 0,void 0,(function*(){t.fileName&&t.fileType&&t.file&&(yield this.uploadFiles(e,n,s,`Inv_${x(this.finalResponse.invoiceDate).format("YYYY-MM-DD")}_${t.fileName}`,t.fileType,t.file))}))):console.log("No file to upload!")}}else{yield this.createYearFolder(e,n,l,r),s=yield this.getFolderDetails(e,n,l),console.log("Year Folders : ",s),s.map(e=>e.name);let t=s.find(e=>e.name==r);t=t.id,console.log(`Existing folder identified for the folder ${r}...: ${t}`),yield this.createMonthFolder(e,n,t,a),c=yield this.getFolderDetails(e,n,t),console.log("Month Folders : ",c),c.map(e=>e.name);let d=c.find(e=>e.name==a);d=d.id,console.log(`Existing folder identified for the folder ${a}...: ${d}`);let p=this.vendors.find(e=>e.id==this.finalResponse.vendor);yield this.createVendorFolder(e,n,d,p.name.trim()),m=yield this.getFolderDetails(e,n,d),console.log("Vendor Folders : ",c),s.map(e=>e.name);let u=m.find(e=>e.name==p.name.trim());u=u.id,console.log(`Existing folder identified for the folder ${p.name.trim()}...: ${u}`),yield this.getFileandFolderDetails(e,n,u),console.log("Vendor File Folders : ",m),console.log(`In this Vendor folder ${p.name.trim()} files have to be stored....`),console.log("Attachemnts will be posted inside this vendor folder : ",p.name.trim(),u),this.invoiceFileDetailsSPIntegration.length>0?o.each(this.invoiceFileDetailsSPIntegration,t=>Object(i.c)(this,void 0,void 0,(function*(){t.fileName&&t.fileType&&t.file&&(yield this.uploadFiles(e,n,u,`Inv_${x(this.finalResponse.invoiceDate).format("YYYY-MM-DD")}_${t.fileName}`,t.fileType,t.file))}))):console.log("No file to upload!")}this.invoiceFileDetailsSPIntegration=[]}else this._toaster.showError("SharePoint Details not found!","",2e3)})),e=>{this._toaster.showError("Failed to create request !","",2e3),this.isLoading=!1,console.error(e)})}}else if(2==this.finalResponse.paymentModel){let e=[];if(this.finalResponse.milestones){for(let n=0;n<this.finalResponse.milestones.length;n++)e.push({invoiceDate:this.finalResponse.milestones[n].invoiceDate,milestoneName:this.finalResponse.milestones[n].milestoneName});let t;yield this._o365.getToken().then(e=>{t=e});for(let n=0;n<e.length;n++)yield this.milestoneInvSP_Integ(n,t,e[n]);this.invoiceFileDetailsSPIntegration=[]}}else{let e=[];if(this.finalResponse.milestones){for(let n=0;n<this.finalResponse.milestones.length;n++)e.push({invoiceDate:this.finalResponse.milestones[n].invoiceDate,milestoneName:this.finalResponse.milestones[n].milestoneName});let t;yield this._o365.getToken().then(e=>{t=e});for(let n=0;n<e.length;n++)yield this.milestoneInvSP_Integ(n,t,e[n]);this.invoiceFileDetailsSPIntegration=[]}}this._p2pGeneral.createPurchaseRequest(this.finalResponse).subscribe(e=>{this.isLoading=!1,"S"==e.messType?(this._toaster.showSuccess("Purchase request created successfully !","",2e3),this.dialogRef.close(e)):this._toaster.showError("Failed to create request !","",2e3)},e=>{this._toaster.showError("Failed to create request !","",2e3),this.isLoading=!1,console.error(e)})}}))}milestoneInvSP_Integ(e,t,n){return Object(i.c)(this,void 0,void 0,(function*(){try{let i=new Date(n.invoiceDate).getFullYear().toString(),l=["January","February","March","April","May","June","July","August","September","October","November","December"][new Date(n.invoiceDate).getMonth()];console.log("Invoice Year & Invoice Month for milestoneCount: ",e,i,l);let r=244,o=yield this._p2pGeneral.getSharePointIntegrationDetails(r).toPromise();if("S"===o.messType){let{teams_channel_name:a,group_id:s,channel_item_id:c}=o.data,m=c;m||(m=yield this.getChannelItemID(t,s,a),yield this._p2pGeneral.postSharePointIntegrationDetails(r,m,s,a).toPromise(),console.log("Channel Item ID Updated successfully!"));let d=yield this.getFolderDetails(t,s,m);if(console.log("Year Folders: ",d),d.map(e=>e.name).includes(i)){let r=d.find(e=>e.name===i).id;console.log(`Existing folder identified for the folder ${i}...: ${r}`);let o=yield this.getFolderDetails(t,s,r);if(console.log("Month Folders: ",o),o.map(e=>e.name).includes(l)){let i=o.find(e=>e.name===l).id;console.log(`Existing folder identified for the folder ${l}...: ${i}`);let r=yield this.getFolderDetails(t,s,i);console.log("Vendor Folders: ",r);let a=r.map(e=>e.name),c=this.vendors.find(e=>e.id===this.finalResponse.vendor);if(a.includes(c.name)){let i=r.find(e=>e.name===c.name).id;console.log(`Existing folder identified for the folder ${c.name}...: ${i}`);let l=this.invoiceFileDetailsSPIntegration.findIndex(t=>t.milestoneIndex===e);if(-1!==l){let e=this.invoiceFileDetailsSPIntegration[l];e.fileName&&e.fileType&&e.file&&(yield this.uploadFiles(t,s,i,`Milestone_${n.milestoneName}_${e.fileName}`,e.fileType,e.file))}}else{yield this.createVendorFolder(t,s,i,c.name);let l=yield this.getFolderDetails(t,s,i);console.log("Vendor Folders: ",l),l.map(e=>e.name);let r=l.find(e=>e.name===c.name).id;console.log(`Existing folder identified for the folder ${c.name}...: ${r}`);let o=this.invoiceFileDetailsSPIntegration.findIndex(t=>t.milestoneIndex===e);if(-1!==o){let e=this.invoiceFileDetailsSPIntegration[o];e.fileName&&e.fileType&&e.file&&(yield this.uploadFiles(t,s,r,`Milestone_${n.milestoneName}_${e.fileName}`,e.fileType,e.file))}}}else{yield this.createMonthFolder(t,s,r,l),o=yield this.getFolderDetails(t,s,r),console.log("Month Folders : ",o),o.map(e=>e.name);let i=o.find(e=>e.name==l);i=i.id,console.log(`Existing folder identified for the folder ${l}...: ${i}`);let a=this.vendors.find(e=>e.id==this.finalResponse.vendor);yield this.createVendorFolder(t,s,i,a.name);let c=yield this.getFolderDetails(t,s,i);console.log("Vendor Folders: ",c),c.map(e=>e.name);let m=c.find(e=>e.name===a.name).id;console.log(`Existing folder identified for the folder ${a.name}...: ${m}`);let d=this.invoiceFileDetailsSPIntegration.findIndex(t=>t.milestoneIndex===e);if(-1!==d){let e=this.invoiceFileDetailsSPIntegration[d];e.fileName&&e.fileType&&e.file&&(yield this.uploadFiles(t,s,m,`Milestone_${n.milestoneName}_${e.fileName}`,e.fileType,e.file))}}}else{yield this.createYearFolder(t,s,m,i);let r=yield this.getFolderDetails(t,s,m);console.log("Year Folders: ",r),r.map(e=>e.name);let o=r.find(e=>e.name==i);o=o.id,console.log(`Existing folder identified for the folder ${i}...: ${o}`),yield this.createMonthFolder(t,s,o,l);let a=yield this.getFolderDetails(t,s,o);console.log("Month Folders : ",a),a.map(e=>e.name);let c=a.find(e=>e.name==l);c=c.id,console.log(`Existing folder identified for the folder ${l}...: ${c}`);let d=this.vendors.find(e=>e.id==this.finalResponse.vendor);yield this.createVendorFolder(t,s,c,d.name);let p=yield this.getFolderDetails(t,s,c);console.log("Vendor Folders: ",p),p.map(e=>e.name);let u=p.find(e=>e.name===d.name).id;console.log(`Existing folder identified for the folder ${d.name}...: ${u}`);let h=this.invoiceFileDetailsSPIntegration.findIndex(t=>t.milestoneIndex===e);if(-1!==h){let e=this.invoiceFileDetailsSPIntegration[h];e.fileName&&e.fileType&&e.file&&(yield this.uploadFiles(t,s,u,`Milestone_${n.milestoneName}_${e.fileName}`,e.fileType,e.file))}}}else this._toaster.showError("SharePoint Details not found!","",5e3)}catch(i){this._toaster.showError("Failed to create request!","",5e3),this.isLoading=!1,console.error(i)}}))}uploadFiles(e,t,n,l,r,o){return Object(i.c)(this,void 0,void 0,(function*(){try{let i=`https://graph.microsoft.com/v1.0/groups/${t}/drive/items/${n}:/${l}.${r}:/content`;console.log("Endpoint Create Folder : ",i);let a=yield this.http.put(i,o,{headers:new V.f({Authorization:"Bearer "+e})}).toPromise();return a&&a.name&&console.log("File has been uploaded successfully !  : ",a.name),Promise.resolve(a)}catch(i){return console.log(`Error at file uploading for ${o}  : ${i}`),i}}))}createYearFolder(e,t,n,l){return Object(i.c)(this,void 0,void 0,(function*(){yield this.createFolder(e,t,n,l)}))}createMonthFolder(e,t,n,l){return Object(i.c)(this,void 0,void 0,(function*(){yield this.createFolder(e,t,n,l)}))}createVendorFolder(e,t,n,l){return Object(i.c)(this,void 0,void 0,(function*(){yield this.createFolder(e,t,n,l)}))}getChannelItemID(e,t,n){return Object(i.c)(this,void 0,void 0,(function*(){let i,l=`https://graph.microsoft.com/v1.0/groups/${t}/drive/root/children`;try{console.log("Endpoint : ",l);let t=yield this.http.get(l,{headers:new V.f({Authorization:"Bearer "+e})}).toPromise();return console.log("Response Data : ",t),t&&t.value.length>0&&(i=t.value.find(e=>e.name===n),i=i.id),Promise.resolve(i)}catch(r){return console.log("Error at getting channel's item id : ",r),r}}))}getFolderDetails(e,t,n){return Object(i.c)(this,void 0,void 0,(function*(){try{let i=[],l=`https://graph.microsoft.com/v1.0/groups/${t}/drive/items/${n}/children`;console.log("Endpoint get Folder Details : ",l);let r=yield this.http.get(l,{headers:new V.f({Authorization:"Bearer "+e})}).toPromise();return r&&r.value.length>0&&o.each(r.value,e=>{e.folder&&i.push(e)}),Promise.resolve(i)}catch(i){return console.log("Error at getting folder names : ",i),i}}))}getFileandFolderDetails(e,t,n){return Object(i.c)(this,void 0,void 0,(function*(){try{let i=[],l=`https://graph.microsoft.com/v1.0/groups/${t}/drive/items/${n}/children`;console.log("Endpoint : ",l);let r=yield this.http.get(l,{headers:new V.f({Authorization:"Bearer "+e})}).toPromise();return r&&r.value.length>0&&o.each(r.value,e=>{i.push(e)}),Promise.resolve(i)}catch(i){return console.log("Error at getting folder names : ",i),i}}))}createFolder(e,t,n,l){return Object(i.c)(this,void 0,void 0,(function*(){try{let i=`https://graph.microsoft.com/v1.0/groups/${t}/drive/items/${n}/children`;console.log(`Endpoint Create Folder for ${l}: ${i}`);const r={name:l,folder:{},"@microsoft.graph.conflictBehavior":"rename"};let o=yield this.http.post(i,r,{headers:new V.f({Authorization:"Bearer "+e,"Content-Type":"application/json"})}).toPromise();return o&&o.name&&console.log("New folder has been created successfully !  : ",o.name),Promise.resolve(o)}catch(i){return console.log(`Error at creating new folder for ${l}  : `,i),i}}))}handlePaymentModel(e){if(2==e.paymentModel){let t=e.milestones.length;e.amount=e.amount.map(e=>({currency_code:e.currency_code,value:e.value*t}))}return e}checkForFormValidation(){let e="",t=!0;if(this.generalDetailsFormgroup.get("amount").valid)if(this.generalDetailsFormgroup.get("vendor").valid)if(this.paymentDetailsFormGroup.get("paymentModel").valid)if(this.paymentDetailsFormGroup.get("paymentTerm").valid){if(!this.generalDetailsFormgroup.get("costCenterArr").valid){let t=this.generalDetailsFormgroup.get("costCenterArr");for(let n=0;n<t.controls.length;n++){let i=!0;if(t.controls[n].get("approvers").valid){if(!t.controls[n].get("items").valid){let l=t.controls[n].get("items");for(let t=0;t<l.controls.length&&(l.controls[t].get("itemUnitPrice").valid?l.controls[t].get("itemCurrency").valid?l.controls[t].get("itemMeasurementUnit").valid?(e="Please choose unit of measurement to proceed",i=!1):l.controls[t].get("itemQuantity").valid?l.controls[t].get("itemName").valid?l.controls[t].get("service").valid||(e="Please choose service to proceed",i=!1):(e="Please enter item name to proceed",i=!1):(e="Please enter number of units  to proceed",i=!1):(e="Please choose item currency to proceed",i=!1):(e="Please enter item amount to proceed",i=!1),i);t++);}}else e="Approvers not found ! , Kindly contact KEBS team.",i=!1;if(!i)break}}}else e="Please select the payment term !";else e="Please select the payment model !";else e="Please select the vendor !";else e="Please enter the amount !";let n=this.generalDetailsFormgroup.get("costCenterArr");console.log("costCenterArr value validation... : ",this.generalDetailsFormgroup.get("costCenterArr").value);for(let i=0;i<n.controls.length;i++){let t=!0,l=n.controls[i].get("items");for(let n=0;n<l.controls.length&&(null!==l.controls[n].get("itemUnitPrice").value&&l.controls[n].get("itemUnitPrice").valid?null!==l.controls[n].get("itemCurrency").value&&l.controls[n].get("itemCurrency").valid?null!==l.controls[n].get("itemMeasurementUnit").value&&l.controls[n].get("itemMeasurementUnit").valid?null!==l.controls[n].get("itemQuantity").value&&l.controls[n].get("itemQuantity").valid?null!==l.controls[n].get("itemName").value&&l.controls[n].get("itemName").valid?null!==l.controls[n].get("service").value&&l.controls[n].get("service").valid||(e="Please choose service to proceed",t=!1):(e="Please enter item name to proceed",t=!1):(e="Please enter number of units  to proceed",t=!1):(e="Please choose unit of measurement to proceed",t=!1):(e="Please choose item currency to proceed",t=!1):(e="Please enter item amount to proceed",t=!1),t);n++);if(!t)break}return this.isBillablePRAailable&&(this.generalDetailsFormgroup.get("customerBilling").valid||(e="Please select the customer billing type !")),""!=e&&(this._p2pGeneral.showMessage(e),t=!1),t}validateChecklist(){let e=this.checklistDetailsFormGroup.get("checklistArr"),t=!0;if(e.controls.length>0&&this.checklistStepVisible)for(let n of e.controls)if(this.isChecklistAttachment){if(console.log(n),!(n.get("isCompleted").value||n.get("isAttached").value&&n.get("isCommented").value)){this._toaster.showError("Invalid checklist","Kindly complete the checklist or attach the attachment with comments",3e3),t=!1;break}}else if(!n.get("isCompleted").value){this._toaster.showError("Invalid checklist","Kindly complete the checklist to proceed",2e3),t=!1;break}return t}validateMilestone(){let e=!0;if(1==this.paymentDetailsFormGroup.get("paymentModel").value)e=!0;else if(2==this.paymentDetailsFormGroup.get("paymentModel").value){let t=this.paymentDetailsFormGroup.get("milestones");if(0==t.controls.length)this._toaster.showError("No milestones found","Add milestones to proceed !",2e3),e=!1;else for(let n of t.controls)if(!n.get("hasBudgetAvailable").value){this._toaster.showError("Budget unavailable","Budget not found for milestone - "+n.get("milestoneName").value,2e3),e=!1;break}}else if(3==this.paymentDetailsFormGroup.get("paymentModel").value){this.milestoneArr=this.paymentDetailsFormGroup.get("milestones");let t=o.where(this.generalDetailsFormgroup.get("amount").value,{currency_code:this.preferredCurrency})[0],n=0,i=!0;this.milestoneArr.controls.forEach((e,t)=>{n+=parseInt(e.get("milestoneAmount").value)});for(let e of this.milestoneArr.controls)if(!e.get("invoiceDate").valid){i=!1;break}0==this.milestoneArr.controls.length?(this._toaster.showError("No milestones found","Add milestones to proceed !",2e3),e=!1):n>t.value?(this._toaster.showError("Exceeding Limit","Total Milestone amount exceeding the PR total",2e3),e=!1):i||(this._toaster.showError("Invoice date not found !","Kindly choose the invoice date to proceed !",2e3),e=!1)}return e}validateExceptionalPr(){let e,t=!0,n=this.generalDetailsFormgroup.get("costCenterArr");return n.controls.every((i,l)=>{if(!n.controls[l].get("hasBudget").value){if(!n.controls[l].get("isExceptionalPR").value)return e={title:"Budget unavailable",msg:"Budget not sufficient to create this request !"},t=!1,!1;if(!n.controls[l].get("exceptionalPRAttachment").value)return e={title:"Attachment not found",msg:"Kindly attach document for exceptional PR approval !"},t=!1,!1}return!0}),t||this._toaster.showError(e.title,e.msg,3e3),t}removeInvoice(e){return Object(i.c)(this,void 0,void 0,(function*(){let t,n;1==this.currentlyActivePaymentModel?(t=this.paymentDetailsFormGroup.get("attachmentData").value,this.paymentDetailsFormGroup.get("uploadInProgress").patchValue(!0)):2!=this.currentlyActivePaymentModel&&3!=this.currentlyActivePaymentModel||(n=this.paymentDetailsFormGroup.get("milestones"),n.controls[e].get("uploadInProgress").patchValue(!0),t=n.controls[e].get("attachmentData").value),yield this._p2pGeneral.deletePRAttachment(t).subscribe(t=>{"S"==t.messType&&(1==this.currentlyActivePaymentModel?(this.paymentDetailsFormGroup.get("attachmentData").patchValue(""),this.paymentDetailsFormGroup.get("uploadInProgress").patchValue(!1)):2!=this.currentlyActivePaymentModel&&3!=this.currentlyActivePaymentModel||(n.controls[e].get("attachmentData").patchValue(""),n.controls[e].get("uploadInProgress").patchValue(!1)))},t=>{console.log(t),1==this.currentlyActivePaymentModel?this.paymentDetailsFormGroup.get("uploadInProgress").patchValue(!1):n.controls[e].get("uploadInProgress").patchValue(!1)})}))}removeExceptionalPrAttachment(e){return Object(i.c)(this,void 0,void 0,(function*(){let t,n=this.generalDetailsFormgroup.get("costCenterArr");n.controls[e].get("isExceptionalPRAttachmentLoading").patchValue(!0),t=n.controls[e].get("exceptionalPRAttachment").value,yield this._p2pGeneral.deletePRAttachment(t).subscribe(t=>{"S"==t.messType&&(n.controls[e].get("exceptionalPRAttachment").patchValue(""),n.controls[e].get("isExceptionalPRAttachmentLoading").patchValue(!1))},t=>{console.log(t),n.controls[e].get("isExceptionalPRAttachmentLoading").patchValue(!1)})}))}checkForDelete(){this.isAvailable=!0,console.log("payment model"),console.log(this.paymentDetailsFormGroup.get("paymentModel").value),2==this.paymentDetailsFormGroup.get("paymentModel").value&&(this.isAvailable=!1)}ngOnDestroy(){this.subs.unsubscribe()}patchFormArray(e,t,n){if("generalDetailsFormgroup"==t){if(this.generalDetailsFormgroup.get(n).clear(),"costCenterArr"==n){let t=this.generalDetailsFormgroup.controls.costCenterArr;e.forEach((e,n)=>{t.push(this.fb.group({costCenter:e.costCenter,costCenterDropDown:e.costCenterDropDown,ccDetail:e.ccDetail,subGroup:e.subGroup,ccItemsTotalAmount:e.ccItemsTotalAmount,ccLeCurrency:e.ccLeCurrency,approvers:e.approvers,hasBudget:e.hasBudget,budgetBalance:e.budgetBalance,budgetBalanceCurrency:e.budgetBalanceCurrency,workflowProperty:e.workflowProperty,isExceptionalPR:e.isExceptionalPR,isExceptionalPRAttachmentLoading:e.isExceptionalPRAttachmentLoading,exceptionalPRAttachment:e.exceptionalPRAttachment,items:this.fb.array([])}));const i=t.at(n).get("items");e.items.forEach(e=>{const t=this.fb.group({itemName:e.itemName,itemQuantity:e.itemQuantity,itemMeasurementUnit:e.itemMeasurementUnit,itemUnitPrice:e.itemUnitPrice,service:e.service,itemAmount:e.itemAmount,itemCurrency:e.itemCurrency,itemCurrencyName:e.itemCurrencyName,convertedAmount:e.convertedAmount});i.push(t)}),this.changeItemAmount(n),this.detectPaymentModelChanges(),this.detectCCArrChanges(),this.detectVendorChanges()})}1!=this.PRDraftData.paymentModel&&2!=this.PRDraftData.paymentModel||("auto"==this.PRDraftData.ccSplitType?this.changeCCSplitType(this.PRDraftData.ccSplitType):"manual"==this.PRDraftData.ccSplitType&&this.applyEnteredCCSplitups())}if("paymentDetailsFormGroup"==t){if(this.paymentDetailsFormGroup.get(n).clear(),"ccSplitDetails"==n){let t=this.paymentDetailsFormGroup.controls.ccSplitDetails;e.forEach((e,n)=>{console.log("ccSplitDetails value : ",e),t.push(this.fb.group({amount:e.amount,ccDetail:e.ccDetail,convertedAmount:e.convertedAmount})),this.changeItemAmount(n),this.detectPaymentModelChanges(),this.detectCCArrChanges(),this.detectVendorChanges()})}if("milestones"==n){let t=this.paymentDetailsFormGroup.controls.milestones;e.forEach((e,n)=>{t.push(this.fb.group({attachmentData:e.attachmentData,hasBudgetAvailable:e.hasBudgetAvailable,invoiceDate:e.invoiceDate,invoiceNumber:e.invoiceNumber,milestoneAmount:e.milestoneAmount,milestoneCurrency:e.milestoneCurrency,milestoneName:e.milestoneName,uploadInProgress:e.uploadInProgress,ccSplitType:e.hasOwnProperty("ccSplitType")?e.ccSplitType:null,convertedMilestonAmt:e.hasOwnProperty("convertedMilestonAmt")?e.convertedMilestonAmt:null,currency:e.hasOwnProperty("currency")?e.currency:null,ccSplitDetails:this.fb.array([])}));const i=t.at(n).get("ccSplitDetails");e.ccSplitDetails.forEach(e=>{console.log("ccSplit Details Value for MileStone : ",e);const t=this.fb.group({amount:e.amount,ccDetail:e.ccDetail,convertedAmount:e.convertedAmount});i.push(t)}),this.detectPaymentModelChanges(),this.detectCCArrChanges(),this.detectVendorChanges()})}1!=this.PRDraftData.paymentModel&&2!=this.PRDraftData.paymentModel||("auto"==this.PRDraftData.ccSplitType?this.changeCCSplitType(this.PRDraftData.ccSplitType):"manual"==this.PRDraftData.ccSplitType&&this.applyEnteredCCSplitups())}}reSubmitPR(){return Object(i.c)(this,void 0,void 0,(function*(){if(this.checkForFormValidation()&&this.validateExceptionalPr()&&this.validateMilestone()&&this.validateChecklist()){this.handleRecurringCcAmount(),this.isLoading=!0;let e=Object.assign(Object.assign(Object.assign({},this.generalDetailsFormgroup.value),this.paymentDetailsFormGroup.value),this.checklistDetailsFormGroup.value);if(this.finalResponse=e,this.finalResponse.customerBilling=null==this.finalResponse.customerBilling||null==this.finalResponse.customerBilling||1==this.finalResponse.customerBilling?0:1,this.finalResponse.invoiceDate&&(this.finalResponse.invoiceDate=x(this.finalResponse.invoiceDate).format("YYYY-MM-DD HH:mm:ss")),this.finalResponse.paymentStartDate&&(this.finalResponse.paymentStartDate=x(this.finalResponse.paymentStartDate).format("YYYY-MM-DD HH:mm:ss")),this.finalResponse.paymentEndDate&&(this.finalResponse.paymentEndDate=x(this.finalResponse.paymentEndDate).format("YYYY-MM-DD HH:mm:ss")),this.finalResponse.milestones&&o.each(this.finalResponse.milestones,e=>{e.invoiceDate=x(e.invoiceDate).format("YYYY-MM-DD HH:mm:ss")}),this.sharepointInvoiceIntegration)if(1==this.finalResponse.paymentModel){if(this.finalResponse.invoiceDate){let e,t,n,l,r=new Date(this.finalResponse.invoiceDate).getFullYear().toString(),a=["January","February","March","April","May","June","July","August","September","October","November","December"][new Date(this.finalResponse.invoiceDate).getMonth()];console.log("Invoice Year & Invoice Month ",r,a),yield this._o365.getToken().then(t=>{e=t});let s=[],c=[],m=[],d=244;this._p2pGeneral.getSharePointIntegrationDetails(d).subscribe(p=>Object(i.c)(this,void 0,void 0,(function*(){if("S"==p.messType){if(t=p.data.teams_channel_name,n=p.data.group_id,l=p.data.channel_item_id,l||(l=yield this.getChannelItemID(e,n,t),this._p2pGeneral.postSharePointIntegrationDetails(d,l,n,t).subscribe(e=>{"S"==e.messType&&console.log("Channel Itam ID Updated successfully!")})),s=yield this.getFolderDetails(e,n,l),console.log("Year Folders : ",s),s.map(e=>e.name).includes(r)){let t=s.find(e=>e.name==r);if(t=t.id,console.log(`Existing folder identified for the folder ${r}...: ${t}`),c=yield this.getFolderDetails(e,n,t),console.log("Month Folders : ",c),c.map(e=>e.name).includes(a)){let t=c.find(e=>e.name==a);t=t.id,console.log(`Existing folder identified for the folder ${a}...: ${t}`),m=yield this.getFolderDetails(e,n,t),console.log("Vendor Folders : ",m);let l=m.map(e=>e.name),r=this.vendors.find(e=>e.id==this.finalResponse.vendor);if(l.includes(r.name.trim())){let t=m.find(e=>e.name==r.name.trim());t=t.id,console.log(`Existing folder identified for the folder ${r.name.trim()}...: ${t}`),yield this.getFileandFolderDetails(e,n,t),console.log("Vendor File Folders : ",m),console.log(`In this Vendor folder ${r.name.trim()} files have to be stored....`),this.invoiceFileDetailsSPIntegration.length>0?o.each(this.invoiceFileDetailsSPIntegration,l=>Object(i.c)(this,void 0,void 0,(function*(){l.fileName&&l.fileType&&l.file&&(yield this.uploadFiles(e,n,t,`Inv_${x(this.finalResponse.invoiceDate).format("YYYY-MM-DD")}_${l.fileName}`,l.fileType,l.file))}))):console.log("No file to upload!")}else{yield this.createVendorFolder(e,n,t,r.name.trim()),m=yield this.getFolderDetails(e,n,t),console.log("Vendor Folders : ",c),s.map(e=>e.name);let l=m.find(e=>e.name==r.name.trim());l=l.id,console.log(`Existing folder identified for the folder ${r.name.trim()}...: ${l}`),yield this.getFileandFolderDetails(e,n,l),console.log("Vendor File Folders : ",m),console.log(`In this Vendor folder ${r.name.trim()} files have to be stored....`),this.invoiceFileDetailsSPIntegration.length>0?o.each(this.invoiceFileDetailsSPIntegration,t=>Object(i.c)(this,void 0,void 0,(function*(){t.fileName&&t.fileType&&t.file&&(yield this.uploadFiles(e,n,l,`Inv_${x(this.finalResponse.invoiceDate).format("YYYY-MM-DD")}_${t.fileName}`,t.fileType,t.file))}))):console.log("No file to upload!")}}else{yield this.createMonthFolder(e,n,t,a),c=yield this.getFolderDetails(e,n,t),console.log("Month Folders : ",c),c.map(e=>e.name);let l=c.find(e=>e.name==a);l=l.id,console.log(`Existing folder identified for the folder ${a}...: ${l}`);let r=this.vendors.find(e=>e.id==this.finalResponse.vendor);yield this.createVendorFolder(e,n,l,r.name.trim()),m=yield this.getFolderDetails(e,n,l),console.log("Vendor Folders : ",m),m.map(e=>e.name);let s=m.find(e=>e.name==r.name.trim());s=s.id,console.log(`Existing folder identified for the folder ${r.name.trim()}...: ${s}`),console.log("Attachemnts will be posted inside this vendor folder : ",r.name.trim(),s),this.invoiceFileDetailsSPIntegration.length>0?o.each(this.invoiceFileDetailsSPIntegration,t=>Object(i.c)(this,void 0,void 0,(function*(){t.fileName&&t.fileType&&t.file&&(yield this.uploadFiles(e,n,s,`Inv_${x(this.finalResponse.invoiceDate).format("YYYY-MM-DD")}_${t.fileName}`,t.fileType,t.file))}))):console.log("No file to upload!")}}else{yield this.createYearFolder(e,n,l,r),s=yield this.getFolderDetails(e,n,l),console.log("Year Folders : ",s),s.map(e=>e.name);let t=s.find(e=>e.name==r);t=t.id,console.log(`Existing folder identified for the folder ${r}...: ${t}`),yield this.createMonthFolder(e,n,t,a),c=yield this.getFolderDetails(e,n,t),console.log("Month Folders : ",c),c.map(e=>e.name);let d=c.find(e=>e.name==a);d=d.id,console.log(`Existing folder identified for the folder ${a}...: ${d}`);let p=this.vendors.find(e=>e.id==this.finalResponse.vendor);yield this.createVendorFolder(e,n,d,p.name.trim()),m=yield this.getFolderDetails(e,n,d),console.log("Vendor Folders : ",c),s.map(e=>e.name);let u=m.find(e=>e.name==p.name.trim());u=u.id,console.log(`Existing folder identified for the folder ${p.name.trim()}...: ${u}`),yield this.getFileandFolderDetails(e,n,u),console.log("Vendor File Folders : ",m),console.log(`In this Vendor folder ${p.name.trim()} files have to be stored....`),console.log("Attachemnts will be posted inside this vendor folder : ",p.name.trim(),u),this.invoiceFileDetailsSPIntegration.length>0?o.each(this.invoiceFileDetailsSPIntegration,t=>Object(i.c)(this,void 0,void 0,(function*(){t.fileName&&t.fileType&&t.file&&(yield this.uploadFiles(e,n,u,`Inv_${x(this.finalResponse.invoiceDate).format("YYYY-MM-DD")}_${t.fileName}`,t.fileType,t.file))}))):console.log("No file to upload!")}this.invoiceFileDetailsSPIntegration=[]}else this._toaster.showError("SharePoint Details not found!","",2e3)})),e=>{this._toaster.showError("Failed to create request !","",2e3),this.isLoading=!1,console.error(e)})}}else if(2==this.finalResponse.paymentModel){let e=[];if(this.finalResponse.milestones){for(let n=0;n<this.finalResponse.milestones.length;n++)e.push({invoiceDate:this.finalResponse.milestones[n].invoiceDate,milestoneName:this.finalResponse.milestones[n].milestoneName});let t;yield this._o365.getToken().then(e=>{t=e});for(let n=0;n<e.length;n++)yield this.milestoneInvSP_Integ(n,t,e[n]);this.invoiceFileDetailsSPIntegration=[]}}else{let e=[];if(this.finalResponse.milestones){for(let n=0;n<this.finalResponse.milestones.length;n++)e.push({invoiceDate:this.finalResponse.milestones[n].invoiceDate,milestoneName:this.finalResponse.milestones[n].milestoneName});let t;yield this._o365.getToken().then(e=>{t=e});for(let n=0;n<e.length;n++)yield this.milestoneInvSP_Integ(n,t,e[n]);this.invoiceFileDetailsSPIntegration=[]}}this._p2pGeneral.reSubmitPurchaseRequest(this.data.p2p_header_id,this.finalResponse).subscribe(e=>{this.isLoading=!1,"S"==e.messType?(this._toaster.showSuccess("Purchase Request moved to Submitted successfully !","",2e3),this.dialogRef.close(e),setTimeout(()=>{window.location.href="/main/p2p/prs"},2e3)):this._toaster.showError("Failed to resubmit request !","",2e3)},e=>{this._toaster.showError("Failed to resubmit request !","",2e3),this.isLoading=!1,console.error(e)})}}))}}return e.\u0275fac=function(t){return new(t||e)(F["\u0275\u0275directiveInject"](V.c),F["\u0275\u0275directiveInject"](O.a),F["\u0275\u0275directiveInject"]($.a),F["\u0275\u0275directiveInject"](l.i),F["\u0275\u0275directiveInject"](L.a),F["\u0275\u0275directiveInject"](j.a),F["\u0275\u0275directiveInject"](B.a),F["\u0275\u0275directiveInject"](q.a),F["\u0275\u0275directiveInject"](s.h),F["\u0275\u0275directiveInject"](s.a))},e.\u0275cmp=F["\u0275\u0275defineComponent"]({type:e,selectors:[["p2p-create-request"]],decls:4,vars:2,consts:[[1,"row"],["matTooltip","Please wait...","class","d-flex pt-4 justify-content-center col-12",4,"ngIf"],["class","col-12 pl-4 pr-4",4,"ngIf"],["matTooltip","Please wait...",1,"d-flex","pt-4","justify-content-center","col-12"],["role","progressbar","mode","indeterminate","diameter","30",2,"width","30px","height","30px"],[1,"col-12","pl-4","pr-4"],[1,"row","mt-3","pb-3","border-bottom","solid"],[1,"col-12","p-0"],[1,"row","align-items-center"],[1,"col-0","pt-1"],[1,"cart-icon"],["class","col-10 pl-1",4,"ngIf"],[1,"col-1","p-0","d-flex","justify-content-end"],["matTooltip","Close","mat-icon-button","",1,"close-btn",3,"click"],[1,"row","border-bottom","solid"],[1,"",3,"ngClass"],[1,"number-circle","mr-2",3,"ngStyle","click"],[1,"step-title",3,"ngStyle","click"],[3,"ngClass"],["class","col-4 p-3  d-flex align-items-center justify-content-start",4,"ngIf"],[3,"formGroup"],[4,"ngIf"],[1,"col-10","pl-1"],[1,"form-title"],[1,"requestor"],["class","row",4,"ngIf"],[1,"form-title",2,"color","red"],[1,"col-4","p-3","d-flex","align-items-center","justify-content-start"],[1,"row","mt-2"],[1,"col-2","p-0"],[1,"field-title",2,"font-weight","500"],[1,"col-2","pl-3"],[1,"col-1","pl-3"],[1,"col-2","pl-2"],["formArrayName","costCenterArr",4,"ngFor","ngForOf"],[1,"col-4","pt-0","pl-0","pr-0"],[1,"row","mt-0"],[1,"col-9","p-0"],["placeholder","Vendor*","formControlName","vendor",3,"list"],["class","col-5 pt-0 pl-0 pr-0",4,"ngIf"],["class","col-4 pt-0 pl-0 pr-0",4,"ngIf"],[1,"row","mt-1"],[1,"col-3","p-0"],["appearance","outline",2,"width","100%"],["formControlName","description","matInput","",2,"height","13vh"],[1,"col-10"],[1,"col-2"],["mat-mini-fab","","matTooltip","Next",1,"create-pr-btn",3,"click"],["formArrayName","costCenterArr"],[1,"border-bottom","solid","pb-3",3,"formGroupName"],[1,"col-2","p-0","my-auto"],["placeholder","Cost Center*","formControlName","costCenterDropDown",3,"list","change"],[1,"col-2","pl-3","my-auto"],["placeholder","Sub Group*","formControlName","subGroup",3,"list","change"],[1,"col-1","pl-3","d-flex","align-items-center"],[1,"add_btn",3,"click"],[1,"add_btn","ml-3",3,"click"],["placement","top","content-type","template",1,"col-1","my-auto","px-0",3,"tooltip"],[1,"col-2","my-auto","px-0"],["style","font-size: 22px;",3,"ngStyle",4,"ngIf"],[1,"col-10","pl-2","pr-0"],["class","row budget-available-small",4,"ngIf"],[1,"row","budget-available-small",2,"font-size","11px !important"],["budgetBalanceToolTip",""],["class","col-2 pl-3 d-flex align-items-center",4,"ngIf"],[1,"col-12","p-0","d-flex","align-items-center"],["class","total-box p-3",4,"ngIf"],["class","col-2",4,"ngIf"],["formArrayName","items","style","background-color: #f3f2f285;",4,"ngFor","ngForOf"],[2,"font-size","22px",3,"ngStyle"],[1,"row","budget-available-small"],[1,"col-2","pl-3","d-flex","align-items-center"],[4,"ngFor","ngForOf"],["placement","top","content-type","template","max-width","300","imgHeight","28px","imgWidth","28px",3,"tooltip","id"],["appraiserTooltip",""],[1,"total-box","p-3"],[2,"font-weight","500"],["type","small",1,"flex-1",3,"showActualAmount","currencyList"],["class","d-flex flex-row",4,"ngIf"],[1,"d-flex","flex-row"],[2,"color","gray","font-size","21px"],[1,"exceptional-pr-msg"],[2,"color","green","font-size","21px"],["mat-stroked-button","","class","upload-invoice-btn",3,"click",4,"ngIf"],["class","m-auto","diameter","20",4,"ngIf"],["mat-stroked-button","",1,"upload-invoice-btn",3,"click"],["type","file","ng2FileSelect","",2,"display","none",3,"uploader","change"],["fileInput",""],["diameter","20",1,"m-auto"],[1,"ml-2","my-auto","file-name",3,"matTooltip"],["mat-icon-button","","matTooltip","Remove attachment",3,"click"],[2,"font-size","21px"],["formArrayName","items",2,"background-color","#f3f2f285"],[3,"formGroupName"],[1,"row","mt-1","border-left","solid"],[1,"col-1","border-bottom","solid"],["matInput","","placeholder","Item name*","formControlName","itemName"],["placeholder","Service*","formControlName","service",3,"list"],["placeholder","Unit of measurement*","formControlName","itemMeasurementUnit",3,"list"],[1,"col-1"],[1,"col-3","pt-2","pl-0","pr-0"],["matInput","","type","number","placeholder","No of units*","formControlName","itemQuantity","oninput","this.value = Math.abs(this.value)",3,"keyup"],["matInput","","type","number","placeholder","Unit Price*","formControlName","itemUnitPrice","oninput","if(this.value<0){this.value= this.value * -1}",3,"keyup"],["placeholder","Currency*","formControlName","itemCurrency",3,"list","change"],["class","total-box",4,"ngIf"],[1,"total-box"],[2,"font-weight","500","color","#cf0001"],[1,"col-5","pt-0","pl-0","pr-0"],["class","row mt-0",4,"ngIf"],[1,"col-6","p-0"],[1,"col-5","p-0"],["placeholder","Customer Billing *","formControlName","customerBilling",3,"list"],["formControlName","peopleInvolved","placeholder","People Involved",1,"filter-wrap",3,"list"],["placeholder","Payment model*","formControlName","paymentModel",3,"list","change"],["placeholder","Payment terms*","formControlName","paymentTerm",3,"list"],[1,"col-3"],["mat-flat-button","",3,"ngClass","click"],["class","row mt-3",4,"ngIf"],[1,"row","mt-3"],["class","col-4",4,"ngFor","ngForOf"],[1,"col-4"],["class","col-2","formArrayName","ccSplitDetails",4,"ngFor","ngForOf"],["mat-flat-button","",1,"split-active",3,"click"],["formArrayName","ccSplitDetails",1,"col-2"],["type","number","matInput","","placeholder","Amount","formControlName","amount"],["matSuffix",""],["mat-icon-button","",3,"click"],["class","row mt-2",4,"ngIf"],["class","row mt-1",4,"ngIf"],[1,"row","mt-4"],["matInput","","formControlName","tAndC"],["class","row","style","margin-top: 3rem;",4,"ngIf"],["matInput","","formControlName","invoiceDate",3,"matDatepicker"],["matSuffix","",3,"for"],["picker1",""],[1,"col-2","mx-2","pl-0"],["matInput","","placeholder","Invoice number","formControlName","invoiceNumber"],[1,"col-4","d-flex","align-items-center"],[1,"ml-2","my-auto","file-name",2,"cursor","pointer",3,"matTooltip"],["matTooltip","Remove invoice",1,"mx-1"],["placeholder","Payment recurrence*","formControlName","paymentReccurance",3,"list"],["matInput","","formControlName","paymentStartDate",3,"matDatepicker","dateChange"],["picker3",""],["matInput","","formControlName","paymentEndDate",3,"matDatepicker","min","dateChange"],["picker4",""],["appearance","outline"],["picker5",""],[1,"col-1","clearButtonDiv"],["mat-button","","color","warn","matTooltip","Clear Milestone Changes",1,"clear-pr-btn",3,"click"],["class","row mt-2 mb-4 pl-4",4,"ngIf"],["formArrayName","milestones",4,"ngFor","ngForOf"],[1,"row","mt-2","mb-4","pl-4"],[1,"col-1","p-0"],[1,"d-flex","flex-row-reverse"],["formArrayName","milestones"],[1,"p-3","border-bottom","solid",2,"background-color","#f9f7f7",3,"formGroupName"],[1,"col-2","d-flex","pl-0"],[1,"col-1","align-items-center","p-0"],[1,"col-2","align-items-center","p-0"],["picker6",""],[1,"col-2","d-flex","align-items-center"],[1,"col-1","mt-2","d-flex"],["class","add_btn ml-3","matTooltip","Remove milestone",3,"click",4,"ngIf"],["matTooltip","checking budget availability...","class","m-auto","diameter","20",4,"ngIf"],[3,"ngStyle",4,"ngIf"],["class","pl-3 budget-available",4,"ngIf"],["multiFileInput",""],["matTooltip","Remove milestone",1,"add_btn","ml-3",3,"click"],["matTooltip","checking budget availability...","diameter","20",1,"m-auto"],[3,"ngStyle"],[1,"pl-3","budget-available"],[1,"col-2","pl-0"],["matInput","","placeholder","Milestone Name*","formControlName","milestoneName"],["type","number","matInput","","placeholder","Milestone Amount*","formControlName","milestoneAmount","onkeyup","if(this.value<0){this.value= this.value * -1}",3,"change"],[1,"col-2","mt-2","d-flex"],["matTooltip","Add milestone",1,"add_btn",3,"click"],["type","number","matInput","","placeholder","Amount","formControlName","fieldAmt"],[1,"row",2,"margin-top","3rem"],["mat-mini-fab","",1,"create-pr-btn",3,"ngClass","click"],[3,"matTooltip",4,"ngIf"],["matTooltip","Please wait...","class","spinner-align","diameter","20",4,"ngIf"],[3,"matTooltip"],["matTooltip","Please wait...","diameter","20",1,"spinner-align"],["style","margin-top: 4rem;",4,"ngIf"],[2,"color","#808080","font-size","13px"],[1,"d-flex","justify-content-center","my-3"],[1,"check-list-area"],[1,"col-5"],[1,"col-1","pl-0"],["class","col-1 pl-0",4,"ngIf"],["class","col-2 pl-0",4,"ngIf"],["formArrayName","checklistArr","class","row",4,"ngFor","ngForOf"],[1,"field-title",2,"position","relative","right","10px"],["formArrayName","checklistArr",1,"row"],[1,"card","col-12",2,"border-left-width","5px !important",3,"formGroupName","ngStyle"],[1,"card-body","row","px-0","py-2"],[1,"col-2","pl-0","my-auto",2,"font-weight","500"],[1,"col-5","pl-0","my-auto"],[1,"col-1","pl-0","my-auto"],[1,"d-flex"],["mat-icon-button","",2,"font-size","25px",3,"ngStyle","click"],[2,"font-size","24px"],["class","col-1 pl-0 my-auto",4,"ngIf"],["class","col-3 p-0",4,"ngIf"],["class","invoice-upload-btn",3,"click",4,"ngIf"],["class","ml-2 my-auto file-name","style","cursor: pointer;font-size: 13px;padding-top:10px;\n                                                max-width: 60px; position: relative;right: 10px;",3,"matTooltip",4,"ngIf"],[1,"invoice-upload-btn",3,"click"],[2,"color","#66615b","font-size","22px","padding-top","8px"],[1,"ml-2","my-auto","file-name",2,"cursor","pointer","font-size","13px","padding-top","10px","max-width","60px","position","relative","right","10px",3,"matTooltip"],["matInput","",2,"height","5vh",3,"change"],[2,"margin-top","4rem"],[1,"d-flex","pb-2","justify-content-center","align-items-center","slide-in-top"],[2,"font-weight","500","font-size","16px"],[1,"d-flex","justify-content-center","align-items-center","slide-from-down","pt-2","pb-2"],["src","https://assets.kebs.app/images/nomilestone.png","height","270","width","300",1,"mt-2","mb-2"]],template:function(e,t){1&e&&(F["\u0275\u0275elementStart"](0,"div",0),F["\u0275\u0275template"](1,z,2,0,"div",1),F["\u0275\u0275template"](2,wt,31,23,"div",2),F["\u0275\u0275elementEnd"](),F["\u0275\u0275element"](3,"br")),2&e&&(F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",t.isModalLoading),F["\u0275\u0275advance"](1),F["\u0275\u0275property"]("ngIf",!t.isModalLoading))},directives:function(){return[c.NgIf,S.a,C.c,f.a,v.a,c.NgClass,c.NgStyle,l.J,l.w,l.n,c.NgForOf,H.a,l.v,l.l,m.c,l.e,d.b,l.h,l.o,M,g.a,Y.a,U.a,a.b,m.g,l.A,N,m.i,u.g,u.i,u.f]},styles:[".form-title[_ngcontent-%COMP%]{font-weight:700}.requestor[_ngcontent-%COMP%]{font-size:13px;color:grey}.cart-icon[_ngcontent-%COMP%]{color:#b3a8a8;font-size:20px}.number-circle[_ngcontent-%COMP%]{cursor:pointer;height:20px;width:20px;border-radius:50%;display:flex;background-color:#817e7e;align-items:center;justify-content:center;color:#fff}.step-title[_ngcontent-%COMP%]{cursor:pointer;color:#817e7e;font-weight:500}.step-active[_ngcontent-%COMP%]{background-color:#ff5253}  .mat-button-toggle-group .mat-button-toggle .mat-button-toggle-button{height:36px}.add_btn[_ngcontent-%COMP%]{height:33px;width:33px;display:flex;cursor:pointer;border:1px solid #efa6a6;align-items:center;border-radius:4px;justify-content:center}.field-title[_ngcontent-%COMP%]{font-size:11px}.upload-invoice-btn[_ngcontent-%COMP%]{color:#cf0001}.budget-available[_ngcontent-%COMP%]{color:grey;font-size:13px;font-weight:500}.budget-available-small[_ngcontent-%COMP%]{color:grey;font-size:12px;font-weight:500}.total-box[_ngcontent-%COMP%]{min-width:12rem;border:1px solid grey;height:3rem;display:flex;justify-content:center;align-items:center}.create-pr-btn[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff}.create-pr-btn-loading[_ngcontent-%COMP%]{background-color:#fff}.split-active[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff}.file-name[_ngcontent-%COMP%]{width:90px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:inline-block}.exceptional-pr-msg[_ngcontent-%COMP%]{font-size:11px;font-weight:500;color:grey}.check-list-area[_ngcontent-%COMP%]{width:70vw}.clear-pr-btn[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff}.clearButtonDiv[_ngcontent-%COMP%]{margin-left:5px}.invoice-upload-btn[_ngcontent-%COMP%]{cursor:pointer}.slide-in-top[_ngcontent-%COMP%]{animation:slide-in-top .5s cubic-bezier(.25,.46,.45,.94) both}@keyframes slide-in-top{0%{transform:translateY(-30px);opacity:0}to{transform:translateY(0);opacity:1}}"]}),e})(),_t=(()=>{class e{}return e.\u0275mod=F["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=F["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},providers:[],imports:[[c.CommonModule,s.g,m.e,d.c,l.E,l.p,p.c,u.h,h.x,g.b,a.c,f.b,v.b,y.a,C.b,S.b,D.b,w.d,k.b]]}),e})()},"TmG/":function(e,t,n){"use strict";n.d(t,"a",(function(){return y}));var i=n("fXoL"),l=n("3Pt+"),r=n("jtHE"),o=n("XNiG"),a=n("NJ67"),s=n("1G5W"),c=n("kmnG"),m=n("ofXK"),d=n("d3UM"),p=n("FKr1"),u=n("WJ5W"),h=n("Qu3c");function g(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-label"),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](e.placeholder)}}function f(e,t){if(1&e&&(i["\u0275\u0275elementStart"](0,"mat-option",7),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()),2&e){const e=i["\u0275\u0275nextContext"]();i["\u0275\u0275property"]("value",null),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate"](e.disableNone?"Select One":"None")}}function v(e,t){if(1&e){const e=i["\u0275\u0275getCurrentView"]();i["\u0275\u0275elementStart"](0,"mat-option",8),i["\u0275\u0275listener"]("click",(function(){i["\u0275\u0275restoreView"](e);const n=t.$implicit;return i["\u0275\u0275nextContext"]().emitChanges(n)})),i["\u0275\u0275text"](1),i["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;i["\u0275\u0275property"]("value",e.id||e._id)("matTooltip",e.name),i["\u0275\u0275advance"](1),i["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let y=(()=>{class e extends a.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new l.j,this.fieldFilterCtrl=new l.j,this.list=[],this.required=!1,this.disableNone=!1,this.valueChange=new i.EventEmitter,this.disabled=!1,this.hasNoneOption=!0,this.filteredList=new r.a,this.change=new i.EventEmitter,this.hideMatLabel=!1,this._onDestroy=new o.b,this.emitChanges=e=>{this.change.emit(e)}}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(s.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(s.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}}return e.\u0275fac=function(t){return new(t||e)(i["\u0275\u0275directiveInject"](i.Renderer2))},e.\u0275cmp=i["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search"]],inputs:{list:"list",placeholder:"placeholder",required:"required",disableNone:"disableNone",disabled:"disabled",hasNoneOption:"hasNoneOption",hideMatLabel:"hideMatLabel"},outputs:{valueChange:"valueChange",change:"change"},features:[i["\u0275\u0275ProvidersFeature"]([{provide:l.t,useExisting:Object(i.forwardRef)(()=>e),multi:!0}]),i["\u0275\u0275InheritDefinitionFeature"],i["\u0275\u0275NgOnChangesFeature"]],decls:9,vars:11,consts:[["appearance","outline"],[4,"ngIf"],[3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value",4,"ngIf"],[3,"value","matTooltip","click",4,"ngFor","ngForOf"],[3,"value"],[3,"value","matTooltip","click"]],template:function(e,t){1&e&&(i["\u0275\u0275elementStart"](0,"mat-form-field",0),i["\u0275\u0275template"](1,g,2,1,"mat-label",1),i["\u0275\u0275elementStart"](2,"mat-select",2,3),i["\u0275\u0275elementStart"](4,"mat-option"),i["\u0275\u0275element"](5,"ngx-mat-select-search",4),i["\u0275\u0275elementEnd"](),i["\u0275\u0275template"](6,f,2,2,"mat-option",5),i["\u0275\u0275template"](7,v,2,3,"mat-option",6),i["\u0275\u0275pipe"](8,"async"),i["\u0275\u0275elementEnd"](),i["\u0275\u0275elementEnd"]()),2&e&&(i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",!t.hideMatLabel),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),i["\u0275\u0275advance"](3),i["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngIf",t.hasNoneOption),i["\u0275\u0275advance"](1),i["\u0275\u0275property"]("ngForOf",i["\u0275\u0275pipeBind1"](8,9,t.filteredList)))},directives:[c.c,m.NgIf,d.c,l.v,l.k,l.F,p.p,u.a,m.NgForOf,c.g,h.a],pipes:[m.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{font-size:13px!important;width:100%}"]}),e})()}}]);