<div class="milestone-dialog-container">
  <h2 mat-dialog-title>Create Milestone</h2>
  
  <mat-dialog-content class="dialog-content">
    <!-- Selected Expenses Summary -->
    <div class="expense-summary">
      <mat-card class="summary-card">
        <mat-card-content>
          <div class="summary-row">
            <span class="label">Selected Expenses:</span>
            <span class="value">{{ selectedExpensesCount }} items</span>
          </div>
          <div class="summary-row">
            <span class="label">Total Amount:</span>
            <span class="value total-amount">{{ formattedTotalAmount }}</span>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Milestone Form -->
    <form [formGroup]="milestoneForm" class="milestone-form">
      <!-- Milestone Name Field -->
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Milestone Name</mat-label>
        <input 
          matInput 
          formControlName="milestone_name" 
          placeholder="Enter milestone name"
          maxlength="100">
        <mat-error *ngIf="milestoneForm.get('milestone_name')?.hasError('required')">
          Milestone name is required
        </mat-error>
        <mat-error *ngIf="milestoneForm.get('milestone_name')?.hasError('minlength')">
          Milestone name must be at least 3 characters long
        </mat-error>
      </mat-form-field>

      <!-- Quote Selection Field -->
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Select Quote</mat-label>
        <mat-select formControlName="quote_id" [disabled]="isLoading">
          <mat-option *ngFor="let quote of availableQuotes" [value]="quote.id">
            {{ quote.name }} ({{ quote.value | currency:'USD':'symbol':'1.2-2' }})
          </mat-option>
        </mat-select>
        <mat-error *ngIf="milestoneForm.get('quote_id')?.hasError('required')">
          Please select a quote
        </mat-error>
        <mat-progress-spinner 
          *ngIf="isLoading" 
          diameter="20" 
          mode="indeterminate"
          matSuffix>
        </mat-progress-spinner>
      </mat-form-field>
    </form>

    <!-- Loading State -->
    <div *ngIf="isLoading" class="loading-container">
      <mat-progress-spinner mode="indeterminate" diameter="40"></mat-progress-spinner>
      <p>Loading available quotes...</p>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions align="end" class="dialog-actions">
    <button 
      mat-button 
      (click)="onCancel()" 
      [disabled]="isSubmitting">
      Cancel
    </button>
    <button 
      mat-raised-button 
      color="primary" 
      (click)="onSubmit()" 
      [disabled]="!milestoneForm.valid || isSubmitting">
      <mat-progress-spinner 
        *ngIf="isSubmitting" 
        diameter="20" 
        mode="indeterminate">
      </mat-progress-spinner>
      <span *ngIf="!isSubmitting">Create Milestone</span>
      <span *ngIf="isSubmitting">Creating...</span>
    </button>
  </mat-dialog-actions>
</div>
