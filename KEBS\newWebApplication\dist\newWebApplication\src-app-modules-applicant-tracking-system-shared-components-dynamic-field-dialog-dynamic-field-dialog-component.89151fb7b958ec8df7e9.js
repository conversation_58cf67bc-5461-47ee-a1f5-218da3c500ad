(window.webpackJsonp=window.webpackJsonp||[]).push([[936],{WUlG:function(t,e,n){"use strict";n.r(e),n.d(e,"DynamicFieldDialogComponent",(function(){return M}));var i=n("mrSG"),a=n("0IaG"),r=n("wd/R"),s=n("1G5W"),o=n("XNiG"),d=n("xG9w"),l=n("fXoL"),c=n("XNFG"),h=n("YVm3"),u=n("XXEo"),g=n("RThm"),p=n("ofXK"),m=n("NFeN"),f=n("UVjm"),v=n("Xa2L");const b=function(t,e,n,i){return{"pointer-events":t,color:e,background:n,border:i}};function x(t,e){if(1&t){const t=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementStart"](0,"div",20),l["\u0275\u0275listener"]("click",(function(){l["\u0275\u0275restoreView"](t);const n=e.$implicit;return l["\u0275\u0275nextContext"](3).onClickDate(n.date)})),l["\u0275\u0275text"](1),l["\u0275\u0275elementEnd"]()}if(2&t){const t=e.$implicit,n=l["\u0275\u0275nextContext"](3);l["\u0275\u0275property"]("ngStyle",l["\u0275\u0275pureFunction4"](2,b,!0===(null==t?null:t.isPrevOrNextMonth)?"none":"",!0===(null==t?null:t.isPrevOrNextMonth)?"#B9C0CA":t.date==n.calendar?"#1890FF":"#8B95A5",t.date==n.calendar?"#0084FE33":"#FFFFFF",t.date==n.calendar?"1px solid #1890FF":"none")),l["\u0275\u0275advance"](1),l["\u0275\u0275textInterpolate1"](" ",null==t?null:t.day," ")}}function S(t,e){if(1&t&&(l["\u0275\u0275elementStart"](0,"div",18),l["\u0275\u0275template"](1,x,2,7,"div",19),l["\u0275\u0275elementEnd"]()),2&t){const t=e.$implicit;l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngForOf",t)}}const y=function(t){return{visibility:t}};function D(t,e){if(1&t){const t=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementContainerStart"](0),l["\u0275\u0275elementStart"](1,"div",10),l["\u0275\u0275elementStart"](2,"div",11),l["\u0275\u0275elementStart"](3,"div",12),l["\u0275\u0275elementStart"](4,"mat-icon",13),l["\u0275\u0275listener"]("click",(function(){return l["\u0275\u0275restoreView"](t),l["\u0275\u0275nextContext"]().getPreviousMonth()})),l["\u0275\u0275text"](5," keyboard_arrow_left "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](6,"div",14),l["\u0275\u0275text"](7),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](8,"div",12),l["\u0275\u0275elementStart"](9,"mat-icon",13),l["\u0275\u0275listener"]("click",(function(){return l["\u0275\u0275restoreView"](t),l["\u0275\u0275nextContext"]().getNextMonth()})),l["\u0275\u0275text"](10," keyboard_arrow_right "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](11,"div",15),l["\u0275\u0275elementStart"](12,"div"),l["\u0275\u0275elementStart"](13,"div",16),l["\u0275\u0275text"](14,"Mo"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](15,"div"),l["\u0275\u0275elementStart"](16,"div",16),l["\u0275\u0275text"](17,"Tu"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](18,"div"),l["\u0275\u0275elementStart"](19,"div",16),l["\u0275\u0275text"](20,"We"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](21,"div"),l["\u0275\u0275elementStart"](22,"div",16),l["\u0275\u0275text"](23,"Th"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](24,"div"),l["\u0275\u0275elementStart"](25,"div",16),l["\u0275\u0275text"](26,"Fr"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](27,"div"),l["\u0275\u0275elementStart"](28,"div",16),l["\u0275\u0275text"](29,"Sa"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](30,"div"),l["\u0275\u0275elementStart"](31,"div",16),l["\u0275\u0275text"](32,"Su"),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](33,S,2,1,"div",17),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementContainerEnd"]()}if(2&t){const t=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](3),l["\u0275\u0275property"]("ngStyle",l["\u0275\u0275pureFunction1"](4,y,t.disableNextOrPreviousMonth("previous")?"hidden":"")),l["\u0275\u0275advance"](4),l["\u0275\u0275textInterpolate1"](" ",t.monthYear," "),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngStyle",l["\u0275\u0275pureFunction1"](6,y,t.disableNextOrPreviousMonth("next")?"hidden":"")),l["\u0275\u0275advance"](25),l["\u0275\u0275property"]("ngForOf",t.daysOfMonth)}}const C=function(){return[]};function _(t,e){if(1&t){const t=l["\u0275\u0275getCurrentView"]();l["\u0275\u0275elementContainerStart"](0),l["\u0275\u0275elementStart"](1,"app-single-select-chip",21),l["\u0275\u0275listener"]("onValueChange",(function(e){return l["\u0275\u0275restoreView"](t),l["\u0275\u0275nextContext"]().onCustomSelectValueChange(e)})),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementContainerEnd"]()}if(2&t){const t=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("placeholder","Select")("masterData",t.data.masterData||l["\u0275\u0275pureFunction0"](4,C))("selectedValue",t.inputSearch)("displayClose",!1)}}function O(t,e){if(1&t&&(l["\u0275\u0275elementContainerStart"](0),l["\u0275\u0275text"](1),l["\u0275\u0275elementContainerEnd"]()),2&t){const t=l["\u0275\u0275nextContext"]();l["\u0275\u0275advance"](1),l["\u0275\u0275textInterpolate1"](" ",t.data.yesBtnText," ")}}function I(t,e){1&t&&l["\u0275\u0275element"](0,"mat-spinner",22)}const P=function(t){return{"pointer-events":t}};let M=(()=>{class t{constructor(t,e,n,i,a,r){this.data=t,this._dialogRef=e,this._toaster=n,this._jobService=i,this._loginService=a,this._onboardingService=r,this._onDestroy=new o.b,this.isApiInProgress=!1,this.inputSearch=null,this.daysOfMonth=[],this.monthYear="",this.monthStartDate="",this.monthEndDate="",this.calendar=null}ngOnInit(){return Object(i.c)(this,void 0,void 0,(function*(){this.aid=this._loginService.getProfile().profile.aid,yield this.getCalendarUI(r())}))}onClose(){this._dialogRef.close(!1)}onCustomSelectValueChange(t){this.inputSearch=t.val}onClickYes(){return Object(i.c)(this,void 0,void 0,(function*(){if("calendar"==this.data.type){if(!this.calendar||""==this.calendar)return void this._toaster.showWarning("Warning \u26a0\ufe0f","Kindly select a Date!",3e3);if("jobValidityDate"==this.data.key)yield this.updateJobDueDate(this.data.bulkId,this.calendar,this.data.isBulkSelectActive,d.pluck(this.data.jobsExcluded,"jobId"));else if("checklistDueDate"==this.data.key){let t={updatedValue:this.calendar,selectedDetails:this.data.selectedDetails,unselectedDetails:this.data.unselectedDetails,isBulkSelectActive:this.data.isBulkSelectActive,searchParams:this.data.search_params};yield this.updateCandidateChecklistDueDateInBulk(t)}else if("onboardingDate"==this.data.key){let t={updatedValue:this.calendar,selectedDetails:this.data.selectedDetails,unselectedDetails:this.data.unselectedDetails,isBulkSelectActive:this.data.isBulkSelectActive,searchParams:this.data.search_params,onboardingStatus:this.data.onboardingStatusId,groupId:this.data.groupId};"onboardingDetailsGroup"==this.data.module?yield this.updateOnboardingDateForCandidatesInGroupInBulk(t):yield this.updateOnboardingDateForCandidatesInBulk(t)}this._dialogRef.close(!0)}else if("input-search"==this.data.type){if(!this.inputSearch)return void this._toaster.showWarning("Warning \u26a0\ufe0f","Kindly select a Value!",3e3);if("jobHiringManager"==this.data.key)yield this.updateJobHiringManager(this.data.bulkId,this.inputSearch,this.data.isBulkSelectActive,d.pluck(this.data.jobsExcluded,"jobId"));else if("assignOnboardingSpoc"==this.data.key){let t={updatedValue:this.inputSearch,selectedDetails:this.data.selectedDetails,unselectedDetails:this.data.unselectedDetails,isBulkSelectActive:this.data.isBulkSelectActive,searchParams:this.data.search_params,onboardingStatus:this.data.onboardingStatusId,groupId:this.data.groupId};"onboardingDetailsGroup"==this.data.module?yield this.updateOnboardingSpocForCandidatesInGroupInBulk(t):yield this.updateOnboardingSpocForCandidatesInBulk(t)}else if("assignCandidateToGroup"==this.data.key){let t={updatedValue:this.inputSearch,selectedDetails:this.data.selectedDetails,unselectedDetails:this.data.unselectedDetails,isBulkSelectActive:this.data.isBulkSelectActive,searchParams:this.data.search_params,onboardingStatus:this.data.onboardingStatusId,groupId:this.data.groupId};yield this.assignGroupToOnboardingCandidatesInBulk(t)}this._dialogRef.close(!0)}}))}getCalendarUI(t){var e,n,a,s;return Object(i.c)(this,void 0,void 0,(function*(){this.monthStartDate=r(t).startOf("month").format("YYYY-MM-DD"),this.monthEndDate=r(t).endOf("month").format("YYYY-MM-DD"),this.monthYear=r(this.monthEndDate).format("MMMM YYYY");let i=this.monthStartDate;this.daysOfMonth=[];for(let t=0;t<7&&!(r(i).month()!=r(this.monthEndDate).month()&&t>=4)&&r(i).month()==r(this.monthStartDate).month();t++){const o=Array(7).fill(null);let d,l=0;for(let c=0;c<7;c++){d=r(i).startOf("isoWeek").add(l,"days").format("YYYY-MM-DD");let h=!1;(r(d).date()<r(this.monthStartDate).date()&&0===t||r(d).date()>r(this.monthEndDate).date()&&r(this.monthEndDate).month()===r(d).month()||r(d).month()!=r(this.monthEndDate).month())&&(h=!0),(null===(e=this.data.validations)||void 0===e?void 0:e.minDate)&&r(d,"YYYY-MM-DD").isBefore(r(null===(n=this.data.validations)||void 0===n?void 0:n.minDate,"YYYY-MM-DD"))&&(h=!0),(null===(a=this.data.validations)||void 0===a?void 0:a.maxDate)&&r(d,"YYYY-MM-DD").isAfter(r(null===(s=this.data.validations)||void 0===s?void 0:s.maxDate,"YYYY-MM-DD"))&&(h=!0),o.splice(c,1,{date:d,day:r(d).date(),isPrevOrNextMonth:h}),l++}this.daysOfMonth.push(o),i=r(d).add(1,"day")}}))}getPreviousMonth(){return Object(i.c)(this,void 0,void 0,(function*(){yield this.getCalendarUI(r(this.monthStartDate).subtract(1,"day"))}))}getNextMonth(){return Object(i.c)(this,void 0,void 0,(function*(){yield this.getCalendarUI(r(this.monthEndDate).add(1,"day"))}))}disableNextOrPreviousMonth(t){var e,n,i,a;return"next"==t?(null===(e=this.data.validations)||void 0===e?void 0:e.maxDate)&&r(this.monthEndDate).isAfter(null===(n=this.data.validations)||void 0===n?void 0:n.maxDate):"previous"==t?(null===(i=this.data.validations)||void 0===i?void 0:i.minDate)&&r(this.monthStartDate).isBefore(null===(a=this.data.validations)||void 0===a?void 0:a.minDate):void 0}onClickDate(t){var e,n;return Object(i.c)(this,void 0,void 0,(function*(){if("calendar"==this.data.type){let i=null===(e=this.data.validations)||void 0===e?void 0:e.minDate,a=null===(n=this.data.validations)||void 0===n?void 0:n.maxDate;if(i&&r(t,"YYYY-MM-DD").isBefore(r(i,"YYYY-MM-DD")))return void this._toaster.showWarning("Warning \u26a0\ufe0f","Date must be greater than current date!",3e3);if(a&&r(t,"YYYY-MM-DD").isAfter(r(i,"YYYY-MM-DD")))return void this._toaster.showWarning("Warning \u26a0\ufe0f","Date must be lesser than current date!",3e3)}let i=r(this.calendar).isSame(t);this.calendar=i?null:r(t).format("YYYY-MM-DD")}))}updateJobDueDate(t,e,n,a){return Object(i.c)(this,void 0,void 0,(function*(){return this.isApiInProgress=!0,new Promise((i,r)=>this._jobService.updateJobDueDate(t,e,this.aid,n,a,this.data.jobStatusId,this.data.isCampusJob,this.data.search_params,this.data.filter).pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this._toaster.showSuccess("Success","Update Successful",3e3):this._toaster.showError("Error",t.msg,3e3),this.isApiInProgress=!1,i(!0)},error:t=>{this._toaster.showError("Error",t.message?t.message:"Update Failed!",3e3),this.isApiInProgress=!1,r()}}))}))}updateJobHiringManager(t,e,n,a){return Object(i.c)(this,void 0,void 0,(function*(){return this.isApiInProgress=!0,new Promise((i,r)=>this._jobService.updateJobHiringManager(t,e,this.aid,n,a,this.data.jobStatusId,this.data.isCampusJob,this.data.search_params,this.data.filter).pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this._toaster.showSuccess("Success","Update Successful",3e3):this._toaster.showError("Error",t.msg,3e3),this.isApiInProgress=!1,i(!0)},error:t=>{this._toaster.showError("Error",t.message?t.message:"Update Failed!",3e3),this.isApiInProgress=!1,r()}}))}))}updateCandidateChecklistDueDateInBulk(t){return Object(i.c)(this,void 0,void 0,(function*(){return this.isApiInProgress=!0,new Promise((e,n)=>this._onboardingService.updateCandidateChecklistDueDateInBulk(t).pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this._toaster.showSuccess("Success \u2705",t.msg,3e3):this._toaster.showError("Error",t.msg,3e3),this.isApiInProgress=!1,e(!0)},error:t=>{this._toaster.showError("Error",t.message?t.message:"Update Failed!",3e3),this.isApiInProgress=!1,n()}}))}))}updateOnboardingDateForCandidatesInBulk(t){return Object(i.c)(this,void 0,void 0,(function*(){return this.isApiInProgress=!0,new Promise((e,n)=>this._onboardingService.updateOnboardingDateForCandidatesInBulk(t).pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this._toaster.showSuccess("Success \u2705",t.msg,3e3):this._toaster.showError("Error",t.msg,3e3),this.isApiInProgress=!1,e(!0)},error:t=>{this._toaster.showError("Error",t.message?t.message:"Update Failed!",3e3),this.isApiInProgress=!1,n()}}))}))}updateOnboardingSpocForCandidatesInBulk(t){return Object(i.c)(this,void 0,void 0,(function*(){return this.isApiInProgress=!0,new Promise((e,n)=>this._onboardingService.updateOnboardingSpocForCandidatesInBulk(t).pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this._toaster.showSuccess("Success \u2705",t.msg,3e3):this._toaster.showError("Error",t.msg,3e3),this.isApiInProgress=!1,e(!0)},error:t=>{this._toaster.showError("Error",t.message?t.message:"Update Failed!",3e3),this.isApiInProgress=!1,n()}}))}))}assignGroupToOnboardingCandidatesInBulk(t){return Object(i.c)(this,void 0,void 0,(function*(){return this.isApiInProgress=!0,new Promise((e,n)=>this._onboardingService.assignGroupToOnboardingCandidatesInBulk(t).pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this._toaster.showSuccess("Success \u2705",t.msg,3e3):this._toaster.showError("Error",t.msg,3e3),this.isApiInProgress=!1,e(!0)},error:t=>{this._toaster.showError("Error",t.message?t.message:"Assign Failed!",3e3),this.isApiInProgress=!1,n()}}))}))}updateOnboardingDateForCandidatesInGroupInBulk(t){return Object(i.c)(this,void 0,void 0,(function*(){return this.isApiInProgress=!0,new Promise((e,n)=>this._onboardingService.updateOnboardingDateForCandidatesInGroupInBulk(t).pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this._toaster.showSuccess("Success \u2705",t.msg,3e3):this._toaster.showError("Error",t.msg,3e3),this.isApiInProgress=!1,e(!0)},error:t=>{this._toaster.showError("Error",t.message?t.message:"Update Failed!",3e3),this.isApiInProgress=!1,n()}}))}))}updateOnboardingSpocForCandidatesInGroupInBulk(t){return Object(i.c)(this,void 0,void 0,(function*(){return this.isApiInProgress=!0,new Promise((e,n)=>this._onboardingService.updateOnboardingSpocForCandidatesInGroupInBulk(t).pipe(Object(s.a)(this._onDestroy)).subscribe({next:t=>{0==t.err?this._toaster.showSuccess("Success \u2705",t.msg,3e3):this._toaster.showError("Error",t.msg,3e3),this.isApiInProgress=!1,e(!0)},error:t=>{this._toaster.showError("Error",t.message?t.message:"Assign Failed!",3e3),this.isApiInProgress=!1,n()}}))}))}}return t.\u0275fac=function(e){return new(e||t)(l["\u0275\u0275directiveInject"](a.a),l["\u0275\u0275directiveInject"](a.h),l["\u0275\u0275directiveInject"](c.a),l["\u0275\u0275directiveInject"](h.a),l["\u0275\u0275directiveInject"](u.a),l["\u0275\u0275directiveInject"](g.a))},t.\u0275cmp=l["\u0275\u0275defineComponent"]({type:t,selectors:[["app-dynamic-field-dialog"]],decls:16,vars:13,consts:[[1,"d-flex","flex-column","bg-container"],[1,"title-text"],[1,"sub-title-text"],[1,"content-text"],[4,"ngIf"],[1,"d-flex","justify-content-end",2,"gap","8px","margin-top","16px"],[1,"cancel-btn",3,"ngStyle","click"],[1,"yes-btn",3,"ngStyle","click"],[4,"ngIf","ngIfElse"],["loading",""],[1,"d-flex","flex-column","align-items-center",2,"height","290px"],[1,"d-flex","align-items-center"],[1,"arrow-div",3,"ngStyle"],[1,"arrow-icon",3,"click"],[1,"bold-text"],[1,"d-flex","flex-container"],[1,"calendar-days","flex-container","flex-center"],["class","d-flex",4,"ngFor","ngForOf"],[1,"d-flex"],["class","calendar-days-of-month flex-container flex-center",3,"ngStyle","click",4,"ngFor","ngForOf"],[1,"calendar-days-of-month","flex-container","flex-center",3,"ngStyle","click"],[3,"placeholder","masterData","selectedValue","displayClose","onValueChange"],["diameter","20",1,"white-spinner"]],template:function(t,e){if(1&t&&(l["\u0275\u0275elementStart"](0,"div",0),l["\u0275\u0275elementStart"](1,"div",1),l["\u0275\u0275text"](2),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](3,"div",2),l["\u0275\u0275text"](4),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](5,"div",3),l["\u0275\u0275text"](6),l["\u0275\u0275elementEnd"](),l["\u0275\u0275template"](7,D,34,8,"ng-container",4),l["\u0275\u0275template"](8,_,2,5,"ng-container",4),l["\u0275\u0275elementStart"](9,"div",5),l["\u0275\u0275elementStart"](10,"div",6),l["\u0275\u0275listener"]("click",(function(){return e.onClose()})),l["\u0275\u0275text"](11," Cancel "),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementStart"](12,"div",7),l["\u0275\u0275listener"]("click",(function(){return e.onClickYes()})),l["\u0275\u0275template"](13,O,2,1,"ng-container",8),l["\u0275\u0275template"](14,I,1,0,"ng-template",null,9,l["\u0275\u0275templateRefExtractor"]),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"](),l["\u0275\u0275elementEnd"]()),2&t){const t=l["\u0275\u0275reference"](15);l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate"](e.data.title),l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate"](e.data.subTitle),l["\u0275\u0275advance"](2),l["\u0275\u0275textInterpolate"](e.data.content),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf","calendar"==e.data.type),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf","input-search"==e.data.type),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngStyle",l["\u0275\u0275pureFunction1"](9,P,e.isApiInProgress?"none":"")),l["\u0275\u0275advance"](2),l["\u0275\u0275property"]("ngStyle",l["\u0275\u0275pureFunction1"](11,P,e.isApiInProgress?"none":"")),l["\u0275\u0275advance"](1),l["\u0275\u0275property"]("ngIf",!e.isApiInProgress)("ngIfElse",t)}},directives:[p.NgIf,p.NgStyle,m.a,p.NgForOf,f.a,v.c],styles:[".bg-container[_ngcontent-%COMP%]{padding:24px}.bg-container[_ngcontent-%COMP%]   .title-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:18px;font-weight:700;color:#111434;margin-bottom:4px}.bg-container[_ngcontent-%COMP%]   .sub-title-text[_ngcontent-%COMP%]{font-size:12px;color:#8b95a5}.bg-container[_ngcontent-%COMP%]   .content-text[_ngcontent-%COMP%], .bg-container[_ngcontent-%COMP%]   .sub-title-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-weight:400;margin-bottom:12px}.bg-container[_ngcontent-%COMP%]   .content-text[_ngcontent-%COMP%]{font-size:14px;color:#6e7b8f;text-align:justify}.bg-container[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{border:1px solid #45546e;border-radius:8px;padding:11px 15px;color:#45546e}.bg-container[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%], .bg-container[_ngcontent-%COMP%]   .yes-btn[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:700;cursor:pointer}.bg-container[_ngcontent-%COMP%]   .yes-btn[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;background:linear-gradient(270deg,var(--atsprimaryColor),var(--atssecondaryColor) 105.29%);border-radius:8px;padding:0 12px;color:#fff}.bg-container[_ngcontent-%COMP%]   .arrow-div[_ngcontent-%COMP%]{width:18px;height:18px}.bg-container[_ngcontent-%COMP%]   .arrow-icon[_ngcontent-%COMP%]{color:#526179;font-size:18px;width:18px;height:18px;cursor:pointer;padding-top:3px}.bg-container[_ngcontent-%COMP%]   .bold-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);color:#5f6c81;font-size:16px;font-weight:700;width:210px;display:flex;justify-content:center}.bg-container[_ngcontent-%COMP%]   .calendar-days[_ngcontent-%COMP%]{color:#b9c0ca}.bg-container[_ngcontent-%COMP%]   .calendar-days[_ngcontent-%COMP%], .bg-container[_ngcontent-%COMP%]   .calendar-days-of-month[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:16px;font-weight:500;width:30px;height:30px;display:flex;justify-content:center;align-items:center;margin:4px}.bg-container[_ngcontent-%COMP%]   .calendar-days-of-month[_ngcontent-%COMP%]{border-radius:4px;background-color:#fff;cursor:pointer}.bg-container[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]{font-size:12px;font-family:var(--atsfontFamily)}.bg-container[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-form-field{font-size:12px!important;font-family:var(--atsfontFamily)!important}.bg-container[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-form-field-wrapper, .bg-container[_ngcontent-%COMP%]   .form-field-class[_ngcontent-%COMP%]     .mat-form-field .mat-form-field-wrapper{padding-bottom:0!important}  .white-spinner circle{stroke:#fff!important}"]}),t})()}}]);