(window.webpackJsonp=window.webpackJsonp||[]).push([[763],{"kgR+":function(e,t,i){"use strict";i.r(t),i.d(t,"TimesheetGeneralConfigRuleComponent",(function(){return j}));var r=i("mrSG"),n=i("bEYa"),o=i("XNiG"),l=i("1G5W"),s=i("fXoL"),a=i("3Pt+"),d=i("0IaG"),m=i("SC65"),c=i("XXEo"),p=i("1A3m"),h=i("BVzC"),u=i("Qu3c"),v=i("NFeN"),f=i("kmnG"),g=i("qFsG"),b=i("jtHE"),y=i("NJ67"),S=i("d3UM"),E=i("FKr1"),D=i("ofXK");const C=["allSelected"],x=["singleSelect"];function w(e,t){if(1&e&&(s["\u0275\u0275elementStart"](0,"mat-option",5),s["\u0275\u0275text"](1),s["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit;s["\u0275\u0275property"]("value",e.id),s["\u0275\u0275advance"](1),s["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let _=(()=>{class e extends y.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new a.j,this.fieldFilterCtrl=new a.j,this.list=[],this.required=!1,this.valueChange=new s.EventEmitter,this.disabled=!1,this.filteredList=new b.a,this._onDestroy=new o.b}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(l.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(l.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}toggleAllSelection(){this.allSelected.selected&&this.fieldCtrl.patchValue([])}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275directiveInject"](s.Renderer2))},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-multi-select-search-dropdown"]],viewQuery:function(e,t){if(1&e&&(s["\u0275\u0275viewQuery"](C,!0),s["\u0275\u0275viewQuery"](x,!0)),2&e){let e;s["\u0275\u0275queryRefresh"](e=s["\u0275\u0275loadQuery"]())&&(t.allSelected=e.first),s["\u0275\u0275queryRefresh"](e=s["\u0275\u0275loadQuery"]())&&(t.singleSelect=e.first)}},inputs:{list:"list",placeholder:"placeholder",required:"required",disabled:"disabled"},outputs:{valueChange:"valueChange"},features:[s["\u0275\u0275ProvidersFeature"]([{provide:a.t,useExisting:Object(s.forwardRef)(()=>e),multi:!0}]),s["\u0275\u0275InheritDefinitionFeature"],s["\u0275\u0275NgOnChangesFeature"]],decls:9,vars:9,consts:[["appearance","outline",2,"width","100%"],["multiple","",3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl"],[3,"value",4,"ngFor","ngForOf"],[3,"value"]],template:function(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"mat-form-field",0),s["\u0275\u0275elementStart"](1,"mat-label"),s["\u0275\u0275text"](2),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](3,"mat-select",1,2),s["\u0275\u0275elementStart"](5,"mat-option"),s["\u0275\u0275element"](6,"ngx-mat-select-search",3),s["\u0275\u0275elementEnd"](),s["\u0275\u0275template"](7,w,2,2,"mat-option",4),s["\u0275\u0275pipe"](8,"async"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e&&(s["\u0275\u0275advance"](2),s["\u0275\u0275textInterpolate"](t.placeholder),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),s["\u0275\u0275advance"](3),s["\u0275\u0275property"]("formControl",t.fieldFilterCtrl),s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("ngForOf",s["\u0275\u0275pipeBind1"](8,7,t.filteredList)))},directives:[f.c,f.g,S.c,a.v,a.k,a.F,E.p,D.NgForOf],pipes:[D.AsyncPipe],styles:[""]}),e})();var F=i("f0Cb"),O=i("bTqV");let j=(()=>{class e{constructor(e,t,i,r,n,l,s){this.fb=e,this.dialogRef=t,this.dialog=i,this._timesheetService=r,this._profileService=n,this._toasterSevice=l,this._errorService=s,this.entityData=[],this.divisionData=[],this.subDivisionData=[],this.employmentTypeData=[],this._onDestroy=new o.b,this.saveBtnDisable=!1}ngOnInit(){this.createForm(),this.getEntityData(),this.getDivisionData(),this.getSubDivisionData(),this.getEmploymentData(),this.currentUser=this._profileService.getProfile().profile}createForm(){this.ruleForm=this.fb.group({ruleName:["",n.e.required],entity:["",n.e.required],division:["",n.e.required],subDivision:["",n.e.required],employmentType:["",n.e.required]})}closeForm(){this.ruleForm.reset(),this.dialog.closeAll()}saveForm(){this.saveBtnDisable=!0,this.ruleForm.patchValue({ruleName:this.ruleForm.get("ruleName").value,entity:this.ruleForm.get("entity").value,division:this.ruleForm.get("division").value,subDivision:this.ruleForm.get("subDivision").value,employmentType:this.ruleForm.get("employmentType").value}),console.log(this.ruleForm.value),this._timesheetService.saveTimesheetRule(this.currentUser.aid,this.currentUser.oid,this.ruleForm.value).pipe(Object(l.a)(this._onDestroy)).subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){"S"==e.messType?(this.saveBtnDisable=!1,this._toasterSevice.showSuccess("Timesheet Configuration Message",e.messText,1e3),this.dialog.closeAll()):(this.saveBtnDisable=!1,this._toasterSevice.showInfo("Timesheet Configuration Message",e.messText,1e3),this.dialog.closeAll())})),e=>{console.log(e),this._errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Something Went Wrong, Kindly Try After Sometime ",e&&e.params?e.params:e&&e.error?e.error.params:{})})}getEntityData(){this._timesheetService.getEntityList().pipe(Object(l.a)(this._onDestroy)).subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){e&&e.data&&(this.entityData=e.data)})),e=>{console.log(e),this._errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Something Went Wrong, Kindly Try After Sometime ",e&&e.params?e.params:e&&e.error?e.error.params:{})})}getDivisionData(){this._timesheetService.getDivisionList().pipe(Object(l.a)(this._onDestroy)).subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){e&&e.data&&(this.divisionData=e.data)})),e=>{console.log(e),this._errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Something Went Wrong, Kindly Try After Sometime ",e&&e.params?e.params:e&&e.error?e.error.params:{})})}getSubDivisionData(){this._timesheetService.getSubDivisionList().pipe(Object(l.a)(this._onDestroy)).subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){e&&e.data&&(this.subDivisionData=e.data)})),e=>{console.log(e),this._errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Something Went Wrong, Kindly Try After Sometime ",e&&e.params?e.params:e&&e.error?e.error.params:{})})}getEmploymentData(){this._timesheetService.getEmploymentList().pipe(Object(l.a)(this._onDestroy)).subscribe(e=>Object(r.c)(this,void 0,void 0,(function*(){e&&e.data&&(this.employmentTypeData=e.data)})),e=>{console.log(e),this._errorService.userErrorAlert(e&&e.code?e.code:e&&e.error?e.error.code:"NIL","Something Went Wrong, Kindly Try After Sometime ",e&&e.params?e.params:e&&e.error?e.error.params:{})})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}}return e.\u0275fac=function(t){return new(t||e)(s["\u0275\u0275directiveInject"](a.i),s["\u0275\u0275directiveInject"](d.h),s["\u0275\u0275directiveInject"](d.b),s["\u0275\u0275directiveInject"](m.a),s["\u0275\u0275directiveInject"](c.a),s["\u0275\u0275directiveInject"](p.a),s["\u0275\u0275directiveInject"](h.a))},e.\u0275cmp=s["\u0275\u0275defineComponent"]({type:e,selectors:[["app-timesheet-general-config-rule"]],decls:47,vars:6,consts:[[1,"container-fluild","pl-2","pr-2","timesheet-general-config-rule"],[3,"formGroup"],[1,"row","p-0"],[1,"col-12","p-2"],[1,"row","border-bottom","solid"],[1,"col-8","pt-2","pb-2","pl-0","d-flex"],[1,"topHeader","my-auto","ml-2"],[1,"col-1","d-flex","pt-2","pb-2"],["matTooltip","Close",1,"view-button-inactive",3,"click"],[1,"row"],[1,"col-12","pl-2"],["appearance","outline"],["matInput","","type","text","maxlength","10","formControlName","ruleName","placeholder","Enter Rule Name","required",""],["formControlName","entity","placeholder","Select Entity",3,"list"],["formControlName","division","placeholder","Select Division",3,"list"],["formControlName","subDivision","placeholder","Select Sub Division",3,"list"],["formControlName","employmentType","placeholder","Select Employment Type",3,"list"],[1,"col-12","pl-1"],[1,"col-3","d-flex","pt-2"],["mat-button","",1,"cancel-btn",3,"click"],["mat-button","",1,"save-btn",3,"disabled","click"]],template:function(e,t){1&e&&(s["\u0275\u0275elementStart"](0,"div",0),s["\u0275\u0275elementStart"](1,"form",1),s["\u0275\u0275elementStart"](2,"div",2),s["\u0275\u0275elementStart"](3,"div",3),s["\u0275\u0275elementStart"](4,"div",4),s["\u0275\u0275elementStart"](5,"div",5),s["\u0275\u0275elementStart"](6,"span",6),s["\u0275\u0275text"](7,"Create New Rule"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](8,"div",7),s["\u0275\u0275elementStart"](9,"button",8),s["\u0275\u0275listener"]("click",(function(){return t.closeForm()})),s["\u0275\u0275elementStart"](10,"mat-icon"),s["\u0275\u0275text"](11,"close"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](12,"div",3),s["\u0275\u0275elementStart"](13,"div",9),s["\u0275\u0275elementStart"](14,"div",10),s["\u0275\u0275text"](15," Name "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](16,"div",10),s["\u0275\u0275elementStart"](17,"mat-form-field",11),s["\u0275\u0275element"](18,"input",12),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](19,"div",9),s["\u0275\u0275elementStart"](20,"div",10),s["\u0275\u0275text"](21," Entity "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](22,"div",10),s["\u0275\u0275element"](23,"app-multi-select-search-dropdown",13),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](24,"div",9),s["\u0275\u0275elementStart"](25,"div",10),s["\u0275\u0275text"](26," Division "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](27,"div",10),s["\u0275\u0275element"](28,"app-multi-select-search-dropdown",14),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](29,"div",9),s["\u0275\u0275elementStart"](30,"div",10),s["\u0275\u0275text"](31," Sub Division "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](32,"div",10),s["\u0275\u0275element"](33,"app-multi-select-search-dropdown",15),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](34,"div",9),s["\u0275\u0275elementStart"](35,"div",10),s["\u0275\u0275text"](36," Employment Type "),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](37,"div",10),s["\u0275\u0275element"](38,"app-multi-select-search-dropdown",16),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275element"](39,"mat-divider"),s["\u0275\u0275elementStart"](40,"div",17),s["\u0275\u0275elementStart"](41,"div",9),s["\u0275\u0275elementStart"](42,"div",18),s["\u0275\u0275elementStart"](43,"button",19),s["\u0275\u0275listener"]("click",(function(){return t.closeForm()})),s["\u0275\u0275text"](44,"Cancel"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementStart"](45,"button",20),s["\u0275\u0275listener"]("click",(function(){return t.saveForm()})),s["\u0275\u0275text"](46,"Save"),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"](),s["\u0275\u0275elementEnd"]()),2&e&&(s["\u0275\u0275advance"](1),s["\u0275\u0275property"]("formGroup",t.ruleForm),s["\u0275\u0275advance"](22),s["\u0275\u0275property"]("list",t.entityData),s["\u0275\u0275advance"](5),s["\u0275\u0275property"]("list",t.divisionData),s["\u0275\u0275advance"](5),s["\u0275\u0275property"]("list",t.subDivisionData),s["\u0275\u0275advance"](5),s["\u0275\u0275property"]("list",t.employmentTypeData),s["\u0275\u0275advance"](7),s["\u0275\u0275property"]("disabled",t.saveBtnDisable))},directives:[a.J,a.w,a.n,u.a,v.a,f.c,g.b,a.e,a.q,a.v,a.l,a.F,_,F.a,O.a],styles:[".timesheet-general-config-rule[_ngcontent-%COMP%]   .topHeader[_ngcontent-%COMP%]{font-family:Roboto;font-style:normal;font-weight:500;font-size:15px;line-height:16px;letter-spacing:.02em;text-transform:capitalize;color:#111434}.timesheet-general-config-rule[_ngcontent-%COMP%]   .cancel-btn[_ngcontent-%COMP%]{width:52px;height:35px;border:1px solid #45546e;border-radius:4px;padding-right:60px;margin-right:15px;font-family:Roboto}.timesheet-general-config-rule[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%]{width:42px;height:35px;background:#ee4961;border-radius:4px;color:#fff;font-family:Roboto}.timesheet-general-config-rule[_ngcontent-%COMP%]   .view-button-inactive[_ngcontent-%COMP%]{margin-top:5px!important;line-height:8px;width:50px;height:25px;margin-right:10px!important;border:none;background:#fff;color:#45546e;font-size:18px;font-weight:700}"]}),e})()}}]);