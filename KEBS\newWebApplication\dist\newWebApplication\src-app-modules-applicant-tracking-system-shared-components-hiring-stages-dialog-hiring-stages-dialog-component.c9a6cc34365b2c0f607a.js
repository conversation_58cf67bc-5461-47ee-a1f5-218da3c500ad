(window.webpackJsonp=window.webpackJsonp||[]).push([[938],{v5dV:function(t,e,n){"use strict";n.r(e),n.d(e,"HiringStagesDialogComponent",(function(){return g}));var i=n("0IaG"),o=n("fXoL"),a=n("NFeN"),l=n("ofXK"),r=n("lVl8"),c=n("yx4D");function p(t,e){if(1&t&&(o["\u0275\u0275elementStart"](0,"span",13),o["\u0275\u0275text"](1),o["\u0275\u0275elementEnd"]()),2&t){const t=o["\u0275\u0275nextContext"]().$implicit;o["\u0275\u0275advance"](1),o["\u0275\u0275textInterpolate2"]("",t.count," - ",t.name,"")}}function s(t,e){if(1&t&&(o["\u0275\u0275elementContainerStart"](0),o["\u0275\u0275elementStart"](1,"div",8),o["\u0275\u0275elementStart"](2,"div"),o["\u0275\u0275element"](3,"div",9),o["\u0275\u0275pipe"](4,"svgSecurityBypass"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](5,"div"),o["\u0275\u0275elementStart"](6,"span",10),o["\u0275\u0275text"](7),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275template"](8,p,2,2,"ng-template",11,12,o["\u0275\u0275templateRefExtractor"]),o["\u0275\u0275elementContainerEnd"]()),2&t){const t=e.$implicit,n=o["\u0275\u0275reference"](9),i=o["\u0275\u0275nextContext"]();o["\u0275\u0275advance"](1),o["\u0275\u0275property"]("tooltip",n)("options",i.hiringStageTooltipOptions),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("innerHTML",o["\u0275\u0275pipeBind1"](4,4,t.icon),o["\u0275\u0275sanitizeHtml"]),o["\u0275\u0275advance"](4),o["\u0275\u0275textInterpolate"](t.count)}}let g=(()=>{class t{constructor(t,e){this.data=t,this._dialogRef=e,this.hiringStageTooltipOptions={placement:"top"}}ngOnInit(){}onClose(){this._dialogRef.close()}}return t.\u0275fac=function(e){return new(e||t)(o["\u0275\u0275directiveInject"](i.a),o["\u0275\u0275directiveInject"](i.h))},t.\u0275cmp=o["\u0275\u0275defineComponent"]({type:t,selectors:[["app-hiring-stages-dialog"]],decls:11,vars:2,consts:[[1,"bg-container"],[1,"d-flex","align-items-center","justify-content-between"],[1,"title","p-0"],[1,"icon"],[1,"icon",3,"click"],[1,"sub-title"],[1,"d-flex","align-items-center","flex-wrap",2,"gap","41px"],[4,"ngFor","ngForOf"],["content-type","template",1,"d-flex","flex-column","align-items-center",2,"gap","8px",3,"tooltip","options"],[3,"innerHTML"],[1,"count-text"],["style","background-color: #111434"],["hiringStageTooltipContent",""],[1,"hiring-stage-tooltip"]],template:function(t,e){1&t&&(o["\u0275\u0275elementStart"](0,"div",0),o["\u0275\u0275elementStart"](1,"div",1),o["\u0275\u0275elementStart"](2,"div",2),o["\u0275\u0275text"](3,"Hiring Stages"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](4,"div",3),o["\u0275\u0275elementStart"](5,"mat-icon",4),o["\u0275\u0275listener"]("click",(function(){return e.onClose()})),o["\u0275\u0275text"](6,"close"),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](7,"div",5),o["\u0275\u0275text"](8),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementStart"](9,"div",6),o["\u0275\u0275template"](10,s,10,6,"ng-container",7),o["\u0275\u0275elementEnd"](),o["\u0275\u0275elementEnd"]()),2&t&&(o["\u0275\u0275advance"](8),o["\u0275\u0275textInterpolate1"]("",e.data.hiringStages.length," stages"),o["\u0275\u0275advance"](2),o["\u0275\u0275property"]("ngForOf",e.data.hiringStages))},directives:[a.a,l.NgForOf,r.a],pipes:[c.a],styles:[".bg-container[_ngcontent-%COMP%]{padding:16px}.bg-container[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:18px;font-weight:700;color:#111434}.bg-container[_ngcontent-%COMP%]   .sub-title[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:12px;font-weight:400;color:#8b95a5;margin-bottom:12px}.bg-container[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{width:18px;height:18px;font-size:18px;color:#1b2140;cursor:pointer}.bg-container[_ngcontent-%COMP%]   .count-text[_ngcontent-%COMP%]{font-family:var(--atsfontFamily);font-size:14px;font-weight:700;color:#1b2140}"]}),t})()}}]);