(window.webpackJsonp=window.webpackJsonp||[]).push([[761],{"/2Uq":function(e,t,i){"use strict";i.r(t),i.d(t,"TaskCreationComponent",(function(){return q}));var n=i("mrSG"),a=i("fXoL"),r=i("3Pt+"),s=i("xG9w"),o=i("wd/R"),l=i("0IaG"),c=i("ofXK"),d=i("jtHE"),p=i("XNiG"),h=i("NJ67"),u=i("1G5W"),m=i("kmnG"),g=i("d3UM"),f=i("FKr1"),v=i("WJ5W");const b=["singleSelect"];function y(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"mat-option",6),a["\u0275\u0275listener"]("click",(function(){a["\u0275\u0275restoreView"](e);const i=t.$implicit;return a["\u0275\u0275nextContext"]().emitChanges(i)})),a["\u0275\u0275text"](1),a["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit;a["\u0275\u0275property"]("value",e.id),a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate1"](" ",e.name," ")}}let S=(()=>{class e extends h.a{constructor(e){super(),this.renderer=e,this.fieldCtrl=new r.j,this.fieldFilterCtrl=new r.j,this.list=[],this.required=!1,this.valueChange=new a.EventEmitter,this.disabled=!1,this.filteredList=new d.a,this.change=new a.EventEmitter,this._onDestroy=new p.b,this.emitChanges=e=>{this.change.emit(e)}}ngOnInit(){this.fieldFilterCtrl.valueChanges.pipe(Object(u.a)(this._onDestroy)).subscribe(()=>{this.filterBanks()}),this.fieldCtrl.valueChanges.pipe(Object(u.a)(this._onDestroy)).subscribe(e=>{this.value=e,this.onChange(e),this.valueChange.emit(e)})}ngOnChanges(){this.filteredList.next(this.list.slice())}ngAfterViewInit(){}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}filterBanks(){if(!this.list)return;let e=this.fieldFilterCtrl.value;e?(e=e.toLowerCase(),this.filteredList.next(this.list.filter(t=>t.name.toLowerCase().indexOf(e)>-1))):this.filteredList.next(this.list.slice())}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){this.fieldCtrl.setValue(e)}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275directiveInject"](a.Renderer2))},e.\u0275cmp=a["\u0275\u0275defineComponent"]({type:e,selectors:[["app-input-search"]],viewQuery:function(e,t){if(1&e&&a["\u0275\u0275viewQuery"](b,!0),2&e){let e;a["\u0275\u0275queryRefresh"](e=a["\u0275\u0275loadQuery"]())&&(t.singleSelect=e.first)}},inputs:{list:"list",placeholder:"placeholder",required:"required",disabled:"disabled"},outputs:{valueChange:"valueChange",change:"change"},features:[a["\u0275\u0275ProvidersFeature"]([{provide:r.t,useExisting:Object(a.forwardRef)(()=>e),multi:!0}]),a["\u0275\u0275InheritDefinitionFeature"],a["\u0275\u0275NgOnChangesFeature"]],decls:11,vars:11,consts:[["appearance","outline"],[3,"formControl","placeholder","required","disabled"],["singleSelect",""],["noEntriesFoundLabel","No results found",3,"formControl","placeholderLabel"],[3,"value"],[3,"value","click",4,"ngFor","ngForOf"],[3,"value","click"]],template:function(e,t){1&e&&(a["\u0275\u0275elementStart"](0,"mat-form-field",0),a["\u0275\u0275elementStart"](1,"mat-label"),a["\u0275\u0275text"](2),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](3,"mat-select",1,2),a["\u0275\u0275elementStart"](5,"mat-option"),a["\u0275\u0275element"](6,"ngx-mat-select-search",3),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](7,"mat-option",4),a["\u0275\u0275text"](8,"None"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275template"](9,y,2,2,"mat-option",5),a["\u0275\u0275pipe"](10,"async"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e&&(a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate"](t.placeholder),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("formControl",t.fieldCtrl)("placeholder",t.placeholder)("required",t.required)("disabled",t.disabled),a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("formControl",t.fieldFilterCtrl)("placeholderLabel",t.placeholder),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("value",null),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("ngForOf",a["\u0275\u0275pipeBind1"](10,9,t.filteredList)))},directives:[m.c,m.g,g.c,r.v,r.k,r.F,f.p,v.a,c.NgForOf],pipes:[c.AsyncPipe],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}"]}),e})();var C=i("Kj3r"),w=i("F97M"),k=i("XVR1"),O=i("qFsG"),x=i("/1cH"),D=i("NFeN");function M(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"mat-label"),a["\u0275\u0275text"](1),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275nextContext"]();a["\u0275\u0275advance"](1),a["\u0275\u0275textInterpolate"](e.label)}}function T(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"mat-option",7),a["\u0275\u0275elementStart"](1,"small"),a["\u0275\u0275text"](2),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=t.$implicit,i=a["\u0275\u0275nextContext"]();a["\u0275\u0275property"]("hidden",!i.isAutocomplete)("value",e),a["\u0275\u0275advance"](2),a["\u0275\u0275textInterpolate2"]("",e.displayName," | ",e.mail,"")}}let E=(()=>{class e extends h.a{constructor(e,t){super(),this.graphApi=e,this._AppShareService=t,this.userSearchSubject=new p.b,this.isAutocomplete=!1,this.Formwidth="",this.showUserList=new a.EventEmitter,this.selectedUser=new a.EventEmitter,this.label="",this.blur=new a.EventEmitter,this.required=!1,this.fieldCtrl=new r.j,this.disabled=!1,this.isGraphApi=0,this._onDestroy=new p.b}ngOnInit(){this.userSearchSubject.pipe(Object(C.a)(600)).subscribe(e=>Object(n.c)(this,void 0,void 0,(function*(){if(console.log(e),this.isGraphApi)this.graphApi.getUserSuggestions(e).then(t=>Object(n.c)(this,void 0,void 0,(function*(){if(t&&t.length>0)this.showUserList.emit(t);else{let t=yield this.getUserSuggestionsFromDB(e);this.showUserList.emit(t)}})),t=>Object(n.c)(this,void 0,void 0,(function*(){console.log(t);let i=yield this.getUserSuggestionsFromDB(e);this.showUserList.emit(i)})));else{let t=yield this.getUserSuggestionsFromDB(e);this.showUserList.emit(t)}}))),this.fieldCtrl.valueChanges.pipe(Object(u.a)(this._onDestroy)).subscribe(e=>{console.log(e),e?(this.value=e.id,this.onChange(e.id)):(this.value="",this.onChange(""))})}ngOnDestroy(){this._onDestroy.next(),this._onDestroy.complete()}ngAfterViewInit(){}searchUser(e){if(!e)return this.graphApi.userSuggestions=[];this.userSearchSubject.next(e)}resetSuggestion(){this.graphApi.userSuggestions=[]}selectedOption(e){this.selectedUser.emit(e.option.value),this.value=e.option.value,this.blur.emit()}displayUserName(e){return e?e.displayName:""}setDisabledState(e){e?this.fieldCtrl.disable():this.fieldCtrl.enable()}writeValue(e){return Object(n.c)(this,void 0,void 0,(function*(){if(console.log("inisde writevalue"),console.log(e),e)if(this.isGraphApi)this.graphApi.getUserProfile(e).then(t=>Object(n.c)(this,void 0,void 0,(function*(){if(t)this.fieldCtrl.setValue(t);else{let t=yield this.getUserProfileFromDB(e);this.fieldCtrl.setValue(t)}})));else{let t=yield this.getUserProfileFromDB(e);console.log(t),this.fieldCtrl.setValue(t)}else this.fieldCtrl.setValue("")}))}getUserSuggestionsFromDB(e){return Object(n.c)(this,void 0,void 0,(function*(){let t=yield this._AppShareService.getUserSuggestionsFromDB(e);return this.graphApi.userSuggestions=t.value,t.value}))}getUserProfileFromDB(e){return Object(n.c)(this,void 0,void 0,(function*(){let t=yield this._AppShareService.getUserProfileFromDB(e);return console.log(t),t&&t.length>0?t[0]:""}))}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275directiveInject"](w.a),a["\u0275\u0275directiveInject"](k.a))},e.\u0275cmp=a["\u0275\u0275defineComponent"]({type:e,selectors:[["app-search-user"]],inputs:{isAutocomplete:"isAutocomplete",Formwidth:"Formwidth",label:"label",required:"required",disabled:"disabled",isGraphApi:"isGraphApi"},outputs:{showUserList:"showUserList",selectedUser:"selectedUser",blur:"blur"},features:[a["\u0275\u0275ProvidersFeature"]([{provide:r.t,useExisting:Object(a.forwardRef)(()=>e),multi:!0}]),a["\u0275\u0275InheritDefinitionFeature"]],decls:9,vars:9,consts:[["appearance","outline","width","Formwidth"],[4,"ngIf"],["matInput","",3,"matAutocomplete","placeholder","required","formControl","disabled","keyup","focus"],["matSuffix","",2,"color","#66615B !important","font-size","21px !important"],[3,"hidden","displayWith","optionSelected"],["auto","matAutocomplete"],[3,"hidden","value",4,"ngFor","ngForOf"],[3,"hidden","value"]],template:function(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"div"),a["\u0275\u0275elementStart"](1,"mat-form-field",0),a["\u0275\u0275template"](2,M,2,1,"mat-label",1),a["\u0275\u0275elementStart"](3,"input",2),a["\u0275\u0275listener"]("keyup",(function(e){return t.searchUser(e.target.value)}))("focus",(function(){return t.resetSuggestion()})),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](4,"mat-icon",3),a["\u0275\u0275text"](5,"person_pin"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](6,"mat-autocomplete",4,5),a["\u0275\u0275listener"]("optionSelected",(function(e){return t.selectedOption(e)})),a["\u0275\u0275template"](8,T,3,4,"mat-option",6),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275reference"](7);a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("ngIf",t.label),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("matAutocomplete",e)("placeholder",t.label)("required",t.required)("formControl",t.fieldCtrl)("disabled",t.disabled),a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("hidden",!t.isAutocomplete)("displayWith",t.displayUserName),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("ngForOf",t.graphApi.userSuggestions)}},directives:[m.c,c.NgIf,O.b,x.d,r.e,r.F,r.v,r.k,D.a,m.i,x.b,c.NgForOf,m.g,f.p],styles:[".mat-form-field[_ngcontent-%COMP%]{width:100%}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-wrapper{padding-bottom:8px!important}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-underline{bottom:0}.mat-form-field[_ngcontent-%COMP%]     .mat-form-field-infix{padding:.65em 0!important;border-top:.54375em solid transparent!important;font-size:13px}.mat-form-field[_ngcontent-%COMP%]     .mat-select-arrow{margin:0 4px!important}"]}),e})();var _=i("dNgK"),F=i("bTqV"),I=i("iadO"),j=(i("we1Z"),i("Qu3c")),P=(i("lVl8"),i("9044")),A=i("Tzv/"),G=i("LcQX"),U=i("ihCf");function V(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"button",33),a["\u0275\u0275listener"]("click",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"]().createTask()})),a["\u0275\u0275elementStart"](1,"mat-icon"),a["\u0275\u0275text"](2," done_all"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()}}function B(e,t){if(1&e){const e=a["\u0275\u0275getCurrentView"]();a["\u0275\u0275elementStart"](0,"button",34),a["\u0275\u0275listener"]("click",(function(){return a["\u0275\u0275restoreView"](e),a["\u0275\u0275nextContext"]().updateTask()})),a["\u0275\u0275elementStart"](1,"mat-icon"),a["\u0275\u0275text"](2," done_all"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()}}const N=function(e,t){return{"btn-active":e,"btn-not-active":t}};let q=(()=>{class e{constructor(e,t,i,s,l,c,d,p){this.dialogRef=e,this.dialogData=t,this.dialog=i,this.fb=s,this.snackBar=l,this.opportunityService=c,this.reloadService=d,this.utilityService=p,this.close=new a.EventEmitter,this.flag=!1,this.submitted=!0,this.is_duplicate=!1,this.currentUser=this.opportunityService.currentUser.oid,this.taskCreationForm=this.fb.group({phase:["",r.H.required],title:["",r.H.required],description:[""],predecessorActivityId:[""],governanceType:[""],salesGovernanceType:[""],endDate:["",r.H.required],newDueOn:[""],contactedBy:["",r.H.required],applicationName:["Opportunities",r.H.required],applicationReferenceId:[parseInt(window.location.pathname.split("/")[5]),r.H.required],effort_percent:[""]}),this.checkWhetherDueOnTodaysDateOrTommorowsDate=e=>{let t=new Date(this.taskCreationForm.value.endDate).getFullYear(),i=new Date(this.taskCreationForm.value.endDate).getDate(),n=new Date(this.taskCreationForm.value.endDate).getMonth();if("today"==e){if(this.taskCreationForm.value.endDate){let e=(new Date).getFullYear();return i+"-"+n+"-"+t==(new Date).getDate()+"-"+(new Date).getMonth()+"-"+e}return!1}if("tommorrow"==e){if(this.taskCreationForm.value.endDate){const e=new Date,a=new Date(e);a.setDate(a.getDate()+1);let r=a.getFullYear();return i+"-"+n+"-"+t==a.getDate()+"-"+a.getMonth()+"-"+r}return!1}},this.fillUpDueOn=e=>{if("today"===e)this.taskCreationForm.patchValue({endDate:new Date});else if("tommorow"===e){const e=new Date,t=new Date(e);t.setDate(t.getDate()+1),this.taskCreationForm.patchValue({endDate:t})}},this.updateTask=()=>{this.submitted=!1,this.taskCreationForm.valid?o(this.taskCreationForm.get("endDate").value).isSame(o(this.taskCreationForm.get("newDueOn").value))?this.opportunityService.editOppTask(this.activityId,this.taskCreationForm.value,!1).subscribe(e=>{console.log(e),this.snackBar.open("Task Updation successfully!","Dismiss",{duration:2e3}),this.dialogRef.close("update required"),this.taskCreationForm.reset(),this.submitted=!0},e=>{console.error(e),this.snackBar.open("Task updation failed!","Dismiss",{duration:2e3}),this.submitted=!0}):this.utilityService.openConfirmationForUpdatingAllOpportunityActivities("Do you want to update dates for Dependant Activities?").then(e=>Object(n.c)(this,void 0,void 0,(function*(){"All"==e?(console.log(e),this.opportunityService.editOppTask(this.activityId,this.taskCreationForm.value,!0).subscribe(e=>{console.log(e),this.snackBar.open("Task Updation successfully!","Dismiss",{duration:2e3}),this.dialogRef.close("update required"),this.taskCreationForm.reset(),this.submitted=!0},e=>{console.error(e),this.snackBar.open("Task updation failed!","Dismiss",{duration:2e3}),this.submitted=!0})):"Single"==e&&(console.log(e),this.opportunityService.editOppTask(this.activityId,this.taskCreationForm.value,!1).subscribe(e=>{console.log(e),this.snackBar.open("Task Updation successfully!","Dismiss",{duration:2e3}),this.dialogRef.close("update required"),this.taskCreationForm.reset(),this.submitted=!0},e=>{console.error(e),this.snackBar.open("Task updation failed!","Dismiss",{duration:2e3}),this.submitted=!0}))}))):(this.snackBar.open("Enter all Mandatory Fields!","Dismiss",{duration:2e3}),this.submitted=!0)},this.getPhase=()=>(console.log("I've been called"),new Promise((e,t)=>{this.opportunityService.getActivityPhase(this.opportunityId).subscribe(t=>{e(t)},e=>{throw console.error(e),e})})),this.getGovernanceTypes=()=>(console.log("*********************GOV*****************"),new Promise((e,t)=>{this.opportunityService.getGovernanceTypes(66).then(t=>{console.log(t),e(t)},e=>{throw console.error(e),e})})),this.getSalesGovernanceTypes=()=>new Promise((e,t)=>{this.opportunityService.getSalesGovernanceTypes(72).then(t=>{this.salesGovernanceTypes=t,console.log(this.salesGovernanceTypes),e(t)},e=>{throw console.error(e),e})}),this.getMarketingGovernanceTypes=()=>new Promise((e,t)=>{this.opportunityService.getSalesGovernanceTypes(74).then(t=>{this.marketingGovernanceTypes=t,e(t)},e=>{throw console.error(e),e})}),this.getAllActivityList=()=>new Promise((e,t)=>{this.opportunityService.getAllActivityList(this.opportunityId,this.flag,this.activityId).subscribe(t=>{console.log(this.activityList),this.activityList=t,e(t)},e=>{throw console.error(e),e})})}ngOnInit(){return Object(n.c)(this,void 0,void 0,(function*(){if(console.log(this.dialogData),this.mode=this.dialogData.mode,this.activityId=this.dialogData.activityId,"Duplicate"==this.mode?(this.dup_mode="Create",this.mode="Edit",this.is_duplicate=!0):"Edit"==this.mode?(this.dup_mode="Edit",this.mode="Edit"):"Create"==this.mode&&(this.dup_mode="Create",this.mode="Create"),this.opportunityId=parseInt(window.location.pathname.split("/")[5]),console.log(this.opportunityId),this.phase=yield this.getPhase(),this.governanceTypes=yield this.getGovernanceTypes(),this.salesGovernanceTypes=yield this.getSalesGovernanceTypes(),this.marketingGovernanceTypes=yield this.getMarketingGovernanceTypes(),this.activityList=yield this.getAllActivityList(),"Edit"==this.mode&&(this.data=this.dialogData.data,this.opportunityId=parseInt(this.data.application_reference_id),this.flag=!0,this.activityId=this.data.activity_id,this.activityList=yield this.getAllActivityList(),console.log("PrevDate",this.data.task_due_date),this.taskCreationForm.patchValue({phase:this.data.phase_id?this.data.phase_id:"",title:this.data.title,description:this.data.description,endDate:o(this.data.task_due_date),newDueOn:o(this.data.task_due_date),contactedBy:this.data.assigned_to,applicationName:"Opportunities",governanceType:this.data.governance_activity_id,salesGovernanceType:this.data.governance_activity_id,predecessorActivityId:this.data.predecessor_id,applicationReferenceId:this.opportunityId,effort_percent:this.data.effort_percent})),"Create"==this.mode){let e=s.where(this.phase,{name:"Logs"});this.taskCreationForm.patchValue({phase:e.length>0?e[0].id:null,contactedBy:this.currentUser})}else this.flag=!1,this.taskCreationForm.reset(),setTimeout(()=>{},1e3)}))}ngOnChanges(){return Object(n.c)(this,void 0,void 0,(function*(){}))}createTask(){this.submitted=!1,this.taskCreationForm.patchValue({applicationName:"Opportunities",applicationReferenceId:this.opportunityId});let e=s.where(this.dialogData.phaseDesc,{phaseId:this.taskCreationForm.value.phase});if(0!=e.length&&o(e[0].taskDueDate).format()<o(this.taskCreationForm.value.endDate).format())return this.snackBar.open("Task due date is greater than phase due date","Dismiss",{duration:3e3}),void(this.submitted=!0);console.log("Task",this.taskCreationForm.value),this.taskCreationForm.valid?this.opportunityService.createTask(this.taskCreationForm.value,this.is_duplicate).subscribe(e=>{console.log(e),this.snackBar.open("Task created successfully!","Dismiss",{duration:2e3}),this.taskCreationForm.reset(),this.dialogRef.close("update required"),this.submitted=!0},e=>{console.log(e),this.snackBar.open("Task creation failed!","Dismiss",{duration:2e3}),this.submitted=!0}):(this.snackBar.open("Enter all Mandatory Fields!","Dismiss",{duration:2e3}),this.submitted=!0)}closeDialog(){this.dialogRef.close()}}return e.\u0275fac=function(t){return new(t||e)(a["\u0275\u0275directiveInject"](l.h),a["\u0275\u0275directiveInject"](l.a),a["\u0275\u0275directiveInject"](l.b),a["\u0275\u0275directiveInject"](r.i),a["\u0275\u0275directiveInject"](_.a),a["\u0275\u0275directiveInject"](P.a),a["\u0275\u0275directiveInject"](A.a),a["\u0275\u0275directiveInject"](G.a))},e.\u0275cmp=a["\u0275\u0275defineComponent"]({type:e,selectors:[["app-task-creation"]],outputs:{close:"close"},features:[a["\u0275\u0275NgOnChangesFeature"]],decls:57,vars:19,consts:[[3,"formGroup"],[1,"container","createMailStyles"],[1,"row","border-bottom","solid"],[1,"col-11","pt-2","createAccount"],[1,"col-1","d-flex"],["mat-icon-button","",1,"ml-auto","close-button",3,"click"],[1,"close-Icon"],[1,"row",2,"margin-top","10px"],[1,"col-12"],[1,"row"],[1,"col-9","pl-0"],["required","true","placeholder","Phase","formControlName","phase",1,"create-account-field",3,"list"],["placeholder","Presales Governance Activity","formControlName","governanceType",1,"create-account-field",3,"list"],["placeholder","Sales Governance Activity","formControlName","governanceType",1,"create-account-field",3,"list"],["placeholder","Marketing Governance Activity","formControlName","governanceType",1,"create-account-field",3,"list"],["appearance","outline",1,"create-account-field"],["matInput","","placeholder","title *","formControlName","title"],["appearance","outline",2,"width","100%","font-size","14px !important"],["matInput","","cdkTextareaAutosize","","cdkAutosizeMinRows","2","cdkAutosizeMaxRows","","placeholder","Description","formControlName","description"],["autosize","cdkTextareaAutosize"],[1,"col-5","pl-0"],["matInput","","required","true","formControlName","endDate",3,"matDatepicker"],["matSuffix","",3,"for"],["picker0",""],[1,"col-4","ml-2","pt-2"],["mat-raised-button","","color","primary",2,"margin-left","2%",3,"ngClass","click"],[1,"col-9","pl-0","organizer"],["label","Owner","required","true","formControlName","contactedBy",3,"isAutocomplete"],[1,"row","pt-5"],[1,"col-9","pr-5","quotes"],[1,"col-3","pl-0"],["mat-icon-button","","class","iconbtn ml-2 mt-0","matTooltip","Create Task","type","submit",3,"click",4,"ngIf"],["mat-icon-button","","class","iconbtn ml-2 mt-0","matTooltip","Edit Task","type","submit",3,"click",4,"ngIf"],["mat-icon-button","","matTooltip","Create Task","type","submit",1,"iconbtn","ml-2","mt-0",3,"click"],["mat-icon-button","","matTooltip","Edit Task","type","submit",1,"iconbtn","ml-2","mt-0",3,"click"]],template:function(e,t){if(1&e&&(a["\u0275\u0275elementStart"](0,"form",0),a["\u0275\u0275elementStart"](1,"div",1),a["\u0275\u0275elementStart"](2,"div",2),a["\u0275\u0275elementStart"](3,"div",3),a["\u0275\u0275text"](4),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](5,"div",4),a["\u0275\u0275elementStart"](6,"button",5),a["\u0275\u0275listener"]("click",(function(){return t.closeDialog()})),a["\u0275\u0275elementStart"](7,"mat-icon",6),a["\u0275\u0275text"](8,"close"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](9,"div",7),a["\u0275\u0275elementStart"](10,"div",8),a["\u0275\u0275elementStart"](11,"div",9),a["\u0275\u0275elementStart"](12,"div",10),a["\u0275\u0275element"](13,"app-input-search",11),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](14,"div",9),a["\u0275\u0275elementStart"](15,"div",10),a["\u0275\u0275element"](16,"app-input-search",12),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](17,"div",9),a["\u0275\u0275elementStart"](18,"div",10),a["\u0275\u0275element"](19,"app-input-search",13),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](20,"div",9),a["\u0275\u0275elementStart"](21,"div",10),a["\u0275\u0275element"](22,"app-input-search",14),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](23,"div",9),a["\u0275\u0275elementStart"](24,"div",10),a["\u0275\u0275elementStart"](25,"mat-form-field",15),a["\u0275\u0275elementStart"](26,"mat-label"),a["\u0275\u0275text"](27,"Title"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275element"](28,"input",16),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](29,"div",9),a["\u0275\u0275elementStart"](30,"div",10),a["\u0275\u0275elementStart"](31,"mat-form-field",17),a["\u0275\u0275element"](32,"textarea",18,19),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](34,"div",9),a["\u0275\u0275elementStart"](35,"div",20),a["\u0275\u0275elementStart"](36,"mat-form-field",15),a["\u0275\u0275elementStart"](37,"mat-label"),a["\u0275\u0275text"](38,"Due on"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275element"](39,"input",21),a["\u0275\u0275element"](40,"mat-datepicker-toggle",22),a["\u0275\u0275element"](41,"mat-datepicker",null,23),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](43,"div",24),a["\u0275\u0275elementStart"](44,"button",25),a["\u0275\u0275listener"]("click",(function(){return t.fillUpDueOn("today")})),a["\u0275\u0275text"](45," Today"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](46,"button",25),a["\u0275\u0275listener"]("click",(function(){return t.fillUpDueOn("tommorow")})),a["\u0275\u0275text"](47," Tommorow"),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](48,"div",9),a["\u0275\u0275elementStart"](49,"div",26),a["\u0275\u0275element"](50,"app-search-user",27),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](51,"div",28),a["\u0275\u0275elementStart"](52,"div",29),a["\u0275\u0275text"](53,' "What we dwell is, what we become" '),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementStart"](54,"div",30),a["\u0275\u0275template"](55,V,3,0,"button",31),a["\u0275\u0275template"](56,B,3,0,"button",32),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"](),a["\u0275\u0275elementEnd"]()),2&e){const e=a["\u0275\u0275reference"](42);a["\u0275\u0275property"]("formGroup",t.taskCreationForm),a["\u0275\u0275advance"](4),a["\u0275\u0275textInterpolate1"]("",t.dup_mode," Task "),a["\u0275\u0275advance"](9),a["\u0275\u0275property"]("list",t.phase),a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("list",t.governanceTypes),a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("list",t.salesGovernanceTypes),a["\u0275\u0275advance"](3),a["\u0275\u0275property"]("list",t.marketingGovernanceTypes),a["\u0275\u0275advance"](17),a["\u0275\u0275property"]("matDatepicker",e),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("for",e),a["\u0275\u0275advance"](4),a["\u0275\u0275property"]("ngClass",a["\u0275\u0275pureFunction2"](13,N,1==t.checkWhetherDueOnTodaysDateOrTommorowsDate("today"),0==t.checkWhetherDueOnTodaysDateOrTommorowsDate("today"))),a["\u0275\u0275advance"](2),a["\u0275\u0275property"]("ngClass",a["\u0275\u0275pureFunction2"](16,N,1==t.checkWhetherDueOnTodaysDateOrTommorowsDate("tommorrow"),0==t.checkWhetherDueOnTodaysDateOrTommorowsDate("tommorrow"))),a["\u0275\u0275advance"](4),a["\u0275\u0275property"]("isAutocomplete",!0),a["\u0275\u0275advance"](5),a["\u0275\u0275property"]("ngIf","Edit"!=t.dup_mode&&t.submitted),a["\u0275\u0275advance"](1),a["\u0275\u0275property"]("ngIf","Edit"==t.dup_mode&&t.submitted)}},directives:function(){return[r.J,r.w,r.n,F.a,D.a,S,r.F,r.v,r.l,m.c,m.g,O.b,r.e,U.b,I.g,I.i,m.i,I.f,c.NgClass,E,c.NgIf,j.a]},styles:[".createMailStyles[_ngcontent-%COMP%]{background-image:url(createTask.757ceefab6f4ac37edbb.png);background-size:212px 161px;background-repeat:no-repeat;overflow-y:hidden!important;background-position:100% 60%;min-height:95vh}.createMailStyles[_ngcontent-%COMP%]   .side-title[_ngcontent-%COMP%]{color:#cf0001;font-size:14px}.createMailStyles[_ngcontent-%COMP%]   .field-invalid[_ngcontent-%COMP%]{font-size:small}.createMailStyles[_ngcontent-%COMP%]   .btn-active[_ngcontent-%COMP%]{background-color:#cf0001;color:#fff;font-weight:400;font-size:13px!important;min-width:60px;line-height:33px;padding:0 8px;border-radius:4px;margin-right:6px!important;margin-bottom:3px}.createMailStyles[_ngcontent-%COMP%]   .organizer[_ngcontent-%COMP%]     .mat-form-field-wrapper .mat-form-field-flex{height:42px}.createMailStyles[_ngcontent-%COMP%]   .btn-not-active[_ngcontent-%COMP%]{margin-right:6px!important;margin-bottom:3px;color:rgba(0,0,0,.534);background-color:rgba(194,193,193,.24);font-weight:400;font-size:13px!important;min-width:60px;line-height:33px;padding:0 8px;border-radius:4px}.createMailStyles[_ngcontent-%COMP%]   .createAccount[_ngcontent-%COMP%]{font-size:14px;color:#cf0001;font-weight:500}.createMailStyles[_ngcontent-%COMP%]   .create-account-field[_ngcontent-%COMP%]{font-size:14px!important;width:100%!important;color:#66615b}.createMailStyles[_ngcontent-%COMP%]   .inputSearch[_ngcontent-%COMP%]     .mat-form-field-appearance-outline .mat-form-field-flex{padding:0 .75em;margin-top:-.25em;position:relative;height:41px;width:204px}.createMailStyles[_ngcontent-%COMP%]   .close-Icon[_ngcontent-%COMP%]{font-size:18px!important;color:#4d4d4b!important}.createMailStyles[_ngcontent-%COMP%]   .close-button[_ngcontent-%COMP%]{width:38px!important;height:38px!important;line-height:38px!important}.createMailStyles[_ngcontent-%COMP%]   .iconbtn[_ngcontent-%COMP%]{background-color:#c92020;color:#fff;padding:0;box-shadow:0 3px 5px -1px rgba(0,0,0,.2),0 6px 10px 0 rgba(0,0,0,.14),0 1px 18px 0 rgba(0,0,0,.12)}.createMailStyles[_ngcontent-%COMP%]   .check-box-text[_ngcontent-%COMP%]{color:#66615b;font-size:14px}.createMailStyles[_ngcontent-%COMP%]   .vip-contact[_ngcontent-%COMP%]{color:#1a1a1a;font-size:14px}.createMailStyles[_ngcontent-%COMP%]   .quotes[_ngcontent-%COMP%]{font-style:italic;font-weight:500;color:#66615b;font-size:14px}.createMailStyles[_ngcontent-%COMP%]   .textArea[_ngcontent-%COMP%]{color:#66615b}"]}),e})()},"Tzv/":function(e,t,i){"use strict";i.d(t,"a",(function(){return o}));var n=i("mrSG"),a=i("XNiG"),r=i("2Vo4"),s=i("fXoL");let o=(()=>{class e{constructor(){this.reloadSubject=new a.b,this.pipelineSubject=new a.b,this.searchRetainSubject=new r.a(""),this.searchData=this.searchRetainSubject.asObservable(),this.salesGovernanceTypeSubject=new r.a([]),this.salesReportGovernanceTypeSubject=new r.a([]),this.salesProposalTypeSubject=new a.b,this.bidManagerSearchSubject=new r.a(""),this.globalBidManagerSearchData=this.bidManagerSearchSubject.asObservable(),this.bidManagerSearchClear=new r.a(!1),this.bidManagerSearchClearFlag=this.bidManagerSearchSubject.asObservable(),this.salesGovernanceSearchSubject=new r.a(""),this.bidManagerUserVariants=new r.a([]),this.refreshVariant=new a.b}sendNotification(e){console.log(e),this.reloadSubject.next(e)}getNotification(){return this.reloadSubject.asObservable()}sendPiplelineContent(e){console.log(e),this.pipelineSubject.next(e)}getPiplelineContent(){return this.pipelineSubject.asObservable()}getRetainSearchContent(){return this.searchData}sendRetainSearchContent(e){console.log(e),this.searchRetainSubject.next(e)}sendGovTypes(e){console.log(e),this.salesGovernanceTypeSubject.next(e)}getGovTypes(){return this.salesGovernanceTypeSubject.asObservable()}getGovTypesValue(){return Object(n.c)(this,void 0,void 0,(function*(){return this.salesGovernanceTypeSubject.getValue()}))}sendSalesGovTypes(e){console.log(e),this.salesReportGovernanceTypeSubject.next(e)}getSalesGovTypes(){return this.salesReportGovernanceTypeSubject.asObservable()}getSalesGovTypesValue(){return Object(n.c)(this,void 0,void 0,(function*(){return this.salesReportGovernanceTypeSubject.getValue()}))}sendProposalFilter(e){console.log(e),this.pipelineSubject.next(e)}getProposalFilter(){return this.pipelineSubject.asObservable()}sendBidManagerSearch(e){console.log(e),this.bidManagerSearchSubject.next(e)}getBidManagerSearchValue(){return this.globalBidManagerSearchData}sendClearBidManagerSearch(e){this.bidManagerSearchClear.next(e)}getClearBidManagerSearch(){return this.bidManagerSearchClear.asObservable()}getClearBidManagerSearchValue(){return this.bidManagerSearchClear.getValue()}sendBidManagerUserVariants(e){this.bidManagerUserVariants.next(e)}getBidManagerUserVariants(){return this.bidManagerUserVariants.asObservable()}refreshBidManagerUserVariants(){return this.refreshVariant.asObservable()}sendRefreshFlag(e){this.refreshVariant.next(e)}}return e.\u0275fac=function(t){return new(t||e)},e.\u0275prov=s["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})()}}]);