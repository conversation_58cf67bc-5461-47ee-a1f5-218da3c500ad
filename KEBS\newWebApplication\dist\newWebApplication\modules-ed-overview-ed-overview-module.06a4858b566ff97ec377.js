(window.webpackJsonp=window.webpackJsonp||[]).push([[811],{"/McB":function(e,t,i){"use strict";i.r(t),i.d(t,"EdOverviewModule",(function(){return M}));var n=i("ofXK"),o=i("tyNb"),s=i("mrSG"),a=i("xG9w"),l=i("1G5W"),r=i("XNiG"),d=i("33Jv"),c=i("fXoL"),h=i("1A3m"),m=i("jAlA"),p=i("XXEo"),u=i("Wp6s"),g=i("NFeN"),v=i("Qu3c");function f(e,t){if(1&e&&(c["\u0275\u0275elementStart"](0,"div",9),c["\u0275\u0275elementStart"](1,"mat-icon",10),c["\u0275\u0275text"](2),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](3,"span",11),c["\u0275\u0275text"](4),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e){const e=c["\u0275\u0275nextContext"]().$implicit;c["\u0275\u0275advance"](2),c["\u0275\u0275textInterpolate"](e.matIcon),c["\u0275\u0275advance"](1),c["\u0275\u0275propertyInterpolate"]("matTooltip",e.name),c["\u0275\u0275advance"](1),c["\u0275\u0275textInterpolate"](e.name)}}const b=function(e,t){return{display:e,"pointer-events":t}};function I(e,t){if(1&e){const e=c["\u0275\u0275getCurrentView"]();c["\u0275\u0275elementStart"](0,"div",7),c["\u0275\u0275listener"]("click",(function(){c["\u0275\u0275restoreView"](e);const i=t.$implicit;return c["\u0275\u0275nextContext"]().handleModuleSelection(i)})),c["\u0275\u0275template"](1,f,5,3,"div",8),c["\u0275\u0275elementEnd"]()}if(2&e){const e=t.$implicit,i=c["\u0275\u0275nextContext"]();c["\u0275\u0275property"]("ngClass",e.isSelected?"module-tile-selected row":"module-tile row")("ngStyle",c["\u0275\u0275pureFunction2"](3,b,e.isVisible?"revert":"none",i.disableNavigation?"none":"visible")),c["\u0275\u0275advance"](1),c["\u0275\u0275property"]("ngIf",e.isVisible)}}let w=(()=>{class e{constructor(e,t,i,n,s){this._router=e,this._route=t,this._toaster=i,this._edService=n,this._loginService=s,this.featureModuleList=[{id:1,name:"About",path:"profile",isSelected:!1,matIcon:"person_filled",isVisible:!0,objectId:179},{id:2,name:"Organisation",path:"organisation",isSelected:!1,matIcon:"account_tree",isVisible:!0,objectId:182},{id:3,name:"Project Details",path:"projectDetails",isSelected:!1,matIcon:"business_center",isVisible:!0,objectId:194},{id:4,name:"Time & Leaves",path:"timeAndLeaves",isSelected:!1,matIcon:"watch_later",isVisible:!1,objectId:187},{id:5,name:"Benefits",path:"benefits",isSelected:!1,matIcon:"redeem",isVisible:!1,objectId:196},{id:6,name:"Awards",path:"awards",isSelected:!1,matIcon:"emoji_events",isVisible:!1,objectId:202},{id:7,name:"Vaccination Details",path:"vaccinationDetails",isSelected:!1,matIcon:"vaccines",isVisible:!1,objectId:200},{id:8,name:"Background Verification",path:"backgroundVerification",isSelected:!1,matIcon:"policy",isVisible:!1,objectId:198},{id:9,name:"Documents",path:"hrDocuments",isSelected:!1,matIcon:"receipt_long",isVisible:!1,objectId:204},{id:11,name:"Payroll Details",path:"payroll",isSelected:!1,matIcon:"payments",isVisible:!1,objectId:345},{id:10,name:"Policies / Guidelines",path:"edTermsAndCondition",isSelected:!1,matIcon:"gavel",isVisible:!1,objectId:252},{id:12,name:"TimeSheet",path:"timesheet",isSelected:!1,matIcon:"timer",isVisible:!1,objectId:400},{id:13,name:"Awards",path:"pms/awards",isSelected:!1,matIcon:"assessment",isVisible:!1,objectId:401},{id:14,name:"PMS",path:"pms",isSelected:!1,matIcon:"assessment",isVisible:!1,objectId:402},{id:15,name:"Expenses",path:"expenses",isSelected:!1,matIcon:"account_balance_wallet",isVisible:!1,objectId:403},{id:16,name:"Help Desk",path:"lcdp/251/Help Desk/e9fa1ebd-5f4c-4319-a5bd-25f8f7bfc73e",isSelected:!1,matIcon:"collections_bookmark",isVisible:!1,objectId:404},{id:17,name:"Pay Roll",path:"p&t",isSelected:!1,matIcon:"payments",isVisible:!1,objectId:405},{id:18,name:"Visa & Travel",path:"lcdp/280/Visa and Travel/29eba4ec-85f2-4ae8-bbe6-faf1bff54afb",isSelected:!1,matIcon:"flight",isVisible:!1,objectId:406},{id:19,name:"LMS Certification",path:"lms-learner",isSelected:!1,matIcon:"school",isVisible:!1,objectId:407},{id:20,name:"OKR",path:"okr",isSelected:!1,matIcon:"golf_course",isVisible:!1,objectId:408}],this._onDestroy=new r.b,this.subs=new d.a,this.checkTermsAndConditionAccess=!1,this.isFromMyProfile=!1,this.disableNavigation=!1,this._router.events.subscribe(e=>{if(e instanceof o.c){let e=this._route.snapshot.firstChild.routeConfig.path;this.featureModuleList.forEach((t,i)=>{t.isSelected=t.path==e})}})}ngOnInit(){return Object(s.c)(this,void 0,void 0,(function*(){this.associateId=parseInt(this._router.url.split("/")[4]),this.applicationRoleAccessList=this._edService.getAllRoleAccess(),this.isFromMyProfile=this._loginService.getProfile().profile.aid==this.associateId,yield this.getEmployeeNameById(this.associateId),this.isFromMyProfile&&(this.checkTermsAndConditionAccess=yield this.CheckTermsAndConditionStatus()),this.initModuleSelection(),this.resolveSubscriptions()}))}resolveSubscriptions(){this._edService.getIsTermsAndCoditionAgreedActivity.pipe(Object(l.a)(this._onDestroy)).subscribe(e=>{e&&e.is_agreed&&(this.disableNavigation=!1)},e=>{console.log(e)})}getEmployeeId(){this.subs.sink=this._edService.getEmployeeId().subscribe(e=>{console.log(e),this.associateId=e})}initModuleSelection(){this.featureModuleList.forEach(e=>{e.isVisible||(e.isVisible=a.where(this.applicationRoleAccessList,{object_id:e.objectId}).length>0)}),this.featureModuleList[0].name="About "+this.employeeName;let e=a.where(this.featureModuleList,{id:10});this.isFromMyProfile&&!this.checkTermsAndConditionAccess&&e[0].isVisible?(this.disableNavigation=!0,this.featureModuleList.forEach((e,t)=>{e.isSelected=10==e.id}),this._router.navigateByUrl("/main/employee-central/employeeCentralDetail/"+this.associateId+"/overview/edTermsAndCondition")):(this.featureModuleList[0].isSelected=!0,this.disableNavigation=!1,this._router.navigateByUrl("/main/employee-central/employeeCentralDetail/"+this.associateId+"/overview/profile"))}handleModuleSelection(e){if(this.featureModuleList.forEach((t,i)=>{t.isSelected=t.id==e.id}),e.id<=11)this._router.navigateByUrl("/main/employee-central/employeeCentralDetail/"+this.associateId+"/overview/"+e.path);else{let t=window.location.origin;window.location.replace(t+"/main/"+e.path)}}getEmployeeNameById(e){return new Promise((t,i)=>{this.subs.sink=this._edService.getEmployeeNameById(e).subscribe(e=>{!e.err&&e.data&&e.data.length>0?(this.employeeName=e.data[0].name,t(!0)):(this._toaster.showError("Something went wrong !","Kindly contact KEBS team to resolve",2e3),t(!1))},e=>{i(e)})})}CheckTermsAndConditionStatus(){let e={associate_id:parseInt(this.associateId)};return new Promise((t,i)=>{this.subs.sink=this._edService.checkTermsAndConditionStatus(e).subscribe(e=>{t(e.has_agreed)},e=>{this._toaster.showError("Error","Failed to retrieve terms and condition status !",2e3),console.log(e),i(e)})})}ngOnDestroy(){this.subs.unsubscribe()}}return e.\u0275fac=function(t){return new(t||e)(c["\u0275\u0275directiveInject"](o.g),c["\u0275\u0275directiveInject"](o.a),c["\u0275\u0275directiveInject"](h.a),c["\u0275\u0275directiveInject"](m.a),c["\u0275\u0275directiveInject"](p.a))},e.\u0275cmp=c["\u0275\u0275defineComponent"]({type:e,selectors:[["app-ed-overview-landing-page"]],decls:8,vars:1,consts:[[1,"container-fluid","pl-0","ed-overview-landing-page"],[1,"col-12","p-0"],[1,"row"],[1,"col-9","p-0"],[1,"col-3","p-0","pt-3"],[2,"width","100%","height","77vh","overflow-y","scroll"],["style","padding: 4px 0px !important;",3,"ngClass","ngStyle","click",4,"ngFor","ngForOf"],[2,"padding","4px 0px !important",3,"ngClass","ngStyle","click"],["class","d-flex flex-row py-2 px-4","style","width: 100%",4,"ngIf"],[1,"d-flex","flex-row","py-2","px-4",2,"width","100%"],[1,"pr-2"],[1,"over-flow-ctrl",3,"matTooltip"]],template:function(e,t){1&e&&(c["\u0275\u0275elementStart"](0,"div",0),c["\u0275\u0275elementStart"](1,"div",1),c["\u0275\u0275elementStart"](2,"div",2),c["\u0275\u0275elementStart"](3,"div",3),c["\u0275\u0275element"](4,"router-outlet"),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementStart"](5,"div",4),c["\u0275\u0275elementStart"](6,"mat-card",5),c["\u0275\u0275template"](7,I,2,6,"div",6),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"](),c["\u0275\u0275elementEnd"]()),2&e&&(c["\u0275\u0275advance"](7),c["\u0275\u0275property"]("ngForOf",t.featureModuleList))},directives:[o.l,u.a,n.NgForOf,n.NgClass,n.NgStyle,n.NgIf,g.a,v.a],styles:[".ed-overview-landing-page[_ngcontent-%COMP%]   .module-tile[_ngcontent-%COMP%]{color:#000;font-size:13px}.ed-overview-landing-page[_ngcontent-%COMP%]   .module-tile[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#7d838b;font-size:20px;margin-right:8px!important}.ed-overview-landing-page[_ngcontent-%COMP%]   .module-tile[_ngcontent-%COMP%]:hover{color:#111434;font-size:15px;font-weight:500;cursor:pointer;transition:.2s;background:#f6f6f7}.ed-overview-landing-page[_ngcontent-%COMP%]   .module-tile[_ngcontent-%COMP%]:hover   mat-icon[_ngcontent-%COMP%]{color:#111434;font-size:20px;margin-right:8px!important}.ed-overview-landing-page[_ngcontent-%COMP%]   .module-tile-selected[_ngcontent-%COMP%]{color:#111434;font-size:15px;font-weight:500;cursor:pointer;background:#f6f6f7;border-left:3px solid #111434}.ed-overview-landing-page[_ngcontent-%COMP%]   .module-tile-selected[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]{color:#111434;font-size:20px;margin-right:8px!important}.ed-overview-landing-page[_ngcontent-%COMP%]   .over-flow-ctrl[_ngcontent-%COMP%], .ed-overview-landing-page[_ngcontent-%COMP%]   .over-flow-ctrl-name[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;text-transform:capitalize;max-width:91%}"]}),e})(),C=(()=>{class e{constructor(e,t,i,n,o){this._auth=e,this._e360Service=t,this.router=i,this._toaster=n,this.activatedRoute=o,this.getEmployeeId()}getEmployeeId(){this._e360Service.getEmployeeId().subscribe(e=>{this.associateId=e})}canActivateChild(e,t){return Object(s.c)(this,void 0,void 0,(function*(){let e=t.url.match(/[0-9]+/);if(this.associateId=(null==e?void 0:e.length)>0?parseInt(e[0]):null,this.associateId)return this.isFromMyProfile()?(console.log("inner if"),yield this.validateTermsAndConditionsForUser()):(console.log("else"),(yield this.checkAdminDetailViewAccess())||(this._toaster.showWarning("Invalid Access","Kindly check the url to proceed !"),this.router.navigateByUrl("/main")),!0);this.router.navigateByUrl("/main/employee-central/")}))}validateTermsAndConditionsForUser(){return Object(s.c)(this,void 0,void 0,(function*(){if(yield this.checkTermsAndConditionStatus())return!0;this._toaster.showWarning("Policies and guildelines","Kindly agree policies and guidelines to proceed !"),this.router.navigateByUrl(`/main/employee-central/employeeCentralDetail/${this.associateId}/overview/edTermsAndCondition`)}))}isFromMyProfile(){return this._auth.getProfile().profile.aid==this.associateId}checkTermsAndConditionStatus(){let e={associate_id:parseInt(this.associateId)};return new Promise((t,i)=>{this._e360Service.checkTermsAndConditionStatus(e).subscribe(e=>{t(e.has_agreed)},e=>{this._toaster.showError("Error","Failed to retrieve terms and condition status !",2e3),console.log(e),i(e)})})}checkAdminDetailViewAccess(){return new Promise((e,t)=>{this._e360Service.validateAdminDetailViewAccess().subscribe(t=>{e(t.has_access)},e=>{this._toaster.showError("Error","Failed to validate associate URL !",2e3),console.log(e),t(e)})})}}return e.\u0275fac=function(t){return new(t||e)(c["\u0275\u0275inject"](p.a),c["\u0275\u0275inject"](m.a),c["\u0275\u0275inject"](o.g),c["\u0275\u0275inject"](h.a),c["\u0275\u0275inject"](o.a))},e.\u0275prov=c["\u0275\u0275defineInjectable"]({token:e,factory:e.\u0275fac,providedIn:"root"}),e})();const y=[{path:"",component:w,children:[{path:"",redirectTo:"profile",pathMatch:"full"},{path:"profile",loadChildren:()=>i.e(813).then(i.bind(null,"/LxJ")).then(e=>e.EdProfileModule),canActivateChild:[C]},{path:"organisation",loadChildren:()=>i.e(810).then(i.bind(null,"MqRg")).then(e=>e.EdOrganizationModule),canActivateChild:[C]},{path:"vaccinationDetails",loadChildren:()=>i.e(819).then(i.bind(null,"FKDU")).then(e=>e.EdVaccinationModule),canActivateChild:[C]},{path:"timeAndLeaves",loadChildren:()=>i.e(818).then(i.bind(null,"kWxL")).then(e=>e.EdTimeModule),canActivateChild:[C]},{path:"projectDetails",loadChildren:()=>i.e(814).then(i.bind(null,"QtIz")).then(e=>e.EdProjectModule),canActivateChild:[C]},{path:"benefits",loadChildren:()=>i.e(808).then(i.bind(null,"6cfA")).then(e=>e.EdBenefitsModule),canActivateChild:[C]},{path:"awards",loadChildren:()=>i.e(806).then(i.bind(null,"CaH6")).then(e=>e.EdAwardsModule),canActivateChild:[C]},{path:"backgroundVerification",loadChildren:()=>i.e(807).then(i.bind(null,"a1Ju")).then(e=>e.EdBackgroundVerificationModule),canActivateChild:[C]},{path:"hrDocuments",loadChildren:()=>i.e(809).then(i.bind(null,"WD+x")).then(e=>e.EdHrDocumentsModule),canActivateChild:[C]},{path:"edTermsAndCondition",loadChildren:()=>Promise.all([i.e(0),i.e(817)]).then(i.bind(null,"kLEv")).then(e=>e.EdTermsAndConditionsModule)},{path:"payroll",loadChildren:()=>i.e(812).then(i.bind(null,"JWtA")).then(e=>e.EdPayrollModule)}]}];let S=(()=>{class e{}return e.\u0275mod=c["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=c["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[o.k.forChild(y)],o.k]}),e})();var _=i("wZkO");let M=(()=>{class e{}return e.\u0275mod=c["\u0275\u0275defineNgModule"]({type:e}),e.\u0275inj=c["\u0275\u0275defineInjector"]({factory:function(t){return new(t||e)},imports:[[n.CommonModule,S,u.d,g.b,_.g,v.b]]}),e})()}}]);